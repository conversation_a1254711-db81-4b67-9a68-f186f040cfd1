{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { map } from './map';\nimport { from } from '../observable/from';\nexport function switchMap(project, resultSelector) {\n  if (typeof resultSelector === 'function') {\n    return source => source.pipe(switchMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n  return source => source.lift(new SwitchMapOperator(project));\n}\nclass SwitchMapOperator {\n  constructor(project) {\n    this.project = project;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new SwitchMapSubscriber(subscriber, this.project));\n  }\n}\nclass SwitchMapSubscriber extends OuterSubscriber {\n  constructor(destination, project) {\n    super(destination);\n    this.project = project;\n    this.index = 0;\n  }\n  _next(value) {\n    let result;\n    const index = this.index++;\n    try {\n      result = this.project(value, index);\n    } catch (error) {\n      this.destination.error(error);\n      return;\n    }\n    this._innerSub(result, value, index);\n  }\n  _innerSub(result, value, index) {\n    const innerSubscription = this.innerSubscription;\n    if (innerSubscription) {\n      innerSubscription.unsubscribe();\n    }\n    const innerSubscriber = new InnerSubscriber(this, value, index);\n    const destination = this.destination;\n    destination.add(innerSubscriber);\n    this.innerSubscription = subscribeToResult(this, result, undefined, undefined, innerSubscriber);\n    if (this.innerSubscription !== innerSubscriber) {\n      destination.add(this.innerSubscription);\n    }\n  }\n  _complete() {\n    const {\n      innerSubscription\n    } = this;\n    if (!innerSubscription || innerSubscription.closed) {\n      super._complete();\n    }\n    this.unsubscribe();\n  }\n  _unsubscribe() {\n    this.innerSubscription = null;\n  }\n  notifyComplete(innerSub) {\n    const destination = this.destination;\n    destination.remove(innerSub);\n    this.innerSubscription = null;\n    if (this.isStopped) {\n      super._complete();\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.destination.next(innerValue);\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "InnerSubscriber", "subscribeToResult", "map", "from", "switchMap", "project", "resultSelector", "source", "pipe", "a", "i", "b", "ii", "lift", "SwitchMapOperator", "constructor", "call", "subscriber", "subscribe", "SwitchMapSubscriber", "destination", "index", "_next", "value", "result", "error", "_innerSub", "innerSubscription", "unsubscribe", "innerSubscriber", "add", "undefined", "_complete", "closed", "_unsubscribe", "notifyComplete", "innerSub", "remove", "isStopped", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/switchMap.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { map } from './map';\nimport { from } from '../observable/from';\nexport function switchMap(project, resultSelector) {\n    if (typeof resultSelector === 'function') {\n        return (source) => source.pipe(switchMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n    }\n    return (source) => source.lift(new SwitchMapOperator(project));\n}\nclass SwitchMapOperator {\n    constructor(project) {\n        this.project = project;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SwitchMapSubscriber(subscriber, this.project));\n    }\n}\nclass SwitchMapSubscriber extends OuterSubscriber {\n    constructor(destination, project) {\n        super(destination);\n        this.project = project;\n        this.index = 0;\n    }\n    _next(value) {\n        let result;\n        const index = this.index++;\n        try {\n            result = this.project(value, index);\n        }\n        catch (error) {\n            this.destination.error(error);\n            return;\n        }\n        this._innerSub(result, value, index);\n    }\n    _innerSub(result, value, index) {\n        const innerSubscription = this.innerSubscription;\n        if (innerSubscription) {\n            innerSubscription.unsubscribe();\n        }\n        const innerSubscriber = new InnerSubscriber(this, value, index);\n        const destination = this.destination;\n        destination.add(innerSubscriber);\n        this.innerSubscription = subscribeToResult(this, result, undefined, undefined, innerSubscriber);\n        if (this.innerSubscription !== innerSubscriber) {\n            destination.add(this.innerSubscription);\n        }\n    }\n    _complete() {\n        const { innerSubscription } = this;\n        if (!innerSubscription || innerSubscription.closed) {\n            super._complete();\n        }\n        this.unsubscribe();\n    }\n    _unsubscribe() {\n        this.innerSubscription = null;\n    }\n    notifyComplete(innerSub) {\n        const destination = this.destination;\n        destination.remove(innerSub);\n        this.innerSubscription = null;\n        if (this.isStopped) {\n            super._complete();\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.destination.next(innerValue);\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,GAAG,QAAQ,OAAO;AAC3B,SAASC,IAAI,QAAQ,oBAAoB;AACzC,OAAO,SAASC,SAASA,CAACC,OAAO,EAAEC,cAAc,EAAE;EAC/C,IAAI,OAAOA,cAAc,KAAK,UAAU,EAAE;IACtC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,CAAC,EAAEC,CAAC,KAAKP,IAAI,CAACE,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACN,GAAG,CAAC,CAACS,CAAC,EAAEC,EAAE,KAAKN,cAAc,CAACG,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9H;EACA,OAAQL,MAAM,IAAKA,MAAM,CAACM,IAAI,CAAC,IAAIC,iBAAiB,CAACT,OAAO,CAAC,CAAC;AAClE;AACA,MAAMS,iBAAiB,CAAC;EACpBC,WAAWA,CAACV,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAW,IAAIA,CAACC,UAAU,EAAEV,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACW,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACZ,OAAO,CAAC,CAAC;EAC9E;AACJ;AACA,MAAMc,mBAAmB,SAASpB,eAAe,CAAC;EAC9CgB,WAAWA,CAACK,WAAW,EAAEf,OAAO,EAAE;IAC9B,KAAK,CAACe,WAAW,CAAC;IAClB,IAAI,CAACf,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgB,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIC,MAAM;IACV,MAAMH,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;IAC1B,IAAI;MACAG,MAAM,GAAG,IAAI,CAACnB,OAAO,CAACkB,KAAK,EAAEF,KAAK,CAAC;IACvC,CAAC,CACD,OAAOI,KAAK,EAAE;MACV,IAAI,CAACL,WAAW,CAACK,KAAK,CAACA,KAAK,CAAC;MAC7B;IACJ;IACA,IAAI,CAACC,SAAS,CAACF,MAAM,EAAED,KAAK,EAAEF,KAAK,CAAC;EACxC;EACAK,SAASA,CAACF,MAAM,EAAED,KAAK,EAAEF,KAAK,EAAE;IAC5B,MAAMM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAChD,IAAIA,iBAAiB,EAAE;MACnBA,iBAAiB,CAACC,WAAW,CAAC,CAAC;IACnC;IACA,MAAMC,eAAe,GAAG,IAAI7B,eAAe,CAAC,IAAI,EAAEuB,KAAK,EAAEF,KAAK,CAAC;IAC/D,MAAMD,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACU,GAAG,CAACD,eAAe,CAAC;IAChC,IAAI,CAACF,iBAAiB,GAAG1B,iBAAiB,CAAC,IAAI,EAAEuB,MAAM,EAAEO,SAAS,EAAEA,SAAS,EAAEF,eAAe,CAAC;IAC/F,IAAI,IAAI,CAACF,iBAAiB,KAAKE,eAAe,EAAE;MAC5CT,WAAW,CAACU,GAAG,CAAC,IAAI,CAACH,iBAAiB,CAAC;IAC3C;EACJ;EACAK,SAASA,CAAA,EAAG;IACR,MAAM;MAAEL;IAAkB,CAAC,GAAG,IAAI;IAClC,IAAI,CAACA,iBAAiB,IAAIA,iBAAiB,CAACM,MAAM,EAAE;MAChD,KAAK,CAACD,SAAS,CAAC,CAAC;IACrB;IACA,IAAI,CAACJ,WAAW,CAAC,CAAC;EACtB;EACAM,YAAYA,CAAA,EAAG;IACX,IAAI,CAACP,iBAAiB,GAAG,IAAI;EACjC;EACAQ,cAAcA,CAACC,QAAQ,EAAE;IACrB,MAAMhB,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACiB,MAAM,CAACD,QAAQ,CAAC;IAC5B,IAAI,CAACT,iBAAiB,GAAG,IAAI;IAC7B,IAAI,IAAI,CAACW,SAAS,EAAE;MAChB,KAAK,CAACN,SAAS,CAAC,CAAC;IACrB;EACJ;EACAO,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEP,QAAQ,EAAE;IACjE,IAAI,CAAChB,WAAW,CAACwB,IAAI,CAACH,UAAU,CAAC;EACrC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { rxSubscriber as rxSubscriberSymbol } from '../symbol/rxSubscriber';\nimport { empty as emptyObserver } from '../Observer';\nexport function toSubscriber(nextOrObserver, error, complete) {\n  if (nextOrObserver) {\n    if (nextOrObserver instanceof Subscriber) {\n      return nextOrObserver;\n    }\n    if (nextOrObserver[rxSubscriberSymbol]) {\n      return nextOrObserver[rxSubscriberSymbol]();\n    }\n  }\n  if (!nextOrObserver && !error && !complete) {\n    return new Subscriber(emptyObserver);\n  }\n  return new Subscriber(nextOrObserver, error, complete);\n}", "map": {"version": 3, "names": ["Subscriber", "rxSubscriber", "rxSubscriberSymbol", "empty", "emptyObserver", "toSubscriber", "nextOrObserver", "error", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/toSubscriber.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { rxSubscriber as rxSubscriberSymbol } from '../symbol/rxSubscriber';\nimport { empty as emptyObserver } from '../Observer';\nexport function toSubscriber(nextOrObserver, error, complete) {\n    if (nextOrObserver) {\n        if (nextOrObserver instanceof Subscriber) {\n            return nextOrObserver;\n        }\n        if (nextOrObserver[rxSubscriberSymbol]) {\n            return nextOrObserver[rxSubscriberSymbol]();\n        }\n    }\n    if (!nextOrObserver && !error && !complete) {\n        return new Subscriber(emptyObserver);\n    }\n    return new Subscriber(nextOrObserver, error, complete);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,IAAIC,kBAAkB,QAAQ,wBAAwB;AAC3E,SAASC,KAAK,IAAIC,aAAa,QAAQ,aAAa;AACpD,OAAO,SAASC,YAAYA,CAACC,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC1D,IAAIF,cAAc,EAAE;IAChB,IAAIA,cAAc,YAAYN,UAAU,EAAE;MACtC,OAAOM,cAAc;IACzB;IACA,IAAIA,cAAc,CAACJ,kBAAkB,CAAC,EAAE;MACpC,OAAOI,cAAc,CAACJ,kBAAkB,CAAC,CAAC,CAAC;IAC/C;EACJ;EACA,IAAI,CAACI,cAAc,IAAI,CAACC,KAAK,IAAI,CAACC,QAAQ,EAAE;IACxC,OAAO,IAAIR,UAAU,CAACI,aAAa,CAAC;EACxC;EACA,OAAO,IAAIJ,UAAU,CAACM,cAAc,EAAEC,KAAK,EAAEC,QAAQ,CAAC;AAC1D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
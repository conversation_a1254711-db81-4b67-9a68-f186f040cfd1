{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/team.service\";\nimport * as i3 from \"app/services/season.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/club.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"app/services/auth.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"app/layout/components/content-header/content-header.component\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"@core/pipes/filter.pipe\";\nfunction TeamSelectionComponent_ng_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 2, season_r4.name), \" \");\n  }\n}\nfunction TeamSelectionComponent_ng_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r5.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", club_r5.code, \" \");\n  }\n}\nfunction TeamSelectionComponent_ng_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r6.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r6.name, \" \");\n  }\n}\nfunction TeamSelectionComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"h4\", 19)(4, \"div\", 12)(5, \"div\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"h6\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"h6\", 23);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(14, \"hr\");\n    i0.ɵɵelementStart(15, \"p\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 24);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TeamSelectionComponent_div_26_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const team_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.openPlayerModal(team_r7));\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const team_r7 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(team_r7.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", team_r7.team_players_count ? team_r7.team_players_count : 0, \" \", i0.ɵɵpipeBind1(10, 10, \"player(s)\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", team_r7.team_coaches_count ? team_r7.team_coaches_count : 0, \" \", i0.ɵɵpipeBind1(13, 12, \"coach(es)\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(17, 14, \"Club\"), \": \", team_r7.club.name, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(20, 16, \"Group\"), \": \", team_r7.group.name, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 18, \"Select\"), \" \");\n  }\n}\nexport class TeamSelectionComponent {\n  constructor(_route, _router, teamService, seasonService, _translateService, _clubService, _loadingService, _authService, _trans) {\n    this._route = _route;\n    this._router = _router;\n    this.teamService = teamService;\n    this.seasonService = seasonService;\n    this._translateService = _translateService;\n    this._clubService = _clubService;\n    this._loadingService = _loadingService;\n    this._authService = _authService;\n    this._trans = _trans;\n    this.seasons = [];\n    this.clubs = [];\n    this.groups = [];\n    this.teams = [];\n    console.log('current User', this._authService.currentUserValue);\n  }\n  setContentHeader() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Team Assignment'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Team'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Team Assignment'),\n          isLink: false\n        }]\n      }\n    };\n  }\n  ngOnInit() {\n    this.setContentHeader();\n    this.getActiveSeasons();\n  }\n  getActiveSeasons() {\n    let status = 'active';\n    this.seasonService.getSeasons(status).subscribe(data => {\n      console.log(`getActiveSeasons`, data);\n      this.seasons = data;\n      if (!this.teamSelection?.seasonSelected) {\n        this.teamSelection = {\n          seasonSelected: this.seasons[0].id\n        };\n      }\n      // check if seasonSelected is not in seasons then set seasonSelected = seasons[0]\n      else if (!this.seasons.find(season => season.id === this.teamSelection?.seasonSelected)) {\n        this.teamSelection = {\n          seasonSelected: this.seasons[0].id\n        };\n      }\n      this.seasonId = this.teamSelection?.seasonSelected;\n      this.getTeam();\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  getTeam() {\n    this._loadingService.show();\n    this.teamService.getTeamInSeason2Manage(this.seasonId).toPromise().then(res => {\n      console.log('res', res);\n      this.teams = res.data;\n      // divide into groups by club\n      this.getClbsGroups(res.data);\n    });\n  }\n  onChangeSeason($event) {\n    console.log('onChangeSeason', $event);\n    this.teamSelection = {\n      seasonSelected: $event\n    };\n    // reset teams\n    this.teams = [];\n    // reset groups\n    this.groups = [];\n    // get teams\n    this.getTeam();\n  }\n  onSelectedClubChange(event) {\n    // find club in clubs by id\n    this.selectedClub = this.clubs.find(club => club.id === event);\n    this.teamSelection = {\n      clubSelected: this.selectedClub\n    };\n    if (event) {\n      this.groups = this.selectedClub.groups;\n      this.selectedGroup = this.groups[0].id;\n    } else {\n      this.clubFilter = null;\n      this.groups = [];\n      this.selectedGroup = null;\n    }\n  }\n  onSelectedGroupChange(event) {\n    this.teamSelection = {\n      groupSelected: event\n    };\n  }\n  getClbsGroups(teams) {\n    // reset clubs\n    this.clubs = [];\n    teams.forEach(team => {\n      // if team.club.id is not in clubs\n      if (!this.clubs.find(club => club.id === team.club.id)) {\n        this.clubs.push(team.club);\n        this.clubs[this.clubs.length - 1].groups = [];\n      }\n      // add group to club if not exist in club\n      let club = this.clubs.find(club => club.id === team.club.id);\n      if (!club.groups.find(group => group.id === team.group.id)) {\n        club.groups.push(team.group);\n        club.groups[club.groups.length - 1].teams = [];\n      }\n      // sort groups\n      club.groups.sort((a, b) => {\n        // get number from string\n        let aNumber;\n        if (parseInt(a.name.match(/\\d+/))) {\n          aNumber = parseInt(a.name.match(/\\d+/)[0]);\n        }\n        let bNumber;\n        if (parseInt(b.name.match(/\\d+/))) {\n          bNumber = parseInt(b.name.match(/\\d+/)[0]);\n        }\n        if (aNumber < bNumber) {\n          return -1;\n        }\n        if (aNumber > bNumber) {\n          return 1;\n        }\n        // if aNumber == bNumber then sort by name\n        if (a.name < b.name) {\n          return -1;\n        }\n        if (a.name > b.name) {\n          return 1;\n        }\n        return 0;\n      });\n    });\n    // sort clubs\n    this.clubs.sort((a, b) => {\n      if (a.name < b.name) {\n        return -1;\n      }\n      if (a.name > b.name) {\n        return 1;\n      }\n      return 0;\n    });\n    // set selected club\n    if (!this.teamSelection?.clubSelected) {\n      this.teamSelection = {\n        clubSelected: this.clubs[0]\n      };\n    }\n    if (this.teamSelection.clubSelected) {\n      if (this.teamSelection.clubSelected.groups[0] && this.teamSelection.clubSelected.groups[0].season_id != this.seasonId) {\n        this.teamSelection = {\n          clubSelected: this.clubs[0]\n        };\n      }\n      // check if clubSelected is not in clubs then set clubSelected = clubs[0]\n      else if (!this.clubs.find(club => club.id === this.teamSelection?.clubSelected)) {\n        this.teamSelection = {\n          clubSelected: this.clubs[0]\n        };\n      }\n    }\n    this.selectedClub = this.teamSelection?.clubSelected;\n    this.clubFilter = this.selectedClub?.id;\n    this.groups = this.selectedClub?.groups ? this.selectedClub?.groups : [];\n    // set selected group\n    // check if groupSelected is not in groups then set groupSelected = groups[0]\n    let checkGroup = this.groups.find(group => group.id === this.teamSelection?.groupSelected);\n    if (!checkGroup && this.groups.length > 0) {\n      if (!this.teamSelection?.groupSelected || !checkGroup) {\n        this.teamSelection = {\n          groupSelected: this.groups[0].id\n        };\n      }\n    } else if (this.groups.length == 0) {\n      this.teamSelection = {\n        groupSelected: null\n      };\n    }\n    this.selectedGroup = this.teamSelection?.groupSelected;\n  }\n  openPlayerModal(team) {\n    this._router.navigate(['teams/team-assignment', team.id]);\n  }\n  set teamSelection(data) {\n    let str_data = JSON.stringify(data);\n    // merge data if exist\n    if (localStorage.getItem('teamSelection')) {\n      let old_data = JSON.parse(localStorage.getItem('teamSelection'));\n      str_data = JSON.stringify({\n        ...old_data,\n        ...data\n      });\n    }\n    localStorage.setItem('teamSelection', str_data);\n  }\n  get teamSelection() {\n    let data = JSON.parse(localStorage.getItem('teamSelection'));\n    if (data) {\n      return data;\n    }\n    return null;\n  }\n  static #_ = this.ɵfac = function TeamSelectionComponent_Factory(t) {\n    return new (t || TeamSelectionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TeamService), i0.ɵɵdirectiveInject(i3.SeasonService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.ClubService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i4.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamSelectionComponent,\n    selectors: [[\"app-team-selection\"]],\n    decls: 29,\n    vars: 42,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\", \"mb-1\"], [1, \"col-12\", \"col-md-4\"], [\"for\", \"season\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-6\", \"col-md-4\"], [\"for\", \"club\"], [\"for\", \"group\"], [\"name\", \"group.id\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [\"class\", \"col-lg-4 col-md-6 col-sm-12 col-12\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"name\", \"group.id\", 3, \"value\"], [1, \"col-lg-4\", \"col-md-6\", \"col-sm-12\", \"col-12\"], [1, \"card\"], [1, \"card-body\"], [1, \"card-title\"], [1, \"col-6\"], [1, \"col-6\", \"text-sm\"], [1, \"pl-1\", \"fw-normal\", \"text-secondary\", \"float-right\"], [1, \"fw-normal\", \"text-secondary\", \"float-right\"], [1, \"card-text\"], [1, \"btn\", \"btn-block\", \"btn-primary\", 3, \"click\"]],\n    template: function TeamSelectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"ng-select\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function TeamSelectionComponent_Template_ng_select_ngModelChange_8_listener($event) {\n          return ctx.seasonId = $event;\n        })(\"change\", function TeamSelectionComponent_Template_ng_select_change_8_listener($event) {\n          return ctx.onChangeSeason($event);\n        });\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵtemplate(10, TeamSelectionComponent_ng_option_10_Template, 3, 4, \"ng-option\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"label\", 9);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"ng-select\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function TeamSelectionComponent_Template_ng_select_ngModelChange_15_listener($event) {\n          return ctx.clubFilter = $event;\n        })(\"change\", function TeamSelectionComponent_Template_ng_select_change_15_listener($event) {\n          return ctx.onSelectedClubChange($event);\n        });\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵtemplate(17, TeamSelectionComponent_ng_option_17_Template, 2, 2, \"ng-option\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 8)(19, \"label\", 10);\n        i0.ɵɵtext(20);\n        i0.ɵɵpipe(21, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"ng-select\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function TeamSelectionComponent_Template_ng_select_ngModelChange_22_listener($event) {\n          return ctx.selectedGroup = $event;\n        })(\"change\", function TeamSelectionComponent_Template_ng_select_change_22_listener($event) {\n          return ctx.onSelectedGroupChange($event);\n        });\n        i0.ɵɵpipe(23, \"translate\");\n        i0.ɵɵtemplate(24, TeamSelectionComponent_ng_option_24_Template, 2, 2, \"ng-option\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(25, \"div\", 12);\n        i0.ɵɵtemplate(26, TeamSelectionComponent_div_26_Template, 24, 20, \"div\", 13);\n        i0.ɵɵpipe(27, \"filter\");\n        i0.ɵɵpipe(28, \"filter\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 20, \"Season\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(9, 22, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.seasonId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 24, \"Club\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(16, 26, \"Select Club\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.clubFilter);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clubs);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(21, 28, \"Group\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(23, 30, \"Select Group\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.selectedGroup);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind4(27, 32, i0.ɵɵpipeBind4(28, 37, ctx.teams, ctx.clubFilter, \"club_id\", true), ctx.selectedGroup, \"group_id\", true));\n      }\n    },\n    dependencies: [i8.NgForOf, i9.ContentHeaderComponent, i10.NgControlStatus, i10.NgModel, i11.NgSelectComponent, i11.ɵr, i4.TranslatePipe, i12.FilterPipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AASA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;ICCpBC,EAAA,CAAAC,cAAA,oBAA8D;IAAAD,EAAA,CAAAE,MAAA,GAC9D;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAD8BH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAmB;IAACN,EAAA,CAAAO,SAAA,GAC9D;IAD8DP,EAAA,CAAAQ,kBAAA,KAAAR,EAAA,CAAAS,WAAA,OAAAJ,SAAA,CAAAK,IAAA,OAC9D;;;;;IAQAV,EAAA,CAAAC,cAAA,oBAAwD;IAAAD,EAAA,CAAAE,MAAA,GACxD;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAD0BH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAAL,EAAA,CAAiB;IAACN,EAAA,CAAAO,SAAA,GACxD;IADwDP,EAAA,CAAAQ,kBAAA,KAAAG,OAAA,CAAAC,IAAA,MACxD;;;;;IAQAZ,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4BH,EAAA,CAAAI,UAAA,UAAAS,QAAA,CAAAP,EAAA,CAAkB;IACxDN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAK,QAAA,CAAAH,IAAA,MACF;;;;;;IAOJV,EAAA,CAAAC,cAAA,cAI+C;IAKlBD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxCH,EAAA,CAAAC,cAAA,cAA2B;IAEvBD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIXH,EAAA,CAAAc,SAAA,UAAM;IACNd,EAAA,CAAAC,cAAA,aAAqB;IACnBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAqB;IACnBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,kBAA0E;IAAlED,EAAA,CAAAe,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,MAAA,CAAAG,eAAA,CAAAL,OAAA,CAAqB;IAAA,EAAC;IACrCpB,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IApBcH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA0B,iBAAA,CAAAN,OAAA,CAAAV,IAAA,CAAe;IAG9BV,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2B,kBAAA,MAAAP,OAAA,CAAAQ,kBAAA,GAAAR,OAAA,CAAAQ,kBAAA,WAAA5B,EAAA,CAAAS,WAAA,2BACF;IAEET,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2B,kBAAA,MAAAP,OAAA,CAAAS,kBAAA,GAAAT,OAAA,CAAAS,kBAAA,WAAA7B,EAAA,CAAAS,WAAA,2BACF;IAMJT,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAAS,WAAA,wBAAAW,OAAA,CAAAU,IAAA,CAAApB,IAAA,MACF;IAEEV,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAAS,WAAA,yBAAAW,OAAA,CAAAW,KAAA,CAAArB,IAAA,MACF;IAEEV,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,wBACF;;;ADlDZ,OAAM,MAAOuB,sBAAsB;EAqBjCC,YACSC,MAAsB,EACtBC,OAAe,EACfC,WAAwB,EACxBC,aAA4B,EAC3BC,iBAAmC,EACpCC,YAAyB,EACzBC,eAA+B,EAC/BC,YAAyB,EACzBC,MAAwB;IARxB,KAAAR,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IA5Bf,KAAAC,OAAO,GAAa,EAAE;IAMtB,KAAAC,KAAK,GASK,EAAE;IACZ,KAAAC,MAAM,GAAG,EAAE;IACX,KAAAC,KAAK,GAAG,EAAE;IAaRC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACP,YAAY,CAACQ,gBAAgB,CAAC;EACjE;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACV,MAAM,CAACW,OAAO,CAAC,iBAAiB,CAAC;MACnDC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACE/C,IAAI,EAAE,IAAI,CAACgC,MAAM,CAACW,OAAO,CAAC,MAAM,CAAC;UACjCK,MAAM,EAAE;SACT,EACD;UACEhD,IAAI,EAAE,IAAI,CAACgC,MAAM,CAACW,OAAO,CAAC,iBAAiB,CAAC;UAC5CK,MAAM,EAAE;SACT;;KAGN;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACT,gBAAgB,EAAE;IACvB,IAAI,CAACU,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAIC,MAAM,GAAG,QAAQ;IACrB,IAAI,CAACxB,aAAa,CAACyB,UAAU,CAACD,MAAM,CAAC,CAACE,SAAS,CAC5CC,IAAI,IAAI;MACPjB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgB,IAAI,CAAC;MACrC,IAAI,CAACrB,OAAO,GAAGqB,IAAI;MAEnB,IAAI,CAAC,IAAI,CAACC,aAAa,EAAEC,cAAc,EAAE;QACvC,IAAI,CAACD,aAAa,GAAG;UACnBC,cAAc,EAAE,IAAI,CAACvB,OAAO,CAAC,CAAC,CAAC,CAACrC;SACjC;;MAEH;MAAA,KACK,IACH,CAAC,IAAI,CAACqC,OAAO,CAACwB,IAAI,CACfC,MAAM,IAAKA,MAAM,CAAC9D,EAAE,KAAK,IAAI,CAAC2D,aAAa,EAAEC,cAAc,CAC7D,EACD;QACA,IAAI,CAACD,aAAa,GAAG;UACnBC,cAAc,EAAE,IAAI,CAACvB,OAAO,CAAC,CAAC,CAAC,CAACrC;SACjC;;MAGH,IAAI,CAAC+D,QAAQ,GAAG,IAAI,CAACJ,aAAa,EAAEC,cAAc;MAClD,IAAI,CAACI,OAAO,EAAE;IAChB,CAAC,EACAC,KAAK,IAAI;MACRxE,IAAI,CAACyE,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACvC,iBAAiB,CAACe,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAiB,OAAOA,CAAA;IACL,IAAI,CAAC9B,eAAe,CAACsC,IAAI,EAAE;IAC3B,IAAI,CAAC1C,WAAW,CACb2C,sBAAsB,CAAC,IAAI,CAACV,QAAQ,CAAC,CACrCW,SAAS,EAAE,CACXC,IAAI,CAAEC,GAAQ,IAAI;MACjBnC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEkC,GAAG,CAAC;MACvB,IAAI,CAACpC,KAAK,GAAGoC,GAAG,CAAClB,IAAI;MACrB;MACA,IAAI,CAACmB,aAAa,CAACD,GAAG,CAAClB,IAAI,CAAC;IAC9B,CAAC,CAAC;EACN;EAEAoB,cAAcA,CAACC,MAAM;IACnBtC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqC,MAAM,CAAC;IACrC,IAAI,CAACpB,aAAa,GAAG;MACnBC,cAAc,EAAEmB;KACjB;IACD;IACA,IAAI,CAACvC,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACD,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACyB,OAAO,EAAE;EAChB;EAEAgB,oBAAoBA,CAACC,KAAK;IACxB;IACA,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC5C,KAAK,CAACuB,IAAI,CAAErC,IAAI,IAAKA,IAAI,CAACxB,EAAE,KAAKiF,KAAK,CAAC;IAChE,IAAI,CAACtB,aAAa,GAAG;MACnBwB,YAAY,EAAE,IAAI,CAACD;KACpB;IACD,IAAID,KAAK,EAAE;MACT,IAAI,CAAC1C,MAAM,GAAG,IAAI,CAAC2C,YAAY,CAAC3C,MAAM;MACtC,IAAI,CAAC6C,aAAa,GAAG,IAAI,CAAC7C,MAAM,CAAC,CAAC,CAAC,CAACvC,EAAE;KACvC,MAAM;MACL,IAAI,CAACqF,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC9C,MAAM,GAAG,EAAE;MAChB,IAAI,CAAC6C,aAAa,GAAG,IAAI;;EAE7B;EAEAE,qBAAqBA,CAACL,KAAK;IACzB,IAAI,CAACtB,aAAa,GAAG;MACnB4B,aAAa,EAAEN;KAChB;EACH;EAEAJ,aAAaA,CAACrC,KAAK;IACjB;IACA,IAAI,CAACF,KAAK,GAAG,EAAE;IAEfE,KAAK,CAACgD,OAAO,CAAEC,IAAI,IAAI;MACrB;MACA,IAAI,CAAC,IAAI,CAACnD,KAAK,CAACuB,IAAI,CAAErC,IAAI,IAAKA,IAAI,CAACxB,EAAE,KAAKyF,IAAI,CAACjE,IAAI,CAACxB,EAAE,CAAC,EAAE;QACxD,IAAI,CAACsC,KAAK,CAACoD,IAAI,CAACD,IAAI,CAACjE,IAAI,CAAC;QAC1B,IAAI,CAACc,KAAK,CAAC,IAAI,CAACA,KAAK,CAACqD,MAAM,GAAG,CAAC,CAAC,CAACpD,MAAM,GAAG,EAAE;;MAE/C;MACA,IAAIf,IAAI,GAAG,IAAI,CAACc,KAAK,CAACuB,IAAI,CAAErC,IAAI,IAAKA,IAAI,CAACxB,EAAE,KAAKyF,IAAI,CAACjE,IAAI,CAACxB,EAAE,CAAC;MAC9D,IAAI,CAACwB,IAAI,CAACe,MAAM,CAACsB,IAAI,CAAEpC,KAAK,IAAKA,KAAK,CAACzB,EAAE,KAAKyF,IAAI,CAAChE,KAAK,CAACzB,EAAE,CAAC,EAAE;QAC5DwB,IAAI,CAACe,MAAM,CAACmD,IAAI,CAACD,IAAI,CAAChE,KAAK,CAAC;QAC5BD,IAAI,CAACe,MAAM,CAACf,IAAI,CAACe,MAAM,CAACoD,MAAM,GAAG,CAAC,CAAC,CAACnD,KAAK,GAAG,EAAE;;MAEhD;MACAhB,IAAI,CAACe,MAAM,CAACqD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACxB;QACA,IAAIC,OAAO;QACX,IAAIC,QAAQ,CAACH,CAAC,CAACzF,IAAI,CAAC6F,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;UACjCF,OAAO,GAAGC,QAAQ,CAACH,CAAC,CAACzF,IAAI,CAAC6F,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5C,IAAIC,OAAO;QACX,IAAIF,QAAQ,CAACF,CAAC,CAAC1F,IAAI,CAAC6F,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;UACjCC,OAAO,GAAGF,QAAQ,CAACF,CAAC,CAAC1F,IAAI,CAAC6F,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5C,IAAIF,OAAO,GAAGG,OAAO,EAAE;UACrB,OAAO,CAAC,CAAC;;QAEX,IAAIH,OAAO,GAAGG,OAAO,EAAE;UACrB,OAAO,CAAC;;QAEV;QACA,IAAIL,CAAC,CAACzF,IAAI,GAAG0F,CAAC,CAAC1F,IAAI,EAAE;UACnB,OAAO,CAAC,CAAC;;QAEX,IAAIyF,CAAC,CAACzF,IAAI,GAAG0F,CAAC,CAAC1F,IAAI,EAAE;UACnB,OAAO,CAAC;;QAEV,OAAO,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACkC,KAAK,CAACsD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACvB,IAAID,CAAC,CAACzF,IAAI,GAAG0F,CAAC,CAAC1F,IAAI,EAAE;QACnB,OAAO,CAAC,CAAC;;MAEX,IAAIyF,CAAC,CAACzF,IAAI,GAAG0F,CAAC,CAAC1F,IAAI,EAAE;QACnB,OAAO,CAAC;;MAEV,OAAO,CAAC;IACV,CAAC,CAAC;IACF;IACA,IAAI,CAAC,IAAI,CAACuD,aAAa,EAAEwB,YAAY,EAAE;MACrC,IAAI,CAACxB,aAAa,GAAG;QACnBwB,YAAY,EAAE,IAAI,CAAC7C,KAAK,CAAC,CAAC;OAC3B;;IAGH,IAAI,IAAI,CAACqB,aAAa,CAACwB,YAAY,EAAE;MACnC,IACE,IAAI,CAACxB,aAAa,CAACwB,YAAY,CAAC5C,MAAM,CAAC,CAAC,CAAC,IACzC,IAAI,CAACoB,aAAa,CAACwB,YAAY,CAAC5C,MAAM,CAAC,CAAC,CAAC,CAAC4D,SAAS,IAAI,IAAI,CAACpC,QAAQ,EACpE;QACA,IAAI,CAACJ,aAAa,GAAG;UACnBwB,YAAY,EAAE,IAAI,CAAC7C,KAAK,CAAC,CAAC;SAC3B;;MAGH;MAAA,KACK,IACH,CAAC,IAAI,CAACA,KAAK,CAACuB,IAAI,CACbrC,IAAI,IAAKA,IAAI,CAACxB,EAAE,KAAK,IAAI,CAAC2D,aAAa,EAAEwB,YAAY,CACvD,EACD;QACA,IAAI,CAACxB,aAAa,GAAG;UACnBwB,YAAY,EAAE,IAAI,CAAC7C,KAAK,CAAC,CAAC;SAC3B;;;IAIL,IAAI,CAAC4C,YAAY,GAAG,IAAI,CAACvB,aAAa,EAAEwB,YAAY;IACpD,IAAI,CAACE,UAAU,GAAG,IAAI,CAACH,YAAY,EAAElF,EAAE;IACvC,IAAI,CAACuC,MAAM,GAAG,IAAI,CAAC2C,YAAY,EAAE3C,MAAM,GAAG,IAAI,CAAC2C,YAAY,EAAE3C,MAAM,GAAG,EAAE;IACxE;IACA;IACA,IAAI6D,UAAU,GAAG,IAAI,CAAC7D,MAAM,CAACsB,IAAI,CAC9BpC,KAAK,IAAKA,KAAK,CAACzB,EAAE,KAAK,IAAI,CAAC2D,aAAa,EAAE4B,aAAa,CAC1D;IACD,IAAI,CAACa,UAAU,IAAI,IAAI,CAAC7D,MAAM,CAACoD,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC,IAAI,CAAChC,aAAa,EAAE4B,aAAa,IAAI,CAACa,UAAU,EAAE;QACrD,IAAI,CAACzC,aAAa,GAAG;UACnB4B,aAAa,EAAE,IAAI,CAAChD,MAAM,CAAC,CAAC,CAAC,CAACvC;SAC/B;;KAEJ,MAAM,IAAI,IAAI,CAACuC,MAAM,CAACoD,MAAM,IAAI,CAAC,EAAE;MAClC,IAAI,CAAChC,aAAa,GAAG;QACnB4B,aAAa,EAAE;OAChB;;IAEH,IAAI,CAACH,aAAa,GAAG,IAAI,CAACzB,aAAa,EAAE4B,aAAa;EACxD;EAEApE,eAAeA,CAACsE,IAAI;IAClB,IAAI,CAAC5D,OAAO,CAACwE,QAAQ,CAAC,CAAC,uBAAuB,EAAEZ,IAAI,CAACzF,EAAE,CAAC,CAAC;EAC3D;EAEA,IAAI2D,aAAaA,CAACD,IAAI;IACpB,IAAI4C,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAAC9C,IAAI,CAAC;IACnC;IACA,IAAI+C,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,EAAE;MACzC,IAAIC,QAAQ,GAAGJ,IAAI,CAACK,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;MAChEJ,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAAC;QAAE,GAAGG,QAAQ;QAAE,GAAGjD;MAAI,CAAE,CAAC;;IAErD+C,YAAY,CAACI,OAAO,CAAC,eAAe,EAAEP,QAAQ,CAAC;EACjD;EACA,IAAI3C,aAAaA,CAAA;IACf,IAAID,IAAI,GAAG6C,IAAI,CAACK,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC5D,IAAIhD,IAAI,EAAE;MACR,OAAOA,IAAI;;IAEb,OAAO,IAAI;EACb;EAAC,QAAAoD,CAAA;qBA5QUpF,sBAAsB,EAAAhC,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvH,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAxH,EAAA,CAAAqH,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA1H,EAAA,CAAAqH,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA5H,EAAA,CAAAqH,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAA9H,EAAA,CAAAqH,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAAqH,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAlI,EAAA,CAAAqH,iBAAA,CAAAc,EAAA,CAAAC,WAAA,GAAApI,EAAA,CAAAqH,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAO,EAAA;UAAtBrG,sBAAsB;IAAAsG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBnC5I,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAc,SAAA,4BAAyE;QAEzEd,EAAA,CAAAC,cAAA,aAAsB;QAEED,EAAA,CAAAE,MAAA,GAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtDH,EAAA,CAAAC,cAAA,mBACoC;QAD2DD,EAAA,CAAAe,UAAA,2BAAA+H,mEAAAzD,MAAA;UAAA,OAAAwD,GAAA,CAAAxE,QAAA,GAAAgB,MAAA;QAAA,EAAsB,oBAAA0D,4DAAA1D,MAAA;UAAA,OACzGwD,GAAA,CAAAzD,cAAA,CAAAC,MAAA,CAAsB;QAAA,EADmF;;QAEnHrF,EAAA,CAAAgJ,UAAA,KAAAC,4CAAA,uBACY;QACdjJ,EAAA,CAAAG,YAAA,EAAY;QAGdH,EAAA,CAAAC,cAAA,cAA4B;QACRD,EAAA,CAAAE,MAAA,IAAwB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAClDH,EAAA,CAAAC,cAAA,oBAC0C;QADkDD,EAAA,CAAAe,UAAA,2BAAAmI,oEAAA7D,MAAA;UAAA,OAAAwD,GAAA,CAAAlD,UAAA,GAAAN,MAAA;QAAA,EAAwB,oBAAA8D,6DAAA9D,MAAA;UAAA,OACxGwD,GAAA,CAAAvD,oBAAA,CAAAD,MAAA,CAA4B;QAAA,EAD4E;;QAElHrF,EAAA,CAAAgJ,UAAA,KAAAI,4CAAA,uBACY;QACdpJ,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,cAA4B;QACPD,EAAA,CAAAE,MAAA,IAAyB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpDH,EAAA,CAAAC,cAAA,oBAEC;QAF4FD,EAAA,CAAAe,UAAA,2BAAAsI,oEAAAhE,MAAA;UAAA,OAAAwD,GAAA,CAAAnD,aAAA,GAAAL,MAAA;QAAA,EAA2B,oBAAAiE,6DAAAjE,MAAA;UAAA,OAC9GwD,GAAA,CAAAjD,qBAAA,CAAAP,MAAA,CAA6B;QAAA,EADiF;;QAGtHrF,EAAA,CAAAgJ,UAAA,KAAAO,4CAAA,wBAEY;QACdvJ,EAAA,CAAAG,YAAA,EAAY;QAKhBH,EAAA,CAAAC,cAAA,eAAiB;QACfD,EAAA,CAAAgJ,UAAA,KAAAQ,sCAAA,oBAgCM;;;QACRxJ,EAAA,CAAAG,YAAA,EAAM;;;QAnEcH,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAI,UAAA,kBAAAyI,GAAA,CAAA1F,aAAA,CAA+B;QAI3BnD,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAS,WAAA,kBAA0B;QACKT,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAAyJ,qBAAA,gBAAAzJ,EAAA,CAAAS,WAAA,yBAA2C;QAAnFT,EAAA,CAAAI,UAAA,oBAAmB,gCAAAyI,GAAA,CAAAxE,QAAA;QAEErE,EAAA,CAAAO,SAAA,GAAU;QAAVP,EAAA,CAAAI,UAAA,YAAAyI,GAAA,CAAAlG,OAAA,CAAU;QAMxB3C,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAS,WAAA,iBAAwB;QACQT,EAAA,CAAAO,SAAA,GAAyC;QAAzCP,EAAA,CAAAyJ,qBAAA,gBAAAzJ,EAAA,CAAAS,WAAA,wBAAyC;QAAhFT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAyI,GAAA,CAAAlD,UAAA;QAEA3F,EAAA,CAAAO,SAAA,GAAQ;QAARP,EAAA,CAAAI,UAAA,YAAAyI,GAAA,CAAAjG,KAAA,CAAQ;QAKnB5C,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAS,WAAA,kBAAyB;QACMT,EAAA,CAAAO,SAAA,GAA0C;QAA1CP,EAAA,CAAAyJ,qBAAA,gBAAAzJ,EAAA,CAAAS,WAAA,yBAA0C;QAAjFT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAyI,GAAA,CAAAnD,aAAA;QAGC1F,EAAA,CAAAO,SAAA,GAAS;QAATP,EAAA,CAAAI,UAAA,YAAAyI,GAAA,CAAAhG,MAAA,CAAS;QAW/C7C,EAAA,CAAAO,SAAA,GAEM;QAFNP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA0J,WAAA,SAAA1J,EAAA,CAAA0J,WAAA,SAAAb,GAAA,CAAA/F,KAAA,EAAA+F,GAAA,CAAAlD,UAAA,oBAAAkD,GAAA,CAAAnD,aAAA,oBAEM", "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r4", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "club_r5", "code", "group_r6", "ɵɵelement", "ɵɵlistener", "TeamSelectionComponent_div_26_Template_button_click_21_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "team_r7", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "openPlayerModal", "ɵɵtextInterpolate", "ɵɵtextInterpolate2", "team_players_count", "team_coaches_count", "club", "group", "TeamSelectionComponent", "constructor", "_route", "_router", "teamService", "seasonService", "_translateService", "_clubService", "_loadingService", "_authService", "_trans", "seasons", "clubs", "groups", "teams", "console", "log", "currentUserValue", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contentHeader", "headerTitle", "instant", "actionButton", "breadcrumb", "type", "links", "isLink", "ngOnInit", "getActiveSeasons", "status", "getSeasons", "subscribe", "data", "teamSelection", "seasonSelected", "find", "season", "seasonId", "getTeam", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "show", "getTeamInSeason2Manage", "to<PERSON>romise", "then", "res", "getClbsGroups", "onChangeSeason", "$event", "onSelectedClubChange", "event", "selectedClub", "clubSelected", "selectedGroup", "clubFilter", "onSelectedGroupChange", "groupSelected", "for<PERSON>ach", "team", "push", "length", "sort", "a", "b", "aNumber", "parseInt", "match", "bNumber", "season_id", "checkGroup", "navigate", "str_data", "JSON", "stringify", "localStorage", "getItem", "old_data", "parse", "setItem", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "TeamService", "i3", "SeasonService", "i4", "TranslateService", "i5", "ClubService", "i6", "LoadingService", "i7", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "TeamSelectionComponent_Template", "rf", "ctx", "TeamSelectionComponent_Template_ng_select_ngModelChange_8_listener", "TeamSelectionComponent_Template_ng_select_change_8_listener", "ɵɵtemplate", "TeamSelectionComponent_ng_option_10_Template", "TeamSelectionComponent_Template_ng_select_ngModelChange_15_listener", "TeamSelectionComponent_Template_ng_select_change_15_listener", "TeamSelectionComponent_ng_option_17_Template", "TeamSelectionComponent_Template_ng_select_ngModelChange_22_listener", "TeamSelectionComponent_Template_ng_select_change_22_listener", "TeamSelectionComponent_ng_option_24_Template", "TeamSelectionComponent_div_26_Template", "ɵɵpropertyInterpolate", "ɵɵpipeBind4"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-selection\\team-selection.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-selection\\team-selection.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Route, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Season } from 'app/interfaces/season';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-team-selection',\r\n  templateUrl: './team-selection.component.html',\r\n  styleUrls: ['./team-selection.component.scss'],\r\n})\r\nexport class TeamSelectionComponent implements OnInit {\r\n  public contentHeader: object;\r\n  seasons: Season[] = [];\r\n  season: Season;\r\n  selectedClub: any;\r\n  selectedGroup: any;\r\n  clubFilter: any;\r\n  seasonId: any;\r\n  clubs:\r\n    | [\r\n        {\r\n          id: number;\r\n          name: string;\r\n          code: string;\r\n          groups: [];\r\n        }\r\n      ]\r\n    | any = [];\r\n  groups = [];\r\n  teams = [];\r\n\r\n  constructor(\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public teamService: TeamService,\r\n    public seasonService: SeasonService,\r\n    private _translateService: TranslateService,\r\n    public _clubService: ClubService,\r\n    public _loadingService: LoadingService,\r\n    public _authService: AuthService,\r\n    public _trans: TranslateService\r\n  ) {\r\n    console.log('current User', this._authService.currentUserValue);\r\n  }\r\n\r\n  setContentHeader() {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Team Assignment'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Team'),\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: this._trans.instant('Team Assignment'),\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.setContentHeader();\r\n    this.getActiveSeasons();\r\n  }\r\n\r\n  getActiveSeasons() {\r\n    let status = 'active';\r\n    this.seasonService.getSeasons(status).subscribe(\r\n      (data) => {\r\n        console.log(`getActiveSeasons`, data);\r\n        this.seasons = data;\r\n\r\n        if (!this.teamSelection?.seasonSelected) {\r\n          this.teamSelection = {\r\n            seasonSelected: this.seasons[0].id,\r\n          };\r\n        }\r\n        // check if seasonSelected is not in seasons then set seasonSelected = seasons[0]\r\n        else if (\r\n          !this.seasons.find(\r\n            (season) => season.id === this.teamSelection?.seasonSelected\r\n          )\r\n        ) {\r\n          this.teamSelection = {\r\n            seasonSelected: this.seasons[0].id,\r\n          };\r\n        }\r\n\r\n        this.seasonId = this.teamSelection?.seasonSelected;\r\n        this.getTeam();\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  getTeam() {\r\n    this._loadingService.show();\r\n    this.teamService\r\n      .getTeamInSeason2Manage(this.seasonId)\r\n      .toPromise()\r\n      .then((res: any) => {\r\n        console.log('res', res);\r\n        this.teams = res.data;\r\n        // divide into groups by club\r\n        this.getClbsGroups(res.data);\r\n      });\r\n  }\r\n\r\n  onChangeSeason($event) {\r\n    console.log('onChangeSeason', $event);\r\n    this.teamSelection = {\r\n      seasonSelected: $event,\r\n    };\r\n    // reset teams\r\n    this.teams = [];\r\n    // reset groups\r\n    this.groups = [];\r\n    // get teams\r\n    this.getTeam();\r\n  }\r\n\r\n  onSelectedClubChange(event) {\r\n    // find club in clubs by id\r\n    this.selectedClub = this.clubs.find((club) => club.id === event);\r\n    this.teamSelection = {\r\n      clubSelected: this.selectedClub,\r\n    };\r\n    if (event) {\r\n      this.groups = this.selectedClub.groups;\r\n      this.selectedGroup = this.groups[0].id;\r\n    } else {\r\n      this.clubFilter = null;\r\n      this.groups = [];\r\n      this.selectedGroup = null;\r\n    }\r\n  }\r\n\r\n  onSelectedGroupChange(event) {\r\n    this.teamSelection = {\r\n      groupSelected: event,\r\n    };\r\n  }\r\n\r\n  getClbsGroups(teams) {\r\n    // reset clubs\r\n    this.clubs = [];\r\n\r\n    teams.forEach((team) => {\r\n      // if team.club.id is not in clubs\r\n      if (!this.clubs.find((club) => club.id === team.club.id)) {\r\n        this.clubs.push(team.club);\r\n        this.clubs[this.clubs.length - 1].groups = [];\r\n      }\r\n      // add group to club if not exist in club\r\n      let club = this.clubs.find((club) => club.id === team.club.id);\r\n      if (!club.groups.find((group) => group.id === team.group.id)) {\r\n        club.groups.push(team.group);\r\n        club.groups[club.groups.length - 1].teams = [];\r\n      }\r\n      // sort groups\r\n      club.groups.sort((a, b) => {\r\n        // get number from string\r\n        let aNumber;\r\n        if (parseInt(a.name.match(/\\d+/))) {\r\n          aNumber = parseInt(a.name.match(/\\d+/)[0]);\r\n        }\r\n        let bNumber;\r\n        if (parseInt(b.name.match(/\\d+/))) {\r\n          bNumber = parseInt(b.name.match(/\\d+/)[0]);\r\n        }\r\n        if (aNumber < bNumber) {\r\n          return -1;\r\n        }\r\n        if (aNumber > bNumber) {\r\n          return 1;\r\n        }\r\n        // if aNumber == bNumber then sort by name\r\n        if (a.name < b.name) {\r\n          return -1;\r\n        }\r\n        if (a.name > b.name) {\r\n          return 1;\r\n        }\r\n        return 0;\r\n      });\r\n    });\r\n    // sort clubs\r\n    this.clubs.sort((a, b) => {\r\n      if (a.name < b.name) {\r\n        return -1;\r\n      }\r\n      if (a.name > b.name) {\r\n        return 1;\r\n      }\r\n      return 0;\r\n    });\r\n    // set selected club\r\n    if (!this.teamSelection?.clubSelected) {\r\n      this.teamSelection = {\r\n        clubSelected: this.clubs[0],\r\n      };\r\n    }\r\n\r\n    if (this.teamSelection.clubSelected) {\r\n      if (\r\n        this.teamSelection.clubSelected.groups[0] &&\r\n        this.teamSelection.clubSelected.groups[0].season_id != this.seasonId\r\n      ) {\r\n        this.teamSelection = {\r\n          clubSelected: this.clubs[0],\r\n        };\r\n      }\r\n\r\n      // check if clubSelected is not in clubs then set clubSelected = clubs[0]\r\n      else if (\r\n        !this.clubs.find(\r\n          (club) => club.id === this.teamSelection?.clubSelected\r\n        )\r\n      ) {\r\n        this.teamSelection = {\r\n          clubSelected: this.clubs[0],\r\n        };\r\n      }\r\n    }\r\n\r\n    this.selectedClub = this.teamSelection?.clubSelected;\r\n    this.clubFilter = this.selectedClub?.id;\r\n    this.groups = this.selectedClub?.groups ? this.selectedClub?.groups : [];\r\n    // set selected group\r\n    // check if groupSelected is not in groups then set groupSelected = groups[0]\r\n    let checkGroup = this.groups.find(\r\n      (group) => group.id === this.teamSelection?.groupSelected\r\n    );\r\n    if (!checkGroup && this.groups.length > 0) {\r\n      if (!this.teamSelection?.groupSelected || !checkGroup) {\r\n        this.teamSelection = {\r\n          groupSelected: this.groups[0].id,\r\n        };\r\n      }\r\n    } else if (this.groups.length == 0) {\r\n      this.teamSelection = {\r\n        groupSelected: null,\r\n      };\r\n    }\r\n    this.selectedGroup = this.teamSelection?.groupSelected;\r\n  }\r\n\r\n  openPlayerModal(team) {\r\n    this._router.navigate(['teams/team-assignment', team.id]);\r\n  }\r\n\r\n  set teamSelection(data) {\r\n    let str_data = JSON.stringify(data);\r\n    // merge data if exist\r\n    if (localStorage.getItem('teamSelection')) {\r\n      let old_data = JSON.parse(localStorage.getItem('teamSelection'));\r\n      str_data = JSON.stringify({ ...old_data, ...data });\r\n    }\r\n    localStorage.setItem('teamSelection', str_data);\r\n  }\r\n  get teamSelection() {\r\n    let data = JSON.parse(localStorage.getItem('teamSelection'));\r\n    if (data) {\r\n      return data;\r\n    }\r\n    return null;\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <div class=\"row mb-1\">\r\n      <div class=\"col-12 col-md-4\">\r\n        <label for=\"season\">{{ 'Season' | translate }}</label>\r\n        <ng-select [searchable]=\"true\" [clearable]=\"false\" placeholder=\"{{'Select Season'|translate}}\" [(ngModel)]=\"seasonId\"\r\n          (change)=\"onChangeSeason($event)\">\r\n          <ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">{{ season.name | translate }}\r\n          </ng-option>\r\n        </ng-select>\r\n      </div>\r\n      <!-- ng select club -->\r\n      <div class=\"col-6 col-md-4\">\r\n        <label for=\"club\">{{ 'Club' | translate }}</label>\r\n        <ng-select [searchable]=\"true\" [clearable]=\"true\" placeholder=\"{{'Select Club'|translate}}\" [(ngModel)]=\"clubFilter\"\r\n          (change)=\"onSelectedClubChange($event)\">\r\n          <ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">{{ club.code }}\r\n          </ng-option>\r\n        </ng-select>\r\n      </div>\r\n      <div class=\"col-6 col-md-4\">\r\n        <label for=\"group\">{{ 'Group' | translate }}</label>\r\n        <ng-select [searchable]=\"true\" [clearable]=\"true\" placeholder=\"{{'Select Group'|translate}}\" [(ngModel)]=\"selectedGroup\"\r\n        (change)=\"onSelectedGroupChange($event)\"\r\n        >\r\n          <ng-option *ngFor=\"let group of groups\" [value]=\"group.id\" name=\"group.id\">\r\n            {{ group.name }}\r\n          </ng-option>\r\n        </ng-select>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- on select club and group show team list -->\r\n    <div class=\"row\">\r\n      <div *ngFor=\"\r\n          let team of teams\r\n            | filter : clubFilter : 'club_id' : true\r\n            | filter : selectedGroup : 'group_id' : true\r\n        \" class=\"col-lg-4 col-md-6 col-sm-12 col-12\">\r\n        <div class=\"card\">\r\n          <div class=\"card-body\">\r\n            <h4 class=\"card-title\">\r\n              <div class=\"row\">\r\n                <div class=\"col-6\">{{ team.name }}</div>\r\n                <div class=\"col-6 text-sm\">\r\n                  <h6 class=\"pl-1 fw-normal text-secondary float-right\">\r\n                    {{ team.team_players_count?team.team_players_count:0 }} {{ 'player(s)' | translate }}\r\n                  </h6>\r\n                  <h6 class=\"fw-normal text-secondary float-right\">\r\n                    {{ team.team_coaches_count?team.team_coaches_count:0 }} {{ 'coach(es)' | translate }}\r\n                  </h6>\r\n                </div>\r\n              </div>\r\n            </h4>\r\n            <hr />\r\n            <p class=\"card-text\">\r\n              {{ 'Club' | translate }}: {{ team.club.name }}\r\n            </p>\r\n            <p class=\"card-text\">\r\n              {{ 'Group' | translate }}: {{ team.group.name }}\r\n            </p>\r\n            <button (click)=\"openPlayerModal(team)\" class=\"btn btn-block btn-primary\">\r\n              {{ 'Select' | translate }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
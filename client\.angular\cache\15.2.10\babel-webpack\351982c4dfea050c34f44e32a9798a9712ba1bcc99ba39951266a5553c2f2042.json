{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nexport class OverlaysService {\n  constructor() {\n    this.document = document;\n    this.period_scoreboard_time = 500;\n    this.period_hide_scoreboard = 400;\n    this.period_score_time = 300;\n    this.period_timer_time = 200;\n    this.period_stoppage_time = 200;\n    this.isShowScoreboard = false;\n    this.isShowStoppageTime = false;\n    this.isShowTimer = false;\n    this.delay = delayInms => {\n      return new Promise(resolve => setTimeout(resolve, delayInms));\n    };\n  }\n  homeScoreChange(value) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      var score = _this.document.getElementById('home_score');\n      let scoreText = score.innerText;\n      // check if the score is the same\n      if (scoreText == value) {\n        return;\n      }\n      score.classList.add('home_score_in');\n      yield _this.delay(_this.period_score_time);\n      score.classList.remove('home_score_in');\n      score.classList.add('home_score_out');\n      // set score text\n      if (value != undefined) {\n        score.innerText = value;\n      }\n      yield _this.delay(_this.period_score_time);\n      score.classList.remove('home_score_out');\n    })();\n  }\n  awayScoreChange(value) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var score = _this2.document.getElementById('away_score');\n      // get text in score\n      let scoreText = score.innerText;\n      // check if the score is the same\n      if (scoreText == value) {\n        return;\n      }\n      score.classList.add('away_score_in');\n      yield _this2.delay(_this2.period_score_time);\n      score.classList.remove('away_score_in');\n      score.classList.add('away_score_out');\n      // set score text\n      if (value != undefined) {\n        score.innerText = value;\n      }\n      yield _this2.delay(_this2.period_score_time);\n      score.classList.remove('away_score_out');\n    })();\n  }\n  // SCOREBOARD\n  toggleScoreboard() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.isShowScoreboard = !_this3.isShowScoreboard;\n      if (_this3.isShowScoreboard) {\n        _this3.showScoreboard();\n        yield _this3.delay(_this3.period_timer_time + _this3.period_scoreboard_time);\n        _this3.toggleTimer(_this3.isShowScoreboard);\n      } else {\n        _this3.toggleTimer(_this3.isShowScoreboard);\n        yield _this3.delay(_this3.period_hide_scoreboard);\n        _this3.hideScoreboard();\n      }\n    })();\n  }\n  hideScoreboard() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      var scoreboard = _this4.document.getElementsByClassName('scoreboard_container')[0];\n      // if scoreboard is already hidden\n      if (scoreboard.classList.contains('d-none')) {\n        return;\n      }\n      scoreboard.classList.add('hide_scoreboard');\n      _this4.isShowScoreboard = false;\n      yield _this4.delay(_this4.period_hide_scoreboard);\n      scoreboard.classList.add('d-none');\n      scoreboard.classList.remove('hide_scoreboard');\n    })();\n  }\n  showScoreboard() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var scoreboard = _this5.document.getElementsByClassName('scoreboard_container')[0];\n      // if scoreboard is already shown\n      if (!scoreboard.classList.contains('d-none')) {\n        return;\n      }\n      scoreboard.classList.remove('d-none');\n      scoreboard.classList.add('show_scoreboard');\n      _this5.isShowScoreboard = true;\n      yield _this5.delay(_this5.period_hide_scoreboard);\n      scoreboard.classList.remove('show_scoreboard');\n    })();\n  }\n  // TIMER\n  toggleTimer(toggle) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      _this6.isShowTimer = !_this6.isShowTimer;\n      if (toggle != undefined) {\n        _this6.isShowTimer = toggle;\n      }\n      if (_this6.isShowTimer) {\n        if (_this6.isShowStoppageTime) {\n          _this6.showTimer();\n          yield _this6.delay(_this6.period_stoppage_time);\n          _this6.showStoppageTime();\n        } else {\n          _this6.showTimer();\n        }\n      } else {\n        if (_this6.isShowStoppageTime) {\n          _this6.hideStoppageTime();\n          yield _this6.delay(_this6.period_stoppage_time);\n          _this6.hideTimer();\n        } else {\n          _this6.hideTimer();\n        }\n      }\n    })();\n  }\n  showTimer() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      var timer = _this7.document.getElementsByClassName('timer_wrapper')[0];\n      // if timer is already shown\n      if (!timer.classList.contains('d-none')) {\n        return;\n      }\n      timer.classList.remove('d-none');\n      timer.classList.add('show_timer');\n      _this7.isShowTimer = true;\n      yield _this7.delay(_this7.period_timer_time);\n      timer.classList.remove('show_timer');\n    })();\n  }\n  hideTimer() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      var timer = _this8.document.getElementsByClassName('timer_wrapper')[0];\n      // if timer is already hidden\n      if (timer.classList.contains('d-none')) {\n        return;\n      }\n      timer.classList.add('hide_timer');\n      _this8.isShowTimer = false;\n      yield _this8.delay(_this8.period_timer_time);\n      timer.classList.add('d-none');\n      timer.classList.remove('hide_timer');\n    })();\n  }\n  // STOPPAGE TIME\n  toggleStoppageTime() {\n    this.isShowStoppageTime = !this.isShowStoppageTime;\n    if (this.isShowStoppageTime) {\n      this.showStoppageTime();\n    } else {\n      this.hideStoppageTime();\n    }\n  }\n  showStoppageTime() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      var stoppageTime = _this9.document.getElementsByClassName('stoppage-time-wraper')[0];\n      stoppageTime.classList.remove('d-none');\n      stoppageTime.classList.add('show_stoppage_time');\n      _this9.isShowStoppageTime = true;\n      yield _this9.delay(_this9.period_stoppage_time);\n      stoppageTime.classList.remove('show_stoppage_time');\n    })();\n  }\n  hideStoppageTime() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      var stoppageTime = _this10.document.getElementsByClassName('stoppage-time-wraper')[0];\n      stoppageTime.classList.add('hide_stoppage_time');\n      yield _this10.delay(_this10.period_stoppage_time);\n      stoppageTime.classList.add('d-none');\n      stoppageTime.classList.remove('hide_stoppage_time');\n    })();\n  }\n  // set home team name\n  setHomeTeamName(name) {\n    var home_name = this.document.getElementById('home_name');\n    home_name.innerText = name;\n  }\n  // set away team name\n  setAwayTeamName(name) {\n    var away_name = this.document.getElementById('away_name');\n    away_name.innerText = name;\n  }\n  // set period\n  setPeriod(period) {\n    var periode = this.document.getElementById('period');\n    periode.innerText = period;\n  }\n  // set timer\n  setTimer(time) {\n    var timer = this.document.getElementById('timer');\n    if (timer.innerText == time) {\n      return;\n    }\n    timer.innerText = time;\n  }\n  // set stoppage time\n  setStoppageTime(time) {\n    var stoppageTime = this.document.getElementById('stoppage_time');\n    stoppageTime.innerText = time;\n  }\n  // set home logo\n  setHomeLogo(src) {\n    var home_logo = this.document.getElementById('home_logo');\n    let currentSrc = home_logo.src;\n    if (currentSrc == src) {\n      return;\n    }\n    home_logo.src = src;\n  }\n  // set away logo\n  setAwayLogo(src) {\n    var away_logo = this.document.getElementById('away_logo');\n    let currentSrc = away_logo.src;\n    if (currentSrc == src) {\n      return;\n    }\n    away_logo.src = src;\n  }\n  setScoreBoardPosition(x, y) {\n    var scoreBoard = this.document.getElementsByClassName('scoreboard_container')[0];\n    scoreBoard.style.left = x + 'px';\n    scoreBoard.style.top = y + 'px';\n  }\n  setScoreBoardPositionTranslate(percentX, percentY) {\n    var scoreBoard = this.document.getElementsByClassName('scoreboard_container')[0];\n    scoreBoard.style.transform = `translate(-${percentX}%,-${percentY}%)`;\n    scoreBoard.style.left = `${percentX}%`;\n    scoreBoard.style.top = `${percentY}%`;\n  }\n  setHomeColor(color) {\n    var home_color = this.document.getElementById('home-color');\n    home_color.style.backgroundColor = color;\n  }\n  setAwayColor(color) {\n    var away_color = this.document.getElementById('away-color');\n    away_color.style.backgroundColor = color;\n  }\n  hideLogo() {\n    let logos = this.document.getElementsByClassName('logo');\n    for (let i = 0; i < logos.length; i++) {\n      logos[i].classList.add('d-none');\n    }\n  }\n  showLogo() {\n    let logos = this.document.getElementsByClassName('logo');\n    for (let i = 0; i < logos.length; i++) {\n      logos[i].classList.remove('d-none');\n    }\n  }\n  // change --scoreboard-primary-color in style tag with id =scoreboard-style\n  setPrimaryColor(color) {\n    if (!color) {\n      color = '#000';\n    }\n    var r = document.querySelector('.screen');\n    r.style.setProperty('--scoreboard-primary-color', color);\n  }\n  // change --scoreboard-secondary-color\n  setSecondaryColor(color) {\n    if (!color) {\n      color = '#00898c';\n    }\n    var r = document.querySelector('.screen');\n    r.style.setProperty('--scoreboard-secondary-color', color);\n  }\n  // change --scoreboard-accent-color\n  setAccentColor(color) {\n    if (!color) {\n      color = '#fff';\n    }\n    var r = document.querySelector('.screen');\n    r.style.setProperty('--scoreboard-accent-color', color);\n  }\n  putScoreBoardPosition(position = 'top-left') {\n    switch (position) {\n      case 'top-left':\n        this.setScoreBoardPosition(0, 0);\n        break;\n      case 'top-middle':\n        this.setScoreBoardPositionTranslate(50, 0);\n        break;\n      case 'top-right':\n        this.setScoreBoardPositionTranslate(100, 0);\n        break;\n      case 'bottom-left':\n        this.setScoreBoardPositionTranslate(0, 100);\n        break;\n      case 'bottom-middle':\n        this.setScoreBoardPositionTranslate(50, 100);\n        break;\n      case 'bottom-right':\n        this.setScoreBoardPositionTranslate(100, 100);\n        break;\n      default:\n        this.setScoreBoardPosition(0, 0);\n        break;\n    }\n  }\n  setDivision(division) {\n    let divisionEl = this.document.getElementById('division');\n    let divisionText = this.document.getElementById('division-text');\n    if (division == '') {\n      divisionEl.classList.add('d-none');\n      return;\n    } else {\n      divisionEl.classList.remove('d-none');\n    }\n    divisionText.innerText = division;\n  }\n  static #_ = this.ɵfac = function OverlaysService_Factory(t) {\n    return new (t || OverlaysService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: OverlaysService,\n    factory: OverlaysService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";;AAKA,OAAM,MAAOA,eAAe;EAU1BC,YAAA;IATA,KAAAC,QAAQ,GAAQA,QAAQ;IACxB,KAAAC,sBAAsB,GAAG,GAAG;IAC5B,KAAAC,sBAAsB,GAAG,GAAG;IAC5B,KAAAC,iBAAiB,GAAG,GAAG;IACvB,KAAAC,iBAAiB,GAAG,GAAG;IACvB,KAAAC,oBAAoB,GAAG,GAAG;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,WAAW,GAAG,KAAK;IA2UnB,KAAAC,KAAK,GAAIC,SAAS,IAAI;MACpB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAEF,SAAS,CAAC,CAAC;IACjE,CAAC;EA5Uc;EAETI,eAAeA,CAACC,KAAK;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACzB,IAAIC,KAAK,GAAGF,KAAI,CAAChB,QAAQ,CAACmB,cAAc,CAAC,YAAY,CAAC;MACtD,IAAIC,SAAS,GAAGF,KAAK,CAACG,SAAS;MAC/B;MACA,IAAID,SAAS,IAAIL,KAAK,EAAE;QACtB;;MAEFG,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;MACpC,MAAMP,KAAI,CAACP,KAAK,CAACO,KAAI,CAACb,iBAAiB,CAAC;MACxCe,KAAK,CAACI,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;MACvCN,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MACrC;MACA,IAAIR,KAAK,IAAIU,SAAS,EAAE;QACtBP,KAAK,CAACG,SAAS,GAAGN,KAAK;;MAEzB,MAAMC,KAAI,CAACP,KAAK,CAACO,KAAI,CAACb,iBAAiB,CAAC;MACxCe,KAAK,CAACI,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;IAAC;EAC3C;EAEME,eAAeA,CAACX,KAAK;IAAA,IAAAY,MAAA;IAAA,OAAAV,iBAAA;MACzB,IAAIC,KAAK,GAAGS,MAAI,CAAC3B,QAAQ,CAACmB,cAAc,CAAC,YAAY,CAAC;MACtD;MACA,IAAIC,SAAS,GAAGF,KAAK,CAACG,SAAS;MAC/B;MACA,IAAID,SAAS,IAAIL,KAAK,EAAE;QACtB;;MAEFG,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;MACpC,MAAMI,MAAI,CAAClB,KAAK,CAACkB,MAAI,CAACxB,iBAAiB,CAAC;MACxCe,KAAK,CAACI,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;MACvCN,KAAK,CAACI,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MACrC;MACA,IAAIR,KAAK,IAAIU,SAAS,EAAE;QACtBP,KAAK,CAACG,SAAS,GAAGN,KAAK;;MAEzB,MAAMY,MAAI,CAAClB,KAAK,CAACkB,MAAI,CAACxB,iBAAiB,CAAC;MACxCe,KAAK,CAACI,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;IAAC;EAC3C;EAEA;EACMI,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAZ,iBAAA;MACpBY,MAAI,CAACvB,gBAAgB,GAAG,CAACuB,MAAI,CAACvB,gBAAgB;MAC9C,IAAIuB,MAAI,CAACvB,gBAAgB,EAAE;QACzBuB,MAAI,CAACC,cAAc,EAAE;QACrB,MAAMD,MAAI,CAACpB,KAAK,CAACoB,MAAI,CAACzB,iBAAiB,GAAGyB,MAAI,CAAC5B,sBAAsB,CAAC;QACtE4B,MAAI,CAACE,WAAW,CAACF,MAAI,CAACvB,gBAAgB,CAAC;OACxC,MAAM;QACLuB,MAAI,CAACE,WAAW,CAACF,MAAI,CAACvB,gBAAgB,CAAC;QACvC,MAAMuB,MAAI,CAACpB,KAAK,CAACoB,MAAI,CAAC3B,sBAAsB,CAAC;QAC7C2B,MAAI,CAACG,cAAc,EAAE;;IACtB;EACH;EAEMA,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MAClB,IAAIiB,UAAU,GAAGD,MAAI,CAACjC,QAAQ,CAACmC,sBAAsB,CACnD,sBAAsB,CACvB,CAAC,CAAC,CAAC;MACJ;MACA,IAAID,UAAU,CAACZ,SAAS,CAACc,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC3C;;MAEFF,UAAU,CAACZ,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC3CU,MAAI,CAAC3B,gBAAgB,GAAG,KAAK;MAC7B,MAAM2B,MAAI,CAACxB,KAAK,CAACwB,MAAI,CAAC/B,sBAAsB,CAAC;MAC7CgC,UAAU,CAACZ,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAClCW,UAAU,CAACZ,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IAAC;EACjD;EAEMM,cAAcA,CAAA;IAAA,IAAAO,MAAA;IAAA,OAAApB,iBAAA;MAClB,IAAIiB,UAAU,GAAGG,MAAI,CAACrC,QAAQ,CAACmC,sBAAsB,CACnD,sBAAsB,CACvB,CAAC,CAAC,CAAC;MACJ;MACA,IAAI,CAACD,UAAU,CAACZ,SAAS,CAACc,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC5C;;MAEFF,UAAU,CAACZ,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;MACrCU,UAAU,CAACZ,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC3Cc,MAAI,CAAC/B,gBAAgB,GAAG,IAAI;MAC5B,MAAM+B,MAAI,CAAC5B,KAAK,CAAC4B,MAAI,CAACnC,sBAAsB,CAAC;MAC7CgC,UAAU,CAACZ,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IAAC;EACjD;EAEA;EACMO,WAAWA,CAACO,MAAM;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA;MACtBsB,MAAI,CAAC/B,WAAW,GAAG,CAAC+B,MAAI,CAAC/B,WAAW;MACpC,IAAI8B,MAAM,IAAIb,SAAS,EAAE;QACvBc,MAAI,CAAC/B,WAAW,GAAG8B,MAAM;;MAE3B,IAAIC,MAAI,CAAC/B,WAAW,EAAE;QACpB,IAAI+B,MAAI,CAAChC,kBAAkB,EAAE;UAC3BgC,MAAI,CAACC,SAAS,EAAE;UAChB,MAAMD,MAAI,CAAC9B,KAAK,CAAC8B,MAAI,CAAClC,oBAAoB,CAAC;UAC3CkC,MAAI,CAACE,gBAAgB,EAAE;SACxB,MAAM;UACLF,MAAI,CAACC,SAAS,EAAE;;OAEnB,MAAM;QACL,IAAID,MAAI,CAAChC,kBAAkB,EAAE;UAC3BgC,MAAI,CAACG,gBAAgB,EAAE;UACvB,MAAMH,MAAI,CAAC9B,KAAK,CAAC8B,MAAI,CAAClC,oBAAoB,CAAC;UAC3CkC,MAAI,CAACI,SAAS,EAAE;SACjB,MAAM;UACLJ,MAAI,CAACI,SAAS,EAAE;;;IAEnB;EACH;EAEMH,SAASA,CAAA;IAAA,IAAAI,MAAA;IAAA,OAAA3B,iBAAA;MACb,IAAI4B,KAAK,GAAGD,MAAI,CAAC5C,QAAQ,CAACmC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACpE;MACA,IAAI,CAACU,KAAK,CAACvB,SAAS,CAACc,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvC;;MAEFS,KAAK,CAACvB,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;MAChCqB,KAAK,CAACvB,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;MACjCqB,MAAI,CAACpC,WAAW,GAAG,IAAI;MACvB,MAAMoC,MAAI,CAACnC,KAAK,CAACmC,MAAI,CAACxC,iBAAiB,CAAC;MACxCyC,KAAK,CAACvB,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;IAAC;EACvC;EAEMmB,SAASA,CAAA;IAAA,IAAAG,MAAA;IAAA,OAAA7B,iBAAA;MACb,IAAI4B,KAAK,GAAGC,MAAI,CAAC9C,QAAQ,CAACmC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACpE;MACA,IAAIU,KAAK,CAACvB,SAAS,CAACc,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACtC;;MAEFS,KAAK,CAACvB,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;MACjCuB,MAAI,CAACtC,WAAW,GAAG,KAAK;MACxB,MAAMsC,MAAI,CAACrC,KAAK,CAACqC,MAAI,CAAC1C,iBAAiB,CAAC;MACxCyC,KAAK,CAACvB,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC7BsB,KAAK,CAACvB,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;IAAC;EACvC;EAEA;EACAuB,kBAAkBA,CAAA;IAChB,IAAI,CAACxC,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACkC,gBAAgB,EAAE;KACxB,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;;EAE3B;EAEMD,gBAAgBA,CAAA;IAAA,IAAAO,MAAA;IAAA,OAAA/B,iBAAA;MACpB,IAAIgC,YAAY,GAAGD,MAAI,CAAChD,QAAQ,CAACmC,sBAAsB,CACrD,sBAAsB,CACvB,CAAC,CAAC,CAAC;MACJc,YAAY,CAAC3B,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;MACvCyB,YAAY,CAAC3B,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAChDyB,MAAI,CAACzC,kBAAkB,GAAG,IAAI;MAC9B,MAAMyC,MAAI,CAACvC,KAAK,CAACuC,MAAI,CAAC3C,oBAAoB,CAAC;MAC3C4C,YAAY,CAAC3B,SAAS,CAACE,MAAM,CAAC,oBAAoB,CAAC;IAAC;EACtD;EAEMkB,gBAAgBA,CAAA;IAAA,IAAAQ,OAAA;IAAA,OAAAjC,iBAAA;MACpB,IAAIgC,YAAY,GAAGC,OAAI,CAAClD,QAAQ,CAACmC,sBAAsB,CACrD,sBAAsB,CACvB,CAAC,CAAC,CAAC;MACJc,YAAY,CAAC3B,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAChD,MAAM2B,OAAI,CAACzC,KAAK,CAACyC,OAAI,CAAC7C,oBAAoB,CAAC;MAC3C4C,YAAY,CAAC3B,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MACpC0B,YAAY,CAAC3B,SAAS,CAACE,MAAM,CAAC,oBAAoB,CAAC;IAAC;EACtD;EAEA;EACA2B,eAAeA,CAACC,IAAI;IAClB,IAAIC,SAAS,GAAG,IAAI,CAACrD,QAAQ,CAACmB,cAAc,CAAC,WAAW,CAAC;IACzDkC,SAAS,CAAChC,SAAS,GAAG+B,IAAI;EAC5B;EAEA;EACAE,eAAeA,CAACF,IAAI;IAClB,IAAIG,SAAS,GAAG,IAAI,CAACvD,QAAQ,CAACmB,cAAc,CAAC,WAAW,CAAC;IACzDoC,SAAS,CAAClC,SAAS,GAAG+B,IAAI;EAC5B;EAEA;EACAI,SAASA,CAACC,MAAM;IACd,IAAIC,OAAO,GAAG,IAAI,CAAC1D,QAAQ,CAACmB,cAAc,CAAC,QAAQ,CAAC;IACpDuC,OAAO,CAACrC,SAAS,GAAGoC,MAAM;EAC5B;EAEA;EACAE,QAAQA,CAACC,IAAI;IACX,IAAIf,KAAK,GAAG,IAAI,CAAC7C,QAAQ,CAACmB,cAAc,CAAC,OAAO,CAAC;IACjD,IAAI0B,KAAK,CAACxB,SAAS,IAAIuC,IAAI,EAAE;MAC3B;;IAEFf,KAAK,CAACxB,SAAS,GAAGuC,IAAI;EACxB;EAEA;EACAC,eAAeA,CAACD,IAAI;IAClB,IAAIX,YAAY,GAAG,IAAI,CAACjD,QAAQ,CAACmB,cAAc,CAAC,eAAe,CAAC;IAChE8B,YAAY,CAAC5B,SAAS,GAAGuC,IAAI;EAC/B;EAEA;EACAE,WAAWA,CAACC,GAAG;IACb,IAAIC,SAAS,GAAG,IAAI,CAAChE,QAAQ,CAACmB,cAAc,CAC1C,WAAW,CACQ;IACrB,IAAI8C,UAAU,GAAGD,SAAS,CAACD,GAAG;IAC9B,IAAIE,UAAU,IAAIF,GAAG,EAAE;MACrB;;IAEFC,SAAS,CAACD,GAAG,GAAGA,GAAG;EACrB;EAEA;EACAG,WAAWA,CAACH,GAAG;IACb,IAAII,SAAS,GAAG,IAAI,CAACnE,QAAQ,CAACmB,cAAc,CAC1C,WAAW,CACQ;IACrB,IAAI8C,UAAU,GAAGE,SAAS,CAACJ,GAAG;IAC9B,IAAIE,UAAU,IAAIF,GAAG,EAAE;MACrB;;IAEFI,SAAS,CAACJ,GAAG,GAAGA,GAAG;EACrB;EAEAK,qBAAqBA,CAACC,CAAC,EAAEC,CAAC;IACxB,IAAIC,UAAU,GAAG,IAAI,CAACvE,QAAQ,CAACmC,sBAAsB,CACnD,sBAAsB,CACvB,CAAC,CAAC,CAAC;IACJoC,UAAU,CAACC,KAAK,CAACC,IAAI,GAAGJ,CAAC,GAAG,IAAI;IAChCE,UAAU,CAACC,KAAK,CAACE,GAAG,GAAGJ,CAAC,GAAG,IAAI;EACjC;EAEAK,8BAA8BA,CAACC,QAAQ,EAAEC,QAAQ;IAC/C,IAAIN,UAAU,GAAG,IAAI,CAACvE,QAAQ,CAACmC,sBAAsB,CACnD,sBAAsB,CACvB,CAAC,CAAC,CAAC;IACJoC,UAAU,CAACC,KAAK,CAACM,SAAS,GAAG,cAAcF,QAAQ,MAAMC,QAAQ,IAAI;IACrEN,UAAU,CAACC,KAAK,CAACC,IAAI,GAAG,GAAGG,QAAQ,GAAG;IACtCL,UAAU,CAACC,KAAK,CAACE,GAAG,GAAG,GAAGG,QAAQ,GAAG;EACvC;EAEAE,YAAYA,CAACC,KAAK;IAChB,IAAIC,UAAU,GAAG,IAAI,CAACjF,QAAQ,CAACmB,cAAc,CAAC,YAAY,CAAC;IAC3D8D,UAAU,CAACT,KAAK,CAACU,eAAe,GAAGF,KAAK;EAC1C;EAEAG,YAAYA,CAACH,KAAK;IAChB,IAAII,UAAU,GAAG,IAAI,CAACpF,QAAQ,CAACmB,cAAc,CAAC,YAAY,CAAC;IAC3DiE,UAAU,CAACZ,KAAK,CAACU,eAAe,GAAGF,KAAK;EAC1C;EAEAK,QAAQA,CAAA;IACN,IAAIC,KAAK,GAAG,IAAI,CAACtF,QAAQ,CAACmC,sBAAsB,CAAC,MAAM,CAAC;IACxD,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCD,KAAK,CAACC,CAAC,CAAC,CAACjE,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;;EAEpC;EAEAkE,QAAQA,CAAA;IACN,IAAIH,KAAK,GAAG,IAAI,CAACtF,QAAQ,CAACmC,sBAAsB,CAAC,MAAM,CAAC;IACxD,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCD,KAAK,CAACC,CAAC,CAAC,CAACjE,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;;EAEvC;EAEA;EACAkE,eAAeA,CAACV,KAAK;IACnB,IAAI,CAACA,KAAK,EAAE;MACVA,KAAK,GAAG,MAAM;;IAEhB,IAAIW,CAAC,GAAG3F,QAAQ,CAAC4F,aAAa,CAAC,SAAS,CAAQ;IAChDD,CAAC,CAACnB,KAAK,CAACqB,WAAW,CAAC,4BAA4B,EAAEb,KAAK,CAAC;EAC1D;EAEA;EACAc,iBAAiBA,CAACd,KAAK;IACrB,IAAI,CAACA,KAAK,EAAE;MACVA,KAAK,GAAG,SAAS;;IAEnB,IAAIW,CAAC,GAAG3F,QAAQ,CAAC4F,aAAa,CAAC,SAAS,CAAQ;IAChDD,CAAC,CAACnB,KAAK,CAACqB,WAAW,CAAC,8BAA8B,EAAEb,KAAK,CAAC;EAC5D;EAEA;EACAe,cAAcA,CAACf,KAAK;IAClB,IAAI,CAACA,KAAK,EAAE;MACVA,KAAK,GAAG,MAAM;;IAEhB,IAAIW,CAAC,GAAG3F,QAAQ,CAAC4F,aAAa,CAAC,SAAS,CAAQ;IAChDD,CAAC,CAACnB,KAAK,CAACqB,WAAW,CAAC,2BAA2B,EAAEb,KAAK,CAAC;EACzD;EAEAgB,qBAAqBA,CAACC,QAAQ,GAAG,UAAU;IACzC,QAAQA,QAAQ;MACd,KAAK,UAAU;QACb,IAAI,CAAC7B,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;QAChC;MACF,KAAK,YAAY;QACf,IAAI,CAACO,8BAA8B,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1C;MACF,KAAK,WAAW;QACd,IAAI,CAACA,8BAA8B,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3C;MACF,KAAK,aAAa;QAChB,IAAI,CAACA,8BAA8B,CAAC,CAAC,EAAE,GAAG,CAAC;QAC3C;MACF,KAAK,eAAe;QAClB,IAAI,CAACA,8BAA8B,CAAC,EAAE,EAAE,GAAG,CAAC;QAC5C;MACF,KAAK,cAAc;QACjB,IAAI,CAACA,8BAA8B,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7C;MACF;QACE,IAAI,CAACP,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;QAChC;;EAEN;EAEA8B,WAAWA,CAACC,QAAgB;IAC1B,IAAIC,UAAU,GAAG,IAAI,CAACpG,QAAQ,CAACmB,cAAc,CAAC,UAAU,CAAC;IACzD,IAAIkF,YAAY,GAAG,IAAI,CAACrG,QAAQ,CAACmB,cAAc,CAAC,eAAe,CAAC;IAChE,IAAIgF,QAAQ,IAAI,EAAE,EAAE;MAClBC,UAAU,CAAC9E,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAClC;KACD,MAAM;MACL6E,UAAU,CAAC9E,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;;IAEvC6E,YAAY,CAAChF,SAAS,GAAG8E,QAAQ;EACnC;EAAC,QAAAG,CAAA;qBAlVUxG,eAAe;EAAA;EAAA,QAAAyG,EAAA;WAAfzG,eAAe;IAAA0G,OAAA,EAAf1G,eAAe,CAAA2G,IAAA;IAAAC,UAAA,EAFd;EAAM", "names": ["OverlaysService", "constructor", "document", "period_scoreboard_time", "period_hide_scoreboard", "period_score_time", "period_timer_time", "period_stoppage_time", "isShowScoreboard", "isShowStoppageTime", "isShowTimer", "delay", "delayInms", "Promise", "resolve", "setTimeout", "homeScoreChange", "value", "_this", "_asyncToGenerator", "score", "getElementById", "scoreText", "innerText", "classList", "add", "remove", "undefined", "awayScoreChange", "_this2", "toggleScoreboard", "_this3", "showScoreboard", "toggleTimer", "hideScoreboard", "_this4", "scoreboard", "getElementsByClassName", "contains", "_this5", "toggle", "_this6", "showTimer", "showStoppageTime", "hideStoppageTime", "hide<PERSON><PERSON>r", "_this7", "timer", "_this8", "toggleStoppageTime", "_this9", "stoppageTime", "_this10", "setHomeTeamName", "name", "home_name", "setAwayTeamName", "away_name", "<PERSON><PERSON><PERSON><PERSON>", "period", "periode", "setTimer", "time", "setStoppageTime", "setHomeLogo", "src", "home_logo", "currentSrc", "setAwayLogo", "away_logo", "setScoreBoardPosition", "x", "y", "scoreBoard", "style", "left", "top", "setScoreBoardPositionTranslate", "percentX", "percentY", "transform", "setHomeColor", "color", "home_color", "backgroundColor", "setAwayColor", "away_color", "<PERSON><PERSON><PERSON>", "logos", "i", "length", "showLogo", "setPrimaryColor", "r", "querySelector", "setProperty", "setSecondaryColor", "setAccentColor", "putScoreBoardPosition", "position", "setDivision", "division", "divisionEl", "divisionText", "_", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\overlays.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OverlaysService {\r\n  document: any = document;\r\n  period_scoreboard_time = 500;\r\n  period_hide_scoreboard = 400;\r\n  period_score_time = 300;\r\n  period_timer_time = 200;\r\n  period_stoppage_time = 200;\r\n  isShowScoreboard = false;\r\n  isShowStoppageTime = false;\r\n  isShowTimer = false;\r\n  constructor() {}\r\n\r\n  async homeScoreChange(value) {\r\n    var score = this.document.getElementById('home_score');\r\n    let scoreText = score.innerText;\r\n    // check if the score is the same\r\n    if (scoreText == value) {\r\n      return;\r\n    }\r\n    score.classList.add('home_score_in');\r\n    await this.delay(this.period_score_time);\r\n    score.classList.remove('home_score_in');\r\n    score.classList.add('home_score_out');\r\n    // set score text\r\n    if (value != undefined) {\r\n      score.innerText = value;\r\n    }\r\n    await this.delay(this.period_score_time);\r\n    score.classList.remove('home_score_out');\r\n  }\r\n\r\n  async awayScoreChange(value) {\r\n    var score = this.document.getElementById('away_score');\r\n    // get text in score\r\n    let scoreText = score.innerText;\r\n    // check if the score is the same\r\n    if (scoreText == value) {\r\n      return;\r\n    }\r\n    score.classList.add('away_score_in');\r\n    await this.delay(this.period_score_time);\r\n    score.classList.remove('away_score_in');\r\n    score.classList.add('away_score_out');\r\n    // set score text\r\n    if (value != undefined) {\r\n      score.innerText = value;\r\n    }\r\n    await this.delay(this.period_score_time);\r\n    score.classList.remove('away_score_out');\r\n  }\r\n\r\n  // SCOREBOARD\r\n  async toggleScoreboard() {\r\n    this.isShowScoreboard = !this.isShowScoreboard;\r\n    if (this.isShowScoreboard) {\r\n      this.showScoreboard();\r\n      await this.delay(this.period_timer_time + this.period_scoreboard_time);\r\n      this.toggleTimer(this.isShowScoreboard);\r\n    } else {\r\n      this.toggleTimer(this.isShowScoreboard);\r\n      await this.delay(this.period_hide_scoreboard);\r\n      this.hideScoreboard();\r\n    }\r\n  }\r\n\r\n  async hideScoreboard() {\r\n    var scoreboard = this.document.getElementsByClassName(\r\n      'scoreboard_container'\r\n    )[0];\r\n    // if scoreboard is already hidden\r\n    if (scoreboard.classList.contains('d-none')) {\r\n      return;\r\n    }\r\n    scoreboard.classList.add('hide_scoreboard');\r\n    this.isShowScoreboard = false;\r\n    await this.delay(this.period_hide_scoreboard);\r\n    scoreboard.classList.add('d-none');\r\n    scoreboard.classList.remove('hide_scoreboard');\r\n  }\r\n\r\n  async showScoreboard() {\r\n    var scoreboard = this.document.getElementsByClassName(\r\n      'scoreboard_container'\r\n    )[0];\r\n    // if scoreboard is already shown\r\n    if (!scoreboard.classList.contains('d-none')) {\r\n      return;\r\n    }\r\n    scoreboard.classList.remove('d-none');\r\n    scoreboard.classList.add('show_scoreboard');\r\n    this.isShowScoreboard = true;\r\n    await this.delay(this.period_hide_scoreboard);\r\n    scoreboard.classList.remove('show_scoreboard');\r\n  }\r\n\r\n  // TIMER\r\n  async toggleTimer(toggle) {\r\n    this.isShowTimer = !this.isShowTimer;\r\n    if (toggle != undefined) {\r\n      this.isShowTimer = toggle;\r\n    }\r\n    if (this.isShowTimer) {\r\n      if (this.isShowStoppageTime) {\r\n        this.showTimer();\r\n        await this.delay(this.period_stoppage_time);\r\n        this.showStoppageTime();\r\n      } else {\r\n        this.showTimer();\r\n      }\r\n    } else {\r\n      if (this.isShowStoppageTime) {\r\n        this.hideStoppageTime();\r\n        await this.delay(this.period_stoppage_time);\r\n        this.hideTimer();\r\n      } else {\r\n        this.hideTimer();\r\n      }\r\n    }\r\n  }\r\n\r\n  async showTimer() {\r\n    var timer = this.document.getElementsByClassName('timer_wrapper')[0];\r\n    // if timer is already shown\r\n    if (!timer.classList.contains('d-none')) {\r\n      return;\r\n    }\r\n    timer.classList.remove('d-none');\r\n    timer.classList.add('show_timer');\r\n    this.isShowTimer = true;\r\n    await this.delay(this.period_timer_time);\r\n    timer.classList.remove('show_timer');\r\n  }\r\n\r\n  async hideTimer() {\r\n    var timer = this.document.getElementsByClassName('timer_wrapper')[0];\r\n    // if timer is already hidden\r\n    if (timer.classList.contains('d-none')) {\r\n      return;\r\n    }\r\n    timer.classList.add('hide_timer');\r\n    this.isShowTimer = false;\r\n    await this.delay(this.period_timer_time);\r\n    timer.classList.add('d-none');\r\n    timer.classList.remove('hide_timer');\r\n  }\r\n\r\n  // STOPPAGE TIME\r\n  toggleStoppageTime() {\r\n    this.isShowStoppageTime = !this.isShowStoppageTime;\r\n    if (this.isShowStoppageTime) {\r\n      this.showStoppageTime();\r\n    } else {\r\n      this.hideStoppageTime();\r\n    }\r\n  }\r\n\r\n  async showStoppageTime() {\r\n    var stoppageTime = this.document.getElementsByClassName(\r\n      'stoppage-time-wraper'\r\n    )[0];\r\n    stoppageTime.classList.remove('d-none');\r\n    stoppageTime.classList.add('show_stoppage_time');\r\n    this.isShowStoppageTime = true;\r\n    await this.delay(this.period_stoppage_time);\r\n    stoppageTime.classList.remove('show_stoppage_time');\r\n  }\r\n\r\n  async hideStoppageTime() {\r\n    var stoppageTime = this.document.getElementsByClassName(\r\n      'stoppage-time-wraper'\r\n    )[0];\r\n    stoppageTime.classList.add('hide_stoppage_time');\r\n    await this.delay(this.period_stoppage_time);\r\n    stoppageTime.classList.add('d-none');\r\n    stoppageTime.classList.remove('hide_stoppage_time');\r\n  }\r\n\r\n  // set home team name\r\n  setHomeTeamName(name) {\r\n    var home_name = this.document.getElementById('home_name');\r\n    home_name.innerText = name;\r\n  }\r\n\r\n  // set away team name\r\n  setAwayTeamName(name) {\r\n    var away_name = this.document.getElementById('away_name');\r\n    away_name.innerText = name;\r\n  }\r\n\r\n  // set period\r\n  setPeriod(period) {\r\n    var periode = this.document.getElementById('period');\r\n    periode.innerText = period;\r\n  }\r\n\r\n  // set timer\r\n  setTimer(time) {\r\n    var timer = this.document.getElementById('timer');\r\n    if (timer.innerText == time) {\r\n      return;\r\n    }\r\n    timer.innerText = time;\r\n  }\r\n\r\n  // set stoppage time\r\n  setStoppageTime(time) {\r\n    var stoppageTime = this.document.getElementById('stoppage_time');\r\n    stoppageTime.innerText = time;\r\n  }\r\n\r\n  // set home logo\r\n  setHomeLogo(src) {\r\n    var home_logo = this.document.getElementById(\r\n      'home_logo'\r\n    ) as HTMLImageElement;\r\n    let currentSrc = home_logo.src;\r\n    if (currentSrc == src) {\r\n      return;\r\n    }\r\n    home_logo.src = src;\r\n  }\r\n\r\n  // set away logo\r\n  setAwayLogo(src) {\r\n    var away_logo = this.document.getElementById(\r\n      'away_logo'\r\n    ) as HTMLImageElement;\r\n    let currentSrc = away_logo.src;\r\n    if (currentSrc == src) {\r\n      return;\r\n    }\r\n    away_logo.src = src;\r\n  }\r\n\r\n  setScoreBoardPosition(x, y) {\r\n    var scoreBoard = this.document.getElementsByClassName(\r\n      'scoreboard_container'\r\n    )[0];\r\n    scoreBoard.style.left = x + 'px';\r\n    scoreBoard.style.top = y + 'px';\r\n  }\r\n\r\n  setScoreBoardPositionTranslate(percentX, percentY) {\r\n    var scoreBoard = this.document.getElementsByClassName(\r\n      'scoreboard_container'\r\n    )[0];\r\n    scoreBoard.style.transform = `translate(-${percentX}%,-${percentY}%)`;\r\n    scoreBoard.style.left = `${percentX}%`;\r\n    scoreBoard.style.top = `${percentY}%`;\r\n  }\r\n\r\n  setHomeColor(color) {\r\n    var home_color = this.document.getElementById('home-color');\r\n    home_color.style.backgroundColor = color;\r\n  }\r\n\r\n  setAwayColor(color) {\r\n    var away_color = this.document.getElementById('away-color');\r\n    away_color.style.backgroundColor = color;\r\n  }\r\n\r\n  hideLogo() {\r\n    let logos = this.document.getElementsByClassName('logo');\r\n    for (let i = 0; i < logos.length; i++) {\r\n      logos[i].classList.add('d-none');\r\n    }\r\n  }\r\n\r\n  showLogo() {\r\n    let logos = this.document.getElementsByClassName('logo');\r\n    for (let i = 0; i < logos.length; i++) {\r\n      logos[i].classList.remove('d-none');\r\n    }\r\n  }\r\n\r\n  // change --scoreboard-primary-color in style tag with id =scoreboard-style\r\n  setPrimaryColor(color) {\r\n    if (!color) {\r\n      color = '#000';\r\n    }\r\n    var r = document.querySelector('.screen') as any;\r\n    r.style.setProperty('--scoreboard-primary-color', color);\r\n  }\r\n\r\n  // change --scoreboard-secondary-color\r\n  setSecondaryColor(color) {\r\n    if (!color) {\r\n      color = '#00898c';\r\n    }\r\n    var r = document.querySelector('.screen') as any;\r\n    r.style.setProperty('--scoreboard-secondary-color', color);\r\n  }\r\n\r\n  // change --scoreboard-accent-color\r\n  setAccentColor(color) {\r\n    if (!color) {\r\n      color = '#fff';\r\n    }\r\n    var r = document.querySelector('.screen') as any;\r\n    r.style.setProperty('--scoreboard-accent-color', color);\r\n  }\r\n\r\n  putScoreBoardPosition(position = 'top-left') {\r\n    switch (position) {\r\n      case 'top-left':\r\n        this.setScoreBoardPosition(0, 0);\r\n        break;\r\n      case 'top-middle':\r\n        this.setScoreBoardPositionTranslate(50, 0);\r\n        break;\r\n      case 'top-right':\r\n        this.setScoreBoardPositionTranslate(100, 0);\r\n        break;\r\n      case 'bottom-left':\r\n        this.setScoreBoardPositionTranslate(0, 100);\r\n        break;\r\n      case 'bottom-middle':\r\n        this.setScoreBoardPositionTranslate(50, 100);\r\n        break;\r\n      case 'bottom-right':\r\n        this.setScoreBoardPositionTranslate(100, 100);\r\n        break;\r\n      default:\r\n        this.setScoreBoardPosition(0, 0);\r\n        break;\r\n    }\r\n  }\r\n\r\n  setDivision(division: string) {\r\n    let divisionEl = this.document.getElementById('division');\r\n    let divisionText = this.document.getElementById('division-text');\r\n    if (division == '') {\r\n      divisionEl.classList.add('d-none');\r\n      return;\r\n    } else {\r\n      divisionEl.classList.remove('d-none');\r\n    }\r\n    divisionText.innerText = division;\r\n  }\r\n\r\n  delay = (delayInms) => {\r\n    return new Promise((resolve) => setTimeout(resolve, delayInms));\r\n  };\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
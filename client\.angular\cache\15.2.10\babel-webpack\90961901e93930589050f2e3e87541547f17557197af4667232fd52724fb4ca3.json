{"ast": null, "code": "import { merge as mergeStatic } from '../observable/merge';\nexport function merge(...observables) {\n  return source => source.lift.call(mergeStatic(source, ...observables));\n}", "map": {"version": 3, "names": ["merge", "mergeStatic", "observables", "source", "lift", "call"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/merge.js"], "sourcesContent": ["import { merge as mergeStatic } from '../observable/merge';\nexport function merge(...observables) {\n    return (source) => source.lift.call(mergeStatic(source, ...observables));\n}\n"], "mappings": "AAAA,SAASA,KAAK,IAAIC,WAAW,QAAQ,qBAAqB;AAC1D,OAAO,SAASD,KAAKA,CAAC,GAAGE,WAAW,EAAE;EAClC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACJ,WAAW,CAACE,MAAM,EAAE,GAAGD,WAAW,CAAC,CAAC;AAC5E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
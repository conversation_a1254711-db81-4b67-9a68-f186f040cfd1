{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { map } from './map';\nimport { from } from '../observable/from';\nexport function exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return source => source.pipe(exhaustMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n  return source => source.lift(new ExhaustMapOperator(project));\n}\nclass ExhaustMapOperator {\n  constructor(project) {\n    this.project = project;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new ExhaustMapSubscriber(subscriber, this.project));\n  }\n}\nclass ExhaustMapSubscriber extends OuterSubscriber {\n  constructor(destination, project) {\n    super(destination);\n    this.project = project;\n    this.hasSubscription = false;\n    this.hasCompleted = false;\n    this.index = 0;\n  }\n  _next(value) {\n    if (!this.hasSubscription) {\n      this.tryNext(value);\n    }\n  }\n  tryNext(value) {\n    let result;\n    const index = this.index++;\n    try {\n      result = this.project(value, index);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n    this.hasSubscription = true;\n    this._innerSub(result, value, index);\n  }\n  _innerSub(result, value, index) {\n    const innerSubscriber = new InnerSubscriber(this, value, index);\n    const destination = this.destination;\n    destination.add(innerSubscriber);\n    const innerSubscription = subscribeToResult(this, result, undefined, undefined, innerSubscriber);\n    if (innerSubscription !== innerSubscriber) {\n      destination.add(innerSubscription);\n    }\n  }\n  _complete() {\n    this.hasCompleted = true;\n    if (!this.hasSubscription) {\n      this.destination.complete();\n    }\n    this.unsubscribe();\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.destination.next(innerValue);\n  }\n  notifyError(err) {\n    this.destination.error(err);\n  }\n  notifyComplete(innerSub) {\n    const destination = this.destination;\n    destination.remove(innerSub);\n    this.hasSubscription = false;\n    if (this.hasCompleted) {\n      this.destination.complete();\n    }\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "InnerSubscriber", "subscribeToResult", "map", "from", "exhaustMap", "project", "resultSelector", "source", "pipe", "a", "i", "b", "ii", "lift", "ExhaustMapOperator", "constructor", "call", "subscriber", "subscribe", "ExhaustMapSubscriber", "destination", "hasSubscription", "hasCompleted", "index", "_next", "value", "tryNext", "result", "err", "error", "_innerSub", "innerSubscriber", "add", "innerSubscription", "undefined", "_complete", "complete", "unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "next", "notifyError", "notifyComplete", "remove"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/exhaustMap.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { map } from './map';\nimport { from } from '../observable/from';\nexport function exhaustMap(project, resultSelector) {\n    if (resultSelector) {\n        return (source) => source.pipe(exhaustMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n    }\n    return (source) => source.lift(new ExhaustMapOperator(project));\n}\nclass ExhaustMapOperator {\n    constructor(project) {\n        this.project = project;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ExhaustMapSubscriber(subscriber, this.project));\n    }\n}\nclass ExhaustMapSubscriber extends OuterSubscriber {\n    constructor(destination, project) {\n        super(destination);\n        this.project = project;\n        this.hasSubscription = false;\n        this.hasCompleted = false;\n        this.index = 0;\n    }\n    _next(value) {\n        if (!this.hasSubscription) {\n            this.tryNext(value);\n        }\n    }\n    tryNext(value) {\n        let result;\n        const index = this.index++;\n        try {\n            result = this.project(value, index);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.hasSubscription = true;\n        this._innerSub(result, value, index);\n    }\n    _innerSub(result, value, index) {\n        const innerSubscriber = new InnerSubscriber(this, value, index);\n        const destination = this.destination;\n        destination.add(innerSubscriber);\n        const innerSubscription = subscribeToResult(this, result, undefined, undefined, innerSubscriber);\n        if (innerSubscription !== innerSubscriber) {\n            destination.add(innerSubscription);\n        }\n    }\n    _complete() {\n        this.hasCompleted = true;\n        if (!this.hasSubscription) {\n            this.destination.complete();\n        }\n        this.unsubscribe();\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.destination.next(innerValue);\n    }\n    notifyError(err) {\n        this.destination.error(err);\n    }\n    notifyComplete(innerSub) {\n        const destination = this.destination;\n        destination.remove(innerSub);\n        this.hasSubscription = false;\n        if (this.hasCompleted) {\n            this.destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,GAAG,QAAQ,OAAO;AAC3B,SAASC,IAAI,QAAQ,oBAAoB;AACzC,OAAO,SAASC,UAAUA,CAACC,OAAO,EAAEC,cAAc,EAAE;EAChD,IAAIA,cAAc,EAAE;IAChB,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,CAAC,EAAEC,CAAC,KAAKP,IAAI,CAACE,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACN,GAAG,CAAC,CAACS,CAAC,EAAEC,EAAE,KAAKN,cAAc,CAACG,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/H;EACA,OAAQL,MAAM,IAAKA,MAAM,CAACM,IAAI,CAAC,IAAIC,kBAAkB,CAACT,OAAO,CAAC,CAAC;AACnE;AACA,MAAMS,kBAAkB,CAAC;EACrBC,WAAWA,CAACV,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAW,IAAIA,CAACC,UAAU,EAAEV,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACW,SAAS,CAAC,IAAIC,oBAAoB,CAACF,UAAU,EAAE,IAAI,CAACZ,OAAO,CAAC,CAAC;EAC/E;AACJ;AACA,MAAMc,oBAAoB,SAASpB,eAAe,CAAC;EAC/CgB,WAAWA,CAACK,WAAW,EAAEf,OAAO,EAAE;IAC9B,KAAK,CAACe,WAAW,CAAC;IAClB,IAAI,CAACf,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAAC,IAAI,CAACJ,eAAe,EAAE;MACvB,IAAI,CAACK,OAAO,CAACD,KAAK,CAAC;IACvB;EACJ;EACAC,OAAOA,CAACD,KAAK,EAAE;IACX,IAAIE,MAAM;IACV,MAAMJ,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;IAC1B,IAAI;MACAI,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACoB,KAAK,EAAEF,KAAK,CAAC;IACvC,CAAC,CACD,OAAOK,GAAG,EAAE;MACR,IAAI,CAACR,WAAW,CAACS,KAAK,CAACD,GAAG,CAAC;MAC3B;IACJ;IACA,IAAI,CAACP,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACS,SAAS,CAACH,MAAM,EAAEF,KAAK,EAAEF,KAAK,CAAC;EACxC;EACAO,SAASA,CAACH,MAAM,EAAEF,KAAK,EAAEF,KAAK,EAAE;IAC5B,MAAMQ,eAAe,GAAG,IAAI/B,eAAe,CAAC,IAAI,EAAEyB,KAAK,EAAEF,KAAK,CAAC;IAC/D,MAAMH,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACY,GAAG,CAACD,eAAe,CAAC;IAChC,MAAME,iBAAiB,GAAGhC,iBAAiB,CAAC,IAAI,EAAE0B,MAAM,EAAEO,SAAS,EAAEA,SAAS,EAAEH,eAAe,CAAC;IAChG,IAAIE,iBAAiB,KAAKF,eAAe,EAAE;MACvCX,WAAW,CAACY,GAAG,CAACC,iBAAiB,CAAC;IACtC;EACJ;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACb,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC,IAAI,CAACD,eAAe,EAAE;MACvB,IAAI,CAACD,WAAW,CAACgB,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACAC,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACvB,WAAW,CAACwB,IAAI,CAACJ,UAAU,CAAC;EACrC;EACAK,WAAWA,CAACjB,GAAG,EAAE;IACb,IAAI,CAACR,WAAW,CAACS,KAAK,CAACD,GAAG,CAAC;EAC/B;EACAkB,cAAcA,CAACH,QAAQ,EAAE;IACrB,MAAMvB,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAAC2B,MAAM,CAACJ,QAAQ,CAAC;IAC5B,IAAI,CAACtB,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACF,WAAW,CAACgB,QAAQ,CAAC,CAAC;IAC/B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
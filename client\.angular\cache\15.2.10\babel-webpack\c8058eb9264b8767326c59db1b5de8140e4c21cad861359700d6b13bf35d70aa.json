{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i2 from \"app/services/sponsor.service\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"app/layout/components/content-header/content-header.component\";\nfunction SponsorComponent_div_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    adtData: a0,\n    captureEvents: a1\n  };\n};\nfunction SponsorComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n    i0.ɵɵtemplate(3, SponsorComponent_div_2_ng_container_3_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementStart(4, \"div\", 7);\n    i0.ɵɵelement(5, \"img\", 8);\n    i0.ɵɵelementStart(6, \"div\", 9)(7, \"h3\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"span\", 12);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const sponsor_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"sponsor_\", sponsor_r1.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.rowActionBtn)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(8, _c0, sponsor_r1, ctx_r0.emitter));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", sponsor_r1.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(sponsor_r1.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"href\", sponsor_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(sponsor_r1.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sponsor_r1.description);\n  }\n}\nexport class SponsorComponent {\n  constructor(_coreSidebarService, _sponsorService, _loadingService, _translateService) {\n    this._coreSidebarService = _coreSidebarService;\n    this._sponsorService = _sponsorService;\n    this._loadingService = _loadingService;\n    this._translateService = _translateService;\n    /* Unused*/\n    // form = new FormGroup({});\n    // model: Sponsor = {\n    //   logo: AppConfig.Fake_Player_Photo,\n    //   name: AppConfig.Fake_Player_Name,\n    //   url: AppConfig.Fake_Player_Link,\n    //   description: AppConfig.Fake_Player_Description,\n    // };\n    // options: FormlyFormOptions = {};\n    // fields: FormlyFieldConfig[] = [\n    //   {\n    //     fieldGroupClassName: 'row',\n    //     fieldGroup: [\n    //       {\n    //         className: 'col-12',\n    //         type: 'image-cropper',\n    //         key: 'logo',\n    //         props: {\n    //           translate: true,\n    //           label: this._translateService.instant('Sponsor logo'),\n    //           required: true,\n    //           upload_url: `${environment.apiUrl}/files/editor`,\n    //           accept: 'image/png,image/jpg,image/jpeg',\n    //           maxFileSize: 12000,\n    //           useCropperDialog: true,\n    //           config: {\n    //             width: 300,\n    //             height: 300,\n    //             responsiveArea: true,\n    //             resizableArea: true,\n    //             keepAspectRatio: false,\n    //             extraZoomOut: true,\n    //             autoCrop: false,\n    //             fill: '#FFFFFF'\n    //           },\n    //           test_image: 'assets/images/logo/logo.png',\n    //         },\n    //         validation: {\n    //           messages: {\n    //             required: (error, field: FormlyFieldConfig) =>{\n    //               return this._translateService.instant('This field is required', {field: this._translateService.instant(field.templateOptions.label)});\n    //             }\n    //           },\n    //           }\n    //       },\n    //       {\n    //         className: 'col-12',\n    //         type: 'input',\n    //         key: 'name',\n    //         templateOptions: {\n    //           required: true,\n    //           label: this._translateService.instant('Sponsor name'),\n    //           maxLength: 50,\n    //           placeholder: this._translateService.instant('Type sponsor name'),\n    //         },\n    //         validation: {\n    //           messages: {\n    //             required: (error, field: FormlyFieldConfig) =>{\n    //               return this._translateService.instant('This field is required', {field: this._translateService.instant(field.templateOptions.label)});\n    //             }\n    //           }\n    //         },\n    //       },\n    //       {\n    //         className: 'col-12',\n    //         type: 'input',\n    //         key: 'url',\n    //         templateOptions: {\n    //           label: this._translateService.instant('Sponsor URL'),\n    //           maxLength: 250,\n    //           placeholder: this._translateService.instant('Type sponsor URL'),\n    //           pattern: /^(https?:\\/\\/)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/\n    //         },\n    //         validation: {\n    //           show: true,\n    //           messages: {\n    //             pattern: this._translateService.instant('Invalid URL, example: https://example.com'),\n    //           },\n    //         },\n    //       },\n    //       {\n    //         className: 'col-12',\n    //         type: 'textarea',\n    //         key: 'description',\n    //         templateOptions: {\n    //           label: this._translateService.instant('Description'),\n    //           placeholder: this._translateService.instant('Type description'),\n    //           maxLength: 250,\n    //           rows: 3,\n    //         },\n    //       },\n    //     ],\n    //   },\n    // ];\n    // emitSponsor(model) {\n    //   this.getAllSponsors().then(() => {\n    //     const element = document.getElementById(`sponsor_${model.id}`);\n    //     element.scrollIntoView({ behavior: 'smooth' });\n    //   });\n    // }\n    // public rowActions: EZBtnActions[] = [\n    //   {\n    //     type: 'collection',\n    //     buttons: [\n    //       {\n    //         label: 'Edit',\n    //         onClick: (row: any) => {\n    //           console.log(row);\n    //           this.action = 'edit';\n    //           this.model = row;\n    //           this._coreSidebarService\n    //             .getSidebarRegistry(this.sideBarID)\n    //             .toggleOpen();\n    //           // scroll to position\n    //           const element = document.getElementById(this.sideBarID);\n    //           element.scrollIntoView({ behavior: 'smooth' });\n    //         },\n    //         icon: 'fa-regular fa-pen-to-square',\n    //       },\n    //       {\n    //         label: 'Delete',\n    //         onClick: (row: any) => {\n    //           Swal.fire({\n    //             title: this._translateService.instant('Are you sure?'),\n    //             text: this._translateService.instant(\n    //               \"You won't be able to revert this!\"\n    //             ),\n    //             icon: 'warning',\n    //             showCancelButton: true,\n    //             confirmButtonText: this._translateService.instant('Yes'),\n    //             cancelButtonText: this._translateService.instant('No'),\n    //           }).then((result) => {\n    //             if (result.isConfirmed) {\n    //               this._sponsorService.deleteSponsor(row.id).subscribe((data) => {\n    //                 this.getAllSponsors();\n    //                 Swal.fire({\n    //                   title: this._translateService.instant('Deleted!'),\n    //                   text: this._translateService.instant(\n    //                     'Deleted successfully'\n    //                   ),\n    //                   icon: 'success',\n    //                   showCancelButton: false,\n    //                   confirmButtonText: this._translateService.instant('OK'),\n    //                 });\n    //               });\n    //             }\n    //           });\n    //         },\n    //         icon: 'fa-regular fa-trash',\n    //       },\n    //     ],\n    //   },\n    // ];\n    // drop(event: CdkDragDrop<string[]>) {\n    //   if (event.previousContainer === event.container) {\n    //     moveItemInArray(\n    //       event.container.data,\n    //       event.previousIndex,\n    //       event.currentIndex\n    //     );\n    //     // update order value\n    //   } else {\n    //     transferArrayItem(\n    //       event.previousContainer.data,\n    //       event.container.data,\n    //       event.previousIndex,\n    //       event.currentIndex\n    //     );\n    //   }\n    //   this.sponsors.forEach((item, index) => {\n    //     item.order = index;\n    //   });\n    //   this._sponsorService\n    //     .updateSponsorOrder(this.sponsors)\n    //     .subscribe((data) => {});\n    // }\n    this.appLogo = AppConfig.Fake_Player_Photo;\n    this.isEdit = true;\n    this.isScrolled = false;\n    this.sideBarID = 'sponsor-editor';\n    this.sideBarName = 'sponsor-editor';\n    this.action = 'create';\n    this.sponsors = [];\n  }\n  getAllSponsors() {\n    this._loadingService.show();\n    return this._sponsorService.getAllSponsors().toPromise().then(res => {\n      this.sponsors = res.data;\n    });\n  }\n  toggleSidebar() {\n    this.action = 'create';\n    this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen();\n  }\n  emitter($event) {\n    console.log($event);\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: 'Sponsor',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Sponsor',\n          isLink: false\n        }]\n      }\n    };\n    this.getAllSponsors();\n  }\n  static #_ = this.ɵfac = function SponsorComponent_Factory(t) {\n    return new (t || SponsorComponent)(i0.ɵɵdirectiveInject(i1.CoreSidebarService), i0.ɵɵdirectiveInject(i2.SponsorService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SponsorComponent,\n    selectors: [[\"app-sponsor\"]],\n    inputs: {\n      isEdit: \"isEdit\"\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[3, \"contentHeader\"], [1, \"row\"], [\"class\", \"col-sm-4 col-12 mb-1\", 3, \"id\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-4\", \"col-12\", \"mb-1\", 3, \"id\"], [1, \"card\", \"border\", \"border-3\", \"mb-1\", 2, \"height\", \"100%\"], [1, \"card-body\", \"pb-1\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"d-flex\", \"flex-column\", \"align-items-center\", 2, \"gap\", \"1rem\"], [\"alt\", \"No image\", \"width\", \"120\", \"height\", \"120\", \"id\", \"sponsor-img\", 1, \"rounded\", 3, \"src\"], [1, \"d-flex\", \"flex-column\", \"align-items-center\"], [\"id\", \"sponsor-name\", 1, \"w-100\", \"text-center\", \"text-break\"], [\"target\", \"_blank\", \"id\", \"sponsor-link\", 1, \"text-primary\", \"text-center\", \"break-all\", 3, \"href\"], [\"id\", \"sponsor-description\", 1, \"text-wrap\", \"break-words\"]],\n    template: function SponsorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-content-header\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, SponsorComponent_div_2_Template, 13, 11, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.sponsors);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgTemplateOutlet, i6.ContentHeaderComponent],\n    styles: [\".cdk-drag-preview[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  border-radius: 4px;\\n  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.cdk-drag-animating[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n.tournament-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:not(.cdk-drag-placeholder) {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n#sponsor-link[_ngcontent-%COMP%] {\\n  word-break: break-all;\\n}\\n\\n#sponsor-description[_ngcontent-%COMP%] {\\n  word-break: break-word;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3BvbnNvci9zcG9uc29yLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksc0JBQUE7RUFDQSxrQkFBQTtFQUNBLHFIQUFBO0FBQ0o7O0FBSUU7RUFDRSxVQUFBO0FBREo7O0FBSUU7RUFDRSxzREFBQTtBQURKOztBQUlFO0VBQ0Usc0RBQUE7QUFESjs7QUFJRTtFQUNFLHFCQUFBO0FBREo7O0FBSUU7RUFDRSxzQkFBQTtBQURKIiwic291cmNlc0NvbnRlbnQiOlsiLmNkay1kcmFnLXByZXZpZXcge1xyXG4gICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICAgIGJveC1zaGFkb3c6IDAgNXB4IDVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4yKSxcclxuICAgICAgICAgICAgICAgIDAgOHB4IDEwcHggMXB4IHJnYmEoMCwgMCwgMCwgMC4xNCksXHJcbiAgICAgICAgICAgICAgICAwIDNweCAxNHB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMTIpO1xyXG4gIH1cclxuICBcclxuICAuY2RrLWRyYWctcGxhY2Vob2xkZXIge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICB9XHJcbiAgXHJcbiAgLmNkay1kcmFnLWFuaW1hdGluZyB7XHJcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMjUwbXMgY3ViaWMtYmV6aWVyKDAsIDAsIDAuMiwgMSk7XHJcbiAgfVxyXG4gIFxyXG4gIC50b3VybmFtZW50LWxpc3QuY2RrLWRyb3AtbGlzdC1kcmFnZ2luZyAuY2FyZDpub3QoLmNkay1kcmFnLXBsYWNlaG9sZGVyKSB7XHJcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMjUwbXMgY3ViaWMtYmV6aWVyKDAsIDAsIDAuMiwgMSk7XHJcbiAgfVxyXG5cclxuICAjc3BvbnNvci1saW5re1xyXG4gICAgd29yZC1icmVhazogYnJlYWstYWxsO1xyXG4gIH1cclxuXHJcbiAgI3Nwb25zb3ItZGVzY3JpcHRpb257XHJcbiAgICB3b3JkLWJyZWFrOiBicmVhay13b3JkO1xyXG4gIH0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;;;;;ICkElCC,EAAA,CAAAC,kBAAA,GAKgB;;;;;;;;;;;IAZtBD,EAAA,CAAAE,cAAA,aAIC;IAGKF,EAAA,CAAAG,UAAA,IAAAC,8CAAA,0BAKgB;IAChBJ,EAAA,CAAAE,cAAA,aAAqE;IACnEF,EAAA,CAAAK,SAAA,aAOE;IACFL,EAAA,CAAAE,cAAA,aAAmD;IACUF,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAClFP,EAAA,CAAAE,cAAA,YAKG;IAAAF,EAAA,CAAAM,MAAA,IAAiB;IAAAN,EAAA,CAAAO,YAAA,EACnB;IAEHP,EAAA,CAAAE,cAAA,gBAA6D;IAAAF,EAAA,CAAAM,MAAA,IAE3D;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;IA/BfP,EAAA,CAAAQ,sBAAA,mBAAAC,UAAA,CAAAC,EAAA,KAA6B;IAKtBV,EAAA,CAAAW,SAAA,GAEa;IAFbX,EAAA,CAAAY,UAAA,qBAAAC,MAAA,CAAAC,YAAA,CAEa,4BAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAP,UAAA,EAAAI,MAAA,CAAAI,OAAA;IAMZjB,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAY,UAAA,QAAAH,UAAA,CAAAS,IAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAoB;IAOuCnB,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAoB,iBAAA,CAAAX,UAAA,CAAAY,IAAA,CAAkB;IAE3ErB,EAAA,CAAAW,SAAA,GAAwB;IAAxBX,EAAA,CAAAsB,qBAAA,SAAAb,UAAA,CAAAc,GAAA,EAAAvB,EAAA,CAAAmB,aAAA,CAAwB;IAIvBnB,EAAA,CAAAW,SAAA,GAAiB;IAAjBX,EAAA,CAAAoB,iBAAA,CAAAX,UAAA,CAAAc,GAAA,CAAiB;IAGuCvB,EAAA,CAAAW,SAAA,GAE3D;IAF2DX,EAAA,CAAAoB,iBAAA,CAAAX,UAAA,CAAAe,WAAA,CAE3D;;;ADxEZ,OAAM,MAAOC,gBAAgB;EAwM3BC,YACUC,mBAAuC,EACvCC,eAA+B,EAC/BC,eAA+B,EAC/BC,iBAAmC;IAHnC,KAAAH,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IA3M3B;IAEA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA,KAAAC,OAAO,GAAGhC,SAAS,CAACiC,iBAAiB;IAC5B,KAAAC,MAAM,GAAY,IAAI;IAC/B,KAAAC,UAAU,GAAY,KAAK;IAE3B,KAAAC,SAAS,GAAW,gBAAgB;IACpC,KAAAC,WAAW,GAAW,gBAAgB;IACtC,KAAAC,MAAM,GAAG,QAAQ;IAEjB,KAAAC,QAAQ,GAAc,EAAE;EASrB;EAEHC,cAAcA,CAAA;IACZ,IAAI,CAACV,eAAe,CAACW,IAAI,EAAE;IAC3B,OAAO,IAAI,CAACZ,eAAe,CACxBW,cAAc,EAAE,CAChBE,SAAS,EAAE,CACXC,IAAI,CAAEC,GAAG,IAAI;MACZ,IAAI,CAACL,QAAQ,GAAGK,GAAG,CAACC,IAAI;IAC1B,CAAC,CAAC;EACN;EACAC,aAAaA,CAAA;IACX,IAAI,CAACR,MAAM,GAAG,QAAQ;IACtB,IAAI,CAACV,mBAAmB,CAACmB,kBAAkB,CAAC,IAAI,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAC1E;EAEA9B,OAAOA,CAAC+B,MAAM;IACZC,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;EACrB;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,SAAS;MACtBC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEpC,IAAI,EAAE,SAAS;UACfqC,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACnB,cAAc,EAAE;EACvB;EAAC,QAAAoB,CAAA;qBAjPUlC,gBAAgB,EAAAzB,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA9D,EAAA,CAAA4D,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhE,EAAA,CAAA4D,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlE,EAAA,CAAA4D,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAAhB5C,gBAAgB;IAAA6C,SAAA;IAAAC,MAAA;MAAAtC,MAAA;IAAA;IAAAuC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCoC7B7E,EAAA,CAAAK,SAAA,4BAAyE;QACzEL,EAAA,CAAAE,cAAA,aAAiB;QACfF,EAAA,CAAAG,UAAA,IAAA4E,+BAAA,mBAsCM;QACR/E,EAAA,CAAAO,YAAA,EAAM;;;QAzCcP,EAAA,CAAAY,UAAA,kBAAAkE,GAAA,CAAA1B,aAAA,CAA+B;QAI3BpD,EAAA,CAAAW,SAAA,GAAW;QAAXX,EAAA,CAAAY,UAAA,YAAAkE,GAAA,CAAAxC,QAAA,CAAW", "names": ["AppConfig", "i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵtemplate", "SponsorComponent_div_2_ng_container_3_Template", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵpropertyInterpolate1", "sponsor_r1", "id", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "rowActionBtn", "ɵɵpureFunction2", "_c0", "emitter", "logo", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "name", "ɵɵpropertyInterpolate", "url", "description", "SponsorComponent", "constructor", "_coreSidebarService", "_sponsorService", "_loadingService", "_translateService", "appLogo", "Fake_Player_Photo", "isEdit", "isScrolled", "sideBarID", "sideBarName", "action", "sponsors", "getAllSponsors", "show", "to<PERSON>romise", "then", "res", "data", "toggleSidebar", "getSidebarRegistry", "toggle<PERSON><PERSON>", "$event", "console", "log", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "isLink", "_", "ɵɵdirectiveInject", "i1", "CoreSidebarService", "i2", "SponsorService", "i3", "LoadingService", "i4", "TranslateService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "SponsorComponent_Template", "rf", "ctx", "SponsorComponent_div_2_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\sponsor\\sponsor.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\sponsor\\sponsor.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { environment } from 'environments/environment';\r\nimport { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';\r\nimport Swal from 'sweetalert2';\r\nimport { SponsorService } from 'app/services/sponsor.service';\r\nimport { Sponsor } from 'app/interfaces/sponsor';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport {\r\n  CdkDragDrop,\r\n  moveItemInArray,\r\n  transferArrayItem,\r\n} from '@angular/cdk/drag-drop';\r\n\r\n@Component({\r\n  selector: 'app-sponsor',\r\n  templateUrl: './sponsor.component.html',\r\n  styleUrls: ['./sponsor.component.scss'],\r\n})\r\nexport class SponsorComponent {\r\n  /* Unused*/\r\n\r\n  // form = new FormGroup({});\r\n\r\n  // model: Sponsor = {\r\n  //   logo: AppConfig.Fake_Player_Photo,\r\n  //   name: AppConfig.Fake_Player_Name,\r\n  //   url: AppConfig.Fake_Player_Link,\r\n  //   description: AppConfig.Fake_Player_Description,\r\n  // };\r\n  // options: FormlyFormOptions = {};\r\n  // fields: FormlyFieldConfig[] = [\r\n  //   {\r\n  //     fieldGroupClassName: 'row',\r\n  //     fieldGroup: [\r\n  //       {\r\n  //         className: 'col-12',\r\n  //         type: 'image-cropper',\r\n  //         key: 'logo',\r\n  //         props: {\r\n  //           translate: true,\r\n  //           label: this._translateService.instant('Sponsor logo'),\r\n  //           required: true,\r\n  //           upload_url: `${environment.apiUrl}/files/editor`,\r\n  //           accept: 'image/png,image/jpg,image/jpeg',\r\n  //           maxFileSize: 12000,\r\n  //           useCropperDialog: true,\r\n  //           config: {\r\n  //             width: 300,\r\n  //             height: 300,\r\n  //             responsiveArea: true,\r\n  //             resizableArea: true,\r\n  //             keepAspectRatio: false,\r\n  //             extraZoomOut: true,\r\n  //             autoCrop: false,\r\n  //             fill: '#FFFFFF'\r\n  //           },\r\n  //           test_image: 'assets/images/logo/logo.png',\r\n  //         },\r\n  //         validation: {\r\n  //           messages: {\r\n  //             required: (error, field: FormlyFieldConfig) =>{\r\n  //               return this._translateService.instant('This field is required', {field: this._translateService.instant(field.templateOptions.label)});\r\n  //             }\r\n  //           },\r\n  //           }\r\n  //       },\r\n  //       {\r\n  //         className: 'col-12',\r\n  //         type: 'input',\r\n  //         key: 'name',\r\n  //         templateOptions: {\r\n  //           required: true,\r\n  //           label: this._translateService.instant('Sponsor name'),\r\n  //           maxLength: 50,\r\n  //           placeholder: this._translateService.instant('Type sponsor name'),\r\n  //         },\r\n  //         validation: {\r\n  //           messages: {\r\n  //             required: (error, field: FormlyFieldConfig) =>{\r\n  //               return this._translateService.instant('This field is required', {field: this._translateService.instant(field.templateOptions.label)});\r\n  //             }\r\n  //           }\r\n  //         },\r\n  //       },\r\n  //       {\r\n  //         className: 'col-12',\r\n  //         type: 'input',\r\n  //         key: 'url',\r\n  //         templateOptions: {\r\n  //           label: this._translateService.instant('Sponsor URL'),\r\n  //           maxLength: 250,\r\n  //           placeholder: this._translateService.instant('Type sponsor URL'),\r\n  //           pattern: /^(https?:\\/\\/)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/\r\n\r\n  //         },\r\n  //         validation: {\r\n  //           show: true,\r\n  //           messages: {\r\n  //             pattern: this._translateService.instant('Invalid URL, example: https://example.com'),\r\n  //           },\r\n  //         },\r\n  //       },\r\n  //       {\r\n  //         className: 'col-12',\r\n  //         type: 'textarea',\r\n  //         key: 'description',\r\n  //         templateOptions: {\r\n  //           label: this._translateService.instant('Description'),\r\n  //           placeholder: this._translateService.instant('Type description'),\r\n  //           maxLength: 250,\r\n  //           rows: 3,\r\n  //         },\r\n  //       },\r\n  //     ],\r\n  //   },\r\n  // ];\r\n\r\n  // emitSponsor(model) {\r\n  //   this.getAllSponsors().then(() => {\r\n  //     const element = document.getElementById(`sponsor_${model.id}`);\r\n  //     element.scrollIntoView({ behavior: 'smooth' });\r\n  //   });\r\n  // }\r\n\r\n  // public rowActions: EZBtnActions[] = [\r\n  //   {\r\n  //     type: 'collection',\r\n  //     buttons: [\r\n  //       {\r\n  //         label: 'Edit',\r\n  //         onClick: (row: any) => {\r\n  //           console.log(row);\r\n  //           this.action = 'edit';\r\n  //           this.model = row;\r\n\r\n  //           this._coreSidebarService\r\n  //             .getSidebarRegistry(this.sideBarID)\r\n  //             .toggleOpen();\r\n\r\n  //           // scroll to position\r\n  //           const element = document.getElementById(this.sideBarID);\r\n  //           element.scrollIntoView({ behavior: 'smooth' });\r\n  //         },\r\n  //         icon: 'fa-regular fa-pen-to-square',\r\n  //       },\r\n  //       {\r\n  //         label: 'Delete',\r\n  //         onClick: (row: any) => {\r\n  //           Swal.fire({\r\n  //             title: this._translateService.instant('Are you sure?'),\r\n  //             text: this._translateService.instant(\r\n  //               \"You won't be able to revert this!\"\r\n  //             ),\r\n  //             icon: 'warning',\r\n  //             showCancelButton: true,\r\n  //             confirmButtonText: this._translateService.instant('Yes'),\r\n  //             cancelButtonText: this._translateService.instant('No'),\r\n  //           }).then((result) => {\r\n  //             if (result.isConfirmed) {\r\n  //               this._sponsorService.deleteSponsor(row.id).subscribe((data) => {\r\n  //                 this.getAllSponsors();\r\n  //                 Swal.fire({\r\n  //                   title: this._translateService.instant('Deleted!'),\r\n  //                   text: this._translateService.instant(\r\n  //                     'Deleted successfully'\r\n  //                   ),\r\n  //                   icon: 'success',\r\n  //                   showCancelButton: false,\r\n  //                   confirmButtonText: this._translateService.instant('OK'),\r\n  //                 });\r\n  //               });\r\n  //             }\r\n  //           });\r\n  //         },\r\n  //         icon: 'fa-regular fa-trash',\r\n  //       },\r\n  //     ],\r\n  //   },\r\n  // ];\r\n\r\n  // drop(event: CdkDragDrop<string[]>) {\r\n  //   if (event.previousContainer === event.container) {\r\n  //     moveItemInArray(\r\n  //       event.container.data,\r\n  //       event.previousIndex,\r\n  //       event.currentIndex\r\n  //     );\r\n  //     // update order value\r\n  //   } else {\r\n  //     transferArrayItem(\r\n  //       event.previousContainer.data,\r\n  //       event.container.data,\r\n  //       event.previousIndex,\r\n  //       event.currentIndex\r\n  //     );\r\n  //   }\r\n\r\n  //   this.sponsors.forEach((item, index) => {\r\n  //     item.order = index;\r\n  //   });\r\n\r\n  //   this._sponsorService\r\n  //     .updateSponsorOrder(this.sponsors)\r\n  //     .subscribe((data) => {});\r\n  // }\r\n\r\n  appLogo = AppConfig.Fake_Player_Photo;\r\n  @Input() isEdit: boolean = true;\r\n  isScrolled: boolean = false;\r\n\r\n  sideBarID: string = 'sponsor-editor';\r\n  sideBarName: string = 'sponsor-editor';\r\n  action = 'create';\r\n\r\n  sponsors: Sponsor[] = [];\r\n\r\n  contentHeader: object;\r\n\r\n  constructor(\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _sponsorService: SponsorService,\r\n    private _loadingService: LoadingService,\r\n    private _translateService: TranslateService\r\n  ) {}\r\n\r\n  getAllSponsors() {\r\n    this._loadingService.show();\r\n    return this._sponsorService\r\n      .getAllSponsors()\r\n      .toPromise()\r\n      .then((res) => {\r\n        this.sponsors = res.data;\r\n      });\r\n  }\r\n  toggleSidebar(): void {\r\n    this.action = 'create';\r\n    this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen();\r\n  }\r\n\r\n  emitter($event) {\r\n    console.log($event);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: 'Sponsor',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Sponsor',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    this.getAllSponsors();\r\n  }\r\n}\r\n", "<!-- <div class=\"content-wrapper container-xxl p-0\" >\r\n  <div class=\"content-body card p-1\">\r\n    content-header component\r\n    <div class=\"row\">\r\n      <app-content-header class=\"col\" [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n      <div class=\"col\">\r\n        <button [ngClass]=\"{'fixed': isScrolled }\" class=\"btn btn-primary float-right mt-1\" type=\"button\"\r\n          (click)=\"toggleSidebar()\" rippleEffect>\r\n          <i class=\"fa fa-plus\"></i> &nbsp;\r\n          <span>{{'Add new sponsor' | translate}}</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"text-center\" *ngIf=\"sponsors.length == 0\">\r\n      <h4 class=\"text-muted\">\r\n        {{'No sponsor found' | translate}}\r\n      </h4>\r\n    </div>\r\n\r\n    <div cdkDropListGroup id=\"sponsors\">\r\n      <div cdkDropList [cdkDropListData]=\"sponsors\" (cdkDropListDropped)=\"drop($event)\">\r\n        <div class=\"row\">\r\n          <div class=\"col-sm-4 col-12 card border border-3\" cdkDrag *ngFor=\"let sponsor of sponsors\" id=\"sponsor_{{sponsor.id}}\"\r\n          style=\"cursor: move; transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\">\r\n          <div class=\"card-body\">\r\n            <ng-container *ngTemplateOutlet=\"rowActionBtn; context: { adtData: sponsor, captureEvents: emitter }\"></ng-container>\r\n            <div class=\"d-flex flex-column align-items-center\">\r\n              <img class=\"rounded\"  [src]=\"sponsor.logo\" alt=\"No image\" width=\"120\" height=\"120\"\r\n                id=\"sponsor-img\">\r\n              <div class=\"d-flex flex-column align-items-center\">\r\n                <h3  id=\"sponsor-name\">{{sponsor.name}}</h3>\r\n                <a href=\"{{sponsor.url}}\" target=\"_blank\" id=\"sponsor-link\" class=\"text-primary text-center break-all\">{{sponsor.url}}</a>\r\n              </div>\r\n\r\n              <span id=\"sponsor-description\" class=\"text-wrap break-words\">{{sponsor.description}}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n  </div>\r\n  <core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"sideBarName\" [id]=\"sideBarID\"\r\n    overlayClass=\"modal-backdrop\" #coreSidebar>\r\n    <sponsor-editor [form]=\"form\" [fields]=\"fields\" [options]=\"options\" [model]=\"model\" [action]=\"action\"\r\n      [sideBarID]=\"sideBarID\" [sideBarName]=\"sideBarName\" (emitSponsor)=\"emitSponsor($event)\">\r\n    </sponsor-editor>\r\n  </core-sidebar>\r\n  <ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\r\n    <app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" [model]=\"model\" (emitter)=\"emitter($event)\"\r\n      style=\"text-align: end;\" btnStyle=\"font-size:15px;color:black!important\"\r\n      btnActionStyle=\"font-size:20px;color:black!important\"></app-btn-dropdown-action>\r\n  </ng-template>\r\n</div> -->\r\n\r\n<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n<div class=\"row\">\r\n  <div\r\n    class=\"col-sm-4 col-12 mb-1\"\r\n    *ngFor=\"let sponsor of sponsors\"\r\n    id=\"sponsor_{{ sponsor.id }}\"\r\n  >\r\n    <div class=\"card border border-3 mb-1\" style=\"height: 100%\">\r\n      <div class=\"card-body pb-1\">\r\n        <ng-container\r\n          *ngTemplateOutlet=\"\r\n            rowActionBtn;\r\n            context: { adtData: sponsor, captureEvents: emitter }\r\n          \"\r\n        ></ng-container>\r\n        <div class=\"d-flex flex-column align-items-center\" style=\"gap: 1rem\">\r\n          <img\r\n            class=\"rounded\"\r\n            [src]=\"sponsor.logo\"\r\n            alt=\"No image\"\r\n            width=\"120\"\r\n            height=\"120\"\r\n            id=\"sponsor-img\"\r\n          />\r\n          <div class=\"d-flex flex-column align-items-center\">\r\n            <h3 class=\"w-100 text-center text-break\" id=\"sponsor-name\">{{ sponsor.name }}</h3>\r\n            <a\r\n              href=\"{{ sponsor.url }}\"\r\n              target=\"_blank\"\r\n              id=\"sponsor-link\"\r\n              class=\"text-primary text-center break-all\"\r\n              >{{ sponsor.url }}</a\r\n            >\r\n          </div>\r\n          <span id=\"sponsor-description\" class=\"text-wrap break-words\">{{\r\n            sponsor.description\r\n          }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
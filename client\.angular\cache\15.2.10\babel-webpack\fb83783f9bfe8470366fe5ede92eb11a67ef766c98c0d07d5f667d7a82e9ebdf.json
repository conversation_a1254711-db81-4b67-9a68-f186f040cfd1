{"ast": null, "code": "import baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Invokes the method at `path` of `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the method to invoke.\n * @param {...*} [args] The arguments to invoke the method with.\n * @returns {*} Returns the result of the invoked method.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': [1, 2, 3, 4] } }] };\n *\n * _.invoke(object, 'a[0].b.c.slice', 1, 3);\n * // => [2, 3]\n */\nvar invoke = baseRest(baseInvoke);\nexport default invoke;", "map": {"version": 3, "names": ["baseInvoke", "baseRest", "invoke"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/invoke.js"], "sourcesContent": ["import baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Invokes the method at `path` of `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the method to invoke.\n * @param {...*} [args] The arguments to invoke the method with.\n * @returns {*} Returns the result of the invoked method.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': [1, 2, 3, 4] } }] };\n *\n * _.invoke(object, 'a[0].b.c.slice', 1, 3);\n * // => [2, 3]\n */\nvar invoke = baseRest(baseInvoke);\n\nexport default invoke;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAGD,QAAQ,CAACD,UAAU,CAAC;AAEjC,eAAeE,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
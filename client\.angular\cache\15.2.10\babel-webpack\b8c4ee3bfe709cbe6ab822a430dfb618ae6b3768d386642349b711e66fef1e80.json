{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/navigation.service\";\nexport class BackButtonDirective {\n  constructor(navigation) {\n    this.navigation = navigation;\n  }\n  onClick() {\n    this.navigation.back();\n  }\n  static #_ = this.ɵfac = function BackButtonDirective_Factory(t) {\n    return new (t || BackButtonDirective)(i0.ɵɵdirectiveInject(i1.NavigationService));\n  };\n  static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: BackButtonDirective,\n    selectors: [[\"\", \"backButton\", \"\"]],\n    hostBindings: function BackButtonDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function BackButtonDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    }\n  });\n}", "map": {"version": 3, "mappings": ";;AAMA,OAAM,MAAOA,mBAAmB;EAC9BC,YAAoBC,UAA6B;IAA7B,KAAAA,UAAU,GAAVA,UAAU;EAAsB;EAEpDC,OAAOA,CAAA;IACL,IAAI,CAACD,UAAU,CAACE,IAAI,EAAE;EACxB;EAAC,QAAAC,CAAA;qBALUL,mBAAmB,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAAnBV,mBAAmB;IAAAW,SAAA;IAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAnBC,GAAA,CAAAZ,OAAA,EAAS;QAAA", "names": ["BackButtonDirective", "constructor", "navigation", "onClick", "back", "_", "i0", "ɵɵdirectiveInject", "i1", "NavigationService", "_2", "selectors", "hostBindings", "BackButtonDirective_HostBindings", "rf", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\hostlisteners\\back-button.directive.ts"], "sourcesContent": ["import { Directive, HostListener } from '@angular/core';\r\nimport { NavigationService } from 'app/services/navigation.service';\r\n\r\n@Directive({\r\n  selector: '[backButton]',\r\n})\r\nexport class BackButtonDirective {\r\n  constructor(private navigation: NavigationService) {}\r\n  @HostListener('click')\r\n  onClick(): void {\r\n    this.navigation.back();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/commons.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/send-messages.service\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/flex-layout/extended\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"app/layout/components/content-header/content-header.component\";\nimport * as i11 from \"angular-datatables\";\nconst _c0 = [\"modalForm\"];\nfunction MessagesDetailsComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"a\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", attachment_r2.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(attachment_r2.filename);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"d-none\": a0\n  };\n};\nexport class MessagesDetailsComponent {\n  constructor(_commonsService, _translateService, _sendMessageService, _loadingService, route, _http) {\n    this._commonsService = _commonsService;\n    this._translateService = _translateService;\n    this._sendMessageService = _sendMessageService;\n    this._loadingService = _loadingService;\n    this.route = route;\n    this._http = _http;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.currentTab = 'mess_detail';\n    this.message_id = this.route.snapshot.params.id;\n  }\n  ngOnInit() {\n    this.getMessage();\n  }\n  getMessage() {\n    this._sendMessageService.getMessageById(this.message_id).subscribe(resp => {\n      this.message = resp;\n      this.contentHeader = {\n        headerTitle: 'Messages Detail',\n        actionButton: false,\n        breadcrumb: {\n          type: '',\n          links: [{\n            name: 'Messages',\n            isLink: true,\n            link: '/messages'\n          }, {\n            name: 'Messages Detail',\n            isLink: false\n          }]\n        }\n      };\n      this.buildTable();\n      this.dtTrigger.next(this.dtOptions);\n      let contentEl = document.getElementById('message');\n      contentEl.innerHTML = this.message.content;\n      this.getDataNotification(resp.season_id);\n    }, error => {\n      console.log(error);\n    });\n  }\n  buildTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        dataTablesParameters.type = this.currentTab;\n        this._http.post(`${environment.apiUrl}/send-messages/${this.message_id}/details`, dataTablesParameters).subscribe(resp => {\n          this._loadingService.dismiss();\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      // columnDefs: [\n      //   { targets: 0, responsivePriority: 1 },\n      //   { targets: -1, responsivePriority: 2 },\n      //   { targets: -2, responsivePriority: 3 },\n      // ],\n      columns: [{\n        data: 'user.first_name',\n        title: this._translateService.instant('Receiver'),\n        render: (data, type, row) => {\n          return `${row.user.first_name} ${row.user.last_name}`;\n        }\n      }, {\n        data: 'user.email',\n        title: this._translateService.instant('Email')\n      }, {\n        data: 'status',\n        title: this._translateService.instant('Status'),\n        className: 'text-center text-uppercase',\n        render: (data, type, row) => {\n          if (data.email && this.currentTab == 'email') {\n            return this._translateService.instant(data.email);\n          }\n          if (data.push_noti && this.currentTab == 'push_noti') {\n            return this._translateService.instant(data.push_noti);\n          }\n          return '';\n        }\n      }, {\n        data: 'read',\n        title: this._translateService.instant('Read'),\n        className: 'text-center',\n        visible: this.currentTab == 'email' ? false : true,\n        render: (data, type, row) => {\n          return data ? this._translateService.instant('Yes') : this._translateService.instant('No');\n        }\n      }, {\n        data: 'created_at',\n        title: this._translateService.instant('Created at'),\n        render: (data, type, row) => {\n          return moment(data).format('YYYY-MM-DD HH:mm:ss');\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: []\n      }\n    };\n  }\n  tabClick(type) {\n    this._loadingService.show();\n    this.currentTab = type;\n    this.dtElement.dtInstance.then(dtInstance => {\n      if (type === 'email') {\n        dtInstance.column(3).visible(false);\n      } else {\n        dtInstance.column(3).visible(true);\n      }\n      dtInstance.ajax.reload();\n    });\n  }\n  getDataNotification(season_id) {\n    this._sendMessageService.geMessageBySeason(season_id, this.message_id).subscribe(resp => {\n      this.message_notification = resp[0];\n    });\n  }\n  ngAfterViewInit() {}\n  static #_ = this.ɵfac = function MessagesDetailsComponent_Factory(t) {\n    return new (t || MessagesDetailsComponent)(i0.ɵɵdirectiveInject(i1.CommonsService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.SendMessagesService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.HttpClient));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessagesDetailsComponent,\n    selectors: [[\"app-messages-details\"]],\n    viewQuery: function MessagesDetailsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalForm = _t.first);\n      }\n    },\n    decls: 59,\n    vars: 29,\n    consts: [[3, \"contentHeader\"], [1, \"card\", \"mb-25\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"navWithIcons\", \"ngbNav\"], [\"ngbNavItem\", \"\", 3, \"click\"], [\"ngbNavLink\", \"\"], [1, \"fa-regular\", \"fa-book\"], [1, \"fa-regular\", \"fa-envelope\"], [1, \"fa-regular\", \"fa-bell\"], [1, \"card\", 3, \"ngClass\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [1, \"row\", \"mt-2\", 3, \"ngClass\"], [1, \"col-md-7\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"card-body\", 2, \"overflow-y\", \"auto\"], [\"id\", \"message\", 1, \"ck-content\"], [\"class\", \"pt-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-5\"], [1, \"card-body\"], [1, \"d-flex\"], [2, \"width\", \"20%\"], [2, \"width\", \"70%\"], [3, \"innerHTML\"], [1, \"pt-1\"], [\"target\", \"_blank\", 3, \"href\"]],\n    template: function MessagesDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-content-header\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"ul\", 2, 3)(4, \"li\", 4);\n        i0.ɵɵlistener(\"click\", function MessagesDetailsComponent_Template_li_click_4_listener() {\n          return ctx.tabClick(\"mess_detail\");\n        });\n        i0.ɵɵelementStart(5, \"a\", 5);\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"li\", 4);\n        i0.ɵɵlistener(\"click\", function MessagesDetailsComponent_Template_li_click_9_listener() {\n          return ctx.tabClick(\"email\");\n        });\n        i0.ɵɵelementStart(10, \"a\", 5);\n        i0.ɵɵelement(11, \"i\", 7);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"li\", 4);\n        i0.ɵɵlistener(\"click\", function MessagesDetailsComponent_Template_li_click_14_listener() {\n          return ctx.tabClick(\"push_noti\");\n        });\n        i0.ɵɵelementStart(15, \"a\", 5);\n        i0.ɵɵelement(16, \"i\", 8);\n        i0.ɵɵtext(17);\n        i0.ɵɵpipe(18, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(19, \"div\", 9);\n        i0.ɵɵelement(20, \"table\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"div\", 14)(25, \"div\", 15);\n        i0.ɵɵtext(26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 16);\n        i0.ɵɵelement(28, \"div\", 17);\n        i0.ɵɵtemplate(29, MessagesDetailsComponent_div_29_Template, 3, 2, \"div\", 18);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"div\", 19)(31, \"div\", 13)(32, \"div\", 20)(33, \"div\", 21)(34, \"div\", 22);\n        i0.ɵɵtext(35, \"Type:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"p\", 23);\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 21)(39, \"div\", 22);\n        i0.ɵɵtext(40, \"Send by:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"p\", 23);\n        i0.ɵɵtext(42);\n        i0.ɵɵelement(43, \"br\");\n        i0.ɵɵtext(44, \" (\");\n        i0.ɵɵelementStart(45, \"span\");\n        i0.ɵɵtext(46);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(47, \" ) \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"div\", 21)(49, \"div\", 22);\n        i0.ɵɵtext(50, \"Send to:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"p\", 23);\n        i0.ɵɵelement(52, \"span\", 24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(53, \"div\", 21)(54, \"div\", 22);\n        i0.ɵɵtext(55, \"Created at:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"p\", 23);\n        i0.ɵɵtext(57);\n        i0.ɵɵpipe(58, \"date\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 16, \"Message Detail\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 18, \"Email\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 20, \"App\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c1, ctx.currentTab == \"mess_detail\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c1, ctx.currentTab != \"mess_detail\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.message == null ? null : ctx.message.title);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.message == null ? null : ctx.message.attachments);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.message_notification == null ? null : ctx.message_notification.type);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate2(\" \", ctx.message_notification == null ? null : ctx.message_notification.send_by.first_name, \" \", ctx.message_notification == null ? null : ctx.message_notification.send_by.last_name, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.message_notification == null ? null : ctx.message_notification.send_by.email);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"innerHTML\", ctx.message_notification == null ? null : ctx.message_notification.send_to, i0.ɵɵsanitizeHtml);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(58, 22, ctx.message_notification == null ? null : ctx.message_notification.created_at, \"yyyy-MM-dd HH:mm:ss\"), \" \");\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i8.DefaultClassDirective, i9.NgbNav, i9.NgbNavItem, i9.NgbNavLink, i10.ContentHeaderComponent, i11.DataTableDirective, i7.DatePipe, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,kBAAkB,QAAQ,oBAAoB;AAIvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;IC8BnBC,EAAA,CAAAC,cAAA,cAAkE;IACrBD,EAAA,CAAAE,MAAA,GAEzC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFaH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,UAAA,SAAAC,aAAA,CAAAC,GAAA,EAAAP,EAAA,CAAAQ,aAAA,CAAuB;IAACR,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAS,iBAAA,CAAAH,aAAA,CAAAI,QAAA,CAEzC;;;;;;;;ADzBZ,OAAM,MAAOC,wBAAwB;EACnCC,YACSC,eAA+B,EAC/BC,iBAAmC,EACnCC,mBAAwC,EACxCC,eAA+B,EAC/BC,KAAqB,EACrBC,KAAiB;IALjB,KAAAL,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,KAAK,GAALA,KAAK;IAMd,KAAAC,SAAS,GAAQvB,kBAAkB;IAEnC,KAAAwB,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAIvB,OAAO,EAAe;IAK5D,KAAAwB,UAAU,GAAQ,aAAa;IAZ7B,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACC,MAAM,CAACC,EAAE;EACjD;EAcAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAA,UAAUA,CAAA;IACR,IAAI,CAACb,mBAAmB,CAACc,cAAc,CAAC,IAAI,CAACN,UAAU,CAAC,CAACO,SAAS,CAC/DC,IAAS,IAAI;MACZ,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,aAAa,GAAG;QACnBC,WAAW,EAAE,iBAAiB;QAC9BC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;UACVC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,CACL;YACEC,IAAI,EAAE,UAAU;YAChBC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;WACP,EACD;YACEF,IAAI,EAAE,iBAAiB;YACvBC,MAAM,EAAE;WACT;;OAGN;MACD,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACrB,SAAS,CAACsB,IAAI,CAAC,IAAI,CAACvB,SAAS,CAAC;MACnC,IAAIwB,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;MAClDF,SAAS,CAACG,SAAS,GAAG,IAAI,CAACf,OAAO,CAACgB,OAAO;MAC1C,IAAI,CAACC,mBAAmB,CAAClB,IAAI,CAACmB,SAAS,CAAC;IAC1C,CAAC,EACAC,KAAK,IAAI;MACRC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB,CAAC,CACF;EACH;EAEAT,UAAUA,CAAA;IACR,IAAI,CAACtB,SAAS,GAAG;MACfkC,GAAG,EAAE,IAAI,CAACzC,eAAe,CAAC0C,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5CD,oBAAoB,CAACvB,IAAI,GAAG,IAAI,CAACf,UAAU;QAC3C,IAAI,CAACJ,KAAK,CACP4C,IAAI,CACH,GAAGjE,WAAW,CAACkE,MAAM,kBAAkB,IAAI,CAACxC,UAAU,UAAU,EAChEqC,oBAAoB,CACrB,CACA9B,SAAS,CAAEC,IAAS,IAAI;UACvB,IAAI,CAACf,eAAe,CAACgD,OAAO,EAAE;UAC9BH,QAAQ,CAAC;YACPI,YAAY,EAAElC,IAAI,CAACkC,YAAY;YAC/BC,eAAe,EAAEnC,IAAI,CAACmC,eAAe;YACrCC,IAAI,EAAEpC,IAAI,CAACoC;WACZ,CAAE;QACL,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAACzD,eAAe,CAAC0C,iBAAiB,CAACgB,IAAI;MACrD;MACA;MACA;MACA;MACA;MACAC,OAAO,EAAE,CACP;QACEL,IAAI,EAAE,iBAAiB;QACvBM,KAAK,EAAE,IAAI,CAAC3D,iBAAiB,CAAC4D,OAAO,CAAC,UAAU,CAAC;QACjDC,MAAM,EAAEA,CAACR,IAAI,EAAE9B,IAAI,EAAEuC,GAAG,KAAI;UAC1B,OAAO,GAAGA,GAAG,CAACC,IAAI,CAACC,UAAU,IAAIF,GAAG,CAACC,IAAI,CAACE,SAAS,EAAE;QACvD;OACD,EACD;QAAEZ,IAAI,EAAE,YAAY;QAAEM,KAAK,EAAE,IAAI,CAAC3D,iBAAiB,CAAC4D,OAAO,CAAC,OAAO;MAAC,CAAE,EACtE;QACEP,IAAI,EAAE,QAAQ;QACdM,KAAK,EAAE,IAAI,CAAC3D,iBAAiB,CAAC4D,OAAO,CAAC,QAAQ,CAAC;QAC/CM,SAAS,EAAE,4BAA4B;QACvCL,MAAM,EAAEA,CAACR,IAAI,EAAE9B,IAAI,EAAEuC,GAAG,KAAI;UAC1B,IAAIT,IAAI,CAACc,KAAK,IAAI,IAAI,CAAC3D,UAAU,IAAI,OAAO,EAAE;YAC5C,OAAO,IAAI,CAACR,iBAAiB,CAAC4D,OAAO,CAACP,IAAI,CAACc,KAAK,CAAC;;UAGnD,IAAId,IAAI,CAACe,SAAS,IAAI,IAAI,CAAC5D,UAAU,IAAI,WAAW,EAAE;YACpD,OAAO,IAAI,CAACR,iBAAiB,CAAC4D,OAAO,CAACP,IAAI,CAACe,SAAS,CAAC;;UAEvD,OAAO,EAAE;QACX;OACD,EACD;QACEf,IAAI,EAAE,MAAM;QACZM,KAAK,EAAE,IAAI,CAAC3D,iBAAiB,CAAC4D,OAAO,CAAC,MAAM,CAAC;QAC7CM,SAAS,EAAE,aAAa;QACxBG,OAAO,EAAE,IAAI,CAAC7D,UAAU,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI;QAClDqD,MAAM,EAAEA,CAACR,IAAI,EAAE9B,IAAI,EAAEuC,GAAG,KAAI;UAC1B,OAAOT,IAAI,GACP,IAAI,CAACrD,iBAAiB,CAAC4D,OAAO,CAAC,KAAK,CAAC,GACrC,IAAI,CAAC5D,iBAAiB,CAAC4D,OAAO,CAAC,IAAI,CAAC;QAC1C;OACD,EACD;QACEP,IAAI,EAAE,YAAY;QAClBM,KAAK,EAAE,IAAI,CAAC3D,iBAAiB,CAAC4D,OAAO,CAAC,YAAY,CAAC;QACnDC,MAAM,EAAEA,CAACR,IAAI,EAAE9B,IAAI,EAAEuC,GAAG,KAAI;UAC1B,OAAO7E,MAAM,CAACoE,IAAI,CAAC,CAACiB,MAAM,CAAC,qBAAqB,CAAC;QACnD;OACD,CACF;MACDC,OAAO,EAAE;QACP/B,GAAG,EAAE,IAAI,CAACzC,eAAe,CAAC0C,iBAAiB,CAAC8B,OAAO,CAAC/B,GAAG;QACvD+B,OAAO,EAAE;;KAEZ;EACH;EAEAC,QAAQA,CAACjD,IAAI;IACX,IAAI,CAACrB,eAAe,CAACuE,IAAI,EAAE;IAC3B,IAAI,CAACjE,UAAU,GAAGe,IAAI;IAClB,IAAI,CAAClB,SAAS,CAACqE,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5D,IAAInD,IAAI,KAAK,OAAO,EAAE;QACpBmD,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACP,OAAO,CAAC,KAAK,CAAC;OACpC,MAAM;QACLK,UAAU,CAACE,MAAM,CAAC,CAAC,CAAC,CAACP,OAAO,CAAC,IAAI,CAAC;;MAEpCK,UAAU,CAAC7B,IAAI,CAACgC,MAAM,EAAE;IAC1B,CAAC,CAAC;EACR;EAEA1C,mBAAmBA,CAACC,SAAkB;IACpC,IAAI,CAACnC,mBAAmB,CAAC6E,iBAAiB,CAAC1C,SAAS,EAAE,IAAI,CAAC3B,UAAU,CAAC,CAACO,SAAS,CAC7EC,IAAS,IAAI;MACZ,IAAI,CAAC8D,oBAAoB,GAAG9D,IAAI,CAAC,CAAC,CAAC;IACrC,CAAC,CACF;EACH;EAGA+D,eAAeA,CAAA,GACf;EAAC,QAAAC,CAAA;qBArKUpF,wBAAwB,EAAAX,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAApG,EAAA,CAAAgG,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAtG,EAAA,CAAAgG,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAxG,EAAA,CAAAgG,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA1G,EAAA,CAAAgG,iBAAA,CAAAW,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;UAAxBlG,wBAAwB;IAAAmG,SAAA;IAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAYxBrH,kBAAkB;;;;;;;;;;;;;;QC9B/BI,EAAA,CAAAmH,SAAA,4BAAyE;QACzEnH,EAAA,CAAAC,cAAA,aAAwB;QAELD,EAAA,CAAAoH,UAAA,mBAAAC,sDAAA;UAAA,OAASH,GAAA,CAAA5B,QAAA,CAAS,aAAa,CAAC;QAAA,EAAC;QAC9CtF,EAAA,CAAAC,cAAA,WACG;QAAAD,EAAA,CAAAmH,SAAA,WAAkC;QAAAnH,EAAA,CAAAE,MAAA,GAAkC;;QAAAF,EAAA,CAAAG,YAAA,EACtE;QAEHH,EAAA,CAAAC,cAAA,YAA2C;QAA5BD,EAAA,CAAAoH,UAAA,mBAAAE,sDAAA;UAAA,OAASJ,GAAA,CAAA5B,QAAA,CAAS,OAAO,CAAC;QAAA,EAAC;QACxCtF,EAAA,CAAAC,cAAA,YACG;QAAAD,EAAA,CAAAmH,SAAA,YAAsC;QAAAnH,EAAA,CAAAE,MAAA,IAAyB;;QAAAF,EAAA,CAAAG,YAAA,EACjE;QAEHH,EAAA,CAAAC,cAAA,aAA+C;QAAhCD,EAAA,CAAAoH,UAAA,mBAAAG,uDAAA;UAAA,OAASL,GAAA,CAAA5B,QAAA,CAAS,WAAW,CAAC;QAAA,EAAC;QAC5CtF,EAAA,CAAAC,cAAA,YACG;QAAAD,EAAA,CAAAmH,SAAA,YAAkC;QAAAnH,EAAA,CAAAE,MAAA,IAAuB;;QAAAF,EAAA,CAAAG,YAAA,EAC3D;QAMPH,EAAA,CAAAC,cAAA,cAAwE;QACtED,EAAA,CAAAmH,SAAA,iBAKS;QACXnH,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA4E;QAK5CD,EAAA,CAAAE,MAAA,IAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAEpDH,EAAA,CAAAC,cAAA,eAA0E;QACxED,EAAA,CAAAmH,SAAA,eAA2C;QAC3CnH,EAAA,CAAAwH,UAAA,KAAAC,wCAAA,kBAIM;QACRzH,EAAA,CAAAG,YAAA,EAAM;QAGVH,EAAA,CAAAC,cAAA,eAAsB;QAIUD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACnCH,EAAA,CAAAC,cAAA,aAAsB;QAAAD,EAAA,CAAAE,MAAA,IAAgC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE5DH,EAAA,CAAAC,cAAA,eAAoB;QACMD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACtCH,EAAA,CAAAC,cAAA,aAAsB;QACpBD,EAAA,CAAAE,MAAA,IAEA;QAAAF,EAAA,CAAAmH,SAAA,UAAM;QACNnH,EAAA,CAAAE,MAAA,UAAC;QAAAF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,IAAyC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAACH,EAAA,CAAAE,MAAA,WAC1D;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,eAAoB;QACMD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACtCH,EAAA,CAAAC,cAAA,aAAsB;QACpBD,EAAA,CAAAmH,SAAA,gBAAyD;QAC3DnH,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,eAAoB;QACMD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACzCH,EAAA,CAAAC,cAAA,aAAsB;QACpBD,EAAA,CAAAE,MAAA,IAGF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;QA5EMH,EAAA,CAAAK,UAAA,kBAAA6G,GAAA,CAAAjF,aAAA,CAA+B;QAKRjC,EAAA,CAAAI,SAAA,GAAkC;QAAlCJ,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAA0H,WAAA,0BAAkC;QAK9B1H,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAA0H,WAAA,kBAAyB;QAK7B1H,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAA0H,WAAA,gBAAuB;QAO7D1H,EAAA,CAAAI,SAAA,GAAqD;QAArDJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA2H,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAA5F,UAAA,mBAAqD;QAGtDtB,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAK,UAAA,cAAA6G,GAAA,CAAA9F,SAAA,CAAuB,cAAA8F,GAAA,CAAA7F,SAAA;QAMtBrB,EAAA,CAAAI,SAAA,GAAqD;QAArDJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA2H,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAA5F,UAAA,mBAAqD;QAK1BtB,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAS,iBAAA,CAAAyG,GAAA,CAAAlF,OAAA,kBAAAkF,GAAA,CAAAlF,OAAA,CAAAyC,KAAA,CAAoB;QAIHzE,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAK,UAAA,YAAA6G,GAAA,CAAAlF,OAAA,kBAAAkF,GAAA,CAAAlF,OAAA,CAAA6F,WAAA,CAAuB;QAaxC7H,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAS,iBAAA,CAAAyG,GAAA,CAAArB,oBAAA,kBAAAqB,GAAA,CAAArB,oBAAA,CAAAxD,IAAA,CAAgC;QAKpDrC,EAAA,CAAAI,SAAA,GAEA;QAFAJ,EAAA,CAAA8H,kBAAA,MAAAZ,GAAA,CAAArB,oBAAA,kBAAAqB,GAAA,CAAArB,oBAAA,CAAAkC,OAAA,CAAAjD,UAAA,OAAAoC,GAAA,CAAArB,oBAAA,kBAAAqB,GAAA,CAAArB,oBAAA,CAAAkC,OAAA,CAAAhD,SAAA,MAEA;QACO/E,EAAA,CAAAI,SAAA,GAAyC;QAAzCJ,EAAA,CAAAS,iBAAA,CAAAyG,GAAA,CAAArB,oBAAA,kBAAAqB,GAAA,CAAArB,oBAAA,CAAAkC,OAAA,CAAA9C,KAAA,CAAyC;QAM1CjF,EAAA,CAAAI,SAAA,GAA2C;QAA3CJ,EAAA,CAAAK,UAAA,cAAA6G,GAAA,CAAArB,oBAAA,kBAAAqB,GAAA,CAAArB,oBAAA,CAAAmC,OAAA,EAAAhI,EAAA,CAAAiI,cAAA,CAA2C;QAMjDjI,EAAA,CAAAI,SAAA,GAGF;QAHEJ,EAAA,CAAAkI,kBAAA,MAAAlI,EAAA,CAAAmI,WAAA,SAAAjB,GAAA,CAAArB,oBAAA,kBAAAqB,GAAA,CAAArB,oBAAA,CAAAuC,UAAA,8BAGF", "names": ["DataTableDirective", "environment", "Subject", "moment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "attachment_r2", "url", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "filename", "MessagesDetailsComponent", "constructor", "_commonsService", "_translateService", "_sendMessageService", "_loadingService", "route", "_http", "dtElement", "dtOptions", "dtTrigger", "currentTab", "message_id", "snapshot", "params", "id", "ngOnInit", "getMessage", "getMessageById", "subscribe", "resp", "message", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "link", "buildTable", "next", "contentEl", "document", "getElementById", "innerHTML", "content", "getDataNotification", "season_id", "error", "console", "log", "dom", "dataTableDefaults", "select", "serverSide", "rowId", "ajax", "dataTablesParameters", "callback", "post", "apiUrl", "dismiss", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "columns", "title", "instant", "render", "row", "user", "first_name", "last_name", "className", "email", "push_noti", "visible", "format", "buttons", "tabClick", "show", "dtInstance", "then", "column", "reload", "geMessageBySeason", "message_notification", "ngAfterViewInit", "_", "ɵɵdirectiveInject", "i1", "CommonsService", "i2", "TranslateService", "i3", "SendMessagesService", "i4", "LoadingService", "i5", "ActivatedRoute", "i6", "HttpClient", "_2", "selectors", "viewQuery", "MessagesDetailsComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "MessagesDetailsComponent_Template_li_click_4_listener", "MessagesDetailsComponent_Template_li_click_9_listener", "MessagesDetailsComponent_Template_li_click_14_listener", "ɵɵtemplate", "MessagesDetailsComponent_div_29_Template", "ɵɵpipeBind1", "ɵɵpureFunction1", "_c1", "attachments", "ɵɵtextInterpolate2", "send_by", "send_to", "ɵɵsanitizeHtml", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "created_at"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\messages\\messages-details\\messages-details.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\messages\\messages-details\\messages-details.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { SendMessagesService } from 'app/services/send-messages.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport moment from 'moment';\r\nimport { LoadingService } from 'app/services/loading.service';\r\n\r\n@Component({\r\n  selector: 'app-messages-details',\r\n  templateUrl: './messages-details.component.html',\r\n  styleUrls: ['./messages-details.component.scss'],\r\n})\r\nexport class MessagesDetailsComponent implements OnInit {\r\n  constructor(\r\n    public _commonsService: CommonsService,\r\n    public _translateService: TranslateService,\r\n    public _sendMessageService: SendMessagesService,\r\n    public _loadingService: LoadingService,\r\n    public route: ActivatedRoute,\r\n    public _http: HttpClient\r\n  ) {\r\n    this.message_id = this.route.snapshot.params.id;\r\n  }\r\n\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @ViewChild('modalForm') modalForm: any;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  message_id: any;\r\n  message: any;\r\n  message_notification: any;\r\n  season_id: number;\r\n  currentTab: any = 'mess_detail';\r\n  public contentHeader: object;\r\n\r\n  ngOnInit(): void {\r\n    this.getMessage();\r\n  }\r\n\r\n  getMessage() {\r\n    this._sendMessageService.getMessageById(this.message_id).subscribe(\r\n      (resp: any) => {\r\n        this.message = resp;\r\n        this.contentHeader = {\r\n          headerTitle: 'Messages Detail',\r\n          actionButton: false,\r\n          breadcrumb: {\r\n            type: '',\r\n            links: [\r\n              {\r\n                name: 'Messages',\r\n                isLink: true,\r\n                link: '/messages',\r\n              },\r\n              {\r\n                name: 'Messages Detail',\r\n                isLink: false,\r\n              },\r\n            ],\r\n          },\r\n        };\r\n        this.buildTable();\r\n        this.dtTrigger.next(this.dtOptions);\r\n        let contentEl = document.getElementById('message');\r\n        contentEl.innerHTML = this.message.content;\r\n        this.getDataNotification(resp.season_id);\r\n      },\r\n      (error) => {\r\n        console.log(error);\r\n      }\r\n    );\r\n  }\r\n\r\n  buildTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        dataTablesParameters.type = this.currentTab;\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/send-messages/${this.message_id}/details`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this._loadingService.dismiss();\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            },);\r\n          });\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      // columnDefs: [\r\n      //   { targets: 0, responsivePriority: 1 },\r\n      //   { targets: -1, responsivePriority: 2 },\r\n      //   { targets: -2, responsivePriority: 3 },\r\n      // ],\r\n      columns: [\r\n        {\r\n          data: 'user.first_name',\r\n          title: this._translateService.instant('Receiver'),\r\n          render: (data, type, row) => {\r\n            return `${row.user.first_name} ${row.user.last_name}`;\r\n          },\r\n        },\r\n        { data: 'user.email', title: this._translateService.instant('Email') },\r\n        {\r\n          data: 'status',\r\n          title: this._translateService.instant('Status'),\r\n          className: 'text-center text-uppercase',\r\n          render: (data, type, row) => {\r\n            if (data.email && this.currentTab == 'email') {\r\n              return this._translateService.instant(data.email);\r\n            }\r\n\r\n            if (data.push_noti && this.currentTab == 'push_noti') {\r\n              return this._translateService.instant(data.push_noti);\r\n            }\r\n            return '';\r\n          },\r\n        },\r\n        {\r\n          data: 'read',\r\n          title: this._translateService.instant('Read'),\r\n          className: 'text-center',\r\n          visible: this.currentTab == 'email' ? false : true,\r\n          render: (data, type, row) => {\r\n            return data\r\n              ? this._translateService.instant('Yes')\r\n              : this._translateService.instant('No');\r\n          },\r\n        },\r\n        {\r\n          data: 'created_at',\r\n          title: this._translateService.instant('Created at'),\r\n          render: (data, type, row) => {\r\n            return moment(data).format('YYYY-MM-DD HH:mm:ss');\r\n          },\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [],\r\n      },\r\n    };\r\n  }\r\n\r\n  tabClick(type) {\r\n    this._loadingService.show();\r\n    this.currentTab = type;\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          if (type === 'email') {\r\n            dtInstance.column(3).visible(false);\r\n          } else {\r\n            dtInstance.column(3).visible(true);\r\n          }\r\n          dtInstance.ajax.reload();\r\n        });\r\n  }\r\n\r\n  getDataNotification(season_id : number) {\r\n    this._sendMessageService.geMessageBySeason(season_id, this.message_id).subscribe(\r\n      (resp: any) => {\r\n        this.message_notification = resp[0];\r\n      }\r\n    );\r\n  }\r\n  \r\n\r\n  ngAfterViewInit() {\r\n  }\r\n}\r\n", "<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n<div class=\"card mb-25\">\r\n  <ul ngbNav #navWithIcons=\"ngbNav\" class=\"nav-tabs m-0\">\r\n    <li ngbNavItem (click)=\"tabClick('mess_detail')\">\r\n      <a ngbNavLink\r\n        ><i class=\"fa-regular fa-book\"></i>{{ 'Message Detail' | translate }}</a\r\n      >\r\n    </li>\r\n    <li ngbNavItem (click)=\"tabClick('email')\">\r\n      <a ngbNavLink\r\n        ><i class=\"fa-regular fa-envelope\"></i>{{ 'Email' | translate }}</a\r\n      >\r\n    </li>\r\n    <li ngbNavItem (click)=\"tabClick('push_noti')\">\r\n      <a ngbNavLink\r\n        ><i class=\"fa-regular fa-bell\"></i>{{ 'App' | translate }}</a\r\n      >\r\n    </li>\r\n  </ul>\r\n</div>\r\n\r\n<!-- Conditional display based on currentTab -->\r\n<div [ngClass]=\"{ 'd-none': currentTab == 'mess_detail' }\" class=\"card\">\r\n  <table\r\n    datatable\r\n    [dtOptions]=\"dtOptions\"\r\n    [dtTrigger]=\"dtTrigger\"\r\n    class=\"table row-border hover\"\r\n  ></table>\r\n</div>\r\n\r\n<div [ngClass]=\"{ 'd-none': currentTab != 'mess_detail' }\" class=\"row mt-2\">\r\n  <!-- Content for 'Message Detail' tab -->\r\n  <div class=\"col-md-7\">\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <div class=\"card-title\">{{ message?.title }}</div>\r\n      </div>\r\n      <div class=\"card-body\" style=\"overflow-y: auto\" tabindex=\"0\" ngbAutofocus>\r\n        <div class=\"ck-content\" id=\"message\"></div>\r\n        <div class=\"pt-1\" *ngFor=\"let attachment of message?.attachments\">\r\n          <a target=\"_blank\" [href]=\"attachment.url\">{{\r\n            attachment.filename\r\n          }}</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"col-md-5\">\r\n    <div class=\"card\">\r\n      <div class=\"card-body\">\r\n        <div class=\"d-flex\">\r\n          <div style=\"width: 20%\">Type:</div>\r\n          <p style=\"width: 70%\">{{ message_notification?.type }}</p>\r\n        </div>\r\n        <div class=\"d-flex\">\r\n          <div style=\"width: 20%\">Send by:</div>\r\n          <p style=\"width: 70%\">\r\n            {{ message_notification?.send_by.first_name }}\r\n            {{ message_notification?.send_by.last_name }}\r\n            <br />\r\n            (<span>{{ message_notification?.send_by.email }}</span> )\r\n          </p>\r\n        </div>\r\n        <div class=\"d-flex\">\r\n          <div style=\"width: 20%\">Send to:</div>\r\n          <p style=\"width: 70%\">\r\n            <span [innerHTML]=\"message_notification?.send_to\"></span>\r\n          </p>\r\n        </div>\r\n        <div class=\"d-flex\">\r\n          <div style=\"width: 20%\">Created at:</div>\r\n          <p style=\"width: 70%\">\r\n            {{\r\n              message_notification?.created_at | date : 'yyyy-MM-dd HH:mm:ss'\r\n            }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
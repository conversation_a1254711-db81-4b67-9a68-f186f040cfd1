{"ast": null, "code": "import { Subject } from '../Subject';\nimport { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function windowTime(windowTimeSpan) {\n  let scheduler = async;\n  let windowCreationInterval = null;\n  let maxWindowSize = Number.POSITIVE_INFINITY;\n  if (isScheduler(arguments[3])) {\n    scheduler = arguments[3];\n  }\n  if (isScheduler(arguments[2])) {\n    scheduler = arguments[2];\n  } else if (isNumeric(arguments[2])) {\n    maxWindowSize = Number(arguments[2]);\n  }\n  if (isScheduler(arguments[1])) {\n    scheduler = arguments[1];\n  } else if (isNumeric(arguments[1])) {\n    windowCreationInterval = Number(arguments[1]);\n  }\n  return function windowTimeOperatorFunction(source) {\n    return source.lift(new WindowTimeOperator(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler));\n  };\n}\nclass WindowTimeOperator {\n  constructor(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n    this.windowTimeSpan = windowTimeSpan;\n    this.windowCreationInterval = windowCreationInterval;\n    this.maxWindowSize = maxWindowSize;\n    this.scheduler = scheduler;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new WindowTimeSubscriber(subscriber, this.windowTimeSpan, this.windowCreationInterval, this.maxWindowSize, this.scheduler));\n  }\n}\nclass CountedSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._numberOfNextedValues = 0;\n  }\n  next(value) {\n    this._numberOfNextedValues++;\n    super.next(value);\n  }\n  get numberOfNextedValues() {\n    return this._numberOfNextedValues;\n  }\n}\nclass WindowTimeSubscriber extends Subscriber {\n  constructor(destination, windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n    super(destination);\n    this.destination = destination;\n    this.windowTimeSpan = windowTimeSpan;\n    this.windowCreationInterval = windowCreationInterval;\n    this.maxWindowSize = maxWindowSize;\n    this.scheduler = scheduler;\n    this.windows = [];\n    const window = this.openWindow();\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      const closeState = {\n        subscriber: this,\n        window,\n        context: null\n      };\n      const creationState = {\n        windowTimeSpan,\n        windowCreationInterval,\n        subscriber: this,\n        scheduler\n      };\n      this.add(scheduler.schedule(dispatchWindowClose, windowTimeSpan, closeState));\n      this.add(scheduler.schedule(dispatchWindowCreation, windowCreationInterval, creationState));\n    } else {\n      const timeSpanOnlyState = {\n        subscriber: this,\n        window,\n        windowTimeSpan\n      };\n      this.add(scheduler.schedule(dispatchWindowTimeSpanOnly, windowTimeSpan, timeSpanOnlyState));\n    }\n  }\n  _next(value) {\n    const windows = this.windows;\n    const len = windows.length;\n    for (let i = 0; i < len; i++) {\n      const window = windows[i];\n      if (!window.closed) {\n        window.next(value);\n        if (window.numberOfNextedValues >= this.maxWindowSize) {\n          this.closeWindow(window);\n        }\n      }\n    }\n  }\n  _error(err) {\n    const windows = this.windows;\n    while (windows.length > 0) {\n      windows.shift().error(err);\n    }\n    this.destination.error(err);\n  }\n  _complete() {\n    const windows = this.windows;\n    while (windows.length > 0) {\n      const window = windows.shift();\n      if (!window.closed) {\n        window.complete();\n      }\n    }\n    this.destination.complete();\n  }\n  openWindow() {\n    const window = new CountedSubject();\n    this.windows.push(window);\n    const destination = this.destination;\n    destination.next(window);\n    return window;\n  }\n  closeWindow(window) {\n    window.complete();\n    const windows = this.windows;\n    windows.splice(windows.indexOf(window), 1);\n  }\n}\nfunction dispatchWindowTimeSpanOnly(state) {\n  const {\n    subscriber,\n    windowTimeSpan,\n    window\n  } = state;\n  if (window) {\n    subscriber.closeWindow(window);\n  }\n  state.window = subscriber.openWindow();\n  this.schedule(state, windowTimeSpan);\n}\nfunction dispatchWindowCreation(state) {\n  const {\n    windowTimeSpan,\n    subscriber,\n    scheduler,\n    windowCreationInterval\n  } = state;\n  const window = subscriber.openWindow();\n  const action = this;\n  let context = {\n    action,\n    subscription: null\n  };\n  const timeSpanState = {\n    subscriber,\n    window,\n    context\n  };\n  context.subscription = scheduler.schedule(dispatchWindowClose, windowTimeSpan, timeSpanState);\n  action.add(context.subscription);\n  action.schedule(state, windowCreationInterval);\n}\nfunction dispatchWindowClose(state) {\n  const {\n    subscriber,\n    window,\n    context\n  } = state;\n  if (context && context.action && context.subscription) {\n    context.action.remove(context.subscription);\n  }\n  subscriber.closeWindow(window);\n}", "map": {"version": 3, "names": ["Subject", "async", "Subscriber", "isNumeric", "isScheduler", "windowTime", "windowTimeSpan", "scheduler", "windowCreationInterval", "maxWindowSize", "Number", "POSITIVE_INFINITY", "arguments", "windowTimeOperatorFunction", "source", "lift", "WindowTimeOperator", "constructor", "call", "subscriber", "subscribe", "WindowTimeSubscriber", "CountedSubject", "_numberOfNextedValues", "next", "value", "numberOfNextedValues", "destination", "windows", "window", "openWindow", "closeState", "context", "creationState", "add", "schedule", "dispatchWindowClose", "dispatchWindowCreation", "timeSpanOnlyState", "dispatchWindowTimeSpanOnly", "_next", "len", "length", "i", "closed", "closeWindow", "_error", "err", "shift", "error", "_complete", "complete", "push", "splice", "indexOf", "state", "action", "subscription", "timeSpanState", "remove"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/windowTime.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function windowTime(windowTimeSpan) {\n    let scheduler = async;\n    let windowCreationInterval = null;\n    let maxWindowSize = Number.POSITIVE_INFINITY;\n    if (isScheduler(arguments[3])) {\n        scheduler = arguments[3];\n    }\n    if (isScheduler(arguments[2])) {\n        scheduler = arguments[2];\n    }\n    else if (isNumeric(arguments[2])) {\n        maxWindowSize = Number(arguments[2]);\n    }\n    if (isScheduler(arguments[1])) {\n        scheduler = arguments[1];\n    }\n    else if (isNumeric(arguments[1])) {\n        windowCreationInterval = Number(arguments[1]);\n    }\n    return function windowTimeOperatorFunction(source) {\n        return source.lift(new WindowTimeOperator(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler));\n    };\n}\nclass WindowTimeOperator {\n    constructor(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n        this.windowTimeSpan = windowTimeSpan;\n        this.windowCreationInterval = windowCreationInterval;\n        this.maxWindowSize = maxWindowSize;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WindowTimeSubscriber(subscriber, this.windowTimeSpan, this.windowCreationInterval, this.maxWindowSize, this.scheduler));\n    }\n}\nclass CountedSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this._numberOfNextedValues = 0;\n    }\n    next(value) {\n        this._numberOfNextedValues++;\n        super.next(value);\n    }\n    get numberOfNextedValues() {\n        return this._numberOfNextedValues;\n    }\n}\nclass WindowTimeSubscriber extends Subscriber {\n    constructor(destination, windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n        super(destination);\n        this.destination = destination;\n        this.windowTimeSpan = windowTimeSpan;\n        this.windowCreationInterval = windowCreationInterval;\n        this.maxWindowSize = maxWindowSize;\n        this.scheduler = scheduler;\n        this.windows = [];\n        const window = this.openWindow();\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            const closeState = { subscriber: this, window, context: null };\n            const creationState = { windowTimeSpan, windowCreationInterval, subscriber: this, scheduler };\n            this.add(scheduler.schedule(dispatchWindowClose, windowTimeSpan, closeState));\n            this.add(scheduler.schedule(dispatchWindowCreation, windowCreationInterval, creationState));\n        }\n        else {\n            const timeSpanOnlyState = { subscriber: this, window, windowTimeSpan };\n            this.add(scheduler.schedule(dispatchWindowTimeSpanOnly, windowTimeSpan, timeSpanOnlyState));\n        }\n    }\n    _next(value) {\n        const windows = this.windows;\n        const len = windows.length;\n        for (let i = 0; i < len; i++) {\n            const window = windows[i];\n            if (!window.closed) {\n                window.next(value);\n                if (window.numberOfNextedValues >= this.maxWindowSize) {\n                    this.closeWindow(window);\n                }\n            }\n        }\n    }\n    _error(err) {\n        const windows = this.windows;\n        while (windows.length > 0) {\n            windows.shift().error(err);\n        }\n        this.destination.error(err);\n    }\n    _complete() {\n        const windows = this.windows;\n        while (windows.length > 0) {\n            const window = windows.shift();\n            if (!window.closed) {\n                window.complete();\n            }\n        }\n        this.destination.complete();\n    }\n    openWindow() {\n        const window = new CountedSubject();\n        this.windows.push(window);\n        const destination = this.destination;\n        destination.next(window);\n        return window;\n    }\n    closeWindow(window) {\n        window.complete();\n        const windows = this.windows;\n        windows.splice(windows.indexOf(window), 1);\n    }\n}\nfunction dispatchWindowTimeSpanOnly(state) {\n    const { subscriber, windowTimeSpan, window } = state;\n    if (window) {\n        subscriber.closeWindow(window);\n    }\n    state.window = subscriber.openWindow();\n    this.schedule(state, windowTimeSpan);\n}\nfunction dispatchWindowCreation(state) {\n    const { windowTimeSpan, subscriber, scheduler, windowCreationInterval } = state;\n    const window = subscriber.openWindow();\n    const action = this;\n    let context = { action, subscription: null };\n    const timeSpanState = { subscriber, window, context };\n    context.subscription = scheduler.schedule(dispatchWindowClose, windowTimeSpan, timeSpanState);\n    action.add(context.subscription);\n    action.schedule(state, windowCreationInterval);\n}\nfunction dispatchWindowClose(state) {\n    const { subscriber, window, context } = state;\n    if (context && context.action && context.subscription) {\n        context.action.remove(context.subscription);\n    }\n    subscriber.closeWindow(window);\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,SAASC,UAAUA,CAACC,cAAc,EAAE;EACvC,IAAIC,SAAS,GAAGN,KAAK;EACrB,IAAIO,sBAAsB,GAAG,IAAI;EACjC,IAAIC,aAAa,GAAGC,MAAM,CAACC,iBAAiB;EAC5C,IAAIP,WAAW,CAACQ,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3BL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC;EAC5B;EACA,IAAIR,WAAW,CAACQ,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3BL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC;EAC5B,CAAC,MACI,IAAIT,SAAS,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9BH,aAAa,GAAGC,MAAM,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;EACxC;EACA,IAAIR,WAAW,CAACQ,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3BL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC;EAC5B,CAAC,MACI,IAAIT,SAAS,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9BJ,sBAAsB,GAAGE,MAAM,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;EACjD;EACA,OAAO,SAASC,0BAA0BA,CAACC,MAAM,EAAE;IAC/C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,kBAAkB,CAACV,cAAc,EAAEE,sBAAsB,EAAEC,aAAa,EAAEF,SAAS,CAAC,CAAC;EAChH,CAAC;AACL;AACA,MAAMS,kBAAkB,CAAC;EACrBC,WAAWA,CAACX,cAAc,EAAEE,sBAAsB,EAAEC,aAAa,EAAEF,SAAS,EAAE;IAC1E,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;EACAW,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,oBAAoB,CAACF,UAAU,EAAE,IAAI,CAACb,cAAc,EAAE,IAAI,CAACE,sBAAsB,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAACF,SAAS,CAAC,CAAC;EACvJ;AACJ;AACA,MAAMe,cAAc,SAAStB,OAAO,CAAC;EACjCiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGL,SAAS,CAAC;IACnB,IAAI,CAACW,qBAAqB,GAAG,CAAC;EAClC;EACAC,IAAIA,CAACC,KAAK,EAAE;IACR,IAAI,CAACF,qBAAqB,EAAE;IAC5B,KAAK,CAACC,IAAI,CAACC,KAAK,CAAC;EACrB;EACA,IAAIC,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACH,qBAAqB;EACrC;AACJ;AACA,MAAMF,oBAAoB,SAASnB,UAAU,CAAC;EAC1Ce,WAAWA,CAACU,WAAW,EAAErB,cAAc,EAAEE,sBAAsB,EAAEC,aAAa,EAAEF,SAAS,EAAE;IACvF,KAAK,CAACoB,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACrB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACqB,OAAO,GAAG,EAAE;IACjB,MAAMC,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAItB,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAChE,MAAMuB,UAAU,GAAG;QAAEZ,UAAU,EAAE,IAAI;QAAEU,MAAM;QAAEG,OAAO,EAAE;MAAK,CAAC;MAC9D,MAAMC,aAAa,GAAG;QAAE3B,cAAc;QAAEE,sBAAsB;QAAEW,UAAU,EAAE,IAAI;QAAEZ;MAAU,CAAC;MAC7F,IAAI,CAAC2B,GAAG,CAAC3B,SAAS,CAAC4B,QAAQ,CAACC,mBAAmB,EAAE9B,cAAc,EAAEyB,UAAU,CAAC,CAAC;MAC7E,IAAI,CAACG,GAAG,CAAC3B,SAAS,CAAC4B,QAAQ,CAACE,sBAAsB,EAAE7B,sBAAsB,EAAEyB,aAAa,CAAC,CAAC;IAC/F,CAAC,MACI;MACD,MAAMK,iBAAiB,GAAG;QAAEnB,UAAU,EAAE,IAAI;QAAEU,MAAM;QAAEvB;MAAe,CAAC;MACtE,IAAI,CAAC4B,GAAG,CAAC3B,SAAS,CAAC4B,QAAQ,CAACI,0BAA0B,EAAEjC,cAAc,EAAEgC,iBAAiB,CAAC,CAAC;IAC/F;EACJ;EACAE,KAAKA,CAACf,KAAK,EAAE;IACT,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMa,GAAG,GAAGb,OAAO,CAACc,MAAM;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC1B,MAAMd,MAAM,GAAGD,OAAO,CAACe,CAAC,CAAC;MACzB,IAAI,CAACd,MAAM,CAACe,MAAM,EAAE;QAChBf,MAAM,CAACL,IAAI,CAACC,KAAK,CAAC;QAClB,IAAII,MAAM,CAACH,oBAAoB,IAAI,IAAI,CAACjB,aAAa,EAAE;UACnD,IAAI,CAACoC,WAAW,CAAChB,MAAM,CAAC;QAC5B;MACJ;IACJ;EACJ;EACAiB,MAAMA,CAACC,GAAG,EAAE;IACR,MAAMnB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,OAAOA,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MACvBd,OAAO,CAACoB,KAAK,CAAC,CAAC,CAACC,KAAK,CAACF,GAAG,CAAC;IAC9B;IACA,IAAI,CAACpB,WAAW,CAACsB,KAAK,CAACF,GAAG,CAAC;EAC/B;EACAG,SAASA,CAAA,EAAG;IACR,MAAMtB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,OAAOA,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMb,MAAM,GAAGD,OAAO,CAACoB,KAAK,CAAC,CAAC;MAC9B,IAAI,CAACnB,MAAM,CAACe,MAAM,EAAE;QAChBf,MAAM,CAACsB,QAAQ,CAAC,CAAC;MACrB;IACJ;IACA,IAAI,CAACxB,WAAW,CAACwB,QAAQ,CAAC,CAAC;EAC/B;EACArB,UAAUA,CAAA,EAAG;IACT,MAAMD,MAAM,GAAG,IAAIP,cAAc,CAAC,CAAC;IACnC,IAAI,CAACM,OAAO,CAACwB,IAAI,CAACvB,MAAM,CAAC;IACzB,MAAMF,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACH,IAAI,CAACK,MAAM,CAAC;IACxB,OAAOA,MAAM;EACjB;EACAgB,WAAWA,CAAChB,MAAM,EAAE;IAChBA,MAAM,CAACsB,QAAQ,CAAC,CAAC;IACjB,MAAMvB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5BA,OAAO,CAACyB,MAAM,CAACzB,OAAO,CAAC0B,OAAO,CAACzB,MAAM,CAAC,EAAE,CAAC,CAAC;EAC9C;AACJ;AACA,SAASU,0BAA0BA,CAACgB,KAAK,EAAE;EACvC,MAAM;IAAEpC,UAAU;IAAEb,cAAc;IAAEuB;EAAO,CAAC,GAAG0B,KAAK;EACpD,IAAI1B,MAAM,EAAE;IACRV,UAAU,CAAC0B,WAAW,CAAChB,MAAM,CAAC;EAClC;EACA0B,KAAK,CAAC1B,MAAM,GAAGV,UAAU,CAACW,UAAU,CAAC,CAAC;EACtC,IAAI,CAACK,QAAQ,CAACoB,KAAK,EAAEjD,cAAc,CAAC;AACxC;AACA,SAAS+B,sBAAsBA,CAACkB,KAAK,EAAE;EACnC,MAAM;IAAEjD,cAAc;IAAEa,UAAU;IAAEZ,SAAS;IAAEC;EAAuB,CAAC,GAAG+C,KAAK;EAC/E,MAAM1B,MAAM,GAAGV,UAAU,CAACW,UAAU,CAAC,CAAC;EACtC,MAAM0B,MAAM,GAAG,IAAI;EACnB,IAAIxB,OAAO,GAAG;IAAEwB,MAAM;IAAEC,YAAY,EAAE;EAAK,CAAC;EAC5C,MAAMC,aAAa,GAAG;IAAEvC,UAAU;IAAEU,MAAM;IAAEG;EAAQ,CAAC;EACrDA,OAAO,CAACyB,YAAY,GAAGlD,SAAS,CAAC4B,QAAQ,CAACC,mBAAmB,EAAE9B,cAAc,EAAEoD,aAAa,CAAC;EAC7FF,MAAM,CAACtB,GAAG,CAACF,OAAO,CAACyB,YAAY,CAAC;EAChCD,MAAM,CAACrB,QAAQ,CAACoB,KAAK,EAAE/C,sBAAsB,CAAC;AAClD;AACA,SAAS4B,mBAAmBA,CAACmB,KAAK,EAAE;EAChC,MAAM;IAAEpC,UAAU;IAAEU,MAAM;IAAEG;EAAQ,CAAC,GAAGuB,KAAK;EAC7C,IAAIvB,OAAO,IAAIA,OAAO,CAACwB,MAAM,IAAIxB,OAAO,CAACyB,YAAY,EAAE;IACnDzB,OAAO,CAACwB,MAAM,CAACG,MAAM,CAAC3B,OAAO,CAACyB,YAAY,CAAC;EAC/C;EACAtC,UAAU,CAAC0B,WAAW,CAAChB,MAAM,CAAC;AAClC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
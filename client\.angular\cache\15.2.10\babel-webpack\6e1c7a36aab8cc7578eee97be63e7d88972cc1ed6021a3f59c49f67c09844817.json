{"ast": null, "code": "import { BREAKPOINT } from '@angular/flex-layout';\nconst BS_BREAKPOINTS = [{\n  alias: 'bs-xs',\n  overlapping: false,\n  mediaQuery: '(max-width: 575.98px)',\n  suffix: 'BsXs'\n}, {\n  alias: 'bs-sm',\n  overlapping: false,\n  mediaQuery: '(min-width: 576px) and (max-width: 767.98px)',\n  suffix: 'BsSm'\n}, {\n  alias: 'bs-md',\n  overlapping: false,\n  mediaQuery: '(min-width: 768px) and (max-width: 991.98px)',\n  suffix: 'BsMd'\n}, {\n  alias: 'bs-lg',\n  overlapping: false,\n  mediaQuery: '(min-width: 992px) and (max-width: 1199.98px)',\n  suffix: 'BsLg'\n}, {\n  alias: 'bs-xl',\n  overlapping: false,\n  mediaQuery: '(min-width: 1200px)',\n  suffix: 'BsXl'\n}, {\n  alias: 'bs-gt-sm',\n  overlapping: false,\n  mediaQuery: '(min-width: 576px)',\n  suffix: 'BsGtSm'\n}, {\n  alias: 'bs-gt-md',\n  overlapping: false,\n  mediaQuery: '(min-width: 768px)',\n  suffix: 'BsGtMd'\n}, {\n  alias: 'bs-gt-lg',\n  overlapping: false,\n  mediaQuery: '(min-width: 992px)',\n  suffix: 'BsGtLg'\n}, {\n  alias: 'bs-gt-xl',\n  overlapping: false,\n  mediaQuery: '(min-width: 1200px)',\n  suffix: 'BsGtXl'\n}, {\n  alias: 'bs-lt-sm',\n  overlapping: false,\n  mediaQuery: '(max-width: 575.98px)',\n  suffix: 'BsLtSm'\n}, {\n  alias: 'bs-lt-md',\n  overlapping: false,\n  mediaQuery: '(max-width: 767.98px)',\n  suffix: 'BsLtMd'\n}, {\n  alias: 'bs-lt-lg',\n  overlapping: false,\n  mediaQuery: '(max-width: 991.98px)',\n  suffix: 'BsLtLg'\n}, {\n  alias: 'bs-lt-xl',\n  overlapping: false,\n  mediaQuery: '(max-width: 1199.98px)',\n  suffix: 'BsLtXl'\n}];\nexport const CustomBreakPointsProvider = {\n  provide: BREAKPOINT,\n  useValue: BS_BREAKPOINTS,\n  multi: true\n};", "map": {"version": 3, "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AAEjD,MAAMC,cAAc,GAAG,CACrB;EAAEC,KAAK,EAAE,OAAO;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,uBAAuB;EAAEC,MAAM,EAAE;AAAM,CAAE,EAC3F;EAAEH,KAAK,EAAE,OAAO;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,8CAA8C;EAAEC,MAAM,EAAE;AAAM,CAAE,EAClH;EAAEH,KAAK,EAAE,OAAO;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,8CAA8C;EAAEC,MAAM,EAAE;AAAM,CAAE,EAClH;EAAEH,KAAK,EAAE,OAAO;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,+CAA+C;EAAEC,MAAM,EAAE;AAAM,CAAE,EACnH;EAAEH,KAAK,EAAE,OAAO;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,qBAAqB;EAAEC,MAAM,EAAE;AAAM,CAAE,EAEzF;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,oBAAoB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAC7F;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,oBAAoB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAC7F;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,oBAAoB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAC7F;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,qBAAqB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAE9F;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,uBAAuB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAChG;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,uBAAuB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAChG;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,uBAAuB;EAAEC,MAAM,EAAE;AAAQ,CAAE,EAChG;EAAEH,KAAK,EAAE,UAAU;EAAEC,WAAW,EAAE,KAAK;EAAEC,UAAU,EAAE,wBAAwB;EAAEC,MAAM,EAAE;AAAQ,CAAE,CAClG;AAED,OAAO,MAAMC,yBAAyB,GAAG;EACvCC,OAAO,EAAEP,UAAU;EACnBQ,QAAQ,EAAEP,cAAc;EACxBQ,KAAK,EAAE;CACR", "names": ["BREAKPOINT", "BS_BREAKPOINTS", "alias", "overlapping", "mediaQuery", "suffix", "CustomBreakPointsProvider", "provide", "useValue", "multi"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\custom-breakpoints.ts"], "sourcesContent": ["import { BREAKPOINT } from '@angular/flex-layout';\r\n\r\nconst BS_BREAKPOINTS = [\r\n  { alias: 'bs-xs', overlapping: false, mediaQuery: '(max-width: 575.98px)', suffix: 'BsXs' },\r\n  { alias: 'bs-sm', overlapping: false, mediaQuery: '(min-width: 576px) and (max-width: 767.98px)', suffix: 'BsSm' },\r\n  { alias: 'bs-md', overlapping: false, mediaQuery: '(min-width: 768px) and (max-width: 991.98px)', suffix: 'BsMd' },\r\n  { alias: 'bs-lg', overlapping: false, mediaQuery: '(min-width: 992px) and (max-width: 1199.98px)', suffix: 'BsLg' },\r\n  { alias: 'bs-xl', overlapping: false, mediaQuery: '(min-width: 1200px)', suffix: 'BsXl' },\r\n\r\n  { alias: 'bs-gt-sm', overlapping: false, mediaQuery: '(min-width: 576px)', suffix: 'BsGtSm' },\r\n  { alias: 'bs-gt-md', overlapping: false, mediaQuery: '(min-width: 768px)', suffix: 'BsGtMd' },\r\n  { alias: 'bs-gt-lg', overlapping: false, mediaQuery: '(min-width: 992px)', suffix: 'BsGtLg' },\r\n  { alias: 'bs-gt-xl', overlapping: false, mediaQuery: '(min-width: 1200px)', suffix: 'BsGtXl' },\r\n\r\n  { alias: 'bs-lt-sm', overlapping: false, mediaQuery: '(max-width: 575.98px)', suffix: 'BsLtSm' },\r\n  { alias: 'bs-lt-md', overlapping: false, mediaQuery: '(max-width: 767.98px)', suffix: 'BsLtMd' },\r\n  { alias: 'bs-lt-lg', overlapping: false, mediaQuery: '(max-width: 991.98px)', suffix: 'BsLtLg' },\r\n  { alias: 'bs-lt-xl', overlapping: false, mediaQuery: '(max-width: 1199.98px)', suffix: 'BsLtXl' }\r\n];\r\n\r\nexport const CustomBreakPointsProvider = {\r\n  provide: BREAKPOINT,\r\n  useValue: BS_BREAKPOINTS,\r\n  multi: true\r\n};\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
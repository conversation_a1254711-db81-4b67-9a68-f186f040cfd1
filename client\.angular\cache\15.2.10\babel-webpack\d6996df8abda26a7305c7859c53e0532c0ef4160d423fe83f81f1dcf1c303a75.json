{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/flex-layout/extended\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 10);\n  }\n  if (rf & 2) {\n    const button_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", button_r5.icon);\n  }\n}\nfunction BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const button_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(button_r5.onClick(ctx_r8.data));\n    });\n    i0.ɵɵtemplate(2, BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_i_2_Template, 1, 1, \"i\", 9);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const button_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(ctx_r4.btnStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", button_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, button_r5.label));\n  }\n}\nfunction BtnDropdownActionComponent_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function BtnDropdownActionComponent_ng_container_1_div_1_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onClick($event));\n    });\n    i0.ɵɵelement(2, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7);\n    i0.ɵɵtemplate(4, BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_Template, 6, 6, \"ng-container\", 1);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(ctx_r2.btnActionStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", action_r1.buttons);\n  }\n}\nfunction BtnDropdownActionComponent_ng_container_1_div_2_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 14);\n  }\n  if (rf & 2) {\n    const action_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", action_r1.icon);\n  }\n}\nfunction BtnDropdownActionComponent_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function BtnDropdownActionComponent_ng_container_1_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const action_r1 = i0.ɵɵnextContext().$implicit;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(action_r1.onClick(ctx_r15.data));\n    });\n    i0.ɵɵtemplate(2, BtnDropdownActionComponent_ng_container_1_div_2_i_2_Template, 1, 1, \"i\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(ctx_r3.btnActionStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", action_r1.icon);\n  }\n}\nfunction BtnDropdownActionComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, BtnDropdownActionComponent_ng_container_1_div_1_Template, 5, 3, \"div\", 2);\n    i0.ɵɵtemplate(2, BtnDropdownActionComponent_ng_container_1_div_2_Template, 3, 3, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const action_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", action_r1.type == \"collection\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", action_r1.type != \"collection\");\n  }\n}\nexport class BtnDropdownActionComponent {\n  constructor() {\n    this.data = {};\n    this.actions = [];\n    this.emitter = new Subject();\n  }\n  ngOnInit() {}\n  onClick(action) {\n    // console.log(action);\n    // console.log(this.data);\n    this.emitter.next({\n      action,\n      data: this.data\n    });\n  }\n  ngOnDestroy() {\n    this.emitter.unsubscribe();\n  }\n  static #_ = this.ɵfac = function BtnDropdownActionComponent_Factory(t) {\n    return new (t || BtnDropdownActionComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BtnDropdownActionComponent,\n    selectors: [[\"app-btn-dropdown-action\"]],\n    inputs: {\n      btnStyle: \"btnStyle\",\n      btnActionStyle: \"btnActionStyle\",\n      class: \"class\",\n      data: \"data\",\n      actions: \"actions\"\n    },\n    outputs: {\n      emitter: \"emitter\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-auto p-1 m-0\", \"ngbDropdown\", \"\", \"container\", \"body\", 4, \"ngIf\"], [\"class\", \"col-auto p-1 m-0\", 4, \"ngIf\"], [\"ngbDropdown\", \"\", \"container\", \"body\", 1, \"col-auto\", \"p-1\", \"m-0\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", \"data-toggle\", \"dropdown\", 1, \"btn\", \"hide-arrow\", \"p-0\", \"text-primary\", 3, \"click\"], [1, \"fa-regular\", \"fa-ellipsis-vertical\"], [\"ngbDropdownMenu\", \"\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"class\", \"mr-50\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"mr-50\", 3, \"ngClass\"], [1, \"col-auto\", \"p-1\", \"m-0\"], [\"type\", \"button\", 1, \"btn\", \"hide-arrow\", \"p-0\", 3, \"click\"], [\"class\", \"mr-50 text-primary\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"mr-50\", \"text-primary\", 3, \"ngClass\"]],\n    template: function BtnDropdownActionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, BtnDropdownActionComponent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.class);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.actions);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.NgbDropdown, i2.NgbDropdownToggle, i2.NgbDropdownMenu, i2.NgbDropdownItem, i3.DefaultClassDirective, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "mappings": "AAQA,SAASA,OAAO,QAAQ,MAAM;;;;;;;;ICENC,EAAA,CAAAC,SAAA,YAAiE;;;;IAA1CD,EAAA,CAAAE,UAAA,YAAAC,SAAA,CAAAC,IAAA,CAAuB;;;;;;IAFtDJ,EAAA,CAAAK,uBAAA,GAAoD;IAChDL,EAAA,CAAAM,cAAA,aAAuE;IAAlDN,EAAA,CAAAO,UAAA,mBAAAC,6FAAA;MAAA,MAAAC,WAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAR,SAAA,GAAAM,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAZ,SAAA,CAAAa,OAAA,CAAAH,MAAA,CAAAI,IAAA,CAAoB;IAAA,EAAC;IAC/CjB,EAAA,CAAAkB,UAAA,IAAAC,2EAAA,eAAiE;IACjEnB,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAoB,MAAA,GAA6B;;IAAApB,EAAA,CAAAqB,YAAA,EAAO;IAElDrB,EAAA,CAAAsB,qBAAA,EAAe;;;;;IAJyCtB,EAAA,CAAAuB,SAAA,GAAkB;IAAlBvB,EAAA,CAAAwB,UAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;IAC9D1B,EAAA,CAAAuB,SAAA,GAAiB;IAAjBvB,EAAA,CAAAE,UAAA,SAAAC,SAAA,CAAAC,IAAA,CAAiB;IACfJ,EAAA,CAAAuB,SAAA,GAA6B;IAA7BvB,EAAA,CAAA2B,iBAAA,CAAA3B,EAAA,CAAA4B,WAAA,OAAAzB,SAAA,CAAA0B,KAAA,EAA6B;;;;;;IATnD7B,EAAA,CAAAM,cAAA,aAA6F;IAErFN,EAAA,CAAAO,UAAA,mBAAAuB,iFAAAC,MAAA;MAAA/B,EAAA,CAAAU,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAkB,OAAA,CAAAjB,OAAA,CAAAe,MAAA,CAAe;IAAA,EAAC;IACzB/B,EAAA,CAAAC,SAAA,WAA+C;IACnDD,EAAA,CAAAqB,YAAA,EAAS;IACTrB,EAAA,CAAAM,cAAA,aAAqB;IACjBN,EAAA,CAAAkB,UAAA,IAAAgB,uEAAA,0BAKe;IACnBlC,EAAA,CAAAqB,YAAA,EAAM;;;;;IAXwDrB,EAAA,CAAAuB,SAAA,GAAwB;IAAxBvB,EAAA,CAAAwB,UAAA,CAAAW,MAAA,CAAAC,cAAA,CAAwB;IAKjDpC,EAAA,CAAAuB,SAAA,GAAiB;IAAjBvB,EAAA,CAAAE,UAAA,YAAAmC,SAAA,CAAAC,OAAA,CAAiB;;;;;IAUlDtC,EAAA,CAAAC,SAAA,YAA+E;;;;IAAvDD,EAAA,CAAAE,UAAA,YAAAmC,SAAA,CAAAjC,IAAA,CAAuB;;;;;;IAFvDJ,EAAA,CAAAM,cAAA,cAAgE;IACcN,EAAA,CAAAO,UAAA,mBAAAgC,iFAAA;MAAAvC,EAAA,CAAAU,aAAA,CAAA8B,IAAA;MAAA,MAAAH,SAAA,GAAArC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAA,MAAA6B,OAAA,GAAAzC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAsB,SAAA,CAAArB,OAAA,CAAAyB,OAAA,CAAAxB,IAAA,CAAoB;IAAA,EAAC;IACpGjB,EAAA,CAAAkB,UAAA,IAAAwB,4DAAA,gBAA+E;IAEnF1C,EAAA,CAAAqB,YAAA,EAAS;;;;;IAHwCrB,EAAA,CAAAuB,SAAA,GAAwB;IAAxBvB,EAAA,CAAAwB,UAAA,CAAAmB,MAAA,CAAAP,cAAA,CAAwB;IACjEpC,EAAA,CAAAuB,SAAA,GAAiB;IAAjBvB,EAAA,CAAAE,UAAA,SAAAmC,SAAA,CAAAjC,IAAA,CAAiB;;;;;IAjBjCJ,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAkB,UAAA,IAAA0B,wDAAA,iBAaM;IACN5C,EAAA,CAAAkB,UAAA,IAAA2B,wDAAA,iBAKM;IACV7C,EAAA,CAAAsB,qBAAA,EAAe;;;;IApBiDtB,EAAA,CAAAuB,SAAA,GAA+B;IAA/BvB,EAAA,CAAAE,UAAA,SAAAmC,SAAA,CAAAS,IAAA,iBAA+B;IAc5D9C,EAAA,CAAAuB,SAAA,GAA+B;IAA/BvB,EAAA,CAAAE,UAAA,SAAAmC,SAAA,CAAAS,IAAA,iBAA+B;;;ADAtE,OAAM,MAAOC,0BAA0B;EAarCC,YAAA;IALA,KAAA/B,IAAI,GAAG,EAAE;IAET,KAAAgC,OAAO,GAAmB,EAAE;IAE5B,KAAAC,OAAO,GAAG,IAAInD,OAAO,EAAO;EACb;EACfoD,QAAQA,CAAA,GAAI;EACZnC,OAAOA,CAACoC,MAAW;IACjB;IACA;IACA,IAAI,CAACF,OAAO,CAACG,IAAI,CAAC;MAChBD,MAAM;MACNnC,IAAI,EAAE,IAAI,CAACA;KACZ,CAAC;EACJ;EACAqC,WAAWA,CAAA;IACT,IAAI,CAACJ,OAAO,CAACK,WAAW,EAAE;EAC5B;EAAC,QAAAC,CAAA;qBAzBUT,0BAA0B;EAAA;EAAA,QAAAU,EAAA;UAA1BV,0BAA0B;IAAAW,SAAA;IAAAC,MAAA;MAAAjC,QAAA;MAAAU,cAAA;MAAAwB,KAAA;MAAA3C,IAAA;MAAAgC,OAAA;IAAA;IAAAY,OAAA;MAAAX,OAAA;IAAA;IAAAY,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBvCnE,EAAA,CAAAM,cAAA,aAAwB;QACpBN,EAAA,CAAAkB,UAAA,IAAAmD,kDAAA,0BAqBe;QACnBrE,EAAA,CAAAqB,YAAA,EAAM;;;QAvBArB,EAAA,CAAAE,UAAA,YAAAkE,GAAA,CAAAR,KAAA,CAAiB;QACc5D,EAAA,CAAAuB,SAAA,GAAU;QAAVvB,EAAA,CAAAE,UAAA,YAAAkE,GAAA,CAAAnB,OAAA,CAAU", "names": ["Subject", "i0", "ɵɵelement", "ɵɵproperty", "button_r5", "icon", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_Template_div_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onClick", "data", "ɵɵtemplate", "BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_i_2_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵstyleMap", "ctx_r4", "btnStyle", "ɵɵtextInterpolate", "ɵɵpipeBind1", "label", "BtnDropdownActionComponent_ng_container_1_div_1_Template_button_click_1_listener", "$event", "_r11", "ctx_r10", "BtnDropdownActionComponent_ng_container_1_div_1_ng_container_4_Template", "ctx_r2", "btnActionStyle", "action_r1", "buttons", "BtnDropdownActionComponent_ng_container_1_div_2_Template_button_click_1_listener", "_r16", "ctx_r15", "BtnDropdownActionComponent_ng_container_1_div_2_i_2_Template", "ctx_r3", "BtnDropdownActionComponent_ng_container_1_div_1_Template", "BtnDropdownActionComponent_ng_container_1_div_2_Template", "type", "BtnDropdownActionComponent", "constructor", "actions", "emitter", "ngOnInit", "action", "next", "ngOnDestroy", "unsubscribe", "_", "_2", "selectors", "inputs", "class", "outputs", "decls", "vars", "consts", "template", "BtnDropdownActionComponent_Template", "rf", "ctx", "BtnDropdownActionComponent_ng_container_1_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\btn-dropdown-action\\btn-dropdown-action.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\btn-dropdown-action\\btn-dropdown-action.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectionStrategy,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n} from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-btn-dropdown-action',\r\n  templateUrl: './btn-dropdown-action.component.html',\r\n  styleUrls: ['./btn-dropdown-action.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class BtnDropdownActionComponent implements OnInit {\r\n  @Input()\r\n  btnStyle: string;\r\n  @Input()\r\n  btnActionStyle: string;\r\n  @Input()\r\n  class: string; \r\n  @Input()\r\n  data = {};\r\n  @Input()\r\n  actions: EZBtnActions[] = [];\r\n  @Output()\r\n  emitter = new Subject<any>();\r\n  constructor() {}\r\n  ngOnInit() {}\r\n  onClick(action: any) {\r\n    // console.log(action);\r\n    // console.log(this.data);\r\n    this.emitter.next({\r\n      action,\r\n      data: this.data,\r\n    });\r\n  }\r\n  ngOnDestroy() {\r\n    this.emitter.unsubscribe();\r\n  }\r\n}\r\nexport interface EZActionButton {\r\n  label?: string;\r\n  onClick?: Function;\r\n  icon?: string;\r\n}\r\nexport interface EZBtnActions {\r\n  type?: string;\r\n  label?: string;\r\n  onClick?: Function;\r\n  icon?: string;\r\n  buttons?: EZActionButton[];\r\n}\r\n", "<div  [ngClass]=\"class\">\r\n    <ng-container *ngFor=\"let action of actions\">\r\n        <div class=\"col-auto p-1 m-0\" ngbDropdown container=\"body\" *ngIf=\"action.type=='collection'\">\r\n            <button type=\"button\" class=\"btn hide-arrow p-0 text-primary\" [style]=\"btnActionStyle\"  ngbDropdownToggle data-toggle=\"dropdown\"\r\n                (click)=\"onClick($event)\">\r\n                <i class=\"fa-regular fa-ellipsis-vertical\"></i>\r\n            </button>\r\n            <div ngbDropdownMenu>\r\n                <ng-container *ngFor=\"let button of action.buttons\">\r\n                    <div ngbDropdownItem (click)=\"button.onClick(data)\" [style]=\"btnStyle\">\r\n                        <i *ngIf=\"button.icon\" [ngClass]=\"button.icon\" class=\"mr-50\"></i>\r\n                        <span>{{button.label | translate }}</span>\r\n                    </div>\r\n                </ng-container>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-auto p-1 m-0\" *ngIf=\"action.type!='collection'\">\r\n            <button type=\"button\" class=\"btn hide-arrow p-0\" [style]=\"btnActionStyle\" (click)=\"action.onClick(data)\">\r\n                <i *ngIf=\"action.icon\"  [ngClass]=\"action.icon\" class=\"mr-50 text-primary\"></i>\r\n                <!-- <span>{{action.label | translate }}</span> -->\r\n            </button>\r\n        </div>\r\n    </ng-container>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
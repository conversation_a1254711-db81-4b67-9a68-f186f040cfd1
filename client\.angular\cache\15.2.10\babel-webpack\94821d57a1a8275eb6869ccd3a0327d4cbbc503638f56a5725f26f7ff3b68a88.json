{"ast": null, "code": "// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Much of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// Many optimizations have been made, so the bundle size is ultimately smaller but performance is similar.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = function (c, id, msg, transfer, cb) {\n  var u = ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c], {\n    type: 'text/javascript'\n  })));\n  var w = new Worker(u);\n  w.onerror = function (e) {\n    return cb(e.error, null);\n  };\n  w.onmessage = function (e) {\n    return cb(null, e.data);\n  };\n  w.postMessage(msg, transfer);\n  return w;\n};\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array,\n  u16 = Uint16Array,\n  u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */0, 0, /* impossible */0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n  var b = new u16(31);\n  for (var i = 0; i < 31; ++i) {\n    b[i] = start += 1 << eb[i - 1];\n  }\n  // numbers here are at max 18 bits\n  var r = new u32(b[30]);\n  for (var i = 1; i < 30; ++i) {\n    for (var j = b[i]; j < b[i + 1]; ++j) {\n      r[j] = j - b[i] << 5 | i;\n    }\n  }\n  return [b, r];\n};\nvar _a = freb(fleb, 2),\n  fl = _a[0],\n  revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0),\n  fd = _b[0],\n  revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n  // reverse table algorithm from SO\n  var x = (i & 0xAAAA) >>> 1 | (i & 0x5555) << 1;\n  x = (x & 0xCCCC) >>> 2 | (x & 0x3333) << 2;\n  x = (x & 0xF0F0) >>> 4 | (x & 0x0F0F) << 4;\n  rev[i] = ((x & 0xFF00) >>> 8 | (x & 0x00FF) << 8) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = function (cd, mb, r) {\n  var s = cd.length;\n  // index\n  var i = 0;\n  // u16 \"map\": index -> # of codes with bit length = index\n  var l = new u16(mb);\n  // length of cd must be 288 (total # of codes)\n  for (; i < s; ++i) ++l[cd[i] - 1];\n  // u16 \"map\": index -> minimum code for bit length = index\n  var le = new u16(mb);\n  for (i = 0; i < mb; ++i) {\n    le[i] = le[i - 1] + l[i - 1] << 1;\n  }\n  var co;\n  if (r) {\n    // u16 \"map\": index -> number of actual bits, symbol for code\n    co = new u16(1 << mb);\n    // bits to remove for reverser\n    var rvb = 15 - mb;\n    for (i = 0; i < s; ++i) {\n      // ignore 0 lengths\n      if (cd[i]) {\n        // num encoding both symbol and bits read\n        var sv = i << 4 | cd[i];\n        // free bits\n        var r_1 = mb - cd[i];\n        // start value\n        var v = le[cd[i] - 1]++ << r_1;\n        // m is end value\n        for (var m = v | (1 << r_1) - 1; v <= m; ++v) {\n          // every 16 bit value starting with the code yields the same result\n          co[rev[v] >>> rvb] = sv;\n        }\n      }\n    }\n  } else {\n    co = new u16(s);\n    for (i = 0; i < s; ++i) co[i] = rev[le[cd[i] - 1]++] >>> 15 - cd[i];\n  }\n  return co;\n};\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i) flt[i] = 8;\nfor (var i = 144; i < 256; ++i) flt[i] = 9;\nfor (var i = 256; i < 280; ++i) flt[i] = 7;\nfor (var i = 280; i < 288; ++i) flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i) fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/hMap(flt, 9, 0),\n  flrm = /*#__PURE__*/hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/hMap(fdt, 5, 0),\n  fdrm = /*#__PURE__*/hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n  var m = a[0];\n  for (var i = 1; i < a.length; ++i) {\n    if (a[i] > m) m = a[i];\n  }\n  return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n  var o = p / 8 >> 0;\n  return (d[o] | d[o + 1] << 8) >>> (p & 7) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n  var o = p / 8 >> 0;\n  return (d[o] | d[o + 1] << 8 | d[o + 2] << 16) >>> (p & 7);\n};\n// get end of byte\nvar shft = function (p) {\n  return (p / 8 >> 0) + (p & 7 && 1);\n};\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n  if (s == null || s < 0) s = 0;\n  if (e == null || e > v.length) e = v.length;\n  // can't use .constructor in case user-supplied\n  var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n  n.set(v.subarray(s, e));\n  return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n  // source length\n  var sl = dat.length;\n  // have to estimate size\n  var noBuf = !buf || st;\n  // no state\n  var noSt = !st || st.i;\n  if (!st) st = {};\n  // Assumes roughly 33% compression ratio average\n  if (!buf) buf = new u8(sl * 3);\n  // ensure buffer can fit at least l elements\n  var cbuf = function (l) {\n    var bl = buf.length;\n    // need to increase size to fit\n    if (l > bl) {\n      // Double or set to necessary, whichever is greater\n      var nbuf = new u8(Math.max(bl * 2, l));\n      nbuf.set(buf);\n      buf = nbuf;\n    }\n  };\n  //  last chunk         bitpos           bytes\n  var final = st.f || 0,\n    pos = st.p || 0,\n    bt = st.b || 0,\n    lm = st.l,\n    dm = st.d,\n    lbt = st.m,\n    dbt = st.n;\n  // total bits\n  var tbts = sl * 8;\n  do {\n    if (!lm) {\n      // BFINAL - this is only 1 when last chunk is next\n      st.f = final = bits(dat, pos, 1);\n      // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n      var type = bits(dat, pos + 1, 3);\n      pos += 3;\n      if (!type) {\n        // go to end of byte boundary\n        var s = shft(pos) + 4,\n          l = dat[s - 4] | dat[s - 3] << 8,\n          t = s + l;\n        if (t > sl) {\n          if (noSt) throw 'unexpected EOF';\n          break;\n        }\n        // ensure size\n        if (noBuf) cbuf(bt + l);\n        // Copy over uncompressed data\n        buf.set(dat.subarray(s, t), bt);\n        // Get new bitpos, update byte count\n        st.b = bt += l, st.p = pos = t * 8;\n        continue;\n      } else if (type == 1) lm = flrm, dm = fdrm, lbt = 9, dbt = 5;else if (type == 2) {\n        //  literal                            lengths\n        var hLit = bits(dat, pos, 31) + 257,\n          hcLen = bits(dat, pos + 10, 15) + 4;\n        var tl = hLit + bits(dat, pos + 5, 31) + 1;\n        pos += 14;\n        // length+distance tree\n        var ldt = new u8(tl);\n        // code length tree\n        var clt = new u8(19);\n        for (var i = 0; i < hcLen; ++i) {\n          // use index map to get real code\n          clt[clim[i]] = bits(dat, pos + i * 3, 7);\n        }\n        pos += hcLen * 3;\n        // code lengths bits\n        var clb = max(clt),\n          clbmsk = (1 << clb) - 1;\n        if (!noSt && pos + tl * (clb + 7) > tbts) break;\n        // code lengths map\n        var clm = hMap(clt, clb, 1);\n        for (var i = 0; i < tl;) {\n          var r = clm[bits(dat, pos, clbmsk)];\n          // bits read\n          pos += r & 15;\n          // symbol\n          var s = r >>> 4;\n          // code length to copy\n          if (s < 16) {\n            ldt[i++] = s;\n          } else {\n            //  copy   count\n            var c = 0,\n              n = 0;\n            if (s == 16) n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];else if (s == 17) n = 3 + bits(dat, pos, 7), pos += 3;else if (s == 18) n = 11 + bits(dat, pos, 127), pos += 7;\n            while (n--) ldt[i++] = c;\n          }\n        }\n        //    length tree                 distance tree\n        var lt = ldt.subarray(0, hLit),\n          dt = ldt.subarray(hLit);\n        // max length bits\n        lbt = max(lt);\n        // max dist bits\n        dbt = max(dt);\n        lm = hMap(lt, lbt, 1);\n        dm = hMap(dt, dbt, 1);\n      } else throw 'invalid block type';\n      if (pos > tbts) throw 'unexpected EOF';\n    }\n    // Make sure the buffer can hold this + the largest possible addition\n    // Maximum chunk size (practically, theoretically infinite) is 2^17;\n    if (noBuf) cbuf(bt + 131072);\n    var lms = (1 << lbt) - 1,\n      dms = (1 << dbt) - 1;\n    var mxa = lbt + dbt + 18;\n    while (noSt || pos + mxa < tbts) {\n      // bits read, code\n      var c = lm[bits16(dat, pos) & lms],\n        sym = c >>> 4;\n      pos += c & 15;\n      if (pos > tbts) throw 'unexpected EOF';\n      if (!c) throw 'invalid length/literal';\n      if (sym < 256) buf[bt++] = sym;else if (sym == 256) {\n        lm = null;\n        break;\n      } else {\n        var add = sym - 254;\n        // no extra bits needed if less\n        if (sym > 264) {\n          // index\n          var i = sym - 257,\n            b = fleb[i];\n          add = bits(dat, pos, (1 << b) - 1) + fl[i];\n          pos += b;\n        }\n        // dist\n        var d = dm[bits16(dat, pos) & dms],\n          dsym = d >>> 4;\n        if (!d) throw 'invalid distance';\n        pos += d & 15;\n        var dt = fd[dsym];\n        if (dsym > 3) {\n          var b = fdeb[dsym];\n          dt += bits16(dat, pos) & (1 << b) - 1, pos += b;\n        }\n        if (pos > tbts) throw 'unexpected EOF';\n        if (noBuf) cbuf(bt + 131072);\n        var end = bt + add;\n        for (; bt < end; bt += 4) {\n          buf[bt] = buf[bt - dt];\n          buf[bt + 1] = buf[bt + 1 - dt];\n          buf[bt + 2] = buf[bt + 2 - dt];\n          buf[bt + 3] = buf[bt + 3 - dt];\n        }\n        bt = end;\n      }\n    }\n    st.l = lm, st.p = pos, st.b = bt;\n    if (lm) final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n  } while (!final);\n  return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n  v <<= p & 7;\n  var o = p / 8 >> 0;\n  d[o] |= v;\n  d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n  v <<= p & 7;\n  var o = p / 8 >> 0;\n  d[o] |= v;\n  d[o + 1] |= v >>> 8;\n  d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n  // Need extra info to make a tree\n  var t = [];\n  for (var i = 0; i < d.length; ++i) {\n    if (d[i]) t.push({\n      s: i,\n      f: d[i]\n    });\n  }\n  var s = t.length;\n  var t2 = t.slice();\n  if (!s) return [new u8(0), 0];\n  if (s == 1) {\n    var v = new u8(t[0].s + 1);\n    v[t[0].s] = 1;\n    return [v, 1];\n  }\n  t.sort(function (a, b) {\n    return a.f - b.f;\n  });\n  // after i2 reaches last ind, will be stopped\n  // freq must be greater than largest possible number of symbols\n  t.push({\n    s: -1,\n    f: 25001\n  });\n  var l = t[0],\n    r = t[1],\n    i0 = 0,\n    i1 = 1,\n    i2 = 2;\n  t[0] = {\n    s: -1,\n    f: l.f + r.f,\n    l: l,\n    r: r\n  };\n  // efficient algorithm from UZIP.js\n  // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n  // symbols that combined have high freq, will start processing i2 (high-freq,\n  // non-composite) symbols instead\n  // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n  while (i1 != s - 1) {\n    l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n    r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n    t[i1++] = {\n      s: -1,\n      f: l.f + r.f,\n      l: l,\n      r: r\n    };\n  }\n  var maxSym = t2[0].s;\n  for (var i = 1; i < s; ++i) {\n    if (t2[i].s > maxSym) maxSym = t2[i].s;\n  }\n  // code lengths\n  var tr = new u16(maxSym + 1);\n  // max bits in tree\n  var mbt = ln(t[i1 - 1], tr, 0);\n  if (mbt > mb) {\n    // more algorithms from UZIP.js\n    // TODO: find out how this code works (debt)\n    //  ind    debt\n    var i = 0,\n      dt = 0;\n    //    left            cost\n    var lft = mbt - mb,\n      cst = 1 << lft;\n    t2.sort(function (a, b) {\n      return tr[b.s] - tr[a.s] || a.f - b.f;\n    });\n    for (; i < s; ++i) {\n      var i2_1 = t2[i].s;\n      if (tr[i2_1] > mb) {\n        dt += cst - (1 << mbt - tr[i2_1]);\n        tr[i2_1] = mb;\n      } else break;\n    }\n    dt >>>= lft;\n    while (dt > 0) {\n      var i2_2 = t2[i].s;\n      if (tr[i2_2] < mb) dt -= 1 << mb - tr[i2_2]++ - 1;else ++i;\n    }\n    for (; i >= 0 && dt; --i) {\n      var i2_3 = t2[i].s;\n      if (tr[i2_3] == mb) {\n        --tr[i2_3];\n        ++dt;\n      }\n    }\n    mbt = mb;\n  }\n  return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n  return n.s == -1 ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1)) : l[n.s] = d;\n};\n// length codes generation\nvar lc = function (c) {\n  var s = c.length;\n  // Note that the semicolon was intentional\n  while (s && !c[--s]);\n  var cl = new u16(++s);\n  //  ind      num         streak\n  var cli = 0,\n    cln = c[0],\n    cls = 1;\n  var w = function (v) {\n    cl[cli++] = v;\n  };\n  for (var i = 1; i <= s; ++i) {\n    if (c[i] == cln && i != s) ++cls;else {\n      if (!cln && cls > 2) {\n        for (; cls > 138; cls -= 138) w(32754);\n        if (cls > 2) {\n          w(cls > 10 ? cls - 11 << 5 | 28690 : cls - 3 << 5 | 12305);\n          cls = 0;\n        }\n      } else if (cls > 3) {\n        w(cln), --cls;\n        for (; cls > 6; cls -= 6) w(8304);\n        if (cls > 2) w(cls - 3 << 5 | 8208), cls = 0;\n      }\n      while (cls--) w(cln);\n      cls = 1;\n      cln = c[i];\n    }\n  }\n  return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n  var l = 0;\n  for (var i = 0; i < cl.length; ++i) l += cf[i] * cl[i];\n  return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n  // no need to write 00 as type: TypedArray defaults to 0\n  var s = dat.length;\n  var o = shft(pos + 2);\n  out[o] = s & 255;\n  out[o + 1] = s >>> 8;\n  out[o + 2] = out[o] ^ 255;\n  out[o + 3] = out[o + 1] ^ 255;\n  for (var i = 0; i < s; ++i) out[o + i + 4] = dat[i];\n  return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n  wbits(out, p++, final);\n  ++lf[256];\n  var _a = hTree(lf, 15),\n    dlt = _a[0],\n    mlb = _a[1];\n  var _b = hTree(df, 15),\n    ddt = _b[0],\n    mdb = _b[1];\n  var _c = lc(dlt),\n    lclt = _c[0],\n    nlc = _c[1];\n  var _d = lc(ddt),\n    lcdt = _d[0],\n    ndc = _d[1];\n  var lcfreq = new u16(19);\n  for (var i = 0; i < lclt.length; ++i) lcfreq[lclt[i] & 31]++;\n  for (var i = 0; i < lcdt.length; ++i) lcfreq[lcdt[i] & 31]++;\n  var _e = hTree(lcfreq, 7),\n    lct = _e[0],\n    mlcb = _e[1];\n  var nlcc = 19;\n  for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc);\n  var flen = bl + 5 << 3;\n  var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n  var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n  if (flen <= ftlen && flen <= dtlen) return wfblk(out, p, dat.subarray(bs, bs + bl));\n  var lm, ll, dm, dl;\n  wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n  if (dtlen < ftlen) {\n    lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n    var llm = hMap(lct, mlcb, 0);\n    wbits(out, p, nlc - 257);\n    wbits(out, p + 5, ndc - 1);\n    wbits(out, p + 10, nlcc - 4);\n    p += 14;\n    for (var i = 0; i < nlcc; ++i) wbits(out, p + 3 * i, lct[clim[i]]);\n    p += 3 * nlcc;\n    var lcts = [lclt, lcdt];\n    for (var it = 0; it < 2; ++it) {\n      var clct = lcts[it];\n      for (var i = 0; i < clct.length; ++i) {\n        var len = clct[i] & 31;\n        wbits(out, p, llm[len]), p += lct[len];\n        if (len > 15) wbits(out, p, clct[i] >>> 5 & 127), p += clct[i] >>> 12;\n      }\n    }\n  } else {\n    lm = flm, ll = flt, dm = fdm, dl = fdt;\n  }\n  for (var i = 0; i < li; ++i) {\n    if (syms[i] > 255) {\n      var len = syms[i] >>> 18 & 31;\n      wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n      if (len > 7) wbits(out, p, syms[i] >>> 23 & 31), p += fleb[len];\n      var dst = syms[i] & 31;\n      wbits16(out, p, dm[dst]), p += dl[dst];\n      if (dst > 3) wbits16(out, p, syms[i] >>> 5 & 8191), p += fdeb[dst];\n    } else {\n      wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n    }\n  }\n  wbits16(out, p, lm[256]);\n  return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n  var s = dat.length;\n  var o = new u8(pre + s + 5 * (1 + Math.floor(s / 7000)) + post);\n  // writing to this writes to the output buffer\n  var w = o.subarray(pre, o.length - post);\n  var pos = 0;\n  if (!lvl || s < 8) {\n    for (var i = 0; i <= s; i += 65535) {\n      // end\n      var e = i + 65535;\n      if (e < s) {\n        // write full block\n        pos = wfblk(w, pos, dat.subarray(i, e));\n      } else {\n        // write final block\n        w[i] = lst;\n        pos = wfblk(w, pos, dat.subarray(i, s));\n      }\n    }\n  } else {\n    var opt = deo[lvl - 1];\n    var n = opt >>> 13,\n      c = opt & 8191;\n    var msk_1 = (1 << plvl) - 1;\n    //    prev 2-byte val map    curr 2-byte val map\n    var prev = new u16(32768),\n      head = new u16(msk_1 + 1);\n    var bs1_1 = Math.ceil(plvl / 3),\n      bs2_1 = 2 * bs1_1;\n    var hsh = function (i) {\n      return (dat[i] ^ dat[i + 1] << bs1_1 ^ dat[i + 2] << bs2_1) & msk_1;\n    };\n    // 24576 is an arbitrary number of maximum symbols per block\n    // 424 buffer for last block\n    var syms = new u32(25000);\n    // length/literal freq   distance freq\n    var lf = new u16(288),\n      df = new u16(32);\n    //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n    var lc_1 = 0,\n      eb = 0,\n      i = 0,\n      li = 0,\n      wi = 0,\n      bs = 0;\n    for (; i < s; ++i) {\n      // hash value\n      var hv = hsh(i);\n      // index mod 32768\n      var imod = i & 32767;\n      // previous index with this value\n      var pimod = head[hv];\n      prev[imod] = pimod;\n      head[hv] = imod;\n      // We always should modify head and prev, but only add symbols if\n      // this data is not yet processed (\"wait\" for wait index)\n      if (wi <= i) {\n        // bytes remaining\n        var rem = s - i;\n        if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n          pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n          li = lc_1 = eb = 0, bs = i;\n          for (var j = 0; j < 286; ++j) lf[j] = 0;\n          for (var j = 0; j < 30; ++j) df[j] = 0;\n        }\n        //  len    dist   chain\n        var l = 2,\n          d = 0,\n          ch_1 = c,\n          dif = imod - pimod & 32767;\n        if (rem > 2 && hv == hsh(i - dif)) {\n          var maxn = Math.min(n, rem) - 1;\n          var maxd = Math.min(32767, i);\n          // max possible length\n          // not capped at dif because decompressors implement \"rolling\" index population\n          var ml = Math.min(258, rem);\n          while (dif <= maxd && --ch_1 && imod != pimod) {\n            if (dat[i + l] == dat[i + l - dif]) {\n              var nl = 0;\n              for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl);\n              if (nl > l) {\n                l = nl, d = dif;\n                // break out early when we reach \"nice\" (we are satisfied enough)\n                if (nl > maxn) break;\n                // now, find the rarest 2-byte sequence within this\n                // length of literals and search for that instead.\n                // Much faster than just using the start\n                var mmd = Math.min(dif, nl - 2);\n                var md = 0;\n                for (var j = 0; j < mmd; ++j) {\n                  var ti = i - dif + j + 32768 & 32767;\n                  var pti = prev[ti];\n                  var cd = ti - pti + 32768 & 32767;\n                  if (cd > md) md = cd, pimod = ti;\n                }\n              }\n            }\n            // check the previous match\n            imod = pimod, pimod = prev[imod];\n            dif += imod - pimod + 32768 & 32767;\n          }\n        }\n        // d will be nonzero only when a match was found\n        if (d) {\n          // store both dist and len data in one Uint32\n          // Make sure this is recognized as a len/dist with 28th bit (2^28)\n          syms[li++] = 268435456 | revfl[l] << 18 | revfd[d];\n          var lin = revfl[l] & 31,\n            din = revfd[d] & 31;\n          eb += fleb[lin] + fdeb[din];\n          ++lf[257 + lin];\n          ++df[din];\n          wi = i + l;\n          ++lc_1;\n        } else {\n          syms[li++] = dat[i];\n          ++lf[dat[i]];\n        }\n      }\n    }\n    pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n    // this is the easiest way to avoid needing to maintain state\n    if (!lst) pos = wfblk(w, pos, et);\n  }\n  return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/function () {\n  var t = new u32(256);\n  for (var i = 0; i < 256; ++i) {\n    var c = i,\n      k = 9;\n    while (--k) c = (c & 1 && 0xEDB88320) ^ c >>> 1;\n    t[i] = c;\n  }\n  return t;\n}();\n// CRC32\nvar crc = function () {\n  var c = 0xFFFFFFFF;\n  return {\n    p: function (d) {\n      // closures have awful performance\n      var cr = c;\n      for (var i = 0; i < d.length; ++i) cr = crct[cr & 255 ^ d[i]] ^ cr >>> 8;\n      c = cr;\n    },\n    d: function () {\n      return c ^ 0xFFFFFFFF;\n    }\n  };\n};\n// Alder32\nvar adler = function () {\n  var a = 1,\n    b = 0;\n  return {\n    p: function (d) {\n      // closures have awful performance\n      var n = a,\n        m = b;\n      var l = d.length;\n      for (var i = 0; i != l;) {\n        var e = Math.min(i + 5552, l);\n        for (; i < e; ++i) n += d[i], m += n;\n        n %= 65521, m %= 65521;\n      }\n      a = n, b = m;\n    },\n    d: function () {\n      return (a >>> 8 << 16 | (b & 255) << 8 | b >>> 8) + ((a & 255) << 23) * 2;\n    }\n  };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n  return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : 12 + opt.mem, pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n  var o = {};\n  for (var k in a) o[k] = a[k];\n  for (var k in b) o[k] = b[k];\n  return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n  var dt = fn();\n  var st = fn.toString();\n  var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n  for (var i = 0; i < dt.length; ++i) {\n    var v = dt[i],\n      k = ks[i];\n    if (typeof v == 'function') {\n      fnStr += ';' + k + '=';\n      var st_1 = v.toString();\n      if (v.prototype) {\n        // for global objects\n        if (st_1.indexOf('[native code]') != -1) {\n          var spInd = st_1.indexOf(' ', 8) + 1;\n          fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n        } else {\n          fnStr += st_1;\n          for (var t in v.prototype) fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n        }\n      } else fnStr += st_1;\n    } else td[k] = v;\n  }\n  return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n  var tl = [];\n  for (var k in v) {\n    if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32) tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n  }\n  return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n  var _a;\n  if (!ch[id]) {\n    var fnStr = '',\n      td_1 = {},\n      m = fns.length - 1;\n    for (var i = 0; i < m; ++i) _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n    ch[id] = wcln(fns[m], fnStr, td_1);\n  }\n  var td = mrg({}, ch[id][1]);\n  return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () {\n  return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8];\n};\nvar bDflt = function () {\n  return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf];\n};\n// gzip extra\nvar gze = function () {\n  return [gzh, gzhl, wbytes, crc, crct];\n};\n// gunzip extra\nvar guze = function () {\n  return [gzs, gzl];\n};\n// zlib extra\nvar zle = function () {\n  return [zlh, wbytes, adler];\n};\n// unzlib extra\nvar zule = function () {\n  return [zlv];\n};\n// post buf\nvar pbf = function (msg) {\n  return postMessage(msg, [msg.buffer]);\n};\n// get u8\nvar gu8 = function (o) {\n  return o && o.size && new u8(o.size);\n};\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n  var w = wrkr(fns, init, id, function (err, dat) {\n    w.terminate();\n    cb(err, dat);\n  });\n  if (!opts.consume) dat = new u8(dat);\n  w.postMessage([dat, opts], [dat.buffer]);\n  return function () {\n    w.terminate();\n  };\n};\n// auto stream\nvar astrm = function (strm) {\n  strm.ondata = function (dat, final) {\n    return postMessage([dat, final], [dat.buffer]);\n  };\n  return function (ev) {\n    return strm.push(ev.data[0], ev.data[1]);\n  };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n  var t;\n  var w = wrkr(fns, init, id, function (err, dat) {\n    if (err) w.terminate(), strm.ondata.call(strm, err);else {\n      if (dat[1]) w.terminate();\n      strm.ondata.call(strm, err, dat[0], dat[1]);\n    }\n  });\n  w.postMessage(opts);\n  strm.push = function (d, f) {\n    if (t) throw 'stream finished';\n    if (!strm.ondata) throw 'no stream handler';\n    w.postMessage([d, t = f], [d.buffer]);\n  };\n  strm.terminate = function () {\n    w.terminate();\n  };\n};\n// read 2 bytes\nvar b2 = function (d, b) {\n  return d[b] | d[b + 1] << 8;\n};\n// read 4 bytes\nvar b4 = function (d, b) {\n  return (d[b] | d[b + 1] << 8 | d[b + 2] << 16) + (d[b + 3] << 23) * 2;\n};\n// write bytes\nvar wbytes = function (d, b, v) {\n  for (; v; ++b) d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n  var fn = o.filename;\n  c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n  if (o.mtime != 0) wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n  if (fn) {\n    c[3] = 8;\n    for (var i = 0; i <= fn.length; ++i) c[i + 10] = fn.charCodeAt(i);\n  }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n  if (d[0] != 31 || d[1] != 139 || d[2] != 8) throw 'invalid gzip data';\n  var flg = d[3];\n  var st = 10;\n  if (flg & 4) st += d[10] | (d[11] << 8) + 2;\n  for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++]);\n  return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n  var l = d.length;\n  return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) + 2 * (d[l - 1] << 23);\n};\n// gzip header length\nvar gzhl = function (o) {\n  return 10 + (o.filename && o.filename.length + 1 || 0);\n};\n// zlib header\nvar zlh = function (c, o) {\n  var lv = o.level,\n    fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n  c[0] = 120, c[1] = fl << 6 | (fl ? 32 - 2 * fl : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n  if ((d[0] & 15) != 8 || d[0] >>> 4 > 7 || (d[0] << 8 | d[1]) % 31) throw 'invalid zlib data';\n  if (d[1] & 32) throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n  if (!cb && typeof opts == 'function') cb = opts, opts = {};\n  this.ondata = cb;\n  return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/function () {\n  function Deflate(opts, cb) {\n    if (!cb && typeof opts == 'function') cb = opts, opts = {};\n    this.ondata = cb;\n    this.o = opts || {};\n  }\n  Deflate.prototype.p = function (c, f) {\n    this.ondata(dopt(c, this.o, 0, 0, !f), f);\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Deflate.prototype.push = function (chunk, final) {\n    if (this.d) throw 'stream finished';\n    if (!this.ondata) throw 'no stream handler';\n    this.d = final;\n    this.p(chunk, final || false);\n  };\n  return Deflate;\n}();\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/function () {\n  function AsyncDeflate(opts, cb) {\n    astrmify([bDflt, function () {\n      return [astrm, Deflate];\n    }], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n      var strm = new Deflate(ev.data);\n      onmessage = astrm(strm);\n    }, 6);\n  }\n  return AsyncDeflate;\n}();\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bDflt], function (ev) {\n    return pbf(deflateSync(ev.data[0], ev.data[1]));\n  }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return dopt(data, opts, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/function () {\n  /**\n   * Creates an inflation stream\n   * @param cb The callback to call whenever data is inflated\n   */\n  function Inflate(cb) {\n    this.s = {};\n    this.p = new u8(0);\n    this.ondata = cb;\n  }\n  Inflate.prototype.e = function (c) {\n    if (this.d) throw 'stream finished';\n    if (!this.ondata) throw 'no stream handler';\n    var l = this.p.length;\n    var n = new u8(l + c.length);\n    n.set(this.p), n.set(c, l), this.p = n;\n  };\n  Inflate.prototype.c = function (final) {\n    this.d = this.s.i = final || false;\n    var bts = this.s.b;\n    var dt = inflt(this.p, this.o, this.s);\n    this.ondata(slc(dt, bts, this.s.b), this.d);\n    this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n    this.p = slc(this.p, this.s.p / 8 >> 0), this.s.p &= 7;\n  };\n  /**\n   * Pushes a chunk to be inflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the final chunk\n   */\n  Inflate.prototype.push = function (chunk, final) {\n    this.e(chunk), this.c(final);\n  };\n  return Inflate;\n}();\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous inflation stream\n   * @param cb The callback to call whenever data is deflated\n   */\n  function AsyncInflate(cb) {\n    this.ondata = cb;\n    astrmify([bInflt, function () {\n      return [astrm, Inflate];\n    }], this, 0, function () {\n      var strm = new Inflate();\n      onmessage = astrm(strm);\n    }, 7);\n  }\n  return AsyncInflate;\n}();\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bInflt], function (ev) {\n    return pbf(inflateSync(ev.data[0], gu8(ev.data[1])));\n  }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n  return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/function () {\n  function Gzip(opts, cb) {\n    this.c = crc();\n    this.l = 0;\n    this.v = 1;\n    Deflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be GZIPped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Gzip.prototype.push = function (chunk, final) {\n    Deflate.prototype.push.call(this, chunk, final);\n  };\n  Gzip.prototype.p = function (c, f) {\n    this.c.p(c);\n    this.l += c.length;\n    var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n    if (this.v) gzh(raw, this.o), this.v = 0;\n    if (f) wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n    this.ondata(raw, f);\n  };\n  return Gzip;\n}();\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/function () {\n  function AsyncGzip(opts, cb) {\n    astrmify([bDflt, gze, function () {\n      return [astrm, Deflate, Gzip];\n    }], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n      var strm = new Gzip(ev.data);\n      onmessage = astrm(strm);\n    }, 8);\n  }\n  return AsyncGzip;\n}();\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bDflt, gze, function () {\n    return [gzipSync];\n  }], function (ev) {\n    return pbf(gzipSync(ev.data[0], ev.data[1]));\n  }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  var c = crc(),\n    l = data.length;\n  c.p(data);\n  var d = dopt(data, opts, gzhl(opts), 8),\n    s = d.length;\n  return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/function () {\n  /**\n   * Creates a GUNZIP stream\n   * @param cb The callback to call whenever data is inflated\n   */\n  function Gunzip(cb) {\n    this.v = 1;\n    Inflate.call(this, cb);\n  }\n  /**\n   * Pushes a chunk to be GUNZIPped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Gunzip.prototype.push = function (chunk, final) {\n    Inflate.prototype.e.call(this, chunk);\n    if (this.v) {\n      var s = gzs(this.p);\n      if (s >= this.p.length && !final) return;\n      this.p = this.p.subarray(s), this.v = 0;\n    }\n    if (final) {\n      if (this.p.length < 8) throw 'invalid gzip stream';\n      this.p = this.p.subarray(0, -8);\n    }\n    // necessary to prevent TS from using the closure value\n    // This allows for workerization to function correctly\n    Inflate.prototype.c.call(this, final);\n  };\n  return Gunzip;\n}();\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous GUNZIP stream\n   * @param cb The callback to call whenever data is deflated\n   */\n  function AsyncGunzip(cb) {\n    this.ondata = cb;\n    astrmify([bInflt, guze, function () {\n      return [astrm, Inflate, Gunzip];\n    }], this, 0, function () {\n      var strm = new Gunzip();\n      onmessage = astrm(strm);\n    }, 9);\n  }\n  return AsyncGunzip;\n}();\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bInflt, guze, function () {\n    return [gunzipSync];\n  }], function (ev) {\n    return pbf(gunzipSync(ev.data[0]));\n  }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n  return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/function () {\n  function Zlib(opts, cb) {\n    this.c = adler();\n    this.v = 1;\n    Deflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be zlibbed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Zlib.prototype.push = function (chunk, final) {\n    Deflate.prototype.push.call(this, chunk, final);\n  };\n  Zlib.prototype.p = function (c, f) {\n    this.c.p(c);\n    var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n    if (this.v) zlh(raw, this.o), this.v = 0;\n    if (f) wbytes(raw, raw.length - 4, this.c.d());\n    this.ondata(raw, f);\n  };\n  return Zlib;\n}();\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/function () {\n  function AsyncZlib(opts, cb) {\n    astrmify([bDflt, zle, function () {\n      return [astrm, Deflate, Zlib];\n    }], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n      var strm = new Zlib(ev.data);\n      onmessage = astrm(strm);\n    }, 10);\n  }\n  return AsyncZlib;\n}();\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bDflt, zle, function () {\n    return [zlibSync];\n  }], function (ev) {\n    return pbf(zlibSync(ev.data[0], ev.data[1]));\n  }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  var a = adler();\n  a.p(data);\n  var d = dopt(data, opts, 2, 4);\n  return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/function () {\n  /**\n   * Creates a Zlib decompression stream\n   * @param cb The callback to call whenever data is inflated\n   */\n  function Unzlib(cb) {\n    this.v = 1;\n    Inflate.call(this, cb);\n  }\n  /**\n   * Pushes a chunk to be unzlibbed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Unzlib.prototype.push = function (chunk, final) {\n    Inflate.prototype.e.call(this, chunk);\n    if (this.v) {\n      if (this.p.length < 2 && !final) return;\n      this.p = this.p.subarray(2), this.v = 0;\n    }\n    if (final) {\n      if (this.p.length < 4) throw 'invalid zlib stream';\n      this.p = this.p.subarray(0, -4);\n    }\n    // necessary to prevent TS from using the closure value\n    // This allows for workerization to function correctly\n    Inflate.prototype.c.call(this, final);\n  };\n  return Unzlib;\n}();\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous Zlib decompression stream\n   * @param cb The callback to call whenever data is deflated\n   */\n  function AsyncUnzlib(cb) {\n    this.ondata = cb;\n    astrmify([bInflt, zule, function () {\n      return [astrm, Inflate, Unzlib];\n    }], this, 0, function () {\n      var strm = new Unzlib();\n      onmessage = astrm(strm);\n    }, 11);\n  }\n  return AsyncUnzlib;\n}();\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return cbify(data, opts, [bInflt, zule, function () {\n    return [unzlibSync];\n  }], function (ev) {\n    return pbf(unzlibSync(ev.data[0], gu8(ev.data[1])));\n  }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n  return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/function () {\n  /**\n   * Creates a decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n  function Decompress(cb) {\n    this.G = Gunzip;\n    this.I = Inflate;\n    this.Z = Unzlib;\n    this.ondata = cb;\n  }\n  /**\n   * Pushes a chunk to be decompressed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Decompress.prototype.push = function (chunk, final) {\n    if (!this.ondata) throw 'no stream handler';\n    if (!this.s) {\n      if (this.p && this.p.length) {\n        var n = new u8(this.p.length + chunk.length);\n        n.set(this.p), n.set(chunk, this.p.length);\n      } else this.p = chunk;\n      if (this.p.length > 2) {\n        var _this_1 = this;\n        var cb = function () {\n          _this_1.ondata.apply(_this_1, arguments);\n        };\n        this.s = this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8 ? new this.G(cb) : (this.p[0] & 15) != 8 || this.p[0] >> 4 > 7 || (this.p[0] << 8 | this.p[1]) % 31 ? new this.I(cb) : new this.Z(cb);\n        this.s.push(this.p, final);\n        this.p = null;\n      }\n    } else this.s.push(chunk, final);\n  };\n  return Decompress;\n}();\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/function () {\n  /**\n  * Creates an asynchronous decompression stream\n  * @param cb The callback to call whenever data is decompressed\n  */\n  function AsyncDecompress(cb) {\n    this.G = AsyncGunzip;\n    this.I = AsyncInflate;\n    this.Z = AsyncUnzlib;\n    this.ondata = cb;\n  }\n  /**\n   * Pushes a chunk to be decompressed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  AsyncDecompress.prototype.push = function (chunk, final) {\n    Decompress.prototype.push.call(this, chunk, final);\n  };\n  return AsyncDecompress;\n}();\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzip(data, opts, cb) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflate(data, opts, cb) : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n  return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzipSync(data, out) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflateSync(data, out) : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n  for (var k in d) {\n    var val = d[k],\n      n = p + k;\n    if (val instanceof u8) t[n] = [val, o];else if (Array.isArray(val)) t[n] = [val[0], mrg(o, val[1])];else fltn(val, n + '/', t, o);\n  }\n};\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n  var l = str.length;\n  if (!latin1 && typeof TextEncoder != 'undefined') return new TextEncoder().encode(str);\n  var ar = new u8(str.length + (str.length >>> 1));\n  var ai = 0;\n  var w = function (v) {\n    ar[ai++] = v;\n  };\n  for (var i = 0; i < l; ++i) {\n    if (ai + 5 > ar.length) {\n      var n = new u8(ai + 8 + (l - i << 1));\n      n.set(ar);\n      ar = n;\n    }\n    var c = str.charCodeAt(i);\n    if (c < 128 || latin1) w(c);else if (c < 2048) w(192 | c >>> 6), w(128 | c & 63);else if (c > 55295 && c < 57344) c = 65536 + (c & 1023 << 10) | str.charCodeAt(++i) & 1023, w(240 | c >>> 18), w(128 | c >>> 12 & 63), w(128 | c >>> 6 & 63), w(128 | c & 63);else w(224 | c >>> 12), w(128 | c >>> 6 & 63), w(128 | c & 63);\n  }\n  return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n  var r = '';\n  if (!latin1 && typeof TextDecoder != 'undefined') return new TextDecoder().decode(dat);\n  for (var i = 0; i < dat.length;) {\n    var c = dat[i++];\n    if (c < 128 || latin1) r += String.fromCharCode(c);else if (c < 224) r += String.fromCharCode((c & 31) << 6 | dat[i++] & 63);else if (c < 240) r += String.fromCharCode((c & 15) << 12 | (dat[i++] & 63) << 6 | dat[i++] & 63);else c = ((c & 15) << 18 | (dat[i++] & 63) << 12 | (dat[i++] & 63) << 6 | dat[i++] & 63) - 65536, r += String.fromCharCode(55296 | c >> 10, 56320 | c & 1023);\n  }\n  return r;\n}\n;\n// skip local zip header\nvar slzh = function (d, b) {\n  return b + 30 + b2(d, b + 26) + b2(d, b + 28);\n};\n// read zip header\nvar zh = function (d, b, z) {\n  var fnl = b2(d, b + 28),\n    fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)),\n    es = b + 46 + fnl;\n  var _a = z ? z64e(d, es) : [b4(d, b + 20), b4(d, b + 24), b4(d, b + 42)],\n    sc = _a[0],\n    su = _a[1],\n    off = _a[2];\n  return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n  for (; b2(d, b) != 1; b += 4 + b2(d, b + 2));\n  return [b4(d, b + 12), b4(d, b + 4), b4(d, b + 20)];\n};\n// write zip header\nvar wzh = function (d, b, c, cmp, su, fn, u, o, ce, t) {\n  var fl = fn.length,\n    l = cmp.length;\n  wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n  if (ce != null) d[b] = 20, b += 2;\n  d[b] = 20, b += 2; // spec compliance? what's that?\n  d[b++] = t == 8 && (o.level == 1 ? 6 : o.level < 6 ? 4 : o.level == 9 ? 2 : 0), d[b++] = u && 8;\n  d[b] = t, b += 2;\n  var dt = new Date(o.mtime || Date.now()),\n    y = dt.getFullYear() - 1980;\n  if (y < 0 || y > 119) throw 'date not in range 1980-2099';\n  wbytes(d, b, (y << 24) * 2 | dt.getMonth() + 1 << 21 | dt.getDate() << 16 | dt.getHours() << 11 | dt.getMinutes() << 5 | dt.getSeconds() >>> 1);\n  b += 4;\n  wbytes(d, b, c);\n  wbytes(d, b + 4, l);\n  wbytes(d, b + 8, su);\n  wbytes(d, b + 12, fl), b += 16; // skip extra field, comment\n  if (ce != null) wbytes(d, b += 10, ce), b += 4;\n  d.set(fn, b);\n  b += fl;\n  if (ce == null) d.set(cmp, b);\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n  wbytes(o, b, 0x6054B50); // skip disk\n  wbytes(o, b + 8, c);\n  wbytes(o, b + 10, c);\n  wbytes(o, b + 12, d);\n  wbytes(o, b + 16, e);\n};\nexport function zip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') throw 'no callback';\n  var r = {};\n  fltn(data, '', r, opts);\n  var k = Object.keys(r);\n  var lft = k.length,\n    o = 0,\n    tot = 0;\n  var slft = lft,\n    files = new Array(lft);\n  var term = [];\n  var tAll = function () {\n    for (var i = 0; i < term.length; ++i) term[i]();\n  };\n  var cbf = function () {\n    var out = new u8(tot + 22),\n      oe = o,\n      cdl = tot - o;\n    tot = 0;\n    for (var i = 0; i < slft; ++i) {\n      var f = files[i];\n      try {\n        wzh(out, tot, f.c, f.d, f.m, f.n, f.u, f.p, null, f.t);\n        wzh(out, o, f.c, f.d, f.m, f.n, f.u, f.p, tot, f.t), o += 46 + f.n.length, tot += 30 + f.n.length + f.d.length;\n      } catch (e) {\n        return cb(e, null);\n      }\n    }\n    wzf(out, o, files.length, cdl, oe);\n    cb(null, out);\n  };\n  if (!lft) cbf();\n  var _loop_1 = function (i) {\n    var fn = k[i];\n    var _a = r[fn],\n      file = _a[0],\n      p = _a[1];\n    var c = crc(),\n      m = file.length;\n    c.p(file);\n    var n = strToU8(fn),\n      s = n.length;\n    var t = p.level == 0 ? 0 : 8;\n    var cbl = function (e, d) {\n      if (e) {\n        tAll();\n        cb(e, null);\n      } else {\n        var l = d.length;\n        files[i] = {\n          t: t,\n          d: d,\n          m: m,\n          c: c.d(),\n          u: fn.length != l,\n          n: n,\n          p: p\n        };\n        o += 30 + s + l;\n        tot += 76 + 2 * s + l;\n        if (! --lft) cbf();\n      }\n    };\n    if (n.length > 65535) cbl('filename too long', null);\n    if (!t) cbl(null, file);else if (m < 160000) {\n      try {\n        cbl(null, deflateSync(file, p));\n      } catch (e) {\n        cbl(e, null);\n      }\n    } else term.push(deflate(file, p, cbl));\n  };\n  // Cannot use lft because it can decrease\n  for (var i = 0; i < slft; ++i) {\n    _loop_1(i);\n  }\n  return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  var r = {};\n  var files = [];\n  fltn(data, '', r, opts);\n  var o = 0;\n  var tot = 0;\n  for (var fn in r) {\n    var _a = r[fn],\n      file = _a[0],\n      p = _a[1];\n    var t = p.level == 0 ? 0 : 8;\n    var n = strToU8(fn),\n      s = n.length;\n    if (n.length > 65535) throw 'filename too long';\n    var d = t ? deflateSync(file, p) : file,\n      l = d.length;\n    var c = crc();\n    c.p(file);\n    files.push({\n      t: t,\n      d: d,\n      m: file.length,\n      c: c.d(),\n      u: fn.length != s,\n      n: n,\n      o: o,\n      p: p\n    });\n    o += 30 + s + l;\n    tot += 76 + 2 * s + l;\n  }\n  var out = new u8(tot + 22),\n    oe = o,\n    cdl = tot - o;\n  for (var i = 0; i < files.length; ++i) {\n    var f = files[i];\n    wzh(out, f.o, f.c, f.d, f.m, f.n, f.u, f.p, null, f.t);\n    wzh(out, o, f.c, f.d, f.m, f.n, f.u, f.p, f.o, f.t), o += 46 + f.n.length;\n  }\n  wzf(out, o, files.length, cdl, oe);\n  return out;\n}\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n  if (typeof cb != 'function') throw 'no callback';\n  var term = [];\n  var tAll = function () {\n    for (var i = 0; i < term.length; ++i) term[i]();\n  };\n  var files = {};\n  var e = data.length - 22;\n  for (; b4(data, e) != 0x6054B50; --e) {\n    if (!e || data.length - e > 65558) {\n      cb('invalid zip file', null);\n      return;\n    }\n  }\n  ;\n  var lft = b2(data, e + 8);\n  if (!lft) cb(null, {});\n  var c = lft;\n  var o = b4(data, e + 16);\n  var z = o == 4294967295;\n  if (z) {\n    e = b4(data, e - 12);\n    if (b4(data, e) != 0x6064B50) throw 'invalid zip file';\n    c = lft = b4(data, e + 32);\n    o = b4(data, e + 48);\n  }\n  var _loop_2 = function (i) {\n    var _a = zh(data, o, z),\n      c_1 = _a[0],\n      sc = _a[1],\n      su = _a[2],\n      fn = _a[3],\n      no = _a[4],\n      off = _a[5],\n      b = slzh(data, off);\n    o = no;\n    var cbl = function (e, d) {\n      if (e) {\n        tAll();\n        cb(e, null);\n      } else {\n        files[fn] = d;\n        if (! --lft) cb(null, files);\n      }\n    };\n    if (!c_1) cbl(null, slc(data, b, b + sc));else if (c_1 == 8) {\n      var infl = data.subarray(b, b + sc);\n      if (sc < 320000) {\n        try {\n          cbl(null, inflateSync(infl, new u8(su)));\n        } catch (e) {\n          cbl(e, null);\n        }\n      } else term.push(inflate(infl, {\n        size: su\n      }, cbl));\n    } else cbl('unknown compression type ' + c_1, null);\n  };\n  for (var i = 0; i < c; ++i) {\n    _loop_2(i);\n  }\n  return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n  var files = {};\n  var e = data.length - 22;\n  for (; b4(data, e) != 0x6054B50; --e) {\n    if (!e || data.length - e > 65558) throw 'invalid zip file';\n  }\n  ;\n  var c = b2(data, e + 8);\n  if (!c) return {};\n  var o = b4(data, e + 16);\n  var z = o == 4294967295;\n  if (z) {\n    e = b4(data, e - 12);\n    if (b4(data, e) != 0x6064B50) throw 'invalid zip file';\n    c = b4(data, e + 32);\n    o = b4(data, e + 48);\n  }\n  for (var i = 0; i < c; ++i) {\n    var _a = zh(data, o, z),\n      c_2 = _a[0],\n      sc = _a[1],\n      su = _a[2],\n      fn = _a[3],\n      no = _a[4],\n      off = _a[5],\n      b = slzh(data, off);\n    o = no;\n    if (!c_2) files[fn] = slc(data, b, b + sc);else if (c_2 == 8) files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));else throw 'unknown compression type ' + c_2;\n  }\n  return files;\n}", "map": {"version": 3, "names": ["ch2", "wk", "c", "id", "msg", "transfer", "cb", "u", "URL", "createObjectURL", "Blob", "type", "w", "Worker", "onerror", "e", "error", "onmessage", "data", "postMessage", "u8", "Uint8Array", "u16", "Uint16Array", "u32", "Uint32Array", "fleb", "fdeb", "clim", "freb", "eb", "start", "b", "i", "r", "j", "_a", "fl", "revfl", "_b", "fd", "revfd", "rev", "x", "hMap", "cd", "mb", "s", "length", "l", "le", "co", "rvb", "sv", "r_1", "v", "m", "flt", "fdt", "flm", "flrm", "fdm", "fdrm", "max", "a", "bits", "d", "p", "o", "bits16", "shft", "slc", "n", "set", "subarray", "inflt", "dat", "buf", "st", "sl", "noBuf", "noSt", "cbuf", "bl", "nbuf", "Math", "final", "f", "pos", "bt", "lm", "dm", "lbt", "dbt", "tbts", "t", "hLit", "hcLen", "tl", "ldt", "clt", "clb", "clbmsk", "clm", "lt", "dt", "lms", "dms", "mxa", "sym", "add", "dsym", "end", "wbits", "wbits16", "hTree", "push", "t2", "slice", "sort", "i0", "i1", "i2", "maxSym", "tr", "mbt", "ln", "lft", "cst", "i2_1", "i2_2", "i2_3", "lc", "cl", "cli", "cln", "cls", "clen", "cf", "wfblk", "out", "wblk", "syms", "lf", "df", "li", "bs", "dlt", "mlb", "ddt", "mdb", "_c", "lclt", "nlc", "_d", "lcdt", "ndc", "lcfreq", "_e", "lct", "mlcb", "nlcc", "flen", "ftlen", "dtlen", "ll", "dl", "llm", "lcts", "it", "clct", "len", "dst", "deo", "et", "dflt", "lvl", "plvl", "pre", "post", "lst", "floor", "opt", "msk_1", "prev", "head", "bs1_1", "ceil", "bs2_1", "hsh", "lc_1", "wi", "hv", "imod", "pimod", "rem", "ch_1", "dif", "maxn", "min", "maxd", "ml", "nl", "mmd", "md", "ti", "pti", "lin", "din", "crct", "k", "crc", "cr", "<PERSON><PERSON>", "dopt", "level", "mem", "log", "mrg", "wcln", "fn", "fnStr", "td", "toString", "ks", "indexOf", "lastIndexOf", "replace", "split", "st_1", "prototype", "spInd", "ch", "cbfs", "constructor", "buffer", "wrkr", "fns", "init", "td_1", "bInflt", "inflateSync", "pbf", "gu8", "bDflt", "deflateSync", "gze", "gzh", "gzhl", "wbytes", "guze", "gzs", "gzl", "zle", "zlh", "zule", "zlv", "size", "cbify", "opts", "err", "terminate", "consume", "astrm", "strm", "ondata", "ev", "astrmify", "call", "b2", "b4", "filename", "mtime", "Date", "now", "charCodeAt", "flg", "zs", "lv", "AsyncCmpStrm", "Deflate", "chunk", "AsyncDeflate", "deflate", "Inflate", "bts", "AsyncInflate", "inflate", "Gzip", "raw", "AsyncGzip", "gzip", "gzipSync", "<PERSON><PERSON><PERSON>", "AsyncGunzip", "gunzip", "gunzipSync", "<PERSON><PERSON><PERSON>", "AsyncZlib", "zlib", "zlibSync", "<PERSON><PERSON><PERSON><PERSON>", "AsyncUnzlib", "unz<PERSON>b", "unzlibSync", "compress", "AsyncCompress", "compressSync", "Compress", "Decompress", "G", "I", "Z", "_this_1", "apply", "arguments", "AsyncDecompress", "decompress", "decompressSync", "fltn", "val", "Array", "isArray", "strToU8", "str", "latin1", "TextEncoder", "encode", "ar", "ai", "strFromU8", "TextDecoder", "decode", "String", "fromCharCode", "slzh", "zh", "z", "fnl", "es", "z64e", "sc", "su", "off", "wzh", "cmp", "ce", "y", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "wzf", "zip", "Object", "keys", "tot", "slft", "files", "term", "tAll", "cbf", "oe", "cdl", "_loop_1", "file", "cbl", "zipSync", "unzip", "_loop_2", "c_1", "no", "infl", "unzipSync", "c_2"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/fflate/esm/browser.js"], "sourcesContent": ["// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Much of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// Many optimizations have been made, so the bundle size is ultimately smaller but performance is similar.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = (function (c, id, msg, transfer, cb) {\n    var u = ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c], { type: 'text/javascript' })));\n    var w = new Worker(u);\n    w.onerror = function (e) { return cb(e.error, null); };\n    w.onmessage = function (e) { return cb(null, e.data); };\n    w.postMessage(msg, transfer);\n    return w;\n});\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i)\n            co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) >> 0;\n    return ((d[o] | (d[o + 1] << 8)) >>> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) >> 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >>> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) >> 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                if (!noSt && pos + tl * (clb + 7) > tbts)\n                    break;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts)\n                throw 'unexpected EOF';\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var mxa = lbt + dbt + 18;\n        while (noSt || pos + mxa < tbts) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts)\n                throw 'unexpected EOF';\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts)\n                    throw 'unexpected EOF';\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = pos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) >> 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) >> 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [new u8(0), 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.floor(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            var hv = hsh(i);\n            // index mod 32768\n            var imod = i & 32767;\n            // previous index with this value\n            var pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst)\n            pos = wfblk(w, pos, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new u32(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && 0xEDB88320) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = 0xFFFFFFFF;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return c ^ 0xFFFFFFFF; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 5552, l);\n                for (; i < e; ++i)\n                    n += d[i], m += n;\n                n %= 65521, m %= 65521;\n            }\n            a = n, b = m;\n        },\n        d: function () { return ((a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8)) + ((a & 255) << 23) * 2; }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    if (!opts.consume)\n        dat = new u8(dat);\n    w.postMessage([dat, opts], [dat.buffer]);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16)) + (d[b + 3] << 23) * 2; };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) + (2 * (d[l - 1] << 23));\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    return dopt(data, opts, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) >> 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = gzs(this.p);\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    var l = str.length;\n    if (!latin1 && typeof TextEncoder != 'undefined')\n        return new TextEncoder().encode(str);\n    var ar = new u8(str.length + (str.length >>> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >>> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >>> 18)), w(128 | ((c >>> 12) & 63)), w(128 | ((c >>> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >>> 12)), w(128 | ((c >>> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    var r = '';\n    if (!latin1 && typeof TextDecoder != 'undefined')\n        return new TextDecoder().decode(dat);\n    for (var i = 0; i < dat.length;) {\n        var c = dat[i++];\n        if (c < 128 || latin1)\n            r += String.fromCharCode(c);\n        else if (c < 224)\n            r += String.fromCharCode((c & 31) << 6 | (dat[i++] & 63));\n        else if (c < 240)\n            r += String.fromCharCode((c & 15) << 12 | (dat[i++] & 63) << 6 | (dat[i++] & 63));\n        else\n            c = ((c & 15) << 18 | (dat[i++] & 63) << 12 | (dat[i++] & 63) << 6 | (dat[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n    }\n    return r;\n}\n;\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl;\n    var _a = z ? z64e(d, es) : [b4(d, b + 20), b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b4(d, b + 12), b4(d, b + 4), b4(d, b + 20)];\n};\n// write zip header\nvar wzh = function (d, b, c, cmp, su, fn, u, o, ce, t) {\n    var fl = fn.length, l = cmp.length;\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b] = 20, b += 2;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (t == 8 && (o.level == 1 ? 6 : o.level < 6 ? 4 : o.level == 9 ? 2 : 0)), d[b++] = u && 8;\n    d[b] = t, b += 2;\n    var dt = new Date(o.mtime || Date.now()), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, ((y << 24) * 2) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1));\n    b += 4;\n    wbytes(d, b, c);\n    wbytes(d, b + 4, l);\n    wbytes(d, b + 8, su);\n    wbytes(d, b + 12, fl), b += 16; // skip extra field, comment\n    if (ce != null)\n        wbytes(d, b += 10, ce), b += 4;\n    d.set(fn, b);\n    b += fl;\n    if (ce == null)\n        d.set(cmp, b);\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                wzh(out, tot, f.c, f.d, f.m, f.n, f.u, f.p, null, f.t);\n                wzh(out, o, f.c, f.d, f.m, f.n, f.u, f.p, tot, f.t), o += 46 + f.n.length, tot += 30 + f.n.length + f.d.length;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), m = file.length;\n        c.p(file);\n        var n = strToU8(fn), s = n.length;\n        var t = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = {\n                    t: t,\n                    d: d,\n                    m: m,\n                    c: c.d(),\n                    u: fn.length != l,\n                    n: n,\n                    p: p\n                };\n                o += 30 + s + l;\n                tot += 76 + 2 * s + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (n.length > 65535)\n            cbl('filename too long', null);\n        if (!t)\n            cbl(null, file);\n        else if (m < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (opts === void 0) { opts = {}; }\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var t = p.level == 0 ? 0 : 8;\n        var n = strToU8(fn), s = n.length;\n        if (n.length > 65535)\n            throw 'filename too long';\n        var d = t ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push({\n            t: t,\n            d: d,\n            m: file.length,\n            c: c.d(),\n            u: fn.length != s,\n            n: n,\n            o: o,\n            p: p\n        });\n        o += 30 + s + l;\n        tot += 76 + 2 * s + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f.c, f.d, f.m, f.n, f.u, f.p, null, f.t);\n        wzh(out, o, f.c, f.d, f.m, f.n, f.u, f.p, f.o, f.t), o += 46 + f.n.length;\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_2 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_2(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,GAAG,GAAG,CAAC,CAAC;AACZ,IAAIC,EAAE,GAAI,SAAAA,CAAUC,CAAC,EAAEC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EAC1C,IAAIC,CAAC,GAAGP,GAAG,CAACG,EAAE,CAAC,KAAKH,GAAG,CAACG,EAAE,CAAC,GAAGK,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACR,CAAC,CAAC,EAAE;IAAES,IAAI,EAAE;EAAkB,CAAC,CAAC,CAAC,CAAC;EAC9F,IAAIC,CAAC,GAAG,IAAIC,MAAM,CAACN,CAAC,CAAC;EACrBK,CAAC,CAACE,OAAO,GAAG,UAAUC,CAAC,EAAE;IAAE,OAAOT,EAAE,CAACS,CAAC,CAACC,KAAK,EAAE,IAAI,CAAC;EAAE,CAAC;EACtDJ,CAAC,CAACK,SAAS,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAOT,EAAE,CAAC,IAAI,EAAES,CAAC,CAACG,IAAI,CAAC;EAAE,CAAC;EACvDN,CAAC,CAACO,WAAW,CAACf,GAAG,EAAEC,QAAQ,CAAC;EAC5B,OAAOO,CAAC;AACZ,CAAE;;AAEF;AACA,IAAIQ,EAAE,GAAGC,UAAU;EAAEC,GAAG,GAAGC,WAAW;EAAEC,GAAG,GAAGC,WAAW;AACzD;AACA,IAAIC,IAAI,GAAG,IAAIN,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAa,CAAC,EAAE,CAAC,EAAE,gBAAiB,CAAC,CAAC,CAAC;AACjJ;AACA;AACA,IAAIO,IAAI,GAAG,IAAIP,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AACxI;AACA,IAAIQ,IAAI,GAAG,IAAIR,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AACrF;AACA,IAAIS,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAE;EAC5B,IAAIC,CAAC,GAAG,IAAIV,GAAG,CAAC,EAAE,CAAC;EACnB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;IACzBD,CAAC,CAACC,CAAC,CAAC,GAAGF,KAAK,IAAI,CAAC,IAAID,EAAE,CAACG,CAAC,GAAG,CAAC,CAAC;EAClC;EACA;EACA,IAAIC,CAAC,GAAG,IAAIV,GAAG,CAACQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;IACzB,KAAK,IAAIE,CAAC,GAAGH,CAAC,CAACC,CAAC,CAAC,EAAEE,CAAC,GAAGH,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAEE,CAAC,EAAE;MAClCD,CAAC,CAACC,CAAC,CAAC,GAAKA,CAAC,GAAGH,CAAC,CAACC,CAAC,CAAC,IAAK,CAAC,GAAIA,CAAC;IAChC;EACJ;EACA,OAAO,CAACD,CAAC,EAAEE,CAAC,CAAC;AACjB,CAAC;AACD,IAAIE,EAAE,GAAGP,IAAI,CAACH,IAAI,EAAE,CAAC,CAAC;EAAEW,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;EAAEE,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;AACjD;AACAC,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAEC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;AAC7B,IAAIC,EAAE,GAAGV,IAAI,CAACF,IAAI,EAAE,CAAC,CAAC;EAAEa,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;EAAEE,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;AACjD;AACA,IAAIG,GAAG,GAAG,IAAIpB,GAAG,CAAC,KAAK,CAAC;AACxB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,KAAK,EAAE,EAAEA,CAAC,EAAE;EAC5B;EACA,IAAIU,CAAC,GAAI,CAACV,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE;EAClDU,CAAC,GAAI,CAACA,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE;EAC9CA,CAAC,GAAI,CAACA,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE;EAC9CD,GAAG,CAACT,CAAC,CAAC,GAAG,CAAE,CAACU,CAAC,GAAG,MAAM,MAAM,CAAC,GAAK,CAACA,CAAC,GAAG,MAAM,KAAK,CAAE,MAAM,CAAC;AAC/D;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAI,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAEZ,CAAC,EAAE;EAC7B,IAAIa,CAAC,GAAGF,EAAE,CAACG,MAAM;EACjB;EACA,IAAIf,CAAC,GAAG,CAAC;EACT;EACA,IAAIgB,CAAC,GAAG,IAAI3B,GAAG,CAACwB,EAAE,CAAC;EACnB;EACA,OAAOb,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EACb,EAAEgB,CAAC,CAACJ,EAAE,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;EAClB;EACA,IAAIiB,EAAE,GAAG,IAAI5B,GAAG,CAACwB,EAAE,CAAC;EACpB,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,EAAE,EAAE,EAAEb,CAAC,EAAE;IACrBiB,EAAE,CAACjB,CAAC,CAAC,GAAIiB,EAAE,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAGgB,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC;EACvC;EACA,IAAIkB,EAAE;EACN,IAAIjB,CAAC,EAAE;IACH;IACAiB,EAAE,GAAG,IAAI7B,GAAG,CAAC,CAAC,IAAIwB,EAAE,CAAC;IACrB;IACA,IAAIM,GAAG,GAAG,EAAE,GAAGN,EAAE;IACjB,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACpB;MACA,IAAIY,EAAE,CAACZ,CAAC,CAAC,EAAE;QACP;QACA,IAAIoB,EAAE,GAAIpB,CAAC,IAAI,CAAC,GAAIY,EAAE,CAACZ,CAAC,CAAC;QACzB;QACA,IAAIqB,GAAG,GAAGR,EAAE,GAAGD,EAAE,CAACZ,CAAC,CAAC;QACpB;QACA,IAAIsB,CAAC,GAAGL,EAAE,CAACL,EAAE,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAIqB,GAAG;QAC9B;QACA,KAAK,IAAIE,CAAC,GAAGD,CAAC,GAAI,CAAC,CAAC,IAAID,GAAG,IAAI,CAAE,EAAEC,CAAC,IAAIC,CAAC,EAAE,EAAED,CAAC,EAAE;UAC5C;UACAJ,EAAE,CAACT,GAAG,CAACa,CAAC,CAAC,KAAKH,GAAG,CAAC,GAAGC,EAAE;QAC3B;MACJ;IACJ;EACJ,CAAC,MACI;IACDF,EAAE,GAAG,IAAI7B,GAAG,CAACyB,CAAC,CAAC;IACf,KAAKd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAClBkB,EAAE,CAAClB,CAAC,CAAC,GAAGS,GAAG,CAACQ,EAAE,CAACL,EAAE,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAM,EAAE,GAAGY,EAAE,CAACZ,CAAC,CAAE;EACrD;EACA,OAAOkB,EAAE;AACb,CAAE;AACF;AACA,IAAIM,GAAG,GAAG,IAAIrC,EAAE,CAAC,GAAG,CAAC;AACrB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EACxBwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd,KAAK,IAAIA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAC1BwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd,KAAK,IAAIA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAC1BwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd,KAAK,IAAIA,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAC1BwB,GAAG,CAACxB,CAAC,CAAC,GAAG,CAAC;AACd;AACA,IAAIyB,GAAG,GAAG,IAAItC,EAAE,CAAC,EAAE,CAAC;AACpB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EACvByB,GAAG,CAACzB,CAAC,CAAC,GAAG,CAAC;AACd;AACA,IAAI0B,GAAG,GAAG,aAAcf,IAAI,CAACa,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAEG,IAAI,GAAG,aAAchB,IAAI,CAACa,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7E;AACA,IAAII,GAAG,GAAG,aAAcjB,IAAI,CAACc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAAEI,IAAI,GAAG,aAAclB,IAAI,CAACc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7E;AACA,IAAIK,GAAG,GAAG,SAAAA,CAAUC,CAAC,EAAE;EACnB,IAAIR,CAAC,GAAGQ,CAAC,CAAC,CAAC,CAAC;EACZ,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,CAAC,CAAChB,MAAM,EAAE,EAAEf,CAAC,EAAE;IAC/B,IAAI+B,CAAC,CAAC/B,CAAC,CAAC,GAAGuB,CAAC,EACRA,CAAC,GAAGQ,CAAC,CAAC/B,CAAC,CAAC;EAChB;EACA,OAAOuB,CAAC;AACZ,CAAC;AACD;AACA,IAAIS,IAAI,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAEX,CAAC,EAAE;EAC1B,IAAIY,CAAC,GAAID,CAAC,GAAG,CAAC,IAAK,CAAC;EACpB,OAAQ,CAACD,CAAC,CAACE,CAAC,CAAC,GAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,OAAOD,CAAC,GAAG,CAAC,CAAC,GAAIX,CAAC;AACrD,CAAC;AACD;AACA,IAAIa,MAAM,GAAG,SAAAA,CAAUH,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,IAAK,CAAC;EACpB,OAAQ,CAACD,CAAC,CAACE,CAAC,CAAC,GAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,GAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,OAAOD,CAAC,GAAG,CAAC,CAAC;AACnE,CAAC;AACD;AACA,IAAIG,IAAI,GAAG,SAAAA,CAAUH,CAAC,EAAE;EAAE,OAAO,CAAEA,CAAC,GAAG,CAAC,IAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAE,CAAC;AACjE;AACA;AACA,IAAII,GAAG,GAAG,SAAAA,CAAUhB,CAAC,EAAER,CAAC,EAAEhC,CAAC,EAAE;EACzB,IAAIgC,CAAC,IAAI,IAAI,IAAIA,CAAC,GAAG,CAAC,EAClBA,CAAC,GAAG,CAAC;EACT,IAAIhC,CAAC,IAAI,IAAI,IAAIA,CAAC,GAAGwC,CAAC,CAACP,MAAM,EACzBjC,CAAC,GAAGwC,CAAC,CAACP,MAAM;EAChB;EACA,IAAIwB,CAAC,GAAG,KAAKjB,CAAC,YAAYjC,GAAG,GAAGA,GAAG,GAAGiC,CAAC,YAAY/B,GAAG,GAAGA,GAAG,GAAGJ,EAAE,EAAEL,CAAC,GAAGgC,CAAC,CAAC;EACzEyB,CAAC,CAACC,GAAG,CAAClB,CAAC,CAACmB,QAAQ,CAAC3B,CAAC,EAAEhC,CAAC,CAAC,CAAC;EACvB,OAAOyD,CAAC;AACZ,CAAC;AACD;AACA,IAAIG,KAAK,GAAG,SAAAA,CAAUC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAE;EAChC;EACA,IAAIC,EAAE,GAAGH,GAAG,CAAC5B,MAAM;EACnB;EACA,IAAIgC,KAAK,GAAG,CAACH,GAAG,IAAIC,EAAE;EACtB;EACA,IAAIG,IAAI,GAAG,CAACH,EAAE,IAAIA,EAAE,CAAC7C,CAAC;EACtB,IAAI,CAAC6C,EAAE,EACHA,EAAE,GAAG,CAAC,CAAC;EACX;EACA,IAAI,CAACD,GAAG,EACJA,GAAG,GAAG,IAAIzD,EAAE,CAAC2D,EAAE,GAAG,CAAC,CAAC;EACxB;EACA,IAAIG,IAAI,GAAG,SAAAA,CAAUjC,CAAC,EAAE;IACpB,IAAIkC,EAAE,GAAGN,GAAG,CAAC7B,MAAM;IACnB;IACA,IAAIC,CAAC,GAAGkC,EAAE,EAAE;MACR;MACA,IAAIC,IAAI,GAAG,IAAIhE,EAAE,CAACiE,IAAI,CAACtB,GAAG,CAACoB,EAAE,GAAG,CAAC,EAAElC,CAAC,CAAC,CAAC;MACtCmC,IAAI,CAACX,GAAG,CAACI,GAAG,CAAC;MACbA,GAAG,GAAGO,IAAI;IACd;EACJ,CAAC;EACD;EACA,IAAIE,KAAK,GAAGR,EAAE,CAACS,CAAC,IAAI,CAAC;IAAEC,GAAG,GAAGV,EAAE,CAACX,CAAC,IAAI,CAAC;IAAEsB,EAAE,GAAGX,EAAE,CAAC9C,CAAC,IAAI,CAAC;IAAE0D,EAAE,GAAGZ,EAAE,CAAC7B,CAAC;IAAE0C,EAAE,GAAGb,EAAE,CAACZ,CAAC;IAAE0B,GAAG,GAAGd,EAAE,CAACtB,CAAC;IAAEqC,GAAG,GAAGf,EAAE,CAACN,CAAC;EACpG;EACA,IAAIsB,IAAI,GAAGf,EAAE,GAAG,CAAC;EACjB,GAAG;IACC,IAAI,CAACW,EAAE,EAAE;MACL;MACAZ,EAAE,CAACS,CAAC,GAAGD,KAAK,GAAGrB,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC;MAChC;MACA,IAAI7E,IAAI,GAAGsD,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;MAChCA,GAAG,IAAI,CAAC;MACR,IAAI,CAAC7E,IAAI,EAAE;QACP;QACA,IAAIoC,CAAC,GAAGuB,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAC;UAAEvC,CAAC,GAAG2B,GAAG,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAI6B,GAAG,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE;UAAEgD,CAAC,GAAGhD,CAAC,GAAGE,CAAC;QACpE,IAAI8C,CAAC,GAAGhB,EAAE,EAAE;UACR,IAAIE,IAAI,EACJ,MAAM,gBAAgB;UAC1B;QACJ;QACA;QACA,IAAID,KAAK,EACLE,IAAI,CAACO,EAAE,GAAGxC,CAAC,CAAC;QAChB;QACA4B,GAAG,CAACJ,GAAG,CAACG,GAAG,CAACF,QAAQ,CAAC3B,CAAC,EAAEgD,CAAC,CAAC,EAAEN,EAAE,CAAC;QAC/B;QACAX,EAAE,CAAC9C,CAAC,GAAGyD,EAAE,IAAIxC,CAAC,EAAE6B,EAAE,CAACX,CAAC,GAAGqB,GAAG,GAAGO,CAAC,GAAG,CAAC;QAClC;MACJ,CAAC,MACI,IAAIpF,IAAI,IAAI,CAAC,EACd+E,EAAE,GAAG9B,IAAI,EAAE+B,EAAE,GAAG7B,IAAI,EAAE8B,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,CAAC,KACtC,IAAIlF,IAAI,IAAI,CAAC,EAAE;QAChB;QACA,IAAIqF,IAAI,GAAG/B,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;UAAES,KAAK,GAAGhC,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC;QACxE,IAAIU,EAAE,GAAGF,IAAI,GAAG/B,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC1CA,GAAG,IAAI,EAAE;QACT;QACA,IAAIW,GAAG,GAAG,IAAI/E,EAAE,CAAC8E,EAAE,CAAC;QACpB;QACA,IAAIE,GAAG,GAAG,IAAIhF,EAAE,CAAC,EAAE,CAAC;QACpB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,KAAK,EAAE,EAAEhE,CAAC,EAAE;UAC5B;UACAmE,GAAG,CAACxE,IAAI,CAACK,CAAC,CAAC,CAAC,GAAGgC,IAAI,CAACW,GAAG,EAAEY,GAAG,GAAGvD,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5C;QACAuD,GAAG,IAAIS,KAAK,GAAG,CAAC;QAChB;QACA,IAAII,GAAG,GAAGtC,GAAG,CAACqC,GAAG,CAAC;UAAEE,MAAM,GAAG,CAAC,CAAC,IAAID,GAAG,IAAI,CAAC;QAC3C,IAAI,CAACpB,IAAI,IAAIO,GAAG,GAAGU,EAAE,IAAIG,GAAG,GAAG,CAAC,CAAC,GAAGP,IAAI,EACpC;QACJ;QACA,IAAIS,GAAG,GAAG3D,IAAI,CAACwD,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC;QAC3B,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,EAAE,GAAG;UACrB,IAAIhE,CAAC,GAAGqE,GAAG,CAACtC,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAEc,MAAM,CAAC,CAAC;UACnC;UACAd,GAAG,IAAItD,CAAC,GAAG,EAAE;UACb;UACA,IAAIa,CAAC,GAAGb,CAAC,KAAK,CAAC;UACf;UACA,IAAIa,CAAC,GAAG,EAAE,EAAE;YACRoD,GAAG,CAAClE,CAAC,EAAE,CAAC,GAAGc,CAAC;UAChB,CAAC,MACI;YACD;YACA,IAAI7C,CAAC,GAAG,CAAC;cAAEsE,CAAC,GAAG,CAAC;YAChB,IAAIzB,CAAC,IAAI,EAAE,EACPyB,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEtF,CAAC,GAAGiG,GAAG,CAAClE,CAAC,GAAG,CAAC,CAAC,CAAC,KACnD,IAAIc,CAAC,IAAI,EAAE,EACZyB,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC,EAAEA,GAAG,IAAI,CAAC,CAAC,KACnC,IAAIzC,CAAC,IAAI,EAAE,EACZyB,CAAC,GAAG,EAAE,GAAGP,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC;YAC1C,OAAOhB,CAAC,EAAE,EACN2B,GAAG,CAAClE,CAAC,EAAE,CAAC,GAAG/B,CAAC;UACpB;QACJ;QACA;QACA,IAAIsG,EAAE,GAAGL,GAAG,CAACzB,QAAQ,CAAC,CAAC,EAAEsB,IAAI,CAAC;UAAES,EAAE,GAAGN,GAAG,CAACzB,QAAQ,CAACsB,IAAI,CAAC;QACvD;QACAJ,GAAG,GAAG7B,GAAG,CAACyC,EAAE,CAAC;QACb;QACAX,GAAG,GAAG9B,GAAG,CAAC0C,EAAE,CAAC;QACbf,EAAE,GAAG9C,IAAI,CAAC4D,EAAE,EAAEZ,GAAG,EAAE,CAAC,CAAC;QACrBD,EAAE,GAAG/C,IAAI,CAAC6D,EAAE,EAAEZ,GAAG,EAAE,CAAC,CAAC;MACzB,CAAC,MAEG,MAAM,oBAAoB;MAC9B,IAAIL,GAAG,GAAGM,IAAI,EACV,MAAM,gBAAgB;IAC9B;IACA;IACA;IACA,IAAId,KAAK,EACLE,IAAI,CAACO,EAAE,GAAG,MAAM,CAAC;IACrB,IAAIiB,GAAG,GAAG,CAAC,CAAC,IAAId,GAAG,IAAI,CAAC;MAAEe,GAAG,GAAG,CAAC,CAAC,IAAId,GAAG,IAAI,CAAC;IAC9C,IAAIe,GAAG,GAAGhB,GAAG,GAAGC,GAAG,GAAG,EAAE;IACxB,OAAOZ,IAAI,IAAIO,GAAG,GAAGoB,GAAG,GAAGd,IAAI,EAAE;MAC7B;MACA,IAAI5F,CAAC,GAAGwF,EAAE,CAACrB,MAAM,CAACO,GAAG,EAAEY,GAAG,CAAC,GAAGkB,GAAG,CAAC;QAAEG,GAAG,GAAG3G,CAAC,KAAK,CAAC;MACjDsF,GAAG,IAAItF,CAAC,GAAG,EAAE;MACb,IAAIsF,GAAG,GAAGM,IAAI,EACV,MAAM,gBAAgB;MAC1B,IAAI,CAAC5F,CAAC,EACF,MAAM,wBAAwB;MAClC,IAAI2G,GAAG,GAAG,GAAG,EACThC,GAAG,CAACY,EAAE,EAAE,CAAC,GAAGoB,GAAG,CAAC,KACf,IAAIA,GAAG,IAAI,GAAG,EAAE;QACjBnB,EAAE,GAAG,IAAI;QACT;MACJ,CAAC,MACI;QACD,IAAIoB,GAAG,GAAGD,GAAG,GAAG,GAAG;QACnB;QACA,IAAIA,GAAG,GAAG,GAAG,EAAE;UACX;UACA,IAAI5E,CAAC,GAAG4E,GAAG,GAAG,GAAG;YAAE7E,CAAC,GAAGN,IAAI,CAACO,CAAC,CAAC;UAC9B6E,GAAG,GAAG7C,IAAI,CAACW,GAAG,EAAEY,GAAG,EAAE,CAAC,CAAC,IAAIxD,CAAC,IAAI,CAAC,CAAC,GAAGK,EAAE,CAACJ,CAAC,CAAC;UAC1CuD,GAAG,IAAIxD,CAAC;QACZ;QACA;QACA,IAAIkC,CAAC,GAAGyB,EAAE,CAACtB,MAAM,CAACO,GAAG,EAAEY,GAAG,CAAC,GAAGmB,GAAG,CAAC;UAAEI,IAAI,GAAG7C,CAAC,KAAK,CAAC;QAClD,IAAI,CAACA,CAAC,EACF,MAAM,kBAAkB;QAC5BsB,GAAG,IAAItB,CAAC,GAAG,EAAE;QACb,IAAIuC,EAAE,GAAGjE,EAAE,CAACuE,IAAI,CAAC;QACjB,IAAIA,IAAI,GAAG,CAAC,EAAE;UACV,IAAI/E,CAAC,GAAGL,IAAI,CAACoF,IAAI,CAAC;UAClBN,EAAE,IAAIpC,MAAM,CAACO,GAAG,EAAEY,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxD,CAAC,IAAI,CAAE,EAAEwD,GAAG,IAAIxD,CAAC;QACrD;QACA,IAAIwD,GAAG,GAAGM,IAAI,EACV,MAAM,gBAAgB;QAC1B,IAAId,KAAK,EACLE,IAAI,CAACO,EAAE,GAAG,MAAM,CAAC;QACrB,IAAIuB,GAAG,GAAGvB,EAAE,GAAGqB,GAAG;QAClB,OAAOrB,EAAE,GAAGuB,GAAG,EAAEvB,EAAE,IAAI,CAAC,EAAE;UACtBZ,GAAG,CAACY,EAAE,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAGgB,EAAE,CAAC;UACtB5B,GAAG,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAG,CAAC,GAAGgB,EAAE,CAAC;UAC9B5B,GAAG,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAG,CAAC,GAAGgB,EAAE,CAAC;UAC9B5B,GAAG,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,GAAG,CAACY,EAAE,GAAG,CAAC,GAAGgB,EAAE,CAAC;QAClC;QACAhB,EAAE,GAAGuB,GAAG;MACZ;IACJ;IACAlC,EAAE,CAAC7B,CAAC,GAAGyC,EAAE,EAAEZ,EAAE,CAACX,CAAC,GAAGqB,GAAG,EAAEV,EAAE,CAAC9C,CAAC,GAAGyD,EAAE;IAChC,IAAIC,EAAE,EACFJ,KAAK,GAAG,CAAC,EAAER,EAAE,CAACtB,CAAC,GAAGoC,GAAG,EAAEd,EAAE,CAACZ,CAAC,GAAGyB,EAAE,EAAEb,EAAE,CAACN,CAAC,GAAGqB,GAAG;EACpD,CAAC,QAAQ,CAACP,KAAK;EACf,OAAOG,EAAE,IAAIZ,GAAG,CAAC7B,MAAM,GAAG6B,GAAG,GAAGN,GAAG,CAACM,GAAG,EAAE,CAAC,EAAEY,EAAE,CAAC;AACnD,CAAC;AACD;AACA,IAAIwB,KAAK,GAAG,SAAAA,CAAU/C,CAAC,EAAEC,CAAC,EAAEZ,CAAC,EAAE;EAC3BA,CAAC,KAAKY,CAAC,GAAG,CAAC;EACX,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,IAAK,CAAC;EACpBD,CAAC,CAACE,CAAC,CAAC,IAAIb,CAAC;EACTW,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIb,CAAC,KAAK,CAAC;AACvB,CAAC;AACD;AACA,IAAI2D,OAAO,GAAG,SAAAA,CAAUhD,CAAC,EAAEC,CAAC,EAAEZ,CAAC,EAAE;EAC7BA,CAAC,KAAKY,CAAC,GAAG,CAAC;EACX,IAAIC,CAAC,GAAID,CAAC,GAAG,CAAC,IAAK,CAAC;EACpBD,CAAC,CAACE,CAAC,CAAC,IAAIb,CAAC;EACTW,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIb,CAAC,KAAK,CAAC;EACnBW,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIb,CAAC,KAAK,EAAE;AACxB,CAAC;AACD;AACA,IAAI4D,KAAK,GAAG,SAAAA,CAAUjD,CAAC,EAAEpB,EAAE,EAAE;EACzB;EACA,IAAIiD,CAAC,GAAG,EAAE;EACV,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,CAAC,CAAClB,MAAM,EAAE,EAAEf,CAAC,EAAE;IAC/B,IAAIiC,CAAC,CAACjC,CAAC,CAAC,EACJ8D,CAAC,CAACqB,IAAI,CAAC;MAAErE,CAAC,EAAEd,CAAC;MAAEsD,CAAC,EAAErB,CAAC,CAACjC,CAAC;IAAE,CAAC,CAAC;EACjC;EACA,IAAIc,CAAC,GAAGgD,CAAC,CAAC/C,MAAM;EAChB,IAAIqE,EAAE,GAAGtB,CAAC,CAACuB,KAAK,CAAC,CAAC;EAClB,IAAI,CAACvE,CAAC,EACF,OAAO,CAAC,IAAI3B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,IAAI2B,CAAC,IAAI,CAAC,EAAE;IACR,IAAIQ,CAAC,GAAG,IAAInC,EAAE,CAAC2E,CAAC,CAAC,CAAC,CAAC,CAAChD,CAAC,GAAG,CAAC,CAAC;IAC1BQ,CAAC,CAACwC,CAAC,CAAC,CAAC,CAAC,CAAChD,CAAC,CAAC,GAAG,CAAC;IACb,OAAO,CAACQ,CAAC,EAAE,CAAC,CAAC;EACjB;EACAwC,CAAC,CAACwB,IAAI,CAAC,UAAUvD,CAAC,EAAEhC,CAAC,EAAE;IAAE,OAAOgC,CAAC,CAACuB,CAAC,GAAGvD,CAAC,CAACuD,CAAC;EAAE,CAAC,CAAC;EAC7C;EACA;EACAQ,CAAC,CAACqB,IAAI,CAAC;IAAErE,CAAC,EAAE,CAAC,CAAC;IAAEwC,CAAC,EAAE;EAAM,CAAC,CAAC;EAC3B,IAAItC,CAAC,GAAG8C,CAAC,CAAC,CAAC,CAAC;IAAE7D,CAAC,GAAG6D,CAAC,CAAC,CAAC,CAAC;IAAEyB,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;EAC9C3B,CAAC,CAAC,CAAC,CAAC,GAAG;IAAEhD,CAAC,EAAE,CAAC,CAAC;IAAEwC,CAAC,EAAEtC,CAAC,CAACsC,CAAC,GAAGrD,CAAC,CAACqD,CAAC;IAAEtC,CAAC,EAAEA,CAAC;IAAEf,CAAC,EAAEA;EAAE,CAAC;EAC1C;EACA;EACA;EACA;EACA;EACA,OAAOuF,EAAE,IAAI1E,CAAC,GAAG,CAAC,EAAE;IAChBE,CAAC,GAAG8C,CAAC,CAACA,CAAC,CAACyB,EAAE,CAAC,CAACjC,CAAC,GAAGQ,CAAC,CAAC2B,EAAE,CAAC,CAACnC,CAAC,GAAGiC,EAAE,EAAE,GAAGE,EAAE,EAAE,CAAC;IACtCxF,CAAC,GAAG6D,CAAC,CAACyB,EAAE,IAAIC,EAAE,IAAI1B,CAAC,CAACyB,EAAE,CAAC,CAACjC,CAAC,GAAGQ,CAAC,CAAC2B,EAAE,CAAC,CAACnC,CAAC,GAAGiC,EAAE,EAAE,GAAGE,EAAE,EAAE,CAAC;IAClD3B,CAAC,CAAC0B,EAAE,EAAE,CAAC,GAAG;MAAE1E,CAAC,EAAE,CAAC,CAAC;MAAEwC,CAAC,EAAEtC,CAAC,CAACsC,CAAC,GAAGrD,CAAC,CAACqD,CAAC;MAAEtC,CAAC,EAAEA,CAAC;MAAEf,CAAC,EAAEA;IAAE,CAAC;EACjD;EACA,IAAIyF,MAAM,GAAGN,EAAE,CAAC,CAAC,CAAC,CAACtE,CAAC;EACpB,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;IACxB,IAAIoF,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC,GAAG4E,MAAM,EAChBA,MAAM,GAAGN,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;EACxB;EACA;EACA,IAAI6E,EAAE,GAAG,IAAItG,GAAG,CAACqG,MAAM,GAAG,CAAC,CAAC;EAC5B;EACA,IAAIE,GAAG,GAAGC,EAAE,CAAC/B,CAAC,CAAC0B,EAAE,GAAG,CAAC,CAAC,EAAEG,EAAE,EAAE,CAAC,CAAC;EAC9B,IAAIC,GAAG,GAAG/E,EAAE,EAAE;IACV;IACA;IACA;IACA,IAAIb,CAAC,GAAG,CAAC;MAAEwE,EAAE,GAAG,CAAC;IACjB;IACA,IAAIsB,GAAG,GAAGF,GAAG,GAAG/E,EAAE;MAAEkF,GAAG,GAAG,CAAC,IAAID,GAAG;IAClCV,EAAE,CAACE,IAAI,CAAC,UAAUvD,CAAC,EAAEhC,CAAC,EAAE;MAAE,OAAO4F,EAAE,CAAC5F,CAAC,CAACe,CAAC,CAAC,GAAG6E,EAAE,CAAC5D,CAAC,CAACjB,CAAC,CAAC,IAAIiB,CAAC,CAACuB,CAAC,GAAGvD,CAAC,CAACuD,CAAC;IAAE,CAAC,CAAC;IACnE,OAAOtD,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACf,IAAIgG,IAAI,GAAGZ,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;MAClB,IAAI6E,EAAE,CAACK,IAAI,CAAC,GAAGnF,EAAE,EAAE;QACf2D,EAAE,IAAIuB,GAAG,IAAI,CAAC,IAAKH,GAAG,GAAGD,EAAE,CAACK,IAAI,CAAE,CAAC;QACnCL,EAAE,CAACK,IAAI,CAAC,GAAGnF,EAAE;MACjB,CAAC,MAEG;IACR;IACA2D,EAAE,MAAMsB,GAAG;IACX,OAAOtB,EAAE,GAAG,CAAC,EAAE;MACX,IAAIyB,IAAI,GAAGb,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;MAClB,IAAI6E,EAAE,CAACM,IAAI,CAAC,GAAGpF,EAAE,EACb2D,EAAE,IAAI,CAAC,IAAK3D,EAAE,GAAG8E,EAAE,CAACM,IAAI,CAAC,EAAE,GAAG,CAAE,CAAC,KAEjC,EAAEjG,CAAC;IACX;IACA,OAAOA,CAAC,IAAI,CAAC,IAAIwE,EAAE,EAAE,EAAExE,CAAC,EAAE;MACtB,IAAIkG,IAAI,GAAGd,EAAE,CAACpF,CAAC,CAAC,CAACc,CAAC;MAClB,IAAI6E,EAAE,CAACO,IAAI,CAAC,IAAIrF,EAAE,EAAE;QAChB,EAAE8E,EAAE,CAACO,IAAI,CAAC;QACV,EAAE1B,EAAE;MACR;IACJ;IACAoB,GAAG,GAAG/E,EAAE;EACZ;EACA,OAAO,CAAC,IAAI1B,EAAE,CAACwG,EAAE,CAAC,EAAEC,GAAG,CAAC;AAC5B,CAAC;AACD;AACA,IAAIC,EAAE,GAAG,SAAAA,CAAUtD,CAAC,EAAEvB,CAAC,EAAEiB,CAAC,EAAE;EACxB,OAAOM,CAAC,CAACzB,CAAC,IAAI,CAAC,CAAC,GACVsC,IAAI,CAACtB,GAAG,CAAC+D,EAAE,CAACtD,CAAC,CAACvB,CAAC,EAAEA,CAAC,EAAEiB,CAAC,GAAG,CAAC,CAAC,EAAE4D,EAAE,CAACtD,CAAC,CAACtC,CAAC,EAAEe,CAAC,EAAEiB,CAAC,GAAG,CAAC,CAAC,CAAC,GAC7CjB,CAAC,CAACuB,CAAC,CAACzB,CAAC,CAAC,GAAGmB,CAAE;AACtB,CAAC;AACD;AACA,IAAIkE,EAAE,GAAG,SAAAA,CAAUlI,CAAC,EAAE;EAClB,IAAI6C,CAAC,GAAG7C,CAAC,CAAC8C,MAAM;EAChB;EACA,OAAOD,CAAC,IAAI,CAAC7C,CAAC,CAAC,EAAE6C,CAAC,CAAC,CACf;EACJ,IAAIsF,EAAE,GAAG,IAAI/G,GAAG,CAAC,EAAEyB,CAAC,CAAC;EACrB;EACA,IAAIuF,GAAG,GAAG,CAAC;IAAEC,GAAG,GAAGrI,CAAC,CAAC,CAAC,CAAC;IAAEsI,GAAG,GAAG,CAAC;EAChC,IAAI5H,CAAC,GAAG,SAAAA,CAAU2C,CAAC,EAAE;IAAE8E,EAAE,CAACC,GAAG,EAAE,CAAC,GAAG/E,CAAC;EAAE,CAAC;EACvC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIc,CAAC,EAAE,EAAEd,CAAC,EAAE;IACzB,IAAI/B,CAAC,CAAC+B,CAAC,CAAC,IAAIsG,GAAG,IAAItG,CAAC,IAAIc,CAAC,EACrB,EAAEyF,GAAG,CAAC,KACL;MACD,IAAI,CAACD,GAAG,IAAIC,GAAG,GAAG,CAAC,EAAE;QACjB,OAAOA,GAAG,GAAG,GAAG,EAAEA,GAAG,IAAI,GAAG,EACxB5H,CAAC,CAAC,KAAK,CAAC;QACZ,IAAI4H,GAAG,GAAG,CAAC,EAAE;UACT5H,CAAC,CAAC4H,GAAG,GAAG,EAAE,GAAKA,GAAG,GAAG,EAAE,IAAK,CAAC,GAAI,KAAK,GAAKA,GAAG,GAAG,CAAC,IAAK,CAAC,GAAI,KAAK,CAAC;UAClEA,GAAG,GAAG,CAAC;QACX;MACJ,CAAC,MACI,IAAIA,GAAG,GAAG,CAAC,EAAE;QACd5H,CAAC,CAAC2H,GAAG,CAAC,EAAE,EAAEC,GAAG;QACb,OAAOA,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EACpB5H,CAAC,CAAC,IAAI,CAAC;QACX,IAAI4H,GAAG,GAAG,CAAC,EACP5H,CAAC,CAAG4H,GAAG,GAAG,CAAC,IAAK,CAAC,GAAI,IAAI,CAAC,EAAEA,GAAG,GAAG,CAAC;MAC3C;MACA,OAAOA,GAAG,EAAE,EACR5H,CAAC,CAAC2H,GAAG,CAAC;MACVC,GAAG,GAAG,CAAC;MACPD,GAAG,GAAGrI,CAAC,CAAC+B,CAAC,CAAC;IACd;EACJ;EACA,OAAO,CAACoG,EAAE,CAAC3D,QAAQ,CAAC,CAAC,EAAE4D,GAAG,CAAC,EAAEvF,CAAC,CAAC;AACnC,CAAC;AACD;AACA,IAAI0F,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAEL,EAAE,EAAE;EACzB,IAAIpF,CAAC,GAAG,CAAC;EACT,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoG,EAAE,CAACrF,MAAM,EAAE,EAAEf,CAAC,EAC9BgB,CAAC,IAAIyF,EAAE,CAACzG,CAAC,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC;EACtB,OAAOgB,CAAC;AACZ,CAAC;AACD;AACA;AACA,IAAI0F,KAAK,GAAG,SAAAA,CAAUC,GAAG,EAAEpD,GAAG,EAAEZ,GAAG,EAAE;EACjC;EACA,IAAI7B,CAAC,GAAG6B,GAAG,CAAC5B,MAAM;EAClB,IAAIoB,CAAC,GAAGE,IAAI,CAACkB,GAAG,GAAG,CAAC,CAAC;EACrBoD,GAAG,CAACxE,CAAC,CAAC,GAAGrB,CAAC,GAAG,GAAG;EAChB6F,GAAG,CAACxE,CAAC,GAAG,CAAC,CAAC,GAAGrB,CAAC,KAAK,CAAC;EACpB6F,GAAG,CAACxE,CAAC,GAAG,CAAC,CAAC,GAAGwE,GAAG,CAACxE,CAAC,CAAC,GAAG,GAAG;EACzBwE,GAAG,CAACxE,CAAC,GAAG,CAAC,CAAC,GAAGwE,GAAG,CAACxE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EAC7B,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EACtB2G,GAAG,CAACxE,CAAC,GAAGnC,CAAC,GAAG,CAAC,CAAC,GAAG2C,GAAG,CAAC3C,CAAC,CAAC;EAC3B,OAAO,CAACmC,CAAC,GAAG,CAAC,GAAGrB,CAAC,IAAI,CAAC;AAC1B,CAAC;AACD;AACA,IAAI8F,IAAI,GAAG,SAAAA,CAAUjE,GAAG,EAAEgE,GAAG,EAAEtD,KAAK,EAAEwD,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAElH,EAAE,EAAEmH,EAAE,EAAEC,EAAE,EAAE/D,EAAE,EAAEhB,CAAC,EAAE;EACnE8C,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,EAAE,EAAEmB,KAAK,CAAC;EACtB,EAAEyD,EAAE,CAAC,GAAG,CAAC;EACT,IAAI3G,EAAE,GAAG+E,KAAK,CAAC4B,EAAE,EAAE,EAAE,CAAC;IAAEI,GAAG,GAAG/G,EAAE,CAAC,CAAC,CAAC;IAAEgH,GAAG,GAAGhH,EAAE,CAAC,CAAC,CAAC;EAChD,IAAIG,EAAE,GAAG4E,KAAK,CAAC6B,EAAE,EAAE,EAAE,CAAC;IAAEK,GAAG,GAAG9G,EAAE,CAAC,CAAC,CAAC;IAAE+G,GAAG,GAAG/G,EAAE,CAAC,CAAC,CAAC;EAChD,IAAIgH,EAAE,GAAGnB,EAAE,CAACe,GAAG,CAAC;IAAEK,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAIG,EAAE,GAAGtB,EAAE,CAACiB,GAAG,CAAC;IAAEM,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAIG,MAAM,GAAG,IAAIvI,GAAG,CAAC,EAAE,CAAC;EACxB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,IAAI,CAACxG,MAAM,EAAE,EAAEf,CAAC,EAChC4H,MAAM,CAACL,IAAI,CAACvH,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;EAC1B,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,IAAI,CAAC3G,MAAM,EAAE,EAAEf,CAAC,EAChC4H,MAAM,CAACF,IAAI,CAAC1H,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;EAC1B,IAAI6H,EAAE,GAAG3C,KAAK,CAAC0C,MAAM,EAAE,CAAC,CAAC;IAAEE,GAAG,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;EACpD,IAAIG,IAAI,GAAG,EAAE;EACb,OAAOA,IAAI,GAAG,CAAC,IAAI,CAACF,GAAG,CAACnI,IAAI,CAACqI,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,EAAEA,IAAI,CAC3C;EACJ,IAAIC,IAAI,GAAI/E,EAAE,GAAG,CAAC,IAAK,CAAC;EACxB,IAAIgF,KAAK,GAAG1B,IAAI,CAACM,EAAE,EAAEtF,GAAG,CAAC,GAAGgF,IAAI,CAACO,EAAE,EAAEtF,GAAG,CAAC,GAAG5B,EAAE;EAC9C,IAAIsI,KAAK,GAAG3B,IAAI,CAACM,EAAE,EAAEI,GAAG,CAAC,GAAGV,IAAI,CAACO,EAAE,EAAEK,GAAG,CAAC,GAAGvH,EAAE,GAAG,EAAE,GAAG,CAAC,GAAGmI,IAAI,GAAGxB,IAAI,CAACoB,MAAM,EAAEE,GAAG,CAAC,IAAI,CAAC,GAAGF,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAGA,MAAM,CAAC,EAAE,CAAC,CAAC;EACvI,IAAIK,IAAI,IAAIC,KAAK,IAAID,IAAI,IAAIE,KAAK,EAC9B,OAAOzB,KAAK,CAACC,GAAG,EAAEzE,CAAC,EAAES,GAAG,CAACF,QAAQ,CAACwE,EAAE,EAAEA,EAAE,GAAG/D,EAAE,CAAC,CAAC;EACnD,IAAIO,EAAE,EAAE2E,EAAE,EAAE1E,EAAE,EAAE2E,EAAE;EAClBrD,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,EAAE,CAAC,IAAIiG,KAAK,GAAGD,KAAK,CAAC,CAAC,EAAEhG,CAAC,IAAI,CAAC;EAC1C,IAAIiG,KAAK,GAAGD,KAAK,EAAE;IACfzE,EAAE,GAAG9C,IAAI,CAACuG,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC,EAAEiB,EAAE,GAAGlB,GAAG,EAAExD,EAAE,GAAG/C,IAAI,CAACyG,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC,EAAEgB,EAAE,GAAGjB,GAAG;IAClE,IAAIkB,GAAG,GAAG3H,IAAI,CAACmH,GAAG,EAAEC,IAAI,EAAE,CAAC,CAAC;IAC5B/C,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,EAAEsF,GAAG,GAAG,GAAG,CAAC;IACxBxC,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,GAAG,CAAC,EAAEyF,GAAG,GAAG,CAAC,CAAC;IAC1B3C,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,GAAG,EAAE,EAAE8F,IAAI,GAAG,CAAC,CAAC;IAC5B9F,CAAC,IAAI,EAAE;IACP,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,IAAI,EAAE,EAAEhI,CAAC,EACzBgF,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,GAAG,CAAC,GAAGlC,CAAC,EAAE8H,GAAG,CAACnI,IAAI,CAACK,CAAC,CAAC,CAAC,CAAC;IACvCkC,CAAC,IAAI,CAAC,GAAG8F,IAAI;IACb,IAAIO,IAAI,GAAG,CAAChB,IAAI,EAAEG,IAAI,CAAC;IACvB,KAAK,IAAIc,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAE,EAAEA,EAAE,EAAE;MAC3B,IAAIC,IAAI,GAAGF,IAAI,CAACC,EAAE,CAAC;MACnB,KAAK,IAAIxI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyI,IAAI,CAAC1H,MAAM,EAAE,EAAEf,CAAC,EAAE;QAClC,IAAI0I,GAAG,GAAGD,IAAI,CAACzI,CAAC,CAAC,GAAG,EAAE;QACtBgF,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,EAAEoG,GAAG,CAACI,GAAG,CAAC,CAAC,EAAExG,CAAC,IAAI4F,GAAG,CAACY,GAAG,CAAC;QACtC,IAAIA,GAAG,GAAG,EAAE,EACR1D,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,EAAGuG,IAAI,CAACzI,CAAC,CAAC,KAAK,CAAC,GAAI,GAAG,CAAC,EAAEkC,CAAC,IAAIuG,IAAI,CAACzI,CAAC,CAAC,KAAK,EAAE;MACjE;IACJ;EACJ,CAAC,MACI;IACDyD,EAAE,GAAG/B,GAAG,EAAE0G,EAAE,GAAG5G,GAAG,EAAEkC,EAAE,GAAG9B,GAAG,EAAEyG,EAAE,GAAG5G,GAAG;EAC1C;EACA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgH,EAAE,EAAE,EAAEhH,CAAC,EAAE;IACzB,IAAI6G,IAAI,CAAC7G,CAAC,CAAC,GAAG,GAAG,EAAE;MACf,IAAI0I,GAAG,GAAI7B,IAAI,CAAC7G,CAAC,CAAC,KAAK,EAAE,GAAI,EAAE;MAC/BiF,OAAO,CAAC0B,GAAG,EAAEzE,CAAC,EAAEuB,EAAE,CAACiF,GAAG,GAAG,GAAG,CAAC,CAAC,EAAExG,CAAC,IAAIkG,EAAE,CAACM,GAAG,GAAG,GAAG,CAAC;MAClD,IAAIA,GAAG,GAAG,CAAC,EACP1D,KAAK,CAAC2B,GAAG,EAAEzE,CAAC,EAAG2E,IAAI,CAAC7G,CAAC,CAAC,KAAK,EAAE,GAAI,EAAE,CAAC,EAAEkC,CAAC,IAAIzC,IAAI,CAACiJ,GAAG,CAAC;MACxD,IAAIC,GAAG,GAAG9B,IAAI,CAAC7G,CAAC,CAAC,GAAG,EAAE;MACtBiF,OAAO,CAAC0B,GAAG,EAAEzE,CAAC,EAAEwB,EAAE,CAACiF,GAAG,CAAC,CAAC,EAAEzG,CAAC,IAAImG,EAAE,CAACM,GAAG,CAAC;MACtC,IAAIA,GAAG,GAAG,CAAC,EACP1D,OAAO,CAAC0B,GAAG,EAAEzE,CAAC,EAAG2E,IAAI,CAAC7G,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI,CAAC,EAAEkC,CAAC,IAAIxC,IAAI,CAACiJ,GAAG,CAAC;IAC/D,CAAC,MACI;MACD1D,OAAO,CAAC0B,GAAG,EAAEzE,CAAC,EAAEuB,EAAE,CAACoD,IAAI,CAAC7G,CAAC,CAAC,CAAC,CAAC,EAAEkC,CAAC,IAAIkG,EAAE,CAACvB,IAAI,CAAC7G,CAAC,CAAC,CAAC;IAClD;EACJ;EACAiF,OAAO,CAAC0B,GAAG,EAAEzE,CAAC,EAAEuB,EAAE,CAAC,GAAG,CAAC,CAAC;EACxB,OAAOvB,CAAC,GAAGkG,EAAE,CAAC,GAAG,CAAC;AACtB,CAAC;AACD;AACA,IAAIQ,GAAG,GAAG,aAAc,IAAIrJ,GAAG,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5G;AACA,IAAIsJ,EAAE,GAAG,aAAc,IAAI1J,EAAE,CAAC,CAAC,CAAC;AAChC;AACA,IAAI2J,IAAI,GAAG,SAAAA,CAAUnG,GAAG,EAAEoG,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAE;EACjD,IAAIrI,CAAC,GAAG6B,GAAG,CAAC5B,MAAM;EAClB,IAAIoB,CAAC,GAAG,IAAIhD,EAAE,CAAC8J,GAAG,GAAGnI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGsC,IAAI,CAACgG,KAAK,CAACtI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAGoI,IAAI,CAAC;EAC/D;EACA,IAAIvK,CAAC,GAAGwD,CAAC,CAACM,QAAQ,CAACwG,GAAG,EAAE9G,CAAC,CAACpB,MAAM,GAAGmI,IAAI,CAAC;EACxC,IAAI3F,GAAG,GAAG,CAAC;EACX,IAAI,CAACwF,GAAG,IAAIjI,CAAC,GAAG,CAAC,EAAE;IACf,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIc,CAAC,EAAEd,CAAC,IAAI,KAAK,EAAE;MAChC;MACA,IAAIlB,CAAC,GAAGkB,CAAC,GAAG,KAAK;MACjB,IAAIlB,CAAC,GAAGgC,CAAC,EAAE;QACP;QACAyC,GAAG,GAAGmD,KAAK,CAAC/H,CAAC,EAAE4E,GAAG,EAAEZ,GAAG,CAACF,QAAQ,CAACzC,CAAC,EAAElB,CAAC,CAAC,CAAC;MAC3C,CAAC,MACI;QACD;QACAH,CAAC,CAACqB,CAAC,CAAC,GAAGmJ,GAAG;QACV5F,GAAG,GAAGmD,KAAK,CAAC/H,CAAC,EAAE4E,GAAG,EAAEZ,GAAG,CAACF,QAAQ,CAACzC,CAAC,EAAEc,CAAC,CAAC,CAAC;MAC3C;IACJ;EACJ,CAAC,MACI;IACD,IAAIuI,GAAG,GAAGT,GAAG,CAACG,GAAG,GAAG,CAAC,CAAC;IACtB,IAAIxG,CAAC,GAAG8G,GAAG,KAAK,EAAE;MAAEpL,CAAC,GAAGoL,GAAG,GAAG,IAAI;IAClC,IAAIC,KAAK,GAAG,CAAC,CAAC,IAAIN,IAAI,IAAI,CAAC;IAC3B;IACA,IAAIO,IAAI,GAAG,IAAIlK,GAAG,CAAC,KAAK,CAAC;MAAEmK,IAAI,GAAG,IAAInK,GAAG,CAACiK,KAAK,GAAG,CAAC,CAAC;IACpD,IAAIG,KAAK,GAAGrG,IAAI,CAACsG,IAAI,CAACV,IAAI,GAAG,CAAC,CAAC;MAAEW,KAAK,GAAG,CAAC,GAAGF,KAAK;IAClD,IAAIG,GAAG,GAAG,SAAAA,CAAU5J,CAAC,EAAE;MAAE,OAAO,CAAC2C,GAAG,CAAC3C,CAAC,CAAC,GAAI2C,GAAG,CAAC3C,CAAC,GAAG,CAAC,CAAC,IAAIyJ,KAAM,GAAI9G,GAAG,CAAC3C,CAAC,GAAG,CAAC,CAAC,IAAI2J,KAAM,IAAIL,KAAK;IAAE,CAAC;IACnG;IACA;IACA,IAAIzC,IAAI,GAAG,IAAItH,GAAG,CAAC,KAAK,CAAC;IACzB;IACA,IAAIuH,EAAE,GAAG,IAAIzH,GAAG,CAAC,GAAG,CAAC;MAAE0H,EAAE,GAAG,IAAI1H,GAAG,CAAC,EAAE,CAAC;IACvC;IACA,IAAIwK,IAAI,GAAG,CAAC;MAAEhK,EAAE,GAAG,CAAC;MAAEG,CAAC,GAAG,CAAC;MAAEgH,EAAE,GAAG,CAAC;MAAE8C,EAAE,GAAG,CAAC;MAAE7C,EAAE,GAAG,CAAC;IACnD,OAAOjH,CAAC,GAAGc,CAAC,EAAE,EAAEd,CAAC,EAAE;MACf;MACA,IAAI+J,EAAE,GAAGH,GAAG,CAAC5J,CAAC,CAAC;MACf;MACA,IAAIgK,IAAI,GAAGhK,CAAC,GAAG,KAAK;MACpB;MACA,IAAIiK,KAAK,GAAGT,IAAI,CAACO,EAAE,CAAC;MACpBR,IAAI,CAACS,IAAI,CAAC,GAAGC,KAAK;MAClBT,IAAI,CAACO,EAAE,CAAC,GAAGC,IAAI;MACf;MACA;MACA,IAAIF,EAAE,IAAI9J,CAAC,EAAE;QACT;QACA,IAAIkK,GAAG,GAAGpJ,CAAC,GAAGd,CAAC;QACf,IAAI,CAAC6J,IAAI,GAAG,IAAI,IAAI7C,EAAE,GAAG,KAAK,KAAKkD,GAAG,GAAG,GAAG,EAAE;UAC1C3G,GAAG,GAAGqD,IAAI,CAACjE,GAAG,EAAEhE,CAAC,EAAE,CAAC,EAAEkI,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAElH,EAAE,EAAEmH,EAAE,EAAEC,EAAE,EAAEjH,CAAC,GAAGiH,EAAE,EAAE1D,GAAG,CAAC;UAC5DyD,EAAE,GAAG6C,IAAI,GAAGhK,EAAE,GAAG,CAAC,EAAEoH,EAAE,GAAGjH,CAAC;UAC1B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EACxB4G,EAAE,CAAC5G,CAAC,CAAC,GAAG,CAAC;UACb,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EACvB6G,EAAE,CAAC7G,CAAC,CAAC,GAAG,CAAC;QACjB;QACA;QACA,IAAIc,CAAC,GAAG,CAAC;UAAEiB,CAAC,GAAG,CAAC;UAAEkI,IAAI,GAAGlM,CAAC;UAAEmM,GAAG,GAAIJ,IAAI,GAAGC,KAAK,GAAI,KAAK;QACxD,IAAIC,GAAG,GAAG,CAAC,IAAIH,EAAE,IAAIH,GAAG,CAAC5J,CAAC,GAAGoK,GAAG,CAAC,EAAE;UAC/B,IAAIC,IAAI,GAAGjH,IAAI,CAACkH,GAAG,CAAC/H,CAAC,EAAE2H,GAAG,CAAC,GAAG,CAAC;UAC/B,IAAIK,IAAI,GAAGnH,IAAI,CAACkH,GAAG,CAAC,KAAK,EAAEtK,CAAC,CAAC;UAC7B;UACA;UACA,IAAIwK,EAAE,GAAGpH,IAAI,CAACkH,GAAG,CAAC,GAAG,EAAEJ,GAAG,CAAC;UAC3B,OAAOE,GAAG,IAAIG,IAAI,IAAI,EAAEJ,IAAI,IAAIH,IAAI,IAAIC,KAAK,EAAE;YAC3C,IAAItH,GAAG,CAAC3C,CAAC,GAAGgB,CAAC,CAAC,IAAI2B,GAAG,CAAC3C,CAAC,GAAGgB,CAAC,GAAGoJ,GAAG,CAAC,EAAE;cAChC,IAAIK,EAAE,GAAG,CAAC;cACV,OAAOA,EAAE,GAAGD,EAAE,IAAI7H,GAAG,CAAC3C,CAAC,GAAGyK,EAAE,CAAC,IAAI9H,GAAG,CAAC3C,CAAC,GAAGyK,EAAE,GAAGL,GAAG,CAAC,EAAE,EAAEK,EAAE,CACpD;cACJ,IAAIA,EAAE,GAAGzJ,CAAC,EAAE;gBACRA,CAAC,GAAGyJ,EAAE,EAAExI,CAAC,GAAGmI,GAAG;gBACf;gBACA,IAAIK,EAAE,GAAGJ,IAAI,EACT;gBACJ;gBACA;gBACA;gBACA,IAAIK,GAAG,GAAGtH,IAAI,CAACkH,GAAG,CAACF,GAAG,EAAEK,EAAE,GAAG,CAAC,CAAC;gBAC/B,IAAIE,EAAE,GAAG,CAAC;gBACV,KAAK,IAAIzK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwK,GAAG,EAAE,EAAExK,CAAC,EAAE;kBAC1B,IAAI0K,EAAE,GAAI5K,CAAC,GAAGoK,GAAG,GAAGlK,CAAC,GAAG,KAAK,GAAI,KAAK;kBACtC,IAAI2K,GAAG,GAAGtB,IAAI,CAACqB,EAAE,CAAC;kBAClB,IAAIhK,EAAE,GAAIgK,EAAE,GAAGC,GAAG,GAAG,KAAK,GAAI,KAAK;kBACnC,IAAIjK,EAAE,GAAG+J,EAAE,EACPA,EAAE,GAAG/J,EAAE,EAAEqJ,KAAK,GAAGW,EAAE;gBAC3B;cACJ;YACJ;YACA;YACAZ,IAAI,GAAGC,KAAK,EAAEA,KAAK,GAAGV,IAAI,CAACS,IAAI,CAAC;YAChCI,GAAG,IAAKJ,IAAI,GAAGC,KAAK,GAAG,KAAK,GAAI,KAAK;UACzC;QACJ;QACA;QACA,IAAIhI,CAAC,EAAE;UACH;UACA;UACA4E,IAAI,CAACG,EAAE,EAAE,CAAC,GAAG,SAAS,GAAI3G,KAAK,CAACW,CAAC,CAAC,IAAI,EAAG,GAAGR,KAAK,CAACyB,CAAC,CAAC;UACpD,IAAI6I,GAAG,GAAGzK,KAAK,CAACW,CAAC,CAAC,GAAG,EAAE;YAAE+J,GAAG,GAAGvK,KAAK,CAACyB,CAAC,CAAC,GAAG,EAAE;UAC5CpC,EAAE,IAAIJ,IAAI,CAACqL,GAAG,CAAC,GAAGpL,IAAI,CAACqL,GAAG,CAAC;UAC3B,EAAEjE,EAAE,CAAC,GAAG,GAAGgE,GAAG,CAAC;UACf,EAAE/D,EAAE,CAACgE,GAAG,CAAC;UACTjB,EAAE,GAAG9J,CAAC,GAAGgB,CAAC;UACV,EAAE6I,IAAI;QACV,CAAC,MACI;UACDhD,IAAI,CAACG,EAAE,EAAE,CAAC,GAAGrE,GAAG,CAAC3C,CAAC,CAAC;UACnB,EAAE8G,EAAE,CAACnE,GAAG,CAAC3C,CAAC,CAAC,CAAC;QAChB;MACJ;IACJ;IACAuD,GAAG,GAAGqD,IAAI,CAACjE,GAAG,EAAEhE,CAAC,EAAEwK,GAAG,EAAEtC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAElH,EAAE,EAAEmH,EAAE,EAAEC,EAAE,EAAEjH,CAAC,GAAGiH,EAAE,EAAE1D,GAAG,CAAC;IAC9D;IACA,IAAI,CAAC4F,GAAG,EACJ5F,GAAG,GAAGmD,KAAK,CAAC/H,CAAC,EAAE4E,GAAG,EAAEsF,EAAE,CAAC;EAC/B;EACA,OAAOvG,GAAG,CAACH,CAAC,EAAE,CAAC,EAAE8G,GAAG,GAAG5G,IAAI,CAACkB,GAAG,CAAC,GAAG2F,IAAI,CAAC;AAC5C,CAAC;AACD;AACA,IAAI8B,IAAI,GAAG,aAAe,YAAY;EAClC,IAAIlH,CAAC,GAAG,IAAIvE,GAAG,CAAC,GAAG,CAAC;EACpB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;IAC1B,IAAI/B,CAAC,GAAG+B,CAAC;MAAEiL,CAAC,GAAG,CAAC;IAChB,OAAO,EAAEA,CAAC,EACNhN,CAAC,GAAG,CAAEA,CAAC,GAAG,CAAC,IAAK,UAAU,IAAKA,CAAC,KAAK,CAAE;IAC3C6F,CAAC,CAAC9D,CAAC,CAAC,GAAG/B,CAAC;EACZ;EACA,OAAO6F,CAAC;AACZ,CAAC,CAAE,CAAC;AACJ;AACA,IAAIoH,GAAG,GAAG,SAAAA,CAAA,EAAY;EAClB,IAAIjN,CAAC,GAAG,UAAU;EAClB,OAAO;IACHiE,CAAC,EAAE,SAAAA,CAAUD,CAAC,EAAE;MACZ;MACA,IAAIkJ,EAAE,GAAGlN,CAAC;MACV,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,CAAC,CAAClB,MAAM,EAAE,EAAEf,CAAC,EAC7BmL,EAAE,GAAGH,IAAI,CAAEG,EAAE,GAAG,GAAG,GAAIlJ,CAAC,CAACjC,CAAC,CAAC,CAAC,GAAImL,EAAE,KAAK,CAAE;MAC7ClN,CAAC,GAAGkN,EAAE;IACV,CAAC;IACDlJ,CAAC,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAOhE,CAAC,GAAG,UAAU;IAAE;EAC5C,CAAC;AACL,CAAC;AACD;AACA,IAAImN,KAAK,GAAG,SAAAA,CAAA,EAAY;EACpB,IAAIrJ,CAAC,GAAG,CAAC;IAAEhC,CAAC,GAAG,CAAC;EAChB,OAAO;IACHmC,CAAC,EAAE,SAAAA,CAAUD,CAAC,EAAE;MACZ;MACA,IAAIM,CAAC,GAAGR,CAAC;QAAER,CAAC,GAAGxB,CAAC;MAChB,IAAIiB,CAAC,GAAGiB,CAAC,CAAClB,MAAM;MAChB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIgB,CAAC,GAAG;QACrB,IAAIlC,CAAC,GAAGsE,IAAI,CAACkH,GAAG,CAACtK,CAAC,GAAG,IAAI,EAAEgB,CAAC,CAAC;QAC7B,OAAOhB,CAAC,GAAGlB,CAAC,EAAE,EAAEkB,CAAC,EACbuC,CAAC,IAAIN,CAAC,CAACjC,CAAC,CAAC,EAAEuB,CAAC,IAAIgB,CAAC;QACrBA,CAAC,IAAI,KAAK,EAAEhB,CAAC,IAAI,KAAK;MAC1B;MACAQ,CAAC,GAAGQ,CAAC,EAAExC,CAAC,GAAGwB,CAAC;IAChB,CAAC;IACDU,CAAC,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAEF,CAAC,KAAK,CAAC,IAAK,EAAE,GAAG,CAAChC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAIA,CAAC,KAAK,CAAE,IAAI,CAAC,CAACgC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC;IAAE;EACpG,CAAC;AACL,CAAC;AACD;AACA;AACA,IAAIsJ,IAAI,GAAG,SAAAA,CAAU1I,GAAG,EAAE0G,GAAG,EAAEJ,GAAG,EAAEC,IAAI,EAAErG,EAAE,EAAE;EAC1C,OAAOiG,IAAI,CAACnG,GAAG,EAAE0G,GAAG,CAACiC,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGjC,GAAG,CAACiC,KAAK,EAAEjC,GAAG,CAACkC,GAAG,IAAI,IAAI,GAAGnI,IAAI,CAACsG,IAAI,CAACtG,IAAI,CAACtB,GAAG,CAAC,CAAC,EAAEsB,IAAI,CAACkH,GAAG,CAAC,EAAE,EAAElH,IAAI,CAACoI,GAAG,CAAC7I,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAI,EAAE,GAAGsI,GAAG,CAACkC,GAAI,EAAEtC,GAAG,EAAEC,IAAI,EAAE,CAACrG,EAAE,CAAC;AAC5K,CAAC;AACD;AACA,IAAI4I,GAAG,GAAG,SAAAA,CAAU1J,CAAC,EAAEhC,CAAC,EAAE;EACtB,IAAIoC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAI8I,CAAC,IAAIlJ,CAAC,EACXI,CAAC,CAAC8I,CAAC,CAAC,GAAGlJ,CAAC,CAACkJ,CAAC,CAAC;EACf,KAAK,IAAIA,CAAC,IAAIlL,CAAC,EACXoC,CAAC,CAAC8I,CAAC,CAAC,GAAGlL,CAAC,CAACkL,CAAC,CAAC;EACf,OAAO9I,CAAC;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuJ,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAEC,EAAE,EAAE;EAChC,IAAIrH,EAAE,GAAGmH,EAAE,CAAC,CAAC;EACb,IAAI9I,EAAE,GAAG8I,EAAE,CAACG,QAAQ,CAAC,CAAC;EACtB,IAAIC,EAAE,GAAGlJ,EAAE,CAACwC,KAAK,CAACxC,EAAE,CAACmJ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEnJ,EAAE,CAACoJ,WAAW,CAAC,GAAG,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACxF,KAAK,IAAInM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,EAAE,CAACzD,MAAM,EAAE,EAAEf,CAAC,EAAE;IAChC,IAAIsB,CAAC,GAAGkD,EAAE,CAACxE,CAAC,CAAC;MAAEiL,CAAC,GAAGc,EAAE,CAAC/L,CAAC,CAAC;IACxB,IAAI,OAAOsB,CAAC,IAAI,UAAU,EAAE;MACxBsK,KAAK,IAAI,GAAG,GAAGX,CAAC,GAAG,GAAG;MACtB,IAAImB,IAAI,GAAG9K,CAAC,CAACwK,QAAQ,CAAC,CAAC;MACvB,IAAIxK,CAAC,CAAC+K,SAAS,EAAE;QACb;QACA,IAAID,IAAI,CAACJ,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE;UACrC,IAAIM,KAAK,GAAGF,IAAI,CAACJ,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC;UACpCJ,KAAK,IAAIQ,IAAI,CAAC/G,KAAK,CAACiH,KAAK,EAAEF,IAAI,CAACJ,OAAO,CAAC,GAAG,EAAEM,KAAK,CAAC,CAAC;QACxD,CAAC,MACI;UACDV,KAAK,IAAIQ,IAAI;UACb,KAAK,IAAItI,CAAC,IAAIxC,CAAC,CAAC+K,SAAS,EACrBT,KAAK,IAAI,GAAG,GAAGX,CAAC,GAAG,aAAa,GAAGnH,CAAC,GAAG,GAAG,GAAGxC,CAAC,CAAC+K,SAAS,CAACvI,CAAC,CAAC,CAACgI,QAAQ,CAAC,CAAC;QAC9E;MACJ,CAAC,MAEGF,KAAK,IAAIQ,IAAI;IACrB,CAAC,MAEGP,EAAE,CAACZ,CAAC,CAAC,GAAG3J,CAAC;EACjB;EACA,OAAO,CAACsK,KAAK,EAAEC,EAAE,CAAC;AACtB,CAAC;AACD,IAAIU,EAAE,GAAG,EAAE;AACX;AACA,IAAIC,IAAI,GAAG,SAAAA,CAAUlL,CAAC,EAAE;EACpB,IAAI2C,EAAE,GAAG,EAAE;EACX,KAAK,IAAIgH,CAAC,IAAI3J,CAAC,EAAE;IACb,IAAIA,CAAC,CAAC2J,CAAC,CAAC,YAAY9L,EAAE,IAAImC,CAAC,CAAC2J,CAAC,CAAC,YAAY5L,GAAG,IAAIiC,CAAC,CAAC2J,CAAC,CAAC,YAAY1L,GAAG,EAChE0E,EAAE,CAACkB,IAAI,CAAC,CAAC7D,CAAC,CAAC2J,CAAC,CAAC,GAAG,IAAI3J,CAAC,CAAC2J,CAAC,CAAC,CAACwB,WAAW,CAACnL,CAAC,CAAC2J,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAAC;EAC3D;EACA,OAAOzI,EAAE;AACb,CAAC;AACD;AACA,IAAI0I,IAAI,GAAG,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE3O,EAAE,EAAEG,EAAE,EAAE;EACpC,IAAI8B,EAAE;EACN,IAAI,CAACoM,EAAE,CAACrO,EAAE,CAAC,EAAE;IACT,IAAI0N,KAAK,GAAG,EAAE;MAAEkB,IAAI,GAAG,CAAC,CAAC;MAAEvL,CAAC,GAAGqL,GAAG,CAAC7L,MAAM,GAAG,CAAC;IAC7C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,CAAC,EAAE,EAAEvB,CAAC,EACtBG,EAAE,GAAGuL,IAAI,CAACkB,GAAG,CAAC5M,CAAC,CAAC,EAAE4L,KAAK,EAAEkB,IAAI,CAAC,EAAElB,KAAK,GAAGzL,EAAE,CAAC,CAAC,CAAC,EAAE2M,IAAI,GAAG3M,EAAE,CAAC,CAAC,CAAC;IAC/DoM,EAAE,CAACrO,EAAE,CAAC,GAAGwN,IAAI,CAACkB,GAAG,CAACrL,CAAC,CAAC,EAAEqK,KAAK,EAAEkB,IAAI,CAAC;EACtC;EACA,IAAIjB,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC,EAAEc,EAAE,CAACrO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,OAAOF,EAAE,CAACuO,EAAE,CAACrO,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,yEAAyE,GAAG2O,IAAI,CAACf,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE5N,EAAE,EAAE2N,EAAE,EAAEW,IAAI,CAACX,EAAE,CAAC,EAAExN,EAAE,CAAC;AAClJ,CAAC;AACD;AACA,IAAI0O,MAAM,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAAC5N,EAAE,EAAEE,GAAG,EAAEE,GAAG,EAAEE,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAES,EAAE,EAAEG,EAAE,EAAEoB,IAAI,EAAEE,IAAI,EAAEpB,GAAG,EAAEE,IAAI,EAAEmB,GAAG,EAAEE,IAAI,EAAEI,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEI,KAAK,EAAEsK,WAAW,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAAE,CAAC;AAChK,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAAChO,EAAE,EAAEE,GAAG,EAAEE,GAAG,EAAEE,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEU,KAAK,EAAEG,KAAK,EAAEkB,GAAG,EAAEF,GAAG,EAAEI,GAAG,EAAEH,GAAG,EAAEhB,GAAG,EAAEmI,GAAG,EAAEC,EAAE,EAAElI,IAAI,EAAEqE,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEW,EAAE,EAAEM,EAAE,EAAEK,IAAI,EAAEE,KAAK,EAAEE,IAAI,EAAEvE,IAAI,EAAEC,GAAG,EAAEwG,IAAI,EAAEuC,IAAI,EAAE+B,WAAW,EAAEH,GAAG,CAAC;AAAE,CAAC;AACrN;AACA,IAAII,GAAG,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEtC,GAAG,EAAEF,IAAI,CAAC;AAAE,CAAC;AAChE;AACA,IAAIyC,IAAI,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,EAAEC,GAAG,CAAC;AAAE,CAAC;AAC7C;AACA,IAAIC,GAAG,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,EAAEL,MAAM,EAAEpC,KAAK,CAAC;AAAE,CAAC;AACtD;AACA,IAAI0C,IAAI,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,CAACC,GAAG,CAAC;AAAE,CAAC;AACxC;AACA,IAAId,GAAG,GAAG,SAAAA,CAAU9O,GAAG,EAAE;EAAE,OAAOe,WAAW,CAACf,GAAG,EAAE,CAACA,GAAG,CAACuO,MAAM,CAAC,CAAC;AAAE,CAAC;AACnE;AACA,IAAIQ,GAAG,GAAG,SAAAA,CAAU/K,CAAC,EAAE;EAAE,OAAOA,CAAC,IAAIA,CAAC,CAAC6L,IAAI,IAAI,IAAI7O,EAAE,CAACgD,CAAC,CAAC6L,IAAI,CAAC;AAAE,CAAC;AAChE;AACA,IAAIC,KAAK,GAAG,SAAAA,CAAUtL,GAAG,EAAEuL,IAAI,EAAEtB,GAAG,EAAEC,IAAI,EAAE3O,EAAE,EAAEG,EAAE,EAAE;EAChD,IAAIM,CAAC,GAAGgO,IAAI,CAACC,GAAG,EAAEC,IAAI,EAAE3O,EAAE,EAAE,UAAUiQ,GAAG,EAAExL,GAAG,EAAE;IAC5ChE,CAAC,CAACyP,SAAS,CAAC,CAAC;IACb/P,EAAE,CAAC8P,GAAG,EAAExL,GAAG,CAAC;EAChB,CAAC,CAAC;EACF,IAAI,CAACuL,IAAI,CAACG,OAAO,EACb1L,GAAG,GAAG,IAAIxD,EAAE,CAACwD,GAAG,CAAC;EACrBhE,CAAC,CAACO,WAAW,CAAC,CAACyD,GAAG,EAAEuL,IAAI,CAAC,EAAE,CAACvL,GAAG,CAAC+J,MAAM,CAAC,CAAC;EACxC,OAAO,YAAY;IAAE/N,CAAC,CAACyP,SAAS,CAAC,CAAC;EAAE,CAAC;AACzC,CAAC;AACD;AACA,IAAIE,KAAK,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACxBA,IAAI,CAACC,MAAM,GAAG,UAAU7L,GAAG,EAAEU,KAAK,EAAE;IAAE,OAAOnE,WAAW,CAAC,CAACyD,GAAG,EAAEU,KAAK,CAAC,EAAE,CAACV,GAAG,CAAC+J,MAAM,CAAC,CAAC;EAAE,CAAC;EACvF,OAAO,UAAU+B,EAAE,EAAE;IAAE,OAAOF,IAAI,CAACpJ,IAAI,CAACsJ,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC;AACtE,CAAC;AACD;AACA,IAAIyP,QAAQ,GAAG,SAAAA,CAAU9B,GAAG,EAAE2B,IAAI,EAAEL,IAAI,EAAErB,IAAI,EAAE3O,EAAE,EAAE;EAChD,IAAI4F,CAAC;EACL,IAAInF,CAAC,GAAGgO,IAAI,CAACC,GAAG,EAAEC,IAAI,EAAE3O,EAAE,EAAE,UAAUiQ,GAAG,EAAExL,GAAG,EAAE;IAC5C,IAAIwL,GAAG,EACHxP,CAAC,CAACyP,SAAS,CAAC,CAAC,EAAEG,IAAI,CAACC,MAAM,CAACG,IAAI,CAACJ,IAAI,EAAEJ,GAAG,CAAC,CAAC,KAC1C;MACD,IAAIxL,GAAG,CAAC,CAAC,CAAC,EACNhE,CAAC,CAACyP,SAAS,CAAC,CAAC;MACjBG,IAAI,CAACC,MAAM,CAACG,IAAI,CAACJ,IAAI,EAAEJ,GAAG,EAAExL,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C;EACJ,CAAC,CAAC;EACFhE,CAAC,CAACO,WAAW,CAACgP,IAAI,CAAC;EACnBK,IAAI,CAACpJ,IAAI,GAAG,UAAUlD,CAAC,EAAEqB,CAAC,EAAE;IACxB,IAAIQ,CAAC,EACD,MAAM,iBAAiB;IAC3B,IAAI,CAACyK,IAAI,CAACC,MAAM,EACZ,MAAM,mBAAmB;IAC7B7P,CAAC,CAACO,WAAW,CAAC,CAAC+C,CAAC,EAAE6B,CAAC,GAAGR,CAAC,CAAC,EAAE,CAACrB,CAAC,CAACyK,MAAM,CAAC,CAAC;EACzC,CAAC;EACD6B,IAAI,CAACH,SAAS,GAAG,YAAY;IAAEzP,CAAC,CAACyP,SAAS,CAAC,CAAC;EAAE,CAAC;AACnD,CAAC;AACD;AACA,IAAIQ,EAAE,GAAG,SAAAA,CAAU3M,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAOkC,CAAC,CAAClC,CAAC,CAAC,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE;AAAE,CAAC;AAC3D;AACA,IAAI8O,EAAE,GAAG,SAAAA,CAAU5M,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAO,CAACkC,CAAC,CAAClC,CAAC,CAAC,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,GAAIkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,IAAI,CAACkC,CAAC,CAAClC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC;AACvG;AACA,IAAIyN,MAAM,GAAG,SAAAA,CAAUvL,CAAC,EAAElC,CAAC,EAAEuB,CAAC,EAAE;EAC5B,OAAOA,CAAC,EAAE,EAAEvB,CAAC,EACTkC,CAAC,CAAClC,CAAC,CAAC,GAAGuB,CAAC,EAAEA,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD;AACA,IAAIgM,GAAG,GAAG,SAAAA,CAAUrP,CAAC,EAAEkE,CAAC,EAAE;EACtB,IAAIwJ,EAAE,GAAGxJ,CAAC,CAAC2M,QAAQ;EACnB7Q,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAGkE,CAAC,CAACmJ,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGnJ,CAAC,CAACmJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAErN,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1F,IAAIkE,CAAC,CAAC4M,KAAK,IAAI,CAAC,EACZvB,MAAM,CAACvP,CAAC,EAAE,CAAC,EAAEmF,IAAI,CAACgG,KAAK,CAAC,IAAI4F,IAAI,CAAC7M,CAAC,CAAC4M,KAAK,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EACpE,IAAItD,EAAE,EAAE;IACJ1N,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACR,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI2L,EAAE,CAAC5K,MAAM,EAAE,EAAEf,CAAC,EAC/B/B,CAAC,CAAC+B,CAAC,GAAG,EAAE,CAAC,GAAG2L,EAAE,CAACuD,UAAU,CAAClP,CAAC,CAAC;EACpC;AACJ,CAAC;AACD;AACA;AACA,IAAI0N,GAAG,GAAG,SAAAA,CAAUzL,CAAC,EAAE;EACnB,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EACtC,MAAM,mBAAmB;EAC7B,IAAIkN,GAAG,GAAGlN,CAAC,CAAC,CAAC,CAAC;EACd,IAAIY,EAAE,GAAG,EAAE;EACX,IAAIsM,GAAG,GAAG,CAAC,EACPtM,EAAE,IAAIZ,CAAC,CAAC,EAAE,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;EAClC,KAAK,IAAImN,EAAE,GAAG,CAACD,GAAG,IAAI,CAAC,GAAG,CAAC,KAAKA,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAACnN,CAAC,CAACY,EAAE,EAAE,CAAC,CACjE;EACJ,OAAOA,EAAE,IAAIsM,GAAG,GAAG,CAAC,CAAC;AACzB,CAAC;AACD;AACA,IAAIxB,GAAG,GAAG,SAAAA,CAAU1L,CAAC,EAAE;EACnB,IAAIjB,CAAC,GAAGiB,CAAC,CAAClB,MAAM;EAChB,OAAO,CAACkB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAGiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAK,CAAC,IAAIiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAE;AAC/E,CAAC;AACD;AACA,IAAIuM,IAAI,GAAG,SAAAA,CAAUpL,CAAC,EAAE;EAAE,OAAO,EAAE,IAAKA,CAAC,CAAC2M,QAAQ,IAAK3M,CAAC,CAAC2M,QAAQ,CAAC/N,MAAM,GAAG,CAAE,IAAK,CAAC,CAAC;AAAE,CAAC;AACvF;AACA,IAAI8M,GAAG,GAAG,SAAAA,CAAU5P,CAAC,EAAEkE,CAAC,EAAE;EACtB,IAAIkN,EAAE,GAAGlN,CAAC,CAACmJ,KAAK;IAAElL,EAAE,GAAGiP,EAAE,IAAI,CAAC,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGA,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACjEpR,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAImC,EAAE,IAAI,CAAC,IAAKA,EAAE,GAAI,EAAE,GAAG,CAAC,GAAGA,EAAE,GAAI,CAAC,CAAC;AAC3D,CAAC;AACD;AACA,IAAI2N,GAAG,GAAG,SAAAA,CAAU9L,CAAC,EAAE;EACnB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,EACjE,MAAM,mBAAmB;EAC7B,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EACT,MAAM,sDAAsD;AACpE,CAAC;AACD,SAASqN,YAAYA,CAACpB,IAAI,EAAE7P,EAAE,EAAE;EAC5B,IAAI,CAACA,EAAE,IAAI,OAAO6P,IAAI,IAAI,UAAU,EAChC7P,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,CAACM,MAAM,GAAGnQ,EAAE;EAChB,OAAO6P,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,IAAIqB,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACrB,IAAI,EAAE7P,EAAE,EAAE;IACvB,IAAI,CAACA,EAAE,IAAI,OAAO6P,IAAI,IAAI,UAAU,EAChC7P,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;IACxB,IAAI,CAACM,MAAM,GAAGnQ,EAAE;IAChB,IAAI,CAAC8D,CAAC,GAAG+L,IAAI,IAAI,CAAC,CAAC;EACvB;EACAqB,OAAO,CAAClD,SAAS,CAACnK,CAAC,GAAG,UAAUjE,CAAC,EAAEqF,CAAC,EAAE;IAClC,IAAI,CAACkL,MAAM,CAACnD,IAAI,CAACpN,CAAC,EAAE,IAAI,CAACkE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACmB,CAAC,CAAC,EAAEA,CAAC,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIiM,OAAO,CAAClD,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAC7C,IAAI,IAAI,CAACpB,CAAC,EACN,MAAM,iBAAiB;IAC3B,IAAI,CAAC,IAAI,CAACuM,MAAM,EACZ,MAAM,mBAAmB;IAC7B,IAAI,CAACvM,CAAC,GAAGoB,KAAK;IACd,IAAI,CAACnB,CAAC,CAACsN,KAAK,EAAEnM,KAAK,IAAI,KAAK,CAAC;EACjC,CAAC;EACD,OAAOkM,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAASA,OAAO;AAChB;AACA;AACA;AACA,IAAIE,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACvB,IAAI,EAAE7P,EAAE,EAAE;IAC5BqQ,QAAQ,CAAC,CACLvB,KAAK,EACL,YAAY;MAAE,OAAO,CAACmB,KAAK,EAAEiB,OAAO,CAAC;IAAE,CAAC,CAC3C,EAAE,IAAI,EAAED,YAAY,CAACX,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE7P,EAAE,CAAC,EAAE,UAAUoQ,EAAE,EAAE;MACtD,IAAIF,IAAI,GAAG,IAAIgB,OAAO,CAACd,EAAE,CAACxP,IAAI,CAAC;MAC/BD,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAOkB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,OAAO,SAASC,OAAOA,CAACzQ,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACpC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO4P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBf,KAAK,CACR,EAAE,UAAUsB,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACG,WAAW,CAACqB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+O,WAAWA,CAACnO,IAAI,EAAEiP,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAC,CAAC;EAAE;EAClC,OAAO7C,IAAI,CAACpM,IAAI,EAAEiP,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA,IAAIyB,OAAO,GAAG,aAAe,YAAY;EACrC;AACJ;AACA;AACA;EACI,SAASA,OAAOA,CAACtR,EAAE,EAAE;IACjB,IAAI,CAACyC,CAAC,GAAG,CAAC,CAAC;IACX,IAAI,CAACoB,CAAC,GAAG,IAAI/C,EAAE,CAAC,CAAC,CAAC;IAClB,IAAI,CAACqP,MAAM,GAAGnQ,EAAE;EACpB;EACAsR,OAAO,CAACtD,SAAS,CAACvN,CAAC,GAAG,UAAUb,CAAC,EAAE;IAC/B,IAAI,IAAI,CAACgE,CAAC,EACN,MAAM,iBAAiB;IAC3B,IAAI,CAAC,IAAI,CAACuM,MAAM,EACZ,MAAM,mBAAmB;IAC7B,IAAIxN,CAAC,GAAG,IAAI,CAACkB,CAAC,CAACnB,MAAM;IACrB,IAAIwB,CAAC,GAAG,IAAIpD,EAAE,CAAC6B,CAAC,GAAG/C,CAAC,CAAC8C,MAAM,CAAC;IAC5BwB,CAAC,CAACC,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,EAAEK,CAAC,CAACC,GAAG,CAACvE,CAAC,EAAE+C,CAAC,CAAC,EAAE,IAAI,CAACkB,CAAC,GAAGK,CAAC;EAC1C,CAAC;EACDoN,OAAO,CAACtD,SAAS,CAACpO,CAAC,GAAG,UAAUoF,KAAK,EAAE;IACnC,IAAI,CAACpB,CAAC,GAAG,IAAI,CAACnB,CAAC,CAACd,CAAC,GAAGqD,KAAK,IAAI,KAAK;IAClC,IAAIuM,GAAG,GAAG,IAAI,CAAC9O,CAAC,CAACf,CAAC;IAClB,IAAIyE,EAAE,GAAG9B,KAAK,CAAC,IAAI,CAACR,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACrB,CAAC,CAAC;IACtC,IAAI,CAAC0N,MAAM,CAAClM,GAAG,CAACkC,EAAE,EAAEoL,GAAG,EAAE,IAAI,CAAC9O,CAAC,CAACf,CAAC,CAAC,EAAE,IAAI,CAACkC,CAAC,CAAC;IAC3C,IAAI,CAACE,CAAC,GAAGG,GAAG,CAACkC,EAAE,EAAE,IAAI,CAAC1D,CAAC,CAACf,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,CAACe,CAAC,CAACf,CAAC,GAAG,IAAI,CAACoC,CAAC,CAACpB,MAAM;IAC5D,IAAI,CAACmB,CAAC,GAAGI,GAAG,CAAC,IAAI,CAACJ,CAAC,EAAG,IAAI,CAACpB,CAAC,CAACoB,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC,EAAE,IAAI,CAACpB,CAAC,CAACoB,CAAC,IAAI,CAAC;EAC5D,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIyN,OAAO,CAACtD,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAC7C,IAAI,CAACvE,CAAC,CAAC0Q,KAAK,CAAC,EAAE,IAAI,CAACvR,CAAC,CAACoF,KAAK,CAAC;EAChC,CAAC;EACD,OAAOsM,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAASA,OAAO;AAChB;AACA;AACA;AACA,IAAIE,YAAY,GAAG,aAAe,YAAY;EAC1C;AACJ;AACA;AACA;EACI,SAASA,YAAYA,CAACxR,EAAE,EAAE;IACtB,IAAI,CAACmQ,MAAM,GAAGnQ,EAAE;IAChBqQ,QAAQ,CAAC,CACL3B,MAAM,EACN,YAAY;MAAE,OAAO,CAACuB,KAAK,EAAEqB,OAAO,CAAC;IAAE,CAAC,CAC3C,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY;MACpB,IAAIpB,IAAI,GAAG,IAAIoB,OAAO,CAAC,CAAC;MACxB3Q,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAOsB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,OAAO,SAASC,OAAOA,CAAC7Q,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACpC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO4P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBnB,MAAM,CACT,EAAE,UAAU0B,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACD,WAAW,CAACyB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEiO,GAAG,CAACuB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2O,WAAWA,CAAC/N,IAAI,EAAE0H,GAAG,EAAE;EACnC,OAAOjE,KAAK,CAACzD,IAAI,EAAE0H,GAAG,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,IAAIoJ,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAAC7B,IAAI,EAAE7P,EAAE,EAAE;IACpB,IAAI,CAACJ,CAAC,GAAGiN,GAAG,CAAC,CAAC;IACd,IAAI,CAAClK,CAAC,GAAG,CAAC;IACV,IAAI,CAACM,CAAC,GAAG,CAAC;IACViO,OAAO,CAACZ,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE7P,EAAE,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI0R,IAAI,CAAC1D,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAC1CkM,OAAO,CAAClD,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEa,KAAK,EAAEnM,KAAK,CAAC;EACnD,CAAC;EACD0M,IAAI,CAAC1D,SAAS,CAACnK,CAAC,GAAG,UAAUjE,CAAC,EAAEqF,CAAC,EAAE;IAC/B,IAAI,CAACrF,CAAC,CAACiE,CAAC,CAACjE,CAAC,CAAC;IACX,IAAI,CAAC+C,CAAC,IAAI/C,CAAC,CAAC8C,MAAM;IAClB,IAAIiP,GAAG,GAAG3E,IAAI,CAACpN,CAAC,EAAE,IAAI,CAACkE,CAAC,EAAE,IAAI,CAACb,CAAC,IAAIiM,IAAI,CAAC,IAAI,CAACpL,CAAC,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAE,CAACA,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAChC,CAAC,EACNgM,GAAG,CAAC0C,GAAG,EAAE,IAAI,CAAC7N,CAAC,CAAC,EAAE,IAAI,CAACb,CAAC,GAAG,CAAC;IAChC,IAAIgC,CAAC,EACDkK,MAAM,CAACwC,GAAG,EAAEA,GAAG,CAACjP,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC9C,CAAC,CAACgE,CAAC,CAAC,CAAC,CAAC,EAAEuL,MAAM,CAACwC,GAAG,EAAEA,GAAG,CAACjP,MAAM,GAAG,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAChF,IAAI,CAACwN,MAAM,CAACwB,GAAG,EAAE1M,CAAC,CAAC;EACvB,CAAC;EACD,OAAOyM,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,SAASA,IAAI;AACb;AACA;AACA;AACA,IAAIE,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAAC/B,IAAI,EAAE7P,EAAE,EAAE;IACzBqQ,QAAQ,CAAC,CACLvB,KAAK,EACLE,GAAG,EACH,YAAY;MAAE,OAAO,CAACiB,KAAK,EAAEiB,OAAO,EAAEQ,IAAI,CAAC;IAAE,CAAC,CACjD,EAAE,IAAI,EAAET,YAAY,CAACX,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE7P,EAAE,CAAC,EAAE,UAAUoQ,EAAE,EAAE;MACtD,IAAIF,IAAI,GAAG,IAAIwB,IAAI,CAACtB,EAAE,CAACxP,IAAI,CAAC;MAC5BD,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAO0B,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,OAAO,SAASC,IAAIA,CAACjR,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACjC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO4P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBf,KAAK,EACLE,GAAG,EACH,YAAY;IAAE,OAAO,CAAC8C,QAAQ,CAAC;EAAE,CAAC,CACrC,EAAE,UAAU1B,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACkD,QAAQ,CAAC1B,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8R,QAAQA,CAAClR,IAAI,EAAEiP,IAAI,EAAE;EACjC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAC,CAAC;EAAE;EAClC,IAAIjQ,CAAC,GAAGiN,GAAG,CAAC,CAAC;IAAElK,CAAC,GAAG/B,IAAI,CAAC8B,MAAM;EAC9B9C,CAAC,CAACiE,CAAC,CAACjD,IAAI,CAAC;EACT,IAAIgD,CAAC,GAAGoJ,IAAI,CAACpM,IAAI,EAAEiP,IAAI,EAAEX,IAAI,CAACW,IAAI,CAAC,EAAE,CAAC,CAAC;IAAEpN,CAAC,GAAGmB,CAAC,CAAClB,MAAM;EACrD,OAAOuM,GAAG,CAACrL,CAAC,EAAEiM,IAAI,CAAC,EAAEV,MAAM,CAACvL,CAAC,EAAEnB,CAAC,GAAG,CAAC,EAAE7C,CAAC,CAACgE,CAAC,CAAC,CAAC,CAAC,EAAEuL,MAAM,CAACvL,CAAC,EAAEnB,CAAC,GAAG,CAAC,EAAEE,CAAC,CAAC,EAAEiB,CAAC;AACxE;AACA;AACA;AACA;AACA,IAAImO,MAAM,GAAG,aAAe,YAAY;EACpC;AACJ;AACA;AACA;EACI,SAASA,MAAMA,CAAC/R,EAAE,EAAE;IAChB,IAAI,CAACiD,CAAC,GAAG,CAAC;IACVqO,OAAO,CAAChB,IAAI,CAAC,IAAI,EAAEtQ,EAAE,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI+R,MAAM,CAAC/D,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAC5CsM,OAAO,CAACtD,SAAS,CAACvN,CAAC,CAAC6P,IAAI,CAAC,IAAI,EAAEa,KAAK,CAAC;IACrC,IAAI,IAAI,CAAClO,CAAC,EAAE;MACR,IAAIR,CAAC,GAAG4M,GAAG,CAAC,IAAI,CAACxL,CAAC,CAAC;MACnB,IAAIpB,CAAC,IAAI,IAAI,CAACoB,CAAC,CAACnB,MAAM,IAAI,CAACsC,KAAK,EAC5B;MACJ,IAAI,CAACnB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC3B,CAAC,CAAC,EAAE,IAAI,CAACQ,CAAC,GAAG,CAAC;IAC3C;IACA,IAAI+B,KAAK,EAAE;MACP,IAAI,IAAI,CAACnB,CAAC,CAACnB,MAAM,GAAG,CAAC,EACjB,MAAM,qBAAqB;MAC/B,IAAI,CAACmB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC;IACA;IACA;IACAkN,OAAO,CAACtD,SAAS,CAACpO,CAAC,CAAC0Q,IAAI,CAAC,IAAI,EAAEtL,KAAK,CAAC;EACzC,CAAC;EACD,OAAO+M,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC;AACJ;AACA;AACA;EACI,SAASA,WAAWA,CAAChS,EAAE,EAAE;IACrB,IAAI,CAACmQ,MAAM,GAAGnQ,EAAE;IAChBqQ,QAAQ,CAAC,CACL3B,MAAM,EACNU,IAAI,EACJ,YAAY;MAAE,OAAO,CAACa,KAAK,EAAEqB,OAAO,EAAES,MAAM,CAAC;IAAE,CAAC,CACnD,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY;MACpB,IAAI7B,IAAI,GAAG,IAAI6B,MAAM,CAAC,CAAC;MACvBpR,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACT;EACA,OAAO8B,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,OAAO,SAASC,MAAMA,CAACrR,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACnC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO4P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBnB,MAAM,EACNU,IAAI,EACJ,YAAY;IAAE,OAAO,CAAC8C,UAAU,CAAC;EAAE,CAAC,CACvC,EAAE,UAAU9B,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAACsD,UAAU,CAAC9B,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkS,UAAUA,CAACtR,IAAI,EAAE0H,GAAG,EAAE;EAClC,OAAOjE,KAAK,CAACzD,IAAI,CAACwD,QAAQ,CAACiL,GAAG,CAACzO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE0H,GAAG,IAAI,IAAIxH,EAAE,CAACwO,GAAG,CAAC1O,IAAI,CAAC,CAAC,CAAC;AACxE;AACA;AACA;AACA;AACA,IAAIuR,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAACtC,IAAI,EAAE7P,EAAE,EAAE;IACpB,IAAI,CAACJ,CAAC,GAAGmN,KAAK,CAAC,CAAC;IAChB,IAAI,CAAC9J,CAAC,GAAG,CAAC;IACViO,OAAO,CAACZ,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE7P,EAAE,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACImS,IAAI,CAACnE,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAC1CkM,OAAO,CAAClD,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEa,KAAK,EAAEnM,KAAK,CAAC;EACnD,CAAC;EACDmN,IAAI,CAACnE,SAAS,CAACnK,CAAC,GAAG,UAAUjE,CAAC,EAAEqF,CAAC,EAAE;IAC/B,IAAI,CAACrF,CAAC,CAACiE,CAAC,CAACjE,CAAC,CAAC;IACX,IAAI+R,GAAG,GAAG3E,IAAI,CAACpN,CAAC,EAAE,IAAI,CAACkE,CAAC,EAAE,IAAI,CAACb,CAAC,IAAI,CAAC,EAAEgC,CAAC,IAAI,CAAC,EAAE,CAACA,CAAC,CAAC;IAClD,IAAI,IAAI,CAAChC,CAAC,EACNuM,GAAG,CAACmC,GAAG,EAAE,IAAI,CAAC7N,CAAC,CAAC,EAAE,IAAI,CAACb,CAAC,GAAG,CAAC;IAChC,IAAIgC,CAAC,EACDkK,MAAM,CAACwC,GAAG,EAAEA,GAAG,CAACjP,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC9C,CAAC,CAACgE,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACuM,MAAM,CAACwB,GAAG,EAAE1M,CAAC,CAAC;EACvB,CAAC;EACD,OAAOkN,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,SAASA,IAAI;AACb;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACvC,IAAI,EAAE7P,EAAE,EAAE;IACzBqQ,QAAQ,CAAC,CACLvB,KAAK,EACLS,GAAG,EACH,YAAY;MAAE,OAAO,CAACU,KAAK,EAAEiB,OAAO,EAAEiB,IAAI,CAAC;IAAE,CAAC,CACjD,EAAE,IAAI,EAAElB,YAAY,CAACX,IAAI,CAAC,IAAI,EAAET,IAAI,EAAE7P,EAAE,CAAC,EAAE,UAAUoQ,EAAE,EAAE;MACtD,IAAIF,IAAI,GAAG,IAAIiC,IAAI,CAAC/B,EAAE,CAACxP,IAAI,CAAC;MAC5BD,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACV;EACA,OAAOkC,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,OAAO,SAASC,IAAIA,CAACzR,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACjC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO4P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBf,KAAK,EACLS,GAAG,EACH,YAAY;IAAE,OAAO,CAAC+C,QAAQ,CAAC;EAAE,CAAC,CACrC,EAAE,UAAUlC,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAAC0D,QAAQ,CAAClC,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEwP,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsS,QAAQA,CAAC1R,IAAI,EAAEiP,IAAI,EAAE;EACjC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAC,CAAC;EAAE;EAClC,IAAInM,CAAC,GAAGqJ,KAAK,CAAC,CAAC;EACfrJ,CAAC,CAACG,CAAC,CAACjD,IAAI,CAAC;EACT,IAAIgD,CAAC,GAAGoJ,IAAI,CAACpM,IAAI,EAAEiP,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,OAAOL,GAAG,CAAC5L,CAAC,EAAEiM,IAAI,CAAC,EAAEV,MAAM,CAACvL,CAAC,EAAEA,CAAC,CAAClB,MAAM,GAAG,CAAC,EAAEgB,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC;AAC1D;AACA;AACA;AACA;AACA,IAAI2O,MAAM,GAAG,aAAe,YAAY;EACpC;AACJ;AACA;AACA;EACI,SAASA,MAAMA,CAACvS,EAAE,EAAE;IAChB,IAAI,CAACiD,CAAC,GAAG,CAAC;IACVqO,OAAO,CAAChB,IAAI,CAAC,IAAI,EAAEtQ,EAAE,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIuS,MAAM,CAACvE,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAC5CsM,OAAO,CAACtD,SAAS,CAACvN,CAAC,CAAC6P,IAAI,CAAC,IAAI,EAAEa,KAAK,CAAC;IACrC,IAAI,IAAI,CAAClO,CAAC,EAAE;MACR,IAAI,IAAI,CAACY,CAAC,CAACnB,MAAM,GAAG,CAAC,IAAI,CAACsC,KAAK,EAC3B;MACJ,IAAI,CAACnB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnB,CAAC,GAAG,CAAC;IAC3C;IACA,IAAI+B,KAAK,EAAE;MACP,IAAI,IAAI,CAACnB,CAAC,CAACnB,MAAM,GAAG,CAAC,EACjB,MAAM,qBAAqB;MAC/B,IAAI,CAACmB,CAAC,GAAG,IAAI,CAACA,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnC;IACA;IACA;IACAkN,OAAO,CAACtD,SAAS,CAACpO,CAAC,CAAC0Q,IAAI,CAAC,IAAI,EAAEtL,KAAK,CAAC;EACzC,CAAC;EACD,OAAOuN,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC;AACJ;AACA;AACA;EACI,SAASA,WAAWA,CAACxS,EAAE,EAAE;IACrB,IAAI,CAACmQ,MAAM,GAAGnQ,EAAE;IAChBqQ,QAAQ,CAAC,CACL3B,MAAM,EACNe,IAAI,EACJ,YAAY;MAAE,OAAO,CAACQ,KAAK,EAAEqB,OAAO,EAAEiB,MAAM,CAAC;IAAE,CAAC,CACnD,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY;MACpB,IAAIrC,IAAI,GAAG,IAAIqC,MAAM,CAAC,CAAC;MACvB5R,SAAS,GAAGsP,KAAK,CAACC,IAAI,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC;EACV;EACA,OAAOsC,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,OAAO,SAASC,MAAMA,CAAC7R,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACnC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAO4P,KAAK,CAAChP,IAAI,EAAEiP,IAAI,EAAE,CACrBnB,MAAM,EACNe,IAAI,EACJ,YAAY;IAAE,OAAO,CAACiD,UAAU,CAAC;EAAE,CAAC,CACvC,EAAE,UAAUtC,EAAE,EAAE;IAAE,OAAOxB,GAAG,CAAC8D,UAAU,CAACtC,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,EAAEiO,GAAG,CAACuB,EAAE,CAACxP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,EAAE,CAAC,EAAEZ,EAAE,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0S,UAAUA,CAAC9R,IAAI,EAAE0H,GAAG,EAAE;EAClC,OAAOjE,KAAK,EAAEqL,GAAG,CAAC9O,IAAI,CAAC,EAAEA,IAAI,CAACwD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGkE,GAAG,CAAC;AACxD;AACA;AACA,SAASuJ,IAAI,IAAIc,QAAQ,EAAEf,SAAS,IAAIgB,aAAa;AACrD;AACA,SAASd,QAAQ,IAAIe,YAAY,EAAEnB,IAAI,IAAIoB,QAAQ;AACnD;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC;AACJ;AACA;AACA;EACI,SAASA,UAAUA,CAAC/S,EAAE,EAAE;IACpB,IAAI,CAACgT,CAAC,GAAGjB,MAAM;IACf,IAAI,CAACkB,CAAC,GAAG3B,OAAO;IAChB,IAAI,CAAC4B,CAAC,GAAGX,MAAM;IACf,IAAI,CAACpC,MAAM,GAAGnQ,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;EACI+S,UAAU,CAAC/E,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IAChD,IAAI,CAAC,IAAI,CAACmL,MAAM,EACZ,MAAM,mBAAmB;IAC7B,IAAI,CAAC,IAAI,CAAC1N,CAAC,EAAE;MACT,IAAI,IAAI,CAACoB,CAAC,IAAI,IAAI,CAACA,CAAC,CAACnB,MAAM,EAAE;QACzB,IAAIwB,CAAC,GAAG,IAAIpD,EAAE,CAAC,IAAI,CAAC+C,CAAC,CAACnB,MAAM,GAAGyO,KAAK,CAACzO,MAAM,CAAC;QAC5CwB,CAAC,CAACC,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,EAAEK,CAAC,CAACC,GAAG,CAACgN,KAAK,EAAE,IAAI,CAACtN,CAAC,CAACnB,MAAM,CAAC;MAC9C,CAAC,MAEG,IAAI,CAACmB,CAAC,GAAGsN,KAAK;MAClB,IAAI,IAAI,CAACtN,CAAC,CAACnB,MAAM,GAAG,CAAC,EAAE;QACnB,IAAIyQ,OAAO,GAAG,IAAI;QAClB,IAAInT,EAAE,GAAG,SAAAA,CAAA,EAAY;UAAEmT,OAAO,CAAChD,MAAM,CAACiD,KAAK,CAACD,OAAO,EAAEE,SAAS,CAAC;QAAE,CAAC;QAClE,IAAI,CAAC5Q,CAAC,GAAI,IAAI,CAACoB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GACzD,IAAI,IAAI,CAACmP,CAAC,CAAChT,EAAE,CAAC,GACb,CAAC,IAAI,CAAC6D,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,IAAK,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,GACjF,IAAI,IAAI,CAACoP,CAAC,CAACjT,EAAE,CAAC,GACd,IAAI,IAAI,CAACkT,CAAC,CAAClT,EAAE,CAAC;QACxB,IAAI,CAACyC,CAAC,CAACqE,IAAI,CAAC,IAAI,CAACjD,CAAC,EAAEmB,KAAK,CAAC;QAC1B,IAAI,CAACnB,CAAC,GAAG,IAAI;MACjB;IACJ,CAAC,MAEG,IAAI,CAACpB,CAAC,CAACqE,IAAI,CAACqK,KAAK,EAAEnM,KAAK,CAAC;EACjC,CAAC;EACD,OAAO+N,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB;AACA;AACA;AACA,IAAIO,eAAe,GAAG,aAAe,YAAY;EAC7C;AACJ;AACA;AACA;EACI,SAASA,eAAeA,CAACtT,EAAE,EAAE;IACzB,IAAI,CAACgT,CAAC,GAAGhB,WAAW;IACpB,IAAI,CAACiB,CAAC,GAAGzB,YAAY;IACrB,IAAI,CAAC0B,CAAC,GAAGV,WAAW;IACpB,IAAI,CAACrC,MAAM,GAAGnQ,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;EACIsT,eAAe,CAACtF,SAAS,CAAClH,IAAI,GAAG,UAAUqK,KAAK,EAAEnM,KAAK,EAAE;IACrD+N,UAAU,CAAC/E,SAAS,CAAClH,IAAI,CAACwJ,IAAI,CAAC,IAAI,EAAEa,KAAK,EAAEnM,KAAK,CAAC;EACtD,CAAC;EACD,OAAOsO,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,SAASA,eAAe;AACxB,OAAO,SAASC,UAAUA,CAAC3S,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EACvC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,OAAQY,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GACjDqR,MAAM,CAACrR,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,CAAC,GACrB,CAACY,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAKA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,IAAK,CAACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,GACzE6Q,OAAO,CAAC7Q,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,CAAC,GACvByS,MAAM,CAAC7R,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwT,cAAcA,CAAC5S,IAAI,EAAE0H,GAAG,EAAE;EACtC,OAAQ1H,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GACjDsR,UAAU,CAACtR,IAAI,EAAE0H,GAAG,CAAC,GACpB,CAAC1H,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAKA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,IAAK,CAACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,GACzE+N,WAAW,CAAC/N,IAAI,EAAE0H,GAAG,CAAC,GACtBoK,UAAU,CAAC9R,IAAI,EAAE0H,GAAG,CAAC;AACnC;AACA;AACA,IAAImL,IAAI,GAAG,SAAAA,CAAU7P,CAAC,EAAEC,CAAC,EAAE4B,CAAC,EAAE3B,CAAC,EAAE;EAC7B,KAAK,IAAI8I,CAAC,IAAIhJ,CAAC,EAAE;IACb,IAAI8P,GAAG,GAAG9P,CAAC,CAACgJ,CAAC,CAAC;MAAE1I,CAAC,GAAGL,CAAC,GAAG+I,CAAC;IACzB,IAAI8G,GAAG,YAAY5S,EAAE,EACjB2E,CAAC,CAACvB,CAAC,CAAC,GAAG,CAACwP,GAAG,EAAE5P,CAAC,CAAC,CAAC,KACf,IAAI6P,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EACvBjO,CAAC,CAACvB,CAAC,CAAC,GAAG,CAACwP,GAAG,CAAC,CAAC,CAAC,EAAEtG,GAAG,CAACtJ,CAAC,EAAE4P,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAEhCD,IAAI,CAACC,GAAG,EAAExP,CAAC,GAAG,GAAG,EAAEuB,CAAC,EAAE3B,CAAC,CAAC;EAChC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+P,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACjC,IAAIpR,CAAC,GAAGmR,GAAG,CAACpR,MAAM;EAClB,IAAI,CAACqR,MAAM,IAAI,OAAOC,WAAW,IAAI,WAAW,EAC5C,OAAO,IAAIA,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,GAAG,CAAC;EACxC,IAAII,EAAE,GAAG,IAAIpT,EAAE,CAACgT,GAAG,CAACpR,MAAM,IAAIoR,GAAG,CAACpR,MAAM,KAAK,CAAC,CAAC,CAAC;EAChD,IAAIyR,EAAE,GAAG,CAAC;EACV,IAAI7T,CAAC,GAAG,SAAAA,CAAU2C,CAAC,EAAE;IAAEiR,EAAE,CAACC,EAAE,EAAE,CAAC,GAAGlR,CAAC;EAAE,CAAC;EACtC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,CAAC,EAAE,EAAEhB,CAAC,EAAE;IACxB,IAAIwS,EAAE,GAAG,CAAC,GAAGD,EAAE,CAACxR,MAAM,EAAE;MACpB,IAAIwB,CAAC,GAAG,IAAIpD,EAAE,CAACqT,EAAE,GAAG,CAAC,IAAKxR,CAAC,GAAGhB,CAAC,IAAK,CAAC,CAAC,CAAC;MACvCuC,CAAC,CAACC,GAAG,CAAC+P,EAAE,CAAC;MACTA,EAAE,GAAGhQ,CAAC;IACV;IACA,IAAItE,CAAC,GAAGkU,GAAG,CAACjD,UAAU,CAAClP,CAAC,CAAC;IACzB,IAAI/B,CAAC,GAAG,GAAG,IAAImU,MAAM,EACjBzT,CAAC,CAACV,CAAC,CAAC,CAAC,KACJ,IAAIA,CAAC,GAAG,IAAI,EACbU,CAAC,CAAC,GAAG,GAAIV,CAAC,KAAK,CAAE,CAAC,EAAEU,CAAC,CAAC,GAAG,GAAIV,CAAC,GAAG,EAAG,CAAC,CAAC,KACrC,IAAIA,CAAC,GAAG,KAAK,IAAIA,CAAC,GAAG,KAAK,EAC3BA,CAAC,GAAG,KAAK,IAAIA,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,GAAIkU,GAAG,CAACjD,UAAU,CAAC,EAAElP,CAAC,CAAC,GAAG,IAAK,EACvDrB,CAAC,CAAC,GAAG,GAAIV,CAAC,KAAK,EAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,GAAKV,CAAC,KAAK,EAAE,GAAI,EAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,GAAKV,CAAC,KAAK,CAAC,GAAI,EAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,GAAIV,CAAC,GAAG,EAAG,CAAC,CAAC,KAElGU,CAAC,CAAC,GAAG,GAAIV,CAAC,KAAK,EAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,GAAKV,CAAC,KAAK,CAAC,GAAI,EAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,GAAIV,CAAC,GAAG,EAAG,CAAC;EACzE;EACA,OAAOqE,GAAG,CAACiQ,EAAE,EAAE,CAAC,EAAEC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAC9P,GAAG,EAAEyP,MAAM,EAAE;EACnC,IAAInS,CAAC,GAAG,EAAE;EACV,IAAI,CAACmS,MAAM,IAAI,OAAOM,WAAW,IAAI,WAAW,EAC5C,OAAO,IAAIA,WAAW,CAAC,CAAC,CAACC,MAAM,CAAChQ,GAAG,CAAC;EACxC,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,GAAG,CAAC5B,MAAM,GAAG;IAC7B,IAAI9C,CAAC,GAAG0E,GAAG,CAAC3C,CAAC,EAAE,CAAC;IAChB,IAAI/B,CAAC,GAAG,GAAG,IAAImU,MAAM,EACjBnS,CAAC,IAAI2S,MAAM,CAACC,YAAY,CAAC5U,CAAC,CAAC,CAAC,KAC3B,IAAIA,CAAC,GAAG,GAAG,EACZgC,CAAC,IAAI2S,MAAM,CAACC,YAAY,CAAC,CAAC5U,CAAC,GAAG,EAAE,KAAK,CAAC,GAAI0E,GAAG,CAAC3C,CAAC,EAAE,CAAC,GAAG,EAAG,CAAC,CAAC,KACzD,IAAI/B,CAAC,GAAG,GAAG,EACZgC,CAAC,IAAI2S,MAAM,CAACC,YAAY,CAAC,CAAC5U,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC0E,GAAG,CAAC3C,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAI2C,GAAG,CAAC3C,CAAC,EAAE,CAAC,GAAG,EAAG,CAAC,CAAC,KAElF/B,CAAC,GAAG,CAAC,CAACA,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC0E,GAAG,CAAC3C,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC2C,GAAG,CAAC3C,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAI2C,GAAG,CAAC3C,CAAC,EAAE,CAAC,GAAG,EAAG,IAAI,KAAK,EACzFC,CAAC,IAAI2S,MAAM,CAACC,YAAY,CAAC,KAAK,GAAI5U,CAAC,IAAI,EAAG,EAAE,KAAK,GAAIA,CAAC,GAAG,IAAK,CAAC;EAC3E;EACA,OAAOgC,CAAC;AACZ;AACA;AACA;AACA,IAAI6S,IAAI,GAAG,SAAAA,CAAU7Q,CAAC,EAAElC,CAAC,EAAE;EAAE,OAAOA,CAAC,GAAG,EAAE,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC;AAAE,CAAC;AAC7E;AACA,IAAIgT,EAAE,GAAG,SAAAA,CAAU9Q,CAAC,EAAElC,CAAC,EAAEiT,CAAC,EAAE;EACxB,IAAIC,GAAG,GAAGrE,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC;IAAE4L,EAAE,GAAG8G,SAAS,CAACxQ,CAAC,CAACQ,QAAQ,CAAC1C,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,GAAGkT,GAAG,CAAC,EAAE,EAAErE,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAAEmT,EAAE,GAAGnT,CAAC,GAAG,EAAE,GAAGkT,GAAG;EACpH,IAAI9S,EAAE,GAAG6S,CAAC,GAAGG,IAAI,CAAClR,CAAC,EAAEiR,EAAE,CAAC,GAAG,CAACrE,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAE8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAE8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,CAAC;IAAEqT,EAAE,GAAGjT,EAAE,CAAC,CAAC,CAAC;IAAEkT,EAAE,GAAGlT,EAAE,CAAC,CAAC,CAAC;IAAEmT,GAAG,GAAGnT,EAAE,CAAC,CAAC,CAAC;EAC7G,OAAO,CAACyO,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAEqT,EAAE,EAAEC,EAAE,EAAE1H,EAAE,EAAEuH,EAAE,GAAGtE,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAEuT,GAAG,CAAC;AAC/E,CAAC;AACD;AACA,IAAIH,IAAI,GAAG,SAAAA,CAAUlR,CAAC,EAAElC,CAAC,EAAE;EACvB,OAAO6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,GAAG6O,EAAE,CAAC3M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,CACvC;EACJ,OAAO,CAAC8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,EAAE8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,CAAC,CAAC,EAAE8O,EAAE,CAAC5M,CAAC,EAAElC,CAAC,GAAG,EAAE,CAAC,CAAC;AACvD,CAAC;AACD;AACA,IAAIwT,GAAG,GAAG,SAAAA,CAAUtR,CAAC,EAAElC,CAAC,EAAE9B,CAAC,EAAEuV,GAAG,EAAEH,EAAE,EAAE1H,EAAE,EAAErN,CAAC,EAAE6D,CAAC,EAAEsR,EAAE,EAAE3P,CAAC,EAAE;EACnD,IAAI1D,EAAE,GAAGuL,EAAE,CAAC5K,MAAM;IAAEC,CAAC,GAAGwS,GAAG,CAACzS,MAAM;EAClCyM,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAE0T,EAAE,IAAI,IAAI,GAAG,SAAS,GAAG,SAAS,CAAC,EAAE1T,CAAC,IAAI,CAAC;EACxD,IAAI0T,EAAE,IAAI,IAAI,EACVxR,CAAC,CAAClC,CAAC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC;EACrBkC,CAAC,CAAClC,CAAC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,CAAC,CAAC;EACnBkC,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAI+D,CAAC,IAAI,CAAC,KAAK3B,CAAC,CAACmJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAGnJ,CAAC,CAACmJ,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGnJ,CAAC,CAACmJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,EAAErJ,CAAC,CAAClC,CAAC,EAAE,CAAC,GAAGzB,CAAC,IAAI,CAAC;EACjG2D,CAAC,CAAClC,CAAC,CAAC,GAAG+D,CAAC,EAAE/D,CAAC,IAAI,CAAC;EAChB,IAAIyE,EAAE,GAAG,IAAIwK,IAAI,CAAC7M,CAAC,CAAC4M,KAAK,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAAEyE,CAAC,GAAGlP,EAAE,CAACmP,WAAW,CAAC,CAAC,GAAG,IAAI;EACrE,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAChB,MAAM,6BAA6B;EACvClG,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAG,CAAC2T,CAAC,IAAI,EAAE,IAAI,CAAC,GAAMlP,EAAE,CAACoP,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAK,EAAG,GAAIpP,EAAE,CAACqP,OAAO,CAAC,CAAC,IAAI,EAAG,GAAIrP,EAAE,CAACsP,QAAQ,CAAC,CAAC,IAAI,EAAG,GAAItP,EAAE,CAACuP,UAAU,CAAC,CAAC,IAAI,CAAE,GAAIvP,EAAE,CAACwP,UAAU,CAAC,CAAC,KAAK,CAAE,CAAC;EAC7JjU,CAAC,IAAI,CAAC;EACNyN,MAAM,CAACvL,CAAC,EAAElC,CAAC,EAAE9B,CAAC,CAAC;EACfuP,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,CAAC,EAAEiB,CAAC,CAAC;EACnBwM,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,CAAC,EAAEsT,EAAE,CAAC;EACpB7F,MAAM,CAACvL,CAAC,EAAElC,CAAC,GAAG,EAAE,EAAEK,EAAE,CAAC,EAAEL,CAAC,IAAI,EAAE,CAAC,CAAC;EAChC,IAAI0T,EAAE,IAAI,IAAI,EACVjG,MAAM,CAACvL,CAAC,EAAElC,CAAC,IAAI,EAAE,EAAE0T,EAAE,CAAC,EAAE1T,CAAC,IAAI,CAAC;EAClCkC,CAAC,CAACO,GAAG,CAACmJ,EAAE,EAAE5L,CAAC,CAAC;EACZA,CAAC,IAAIK,EAAE;EACP,IAAIqT,EAAE,IAAI,IAAI,EACVxR,CAAC,CAACO,GAAG,CAACgR,GAAG,EAAEzT,CAAC,CAAC;AACrB,CAAC;AACD;AACA,IAAIkU,GAAG,GAAG,SAAAA,CAAU9R,CAAC,EAAEpC,CAAC,EAAE9B,CAAC,EAAEgE,CAAC,EAAEnD,CAAC,EAAE;EAC/B0O,MAAM,CAACrL,CAAC,EAAEpC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;EACzByN,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,CAAC,EAAE9B,CAAC,CAAC;EACnBuP,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,EAAE,EAAE9B,CAAC,CAAC;EACpBuP,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,EAAE,EAAEkC,CAAC,CAAC;EACpBuL,MAAM,CAACrL,CAAC,EAAEpC,CAAC,GAAG,EAAE,EAAEjB,CAAC,CAAC;AACxB,CAAC;AACD,OAAO,SAASoV,GAAGA,CAACjV,IAAI,EAAEiP,IAAI,EAAE7P,EAAE,EAAE;EAChC,IAAI,CAACA,EAAE,EACHA,EAAE,GAAG6P,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACxB,IAAI,OAAO7P,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,IAAI4B,CAAC,GAAG,CAAC,CAAC;EACV6R,IAAI,CAAC7S,IAAI,EAAE,EAAE,EAAEgB,CAAC,EAAEiO,IAAI,CAAC;EACvB,IAAIjD,CAAC,GAAGkJ,MAAM,CAACC,IAAI,CAACnU,CAAC,CAAC;EACtB,IAAI6F,GAAG,GAAGmF,CAAC,CAAClK,MAAM;IAAEoB,CAAC,GAAG,CAAC;IAAEkS,GAAG,GAAG,CAAC;EAClC,IAAIC,IAAI,GAAGxO,GAAG;IAAEyO,KAAK,GAAG,IAAIvC,KAAK,CAAClM,GAAG,CAAC;EACtC,IAAI0O,IAAI,GAAG,EAAE;EACb,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;IACnB,KAAK,IAAIzU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwU,IAAI,CAACzT,MAAM,EAAE,EAAEf,CAAC,EAChCwU,IAAI,CAACxU,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,IAAI0U,GAAG,GAAG,SAAAA,CAAA,EAAY;IAClB,IAAI/N,GAAG,GAAG,IAAIxH,EAAE,CAACkV,GAAG,GAAG,EAAE,CAAC;MAAEM,EAAE,GAAGxS,CAAC;MAAEyS,GAAG,GAAGP,GAAG,GAAGlS,CAAC;IACjDkS,GAAG,GAAG,CAAC;IACP,KAAK,IAAIrU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsU,IAAI,EAAE,EAAEtU,CAAC,EAAE;MAC3B,IAAIsD,CAAC,GAAGiR,KAAK,CAACvU,CAAC,CAAC;MAChB,IAAI;QACAuT,GAAG,CAAC5M,GAAG,EAAE0N,GAAG,EAAE/Q,CAAC,CAACrF,CAAC,EAAEqF,CAAC,CAACrB,CAAC,EAAEqB,CAAC,CAAC/B,CAAC,EAAE+B,CAAC,CAACf,CAAC,EAAEe,CAAC,CAAChF,CAAC,EAAEgF,CAAC,CAACpB,CAAC,EAAE,IAAI,EAAEoB,CAAC,CAACQ,CAAC,CAAC;QACtDyP,GAAG,CAAC5M,GAAG,EAAExE,CAAC,EAAEmB,CAAC,CAACrF,CAAC,EAAEqF,CAAC,CAACrB,CAAC,EAAEqB,CAAC,CAAC/B,CAAC,EAAE+B,CAAC,CAACf,CAAC,EAAEe,CAAC,CAAChF,CAAC,EAAEgF,CAAC,CAACpB,CAAC,EAAEmS,GAAG,EAAE/Q,CAAC,CAACQ,CAAC,CAAC,EAAE3B,CAAC,IAAI,EAAE,GAAGmB,CAAC,CAACf,CAAC,CAACxB,MAAM,EAAEsT,GAAG,IAAI,EAAE,GAAG/Q,CAAC,CAACf,CAAC,CAACxB,MAAM,GAAGuC,CAAC,CAACrB,CAAC,CAAClB,MAAM;MAClH,CAAC,CACD,OAAOjC,CAAC,EAAE;QACN,OAAOT,EAAE,CAACS,CAAC,EAAE,IAAI,CAAC;MACtB;IACJ;IACAmV,GAAG,CAACtN,GAAG,EAAExE,CAAC,EAAEoS,KAAK,CAACxT,MAAM,EAAE6T,GAAG,EAAED,EAAE,CAAC;IAClCtW,EAAE,CAAC,IAAI,EAAEsI,GAAG,CAAC;EACjB,CAAC;EACD,IAAI,CAACb,GAAG,EACJ4O,GAAG,CAAC,CAAC;EACT,IAAIG,OAAO,GAAG,SAAAA,CAAU7U,CAAC,EAAE;IACvB,IAAI2L,EAAE,GAAGV,CAAC,CAACjL,CAAC,CAAC;IACb,IAAIG,EAAE,GAAGF,CAAC,CAAC0L,EAAE,CAAC;MAAEmJ,IAAI,GAAG3U,EAAE,CAAC,CAAC,CAAC;MAAE+B,CAAC,GAAG/B,EAAE,CAAC,CAAC,CAAC;IACvC,IAAIlC,CAAC,GAAGiN,GAAG,CAAC,CAAC;MAAE3J,CAAC,GAAGuT,IAAI,CAAC/T,MAAM;IAC9B9C,CAAC,CAACiE,CAAC,CAAC4S,IAAI,CAAC;IACT,IAAIvS,CAAC,GAAG2P,OAAO,CAACvG,EAAE,CAAC;MAAE7K,CAAC,GAAGyB,CAAC,CAACxB,MAAM;IACjC,IAAI+C,CAAC,GAAG5B,CAAC,CAACoJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5B,IAAIyJ,GAAG,GAAG,SAAAA,CAAUjW,CAAC,EAAEmD,CAAC,EAAE;MACtB,IAAInD,CAAC,EAAE;QACH2V,IAAI,CAAC,CAAC;QACNpW,EAAE,CAACS,CAAC,EAAE,IAAI,CAAC;MACf,CAAC,MACI;QACD,IAAIkC,CAAC,GAAGiB,CAAC,CAAClB,MAAM;QAChBwT,KAAK,CAACvU,CAAC,CAAC,GAAG;UACP8D,CAAC,EAAEA,CAAC;UACJ7B,CAAC,EAAEA,CAAC;UACJV,CAAC,EAAEA,CAAC;UACJtD,CAAC,EAAEA,CAAC,CAACgE,CAAC,CAAC,CAAC;UACR3D,CAAC,EAAEqN,EAAE,CAAC5K,MAAM,IAAIC,CAAC;UACjBuB,CAAC,EAAEA,CAAC;UACJL,CAAC,EAAEA;QACP,CAAC;QACDC,CAAC,IAAI,EAAE,GAAGrB,CAAC,GAAGE,CAAC;QACfqT,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGvT,CAAC,GAAGE,CAAC;QACrB,IAAI,CAAC,GAAE8E,GAAG,EACN4O,GAAG,CAAC,CAAC;MACb;IACJ,CAAC;IACD,IAAInS,CAAC,CAACxB,MAAM,GAAG,KAAK,EAChBgU,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAClC,IAAI,CAACjR,CAAC,EACFiR,GAAG,CAAC,IAAI,EAAED,IAAI,CAAC,CAAC,KACf,IAAIvT,CAAC,GAAG,MAAM,EAAE;MACjB,IAAI;QACAwT,GAAG,CAAC,IAAI,EAAE3H,WAAW,CAAC0H,IAAI,EAAE5S,CAAC,CAAC,CAAC;MACnC,CAAC,CACD,OAAOpD,CAAC,EAAE;QACNiW,GAAG,CAACjW,CAAC,EAAE,IAAI,CAAC;MAChB;IACJ,CAAC,MAEG0V,IAAI,CAACrP,IAAI,CAACuK,OAAO,CAACoF,IAAI,EAAE5S,CAAC,EAAE6S,GAAG,CAAC,CAAC;EACxC,CAAC;EACD;EACA,KAAK,IAAI/U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsU,IAAI,EAAE,EAAEtU,CAAC,EAAE;IAC3B6U,OAAO,CAAC7U,CAAC,CAAC;EACd;EACA,OAAOyU,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,OAAOA,CAAC/V,IAAI,EAAEiP,IAAI,EAAE;EAChC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAC,CAAC;EAAE;EAClC,IAAIjO,CAAC,GAAG,CAAC,CAAC;EACV,IAAIsU,KAAK,GAAG,EAAE;EACdzC,IAAI,CAAC7S,IAAI,EAAE,EAAE,EAAEgB,CAAC,EAAEiO,IAAI,CAAC;EACvB,IAAI/L,CAAC,GAAG,CAAC;EACT,IAAIkS,GAAG,GAAG,CAAC;EACX,KAAK,IAAI1I,EAAE,IAAI1L,CAAC,EAAE;IACd,IAAIE,EAAE,GAAGF,CAAC,CAAC0L,EAAE,CAAC;MAAEmJ,IAAI,GAAG3U,EAAE,CAAC,CAAC,CAAC;MAAE+B,CAAC,GAAG/B,EAAE,CAAC,CAAC,CAAC;IACvC,IAAI2D,CAAC,GAAG5B,CAAC,CAACoJ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5B,IAAI/I,CAAC,GAAG2P,OAAO,CAACvG,EAAE,CAAC;MAAE7K,CAAC,GAAGyB,CAAC,CAACxB,MAAM;IACjC,IAAIwB,CAAC,CAACxB,MAAM,GAAG,KAAK,EAChB,MAAM,mBAAmB;IAC7B,IAAIkB,CAAC,GAAG6B,CAAC,GAAGsJ,WAAW,CAAC0H,IAAI,EAAE5S,CAAC,CAAC,GAAG4S,IAAI;MAAE9T,CAAC,GAAGiB,CAAC,CAAClB,MAAM;IACrD,IAAI9C,CAAC,GAAGiN,GAAG,CAAC,CAAC;IACbjN,CAAC,CAACiE,CAAC,CAAC4S,IAAI,CAAC;IACTP,KAAK,CAACpP,IAAI,CAAC;MACPrB,CAAC,EAAEA,CAAC;MACJ7B,CAAC,EAAEA,CAAC;MACJV,CAAC,EAAEuT,IAAI,CAAC/T,MAAM;MACd9C,CAAC,EAAEA,CAAC,CAACgE,CAAC,CAAC,CAAC;MACR3D,CAAC,EAAEqN,EAAE,CAAC5K,MAAM,IAAID,CAAC;MACjByB,CAAC,EAAEA,CAAC;MACJJ,CAAC,EAAEA,CAAC;MACJD,CAAC,EAAEA;IACP,CAAC,CAAC;IACFC,CAAC,IAAI,EAAE,GAAGrB,CAAC,GAAGE,CAAC;IACfqT,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGvT,CAAC,GAAGE,CAAC;EACzB;EACA,IAAI2F,GAAG,GAAG,IAAIxH,EAAE,CAACkV,GAAG,GAAG,EAAE,CAAC;IAAEM,EAAE,GAAGxS,CAAC;IAAEyS,GAAG,GAAGP,GAAG,GAAGlS,CAAC;EACjD,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuU,KAAK,CAACxT,MAAM,EAAE,EAAEf,CAAC,EAAE;IACnC,IAAIsD,CAAC,GAAGiR,KAAK,CAACvU,CAAC,CAAC;IAChBuT,GAAG,CAAC5M,GAAG,EAAErD,CAAC,CAACnB,CAAC,EAAEmB,CAAC,CAACrF,CAAC,EAAEqF,CAAC,CAACrB,CAAC,EAAEqB,CAAC,CAAC/B,CAAC,EAAE+B,CAAC,CAACf,CAAC,EAAEe,CAAC,CAAChF,CAAC,EAAEgF,CAAC,CAACpB,CAAC,EAAE,IAAI,EAAEoB,CAAC,CAACQ,CAAC,CAAC;IACtDyP,GAAG,CAAC5M,GAAG,EAAExE,CAAC,EAAEmB,CAAC,CAACrF,CAAC,EAAEqF,CAAC,CAACrB,CAAC,EAAEqB,CAAC,CAAC/B,CAAC,EAAE+B,CAAC,CAACf,CAAC,EAAEe,CAAC,CAAChF,CAAC,EAAEgF,CAAC,CAACpB,CAAC,EAAEoB,CAAC,CAACnB,CAAC,EAAEmB,CAAC,CAACQ,CAAC,CAAC,EAAE3B,CAAC,IAAI,EAAE,GAAGmB,CAAC,CAACf,CAAC,CAACxB,MAAM;EAC7E;EACAkT,GAAG,CAACtN,GAAG,EAAExE,CAAC,EAAEoS,KAAK,CAACxT,MAAM,EAAE6T,GAAG,EAAED,EAAE,CAAC;EAClC,OAAOhO,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsO,KAAKA,CAAChW,IAAI,EAAEZ,EAAE,EAAE;EAC5B,IAAI,OAAOA,EAAE,IAAI,UAAU,EACvB,MAAM,aAAa;EACvB,IAAImW,IAAI,GAAG,EAAE;EACb,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;IACnB,KAAK,IAAIzU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwU,IAAI,CAACzT,MAAM,EAAE,EAAEf,CAAC,EAChCwU,IAAI,CAACxU,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC;EACD,IAAIuU,KAAK,GAAG,CAAC,CAAC;EACd,IAAIzV,CAAC,GAAGG,IAAI,CAAC8B,MAAM,GAAG,EAAE;EACxB,OAAO8N,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EAAE,EAAEA,CAAC,EAAE;IAClC,IAAI,CAACA,CAAC,IAAIG,IAAI,CAAC8B,MAAM,GAAGjC,CAAC,GAAG,KAAK,EAAE;MAC/BT,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC;MAC5B;IACJ;EACJ;EACA;EACA,IAAIyH,GAAG,GAAG8I,EAAE,CAAC3P,IAAI,EAAEH,CAAC,GAAG,CAAC,CAAC;EACzB,IAAI,CAACgH,GAAG,EACJzH,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EAChB,IAAIJ,CAAC,GAAG6H,GAAG;EACX,IAAI3D,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB,IAAIkU,CAAC,GAAG7Q,CAAC,IAAI,UAAU;EACvB,IAAI6Q,CAAC,EAAE;IACHlU,CAAC,GAAG+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IACpB,IAAI+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EACxB,MAAM,kBAAkB;IAC5Bb,CAAC,GAAG6H,GAAG,GAAG+I,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IAC1BqD,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB;EACA,IAAIoW,OAAO,GAAG,SAAAA,CAAUlV,CAAC,EAAE;IACvB,IAAIG,EAAE,GAAG4S,EAAE,CAAC9T,IAAI,EAAEkD,CAAC,EAAE6Q,CAAC,CAAC;MAAEmC,GAAG,GAAGhV,EAAE,CAAC,CAAC,CAAC;MAAEiT,EAAE,GAAGjT,EAAE,CAAC,CAAC,CAAC;MAAEkT,EAAE,GAAGlT,EAAE,CAAC,CAAC,CAAC;MAAEwL,EAAE,GAAGxL,EAAE,CAAC,CAAC,CAAC;MAAEiV,EAAE,GAAGjV,EAAE,CAAC,CAAC,CAAC;MAAEmT,GAAG,GAAGnT,EAAE,CAAC,CAAC,CAAC;MAAEJ,CAAC,GAAG+S,IAAI,CAAC7T,IAAI,EAAEqU,GAAG,CAAC;IACtHnR,CAAC,GAAGiT,EAAE;IACN,IAAIL,GAAG,GAAG,SAAAA,CAAUjW,CAAC,EAAEmD,CAAC,EAAE;MACtB,IAAInD,CAAC,EAAE;QACH2V,IAAI,CAAC,CAAC;QACNpW,EAAE,CAACS,CAAC,EAAE,IAAI,CAAC;MACf,CAAC,MACI;QACDyV,KAAK,CAAC5I,EAAE,CAAC,GAAG1J,CAAC;QACb,IAAI,CAAC,GAAE6D,GAAG,EACNzH,EAAE,CAAC,IAAI,EAAEkW,KAAK,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACY,GAAG,EACJJ,GAAG,CAAC,IAAI,EAAEzS,GAAG,CAACrD,IAAI,EAAEc,CAAC,EAAEA,CAAC,GAAGqT,EAAE,CAAC,CAAC,CAAC,KAC/B,IAAI+B,GAAG,IAAI,CAAC,EAAE;MACf,IAAIE,IAAI,GAAGpW,IAAI,CAACwD,QAAQ,CAAC1C,CAAC,EAAEA,CAAC,GAAGqT,EAAE,CAAC;MACnC,IAAIA,EAAE,GAAG,MAAM,EAAE;QACb,IAAI;UACA2B,GAAG,CAAC,IAAI,EAAE/H,WAAW,CAACqI,IAAI,EAAE,IAAIlW,EAAE,CAACkU,EAAE,CAAC,CAAC,CAAC;QAC5C,CAAC,CACD,OAAOvU,CAAC,EAAE;UACNiW,GAAG,CAACjW,CAAC,EAAE,IAAI,CAAC;QAChB;MACJ,CAAC,MAEG0V,IAAI,CAACrP,IAAI,CAAC2K,OAAO,CAACuF,IAAI,EAAE;QAAErH,IAAI,EAAEqF;MAAG,CAAC,EAAE0B,GAAG,CAAC,CAAC;IACnD,CAAC,MAEGA,GAAG,CAAC,2BAA2B,GAAGI,GAAG,EAAE,IAAI,CAAC;EACpD,CAAC;EACD,KAAK,IAAInV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,CAAC,EAAE,EAAE+B,CAAC,EAAE;IACxBkV,OAAO,CAAClV,CAAC,CAAC;EACd;EACA,OAAOyU,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASa,SAASA,CAACrW,IAAI,EAAE;EAC5B,IAAIsV,KAAK,GAAG,CAAC,CAAC;EACd,IAAIzV,CAAC,GAAGG,IAAI,CAAC8B,MAAM,GAAG,EAAE;EACxB,OAAO8N,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EAAE,EAAEA,CAAC,EAAE;IAClC,IAAI,CAACA,CAAC,IAAIG,IAAI,CAAC8B,MAAM,GAAGjC,CAAC,GAAG,KAAK,EAC7B,MAAM,kBAAkB;EAChC;EACA;EACA,IAAIb,CAAC,GAAG2Q,EAAE,CAAC3P,IAAI,EAAEH,CAAC,GAAG,CAAC,CAAC;EACvB,IAAI,CAACb,CAAC,EACF,OAAO,CAAC,CAAC;EACb,IAAIkE,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB,IAAIkU,CAAC,GAAG7Q,CAAC,IAAI,UAAU;EACvB,IAAI6Q,CAAC,EAAE;IACHlU,CAAC,GAAG+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IACpB,IAAI+P,EAAE,CAAC5P,IAAI,EAAEH,CAAC,CAAC,IAAI,SAAS,EACxB,MAAM,kBAAkB;IAC5Bb,CAAC,GAAG4Q,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;IACpBqD,CAAC,GAAG0M,EAAE,CAAC5P,IAAI,EAAEH,CAAC,GAAG,EAAE,CAAC;EACxB;EACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,CAAC,EAAE,EAAE+B,CAAC,EAAE;IACxB,IAAIG,EAAE,GAAG4S,EAAE,CAAC9T,IAAI,EAAEkD,CAAC,EAAE6Q,CAAC,CAAC;MAAEuC,GAAG,GAAGpV,EAAE,CAAC,CAAC,CAAC;MAAEiT,EAAE,GAAGjT,EAAE,CAAC,CAAC,CAAC;MAAEkT,EAAE,GAAGlT,EAAE,CAAC,CAAC,CAAC;MAAEwL,EAAE,GAAGxL,EAAE,CAAC,CAAC,CAAC;MAAEiV,EAAE,GAAGjV,EAAE,CAAC,CAAC,CAAC;MAAEmT,GAAG,GAAGnT,EAAE,CAAC,CAAC,CAAC;MAAEJ,CAAC,GAAG+S,IAAI,CAAC7T,IAAI,EAAEqU,GAAG,CAAC;IACtHnR,CAAC,GAAGiT,EAAE;IACN,IAAI,CAACG,GAAG,EACJhB,KAAK,CAAC5I,EAAE,CAAC,GAAGrJ,GAAG,CAACrD,IAAI,EAAEc,CAAC,EAAEA,CAAC,GAAGqT,EAAE,CAAC,CAAC,KAChC,IAAImC,GAAG,IAAI,CAAC,EACbhB,KAAK,CAAC5I,EAAE,CAAC,GAAGqB,WAAW,CAAC/N,IAAI,CAACwD,QAAQ,CAAC1C,CAAC,EAAEA,CAAC,GAAGqT,EAAE,CAAC,EAAE,IAAIjU,EAAE,CAACkU,EAAE,CAAC,CAAC,CAAC,KAE9D,MAAM,2BAA2B,GAAGkC,GAAG;EAC/C;EACA,OAAOhB,KAAK;AAChB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
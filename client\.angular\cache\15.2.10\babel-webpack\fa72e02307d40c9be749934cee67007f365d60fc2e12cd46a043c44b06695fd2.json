{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, Input, Output, NgModule } from '@angular/core';\nimport { EditorWatchdog } from '@ckeditor/ckeditor5-watchdog';\nimport { first } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md.\n */\n// A copy of @ckeditor/ckeditor5-utils/src/uid.js\n// A hash table of hex numbers to avoid using toString() in uid() which is costly.\n// [ '00', '01', '02', ..., 'fe', 'ff' ]\nfunction CKEditorComponent_ng_template_0_Template(rf, ctx) {}\nconst HEX_NUMBERS = new Array(256).fill(0).map((val, index) => ('0' + index.toString(16)).slice(-2));\n/**\n * Returns a unique id. The id starts with an \"e\" character and a randomly generated string of\n * 32 alphanumeric characters.\n *\n * **Note**: The characters the unique id is built from correspond to the hex number notation\n * (from \"0\" to \"9\", from \"a\" to \"f\"). In other words, each id corresponds to an \"e\" followed\n * by 16 8-bit numbers next to each other.\n *\n * @returns An unique id string.\n */\nfunction uid() {\n  // Let's create some positive random 32bit integers first.\n  //\n  // 1. Math.random() is a float between 0 and 1.\n  // 2. 0x100000000 is 2^32 = **********.\n  // 3. >>> 0 enforces integer (in JS all numbers are floating point).\n  //\n  // For instance:\n  //\t\tMath.random() * 0x100000000 = 3366450031.853859\n  // but\n  //\t\tMath.random() * 0x100000000 >>> 0 = 3366450031.\n  const r1 = Math.random() * 0x100000000 >>> 0;\n  const r2 = Math.random() * 0x100000000 >>> 0;\n  const r3 = Math.random() * 0x100000000 >>> 0;\n  const r4 = Math.random() * 0x100000000 >>> 0;\n  // Make sure that id does not start with number.\n  return 'e' + HEX_NUMBERS[r1 >> 0 & 0xFF] + HEX_NUMBERS[r1 >> 8 & 0xFF] + HEX_NUMBERS[r1 >> 16 & 0xFF] + HEX_NUMBERS[r1 >> 24 & 0xFF] + HEX_NUMBERS[r2 >> 0 & 0xFF] + HEX_NUMBERS[r2 >> 8 & 0xFF] + HEX_NUMBERS[r2 >> 16 & 0xFF] + HEX_NUMBERS[r2 >> 24 & 0xFF] + HEX_NUMBERS[r3 >> 0 & 0xFF] + HEX_NUMBERS[r3 >> 8 & 0xFF] + HEX_NUMBERS[r3 >> 16 & 0xFF] + HEX_NUMBERS[r3 >> 24 & 0xFF] + HEX_NUMBERS[r4 >> 0 & 0xFF] + HEX_NUMBERS[r4 >> 8 & 0xFF] + HEX_NUMBERS[r4 >> 16 & 0xFF] + HEX_NUMBERS[r4 >> 24 & 0xFF];\n}\nconst ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID = 'Lock from Angular integration (@ckeditor/ckeditor5-angular)';\nclass CKEditorComponent {\n  constructor(elementRef, ngZone) {\n    /**\n     * The configuration of the editor.\n     * See https://ckeditor.com/docs/ckeditor5/latest/api/module_core_editor_editorconfig-EditorConfig.html\n     * to learn more.\n     */\n    this.config = {};\n    /**\n     * The initial data of the editor. Useful when not using the ngModel.\n     * See https://angular.io/api/forms/NgModel to learn more.\n     */\n    this.data = '';\n    /**\n     * Tag name of the editor component.\n     *\n     * The default tag is 'div'.\n     */\n    this.tagName = 'div';\n    /**\n     * Allows disabling the two-way data binding mechanism. Disabling it can boost performance for large documents.\n     *\n     * When a component is connected using the [(ngModel)] or [formControl] directives and this value is set to true then none of the data\n     * will ever be synchronized.\n     *\n     * An integrator must call `editor.data.get()` manually once the application needs the editor's data.\n     * An editor instance can be received in the `ready()` callback.\n     */\n    this.disableTwoWayDataBinding = false;\n    /**\n     * Fires when the editor is ready. It corresponds with the `editor#ready`\n     * https://ckeditor.com/docs/ckeditor5/latest/api/module_core_editor_editor-Editor.html#event-ready\n     * event.\n     */\n    this.ready = new EventEmitter();\n    /**\n     * Fires when the content of the editor has changed. It corresponds with the `editor.model.document#change`\n     * https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_model_document-Document.html#event-change\n     * event.\n     */\n    this.change = new EventEmitter();\n    /**\n     * Fires when the editing view of the editor is blurred. It corresponds with the `editor.editing.view.document#blur`\n     * https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_view_document-Document.html#event-event:blur\n     * event.\n     */\n    this.blur = new EventEmitter();\n    /**\n     * Fires when the editing view of the editor is focused. It corresponds with the `editor.editing.view.document#focus`\n     * https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_view_document-Document.html#event-event:focus\n     * event.\n     */\n    this.focus = new EventEmitter();\n    /**\n     * Fires when the editor component crashes.\n     */\n    this.error = new EventEmitter();\n    /**\n     * If the component is read–only before the editor instance is created, it remembers that state,\n     * so the editor can become read–only once it is ready.\n     */\n    this.initiallyDisabled = false;\n    /**\n     * A lock flag preventing from calling the `cvaOnChange()` during setting editor data.\n     */\n    this.isEditorSettingData = false;\n    this.id = uid();\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    // To avoid issues with the community typings and CKEditor 5, let's treat window as any. See #342.\n    const {\n      CKEDITOR_VERSION\n    } = window;\n    if (CKEDITOR_VERSION) {\n      const [major] = CKEDITOR_VERSION.split('.').map(Number);\n      if (major < 37) {\n        console.warn('The <CKEditor> component requires using CKEditor 5 in version 37 or higher.');\n      }\n    } else {\n      console.warn('Cannot find the \"CKEDITOR_VERSION\" in the \"window\" scope.');\n    }\n  }\n  /**\n   * When set `true`, the editor becomes read-only.\n   * See https://ckeditor.com/docs/ckeditor5/latest/api/module_core_editor_editor-Editor.html#member-isReadOnly\n   * to learn more.\n   */\n  set disabled(isDisabled) {\n    this.setDisabledState(isDisabled);\n  }\n  get disabled() {\n    if (this.editorInstance) {\n      return this.editorInstance.isReadOnly;\n    }\n    return this.initiallyDisabled;\n  }\n  /**\n   * The instance of the editor created by this component.\n   */\n  get editorInstance() {\n    let editorWatchdog = this.editorWatchdog;\n    if (this.watchdog) {\n      // Temporarily use the `_watchdogs` internal map as the `getItem()` method throws\n      // an error when the item is not registered yet.\n      // See https://github.com/ckeditor/ckeditor5-angular/issues/177.\n      // TODO should be able to change when new chages in Watcdog are released.\n      editorWatchdog = this.watchdog._watchdogs.get(this.id);\n    }\n    if (editorWatchdog) {\n      return editorWatchdog.editor;\n    }\n    return null;\n  }\n  // Implementing the OnChanges interface. Whenever the `data` property is changed, update the editor content.\n  ngOnChanges(changes) {\n    if (Object.prototype.hasOwnProperty.call(changes, 'data') && changes.data && !changes.data.isFirstChange()) {\n      this.writeValue(changes.data.currentValue);\n    }\n  }\n  // Implementing the AfterViewInit interface.\n  ngAfterViewInit() {\n    this.attachToWatchdog();\n  }\n  // Implementing the OnDestroy interface.\n  ngOnDestroy() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.watchdog) {\n        yield _this.watchdog.remove(_this.id);\n      } else if (_this.editorWatchdog && _this.editorWatchdog.editor) {\n        yield _this.editorWatchdog.destroy();\n        _this.editorWatchdog = undefined;\n      }\n    })();\n  }\n  // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n  writeValue(value) {\n    // This method is called with the `null` value when the form resets.\n    // A component's responsibility is to restore to the initial state.\n    if (value === null) {\n      value = '';\n    }\n    // If already initialized.\n    if (this.editorInstance) {\n      // The lock mechanism prevents from calling `cvaOnChange()` during changing\n      // the editor state. See #139\n      this.isEditorSettingData = true;\n      this.editorInstance.data.set(value);\n      this.isEditorSettingData = false;\n    }\n    // If not, wait for it to be ready; store the data.\n    else {\n      // If the editor element is already available, then update its content.\n      this.data = value;\n      // If not, then wait until it is ready\n      // and change data only for the first `ready` event.\n      this.ready.pipe(first()).subscribe(editor => {\n        editor.data.set(this.data);\n      });\n    }\n  }\n  // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n  registerOnChange(callback) {\n    this.cvaOnChange = callback;\n  }\n  // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n  registerOnTouched(callback) {\n    this.cvaOnTouched = callback;\n  }\n  // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n  setDisabledState(isDisabled) {\n    // If already initialized.\n    if (this.editorInstance) {\n      if (isDisabled) {\n        this.editorInstance.enableReadOnlyMode(ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID);\n      } else {\n        this.editorInstance.disableReadOnlyMode(ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID);\n      }\n    }\n    // Store the state anyway to use it once the editor is created.\n    this.initiallyDisabled = isDisabled;\n  }\n  /**\n   * Creates the editor instance, sets initial editor data, then integrates\n   * the editor with the Angular component. This method does not use the `editor.data.set()`\n   * because of the issue in the collaboration mode (#6).\n   */\n  attachToWatchdog() {\n    var _this2 = this;\n    // TODO: elementOrData parameter type can be simplified to HTMLElemen after templated Watchdog will be released.\n    const creator = (elementOrData, config) => {\n      return this.ngZone.runOutsideAngular( /*#__PURE__*/_asyncToGenerator(function* () {\n        _this2.elementRef.nativeElement.appendChild(elementOrData);\n        const editor = yield _this2.editor.create(elementOrData, config);\n        if (_this2.initiallyDisabled) {\n          editor.enableReadOnlyMode(ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID);\n        }\n        _this2.ngZone.run(() => {\n          _this2.ready.emit(editor);\n        });\n        _this2.setUpEditorEvents(editor);\n        return editor;\n      }));\n    };\n    const destructor = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (editor) {\n        yield editor.destroy();\n        _this2.elementRef.nativeElement.removeChild(_this2.editorElement);\n      });\n      return function destructor(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    const emitError = () => {\n      this.ngZone.run(() => {\n        this.error.emit();\n      });\n    };\n    const element = document.createElement(this.tagName);\n    const config = this.getConfig();\n    this.editorElement = element;\n    // Based on the presence of the watchdog decide how to initialize the editor.\n    if (this.watchdog) {\n      // When the context watchdog is passed add the new item to it based on the passed configuration.\n      this.watchdog.add({\n        id: this.id,\n        type: 'editor',\n        creator,\n        destructor,\n        sourceElementOrData: element,\n        config\n      });\n      this.watchdog.on('itemError', (_, {\n        itemId\n      }) => {\n        if (itemId === this.id) {\n          emitError();\n        }\n      });\n    } else {\n      // In the other case create the watchdog by hand to keep the editor running.\n      const editorWatchdog = new EditorWatchdog(this.editor, this.editorWatchdogConfig);\n      editorWatchdog.setCreator(creator);\n      editorWatchdog.setDestructor(destructor);\n      editorWatchdog.on('error', emitError);\n      this.editorWatchdog = editorWatchdog;\n      this.editorWatchdog.create(element, config);\n    }\n  }\n  getConfig() {\n    if (this.data && this.config.initialData) {\n      throw new Error('Editor data should be provided either using `config.initialData` or `data` properties.');\n    }\n    const config = {\n      ...this.config\n    };\n    // Merge two possible ways of providing data into the `config.initialData` field.\n    const initialData = this.config.initialData || this.data;\n    if (initialData) {\n      // Define the `config.initialData` only when the initial content is specified.\n      config.initialData = initialData;\n    }\n    return config;\n  }\n  /**\n   * Integrates the editor with the component by attaching related event listeners.\n   */\n  setUpEditorEvents(editor) {\n    const modelDocument = editor.model.document;\n    const viewDocument = editor.editing.view.document;\n    modelDocument.on('change:data', evt => {\n      this.ngZone.run(() => {\n        if (this.disableTwoWayDataBinding) {\n          return;\n        }\n        if (this.cvaOnChange && !this.isEditorSettingData) {\n          const data = editor.data.get();\n          this.cvaOnChange(data);\n        }\n        this.change.emit({\n          event: evt,\n          editor\n        });\n      });\n    });\n    viewDocument.on('focus', evt => {\n      this.ngZone.run(() => {\n        this.focus.emit({\n          event: evt,\n          editor\n        });\n      });\n    });\n    viewDocument.on('blur', evt => {\n      this.ngZone.run(() => {\n        if (this.cvaOnTouched) {\n          this.cvaOnTouched();\n        }\n        this.blur.emit({\n          event: evt,\n          editor\n        });\n      });\n    });\n  }\n}\nCKEditorComponent.ɵfac = function CKEditorComponent_Factory(t) {\n  return new (t || CKEditorComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nCKEditorComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CKEditorComponent,\n  selectors: [[\"ckeditor\"]],\n  inputs: {\n    editor: \"editor\",\n    config: \"config\",\n    data: \"data\",\n    tagName: \"tagName\",\n    watchdog: \"watchdog\",\n    editorWatchdogConfig: \"editorWatchdogConfig\",\n    disableTwoWayDataBinding: \"disableTwoWayDataBinding\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    ready: \"ready\",\n    change: \"change\",\n    blur: \"blur\",\n    focus: \"focus\",\n    error: \"error\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: NG_VALUE_ACCESSOR,\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    useExisting: forwardRef(() => CKEditorComponent),\n    multi: true\n  }]), i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 0,\n  template: function CKEditorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CKEditorComponent_ng_template_0_Template, 0, 0, \"ng-template\");\n    }\n  },\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CKEditorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ckeditor',\n      template: '<ng-template></ng-template>',\n      // Integration with @angular/forms.\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        useExisting: forwardRef(() => CKEditorComponent),\n        multi: true\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    editor: [{\n      type: Input\n    }],\n    config: [{\n      type: Input\n    }],\n    data: [{\n      type: Input\n    }],\n    tagName: [{\n      type: Input\n    }],\n    watchdog: [{\n      type: Input\n    }],\n    editorWatchdogConfig: [{\n      type: Input\n    }],\n    disableTwoWayDataBinding: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    ready: [{\n      type: Output\n    }],\n    change: [{\n      type: Output\n    }],\n    blur: [{\n      type: Output\n    }],\n    focus: [{\n      type: Output\n    }],\n    error: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md.\n */\nclass CKEditorModule {}\nCKEditorModule.ɵfac = function CKEditorModule_Factory(t) {\n  return new (t || CKEditorModule)();\n};\nCKEditorModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CKEditorModule\n});\nCKEditorModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[FormsModule, CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CKEditorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [FormsModule, CommonModule],\n      declarations: [CKEditorComponent],\n      exports: [CKEditorComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CKEditorComponent, CKEditorModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "forwardRef", "Component", "Input", "Output", "NgModule", "EditorWatchdog", "first", "NG_VALUE_ACCESSOR", "FormsModule", "CommonModule", "CKEditorComponent_ng_template_0_Template", "rf", "ctx", "HEX_NUMBERS", "Array", "fill", "map", "val", "index", "toString", "slice", "uid", "r1", "Math", "random", "r2", "r3", "r4", "ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID", "CKEditorComponent", "constructor", "elementRef", "ngZone", "config", "data", "tagName", "disableTwoWayDataBinding", "ready", "change", "blur", "focus", "error", "initiallyDisabled", "isEditorSettingData", "id", "CKEDITOR_VERSION", "window", "major", "split", "Number", "console", "warn", "disabled", "isDisabled", "setDisabledState", "editorInstance", "isReadOnly", "editorWatchdog", "watchdog", "_watchdogs", "get", "editor", "ngOnChanges", "changes", "Object", "prototype", "hasOwnProperty", "call", "isFirstChange", "writeValue", "currentValue", "ngAfterViewInit", "attachToWatchdog", "ngOnDestroy", "_this", "_asyncToGenerator", "remove", "destroy", "undefined", "value", "set", "pipe", "subscribe", "registerOnChange", "callback", "cvaOnChange", "registerOnTouched", "cvaOnTouched", "enableReadOnlyMode", "disableReadOnlyMode", "_this2", "creator", "elementOrData", "runOutsideAngular", "nativeElement", "append<PERSON><PERSON><PERSON>", "create", "run", "emit", "setUpEditorEvents", "destructor", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "editor<PERSON><PERSON>", "_x", "apply", "arguments", "emitError", "element", "document", "createElement", "getConfig", "add", "type", "sourceElementOrData", "on", "_", "itemId", "editorWatchdogConfig", "setCreator", "setDestructor", "initialData", "Error", "modelDocument", "model", "viewDocument", "editing", "view", "evt", "event", "ɵfac", "CKEditorComponent_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "template", "CKEditorComponent_Template", "ɵɵtemplate", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "CKEditorModule", "CKEditorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@ckeditor/ckeditor5-angular/fesm2020/ckeditor-ckeditor5-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, Input, Output, NgModule } from '@angular/core';\nimport { EditorWatchdog } from '@ckeditor/ckeditor5-watchdog';\nimport { first } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md.\n */\n// A copy of @ckeditor/ckeditor5-utils/src/uid.js\n// A hash table of hex numbers to avoid using toString() in uid() which is costly.\n// [ '00', '01', '02', ..., 'fe', 'ff' ]\nconst HEX_NUMBERS = new Array(256).fill(0)\n    .map((val, index) => ('0' + (index).toString(16)).slice(-2));\n/**\n * Returns a unique id. The id starts with an \"e\" character and a randomly generated string of\n * 32 alphanumeric characters.\n *\n * **Note**: The characters the unique id is built from correspond to the hex number notation\n * (from \"0\" to \"9\", from \"a\" to \"f\"). In other words, each id corresponds to an \"e\" followed\n * by 16 8-bit numbers next to each other.\n *\n * @returns An unique id string.\n */\nfunction uid() {\n    // Let's create some positive random 32bit integers first.\n    //\n    // 1. Math.random() is a float between 0 and 1.\n    // 2. 0x100000000 is 2^32 = **********.\n    // 3. >>> 0 enforces integer (in JS all numbers are floating point).\n    //\n    // For instance:\n    //\t\tMath.random() * 0x100000000 = 3366450031.853859\n    // but\n    //\t\tMath.random() * 0x100000000 >>> 0 = 3366450031.\n    const r1 = Math.random() * 0x100000000 >>> 0;\n    const r2 = Math.random() * 0x100000000 >>> 0;\n    const r3 = Math.random() * 0x100000000 >>> 0;\n    const r4 = Math.random() * 0x100000000 >>> 0;\n    // Make sure that id does not start with number.\n    return 'e' +\n        HEX_NUMBERS[r1 >> 0 & 0xFF] +\n        HEX_NUMBERS[r1 >> 8 & 0xFF] +\n        HEX_NUMBERS[r1 >> 16 & 0xFF] +\n        HEX_NUMBERS[r1 >> 24 & 0xFF] +\n        HEX_NUMBERS[r2 >> 0 & 0xFF] +\n        HEX_NUMBERS[r2 >> 8 & 0xFF] +\n        HEX_NUMBERS[r2 >> 16 & 0xFF] +\n        HEX_NUMBERS[r2 >> 24 & 0xFF] +\n        HEX_NUMBERS[r3 >> 0 & 0xFF] +\n        HEX_NUMBERS[r3 >> 8 & 0xFF] +\n        HEX_NUMBERS[r3 >> 16 & 0xFF] +\n        HEX_NUMBERS[r3 >> 24 & 0xFF] +\n        HEX_NUMBERS[r4 >> 0 & 0xFF] +\n        HEX_NUMBERS[r4 >> 8 & 0xFF] +\n        HEX_NUMBERS[r4 >> 16 & 0xFF] +\n        HEX_NUMBERS[r4 >> 24 & 0xFF];\n}\n\nconst ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID = 'Lock from Angular integration (@ckeditor/ckeditor5-angular)';\nclass CKEditorComponent {\n    constructor(elementRef, ngZone) {\n        /**\n         * The configuration of the editor.\n         * See https://ckeditor.com/docs/ckeditor5/latest/api/module_core_editor_editorconfig-EditorConfig.html\n         * to learn more.\n         */\n        this.config = {};\n        /**\n         * The initial data of the editor. Useful when not using the ngModel.\n         * See https://angular.io/api/forms/NgModel to learn more.\n         */\n        this.data = '';\n        /**\n         * Tag name of the editor component.\n         *\n         * The default tag is 'div'.\n         */\n        this.tagName = 'div';\n        /**\n         * Allows disabling the two-way data binding mechanism. Disabling it can boost performance for large documents.\n         *\n         * When a component is connected using the [(ngModel)] or [formControl] directives and this value is set to true then none of the data\n         * will ever be synchronized.\n         *\n         * An integrator must call `editor.data.get()` manually once the application needs the editor's data.\n         * An editor instance can be received in the `ready()` callback.\n         */\n        this.disableTwoWayDataBinding = false;\n        /**\n         * Fires when the editor is ready. It corresponds with the `editor#ready`\n         * https://ckeditor.com/docs/ckeditor5/latest/api/module_core_editor_editor-Editor.html#event-ready\n         * event.\n         */\n        this.ready = new EventEmitter();\n        /**\n         * Fires when the content of the editor has changed. It corresponds with the `editor.model.document#change`\n         * https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_model_document-Document.html#event-change\n         * event.\n         */\n        this.change = new EventEmitter();\n        /**\n         * Fires when the editing view of the editor is blurred. It corresponds with the `editor.editing.view.document#blur`\n         * https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_view_document-Document.html#event-event:blur\n         * event.\n         */\n        this.blur = new EventEmitter();\n        /**\n         * Fires when the editing view of the editor is focused. It corresponds with the `editor.editing.view.document#focus`\n         * https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_view_document-Document.html#event-event:focus\n         * event.\n         */\n        this.focus = new EventEmitter();\n        /**\n         * Fires when the editor component crashes.\n         */\n        this.error = new EventEmitter();\n        /**\n         * If the component is read–only before the editor instance is created, it remembers that state,\n         * so the editor can become read–only once it is ready.\n         */\n        this.initiallyDisabled = false;\n        /**\n         * A lock flag preventing from calling the `cvaOnChange()` during setting editor data.\n         */\n        this.isEditorSettingData = false;\n        this.id = uid();\n        this.ngZone = ngZone;\n        this.elementRef = elementRef;\n        // To avoid issues with the community typings and CKEditor 5, let's treat window as any. See #342.\n        const { CKEDITOR_VERSION } = window;\n        if (CKEDITOR_VERSION) {\n            const [major] = CKEDITOR_VERSION.split('.').map(Number);\n            if (major < 37) {\n                console.warn('The <CKEditor> component requires using CKEditor 5 in version 37 or higher.');\n            }\n        }\n        else {\n            console.warn('Cannot find the \"CKEDITOR_VERSION\" in the \"window\" scope.');\n        }\n    }\n    /**\n     * When set `true`, the editor becomes read-only.\n     * See https://ckeditor.com/docs/ckeditor5/latest/api/module_core_editor_editor-Editor.html#member-isReadOnly\n     * to learn more.\n     */\n    set disabled(isDisabled) {\n        this.setDisabledState(isDisabled);\n    }\n    get disabled() {\n        if (this.editorInstance) {\n            return this.editorInstance.isReadOnly;\n        }\n        return this.initiallyDisabled;\n    }\n    /**\n     * The instance of the editor created by this component.\n     */\n    get editorInstance() {\n        let editorWatchdog = this.editorWatchdog;\n        if (this.watchdog) {\n            // Temporarily use the `_watchdogs` internal map as the `getItem()` method throws\n            // an error when the item is not registered yet.\n            // See https://github.com/ckeditor/ckeditor5-angular/issues/177.\n            // TODO should be able to change when new chages in Watcdog are released.\n            editorWatchdog = this.watchdog._watchdogs.get(this.id);\n        }\n        if (editorWatchdog) {\n            return editorWatchdog.editor;\n        }\n        return null;\n    }\n    // Implementing the OnChanges interface. Whenever the `data` property is changed, update the editor content.\n    ngOnChanges(changes) {\n        if (Object.prototype.hasOwnProperty.call(changes, 'data') && changes.data && !changes.data.isFirstChange()) {\n            this.writeValue(changes.data.currentValue);\n        }\n    }\n    // Implementing the AfterViewInit interface.\n    ngAfterViewInit() {\n        this.attachToWatchdog();\n    }\n    // Implementing the OnDestroy interface.\n    async ngOnDestroy() {\n        if (this.watchdog) {\n            await this.watchdog.remove(this.id);\n        }\n        else if (this.editorWatchdog && this.editorWatchdog.editor) {\n            await this.editorWatchdog.destroy();\n            this.editorWatchdog = undefined;\n        }\n    }\n    // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n    writeValue(value) {\n        // This method is called with the `null` value when the form resets.\n        // A component's responsibility is to restore to the initial state.\n        if (value === null) {\n            value = '';\n        }\n        // If already initialized.\n        if (this.editorInstance) {\n            // The lock mechanism prevents from calling `cvaOnChange()` during changing\n            // the editor state. See #139\n            this.isEditorSettingData = true;\n            this.editorInstance.data.set(value);\n            this.isEditorSettingData = false;\n        }\n        // If not, wait for it to be ready; store the data.\n        else {\n            // If the editor element is already available, then update its content.\n            this.data = value;\n            // If not, then wait until it is ready\n            // and change data only for the first `ready` event.\n            this.ready\n                .pipe(first())\n                .subscribe(editor => {\n                editor.data.set(this.data);\n            });\n        }\n    }\n    // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n    registerOnChange(callback) {\n        this.cvaOnChange = callback;\n    }\n    // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n    registerOnTouched(callback) {\n        this.cvaOnTouched = callback;\n    }\n    // Implementing the ControlValueAccessor interface (only when binding to ngModel).\n    setDisabledState(isDisabled) {\n        // If already initialized.\n        if (this.editorInstance) {\n            if (isDisabled) {\n                this.editorInstance.enableReadOnlyMode(ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID);\n            }\n            else {\n                this.editorInstance.disableReadOnlyMode(ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID);\n            }\n        }\n        // Store the state anyway to use it once the editor is created.\n        this.initiallyDisabled = isDisabled;\n    }\n    /**\n     * Creates the editor instance, sets initial editor data, then integrates\n     * the editor with the Angular component. This method does not use the `editor.data.set()`\n     * because of the issue in the collaboration mode (#6).\n     */\n    attachToWatchdog() {\n        // TODO: elementOrData parameter type can be simplified to HTMLElemen after templated Watchdog will be released.\n        const creator = ((elementOrData, config) => {\n            return this.ngZone.runOutsideAngular(async () => {\n                this.elementRef.nativeElement.appendChild(elementOrData);\n                const editor = await this.editor.create(elementOrData, config);\n                if (this.initiallyDisabled) {\n                    editor.enableReadOnlyMode(ANGULAR_INTEGRATION_READ_ONLY_LOCK_ID);\n                }\n                this.ngZone.run(() => {\n                    this.ready.emit(editor);\n                });\n                this.setUpEditorEvents(editor);\n                return editor;\n            });\n        });\n        const destructor = async (editor) => {\n            await editor.destroy();\n            this.elementRef.nativeElement.removeChild(this.editorElement);\n        };\n        const emitError = () => {\n            this.ngZone.run(() => {\n                this.error.emit();\n            });\n        };\n        const element = document.createElement(this.tagName);\n        const config = this.getConfig();\n        this.editorElement = element;\n        // Based on the presence of the watchdog decide how to initialize the editor.\n        if (this.watchdog) {\n            // When the context watchdog is passed add the new item to it based on the passed configuration.\n            this.watchdog.add({\n                id: this.id,\n                type: 'editor',\n                creator,\n                destructor,\n                sourceElementOrData: element,\n                config\n            });\n            this.watchdog.on('itemError', (_, { itemId }) => {\n                if (itemId === this.id) {\n                    emitError();\n                }\n            });\n        }\n        else {\n            // In the other case create the watchdog by hand to keep the editor running.\n            const editorWatchdog = new EditorWatchdog(this.editor, this.editorWatchdogConfig);\n            editorWatchdog.setCreator(creator);\n            editorWatchdog.setDestructor(destructor);\n            editorWatchdog.on('error', emitError);\n            this.editorWatchdog = editorWatchdog;\n            this.editorWatchdog.create(element, config);\n        }\n    }\n    getConfig() {\n        if (this.data && this.config.initialData) {\n            throw new Error('Editor data should be provided either using `config.initialData` or `data` properties.');\n        }\n        const config = { ...this.config };\n        // Merge two possible ways of providing data into the `config.initialData` field.\n        const initialData = this.config.initialData || this.data;\n        if (initialData) {\n            // Define the `config.initialData` only when the initial content is specified.\n            config.initialData = initialData;\n        }\n        return config;\n    }\n    /**\n     * Integrates the editor with the component by attaching related event listeners.\n     */\n    setUpEditorEvents(editor) {\n        const modelDocument = editor.model.document;\n        const viewDocument = editor.editing.view.document;\n        modelDocument.on('change:data', evt => {\n            this.ngZone.run(() => {\n                if (this.disableTwoWayDataBinding) {\n                    return;\n                }\n                if (this.cvaOnChange && !this.isEditorSettingData) {\n                    const data = editor.data.get();\n                    this.cvaOnChange(data);\n                }\n                this.change.emit({ event: evt, editor });\n            });\n        });\n        viewDocument.on('focus', evt => {\n            this.ngZone.run(() => {\n                this.focus.emit({ event: evt, editor });\n            });\n        });\n        viewDocument.on('blur', evt => {\n            this.ngZone.run(() => {\n                if (this.cvaOnTouched) {\n                    this.cvaOnTouched();\n                }\n                this.blur.emit({ event: evt, editor });\n            });\n        });\n    }\n}\nCKEditorComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.4.0\", ngImport: i0, type: CKEditorComponent, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nCKEditorComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.4.0\", type: CKEditorComponent, selector: \"ckeditor\", inputs: { editor: \"editor\", config: \"config\", data: \"data\", tagName: \"tagName\", watchdog: \"watchdog\", editorWatchdogConfig: \"editorWatchdogConfig\", disableTwoWayDataBinding: \"disableTwoWayDataBinding\", disabled: \"disabled\" }, outputs: { ready: \"ready\", change: \"change\", blur: \"blur\", focus: \"focus\", error: \"error\" }, providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            useExisting: forwardRef(() => CKEditorComponent),\n            multi: true\n        }\n    ], usesOnChanges: true, ngImport: i0, template: '<ng-template></ng-template>', isInline: true });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.4.0\", ngImport: i0, type: CKEditorComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ckeditor',\n                    template: '<ng-template></ng-template>',\n                    // Integration with @angular/forms.\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                            useExisting: forwardRef(() => CKEditorComponent),\n                            multi: true\n                        }\n                    ]\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { editor: [{\n                type: Input\n            }], config: [{\n                type: Input\n            }], data: [{\n                type: Input\n            }], tagName: [{\n                type: Input\n            }], watchdog: [{\n                type: Input\n            }], editorWatchdogConfig: [{\n                type: Input\n            }], disableTwoWayDataBinding: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], ready: [{\n                type: Output\n            }], change: [{\n                type: Output\n            }], blur: [{\n                type: Output\n            }], focus: [{\n                type: Output\n            }], error: [{\n                type: Output\n            }] } });\n\n/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md.\n */\nclass CKEditorModule {\n}\nCKEditorModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.4.0\", ngImport: i0, type: CKEditorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCKEditorModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.4.0\", ngImport: i0, type: CKEditorModule, declarations: [CKEditorComponent], imports: [FormsModule, CommonModule], exports: [CKEditorComponent] });\nCKEditorModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.4.0\", ngImport: i0, type: CKEditorModule, imports: [[FormsModule, CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.4.0\", ngImport: i0, type: CKEditorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [FormsModule, CommonModule],\n                    declarations: [CKEditorComponent],\n                    exports: [CKEditorComponent]\n                }]\n        }] });\n\n/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CKEditorComponent, CKEditorModule };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC5F,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,gBAAgB;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;AACA,MAAMC,WAAW,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CACrCC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK,CAAC,GAAG,GAAIA,KAAK,CAAEC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAAA,EAAG;EACX;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC;EAC5C,MAAMC,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC;EAC5C,MAAME,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC;EAC5C,MAAMG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC;EAC5C;EACA,OAAO,GAAG,GACNX,WAAW,CAACS,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3BT,WAAW,CAACS,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3BT,WAAW,CAACS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5BT,WAAW,CAACS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5BT,WAAW,CAACY,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3BZ,WAAW,CAACY,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3BZ,WAAW,CAACY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5BZ,WAAW,CAACY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5BZ,WAAW,CAACa,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3Bb,WAAW,CAACa,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3Bb,WAAW,CAACa,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5Bb,WAAW,CAACa,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5Bb,WAAW,CAACc,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3Bd,WAAW,CAACc,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAC3Bd,WAAW,CAACc,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAC5Bd,WAAW,CAACc,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACpC;AAEA,MAAMC,qCAAqC,GAAG,6DAA6D;AAC3G,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAE;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,IAAItC,YAAY,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACuC,MAAM,GAAG,IAAIvC,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwC,IAAI,GAAG,IAAIxC,YAAY,CAAC,CAAC;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyC,KAAK,GAAG,IAAIzC,YAAY,CAAC,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAAC0C,KAAK,GAAG,IAAI1C,YAAY,CAAC,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAAC2C,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,EAAE,GAAGvB,GAAG,CAAC,CAAC;IACf,IAAI,CAACW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B;IACA,MAAM;MAAEc;IAAiB,CAAC,GAAGC,MAAM;IACnC,IAAID,gBAAgB,EAAE;MAClB,MAAM,CAACE,KAAK,CAAC,GAAGF,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAAChC,GAAG,CAACiC,MAAM,CAAC;MACvD,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZG,OAAO,CAACC,IAAI,CAAC,6EAA6E,CAAC;MAC/F;IACJ,CAAC,MACI;MACDD,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;IAC7E;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,QAAQA,CAACC,UAAU,EAAE;IACrB,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;EACrC;EACA,IAAID,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAACG,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc,CAACC,UAAU;IACzC;IACA,OAAO,IAAI,CAACd,iBAAiB;EACjC;EACA;AACJ;AACA;EACI,IAAIa,cAAcA,CAAA,EAAG;IACjB,IAAIE,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf;MACA;MACA;MACA;MACAD,cAAc,GAAG,IAAI,CAACC,QAAQ,CAACC,UAAU,CAACC,GAAG,CAAC,IAAI,CAAChB,EAAE,CAAC;IAC1D;IACA,IAAIa,cAAc,EAAE;MAChB,OAAOA,cAAc,CAACI,MAAM;IAChC;IACA,OAAO,IAAI;EACf;EACA;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,OAAO,EAAE,MAAM,CAAC,IAAIA,OAAO,CAAC7B,IAAI,IAAI,CAAC6B,OAAO,CAAC7B,IAAI,CAACkC,aAAa,CAAC,CAAC,EAAE;MACxG,IAAI,CAACC,UAAU,CAACN,OAAO,CAAC7B,IAAI,CAACoC,YAAY,CAAC;IAC9C;EACJ;EACA;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EACA;EACMC,WAAWA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAID,KAAI,CAAChB,QAAQ,EAAE;QACf,MAAMgB,KAAI,CAAChB,QAAQ,CAACkB,MAAM,CAACF,KAAI,CAAC9B,EAAE,CAAC;MACvC,CAAC,MACI,IAAI8B,KAAI,CAACjB,cAAc,IAAIiB,KAAI,CAACjB,cAAc,CAACI,MAAM,EAAE;QACxD,MAAMa,KAAI,CAACjB,cAAc,CAACoB,OAAO,CAAC,CAAC;QACnCH,KAAI,CAACjB,cAAc,GAAGqB,SAAS;MACnC;IAAC;EACL;EACA;EACAT,UAAUA,CAACU,KAAK,EAAE;IACd;IACA;IACA,IAAIA,KAAK,KAAK,IAAI,EAAE;MAChBA,KAAK,GAAG,EAAE;IACd;IACA;IACA,IAAI,IAAI,CAACxB,cAAc,EAAE;MACrB;MACA;MACA,IAAI,CAACZ,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACY,cAAc,CAACrB,IAAI,CAAC8C,GAAG,CAACD,KAAK,CAAC;MACnC,IAAI,CAACpC,mBAAmB,GAAG,KAAK;IACpC;IACA;IAAA,KACK;MACD;MACA,IAAI,CAACT,IAAI,GAAG6C,KAAK;MACjB;MACA;MACA,IAAI,CAAC1C,KAAK,CACL4C,IAAI,CAAC3E,KAAK,CAAC,CAAC,CAAC,CACb4E,SAAS,CAACrB,MAAM,IAAI;QACrBA,MAAM,CAAC3B,IAAI,CAAC8C,GAAG,CAAC,IAAI,CAAC9C,IAAI,CAAC;MAC9B,CAAC,CAAC;IACN;EACJ;EACA;EACAiD,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACC,WAAW,GAAGD,QAAQ;EAC/B;EACA;EACAE,iBAAiBA,CAACF,QAAQ,EAAE;IACxB,IAAI,CAACG,YAAY,GAAGH,QAAQ;EAChC;EACA;EACA9B,gBAAgBA,CAACD,UAAU,EAAE;IACzB;IACA,IAAI,IAAI,CAACE,cAAc,EAAE;MACrB,IAAIF,UAAU,EAAE;QACZ,IAAI,CAACE,cAAc,CAACiC,kBAAkB,CAAC5D,qCAAqC,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAAC2B,cAAc,CAACkC,mBAAmB,CAAC7D,qCAAqC,CAAC;MAClF;IACJ;IACA;IACA,IAAI,CAACc,iBAAiB,GAAGW,UAAU;EACvC;EACA;AACJ;AACA;AACA;AACA;EACImB,gBAAgBA,CAAA,EAAG;IAAA,IAAAkB,MAAA;IACf;IACA,MAAMC,OAAO,GAAIA,CAACC,aAAa,EAAE3D,MAAM,KAAK;MACxC,OAAO,IAAI,CAACD,MAAM,CAAC6D,iBAAiB,eAAAlB,iBAAA,CAAC,aAAY;QAC7Ce,MAAI,CAAC3D,UAAU,CAAC+D,aAAa,CAACC,WAAW,CAACH,aAAa,CAAC;QACxD,MAAM/B,MAAM,SAAS6B,MAAI,CAAC7B,MAAM,CAACmC,MAAM,CAACJ,aAAa,EAAE3D,MAAM,CAAC;QAC9D,IAAIyD,MAAI,CAAChD,iBAAiB,EAAE;UACxBmB,MAAM,CAAC2B,kBAAkB,CAAC5D,qCAAqC,CAAC;QACpE;QACA8D,MAAI,CAAC1D,MAAM,CAACiE,GAAG,CAAC,MAAM;UAClBP,MAAI,CAACrD,KAAK,CAAC6D,IAAI,CAACrC,MAAM,CAAC;QAC3B,CAAC,CAAC;QACF6B,MAAI,CAACS,iBAAiB,CAACtC,MAAM,CAAC;QAC9B,OAAOA,MAAM;MACjB,CAAC,EAAC;IACN,CAAE;IACF,MAAMuC,UAAU;MAAA,IAAAC,KAAA,GAAA1B,iBAAA,CAAG,WAAOd,MAAM,EAAK;QACjC,MAAMA,MAAM,CAACgB,OAAO,CAAC,CAAC;QACtBa,MAAI,CAAC3D,UAAU,CAAC+D,aAAa,CAACQ,WAAW,CAACZ,MAAI,CAACa,aAAa,CAAC;MACjE,CAAC;MAAA,gBAHKH,UAAUA,CAAAI,EAAA;QAAA,OAAAH,KAAA,CAAAI,KAAA,OAAAC,SAAA;MAAA;IAAA,GAGf;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,IAAI,CAAC3E,MAAM,CAACiE,GAAG,CAAC,MAAM;QAClB,IAAI,CAACxD,KAAK,CAACyD,IAAI,CAAC,CAAC;MACrB,CAAC,CAAC;IACN,CAAC;IACD,MAAMU,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC3E,OAAO,CAAC;IACpD,MAAMF,MAAM,GAAG,IAAI,CAAC8E,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACR,aAAa,GAAGK,OAAO;IAC5B;IACA,IAAI,IAAI,CAAClD,QAAQ,EAAE;MACf;MACA,IAAI,CAACA,QAAQ,CAACsD,GAAG,CAAC;QACdpE,EAAE,EAAE,IAAI,CAACA,EAAE;QACXqE,IAAI,EAAE,QAAQ;QACdtB,OAAO;QACPS,UAAU;QACVc,mBAAmB,EAAEN,OAAO;QAC5B3E;MACJ,CAAC,CAAC;MACF,IAAI,CAACyB,QAAQ,CAACyD,EAAE,CAAC,WAAW,EAAE,CAACC,CAAC,EAAE;QAAEC;MAAO,CAAC,KAAK;QAC7C,IAAIA,MAAM,KAAK,IAAI,CAACzE,EAAE,EAAE;UACpB+D,SAAS,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA,MAAMlD,cAAc,GAAG,IAAIpD,cAAc,CAAC,IAAI,CAACwD,MAAM,EAAE,IAAI,CAACyD,oBAAoB,CAAC;MACjF7D,cAAc,CAAC8D,UAAU,CAAC5B,OAAO,CAAC;MAClClC,cAAc,CAAC+D,aAAa,CAACpB,UAAU,CAAC;MACxC3C,cAAc,CAAC0D,EAAE,CAAC,OAAO,EAAER,SAAS,CAAC;MACrC,IAAI,CAAClD,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACA,cAAc,CAACuC,MAAM,CAACY,OAAO,EAAE3E,MAAM,CAAC;IAC/C;EACJ;EACA8E,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC7E,IAAI,IAAI,IAAI,CAACD,MAAM,CAACwF,WAAW,EAAE;MACtC,MAAM,IAAIC,KAAK,CAAC,wFAAwF,CAAC;IAC7G;IACA,MAAMzF,MAAM,GAAG;MAAE,GAAG,IAAI,CAACA;IAAO,CAAC;IACjC;IACA,MAAMwF,WAAW,GAAG,IAAI,CAACxF,MAAM,CAACwF,WAAW,IAAI,IAAI,CAACvF,IAAI;IACxD,IAAIuF,WAAW,EAAE;MACb;MACAxF,MAAM,CAACwF,WAAW,GAAGA,WAAW;IACpC;IACA,OAAOxF,MAAM;EACjB;EACA;AACJ;AACA;EACIkE,iBAAiBA,CAACtC,MAAM,EAAE;IACtB,MAAM8D,aAAa,GAAG9D,MAAM,CAAC+D,KAAK,CAACf,QAAQ;IAC3C,MAAMgB,YAAY,GAAGhE,MAAM,CAACiE,OAAO,CAACC,IAAI,CAAClB,QAAQ;IACjDc,aAAa,CAACR,EAAE,CAAC,aAAa,EAAEa,GAAG,IAAI;MACnC,IAAI,CAAChG,MAAM,CAACiE,GAAG,CAAC,MAAM;QAClB,IAAI,IAAI,CAAC7D,wBAAwB,EAAE;UAC/B;QACJ;QACA,IAAI,IAAI,CAACiD,WAAW,IAAI,CAAC,IAAI,CAAC1C,mBAAmB,EAAE;UAC/C,MAAMT,IAAI,GAAG2B,MAAM,CAAC3B,IAAI,CAAC0B,GAAG,CAAC,CAAC;UAC9B,IAAI,CAACyB,WAAW,CAACnD,IAAI,CAAC;QAC1B;QACA,IAAI,CAACI,MAAM,CAAC4D,IAAI,CAAC;UAAE+B,KAAK,EAAED,GAAG;UAAEnE;QAAO,CAAC,CAAC;MAC5C,CAAC,CAAC;IACN,CAAC,CAAC;IACFgE,YAAY,CAACV,EAAE,CAAC,OAAO,EAAEa,GAAG,IAAI;MAC5B,IAAI,CAAChG,MAAM,CAACiE,GAAG,CAAC,MAAM;QAClB,IAAI,CAACzD,KAAK,CAAC0D,IAAI,CAAC;UAAE+B,KAAK,EAAED,GAAG;UAAEnE;QAAO,CAAC,CAAC;MAC3C,CAAC,CAAC;IACN,CAAC,CAAC;IACFgE,YAAY,CAACV,EAAE,CAAC,MAAM,EAAEa,GAAG,IAAI;MAC3B,IAAI,CAAChG,MAAM,CAACiE,GAAG,CAAC,MAAM;QAClB,IAAI,IAAI,CAACV,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAAC,CAAC;QACvB;QACA,IAAI,CAAChD,IAAI,CAAC2D,IAAI,CAAC;UAAE+B,KAAK,EAAED,GAAG;UAAEnE;QAAO,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AACAhC,iBAAiB,CAACqG,IAAI,YAAAC,0BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFvG,iBAAiB,EAA3B/B,EAAE,CAAAuI,iBAAA,CAA2CvI,EAAE,CAACwI,UAAU,GAA1DxI,EAAE,CAAAuI,iBAAA,CAAqEvI,EAAE,CAACyI,MAAM;AAAA,CAA4C;AAChO1G,iBAAiB,CAAC2G,IAAI,kBAD8E1I,EAAE,CAAA2I,iBAAA;EAAAxB,IAAA,EACJpF,iBAAiB;EAAA6G,SAAA;EAAAC,MAAA;IAAA9E,MAAA;IAAA5B,MAAA;IAAAC,IAAA;IAAAC,OAAA;IAAAuB,QAAA;IAAA4D,oBAAA;IAAAlF,wBAAA;IAAAgB,QAAA;EAAA;EAAAwF,OAAA;IAAAvG,KAAA;IAAAC,MAAA;IAAAC,IAAA;IAAAC,KAAA;IAAAC,KAAA;EAAA;EAAAoG,QAAA,GADf/I,EAAE,CAAAgJ,kBAAA,CAC+W,CAC7c;IACIC,OAAO,EAAExI,iBAAiB;IAC1B;IACAyI,WAAW,EAAEhJ,UAAU,CAAC,MAAM6B,iBAAiB,CAAC;IAChDoH,KAAK,EAAE;EACX,CAAC,CACJ,GAR+FnJ,EAAE,CAAAoJ,oBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,2BAAA3I,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFb,EAAE,CAAAyJ,UAAA,IAAA7I,wCAAA,qBAQvB,CAAC;IAAA;EAAA;EAAA8I,aAAA;AAAA,EAAoB;AACpG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAToG3J,EAAE,CAAA4J,iBAAA,CASX7H,iBAAiB,EAAc,CAAC;IAC/GoF,IAAI,EAAEhH,SAAS;IACf0J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBP,QAAQ,EAAE,6BAA6B;MACvC;MACAQ,SAAS,EAAE,CACP;QACId,OAAO,EAAExI,iBAAiB;QAC1B;QACAyI,WAAW,EAAEhJ,UAAU,CAAC,MAAM6B,iBAAiB,CAAC;QAChDoH,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhC,IAAI,EAAEnH,EAAE,CAACwI;IAAW,CAAC,EAAE;MAAErB,IAAI,EAAEnH,EAAE,CAACyI;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE1E,MAAM,EAAE,CAAC;MAC/GoD,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAE+B,MAAM,EAAE,CAAC;MACTgF,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEgC,IAAI,EAAE,CAAC;MACP+E,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEiC,OAAO,EAAE,CAAC;MACV8E,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEwD,QAAQ,EAAE,CAAC;MACXuD,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEoH,oBAAoB,EAAE,CAAC;MACvBL,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEkC,wBAAwB,EAAE,CAAC;MAC3B6E,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEkD,QAAQ,EAAE,CAAC;MACX6D,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEmC,KAAK,EAAE,CAAC;MACR4E,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEmC,MAAM,EAAE,CAAC;MACT2E,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEoC,IAAI,EAAE,CAAC;MACP0E,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEqC,KAAK,EAAE,CAAC;MACRyE,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEsC,KAAK,EAAE,CAAC;MACRwE,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM2J,cAAc,CAAC;AAErBA,cAAc,CAAC5B,IAAI,YAAA6B,uBAAA3B,CAAA;EAAA,YAAAA,CAAA,IAAwF0B,cAAc;AAAA,CAAkD;AAC3KA,cAAc,CAACE,IAAI,kBA3DiFlK,EAAE,CAAAmK,gBAAA;EAAAhD,IAAA,EA2DM6C;AAAc,EAA0G;AACpOA,cAAc,CAACI,IAAI,kBA5DiFpK,EAAE,CAAAqK,gBAAA;EAAAC,OAAA,GA4DgC,CAAC5J,WAAW,EAAEC,YAAY,CAAC;AAAA,EAAI;AACrK;EAAA,QAAAgJ,SAAA,oBAAAA,SAAA,KA7DoG3J,EAAE,CAAA4J,iBAAA,CA6DXI,cAAc,EAAc,CAAC;IAC5G7C,IAAI,EAAE7G,QAAQ;IACduJ,IAAI,EAAE,CAAC;MACCS,OAAO,EAAE,CAAC5J,WAAW,EAAEC,YAAY,CAAC;MACpC4J,YAAY,EAAE,CAACxI,iBAAiB,CAAC;MACjCyI,OAAO,EAAE,CAACzI,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,iBAAiB,EAAEiI,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
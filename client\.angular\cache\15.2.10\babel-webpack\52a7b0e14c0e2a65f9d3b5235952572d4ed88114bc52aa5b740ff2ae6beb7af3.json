{"ast": null, "code": "import baseIsDate from './_baseIsDate.js';\nimport baseUnary from './_baseUnary.js';\nimport nodeUtil from './_nodeUtil.js';\n\n/* Node.js helper references. */\nvar nodeIsDate = nodeUtil && nodeUtil.isDate;\n\n/**\n * Checks if `value` is classified as a `Date` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n * @example\n *\n * _.isDate(new Date);\n * // => true\n *\n * _.isDate('Mon April 23 2012');\n * // => false\n */\nvar isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;\nexport default isDate;", "map": {"version": 3, "names": ["baseIsDate", "baseUnary", "nodeUtil", "nodeIsDate", "isDate"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/isDate.js"], "sourcesContent": ["import baseIsDate from './_baseIsDate.js';\nimport baseUnary from './_baseUnary.js';\nimport nodeUtil from './_nodeUtil.js';\n\n/* Node.js helper references. */\nvar nodeIsDate = nodeUtil && nodeUtil.isDate;\n\n/**\n * Checks if `value` is classified as a `Date` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n * @example\n *\n * _.isDate(new Date);\n * // => true\n *\n * _.isDate('Mon April 23 2012');\n * // => false\n */\nvar isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;\n\nexport default isDate;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,UAAU,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,MAAM;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,MAAM,GAAGD,UAAU,GAAGF,SAAS,CAACE,UAAU,CAAC,GAAGH,UAAU;AAE5D,eAAeI,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FieldWrapper } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ngx-formly/core\";\nfunction FormlyHorizontalWrapper_label_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FormlyHorizontalWrapper_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, FormlyHorizontalWrapper_label_1_ng_container_2_Template, 2, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"for\", ctx_r0.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.to.label, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.to.required && ctx_r0.to.hideRequiredMarker !== true);\n  }\n}\nfunction FormlyHorizontalWrapper_ng_template_3_Template(rf, ctx) {}\nfunction FormlyHorizontalWrapper_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"formly-validation-message\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"field\", ctx_r3.field);\n  }\n}\nexport class FormlyHorizontalWrapper extends FieldWrapper {\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵFormlyHorizontalWrapper_BaseFactory;\n    return function FormlyHorizontalWrapper_Factory(t) {\n      return (ɵFormlyHorizontalWrapper_BaseFactory || (ɵFormlyHorizontalWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(FormlyHorizontalWrapper)))(t || FormlyHorizontalWrapper);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormlyHorizontalWrapper,\n    selectors: [[\"formly-horizontal-wrapper\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 2,\n    consts: [[1, \"row\", \"mb-3\"], [\"class\", \"col-auto form-label\", 4, \"ngIf\"], [1, \"col\"], [\"fieldComponent\", \"\"], [\"class\", \"col-sm-3 invalid-feedback d-block\", 4, \"ngIf\"], [1, \"col-auto\", \"form-label\"], [4, \"ngIf\"], [1, \"col-sm-3\", \"invalid-feedback\", \"d-block\"], [3, \"field\"]],\n    template: function FormlyHorizontalWrapper_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, FormlyHorizontalWrapper_label_1_Template, 3, 3, \"label\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtemplate(3, FormlyHorizontalWrapper_ng_template_3_Template, 0, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, FormlyHorizontalWrapper_div_5_Template, 2, 1, \"div\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.to.label);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.showError);\n      }\n    },\n    dependencies: [i1.NgIf, i2.ɵFormlyValidationMessage],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,kBAAkB;;;;;;IAQvCC,EAAA,CAAAC,uBAAA,GACG;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,qBAAA,EACH;;;;;IAJHH,EAAA,CAAAI,cAAA,eAAoE;IAClEJ,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAK,UAAA,IAAAC,uDAAA,0BAEC;IACHN,EAAA,CAAAO,YAAA,EAAQ;;;;IALDP,EAAA,CAAAQ,WAAA,QAAAC,MAAA,CAAAC,EAAA,CAAe;IACpBV,EAAA,CAAAW,SAAA,GACA;IADAX,EAAA,CAAAY,kBAAA,MAAAH,MAAA,CAAAI,EAAA,CAAAC,KAAA,MACA;IAAed,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAe,UAAA,SAAAN,MAAA,CAAAI,EAAA,CAAAG,QAAA,IAAAP,MAAA,CAAAI,EAAA,CAAAI,kBAAA,UAAmD;;;;;;IAQpEjB,EAAA,CAAAI,cAAA,aAAiE;IAC/DJ,EAAA,CAAAkB,SAAA,mCAAuE;IACzElB,EAAA,CAAAO,YAAA,EAAM;;;;IADuBP,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAe,UAAA,UAAAI,MAAA,CAAAC,KAAA,CAAe;;;AAKlD,OAAM,MAAOC,uBAAwB,SAAQtB,YAAY;EAAA,QAAAuB,CAAA;;;uHAA5CD,uBAAuB,IAAAE,CAAA,IAAvBF,uBAAuB;IAAA;EAAA;EAAA,QAAAG,EAAA;UAAvBH,uBAAuB;IAAAI,SAAA;IAAAC,QAAA,GAAA1B,EAAA,CAAA2B,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAjBhCjC,EAAA,CAAAI,cAAA,aAAsB;QACpBJ,EAAA,CAAAK,UAAA,IAAA8B,wCAAA,mBAKQ;QACRnC,EAAA,CAAAI,cAAA,aAAiB;QACfJ,EAAA,CAAAK,UAAA,IAAA+B,8CAAA,gCAAApC,EAAA,CAAAqC,sBAAA,CAA2C;QAC7CrC,EAAA,CAAAO,YAAA,EAAM;QAENP,EAAA,CAAAK,UAAA,IAAAiC,sCAAA,iBAEM;QACRtC,EAAA,CAAAO,YAAA,EAAM;;;QAbgDP,EAAA,CAAAW,SAAA,GAAc;QAAdX,EAAA,CAAAe,UAAA,SAAAmB,GAAA,CAAArB,EAAA,CAAAC,KAAA,CAAc;QAU5Dd,EAAA,CAAAW,SAAA,GAAe;QAAfX,EAAA,CAAAe,UAAA,SAAAmB,GAAA,CAAAK,SAAA,CAAe", "names": ["FieldWrapper", "i0", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵelementStart", "ɵɵtemplate", "FormlyHorizontalWrapper_label_1_ng_container_2_Template", "ɵɵelementEnd", "ɵɵattribute", "ctx_r0", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "to", "label", "ɵɵproperty", "required", "hideRequiredMarker", "ɵɵelement", "ctx_r3", "field", "FormlyHorizontalWrapper", "_", "t", "_2", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "FormlyHorizontalWrapper_Template", "rf", "ctx", "FormlyHorizontalWrapper_label_1_Template", "FormlyHorizontalWrapper_ng_template_3_Template", "ɵɵtemplateRefExtractor", "FormlyHorizontalWrapper_div_5_Template", "showError"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\editor-sidebar\\horizontal-wrapper.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FieldWrapper } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'formly-horizontal-wrapper',\r\n  template: `\r\n    <div class=\"row mb-3\">\r\n      <label [attr.for]=\"id\" class=\"col-auto form-label\" *ngIf=\"to.label\">\r\n        {{ to.label }}\r\n        <ng-container *ngIf=\"to.required && to.hideRequiredMarker !== true\"\r\n          >*</ng-container\r\n        >\r\n      </label>\r\n      <div class=\"col\">\r\n        <ng-template #fieldComponent></ng-template>\r\n      </div>\r\n\r\n      <div *ngIf=\"showError\" class=\"col-sm-3 invalid-feedback d-block\">\r\n        <formly-validation-message [field]=\"field\"></formly-validation-message>\r\n      </div>\r\n    </div>\r\n  `,\r\n})\r\nexport class FormlyHorizontalWrapper extends FieldWrapper {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
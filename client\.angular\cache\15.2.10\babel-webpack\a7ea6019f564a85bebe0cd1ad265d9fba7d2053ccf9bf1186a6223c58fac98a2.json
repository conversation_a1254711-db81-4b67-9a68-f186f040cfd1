{"ast": null, "code": "import constant from './constant.js';\nimport createInverter from './_createInverter.js';\nimport identity from './identity.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Creates an object composed of the inverted keys and values of `object`.\n * If `object` contains duplicate values, subsequent values overwrite\n * property assignments of previous values.\n *\n * @static\n * @memberOf _\n * @since 0.7.0\n * @category Object\n * @param {Object} object The object to invert.\n * @returns {Object} Returns the new inverted object.\n * @example\n *\n * var object = { 'a': 1, 'b': 2, 'c': 1 };\n *\n * _.invert(object);\n * // => { '1': 'c', '2': 'b' }\n */\nvar invert = createInverter(function (result, value, key) {\n  if (value != null && typeof value.toString != 'function') {\n    value = nativeObjectToString.call(value);\n  }\n  result[value] = key;\n}, constant(identity));\nexport default invert;", "map": {"version": 3, "names": ["constant", "createInverter", "identity", "objectProto", "Object", "prototype", "nativeObjectToString", "toString", "invert", "result", "value", "key", "call"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/invert.js"], "sourcesContent": ["import constant from './constant.js';\nimport createInverter from './_createInverter.js';\nimport identity from './identity.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Creates an object composed of the inverted keys and values of `object`.\n * If `object` contains duplicate values, subsequent values overwrite\n * property assignments of previous values.\n *\n * @static\n * @memberOf _\n * @since 0.7.0\n * @category Object\n * @param {Object} object The object to invert.\n * @returns {Object} Returns the new inverted object.\n * @example\n *\n * var object = { 'a': 1, 'b': 2, 'c': 1 };\n *\n * _.invert(object);\n * // => { '1': 'c', '2': 'b' }\n */\nvar invert = createInverter(function(result, value, key) {\n  if (value != null &&\n      typeof value.toString != 'function') {\n    value = nativeObjectToString.call(value);\n  }\n\n  result[value] = key;\n}, constant(identity));\n\nexport default invert;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAGH,WAAW,CAACI,QAAQ;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAGP,cAAc,CAAC,UAASQ,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACvD,IAAID,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACH,QAAQ,IAAI,UAAU,EAAE;IACvCG,KAAK,GAAGJ,oBAAoB,CAACM,IAAI,CAACF,KAAK,CAAC;EAC1C;EAEAD,MAAM,CAACC,KAAK,CAAC,GAAGC,GAAG;AACrB,CAAC,EAAEX,QAAQ,CAACE,QAAQ,CAAC,CAAC;AAEtB,eAAeM,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
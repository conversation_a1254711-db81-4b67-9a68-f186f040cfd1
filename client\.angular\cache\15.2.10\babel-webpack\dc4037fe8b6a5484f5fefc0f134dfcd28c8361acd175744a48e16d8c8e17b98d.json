{"ast": null, "code": "const UnsubscriptionErrorImpl = (() => {\n  function UnsubscriptionErrorImpl(errors) {\n    Error.call(this);\n    this.message = errors ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n    return this;\n  }\n  UnsubscriptionErrorImpl.prototype = Object.create(Error.prototype);\n  return UnsubscriptionErrorImpl;\n})();\nexport const UnsubscriptionError = UnsubscriptionErrorImpl;", "map": {"version": 3, "names": ["UnsubscriptionErrorImpl", "errors", "Error", "call", "message", "length", "map", "err", "i", "toString", "join", "name", "prototype", "Object", "create", "UnsubscriptionError"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/UnsubscriptionError.js"], "sourcesContent": ["const UnsubscriptionErrorImpl = (() => {\n    function UnsubscriptionErrorImpl(errors) {\n        Error.call(this);\n        this.message = errors ?\n            `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n        this.name = 'UnsubscriptionError';\n        this.errors = errors;\n        return this;\n    }\n    UnsubscriptionErrorImpl.prototype = Object.create(Error.prototype);\n    return UnsubscriptionErrorImpl;\n})();\nexport const UnsubscriptionError = UnsubscriptionErrorImpl;\n"], "mappings": "AAAA,MAAMA,uBAAuB,GAAG,CAAC,MAAM;EACnC,SAASA,uBAAuBA,CAACC,MAAM,EAAE;IACrCC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;IAChB,IAAI,CAACC,OAAO,GAAGH,MAAM,GAChB,GAAEA,MAAM,CAACI,MAAO;AAC7B,EAAEJ,MAAM,CAACK,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAM,GAAEA,CAAC,GAAG,CAAE,KAAID,GAAG,CAACE,QAAQ,CAAC,CAAE,EAAC,CAAC,CAACC,IAAI,CAAC,MAAM,CAAE,EAAC,GAAG,EAAE;IACnE,IAAI,CAACC,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACV,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACf;EACAD,uBAAuB,CAACY,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACZ,KAAK,CAACU,SAAS,CAAC;EAClE,OAAOZ,uBAAuB;AAClC,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMe,mBAAmB,GAAGf,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
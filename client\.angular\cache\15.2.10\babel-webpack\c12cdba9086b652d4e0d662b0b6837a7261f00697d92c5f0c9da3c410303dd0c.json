{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\r\n * Container class for a captured webcam image\r\n * <AUTHOR> davidshen84\r\n */\nconst _c0 = [\"video\"];\nconst _c1 = [\"canvas\"];\nfunction WebcamComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function WebcamComponent_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.rotateVideoInput(true));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nclass WebcamImage {\n  constructor(imageAsDataUrl, mimeType, imageData) {\n    this._mimeType = null;\n    this._imageAsBase64 = null;\n    this._imageAsDataUrl = null;\n    this._imageData = null;\n    this._mimeType = mimeType;\n    this._imageAsDataUrl = imageAsDataUrl;\n    this._imageData = imageData;\n  }\n  /**\r\n   * Extracts the Base64 data out of the given dataUrl.\r\n   * @param dataUrl the given dataUrl\r\n   * @param mimeType the mimeType of the data\r\n   */\n  static getDataFromDataUrl(dataUrl, mimeType) {\n    return dataUrl.replace(`data:${mimeType};base64,`, '');\n  }\n  /**\r\n   * Get the base64 encoded image data\r\n   * @returns base64 data of the image\r\n   */\n  get imageAsBase64() {\n    return this._imageAsBase64 ? this._imageAsBase64 : this._imageAsBase64 = WebcamImage.getDataFromDataUrl(this._imageAsDataUrl, this._mimeType);\n  }\n  /**\r\n   * Get the encoded image as dataUrl\r\n   * @returns the dataUrl of the image\r\n   */\n  get imageAsDataUrl() {\n    return this._imageAsDataUrl;\n  }\n  /**\r\n   * Get the ImageData object associated with the canvas' 2d context.\r\n   * @returns the ImageData of the canvas's 2d context.\r\n   */\n  get imageData() {\n    return this._imageData;\n  }\n}\nclass WebcamUtil {\n  /**\r\n   * Lists available videoInput devices\r\n   * @returns a list of media device info.\r\n   */\n  static getAvailableVideoInputs() {\n    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {\n      return Promise.reject('enumerateDevices() not supported.');\n    }\n    return new Promise((resolve, reject) => {\n      navigator.mediaDevices.enumerateDevices().then(devices => {\n        resolve(devices.filter(device => device.kind === 'videoinput'));\n      }).catch(err => {\n        reject(err.message || err);\n      });\n    });\n  }\n}\nclass WebcamComponent {\n  constructor() {\n    /** Defines the max width of the webcam area in px */\n    this.width = 640;\n    /** Defines the max height of the webcam area in px */\n    this.height = 480;\n    /** Defines base constraints to apply when requesting video track from UserMedia */\n    this.videoOptions = WebcamComponent.DEFAULT_VIDEO_OPTIONS;\n    /** Flag to enable/disable camera switch. If enabled, a switch icon will be displayed if multiple cameras were found */\n    this.allowCameraSwitch = true;\n    /** Flag to control whether an ImageData object is stored into the WebcamImage object. */\n    this.captureImageData = false;\n    /** The image type to use when capturing snapshots */\n    this.imageType = WebcamComponent.DEFAULT_IMAGE_TYPE;\n    /** The image quality to use when capturing snapshots (number between 0 and 1) */\n    this.imageQuality = WebcamComponent.DEFAULT_IMAGE_QUALITY;\n    /** EventEmitter which fires when an image has been captured */\n    this.imageCapture = new EventEmitter();\n    /** Emits a mediaError if webcam cannot be initialized (e.g. missing user permissions) */\n    this.initError = new EventEmitter();\n    /** Emits when the webcam video was clicked */\n    this.imageClick = new EventEmitter();\n    /** Emits the active deviceId after the active video device was switched */\n    this.cameraSwitched = new EventEmitter();\n    /** available video devices */\n    this.availableVideoInputs = [];\n    /** Indicates whether the video device is ready to be switched */\n    this.videoInitialized = false;\n    /** Index of active video in availableVideoInputs */\n    this.activeVideoInputIndex = -1;\n    /** MediaStream object in use for streaming UserMedia data */\n    this.mediaStream = null;\n    /** width and height of the active video stream */\n    this.activeVideoSettings = null;\n  }\n  /**\r\n   * If the given Observable emits, an image will be captured and emitted through 'imageCapture' EventEmitter\r\n   */\n  set trigger(trigger) {\n    if (this.triggerSubscription) {\n      this.triggerSubscription.unsubscribe();\n    }\n    // Subscribe to events from this Observable to take snapshots\n    this.triggerSubscription = trigger.subscribe(() => {\n      this.takeSnapshot();\n    });\n  }\n  /**\r\n   * If the given Observable emits, the active webcam will be switched to the one indicated by the emitted value.\r\n   * @param switchCamera Indicates which webcam to switch to\r\n   *   true: cycle forwards through available webcams\r\n   *   false: cycle backwards through available webcams\r\n   *   string: activate the webcam with the given id\r\n   */\n  set switchCamera(switchCamera) {\n    if (this.switchCameraSubscription) {\n      this.switchCameraSubscription.unsubscribe();\n    }\n    // Subscribe to events from this Observable to switch video device\n    this.switchCameraSubscription = switchCamera.subscribe(value => {\n      if (typeof value === 'string') {\n        // deviceId was specified\n        this.switchToVideoInput(value);\n      } else {\n        // direction was specified\n        this.rotateVideoInput(value !== false);\n      }\n    });\n  }\n  /**\r\n   * Get MediaTrackConstraints to request streaming the given device\r\n   * @param deviceId\r\n   * @param baseMediaTrackConstraints base constraints to merge deviceId-constraint into\r\n   * @returns\r\n   */\n  static getMediaConstraintsForDevice(deviceId, baseMediaTrackConstraints) {\n    const result = baseMediaTrackConstraints ? baseMediaTrackConstraints : this.DEFAULT_VIDEO_OPTIONS;\n    if (deviceId) {\n      result.deviceId = {\n        exact: deviceId\n      };\n    }\n    return result;\n  }\n  /**\r\n   * Tries to harvest the deviceId from the given mediaStreamTrack object.\r\n   * Browsers populate this object differently; this method tries some different approaches\r\n   * to read the id.\r\n   * @param mediaStreamTrack\r\n   * @returns deviceId if found in the mediaStreamTrack\r\n   */\n  static getDeviceIdFromMediaStreamTrack(mediaStreamTrack) {\n    if (mediaStreamTrack.getSettings && mediaStreamTrack.getSettings() && mediaStreamTrack.getSettings().deviceId) {\n      return mediaStreamTrack.getSettings().deviceId;\n    } else if (mediaStreamTrack.getConstraints && mediaStreamTrack.getConstraints() && mediaStreamTrack.getConstraints().deviceId) {\n      const deviceIdObj = mediaStreamTrack.getConstraints().deviceId;\n      return WebcamComponent.getValueFromConstrainDOMString(deviceIdObj);\n    }\n  }\n  /**\r\n   * Tries to harvest the facingMode from the given mediaStreamTrack object.\r\n   * Browsers populate this object differently; this method tries some different approaches\r\n   * to read the value.\r\n   * @param mediaStreamTrack\r\n   * @returns facingMode if found in the mediaStreamTrack\r\n   */\n  static getFacingModeFromMediaStreamTrack(mediaStreamTrack) {\n    if (mediaStreamTrack) {\n      if (mediaStreamTrack.getSettings && mediaStreamTrack.getSettings() && mediaStreamTrack.getSettings().facingMode) {\n        return mediaStreamTrack.getSettings().facingMode;\n      } else if (mediaStreamTrack.getConstraints && mediaStreamTrack.getConstraints() && mediaStreamTrack.getConstraints().facingMode) {\n        const facingModeConstraint = mediaStreamTrack.getConstraints().facingMode;\n        return WebcamComponent.getValueFromConstrainDOMString(facingModeConstraint);\n      }\n    }\n  }\n  /**\r\n   * Determines whether the given mediaStreamTrack claims itself as user facing\r\n   * @param mediaStreamTrack\r\n   */\n  static isUserFacing(mediaStreamTrack) {\n    const facingMode = WebcamComponent.getFacingModeFromMediaStreamTrack(mediaStreamTrack);\n    return facingMode ? 'user' === facingMode.toLowerCase() : false;\n  }\n  /**\r\n   * Extracts the value from the given ConstrainDOMString\r\n   * @param constrainDOMString\r\n   */\n  static getValueFromConstrainDOMString(constrainDOMString) {\n    if (constrainDOMString) {\n      if (constrainDOMString instanceof String) {\n        return String(constrainDOMString);\n      } else if (Array.isArray(constrainDOMString) && Array(constrainDOMString).length > 0) {\n        return String(constrainDOMString[0]);\n      } else if (typeof constrainDOMString === 'object') {\n        if (constrainDOMString['exact']) {\n          return String(constrainDOMString['exact']);\n        } else if (constrainDOMString['ideal']) {\n          return String(constrainDOMString['ideal']);\n        }\n      }\n    }\n    return null;\n  }\n  ngAfterViewInit() {\n    this.detectAvailableDevices().then(() => {\n      // start video\n      this.switchToVideoInput(null);\n    }).catch(err => {\n      this.initError.next({\n        message: err\n      });\n      // fallback: still try to load webcam, even if device enumeration failed\n      this.switchToVideoInput(null);\n    });\n  }\n  ngOnDestroy() {\n    this.stopMediaTracks();\n    this.unsubscribeFromSubscriptions();\n  }\n  /**\r\n   * Takes a snapshot of the current webcam's view and emits the image as an event\r\n   */\n  takeSnapshot() {\n    // set canvas size to actual video size\n    const _video = this.nativeVideoElement;\n    const dimensions = {\n      width: this.width,\n      height: this.height\n    };\n    if (_video.videoWidth) {\n      dimensions.width = _video.videoWidth;\n      dimensions.height = _video.videoHeight;\n    }\n    const _canvas = this.canvas.nativeElement;\n    _canvas.width = dimensions.width;\n    _canvas.height = dimensions.height;\n    // paint snapshot image to canvas\n    const context2d = _canvas.getContext('2d');\n    context2d.drawImage(_video, 0, 0);\n    // read canvas content as image\n    const mimeType = this.imageType ? this.imageType : WebcamComponent.DEFAULT_IMAGE_TYPE;\n    const quality = this.imageQuality ? this.imageQuality : WebcamComponent.DEFAULT_IMAGE_QUALITY;\n    const dataUrl = _canvas.toDataURL(mimeType, quality);\n    // get the ImageData object from the canvas' context.\n    let imageData = null;\n    if (this.captureImageData) {\n      imageData = context2d.getImageData(0, 0, _canvas.width, _canvas.height);\n    }\n    this.imageCapture.next(new WebcamImage(dataUrl, mimeType, imageData));\n  }\n  /**\r\n   * Switches to the next/previous video device\r\n   * @param forward\r\n   */\n  rotateVideoInput(forward) {\n    if (this.availableVideoInputs && this.availableVideoInputs.length > 1) {\n      const increment = forward ? 1 : this.availableVideoInputs.length - 1;\n      const nextInputIndex = (this.activeVideoInputIndex + increment) % this.availableVideoInputs.length;\n      this.switchToVideoInput(this.availableVideoInputs[nextInputIndex].deviceId);\n    }\n  }\n  /**\r\n   * Switches the camera-view to the specified video device\r\n   */\n  switchToVideoInput(deviceId) {\n    this.videoInitialized = false;\n    this.stopMediaTracks();\n    this.initWebcam(deviceId, this.videoOptions);\n  }\n  /**\r\n   * Event-handler for video resize event.\r\n   * Triggers Angular change detection so that new video dimensions get applied\r\n   */\n  videoResize() {\n    // here to trigger Angular change detection\n  }\n  get videoWidth() {\n    const videoRatio = this.getVideoAspectRatio();\n    return Math.min(this.width, this.height * videoRatio);\n  }\n  get videoHeight() {\n    const videoRatio = this.getVideoAspectRatio();\n    return Math.min(this.height, this.width / videoRatio);\n  }\n  get videoStyleClasses() {\n    let classes = '';\n    if (this.isMirrorImage()) {\n      classes += 'mirrored ';\n    }\n    return classes.trim();\n  }\n  get nativeVideoElement() {\n    return this.video.nativeElement;\n  }\n  /**\r\n   * Returns the video aspect ratio of the active video stream\r\n   */\n  getVideoAspectRatio() {\n    // calculate ratio from video element dimensions if present\n    const videoElement = this.nativeVideoElement;\n    if (videoElement.videoWidth && videoElement.videoWidth > 0 && videoElement.videoHeight && videoElement.videoHeight > 0) {\n      return videoElement.videoWidth / videoElement.videoHeight;\n    }\n    // nothing present - calculate ratio based on width/height params\n    return this.width / this.height;\n  }\n  /**\r\n   * Init webcam live view\r\n   */\n  initWebcam(deviceId, userVideoTrackConstraints) {\n    const _video = this.nativeVideoElement;\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n      // merge deviceId -> userVideoTrackConstraints\n      const videoTrackConstraints = WebcamComponent.getMediaConstraintsForDevice(deviceId, userVideoTrackConstraints);\n      navigator.mediaDevices.getUserMedia({\n        video: videoTrackConstraints\n      }).then(stream => {\n        this.mediaStream = stream;\n        _video.srcObject = stream;\n        _video.play();\n        this.activeVideoSettings = stream.getVideoTracks()[0].getSettings();\n        const activeDeviceId = WebcamComponent.getDeviceIdFromMediaStreamTrack(stream.getVideoTracks()[0]);\n        this.cameraSwitched.next(activeDeviceId);\n        // Initial detect may run before user gave permissions, returning no deviceIds. This prevents later camera switches. (#47)\n        // Run detect once again within getUserMedia callback, to make sure this time we have permissions and get deviceIds.\n        this.detectAvailableDevices().then(() => {\n          this.activeVideoInputIndex = activeDeviceId ? this.availableVideoInputs.findIndex(mediaDeviceInfo => mediaDeviceInfo.deviceId === activeDeviceId) : -1;\n          this.videoInitialized = true;\n        }).catch(() => {\n          this.activeVideoInputIndex = -1;\n          this.videoInitialized = true;\n        });\n      }).catch(err => {\n        this.initError.next({\n          message: err.message,\n          mediaStreamError: err\n        });\n      });\n    } else {\n      this.initError.next({\n        message: 'Cannot read UserMedia from MediaDevices.'\n      });\n    }\n  }\n  getActiveVideoTrack() {\n    return this.mediaStream ? this.mediaStream.getVideoTracks()[0] : null;\n  }\n  isMirrorImage() {\n    if (!this.getActiveVideoTrack()) {\n      return false;\n    }\n    // check for explicit mirror override parameter\n    {\n      let mirror = 'auto';\n      if (this.mirrorImage) {\n        if (typeof this.mirrorImage === 'string') {\n          mirror = String(this.mirrorImage).toLowerCase();\n        } else {\n          // WebcamMirrorProperties\n          if (this.mirrorImage.x) {\n            mirror = this.mirrorImage.x.toLowerCase();\n          }\n        }\n      }\n      switch (mirror) {\n        case 'always':\n          return true;\n        case 'never':\n          return false;\n      }\n    }\n    // default: enable mirroring if webcam is user facing\n    return WebcamComponent.isUserFacing(this.getActiveVideoTrack());\n  }\n  /**\r\n   * Stops all active media tracks.\r\n   * This prevents the webcam from being indicated as active,\r\n   * even if it is no longer used by this component.\r\n   */\n  stopMediaTracks() {\n    if (this.mediaStream && this.mediaStream.getTracks) {\n      // pause video to prevent mobile browser freezes\n      this.nativeVideoElement.pause();\n      // getTracks() returns all media tracks (video+audio)\n      this.mediaStream.getTracks().forEach(track => track.stop());\n    }\n  }\n  /**\r\n   * Unsubscribe from all open subscriptions\r\n   */\n  unsubscribeFromSubscriptions() {\n    if (this.triggerSubscription) {\n      this.triggerSubscription.unsubscribe();\n    }\n    if (this.switchCameraSubscription) {\n      this.switchCameraSubscription.unsubscribe();\n    }\n  }\n  /**\r\n   * Reads available input devices\r\n   */\n  detectAvailableDevices() {\n    return new Promise((resolve, reject) => {\n      WebcamUtil.getAvailableVideoInputs().then(devices => {\n        this.availableVideoInputs = devices;\n        resolve(devices);\n      }).catch(err => {\n        this.availableVideoInputs = [];\n        reject(err);\n      });\n    });\n  }\n}\nWebcamComponent.DEFAULT_VIDEO_OPTIONS = {\n  facingMode: 'environment'\n};\nWebcamComponent.DEFAULT_IMAGE_TYPE = 'image/jpeg';\nWebcamComponent.DEFAULT_IMAGE_QUALITY = 0.92;\nWebcamComponent.ɵfac = function WebcamComponent_Factory(t) {\n  return new (t || WebcamComponent)();\n};\nWebcamComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: WebcamComponent,\n  selectors: [[\"webcam\"]],\n  viewQuery: function WebcamComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.video = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvas = _t.first);\n    }\n  },\n  inputs: {\n    width: \"width\",\n    height: \"height\",\n    videoOptions: \"videoOptions\",\n    allowCameraSwitch: \"allowCameraSwitch\",\n    mirrorImage: \"mirrorImage\",\n    captureImageData: \"captureImageData\",\n    imageType: \"imageType\",\n    imageQuality: \"imageQuality\",\n    trigger: \"trigger\",\n    switchCamera: \"switchCamera\"\n  },\n  outputs: {\n    imageCapture: \"imageCapture\",\n    initError: \"initError\",\n    imageClick: \"imageClick\",\n    cameraSwitched: \"cameraSwitched\"\n  },\n  decls: 6,\n  vars: 7,\n  consts: [[1, \"webcam-wrapper\", 3, \"click\"], [\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 3, \"width\", \"height\", \"resize\"], [\"video\", \"\"], [\"class\", \"camera-switch\", 3, \"click\", 4, \"ngIf\"], [3, \"width\", \"height\"], [\"canvas\", \"\"], [1, \"camera-switch\", 3, \"click\"]],\n  template: function WebcamComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function WebcamComponent_Template_div_click_0_listener() {\n        return ctx.imageClick.next();\n      });\n      i0.ɵɵelementStart(1, \"video\", 1, 2);\n      i0.ɵɵlistener(\"resize\", function WebcamComponent_Template_video_resize_1_listener() {\n        return ctx.videoResize();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, WebcamComponent_div_3_Template, 1, 0, \"div\", 3);\n      i0.ɵɵelement(4, \"canvas\", 4, 5);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMap(ctx.videoStyleClasses);\n      i0.ɵɵproperty(\"width\", ctx.videoWidth)(\"height\", ctx.videoHeight);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.allowCameraSwitch && ctx.availableVideoInputs.length > 1 && ctx.videoInitialized);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", ctx.width)(\"height\", ctx.height);\n    }\n  },\n  dependencies: [i1.NgIf],\n  styles: [\".webcam-wrapper[_ngcontent-%COMP%]{display:inline-block;position:relative;line-height:0}.webcam-wrapper[_ngcontent-%COMP%]   video.mirrored[_ngcontent-%COMP%]{transform:scaleX(-1)}.webcam-wrapper[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%]{display:none}.webcam-wrapper[_ngcontent-%COMP%]   .camera-switch[_ngcontent-%COMP%]{background-color:#0000001a;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAE9UlEQVR42u2aT2hdRRTGf+cRQqghSqihdBFDkRISK2KDfzDWxHaRQHEhaINKqa1gKQhd6EZLN+IidCH+Q0oWIkVRC21BQxXRitVaSbKoJSGtYGoK2tQ/tU1jY5v0c5F54Xl7b/KSO/PyEt+3e5f75p7zzZwzZ74zUEIJJfyfYaEGllQGVAGZlENdBy6Z2cSiYFTSKkkfS/pH/nBF0kFJdUW9AiRVASeAukD8DgNrzOySrwEzng18KaDzALXuG8W3AiStAvqBisBRNg40mtlPxbYCOgvgPO4bncWW+JpVeDQXRQhIygDfA00F5r0XuNfMrgclQFI98DDQCNQA5ZFXqoCWBVp8XwHRHeEqcN7loy/NbHBesyqpQ1KfFj/6nC+ZvFaApFrgPaCZpYVvgCfNbDiRAElNwGFg+RIt/X8H2s2s9wYCJDUAR4HqJX7++RN40MwGpgmQVAH0AQ2BPz4AHHPl8nBOAqtyFWQjsA6oL4Ada81sPDv7uwImod8kvSJp9RyS8O2SXnb/DYVd2Y9VSroQ4ANXJO2WVJmixqh0kzMWwL4LkiqRtDnA4D1zmfE8j9g9AezcnAHaPcfXdbfdnPZ2Yps6+DwAvO/Z1naTdApY7Xng48BDZnY1MpMVQBuw3iXc5Tnb0wBwBPjUzP6eoezuArZ6svM0geJLkvZEYnl3nkntoqROSbckSW2Suj3ZOIangc7GPJuUtNGdFIfmMeavktoSSKiW9LMPw30Q8JqkekmjCbOZRhuclLQjgYSNxUBAj6RyZ9ATgUJpUtJTCSR8vpAEXHAyWK5BXYFIGHOlepSAloUk4NEYgyoknQhEwhFJ0e8h6VSaQeerCb5uZgdi9utxYBNwOUD93hIVXswM4INCi6K9wAszFC2DwLOBDjHbYp59karIUnRdzYy/3ClqVklaUhfwTICj7K25OqA7a4wWagVsm4Me/xzwg2cCqqONFzO7DPxSCAJi436GUBgHHguQD2oTlJ55oSzP9ybccsttSJw1szdjFOSnI/8dTCGZHwcORp4Nx7y3B1iZ8/sm4MW8/Euxg5wIsS/HaAp3zeP4/G7obRDXI4jiTIA22H7Xdc7X+S3A5lC7QBQ357aq3VAjCeSkwUfAJrfvz+R8A9ADLAtZB+TinpjC5JMA+//jwPZZnF8G7J+L8z4IWB/zbG+gIujVWfLBW/NStVMmqaG4POJRsIjix7h8IGnLQuoBbQki5sVAJHyYm7YkNaRRtXwQ8G1cHpX0iKRrgUjYno17Sf0LrQhJUkdCeHWkVITGJI0k1QeS3ikGSUzOyJUJJNznYneuOCnpTldcxa2kP3xJYqOeSDjqZG8ShJLnE8TTuMS6Iyu1BW7djZqkfo9N0QOuYJmYQddfB7RG+gLTNzqAY9FrL+5/nwEbvDdJJe3zzOrhNP3AWRqmk55t3ZcBuj3b2gb0Sbrbo/NNzk7fFzu7s/E5EiC+rrmeQU0Kx2skvRFoOx2ZzlmSdgbsw49JetvtBpk8nM64d/cGbNtJ0s7cGyJlwHeEv+t3nqnLSgPAUOSGyG3AHUxdzqoJbEcvcL+ZTeTeEapzJKxgaeOcc/7Mf06D7kFrguS0VDAMtGadv+E47DT9tcChJej8ISfpD+abgTe45uOkFi8mnQ+JBVQ+d4VXuOptjavcyot8pq86mfwk8LWZnaOEEkoooYQSSojDv8AhQNeGfe0jAAAAAElFTkSuQmCC);background-repeat:no-repeat;border-radius:5px;position:absolute;right:13px;top:10px;height:48px;width:48px;background-size:80%;cursor:pointer;background-position:center;transition:background-color .2s ease}.webcam-wrapper[_ngcontent-%COMP%]   .camera-switch[_ngcontent-%COMP%]:hover{background-color:#0000002e}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WebcamComponent, [{\n    type: Component,\n    args: [{\n      selector: 'webcam',\n      template: \"<div class=\\\"webcam-wrapper\\\" (click)=\\\"imageClick.next();\\\">\\r\\n  <video #video [width]=\\\"videoWidth\\\" [height]=\\\"videoHeight\\\" [class]=\\\"videoStyleClasses\\\" autoplay muted playsinline (resize)=\\\"videoResize()\\\"></video>\\r\\n  <div class=\\\"camera-switch\\\" *ngIf=\\\"allowCameraSwitch && availableVideoInputs.length > 1 && videoInitialized\\\" (click)=\\\"rotateVideoInput(true)\\\"></div>\\r\\n  <canvas #canvas [width]=\\\"width\\\" [height]=\\\"height\\\"></canvas>\\r\\n</div>\\r\\n\",\n      styles: [\".webcam-wrapper{display:inline-block;position:relative;line-height:0}.webcam-wrapper video.mirrored{transform:scaleX(-1)}.webcam-wrapper canvas{display:none}.webcam-wrapper .camera-switch{background-color:#0000001a;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAE9UlEQVR42u2aT2hdRRTGf+cRQqghSqihdBFDkRISK2KDfzDWxHaRQHEhaINKqa1gKQhd6EZLN+IidCH+Q0oWIkVRC21BQxXRitVaSbKoJSGtYGoK2tQ/tU1jY5v0c5F54Xl7b/KSO/PyEt+3e5f75p7zzZwzZ74zUEIJJfyfYaEGllQGVAGZlENdBy6Z2cSiYFTSKkkfS/pH/nBF0kFJdUW9AiRVASeAukD8DgNrzOySrwEzng18KaDzALXuG8W3AiStAvqBisBRNg40mtlPxbYCOgvgPO4bncWW+JpVeDQXRQhIygDfA00F5r0XuNfMrgclQFI98DDQCNQA5ZFXqoCWBVp8XwHRHeEqcN7loy/NbHBesyqpQ1KfFj/6nC+ZvFaApFrgPaCZpYVvgCfNbDiRAElNwGFg+RIt/X8H2s2s9wYCJDUAR4HqJX7++RN40MwGpgmQVAH0AQ2BPz4AHHPl8nBOAqtyFWQjsA6oL4Ada81sPDv7uwImod8kvSJp9RyS8O2SXnb/DYVd2Y9VSroQ4ANXJO2WVJmixqh0kzMWwL4LkiqRtDnA4D1zmfE8j9g9AezcnAHaPcfXdbfdnPZ2Yps6+DwAvO/Z1naTdApY7Xng48BDZnY1MpMVQBuw3iXc5Tnb0wBwBPjUzP6eoezuArZ6svM0geJLkvZEYnl3nkntoqROSbckSW2Suj3ZOIangc7GPJuUtNGdFIfmMeavktoSSKiW9LMPw30Q8JqkekmjCbOZRhuclLQjgYSNxUBAj6RyZ9ATgUJpUtJTCSR8vpAEXHAyWK5BXYFIGHOlepSAloUk4NEYgyoknQhEwhFJ0e8h6VSaQeerCb5uZgdi9utxYBNwOUD93hIVXswM4INCi6K9wAszFC2DwLOBDjHbYp59karIUnRdzYy/3ClqVklaUhfwTICj7K25OqA7a4wWagVsm4Me/xzwg2cCqqONFzO7DPxSCAJi436GUBgHHguQD2oTlJ55oSzP9ybccsttSJw1szdjFOSnI/8dTCGZHwcORp4Nx7y3B1iZ8/sm4MW8/Euxg5wIsS/HaAp3zeP4/G7obRDXI4jiTIA22H7Xdc7X+S3A5lC7QBQ357aq3VAjCeSkwUfAJrfvz+R8A9ADLAtZB+TinpjC5JMA+//jwPZZnF8G7J+L8z4IWB/zbG+gIujVWfLBW/NStVMmqaG4POJRsIjix7h8IGnLQuoBbQki5sVAJHyYm7YkNaRRtXwQ8G1cHpX0iKRrgUjYno17Sf0LrQhJUkdCeHWkVITGJI0k1QeS3ikGSUzOyJUJJNznYneuOCnpTldcxa2kP3xJYqOeSDjqZG8ShJLnE8TTuMS6Iyu1BW7djZqkfo9N0QOuYJmYQddfB7RG+gLTNzqAY9FrL+5/nwEbvDdJJe3zzOrhNP3AWRqmk55t3ZcBuj3b2gb0Sbrbo/NNzk7fFzu7s/E5EiC+rrmeQU0Kx2skvRFoOx2ZzlmSdgbsw49JetvtBpk8nM64d/cGbNtJ0s7cGyJlwHeEv+t3nqnLSgPAUOSGyG3AHUxdzqoJbEcvcL+ZTeTeEapzJKxgaeOcc/7Mf06D7kFrguS0VDAMtGadv+E47DT9tcChJej8ISfpD+abgTe45uOkFi8mnQ+JBVQ+d4VXuOptjavcyot8pq86mfwk8LWZnaOEEkoooYQSSojDv8AhQNeGfe0jAAAAAElFTkSuQmCC);background-repeat:no-repeat;border-radius:5px;position:absolute;right:13px;top:10px;height:48px;width:48px;background-size:80%;cursor:pointer;background-position:center;transition:background-color .2s ease}.webcam-wrapper .camera-switch:hover{background-color:#0000002e}\\n\"]\n    }]\n  }], null, {\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }],\n    videoOptions: [{\n      type: Input\n    }],\n    allowCameraSwitch: [{\n      type: Input\n    }],\n    mirrorImage: [{\n      type: Input\n    }],\n    captureImageData: [{\n      type: Input\n    }],\n    imageType: [{\n      type: Input\n    }],\n    imageQuality: [{\n      type: Input\n    }],\n    imageCapture: [{\n      type: Output\n    }],\n    initError: [{\n      type: Output\n    }],\n    imageClick: [{\n      type: Output\n    }],\n    cameraSwitched: [{\n      type: Output\n    }],\n    video: [{\n      type: ViewChild,\n      args: ['video', {\n        static: true\n      }]\n    }],\n    canvas: [{\n      type: ViewChild,\n      args: ['canvas', {\n        static: true\n      }]\n    }],\n    trigger: [{\n      type: Input\n    }],\n    switchCamera: [{\n      type: Input\n    }]\n  });\n})();\nconst COMPONENTS = [WebcamComponent];\nclass WebcamModule {}\nWebcamModule.ɵfac = function WebcamModule_Factory(t) {\n  return new (t || WebcamModule)();\n};\nWebcamModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: WebcamModule\n});\nWebcamModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WebcamModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [COMPONENTS],\n      exports: [COMPONENTS]\n    }]\n  }], null, null);\n})();\nclass WebcamInitError {\n  constructor() {\n    this.message = null;\n    this.mediaStreamError = null;\n  }\n}\nclass WebcamMirrorProperties {}\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { WebcamComponent, WebcamImage, WebcamInitError, WebcamMirrorProperties, WebcamModule, WebcamUtil };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "Input", "Output", "ViewChild", "NgModule", "i1", "CommonModule", "_c0", "_c1", "WebcamComponent_div_3_Template", "rf", "ctx", "_r4", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "WebcamComponent_div_3_Template_div_click_0_listener", "ɵɵrestoreView", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "rotateVideoInput", "ɵɵelementEnd", "WebcamImage", "constructor", "imageAsDataUrl", "mimeType", "imageData", "_mimeType", "_imageAsBase64", "_imageAsDataUrl", "_imageData", "getDataFromDataUrl", "dataUrl", "replace", "imageAsBase64", "WebcamUtil", "getAvailableVideoInputs", "navigator", "mediaDevices", "enumerateDevices", "Promise", "reject", "resolve", "then", "devices", "filter", "device", "kind", "catch", "err", "message", "WebcamComponent", "width", "height", "videoOptions", "DEFAULT_VIDEO_OPTIONS", "allowCameraSwitch", "captureImageData", "imageType", "DEFAULT_IMAGE_TYPE", "imageQuality", "DEFAULT_IMAGE_QUALITY", "imageCapture", "initError", "imageClick", "cameraSwitched", "availableVideoInputs", "videoInitialized", "activeVideoInputIndex", "mediaStream", "activeVideoSettings", "trigger", "triggerSubscription", "unsubscribe", "subscribe", "takeSnapshot", "switchCamera", "switchCameraSubscription", "value", "switchToVideoInput", "getMediaConstraintsForDevice", "deviceId", "baseMediaTrackConstraints", "result", "exact", "getDeviceIdFromMediaStreamTrack", "mediaStreamTrack", "getSettings", "getConstraints", "deviceIdObj", "getValueFromConstrainDOMString", "getFacingModeFromMediaStreamTrack", "facingMode", "facingModeConstraint", "isUserFacing", "toLowerCase", "constrainDOMString", "String", "Array", "isArray", "length", "ngAfterViewInit", "detectAvailableDevices", "next", "ngOnDestroy", "stopMediaTracks", "unsubscribeFromSubscriptions", "_video", "nativeVideoElement", "dimensions", "videoWidth", "videoHeight", "_canvas", "canvas", "nativeElement", "context2d", "getContext", "drawImage", "quality", "toDataURL", "getImageData", "forward", "increment", "nextInputIndex", "initWebcam", "videoResize", "videoRatio", "getVideoAspectRatio", "Math", "min", "videoStyleClasses", "classes", "isMirrorImage", "trim", "video", "videoElement", "userVideoTrackConstraints", "getUserMedia", "videoTrackConstraints", "stream", "srcObject", "play", "getVideoTracks", "activeDeviceId", "findIndex", "mediaDeviceInfo", "mediaStreamError", "getActiveVideoTrack", "mirror", "mirrorImage", "x", "getTracks", "pause", "for<PERSON>ach", "track", "stop", "ɵfac", "WebcamComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "WebcamComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "outputs", "decls", "vars", "consts", "template", "WebcamComponent_Template", "WebcamComponent_Template_div_click_0_listener", "WebcamComponent_Template_video_resize_1_listener", "ɵɵtemplate", "ɵɵelement", "ɵɵadvance", "ɵɵclassMap", "ɵɵproperty", "dependencies", "NgIf", "styles", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "static", "COMPONENTS", "WebcamModule", "WebcamModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "WebcamInitError", "WebcamMirrorProperties"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/ngx-webcam/fesm2020/ngx-webcam.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\r\n * Container class for a captured webcam image\r\n * <AUTHOR> davidshen84\r\n */\r\nclass WebcamImage {\r\n    constructor(imageAsDataUrl, mimeType, imageData) {\r\n        this._mimeType = null;\r\n        this._imageAsBase64 = null;\r\n        this._imageAsDataUrl = null;\r\n        this._imageData = null;\r\n        this._mimeType = mimeType;\r\n        this._imageAsDataUrl = imageAsDataUrl;\r\n        this._imageData = imageData;\r\n    }\r\n    /**\r\n     * Extracts the Base64 data out of the given dataUrl.\r\n     * @param dataUrl the given dataUrl\r\n     * @param mimeType the mimeType of the data\r\n     */\r\n    static getDataFromDataUrl(dataUrl, mimeType) {\r\n        return dataUrl.replace(`data:${mimeType};base64,`, '');\r\n    }\r\n    /**\r\n     * Get the base64 encoded image data\r\n     * @returns base64 data of the image\r\n     */\r\n    get imageAsBase64() {\r\n        return this._imageAsBase64 ? this._imageAsBase64\r\n            : this._imageAsBase64 = WebcamImage.getDataFromDataUrl(this._imageAsDataUrl, this._mimeType);\r\n    }\r\n    /**\r\n     * Get the encoded image as dataUrl\r\n     * @returns the dataUrl of the image\r\n     */\r\n    get imageAsDataUrl() {\r\n        return this._imageAsDataUrl;\r\n    }\r\n    /**\r\n     * Get the ImageData object associated with the canvas' 2d context.\r\n     * @returns the ImageData of the canvas's 2d context.\r\n     */\r\n    get imageData() {\r\n        return this._imageData;\r\n    }\r\n}\n\nclass WebcamUtil {\r\n    /**\r\n     * Lists available videoInput devices\r\n     * @returns a list of media device info.\r\n     */\r\n    static getAvailableVideoInputs() {\r\n        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {\r\n            return Promise.reject('enumerateDevices() not supported.');\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            navigator.mediaDevices.enumerateDevices()\r\n                .then((devices) => {\r\n                resolve(devices.filter((device) => device.kind === 'videoinput'));\r\n            })\r\n                .catch(err => {\r\n                reject(err.message || err);\r\n            });\r\n        });\r\n    }\r\n}\n\nclass WebcamComponent {\r\n    constructor() {\r\n        /** Defines the max width of the webcam area in px */\r\n        this.width = 640;\r\n        /** Defines the max height of the webcam area in px */\r\n        this.height = 480;\r\n        /** Defines base constraints to apply when requesting video track from UserMedia */\r\n        this.videoOptions = WebcamComponent.DEFAULT_VIDEO_OPTIONS;\r\n        /** Flag to enable/disable camera switch. If enabled, a switch icon will be displayed if multiple cameras were found */\r\n        this.allowCameraSwitch = true;\r\n        /** Flag to control whether an ImageData object is stored into the WebcamImage object. */\r\n        this.captureImageData = false;\r\n        /** The image type to use when capturing snapshots */\r\n        this.imageType = WebcamComponent.DEFAULT_IMAGE_TYPE;\r\n        /** The image quality to use when capturing snapshots (number between 0 and 1) */\r\n        this.imageQuality = WebcamComponent.DEFAULT_IMAGE_QUALITY;\r\n        /** EventEmitter which fires when an image has been captured */\r\n        this.imageCapture = new EventEmitter();\r\n        /** Emits a mediaError if webcam cannot be initialized (e.g. missing user permissions) */\r\n        this.initError = new EventEmitter();\r\n        /** Emits when the webcam video was clicked */\r\n        this.imageClick = new EventEmitter();\r\n        /** Emits the active deviceId after the active video device was switched */\r\n        this.cameraSwitched = new EventEmitter();\r\n        /** available video devices */\r\n        this.availableVideoInputs = [];\r\n        /** Indicates whether the video device is ready to be switched */\r\n        this.videoInitialized = false;\r\n        /** Index of active video in availableVideoInputs */\r\n        this.activeVideoInputIndex = -1;\r\n        /** MediaStream object in use for streaming UserMedia data */\r\n        this.mediaStream = null;\r\n        /** width and height of the active video stream */\r\n        this.activeVideoSettings = null;\r\n    }\r\n    /**\r\n     * If the given Observable emits, an image will be captured and emitted through 'imageCapture' EventEmitter\r\n     */\r\n    set trigger(trigger) {\r\n        if (this.triggerSubscription) {\r\n            this.triggerSubscription.unsubscribe();\r\n        }\r\n        // Subscribe to events from this Observable to take snapshots\r\n        this.triggerSubscription = trigger.subscribe(() => {\r\n            this.takeSnapshot();\r\n        });\r\n    }\r\n    /**\r\n     * If the given Observable emits, the active webcam will be switched to the one indicated by the emitted value.\r\n     * @param switchCamera Indicates which webcam to switch to\r\n     *   true: cycle forwards through available webcams\r\n     *   false: cycle backwards through available webcams\r\n     *   string: activate the webcam with the given id\r\n     */\r\n    set switchCamera(switchCamera) {\r\n        if (this.switchCameraSubscription) {\r\n            this.switchCameraSubscription.unsubscribe();\r\n        }\r\n        // Subscribe to events from this Observable to switch video device\r\n        this.switchCameraSubscription = switchCamera.subscribe((value) => {\r\n            if (typeof value === 'string') {\r\n                // deviceId was specified\r\n                this.switchToVideoInput(value);\r\n            }\r\n            else {\r\n                // direction was specified\r\n                this.rotateVideoInput(value !== false);\r\n            }\r\n        });\r\n    }\r\n    /**\r\n     * Get MediaTrackConstraints to request streaming the given device\r\n     * @param deviceId\r\n     * @param baseMediaTrackConstraints base constraints to merge deviceId-constraint into\r\n     * @returns\r\n     */\r\n    static getMediaConstraintsForDevice(deviceId, baseMediaTrackConstraints) {\r\n        const result = baseMediaTrackConstraints ? baseMediaTrackConstraints : this.DEFAULT_VIDEO_OPTIONS;\r\n        if (deviceId) {\r\n            result.deviceId = { exact: deviceId };\r\n        }\r\n        return result;\r\n    }\r\n    /**\r\n     * Tries to harvest the deviceId from the given mediaStreamTrack object.\r\n     * Browsers populate this object differently; this method tries some different approaches\r\n     * to read the id.\r\n     * @param mediaStreamTrack\r\n     * @returns deviceId if found in the mediaStreamTrack\r\n     */\r\n    static getDeviceIdFromMediaStreamTrack(mediaStreamTrack) {\r\n        if (mediaStreamTrack.getSettings && mediaStreamTrack.getSettings() && mediaStreamTrack.getSettings().deviceId) {\r\n            return mediaStreamTrack.getSettings().deviceId;\r\n        }\r\n        else if (mediaStreamTrack.getConstraints && mediaStreamTrack.getConstraints() && mediaStreamTrack.getConstraints().deviceId) {\r\n            const deviceIdObj = mediaStreamTrack.getConstraints().deviceId;\r\n            return WebcamComponent.getValueFromConstrainDOMString(deviceIdObj);\r\n        }\r\n    }\r\n    /**\r\n     * Tries to harvest the facingMode from the given mediaStreamTrack object.\r\n     * Browsers populate this object differently; this method tries some different approaches\r\n     * to read the value.\r\n     * @param mediaStreamTrack\r\n     * @returns facingMode if found in the mediaStreamTrack\r\n     */\r\n    static getFacingModeFromMediaStreamTrack(mediaStreamTrack) {\r\n        if (mediaStreamTrack) {\r\n            if (mediaStreamTrack.getSettings && mediaStreamTrack.getSettings() && mediaStreamTrack.getSettings().facingMode) {\r\n                return mediaStreamTrack.getSettings().facingMode;\r\n            }\r\n            else if (mediaStreamTrack.getConstraints && mediaStreamTrack.getConstraints() && mediaStreamTrack.getConstraints().facingMode) {\r\n                const facingModeConstraint = mediaStreamTrack.getConstraints().facingMode;\r\n                return WebcamComponent.getValueFromConstrainDOMString(facingModeConstraint);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * Determines whether the given mediaStreamTrack claims itself as user facing\r\n     * @param mediaStreamTrack\r\n     */\r\n    static isUserFacing(mediaStreamTrack) {\r\n        const facingMode = WebcamComponent.getFacingModeFromMediaStreamTrack(mediaStreamTrack);\r\n        return facingMode ? 'user' === facingMode.toLowerCase() : false;\r\n    }\r\n    /**\r\n     * Extracts the value from the given ConstrainDOMString\r\n     * @param constrainDOMString\r\n     */\r\n    static getValueFromConstrainDOMString(constrainDOMString) {\r\n        if (constrainDOMString) {\r\n            if (constrainDOMString instanceof String) {\r\n                return String(constrainDOMString);\r\n            }\r\n            else if (Array.isArray(constrainDOMString) && Array(constrainDOMString).length > 0) {\r\n                return String(constrainDOMString[0]);\r\n            }\r\n            else if (typeof constrainDOMString === 'object') {\r\n                if (constrainDOMString['exact']) {\r\n                    return String(constrainDOMString['exact']);\r\n                }\r\n                else if (constrainDOMString['ideal']) {\r\n                    return String(constrainDOMString['ideal']);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n    ngAfterViewInit() {\r\n        this.detectAvailableDevices()\r\n            .then(() => {\r\n            // start video\r\n            this.switchToVideoInput(null);\r\n        })\r\n            .catch((err) => {\r\n            this.initError.next({ message: err });\r\n            // fallback: still try to load webcam, even if device enumeration failed\r\n            this.switchToVideoInput(null);\r\n        });\r\n    }\r\n    ngOnDestroy() {\r\n        this.stopMediaTracks();\r\n        this.unsubscribeFromSubscriptions();\r\n    }\r\n    /**\r\n     * Takes a snapshot of the current webcam's view and emits the image as an event\r\n     */\r\n    takeSnapshot() {\r\n        // set canvas size to actual video size\r\n        const _video = this.nativeVideoElement;\r\n        const dimensions = { width: this.width, height: this.height };\r\n        if (_video.videoWidth) {\r\n            dimensions.width = _video.videoWidth;\r\n            dimensions.height = _video.videoHeight;\r\n        }\r\n        const _canvas = this.canvas.nativeElement;\r\n        _canvas.width = dimensions.width;\r\n        _canvas.height = dimensions.height;\r\n        // paint snapshot image to canvas\r\n        const context2d = _canvas.getContext('2d');\r\n        context2d.drawImage(_video, 0, 0);\r\n        // read canvas content as image\r\n        const mimeType = this.imageType ? this.imageType : WebcamComponent.DEFAULT_IMAGE_TYPE;\r\n        const quality = this.imageQuality ? this.imageQuality : WebcamComponent.DEFAULT_IMAGE_QUALITY;\r\n        const dataUrl = _canvas.toDataURL(mimeType, quality);\r\n        // get the ImageData object from the canvas' context.\r\n        let imageData = null;\r\n        if (this.captureImageData) {\r\n            imageData = context2d.getImageData(0, 0, _canvas.width, _canvas.height);\r\n        }\r\n        this.imageCapture.next(new WebcamImage(dataUrl, mimeType, imageData));\r\n    }\r\n    /**\r\n     * Switches to the next/previous video device\r\n     * @param forward\r\n     */\r\n    rotateVideoInput(forward) {\r\n        if (this.availableVideoInputs && this.availableVideoInputs.length > 1) {\r\n            const increment = forward ? 1 : (this.availableVideoInputs.length - 1);\r\n            const nextInputIndex = (this.activeVideoInputIndex + increment) % this.availableVideoInputs.length;\r\n            this.switchToVideoInput(this.availableVideoInputs[nextInputIndex].deviceId);\r\n        }\r\n    }\r\n    /**\r\n     * Switches the camera-view to the specified video device\r\n     */\r\n    switchToVideoInput(deviceId) {\r\n        this.videoInitialized = false;\r\n        this.stopMediaTracks();\r\n        this.initWebcam(deviceId, this.videoOptions);\r\n    }\r\n    /**\r\n     * Event-handler for video resize event.\r\n     * Triggers Angular change detection so that new video dimensions get applied\r\n     */\r\n    videoResize() {\r\n        // here to trigger Angular change detection\r\n    }\r\n    get videoWidth() {\r\n        const videoRatio = this.getVideoAspectRatio();\r\n        return Math.min(this.width, this.height * videoRatio);\r\n    }\r\n    get videoHeight() {\r\n        const videoRatio = this.getVideoAspectRatio();\r\n        return Math.min(this.height, this.width / videoRatio);\r\n    }\r\n    get videoStyleClasses() {\r\n        let classes = '';\r\n        if (this.isMirrorImage()) {\r\n            classes += 'mirrored ';\r\n        }\r\n        return classes.trim();\r\n    }\r\n    get nativeVideoElement() {\r\n        return this.video.nativeElement;\r\n    }\r\n    /**\r\n     * Returns the video aspect ratio of the active video stream\r\n     */\r\n    getVideoAspectRatio() {\r\n        // calculate ratio from video element dimensions if present\r\n        const videoElement = this.nativeVideoElement;\r\n        if (videoElement.videoWidth && videoElement.videoWidth > 0 &&\r\n            videoElement.videoHeight && videoElement.videoHeight > 0) {\r\n            return videoElement.videoWidth / videoElement.videoHeight;\r\n        }\r\n        // nothing present - calculate ratio based on width/height params\r\n        return this.width / this.height;\r\n    }\r\n    /**\r\n     * Init webcam live view\r\n     */\r\n    initWebcam(deviceId, userVideoTrackConstraints) {\r\n        const _video = this.nativeVideoElement;\r\n        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\r\n            // merge deviceId -> userVideoTrackConstraints\r\n            const videoTrackConstraints = WebcamComponent.getMediaConstraintsForDevice(deviceId, userVideoTrackConstraints);\r\n            navigator.mediaDevices.getUserMedia({ video: videoTrackConstraints })\r\n                .then((stream) => {\r\n                this.mediaStream = stream;\r\n                _video.srcObject = stream;\r\n                _video.play();\r\n                this.activeVideoSettings = stream.getVideoTracks()[0].getSettings();\r\n                const activeDeviceId = WebcamComponent.getDeviceIdFromMediaStreamTrack(stream.getVideoTracks()[0]);\r\n                this.cameraSwitched.next(activeDeviceId);\r\n                // Initial detect may run before user gave permissions, returning no deviceIds. This prevents later camera switches. (#47)\r\n                // Run detect once again within getUserMedia callback, to make sure this time we have permissions and get deviceIds.\r\n                this.detectAvailableDevices()\r\n                    .then(() => {\r\n                    this.activeVideoInputIndex = activeDeviceId ? this.availableVideoInputs\r\n                        .findIndex((mediaDeviceInfo) => mediaDeviceInfo.deviceId === activeDeviceId) : -1;\r\n                    this.videoInitialized = true;\r\n                })\r\n                    .catch(() => {\r\n                    this.activeVideoInputIndex = -1;\r\n                    this.videoInitialized = true;\r\n                });\r\n            })\r\n                .catch((err) => {\r\n                this.initError.next({ message: err.message, mediaStreamError: err });\r\n            });\r\n        }\r\n        else {\r\n            this.initError.next({ message: 'Cannot read UserMedia from MediaDevices.' });\r\n        }\r\n    }\r\n    getActiveVideoTrack() {\r\n        return this.mediaStream ? this.mediaStream.getVideoTracks()[0] : null;\r\n    }\r\n    isMirrorImage() {\r\n        if (!this.getActiveVideoTrack()) {\r\n            return false;\r\n        }\r\n        // check for explicit mirror override parameter\r\n        {\r\n            let mirror = 'auto';\r\n            if (this.mirrorImage) {\r\n                if (typeof this.mirrorImage === 'string') {\r\n                    mirror = String(this.mirrorImage).toLowerCase();\r\n                }\r\n                else {\r\n                    // WebcamMirrorProperties\r\n                    if (this.mirrorImage.x) {\r\n                        mirror = this.mirrorImage.x.toLowerCase();\r\n                    }\r\n                }\r\n            }\r\n            switch (mirror) {\r\n                case 'always':\r\n                    return true;\r\n                case 'never':\r\n                    return false;\r\n            }\r\n        }\r\n        // default: enable mirroring if webcam is user facing\r\n        return WebcamComponent.isUserFacing(this.getActiveVideoTrack());\r\n    }\r\n    /**\r\n     * Stops all active media tracks.\r\n     * This prevents the webcam from being indicated as active,\r\n     * even if it is no longer used by this component.\r\n     */\r\n    stopMediaTracks() {\r\n        if (this.mediaStream && this.mediaStream.getTracks) {\r\n            // pause video to prevent mobile browser freezes\r\n            this.nativeVideoElement.pause();\r\n            // getTracks() returns all media tracks (video+audio)\r\n            this.mediaStream.getTracks()\r\n                .forEach((track) => track.stop());\r\n        }\r\n    }\r\n    /**\r\n     * Unsubscribe from all open subscriptions\r\n     */\r\n    unsubscribeFromSubscriptions() {\r\n        if (this.triggerSubscription) {\r\n            this.triggerSubscription.unsubscribe();\r\n        }\r\n        if (this.switchCameraSubscription) {\r\n            this.switchCameraSubscription.unsubscribe();\r\n        }\r\n    }\r\n    /**\r\n     * Reads available input devices\r\n     */\r\n    detectAvailableDevices() {\r\n        return new Promise((resolve, reject) => {\r\n            WebcamUtil.getAvailableVideoInputs()\r\n                .then((devices) => {\r\n                this.availableVideoInputs = devices;\r\n                resolve(devices);\r\n            })\r\n                .catch(err => {\r\n                this.availableVideoInputs = [];\r\n                reject(err);\r\n            });\r\n        });\r\n    }\r\n}\r\nWebcamComponent.DEFAULT_VIDEO_OPTIONS = { facingMode: 'environment' };\r\nWebcamComponent.DEFAULT_IMAGE_TYPE = 'image/jpeg';\r\nWebcamComponent.DEFAULT_IMAGE_QUALITY = 0.92;\r\nWebcamComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: WebcamComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\r\nWebcamComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.0\", type: WebcamComponent, selector: \"webcam\", inputs: { width: \"width\", height: \"height\", videoOptions: \"videoOptions\", allowCameraSwitch: \"allowCameraSwitch\", mirrorImage: \"mirrorImage\", captureImageData: \"captureImageData\", imageType: \"imageType\", imageQuality: \"imageQuality\", trigger: \"trigger\", switchCamera: \"switchCamera\" }, outputs: { imageCapture: \"imageCapture\", initError: \"initError\", imageClick: \"imageClick\", cameraSwitched: \"cameraSwitched\" }, viewQueries: [{ propertyName: \"video\", first: true, predicate: [\"video\"], descendants: true, static: true }, { propertyName: \"canvas\", first: true, predicate: [\"canvas\"], descendants: true, static: true }], ngImport: i0, template: \"<div class=\\\"webcam-wrapper\\\" (click)=\\\"imageClick.next();\\\">\\r\\n  <video #video [width]=\\\"videoWidth\\\" [height]=\\\"videoHeight\\\" [class]=\\\"videoStyleClasses\\\" autoplay muted playsinline (resize)=\\\"videoResize()\\\"></video>\\r\\n  <div class=\\\"camera-switch\\\" *ngIf=\\\"allowCameraSwitch && availableVideoInputs.length > 1 && videoInitialized\\\" (click)=\\\"rotateVideoInput(true)\\\"></div>\\r\\n  <canvas #canvas [width]=\\\"width\\\" [height]=\\\"height\\\"></canvas>\\r\\n</div>\\r\\n\", styles: [\".webcam-wrapper{display:inline-block;position:relative;line-height:0}.webcam-wrapper video.mirrored{transform:scaleX(-1)}.webcam-wrapper canvas{display:none}.webcam-wrapper .camera-switch{background-color:#0000001a;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAE9UlEQVR42u2aT2hdRRTGf+cRQqghSqihdBFDkRISK2KDfzDWxHaRQHEhaINKqa1gKQhd6EZLN+IidCH+Q0oWIkVRC21BQxXRitVaSbKoJSGtYGoK2tQ/tU1jY5v0c5F54Xl7b/KSO/PyEt+3e5f75p7zzZwzZ74zUEIJJfyfYaEGllQGVAGZlENdBy6Z2cSiYFTSKkkfS/pH/nBF0kFJdUW9AiRVASeAukD8DgNrzOySrwEzng18KaDzALXuG8W3AiStAvqBisBRNg40mtlPxbYCOgvgPO4bncWW+JpVeDQXRQhIygDfA00F5r0XuNfMrgclQFI98DDQCNQA5ZFXqoCWBVp8XwHRHeEqcN7loy/NbHBesyqpQ1KfFj/6nC+ZvFaApFrgPaCZpYVvgCfNbDiRAElNwGFg+RIt/X8H2s2s9wYCJDUAR4HqJX7++RN40MwGpgmQVAH0AQ2BPz4AHHPl8nBOAqtyFWQjsA6oL4Ada81sPDv7uwImod8kvSJp9RyS8O2SXnb/DYVd2Y9VSroQ4ANXJO2WVJmixqh0kzMWwL4LkiqRtDnA4D1zmfE8j9g9AezcnAHaPcfXdbfdnPZ2Yps6+DwAvO/Z1naTdApY7Xng48BDZnY1MpMVQBuw3iXc5Tnb0wBwBPjUzP6eoezuArZ6svM0geJLkvZEYnl3nkntoqROSbckSW2Suj3ZOIangc7GPJuUtNGdFIfmMeavktoSSKiW9LMPw30Q8JqkekmjCbOZRhuclLQjgYSNxUBAj6RyZ9ATgUJpUtJTCSR8vpAEXHAyWK5BXYFIGHOlepSAloUk4NEYgyoknQhEwhFJ0e8h6VSaQeerCb5uZgdi9utxYBNwOUD93hIVXswM4INCi6K9wAszFC2DwLOBDjHbYp59karIUnRdzYy/3ClqVklaUhfwTICj7K25OqA7a4wWagVsm4Me/xzwg2cCqqONFzO7DPxSCAJi436GUBgHHguQD2oTlJ55oSzP9ybccsttSJw1szdjFOSnI/8dTCGZHwcORp4Nx7y3B1iZ8/sm4MW8/Euxg5wIsS/HaAp3zeP4/G7obRDXI4jiTIA22H7Xdc7X+S3A5lC7QBQ357aq3VAjCeSkwUfAJrfvz+R8A9ADLAtZB+TinpjC5JMA+//jwPZZnF8G7J+L8z4IWB/zbG+gIujVWfLBW/NStVMmqaG4POJRsIjix7h8IGnLQuoBbQki5sVAJHyYm7YkNaRRtXwQ8G1cHpX0iKRrgUjYno17Sf0LrQhJUkdCeHWkVITGJI0k1QeS3ikGSUzOyJUJJNznYneuOCnpTldcxa2kP3xJYqOeSDjqZG8ShJLnE8TTuMS6Iyu1BW7djZqkfo9N0QOuYJmYQddfB7RG+gLTNzqAY9FrL+5/nwEbvDdJJe3zzOrhNP3AWRqmk55t3ZcBuj3b2gb0Sbrbo/NNzk7fFzu7s/E5EiC+rrmeQU0Kx2skvRFoOx2ZzlmSdgbsw49JetvtBpk8nM64d/cGbNtJ0s7cGyJlwHeEv+t3nqnLSgPAUOSGyG3AHUxdzqoJbEcvcL+ZTeTeEapzJKxgaeOcc/7Mf06D7kFrguS0VDAMtGadv+E47DT9tcChJej8ISfpD+abgTe45uOkFi8mnQ+JBVQ+d4VXuOptjavcyot8pq86mfwk8LWZnaOEEkoooYQSSojDv8AhQNeGfe0jAAAAAElFTkSuQmCC);background-repeat:no-repeat;border-radius:5px;position:absolute;right:13px;top:10px;height:48px;width:48px;background-size:80%;cursor:pointer;background-position:center;transition:background-color .2s ease}.webcam-wrapper .camera-switch:hover{background-color:#0000002e}\\n\"], directives: [{ type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: WebcamComponent, decorators: [{\r\n            type: Component,\r\n            args: [{ selector: 'webcam', template: \"<div class=\\\"webcam-wrapper\\\" (click)=\\\"imageClick.next();\\\">\\r\\n  <video #video [width]=\\\"videoWidth\\\" [height]=\\\"videoHeight\\\" [class]=\\\"videoStyleClasses\\\" autoplay muted playsinline (resize)=\\\"videoResize()\\\"></video>\\r\\n  <div class=\\\"camera-switch\\\" *ngIf=\\\"allowCameraSwitch && availableVideoInputs.length > 1 && videoInitialized\\\" (click)=\\\"rotateVideoInput(true)\\\"></div>\\r\\n  <canvas #canvas [width]=\\\"width\\\" [height]=\\\"height\\\"></canvas>\\r\\n</div>\\r\\n\", styles: [\".webcam-wrapper{display:inline-block;position:relative;line-height:0}.webcam-wrapper video.mirrored{transform:scaleX(-1)}.webcam-wrapper canvas{display:none}.webcam-wrapper .camera-switch{background-color:#0000001a;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAE9UlEQVR42u2aT2hdRRTGf+cRQqghSqihdBFDkRISK2KDfzDWxHaRQHEhaINKqa1gKQhd6EZLN+IidCH+Q0oWIkVRC21BQxXRitVaSbKoJSGtYGoK2tQ/tU1jY5v0c5F54Xl7b/KSO/PyEt+3e5f75p7zzZwzZ74zUEIJJfyfYaEGllQGVAGZlENdBy6Z2cSiYFTSKkkfS/pH/nBF0kFJdUW9AiRVASeAukD8DgNrzOySrwEzng18KaDzALXuG8W3AiStAvqBisBRNg40mtlPxbYCOgvgPO4bncWW+JpVeDQXRQhIygDfA00F5r0XuNfMrgclQFI98DDQCNQA5ZFXqoCWBVp8XwHRHeEqcN7loy/NbHBesyqpQ1KfFj/6nC+ZvFaApFrgPaCZpYVvgCfNbDiRAElNwGFg+RIt/X8H2s2s9wYCJDUAR4HqJX7++RN40MwGpgmQVAH0AQ2BPz4AHHPl8nBOAqtyFWQjsA6oL4Ada81sPDv7uwImod8kvSJp9RyS8O2SXnb/DYVd2Y9VSroQ4ANXJO2WVJmixqh0kzMWwL4LkiqRtDnA4D1zmfE8j9g9AezcnAHaPcfXdbfdnPZ2Yps6+DwAvO/Z1naTdApY7Xng48BDZnY1MpMVQBuw3iXc5Tnb0wBwBPjUzP6eoezuArZ6svM0geJLkvZEYnl3nkntoqROSbckSW2Suj3ZOIangc7GPJuUtNGdFIfmMeavktoSSKiW9LMPw30Q8JqkekmjCbOZRhuclLQjgYSNxUBAj6RyZ9ATgUJpUtJTCSR8vpAEXHAyWK5BXYFIGHOlepSAloUk4NEYgyoknQhEwhFJ0e8h6VSaQeerCb5uZgdi9utxYBNwOUD93hIVXswM4INCi6K9wAszFC2DwLOBDjHbYp59karIUnRdzYy/3ClqVklaUhfwTICj7K25OqA7a4wWagVsm4Me/xzwg2cCqqONFzO7DPxSCAJi436GUBgHHguQD2oTlJ55oSzP9ybccsttSJw1szdjFOSnI/8dTCGZHwcORp4Nx7y3B1iZ8/sm4MW8/Euxg5wIsS/HaAp3zeP4/G7obRDXI4jiTIA22H7Xdc7X+S3A5lC7QBQ357aq3VAjCeSkwUfAJrfvz+R8A9ADLAtZB+TinpjC5JMA+//jwPZZnF8G7J+L8z4IWB/zbG+gIujVWfLBW/NStVMmqaG4POJRsIjix7h8IGnLQuoBbQki5sVAJHyYm7YkNaRRtXwQ8G1cHpX0iKRrgUjYno17Sf0LrQhJUkdCeHWkVITGJI0k1QeS3ikGSUzOyJUJJNznYneuOCnpTldcxa2kP3xJYqOeSDjqZG8ShJLnE8TTuMS6Iyu1BW7djZqkfo9N0QOuYJmYQddfB7RG+gLTNzqAY9FrL+5/nwEbvDdJJe3zzOrhNP3AWRqmk55t3ZcBuj3b2gb0Sbrbo/NNzk7fFzu7s/E5EiC+rrmeQU0Kx2skvRFoOx2ZzlmSdgbsw49JetvtBpk8nM64d/cGbNtJ0s7cGyJlwHeEv+t3nqnLSgPAUOSGyG3AHUxdzqoJbEcvcL+ZTeTeEapzJKxgaeOcc/7Mf06D7kFrguS0VDAMtGadv+E47DT9tcChJej8ISfpD+abgTe45uOkFi8mnQ+JBVQ+d4VXuOptjavcyot8pq86mfwk8LWZnaOEEkoooYQSSojDv8AhQNeGfe0jAAAAAElFTkSuQmCC);background-repeat:no-repeat;border-radius:5px;position:absolute;right:13px;top:10px;height:48px;width:48px;background-size:80%;cursor:pointer;background-position:center;transition:background-color .2s ease}.webcam-wrapper .camera-switch:hover{background-color:#0000002e}\\n\"] }]\r\n        }], propDecorators: { width: [{\r\n                type: Input\r\n            }], height: [{\r\n                type: Input\r\n            }], videoOptions: [{\r\n                type: Input\r\n            }], allowCameraSwitch: [{\r\n                type: Input\r\n            }], mirrorImage: [{\r\n                type: Input\r\n            }], captureImageData: [{\r\n                type: Input\r\n            }], imageType: [{\r\n                type: Input\r\n            }], imageQuality: [{\r\n                type: Input\r\n            }], imageCapture: [{\r\n                type: Output\r\n            }], initError: [{\r\n                type: Output\r\n            }], imageClick: [{\r\n                type: Output\r\n            }], cameraSwitched: [{\r\n                type: Output\r\n            }], video: [{\r\n                type: ViewChild,\r\n                args: ['video', { static: true }]\r\n            }], canvas: [{\r\n                type: ViewChild,\r\n                args: ['canvas', { static: true }]\r\n            }], trigger: [{\r\n                type: Input\r\n            }], switchCamera: [{\r\n                type: Input\r\n            }] } });\n\nconst COMPONENTS = [\r\n    WebcamComponent\r\n];\r\nclass WebcamModule {\r\n}\r\nWebcamModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: WebcamModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nWebcamModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: WebcamModule, declarations: [WebcamComponent], imports: [CommonModule], exports: [WebcamComponent] });\r\nWebcamModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: WebcamModule, imports: [[\r\n            CommonModule\r\n        ]] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: WebcamModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    imports: [\r\n                        CommonModule\r\n                    ],\r\n                    declarations: [\r\n                        COMPONENTS\r\n                    ],\r\n                    exports: [\r\n                        COMPONENTS\r\n                    ]\r\n                }]\r\n        }] });\n\nclass WebcamInitError {\r\n    constructor() {\r\n        this.message = null;\r\n        this.mediaStreamError = null;\r\n    }\r\n}\n\nclass WebcamMirrorProperties {\r\n}\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { WebcamComponent, WebcamImage, WebcamInitError, WebcamMirrorProperties, WebcamModule, WebcamUtil };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC3F,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA6akGd,EAAE,CAAAe,gBAAA;IAAFf,EAAE,CAAAgB,cAAA,YAC2hC,CAAC;IAD9hChB,EAAE,CAAAiB,UAAA,mBAAAC,oDAAA;MAAFlB,EAAE,CAAAmB,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFpB,EAAE,CAAAqB,aAAA;MAAA,OAAFrB,EAAE,CAAAsB,WAAA,CACmgCF,MAAA,CAAAG,gBAAA,CAAiB,IAAI,EAAC;IAAA,CAAC,CAAC;IAD7hCvB,EAAE,CAAAwB,YAAA,CACiiC,CAAC;EAAA;AAAA;AA1atoC,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,cAAc,EAAEC,QAAQ,EAAEC,SAAS,EAAE;IAC7C,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACH,SAAS,GAAGF,QAAQ;IACzB,IAAI,CAACI,eAAe,GAAGL,cAAc;IACrC,IAAI,CAACM,UAAU,GAAGJ,SAAS;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOK,kBAAkBA,CAACC,OAAO,EAAEP,QAAQ,EAAE;IACzC,OAAOO,OAAO,CAACC,OAAO,CAAE,QAAOR,QAAS,UAAS,EAAE,EAAE,CAAC;EAC1D;EACA;AACJ;AACA;AACA;EACI,IAAIS,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACN,cAAc,GAAG,IAAI,CAACA,cAAc,GAC1C,IAAI,CAACA,cAAc,GAAGN,WAAW,CAACS,kBAAkB,CAAC,IAAI,CAACF,eAAe,EAAE,IAAI,CAACF,SAAS,CAAC;EACpG;EACA;AACJ;AACA;AACA;EACI,IAAIH,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACK,eAAe;EAC/B;EACA;AACJ;AACA;AACA;EACI,IAAIH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACI,UAAU;EAC1B;AACJ;AAEA,MAAMK,UAAU,CAAC;EACb;AACJ;AACA;AACA;EACI,OAAOC,uBAAuBA,CAAA,EAAG;IAC7B,IAAI,CAACC,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,gBAAgB,EAAE;MACrE,OAAOC,OAAO,CAACC,MAAM,CAAC,mCAAmC,CAAC;IAC9D;IACA,OAAO,IAAID,OAAO,CAAC,CAACE,OAAO,EAAED,MAAM,KAAK;MACpCJ,SAAS,CAACC,YAAY,CAACC,gBAAgB,CAAC,CAAC,CACpCI,IAAI,CAAEC,OAAO,IAAK;QACnBF,OAAO,CAACE,OAAO,CAACC,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC,CAAC;MACrE,CAAC,CAAC,CACGC,KAAK,CAACC,GAAG,IAAI;QACdR,MAAM,CAACQ,GAAG,CAACC,OAAO,IAAID,GAAG,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AAEA,MAAME,eAAe,CAAC;EAClB5B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC6B,KAAK,GAAG,GAAG;IAChB;IACA,IAAI,CAACC,MAAM,GAAG,GAAG;IACjB;IACA,IAAI,CAACC,YAAY,GAAGH,eAAe,CAACI,qBAAqB;IACzD;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;IACA,IAAI,CAACC,SAAS,GAAGP,eAAe,CAACQ,kBAAkB;IACnD;IACA,IAAI,CAACC,YAAY,GAAGT,eAAe,CAACU,qBAAqB;IACzD;IACA,IAAI,CAACC,YAAY,GAAG,IAAIhE,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACiE,SAAS,GAAG,IAAIjE,YAAY,CAAC,CAAC;IACnC;IACA,IAAI,CAACkE,UAAU,GAAG,IAAIlE,YAAY,CAAC,CAAC;IACpC;IACA,IAAI,CAACmE,cAAc,GAAG,IAAInE,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACoE,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;IACA,IAAI,CAACC,qBAAqB,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACnC;EACA;AACJ;AACA;EACI,IAAIC,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACC,WAAW,CAAC,CAAC;IAC1C;IACA;IACA,IAAI,CAACD,mBAAmB,GAAGD,OAAO,CAACG,SAAS,CAAC,MAAM;MAC/C,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACA,wBAAwB,CAACJ,WAAW,CAAC,CAAC;IAC/C;IACA;IACA,IAAI,CAACI,wBAAwB,GAAGD,YAAY,CAACF,SAAS,CAAEI,KAAK,IAAK;MAC9D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B;QACA,IAAI,CAACC,kBAAkB,CAACD,KAAK,CAAC;MAClC,CAAC,MACI;QACD;QACA,IAAI,CAAC1D,gBAAgB,CAAC0D,KAAK,KAAK,KAAK,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOE,4BAA4BA,CAACC,QAAQ,EAAEC,yBAAyB,EAAE;IACrE,MAAMC,MAAM,GAAGD,yBAAyB,GAAGA,yBAAyB,GAAG,IAAI,CAAC3B,qBAAqB;IACjG,IAAI0B,QAAQ,EAAE;MACVE,MAAM,CAACF,QAAQ,GAAG;QAAEG,KAAK,EAAEH;MAAS,CAAC;IACzC;IACA,OAAOE,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOE,+BAA+BA,CAACC,gBAAgB,EAAE;IACrD,IAAIA,gBAAgB,CAACC,WAAW,IAAID,gBAAgB,CAACC,WAAW,CAAC,CAAC,IAAID,gBAAgB,CAACC,WAAW,CAAC,CAAC,CAACN,QAAQ,EAAE;MAC3G,OAAOK,gBAAgB,CAACC,WAAW,CAAC,CAAC,CAACN,QAAQ;IAClD,CAAC,MACI,IAAIK,gBAAgB,CAACE,cAAc,IAAIF,gBAAgB,CAACE,cAAc,CAAC,CAAC,IAAIF,gBAAgB,CAACE,cAAc,CAAC,CAAC,CAACP,QAAQ,EAAE;MACzH,MAAMQ,WAAW,GAAGH,gBAAgB,CAACE,cAAc,CAAC,CAAC,CAACP,QAAQ;MAC9D,OAAO9B,eAAe,CAACuC,8BAA8B,CAACD,WAAW,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOE,iCAAiCA,CAACL,gBAAgB,EAAE;IACvD,IAAIA,gBAAgB,EAAE;MAClB,IAAIA,gBAAgB,CAACC,WAAW,IAAID,gBAAgB,CAACC,WAAW,CAAC,CAAC,IAAID,gBAAgB,CAACC,WAAW,CAAC,CAAC,CAACK,UAAU,EAAE;QAC7G,OAAON,gBAAgB,CAACC,WAAW,CAAC,CAAC,CAACK,UAAU;MACpD,CAAC,MACI,IAAIN,gBAAgB,CAACE,cAAc,IAAIF,gBAAgB,CAACE,cAAc,CAAC,CAAC,IAAIF,gBAAgB,CAACE,cAAc,CAAC,CAAC,CAACI,UAAU,EAAE;QAC3H,MAAMC,oBAAoB,GAAGP,gBAAgB,CAACE,cAAc,CAAC,CAAC,CAACI,UAAU;QACzE,OAAOzC,eAAe,CAACuC,8BAA8B,CAACG,oBAAoB,CAAC;MAC/E;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI,OAAOC,YAAYA,CAACR,gBAAgB,EAAE;IAClC,MAAMM,UAAU,GAAGzC,eAAe,CAACwC,iCAAiC,CAACL,gBAAgB,CAAC;IACtF,OAAOM,UAAU,GAAG,MAAM,KAAKA,UAAU,CAACG,WAAW,CAAC,CAAC,GAAG,KAAK;EACnE;EACA;AACJ;AACA;AACA;EACI,OAAOL,8BAA8BA,CAACM,kBAAkB,EAAE;IACtD,IAAIA,kBAAkB,EAAE;MACpB,IAAIA,kBAAkB,YAAYC,MAAM,EAAE;QACtC,OAAOA,MAAM,CAACD,kBAAkB,CAAC;MACrC,CAAC,MACI,IAAIE,KAAK,CAACC,OAAO,CAACH,kBAAkB,CAAC,IAAIE,KAAK,CAACF,kBAAkB,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;QAChF,OAAOH,MAAM,CAACD,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MACI,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;QAC7C,IAAIA,kBAAkB,CAAC,OAAO,CAAC,EAAE;UAC7B,OAAOC,MAAM,CAACD,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC,MACI,IAAIA,kBAAkB,CAAC,OAAO,CAAC,EAAE;UAClC,OAAOC,MAAM,CAACD,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC9C;MACJ;IACJ;IACA,OAAO,IAAI;EACf;EACAK,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,sBAAsB,CAAC,CAAC,CACxB3D,IAAI,CAAC,MAAM;MACZ;MACA,IAAI,CAACoC,kBAAkB,CAAC,IAAI,CAAC;IACjC,CAAC,CAAC,CACG/B,KAAK,CAAEC,GAAG,IAAK;MAChB,IAAI,CAACc,SAAS,CAACwC,IAAI,CAAC;QAAErD,OAAO,EAAED;MAAI,CAAC,CAAC;MACrC;MACA,IAAI,CAAC8B,kBAAkB,CAAC,IAAI,CAAC;IACjC,CAAC,CAAC;EACN;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EACA;AACJ;AACA;EACI/B,YAAYA,CAAA,EAAG;IACX;IACA,MAAMgC,MAAM,GAAG,IAAI,CAACC,kBAAkB;IACtC,MAAMC,UAAU,GAAG;MAAEzD,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEC,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC;IAC7D,IAAIsD,MAAM,CAACG,UAAU,EAAE;MACnBD,UAAU,CAACzD,KAAK,GAAGuD,MAAM,CAACG,UAAU;MACpCD,UAAU,CAACxD,MAAM,GAAGsD,MAAM,CAACI,WAAW;IAC1C;IACA,MAAMC,OAAO,GAAG,IAAI,CAACC,MAAM,CAACC,aAAa;IACzCF,OAAO,CAAC5D,KAAK,GAAGyD,UAAU,CAACzD,KAAK;IAChC4D,OAAO,CAAC3D,MAAM,GAAGwD,UAAU,CAACxD,MAAM;IAClC;IACA,MAAM8D,SAAS,GAAGH,OAAO,CAACI,UAAU,CAAC,IAAI,CAAC;IAC1CD,SAAS,CAACE,SAAS,CAACV,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC;IACA,MAAMlF,QAAQ,GAAG,IAAI,CAACiC,SAAS,GAAG,IAAI,CAACA,SAAS,GAAGP,eAAe,CAACQ,kBAAkB;IACrF,MAAM2D,OAAO,GAAG,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACA,YAAY,GAAGT,eAAe,CAACU,qBAAqB;IAC7F,MAAM7B,OAAO,GAAGgF,OAAO,CAACO,SAAS,CAAC9F,QAAQ,EAAE6F,OAAO,CAAC;IACpD;IACA,IAAI5F,SAAS,GAAG,IAAI;IACpB,IAAI,IAAI,CAAC+B,gBAAgB,EAAE;MACvB/B,SAAS,GAAGyF,SAAS,CAACK,YAAY,CAAC,CAAC,EAAE,CAAC,EAAER,OAAO,CAAC5D,KAAK,EAAE4D,OAAO,CAAC3D,MAAM,CAAC;IAC3E;IACA,IAAI,CAACS,YAAY,CAACyC,IAAI,CAAC,IAAIjF,WAAW,CAACU,OAAO,EAAEP,QAAQ,EAAEC,SAAS,CAAC,CAAC;EACzE;EACA;AACJ;AACA;AACA;EACIN,gBAAgBA,CAACqG,OAAO,EAAE;IACtB,IAAI,IAAI,CAACvD,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACkC,MAAM,GAAG,CAAC,EAAE;MACnE,MAAMsB,SAAS,GAAGD,OAAO,GAAG,CAAC,GAAI,IAAI,CAACvD,oBAAoB,CAACkC,MAAM,GAAG,CAAE;MACtE,MAAMuB,cAAc,GAAG,CAAC,IAAI,CAACvD,qBAAqB,GAAGsD,SAAS,IAAI,IAAI,CAACxD,oBAAoB,CAACkC,MAAM;MAClG,IAAI,CAACrB,kBAAkB,CAAC,IAAI,CAACb,oBAAoB,CAACyD,cAAc,CAAC,CAAC1C,QAAQ,CAAC;IAC/E;EACJ;EACA;AACJ;AACA;EACIF,kBAAkBA,CAACE,QAAQ,EAAE;IACzB,IAAI,CAACd,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACsC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACmB,UAAU,CAAC3C,QAAQ,EAAE,IAAI,CAAC3B,YAAY,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIuE,WAAWA,CAAA,EAAG;IACV;EAAA;EAEJ,IAAIf,UAAUA,CAAA,EAAG;IACb,MAAMgB,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC7C,OAAOC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC7E,KAAK,EAAE,IAAI,CAACC,MAAM,GAAGyE,UAAU,CAAC;EACzD;EACA,IAAIf,WAAWA,CAAA,EAAG;IACd,MAAMe,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC7C,OAAOC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5E,MAAM,EAAE,IAAI,CAACD,KAAK,GAAG0E,UAAU,CAAC;EACzD;EACA,IAAII,iBAAiBA,CAAA,EAAG;IACpB,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtBD,OAAO,IAAI,WAAW;IAC1B;IACA,OAAOA,OAAO,CAACE,IAAI,CAAC,CAAC;EACzB;EACA,IAAIzB,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC0B,KAAK,CAACpB,aAAa;EACnC;EACA;AACJ;AACA;EACIa,mBAAmBA,CAAA,EAAG;IAClB;IACA,MAAMQ,YAAY,GAAG,IAAI,CAAC3B,kBAAkB;IAC5C,IAAI2B,YAAY,CAACzB,UAAU,IAAIyB,YAAY,CAACzB,UAAU,GAAG,CAAC,IACtDyB,YAAY,CAACxB,WAAW,IAAIwB,YAAY,CAACxB,WAAW,GAAG,CAAC,EAAE;MAC1D,OAAOwB,YAAY,CAACzB,UAAU,GAAGyB,YAAY,CAACxB,WAAW;IAC7D;IACA;IACA,OAAO,IAAI,CAAC3D,KAAK,GAAG,IAAI,CAACC,MAAM;EACnC;EACA;AACJ;AACA;EACIuE,UAAUA,CAAC3C,QAAQ,EAAEuD,yBAAyB,EAAE;IAC5C,MAAM7B,MAAM,GAAG,IAAI,CAACC,kBAAkB;IACtC,IAAIvE,SAAS,CAACC,YAAY,IAAID,SAAS,CAACC,YAAY,CAACmG,YAAY,EAAE;MAC/D;MACA,MAAMC,qBAAqB,GAAGvF,eAAe,CAAC6B,4BAA4B,CAACC,QAAQ,EAAEuD,yBAAyB,CAAC;MAC/GnG,SAAS,CAACC,YAAY,CAACmG,YAAY,CAAC;QAAEH,KAAK,EAAEI;MAAsB,CAAC,CAAC,CAChE/F,IAAI,CAAEgG,MAAM,IAAK;QAClB,IAAI,CAACtE,WAAW,GAAGsE,MAAM;QACzBhC,MAAM,CAACiC,SAAS,GAAGD,MAAM;QACzBhC,MAAM,CAACkC,IAAI,CAAC,CAAC;QACb,IAAI,CAACvE,mBAAmB,GAAGqE,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACvD,WAAW,CAAC,CAAC;QACnE,MAAMwD,cAAc,GAAG5F,eAAe,CAACkC,+BAA+B,CAACsD,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClG,IAAI,CAAC7E,cAAc,CAACsC,IAAI,CAACwC,cAAc,CAAC;QACxC;QACA;QACA,IAAI,CAACzC,sBAAsB,CAAC,CAAC,CACxB3D,IAAI,CAAC,MAAM;UACZ,IAAI,CAACyB,qBAAqB,GAAG2E,cAAc,GAAG,IAAI,CAAC7E,oBAAoB,CAClE8E,SAAS,CAAEC,eAAe,IAAKA,eAAe,CAAChE,QAAQ,KAAK8D,cAAc,CAAC,GAAG,CAAC,CAAC;UACrF,IAAI,CAAC5E,gBAAgB,GAAG,IAAI;QAChC,CAAC,CAAC,CACGnB,KAAK,CAAC,MAAM;UACb,IAAI,CAACoB,qBAAqB,GAAG,CAAC,CAAC;UAC/B,IAAI,CAACD,gBAAgB,GAAG,IAAI;QAChC,CAAC,CAAC;MACN,CAAC,CAAC,CACGnB,KAAK,CAAEC,GAAG,IAAK;QAChB,IAAI,CAACc,SAAS,CAACwC,IAAI,CAAC;UAAErD,OAAO,EAAED,GAAG,CAACC,OAAO;UAAEgG,gBAAgB,EAAEjG;QAAI,CAAC,CAAC;MACxE,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACc,SAAS,CAACwC,IAAI,CAAC;QAAErD,OAAO,EAAE;MAA2C,CAAC,CAAC;IAChF;EACJ;EACAiG,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9E,WAAW,GAAG,IAAI,CAACA,WAAW,CAACyE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACzE;EACAV,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACe,mBAAmB,CAAC,CAAC,EAAE;MAC7B,OAAO,KAAK;IAChB;IACA;IACA;MACI,IAAIC,MAAM,GAAG,MAAM;MACnB,IAAI,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,OAAO,IAAI,CAACA,WAAW,KAAK,QAAQ,EAAE;UACtCD,MAAM,GAAGnD,MAAM,CAAC,IAAI,CAACoD,WAAW,CAAC,CAACtD,WAAW,CAAC,CAAC;QACnD,CAAC,MACI;UACD;UACA,IAAI,IAAI,CAACsD,WAAW,CAACC,CAAC,EAAE;YACpBF,MAAM,GAAG,IAAI,CAACC,WAAW,CAACC,CAAC,CAACvD,WAAW,CAAC,CAAC;UAC7C;QACJ;MACJ;MACA,QAAQqD,MAAM;QACV,KAAK,QAAQ;UACT,OAAO,IAAI;QACf,KAAK,OAAO;UACR,OAAO,KAAK;MACpB;IACJ;IACA;IACA,OAAOjG,eAAe,CAAC2C,YAAY,CAAC,IAAI,CAACqD,mBAAmB,CAAC,CAAC,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI1C,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACpC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACkF,SAAS,EAAE;MAChD;MACA,IAAI,CAAC3C,kBAAkB,CAAC4C,KAAK,CAAC,CAAC;MAC/B;MACA,IAAI,CAACnF,WAAW,CAACkF,SAAS,CAAC,CAAC,CACvBE,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;IACzC;EACJ;EACA;AACJ;AACA;EACIjD,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAAClC,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACC,WAAW,CAAC,CAAC;IAC1C;IACA,IAAI,IAAI,CAACI,wBAAwB,EAAE;MAC/B,IAAI,CAACA,wBAAwB,CAACJ,WAAW,CAAC,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;EACI6B,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI9D,OAAO,CAAC,CAACE,OAAO,EAAED,MAAM,KAAK;MACpCN,UAAU,CAACC,uBAAuB,CAAC,CAAC,CAC/BO,IAAI,CAAEC,OAAO,IAAK;QACnB,IAAI,CAACsB,oBAAoB,GAAGtB,OAAO;QACnCF,OAAO,CAACE,OAAO,CAAC;MACpB,CAAC,CAAC,CACGI,KAAK,CAACC,GAAG,IAAI;QACd,IAAI,CAACiB,oBAAoB,GAAG,EAAE;QAC9BzB,MAAM,CAACQ,GAAG,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AACAE,eAAe,CAACI,qBAAqB,GAAG;EAAEqC,UAAU,EAAE;AAAc,CAAC;AACrEzC,eAAe,CAACQ,kBAAkB,GAAG,YAAY;AACjDR,eAAe,CAACU,qBAAqB,GAAG,IAAI;AAC5CV,eAAe,CAACyG,IAAI,YAAAC,wBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwF3G,eAAe;AAAA,CAAmD;AAC9KA,eAAe,CAAC4G,IAAI,kBAD8ElK,EAAE,CAAAmK,iBAAA;EAAAC,IAAA,EACJ9G,eAAe;EAAA+G,SAAA;EAAAC,SAAA,WAAAC,sBAAA3J,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADbZ,EAAE,CAAAwK,WAAA,CAAA/J,GAAA;MAAFT,EAAE,CAAAwK,WAAA,CAAA9J,GAAA;IAAA;IAAA,IAAAE,EAAA;MAAA,IAAA6J,EAAA;MAAFzK,EAAE,CAAA0K,cAAA,CAAAD,EAAA,GAAFzK,EAAE,CAAA2K,WAAA,QAAA9J,GAAA,CAAA4H,KAAA,GAAAgC,EAAA,CAAAG,KAAA;MAAF5K,EAAE,CAAA0K,cAAA,CAAAD,EAAA,GAAFzK,EAAE,CAAA2K,WAAA,QAAA9J,GAAA,CAAAuG,MAAA,GAAAqD,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAAtH,KAAA;IAAAC,MAAA;IAAAC,YAAA;IAAAE,iBAAA;IAAA6F,WAAA;IAAA5F,gBAAA;IAAAC,SAAA;IAAAE,YAAA;IAAAW,OAAA;IAAAK,YAAA;EAAA;EAAA+F,OAAA;IAAA7G,YAAA;IAAAC,SAAA;IAAAC,UAAA;IAAAC,cAAA;EAAA;EAAA2G,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAvK,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFZ,EAAE,CAAAgB,cAAA,YACkuB,CAAC;MADruBhB,EAAE,CAAAiB,UAAA,mBAAAmK,8CAAA;QAAA,OAC8sBvK,GAAA,CAAAsD,UAAA,CAAAuC,IAAA,CAAgB,CAAC;MAAA,CAAE,CAAC;MADpuB1G,EAAE,CAAAgB,cAAA,iBAC03B,CAAC;MAD73BhB,EAAE,CAAAiB,UAAA,oBAAAoK,iDAAA;QAAA,OAC22BxK,GAAA,CAAAmH,WAAA,CAAY,CAAC;MAAA,CAAC,CAAC;MAD53BhI,EAAE,CAAAwB,YAAA,CACk4B,CAAC;MADr4BxB,EAAE,CAAAsL,UAAA,IAAA3K,8BAAA,gBACiiC,CAAC;MADpiCX,EAAE,CAAAuL,SAAA,kBACsmC,CAAC;MADzmCvL,EAAE,CAAAwB,YAAA,CACgnC,CAAC;IAAA;IAAA,IAAAZ,EAAA;MADnnCZ,EAAE,CAAAwL,SAAA,EACm0B,CAAC;MADt0BxL,EAAE,CAAAyL,UAAA,CAAA5K,GAAA,CAAAwH,iBACm0B,CAAC;MADt0BrI,EAAE,CAAA0L,UAAA,UAAA7K,GAAA,CAAAoG,UAC4wB,CAAC,WAAApG,GAAA,CAAAqG,WAAD,CAAC;MAD/wBlH,EAAE,CAAAwL,SAAA,EACq/B,CAAC;MADx/BxL,EAAE,CAAA0L,UAAA,SAAA7K,GAAA,CAAA8C,iBAAA,IAAA9C,GAAA,CAAAwD,oBAAA,CAAAkC,MAAA,QAAA1F,GAAA,CAAAyD,gBACq/B,CAAC;MADx/BtE,EAAE,CAAAwL,SAAA,EACwkC,CAAC;MAD3kCxL,EAAE,CAAA0L,UAAA,UAAA7K,GAAA,CAAA0C,KACwkC,CAAC,WAAA1C,GAAA,CAAA2C,MAAD,CAAC;IAAA;EAAA;EAAAmI,YAAA,GAA80EpL,EAAE,CAACqL,IAAI;EAAAC,MAAA;AAAA,EAAoE;AACtkH;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFkG9L,EAAE,CAAA+L,iBAAA,CAETzI,eAAe,EAAc,CAAC;IAC7G8G,IAAI,EAAElK,SAAS;IACf8L,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,QAAQ;MAAEf,QAAQ,EAAE,idAAid;MAAEW,MAAM,EAAE,CAAC,8vEAA8vE;IAAE,CAAC;EACxwF,CAAC,CAAC,QAAkB;IAAEtI,KAAK,EAAE,CAAC;MACtB6G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEqD,MAAM,EAAE,CAAC;MACT4G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEsD,YAAY,EAAE,CAAC;MACf2G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEwD,iBAAiB,EAAE,CAAC;MACpByG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEqJ,WAAW,EAAE,CAAC;MACdY,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEyD,gBAAgB,EAAE,CAAC;MACnBwG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE0D,SAAS,EAAE,CAAC;MACZuG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE4D,YAAY,EAAE,CAAC;MACfqG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE8D,YAAY,EAAE,CAAC;MACfmG,IAAI,EAAEhK;IACV,CAAC,CAAC;IAAE8D,SAAS,EAAE,CAAC;MACZkG,IAAI,EAAEhK;IACV,CAAC,CAAC;IAAE+D,UAAU,EAAE,CAAC;MACbiG,IAAI,EAAEhK;IACV,CAAC,CAAC;IAAEgE,cAAc,EAAE,CAAC;MACjBgG,IAAI,EAAEhK;IACV,CAAC,CAAC;IAAEqI,KAAK,EAAE,CAAC;MACR2B,IAAI,EAAE/J,SAAS;MACf2L,IAAI,EAAE,CAAC,OAAO,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC,CAAC;IAAE9E,MAAM,EAAE,CAAC;MACTgD,IAAI,EAAE/J,SAAS;MACf2L,IAAI,EAAE,CAAC,QAAQ,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC;IACrC,CAAC,CAAC;IAAExH,OAAO,EAAE,CAAC;MACV0F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE4E,YAAY,EAAE,CAAC;MACfqF,IAAI,EAAEjK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgM,UAAU,GAAG,CACf7I,eAAe,CAClB;AACD,MAAM8I,YAAY,CAAC;AAEnBA,YAAY,CAACrC,IAAI,YAAAsC,qBAAApC,CAAA;EAAA,YAAAA,CAAA,IAAwFmC,YAAY;AAAA,CAAkD;AACvKA,YAAY,CAACE,IAAI,kBA/CiFtM,EAAE,CAAAuM,gBAAA;EAAAnC,IAAA,EA+CMgC;AAAY,EAAyF;AAC/MA,YAAY,CAACI,IAAI,kBAhDiFxM,EAAE,CAAAyM,gBAAA;EAAAC,OAAA,GAgD8B,CACtHlM,YAAY,CACf;AAAA,EAAI;AACb;EAAA,QAAAsL,SAAA,oBAAAA,SAAA,KAnDkG9L,EAAE,CAAA+L,iBAAA,CAmDTK,YAAY,EAAc,CAAC;IAC1GhC,IAAI,EAAE9J,QAAQ;IACd0L,IAAI,EAAE,CAAC;MACCU,OAAO,EAAE,CACLlM,YAAY,CACf;MACDmM,YAAY,EAAE,CACVR,UAAU,CACb;MACDS,OAAO,EAAE,CACLT,UAAU;IAElB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMU,eAAe,CAAC;EAClBnL,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACgG,gBAAgB,GAAG,IAAI;EAChC;AACJ;AAEA,MAAMyD,sBAAsB,CAAC;;AAG7B;AACA;AACA;;AAEA,SAASxJ,eAAe,EAAE7B,WAAW,EAAEoL,eAAe,EAAEC,sBAAsB,EAAEV,YAAY,EAAE9J,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "let toSJISFunction;\nconst CODEWORDS_COUNT = [0,\n// Not used\n26, 44, 70, 100, 134, 172, 196, 242, 292, 346, 404, 466, 532, 581, 655, 733, 815, 901, 991, 1085, 1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185, 2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706];\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize(version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined');\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40');\n  return version * 4 + 17;\n};\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords(version) {\n  return CODEWORDS_COUNT[version];\n};\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0;\n  while (data !== 0) {\n    digit++;\n    data >>>= 1;\n  }\n  return digit;\n};\nexports.setToSJISFunction = function setToSJISFunction(f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.');\n  }\n  toSJISFunction = f;\n};\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined';\n};\nexports.toSJIS = function toSJIS(kanji) {\n  return toSJISFunction(kanji);\n};", "map": {"version": 3, "names": ["toSJISFunction", "CODEWORDS_COUNT", "exports", "getSymbolSize", "version", "Error", "getSymbolTotalCodewords", "getBCHDigit", "data", "digit", "setToSJISFunction", "f", "isKanjiModeEnabled", "toSJIS", "kanji"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/core/utils.js"], "sourcesContent": ["let toSJISFunction\nconst CODEWORDS_COUNT = [\n  0, // Not used\n  26, 44, 70, 100, 134, 172, 196, 242, 292, 346,\n  404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n  1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185,\n  2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706\n]\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize (version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined')\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40')\n  return version * 4 + 17\n}\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords (version) {\n  return CODEWORDS_COUNT[version]\n}\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0\n\n  while (data !== 0) {\n    digit++\n    data >>>= 1\n  }\n\n  return digit\n}\n\nexports.setToSJISFunction = function setToSJISFunction (f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.')\n  }\n\n  toSJISFunction = f\n}\n\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined'\n}\n\nexports.toSJIS = function toSJIS (kanji) {\n  return toSJISFunction(kanji)\n}\n"], "mappings": "AAAA,IAAIA,cAAc;AAClB,MAAMC,eAAe,GAAG,CACtB,CAAC;AAAE;AACH,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC7C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EACjD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC1D,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAC3D;;AAED;AACA;AACA;AACA;AACA;AACA;AACAC,OAAO,CAACC,aAAa,GAAG,SAASA,aAAaA,CAAEC,OAAO,EAAE;EACvD,IAAI,CAACA,OAAO,EAAE,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EACtE,IAAID,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;EAC7F,OAAOD,OAAO,GAAG,CAAC,GAAG,EAAE;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACI,uBAAuB,GAAG,SAASA,uBAAuBA,CAAEF,OAAO,EAAE;EAC3E,OAAOH,eAAe,CAACG,OAAO,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACK,WAAW,GAAG,UAAUC,IAAI,EAAE;EACpC,IAAIC,KAAK,GAAG,CAAC;EAEb,OAAOD,IAAI,KAAK,CAAC,EAAE;IACjBC,KAAK,EAAE;IACPD,IAAI,MAAM,CAAC;EACb;EAEA,OAAOC,KAAK;AACd,CAAC;AAEDP,OAAO,CAACQ,iBAAiB,GAAG,SAASA,iBAAiBA,CAAEC,CAAC,EAAE;EACzD,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;IAC3B,MAAM,IAAIN,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAEAL,cAAc,GAAGW,CAAC;AACpB,CAAC;AAEDT,OAAO,CAACU,kBAAkB,GAAG,YAAY;EACvC,OAAO,OAAOZ,cAAc,KAAK,WAAW;AAC9C,CAAC;AAEDE,OAAO,CAACW,MAAM,GAAG,SAASA,MAAMA,CAAEC,KAAK,EAAE;EACvC,OAAOd,cAAc,CAACc,KAAK,CAAC;AAC9B,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
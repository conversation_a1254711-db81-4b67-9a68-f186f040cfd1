{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from 'environments/environment';\nimport { PushNotifications } from '@capacitor/push-notifications';\nimport { BehaviorSubject } from 'rxjs';\nimport { Capacitor } from '@capacitor/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"app/services/fcm.service\";\nexport class NotificationsService {\n  /**\r\n   *\r\n   * @param {HttpClient} _httpClient\r\n   */\n  constructor(router, _httpClient, _toastService, _fcmService) {\n    var _this = this;\n    this.router = router;\n    this._httpClient = _httpClient;\n    this._toastService = _toastService;\n    this._fcmService = _fcmService;\n    // Public\n    this.apiData = [];\n    this.isPushNotificationsAvailable = Capacitor.isPluginAvailable('PushNotifications');\n    this.onApiDataChange = new BehaviorSubject('');\n    if (this.currentUserValue) {\n      this.getNotificationsData();\n      if (this.isPushNotificationsAvailable) {\n        PushNotifications.addListener('pushNotificationReceived', /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (notification) {\n            _this.getNotificationsData();\n          });\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n        PushNotifications.addListener('pushNotificationActionPerformed', /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (notification) {\n            _this.getNotificationsData();\n            const data = notification.notification.data;\n            console.log('Push action performed: ' + JSON.stringify(notification.notification.data));\n            console.log('data: ', data);\n            if (data.hasOwnProperty('go_url')) {\n              console.log('go_url: ', data.go_url);\n              setTimeout(() => {\n                _this.router.navigate([data.go_url]);\n                // setTimeout(() => {\n                //   window.location.reload();\n                // }, 200);\n              }, 1000);\n            }\n          });\n          return function (_x2) {\n            return _ref2.apply(this, arguments);\n          };\n        }());\n      }\n    }\n  }\n  /**\r\n   * Get Notifications Data\r\n   */\n  getNotificationsData() {\n    let userId = this.currentUserValue.id;\n    return new Promise((resolve, reject) => {\n      this._httpClient.get(`${environment.apiUrl}/user-messages/${userId}`).subscribe(response => {\n        this.apiData = response;\n        this.onApiDataChange.next(this.apiData);\n        resolve(this.apiData);\n      }, reject);\n    });\n  }\n  markAsRead(message) {\n    if (message.read) {\n      return;\n    }\n    return new Promise((resolve, reject) => {\n      this._httpClient.post(`${environment.apiUrl}/user-messages/mark-as-read`, {\n        user_message_id: message.id\n      }).subscribe(response => {\n        this.getNotificationsData();\n        resolve(response);\n      }, reject);\n    });\n  }\n  maskAllAsRead() {\n    // apiUrl/user-messages/mark-all-as-read/{user_id}\n    let userId = this.currentUserValue.id;\n    return new Promise((resolve, reject) => {\n      this._httpClient.get(`${environment.apiUrl}/user-messages/mark-all-as-read/${userId}`).subscribe(response => {\n        this.getNotificationsData();\n        resolve(response);\n      }, reject);\n    });\n  }\n  listen() {\n    this._fcmService.currentMessage.subscribe(message => {\n      console.log('new message received. ', message);\n      this.getNotificationsData();\n      const data = message.data;\n      console.log('data: ', data);\n      // show Toast\n      this._toastService.success(data.title, data.body);\n    });\n  }\n  getAllNotifications() {\n    let userId = this.currentUserValue.id;\n    return this._httpClient.get(`${environment.apiUrl}/user-messages/${userId}`, {\n      params: {\n        take: 'all'\n      }\n    });\n  }\n  static #_ = this.ɵfac = function NotificationsService_Factory(t) {\n    return new (t || NotificationsService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.ToastrService), i0.ɵɵinject(i4.FcmService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NotificationsService,\n    factory: NotificationsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AAGA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAEEC,iBAAiB,QAEZ,+BAA+B;AACtC,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,SAAS,QAAQ,iBAAiB;;;;;;AAS3C,OAAM,MAAOC,oBAAoB;EAO/B;;;;EAIAC,YACSC,MAAc,EACbC,WAAuB,EACvBC,aAA4B,EAC5BC,WAAuB;IAAA,IAAAC,KAAA;IAHxB,KAAAJ,MAAM,GAANA,MAAM;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAdrB;IACO,KAAAE,OAAO,GAAG,EAAE;IAGnB,KAAAC,4BAA4B,GAC1BT,SAAS,CAACU,iBAAiB,CAAC,mBAAmB,CAAC;IAWhD,IAAI,CAACC,eAAe,GAAG,IAAIZ,eAAe,CAAC,EAAE,CAAC;IAC9C,IAAI,IAAI,CAACa,gBAAgB,EAAE;MACzB,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,IAAI,CAACJ,4BAA4B,EAAE;QACrCX,iBAAiB,CAACgB,WAAW,CAC3B,0BAA0B;UAAA,IAAAC,IAAA,GAAAC,iBAAA,CAC1B,WAAOC,YAAoC,EAAI;YAC7CV,KAAI,CAACM,oBAAoB,EAAE;UAC7B,CAAC;UAAA,iBAAAK,EAAA;YAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;UAAA;QAAA,IACF;QAEDtB,iBAAiB,CAACgB,WAAW,CAC3B,iCAAiC;UAAA,IAAAO,KAAA,GAAAL,iBAAA,CACjC,WAAOC,YAA6B,EAAI;YACtCV,KAAI,CAACM,oBAAoB,EAAE;YAC3B,MAAMS,IAAI,GAAGL,YAAY,CAACA,YAAY,CAACK,IAAI;YAC3CC,OAAO,CAACC,GAAG,CACT,yBAAyB,GACvBC,IAAI,CAACC,SAAS,CAACT,YAAY,CAACA,YAAY,CAACK,IAAI,CAAC,CACjD;YACDC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,IAAI,CAAC;YAC3B,IAAIA,IAAI,CAACK,cAAc,CAAC,QAAQ,CAAC,EAAE;cACjCJ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI,CAACM,MAAM,CAAC;cACpCC,UAAU,CAAC,MAAK;gBACdtB,KAAI,CAACJ,MAAM,CAAC2B,QAAQ,CAAC,CAACR,IAAI,CAACM,MAAM,CAAC,CAAC;gBACnC;gBACA;gBACA;cACF,CAAC,EAAE,IAAI,CAAC;;UAEZ,CAAC;UAAA,iBAAAG,GAAA;YAAA,OAAAV,KAAA,CAAAF,KAAA,OAAAC,SAAA;UAAA;QAAA,IACF;;;EAGP;EAEA;;;EAGAP,oBAAoBA,CAAA;IAClB,IAAImB,MAAM,GAAG,IAAI,CAACpB,gBAAgB,CAACqB,EAAE;IACrC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAAChC,WAAW,CACbiC,GAAG,CAAC,GAAGxC,WAAW,CAACyC,MAAM,kBAAkBN,MAAM,EAAE,CAAC,CACpDO,SAAS,CAAEC,QAAa,IAAI;QAC3B,IAAI,CAAChC,OAAO,GAAGgC,QAAQ;QACvB,IAAI,CAAC7B,eAAe,CAAC8B,IAAI,CAAC,IAAI,CAACjC,OAAO,CAAC;QACvC2B,OAAO,CAAC,IAAI,CAAC3B,OAAO,CAAC;MACvB,CAAC,EAAE4B,MAAM,CAAC;IACd,CAAC,CAAC;EACJ;EAEAM,UAAUA,CAACC,OAAO;IAChB,IAAIA,OAAO,CAACC,IAAI,EAAE;MAChB;;IAEF,OAAO,IAAIV,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAAChC,WAAW,CACbyC,IAAI,CAAC,GAAGhD,WAAW,CAACyC,MAAM,6BAA6B,EAAE;QACxDQ,eAAe,EAAEH,OAAO,CAACV;OAC1B,CAAC,CACDM,SAAS,CAAEC,QAAa,IAAI;QAC3B,IAAI,CAAC3B,oBAAoB,EAAE;QAC3BsB,OAAO,CAACK,QAAQ,CAAC;MACnB,CAAC,EAAEJ,MAAM,CAAC;IACd,CAAC,CAAC;EACJ;EAEAW,aAAaA,CAAA;IACX;IACA,IAAIf,MAAM,GAAG,IAAI,CAACpB,gBAAgB,CAACqB,EAAE;IACrC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAAChC,WAAW,CACbiC,GAAG,CAAC,GAAGxC,WAAW,CAACyC,MAAM,mCAAmCN,MAAM,EAAE,CAAC,CACrEO,SAAS,CAAEC,QAAa,IAAI;QAC3B,IAAI,CAAC3B,oBAAoB,EAAE;QAC3BsB,OAAO,CAACK,QAAQ,CAAC;MACnB,CAAC,EAAEJ,MAAM,CAAC;IACd,CAAC,CAAC;EACJ;EAEAY,MAAMA,CAAA;IACJ,IAAI,CAAC1C,WAAW,CAAC2C,cAAc,CAACV,SAAS,CAAEI,OAAO,IAAI;MACpDpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,OAAO,CAAC;MAC9C,IAAI,CAAC9B,oBAAoB,EAAE;MAC3B,MAAMS,IAAI,GAAGqB,OAAO,CAACrB,IAAI;MACzBC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,IAAI,CAAC;MAE3B;MACA,IAAI,CAACjB,aAAa,CAAC6C,OAAO,CAAC5B,IAAI,CAAC6B,KAAK,EAAE7B,IAAI,CAAC8B,IAAI,CAAC;IACnD,CAAC,CAAC;EACJ;EAEAC,mBAAmBA,CAAA;IACjB,IAAIrB,MAAM,GAAG,IAAI,CAACpB,gBAAgB,CAACqB,EAAE;IACrC,OAAO,IAAI,CAAC7B,WAAW,CAACiC,GAAG,CACzB,GAAGxC,WAAW,CAACyC,MAAM,kBAAkBN,MAAM,EAAE,EAC/C;MACEsB,MAAM,EAAE;QACNC,IAAI,EAAE;;KAET,CACF;EACH;EAAC,QAAAC,CAAA;qBAxHUvD,oBAAoB,EAAAwD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAApBlE,oBAAoB;IAAAmE,OAAA,EAApBnE,oBAAoB,CAAAoE,IAAA;IAAAC,UAAA,EAFnB;EAAM", "names": ["environment", "PushNotifications", "BehaviorSubject", "Capacitor", "NotificationsService", "constructor", "router", "_httpClient", "_toastService", "_fcmService", "_this", "apiData", "isPushNotificationsAvailable", "isPluginAvailable", "onApiDataChange", "currentUserValue", "getNotificationsData", "addListener", "_ref", "_asyncToGenerator", "notification", "_x", "apply", "arguments", "_ref2", "data", "console", "log", "JSON", "stringify", "hasOwnProperty", "go_url", "setTimeout", "navigate", "_x2", "userId", "id", "Promise", "resolve", "reject", "get", "apiUrl", "subscribe", "response", "next", "mark<PERSON><PERSON><PERSON>", "message", "read", "post", "user_message_id", "maskAllAsRead", "listen", "currentMessage", "success", "title", "body", "getAllNotifications", "params", "take", "_", "i0", "ɵɵinject", "i1", "Router", "i2", "HttpClient", "i3", "ToastrService", "i4", "FcmService", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar-notification\\notifications.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { environment } from 'environments/environment';\r\nimport {\r\n  ActionPerformed,\r\n  PushNotifications,\r\n  PushNotificationSchema,\r\n} from '@capacitor/push-notifications';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { AngularFireMessaging } from '@angular/fire/compat/messaging';\r\nimport { Router } from '@angular/router';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { FcmService } from 'app/services/fcm.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class NotificationsService {\r\n  // Public\r\n  public apiData = [];\r\n  public onApiDataChange: BehaviorSubject<any>;\r\n  currentUserValue: any;\r\n  isPushNotificationsAvailable =\r\n    Capacitor.isPluginAvailable('PushNotifications');\r\n  /**\r\n   *\r\n   * @param {HttpClient} _httpClient\r\n   */\r\n  constructor(\r\n    public router: Router,\r\n    private _httpClient: HttpClient,\r\n    private _toastService: ToastrService,\r\n    private _fcmService: FcmService\r\n  ) {\r\n    this.onApiDataChange = new BehaviorSubject('');\r\n    if (this.currentUserValue) {\r\n      this.getNotificationsData();\r\n      if (this.isPushNotificationsAvailable) {\r\n        PushNotifications.addListener(\r\n          'pushNotificationReceived',\r\n          async (notification: PushNotificationSchema) => {\r\n            this.getNotificationsData();\r\n          }\r\n        );\r\n\r\n        PushNotifications.addListener(\r\n          'pushNotificationActionPerformed',\r\n          async (notification: ActionPerformed) => {\r\n            this.getNotificationsData();\r\n            const data = notification.notification.data;\r\n            console.log(\r\n              'Push action performed: ' +\r\n                JSON.stringify(notification.notification.data)\r\n            );\r\n            console.log('data: ', data);\r\n            if (data.hasOwnProperty('go_url')) {\r\n              console.log('go_url: ', data.go_url);\r\n              setTimeout(() => {\r\n                this.router.navigate([data.go_url]);\r\n                // setTimeout(() => {\r\n                //   window.location.reload();\r\n                // }, 200);\r\n              }, 1000);\r\n            }\r\n          }\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get Notifications Data\r\n   */\r\n  getNotificationsData(): Promise<any[]> {\r\n    let userId = this.currentUserValue.id;\r\n    return new Promise((resolve, reject) => {\r\n      this._httpClient\r\n        .get(`${environment.apiUrl}/user-messages/${userId}`)\r\n        .subscribe((response: any) => {\r\n          this.apiData = response;\r\n          this.onApiDataChange.next(this.apiData);\r\n          resolve(this.apiData);\r\n        }, reject);\r\n    });\r\n  }\r\n\r\n  markAsRead(message) {\r\n    if (message.read) {\r\n      return;\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      this._httpClient\r\n        .post(`${environment.apiUrl}/user-messages/mark-as-read`, {\r\n          user_message_id: message.id,\r\n        })\r\n        .subscribe((response: any) => {\r\n          this.getNotificationsData();\r\n          resolve(response);\r\n        }, reject);\r\n    });\r\n  }\r\n\r\n  maskAllAsRead() {\r\n    // apiUrl/user-messages/mark-all-as-read/{user_id}\r\n    let userId = this.currentUserValue.id;\r\n    return new Promise((resolve, reject) => {\r\n      this._httpClient\r\n        .get(`${environment.apiUrl}/user-messages/mark-all-as-read/${userId}`)\r\n        .subscribe((response: any) => {\r\n          this.getNotificationsData();\r\n          resolve(response);\r\n        }, reject);\r\n    });\r\n  }\r\n\r\n  listen() {\r\n    this._fcmService.currentMessage.subscribe((message) => {\r\n      console.log('new message received. ', message);\r\n      this.getNotificationsData();\r\n      const data = message.data;\r\n      console.log('data: ', data);\r\n\r\n      // show Toast\r\n      this._toastService.success(data.title, data.body);\r\n    });\r\n  }\r\n\r\n  getAllNotifications() {\r\n    let userId = this.currentUserValue.id;\r\n    return this._httpClient.get(\r\n      `${environment.apiUrl}/user-messages/${userId}`,\r\n      {\r\n        params: {\r\n          take: 'all',\r\n        },\r\n      }\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
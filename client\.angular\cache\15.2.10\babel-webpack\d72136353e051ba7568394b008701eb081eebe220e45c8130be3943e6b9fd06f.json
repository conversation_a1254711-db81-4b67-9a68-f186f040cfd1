{"ast": null, "code": "import { takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\n// import conten\nimport Swal from 'sweetalert2';\nimport { ModalSeasonInfoComponent } from './modal-season-info/modal-season-info.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./../../../services/registration.service\";\nimport * as i3 from \"./../../../services/loading.service\";\nimport * as i4 from \"./../../../services/club.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"app/layout/components/content-header/content-header.component\";\nimport * as i9 from \"@ngx-translate/core\";\nimport * as i10 from \"../../../../@core/pipes/localized-date.pipe\";\nfunction SelectEventComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"div\", 9)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"i\", 11);\n    i0.ɵɵlistener(\"click\", function SelectEventComponent_div_5_Template_i_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const event_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openModalInfo(event_r1));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(9, \"hr\", 12);\n    i0.ɵɵelementStart(10, \"table\", 13)(11, \"tr\")(12, \"td\", 14);\n    i0.ɵɵelement(13, \"i\", 15);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 14);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"localizedDate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"tr\")(20, \"td\", 14);\n    i0.ɵɵelement(21, \"i\", 15);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 14);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"localizedDate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function SelectEventComponent_div_5_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const event_r1 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openTeamListModal(event_r1));\n    });\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const event_r1 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(6, 7, \"Season\"), \" \", event_r1.name, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 9, \"Registration Start\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 11, event_r1.start_register_date, \"dd-MMM-yyyy\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 14, \"Registration End\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 16, event_r1.end_date, \"dd-MMM-yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 19, \"Select\"), \"\");\n  }\n}\nexport class SelectEventComponent {\n  constructor(_router, _registrationService, _loadingService, _clubService, _modalService, _titleService) {\n    this._router = _router;\n    this._registrationService = _registrationService;\n    this._loadingService = _loadingService;\n    this._clubService = _clubService;\n    this._modalService = _modalService;\n    this._titleService = _titleService;\n    this._clubs = [];\n    this._titleService.setTitle('Select Season');\n  }\n  ngOnInit() {\n    // content header\n    this.contentHeader = {\n      headerTitle: 'Application',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Application',\n          isLink: false\n        }]\n      }\n    };\n    // define default values for unsubscribe all\n    this._unsubscribeAll = new Subject();\n    // get current seasons\n    this._getCurrentSeason();\n  }\n  openTeamListModal(season) {\n    this._registrationService.selectedSeason = season;\n    if (!this.dontShowMessage) {\n      this.openModalInfo(season);\n    }\n    // navigate to select-player\n    setTimeout(() => {\n      this._router.navigate(['registration/season/', season.id, 'select-player']);\n    }, 500);\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getCurrentSeason().pipe(takeUntil(this._unsubscribeAll)).subscribe(data => {\n      this.currentSeasons = data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    });\n  }\n  openModalInfo(season) {\n    const modalRef = this._modalService.open(ModalSeasonInfoComponent, {\n      size: 'md',\n      centered: true,\n      backdrop: 'static'\n    });\n    modalRef.componentInstance.season = season;\n  }\n  get dontShowMessage() {\n    let key = this._registrationService.selectedSeason.id.toString();\n    let data = JSON.parse(localStorage.getItem('dontShowMessage'));\n    if (data) {\n      return data.seasons[key]?.info;\n    }\n    return false;\n  }\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  static #_ = this.ɵfac = function SelectEventComponent_Factory(t) {\n    return new (t || SelectEventComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.RegistrationService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.ClubService), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SelectEventComponent,\n    selectors: [[\"app-select-event\"]],\n    decls: 6,\n    vars: 2,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"select-event-page\"], [1, \"row\"], [\"class\", \"col-lg-4 col-md-6 col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-4\", \"col-md-6\", \"col-12\"], [1, \"card\"], [1, \"card-body\"], [1, \"d-flex\"], [1, \"ml-auto\"], [1, \"text-muted\", \"fa-light\", \"fa-circle-info\", \"fa-xl\", 3, \"click\"], [1, \"mt-50\", \"mb-50\"], [1, \"w-100\", \"mb-1\"], [1, \"text-dark\"], [1, \"fa-regular\", \"fa-clock\", \"mr-50\"], [1, \"btn\", \"btn-block\", \"btn-primary\", 3, \"click\"]],\n    template: function SelectEventComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4);\n        i0.ɵɵtemplate(5, SelectEventComponent_div_5_Template, 30, 21, \"div\", 5);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.currentSeasons);\n      }\n    },\n    dependencies: [i7.NgForOf, i8.ContentHeaderComponent, i9.TranslatePipe, i10.LocalizedDatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,OAAO,QAAQ,MAAM;AAI9B;AACA,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,wBAAwB,QAAQ,iDAAiD;;;;;;;;;;;;;;;ICFlFC,EAAA,CAAAC,cAAA,aAA2E;IAI/DD,EAAA,CAAAE,MAAA,GAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,cAAqB;IACiCD,EAAA,CAAAI,UAAA,mBAAAC,uDAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAL,QAAA,CAAoB;IAAA,EAAC;IAACT,EAAA,CAAAG,YAAA,EAAI;IAG3FH,EAAA,CAAAe,SAAA,aAAwB;IACxBf,EAAA,CAAAC,cAAA,iBAA0B;IAGpBD,EAAA,CAAAe,SAAA,aAAyC;IACzCf,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAE,MAAA,IAA8D;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE3FH,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAe,SAAA,aAAyC;IACzCf,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAmD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIlFH,EAAA,CAAAC,cAAA,kBAA6E;IAArED,EAAA,CAAAI,UAAA,mBAAAY,6DAAA;MAAA,MAAAV,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAO,MAAA,GAAAjB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAI,MAAA,CAAAC,iBAAA,CAAAT,QAAA,CAAwB;IAAA,EAAC;IACxCT,EAAA,CAAAE,MAAA,IAAuB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxB5BH,EAAA,CAAAmB,SAAA,GAAuC;IAAvCnB,EAAA,CAAAoB,kBAAA,KAAApB,EAAA,CAAAqB,WAAA,uBAAAZ,QAAA,CAAAa,IAAA,KAAuC;IAUvCtB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAqB,WAAA,oCACF;IACsBrB,EAAA,CAAAmB,SAAA,GAA8D;IAA9DnB,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAAyB,WAAA,SAAAhB,QAAA,CAAAiB,mBAAA,iBAA8D;IAKlF1B,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAqB,WAAA,mCACF;IACsBrB,EAAA,CAAAmB,SAAA,GAAmD;IAAnDnB,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAAyB,WAAA,SAAAhB,QAAA,CAAAkB,QAAA,iBAAmD;IAK3E3B,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAqB,WAAA,uBAAuB;;;ADjBvC,OAAM,MAAOO,oBAAoB;EAS/BC,YACUC,OAAe,EACfC,oBAAyC,EACzCC,eAA+B,EAC/BC,YAAyB,EAC1BC,aAAuB,EACvBC,aAAoB;IALnB,KAAAL,OAAO,GAAPA,OAAO;IACP,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IARd,KAAAC,MAAM,GAAG,EAAE;IAUjB,IAAI,CAACD,aAAa,CAACE,QAAQ,CAAC,eAAe,CAAC;EAC9C;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,aAAa;MAC1BC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEtB,IAAI,EAAE,aAAa;UACnBuB,MAAM,EAAE;SACT;;KAGN;IAED;IACA,IAAI,CAACC,eAAe,GAAG,IAAIjD,OAAO,EAAE;IAEpC;IACA,IAAI,CAACkD,iBAAiB,EAAE;EAC1B;EAEA7B,iBAAiBA,CAAC8B,MAAM;IACtB,IAAI,CAACjB,oBAAoB,CAACkB,cAAc,GAAGD,MAAM;IACjD,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE;MACzB,IAAI,CAACpC,aAAa,CAACkC,MAAM,CAAC;;IAE5B;IACAG,UAAU,CAAC,MAAK;MACd,IAAI,CAACrB,OAAO,CAACsB,QAAQ,CAAC,CACpB,sBAAsB,EACtBJ,MAAM,CAACK,EAAE,EACT,eAAe,CAChB,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAN,iBAAiBA,CAAA;IACf,IAAI,CAACf,eAAe,CAACsB,IAAI,EAAE;IAC3B,IAAI,CAACvB,oBAAoB,CACtBwB,gBAAgB,EAAE,CAClBC,IAAI,CAAC5D,SAAS,CAAC,IAAI,CAACkD,eAAe,CAAC,CAAC,CACrCW,SAAS,CACPC,IAAI,IAAI;MACP,IAAI,CAACC,cAAc,GAAGD,IAAI;IAC5B,CAAC,EACAE,KAAK,IAAI;MACR9D,IAAI,CAAC+D,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,CACF;EACL;EAGApD,aAAaA,CAACkC,MAAM;IAClB,MAAMmB,QAAQ,GAAG,IAAI,CAACjC,aAAa,CAACkC,IAAI,CAACrE,wBAAwB,EAAE;MACjEsE,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,CAAC;IACFJ,QAAQ,CAACK,iBAAiB,CAACxB,MAAM,GAAGA,MAAM;EAC5C;EAEA,IAAIE,eAAeA,CAAA;IACjB,IAAIuB,GAAG,GAAG,IAAI,CAAC1C,oBAAoB,CAACkB,cAAc,CAACI,EAAE,CAACqB,QAAQ,EAAE;IAChE,IAAIhB,IAAI,GAAGiB,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAC9D,IAAIpB,IAAI,EAAE;MACR,OAAOA,IAAI,CAACqB,OAAO,CAACN,GAAG,CAAC,EAAEO,IAAI;;IAEhC,OAAO,KAAK;EACd;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACnC,eAAe,CAACoC,IAAI,EAAE;IAC3B,IAAI,CAACpC,eAAe,CAACqC,QAAQ,EAAE;EACjC;EAAC,QAAAC,CAAA;qBArGUxD,oBAAoB,EAAA5B,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAzF,EAAA,CAAAqF,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3F,EAAA,CAAAqF,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7F,EAAA,CAAAqF,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAA/F,EAAA,CAAAqF,iBAAA,CAAAW,EAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAApBtE,oBAAoB;IAAAuE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnBjCzG,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAe,SAAA,4BAAyE;QAGzEf,EAAA,CAAAC,cAAA,iBAAgC;QAE5BD,EAAA,CAAA2G,UAAA,IAAAC,mCAAA,mBA+BM;QACR5G,EAAA,CAAAG,YAAA,EAAM;;;QArCYH,EAAA,CAAAmB,SAAA,GAA+B;QAA/BnB,EAAA,CAAA6G,UAAA,kBAAAH,GAAA,CAAAnE,aAAA,CAA+B;QAKSvC,EAAA,CAAAmB,SAAA,GAAiB;QAAjBnB,EAAA,CAAA6G,UAAA,YAAAH,GAAA,CAAA/C,cAAA,CAAiB", "names": ["takeUntil", "Subject", "<PERSON><PERSON>", "ModalSeasonInfoComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SelectEventComponent_div_5_Template_i_click_8_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "event_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openModalInfo", "ɵɵelement", "SelectEventComponent_div_5_Template_button_click_27_listener", "ctx_r4", "openTeamListModal", "ɵɵadvance", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "name", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind2", "start_register_date", "end_date", "SelectEventComponent", "constructor", "_router", "_registrationService", "_loadingService", "_clubService", "_modalService", "_titleService", "_clubs", "setTitle", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "isLink", "_unsubscribeAll", "_getCurrentSeason", "season", "selectedS<PERSON>on", "dontShowMessage", "setTimeout", "navigate", "id", "show", "getCurrentSeason", "pipe", "subscribe", "data", "currentSeasons", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "modalRef", "open", "size", "centered", "backdrop", "componentInstance", "key", "toString", "JSON", "parse", "localStorage", "getItem", "seasons", "info", "ngOnDestroy", "next", "complete", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "RegistrationService", "i3", "LoadingService", "i4", "ClubService", "i5", "NgbModal", "i6", "Title", "_2", "selectors", "decls", "vars", "consts", "template", "SelectEventComponent_Template", "rf", "ctx", "ɵɵtemplate", "SelectEventComponent_div_5_Template", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\registration\\select-event\\select-event.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\registration\\select-event\\select-event.component.html"], "sourcesContent": ["import { ClubService } from './../../../services/club.service';\r\nimport { LoadingService } from './../../../services/loading.service';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { Subject } from 'rxjs';\r\nimport { RegistrationService } from './../../../services/registration.service';\r\nimport { Router } from '@angular/router';\r\nimport { Component, ViewEncapsulation } from '@angular/core';\r\n// import conten\r\nimport Swal from 'sweetalert2';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ModalSeasonInfoComponent } from './modal-season-info/modal-season-info.component';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-select-event',\r\n  templateUrl: './select-event.component.html',\r\n  styleUrls: ['./select-event.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class SelectEventComponent {\r\n  // public variables\r\n  public contentHeader: object;\r\n  public currentSeasons;\r\n\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n  private _clubs = [];\r\n\r\n  constructor(\r\n    private _router: Router,\r\n    private _registrationService: RegistrationService,\r\n    private _loadingService: LoadingService,\r\n    private _clubService: ClubService,\r\n    public _modalService: NgbModal,\r\n    public _titleService: Title\r\n  ) {\r\n    this._titleService.setTitle('Select Season');\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // content header\r\n    this.contentHeader = {\r\n      headerTitle: 'Application',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Application',\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    // define default values for unsubscribe all\r\n    this._unsubscribeAll = new Subject();\r\n\r\n    // get current seasons\r\n    this._getCurrentSeason();\r\n  }\r\n\r\n  openTeamListModal(season) {\r\n    this._registrationService.selectedSeason = season;\r\n    if (!this.dontShowMessage) {\r\n      this.openModalInfo(season);\r\n    }\r\n    // navigate to select-player\r\n    setTimeout(() => {\r\n      this._router.navigate([\r\n        'registration/season/',\r\n        season.id,\r\n        'select-player'\r\n      ]);\r\n    }, 500);\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService\r\n      .getCurrentSeason()\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe(\r\n        (data) => {\r\n          this.currentSeasons = data;\r\n        },\r\n        (error) => {\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: error.message,\r\n            icon: 'error',\r\n            confirmButtonText: 'OK'\r\n          });\r\n        }\r\n      );\r\n  }\r\n\r\n\r\n  openModalInfo(season) {\r\n    const modalRef = this._modalService.open(ModalSeasonInfoComponent, {\r\n      size: 'md',\r\n      centered: true,\r\n      backdrop: 'static'\r\n    });\r\n    modalRef.componentInstance.season = season;\r\n  }\r\n\r\n  get dontShowMessage(): boolean {\r\n    let key = this._registrationService.selectedSeason.id.toString();\r\n    let data = JSON.parse(localStorage.getItem('dontShowMessage'));\r\n    if (data) {\r\n      return data.seasons[key]?.info;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <!-- Season events list start -->\r\n    <section id=\"select-event-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-lg-4 col-md-6 col-12\" *ngFor=\"let event of currentSeasons\">\r\n          <div class=\"card\">\r\n            <div class=\"card-body\">\r\n              <div class=\"d-flex\">\r\n                <h4>{{'Season' | translate}} {{event.name}}</h4>\r\n                <div class=\"ml-auto\">\r\n                  <i class=\"text-muted fa-light fa-circle-info fa-xl\" (click)=\"openModalInfo(event)\"></i>\r\n                </div>\r\n              </div>\r\n              <hr class=\"mt-50 mb-50\">\r\n              <table class=\"w-100 mb-1\">\r\n                <tr>\r\n                  <td class=\"text-dark\">\r\n                    <i class=\"fa-regular fa-clock mr-50\"></i>\r\n                    {{ 'Registration Start' | translate }}:\r\n                  </td>\r\n                  <td class=\"text-dark\">{{ event.start_register_date | localizedDate: 'dd-MMM-yyyy' }}</td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"text-dark\">\r\n                    <i class=\"fa-regular fa-clock mr-50\"></i>\r\n                    {{ 'Registration End' | translate }}:\r\n                  </td>\r\n                  <td class=\"text-dark\">{{ event.end_date | localizedDate: 'dd-MMM-yyyy' }}</td>\r\n                </tr>\r\n              </table>\r\n\r\n              <button (click)=\"openTeamListModal(event)\" class=\"btn btn-block btn-primary\">\r\n                {{'Select'| translate}}</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n    <!-- Season events list end -->\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function takeWhile(predicate, inclusive = false) {\n  return source => source.lift(new TakeWhileOperator(predicate, inclusive));\n}\nclass TakeWhileOperator {\n  constructor(predicate, inclusive) {\n    this.predicate = predicate;\n    this.inclusive = inclusive;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new TakeWhileSubscriber(subscriber, this.predicate, this.inclusive));\n  }\n}\nclass TakeWhileSubscriber extends Subscriber {\n  constructor(destination, predicate, inclusive) {\n    super(destination);\n    this.predicate = predicate;\n    this.inclusive = inclusive;\n    this.index = 0;\n  }\n  _next(value) {\n    const destination = this.destination;\n    let result;\n    try {\n      result = this.predicate(value, this.index++);\n    } catch (err) {\n      destination.error(err);\n      return;\n    }\n    this.nextOrComplete(value, result);\n  }\n  nextOrComplete(value, predicateResult) {\n    const destination = this.destination;\n    if (Boolean(predicateResult)) {\n      destination.next(value);\n    } else {\n      if (this.inclusive) {\n        destination.next(value);\n      }\n      destination.complete();\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "inclusive", "source", "lift", "TakeWhileOperator", "constructor", "call", "subscriber", "subscribe", "TakeWhileSubscriber", "destination", "index", "_next", "value", "result", "err", "error", "nextOrComplete", "predicateResult", "Boolean", "next", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/takeWhile.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function takeWhile(predicate, inclusive = false) {\n    return (source) => source.lift(new TakeWhileOperator(predicate, inclusive));\n}\nclass TakeWhileOperator {\n    constructor(predicate, inclusive) {\n        this.predicate = predicate;\n        this.inclusive = inclusive;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new TakeWhileSubscriber(subscriber, this.predicate, this.inclusive));\n    }\n}\nclass TakeWhileSubscriber extends Subscriber {\n    constructor(destination, predicate, inclusive) {\n        super(destination);\n        this.predicate = predicate;\n        this.inclusive = inclusive;\n        this.index = 0;\n    }\n    _next(value) {\n        const destination = this.destination;\n        let result;\n        try {\n            result = this.predicate(value, this.index++);\n        }\n        catch (err) {\n            destination.error(err);\n            return;\n        }\n        this.nextOrComplete(value, result);\n    }\n    nextOrComplete(value, predicateResult) {\n        const destination = this.destination;\n        if (Boolean(predicateResult)) {\n            destination.next(value);\n        }\n        else {\n            if (this.inclusive) {\n                destination.next(value);\n            }\n            destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,SAAS,GAAG,KAAK,EAAE;EACpD,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACJ,SAAS,EAAEC,SAAS,CAAC,CAAC;AAC/E;AACA,MAAMG,iBAAiB,CAAC;EACpBC,WAAWA,CAACL,SAAS,EAAEC,SAAS,EAAE;IAC9B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACP,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;EAChG;AACJ;AACA,MAAMQ,mBAAmB,SAASX,UAAU,CAAC;EACzCO,WAAWA,CAACK,WAAW,EAAEV,SAAS,EAAEC,SAAS,EAAE;IAC3C,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACV,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACU,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,MAAMH,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAII,MAAM;IACV,IAAI;MACAA,MAAM,GAAG,IAAI,CAACd,SAAS,CAACa,KAAK,EAAE,IAAI,CAACF,KAAK,EAAE,CAAC;IAChD,CAAC,CACD,OAAOI,GAAG,EAAE;MACRL,WAAW,CAACM,KAAK,CAACD,GAAG,CAAC;MACtB;IACJ;IACA,IAAI,CAACE,cAAc,CAACJ,KAAK,EAAEC,MAAM,CAAC;EACtC;EACAG,cAAcA,CAACJ,KAAK,EAAEK,eAAe,EAAE;IACnC,MAAMR,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAIS,OAAO,CAACD,eAAe,CAAC,EAAE;MAC1BR,WAAW,CAACU,IAAI,CAACP,KAAK,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,IAAI,CAACZ,SAAS,EAAE;QAChBS,WAAW,CAACU,IAAI,CAACP,KAAK,CAAC;MAC3B;MACAH,WAAW,CAACW,QAAQ,CAAC,CAAC;IAC1B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function observeOn(scheduler, delay = 0) {\n  return function observeOnOperatorFunction(source) {\n    return source.lift(new ObserveOnOperator(scheduler, delay));\n  };\n}\nexport class ObserveOnOperator {\n  constructor(scheduler, delay = 0) {\n    this.scheduler = scheduler;\n    this.delay = delay;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new ObserveOnSubscriber(subscriber, this.scheduler, this.delay));\n  }\n}\nexport class ObserveOnSubscriber extends Subscriber {\n  constructor(destination, scheduler, delay = 0) {\n    super(destination);\n    this.scheduler = scheduler;\n    this.delay = delay;\n  }\n  static dispatch(arg) {\n    const {\n      notification,\n      destination\n    } = arg;\n    notification.observe(destination);\n    this.unsubscribe();\n  }\n  scheduleMessage(notification) {\n    const destination = this.destination;\n    destination.add(this.scheduler.schedule(ObserveOnSubscriber.dispatch, this.delay, new ObserveOnMessage(notification, this.destination)));\n  }\n  _next(value) {\n    this.scheduleMessage(Notification.createNext(value));\n  }\n  _error(err) {\n    this.scheduleMessage(Notification.createError(err));\n    this.unsubscribe();\n  }\n  _complete() {\n    this.scheduleMessage(Notification.createComplete());\n    this.unsubscribe();\n  }\n}\nexport class ObserveOnMessage {\n  constructor(notification, destination) {\n    this.notification = notification;\n    this.destination = destination;\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "Notification", "observeOn", "scheduler", "delay", "observeOnOperatorFunction", "source", "lift", "ObserveOnOperator", "constructor", "call", "subscriber", "subscribe", "ObserveOnSubscriber", "destination", "dispatch", "arg", "notification", "observe", "unsubscribe", "scheduleMessage", "add", "schedule", "ObserveOnMessage", "_next", "value", "createNext", "_error", "err", "createError", "_complete", "createComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/observeOn.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function observeOn(scheduler, delay = 0) {\n    return function observeOnOperatorFunction(source) {\n        return source.lift(new ObserveOnOperator(scheduler, delay));\n    };\n}\nexport class ObserveOnOperator {\n    constructor(scheduler, delay = 0) {\n        this.scheduler = scheduler;\n        this.delay = delay;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ObserveOnSubscriber(subscriber, this.scheduler, this.delay));\n    }\n}\nexport class ObserveOnSubscriber extends Subscriber {\n    constructor(destination, scheduler, delay = 0) {\n        super(destination);\n        this.scheduler = scheduler;\n        this.delay = delay;\n    }\n    static dispatch(arg) {\n        const { notification, destination } = arg;\n        notification.observe(destination);\n        this.unsubscribe();\n    }\n    scheduleMessage(notification) {\n        const destination = this.destination;\n        destination.add(this.scheduler.schedule(ObserveOnSubscriber.dispatch, this.delay, new ObserveOnMessage(notification, this.destination)));\n    }\n    _next(value) {\n        this.scheduleMessage(Notification.createNext(value));\n    }\n    _error(err) {\n        this.scheduleMessage(Notification.createError(err));\n        this.unsubscribe();\n    }\n    _complete() {\n        this.scheduleMessage(Notification.createComplete());\n        this.unsubscribe();\n    }\n}\nexport class ObserveOnMessage {\n    constructor(notification, destination) {\n        this.notification = notification;\n        this.destination = destination;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC5C,OAAO,SAASC,yBAAyBA,CAACC,MAAM,EAAE;IAC9C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACL,SAAS,EAAEC,KAAK,CAAC,CAAC;EAC/D,CAAC;AACL;AACA,OAAO,MAAMI,iBAAiB,CAAC;EAC3BC,WAAWA,CAACN,SAAS,EAAEC,KAAK,GAAG,CAAC,EAAE;IAC9B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACAM,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACR,SAAS,EAAE,IAAI,CAACC,KAAK,CAAC,CAAC;EAC5F;AACJ;AACA,OAAO,MAAMS,mBAAmB,SAASb,UAAU,CAAC;EAChDS,WAAWA,CAACK,WAAW,EAAEX,SAAS,EAAEC,KAAK,GAAG,CAAC,EAAE;IAC3C,KAAK,CAACU,WAAW,CAAC;IAClB,IAAI,CAACX,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACA,OAAOW,QAAQA,CAACC,GAAG,EAAE;IACjB,MAAM;MAAEC,YAAY;MAAEH;IAAY,CAAC,GAAGE,GAAG;IACzCC,YAAY,CAACC,OAAO,CAACJ,WAAW,CAAC;IACjC,IAAI,CAACK,WAAW,CAAC,CAAC;EACtB;EACAC,eAAeA,CAACH,YAAY,EAAE;IAC1B,MAAMH,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACO,GAAG,CAAC,IAAI,CAAClB,SAAS,CAACmB,QAAQ,CAACT,mBAAmB,CAACE,QAAQ,EAAE,IAAI,CAACX,KAAK,EAAE,IAAImB,gBAAgB,CAACN,YAAY,EAAE,IAAI,CAACH,WAAW,CAAC,CAAC,CAAC;EAC5I;EACAU,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACL,eAAe,CAACnB,YAAY,CAACyB,UAAU,CAACD,KAAK,CAAC,CAAC;EACxD;EACAE,MAAMA,CAACC,GAAG,EAAE;IACR,IAAI,CAACR,eAAe,CAACnB,YAAY,CAAC4B,WAAW,CAACD,GAAG,CAAC,CAAC;IACnD,IAAI,CAACT,WAAW,CAAC,CAAC;EACtB;EACAW,SAASA,CAAA,EAAG;IACR,IAAI,CAACV,eAAe,CAACnB,YAAY,CAAC8B,cAAc,CAAC,CAAC,CAAC;IACnD,IAAI,CAACZ,WAAW,CAAC,CAAC;EACtB;AACJ;AACA,OAAO,MAAMI,gBAAgB,CAAC;EAC1Bd,WAAWA,CAACQ,YAAY,EAAEH,WAAW,EAAE;IACnC,IAAI,CAACG,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACH,WAAW,GAAGA,WAAW;EAClC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
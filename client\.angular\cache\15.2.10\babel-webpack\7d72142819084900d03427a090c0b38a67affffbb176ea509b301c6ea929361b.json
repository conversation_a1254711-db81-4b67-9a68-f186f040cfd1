{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"app/services/settings.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"app/services/registration.service\";\nimport * as i5 from \"app/services/customfield.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i8 from \"../../../../../@core/pipes/decode-html.pipe\";\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 16);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.getOldValue(item_r2.key), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate((tmp_0_0 = ctx_r6.getOldValue(item_r2.key)) !== null && tmp_0_0 !== undefined ? tmp_0_0 : \"\");\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r7.getNewValue(item_r2.key), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.getRadioLabel(ctx_r8.rowData.metadata.new_data[item_r2.key], item_r2.props.options));\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_11_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"decodeHtml\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r15.formatValue(ctx_r15.getNewValue(item_r2.key))));\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_11_span_0_Template, 3, 3, \"span\", 7);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r2.type !== \"radio\" && item_r2.type !== \"select\");\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 6);\n    i0.ɵɵtemplate(5, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_img_5_Template, 1, 1, \"img\", 12);\n    i0.ɵɵtemplate(6, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_6_Template, 2, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 6);\n    i0.ɵɵtemplate(9, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_container_9_Template, 2, 1, \"ng-container\", 14);\n    i0.ɵɵtemplate(10, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_container_10_Template, 3, 1, \"ng-container\", 7);\n    i0.ɵɵtemplate(11, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_11_Template, 1, 1, \"ng-template\", null, 15, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r5 = i0.ɵɵreference(7);\n    const _r9 = i0.ɵɵreference(12);\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.props.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r2.type.includes(\"photo\") || item_r2.type.includes(\"image\") || item_r2.key.includes(\"photo\"))(\"ngIfElse\", _r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageType(item_r2))(\"ngIfElse\", _r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r2.type === \"radio\" || item_r2.type === \"select\");\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_Template, 13, 6, \"tr\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.compareData(item_r2.key, ctx_r1.rowData.metadata, item_r2) == false);\n  }\n}\nfunction ModalProcessRequestComponent_tbody_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, ModalProcessRequestComponent_tbody_17_ng_container_1_Template, 2, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0._settingsService.customFieldsValue);\n  }\n}\nexport class ModalProcessRequestComponent {\n  constructor(_translateService, _settingsService, modalService, activeModal, _registrationService, _customfieldService) {\n    this._translateService = _translateService;\n    this._settingsService = _settingsService;\n    this.modalService = modalService;\n    this.activeModal = activeModal;\n    this._registrationService = _registrationService;\n    this._customfieldService = _customfieldService;\n  }\n  ngOnInit() {\n    console.log(this.rowData);\n    this.getCustomFields();\n  }\n  getCustomFields() {\n    console.log(this._settingsService.customFieldsValue);\n  }\n  closeModal() {\n    this.modalService.dismissAll();\n  }\n  compareData(fieldKey, metadata, field) {\n    if (fieldKey == 'description') {\n      return true;\n    }\n    const matchingKeys = this._customfieldService.getMatchingKeys(metadata);\n    if (!matchingKeys.includes(fieldKey)) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n  playerValue(key) {\n    let value = this.rowData.player?.[key] || this.rowData.player?.user?.[key] || this.rowData.player?.custom_fields?.[key];\n    // Filter key in customFieldsValue\n    const customFieldsValue = this._settingsService.customFieldsValue;\n    const customField = customFieldsValue.find(field => field.key === key);\n    // Check if customField exists before accessing its properties\n    if (customField && (customField.type === 'radio' || customField.type === 'select') && customField.props?.options) {\n      let label = this.getRadioLabel(value, customField.props.options);\n      return label;\n    }\n    return this.formatValue(value);\n  }\n  getOldValue(key) {\n    console.log(key, this.rowData.metadata.old_data[key]);\n    return this.rowData.metadata.old_data[key] ?? this.rowData.metadata.old_data.user[key];\n  }\n  getNewValue(key) {\n    return this.rowData.metadata.new_data[key];\n  }\n  isImageType(item) {\n    return item.type?.includes('photo') || item.type?.includes('image') || item.key?.includes('photo');\n  }\n  getRadioLabel(value, options) {\n    if (!options || !value) {\n      return value; // Return the value if options or value are missing\n    }\n\n    console.log(value);\n    const option = options.find(opt => opt.value === value); // Find the option with the matching value\n    console.log(option);\n    return option.label; // Return the label if found, otherwise return the value\n  }\n\n  formatValue(value) {\n    // If value is a string and is a valid ISO datetime, format it\n    if (typeof value === 'string') {\n      const result = value == 'null' ? '' : value;\n      return /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}/.test(value) ? value.replace('T', ' ') : result;\n    }\n    return value; // Ensure to return the value if it is not a string\n  }\n\n  onReject() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        value: text\n      } = yield Swal.fire({\n        title: _this._translateService.instant('Reason for rejection'),\n        input: 'textarea',\n        inputLabel: 'Message',\n        inputPlaceholder: _this._translateService.instant('Enter reason for rejection'),\n        inputAttributes: {\n          'aria-label': 'Enter reason for rejection',\n          maxlength: 500\n        },\n        showCancelButton: true,\n        reverseButtons: true\n      });\n      Swal.fire({\n        title: _this._translateService.instant('Are you sure') + '?',\n        text: _this._translateService.instant('You are about to reject this request') + '!',\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: _this._translateService.instant('Yes'),\n        cancelButtonText: _this._translateService.instant('No'),\n        reverseButtons: true\n      }).then(result => {\n        if (result.isConfirmed) {\n          _this.reject(text);\n        }\n      });\n    })();\n  }\n  onAccept() {\n    Swal.fire({\n      title: this._translateService.instant('Are you sure') + '?',\n      text: this._translateService.instant('You are about to accept this request? All fields will be updated!'),\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.approve();\n      }\n    });\n  }\n  reject(notes) {\n    this._registrationService.rejectRequestUpdatePlayer(this.rowData.id, notes).subscribe(resp => {\n      this.activeModal.close({\n        status: 'rejected',\n        message: resp.message\n      });\n    }, error => {\n      console.log(error);\n    });\n  }\n  approve() {\n    const changedKeys = this.checkForDifferences();\n    this._registrationService.approveRequestUpdatePlayer(this.rowData.id).subscribe(resp => {\n      this.activeModal.close({\n        status: 'approved',\n        message: resp.message\n      });\n      if (changedKeys.includes('dob') && this.rowData.player.validate_status === 'Validated') {\n        this.approveGroup();\n      }\n    }, error => {\n      console.log(error);\n    });\n  }\n  approveGroup() {\n    this._registrationService.approveRegistrationGroup(this.rowData.player_id).subscribe(resp => {\n      this.activeModal.close({\n        status: 'approved',\n        message: resp.message\n      });\n    }, error => {\n      console.log(error);\n    });\n  }\n  checkForDifferences() {\n    let changedKeys = [];\n    this._settingsService.customFieldsValue.forEach(field => {\n      let currentValue = this.playerValue(field.key);\n      let newValue = this.rowData.metadata.new_data[field.key];\n      if (newValue !== currentValue) {\n        changedKeys.push(field.key);\n      }\n    });\n    return changedKeys;\n  }\n  static #_ = this.ɵfac = function ModalProcessRequestComponent_Factory(t) {\n    return new (t || ModalProcessRequestComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.SettingsService), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i3.NgbActiveModal), i0.ɵɵdirectiveInject(i4.RegistrationService), i0.ɵɵdirectiveInject(i5.CustomFieldService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalProcessRequestComponent,\n    selectors: [[\"app-modal-process-request\"]],\n    inputs: {\n      rowData: \"rowData\"\n    },\n    decls: 23,\n    vars: 4,\n    consts: [[1, \"modal-header\"], [\"id\", \"myModalLabel1\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [1, \"table\", \"table-bordered\"], [1, \"text-center\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [\"style\", \"max-width: 250px;\", \"class\", \"img-fluid\", \"alt\", \"Image\", 3, \"src\", 4, \"ngIf\", \"ngIfElse\"], [\"currentInfor\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [\"elseBlock\", \"\"], [\"alt\", \"Image\", 1, \"img-fluid\", 2, \"max-width\", \"250px\", 3, \"src\"], [\"alt\", \"\", 1, \"img-fluid\", 2, \"max-width\", \"250px\", 3, \"src\"]],\n    template: function ModalProcessRequestComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalProcessRequestComponent_Template_button_click_4_listener() {\n          return ctx.activeModal.close();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 4)(8, \"table\", 5)(9, \"thead\")(10, \"tr\", 6)(11, \"th\");\n        i0.ɵɵtext(12, \"Field\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"th\");\n        i0.ɵɵtext(14, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"th\");\n        i0.ɵɵtext(16, \"To\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(17, ModalProcessRequestComponent_tbody_17_Template, 2, 1, \"tbody\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 8)(19, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ModalProcessRequestComponent_Template_button_click_19_listener() {\n          return ctx.onReject();\n        });\n        i0.ɵɵtext(20, \" Reject \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ModalProcessRequestComponent_Template_button_click_21_listener() {\n          return ctx.onAccept();\n        });\n        i0.ɵɵtext(22, \" Accept \");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"Compare information\"));\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngIf\", ctx.rowData);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.RippleEffectDirective, i8.DecodeHtmlPipe, i1.TranslatePipe],\n    styles: [\"td[_ngcontent-%COMP%], th[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdGFibGVzL3BsYXllci11cGRhdGVzL21vZGFsLXByb2Nlc3MtcmVxdWVzdC9tb2RhbC1wcm9jZXNzLXJlcXVlc3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxnQkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsidGQsdGh7XHJcbiAgICBwYWRkaW5nOiAwLjI1cmVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAMA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;;ICiBpBC,EAAA,CAAAC,SAAA,cAIyH;;;;;IAJpHD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,GAAA,GAAAN,EAAA,CAAAO,aAAA,CAA6B;;;;;IAQhCP,EAAA,CAAAQ,cAAA,WAAM;IAAAR,EAAA,CAAAS,MAAA,GAAiC;IAAAT,EAAA,CAAAU,YAAA,EAAO;;;;;;IAAxCV,EAAA,CAAAW,SAAA,GAAiC;IAAjCX,EAAA,CAAAY,iBAAA,EAAAC,OAAA,GAAAC,MAAA,CAAAV,WAAA,CAAAC,OAAA,CAAAC,GAAA,eAAAO,OAAA,KAAAE,SAAA,GAAAF,OAAA,MAAiC;;;;;IAMzCb,EAAA,CAAAgB,uBAAA,GAAwD;IACtDhB,EAAA,CAAAC,SAAA,cAGY;IACdD,EAAA,CAAAiB,qBAAA,EAAe;;;;;IAJRjB,EAAA,CAAAW,SAAA,GAA6B;IAA7BX,EAAA,CAAAE,UAAA,QAAAgB,MAAA,CAAAC,WAAA,CAAAd,OAAA,CAAAC,GAAA,GAAAN,EAAA,CAAAO,aAAA,CAA6B;;;;;IAMpCP,EAAA,CAAAgB,uBAAA,GAAsE;IACpEhB,EAAA,CAAAQ,cAAA,WAAM;IAAAR,EAAA,CAAAS,MAAA,GAA4E;IAAAT,EAAA,CAAAU,YAAA,EAAO;IAC3FV,EAAA,CAAAiB,qBAAA,EAAe;;;;;IADPjB,EAAA,CAAAW,SAAA,GAA4E;IAA5EX,EAAA,CAAAY,iBAAA,CAAAQ,MAAA,CAAAC,aAAA,CAAAD,MAAA,CAAAE,OAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAnB,OAAA,CAAAC,GAAA,GAAAD,OAAA,CAAAoB,KAAA,CAAAC,OAAA,EAA4E;;;;;IAIlF1B,EAAA,CAAAQ,cAAA,WAC0D;IAAAR,EAAA,CAAAS,MAAA,GAAqD;;IAAAT,EAAA,CAAAU,YAAA,EAAO;;;;;IAA5DV,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAA2B,WAAA,OAAAC,OAAA,CAAAC,WAAA,CAAAD,OAAA,CAAAT,WAAA,CAAAd,OAAA,CAAAC,GAAA,IAAqD;;;;;IAD/GN,EAAA,CAAA8B,UAAA,IAAAC,wFAAA,kBACsH;;;;IAAnH/B,EAAA,CAAAE,UAAA,SAAAG,OAAA,CAAA2B,IAAA,gBAAA3B,OAAA,CAAA2B,IAAA,cAAqD;;;;;IAjC9DhC,EAAA,CAAAQ,cAAA,SAAmE;IAE5DR,EAAA,CAAAS,MAAA,GAAsB;IAAAT,EAAA,CAAAU,YAAA,EAAI;IAE/BV,EAAA,CAAAQ,cAAA,YAAwB;IAEtBR,EAAA,CAAA8B,UAAA,IAAAG,wEAAA,kBAIyH;IAEzHjC,EAAA,CAAA8B,UAAA,IAAAI,gFAAA,iCAAAlC,EAAA,CAAAmC,sBAAA,CAGc;IAChBnC,EAAA,CAAAU,YAAA,EAAK;IAGLV,EAAA,CAAAQ,cAAA,YAAwB;IACtBR,EAAA,CAAA8B,UAAA,IAAAM,iFAAA,2BAKe;IAEfpC,EAAA,CAAA8B,UAAA,KAAAO,kFAAA,0BAEe;IAEfrC,EAAA,CAAA8B,UAAA,KAAAQ,iFAAA,iCAAAtC,EAAA,CAAAmC,sBAAA,CAGc;IAChBnC,EAAA,CAAAU,YAAA,EAAK;;;;;;;IAjCAV,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAY,iBAAA,CAAAP,OAAA,CAAAoB,KAAA,CAAAc,KAAA,CAAsB;IAQnBvC,EAAA,CAAAW,SAAA,GAAgG;IAAhGX,EAAA,CAAAE,UAAA,SAAAG,OAAA,CAAA2B,IAAA,CAAAQ,QAAA,aAAAnC,OAAA,CAAA2B,IAAA,CAAAQ,QAAA,aAAAnC,OAAA,CAAAC,GAAA,CAAAkC,QAAA,UAAgG,aAAAC,GAAA;IAUvFzC,EAAA,CAAAW,SAAA,GAAyB;IAAzBX,EAAA,CAAAE,UAAA,SAAAwC,MAAA,CAAAC,WAAA,CAAAtC,OAAA,EAAyB,aAAAuC,GAAA;IAOzB5C,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAE,UAAA,SAAAG,OAAA,CAAA2B,IAAA,gBAAA3B,OAAA,CAAA2B,IAAA,cAAqD;;;;;IA5B1EhC,EAAA,CAAAgB,uBAAA,GAAsE;IACpEhB,EAAA,CAAA8B,UAAA,IAAAe,kEAAA,iBAqCK;IACP7C,EAAA,CAAAiB,qBAAA,EAAe;;;;;IAtCRjB,EAAA,CAAAW,SAAA,GAA4D;IAA5DX,EAAA,CAAAE,UAAA,SAAA4C,MAAA,CAAAC,WAAA,CAAA1C,OAAA,CAAAC,GAAA,EAAAwC,MAAA,CAAAxB,OAAA,CAAAC,QAAA,EAAAlB,OAAA,WAA4D;;;;;IAFnEL,EAAA,CAAAQ,cAAA,YAAuB;IACvBR,EAAA,CAAA8B,UAAA,IAAAkB,6DAAA,2BAuCe;IACfhD,EAAA,CAAAU,YAAA,EAAQ;;;;IAxCuBV,EAAA,CAAAW,SAAA,GAAqC;IAArCX,EAAA,CAAAE,UAAA,YAAA+C,MAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAqC;;;ADHxE,OAAM,MAAOC,4BAA4B;EAGvCC,YACSC,iBAAmC,EACnCJ,gBAAiC,EACjCK,YAAsB,EACtBC,WAA2B,EAC3BC,oBAAyC,EACzCC,mBAAuC;IALvC,KAAAJ,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAJ,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAK,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,mBAAmB,GAAnBA,mBAAmB;EAE5B;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvC,OAAO,CAAC;IACzB,IAAI,CAACwC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACbF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACX,gBAAgB,CAACC,iBAAiB,CAAC;EACtD;EAEAY,UAAUA,CAAA;IACR,IAAI,CAACR,YAAY,CAACS,UAAU,EAAE;EAChC;EAEAjB,WAAWA,CAACkB,QAAgB,EAAE1C,QAAa,EAAE2C,KAAU;IACrD,IAAID,QAAQ,IAAI,aAAa,EAAE;MAC7B,OAAO,IAAI;;IAEb,MAAME,YAAY,GAAG,IAAI,CAACT,mBAAmB,CAACU,eAAe,CAAC7C,QAAQ,CAAC;IACvE,IAAI,CAAC4C,YAAY,CAAC3B,QAAQ,CAACyB,QAAQ,CAAC,EAAE;MACpC,OAAO,IAAI;KACZ,MAAM;MACL,OAAO,KAAK;;EAEhB;EAEAI,WAAWA,CAAC/D,GAAW;IACrB,IAAIgE,KAAK,GACP,IAAI,CAAChD,OAAO,CAACiD,MAAM,GAAGjE,GAAG,CAAC,IAC1B,IAAI,CAACgB,OAAO,CAACiD,MAAM,EAAEC,IAAI,GAAGlE,GAAG,CAAC,IAChC,IAAI,CAACgB,OAAO,CAACiD,MAAM,EAAEE,aAAa,GAAGnE,GAAG,CAAC;IAE3C;IACA,MAAM6C,iBAAiB,GAAG,IAAI,CAACD,gBAAgB,CAACC,iBAAiB;IACjE,MAAMuB,WAAW,GAAGvB,iBAAiB,CAACwB,IAAI,CAAET,KAAK,IAAKA,KAAK,CAAC5D,GAAG,KAAKA,GAAG,CAAC;IAExE;IACA,IAAIoE,WAAW,KAAKA,WAAW,CAAC1C,IAAI,KAAK,OAAO,IAAI0C,WAAW,CAAC1C,IAAI,KAAK,QAAQ,CAAC,IAAI0C,WAAW,CAACjD,KAAK,EAAEC,OAAO,EAAE;MAChH,IAAIa,KAAK,GAAG,IAAI,CAAClB,aAAa,CAACiD,KAAK,EAAEI,WAAW,CAACjD,KAAK,CAACC,OAAO,CAAC;MAChE,OAAOa,KAAK;;IAGd,OAAO,IAAI,CAACV,WAAW,CAACyC,KAAK,CAAC;EAChC;EAEAlE,WAAWA,CAACE,GAAG;IACbsD,OAAO,CAACC,GAAG,CAACvD,GAAG,EAAE,IAAI,CAACgB,OAAO,CAACC,QAAQ,CAACqD,QAAQ,CAACtE,GAAG,CAAC,CAAC;IACrD,OAAO,IAAI,CAACgB,OAAO,CAACC,QAAQ,CAACqD,QAAQ,CAACtE,GAAG,CAAC,IAAI,IAAI,CAACgB,OAAO,CAACC,QAAQ,CAACqD,QAAQ,CAACJ,IAAI,CAAClE,GAAG,CAAC;EACxF;EAEAa,WAAWA,CAACb,GAAG;IACb,OAAO,IAAI,CAACgB,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAAClB,GAAG,CAAC;EAC5C;EAEAqC,WAAWA,CAACkC,IAAS;IACnB,OACEA,IAAI,CAAC7C,IAAI,EAAEQ,QAAQ,CAAC,OAAO,CAAC,IAC5BqC,IAAI,CAAC7C,IAAI,EAAEQ,QAAQ,CAAC,OAAO,CAAC,IAC5BqC,IAAI,CAACvE,GAAG,EAAEkC,QAAQ,CAAC,OAAO,CAAC;EAE/B;EAEAnB,aAAaA,CAACiD,KAAU,EAAE5C,OAAY;IACpC,IAAI,CAACA,OAAO,IAAI,CAAC4C,KAAK,EAAE;MACtB,OAAOA,KAAK,CAAC,CAAC;;;IAGhBV,OAAO,CAACC,GAAG,CAACS,KAAK,CAAC;IAClB,MAAMQ,MAAM,GAAGpD,OAAO,CAACiD,IAAI,CAACI,GAAG,IAAIA,GAAG,CAACT,KAAK,KAAKA,KAAK,CAAC,CAAC,CAAC;IACzDV,OAAO,CAACC,GAAG,CAACiB,MAAM,CAAC;IAEnB,OAAOA,MAAM,CAACvC,KAAK,CAAC,CAAC;EACvB;;EAEAV,WAAWA,CAACyC,KAAU;IACpB;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMU,MAAM,GAAIV,KAAK,IAAI,MAAM,GAAI,EAAE,GAAGA,KAAK;MAC7C,OAAO,gCAAgC,CAACW,IAAI,CAACX,KAAK,CAAC,GAC/CA,KAAK,CAACY,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GACvBF,MAAM;;IAGZ,OAAOV,KAAK,CAAC,CAAC;EAChB;;EAEMa,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAM;QAAEf,KAAK,EAAEgB;MAAI,CAAE,SAAUvF,IAAY,CAACwF,IAAI,CAAC;QAC/CC,KAAK,EAAEJ,KAAI,CAAC9B,iBAAiB,CAACmC,OAAO,CAAC,sBAAsB,CAAC;QAC7DC,KAAK,EAAE,UAAU;QACjBC,UAAU,EAAE,SAAS;QACrBC,gBAAgB,EAAER,KAAI,CAAC9B,iBAAiB,CAACmC,OAAO,CAC9C,4BAA4B,CAC7B;QACDI,eAAe,EAAE;UACf,YAAY,EAAE,4BAA4B;UAC1CC,SAAS,EAAE;SACZ;QACDC,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE;OACjB,CAAC;MAEFjG,IAAI,CAACwF,IAAI,CAAC;QACRC,KAAK,EAAEJ,KAAI,CAAC9B,iBAAiB,CAACmC,OAAO,CAAC,cAAc,CAAC,GAAG,GAAG;QAC3DH,IAAI,EACFF,KAAI,CAAC9B,iBAAiB,CAACmC,OAAO,CAC5B,sCAAsC,CACvC,GAAG,GAAG;QACTQ,IAAI,EAAE,SAAS;QACfF,gBAAgB,EAAE,IAAI;QACtBG,iBAAiB,EAAEd,KAAI,CAAC9B,iBAAiB,CAACmC,OAAO,CAAC,KAAK,CAAC;QACxDU,gBAAgB,EAAEf,KAAI,CAAC9B,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;QACtDO,cAAc,EAAE;OACjB,CAAC,CAACI,IAAI,CAAEpB,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACqB,WAAW,EAAE;UACtBjB,KAAI,CAACkB,MAAM,CAAChB,IAAI,CAAC;;MAErB,CAAC,CAAC;IAAC;EAEL;EAEAiB,QAAQA,CAAA;IACNxG,IAAI,CAACwF,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAAClC,iBAAiB,CAACmC,OAAO,CAAC,cAAc,CAAC,GAAG,GAAG;MAC3DH,IAAI,EAAE,IAAI,CAAChC,iBAAiB,CAACmC,OAAO,CAClC,mEAAmE,CACpE;MACDQ,IAAI,EAAE,SAAS;MACfF,gBAAgB,EAAE,IAAI;MACtBG,iBAAiB,EAAE,IAAI,CAAC5C,iBAAiB,CAACmC,OAAO,CAAC,KAAK,CAAC;MACxDU,gBAAgB,EAAE,IAAI,CAAC7C,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;MACtDO,cAAc,EAAE;KACjB,CAAC,CAACI,IAAI,CAAEpB,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACqB,WAAW,EAAE;QACtB,IAAI,CAACG,OAAO,EAAE;;IAElB,CAAC,CAAC;EACJ;EAEAF,MAAMA,CAACG,KAAK;IACV,IAAI,CAAChD,oBAAoB,CACtBiD,yBAAyB,CAAC,IAAI,CAACpF,OAAO,CAACqF,EAAE,EAAEF,KAAK,CAAC,CACjDG,SAAS,CACPC,IAAI,IAAI;MACP,IAAI,CAACrD,WAAW,CAACsD,KAAK,CAAC;QAAEC,MAAM,EAAE,UAAU;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAO,CAAE,CAAC;IACvE,CAAC,EACAC,KAAK,IAAI;MACRrD,OAAO,CAACC,GAAG,CAACoD,KAAK,CAAC;IACpB,CAAC,CACF;EACL;EAEAT,OAAOA,CAAA;IACL,MAAMU,WAAW,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAC9C,IAAI,CAAC1D,oBAAoB,CACtB2D,0BAA0B,CAAC,IAAI,CAAC9F,OAAO,CAACqF,EAAE,CAAC,CAC3CC,SAAS,CACPC,IAAI,IAAI;MACP,IAAI,CAACrD,WAAW,CAACsD,KAAK,CAAC;QAAEC,MAAM,EAAE,UAAU;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAO,CAAE,CAAC;MACrE,IACEE,WAAW,CAAC1E,QAAQ,CAAC,KAAK,CAAC,IAC3B,IAAI,CAAClB,OAAO,CAACiD,MAAM,CAAC8C,eAAe,KAAK,WAAW,EACnD;QACA,IAAI,CAACC,YAAY,EAAE;;IAEvB,CAAC,EACAL,KAAK,IAAI;MACRrD,OAAO,CAACC,GAAG,CAACoD,KAAK,CAAC;IACpB,CAAC,CACF;EACL;EAEAK,YAAYA,CAAA;IACV,IAAI,CAAC7D,oBAAoB,CACtB8D,wBAAwB,CAAC,IAAI,CAACjG,OAAO,CAACkG,SAAS,CAAC,CAChDZ,SAAS,CACPC,IAAI,IAAI;MACP,IAAI,CAACrD,WAAW,CAACsD,KAAK,CAAC;QAAEC,MAAM,EAAE,UAAU;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAO,CAAE,CAAC;IACvE,CAAC,EACAC,KAAK,IAAI;MACRrD,OAAO,CAACC,GAAG,CAACoD,KAAK,CAAC;IACpB,CAAC,CACF;EACL;EAEAE,mBAAmBA,CAAA;IACjB,IAAID,WAAW,GAAG,EAAE;IACpB,IAAI,CAAChE,gBAAgB,CAACC,iBAAiB,CAACsE,OAAO,CAAEvD,KAAK,IAAI;MACxD,IAAIwD,YAAY,GAAG,IAAI,CAACrD,WAAW,CAACH,KAAK,CAAC5D,GAAG,CAAC;MAC9C,IAAIqH,QAAQ,GAAG,IAAI,CAACrG,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAAC0C,KAAK,CAAC5D,GAAG,CAAC;MACxD,IAAIqH,QAAQ,KAAKD,YAAY,EAAE;QAC7BR,WAAW,CAACU,IAAI,CAAC1D,KAAK,CAAC5D,GAAG,CAAC;;IAE/B,CAAC,CAAC;IACF,OAAO4G,WAAW;EACpB;EAAC,QAAAW,CAAA;qBA/MUzE,4BAA4B,EAAApD,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAC,QAAA,GAAApI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAArI,EAAA,CAAA8H,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAAvI,EAAA,CAAA8H,iBAAA,CAAAU,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA;UAA5BtF,4BAA4B;IAAAuF,SAAA;IAAAC,MAAA;MAAAtH,OAAA;IAAA;IAAAuH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbzClJ,EAAA,CAAAQ,cAAA,aAA0B;QACmBR,EAAA,CAAAS,MAAA,GAAuC;;QAAAT,EAAA,CAAAU,YAAA,EAAK;QACvFV,EAAA,CAAAQ,cAAA,gBAAqF;QAA9BR,EAAA,CAAAoJ,UAAA,mBAAAC,8DAAA;UAAA,OAASF,GAAA,CAAA3F,WAAA,CAAAsD,KAAA,EAAmB;QAAA,EAAC;QAClF9G,EAAA,CAAAQ,cAAA,cAAyB;QAAAR,EAAA,CAAAS,MAAA,aAAO;QAAAT,EAAA,CAAAU,YAAA,EAAO;QAG3CV,EAAA,CAAAQ,cAAA,aAAkD;QAIxCR,EAAA,CAAAS,MAAA,aAAK;QAAAT,EAAA,CAAAU,YAAA,EAAK;QACdV,EAAA,CAAAQ,cAAA,UAAI;QAAAR,EAAA,CAAAS,MAAA,YAAI;QAAAT,EAAA,CAAAU,YAAA,EAAK;QACbV,EAAA,CAAAQ,cAAA,UAAI;QAAAR,EAAA,CAAAS,MAAA,UAAE;QAAAT,EAAA,CAAAU,YAAA,EAAK;QAGbV,EAAA,CAAA8B,UAAA,KAAAwH,8CAAA,mBAyCQ;QACVtJ,EAAA,CAAAU,YAAA,EAAQ;QAEVV,EAAA,CAAAQ,cAAA,cAA0B;QACkCR,EAAA,CAAAoJ,UAAA,mBAAAG,+DAAA;UAAA,OAASJ,GAAA,CAAAhE,QAAA,EAAU;QAAA,EAAC;QAC5EnF,EAAA,CAAAS,MAAA,gBACF;QAAAT,EAAA,CAAAU,YAAA,EAAS;QACTV,EAAA,CAAAQ,cAAA,kBAAgF;QAArBR,EAAA,CAAAoJ,UAAA,mBAAAI,+DAAA;UAAA,OAASL,GAAA,CAAA5C,QAAA,EAAU;QAAA,EAAC;QAC7EvG,EAAA,CAAAS,MAAA,gBACF;QAAAT,EAAA,CAAAU,YAAA,EAAS;;;QAhEkCV,EAAA,CAAAW,SAAA,GAAuC;QAAvCX,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAA2B,WAAA,8BAAuC;QAcxE3B,EAAA,CAAAW,SAAA,IAAa;QAAbX,EAAA,CAAAE,UAAA,SAAAiJ,GAAA,CAAA7H,OAAA,CAAa", "names": ["<PERSON><PERSON>", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r4", "getOldValue", "item_r2", "key", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "tmp_0_0", "ctx_r6", "undefined", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r7", "getNewValue", "ctx_r8", "getRadioLabel", "rowData", "metadata", "new_data", "props", "options", "ɵɵpipeBind1", "ctx_r15", "formatValue", "ɵɵtemplate", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_11_span_0_Template", "type", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_img_5_Template", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_6_Template", "ɵɵtemplateRefExtractor", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_container_9_Template", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_container_10_Template", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_ng_template_11_Template", "label", "includes", "_r5", "ctx_r3", "isImageType", "_r9", "ModalProcessRequestComponent_tbody_17_ng_container_1_tr_1_Template", "ctx_r1", "compareData", "ModalProcessRequestComponent_tbody_17_ng_container_1_Template", "ctx_r0", "_settingsService", "customFieldsValue", "ModalProcessRequestComponent", "constructor", "_translateService", "modalService", "activeModal", "_registrationService", "_customfieldService", "ngOnInit", "console", "log", "get<PERSON>ustom<PERSON>ields", "closeModal", "dismissAll", "<PERSON><PERSON><PERSON>", "field", "matching<PERSON><PERSON>s", "getMatchingKeys", "player<PERSON><PERSON><PERSON>", "value", "player", "user", "custom_fields", "customField", "find", "old_data", "item", "option", "opt", "result", "test", "replace", "onReject", "_this", "_asyncToGenerator", "text", "fire", "title", "instant", "input", "inputLabel", "inputPlaceholder", "inputAttributes", "maxlength", "showCancelButton", "reverseButtons", "icon", "confirmButtonText", "cancelButtonText", "then", "isConfirmed", "reject", "onAccept", "approve", "notes", "rejectRequestUpdatePlayer", "id", "subscribe", "resp", "close", "status", "message", "error", "changed<PERSON><PERSON><PERSON>", "checkForDifferences", "approveRequestUpdatePlayer", "validate_status", "approveGroup", "approveRegistrationGroup", "player_id", "for<PERSON>ach", "currentValue", "newValue", "push", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "SettingsService", "i3", "NgbModal", "NgbActiveModal", "i4", "RegistrationService", "i5", "CustomFieldService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ModalProcessRequestComponent_Template", "rf", "ctx", "ɵɵlistener", "ModalProcessRequestComponent_Template_button_click_4_listener", "ModalProcessRequestComponent_tbody_17_Template", "ModalProcessRequestComponent_Template_button_click_19_listener", "ModalProcessRequestComponent_Template_button_click_21_listener"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\player-updates\\modal-process-request\\modal-process-request.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\player-updates\\modal-process-request\\modal-process-request.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CustomFieldService } from 'app/services/customfield.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-modal-process-request',\r\n  templateUrl: './modal-process-request.component.html',\r\n  styleUrls: ['./modal-process-request.component.scss']\r\n})\r\nexport class ModalProcessRequestComponent implements OnInit {\r\n  @Input() rowData: any;\r\n\r\n  constructor(\r\n    public _translateService: TranslateService,\r\n    public _settingsService: SettingsService,\r\n    public modalService: NgbModal,\r\n    public activeModal: NgbActiveModal,\r\n    public _registrationService: RegistrationService,\r\n    public _customfieldService: CustomFieldService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.rowData);\r\n    this.getCustomFields();\r\n  }\r\n\r\n  getCustomFields() {\r\n    console.log(this._settingsService.customFieldsValue);\r\n  }\r\n\r\n  closeModal() {\r\n    this.modalService.dismissAll();\r\n  }\r\n\r\n  compareData(fieldKey: string, metadata: any, field: any): boolean {\r\n    if (fieldKey == 'description') {\r\n      return true;\r\n    }\r\n    const matchingKeys = this._customfieldService.getMatchingKeys(metadata);\r\n    if (!matchingKeys.includes(fieldKey)) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  playerValue(key: string) {\r\n    let value =\r\n      this.rowData.player?.[key] ||\r\n      this.rowData.player?.user?.[key] ||\r\n      this.rowData.player?.custom_fields?.[key];\r\n\r\n    // Filter key in customFieldsValue\r\n    const customFieldsValue = this._settingsService.customFieldsValue;\r\n    const customField = customFieldsValue.find((field) => field.key === key);\r\n\r\n    // Check if customField exists before accessing its properties\r\n    if (customField && (customField.type === 'radio' || customField.type === 'select') && customField.props?.options) {\r\n      let label = this.getRadioLabel(value, customField.props.options);\r\n      return label;\r\n    }\r\n\r\n    return this.formatValue(value);\r\n  }\r\n\r\n  getOldValue(key) {\r\n    console.log(key, this.rowData.metadata.old_data[key]);\r\n    return this.rowData.metadata.old_data[key] ?? this.rowData.metadata.old_data.user[key];\r\n  }\r\n\r\n  getNewValue(key) {\r\n    return this.rowData.metadata.new_data[key];\r\n  }\r\n\r\n  isImageType(item: any): boolean {\r\n    return (\r\n      item.type?.includes('photo') ||\r\n      item.type?.includes('image') ||\r\n      item.key?.includes('photo')\r\n    );\r\n  }\r\n\r\n  getRadioLabel(value: any, options: any) {\r\n    if (!options || !value) {\r\n      return value; // Return the value if options or value are missing\r\n    }\r\n\r\n    console.log(value);\r\n    const option = options.find(opt => opt.value === value); // Find the option with the matching value\r\n    console.log(option);\r\n\r\n    return option.label; // Return the label if found, otherwise return the value\r\n  }\r\n\r\n  formatValue(value: any) {\r\n    // If value is a string and is a valid ISO datetime, format it\r\n    if (typeof value === 'string') {\r\n      const result = (value == 'null') ? '' : value;\r\n      return /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}/.test(value)\r\n        ? value.replace('T', ' ')\r\n        : result;\r\n    }\r\n\r\n    return value; // Ensure to return the value if it is not a string\r\n  }\r\n\r\n  async onReject() {\r\n    const { value: text } = await (Swal as any).fire({\r\n      title: this._translateService.instant('Reason for rejection'),\r\n      input: 'textarea',\r\n      inputLabel: 'Message',\r\n      inputPlaceholder: this._translateService.instant(\r\n        'Enter reason for rejection'\r\n      ),\r\n      inputAttributes: {\r\n        'aria-label': 'Enter reason for rejection',\r\n        maxlength: 500\r\n      },\r\n      showCancelButton: true,\r\n      reverseButtons: true\r\n    });\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure') + '?',\r\n      text:\r\n        this._translateService.instant(\r\n          'You are about to reject this request'\r\n        ) + '!',\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.reject(text);\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  onAccept() {\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure') + '?',\r\n      text: this._translateService.instant(\r\n        'You are about to accept this request? All fields will be updated!'\r\n      ),\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.approve();\r\n      }\r\n    });\r\n  }\r\n\r\n  reject(notes) {\r\n    this._registrationService\r\n      .rejectRequestUpdatePlayer(this.rowData.id, notes)\r\n      .subscribe(\r\n        (resp) => {\r\n          this.activeModal.close({ status: 'rejected', message: resp.message });\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      );\r\n  }\r\n\r\n  approve() {\r\n    const changedKeys = this.checkForDifferences();\r\n    this._registrationService\r\n      .approveRequestUpdatePlayer(this.rowData.id)\r\n      .subscribe(\r\n        (resp) => {\r\n          this.activeModal.close({ status: 'approved', message: resp.message });\r\n          if (\r\n            changedKeys.includes('dob') &&\r\n            this.rowData.player.validate_status === 'Validated'\r\n          ) {\r\n            this.approveGroup();\r\n          }\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      );\r\n  }\r\n\r\n  approveGroup() {\r\n    this._registrationService\r\n      .approveRegistrationGroup(this.rowData.player_id)\r\n      .subscribe(\r\n        (resp) => {\r\n          this.activeModal.close({ status: 'approved', message: resp.message });\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      );\r\n  }\r\n\r\n  checkForDifferences() {\r\n    let changedKeys = [];\r\n    this._settingsService.customFieldsValue.forEach((field) => {\r\n      let currentValue = this.playerValue(field.key);\r\n      let newValue = this.rowData.metadata.new_data[field.key];\r\n      if (newValue !== currentValue) {\r\n        changedKeys.push(field.key);\r\n      }\r\n    });\r\n    return changedKeys;\r\n  }\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h4 class=\"modal-title\" id=\"myModalLabel1\">{{ 'Compare information' | translate }}</h4>\r\n  <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"activeModal.close()\">\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n  <table class=\"table table-bordered\">\r\n    <thead>\r\n    <tr class=\"text-center\">\r\n      <th>Field</th>\r\n      <th>From</th>\r\n      <th>To</th>\r\n    </tr>\r\n    </thead>\r\n    <tbody *ngIf=\"rowData\">\r\n    <ng-container *ngFor=\"let item of _settingsService.customFieldsValue\">\r\n      <tr *ngIf=\"compareData(item.key, rowData.metadata, item) == false\">\r\n        <td>\r\n          <b>{{ item.props.label }}</b>\r\n        </td>\r\n        <td class=\"text-center\">\r\n          <!-- Use playerValue to get the image source dynamically -->\r\n          <img [src]=\"getOldValue(item.key)\"\r\n               style=\"max-width: 250px;\"\r\n               class=\"img-fluid\"\r\n               alt=\"Image\"\r\n               *ngIf=\"item.type.includes('photo') || item.type.includes('image') || item.key.includes('photo'); else currentInfor\">\r\n\r\n          <ng-template #currentInfor>\r\n            <!-- Show text if the image is not found -->\r\n            <span>{{ getOldValue(item.key) ?? '' }}</span>\r\n          </ng-template>\r\n        </td>\r\n\r\n\r\n        <td class=\"text-center\">\r\n          <ng-container *ngIf=\"isImageType(item); else elseBlock\">\r\n            <img [src]=\"getNewValue(item.key)\"\r\n                 style=\"max-width: 250px;\"\r\n                 class=\"img-fluid\"\r\n                 alt=\"\">\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"item.type === 'radio' || item.type === 'select'\">\r\n            <span>{{ getRadioLabel(rowData.metadata.new_data[item.key], item.props.options) }}</span>\r\n          </ng-container>\r\n\r\n          <ng-template #elseBlock>\r\n            <span\r\n              *ngIf=\"item.type !== 'radio' && item.type !== 'select'\">{{ formatValue(getNewValue(item.key)) | decodeHtml }}</span>\r\n          </ng-template>\r\n        </td>\r\n\r\n      </tr>\r\n    </ng-container>\r\n    </tbody>\r\n  </table>\r\n</div>\r\n<div class=\"modal-footer\">\r\n  <button type=\"button\" class=\"btn btn-danger\" rippleEffect (click)=\"onReject()\">\r\n    Reject\r\n  </button>\r\n  <button type=\"button\" class=\"btn btn-primary\" rippleEffect (click)=\"onAccept()\">\r\n    Accept\r\n  </button>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
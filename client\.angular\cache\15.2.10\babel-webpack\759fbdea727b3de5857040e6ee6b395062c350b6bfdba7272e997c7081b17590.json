{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { ModalFollowsComponent } from './modal-follows/modal-follows.component';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/user.service\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"app/services/commons.service\";\nimport * as i4 from \"app/services/club.service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"@angular/common\";\nfunction NotificationComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"img\", 15);\n    i0.ɵɵlistener(\"error\", function NotificationComponent_div_16_div_1_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6._commonsService.onloadImgErr($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", ctx_r2.fav_clubs[i_r5] == null ? null : ctx_r2.fav_clubs[i_r5].code);\n    i0.ɵɵproperty(\"openDelay\", 50)(\"closeDelay\", 200);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r2.fav_clubs[i_r5] == null ? null : ctx_r2.fav_clubs[i_r5].logo, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationComponent_div_16_h6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.lenghtListDemo(ctx_r3.fav_clubs).lengthHide, \" \");\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction NotificationComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, NotificationComponent_div_16_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵtemplate(2, NotificationComponent_div_16_h6_2_Template, 2, 1, \"h6\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0).constructor(ctx_r0.lenghtListDemo(ctx_r0.fav_clubs).lengthShow));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.lenghtListDemo(ctx_r0.fav_clubs).lengthHide > 0);\n  }\n}\nfunction NotificationComponent_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"img\", 15);\n    i0.ɵɵlistener(\"error\", function NotificationComponent_div_28_div_1_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12._commonsService.onloadImgErr($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r11 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", ctx_r8.fav_teams[i_r11] == null ? null : ctx_r8.fav_teams[i_r11].name);\n    i0.ɵɵproperty(\"openDelay\", 50)(\"closeDelay\", 200);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r8.fav_teams[i_r11] == null ? null : ctx_r8.fav_teams[i_r11].logo, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationComponent_div_28_h6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r9.lenghtListDemo(ctx_r9.fav_teams).lengthHide, \" \");\n  }\n}\nfunction NotificationComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, NotificationComponent_div_28_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵtemplate(2, NotificationComponent_div_28_h6_2_Template, 2, 1, \"h6\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0).constructor(ctx_r1.lenghtListDemo(ctx_r1.fav_teams).lengthShow));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lenghtListDemo(ctx_r1.fav_teams).lengthHide > 0);\n  }\n}\nexport class NotificationComponent {\n  constructor(_userService, _modalService, _commonsService, _clubService, _translateService) {\n    this._userService = _userService;\n    this._modalService = _modalService;\n    this._commonsService = _commonsService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this.user = {};\n    this.club_list = [];\n    this.fav_clubs = [];\n    this.team_list = [];\n    this.fav_teams = [];\n    this.group_list = [];\n    this.updateFavClub = new EventEmitter();\n    this.updateFavTeam = new EventEmitter();\n    this.team_selects = [];\n    this.team_selects = [{\n      placeholder: 'Select club',\n      options: this.club_list,\n      key: 'club_id',\n      default: this.club_list[0]?.id\n    }, {\n      placeholder: 'Select group',\n      options: this.group_list,\n      key: 'group_id',\n      default: this.group_list[0]?.id\n    }];\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.group_list && changes.group_list.currentValue != changes.group_list.previousValue) {\n      this.team_selects[1].options = changes.group_list.currentValue;\n      this.team_selects[1].default = changes.group_list.currentValue[0]?.id;\n    }\n    if (changes.club_list && changes.club_list.currentValue != changes.club_list.previousValue) {\n      this.team_selects[0].options = changes.club_list.currentValue;\n      this.team_selects[0].default = changes.club_list.currentValue[0]?.id;\n    }\n  }\n  lenghtListDemo(list = []) {\n    let lengthShow = list.length;\n    let lengthHide = 0;\n    if (list.length >= 5) {\n      lengthShow = 5;\n      lengthHide = list.length - 5;\n    }\n    return {\n      lengthShow: lengthShow,\n      lengthHide: lengthHide\n    };\n  }\n  openModalFollows(data, funcToggle, event) {\n    if (data.list.length == 0) {\n      Swal.fire({\n        icon: 'info',\n        title: this._translateService.instant('Notification'),\n        text: this._translateService.instant('No data found, please try again later or contact administrator!'),\n        confirmButtonText: this._translateService.instant('OK'),\n        customClass: {\n          confirmButton: 'btn btn-primary'\n        }\n      });\n      return;\n    }\n    let modalRef = this._modalService.open(ModalFollowsComponent, {\n      size: 'lg',\n      centered: true,\n      backdrop: 'static',\n      scrollable: true\n    });\n    modalRef.componentInstance.data = data;\n    modalRef.componentInstance.onToggle = funcToggle;\n    //  when close modal\n    modalRef.dismissed.subscribe(res => {\n      event.emit(res);\n    });\n  }\n  toggleFavouriteClub(item) {\n    this._userService.toggleFavouriteClub(item.id).subscribe(res => {\n      // item.isFollow = !item.isFollow;\n    }, err => {\n      item.isFollow = false;\n      Swal.fire({\n        icon: 'error',\n        title: this._translateService.instant('Error'),\n        text: err.message,\n        confirmButtonText: this._translateService.instant('OK'),\n        customClass: {\n          confirmButton: 'btn btn-primary'\n        }\n      });\n    });\n  }\n  toggleFavouriteTeam(item) {\n    this._userService.toggleFavouriteTeam(item.id).subscribe(res => {\n      // item.isFollow = !item.isFollow;\n    }, err => {\n      item.isFollow = false;\n      Swal.fire({\n        icon: 'error',\n        title: this._translateService.instant('Error'),\n        text: err.message,\n        confirmButtonText: this._translateService.instant('OK'),\n        customClass: {\n          confirmButton: 'btn btn-primary'\n        }\n      });\n    });\n  }\n  static #_ = this.ɵfac = function NotificationComponent_Factory(t) {\n    return new (t || NotificationComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.CommonsService), i0.ɵɵdirectiveInject(i4.ClubService), i0.ɵɵdirectiveInject(i5.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotificationComponent,\n    selectors: [[\"profile-notification\"]],\n    inputs: {\n      user: \"user\",\n      club_list: \"club_list\",\n      fav_clubs: \"fav_clubs\",\n      team_list: \"team_list\",\n      fav_teams: \"fav_teams\",\n      group_list: \"group_list\"\n    },\n    outputs: {\n      updateFavClub: \"updateFavClub\",\n      updateFavTeam: \"updateFavTeam\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 29,\n    vars: 17,\n    consts: [[1, \"card\"], [1, \"mb-0\", 2, \"padding\", \"21px 21px 0px 21px\"], [1, \"card-group\"], [\"id\", \"favourite_clubs\", 1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"mb-0\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"avatar-group\", 4, \"ngIf\"], [\"id\", \"favourite_teams\", 1, \"card\"], [1, \"avatar-group\"], [\"placement\", \"bottom\", \"container\", \"body\", \"class\", \"avatar pull-up\", 3, \"ngbTooltip\", \"openDelay\", \"closeDelay\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"align-self-center cursor-pointer ml-50 mb-0\", 4, \"ngIf\"], [\"placement\", \"bottom\", \"container\", \"body\", 1, \"avatar\", \"pull-up\", 3, \"ngbTooltip\", \"openDelay\", \"closeDelay\"], [\"alt\", \"Avatar\", \"width\", \"33\", \"height\", \"33\", 3, \"src\", \"error\"], [1, \"align-self-center\", \"cursor-pointer\", \"ml-50\", \"mb-0\"]],\n    template: function NotificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"p\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5)(8, \"h4\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\")(12, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function NotificationComponent_Template_button_click_12_listener() {\n          return ctx.openModalFollows({\n            list: ctx.club_list,\n            title: \"Favourite Clubs\"\n          }, ctx.toggleFavouriteClub, ctx.updateFavClub);\n        });\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(15, \"div\", 8);\n        i0.ɵɵtemplate(16, NotificationComponent_div_16_Template, 3, 3, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 4)(19, \"div\", 5)(20, \"h4\", 6);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\")(24, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function NotificationComponent_Template_button_click_24_listener() {\n          return ctx.openModalFollows({\n            list: ctx.team_list,\n            title: \"Favourite Teams\",\n            selects: ctx.team_selects\n          }, ctx.toggleFavouriteTeam, ctx.updateFavTeam);\n        });\n        i0.ɵɵtext(25);\n        i0.ɵɵpipe(26, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(27, \"div\", 8);\n        i0.ɵɵtemplate(28, NotificationComponent_div_28_Template, 3, 3, \"div\", 9);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 7, \"Follow club/teams to receive the latest announcements related to clubs/teams\"), \" \");\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 9, \"Favourite Clubs\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 11, \"Follows\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.fav_clubs.length > 0);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 13, \"Favourite Teams\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 15, \"Follows\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.fav_teams.length > 0);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i2.NgbTooltip, i5.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAG9E,SAASC,qBAAqB,QAAQ,yCAAyC;AAI/E,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;ICYVC,EAAA,CAAAC,cAAA,cAE0F;IACtDD,EAAA,CAAAE,UAAA,mBAAAC,iEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,eAAA,CAAAC,YAAA,CAAAP,MAAA,CAAoC;IAAA,EAAC;IAA9EJ,EAAA,CAAAY,YAAA,EAC6B;;;;;IAJQZ,EAAA,CAAAa,qBAAA,eAAAC,MAAA,CAAAC,SAAA,CAAAC,IAAA,mBAAAF,MAAA,CAAAC,SAAA,CAAAC,IAAA,EAAAC,IAAA,CAAmC;IAACjB,EAAA,CAAAkB,UAAA,iBAAgB;IAGpFlB,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAkB,UAAA,QAAAJ,MAAA,CAAAC,SAAA,CAAAC,IAAA,mBAAAF,MAAA,CAAAC,SAAA,CAAAC,IAAA,EAAAI,IAAA,EAAApB,EAAA,CAAAqB,aAAA,CAA0B;;;;;IAGnCrB,EAAA,CAAAC,cAAA,aACqD;IACjDD,EAAA,CAAAsB,MAAA,GACJ;IAAAtB,EAAA,CAAAY,YAAA,EAAK;;;;IADDZ,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAuB,kBAAA,OAAAC,MAAA,CAAAC,cAAA,CAAAD,MAAA,CAAAT,SAAA,EAAAW,UAAA,MACJ;;;;;;;;IAVJ1B,EAAA,CAAAC,cAAA,cAAuD;IACnDD,EAAA,CAAA2B,UAAA,IAAAC,2CAAA,kBAKM;IACN5B,EAAA,CAAA2B,UAAA,IAAAE,0CAAA,iBAGK;IACT7B,EAAA,CAAAY,YAAA,EAAM;;;;IARmBZ,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,MAAA,CAAAR,cAAA,CAAAQ,MAAA,CAAAlB,SAAA,EAAAmB,UAAA,EAAyD;IAKzElC,EAAA,CAAAmB,SAAA,GAA8C;IAA9CnB,EAAA,CAAAkB,UAAA,SAAAe,MAAA,CAAAR,cAAA,CAAAQ,MAAA,CAAAlB,SAAA,EAAAW,UAAA,KAA8C;;;;;;IAoBnD1B,EAAA,CAAAC,cAAA,cAE0F;IACtDD,EAAA,CAAAE,UAAA,mBAAAiC,iEAAA/B,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4B,OAAA,CAAA3B,eAAA,CAAAC,YAAA,CAAAP,MAAA,CAAoC;IAAA,EAAC;IAA9EJ,EAAA,CAAAY,YAAA,EAC6B;;;;;IAJQZ,EAAA,CAAAa,qBAAA,eAAAyB,MAAA,CAAAC,SAAA,CAAAC,KAAA,mBAAAF,MAAA,CAAAC,SAAA,CAAAC,KAAA,EAAAC,IAAA,CAAmC;IAACzC,EAAA,CAAAkB,UAAA,iBAAgB;IAGpFlB,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAkB,UAAA,QAAAoB,MAAA,CAAAC,SAAA,CAAAC,KAAA,mBAAAF,MAAA,CAAAC,SAAA,CAAAC,KAAA,EAAApB,IAAA,EAAApB,EAAA,CAAAqB,aAAA,CAA0B;;;;;IAGnCrB,EAAA,CAAAC,cAAA,aACqD;IACjDD,EAAA,CAAAsB,MAAA,GACJ;IAAAtB,EAAA,CAAAY,YAAA,EAAK;;;;IADDZ,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAAuB,kBAAA,OAAAmB,MAAA,CAAAjB,cAAA,CAAAiB,MAAA,CAAAH,SAAA,EAAAb,UAAA,MACJ;;;;;IAVJ1B,EAAA,CAAAC,cAAA,cAAuD;IACnDD,EAAA,CAAA2B,UAAA,IAAAgB,2CAAA,kBAKM;IACN3C,EAAA,CAAA2B,UAAA,IAAAiB,0CAAA,iBAGK;IACT5C,EAAA,CAAAY,YAAA,EAAM;;;;IARmBZ,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAa,MAAA,CAAApB,cAAA,CAAAoB,MAAA,CAAAN,SAAA,EAAAL,UAAA,EAAyD;IAKzElC,EAAA,CAAAmB,SAAA,GAA8C;IAA9CnB,EAAA,CAAAkB,UAAA,SAAA2B,MAAA,CAAApB,cAAA,CAAAoB,MAAA,CAAAN,SAAA,EAAAb,UAAA,KAA8C;;;ADvCvE,OAAM,MAAOoB,qBAAqB;EAChCd,YACSe,YAAyB,EACzBC,aAAuB,EACvBtC,eAA+B,EAC/BuC,YAAyB,EACzBC,iBAAmC;IAJnC,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAtC,eAAe,GAAfA,eAAe;IACf,KAAAuC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAiBjB,KAAAC,IAAI,GAAQ,EAAE;IACd,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAArC,SAAS,GAAQ,EAAE;IACnB,KAAAsC,SAAS,GAAQ,EAAE;IACnB,KAAAd,SAAS,GAAQ,EAAE;IACnB,KAAAe,UAAU,GAAQ,EAAE;IACnB,KAAAC,aAAa,GAAsB,IAAI1D,YAAY,EAAE;IACrD,KAAA2D,aAAa,GAAsB,IAAI3D,YAAY,EAAE;IACxD,KAAA4D,YAAY,GAAG,EAAE;IAvBtB,IAAI,CAACA,YAAY,GAAG,CAClB;MACEC,WAAW,EAAE,aAAa;MAC1BC,OAAO,EAAE,IAAI,CAACP,SAAS;MACvBQ,GAAG,EAAE,SAAS;MACdC,OAAO,EAAE,IAAI,CAACT,SAAS,CAAC,CAAC,CAAC,EAAEU;KAC7B,EACD;MACEJ,WAAW,EAAE,cAAc;MAC3BC,OAAO,EAAE,IAAI,CAACL,UAAU;MACxBM,GAAG,EAAE,UAAU;MACfC,OAAO,EAAE,IAAI,CAACP,UAAU,CAAC,CAAC,CAAC,EAAEQ;KAC9B,CACF;EACH;EAUAC,QAAQA,CAAA,GAAU;EAElBC,WAAWA,CAACC,OAAY;IACtB,IACEA,OAAO,CAACX,UAAU,IAClBW,OAAO,CAACX,UAAU,CAACY,YAAY,IAAID,OAAO,CAACX,UAAU,CAACa,aAAa,EACnE;MACA,IAAI,CAACV,YAAY,CAAC,CAAC,CAAC,CAACE,OAAO,GAAGM,OAAO,CAACX,UAAU,CAACY,YAAY;MAC9D,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC,CAACI,OAAO,GAAGI,OAAO,CAACX,UAAU,CAACY,YAAY,CAAC,CAAC,CAAC,EAAEJ,EAAE;;IAGvE,IACEG,OAAO,CAACb,SAAS,IACjBa,OAAO,CAACb,SAAS,CAACc,YAAY,IAAID,OAAO,CAACb,SAAS,CAACe,aAAa,EACjE;MACA,IAAI,CAACV,YAAY,CAAC,CAAC,CAAC,CAACE,OAAO,GAAGM,OAAO,CAACb,SAAS,CAACc,YAAY;MAC7D,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC,CAACI,OAAO,GAAGI,OAAO,CAACb,SAAS,CAACc,YAAY,CAAC,CAAC,CAAC,EAAEJ,EAAE;;EAExE;EAEArC,cAAcA,CAAC2C,IAAI,GAAG,EAAE;IACtB,IAAIlC,UAAU,GAAGkC,IAAI,CAACC,MAAM;IAC5B,IAAI3C,UAAU,GAAG,CAAC;IAClB,IAAI0C,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE;MACpBnC,UAAU,GAAG,CAAC;MACdR,UAAU,GAAG0C,IAAI,CAACC,MAAM,GAAG,CAAC;;IAE9B,OAAO;MACLnC,UAAU,EAAEA,UAAU;MACtBR,UAAU,EAAEA;KACb;EACH;EAEA4C,gBAAgBA,CAACC,IAAI,EAAEC,UAA0B,EAAEC,KAAwB;IACzE,IAAIF,IAAI,CAACH,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE;MACzBtE,IAAI,CAAC2E,IAAI,CAAC;QACRC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC2B,OAAO,CAAC,cAAc,CAAC;QACrDC,IAAI,EAAE,IAAI,CAAC5B,iBAAiB,CAAC2B,OAAO,CAAC,iEAAiE,CAAC;QACvGE,iBAAiB,EAAE,IAAI,CAAC7B,iBAAiB,CAAC2B,OAAO,CAAC,IAAI,CAAC;QACvDG,WAAW,EAAE;UACXC,aAAa,EAAE;;OAElB,CAAC;MACF;;IAEF,IAAIC,QAAQ,GAAG,IAAI,CAAClC,aAAa,CAACmC,IAAI,CAACrF,qBAAqB,EAAE;MAC5DsF,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;KACb,CAAC;IAEFL,QAAQ,CAACM,iBAAiB,CAACjB,IAAI,GAAGA,IAAI;IACtCW,QAAQ,CAACM,iBAAiB,CAACC,QAAQ,GAAGjB,UAAU;IAEhD;IACAU,QAAQ,CAACQ,SAAS,CAACC,SAAS,CAAEC,GAAG,IAAI;MACnCnB,KAAK,CAACoB,IAAI,CAACD,GAAG,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAE,mBAAmBA,CAACC,IAAS;IAC3B,IAAI,CAAChD,YAAY,CAAC+C,mBAAmB,CAACC,IAAI,CAACjC,EAAE,CAAC,CAAC6B,SAAS,CACrDC,GAAG,IAAI;MACN;IAAA,CACD,EACAI,GAAG,IAAI;MACND,IAAI,CAACE,QAAQ,GAAG,KAAK;MACrBlG,IAAI,CAAC2E,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC2B,OAAO,CAAC,OAAO,CAAC;QAC9CC,IAAI,EAAEkB,GAAG,CAACE,OAAO;QACjBnB,iBAAiB,EAAE,IAAI,CAAC7B,iBAAiB,CAAC2B,OAAO,CAAC,IAAI,CAAC;QACvDG,WAAW,EAAE;UACXC,aAAa,EAAE;;OAElB,CAAC;IACJ,CAAC,CACF;EACH;EAEAkB,mBAAmBA,CAACJ,IAAS;IAC3B,IAAI,CAAChD,YAAY,CAACoD,mBAAmB,CAACJ,IAAI,CAACjC,EAAE,CAAC,CAAC6B,SAAS,CACrDC,GAAG,IAAI;MACN;IAAA,CACD,EACAI,GAAG,IAAI;MACND,IAAI,CAACE,QAAQ,GAAG,KAAK;MACrBlG,IAAI,CAAC2E,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC2B,OAAO,CAAC,OAAO,CAAC;QAC9CC,IAAI,EAAEkB,GAAG,CAACE,OAAO;QACjBnB,iBAAiB,EAAE,IAAI,CAAC7B,iBAAiB,CAAC2B,OAAO,CAAC,IAAI,CAAC;QACvDG,WAAW,EAAE;UACXC,aAAa,EAAE;;OAElB,CAAC;IACJ,CAAC,CACF;EACH;EAAC,QAAAmB,CAAA;qBApIUtD,qBAAqB,EAAA9C,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvG,EAAA,CAAAqG,iBAAA,CAAAG,EAAA,CAAAC,QAAA,GAAAzG,EAAA,CAAAqG,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3G,EAAA,CAAAqG,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAAqG,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAArBlE,qBAAqB;IAAAmE,SAAA;IAAAC,MAAA;MAAA/D,IAAA;MAAAC,SAAA;MAAArC,SAAA;MAAAsC,SAAA;MAAAd,SAAA;MAAAe,UAAA;IAAA;IAAA6D,OAAA;MAAA5D,aAAA;MAAAC,aAAA;IAAA;IAAA4D,QAAA,GAAApH,EAAA,CAAAqH,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdlC3H,EAAA,CAAAC,cAAA,aAAkB;QAEVD,EAAA,CAAAsB,MAAA,GACJ;;QAAAtB,EAAA,CAAAY,YAAA,EAAI;QACJZ,EAAA,CAAAC,cAAA,aAAwB;QAISD,EAAA,CAAAsB,MAAA,GAAiC;;QAAAtB,EAAA,CAAAY,YAAA,EAAK;QAE3DZ,EAAA,CAAAC,cAAA,WAAK;QAEGD,EAAA,CAAAE,UAAA,mBAAA2H,wDAAA;UAAA,OAASD,GAAA,CAAAtD,gBAAA;YAAAF,IAAA,EAAAwD,GAAA,CAAAxE,SAAA;YAAAwB,KAAA,EAAyC;UAAiB,GAAAgD,GAAA,CAAA9B,mBAAA,EAAA8B,GAAA,CAAArE,aAAA,CAAoC;QAAA,EAAC;QACxGvD,EAAA,CAAAsB,MAAA,IACJ;;QAAAtB,EAAA,CAAAY,YAAA,EAAS;QAGjBZ,EAAA,CAAAC,cAAA,cAAuB;QACnBD,EAAA,CAAA2B,UAAA,KAAAmG,qCAAA,iBAWM;QACV9H,EAAA,CAAAY,YAAA,EAAM;QAEVZ,EAAA,CAAAC,cAAA,eAAuC;QAGVD,EAAA,CAAAsB,MAAA,IAAiC;;QAAAtB,EAAA,CAAAY,YAAA,EAAK;QAE3DZ,EAAA,CAAAC,cAAA,WAAK;QAEGD,EAAA,CAAAE,UAAA,mBAAA6H,wDAAA;UAAA,OAASH,GAAA,CAAAtD,gBAAA;YAAAF,IAAA,EAAAwD,GAAA,CAAAvE,SAAA;YAAAuB,KAAA,EAAyC,iBAAiB;YAAAoD,OAAA,EAAAJ,GAAA,CAAAnE;UAAA,GAAAmE,GAAA,CAAAzB,mBAAA,EAAAyB,GAAA,CAAApE,aAAA,CAAyD;QAAA,EAAC;QAC7HxD,EAAA,CAAAsB,MAAA,IACJ;;QAAAtB,EAAA,CAAAY,YAAA,EAAS;QAGjBZ,EAAA,CAAAC,cAAA,cAAwB;QACpBD,EAAA,CAAA2B,UAAA,KAAAsG,qCAAA,iBAWM;QACVjI,EAAA,CAAAY,YAAA,EAAM;;;QAvDVZ,EAAA,CAAAmB,SAAA,GACJ;QADInB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAkI,WAAA,4FACJ;QAKiClI,EAAA,CAAAmB,SAAA,GAAiC;QAAjCnB,EAAA,CAAAmI,iBAAA,CAAAnI,EAAA,CAAAkI,WAAA,2BAAiC;QAK9ClI,EAAA,CAAAmB,SAAA,GACJ;QADInB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAkI,WAAA,yBACJ;QAIuBlI,EAAA,CAAAmB,SAAA,GAA0B;QAA1BnB,EAAA,CAAAkB,UAAA,SAAA0G,GAAA,CAAA7G,SAAA,CAAAsD,MAAA,KAA0B;QAiBhCrE,EAAA,CAAAmB,SAAA,GAAiC;QAAjCnB,EAAA,CAAAmI,iBAAA,CAAAnI,EAAA,CAAAkI,WAAA,4BAAiC;QAK9ClI,EAAA,CAAAmB,SAAA,GACJ;QADInB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAkI,WAAA,yBACJ;QAIuBlI,EAAA,CAAAmB,SAAA,GAA0B;QAA1BnB,EAAA,CAAAkB,UAAA,SAAA0G,GAAA,CAAArF,SAAA,CAAA8B,MAAA,KAA0B", "names": ["EventEmitter", "ModalFollowsComponent", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "NotificationComponent_div_16_div_1_Template_img_error_1_listener", "$event", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "_commonsService", "onloadImgErr", "ɵɵelementEnd", "ɵɵpropertyInterpolate", "ctx_r2", "fav_clubs", "i_r5", "code", "ɵɵproperty", "ɵɵadvance", "logo", "ɵɵsanitizeUrl", "ɵɵtext", "ɵɵtextInterpolate1", "ctx_r3", "lenghtListDemo", "lengthHide", "ɵɵtemplate", "NotificationComponent_div_16_div_1_Template", "NotificationComponent_div_16_h6_2_Template", "ɵɵpureFunction0", "_c0", "constructor", "ctx_r0", "lengthShow", "NotificationComponent_div_28_div_1_Template_img_error_1_listener", "_r13", "ctx_r12", "ctx_r8", "fav_teams", "i_r11", "name", "ctx_r9", "NotificationComponent_div_28_div_1_Template", "NotificationComponent_div_28_h6_2_Template", "ctx_r1", "NotificationComponent", "_userService", "_modalService", "_clubService", "_translateService", "user", "club_list", "team_list", "group_list", "updateFavClub", "updateFavTeam", "team_selects", "placeholder", "options", "key", "default", "id", "ngOnInit", "ngOnChanges", "changes", "currentValue", "previousValue", "list", "length", "openModalFollows", "data", "func<PERSON>oggle", "event", "fire", "icon", "title", "instant", "text", "confirmButtonText", "customClass", "confirmButton", "modalRef", "open", "size", "centered", "backdrop", "scrollable", "componentInstance", "onToggle", "dismissed", "subscribe", "res", "emit", "toggleFavouriteClub", "item", "err", "is<PERSON><PERSON>ow", "message", "toggleFavouriteTeam", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "NgbModal", "i3", "CommonsService", "i4", "ClubService", "i5", "TranslateService", "_2", "selectors", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NotificationComponent_Template", "rf", "ctx", "NotificationComponent_Template_button_click_12_listener", "NotificationComponent_div_16_Template", "NotificationComponent_Template_button_click_24_listener", "selects", "NotificationComponent_div_28_Template", "ɵɵpipeBind1", "ɵɵtextInterpolate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\notification\\notification.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\notification\\notification.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { ModalFollowsComponent } from './modal-follows/modal-follows.component';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'profile-notification',\r\n  templateUrl: './notification.component.html',\r\n  styleUrls: ['./notification.component.scss'],\r\n})\r\nexport class NotificationComponent implements OnInit {\r\n  constructor(\r\n    public _userService: UserService,\r\n    public _modalService: NgbModal,\r\n    public _commonsService: CommonsService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService\r\n  ) {\r\n    this.team_selects = [\r\n      {\r\n        placeholder: 'Select club',\r\n        options: this.club_list,\r\n        key: 'club_id',\r\n        default: this.club_list[0]?.id,\r\n      },\r\n      {\r\n        placeholder: 'Select group',\r\n        options: this.group_list,\r\n        key: 'group_id',\r\n        default: this.group_list[0]?.id,\r\n      },\r\n    ];\r\n  }\r\n  @Input() user: any = {};\r\n  @Input() club_list: any = [];\r\n  @Input() fav_clubs: any = [];\r\n  @Input() team_list: any = [];\r\n  @Input() fav_teams: any = [];\r\n  @Input() group_list: any = [];\r\n  @Output() updateFavClub: EventEmitter<any> = new EventEmitter();\r\n  @Output() updateFavTeam: EventEmitter<any> = new EventEmitter();\r\n  public team_selects = [];\r\n  ngOnInit(): void {}\r\n\r\n  ngOnChanges(changes: any) {\r\n    if (\r\n      changes.group_list &&\r\n      changes.group_list.currentValue != changes.group_list.previousValue\r\n    ) {\r\n      this.team_selects[1].options = changes.group_list.currentValue;\r\n      this.team_selects[1].default = changes.group_list.currentValue[0]?.id;\r\n    }\r\n\r\n    if (\r\n      changes.club_list &&\r\n      changes.club_list.currentValue != changes.club_list.previousValue\r\n    ) {\r\n      this.team_selects[0].options = changes.club_list.currentValue;\r\n      this.team_selects[0].default = changes.club_list.currentValue[0]?.id;\r\n    }\r\n  }\r\n\r\n  lenghtListDemo(list = []) {\r\n    let lengthShow = list.length;\r\n    let lengthHide = 0;\r\n    if (list.length >= 5) {\r\n      lengthShow = 5;\r\n      lengthHide = list.length - 5;\r\n    }\r\n    return {\r\n      lengthShow: lengthShow,\r\n      lengthHide: lengthHide,\r\n    };\r\n  }\r\n\r\n  openModalFollows(data, funcToggle: (item) => void, event: EventEmitter<any>) {\r\n    if (data.list.length == 0) {\r\n      Swal.fire({\r\n        icon: 'info',\r\n        title: this._translateService.instant('Notification'),\r\n        text: this._translateService.instant('No data found, please try again later or contact administrator!'),\r\n        confirmButtonText: this._translateService.instant('OK'),\r\n        customClass: {\r\n          confirmButton: 'btn btn-primary',\r\n        },\r\n      });\r\n      return;\r\n    }\r\n    let modalRef = this._modalService.open(ModalFollowsComponent, {\r\n      size: 'lg',\r\n      centered: true,\r\n      backdrop: 'static',\r\n      scrollable: true,\r\n    });\r\n\r\n    modalRef.componentInstance.data = data;\r\n    modalRef.componentInstance.onToggle = funcToggle;\r\n\r\n    //  when close modal\r\n    modalRef.dismissed.subscribe((res) => {\r\n      event.emit(res);\r\n    });\r\n  }\r\n\r\n  toggleFavouriteClub(item: any) {\r\n    this._userService.toggleFavouriteClub(item.id).subscribe(\r\n      (res) => {\r\n        // item.isFollow = !item.isFollow;\r\n      },\r\n      (err) => {\r\n        item.isFollow = false;\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: this._translateService.instant('Error'),\r\n          text: err.message,\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary',\r\n          },\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  toggleFavouriteTeam(item: any) {\r\n    this._userService.toggleFavouriteTeam(item.id).subscribe(\r\n      (res) => {\r\n        // item.isFollow = !item.isFollow;\r\n      },\r\n      (err) => {\r\n        item.isFollow = false;\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: this._translateService.instant('Error'),\r\n          text: err.message,\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary',\r\n          },\r\n        });\r\n      }\r\n    );\r\n  }\r\n}\r\n", "<div class=\"card\">\r\n    <p class=\"mb-0\" style=\"padding: 21px 21px 0px 21px;\">\r\n        {{'Follow club/teams to receive the latest announcements related to clubs/teams'|translate}}\r\n    </p>\r\n    <div class=\"card-group\">\r\n        <div class=\"card\" id=\"favourite_clubs\">\r\n            <div class=\"card-header\">\r\n                <div class=\"card-title\">\r\n                    <h4 class=\"mb-0\">{{'Favourite Clubs' | translate}}</h4>\r\n                </div>\r\n                <div>\r\n                    <button class=\"btn btn-primary btn-sm\"\r\n                        (click)=\"openModalFollows({list: club_list,title: 'Favourite Clubs'},toggleFavouriteClub,updateFavClub)\">\r\n                        {{'Follows'| translate}}\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"card-body\">\r\n                <div class=\"avatar-group\" *ngIf=\"fav_clubs.length > 0\">\r\n                    <div placement=\"bottom\" container=\"body\" ngbTooltip=\"{{fav_clubs[i]?.code}}\" [openDelay]=\"50\"\r\n                        [closeDelay]=\"200\" class=\"avatar pull-up\"\r\n                        *ngFor=\"let club of [].constructor(lenghtListDemo(fav_clubs).lengthShow); index as i\">\r\n                        <img [src]=\"fav_clubs[i]?.logo\" (error)=\"_commonsService.onloadImgErr($event)\" alt=\"Avatar\"\r\n                            width=\"33\" height=\"33\" />\r\n                    </div>\r\n                    <h6 class=\"align-self-center cursor-pointer ml-50 mb-0\"\r\n                        *ngIf=\"lenghtListDemo(fav_clubs).lengthHide > 0\">\r\n                        +{{lenghtListDemo(fav_clubs).lengthHide}}\r\n                    </h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"card\" id=\"favourite_teams\">\r\n            <div class=\"card-header\">\r\n                <div class=\"card-title\">\r\n                    <h4 class=\"mb-0\">{{'Favourite Teams' | translate}}</h4>\r\n                </div>\r\n                <div>\r\n                    <button class=\"btn btn-primary btn-sm\"\r\n                        (click)=\"openModalFollows({list: team_list,title: 'Favourite Teams',selects:team_selects},toggleFavouriteTeam,updateFavTeam)\">\r\n                        {{'Follows' | translate}}\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\" card-body\">\r\n                <div class=\"avatar-group\" *ngIf=\"fav_teams.length > 0\">\r\n                    <div placement=\"bottom\" container=\"body\" ngbTooltip=\"{{fav_teams[i]?.name}}\" [openDelay]=\"50\"\r\n                        [closeDelay]=\"200\" class=\"avatar pull-up\"\r\n                        *ngFor=\"let club of [].constructor(lenghtListDemo(fav_teams).lengthShow); index as i\">\r\n                        <img [src]=\"fav_teams[i]?.logo\" (error)=\"_commonsService.onloadImgErr($event)\" alt=\"Avatar\"\r\n                            width=\"33\" height=\"33\" />\r\n                    </div>\r\n                    <h6 class=\"align-self-center cursor-pointer ml-50 mb-0\"\r\n                        *ngIf=\"lenghtListDemo(fav_teams).lengthHide > 0\">\r\n                        +{{lenghtListDemo(fav_teams).lengthHide}}\r\n                    </h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
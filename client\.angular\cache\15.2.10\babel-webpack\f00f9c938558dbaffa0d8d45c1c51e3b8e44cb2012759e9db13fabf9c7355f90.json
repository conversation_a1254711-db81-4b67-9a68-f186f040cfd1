{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { NgbAccordionModule, NgbCollapseModule, NgbDropdownModule, NgbModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { DataTablesModule } from 'angular-datatables';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\nimport { MatchCardModule } from 'app/components/match-card/match-card.module';\nimport { ProfileComponent } from './profile.component';\nimport { EditProfileComponent } from './edit-profile/edit-profile.component';\nimport { ProfileSercurityComponent } from './profile-sercurity/profile-sercurity.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { ModalFollowsComponent } from './notification/modal-follows/modal-follows.component';\nimport { ModalTwofaComponent } from './profile-sercurity/modal-twofa/modal-twofa.component';\nimport { FilterPipe } from '@core/pipes/filter.pipe';\nimport { QRCodeModule } from 'angularx-qrcode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\nconst routes = [{\n  path: 'profile',\n  component: ProfileComponent,\n  data: {\n    title: 'Account'\n  }\n}];\nexport class ProfileModule {\n  static #_ = this.ɵfac = function ProfileModule_Factory(t) {\n    return new (t || ProfileModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProfileModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [FilterPipe],\n    imports: [QRCodeModule, CoreCommonModule, NgbNavModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, RouterModule.forChild(routes), ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, MatchCardModule, FormlyModule.forRoot({\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    })]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent, EditProfileComponent, ProfileSercurityComponent, NotificationComponent, ModalFollowsComponent, ModalTwofaComponent],\n    imports: [QRCodeModule, CoreCommonModule, NgbNavModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, i1.RouterModule, ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, MatchCardModule, i2.FormlyModule],\n    exports: [ProfileComponent, EditProfileComponent, ProfileSercurityComponent, NotificationComponent, ModalTwofaComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,kBAAkB,EAClBC,iBAAiB,EACjBC,iBAAiB,EACjBC,SAAS,EACTC,YAAY,QACP,4BAA4B;AACnC,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,eAAe,QAA0B,qBAAqB;AACvE,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,qDAAqD;AAC5D,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAAuBC,YAAY,QAAQ,kBAAkB;AAC7D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,qBAAqB,QAAQ,sDAAsD;AAC5F,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,YAAY,QAAQ,iBAAiB;;;;AAE9C,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEV,gBAAgB;EAC3BW,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAS;CACzB,CACF;AAkDD,OAAM,MAAOC,aAAa;EAAA,QAAAC,CAAA;qBAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA;UAAbF;EAAa;EAAA,QAAAG,EAAA;eA/Cb,CAACV,UAAU,CAAC;IAAAW,OAAA,GAUrBV,YAAY,EACZjB,gBAAgB,EAChBP,YAAY,EACZJ,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBJ,YAAY,EACZC,YAAY,CAACwC,QAAQ,CAACV,MAAM,CAAC,EAC7BxB,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBE,uBAAuB,EACvBG,mBAAmB,EACnBlB,iBAAiB,EACjBiB,qBAAqB,EACrBE,eAAe,EACfH,YAAY,CAACuB,OAAO,CAAC;MACnBC,kBAAkB,EAAE,CAClB;QAAEC,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE5B;MAAuB,CAAE;KAE5D,CAAC;EAAA;;;2EAUOmB,aAAa;IAAAU,YAAA,GA7CtBvB,gBAAgB,EAChBC,oBAAoB,EACpBC,yBAAyB,EACzBC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB;IAAAY,OAAA,GAGnBV,YAAY,EACZjB,gBAAgB,EAChBP,YAAY,EACZJ,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBJ,YAAY,EAAA+C,EAAA,CAAA9C,YAAA,EAEZM,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBE,uBAAuB,EACvBG,mBAAmB,EACnBlB,iBAAiB,EACjBiB,qBAAqB,EACrBE,eAAe,EAAA0B,EAAA,CAAA7B,YAAA;IAAA8B,OAAA,GAQf1B,gBAAgB,EAChBC,oBAAoB,EACpBC,yBAAyB,EACzBC,qBAAqB,EACrBE,mBAAmB;EAAA;AAAA", "names": ["CommonModule", "RouterModule", "NgbAccordionModule", "NgbCollapseModule", "NgbDropdownModule", "NgbModule", "NgbNavModule", "ContentHeaderModule", "FormsModule", "ReactiveFormsModule", "ErrorMessageModule", "TranslateModule", "CoreSidebarModule", "CoreCommonModule", "DataTablesModule", "NgSelectModule", "EditorSidebarModule", "serverValidationMessage", "BtnDropdownActionModule", "FormlyModule", "FormlyBootstrapModule", "CoreTouchspinModule", "MatchCardModule", "ProfileComponent", "EditProfileComponent", "ProfileSercurityComponent", "NotificationComponent", "ModalFollowsComponent", "ModalTwofaComponent", "FilterPipe", "QRCodeModule", "routes", "path", "component", "data", "title", "ProfileModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "validationMessages", "name", "message", "declarations", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport {\r\n  NgbAccordionModule,\r\n  NgbCollapseModule,\r\n  NgbDropdownModule,\r\n  NgbModule,\r\n  NgbNavModule,\r\n} from '@ng-bootstrap/ng-bootstrap';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport {\r\n  EditorSidebarModule,\r\n  serverValidationMessage,\r\n} from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { FormlyConfig, FormlyModule } from '@ngx-formly/core';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\r\nimport { MatchCardModule } from 'app/components/match-card/match-card.module';\r\nimport { ProfileComponent } from './profile.component';\r\nimport { EditProfileComponent } from './edit-profile/edit-profile.component';\r\nimport { ProfileSercurityComponent } from './profile-sercurity/profile-sercurity.component';\r\nimport { NotificationComponent } from './notification/notification.component';\r\nimport { ModalFollowsComponent } from './notification/modal-follows/modal-follows.component';\r\nimport { ModalTwofaComponent } from './profile-sercurity/modal-twofa/modal-twofa.component';\r\nimport { FilterPipe } from '@core/pipes/filter.pipe';\r\nimport { QRCodeModule } from 'angularx-qrcode';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'profile',\r\n    component: ProfileComponent,\r\n    data: { title: 'Account' },\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  providers: [FilterPipe],\r\n  declarations: [\r\n    ProfileComponent,\r\n    EditProfileComponent,\r\n    ProfileSercurityComponent,\r\n    NotificationComponent,\r\n    ModalFollowsComponent,\r\n    ModalTwofaComponent,\r\n  ],\r\n  imports: [\r\n    QRCodeModule,\r\n    CoreCommonModule,\r\n    NgbNavModule,\r\n    NgbAccordionModule,\r\n    NgbModule,\r\n    NgbDropdownModule,\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ErrorMessageModule,\r\n    TranslateModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    DataTablesModule,\r\n    NgSelectModule,\r\n    EditorSidebarModule,\r\n    BtnDropdownActionModule,\r\n    CoreTouchspinModule,\r\n    NgbCollapseModule,\r\n    FormlyBootstrapModule,\r\n    MatchCardModule,\r\n    FormlyModule.forRoot({\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],  \r\n    }),\r\n  ],\r\n  exports: [\r\n    ProfileComponent,\r\n    EditProfileComponent,\r\n    ProfileSercurityComponent,\r\n    NotificationComponent,\r\n    ModalTwofaComponent,\r\n  ],\r\n})\r\nexport class ProfileModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
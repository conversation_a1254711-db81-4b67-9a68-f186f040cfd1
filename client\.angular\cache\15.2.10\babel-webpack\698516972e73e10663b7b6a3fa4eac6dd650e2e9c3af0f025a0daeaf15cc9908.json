{"ast": null, "code": "import { reduce } from './reduce';\nfunction toArrayReducer(arr, item, index) {\n  if (index === 0) {\n    return [item];\n  }\n  arr.push(item);\n  return arr;\n}\nexport function toArray() {\n  return reduce(toArrayReducer, []);\n}", "map": {"version": 3, "names": ["reduce", "toArrayReducer", "arr", "item", "index", "push", "toArray"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/toArray.js"], "sourcesContent": ["import { reduce } from './reduce';\nfunction toArrayReducer(arr, item, index) {\n    if (index === 0) {\n        return [item];\n    }\n    arr.push(item);\n    return arr;\n}\nexport function toArray() {\n    return reduce(toArrayReducer, []);\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,cAAcA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACtC,IAAIA,KAAK,KAAK,CAAC,EAAE;IACb,OAAO,CAACD,IAAI,CAAC;EACjB;EACAD,GAAG,CAACG,IAAI,CAACF,IAAI,CAAC;EACd,OAAOD,GAAG;AACd;AACA,OAAO,SAASI,OAAOA,CAAA,EAAG;EACtB,OAAON,MAAM,CAACC,cAAc,EAAE,EAAE,CAAC;AACrC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
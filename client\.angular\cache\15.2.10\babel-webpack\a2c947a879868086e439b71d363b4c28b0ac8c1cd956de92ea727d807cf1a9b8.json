{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin, CapacitorException } from '@capacitor/core';\nimport { CameraSource, CameraDirection } from './definitions';\nexport class CameraWeb extends WebPlugin {\n  getPhoto(options) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // eslint-disable-next-line no-async-promise-executor\n      return new Promise( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (resolve, reject) {\n          if (options.webUseInput || options.source === CameraSource.Photos) {\n            _this.fileInputExperience(options, resolve, reject);\n          } else if (options.source === CameraSource.Prompt) {\n            let actionSheet = document.querySelector('pwa-action-sheet');\n            if (!actionSheet) {\n              actionSheet = document.createElement('pwa-action-sheet');\n              document.body.appendChild(actionSheet);\n            }\n            actionSheet.header = options.promptLabelHeader || 'Photo';\n            actionSheet.cancelable = false;\n            actionSheet.options = [{\n              title: options.promptLabelPhoto || 'From Photos'\n            }, {\n              title: options.promptLabelPicture || 'Take Picture'\n            }];\n            actionSheet.addEventListener('onSelection', /*#__PURE__*/function () {\n              var _ref2 = _asyncToGenerator(function* (e) {\n                const selection = e.detail;\n                if (selection === 0) {\n                  _this.fileInputExperience(options, resolve, reject);\n                } else {\n                  _this.cameraExperience(options, resolve, reject);\n                }\n              });\n              return function (_x3) {\n                return _ref2.apply(this, arguments);\n              };\n            }());\n          } else {\n            _this.cameraExperience(options, resolve, reject);\n          }\n        });\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  pickImages(_options) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // eslint-disable-next-line no-async-promise-executor\n      return new Promise( /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (resolve, reject) {\n          _this2.multipleFileInputExperience(resolve, reject);\n        });\n        return function (_x4, _x5) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  cameraExperience(options, resolve, reject) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (customElements.get('pwa-camera-modal')) {\n        const cameraModal = document.createElement('pwa-camera-modal');\n        cameraModal.facingMode = options.direction === CameraDirection.Front ? 'user' : 'environment';\n        document.body.appendChild(cameraModal);\n        try {\n          yield cameraModal.componentOnReady();\n          cameraModal.addEventListener('onPhoto', /*#__PURE__*/function () {\n            var _ref4 = _asyncToGenerator(function* (e) {\n              const photo = e.detail;\n              if (photo === null) {\n                reject(new CapacitorException('User cancelled photos app'));\n              } else if (photo instanceof Error) {\n                reject(photo);\n              } else {\n                resolve(yield _this3._getCameraPhoto(photo, options));\n              }\n              cameraModal.dismiss();\n              document.body.removeChild(cameraModal);\n            });\n            return function (_x6) {\n              return _ref4.apply(this, arguments);\n            };\n          }());\n          cameraModal.present();\n        } catch (e) {\n          _this3.fileInputExperience(options, resolve, reject);\n        }\n      } else {\n        console.error(`Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements.`);\n        _this3.fileInputExperience(options, resolve, reject);\n      }\n    })();\n  }\n  fileInputExperience(options, resolve, reject) {\n    let input = document.querySelector('#_capacitor-camera-input');\n    const cleanup = () => {\n      var _a;\n      (_a = input.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(input);\n    };\n    if (!input) {\n      input = document.createElement('input');\n      input.id = '_capacitor-camera-input';\n      input.type = 'file';\n      input.hidden = true;\n      document.body.appendChild(input);\n      input.addEventListener('change', _e => {\n        const file = input.files[0];\n        let format = 'jpeg';\n        if (file.type === 'image/png') {\n          format = 'png';\n        } else if (file.type === 'image/gif') {\n          format = 'gif';\n        }\n        if (options.resultType === 'dataUrl' || options.resultType === 'base64') {\n          const reader = new FileReader();\n          reader.addEventListener('load', () => {\n            if (options.resultType === 'dataUrl') {\n              resolve({\n                dataUrl: reader.result,\n                format\n              });\n            } else if (options.resultType === 'base64') {\n              const b64 = reader.result.split(',')[1];\n              resolve({\n                base64String: b64,\n                format\n              });\n            }\n            cleanup();\n          });\n          reader.readAsDataURL(file);\n        } else {\n          resolve({\n            webPath: URL.createObjectURL(file),\n            format: format\n          });\n          cleanup();\n        }\n      });\n      input.addEventListener('cancel', _e => {\n        reject(new CapacitorException('User cancelled photos app'));\n        cleanup();\n      });\n    }\n    input.accept = 'image/*';\n    input.capture = true;\n    if (options.source === CameraSource.Photos || options.source === CameraSource.Prompt) {\n      input.removeAttribute('capture');\n    } else if (options.direction === CameraDirection.Front) {\n      input.capture = 'user';\n    } else if (options.direction === CameraDirection.Rear) {\n      input.capture = 'environment';\n    }\n    input.click();\n  }\n  multipleFileInputExperience(resolve, reject) {\n    let input = document.querySelector('#_capacitor-camera-input-multiple');\n    const cleanup = () => {\n      var _a;\n      (_a = input.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(input);\n    };\n    if (!input) {\n      input = document.createElement('input');\n      input.id = '_capacitor-camera-input-multiple';\n      input.type = 'file';\n      input.hidden = true;\n      input.multiple = true;\n      document.body.appendChild(input);\n      input.addEventListener('change', _e => {\n        const photos = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < input.files.length; i++) {\n          const file = input.files[i];\n          let format = 'jpeg';\n          if (file.type === 'image/png') {\n            format = 'png';\n          } else if (file.type === 'image/gif') {\n            format = 'gif';\n          }\n          photos.push({\n            webPath: URL.createObjectURL(file),\n            format: format\n          });\n        }\n        resolve({\n          photos\n        });\n        cleanup();\n      });\n      input.addEventListener('cancel', _e => {\n        reject(new CapacitorException('User cancelled photos app'));\n        cleanup();\n      });\n    }\n    input.accept = 'image/*';\n    input.click();\n  }\n  _getCameraPhoto(photo, options) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      const format = photo.type.split('/')[1];\n      if (options.resultType === 'uri') {\n        resolve({\n          webPath: URL.createObjectURL(photo),\n          format: format,\n          saved: false\n        });\n      } else {\n        reader.readAsDataURL(photo);\n        reader.onloadend = () => {\n          const r = reader.result;\n          if (options.resultType === 'dataUrl') {\n            resolve({\n              dataUrl: r,\n              format: format,\n              saved: false\n            });\n          } else {\n            resolve({\n              base64String: r.split(',')[1],\n              format: format,\n              saved: false\n            });\n          }\n        };\n        reader.onerror = e => {\n          reject(e);\n        };\n      }\n    });\n  }\n  checkPermissions() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (typeof navigator === 'undefined' || !navigator.permissions) {\n        throw _this4.unavailable('Permissions API not available in this browser');\n      }\n      try {\n        // https://developer.mozilla.org/en-US/docs/Web/API/Permissions/query\n        // the specific permissions that are supported varies among browsers that implement the\n        // permissions API, so we need a try/catch in case 'camera' is invalid\n        const permission = yield window.navigator.permissions.query({\n          name: 'camera'\n        });\n        return {\n          camera: permission.state,\n          photos: 'granted'\n        };\n      } catch (_a) {\n        throw _this4.unavailable('Camera permissions are not available in this browser');\n      }\n    })();\n  }\n  requestPermissions() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      throw _this5.unimplemented('Not implemented on web.');\n    })();\n  }\n  pickLimitedLibraryPhotos() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      throw _this6.unavailable('Not implemented on web.');\n    })();\n  }\n  getLimitedLibraryPhotos() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      throw _this7.unavailable('Not implemented on web.');\n    })();\n  }\n}\nconst Camera = new CameraWeb();\nexport { Camera };", "map": {"version": 3, "names": ["WebPlugin", "CapacitorException", "CameraSource", "CameraDirection", "CameraWeb", "getPhoto", "options", "_this", "_asyncToGenerator", "Promise", "_ref", "resolve", "reject", "webUseInput", "source", "Photos", "fileInputExperience", "Prompt", "actionSheet", "document", "querySelector", "createElement", "body", "append<PERSON><PERSON><PERSON>", "header", "prompt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelable", "title", "prompt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "promptLabelPicture", "addEventListener", "_ref2", "e", "selection", "detail", "cameraExperience", "_x3", "apply", "arguments", "_x", "_x2", "pickImages", "_options", "_this2", "_ref3", "multipleFileInputExperience", "_x4", "_x5", "_this3", "customElements", "get", "cameraModal", "facingMode", "direction", "Front", "componentOnReady", "_ref4", "photo", "Error", "_getCameraPhoto", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "_x6", "present", "console", "error", "input", "cleanup", "_a", "parentNode", "id", "type", "hidden", "_e", "file", "files", "format", "resultType", "reader", "FileReader", "dataUrl", "result", "b64", "split", "base64String", "readAsDataURL", "webPath", "URL", "createObjectURL", "accept", "capture", "removeAttribute", "Rear", "click", "multiple", "photos", "i", "length", "push", "saved", "onloadend", "r", "onerror", "checkPermissions", "_this4", "navigator", "permissions", "unavailable", "permission", "window", "query", "name", "camera", "state", "requestPermissions", "_this5", "unimplemented", "pickLimitedLibraryPhotos", "_this6", "getLimitedLibraryPhotos", "_this7", "Camera"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/camera/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin, CapacitorException } from '@capacitor/core';\nimport { CameraSource, CameraDirection } from './definitions';\nexport class CameraWeb extends WebPlugin {\n    async getPhoto(options) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise(async (resolve, reject) => {\n            if (options.webUseInput || options.source === CameraSource.Photos) {\n                this.fileInputExperience(options, resolve, reject);\n            }\n            else if (options.source === CameraSource.Prompt) {\n                let actionSheet = document.querySelector('pwa-action-sheet');\n                if (!actionSheet) {\n                    actionSheet = document.createElement('pwa-action-sheet');\n                    document.body.appendChild(actionSheet);\n                }\n                actionSheet.header = options.promptLabelHeader || 'Photo';\n                actionSheet.cancelable = false;\n                actionSheet.options = [\n                    { title: options.promptLabelPhoto || 'From Photos' },\n                    { title: options.promptLabelPicture || 'Take Picture' },\n                ];\n                actionSheet.addEventListener('onSelection', async (e) => {\n                    const selection = e.detail;\n                    if (selection === 0) {\n                        this.fileInputExperience(options, resolve, reject);\n                    }\n                    else {\n                        this.cameraExperience(options, resolve, reject);\n                    }\n                });\n            }\n            else {\n                this.cameraExperience(options, resolve, reject);\n            }\n        });\n    }\n    async pickImages(_options) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise(async (resolve, reject) => {\n            this.multipleFileInputExperience(resolve, reject);\n        });\n    }\n    async cameraExperience(options, resolve, reject) {\n        if (customElements.get('pwa-camera-modal')) {\n            const cameraModal = document.createElement('pwa-camera-modal');\n            cameraModal.facingMode =\n                options.direction === CameraDirection.Front ? 'user' : 'environment';\n            document.body.appendChild(cameraModal);\n            try {\n                await cameraModal.componentOnReady();\n                cameraModal.addEventListener('onPhoto', async (e) => {\n                    const photo = e.detail;\n                    if (photo === null) {\n                        reject(new CapacitorException('User cancelled photos app'));\n                    }\n                    else if (photo instanceof Error) {\n                        reject(photo);\n                    }\n                    else {\n                        resolve(await this._getCameraPhoto(photo, options));\n                    }\n                    cameraModal.dismiss();\n                    document.body.removeChild(cameraModal);\n                });\n                cameraModal.present();\n            }\n            catch (e) {\n                this.fileInputExperience(options, resolve, reject);\n            }\n        }\n        else {\n            console.error(`Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements.`);\n            this.fileInputExperience(options, resolve, reject);\n        }\n    }\n    fileInputExperience(options, resolve, reject) {\n        let input = document.querySelector('#_capacitor-camera-input');\n        const cleanup = () => {\n            var _a;\n            (_a = input.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(input);\n        };\n        if (!input) {\n            input = document.createElement('input');\n            input.id = '_capacitor-camera-input';\n            input.type = 'file';\n            input.hidden = true;\n            document.body.appendChild(input);\n            input.addEventListener('change', (_e) => {\n                const file = input.files[0];\n                let format = 'jpeg';\n                if (file.type === 'image/png') {\n                    format = 'png';\n                }\n                else if (file.type === 'image/gif') {\n                    format = 'gif';\n                }\n                if (options.resultType === 'dataUrl' ||\n                    options.resultType === 'base64') {\n                    const reader = new FileReader();\n                    reader.addEventListener('load', () => {\n                        if (options.resultType === 'dataUrl') {\n                            resolve({\n                                dataUrl: reader.result,\n                                format,\n                            });\n                        }\n                        else if (options.resultType === 'base64') {\n                            const b64 = reader.result.split(',')[1];\n                            resolve({\n                                base64String: b64,\n                                format,\n                            });\n                        }\n                        cleanup();\n                    });\n                    reader.readAsDataURL(file);\n                }\n                else {\n                    resolve({\n                        webPath: URL.createObjectURL(file),\n                        format: format,\n                    });\n                    cleanup();\n                }\n            });\n            input.addEventListener('cancel', (_e) => {\n                reject(new CapacitorException('User cancelled photos app'));\n                cleanup();\n            });\n        }\n        input.accept = 'image/*';\n        input.capture = true;\n        if (options.source === CameraSource.Photos ||\n            options.source === CameraSource.Prompt) {\n            input.removeAttribute('capture');\n        }\n        else if (options.direction === CameraDirection.Front) {\n            input.capture = 'user';\n        }\n        else if (options.direction === CameraDirection.Rear) {\n            input.capture = 'environment';\n        }\n        input.click();\n    }\n    multipleFileInputExperience(resolve, reject) {\n        let input = document.querySelector('#_capacitor-camera-input-multiple');\n        const cleanup = () => {\n            var _a;\n            (_a = input.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(input);\n        };\n        if (!input) {\n            input = document.createElement('input');\n            input.id = '_capacitor-camera-input-multiple';\n            input.type = 'file';\n            input.hidden = true;\n            input.multiple = true;\n            document.body.appendChild(input);\n            input.addEventListener('change', (_e) => {\n                const photos = [];\n                // eslint-disable-next-line @typescript-eslint/prefer-for-of\n                for (let i = 0; i < input.files.length; i++) {\n                    const file = input.files[i];\n                    let format = 'jpeg';\n                    if (file.type === 'image/png') {\n                        format = 'png';\n                    }\n                    else if (file.type === 'image/gif') {\n                        format = 'gif';\n                    }\n                    photos.push({\n                        webPath: URL.createObjectURL(file),\n                        format: format,\n                    });\n                }\n                resolve({ photos });\n                cleanup();\n            });\n            input.addEventListener('cancel', (_e) => {\n                reject(new CapacitorException('User cancelled photos app'));\n                cleanup();\n            });\n        }\n        input.accept = 'image/*';\n        input.click();\n    }\n    _getCameraPhoto(photo, options) {\n        return new Promise((resolve, reject) => {\n            const reader = new FileReader();\n            const format = photo.type.split('/')[1];\n            if (options.resultType === 'uri') {\n                resolve({\n                    webPath: URL.createObjectURL(photo),\n                    format: format,\n                    saved: false,\n                });\n            }\n            else {\n                reader.readAsDataURL(photo);\n                reader.onloadend = () => {\n                    const r = reader.result;\n                    if (options.resultType === 'dataUrl') {\n                        resolve({\n                            dataUrl: r,\n                            format: format,\n                            saved: false,\n                        });\n                    }\n                    else {\n                        resolve({\n                            base64String: r.split(',')[1],\n                            format: format,\n                            saved: false,\n                        });\n                    }\n                };\n                reader.onerror = e => {\n                    reject(e);\n                };\n            }\n        });\n    }\n    async checkPermissions() {\n        if (typeof navigator === 'undefined' || !navigator.permissions) {\n            throw this.unavailable('Permissions API not available in this browser');\n        }\n        try {\n            // https://developer.mozilla.org/en-US/docs/Web/API/Permissions/query\n            // the specific permissions that are supported varies among browsers that implement the\n            // permissions API, so we need a try/catch in case 'camera' is invalid\n            const permission = await window.navigator.permissions.query({\n                name: 'camera',\n            });\n            return {\n                camera: permission.state,\n                photos: 'granted',\n            };\n        }\n        catch (_a) {\n            throw this.unavailable('Camera permissions are not available in this browser');\n        }\n    }\n    async requestPermissions() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n    async pickLimitedLibraryPhotos() {\n        throw this.unavailable('Not implemented on web.');\n    }\n    async getLimitedLibraryPhotos() {\n        throw this.unavailable('Not implemented on web.');\n    }\n}\nconst Camera = new CameraWeb();\nexport { Camera };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,kBAAkB,QAAQ,iBAAiB;AAC/D,SAASC,YAAY,EAAEC,eAAe,QAAQ,eAAe;AAC7D,OAAO,MAAMC,SAAS,SAASJ,SAAS,CAAC;EAC/BK,QAAQA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpB;MACA,OAAO,IAAIC,OAAO;QAAA,IAAAC,IAAA,GAAAF,iBAAA,CAAC,WAAOG,OAAO,EAAEC,MAAM,EAAK;UAC1C,IAAIN,OAAO,CAACO,WAAW,IAAIP,OAAO,CAACQ,MAAM,KAAKZ,YAAY,CAACa,MAAM,EAAE;YAC/DR,KAAI,CAACS,mBAAmB,CAACV,OAAO,EAAEK,OAAO,EAAEC,MAAM,CAAC;UACtD,CAAC,MACI,IAAIN,OAAO,CAACQ,MAAM,KAAKZ,YAAY,CAACe,MAAM,EAAE;YAC7C,IAAIC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;YAC5D,IAAI,CAACF,WAAW,EAAE;cACdA,WAAW,GAAGC,QAAQ,CAACE,aAAa,CAAC,kBAAkB,CAAC;cACxDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,WAAW,CAAC;YAC1C;YACAA,WAAW,CAACM,MAAM,GAAGlB,OAAO,CAACmB,iBAAiB,IAAI,OAAO;YACzDP,WAAW,CAACQ,UAAU,GAAG,KAAK;YAC9BR,WAAW,CAACZ,OAAO,GAAG,CAClB;cAAEqB,KAAK,EAAErB,OAAO,CAACsB,gBAAgB,IAAI;YAAc,CAAC,EACpD;cAAED,KAAK,EAAErB,OAAO,CAACuB,kBAAkB,IAAI;YAAe,CAAC,CAC1D;YACDX,WAAW,CAACY,gBAAgB,CAAC,aAAa;cAAA,IAAAC,KAAA,GAAAvB,iBAAA,CAAE,WAAOwB,CAAC,EAAK;gBACrD,MAAMC,SAAS,GAAGD,CAAC,CAACE,MAAM;gBAC1B,IAAID,SAAS,KAAK,CAAC,EAAE;kBACjB1B,KAAI,CAACS,mBAAmB,CAACV,OAAO,EAAEK,OAAO,EAAEC,MAAM,CAAC;gBACtD,CAAC,MACI;kBACDL,KAAI,CAAC4B,gBAAgB,CAAC7B,OAAO,EAAEK,OAAO,EAAEC,MAAM,CAAC;gBACnD;cACJ,CAAC;cAAA,iBAAAwB,GAAA;gBAAA,OAAAL,KAAA,CAAAM,KAAA,OAAAC,SAAA;cAAA;YAAA,IAAC;UACN,CAAC,MACI;YACD/B,KAAI,CAAC4B,gBAAgB,CAAC7B,OAAO,EAAEK,OAAO,EAAEC,MAAM,CAAC;UACnD;QACJ,CAAC;QAAA,iBAAA2B,EAAA,EAAAC,GAAA;UAAA,OAAA9B,IAAA,CAAA2B,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACMG,UAAUA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAnC,iBAAA;MACvB;MACA,OAAO,IAAIC,OAAO;QAAA,IAAAmC,KAAA,GAAApC,iBAAA,CAAC,WAAOG,OAAO,EAAEC,MAAM,EAAK;UAC1C+B,MAAI,CAACE,2BAA2B,CAAClC,OAAO,EAAEC,MAAM,CAAC;QACrD,CAAC;QAAA,iBAAAkC,GAAA,EAAAC,GAAA;UAAA,OAAAH,KAAA,CAAAP,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACMH,gBAAgBA,CAAC7B,OAAO,EAAEK,OAAO,EAAEC,MAAM,EAAE;IAAA,IAAAoC,MAAA;IAAA,OAAAxC,iBAAA;MAC7C,IAAIyC,cAAc,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAE;QACxC,MAAMC,WAAW,GAAGhC,QAAQ,CAACE,aAAa,CAAC,kBAAkB,CAAC;QAC9D8B,WAAW,CAACC,UAAU,GAClB9C,OAAO,CAAC+C,SAAS,KAAKlD,eAAe,CAACmD,KAAK,GAAG,MAAM,GAAG,aAAa;QACxEnC,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC4B,WAAW,CAAC;QACtC,IAAI;UACA,MAAMA,WAAW,CAACI,gBAAgB,CAAC,CAAC;UACpCJ,WAAW,CAACrB,gBAAgB,CAAC,SAAS;YAAA,IAAA0B,KAAA,GAAAhD,iBAAA,CAAE,WAAOwB,CAAC,EAAK;cACjD,MAAMyB,KAAK,GAAGzB,CAAC,CAACE,MAAM;cACtB,IAAIuB,KAAK,KAAK,IAAI,EAAE;gBAChB7C,MAAM,CAAC,IAAIX,kBAAkB,CAAC,2BAA2B,CAAC,CAAC;cAC/D,CAAC,MACI,IAAIwD,KAAK,YAAYC,KAAK,EAAE;gBAC7B9C,MAAM,CAAC6C,KAAK,CAAC;cACjB,CAAC,MACI;gBACD9C,OAAO,OAAOqC,MAAI,CAACW,eAAe,CAACF,KAAK,EAAEnD,OAAO,CAAC,CAAC;cACvD;cACA6C,WAAW,CAACS,OAAO,CAAC,CAAC;cACrBzC,QAAQ,CAACG,IAAI,CAACuC,WAAW,CAACV,WAAW,CAAC;YAC1C,CAAC;YAAA,iBAAAW,GAAA;cAAA,OAAAN,KAAA,CAAAnB,KAAA,OAAAC,SAAA;YAAA;UAAA,IAAC;UACFa,WAAW,CAACY,OAAO,CAAC,CAAC;QACzB,CAAC,CACD,OAAO/B,CAAC,EAAE;UACNgB,MAAI,CAAChC,mBAAmB,CAACV,OAAO,EAAEK,OAAO,EAAEC,MAAM,CAAC;QACtD;MACJ,CAAC,MACI;QACDoD,OAAO,CAACC,KAAK,CAAE,6GAA4G,CAAC;QAC5HjB,MAAI,CAAChC,mBAAmB,CAACV,OAAO,EAAEK,OAAO,EAAEC,MAAM,CAAC;MACtD;IAAC;EACL;EACAI,mBAAmBA,CAACV,OAAO,EAAEK,OAAO,EAAEC,MAAM,EAAE;IAC1C,IAAIsD,KAAK,GAAG/C,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;IAC9D,MAAM+C,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGF,KAAK,CAACG,UAAU,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,WAAW,CAACK,KAAK,CAAC;IACtF,CAAC;IACD,IAAI,CAACA,KAAK,EAAE;MACRA,KAAK,GAAG/C,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MACvC6C,KAAK,CAACI,EAAE,GAAG,yBAAyB;MACpCJ,KAAK,CAACK,IAAI,GAAG,MAAM;MACnBL,KAAK,CAACM,MAAM,GAAG,IAAI;MACnBrD,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC2C,KAAK,CAAC;MAChCA,KAAK,CAACpC,gBAAgB,CAAC,QAAQ,EAAG2C,EAAE,IAAK;QACrC,MAAMC,IAAI,GAAGR,KAAK,CAACS,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAIC,MAAM,GAAG,MAAM;QACnB,IAAIF,IAAI,CAACH,IAAI,KAAK,WAAW,EAAE;UAC3BK,MAAM,GAAG,KAAK;QAClB,CAAC,MACI,IAAIF,IAAI,CAACH,IAAI,KAAK,WAAW,EAAE;UAChCK,MAAM,GAAG,KAAK;QAClB;QACA,IAAItE,OAAO,CAACuE,UAAU,KAAK,SAAS,IAChCvE,OAAO,CAACuE,UAAU,KAAK,QAAQ,EAAE;UACjC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;UAC/BD,MAAM,CAAChD,gBAAgB,CAAC,MAAM,EAAE,MAAM;YAClC,IAAIxB,OAAO,CAACuE,UAAU,KAAK,SAAS,EAAE;cAClClE,OAAO,CAAC;gBACJqE,OAAO,EAAEF,MAAM,CAACG,MAAM;gBACtBL;cACJ,CAAC,CAAC;YACN,CAAC,MACI,IAAItE,OAAO,CAACuE,UAAU,KAAK,QAAQ,EAAE;cACtC,MAAMK,GAAG,GAAGJ,MAAM,CAACG,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cACvCxE,OAAO,CAAC;gBACJyE,YAAY,EAAEF,GAAG;gBACjBN;cACJ,CAAC,CAAC;YACN;YACAT,OAAO,CAAC,CAAC;UACb,CAAC,CAAC;UACFW,MAAM,CAACO,aAAa,CAACX,IAAI,CAAC;QAC9B,CAAC,MACI;UACD/D,OAAO,CAAC;YACJ2E,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACd,IAAI,CAAC;YAClCE,MAAM,EAAEA;UACZ,CAAC,CAAC;UACFT,OAAO,CAAC,CAAC;QACb;MACJ,CAAC,CAAC;MACFD,KAAK,CAACpC,gBAAgB,CAAC,QAAQ,EAAG2C,EAAE,IAAK;QACrC7D,MAAM,CAAC,IAAIX,kBAAkB,CAAC,2BAA2B,CAAC,CAAC;QAC3DkE,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN;IACAD,KAAK,CAACuB,MAAM,GAAG,SAAS;IACxBvB,KAAK,CAACwB,OAAO,GAAG,IAAI;IACpB,IAAIpF,OAAO,CAACQ,MAAM,KAAKZ,YAAY,CAACa,MAAM,IACtCT,OAAO,CAACQ,MAAM,KAAKZ,YAAY,CAACe,MAAM,EAAE;MACxCiD,KAAK,CAACyB,eAAe,CAAC,SAAS,CAAC;IACpC,CAAC,MACI,IAAIrF,OAAO,CAAC+C,SAAS,KAAKlD,eAAe,CAACmD,KAAK,EAAE;MAClDY,KAAK,CAACwB,OAAO,GAAG,MAAM;IAC1B,CAAC,MACI,IAAIpF,OAAO,CAAC+C,SAAS,KAAKlD,eAAe,CAACyF,IAAI,EAAE;MACjD1B,KAAK,CAACwB,OAAO,GAAG,aAAa;IACjC;IACAxB,KAAK,CAAC2B,KAAK,CAAC,CAAC;EACjB;EACAhD,2BAA2BA,CAAClC,OAAO,EAAEC,MAAM,EAAE;IACzC,IAAIsD,KAAK,GAAG/C,QAAQ,CAACC,aAAa,CAAC,mCAAmC,CAAC;IACvE,MAAM+C,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGF,KAAK,CAACG,UAAU,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,WAAW,CAACK,KAAK,CAAC;IACtF,CAAC;IACD,IAAI,CAACA,KAAK,EAAE;MACRA,KAAK,GAAG/C,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MACvC6C,KAAK,CAACI,EAAE,GAAG,kCAAkC;MAC7CJ,KAAK,CAACK,IAAI,GAAG,MAAM;MACnBL,KAAK,CAACM,MAAM,GAAG,IAAI;MACnBN,KAAK,CAAC4B,QAAQ,GAAG,IAAI;MACrB3E,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC2C,KAAK,CAAC;MAChCA,KAAK,CAACpC,gBAAgB,CAAC,QAAQ,EAAG2C,EAAE,IAAK;QACrC,MAAMsB,MAAM,GAAG,EAAE;QACjB;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,KAAK,CAACS,KAAK,CAACsB,MAAM,EAAED,CAAC,EAAE,EAAE;UACzC,MAAMtB,IAAI,GAAGR,KAAK,CAACS,KAAK,CAACqB,CAAC,CAAC;UAC3B,IAAIpB,MAAM,GAAG,MAAM;UACnB,IAAIF,IAAI,CAACH,IAAI,KAAK,WAAW,EAAE;YAC3BK,MAAM,GAAG,KAAK;UAClB,CAAC,MACI,IAAIF,IAAI,CAACH,IAAI,KAAK,WAAW,EAAE;YAChCK,MAAM,GAAG,KAAK;UAClB;UACAmB,MAAM,CAACG,IAAI,CAAC;YACRZ,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACd,IAAI,CAAC;YAClCE,MAAM,EAAEA;UACZ,CAAC,CAAC;QACN;QACAjE,OAAO,CAAC;UAAEoF;QAAO,CAAC,CAAC;QACnB5B,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;MACFD,KAAK,CAACpC,gBAAgB,CAAC,QAAQ,EAAG2C,EAAE,IAAK;QACrC7D,MAAM,CAAC,IAAIX,kBAAkB,CAAC,2BAA2B,CAAC,CAAC;QAC3DkE,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN;IACAD,KAAK,CAACuB,MAAM,GAAG,SAAS;IACxBvB,KAAK,CAAC2B,KAAK,CAAC,CAAC;EACjB;EACAlC,eAAeA,CAACF,KAAK,EAAEnD,OAAO,EAAE;IAC5B,OAAO,IAAIG,OAAO,CAAC,CAACE,OAAO,EAAEC,MAAM,KAAK;MACpC,MAAMkE,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/B,MAAMH,MAAM,GAAGnB,KAAK,CAACc,IAAI,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACvC,IAAI7E,OAAO,CAACuE,UAAU,KAAK,KAAK,EAAE;QAC9BlE,OAAO,CAAC;UACJ2E,OAAO,EAAEC,GAAG,CAACC,eAAe,CAAC/B,KAAK,CAAC;UACnCmB,MAAM,EAAEA,MAAM;UACduB,KAAK,EAAE;QACX,CAAC,CAAC;MACN,CAAC,MACI;QACDrB,MAAM,CAACO,aAAa,CAAC5B,KAAK,CAAC;QAC3BqB,MAAM,CAACsB,SAAS,GAAG,MAAM;UACrB,MAAMC,CAAC,GAAGvB,MAAM,CAACG,MAAM;UACvB,IAAI3E,OAAO,CAACuE,UAAU,KAAK,SAAS,EAAE;YAClClE,OAAO,CAAC;cACJqE,OAAO,EAAEqB,CAAC;cACVzB,MAAM,EAAEA,MAAM;cACduB,KAAK,EAAE;YACX,CAAC,CAAC;UACN,CAAC,MACI;YACDxF,OAAO,CAAC;cACJyE,YAAY,EAAEiB,CAAC,CAAClB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cAC7BP,MAAM,EAAEA,MAAM;cACduB,KAAK,EAAE;YACX,CAAC,CAAC;UACN;QACJ,CAAC;QACDrB,MAAM,CAACwB,OAAO,GAAGtE,CAAC,IAAI;UAClBpB,MAAM,CAACoB,CAAC,CAAC;QACb,CAAC;MACL;IACJ,CAAC,CAAC;EACN;EACMuE,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhG,iBAAA;MACrB,IAAI,OAAOiG,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACC,WAAW,EAAE;QAC5D,MAAMF,MAAI,CAACG,WAAW,CAAC,+CAA+C,CAAC;MAC3E;MACA,IAAI;QACA;QACA;QACA;QACA,MAAMC,UAAU,SAASC,MAAM,CAACJ,SAAS,CAACC,WAAW,CAACI,KAAK,CAAC;UACxDC,IAAI,EAAE;QACV,CAAC,CAAC;QACF,OAAO;UACHC,MAAM,EAAEJ,UAAU,CAACK,KAAK;UACxBlB,MAAM,EAAE;QACZ,CAAC;MACL,CAAC,CACD,OAAO3B,EAAE,EAAE;QACP,MAAMoC,MAAI,CAACG,WAAW,CAAC,sDAAsD,CAAC;MAClF;IAAC;EACL;EACMO,kBAAkBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3G,iBAAA;MACvB,MAAM2G,MAAI,CAACC,aAAa,CAAC,yBAAyB,CAAC;IAAC;EACxD;EACMC,wBAAwBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA9G,iBAAA;MAC7B,MAAM8G,MAAI,CAACX,WAAW,CAAC,yBAAyB,CAAC;IAAC;EACtD;EACMY,uBAAuBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhH,iBAAA;MAC5B,MAAMgH,MAAI,CAACb,WAAW,CAAC,yBAAyB,CAAC;IAAC;EACtD;AACJ;AACA,MAAMc,MAAM,GAAG,IAAIrH,SAAS,CAAC,CAAC;AAC9B,SAASqH,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
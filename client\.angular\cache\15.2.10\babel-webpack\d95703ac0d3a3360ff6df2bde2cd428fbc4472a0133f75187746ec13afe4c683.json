{"ast": null, "code": "/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\r\n */\nconst CONSTANTS = {\n  /**\r\n   * @define {boolean} Whether this is the client Node.js SDK.\r\n   */\n  NODE_CLIENT: false,\n  /**\r\n   * @define {boolean} Whether this is the Admin Node.js SDK.\r\n   */\n  NODE_ADMIN: false,\n  /**\r\n   * Firebase SDK Version\r\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Throws an error if the provided assertion is falsy\r\n */\nconst assert = function (assertion, message) {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n/**\r\n * Returns an Error object suitable for throwing.\r\n */\nconst assertionError = function (message) {\n  return new Error('Firebase Database (' + CONSTANTS.SDK_VERSION + ') INTERNAL ASSERT FAILED: ' + message);\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst stringToByteArray$1 = function (str) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if ((c & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\r\n * Turns an array of numbers into the string given by the concatenation of the\r\n * characters to which the numbers correspond.\r\n * @param bytes Array of numbers representing characters.\r\n * @return Stringification of the array.\r\n */\nconst byteArrayToString = function (bytes) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 31) << 6 | c2 & 63);\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u = ((c1 & 7) << 18 | (c2 & 63) << 12 | (c3 & 63) << 6 | c4 & 63) - 0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 15) << 12 | (c2 & 63) << 6 | c3 & 63);\n    }\n  }\n  return out.join('');\n};\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\nconst base64 = {\n  /**\r\n   * Maps bytes to characters.\r\n   */\n  byteToCharMap_: null,\n  /**\r\n   * Maps characters to bytes.\r\n   */\n  charToByteMap_: null,\n  /**\r\n   * Maps bytes to websafe characters.\r\n   * @private\r\n   */\n  byteToCharMapWebSafe_: null,\n  /**\r\n   * Maps websafe characters to bytes.\r\n   * @private\r\n   */\n  charToByteMapWebSafe_: null,\n  /**\r\n   * Our default alphabet, shared between\r\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\r\n   */\n  ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n  /**\r\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\r\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n  /**\r\n   * Our websafe alphabet.\r\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n  /**\r\n   * Whether this browser supports the atob and btoa functions. This extension\r\n   * started at Mozilla but is now implemented by many browsers. We use the\r\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\r\n   * but still allowing the standard per-browser compilations.\r\n   *\r\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n  /**\r\n   * Base64-encode an array of bytes.\r\n   *\r\n   * @param input An array of bytes (numbers with\r\n   *     value in [0, 255]) to encode.\r\n   * @param webSafe Boolean indicating we should use the\r\n   *     alternative alphabet.\r\n   * @return The base64 encoded string.\r\n   */\n  encodeByteArray(input, webSafe) {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n    this.init_();\n    const byteToCharMap = webSafe ? this.byteToCharMapWebSafe_ : this.byteToCharMap_;\n    const output = [];\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n      const outByte1 = byte1 >> 2;\n      const outByte2 = (byte1 & 0x03) << 4 | byte2 >> 4;\n      let outByte3 = (byte2 & 0x0f) << 2 | byte3 >> 6;\n      let outByte4 = byte3 & 0x3f;\n      if (!haveByte3) {\n        outByte4 = 64;\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n      output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\n    }\n    return output.join('');\n  },\n  /**\r\n   * Base64-encode a string.\r\n   *\r\n   * @param input A string to encode.\r\n   * @param webSafe If true, we should use the\r\n   *     alternative alphabet.\r\n   * @return The base64 encoded string.\r\n   */\n  encodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray$1(input), webSafe);\n  },\n  /**\r\n   * Base64-decode a string.\r\n   *\r\n   * @param input to decode.\r\n   * @param webSafe True if we should use the\r\n   *     alternative alphabet.\r\n   * @return string representing the decoded value.\r\n   */\n  decodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n  /**\r\n   * Base64-decode a string.\r\n   *\r\n   * In base-64 decoding, groups of four characters are converted into three\r\n   * bytes.  If the encoder did not apply padding, the input length may not\r\n   * be a multiple of 4.\r\n   *\r\n   * In this case, the last group will have fewer than 4 characters, and\r\n   * padding will be inferred.  If the group has one or two characters, it decodes\r\n   * to one byte.  If the group has three characters, it decodes to two bytes.\r\n   *\r\n   * @param input Input to decode.\r\n   * @param webSafe True if we should use the web-safe alphabet.\r\n   * @return bytes representing the decoded value.\r\n   */\n  decodeStringToByteArray(input, webSafe) {\n    this.init_();\n    const charToByteMap = webSafe ? this.charToByteMapWebSafe_ : this.charToByteMap_;\n    const output = [];\n    for (let i = 0; i < input.length;) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n      const outByte1 = byte1 << 2 | byte2 >> 4;\n      output.push(outByte1);\n      if (byte3 !== 64) {\n        const outByte2 = byte2 << 4 & 0xf0 | byte3 >> 2;\n        output.push(outByte2);\n        if (byte4 !== 64) {\n          const outByte3 = byte3 << 6 & 0xc0 | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n    return output;\n  },\n  /**\r\n   * Lazy static initialization function. Called before\r\n   * accessing any of the static map variables.\r\n   * @private\r\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n/**\r\n * An error encountered while decoding base64 string.\r\n */\nclass DecodeBase64StringError extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = 'DecodeBase64StringError';\n  }\n}\n/**\r\n * URL-safe base64 encoding\r\n */\nconst base64Encode = function (str) {\n  const utf8Bytes = stringToByteArray$1(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n/**\r\n * URL-safe base64 encoding (without \".\" padding in the end).\r\n * e.g. Used in JSON Web Token (JWT) parts.\r\n */\nconst base64urlEncodeWithoutPadding = function (str) {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n/**\r\n * URL-safe base64 decoding\r\n *\r\n * NOTE: DO NOT use the global atob() function - it does NOT support the\r\n * base64Url variant encoding.\r\n *\r\n * @param str To be decoded\r\n * @return Decoded result, if possible\r\n */\nconst base64Decode = function (str) {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Do a deep-copy of basic JavaScript Objects or Arrays.\r\n */\nfunction deepCopy(value) {\n  return deepExtend(undefined, value);\n}\n/**\r\n * Copy properties from source to target (recursively allows extension\r\n * of Objects and Arrays).  Scalar values in the target are over-written.\r\n * If target is undefined, an object of the appropriate type will be created\r\n * (and returned).\r\n *\r\n * We recursively copy all child properties of plain Objects in the source- so\r\n * that namespace- like dictionaries are merged.\r\n *\r\n * Note that the target can be a function, in which case the properties in\r\n * the source Object are copied onto it as static properties of the Function.\r\n *\r\n * Note: we don't merge __proto__ to prevent prototype pollution\r\n */\nfunction deepExtend(target, source) {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source;\n      return new Date(dateValue.getTime());\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    target[prop] = deepExtend(target[prop], source[prop]);\n  }\n  return target;\n}\nfunction isValidKey(key) {\n  return key !== '__proto__';\n}\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Polyfill for `globalThis` object.\r\n * @returns the `globalThis` object for the given environment.\r\n * @public\r\n */\nfunction getGlobal() {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\n/**\r\n * Attempt to read defaults from a JSON string provided to\r\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\r\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\r\n * The dots are in parens because certain compilers (Vite?) cannot\r\n * handle seeing that variable in comments.\r\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\r\n */\nconst getDefaultsFromEnvVariable = () => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\nconst getDefaultsFromCookie = () => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n/**\r\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\r\n * (1) if such an object exists as a property of `globalThis`\r\n * (2) if such an object was provided on a shell environment variable\r\n * (3) if such an object exists in a cookie\r\n * @public\r\n */\nconst getDefaults = () => {\n  try {\n    return getDefaultsFromGlobal() || getDefaultsFromEnvVariable() || getDefaultsFromCookie();\n  } catch (e) {\n    /**\r\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\r\n     * to any environment case we have not accounted for. Log to\r\n     * info instead of swallowing so we can find these unknown cases\r\n     * and add paths for them if needed.\r\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n/**\r\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\r\n * for the given product.\r\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\r\n * @public\r\n */\nconst getDefaultEmulatorHost = productName => {\n  var _a, _b;\n  return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName];\n};\n/**\r\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\r\n * for the given product.\r\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\r\n * @public\r\n */\nconst getDefaultEmulatorHostnameAndPort = productName => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n/**\r\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\r\n * @public\r\n */\nconst getDefaultAppConfig = () => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config;\n};\n/**\r\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\r\n * prefixed by \"_\")\r\n * @public\r\n */\nconst getExperimentalSetting = name => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`];\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass Deferred {\n  constructor() {\n    this.reject = () => {};\n    this.resolve = () => {};\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n  /**\r\n   * Our API internals are not promiseified and cannot because our callback APIs have subtle expectations around\r\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\r\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\r\n   */\n  wrapCallback(callback) {\n    return (error, value) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction createMockUserToken(token, projectId) {\n  if (token.uid) {\n    throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n  const payload = Object.assign({\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    }\n  }, token);\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [base64urlEncodeWithoutPadding(JSON.stringify(header)), base64urlEncodeWithoutPadding(JSON.stringify(payload)), signature].join('.');\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns navigator.userAgent string or '' if it's not defined.\r\n * @return user agent string\r\n */\nfunction getUA() {\n  if (typeof navigator !== 'undefined' && typeof navigator['userAgent'] === 'string') {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n/**\r\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\r\n *\r\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\r\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\r\n * wait for a callback.\r\n */\nfunction isMobileCordova() {\n  return typeof window !== 'undefined' &&\n  // @ts-ignore Setting up an broadly applicable index signature for Window\n  // just to deal with this case would probably be a bad idea.\n  !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) && /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA());\n}\n/**\r\n * Detect Node.js.\r\n *\r\n * @return true if Node.js environment is detected or specified.\r\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nfunction isNode() {\n  var _a;\n  const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n  try {\n    return Object.prototype.toString.call(global.process) === '[object process]';\n  } catch (e) {\n    return false;\n  }\n}\n/**\r\n * Detect Browser Environment\r\n */\nfunction isBrowser() {\n  return typeof self === 'object' && self.self === self;\n}\nfunction isBrowserExtension() {\n  const runtime = typeof chrome === 'object' ? chrome.runtime : typeof browser === 'object' ? browser.runtime : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n/**\r\n * Detect React Native.\r\n *\r\n * @return true if ReactNative environment is detected.\r\n */\nfunction isReactNative() {\n  return typeof navigator === 'object' && navigator['product'] === 'ReactNative';\n}\n/** Detects Electron apps. */\nfunction isElectron() {\n  return getUA().indexOf('Electron/') >= 0;\n}\n/** Detects Internet Explorer. */\nfunction isIE() {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n/** Detects Universal Windows Platform apps. */\nfunction isUWP() {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n/**\r\n * Detect whether the current SDK build is the Node version.\r\n *\r\n * @return true if it's the Node SDK build.\r\n */\nfunction isNodeSdk() {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n/** Returns true if we are running in Safari. */\nfunction isSafari() {\n  return !isNode() && navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');\n}\n/**\r\n * This method checks if indexedDB is supported by current browser/service worker context\r\n * @return true if indexedDB is supported by current browser/service worker context\r\n */\nfunction isIndexedDBAvailable() {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n/**\r\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\r\n * if errors occur during the database open operation.\r\n *\r\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\r\n * private browsing)\r\n */\nfunction validateIndexedDBOpenable() {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist = true;\n      const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n      request.onerror = () => {\n        var _a;\n        reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n/**\r\n *\r\n * This method checks whether cookie is enabled within current browser\r\n * @return true if cookie is enabled within current browser\r\n */\nfunction areCookiesEnabled() {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * @fileoverview Standardized Firebase Error.\r\n *\r\n * Usage:\r\n *\r\n *   // Typescript string literals for type-safe codes\r\n *   type Err =\r\n *     'unknown' |\r\n *     'object-not-found'\r\n *     ;\r\n *\r\n *   // Closure enum for type-safe error codes\r\n *   // at-enum {string}\r\n *   var Err = {\r\n *     UNKNOWN: 'unknown',\r\n *     OBJECT_NOT_FOUND: 'object-not-found',\r\n *   }\r\n *\r\n *   let errors: Map<Err, string> = {\r\n *     'generic-error': \"Unknown error\",\r\n *     'file-not-found': \"Could not find file: {$file}\",\r\n *   };\r\n *\r\n *   // Type-safe function - must pass a valid error code as param.\r\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\r\n *\r\n *   ...\r\n *   throw error.create(Err.GENERIC);\r\n *   ...\r\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\r\n *   ...\r\n *   // Service: Could not file file: foo.txt (service/file-not-found).\r\n *\r\n *   catch (e) {\r\n *     assert(e.message === \"Could not find file: foo.txt.\");\r\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\r\n *       console.log(\"Could not read file: \" + e['file']);\r\n *     }\r\n *   }\r\n */\nconst ERROR_NAME = 'FirebaseError';\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nclass FirebaseError extends Error {\n  constructor( /** The error code for this error. */\n  code, message, /** Custom data for this error. */\n  customData) {\n    super(message);\n    this.code = code;\n    this.customData = customData;\n    /** The custom name for all FirebaseErrors. */\n    this.name = ERROR_NAME;\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\nclass ErrorFactory {\n  constructor(service, serviceName, errors) {\n    this.service = service;\n    this.serviceName = serviceName;\n    this.errors = errors;\n  }\n  create(code, ...data) {\n    const customData = data[0] || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n    return error;\n  }\n}\nfunction replaceTemplate(template, data) {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Evaluates a JSON string into a javascript object.\r\n *\r\n * @param {string} str A string containing JSON.\r\n * @return {*} The javascript object representing the specified JSON.\r\n */\nfunction jsonEval(str) {\n  return JSON.parse(str);\n}\n/**\r\n * Returns JSON representing a javascript object.\r\n * @param {*} data Javascript object to be stringified.\r\n * @return {string} The JSON contents of the object.\r\n */\nfunction stringify(data) {\n  return JSON.stringify(data);\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Decodes a Firebase auth. token into constituent parts.\r\n *\r\n * Notes:\r\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\nconst decode = function (token) {\n  let header = {},\n    claims = {},\n    data = {},\n    signature = '';\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '');\n    claims = jsonEval(base64Decode(parts[1]) || '');\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n/**\r\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\r\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\r\n *\r\n * Notes:\r\n * - May return a false negative if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\nconst isValidTimestamp = function (token) {\n  const claims = decode(token).claims;\n  const now = Math.floor(new Date().getTime() / 1000);\n  let validSince = 0,\n    validUntil = 0;\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'];\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'];\n    }\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'];\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n  return !!now && !!validSince && !!validUntil && now >= validSince && now <= validUntil;\n};\n/**\r\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\r\n *\r\n * Notes:\r\n * - May return null if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\nconst issuedAtTime = function (token) {\n  const claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'];\n  }\n  return null;\n};\n/**\r\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\r\n *\r\n * Notes:\r\n * - May return a false negative if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\nconst isValidFormat = function (token) {\n  const decoded = decode(token),\n    claims = decoded.claims;\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n/**\r\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\r\n *\r\n * Notes:\r\n * - May return a false negative if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\nconst isAdmin = function (token) {\n  const claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction contains(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction safeGet(obj, key) {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\nfunction isEmpty(obj) {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction map(obj, fn, contextObj) {\n  const res = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res;\n}\n/**\r\n * Deep equal two objects. Support Arrays and Objects.\r\n */\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n    const aProp = a[k];\n    const bProp = b[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isObject(thing) {\n  return thing !== null && typeof thing === 'object';\n}\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\r\n * @internal\r\n */\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\n  const deferredPromise = new Deferred();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\r\n * params object (e.g. {arg: 'val', arg2: 'val2'})\r\n * Note: You must prepend it with ? when adding it to a URL.\r\n */\nfunction querystring(querystringParams) {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n/**\r\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\r\n * (e.g. {arg: 'val', arg2: 'val2'})\r\n */\nfunction querystringDecode(querystring) {\n  const obj = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n/**\r\n * Extract the query string part of a URL, including the leading question mark (if present).\r\n */\nfunction extractQuerystring(url) {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * @fileoverview SHA-1 cryptographic hash.\r\n * Variable names follow the notation in FIPS PUB 180-3:\r\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\r\n *\r\n * Usage:\r\n *   var sha1 = new sha1();\r\n *   sha1.update(bytes);\r\n *   var hash = sha1.digest();\r\n *\r\n * Performance:\r\n *   Chrome 23:   ~400 Mbit/s\r\n *   Firefox 16:  ~250 Mbit/s\r\n *\r\n */\n/**\r\n * SHA-1 cryptographic hash constructor.\r\n *\r\n * The properties declared here are discussed in the above algorithm document.\r\n * @constructor\r\n * @final\r\n * @struct\r\n */\nclass Sha1 {\n  constructor() {\n    /**\r\n     * Holds the previous values of accumulated variables a-e in the compress_\r\n     * function.\r\n     * @private\r\n     */\n    this.chain_ = [];\n    /**\r\n     * A buffer holding the partially computed hash result.\r\n     * @private\r\n     */\n    this.buf_ = [];\n    /**\r\n     * An array of 80 bytes, each a part of the message to be hashed.  Referred to\r\n     * as the message schedule in the docs.\r\n     * @private\r\n     */\n    this.W_ = [];\n    /**\r\n     * Contains data needed to pad messages less than 64 bytes.\r\n     * @private\r\n     */\n    this.pad_ = [];\n    /**\r\n     * @private {number}\r\n     */\n    this.inbuf_ = 0;\n    /**\r\n     * @private {number}\r\n     */\n    this.total_ = 0;\n    this.blockSize = 512 / 8;\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n    this.reset();\n  }\n  reset() {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n  /**\r\n   * Internal compress helper function.\r\n   * @param buf Block to compress.\r\n   * @param offset Offset of the block in the buffer.\r\n   * @private\r\n   */\n  compress_(buf, offset) {\n    if (!offset) {\n      offset = 0;\n    }\n    const W = this.W_;\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] = buf.charCodeAt(offset) << 24 | buf.charCodeAt(offset + 1) << 16 | buf.charCodeAt(offset + 2) << 8 | buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] = buf[offset] << 24 | buf[offset + 1] << 16 | buf[offset + 2] << 8 | buf[offset + 3];\n        offset += 4;\n      }\n    }\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = (t << 1 | t >>> 31) & 0xffffffff;\n    }\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ b & (c ^ d);\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = b & c | d & (b | c);\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n      const t = (a << 5 | a >>> 27) + f + e + k + W[i] & 0xffffffff;\n      e = d;\n      d = c;\n      c = (b << 30 | b >>> 2) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n    this.chain_[0] = this.chain_[0] + a & 0xffffffff;\n    this.chain_[1] = this.chain_[1] + b & 0xffffffff;\n    this.chain_[2] = this.chain_[2] + c & 0xffffffff;\n    this.chain_[3] = this.chain_[3] + d & 0xffffffff;\n    this.chain_[4] = this.chain_[4] + e & 0xffffffff;\n  }\n  update(bytes, length) {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n    if (length === undefined) {\n      length = bytes.length;\n    }\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n  /** @override */\n  digest() {\n    const digest = [];\n    let totalBits = this.total_ * 8;\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n\n    this.compress_(this.buf_);\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = this.chain_[i] >> j & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n\n/**\r\n * Helper to make a Subscribe function (just like Promise helps make a\r\n * Thenable).\r\n *\r\n * @param executor Function which can make calls to a single Observer\r\n *     as a proxy.\r\n * @param onNoObservers Callback when count of Observers goes to zero.\r\n */\nfunction createSubscribe(executor, onNoObservers) {\n  const proxy = new ObserverProxy(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n/**\r\n * Implement fan-out for any number of Observers attached via a subscribe\r\n * function.\r\n */\nclass ObserverProxy {\n  /**\r\n   * @param executor Function which can make calls to a single Observer\r\n   *     as a proxy.\r\n   * @param onNoObservers Callback when count of Observers goes to zero.\r\n   */\n  constructor(executor, onNoObservers) {\n    this.observers = [];\n    this.unsubscribes = [];\n    this.observerCount = 0;\n    // Micro-task scheduling by calling task.then().\n    this.task = Promise.resolve();\n    this.finalized = false;\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task.then(() => {\n      executor(this);\n    }).catch(e => {\n      this.error(e);\n    });\n  }\n  next(value) {\n    this.forEachObserver(observer => {\n      observer.next(value);\n    });\n  }\n  error(error) {\n    this.forEachObserver(observer => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n  complete() {\n    this.forEachObserver(observer => {\n      observer.complete();\n    });\n    this.close();\n  }\n  /**\r\n   * Subscribe function that can be used to add an Observer to the fan-out list.\r\n   *\r\n   * - We require that no event is sent to a subscriber sychronously to their\r\n   *   call to subscribe().\r\n   */\n  subscribe(nextOrObserver, error, complete) {\n    let observer;\n    if (nextOrObserver === undefined && error === undefined && complete === undefined) {\n      throw new Error('Missing Observer.');\n    }\n    // Assemble an Observer object when passed as callback functions.\n    if (implementsAnyMethods(nextOrObserver, ['next', 'error', 'complete'])) {\n      observer = nextOrObserver;\n    } else {\n      observer = {\n        next: nextOrObserver,\n        error,\n        complete\n      };\n    }\n    if (observer.next === undefined) {\n      observer.next = noop;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop;\n    }\n    const unsub = this.unsubscribeOne.bind(this, this.observers.length);\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n    this.observers.push(observer);\n    return unsub;\n  }\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  unsubscribeOne(i) {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n    delete this.observers[i];\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n  forEachObserver(fn) {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  sendOne(i, fn) {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n  close(err) {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(fn, onError) {\n  return (...args) => {\n    Promise.resolve(true).then(() => {\n      fn(...args);\n    }).catch(error => {\n      if (onError) {\n        onError(error);\n      }\n    });\n  };\n}\n/**\r\n * Return true if the object passed in implements any of the named methods.\r\n */\nfunction implementsAnyMethods(obj, methods) {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n  return false;\n}\nfunction noop() {\n  // do nothing\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Check to make sure the appropriate number of arguments are provided for a public function.\r\n * Throws an error if it fails.\r\n *\r\n * @param fnName The function name\r\n * @param minCount The minimum number of arguments to allow for the function call\r\n * @param maxCount The maximum number of argument to allow for the function call\r\n * @param argCount The actual number of arguments provided.\r\n */\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error = fnName + ' failed: Was called with ' + argCount + (argCount === 1 ? ' argument.' : ' arguments.') + ' Expects ' + argError + '.';\n    throw new Error(error);\n  }\n};\n/**\r\n * Generates a string to prefix an error message about failed argument validation\r\n *\r\n * @param fnName The function name\r\n * @param argName The name of the argument\r\n * @return The prefix to add to the error thrown for validation.\r\n */\nfunction errorPrefix(fnName, argName) {\n  return `${fnName} failed: ${argName} argument `;\n}\n/**\r\n * @param fnName\r\n * @param argumentNumber\r\n * @param namespace\r\n * @param optional\r\n */\nfunction validateNamespace(fnName, namespace, optional) {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\n  }\n}\nfunction validateCallback(fnName, argumentName,\n// eslint-disable-next-line @typescript-eslint/ban-types\ncallback, optional) {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\n  }\n}\nfunction validateContextObject(fnName, argumentName, context, optional) {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in Javascript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n/**\r\n * @param {string} str\r\n * @return {Array}\r\n */\nconst stringToByteArray = function (str) {\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if (c < 65536) {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\r\n * Calculate length without actually converting; useful for doing cheaper validation.\r\n * @param {string} str\r\n * @return {number}\r\n */\nconst stringLength = function (str) {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Copied from https://stackoverflow.com/a/2117523\r\n * Generates a new uuid.\r\n * @public\r\n */\nconst uuidv4 = function () {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = Math.random() * 16 | 0,\n      v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The amount of milliseconds to exponentially increase.\r\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n/**\r\n * The factor to backoff by.\r\n * Should be a number greater than 1.\r\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n/**\r\n * The maximum milliseconds to increase to.\r\n *\r\n * <p>Visible for testing\r\n */\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n/**\r\n * The percentage of backoff time to randomize by.\r\n * See\r\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\r\n * for context.\r\n *\r\n * <p>Visible for testing\r\n */\nconst RANDOM_FACTOR = 0.5;\n/**\r\n * Based on the backoff method from\r\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\r\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\r\n */\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n  // A fraction of the backoff value to add/subtract.\n  // Deviation: changes multiplication order to improve readability.\n  RANDOM_FACTOR * currBaseValue * (\n  // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n  // if we add or subtract.\n  Math.random() - 0.5) * 2);\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Provide English ordinal letters after a number\r\n */\nfunction ordinal(i) {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\nfunction indicator(i) {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction getModularInstance(service) {\n  if (service && service._delegate) {\n    return service._delegate;\n  } else {\n    return service;\n  }\n}\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isUWP, isValidFormat, isValidTimestamp, issuedAtTime, jsonEval, map, ordinal, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, uuidv4, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };", "map": {"version": 3, "names": ["CONSTANTS", "NODE_CLIENT", "NODE_ADMIN", "SDK_VERSION", "assert", "assertion", "message", "assertionError", "Error", "stringToByteArray$1", "str", "out", "p", "i", "length", "c", "charCodeAt", "byteArrayToString", "bytes", "pos", "c1", "String", "fromCharCode", "c2", "c3", "c4", "u", "join", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte1", "outByte2", "outByte3", "outByte4", "push", "encodeString", "btoa", "decodeString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "haveByte4", "byte4", "DecodeBase64StringError", "constructor", "arguments", "name", "base64Encode", "utf8Bytes", "base64urlEncodeWithoutPadding", "replace", "base64Decode", "e", "console", "error", "deepCopy", "value", "deepExtend", "undefined", "target", "source", "Object", "Date", "dateValue", "getTime", "prop", "hasOwnProperty", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "getGlobal", "self", "window", "global", "getDefaultsFromGlobal", "__FIREBASE_DEFAULTS__", "getDefaultsFromEnvVariable", "process", "env", "defaultsJsonString", "JSON", "parse", "getDefaultsFromCookie", "document", "match", "cookie", "decoded", "getDefaults", "info", "getDefaultEmulatorHost", "productName", "_a", "_b", "emulatorHosts", "getDefaultEmulatorHostnameAndPort", "host", "separatorIndex", "lastIndexOf", "port", "parseInt", "substring", "getDefaultAppConfig", "config", "getExperimentalSetting", "Deferred", "reject", "resolve", "promise", "Promise", "wrapCallback", "callback", "catch", "createMockUserToken", "token", "projectId", "uid", "header", "alg", "type", "project", "iat", "sub", "user_id", "payload", "assign", "iss", "aud", "exp", "auth_time", "firebase", "sign_in_provider", "identities", "signature", "stringify", "getUA", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "isNode", "forceEnvironment", "prototype", "toString", "call", "<PERSON><PERSON><PERSON><PERSON>", "isBrowserExtension", "runtime", "chrome", "browser", "id", "isReactNative", "isElectron", "indexOf", "isIE", "ua", "isUWP", "isNodeSdk", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "includes", "isIndexedDBAvailable", "indexedDB", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "request", "open", "onsuccess", "result", "close", "deleteDatabase", "onupgradeneeded", "onerror", "areCookiesEnabled", "cookieEnabled", "ERROR_NAME", "FirebaseError", "code", "customData", "setPrototypeOf", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "fullMessage", "PATTERN", "_", "jsonEval", "decode", "claims", "parts", "split", "isValidTimestamp", "now", "Math", "floor", "validSince", "validUntil", "issuedAtTime", "isValidFormat", "isAdmin", "contains", "obj", "safeGet", "isEmpty", "map", "fn", "contextObj", "res", "deepEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "k", "aProp", "bProp", "isObject", "thing", "promiseWithTimeout", "timeInMS", "deferred<PERSON><PERSON><PERSON>", "setTimeout", "then", "querystring", "querystringParams", "params", "entries", "for<PERSON>ach", "arrayVal", "encodeURIComponent", "querystringDecode", "tokens", "decodeURIComponent", "extractQuerystring", "url", "queryStart", "fragmentStart", "Sha1", "chain_", "buf_", "W_", "pad_", "inbuf_", "total_", "blockSize", "reset", "compress_", "buf", "offset", "W", "t", "d", "f", "update", "lengthMinusBlock", "n", "inbuf", "digest", "totalBits", "j", "createSubscribe", "executor", "onNoObservers", "proxy", "ObserverProxy", "subscribe", "bind", "observers", "unsubscribes", "observerCount", "task", "finalized", "next", "forEachObserver", "observer", "complete", "nextOrObserver", "implementsAnyMethods", "noop", "unsub", "unsubscribeOne", "finalError", "sendOne", "err", "async", "onError", "args", "methods", "method", "validateArgCount", "fnName", "minCount", "maxCount", "argCount", "arg<PERSON><PERSON>r", "errorPrefix", "argName", "validateNamespace", "namespace", "optional", "validate<PERSON><PERSON>back", "argumentName", "validateContextObject", "context", "stringToByteArray", "high", "low", "stringLength", "uuidv4", "r", "random", "v", "DEFAULT_INTERVAL_MILLIS", "DEFAULT_BACKOFF_FACTOR", "MAX_VALUE_MILLIS", "RANDOM_FACTOR", "calculateBackoffMillis", "backoffCount", "<PERSON><PERSON><PERSON><PERSON>", "backoffFactor", "currBaseValue", "pow", "randomWait", "round", "min", "ordinal", "Number", "isFinite", "indicator", "abs", "cent", "dec", "getModularInstance", "_delegate"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/node_modules/@firebase/util/dist/index.esm2017.js"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\r\n */\r\nconst CONSTANTS = {\r\n    /**\r\n     * @define {boolean} Whether this is the client Node.js SDK.\r\n     */\r\n    NODE_CLIENT: false,\r\n    /**\r\n     * @define {boolean} Whether this is the Admin Node.js SDK.\r\n     */\r\n    NODE_ADMIN: false,\r\n    /**\r\n     * Firebase SDK Version\r\n     */\r\n    SDK_VERSION: '${JSCORE_VERSION}'\r\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Throws an error if the provided assertion is falsy\r\n */\r\nconst assert = function (assertion, message) {\r\n    if (!assertion) {\r\n        throw assertionError(message);\r\n    }\r\n};\r\n/**\r\n * Returns an Error object suitable for throwing.\r\n */\r\nconst assertionError = function (message) {\r\n    return new Error('Firebase Database (' +\r\n        CONSTANTS.SDK_VERSION +\r\n        ') INTERNAL ASSERT FAILED: ' +\r\n        message);\r\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst stringToByteArray$1 = function (str) {\r\n    // TODO(user): Use native implementations if/when available\r\n    const out = [];\r\n    let p = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n        let c = str.charCodeAt(i);\r\n        if (c < 128) {\r\n            out[p++] = c;\r\n        }\r\n        else if (c < 2048) {\r\n            out[p++] = (c >> 6) | 192;\r\n            out[p++] = (c & 63) | 128;\r\n        }\r\n        else if ((c & 0xfc00) === 0xd800 &&\r\n            i + 1 < str.length &&\r\n            (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\r\n            // Surrogate Pair\r\n            c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\r\n            out[p++] = (c >> 18) | 240;\r\n            out[p++] = ((c >> 12) & 63) | 128;\r\n            out[p++] = ((c >> 6) & 63) | 128;\r\n            out[p++] = (c & 63) | 128;\r\n        }\r\n        else {\r\n            out[p++] = (c >> 12) | 224;\r\n            out[p++] = ((c >> 6) & 63) | 128;\r\n            out[p++] = (c & 63) | 128;\r\n        }\r\n    }\r\n    return out;\r\n};\r\n/**\r\n * Turns an array of numbers into the string given by the concatenation of the\r\n * characters to which the numbers correspond.\r\n * @param bytes Array of numbers representing characters.\r\n * @return Stringification of the array.\r\n */\r\nconst byteArrayToString = function (bytes) {\r\n    // TODO(user): Use native implementations if/when available\r\n    const out = [];\r\n    let pos = 0, c = 0;\r\n    while (pos < bytes.length) {\r\n        const c1 = bytes[pos++];\r\n        if (c1 < 128) {\r\n            out[c++] = String.fromCharCode(c1);\r\n        }\r\n        else if (c1 > 191 && c1 < 224) {\r\n            const c2 = bytes[pos++];\r\n            out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\r\n        }\r\n        else if (c1 > 239 && c1 < 365) {\r\n            // Surrogate Pair\r\n            const c2 = bytes[pos++];\r\n            const c3 = bytes[pos++];\r\n            const c4 = bytes[pos++];\r\n            const u = (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\r\n                0x10000;\r\n            out[c++] = String.fromCharCode(0xd800 + (u >> 10));\r\n            out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\r\n        }\r\n        else {\r\n            const c2 = bytes[pos++];\r\n            const c3 = bytes[pos++];\r\n            out[c++] = String.fromCharCode(((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\r\n        }\r\n    }\r\n    return out.join('');\r\n};\r\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\r\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\r\n// Static lookup maps, lazily populated by init_()\r\nconst base64 = {\r\n    /**\r\n     * Maps bytes to characters.\r\n     */\r\n    byteToCharMap_: null,\r\n    /**\r\n     * Maps characters to bytes.\r\n     */\r\n    charToByteMap_: null,\r\n    /**\r\n     * Maps bytes to websafe characters.\r\n     * @private\r\n     */\r\n    byteToCharMapWebSafe_: null,\r\n    /**\r\n     * Maps websafe characters to bytes.\r\n     * @private\r\n     */\r\n    charToByteMapWebSafe_: null,\r\n    /**\r\n     * Our default alphabet, shared between\r\n     * ENCODED_VALS and ENCODED_VALS_WEBSAFE\r\n     */\r\n    ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\r\n    /**\r\n     * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\r\n     */\r\n    get ENCODED_VALS() {\r\n        return this.ENCODED_VALS_BASE + '+/=';\r\n    },\r\n    /**\r\n     * Our websafe alphabet.\r\n     */\r\n    get ENCODED_VALS_WEBSAFE() {\r\n        return this.ENCODED_VALS_BASE + '-_.';\r\n    },\r\n    /**\r\n     * Whether this browser supports the atob and btoa functions. This extension\r\n     * started at Mozilla but is now implemented by many browsers. We use the\r\n     * ASSUME_* variables to avoid pulling in the full useragent detection library\r\n     * but still allowing the standard per-browser compilations.\r\n     *\r\n     */\r\n    HAS_NATIVE_SUPPORT: typeof atob === 'function',\r\n    /**\r\n     * Base64-encode an array of bytes.\r\n     *\r\n     * @param input An array of bytes (numbers with\r\n     *     value in [0, 255]) to encode.\r\n     * @param webSafe Boolean indicating we should use the\r\n     *     alternative alphabet.\r\n     * @return The base64 encoded string.\r\n     */\r\n    encodeByteArray(input, webSafe) {\r\n        if (!Array.isArray(input)) {\r\n            throw Error('encodeByteArray takes an array as a parameter');\r\n        }\r\n        this.init_();\r\n        const byteToCharMap = webSafe\r\n            ? this.byteToCharMapWebSafe_\r\n            : this.byteToCharMap_;\r\n        const output = [];\r\n        for (let i = 0; i < input.length; i += 3) {\r\n            const byte1 = input[i];\r\n            const haveByte2 = i + 1 < input.length;\r\n            const byte2 = haveByte2 ? input[i + 1] : 0;\r\n            const haveByte3 = i + 2 < input.length;\r\n            const byte3 = haveByte3 ? input[i + 2] : 0;\r\n            const outByte1 = byte1 >> 2;\r\n            const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\r\n            let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\r\n            let outByte4 = byte3 & 0x3f;\r\n            if (!haveByte3) {\r\n                outByte4 = 64;\r\n                if (!haveByte2) {\r\n                    outByte3 = 64;\r\n                }\r\n            }\r\n            output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\r\n        }\r\n        return output.join('');\r\n    },\r\n    /**\r\n     * Base64-encode a string.\r\n     *\r\n     * @param input A string to encode.\r\n     * @param webSafe If true, we should use the\r\n     *     alternative alphabet.\r\n     * @return The base64 encoded string.\r\n     */\r\n    encodeString(input, webSafe) {\r\n        // Shortcut for Mozilla browsers that implement\r\n        // a native base64 encoder in the form of \"btoa/atob\"\r\n        if (this.HAS_NATIVE_SUPPORT && !webSafe) {\r\n            return btoa(input);\r\n        }\r\n        return this.encodeByteArray(stringToByteArray$1(input), webSafe);\r\n    },\r\n    /**\r\n     * Base64-decode a string.\r\n     *\r\n     * @param input to decode.\r\n     * @param webSafe True if we should use the\r\n     *     alternative alphabet.\r\n     * @return string representing the decoded value.\r\n     */\r\n    decodeString(input, webSafe) {\r\n        // Shortcut for Mozilla browsers that implement\r\n        // a native base64 encoder in the form of \"btoa/atob\"\r\n        if (this.HAS_NATIVE_SUPPORT && !webSafe) {\r\n            return atob(input);\r\n        }\r\n        return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\r\n    },\r\n    /**\r\n     * Base64-decode a string.\r\n     *\r\n     * In base-64 decoding, groups of four characters are converted into three\r\n     * bytes.  If the encoder did not apply padding, the input length may not\r\n     * be a multiple of 4.\r\n     *\r\n     * In this case, the last group will have fewer than 4 characters, and\r\n     * padding will be inferred.  If the group has one or two characters, it decodes\r\n     * to one byte.  If the group has three characters, it decodes to two bytes.\r\n     *\r\n     * @param input Input to decode.\r\n     * @param webSafe True if we should use the web-safe alphabet.\r\n     * @return bytes representing the decoded value.\r\n     */\r\n    decodeStringToByteArray(input, webSafe) {\r\n        this.init_();\r\n        const charToByteMap = webSafe\r\n            ? this.charToByteMapWebSafe_\r\n            : this.charToByteMap_;\r\n        const output = [];\r\n        for (let i = 0; i < input.length;) {\r\n            const byte1 = charToByteMap[input.charAt(i++)];\r\n            const haveByte2 = i < input.length;\r\n            const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\r\n            ++i;\r\n            const haveByte3 = i < input.length;\r\n            const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\r\n            ++i;\r\n            const haveByte4 = i < input.length;\r\n            const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\r\n            ++i;\r\n            if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\r\n                throw new DecodeBase64StringError();\r\n            }\r\n            const outByte1 = (byte1 << 2) | (byte2 >> 4);\r\n            output.push(outByte1);\r\n            if (byte3 !== 64) {\r\n                const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\r\n                output.push(outByte2);\r\n                if (byte4 !== 64) {\r\n                    const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\r\n                    output.push(outByte3);\r\n                }\r\n            }\r\n        }\r\n        return output;\r\n    },\r\n    /**\r\n     * Lazy static initialization function. Called before\r\n     * accessing any of the static map variables.\r\n     * @private\r\n     */\r\n    init_() {\r\n        if (!this.byteToCharMap_) {\r\n            this.byteToCharMap_ = {};\r\n            this.charToByteMap_ = {};\r\n            this.byteToCharMapWebSafe_ = {};\r\n            this.charToByteMapWebSafe_ = {};\r\n            // We want quick mappings back and forth, so we precompute two maps.\r\n            for (let i = 0; i < this.ENCODED_VALS.length; i++) {\r\n                this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\r\n                this.charToByteMap_[this.byteToCharMap_[i]] = i;\r\n                this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\r\n                this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\r\n                // Be forgiving when decoding and correctly decode both encodings.\r\n                if (i >= this.ENCODED_VALS_BASE.length) {\r\n                    this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\r\n                    this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n/**\r\n * An error encountered while decoding base64 string.\r\n */\r\nclass DecodeBase64StringError extends Error {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = 'DecodeBase64StringError';\r\n    }\r\n}\r\n/**\r\n * URL-safe base64 encoding\r\n */\r\nconst base64Encode = function (str) {\r\n    const utf8Bytes = stringToByteArray$1(str);\r\n    return base64.encodeByteArray(utf8Bytes, true);\r\n};\r\n/**\r\n * URL-safe base64 encoding (without \".\" padding in the end).\r\n * e.g. Used in JSON Web Token (JWT) parts.\r\n */\r\nconst base64urlEncodeWithoutPadding = function (str) {\r\n    // Use base64url encoding and remove padding in the end (dot characters).\r\n    return base64Encode(str).replace(/\\./g, '');\r\n};\r\n/**\r\n * URL-safe base64 decoding\r\n *\r\n * NOTE: DO NOT use the global atob() function - it does NOT support the\r\n * base64Url variant encoding.\r\n *\r\n * @param str To be decoded\r\n * @return Decoded result, if possible\r\n */\r\nconst base64Decode = function (str) {\r\n    try {\r\n        return base64.decodeString(str, true);\r\n    }\r\n    catch (e) {\r\n        console.error('base64Decode failed: ', e);\r\n    }\r\n    return null;\r\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Do a deep-copy of basic JavaScript Objects or Arrays.\r\n */\r\nfunction deepCopy(value) {\r\n    return deepExtend(undefined, value);\r\n}\r\n/**\r\n * Copy properties from source to target (recursively allows extension\r\n * of Objects and Arrays).  Scalar values in the target are over-written.\r\n * If target is undefined, an object of the appropriate type will be created\r\n * (and returned).\r\n *\r\n * We recursively copy all child properties of plain Objects in the source- so\r\n * that namespace- like dictionaries are merged.\r\n *\r\n * Note that the target can be a function, in which case the properties in\r\n * the source Object are copied onto it as static properties of the Function.\r\n *\r\n * Note: we don't merge __proto__ to prevent prototype pollution\r\n */\r\nfunction deepExtend(target, source) {\r\n    if (!(source instanceof Object)) {\r\n        return source;\r\n    }\r\n    switch (source.constructor) {\r\n        case Date:\r\n            // Treat Dates like scalars; if the target date object had any child\r\n            // properties - they will be lost!\r\n            const dateValue = source;\r\n            return new Date(dateValue.getTime());\r\n        case Object:\r\n            if (target === undefined) {\r\n                target = {};\r\n            }\r\n            break;\r\n        case Array:\r\n            // Always copy the array source and overwrite the target.\r\n            target = [];\r\n            break;\r\n        default:\r\n            // Not a plain Object - treat it as a scalar.\r\n            return source;\r\n    }\r\n    for (const prop in source) {\r\n        // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\r\n        if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\r\n            continue;\r\n        }\r\n        target[prop] = deepExtend(target[prop], source[prop]);\r\n    }\r\n    return target;\r\n}\r\nfunction isValidKey(key) {\r\n    return key !== '__proto__';\r\n}\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Polyfill for `globalThis` object.\r\n * @returns the `globalThis` object for the given environment.\r\n * @public\r\n */\r\nfunction getGlobal() {\r\n    if (typeof self !== 'undefined') {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined') {\r\n        return window;\r\n    }\r\n    if (typeof global !== 'undefined') {\r\n        return global;\r\n    }\r\n    throw new Error('Unable to locate global object.');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\r\n/**\r\n * Attempt to read defaults from a JSON string provided to\r\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\r\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\r\n * The dots are in parens because certain compilers (Vite?) cannot\r\n * handle seeing that variable in comments.\r\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\r\n */\r\nconst getDefaultsFromEnvVariable = () => {\r\n    if (typeof process === 'undefined' || typeof process.env === 'undefined') {\r\n        return;\r\n    }\r\n    const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\r\n    if (defaultsJsonString) {\r\n        return JSON.parse(defaultsJsonString);\r\n    }\r\n};\r\nconst getDefaultsFromCookie = () => {\r\n    if (typeof document === 'undefined') {\r\n        return;\r\n    }\r\n    let match;\r\n    try {\r\n        match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\r\n    }\r\n    catch (e) {\r\n        // Some environments such as Angular Universal SSR have a\r\n        // `document` object but error on accessing `document.cookie`.\r\n        return;\r\n    }\r\n    const decoded = match && base64Decode(match[1]);\r\n    return decoded && JSON.parse(decoded);\r\n};\r\n/**\r\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\r\n * (1) if such an object exists as a property of `globalThis`\r\n * (2) if such an object was provided on a shell environment variable\r\n * (3) if such an object exists in a cookie\r\n * @public\r\n */\r\nconst getDefaults = () => {\r\n    try {\r\n        return (getDefaultsFromGlobal() ||\r\n            getDefaultsFromEnvVariable() ||\r\n            getDefaultsFromCookie());\r\n    }\r\n    catch (e) {\r\n        /**\r\n         * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\r\n         * to any environment case we have not accounted for. Log to\r\n         * info instead of swallowing so we can find these unknown cases\r\n         * and add paths for them if needed.\r\n         */\r\n        console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\r\n        return;\r\n    }\r\n};\r\n/**\r\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\r\n * for the given product.\r\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\r\n * @public\r\n */\r\nconst getDefaultEmulatorHost = (productName) => { var _a, _b; return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName]; };\r\n/**\r\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\r\n * for the given product.\r\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\r\n * @public\r\n */\r\nconst getDefaultEmulatorHostnameAndPort = (productName) => {\r\n    const host = getDefaultEmulatorHost(productName);\r\n    if (!host) {\r\n        return undefined;\r\n    }\r\n    const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\r\n    if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\r\n        throw new Error(`Invalid host ${host} with no separate hostname and port!`);\r\n    }\r\n    // eslint-disable-next-line no-restricted-globals\r\n    const port = parseInt(host.substring(separatorIndex + 1), 10);\r\n    if (host[0] === '[') {\r\n        // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\r\n        return [host.substring(1, separatorIndex - 1), port];\r\n    }\r\n    else {\r\n        return [host.substring(0, separatorIndex), port];\r\n    }\r\n};\r\n/**\r\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\r\n * @public\r\n */\r\nconst getDefaultAppConfig = () => { var _a; return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config; };\r\n/**\r\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\r\n * prefixed by \"_\")\r\n * @public\r\n */\r\nconst getExperimentalSetting = (name) => { var _a; return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`]; };\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass Deferred {\r\n    constructor() {\r\n        this.reject = () => { };\r\n        this.resolve = () => { };\r\n        this.promise = new Promise((resolve, reject) => {\r\n            this.resolve = resolve;\r\n            this.reject = reject;\r\n        });\r\n    }\r\n    /**\r\n     * Our API internals are not promiseified and cannot because our callback APIs have subtle expectations around\r\n     * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\r\n     * and returns a node-style callback which will resolve or reject the Deferred's promise.\r\n     */\r\n    wrapCallback(callback) {\r\n        return (error, value) => {\r\n            if (error) {\r\n                this.reject(error);\r\n            }\r\n            else {\r\n                this.resolve(value);\r\n            }\r\n            if (typeof callback === 'function') {\r\n                // Attaching noop handler just in case developer wasn't expecting\r\n                // promises\r\n                this.promise.catch(() => { });\r\n                // Some of our callbacks don't expect a value and our own tests\r\n                // assert that the parameter length is 1\r\n                if (callback.length === 1) {\r\n                    callback(error);\r\n                }\r\n                else {\r\n                    callback(error, value);\r\n                }\r\n            }\r\n        };\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction createMockUserToken(token, projectId) {\r\n    if (token.uid) {\r\n        throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\r\n    }\r\n    // Unsecured JWTs use \"none\" as the algorithm.\r\n    const header = {\r\n        alg: 'none',\r\n        type: 'JWT'\r\n    };\r\n    const project = projectId || 'demo-project';\r\n    const iat = token.iat || 0;\r\n    const sub = token.sub || token.user_id;\r\n    if (!sub) {\r\n        throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\r\n    }\r\n    const payload = Object.assign({ \r\n        // Set all required fields to decent defaults\r\n        iss: `https://securetoken.google.com/${project}`, aud: project, iat, exp: iat + 3600, auth_time: iat, sub, user_id: sub, firebase: {\r\n            sign_in_provider: 'custom',\r\n            identities: {}\r\n        } }, token);\r\n    // Unsecured JWTs use the empty string as a signature.\r\n    const signature = '';\r\n    return [\r\n        base64urlEncodeWithoutPadding(JSON.stringify(header)),\r\n        base64urlEncodeWithoutPadding(JSON.stringify(payload)),\r\n        signature\r\n    ].join('.');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns navigator.userAgent string or '' if it's not defined.\r\n * @return user agent string\r\n */\r\nfunction getUA() {\r\n    if (typeof navigator !== 'undefined' &&\r\n        typeof navigator['userAgent'] === 'string') {\r\n        return navigator['userAgent'];\r\n    }\r\n    else {\r\n        return '';\r\n    }\r\n}\r\n/**\r\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\r\n *\r\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\r\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\r\n * wait for a callback.\r\n */\r\nfunction isMobileCordova() {\r\n    return (typeof window !== 'undefined' &&\r\n        // @ts-ignore Setting up an broadly applicable index signature for Window\r\n        // just to deal with this case would probably be a bad idea.\r\n        !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\r\n        /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA()));\r\n}\r\n/**\r\n * Detect Node.js.\r\n *\r\n * @return true if Node.js environment is detected or specified.\r\n */\r\n// Node detection logic from: https://github.com/iliakan/detect-node/\r\nfunction isNode() {\r\n    var _a;\r\n    const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\r\n    if (forceEnvironment === 'node') {\r\n        return true;\r\n    }\r\n    else if (forceEnvironment === 'browser') {\r\n        return false;\r\n    }\r\n    try {\r\n        return (Object.prototype.toString.call(global.process) === '[object process]');\r\n    }\r\n    catch (e) {\r\n        return false;\r\n    }\r\n}\r\n/**\r\n * Detect Browser Environment\r\n */\r\nfunction isBrowser() {\r\n    return typeof self === 'object' && self.self === self;\r\n}\r\nfunction isBrowserExtension() {\r\n    const runtime = typeof chrome === 'object'\r\n        ? chrome.runtime\r\n        : typeof browser === 'object'\r\n            ? browser.runtime\r\n            : undefined;\r\n    return typeof runtime === 'object' && runtime.id !== undefined;\r\n}\r\n/**\r\n * Detect React Native.\r\n *\r\n * @return true if ReactNative environment is detected.\r\n */\r\nfunction isReactNative() {\r\n    return (typeof navigator === 'object' && navigator['product'] === 'ReactNative');\r\n}\r\n/** Detects Electron apps. */\r\nfunction isElectron() {\r\n    return getUA().indexOf('Electron/') >= 0;\r\n}\r\n/** Detects Internet Explorer. */\r\nfunction isIE() {\r\n    const ua = getUA();\r\n    return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\r\n}\r\n/** Detects Universal Windows Platform apps. */\r\nfunction isUWP() {\r\n    return getUA().indexOf('MSAppHost/') >= 0;\r\n}\r\n/**\r\n * Detect whether the current SDK build is the Node version.\r\n *\r\n * @return true if it's the Node SDK build.\r\n */\r\nfunction isNodeSdk() {\r\n    return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\r\n}\r\n/** Returns true if we are running in Safari. */\r\nfunction isSafari() {\r\n    return (!isNode() &&\r\n        navigator.userAgent.includes('Safari') &&\r\n        !navigator.userAgent.includes('Chrome'));\r\n}\r\n/**\r\n * This method checks if indexedDB is supported by current browser/service worker context\r\n * @return true if indexedDB is supported by current browser/service worker context\r\n */\r\nfunction isIndexedDBAvailable() {\r\n    try {\r\n        return typeof indexedDB === 'object';\r\n    }\r\n    catch (e) {\r\n        return false;\r\n    }\r\n}\r\n/**\r\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\r\n * if errors occur during the database open operation.\r\n *\r\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\r\n * private browsing)\r\n */\r\nfunction validateIndexedDBOpenable() {\r\n    return new Promise((resolve, reject) => {\r\n        try {\r\n            let preExist = true;\r\n            const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\r\n            const request = self.indexedDB.open(DB_CHECK_NAME);\r\n            request.onsuccess = () => {\r\n                request.result.close();\r\n                // delete database only when it doesn't pre-exist\r\n                if (!preExist) {\r\n                    self.indexedDB.deleteDatabase(DB_CHECK_NAME);\r\n                }\r\n                resolve(true);\r\n            };\r\n            request.onupgradeneeded = () => {\r\n                preExist = false;\r\n            };\r\n            request.onerror = () => {\r\n                var _a;\r\n                reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\r\n            };\r\n        }\r\n        catch (error) {\r\n            reject(error);\r\n        }\r\n    });\r\n}\r\n/**\r\n *\r\n * This method checks whether cookie is enabled within current browser\r\n * @return true if cookie is enabled within current browser\r\n */\r\nfunction areCookiesEnabled() {\r\n    if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\r\n        return false;\r\n    }\r\n    return true;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @fileoverview Standardized Firebase Error.\r\n *\r\n * Usage:\r\n *\r\n *   // Typescript string literals for type-safe codes\r\n *   type Err =\r\n *     'unknown' |\r\n *     'object-not-found'\r\n *     ;\r\n *\r\n *   // Closure enum for type-safe error codes\r\n *   // at-enum {string}\r\n *   var Err = {\r\n *     UNKNOWN: 'unknown',\r\n *     OBJECT_NOT_FOUND: 'object-not-found',\r\n *   }\r\n *\r\n *   let errors: Map<Err, string> = {\r\n *     'generic-error': \"Unknown error\",\r\n *     'file-not-found': \"Could not find file: {$file}\",\r\n *   };\r\n *\r\n *   // Type-safe function - must pass a valid error code as param.\r\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\r\n *\r\n *   ...\r\n *   throw error.create(Err.GENERIC);\r\n *   ...\r\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\r\n *   ...\r\n *   // Service: Could not file file: foo.txt (service/file-not-found).\r\n *\r\n *   catch (e) {\r\n *     assert(e.message === \"Could not find file: foo.txt.\");\r\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\r\n *       console.log(\"Could not read file: \" + e['file']);\r\n *     }\r\n *   }\r\n */\r\nconst ERROR_NAME = 'FirebaseError';\r\n// Based on code from:\r\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\r\nclass FirebaseError extends Error {\r\n    constructor(\r\n    /** The error code for this error. */\r\n    code, message, \r\n    /** Custom data for this error. */\r\n    customData) {\r\n        super(message);\r\n        this.code = code;\r\n        this.customData = customData;\r\n        /** The custom name for all FirebaseErrors. */\r\n        this.name = ERROR_NAME;\r\n        // Fix For ES5\r\n        // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\r\n        Object.setPrototypeOf(this, FirebaseError.prototype);\r\n        // Maintains proper stack trace for where our error was thrown.\r\n        // Only available on V8.\r\n        if (Error.captureStackTrace) {\r\n            Error.captureStackTrace(this, ErrorFactory.prototype.create);\r\n        }\r\n    }\r\n}\r\nclass ErrorFactory {\r\n    constructor(service, serviceName, errors) {\r\n        this.service = service;\r\n        this.serviceName = serviceName;\r\n        this.errors = errors;\r\n    }\r\n    create(code, ...data) {\r\n        const customData = data[0] || {};\r\n        const fullCode = `${this.service}/${code}`;\r\n        const template = this.errors[code];\r\n        const message = template ? replaceTemplate(template, customData) : 'Error';\r\n        // Service Name: Error message (service/code).\r\n        const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\r\n        const error = new FirebaseError(fullCode, fullMessage, customData);\r\n        return error;\r\n    }\r\n}\r\nfunction replaceTemplate(template, data) {\r\n    return template.replace(PATTERN, (_, key) => {\r\n        const value = data[key];\r\n        return value != null ? String(value) : `<${key}?>`;\r\n    });\r\n}\r\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Evaluates a JSON string into a javascript object.\r\n *\r\n * @param {string} str A string containing JSON.\r\n * @return {*} The javascript object representing the specified JSON.\r\n */\r\nfunction jsonEval(str) {\r\n    return JSON.parse(str);\r\n}\r\n/**\r\n * Returns JSON representing a javascript object.\r\n * @param {*} data Javascript object to be stringified.\r\n * @return {string} The JSON contents of the object.\r\n */\r\nfunction stringify(data) {\r\n    return JSON.stringify(data);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Decodes a Firebase auth. token into constituent parts.\r\n *\r\n * Notes:\r\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\r\nconst decode = function (token) {\r\n    let header = {}, claims = {}, data = {}, signature = '';\r\n    try {\r\n        const parts = token.split('.');\r\n        header = jsonEval(base64Decode(parts[0]) || '');\r\n        claims = jsonEval(base64Decode(parts[1]) || '');\r\n        signature = parts[2];\r\n        data = claims['d'] || {};\r\n        delete claims['d'];\r\n    }\r\n    catch (e) { }\r\n    return {\r\n        header,\r\n        claims,\r\n        data,\r\n        signature\r\n    };\r\n};\r\n/**\r\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\r\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\r\n *\r\n * Notes:\r\n * - May return a false negative if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\r\nconst isValidTimestamp = function (token) {\r\n    const claims = decode(token).claims;\r\n    const now = Math.floor(new Date().getTime() / 1000);\r\n    let validSince = 0, validUntil = 0;\r\n    if (typeof claims === 'object') {\r\n        if (claims.hasOwnProperty('nbf')) {\r\n            validSince = claims['nbf'];\r\n        }\r\n        else if (claims.hasOwnProperty('iat')) {\r\n            validSince = claims['iat'];\r\n        }\r\n        if (claims.hasOwnProperty('exp')) {\r\n            validUntil = claims['exp'];\r\n        }\r\n        else {\r\n            // token will expire after 24h by default\r\n            validUntil = validSince + 86400;\r\n        }\r\n    }\r\n    return (!!now &&\r\n        !!validSince &&\r\n        !!validUntil &&\r\n        now >= validSince &&\r\n        now <= validUntil);\r\n};\r\n/**\r\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\r\n *\r\n * Notes:\r\n * - May return null if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\r\nconst issuedAtTime = function (token) {\r\n    const claims = decode(token).claims;\r\n    if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\r\n        return claims['iat'];\r\n    }\r\n    return null;\r\n};\r\n/**\r\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\r\n *\r\n * Notes:\r\n * - May return a false negative if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\r\nconst isValidFormat = function (token) {\r\n    const decoded = decode(token), claims = decoded.claims;\r\n    return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\r\n};\r\n/**\r\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\r\n *\r\n * Notes:\r\n * - May return a false negative if there's no native base64 decoding support.\r\n * - Doesn't check if the token is actually valid.\r\n */\r\nconst isAdmin = function (token) {\r\n    const claims = decode(token).claims;\r\n    return typeof claims === 'object' && claims['admin'] === true;\r\n};\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction contains(obj, key) {\r\n    return Object.prototype.hasOwnProperty.call(obj, key);\r\n}\r\nfunction safeGet(obj, key) {\r\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\r\n        return obj[key];\r\n    }\r\n    else {\r\n        return undefined;\r\n    }\r\n}\r\nfunction isEmpty(obj) {\r\n    for (const key in obj) {\r\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\nfunction map(obj, fn, contextObj) {\r\n    const res = {};\r\n    for (const key in obj) {\r\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\r\n            res[key] = fn.call(contextObj, obj[key], key, obj);\r\n        }\r\n    }\r\n    return res;\r\n}\r\n/**\r\n * Deep equal two objects. Support Arrays and Objects.\r\n */\r\nfunction deepEqual(a, b) {\r\n    if (a === b) {\r\n        return true;\r\n    }\r\n    const aKeys = Object.keys(a);\r\n    const bKeys = Object.keys(b);\r\n    for (const k of aKeys) {\r\n        if (!bKeys.includes(k)) {\r\n            return false;\r\n        }\r\n        const aProp = a[k];\r\n        const bProp = b[k];\r\n        if (isObject(aProp) && isObject(bProp)) {\r\n            if (!deepEqual(aProp, bProp)) {\r\n                return false;\r\n            }\r\n        }\r\n        else if (aProp !== bProp) {\r\n            return false;\r\n        }\r\n    }\r\n    for (const k of bKeys) {\r\n        if (!aKeys.includes(k)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\nfunction isObject(thing) {\r\n    return thing !== null && typeof thing === 'object';\r\n}\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\r\n * @internal\r\n */\r\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\r\n    const deferredPromise = new Deferred();\r\n    setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\r\n    promise.then(deferredPromise.resolve, deferredPromise.reject);\r\n    return deferredPromise.promise;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\r\n * params object (e.g. {arg: 'val', arg2: 'val2'})\r\n * Note: You must prepend it with ? when adding it to a URL.\r\n */\r\nfunction querystring(querystringParams) {\r\n    const params = [];\r\n    for (const [key, value] of Object.entries(querystringParams)) {\r\n        if (Array.isArray(value)) {\r\n            value.forEach(arrayVal => {\r\n                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\r\n            });\r\n        }\r\n        else {\r\n            params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\r\n        }\r\n    }\r\n    return params.length ? '&' + params.join('&') : '';\r\n}\r\n/**\r\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\r\n * (e.g. {arg: 'val', arg2: 'val2'})\r\n */\r\nfunction querystringDecode(querystring) {\r\n    const obj = {};\r\n    const tokens = querystring.replace(/^\\?/, '').split('&');\r\n    tokens.forEach(token => {\r\n        if (token) {\r\n            const [key, value] = token.split('=');\r\n            obj[decodeURIComponent(key)] = decodeURIComponent(value);\r\n        }\r\n    });\r\n    return obj;\r\n}\r\n/**\r\n * Extract the query string part of a URL, including the leading question mark (if present).\r\n */\r\nfunction extractQuerystring(url) {\r\n    const queryStart = url.indexOf('?');\r\n    if (!queryStart) {\r\n        return '';\r\n    }\r\n    const fragmentStart = url.indexOf('#', queryStart);\r\n    return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @fileoverview SHA-1 cryptographic hash.\r\n * Variable names follow the notation in FIPS PUB 180-3:\r\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\r\n *\r\n * Usage:\r\n *   var sha1 = new sha1();\r\n *   sha1.update(bytes);\r\n *   var hash = sha1.digest();\r\n *\r\n * Performance:\r\n *   Chrome 23:   ~400 Mbit/s\r\n *   Firefox 16:  ~250 Mbit/s\r\n *\r\n */\r\n/**\r\n * SHA-1 cryptographic hash constructor.\r\n *\r\n * The properties declared here are discussed in the above algorithm document.\r\n * @constructor\r\n * @final\r\n * @struct\r\n */\r\nclass Sha1 {\r\n    constructor() {\r\n        /**\r\n         * Holds the previous values of accumulated variables a-e in the compress_\r\n         * function.\r\n         * @private\r\n         */\r\n        this.chain_ = [];\r\n        /**\r\n         * A buffer holding the partially computed hash result.\r\n         * @private\r\n         */\r\n        this.buf_ = [];\r\n        /**\r\n         * An array of 80 bytes, each a part of the message to be hashed.  Referred to\r\n         * as the message schedule in the docs.\r\n         * @private\r\n         */\r\n        this.W_ = [];\r\n        /**\r\n         * Contains data needed to pad messages less than 64 bytes.\r\n         * @private\r\n         */\r\n        this.pad_ = [];\r\n        /**\r\n         * @private {number}\r\n         */\r\n        this.inbuf_ = 0;\r\n        /**\r\n         * @private {number}\r\n         */\r\n        this.total_ = 0;\r\n        this.blockSize = 512 / 8;\r\n        this.pad_[0] = 128;\r\n        for (let i = 1; i < this.blockSize; ++i) {\r\n            this.pad_[i] = 0;\r\n        }\r\n        this.reset();\r\n    }\r\n    reset() {\r\n        this.chain_[0] = 0x67452301;\r\n        this.chain_[1] = 0xefcdab89;\r\n        this.chain_[2] = 0x98badcfe;\r\n        this.chain_[3] = 0x10325476;\r\n        this.chain_[4] = 0xc3d2e1f0;\r\n        this.inbuf_ = 0;\r\n        this.total_ = 0;\r\n    }\r\n    /**\r\n     * Internal compress helper function.\r\n     * @param buf Block to compress.\r\n     * @param offset Offset of the block in the buffer.\r\n     * @private\r\n     */\r\n    compress_(buf, offset) {\r\n        if (!offset) {\r\n            offset = 0;\r\n        }\r\n        const W = this.W_;\r\n        // get 16 big endian words\r\n        if (typeof buf === 'string') {\r\n            for (let i = 0; i < 16; i++) {\r\n                // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\r\n                // have a bug that turns the post-increment ++ operator into pre-increment\r\n                // during JIT compilation.  We have code that depends heavily on SHA-1 for\r\n                // correctness and which is affected by this bug, so I've removed all uses\r\n                // of post-increment ++ in which the result value is used.  We can revert\r\n                // this change once the Safari bug\r\n                // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\r\n                // most clients have been updated.\r\n                W[i] =\r\n                    (buf.charCodeAt(offset) << 24) |\r\n                        (buf.charCodeAt(offset + 1) << 16) |\r\n                        (buf.charCodeAt(offset + 2) << 8) |\r\n                        buf.charCodeAt(offset + 3);\r\n                offset += 4;\r\n            }\r\n        }\r\n        else {\r\n            for (let i = 0; i < 16; i++) {\r\n                W[i] =\r\n                    (buf[offset] << 24) |\r\n                        (buf[offset + 1] << 16) |\r\n                        (buf[offset + 2] << 8) |\r\n                        buf[offset + 3];\r\n                offset += 4;\r\n            }\r\n        }\r\n        // expand to 80 words\r\n        for (let i = 16; i < 80; i++) {\r\n            const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\r\n            W[i] = ((t << 1) | (t >>> 31)) & 0xffffffff;\r\n        }\r\n        let a = this.chain_[0];\r\n        let b = this.chain_[1];\r\n        let c = this.chain_[2];\r\n        let d = this.chain_[3];\r\n        let e = this.chain_[4];\r\n        let f, k;\r\n        // TODO(user): Try to unroll this loop to speed up the computation.\r\n        for (let i = 0; i < 80; i++) {\r\n            if (i < 40) {\r\n                if (i < 20) {\r\n                    f = d ^ (b & (c ^ d));\r\n                    k = 0x5a827999;\r\n                }\r\n                else {\r\n                    f = b ^ c ^ d;\r\n                    k = 0x6ed9eba1;\r\n                }\r\n            }\r\n            else {\r\n                if (i < 60) {\r\n                    f = (b & c) | (d & (b | c));\r\n                    k = 0x8f1bbcdc;\r\n                }\r\n                else {\r\n                    f = b ^ c ^ d;\r\n                    k = 0xca62c1d6;\r\n                }\r\n            }\r\n            const t = (((a << 5) | (a >>> 27)) + f + e + k + W[i]) & 0xffffffff;\r\n            e = d;\r\n            d = c;\r\n            c = ((b << 30) | (b >>> 2)) & 0xffffffff;\r\n            b = a;\r\n            a = t;\r\n        }\r\n        this.chain_[0] = (this.chain_[0] + a) & 0xffffffff;\r\n        this.chain_[1] = (this.chain_[1] + b) & 0xffffffff;\r\n        this.chain_[2] = (this.chain_[2] + c) & 0xffffffff;\r\n        this.chain_[3] = (this.chain_[3] + d) & 0xffffffff;\r\n        this.chain_[4] = (this.chain_[4] + e) & 0xffffffff;\r\n    }\r\n    update(bytes, length) {\r\n        // TODO(johnlenz): tighten the function signature and remove this check\r\n        if (bytes == null) {\r\n            return;\r\n        }\r\n        if (length === undefined) {\r\n            length = bytes.length;\r\n        }\r\n        const lengthMinusBlock = length - this.blockSize;\r\n        let n = 0;\r\n        // Using local instead of member variables gives ~5% speedup on Firefox 16.\r\n        const buf = this.buf_;\r\n        let inbuf = this.inbuf_;\r\n        // The outer while loop should execute at most twice.\r\n        while (n < length) {\r\n            // When we have no data in the block to top up, we can directly process the\r\n            // input buffer (assuming it contains sufficient data). This gives ~25%\r\n            // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\r\n            // the data is provided in large chunks (or in multiples of 64 bytes).\r\n            if (inbuf === 0) {\r\n                while (n <= lengthMinusBlock) {\r\n                    this.compress_(bytes, n);\r\n                    n += this.blockSize;\r\n                }\r\n            }\r\n            if (typeof bytes === 'string') {\r\n                while (n < length) {\r\n                    buf[inbuf] = bytes.charCodeAt(n);\r\n                    ++inbuf;\r\n                    ++n;\r\n                    if (inbuf === this.blockSize) {\r\n                        this.compress_(buf);\r\n                        inbuf = 0;\r\n                        // Jump to the outer loop so we use the full-block optimization.\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                while (n < length) {\r\n                    buf[inbuf] = bytes[n];\r\n                    ++inbuf;\r\n                    ++n;\r\n                    if (inbuf === this.blockSize) {\r\n                        this.compress_(buf);\r\n                        inbuf = 0;\r\n                        // Jump to the outer loop so we use the full-block optimization.\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        this.inbuf_ = inbuf;\r\n        this.total_ += length;\r\n    }\r\n    /** @override */\r\n    digest() {\r\n        const digest = [];\r\n        let totalBits = this.total_ * 8;\r\n        // Add pad 0x80 0x00*.\r\n        if (this.inbuf_ < 56) {\r\n            this.update(this.pad_, 56 - this.inbuf_);\r\n        }\r\n        else {\r\n            this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\r\n        }\r\n        // Add # bits.\r\n        for (let i = this.blockSize - 1; i >= 56; i--) {\r\n            this.buf_[i] = totalBits & 255;\r\n            totalBits /= 256; // Don't use bit-shifting here!\r\n        }\r\n        this.compress_(this.buf_);\r\n        let n = 0;\r\n        for (let i = 0; i < 5; i++) {\r\n            for (let j = 24; j >= 0; j -= 8) {\r\n                digest[n] = (this.chain_[i] >> j) & 255;\r\n                ++n;\r\n            }\r\n        }\r\n        return digest;\r\n    }\r\n}\n\n/**\r\n * Helper to make a Subscribe function (just like Promise helps make a\r\n * Thenable).\r\n *\r\n * @param executor Function which can make calls to a single Observer\r\n *     as a proxy.\r\n * @param onNoObservers Callback when count of Observers goes to zero.\r\n */\r\nfunction createSubscribe(executor, onNoObservers) {\r\n    const proxy = new ObserverProxy(executor, onNoObservers);\r\n    return proxy.subscribe.bind(proxy);\r\n}\r\n/**\r\n * Implement fan-out for any number of Observers attached via a subscribe\r\n * function.\r\n */\r\nclass ObserverProxy {\r\n    /**\r\n     * @param executor Function which can make calls to a single Observer\r\n     *     as a proxy.\r\n     * @param onNoObservers Callback when count of Observers goes to zero.\r\n     */\r\n    constructor(executor, onNoObservers) {\r\n        this.observers = [];\r\n        this.unsubscribes = [];\r\n        this.observerCount = 0;\r\n        // Micro-task scheduling by calling task.then().\r\n        this.task = Promise.resolve();\r\n        this.finalized = false;\r\n        this.onNoObservers = onNoObservers;\r\n        // Call the executor asynchronously so subscribers that are called\r\n        // synchronously after the creation of the subscribe function\r\n        // can still receive the very first value generated in the executor.\r\n        this.task\r\n            .then(() => {\r\n            executor(this);\r\n        })\r\n            .catch(e => {\r\n            this.error(e);\r\n        });\r\n    }\r\n    next(value) {\r\n        this.forEachObserver((observer) => {\r\n            observer.next(value);\r\n        });\r\n    }\r\n    error(error) {\r\n        this.forEachObserver((observer) => {\r\n            observer.error(error);\r\n        });\r\n        this.close(error);\r\n    }\r\n    complete() {\r\n        this.forEachObserver((observer) => {\r\n            observer.complete();\r\n        });\r\n        this.close();\r\n    }\r\n    /**\r\n     * Subscribe function that can be used to add an Observer to the fan-out list.\r\n     *\r\n     * - We require that no event is sent to a subscriber sychronously to their\r\n     *   call to subscribe().\r\n     */\r\n    subscribe(nextOrObserver, error, complete) {\r\n        let observer;\r\n        if (nextOrObserver === undefined &&\r\n            error === undefined &&\r\n            complete === undefined) {\r\n            throw new Error('Missing Observer.');\r\n        }\r\n        // Assemble an Observer object when passed as callback functions.\r\n        if (implementsAnyMethods(nextOrObserver, [\r\n            'next',\r\n            'error',\r\n            'complete'\r\n        ])) {\r\n            observer = nextOrObserver;\r\n        }\r\n        else {\r\n            observer = {\r\n                next: nextOrObserver,\r\n                error,\r\n                complete\r\n            };\r\n        }\r\n        if (observer.next === undefined) {\r\n            observer.next = noop;\r\n        }\r\n        if (observer.error === undefined) {\r\n            observer.error = noop;\r\n        }\r\n        if (observer.complete === undefined) {\r\n            observer.complete = noop;\r\n        }\r\n        const unsub = this.unsubscribeOne.bind(this, this.observers.length);\r\n        // Attempt to subscribe to a terminated Observable - we\r\n        // just respond to the Observer with the final error or complete\r\n        // event.\r\n        if (this.finalized) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this.task.then(() => {\r\n                try {\r\n                    if (this.finalError) {\r\n                        observer.error(this.finalError);\r\n                    }\r\n                    else {\r\n                        observer.complete();\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    // nothing\r\n                }\r\n                return;\r\n            });\r\n        }\r\n        this.observers.push(observer);\r\n        return unsub;\r\n    }\r\n    // Unsubscribe is synchronous - we guarantee that no events are sent to\r\n    // any unsubscribed Observer.\r\n    unsubscribeOne(i) {\r\n        if (this.observers === undefined || this.observers[i] === undefined) {\r\n            return;\r\n        }\r\n        delete this.observers[i];\r\n        this.observerCount -= 1;\r\n        if (this.observerCount === 0 && this.onNoObservers !== undefined) {\r\n            this.onNoObservers(this);\r\n        }\r\n    }\r\n    forEachObserver(fn) {\r\n        if (this.finalized) {\r\n            // Already closed by previous event....just eat the additional values.\r\n            return;\r\n        }\r\n        // Since sendOne calls asynchronously - there is no chance that\r\n        // this.observers will become undefined.\r\n        for (let i = 0; i < this.observers.length; i++) {\r\n            this.sendOne(i, fn);\r\n        }\r\n    }\r\n    // Call the Observer via one of it's callback function. We are careful to\r\n    // confirm that the observe has not been unsubscribed since this asynchronous\r\n    // function had been queued.\r\n    sendOne(i, fn) {\r\n        // Execute the callback asynchronously\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.task.then(() => {\r\n            if (this.observers !== undefined && this.observers[i] !== undefined) {\r\n                try {\r\n                    fn(this.observers[i]);\r\n                }\r\n                catch (e) {\r\n                    // Ignore exceptions raised in Observers or missing methods of an\r\n                    // Observer.\r\n                    // Log error to console. b/31404806\r\n                    if (typeof console !== 'undefined' && console.error) {\r\n                        console.error(e);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    close(err) {\r\n        if (this.finalized) {\r\n            return;\r\n        }\r\n        this.finalized = true;\r\n        if (err !== undefined) {\r\n            this.finalError = err;\r\n        }\r\n        // Proxy is no longer needed - garbage collect references\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.task.then(() => {\r\n            this.observers = undefined;\r\n            this.onNoObservers = undefined;\r\n        });\r\n    }\r\n}\r\n/** Turn synchronous function into one called asynchronously. */\r\n// eslint-disable-next-line @typescript-eslint/ban-types\r\nfunction async(fn, onError) {\r\n    return (...args) => {\r\n        Promise.resolve(true)\r\n            .then(() => {\r\n            fn(...args);\r\n        })\r\n            .catch((error) => {\r\n            if (onError) {\r\n                onError(error);\r\n            }\r\n        });\r\n    };\r\n}\r\n/**\r\n * Return true if the object passed in implements any of the named methods.\r\n */\r\nfunction implementsAnyMethods(obj, methods) {\r\n    if (typeof obj !== 'object' || obj === null) {\r\n        return false;\r\n    }\r\n    for (const method of methods) {\r\n        if (method in obj && typeof obj[method] === 'function') {\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction noop() {\r\n    // do nothing\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Check to make sure the appropriate number of arguments are provided for a public function.\r\n * Throws an error if it fails.\r\n *\r\n * @param fnName The function name\r\n * @param minCount The minimum number of arguments to allow for the function call\r\n * @param maxCount The maximum number of argument to allow for the function call\r\n * @param argCount The actual number of arguments provided.\r\n */\r\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\r\n    let argError;\r\n    if (argCount < minCount) {\r\n        argError = 'at least ' + minCount;\r\n    }\r\n    else if (argCount > maxCount) {\r\n        argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\r\n    }\r\n    if (argError) {\r\n        const error = fnName +\r\n            ' failed: Was called with ' +\r\n            argCount +\r\n            (argCount === 1 ? ' argument.' : ' arguments.') +\r\n            ' Expects ' +\r\n            argError +\r\n            '.';\r\n        throw new Error(error);\r\n    }\r\n};\r\n/**\r\n * Generates a string to prefix an error message about failed argument validation\r\n *\r\n * @param fnName The function name\r\n * @param argName The name of the argument\r\n * @return The prefix to add to the error thrown for validation.\r\n */\r\nfunction errorPrefix(fnName, argName) {\r\n    return `${fnName} failed: ${argName} argument `;\r\n}\r\n/**\r\n * @param fnName\r\n * @param argumentNumber\r\n * @param namespace\r\n * @param optional\r\n */\r\nfunction validateNamespace(fnName, namespace, optional) {\r\n    if (optional && !namespace) {\r\n        return;\r\n    }\r\n    if (typeof namespace !== 'string') {\r\n        //TODO: I should do more validation here. We only allow certain chars in namespaces.\r\n        throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\r\n    }\r\n}\r\nfunction validateCallback(fnName, argumentName, \r\n// eslint-disable-next-line @typescript-eslint/ban-types\r\ncallback, optional) {\r\n    if (optional && !callback) {\r\n        return;\r\n    }\r\n    if (typeof callback !== 'function') {\r\n        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\r\n    }\r\n}\r\nfunction validateContextObject(fnName, argumentName, context, optional) {\r\n    if (optional && !context) {\r\n        return;\r\n    }\r\n    if (typeof context !== 'object' || context === null) {\r\n        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\r\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\r\n// so it's been modified.\r\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\r\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\r\n// use 2 characters in Javascript.  All 4-byte UTF-8 characters begin with a first\r\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\r\n// pair).\r\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\r\n/**\r\n * @param {string} str\r\n * @return {Array}\r\n */\r\nconst stringToByteArray = function (str) {\r\n    const out = [];\r\n    let p = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n        let c = str.charCodeAt(i);\r\n        // Is this the lead surrogate in a surrogate pair?\r\n        if (c >= 0xd800 && c <= 0xdbff) {\r\n            const high = c - 0xd800; // the high 10 bits.\r\n            i++;\r\n            assert(i < str.length, 'Surrogate pair missing trail surrogate.');\r\n            const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\r\n            c = 0x10000 + (high << 10) + low;\r\n        }\r\n        if (c < 128) {\r\n            out[p++] = c;\r\n        }\r\n        else if (c < 2048) {\r\n            out[p++] = (c >> 6) | 192;\r\n            out[p++] = (c & 63) | 128;\r\n        }\r\n        else if (c < 65536) {\r\n            out[p++] = (c >> 12) | 224;\r\n            out[p++] = ((c >> 6) & 63) | 128;\r\n            out[p++] = (c & 63) | 128;\r\n        }\r\n        else {\r\n            out[p++] = (c >> 18) | 240;\r\n            out[p++] = ((c >> 12) & 63) | 128;\r\n            out[p++] = ((c >> 6) & 63) | 128;\r\n            out[p++] = (c & 63) | 128;\r\n        }\r\n    }\r\n    return out;\r\n};\r\n/**\r\n * Calculate length without actually converting; useful for doing cheaper validation.\r\n * @param {string} str\r\n * @return {number}\r\n */\r\nconst stringLength = function (str) {\r\n    let p = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n        const c = str.charCodeAt(i);\r\n        if (c < 128) {\r\n            p++;\r\n        }\r\n        else if (c < 2048) {\r\n            p += 2;\r\n        }\r\n        else if (c >= 0xd800 && c <= 0xdbff) {\r\n            // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\r\n            p += 4;\r\n            i++; // skip trail surrogate.\r\n        }\r\n        else {\r\n            p += 3;\r\n        }\r\n    }\r\n    return p;\r\n};\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Copied from https://stackoverflow.com/a/2117523\r\n * Generates a new uuid.\r\n * @public\r\n */\r\nconst uuidv4 = function () {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\r\n        const r = (Math.random() * 16) | 0, v = c === 'x' ? r : (r & 0x3) | 0x8;\r\n        return v.toString(16);\r\n    });\r\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * The amount of milliseconds to exponentially increase.\r\n */\r\nconst DEFAULT_INTERVAL_MILLIS = 1000;\r\n/**\r\n * The factor to backoff by.\r\n * Should be a number greater than 1.\r\n */\r\nconst DEFAULT_BACKOFF_FACTOR = 2;\r\n/**\r\n * The maximum milliseconds to increase to.\r\n *\r\n * <p>Visible for testing\r\n */\r\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\r\n/**\r\n * The percentage of backoff time to randomize by.\r\n * See\r\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\r\n * for context.\r\n *\r\n * <p>Visible for testing\r\n */\r\nconst RANDOM_FACTOR = 0.5;\r\n/**\r\n * Based on the backoff method from\r\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\r\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\r\n */\r\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\r\n    // Calculates an exponentially increasing value.\r\n    // Deviation: calculates value from count and a constant interval, so we only need to save value\r\n    // and count to restore state.\r\n    const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\r\n    // A random \"fuzz\" to avoid waves of retries.\r\n    // Deviation: randomFactor is required.\r\n    const randomWait = Math.round(\r\n    // A fraction of the backoff value to add/subtract.\r\n    // Deviation: changes multiplication order to improve readability.\r\n    RANDOM_FACTOR *\r\n        currBaseValue *\r\n        // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\r\n        // if we add or subtract.\r\n        (Math.random() - 0.5) *\r\n        2);\r\n    // Limits backoff to max to avoid effectively permanent backoff.\r\n    return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Provide English ordinal letters after a number\r\n */\r\nfunction ordinal(i) {\r\n    if (!Number.isFinite(i)) {\r\n        return `${i}`;\r\n    }\r\n    return i + indicator(i);\r\n}\r\nfunction indicator(i) {\r\n    i = Math.abs(i);\r\n    const cent = i % 100;\r\n    if (cent >= 10 && cent <= 20) {\r\n        return 'th';\r\n    }\r\n    const dec = i % 10;\r\n    if (dec === 1) {\r\n        return 'st';\r\n    }\r\n    if (dec === 2) {\r\n        return 'nd';\r\n    }\r\n    if (dec === 3) {\r\n        return 'rd';\r\n    }\r\n    return 'th';\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getModularInstance(service) {\r\n    if (service && service._delegate) {\r\n        return service._delegate;\r\n    }\r\n    else {\r\n        return service;\r\n    }\r\n}\n\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isUWP, isValidFormat, isValidTimestamp, issuedAtTime, jsonEval, map, ordinal, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, uuidv4, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,SAAS,GAAG;EACd;AACJ;AACA;EACIC,WAAW,EAAE,KAAK;EAClB;AACJ;AACA;EACIC,UAAU,EAAE,KAAK;EACjB;AACJ;AACA;EACIC,WAAW,EAAE;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,SAAAA,CAAUC,SAAS,EAAEC,OAAO,EAAE;EACzC,IAAI,CAACD,SAAS,EAAE;IACZ,MAAME,cAAc,CAACD,OAAO,CAAC;EACjC;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAMC,cAAc,GAAG,SAAAA,CAAUD,OAAO,EAAE;EACtC,OAAO,IAAIE,KAAK,CAAC,qBAAqB,GAClCR,SAAS,CAACG,WAAW,GACrB,4BAA4B,GAC5BG,OAAO,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,GAAG,SAAAA,CAAUC,GAAG,EAAE;EACvC;EACA,MAAMC,GAAG,GAAG,EAAE;EACd,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAIE,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IACzB,IAAIE,CAAC,GAAG,GAAG,EAAE;MACTJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC;IAChB,CAAC,MACI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACfJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,CAAC,GAAI,GAAG;MACzBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI,IAAI,CAACA,CAAC,GAAG,MAAM,MAAM,MAAM,IAC5BF,CAAC,GAAG,CAAC,GAAGH,GAAG,CAACI,MAAM,IAClB,CAACJ,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM,EAAE;MAC7C;MACAE,CAAC,GAAG,OAAO,IAAI,CAACA,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,IAAIL,GAAG,CAACM,UAAU,CAAC,EAAEH,CAAC,CAAC,GAAG,MAAM,CAAC;MACnEF,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG;MACjCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI;MACDJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B;EACJ;EACA,OAAOJ,GAAG;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,iBAAiB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACvC;EACA,MAAMP,GAAG,GAAG,EAAE;EACd,IAAIQ,GAAG,GAAG,CAAC;IAAEJ,CAAC,GAAG,CAAC;EAClB,OAAOI,GAAG,GAAGD,KAAK,CAACJ,MAAM,EAAE;IACvB,MAAMM,EAAE,GAAGF,KAAK,CAACC,GAAG,EAAE,CAAC;IACvB,IAAIC,EAAE,GAAG,GAAG,EAAE;MACVT,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAACF,EAAE,CAAC;IACtC,CAAC,MACI,IAAIA,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;MAC3B,MAAMG,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvBR,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAE,CAACF,EAAE,GAAG,EAAE,KAAK,CAAC,GAAKG,EAAE,GAAG,EAAG,CAAC;IAChE,CAAC,MACI,IAAIH,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;MAC3B;MACA,MAAMG,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMK,EAAE,GAAGN,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMM,EAAE,GAAGP,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMO,CAAC,GAAG,CAAE,CAACN,EAAE,GAAG,CAAC,KAAK,EAAE,GAAK,CAACG,EAAE,GAAG,EAAE,KAAK,EAAG,GAAI,CAACC,EAAE,GAAG,EAAE,KAAK,CAAE,GAAIC,EAAE,GAAG,EAAG,IAC1E,OAAO;MACXd,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAC,MAAM,IAAII,CAAC,IAAI,EAAE,CAAC,CAAC;MAClDf,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAC,MAAM,IAAII,CAAC,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC,MACI;MACD,MAAMH,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMK,EAAE,GAAGN,KAAK,CAACC,GAAG,EAAE,CAAC;MACvBR,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAE,CAACF,EAAE,GAAG,EAAE,KAAK,EAAE,GAAK,CAACG,EAAE,GAAG,EAAE,KAAK,CAAE,GAAIC,EAAE,GAAG,EAAG,CAAC;IACpF;EACJ;EACA,OAAOb,GAAG,CAACgB,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACX;AACJ;AACA;EACIC,cAAc,EAAE,IAAI;EACpB;AACJ;AACA;EACIC,cAAc,EAAE,IAAI;EACpB;AACJ;AACA;AACA;EACIC,qBAAqB,EAAE,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,qBAAqB,EAAE,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,iBAAiB,EAAE,4BAA4B,GAAG,4BAA4B,GAAG,YAAY;EAC7F;AACJ;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,iBAAiB,GAAG,KAAK;EACzC,CAAC;EACD;AACJ;AACA;EACI,IAAIE,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACF,iBAAiB,GAAG,KAAK;EACzC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,kBAAkB,EAAE,OAAOC,IAAI,KAAK,UAAU;EAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC5B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACvB,MAAM/B,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA,IAAI,CAACmC,KAAK,CAAC,CAAC;IACZ,MAAMC,aAAa,GAAGJ,OAAO,GACvB,IAAI,CAACT,qBAAqB,GAC1B,IAAI,CAACF,cAAc;IACzB,MAAMgB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,KAAK,CAACzB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACtC,MAAMiC,KAAK,GAAGP,KAAK,CAAC1B,CAAC,CAAC;MACtB,MAAMkC,SAAS,GAAGlC,CAAC,GAAG,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MACtC,MAAMkC,KAAK,GAAGD,SAAS,GAAGR,KAAK,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,MAAMoC,SAAS,GAAGpC,CAAC,GAAG,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MACtC,MAAMoC,KAAK,GAAGD,SAAS,GAAGV,KAAK,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,MAAMsC,QAAQ,GAAGL,KAAK,IAAI,CAAC;MAC3B,MAAMM,QAAQ,GAAI,CAACN,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKE,KAAK,IAAI,CAAE;MACrD,IAAIK,QAAQ,GAAI,CAACL,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKE,KAAK,IAAI,CAAE;MACnD,IAAII,QAAQ,GAAGJ,KAAK,GAAG,IAAI;MAC3B,IAAI,CAACD,SAAS,EAAE;QACZK,QAAQ,GAAG,EAAE;QACb,IAAI,CAACP,SAAS,EAAE;UACZM,QAAQ,GAAG,EAAE;QACjB;MACJ;MACAR,MAAM,CAACU,IAAI,CAACX,aAAa,CAACO,QAAQ,CAAC,EAAEP,aAAa,CAACQ,QAAQ,CAAC,EAAER,aAAa,CAACS,QAAQ,CAAC,EAAET,aAAa,CAACU,QAAQ,CAAC,CAAC;IACnH;IACA,OAAOT,MAAM,CAAClB,IAAI,CAAC,EAAE,CAAC;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,YAAYA,CAACjB,KAAK,EAAEC,OAAO,EAAE;IACzB;IACA;IACA,IAAI,IAAI,CAACJ,kBAAkB,IAAI,CAACI,OAAO,EAAE;MACrC,OAAOiB,IAAI,CAAClB,KAAK,CAAC;IACtB;IACA,OAAO,IAAI,CAACD,eAAe,CAAC7B,mBAAmB,CAAC8B,KAAK,CAAC,EAAEC,OAAO,CAAC;EACpE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,YAAYA,CAACnB,KAAK,EAAEC,OAAO,EAAE;IACzB;IACA;IACA,IAAI,IAAI,CAACJ,kBAAkB,IAAI,CAACI,OAAO,EAAE;MACrC,OAAOH,IAAI,CAACE,KAAK,CAAC;IACtB;IACA,OAAOtB,iBAAiB,CAAC,IAAI,CAAC0C,uBAAuB,CAACpB,KAAK,EAAEC,OAAO,CAAC,CAAC;EAC1E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,uBAAuBA,CAACpB,KAAK,EAAEC,OAAO,EAAE;IACpC,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,MAAMiB,aAAa,GAAGpB,OAAO,GACvB,IAAI,CAACR,qBAAqB,GAC1B,IAAI,CAACF,cAAc;IACzB,MAAMe,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,KAAK,CAACzB,MAAM,GAAG;MAC/B,MAAMgC,KAAK,GAAGc,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,EAAE,CAAC,CAAC;MAC9C,MAAMkC,SAAS,GAAGlC,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMkC,KAAK,GAAGD,SAAS,GAAGa,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,CAAC;MAC5D,EAAEA,CAAC;MACH,MAAMoC,SAAS,GAAGpC,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMoC,KAAK,GAAGD,SAAS,GAAGW,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7D,EAAEA,CAAC;MACH,MAAMiD,SAAS,GAAGjD,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMiD,KAAK,GAAGD,SAAS,GAAGF,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7D,EAAEA,CAAC;MACH,IAAIiC,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIa,KAAK,IAAI,IAAI,EAAE;QAClE,MAAM,IAAIC,uBAAuB,CAAC,CAAC;MACvC;MACA,MAAMb,QAAQ,GAAIL,KAAK,IAAI,CAAC,GAAKE,KAAK,IAAI,CAAE;MAC5CH,MAAM,CAACU,IAAI,CAACJ,QAAQ,CAAC;MACrB,IAAID,KAAK,KAAK,EAAE,EAAE;QACd,MAAME,QAAQ,GAAKJ,KAAK,IAAI,CAAC,GAAI,IAAI,GAAKE,KAAK,IAAI,CAAE;QACrDL,MAAM,CAACU,IAAI,CAACH,QAAQ,CAAC;QACrB,IAAIW,KAAK,KAAK,EAAE,EAAE;UACd,MAAMV,QAAQ,GAAKH,KAAK,IAAI,CAAC,GAAI,IAAI,GAAIa,KAAK;UAC9ClB,MAAM,CAACU,IAAI,CAACF,QAAQ,CAAC;QACzB;MACJ;IACJ;IACA,OAAOR,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACd,cAAc,EAAE;MACtB,IAAI,CAACA,cAAc,GAAG,CAAC,CAAC;MACxB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;MACxB,IAAI,CAACC,qBAAqB,GAAG,CAAC,CAAC;MAC/B,IAAI,CAACC,qBAAqB,GAAG,CAAC,CAAC;MAC/B;MACA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACqB,YAAY,CAACpB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAI,CAACgB,cAAc,CAAChB,CAAC,CAAC,GAAG,IAAI,CAACqB,YAAY,CAAC2B,MAAM,CAAChD,CAAC,CAAC;QACpD,IAAI,CAACiB,cAAc,CAAC,IAAI,CAACD,cAAc,CAAChB,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC/C,IAAI,CAACkB,qBAAqB,CAAClB,CAAC,CAAC,GAAG,IAAI,CAACsB,oBAAoB,CAAC0B,MAAM,CAAChD,CAAC,CAAC;QACnE,IAAI,CAACmB,qBAAqB,CAAC,IAAI,CAACD,qBAAqB,CAAClB,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC7D;QACA,IAAIA,CAAC,IAAI,IAAI,CAACoB,iBAAiB,CAACnB,MAAM,EAAE;UACpC,IAAI,CAACgB,cAAc,CAAC,IAAI,CAACK,oBAAoB,CAAC0B,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAGA,CAAC;UAC5D,IAAI,CAACmB,qBAAqB,CAAC,IAAI,CAACE,YAAY,CAAC2B,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC/D;MACJ;IACJ;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAMmD,uBAAuB,SAASxD,KAAK,CAAC;EACxCyD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,yBAAyB;EACzC;AACJ;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,SAAAA,CAAU1D,GAAG,EAAE;EAChC,MAAM2D,SAAS,GAAG5D,mBAAmB,CAACC,GAAG,CAAC;EAC1C,OAAOkB,MAAM,CAACU,eAAe,CAAC+B,SAAS,EAAE,IAAI,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,SAAAA,CAAU5D,GAAG,EAAE;EACjD;EACA,OAAO0D,YAAY,CAAC1D,GAAG,CAAC,CAAC6D,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,SAAAA,CAAU9D,GAAG,EAAE;EAChC,IAAI;IACA,OAAOkB,MAAM,CAAC8B,YAAY,CAAChD,GAAG,EAAE,IAAI,CAAC;EACzC,CAAC,CACD,OAAO+D,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;EAC7C;EACA,OAAO,IAAI;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOC,UAAU,CAACC,SAAS,EAAEF,KAAK,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACE,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAI,EAAEA,MAAM,YAAYC,MAAM,CAAC,EAAE;IAC7B,OAAOD,MAAM;EACjB;EACA,QAAQA,MAAM,CAAChB,WAAW;IACtB,KAAKkB,IAAI;MACL;MACA;MACA,MAAMC,SAAS,GAAGH,MAAM;MACxB,OAAO,IAAIE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAC,CAAC,CAAC;IACxC,KAAKH,MAAM;MACP,IAAIF,MAAM,KAAKD,SAAS,EAAE;QACtBC,MAAM,GAAG,CAAC,CAAC;MACf;MACA;IACJ,KAAKvC,KAAK;MACN;MACAuC,MAAM,GAAG,EAAE;MACX;IACJ;MACI;MACA,OAAOC,MAAM;EACrB;EACA,KAAK,MAAMK,IAAI,IAAIL,MAAM,EAAE;IACvB;IACA,IAAI,CAACA,MAAM,CAACM,cAAc,CAACD,IAAI,CAAC,IAAI,CAACE,UAAU,CAACF,IAAI,CAAC,EAAE;MACnD;IACJ;IACAN,MAAM,CAACM,IAAI,CAAC,GAAGR,UAAU,CAACE,MAAM,CAACM,IAAI,CAAC,EAAEL,MAAM,CAACK,IAAI,CAAC,CAAC;EACzD;EACA,OAAON,MAAM;AACjB;AACA,SAASQ,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAK,WAAW;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACjB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOA,IAAI;EACf;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB;EACA,MAAM,IAAIrF,KAAK,CAAC,iCAAiC,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsF,qBAAqB,GAAGA,CAAA,KAAMJ,SAAS,CAAC,CAAC,CAACK,qBAAqB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EACrC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,GAAG,KAAK,WAAW,EAAE;IACtE;EACJ;EACA,MAAMC,kBAAkB,GAAGF,OAAO,CAACC,GAAG,CAACH,qBAAqB;EAC5D,IAAII,kBAAkB,EAAE;IACpB,OAAOC,IAAI,CAACC,KAAK,CAACF,kBAAkB,CAAC;EACzC;AACJ,CAAC;AACD,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;EAChC,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACjC;EACJ;EACA,IAAIC,KAAK;EACT,IAAI;IACAA,KAAK,GAAGD,QAAQ,CAACE,MAAM,CAACD,KAAK,CAAC,+BAA+B,CAAC;EAClE,CAAC,CACD,OAAO/B,CAAC,EAAE;IACN;IACA;IACA;EACJ;EACA,MAAMiC,OAAO,GAAGF,KAAK,IAAIhC,YAAY,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C,OAAOE,OAAO,IAAIN,IAAI,CAACC,KAAK,CAACK,OAAO,CAAC;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACtB,IAAI;IACA,OAAQb,qBAAqB,CAAC,CAAC,IAC3BE,0BAA0B,CAAC,CAAC,IAC5BM,qBAAqB,CAAC,CAAC;EAC/B,CAAC,CACD,OAAO7B,CAAC,EAAE;IACN;AACR;AACA;AACA;AACA;AACA;IACQC,OAAO,CAACkC,IAAI,CAAE,+CAA8CnC,CAAE,EAAC,CAAC;IAChE;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,sBAAsB,GAAIC,WAAW,IAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,aAAa,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACF,WAAW,CAAC;AAAE,CAAC;AAC9M;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,iCAAiC,GAAIJ,WAAW,IAAK;EACvD,MAAMK,IAAI,GAAGN,sBAAsB,CAACC,WAAW,CAAC;EAChD,IAAI,CAACK,IAAI,EAAE;IACP,OAAOpC,SAAS;EACpB;EACA,MAAMqC,cAAc,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,IAAID,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,CAAC,KAAKD,IAAI,CAACrG,MAAM,EAAE;IAC3D,MAAM,IAAIN,KAAK,CAAE,gBAAe2G,IAAK,sCAAqC,CAAC;EAC/E;EACA;EACA,MAAMG,IAAI,GAAGC,QAAQ,CAACJ,IAAI,CAACK,SAAS,CAACJ,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7D,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACjB;IACA,OAAO,CAACA,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEJ,cAAc,GAAG,CAAC,CAAC,EAAEE,IAAI,CAAC;EACxD,CAAC,MACI;IACD,OAAO,CAACH,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEJ,cAAc,CAAC,EAAEE,IAAI,CAAC;EACpD;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;EAAE,IAAIV,EAAE;EAAE,OAAO,CAACA,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,MAAM;AAAE,CAAC;AACzH;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAIxD,IAAI,IAAK;EAAE,IAAI4C,EAAE;EAAE,OAAO,CAACA,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAE,IAAG5C,IAAK,EAAC,CAAC;AAAE,CAAC;;AAErI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyD,QAAQ,CAAC;EACX3D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4D,MAAM,GAAG,MAAM,CAAE,CAAC;IACvB,IAAI,CAACC,OAAO,GAAG,MAAM,CAAE,CAAC;IACxB,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACF,OAAO,EAAED,MAAM,KAAK;MAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACxB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACII,YAAYA,CAACC,QAAQ,EAAE;IACnB,OAAO,CAACvD,KAAK,EAAEE,KAAK,KAAK;MACrB,IAAIF,KAAK,EAAE;QACP,IAAI,CAACkD,MAAM,CAAClD,KAAK,CAAC;MACtB,CAAC,MACI;QACD,IAAI,CAACmD,OAAO,CAACjD,KAAK,CAAC;MACvB;MACA,IAAI,OAAOqD,QAAQ,KAAK,UAAU,EAAE;QAChC;QACA;QACA,IAAI,CAACH,OAAO,CAACI,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;QAC7B;QACA;QACA,IAAID,QAAQ,CAACpH,MAAM,KAAK,CAAC,EAAE;UACvBoH,QAAQ,CAACvD,KAAK,CAAC;QACnB,CAAC,MACI;UACDuD,QAAQ,CAACvD,KAAK,EAAEE,KAAK,CAAC;QAC1B;MACJ;IACJ,CAAC;EACL;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuD,mBAAmBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC3C,IAAID,KAAK,CAACE,GAAG,EAAE;IACX,MAAM,IAAI/H,KAAK,CAAC,8GAA8G,CAAC;EACnI;EACA;EACA,MAAMgI,MAAM,GAAG;IACXC,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE;EACV,CAAC;EACD,MAAMC,OAAO,GAAGL,SAAS,IAAI,cAAc;EAC3C,MAAMM,GAAG,GAAGP,KAAK,CAACO,GAAG,IAAI,CAAC;EAC1B,MAAMC,GAAG,GAAGR,KAAK,CAACQ,GAAG,IAAIR,KAAK,CAACS,OAAO;EACtC,IAAI,CAACD,GAAG,EAAE;IACN,MAAM,IAAIrI,KAAK,CAAC,sDAAsD,CAAC;EAC3E;EACA,MAAMuI,OAAO,GAAG7D,MAAM,CAAC8D,MAAM,CAAC;IAC1B;IACAC,GAAG,EAAG,kCAAiCN,OAAQ,EAAC;IAAEO,GAAG,EAAEP,OAAO;IAAEC,GAAG;IAAEO,GAAG,EAAEP,GAAG,GAAG,IAAI;IAAEQ,SAAS,EAAER,GAAG;IAAEC,GAAG;IAAEC,OAAO,EAAED,GAAG;IAAEQ,QAAQ,EAAE;MAC/HC,gBAAgB,EAAE,QAAQ;MAC1BC,UAAU,EAAE,CAAC;IACjB;EAAE,CAAC,EAAElB,KAAK,CAAC;EACf;EACA,MAAMmB,SAAS,GAAG,EAAE;EACpB,OAAO,CACHlF,6BAA6B,CAAC8B,IAAI,CAACqD,SAAS,CAACjB,MAAM,CAAC,CAAC,EACrDlE,6BAA6B,CAAC8B,IAAI,CAACqD,SAAS,CAACV,OAAO,CAAC,CAAC,EACtDS,SAAS,CACZ,CAAC7H,IAAI,CAAC,GAAG,CAAC;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+H,KAAKA,CAAA,EAAG;EACb,IAAI,OAAOC,SAAS,KAAK,WAAW,IAChC,OAAOA,SAAS,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;IAC5C,OAAOA,SAAS,CAAC,WAAW,CAAC;EACjC,CAAC,MACI;IACD,OAAO,EAAE;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAAA,EAAG;EACvB,OAAQ,OAAOhE,MAAM,KAAK,WAAW;EACjC;EACA;EACA,CAAC,EAAEA,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM,CAAC,UAAU,CAAC,IAAIA,MAAM,CAAC,UAAU,CAAC,CAAC,IACjE,mDAAmD,CAACiE,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAA,EAAG;EACd,IAAI/C,EAAE;EACN,MAAMgD,gBAAgB,GAAG,CAAChD,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,gBAAgB;EACtG,IAAIA,gBAAgB,KAAK,MAAM,EAAE;IAC7B,OAAO,IAAI;EACf,CAAC,MACI,IAAIA,gBAAgB,KAAK,SAAS,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,IAAI;IACA,OAAQ7E,MAAM,CAAC8E,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACrE,MAAM,CAACI,OAAO,CAAC,KAAK,kBAAkB;EACjF,CAAC,CACD,OAAOxB,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;AACJ;AACA;AACA;AACA;AACA,SAAS0F,SAASA,CAAA,EAAG;EACjB,OAAO,OAAOxE,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACA,IAAI,KAAKA,IAAI;AACzD;AACA,SAASyE,kBAAkBA,CAAA,EAAG;EAC1B,MAAMC,OAAO,GAAG,OAAOC,MAAM,KAAK,QAAQ,GACpCA,MAAM,CAACD,OAAO,GACd,OAAOE,OAAO,KAAK,QAAQ,GACvBA,OAAO,CAACF,OAAO,GACftF,SAAS;EACnB,OAAO,OAAOsF,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACG,EAAE,KAAKzF,SAAS;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0F,aAAaA,CAAA,EAAG;EACrB,OAAQ,OAAOd,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAAC,SAAS,CAAC,KAAK,aAAa;AACnF;AACA;AACA,SAASe,UAAUA,CAAA,EAAG;EAClB,OAAOhB,KAAK,CAAC,CAAC,CAACiB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;AAC5C;AACA;AACA,SAASC,IAAIA,CAAA,EAAG;EACZ,MAAMC,EAAE,GAAGnB,KAAK,CAAC,CAAC;EAClB,OAAOmB,EAAE,CAACF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAIE,EAAE,CAACF,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;AAClE;AACA;AACA,SAASG,KAAKA,CAAA,EAAG;EACb,OAAOpB,KAAK,CAAC,CAAC,CAACiB,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,SAASA,CAAA,EAAG;EACjB,OAAO/K,SAAS,CAACC,WAAW,KAAK,IAAI,IAAID,SAAS,CAACE,UAAU,KAAK,IAAI;AAC1E;AACA;AACA,SAAS8K,QAAQA,CAAA,EAAG;EAChB,OAAQ,CAAClB,MAAM,CAAC,CAAC,IACbH,SAAS,CAACsB,SAAS,CAACC,QAAQ,CAAC,QAAQ,CAAC,IACtC,CAACvB,SAAS,CAACsB,SAAS,CAACC,QAAQ,CAAC,QAAQ,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,IAAI;IACA,OAAO,OAAOC,SAAS,KAAK,QAAQ;EACxC,CAAC,CACD,OAAO3G,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4G,yBAAyBA,CAAA,EAAG;EACjC,OAAO,IAAIrD,OAAO,CAAC,CAACF,OAAO,EAAED,MAAM,KAAK;IACpC,IAAI;MACA,IAAIyD,QAAQ,GAAG,IAAI;MACnB,MAAMC,aAAa,GAAG,yDAAyD;MAC/E,MAAMC,OAAO,GAAG7F,IAAI,CAACyF,SAAS,CAACK,IAAI,CAACF,aAAa,CAAC;MAClDC,OAAO,CAACE,SAAS,GAAG,MAAM;QACtBF,OAAO,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC;QACtB;QACA,IAAI,CAACN,QAAQ,EAAE;UACX3F,IAAI,CAACyF,SAAS,CAACS,cAAc,CAACN,aAAa,CAAC;QAChD;QACAzD,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC;MACD0D,OAAO,CAACM,eAAe,GAAG,MAAM;QAC5BR,QAAQ,GAAG,KAAK;MACpB,CAAC;MACDE,OAAO,CAACO,OAAO,GAAG,MAAM;QACpB,IAAIhF,EAAE;QACNc,MAAM,CAAC,CAAC,CAACd,EAAE,GAAGyE,OAAO,CAAC7G,KAAK,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzG,OAAO,KAAK,EAAE,CAAC;MACxF,CAAC;IACL,CAAC,CACD,OAAOqE,KAAK,EAAE;MACVkD,MAAM,CAAClD,KAAK,CAAC;IACjB;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASqH,iBAAiBA,CAAA,EAAG;EACzB,IAAI,OAAOrC,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACsC,aAAa,EAAE;IAC9D,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,eAAe;AAClC;AACA;AACA,MAAMC,aAAa,SAAS3L,KAAK,CAAC;EAC9ByD,WAAWA,CAAA,CACX;EACAmI,IAAI,EAAE9L,OAAO,EACb;EACA+L,UAAU,EAAE;IACR,KAAK,CAAC/L,OAAO,CAAC;IACd,IAAI,CAAC8L,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAAClI,IAAI,GAAG+H,UAAU;IACtB;IACA;IACAhH,MAAM,CAACoH,cAAc,CAAC,IAAI,EAAEH,aAAa,CAACnC,SAAS,CAAC;IACpD;IACA;IACA,IAAIxJ,KAAK,CAAC+L,iBAAiB,EAAE;MACzB/L,KAAK,CAAC+L,iBAAiB,CAAC,IAAI,EAAEC,YAAY,CAACxC,SAAS,CAACyC,MAAM,CAAC;IAChE;EACJ;AACJ;AACA,MAAMD,YAAY,CAAC;EACfvI,WAAWA,CAACyI,OAAO,EAAEC,WAAW,EAAEC,MAAM,EAAE;IACtC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAH,MAAMA,CAACL,IAAI,EAAE,GAAGS,IAAI,EAAE;IAClB,MAAMR,UAAU,GAAGQ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAChC,MAAMC,QAAQ,GAAI,GAAE,IAAI,CAACJ,OAAQ,IAAGN,IAAK,EAAC;IAC1C,MAAMW,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACR,IAAI,CAAC;IAClC,MAAM9L,OAAO,GAAGyM,QAAQ,GAAGC,eAAe,CAACD,QAAQ,EAAEV,UAAU,CAAC,GAAG,OAAO;IAC1E;IACA,MAAMY,WAAW,GAAI,GAAE,IAAI,CAACN,WAAY,KAAIrM,OAAQ,KAAIwM,QAAS,IAAG;IACpE,MAAMnI,KAAK,GAAG,IAAIwH,aAAa,CAACW,QAAQ,EAAEG,WAAW,EAAEZ,UAAU,CAAC;IAClE,OAAO1H,KAAK;EAChB;AACJ;AACA,SAASqI,eAAeA,CAACD,QAAQ,EAAEF,IAAI,EAAE;EACrC,OAAOE,QAAQ,CAACxI,OAAO,CAAC2I,OAAO,EAAE,CAACC,CAAC,EAAE1H,GAAG,KAAK;IACzC,MAAMZ,KAAK,GAAGgI,IAAI,CAACpH,GAAG,CAAC;IACvB,OAAOZ,KAAK,IAAI,IAAI,GAAGxD,MAAM,CAACwD,KAAK,CAAC,GAAI,IAAGY,GAAI,IAAG;EACtD,CAAC,CAAC;AACN;AACA,MAAMyH,OAAO,GAAG,eAAe;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAAC1M,GAAG,EAAE;EACnB,OAAO0F,IAAI,CAACC,KAAK,CAAC3F,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+I,SAASA,CAACoD,IAAI,EAAE;EACrB,OAAOzG,IAAI,CAACqD,SAAS,CAACoD,IAAI,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,MAAM,GAAG,SAAAA,CAAUhF,KAAK,EAAE;EAC5B,IAAIG,MAAM,GAAG,CAAC,CAAC;IAAE8E,MAAM,GAAG,CAAC,CAAC;IAAET,IAAI,GAAG,CAAC,CAAC;IAAErD,SAAS,GAAG,EAAE;EACvD,IAAI;IACA,MAAM+D,KAAK,GAAGlF,KAAK,CAACmF,KAAK,CAAC,GAAG,CAAC;IAC9BhF,MAAM,GAAG4E,QAAQ,CAAC5I,YAAY,CAAC+I,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/CD,MAAM,GAAGF,QAAQ,CAAC5I,YAAY,CAAC+I,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/C/D,SAAS,GAAG+D,KAAK,CAAC,CAAC,CAAC;IACpBV,IAAI,GAAGS,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,OAAOA,MAAM,CAAC,GAAG,CAAC;EACtB,CAAC,CACD,OAAO7I,CAAC,EAAE,CAAE;EACZ,OAAO;IACH+D,MAAM;IACN8E,MAAM;IACNT,IAAI;IACJrD;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiE,gBAAgB,GAAG,SAAAA,CAAUpF,KAAK,EAAE;EACtC,MAAMiF,MAAM,GAAGD,MAAM,CAAChF,KAAK,CAAC,CAACiF,MAAM;EACnC,MAAMI,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAIzI,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;EACnD,IAAIwI,UAAU,GAAG,CAAC;IAAEC,UAAU,GAAG,CAAC;EAClC,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;IAC5B,IAAIA,MAAM,CAAC/H,cAAc,CAAC,KAAK,CAAC,EAAE;MAC9BsI,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAC;IAC9B,CAAC,MACI,IAAIA,MAAM,CAAC/H,cAAc,CAAC,KAAK,CAAC,EAAE;MACnCsI,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAC;IAC9B;IACA,IAAIA,MAAM,CAAC/H,cAAc,CAAC,KAAK,CAAC,EAAE;MAC9BuI,UAAU,GAAGR,MAAM,CAAC,KAAK,CAAC;IAC9B,CAAC,MACI;MACD;MACAQ,UAAU,GAAGD,UAAU,GAAG,KAAK;IACnC;EACJ;EACA,OAAQ,CAAC,CAACH,GAAG,IACT,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,UAAU,IACZJ,GAAG,IAAIG,UAAU,IACjBH,GAAG,IAAII,UAAU;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,SAAAA,CAAU1F,KAAK,EAAE;EAClC,MAAMiF,MAAM,GAAGD,MAAM,CAAChF,KAAK,CAAC,CAACiF,MAAM;EACnC,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC/H,cAAc,CAAC,KAAK,CAAC,EAAE;IAC5D,OAAO+H,MAAM,CAAC,KAAK,CAAC;EACxB;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,aAAa,GAAG,SAAAA,CAAU3F,KAAK,EAAE;EACnC,MAAM3B,OAAO,GAAG2G,MAAM,CAAChF,KAAK,CAAC;IAAEiF,MAAM,GAAG5G,OAAO,CAAC4G,MAAM;EACtD,OAAO,CAAC,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC/H,cAAc,CAAC,KAAK,CAAC;AACjF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0I,OAAO,GAAG,SAAAA,CAAU5F,KAAK,EAAE;EAC7B,MAAMiF,MAAM,GAAGD,MAAM,CAAChF,KAAK,CAAC,CAACiF,MAAM;EACnC,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;AACjE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,QAAQA,CAACC,GAAG,EAAE1I,GAAG,EAAE;EACxB,OAAOP,MAAM,CAAC8E,SAAS,CAACzE,cAAc,CAAC2E,IAAI,CAACiE,GAAG,EAAE1I,GAAG,CAAC;AACzD;AACA,SAAS2I,OAAOA,CAACD,GAAG,EAAE1I,GAAG,EAAE;EACvB,IAAIP,MAAM,CAAC8E,SAAS,CAACzE,cAAc,CAAC2E,IAAI,CAACiE,GAAG,EAAE1I,GAAG,CAAC,EAAE;IAChD,OAAO0I,GAAG,CAAC1I,GAAG,CAAC;EACnB,CAAC,MACI;IACD,OAAOV,SAAS;EACpB;AACJ;AACA,SAASsJ,OAAOA,CAACF,GAAG,EAAE;EAClB,KAAK,MAAM1I,GAAG,IAAI0I,GAAG,EAAE;IACnB,IAAIjJ,MAAM,CAAC8E,SAAS,CAACzE,cAAc,CAAC2E,IAAI,CAACiE,GAAG,EAAE1I,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAAS6I,GAAGA,CAACH,GAAG,EAAEI,EAAE,EAAEC,UAAU,EAAE;EAC9B,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,MAAMhJ,GAAG,IAAI0I,GAAG,EAAE;IACnB,IAAIjJ,MAAM,CAAC8E,SAAS,CAACzE,cAAc,CAAC2E,IAAI,CAACiE,GAAG,EAAE1I,GAAG,CAAC,EAAE;MAChDgJ,GAAG,CAAChJ,GAAG,CAAC,GAAG8I,EAAE,CAACrE,IAAI,CAACsE,UAAU,EAAEL,GAAG,CAAC1I,GAAG,CAAC,EAAEA,GAAG,EAAE0I,GAAG,CAAC;IACtD;EACJ;EACA,OAAOM,GAAG;AACd;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,MAAMC,KAAK,GAAG3J,MAAM,CAAC4J,IAAI,CAACH,CAAC,CAAC;EAC5B,MAAMI,KAAK,GAAG7J,MAAM,CAAC4J,IAAI,CAACF,CAAC,CAAC;EAC5B,KAAK,MAAMI,CAAC,IAAIH,KAAK,EAAE;IACnB,IAAI,CAACE,KAAK,CAAC7D,QAAQ,CAAC8D,CAAC,CAAC,EAAE;MACpB,OAAO,KAAK;IAChB;IACA,MAAMC,KAAK,GAAGN,CAAC,CAACK,CAAC,CAAC;IAClB,MAAME,KAAK,GAAGN,CAAC,CAACI,CAAC,CAAC;IAClB,IAAIG,QAAQ,CAACF,KAAK,CAAC,IAAIE,QAAQ,CAACD,KAAK,CAAC,EAAE;MACpC,IAAI,CAACR,SAAS,CAACO,KAAK,EAAEC,KAAK,CAAC,EAAE;QAC1B,OAAO,KAAK;MAChB;IACJ,CAAC,MACI,IAAID,KAAK,KAAKC,KAAK,EAAE;MACtB,OAAO,KAAK;IAChB;EACJ;EACA,KAAK,MAAMF,CAAC,IAAID,KAAK,EAAE;IACnB,IAAI,CAACF,KAAK,CAAC3D,QAAQ,CAAC8D,CAAC,CAAC,EAAE;MACpB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACtH,OAAO,EAAEuH,QAAQ,GAAG,IAAI,EAAE;EAClD,MAAMC,eAAe,GAAG,IAAI3H,QAAQ,CAAC,CAAC;EACtC4H,UAAU,CAAC,MAAMD,eAAe,CAAC1H,MAAM,CAAC,UAAU,CAAC,EAAEyH,QAAQ,CAAC;EAC9DvH,OAAO,CAAC0H,IAAI,CAACF,eAAe,CAACzH,OAAO,EAAEyH,eAAe,CAAC1H,MAAM,CAAC;EAC7D,OAAO0H,eAAe,CAACxH,OAAO;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2H,WAAWA,CAACC,iBAAiB,EAAE;EACpC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAM,CAACnK,GAAG,EAAEZ,KAAK,CAAC,IAAIK,MAAM,CAAC2K,OAAO,CAACF,iBAAiB,CAAC,EAAE;IAC1D,IAAIlN,KAAK,CAACC,OAAO,CAACmC,KAAK,CAAC,EAAE;MACtBA,KAAK,CAACiL,OAAO,CAACC,QAAQ,IAAI;QACtBH,MAAM,CAACrM,IAAI,CAACyM,kBAAkB,CAACvK,GAAG,CAAC,GAAG,GAAG,GAAGuK,kBAAkB,CAACD,QAAQ,CAAC,CAAC;MAC7E,CAAC,CAAC;IACN,CAAC,MACI;MACDH,MAAM,CAACrM,IAAI,CAACyM,kBAAkB,CAACvK,GAAG,CAAC,GAAG,GAAG,GAAGuK,kBAAkB,CAACnL,KAAK,CAAC,CAAC;IAC1E;EACJ;EACA,OAAO+K,MAAM,CAAC9O,MAAM,GAAG,GAAG,GAAG8O,MAAM,CAACjO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;AACtD;AACA;AACA;AACA;AACA;AACA,SAASsO,iBAAiBA,CAACP,WAAW,EAAE;EACpC,MAAMvB,GAAG,GAAG,CAAC,CAAC;EACd,MAAM+B,MAAM,GAAGR,WAAW,CAACnL,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACiJ,KAAK,CAAC,GAAG,CAAC;EACxD0C,MAAM,CAACJ,OAAO,CAACzH,KAAK,IAAI;IACpB,IAAIA,KAAK,EAAE;MACP,MAAM,CAAC5C,GAAG,EAAEZ,KAAK,CAAC,GAAGwD,KAAK,CAACmF,KAAK,CAAC,GAAG,CAAC;MACrCW,GAAG,CAACgC,kBAAkB,CAAC1K,GAAG,CAAC,CAAC,GAAG0K,kBAAkB,CAACtL,KAAK,CAAC;IAC5D;EACJ,CAAC,CAAC;EACF,OAAOsJ,GAAG;AACd;AACA;AACA;AACA;AACA,SAASiC,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,MAAMC,UAAU,GAAGD,GAAG,CAAC1F,OAAO,CAAC,GAAG,CAAC;EACnC,IAAI,CAAC2F,UAAU,EAAE;IACb,OAAO,EAAE;EACb;EACA,MAAMC,aAAa,GAAGF,GAAG,CAAC1F,OAAO,CAAC,GAAG,EAAE2F,UAAU,CAAC;EAClD,OAAOD,GAAG,CAAC7I,SAAS,CAAC8I,UAAU,EAAEC,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAGxL,SAAS,CAAC;AACnF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyL,IAAI,CAAC;EACPvM,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwM,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC;IACf;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,GAAG,GAAG,CAAC;IACxB,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;IAClB,KAAK,IAAI/P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACkQ,SAAS,EAAE,EAAElQ,CAAC,EAAE;MACrC,IAAI,CAAC+P,IAAI,CAAC/P,CAAC,CAAC,GAAG,CAAC;IACpB;IACA,IAAI,CAACmQ,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACP,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACI,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,SAASA,CAACC,GAAG,EAAEC,MAAM,EAAE;IACnB,IAAI,CAACA,MAAM,EAAE;MACTA,MAAM,GAAG,CAAC;IACd;IACA,MAAMC,CAAC,GAAG,IAAI,CAACT,EAAE;IACjB;IACA,IAAI,OAAOO,GAAG,KAAK,QAAQ,EAAE;MACzB,KAAK,IAAIrQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAuQ,CAAC,CAACvQ,CAAC,CAAC,GACCqQ,GAAG,CAAClQ,UAAU,CAACmQ,MAAM,CAAC,IAAI,EAAE,GACxBD,GAAG,CAAClQ,UAAU,CAACmQ,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACjCD,GAAG,CAAClQ,UAAU,CAACmQ,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACjCD,GAAG,CAAClQ,UAAU,CAACmQ,MAAM,GAAG,CAAC,CAAC;QAClCA,MAAM,IAAI,CAAC;MACf;IACJ,CAAC,MACI;MACD,KAAK,IAAItQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzBuQ,CAAC,CAACvQ,CAAC,CAAC,GACCqQ,GAAG,CAACC,MAAM,CAAC,IAAI,EAAE,GACbD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACtBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;QACvBA,MAAM,IAAI,CAAC;MACf;IACJ;IACA;IACA,KAAK,IAAItQ,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMwQ,CAAC,GAAGD,CAAC,CAACvQ,CAAC,GAAG,CAAC,CAAC,GAAGuQ,CAAC,CAACvQ,CAAC,GAAG,CAAC,CAAC,GAAGuQ,CAAC,CAACvQ,CAAC,GAAG,EAAE,CAAC,GAAGuQ,CAAC,CAACvQ,CAAC,GAAG,EAAE,CAAC;MACrDuQ,CAAC,CAACvQ,CAAC,CAAC,GAAG,CAAEwQ,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAI,UAAU;IAC/C;IACA,IAAI1C,CAAC,GAAG,IAAI,CAAC8B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI7B,CAAC,GAAG,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI1P,CAAC,GAAG,IAAI,CAAC0P,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIa,CAAC,GAAG,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIhM,CAAC,GAAG,IAAI,CAACgM,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIc,CAAC,EAAEvC,CAAC;IACR;IACA,KAAK,IAAInO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAIA,CAAC,GAAG,EAAE,EAAE;QACR,IAAIA,CAAC,GAAG,EAAE,EAAE;UACR0Q,CAAC,GAAGD,CAAC,GAAI1C,CAAC,IAAI7N,CAAC,GAAGuQ,CAAC,CAAE;UACrBtC,CAAC,GAAG,UAAU;QAClB,CAAC,MACI;UACDuC,CAAC,GAAG3C,CAAC,GAAG7N,CAAC,GAAGuQ,CAAC;UACbtC,CAAC,GAAG,UAAU;QAClB;MACJ,CAAC,MACI;QACD,IAAInO,CAAC,GAAG,EAAE,EAAE;UACR0Q,CAAC,GAAI3C,CAAC,GAAG7N,CAAC,GAAKuQ,CAAC,IAAI1C,CAAC,GAAG7N,CAAC,CAAE;UAC3BiO,CAAC,GAAG,UAAU;QAClB,CAAC,MACI;UACDuC,CAAC,GAAG3C,CAAC,GAAG7N,CAAC,GAAGuQ,CAAC;UACbtC,CAAC,GAAG,UAAU;QAClB;MACJ;MACA,MAAMqC,CAAC,GAAI,CAAE1C,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAI4C,CAAC,GAAG9M,CAAC,GAAGuK,CAAC,GAAGoC,CAAC,CAACvQ,CAAC,CAAC,GAAI,UAAU;MACnE4D,CAAC,GAAG6M,CAAC;MACLA,CAAC,GAAGvQ,CAAC;MACLA,CAAC,GAAG,CAAE6N,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,CAAE,IAAI,UAAU;MACxCA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAG0C,CAAC;IACT;IACA,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG9B,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC8B,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG7B,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG1P,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC0P,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGa,CAAC,GAAI,UAAU;IAClD,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGhM,CAAC,GAAI,UAAU;EACtD;EACA+M,MAAMA,CAACtQ,KAAK,EAAEJ,MAAM,EAAE;IAClB;IACA,IAAII,KAAK,IAAI,IAAI,EAAE;MACf;IACJ;IACA,IAAIJ,MAAM,KAAKiE,SAAS,EAAE;MACtBjE,MAAM,GAAGI,KAAK,CAACJ,MAAM;IACzB;IACA,MAAM2Q,gBAAgB,GAAG3Q,MAAM,GAAG,IAAI,CAACiQ,SAAS;IAChD,IAAIW,CAAC,GAAG,CAAC;IACT;IACA,MAAMR,GAAG,GAAG,IAAI,CAACR,IAAI;IACrB,IAAIiB,KAAK,GAAG,IAAI,CAACd,MAAM;IACvB;IACA,OAAOa,CAAC,GAAG5Q,MAAM,EAAE;MACf;MACA;MACA;MACA;MACA,IAAI6Q,KAAK,KAAK,CAAC,EAAE;QACb,OAAOD,CAAC,IAAID,gBAAgB,EAAE;UAC1B,IAAI,CAACR,SAAS,CAAC/P,KAAK,EAAEwQ,CAAC,CAAC;UACxBA,CAAC,IAAI,IAAI,CAACX,SAAS;QACvB;MACJ;MACA,IAAI,OAAO7P,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOwQ,CAAC,GAAG5Q,MAAM,EAAE;UACfoQ,GAAG,CAACS,KAAK,CAAC,GAAGzQ,KAAK,CAACF,UAAU,CAAC0Q,CAAC,CAAC;UAChC,EAAEC,KAAK;UACP,EAAED,CAAC;UACH,IAAIC,KAAK,KAAK,IAAI,CAACZ,SAAS,EAAE;YAC1B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC;YACnBS,KAAK,GAAG,CAAC;YACT;YACA;UACJ;QACJ;MACJ,CAAC,MACI;QACD,OAAOD,CAAC,GAAG5Q,MAAM,EAAE;UACfoQ,GAAG,CAACS,KAAK,CAAC,GAAGzQ,KAAK,CAACwQ,CAAC,CAAC;UACrB,EAAEC,KAAK;UACP,EAAED,CAAC;UACH,IAAIC,KAAK,KAAK,IAAI,CAACZ,SAAS,EAAE;YAC1B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC;YACnBS,KAAK,GAAG,CAAC;YACT;YACA;UACJ;QACJ;MACJ;IACJ;IACA,IAAI,CAACd,MAAM,GAAGc,KAAK;IACnB,IAAI,CAACb,MAAM,IAAIhQ,MAAM;EACzB;EACA;EACA8Q,MAAMA,CAAA,EAAG;IACL,MAAMA,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;IAC/B;IACA,IAAI,IAAI,CAACD,MAAM,GAAG,EAAE,EAAE;MAClB,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,IAAI,EAAE,EAAE,GAAG,IAAI,CAACC,MAAM,CAAC;IAC5C,CAAC,MACI;MACD,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,IAAI,EAAE,IAAI,CAACG,SAAS,IAAI,IAAI,CAACF,MAAM,GAAG,EAAE,CAAC,CAAC;IAC/D;IACA;IACA,KAAK,IAAIhQ,CAAC,GAAG,IAAI,CAACkQ,SAAS,GAAG,CAAC,EAAElQ,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAI,CAAC6P,IAAI,CAAC7P,CAAC,CAAC,GAAGgR,SAAS,GAAG,GAAG;MAC9BA,SAAS,IAAI,GAAG,CAAC,CAAC;IACtB;;IACA,IAAI,CAACZ,SAAS,CAAC,IAAI,CAACP,IAAI,CAAC;IACzB,IAAIgB,CAAC,GAAG,CAAC;IACT,KAAK,IAAI7Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,KAAK,IAAIiR,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC7BF,MAAM,CAACF,CAAC,CAAC,GAAI,IAAI,CAACjB,MAAM,CAAC5P,CAAC,CAAC,IAAIiR,CAAC,GAAI,GAAG;QACvC,EAAEJ,CAAC;MACP;IACJ;IACA,OAAOE,MAAM;EACjB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACC,QAAQ,EAAEC,aAAa,EAAE;EAC9C,MAAMC,KAAK,GAAG,IAAIC,aAAa,CAACH,QAAQ,EAAEC,aAAa,CAAC;EACxD,OAAOC,KAAK,CAACE,SAAS,CAACC,IAAI,CAACH,KAAK,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;EACIlO,WAAWA,CAAC+N,QAAQ,EAAEC,aAAa,EAAE;IACjC,IAAI,CAACK,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAACC,IAAI,GAAGzK,OAAO,CAACF,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC4K,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,aAAa,GAAGA,aAAa;IAClC;IACA;IACA;IACA,IAAI,CAACQ,IAAI,CACJhD,IAAI,CAAC,MAAM;MACZuC,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC,CACG7J,KAAK,CAAC1D,CAAC,IAAI;MACZ,IAAI,CAACE,KAAK,CAACF,CAAC,CAAC;IACjB,CAAC,CAAC;EACN;EACAkO,IAAIA,CAAC9N,KAAK,EAAE;IACR,IAAI,CAAC+N,eAAe,CAAEC,QAAQ,IAAK;MAC/BA,QAAQ,CAACF,IAAI,CAAC9N,KAAK,CAAC;IACxB,CAAC,CAAC;EACN;EACAF,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,CAACiO,eAAe,CAAEC,QAAQ,IAAK;MAC/BA,QAAQ,CAAClO,KAAK,CAACA,KAAK,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAACiH,KAAK,CAACjH,KAAK,CAAC;EACrB;EACAmO,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,eAAe,CAAEC,QAAQ,IAAK;MAC/BA,QAAQ,CAACC,QAAQ,CAAC,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAAClH,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwG,SAASA,CAACW,cAAc,EAAEpO,KAAK,EAAEmO,QAAQ,EAAE;IACvC,IAAID,QAAQ;IACZ,IAAIE,cAAc,KAAKhO,SAAS,IAC5BJ,KAAK,KAAKI,SAAS,IACnB+N,QAAQ,KAAK/N,SAAS,EAAE;MACxB,MAAM,IAAIvE,KAAK,CAAC,mBAAmB,CAAC;IACxC;IACA;IACA,IAAIwS,oBAAoB,CAACD,cAAc,EAAE,CACrC,MAAM,EACN,OAAO,EACP,UAAU,CACb,CAAC,EAAE;MACAF,QAAQ,GAAGE,cAAc;IAC7B,CAAC,MACI;MACDF,QAAQ,GAAG;QACPF,IAAI,EAAEI,cAAc;QACpBpO,KAAK;QACLmO;MACJ,CAAC;IACL;IACA,IAAID,QAAQ,CAACF,IAAI,KAAK5N,SAAS,EAAE;MAC7B8N,QAAQ,CAACF,IAAI,GAAGM,IAAI;IACxB;IACA,IAAIJ,QAAQ,CAAClO,KAAK,KAAKI,SAAS,EAAE;MAC9B8N,QAAQ,CAAClO,KAAK,GAAGsO,IAAI;IACzB;IACA,IAAIJ,QAAQ,CAACC,QAAQ,KAAK/N,SAAS,EAAE;MACjC8N,QAAQ,CAACC,QAAQ,GAAGG,IAAI;IAC5B;IACA,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAACd,IAAI,CAAC,IAAI,EAAE,IAAI,CAACC,SAAS,CAACxR,MAAM,CAAC;IACnE;IACA;IACA;IACA,IAAI,IAAI,CAAC4R,SAAS,EAAE;MAChB;MACA,IAAI,CAACD,IAAI,CAAChD,IAAI,CAAC,MAAM;QACjB,IAAI;UACA,IAAI,IAAI,CAAC2D,UAAU,EAAE;YACjBP,QAAQ,CAAClO,KAAK,CAAC,IAAI,CAACyO,UAAU,CAAC;UACnC,CAAC,MACI;YACDP,QAAQ,CAACC,QAAQ,CAAC,CAAC;UACvB;QACJ,CAAC,CACD,OAAOrO,CAAC,EAAE;UACN;QAAA;QAEJ;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC6N,SAAS,CAAC/O,IAAI,CAACsP,QAAQ,CAAC;IAC7B,OAAOK,KAAK;EAChB;EACA;EACA;EACAC,cAAcA,CAACtS,CAAC,EAAE;IACd,IAAI,IAAI,CAACyR,SAAS,KAAKvN,SAAS,IAAI,IAAI,CAACuN,SAAS,CAACzR,CAAC,CAAC,KAAKkE,SAAS,EAAE;MACjE;IACJ;IACA,OAAO,IAAI,CAACuN,SAAS,CAACzR,CAAC,CAAC;IACxB,IAAI,CAAC2R,aAAa,IAAI,CAAC;IACvB,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,IAAI,IAAI,CAACP,aAAa,KAAKlN,SAAS,EAAE;MAC9D,IAAI,CAACkN,aAAa,CAAC,IAAI,CAAC;IAC5B;EACJ;EACAW,eAAeA,CAACrE,EAAE,EAAE;IAChB,IAAI,IAAI,CAACmE,SAAS,EAAE;MAChB;MACA;IACJ;IACA;IACA;IACA,KAAK,IAAI7R,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyR,SAAS,CAACxR,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACwS,OAAO,CAACxS,CAAC,EAAE0N,EAAE,CAAC;IACvB;EACJ;EACA;EACA;EACA;EACA8E,OAAOA,CAACxS,CAAC,EAAE0N,EAAE,EAAE;IACX;IACA;IACA,IAAI,CAACkE,IAAI,CAAChD,IAAI,CAAC,MAAM;MACjB,IAAI,IAAI,CAAC6C,SAAS,KAAKvN,SAAS,IAAI,IAAI,CAACuN,SAAS,CAACzR,CAAC,CAAC,KAAKkE,SAAS,EAAE;QACjE,IAAI;UACAwJ,EAAE,CAAC,IAAI,CAAC+D,SAAS,CAACzR,CAAC,CAAC,CAAC;QACzB,CAAC,CACD,OAAO4D,CAAC,EAAE;UACN;UACA;UACA;UACA,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,KAAK,EAAE;YACjDD,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;UACpB;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;EACAmH,KAAKA,CAAC0H,GAAG,EAAE;IACP,IAAI,IAAI,CAACZ,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAIY,GAAG,KAAKvO,SAAS,EAAE;MACnB,IAAI,CAACqO,UAAU,GAAGE,GAAG;IACzB;IACA;IACA;IACA,IAAI,CAACb,IAAI,CAAChD,IAAI,CAAC,MAAM;MACjB,IAAI,CAAC6C,SAAS,GAAGvN,SAAS;MAC1B,IAAI,CAACkN,aAAa,GAAGlN,SAAS;IAClC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA,SAASwO,KAAKA,CAAChF,EAAE,EAAEiF,OAAO,EAAE;EACxB,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChBzL,OAAO,CAACF,OAAO,CAAC,IAAI,CAAC,CAChB2H,IAAI,CAAC,MAAM;MACZlB,EAAE,CAAC,GAAGkF,IAAI,CAAC;IACf,CAAC,CAAC,CACGtL,KAAK,CAAExD,KAAK,IAAK;MAClB,IAAI6O,OAAO,EAAE;QACTA,OAAO,CAAC7O,KAAK,CAAC;MAClB;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AACA;AACA;AACA;AACA,SAASqO,oBAAoBA,CAAC7E,GAAG,EAAEuF,OAAO,EAAE;EACxC,IAAI,OAAOvF,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,KAAK,MAAMwF,MAAM,IAAID,OAAO,EAAE;IAC1B,IAAIC,MAAM,IAAIxF,GAAG,IAAI,OAAOA,GAAG,CAACwF,MAAM,CAAC,KAAK,UAAU,EAAE;MACpD,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASV,IAAIA,CAAA,EAAG;EACZ;AAAA;;AAGJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,gBAAgB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACrE,IAAIC,QAAQ;EACZ,IAAID,QAAQ,GAAGF,QAAQ,EAAE;IACrBG,QAAQ,GAAG,WAAW,GAAGH,QAAQ;EACrC,CAAC,MACI,IAAIE,QAAQ,GAAGD,QAAQ,EAAE;IAC1BE,QAAQ,GAAGF,QAAQ,KAAK,CAAC,GAAG,MAAM,GAAG,eAAe,GAAGA,QAAQ;EACnE;EACA,IAAIE,QAAQ,EAAE;IACV,MAAMtP,KAAK,GAAGkP,MAAM,GAChB,2BAA2B,GAC3BG,QAAQ,IACPA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,aAAa,CAAC,GAC/C,WAAW,GACXC,QAAQ,GACR,GAAG;IACP,MAAM,IAAIzT,KAAK,CAACmE,KAAK,CAAC;EAC1B;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuP,WAAWA,CAACL,MAAM,EAAEM,OAAO,EAAE;EAClC,OAAQ,GAAEN,MAAO,YAAWM,OAAQ,YAAW;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACP,MAAM,EAAEQ,SAAS,EAAEC,QAAQ,EAAE;EACpD,IAAIA,QAAQ,IAAI,CAACD,SAAS,EAAE;IACxB;EACJ;EACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IAC/B;IACA,MAAM,IAAI7T,KAAK,CAAC0T,WAAW,CAACL,MAAM,EAAE,WAAW,CAAC,GAAG,qCAAqC,CAAC;EAC7F;AACJ;AACA,SAASU,gBAAgBA,CAACV,MAAM,EAAEW,YAAY;AAC9C;AACAtM,QAAQ,EAAEoM,QAAQ,EAAE;EAChB,IAAIA,QAAQ,IAAI,CAACpM,QAAQ,EAAE;IACvB;EACJ;EACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAChC,MAAM,IAAI1H,KAAK,CAAC0T,WAAW,CAACL,MAAM,EAAEW,YAAY,CAAC,GAAG,2BAA2B,CAAC;EACpF;AACJ;AACA,SAASC,qBAAqBA,CAACZ,MAAM,EAAEW,YAAY,EAAEE,OAAO,EAAEJ,QAAQ,EAAE;EACpE,IAAIA,QAAQ,IAAI,CAACI,OAAO,EAAE;IACtB;EACJ;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;IACjD,MAAM,IAAIlU,KAAK,CAAC0T,WAAW,CAACL,MAAM,EAAEW,YAAY,CAAC,GAAG,iCAAiC,CAAC;EAC1F;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,GAAG,SAAAA,CAAUjU,GAAG,EAAE;EACrC,MAAMC,GAAG,GAAG,EAAE;EACd,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAIE,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IACzB;IACA,IAAIE,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAC5B,MAAM6T,IAAI,GAAG7T,CAAC,GAAG,MAAM,CAAC,CAAC;MACzBF,CAAC,EAAE;MACHT,MAAM,CAACS,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAE,yCAAyC,CAAC;MACjE,MAAM+T,GAAG,GAAGnU,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;MACxCE,CAAC,GAAG,OAAO,IAAI6T,IAAI,IAAI,EAAE,CAAC,GAAGC,GAAG;IACpC;IACA,IAAI9T,CAAC,GAAG,GAAG,EAAE;MACTJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC;IAChB,CAAC,MACI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACfJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,CAAC,GAAI,GAAG;MACzBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI,IAAIA,CAAC,GAAG,KAAK,EAAE;MAChBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI;MACDJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG;MACjCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B;EACJ;EACA,OAAOJ,GAAG;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMmU,YAAY,GAAG,SAAAA,CAAUpU,GAAG,EAAE;EAChC,IAAIE,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IAC3B,IAAIE,CAAC,GAAG,GAAG,EAAE;MACTH,CAAC,EAAE;IACP,CAAC,MACI,IAAIG,CAAC,GAAG,IAAI,EAAE;MACfH,CAAC,IAAI,CAAC;IACV,CAAC,MACI,IAAIG,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MACjC;MACAH,CAAC,IAAI,CAAC;MACNC,CAAC,EAAE,CAAC,CAAC;IACT,CAAC,MACI;MACDD,CAAC,IAAI,CAAC;IACV;EACJ;EACA,OAAOA,CAAC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmU,MAAM,GAAG,SAAAA,CAAA,EAAY;EACvB,OAAO,sCAAsC,CAACxQ,OAAO,CAAC,OAAO,EAAExD,CAAC,IAAI;IAChE,MAAMiU,CAAC,GAAIrH,IAAI,CAACsH,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;MAAEC,CAAC,GAAGnU,CAAC,KAAK,GAAG,GAAGiU,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACvE,OAAOE,CAAC,CAACjL,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkL,uBAAuB,GAAG,IAAI;AACpC;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,YAAY,EAAEC,cAAc,GAAGN,uBAAuB,EAAEO,aAAa,GAAGN,sBAAsB,EAAE;EAC5H;EACA;EACA;EACA,MAAMO,aAAa,GAAGF,cAAc,GAAG9H,IAAI,CAACiI,GAAG,CAACF,aAAa,EAAEF,YAAY,CAAC;EAC5E;EACA;EACA,MAAMK,UAAU,GAAGlI,IAAI,CAACmI,KAAK;EAC7B;EACA;EACAR,aAAa,GACTK,aAAa;EACb;EACA;EACChI,IAAI,CAACsH,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GACrB,CAAC,CAAC;EACN;EACA,OAAOtH,IAAI,CAACoI,GAAG,CAACV,gBAAgB,EAAEM,aAAa,GAAGE,UAAU,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,OAAOA,CAACnV,CAAC,EAAE;EAChB,IAAI,CAACoV,MAAM,CAACC,QAAQ,CAACrV,CAAC,CAAC,EAAE;IACrB,OAAQ,GAAEA,CAAE,EAAC;EACjB;EACA,OAAOA,CAAC,GAAGsV,SAAS,CAACtV,CAAC,CAAC;AAC3B;AACA,SAASsV,SAASA,CAACtV,CAAC,EAAE;EAClBA,CAAC,GAAG8M,IAAI,CAACyI,GAAG,CAACvV,CAAC,CAAC;EACf,MAAMwV,IAAI,GAAGxV,CAAC,GAAG,GAAG;EACpB,IAAIwV,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;IAC1B,OAAO,IAAI;EACf;EACA,MAAMC,GAAG,GAAGzV,CAAC,GAAG,EAAE;EAClB,IAAIyV,GAAG,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACf;EACA,IAAIA,GAAG,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACf;EACA,IAAIA,GAAG,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACf;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAC7J,OAAO,EAAE;EACjC,IAAIA,OAAO,IAAIA,OAAO,CAAC8J,SAAS,EAAE;IAC9B,OAAO9J,OAAO,CAAC8J,SAAS;EAC5B,CAAC,MACI;IACD,OAAO9J,OAAO;EAClB;AACJ;AAEA,SAAS1M,SAAS,EAAEgE,uBAAuB,EAAE4D,QAAQ,EAAE4E,YAAY,EAAEL,aAAa,EAAEkJ,gBAAgB,EAAEC,aAAa,EAAE9E,IAAI,EAAExE,iBAAiB,EAAE5L,MAAM,EAAEG,cAAc,EAAEgT,KAAK,EAAE3R,MAAM,EAAE4C,YAAY,EAAEJ,YAAY,EAAEE,6BAA6B,EAAEiR,sBAAsB,EAAErH,QAAQ,EAAE9F,mBAAmB,EAAE2J,eAAe,EAAE1E,MAAM,EAAEzI,QAAQ,EAAE8J,SAAS,EAAE5J,UAAU,EAAEoP,WAAW,EAAE9D,kBAAkB,EAAE3I,mBAAmB,EAAEZ,sBAAsB,EAAEK,iCAAiC,EAAEP,WAAW,EAAEgB,sBAAsB,EAAEjC,SAAS,EAAE6Q,kBAAkB,EAAE7M,KAAK,EAAEuE,OAAO,EAAE9D,SAAS,EAAEC,kBAAkB,EAAEM,UAAU,EAAE2D,OAAO,EAAEzD,IAAI,EAAEO,oBAAoB,EAAEvB,eAAe,EAAEE,MAAM,EAAEiB,SAAS,EAAEN,aAAa,EAAEO,QAAQ,EAAEF,KAAK,EAAEkD,aAAa,EAAEP,gBAAgB,EAAEM,YAAY,EAAEX,QAAQ,EAAEkB,GAAG,EAAE0H,OAAO,EAAE3G,kBAAkB,EAAEK,WAAW,EAAEO,iBAAiB,EAAE7B,OAAO,EAAE0G,YAAY,EAAEH,iBAAiB,EAAElL,SAAS,EAAEsL,MAAM,EAAEnB,gBAAgB,EAAEW,gBAAgB,EAAEE,qBAAqB,EAAEpJ,yBAAyB,EAAE+I,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
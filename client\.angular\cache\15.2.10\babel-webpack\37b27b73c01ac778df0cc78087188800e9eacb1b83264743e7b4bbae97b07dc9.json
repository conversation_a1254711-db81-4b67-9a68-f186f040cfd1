{"ast": null, "code": "export var NavigationBarPluginEvents;\n(function (NavigationBarPluginEvents) {\n  /**\r\n   * Called after the navigation bar is displayed\r\n   */\n  NavigationBarPluginEvents[\"SHOW\"] = \"onShow\";\n  /**\r\n   * Called after navigation bar is hidden\r\n   */\n  NavigationBarPluginEvents[\"HIDE\"] = \"onHide\";\n  /**\r\n   * Called after navigation bar color is changed\r\n   */\n  NavigationBarPluginEvents[\"COLOR_CHANGE\"] = \"onColorChange\";\n})(NavigationBarPluginEvents || (NavigationBarPluginEvents = {}));", "map": {"version": 3, "names": ["NavigationBarPluginEvents"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@hugotomazi/capacitor-navigation-bar/dist/esm/navigationbar.events.js"], "sourcesContent": ["export var NavigationBarPluginEvents;\r\n(function (NavigationBarPluginEvents) {\r\n    /**\r\n     * Called after the navigation bar is displayed\r\n     */\r\n    NavigationBarPluginEvents[\"SHOW\"] = \"onShow\";\r\n    /**\r\n     * Called after navigation bar is hidden\r\n     */\r\n    NavigationBarPluginEvents[\"HIDE\"] = \"onHide\";\r\n    /**\r\n     * Called after navigation bar color is changed\r\n     */\r\n    NavigationBarPluginEvents[\"COLOR_CHANGE\"] = \"onColorChange\";\r\n})(NavigationBarPluginEvents || (NavigationBarPluginEvents = {}));\r\n"], "mappings": "AAAA,OAAO,IAAIA,yBAAyB;AACpC,CAAC,UAAUA,yBAAyB,EAAE;EAClC;AACJ;AACA;EACIA,yBAAyB,CAAC,MAAM,CAAC,GAAG,QAAQ;EAC5C;AACJ;AACA;EACIA,yBAAyB,CAAC,MAAM,CAAC,GAAG,QAAQ;EAC5C;AACJ;AACA;EACIA,yBAAyB,CAAC,cAAc,CAAC,GAAG,eAAe;AAC/D,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { environment } from './../../environments/environment';\nimport { TeamService } from './team.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TeamPlayerService extends TeamService {\n  constructor(httpClient) {\n    super(httpClient);\n    this.httpClient = httpClient;\n  }\n  // Add any additional methods or override existing methods here\n  getPlayersByTeamId(teamId) {\n    return this.httpClient.get(`${environment.apiUrl}/teams/${teamId}/players`);\n  }\n  getUnassignPlayersBySeasonId(seasonId) {\n    return this.httpClient.get(`${environment.apiUrl}/teams/${seasonId}/unassign-players`);\n  }\n  // You can also add methods specific to managing team players here\n  // For example:\n  addPlayerToTeam(teamId, playerId) {\n    // Implement your logic to add a player to a team\n  }\n  removePlayerFromTeam(teamId, playerId) {\n    // Implement your logic to remove a player from a team\n  }\n  static #_ = this.ɵfac = function TeamPlayerService_Factory(t) {\n    return new (t || TeamPlayerService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TeamPlayerService,\n    factory: TeamPlayerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,WAAW,QAAQ,gBAAgB;;;AAK5C,OAAM,MAAOC,iBAAkB,SAAQD,WAAW;EAGhDE,YAAoBC,UAAsB;IACxC,KAAK,CAACA,UAAU,CAAC;IADC,KAAAA,UAAU,GAAVA,UAAU;EAE9B;EAEA;EACAC,kBAAkBA,CAACC,MAAW;IAC5B,OAAO,IAAI,CAACF,UAAU,CAACG,GAAG,CAAC,GAAGP,WAAW,CAACQ,MAAM,UAAUF,MAAM,UAAU,CAAC;EAC7E;EAEAG,4BAA4BA,CAACC,QAAa;IACxC,OAAO,IAAI,CAACN,UAAU,CAACG,GAAG,CAAC,GAAGP,WAAW,CAACQ,MAAM,UAAUE,QAAQ,mBAAmB,CAAC;EACxF;EAEA;EACA;EACAC,eAAeA,CAACL,MAAW,EAAEM,QAAa;IACxC;EAAA;EAGFC,oBAAoBA,CAACP,MAAW,EAAEM,QAAa;IAC7C;EAAA;EACD,QAAAE,CAAA;qBAxBUZ,iBAAiB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAjBjB,iBAAiB;IAAAkB,OAAA,EAAjBlB,iBAAiB,CAAAmB,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["environment", "TeamService", "TeamPlayerService", "constructor", "httpClient", "getPlayersByTeamId", "teamId", "get", "apiUrl", "getUnassignPlayersBySeasonId", "seasonId", "addPlayerToTeam", "playerId", "removePlayerFromTeam", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\team-player.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { environment } from './../../environments/environment';\r\nimport { TeamService } from './team.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TeamPlayerService extends TeamService {\r\n\r\n\r\n  constructor(private httpClient: HttpClient) {\r\n    super(httpClient);\r\n  }\r\n\r\n  // Add any additional methods or override existing methods here\r\n  getPlayersByTeamId(teamId: any) {\r\n    return this.httpClient.get(`${environment.apiUrl}/teams/${teamId}/players`);\r\n  }\r\n\r\n  getUnassignPlayersBySeasonId(seasonId: any) {\r\n    return this.httpClient.get(`${environment.apiUrl}/teams/${seasonId}/unassign-players`);\r\n  }\r\n\r\n  // You can also add methods specific to managing team players here\r\n  // For example:\r\n  addPlayerToTeam(teamId: any, playerId: any) {\r\n    // Implement your logic to add a player to a team\r\n  }\r\n\r\n  removePlayerFromTeam(teamId: any, playerId: any) {\r\n    // Implement your logic to remove a player from a team\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
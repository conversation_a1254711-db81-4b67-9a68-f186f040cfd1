{"ast": null, "code": "import { ElementRef, ChangeDetectorRef } from '@angular/core';\nimport * as Feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nexport class FeatherIconDirective {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {ElementRef} _elementRef\r\n   */\n  constructor(_elementRef, _changeDetector) {\n    this._elementRef = _elementRef;\n    this._changeDetector = _changeDetector;\n  }\n  ngOnChanges(changes) {\n    // Get the native element\n    this._nativeElement = this._elementRef.nativeElement;\n    // SVG parameter\n    this.name = changes.name ? changes.name.currentValue : '';\n    this.size = changes.size ? changes.size.currentValue : '14'; // Set default size 14\n    this.class = changes.class ? changes.class.currentValue : '';\n    // Create SVG\n    const svg = Feather.icons[this.name].toSvg({\n      class: this.class,\n      width: this.size,\n      height: this.size\n    });\n    // Set SVG\n    if (this.inner) {\n      this._nativeElement.innerHTML = svg;\n    } else {\n      this._nativeElement.outerHTML = svg;\n    }\n    this._changeDetector.markForCheck();\n  }\n  static #_ = this.ɵfac = function FeatherIconDirective_Factory(t) {\n    return new (t || FeatherIconDirective)(i0.ɵɵdirectiveInject(ElementRef), i0.ɵɵdirectiveInject(ChangeDetectorRef));\n  };\n  static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: FeatherIconDirective,\n    selectors: [[\"\", \"data-feather\", \"\"]],\n    inputs: {\n      name: [\"data-feather\", \"name\"],\n      class: \"class\",\n      size: \"size\",\n      inner: \"inner\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,UAAU,EAAiBC,iBAAiB,QAAkC,eAAe;AAEjH,OAAO,KAAKC,OAAO,MAAM,eAAe;;AAKxC,OAAM,MAAOC,oBAAoB;EAS/B;;;;;EAKAC,YAC8BC,WAAuB,EAChBC,eAAkC;IADzC,KAAAD,WAAW,GAAXA,WAAW;IACJ,KAAAC,eAAe,GAAfA,eAAe;EACjD;EAEHC,WAAWA,CAACC,OAAsB;IAChC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACJ,WAAW,CAACK,aAAa;IAEpD;IACA,IAAI,CAACC,IAAI,GAAGH,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACG,IAAI,CAACC,YAAY,GAAG,EAAE;IACzD,IAAI,CAACC,IAAI,GAAGL,OAAO,CAACK,IAAI,GAAGL,OAAO,CAACK,IAAI,CAACD,YAAY,GAAG,IAAI,CAAC,CAAC;IAC7D,IAAI,CAACE,KAAK,GAAGN,OAAO,CAACM,KAAK,GAAGN,OAAO,CAACM,KAAK,CAACF,YAAY,GAAG,EAAE;IAE5D;IACA,MAAMG,GAAG,GAAGb,OAAO,CAACc,KAAK,CAAC,IAAI,CAACL,IAAI,CAAC,CAACM,KAAK,CAAC;MACzCH,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBI,KAAK,EAAE,IAAI,CAACL,IAAI;MAChBM,MAAM,EAAE,IAAI,CAACN;KACd,CAAC;IAEF;IACA,IAAI,IAAI,CAACO,KAAK,EAAE;MACd,IAAI,CAACX,cAAc,CAACY,SAAS,GAAGN,GAAG;KACpC,MAAM;MACL,IAAI,CAACN,cAAc,CAACa,SAAS,GAAGP,GAAG;;IAErC,IAAI,CAACT,eAAe,CAACiB,YAAY,EAAE;EACrC;EAAC,QAAAC,CAAA;qBA1CUrB,oBAAoB,EAAAsB,EAAA,CAAAC,iBAAA,CAerB1B,UAAU,GAAAyB,EAAA,CAAAC,iBAAA,CACVzB,iBAAiB;EAAA;EAAA,QAAA0B,EAAA;UAhBhBxB,oBAAoB;IAAAyB,SAAA;IAAAC,MAAA;MAAAlB,IAAA;MAAAG,KAAA;MAAAD,IAAA;MAAAO,KAAA;IAAA;IAAAU,QAAA,GAAAL,EAAA,CAAAM,oBAAA;EAAA", "names": ["ElementRef", "ChangeDetectorRef", "<PERSON><PERSON>", "FeatherIconDirective", "constructor", "_elementRef", "_changeDetector", "ngOnChanges", "changes", "_nativeElement", "nativeElement", "name", "currentValue", "size", "class", "svg", "icons", "toSvg", "width", "height", "inner", "innerHTML", "outerHTML", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "i0", "ɵɵdirectiveInject", "_2", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\directives\\core-feather-icons\\core-feather-icons.ts"], "sourcesContent": ["import { Directive, ElementRef, Input, Inject, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';\r\n\r\nimport * as Feather from 'feather-icons';\r\n\r\n@Directive({\r\n  selector: '[data-feather]'\r\n})\r\nexport class FeatherIconDirective implements OnChanges {\r\n  // Private\r\n  private _nativeElement: any;\r\n\r\n  @Input('data-feather') name!: string;\r\n  @Input() class!: string;\r\n  @Input() size!: string;\r\n  @Input() inner!: boolean;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {ElementRef} _elementRef\r\n   */\r\n  constructor(\r\n    @Inject(ElementRef) private _elementRef: ElementRef,\r\n    @Inject(ChangeDetectorRef) private _changeDetector: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    // Get the native element\r\n    this._nativeElement = this._elementRef.nativeElement;\r\n\r\n    // SVG parameter\r\n    this.name = changes.name ? changes.name.currentValue : '';\r\n    this.size = changes.size ? changes.size.currentValue : '14'; // Set default size 14\r\n    this.class = changes.class ? changes.class.currentValue : '';\r\n\r\n    // Create SVG\r\n    const svg = Feather.icons[this.name].toSvg({\r\n      class: this.class,\r\n      width: this.size,\r\n      height: this.size\r\n    });\r\n\r\n    // Set SVG\r\n    if (this.inner) {\r\n      this._nativeElement.innerHTML = svg;\r\n    } else {\r\n      this._nativeElement.outerHTML = svg;\r\n    }\r\n    this._changeDetector.markForCheck();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FieldType } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@ngx-formly/core\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction FormlyFieldTabs_li_3_ng_template_3_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r6.form.valid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"Submit\"));\n  }\n}\nfunction FormlyFieldTabs_li_3_ng_template_3_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FormlyFieldTabs_li_3_ng_template_3_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const i_r3 = i0.ɵɵnextContext(2).index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(ctx_r9.nextTab(_r0, i_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = i0.ɵɵnextContext(2).index;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r7.isValid(ctx_r7.field.fieldGroup[i_r3]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"Next\"));\n  }\n}\nfunction FormlyFieldTabs_li_3_ng_template_3_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function FormlyFieldTabs_li_3_ng_template_3_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const i_r3 = i0.ɵɵnextContext(2).index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(ctx_r13.prevTab(_r0, i_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"Previous\"));\n  }\n}\nfunction FormlyFieldTabs_li_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"formly-field\", 7);\n    i0.ɵɵelementStart(1, \"div\", 8);\n    i0.ɵɵtemplate(2, FormlyFieldTabs_li_3_ng_template_3_button_2_Template, 3, 4, \"button\", 9);\n    i0.ɵɵtemplate(3, FormlyFieldTabs_li_3_ng_template_3_button_3_Template, 3, 4, \"button\", 10);\n    i0.ɵɵtemplate(4, FormlyFieldTabs_li_3_ng_template_3_button_4_Template, 3, 3, \"button\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    const tab_r2 = ctx_r16.$implicit;\n    const last_r4 = ctx_r16.last;\n    const i_r3 = ctx_r16.index;\n    i0.ɵɵproperty(\"field\", tab_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", last_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r3 !== 0);\n  }\n}\nfunction FormlyFieldTabs_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 4)(1, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function FormlyFieldTabs_li_3_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const i_r3 = restoredCtx.index;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onChangeTab(i_r3, ctx_r17.model));\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FormlyFieldTabs_li_3_ng_template_3_Template, 5, 4, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngbNavItem\", i_r3)(\"disabled\", i_r3 !== 0 && !ctx_r1.isValid(ctx_r1.field.fieldGroup[i_r3 - 1]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(2, 3, tab_r2.props.label), i0.ɵɵsanitizeHtml);\n  }\n}\nexport class FormlyFieldTabs extends FieldType {\n  constructor() {\n    super(...arguments);\n    this.activeID = 0;\n  }\n  ngOnInit() {\n    if (this.field.props.hasOwnProperty('onNext')) {\n      this.onNext = this.field.props.onNext;\n    }\n    if (this.field.props.hasOwnProperty('onPrev')) {\n      this.onPrev = this.field.props.onPrev;\n    }\n    if (this.field.props.hasOwnProperty('onChangeTab')) {\n      this.onChangeTab = this.field.props.onChangeTab;\n    }\n  }\n  isValid(field) {\n    if (field.key) {\n      return field.formControl.valid;\n    }\n    return field.fieldGroup ? field.fieldGroup.every(f => this.isValid(f)) : true;\n  }\n  nextTab(ngbNav, current_index) {\n    console.log('Next current_index', current_index);\n    if (current_index >= 0) {\n      this.form.markAsPristine();\n      this.activeID = current_index + 1;\n      ngbNav.select(this.activeID);\n      if (this.onNext) this.onNext(this.activeID, this.model);\n      if (this.onChangeTab) this.onChangeTab(this.activeID, this.model);\n    }\n  }\n  prevTab(ngbNav, current_index) {\n    if (current_index > 0) {\n      this.activeID = current_index - 1;\n      ngbNav.select(this.activeID);\n      if (this.onPrev) this.onPrev(this.activeID, this.model);\n      if (this.onChangeTab) this.onChangeTab(this.activeID, this.model);\n    }\n  }\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵFormlyFieldTabs_BaseFactory;\n    return function FormlyFieldTabs_Factory(t) {\n      return (ɵFormlyFieldTabs_BaseFactory || (ɵFormlyFieldTabs_BaseFactory = i0.ɵɵgetInheritedFactory(FormlyFieldTabs)))(t || FormlyFieldTabs);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormlyFieldTabs,\n    selectors: [[\"formly-field-tabs\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 5,\n    vars: 2,\n    consts: [[\"ngbNav\", \"\", 1, \"nav-tabs\"], [\"nav\", \"ngbNav\"], [3, \"ngbNavItem\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [3, \"ngbNavItem\", \"disabled\"], [\"ngbNavLink\", \"\", 3, \"innerHTML\", \"click\"], [\"ngbNavContent\", \"\"], [3, \"field\"], [1, \"d-flex\", \"justify-content-between\", \"flex-row-reverse\"], [\"class\", \"btn btn-primary\", \"type\", \"submit\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", \"type\", \"button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function FormlyFieldTabs_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 0, 1);\n        i0.ɵɵtemplate(3, FormlyFieldTabs_li_3_Template, 4, 5, \"li\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(2);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.field.fieldGroup);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.NgbNavContent, i2.NgbNav, i2.NgbNavItem, i2.NgbNavLink, i2.NgbNavOutlet, i3.FormlyField, i4.TranslatePipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAA2B,kBAAkB;;;;;;;;ICO3CC,EAAA,CAAAC,cAAA,iBACkB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADJH,EAAA,CAAAI,UAAA,cAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAwB;IACnDP,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,iBAAsB;;;;;;IACxCV,EAAA,CAAAC,cAAA,iBAC+C;IADaD,EAAA,CAAAW,UAAA,mBAAAC,6EAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAf,EAAA,CAAAgB,aAAA,IAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,MAAAG,GAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAH,MAAA,CAAAI,OAAA,CAAAH,GAAA,EAAAJ,IAAA,CAAc;IAAA,EAAC;IACrCf,EAAA,CAAAE,MAAA,GAAoB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAAxEH,EAAA,CAAAI,UAAA,cAAAmB,MAAA,CAAAC,OAAA,CAAAD,MAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAX,IAAA,GAA0C;IAACf,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,eAAoB;;;;;;IACnEV,EAAA,CAAAC,cAAA,iBAC6B;IAAzBD,EAAA,CAAAW,UAAA,mBAAAgB,6EAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAe,IAAA;MAAA,MAAAb,IAAA,GAAAf,EAAA,CAAAgB,aAAA,IAAAC,KAAA;MAAA,MAAAY,OAAA,GAAA7B,EAAA,CAAAgB,aAAA;MAAA,MAAAG,GAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAQ,OAAA,CAAAC,OAAA,CAAAX,GAAA,EAAAJ,IAAA,CAAc;IAAA,EAAC;IAACf,EAAA,CAAAE,MAAA,GAAwB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IAAjCH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,mBAAwB;;;;;IAPzDV,EAAA,CAAA+B,SAAA,sBAA2C;IAC3C/B,EAAA,CAAAC,cAAA,aAA6D;IACzDD,EAAA,CAAAgC,UAAA,IAAAC,oDAAA,oBACiD;IACjDjC,EAAA,CAAAgC,UAAA,IAAAE,oDAAA,qBAC4E;IAC5ElC,EAAA,CAAAgC,UAAA,IAAAG,oDAAA,qBAC8D;IAClEnC,EAAA,CAAAG,YAAA,EAAM;;;;;;;IARQH,EAAA,CAAAI,UAAA,UAAAgC,MAAA,CAAa;IAEdpC,EAAA,CAAAQ,SAAA,GAAU;IAAVR,EAAA,CAAAI,UAAA,SAAAiC,OAAA,CAAU;IAEVrC,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAI,UAAA,UAAAiC,OAAA,CAAW;IAEXrC,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAI,UAAA,SAAAW,IAAA,OAAa;;;;;;IAVlCf,EAAA,CAAAC,cAAA,YAC8D;IACJD,EAAA,CAAAW,UAAA,mBAAA2B,iDAAA;MAAA,MAAAC,WAAA,GAAAvC,EAAA,CAAAa,aAAA,CAAA2B,IAAA;MAAA,MAAAzB,IAAA,GAAAwB,WAAA,CAAAtB,KAAA;MAAA,MAAAwB,OAAA,GAAAzC,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAqB,WAAA,CAAAoB,OAAA,CAAAC,WAAA,CAAA3B,IAAA,EAAA0B,OAAA,CAAAE,KAAA,CAAoB;IAAA,EAAC;;IAAC3C,EAAA,CAAAG,YAAA,EAAI;IACzFH,EAAA,CAAAgC,UAAA,IAAAY,2CAAA,yBAUc;IAClB5C,EAAA,CAAAG,YAAA,EAAK;;;;;;IAdDH,EAAA,CAAAI,UAAA,eAAAW,IAAA,CAAgB,aAAAA,IAAA,WAAA8B,MAAA,CAAArB,OAAA,CAAAqB,MAAA,CAAApB,KAAA,CAAAC,UAAA,CAAAX,IAAA;IAEFf,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAI,UAAA,cAAAJ,EAAA,CAAAU,WAAA,OAAA0B,MAAA,CAAAU,KAAA,CAAAC,KAAA,GAAA/C,EAAA,CAAAgD,cAAA,CAAuC;;;ADEjE,OAAM,MAAOC,eAAgB,SAAQlD,SAAS;EAJ9CmD,YAAA;;IAKE,KAAAC,QAAQ,GAAG,CAAC;;EAIZC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC3B,KAAK,CAACqB,KAAK,CAACO,cAAc,CAAC,QAAQ,CAAC,EAAE;MAC7C,IAAI,CAACC,MAAM,GAAG,IAAI,CAAC7B,KAAK,CAACqB,KAAK,CAACQ,MAAM;;IAGvC,IAAI,IAAI,CAAC7B,KAAK,CAACqB,KAAK,CAACO,cAAc,CAAC,QAAQ,CAAC,EAAE;MAC7C,IAAI,CAACE,MAAM,GAAG,IAAI,CAAC9B,KAAK,CAACqB,KAAK,CAACS,MAAM;;IAGvC,IAAI,IAAI,CAAC9B,KAAK,CAACqB,KAAK,CAACO,cAAc,CAAC,aAAa,CAAC,EAAE;MAClD,IAAI,CAACX,WAAW,GAAG,IAAI,CAACjB,KAAK,CAACqB,KAAK,CAACJ,WAAW;;EAEnD;EACAlB,OAAOA,CAACC,KAAwB;IAC9B,IAAIA,KAAK,CAAC+B,GAAG,EAAE;MACb,OAAO/B,KAAK,CAACgC,WAAW,CAAClD,KAAK;;IAEhC,OAAOkB,KAAK,CAACC,UAAU,GACnBD,KAAK,CAACC,UAAU,CAACgC,KAAK,CAAEC,CAAC,IAAK,IAAI,CAACnC,OAAO,CAACmC,CAAC,CAAC,CAAC,GAC9C,IAAI;EACV;EAEArC,OAAOA,CAACsC,MAAW,EAAEC,aAAqB;IACxCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,aAAa,CAAC;IAChD,IAAIA,aAAa,IAAI,CAAC,EAAE;MACtB,IAAI,CAACvD,IAAI,CAAC0D,cAAc,EAAE;MAC1B,IAAI,CAACb,QAAQ,GAAGU,aAAa,GAAG,CAAC;MACjCD,MAAM,CAACK,MAAM,CAAC,IAAI,CAACd,QAAQ,CAAC;MAC5B,IAAI,IAAI,CAACG,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,IAAI,CAACH,QAAQ,EAAE,IAAI,CAACR,KAAK,CAAC;MACvD,IAAI,IAAI,CAACD,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACS,QAAQ,EAAE,IAAI,CAACR,KAAK,CAAC;;EAErE;EAEAb,OAAOA,CAAC8B,MAAW,EAAEC,aAAqB;IACxC,IAAIA,aAAa,GAAG,CAAC,EAAE;MACrB,IAAI,CAACV,QAAQ,GAAGU,aAAa,GAAG,CAAC;MACjCD,MAAM,CAACK,MAAM,CAAC,IAAI,CAACd,QAAQ,CAAC;MAC5B,IAAI,IAAI,CAACI,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAACR,KAAK,CAAC;MACvD,IAAI,IAAI,CAACD,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACS,QAAQ,EAAE,IAAI,CAACR,KAAK,CAAC;;EAErE;EAAC,QAAAuB,CAAA;;;uGA7CUjB,eAAe,IAAAkB,CAAA,IAAflB,eAAe;IAAA;EAAA;EAAA,QAAAmB,EAAA;UAAfnB,eAAe;IAAAoB,SAAA;IAAAC,QAAA,GAAAtE,EAAA,CAAAuE,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCN5B7E,EAAA,CAAAC,cAAA,UAAK;QAEGD,EAAA,CAAAgC,UAAA,IAAA+C,6BAAA,gBAcK;QACT/E,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAA+B,SAAA,aAA6C;QACjD/B,EAAA,CAAAG,YAAA,EAAM;;;;QAjBuCH,EAAA,CAAAQ,SAAA,GAAqB;QAArBR,EAAA,CAAAI,UAAA,YAAA0E,GAAA,CAAArD,KAAA,CAAAC,UAAA,CAAqB;QAgBzD1B,EAAA,CAAAQ,SAAA,GAAoB;QAApBR,EAAA,CAAAI,UAAA,iBAAAe,GAAA,CAAoB", "names": ["FieldType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r6", "form", "valid", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵlistener", "FormlyFieldTabs_li_3_ng_template_3_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "i_r3", "ɵɵnextContext", "index", "ctx_r9", "_r0", "ɵɵreference", "ɵɵresetView", "nextTab", "ctx_r7", "<PERSON><PERSON><PERSON><PERSON>", "field", "fieldGroup", "FormlyFieldTabs_li_3_ng_template_3_button_4_Template_button_click_0_listener", "_r14", "ctx_r13", "prevTab", "ɵɵelement", "ɵɵtemplate", "FormlyFieldTabs_li_3_ng_template_3_button_2_Template", "FormlyFieldTabs_li_3_ng_template_3_button_3_Template", "FormlyFieldTabs_li_3_ng_template_3_button_4_Template", "tab_r2", "last_r4", "FormlyFieldTabs_li_3_Template_a_click_1_listener", "restoredCtx", "_r18", "ctx_r17", "onChangeTab", "model", "FormlyFieldTabs_li_3_ng_template_3_Template", "ctx_r1", "props", "label", "ɵɵsanitizeHtml", "FormlyFieldTabs", "constructor", "activeID", "ngOnInit", "hasOwnProperty", "onNext", "onPrev", "key", "formControl", "every", "f", "ngbNav", "current_index", "console", "log", "mark<PERSON><PERSON>ristine", "select", "_", "t", "_2", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "FormlyFieldTabs_Template", "rf", "ctx", "FormlyFieldTabs_li_3_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\tabs-type\\tabs-type.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\tabs-type\\tabs-type.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input } from '@angular/core';\r\nimport { FieldType, FormlyFieldConfig } from '@ngx-formly/core';\r\n@Component({\r\n  selector: 'formly-field-tabs',\r\n  templateUrl: './tabs-type.component.html',\r\n})\r\nexport class FormlyFieldTabs extends FieldType {\r\n  activeID = 0;\r\n  onNext: any;\r\n  onPrev: any;\r\n  onChangeTab: any;\r\n  ngOnInit() {\r\n    if (this.field.props.hasOwnProperty('onNext')) {\r\n      this.onNext = this.field.props.onNext;\r\n    }\r\n\r\n    if (this.field.props.hasOwnProperty('onPrev')) {\r\n      this.onPrev = this.field.props.onPrev;\r\n    }\r\n\r\n    if (this.field.props.hasOwnProperty('onChangeTab')) {\r\n      this.onChangeTab = this.field.props.onChangeTab;\r\n    }\r\n  }\r\n  isValid(field: FormlyFieldConfig): boolean {\r\n    if (field.key) {\r\n      return field.formControl.valid;\r\n    }\r\n    return field.fieldGroup\r\n      ? field.fieldGroup.every((f) => this.isValid(f))\r\n      : true;\r\n  }\r\n\r\n  nextTab(ngbNav: any, current_index: number) {\r\n    console.log('Next current_index', current_index);\r\n    if (current_index >= 0) {\r\n      this.form.markAsPristine();\r\n      this.activeID = current_index + 1;\r\n      ngbNav.select(this.activeID);\r\n      if (this.onNext) this.onNext(this.activeID, this.model);\r\n      if (this.onChangeTab) this.onChangeTab(this.activeID, this.model);\r\n    }\r\n  }\r\n\r\n  prevTab(ngbNav: any, current_index: number) {\r\n    if (current_index > 0) {\r\n      this.activeID = current_index - 1;\r\n      ngbNav.select(this.activeID);\r\n      if (this.onPrev) this.onPrev(this.activeID, this.model);\r\n      if (this.onChangeTab) this.onChangeTab(this.activeID, this.model);\r\n    }\r\n  }\r\n}\r\n", "<div>\r\n    <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs\">\r\n        <li [ngbNavItem]=\"i\" *ngFor=\"let tab of field.fieldGroup; let i = index; let last = last\"\r\n            [disabled]=\"i !== 0 && !isValid(field.fieldGroup[i - 1])\">\r\n            <a ngbNavLink [innerHTML]=\"tab.props.label|translate\" (click)=\"onChangeTab(i,model)\"></a>\r\n            <ng-template ngbNavContent>\r\n                <formly-field [field]=\"tab\"></formly-field>\r\n                <div class=\"d-flex justify-content-between flex-row-reverse\">\r\n                    <button *ngIf=\"last\" class=\"btn btn-primary\" [disabled]=\"!form.valid\"\r\n                        type=\"submit\">{{\"Submit\"|translate}}</button>\r\n                    <button *ngIf=\"!last\" class=\"btn btn-primary\" type=\"button\" (click)=\"nextTab(nav,i)\"\r\n                        [disabled]=\"!isValid(field.fieldGroup[i])\">{{'Next'|translate}}</button>\r\n                    <button *ngIf=\"i !== 0\" class=\"btn btn-primary\" type=\"button\"\r\n                        (click)=\"prevTab(nav,i)\">{{'Previous'|translate}}</button>\r\n                </div>\r\n            </ng-template>\r\n        </li>\r\n    </ul>\r\n    <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
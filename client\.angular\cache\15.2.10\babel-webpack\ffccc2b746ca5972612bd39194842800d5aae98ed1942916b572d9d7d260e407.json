{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport Swal from 'sweetalert2';\nimport { Capacitor } from '@capacitor/core';\nimport { environment } from 'environments/environment';\nimport { StatusBar } from '@capacitor/status-bar';\nimport { NavigationBar } from '@hugotomazi/capacitor-navigation-bar';\nimport { prominent } from 'color.js';\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/loading.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"ngx-ui-tour-ng-bootstrap\";\nimport * as i5 from \"@core/services/config.service\";\nimport * as i6 from \"app/services/tournament.service\";\nimport * as i7 from \"@ngx-translate/core\";\nimport * as i8 from \"app/services/tutorial-tour.service\";\nimport * as i9 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i10 from \"app/components/overlays/overlays.service\";\nimport * as i11 from \"app/services/auth.service\";\nimport * as i12 from \"app/services/livekit.service\";\nimport * as i13 from \"app/services/commons.service\";\nimport * as i14 from \"app/services/world-time.service\";\nimport * as i15 from \"app/services/settings.service\";\nexport class BroadcastComponent {\n  onMessage(event) {\n    const receivedData = event.data;\n    // console.log('receivedData', receivedData);\n    if (receivedData.hasOwnProperty('is_back') && receivedData.is_back === true) {\n      this.removeIframe();\n    }\n  }\n  constructor(route, router, loading, _modalService, tourService, _coreConfigService, _tournamentService, _translateService, _tutorialTourService, _coreSidebarService, overlaysService, _authService, livekitService, commonService, worldTimeService, settingService) {\n    var _this = this;\n    this.route = route;\n    this.router = router;\n    this.loading = loading;\n    this._modalService = _modalService;\n    this.tourService = tourService;\n    this._coreConfigService = _coreConfigService;\n    this._tournamentService = _tournamentService;\n    this._translateService = _translateService;\n    this._tutorialTourService = _tutorialTourService;\n    this._coreSidebarService = _coreSidebarService;\n    this.overlaysService = overlaysService;\n    this._authService = _authService;\n    this.livekitService = livekitService;\n    this.commonService = commonService;\n    this.worldTimeService = worldTimeService;\n    this.settingService = settingService;\n    this.iframeUrl = `${environment.ezstreamUrl}/broadcast-iframe/`;\n    this.playback_url = '';\n    this.matchId = this.route.snapshot.paramMap.get('id');\n    this._coreConfigService.setConfig({\n      layout: {\n        navbar: {\n          hidden: true\n        },\n        menu: {\n          hidden: true\n        },\n        footer: {\n          hidden: true\n        },\n        enableLocalStorage: false\n      }\n    });\n    if (Capacitor.isNativePlatform()) {\n      ScreenOrientation.lock({\n        orientation: 'landscape'\n      });\n      this.hideStatusBar();\n      setInterval(() => {\n        StatusBar.getInfo().then( /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (info) {\n            if (info.visible) {\n              yield _this.hideStatusBar();\n            }\n          });\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }()).catch(err => {\n          console.log('error', err);\n        });\n      }, 4000);\n    }\n  }\n  hideStatusBar() {\n    return _asyncToGenerator(function* () {\n      yield StatusBar.hide();\n      yield NavigationBar.hide();\n    })();\n  }\n  ngOnInit() {\n    this.showUserAssignedToMatch();\n    this.getMatchById();\n  }\n  showUserAssignedToMatch() {\n    this._tournamentService.showUserAssignedToMatch(this.matchId).subscribe(res => {\n      console.log(res);\n      if (res.data.length > 0) {\n        this.token = res.data[0].token;\n        this.role = res.data[0].role;\n      }\n    });\n  }\n  getMetadata() {\n    this.settingService.getMetadaSettings().subscribe(res => {\n      this.metadata = res;\n      this.initIframeBroadcast();\n    });\n  }\n  getMatchById() {\n    this.loading.show();\n    this._tournamentService.getMatchById(this.matchId).subscribe(res => {\n      console.log('match', res);\n      this.match_info = res;\n      this.playback_url = this.commonService.getAzureHLSPlayBackUrlDummny(this.match_info.id);\n      this.getMetadata();\n    });\n  }\n  initIframeBroadcast() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loading.show();\n      let away_colors = (yield _this2.getColors(_this2.replaceURL(_this2.match_info.away_team.club?.logo))) || \"#000000\";\n      let home_colors = (yield _this2.getColors(_this2.replaceURL(_this2.match_info.home_team.club?.logo))) || \"#000000\";\n      let away_color = away_colors[1] == '#000000' ? away_colors[2] : away_colors[1];\n      let home_color = home_colors[1] == '#000000' ? home_colors[2] : home_colors[1];\n      if (away_color == home_color) {\n        away_color = away_colors[3];\n        if (away_color == home_color) {\n          away_color = away_colors[0] != '#ffffff' ? away_colors[0] : away_colors[4];\n        }\n      }\n      let away_team = {\n        color: away_color.toString(),\n        logo: _this2.match_info.away_team.club?.logo,\n        name: _this2.match_info.away_team.name\n      };\n      let home_team = {\n        color: home_color.toString(),\n        logo: _this2.match_info.home_team.club?.logo,\n        name: _this2.match_info.home_team.name\n      };\n      console.log('home_team', home_team);\n      console.log('away_team', away_team);\n      let title = `${_this2.match_info.stage?.tournament?.name} - ${_this2.match_info.round_name}`;\n      _this2.livekitConfig = JSON.parse(localStorage.getItem('livekit'));\n      _this2.iframeUrl += _this2.matchId;\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'title', title);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'token', _this2.token);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'role', _this2.role);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'host', JSON.parse(localStorage.getItem('livekit')).host);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'home_team', JSON.stringify(home_team));\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'away_team', JSON.stringify(away_team));\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'webhookURL', `${environment.apiUrl}/livestream-webhook`);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'deleteRoleURL', `${environment.apiUrl}/delete-role-webhook`);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'user_id', _this2._authService.currentUserValue.id);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'playback_url', _this2.playback_url);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'metadata', JSON.stringify(_this2.metadata));\n      console.log(\"iframeUrl\", _this2.iframeUrl);\n      let iframe = document.createElement('iframe');\n      iframe.src = _this2.iframeUrl;\n      iframe.classList.add('iframe-fullscreen');\n      iframe.setAttribute('allow', 'autoplay; fullscreen; picture-in-picture;camera *;microphone *');\n      iframe.style.backgroundColor = 'transparent';\n      iframe.setAttribute('crossorigin', 'anonymous');\n      // append iframe to html\n      _this2.iframe = iframe;\n      iframe.onload = () => {\n        setTimeout(() => {\n          _this2.loading.dismiss();\n        }, 2000);\n      };\n      iframe.onerror = () => {\n        _this2.loading.dismiss();\n        Swal.fire({\n          icon: 'error',\n          title: 'Error',\n          text: 'Error loading the broadcast page. Please try again'\n        }).then(() => {\n          _this2.removeIframe();\n        });\n      };\n      document.body.parentElement.appendChild(_this2.iframe);\n    })();\n  }\n  addParamToUrl(url, key, value) {\n    let urlObj = new URL(url);\n    urlObj.searchParams.append(key, value);\n    return urlObj.toString();\n  }\n  replaceURL(url) {\n    let origin = window.location.origin;\n    let byPassURL = `${environment.proxyUrl}/proxy?url=`;\n    // check url different from origin\n    if (url.indexOf(origin) === -1) {\n      return byPassURL + url;\n    } else {\n      return url;\n    }\n  }\n  removeIframe() {\n    if (this.iframe) {\n      this.iframe.remove();\n    }\n    this.router.navigate(['/streaming']);\n  }\n  getColors(src) {\n    return _asyncToGenerator(function* () {\n      try {\n        let color = yield prominent(src, {\n          amount: 5,\n          format: 'hex'\n        });\n        return color;\n      } catch (error) {\n        return null;\n      }\n    })();\n  }\n  ngOnDestroy() {\n    this.removeIframe();\n  }\n  static #_ = this.ɵfac = function BroadcastComponent_Factory(t) {\n    return new (t || BroadcastComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.LoadingService), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.TourService), i0.ɵɵdirectiveInject(i5.CoreConfigService), i0.ɵɵdirectiveInject(i6.TournamentService), i0.ɵɵdirectiveInject(i7.TranslateService), i0.ɵɵdirectiveInject(i8.TutorialTourService), i0.ɵɵdirectiveInject(i9.CoreSidebarService), i0.ɵɵdirectiveInject(i10.OverlaysService), i0.ɵɵdirectiveInject(i11.AuthService), i0.ɵɵdirectiveInject(i12.LivekitService), i0.ɵɵdirectiveInject(i13.CommonsService), i0.ɵɵdirectiveInject(i14.WorldTimeService), i0.ɵɵdirectiveInject(i15.SettingsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BroadcastComponent,\n    selectors: [[\"app-broadcast\"]],\n    hostBindings: function BroadcastComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"message\", function BroadcastComponent_message_HostBindingHandler($event) {\n          return ctx.onMessage($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 0,\n    vars: 0,\n    template: function BroadcastComponent_Template(rf, ctx) {},\n    styles: [\".iframe-fullscreen {\\n  width: 100%;\\n  height: 100vh;\\n  border: none;\\n  margin: 0;\\n  padding: 0;\\n  overflow: hidden;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  z-index: 9999;\\n  background-color: white;\\n  display: block;\\n  transition: all 0.3s ease;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3RyZWFtaW5nL2Jyb2FkY2FzdC9icm9hZGNhc3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7RUFDQSxTQUFBO0VBQ0EsVUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuaWZyYW1lLWZ1bGxzY3JlZW4ge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIG1hcmdpbjogMDtcclxuICBwYWRkaW5nOiAwO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgcG9zaXRpb246IGZpeGVkO1xyXG4gIHRvcDogMDtcclxuICBsZWZ0OiAwO1xyXG4gIHotaW5kZXg6IDk5OTk7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAeA,OAAOA,IAAwB,MAAM,aAAa;AAClD,SAASC,SAAS,QAAQ,iBAAiB;AAiB3C,SAASC,WAAW,QAAQ,0BAA0B;AAItD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,aAAa,QAAQ,sCAAsC;AAMpE,SAASC,SAAS,QAAQ,UAAU;AAEpC,SAASC,iBAAiB,QAAQ,+BAA+B;;;;;;;;;;;;;;;;;AAajE,OAAM,MAAOC,kBAAkB;EAE7BC,SAASA,CAACC,KAAmB;IAC3B,MAAMC,YAAY,GAAGD,KAAK,CAACE,IAAI;IAC/B;IACA,IACED,YAAY,CAACE,cAAc,CAAC,SAAS,CAAC,IACtCF,YAAY,CAACG,OAAO,KAAK,IAAI,EAC7B;MACA,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAYAC,YACSC,KAAqB,EACrBC,MAAc,EACbC,OAAuB,EACxBC,aAAuB,EACvBC,WAAwB,EACvBC,kBAAqC,EACtCC,kBAAqC,EACrCC,iBAAmC,EACnCC,oBAAyC,EACzCC,mBAAuC,EACvCC,eAAgC,EAChCC,YAAyB,EACzBC,cAA8B,EAC9BC,aAA6B,EAC7BC,gBAAkC,EAClCC,cAA+B;IAAA,IAAAC,KAAA;IAf/B,KAAAhB,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,OAAO,GAAPA,OAAO;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IAzBvB,KAAAE,SAAS,GAAG,GAAG/B,WAAW,CAACgC,WAAW,oBAAoB;IAO1D,KAAAC,YAAY,GAAG,EAAE;IAoBf,IAAI,CAACC,OAAO,GAAG,IAAI,CAACpB,KAAK,CAACqB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAGrD,IAAI,CAAClB,kBAAkB,CAACmB,SAAS,CAAC;MAChCC,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,MAAM,EAAE;SACT;QACDC,IAAI,EAAE;UACJD,MAAM,EAAE;SACT;QACDE,MAAM,EAAE;UACNF,MAAM,EAAE;SACT;QACDG,kBAAkB,EAAE;;KAEvB,CAAC;IACF,IAAI7C,SAAS,CAAC8C,gBAAgB,EAAE,EAAE;MAChCzC,iBAAiB,CAAC0C,IAAI,CAAC;QAAEC,WAAW,EAAE;MAAW,CAAE,CAAC;MACpD,IAAI,CAACC,aAAa,EAAE;MACpBC,WAAW,CAAC,MAAK;QACfhD,SAAS,CAACiD,OAAO,EAAE,CAChBC,IAAI;UAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAC,WAAOC,IAAI,EAAI;YACnB,IAAIA,IAAI,CAACC,OAAO,EAAE;cAChB,MAAMzB,KAAI,CAACkB,aAAa,EAAE;;UAE9B,CAAC;UAAA,iBAAAQ,EAAA;YAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC,CACDC,KAAK,CAAEC,GAAG,IAAI;UACbC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,GAAG,CAAC;QAC3B,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAAC;;EAKZ;EAEMZ,aAAaA,CAAA;IAAA,OAAAK,iBAAA;MACjB,MAAMpD,SAAS,CAAC8D,IAAI,EAAE;MACtB,MAAM7D,aAAa,CAAC6D,IAAI,EAAE;IAAC;EAC7B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,YAAY,EAAE;EAGrB;EAEAD,uBAAuBA,CAAA;IACrB,IAAI,CAAC7C,kBAAkB,CACpB6C,uBAAuB,CAAC,IAAI,CAAC/B,OAAO,CAAC,CACrCiC,SAAS,CAAEC,GAAQ,IAAI;MACtBP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;MAChB,IAAIA,GAAG,CAAC3D,IAAI,CAAC4D,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACC,KAAK,GAAGF,GAAG,CAAC3D,IAAI,CAAC,CAAC,CAAC,CAAC6D,KAAK;QAC9B,IAAI,CAACC,IAAI,GAAGH,GAAG,CAAC3D,IAAI,CAAC,CAAC,CAAC,CAAC8D,IAAI;;IAEhC,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC3C,cAAc,CAAC4C,iBAAiB,EAAE,CAACN,SAAS,CAAEC,GAAQ,IAAI;MAC7D,IAAI,CAACM,QAAQ,GAAGN,GAAG;MACnB,IAAI,CAACO,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EACAT,YAAYA,CAAA;IACV,IAAI,CAAClD,OAAO,CAAC4D,IAAI,EAAE;IACnB,IAAI,CAACxD,kBAAkB,CAAC8C,YAAY,CAAC,IAAI,CAAChC,OAAO,CAAC,CAACiC,SAAS,CAAEC,GAAG,IAAI;MACnEP,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEM,GAAG,CAAC;MACzB,IAAI,CAACS,UAAU,GAAGT,GAAG;MACrB,IAAI,CAACnC,YAAY,GAAG,IAAI,CAACN,aAAa,CAACmD,4BAA4B,CACjE,IAAI,CAACD,UAAU,CAACE,EAAE,CACnB;MACD,IAAI,CAACP,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEMG,mBAAmBA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAA3B,iBAAA;MACvB2B,MAAI,CAAChE,OAAO,CAAC4D,IAAI,EAAE;MACnB,IAAIK,WAAW,GAAG,OAAMD,MAAI,CAACE,SAAS,CACpCF,MAAI,CAACG,UAAU,CAACH,MAAI,CAACH,UAAU,CAACO,SAAS,CAACC,IAAI,EAAEC,IAAI,CAAC,CACtD,KAAI,SAAS;MAEd,IAAIC,WAAW,GAAG,OAAMP,MAAI,CAACE,SAAS,CACpCF,MAAI,CAACG,UAAU,CAACH,MAAI,CAACH,UAAU,CAACW,SAAS,CAACH,IAAI,EAAEC,IAAI,CAAC,CACtD,KAAI,SAAS;MAEd,IAAIG,UAAU,GACZR,WAAW,CAAC,CAAC,CAAC,IAAI,SAAS,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;MAC/D,IAAIS,UAAU,GACZH,WAAW,CAAC,CAAC,CAAC,IAAI,SAAS,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;MAC/D,IAAIE,UAAU,IAAIC,UAAU,EAAE;QAC5BD,UAAU,GAAGR,WAAW,CAAC,CAAC,CAAC;QAC3B,IAAIQ,UAAU,IAAIC,UAAU,EAAE;UAC5BD,UAAU,GACRR,WAAW,CAAC,CAAC,CAAC,IAAI,SAAS,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;;;MAInE,IAAIG,SAAS,GAAS;QACpBO,KAAK,EAAEF,UAAU,CAACG,QAAQ,EAAE;QAC5BN,IAAI,EAAEN,MAAI,CAACH,UAAU,CAACO,SAAS,CAACC,IAAI,EAAEC,IAAI;QAC1CO,IAAI,EAAEb,MAAI,CAACH,UAAU,CAACO,SAAS,CAACS;OACjC;MAED,IAAIL,SAAS,GAAS;QACpBG,KAAK,EAAED,UAAU,CAACE,QAAQ,EAAE;QAC5BN,IAAI,EAAEN,MAAI,CAACH,UAAU,CAACW,SAAS,CAACH,IAAI,EAAEC,IAAI;QAC1CO,IAAI,EAAEb,MAAI,CAACH,UAAU,CAACW,SAAS,CAACK;OACjC;MAEDhC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0B,SAAS,CAAC;MACnC3B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsB,SAAS,CAAC;MAEnC,IAAIU,KAAK,GAAG,GAAGd,MAAI,CAACH,UAAU,CAACkB,KAAK,EAAEC,UAAU,EAAEH,IAAI,MAAMb,MAAI,CAACH,UAAU,CAACoB,UAAU,EAAE;MAExFjB,MAAI,CAACkB,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC;MAIhEtB,MAAI,CAACjD,SAAS,IAAIiD,MAAI,CAAC9C,OAAO;MAC9B8C,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CAACvB,MAAI,CAACjD,SAAS,EAAE,OAAO,EAAE+D,KAAK,CAAC;MACnEd,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CAACvB,MAAI,CAACjD,SAAS,EAAE,OAAO,EAAEiD,MAAI,CAACV,KAAK,CAAC;MACxEU,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CAACvB,MAAI,CAACjD,SAAS,EAAE,MAAM,EAAEiD,MAAI,CAACT,IAAI,CAAC;MACtES,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,MAAM,EACNoE,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC,CAACE,IAAI,CACjD;MACDxB,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,WAAW,EACXoE,IAAI,CAACM,SAAS,CAACjB,SAAS,CAAC,CAC1B;MAEDR,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,WAAW,EACXoE,IAAI,CAACM,SAAS,CAACrB,SAAS,CAAC,CAC1B;MAEDJ,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,YAAY,EACZ,GAAG/B,WAAW,CAAC0G,MAAM,qBAAqB,CAC3C;MAED1B,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,eAAe,EACf,GAAG/B,WAAW,CAAC0G,MAAM,sBAAsB,CAC5C;MACD1B,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,SAAS,EACTiD,MAAI,CAACvD,YAAY,CAACkF,gBAAgB,CAAC5B,EAAE,CACtC;MACDC,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,cAAc,EACdiD,MAAI,CAAC/C,YAAY,CAClB;MACD+C,MAAI,CAACjD,SAAS,GAAGiD,MAAI,CAACuB,aAAa,CACjCvB,MAAI,CAACjD,SAAS,EACd,UAAU,EACVoE,IAAI,CAACM,SAAS,CAACzB,MAAI,CAACN,QAAQ,CAAC,CAC9B;MACDb,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkB,MAAI,CAACjD,SAAS,CAAC;MACxC,IAAI6E,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC7CF,MAAM,CAACG,GAAG,GAAG/B,MAAI,CAACjD,SAAS;MAC3B6E,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;MACzCL,MAAM,CAACM,YAAY,CACjB,OAAO,EACP,gEAAgE,CACjE;MACDN,MAAM,CAACO,KAAK,CAACC,eAAe,GAAG,aAAa;MAC5CR,MAAM,CAACM,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC;MAC/C;MACAlC,MAAI,CAAC4B,MAAM,GAAGA,MAAM;MACpBA,MAAM,CAACS,MAAM,GAAG,MAAK;QACnBC,UAAU,CAAC,MAAK;UACdtC,MAAI,CAAChE,OAAO,CAACuG,OAAO,EAAE;QACxB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDX,MAAM,CAACY,OAAO,GAAG,MAAK;QACpBxC,MAAI,CAAChE,OAAO,CAACuG,OAAO,EAAE;QACtBzH,IAAI,CAAC2H,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACb5B,KAAK,EAAE,OAAO;UACd6B,IAAI,EAAE;SACP,CAAC,CAACxE,IAAI,CAAC,MAAK;UACX6B,MAAI,CAACpE,YAAY,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC;MAEDiG,QAAQ,CAACe,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC9C,MAAI,CAAC4B,MAAM,CAAC;IAAC;EACvD;EAEAL,aAAaA,CAACwB,GAAG,EAAEC,GAAG,EAAEC,KAAK;IAC3B,IAAIC,MAAM,GAAG,IAAIC,GAAG,CAACJ,GAAG,CAAC;IACzBG,MAAM,CAACE,YAAY,CAACC,MAAM,CAACL,GAAG,EAAEC,KAAK,CAAC;IACtC,OAAOC,MAAM,CAACtC,QAAQ,EAAE;EAC1B;EAEAT,UAAUA,CAAC4C,GAAW;IACpB,IAAIO,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACF,MAAM;IACnC,IAAIG,SAAS,GAAG,GAAGzI,WAAW,CAAC0I,QAAQ,aAAa;IACpD;IACA,IAAIX,GAAG,CAACY,OAAO,CAACL,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9B,OAAOG,SAAS,GAAGV,GAAG;KACvB,MAAM;MACL,OAAOA,GAAG;;EAEd;EAEAnH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgG,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACgC,MAAM,EAAE;;IAEtB,IAAI,CAAC7H,MAAM,CAAC8H,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEM3D,SAASA,CAAC6B,GAAG;IAAA,OAAA1D,iBAAA;MACjB,IAAI;QACF,IAAIsC,KAAK,SAASxF,SAAS,CAAC4G,GAAG,EAAE;UAAE+B,MAAM,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAE,CAAC;QAC9D,OAAOpD,KAAK;OACb,CAAC,OAAOqD,KAAK,EAAE;QACd,OAAO,IAAI;;IACZ;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrI,YAAY,EAAE;EACrB;EAAC,QAAAsI,CAAA;qBArRU7I,kBAAkB,EAAA8I,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,QAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAU,EAAA,CAAAC,iBAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAAd,EAAA,CAAAC,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAAhB,EAAA,CAAAC,iBAAA,CAAAgB,EAAA,CAAAC,mBAAA,GAAAlB,EAAA,CAAAC,iBAAA,CAAAkB,EAAA,CAAAC,kBAAA,GAAApB,EAAA,CAAAC,iBAAA,CAAAoB,GAAA,CAAAC,eAAA,GAAAtB,EAAA,CAAAC,iBAAA,CAAAsB,GAAA,CAAAC,WAAA,GAAAxB,EAAA,CAAAC,iBAAA,CAAAwB,GAAA,CAAAC,cAAA,GAAA1B,EAAA,CAAAC,iBAAA,CAAA0B,GAAA,CAAAC,cAAA,GAAA5B,EAAA,CAAAC,iBAAA,CAAA4B,GAAA,CAAAC,gBAAA,GAAA9B,EAAA,CAAAC,iBAAA,CAAA8B,GAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA;UAAlB/K,kBAAkB;IAAAgL,SAAA;IAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAlBC,GAAA,CAAAnL,SAAA,CAAAoL,MAAA,CAAiB;QAAA,UAAAvC,EAAA,CAAAwC,eAAA", "names": ["<PERSON><PERSON>", "Capacitor", "environment", "StatusBar", "NavigationBar", "prominent", "ScreenOrientation", "BroadcastComponent", "onMessage", "event", "receivedData", "data", "hasOwnProperty", "is_back", "removeIframe", "constructor", "route", "router", "loading", "_modalService", "tourService", "_coreConfigService", "_tournamentService", "_translateService", "_tutorialTourService", "_coreSidebarService", "overlaysService", "_authService", "livekitService", "commonService", "worldTimeService", "settingService", "_this", "iframeUrl", "ezstreamUrl", "playback_url", "matchId", "snapshot", "paramMap", "get", "setConfig", "layout", "navbar", "hidden", "menu", "footer", "enableLocalStorage", "isNativePlatform", "lock", "orientation", "hideStatusBar", "setInterval", "getInfo", "then", "_ref", "_asyncToGenerator", "info", "visible", "_x", "apply", "arguments", "catch", "err", "console", "log", "hide", "ngOnInit", "showUserAssignedToMatch", "getMatchById", "subscribe", "res", "length", "token", "role", "getMetadata", "getMetadaSettings", "metadata", "initIframeBroadcast", "show", "match_info", "getAzureHLSPlayBackUrlDummny", "id", "_this2", "away_colors", "getColors", "replaceURL", "away_team", "club", "logo", "home_colors", "home_team", "away_color", "home_color", "color", "toString", "name", "title", "stage", "tournament", "round_name", "livekitConfig", "JSON", "parse", "localStorage", "getItem", "addParamToUrl", "host", "stringify", "apiUrl", "currentUserValue", "iframe", "document", "createElement", "src", "classList", "add", "setAttribute", "style", "backgroundColor", "onload", "setTimeout", "dismiss", "onerror", "fire", "icon", "text", "body", "parentElement", "append<PERSON><PERSON><PERSON>", "url", "key", "value", "url<PERSON>bj", "URL", "searchParams", "append", "origin", "window", "location", "byPassURL", "proxyUrl", "indexOf", "remove", "navigate", "amount", "format", "error", "ngOnDestroy", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "LoadingService", "i3", "NgbModal", "i4", "TourService", "i5", "CoreConfigService", "i6", "TournamentService", "i7", "TranslateService", "i8", "TutorialTourService", "i9", "CoreSidebarService", "i10", "OverlaysService", "i11", "AuthService", "i12", "LivekitService", "i13", "CommonsService", "i14", "WorldTimeService", "i15", "SettingsService", "_2", "selectors", "hostBindings", "BroadcastComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\broadcast\\broadcast.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostListener,\r\n  Input,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { Red5Service, Red5StreamConfig } from 'app/services/red5.service';\r\nimport Swal, { SweetAlertIcon } from 'sweetalert2';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { AppConfig, coreConfig } from 'app/app-config';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport moment from 'moment';\r\nimport { BatteryInfo, Device } from '@capacitor/device';\r\nimport { UpdateMatchDetailsComponent } from 'app/pages/league-tournament/modal-update-match-details/update-match-details.component';\r\nimport { Location, formatNumber } from '@angular/common';\r\nimport { IStepOption, TourService } from 'ngx-ui-tour-ng-bootstrap';\r\nimport { TutorialTourService } from 'app/services/tutorial-tour.service';\r\nimport { BroadcastService } from 'app/services/broadcast.service';\r\nimport { MuxService } from 'app/services/mux.service';\r\nimport { OverlaysService } from 'app/components/overlays/overlays.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Camera } from '@capacitor/camera';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { environment } from 'environments/environment';\r\nimport { LivekitService, RoomMetadata } from 'app/services/livekit.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { UnoOverlaysService } from 'app/services/uno-overlays.service';\r\nimport { StatusBar } from '@capacitor/status-bar';\r\nimport { NavigationBar } from '@hugotomazi/capacitor-navigation-bar';\r\nimport { WorldTimeService } from 'app/services/world-time.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { s } from '@fullcalendar/core/internal-common';\r\nimport { prominent } from 'color.js';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\r\n\r\ndeclare var window: any;\r\ninterface LiveKitConfig {\r\n  host: string;\r\n  wslURL: string;\r\n}\r\n@Component({\r\n  selector: 'app-broadcast',\r\n  templateUrl: './broadcast.component.html',\r\n  styleUrls: ['./broadcast.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class BroadcastComponent implements OnInit {\r\n  @HostListener('window:message', ['$event'])\r\n  onMessage(event: MessageEvent): void {\r\n    const receivedData = event.data;\r\n    // console.log('receivedData', receivedData);\r\n    if (\r\n      receivedData.hasOwnProperty('is_back') &&\r\n      receivedData.is_back === true\r\n    ) {\r\n      this.removeIframe();\r\n    }\r\n  }\r\n\r\n  matchId: any;\r\n  iframeUrl = `${environment.ezstreamUrl}/broadcast-iframe/`;\r\n  iframe: HTMLIFrameElement;\r\n  match_info: any;\r\n  token: string;\r\n  livekitConfig: LiveKitConfig;\r\n  role: string;\r\n  broadcast_data: any;\r\n  playback_url = '';\r\n  metadata: any;\r\n  constructor(\r\n    public route: ActivatedRoute,\r\n    public router: Router,\r\n    private loading: LoadingService,\r\n    public _modalService: NgbModal,\r\n    public tourService: TourService,\r\n    private _coreConfigService: CoreConfigService,\r\n    public _tournamentService: TournamentService,\r\n    public _translateService: TranslateService,\r\n    public _tutorialTourService: TutorialTourService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public overlaysService: OverlaysService,\r\n    public _authService: AuthService,\r\n    public livekitService: LivekitService,\r\n    public commonService: CommonsService,\r\n    public worldTimeService: WorldTimeService,\r\n    public settingService: SettingsService\r\n  ) {\r\n    this.matchId = this.route.snapshot.paramMap.get('id');\r\n\r\n  \r\n    this._coreConfigService.setConfig({\r\n      layout: {\r\n        navbar: {\r\n          hidden: true,\r\n        },\r\n        menu: {\r\n          hidden: true,\r\n        },\r\n        footer: {\r\n          hidden: true,\r\n        },\r\n        enableLocalStorage: false,\r\n      },\r\n    });\r\n    if (Capacitor.isNativePlatform()) {\r\n      ScreenOrientation.lock({ orientation: 'landscape' });\r\n      this.hideStatusBar();\r\n      setInterval(() => {\r\n        StatusBar.getInfo()\r\n          .then(async (info) => {\r\n            if (info.visible) {\r\n              await this.hideStatusBar();\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log('error', err);\r\n          });\r\n      }, 4000);\r\n    }\r\n\r\n  \r\n\r\n  }\r\n\r\n  async hideStatusBar() {\r\n    await StatusBar.hide();\r\n    await NavigationBar.hide();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.showUserAssignedToMatch();\r\n    this.getMatchById();\r\n\r\n   \r\n  }\r\n\r\n  showUserAssignedToMatch() {\r\n    this._tournamentService\r\n      .showUserAssignedToMatch(this.matchId)\r\n      .subscribe((res: any) => {\r\n        console.log(res);\r\n        if (res.data.length > 0) {\r\n          this.token = res.data[0].token;\r\n          this.role = res.data[0].role;\r\n        }\r\n      });\r\n  }\r\n\r\n  getMetadata() {\r\n    this.settingService.getMetadaSettings().subscribe((res: any) => {\r\n      this.metadata = res;\r\n      this.initIframeBroadcast();\r\n    })\r\n  }\r\n  getMatchById() {\r\n    this.loading.show();\r\n    this._tournamentService.getMatchById(this.matchId).subscribe((res) => {\r\n      console.log('match', res);\r\n      this.match_info = res;\r\n      this.playback_url = this.commonService.getAzureHLSPlayBackUrlDummny(\r\n        this.match_info.id\r\n      );\r\n      this.getMetadata();\r\n    });\r\n  }\r\n\r\n  async initIframeBroadcast() {\r\n    this.loading.show();\r\n    let away_colors = await this.getColors(\r\n      this.replaceURL(this.match_info.away_team.club?.logo)\r\n    ) || \"#000000\";\r\n\r\n    let home_colors = await this.getColors(\r\n      this.replaceURL(this.match_info.home_team.club?.logo) \r\n    ) || \"#000000\";\r\n\r\n    let away_color =\r\n      away_colors[1] == '#000000' ? away_colors[2] : away_colors[1];\r\n    let home_color =\r\n      home_colors[1] == '#000000' ? home_colors[2] : home_colors[1];\r\n    if (away_color == home_color) {\r\n      away_color = away_colors[3];\r\n      if (away_color == home_color) {\r\n        away_color =\r\n          away_colors[0] != '#ffffff' ? away_colors[0] : away_colors[4];\r\n      }\r\n    }\r\n\r\n    let away_team: Team = {\r\n      color: away_color.toString(),\r\n      logo: this.match_info.away_team.club?.logo,\r\n      name: this.match_info.away_team.name,\r\n    };\r\n\r\n    let home_team: Team = {\r\n      color: home_color.toString(),\r\n      logo: this.match_info.home_team.club?.logo,\r\n      name: this.match_info.home_team.name,\r\n    };\r\n\r\n    console.log('home_team', home_team);\r\n    console.log('away_team', away_team);\r\n\r\n    let title = `${this.match_info.stage?.tournament?.name} - ${this.match_info.round_name}`;\r\n\r\n    this.livekitConfig = JSON.parse(localStorage.getItem('livekit'));\r\n\r\n    \r\n\r\n    this.iframeUrl += this.matchId;\r\n    this.iframeUrl = this.addParamToUrl(this.iframeUrl, 'title', title);\r\n    this.iframeUrl = this.addParamToUrl(this.iframeUrl, 'token', this.token);\r\n    this.iframeUrl = this.addParamToUrl(this.iframeUrl, 'role', this.role);\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'host',\r\n      JSON.parse(localStorage.getItem('livekit')).host\r\n    );\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'home_team',\r\n      JSON.stringify(home_team)\r\n    );\r\n\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'away_team',\r\n      JSON.stringify(away_team)\r\n    );\r\n\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'webhookURL',\r\n      `${environment.apiUrl}/livestream-webhook`\r\n    );\r\n\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'deleteRoleURL',\r\n      `${environment.apiUrl}/delete-role-webhook`\r\n    );\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'user_id',\r\n      this._authService.currentUserValue.id\r\n    );\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'playback_url',\r\n      this.playback_url\r\n    );\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'metadata',\r\n      JSON.stringify(this.metadata)\r\n    )\r\n    console.log(\"iframeUrl\", this.iframeUrl);\r\n    let iframe = document.createElement('iframe');\r\n    iframe.src = this.iframeUrl;\r\n    iframe.classList.add('iframe-fullscreen');\r\n    iframe.setAttribute(\r\n      'allow',\r\n      'autoplay; fullscreen; picture-in-picture;camera *;microphone *'\r\n    );\r\n    iframe.style.backgroundColor = 'transparent';\r\n    iframe.setAttribute('crossorigin', 'anonymous');\r\n    // append iframe to html\r\n    this.iframe = iframe;\r\n    iframe.onload = () => {\r\n      setTimeout(() => {\r\n        this.loading.dismiss();\r\n      }, 2000);\r\n    };\r\n\r\n    iframe.onerror = () => {\r\n      this.loading.dismiss();\r\n      Swal.fire({\r\n        icon: 'error',\r\n        title: 'Error',\r\n        text: 'Error loading the broadcast page. Please try again',\r\n      }).then(() => {\r\n        this.removeIframe();\r\n      });\r\n    };\r\n\r\n    document.body.parentElement.appendChild(this.iframe);\r\n  }\r\n\r\n  addParamToUrl(url, key, value) {\r\n    let urlObj = new URL(url);\r\n    urlObj.searchParams.append(key, value);\r\n    return urlObj.toString();\r\n  }\r\n\r\n  replaceURL(url: string) {\r\n    let origin = window.location.origin;\r\n    let byPassURL = `${environment.proxyUrl}/proxy?url=`;\r\n    // check url different from origin\r\n    if (url.indexOf(origin) === -1) {\r\n      return byPassURL + url;\r\n    } else {\r\n      return url;\r\n    }\r\n  }\r\n\r\n  removeIframe() {\r\n    if (this.iframe) {\r\n      this.iframe.remove();\r\n    }\r\n    this.router.navigate(['/streaming']);\r\n  }\r\n\r\n  async getColors(src) {\r\n    try {\r\n      let color = await prominent(src, { amount: 5, format: 'hex' });\r\n      return color;\r\n    } catch (error) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.removeIframe();\r\n  }\r\n}\r\n\r\ninterface Team {\r\n  color: string;\r\n  logo: string;\r\n  name: string;\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
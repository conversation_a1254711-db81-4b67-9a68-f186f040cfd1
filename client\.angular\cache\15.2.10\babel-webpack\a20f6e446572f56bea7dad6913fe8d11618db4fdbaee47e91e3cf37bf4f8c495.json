{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport { ModalProcessRequestComponent } from './modal-process-request/modal-process-request.component';\nimport { AppConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"app/services/registration.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"app/services/season.service\";\nimport * as i7 from \"app/services/club.service\";\nimport * as i8 from \"app/services/loading.service\";\nimport * as i9 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i10 from \"app/services/commons.service\";\nimport * as i11 from \"app/services/user.service\";\nimport * as i12 from \"app/services/settings.service\";\nimport * as i13 from \"@angular/platform-browser\";\nimport * as i14 from \"angular-datatables\";\nimport * as i15 from \"app/layout/components/content-header/content-header.component\";\nexport class PlayerUpdatesComponent {\n  constructor(route, _http, _registrationService, _translateService, modalService, seasonService, _clubService, _loadingService, _coreSidebarService, _commonsService, _userService, _settingsService, _titleService) {\n    this.route = route;\n    this._http = _http;\n    this._registrationService = _registrationService;\n    this._translateService = _translateService;\n    this.modalService = modalService;\n    this.seasonService = seasonService;\n    this._clubService = _clubService;\n    this._loadingService = _loadingService;\n    this._coreSidebarService = _coreSidebarService;\n    this._commonsService = _commonsService;\n    this._userService = _userService;\n    this._settingsService = _settingsService;\n    this._titleService = _titleService;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this._titleService.setTitle('Requests Update');\n  }\n  ngOnInit() {\n    this.setContentHeader();\n    this.initTable();\n  }\n  initTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      order: [[0, 'desc']],\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        this._http.post(`${environment.apiUrl}/players/player-updates`, dataTablesParameters).subscribe(resp => {\n          this._loadingService.dismiss();\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: 2\n      }, {\n        responsivePriority: 2,\n        targets: 6\n      }],\n      columns: [{\n        title: 'id',\n        data: 'id',\n        visible: false\n      }, {\n        title: 'id',\n        data: 'id',\n        render: (data, type, row) => {\n          return `<b>#${row.id}</b>`;\n        }\n      }, {\n        title: this._translateService.instant('Player name'),\n        data: 'player.user',\n        render: (data, type, row) => {\n          return this._userService.fullName(data);\n        }\n      }, {\n        title: this._translateService.instant('Parent name'),\n        data: 'player.primary_guardian.user',\n        render: (data, type, row) => {\n          return this._userService.fullName(data);\n        }\n      }, {\n        title: this._translateService.instant('Parent email'),\n        data: 'player.primary_guardian.user.email'\n      }, {\n        title: this._translateService.instant('Description'),\n        data: 'description'\n      }, {\n        title: this._translateService.instant('Status'),\n        data: 'status',\n        render: (data, type, row) => {\n          return `<span class=\" ${this._commonsService.getClassPlayerUpdateStatus(data)}\">${this._translateService.instant(data)}</span>`;\n        }\n      }, {\n        title: this._translateService.instant('Created at'),\n        data: 'created_at',\n        render: (data, type, row) => {\n          return moment(data).format('YYYY-MM-DD HH:mm:ss');\n        }\n      }, {\n        title: this._translateService.instant('Updated at'),\n        data: 'updated_at',\n        render: (data, type, row) => {\n          return moment(data).format('YYYY-MM-DD HH:mm:ss');\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: this._translateService.instant('Process Request'),\n          extend: 'selectedSingle',\n          action: (e, dt, node, config) => {\n            const selectedRow = dt.row({\n              selected: true\n            }).data();\n            // check status is pending\n            if (AppConfig.PLAYER_UPDATE_STATUS.pending !== selectedRow.status) {\n              Swal.fire(this._translateService.instant('Notification'), this._translateService.instant('Only pending request can be processed'), 'info');\n              return;\n            }\n            this.openModalProcessRequest(selectedRow);\n          }\n        }]\n      }\n    };\n  }\n  setContentHeader() {\n    this.contentHeader = {\n      headerTitle: this._translateService.instant('Requests Update'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Registrations',\n          isLink: false\n        }, {\n          name: this._translateService.instant('Requests Update'),\n          isLink: false\n        }]\n      }\n    };\n  }\n  openModalProcessRequest(rowData) {\n    let modalRef = this.modalService.open(ModalProcessRequestComponent, {\n      centered: true,\n      backdrop: 'static',\n      size: 'lg'\n    });\n    modalRef.componentInstance.rowData = rowData;\n    modalRef.result.then(result => {\n      if (result?.status) {\n        // reload table\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    });\n  }\n  ngAfterViewInit() {\n    this.dtTrigger.next(this.dtOptions);\n  }\n  static #_ = this.ɵfac = function PlayerUpdatesComponent_Factory(t) {\n    return new (t || PlayerUpdatesComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.RegistrationService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.SeasonService), i0.ɵɵdirectiveInject(i7.ClubService), i0.ɵɵdirectiveInject(i8.LoadingService), i0.ɵɵdirectiveInject(i9.CoreSidebarService), i0.ɵɵdirectiveInject(i10.CommonsService), i0.ɵɵdirectiveInject(i11.UserService), i0.ɵɵdirectiveInject(i12.SettingsService), i0.ɵɵdirectiveInject(i13.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PlayerUpdatesComponent,\n    selectors: [[\"app-player-updates\"]],\n    viewQuery: function PlayerUpdatesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 9,\n    vars: 3,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"]],\n    template: function PlayerUpdatesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵelement(6, \"div\", 6);\n        i0.ɵɵelementStart(7, \"div\");\n        i0.ɵɵelement(8, \"table\", 7);\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n      }\n    },\n    dependencies: [i14.DataTableDirective, i15.ContentHeaderComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAMA,SAASA,kBAAkB,QAAQ,oBAAoB;AASvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;AAQ3B,OAAM,MAAOC,sBAAsB;EAOjCC,YACUC,KAAqB,EACrBC,KAAiB,EACjBC,oBAAyC,EAC1CC,iBAAmC,EACnCC,YAAsB,EACtBC,aAA4B,EAC5BC,YAAyB,EACzBC,eAA+B,EAC/BC,mBAAuC,EACvCC,eAA+B,EAC/BC,YAAyB,EACzBC,gBAAiC,EACjCC,aAAoB;IAZnB,KAAAZ,KAAK,GAALA,KAAK;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAlBtB,KAAAC,SAAS,GAAQtB,kBAAkB;IACnC,KAAAuB,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAItB,OAAO,EAAe;IAkB1D,IAAI,CAACmB,aAAa,CAACI,QAAQ,CAAC,iBAAiB,CAAC;EAChD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACL,SAAS,GAAG;MACfM,GAAG,EAAE,IAAI,CAACX,eAAe,CAACY,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;MACpBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAAC1B,KAAK,CACP2B,IAAI,CACH,GAAGpC,WAAW,CAACqC,MAAM,yBAAyB,EAC9CH,oBAAoB,CACrB,CACAI,SAAS,CAAEC,IAAS,IAAI;UACvB,IAAI,CAACxB,eAAe,CAACyB,OAAO,EAAE;UAE9BL,QAAQ,CAAC;YACP;YACAM,YAAY,EAAEF,IAAI,CAACE,YAAY;YAC/BC,eAAe,EAAEH,IAAI,CAACG,eAAe;YACrCC,IAAI,EAAEJ,IAAI,CAACI;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAAC7B,eAAe,CAACY,iBAAiB,CAACkB,IAAI;MACrDC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,EACrC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,CACtC;MACDC,OAAO,EAAE,CACP;QACEC,KAAK,EAAE,IAAI;QACXT,IAAI,EAAE,IAAI;QACVU,OAAO,EAAE;OACV,EACD;QACED,KAAK,EAAE,IAAI;QACXT,IAAI,EAAE,IAAI;QACVW,MAAM,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,OAAOA,GAAG,CAACC,EAAE,MAAM;QAC5B;OACD,EACD;QACEL,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,aAAa,CAAC;QACpDf,IAAI,EAAE,aAAa;QACnBW,MAAM,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,IAAI,CAACtC,YAAY,CAACyC,QAAQ,CAAChB,IAAI,CAAC;QACzC;OACD,EACD;QACES,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,aAAa,CAAC;QACpDf,IAAI,EAAE,8BAA8B;QACpCW,MAAM,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,IAAI,CAACtC,YAAY,CAACyC,QAAQ,CAAChB,IAAI,CAAC;QACzC;OACD,EACD;QACES,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,cAAc,CAAC;QACrDf,IAAI,EAAE;OACP,EACD;QACES,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,aAAa,CAAC;QACpDf,IAAI,EAAE;OACP,EACD;QACES,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,QAAQ,CAAC;QAC/Cf,IAAI,EAAE,QAAQ;QACdW,MAAM,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,iBAAiB,IAAI,CAACvC,eAAe,CAAC2C,0BAA0B,CACrEjB,IAAI,CACL,KAAK,IAAI,CAAChC,iBAAiB,CAAC+C,OAAO,CAACf,IAAI,CAAC,SAAS;QACrD;OACD,EACD;QACES,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,YAAY,CAAC;QACnDf,IAAI,EAAE,YAAY;QAClBW,MAAM,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAOnD,MAAM,CAACsC,IAAI,CAAC,CAACkB,MAAM,CAAC,qBAAqB,CAAC;QACnD;OACD,EACD;QACET,KAAK,EAAE,IAAI,CAACzC,iBAAiB,CAAC+C,OAAO,CAAC,YAAY,CAAC;QACnDf,IAAI,EAAE,YAAY;QAClBW,MAAM,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAOnD,MAAM,CAACsC,IAAI,CAAC,CAACkB,MAAM,CAAC,qBAAqB,CAAC;QACnD;OACD,CACF;MACDC,OAAO,EAAE;QACPlC,GAAG,EAAE,IAAI,CAACX,eAAe,CAACY,iBAAiB,CAACiC,OAAO,CAAClC,GAAG;QACvDkC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,IAAI,CAACpD,iBAAiB,CAAC+C,OAAO,CAAC,iBAAiB,CAAC;UACvDM,MAAM,EAAE,gBAAgB;UACxBC,MAAM,EAAEA,CAACC,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,MAAMC,WAAW,GAAGH,EAAE,CAACX,GAAG,CAAC;cAAEe,QAAQ,EAAE;YAAI,CAAE,CAAC,CAAC5B,IAAI,EAAE;YACrD;YACA,IACExC,SAAS,CAACqE,oBAAoB,CAACC,OAAO,KAAKH,WAAW,CAACI,MAAM,EAC7D;cACAtE,IAAI,CAACuE,IAAI,CACP,IAAI,CAAChE,iBAAiB,CAAC+C,OAAO,CAAC,cAAc,CAAC,EAC9C,IAAI,CAAC/C,iBAAiB,CAAC+C,OAAO,CAC5B,uCAAuC,CACxC,EACD,MAAM,CACP;cACD;;YAEF,IAAI,CAACkB,uBAAuB,CAACN,WAAW,CAAC;UAC3C;SACD;;KAGN;EACH;EACA5C,gBAAgBA,CAAA;IACd,IAAI,CAACmD,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACnE,iBAAiB,CAAC+C,OAAO,CAAC,iBAAiB,CAAC;MAC9DqB,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVzB,IAAI,EAAE,EAAE;QACR0B,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,eAAe;UACrBC,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACvE,iBAAiB,CAAC+C,OAAO,CAAC,iBAAiB,CAAC;UACvDyB,MAAM,EAAE;SACT;;KAGN;EACH;EAEAP,uBAAuBA,CAACQ,OAAY;IAClC,IAAIC,QAAQ,GAAG,IAAI,CAACzE,YAAY,CAAC0E,IAAI,CAACpF,4BAA4B,EAAE;MAClEqF,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,QAAQ;MAClBC,IAAI,EAAE;KACP,CAAC;IAEFJ,QAAQ,CAACK,iBAAiB,CAACN,OAAO,GAAGA,OAAO;IAC5CC,QAAQ,CAACM,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,EAAEjB,MAAM,EAAE;QAClB;QACA,IAAI,CAACrD,SAAS,CAACwE,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;UAC5DA,UAAU,CAAC5D,IAAI,CAAC6D,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EACAC,eAAeA,CAAA;IACb,IAAI,CAACxE,SAAS,CAACyE,IAAI,CAAC,IAAI,CAAC1E,SAAS,CAAC;EACrC;EAAC,QAAA2E,CAAA;qBA1LU3F,sBAAsB,EAAA4F,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAAjB,EAAA,CAAAC,iBAAA,CAAAiB,EAAA,CAAAC,kBAAA,GAAAnB,EAAA,CAAAC,iBAAA,CAAAmB,GAAA,CAAAC,cAAA,GAAArB,EAAA,CAAAC,iBAAA,CAAAqB,GAAA,CAAAC,WAAA,GAAAvB,EAAA,CAAAC,iBAAA,CAAAuB,GAAA,CAAAC,eAAA,GAAAzB,EAAA,CAAAC,iBAAA,CAAAyB,GAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBxH,sBAAsB;IAAAyH,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBACtBnI,kBAAkB;;;;;;;;;;;;QC7B/BmG,EAAA,CAAAkC,cAAA,aAA+C;QAGvClC,EAAA,CAAAmC,SAAA,4BAAyE;QAEzEnC,EAAA,CAAAkC,cAAA,aAAiB;QAGLlC,EAAA,CAAAmC,SAAA,aACM;QACNnC,EAAA,CAAAkC,cAAA,UAAK;QACDlC,EAAA,CAAAmC,SAAA,eAEQ;QACZnC,EAAA,CAAAoC,YAAA,EAAM;;;QAXEpC,EAAA,CAAAqC,SAAA,GAA+B;QAA/BrC,EAAA,CAAAsC,UAAA,kBAAAL,GAAA,CAAAtD,aAAA,CAA+B;QAQlBqB,EAAA,CAAAqC,SAAA,GAAuB;QAAvBrC,EAAA,CAAAsC,UAAA,cAAAL,GAAA,CAAA7G,SAAA,CAAuB,cAAA6G,GAAA,CAAA5G,SAAA", "names": ["DataTableDirective", "environment", "Subject", "ModalProcessRequestComponent", "AppConfig", "<PERSON><PERSON>", "moment", "PlayerUpdatesComponent", "constructor", "route", "_http", "_registrationService", "_translateService", "modalService", "seasonService", "_clubService", "_loadingService", "_coreSidebarService", "_commonsService", "_userService", "_settingsService", "_titleService", "dtElement", "dtOptions", "dtTrigger", "setTitle", "ngOnInit", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initTable", "dom", "dataTableDefaults", "select", "order", "rowId", "ajax", "dataTablesParameters", "callback", "post", "apiUrl", "subscribe", "resp", "dismiss", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "columnDefs", "responsivePriority", "targets", "columns", "title", "visible", "render", "type", "row", "id", "instant", "fullName", "getClassPlayerUpdateStatus", "format", "buttons", "text", "extend", "action", "e", "dt", "node", "config", "selectedRow", "selected", "PLAYER_UPDATE_STATUS", "pending", "status", "fire", "openModalProcessRequest", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "rowData", "modalRef", "open", "centered", "backdrop", "size", "componentInstance", "result", "then", "dtInstance", "reload", "ngAfterViewInit", "next", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "HttpClient", "i3", "RegistrationService", "i4", "TranslateService", "i5", "NgbModal", "i6", "SeasonService", "i7", "ClubService", "i8", "LoadingService", "i9", "CoreSidebarService", "i10", "CommonsService", "i11", "UserService", "i12", "SettingsService", "i13", "Title", "_2", "selectors", "viewQuery", "PlayerUpdatesComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\player-updates\\player-updates.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\player-updates\\player-updates.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport { ModalProcessRequestComponent } from './modal-process-request/modal-process-request.component';\r\nimport { AppConfig } from 'app/app-config';\r\nimport Swal from 'sweetalert2';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-player-updates',\r\n  templateUrl: './player-updates.component.html',\r\n  styleUrls: ['./player-updates.component.scss'],\r\n})\r\nexport class PlayerUpdatesComponent implements OnInit {\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  public contentHeader: object;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private _http: HttpClient,\r\n    private _registrationService: RegistrationService,\r\n    public _translateService: TranslateService,\r\n    public modalService: NgbModal,\r\n    public seasonService: SeasonService,\r\n    public _clubService: ClubService,\r\n    public _loadingService: LoadingService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _commonsService: CommonsService,\r\n    public _userService: UserService,\r\n    public _settingsService: SettingsService,\r\n    public _titleService: Title\r\n  ) {\r\n    this._titleService.setTitle('Requests Update');\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.setContentHeader();\r\n    this.initTable();\r\n  }\r\n\r\n  initTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      order: [[0, 'desc']],\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/players/player-updates`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this._loadingService.dismiss();\r\n\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: 2 },\r\n        { responsivePriority: 2, targets: 6 },\r\n      ],\r\n      columns: [\r\n        {\r\n          title: 'id',\r\n          data: 'id',\r\n          visible: false,\r\n        },\r\n        {\r\n          title: 'id',\r\n          data: 'id',\r\n          render: (data, type, row) => {\r\n            return `<b>#${row.id}</b>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Player name'),\r\n          data: 'player.user',\r\n          render: (data, type, row) => {\r\n            return this._userService.fullName(data);\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Parent name'),\r\n          data: 'player.primary_guardian.user',\r\n          render: (data, type, row) => {\r\n            return this._userService.fullName(data);\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Parent email'),\r\n          data: 'player.primary_guardian.user.email',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Description'),\r\n          data: 'description',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Status'),\r\n          data: 'status',\r\n          render: (data, type, row) => {\r\n            return `<span class=\" ${this._commonsService.getClassPlayerUpdateStatus(\r\n              data\r\n            )}\">${this._translateService.instant(data)}</span>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Created at'),\r\n          data: 'created_at',\r\n          render: (data, type, row) => {\r\n            return moment(data).format('YYYY-MM-DD HH:mm:ss');\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Updated at'),\r\n          data: 'updated_at',\r\n          render: (data, type, row) => {\r\n            return moment(data).format('YYYY-MM-DD HH:mm:ss');\r\n          },\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: this._translateService.instant('Process Request'),\r\n            extend: 'selectedSingle',\r\n            action: (e, dt, node, config) => {\r\n              const selectedRow = dt.row({ selected: true }).data();\r\n              // check status is pending\r\n              if (\r\n                AppConfig.PLAYER_UPDATE_STATUS.pending !== selectedRow.status\r\n              ) {\r\n                Swal.fire(\r\n                  this._translateService.instant('Notification'),\r\n                  this._translateService.instant(\r\n                    'Only pending request can be processed'\r\n                  ),\r\n                  'info'\r\n                );\r\n                return;\r\n              }\r\n              this.openModalProcessRequest(selectedRow);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n  setContentHeader() {\r\n    this.contentHeader = {\r\n      headerTitle: this._translateService.instant('Requests Update'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Registrations',\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: this._translateService.instant('Requests Update'),\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  openModalProcessRequest(rowData: any) {\r\n    let modalRef = this.modalService.open(ModalProcessRequestComponent, {\r\n      centered: true,\r\n      backdrop: 'static',\r\n      size: 'lg',\r\n    });\r\n\r\n    modalRef.componentInstance.rowData = rowData;\r\n    modalRef.result.then((result) => {\r\n      if (result?.status) {\r\n        // reload table\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n        });\r\n      }\r\n    });\r\n  }\r\n  ngAfterViewInit(): void {\r\n    this.dtTrigger.next(this.dtOptions);\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <div class=\"content-body\">\r\n        <!-- content-header component -->\r\n        <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n        <div class=\"row\">\r\n            <div class=\"col-12\">\r\n                <div class=\"card\">\r\n                    <div class=\"card-header\">\r\n                    </div>\r\n                    <div>\r\n                        <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\"\r\n                            class=\"table border row-border hover\">\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n</div>\r\n<!-- \r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n    <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n    </app-editor-sidebar>\r\n</core-sidebar> -->"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
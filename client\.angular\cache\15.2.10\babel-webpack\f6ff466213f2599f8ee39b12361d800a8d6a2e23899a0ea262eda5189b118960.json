{"ast": null, "code": "const TimeoutErrorImpl = (() => {\n  function TimeoutErrorImpl() {\n    Error.call(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    return this;\n  }\n  TimeoutErrorImpl.prototype = Object.create(Error.prototype);\n  return TimeoutErrorImpl;\n})();\nexport const TimeoutError = TimeoutErrorImpl;", "map": {"version": 3, "names": ["TimeoutErrorImpl", "Error", "call", "message", "name", "prototype", "Object", "create", "TimeoutError"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/TimeoutError.js"], "sourcesContent": ["const TimeoutErrorImpl = (() => {\n    function TimeoutErrorImpl() {\n        Error.call(this);\n        this.message = 'Timeout has occurred';\n        this.name = 'TimeoutError';\n        return this;\n    }\n    TimeoutErrorImpl.prototype = Object.create(Error.prototype);\n    return TimeoutErrorImpl;\n})();\nexport const TimeoutError = TimeoutErrorImpl;\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,CAAC,MAAM;EAC5B,SAASA,gBAAgBA,CAAA,EAAG;IACxBC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,sBAAsB;IACrC,IAAI,CAACC,IAAI,GAAG,cAAc;IAC1B,OAAO,IAAI;EACf;EACAJ,gBAAgB,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,KAAK,CAACI,SAAS,CAAC;EAC3D,OAAOL,gBAAgB;AAC3B,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMQ,YAAY,GAAGR,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
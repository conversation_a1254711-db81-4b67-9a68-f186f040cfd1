{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function debounce(durationSelector) {\n  return source => source.lift(new DebounceOperator(durationSelector));\n}\nclass DebounceOperator {\n  constructor(durationSelector) {\n    this.durationSelector = durationSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new DebounceSubscriber(subscriber, this.durationSelector));\n  }\n}\nclass DebounceSubscriber extends OuterSubscriber {\n  constructor(destination, durationSelector) {\n    super(destination);\n    this.durationSelector = durationSelector;\n    this.hasValue = false;\n    this.durationSubscription = null;\n  }\n  _next(value) {\n    try {\n      const result = this.durationSelector.call(this, value);\n      if (result) {\n        this._tryNext(value, result);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n  _complete() {\n    this.emitValue();\n    this.destination.complete();\n  }\n  _tryNext(value, duration) {\n    let subscription = this.durationSubscription;\n    this.value = value;\n    this.hasValue = true;\n    if (subscription) {\n      subscription.unsubscribe();\n      this.remove(subscription);\n    }\n    subscription = subscribeToResult(this, duration);\n    if (subscription && !subscription.closed) {\n      this.add(this.durationSubscription = subscription);\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.emitValue();\n  }\n  notifyComplete() {\n    this.emitValue();\n  }\n  emitValue() {\n    if (this.hasValue) {\n      const value = this.value;\n      const subscription = this.durationSubscription;\n      if (subscription) {\n        this.durationSubscription = null;\n        subscription.unsubscribe();\n        this.remove(subscription);\n      }\n      this.value = null;\n      this.hasValue = false;\n      super._next(value);\n    }\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "subscribeToResult", "debounce", "durationSelector", "source", "lift", "DebounceOperator", "constructor", "call", "subscriber", "subscribe", "DebounceSubscriber", "destination", "hasValue", "durationSubscription", "_next", "value", "result", "_tryNext", "err", "error", "_complete", "emitValue", "complete", "duration", "subscription", "unsubscribe", "remove", "closed", "add", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "notifyComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/debounce.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function debounce(durationSelector) {\n    return (source) => source.lift(new DebounceOperator(durationSelector));\n}\nclass DebounceOperator {\n    constructor(durationSelector) {\n        this.durationSelector = durationSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DebounceSubscriber(subscriber, this.durationSelector));\n    }\n}\nclass DebounceSubscriber extends OuterSubscriber {\n    constructor(destination, durationSelector) {\n        super(destination);\n        this.durationSelector = durationSelector;\n        this.hasValue = false;\n        this.durationSubscription = null;\n    }\n    _next(value) {\n        try {\n            const result = this.durationSelector.call(this, value);\n            if (result) {\n                this._tryNext(value, result);\n            }\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n    _complete() {\n        this.emitValue();\n        this.destination.complete();\n    }\n    _tryNext(value, duration) {\n        let subscription = this.durationSubscription;\n        this.value = value;\n        this.hasValue = true;\n        if (subscription) {\n            subscription.unsubscribe();\n            this.remove(subscription);\n        }\n        subscription = subscribeToResult(this, duration);\n        if (subscription && !subscription.closed) {\n            this.add(this.durationSubscription = subscription);\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.emitValue();\n    }\n    notifyComplete() {\n        this.emitValue();\n    }\n    emitValue() {\n        if (this.hasValue) {\n            const value = this.value;\n            const subscription = this.durationSubscription;\n            if (subscription) {\n                this.durationSubscription = null;\n                subscription.unsubscribe();\n                this.remove(subscription);\n            }\n            this.value = null;\n            this.hasValue = false;\n            super._next(value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,QAAQA,CAACC,gBAAgB,EAAE;EACvC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,gBAAgB,CAACH,gBAAgB,CAAC,CAAC;AAC1E;AACA,MAAMG,gBAAgB,CAAC;EACnBC,WAAWA,CAACJ,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,kBAAkB,CAACF,UAAU,EAAE,IAAI,CAACN,gBAAgB,CAAC,CAAC;EACtF;AACJ;AACA,MAAMQ,kBAAkB,SAASX,eAAe,CAAC;EAC7CO,WAAWA,CAACK,WAAW,EAAET,gBAAgB,EAAE;IACvC,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACT,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACU,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,oBAAoB,GAAG,IAAI;EACpC;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI;MACA,MAAMC,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAACK,IAAI,CAAC,IAAI,EAAEQ,KAAK,CAAC;MACtD,IAAIC,MAAM,EAAE;QACR,IAAI,CAACC,QAAQ,CAACF,KAAK,EAAEC,MAAM,CAAC;MAChC;IACJ,CAAC,CACD,OAAOE,GAAG,EAAE;MACR,IAAI,CAACP,WAAW,CAACQ,KAAK,CAACD,GAAG,CAAC;IAC/B;EACJ;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAACV,WAAW,CAACW,QAAQ,CAAC,CAAC;EAC/B;EACAL,QAAQA,CAACF,KAAK,EAAEQ,QAAQ,EAAE;IACtB,IAAIC,YAAY,GAAG,IAAI,CAACX,oBAAoB;IAC5C,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACH,QAAQ,GAAG,IAAI;IACpB,IAAIY,YAAY,EAAE;MACdA,YAAY,CAACC,WAAW,CAAC,CAAC;MAC1B,IAAI,CAACC,MAAM,CAACF,YAAY,CAAC;IAC7B;IACAA,YAAY,GAAGxB,iBAAiB,CAAC,IAAI,EAAEuB,QAAQ,CAAC;IAChD,IAAIC,YAAY,IAAI,CAACA,YAAY,CAACG,MAAM,EAAE;MACtC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACf,oBAAoB,GAAGW,YAAY,CAAC;IACtD;EACJ;EACAK,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACb,SAAS,CAAC,CAAC;EACpB;EACAc,cAAcA,CAAA,EAAG;IACb,IAAI,CAACd,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,QAAQ,EAAE;MACf,MAAMG,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAMS,YAAY,GAAG,IAAI,CAACX,oBAAoB;MAC9C,IAAIW,YAAY,EAAE;QACd,IAAI,CAACX,oBAAoB,GAAG,IAAI;QAChCW,YAAY,CAACC,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,MAAM,CAACF,YAAY,CAAC;MAC7B;MACA,IAAI,CAACT,KAAK,GAAG,IAAI;MACjB,IAAI,CAACH,QAAQ,GAAG,KAAK;MACrB,KAAK,CAACE,KAAK,CAACC,KAAK,CAAC;IACtB;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
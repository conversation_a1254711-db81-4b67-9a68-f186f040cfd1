{"ast": null, "code": "import { of } from './of';\nimport { concatAll } from '../operators/concatAll';\nexport function concat(...observables) {\n  return concatAll()(of(...observables));\n}", "map": {"version": 3, "names": ["of", "concatAll", "concat", "observables"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/concat.js"], "sourcesContent": ["import { of } from './of';\nimport { concatAll } from '../operators/concatAll';\nexport function concat(...observables) {\n    return concatAll()(of(...observables));\n}\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,MAAM;AACzB,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAO,SAASC,MAAMA,CAAC,GAAGC,WAAW,EAAE;EACnC,OAAOF,SAAS,CAAC,CAAC,CAACD,EAAE,CAAC,GAAGG,WAAW,CAAC,CAAC;AAC1C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
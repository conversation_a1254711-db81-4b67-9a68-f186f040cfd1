{"ast": null, "code": "import { Subscriber } from './Subscriber';\nexport class InnerSubscriber extends Subscriber {\n  constructor(parent, outerValue, outerIndex) {\n    super();\n    this.parent = parent;\n    this.outerValue = outerValue;\n    this.outerIndex = outerIndex;\n    this.index = 0;\n  }\n  _next(value) {\n    this.parent.notifyNext(this.outerValue, value, this.outerIndex, this.index++, this);\n  }\n  _error(error) {\n    this.parent.notifyError(error, this);\n    this.unsubscribe();\n  }\n  _complete() {\n    this.parent.notifyComplete(this);\n    this.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "InnerSubscriber", "constructor", "parent", "outerValue", "outerIndex", "index", "_next", "value", "notifyNext", "_error", "error", "notifyError", "unsubscribe", "_complete", "notifyComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/InnerSubscriber.js"], "sourcesContent": ["import { Subscriber } from './Subscriber';\nexport class InnerSubscriber extends Subscriber {\n    constructor(parent, outerValue, outerIndex) {\n        super();\n        this.parent = parent;\n        this.outerValue = outerValue;\n        this.outerIndex = outerIndex;\n        this.index = 0;\n    }\n    _next(value) {\n        this.parent.notifyNext(this.outerValue, value, this.outerIndex, this.index++, this);\n    }\n    _error(error) {\n        this.parent.notifyError(error, this);\n        this.unsubscribe();\n    }\n    _complete() {\n        this.parent.notifyComplete(this);\n        this.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,MAAMC,eAAe,SAASD,UAAU,CAAC;EAC5CE,WAAWA,CAACC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAE;IACxC,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACL,MAAM,CAACM,UAAU,CAAC,IAAI,CAACL,UAAU,EAAEI,KAAK,EAAE,IAAI,CAACH,UAAU,EAAE,IAAI,CAACC,KAAK,EAAE,EAAE,IAAI,CAAC;EACvF;EACAI,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,CAACR,MAAM,CAACS,WAAW,CAACD,KAAK,EAAE,IAAI,CAAC;IACpC,IAAI,CAACE,WAAW,CAAC,CAAC;EACtB;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACX,MAAM,CAACY,cAAc,CAAC,IAAI,CAAC;IAChC,IAAI,CAACF,WAAW,CAAC,CAAC;EACtB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
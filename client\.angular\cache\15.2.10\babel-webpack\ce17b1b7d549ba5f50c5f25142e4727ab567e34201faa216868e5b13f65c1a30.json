{"ast": null, "code": "import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function repeatWhen(notifier) {\n  return source => source.lift(new RepeatWhenOperator(notifier));\n}\nclass RepeatWhenOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));\n  }\n}\nclass RepeatWhenSubscriber extends OuterSubscriber {\n  constructor(destination, notifier, source) {\n    super(destination);\n    this.notifier = notifier;\n    this.source = source;\n    this.sourceIsBeingSubscribedTo = true;\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.sourceIsBeingSubscribedTo = true;\n    this.source.subscribe(this);\n  }\n  notifyComplete(innerSub) {\n    if (this.sourceIsBeingSubscribedTo === false) {\n      return super.complete();\n    }\n  }\n  complete() {\n    this.sourceIsBeingSubscribedTo = false;\n    if (!this.isStopped) {\n      if (!this.retries) {\n        this.subscribeToRetries();\n      }\n      if (!this.retriesSubscription || this.retriesSubscription.closed) {\n        return super.complete();\n      }\n      this._unsubscribeAndRecycle();\n      this.notifications.next();\n    }\n  }\n  _unsubscribe() {\n    const {\n      notifications,\n      retriesSubscription\n    } = this;\n    if (notifications) {\n      notifications.unsubscribe();\n      this.notifications = null;\n    }\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = null;\n    }\n    this.retries = null;\n  }\n  _unsubscribeAndRecycle() {\n    const {\n      _unsubscribe\n    } = this;\n    this._unsubscribe = null;\n    super._unsubscribeAndRecycle();\n    this._unsubscribe = _unsubscribe;\n    return this;\n  }\n  subscribeToRetries() {\n    this.notifications = new Subject();\n    let retries;\n    try {\n      const {\n        notifier\n      } = this;\n      retries = notifier(this.notifications);\n    } catch (e) {\n      return super.complete();\n    }\n    this.retries = retries;\n    this.retriesSubscription = subscribeToResult(this, retries);\n  }\n}", "map": {"version": 3, "names": ["Subject", "OuterSubscriber", "subscribeToResult", "repeatWhen", "notifier", "source", "lift", "RepeatWhenOperator", "constructor", "call", "subscriber", "subscribe", "RepeatWhenSubscriber", "destination", "sourceIsBeingSubscribedTo", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "notifyComplete", "complete", "isStopped", "retries", "subscribeToRetries", "retriesSubscription", "closed", "_unsubscribeAndRecycle", "notifications", "next", "_unsubscribe", "unsubscribe", "e"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/repeatWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function repeatWhen(notifier) {\n    return (source) => source.lift(new RepeatWhenOperator(notifier));\n}\nclass RepeatWhenOperator {\n    constructor(notifier) {\n        this.notifier = notifier;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));\n    }\n}\nclass RepeatWhenSubscriber extends OuterSubscriber {\n    constructor(destination, notifier, source) {\n        super(destination);\n        this.notifier = notifier;\n        this.source = source;\n        this.sourceIsBeingSubscribedTo = true;\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.sourceIsBeingSubscribedTo = true;\n        this.source.subscribe(this);\n    }\n    notifyComplete(innerSub) {\n        if (this.sourceIsBeingSubscribedTo === false) {\n            return super.complete();\n        }\n    }\n    complete() {\n        this.sourceIsBeingSubscribedTo = false;\n        if (!this.isStopped) {\n            if (!this.retries) {\n                this.subscribeToRetries();\n            }\n            if (!this.retriesSubscription || this.retriesSubscription.closed) {\n                return super.complete();\n            }\n            this._unsubscribeAndRecycle();\n            this.notifications.next();\n        }\n    }\n    _unsubscribe() {\n        const { notifications, retriesSubscription } = this;\n        if (notifications) {\n            notifications.unsubscribe();\n            this.notifications = null;\n        }\n        if (retriesSubscription) {\n            retriesSubscription.unsubscribe();\n            this.retriesSubscription = null;\n        }\n        this.retries = null;\n    }\n    _unsubscribeAndRecycle() {\n        const { _unsubscribe } = this;\n        this._unsubscribe = null;\n        super._unsubscribeAndRecycle();\n        this._unsubscribe = _unsubscribe;\n        return this;\n    }\n    subscribeToRetries() {\n        this.notifications = new Subject();\n        let retries;\n        try {\n            const { notifier } = this;\n            retries = notifier(this.notifications);\n        }\n        catch (e) {\n            return super.complete();\n        }\n        this.retries = retries;\n        this.retriesSubscription = subscribeToResult(this, retries);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAE;EACjC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,kBAAkB,CAACH,QAAQ,CAAC,CAAC;AACpE;AACA,MAAMG,kBAAkB,CAAC;EACrBC,WAAWA,CAACJ,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,oBAAoB,CAACF,UAAU,EAAE,IAAI,CAACN,QAAQ,EAAEC,MAAM,CAAC,CAAC;EACxF;AACJ;AACA,MAAMO,oBAAoB,SAASX,eAAe,CAAC;EAC/CO,WAAWA,CAACK,WAAW,EAAET,QAAQ,EAAEC,MAAM,EAAE;IACvC,KAAK,CAACQ,WAAW,CAAC;IAClB,IAAI,CAACT,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACS,yBAAyB,GAAG,IAAI;EACzC;EACAC,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACN,yBAAyB,GAAG,IAAI;IACrC,IAAI,CAACT,MAAM,CAACM,SAAS,CAAC,IAAI,CAAC;EAC/B;EACAU,cAAcA,CAACD,QAAQ,EAAE;IACrB,IAAI,IAAI,CAACN,yBAAyB,KAAK,KAAK,EAAE;MAC1C,OAAO,KAAK,CAACQ,QAAQ,CAAC,CAAC;IAC3B;EACJ;EACAA,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,yBAAyB,GAAG,KAAK;IACtC,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE;MACjB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;QACf,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B;MACA,IAAI,CAAC,IAAI,CAACC,mBAAmB,IAAI,IAAI,CAACA,mBAAmB,CAACC,MAAM,EAAE;QAC9D,OAAO,KAAK,CAACL,QAAQ,CAAC,CAAC;MAC3B;MACA,IAAI,CAACM,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEF,aAAa;MAAEH;IAAoB,CAAC,GAAG,IAAI;IACnD,IAAIG,aAAa,EAAE;MACfA,aAAa,CAACG,WAAW,CAAC,CAAC;MAC3B,IAAI,CAACH,aAAa,GAAG,IAAI;IAC7B;IACA,IAAIH,mBAAmB,EAAE;MACrBA,mBAAmB,CAACM,WAAW,CAAC,CAAC;MACjC,IAAI,CAACN,mBAAmB,GAAG,IAAI;IACnC;IACA,IAAI,CAACF,OAAO,GAAG,IAAI;EACvB;EACAI,sBAAsBA,CAAA,EAAG;IACrB,MAAM;MAAEG;IAAa,CAAC,GAAG,IAAI;IAC7B,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,KAAK,CAACH,sBAAsB,CAAC,CAAC;IAC9B,IAAI,CAACG,YAAY,GAAGA,YAAY;IAChC,OAAO,IAAI;EACf;EACAN,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACI,aAAa,GAAG,IAAI7B,OAAO,CAAC,CAAC;IAClC,IAAIwB,OAAO;IACX,IAAI;MACA,MAAM;QAAEpB;MAAS,CAAC,GAAG,IAAI;MACzBoB,OAAO,GAAGpB,QAAQ,CAAC,IAAI,CAACyB,aAAa,CAAC;IAC1C,CAAC,CACD,OAAOI,CAAC,EAAE;MACN,OAAO,KAAK,CAACX,QAAQ,CAAC,CAAC;IAC3B;IACA,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,mBAAmB,GAAGxB,iBAAiB,CAAC,IAAI,EAAEsB,OAAO,CAAC;EAC/D;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
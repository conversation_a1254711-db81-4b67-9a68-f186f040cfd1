{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function buffer(closingNotifier) {\n  return function bufferOperatorFunction(source) {\n    return source.lift(new BufferOperator(closingNotifier));\n  };\n}\nclass BufferOperator {\n  constructor(closingNotifier) {\n    this.closingNotifier = closingNotifier;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new BufferSubscriber(subscriber, this.closingNotifier));\n  }\n}\nclass BufferSubscriber extends OuterSubscriber {\n  constructor(destination, closingNotifier) {\n    super(destination);\n    this.buffer = [];\n    this.add(subscribeToResult(this, closingNotifier));\n  }\n  _next(value) {\n    this.buffer.push(value);\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    const buffer = this.buffer;\n    this.buffer = [];\n    this.destination.next(buffer);\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "subscribeToResult", "buffer", "closingNotifier", "bufferOperatorFunction", "source", "lift", "BufferOperator", "constructor", "call", "subscriber", "subscribe", "BufferSubscriber", "destination", "add", "_next", "value", "push", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/buffer.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function buffer(closingNotifier) {\n    return function bufferOperatorFunction(source) {\n        return source.lift(new BufferOperator(closingNotifier));\n    };\n}\nclass BufferOperator {\n    constructor(closingNotifier) {\n        this.closingNotifier = closingNotifier;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferSubscriber(subscriber, this.closingNotifier));\n    }\n}\nclass BufferSubscriber extends OuterSubscriber {\n    constructor(destination, closingNotifier) {\n        super(destination);\n        this.buffer = [];\n        this.add(subscribeToResult(this, closingNotifier));\n    }\n    _next(value) {\n        this.buffer.push(value);\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        const buffer = this.buffer;\n        this.buffer = [];\n        this.destination.next(buffer);\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,MAAMA,CAACC,eAAe,EAAE;EACpC,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAE;IAC3C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,cAAc,CAACJ,eAAe,CAAC,CAAC;EAC3D,CAAC;AACL;AACA,MAAMI,cAAc,CAAC;EACjBC,WAAWA,CAACL,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACAM,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,gBAAgB,CAACF,UAAU,EAAE,IAAI,CAACP,eAAe,CAAC,CAAC;EACnF;AACJ;AACA,MAAMS,gBAAgB,SAASZ,eAAe,CAAC;EAC3CQ,WAAWA,CAACK,WAAW,EAAEV,eAAe,EAAE;IACtC,KAAK,CAACU,WAAW,CAAC;IAClB,IAAI,CAACX,MAAM,GAAG,EAAE;IAChB,IAAI,CAACY,GAAG,CAACb,iBAAiB,CAAC,IAAI,EAAEE,eAAe,CAAC,CAAC;EACtD;EACAY,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACd,MAAM,CAACe,IAAI,CAACD,KAAK,CAAC;EAC3B;EACAE,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,MAAMrB,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAI,CAACW,WAAW,CAACW,IAAI,CAACtB,MAAM,CAAC;EACjC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
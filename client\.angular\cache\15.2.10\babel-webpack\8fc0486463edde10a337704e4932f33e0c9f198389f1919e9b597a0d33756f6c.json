{"ast": null, "code": "import { Capacitor } from '@capacitor/core';\nimport { AppConfig } from 'app/app-config';\nimport { menu } from 'app/menu/menu';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/services/config.service\";\nimport * as i3 from \"app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@core/components/core-menu/core-menu.service\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"app/services/registration.service\";\nimport * as i8 from \"app/services/loading.service\";\nimport * as i9 from \"app/services/commons.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i13 from \"../../layout/components/content-background/content-background.component\";\nimport * as i14 from \"../../components/card-season/card-season.component\";\nimport * as i15 from \"./tutorial/tutorial.component\";\nimport * as i16 from \"./dashboard/dashboard.component\";\nimport * as i17 from \"@ngx-translate/core\";\nfunction HomeComponent_app_content_background_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-content-background\");\n  }\n}\nfunction HomeComponent_ng_container_4_div_4_div_8_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26)(1, \"span\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const player_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 2, \"Dob\"), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 4, player_r6.dob, \"dd-MMM-yyyy\"), \" \");\n  }\n}\nfunction HomeComponent_ng_container_4_div_4_div_8_p_14_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const player_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r11.group.name, \" \", i_r12 === player_r6.registrations[0].assigned_groups.length - 1 ? \"\" : \",\", \" \");\n  }\n}\nfunction HomeComponent_ng_container_4_div_4_div_8_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26)(1, \"span\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HomeComponent_ng_container_4_div_4_div_8_p_14_span_4_Template, 2, 2, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const player_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 2, \"Groups\"), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", player_r6.registrations[0].assigned_groups);\n  }\n}\nfunction HomeComponent_ng_container_4_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19)(3, \"div\", 20)(4, \"div\", 21)(5, \"img\", 22);\n    i0.ɵɵlistener(\"error\", function HomeComponent_ng_container_4_div_4_div_8_Template_img_error_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.onError($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 23)(7, \"div\", 24)(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"h4\", 26);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, HomeComponent_ng_container_4_div_4_div_8_p_13_Template, 6, 7, \"p\", 27);\n    i0.ɵɵtemplate(14, HomeComponent_ng_container_4_div_4_div_8_p_14_Template, 5, 4, \"p\", 27);\n    i0.ɵɵelementStart(15, \"p\", 26)(16, \"span\", 28);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const player_r6 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"card-player-\", player_r6.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", player_r6.photo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r4.commonService.getBadgeRegistration(player_r6.registrations[0].approval_status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 10, player_r6.registrations[0].approval_status).toUpperCase(), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.name_settings == null ? null : ctx_r4.name_settings.is_on) == 1 ? player_r6.user.first_name + \" \" + player_r6.user.last_name : player_r6.user.last_name + \" \" + player_r6.user.first_name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", player_r6.registrations[0].assigned_groups.length == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", player_r6.registrations[0].assigned_groups.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(18, 12, \"Club\"), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", player_r6.registrations[0].club.code, \" \");\n  }\n}\nfunction HomeComponent_ng_container_4_div_4_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"You haven't registered your children yet\"), \" \");\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"btn-block btn-flat-primary\": a0,\n    \"btn-primary\": a1\n  };\n};\nfunction HomeComponent_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 10)(4, \"h4\", 11);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 12);\n    i0.ɵɵtemplate(8, HomeComponent_ng_container_4_div_4_div_8_Template, 20, 14, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 14);\n    i0.ɵɵtemplate(10, HomeComponent_ng_container_4_div_4_p_10_Template, 3, 3, \"p\", 15);\n    i0.ɵɵelementStart(11, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function HomeComponent_ng_container_4_div_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.registerNow());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 5, \"player registration\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.players);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.players.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c0, ctx_r3.players.length !== 0, ctx_r3.players.length === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 7, ctx_r3.players.length !== 0 ? \"View details\" : \"Register Now\"), \" \");\n  }\n}\nfunction HomeComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5);\n    i0.ɵɵelement(3, \"card-season\", 6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, HomeComponent_ng_container_4_div_4_Template, 14, 12, \"div\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"season\", ctx_r1.currentSeason);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.players);\n  }\n}\nfunction HomeComponent_div_5_app_tutorial_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-tutorial\");\n  }\n}\nfunction HomeComponent_div_5_app_dashboard_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-dashboard\");\n  }\n}\nfunction HomeComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, HomeComponent_div_5_app_tutorial_1_Template, 1, 0, \"app-tutorial\", 2);\n    i0.ɵɵtemplate(2, HomeComponent_div_5_app_dashboard_2_Template, 1, 0, \"app-dashboard\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.haveMatch);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.haveMatch);\n  }\n}\nexport class HomeComponent {\n  constructor(_http, _coreConfigService, _authService, _router, _coreMenuService, _titleService, registrationService, loadingService, commonService) {\n    this._http = _http;\n    this._coreConfigService = _coreConfigService;\n    this._authService = _authService;\n    this._router = _router;\n    this._coreMenuService = _coreMenuService;\n    this._titleService = _titleService;\n    this.registrationService = registrationService;\n    this.loadingService = loadingService;\n    this.commonService = commonService;\n    this.permissions = {};\n    this.haveMatch = false;\n    if (Capacitor.isNativePlatform()) {\n      ScreenOrientation.lock({\n        orientation: 'portrait'\n      });\n    }\n    this._titleService.setTitle('Home');\n    this._coreConfigService.config = {\n      layout: {\n        navbar: {\n          hidden: false,\n          type: 'fixed-top'\n        },\n        menu: {\n          hidden: false\n        },\n        footer: {\n          hidden: true\n        },\n        customizer: false,\n        enableLocalStorage: true\n      }\n    };\n    this.permissions.registration = this._authService.currentUserValue.role.permissions.find(x => x.id == AppConfig.PERMISSIONS.registration);\n    if (this.permissions.registration) {\n      this.getSeason();\n    }\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\n  }\n  onError($event) {\n    $event.target.src = 'assets/images/logo/ezactive_1024x1024.png';\n  }\n  onClick() {\n    Swal.fire({\n      title: 'Click',\n      text: 'You clicked the button!',\n      icon: 'success',\n      confirmButtonText: 'Cool'\n    });\n  }\n  getSeason() {\n    this.loadingService.show();\n    return this.registrationService.getCurrentSeason().subscribe(season => {\n      if (season.length > 0) {\n        this.currentSeason = season[0];\n        this.registrationService.selectedSeason = this.currentSeason;\n        console.log(this.currentSeason);\n        this.getPlayers();\n      }\n    });\n  }\n  getPlayers() {\n    if (this.currentSeason) {\n      this.registrationService.getPlayersRegistrationStatus(this.currentSeason.id).subscribe(data => {\n        this.players = data.players;\n        console.log(this.players);\n      });\n    }\n  }\n  registerNow() {\n    if (this.currentSeason) {\n      console.log(`registration/season/${this.currentSeason.id}/select-player`);\n      this._router.navigate([`registration/season/${this.currentSeason.id}/select-player`]);\n    }\n  }\n  checkMatch() {\n    this._http.post(`${environment.apiUrl}/stage-matches/check`, {}).subscribe(resp => {\n      if (resp) {\n        this.haveMatch = resp.success;\n      }\n    });\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: 'Tutorial',\n      actionButton: false\n    };\n    this._authService.getProfile().subscribe(data => {\n      // console.log('data', data);\n      this._coreMenuService.unregister('main');\n      this._coreMenuService.register('main', menu);\n      this._coreMenuService.setCurrentMenu('main');\n      this.user = data;\n      // redirect to fixtues & results page if user is guest\n      // if (data.role.id == 7) {\n      //   this._router.navigate(['/leagues/select-tournament']);\n      // }\n    });\n\n    this.checkMatch();\n  }\n  checkPermissions() {\n    if (this.user) {\n      return this.user.role.id == AppConfig.USER_ROLES.admin;\n    }\n  }\n  static #_ = this.ɵfac = function HomeComponent_Factory(t) {\n    return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CoreConfigService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.CoreMenuService), i0.ɵɵdirectiveInject(i6.Title), i0.ɵɵdirectiveInject(i7.RegistrationService), i0.ɵɵdirectiveInject(i8.LoadingService), i0.ɵɵdirectiveInject(i9.CommonsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    decls: 6,\n    vars: 3,\n    consts: [[\"id\", \"home\", 1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [4, \"ngIf\"], [\"id\", \"home-page\"], [1, \"row\"], [1, \"col-12\", \"col-lg-6\"], [3, \"season\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-body\", \"p-1\"], [1, \"card-title\", \"text-capitalize\"], [1, \"row\", \"m-0\"], [\"class\", \"col-12 col-md-6 col-lg-4 px-0 px-md-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"flex-column\", \"align-items-center\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"type\", \"button\", \"id\", \"btn-go-to-register\", \"rippleEffect\", \"\", 1, \"btn\", 3, \"ngClass\", \"click\"], [1, \"col-12\", \"col-md-6\", \"col-lg-4\", \"px-0\", \"px-md-50\"], [1, \"card\", \"mb-1\", \"border-card\", 3, \"id\"], [1, \"card-body\", \"p-50\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar\", \"avatar-xl\", \"mr-75\", \"bg-transparent\"], [\"alt\", \"Avatar\", 1, \"rounded\", 2, \"object-fit\", \"contain\", 3, \"src\", \"error\"], [1, \"media-body\", \"my-auto\"], [1, \"d-flex\", \"align-items-end\", \"justify-content-end\"], [1, \"d-table\", \"badge\", \"badge-pill\"], [1, \"mb-50\"], [\"class\", \"mb-50\", 4, \"ngIf\"], [1, \"text-muted\"], [1, \"text-muted\", \"text-capitalize\"], [4, \"ngFor\", \"ngForOf\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, HomeComponent_app_content_background_2_Template, 1, 0, \"app-content-background\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3);\n        i0.ɵɵtemplate(4, HomeComponent_ng_container_4_Template, 5, 2, \"ng-container\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, HomeComponent_div_5_Template, 3, 2, \"div\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.checkPermissions());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.permissions.registration);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.checkPermissions());\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i11.DefaultClassDirective, i12.RippleEffectDirective, i13.ContentBackgroundComponent, i14.CardSeasonComponent, i15.TutorialComponent, i16.DashboardComponent, i10.DatePipe, i17.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,SAAS,QAAQ,iBAAiB;AAG3C,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,IAAI,QAAQ,eAAe;AAKpC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,iBAAiB,QAAQ,+BAA+B;;;;;;;;;;;;;;;;;;;;;ICV7DC,EAAA,CAAAC,SAAA,6BAA6E;;;;;IAmCrDD,EAAA,CAAAE,cAAA,YAA6E;IAClCF,EAAA,CAAAG,MAAA,GAAoB;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,GAEvE;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAFuCJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,mBAAoB;IAAQP,EAAA,CAAAK,SAAA,GAEvE;IAFuEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAQ,WAAA,OAAAC,SAAA,CAAAC,GAAA,sBAEvE;;;;;IAGEV,EAAA,CAAAE,cAAA,WAAkF;IAChFF,EAAA,CAAAG,MAAA,GAEF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAFLJ,EAAA,CAAAK,SAAA,GAEF;IAFEL,EAAA,CAAAW,kBAAA,MAAAC,QAAA,CAAAC,KAAA,CAAAC,IAAA,OAAAC,KAAA,KAAAN,SAAA,CAAAO,aAAA,IAAAC,eAAA,CAAAC,MAAA,qBAEF;;;;;IALFlB,EAAA,CAAAE,cAAA,YAA4E;IACjDF,EAAA,CAAAG,MAAA,GAAuB;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAmB,UAAA,IAAAC,6DAAA,mBAGO;IACTpB,EAAA,CAAAI,YAAA,EAAI;;;;IALuBJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,sBAAuB;IACzBP,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAqB,UAAA,YAAAZ,SAAA,CAAAO,aAAA,IAAAC,eAAA,CAA4C;;;;;;IA1B/EjB,EAAA,CAAAE,cAAA,cAAmF;IAMvEF,EAAA,CAAAsB,UAAA,mBAAAC,uEAAAC,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAF,OAAA,CAAAG,OAAA,CAAAN,MAAA,CAAe;IAAA,EAAC;IAD3BxB,EAAA,CAAAI,YAAA,EAC8B;IAEhCJ,EAAA,CAAAE,cAAA,cAAgC;IAI1BF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAE,cAAA,cAAkB;IAChBF,EAAA,CAAAG,MAAA,IAGF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAmB,UAAA,KAAAY,sDAAA,gBAGI;IACJ/B,EAAA,CAAAmB,UAAA,KAAAa,sDAAA,gBAMI;IACJhC,EAAA,CAAAE,cAAA,aAAiB;IACUF,EAAA,CAAAG,MAAA,IAAqB;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrDJ,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAjCuBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAiC,sBAAA,uBAAAxB,SAAA,CAAAyB,EAAA,KAA8B;IAIpDlC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAqB,UAAA,QAAAZ,SAAA,CAAA0B,KAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAAoB;IAMrBpC,EAAA,CAAAK,SAAA,GAAqF;IAArFL,EAAA,CAAAqC,UAAA,CAAAC,MAAA,CAAAC,aAAA,CAAAC,oBAAA,CAAA/B,SAAA,CAAAO,aAAA,IAAAyB,eAAA,EAAqF;IACrFzC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAO,WAAA,SAAAE,SAAA,CAAAO,aAAA,IAAAyB,eAAA,EAAAC,WAAA,QACF;IAGA1C,EAAA,CAAAK,SAAA,GAGF;IAHEL,EAAA,CAAAM,kBAAA,OAAAgC,MAAA,CAAAK,aAAA,kBAAAL,MAAA,CAAAK,aAAA,CAAAC,KAAA,SAAAnC,SAAA,CAAAoC,IAAA,CAAAC,UAAA,SAAArC,SAAA,CAAAoC,IAAA,CAAAE,SAAA,GAAAtC,SAAA,CAAAoC,IAAA,CAAAE,SAAA,SAAAtC,SAAA,CAAAoC,IAAA,CAAAC,UAAA,MAGF;IACkB9C,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAAqB,UAAA,SAAAZ,SAAA,CAAAO,aAAA,IAAAC,eAAA,CAAAC,MAAA,MAAyD;IAIzDlB,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAqB,UAAA,SAAAZ,SAAA,CAAAO,aAAA,IAAAC,eAAA,CAAAC,MAAA,KAAwD;IAQ/ClB,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAO,WAAA,sBAAqB;IAC9CP,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,SAAA,CAAAO,aAAA,IAAAgC,IAAA,CAAAC,IAAA,MACF;;;;;IASVjD,EAAA,CAAAE,cAAA,YAAmD;IACjDF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAO,WAAA,wDACF;;;;;;;;;;;;IAnDVP,EAAA,CAAAE,cAAA,aAAiC;IAIcF,EAAA,CAAAG,MAAA,GAAmC;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/EJ,EAAA,CAAAE,cAAA,cAAqB;IACnBF,EAAA,CAAAmB,UAAA,IAAA+B,iDAAA,oBAuCM;IACRlD,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,cAAmD;IACjDF,EAAA,CAAAmB,UAAA,KAAAgC,gDAAA,gBAEI;IACJnD,EAAA,CAAAE,cAAA,kBAEuC;IAArCF,EAAA,CAAAsB,UAAA,mBAAA8B,qEAAA;MAAApD,EAAA,CAAAyB,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAyB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACvBvD,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IApD4BJ,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAwD,iBAAA,CAAAxD,EAAA,CAAAO,WAAA,8BAAmC;IAEDP,EAAA,CAAAK,SAAA,GAAU;IAAVL,EAAA,CAAAqB,UAAA,YAAAoC,MAAA,CAAAC,OAAA,CAAU;IA2C1D1D,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAqB,UAAA,SAAAoC,MAAA,CAAAC,OAAA,CAAAxC,MAAA,OAA0B;IAI/ClB,EAAA,CAAAK,SAAA,GAAqG;IAArGL,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA2D,eAAA,IAAAC,GAAA,EAAAH,MAAA,CAAAC,OAAA,CAAAxC,MAAA,QAAAuC,MAAA,CAAAC,OAAA,CAAAxC,MAAA,QAAqG;IAErGlB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAO,WAAA,QAAAkD,MAAA,CAAAC,OAAA,CAAAxC,MAAA,+CACF;;;;;IA9DZlB,EAAA,CAAA6D,uBAAA,GAA+C;IAC7C7D,EAAA,CAAAE,cAAA,aAAiB;IAEbF,EAAA,CAAAC,SAAA,qBAAoD;IACtDD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAmB,UAAA,IAAA2C,2CAAA,mBA6DM;IAkCR9D,EAAA,CAAA+D,qBAAA,EAAe;;;;IAlGI/D,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAqB,UAAA,WAAA2C,MAAA,CAAAC,aAAA,CAAwB;IAGvBjE,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAqB,UAAA,SAAA2C,MAAA,CAAAN,OAAA,CAAa;;;;;IAkGjC1D,EAAA,CAAAC,SAAA,mBAAgD;;;;;IAChDD,EAAA,CAAAC,SAAA,oBAAiD;;;;;IAFnDD,EAAA,CAAAE,cAAA,UAAgC;IAC9BF,EAAA,CAAAmB,UAAA,IAAA+C,2CAAA,0BAAgD;IAChDlE,EAAA,CAAAmB,UAAA,IAAAgD,4CAAA,2BAAiD;IACnDnE,EAAA,CAAAI,YAAA,EAAM;;;;IAFWJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAqB,UAAA,UAAA+C,MAAA,CAAAC,SAAA,CAAgB;IACfrE,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAqB,UAAA,SAAA+C,MAAA,CAAAC,SAAA,CAAe;;;AD1FrC,OAAM,MAAOC,aAAa;EASxBC,YACSC,KAAiB,EAChBC,kBAAqC,EACtCC,YAAyB,EACxBC,OAAe,EAChBC,gBAAiC,EACjCC,aAAoB,EACpBC,mBAAwC,EACxCC,cAA8B,EAC9BxC,aAA6B;IAR7B,KAAAiC,KAAK,GAALA,KAAK;IACJ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,OAAO,GAAPA,OAAO;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAxC,aAAa,GAAbA,aAAa;IAdtB,KAAAyC,WAAW,GAAQ,EAAE;IACrB,KAAAX,SAAS,GAAY,KAAK;IAexB,IAAI3E,SAAS,CAACuF,gBAAgB,EAAE,EAAE;MAChClF,iBAAiB,CAACmF,IAAI,CAAC;QAAEC,WAAW,EAAE;MAAU,CAAE,CAAC;;IAErD,IAAI,CAACN,aAAa,CAACO,QAAQ,CAAC,MAAM,CAAC;IACnC,IAAI,CAACX,kBAAkB,CAACY,MAAM,GAAG;MAC/BC,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,MAAM,EAAE,KAAK;UACbC,IAAI,EAAE;SACP;QACD7F,IAAI,EAAE;UACJ4F,MAAM,EAAE;SACT;QACDE,MAAM,EAAE;UACNF,MAAM,EAAE;SACT;QACDG,UAAU,EAAE,KAAK;QACjBC,kBAAkB,EAAE;;KAEvB;IAED,IAAI,CAACZ,WAAW,CAACa,YAAY,GAC3B,IAAI,CAACnB,YAAY,CAACoB,gBAAgB,CAACC,IAAI,CAACf,WAAW,CAACgB,IAAI,CACrDC,CAAC,IAAKA,CAAC,CAAC/D,EAAE,IAAIvC,SAAS,CAACuG,WAAW,CAACL,YAAY,CAClD;IAEH,IAAI,IAAI,CAACb,WAAW,CAACa,YAAY,EAAE;MACjC,IAAI,CAACM,SAAS,EAAE;;IAElB,IAAI,CAACxD,aAAa,GAAGyD,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;EACxE;EAEAzE,OAAOA,CAACN,MAAM;IACZA,MAAM,CAACgF,MAAM,CAACC,GAAG,GAAG,2CAA2C;EACjE;EAEAC,OAAOA,CAAA;IACL5G,IAAI,CAAC6G,IAAI,CAAC;MACRC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,SAAS;MACfC,iBAAiB,EAAE;KACpB,CAAC;EACJ;EAEAZ,SAASA,CAAA;IACP,IAAI,CAACpB,cAAc,CAACiC,IAAI,EAAE;IAC1B,OAAO,IAAI,CAAClC,mBAAmB,CAACmC,gBAAgB,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MACtE,IAAIA,MAAM,CAACjG,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI,CAAC+C,aAAa,GAAGkD,MAAM,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACrC,mBAAmB,CAACsC,cAAc,GAAG,IAAI,CAACnD,aAAa;QAC5DoD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrD,aAAa,CAAC;QAC/B,IAAI,CAACsD,UAAU,EAAE;;IAErB,CAAC,CAAC;EACJ;EAEAA,UAAUA,CAAA;IACR,IAAI,IAAI,CAACtD,aAAa,EAAE;MACtB,IAAI,CAACa,mBAAmB,CACrB0C,4BAA4B,CAAC,IAAI,CAACvD,aAAa,CAAC/B,EAAE,CAAC,CACnDgF,SAAS,CAAEO,IAAI,IAAI;QAClB,IAAI,CAAC/D,OAAO,GAAG+D,IAAI,CAAC/D,OAAO;QAC3B2D,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5D,OAAO,CAAC;MAC3B,CAAC,CAAC;;EAER;EAEAH,WAAWA,CAAA;IACT,IAAI,IAAI,CAACU,aAAa,EAAE;MACtBoD,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAACrD,aAAa,CAAC/B,EAAE,gBAAgB,CAAC;MAEzE,IAAI,CAACyC,OAAO,CAAC+C,QAAQ,CAAC,CACpB,uBAAuB,IAAI,CAACzD,aAAa,CAAC/B,EAAE,gBAAgB,CAC7D,CAAC;;EAEN;EAEAyF,UAAUA,CAAA;IACR,IAAI,CAACnD,KAAK,CAACoD,IAAI,CAAM,GAAG/H,WAAW,CAACgI,MAAM,sBAAsB,EAAE,EAAE,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAC1F,IAAIA,IAAI,EAAE;QACR,IAAI,CAACzD,SAAS,GAAGyD,IAAI,CAACC,OAAO;;IAEjC,CAAC,CACF;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,UAAU;MACvBC,YAAY,EAAE;KACf;IACD,IAAI,CAACzD,YAAY,CAAC0D,UAAU,EAAE,CAAClB,SAAS,CAAEO,IAAS,IAAI;MACrD;MACA,IAAI,CAAC7C,gBAAgB,CAACyD,UAAU,CAAC,MAAM,CAAC;MACxC,IAAI,CAACzD,gBAAgB,CAAC0D,QAAQ,CAAC,MAAM,EAAE1I,IAAI,CAAC;MAC5C,IAAI,CAACgF,gBAAgB,CAAC2D,cAAc,CAAC,MAAM,CAAC;MAE5C,IAAI,CAAC1F,IAAI,GAAG4E,IAAI;MAEhB;MACA;MACA;MACA;IACF,CAAC,CAAC;;IACF,IAAI,CAACE,UAAU,EAAE;EACnB;EACAa,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC3F,IAAI,EAAE;MACb,OAAO,IAAI,CAACA,IAAI,CAACkD,IAAI,CAAC7D,EAAE,IAAIvC,SAAS,CAAC8I,UAAU,CAACC,KAAK;;EAE1D;EAAC,QAAAC,CAAA;qBAnIUrE,aAAa,EAAAtE,EAAA,CAAA4I,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA9I,EAAA,CAAA4I,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAhJ,EAAA,CAAA4I,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlJ,EAAA,CAAA4I,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAApJ,EAAA,CAAA4I,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAtJ,EAAA,CAAA4I,iBAAA,CAAAW,EAAA,CAAAC,KAAA,GAAAxJ,EAAA,CAAA4I,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAA1J,EAAA,CAAA4I,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAA4I,iBAAA,CAAAiB,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAAbzF,aAAa;IAAA0F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCxB1BtK,EAAA,CAAAE,cAAA,aAAyD;QAMrDF,EAAA,CAAAmB,UAAA,IAAAqJ,+CAAA,oCAA6E;QAE7ExK,EAAA,CAAAE,cAAA,iBAAwB;QACtBF,EAAA,CAAAmB,UAAA,IAAAsJ,qCAAA,0BAqGe;QACjBzK,EAAA,CAAAI,YAAA,EAAU;QACVJ,EAAA,CAAAmB,UAAA,IAAAuJ,4BAAA,iBAGM;QAER1K,EAAA,CAAAI,YAAA,EAAM;;;QA/GqBJ,EAAA,CAAAK,SAAA,GAAyB;QAAzBL,EAAA,CAAAqB,UAAA,UAAAkJ,GAAA,CAAA/B,gBAAA,GAAyB;QAGjCxI,EAAA,CAAAK,SAAA,GAA8B;QAA9BL,EAAA,CAAAqB,UAAA,SAAAkJ,GAAA,CAAAvF,WAAA,CAAAa,YAAA,CAA8B;QAuGzC7F,EAAA,CAAAK,SAAA,GAAwB;QAAxBL,EAAA,CAAAqB,UAAA,SAAAkJ,GAAA,CAAA/B,gBAAA,GAAwB", "names": ["Capacitor", "AppConfig", "menu", "environment", "<PERSON><PERSON>", "ScreenOrientation", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵpipeBind2", "player_r6", "dob", "ɵɵtextInterpolate2", "item_r11", "group", "name", "i_r12", "registrations", "assigned_groups", "length", "ɵɵtemplate", "HomeComponent_ng_container_4_div_4_div_8_p_14_span_4_Template", "ɵɵproperty", "ɵɵlistener", "HomeComponent_ng_container_4_div_4_div_8_Template_img_error_5_listener", "$event", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "onError", "HomeComponent_ng_container_4_div_4_div_8_p_13_Template", "HomeComponent_ng_container_4_div_4_div_8_p_14_Template", "ɵɵpropertyInterpolate1", "id", "photo", "ɵɵsanitizeUrl", "ɵɵclassMap", "ctx_r4", "commonService", "getBadgeRegistration", "approval_status", "toUpperCase", "name_settings", "is_on", "user", "first_name", "last_name", "club", "code", "HomeComponent_ng_container_4_div_4_div_8_Template", "HomeComponent_ng_container_4_div_4_p_10_Template", "HomeComponent_ng_container_4_div_4_Template_button_click_11_listener", "_r18", "ctx_r17", "registerNow", "ɵɵtextInterpolate", "ctx_r3", "players", "ɵɵpureFunction2", "_c0", "ɵɵelementContainerStart", "HomeComponent_ng_container_4_div_4_Template", "ɵɵelementContainerEnd", "ctx_r1", "currentSeason", "HomeComponent_div_5_app_tutorial_1_Template", "HomeComponent_div_5_app_dashboard_2_Template", "ctx_r2", "haveMatch", "HomeComponent", "constructor", "_http", "_coreConfigService", "_authService", "_router", "_coreMenuService", "_titleService", "registrationService", "loadingService", "permissions", "isNativePlatform", "lock", "orientation", "setTitle", "config", "layout", "navbar", "hidden", "type", "footer", "customizer", "enableLocalStorage", "registration", "currentUserValue", "role", "find", "x", "PERMISSIONS", "getSeason", "JSON", "parse", "localStorage", "getItem", "target", "src", "onClick", "fire", "title", "text", "icon", "confirmButtonText", "show", "getCurrentSeason", "subscribe", "season", "selectedS<PERSON>on", "console", "log", "getPlayers", "getPlayersRegistrationStatus", "data", "navigate", "checkMatch", "post", "apiUrl", "resp", "success", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "getProfile", "unregister", "register", "setCurrentMenu", "checkPermissions", "USER_ROLES", "admin", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CoreConfigService", "i3", "AuthService", "i4", "Router", "i5", "CoreMenuService", "i6", "Title", "i7", "RegistrationService", "i8", "LoadingService", "i9", "CommonsService", "_2", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_app_content_background_2_Template", "HomeComponent_ng_container_4_Template", "HomeComponent_div_5_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\home\\home.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { Router } from '@angular/router';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { CoreMenuService } from '@core/components/core-menu/core-menu.service';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { User } from 'app/interfaces/user';\r\nimport { menu } from 'app/menu/menu';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { environment } from 'environments/environment';\r\nimport Swal from 'sweetalert2';\r\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  public contentHeader: object;\r\n  currentSeason: any;\r\n  players: any[];\r\n  permissions: any = {};\r\n  haveMatch: boolean = false;\r\n  public user:User;\r\n  public name_settings : any;\r\n\r\n  constructor(\r\n    public _http: HttpClient,\r\n    private _coreConfigService: CoreConfigService,\r\n    public _authService: AuthService,\r\n    private _router: Router,\r\n    public _coreMenuService: CoreMenuService,\r\n    public _titleService: Title,\r\n    public registrationService: RegistrationService,\r\n    public loadingService: LoadingService,\r\n    public commonService: CommonsService\r\n  ) {\r\n    if (Capacitor.isNativePlatform()) {\r\n      ScreenOrientation.lock({ orientation: 'portrait' });\r\n    }\r\n    this._titleService.setTitle('Home');\r\n    this._coreConfigService.config = {\r\n      layout: {\r\n        navbar: {\r\n          hidden: false,\r\n          type: 'fixed-top',\r\n        },\r\n        menu: {\r\n          hidden: false,\r\n        },\r\n        footer: {\r\n          hidden: true,\r\n        },\r\n        customizer: false,\r\n        enableLocalStorage: true,\r\n      },\r\n    };\r\n\r\n    this.permissions.registration =\r\n      this._authService.currentUserValue.role.permissions.find(\r\n        (x) => x.id == AppConfig.PERMISSIONS.registration\r\n      );\r\n\r\n    if (this.permissions.registration) {\r\n      this.getSeason();\r\n    }\r\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n  }\r\n\r\n  onError($event) {\r\n    $event.target.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n  }\r\n\r\n  onClick() {\r\n    Swal.fire({\r\n      title: 'Click',\r\n      text: 'You clicked the button!',\r\n      icon: 'success',\r\n      confirmButtonText: 'Cool',\r\n    });\r\n  }\r\n\r\n  getSeason() {\r\n    this.loadingService.show();\r\n    return this.registrationService.getCurrentSeason().subscribe((season) => {\r\n      if (season.length > 0) {\r\n        this.currentSeason = season[0];\r\n        this.registrationService.selectedSeason = this.currentSeason;\r\n        console.log(this.currentSeason);\r\n        this.getPlayers();\r\n      }\r\n    });\r\n  }\r\n\r\n  getPlayers() {\r\n    if (this.currentSeason) {\r\n      this.registrationService\r\n        .getPlayersRegistrationStatus(this.currentSeason.id)\r\n        .subscribe((data) => {\r\n          this.players = data.players;\r\n          console.log(this.players);\r\n        });\r\n    }\r\n  }\r\n\r\n  registerNow() {\r\n    if (this.currentSeason) {\r\n      console.log(`registration/season/${this.currentSeason.id}/select-player`);\r\n\r\n      this._router.navigate([\r\n        `registration/season/${this.currentSeason.id}/select-player`,\r\n      ]);\r\n    }\r\n  }\r\n\r\n  checkMatch() {\r\n    this._http.post<any>(`${environment.apiUrl}/stage-matches/check`, {}).subscribe((resp: any) => {\r\n        if (resp) {\r\n          this.haveMatch = resp.success;\r\n        }\r\n      }\r\n    )\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.contentHeader = {\r\n      headerTitle: 'Tutorial',\r\n      actionButton: false,\r\n    };\r\n    this._authService.getProfile().subscribe((data:User) => {\r\n      // console.log('data', data);\r\n      this._coreMenuService.unregister('main');\r\n      this._coreMenuService.register('main', menu);\r\n      this._coreMenuService.setCurrentMenu('main');\r\n\r\n      this.user = data;\r\n\r\n      // redirect to fixtues & results page if user is guest\r\n      // if (data.role.id == 7) {\r\n      //   this._router.navigate(['/leagues/select-tournament']);\r\n      // }\r\n    });\r\n    this.checkMatch();\r\n  }\r\n  checkPermissions() {\r\n    if (this.user) {\r\n      return this.user.role.id == AppConfig.USER_ROLES.admin;\r\n    }\r\n  }\r\n}\r\n", "<div id=\"home\" class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n\r\n    <!-- content-header component -->\r\n    <!-- <app-content-header [contentHeader]=\"contentHeader\"></app-content-header> -->\r\n    <!-- <app-content-header [contentHeader]=\"contentHeader\"></app-content-header> -->\r\n    <app-content-background *ngIf=\"!checkPermissions()\"></app-content-background>\r\n    <!-- Basic Alerts start -->\r\n    <section id=\"home-page\">\r\n      <ng-container *ngIf=\"permissions.registration\">\r\n        <div class=\"row\">\r\n          <div class=\"col-12 col-lg-6\">\r\n            <card-season [season]=\"currentSeason\"></card-season>\r\n          </div>\r\n        </div>\r\n        <div class=\"row\" *ngIf=\"players\">\r\n          <div class=\"col-12\">\r\n            <div class=\"card\">\r\n              <div class=\"card-body p-1\">\r\n                <h4 class=\"card-title text-capitalize\">{{'player registration'|translate}}</h4>\r\n                <div class=\"row m-0\">\r\n                  <div class=\"col-12 col-md-6 col-lg-4 px-0 px-md-50\" *ngFor=\"let player of players\">\r\n                    <div class=\"card mb-1 border-card\" id=\"card-player-{{player.id}}\">\r\n                      <div class=\"card-body p-50\">\r\n                        <div class=\"d-flex align-items-center\">\r\n                          <div class=\"avatar avatar-xl mr-75 bg-transparent\">\r\n                            <img [src]=\"player.photo\" style=\"object-fit: contain\" class=\"rounded\" alt=\"Avatar\"\r\n                              (error)=\"onError($event)\" />\r\n                          </div>\r\n                          <div class=\"media-body my-auto\">\r\n                            <div class=\"d-flex align-items-end justify-content-end\">\r\n                              <span class=\"d-table badge badge-pill\"\r\n                                [class]=\"commonService.getBadgeRegistration(player.registrations[0].approval_status)\">\r\n                                {{ (player.registrations[0].approval_status|translate).toUpperCase() }}\r\n                              </span>\r\n                            </div>\r\n                            <h4 class=\"mb-50\">\r\n                              {{ name_settings?.is_on == 1 \r\n                                ? player.user.first_name + ' ' + player.user.last_name \r\n                                : player.user.last_name + ' ' + player.user.first_name }}\r\n                            </h4>\r\n                            <p class=\"mb-50\" *ngIf=\"player.registrations[0].assigned_groups.length == 0\">\r\n                              <span class=\"text-muted text-capitalize\">{{'Dob'|translate}}:</span> {{player.dob | date :\r\n                              'dd-MMM-yyyy'}}\r\n                            </p>\r\n                            <p class=\"mb-50\" *ngIf=\"player.registrations[0].assigned_groups.length > 0\">\r\n                              <span class=\"text-muted\">{{'Groups'|translate}}:</span>\r\n                              <span *ngFor=\"let item of player.registrations[0].assigned_groups; let i = index\">\r\n                                {{item.group.name}}\r\n                                {{i === player.registrations[0].assigned_groups.length - 1 ? '' : ','}}\r\n                              </span>\r\n                            </p>\r\n                            <p class=\"mb-50\">\r\n                              <span class=\"text-muted\">{{'Club'|translate}}:</span>\r\n                              {{player.registrations[0].club.code}}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"d-flex flex-column align-items-center\">\r\n                  <p class=\"text-muted\" *ngIf=\"players.length === 0\">\r\n                    {{\"You haven't registered your children yet\"|translate}}\r\n                  </p>\r\n                  <button type=\"button\" class=\"btn\" id=\"btn-go-to-register\"\r\n                    [ngClass]=\"{'btn-block btn-flat-primary': players.length !== 0, 'btn-primary': players.length === 0}\"\r\n                    (click)=\"registerNow()\" rippleEffect>\r\n                    {{(players.length !== 0?'View details':'Register Now')|translate}}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <div class=\"card-body\">\r\n              <div class=\"d-flex justify-content-between\">\r\n                <h4 class=\"card-title\">{{'Fixtures'|translate}} & {{'Results'|translate}}</h4>\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </div>\r\n              <div>\r\n                <ul ngbNav #nav=\"ngbNav\" class=\"nav nav-pills\">\r\n                  <li ngbNavItem>\r\n                    <a ngbNavLink>{{'Fixtures'|translate}}</a>\r\n                    <ng-template ngbNavContent>\r\n\r\n                    </ng-template>\r\n                  </li>\r\n                  <li ngbNavItem>\r\n                    <a ngbNavLink>{{'Results'|translate}}</a>\r\n                    <ng-template ngbNavContent>\r\n\r\n                    </ng-template>\r\n                  </li>\r\n                </ul>\r\n                <div [ngbNavOutlet]=\"nav\" class=\"mt-1\"></div>\r\n\r\n              </div>\r\n              <p class=\"text-muted text-center m-0\">\r\n                You haven't followed any groups yet.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n      </ng-container>\r\n    </section>\r\n    <div *ngIf=\"checkPermissions()\">\r\n      <app-tutorial *ngIf=\"!haveMatch\"></app-tutorial>\r\n      <app-dashboard *ngIf=\"haveMatch\"></app-dashboard>\r\n    </div>\r\n    <!-- Basic Alerts end -->\r\n  </div>\r\n</div> \r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
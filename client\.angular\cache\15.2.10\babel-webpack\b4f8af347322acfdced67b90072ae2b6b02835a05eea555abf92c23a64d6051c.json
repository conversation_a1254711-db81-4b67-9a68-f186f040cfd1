{"ast": null, "code": "import { environment } from 'environments/environment';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PermissionService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getAll() {\n    return this._http.get(`${environment.apiUrl}/permissions/index`).pipe(map(data => {\n      // Store the data in the local variable\n      // this._currentSeasons = data;\n      //Return the data\n      return data;\n    }));\n  }\n  static #_ = this.ɵfac = function PermissionService_Factory(t) {\n    return new (t || PermissionService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PermissionService,\n    factory: PermissionService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,GAAG,QAAQ,gBAAgB;;;AAKpC,OAAM,MAAOC,iBAAiB;EAC5BC,YAAoBC,KAAiB;IAAjB,KAAAA,KAAK,GAALA,KAAK;EAAe;EAExCC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACD,KAAK,CAACE,GAAG,CAAM,GAAGN,WAAW,CAACO,MAAM,oBAAoB,CAAC,CAACC,IAAI,CACxEP,GAAG,CAAEQ,IAAI,IAAI;MACX;MACA;MACA;MACA,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAAC,QAAAC,CAAA;qBAZUR,iBAAiB,EAAAS,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAjBb,iBAAiB;IAAAc,OAAA,EAAjBd,iBAAiB,CAAAe,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["environment", "map", "PermissionService", "constructor", "_http", "getAll", "get", "apiUrl", "pipe", "data", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\permission.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\nimport { map } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PermissionService {\r\n  constructor(private _http: HttpClient) {}\r\n\r\n  getAll() {\r\n    return this._http.get<any>(`${environment.apiUrl}/permissions/index`).pipe(\r\n      map((data) => {\r\n        // Store the data in the local variable\r\n        // this._currentSeasons = data;\r\n        //Return the data\r\n        return data;\r\n      })\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
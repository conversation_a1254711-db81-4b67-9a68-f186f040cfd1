{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport Swal from 'sweetalert2';\nimport { Subject } from 'rxjs';\nimport { DataTableDirective } from 'angular-datatables';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/stage.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i7 from \"@ngx-formly/core\";\nfunction ModalAssignRefereesComponent_ngb_alert_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngb-alert\", 10)(1, \"div\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"type\", \"warning\")(\"dismissible\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 4, \"Warning\"), \" \", i0.ɵɵpipeBind1(4, 6, \"You are editing multiple rows! Please be careful, data will be updated for all selected rows.\"), \" \");\n  }\n}\nexport class ModalAssignRefereesComponent {\n  constructor(_stageService, _translateService, _modalService) {\n    this._stageService = _stageService;\n    this._translateService = _translateService;\n    this._modalService = _modalService;\n    this.isMultipleAssign = false;\n    this.selectedIds = [];\n    this.listReferees = [];\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.dtElement = DataTableDirective;\n    this.onSubmit = new EventEmitter();\n  }\n  ngOnInit() {}\n  getListSelectedReferees() {\n    console.log(this.assignRefereeModel.list_referees);\n    console.log(this.listReferees.filter(e => this.assignRefereeModel.list_referees.includes(e.id)));\n    return this.listReferees.filter(e => this.assignRefereeModel.list_referees.includes(e.id));\n  }\n  closeModal() {\n    this._modalService.dismissAll();\n  }\n  onSubmitAssignReferee({\n    list_referees\n  }) {\n    console.log(list_referees);\n    Swal.fire({\n      title: this._translateService.instant('Assign Referee'),\n      text: `Are you sure you want to assign ${list_referees.length} referees to the selected matches?`,\n      icon: 'info',\n      confirmButtonText: this._translateService.instant('Assign')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._stageService.assignReferees(this.selectedIds, list_referees).subscribe(response => {\n          this.onSubmit.emit(true);\n          this.closeModal();\n          Swal.fire({\n            title: 'Success',\n            text: response.message,\n            icon: 'success',\n            confirmButtonText: 'OK'\n          });\n        }, error => {\n          Swal.fire({\n            title: 'Error',\n            text: error.message,\n            icon: 'error',\n            confirmButtonText: 'OK'\n          });\n        });\n      }\n    });\n  }\n  removeReferee(referee) {\n    console.log(referee);\n  }\n  static #_ = this.ɵfac = function ModalAssignRefereesComponent_Factory(t) {\n    return new (t || ModalAssignRefereesComponent)(i0.ɵɵdirectiveInject(i1.StageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.NgbModal));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalAssignRefereesComponent,\n    selectors: [[\"app-modal-assign-referees\"]],\n    viewQuery: function ModalAssignRefereesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      isMultipleAssign: \"isMultipleAssign\",\n      assignRefereeForm: \"assignRefereeForm\",\n      assignRefereeFields: \"assignRefereeFields\",\n      assignRefereeModel: \"assignRefereeModel\",\n      selectedIds: \"selectedIds\",\n      listReferees: \"listReferees\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 15,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalAssignReferee\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"type\", \"dismissible\", 4, \"ngIf\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"], [3, \"type\", \"dismissible\"], [1, \"alert-body\"]],\n    template: function ModalAssignRefereesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalAssignRefereesComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalAssignRefereesComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitAssignReferee(ctx.assignRefereeModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5);\n        i0.ɵɵtemplate(9, ModalAssignRefereesComponent_ngb_alert_9_Template, 5, 8, \"ngb-alert\", 6);\n        i0.ɵɵelementStart(10, \"formly-form\", 7);\n        i0.ɵɵlistener(\"submit\", function ModalAssignRefereesComponent_Template_formly_form_submit_10_listener() {\n          return ctx.onSubmitAssignReferee(ctx.assignRefereeModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Assign Referee\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.assignRefereeForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isMultipleAssign);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.assignRefereeForm)(\"fields\", ctx.assignRefereeFields)(\"model\", ctx.assignRefereeModel);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 9, \"Assign\"), \" \");\n      }\n    },\n    dependencies: [i4.NgIf, i5.ɵNgNoValidate, i5.NgControlStatusGroup, i5.FormGroupDirective, i6.RippleEffectDirective, i3.NgbAlert, i7.FormlyForm, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAkC,eAAe;AAEjF,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,OAAO,QAAQ,MAAM;AAG9B,SAASC,kBAAkB,QAAQ,oBAAoB;;;;;;;;;;;ICOnDC,EAAA,CAAAC,cAAA,oBAIC;IAEGD,EAAA,CAAAE,MAAA,GAOF;;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IAXNH,EAAA,CAAAI,UAAA,mBAAkB;IAIhBJ,EAAA,CAAAK,SAAA,GAOF;IAPEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAO,WAAA,wBAAAP,EAAA,CAAAO,WAAA,6GAOF;;;ADXN,OAAM,MAAOC,4BAA4B;EAkBvCC,YACUC,aAA2B,EAC3BC,iBAAmC,EACnCC,aAAuB;IAFvB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IAnBd,KAAAC,gBAAgB,GAAY,KAAK;IAIjC,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,YAAY,GAAG,EAAE;IAE1B,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAInB,OAAO,EAAe;IAG5D,KAAAoB,SAAS,GAAQnB,kBAAkB;IAEzB,KAAAoB,QAAQ,GAAG,IAAIvB,YAAY,EAAE;EASvC;EAEAwB,QAAQA,CAAA,GACR;EAEAC,uBAAuBA,CAAA;IACrBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,kBAAkB,CAACC,aAAa,CAAC;IAClDH,OAAO,CAACC,GAAG,CAAC,IAAI,CAACR,YAAY,CAACW,MAAM,CAAEC,CAAC,IAAK,IAAI,CAACH,kBAAkB,CAACC,aAAa,CAACG,QAAQ,CAACD,CAAC,CAACE,EAAE,CAAC,CAAC,CAAC;IAClG,OAAO,IAAI,CAACd,YAAY,CAACW,MAAM,CAAEC,CAAC,IAAK,IAAI,CAACH,kBAAkB,CAACC,aAAa,CAACG,QAAQ,CAACD,CAAC,CAACE,EAAE,CAAC,CAAC;EAC9F;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAClB,aAAa,CAACmB,UAAU,EAAE;EACjC;EAGAC,qBAAqBA,CAAC;IAAEP;EAAa,CAAE;IAErCH,OAAO,CAACC,GAAG,CAACE,aAAa,CAAC;IAE1B5B,IAAI,CAACoC,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACvB,iBAAiB,CAACwB,OAAO,CAAC,gBAAgB,CAAC;MACvDC,IAAI,EAAE,mCAAmCX,aAAa,CAACY,MAAM,oCAAoC;MACjGC,IAAI,EAAE,MAAM;MACZC,iBAAiB,EAAE,IAAI,CAAC5B,iBAAiB,CAACwB,OAAO,CAAC,QAAQ;KAC3D,CAAC,CAACK,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAAChC,aAAa,CAACiC,cAAc,CAAC,IAAI,CAAC7B,WAAW,EAAEW,aAAa,CAAC,CAC/DmB,SAAS,CAAEC,QAAQ,IAAI;UACtB,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,CAAC,IAAI,CAAC;UAExB,IAAI,CAAChB,UAAU,EAAE;UAEjBjC,IAAI,CAACoC,IAAI,CAAC;YACRC,KAAK,EAAE,SAAS;YAChBE,IAAI,EAAES,QAAQ,CAACE,OAAO;YACtBT,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE;WACpB,CAAC;QACJ,CAAC,EAAGS,KAAK,IAAI;UACXnD,IAAI,CAACoC,IAAI,CAAC;YACRC,KAAK,EAAE,OAAO;YACdE,IAAI,EAAEY,KAAK,CAACD,OAAO;YACnBT,IAAI,EAAE,OAAO;YACbC,iBAAiB,EAAE;WACpB,CAAC;QACJ,CAAC,CAAC;;IAIR,CAAC,CAAC;EACJ;EAEAU,aAAaA,CAACC,OAAO;IACnB5B,OAAO,CAACC,GAAG,CAAC2B,OAAO,CAAC;EACtB;EAAC,QAAAC,CAAA;qBA/EU3C,4BAA4B,EAAAR,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAxD,EAAA,CAAAoD,iBAAA,CAAAK,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA;UAA5BnD,4BAA4B;IAAAoD,SAAA;IAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAY5BhE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;QC9B/BC,EAAA,CAAAC,cAAA,aAA0B;QACwBD,EAAA,CAAAE,MAAA,GAAkC;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvFH,EAAA,CAAAC,cAAA,gBAKC;QAFCD,EAAA,CAAAiE,UAAA,mBAAAC,8DAAA;UAAA,OAASF,GAAA,CAAAlC,UAAA,EAAY;QAAA,EAAC;QAGtB9B,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAE,MAAA,aAAO;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAG3CH,EAAA,CAAAC,cAAA,cAGC;QADCD,EAAA,CAAAiE,UAAA,sBAAAE,+DAAA;UAAA,OAAYH,GAAA,CAAAhC,qBAAA,CAAAgC,GAAA,CAAAxC,kBAAA,CAAyC;QAAA,EAAC;QAEtDxB,EAAA,CAAAC,cAAA,aAAkD;QAChDD,EAAA,CAAAoE,UAAA,IAAAC,iDAAA,uBAcY;QACZrE,EAAA,CAAAC,cAAA,sBAKC;QADCD,EAAA,CAAAiE,UAAA,oBAAAK,qEAAA;UAAA,OAAUN,GAAA,CAAAhC,qBAAA,CAAAgC,GAAA,CAAAxC,kBAAA,CAAyC;QAAA,EAAC;QACrDxB,EAAA,CAAAG,YAAA,EAAc;QAEjBH,EAAA,CAAAC,cAAA,cAA0B;QAEtBD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAxCqCH,EAAA,CAAAK,SAAA,GAAkC;QAAlCL,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAO,WAAA,yBAAkC;QAWlFP,EAAA,CAAAK,SAAA,GAA+B;QAA/BL,EAAA,CAAAI,UAAA,cAAA4D,GAAA,CAAAQ,iBAAA,CAA+B;QAK1BxE,EAAA,CAAAK,SAAA,GAAsB;QAAtBL,EAAA,CAAAI,UAAA,SAAA4D,GAAA,CAAAnD,gBAAA,CAAsB;QAevBb,EAAA,CAAAK,SAAA,GAA0B;QAA1BL,EAAA,CAAAI,UAAA,SAAA4D,GAAA,CAAAQ,iBAAA,CAA0B,WAAAR,GAAA,CAAAS,mBAAA,WAAAT,GAAA,CAAAxC,kBAAA;QAQ1BxB,EAAA,CAAAK,SAAA,GACF;QADEL,EAAA,CAAA0E,kBAAA,MAAA1E,EAAA,CAAAO,WAAA,uBACF", "names": ["EventEmitter", "<PERSON><PERSON>", "Subject", "DataTableDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "ModalAssignRefereesComponent", "constructor", "_stageService", "_translateService", "_modalService", "isMultipleAssign", "selectedIds", "listReferees", "dtOptions", "dtTrigger", "dtElement", "onSubmit", "ngOnInit", "getListSelectedReferees", "console", "log", "assignRefereeModel", "list_referees", "filter", "e", "includes", "id", "closeModal", "dismissAll", "onSubmitAssignReferee", "fire", "title", "instant", "text", "length", "icon", "confirmButtonText", "then", "result", "isConfirmed", "assignReferees", "subscribe", "response", "emit", "message", "error", "remove<PERSON><PERSON><PERSON><PERSON>", "referee", "_", "ɵɵdirectiveInject", "i1", "StageService", "i2", "TranslateService", "i3", "NgbModal", "_2", "selectors", "viewQuery", "ModalAssignRefereesComponent_Query", "rf", "ctx", "ɵɵlistener", "ModalAssignRefereesComponent_Template_button_click_4_listener", "ModalAssignRefereesComponent_Template_form_ngSubmit_7_listener", "ɵɵtemplate", "ModalAssignRefereesComponent_ngb_alert_9_Template", "ModalAssignRefereesComponent_Template_formly_form_submit_10_listener", "ɵɵtextInterpolate", "assignRefereeForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\modal-assign-referees\\modal-assign-referees.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\modal-assign-referees\\modal-assign-referees.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport Swal from 'sweetalert2';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { Subject } from 'rxjs';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { environment } from '../../../../../../environments/environment';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { CommonsService } from '../../../../../services/commons.service';\r\nimport { HttpClient } from '@angular/common/http';\r\n\r\n@Component({\r\n  selector: 'app-modal-assign-referees',\r\n  templateUrl: './modal-assign-referees.component.html',\r\n  styleUrls: ['./modal-assign-referees.component.scss']\r\n})\r\nexport class ModalAssignRefereesComponent {\r\n\r\n  @Input() isMultipleAssign: boolean = false;\r\n  @Input() assignRefereeForm;\r\n  @Input() assignRefereeFields;\r\n  @Input() assignRefereeModel;\r\n  @Input() selectedIds: any[] = [];\r\n  @Input() listReferees = [];\r\n\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n\r\n  @Output() onSubmit = new EventEmitter();\r\n\r\n\r\n  constructor(\r\n    private _stageService: StageService,\r\n    private _translateService: TranslateService,\r\n    private _modalService: NgbModal\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n  getListSelectedReferees() {\r\n    console.log(this.assignRefereeModel.list_referees);\r\n    console.log(this.listReferees.filter((e) => this.assignRefereeModel.list_referees.includes(e.id)));\r\n    return this.listReferees.filter((e) => this.assignRefereeModel.list_referees.includes(e.id));\r\n  }\r\n\r\n  closeModal() {\r\n    this._modalService.dismissAll();\r\n  }\r\n\r\n\r\n  onSubmitAssignReferee({ list_referees }) {\r\n\r\n    console.log(list_referees);\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Assign Referee'),\r\n      text: `Are you sure you want to assign ${list_referees.length} referees to the selected matches?`,\r\n      icon: 'info',\r\n      confirmButtonText: this._translateService.instant('Assign')\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this._stageService.assignReferees(this.selectedIds, list_referees)\r\n          .subscribe((response) => {\r\n            this.onSubmit.emit(true);\r\n\r\n            this.closeModal();\r\n\r\n            Swal.fire({\r\n              title: 'Success',\r\n              text: response.message,\r\n              icon: 'success',\r\n              confirmButtonText: 'OK'\r\n            });\r\n          }, (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: 'OK'\r\n            });\r\n          });\r\n\r\n      }\r\n\r\n    });\r\n  }\r\n\r\n  removeReferee(referee) {\r\n    console.log(referee);\r\n  }\r\n}", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"modalAssignReferee\">{{ \"Assign Referee\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"assignRefereeForm\"\r\n  (ngSubmit)=\"onSubmitAssignReferee(assignRefereeModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <ngb-alert\r\n      *ngIf=\"isMultipleAssign\"\r\n      [type]=\"'warning'\"\r\n      [dismissible]=\"false\"\r\n    >\r\n      <div class=\"alert-body\">\r\n        {{\r\n          'Warning'\r\n            | translate\r\n        }}\r\n        {{\r\n          'You are editing multiple rows! Please be careful, data will be updated for all selected rows.' |translate\r\n        }}\r\n      </div>\r\n    </ngb-alert>\r\n    <formly-form\r\n      [form]=\"assignRefereeForm\"\r\n      [fields]=\"assignRefereeFields\"\r\n      [model]=\"assignRefereeModel\"\r\n      (submit)=\"onSubmitAssignReferee(assignRefereeModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n      {{ 'Assign' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport function isIterable(input) {\n  return input && typeof input[Symbol_iterator] === 'function';\n}", "map": {"version": 3, "names": ["iterator", "Symbol_iterator", "isIterable", "input"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isIterable.js"], "sourcesContent": ["import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport function isIterable(input) {\n    return input && typeof input[Symbol_iterator] === 'function';\n}\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,eAAe,QAAQ,oBAAoB;AAChE,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACF,eAAe,CAAC,KAAK,UAAU;AAChE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
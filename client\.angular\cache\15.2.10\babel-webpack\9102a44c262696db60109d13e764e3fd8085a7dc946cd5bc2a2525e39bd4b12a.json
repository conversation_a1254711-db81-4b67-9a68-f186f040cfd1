{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin } from '@capacitor/core';\nexport class NavigationBarWeb extends WebPlugin {\n  show() {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        console.log('Navigation Bar Showed!');\n        resolve();\n      });\n    })();\n  }\n  hide() {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        console.log('Navigation Bar Hided!');\n        resolve();\n      });\n    })();\n  }\n  setColor(options) {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        console.log(`Navigation Bar color changed to ${options.color ? options.color : '#FFFFFF'} : Dark Buttons: ${options.darkButtons ? 'YES' : 'NO'}`);\n        resolve();\n      });\n    })();\n  }\n  setTransparency(options) {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        console.log(`Navigation Bar is transparent: ${options.isTransparent ? 'YES' : 'NO'}`);\n        resolve();\n      });\n    })();\n  }\n  getColor() {\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        resolve({\n          color: '#FFFFFF'\n        });\n      });\n    })();\n  }\n}", "map": {"version": 3, "names": ["WebPlugin", "NavigationBarWeb", "show", "_asyncToGenerator", "Promise", "resolve", "console", "log", "hide", "setColor", "options", "color", "darkButtons", "setTransparency", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getColor"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@hugotomazi/capacitor-navigation-bar/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\r\nexport class NavigationBarWeb extends WebPlugin {\r\n    async show() {\r\n        return new Promise((resolve) => {\r\n            console.log('Navigation Bar Showed!');\r\n            resolve();\r\n        });\r\n    }\r\n    async hide() {\r\n        return new Promise((resolve) => {\r\n            console.log('Navigation Bar Hided!');\r\n            resolve();\r\n        });\r\n    }\r\n    async setColor(options) {\r\n        return new Promise((resolve) => {\r\n            console.log(`Navigation Bar color changed to ${options.color ? options.color : '#FFFFFF'} : Dark Buttons: ${options.darkButtons ? 'YES' : 'NO'}`);\r\n            resolve();\r\n        });\r\n    }\r\n    async setTransparency(options) {\r\n        return new Promise((resolve) => {\r\n            console.log(`Navigation Bar is transparent: ${options.isTransparent ? 'YES' : 'NO'}`);\r\n            resolve();\r\n        });\r\n    }\r\n    async getColor() {\r\n        return new Promise((resolve) => {\r\n            resolve({ color: '#FFFFFF' });\r\n        });\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,gBAAgB,SAASD,SAAS,CAAC;EACtCE,IAAIA,CAAA,EAAG;IAAA,OAAAC,iBAAA;MACT,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCF,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IAAC;EACP;EACMG,IAAIA,CAAA,EAAG;IAAA,OAAAL,iBAAA;MACT,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpCF,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IAAC;EACP;EACMI,QAAQA,CAACC,OAAO,EAAE;IAAA,OAAAP,iBAAA;MACpB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BC,OAAO,CAACC,GAAG,CAAE,mCAAkCG,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACC,KAAK,GAAG,SAAU,oBAAmBD,OAAO,CAACE,WAAW,GAAG,KAAK,GAAG,IAAK,EAAC,CAAC;QACjJP,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IAAC;EACP;EACMQ,eAAeA,CAACH,OAAO,EAAE;IAAA,OAAAP,iBAAA;MAC3B,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BC,OAAO,CAACC,GAAG,CAAE,kCAAiCG,OAAO,CAACI,aAAa,GAAG,KAAK,GAAG,IAAK,EAAC,CAAC;QACrFT,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IAAC;EACP;EACMU,QAAQA,CAAA,EAAG;IAAA,OAAAZ,iBAAA;MACb,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BA,OAAO,CAAC;UAAEM,KAAK,EAAE;QAAU,CAAC,CAAC;MACjC,CAAC,CAAC;IAAC;EACP;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
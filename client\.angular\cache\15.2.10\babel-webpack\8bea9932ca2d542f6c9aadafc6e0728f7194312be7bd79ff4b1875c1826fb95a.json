{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function delay(delay, scheduler = async) {\n  const absoluteDelay = isDate(delay);\n  const delayFor = absoluteDelay ? +delay - scheduler.now() : Math.abs(delay);\n  return source => source.lift(new DelayOperator(delayFor, scheduler));\n}\nclass DelayOperator {\n  constructor(delay, scheduler) {\n    this.delay = delay;\n    this.scheduler = scheduler;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new DelaySubscriber(subscriber, this.delay, this.scheduler));\n  }\n}\nclass DelaySubscriber extends Subscriber {\n  constructor(destination, delay, scheduler) {\n    super(destination);\n    this.delay = delay;\n    this.scheduler = scheduler;\n    this.queue = [];\n    this.active = false;\n    this.errored = false;\n  }\n  static dispatch(state) {\n    const source = state.source;\n    const queue = source.queue;\n    const scheduler = state.scheduler;\n    const destination = state.destination;\n    while (queue.length > 0 && queue[0].time - scheduler.now() <= 0) {\n      queue.shift().notification.observe(destination);\n    }\n    if (queue.length > 0) {\n      const delay = Math.max(0, queue[0].time - scheduler.now());\n      this.schedule(state, delay);\n    } else {\n      this.unsubscribe();\n      source.active = false;\n    }\n  }\n  _schedule(scheduler) {\n    this.active = true;\n    const destination = this.destination;\n    destination.add(scheduler.schedule(DelaySubscriber.dispatch, this.delay, {\n      source: this,\n      destination: this.destination,\n      scheduler: scheduler\n    }));\n  }\n  scheduleNotification(notification) {\n    if (this.errored === true) {\n      return;\n    }\n    const scheduler = this.scheduler;\n    const message = new DelayMessage(scheduler.now() + this.delay, notification);\n    this.queue.push(message);\n    if (this.active === false) {\n      this._schedule(scheduler);\n    }\n  }\n  _next(value) {\n    this.scheduleNotification(Notification.createNext(value));\n  }\n  _error(err) {\n    this.errored = true;\n    this.queue = [];\n    this.destination.error(err);\n    this.unsubscribe();\n  }\n  _complete() {\n    this.scheduleNotification(Notification.createComplete());\n    this.unsubscribe();\n  }\n}\nclass DelayMessage {\n  constructor(time, notification) {\n    this.time = time;\n    this.notification = notification;\n  }\n}", "map": {"version": 3, "names": ["async", "isDate", "Subscriber", "Notification", "delay", "scheduler", "absoluteDelay", "delayFor", "now", "Math", "abs", "source", "lift", "DelayOperator", "constructor", "call", "subscriber", "subscribe", "DelaySubscriber", "destination", "queue", "active", "errored", "dispatch", "state", "length", "time", "shift", "notification", "observe", "max", "schedule", "unsubscribe", "_schedule", "add", "scheduleNotification", "message", "DelayMessage", "push", "_next", "value", "createNext", "_error", "err", "error", "_complete", "createComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/delay.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function delay(delay, scheduler = async) {\n    const absoluteDelay = isDate(delay);\n    const delayFor = absoluteDelay ? (+delay - scheduler.now()) : Math.abs(delay);\n    return (source) => source.lift(new DelayOperator(delayFor, scheduler));\n}\nclass DelayOperator {\n    constructor(delay, scheduler) {\n        this.delay = delay;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DelaySubscriber(subscriber, this.delay, this.scheduler));\n    }\n}\nclass DelaySubscriber extends Subscriber {\n    constructor(destination, delay, scheduler) {\n        super(destination);\n        this.delay = delay;\n        this.scheduler = scheduler;\n        this.queue = [];\n        this.active = false;\n        this.errored = false;\n    }\n    static dispatch(state) {\n        const source = state.source;\n        const queue = source.queue;\n        const scheduler = state.scheduler;\n        const destination = state.destination;\n        while (queue.length > 0 && (queue[0].time - scheduler.now()) <= 0) {\n            queue.shift().notification.observe(destination);\n        }\n        if (queue.length > 0) {\n            const delay = Math.max(0, queue[0].time - scheduler.now());\n            this.schedule(state, delay);\n        }\n        else {\n            this.unsubscribe();\n            source.active = false;\n        }\n    }\n    _schedule(scheduler) {\n        this.active = true;\n        const destination = this.destination;\n        destination.add(scheduler.schedule(DelaySubscriber.dispatch, this.delay, {\n            source: this, destination: this.destination, scheduler: scheduler\n        }));\n    }\n    scheduleNotification(notification) {\n        if (this.errored === true) {\n            return;\n        }\n        const scheduler = this.scheduler;\n        const message = new DelayMessage(scheduler.now() + this.delay, notification);\n        this.queue.push(message);\n        if (this.active === false) {\n            this._schedule(scheduler);\n        }\n    }\n    _next(value) {\n        this.scheduleNotification(Notification.createNext(value));\n    }\n    _error(err) {\n        this.errored = true;\n        this.queue = [];\n        this.destination.error(err);\n        this.unsubscribe();\n    }\n    _complete() {\n        this.scheduleNotification(Notification.createComplete());\n        this.unsubscribe();\n    }\n}\nclass DelayMessage {\n    constructor(time, notification) {\n        this.time = time;\n        this.notification = notification;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,KAAKA,CAACA,KAAK,EAAEC,SAAS,GAAGL,KAAK,EAAE;EAC5C,MAAMM,aAAa,GAAGL,MAAM,CAACG,KAAK,CAAC;EACnC,MAAMG,QAAQ,GAAGD,aAAa,GAAI,CAACF,KAAK,GAAGC,SAAS,CAACG,GAAG,CAAC,CAAC,GAAIC,IAAI,CAACC,GAAG,CAACN,KAAK,CAAC;EAC7E,OAAQO,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,aAAa,CAACN,QAAQ,EAAEF,SAAS,CAAC,CAAC;AAC1E;AACA,MAAMQ,aAAa,CAAC;EAChBC,WAAWA,CAACV,KAAK,EAAEC,SAAS,EAAE;IAC1B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAU,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,eAAe,CAACF,UAAU,EAAE,IAAI,CAACZ,KAAK,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;EACxF;AACJ;AACA,MAAMa,eAAe,SAAShB,UAAU,CAAC;EACrCY,WAAWA,CAACK,WAAW,EAAEf,KAAK,EAAEC,SAAS,EAAE;IACvC,KAAK,CAACc,WAAW,CAAC;IAClB,IAAI,CAACf,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACe,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA,OAAOC,QAAQA,CAACC,KAAK,EAAE;IACnB,MAAMb,MAAM,GAAGa,KAAK,CAACb,MAAM;IAC3B,MAAMS,KAAK,GAAGT,MAAM,CAACS,KAAK;IAC1B,MAAMf,SAAS,GAAGmB,KAAK,CAACnB,SAAS;IACjC,MAAMc,WAAW,GAAGK,KAAK,CAACL,WAAW;IACrC,OAAOC,KAAK,CAACK,MAAM,GAAG,CAAC,IAAKL,KAAK,CAAC,CAAC,CAAC,CAACM,IAAI,GAAGrB,SAAS,CAACG,GAAG,CAAC,CAAC,IAAK,CAAC,EAAE;MAC/DY,KAAK,CAACO,KAAK,CAAC,CAAC,CAACC,YAAY,CAACC,OAAO,CAACV,WAAW,CAAC;IACnD;IACA,IAAIC,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;MAClB,MAAMrB,KAAK,GAAGK,IAAI,CAACqB,GAAG,CAAC,CAAC,EAAEV,KAAK,CAAC,CAAC,CAAC,CAACM,IAAI,GAAGrB,SAAS,CAACG,GAAG,CAAC,CAAC,CAAC;MAC1D,IAAI,CAACuB,QAAQ,CAACP,KAAK,EAAEpB,KAAK,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAAC4B,WAAW,CAAC,CAAC;MAClBrB,MAAM,CAACU,MAAM,GAAG,KAAK;IACzB;EACJ;EACAY,SAASA,CAAC5B,SAAS,EAAE;IACjB,IAAI,CAACgB,MAAM,GAAG,IAAI;IAClB,MAAMF,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACe,GAAG,CAAC7B,SAAS,CAAC0B,QAAQ,CAACb,eAAe,CAACK,QAAQ,EAAE,IAAI,CAACnB,KAAK,EAAE;MACrEO,MAAM,EAAE,IAAI;MAAEQ,WAAW,EAAE,IAAI,CAACA,WAAW;MAAEd,SAAS,EAAEA;IAC5D,CAAC,CAAC,CAAC;EACP;EACA8B,oBAAoBA,CAACP,YAAY,EAAE;IAC/B,IAAI,IAAI,CAACN,OAAO,KAAK,IAAI,EAAE;MACvB;IACJ;IACA,MAAMjB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAM+B,OAAO,GAAG,IAAIC,YAAY,CAAChC,SAAS,CAACG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACJ,KAAK,EAAEwB,YAAY,CAAC;IAC5E,IAAI,CAACR,KAAK,CAACkB,IAAI,CAACF,OAAO,CAAC;IACxB,IAAI,IAAI,CAACf,MAAM,KAAK,KAAK,EAAE;MACvB,IAAI,CAACY,SAAS,CAAC5B,SAAS,CAAC;IAC7B;EACJ;EACAkC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACL,oBAAoB,CAAChC,YAAY,CAACsC,UAAU,CAACD,KAAK,CAAC,CAAC;EAC7D;EACAE,MAAMA,CAACC,GAAG,EAAE;IACR,IAAI,CAACrB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,KAAK,GAAG,EAAE;IACf,IAAI,CAACD,WAAW,CAACyB,KAAK,CAACD,GAAG,CAAC;IAC3B,IAAI,CAACX,WAAW,CAAC,CAAC;EACtB;EACAa,SAASA,CAAA,EAAG;IACR,IAAI,CAACV,oBAAoB,CAAChC,YAAY,CAAC2C,cAAc,CAAC,CAAC,CAAC;IACxD,IAAI,CAACd,WAAW,CAAC,CAAC;EACtB;AACJ;AACA,MAAMK,YAAY,CAAC;EACfvB,WAAWA,CAACY,IAAI,EAAEE,YAAY,EAAE;IAC5B,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,YAAY,GAAGA,YAAY;EACpC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/components/core-menu/core-menu.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/flex-layout/extended\";\nimport * as i6 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i7 from \"@core/components/core-menu/vertical/item/item.component\";\nconst _c0 = [\"core-menu-vertical-collapsible\", \"\"];\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.toggleOpen($event));\n    });\n    i0.ɵɵtemplate(1, CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r2 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.classes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r2);\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.item.icon);\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data-feather\", ctx_r11.item.icon);\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_i_1_Template, 1, 1, \"i\", 9);\n    i0.ɵɵtemplate(2, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_span_2_Template, 1, 1, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.item.icon.startsWith(\"fa-\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.item.icon.startsWith(\"fa-\"));\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"translate\", ctx_r9.item.badge.translate)(\"ngClass\", ctx_r9.item.badge.classes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.item.badge.title, \" \");\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n    i0.ɵɵelementStart(1, \"span\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_span_3_Template, 2, 3, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.item.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"translate\", ctx_r3.item.translate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.item.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.item.badge);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    disabled: a0\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    exact: a0\n  };\n};\nconst _c3 = function () {\n  return [];\n};\nconst _c4 = function (a0) {\n  return [a0];\n};\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 16);\n    i0.ɵɵelement(1, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"item\", item_r12)(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, item_r12.disabled === true))(\"routerLinkActive\", !item_r12.openInNewTab ? \"active\" : \"\")(\"routerLinkActiveOptions\", i0.ɵɵpureFunction1(7, _c2, item_r12.exactMatch || false));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", item_r12.openInNewTab ? i0.ɵɵpureFunction0(9, _c3) : i0.ɵɵpureFunction1(10, _c4, item_r12.url));\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 18);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"item\", item_r12);\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_li_1_Template, 2, 12, \"li\", 14);\n    i0.ɵɵtemplate(2, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_li_2_Template, 1, 1, \"li\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.type == \"item\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.type == \"collapsible\");\n  }\n}\nfunction CoreMenuVerticalCollapsibleComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_Template, 2, 2, \"a\", 1);\n    i0.ɵɵtemplate(2, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_Template, 4, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(4, \"ul\", 3);\n    i0.ɵɵtemplate(5, CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_Template, 3, 2, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.item.url);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.item.children);\n  }\n}\nexport class CoreMenuVerticalCollapsibleComponent {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {Router} _router\r\n   * @param {CoreMenuService} _coreMenuService\r\n   * @param {ChangeDetectorRef} _changeDetectorRef\r\n   */\n  constructor(_router, _coreMenuService, _changeDetectorRef) {\n    this._router = _router;\n    this._coreMenuService = _coreMenuService;\n    this._changeDetectorRef = _changeDetectorRef;\n    this.isOpen = false;\n    // Set the private defaults\n    this._unsubscribeAll = new Subject();\n  }\n  // Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    // Listen for router events and expand\n    this._router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this._unsubscribeAll)).subscribe(event => {\n      // Confirm if the urlAfterRedirects can be found in one of the children of this item\n      if (this.confirmUrlInChildren(this.item, event.urlAfterRedirects)) {\n        this.expand();\n      } else {\n        this.collapse();\n      }\n    });\n    // Subscribe to the current menu changes\n    this._coreMenuService.onMenuChanged.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {\n      this.currentUser = this._coreMenuService.currentUser;\n    });\n    // Listen for collapsing of any menu item\n    this._coreMenuService.onItemCollapsed.pipe(takeUntil(this._unsubscribeAll)).subscribe(clickedItem => {\n      if (clickedItem && clickedItem.children) {\n        // Check if the clicked item is one of the children of this item\n        if (this.confirmItemInChildren(this.item, clickedItem)) {\n          return;\n        }\n        // Check if the url can be found in one of the children of this item\n        if (this.confirmUrlInChildren(this.item, this._router.url)) {\n          return;\n        }\n        // If the clicked item is not this item, collapse...\n        if (this.item !== clickedItem) {\n          this.collapse();\n        }\n      }\n    });\n    // Check if the url can be found in one of the children of this item\n    // Required for onInit case (i.e switching theme customizer menu layout)\n    if (this.confirmUrlInChildren(this.item, this._router.url)) {\n      this.expand();\n    } else {\n      this.collapse();\n    }\n  }\n  /**\r\n   * On destroy\r\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  // Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Toggle collapse\r\n   *\r\n   * @param e\r\n   */\n  toggleOpen(e) {\n    e.preventDefault();\n    this.isOpen = !this.isOpen;\n    // Menu collapse toggled...\n    this._coreMenuService.onItemCollapsed.next(this.item);\n    this._coreMenuService.onItemCollapseToggled.next();\n  }\n  /**\r\n   * Expand the collapsible menu\r\n   */\n  expand() {\n    if (this.isOpen) {\n      return;\n    }\n    this.isOpen = true;\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n    this._coreMenuService.onItemCollapseToggled.next();\n  }\n  /**\r\n   * Collapse the collapsible menu\r\n   */\n  collapse() {\n    if (!this.isOpen) {\n      return;\n    }\n    this.isOpen = false;\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n    this._coreMenuService.onItemCollapseToggled.next();\n  }\n  /**\r\n   * Confirms if the provided url can be found in one of the given parent's children\r\n   *\r\n   * @param parent\r\n   * @param url\r\n   * @returns {boolean}\r\n   */\n  confirmUrlInChildren(parent, url) {\n    const children = parent.children;\n    // Return false if parent don't have any children\n    if (!children) {\n      return false;\n    }\n    // Loop all the children\n    for (const child of children) {\n      // If children has child (Sub to sub item url)\n      if (child.children) {\n        // Call function again with child\n        if (this.confirmUrlInChildren(child, url)) {\n          return true;\n        }\n      }\n      // If child.url is same as provided url\n      if (child.url === url || url.includes(child.url)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\r\n   * Check if the provided parent has the provided item in one of its children\r\n   *\r\n   * @param parent\r\n   * @param item\r\n   * @returns {boolean}\r\n   */\n  confirmItemInChildren(parent, item) {\n    const children = parent.children;\n    // Return false if parent don't have any children\n    if (!children) {\n      return false;\n    }\n    // Return true parent has the provided item in one of its children\n    if (children.indexOf(item) > -1) {\n      return true;\n    }\n    for (const child of children) {\n      if (child.children) {\n        // Call function again with child (for sub to sub item)\n        if (this.confirmItemInChildren(child, item)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static #_ = this.ɵfac = function CoreMenuVerticalCollapsibleComponent_Factory(t) {\n    return new (t || CoreMenuVerticalCollapsibleComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CoreMenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CoreMenuVerticalCollapsibleComponent,\n    selectors: [[\"\", \"core-menu-vertical-collapsible\", \"\"]],\n    hostVars: 2,\n    hostBindings: function CoreMenuVerticalCollapsibleComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"open\", ctx.isOpen);\n      }\n    },\n    inputs: {\n      item: \"item\"\n    },\n    attrs: _c0,\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [\"class\", \"d-flex align-items-center\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"itemContent\", \"\"], [1, \"menu-content\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", 3, \"ngClass\", \"click\"], [4, \"ngTemplateOutlet\"], [1, \"menu-title\", \"text-truncate\", 3, \"translate\"], [\"class\", \"badge ml-auto mr-1\", 3, \"translate\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"data-feather\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"data-feather\"], [1, \"badge\", \"ml-auto\", \"mr-1\", 3, \"translate\", \"ngClass\"], [\"core-menu-vertical-item\", \"\", 3, \"item\", \"ngClass\", \"routerLinkActive\", \"routerLinkActiveOptions\", 4, \"ngIf\"], [\"core-menu-vertical-collapsible\", \"\", \"class\", \"nav-item has-sub\", 3, \"item\", 4, \"ngIf\"], [\"core-menu-vertical-item\", \"\", 3, \"item\", \"ngClass\", \"routerLinkActive\", \"routerLinkActiveOptions\"], [1, \"d-none\", 3, \"routerLink\"], [\"core-menu-vertical-collapsible\", \"\", 1, \"nav-item\", \"has-sub\", 3, \"item\"]],\n    template: function CoreMenuVerticalCollapsibleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CoreMenuVerticalCollapsibleComponent_ng_container_0_Template, 6, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.item.hidden);\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgTemplateOutlet, i1.RouterLink, i1.RouterLinkActive, i4.TranslateDirective, i5.DefaultClassDirective, i6.FeatherIconDirective, i7.CoreMenuVerticalItemComponent, CoreMenuVerticalCollapsibleComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,aAAa,QAAgB,iBAAiB;AAEvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;ICD9CC,EAAA,CAAAC,kBAAA,GAA6D;;;;;;IAD/DD,EAAA,CAAAE,cAAA,WAA6G;IAA7BF,EAAA,CAAAG,UAAA,mBAAAC,oFAAAC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAN,MAAA,CAAkB;IAAA,EAAC;IAC1GL,EAAA,CAAAY,UAAA,IAAAC,+EAAA,0BAA6D;IAC/Db,EAAA,CAAAc,YAAA,EAAI;;;;;;IAFiCd,EAAA,CAAAe,UAAA,YAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAwB;IAC5ClB,EAAA,CAAAmB,SAAA,GAA6B;IAA7BnB,EAAA,CAAAe,UAAA,qBAAAK,GAAA,CAA6B;;;;;IAM1CpB,EAAA,CAAAqB,SAAA,YAAiE;;;;IAA9DrB,EAAA,CAAAe,UAAA,YAAAO,OAAA,CAAAL,IAAA,CAAAM,IAAA,CAAqB;;;;;IACxBvB,EAAA,CAAAqB,SAAA,eAA6E;;;;IAAvErB,EAAA,CAAAe,UAAA,iBAAAS,OAAA,CAAAP,IAAA,CAAAM,IAAA,CAA0B;;;;;IAFlCvB,EAAA,CAAAyB,uBAAA,GAAgC;IAC9BzB,EAAA,CAAAY,UAAA,IAAAc,6FAAA,eAAiE;IACjE1B,EAAA,CAAAY,UAAA,IAAAe,gGAAA,mBAA6E;IAC/E3B,EAAA,CAAA4B,qBAAA,EAAe;;;;IAFa5B,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAe,UAAA,SAAAc,MAAA,CAAAZ,IAAA,CAAAM,IAAA,CAAAO,UAAA,QAAiC;IACzB9B,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAe,UAAA,UAAAc,MAAA,CAAAZ,IAAA,CAAAM,IAAA,CAAAO,UAAA,QAAkC;;;;;IAGtE9B,EAAA,CAAAE,cAAA,eAKC;IACCF,EAAA,CAAA+B,MAAA,GACF;IAAA/B,EAAA,CAAAc,YAAA,EAAO;;;;IAJLd,EAAA,CAAAe,UAAA,cAAAiB,MAAA,CAAAf,IAAA,CAAAgB,KAAA,CAAAC,SAAA,CAAkC,YAAAF,MAAA,CAAAf,IAAA,CAAAgB,KAAA,CAAAf,OAAA;IAGlClB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmC,kBAAA,MAAAH,MAAA,CAAAf,IAAA,CAAAgB,KAAA,CAAAG,KAAA,MACF;;;;;IAZApC,EAAA,CAAAY,UAAA,IAAAyB,yFAAA,0BAGe;IACfrC,EAAA,CAAAE,cAAA,cAAoE;IAAAF,EAAA,CAAA+B,MAAA,GAAgB;IAAA/B,EAAA,CAAAc,YAAA,EAAO;IAC3Fd,EAAA,CAAAY,UAAA,IAAA0B,iFAAA,kBAOO;;;;IAZQtC,EAAA,CAAAe,UAAA,SAAAwB,MAAA,CAAAtB,IAAA,CAAAM,IAAA,CAAe;IAISvB,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAe,UAAA,cAAAwB,MAAA,CAAAtB,IAAA,CAAAiB,SAAA,CAA4B;IAAClC,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAwC,iBAAA,CAAAD,MAAA,CAAAtB,IAAA,CAAAmB,KAAA,CAAgB;IAGjFpC,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAe,UAAA,SAAAwB,MAAA,CAAAtB,IAAA,CAAAgB,KAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;IAYjBjC,EAAA,CAAAE,cAAA,aAOC;IACCF,EAAA,CAAAqB,SAAA,eAA+E;IACjFrB,EAAA,CAAAc,YAAA,EAAK;;;;IAPHd,EAAA,CAAAe,UAAA,SAAA0B,QAAA,CAAa,YAAAzC,EAAA,CAAA0C,eAAA,IAAAC,GAAA,EAAAF,QAAA,CAAAG,QAAA,iCAAAH,QAAA,CAAAI,YAAA,6CAAA7C,EAAA,CAAA0C,eAAA,IAAAI,GAAA,EAAAL,QAAA,CAAAM,UAAA;IAMP/C,EAAA,CAAAmB,SAAA,GAAkD;IAAlDnB,EAAA,CAAAe,UAAA,eAAA0B,QAAA,CAAAI,YAAA,GAAA7C,EAAA,CAAAgD,eAAA,IAAAC,GAAA,IAAAjD,EAAA,CAAA0C,eAAA,KAAAQ,GAAA,EAAAT,QAAA,CAAAU,GAAA,EAAkD;;;;;IAG1DnD,EAAA,CAAAqB,SAAA,aAAkH;;;;IAA5CrB,EAAA,CAAAe,UAAA,SAAA0B,QAAA,CAAa;;;;;IAbrFzC,EAAA,CAAAyB,uBAAA,GAAiD;IAE/CzB,EAAA,CAAAY,UAAA,IAAAwC,gFAAA,kBASK;IAELpD,EAAA,CAAAY,UAAA,IAAAyC,gFAAA,iBAAkH;IACpHrD,EAAA,CAAA4B,qBAAA,EAAe;;;;IATV5B,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAe,UAAA,SAAA0B,QAAA,CAAAa,IAAA,WAAyB;IAQQtD,EAAA,CAAAmB,SAAA,GAAgC;IAAhCnB,EAAA,CAAAe,UAAA,SAAA0B,QAAA,CAAAa,IAAA,kBAAgC;;;;;IAtC1EtD,EAAA,CAAAyB,uBAAA,GAAmC;IAEjCzB,EAAA,CAAAY,UAAA,IAAA2C,gEAAA,eAEI;IAGJvD,EAAA,CAAAY,UAAA,IAAA4C,0EAAA,gCAAAxD,EAAA,CAAAyD,sBAAA,CAcc;IAGdzD,EAAA,CAAAE,cAAA,YAAyB;IACvBF,EAAA,CAAAY,UAAA,IAAA8C,2EAAA,0BAce;IACjB1D,EAAA,CAAAc,YAAA,EAAK;IACPd,EAAA,CAAA4B,qBAAA,EAAe;;;;IAvCkD5B,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAe,UAAA,UAAA4C,MAAA,CAAA1C,IAAA,CAAAkC,GAAA,CAAe;IAuB7CnD,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAe,UAAA,YAAA4C,MAAA,CAAA1C,IAAA,CAAA2C,QAAA,CAAgB;;;ADVnD,OAAM,MAAOC,oCAAoC;EAY/C;;;;;;;EAOAC,YACUC,OAAe,EACfC,gBAAiC,EACjCC,kBAAqC;IAFrC,KAAAF,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAfrB,KAAAC,MAAM,GAAG,KAAK;IAiBnB;IACA,IAAI,CAACC,eAAe,GAAG,IAAItE,OAAO,EAAE;EACtC;EAEA;EACA;EAEA;;;EAGAuE,QAAQA,CAAA;IACN;IACA,IAAI,CAACL,OAAO,CAACM,MAAM,CAChBC,IAAI,CACHxE,MAAM,CAACyE,KAAK,IAAIA,KAAK,YAAY3E,aAAa,CAAC,EAC/CG,SAAS,CAAC,IAAI,CAACoE,eAAe,CAAC,CAChC,CACAK,SAAS,CAAED,KAAoB,IAAI;MAClC;MACA,IAAI,IAAI,CAACE,oBAAoB,CAAC,IAAI,CAACxD,IAAI,EAAEsD,KAAK,CAACG,iBAAiB,CAAC,EAAE;QACjE,IAAI,CAACC,MAAM,EAAE;OACd,MAAM;QACL,IAAI,CAACC,QAAQ,EAAE;;IAEnB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACZ,gBAAgB,CAACa,aAAa,CAACP,IAAI,CAACvE,SAAS,CAAC,IAAI,CAACoE,eAAe,CAAC,CAAC,CAACK,SAAS,CAAC,MAAK;MACvF,IAAI,CAACM,WAAW,GAAG,IAAI,CAACd,gBAAgB,CAACc,WAAW;IACtD,CAAC,CAAC;IAEF;IACA,IAAI,CAACd,gBAAgB,CAACe,eAAe,CAACT,IAAI,CAACvE,SAAS,CAAC,IAAI,CAACoE,eAAe,CAAC,CAAC,CAACK,SAAS,CAACQ,WAAW,IAAG;MAClG,IAAIA,WAAW,IAAIA,WAAW,CAACpB,QAAQ,EAAE;QACvC;QACA,IAAI,IAAI,CAACqB,qBAAqB,CAAC,IAAI,CAAChE,IAAI,EAAE+D,WAAW,CAAC,EAAE;UACtD;;QAGF;QACA,IAAI,IAAI,CAACP,oBAAoB,CAAC,IAAI,CAACxD,IAAI,EAAE,IAAI,CAAC8C,OAAO,CAACZ,GAAG,CAAC,EAAE;UAC1D;;QAGF;QACA,IAAI,IAAI,CAAClC,IAAI,KAAK+D,WAAW,EAAE;UAC7B,IAAI,CAACJ,QAAQ,EAAE;;;IAGrB,CAAC,CAAC;IAEF;IACA;IACA,IAAI,IAAI,CAACH,oBAAoB,CAAC,IAAI,CAACxD,IAAI,EAAE,IAAI,CAAC8C,OAAO,CAACZ,GAAG,CAAC,EAAE;MAC1D,IAAI,CAACwB,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACC,QAAQ,EAAE;;EAEnB;EAEA;;;EAGAM,WAAWA,CAAA;IACT;IACA,IAAI,CAACf,eAAe,CAACgB,IAAI,EAAE;IAC3B,IAAI,CAAChB,eAAe,CAACiB,QAAQ,EAAE;EACjC;EAEA;EACA;EAEA;;;;;EAKAzE,UAAUA,CAAC0E,CAAC;IACVA,CAAC,CAACC,cAAc,EAAE;IAElB,IAAI,CAACpB,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAE1B;IACA,IAAI,CAACF,gBAAgB,CAACe,eAAe,CAACI,IAAI,CAAC,IAAI,CAAClE,IAAI,CAAC;IACrD,IAAI,CAAC+C,gBAAgB,CAACuB,qBAAqB,CAACJ,IAAI,EAAE;EACpD;EAEA;;;EAGAR,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACT,MAAM,EAAE;MACf;;IAGF,IAAI,CAACA,MAAM,GAAG,IAAI;IAElB;IACA,IAAI,CAACD,kBAAkB,CAACuB,YAAY,EAAE;IAEtC,IAAI,CAACxB,gBAAgB,CAACuB,qBAAqB,CAACJ,IAAI,EAAE;EACpD;EAEA;;;EAGAP,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACV,MAAM,EAAE;MAChB;;IAGF,IAAI,CAACA,MAAM,GAAG,KAAK;IAEnB;IACA,IAAI,CAACD,kBAAkB,CAACuB,YAAY,EAAE;IAEtC,IAAI,CAACxB,gBAAgB,CAACuB,qBAAqB,CAACJ,IAAI,EAAE;EACpD;EAEA;;;;;;;EAOAV,oBAAoBA,CAACgB,MAAM,EAAEtC,GAAG;IAC9B,MAAMS,QAAQ,GAAG6B,MAAM,CAAC7B,QAAQ;IAEhC;IACA,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,KAAK;;IAGd;IACA,KAAK,MAAM8B,KAAK,IAAI9B,QAAQ,EAAE;MAC5B;MACA,IAAI8B,KAAK,CAAC9B,QAAQ,EAAE;QAClB;QACA,IAAI,IAAI,CAACa,oBAAoB,CAACiB,KAAK,EAAEvC,GAAG,CAAC,EAAE;UACzC,OAAO,IAAI;;;MAIf;MACA,IAAIuC,KAAK,CAACvC,GAAG,KAAKA,GAAG,IAAIA,GAAG,CAACwC,QAAQ,CAACD,KAAK,CAACvC,GAAG,CAAC,EAAE;QAChD,OAAO,IAAI;;;IAIf,OAAO,KAAK;EACd;EAEA;;;;;;;EAOA8B,qBAAqBA,CAACQ,MAAM,EAAExE,IAAI;IAChC,MAAM2C,QAAQ,GAAG6B,MAAM,CAAC7B,QAAQ;IAEhC;IACA,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,KAAK;;IAGd;IACA,IAAIA,QAAQ,CAACgC,OAAO,CAAC3E,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/B,OAAO,IAAI;;IAGb,KAAK,MAAMyE,KAAK,IAAI9B,QAAQ,EAAE;MAC5B,IAAI8B,KAAK,CAAC9B,QAAQ,EAAE;QAClB;QACA,IAAI,IAAI,CAACqB,qBAAqB,CAACS,KAAK,EAAEzE,IAAI,CAAC,EAAE;UAC3C,OAAO,IAAI;;;;IAKjB,OAAO,KAAK;EACd;EAAC,QAAA4E,CAAA;qBA/MUhC,oCAAoC,EAAA7D,EAAA,CAAA8F,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhG,EAAA,CAAA8F,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlG,EAAA,CAAA8F,iBAAA,CAAA9F,EAAA,CAAAmG,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAApCvC,oCAAoC;IAAAwC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,kDAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCfjDzG,EAAA,CAAAY,UAAA,IAAA+F,4DAAA,0BAyCe;;;QAzCA3G,EAAA,CAAAe,UAAA,UAAA2F,GAAA,CAAAzF,IAAA,CAAA2F,MAAA,CAAkB;;;yNDepB/C,oCAAoC;IAAAgD,aAAA;EAAA", "names": ["NavigationEnd", "Subject", "filter", "takeUntil", "i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵlistener", "CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_Template_a_click_0_listener", "$event", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "toggle<PERSON><PERSON>", "ɵɵtemplate", "CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_ng_container_1_Template", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "item", "classes", "ɵɵadvance", "_r2", "ɵɵelement", "ctx_r10", "icon", "ctx_r11", "ɵɵelementContainerStart", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_i_1_Template", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_span_2_Template", "ɵɵelementContainerEnd", "ctx_r8", "startsWith", "ɵɵtext", "ctx_r9", "badge", "translate", "ɵɵtextInterpolate1", "title", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_ng_container_0_Template", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_span_3_Template", "ctx_r3", "ɵɵtextInterpolate", "item_r12", "ɵɵpureFunction1", "_c1", "disabled", "openInNewTab", "_c2", "exactMatch", "ɵɵpureFunction0", "_c3", "_c4", "url", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_li_1_Template", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_li_2_Template", "type", "CoreMenuVerticalCollapsibleComponent_ng_container_0_a_1_Template", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_template_2_Template", "ɵɵtemplateRefExtractor", "CoreMenuVerticalCollapsibleComponent_ng_container_0_ng_container_5_Template", "ctx_r0", "children", "CoreMenuVerticalCollapsibleComponent", "constructor", "_router", "_coreMenuService", "_changeDetectorRef", "isOpen", "_unsubscribeAll", "ngOnInit", "events", "pipe", "event", "subscribe", "confirmUrlInChildren", "urlAfterRedirects", "expand", "collapse", "onMenuChanged", "currentUser", "onItemCollapsed", "clickedItem", "confirmItemInChildren", "ngOnDestroy", "next", "complete", "e", "preventDefault", "onItemCollapseToggled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "child", "includes", "indexOf", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "CoreMenuService", "ChangeDetectorRef", "_2", "selectors", "hostVars", "hostBindings", "CoreMenuVerticalCollapsibleComponent_HostBindings", "rf", "ctx", "CoreMenuVerticalCollapsibleComponent_ng_container_0_Template", "hidden", "encapsulation"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\vertical\\collapsible\\collapsible.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\vertical\\collapsible\\collapsible.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, HostBinding, Input, OnD<PERSON>roy, OnInit } from '@angular/core';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\n\r\nimport { Subject } from 'rxjs';\r\nimport { filter, takeUntil } from 'rxjs/operators';\r\n\r\nimport { CoreMenuItem } from '@core/types';\r\nimport { CoreMenuService } from '@core/components/core-menu/core-menu.service';\r\n\r\nimport { User } from 'app/interfaces/user';\r\n\r\n@Component({\r\n  selector: '[core-menu-vertical-collapsible]',\r\n  templateUrl: './collapsible.component.html'\r\n})\r\nexport class CoreMenuVerticalCollapsibleComponent implements OnInit, OnDestroy {\r\n  currentUser: User;\r\n\r\n  @Input()\r\n  item: CoreMenuItem;\r\n\r\n  @HostBinding('class.open')\r\n  public isOpen = false;\r\n\r\n  // Private\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {Router} _router\r\n   * @param {CoreMenuService} _coreMenuService\r\n   * @param {ChangeDetectorRef} _changeDetectorRef\r\n   */\r\n  constructor(\r\n    private _router: Router,\r\n    private _coreMenuService: CoreMenuService,\r\n    private _changeDetectorRef: ChangeDetectorRef\r\n  ) {\r\n    // Set the private defaults\r\n    this._unsubscribeAll = new Subject();\r\n  }\r\n\r\n  // Lifecycle hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit(): void {\r\n    // Listen for router events and expand\r\n    this._router.events\r\n      .pipe(\r\n        filter(event => event instanceof NavigationEnd),\r\n        takeUntil(this._unsubscribeAll)\r\n      )\r\n      .subscribe((event: NavigationEnd) => {\r\n        // Confirm if the urlAfterRedirects can be found in one of the children of this item\r\n        if (this.confirmUrlInChildren(this.item, event.urlAfterRedirects)) {\r\n          this.expand();\r\n        } else {\r\n          this.collapse();\r\n        }\r\n      });\r\n\r\n    // Subscribe to the current menu changes\r\n    this._coreMenuService.onMenuChanged.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {\r\n      this.currentUser = this._coreMenuService.currentUser;\r\n    });\r\n\r\n    // Listen for collapsing of any menu item\r\n    this._coreMenuService.onItemCollapsed.pipe(takeUntil(this._unsubscribeAll)).subscribe(clickedItem => {\r\n      if (clickedItem && clickedItem.children) {\r\n        // Check if the clicked item is one of the children of this item\r\n        if (this.confirmItemInChildren(this.item, clickedItem)) {\r\n          return;\r\n        }\r\n\r\n        // Check if the url can be found in one of the children of this item\r\n        if (this.confirmUrlInChildren(this.item, this._router.url)) {\r\n          return;\r\n        }\r\n\r\n        // If the clicked item is not this item, collapse...\r\n        if (this.item !== clickedItem) {\r\n          this.collapse();\r\n        }\r\n      }\r\n    });\r\n\r\n    // Check if the url can be found in one of the children of this item\r\n    // Required for onInit case (i.e switching theme customizer menu layout)\r\n    if (this.confirmUrlInChildren(this.item, this._router.url)) {\r\n      this.expand();\r\n    } else {\r\n      this.collapse();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * On destroy\r\n   */\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n\r\n  // Public methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Toggle collapse\r\n   *\r\n   * @param e\r\n   */\r\n  toggleOpen(e): void {\r\n    e.preventDefault();\r\n\r\n    this.isOpen = !this.isOpen;\r\n\r\n    // Menu collapse toggled...\r\n    this._coreMenuService.onItemCollapsed.next(this.item);\r\n    this._coreMenuService.onItemCollapseToggled.next();\r\n  }\r\n\r\n  /**\r\n   * Expand the collapsible menu\r\n   */\r\n  expand(): void {\r\n    if (this.isOpen) {\r\n      return;\r\n    }\r\n\r\n    this.isOpen = true;\r\n\r\n    // Mark for check\r\n    this._changeDetectorRef.markForCheck();\r\n\r\n    this._coreMenuService.onItemCollapseToggled.next();\r\n  }\r\n\r\n  /**\r\n   * Collapse the collapsible menu\r\n   */\r\n  collapse(): void {\r\n    if (!this.isOpen) {\r\n      return;\r\n    }\r\n\r\n    this.isOpen = false;\r\n\r\n    // Mark for check\r\n    this._changeDetectorRef.markForCheck();\r\n\r\n    this._coreMenuService.onItemCollapseToggled.next();\r\n  }\r\n\r\n  /**\r\n   * Confirms if the provided url can be found in one of the given parent's children\r\n   *\r\n   * @param parent\r\n   * @param url\r\n   * @returns {boolean}\r\n   */\r\n  confirmUrlInChildren(parent, url): boolean {\r\n    const children = parent.children;\r\n\r\n    // Return false if parent don't have any children\r\n    if (!children) {\r\n      return false;\r\n    }\r\n\r\n    // Loop all the children\r\n    for (const child of children) {\r\n      // If children has child (Sub to sub item url)\r\n      if (child.children) {\r\n        // Call function again with child\r\n        if (this.confirmUrlInChildren(child, url)) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // If child.url is same as provided url\r\n      if (child.url === url || url.includes(child.url)) {\r\n        return true;\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if the provided parent has the provided item in one of its children\r\n   *\r\n   * @param parent\r\n   * @param item\r\n   * @returns {boolean}\r\n   */\r\n  confirmItemInChildren(parent, item): boolean {\r\n    const children = parent.children;\r\n\r\n    // Return false if parent don't have any children\r\n    if (!children) {\r\n      return false;\r\n    }\r\n\r\n    // Return true parent has the provided item in one of its children\r\n    if (children.indexOf(item) > -1) {\r\n      return true;\r\n    }\r\n\r\n    for (const child of children) {\r\n      if (child.children) {\r\n        // Call function again with child (for sub to sub item)\r\n        if (this.confirmItemInChildren(child, item)) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n}\r\n", "<ng-container *ngIf=\"!item.hidden\">\r\n  <!-- collapsible title -->\r\n  <a class=\"d-flex align-items-center\" [ngClass]=\"item.classes\" *ngIf=\"!item.url\" (click)=\"toggleOpen($event)\">\r\n    <ng-container *ngTemplateOutlet=\"itemContent\"></ng-container>\r\n  </a>\r\n\r\n  <!-- itemContent template -->\r\n  <ng-template #itemContent>\r\n    <ng-container *ngIf=\"item.icon\">\r\n      <i [ngClass]=\"item.icon\" *ngIf=\"item.icon.startsWith('fa-')\"></i>\r\n      <span [data-feather]=\"item.icon\" *ngIf=\"!item.icon.startsWith('fa-')\"></span>\r\n    </ng-container>\r\n    <span class=\"menu-title text-truncate\" [translate]=\"item.translate\">{{ item.title }}</span>\r\n    <span\r\n      class=\"badge ml-auto mr-1\"\r\n      *ngIf=\"item.badge\"\r\n      [translate]=\"item.badge.translate\"\r\n      [ngClass]=\"item.badge.classes\"\r\n    >\r\n      {{ item.badge.title }}\r\n    </span>\r\n  </ng-template>\r\n\r\n  <!-- sub-menu item/collapsible -->\r\n  <ul class=\"menu-content\">\r\n    <ng-container *ngFor=\"let item of item.children\">\r\n      <!-- item -->\r\n      <li\r\n        core-menu-vertical-item\r\n        [item]=\"item\"\r\n        *ngIf=\"item.type == 'item'\"\r\n        [ngClass]=\"{ disabled: item.disabled === true }\"\r\n        [routerLinkActive]=\"!item.openInNewTab ? 'active' : ''\"\r\n        [routerLinkActiveOptions]=\"{ exact: item.exactMatch || false }\"\r\n      >\r\n        <span [routerLink]=\"item.openInNewTab ? [] : [item.url]\" class=\"d-none\"></span>\r\n      </li>\r\n      <!-- collapsible -->\r\n      <li core-menu-vertical-collapsible *ngIf=\"item.type == 'collapsible'\" [item]=\"item\" class=\"nav-item has-sub\"></li>\r\n    </ng-container>\r\n  </ul>\r\n</ng-container>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
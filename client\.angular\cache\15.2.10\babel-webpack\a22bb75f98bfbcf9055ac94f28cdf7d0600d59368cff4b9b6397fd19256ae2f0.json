{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\n/**\r\n * Sanitize HTML\r\n */\nexport class SafePipe {\n  /**\r\n   * Pi<PERSON> Constructor\r\n   *\r\n   * @param _sanitizer: DomSanitezer\r\n   */\n  // tslint:disable-next-line\n  constructor(_sanitizer) {\n    this._sanitizer = _sanitizer;\n  }\n  /**\r\n   * Transform\r\n   *\r\n   * @param value: string\r\n   * @param type: string\r\n   */\n  transform(value, type) {\n    switch (type) {\n      case 'html':\n        return this._sanitizer.bypassSecurityTrustHtml(value);\n      case 'style':\n        return this._sanitizer.bypassSecurityTrustStyle(value);\n      case 'script':\n        return this._sanitizer.bypassSecurityTrustScript(value);\n      case 'url':\n        return this._sanitizer.bypassSecurityTrustUrl(value);\n      case 'resourceUrl':\n        return this._sanitizer.bypassSecurityTrustResourceUrl(value);\n      default:\n        return this._sanitizer.bypassSecurityTrustHtml(value);\n    }\n  }\n  static #_ = this.ɵfac = function SafePipe_Factory(t) {\n    return new (t || SafePipe)(i0.ɵɵdirectiveInject(i1.<PERSON><PERSON><PERSON>ti<PERSON>, 16));\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"safe\",\n    type: SafePipe,\n    pure: true\n  });\n}", "map": {"version": 3, "mappings": ";;AAGA;;;AAMA,OAAM,MAAOA,QAAQ;EACnB;;;;;EAKA;EACAC,YAAsBC,UAAwB;IAAxB,KAAAA,UAAU,GAAVA,UAAU;EAAiB;EAEjD;;;;;;EAMAC,SAASA,CAACC,KAAa,EAAEC,IAAY;IACnC,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,OAAO,IAAI,CAACH,UAAU,CAACI,uBAAuB,CAACF,KAAK,CAAC;MACvD,KAAK,OAAO;QACV,OAAO,IAAI,CAACF,UAAU,CAACK,wBAAwB,CAACH,KAAK,CAAC;MACxD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACF,UAAU,CAACM,yBAAyB,CAACJ,KAAK,CAAC;MACzD,KAAK,KAAK;QACR,OAAO,IAAI,CAACF,UAAU,CAACO,sBAAsB,CAACL,KAAK,CAAC;MACtD,KAAK,aAAa;QAChB,OAAO,IAAI,CAACF,UAAU,CAACQ,8BAA8B,CAACN,KAAK,CAAC;MAC9D;QACE,OAAO,IAAI,CAACF,UAAU,CAACI,uBAAuB,CAACF,KAAK,CAAC;;EAE3D;EAAC,QAAAO,CAAA;qBA9BUX,QAAQ,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA;;UAARhB,QAAQ;IAAAiB,IAAA;EAAA", "names": ["SafePipe", "constructor", "_sanitizer", "transform", "value", "type", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "_", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "_2", "pure"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\pipes\\safe.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport { <PERSON><PERSON>anitizer, SafeHtml, SafeStyle, SafeScript, SafeUrl, SafeResourceUrl } from '@angular/platform-browser';\r\n\r\n/**\r\n * Sanitize HTML\r\n */\r\n@Pipe({\r\n  name: 'safe'\r\n})\r\nexport class SafePipe implements PipeTransform {\r\n  /**\r\n   * Pipe Constructor\r\n   *\r\n   * @param _sanitizer: DomSanitezer\r\n   */\r\n  // tslint:disable-next-line\r\n  constructor(protected _sanitizer: DomSanitizer) {}\r\n\r\n  /**\r\n   * Transform\r\n   *\r\n   * @param value: string\r\n   * @param type: string\r\n   */\r\n  transform(value: string, type: string): SafeHtml | SafeStyle | SafeScript | SafeUrl | SafeResourceUrl {\r\n    switch (type) {\r\n      case 'html':\r\n        return this._sanitizer.bypassSecurityTrustHtml(value);\r\n      case 'style':\r\n        return this._sanitizer.bypassSecurityTrustStyle(value);\r\n      case 'script':\r\n        return this._sanitizer.bypassSecurityTrustScript(value);\r\n      case 'url':\r\n        return this._sanitizer.bypassSecurityTrustUrl(value);\r\n      case 'resourceUrl':\r\n        return this._sanitizer.bypassSecurityTrustResourceUrl(value);\r\n      default:\r\n        return this._sanitizer.bypassSecurityTrustHtml(value);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { zip as zipStatic } from '../observable/zip';\nexport function zip(...observables) {\n  return function zipOperatorFunction(source) {\n    return source.lift.call(zipStatic(source, ...observables));\n  };\n}", "map": {"version": 3, "names": ["zip", "zipStatic", "observables", "zipOperatorFunction", "source", "lift", "call"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/zip.js"], "sourcesContent": ["import { zip as zipStatic } from '../observable/zip';\nexport function zip(...observables) {\n    return function zipOperatorFunction(source) {\n        return source.lift.call(zipStatic(source, ...observables));\n    };\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,SAAS,QAAQ,mBAAmB;AACpD,OAAO,SAASD,GAAGA,CAAC,GAAGE,WAAW,EAAE;EAChC,OAAO,SAASC,mBAAmBA,CAACC,MAAM,EAAE;IACxC,OAAOA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACL,SAAS,CAACG,MAAM,EAAE,GAAGF,WAAW,CAAC,CAAC;EAC9D,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
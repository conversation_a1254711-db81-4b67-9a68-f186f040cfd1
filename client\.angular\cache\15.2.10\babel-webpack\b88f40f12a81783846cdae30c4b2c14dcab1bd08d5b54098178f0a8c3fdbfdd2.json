{"ast": null, "code": "import { ErrorFactory, contains, deepExtend, createSubscribe, isBrowser } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport * as modularAPIs from '@firebase/app';\nimport { _addComponent, deleteApp, _DEFAULT_ENTRY_NAME, _addOrOverwriteComponent, registerVersion } from '@firebase/app';\nimport { Logger } from '@firebase/logger';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Global context object for a collection of services using\r\n * a shared authentication state.\r\n *\r\n * marked as internal because it references internal types exported from @firebase/app\r\n * @internal\r\n */\nclass FirebaseAppImpl {\n  constructor(_delegate, firebase) {\n    this._delegate = _delegate;\n    this.firebase = firebase;\n    // add itself to container\n    _addComponent(_delegate, new Component('app-compat', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n    this.container = _delegate.container;\n  }\n  get automaticDataCollectionEnabled() {\n    return this._delegate.automaticDataCollectionEnabled;\n  }\n  set automaticDataCollectionEnabled(val) {\n    this._delegate.automaticDataCollectionEnabled = val;\n  }\n  get name() {\n    return this._delegate.name;\n  }\n  get options() {\n    return this._delegate.options;\n  }\n  delete() {\n    return new Promise(resolve => {\n      this._delegate.checkDestroyed();\n      resolve();\n    }).then(() => {\n      this.firebase.INTERNAL.removeApp(this.name);\n      return deleteApp(this._delegate);\n    });\n  }\n  /**\r\n   * Return a service instance associated with this app (creating it\r\n   * on demand), identified by the passed instanceIdentifier.\r\n   *\r\n   * NOTE: Currently storage and functions are the only ones that are leveraging this\r\n   * functionality. They invoke it by calling:\r\n   *\r\n   * ```javascript\r\n   * firebase.app().storage('STORAGE BUCKET ID')\r\n   * ```\r\n   *\r\n   * The service name is passed to this already\r\n   * @internal\r\n   */\n  _getService(name, instanceIdentifier = _DEFAULT_ENTRY_NAME) {\n    var _a;\n    this._delegate.checkDestroyed();\n    // Initialize instance if InstatiationMode is `EXPLICIT`.\n    const provider = this._delegate.container.getProvider(name);\n    if (!provider.isInitialized() && ((_a = provider.getComponent()) === null || _a === void 0 ? void 0 : _a.instantiationMode) === \"EXPLICIT\" /* InstantiationMode.EXPLICIT */) {\n      provider.initialize();\n    }\n    // getImmediate will always succeed because _getService is only called for registered components.\n    return provider.getImmediate({\n      identifier: instanceIdentifier\n    });\n  }\n  /**\r\n   * Remove a service instance from the cache, so we will create a new instance for this service\r\n   * when people try to get it again.\r\n   *\r\n   * NOTE: currently only firestore uses this functionality to support firestore shutdown.\r\n   *\r\n   * @param name The service name\r\n   * @param instanceIdentifier instance identifier in case multiple instances are allowed\r\n   * @internal\r\n   */\n  _removeServiceInstance(name, instanceIdentifier = _DEFAULT_ENTRY_NAME) {\n    this._delegate.container\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .getProvider(name).clearInstance(instanceIdentifier);\n  }\n  /**\r\n   * @param component the component being added to this app's container\r\n   * @internal\r\n   */\n  _addComponent(component) {\n    _addComponent(this._delegate, component);\n  }\n  _addOrOverwriteComponent(component) {\n    _addOrOverwriteComponent(this._delegate, component);\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      automaticDataCollectionEnabled: this.automaticDataCollectionEnabled,\n      options: this.options\n    };\n  }\n}\n// TODO: investigate why the following needs to be commented out\n// Prevent dead-code elimination of these methods w/o invalid property\n// copying.\n// (FirebaseAppImpl.prototype.name && FirebaseAppImpl.prototype.options) ||\n//   FirebaseAppImpl.prototype.delete ||\n//   console.log('dc');\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" + 'call Firebase App.initializeApp()',\n  [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app-compat', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Because auth can't share code with other components, we attach the utility functions\r\n * in an internal namespace to share code.\r\n * This function return a firebase namespace object without\r\n * any utility functions, so it can be shared between the regular firebaseNamespace and\r\n * the lite version.\r\n */\nfunction createFirebaseNamespaceCore(firebaseAppImpl) {\n  const apps = {};\n  // // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  // const components = new Map<string, Component<any>>();\n  // A namespace is a plain JavaScript Object.\n  const namespace = {\n    // Hack to prevent Babel from modifying the object returned\n    // as the firebase namespace.\n    // @ts-ignore\n    __esModule: true,\n    initializeApp: initializeAppCompat,\n    // @ts-ignore\n    app,\n    registerVersion: modularAPIs.registerVersion,\n    setLogLevel: modularAPIs.setLogLevel,\n    onLog: modularAPIs.onLog,\n    // @ts-ignore\n    apps: null,\n    SDK_VERSION: modularAPIs.SDK_VERSION,\n    INTERNAL: {\n      registerComponent: registerComponentCompat,\n      removeApp,\n      useAsService,\n      modularAPIs\n    }\n  };\n  // Inject a circular default export to allow Babel users who were previously\n  // using:\n  //\n  //   import firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase').default;\n  //\n  // instead of\n  //\n  //   import * as firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase');\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  namespace['default'] = namespace;\n  // firebase.apps is a read-only getter.\n  Object.defineProperty(namespace, 'apps', {\n    get: getApps\n  });\n  /**\r\n   * Called by App.delete() - but before any services associated with the App\r\n   * are deleted.\r\n   */\n  function removeApp(name) {\n    delete apps[name];\n  }\n  /**\r\n   * Get the App object for a given name (or DEFAULT).\r\n   */\n  function app(name) {\n    name = name || modularAPIs._DEFAULT_ENTRY_NAME;\n    if (!contains(apps, name)) {\n      throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, {\n        appName: name\n      });\n    }\n    return apps[name];\n  }\n  // @ts-ignore\n  app['App'] = firebaseAppImpl;\n  /**\r\n   * Create a new App instance (name must be unique).\r\n   *\r\n   * This function is idempotent. It can be called more than once and return the same instance using the same options and config.\r\n   */\n  function initializeAppCompat(options, rawConfig = {}) {\n    const app = modularAPIs.initializeApp(options, rawConfig);\n    if (contains(apps, app.name)) {\n      return apps[app.name];\n    }\n    const appCompat = new firebaseAppImpl(app, namespace);\n    apps[app.name] = appCompat;\n    return appCompat;\n  }\n  /*\r\n   * Return an array of all the non-deleted FirebaseApps.\r\n   */\n  function getApps() {\n    // Make a copy so caller cannot mutate the apps list.\n    return Object.keys(apps).map(name => apps[name]);\n  }\n  function registerComponentCompat(component) {\n    const componentName = component.name;\n    const componentNameWithoutCompat = componentName.replace('-compat', '');\n    if (modularAPIs._registerComponent(component) && component.type === \"PUBLIC\" /* ComponentType.PUBLIC */) {\n      // create service namespace for public components\n      // The Service namespace is an accessor function ...\n      const serviceNamespace = (appArg = app()) => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (typeof appArg[componentNameWithoutCompat] !== 'function') {\n          // Invalid argument.\n          // This happens in the following case: firebase.storage('gs:/')\n          throw ERROR_FACTORY.create(\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */, {\n            appName: componentName\n          });\n        }\n        // Forward service instance lookup to the FirebaseApp.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return appArg[componentNameWithoutCompat]();\n      };\n      // ... and a container for service-level properties.\n      if (component.serviceProps !== undefined) {\n        deepExtend(serviceNamespace, component.serviceProps);\n      }\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      namespace[componentNameWithoutCompat] = serviceNamespace;\n      // Patch the FirebaseAppImpl prototype\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      firebaseAppImpl.prototype[componentNameWithoutCompat] =\n      // TODO: The eslint disable can be removed and the 'ignoreRestArgs'\n      // option added to the no-explicit-any rule when ESlint releases it.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      function (...args) {\n        const serviceFxn = this._getService.bind(this, componentName);\n        return serviceFxn.apply(this, component.multipleInstances ? args : []);\n      };\n    }\n    return component.type === \"PUBLIC\" /* ComponentType.PUBLIC */ ?\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    namespace[componentNameWithoutCompat] : null;\n  }\n  // Map the requested service to a registered service name\n  // (used to map auth to serverAuth service when needed).\n  function useAsService(app, name) {\n    if (name === 'serverAuth') {\n      return null;\n    }\n    const useService = name;\n    return useService;\n  }\n  return namespace;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Return a firebase namespace object.\r\n *\r\n * In production, this will be called exactly once and the result\r\n * assigned to the 'firebase' global.  It may be called multiple times\r\n * in unit tests.\r\n */\nfunction createFirebaseNamespace() {\n  const namespace = createFirebaseNamespaceCore(FirebaseAppImpl);\n  namespace.INTERNAL = Object.assign(Object.assign({}, namespace.INTERNAL), {\n    createFirebaseNamespace,\n    extendNamespace,\n    createSubscribe,\n    ErrorFactory,\n    deepExtend\n  });\n  /**\r\n   * Patch the top-level firebase namespace with additional properties.\r\n   *\r\n   * firebase.INTERNAL.extendNamespace()\r\n   */\n  function extendNamespace(props) {\n    deepExtend(namespace, props);\n  }\n  return namespace;\n}\nconst firebase$1 = createFirebaseNamespace();\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/app-compat');\nconst name = \"@firebase/app-compat\";\nconst version = \"0.2.32\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction registerCoreComponents(variant) {\n  // Register `app` package.\n  registerVersion(name, version, variant);\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// Firebase Lite detection\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nif (isBrowser() && self.firebase !== undefined) {\n  logger.warn(`\n    Warning: Firebase is already defined in the global scope. Please make sure\n    Firebase library is only loaded once.\n  `);\n  // eslint-disable-next-line\n  const sdkVersion = self.firebase.SDK_VERSION;\n  if (sdkVersion && sdkVersion.indexOf('LITE') >= 0) {\n    logger.warn(`\n    Warning: You are trying to load Firebase while using Firebase Performance standalone script.\n    You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.\n    `);\n  }\n}\nconst firebase = firebase$1;\nregisterCoreComponents();\nexport { firebase as default };", "map": {"version": 3, "names": ["ErrorFactory", "contains", "deepExtend", "createSubscribe", "<PERSON><PERSON><PERSON><PERSON>", "Component", "modularAPIs", "_addComponent", "deleteApp", "_DEFAULT_ENTRY_NAME", "_addOrOverwriteComponent", "registerVersion", "<PERSON><PERSON>", "FirebaseAppImpl", "constructor", "_delegate", "firebase", "container", "automaticDataCollectionEnabled", "val", "name", "options", "delete", "Promise", "resolve", "checkDestroyed", "then", "INTERNAL", "removeApp", "_getService", "instanceIdentifier", "_a", "provider", "get<PERSON><PERSON><PERSON>", "isInitialized", "getComponent", "instantiationMode", "initialize", "getImmediate", "identifier", "_removeServiceInstance", "clearInstance", "component", "toJSON", "ERRORS", "ERROR_FACTORY", "createFirebaseNamespaceCore", "firebaseAppImpl", "apps", "namespace", "__esModule", "initializeApp", "initializeAppCompat", "app", "setLogLevel", "onLog", "SDK_VERSION", "registerComponent", "registerComponentCompat", "useAsService", "Object", "defineProperty", "get", "getApps", "create", "appName", "rawConfig", "appCompat", "keys", "map", "componentName", "componentNameWithoutCompat", "replace", "_registerComponent", "type", "serviceNamespace", "appArg", "serviceProps", "undefined", "prototype", "args", "serviceFxn", "bind", "apply", "multipleInstances", "useService", "createFirebaseNamespace", "assign", "extendNamespace", "props", "firebase$1", "logger", "version", "registerCoreComponents", "variant", "self", "warn", "sdkVersion", "indexOf", "default"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@firebase/app-compat/dist/esm/index.esm2017.js"], "sourcesContent": ["import { ErrorFactory, contains, deepExtend, createSubscribe, isBrowser } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport * as modularAPIs from '@firebase/app';\nimport { _addComponent, deleteApp, _DEFAULT_ENTRY_NAME, _addOrOverwriteComponent, registerVersion } from '@firebase/app';\nimport { Logger } from '@firebase/logger';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Global context object for a collection of services using\r\n * a shared authentication state.\r\n *\r\n * marked as internal because it references internal types exported from @firebase/app\r\n * @internal\r\n */\r\nclass FirebaseAppImpl {\r\n    constructor(_delegate, firebase) {\r\n        this._delegate = _delegate;\r\n        this.firebase = firebase;\r\n        // add itself to container\r\n        _addComponent(_delegate, new Component('app-compat', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n        this.container = _delegate.container;\r\n    }\r\n    get automaticDataCollectionEnabled() {\r\n        return this._delegate.automaticDataCollectionEnabled;\r\n    }\r\n    set automaticDataCollectionEnabled(val) {\r\n        this._delegate.automaticDataCollectionEnabled = val;\r\n    }\r\n    get name() {\r\n        return this._delegate.name;\r\n    }\r\n    get options() {\r\n        return this._delegate.options;\r\n    }\r\n    delete() {\r\n        return new Promise(resolve => {\r\n            this._delegate.checkDestroyed();\r\n            resolve();\r\n        }).then(() => {\r\n            this.firebase.INTERNAL.removeApp(this.name);\r\n            return deleteApp(this._delegate);\r\n        });\r\n    }\r\n    /**\r\n     * Return a service instance associated with this app (creating it\r\n     * on demand), identified by the passed instanceIdentifier.\r\n     *\r\n     * NOTE: Currently storage and functions are the only ones that are leveraging this\r\n     * functionality. They invoke it by calling:\r\n     *\r\n     * ```javascript\r\n     * firebase.app().storage('STORAGE BUCKET ID')\r\n     * ```\r\n     *\r\n     * The service name is passed to this already\r\n     * @internal\r\n     */\r\n    _getService(name, instanceIdentifier = _DEFAULT_ENTRY_NAME) {\r\n        var _a;\r\n        this._delegate.checkDestroyed();\r\n        // Initialize instance if InstatiationMode is `EXPLICIT`.\r\n        const provider = this._delegate.container.getProvider(name);\r\n        if (!provider.isInitialized() &&\r\n            ((_a = provider.getComponent()) === null || _a === void 0 ? void 0 : _a.instantiationMode) === \"EXPLICIT\" /* InstantiationMode.EXPLICIT */) {\r\n            provider.initialize();\r\n        }\r\n        // getImmediate will always succeed because _getService is only called for registered components.\r\n        return provider.getImmediate({\r\n            identifier: instanceIdentifier\r\n        });\r\n    }\r\n    /**\r\n     * Remove a service instance from the cache, so we will create a new instance for this service\r\n     * when people try to get it again.\r\n     *\r\n     * NOTE: currently only firestore uses this functionality to support firestore shutdown.\r\n     *\r\n     * @param name The service name\r\n     * @param instanceIdentifier instance identifier in case multiple instances are allowed\r\n     * @internal\r\n     */\r\n    _removeServiceInstance(name, instanceIdentifier = _DEFAULT_ENTRY_NAME) {\r\n        this._delegate.container\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            .getProvider(name)\r\n            .clearInstance(instanceIdentifier);\r\n    }\r\n    /**\r\n     * @param component the component being added to this app's container\r\n     * @internal\r\n     */\r\n    _addComponent(component) {\r\n        _addComponent(this._delegate, component);\r\n    }\r\n    _addOrOverwriteComponent(component) {\r\n        _addOrOverwriteComponent(this._delegate, component);\r\n    }\r\n    toJSON() {\r\n        return {\r\n            name: this.name,\r\n            automaticDataCollectionEnabled: this.automaticDataCollectionEnabled,\r\n            options: this.options\r\n        };\r\n    }\r\n}\r\n// TODO: investigate why the following needs to be commented out\r\n// Prevent dead-code elimination of these methods w/o invalid property\r\n// copying.\r\n// (FirebaseAppImpl.prototype.name && FirebaseAppImpl.prototype.options) ||\r\n//   FirebaseAppImpl.prototype.delete ||\r\n//   console.log('dc');\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERRORS = {\r\n    [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" +\r\n        'call Firebase App.initializeApp()',\r\n    [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' +\r\n        'Firebase App instance.'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('app-compat', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Because auth can't share code with other components, we attach the utility functions\r\n * in an internal namespace to share code.\r\n * This function return a firebase namespace object without\r\n * any utility functions, so it can be shared between the regular firebaseNamespace and\r\n * the lite version.\r\n */\r\nfunction createFirebaseNamespaceCore(firebaseAppImpl) {\r\n    const apps = {};\r\n    // // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    // const components = new Map<string, Component<any>>();\r\n    // A namespace is a plain JavaScript Object.\r\n    const namespace = {\r\n        // Hack to prevent Babel from modifying the object returned\r\n        // as the firebase namespace.\r\n        // @ts-ignore\r\n        __esModule: true,\r\n        initializeApp: initializeAppCompat,\r\n        // @ts-ignore\r\n        app,\r\n        registerVersion: modularAPIs.registerVersion,\r\n        setLogLevel: modularAPIs.setLogLevel,\r\n        onLog: modularAPIs.onLog,\r\n        // @ts-ignore\r\n        apps: null,\r\n        SDK_VERSION: modularAPIs.SDK_VERSION,\r\n        INTERNAL: {\r\n            registerComponent: registerComponentCompat,\r\n            removeApp,\r\n            useAsService,\r\n            modularAPIs\r\n        }\r\n    };\r\n    // Inject a circular default export to allow Babel users who were previously\r\n    // using:\r\n    //\r\n    //   import firebase from 'firebase';\r\n    //   which becomes: var firebase = require('firebase').default;\r\n    //\r\n    // instead of\r\n    //\r\n    //   import * as firebase from 'firebase';\r\n    //   which becomes: var firebase = require('firebase');\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    namespace['default'] = namespace;\r\n    // firebase.apps is a read-only getter.\r\n    Object.defineProperty(namespace, 'apps', {\r\n        get: getApps\r\n    });\r\n    /**\r\n     * Called by App.delete() - but before any services associated with the App\r\n     * are deleted.\r\n     */\r\n    function removeApp(name) {\r\n        delete apps[name];\r\n    }\r\n    /**\r\n     * Get the App object for a given name (or DEFAULT).\r\n     */\r\n    function app(name) {\r\n        name = name || modularAPIs._DEFAULT_ENTRY_NAME;\r\n        if (!contains(apps, name)) {\r\n            throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, { appName: name });\r\n        }\r\n        return apps[name];\r\n    }\r\n    // @ts-ignore\r\n    app['App'] = firebaseAppImpl;\r\n    /**\r\n     * Create a new App instance (name must be unique).\r\n     *\r\n     * This function is idempotent. It can be called more than once and return the same instance using the same options and config.\r\n     */\r\n    function initializeAppCompat(options, rawConfig = {}) {\r\n        const app = modularAPIs.initializeApp(options, rawConfig);\r\n        if (contains(apps, app.name)) {\r\n            return apps[app.name];\r\n        }\r\n        const appCompat = new firebaseAppImpl(app, namespace);\r\n        apps[app.name] = appCompat;\r\n        return appCompat;\r\n    }\r\n    /*\r\n     * Return an array of all the non-deleted FirebaseApps.\r\n     */\r\n    function getApps() {\r\n        // Make a copy so caller cannot mutate the apps list.\r\n        return Object.keys(apps).map(name => apps[name]);\r\n    }\r\n    function registerComponentCompat(component) {\r\n        const componentName = component.name;\r\n        const componentNameWithoutCompat = componentName.replace('-compat', '');\r\n        if (modularAPIs._registerComponent(component) &&\r\n            component.type === \"PUBLIC\" /* ComponentType.PUBLIC */) {\r\n            // create service namespace for public components\r\n            // The Service namespace is an accessor function ...\r\n            const serviceNamespace = (appArg = app()) => {\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                if (typeof appArg[componentNameWithoutCompat] !== 'function') {\r\n                    // Invalid argument.\r\n                    // This happens in the following case: firebase.storage('gs:/')\r\n                    throw ERROR_FACTORY.create(\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */, {\r\n                        appName: componentName\r\n                    });\r\n                }\r\n                // Forward service instance lookup to the FirebaseApp.\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                return appArg[componentNameWithoutCompat]();\r\n            };\r\n            // ... and a container for service-level properties.\r\n            if (component.serviceProps !== undefined) {\r\n                deepExtend(serviceNamespace, component.serviceProps);\r\n            }\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            namespace[componentNameWithoutCompat] = serviceNamespace;\r\n            // Patch the FirebaseAppImpl prototype\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            firebaseAppImpl.prototype[componentNameWithoutCompat] =\r\n                // TODO: The eslint disable can be removed and the 'ignoreRestArgs'\r\n                // option added to the no-explicit-any rule when ESlint releases it.\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                function (...args) {\r\n                    const serviceFxn = this._getService.bind(this, componentName);\r\n                    return serviceFxn.apply(this, component.multipleInstances ? args : []);\r\n                };\r\n        }\r\n        return component.type === \"PUBLIC\" /* ComponentType.PUBLIC */\r\n            ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                namespace[componentNameWithoutCompat]\r\n            : null;\r\n    }\r\n    // Map the requested service to a registered service name\r\n    // (used to map auth to serverAuth service when needed).\r\n    function useAsService(app, name) {\r\n        if (name === 'serverAuth') {\r\n            return null;\r\n        }\r\n        const useService = name;\r\n        return useService;\r\n    }\r\n    return namespace;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Return a firebase namespace object.\r\n *\r\n * In production, this will be called exactly once and the result\r\n * assigned to the 'firebase' global.  It may be called multiple times\r\n * in unit tests.\r\n */\r\nfunction createFirebaseNamespace() {\r\n    const namespace = createFirebaseNamespaceCore(FirebaseAppImpl);\r\n    namespace.INTERNAL = Object.assign(Object.assign({}, namespace.INTERNAL), { createFirebaseNamespace,\r\n        extendNamespace,\r\n        createSubscribe,\r\n        ErrorFactory,\r\n        deepExtend });\r\n    /**\r\n     * Patch the top-level firebase namespace with additional properties.\r\n     *\r\n     * firebase.INTERNAL.extendNamespace()\r\n     */\r\n    function extendNamespace(props) {\r\n        deepExtend(namespace, props);\r\n    }\r\n    return namespace;\r\n}\r\nconst firebase$1 = createFirebaseNamespace();\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst logger = new Logger('@firebase/app-compat');\n\nconst name = \"@firebase/app-compat\";\nconst version = \"0.2.32\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction registerCoreComponents(variant) {\r\n    // Register `app` package.\r\n    registerVersion(name, version, variant);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// Firebase Lite detection\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nif (isBrowser() && self.firebase !== undefined) {\r\n    logger.warn(`\n    Warning: Firebase is already defined in the global scope. Please make sure\n    Firebase library is only loaded once.\n  `);\r\n    // eslint-disable-next-line\r\n    const sdkVersion = self.firebase.SDK_VERSION;\r\n    if (sdkVersion && sdkVersion.indexOf('LITE') >= 0) {\r\n        logger.warn(`\n    Warning: You are trying to load Firebase while using Firebase Performance standalone script.\n    You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.\n    `);\r\n    }\r\n}\r\nconst firebase = firebase$1;\r\nregisterCoreComponents();\n\nexport { firebase as default };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,eAAe,EAAEC,SAAS,QAAQ,gBAAgB;AAC/F,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,KAAKC,WAAW,MAAM,eAAe;AAC5C,SAASC,aAAa,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,wBAAwB,EAAEC,eAAe,QAAQ,eAAe;AACxH,SAASC,MAAM,QAAQ,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;IACAT,aAAa,CAACQ,SAAS,EAAE,IAAIV,SAAS,CAAC,YAAY,EAAE,MAAM,IAAI,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;IACtG,IAAI,CAACY,SAAS,GAAGF,SAAS,CAACE,SAAS;EACxC;EACA,IAAIC,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAACH,SAAS,CAACG,8BAA8B;EACxD;EACA,IAAIA,8BAA8BA,CAACC,GAAG,EAAE;IACpC,IAAI,CAACJ,SAAS,CAACG,8BAA8B,GAAGC,GAAG;EACvD;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACL,SAAS,CAACK,IAAI;EAC9B;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACN,SAAS,CAACM,OAAO;EACjC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACT,SAAS,CAACU,cAAc,CAAC,CAAC;MAC/BD,OAAO,CAAC,CAAC;IACb,CAAC,CAAC,CAACE,IAAI,CAAC,MAAM;MACV,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAACC,SAAS,CAAC,IAAI,CAACR,IAAI,CAAC;MAC3C,OAAOZ,SAAS,CAAC,IAAI,CAACO,SAAS,CAAC;IACpC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,WAAWA,CAACT,IAAI,EAAEU,kBAAkB,GAAGrB,mBAAmB,EAAE;IACxD,IAAIsB,EAAE;IACN,IAAI,CAAChB,SAAS,CAACU,cAAc,CAAC,CAAC;IAC/B;IACA,MAAMO,QAAQ,GAAG,IAAI,CAACjB,SAAS,CAACE,SAAS,CAACgB,WAAW,CAACb,IAAI,CAAC;IAC3D,IAAI,CAACY,QAAQ,CAACE,aAAa,CAAC,CAAC,IACzB,CAAC,CAACH,EAAE,GAAGC,QAAQ,CAACG,YAAY,CAAC,CAAC,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,iBAAiB,MAAM,UAAU,CAAC,kCAAkC;MAC5IJ,QAAQ,CAACK,UAAU,CAAC,CAAC;IACzB;IACA;IACA,OAAOL,QAAQ,CAACM,YAAY,CAAC;MACzBC,UAAU,EAAET;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,sBAAsBA,CAACpB,IAAI,EAAEU,kBAAkB,GAAGrB,mBAAmB,EAAE;IACnE,IAAI,CAACM,SAAS,CAACE;IACX;IAAA,CACCgB,WAAW,CAACb,IAAI,CAAC,CACjBqB,aAAa,CAACX,kBAAkB,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIvB,aAAaA,CAACmC,SAAS,EAAE;IACrBnC,aAAa,CAAC,IAAI,CAACQ,SAAS,EAAE2B,SAAS,CAAC;EAC5C;EACAhC,wBAAwBA,CAACgC,SAAS,EAAE;IAChChC,wBAAwB,CAAC,IAAI,CAACK,SAAS,EAAE2B,SAAS,CAAC;EACvD;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO;MACHvB,IAAI,EAAE,IAAI,CAACA,IAAI;MACfF,8BAA8B,EAAE,IAAI,CAACA,8BAA8B;MACnEG,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,MAAM,GAAG;EACX,CAAC,QAAQ,CAAC,wBAAwB,kDAAkD,GAChF,mCAAmC;EACvC,CAAC,sBAAsB,CAAC,sCAAsC,sDAAsD,GAChH;AACR,CAAC;AACD,MAAMC,aAAa,GAAG,IAAI7C,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE4C,MAAM,CAAC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,2BAA2BA,CAACC,eAAe,EAAE;EAClD,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf;EACA;EACA;EACA,MAAMC,SAAS,GAAG;IACd;IACA;IACA;IACAC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAEC,mBAAmB;IAClC;IACAC,GAAG;IACH1C,eAAe,EAAEL,WAAW,CAACK,eAAe;IAC5C2C,WAAW,EAAEhD,WAAW,CAACgD,WAAW;IACpCC,KAAK,EAAEjD,WAAW,CAACiD,KAAK;IACxB;IACAP,IAAI,EAAE,IAAI;IACVQ,WAAW,EAAElD,WAAW,CAACkD,WAAW;IACpC7B,QAAQ,EAAE;MACN8B,iBAAiB,EAAEC,uBAAuB;MAC1C9B,SAAS;MACT+B,YAAY;MACZrD;IACJ;EACJ,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA2C,SAAS,CAAC,SAAS,CAAC,GAAGA,SAAS;EAChC;EACAW,MAAM,CAACC,cAAc,CAACZ,SAAS,EAAE,MAAM,EAAE;IACrCa,GAAG,EAAEC;EACT,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACI,SAASnC,SAASA,CAACR,IAAI,EAAE;IACrB,OAAO4B,IAAI,CAAC5B,IAAI,CAAC;EACrB;EACA;AACJ;AACA;EACI,SAASiC,GAAGA,CAACjC,IAAI,EAAE;IACfA,IAAI,GAAGA,IAAI,IAAId,WAAW,CAACG,mBAAmB;IAC9C,IAAI,CAACR,QAAQ,CAAC+C,IAAI,EAAE5B,IAAI,CAAC,EAAE;MACvB,MAAMyB,aAAa,CAACmB,MAAM,CAAC,QAAQ,CAAC,uBAAuB;QAAEC,OAAO,EAAE7C;MAAK,CAAC,CAAC;IACjF;IACA,OAAO4B,IAAI,CAAC5B,IAAI,CAAC;EACrB;EACA;EACAiC,GAAG,CAAC,KAAK,CAAC,GAAGN,eAAe;EAC5B;AACJ;AACA;AACA;AACA;EACI,SAASK,mBAAmBA,CAAC/B,OAAO,EAAE6C,SAAS,GAAG,CAAC,CAAC,EAAE;IAClD,MAAMb,GAAG,GAAG/C,WAAW,CAAC6C,aAAa,CAAC9B,OAAO,EAAE6C,SAAS,CAAC;IACzD,IAAIjE,QAAQ,CAAC+C,IAAI,EAAEK,GAAG,CAACjC,IAAI,CAAC,EAAE;MAC1B,OAAO4B,IAAI,CAACK,GAAG,CAACjC,IAAI,CAAC;IACzB;IACA,MAAM+C,SAAS,GAAG,IAAIpB,eAAe,CAACM,GAAG,EAAEJ,SAAS,CAAC;IACrDD,IAAI,CAACK,GAAG,CAACjC,IAAI,CAAC,GAAG+C,SAAS;IAC1B,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACI,SAASJ,OAAOA,CAAA,EAAG;IACf;IACA,OAAOH,MAAM,CAACQ,IAAI,CAACpB,IAAI,CAAC,CAACqB,GAAG,CAACjD,IAAI,IAAI4B,IAAI,CAAC5B,IAAI,CAAC,CAAC;EACpD;EACA,SAASsC,uBAAuBA,CAAChB,SAAS,EAAE;IACxC,MAAM4B,aAAa,GAAG5B,SAAS,CAACtB,IAAI;IACpC,MAAMmD,0BAA0B,GAAGD,aAAa,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACvE,IAAIlE,WAAW,CAACmE,kBAAkB,CAAC/B,SAAS,CAAC,IACzCA,SAAS,CAACgC,IAAI,KAAK,QAAQ,CAAC,4BAA4B;MACxD;MACA;MACA,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,GAAGvB,GAAG,CAAC,CAAC,KAAK;QACzC;QACA,IAAI,OAAOuB,MAAM,CAACL,0BAA0B,CAAC,KAAK,UAAU,EAAE;UAC1D;UACA;UACA,MAAM1B,aAAa,CAACmB,MAAM,CAAC,sBAAsB,CAAC,qCAAqC;YACnFC,OAAO,EAAEK;UACb,CAAC,CAAC;QACN;QACA;QACA;QACA,OAAOM,MAAM,CAACL,0BAA0B,CAAC,CAAC,CAAC;MAC/C,CAAC;MACD;MACA,IAAI7B,SAAS,CAACmC,YAAY,KAAKC,SAAS,EAAE;QACtC5E,UAAU,CAACyE,gBAAgB,EAAEjC,SAAS,CAACmC,YAAY,CAAC;MACxD;MACA;MACA5B,SAAS,CAACsB,0BAA0B,CAAC,GAAGI,gBAAgB;MACxD;MACA;MACA5B,eAAe,CAACgC,SAAS,CAACR,0BAA0B,CAAC;MACjD;MACA;MACA;MACA,UAAU,GAAGS,IAAI,EAAE;QACf,MAAMC,UAAU,GAAG,IAAI,CAACpD,WAAW,CAACqD,IAAI,CAAC,IAAI,EAAEZ,aAAa,CAAC;QAC7D,OAAOW,UAAU,CAACE,KAAK,CAAC,IAAI,EAAEzC,SAAS,CAAC0C,iBAAiB,GAAGJ,IAAI,GAAG,EAAE,CAAC;MAC1E,CAAC;IACT;IACA,OAAOtC,SAAS,CAACgC,IAAI,KAAK,QAAQ,CAAC;IAC7B;IACEzB,SAAS,CAACsB,0BAA0B,CAAC,GACvC,IAAI;EACd;EACA;EACA;EACA,SAASZ,YAAYA,CAACN,GAAG,EAAEjC,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAK,YAAY,EAAE;MACvB,OAAO,IAAI;IACf;IACA,MAAMiE,UAAU,GAAGjE,IAAI;IACvB,OAAOiE,UAAU;EACrB;EACA,OAAOpC,SAAS;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqC,uBAAuBA,CAAA,EAAG;EAC/B,MAAMrC,SAAS,GAAGH,2BAA2B,CAACjC,eAAe,CAAC;EAC9DoC,SAAS,CAACtB,QAAQ,GAAGiC,MAAM,CAAC2B,MAAM,CAAC3B,MAAM,CAAC2B,MAAM,CAAC,CAAC,CAAC,EAAEtC,SAAS,CAACtB,QAAQ,CAAC,EAAE;IAAE2D,uBAAuB;IAC/FE,eAAe;IACfrF,eAAe;IACfH,YAAY;IACZE;EAAW,CAAC,CAAC;EACjB;AACJ;AACA;AACA;AACA;EACI,SAASsF,eAAeA,CAACC,KAAK,EAAE;IAC5BvF,UAAU,CAAC+C,SAAS,EAAEwC,KAAK,CAAC;EAChC;EACA,OAAOxC,SAAS;AACpB;AACA,MAAMyC,UAAU,GAAGJ,uBAAuB,CAAC,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,MAAM,GAAG,IAAI/E,MAAM,CAAC,sBAAsB,CAAC;AAEjD,MAAMQ,IAAI,GAAG,sBAAsB;AACnC,MAAMwE,OAAO,GAAG,QAAQ;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EACrC;EACAnF,eAAe,CAACS,IAAI,EAAEwE,OAAO,EAAEE,OAAO,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI1F,SAAS,CAAC,CAAC,IAAI2F,IAAI,CAAC/E,QAAQ,KAAK8D,SAAS,EAAE;EAC5Ca,MAAM,CAACK,IAAI,CAAE;AACjB;AACA;AACA,GAAG,CAAC;EACA;EACA,MAAMC,UAAU,GAAGF,IAAI,CAAC/E,QAAQ,CAACwC,WAAW;EAC5C,IAAIyC,UAAU,IAAIA,UAAU,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;IAC/CP,MAAM,CAACK,IAAI,CAAE;AACrB;AACA;AACA,KAAK,CAAC;EACF;AACJ;AACA,MAAMhF,QAAQ,GAAG0E,UAAU;AAC3BG,sBAAsB,CAAC,CAAC;AAExB,SAAS7E,QAAQ,IAAImF,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
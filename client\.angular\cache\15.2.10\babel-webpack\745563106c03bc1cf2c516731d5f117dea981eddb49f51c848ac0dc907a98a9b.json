{"ast": null, "code": "const canPromise = require('./can-promise');\nconst QRCode = require('./core/qrcode');\nconst CanvasRenderer = require('./renderer/canvas');\nconst SvgRenderer = require('./renderer/svg-tag.js');\nfunction renderCanvas(renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1);\n  const argsNum = args.length;\n  const isLastArgCb = typeof args[argsNum - 1] === 'function';\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument');\n  }\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided');\n    }\n    if (argsNum === 2) {\n      cb = text;\n      text = canvas;\n      canvas = opts = undefined;\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts;\n        opts = undefined;\n      } else {\n        cb = opts;\n        opts = text;\n        text = canvas;\n        canvas = undefined;\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided');\n    }\n    if (argsNum === 1) {\n      text = canvas;\n      canvas = opts = undefined;\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text;\n      text = canvas;\n      canvas = undefined;\n    }\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts);\n        resolve(renderFunc(data, canvas, opts));\n      } catch (e) {\n        reject(e);\n      }\n    });\n  }\n  try {\n    const data = QRCode.create(text, opts);\n    cb(null, renderFunc(data, canvas, opts));\n  } catch (e) {\n    cb(e);\n  }\n}\nexports.create = QRCode.create;\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL);\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts);\n});", "map": {"version": 3, "names": ["canPromise", "require", "QRCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Svg<PERSON><PERSON><PERSON>", "renderCanvas", "renderFunc", "canvas", "text", "opts", "cb", "args", "slice", "call", "arguments", "argsNum", "length", "isLastArgCb", "Error", "undefined", "getContext", "Promise", "resolve", "reject", "data", "create", "e", "exports", "to<PERSON><PERSON><PERSON>", "bind", "render", "toDataURL", "renderToDataURL", "toString", "_"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/browser.js"], "sourcesContent": ["\nconst canPromise = require('./can-promise')\n\nconst QRCode = require('./core/qrcode')\nconst CanvasRenderer = require('./renderer/canvas')\nconst SvgRenderer = require('./renderer/svg-tag.js')\n\nfunction renderCanvas (renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1)\n  const argsNum = args.length\n  const isLastArgCb = typeof args[argsNum - 1] === 'function'\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument')\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 2) {\n      cb = text\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts\n        opts = undefined\n      } else {\n        cb = opts\n        opts = text\n        text = canvas\n        canvas = undefined\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 1) {\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text\n      text = canvas\n      canvas = undefined\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts)\n        resolve(renderFunc(data, canvas, opts))\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n\n  try {\n    const data = QRCode.create(text, opts)\n    cb(null, renderFunc(data, canvas, opts))\n  } catch (e) {\n    cb(e)\n  }\n}\n\nexports.create = QRCode.create\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render)\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL)\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts)\n})\n"], "mappings": "AACA,MAAMA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE3C,MAAMC,MAAM,GAAGD,OAAO,CAAC,eAAe,CAAC;AACvC,MAAME,cAAc,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AACnD,MAAMG,WAAW,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAEpD,SAASI,YAAYA,CAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACzD,MAAMC,IAAI,GAAG,EAAE,CAACC,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC;EACxC,MAAMC,OAAO,GAAGJ,IAAI,CAACK,MAAM;EAC3B,MAAMC,WAAW,GAAG,OAAON,IAAI,CAACI,OAAO,GAAG,CAAC,CAAC,KAAK,UAAU;EAE3D,IAAI,CAACE,WAAW,IAAI,CAACjB,UAAU,CAAC,CAAC,EAAE;IACjC,MAAM,IAAIkB,KAAK,CAAC,oCAAoC,CAAC;EACvD;EAEA,IAAID,WAAW,EAAE;IACf,IAAIF,OAAO,GAAG,CAAC,EAAE;MACf,MAAM,IAAIG,KAAK,CAAC,4BAA4B,CAAC;IAC/C;IAEA,IAAIH,OAAO,KAAK,CAAC,EAAE;MACjBL,EAAE,GAAGF,IAAI;MACTA,IAAI,GAAGD,MAAM;MACbA,MAAM,GAAGE,IAAI,GAAGU,SAAS;IAC3B,CAAC,MAAM,IAAIJ,OAAO,KAAK,CAAC,EAAE;MACxB,IAAIR,MAAM,CAACa,UAAU,IAAI,OAAOV,EAAE,KAAK,WAAW,EAAE;QAClDA,EAAE,GAAGD,IAAI;QACTA,IAAI,GAAGU,SAAS;MAClB,CAAC,MAAM;QACLT,EAAE,GAAGD,IAAI;QACTA,IAAI,GAAGD,IAAI;QACXA,IAAI,GAAGD,MAAM;QACbA,MAAM,GAAGY,SAAS;MACpB;IACF;EACF,CAAC,MAAM;IACL,IAAIJ,OAAO,GAAG,CAAC,EAAE;MACf,MAAM,IAAIG,KAAK,CAAC,4BAA4B,CAAC;IAC/C;IAEA,IAAIH,OAAO,KAAK,CAAC,EAAE;MACjBP,IAAI,GAAGD,MAAM;MACbA,MAAM,GAAGE,IAAI,GAAGU,SAAS;IAC3B,CAAC,MAAM,IAAIJ,OAAO,KAAK,CAAC,IAAI,CAACR,MAAM,CAACa,UAAU,EAAE;MAC9CX,IAAI,GAAGD,IAAI;MACXA,IAAI,GAAGD,MAAM;MACbA,MAAM,GAAGY,SAAS;IACpB;IAEA,OAAO,IAAIE,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MAC5C,IAAI;QACF,MAAMC,IAAI,GAAGtB,MAAM,CAACuB,MAAM,CAACjB,IAAI,EAAEC,IAAI,CAAC;QACtCa,OAAO,CAAChB,UAAU,CAACkB,IAAI,EAAEjB,MAAM,EAAEE,IAAI,CAAC,CAAC;MACzC,CAAC,CAAC,OAAOiB,CAAC,EAAE;QACVH,MAAM,CAACG,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ;EAEA,IAAI;IACF,MAAMF,IAAI,GAAGtB,MAAM,CAACuB,MAAM,CAACjB,IAAI,EAAEC,IAAI,CAAC;IACtCC,EAAE,CAAC,IAAI,EAAEJ,UAAU,CAACkB,IAAI,EAAEjB,MAAM,EAAEE,IAAI,CAAC,CAAC;EAC1C,CAAC,CAAC,OAAOiB,CAAC,EAAE;IACVhB,EAAE,CAACgB,CAAC,CAAC;EACP;AACF;AAEAC,OAAO,CAACF,MAAM,GAAGvB,MAAM,CAACuB,MAAM;AAC9BE,OAAO,CAACC,QAAQ,GAAGvB,YAAY,CAACwB,IAAI,CAAC,IAAI,EAAE1B,cAAc,CAAC2B,MAAM,CAAC;AACjEH,OAAO,CAACI,SAAS,GAAG1B,YAAY,CAACwB,IAAI,CAAC,IAAI,EAAE1B,cAAc,CAAC6B,eAAe,CAAC;;AAE3E;AACAL,OAAO,CAACM,QAAQ,GAAG5B,YAAY,CAACwB,IAAI,CAAC,IAAI,EAAE,UAAUL,IAAI,EAAEU,CAAC,EAAEzB,IAAI,EAAE;EAClE,OAAOL,WAAW,CAAC0B,MAAM,CAACN,IAAI,EAAEf,IAAI,CAAC;AACvC,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
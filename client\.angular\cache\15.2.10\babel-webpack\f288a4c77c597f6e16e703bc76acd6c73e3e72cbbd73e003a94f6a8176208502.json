{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"app/services/team.service\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"app/services/auth.service\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"app/layout/components/content-header/content-header.component\";\nimport * as i10 from \"./assign-coaches/assign-coaches.component\";\nimport * as i11 from \"./assign-players/assign-players.component\";\nfunction TeamAssignmentComponent_li_9_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-assign-players\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"team\", ctx_r3.team);\n  }\n}\nfunction TeamAssignmentComponent_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 11)(1, \"a\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TeamAssignmentComponent_li_9_ng_template_4_Template, 1, 1, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"Players\"));\n  }\n}\nfunction TeamAssignmentComponent_li_10_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-assign-coaches\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"team\", ctx_r4.team);\n  }\n}\nfunction TeamAssignmentComponent_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 11)(1, \"a\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TeamAssignmentComponent_li_10_ng_template_4_Template, 1, 1, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"Coaches\"));\n  }\n}\nexport class TeamAssignmentComponent {\n  constructor(_trans, _route, _router, _teamService, _loadingService, _authService, _titleService) {\n    this._trans = _trans;\n    this._route = _route;\n    this._router = _router;\n    this._teamService = _teamService;\n    this._loadingService = _loadingService;\n    this._authService = _authService;\n    this._titleService = _titleService;\n    this.team = null;\n    this.group_stages = [];\n    this.allowEditTeam = true;\n    this.permissions = {};\n    this.isManagerofTeam = false;\n    this.teamId = this._route.snapshot.paramMap.get('teamId');\n    console.log('teamId', this.teamId);\n    this.currentUser = this._authService.currentUserValue;\n    this.permissions.assign_player = this.currentUser.role.permissions.find(x => x.id == AppConfig.PERMISSIONS.assign_players);\n    this.permissions.assign_coach = this.currentUser.role.permissions.find(x => x.id == AppConfig.PERMISSIONS.assign_coaches);\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getTeam();\n      let res = yield _this._teamService.isClubManagerOfTeam(_this.teamId);\n      _this.isManagerofTeam = res.data;\n    })();\n  }\n  setContentHeader() {\n    this._titleService.setTitle(`${this.team?.name} [${this.team?.group.name}]`);\n    this.contentHeader = {\n      headerTitle: `${this.team?.name} [${this.team?.group.name}]`,\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Teams'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Teams'),\n          isLink: true,\n          link: '/teams/team-management'\n        }, {\n          name: `${this.team?.name} [${this.team?.group.name}]`,\n          isLink: false\n        }]\n      }\n    };\n  }\n  getTeam() {\n    this._teamService.getTeamById(this.teamId).subscribe(res => {\n      if (res) {\n        this.team = res;\n        this.setContentHeader();\n      }\n    });\n  }\n  static #_ = this.ɵfac = function TeamAssignmentComponent_Factory(t) {\n    return new (t || TeamAssignmentComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TeamService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamAssignmentComponent,\n    selectors: [[\"app-team-assignment\"]],\n    decls: 12,\n    vars: 4,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"team-assignment-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\", 4, \"ngIf\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [\"ngbNavItem\", \"\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [3, \"team\"], [\"href\", \"javascript:void(0)\", \"ngbNavLink\", \"\"]],\n    template: function TeamAssignmentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8);\n        i0.ɵɵtemplate(9, TeamAssignmentComponent_li_9_Template, 5, 3, \"li\", 9);\n        i0.ɵɵtemplate(10, TeamAssignmentComponent_li_10_Template, 5, 3, \"li\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(11, \"div\", 10);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.permissions.assign_player);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.permissions.assign_coach && ctx.isManagerofTeam);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n      }\n    },\n    dependencies: [i7.NgbNavContent, i7.NgbNav, i7.NgbNavItem, i7.NgbNavLink, i7.NgbNavOutlet, i8.NgIf, i9.ContentHeaderComponent, i10.TeamCoachesComponent, i11.TeamPlayersComponent, i1.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAOA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICKNC,EAAA,CAAAC,SAAA,6BAAuD;;;;IAAnCD,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa;;;;;IAHzCJ,EAAA,CAAAK,cAAA,aAAiD;IAC/BL,EAAA,CAAAM,MAAA,GAA2B;;IAAAN,EAAA,CAAAO,YAAA,EAAI;IAC7CP,EAAA,CAAAQ,UAAA,IAAAC,mDAAA,0BAEc;IAClBT,EAAA,CAAAO,YAAA,EAAK;;;IAJaP,EAAA,CAAAU,SAAA,GAA2B;IAA3BV,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAY,WAAA,kBAA2B;;;;;IAQrCZ,EAAA,CAAAC,SAAA,6BAAuD;;;;IAAnCD,EAAA,CAAAE,UAAA,SAAAW,MAAA,CAAAT,IAAA,CAAa;;;;;IAHzCJ,EAAA,CAAAK,cAAA,aAAmE;IACvBL,EAAA,CAAAM,MAAA,GAA2B;;IAAAN,EAAA,CAAAO,YAAA,EAAI;IACvEP,EAAA,CAAAQ,UAAA,IAAAM,oDAAA,0BAEc;IAClBd,EAAA,CAAAO,YAAA,EAAK;;;IAJuCP,EAAA,CAAAU,SAAA,GAA2B;IAA3BV,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAY,WAAA,kBAA2B;;;ADAnG,OAAM,MAAOG,uBAAuB;EAWlCC,YACSC,MAAwB,EACxBC,MAAsB,EACtBC,OAAe,EACfC,YAAyB,EACzBC,eAA+B,EAC/BC,YAAyB,EACzBC,aAAoB;IANpB,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IAdtB,KAAAnB,IAAI,GAAQ,IAAI;IAChB,KAAAoB,YAAY,GAAQ,EAAE;IAEtB,KAAAC,aAAa,GAAG,IAAI;IAEpB,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAC,eAAe,GAAG,KAAK;IAUrB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACV,MAAM,CAACW,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,QAAQ,CAAC;IACzDC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACL,MAAM,CAAC;IAClC,IAAI,CAACM,WAAW,GAAG,IAAI,CAACZ,YAAY,CAACa,gBAAgB;IAErD,IAAI,CAACT,WAAW,CAACU,aAAa,GAAG,IAAI,CAACF,WAAW,CAACG,IAAI,CAACX,WAAW,CAACY,IAAI,CACpEC,CAAC,IAAKA,CAAC,CAACC,EAAE,IAAIzC,SAAS,CAAC0C,WAAW,CAACC,cAAc,CACpD;IACD,IAAI,CAAChB,WAAW,CAACiB,YAAY,GAAG,IAAI,CAACT,WAAW,CAACG,IAAI,CAACX,WAAW,CAACY,IAAI,CACnEC,CAAC,IAAKA,CAAC,CAACC,EAAE,IAAIzC,SAAS,CAAC0C,WAAW,CAACG,cAAc,CACpD;EACH;EAEMC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACE,OAAO,EAAE;MACd,IAAIC,GAAG,SAASH,KAAI,CAAC1B,YAAY,CAAC8B,mBAAmB,CAACJ,KAAI,CAAClB,MAAM,CAAC;MAClEkB,KAAI,CAACnB,eAAe,GAAGsB,GAAG,CAACE,IAAI;IAAC;EAClC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC7B,aAAa,CAAC8B,QAAQ,CACzB,GAAG,IAAI,CAACjD,IAAI,EAAEkD,IAAI,KAAK,IAAI,CAAClD,IAAI,EAAEmD,KAAK,CAACD,IAAI,GAAG,CAChD;IACD,IAAI,CAACE,aAAa,GAAG;MACnBC,WAAW,EAAE,GAAG,IAAI,CAACrD,IAAI,EAAEkD,IAAI,KAAK,IAAI,CAAClD,IAAI,EAAEmD,KAAK,CAACD,IAAI,GAAG;MAC5DI,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEP,IAAI,EAAE,IAAI,CAACrC,MAAM,CAAC6C,OAAO,CAAC,OAAO,CAAC;UAClCC,MAAM,EAAE;SACT,EACD;UACET,IAAI,EAAE,IAAI,CAACrC,MAAM,CAAC6C,OAAO,CAAC,OAAO,CAAC;UAClCC,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE;SACP,EACD;UACEV,IAAI,EAAE,GAAG,IAAI,CAAClD,IAAI,EAAEkD,IAAI,KAAK,IAAI,CAAClD,IAAI,EAAEmD,KAAK,CAACD,IAAI,GAAG;UACrDS,MAAM,EAAE;SACT;;KAGN;EACH;EAEAf,OAAOA,CAAA;IACL,IAAI,CAAC5B,YAAY,CAAC6C,WAAW,CAAC,IAAI,CAACrC,MAAM,CAAC,CAACsC,SAAS,CAAEjB,GAAQ,IAAI;MAChE,IAAIA,GAAG,EAAE;QACP,IAAI,CAAC7C,IAAI,GAAG6C,GAAG;QACf,IAAI,CAACG,gBAAgB,EAAE;;IAE3B,CAAC,CAAC;EACJ;EAAC,QAAAe,CAAA;qBAzEUpD,uBAAuB,EAAAf,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAzE,EAAA,CAAAoE,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA3E,EAAA,CAAAoE,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA7E,EAAA,CAAAoE,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAAoE,iBAAA,CAAAY,EAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAvBnE,uBAAuB;IAAAoE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBpCzF,EAAA,CAAAK,cAAA,aAA+C;QAEvCL,EAAA,CAAAC,SAAA,4BAAyE;QAEzED,EAAA,CAAAK,cAAA,iBAAmC;QAKfL,EAAA,CAAAQ,UAAA,IAAAmF,qCAAA,gBAKK;QACL3F,EAAA,CAAAQ,UAAA,KAAAoF,sCAAA,gBAKK;QACT5F,EAAA,CAAAO,YAAA,EAAK;QAETP,EAAA,CAAAC,SAAA,eAA6C;QACjDD,EAAA,CAAAO,YAAA,EAAM;;;;QAtBMP,EAAA,CAAAU,SAAA,GAA+B;QAA/BV,EAAA,CAAAE,UAAA,kBAAAwF,GAAA,CAAAlC,aAAA,CAA+B;QAOfxD,EAAA,CAAAU,SAAA,GAA+B;QAA/BV,EAAA,CAAAE,UAAA,SAAAwF,GAAA,CAAAhE,WAAA,CAAAU,aAAA,CAA+B;QAM/BpC,EAAA,CAAAU,SAAA,GAAiD;QAAjDV,EAAA,CAAAE,UAAA,SAAAwF,GAAA,CAAAhE,WAAA,CAAAiB,YAAA,IAAA+C,GAAA,CAAA/D,eAAA,CAAiD;QAQpE3B,EAAA,CAAAU,SAAA,GAAoB;QAApBV,EAAA,CAAAE,UAAA,iBAAA2F,GAAA,CAAoB", "names": ["AppConfig", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r3", "team", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "TeamAssignmentComponent_li_9_ng_template_4_Template", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ctx_r4", "TeamAssignmentComponent_li_10_ng_template_4_Template", "TeamAssignmentComponent", "constructor", "_trans", "_route", "_router", "_teamService", "_loadingService", "_authService", "_titleService", "group_stages", "allowEditTeam", "permissions", "isManagerofTeam", "teamId", "snapshot", "paramMap", "get", "console", "log", "currentUser", "currentUserValue", "assign_player", "role", "find", "x", "id", "PERMISSIONS", "assign_players", "assign_coach", "assign_coaches", "ngOnInit", "_this", "_asyncToGenerator", "getTeam", "res", "isClubManagerOfTeam", "data", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setTitle", "name", "group", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "instant", "isLink", "link", "getTeamById", "subscribe", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "ActivatedRoute", "Router", "i3", "TeamService", "i4", "LoadingService", "i5", "AuthService", "i6", "Title", "_2", "selectors", "decls", "vars", "consts", "template", "TeamAssignmentComponent_Template", "rf", "ctx", "TeamAssignmentComponent_li_9_Template", "TeamAssignmentComponent_li_10_Template", "_r0"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\team-assignment.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\team-assignment.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { User } from 'app/interfaces/user';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-team-assignment',\r\n  templateUrl: './team-assignment.component.html',\r\n  styleUrls: ['./team-assignment.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class TeamAssignmentComponent implements OnInit {\r\n  contentHeader: object;\r\n  teamId: any;\r\n  teamName: any;\r\n  team: any = null;\r\n  group_stages: any = [];\r\n  tableData: any;\r\n  allowEditTeam = true;\r\n  currentUser: User;\r\n  permissions: any = {};\r\n  isManagerofTeam = false;\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _teamService: TeamService,\r\n    public _loadingService: LoadingService,\r\n    public _authService: AuthService,\r\n    public _titleService: Title\r\n  ) {\r\n    this.teamId = this._route.snapshot.paramMap.get('teamId');\r\n    console.log('teamId', this.teamId);\r\n    this.currentUser = this._authService.currentUserValue;\r\n\r\n    this.permissions.assign_player = this.currentUser.role.permissions.find(\r\n      (x) => x.id == AppConfig.PERMISSIONS.assign_players\r\n    );\r\n    this.permissions.assign_coach = this.currentUser.role.permissions.find(\r\n      (x) => x.id == AppConfig.PERMISSIONS.assign_coaches\r\n    );  \r\n  }\r\n\r\n  async ngOnInit(): Promise<void> {\r\n    this.getTeam();\r\n    let res = await this._teamService.isClubManagerOfTeam(this.teamId);\r\n    this.isManagerofTeam = res.data;\r\n  }\r\n\r\n  setContentHeader() {\r\n    this._titleService.setTitle(\r\n      `${this.team?.name} [${this.team?.group.name}]`\r\n    );\r\n    this.contentHeader = {\r\n      headerTitle: `${this.team?.name} [${this.team?.group.name}]`,\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Teams'),\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: this._trans.instant('Teams'),\r\n            isLink: true,\r\n            link: '/teams/team-management',\r\n          },\r\n          {\r\n            name: `${this.team?.name} [${this.team?.group.name}]`,\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  getTeam() {\r\n    this._teamService.getTeamById(this.teamId).subscribe((res: any) => {\r\n      if (res) {\r\n        this.team = res;\r\n        this.setContentHeader();\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <div class=\"content-body\">\r\n        <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n        <section id=\"team-assignment-page\">\r\n            <div class=\"row\">\r\n                <div class=\"col-12\">\r\n                    <div class=\"card\">\r\n                        <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n                            <li ngbNavItem *ngIf=\"permissions.assign_player\">\r\n                                <a ngbNavLink>{{ 'Players' | translate }}</a>\r\n                                <ng-template ngbNavContent>\r\n                                    <app-assign-players [team]=\"team\"></app-assign-players>\r\n                                </ng-template>\r\n                            </li>\r\n                            <li ngbNavItem *ngIf=\"permissions.assign_coach && isManagerofTeam\">\r\n                                <a href=\"javascript:void(0)\" ngbNavLink>{{ 'Coaches' | translate }}</a>\r\n                                <ng-template ngbNavContent>\r\n                                    <app-assign-coaches [team]=\"team\"></app-assign-coaches>\r\n                                </ng-template>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                    <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
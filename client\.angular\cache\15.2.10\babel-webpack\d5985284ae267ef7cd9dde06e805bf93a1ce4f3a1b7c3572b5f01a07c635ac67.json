{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { CoreCommonModule } from '@core/common.module';\nimport { BreadcrumbModule } from 'app/layout/components/content-header/breadcrumb/breadcrumb.module';\nimport { ContentHeaderComponent } from 'app/layout/components/content-header/content-header.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nexport class ContentHeaderModule {\n  static #_ = this.ɵfac = function ContentHeaderModule_Factory(t) {\n    return new (t || ContentHeaderModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ContentHeaderModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, CoreCommonModule, BreadcrumbModule, NgbModule, TranslateModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContentHeaderModule, {\n    declarations: [ContentHeaderComponent],\n    imports: [CommonModule, RouterModule, CoreCommonModule, BreadcrumbModule, NgbModule, TranslateModule],\n    exports: [ContentHeaderComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,SAAS,QAAQ,4BAA4B;AAEtD,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,SAASC,gBAAgB,QAAQ,mEAAmE;AACpG,SAASC,sBAAsB,QAAQ,+DAA+D;AACtG,SAASC,eAAe,QAAQ,qBAAqB;;AAOrD,OAAM,MAAOC,mBAAmB;EAAA,QAAAC,CAAA;qBAAnBD,mBAAmB;EAAA;EAAA,QAAAE,EAAA;UAAnBF;EAAmB;EAAA,QAAAG,EAAA;cAHpBV,YAAY,EAAEC,YAAY,EAAEE,gBAAgB,EAAEC,gBAAgB,EAAEF,SAAS,EAAEI,eAAe;EAAA;;;2EAGzFC,mBAAmB;IAAAI,YAAA,GAJfN,sBAAsB;IAAAO,OAAA,GAC3BZ,YAAY,EAAEC,YAAY,EAAEE,gBAAgB,EAAEC,gBAAgB,EAAEF,SAAS,EAAEI,eAAe;IAAAO,OAAA,GAC1FR,sBAAsB;EAAA;AAAA", "names": ["CommonModule", "RouterModule", "NgbModule", "CoreCommonModule", "BreadcrumbModule", "ContentHeaderComponent", "TranslateModule", "ContentHeaderModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content-header\\content-header.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\n\r\nimport { CoreCommonModule } from '@core/common.module';\r\n\r\nimport { BreadcrumbModule } from 'app/layout/components/content-header/breadcrumb/breadcrumb.module';\r\nimport { ContentHeaderComponent } from 'app/layout/components/content-header/content-header.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\n\r\n@NgModule({\r\n  declarations: [ContentHeaderComponent],\r\n  imports: [CommonModule, RouterModule, CoreCommonModule, BreadcrumbModule, NgbModule, TranslateModule],\r\n  exports: [ContentHeaderComponent]\r\n})\r\nexport class ContentHeaderModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
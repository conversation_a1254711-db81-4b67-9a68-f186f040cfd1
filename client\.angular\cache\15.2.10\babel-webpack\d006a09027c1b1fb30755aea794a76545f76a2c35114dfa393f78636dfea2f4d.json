{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { empty } from '../observable/empty';\nexport function repeat(count = -1) {\n  return source => {\n    if (count === 0) {\n      return empty();\n    } else if (count < 0) {\n      return source.lift(new RepeatOperator(-1, source));\n    } else {\n      return source.lift(new RepeatOperator(count - 1, source));\n    }\n  };\n}\nclass RepeatOperator {\n  constructor(count, source) {\n    this.count = count;\n    this.source = source;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new RepeatSubscriber(subscriber, this.count, this.source));\n  }\n}\nclass RepeatSubscriber extends Subscriber {\n  constructor(destination, count, source) {\n    super(destination);\n    this.count = count;\n    this.source = source;\n  }\n  complete() {\n    if (!this.isStopped) {\n      const {\n        source,\n        count\n      } = this;\n      if (count === 0) {\n        return super.complete();\n      } else if (count > -1) {\n        this.count = count - 1;\n      }\n      source.subscribe(this._unsubscribeAndRecycle());\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "empty", "repeat", "count", "source", "lift", "RepeatOperator", "constructor", "call", "subscriber", "subscribe", "RepeatSubscriber", "destination", "complete", "isStopped", "_unsubscribeAndRecycle"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/repeat.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { empty } from '../observable/empty';\nexport function repeat(count = -1) {\n    return (source) => {\n        if (count === 0) {\n            return empty();\n        }\n        else if (count < 0) {\n            return source.lift(new RepeatOperator(-1, source));\n        }\n        else {\n            return source.lift(new RepeatOperator(count - 1, source));\n        }\n    };\n}\nclass RepeatOperator {\n    constructor(count, source) {\n        this.count = count;\n        this.source = source;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new RepeatSubscriber(subscriber, this.count, this.source));\n    }\n}\nclass RepeatSubscriber extends Subscriber {\n    constructor(destination, count, source) {\n        super(destination);\n        this.count = count;\n        this.source = source;\n    }\n    complete() {\n        if (!this.isStopped) {\n            const { source, count } = this;\n            if (count === 0) {\n                return super.complete();\n            }\n            else if (count > -1) {\n                this.count = count - 1;\n            }\n            source.subscribe(this._unsubscribeAndRecycle());\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,MAAMA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE;EAC/B,OAAQC,MAAM,IAAK;IACf,IAAID,KAAK,KAAK,CAAC,EAAE;MACb,OAAOF,KAAK,CAAC,CAAC;IAClB,CAAC,MACI,IAAIE,KAAK,GAAG,CAAC,EAAE;MAChB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAIC,cAAc,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC;IACtD,CAAC,MACI;MACD,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,cAAc,CAACH,KAAK,GAAG,CAAC,EAAEC,MAAM,CAAC,CAAC;IAC7D;EACJ,CAAC;AACL;AACA,MAAME,cAAc,CAAC;EACjBC,WAAWA,CAACJ,KAAK,EAAEC,MAAM,EAAE;IACvB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAI,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,gBAAgB,CAACF,UAAU,EAAE,IAAI,CAACN,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC;EACtF;AACJ;AACA,MAAMO,gBAAgB,SAASX,UAAU,CAAC;EACtCO,WAAWA,CAACK,WAAW,EAAET,KAAK,EAAEC,MAAM,EAAE;IACpC,KAAK,CAACQ,WAAW,CAAC;IAClB,IAAI,CAACT,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAS,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,MAAM;QAAEV,MAAM;QAAED;MAAM,CAAC,GAAG,IAAI;MAC9B,IAAIA,KAAK,KAAK,CAAC,EAAE;QACb,OAAO,KAAK,CAACU,QAAQ,CAAC,CAAC;MAC3B,CAAC,MACI,IAAIV,KAAK,GAAG,CAAC,CAAC,EAAE;QACjB,IAAI,CAACA,KAAK,GAAGA,KAAK,GAAG,CAAC;MAC1B;MACAC,MAAM,CAACM,SAAS,CAAC,IAAI,CAACK,sBAAsB,CAAC,CAAC,CAAC;IACnD;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
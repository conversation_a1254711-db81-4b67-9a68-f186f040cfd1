{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements taken from the end.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to take.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.takeRight([1, 2, 3]);\n * // => [3]\n *\n * _.takeRight([1, 2, 3], 2);\n * // => [2, 3]\n *\n * _.takeRight([1, 2, 3], 5);\n * // => [1, 2, 3]\n *\n * _.takeRight([1, 2, 3], 0);\n * // => []\n */\nfunction takeRight(array, n, guard) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  n = guard || n === undefined ? 1 : toInteger(n);\n  n = length - n;\n  return baseSlice(array, n < 0 ? 0 : n, length);\n}\nexport default takeRight;", "map": {"version": 3, "names": ["baseSlice", "toInteger", "takeRight", "array", "n", "guard", "length", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/takeRight.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements taken from the end.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to take.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.takeRight([1, 2, 3]);\n * // => [3]\n *\n * _.takeRight([1, 2, 3], 2);\n * // => [2, 3]\n *\n * _.takeRight([1, 2, 3], 5);\n * // => [1, 2, 3]\n *\n * _.takeRight([1, 2, 3], 0);\n * // => []\n */\nfunction takeRight(array, n, guard) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  n = (guard || n === undefined) ? 1 : toInteger(n);\n  n = length - n;\n  return baseSlice(array, n < 0 ? 0 : n, length);\n}\n\nexport default takeRight;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EACAF,CAAC,GAAIC,KAAK,IAAID,CAAC,KAAKG,SAAS,GAAI,CAAC,GAAGN,SAAS,CAACG,CAAC,CAAC;EACjDA,CAAC,GAAGE,MAAM,GAAGF,CAAC;EACd,OAAOJ,SAAS,CAACG,KAAK,EAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAEE,MAAM,CAAC;AAChD;AAEA,eAAeJ,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
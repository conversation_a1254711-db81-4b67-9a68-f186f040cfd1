{"ast": null, "code": "import metaMap from './_metaMap.js';\nimport noop from './noop.js';\n\n/**\n * Gets metadata for `func`.\n *\n * @private\n * @param {Function} func The function to query.\n * @returns {*} Returns the metadata for `func`.\n */\nvar getData = !metaMap ? noop : function (func) {\n  return metaMap.get(func);\n};\nexport default getData;", "map": {"version": 3, "names": ["metaMap", "noop", "getData", "func", "get"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_getData.js"], "sourcesContent": ["import metaMap from './_metaMap.js';\nimport noop from './noop.js';\n\n/**\n * Gets metadata for `func`.\n *\n * @private\n * @param {Function} func The function to query.\n * @returns {*} Returns the metadata for `func`.\n */\nvar getData = !metaMap ? noop : function(func) {\n  return metaMap.get(func);\n};\n\nexport default getData;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,CAACF,OAAO,GAAGC,IAAI,GAAG,UAASE,IAAI,EAAE;EAC7C,OAAOH,OAAO,CAACI,GAAG,CAACD,IAAI,CAAC;AAC1B,CAAC;AAED,eAAeD,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
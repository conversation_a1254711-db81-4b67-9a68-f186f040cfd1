{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isScheduler } from '../util/isScheduler';\nexport function bufferTime(bufferTimeSpan) {\n  let length = arguments.length;\n  let scheduler = async;\n  if (isScheduler(arguments[arguments.length - 1])) {\n    scheduler = arguments[arguments.length - 1];\n    length--;\n  }\n  let bufferCreationInterval = null;\n  if (length >= 2) {\n    bufferCreationInterval = arguments[1];\n  }\n  let maxBufferSize = Number.POSITIVE_INFINITY;\n  if (length >= 3) {\n    maxBufferSize = arguments[2];\n  }\n  return function bufferTimeOperatorFunction(source) {\n    return source.lift(new BufferTimeOperator(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler));\n  };\n}\nclass BufferTimeOperator {\n  constructor(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n    this.bufferTimeSpan = bufferTimeSpan;\n    this.bufferCreationInterval = bufferCreationInterval;\n    this.maxBufferSize = maxBufferSize;\n    this.scheduler = scheduler;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new BufferTimeSubscriber(subscriber, this.bufferTimeSpan, this.bufferCreationInterval, this.maxBufferSize, this.scheduler));\n  }\n}\nclass Context {\n  constructor() {\n    this.buffer = [];\n  }\n}\nclass BufferTimeSubscriber extends Subscriber {\n  constructor(destination, bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n    super(destination);\n    this.bufferTimeSpan = bufferTimeSpan;\n    this.bufferCreationInterval = bufferCreationInterval;\n    this.maxBufferSize = maxBufferSize;\n    this.scheduler = scheduler;\n    this.contexts = [];\n    const context = this.openContext();\n    this.timespanOnly = bufferCreationInterval == null || bufferCreationInterval < 0;\n    if (this.timespanOnly) {\n      const timeSpanOnlyState = {\n        subscriber: this,\n        context,\n        bufferTimeSpan\n      };\n      this.add(context.closeAction = scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n    } else {\n      const closeState = {\n        subscriber: this,\n        context\n      };\n      const creationState = {\n        bufferTimeSpan,\n        bufferCreationInterval,\n        subscriber: this,\n        scheduler\n      };\n      this.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, closeState));\n      this.add(scheduler.schedule(dispatchBufferCreation, bufferCreationInterval, creationState));\n    }\n  }\n  _next(value) {\n    const contexts = this.contexts;\n    const len = contexts.length;\n    let filledBufferContext;\n    for (let i = 0; i < len; i++) {\n      const context = contexts[i];\n      const buffer = context.buffer;\n      buffer.push(value);\n      if (buffer.length == this.maxBufferSize) {\n        filledBufferContext = context;\n      }\n    }\n    if (filledBufferContext) {\n      this.onBufferFull(filledBufferContext);\n    }\n  }\n  _error(err) {\n    this.contexts.length = 0;\n    super._error(err);\n  }\n  _complete() {\n    const {\n      contexts,\n      destination\n    } = this;\n    while (contexts.length > 0) {\n      const context = contexts.shift();\n      destination.next(context.buffer);\n    }\n    super._complete();\n  }\n  _unsubscribe() {\n    this.contexts = null;\n  }\n  onBufferFull(context) {\n    this.closeContext(context);\n    const closeAction = context.closeAction;\n    closeAction.unsubscribe();\n    this.remove(closeAction);\n    if (!this.closed && this.timespanOnly) {\n      context = this.openContext();\n      const bufferTimeSpan = this.bufferTimeSpan;\n      const timeSpanOnlyState = {\n        subscriber: this,\n        context,\n        bufferTimeSpan\n      };\n      this.add(context.closeAction = this.scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n    }\n  }\n  openContext() {\n    const context = new Context();\n    this.contexts.push(context);\n    return context;\n  }\n  closeContext(context) {\n    this.destination.next(context.buffer);\n    const contexts = this.contexts;\n    const spliceIndex = contexts ? contexts.indexOf(context) : -1;\n    if (spliceIndex >= 0) {\n      contexts.splice(contexts.indexOf(context), 1);\n    }\n  }\n}\nfunction dispatchBufferTimeSpanOnly(state) {\n  const subscriber = state.subscriber;\n  const prevContext = state.context;\n  if (prevContext) {\n    subscriber.closeContext(prevContext);\n  }\n  if (!subscriber.closed) {\n    state.context = subscriber.openContext();\n    state.context.closeAction = this.schedule(state, state.bufferTimeSpan);\n  }\n}\nfunction dispatchBufferCreation(state) {\n  const {\n    bufferCreationInterval,\n    bufferTimeSpan,\n    subscriber,\n    scheduler\n  } = state;\n  const context = subscriber.openContext();\n  const action = this;\n  if (!subscriber.closed) {\n    subscriber.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, {\n      subscriber,\n      context\n    }));\n    action.schedule(state, bufferCreationInterval);\n  }\n}\nfunction dispatchBufferClose(arg) {\n  const {\n    subscriber,\n    context\n  } = arg;\n  subscriber.closeContext(context);\n}", "map": {"version": 3, "names": ["async", "Subscriber", "isScheduler", "bufferTime", "bufferTimeSpan", "length", "arguments", "scheduler", "bufferCreationInterval", "maxBufferSize", "Number", "POSITIVE_INFINITY", "bufferTimeOperatorFunction", "source", "lift", "BufferTimeOperator", "constructor", "call", "subscriber", "subscribe", "BufferTimeSubscriber", "Context", "buffer", "destination", "contexts", "context", "openContext", "timespanOnly", "timeSpanOnlyState", "add", "closeAction", "schedule", "dispatchBufferTimeSpanOnly", "closeState", "creationState", "dispatchBufferClose", "dispatchBufferCreation", "_next", "value", "len", "filledBufferContext", "i", "push", "onBufferFull", "_error", "err", "_complete", "shift", "next", "_unsubscribe", "closeContext", "unsubscribe", "remove", "closed", "spliceIndex", "indexOf", "splice", "state", "prevContext", "action", "arg"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/bufferTime.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isScheduler } from '../util/isScheduler';\nexport function bufferTime(bufferTimeSpan) {\n    let length = arguments.length;\n    let scheduler = async;\n    if (isScheduler(arguments[arguments.length - 1])) {\n        scheduler = arguments[arguments.length - 1];\n        length--;\n    }\n    let bufferCreationInterval = null;\n    if (length >= 2) {\n        bufferCreationInterval = arguments[1];\n    }\n    let maxBufferSize = Number.POSITIVE_INFINITY;\n    if (length >= 3) {\n        maxBufferSize = arguments[2];\n    }\n    return function bufferTimeOperatorFunction(source) {\n        return source.lift(new BufferTimeOperator(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler));\n    };\n}\nclass BufferTimeOperator {\n    constructor(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n        this.bufferTimeSpan = bufferTimeSpan;\n        this.bufferCreationInterval = bufferCreationInterval;\n        this.maxBufferSize = maxBufferSize;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferTimeSubscriber(subscriber, this.bufferTimeSpan, this.bufferCreationInterval, this.maxBufferSize, this.scheduler));\n    }\n}\nclass Context {\n    constructor() {\n        this.buffer = [];\n    }\n}\nclass BufferTimeSubscriber extends Subscriber {\n    constructor(destination, bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n        super(destination);\n        this.bufferTimeSpan = bufferTimeSpan;\n        this.bufferCreationInterval = bufferCreationInterval;\n        this.maxBufferSize = maxBufferSize;\n        this.scheduler = scheduler;\n        this.contexts = [];\n        const context = this.openContext();\n        this.timespanOnly = bufferCreationInterval == null || bufferCreationInterval < 0;\n        if (this.timespanOnly) {\n            const timeSpanOnlyState = { subscriber: this, context, bufferTimeSpan };\n            this.add(context.closeAction = scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n        }\n        else {\n            const closeState = { subscriber: this, context };\n            const creationState = { bufferTimeSpan, bufferCreationInterval, subscriber: this, scheduler };\n            this.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, closeState));\n            this.add(scheduler.schedule(dispatchBufferCreation, bufferCreationInterval, creationState));\n        }\n    }\n    _next(value) {\n        const contexts = this.contexts;\n        const len = contexts.length;\n        let filledBufferContext;\n        for (let i = 0; i < len; i++) {\n            const context = contexts[i];\n            const buffer = context.buffer;\n            buffer.push(value);\n            if (buffer.length == this.maxBufferSize) {\n                filledBufferContext = context;\n            }\n        }\n        if (filledBufferContext) {\n            this.onBufferFull(filledBufferContext);\n        }\n    }\n    _error(err) {\n        this.contexts.length = 0;\n        super._error(err);\n    }\n    _complete() {\n        const { contexts, destination } = this;\n        while (contexts.length > 0) {\n            const context = contexts.shift();\n            destination.next(context.buffer);\n        }\n        super._complete();\n    }\n    _unsubscribe() {\n        this.contexts = null;\n    }\n    onBufferFull(context) {\n        this.closeContext(context);\n        const closeAction = context.closeAction;\n        closeAction.unsubscribe();\n        this.remove(closeAction);\n        if (!this.closed && this.timespanOnly) {\n            context = this.openContext();\n            const bufferTimeSpan = this.bufferTimeSpan;\n            const timeSpanOnlyState = { subscriber: this, context, bufferTimeSpan };\n            this.add(context.closeAction = this.scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n        }\n    }\n    openContext() {\n        const context = new Context();\n        this.contexts.push(context);\n        return context;\n    }\n    closeContext(context) {\n        this.destination.next(context.buffer);\n        const contexts = this.contexts;\n        const spliceIndex = contexts ? contexts.indexOf(context) : -1;\n        if (spliceIndex >= 0) {\n            contexts.splice(contexts.indexOf(context), 1);\n        }\n    }\n}\nfunction dispatchBufferTimeSpanOnly(state) {\n    const subscriber = state.subscriber;\n    const prevContext = state.context;\n    if (prevContext) {\n        subscriber.closeContext(prevContext);\n    }\n    if (!subscriber.closed) {\n        state.context = subscriber.openContext();\n        state.context.closeAction = this.schedule(state, state.bufferTimeSpan);\n    }\n}\nfunction dispatchBufferCreation(state) {\n    const { bufferCreationInterval, bufferTimeSpan, subscriber, scheduler } = state;\n    const context = subscriber.openContext();\n    const action = this;\n    if (!subscriber.closed) {\n        subscriber.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, { subscriber, context }));\n        action.schedule(state, bufferCreationInterval);\n    }\n}\nfunction dispatchBufferClose(arg) {\n    const { subscriber, context } = arg;\n    subscriber.closeContext(context);\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,SAASC,UAAUA,CAACC,cAAc,EAAE;EACvC,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM;EAC7B,IAAIE,SAAS,GAAGP,KAAK;EACrB,IAAIE,WAAW,CAACI,SAAS,CAACA,SAAS,CAACD,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;IAC9CE,SAAS,GAAGD,SAAS,CAACA,SAAS,CAACD,MAAM,GAAG,CAAC,CAAC;IAC3CA,MAAM,EAAE;EACZ;EACA,IAAIG,sBAAsB,GAAG,IAAI;EACjC,IAAIH,MAAM,IAAI,CAAC,EAAE;IACbG,sBAAsB,GAAGF,SAAS,CAAC,CAAC,CAAC;EACzC;EACA,IAAIG,aAAa,GAAGC,MAAM,CAACC,iBAAiB;EAC5C,IAAIN,MAAM,IAAI,CAAC,EAAE;IACbI,aAAa,GAAGH,SAAS,CAAC,CAAC,CAAC;EAChC;EACA,OAAO,SAASM,0BAA0BA,CAACC,MAAM,EAAE;IAC/C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,kBAAkB,CAACX,cAAc,EAAEI,sBAAsB,EAAEC,aAAa,EAAEF,SAAS,CAAC,CAAC;EAChH,CAAC;AACL;AACA,MAAMQ,kBAAkB,CAAC;EACrBC,WAAWA,CAACZ,cAAc,EAAEI,sBAAsB,EAAEC,aAAa,EAAEF,SAAS,EAAE;IAC1E,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACI,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;EACAU,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,oBAAoB,CAACF,UAAU,EAAE,IAAI,CAACd,cAAc,EAAE,IAAI,CAACI,sBAAsB,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAACF,SAAS,CAAC,CAAC;EACvJ;AACJ;AACA,MAAMc,OAAO,CAAC;EACVL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACM,MAAM,GAAG,EAAE;EACpB;AACJ;AACA,MAAMF,oBAAoB,SAASnB,UAAU,CAAC;EAC1Ce,WAAWA,CAACO,WAAW,EAAEnB,cAAc,EAAEI,sBAAsB,EAAEC,aAAa,EAAEF,SAAS,EAAE;IACvF,KAAK,CAACgB,WAAW,CAAC;IAClB,IAAI,CAACnB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACI,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiB,QAAQ,GAAG,EAAE;IAClB,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IAClC,IAAI,CAACC,YAAY,GAAGnB,sBAAsB,IAAI,IAAI,IAAIA,sBAAsB,GAAG,CAAC;IAChF,IAAI,IAAI,CAACmB,YAAY,EAAE;MACnB,MAAMC,iBAAiB,GAAG;QAAEV,UAAU,EAAE,IAAI;QAAEO,OAAO;QAAErB;MAAe,CAAC;MACvE,IAAI,CAACyB,GAAG,CAACJ,OAAO,CAACK,WAAW,GAAGvB,SAAS,CAACwB,QAAQ,CAACC,0BAA0B,EAAE5B,cAAc,EAAEwB,iBAAiB,CAAC,CAAC;IACrH,CAAC,MACI;MACD,MAAMK,UAAU,GAAG;QAAEf,UAAU,EAAE,IAAI;QAAEO;MAAQ,CAAC;MAChD,MAAMS,aAAa,GAAG;QAAE9B,cAAc;QAAEI,sBAAsB;QAAEU,UAAU,EAAE,IAAI;QAAEX;MAAU,CAAC;MAC7F,IAAI,CAACsB,GAAG,CAACJ,OAAO,CAACK,WAAW,GAAGvB,SAAS,CAACwB,QAAQ,CAACI,mBAAmB,EAAE/B,cAAc,EAAE6B,UAAU,CAAC,CAAC;MACnG,IAAI,CAACJ,GAAG,CAACtB,SAAS,CAACwB,QAAQ,CAACK,sBAAsB,EAAE5B,sBAAsB,EAAE0B,aAAa,CAAC,CAAC;IAC/F;EACJ;EACAG,KAAKA,CAACC,KAAK,EAAE;IACT,MAAMd,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMe,GAAG,GAAGf,QAAQ,CAACnB,MAAM;IAC3B,IAAImC,mBAAmB;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC1B,MAAMhB,OAAO,GAAGD,QAAQ,CAACiB,CAAC,CAAC;MAC3B,MAAMnB,MAAM,GAAGG,OAAO,CAACH,MAAM;MAC7BA,MAAM,CAACoB,IAAI,CAACJ,KAAK,CAAC;MAClB,IAAIhB,MAAM,CAACjB,MAAM,IAAI,IAAI,CAACI,aAAa,EAAE;QACrC+B,mBAAmB,GAAGf,OAAO;MACjC;IACJ;IACA,IAAIe,mBAAmB,EAAE;MACrB,IAAI,CAACG,YAAY,CAACH,mBAAmB,CAAC;IAC1C;EACJ;EACAI,MAAMA,CAACC,GAAG,EAAE;IACR,IAAI,CAACrB,QAAQ,CAACnB,MAAM,GAAG,CAAC;IACxB,KAAK,CAACuC,MAAM,CAACC,GAAG,CAAC;EACrB;EACAC,SAASA,CAAA,EAAG;IACR,MAAM;MAAEtB,QAAQ;MAAED;IAAY,CAAC,GAAG,IAAI;IACtC,OAAOC,QAAQ,CAACnB,MAAM,GAAG,CAAC,EAAE;MACxB,MAAMoB,OAAO,GAAGD,QAAQ,CAACuB,KAAK,CAAC,CAAC;MAChCxB,WAAW,CAACyB,IAAI,CAACvB,OAAO,CAACH,MAAM,CAAC;IACpC;IACA,KAAK,CAACwB,SAAS,CAAC,CAAC;EACrB;EACAG,YAAYA,CAAA,EAAG;IACX,IAAI,CAACzB,QAAQ,GAAG,IAAI;EACxB;EACAmB,YAAYA,CAAClB,OAAO,EAAE;IAClB,IAAI,CAACyB,YAAY,CAACzB,OAAO,CAAC;IAC1B,MAAMK,WAAW,GAAGL,OAAO,CAACK,WAAW;IACvCA,WAAW,CAACqB,WAAW,CAAC,CAAC;IACzB,IAAI,CAACC,MAAM,CAACtB,WAAW,CAAC;IACxB,IAAI,CAAC,IAAI,CAACuB,MAAM,IAAI,IAAI,CAAC1B,YAAY,EAAE;MACnCF,OAAO,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MAC5B,MAAMtB,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,MAAMwB,iBAAiB,GAAG;QAAEV,UAAU,EAAE,IAAI;QAAEO,OAAO;QAAErB;MAAe,CAAC;MACvE,IAAI,CAACyB,GAAG,CAACJ,OAAO,CAACK,WAAW,GAAG,IAAI,CAACvB,SAAS,CAACwB,QAAQ,CAACC,0BAA0B,EAAE5B,cAAc,EAAEwB,iBAAiB,CAAC,CAAC;IAC1H;EACJ;EACAF,WAAWA,CAAA,EAAG;IACV,MAAMD,OAAO,GAAG,IAAIJ,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACG,QAAQ,CAACkB,IAAI,CAACjB,OAAO,CAAC;IAC3B,OAAOA,OAAO;EAClB;EACAyB,YAAYA,CAACzB,OAAO,EAAE;IAClB,IAAI,CAACF,WAAW,CAACyB,IAAI,CAACvB,OAAO,CAACH,MAAM,CAAC;IACrC,MAAME,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAM8B,WAAW,GAAG9B,QAAQ,GAAGA,QAAQ,CAAC+B,OAAO,CAAC9B,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI6B,WAAW,IAAI,CAAC,EAAE;MAClB9B,QAAQ,CAACgC,MAAM,CAAChC,QAAQ,CAAC+B,OAAO,CAAC9B,OAAO,CAAC,EAAE,CAAC,CAAC;IACjD;EACJ;AACJ;AACA,SAASO,0BAA0BA,CAACyB,KAAK,EAAE;EACvC,MAAMvC,UAAU,GAAGuC,KAAK,CAACvC,UAAU;EACnC,MAAMwC,WAAW,GAAGD,KAAK,CAAChC,OAAO;EACjC,IAAIiC,WAAW,EAAE;IACbxC,UAAU,CAACgC,YAAY,CAACQ,WAAW,CAAC;EACxC;EACA,IAAI,CAACxC,UAAU,CAACmC,MAAM,EAAE;IACpBI,KAAK,CAAChC,OAAO,GAAGP,UAAU,CAACQ,WAAW,CAAC,CAAC;IACxC+B,KAAK,CAAChC,OAAO,CAACK,WAAW,GAAG,IAAI,CAACC,QAAQ,CAAC0B,KAAK,EAAEA,KAAK,CAACrD,cAAc,CAAC;EAC1E;AACJ;AACA,SAASgC,sBAAsBA,CAACqB,KAAK,EAAE;EACnC,MAAM;IAAEjD,sBAAsB;IAAEJ,cAAc;IAAEc,UAAU;IAAEX;EAAU,CAAC,GAAGkD,KAAK;EAC/E,MAAMhC,OAAO,GAAGP,UAAU,CAACQ,WAAW,CAAC,CAAC;EACxC,MAAMiC,MAAM,GAAG,IAAI;EACnB,IAAI,CAACzC,UAAU,CAACmC,MAAM,EAAE;IACpBnC,UAAU,CAACW,GAAG,CAACJ,OAAO,CAACK,WAAW,GAAGvB,SAAS,CAACwB,QAAQ,CAACI,mBAAmB,EAAE/B,cAAc,EAAE;MAAEc,UAAU;MAAEO;IAAQ,CAAC,CAAC,CAAC;IACtHkC,MAAM,CAAC5B,QAAQ,CAAC0B,KAAK,EAAEjD,sBAAsB,CAAC;EAClD;AACJ;AACA,SAAS2B,mBAAmBA,CAACyB,GAAG,EAAE;EAC9B,MAAM;IAAE1C,UAAU;IAAEO;EAAQ,CAAC,GAAGmC,GAAG;EACnC1C,UAAU,CAACgC,YAAY,CAACzB,OAAO,CAAC;AACpC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
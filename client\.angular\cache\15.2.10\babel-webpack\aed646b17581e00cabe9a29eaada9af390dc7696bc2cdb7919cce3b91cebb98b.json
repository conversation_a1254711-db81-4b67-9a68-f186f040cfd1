{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SearchService {\n  /**\r\n   *\r\n   * @param {HttpClient} _httpClient\r\n   */\n  constructor(_httpClient) {\n    this._httpClient = _httpClient;\n    // Public\n    this.search = '';\n    this.apiData = [];\n    this.onApiDataChange = new BehaviorSubject('');\n    this.onIsBookmarkOpenChange = new BehaviorSubject(false);\n    this.getSearchData();\n  }\n  /**\r\n   * Get Search Data\r\n   */\n  getSearchData() {\n    return new Promise((resolve, reject) => {\n      this._httpClient.get('api/search-data').subscribe(response => {\n        this.apiData = response;\n        this.onApiDataChange.next(this.apiData);\n        resolve(this.apiData);\n      }, reject);\n    });\n  }\n  static #_ = this.ɵfac = function SearchService_Factory(t) {\n    return new (t || SearchService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SearchService,\n    factory: SearchService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,eAAe,QAAQ,MAAM;;;AAKtC,OAAM,MAAOC,aAAa;EAOxB;;;;EAIAC,YAAoBC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAV/B;IACO,KAAAC,MAAM,GAAG,EAAE;IACX,KAAAC,OAAO,GAAG,EAAE;IASjB,IAAI,CAACC,eAAe,GAAG,IAAIN,eAAe,CAAC,EAAE,CAAC;IAC9C,IAAI,CAACO,sBAAsB,GAAG,IAAIP,eAAe,CAAC,KAAK,CAAC;IACxD,IAAI,CAACQ,aAAa,EAAE;EACtB;EAEA;;;EAGAA,aAAaA,CAAA;IACX,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACR,WAAW,CAACS,GAAG,CAAC,iBAAiB,CAAC,CAACC,SAAS,CAAEC,QAAa,IAAI;QAClE,IAAI,CAACT,OAAO,GAAGS,QAAQ;QACvB,IAAI,CAACR,eAAe,CAACS,IAAI,CAAC,IAAI,CAACV,OAAO,CAAC;QACvCK,OAAO,CAAC,IAAI,CAACL,OAAO,CAAC;MACvB,CAAC,EAAEM,MAAM,CAAC;IACZ,CAAC,CAAC;EACJ;EAAC,QAAAK,CAAA;qBA5BUf,aAAa,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAbpB,aAAa;IAAAqB,OAAA,EAAbrB,aAAa,CAAAsB,IAAA;IAAAC,UAAA,EAFZ;EAAM", "names": ["BehaviorSubject", "SearchService", "constructor", "_httpClient", "search", "apiData", "onApiDataChange", "onIsBookmarkOpenChange", "getSearchData", "Promise", "resolve", "reject", "get", "subscribe", "response", "next", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar-search\\search.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\n\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SearchService {\r\n  // Public\r\n  public search = '';\r\n  public apiData = [];\r\n  public onApiDataChange: BehaviorSubject<any>;\r\n  public onIsBookmarkOpenChange: BehaviorSubject<any>;\r\n\r\n  /**\r\n   *\r\n   * @param {HttpClient} _httpClient\r\n   */\r\n  constructor(private _httpClient: HttpClient) {\r\n    this.onApiDataChange = new BehaviorSubject('');\r\n    this.onIsBookmarkOpenChange = new BehaviorSubject(false);\r\n    this.getSearchData();\r\n  }\r\n\r\n  /**\r\n   * Get Search Data\r\n   */\r\n  getSearchData(): Promise<any[]> {\r\n    return new Promise((resolve, reject) => {\r\n      this._httpClient.get('api/search-data').subscribe((response: any) => {\r\n        this.apiData = response;\r\n        this.onApiDataChange.next(this.apiData);\r\n        resolve(this.apiData);\r\n      }, reject);\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/*!\n * perfect-scrollbar v1.5.0\n * Copyright 2020 Hyun<PERSON>, MDBootstrap and Contributors\n * Licensed under MIT\n */\n\nfunction get(element) {\n  return getComputedStyle(element);\n}\nfunction set(element, obj) {\n  for (var key in obj) {\n    var val = obj[key];\n    if (typeof val === 'number') {\n      val = val + \"px\";\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\nfunction div(className) {\n  var div = document.createElement('div');\n  div.className = className;\n  return div;\n}\nvar elMatches = typeof Element !== 'undefined' && (Element.prototype.matches || Element.prototype.webkitMatchesSelector || Element.prototype.mozMatchesSelector || Element.prototype.msMatchesSelector);\nfunction matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n  return elMatches.call(element, query);\n}\nfunction remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\nfunction queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, function (child) {\n    return matches(child, selector);\n  });\n}\nvar cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: function (x) {\n      return \"ps__thumb-\" + x;\n    },\n    rail: function (x) {\n      return \"ps__rail-\" + x;\n    },\n    consuming: 'ps__child--consume'\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: function (x) {\n      return \"ps--active-\" + x;\n    },\n    scrolling: function (x) {\n      return \"ps--scrolling-\" + x;\n    }\n  }\n};\n\n/*\n * Helper methods\n */\nvar scrollingClassTimeout = {\n  x: null,\n  y: null\n};\nfunction addScrollingClass(i, x) {\n  var classList = i.element.classList;\n  var className = cls.state.scrolling(x);\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\nfunction removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(function () {\n    return i.isAlive && i.element.classList.remove(cls.state.scrolling(x));\n  }, i.settings.scrollingThreshold);\n}\nfunction setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\nvar EventElement = function EventElement(element) {\n  this.element = element;\n  this.handlers = {};\n};\nvar prototypeAccessors = {\n  isEmpty: {\n    configurable: true\n  }\n};\nEventElement.prototype.bind = function bind(eventName, handler) {\n  if (typeof this.handlers[eventName] === 'undefined') {\n    this.handlers[eventName] = [];\n  }\n  this.handlers[eventName].push(handler);\n  this.element.addEventListener(eventName, handler, false);\n};\nEventElement.prototype.unbind = function unbind(eventName, target) {\n  var this$1 = this;\n  this.handlers[eventName] = this.handlers[eventName].filter(function (handler) {\n    if (target && handler !== target) {\n      return true;\n    }\n    this$1.element.removeEventListener(eventName, handler, false);\n    return false;\n  });\n};\nEventElement.prototype.unbindAll = function unbindAll() {\n  for (var name in this.handlers) {\n    this.unbind(name);\n  }\n};\nprototypeAccessors.isEmpty.get = function () {\n  var this$1 = this;\n  return Object.keys(this.handlers).every(function (key) {\n    return this$1.handlers[key].length === 0;\n  });\n};\nObject.defineProperties(EventElement.prototype, prototypeAccessors);\nvar EventManager = function EventManager() {\n  this.eventElements = [];\n};\nEventManager.prototype.eventElement = function eventElement(element) {\n  var ee = this.eventElements.filter(function (ee) {\n    return ee.element === element;\n  })[0];\n  if (!ee) {\n    ee = new EventElement(element);\n    this.eventElements.push(ee);\n  }\n  return ee;\n};\nEventManager.prototype.bind = function bind(element, eventName, handler) {\n  this.eventElement(element).bind(eventName, handler);\n};\nEventManager.prototype.unbind = function unbind(element, eventName, handler) {\n  var ee = this.eventElement(element);\n  ee.unbind(eventName, handler);\n  if (ee.isEmpty) {\n    // remove\n    this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n  }\n};\nEventManager.prototype.unbindAll = function unbindAll() {\n  this.eventElements.forEach(function (e) {\n    return e.unbindAll();\n  });\n  this.eventElements = [];\n};\nEventManager.prototype.once = function once(element, eventName, handler) {\n  var ee = this.eventElement(element);\n  var onceHandler = function (evt) {\n    ee.unbind(eventName, onceHandler);\n    handler(evt);\n  };\n  ee.bind(eventName, onceHandler);\n};\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  } else {\n    var evt = document.createEvent('CustomEvent');\n    evt.initCustomEvent(name, false, false, undefined);\n    return evt;\n  }\n}\nfunction processScrollDiff(i, axis, diff, useScrollingClass, forceFireReachEvent) {\n  if (useScrollingClass === void 0) useScrollingClass = true;\n  if (forceFireReachEvent === void 0) forceFireReachEvent = false;\n  var fields;\n  if (axis === 'top') {\n    fields = ['contentHeight', 'containerHeight', 'scrollTop', 'y', 'up', 'down'];\n  } else if (axis === 'left') {\n    fields = ['contentWidth', 'containerWidth', 'scrollLeft', 'x', 'left', 'right'];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n  processScrollDiff$1(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\nfunction processScrollDiff$1(i, diff, ref, useScrollingClass, forceFireReachEvent) {\n  var contentHeight = ref[0];\n  var containerHeight = ref[1];\n  var scrollTop = ref[2];\n  var y = ref[3];\n  var up = ref[4];\n  var down = ref[5];\n  if (useScrollingClass === void 0) useScrollingClass = true;\n  if (forceFireReachEvent === void 0) forceFireReachEvent = false;\n  var element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n  if (diff) {\n    element.dispatchEvent(createEvent(\"ps-scroll-\" + y));\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(\"ps-scroll-\" + up));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(\"ps-scroll-\" + down));\n    }\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(\"ps-\" + y + \"-reach-\" + i.reach[y]));\n  }\n}\nfunction toInt(x) {\n  return parseInt(x, 10) || 0;\n}\nfunction isEditable(el) {\n  return matches(el, 'input,[contenteditable]') || matches(el, 'select,[contenteditable]') || matches(el, 'textarea,[contenteditable]') || matches(el, 'button,[contenteditable]');\n}\nfunction outerWidth(element) {\n  var styles = get(element);\n  return toInt(styles.width) + toInt(styles.paddingLeft) + toInt(styles.paddingRight) + toInt(styles.borderLeftWidth) + toInt(styles.borderRightWidth);\n}\nvar env = {\n  isWebKit: typeof document !== 'undefined' && 'WebkitAppearance' in document.documentElement.style,\n  supportsTouch: typeof window !== 'undefined' && ('ontouchstart' in window || 'maxTouchPoints' in window.navigator && window.navigator.maxTouchPoints > 0 || window.DocumentTouch && document instanceof window.DocumentTouch),\n  supportsIePointer: typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome: typeof navigator !== 'undefined' && /Chrome/i.test(navigator && navigator.userAgent)\n};\nfunction updateGeometry(i) {\n  var element = i.element;\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  var rect = element.getBoundingClientRect();\n  i.containerWidth = Math.ceil(rect.width);\n  i.containerHeight = Math.ceil(rect.height);\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('x')).forEach(function (el) {\n      return remove(el);\n    });\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('y')).forEach(function (el) {\n      return remove(el);\n    });\n    element.appendChild(i.scrollbarYRail);\n  }\n  if (!i.settings.suppressScrollX && i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(i, toInt(i.railXWidth * i.containerWidth / i.contentWidth));\n    i.scrollbarXLeft = toInt((i.negativeScrollAdjustment + element.scrollLeft) * (i.railXWidth - i.scrollbarXWidth) / (i.contentWidth - i.containerWidth));\n  } else {\n    i.scrollbarXActive = false;\n  }\n  if (!i.settings.suppressScrollY && i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(i, toInt(i.railYHeight * i.containerHeight / i.contentHeight));\n    i.scrollbarYTop = toInt(roundedScrollTop * (i.railYHeight - i.scrollbarYHeight) / (i.contentHeight - i.containerHeight));\n  } else {\n    i.scrollbarYActive = false;\n  }\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n  updateCss(element, i);\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\nfunction updateCss(element, i) {\n  var xRailOffset = {\n    width: i.railXWidth\n  };\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  if (i.isRtl) {\n    xRailOffset.left = i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth - i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  set(i.scrollbarXRail, xRailOffset);\n  var yRailOffset = {\n    top: roundedScrollTop,\n    height: i.railYHeight\n  };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right = i.contentWidth - (i.negativeScrollAdjustment + element.scrollLeft) - i.scrollbarYRight - i.scrollbarYOuterWidth - 9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left = i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth * 2 - i.contentWidth - i.scrollbarYLeft - i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  set(i.scrollbarYRail, yRailOffset);\n  set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth\n  });\n  set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth\n  });\n}\nfunction clickRail(i) {\n  var element = i.element;\n  i.event.bind(i.scrollbarY, 'mousedown', function (e) {\n    return e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarYRail, 'mousedown', function (e) {\n    var positionTop = e.pageY - window.pageYOffset - i.scrollbarYRail.getBoundingClientRect().top;\n    var direction = positionTop > i.scrollbarYTop ? 1 : -1;\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n    e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarX, 'mousedown', function (e) {\n    return e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarXRail, 'mousedown', function (e) {\n    var positionLeft = e.pageX - window.pageXOffset - i.scrollbarXRail.getBoundingClientRect().left;\n    var direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n    e.stopPropagation();\n  });\n}\nfunction dragThumb(i) {\n  bindMouseScrollHandler(i, ['containerWidth', 'contentWidth', 'pageX', 'railXWidth', 'scrollbarX', 'scrollbarXWidth', 'scrollLeft', 'x', 'scrollbarXRail']);\n  bindMouseScrollHandler(i, ['containerHeight', 'contentHeight', 'pageY', 'railYHeight', 'scrollbarY', 'scrollbarYHeight', 'scrollTop', 'y', 'scrollbarYRail']);\n}\nfunction bindMouseScrollHandler(i, ref) {\n  var containerHeight = ref[0];\n  var contentHeight = ref[1];\n  var pageY = ref[2];\n  var railYHeight = ref[3];\n  var scrollbarY = ref[4];\n  var scrollbarYHeight = ref[5];\n  var scrollTop = ref[6];\n  var y = ref[7];\n  var scrollbarYRail = ref[8];\n  var element = i.element;\n  var startingScrollTop = null;\n  var startingMousePageY = null;\n  var scrollBy = null;\n  function mouseMoveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    element[scrollTop] = startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n    addScrollingClass(i, y);\n    updateGeometry(i);\n    e.stopPropagation();\n    e.preventDefault();\n  }\n  function mouseUpHandler() {\n    removeScrollingClass(i, y);\n    i[scrollbarYRail].classList.remove(cls.state.clicking);\n    i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n  }\n  function bindMoves(e, touchMode) {\n    startingScrollTop = element[scrollTop];\n    if (touchMode && e.touches) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    startingMousePageY = e[pageY];\n    scrollBy = (i[contentHeight] - i[containerHeight]) / (i[railYHeight] - i[scrollbarYHeight]);\n    if (!touchMode) {\n      i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n      i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n      e.preventDefault();\n    } else {\n      i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n    }\n    i[scrollbarYRail].classList.add(cls.state.clicking);\n    e.stopPropagation();\n  }\n  i.event.bind(i[scrollbarY], 'mousedown', function (e) {\n    bindMoves(e);\n  });\n  i.event.bind(i[scrollbarY], 'touchstart', function (e) {\n    bindMoves(e, true);\n  });\n}\nfunction keyboard(i) {\n  var element = i.element;\n  var elementHovered = function () {\n    return matches(element, ':hover');\n  };\n  var scrollbarFocused = function () {\n    return matches(i.scrollbarX, ':focus') || matches(i.scrollbarY, ':focus');\n  };\n  function shouldPreventDefault(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (scrollTop === 0 && deltaY > 0 || scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    var scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (scrollLeft === 0 && deltaX < 0 || scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n  i.event.bind(i.ownerDocument, 'keydown', function (e) {\n    if (e.isDefaultPrevented && e.isDefaultPrevented() || e.defaultPrevented) {\n      return;\n    }\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n    var activeElement = document.activeElement ? document.activeElement : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n    var deltaX = 0;\n    var deltaY = 0;\n    switch (e.which) {\n      case 37:\n        // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38:\n        // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39:\n        // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40:\n        // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32:\n        // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33:\n        // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34:\n        // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36:\n        // home\n        deltaY = i.contentHeight;\n        break;\n      case 35:\n        // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\nfunction wheel(i) {\n  var element = i.element;\n  function shouldPreventDefault(deltaX, deltaY) {\n    var roundedScrollTop = Math.floor(element.scrollTop);\n    var isTop = element.scrollTop === 0;\n    var isBottom = roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    var isLeft = element.scrollLeft === 0;\n    var isRight = element.scrollLeft + element.offsetWidth === element.scrollWidth;\n    var hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n  function getDeltaFromEvent(e) {\n    var deltaX = e.deltaX;\n    var deltaY = -1 * e.deltaY;\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = -1 * e.wheelDeltaX / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n    if (!element.contains(target)) {\n      return false;\n    }\n    var cursor = target;\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (cursor.scrollTop > 0 && deltaY < 0 || cursor.scrollTop < maxScrollTop && deltaY > 0) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (cursor.scrollLeft > 0 && deltaX < 0 || cursor.scrollLeft < maxScrollLeft && deltaX > 0) {\n            return true;\n          }\n        }\n      }\n      cursor = cursor.parentNode;\n    }\n    return false;\n  }\n  function mousewheelHandler(e) {\n    var ref = getDeltaFromEvent(e);\n    var deltaX = ref[0];\n    var deltaY = ref[1];\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n    var shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n    updateGeometry(i);\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\nfunction touch(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n  var element = i.element;\n  function shouldPrevent(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    var scrollLeft = element.scrollLeft;\n    var magnitudeX = Math.abs(deltaX);\n    var magnitudeY = Math.abs(deltaY);\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight || deltaY > 0 && scrollTop === 0) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth || deltaX > 0 && scrollLeft === 0) {\n        return true;\n      }\n    }\n    return true;\n  }\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n    updateGeometry(i);\n  }\n  var startOffset = {};\n  var startTime = 0;\n  var speed = {};\n  var easingLoop = null;\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    } else {\n      // Maybe IE pointer\n      return e;\n    }\n  }\n  function shouldHandle(e) {\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (e.pointerType && e.pointerType !== 'mouse' && e.pointerType !== e.MSPOINTER_TYPE_MOUSE) {\n      return true;\n    }\n    return false;\n  }\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n    var touch = getTouch(e);\n    startOffset.pageX = touch.pageX;\n    startOffset.pageY = touch.pageY;\n    startTime = new Date().getTime();\n    if (easingLoop !== null) {\n      clearInterval(easingLoop);\n    }\n  }\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n    var cursor = target;\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (cursor.scrollTop > 0 && deltaY < 0 || cursor.scrollTop < maxScrollTop && deltaY > 0) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (cursor.scrollLeft > 0 && deltaX < 0 || cursor.scrollLeft < maxScrollLeft && deltaX > 0) {\n            return true;\n          }\n        }\n      }\n      cursor = cursor.parentNode;\n    }\n    return false;\n  }\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      var touch = getTouch(e);\n      var currentOffset = {\n        pageX: touch.pageX,\n        pageY: touch.pageY\n      };\n      var differenceX = currentOffset.pageX - startOffset.pageX;\n      var differenceY = currentOffset.pageY - startOffset.pageY;\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n      applyTouchMove(differenceX, differenceY);\n      startOffset = currentOffset;\n      var currentTime = new Date().getTime();\n      var timeGap = currentTime - startTime;\n      if (timeGap > 0) {\n        speed.x = differenceX / timeGap;\n        speed.y = differenceY / timeGap;\n        startTime = currentTime;\n      }\n      if (shouldPrevent(differenceX, differenceY)) {\n        e.preventDefault();\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(easingLoop);\n      easingLoop = setInterval(function () {\n        if (i.isInitialized) {\n          clearInterval(easingLoop);\n          return;\n        }\n        if (!speed.x && !speed.y) {\n          clearInterval(easingLoop);\n          return;\n        }\n        if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n          clearInterval(easingLoop);\n          return;\n        }\n        applyTouchMove(speed.x * 30, speed.y * 30);\n        speed.x *= 0.8;\n        speed.y *= 0.8;\n      }, 10);\n    }\n  }\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\nvar defaultSettings = function () {\n  return {\n    handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n    maxScrollbarLength: null,\n    minScrollbarLength: null,\n    scrollingThreshold: 1000,\n    scrollXMarginOffset: 0,\n    scrollYMarginOffset: 0,\n    suppressScrollX: false,\n    suppressScrollY: false,\n    swipeEasing: true,\n    useBothWheelAxes: false,\n    wheelPropagation: true,\n    wheelSpeed: 1\n  };\n};\nvar handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard: keyboard,\n  wheel: wheel,\n  touch: touch\n};\nvar PerfectScrollbar = function PerfectScrollbar(element, userSettings) {\n  var this$1 = this;\n  if (userSettings === void 0) userSettings = {};\n  if (typeof element === 'string') {\n    element = document.querySelector(element);\n  }\n  if (!element || !element.nodeName) {\n    throw new Error('no element is specified to initialize PerfectScrollbar');\n  }\n  this.element = element;\n  element.classList.add(cls.main);\n  this.settings = defaultSettings();\n  for (var key in userSettings) {\n    this.settings[key] = userSettings[key];\n  }\n  this.containerWidth = null;\n  this.containerHeight = null;\n  this.contentWidth = null;\n  this.contentHeight = null;\n  var focus = function () {\n    return element.classList.add(cls.state.focus);\n  };\n  var blur = function () {\n    return element.classList.remove(cls.state.focus);\n  };\n  this.isRtl = get(element).direction === 'rtl';\n  if (this.isRtl === true) {\n    element.classList.add(cls.rtl);\n  }\n  this.isNegativeScroll = function () {\n    var originalScrollLeft = element.scrollLeft;\n    var result = null;\n    element.scrollLeft = -1;\n    result = element.scrollLeft < 0;\n    element.scrollLeft = originalScrollLeft;\n    return result;\n  }();\n  this.negativeScrollAdjustment = this.isNegativeScroll ? element.scrollWidth - element.clientWidth : 0;\n  this.event = new EventManager();\n  this.ownerDocument = element.ownerDocument || document;\n  this.scrollbarXRail = div(cls.element.rail('x'));\n  element.appendChild(this.scrollbarXRail);\n  this.scrollbarX = div(cls.element.thumb('x'));\n  this.scrollbarXRail.appendChild(this.scrollbarX);\n  this.scrollbarX.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarX, 'focus', focus);\n  this.event.bind(this.scrollbarX, 'blur', blur);\n  this.scrollbarXActive = null;\n  this.scrollbarXWidth = null;\n  this.scrollbarXLeft = null;\n  var railXStyle = get(this.scrollbarXRail);\n  this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n  if (isNaN(this.scrollbarXBottom)) {\n    this.isScrollbarXUsingBottom = false;\n    this.scrollbarXTop = toInt(railXStyle.top);\n  } else {\n    this.isScrollbarXUsingBottom = true;\n  }\n  this.railBorderXWidth = toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n  // Set rail to display:block to calculate margins\n  set(this.scrollbarXRail, {\n    display: 'block'\n  });\n  this.railXMarginWidth = toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n  set(this.scrollbarXRail, {\n    display: ''\n  });\n  this.railXWidth = null;\n  this.railXRatio = null;\n  this.scrollbarYRail = div(cls.element.rail('y'));\n  element.appendChild(this.scrollbarYRail);\n  this.scrollbarY = div(cls.element.thumb('y'));\n  this.scrollbarYRail.appendChild(this.scrollbarY);\n  this.scrollbarY.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarY, 'focus', focus);\n  this.event.bind(this.scrollbarY, 'blur', blur);\n  this.scrollbarYActive = null;\n  this.scrollbarYHeight = null;\n  this.scrollbarYTop = null;\n  var railYStyle = get(this.scrollbarYRail);\n  this.scrollbarYRight = parseInt(railYStyle.right, 10);\n  if (isNaN(this.scrollbarYRight)) {\n    this.isScrollbarYUsingRight = false;\n    this.scrollbarYLeft = toInt(railYStyle.left);\n  } else {\n    this.isScrollbarYUsingRight = true;\n  }\n  this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n  this.railBorderYWidth = toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n  set(this.scrollbarYRail, {\n    display: 'block'\n  });\n  this.railYMarginHeight = toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n  set(this.scrollbarYRail, {\n    display: ''\n  });\n  this.railYHeight = null;\n  this.railYRatio = null;\n  this.reach = {\n    x: element.scrollLeft <= 0 ? 'start' : element.scrollLeft >= this.contentWidth - this.containerWidth ? 'end' : null,\n    y: element.scrollTop <= 0 ? 'start' : element.scrollTop >= this.contentHeight - this.containerHeight ? 'end' : null\n  };\n  this.isAlive = true;\n  this.settings.handlers.forEach(function (handlerName) {\n    return handlers[handlerName](this$1);\n  });\n  this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n  this.lastScrollLeft = element.scrollLeft; // for onScroll only\n  this.event.bind(this.element, 'scroll', function (e) {\n    return this$1.onScroll(e);\n  });\n  updateGeometry(this);\n};\nPerfectScrollbar.prototype.update = function update() {\n  if (!this.isAlive) {\n    return;\n  }\n\n  // Recalcuate negative scrollLeft adjustment\n  this.negativeScrollAdjustment = this.isNegativeScroll ? this.element.scrollWidth - this.element.clientWidth : 0;\n\n  // Recalculate rail margins\n  set(this.scrollbarXRail, {\n    display: 'block'\n  });\n  set(this.scrollbarYRail, {\n    display: 'block'\n  });\n  this.railXMarginWidth = toInt(get(this.scrollbarXRail).marginLeft) + toInt(get(this.scrollbarXRail).marginRight);\n  this.railYMarginHeight = toInt(get(this.scrollbarYRail).marginTop) + toInt(get(this.scrollbarYRail).marginBottom);\n\n  // Hide scrollbars not to affect scrollWidth and scrollHeight\n  set(this.scrollbarXRail, {\n    display: 'none'\n  });\n  set(this.scrollbarYRail, {\n    display: 'none'\n  });\n  updateGeometry(this);\n  processScrollDiff(this, 'top', 0, false, true);\n  processScrollDiff(this, 'left', 0, false, true);\n  set(this.scrollbarXRail, {\n    display: ''\n  });\n  set(this.scrollbarYRail, {\n    display: ''\n  });\n};\nPerfectScrollbar.prototype.onScroll = function onScroll(e) {\n  if (!this.isAlive) {\n    return;\n  }\n  updateGeometry(this);\n  processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n  processScrollDiff(this, 'left', this.element.scrollLeft - this.lastScrollLeft);\n  this.lastScrollTop = Math.floor(this.element.scrollTop);\n  this.lastScrollLeft = this.element.scrollLeft;\n};\nPerfectScrollbar.prototype.destroy = function destroy() {\n  if (!this.isAlive) {\n    return;\n  }\n  this.event.unbindAll();\n  remove(this.scrollbarX);\n  remove(this.scrollbarY);\n  remove(this.scrollbarXRail);\n  remove(this.scrollbarYRail);\n  this.removePsClasses();\n\n  // unset elements\n  this.element = null;\n  this.scrollbarX = null;\n  this.scrollbarY = null;\n  this.scrollbarXRail = null;\n  this.scrollbarYRail = null;\n  this.isAlive = false;\n};\nPerfectScrollbar.prototype.removePsClasses = function removePsClasses() {\n  this.element.className = this.element.className.split(' ').filter(function (name) {\n    return !name.match(/^ps([-_].+|)$/);\n  }).join(' ');\n};\nexport default PerfectScrollbar;", "map": {"version": 3, "names": ["get", "element", "getComputedStyle", "set", "obj", "key", "val", "style", "div", "className", "document", "createElement", "elMatches", "Element", "prototype", "matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "query", "Error", "call", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "query<PERSON><PERSON><PERSON><PERSON>", "selector", "Array", "filter", "children", "child", "cls", "main", "rtl", "thumb", "x", "rail", "consuming", "state", "focus", "clicking", "active", "scrolling", "scrollingClassTimeout", "y", "addScrollingClass", "i", "classList", "contains", "clearTimeout", "add", "removeScrollingClass", "setTimeout", "isAlive", "settings", "scrollingT<PERSON>eshold", "setScrollingClassInstantly", "EventElement", "handlers", "prototypeAccessors", "isEmpty", "configurable", "bind", "eventName", "handler", "push", "addEventListener", "unbind", "target", "this$1", "removeEventListener", "unbindAll", "name", "Object", "keys", "every", "length", "defineProperties", "EventManager", "eventElements", "eventElement", "ee", "splice", "indexOf", "for<PERSON>ach", "e", "once", "once<PERSON><PERSON><PERSON>", "evt", "createEvent", "window", "CustomEvent", "initCustomEvent", "undefined", "processScrollDiff", "axis", "diff", "useScrollingClass", "forceFireReachEvent", "fields", "processScrollDiff$1", "ref", "contentHeight", "containerHeight", "scrollTop", "up", "down", "reach", "dispatchEvent", "toInt", "parseInt", "isEditable", "el", "outerWidth", "styles", "width", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "env", "isWebKit", "documentElement", "supportsTouch", "navigator", "maxTouchPoints", "DocumentTouch", "supportsIePointer", "msMaxTouchPoints", "isChrome", "test", "userAgent", "updateGeometry", "roundedScrollTop", "Math", "floor", "rect", "getBoundingClientRect", "containerWidth", "ceil", "height", "contentWidth", "scrollWidth", "scrollHeight", "scrollbarXRail", "append<PERSON><PERSON><PERSON>", "scrollbarYRail", "suppressScrollX", "scrollXMarginOffset", "scrollbarXActive", "railXWidth", "railXMarginWidth", "railXRatio", "scrollbarXWidth", "getThumbSize", "scrollbarXLeft", "negativeScrollAdjustment", "scrollLeft", "suppressScrollY", "scrollYMarginOffset", "scrollbarYActive", "railYHeight", "railYMarginHeight", "railYRatio", "scrollbarYHeight", "scrollbarYTop", "updateCss", "isRtl", "thumbSize", "minScrollbar<PERSON><PERSON>th", "max", "maxS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "min", "xRailOffset", "left", "isScrollbarXUsingBottom", "bottom", "scrollbarXBottom", "top", "scrollbarXTop", "yRailOffset", "isScrollbarYUsingRight", "right", "scrollbarYRight", "scrollbarYOuterWidth", "scrollbarYLeft", "scrollbarX", "railBorderXWidth", "scrollbarY", "railBorderYWidth", "clickRail", "event", "stopPropagation", "positionTop", "pageY", "pageYOffset", "direction", "positionLeft", "pageX", "pageXOffset", "dragThumb", "bindMouseScrollHandler", "startingScrollTop", "startingMousePageY", "scrollBy", "mouseMoveHandler", "touches", "preventDefault", "mouseUpHandler", "ownerDocument", "bindMoves", "touchMode", "keyboard", "elementHovered", "scrollbarFocused", "shouldPreventDefault", "deltaX", "deltaY", "wheelPropagation", "isDefaultPrevented", "defaultPrevented", "activeElement", "tagName", "contentDocument", "shadowRoot", "which", "metaKey", "altKey", "shift<PERSON>ey", "wheel", "isTop", "isBottom", "offsetHeight", "isLeft", "isRight", "offsetWidth", "hitsBound", "abs", "getDeltaFromEvent", "wheelDeltaX", "wheelDeltaY", "deltaMode", "wheelDelta", "shouldBeConsumedByChild", "querySelector", "cursor", "overflowY", "match", "maxScrollTop", "clientHeight", "overflowX", "maxScrollLeft", "clientWidth", "mousewheelHandler", "shouldPrevent", "useBothWheelAxes", "wheelSpeed", "ctrl<PERSON>ey", "onwheel", "onmousew<PERSON><PERSON>", "touch", "magnitudeX", "magnitudeY", "scrollY", "applyTouchMove", "differenceX", "differenceY", "startOffset", "startTime", "speed", "easingLoop", "getTouch", "targetTouches", "<PERSON><PERSON><PERSON><PERSON>", "pointerType", "buttons", "MSPOINTER_TYPE_MOUSE", "touchStart", "Date", "getTime", "clearInterval", "touchMove", "currentOffset", "currentTime", "timeGap", "touchEnd", "swipeEasing", "setInterval", "isInitialized", "PointerEvent", "MSPointerEvent", "defaultSettings", "PerfectScrollbar", "userSettings", "nodeName", "blur", "isNegativeScroll", "originalScrollLeft", "result", "setAttribute", "railXStyle", "isNaN", "display", "marginLeft", "marginRight", "railYStyle", "borderTopWidth", "borderBottomWidth", "marginTop", "marginBottom", "handler<PERSON>ame", "lastScrollTop", "lastScrollLeft", "onScroll", "update", "destroy", "removePsClasses", "split", "join"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/perfect-scrollbar/dist/perfect-scrollbar.esm.js"], "sourcesContent": ["/*!\n * perfect-scrollbar v1.5.0\n * Copyright 2020 Hyun<PERSON>, MDBootstrap and Contributors\n * Licensed under MIT\n */\n\nfunction get(element) {\n  return getComputedStyle(element);\n}\n\nfunction set(element, obj) {\n  for (var key in obj) {\n    var val = obj[key];\n    if (typeof val === 'number') {\n      val = val + \"px\";\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n\nfunction div(className) {\n  var div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nvar elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nfunction matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nfunction remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nfunction queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, function (child) { return matches(child, selector); }\n  );\n}\n\nvar cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: function (x) { return (\"ps__thumb-\" + x); },\n    rail: function (x) { return (\"ps__rail-\" + x); },\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: function (x) { return (\"ps--active-\" + x); },\n    scrolling: function (x) { return (\"ps--scrolling-\" + x); },\n  },\n};\n\n/*\n * Helper methods\n */\nvar scrollingClassTimeout = { x: null, y: null };\n\nfunction addScrollingClass(i, x) {\n  var classList = i.element.classList;\n  var className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nfunction removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    function () { return i.isAlive && i.element.classList.remove(cls.state.scrolling(x)); },\n    i.settings.scrollingThreshold\n  );\n}\n\nfunction setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n\nvar EventElement = function EventElement(element) {\n  this.element = element;\n  this.handlers = {};\n};\n\nvar prototypeAccessors = { isEmpty: { configurable: true } };\n\nEventElement.prototype.bind = function bind (eventName, handler) {\n  if (typeof this.handlers[eventName] === 'undefined') {\n    this.handlers[eventName] = [];\n  }\n  this.handlers[eventName].push(handler);\n  this.element.addEventListener(eventName, handler, false);\n};\n\nEventElement.prototype.unbind = function unbind (eventName, target) {\n    var this$1 = this;\n\n  this.handlers[eventName] = this.handlers[eventName].filter(function (handler) {\n    if (target && handler !== target) {\n      return true;\n    }\n    this$1.element.removeEventListener(eventName, handler, false);\n    return false;\n  });\n};\n\nEventElement.prototype.unbindAll = function unbindAll () {\n  for (var name in this.handlers) {\n    this.unbind(name);\n  }\n};\n\nprototypeAccessors.isEmpty.get = function () {\n    var this$1 = this;\n\n  return Object.keys(this.handlers).every(\n    function (key) { return this$1.handlers[key].length === 0; }\n  );\n};\n\nObject.defineProperties( EventElement.prototype, prototypeAccessors );\n\nvar EventManager = function EventManager() {\n  this.eventElements = [];\n};\n\nEventManager.prototype.eventElement = function eventElement (element) {\n  var ee = this.eventElements.filter(function (ee) { return ee.element === element; })[0];\n  if (!ee) {\n    ee = new EventElement(element);\n    this.eventElements.push(ee);\n  }\n  return ee;\n};\n\nEventManager.prototype.bind = function bind (element, eventName, handler) {\n  this.eventElement(element).bind(eventName, handler);\n};\n\nEventManager.prototype.unbind = function unbind (element, eventName, handler) {\n  var ee = this.eventElement(element);\n  ee.unbind(eventName, handler);\n\n  if (ee.isEmpty) {\n    // remove\n    this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n  }\n};\n\nEventManager.prototype.unbindAll = function unbindAll () {\n  this.eventElements.forEach(function (e) { return e.unbindAll(); });\n  this.eventElements = [];\n};\n\nEventManager.prototype.once = function once (element, eventName, handler) {\n  var ee = this.eventElement(element);\n  var onceHandler = function (evt) {\n    ee.unbind(eventName, onceHandler);\n    handler(evt);\n  };\n  ee.bind(eventName, onceHandler);\n};\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  } else {\n    var evt = document.createEvent('CustomEvent');\n    evt.initCustomEvent(name, false, false, undefined);\n    return evt;\n  }\n}\n\nfunction processScrollDiff(\n  i,\n  axis,\n  diff,\n  useScrollingClass,\n  forceFireReachEvent\n) {\n  if ( useScrollingClass === void 0 ) useScrollingClass = true;\n  if ( forceFireReachEvent === void 0 ) forceFireReachEvent = false;\n\n  var fields;\n  if (axis === 'top') {\n    fields = [\n      'contentHeight',\n      'containerHeight',\n      'scrollTop',\n      'y',\n      'up',\n      'down' ];\n  } else if (axis === 'left') {\n    fields = [\n      'contentWidth',\n      'containerWidth',\n      'scrollLeft',\n      'x',\n      'left',\n      'right' ];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff$1(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff$1(\n  i,\n  diff,\n  ref,\n  useScrollingClass,\n  forceFireReachEvent\n) {\n  var contentHeight = ref[0];\n  var containerHeight = ref[1];\n  var scrollTop = ref[2];\n  var y = ref[3];\n  var up = ref[4];\n  var down = ref[5];\n  if ( useScrollingClass === void 0 ) useScrollingClass = true;\n  if ( forceFireReachEvent === void 0 ) forceFireReachEvent = false;\n\n  var element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent((\"ps-scroll-\" + y)));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent((\"ps-scroll-\" + up)));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent((\"ps-scroll-\" + down)));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent((\"ps-\" + y + \"-reach-\" + (i.reach[y]))));\n  }\n}\n\nfunction toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nfunction isEditable(el) {\n  return (\n    matches(el, 'input,[contenteditable]') ||\n    matches(el, 'select,[contenteditable]') ||\n    matches(el, 'textarea,[contenteditable]') ||\n    matches(el, 'button,[contenteditable]')\n  );\n}\n\nfunction outerWidth(element) {\n  var styles = get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nvar env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n\nfunction updateGeometry(i) {\n  var element = i.element;\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  var rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.ceil(rect.width);\n  i.containerHeight = Math.ceil(rect.height);\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('x')).forEach(function (el) { return remove(el); }\n    );\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('y')).forEach(function (el) { return remove(el); }\n    );\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(\n      i,\n      toInt((i.railXWidth * i.containerWidth) / i.contentWidth)\n    );\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) *\n        (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  var xRailOffset = { width: i.railXWidth };\n  var roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment +\n      element.scrollLeft +\n      i.containerWidth -\n      i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  set(i.scrollbarXRail, xRailOffset);\n\n  var yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  set(i.scrollbarYRail, yRailOffset);\n\n  set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n\nfunction clickRail(i) {\n  var element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', function (e) { return e.stopPropagation(); });\n  i.event.bind(i.scrollbarYRail, 'mousedown', function (e) {\n    var positionTop =\n      e.pageY -\n      window.pageYOffset -\n      i.scrollbarYRail.getBoundingClientRect().top;\n    var direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', function (e) { return e.stopPropagation(); });\n  i.event.bind(i.scrollbarXRail, 'mousedown', function (e) {\n    var positionLeft =\n      e.pageX -\n      window.pageXOffset -\n      i.scrollbarXRail.getBoundingClientRect().left;\n    var direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n\nfunction dragThumb(i) {\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail' ]);\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail' ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  ref\n) {\n  var containerHeight = ref[0];\n  var contentHeight = ref[1];\n  var pageY = ref[2];\n  var railYHeight = ref[3];\n  var scrollbarY = ref[4];\n  var scrollbarYHeight = ref[5];\n  var scrollTop = ref[6];\n  var y = ref[7];\n  var scrollbarYRail = ref[8];\n\n  var element = i.element;\n\n  var startingScrollTop = null;\n  var startingMousePageY = null;\n  var scrollBy = null;\n\n  function mouseMoveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    element[scrollTop] =\n      startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n    addScrollingClass(i, y);\n    updateGeometry(i);\n\n    e.stopPropagation();\n    e.preventDefault();\n  }\n\n  function mouseUpHandler() {\n    removeScrollingClass(i, y);\n    i[scrollbarYRail].classList.remove(cls.state.clicking);\n    i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n  }\n\n  function bindMoves(e, touchMode) {\n    startingScrollTop = element[scrollTop];\n    if (touchMode && e.touches) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    startingMousePageY = e[pageY];\n    scrollBy =\n      (i[contentHeight] - i[containerHeight]) /\n      (i[railYHeight] - i[scrollbarYHeight]);\n    if (!touchMode) {\n      i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n      i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n      e.preventDefault();\n    } else {\n      i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n    }\n\n    i[scrollbarYRail].classList.add(cls.state.clicking);\n\n    e.stopPropagation();\n  }\n\n  i.event.bind(i[scrollbarY], 'mousedown', function (e) {\n    bindMoves(e);\n  });\n  i.event.bind(i[scrollbarY], 'touchstart', function (e) {\n    bindMoves(e, true);\n  });\n}\n\nfunction keyboard(i) {\n  var element = i.element;\n\n  var elementHovered = function () { return matches(element, ':hover'); };\n  var scrollbarFocused = function () { return matches(i.scrollbarX, ':focus') || matches(i.scrollbarY, ':focus'); };\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    var scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', function (e) {\n    if (\n      (e.isDefaultPrevented && e.isDefaultPrevented()) ||\n      e.defaultPrevented\n    ) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    var activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    var deltaX = 0;\n    var deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n\nfunction wheel(i) {\n  var element = i.element;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    var roundedScrollTop = Math.floor(element.scrollTop);\n    var isTop = element.scrollTop === 0;\n    var isBottom =\n      roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    var isLeft = element.scrollLeft === 0;\n    var isRight =\n      element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    var hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    var deltaX = e.deltaX;\n    var deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    var cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    var ref = getDeltaFromEvent(e);\n    var deltaX = ref[0];\n    var deltaY = ref[1];\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    var shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n\nfunction touch(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  var element = i.element;\n\n  function shouldPrevent(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    var scrollLeft = element.scrollLeft;\n    var magnitudeX = Math.abs(deltaX);\n    var magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  var startOffset = {};\n  var startTime = 0;\n  var speed = {};\n  var easingLoop = null;\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    } else {\n      // Maybe IE pointer\n      return e;\n    }\n  }\n\n  function shouldHandle(e) {\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (\n      e.pointerType &&\n      e.pointerType !== 'mouse' &&\n      e.pointerType !== e.MSPOINTER_TYPE_MOUSE\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    var touch = getTouch(e);\n\n    startOffset.pageX = touch.pageX;\n    startOffset.pageY = touch.pageY;\n\n    startTime = new Date().getTime();\n\n    if (easingLoop !== null) {\n      clearInterval(easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    var cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      var touch = getTouch(e);\n\n      var currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      var differenceX = currentOffset.pageX - startOffset.pageX;\n      var differenceY = currentOffset.pageY - startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      startOffset = currentOffset;\n\n      var currentTime = new Date().getTime();\n\n      var timeGap = currentTime - startTime;\n      if (timeGap > 0) {\n        speed.x = differenceX / timeGap;\n        speed.y = differenceY / timeGap;\n        startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        e.preventDefault();\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(easingLoop);\n      easingLoop = setInterval(function() {\n        if (i.isInitialized) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (!speed.x && !speed.y) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        applyTouchMove(speed.x * 30, speed.y * 30);\n\n        speed.x *= 0.8;\n        speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n\nvar defaultSettings = function () { return ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n}); };\n\nvar handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard: keyboard,\n  wheel: wheel,\n  touch: touch,\n};\n\nvar PerfectScrollbar = function PerfectScrollbar(element, userSettings) {\n  var this$1 = this;\n  if ( userSettings === void 0 ) userSettings = {};\n\n  if (typeof element === 'string') {\n    element = document.querySelector(element);\n  }\n\n  if (!element || !element.nodeName) {\n    throw new Error('no element is specified to initialize PerfectScrollbar');\n  }\n\n  this.element = element;\n\n  element.classList.add(cls.main);\n\n  this.settings = defaultSettings();\n  for (var key in userSettings) {\n    this.settings[key] = userSettings[key];\n  }\n\n  this.containerWidth = null;\n  this.containerHeight = null;\n  this.contentWidth = null;\n  this.contentHeight = null;\n\n  var focus = function () { return element.classList.add(cls.state.focus); };\n  var blur = function () { return element.classList.remove(cls.state.focus); };\n\n  this.isRtl = get(element).direction === 'rtl';\n  if (this.isRtl === true) {\n    element.classList.add(cls.rtl);\n  }\n  this.isNegativeScroll = (function () {\n    var originalScrollLeft = element.scrollLeft;\n    var result = null;\n    element.scrollLeft = -1;\n    result = element.scrollLeft < 0;\n    element.scrollLeft = originalScrollLeft;\n    return result;\n  })();\n  this.negativeScrollAdjustment = this.isNegativeScroll\n    ? element.scrollWidth - element.clientWidth\n    : 0;\n  this.event = new EventManager();\n  this.ownerDocument = element.ownerDocument || document;\n\n  this.scrollbarXRail = div(cls.element.rail('x'));\n  element.appendChild(this.scrollbarXRail);\n  this.scrollbarX = div(cls.element.thumb('x'));\n  this.scrollbarXRail.appendChild(this.scrollbarX);\n  this.scrollbarX.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarX, 'focus', focus);\n  this.event.bind(this.scrollbarX, 'blur', blur);\n  this.scrollbarXActive = null;\n  this.scrollbarXWidth = null;\n  this.scrollbarXLeft = null;\n  var railXStyle = get(this.scrollbarXRail);\n  this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n  if (isNaN(this.scrollbarXBottom)) {\n    this.isScrollbarXUsingBottom = false;\n    this.scrollbarXTop = toInt(railXStyle.top);\n  } else {\n    this.isScrollbarXUsingBottom = true;\n  }\n  this.railBorderXWidth =\n    toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n  // Set rail to display:block to calculate margins\n  set(this.scrollbarXRail, { display: 'block' });\n  this.railXMarginWidth =\n    toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n  set(this.scrollbarXRail, { display: '' });\n  this.railXWidth = null;\n  this.railXRatio = null;\n\n  this.scrollbarYRail = div(cls.element.rail('y'));\n  element.appendChild(this.scrollbarYRail);\n  this.scrollbarY = div(cls.element.thumb('y'));\n  this.scrollbarYRail.appendChild(this.scrollbarY);\n  this.scrollbarY.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarY, 'focus', focus);\n  this.event.bind(this.scrollbarY, 'blur', blur);\n  this.scrollbarYActive = null;\n  this.scrollbarYHeight = null;\n  this.scrollbarYTop = null;\n  var railYStyle = get(this.scrollbarYRail);\n  this.scrollbarYRight = parseInt(railYStyle.right, 10);\n  if (isNaN(this.scrollbarYRight)) {\n    this.isScrollbarYUsingRight = false;\n    this.scrollbarYLeft = toInt(railYStyle.left);\n  } else {\n    this.isScrollbarYUsingRight = true;\n  }\n  this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n  this.railBorderYWidth =\n    toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n  set(this.scrollbarYRail, { display: 'block' });\n  this.railYMarginHeight =\n    toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n  set(this.scrollbarYRail, { display: '' });\n  this.railYHeight = null;\n  this.railYRatio = null;\n\n  this.reach = {\n    x:\n      element.scrollLeft <= 0\n        ? 'start'\n        : element.scrollLeft >= this.contentWidth - this.containerWidth\n        ? 'end'\n        : null,\n    y:\n      element.scrollTop <= 0\n        ? 'start'\n        : element.scrollTop >= this.contentHeight - this.containerHeight\n        ? 'end'\n        : null,\n  };\n\n  this.isAlive = true;\n\n  this.settings.handlers.forEach(function (handlerName) { return handlers[handlerName](this$1); });\n\n  this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n  this.lastScrollLeft = element.scrollLeft; // for onScroll only\n  this.event.bind(this.element, 'scroll', function (e) { return this$1.onScroll(e); });\n  updateGeometry(this);\n};\n\nPerfectScrollbar.prototype.update = function update () {\n  if (!this.isAlive) {\n    return;\n  }\n\n  // Recalcuate negative scrollLeft adjustment\n  this.negativeScrollAdjustment = this.isNegativeScroll\n    ? this.element.scrollWidth - this.element.clientWidth\n    : 0;\n\n  // Recalculate rail margins\n  set(this.scrollbarXRail, { display: 'block' });\n  set(this.scrollbarYRail, { display: 'block' });\n  this.railXMarginWidth =\n    toInt(get(this.scrollbarXRail).marginLeft) +\n    toInt(get(this.scrollbarXRail).marginRight);\n  this.railYMarginHeight =\n    toInt(get(this.scrollbarYRail).marginTop) +\n    toInt(get(this.scrollbarYRail).marginBottom);\n\n  // Hide scrollbars not to affect scrollWidth and scrollHeight\n  set(this.scrollbarXRail, { display: 'none' });\n  set(this.scrollbarYRail, { display: 'none' });\n\n  updateGeometry(this);\n\n  processScrollDiff(this, 'top', 0, false, true);\n  processScrollDiff(this, 'left', 0, false, true);\n\n  set(this.scrollbarXRail, { display: '' });\n  set(this.scrollbarYRail, { display: '' });\n};\n\nPerfectScrollbar.prototype.onScroll = function onScroll (e) {\n  if (!this.isAlive) {\n    return;\n  }\n\n  updateGeometry(this);\n  processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n  processScrollDiff(\n    this,\n    'left',\n    this.element.scrollLeft - this.lastScrollLeft\n  );\n\n  this.lastScrollTop = Math.floor(this.element.scrollTop);\n  this.lastScrollLeft = this.element.scrollLeft;\n};\n\nPerfectScrollbar.prototype.destroy = function destroy () {\n  if (!this.isAlive) {\n    return;\n  }\n\n  this.event.unbindAll();\n  remove(this.scrollbarX);\n  remove(this.scrollbarY);\n  remove(this.scrollbarXRail);\n  remove(this.scrollbarYRail);\n  this.removePsClasses();\n\n  // unset elements\n  this.element = null;\n  this.scrollbarX = null;\n  this.scrollbarY = null;\n  this.scrollbarXRail = null;\n  this.scrollbarYRail = null;\n\n  this.isAlive = false;\n};\n\nPerfectScrollbar.prototype.removePsClasses = function removePsClasses () {\n  this.element.className = this.element.className\n    .split(' ')\n    .filter(function (name) { return !name.match(/^ps([-_].+|)$/); })\n    .join(' ');\n};\n\nexport default PerfectScrollbar;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,OAAO,EAAE;EACpB,OAAOC,gBAAgB,CAACD,OAAO,CAAC;AAClC;AAEA,SAASE,GAAGA,CAACF,OAAO,EAAEG,GAAG,EAAE;EACzB,KAAK,IAAIC,GAAG,IAAID,GAAG,EAAE;IACnB,IAAIE,GAAG,GAAGF,GAAG,CAACC,GAAG,CAAC;IAClB,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;MAC3BA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClB;IACAL,OAAO,CAACM,KAAK,CAACF,GAAG,CAAC,GAAGC,GAAG;EAC1B;EACA,OAAOL,OAAO;AAChB;AAEA,SAASO,GAAGA,CAACC,SAAS,EAAE;EACtB,IAAID,GAAG,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACvCH,GAAG,CAACC,SAAS,GAAGA,SAAS;EACzB,OAAOD,GAAG;AACZ;AAEA,IAAII,SAAS,GACX,OAAOC,OAAO,KAAK,WAAW,KAC7BA,OAAO,CAACC,SAAS,CAACC,OAAO,IACxBF,OAAO,CAACC,SAAS,CAACE,qBAAqB,IACvCH,OAAO,CAACC,SAAS,CAACG,kBAAkB,IACpCJ,OAAO,CAACC,SAAS,CAACI,iBAAiB,CAAC;AAExC,SAASH,OAAOA,CAACd,OAAO,EAAEkB,KAAK,EAAE;EAC/B,IAAI,CAACP,SAAS,EAAE;IACd,MAAM,IAAIQ,KAAK,CAAC,sCAAsC,CAAC;EACzD;EAEA,OAAOR,SAAS,CAACS,IAAI,CAACpB,OAAO,EAAEkB,KAAK,CAAC;AACvC;AAEA,SAASG,MAAMA,CAACrB,OAAO,EAAE;EACvB,IAAIA,OAAO,CAACqB,MAAM,EAAE;IAClBrB,OAAO,CAACqB,MAAM,CAAC,CAAC;EAClB,CAAC,MAAM;IACL,IAAIrB,OAAO,CAACsB,UAAU,EAAE;MACtBtB,OAAO,CAACsB,UAAU,CAACC,WAAW,CAACvB,OAAO,CAAC;IACzC;EACF;AACF;AAEA,SAASwB,aAAaA,CAACxB,OAAO,EAAEyB,QAAQ,EAAE;EACxC,OAAOC,KAAK,CAACb,SAAS,CAACc,MAAM,CAACP,IAAI,CAACpB,OAAO,CAAC4B,QAAQ,EAAE,UAAUC,KAAK,EAAE;IAAE,OAAOf,OAAO,CAACe,KAAK,EAAEJ,QAAQ,CAAC;EAAE,CACzG,CAAC;AACH;AAEA,IAAIK,GAAG,GAAG;EACRC,IAAI,EAAE,IAAI;EACVC,GAAG,EAAE,SAAS;EACdhC,OAAO,EAAE;IACPiC,KAAK,EAAE,SAAAA,CAAUC,CAAC,EAAE;MAAE,OAAQ,YAAY,GAAGA,CAAC;IAAG,CAAC;IAClDC,IAAI,EAAE,SAAAA,CAAUD,CAAC,EAAE;MAAE,OAAQ,WAAW,GAAGA,CAAC;IAAG,CAAC;IAChDE,SAAS,EAAE;EACb,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,SAAAA,CAAUN,CAAC,EAAE;MAAE,OAAQ,aAAa,GAAGA,CAAC;IAAG,CAAC;IACpDO,SAAS,EAAE,SAAAA,CAAUP,CAAC,EAAE;MAAE,OAAQ,gBAAgB,GAAGA,CAAC;IAAG;EAC3D;AACF,CAAC;;AAED;AACA;AACA;AACA,IAAIQ,qBAAqB,GAAG;EAAER,CAAC,EAAE,IAAI;EAAES,CAAC,EAAE;AAAK,CAAC;AAEhD,SAASC,iBAAiBA,CAACC,CAAC,EAAEX,CAAC,EAAE;EAC/B,IAAIY,SAAS,GAAGD,CAAC,CAAC7C,OAAO,CAAC8C,SAAS;EACnC,IAAItC,SAAS,GAAGsB,GAAG,CAACO,KAAK,CAACI,SAAS,CAACP,CAAC,CAAC;EAEtC,IAAIY,SAAS,CAACC,QAAQ,CAACvC,SAAS,CAAC,EAAE;IACjCwC,YAAY,CAACN,qBAAqB,CAACR,CAAC,CAAC,CAAC;EACxC,CAAC,MAAM;IACLY,SAAS,CAACG,GAAG,CAACzC,SAAS,CAAC;EAC1B;AACF;AAEA,SAAS0C,oBAAoBA,CAACL,CAAC,EAAEX,CAAC,EAAE;EAClCQ,qBAAqB,CAACR,CAAC,CAAC,GAAGiB,UAAU,CACnC,YAAY;IAAE,OAAON,CAAC,CAACO,OAAO,IAAIP,CAAC,CAAC7C,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACI,SAAS,CAACP,CAAC,CAAC,CAAC;EAAE,CAAC,EACvFW,CAAC,CAACQ,QAAQ,CAACC,kBACb,CAAC;AACH;AAEA,SAASC,0BAA0BA,CAACV,CAAC,EAAEX,CAAC,EAAE;EACxCU,iBAAiB,CAACC,CAAC,EAAEX,CAAC,CAAC;EACvBgB,oBAAoB,CAACL,CAAC,EAAEX,CAAC,CAAC;AAC5B;AAEA,IAAIsB,YAAY,GAAG,SAASA,YAAYA,CAACxD,OAAO,EAAE;EAChD,IAAI,CAACA,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACyD,QAAQ,GAAG,CAAC,CAAC;AACpB,CAAC;AAED,IAAIC,kBAAkB,GAAG;EAAEC,OAAO,EAAE;IAAEC,YAAY,EAAE;EAAK;AAAE,CAAC;AAE5DJ,YAAY,CAAC3C,SAAS,CAACgD,IAAI,GAAG,SAASA,IAAIA,CAAEC,SAAS,EAAEC,OAAO,EAAE;EAC/D,IAAI,OAAO,IAAI,CAACN,QAAQ,CAACK,SAAS,CAAC,KAAK,WAAW,EAAE;IACnD,IAAI,CAACL,QAAQ,CAACK,SAAS,CAAC,GAAG,EAAE;EAC/B;EACA,IAAI,CAACL,QAAQ,CAACK,SAAS,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC;EACtC,IAAI,CAAC/D,OAAO,CAACiE,gBAAgB,CAACH,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;AAC1D,CAAC;AAEDP,YAAY,CAAC3C,SAAS,CAACqD,MAAM,GAAG,SAASA,MAAMA,CAAEJ,SAAS,EAAEK,MAAM,EAAE;EAChE,IAAIC,MAAM,GAAG,IAAI;EAEnB,IAAI,CAACX,QAAQ,CAACK,SAAS,CAAC,GAAG,IAAI,CAACL,QAAQ,CAACK,SAAS,CAAC,CAACnC,MAAM,CAAC,UAAUoC,OAAO,EAAE;IAC5E,IAAII,MAAM,IAAIJ,OAAO,KAAKI,MAAM,EAAE;MAChC,OAAO,IAAI;IACb;IACAC,MAAM,CAACpE,OAAO,CAACqE,mBAAmB,CAACP,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IAC7D,OAAO,KAAK;EACd,CAAC,CAAC;AACJ,CAAC;AAEDP,YAAY,CAAC3C,SAAS,CAACyD,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACvD,KAAK,IAAIC,IAAI,IAAI,IAAI,CAACd,QAAQ,EAAE;IAC9B,IAAI,CAACS,MAAM,CAACK,IAAI,CAAC;EACnB;AACF,CAAC;AAEDb,kBAAkB,CAACC,OAAO,CAAC5D,GAAG,GAAG,YAAY;EACzC,IAAIqE,MAAM,GAAG,IAAI;EAEnB,OAAOI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAACiB,KAAK,CACrC,UAAUtE,GAAG,EAAE;IAAE,OAAOgE,MAAM,CAACX,QAAQ,CAACrD,GAAG,CAAC,CAACuE,MAAM,KAAK,CAAC;EAAE,CAC7D,CAAC;AACH,CAAC;AAEDH,MAAM,CAACI,gBAAgB,CAAEpB,YAAY,CAAC3C,SAAS,EAAE6C,kBAAmB,CAAC;AAErE,IAAImB,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EACzC,IAAI,CAACC,aAAa,GAAG,EAAE;AACzB,CAAC;AAEDD,YAAY,CAAChE,SAAS,CAACkE,YAAY,GAAG,SAASA,YAAYA,CAAE/E,OAAO,EAAE;EACpE,IAAIgF,EAAE,GAAG,IAAI,CAACF,aAAa,CAACnD,MAAM,CAAC,UAAUqD,EAAE,EAAE;IAAE,OAAOA,EAAE,CAAChF,OAAO,KAAKA,OAAO;EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,IAAI,CAACgF,EAAE,EAAE;IACPA,EAAE,GAAG,IAAIxB,YAAY,CAACxD,OAAO,CAAC;IAC9B,IAAI,CAAC8E,aAAa,CAACd,IAAI,CAACgB,EAAE,CAAC;EAC7B;EACA,OAAOA,EAAE;AACX,CAAC;AAEDH,YAAY,CAAChE,SAAS,CAACgD,IAAI,GAAG,SAASA,IAAIA,CAAE7D,OAAO,EAAE8D,SAAS,EAAEC,OAAO,EAAE;EACxE,IAAI,CAACgB,YAAY,CAAC/E,OAAO,CAAC,CAAC6D,IAAI,CAACC,SAAS,EAAEC,OAAO,CAAC;AACrD,CAAC;AAEDc,YAAY,CAAChE,SAAS,CAACqD,MAAM,GAAG,SAASA,MAAMA,CAAElE,OAAO,EAAE8D,SAAS,EAAEC,OAAO,EAAE;EAC5E,IAAIiB,EAAE,GAAG,IAAI,CAACD,YAAY,CAAC/E,OAAO,CAAC;EACnCgF,EAAE,CAACd,MAAM,CAACJ,SAAS,EAAEC,OAAO,CAAC;EAE7B,IAAIiB,EAAE,CAACrB,OAAO,EAAE;IACd;IACA,IAAI,CAACmB,aAAa,CAACG,MAAM,CAAC,IAAI,CAACH,aAAa,CAACI,OAAO,CAACF,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9D;AACF,CAAC;AAEDH,YAAY,CAAChE,SAAS,CAACyD,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACvD,IAAI,CAACQ,aAAa,CAACK,OAAO,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACd,SAAS,CAAC,CAAC;EAAE,CAAC,CAAC;EAClE,IAAI,CAACQ,aAAa,GAAG,EAAE;AACzB,CAAC;AAEDD,YAAY,CAAChE,SAAS,CAACwE,IAAI,GAAG,SAASA,IAAIA,CAAErF,OAAO,EAAE8D,SAAS,EAAEC,OAAO,EAAE;EACxE,IAAIiB,EAAE,GAAG,IAAI,CAACD,YAAY,CAAC/E,OAAO,CAAC;EACnC,IAAIsF,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC/BP,EAAE,CAACd,MAAM,CAACJ,SAAS,EAAEwB,WAAW,CAAC;IACjCvB,OAAO,CAACwB,GAAG,CAAC;EACd,CAAC;EACDP,EAAE,CAACnB,IAAI,CAACC,SAAS,EAAEwB,WAAW,CAAC;AACjC,CAAC;AAED,SAASE,WAAWA,CAACjB,IAAI,EAAE;EACzB,IAAI,OAAOkB,MAAM,CAACC,WAAW,KAAK,UAAU,EAAE;IAC5C,OAAO,IAAIA,WAAW,CAACnB,IAAI,CAAC;EAC9B,CAAC,MAAM;IACL,IAAIgB,GAAG,GAAG9E,QAAQ,CAAC+E,WAAW,CAAC,aAAa,CAAC;IAC7CD,GAAG,CAACI,eAAe,CAACpB,IAAI,EAAE,KAAK,EAAE,KAAK,EAAEqB,SAAS,CAAC;IAClD,OAAOL,GAAG;EACZ;AACF;AAEA,SAASM,iBAAiBA,CACxBhD,CAAC,EACDiD,IAAI,EACJC,IAAI,EACJC,iBAAiB,EACjBC,mBAAmB,EACnB;EACA,IAAKD,iBAAiB,KAAK,KAAK,CAAC,EAAGA,iBAAiB,GAAG,IAAI;EAC5D,IAAKC,mBAAmB,KAAK,KAAK,CAAC,EAAGA,mBAAmB,GAAG,KAAK;EAEjE,IAAIC,MAAM;EACV,IAAIJ,IAAI,KAAK,KAAK,EAAE;IAClBI,MAAM,GAAG,CACP,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,GAAG,EACH,IAAI,EACJ,MAAM,CAAE;EACZ,CAAC,MAAM,IAAIJ,IAAI,KAAK,MAAM,EAAE;IAC1BI,MAAM,GAAG,CACP,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,GAAG,EACH,MAAM,EACN,OAAO,CAAE;EACb,CAAC,MAAM;IACL,MAAM,IAAI/E,KAAK,CAAC,kCAAkC,CAAC;EACrD;EAEAgF,mBAAmB,CAACtD,CAAC,EAAEkD,IAAI,EAAEG,MAAM,EAAEF,iBAAiB,EAAEC,mBAAmB,CAAC;AAC9E;AAEA,SAASE,mBAAmBA,CAC1BtD,CAAC,EACDkD,IAAI,EACJK,GAAG,EACHJ,iBAAiB,EACjBC,mBAAmB,EACnB;EACA,IAAII,aAAa,GAAGD,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAIE,eAAe,GAAGF,GAAG,CAAC,CAAC,CAAC;EAC5B,IAAIG,SAAS,GAAGH,GAAG,CAAC,CAAC,CAAC;EACtB,IAAIzD,CAAC,GAAGyD,GAAG,CAAC,CAAC,CAAC;EACd,IAAII,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC;EACf,IAAIK,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC;EACjB,IAAKJ,iBAAiB,KAAK,KAAK,CAAC,EAAGA,iBAAiB,GAAG,IAAI;EAC5D,IAAKC,mBAAmB,KAAK,KAAK,CAAC,EAAGA,mBAAmB,GAAG,KAAK;EAEjE,IAAIjG,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;;EAEvB;EACA6C,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,GAAG,IAAI;;EAEjB;EACA,IAAI3C,OAAO,CAACuG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC1B1D,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,GAAG,OAAO;EACtB;;EAEA;EACA,IAAI3C,OAAO,CAACuG,SAAS,CAAC,GAAG1D,CAAC,CAACwD,aAAa,CAAC,GAAGxD,CAAC,CAACyD,eAAe,CAAC,GAAG,CAAC,EAAE;IAClEzD,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,GAAG,KAAK;EACpB;EAEA,IAAIoD,IAAI,EAAE;IACR/F,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,YAAY,GAAG7C,CAAE,CAAC,CAAC;IAEtD,IAAIoD,IAAI,GAAG,CAAC,EAAE;MACZ/F,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,YAAY,GAAGgB,EAAG,CAAC,CAAC;IACzD,CAAC,MAAM,IAAIT,IAAI,GAAG,CAAC,EAAE;MACnB/F,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,YAAY,GAAGiB,IAAK,CAAC,CAAC;IAC3D;IAEA,IAAIT,iBAAiB,EAAE;MACrBzC,0BAA0B,CAACV,CAAC,EAAEF,CAAC,CAAC;IAClC;EACF;EAEA,IAAIE,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,KAAKoD,IAAI,IAAIE,mBAAmB,CAAC,EAAE;IAC/CjG,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,KAAK,GAAG7C,CAAC,GAAG,SAAS,GAAIE,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAG,CAAC,CAAC;EAC5E;AACF;AAEA,SAASiE,KAAKA,CAAC1E,CAAC,EAAE;EAChB,OAAO2E,QAAQ,CAAC3E,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;AAC7B;AAEA,SAAS4E,UAAUA,CAACC,EAAE,EAAE;EACtB,OACEjG,OAAO,CAACiG,EAAE,EAAE,yBAAyB,CAAC,IACtCjG,OAAO,CAACiG,EAAE,EAAE,0BAA0B,CAAC,IACvCjG,OAAO,CAACiG,EAAE,EAAE,4BAA4B,CAAC,IACzCjG,OAAO,CAACiG,EAAE,EAAE,0BAA0B,CAAC;AAE3C;AAEA,SAASC,UAAUA,CAAChH,OAAO,EAAE;EAC3B,IAAIiH,MAAM,GAAGlH,GAAG,CAACC,OAAO,CAAC;EACzB,OACE4G,KAAK,CAACK,MAAM,CAACC,KAAK,CAAC,GACnBN,KAAK,CAACK,MAAM,CAACE,WAAW,CAAC,GACzBP,KAAK,CAACK,MAAM,CAACG,YAAY,CAAC,GAC1BR,KAAK,CAACK,MAAM,CAACI,eAAe,CAAC,GAC7BT,KAAK,CAACK,MAAM,CAACK,gBAAgB,CAAC;AAElC;AAEA,IAAIC,GAAG,GAAG;EACRC,QAAQ,EACN,OAAO/G,QAAQ,KAAK,WAAW,IAC/B,kBAAkB,IAAIA,QAAQ,CAACgH,eAAe,CAACnH,KAAK;EACtDoH,aAAa,EACX,OAAOjC,MAAM,KAAK,WAAW,KAC5B,cAAc,IAAIA,MAAM,IACtB,gBAAgB,IAAIA,MAAM,CAACkC,SAAS,IACnClC,MAAM,CAACkC,SAAS,CAACC,cAAc,GAAG,CAAE,IACrCnC,MAAM,CAACoC,aAAa,IAAIpH,QAAQ,YAAYgF,MAAM,CAACoC,aAAc,CAAC;EACvEC,iBAAiB,EACf,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACI,gBAAgB;EAChEC,QAAQ,EACN,OAAOL,SAAS,KAAK,WAAW,IAChC,SAAS,CAACM,IAAI,CAACN,SAAS,IAAIA,SAAS,CAACO,SAAS;AACnD,CAAC;AAED,SAASC,cAAcA,CAACtF,CAAC,EAAE;EACzB,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EACvB,IAAIoI,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;EACpD,IAAIgC,IAAI,GAAGvI,OAAO,CAACwI,qBAAqB,CAAC,CAAC;EAE1C3F,CAAC,CAAC4F,cAAc,GAAGJ,IAAI,CAACK,IAAI,CAACH,IAAI,CAACrB,KAAK,CAAC;EACxCrE,CAAC,CAACyD,eAAe,GAAG+B,IAAI,CAACK,IAAI,CAACH,IAAI,CAACI,MAAM,CAAC;EAC1C9F,CAAC,CAAC+F,YAAY,GAAG5I,OAAO,CAAC6I,WAAW;EACpChG,CAAC,CAACwD,aAAa,GAAGrG,OAAO,CAAC8I,YAAY;EAEtC,IAAI,CAAC9I,OAAO,CAAC+C,QAAQ,CAACF,CAAC,CAACkG,cAAc,CAAC,EAAE;IACvC;IACAvH,aAAa,CAACxB,OAAO,EAAE8B,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACgD,OAAO,CAAC,UAAU4B,EAAE,EAAE;MAAE,OAAO1F,MAAM,CAAC0F,EAAE,CAAC;IAAE,CACzF,CAAC;IACD/G,OAAO,CAACgJ,WAAW,CAACnG,CAAC,CAACkG,cAAc,CAAC;EACvC;EACA,IAAI,CAAC/I,OAAO,CAAC+C,QAAQ,CAACF,CAAC,CAACoG,cAAc,CAAC,EAAE;IACvC;IACAzH,aAAa,CAACxB,OAAO,EAAE8B,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACgD,OAAO,CAAC,UAAU4B,EAAE,EAAE;MAAE,OAAO1F,MAAM,CAAC0F,EAAE,CAAC;IAAE,CACzF,CAAC;IACD/G,OAAO,CAACgJ,WAAW,CAACnG,CAAC,CAACoG,cAAc,CAAC;EACvC;EAEA,IACE,CAACpG,CAAC,CAACQ,QAAQ,CAAC6F,eAAe,IAC3BrG,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAACQ,QAAQ,CAAC8F,mBAAmB,GAAGtG,CAAC,CAAC+F,YAAY,EAClE;IACA/F,CAAC,CAACuG,gBAAgB,GAAG,IAAI;IACzBvG,CAAC,CAACwG,UAAU,GAAGxG,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAACyG,gBAAgB;IACpDzG,CAAC,CAAC0G,UAAU,GAAG1G,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAACwG,UAAU;IAC9CxG,CAAC,CAAC2G,eAAe,GAAGC,YAAY,CAC9B5G,CAAC,EACD+D,KAAK,CAAE/D,CAAC,CAACwG,UAAU,GAAGxG,CAAC,CAAC4F,cAAc,GAAI5F,CAAC,CAAC+F,YAAY,CAC1D,CAAC;IACD/F,CAAC,CAAC6G,cAAc,GAAG9C,KAAK,CACrB,CAAC/D,CAAC,CAAC8G,wBAAwB,GAAG3J,OAAO,CAAC4J,UAAU,KAC9C/G,CAAC,CAACwG,UAAU,GAAGxG,CAAC,CAAC2G,eAAe,CAAC,IACjC3G,CAAC,CAAC+F,YAAY,GAAG/F,CAAC,CAAC4F,cAAc,CACtC,CAAC;EACH,CAAC,MAAM;IACL5F,CAAC,CAACuG,gBAAgB,GAAG,KAAK;EAC5B;EAEA,IACE,CAACvG,CAAC,CAACQ,QAAQ,CAACwG,eAAe,IAC3BhH,CAAC,CAACyD,eAAe,GAAGzD,CAAC,CAACQ,QAAQ,CAACyG,mBAAmB,GAAGjH,CAAC,CAACwD,aAAa,EACpE;IACAxD,CAAC,CAACkH,gBAAgB,GAAG,IAAI;IACzBlH,CAAC,CAACmH,WAAW,GAAGnH,CAAC,CAACyD,eAAe,GAAGzD,CAAC,CAACoH,iBAAiB;IACvDpH,CAAC,CAACqH,UAAU,GAAGrH,CAAC,CAACyD,eAAe,GAAGzD,CAAC,CAACmH,WAAW;IAChDnH,CAAC,CAACsH,gBAAgB,GAAGV,YAAY,CAC/B5G,CAAC,EACD+D,KAAK,CAAE/D,CAAC,CAACmH,WAAW,GAAGnH,CAAC,CAACyD,eAAe,GAAIzD,CAAC,CAACwD,aAAa,CAC7D,CAAC;IACDxD,CAAC,CAACuH,aAAa,GAAGxD,KAAK,CACpBwB,gBAAgB,IAAIvF,CAAC,CAACmH,WAAW,GAAGnH,CAAC,CAACsH,gBAAgB,CAAC,IACrDtH,CAAC,CAACwD,aAAa,GAAGxD,CAAC,CAACyD,eAAe,CACxC,CAAC;EACH,CAAC,MAAM;IACLzD,CAAC,CAACkH,gBAAgB,GAAG,KAAK;EAC5B;EAEA,IAAIlH,CAAC,CAAC6G,cAAc,IAAI7G,CAAC,CAACwG,UAAU,GAAGxG,CAAC,CAAC2G,eAAe,EAAE;IACxD3G,CAAC,CAAC6G,cAAc,GAAG7G,CAAC,CAACwG,UAAU,GAAGxG,CAAC,CAAC2G,eAAe;EACrD;EACA,IAAI3G,CAAC,CAACuH,aAAa,IAAIvH,CAAC,CAACmH,WAAW,GAAGnH,CAAC,CAACsH,gBAAgB,EAAE;IACzDtH,CAAC,CAACuH,aAAa,GAAGvH,CAAC,CAACmH,WAAW,GAAGnH,CAAC,CAACsH,gBAAgB;EACtD;EAEAE,SAAS,CAACrK,OAAO,EAAE6C,CAAC,CAAC;EAErB,IAAIA,CAAC,CAACuG,gBAAgB,EAAE;IACtBpJ,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLxC,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/CK,CAAC,CAAC2G,eAAe,GAAG,CAAC;IACrB3G,CAAC,CAAC6G,cAAc,GAAG,CAAC;IACpB1J,OAAO,CAAC4J,UAAU,GAAG/G,CAAC,CAACyH,KAAK,KAAK,IAAI,GAAGzH,CAAC,CAAC+F,YAAY,GAAG,CAAC;EAC5D;EACA,IAAI/F,CAAC,CAACkH,gBAAgB,EAAE;IACtB/J,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLxC,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/CK,CAAC,CAACsH,gBAAgB,GAAG,CAAC;IACtBtH,CAAC,CAACuH,aAAa,GAAG,CAAC;IACnBpK,OAAO,CAACuG,SAAS,GAAG,CAAC;EACvB;AACF;AAEA,SAASkD,YAAYA,CAAC5G,CAAC,EAAE0H,SAAS,EAAE;EAClC,IAAI1H,CAAC,CAACQ,QAAQ,CAACmH,kBAAkB,EAAE;IACjCD,SAAS,GAAGlC,IAAI,CAACoC,GAAG,CAACF,SAAS,EAAE1H,CAAC,CAACQ,QAAQ,CAACmH,kBAAkB,CAAC;EAChE;EACA,IAAI3H,CAAC,CAACQ,QAAQ,CAACqH,kBAAkB,EAAE;IACjCH,SAAS,GAAGlC,IAAI,CAACsC,GAAG,CAACJ,SAAS,EAAE1H,CAAC,CAACQ,QAAQ,CAACqH,kBAAkB,CAAC;EAChE;EACA,OAAOH,SAAS;AAClB;AAEA,SAASF,SAASA,CAACrK,OAAO,EAAE6C,CAAC,EAAE;EAC7B,IAAI+H,WAAW,GAAG;IAAE1D,KAAK,EAAErE,CAAC,CAACwG;EAAW,CAAC;EACzC,IAAIjB,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;EAEpD,IAAI1D,CAAC,CAACyH,KAAK,EAAE;IACXM,WAAW,CAACC,IAAI,GACdhI,CAAC,CAAC8G,wBAAwB,GAC1B3J,OAAO,CAAC4J,UAAU,GAClB/G,CAAC,CAAC4F,cAAc,GAChB5F,CAAC,CAAC+F,YAAY;EAClB,CAAC,MAAM;IACLgC,WAAW,CAACC,IAAI,GAAG7K,OAAO,CAAC4J,UAAU;EACvC;EACA,IAAI/G,CAAC,CAACiI,uBAAuB,EAAE;IAC7BF,WAAW,CAACG,MAAM,GAAGlI,CAAC,CAACmI,gBAAgB,GAAG5C,gBAAgB;EAC5D,CAAC,MAAM;IACLwC,WAAW,CAACK,GAAG,GAAGpI,CAAC,CAACqI,aAAa,GAAG9C,gBAAgB;EACtD;EACAlI,GAAG,CAAC2C,CAAC,CAACkG,cAAc,EAAE6B,WAAW,CAAC;EAElC,IAAIO,WAAW,GAAG;IAAEF,GAAG,EAAE7C,gBAAgB;IAAEO,MAAM,EAAE9F,CAAC,CAACmH;EAAY,CAAC;EAClE,IAAInH,CAAC,CAACuI,sBAAsB,EAAE;IAC5B,IAAIvI,CAAC,CAACyH,KAAK,EAAE;MACXa,WAAW,CAACE,KAAK,GACfxI,CAAC,CAAC+F,YAAY,IACb/F,CAAC,CAAC8G,wBAAwB,GAAG3J,OAAO,CAAC4J,UAAU,CAAC,GACjD/G,CAAC,CAACyI,eAAe,GACjBzI,CAAC,CAAC0I,oBAAoB,GACtB,CAAC;IACL,CAAC,MAAM;MACLJ,WAAW,CAACE,KAAK,GAAGxI,CAAC,CAACyI,eAAe,GAAGtL,OAAO,CAAC4J,UAAU;IAC5D;EACF,CAAC,MAAM;IACL,IAAI/G,CAAC,CAACyH,KAAK,EAAE;MACXa,WAAW,CAACN,IAAI,GACdhI,CAAC,CAAC8G,wBAAwB,GAC1B3J,OAAO,CAAC4J,UAAU,GAClB/G,CAAC,CAAC4F,cAAc,GAAG,CAAC,GACpB5F,CAAC,CAAC+F,YAAY,GACd/F,CAAC,CAAC2I,cAAc,GAChB3I,CAAC,CAAC0I,oBAAoB;IAC1B,CAAC,MAAM;MACLJ,WAAW,CAACN,IAAI,GAAGhI,CAAC,CAAC2I,cAAc,GAAGxL,OAAO,CAAC4J,UAAU;IAC1D;EACF;EACA1J,GAAG,CAAC2C,CAAC,CAACoG,cAAc,EAAEkC,WAAW,CAAC;EAElCjL,GAAG,CAAC2C,CAAC,CAAC4I,UAAU,EAAE;IAChBZ,IAAI,EAAEhI,CAAC,CAAC6G,cAAc;IACtBxC,KAAK,EAAErE,CAAC,CAAC2G,eAAe,GAAG3G,CAAC,CAAC6I;EAC/B,CAAC,CAAC;EACFxL,GAAG,CAAC2C,CAAC,CAAC8I,UAAU,EAAE;IAChBV,GAAG,EAAEpI,CAAC,CAACuH,aAAa;IACpBzB,MAAM,EAAE9F,CAAC,CAACsH,gBAAgB,GAAGtH,CAAC,CAAC+I;EACjC,CAAC,CAAC;AACJ;AAEA,SAASC,SAASA,CAAChJ,CAAC,EAAE;EACpB,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB6C,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAAC8I,UAAU,EAAE,WAAW,EAAE,UAAUvG,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC2G,eAAe,CAAC,CAAC;EAAE,CAAC,CAAC;EACrFlJ,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAACoG,cAAc,EAAE,WAAW,EAAE,UAAU7D,CAAC,EAAE;IACvD,IAAI4G,WAAW,GACb5G,CAAC,CAAC6G,KAAK,GACPxG,MAAM,CAACyG,WAAW,GAClBrJ,CAAC,CAACoG,cAAc,CAACT,qBAAqB,CAAC,CAAC,CAACyC,GAAG;IAC9C,IAAIkB,SAAS,GAAGH,WAAW,GAAGnJ,CAAC,CAACuH,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAEtDvH,CAAC,CAAC7C,OAAO,CAACuG,SAAS,IAAI4F,SAAS,GAAGtJ,CAAC,CAACyD,eAAe;IACpD6B,cAAc,CAACtF,CAAC,CAAC;IAEjBuC,CAAC,CAAC2G,eAAe,CAAC,CAAC;EACrB,CAAC,CAAC;EAEFlJ,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAAC4I,UAAU,EAAE,WAAW,EAAE,UAAUrG,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC2G,eAAe,CAAC,CAAC;EAAE,CAAC,CAAC;EACrFlJ,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAACkG,cAAc,EAAE,WAAW,EAAE,UAAU3D,CAAC,EAAE;IACvD,IAAIgH,YAAY,GACdhH,CAAC,CAACiH,KAAK,GACP5G,MAAM,CAAC6G,WAAW,GAClBzJ,CAAC,CAACkG,cAAc,CAACP,qBAAqB,CAAC,CAAC,CAACqC,IAAI;IAC/C,IAAIsB,SAAS,GAAGC,YAAY,GAAGvJ,CAAC,CAAC6G,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;IAExD7G,CAAC,CAAC7C,OAAO,CAAC4J,UAAU,IAAIuC,SAAS,GAAGtJ,CAAC,CAAC4F,cAAc;IACpDN,cAAc,CAACtF,CAAC,CAAC;IAEjBuC,CAAC,CAAC2G,eAAe,CAAC,CAAC;EACrB,CAAC,CAAC;AACJ;AAEA,SAASQ,SAASA,CAAC1J,CAAC,EAAE;EACpB2J,sBAAsB,CAAC3J,CAAC,EAAE,CACxB,gBAAgB,EAChB,cAAc,EACd,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,YAAY,EACZ,GAAG,EACH,gBAAgB,CAAE,CAAC;EACrB2J,sBAAsB,CAAC3J,CAAC,EAAE,CACxB,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,aAAa,EACb,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,GAAG,EACH,gBAAgB,CAAE,CAAC;AACvB;AAEA,SAAS2J,sBAAsBA,CAC7B3J,CAAC,EACDuD,GAAG,EACH;EACA,IAAIE,eAAe,GAAGF,GAAG,CAAC,CAAC,CAAC;EAC5B,IAAIC,aAAa,GAAGD,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAI6F,KAAK,GAAG7F,GAAG,CAAC,CAAC,CAAC;EAClB,IAAI4D,WAAW,GAAG5D,GAAG,CAAC,CAAC,CAAC;EACxB,IAAIuF,UAAU,GAAGvF,GAAG,CAAC,CAAC,CAAC;EACvB,IAAI+D,gBAAgB,GAAG/D,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAIG,SAAS,GAAGH,GAAG,CAAC,CAAC,CAAC;EACtB,IAAIzD,CAAC,GAAGyD,GAAG,CAAC,CAAC,CAAC;EACd,IAAI6C,cAAc,GAAG7C,GAAG,CAAC,CAAC,CAAC;EAE3B,IAAIpG,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,IAAIyM,iBAAiB,GAAG,IAAI;EAC5B,IAAIC,kBAAkB,GAAG,IAAI;EAC7B,IAAIC,QAAQ,GAAG,IAAI;EAEnB,SAASC,gBAAgBA,CAACxH,CAAC,EAAE;IAC3B,IAAIA,CAAC,CAACyH,OAAO,IAAIzH,CAAC,CAACyH,OAAO,CAAC,CAAC,CAAC,EAAE;MAC7BzH,CAAC,CAAC6G,KAAK,CAAC,GAAG7G,CAAC,CAACyH,OAAO,CAAC,CAAC,CAAC,CAACZ,KAAK;IAC/B;IACAjM,OAAO,CAACuG,SAAS,CAAC,GAChBkG,iBAAiB,GAAGE,QAAQ,IAAIvH,CAAC,CAAC6G,KAAK,CAAC,GAAGS,kBAAkB,CAAC;IAChE9J,iBAAiB,CAACC,CAAC,EAAEF,CAAC,CAAC;IACvBwF,cAAc,CAACtF,CAAC,CAAC;IAEjBuC,CAAC,CAAC2G,eAAe,CAAC,CAAC;IACnB3G,CAAC,CAAC0H,cAAc,CAAC,CAAC;EACpB;EAEA,SAASC,cAAcA,CAAA,EAAG;IACxB7J,oBAAoB,CAACL,CAAC,EAAEF,CAAC,CAAC;IAC1BE,CAAC,CAACoG,cAAc,CAAC,CAACnG,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACE,QAAQ,CAAC;IACtDM,CAAC,CAACiJ,KAAK,CAAC5H,MAAM,CAACrB,CAAC,CAACmK,aAAa,EAAE,WAAW,EAAEJ,gBAAgB,CAAC;EAChE;EAEA,SAASK,SAASA,CAAC7H,CAAC,EAAE8H,SAAS,EAAE;IAC/BT,iBAAiB,GAAGzM,OAAO,CAACuG,SAAS,CAAC;IACtC,IAAI2G,SAAS,IAAI9H,CAAC,CAACyH,OAAO,EAAE;MAC1BzH,CAAC,CAAC6G,KAAK,CAAC,GAAG7G,CAAC,CAACyH,OAAO,CAAC,CAAC,CAAC,CAACZ,KAAK;IAC/B;IACAS,kBAAkB,GAAGtH,CAAC,CAAC6G,KAAK,CAAC;IAC7BU,QAAQ,GACN,CAAC9J,CAAC,CAACwD,aAAa,CAAC,GAAGxD,CAAC,CAACyD,eAAe,CAAC,KACrCzD,CAAC,CAACmH,WAAW,CAAC,GAAGnH,CAAC,CAACsH,gBAAgB,CAAC,CAAC;IACxC,IAAI,CAAC+C,SAAS,EAAE;MACdrK,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAACmK,aAAa,EAAE,WAAW,EAAEJ,gBAAgB,CAAC;MAC5D/J,CAAC,CAACiJ,KAAK,CAACzG,IAAI,CAACxC,CAAC,CAACmK,aAAa,EAAE,SAAS,EAAED,cAAc,CAAC;MACxD3H,CAAC,CAAC0H,cAAc,CAAC,CAAC;IACpB,CAAC,MAAM;MACLjK,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAACmK,aAAa,EAAE,WAAW,EAAEJ,gBAAgB,CAAC;IAC9D;IAEA/J,CAAC,CAACoG,cAAc,CAAC,CAACnG,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACE,QAAQ,CAAC;IAEnD6C,CAAC,CAAC2G,eAAe,CAAC,CAAC;EACrB;EAEAlJ,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAAC8I,UAAU,CAAC,EAAE,WAAW,EAAE,UAAUvG,CAAC,EAAE;IACpD6H,SAAS,CAAC7H,CAAC,CAAC;EACd,CAAC,CAAC;EACFvC,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAAC8I,UAAU,CAAC,EAAE,YAAY,EAAE,UAAUvG,CAAC,EAAE;IACrD6H,SAAS,CAAC7H,CAAC,EAAE,IAAI,CAAC;EACpB,CAAC,CAAC;AACJ;AAEA,SAAS+H,QAAQA,CAACtK,CAAC,EAAE;EACnB,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,IAAIoN,cAAc,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOtM,OAAO,CAACd,OAAO,EAAE,QAAQ,CAAC;EAAE,CAAC;EACvE,IAAIqN,gBAAgB,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOvM,OAAO,CAAC+B,CAAC,CAAC4I,UAAU,EAAE,QAAQ,CAAC,IAAI3K,OAAO,CAAC+B,CAAC,CAAC8I,UAAU,EAAE,QAAQ,CAAC;EAAE,CAAC;EAEjH,SAAS2B,oBAAoBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC5C,IAAIjH,SAAS,GAAG8B,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;IAC7C,IAAIgH,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAAC1K,CAAC,CAACkH,gBAAgB,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IACGxD,SAAS,KAAK,CAAC,IAAIiH,MAAM,GAAG,CAAC,IAC7BjH,SAAS,IAAI1D,CAAC,CAACwD,aAAa,GAAGxD,CAAC,CAACyD,eAAe,IAAIkH,MAAM,GAAG,CAAE,EAChE;QACA,OAAO,CAAC3K,CAAC,CAACQ,QAAQ,CAACoK,gBAAgB;MACrC;IACF;IAEA,IAAI7D,UAAU,GAAG5J,OAAO,CAAC4J,UAAU;IACnC,IAAI4D,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAAC3K,CAAC,CAACuG,gBAAgB,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IACGQ,UAAU,KAAK,CAAC,IAAI2D,MAAM,GAAG,CAAC,IAC9B3D,UAAU,IAAI/G,CAAC,CAAC+F,YAAY,GAAG/F,CAAC,CAAC4F,cAAc,IAAI8E,MAAM,GAAG,CAAE,EAC/D;QACA,OAAO,CAAC1K,CAAC,CAACQ,QAAQ,CAACoK,gBAAgB;MACrC;IACF;IACA,OAAO,IAAI;EACb;EAEA5K,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAChB,CAAC,CAACmK,aAAa,EAAE,SAAS,EAAE,UAAU5H,CAAC,EAAE;IACpD,IACGA,CAAC,CAACsI,kBAAkB,IAAItI,CAAC,CAACsI,kBAAkB,CAAC,CAAC,IAC/CtI,CAAC,CAACuI,gBAAgB,EAClB;MACA;IACF;IAEA,IAAI,CAACP,cAAc,CAAC,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAE;MAC5C;IACF;IAEA,IAAIO,aAAa,GAAGnN,QAAQ,CAACmN,aAAa,GACtCnN,QAAQ,CAACmN,aAAa,GACtB/K,CAAC,CAACmK,aAAa,CAACY,aAAa;IACjC,IAAIA,aAAa,EAAE;MACjB,IAAIA,aAAa,CAACC,OAAO,KAAK,QAAQ,EAAE;QACtCD,aAAa,GAAGA,aAAa,CAACE,eAAe,CAACF,aAAa;MAC7D,CAAC,MAAM;QACL;QACA,OAAOA,aAAa,CAACG,UAAU,EAAE;UAC/BH,aAAa,GAAGA,aAAa,CAACG,UAAU,CAACH,aAAa;QACxD;MACF;MACA,IAAI9G,UAAU,CAAC8G,aAAa,CAAC,EAAE;QAC7B;MACF;IACF;IAEA,IAAIL,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;IAEd,QAAQpI,CAAC,CAAC4I,KAAK;MACb,KAAK,EAAE;QAAE;QACP,IAAI5I,CAAC,CAAC6I,OAAO,EAAE;UACbV,MAAM,GAAG,CAAC1K,CAAC,CAAC+F,YAAY;QAC1B,CAAC,MAAM,IAAIxD,CAAC,CAAC8I,MAAM,EAAE;UACnBX,MAAM,GAAG,CAAC1K,CAAC,CAAC4F,cAAc;QAC5B,CAAC,MAAM;UACL8E,MAAM,GAAG,CAAC,EAAE;QACd;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAInI,CAAC,CAAC6I,OAAO,EAAE;UACbT,MAAM,GAAG3K,CAAC,CAACwD,aAAa;QAC1B,CAAC,MAAM,IAAIjB,CAAC,CAAC8I,MAAM,EAAE;UACnBV,MAAM,GAAG3K,CAAC,CAACyD,eAAe;QAC5B,CAAC,MAAM;UACLkH,MAAM,GAAG,EAAE;QACb;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAIpI,CAAC,CAAC6I,OAAO,EAAE;UACbV,MAAM,GAAG1K,CAAC,CAAC+F,YAAY;QACzB,CAAC,MAAM,IAAIxD,CAAC,CAAC8I,MAAM,EAAE;UACnBX,MAAM,GAAG1K,CAAC,CAAC4F,cAAc;QAC3B,CAAC,MAAM;UACL8E,MAAM,GAAG,EAAE;QACb;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAInI,CAAC,CAAC6I,OAAO,EAAE;UACbT,MAAM,GAAG,CAAC3K,CAAC,CAACwD,aAAa;QAC3B,CAAC,MAAM,IAAIjB,CAAC,CAAC8I,MAAM,EAAE;UACnBV,MAAM,GAAG,CAAC3K,CAAC,CAACyD,eAAe;QAC7B,CAAC,MAAM;UACLkH,MAAM,GAAG,CAAC,EAAE;QACd;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAIpI,CAAC,CAAC+I,QAAQ,EAAE;UACdX,MAAM,GAAG3K,CAAC,CAACyD,eAAe;QAC5B,CAAC,MAAM;UACLkH,MAAM,GAAG,CAAC3K,CAAC,CAACyD,eAAe;QAC7B;QACA;MACF,KAAK,EAAE;QAAE;QACPkH,MAAM,GAAG3K,CAAC,CAACyD,eAAe;QAC1B;MACF,KAAK,EAAE;QAAE;QACPkH,MAAM,GAAG,CAAC3K,CAAC,CAACyD,eAAe;QAC3B;MACF,KAAK,EAAE;QAAE;QACPkH,MAAM,GAAG3K,CAAC,CAACwD,aAAa;QACxB;MACF,KAAK,EAAE;QAAE;QACPmH,MAAM,GAAG,CAAC3K,CAAC,CAACwD,aAAa;QACzB;MACF;QACE;IACJ;IAEA,IAAIxD,CAAC,CAACQ,QAAQ,CAAC6F,eAAe,IAAIqE,MAAM,KAAK,CAAC,EAAE;MAC9C;IACF;IACA,IAAI1K,CAAC,CAACQ,QAAQ,CAACwG,eAAe,IAAI2D,MAAM,KAAK,CAAC,EAAE;MAC9C;IACF;IAEAxN,OAAO,CAACuG,SAAS,IAAIiH,MAAM;IAC3BxN,OAAO,CAAC4J,UAAU,IAAI2D,MAAM;IAC5BpF,cAAc,CAACtF,CAAC,CAAC;IAEjB,IAAIyK,oBAAoB,CAACC,MAAM,EAAEC,MAAM,CAAC,EAAE;MACxCpI,CAAC,CAAC0H,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,CAAC;AACJ;AAEA,SAASsB,KAAKA,CAACvL,CAAC,EAAE;EAChB,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,SAASsN,oBAAoBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC5C,IAAIpF,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;IACpD,IAAI8H,KAAK,GAAGrO,OAAO,CAACuG,SAAS,KAAK,CAAC;IACnC,IAAI+H,QAAQ,GACVlG,gBAAgB,GAAGpI,OAAO,CAACuO,YAAY,KAAKvO,OAAO,CAAC8I,YAAY;IAClE,IAAI0F,MAAM,GAAGxO,OAAO,CAAC4J,UAAU,KAAK,CAAC;IACrC,IAAI6E,OAAO,GACTzO,OAAO,CAAC4J,UAAU,GAAG5J,OAAO,CAAC0O,WAAW,KAAK1O,OAAO,CAAC6I,WAAW;IAElE,IAAI8F,SAAS;;IAEb;IACA,IAAItG,IAAI,CAACuG,GAAG,CAACpB,MAAM,CAAC,GAAGnF,IAAI,CAACuG,GAAG,CAACrB,MAAM,CAAC,EAAE;MACvCoB,SAAS,GAAGN,KAAK,IAAIC,QAAQ;IAC/B,CAAC,MAAM;MACLK,SAAS,GAAGH,MAAM,IAAIC,OAAO;IAC/B;IAEA,OAAOE,SAAS,GAAG,CAAC9L,CAAC,CAACQ,QAAQ,CAACoK,gBAAgB,GAAG,IAAI;EACxD;EAEA,SAASoB,iBAAiBA,CAACzJ,CAAC,EAAE;IAC5B,IAAImI,MAAM,GAAGnI,CAAC,CAACmI,MAAM;IACrB,IAAIC,MAAM,GAAG,CAAC,CAAC,GAAGpI,CAAC,CAACoI,MAAM;IAE1B,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MAClE;MACAD,MAAM,GAAI,CAAC,CAAC,GAAGnI,CAAC,CAAC0J,WAAW,GAAI,CAAC;MACjCtB,MAAM,GAAGpI,CAAC,CAAC2J,WAAW,GAAG,CAAC;IAC5B;IAEA,IAAI3J,CAAC,CAAC4J,SAAS,IAAI5J,CAAC,CAAC4J,SAAS,KAAK,CAAC,EAAE;MACpC;MACAzB,MAAM,IAAI,EAAE;MACZC,MAAM,IAAI,EAAE;IACd;IAEA,IAAID,MAAM,KAAKA,MAAM,IAAIC,MAAM,KAAKA,MAAM,CAAC,kBAAkB;MAC3D;MACAD,MAAM,GAAG,CAAC;MACVC,MAAM,GAAGpI,CAAC,CAAC6J,UAAU;IACvB;IAEA,IAAI7J,CAAC,CAAC+I,QAAQ,EAAE;MACd;MACA,OAAO,CAAC,CAACX,MAAM,EAAE,CAACD,MAAM,CAAC;IAC3B;IACA,OAAO,CAACA,MAAM,EAAEC,MAAM,CAAC;EACzB;EAEA,SAAS0B,uBAAuBA,CAAC/K,MAAM,EAAEoJ,MAAM,EAAEC,MAAM,EAAE;IACvD;IACA,IAAI,CAACjG,GAAG,CAACC,QAAQ,IAAIxH,OAAO,CAACmP,aAAa,CAAC,cAAc,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IAEA,IAAI,CAACnP,OAAO,CAAC+C,QAAQ,CAACoB,MAAM,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd;IAEA,IAAIiL,MAAM,GAAGjL,MAAM;IAEnB,OAAOiL,MAAM,IAAIA,MAAM,KAAKpP,OAAO,EAAE;MACnC,IAAIoP,MAAM,CAACtM,SAAS,CAACC,QAAQ,CAACjB,GAAG,CAAC9B,OAAO,CAACoC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MAEA,IAAI9B,KAAK,GAAGP,GAAG,CAACqP,MAAM,CAAC;;MAEvB;MACA,IAAI5B,MAAM,IAAIlN,KAAK,CAAC+O,SAAS,CAACC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAIC,YAAY,GAAGH,MAAM,CAACtG,YAAY,GAAGsG,MAAM,CAACI,YAAY;QAC5D,IAAID,YAAY,GAAG,CAAC,EAAE;UACpB,IACGH,MAAM,CAAC7I,SAAS,GAAG,CAAC,IAAIiH,MAAM,GAAG,CAAC,IAClC4B,MAAM,CAAC7I,SAAS,GAAGgJ,YAAY,IAAI/B,MAAM,GAAG,CAAE,EAC/C;YACA,OAAO,IAAI;UACb;QACF;MACF;MACA;MACA,IAAID,MAAM,IAAIjN,KAAK,CAACmP,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAII,aAAa,GAAGN,MAAM,CAACvG,WAAW,GAAGuG,MAAM,CAACO,WAAW;QAC3D,IAAID,aAAa,GAAG,CAAC,EAAE;UACrB,IACGN,MAAM,CAACxF,UAAU,GAAG,CAAC,IAAI2D,MAAM,GAAG,CAAC,IACnC6B,MAAM,CAACxF,UAAU,GAAG8F,aAAa,IAAInC,MAAM,GAAG,CAAE,EACjD;YACA,OAAO,IAAI;UACb;QACF;MACF;MAEA6B,MAAM,GAAGA,MAAM,CAAC9N,UAAU;IAC5B;IAEA,OAAO,KAAK;EACd;EAEA,SAASsO,iBAAiBA,CAACxK,CAAC,EAAE;IAC5B,IAAIgB,GAAG,GAAGyI,iBAAiB,CAACzJ,CAAC,CAAC;IAC9B,IAAImI,MAAM,GAAGnH,GAAG,CAAC,CAAC,CAAC;IACnB,IAAIoH,MAAM,GAAGpH,GAAG,CAAC,CAAC,CAAC;IAEnB,IAAI8I,uBAAuB,CAAC9J,CAAC,CAACjB,MAAM,EAAEoJ,MAAM,EAAEC,MAAM,CAAC,EAAE;MACrD;IACF;IAEA,IAAIqC,aAAa,GAAG,KAAK;IACzB,IAAI,CAAChN,CAAC,CAACQ,QAAQ,CAACyM,gBAAgB,EAAE;MAChC;MACA;MACA9P,OAAO,CAACuG,SAAS,IAAIiH,MAAM,GAAG3K,CAAC,CAACQ,QAAQ,CAAC0M,UAAU;MACnD/P,OAAO,CAAC4J,UAAU,IAAI2D,MAAM,GAAG1K,CAAC,CAACQ,QAAQ,CAAC0M,UAAU;IACtD,CAAC,MAAM,IAAIlN,CAAC,CAACkH,gBAAgB,IAAI,CAAClH,CAAC,CAACuG,gBAAgB,EAAE;MACpD;MACA;MACA,IAAIoE,MAAM,EAAE;QACVxN,OAAO,CAACuG,SAAS,IAAIiH,MAAM,GAAG3K,CAAC,CAACQ,QAAQ,CAAC0M,UAAU;MACrD,CAAC,MAAM;QACL/P,OAAO,CAACuG,SAAS,IAAIgH,MAAM,GAAG1K,CAAC,CAACQ,QAAQ,CAAC0M,UAAU;MACrD;MACAF,aAAa,GAAG,IAAI;IACtB,CAAC,MAAM,IAAIhN,CAAC,CAACuG,gBAAgB,IAAI,CAACvG,CAAC,CAACkH,gBAAgB,EAAE;MACpD;MACA;MACA,IAAIwD,MAAM,EAAE;QACVvN,OAAO,CAAC4J,UAAU,IAAI2D,MAAM,GAAG1K,CAAC,CAACQ,QAAQ,CAAC0M,UAAU;MACtD,CAAC,MAAM;QACL/P,OAAO,CAAC4J,UAAU,IAAI4D,MAAM,GAAG3K,CAAC,CAACQ,QAAQ,CAAC0M,UAAU;MACtD;MACAF,aAAa,GAAG,IAAI;IACtB;IAEA1H,cAAc,CAACtF,CAAC,CAAC;IAEjBgN,aAAa,GAAGA,aAAa,IAAIvC,oBAAoB,CAACC,MAAM,EAAEC,MAAM,CAAC;IACrE,IAAIqC,aAAa,IAAI,CAACzK,CAAC,CAAC4K,OAAO,EAAE;MAC/B5K,CAAC,CAAC2G,eAAe,CAAC,CAAC;MACnB3G,CAAC,CAAC0H,cAAc,CAAC,CAAC;IACpB;EACF;EAEA,IAAI,OAAOrH,MAAM,CAACwK,OAAO,KAAK,WAAW,EAAE;IACzCpN,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,OAAO,EAAE4P,iBAAiB,CAAC;EACnD,CAAC,MAAM,IAAI,OAAOnK,MAAM,CAACyK,YAAY,KAAK,WAAW,EAAE;IACrDrN,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,YAAY,EAAE4P,iBAAiB,CAAC;EACxD;AACF;AAEA,SAASO,KAAKA,CAACtN,CAAC,EAAE;EAChB,IAAI,CAAC0E,GAAG,CAACG,aAAa,IAAI,CAACH,GAAG,CAACO,iBAAiB,EAAE;IAChD;EACF;EAEA,IAAI9H,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,SAAS6P,aAAaA,CAACtC,MAAM,EAAEC,MAAM,EAAE;IACrC,IAAIjH,SAAS,GAAG8B,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;IAC7C,IAAIqD,UAAU,GAAG5J,OAAO,CAAC4J,UAAU;IACnC,IAAIwG,UAAU,GAAG/H,IAAI,CAACuG,GAAG,CAACrB,MAAM,CAAC;IACjC,IAAI8C,UAAU,GAAGhI,IAAI,CAACuG,GAAG,CAACpB,MAAM,CAAC;IAEjC,IAAI6C,UAAU,GAAGD,UAAU,EAAE;MAC3B;;MAEA,IACG5C,MAAM,GAAG,CAAC,IAAIjH,SAAS,KAAK1D,CAAC,CAACwD,aAAa,GAAGxD,CAAC,CAACyD,eAAe,IAC/DkH,MAAM,GAAG,CAAC,IAAIjH,SAAS,KAAK,CAAE,EAC/B;QACA;QACA,OAAOd,MAAM,CAAC6K,OAAO,KAAK,CAAC,IAAI9C,MAAM,GAAG,CAAC,IAAIjG,GAAG,CAACS,QAAQ;MAC3D;IACF,CAAC,MAAM,IAAIoI,UAAU,GAAGC,UAAU,EAAE;MAClC;;MAEA,IACG9C,MAAM,GAAG,CAAC,IAAI3D,UAAU,KAAK/G,CAAC,CAAC+F,YAAY,GAAG/F,CAAC,CAAC4F,cAAc,IAC9D8E,MAAM,GAAG,CAAC,IAAI3D,UAAU,KAAK,CAAE,EAChC;QACA,OAAO,IAAI;MACb;IACF;IAEA,OAAO,IAAI;EACb;EAEA,SAAS2G,cAAcA,CAACC,WAAW,EAAEC,WAAW,EAAE;IAChDzQ,OAAO,CAACuG,SAAS,IAAIkK,WAAW;IAChCzQ,OAAO,CAAC4J,UAAU,IAAI4G,WAAW;IAEjCrI,cAAc,CAACtF,CAAC,CAAC;EACnB;EAEA,IAAI6N,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,UAAU,GAAG,IAAI;EAErB,SAASC,QAAQA,CAAC1L,CAAC,EAAE;IACnB,IAAIA,CAAC,CAAC2L,aAAa,EAAE;MACnB,OAAO3L,CAAC,CAAC2L,aAAa,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL;MACA,OAAO3L,CAAC;IACV;EACF;EAEA,SAAS4L,YAAYA,CAAC5L,CAAC,EAAE;IACvB,IAAIA,CAAC,CAAC6L,WAAW,IAAI7L,CAAC,CAAC6L,WAAW,KAAK,KAAK,IAAI7L,CAAC,CAAC8L,OAAO,KAAK,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,IAAI9L,CAAC,CAAC2L,aAAa,IAAI3L,CAAC,CAAC2L,aAAa,CAACpM,MAAM,KAAK,CAAC,EAAE;MACnD,OAAO,IAAI;IACb;IACA,IACES,CAAC,CAAC6L,WAAW,IACb7L,CAAC,CAAC6L,WAAW,KAAK,OAAO,IACzB7L,CAAC,CAAC6L,WAAW,KAAK7L,CAAC,CAAC+L,oBAAoB,EACxC;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA,SAASC,UAAUA,CAAChM,CAAC,EAAE;IACrB,IAAI,CAAC4L,YAAY,CAAC5L,CAAC,CAAC,EAAE;MACpB;IACF;IAEA,IAAI+K,KAAK,GAAGW,QAAQ,CAAC1L,CAAC,CAAC;IAEvBsL,WAAW,CAACrE,KAAK,GAAG8D,KAAK,CAAC9D,KAAK;IAC/BqE,WAAW,CAACzE,KAAK,GAAGkE,KAAK,CAAClE,KAAK;IAE/B0E,SAAS,GAAG,IAAIU,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEhC,IAAIT,UAAU,KAAK,IAAI,EAAE;MACvBU,aAAa,CAACV,UAAU,CAAC;IAC3B;EACF;EAEA,SAAS3B,uBAAuBA,CAAC/K,MAAM,EAAEoJ,MAAM,EAAEC,MAAM,EAAE;IACvD,IAAI,CAACxN,OAAO,CAAC+C,QAAQ,CAACoB,MAAM,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd;IAEA,IAAIiL,MAAM,GAAGjL,MAAM;IAEnB,OAAOiL,MAAM,IAAIA,MAAM,KAAKpP,OAAO,EAAE;MACnC,IAAIoP,MAAM,CAACtM,SAAS,CAACC,QAAQ,CAACjB,GAAG,CAAC9B,OAAO,CAACoC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MAEA,IAAI9B,KAAK,GAAGP,GAAG,CAACqP,MAAM,CAAC;;MAEvB;MACA,IAAI5B,MAAM,IAAIlN,KAAK,CAAC+O,SAAS,CAACC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAIC,YAAY,GAAGH,MAAM,CAACtG,YAAY,GAAGsG,MAAM,CAACI,YAAY;QAC5D,IAAID,YAAY,GAAG,CAAC,EAAE;UACpB,IACGH,MAAM,CAAC7I,SAAS,GAAG,CAAC,IAAIiH,MAAM,GAAG,CAAC,IAClC4B,MAAM,CAAC7I,SAAS,GAAGgJ,YAAY,IAAI/B,MAAM,GAAG,CAAE,EAC/C;YACA,OAAO,IAAI;UACb;QACF;MACF;MACA;MACA,IAAID,MAAM,IAAIjN,KAAK,CAACmP,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAII,aAAa,GAAGN,MAAM,CAACvG,WAAW,GAAGuG,MAAM,CAACO,WAAW;QAC3D,IAAID,aAAa,GAAG,CAAC,EAAE;UACrB,IACGN,MAAM,CAACxF,UAAU,GAAG,CAAC,IAAI2D,MAAM,GAAG,CAAC,IACnC6B,MAAM,CAACxF,UAAU,GAAG8F,aAAa,IAAInC,MAAM,GAAG,CAAE,EACjD;YACA,OAAO,IAAI;UACb;QACF;MACF;MAEA6B,MAAM,GAAGA,MAAM,CAAC9N,UAAU;IAC5B;IAEA,OAAO,KAAK;EACd;EAEA,SAASkQ,SAASA,CAACpM,CAAC,EAAE;IACpB,IAAI4L,YAAY,CAAC5L,CAAC,CAAC,EAAE;MACnB,IAAI+K,KAAK,GAAGW,QAAQ,CAAC1L,CAAC,CAAC;MAEvB,IAAIqM,aAAa,GAAG;QAAEpF,KAAK,EAAE8D,KAAK,CAAC9D,KAAK;QAAEJ,KAAK,EAAEkE,KAAK,CAAClE;MAAM,CAAC;MAE9D,IAAIuE,WAAW,GAAGiB,aAAa,CAACpF,KAAK,GAAGqE,WAAW,CAACrE,KAAK;MACzD,IAAIoE,WAAW,GAAGgB,aAAa,CAACxF,KAAK,GAAGyE,WAAW,CAACzE,KAAK;MAEzD,IAAIiD,uBAAuB,CAAC9J,CAAC,CAACjB,MAAM,EAAEqM,WAAW,EAAEC,WAAW,CAAC,EAAE;QAC/D;MACF;MAEAF,cAAc,CAACC,WAAW,EAAEC,WAAW,CAAC;MACxCC,WAAW,GAAGe,aAAa;MAE3B,IAAIC,WAAW,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAEtC,IAAIK,OAAO,GAAGD,WAAW,GAAGf,SAAS;MACrC,IAAIgB,OAAO,GAAG,CAAC,EAAE;QACff,KAAK,CAAC1O,CAAC,GAAGsO,WAAW,GAAGmB,OAAO;QAC/Bf,KAAK,CAACjO,CAAC,GAAG8N,WAAW,GAAGkB,OAAO;QAC/BhB,SAAS,GAAGe,WAAW;MACzB;MAEA,IAAI7B,aAAa,CAACW,WAAW,EAAEC,WAAW,CAAC,EAAE;QAC3CrL,CAAC,CAAC0H,cAAc,CAAC,CAAC;MACpB;IACF;EACF;EACA,SAAS8E,QAAQA,CAAA,EAAG;IAClB,IAAI/O,CAAC,CAACQ,QAAQ,CAACwO,WAAW,EAAE;MAC1BN,aAAa,CAACV,UAAU,CAAC;MACzBA,UAAU,GAAGiB,WAAW,CAAC,YAAW;QAClC,IAAIjP,CAAC,CAACkP,aAAa,EAAE;UACnBR,aAAa,CAACV,UAAU,CAAC;UACzB;QACF;QAEA,IAAI,CAACD,KAAK,CAAC1O,CAAC,IAAI,CAAC0O,KAAK,CAACjO,CAAC,EAAE;UACxB4O,aAAa,CAACV,UAAU,CAAC;UACzB;QACF;QAEA,IAAIxI,IAAI,CAACuG,GAAG,CAACgC,KAAK,CAAC1O,CAAC,CAAC,GAAG,IAAI,IAAImG,IAAI,CAACuG,GAAG,CAACgC,KAAK,CAACjO,CAAC,CAAC,GAAG,IAAI,EAAE;UACxD4O,aAAa,CAACV,UAAU,CAAC;UACzB;QACF;QAEAN,cAAc,CAACK,KAAK,CAAC1O,CAAC,GAAG,EAAE,EAAE0O,KAAK,CAACjO,CAAC,GAAG,EAAE,CAAC;QAE1CiO,KAAK,CAAC1O,CAAC,IAAI,GAAG;QACd0O,KAAK,CAACjO,CAAC,IAAI,GAAG;MAChB,CAAC,EAAE,EAAE,CAAC;IACR;EACF;EAEA,IAAI4E,GAAG,CAACG,aAAa,EAAE;IACrB7E,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,YAAY,EAAEoR,UAAU,CAAC;IAC/CvO,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,WAAW,EAAEwR,SAAS,CAAC;IAC7C3O,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,UAAU,EAAE4R,QAAQ,CAAC;EAC7C,CAAC,MAAM,IAAIrK,GAAG,CAACO,iBAAiB,EAAE;IAChC,IAAIrC,MAAM,CAACuM,YAAY,EAAE;MACvBnP,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,aAAa,EAAEoR,UAAU,CAAC;MAChDvO,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,aAAa,EAAEwR,SAAS,CAAC;MAC/C3O,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,WAAW,EAAE4R,QAAQ,CAAC;IAC9C,CAAC,MAAM,IAAInM,MAAM,CAACwM,cAAc,EAAE;MAChCpP,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,eAAe,EAAEoR,UAAU,CAAC;MAClDvO,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,eAAe,EAAEwR,SAAS,CAAC;MACjD3O,CAAC,CAACiJ,KAAK,CAACjI,IAAI,CAAC7D,OAAO,EAAE,aAAa,EAAE4R,QAAQ,CAAC;IAChD;EACF;AACF;AAEA,IAAIM,eAAe,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAQ;IAC1CzO,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;IACpEiH,kBAAkB,EAAE,IAAI;IACxBF,kBAAkB,EAAE,IAAI;IACxBlH,kBAAkB,EAAE,IAAI;IACxB6F,mBAAmB,EAAE,CAAC;IACtBW,mBAAmB,EAAE,CAAC;IACtBZ,eAAe,EAAE,KAAK;IACtBW,eAAe,EAAE,KAAK;IACtBgI,WAAW,EAAE,IAAI;IACjB/B,gBAAgB,EAAE,KAAK;IACvBrC,gBAAgB,EAAE,IAAI;IACtBsC,UAAU,EAAE;EACd,CAAC;AAAG,CAAC;AAEL,IAAItM,QAAQ,GAAG;EACb,YAAY,EAAEoI,SAAS;EACvB,YAAY,EAAEU,SAAS;EACvBY,QAAQ,EAAEA,QAAQ;EAClBiB,KAAK,EAAEA,KAAK;EACZ+B,KAAK,EAAEA;AACT,CAAC;AAED,IAAIgC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACnS,OAAO,EAAEoS,YAAY,EAAE;EACtE,IAAIhO,MAAM,GAAG,IAAI;EACjB,IAAKgO,YAAY,KAAK,KAAK,CAAC,EAAGA,YAAY,GAAG,CAAC,CAAC;EAEhD,IAAI,OAAOpS,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGS,QAAQ,CAAC0O,aAAa,CAACnP,OAAO,CAAC;EAC3C;EAEA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACqS,QAAQ,EAAE;IACjC,MAAM,IAAIlR,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EAEA,IAAI,CAACnB,OAAO,GAAGA,OAAO;EAEtBA,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACC,IAAI,CAAC;EAE/B,IAAI,CAACsB,QAAQ,GAAG6O,eAAe,CAAC,CAAC;EACjC,KAAK,IAAI9R,GAAG,IAAIgS,YAAY,EAAE;IAC5B,IAAI,CAAC/O,QAAQ,CAACjD,GAAG,CAAC,GAAGgS,YAAY,CAAChS,GAAG,CAAC;EACxC;EAEA,IAAI,CAACqI,cAAc,GAAG,IAAI;EAC1B,IAAI,CAACnC,eAAe,GAAG,IAAI;EAC3B,IAAI,CAACsC,YAAY,GAAG,IAAI;EACxB,IAAI,CAACvC,aAAa,GAAG,IAAI;EAEzB,IAAI/D,KAAK,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOtC,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACC,KAAK,CAAC;EAAE,CAAC;EAC1E,IAAIgQ,IAAI,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOtS,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACC,KAAK,CAAC;EAAE,CAAC;EAE5E,IAAI,CAACgI,KAAK,GAAGvK,GAAG,CAACC,OAAO,CAAC,CAACmM,SAAS,KAAK,KAAK;EAC7C,IAAI,IAAI,CAAC7B,KAAK,KAAK,IAAI,EAAE;IACvBtK,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACE,GAAG,CAAC;EAChC;EACA,IAAI,CAACuQ,gBAAgB,GAAI,YAAY;IACnC,IAAIC,kBAAkB,GAAGxS,OAAO,CAAC4J,UAAU;IAC3C,IAAI6I,MAAM,GAAG,IAAI;IACjBzS,OAAO,CAAC4J,UAAU,GAAG,CAAC,CAAC;IACvB6I,MAAM,GAAGzS,OAAO,CAAC4J,UAAU,GAAG,CAAC;IAC/B5J,OAAO,CAAC4J,UAAU,GAAG4I,kBAAkB;IACvC,OAAOC,MAAM;EACf,CAAC,CAAE,CAAC;EACJ,IAAI,CAAC9I,wBAAwB,GAAG,IAAI,CAAC4I,gBAAgB,GACjDvS,OAAO,CAAC6I,WAAW,GAAG7I,OAAO,CAAC2P,WAAW,GACzC,CAAC;EACL,IAAI,CAAC7D,KAAK,GAAG,IAAIjH,YAAY,CAAC,CAAC;EAC/B,IAAI,CAACmI,aAAa,GAAGhN,OAAO,CAACgN,aAAa,IAAIvM,QAAQ;EAEtD,IAAI,CAACsI,cAAc,GAAGxI,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC;EAChDnC,OAAO,CAACgJ,WAAW,CAAC,IAAI,CAACD,cAAc,CAAC;EACxC,IAAI,CAAC0C,UAAU,GAAGlL,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC7C,IAAI,CAAC8G,cAAc,CAACC,WAAW,CAAC,IAAI,CAACyC,UAAU,CAAC;EAChD,IAAI,CAACA,UAAU,CAACiH,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;EAC3C,IAAI,CAAC5G,KAAK,CAACjI,IAAI,CAAC,IAAI,CAAC4H,UAAU,EAAE,OAAO,EAAEnJ,KAAK,CAAC;EAChD,IAAI,CAACwJ,KAAK,CAACjI,IAAI,CAAC,IAAI,CAAC4H,UAAU,EAAE,MAAM,EAAE6G,IAAI,CAAC;EAC9C,IAAI,CAAClJ,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACI,eAAe,GAAG,IAAI;EAC3B,IAAI,CAACE,cAAc,GAAG,IAAI;EAC1B,IAAIiJ,UAAU,GAAG5S,GAAG,CAAC,IAAI,CAACgJ,cAAc,CAAC;EACzC,IAAI,CAACiC,gBAAgB,GAAGnE,QAAQ,CAAC8L,UAAU,CAAC5H,MAAM,EAAE,EAAE,CAAC;EACvD,IAAI6H,KAAK,CAAC,IAAI,CAAC5H,gBAAgB,CAAC,EAAE;IAChC,IAAI,CAACF,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACI,aAAa,GAAGtE,KAAK,CAAC+L,UAAU,CAAC1H,GAAG,CAAC;EAC5C,CAAC,MAAM;IACL,IAAI,CAACH,uBAAuB,GAAG,IAAI;EACrC;EACA,IAAI,CAACY,gBAAgB,GACnB9E,KAAK,CAAC+L,UAAU,CAACtL,eAAe,CAAC,GAAGT,KAAK,CAAC+L,UAAU,CAACrL,gBAAgB,CAAC;EACxE;EACApH,GAAG,CAAC,IAAI,CAAC6I,cAAc,EAAE;IAAE8J,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C,IAAI,CAACvJ,gBAAgB,GACnB1C,KAAK,CAAC+L,UAAU,CAACG,UAAU,CAAC,GAAGlM,KAAK,CAAC+L,UAAU,CAACI,WAAW,CAAC;EAC9D7S,GAAG,CAAC,IAAI,CAAC6I,cAAc,EAAE;IAAE8J,OAAO,EAAE;EAAG,CAAC,CAAC;EACzC,IAAI,CAACxJ,UAAU,GAAG,IAAI;EACtB,IAAI,CAACE,UAAU,GAAG,IAAI;EAEtB,IAAI,CAACN,cAAc,GAAG1I,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC;EAChDnC,OAAO,CAACgJ,WAAW,CAAC,IAAI,CAACC,cAAc,CAAC;EACxC,IAAI,CAAC0C,UAAU,GAAGpL,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC7C,IAAI,CAACgH,cAAc,CAACD,WAAW,CAAC,IAAI,CAAC2C,UAAU,CAAC;EAChD,IAAI,CAACA,UAAU,CAAC+G,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;EAC3C,IAAI,CAAC5G,KAAK,CAACjI,IAAI,CAAC,IAAI,CAAC8H,UAAU,EAAE,OAAO,EAAErJ,KAAK,CAAC;EAChD,IAAI,CAACwJ,KAAK,CAACjI,IAAI,CAAC,IAAI,CAAC8H,UAAU,EAAE,MAAM,EAAE2G,IAAI,CAAC;EAC9C,IAAI,CAACvI,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACI,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;EACzB,IAAI4I,UAAU,GAAGjT,GAAG,CAAC,IAAI,CAACkJ,cAAc,CAAC;EACzC,IAAI,CAACqC,eAAe,GAAGzE,QAAQ,CAACmM,UAAU,CAAC3H,KAAK,EAAE,EAAE,CAAC;EACrD,IAAIuH,KAAK,CAAC,IAAI,CAACtH,eAAe,CAAC,EAAE;IAC/B,IAAI,CAACF,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACI,cAAc,GAAG5E,KAAK,CAACoM,UAAU,CAACnI,IAAI,CAAC;EAC9C,CAAC,MAAM;IACL,IAAI,CAACO,sBAAsB,GAAG,IAAI;EACpC;EACA,IAAI,CAACG,oBAAoB,GAAG,IAAI,CAACjB,KAAK,GAAGtD,UAAU,CAAC,IAAI,CAAC2E,UAAU,CAAC,GAAG,IAAI;EAC3E,IAAI,CAACC,gBAAgB,GACnBhF,KAAK,CAACoM,UAAU,CAACC,cAAc,CAAC,GAAGrM,KAAK,CAACoM,UAAU,CAACE,iBAAiB,CAAC;EACxEhT,GAAG,CAAC,IAAI,CAAC+I,cAAc,EAAE;IAAE4J,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C,IAAI,CAAC5I,iBAAiB,GACpBrD,KAAK,CAACoM,UAAU,CAACG,SAAS,CAAC,GAAGvM,KAAK,CAACoM,UAAU,CAACI,YAAY,CAAC;EAC9DlT,GAAG,CAAC,IAAI,CAAC+I,cAAc,EAAE;IAAE4J,OAAO,EAAE;EAAG,CAAC,CAAC;EACzC,IAAI,CAAC7I,WAAW,GAAG,IAAI;EACvB,IAAI,CAACE,UAAU,GAAG,IAAI;EAEtB,IAAI,CAACxD,KAAK,GAAG;IACXxE,CAAC,EACClC,OAAO,CAAC4J,UAAU,IAAI,CAAC,GACnB,OAAO,GACP5J,OAAO,CAAC4J,UAAU,IAAI,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACH,cAAc,GAC7D,KAAK,GACL,IAAI;IACV9F,CAAC,EACC3C,OAAO,CAACuG,SAAS,IAAI,CAAC,GAClB,OAAO,GACPvG,OAAO,CAACuG,SAAS,IAAI,IAAI,CAACF,aAAa,GAAG,IAAI,CAACC,eAAe,GAC9D,KAAK,GACL;EACR,CAAC;EAED,IAAI,CAAClD,OAAO,GAAG,IAAI;EAEnB,IAAI,CAACC,QAAQ,CAACI,QAAQ,CAAC0B,OAAO,CAAC,UAAUkO,WAAW,EAAE;IAAE,OAAO5P,QAAQ,CAAC4P,WAAW,CAAC,CAACjP,MAAM,CAAC;EAAE,CAAC,CAAC;EAEhG,IAAI,CAACkP,aAAa,GAAGjL,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC,CAAC,CAAC;EACpD,IAAI,CAACgN,cAAc,GAAGvT,OAAO,CAAC4J,UAAU,CAAC,CAAC;EAC1C,IAAI,CAACkC,KAAK,CAACjI,IAAI,CAAC,IAAI,CAAC7D,OAAO,EAAE,QAAQ,EAAE,UAAUoF,CAAC,EAAE;IAAE,OAAOhB,MAAM,CAACoP,QAAQ,CAACpO,CAAC,CAAC;EAAE,CAAC,CAAC;EACpF+C,cAAc,CAAC,IAAI,CAAC;AACtB,CAAC;AAEDgK,gBAAgB,CAACtR,SAAS,CAAC4S,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EACrD,IAAI,CAAC,IAAI,CAACrQ,OAAO,EAAE;IACjB;EACF;;EAEA;EACA,IAAI,CAACuG,wBAAwB,GAAG,IAAI,CAAC4I,gBAAgB,GACjD,IAAI,CAACvS,OAAO,CAAC6I,WAAW,GAAG,IAAI,CAAC7I,OAAO,CAAC2P,WAAW,GACnD,CAAC;;EAEL;EACAzP,GAAG,CAAC,IAAI,CAAC6I,cAAc,EAAE;IAAE8J,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C3S,GAAG,CAAC,IAAI,CAAC+I,cAAc,EAAE;IAAE4J,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C,IAAI,CAACvJ,gBAAgB,GACnB1C,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAACgJ,cAAc,CAAC,CAAC+J,UAAU,CAAC,GAC1ClM,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAACgJ,cAAc,CAAC,CAACgK,WAAW,CAAC;EAC7C,IAAI,CAAC9I,iBAAiB,GACpBrD,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAACkJ,cAAc,CAAC,CAACkK,SAAS,CAAC,GACzCvM,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAACkJ,cAAc,CAAC,CAACmK,YAAY,CAAC;;EAE9C;EACAlT,GAAG,CAAC,IAAI,CAAC6I,cAAc,EAAE;IAAE8J,OAAO,EAAE;EAAO,CAAC,CAAC;EAC7C3S,GAAG,CAAC,IAAI,CAAC+I,cAAc,EAAE;IAAE4J,OAAO,EAAE;EAAO,CAAC,CAAC;EAE7C1K,cAAc,CAAC,IAAI,CAAC;EAEpBtC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC9CA,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAE/C3F,GAAG,CAAC,IAAI,CAAC6I,cAAc,EAAE;IAAE8J,OAAO,EAAE;EAAG,CAAC,CAAC;EACzC3S,GAAG,CAAC,IAAI,CAAC+I,cAAc,EAAE;IAAE4J,OAAO,EAAE;EAAG,CAAC,CAAC;AAC3C,CAAC;AAEDV,gBAAgB,CAACtR,SAAS,CAAC2S,QAAQ,GAAG,SAASA,QAAQA,CAAEpO,CAAC,EAAE;EAC1D,IAAI,CAAC,IAAI,CAAChC,OAAO,EAAE;IACjB;EACF;EAEA+E,cAAc,CAAC,IAAI,CAAC;EACpBtC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC7F,OAAO,CAACuG,SAAS,GAAG,IAAI,CAAC+M,aAAa,CAAC;EAC3EzN,iBAAiB,CACf,IAAI,EACJ,MAAM,EACN,IAAI,CAAC7F,OAAO,CAAC4J,UAAU,GAAG,IAAI,CAAC2J,cACjC,CAAC;EAED,IAAI,CAACD,aAAa,GAAGjL,IAAI,CAACC,KAAK,CAAC,IAAI,CAACtI,OAAO,CAACuG,SAAS,CAAC;EACvD,IAAI,CAACgN,cAAc,GAAG,IAAI,CAACvT,OAAO,CAAC4J,UAAU;AAC/C,CAAC;AAEDuI,gBAAgB,CAACtR,SAAS,CAAC6S,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EACvD,IAAI,CAAC,IAAI,CAACtQ,OAAO,EAAE;IACjB;EACF;EAEA,IAAI,CAAC0I,KAAK,CAACxH,SAAS,CAAC,CAAC;EACtBjD,MAAM,CAAC,IAAI,CAACoK,UAAU,CAAC;EACvBpK,MAAM,CAAC,IAAI,CAACsK,UAAU,CAAC;EACvBtK,MAAM,CAAC,IAAI,CAAC0H,cAAc,CAAC;EAC3B1H,MAAM,CAAC,IAAI,CAAC4H,cAAc,CAAC;EAC3B,IAAI,CAAC0K,eAAe,CAAC,CAAC;;EAEtB;EACA,IAAI,CAAC3T,OAAO,GAAG,IAAI;EACnB,IAAI,CAACyL,UAAU,GAAG,IAAI;EACtB,IAAI,CAACE,UAAU,GAAG,IAAI;EACtB,IAAI,CAAC5C,cAAc,GAAG,IAAI;EAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;EAE1B,IAAI,CAAC7F,OAAO,GAAG,KAAK;AACtB,CAAC;AAED+O,gBAAgB,CAACtR,SAAS,CAAC8S,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;EACvE,IAAI,CAAC3T,OAAO,CAACQ,SAAS,GAAG,IAAI,CAACR,OAAO,CAACQ,SAAS,CAC5CoT,KAAK,CAAC,GAAG,CAAC,CACVjS,MAAM,CAAC,UAAU4C,IAAI,EAAE;IAAE,OAAO,CAACA,IAAI,CAAC+K,KAAK,CAAC,eAAe,CAAC;EAAE,CAAC,CAAC,CAChEuE,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;AAED,eAAe1B,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
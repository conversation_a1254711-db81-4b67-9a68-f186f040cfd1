{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function schedulePromise(input, scheduler) {\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    sub.add(scheduler.schedule(() => input.then(value => {\n      sub.add(scheduler.schedule(() => {\n        subscriber.next(value);\n        sub.add(scheduler.schedule(() => subscriber.complete()));\n      }));\n    }, err => {\n      sub.add(scheduler.schedule(() => subscriber.error(err)));\n    })));\n    return sub;\n  });\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "schedulePromise", "input", "scheduler", "subscriber", "sub", "add", "schedule", "then", "value", "next", "complete", "err", "error"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduled/schedulePromise.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function schedulePromise(input, scheduler) {\n    return new Observable(subscriber => {\n        const sub = new Subscription();\n        sub.add(scheduler.schedule(() => input.then(value => {\n            sub.add(scheduler.schedule(() => {\n                subscriber.next(value);\n                sub.add(scheduler.schedule(() => subscriber.complete()));\n            }));\n        }, err => {\n            sub.add(scheduler.schedule(() => subscriber.error(err)));\n        })));\n        return sub;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC9C,OAAO,IAAIJ,UAAU,CAACK,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAG,IAAIL,YAAY,CAAC,CAAC;IAC9BK,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAML,KAAK,CAACM,IAAI,CAACC,KAAK,IAAI;MACjDJ,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAM;QAC7BH,UAAU,CAACM,IAAI,CAACD,KAAK,CAAC;QACtBJ,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAMH,UAAU,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC;IACP,CAAC,EAAEC,GAAG,IAAI;MACNP,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAMH,UAAU,CAACS,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC;IACJ,OAAOP,GAAG;EACd,CAAC,CAAC;AACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
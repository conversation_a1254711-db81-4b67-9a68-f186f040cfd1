{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function materialize() {\n  return function materializeOperatorFunction(source) {\n    return source.lift(new MaterializeOperator());\n  };\n}\nclass MaterializeOperator {\n  call(subscriber, source) {\n    return source.subscribe(new MaterializeSubscriber(subscriber));\n  }\n}\nclass MaterializeSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n  _next(value) {\n    this.destination.next(Notification.createNext(value));\n  }\n  _error(err) {\n    const destination = this.destination;\n    destination.next(Notification.createError(err));\n    destination.complete();\n  }\n  _complete() {\n    const destination = this.destination;\n    destination.next(Notification.createComplete());\n    destination.complete();\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "Notification", "materialize", "materializeOperatorFunction", "source", "lift", "MaterializeOperator", "call", "subscriber", "subscribe", "MaterializeSubscriber", "constructor", "destination", "_next", "value", "next", "createNext", "_error", "err", "createError", "complete", "_complete", "createComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/materialize.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function materialize() {\n    return function materializeOperatorFunction(source) {\n        return source.lift(new MaterializeOperator());\n    };\n}\nclass MaterializeOperator {\n    call(subscriber, source) {\n        return source.subscribe(new MaterializeSubscriber(subscriber));\n    }\n}\nclass MaterializeSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n    }\n    _next(value) {\n        this.destination.next(Notification.createNext(value));\n    }\n    _error(err) {\n        const destination = this.destination;\n        destination.next(Notification.createError(err));\n        destination.complete();\n    }\n    _complete() {\n        const destination = this.destination;\n        destination.next(Notification.createComplete());\n        destination.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC1B,OAAO,SAASC,2BAA2BA,CAACC,MAAM,EAAE;IAChD,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,mBAAmB,CAAC,CAAC,CAAC;EACjD,CAAC;AACL;AACA,MAAMA,mBAAmB,CAAC;EACtBC,IAAIA,CAACC,UAAU,EAAEJ,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACK,SAAS,CAAC,IAAIC,qBAAqB,CAACF,UAAU,CAAC,CAAC;EAClE;AACJ;AACA,MAAME,qBAAqB,SAASV,UAAU,CAAC;EAC3CW,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;EACtB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACF,WAAW,CAACG,IAAI,CAACd,YAAY,CAACe,UAAU,CAACF,KAAK,CAAC,CAAC;EACzD;EACAG,MAAMA,CAACC,GAAG,EAAE;IACR,MAAMN,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACG,IAAI,CAACd,YAAY,CAACkB,WAAW,CAACD,GAAG,CAAC,CAAC;IAC/CN,WAAW,CAACQ,QAAQ,CAAC,CAAC;EAC1B;EACAC,SAASA,CAAA,EAAG;IACR,MAAMT,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACG,IAAI,CAACd,YAAY,CAACqB,cAAc,CAAC,CAAC,CAAC;IAC/CV,WAAW,CAACQ,QAAQ,CAAC,CAAC;EAC1B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
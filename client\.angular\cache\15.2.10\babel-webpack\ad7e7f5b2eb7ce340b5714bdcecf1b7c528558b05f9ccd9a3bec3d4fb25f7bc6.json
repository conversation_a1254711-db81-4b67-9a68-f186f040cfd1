{"ast": null, "code": "function hex2rgba(hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString();\n  }\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string');\n  }\n  let hexCode = hex.slice().replace('#', '').split('');\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex);\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c];\n    }));\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F');\n  const hexValue = parseInt(hexCode.join(''), 16);\n  return {\n    r: hexValue >> 24 & 255,\n    g: hexValue >> 16 & 255,\n    b: hexValue >> 8 & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  };\n}\nexports.getOptions = function getOptions(options) {\n  if (!options) options = {};\n  if (!options.color) options.color = {};\n  const margin = typeof options.margin === 'undefined' || options.margin === null || options.margin < 0 ? 4 : options.margin;\n  const width = options.width && options.width >= 21 ? options.width : undefined;\n  const scale = options.scale || 4;\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  };\n};\nexports.getScale = function getScale(qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2 ? opts.width / (qrSize + opts.margin * 2) : opts.scale;\n};\nexports.getImageWidth = function getImageWidth(qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts);\n  return Math.floor((qrSize + opts.margin * 2) * scale);\n};\nexports.qrToImageData = function qrToImageData(imgData, qr, opts) {\n  const size = qr.modules.size;\n  const data = qr.modules.data;\n  const scale = exports.getScale(size, opts);\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale);\n  const scaledMargin = opts.margin * scale;\n  const palette = [opts.color.light, opts.color.dark];\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4;\n      let pxColor = opts.color.light;\n      if (i >= scaledMargin && j >= scaledMargin && i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale);\n        const jSrc = Math.floor((j - scaledMargin) / scale);\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0];\n      }\n      imgData[posDst++] = pxColor.r;\n      imgData[posDst++] = pxColor.g;\n      imgData[posDst++] = pxColor.b;\n      imgData[posDst] = pxColor.a;\n    }\n  }\n};", "map": {"version": 3, "names": ["hex2rgba", "hex", "toString", "Error", "hexCode", "slice", "replace", "split", "length", "Array", "prototype", "concat", "apply", "map", "c", "push", "hexValue", "parseInt", "join", "r", "g", "b", "a", "exports", "getOptions", "options", "color", "margin", "width", "undefined", "scale", "dark", "light", "type", "rendererOpts", "getScale", "qrSize", "opts", "getImageWidth", "Math", "floor", "qrToImageData", "imgData", "qr", "size", "modules", "data", "symbolSize", "<PERSON><PERSON><PERSON><PERSON>", "palette", "i", "j", "posDst", "pxColor", "iSrc", "jSrc"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/renderer/utils.js"], "sourcesContent": ["function hex2rgba (hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString()\n  }\n\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string')\n  }\n\n  let hexCode = hex.slice().replace('#', '').split('')\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex)\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c]\n    }))\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F')\n\n  const hexValue = parseInt(hexCode.join(''), 16)\n\n  return {\n    r: (hexValue >> 24) & 255,\n    g: (hexValue >> 16) & 255,\n    b: (hexValue >> 8) & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  }\n}\n\nexports.getOptions = function getOptions (options) {\n  if (!options) options = {}\n  if (!options.color) options.color = {}\n\n  const margin = typeof options.margin === 'undefined' ||\n    options.margin === null ||\n    options.margin < 0\n    ? 4\n    : options.margin\n\n  const width = options.width && options.width >= 21 ? options.width : undefined\n  const scale = options.scale || 4\n\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  }\n}\n\nexports.getScale = function getScale (qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2\n    ? opts.width / (qrSize + opts.margin * 2)\n    : opts.scale\n}\n\nexports.getImageWidth = function getImageWidth (qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts)\n  return Math.floor((qrSize + opts.margin * 2) * scale)\n}\n\nexports.qrToImageData = function qrToImageData (imgData, qr, opts) {\n  const size = qr.modules.size\n  const data = qr.modules.data\n  const scale = exports.getScale(size, opts)\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale)\n  const scaledMargin = opts.margin * scale\n  const palette = [opts.color.light, opts.color.dark]\n\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4\n      let pxColor = opts.color.light\n\n      if (i >= scaledMargin && j >= scaledMargin &&\n        i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale)\n        const jSrc = Math.floor((j - scaledMargin) / scale)\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0]\n      }\n\n      imgData[posDst++] = pxColor.r\n      imgData[posDst++] = pxColor.g\n      imgData[posDst++] = pxColor.b\n      imgData[posDst] = pxColor.a\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,QAAQA,CAAEC,GAAG,EAAE;EACtB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAGA,GAAG,CAACC,QAAQ,CAAC,CAAC;EACtB;EAEA,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIE,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAEA,IAAIC,OAAO,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC;EACpD,IAAIH,OAAO,CAACI,MAAM,GAAG,CAAC,IAAIJ,OAAO,CAACI,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;IACpE,MAAM,IAAIL,KAAK,CAAC,qBAAqB,GAAGF,GAAG,CAAC;EAC9C;;EAEA;EACA,IAAIG,OAAO,CAACI,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;IAChDJ,OAAO,GAAGK,KAAK,CAACC,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAER,OAAO,CAACS,GAAG,CAAC,UAAUC,CAAC,EAAE;MAClE,OAAO,CAACA,CAAC,EAAEA,CAAC,CAAC;IACf,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,IAAIV,OAAO,CAACI,MAAM,KAAK,CAAC,EAAEJ,OAAO,CAACW,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EAEhD,MAAMC,QAAQ,GAAGC,QAAQ,CAACb,OAAO,CAACc,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAE/C,OAAO;IACLC,CAAC,EAAGH,QAAQ,IAAI,EAAE,GAAI,GAAG;IACzBI,CAAC,EAAGJ,QAAQ,IAAI,EAAE,GAAI,GAAG;IACzBK,CAAC,EAAGL,QAAQ,IAAI,CAAC,GAAI,GAAG;IACxBM,CAAC,EAAEN,QAAQ,GAAG,GAAG;IACjBf,GAAG,EAAE,GAAG,GAAGG,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACa,IAAI,CAAC,EAAE;EACxC,CAAC;AACH;AAEAK,OAAO,CAACC,UAAU,GAAG,SAASA,UAAUA,CAAEC,OAAO,EAAE;EACjD,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;EAC1B,IAAI,CAACA,OAAO,CAACC,KAAK,EAAED,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;EAEtC,MAAMC,MAAM,GAAG,OAAOF,OAAO,CAACE,MAAM,KAAK,WAAW,IAClDF,OAAO,CAACE,MAAM,KAAK,IAAI,IACvBF,OAAO,CAACE,MAAM,GAAG,CAAC,GAChB,CAAC,GACDF,OAAO,CAACE,MAAM;EAElB,MAAMC,KAAK,GAAGH,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACG,KAAK,IAAI,EAAE,GAAGH,OAAO,CAACG,KAAK,GAAGC,SAAS;EAC9E,MAAMC,KAAK,GAAGL,OAAO,CAACK,KAAK,IAAI,CAAC;EAEhC,OAAO;IACLF,KAAK,EAAEA,KAAK;IACZE,KAAK,EAAEF,KAAK,GAAG,CAAC,GAAGE,KAAK;IACxBH,MAAM,EAAEA,MAAM;IACdD,KAAK,EAAE;MACLK,IAAI,EAAE/B,QAAQ,CAACyB,OAAO,CAACC,KAAK,CAACK,IAAI,IAAI,WAAW,CAAC;MACjDC,KAAK,EAAEhC,QAAQ,CAACyB,OAAO,CAACC,KAAK,CAACM,KAAK,IAAI,WAAW;IACpD,CAAC;IACDC,IAAI,EAAER,OAAO,CAACQ,IAAI;IAClBC,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,CAAC;EACzC,CAAC;AACH,CAAC;AAEDX,OAAO,CAACY,QAAQ,GAAG,SAASA,QAAQA,CAAEC,MAAM,EAAEC,IAAI,EAAE;EAClD,OAAOA,IAAI,CAACT,KAAK,IAAIS,IAAI,CAACT,KAAK,IAAIQ,MAAM,GAAGC,IAAI,CAACV,MAAM,GAAG,CAAC,GACvDU,IAAI,CAACT,KAAK,IAAIQ,MAAM,GAAGC,IAAI,CAACV,MAAM,GAAG,CAAC,CAAC,GACvCU,IAAI,CAACP,KAAK;AAChB,CAAC;AAEDP,OAAO,CAACe,aAAa,GAAG,SAASA,aAAaA,CAAEF,MAAM,EAAEC,IAAI,EAAE;EAC5D,MAAMP,KAAK,GAAGP,OAAO,CAACY,QAAQ,CAACC,MAAM,EAAEC,IAAI,CAAC;EAC5C,OAAOE,IAAI,CAACC,KAAK,CAAC,CAACJ,MAAM,GAAGC,IAAI,CAACV,MAAM,GAAG,CAAC,IAAIG,KAAK,CAAC;AACvD,CAAC;AAEDP,OAAO,CAACkB,aAAa,GAAG,SAASA,aAAaA,CAAEC,OAAO,EAAEC,EAAE,EAAEN,IAAI,EAAE;EACjE,MAAMO,IAAI,GAAGD,EAAE,CAACE,OAAO,CAACD,IAAI;EAC5B,MAAME,IAAI,GAAGH,EAAE,CAACE,OAAO,CAACC,IAAI;EAC5B,MAAMhB,KAAK,GAAGP,OAAO,CAACY,QAAQ,CAACS,IAAI,EAAEP,IAAI,CAAC;EAC1C,MAAMU,UAAU,GAAGR,IAAI,CAACC,KAAK,CAAC,CAACI,IAAI,GAAGP,IAAI,CAACV,MAAM,GAAG,CAAC,IAAIG,KAAK,CAAC;EAC/D,MAAMkB,YAAY,GAAGX,IAAI,CAACV,MAAM,GAAGG,KAAK;EACxC,MAAMmB,OAAO,GAAG,CAACZ,IAAI,CAACX,KAAK,CAACM,KAAK,EAAEK,IAAI,CAACX,KAAK,CAACK,IAAI,CAAC;EAEnD,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAEG,CAAC,EAAE,EAAE;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,EAAEI,CAAC,EAAE,EAAE;MACnC,IAAIC,MAAM,GAAG,CAACF,CAAC,GAAGH,UAAU,GAAGI,CAAC,IAAI,CAAC;MACrC,IAAIE,OAAO,GAAGhB,IAAI,CAACX,KAAK,CAACM,KAAK;MAE9B,IAAIkB,CAAC,IAAIF,YAAY,IAAIG,CAAC,IAAIH,YAAY,IACxCE,CAAC,GAAGH,UAAU,GAAGC,YAAY,IAAIG,CAAC,GAAGJ,UAAU,GAAGC,YAAY,EAAE;QAChE,MAAMM,IAAI,GAAGf,IAAI,CAACC,KAAK,CAAC,CAACU,CAAC,GAAGF,YAAY,IAAIlB,KAAK,CAAC;QACnD,MAAMyB,IAAI,GAAGhB,IAAI,CAACC,KAAK,CAAC,CAACW,CAAC,GAAGH,YAAY,IAAIlB,KAAK,CAAC;QACnDuB,OAAO,GAAGJ,OAAO,CAACH,IAAI,CAACQ,IAAI,GAAGV,IAAI,GAAGW,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACrD;MAEAb,OAAO,CAACU,MAAM,EAAE,CAAC,GAAGC,OAAO,CAAClC,CAAC;MAC7BuB,OAAO,CAACU,MAAM,EAAE,CAAC,GAAGC,OAAO,CAACjC,CAAC;MAC7BsB,OAAO,CAACU,MAAM,EAAE,CAAC,GAAGC,OAAO,CAAChC,CAAC;MAC7BqB,OAAO,CAACU,MAAM,CAAC,GAAGC,OAAO,CAAC/B,CAAC;IAC7B;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
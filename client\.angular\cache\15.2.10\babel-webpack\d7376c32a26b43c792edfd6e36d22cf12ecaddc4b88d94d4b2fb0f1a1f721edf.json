{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { NgbAccordionModule, NgbCollapseModule, NgbDropdownModule, NgbModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { DataTablesModule } from 'angular-datatables';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\nimport { FilterPipe } from '@core/pipes/filter.pipe';\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { PlayersComponent } from './players.component';\nimport { ShareModule } from 'app/share/share.module';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\nconst routes = [{\n  path: '',\n  component: PlayersComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_children\n  }\n}];\nexport class PlayersModule {\n  static #_ = this.ɵfac = function PlayersModule_Factory(t) {\n    return new (t || PlayersModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: PlayersModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [FilterPipe],\n    imports: [ShareModule, CKEditorModule, CoreCommonModule, NgbNavModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, RouterModule.forChild(routes), ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, CorePipesModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, FormlyModule.forRoot({\n      types: [{\n        name: 'ng-select',\n        component: NgSelectTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'file',\n        component: FormlyFieldFile,\n        wrappers: ['form-field']\n      }],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    })]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlayersModule, {\n    declarations: [PlayersComponent],\n    imports: [ShareModule, CKEditorModule, CoreCommonModule, NgbNavModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, i1.RouterModule, ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, CorePipesModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, i2.FormlyModule],\n    exports: [PlayersComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,kBAAkB,EAClBC,iBAAiB,EACjBC,iBAAiB,EACjBC,SAAS,EACTC,YAAY,QACP,4BAA4B;AACnC,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,eAAe,QAA0B,qBAAqB;AACvE,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,qDAAqD;AAC5D,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAAuBC,YAAY,QAAQ,kBAAkB;AAC7D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;;;;AAC1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL,gBAAgB;EAC3BM,WAAW,EAAE,CAACR,gBAAgB,CAAC;EAC/BS,IAAI,EAAE;IAAEC,WAAW,EAAET,SAAS,CAACU,WAAW,CAACC;EAAe;CAC3D,CACF;AAkDD,OAAM,MAAOC,aAAa;EAAA,QAAAC,CAAA;qBAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA;UAAbF;EAAa;EAAA,QAAAG,EAAA;eA/Cb,CAACpB,UAAU,CAAC;IAAAqB,OAAA,GAGrBd,WAAW,EACXL,cAAc,EACdX,gBAAgB,EAChBP,YAAY,EACZJ,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBJ,YAAY,EACZC,YAAY,CAAC2C,QAAQ,CAACb,MAAM,CAAC,EAC7BxB,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACde,eAAe,EACfd,mBAAmB,EACnBE,uBAAuB,EACvBG,mBAAmB,EACnBlB,iBAAiB,EACjBiB,qBAAqB,EACrBD,YAAY,CAAC0B,OAAO,CAAC;MACnBC,KAAK,EAAE,CACL;QACEC,IAAI,EAAE,WAAW;QACjBd,SAAS,EAAEV,qBAAqB;QAChCyB,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACED,IAAI,EAAE,MAAM;QACZd,SAAS,EAAER,eAAe;QAC1BuB,QAAQ,EAAE,CAAC,YAAY;OACxB,CACF;MACDC,kBAAkB,EAAE,CAClB;QAAEF,IAAI,EAAE,aAAa;QAAEG,OAAO,EAAEjC;MAAuB,CAAE;KAE5D,CAAC;EAAA;;;2EAIOsB,aAAa;IAAAY,YAAA,GA9CTvB,gBAAgB;IAAAe,OAAA,GAE7Bd,WAAW,EACXL,cAAc,EACdX,gBAAgB,EAChBP,YAAY,EACZJ,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBJ,YAAY,EAAAoD,EAAA,CAAAnD,YAAA,EAEZM,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACde,eAAe,EACfd,mBAAmB,EACnBE,uBAAuB,EACvBG,mBAAmB,EACnBlB,iBAAiB,EACjBiB,qBAAqB,EAAAiC,EAAA,CAAAlC,YAAA;IAAAmC,OAAA,GAmBb1B,gBAAgB;EAAA;AAAA", "names": ["CommonModule", "RouterModule", "NgbAccordionModule", "NgbCollapseModule", "NgbDropdownModule", "NgbModule", "NgbNavModule", "ContentHeaderModule", "FormsModule", "ReactiveFormsModule", "ErrorMessageModule", "TranslateModule", "CoreSidebarModule", "CoreCommonModule", "DataTablesModule", "NgSelectModule", "EditorSidebarModule", "serverValidationMessage", "BtnDropdownActionModule", "FormlyModule", "FormlyBootstrapModule", "CoreTouchspinModule", "FilterPipe", "NgSelectTypeComponent", "CKEditorModule", "FormlyFieldFile", "PermissionsGuard", "AppConfig", "PlayersComponent", "ShareModule", "CorePipesModule", "routes", "path", "component", "canActivate", "data", "permissions", "PERMISSIONS", "manage_children", "PlayersModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "types", "name", "wrappers", "validationMessages", "message", "declarations", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\players\\players.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport {\r\n  NgbAccordionModule,\r\n  NgbCollapseModule,\r\n  NgbDropdownModule,\r\n  NgbModule,\r\n  NgbNavModule,\r\n} from '@ng-bootstrap/ng-bootstrap';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport {\r\n  EditorSidebarModule,\r\n  serverValidationMessage,\r\n} from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { FormlyConfig, FormlyModule } from '@ngx-formly/core';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\r\nimport { FilterPipe } from '@core/pipes/filter.pipe';\r\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\r\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\r\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\r\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { PlayersComponent } from './players.component';\r\nimport { ShareModule } from 'app/share/share.module';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: PlayersComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_children },\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  providers: [FilterPipe],\r\n  declarations: [PlayersComponent],\r\n  imports: [\r\n    ShareModule,\r\n    CKEditorModule,\r\n    CoreCommonModule,\r\n    NgbNavModule,\r\n    NgbAccordionModule,\r\n    NgbModule,\r\n    NgbDropdownModule,\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ErrorMessageModule,\r\n    TranslateModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    DataTablesModule,\r\n    NgSelectModule,\r\n    CorePipesModule,\r\n    EditorSidebarModule,\r\n    BtnDropdownActionModule,\r\n    CoreTouchspinModule,\r\n    NgbCollapseModule,\r\n    FormlyBootstrapModule,\r\n    FormlyModule.forRoot({\r\n      types: [\r\n        {\r\n          name: 'ng-select',\r\n          component: NgSelectTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'file',\r\n          component: FormlyFieldFile,\r\n          wrappers: ['form-field'],\r\n        },\r\n      ],\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],\r\n    }),\r\n  ],\r\n  exports: [PlayersComponent],\r\n})\r\nexport class PlayersModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
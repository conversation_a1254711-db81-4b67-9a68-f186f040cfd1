{"ast": null, "code": "export const REGEX = {\n  URL: /^https?:/\n};", "map": {"version": 3, "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAG;EACnBC,GAAG,EAAE;CACN", "names": ["REGEX", "URL"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\helpers\\regex.ts"], "sourcesContent": ["export const REGEX = {\r\n  URL: /^https?:/,\r\n};\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
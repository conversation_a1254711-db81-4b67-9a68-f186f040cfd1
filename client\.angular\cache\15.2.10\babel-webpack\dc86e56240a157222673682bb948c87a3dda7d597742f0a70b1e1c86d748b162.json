{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function sampleTime(period, scheduler = async) {\n  return source => source.lift(new SampleTimeOperator(period, scheduler));\n}\nclass SampleTimeOperator {\n  constructor(period, scheduler) {\n    this.period = period;\n    this.scheduler = scheduler;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new SampleTimeSubscriber(subscriber, this.period, this.scheduler));\n  }\n}\nclass SampleTimeSubscriber extends Subscriber {\n  constructor(destination, period, scheduler) {\n    super(destination);\n    this.period = period;\n    this.scheduler = scheduler;\n    this.hasValue = false;\n    this.add(scheduler.schedule(dispatchNotification, period, {\n      subscriber: this,\n      period\n    }));\n  }\n  _next(value) {\n    this.lastValue = value;\n    this.hasValue = true;\n  }\n  notifyNext() {\n    if (this.hasValue) {\n      this.hasValue = false;\n      this.destination.next(this.lastValue);\n    }\n  }\n}\nfunction dispatchNotification(state) {\n  let {\n    subscriber,\n    period\n  } = state;\n  subscriber.notifyNext();\n  this.schedule(state, period);\n}", "map": {"version": 3, "names": ["Subscriber", "async", "sampleTime", "period", "scheduler", "source", "lift", "SampleTimeOperator", "constructor", "call", "subscriber", "subscribe", "SampleTimeSubscriber", "destination", "hasValue", "add", "schedule", "dispatchNotification", "_next", "value", "lastValue", "notifyNext", "next", "state"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/sampleTime.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function sampleTime(period, scheduler = async) {\n    return (source) => source.lift(new SampleTimeOperator(period, scheduler));\n}\nclass SampleTimeOperator {\n    constructor(period, scheduler) {\n        this.period = period;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SampleTimeSubscriber(subscriber, this.period, this.scheduler));\n    }\n}\nclass SampleTimeSubscriber extends Subscriber {\n    constructor(destination, period, scheduler) {\n        super(destination);\n        this.period = period;\n        this.scheduler = scheduler;\n        this.hasValue = false;\n        this.add(scheduler.schedule(dispatchNotification, period, { subscriber: this, period }));\n    }\n    _next(value) {\n        this.lastValue = value;\n        this.hasValue = true;\n    }\n    notifyNext() {\n        if (this.hasValue) {\n            this.hasValue = false;\n            this.destination.next(this.lastValue);\n        }\n    }\n}\nfunction dispatchNotification(state) {\n    let { subscriber, period } = state;\n    subscriber.notifyNext();\n    this.schedule(state, period);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,GAAGH,KAAK,EAAE;EAClD,OAAQI,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,kBAAkB,CAACJ,MAAM,EAAEC,SAAS,CAAC,CAAC;AAC7E;AACA,MAAMG,kBAAkB,CAAC;EACrBC,WAAWA,CAACL,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,oBAAoB,CAACF,UAAU,EAAE,IAAI,CAACP,MAAM,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;EAC9F;AACJ;AACA,MAAMQ,oBAAoB,SAASZ,UAAU,CAAC;EAC1CQ,WAAWA,CAACK,WAAW,EAAEV,MAAM,EAAEC,SAAS,EAAE;IACxC,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACV,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACU,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,GAAG,CAACX,SAAS,CAACY,QAAQ,CAACC,oBAAoB,EAAEd,MAAM,EAAE;MAAEO,UAAU,EAAE,IAAI;MAAEP;IAAO,CAAC,CAAC,CAAC;EAC5F;EACAe,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACC,SAAS,GAAGD,KAAK;IACtB,IAAI,CAACL,QAAQ,GAAG,IAAI;EACxB;EACAO,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACP,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACD,WAAW,CAACS,IAAI,CAAC,IAAI,CAACF,SAAS,CAAC;IACzC;EACJ;AACJ;AACA,SAASH,oBAAoBA,CAACM,KAAK,EAAE;EACjC,IAAI;IAAEb,UAAU;IAAEP;EAAO,CAAC,GAAGoB,KAAK;EAClCb,UAAU,CAACW,UAAU,CAAC,CAAC;EACvB,IAAI,CAACL,QAAQ,CAACO,KAAK,EAAEpB,MAAM,CAAC;AAChC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
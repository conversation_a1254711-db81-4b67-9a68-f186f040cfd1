{"ast": null, "code": "import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function () {\n  return root.Date.now();\n};\nexport default now;", "map": {"version": 3, "names": ["root", "now", "Date"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/now.js"], "sourcesContent": ["import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nexport default now;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAG,SAAAA,CAAA,EAAW;EACnB,OAAOD,IAAI,CAACE,IAAI,CAACD,GAAG,CAAC,CAAC;AACxB,CAAC;AAED,eAAeA,GAAG"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
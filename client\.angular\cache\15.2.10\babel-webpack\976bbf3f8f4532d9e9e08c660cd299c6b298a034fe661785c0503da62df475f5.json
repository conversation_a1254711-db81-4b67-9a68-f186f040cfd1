{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AppConfig } from 'app/app-config';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport { ValidatorComponent } from './validator/validator.component';\nimport { DataTableDirective } from 'angular-datatables';\nimport { Subject } from 'rxjs';\nimport moment from 'moment';\nimport { ModalSuitableGroupsComponent } from './modal-suitable-groups/modal-suitable-groups.component';\nimport { ModalInputCancelReasonComponent } from './modal-input-cancel-reason/modal-input-cancel-reason.component';\nimport { ModalChangeClubComponent } from './modal-change-club/modal-change-club.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"app/services/registration.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"app/services/season.service\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"@angular/fire/compat/messaging\";\nimport * as i9 from \"app/services/club.service\";\nimport * as i10 from \"app/services/fcm.service\";\nimport * as i11 from \"app/services/loading.service\";\nimport * as i12 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i13 from \"app/services/payment.service\";\nimport * as i14 from \"app/services/commons.service\";\nimport * as i15 from \"app/services/settings.service\";\nimport * as i16 from \"@angular/platform-browser\";\nimport * as i17 from \"app/services/export.service\";\nimport * as i18 from \"@angular/common\";\nimport * as i19 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i20 from \"@angular/forms\";\nimport * as i21 from \"angular-datatables\";\nimport * as i22 from \"app/layout/components/content-header/content-header.component\";\nimport * as i23 from \"@ng-select/ng-select\";\nimport * as i24 from \"../../components/editor-sidebar/editor-sidebar.component\";\nconst _c0 = [\"modalValidator\"];\nfunction AdminRegistrationComponent_ng_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 2, season_r1.name), \" \");\n  }\n}\nexport class AdminRegistrationComponent {\n  constructor(route, _http, _registrationService, _translateService, modalService, seasonService, _toastService, _angularFire, _clubService, _fcmService, _loadingService, _coreSidebarService, _paymentService, _commonsService, _settingsService, _titleService, _exportService) {\n    this.route = route;\n    this._http = _http;\n    this._registrationService = _registrationService;\n    this._translateService = _translateService;\n    this.modalService = modalService;\n    this.seasonService = seasonService;\n    this._toastService = _toastService;\n    this._angularFire = _angularFire;\n    this._clubService = _clubService;\n    this._fcmService = _fcmService;\n    this._loadingService = _loadingService;\n    this._coreSidebarService = _coreSidebarService;\n    this._paymentService = _paymentService;\n    this._commonsService = _commonsService;\n    this._settingsService = _settingsService;\n    this._titleService = _titleService;\n    this._exportService = _exportService;\n    this.seasons = [];\n    this.seasonId = 0;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.table_name = 'registration_table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: 'Create new registration',\n        edit: 'Edit registration',\n        remove: 'Delete registration'\n      },\n      url: `${environment.apiUrl}/registrations/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'club_id',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Edit club'),\n        placeholder: this._translateService.instant('Select Club'),\n        required: true,\n        // selected club\n        valueProp: 'id',\n        labelProp: 'name',\n        options: [{\n          id: 1,\n          name: 'Loading...'\n        }]\n      },\n      //selected club\n      defaultValue: 1,\n      className: ''\n    }];\n    this.customFields = [];\n    this.initSettings = {};\n    this.is_validate_required = true;\n    this.subscriptions = [];\n    this._titleService.setTitle('Registrations');\n  }\n  ngOnInit() {\n    $.fx.off = true; //this is for disable jquery animation\n    this.setContentHeader();\n    this.subscriptions.push(this._settingsService.customFields.subscribe(customFields => {\n      this.customFields = customFields;\n    }));\n    // this.listenNotification();\n    this._settingsService.getCustomFields();\n    this.getActiveSeasons();\n  }\n  convertCustomFields2Columns(defaultColumns) {\n    let columns = [];\n    let inogreColumns = ['player.photo', 'player.first_name', 'player.last_name', 'club_id', 'player.document_photo'];\n    this.customFields.forEach(customField => {\n      //filter if defaultColumns.data == customField.key => skip\n      let isExist = defaultColumns.filter(defaultColumn => {\n        return defaultColumn.data == customField.key;\n      });\n      if (isExist.length > 0 || inogreColumns.indexOf(customField.key) > -1) {\n        return;\n      }\n      columns.push({\n        data: null,\n        title: this._translateService.instant(customField.props.label),\n        visible: false,\n        render: function (data, type, row) {\n          if (data.hasOwnProperty(customField.key)) {\n            let value = data[customField.key];\n            if (!value) {\n              return '';\n            }\n            return data[customField.key];\n          } else {\n            if (data.custom_fields && data.custom_fields.hasOwnProperty(customField.key)) {\n              let value = data.custom_fields[customField.key];\n              if (!value) {\n                return '';\n              }\n              return value;\n            } else return '';\n          }\n        }\n      });\n    });\n    // merge columns and defaultColumns\n    columns = [...defaultColumns, ...columns];\n    return columns;\n  }\n  initDatatable() {\n    var _this = this;\n    let defaultColumns = [{\n      data: 'id',\n      title: this._translateService.instant('ID'),\n      visible: false\n    }, {\n      sortable: false,\n      data: 'player.photo',\n      title: this._translateService.instant('Photo'),\n      render: function (data, type, row) {\n        //create image\n        let img = document.createElement('img');\n        img.src = data;\n        img.id = `img-${row.id}`;\n        img.style.width = '50px';\n        img.style.height = 'auto';\n        img.style.objectFit = 'cover';\n        img.style.backgroundColor = '#fff';\n        img.style.objectFit = 'cover';\n        if (data == null) {\n          img.src = 'assets/images/logo/ezactive_1024x1024.png';\n        }\n        // check get image error\n        img.onerror = function () {\n          img.src = 'assets/images/logo/ezactive_1024x1024.png';\n          // set src by row id\n          $(`#img-${row.id}`).attr('src', img.src);\n        };\n        return type === 'print' ? 'print' : img.outerHTML;\n      }\n    }, {\n      data: 'player.user',\n      title: this._translateService.instant('Player'),\n      className: 'font-weight-bolder',\n      render: function (data, type, row) {\n        console.log(data);\n        return `${data.first_name} ${data.last_name}`;\n      }\n    }, {\n      data: 'player.dob',\n      title: this._translateService.instant('Date of birth'),\n      render: function (data, type, row) {\n        if (data == null) {\n          return ``;\n        }\n        return `<p class=\"m-0\" style=\"min-width: max-content\">${data}</p>`;\n      }\n    }, {\n      data: 'player.gender',\n      title: this._translateService.instant('Gender'),\n      render: (data, type, row) => {\n        if (data) {\n          return data == 'Male' ? `<span class=\"badge badge-light-info\">${this._translateService.instant('Male')}</span>` : `<span class=\"badge badge-light-warning\"> ${this._translateService.instant('Female')}</span>`;\n        } else {\n          return ``;\n        }\n      }\n    }, {\n      sortable: false,\n      data: 'player.primary_guardian.user',\n      title: this._translateService.instant('Parent name'),\n      visible: false,\n      render: (data, type, row) => {\n        return `${data.first_name} ${data.last_name}`;\n      }\n    }, {\n      sortable: false,\n      data: 'player.primary_guardian.user.email',\n      title: this._translateService.instant('Parent email'),\n      visible: false\n    }, {\n      data: 'club',\n      title: this._translateService.instant('Club'),\n      render: (data, type, row) => {\n        return data == null ? '' : `${data.name}`;\n      }\n    }, {\n      data: 'registered_date',\n      title: this._translateService.instant('Reg_Date'),\n      render(data, type, row) {\n        if (data) {\n          return moment.utc(data).local().format('YYYY-MM-DD HH:mm:ss');\n        }\n      }\n    }, {\n      data: 'approved_date',\n      visible: false,\n      title: this._translateService.instant('App_Date'),\n      render(data, type, row) {\n        if (data) {\n          return moment.utc(data).local().format('YYYY-MM-DD HH:mm:ss');\n        } else {\n          return '';\n        }\n      }\n    }, {\n      sortable: false,\n      data: 'assigned_groups',\n      title: this._translateService.instant('Groups'),\n      render: (data, type, row) => {\n        const groupNames = data.map(assigned_groups => assigned_groups.group.name).join(', ');\n        return groupNames;\n      }\n    }, {\n      sortable: false,\n      data: 'approval_status',\n      title: this._translateService.instant('Approval Status'),\n      render: (data, type, row) => {\n        let registered_text = this._translateService.instant('Registered');\n        let pending_text = this._translateService.instant('Pending');\n        let approved_text = this._translateService.instant('Approved');\n        let rejected_text = this._translateService.instant('Rejected');\n        let cancelled_text = this._translateService.instant('Cancelled');\n        let payment_error_text = this._translateService.instant('Payment Error');\n        if (data == null) {\n          return ``;\n        }\n        switch (data) {\n          case AppConfig.APPROVE_STATUS.Registered:\n            return `<span class=\"badge badge-light-info\">${registered_text}</span>`;\n          case AppConfig.APPROVE_STATUS.Approved:\n            return `<span class=\"badge badge-light-success\">${approved_text}</span>`;\n          case AppConfig.APPROVE_STATUS.Rejected:\n            return `<span class=\"badge badge-light-danger\">${rejected_text}</span>`;\n          case AppConfig.APPROVE_STATUS.Cancelled:\n            return `<span class=\"badge badge-light-dark\">${cancelled_text}</span>`;\n          case AppConfig.APPROVE_STATUS.PaymentError:\n            return `<span class=\"badge badge-light-danger\">${payment_error_text}</span>`;\n          case AppConfig.APPROVE_STATUS.Pending:\n            return `<span class=\"badge badge-light-secondary\">${pending_text}</span>`;\n          default:\n            return `<span class=\"badge badge-light-secondary\">${data.toUpperCase()}</span>`;\n        }\n      }\n    }, {\n      data: 'updated_at',\n      title: this._translateService.instant('Updated at'),\n      visible: false,\n      render(data, type, row) {\n        if (data) {\n          return moment.utc(data).local().format('YYYY-MM-DD HH:mm:ss');\n        } else {\n          return '';\n        }\n      }\n    }];\n    if (this.is_validate_required) {\n      defaultColumns.push({\n        sortable: false,\n        data: 'player.validate_status',\n        title: this._translateService.instant('Validation Status'),\n        render: (data, type, row) => {\n          return this._commonsService.getBadgeValidateStatus(data);\n        }\n      });\n    }\n    if (this._settingsService.initSettingsValue.hasOwnProperty('is_payment_required') && this._settingsService.initSettingsValue.is_payment_required) {\n      // push to length - 2\n      defaultColumns.splice(defaultColumns.length - 2, 0, {\n        sortable: false,\n        data: 'payment_details',\n        title: this._translateService.instant('Payment Status'),\n        render: (data, type, row) => {\n          // filter by id\n          console.log(\"id\", row.id);\n          data = data.filter(payment => {\n            return payment.product_id == row.id;\n          });\n          if (data.length == 0) {\n            return ``;\n          }\n          const weird_invoice = data.filter(payment => {\n            return payment.status.toLowerCase() != AppConfig.PAYMENT_STATUS.paid && payment.status.toLowerCase() != AppConfig.PAYMENT_STATUS.marked_as_paid;\n          });\n          if (weird_invoice.length == 0) {\n            return `<span class=\"badge badge-light-success\">${this._translateService.instant('Paid')}</span>`;\n          } else {\n            return `<span class=\"badge badge-light-warning\">${this._translateService.instant('Sent')}</span>`;\n          }\n        }\n      });\n    }\n    defaultColumns = this.convertCustomFields2Columns(defaultColumns);\n    let buttons = [{\n      // drop down button\n      extend: 'collection',\n      background: false,\n      text: this._translateService.instant('Action'),\n      className: 'action-button',\n      buttons: [{\n        text: `<i class=\"fa-duotone fa-check-double\"></i> ${this._translateService.instant('Approve full')}`,\n        extend: 'selectedSingle',\n        className: 'action-button-item approve-button',\n        action: (e, dt, node, config) => {\n          this._loadingService.show();\n          let data = dt.row({\n            selected: true\n          }).data();\n          this._registrationService.approve(data.id).subscribe(res => {\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: res.message,\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n          }, err => {\n            Swal.fire({\n              title: this._translateService.instant('Error'),\n              text: err.message,\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n          });\n        }\n      }, {\n        // cancel registration\n        text: `<i class=\"fa-duotone fa-trash\">&nbsp;</i> ${this._translateService.instant('Cancel registration')}`,\n        extend: 'selectedSingle',\n        className: 'action-button-item cancel-button',\n        action: (e, dt, node, config) => {\n          let data = dt.row({\n            selected: true\n          }).data();\n          console.log(data.approval_status);\n          if (data.approval_status == AppConfig.APPROVE_STATUS.Cancelled) {\n            // Toast\n            this._toastService.warning(this._translateService.instant('Already cancelled'));\n            return;\n          } else if (data.approval_status == AppConfig.APPROVE_STATUS.Approved) {\n            // Toast\n            this._toastService.warning(this._translateService.instant('Cannot cancel approved registration'));\n            return;\n          }\n          // open modal to confirm and input reason\n          const modalRef = this.modalService.open(ModalInputCancelReasonComponent, {\n            size: 'lg',\n            backdrop: 'static',\n            keyboard: true\n          });\n          modalRef.componentInstance.windowClass = 'modal-center';\n          modalRef.componentInstance.backdrop = 'static';\n          modalRef.componentInstance.registrations = data;\n          modalRef.result.then(result => {\n            this._loadingService.show();\n            this._registrationService.cancel(data.id, result.reason).subscribe(res => {\n              this._loadingService.dismiss();\n              Swal.fire({\n                title: this._translateService.instant('Success'),\n                text: res.message,\n                icon: 'success',\n                confirmButtonText: this._translateService.instant('OK'),\n                customClass: {\n                  confirmButton: 'btn btn-primary'\n                }\n              });\n              this.dtElement.dtInstance.then(dtInstance => {\n                dtInstance.ajax.reload();\n              });\n            }, err => {\n              Swal.fire({\n                title: this._translateService.instant('Error'),\n                text: err.message,\n                icon: 'error',\n                confirmButtonText: this._translateService.instant('OK'),\n                customClass: {\n                  confirmButton: 'btn btn-primary'\n                }\n              });\n            });\n          }, reason => {\n            // dismiss\n            console.log('reason', reason);\n          });\n        }\n      }, {\n        // button add new group\n        text: `<i class=\"fa-duotone fa-user-plus\"></i> ${this._translateService.instant('Assign New Group')}`,\n        extend: 'selectedSingle',\n        className: 'action-button-item',\n        action: (e, dt, node, config) => {\n          // Add your code here for handling the button click event\n          const data = dt.row({\n            selected: true\n          }).data();\n          const modalRef = this.modalService.open(ModalSuitableGroupsComponent, {\n            size: 'lg',\n            backdrop: 'static',\n            keyboard: true\n          });\n          modalRef.componentInstance.windowClass = 'modal-center';\n          modalRef.componentInstance.registrations = data;\n          modalRef.result.then(result => {\n            if (result) {\n              this._loadingService.show();\n              const registration_id = data.id;\n              const group_id = result.group_id;\n              this._registrationService.assignNewGroup(registration_id, group_id).subscribe(res => {\n                Swal.fire({\n                  title: this._translateService.instant('Success'),\n                  text: res.message,\n                  icon: 'success',\n                  confirmButtonText: this._translateService.instant('OK'),\n                  customClass: {\n                    confirmButton: 'btn btn-primary'\n                  }\n                });\n                this.dtElement.dtInstance.then(dtInstance => {\n                  dtInstance.ajax.reload();\n                });\n              }, err => {\n                Swal.fire({\n                  title: this._translateService.instant('Error'),\n                  text: err.message,\n                  icon: 'error',\n                  confirmButtonText: this._translateService.instant('OK'),\n                  customClass: {\n                    confirmButton: 'btn btn-primary'\n                  }\n                });\n              });\n            }\n          });\n        }\n      }, {\n        extend: 'excel',\n        text: `<i class=\"fa-regular fa-file-excel\"></i> ${this._translateService.instant('Export to Excel')}`,\n        className: 'action-button-item',\n        exportOptions: {\n          columns: ':visible',\n          orthogonal: 'excel',\n          modifier: {\n            order: 'current',\n            page: 'all',\n            selected: false\n          },\n          customize: function (doc) {\n            doc.styles.tableBodyEven.alignment = 'left';\n            doc.styles.tableBodyOdd.alignment = 'left';\n          }\n        },\n        action: function () {\n          var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n            const data = dt.buttons.exportData();\n            yield _this._exportService.exportExcel(data, 'Registrations.xlsx');\n          });\n          return function action(_x, _x2, _x3, _x4) {\n            return _ref.apply(this, arguments);\n          };\n        }()\n      }, {\n        extend: 'print',\n        text: `<i class=\"fa-regular fa-print\"></i> ${this._translateService.instant('Print')}`,\n        className: 'action-button-item',\n        exportOptions: {\n          columns: ':visible',\n          orthogonal: 'print',\n          modifier: {\n            order: 'current',\n            page: 'all',\n            selected: false\n          }\n        },\n        action: function () {\n          var _ref2 = _asyncToGenerator(function* (e, dt, button, config) {\n            const data = dt.buttons.exportData();\n            yield _this._exportService.exportPDF(data, 'Registrations.pdf');\n          });\n          return function action(_x5, _x6, _x7, _x8) {\n            return _ref2.apply(this, arguments);\n          };\n        }()\n      }, {\n        text: `<i class=\"fa-regular fa-rotate\"></i> ${this._translateService.instant('Sync Payment Status')}`,\n        className: 'action-button-item approve-button',\n        action: (e, dt, node, config) => {\n          this._loadingService.show();\n          // selected rows\n          const row_data = dt.rows({\n            selected: true\n          }).data();\n          const payment_detail = row_data[0].payment_details;\n          if (payment_detail.length == 0) {\n            Swal.fire({\n              title: this._translateService.instant('Warning'),\n              text: this._translateService.instant('This registration has no invoice to sync status'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            this._loadingService.dismiss();\n            return;\n          }\n          const payment_ids = payment_detail.map(item => item.id);\n          this._paymentService.syncPayments(payment_ids).subscribe(res => {\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: \"Payment status synced\",\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            this.dtElement.dtInstance.then(dtInstance => {\n              dtInstance.ajax.reload();\n            });\n          });\n        }\n      }, {\n        text: `<i class=\"fa-sharp fa-light fa-calendar-range\"></i> ${this._translateService.instant('Send Reminder')}`,\n        extend: 'selectedSingle',\n        className: 'action-button-item approve-button',\n        action: (e, dt, node, config) => {\n          this._loadingService.show();\n          let data = dt.row({\n            selected: true\n          }).data();\n          this._registrationService.sendReminderPayment(data.id).subscribe(res => {\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: res.message,\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            this.dtElement.dtInstance.then(dtInstance => {\n              dtInstance.ajax.reload();\n            });\n          }, err => {\n            Swal.fire({\n              title: this._translateService.instant('Error'),\n              text: err.message,\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n          });\n        }\n      }]\n    }, {\n      text: '<i class=\"fas fa-edit mr-1\"></i> ' + this._translateService.instant('Edit Club'),\n      titleAttr: this._translateService.instant('Edit Club'),\n      action: (e, dt, node, config) => {\n        let selectedRows = dt.rows({\n          selected: true\n        }).data();\n        if (selectedRows.length == 0) {\n          Swal.fire({\n            title: 'Please select at least one player',\n            icon: 'warning'\n          });\n          return;\n        }\n        let approveStatus = selectedRows[0].approval_status;\n        console.log(\"approveStatus\", selectedRows);\n        if (approveStatus == AppConfig.APPROVE_STATUS.Approved) {\n          Swal.fire({\n            title: this._translateService.instant('This registration is already approved, cannot edit'),\n            icon: 'warning'\n          });\n          return;\n        }\n        // open modal change club\n        // const clubs\n        this.getAllClubsIsActive().then(clubs => {\n          console.log(clubs);\n          const modalRef = this.modalService.open(ModalChangeClubComponent, {\n            size: 'md',\n            backdrop: 'static',\n            keyboard: false\n          });\n          modalRef.componentInstance.clubs = clubs;\n          modalRef.componentInstance.current_club = selectedRows[0].club;\n          modalRef.result.then(result => {\n            console.log(result);\n            this._loadingService.show();\n            this._registrationService.changeClub(selectedRows[0].id, result.club_id).subscribe(res => {\n              this._loadingService.dismiss();\n              this.reloadDataTable();\n            });\n          });\n        });\n      },\n      extend: 'selected'\n    }, {\n      // column\n      extend: 'colvis',\n      text: this._translateService.instant('Column')\n    }];\n    if (this.is_validate_required) {\n      // add validate button to index 1\n      buttons.splice(1, 0, {\n        // Validate button\n        text: '<i class=\"fa-duotone fa-check-double\"></i> ' + this._translateService.instant('Validate'),\n        action: (e, dt, node, config) => {\n          let selectedRows = dt.rows({\n            selected: true\n          }).data();\n          let registrations = [];\n          selectedRows.each(row => {\n            registrations.push(row);\n          });\n          console.log(\"selected row\", selectedRows);\n          let data = {\n            registrations: registrations,\n            status: selectedRows[0].player.validate_status\n          };\n          const cancel_status = data.registrations.some(reg => reg.approval_status == AppConfig.APPROVE_STATUS.Cancelled);\n          if (cancel_status) {\n            Swal.fire({\n              title: this._translateService.instant('Warning'),\n              text: this._translateService.instant('Cannot validate cancelled registration'),\n              icon: 'warning',\n              confirmButtonText: this._translateService.instant('OK'),\n              customClass: {\n                confirmButton: 'btn btn-primary'\n              }\n            });\n            return;\n          }\n          if (data.status == AppConfig.VALIDATE_STATUS.Updated || data.status == AppConfig.VALIDATE_STATUS.Pending) {\n            this.modalValidatorOpen(data);\n          } else {\n            Swal.fire({\n              title: this._translateService.instant('Only \"Updated\" and \"Pending\" status can be validate'),\n              icon: 'warning'\n            });\n          }\n        },\n        extend: 'selected'\n      });\n    }\n    this.dtOptions = {\n      dom: 'B<\"row ml-2 mr-2\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-2 pr-2\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\n      select: 'single',\n      serverSide: true,\n      processing: true,\n      rowId: 'id',\n      stateSave: true,\n      ajax: (dataTablesParameters, callback) => {\n        dataTablesParameters['page_size'] = dataTablesParameters.length;\n        dataTablesParameters['page'] = Math.floor(dataTablesParameters.start / dataTablesParameters.length) + 1;\n        dataTablesParameters['season_id'] = this.seasonId;\n        dataTablesParameters['type'] = 'registration';\n        // Determine sort column based on index\n        const sortColumnIndex = dataTablesParameters.order[0].column;\n        const sortDirection = dataTablesParameters.order[0].dir;\n        const payload = {\n          season_id: dataTablesParameters['season_id'],\n          type: dataTablesParameters['type'],\n          page: dataTablesParameters['page'],\n          page_size: dataTablesParameters['page_size'],\n          search: dataTablesParameters.search.value,\n          sort_column: dataTablesParameters.columns[sortColumnIndex].data,\n          sort_direction: sortDirection // Sort direction\n        };\n\n        this._http.post(`${environment.apiUrl}/registrations/by-season-and-type`, payload).subscribe(resp => {\n          this._loadingService.dismiss();\n          if (resp && resp.recordsTotal !== undefined && resp.recordsFiltered !== undefined) {\n            callback({\n              recordsTotal: resp.recordsTotal,\n              recordsFiltered: resp.recordsFiltered,\n              data: resp.data\n            });\n          } else {\n            console.error('Error: Server response lacks required pagination data.');\n          }\n        });\n      },\n      pagingType: 'simple_numbers',\n      language: this._commonsService.dataTableDefaults.lang,\n      paging: true,\n      paginate: {\n        first: this._translateService.instant('First'),\n        last: this._translateService.instant('Last'),\n        next: this._translateService.instant('Next'),\n        previous: this._translateService.instant('Previous')\n      },\n      lengthMenu: [10, 25, 50, 100],\n      pageLength: 10,\n      order: [7, 'desc'],\n      responsive: true,\n      scrollX: false,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: 1\n      }, {\n        responsivePriority: 2,\n        targets: 9\n      }, {\n        responsivePriority: 3,\n        targets: 0\n      }],\n      columns: defaultColumns,\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: buttons\n      }\n    };\n  }\n  listenNotification() {\n    // if(this._fcmService.isPushNotificationsAvailable){\n    //   PushNotifications.addListener('pushNotificationReceived', (notification) => {\n    //     console.log('pushNotificationReceived', notification);\n    //     this.reloadDataTable();\n    //   })\n    // }else{\n    //    this._fcmService.currentMessage.subscribe((message) => {\n    //     console.log('New message received:', message);\n    //     this.reloadDataTable();\n    //   })\n    // }\n  }\n  setContentHeader() {\n    this.contentHeader = {\n      headerTitle: this._translateService.instant('All Registrations'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Registrations',\n          isLink: false\n        }, {\n          name: this._translateService.instant('All Registrations'),\n          isLink: false\n        }]\n      }\n    };\n  }\n  onSuccess($event) {\n    console.log('onSuccess', $event);\n    this.reloadDataTable();\n  }\n  getActiveSeasons() {\n    let status = 'active';\n    this.seasonService.getSeasons(status).subscribe(data => {\n      this.seasons = data;\n      this.season = this.seasons[0];\n      this.seasonId = this.season.id;\n      if (this._settingsService.initSettingsValue && this._settingsService.initSettingsValue.hasOwnProperty('is_validate_required')) {\n        this.is_validate_required = this._settingsService.initSettingsValue.is_validate_required;\n      }\n      this.initDatatable();\n      this.dtTrigger.next(this.dtOptions);\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onChangeSeason($event) {\n    console.log('onChangeSeason', $event);\n    this.season = $event;\n    this.seasonId = this.season.id;\n    this.reloadDataTable();\n  }\n  reloadDataTable() {\n    this._loadingService.show();\n    console.log('reloadDataTable');\n    if (this.dtElement && this.dtElement.dtInstance) this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n  }\n  modalValidatorOpen(data) {\n    const modalRef = this.modalService.open(ValidatorComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: true\n    });\n    console.log(\"data\", data);\n    modalRef.componentInstance.registrations = data.registrations[0];\n    modalRef.componentInstance.status = data.status;\n    modalRef.componentInstance.dtElement = this.dtElement;\n    //    let registration_id = data.registrations[0].id;\n    // modalRef.result.then(\n    //   (result) => {\n    //     this._registrationService.validateRegistration(registration_id,result).toPromise().then((data) => {\n    //       console.log('data', data);\n    //       this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\n    //         dtInstance.ajax.reload();\n    //       });\n    //     });\n    //   }\n    // );\n  }\n\n  showClubName(row) {\n    return new Promise((resolve, reject) => {\n      let selectedValidateStatus = row.validate_status;\n      let selectedClub = row.club_id;\n      this.getAllClubsIsActive().then(data => {\n        this.fields.forEach(field => {\n          if (field.key == 'club_id') {\n            field.templateOptions.options = data;\n            field.defaultValue = selectedClub;\n            resolve(true);\n          }\n        });\n      });\n    });\n  }\n  getAllClubsIsActive() {\n    return new Promise((resolve, reject) => {\n      this._clubService.getAllClubsIsActive().toPromise().then(data => {\n        resolve(data);\n      });\n    });\n  }\n  editor(action, row) {\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    if (action == 'edit' && row) {\n      let approveStatus = row.approval_status;\n      if (approveStatus == AppConfig.APPROVE_STATUS.Approved) {\n        Swal.fire({\n          title: this._translateService.instant('This registration is already approved, cannot edit'),\n          icon: 'warning'\n        });\n        return;\n      }\n    }\n    this.showClubName(row).then(data => {\n      if (data) {\n        this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n      }\n    });\n  }\n  getSeason(id) {\n    return new Promise((resolve, reject) => {\n      this._registrationService.getSeasonByID(id).subscribe(data => {\n        resolve(data);\n      });\n    });\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.subscriptions.forEach(subscription => subscription.unsubscribe());\n  }\n  static #_ = this.ɵfac = function AdminRegistrationComponent_Factory(t) {\n    return new (t || AdminRegistrationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.RegistrationService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.SeasonService), i0.ɵɵdirectiveInject(i7.ToastrService), i0.ɵɵdirectiveInject(i8.AngularFireMessaging), i0.ɵɵdirectiveInject(i9.ClubService), i0.ɵɵdirectiveInject(i10.FcmService), i0.ɵɵdirectiveInject(i11.LoadingService), i0.ɵɵdirectiveInject(i12.CoreSidebarService), i0.ɵɵdirectiveInject(i13.PaymentService), i0.ɵɵdirectiveInject(i14.CommonsService), i0.ɵɵdirectiveInject(i15.SettingsService), i0.ɵɵdirectiveInject(i16.Title), i0.ɵɵdirectiveInject(i17.ExportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AdminRegistrationComponent,\n    selectors: [[\"app-admin-registration\"]],\n    viewQuery: function AdminRegistrationComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalValidator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 22,\n    vars: 20,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\", \"mb-1\"], [1, \"col\"], [\"for\", \"season\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"], [3, \"value\"]],\n    template: function AdminRegistrationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"ng-select\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function AdminRegistrationComponent_Template_ng_select_ngModelChange_8_listener($event) {\n          return ctx.season = $event;\n        })(\"change\", function AdminRegistrationComponent_Template_ng_select_change_8_listener($event) {\n          return ctx.onChangeSeason($event);\n        });\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵtemplate(10, AdminRegistrationComponent_ng_option_10_Template, 3, 4, \"ng-option\", 7);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"div\", 11)(15, \"h4\", 12);\n        i0.ɵɵtext(16);\n        i0.ɵɵpipe(17, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\");\n        i0.ɵɵelement(19, \"table\", 13);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(20, \"core-sidebar\", 14);\n        i0.ɵɵelement(21, \"app-editor-sidebar\", 15);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 14, \"Season\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(9, 16, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.season);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 18, \"Registration List\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    dependencies: [i18.NgForOf, i19.CoreSidebarComponent, i20.NgControlStatus, i20.NgModel, i21.DataTableDirective, i22.ContentHeaderComponent, i23.NgSelectComponent, i23.ɵr, i24.EditorSidebarComponent, i4.TranslatePipe],\n    styles: [\".modal-center[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal-center[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\n  margin: 0 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYWRtaW4tcmVnaXN0cmF0aW9uL2FkbWluLXJlZ2lzdHJhdGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLHdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQUNGOztBQUVBO0VBQ0Usb0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5tb2RhbC1jZW50ZXIge1xyXG4gIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG59XHJcblxyXG4ubW9kYWwtY2VudGVyIC5tb2RhbC1kaWFsb2cge1xyXG4gIG1hcmdpbjogMCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4gIFxyXG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAaA,SAAQA,SAAS,QAAO,gBAAgB;AAGxC,SAAQC,WAAW,QAAO,0BAA0B;AACpD,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAAQC,kBAAkB,QAAO,iCAAiC;AAClE,SAAQC,kBAAkB,QAAO,oBAAoB;AAMrD,SAAQC,OAAO,QAAqB,MAAM;AAE1C,OAAOC,MAAM,MAAM,QAAQ;AAG3B,SAAQC,4BAA4B,QAAO,yDAAyD;AACpG,SAAQC,+BAA+B,QAAO,iEAAiE;AAQ/G,SAAQC,wBAAwB,QAAO,iDAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC7B9EC,EAAA,CAAAC,cAAA,oBAA2D;IAAAD,EAAA,CAAAE,MAAA,GAC3D;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAD8BH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB;IAACL,EAAA,CAAAM,SAAA,GAC3D;IAD2DN,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAQ,WAAA,OAAAH,SAAA,CAAAI,IAAA,OAC3D;;;ADoCV,OAAM,MAAOC,0BAA0B;EAqDnCC,YACYC,KAAqB,EACrBC,KAAiB,EACjBC,oBAAyC,EAC1CC,iBAAmC,EACnCC,YAAsB,EACtBC,aAA4B,EAC5BC,aAA4B,EAC3BC,YAAkC,EACnCC,YAAyB,EACxBC,WAAuB,EACxBC,eAA+B,EAC/BC,mBAAuC,EACvCC,eAA+B,EAC/BC,eAA+B,EAC/BC,gBAAiC,EACjCC,aAAoB,EACnBC,cAA6B;IAhB7B,KAAAhB,KAAK,GAALA,KAAK;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IAnE1B,KAAAC,OAAO,GAAa,EAAE;IAEtB,KAAAC,QAAQ,GAAW,CAAC;IAIpB,KAAAC,SAAS,GAAQrC,kBAAkB;IACnC,KAAAsC,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAItC,OAAO,EAAe;IAE5D,KAAAuC,UAAU,GAAG,oBAAoB;IACjC,KAAAC,MAAM,GAAQ;MACVC,SAAS,EAAE,IAAI,CAACF,UAAU;MAC1BG,KAAK,EAAE;QACHC,MAAM,EAAE,yBAAyB;QACjCC,IAAI,EAAE,mBAAmB;QACzBC,MAAM,EAAE;OACX;MACDC,GAAG,EAAE,GAAGlD,WAAW,CAACmD,MAAM,uBAAuB;MACjDC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACX;IACM,KAAAC,MAAM,GAAU,CACnB;MACIC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAAClC,iBAAiB,CAACmC,OAAO,CAAC,WAAW,CAAC;QAClDC,WAAW,EAAE,IAAI,CAACpC,iBAAiB,CAACmC,OAAO,CAAC,aAAa,CAAC;QAC1DE,QAAQ,EAAE,IAAI;QAEd;QACAC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,CACL;UACIC,EAAE,EAAE,CAAC;UACL/C,IAAI,EAAE;SACT;OAER;MACD;MACAgD,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE;KACd,CACJ;IACD,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,YAAY,GAAQ,EAAE;IACtB,KAAAC,oBAAoB,GAAY,IAAI;IAwBpC,KAAAC,aAAa,GAAmB,EAAE;IAH9B,IAAI,CAACnC,aAAa,CAACoC,QAAQ,CAAC,eAAe,CAAC;EAChD;EAIAC,QAAQA,CAAA;IACJC,CAAC,CAACC,EAAE,CAACC,GAAG,GAAG,IAAI,CAAC,CAAC;IACjB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACN,aAAa,CAACO,IAAI,CACnB,IAAI,CAAC3C,gBAAgB,CAACiC,YAAY,CAACW,SAAS,CAAEX,YAAY,IAAI;MAC1D,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC,CAAC,CAAC,CACL;IAED;IAEA,IAAI,CAACjC,gBAAgB,CAAC6C,eAAe,EAAE;IAEvC,IAAI,CAACC,gBAAgB,EAAE;EAC3B;EAEAC,2BAA2BA,CAACC,cAAqB;IAC7C,IAAIC,OAAO,GAAU,EAAE;IACvB,IAAIC,aAAa,GAAG,CAChB,cAAc,EACd,mBAAmB,EACnB,kBAAkB,EAClB,SAAS,EACT,uBAAuB,CAC1B;IACD,IAAI,CAACjB,YAAY,CAACkB,OAAO,CAAEC,WAAW,IAAI;MACtC;MACA,IAAIC,OAAO,GAAGL,cAAc,CAACM,MAAM,CAAEC,aAAa,IAAI;QAClD,OAAOA,aAAa,CAACC,IAAI,IAAIJ,WAAW,CAAChC,GAAG;MAChD,CAAC,CAAC;MACF,IAAIiC,OAAO,CAACI,MAAM,GAAG,CAAC,IAAIP,aAAa,CAACQ,OAAO,CAACN,WAAW,CAAChC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACnE;;MAEJ6B,OAAO,CAACN,IAAI,CAAC;QACTa,IAAI,EAAE,IAAI;QACV7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC4B,WAAW,CAAC9B,KAAK,CAACC,KAAK,CAAC;QAC9DoC,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,SAAAA,CAAUJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;UAC5C,IAAIL,IAAI,CAACM,cAAc,CAACV,WAAW,CAAChC,GAAG,CAAC,EAAE;YACtC,IAAI2C,KAAK,GAAGP,IAAI,CAACJ,WAAW,CAAChC,GAAG,CAAC;YACjC,IAAI,CAAC2C,KAAK,EAAE;cACR,OAAO,EAAE;;YAEb,OAAOP,IAAI,CAACJ,WAAW,CAAChC,GAAG,CAAC;WAC/B,MAAM;YACH,IACIoC,IAAI,CAACQ,aAAa,IAClBR,IAAI,CAACQ,aAAa,CAACF,cAAc,CAACV,WAAW,CAAChC,GAAG,CAAC,EACpD;cACE,IAAI2C,KAAK,GAAGP,IAAI,CAACQ,aAAa,CAACZ,WAAW,CAAChC,GAAG,CAAC;cAC/C,IAAI,CAAC2C,KAAK,EAAE;gBACR,OAAO,EAAE;;cAEb,OAAOA,KAAK;aACf,MAAM,OAAO,EAAE;;QAExB;OACH,CAAC;IACN,CAAC,CAAC;IAEF;IACAd,OAAO,GAAG,CAAC,GAAGD,cAAc,EAAE,GAAGC,OAAO,CAAC;IACzC,OAAOA,OAAO;EAClB;EAEAgB,aAAaA,CAAA;IAAA,IAAAC,KAAA;IACT,IAAIlB,cAAc,GAAG,CACjB;MAACQ,IAAI,EAAE,IAAI;MAAE7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;MAAEmC,OAAO,EAAE;IAAK,CAAC,EACzE;MACIQ,QAAQ,EAAE,KAAK;MACfX,IAAI,EAAE,cAAc;MACpB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,OAAO,CAAC;MAC9CoC,MAAM,EAAE,SAAAA,CAAUJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;QAC5C;QACA,IAAIO,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACvCF,GAAG,CAACG,GAAG,GAAGf,IAAI;QACdY,GAAG,CAACtC,EAAE,GAAG,OAAO+B,GAAG,CAAC/B,EAAE,EAAE;QACxBsC,GAAG,CAACI,KAAK,CAACC,KAAK,GAAG,MAAM;QACxBL,GAAG,CAACI,KAAK,CAACE,MAAM,GAAG,MAAM;QACzBN,GAAG,CAACI,KAAK,CAACG,SAAS,GAAG,OAAO;QAC7BP,GAAG,CAACI,KAAK,CAACI,eAAe,GAAG,MAAM;QAClCR,GAAG,CAACI,KAAK,CAACG,SAAS,GAAG,OAAO;QAC7B,IAAInB,IAAI,IAAI,IAAI,EAAE;UACdY,GAAG,CAACG,GAAG,GAAG,2CAA2C;;QAEzD;QACAH,GAAG,CAACS,OAAO,GAAG;UACVT,GAAG,CAACG,GAAG,GAAG,2CAA2C;UACrD;UACAhC,CAAC,CAAC,QAAQsB,GAAG,CAAC/B,EAAE,EAAE,CAAC,CAACgD,IAAI,CAAC,KAAK,EAAEV,GAAG,CAACG,GAAG,CAAC;QAC5C,CAAC;QACD,OAAOlD,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG+C,GAAG,CAACW,SAAS;MACrD;KACH,EACD;MACIvB,IAAI,EAAE,aAAa;MACnB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,QAAQ,CAAC;MAC/CQ,SAAS,EAAE,oBAAoB;MAC/B4B,MAAM,EAAE,SAAAA,CAAUJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;QAC5CmB,OAAO,CAACC,GAAG,CAACzB,IAAI,CAAC;QACjB,OAAO,GAAGA,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC2B,SAAS,EAAE;MACjD;KACH,EACD;MACI3B,IAAI,EAAE,YAAY;MAClB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,eAAe,CAAC;MACtDoC,MAAM,EAAE,SAAAA,CAAUJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;QAC5C,IAAIL,IAAI,IAAI,IAAI,EAAE;UACd,OAAO,EAAE;;QAEb,OAAO,iDAAiDA,IAAI,MAAM;MACtE;KACH,EACD;MACIA,IAAI,EAAE,eAAe;MACrB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,QAAQ,CAAC;MAC/CoC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;QACvC,IAAIL,IAAI,EAAE;UACN,OAAOA,IAAI,IAAI,MAAM,GACf,wCAAwC,IAAI,CAACnE,iBAAiB,CAACmC,OAAO,CACpE,MAAM,CACT,SAAS,GACR,4CAA4C,IAAI,CAACnC,iBAAiB,CAACmC,OAAO,CACxE,QAAQ,CACX,SAAS;SACjB,MAAM;UACH,OAAO,EAAE;;MAEjB;KACH,EACD;MACI2C,QAAQ,EAAE,KAAK;MACfX,IAAI,EAAE,8BAA8B;MACpC7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,aAAa,CAAC;MACpDmC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;QACvC,OAAO,GAAGL,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC2B,SAAS,EAAE;MACjD;KACH,EACD;MACIhB,QAAQ,EAAE,KAAK;MACfX,IAAI,EAAE,oCAAoC;MAC1C7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,cAAc,CAAC;MACrDmC,OAAO,EAAE;KACZ,EACD;MACIH,IAAI,EAAE,MAAM;MACZ7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,MAAM,CAAC;MAC7CoC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;QACvC,OAAQL,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,GAAGA,IAAI,CAACzE,IAAI,EAAE;MAC9C;KACH,EACD;MACIyE,IAAI,EAAE,iBAAiB;MACvB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,UAAU,CAAC;MAEjDoC,MAAMA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;QACjC,IAAIL,IAAI,EAAE;UACN,OAAOtF,MAAM,CAACkH,GAAG,CAAC5B,IAAI,CAAC,CAAC6B,KAAK,EAAE,CAACC,MAAM,CAAC,qBAAqB,CAAC;;MAErE;KACH,EACD;MACI9B,IAAI,EAAE,eAAe;MACrBG,OAAO,EAAE,KAAK;MACdhD,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,UAAU,CAAC;MACjDoC,MAAMA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;QACjC,IAAIL,IAAI,EAAE;UACN,OAAOtF,MAAM,CAACkH,GAAG,CAAC5B,IAAI,CAAC,CAAC6B,KAAK,EAAE,CAACC,MAAM,CAAC,qBAAqB,CAAC;SAChE,MAAM;UACH,OAAO,EAAE;;MAEjB;KACH,EACD;MACInB,QAAQ,EAAE,KAAK;MACfX,IAAI,EAAE,iBAAiB;MACvB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,QAAQ,CAAC;MAC/CoC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;QACvC,MAAM0B,UAAU,GAAG/B,IAAI,CAACgC,GAAG,CAAEC,eAAoB,IAAKA,eAAe,CAACC,KAAK,CAAC3G,IAAI,CAAC,CAAC4G,IAAI,CAAC,IAAI,CAAC;QAC5F,OAAOJ,UAAU;MACrB;KACH,EACD;MACIpB,QAAQ,EAAE,KAAK;MACfX,IAAI,EAAE,iBAAiB;MACvB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,iBAAiB,CAAC;MACxDoC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;QACvC,IAAI+B,eAAe,GAAG,IAAI,CAACvG,iBAAiB,CAACmC,OAAO,CAAC,YAAY,CAAC;QAClE,IAAIqE,YAAY,GAAG,IAAI,CAACxG,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;QAC5D,IAAIsE,aAAa,GAAG,IAAI,CAACzG,iBAAiB,CAACmC,OAAO,CAAC,UAAU,CAAC;QAC9D,IAAIuE,aAAa,GAAG,IAAI,CAAC1G,iBAAiB,CAACmC,OAAO,CAAC,UAAU,CAAC;QAC9D,IAAIwE,cAAc,GAAG,IAAI,CAAC3G,iBAAiB,CAACmC,OAAO,CAAC,WAAW,CAAC;QAChE,IAAIyE,kBAAkB,GAAG,IAAI,CAAC5G,iBAAiB,CAACmC,OAAO,CAAC,eAAe,CAAC;QACxE,IAAIgC,IAAI,IAAI,IAAI,EAAE;UACd,OAAO,EAAE;;QAEb,QAAQA,IAAI;UACR,KAAK5F,SAAS,CAACsI,cAAc,CAACC,UAAU;YACpC,OAAO,wCAAwCP,eAAe,SAAS;UAC3E,KAAKhI,SAAS,CAACsI,cAAc,CAACE,QAAQ;YAClC,OAAO,2CAA2CN,aAAa,SAAS;UAC5E,KAAKlI,SAAS,CAACsI,cAAc,CAACG,QAAQ;YAClC,OAAO,0CAA0CN,aAAa,SAAS;UAC3E,KAAKnI,SAAS,CAACsI,cAAc,CAACI,SAAS;YACnC,OAAO,wCAAwCN,cAAc,SAAS;UAC1E,KAAKpI,SAAS,CAACsI,cAAc,CAACK,YAAY;YACtC,OAAO,0CAA0CN,kBAAkB,SAAS;UAChF,KAAKrI,SAAS,CAACsI,cAAc,CAACM,OAAO;YACjC,OAAO,6CAA6CX,YAAY,SAAS;UAC7E;YACI,OAAO,6CAA6CrC,IAAI,CAACiD,WAAW,EAAE,SAAS;;MAE3F;KACH,EAED;MACIjD,IAAI,EAAE,YAAY;MAClB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,YAAY,CAAC;MACnDmC,OAAO,EAAE,KAAK;MACdC,MAAMA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ;QACjC,IAAIL,IAAI,EAAE;UACN,OAAOtF,MAAM,CAACkH,GAAG,CAAC5B,IAAI,CAAC,CAAC6B,KAAK,EAAE,CAACC,MAAM,CAAC,qBAAqB,CAAC;SAChE,MAAM;UACH,OAAO,EAAE;;MAEjB;KACH,CACJ;IACD,IAAI,IAAI,CAACnD,oBAAoB,EAAE;MAC3Ba,cAAc,CAACL,IAAI,CAAC;QAChBwB,QAAQ,EAAE,KAAK;QACfX,IAAI,EAAE,wBAAwB;QAC9B7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,mBAAmB,CAAC;QAC1DoC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;UACvC,OAAO,IAAI,CAAC9D,eAAe,CAAC2G,sBAAsB,CAAClD,IAAI,CAAC;QAC5D;OACH,CAAC;;IAGN,IACI,IAAI,CAACxD,gBAAgB,CAAC2G,iBAAiB,CAAC7C,cAAc,CAClD,qBAAqB,CACxB,IACD,IAAI,CAAC9D,gBAAgB,CAAC2G,iBAAiB,CAACC,mBAAmB,EAC7D;MACE;MACA5D,cAAc,CAAC6D,MAAM,CAAC7D,cAAc,CAACS,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;QAChDU,QAAQ,EAAE,KAAK;QACfX,IAAI,EAAE,iBAAiB;QACvB7C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,gBAAgB,CAAC;QACvDoC,MAAM,EAAEA,CAACJ,IAAS,EAAEnC,IAAS,EAAEwC,GAAQ,KAAI;UACvC;UACAmB,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEpB,GAAG,CAAC/B,EAAE,CAAC;UACzB0B,IAAI,GAAGA,IAAI,CAACF,MAAM,CAAEwD,OAAO,IAAI;YAC3B,OAAOA,OAAO,CAACC,UAAU,IAAIlD,GAAG,CAAC/B,EAAE;UACvC,CAAC,CAAC;UACF,IAAI0B,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE;YAClB,OAAO,EAAE;;UAGb,MAAMuD,aAAa,GAAGxD,IAAI,CAACF,MAAM,CAAEwD,OAAO,IAAI;YAC1C,OAAOA,OAAO,CAACG,MAAM,CAACC,WAAW,EAAE,IAAItJ,SAAS,CAACuJ,cAAc,CAACC,IAAI,IAAIN,OAAO,CAACG,MAAM,CAACC,WAAW,EAAE,IAAItJ,SAAS,CAACuJ,cAAc,CAACE,cAAc;UACnJ,CAAC,CAAC;UAEF,IAAIL,aAAa,CAACvD,MAAM,IAAI,CAAC,EAAE;YAC3B,OAAO,2CAA2C,IAAI,CAACpE,iBAAiB,CAACmC,OAAO,CAAC,MAAM,CAAC,SAAS;WACpG,MAAM;YACH,OAAO,2CAA2C,IAAI,CAACnC,iBAAiB,CAACmC,OAAO,CAAC,MAAM,CAAC,SAAS;;QAIzG;OACH,CAAC;;IAGNwB,cAAc,GAAG,IAAI,CAACD,2BAA2B,CAACC,cAAc,CAAC;IACjE,IAAIsE,OAAO,GAAQ,CACf;MACI;MACAC,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAAC,QAAQ,CAAC;MAC9CQ,SAAS,EAAE,eAAe;MAC1BsF,OAAO,EAAE,CACL;QACIG,IAAI,EAAE,8CAA8C,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAC9E,cAAc,CACjB,EAAE;QACH+F,MAAM,EAAE,gBAAgB;QACxBvF,SAAS,EAAE,mCAAmC;QAC9Cd,MAAM,EAAEA,CAACwG,CAAM,EAAEC,EAAO,EAAEC,IAAS,EAAEC,MAAW,KAAI;UAChD,IAAI,CAACjI,eAAe,CAACkI,IAAI,EAAE;UAC3B,IAAItE,IAAI,GAAGmE,EAAE,CAAC9D,GAAG,CAAC;YAACkE,QAAQ,EAAE;UAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;UAC1C,IAAI,CAACpE,oBAAoB,CAAC4I,OAAO,CAACxE,IAAI,CAAC1B,EAAE,CAAC,CAACc,SAAS,CAC/CqF,GAAG,IAAI;YACJnK,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;cAChDiG,IAAI,EAAEQ,GAAG,CAACE,OAAO;cACjBC,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;UAEN,CAAC,EACAC,GAAG,IAAI;YACJ1K,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,OAAO,CAAC;cAC9CiG,IAAI,EAAEe,GAAG,CAACL,OAAO;cACjBC,IAAI,EAAE,OAAO;cACbC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;UACN,CAAC,CACJ;QACL;OACH,EACD;QACI;QACAd,IAAI,EAAE,6CAA6C,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAC7E,qBAAqB,CACxB,EAAE;QACH+F,MAAM,EAAE,gBAAgB;QACxBvF,SAAS,EAAE,kCAAkC;QAC7Cd,MAAM,EAAEA,CAACwG,CAAM,EAAEC,EAAO,EAAEC,IAAS,EAAEC,MAAW,KAAI;UAChD,IAAIrE,IAAI,GAAGmE,EAAE,CAAC9D,GAAG,CAAC;YAACkE,QAAQ,EAAE;UAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;UAE1CwB,OAAO,CAACC,GAAG,CAACzB,IAAI,CAACiF,eAAe,CAAC;UAEjC,IAAIjF,IAAI,CAACiF,eAAe,IAAI7K,SAAS,CAACsI,cAAc,CAACI,SAAS,EAAE;YAC5D;YACA,IAAI,CAAC9G,aAAa,CAACkJ,OAAO,CAAC,IAAI,CAACrJ,iBAAiB,CAACmC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAC/E;WACH,MAAM,IAAIgC,IAAI,CAACiF,eAAe,IAAI7K,SAAS,CAACsI,cAAc,CAACE,QAAQ,EAAE;YAClE;YACA,IAAI,CAAC5G,aAAa,CAACkJ,OAAO,CAAC,IAAI,CAACrJ,iBAAiB,CAACmC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YACjG;;UAGJ;UACA,MAAMmH,QAAQ,GAAG,IAAI,CAACrJ,YAAY,CAACsJ,IAAI,CACnCxK,+BAA+B,EAC/B;YACIyK,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE;WACb,CACJ;UAEDJ,QAAQ,CAACK,iBAAiB,CAACC,WAAW,GAAG,cAAc;UACvDN,QAAQ,CAACK,iBAAiB,CAACF,QAAQ,GAAG,QAAQ;UAE9CH,QAAQ,CAACK,iBAAiB,CAACE,aAAa,GAAG1F,IAAI;UAE/CmF,QAAQ,CAACQ,MAAM,CAACC,IAAI,CACfD,MAAM,IAAI;YACP,IAAI,CAACvJ,eAAe,CAACkI,IAAI,EAAE;YAE3B,IAAI,CAAC1I,oBAAoB,CACpBiK,MAAM,CAAC7F,IAAI,CAAC1B,EAAE,EAAEqH,MAAM,CAACG,MAAM,CAAC,CAC9B1G,SAAS,CACLqF,GAAG,IAAI;cACJ,IAAI,CAACrI,eAAe,CAAC2J,OAAO,EAAE;cAC9BzL,IAAI,CAACoK,IAAI,CAAC;gBACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;gBAChDiG,IAAI,EAAEQ,GAAG,CAACE,OAAO;gBACjBC,IAAI,EAAE,SAAS;gBACfC,iBAAiB,EACb,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;gBACxC8G,WAAW,EAAE;kBACTC,aAAa,EAAE;;eAEtB,CAAC;cACF,IAAI,CAAClI,SAAS,CAACmJ,UAAU,CAACJ,IAAI,CACzBI,UAA0B,IAAI;gBAC3BA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;cAC5B,CAAC,CACJ;YACL,CAAC,EACAlB,GAAG,IAAI;cACJ1K,IAAI,CAACoK,IAAI,CAAC;gBACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,OAAO,CAAC;gBAC9CiG,IAAI,EAAEe,GAAG,CAACL,OAAO;gBACjBC,IAAI,EAAE,OAAO;gBACbC,iBAAiB,EACb,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;gBACxC8G,WAAW,EAAE;kBACTC,aAAa,EAAE;;eAEtB,CAAC;YACN,CAAC,CACJ;UACT,CAAC,EACAe,MAAM,IAAI;YACP;YACAtE,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEqE,MAAM,CAAC;UACjC,CAAC,CACJ;QACL;OACH,EACD;QACI;QACA7B,IAAI,EAAE,2CAA2C,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAC3E,kBAAkB,CACrB,EAAE;QACH+F,MAAM,EAAE,gBAAgB;QACxBvF,SAAS,EAAE,oBAAoB;QAC/Bd,MAAM,EAAEA,CAACwG,CAAM,EAAEC,EAAO,EAAEC,IAAS,EAAEC,MAAW,KAAI;UAChD;UACA,MAAMrE,IAAI,GAAiBmE,EAAE,CAAC9D,GAAG,CAAC;YAACkE,QAAQ,EAAE;UAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;UAE1D,MAAMmF,QAAQ,GAAG,IAAI,CAACrJ,YAAY,CAACsJ,IAAI,CACnCzK,4BAA4B,EAC5B;YACI0K,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE;WACb,CACJ;UAEDJ,QAAQ,CAACK,iBAAiB,CAACC,WAAW,GAAG,cAAc;UAEvDN,QAAQ,CAACK,iBAAiB,CAACE,aAAa,GAAG1F,IAAI;UAE/CmF,QAAQ,CAACQ,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;YAC5B,IAAIA,MAAM,EAAE;cACR,IAAI,CAACvJ,eAAe,CAACkI,IAAI,EAAE;cAC3B,MAAM6B,eAAe,GAAGnG,IAAI,CAAC1B,EAAE;cAC/B,MAAM8H,QAAQ,GAAGT,MAAM,CAACS,QAAQ;cAChC,IAAI,CAACxK,oBAAoB,CACpByK,cAAc,CAACF,eAAe,EAAEC,QAAQ,CAAC,CACzChH,SAAS,CACLqF,GAAG,IAAI;gBACJnK,IAAI,CAACoK,IAAI,CAAC;kBACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;kBAChDiG,IAAI,EAAEQ,GAAG,CAACE,OAAO;kBACjBC,IAAI,EAAE,SAAS;kBACfC,iBAAiB,EACb,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;kBACxC8G,WAAW,EAAE;oBACTC,aAAa,EAAE;;iBAEtB,CAAC;gBACF,IAAI,CAAClI,SAAS,CAACmJ,UAAU,CAACJ,IAAI,CACzBI,UAA0B,IAAI;kBAC3BA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;gBAC5B,CAAC,CACJ;cACL,CAAC,EACAlB,GAAG,IAAI;gBACJ1K,IAAI,CAACoK,IAAI,CAAC;kBACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,OAAO,CAAC;kBAC9CiG,IAAI,EAAEe,GAAG,CAACL,OAAO;kBACjBC,IAAI,EAAE,OAAO;kBACbC,iBAAiB,EACb,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;kBACxC8G,WAAW,EAAE;oBACTC,aAAa,EAAE;;iBAEtB,CAAC;cACN,CAAC,CACJ;;UAEb,CAAC,CAAC;QACN;OACH,EACD;QACIhB,MAAM,EAAE,OAAO;QACfE,IAAI,EAAE,4CAA4C,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAC5E,iBAAiB,CACpB,EAAE;QACHQ,SAAS,EAAE,oBAAoB;QAC/B8H,aAAa,EAAE;UACX7G,OAAO,EAAE,UAAU;UACnB8G,UAAU,EAAE,OAAO;UACnBC,QAAQ,EAAE;YACNC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE,KAAK;YACXnC,QAAQ,EAAE;WACb;UACDoC,SAAS,EAAE,SAAAA,CAAUC,GAAG;YACpBA,GAAG,CAACC,MAAM,CAACC,aAAa,CAACC,SAAS,GAAG,MAAM;YAC3CH,GAAG,CAACC,MAAM,CAACG,YAAY,CAACD,SAAS,GAAG,MAAM;UAC9C;SACH;QACDrJ,MAAM;UAAA,IAAAuJ,IAAA,GAAAC,iBAAA,CAAE,WAAOhD,CAAM,EAAEC,EAAO,EAAEgD,MAAW,EAAE9C,MAAW,EAAI;YACxD,MAAMrE,IAAI,GAAGmE,EAAE,CAACL,OAAO,CAACsD,UAAU,EAAE;YACpC,MAAM1G,KAAI,CAAChE,cAAc,CAAC2K,WAAW,CAACrH,IAAI,EAAE,oBAAoB,CAAC;UACrE,CAAC;UAAA,gBAAAtC,OAAA4J,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;YAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;UAAA;QAAA;OACJ,EACD;QACI5D,MAAM,EAAE,OAAO;QACfE,IAAI,EAAE,uCAAuC,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CACvE,OAAO,CACV,EAAE;QACHQ,SAAS,EAAE,oBAAoB;QAC/B8H,aAAa,EAAE;UACX7G,OAAO,EAAE,UAAU;UACnB8G,UAAU,EAAE,OAAO;UACnBC,QAAQ,EAAE;YACNC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE,KAAK;YACXnC,QAAQ,EAAE;;SAEjB;QACD7G,MAAM;UAAA,IAAAkK,KAAA,GAAAV,iBAAA,CAAE,WAAOhD,CAAM,EAAEC,EAAO,EAAEgD,MAAW,EAAE9C,MAAW,EAAI;YACxD,MAAMrE,IAAI,GAAGmE,EAAE,CAACL,OAAO,CAACsD,UAAU,EAAE;YACpC,MAAM1G,KAAI,CAAChE,cAAc,CAACmL,SAAS,CAAC7H,IAAI,EAAE,mBAAmB,CAAC;UAClE,CAAC;UAAA,gBAAAtC,OAAAoK,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;YAAA,OAAAL,KAAA,CAAAF,KAAA,OAAAC,SAAA;UAAA;QAAA;OACJ,EAED;QACI1D,IAAI,EAAE,wCAAwC,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CACxE,qBAAqB,CACxB,EAAE;QACHQ,SAAS,EAAE,mCAAmC;QAC9Cd,MAAM,EAAEA,CAACwG,CAAM,EAAEC,EAAO,EAAEC,IAAS,EAAEC,MAAW,KAAI;UAChD,IAAI,CAACjI,eAAe,CAACkI,IAAI,EAAE;UAE3B;UACA,MAAM4D,QAAQ,GAAG/D,EAAE,CAACgE,IAAI,CAAC;YAAC5D,QAAQ,EAAE;UAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;UACjD,MAAMoI,cAAc,GAAGF,QAAQ,CAAC,CAAC,CAAC,CAACG,eAAe;UAGlD,IAAID,cAAc,CAACnI,MAAM,IAAI,CAAC,EAAE;YAC5B3F,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;cAChDiG,IAAI,EAAE,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAAC,iDAAiD,CAAC;cACvF4G,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;YACF,IAAI,CAAC3I,eAAe,CAAC2J,OAAO,EAAE;YAC9B;;UAGJ,MAAMuC,WAAW,GAAGF,cAAc,CAACpG,GAAG,CAAEuG,IAAI,IAAKA,IAAI,CAACjK,EAAE,CAAC;UAEzD,IAAI,CAAChC,eAAe,CACfkM,YAAY,CAACF,WAAW,CAAC,CACzBlJ,SAAS,CAAEqF,GAAG,IAAI;YACfnK,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;cAChDiG,IAAI,EAAE,uBAAuB;cAC7BW,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;YACF,IAAI,CAAClI,SAAS,CAACmJ,UAAU,CAACJ,IAAI,CACzBI,UAA0B,IAAI;cAC3BA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;YAC5B,CAAC,CACJ;UACL,CAAC,CAAC;QACV;OACH,EACD;QACIjC,IAAI,EAAE,uDAAuD,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CACvF,eAAe,CAClB,EAAE;QACH+F,MAAM,EAAE,gBAAgB;QACxBvF,SAAS,EAAE,mCAAmC;QAC9Cd,MAAM,EAAEA,CAACwG,CAAM,EAAEC,EAAO,EAAEC,IAAS,EAAEC,MAAW,KAAI;UAChD,IAAI,CAACjI,eAAe,CAACkI,IAAI,EAAE;UAC3B,IAAItE,IAAI,GAAGmE,EAAE,CAAC9D,GAAG,CAAC;YAACkE,QAAQ,EAAE;UAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;UAC1C,IAAI,CAACpE,oBAAoB,CAAC6M,mBAAmB,CAACzI,IAAI,CAAC1B,EAAE,CAAC,CAACc,SAAS,CAC3DqF,GAAG,IAAI;YACJnK,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;cAChDiG,IAAI,EAAEQ,GAAG,CAACE,OAAO;cACjBC,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;YACF,IAAI,CAAClI,SAAS,CAACmJ,UAAU,CAACJ,IAAI,CACzBI,UAA0B,IAAI;cAC3BA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;YAC5B,CAAC,CACJ;UACL,CAAC,EACAlB,GAAG,IAAI;YACJ1K,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,OAAO,CAAC;cAC9CiG,IAAI,EAAEe,GAAG,CAACL,OAAO;cACjBC,IAAI,EAAE,OAAO;cACbC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;UACN,CAAC,CACJ;QACL;OACH;KAER,EACD;MACId,IAAI,EACA,mCAAmC,GACnC,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAAC,WAAW,CAAC;MAC/C0K,SAAS,EAAE,IAAI,CAAC7M,iBAAiB,CAACmC,OAAO,CAAC,WAAW,CAAC;MACtDN,MAAM,EAAEA,CAACwG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;QAC5B,IAAIsE,YAAY,GAAGxE,EAAE,CAACgE,IAAI,CAAC;UAAC5D,QAAQ,EAAE;QAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;QAEnD,IAAI2I,YAAY,CAAC1I,MAAM,IAAI,CAAC,EAAE;UAC1B3F,IAAI,CAACoK,IAAI,CAAC;YACNvH,KAAK,EAAE,mCAAmC;YAC1CyH,IAAI,EAAE;WACT,CAAC;UACF;;QAGJ,IAAIgE,aAAa,GAAGD,YAAY,CAAC,CAAC,CAAC,CAAC1D,eAAe;QAEnDzD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkH,YAAY,CAAC;QAG1C,IAAIC,aAAa,IAAIxO,SAAS,CAACsI,cAAc,CAACE,QAAQ,EAAE;UACpDtI,IAAI,CAACoK,IAAI,CAAC;YACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,oDAAoD,CAAC;YAC3F4G,IAAI,EAAE;WACT,CAAC;UACF;;QAGJ;QAGA;QACA,IAAI,CAACiE,mBAAmB,EAAE,CAACjD,IAAI,CAAEkD,KAAK,IAAI;UACtCtH,OAAO,CAACC,GAAG,CAACqH,KAAK,CAAC;UAClB,MAAM3D,QAAQ,GAAG,IAAI,CAACrJ,YAAY,CAACsJ,IAAI,CACnCvK,wBAAwB,EACxB;YACIwK,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE;WACb,CACJ;UAEDJ,QAAQ,CAACK,iBAAiB,CAACsD,KAAK,GAAGA,KAAK;UACxC3D,QAAQ,CAACK,iBAAiB,CAACuD,YAAY,GAAGJ,YAAY,CAAC,CAAC,CAAC,CAACK,IAAI;UAE9D7D,QAAQ,CAACQ,MAAM,CAACC,IAAI,CACfD,MAAM,IAAI;YACPnE,OAAO,CAACC,GAAG,CAACkE,MAAM,CAAC;YACnB,IAAI,CAACvJ,eAAe,CAACkI,IAAI,EAAE;YAC3B,IAAI,CAAC1I,oBAAoB,CACpBqN,UAAU,CAACN,YAAY,CAAC,CAAC,CAAC,CAACrK,EAAE,EAAEqH,MAAM,CAACuD,OAAO,CAAC,CAC9C9J,SAAS,CAAEqF,GAAG,IAAI;cACf,IAAI,CAACrI,eAAe,CAAC2J,OAAO,EAAE;cAC9B,IAAI,CAACoD,eAAe,EAAE;YAC1B,CAAC,CAAC;UACV,CAAC,CACJ;QACL,CAAC,CAAC;MAGN,CAAC;MACDpF,MAAM,EAAE;KACX,EAED;MACI;MACAA,MAAM,EAAE,QAAQ;MAChBE,IAAI,EAAE,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAAC,QAAQ;KAChD,CACJ;IAED,IAAI,IAAI,CAACW,oBAAoB,EAAE;MAC3B;MACAmF,OAAO,CAACT,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACjB;QACAY,IAAI,EACA,6CAA6C,GAC7C,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAAC,UAAU,CAAC;QAC9CN,MAAM,EAAEA,CAACwG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC5B,IAAIsE,YAAY,GAAGxE,EAAE,CAACgE,IAAI,CAAC;YAAC5D,QAAQ,EAAE;UAAI,CAAC,CAAC,CAACvE,IAAI,EAAE;UAEnD,IAAI0F,aAAa,GAAG,EAAE;UACtBiD,YAAY,CAACS,IAAI,CAAE/I,GAAG,IAAI;YACtBqF,aAAa,CAACvG,IAAI,CAACkB,GAAG,CAAC;UAC3B,CAAC,CAAC;UACFmB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkH,YAAY,CAAC;UACzC,IAAI3I,IAAI,GAAG;YACP0F,aAAa,EAAEA,aAAa;YAC5BjC,MAAM,EAAEkF,YAAY,CAAC,CAAC,CAAC,CAACU,MAAM,CAACC;WAClC;UAED,MAAMC,aAAa,GAAYvJ,IAAI,CAAC0F,aAAa,CAAC8D,IAAI,CACjDC,GAAG,IAAKA,GAAG,CAACxE,eAAe,IAAI7K,SAAS,CAACsI,cAAc,CAACI,SAAS,CACrE;UAED,IAAIyG,aAAa,EAAE;YACfjP,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,SAAS,CAAC;cAChDiG,IAAI,EAAE,IAAI,CAACpI,iBAAiB,CAACmC,OAAO,CAAC,wCAAwC,CAAC;cAC9E4G,IAAI,EAAE,SAAS;cACfC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI,CAAC;cACvD8G,WAAW,EAAE;gBACTC,aAAa,EAAE;;aAEtB,CAAC;YACF;;UAGJ,IACI/E,IAAI,CAACyD,MAAM,IAAIrJ,SAAS,CAACsP,eAAe,CAACC,OAAO,IAChD3J,IAAI,CAACyD,MAAM,IAAIrJ,SAAS,CAACsP,eAAe,CAAC1G,OAAO,EAClD;YACE,IAAI,CAAC4G,kBAAkB,CAAC5J,IAAI,CAAC;WAChC,MAAM;YACH1F,IAAI,CAACoK,IAAI,CAAC;cACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CACjC,qDAAqD,CACxD;cACD4G,IAAI,EAAE;aACT,CAAC;;QAEV,CAAC;QACDb,MAAM,EAAE;OACX,CAAC;;IAIN,IAAI,CAACjH,SAAS,GAAG;MACb+M,GAAG,EAAE,iKAAiK;MACtKC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfjE,IAAI,EAAEA,CAACkE,oBAAyB,EAAEC,QAAQ,KAAI;QAC1CD,oBAAoB,CAAC,WAAW,CAAC,GAAGA,oBAAoB,CAAClK,MAAM;QAC/DkK,oBAAoB,CAAC,MAAM,CAAC,GACxBE,IAAI,CAACC,KAAK,CAACH,oBAAoB,CAACI,KAAK,GAAGJ,oBAAoB,CAAClK,MAAM,CAAC,GACpE,CAAC;QACLkK,oBAAoB,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvN,QAAQ;QACjDuN,oBAAoB,CAAC,MAAM,CAAC,GAAG,cAAc;QAE7C;QACA,MAAMK,eAAe,GAAGL,oBAAoB,CAAC1D,KAAK,CAAC,CAAC,CAAC,CAACgE,MAAM;QAE5D,MAAMC,aAAa,GAAGP,oBAAoB,CAAC1D,KAAK,CAAC,CAAC,CAAC,CAACkE,GAAG;QAEvD,MAAMC,OAAO,GAAG;UACZC,SAAS,EAAEV,oBAAoB,CAAC,WAAW,CAAC;UAC5CtM,IAAI,EAAEsM,oBAAoB,CAAC,MAAM,CAAC;UAClCzD,IAAI,EAAEyD,oBAAoB,CAAC,MAAM,CAAC;UAClCW,SAAS,EAAEX,oBAAoB,CAAC,WAAW,CAAC;UAC5CY,MAAM,EAAEZ,oBAAoB,CAACY,MAAM,CAACxK,KAAK;UACzCyK,WAAW,EAAEb,oBAAoB,CAAC1K,OAAO,CAAC+K,eAAe,CAAC,CAACxK,IAAI;UAC/DiL,cAAc,EAAEP,aAAa,CAAE;SAClC;;QAED,IAAI,CAAC/O,KAAK,CACLuP,IAAI,CACD,GAAG7Q,WAAW,CAACmD,MAAM,mCAAmC,EACxDoN,OAAO,CACV,CACAxL,SAAS,CAAE+L,IAAS,IAAI;UACrB,IAAI,CAAC/O,eAAe,CAAC2J,OAAO,EAAE;UAE9B,IACIoF,IAAI,IACJA,IAAI,CAACC,YAAY,KAAKC,SAAS,IAC/BF,IAAI,CAACG,eAAe,KAAKD,SAAS,EACpC;YACEjB,QAAQ,CAAC;cACLgB,YAAY,EAAED,IAAI,CAACC,YAAY;cAC/BE,eAAe,EAAEH,IAAI,CAACG,eAAe;cACrCtL,IAAI,EAAEmL,IAAI,CAACnL;aACd,CAAC;WACL,MAAM;YACHwB,OAAO,CAAC+J,KAAK,CACT,wDAAwD,CAC3D;;QAET,CAAC,CAAC;MACV,CAAC;MACDC,UAAU,EAAE,gBAAgB;MAC5BC,QAAQ,EAAE,IAAI,CAAClP,eAAe,CAACmP,iBAAiB,CAACC,IAAI;MACrDC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;QACNC,KAAK,EAAE,IAAI,CAACjQ,iBAAiB,CAACmC,OAAO,CAAC,OAAO,CAAC;QAC9C+N,IAAI,EAAE,IAAI,CAAClQ,iBAAiB,CAACmC,OAAO,CAAC,MAAM,CAAC;QAC5CgO,IAAI,EAAE,IAAI,CAACnQ,iBAAiB,CAACmC,OAAO,CAAC,MAAM,CAAC;QAC5CiO,QAAQ,EAAE,IAAI,CAACpQ,iBAAiB,CAACmC,OAAO,CAAC,UAAU;OACtD;MACDkO,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MAC7BC,UAAU,EAAE,EAAE;MACd1F,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;MAClB2F,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,CACR;QAACC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAC,EACnC;QAACD,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAC,EACnC;QAACD,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAC,CACtC;MACD/M,OAAO,EAAED,cAAc;MACvBsE,OAAO,EAAE;QACL+F,GAAG,EAAE,IAAI,CAACtN,eAAe,CAACmP,iBAAiB,CAAC5H,OAAO,CAAC+F,GAAG;QACvD/F,OAAO,EAAEA;;KAEhB;EAGL;EAEA2I,kBAAkBA,CAAA;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGJvN,gBAAgBA,CAAA;IACZ,IAAI,CAACwN,aAAa,GAAG;MACjBC,WAAW,EAAE,IAAI,CAAC9Q,iBAAiB,CAACmC,OAAO,CAAC,mBAAmB,CAAC;MAChE4O,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACRhP,IAAI,EAAE,EAAE;QACRiP,KAAK,EAAE,CACH;UACIvR,IAAI,EAAE,eAAe;UACrBwR,MAAM,EAAE;SACX,EACD;UACIxR,IAAI,EAAE,IAAI,CAACM,iBAAiB,CAACmC,OAAO,CAAC,mBAAmB,CAAC;UACzD+O,MAAM,EAAE;SACX;;KAGZ;EACL;EAEAC,SAASA,CAACC,MAAM;IACZzL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwL,MAAM,CAAC;IAChC,IAAI,CAAC9D,eAAe,EAAE;EAC1B;EAEA7J,gBAAgBA,CAAA;IACZ,IAAImE,MAAM,GAAG,QAAQ;IACrB,IAAI,CAAC1H,aAAa,CAACmR,UAAU,CAACzJ,MAAM,CAAC,CAACrE,SAAS,CAC1CY,IAAI,IAAI;MACL,IAAI,CAACrD,OAAO,GAAGqD,IAAI;MACnB,IAAI,CAACmN,MAAM,GAAG,IAAI,CAACxQ,OAAO,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACuQ,MAAM,CAAC7O,EAAE;MAC9B,IACI,IAAI,CAAC9B,gBAAgB,CAAC2G,iBAAiB,IACvC,IAAI,CAAC3G,gBAAgB,CAAC2G,iBAAiB,CAAC7C,cAAc,CAClD,sBAAsB,CACzB,EACH;QACE,IAAI,CAAC3B,oBAAoB,GACrB,IAAI,CAACnC,gBAAgB,CAAC2G,iBAAiB,CAACxE,oBAAoB;;MAEpE,IAAI,CAAC8B,aAAa,EAAE;MACpB,IAAI,CAAC1D,SAAS,CAACiP,IAAI,CAAC,IAAI,CAAClP,SAAS,CAAC;IACvC,CAAC,EACAyO,KAAK,IAAI;MACNjR,IAAI,CAACoK,IAAI,CAAC;QACNvH,KAAK,EAAE,OAAO;QACd8G,IAAI,EAAEsH,KAAK,CAAC5G,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAChJ,iBAAiB,CAACmC,OAAO,CAAC,IAAI;OACzD,CAAC;IACN,CAAC,CACJ;EACL;EAEAoP,cAAcA,CAACH,MAAM;IACjBzL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwL,MAAM,CAAC;IACrC,IAAI,CAACE,MAAM,GAAGF,MAAM;IACpB,IAAI,CAACrQ,QAAQ,GAAG,IAAI,CAACuQ,MAAM,CAAC7O,EAAE;IAC9B,IAAI,CAAC6K,eAAe,EAAE;EAC1B;EAEAA,eAAeA,CAAA;IACX,IAAI,CAAC/M,eAAe,CAACkI,IAAI,EAAE;IAC3B9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B,IAAI,IAAI,CAAC5E,SAAS,IAAI,IAAI,CAACA,SAAS,CAACmJ,UAAU,EAC3C,IAAI,CAACnJ,SAAS,CAACmJ,UAAU,CAACJ,IAAI,CAAEI,UAA0B,IAAI;MAC1DA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;IAC5B,CAAC,CAAC;EACV;EAEA0D,kBAAkBA,CAAC5J,IAAI;IACnB,MAAMmF,QAAQ,GAAG,IAAI,CAACrJ,YAAY,CAACsJ,IAAI,CAAC7K,kBAAkB,EAAE;MACxD8K,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;KACb,CAAC;IAEF/D,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEzB,IAAI,CAAC;IAEzBmF,QAAQ,CAACK,iBAAiB,CAACE,aAAa,GAAG1F,IAAI,CAAC0F,aAAa,CAAC,CAAC,CAAC;IAChEP,QAAQ,CAACK,iBAAiB,CAAC/B,MAAM,GAAGzD,IAAI,CAACyD,MAAM;IAC/C0B,QAAQ,CAACK,iBAAiB,CAAC3I,SAAS,GAAG,IAAI,CAACA,SAAS;IAErD;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;;EAEAwQ,YAAYA,CAAChN,GAAG;IACZ,OAAO,IAAIiN,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACnC,IAAIC,sBAAsB,GAAGpN,GAAG,CAACiJ,eAAe;MAChD,IAAIoE,YAAY,GAAGrN,GAAG,CAAC6I,OAAO;MAC9B,IAAI,CAACL,mBAAmB,EAAE,CAACjD,IAAI,CAAE5F,IAAI,IAAI;QACrC,IAAI,CAACrC,MAAM,CAACgC,OAAO,CAAEgO,KAAK,IAAI;UAC1B,IAAIA,KAAK,CAAC/P,GAAG,IAAI,SAAS,EAAE;YACxB+P,KAAK,CAACC,eAAe,CAACvP,OAAO,GAAG2B,IAAI;YACpC2N,KAAK,CAACpP,YAAY,GAAGmP,YAAY;YACjCH,OAAO,CAAC,IAAI,CAAC;;QAErB,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA1E,mBAAmBA,CAAA;IACf,OAAO,IAAIyE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACnC,IAAI,CAACtR,YAAY,CACZ2M,mBAAmB,EAAE,CACrBgF,SAAS,EAAE,CACXjI,IAAI,CAAE5F,IAAI,IAAI;QACXuN,OAAO,CAACvN,IAAI,CAAC;MACjB,CAAC,CAAC;IACV,CAAC,CAAC;EACN;EAEA8N,MAAMA,CAACpQ,MAAM,EAAE2C,GAAG;IACd,IAAI,CAACpD,MAAM,CAACS,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACT,MAAM,CAACoD,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI3C,MAAM,IAAI,MAAM,IAAI2C,GAAG,EAAE;MACzB,IAAIuI,aAAa,GAAGvI,GAAG,CAAC4E,eAAe;MACvC,IAAI2D,aAAa,IAAIxO,SAAS,CAACsI,cAAc,CAACE,QAAQ,EAAE;QACpDtI,IAAI,CAACoK,IAAI,CAAC;UACNvH,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACmC,OAAO,CAAC,oDAAoD,CAAC;UAC3F4G,IAAI,EAAE;SACT,CAAC;QACF;;;IAIR,IAAI,CAACyI,YAAY,CAAChN,GAAG,CAAC,CAACuF,IAAI,CAAE5F,IAAI,IAAI;MACjC,IAAIA,IAAI,EAAE;QACN,IAAI,CAAC3D,mBAAmB,CACnB0R,kBAAkB,CAAC,IAAI,CAAC/Q,UAAU,CAAC,CACnCgR,UAAU,EAAE;;IAEzB,CAAC,CAAC;EACN;EAEAC,SAASA,CAAC3P,EAAE;IACR,OAAO,IAAIgP,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACnC,IAAI,CAAC5R,oBAAoB,CAACsS,aAAa,CAAC5P,EAAE,CAAC,CAACc,SAAS,CAAEY,IAAI,IAAI;QAC3DuN,OAAO,CAACvN,IAAI,CAAC;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAmO,eAAeA,CAAA,GACf;EAEAC,WAAWA,CAAA;IACP,IAAI,CAACrR,SAAS,CAACsR,WAAW,EAAE;IAC5B,IAAI,CAACzP,aAAa,CAACe,OAAO,CAAE2O,YAAY,IAAKA,YAAY,CAACD,WAAW,EAAE,CAAC;EAC5E;EAAC,QAAAE,CAAA;qBAhjCQ/S,0BAA0B,EAAAV,EAAA,CAAA0T,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5T,EAAA,CAAA0T,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA9T,EAAA,CAAA0T,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAhU,EAAA,CAAA0T,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAlU,EAAA,CAAA0T,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAApU,EAAA,CAAA0T,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAAtU,EAAA,CAAA0T,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAxU,EAAA,CAAA0T,iBAAA,CAAAe,EAAA,CAAAC,oBAAA,GAAA1U,EAAA,CAAA0T,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAA5U,EAAA,CAAA0T,iBAAA,CAAAmB,GAAA,CAAAC,UAAA,GAAA9U,EAAA,CAAA0T,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAAhV,EAAA,CAAA0T,iBAAA,CAAAuB,GAAA,CAAAC,kBAAA,GAAAlV,EAAA,CAAA0T,iBAAA,CAAAyB,GAAA,CAAAC,cAAA,GAAApV,EAAA,CAAA0T,iBAAA,CAAA2B,GAAA,CAAAC,cAAA,GAAAtV,EAAA,CAAA0T,iBAAA,CAAA6B,GAAA,CAAAC,eAAA,GAAAxV,EAAA,CAAA0T,iBAAA,CAAA+B,GAAA,CAAAC,KAAA,GAAA1V,EAAA,CAAA0T,iBAAA,CAAAiC,GAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAA1BnV,0BAA0B;IAAAoV,SAAA;IAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBAQxBvW,kBAAkB;;;;;;;;;;;;;QCxDjCM,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAmW,SAAA,4BAAyE;QAEzEnW,EAAA,CAAAC,cAAA,aAAsB;QAGED,EAAA,CAAAE,MAAA,GAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtDH,EAAA,CAAAC,cAAA,mBACyD;QAAvDD,EAAA,CAAAoW,UAAA,2BAAAC,uEAAAlE,MAAA;UAAA,OAAA+D,GAAA,CAAA7D,MAAA,GAAAF,MAAA;QAAA,EAAoB,oBAAAmE,gEAAAnE,MAAA;UAAA,OAAW+D,GAAA,CAAA5D,cAAA,CAAAH,MAAA,CAAsB;QAAA,EAAjC;;QACpBnS,EAAA,CAAAuW,UAAA,KAAAC,gDAAA,uBACY;QACdxW,EAAA,CAAAG,YAAA,EAAY;QAIhBH,EAAA,CAAAC,cAAA,cAAiB;QAIcD,EAAA,CAAAE,MAAA,IAAqC;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEnEH,EAAA,CAAAC,cAAA,WAAK;QACHD,EAAA,CAAAmW,SAAA,iBACQ;QACVnW,EAAA,CAAAG,YAAA,EAAM;QAOhBH,EAAA,CAAAC,cAAA,wBAAqH;QACnHD,EAAA,CAAAmW,SAAA,8BACqB;QACvBnW,EAAA,CAAAG,YAAA,EAAe;;;QAjCSH,EAAA,CAAAM,SAAA,GAA+B;QAA/BN,EAAA,CAAAI,UAAA,kBAAA8V,GAAA,CAAAtE,aAAA,CAA+B;QAK3B5R,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAAyW,iBAAA,CAAAzW,EAAA,CAAAQ,WAAA,kBAA0B;QACKR,EAAA,CAAAM,SAAA,GAA2C;QAA3CN,EAAA,CAAA0W,qBAAA,gBAAA1W,EAAA,CAAAQ,WAAA,yBAA2C;QAAnFR,EAAA,CAAAI,UAAA,oBAAmB,gCAAA8V,GAAA,CAAA7D,MAAA;QAEErS,EAAA,CAAAM,SAAA,GAAU;QAAVN,EAAA,CAAAI,UAAA,YAAA8V,GAAA,CAAArU,OAAA,CAAU;QAUf7B,EAAA,CAAAM,SAAA,GAAqC;QAArCN,EAAA,CAAAyW,iBAAA,CAAAzW,EAAA,CAAAQ,WAAA,8BAAqC;QAG3CR,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAI,UAAA,cAAA8V,GAAA,CAAAlU,SAAA,CAAuB,cAAAkU,GAAA,CAAAjU,SAAA;QASejC,EAAA,CAAAM,SAAA,GAAmB;QAAnBN,EAAA,CAAAI,UAAA,SAAA8V,GAAA,CAAAhU,UAAA,CAAmB;QAChElC,EAAA,CAAAM,SAAA,GAAmB;QAAnBN,EAAA,CAAAI,UAAA,UAAA8V,GAAA,CAAAnU,SAAA,CAAmB,WAAAmU,GAAA,CAAArT,MAAA,YAAAqT,GAAA,CAAA/T,MAAA", "names": ["AppConfig", "environment", "<PERSON><PERSON>", "ValidatorComponent", "DataTableDirective", "Subject", "moment", "ModalSuitableGroupsComponent", "ModalInputCancelReasonComponent", "ModalChangeClubComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "AdminRegistrationComponent", "constructor", "route", "_http", "_registrationService", "_translateService", "modalService", "seasonService", "_toastService", "_angularFire", "_clubService", "_fcmService", "_loadingService", "_coreSidebarService", "_paymentService", "_commonsService", "_settingsService", "_titleService", "_exportService", "seasons", "seasonId", "dtElement", "dtOptions", "dtTrigger", "table_name", "params", "editor_id", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "label", "instant", "placeholder", "required", "valueProp", "labelProp", "options", "id", "defaultValue", "className", "customFields", "initSettings", "is_validate_required", "subscriptions", "setTitle", "ngOnInit", "$", "fx", "off", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "subscribe", "get<PERSON>ustom<PERSON>ields", "getActiveSeasons", "convertCustomFields2Columns", "defaultColumns", "columns", "inogreColumns", "for<PERSON>ach", "customField", "isExist", "filter", "defaultColumn", "data", "length", "indexOf", "visible", "render", "row", "hasOwnProperty", "value", "custom_fields", "initDatatable", "_this", "sortable", "img", "document", "createElement", "src", "style", "width", "height", "objectFit", "backgroundColor", "onerror", "attr", "outerHTML", "console", "log", "first_name", "last_name", "utc", "local", "format", "groupNames", "map", "assigned_groups", "group", "join", "registered_text", "pending_text", "approved_text", "rejected_text", "cancelled_text", "payment_error_text", "APPROVE_STATUS", "Registered", "Approved", "Rejected", "Cancelled", "PaymentError", "Pending", "toUpperCase", "getBadgeValidateStatus", "initSettingsValue", "is_payment_required", "splice", "payment", "product_id", "weird_invoice", "status", "toLowerCase", "PAYMENT_STATUS", "paid", "marked_as_paid", "buttons", "extend", "background", "text", "e", "dt", "node", "config", "show", "selected", "approve", "res", "fire", "message", "icon", "confirmButtonText", "customClass", "confirmButton", "err", "approval_status", "warning", "modalRef", "open", "size", "backdrop", "keyboard", "componentInstance", "windowClass", "registrations", "result", "then", "cancel", "reason", "dismiss", "dtInstance", "ajax", "reload", "registration_id", "group_id", "assignNewGroup", "exportOptions", "orthogonal", "modifier", "order", "page", "customize", "doc", "styles", "tableBodyEven", "alignment", "tableBodyOdd", "_ref", "_asyncToGenerator", "button", "exportData", "exportExcel", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "_ref2", "exportPDF", "_x5", "_x6", "_x7", "_x8", "row_data", "rows", "payment_detail", "payment_details", "payment_ids", "item", "syncPayments", "sendReminderPayment", "titleAttr", "selectedRows", "approveStatus", "getAllClubsIsActive", "clubs", "current_club", "club", "changeClub", "club_id", "reloadDataTable", "each", "player", "validate_status", "cancel_status", "some", "reg", "VALIDATE_STATUS", "Updated", "modalValidatorOpen", "dom", "select", "serverSide", "processing", "rowId", "stateSave", "dataTablesParameters", "callback", "Math", "floor", "start", "sortColumnIndex", "column", "sortDirection", "dir", "payload", "season_id", "page_size", "search", "sort_column", "sort_direction", "post", "resp", "recordsTotal", "undefined", "recordsFiltered", "error", "pagingType", "language", "dataTableDefaults", "lang", "paging", "paginate", "first", "last", "next", "previous", "lengthMenu", "pageLength", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "listenNotification", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "onSuccess", "$event", "getSeasons", "season", "onChangeSeason", "showClubName", "Promise", "resolve", "reject", "selectedValidateStatus", "selectedClub", "field", "templateOptions", "to<PERSON>romise", "editor", "getSidebarRegistry", "toggle<PERSON><PERSON>", "getSeason", "getSeasonByID", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "subscription", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "HttpClient", "i3", "RegistrationService", "i4", "TranslateService", "i5", "NgbModal", "i6", "SeasonService", "i7", "ToastrService", "i8", "AngularFireMessaging", "i9", "ClubService", "i10", "FcmService", "i11", "LoadingService", "i12", "CoreSidebarService", "i13", "PaymentService", "i14", "CommonsService", "i15", "SettingsService", "i16", "Title", "i17", "ExportService", "_2", "selectors", "viewQuery", "AdminRegistrationComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "AdminRegistrationComponent_Template_ng_select_ngModelChange_8_listener", "AdminRegistrationComponent_Template_ng_select_change_8_listener", "ɵɵtemplate", "AdminRegistrationComponent_ng_option_10_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\admin-registration.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\admin-registration.component.html"], "sourcesContent": ["import {HttpClient} from '@angular/common/http';\r\nimport {\r\n    AfterViewInit,\r\n    Component,\r\n    ElementRef,\r\n    Input,\r\n    OnDestroy,\r\n    OnInit,\r\n    TemplateRef,\r\n    ViewChild,\r\n} from '@angular/core';\r\nimport {ActivatedRoute} from '@angular/router';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {AppConfig} from 'app/app-config';\r\nimport {CommonsService} from 'app/services/commons.service';\r\nimport {RegistrationService} from 'app/services/registration.service';\r\nimport {environment} from 'environments/environment';\r\nimport Swal from 'sweetalert2';\r\nimport {NgbModal} from '@ng-bootstrap/ng-bootstrap';\r\nimport {ValidatorComponent} from './validator/validator.component';\r\nimport {DataTableDirective} from 'angular-datatables';\r\nimport {CoreSidebarService} from '@core/components/core-sidebar/core-sidebar.service';\r\nimport {ClubService} from 'app/services/club.service';\r\nimport {LoadingService} from 'app/services/loading.service';\r\nimport {Season} from 'app/interfaces/season';\r\nimport {SeasonService} from 'app/services/season.service';\r\nimport {Subject, Subscription} from 'rxjs';\r\nimport {ADTSettings} from 'angular-datatables/src/models/settings';\r\nimport moment from 'moment';\r\nimport {SettingsService} from 'app/services/settings.service';\r\nimport {Registration} from 'app/interfaces/registration';\r\nimport {ModalSuitableGroupsComponent} from './modal-suitable-groups/modal-suitable-groups.component';\r\nimport {ModalInputCancelReasonComponent} from './modal-input-cancel-reason/modal-input-cancel-reason.component';\r\nimport {Title} from '@angular/platform-browser';\r\nimport {ToastrService} from 'ngx-toastr';\r\nimport {PaymentService} from 'app/services/payment.service';\r\nimport {ExportService} from 'app/services/export.service';\r\nimport {AngularFireMessaging} from '@angular/fire/compat/messaging';\r\nimport {NotificationsService} from 'app/layout/components/navbar/navbar-notification/notifications.service';\r\nimport {FcmService} from 'app/services/fcm.service';\r\nimport {ModalChangeClubComponent} from './modal-change-club/modal-change-club.component';\r\nimport {PushNotifications} from '@capacitor/push-notifications';\r\n\r\n@Component({\r\n    selector: 'app-admin-registration',\r\n    templateUrl: './admin-registration.component.html',\r\n    styleUrls: ['./admin-registration.component.scss'],\r\n})\r\nexport class AdminRegistrationComponent\r\n    implements AfterViewInit, OnDestroy, OnInit {\r\n    public contentHeader: object;\r\n    seasons: Season[] = [];\r\n    season: Season;\r\n    seasonId: number = 0;\r\n    @ViewChild('modalValidator') modalValidator: TemplateRef<any>;\r\n\r\n    @ViewChild(DataTableDirective, {static: false})\r\n    dtElement: any = DataTableDirective;\r\n    dtOptions: any = {};\r\n    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n\r\n    table_name = 'registration_table';\r\n    params: any = {\r\n        editor_id: this.table_name,\r\n        title: {\r\n            create: 'Create new registration',\r\n            edit: 'Edit registration',\r\n            remove: 'Delete registration',\r\n        },\r\n        url: `${environment.apiUrl}/registrations/editor`,\r\n        method: 'POST',\r\n        action: 'create',\r\n    };\r\n    public fields: any[] = [\r\n        {\r\n            key: 'club_id',\r\n            type: 'select',\r\n            props: {\r\n                label: this._translateService.instant('Edit club'),\r\n                placeholder: this._translateService.instant('Select Club'),\r\n                required: true,\r\n\r\n                // selected club\r\n                valueProp: 'id',\r\n                labelProp: 'name',\r\n                options: [\r\n                    {\r\n                        id: 1,\r\n                        name: 'Loading...',\r\n                    },\r\n                ],\r\n            },\r\n            //selected club\r\n            defaultValue: 1,\r\n            className: '',\r\n        },\r\n    ];\r\n    customFields: any[] = [];\r\n    initSettings: any = {};\r\n    is_validate_required: boolean = true;\r\n\r\n    constructor(\r\n        private route: ActivatedRoute,\r\n        private _http: HttpClient,\r\n        private _registrationService: RegistrationService,\r\n        public _translateService: TranslateService,\r\n        public modalService: NgbModal,\r\n        public seasonService: SeasonService,\r\n        public _toastService: ToastrService,\r\n        private _angularFire: AngularFireMessaging,\r\n        public _clubService: ClubService,\r\n        private _fcmService: FcmService,\r\n        public _loadingService: LoadingService,\r\n        public _coreSidebarService: CoreSidebarService,\r\n        public _paymentService: PaymentService,\r\n        public _commonsService: CommonsService,\r\n        public _settingsService: SettingsService,\r\n        public _titleService: Title,\r\n        private _exportService: ExportService\r\n    ) {\r\n        this._titleService.setTitle('Registrations');\r\n    }\r\n\r\n    subscriptions: Subscription[] = [];\r\n\r\n    ngOnInit(): void {\r\n        $.fx.off = true; //this is for disable jquery animation\r\n        this.setContentHeader();\r\n        this.subscriptions.push(\r\n            this._settingsService.customFields.subscribe((customFields) => {\r\n                this.customFields = customFields;\r\n            })\r\n        );\r\n\r\n        // this.listenNotification();\r\n\r\n        this._settingsService.getCustomFields();\r\n\r\n        this.getActiveSeasons();\r\n    }\r\n\r\n    convertCustomFields2Columns(defaultColumns: any[]) {\r\n        let columns: any[] = [];\r\n        let inogreColumns = [\r\n            'player.photo',\r\n            'player.first_name',\r\n            'player.last_name',\r\n            'club_id',\r\n            'player.document_photo',\r\n        ];\r\n        this.customFields.forEach((customField) => {\r\n            //filter if defaultColumns.data == customField.key => skip\r\n            let isExist = defaultColumns.filter((defaultColumn) => {\r\n                return defaultColumn.data == customField.key;\r\n            });\r\n            if (isExist.length > 0 || inogreColumns.indexOf(customField.key) > -1) {\r\n                return;\r\n            }\r\n            columns.push({\r\n                data: null,\r\n                title: this._translateService.instant(customField.props.label),\r\n                visible: false,\r\n                render: function (data: any, type: any, row: any) {\r\n                    if (data.hasOwnProperty(customField.key)) {\r\n                        let value = data[customField.key];\r\n                        if (!value) {\r\n                            return '';\r\n                        }\r\n                        return data[customField.key];\r\n                    } else {\r\n                        if (\r\n                            data.custom_fields &&\r\n                            data.custom_fields.hasOwnProperty(customField.key)\r\n                        ) {\r\n                            let value = data.custom_fields[customField.key];\r\n                            if (!value) {\r\n                                return '';\r\n                            }\r\n                            return value;\r\n                        } else return '';\r\n                    }\r\n                },\r\n            });\r\n        });\r\n\r\n        // merge columns and defaultColumns\r\n        columns = [...defaultColumns, ...columns];\r\n        return columns;\r\n    }\r\n\r\n    initDatatable() {\r\n        let defaultColumns = [\r\n            {data: 'id', title: this._translateService.instant('ID'), visible: false},\r\n            {\r\n                sortable: false,\r\n                data: 'player.photo',\r\n                title: this._translateService.instant('Photo'),\r\n                render: function (data: any, type: any, row: any) {\r\n                    //create image\r\n                    let img = document.createElement('img');\r\n                    img.src = data;\r\n                    img.id = `img-${row.id}`;\r\n                    img.style.width = '50px';\r\n                    img.style.height = 'auto';\r\n                    img.style.objectFit = 'cover';\r\n                    img.style.backgroundColor = '#fff';\r\n                    img.style.objectFit = 'cover';\r\n                    if (data == null) {\r\n                        img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n                    }\r\n                    // check get image error\r\n                    img.onerror = function () {\r\n                        img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n                        // set src by row id\r\n                        $(`#img-${row.id}`).attr('src', img.src);\r\n                    };\r\n                    return type === 'print' ? 'print' : img.outerHTML;\r\n                },\r\n            },\r\n            {\r\n                data: 'player.user',\r\n                title: this._translateService.instant('Player'),\r\n                className: 'font-weight-bolder',\r\n                render: function (data: any, type: any, row: any) {\r\n                    console.log(data);\r\n                    return `${data.first_name} ${data.last_name}`;\r\n                },\r\n            },\r\n            {\r\n                data: 'player.dob',\r\n                title: this._translateService.instant('Date of birth'),\r\n                render: function (data: any, type: any, row: any) {\r\n                    if (data == null) {\r\n                        return ``;\r\n                    }\r\n                    return `<p class=\"m-0\" style=\"min-width: max-content\">${data}</p>`;\r\n                },\r\n            },\r\n            {\r\n                data: 'player.gender',\r\n                title: this._translateService.instant('Gender'),\r\n                render: (data: any, type: any, row: any) => {\r\n                    if (data) {\r\n                        return data == 'Male'\r\n                            ? `<span class=\"badge badge-light-info\">${this._translateService.instant(\r\n                                'Male'\r\n                            )}</span>`\r\n                            : `<span class=\"badge badge-light-warning\"> ${this._translateService.instant(\r\n                                'Female'\r\n                            )}</span>`;\r\n                    } else {\r\n                        return ``;\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                sortable: false,\r\n                data: 'player.primary_guardian.user',\r\n                title: this._translateService.instant('Parent name'),\r\n                visible: false,\r\n                render: (data: any, type: any, row: any) => {\r\n                    return `${data.first_name} ${data.last_name}`;\r\n                }\r\n            },\r\n            {\r\n                sortable: false,\r\n                data: 'player.primary_guardian.user.email',\r\n                title: this._translateService.instant('Parent email'),\r\n                visible: false,\r\n            },\r\n            {\r\n                data: 'club',\r\n                title: this._translateService.instant('Club'),\r\n                render: (data: any, type: any, row: any) => {\r\n                    return (data == null ? '' : `${data.name}`);\r\n                }\r\n            },\r\n            {\r\n                data: 'registered_date',\r\n                title: this._translateService.instant('Reg_Date'),\r\n\r\n                render(data: any, type: any, row: any) {\r\n                    if (data) {\r\n                        return moment.utc(data).local().format('YYYY-MM-DD HH:mm:ss');\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                data: 'approved_date',\r\n                visible: false,\r\n                title: this._translateService.instant('App_Date'),\r\n                render(data: any, type: any, row: any) {\r\n                    if (data) {\r\n                        return moment.utc(data).local().format('YYYY-MM-DD HH:mm:ss');\r\n                    } else {\r\n                        return '';\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                sortable: false,\r\n                data: 'assigned_groups',\r\n                title: this._translateService.instant('Groups'),\r\n                render: (data: any, type: any, row: any) => {\r\n                    const groupNames = data.map((assigned_groups: any) => assigned_groups.group.name).join(', ');\r\n                    return groupNames;\r\n                }\r\n            },\r\n            {\r\n                sortable: false,\r\n                data: 'approval_status',\r\n                title: this._translateService.instant('Approval Status'),\r\n                render: (data: any, type: any, row: any) => {\r\n                    let registered_text = this._translateService.instant('Registered');\r\n                    let pending_text = this._translateService.instant('Pending');\r\n                    let approved_text = this._translateService.instant('Approved');\r\n                    let rejected_text = this._translateService.instant('Rejected');\r\n                    let cancelled_text = this._translateService.instant('Cancelled');\r\n                    let payment_error_text = this._translateService.instant('Payment Error');\r\n                    if (data == null) {\r\n                        return ``;\r\n                    }\r\n                    switch (data) {\r\n                        case AppConfig.APPROVE_STATUS.Registered:\r\n                            return `<span class=\"badge badge-light-info\">${registered_text}</span>`;\r\n                        case AppConfig.APPROVE_STATUS.Approved:\r\n                            return `<span class=\"badge badge-light-success\">${approved_text}</span>`;\r\n                        case AppConfig.APPROVE_STATUS.Rejected:\r\n                            return `<span class=\"badge badge-light-danger\">${rejected_text}</span>`;\r\n                        case AppConfig.APPROVE_STATUS.Cancelled:\r\n                            return `<span class=\"badge badge-light-dark\">${cancelled_text}</span>`;\r\n                        case AppConfig.APPROVE_STATUS.PaymentError:\r\n                            return `<span class=\"badge badge-light-danger\">${payment_error_text}</span>`;\r\n                        case AppConfig.APPROVE_STATUS.Pending:\r\n                            return `<span class=\"badge badge-light-secondary\">${pending_text}</span>`;\r\n                        default:\r\n                            return `<span class=\"badge badge-light-secondary\">${data.toUpperCase()}</span>`;\r\n                    }\r\n                },\r\n            },\r\n\r\n            {\r\n                data: 'updated_at',\r\n                title: this._translateService.instant('Updated at'),\r\n                visible: false,\r\n                render(data: any, type: any, row: any) {\r\n                    if (data) {\r\n                        return moment.utc(data).local().format('YYYY-MM-DD HH:mm:ss');\r\n                    } else {\r\n                        return '';\r\n                    }\r\n                },\r\n            },\r\n        ];\r\n        if (this.is_validate_required) {\r\n            defaultColumns.push({\r\n                sortable: false,\r\n                data: 'player.validate_status',\r\n                title: this._translateService.instant('Validation Status'),\r\n                render: (data: any, type: any, row: any) => {\r\n                    return this._commonsService.getBadgeValidateStatus(data);\r\n                },\r\n            });\r\n        }\r\n\r\n        if (\r\n            this._settingsService.initSettingsValue.hasOwnProperty(\r\n                'is_payment_required'\r\n            ) &&\r\n            this._settingsService.initSettingsValue.is_payment_required\r\n        ) {\r\n            // push to length - 2\r\n            defaultColumns.splice(defaultColumns.length - 2, 0, {\r\n                sortable: false,\r\n                data: 'payment_details',\r\n                title: this._translateService.instant('Payment Status'),\r\n                render: (data: any, type: any, row: any) => {\r\n                    // filter by id\r\n                    console.log(\"id\", row.id);\r\n                    data = data.filter((payment) => {\r\n                        return payment.product_id == row.id;\r\n                    });\r\n                    if (data.length == 0) {\r\n                        return ``;\r\n                    }\r\n\r\n                    const weird_invoice = data.filter((payment) => {\r\n                        return payment.status.toLowerCase() != AppConfig.PAYMENT_STATUS.paid && payment.status.toLowerCase() != AppConfig.PAYMENT_STATUS.marked_as_paid\r\n                    });\r\n\r\n                    if (weird_invoice.length == 0) {\r\n                        return `<span class=\"badge badge-light-success\">${this._translateService.instant('Paid')}</span>`;\r\n                    } else {\r\n                        return `<span class=\"badge badge-light-warning\">${this._translateService.instant('Sent')}</span>`;\r\n                    }\r\n\r\n\r\n                },\r\n            });\r\n        }\r\n\r\n        defaultColumns = this.convertCustomFields2Columns(defaultColumns);\r\n        let buttons: any = [\r\n            {\r\n                // drop down button\r\n                extend: 'collection',\r\n                background: false,\r\n                text: this._translateService.instant('Action'),\r\n                className: 'action-button',\r\n                buttons: [\r\n                    {\r\n                        text: `<i class=\"fa-duotone fa-check-double\"></i> ${this._translateService.instant(\r\n                            'Approve full'\r\n                        )}`,\r\n                        extend: 'selectedSingle',\r\n                        className: 'action-button-item approve-button',\r\n                        action: (e: any, dt: any, node: any, config: any) => {\r\n                            this._loadingService.show();\r\n                            let data = dt.row({selected: true}).data();\r\n                            this._registrationService.approve(data.id).subscribe(\r\n                                (res) => {\r\n                                    Swal.fire({\r\n                                        title: this._translateService.instant('Success'),\r\n                                        text: res.message,\r\n                                        icon: 'success',\r\n                                        confirmButtonText: this._translateService.instant('OK'),\r\n                                        customClass: {\r\n                                            confirmButton: 'btn btn-primary',\r\n                                        },\r\n                                    });\r\n\r\n                                },\r\n                                (err) => {\r\n                                    Swal.fire({\r\n                                        title: this._translateService.instant('Error'),\r\n                                        text: err.message,\r\n                                        icon: 'error',\r\n                                        confirmButtonText: this._translateService.instant('OK'),\r\n                                        customClass: {\r\n                                            confirmButton: 'btn btn-primary',\r\n                                        },\r\n                                    });\r\n                                }\r\n                            );\r\n                        },\r\n                    },\r\n                    {\r\n                        // cancel registration\r\n                        text: `<i class=\"fa-duotone fa-trash\">&nbsp;</i> ${this._translateService.instant(\r\n                            'Cancel registration'\r\n                        )}`,\r\n                        extend: 'selectedSingle',\r\n                        className: 'action-button-item cancel-button',\r\n                        action: (e: any, dt: any, node: any, config: any) => {\r\n                            let data = dt.row({selected: true}).data();\r\n\r\n                            console.log(data.approval_status);\r\n\r\n                            if (data.approval_status == AppConfig.APPROVE_STATUS.Cancelled) {\r\n                                // Toast\r\n                                this._toastService.warning(this._translateService.instant('Already cancelled'));\r\n                                return;\r\n                            } else if (data.approval_status == AppConfig.APPROVE_STATUS.Approved) {\r\n                                // Toast\r\n                                this._toastService.warning(this._translateService.instant('Cannot cancel approved registration'));\r\n                                return;\r\n                            }\r\n\r\n                            // open modal to confirm and input reason\r\n                            const modalRef = this.modalService.open(\r\n                                ModalInputCancelReasonComponent,\r\n                                {\r\n                                    size: 'lg',\r\n                                    backdrop: 'static',\r\n                                    keyboard: true,\r\n                                }\r\n                            );\r\n\r\n                            modalRef.componentInstance.windowClass = 'modal-center';\r\n                            modalRef.componentInstance.backdrop = 'static';\r\n\r\n                            modalRef.componentInstance.registrations = data;\r\n\r\n                            modalRef.result.then(\r\n                                (result) => {\r\n                                    this._loadingService.show();\r\n\r\n                                    this._registrationService\r\n                                        .cancel(data.id, result.reason)\r\n                                        .subscribe(\r\n                                            (res) => {\r\n                                                this._loadingService.dismiss();\r\n                                                Swal.fire({\r\n                                                    title: this._translateService.instant('Success'),\r\n                                                    text: res.message,\r\n                                                    icon: 'success',\r\n                                                    confirmButtonText:\r\n                                                        this._translateService.instant('OK'),\r\n                                                    customClass: {\r\n                                                        confirmButton: 'btn btn-primary',\r\n                                                    },\r\n                                                });\r\n                                                this.dtElement.dtInstance.then(\r\n                                                    (dtInstance: DataTables.Api) => {\r\n                                                        dtInstance.ajax.reload();\r\n                                                    }\r\n                                                );\r\n                                            },\r\n                                            (err) => {\r\n                                                Swal.fire({\r\n                                                    title: this._translateService.instant('Error'),\r\n                                                    text: err.message,\r\n                                                    icon: 'error',\r\n                                                    confirmButtonText:\r\n                                                        this._translateService.instant('OK'),\r\n                                                    customClass: {\r\n                                                        confirmButton: 'btn btn-primary',\r\n                                                    },\r\n                                                });\r\n                                            }\r\n                                        );\r\n                                },\r\n                                (reason) => {\r\n                                    // dismiss\r\n                                    console.log('reason', reason);\r\n                                }\r\n                            );\r\n                        },\r\n                    },\r\n                    {\r\n                        // button add new group\r\n                        text: `<i class=\"fa-duotone fa-user-plus\"></i> ${this._translateService.instant(\r\n                            'Assign New Group'\r\n                        )}`,\r\n                        extend: 'selectedSingle',\r\n                        className: 'action-button-item',\r\n                        action: (e: any, dt: any, node: any, config: any) => {\r\n                            // Add your code here for handling the button click event\r\n                            const data: Registration = dt.row({selected: true}).data();\r\n\r\n                            const modalRef = this.modalService.open(\r\n                                ModalSuitableGroupsComponent,\r\n                                {\r\n                                    size: 'lg',\r\n                                    backdrop: 'static',\r\n                                    keyboard: true,\r\n                                }\r\n                            );\r\n\r\n                            modalRef.componentInstance.windowClass = 'modal-center';\r\n\r\n                            modalRef.componentInstance.registrations = data;\r\n\r\n                            modalRef.result.then((result) => {\r\n                                if (result) {\r\n                                    this._loadingService.show();\r\n                                    const registration_id = data.id;\r\n                                    const group_id = result.group_id;\r\n                                    this._registrationService\r\n                                        .assignNewGroup(registration_id, group_id)\r\n                                        .subscribe(\r\n                                            (res) => {\r\n                                                Swal.fire({\r\n                                                    title: this._translateService.instant('Success'),\r\n                                                    text: res.message,\r\n                                                    icon: 'success',\r\n                                                    confirmButtonText:\r\n                                                        this._translateService.instant('OK'),\r\n                                                    customClass: {\r\n                                                        confirmButton: 'btn btn-primary',\r\n                                                    },\r\n                                                });\r\n                                                this.dtElement.dtInstance.then(\r\n                                                    (dtInstance: DataTables.Api) => {\r\n                                                        dtInstance.ajax.reload();\r\n                                                    }\r\n                                                );\r\n                                            },\r\n                                            (err) => {\r\n                                                Swal.fire({\r\n                                                    title: this._translateService.instant('Error'),\r\n                                                    text: err.message,\r\n                                                    icon: 'error',\r\n                                                    confirmButtonText:\r\n                                                        this._translateService.instant('OK'),\r\n                                                    customClass: {\r\n                                                        confirmButton: 'btn btn-primary',\r\n                                                    },\r\n                                                });\r\n                                            }\r\n                                        );\r\n                                }\r\n                            });\r\n                        },\r\n                    },\r\n                    {\r\n                        extend: 'excel',\r\n                        text: `<i class=\"fa-regular fa-file-excel\"></i> ${this._translateService.instant(\r\n                            'Export to Excel'\r\n                        )}`,\r\n                        className: 'action-button-item',\r\n                        exportOptions: {\r\n                            columns: ':visible',\r\n                            orthogonal: 'excel',\r\n                            modifier: {\r\n                                order: 'current',\r\n                                page: 'all',\r\n                                selected: false,\r\n                            },\r\n                            customize: function (doc) {\r\n                                doc.styles.tableBodyEven.alignment = 'left';\r\n                                doc.styles.tableBodyOdd.alignment = 'left';\r\n                            },\r\n                        },\r\n                        action: async (e: any, dt: any, button: any, config: any) => {\r\n                            const data = dt.buttons.exportData();\r\n                            await this._exportService.exportExcel(data, 'Registrations.xlsx');\r\n                        }\r\n                    },\r\n                    {\r\n                        extend: 'print',\r\n                        text: `<i class=\"fa-regular fa-print\"></i> ${this._translateService.instant(\r\n                            'Print'\r\n                        )}`,\r\n                        className: 'action-button-item',\r\n                        exportOptions: {\r\n                            columns: ':visible',\r\n                            orthogonal: 'print',\r\n                            modifier: {\r\n                                order: 'current',\r\n                                page: 'all',\r\n                                selected: false,\r\n                            },\r\n                        },\r\n                        action: async (e: any, dt: any, button: any, config: any) => {\r\n                            const data = dt.buttons.exportData();\r\n                            await this._exportService.exportPDF(data, 'Registrations.pdf');\r\n                        }\r\n                    },\r\n\r\n                    {\r\n                        text: `<i class=\"fa-regular fa-rotate\"></i> ${this._translateService.instant(\r\n                            'Sync Payment Status'\r\n                        )}`,\r\n                        className: 'action-button-item approve-button',\r\n                        action: (e: any, dt: any, node: any, config: any) => {\r\n                            this._loadingService.show();\r\n\r\n                            // selected rows\r\n                            const row_data = dt.rows({selected: true}).data();\r\n                            const payment_detail = row_data[0].payment_details;\r\n\r\n\r\n                            if (payment_detail.length == 0) {\r\n                                Swal.fire({\r\n                                    title: this._translateService.instant('Warning'),\r\n                                    text: this._translateService.instant('This registration has no invoice to sync status'),\r\n                                    icon: 'warning',\r\n                                    confirmButtonText: this._translateService.instant('OK'),\r\n                                    customClass: {\r\n                                        confirmButton: 'btn btn-primary',\r\n                                    },\r\n                                });\r\n                                this._loadingService.dismiss();\r\n                                return;\r\n                            }\r\n\r\n                            const payment_ids = payment_detail.map((item) => item.id);\r\n\r\n                            this._paymentService\r\n                                .syncPayments(payment_ids)\r\n                                .subscribe((res) => {\r\n                                    Swal.fire({\r\n                                        title: this._translateService.instant('Success'),\r\n                                        text: \"Payment status synced\",\r\n                                        icon: 'success',\r\n                                        confirmButtonText: this._translateService.instant('OK'),\r\n                                        customClass: {\r\n                                            confirmButton: 'btn btn-primary',\r\n                                        },\r\n                                    });\r\n                                    this.dtElement.dtInstance.then(\r\n                                        (dtInstance: DataTables.Api) => {\r\n                                            dtInstance.ajax.reload();\r\n                                        }\r\n                                    );\r\n                                });\r\n                        },\r\n                    },\r\n                    {\r\n                        text: `<i class=\"fa-sharp fa-light fa-calendar-range\"></i> ${this._translateService.instant(\r\n                            'Send Reminder'\r\n                        )}`,\r\n                        extend: 'selectedSingle',\r\n                        className: 'action-button-item approve-button',\r\n                        action: (e: any, dt: any, node: any, config: any) => {\r\n                            this._loadingService.show();\r\n                            let data = dt.row({selected: true}).data();\r\n                            this._registrationService.sendReminderPayment(data.id).subscribe(\r\n                                (res) => {\r\n                                    Swal.fire({\r\n                                        title: this._translateService.instant('Success'),\r\n                                        text: res.message,\r\n                                        icon: 'success',\r\n                                        confirmButtonText: this._translateService.instant('OK'),\r\n                                        customClass: {\r\n                                            confirmButton: 'btn btn-primary',\r\n                                        },\r\n                                    });\r\n                                    this.dtElement.dtInstance.then(\r\n                                        (dtInstance: DataTables.Api) => {\r\n                                            dtInstance.ajax.reload();\r\n                                        }\r\n                                    );\r\n                                },\r\n                                (err) => {\r\n                                    Swal.fire({\r\n                                        title: this._translateService.instant('Error'),\r\n                                        text: err.message,\r\n                                        icon: 'error',\r\n                                        confirmButtonText: this._translateService.instant('OK'),\r\n                                        customClass: {\r\n                                            confirmButton: 'btn btn-primary',\r\n                                        },\r\n                                    });\r\n                                }\r\n                            );\r\n                        },\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                text:\r\n                    '<i class=\"fas fa-edit mr-1\"></i> ' +\r\n                    this._translateService.instant('Edit Club'),\r\n                titleAttr: this._translateService.instant('Edit Club'),\r\n                action: (e, dt, node, config) => {\r\n                    let selectedRows = dt.rows({selected: true}).data();\r\n\r\n                    if (selectedRows.length == 0) {\r\n                        Swal.fire({\r\n                            title: 'Please select at least one player',\r\n                            icon: 'warning',\r\n                        });\r\n                        return;\r\n                    }\r\n\r\n                    let approveStatus = selectedRows[0].approval_status;\r\n\r\n                    console.log(\"approveStatus\", selectedRows);\r\n\r\n\r\n                    if (approveStatus == AppConfig.APPROVE_STATUS.Approved) {\r\n                        Swal.fire({\r\n                            title: this._translateService.instant('This registration is already approved, cannot edit'),\r\n                            icon: 'warning',\r\n                        });\r\n                        return;\r\n                    }\r\n\r\n                    // open modal change club\r\n\r\n\r\n                    // const clubs\r\n                    this.getAllClubsIsActive().then((clubs) => {\r\n                        console.log(clubs);\r\n                        const modalRef = this.modalService.open(\r\n                            ModalChangeClubComponent,\r\n                            {\r\n                                size: 'md',\r\n                                backdrop: 'static',\r\n                                keyboard: false,\r\n                            }\r\n                        );\r\n\r\n                        modalRef.componentInstance.clubs = clubs;\r\n                        modalRef.componentInstance.current_club = selectedRows[0].club;\r\n\r\n                        modalRef.result.then(\r\n                            (result) => {\r\n                                console.log(result);\r\n                                this._loadingService.show();\r\n                                this._registrationService\r\n                                    .changeClub(selectedRows[0].id, result.club_id)\r\n                                    .subscribe((res) => {\r\n                                        this._loadingService.dismiss();\r\n                                        this.reloadDataTable();\r\n                                    });\r\n                            }\r\n                        )\r\n                    })\r\n\r\n\r\n                },\r\n                extend: 'selected',\r\n            },\r\n\r\n            {\r\n                // column\r\n                extend: 'colvis',\r\n                text: this._translateService.instant('Column'),\r\n            },\r\n        ];\r\n\r\n        if (this.is_validate_required) {\r\n            // add validate button to index 1\r\n            buttons.splice(1, 0, {\r\n                // Validate button\r\n                text:\r\n                    '<i class=\"fa-duotone fa-check-double\"></i> ' +\r\n                    this._translateService.instant('Validate'),\r\n                action: (e, dt, node, config) => {\r\n                    let selectedRows = dt.rows({selected: true}).data();\r\n\r\n                    let registrations = [];\r\n                    selectedRows.each((row) => {\r\n                        registrations.push(row);\r\n                    });\r\n                    console.log(\"selected row\", selectedRows);\r\n                    let data = {\r\n                        registrations: registrations,\r\n                        status: selectedRows[0].player.validate_status\r\n                    };\r\n\r\n                    const cancel_status: boolean = data.registrations.some(\r\n                        (reg) => reg.approval_status == AppConfig.APPROVE_STATUS.Cancelled\r\n                    )\r\n\r\n                    if (cancel_status) {\r\n                        Swal.fire({\r\n                            title: this._translateService.instant('Warning'),\r\n                            text: this._translateService.instant('Cannot validate cancelled registration'),\r\n                            icon: 'warning',\r\n                            confirmButtonText: this._translateService.instant('OK'),\r\n                            customClass: {\r\n                                confirmButton: 'btn btn-primary',\r\n                            }\r\n                        });\r\n                        return;\r\n                    }\r\n\r\n                    if (\r\n                        data.status == AppConfig.VALIDATE_STATUS.Updated ||\r\n                        data.status == AppConfig.VALIDATE_STATUS.Pending\r\n                    ) {\r\n                        this.modalValidatorOpen(data);\r\n                    } else {\r\n                        Swal.fire({\r\n                            title: this._translateService.instant(\r\n                                'Only \"Updated\" and \"Pending\" status can be validate'\r\n                            ),\r\n                            icon: 'warning',\r\n                        });\r\n                    }\r\n                },\r\n                extend: 'selected',\r\n            });\r\n        }\r\n\r\n\r\n        this.dtOptions = {\r\n            dom: 'B<\"row ml-2 mr-2\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-2 pr-2\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\r\n            select: 'single',\r\n            serverSide: true,\r\n            processing: true,\r\n            rowId: 'id',\r\n            stateSave: true,\r\n            ajax: (dataTablesParameters: any, callback) => {\r\n                dataTablesParameters['page_size'] = dataTablesParameters.length;\r\n                dataTablesParameters['page'] =\r\n                    Math.floor(dataTablesParameters.start / dataTablesParameters.length) +\r\n                    1;\r\n                dataTablesParameters['season_id'] = this.seasonId;\r\n                dataTablesParameters['type'] = 'registration';\r\n\r\n                // Determine sort column based on index\r\n                const sortColumnIndex = dataTablesParameters.order[0].column;\r\n\r\n                const sortDirection = dataTablesParameters.order[0].dir;\r\n\r\n                const payload = {\r\n                    season_id: dataTablesParameters['season_id'],\r\n                    type: dataTablesParameters['type'],\r\n                    page: dataTablesParameters['page'],\r\n                    page_size: dataTablesParameters['page_size'],\r\n                    search: dataTablesParameters.search.value,\r\n                    sort_column: dataTablesParameters.columns[sortColumnIndex].data,\r\n                    sort_direction: sortDirection, // Sort direction\r\n                };\r\n\r\n                this._http\r\n                    .post<any>(\r\n                        `${environment.apiUrl}/registrations/by-season-and-type`,\r\n                        payload\r\n                    )\r\n                    .subscribe((resp: any) => {\r\n                        this._loadingService.dismiss();\r\n\r\n                        if (\r\n                            resp &&\r\n                            resp.recordsTotal !== undefined &&\r\n                            resp.recordsFiltered !== undefined\r\n                        ) {\r\n                            callback({\r\n                                recordsTotal: resp.recordsTotal,\r\n                                recordsFiltered: resp.recordsFiltered,\r\n                                data: resp.data,\r\n                            });\r\n                        } else {\r\n                            console.error(\r\n                                'Error: Server response lacks required pagination data.'\r\n                            );\r\n                        }\r\n                    });\r\n            },\r\n            pagingType: 'simple_numbers',\r\n            language: this._commonsService.dataTableDefaults.lang,\r\n            paging: true,\r\n            paginate: {\r\n                first: this._translateService.instant('First'),\r\n                last: this._translateService.instant('Last'),\r\n                next: this._translateService.instant('Next'),\r\n                previous: this._translateService.instant('Previous'),\r\n            },\r\n            lengthMenu: [10, 25, 50, 100],\r\n            pageLength: 10,\r\n            order: [7, 'desc'],\r\n            responsive: true,\r\n            scrollX: false,\r\n            columnDefs: [\r\n                {responsivePriority: 1, targets: 1},\r\n                {responsivePriority: 2, targets: 9},\r\n                {responsivePriority: 3, targets: 0},\r\n            ],\r\n            columns: defaultColumns,\r\n            buttons: {\r\n                dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n                buttons: buttons,\r\n            },\r\n        };\r\n\r\n\r\n    }\r\n\r\n    listenNotification() {\r\n        // if(this._fcmService.isPushNotificationsAvailable){\r\n        //   PushNotifications.addListener('pushNotificationReceived', (notification) => {\r\n        //     console.log('pushNotificationReceived', notification);\r\n        //     this.reloadDataTable();\r\n        //   })\r\n        // }else{\r\n        //    this._fcmService.currentMessage.subscribe((message) => {\r\n        //     console.log('New message received:', message);\r\n        //     this.reloadDataTable();\r\n        //   })\r\n        // }\r\n    }\r\n\r\n    setContentHeader() {\r\n        this.contentHeader = {\r\n            headerTitle: this._translateService.instant('All Registrations'),\r\n            actionButton: false,\r\n            breadcrumb: {\r\n                type: '',\r\n                links: [\r\n                    {\r\n                        name: 'Registrations',\r\n                        isLink: false,\r\n                    },\r\n                    {\r\n                        name: this._translateService.instant('All Registrations'),\r\n                        isLink: false,\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    }\r\n\r\n    onSuccess($event) {\r\n        console.log('onSuccess', $event);\r\n        this.reloadDataTable();\r\n    }\r\n\r\n    getActiveSeasons() {\r\n        let status = 'active';\r\n        this.seasonService.getSeasons(status).subscribe(\r\n            (data) => {\r\n                this.seasons = data;\r\n                this.season = this.seasons[0];\r\n                this.seasonId = this.season.id;\r\n                if (\r\n                    this._settingsService.initSettingsValue &&\r\n                    this._settingsService.initSettingsValue.hasOwnProperty(\r\n                        'is_validate_required'\r\n                    )\r\n                ) {\r\n                    this.is_validate_required =\r\n                        this._settingsService.initSettingsValue.is_validate_required;\r\n                }\r\n                this.initDatatable();\r\n                this.dtTrigger.next(this.dtOptions);\r\n            },\r\n            (error) => {\r\n                Swal.fire({\r\n                    title: 'Error',\r\n                    text: error.message,\r\n                    icon: 'error',\r\n                    confirmButtonText: this._translateService.instant('OK'),\r\n                });\r\n            }\r\n        );\r\n    }\r\n\r\n    onChangeSeason($event) {\r\n        console.log('onChangeSeason', $event);\r\n        this.season = $event;\r\n        this.seasonId = this.season.id;\r\n        this.reloadDataTable();\r\n    }\r\n\r\n    reloadDataTable(): void {\r\n        this._loadingService.show();\r\n        console.log('reloadDataTable');\r\n        if (this.dtElement && this.dtElement.dtInstance)\r\n            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n                dtInstance.ajax.reload();\r\n            });\r\n    }\r\n\r\n    modalValidatorOpen(data) {\r\n        const modalRef = this.modalService.open(ValidatorComponent, {\r\n            size: 'lg',\r\n            backdrop: 'static',\r\n            keyboard: true,\r\n        });\r\n\r\n        console.log(\"data\", data);\r\n\r\n        modalRef.componentInstance.registrations = data.registrations[0];\r\n        modalRef.componentInstance.status = data.status;\r\n        modalRef.componentInstance.dtElement = this.dtElement;\r\n\r\n        //    let registration_id = data.registrations[0].id;\r\n\r\n        // modalRef.result.then(\r\n        //   (result) => {\r\n        //     this._registrationService.validateRegistration(registration_id,result).toPromise().then((data) => {\r\n        //       console.log('data', data);\r\n        //       this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n        //         dtInstance.ajax.reload();\r\n        //       });\r\n        //     });\r\n        //   }\r\n        // );\r\n    }\r\n\r\n    showClubName(row) {\r\n        return new Promise((resolve, reject) => {\r\n            let selectedValidateStatus = row.validate_status;\r\n            let selectedClub = row.club_id;\r\n            this.getAllClubsIsActive().then((data) => {\r\n                this.fields.forEach((field) => {\r\n                    if (field.key == 'club_id') {\r\n                        field.templateOptions.options = data;\r\n                        field.defaultValue = selectedClub;\r\n                        resolve(true);\r\n                    }\r\n                });\r\n            });\r\n        });\r\n    }\r\n\r\n    getAllClubsIsActive() {\r\n        return new Promise((resolve, reject) => {\r\n            this._clubService\r\n                .getAllClubsIsActive()\r\n                .toPromise()\r\n                .then((data) => {\r\n                    resolve(data);\r\n                });\r\n        });\r\n    }\r\n\r\n    editor(action, row) {\r\n        this.params.action = action;\r\n        this.params.row = row ? row : null;\r\n        if (action == 'edit' && row) {\r\n            let approveStatus = row.approval_status;\r\n            if (approveStatus == AppConfig.APPROVE_STATUS.Approved) {\r\n                Swal.fire({\r\n                    title: this._translateService.instant('This registration is already approved, cannot edit'),\r\n                    icon: 'warning',\r\n                });\r\n                return;\r\n            }\r\n        }\r\n\r\n        this.showClubName(row).then((data) => {\r\n            if (data) {\r\n                this._coreSidebarService\r\n                    .getSidebarRegistry(this.table_name)\r\n                    .toggleOpen();\r\n            }\r\n        });\r\n    }\r\n\r\n    getSeason(id) {\r\n        return new Promise((resolve, reject) => {\r\n            this._registrationService.getSeasonByID(id).subscribe((data) => {\r\n                resolve(data);\r\n            });\r\n        });\r\n    }\r\n\r\n    ngAfterViewInit(): void {\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        this.dtTrigger.unsubscribe();\r\n        this.subscriptions.forEach((subscription) => subscription.unsubscribe());\r\n    }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <div class=\"row mb-1\">\r\n      <!-- ng select season -->\r\n      <div class=\"col\">\r\n        <label for=\"season\">{{ 'Season' | translate }}</label>\r\n        <ng-select [searchable]=\"true\" [clearable]=\"false\" placeholder=\"{{'Select Season'|translate}}\"\r\n          [(ngModel)]=\"season\" (change)=\"onChangeSeason($event)\">\r\n          <ng-option *ngFor=\"let season of seasons\" [value]=\"season\">{{ season.name | translate }}\r\n          </ng-option>\r\n        </ng-select>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-12\">\r\n        <div class=\"card\">\r\n          <div class=\"card-header\">\r\n            <h4 class=\"card-title\">{{ 'Registration List' | translate }}</h4>\r\n          </div>\r\n          <div>\r\n            <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table border row-border hover\">\r\n            </table>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n  <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n  </app-editor-sidebar>\r\n</core-sidebar>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/*!\n * lightgallery | 2.7.2 | September 20th 2023\n * http://www.lightgalleryjs.com/\n * Copyright (c) 2020 Sachin Neravath;\n * @license GPLv3\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\n\n/**\r\n * List of lightGallery events\r\n * All events should be documented here\r\n * Below interfaces are used to build the website documentations\r\n * */\nvar lGEvents = {\n  afterAppendSlide: 'lgAfterAppendSlide',\n  init: 'lgInit',\n  hasVideo: 'lgHasVideo',\n  containerResize: 'lgContainerResize',\n  updateSlides: 'lgUpdateSlides',\n  afterAppendSubHtml: 'lgAfterAppendSubHtml',\n  beforeOpen: 'lgBeforeOpen',\n  afterOpen: 'lgAfterOpen',\n  slideItemLoad: 'lgSlideItemLoad',\n  beforeSlide: 'lgBeforeSlide',\n  afterSlide: 'lgAfterSlide',\n  posterClick: 'lgPosterClick',\n  dragStart: 'lgDragStart',\n  dragMove: 'lgDragMove',\n  dragEnd: 'lgDragEnd',\n  beforeNextSlide: 'lgBeforeNextSlide',\n  beforePrevSlide: 'lgBeforePrevSlide',\n  beforeClose: 'lgBeforeClose',\n  afterClose: 'lgAfterClose',\n  rotateLeft: 'lgRotateLeft',\n  rotateRight: 'lgRotateRight',\n  flipHorizontal: 'lgFlipHorizontal',\n  flipVertical: 'lgFlipVertical',\n  autoplay: 'lgAutoplay',\n  autoplayStart: 'lgAutoplayStart',\n  autoplayStop: 'lgAutoplayStop'\n};\nvar autoplaySettings = {\n  autoplay: true,\n  slideShowAutoplay: false,\n  slideShowInterval: 5000,\n  progressBar: true,\n  forceSlideShowAutoplay: false,\n  autoplayControls: true,\n  appendAutoplayControlsTo: '.lg-toolbar',\n  autoplayPluginStrings: {\n    toggleAutoplay: 'Toggle Autoplay'\n  }\n};\n\n/**\r\n * Creates the autoplay plugin.\r\n * @param {object} element - lightGallery element\r\n */\nvar Autoplay = /** @class */function () {\n  function Autoplay(instance) {\n    this.core = instance;\n    // extend module default settings with lightGallery core settings\n    this.settings = __assign(__assign({}, autoplaySettings), this.core.settings);\n    return this;\n  }\n  Autoplay.prototype.init = function () {\n    var _this = this;\n    if (!this.settings.autoplay) {\n      return;\n    }\n    this.interval = false;\n    // Identify if slide happened from autoplay\n    this.fromAuto = true;\n    // Identify if autoplay canceled from touch/drag\n    this.pausedOnTouchDrag = false;\n    this.pausedOnSlideChange = false;\n    // append autoplay controls\n    if (this.settings.autoplayControls) {\n      this.controls();\n    }\n    // Create progress bar\n    if (this.settings.progressBar) {\n      this.core.outer.append('<div class=\"lg-progress-bar\"><div class=\"lg-progress\"></div></div>');\n    }\n    // Start autoplay\n    if (this.settings.slideShowAutoplay) {\n      this.core.LGel.once(lGEvents.slideItemLoad + \".autoplay\", function () {\n        _this.startAutoPlay();\n      });\n    }\n    // cancel interval on touchstart and dragstart\n    this.core.LGel.on(lGEvents.dragStart + \".autoplay touchstart.lg.autoplay\", function () {\n      if (_this.interval) {\n        _this.stopAutoPlay();\n        _this.pausedOnTouchDrag = true;\n      }\n    });\n    // restore autoplay if autoplay canceled from touchstart / dragstart\n    this.core.LGel.on(lGEvents.dragEnd + \".autoplay touchend.lg.autoplay\", function () {\n      if (!_this.interval && _this.pausedOnTouchDrag) {\n        _this.startAutoPlay();\n        _this.pausedOnTouchDrag = false;\n      }\n    });\n    this.core.LGel.on(lGEvents.beforeSlide + \".autoplay\", function () {\n      _this.showProgressBar();\n      if (!_this.fromAuto && _this.interval) {\n        _this.stopAutoPlay();\n        _this.pausedOnSlideChange = true;\n      } else {\n        _this.pausedOnSlideChange = false;\n      }\n      _this.fromAuto = false;\n    });\n    // restore autoplay if autoplay canceled from touchstart / dragstart\n    this.core.LGel.on(lGEvents.afterSlide + \".autoplay\", function () {\n      if (_this.pausedOnSlideChange && !_this.interval && _this.settings.forceSlideShowAutoplay) {\n        _this.startAutoPlay();\n        _this.pausedOnSlideChange = false;\n      }\n    });\n    // set progress\n    this.showProgressBar();\n  };\n  Autoplay.prototype.showProgressBar = function () {\n    var _this = this;\n    if (this.settings.progressBar && this.fromAuto) {\n      var _$progressBar_1 = this.core.outer.find('.lg-progress-bar');\n      var _$progress_1 = this.core.outer.find('.lg-progress');\n      if (this.interval) {\n        _$progress_1.removeAttr('style');\n        _$progressBar_1.removeClass('lg-start');\n        setTimeout(function () {\n          _$progress_1.css('transition', 'width ' + (_this.core.settings.speed + _this.settings.slideShowInterval) + 'ms ease 0s');\n          _$progressBar_1.addClass('lg-start');\n        }, 20);\n      }\n    }\n  };\n  // Manage autoplay via play/stop buttons\n  Autoplay.prototype.controls = function () {\n    var _this = this;\n    var _html = \"<button aria-label=\\\"\" + this.settings.autoplayPluginStrings['toggleAutoplay'] + \"\\\" type=\\\"button\\\" class=\\\"lg-autoplay-button lg-icon\\\"></button>\";\n    // Append autoplay controls\n    this.core.outer.find(this.settings.appendAutoplayControlsTo).append(_html);\n    this.core.outer.find('.lg-autoplay-button').first().on('click.lg.autoplay', function () {\n      if (_this.core.outer.hasClass('lg-show-autoplay')) {\n        _this.stopAutoPlay();\n      } else {\n        if (!_this.interval) {\n          _this.startAutoPlay();\n        }\n      }\n    });\n  };\n  // Autostart gallery\n  Autoplay.prototype.startAutoPlay = function () {\n    var _this = this;\n    this.core.outer.find('.lg-progress').css('transition', 'width ' + (this.core.settings.speed + this.settings.slideShowInterval) + 'ms ease 0s');\n    this.core.outer.addClass('lg-show-autoplay');\n    this.core.outer.find('.lg-progress-bar').addClass('lg-start');\n    this.core.LGel.trigger(lGEvents.autoplayStart, {\n      index: this.core.index\n    });\n    this.interval = setInterval(function () {\n      if (_this.core.index + 1 < _this.core.galleryItems.length) {\n        _this.core.index++;\n      } else {\n        _this.core.index = 0;\n      }\n      _this.core.LGel.trigger(lGEvents.autoplay, {\n        index: _this.core.index\n      });\n      _this.fromAuto = true;\n      _this.core.slide(_this.core.index, false, false, 'next');\n    }, this.core.settings.speed + this.settings.slideShowInterval);\n  };\n  // cancel Autostart\n  Autoplay.prototype.stopAutoPlay = function () {\n    if (this.interval) {\n      this.core.LGel.trigger(lGEvents.autoplayStop, {\n        index: this.core.index\n      });\n      this.core.outer.find('.lg-progress').removeAttr('style');\n      this.core.outer.removeClass('lg-show-autoplay');\n      this.core.outer.find('.lg-progress-bar').removeClass('lg-start');\n    }\n    clearInterval(this.interval);\n    this.interval = false;\n  };\n  Autoplay.prototype.closeGallery = function () {\n    this.stopAutoPlay();\n  };\n  Autoplay.prototype.destroy = function () {\n    if (this.settings.autoplay) {\n      this.core.outer.find('.lg-progress-bar').remove();\n    }\n    // Remove all event listeners added by autoplay plugin\n    this.core.LGel.off('.lg.autoplay');\n    this.core.LGel.off('.autoplay');\n  };\n  return Autoplay;\n}();\nexport default Autoplay;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "lGEvents", "afterAppendSlide", "init", "hasVideo", "containerResize", "updateSlides", "afterAppendSubHtml", "beforeOpen", "afterOpen", "slideItemLoad", "beforeSlide", "afterSlide", "posterClick", "dragStart", "dragMove", "dragEnd", "beforeNextSlide", "beforePrevSlide", "beforeClose", "afterClose", "rotateLeft", "rotateRight", "flipHorizontal", "flipVertical", "autoplay", "autoplayStart", "autoplayStop", "autoplaySettings", "slideShowAutoplay", "slideShowInterval", "progressBar", "forceSlideShowAutoplay", "autoplayControls", "appendAutoplayControlsTo", "autoplayPluginStrings", "toggleAutoplay", "Autoplay", "instance", "core", "settings", "_this", "interval", "fromAuto", "pausedOnTouchDrag", "pausedOnSlideChange", "controls", "outer", "append", "LGel", "once", "startAutoPlay", "on", "stopAutoPlay", "showProgressBar", "_$progressBar_1", "find", "_$progress_1", "removeAttr", "removeClass", "setTimeout", "css", "speed", "addClass", "_html", "first", "hasClass", "trigger", "index", "setInterval", "galleryItems", "slide", "clearInterval", "closeGallery", "destroy", "remove", "off"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lightgallery/plugins/autoplay/lg-autoplay.es5.js"], "sourcesContent": ["/*!\n * lightgallery | 2.7.2 | September 20th 2023\n * http://www.lightgalleryjs.com/\n * Copyright (c) 2020 Sachin Neravath;\n * @license GPLv3\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\n/**\r\n * List of lightGallery events\r\n * All events should be documented here\r\n * Below interfaces are used to build the website documentations\r\n * */\r\nvar lGEvents = {\r\n    afterAppendSlide: 'lgAfterAppendSlide',\r\n    init: 'lgInit',\r\n    hasVideo: 'lgHasVideo',\r\n    containerResize: 'lgContainerResize',\r\n    updateSlides: 'lgUpdateSlides',\r\n    afterAppendSubHtml: 'lgAfterAppendSubHtml',\r\n    beforeOpen: 'lgBeforeOpen',\r\n    afterOpen: 'lgAfterOpen',\r\n    slideItemLoad: 'lgSlideItemLoad',\r\n    beforeSlide: 'lgBeforeSlide',\r\n    afterSlide: 'lgAfterSlide',\r\n    posterClick: 'lgPosterClick',\r\n    dragStart: 'lgDragStart',\r\n    dragMove: 'lgDragMove',\r\n    dragEnd: 'lgDragEnd',\r\n    beforeNextSlide: 'lgBeforeNextSlide',\r\n    beforePrevSlide: 'lgBeforePrevSlide',\r\n    beforeClose: 'lgBeforeClose',\r\n    afterClose: 'lgAfterClose',\r\n    rotateLeft: 'lgRotateLeft',\r\n    rotateRight: 'lgRotateRight',\r\n    flipHorizontal: 'lgFlipHorizontal',\r\n    flipVertical: 'lgFlipVertical',\r\n    autoplay: 'lgAutoplay',\r\n    autoplayStart: 'lgAutoplayStart',\r\n    autoplayStop: 'lgAutoplayStop',\r\n};\n\nvar autoplaySettings = {\r\n    autoplay: true,\r\n    slideShowAutoplay: false,\r\n    slideShowInterval: 5000,\r\n    progressBar: true,\r\n    forceSlideShowAutoplay: false,\r\n    autoplayControls: true,\r\n    appendAutoplayControlsTo: '.lg-toolbar',\r\n    autoplayPluginStrings: {\r\n        toggleAutoplay: 'Toggle Autoplay',\r\n    },\r\n};\n\n/**\r\n * Creates the autoplay plugin.\r\n * @param {object} element - lightGallery element\r\n */\r\nvar Autoplay = /** @class */ (function () {\r\n    function Autoplay(instance) {\r\n        this.core = instance;\r\n        // extend module default settings with lightGallery core settings\r\n        this.settings = __assign(__assign({}, autoplaySettings), this.core.settings);\r\n        return this;\r\n    }\r\n    Autoplay.prototype.init = function () {\r\n        var _this = this;\r\n        if (!this.settings.autoplay) {\r\n            return;\r\n        }\r\n        this.interval = false;\r\n        // Identify if slide happened from autoplay\r\n        this.fromAuto = true;\r\n        // Identify if autoplay canceled from touch/drag\r\n        this.pausedOnTouchDrag = false;\r\n        this.pausedOnSlideChange = false;\r\n        // append autoplay controls\r\n        if (this.settings.autoplayControls) {\r\n            this.controls();\r\n        }\r\n        // Create progress bar\r\n        if (this.settings.progressBar) {\r\n            this.core.outer.append('<div class=\"lg-progress-bar\"><div class=\"lg-progress\"></div></div>');\r\n        }\r\n        // Start autoplay\r\n        if (this.settings.slideShowAutoplay) {\r\n            this.core.LGel.once(lGEvents.slideItemLoad + \".autoplay\", function () {\r\n                _this.startAutoPlay();\r\n            });\r\n        }\r\n        // cancel interval on touchstart and dragstart\r\n        this.core.LGel.on(lGEvents.dragStart + \".autoplay touchstart.lg.autoplay\", function () {\r\n            if (_this.interval) {\r\n                _this.stopAutoPlay();\r\n                _this.pausedOnTouchDrag = true;\r\n            }\r\n        });\r\n        // restore autoplay if autoplay canceled from touchstart / dragstart\r\n        this.core.LGel.on(lGEvents.dragEnd + \".autoplay touchend.lg.autoplay\", function () {\r\n            if (!_this.interval && _this.pausedOnTouchDrag) {\r\n                _this.startAutoPlay();\r\n                _this.pausedOnTouchDrag = false;\r\n            }\r\n        });\r\n        this.core.LGel.on(lGEvents.beforeSlide + \".autoplay\", function () {\r\n            _this.showProgressBar();\r\n            if (!_this.fromAuto && _this.interval) {\r\n                _this.stopAutoPlay();\r\n                _this.pausedOnSlideChange = true;\r\n            }\r\n            else {\r\n                _this.pausedOnSlideChange = false;\r\n            }\r\n            _this.fromAuto = false;\r\n        });\r\n        // restore autoplay if autoplay canceled from touchstart / dragstart\r\n        this.core.LGel.on(lGEvents.afterSlide + \".autoplay\", function () {\r\n            if (_this.pausedOnSlideChange &&\r\n                !_this.interval &&\r\n                _this.settings.forceSlideShowAutoplay) {\r\n                _this.startAutoPlay();\r\n                _this.pausedOnSlideChange = false;\r\n            }\r\n        });\r\n        // set progress\r\n        this.showProgressBar();\r\n    };\r\n    Autoplay.prototype.showProgressBar = function () {\r\n        var _this = this;\r\n        if (this.settings.progressBar && this.fromAuto) {\r\n            var _$progressBar_1 = this.core.outer.find('.lg-progress-bar');\r\n            var _$progress_1 = this.core.outer.find('.lg-progress');\r\n            if (this.interval) {\r\n                _$progress_1.removeAttr('style');\r\n                _$progressBar_1.removeClass('lg-start');\r\n                setTimeout(function () {\r\n                    _$progress_1.css('transition', 'width ' +\r\n                        (_this.core.settings.speed +\r\n                            _this.settings.slideShowInterval) +\r\n                        'ms ease 0s');\r\n                    _$progressBar_1.addClass('lg-start');\r\n                }, 20);\r\n            }\r\n        }\r\n    };\r\n    // Manage autoplay via play/stop buttons\r\n    Autoplay.prototype.controls = function () {\r\n        var _this = this;\r\n        var _html = \"<button aria-label=\\\"\" + this.settings.autoplayPluginStrings['toggleAutoplay'] + \"\\\" type=\\\"button\\\" class=\\\"lg-autoplay-button lg-icon\\\"></button>\";\r\n        // Append autoplay controls\r\n        this.core.outer\r\n            .find(this.settings.appendAutoplayControlsTo)\r\n            .append(_html);\r\n        this.core.outer\r\n            .find('.lg-autoplay-button')\r\n            .first()\r\n            .on('click.lg.autoplay', function () {\r\n            if (_this.core.outer.hasClass('lg-show-autoplay')) {\r\n                _this.stopAutoPlay();\r\n            }\r\n            else {\r\n                if (!_this.interval) {\r\n                    _this.startAutoPlay();\r\n                }\r\n            }\r\n        });\r\n    };\r\n    // Autostart gallery\r\n    Autoplay.prototype.startAutoPlay = function () {\r\n        var _this = this;\r\n        this.core.outer\r\n            .find('.lg-progress')\r\n            .css('transition', 'width ' +\r\n            (this.core.settings.speed +\r\n                this.settings.slideShowInterval) +\r\n            'ms ease 0s');\r\n        this.core.outer.addClass('lg-show-autoplay');\r\n        this.core.outer.find('.lg-progress-bar').addClass('lg-start');\r\n        this.core.LGel.trigger(lGEvents.autoplayStart, {\r\n            index: this.core.index,\r\n        });\r\n        this.interval = setInterval(function () {\r\n            if (_this.core.index + 1 < _this.core.galleryItems.length) {\r\n                _this.core.index++;\r\n            }\r\n            else {\r\n                _this.core.index = 0;\r\n            }\r\n            _this.core.LGel.trigger(lGEvents.autoplay, {\r\n                index: _this.core.index,\r\n            });\r\n            _this.fromAuto = true;\r\n            _this.core.slide(_this.core.index, false, false, 'next');\r\n        }, this.core.settings.speed + this.settings.slideShowInterval);\r\n    };\r\n    // cancel Autostart\r\n    Autoplay.prototype.stopAutoPlay = function () {\r\n        if (this.interval) {\r\n            this.core.LGel.trigger(lGEvents.autoplayStop, {\r\n                index: this.core.index,\r\n            });\r\n            this.core.outer.find('.lg-progress').removeAttr('style');\r\n            this.core.outer.removeClass('lg-show-autoplay');\r\n            this.core.outer.find('.lg-progress-bar').removeClass('lg-start');\r\n        }\r\n        clearInterval(this.interval);\r\n        this.interval = false;\r\n    };\r\n    Autoplay.prototype.closeGallery = function () {\r\n        this.stopAutoPlay();\r\n    };\r\n    Autoplay.prototype.destroy = function () {\r\n        if (this.settings.autoplay) {\r\n            this.core.outer.find('.lg-progress-bar').remove();\r\n        }\r\n        // Remove all event listeners added by autoplay plugin\r\n        this.core.LGel.off('.lg.autoplay');\r\n        this.core.LGel.off('.autoplay');\r\n    };\r\n    return Autoplay;\r\n}());\n\nexport default Autoplay;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,QAAQ,GAAG,SAAAA,CAAA,EAAW;EACtBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASF,QAAQA,CAACG,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAChF;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAIO,QAAQ,GAAG;EACXC,gBAAgB,EAAE,oBAAoB;EACtCC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,eAAe,EAAE,mBAAmB;EACpCC,YAAY,EAAE,gBAAgB;EAC9BC,kBAAkB,EAAE,sBAAsB;EAC1CC,UAAU,EAAE,cAAc;EAC1BC,SAAS,EAAE,aAAa;EACxBC,aAAa,EAAE,iBAAiB;EAChCC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,SAAS,EAAE,aAAa;EACxBC,QAAQ,EAAE,YAAY;EACtBC,OAAO,EAAE,WAAW;EACpBC,eAAe,EAAE,mBAAmB;EACpCC,eAAe,EAAE,mBAAmB;EACpCC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,cAAc,EAAE,kBAAkB;EAClCC,YAAY,EAAE,gBAAgB;EAC9BC,QAAQ,EAAE,YAAY;EACtBC,aAAa,EAAE,iBAAiB;EAChCC,YAAY,EAAE;AAClB,CAAC;AAED,IAAIC,gBAAgB,GAAG;EACnBH,QAAQ,EAAE,IAAI;EACdI,iBAAiB,EAAE,KAAK;EACxBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,sBAAsB,EAAE,KAAK;EAC7BC,gBAAgB,EAAE,IAAI;EACtBC,wBAAwB,EAAE,aAAa;EACvCC,qBAAqB,EAAE;IACnBC,cAAc,EAAE;EACpB;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,QAAQ,EAAE;IACxB,IAAI,CAACC,IAAI,GAAGD,QAAQ;IACpB;IACA,IAAI,CAACE,QAAQ,GAAGrD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyC,gBAAgB,CAAC,EAAE,IAAI,CAACW,IAAI,CAACC,QAAQ,CAAC;IAC5E,OAAO,IAAI;EACf;EACAH,QAAQ,CAACxC,SAAS,CAACM,IAAI,GAAG,YAAY;IAClC,IAAIsC,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACD,QAAQ,CAACf,QAAQ,EAAE;MACzB;IACJ;IACA,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,IAAI,CAACL,QAAQ,CAACP,gBAAgB,EAAE;MAChC,IAAI,CAACa,QAAQ,CAAC,CAAC;IACnB;IACA;IACA,IAAI,IAAI,CAACN,QAAQ,CAACT,WAAW,EAAE;MAC3B,IAAI,CAACQ,IAAI,CAACQ,KAAK,CAACC,MAAM,CAAC,oEAAoE,CAAC;IAChG;IACA;IACA,IAAI,IAAI,CAACR,QAAQ,CAACX,iBAAiB,EAAE;MACjC,IAAI,CAACU,IAAI,CAACU,IAAI,CAACC,IAAI,CAACjD,QAAQ,CAACS,aAAa,GAAG,WAAW,EAAE,YAAY;QAClE+B,KAAK,CAACU,aAAa,CAAC,CAAC;MACzB,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACZ,IAAI,CAACU,IAAI,CAACG,EAAE,CAACnD,QAAQ,CAACa,SAAS,GAAG,kCAAkC,EAAE,YAAY;MACnF,IAAI2B,KAAK,CAACC,QAAQ,EAAE;QAChBD,KAAK,CAACY,YAAY,CAAC,CAAC;QACpBZ,KAAK,CAACG,iBAAiB,GAAG,IAAI;MAClC;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACL,IAAI,CAACU,IAAI,CAACG,EAAE,CAACnD,QAAQ,CAACe,OAAO,GAAG,gCAAgC,EAAE,YAAY;MAC/E,IAAI,CAACyB,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACG,iBAAiB,EAAE;QAC5CH,KAAK,CAACU,aAAa,CAAC,CAAC;QACrBV,KAAK,CAACG,iBAAiB,GAAG,KAAK;MACnC;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,IAAI,CAACU,IAAI,CAACG,EAAE,CAACnD,QAAQ,CAACU,WAAW,GAAG,WAAW,EAAE,YAAY;MAC9D8B,KAAK,CAACa,eAAe,CAAC,CAAC;MACvB,IAAI,CAACb,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACC,QAAQ,EAAE;QACnCD,KAAK,CAACY,YAAY,CAAC,CAAC;QACpBZ,KAAK,CAACI,mBAAmB,GAAG,IAAI;MACpC,CAAC,MACI;QACDJ,KAAK,CAACI,mBAAmB,GAAG,KAAK;MACrC;MACAJ,KAAK,CAACE,QAAQ,GAAG,KAAK;IAC1B,CAAC,CAAC;IACF;IACA,IAAI,CAACJ,IAAI,CAACU,IAAI,CAACG,EAAE,CAACnD,QAAQ,CAACW,UAAU,GAAG,WAAW,EAAE,YAAY;MAC7D,IAAI6B,KAAK,CAACI,mBAAmB,IACzB,CAACJ,KAAK,CAACC,QAAQ,IACfD,KAAK,CAACD,QAAQ,CAACR,sBAAsB,EAAE;QACvCS,KAAK,CAACU,aAAa,CAAC,CAAC;QACrBV,KAAK,CAACI,mBAAmB,GAAG,KAAK;MACrC;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACS,eAAe,CAAC,CAAC;EAC1B,CAAC;EACDjB,QAAQ,CAACxC,SAAS,CAACyD,eAAe,GAAG,YAAY;IAC7C,IAAIb,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAACD,QAAQ,CAACT,WAAW,IAAI,IAAI,CAACY,QAAQ,EAAE;MAC5C,IAAIY,eAAe,GAAG,IAAI,CAAChB,IAAI,CAACQ,KAAK,CAACS,IAAI,CAAC,kBAAkB,CAAC;MAC9D,IAAIC,YAAY,GAAG,IAAI,CAAClB,IAAI,CAACQ,KAAK,CAACS,IAAI,CAAC,cAAc,CAAC;MACvD,IAAI,IAAI,CAACd,QAAQ,EAAE;QACfe,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;QAChCH,eAAe,CAACI,WAAW,CAAC,UAAU,CAAC;QACvCC,UAAU,CAAC,YAAY;UACnBH,YAAY,CAACI,GAAG,CAAC,YAAY,EAAE,QAAQ,IAClCpB,KAAK,CAACF,IAAI,CAACC,QAAQ,CAACsB,KAAK,GACtBrB,KAAK,CAACD,QAAQ,CAACV,iBAAiB,CAAC,GACrC,YAAY,CAAC;UACjByB,eAAe,CAACQ,QAAQ,CAAC,UAAU,CAAC;QACxC,CAAC,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC;EACD;EACA1B,QAAQ,CAACxC,SAAS,CAACiD,QAAQ,GAAG,YAAY;IACtC,IAAIL,KAAK,GAAG,IAAI;IAChB,IAAIuB,KAAK,GAAG,uBAAuB,GAAG,IAAI,CAACxB,QAAQ,CAACL,qBAAqB,CAAC,gBAAgB,CAAC,GAAG,mEAAmE;IACjK;IACA,IAAI,CAACI,IAAI,CAACQ,KAAK,CACVS,IAAI,CAAC,IAAI,CAAChB,QAAQ,CAACN,wBAAwB,CAAC,CAC5Cc,MAAM,CAACgB,KAAK,CAAC;IAClB,IAAI,CAACzB,IAAI,CAACQ,KAAK,CACVS,IAAI,CAAC,qBAAqB,CAAC,CAC3BS,KAAK,CAAC,CAAC,CACPb,EAAE,CAAC,mBAAmB,EAAE,YAAY;MACrC,IAAIX,KAAK,CAACF,IAAI,CAACQ,KAAK,CAACmB,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC/CzB,KAAK,CAACY,YAAY,CAAC,CAAC;MACxB,CAAC,MACI;QACD,IAAI,CAACZ,KAAK,CAACC,QAAQ,EAAE;UACjBD,KAAK,CAACU,aAAa,CAAC,CAAC;QACzB;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EACD;EACAd,QAAQ,CAACxC,SAAS,CAACsD,aAAa,GAAG,YAAY;IAC3C,IAAIV,KAAK,GAAG,IAAI;IAChB,IAAI,CAACF,IAAI,CAACQ,KAAK,CACVS,IAAI,CAAC,cAAc,CAAC,CACpBK,GAAG,CAAC,YAAY,EAAE,QAAQ,IAC1B,IAAI,CAACtB,IAAI,CAACC,QAAQ,CAACsB,KAAK,GACrB,IAAI,CAACtB,QAAQ,CAACV,iBAAiB,CAAC,GACpC,YAAY,CAAC;IACjB,IAAI,CAACS,IAAI,CAACQ,KAAK,CAACgB,QAAQ,CAAC,kBAAkB,CAAC;IAC5C,IAAI,CAACxB,IAAI,CAACQ,KAAK,CAACS,IAAI,CAAC,kBAAkB,CAAC,CAACO,QAAQ,CAAC,UAAU,CAAC;IAC7D,IAAI,CAACxB,IAAI,CAACU,IAAI,CAACkB,OAAO,CAAClE,QAAQ,CAACyB,aAAa,EAAE;MAC3C0C,KAAK,EAAE,IAAI,CAAC7B,IAAI,CAAC6B;IACrB,CAAC,CAAC;IACF,IAAI,CAAC1B,QAAQ,GAAG2B,WAAW,CAAC,YAAY;MACpC,IAAI5B,KAAK,CAACF,IAAI,CAAC6B,KAAK,GAAG,CAAC,GAAG3B,KAAK,CAACF,IAAI,CAAC+B,YAAY,CAAC3E,MAAM,EAAE;QACvD8C,KAAK,CAACF,IAAI,CAAC6B,KAAK,EAAE;MACtB,CAAC,MACI;QACD3B,KAAK,CAACF,IAAI,CAAC6B,KAAK,GAAG,CAAC;MACxB;MACA3B,KAAK,CAACF,IAAI,CAACU,IAAI,CAACkB,OAAO,CAAClE,QAAQ,CAACwB,QAAQ,EAAE;QACvC2C,KAAK,EAAE3B,KAAK,CAACF,IAAI,CAAC6B;MACtB,CAAC,CAAC;MACF3B,KAAK,CAACE,QAAQ,GAAG,IAAI;MACrBF,KAAK,CAACF,IAAI,CAACgC,KAAK,CAAC9B,KAAK,CAACF,IAAI,CAAC6B,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAC5D,CAAC,EAAE,IAAI,CAAC7B,IAAI,CAACC,QAAQ,CAACsB,KAAK,GAAG,IAAI,CAACtB,QAAQ,CAACV,iBAAiB,CAAC;EAClE,CAAC;EACD;EACAO,QAAQ,CAACxC,SAAS,CAACwD,YAAY,GAAG,YAAY;IAC1C,IAAI,IAAI,CAACX,QAAQ,EAAE;MACf,IAAI,CAACH,IAAI,CAACU,IAAI,CAACkB,OAAO,CAAClE,QAAQ,CAAC0B,YAAY,EAAE;QAC1CyC,KAAK,EAAE,IAAI,CAAC7B,IAAI,CAAC6B;MACrB,CAAC,CAAC;MACF,IAAI,CAAC7B,IAAI,CAACQ,KAAK,CAACS,IAAI,CAAC,cAAc,CAAC,CAACE,UAAU,CAAC,OAAO,CAAC;MACxD,IAAI,CAACnB,IAAI,CAACQ,KAAK,CAACY,WAAW,CAAC,kBAAkB,CAAC;MAC/C,IAAI,CAACpB,IAAI,CAACQ,KAAK,CAACS,IAAI,CAAC,kBAAkB,CAAC,CAACG,WAAW,CAAC,UAAU,CAAC;IACpE;IACAa,aAAa,CAAC,IAAI,CAAC9B,QAAQ,CAAC;IAC5B,IAAI,CAACA,QAAQ,GAAG,KAAK;EACzB,CAAC;EACDL,QAAQ,CAACxC,SAAS,CAAC4E,YAAY,GAAG,YAAY;IAC1C,IAAI,CAACpB,YAAY,CAAC,CAAC;EACvB,CAAC;EACDhB,QAAQ,CAACxC,SAAS,CAAC6E,OAAO,GAAG,YAAY;IACrC,IAAI,IAAI,CAAClC,QAAQ,CAACf,QAAQ,EAAE;MACxB,IAAI,CAACc,IAAI,CAACQ,KAAK,CAACS,IAAI,CAAC,kBAAkB,CAAC,CAACmB,MAAM,CAAC,CAAC;IACrD;IACA;IACA,IAAI,CAACpC,IAAI,CAACU,IAAI,CAAC2B,GAAG,CAAC,cAAc,CAAC;IAClC,IAAI,CAACrC,IAAI,CAACU,IAAI,CAAC2B,GAAG,CAAC,WAAW,CAAC;EACnC,CAAC;EACD,OAAOvC,QAAQ;AACnB,CAAC,CAAC,CAAE;AAEJ,eAAeA,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
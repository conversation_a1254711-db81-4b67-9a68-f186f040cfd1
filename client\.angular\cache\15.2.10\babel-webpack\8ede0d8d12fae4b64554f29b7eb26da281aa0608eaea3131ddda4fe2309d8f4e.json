{"ast": null, "code": "import { isArray } from '../util/isArray';\nimport { CombineLatestOperator } from '../observable/combineLatest';\nimport { from } from '../observable/from';\nconst none = {};\nexport function combineLatest(...observables) {\n  let project = null;\n  if (typeof observables[observables.length - 1] === 'function') {\n    project = observables.pop();\n  }\n  if (observables.length === 1 && isArray(observables[0])) {\n    observables = observables[0].slice();\n  }\n  return source => source.lift.call(from([source, ...observables]), new CombineLatestOperator(project));\n}", "map": {"version": 3, "names": ["isArray", "CombineLatestOperator", "from", "none", "combineLatest", "observables", "project", "length", "pop", "slice", "source", "lift", "call"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/combineLatest.js"], "sourcesContent": ["import { isArray } from '../util/isArray';\nimport { CombineLatestOperator } from '../observable/combineLatest';\nimport { from } from '../observable/from';\nconst none = {};\nexport function combineLatest(...observables) {\n    let project = null;\n    if (typeof observables[observables.length - 1] === 'function') {\n        project = observables.pop();\n    }\n    if (observables.length === 1 && isArray(observables[0])) {\n        observables = observables[0].slice();\n    }\n    return (source) => source.lift.call(from([source, ...observables]), new CombineLatestOperator(project));\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,IAAI,QAAQ,oBAAoB;AACzC,MAAMC,IAAI,GAAG,CAAC,CAAC;AACf,OAAO,SAASC,aAAaA,CAAC,GAAGC,WAAW,EAAE;EAC1C,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAI,OAAOD,WAAW,CAACA,WAAW,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;IAC3DD,OAAO,GAAGD,WAAW,CAACG,GAAG,CAAC,CAAC;EAC/B;EACA,IAAIH,WAAW,CAACE,MAAM,KAAK,CAAC,IAAIP,OAAO,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACrDA,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC;EACxC;EACA,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACV,IAAI,CAAC,CAACQ,MAAM,EAAE,GAAGL,WAAW,CAAC,CAAC,EAAE,IAAIJ,qBAAqB,CAACK,OAAO,CAAC,CAAC;AAC3G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
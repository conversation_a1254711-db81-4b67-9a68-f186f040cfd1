{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Component, HostListener, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { NavigationStart, RouterModule } from '@angular/router';\nimport { Subject, merge, fromEvent, interval } from 'rxjs';\nimport { map, filter, first, delay, debounce } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nvar TourState;\n(function (TourState) {\n  TourState[TourState[\"OFF\"] = 0] = \"OFF\";\n  TourState[TourState[\"ON\"] = 1] = \"ON\";\n  TourState[TourState[\"PAUSED\"] = 2] = \"PAUSED\";\n})(TourState || (TourState = {}));\nvar Direction;\n(function (Direction) {\n  Direction[Direction[\"Forwards\"] = 0] = \"Forwards\";\n  Direction[Direction[\"Backwards\"] = 1] = \"Backwards\";\n})(Direction || (Direction = {}));\n// noinspection JSUnusedGlobalSymbols\nclass TourService {\n  constructor(router, rendererFactory) {\n    this.router = router;\n    this.rendererFactory = rendererFactory;\n    this.stepShow$ = new Subject();\n    this.stepHide$ = new Subject();\n    this.initialize$ = new Subject();\n    this.start$ = new Subject();\n    this.end$ = new Subject();\n    this.pause$ = new Subject();\n    this.resume$ = new Subject();\n    this.anchorRegister$ = new Subject();\n    this.anchorUnregister$ = new Subject();\n    this.events$ = merge(this.stepShow$.pipe(map(value => ({\n      name: 'stepShow',\n      value\n    }))), this.stepHide$.pipe(map(value => ({\n      name: 'stepHide',\n      value\n    }))), this.initialize$.pipe(map(value => ({\n      name: 'initialize',\n      value\n    }))), this.start$.pipe(map(value => ({\n      name: 'start',\n      value\n    }))), this.end$.pipe(map(value => ({\n      name: 'end',\n      value\n    }))), this.pause$.pipe(map(value => ({\n      name: 'pause',\n      value\n    }))), this.resume$.pipe(map(value => ({\n      name: 'resume',\n      value\n    }))), this.anchorRegister$.pipe(map(value => ({\n      name: 'anchorRegister',\n      value\n    }))), this.anchorUnregister$.pipe(map(value => ({\n      name: 'anchorUnregister',\n      value\n    }))));\n    this.steps = [];\n    this.anchors = {};\n    this.status = TourState.OFF;\n    this.isHotKeysEnabled = true;\n    this.direction = Direction.Forwards;\n    this.renderer = rendererFactory.createRenderer(null, null);\n  }\n  initialize(steps, stepDefaults) {\n    if (steps && steps.length > 0) {\n      this.status = TourState.OFF;\n      this.steps = steps.map(step => Object.assign({}, stepDefaults, step));\n      this.initialize$.next(this.steps);\n    }\n  }\n  disableHotkeys() {\n    this.isHotKeysEnabled = false;\n  }\n  enableHotkeys() {\n    this.isHotKeysEnabled = true;\n  }\n  start() {\n    this.startAt(0);\n  }\n  startAt(stepId) {\n    this.status = TourState.ON;\n    this.goToStep(this.loadStep(stepId));\n    this.start$.next();\n    this.router.events.pipe(filter(event => event instanceof NavigationStart), first()).subscribe(() => {\n      if (this.currentStep && this.currentStep.hasOwnProperty('route')) {\n        this.hideStep(this.currentStep);\n      }\n    });\n  }\n  end() {\n    this.status = TourState.OFF;\n    this.hideStep(this.currentStep);\n    this.currentStep = undefined;\n    this.removeLastAnchorClickListener();\n    this.end$.next();\n  }\n  pause() {\n    this.status = TourState.PAUSED;\n    this.hideStep(this.currentStep);\n    this.pause$.next();\n  }\n  resume() {\n    this.status = TourState.ON;\n    this.showStep(this.currentStep);\n    this.resume$.next();\n  }\n  toggle(pause) {\n    if (pause) {\n      if (this.currentStep) {\n        this.pause();\n      } else {\n        this.resume();\n      }\n    } else {\n      if (this.currentStep) {\n        this.end();\n      } else {\n        this.start();\n      }\n    }\n  }\n  next() {\n    this.direction = Direction.Forwards;\n    if (this.hasNext(this.currentStep)) {\n      this.goToStep(this.loadStep(this.currentStep.nextStep || this.steps.indexOf(this.currentStep) + 1));\n    }\n  }\n  hasNext(step) {\n    if (!step) {\n      console.warn('Can\\'t get next step. No currentStep.');\n      return false;\n    }\n    return step.nextStep !== undefined || this.steps.indexOf(step) < this.steps.length - 1 && !this.isNextOptionalAnchorMissing(step);\n  }\n  isNextOptionalAnchorMissing(step) {\n    const stepIndex = this.steps.indexOf(step);\n    for (let i = stepIndex + 1; i < this.steps.length; i++) {\n      const nextStep = this.steps[i];\n      if (!nextStep.isOptional || this.anchors[nextStep.anchorId]) return false;\n    }\n    return true;\n  }\n  prev() {\n    this.direction = Direction.Backwards;\n    if (this.hasPrev(this.currentStep)) {\n      this.goToStep(this.loadStep(this.currentStep.prevStep || this.steps.indexOf(this.currentStep) - 1));\n    }\n  }\n  hasPrev(step) {\n    if (!step) {\n      console.warn('Can\\'t get previous step. No currentStep.');\n      return false;\n    }\n    return step.prevStep !== undefined || this.steps.indexOf(step) > 0 && !this.isPrevOptionalAnchorMising(step);\n  }\n  isPrevOptionalAnchorMising(step) {\n    const stepIndex = this.steps.indexOf(step);\n    for (let i = stepIndex - 1; i > -1; i--) {\n      const prevStep = this.steps[i];\n      if (!prevStep.isOptional || this.anchors[prevStep.anchorId]) return false;\n    }\n    return true;\n  }\n  goto(stepId) {\n    this.goToStep(this.loadStep(stepId));\n  }\n  register(anchorId, anchor) {\n    if (!anchorId) {\n      return;\n    }\n    if (this.anchors[anchorId]) {\n      throw new Error('anchorId ' + anchorId + ' already registered!');\n    }\n    this.anchors[anchorId] = anchor;\n    this.anchorRegister$.next(anchorId);\n  }\n  unregister(anchorId) {\n    if (!anchorId) {\n      return;\n    }\n    delete this.anchors[anchorId];\n    this.anchorUnregister$.next(anchorId);\n  }\n  getStatus() {\n    return this.status;\n  }\n  isHotkeysEnabled() {\n    return this.isHotKeysEnabled;\n  }\n  goToStep(step) {\n    if (!step) {\n      console.warn('Can\\'t go to non-existent step');\n      this.end();\n      return;\n    }\n    if (this.currentStep) {\n      this.hideStep(this.currentStep);\n    }\n    this.removeLastAnchorClickListener();\n    this.listenToOnAnchorClick(step);\n    if (step.route !== undefined && step.route !== null) {\n      this.navigateToRouteAndSetStep(step);\n    } else {\n      this.setCurrentStepAsync(step);\n    }\n  }\n  removeLastAnchorClickListener() {\n    if (this.unListenNextOnAnchorClickFn) {\n      this.unListenNextOnAnchorClickFn();\n      this.unListenNextOnAnchorClickFn = undefined;\n    }\n  }\n  listenToOnAnchorClick(step) {\n    if (step.goToNextOnAnchorClick) {\n      const anchor = this.anchors[step.anchorId];\n      this.unListenNextOnAnchorClickFn = this.renderer.listen(anchor.element.nativeElement, 'click', () => this.next());\n    }\n  }\n  navigateToRouteAndSetStep(step) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const url = typeof step.route === 'string' ? step.route : _this.router.createUrlTree(step.route),\n        matchOptions = {\n          paths: 'exact',\n          matrixParams: 'exact',\n          queryParams: 'subset',\n          fragment: 'ignored'\n        };\n      const isActive = _this.router.isActive(url, matchOptions);\n      if (isActive) {\n        _this.setCurrentStepAsync(step);\n        return;\n      }\n      const navigated = yield _this.router.navigateByUrl(url);\n      if (!navigated) {\n        console.warn('Navigation to route failed: ', step.route);\n        _this.end();\n      } else {\n        _this.setCurrentStepAsync(step, step.delayAfterNavigation);\n      }\n    })();\n  }\n  loadStep(stepId) {\n    if (typeof stepId === 'number') {\n      return this.steps[stepId];\n    } else {\n      return this.steps.find(step => step.stepId === stepId);\n    }\n  }\n  setCurrentStep(step) {\n    this.currentStep = step;\n    this.showStep(this.currentStep);\n    this.router.events.pipe(filter(event => event instanceof NavigationStart), first()).subscribe(() => {\n      if (this.currentStep && this.currentStep.hasOwnProperty('route')) {\n        this.hideStep(this.currentStep);\n      }\n    });\n  }\n  setCurrentStepAsync(step, delay = 0) {\n    setTimeout(() => this.setCurrentStep(step), delay);\n  }\n  showStep(step) {\n    const anchor = this.anchors[step && step.anchorId];\n    if (!anchor) {\n      if (step.isAsync) {\n        this.anchorRegister$.pipe(filter(anchorId => anchorId === step.anchorId), first(), delay(0)).subscribe(() => this.showStep(step));\n        return;\n      }\n      if (step.isOptional) {\n        this.direction === Direction.Forwards ? this.next() : this.prev();\n        return;\n      }\n      console.warn('Can\\'t attach to unregistered anchor with id ' + step.anchorId);\n      this.end();\n      return;\n    }\n    anchor.showTourStep(step);\n    this.stepShow$.next(step);\n  }\n  hideStep(step) {\n    const anchor = this.anchors[step && step.anchorId];\n    if (!anchor) {\n      return;\n    }\n    anchor.hideTourStep();\n    this.stepHide$.next(step);\n  }\n}\nTourService.ɵfac = function TourService_Factory(t) {\n  return new (t || TourService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i0.RendererFactory2));\n};\nTourService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TourService,\n  factory: TourService.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.Router\n    }, {\n      type: i0.RendererFactory2\n    }];\n  }, null);\n})();\nclass TourHotkeyListenerComponent {\n  constructor(tourService) {\n    this.tourService = tourService;\n  }\n  /**\r\n   * Configures hot keys for controlling the tour with the keyboard\r\n   */\n  onEscapeKey() {\n    if (this.tourService.getStatus() === TourState.ON && this.tourService.isHotkeysEnabled()) {\n      this.tourService.end();\n    }\n  }\n  onArrowRightKey() {\n    if (this.tourService.getStatus() === TourState.ON && this.tourService.hasNext(this.tourService.currentStep) && this.tourService.isHotkeysEnabled()) {\n      this.tourService.next();\n    }\n  }\n  onArrowLeftKey() {\n    if (this.tourService.getStatus() === TourState.ON && this.tourService.hasPrev(this.tourService.currentStep) && this.tourService.isHotkeysEnabled()) {\n      this.tourService.prev();\n    }\n  }\n}\nTourHotkeyListenerComponent.ɵfac = function TourHotkeyListenerComponent_Factory(t) {\n  return new (t || TourHotkeyListenerComponent)(i0.ɵɵdirectiveInject(TourService));\n};\nTourHotkeyListenerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TourHotkeyListenerComponent,\n  selectors: [[\"tour-hotkey-listener\"]],\n  hostBindings: function TourHotkeyListenerComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown.Escape\", function TourHotkeyListenerComponent_keydown_Escape_HostBindingHandler() {\n        return ctx.onEscapeKey();\n      }, false, i0.ɵɵresolveWindow)(\"keydown.ArrowRight\", function TourHotkeyListenerComponent_keydown_ArrowRight_HostBindingHandler() {\n        return ctx.onArrowRightKey();\n      }, false, i0.ɵɵresolveWindow)(\"keydown.ArrowLeft\", function TourHotkeyListenerComponent_keydown_ArrowLeft_HostBindingHandler() {\n        return ctx.onArrowLeftKey();\n      }, false, i0.ɵɵresolveWindow);\n    }\n  },\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function TourHotkeyListenerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourHotkeyListenerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tour-hotkey-listener',\n      template: `<ng-content></ng-content>`\n    }]\n  }], function () {\n    return [{\n      type: TourService\n    }];\n  }, {\n    onEscapeKey: [{\n      type: HostListener,\n      args: ['window:keydown.Escape']\n    }],\n    onArrowRightKey: [{\n      type: HostListener,\n      args: ['window:keydown.ArrowRight']\n    }],\n    onArrowLeftKey: [{\n      type: HostListener,\n      args: ['window:keydown.ArrowLeft']\n    }]\n  });\n})();\nvar ElementSides;\n(function (ElementSides) {\n  ElementSides[ElementSides[\"Top\"] = 0] = \"Top\";\n  ElementSides[ElementSides[\"Bottom\"] = 1] = \"Bottom\";\n  ElementSides[ElementSides[\"All\"] = 2] = \"All\";\n})(ElementSides || (ElementSides = {}));\nfunction isInViewport(htlmElement, sidesToCheck = ElementSides.All) {\n  const viewportWidth = window.innerWidth,\n    viewportHeight = window.innerHeight,\n    boundingRectangle = htlmElement.getBoundingClientRect(),\n    areCornersInViewport = boundingRectangle.left >= 0 && boundingRectangle.right <= viewportWidth,\n    isTopInViewport = boundingRectangle.top >= 0,\n    isBottomInViewport = boundingRectangle.bottom <= viewportHeight;\n  if (sidesToCheck === ElementSides.Top) {\n    return isTopInViewport && areCornersInViewport;\n  }\n  if (sidesToCheck === ElementSides.Bottom) {\n    return isBottomInViewport && areCornersInViewport;\n  }\n  return isTopInViewport && isBottomInViewport && areCornersInViewport;\n}\nclass ScrollingUtil {\n  static ensureVisible(htmlElement) {\n    if (!isInViewport(htmlElement, ElementSides.Bottom)) {\n      htmlElement.scrollIntoView(false);\n    } else if (!isInViewport(htmlElement, ElementSides.Top)) {\n      htmlElement.scrollIntoView(true);\n    }\n  }\n}\nclass TourBackdropService {\n  constructor(rendererFactory) {\n    this.renderer = rendererFactory.createRenderer(null, null);\n  }\n  show(targetElement, isScrollingEnabled = true) {\n    this.isScrollingEnabled = isScrollingEnabled;\n    this.targetHtmlElement = targetElement.nativeElement;\n    if (!this.backdropElements) {\n      this.backdropElements = this.createBackdropElements();\n      this.subscribeToWindowResizeEvent();\n    }\n    this.setBackdropPosition();\n  }\n  setBackdropPosition() {\n    const elementBoundingRect = this.targetHtmlElement.getBoundingClientRect(),\n      docEl = document.documentElement,\n      scrollHeight = docEl.scrollHeight,\n      scrollWidth = docEl.scrollWidth,\n      scrollX = window.scrollX,\n      scrollY = window.scrollY,\n      leftRect = {\n        width: elementBoundingRect.left + scrollX,\n        height: scrollHeight,\n        top: 0,\n        left: 0\n      },\n      topRect = {\n        width: elementBoundingRect.width,\n        height: elementBoundingRect.top + scrollY,\n        top: 0,\n        left: elementBoundingRect.left + scrollX\n      },\n      bottomRect = {\n        width: elementBoundingRect.width,\n        height: scrollHeight - (elementBoundingRect.bottom + scrollY),\n        top: elementBoundingRect.bottom + scrollY,\n        left: elementBoundingRect.left + scrollX\n      },\n      rightRect = {\n        width: scrollWidth - (elementBoundingRect.right + scrollX),\n        height: scrollHeight,\n        top: 0,\n        left: elementBoundingRect.right + scrollX\n      },\n      rectangles = [leftRect, topRect, bottomRect, rightRect];\n    for (let i = 0; i < rectangles.length; i++) {\n      const styles = this.createBackdropStyles(rectangles[i]);\n      this.applyStyles(styles, this.backdropElements[i]);\n    }\n  }\n  subscribeToWindowResizeEvent() {\n    const resizeObservable$ = fromEvent(window, 'resize');\n    this.windowResizeSubscription$ = resizeObservable$.pipe(debounce(() => interval(10))).subscribe(() => {\n      this.setBackdropPosition();\n      ScrollingUtil.ensureVisible(this.targetHtmlElement);\n    });\n  }\n  close() {\n    if (this.backdropElements) {\n      this.removeBackdropElement();\n      this.windowResizeSubscription$.unsubscribe();\n    }\n  }\n  removeBackdropElement() {\n    this.backdropElements.forEach(backdropElement => this.renderer.removeChild(document.body, backdropElement));\n    this.backdropElements = undefined;\n  }\n  applyStyles(styles, element) {\n    for (const name of Object.keys(styles)) {\n      this.renderer.setStyle(element, name, styles[name]);\n    }\n  }\n  createBackdropStyles(rectangle) {\n    return {\n      position: this.isScrollingEnabled ? 'absolute' : 'fixed',\n      width: `${rectangle.width}px`,\n      height: `${rectangle.height}px`,\n      top: `${rectangle.top}px`,\n      left: `${rectangle.left}px`,\n      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n      zIndex: '101'\n    };\n  }\n  createBackdropElement() {\n    const backdropElement = this.renderer.createElement('div');\n    this.renderer.addClass(backdropElement, 'ngx-ui-tour_backdrop');\n    this.renderer.appendChild(document.body, backdropElement);\n    return backdropElement;\n  }\n  createBackdropElements() {\n    return Array.from({\n      length: 4\n    }).map(() => this.createBackdropElement());\n  }\n}\nTourBackdropService.ɵfac = function TourBackdropService_Factory(t) {\n  return new (t || TourBackdropService)(i0.ɵɵinject(i0.RendererFactory2));\n};\nTourBackdropService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TourBackdropService,\n  factory: TourBackdropService.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourBackdropService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }];\n  }, null);\n})();\nclass TourModule {\n  static forRoot() {\n    return {\n      ngModule: TourModule,\n      providers: [TourService, TourBackdropService]\n    };\n  }\n}\nTourModule.ɵfac = function TourModule_Factory(t) {\n  return new (t || TourModule)();\n};\nTourModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TourModule\n});\nTourModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule, RouterModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TourHotkeyListenerComponent],\n      exports: [TourHotkeyListenerComponent],\n      imports: [CommonModule, RouterModule]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-ui-tour-core\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { ElementSides, ScrollingUtil, TourBackdropService, TourHotkeyListenerComponent, TourModule, TourService, TourState, isInViewport };", "map": {"version": 3, "names": ["CommonModule", "i0", "Injectable", "Component", "HostListener", "NgModule", "i1", "NavigationStart", "RouterModule", "Subject", "merge", "fromEvent", "interval", "map", "filter", "first", "delay", "debounce", "_c0", "TourState", "Direction", "TourService", "constructor", "router", "rendererFactory", "stepShow$", "stepHide$", "initialize$", "start$", "end$", "pause$", "resume$", "anchorRegister$", "anchorUnregister$", "events$", "pipe", "value", "name", "steps", "anchors", "status", "OFF", "isHotKeysEnabled", "direction", "Forwards", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "initialize", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "step", "Object", "assign", "next", "disableHotkeys", "enableHotkeys", "start", "startAt", "stepId", "ON", "goToStep", "loadStep", "events", "event", "subscribe", "currentStep", "hasOwnProperty", "hideStep", "end", "undefined", "removeLastAnchorClickListener", "pause", "PAUSED", "resume", "showStep", "toggle", "hasNext", "nextStep", "indexOf", "console", "warn", "isNextOptionalAnchorMissing", "stepIndex", "i", "isOptional", "anchorId", "prev", "Backwards", "has<PERSON>rev", "prevStep", "isPrevOptionalAnchorMising", "goto", "register", "anchor", "Error", "unregister", "getStatus", "isHotkeysEnabled", "listenToOnAnchorClick", "route", "navigateToRouteAndSetStep", "setCurrentStepAsync", "unListenNextOnAnchorClickFn", "goToNextOnAnchorClick", "listen", "element", "nativeElement", "_this", "_asyncToGenerator", "url", "createUrlTree", "matchOptions", "paths", "matrixParams", "queryParams", "fragment", "isActive", "navigated", "navigateByUrl", "delayAfterNavigation", "find", "setCurrentStep", "setTimeout", "isAsync", "showTourStep", "hideTourStep", "ɵfac", "TourService_Factory", "t", "ɵɵinject", "Router", "RendererFactory2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "TourHotkeyListenerComponent", "tourService", "onEscapeKey", "onArrowRightKey", "onArrowLeftKey", "TourHotkeyListenerComponent_Factory", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostBindings", "TourHotkeyListenerComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "TourHotkeyListenerComponent_keydown_Escape_HostBindingHandler", "ɵɵresolveWindow", "TourHotkeyListenerComponent_keydown_ArrowRight_HostBindingHandler", "TourHotkeyListenerComponent_keydown_ArrowLeft_HostBindingHandler", "ngContentSelectors", "decls", "vars", "template", "TourHotkeyListenerComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "args", "selector", "ElementSides", "isInViewport", "htlmElement", "sidesToCheck", "All", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "boundingRectangle", "getBoundingClientRect", "areCornersInViewport", "left", "right", "isTopInViewport", "top", "isBottomInViewport", "bottom", "Top", "Bottom", "ScrollingUtil", "ensureVisible", "htmlElement", "scrollIntoView", "TourBackdropService", "show", "targetElement", "isScrollingEnabled", "targetHtmlElement", "backdropElements", "createBackdropElements", "subscribeToWindowResizeEvent", "setBackdropPosition", "elementBoundingRect", "docEl", "document", "documentElement", "scrollHeight", "scrollWidth", "scrollX", "scrollY", "leftRect", "width", "height", "topRect", "bottomRect", "rightRect", "rectangles", "styles", "createBackdropStyles", "applyStyles", "resizeObservable$", "windowResizeSubscription$", "close", "removeBackdropElement", "unsubscribe", "for<PERSON>ach", "backdropElement", "<PERSON><PERSON><PERSON><PERSON>", "body", "keys", "setStyle", "rectangle", "position", "backgroundColor", "zIndex", "createBackdropElement", "createElement", "addClass", "append<PERSON><PERSON><PERSON>", "Array", "from", "TourBackdropService_Factory", "TourModule", "forRoot", "ngModule", "providers", "TourModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/ngx-ui-tour-core/fesm2020/ngx-ui-tour-core.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Component, HostListener, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { NavigationStart, RouterModule } from '@angular/router';\nimport { Subject, merge, fromEvent, interval } from 'rxjs';\nimport { map, filter, first, delay, debounce } from 'rxjs/operators';\n\nvar TourState;\r\n(function (TourState) {\r\n    TourState[TourState[\"OFF\"] = 0] = \"OFF\";\r\n    TourState[TourState[\"ON\"] = 1] = \"ON\";\r\n    TourState[TourState[\"PAUSED\"] = 2] = \"PAUSED\";\r\n})(TourState || (TourState = {}));\r\nvar Direction;\r\n(function (Direction) {\r\n    Direction[Direction[\"Forwards\"] = 0] = \"Forwards\";\r\n    Direction[Direction[\"Backwards\"] = 1] = \"Backwards\";\r\n})(Direction || (Direction = {}));\r\n// noinspection JSUnusedGlobalSymbols\r\nclass TourService {\r\n    constructor(router, rendererFactory) {\r\n        this.router = router;\r\n        this.rendererFactory = rendererFactory;\r\n        this.stepShow$ = new Subject();\r\n        this.stepHide$ = new Subject();\r\n        this.initialize$ = new Subject();\r\n        this.start$ = new Subject();\r\n        this.end$ = new Subject();\r\n        this.pause$ = new Subject();\r\n        this.resume$ = new Subject();\r\n        this.anchorRegister$ = new Subject();\r\n        this.anchorUnregister$ = new Subject();\r\n        this.events$ = merge(this.stepShow$.pipe(map(value => ({ name: 'stepShow', value }))), this.stepHide$.pipe(map(value => ({ name: 'stepHide', value }))), this.initialize$.pipe(map(value => ({ name: 'initialize', value }))), this.start$.pipe(map(value => ({ name: 'start', value }))), this.end$.pipe(map(value => ({ name: 'end', value }))), this.pause$.pipe(map(value => ({ name: 'pause', value }))), this.resume$.pipe(map(value => ({ name: 'resume', value }))), this.anchorRegister$.pipe(map(value => ({\r\n            name: 'anchorRegister',\r\n            value\r\n        }))), this.anchorUnregister$.pipe(map(value => ({\r\n            name: 'anchorUnregister',\r\n            value\r\n        }))));\r\n        this.steps = [];\r\n        this.anchors = {};\r\n        this.status = TourState.OFF;\r\n        this.isHotKeysEnabled = true;\r\n        this.direction = Direction.Forwards;\r\n        this.renderer = rendererFactory.createRenderer(null, null);\r\n    }\r\n    initialize(steps, stepDefaults) {\r\n        if (steps && steps.length > 0) {\r\n            this.status = TourState.OFF;\r\n            this.steps = steps.map(step => Object.assign({}, stepDefaults, step));\r\n            this.initialize$.next(this.steps);\r\n        }\r\n    }\r\n    disableHotkeys() {\r\n        this.isHotKeysEnabled = false;\r\n    }\r\n    enableHotkeys() {\r\n        this.isHotKeysEnabled = true;\r\n    }\r\n    start() {\r\n        this.startAt(0);\r\n    }\r\n    startAt(stepId) {\r\n        this.status = TourState.ON;\r\n        this.goToStep(this.loadStep(stepId));\r\n        this.start$.next();\r\n        this.router.events\r\n            .pipe(filter(event => event instanceof NavigationStart), first())\r\n            .subscribe(() => {\r\n            if (this.currentStep && this.currentStep.hasOwnProperty('route')) {\r\n                this.hideStep(this.currentStep);\r\n            }\r\n        });\r\n    }\r\n    end() {\r\n        this.status = TourState.OFF;\r\n        this.hideStep(this.currentStep);\r\n        this.currentStep = undefined;\r\n        this.removeLastAnchorClickListener();\r\n        this.end$.next();\r\n    }\r\n    pause() {\r\n        this.status = TourState.PAUSED;\r\n        this.hideStep(this.currentStep);\r\n        this.pause$.next();\r\n    }\r\n    resume() {\r\n        this.status = TourState.ON;\r\n        this.showStep(this.currentStep);\r\n        this.resume$.next();\r\n    }\r\n    toggle(pause) {\r\n        if (pause) {\r\n            if (this.currentStep) {\r\n                this.pause();\r\n            }\r\n            else {\r\n                this.resume();\r\n            }\r\n        }\r\n        else {\r\n            if (this.currentStep) {\r\n                this.end();\r\n            }\r\n            else {\r\n                this.start();\r\n            }\r\n        }\r\n    }\r\n    next() {\r\n        this.direction = Direction.Forwards;\r\n        if (this.hasNext(this.currentStep)) {\r\n            this.goToStep(this.loadStep(this.currentStep.nextStep || this.steps.indexOf(this.currentStep) + 1));\r\n        }\r\n    }\r\n    hasNext(step) {\r\n        if (!step) {\r\n            console.warn('Can\\'t get next step. No currentStep.');\r\n            return false;\r\n        }\r\n        return (step.nextStep !== undefined ||\r\n            (this.steps.indexOf(step) < this.steps.length - 1 && !this.isNextOptionalAnchorMissing(step)));\r\n    }\r\n    isNextOptionalAnchorMissing(step) {\r\n        const stepIndex = this.steps.indexOf(step);\r\n        for (let i = stepIndex + 1; i < this.steps.length; i++) {\r\n            const nextStep = this.steps[i];\r\n            if (!nextStep.isOptional || this.anchors[nextStep.anchorId])\r\n                return false;\r\n        }\r\n        return true;\r\n    }\r\n    prev() {\r\n        this.direction = Direction.Backwards;\r\n        if (this.hasPrev(this.currentStep)) {\r\n            this.goToStep(this.loadStep(this.currentStep.prevStep || this.steps.indexOf(this.currentStep) - 1));\r\n        }\r\n    }\r\n    hasPrev(step) {\r\n        if (!step) {\r\n            console.warn('Can\\'t get previous step. No currentStep.');\r\n            return false;\r\n        }\r\n        return step.prevStep !== undefined ||\r\n            (this.steps.indexOf(step) > 0 && !this.isPrevOptionalAnchorMising(step));\r\n    }\r\n    isPrevOptionalAnchorMising(step) {\r\n        const stepIndex = this.steps.indexOf(step);\r\n        for (let i = stepIndex - 1; i > -1; i--) {\r\n            const prevStep = this.steps[i];\r\n            if (!prevStep.isOptional || this.anchors[prevStep.anchorId])\r\n                return false;\r\n        }\r\n        return true;\r\n    }\r\n    goto(stepId) {\r\n        this.goToStep(this.loadStep(stepId));\r\n    }\r\n    register(anchorId, anchor) {\r\n        if (!anchorId) {\r\n            return;\r\n        }\r\n        if (this.anchors[anchorId]) {\r\n            throw new Error('anchorId ' + anchorId + ' already registered!');\r\n        }\r\n        this.anchors[anchorId] = anchor;\r\n        this.anchorRegister$.next(anchorId);\r\n    }\r\n    unregister(anchorId) {\r\n        if (!anchorId) {\r\n            return;\r\n        }\r\n        delete this.anchors[anchorId];\r\n        this.anchorUnregister$.next(anchorId);\r\n    }\r\n    getStatus() {\r\n        return this.status;\r\n    }\r\n    isHotkeysEnabled() {\r\n        return this.isHotKeysEnabled;\r\n    }\r\n    goToStep(step) {\r\n        if (!step) {\r\n            console.warn('Can\\'t go to non-existent step');\r\n            this.end();\r\n            return;\r\n        }\r\n        if (this.currentStep) {\r\n            this.hideStep(this.currentStep);\r\n        }\r\n        this.removeLastAnchorClickListener();\r\n        this.listenToOnAnchorClick(step);\r\n        if (step.route !== undefined && step.route !== null) {\r\n            this.navigateToRouteAndSetStep(step);\r\n        }\r\n        else {\r\n            this.setCurrentStepAsync(step);\r\n        }\r\n    }\r\n    removeLastAnchorClickListener() {\r\n        if (this.unListenNextOnAnchorClickFn) {\r\n            this.unListenNextOnAnchorClickFn();\r\n            this.unListenNextOnAnchorClickFn = undefined;\r\n        }\r\n    }\r\n    listenToOnAnchorClick(step) {\r\n        if (step.goToNextOnAnchorClick) {\r\n            const anchor = this.anchors[step.anchorId];\r\n            this.unListenNextOnAnchorClickFn = this.renderer\r\n                .listen(anchor.element.nativeElement, 'click', () => this.next());\r\n        }\r\n    }\r\n    async navigateToRouteAndSetStep(step) {\r\n        const url = typeof step.route === 'string' ? step.route : this.router.createUrlTree(step.route), matchOptions = {\r\n            paths: 'exact',\r\n            matrixParams: 'exact',\r\n            queryParams: 'subset',\r\n            fragment: 'ignored'\r\n        };\r\n        const isActive = this.router.isActive(url, matchOptions);\r\n        if (isActive) {\r\n            this.setCurrentStepAsync(step);\r\n            return;\r\n        }\r\n        const navigated = await this.router.navigateByUrl(url);\r\n        if (!navigated) {\r\n            console.warn('Navigation to route failed: ', step.route);\r\n            this.end();\r\n        }\r\n        else {\r\n            this.setCurrentStepAsync(step, step.delayAfterNavigation);\r\n        }\r\n    }\r\n    loadStep(stepId) {\r\n        if (typeof stepId === 'number') {\r\n            return this.steps[stepId];\r\n        }\r\n        else {\r\n            return this.steps.find(step => step.stepId === stepId);\r\n        }\r\n    }\r\n    setCurrentStep(step) {\r\n        this.currentStep = step;\r\n        this.showStep(this.currentStep);\r\n        this.router.events\r\n            .pipe(filter(event => event instanceof NavigationStart), first())\r\n            .subscribe(() => {\r\n            if (this.currentStep && this.currentStep.hasOwnProperty('route')) {\r\n                this.hideStep(this.currentStep);\r\n            }\r\n        });\r\n    }\r\n    setCurrentStepAsync(step, delay = 0) {\r\n        setTimeout(() => this.setCurrentStep(step), delay);\r\n    }\r\n    showStep(step) {\r\n        const anchor = this.anchors[step && step.anchorId];\r\n        if (!anchor) {\r\n            if (step.isAsync) {\r\n                this.anchorRegister$\r\n                    .pipe(filter(anchorId => anchorId === step.anchorId), first(), delay(0))\r\n                    .subscribe(() => this.showStep(step));\r\n                return;\r\n            }\r\n            if (step.isOptional) {\r\n                this.direction === Direction.Forwards ? this.next() : this.prev();\r\n                return;\r\n            }\r\n            console.warn('Can\\'t attach to unregistered anchor with id ' + step.anchorId);\r\n            this.end();\r\n            return;\r\n        }\r\n        anchor.showTourStep(step);\r\n        this.stepShow$.next(step);\r\n    }\r\n    hideStep(step) {\r\n        const anchor = this.anchors[step && step.anchorId];\r\n        if (!anchor) {\r\n            return;\r\n        }\r\n        anchor.hideTourStep();\r\n        this.stepHide$.next(step);\r\n    }\r\n}\r\nTourService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourService, deps: [{ token: i1.Router }, { token: i0.RendererFactory2 }], target: i0.ɵɵFactoryTarget.Injectable });\r\nTourService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourService });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourService, decorators: [{\r\n            type: Injectable\r\n        }], ctorParameters: function () { return [{ type: i1.Router }, { type: i0.RendererFactory2 }]; } });\n\nclass TourHotkeyListenerComponent {\r\n    constructor(tourService) {\r\n        this.tourService = tourService;\r\n    }\r\n    /**\r\n     * Configures hot keys for controlling the tour with the keyboard\r\n     */\r\n    onEscapeKey() {\r\n        if (this.tourService.getStatus() === TourState.ON &&\r\n            this.tourService.isHotkeysEnabled()) {\r\n            this.tourService.end();\r\n        }\r\n    }\r\n    onArrowRightKey() {\r\n        if (this.tourService.getStatus() === TourState.ON &&\r\n            this.tourService.hasNext(this.tourService.currentStep) &&\r\n            this.tourService.isHotkeysEnabled()) {\r\n            this.tourService.next();\r\n        }\r\n    }\r\n    onArrowLeftKey() {\r\n        if (this.tourService.getStatus() === TourState.ON &&\r\n            this.tourService.hasPrev(this.tourService.currentStep) &&\r\n            this.tourService.isHotkeysEnabled()) {\r\n            this.tourService.prev();\r\n        }\r\n    }\r\n}\r\nTourHotkeyListenerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourHotkeyListenerComponent, deps: [{ token: TourService }], target: i0.ɵɵFactoryTarget.Component });\r\nTourHotkeyListenerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.11\", type: TourHotkeyListenerComponent, selector: \"tour-hotkey-listener\", host: { listeners: { \"window:keydown.Escape\": \"onEscapeKey()\", \"window:keydown.ArrowRight\": \"onArrowRightKey()\", \"window:keydown.ArrowLeft\": \"onArrowLeftKey()\" } }, ngImport: i0, template: `<ng-content></ng-content>`, isInline: true });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourHotkeyListenerComponent, decorators: [{\r\n            type: Component,\r\n            args: [{\r\n                    selector: 'tour-hotkey-listener',\r\n                    template: `<ng-content></ng-content>`\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: TourService }]; }, propDecorators: { onEscapeKey: [{\r\n                type: HostListener,\r\n                args: ['window:keydown.Escape']\r\n            }], onArrowRightKey: [{\r\n                type: HostListener,\r\n                args: ['window:keydown.ArrowRight']\r\n            }], onArrowLeftKey: [{\r\n                type: HostListener,\r\n                args: ['window:keydown.ArrowLeft']\r\n            }] } });\n\nvar ElementSides;\r\n(function (ElementSides) {\r\n    ElementSides[ElementSides[\"Top\"] = 0] = \"Top\";\r\n    ElementSides[ElementSides[\"Bottom\"] = 1] = \"Bottom\";\r\n    ElementSides[ElementSides[\"All\"] = 2] = \"All\";\r\n})(ElementSides || (ElementSides = {}));\r\nfunction isInViewport(htlmElement, sidesToCheck = ElementSides.All) {\r\n    const viewportWidth = window.innerWidth, viewportHeight = window.innerHeight, boundingRectangle = htlmElement.getBoundingClientRect(), areCornersInViewport = boundingRectangle.left >= 0 && boundingRectangle.right <= viewportWidth, isTopInViewport = boundingRectangle.top >= 0, isBottomInViewport = boundingRectangle.bottom <= viewportHeight;\r\n    if (sidesToCheck === ElementSides.Top) {\r\n        return isTopInViewport && areCornersInViewport;\r\n    }\r\n    if (sidesToCheck === ElementSides.Bottom) {\r\n        return isBottomInViewport && areCornersInViewport;\r\n    }\r\n    return isTopInViewport && isBottomInViewport && areCornersInViewport;\r\n}\n\nclass ScrollingUtil {\r\n    static ensureVisible(htmlElement) {\r\n        if (!isInViewport(htmlElement, ElementSides.Bottom)) {\r\n            htmlElement.scrollIntoView(false);\r\n        }\r\n        else if (!isInViewport(htmlElement, ElementSides.Top)) {\r\n            htmlElement.scrollIntoView(true);\r\n        }\r\n    }\r\n}\n\nclass TourBackdropService {\r\n    constructor(rendererFactory) {\r\n        this.renderer = rendererFactory.createRenderer(null, null);\r\n    }\r\n    show(targetElement, isScrollingEnabled = true) {\r\n        this.isScrollingEnabled = isScrollingEnabled;\r\n        this.targetHtmlElement = targetElement.nativeElement;\r\n        if (!this.backdropElements) {\r\n            this.backdropElements = this.createBackdropElements();\r\n            this.subscribeToWindowResizeEvent();\r\n        }\r\n        this.setBackdropPosition();\r\n    }\r\n    setBackdropPosition() {\r\n        const elementBoundingRect = this.targetHtmlElement.getBoundingClientRect(), docEl = document.documentElement, scrollHeight = docEl.scrollHeight, scrollWidth = docEl.scrollWidth, scrollX = window.scrollX, scrollY = window.scrollY, leftRect = {\r\n            width: elementBoundingRect.left + scrollX,\r\n            height: scrollHeight,\r\n            top: 0,\r\n            left: 0\r\n        }, topRect = {\r\n            width: elementBoundingRect.width,\r\n            height: elementBoundingRect.top + scrollY,\r\n            top: 0,\r\n            left: elementBoundingRect.left + scrollX\r\n        }, bottomRect = {\r\n            width: elementBoundingRect.width,\r\n            height: scrollHeight - (elementBoundingRect.bottom + scrollY),\r\n            top: elementBoundingRect.bottom + scrollY,\r\n            left: elementBoundingRect.left + scrollX\r\n        }, rightRect = {\r\n            width: scrollWidth - (elementBoundingRect.right + scrollX),\r\n            height: scrollHeight,\r\n            top: 0,\r\n            left: elementBoundingRect.right + scrollX\r\n        }, rectangles = [leftRect, topRect, bottomRect, rightRect];\r\n        for (let i = 0; i < rectangles.length; i++) {\r\n            const styles = this.createBackdropStyles(rectangles[i]);\r\n            this.applyStyles(styles, this.backdropElements[i]);\r\n        }\r\n    }\r\n    subscribeToWindowResizeEvent() {\r\n        const resizeObservable$ = fromEvent(window, 'resize');\r\n        this.windowResizeSubscription$ = resizeObservable$\r\n            .pipe(debounce(() => interval(10)))\r\n            .subscribe(() => {\r\n            this.setBackdropPosition();\r\n            ScrollingUtil.ensureVisible(this.targetHtmlElement);\r\n        });\r\n    }\r\n    close() {\r\n        if (this.backdropElements) {\r\n            this.removeBackdropElement();\r\n            this.windowResizeSubscription$.unsubscribe();\r\n        }\r\n    }\r\n    removeBackdropElement() {\r\n        this.backdropElements.forEach(backdropElement => this.renderer.removeChild(document.body, backdropElement));\r\n        this.backdropElements = undefined;\r\n    }\r\n    applyStyles(styles, element) {\r\n        for (const name of Object.keys(styles)) {\r\n            this.renderer.setStyle(element, name, styles[name]);\r\n        }\r\n    }\r\n    createBackdropStyles(rectangle) {\r\n        return {\r\n            position: this.isScrollingEnabled ? 'absolute' : 'fixed',\r\n            width: `${rectangle.width}px`,\r\n            height: `${rectangle.height}px`,\r\n            top: `${rectangle.top}px`,\r\n            left: `${rectangle.left}px`,\r\n            backgroundColor: 'rgba(0, 0, 0, 0.7)',\r\n            zIndex: '101'\r\n        };\r\n    }\r\n    createBackdropElement() {\r\n        const backdropElement = this.renderer.createElement('div');\r\n        this.renderer.addClass(backdropElement, 'ngx-ui-tour_backdrop');\r\n        this.renderer.appendChild(document.body, backdropElement);\r\n        return backdropElement;\r\n    }\r\n    createBackdropElements() {\r\n        return Array\r\n            .from({ length: 4 })\r\n            .map(() => this.createBackdropElement());\r\n    }\r\n}\r\nTourBackdropService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourBackdropService, deps: [{ token: i0.RendererFactory2 }], target: i0.ɵɵFactoryTarget.Injectable });\r\nTourBackdropService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourBackdropService });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourBackdropService, decorators: [{\r\n            type: Injectable\r\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }]; } });\n\nclass TourModule {\r\n    static forRoot() {\r\n        return {\r\n            ngModule: TourModule,\r\n            providers: [\r\n                TourService,\r\n                TourBackdropService\r\n            ],\r\n        };\r\n    }\r\n}\r\nTourModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nTourModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourModule, declarations: [TourHotkeyListenerComponent], imports: [CommonModule, RouterModule], exports: [TourHotkeyListenerComponent] });\r\nTourModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourModule, imports: [[CommonModule, RouterModule]] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    declarations: [TourHotkeyListenerComponent],\r\n                    exports: [TourHotkeyListenerComponent],\r\n                    imports: [CommonModule, RouterModule],\r\n                }]\r\n        }] });\n\n/*\r\n * Public API Surface of ngx-ui-tour-core\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { ElementSides, ScrollingUtil, TourBackdropService, TourHotkeyListenerComponent, TourModule, TourService, TourState, isInViewport };\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC7E,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,eAAe,EAAEC,YAAY,QAAQ,iBAAiB;AAC/D,SAASC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;AAC1D,SAASC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAErE,IAAIC,SAAS;AACb,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACA,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACvCA,SAAS,CAACA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACrCA,SAAS,CAACA,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACjD,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAIC,SAAS;AACb,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACA,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACjDA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;AACvD,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC;AACA,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,MAAM,EAAEC,eAAe,EAAE;IACjC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,SAAS,GAAG,IAAIhB,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACiB,SAAS,GAAG,IAAIjB,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACkB,WAAW,GAAG,IAAIlB,OAAO,CAAC,CAAC;IAChC,IAAI,CAACmB,MAAM,GAAG,IAAInB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACoB,IAAI,GAAG,IAAIpB,OAAO,CAAC,CAAC;IACzB,IAAI,CAACqB,MAAM,GAAG,IAAIrB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACsB,OAAO,GAAG,IAAItB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACuB,eAAe,GAAG,IAAIvB,OAAO,CAAC,CAAC;IACpC,IAAI,CAACwB,iBAAiB,GAAG,IAAIxB,OAAO,CAAC,CAAC;IACtC,IAAI,CAACyB,OAAO,GAAGxB,KAAK,CAAC,IAAI,CAACe,SAAS,CAACU,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACV,SAAS,CAACS,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACT,WAAW,CAACQ,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,YAAY;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACR,MAAM,CAACO,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACP,IAAI,CAACM,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACN,MAAM,CAACK,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACL,OAAO,CAACI,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAED;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACJ,eAAe,CAACG,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MACjfC,IAAI,EAAE,gBAAgB;MACtBD;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACH,iBAAiB,CAACE,IAAI,CAACtB,GAAG,CAACuB,KAAK,KAAK;MAC5CC,IAAI,EAAE,kBAAkB;MACxBD;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,MAAM,GAAGrB,SAAS,CAACsB,GAAG;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,SAAS,GAAGvB,SAAS,CAACwB,QAAQ;IACnC,IAAI,CAACC,QAAQ,GAAGrB,eAAe,CAACsB,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC9D;EACAC,UAAUA,CAACT,KAAK,EAAEU,YAAY,EAAE;IAC5B,IAAIV,KAAK,IAAIA,KAAK,CAACW,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACT,MAAM,GAAGrB,SAAS,CAACsB,GAAG;MAC3B,IAAI,CAACH,KAAK,GAAGA,KAAK,CAACzB,GAAG,CAACqC,IAAI,IAAIC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,YAAY,EAAEE,IAAI,CAAC,CAAC;MACrE,IAAI,CAACvB,WAAW,CAAC0B,IAAI,CAAC,IAAI,CAACf,KAAK,CAAC;IACrC;EACJ;EACAgB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACZ,gBAAgB,GAAG,KAAK;EACjC;EACAa,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACb,gBAAgB,GAAG,IAAI;EAChC;EACAc,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EACnB;EACAA,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAAClB,MAAM,GAAGrB,SAAS,CAACwC,EAAE;IAC1B,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACC,QAAQ,CAACH,MAAM,CAAC,CAAC;IACpC,IAAI,CAAC9B,MAAM,CAACyB,IAAI,CAAC,CAAC;IAClB,IAAI,CAAC9B,MAAM,CAACuC,MAAM,CACb3B,IAAI,CAACrB,MAAM,CAACiD,KAAK,IAAIA,KAAK,YAAYxD,eAAe,CAAC,EAAEQ,KAAK,CAAC,CAAC,CAAC,CAChEiD,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE;QAC9D,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACAG,GAAGA,CAAA,EAAG;IACF,IAAI,CAAC5B,MAAM,GAAGrB,SAAS,CAACsB,GAAG;IAC3B,IAAI,CAAC0B,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;IAC/B,IAAI,CAACA,WAAW,GAAGI,SAAS;IAC5B,IAAI,CAACC,6BAA6B,CAAC,CAAC;IACpC,IAAI,CAACzC,IAAI,CAACwB,IAAI,CAAC,CAAC;EACpB;EACAkB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC/B,MAAM,GAAGrB,SAAS,CAACqD,MAAM;IAC9B,IAAI,CAACL,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;IAC/B,IAAI,CAACnC,MAAM,CAACuB,IAAI,CAAC,CAAC;EACtB;EACAoB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACjC,MAAM,GAAGrB,SAAS,CAACwC,EAAE;IAC1B,IAAI,CAACe,QAAQ,CAAC,IAAI,CAACT,WAAW,CAAC;IAC/B,IAAI,CAAClC,OAAO,CAACsB,IAAI,CAAC,CAAC;EACvB;EACAsB,MAAMA,CAACJ,KAAK,EAAE;IACV,IAAIA,KAAK,EAAE;MACP,IAAI,IAAI,CAACN,WAAW,EAAE;QAClB,IAAI,CAACM,KAAK,CAAC,CAAC;MAChB,CAAC,MACI;QACD,IAAI,CAACE,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACR,WAAW,EAAE;QAClB,IAAI,CAACG,GAAG,CAAC,CAAC;MACd,CAAC,MACI;QACD,IAAI,CAACZ,KAAK,CAAC,CAAC;MAChB;IACJ;EACJ;EACAH,IAAIA,CAAA,EAAG;IACH,IAAI,CAACV,SAAS,GAAGvB,SAAS,CAACwB,QAAQ;IACnC,IAAI,IAAI,CAACgC,OAAO,CAAC,IAAI,CAACX,WAAW,CAAC,EAAE;MAChC,IAAI,CAACL,QAAQ,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACI,WAAW,CAACY,QAAQ,IAAI,IAAI,CAACvC,KAAK,CAACwC,OAAO,CAAC,IAAI,CAACb,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IACvG;EACJ;EACAW,OAAOA,CAAC1B,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,EAAE;MACP6B,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;MACrD,OAAO,KAAK;IAChB;IACA,OAAQ9B,IAAI,CAAC2B,QAAQ,KAAKR,SAAS,IAC9B,IAAI,CAAC/B,KAAK,CAACwC,OAAO,CAAC5B,IAAI,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACW,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACgC,2BAA2B,CAAC/B,IAAI,CAAE;EACrG;EACA+B,2BAA2BA,CAAC/B,IAAI,EAAE;IAC9B,MAAMgC,SAAS,GAAG,IAAI,CAAC5C,KAAK,CAACwC,OAAO,CAAC5B,IAAI,CAAC;IAC1C,KAAK,IAAIiC,CAAC,GAAGD,SAAS,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAAC7C,KAAK,CAACW,MAAM,EAAEkC,CAAC,EAAE,EAAE;MACpD,MAAMN,QAAQ,GAAG,IAAI,CAACvC,KAAK,CAAC6C,CAAC,CAAC;MAC9B,IAAI,CAACN,QAAQ,CAACO,UAAU,IAAI,IAAI,CAAC7C,OAAO,CAACsC,QAAQ,CAACQ,QAAQ,CAAC,EACvD,OAAO,KAAK;IACpB;IACA,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC3C,SAAS,GAAGvB,SAAS,CAACmE,SAAS;IACpC,IAAI,IAAI,CAACC,OAAO,CAAC,IAAI,CAACvB,WAAW,CAAC,EAAE;MAChC,IAAI,CAACL,QAAQ,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACI,WAAW,CAACwB,QAAQ,IAAI,IAAI,CAACnD,KAAK,CAACwC,OAAO,CAAC,IAAI,CAACb,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IACvG;EACJ;EACAuB,OAAOA,CAACtC,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,EAAE;MACP6B,OAAO,CAACC,IAAI,CAAC,2CAA2C,CAAC;MACzD,OAAO,KAAK;IAChB;IACA,OAAO9B,IAAI,CAACuC,QAAQ,KAAKpB,SAAS,IAC7B,IAAI,CAAC/B,KAAK,CAACwC,OAAO,CAAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAACwC,0BAA0B,CAACxC,IAAI,CAAE;EAChF;EACAwC,0BAA0BA,CAACxC,IAAI,EAAE;IAC7B,MAAMgC,SAAS,GAAG,IAAI,CAAC5C,KAAK,CAACwC,OAAO,CAAC5B,IAAI,CAAC;IAC1C,KAAK,IAAIiC,CAAC,GAAGD,SAAS,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACrC,MAAMM,QAAQ,GAAG,IAAI,CAACnD,KAAK,CAAC6C,CAAC,CAAC;MAC9B,IAAI,CAACM,QAAQ,CAACL,UAAU,IAAI,IAAI,CAAC7C,OAAO,CAACkD,QAAQ,CAACJ,QAAQ,CAAC,EACvD,OAAO,KAAK;IACpB;IACA,OAAO,IAAI;EACf;EACAM,IAAIA,CAACjC,MAAM,EAAE;IACT,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACC,QAAQ,CAACH,MAAM,CAAC,CAAC;EACxC;EACAkC,QAAQA,CAACP,QAAQ,EAAEQ,MAAM,EAAE;IACvB,IAAI,CAACR,QAAQ,EAAE;MACX;IACJ;IACA,IAAI,IAAI,CAAC9C,OAAO,CAAC8C,QAAQ,CAAC,EAAE;MACxB,MAAM,IAAIS,KAAK,CAAC,WAAW,GAAGT,QAAQ,GAAG,sBAAsB,CAAC;IACpE;IACA,IAAI,CAAC9C,OAAO,CAAC8C,QAAQ,CAAC,GAAGQ,MAAM;IAC/B,IAAI,CAAC7D,eAAe,CAACqB,IAAI,CAACgC,QAAQ,CAAC;EACvC;EACAU,UAAUA,CAACV,QAAQ,EAAE;IACjB,IAAI,CAACA,QAAQ,EAAE;MACX;IACJ;IACA,OAAO,IAAI,CAAC9C,OAAO,CAAC8C,QAAQ,CAAC;IAC7B,IAAI,CAACpD,iBAAiB,CAACoB,IAAI,CAACgC,QAAQ,CAAC;EACzC;EACAW,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxD,MAAM;EACtB;EACAyD,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACvD,gBAAgB;EAChC;EACAkB,QAAQA,CAACV,IAAI,EAAE;IACX,IAAI,CAACA,IAAI,EAAE;MACP6B,OAAO,CAACC,IAAI,CAAC,gCAAgC,CAAC;MAC9C,IAAI,CAACZ,GAAG,CAAC,CAAC;MACV;IACJ;IACA,IAAI,IAAI,CAACH,WAAW,EAAE;MAClB,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;IACnC;IACA,IAAI,CAACK,6BAA6B,CAAC,CAAC;IACpC,IAAI,CAAC4B,qBAAqB,CAAChD,IAAI,CAAC;IAChC,IAAIA,IAAI,CAACiD,KAAK,KAAK9B,SAAS,IAAInB,IAAI,CAACiD,KAAK,KAAK,IAAI,EAAE;MACjD,IAAI,CAACC,yBAAyB,CAAClD,IAAI,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAACmD,mBAAmB,CAACnD,IAAI,CAAC;IAClC;EACJ;EACAoB,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACgC,2BAA2B,EAAE;MAClC,IAAI,CAACA,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACA,2BAA2B,GAAGjC,SAAS;IAChD;EACJ;EACA6B,qBAAqBA,CAAChD,IAAI,EAAE;IACxB,IAAIA,IAAI,CAACqD,qBAAqB,EAAE;MAC5B,MAAMV,MAAM,GAAG,IAAI,CAACtD,OAAO,CAACW,IAAI,CAACmC,QAAQ,CAAC;MAC1C,IAAI,CAACiB,2BAA2B,GAAG,IAAI,CAACzD,QAAQ,CAC3C2D,MAAM,CAACX,MAAM,CAACY,OAAO,CAACC,aAAa,EAAE,OAAO,EAAE,MAAM,IAAI,CAACrD,IAAI,CAAC,CAAC,CAAC;IACzE;EACJ;EACM+C,yBAAyBA,CAAClD,IAAI,EAAE;IAAA,IAAAyD,KAAA;IAAA,OAAAC,iBAAA;MAClC,MAAMC,GAAG,GAAG,OAAO3D,IAAI,CAACiD,KAAK,KAAK,QAAQ,GAAGjD,IAAI,CAACiD,KAAK,GAAGQ,KAAI,CAACpF,MAAM,CAACuF,aAAa,CAAC5D,IAAI,CAACiD,KAAK,CAAC;QAAEY,YAAY,GAAG;UAC5GC,KAAK,EAAE,OAAO;UACdC,YAAY,EAAE,OAAO;UACrBC,WAAW,EAAE,QAAQ;UACrBC,QAAQ,EAAE;QACd,CAAC;MACD,MAAMC,QAAQ,GAAGT,KAAI,CAACpF,MAAM,CAAC6F,QAAQ,CAACP,GAAG,EAAEE,YAAY,CAAC;MACxD,IAAIK,QAAQ,EAAE;QACVT,KAAI,CAACN,mBAAmB,CAACnD,IAAI,CAAC;QAC9B;MACJ;MACA,MAAMmE,SAAS,SAASV,KAAI,CAACpF,MAAM,CAAC+F,aAAa,CAACT,GAAG,CAAC;MACtD,IAAI,CAACQ,SAAS,EAAE;QACZtC,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAE9B,IAAI,CAACiD,KAAK,CAAC;QACxDQ,KAAI,CAACvC,GAAG,CAAC,CAAC;MACd,CAAC,MACI;QACDuC,KAAI,CAACN,mBAAmB,CAACnD,IAAI,EAAEA,IAAI,CAACqE,oBAAoB,CAAC;MAC7D;IAAC;EACL;EACA1D,QAAQA,CAACH,MAAM,EAAE;IACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC5B,OAAO,IAAI,CAACpB,KAAK,CAACoB,MAAM,CAAC;IAC7B,CAAC,MACI;MACD,OAAO,IAAI,CAACpB,KAAK,CAACkF,IAAI,CAACtE,IAAI,IAAIA,IAAI,CAACQ,MAAM,KAAKA,MAAM,CAAC;IAC1D;EACJ;EACA+D,cAAcA,CAACvE,IAAI,EAAE;IACjB,IAAI,CAACe,WAAW,GAAGf,IAAI;IACvB,IAAI,CAACwB,QAAQ,CAAC,IAAI,CAACT,WAAW,CAAC;IAC/B,IAAI,CAAC1C,MAAM,CAACuC,MAAM,CACb3B,IAAI,CAACrB,MAAM,CAACiD,KAAK,IAAIA,KAAK,YAAYxD,eAAe,CAAC,EAAEQ,KAAK,CAAC,CAAC,CAAC,CAChEiD,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE;QAC9D,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACAoC,mBAAmBA,CAACnD,IAAI,EAAElC,KAAK,GAAG,CAAC,EAAE;IACjC0G,UAAU,CAAC,MAAM,IAAI,CAACD,cAAc,CAACvE,IAAI,CAAC,EAAElC,KAAK,CAAC;EACtD;EACA0D,QAAQA,CAACxB,IAAI,EAAE;IACX,MAAM2C,MAAM,GAAG,IAAI,CAACtD,OAAO,CAACW,IAAI,IAAIA,IAAI,CAACmC,QAAQ,CAAC;IAClD,IAAI,CAACQ,MAAM,EAAE;MACT,IAAI3C,IAAI,CAACyE,OAAO,EAAE;QACd,IAAI,CAAC3F,eAAe,CACfG,IAAI,CAACrB,MAAM,CAACuE,QAAQ,IAAIA,QAAQ,KAAKnC,IAAI,CAACmC,QAAQ,CAAC,EAAEtE,KAAK,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CACvEgD,SAAS,CAAC,MAAM,IAAI,CAACU,QAAQ,CAACxB,IAAI,CAAC,CAAC;QACzC;MACJ;MACA,IAAIA,IAAI,CAACkC,UAAU,EAAE;QACjB,IAAI,CAACzC,SAAS,KAAKvB,SAAS,CAACwB,QAAQ,GAAG,IAAI,CAACS,IAAI,CAAC,CAAC,GAAG,IAAI,CAACiC,IAAI,CAAC,CAAC;QACjE;MACJ;MACAP,OAAO,CAACC,IAAI,CAAC,+CAA+C,GAAG9B,IAAI,CAACmC,QAAQ,CAAC;MAC7E,IAAI,CAACjB,GAAG,CAAC,CAAC;MACV;IACJ;IACAyB,MAAM,CAAC+B,YAAY,CAAC1E,IAAI,CAAC;IACzB,IAAI,CAACzB,SAAS,CAAC4B,IAAI,CAACH,IAAI,CAAC;EAC7B;EACAiB,QAAQA,CAACjB,IAAI,EAAE;IACX,MAAM2C,MAAM,GAAG,IAAI,CAACtD,OAAO,CAACW,IAAI,IAAIA,IAAI,CAACmC,QAAQ,CAAC;IAClD,IAAI,CAACQ,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACgC,YAAY,CAAC,CAAC;IACrB,IAAI,CAACnG,SAAS,CAAC2B,IAAI,CAACH,IAAI,CAAC;EAC7B;AACJ;AACA7B,WAAW,CAACyG,IAAI,YAAAC,oBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyF3G,WAAW,EAArBpB,EAAE,CAAAgI,QAAA,CAAqC3H,EAAE,CAAC4H,MAAM,GAAhDjI,EAAE,CAAAgI,QAAA,CAA2DhI,EAAE,CAACkI,gBAAgB;AAAA,CAA6C;AAC5N9G,WAAW,CAAC+G,KAAK,kBAD8EnI,EAAE,CAAAoI,kBAAA;EAAAC,KAAA,EACYjH,WAAW;EAAAkH,OAAA,EAAXlH,WAAW,CAAAyG;AAAA,EAAG;AAC3H;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAF+FvI,EAAE,CAAAwI,iBAAA,CAELpH,WAAW,EAAc,CAAC;IAC1GqH,IAAI,EAAExI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEwI,IAAI,EAAEpI,EAAE,CAAC4H;IAAO,CAAC,EAAE;MAAEQ,IAAI,EAAEzI,EAAE,CAACkI;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;AAExG,MAAMQ,2BAA2B,CAAC;EAC9BrH,WAAWA,CAACsH,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA;AACJ;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACD,WAAW,CAAC5C,SAAS,CAAC,CAAC,KAAK7E,SAAS,CAACwC,EAAE,IAC7C,IAAI,CAACiF,WAAW,CAAC3C,gBAAgB,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC2C,WAAW,CAACxE,GAAG,CAAC,CAAC;IAC1B;EACJ;EACA0E,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACF,WAAW,CAAC5C,SAAS,CAAC,CAAC,KAAK7E,SAAS,CAACwC,EAAE,IAC7C,IAAI,CAACiF,WAAW,CAAChE,OAAO,CAAC,IAAI,CAACgE,WAAW,CAAC3E,WAAW,CAAC,IACtD,IAAI,CAAC2E,WAAW,CAAC3C,gBAAgB,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC2C,WAAW,CAACvF,IAAI,CAAC,CAAC;IAC3B;EACJ;EACA0F,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACH,WAAW,CAAC5C,SAAS,CAAC,CAAC,KAAK7E,SAAS,CAACwC,EAAE,IAC7C,IAAI,CAACiF,WAAW,CAACpD,OAAO,CAAC,IAAI,CAACoD,WAAW,CAAC3E,WAAW,CAAC,IACtD,IAAI,CAAC2E,WAAW,CAAC3C,gBAAgB,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC2C,WAAW,CAACtD,IAAI,CAAC,CAAC;IAC3B;EACJ;AACJ;AACAqD,2BAA2B,CAACb,IAAI,YAAAkB,oCAAAhB,CAAA;EAAA,YAAAA,CAAA,IAAyFW,2BAA2B,EAlCrD1I,EAAE,CAAAgJ,iBAAA,CAkCqE5H,WAAW;AAAA,CAA4C;AAC7NsH,2BAA2B,CAACO,IAAI,kBAnC+DjJ,EAAE,CAAAkJ,iBAAA;EAAAT,IAAA,EAmCYC,2BAA2B;EAAAS,SAAA;EAAAC,YAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnCzCtJ,EAAE,CAAAwJ,UAAA,4BAAAC,8DAAA;QAAA,OAmCYF,GAAA,CAAAX,WAAA,CAAY,CAAC;MAAA,UAnC3B5I,EAAE,CAAA0J,eAAA,iCAAAC,kEAAA;QAAA,OAmCYJ,GAAA,CAAAV,eAAA,CAAgB,CAAC;MAAA,UAnC/B7I,EAAE,CAAA0J,eAAA,gCAAAE,iEAAA;QAAA,OAmCYL,GAAA,CAAAT,cAAA,CAAe,CAAC;MAAA,UAnC9B9I,EAAE,CAAA0J,eAAA;IAAA;EAAA;EAAAG,kBAAA,EAAA5I,GAAA;EAAA6I,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,qCAAAX,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFtJ,EAAE,CAAAkK,eAAA;MAAFlK,EAAE,CAAAmK,YAAA,EAmCiS,CAAC;IAAA;EAAA;EAAAC,aAAA;AAAA,EAAoB;AACvZ;EAAA,QAAA7B,SAAA,oBAAAA,SAAA,KApC+FvI,EAAE,CAAAwI,iBAAA,CAoCLE,2BAA2B,EAAc,CAAC;IAC1HD,IAAI,EAAEvI,SAAS;IACfmK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCN,QAAQ,EAAG;IACf,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvB,IAAI,EAAErH;IAAY,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEwH,WAAW,EAAE,CAAC;MAC7FH,IAAI,EAAEtI,YAAY;MAClBkK,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC,CAAC;IAAExB,eAAe,EAAE,CAAC;MAClBJ,IAAI,EAAEtI,YAAY;MAClBkK,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEvB,cAAc,EAAE,CAAC;MACjBL,IAAI,EAAEtI,YAAY;MAClBkK,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;AAEhB,IAAIE,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAACA,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC7CA,YAAY,CAACA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnDA,YAAY,CAACA,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACjD,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,SAASC,YAAYA,CAACC,WAAW,EAAEC,YAAY,GAAGH,YAAY,CAACI,GAAG,EAAE;EAChE,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;IAAEC,cAAc,GAAGF,MAAM,CAACG,WAAW;IAAEC,iBAAiB,GAAGR,WAAW,CAACS,qBAAqB,CAAC,CAAC;IAAEC,oBAAoB,GAAGF,iBAAiB,CAACG,IAAI,IAAI,CAAC,IAAIH,iBAAiB,CAACI,KAAK,IAAIT,aAAa;IAAEU,eAAe,GAAGL,iBAAiB,CAACM,GAAG,IAAI,CAAC;IAAEC,kBAAkB,GAAGP,iBAAiB,CAACQ,MAAM,IAAIV,cAAc;EACpV,IAAIL,YAAY,KAAKH,YAAY,CAACmB,GAAG,EAAE;IACnC,OAAOJ,eAAe,IAAIH,oBAAoB;EAClD;EACA,IAAIT,YAAY,KAAKH,YAAY,CAACoB,MAAM,EAAE;IACtC,OAAOH,kBAAkB,IAAIL,oBAAoB;EACrD;EACA,OAAOG,eAAe,IAAIE,kBAAkB,IAAIL,oBAAoB;AACxE;AAEA,MAAMS,aAAa,CAAC;EAChB,OAAOC,aAAaA,CAACC,WAAW,EAAE;IAC9B,IAAI,CAACtB,YAAY,CAACsB,WAAW,EAAEvB,YAAY,CAACoB,MAAM,CAAC,EAAE;MACjDG,WAAW,CAACC,cAAc,CAAC,KAAK,CAAC;IACrC,CAAC,MACI,IAAI,CAACvB,YAAY,CAACsB,WAAW,EAAEvB,YAAY,CAACmB,GAAG,CAAC,EAAE;MACnDI,WAAW,CAACC,cAAc,CAAC,IAAI,CAAC;IACpC;EACJ;AACJ;AAEA,MAAMC,mBAAmB,CAAC;EACtB3K,WAAWA,CAACE,eAAe,EAAE;IACzB,IAAI,CAACqB,QAAQ,GAAGrB,eAAe,CAACsB,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC9D;EACAoJ,IAAIA,CAACC,aAAa,EAAEC,kBAAkB,GAAG,IAAI,EAAE;IAC3C,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,iBAAiB,GAAGF,aAAa,CAACzF,aAAa;IACpD,IAAI,CAAC,IAAI,CAAC4F,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;MACrD,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACvC;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAA,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,mBAAmB,GAAG,IAAI,CAACL,iBAAiB,CAAClB,qBAAqB,CAAC,CAAC;MAAEwB,KAAK,GAAGC,QAAQ,CAACC,eAAe;MAAEC,YAAY,GAAGH,KAAK,CAACG,YAAY;MAAEC,WAAW,GAAGJ,KAAK,CAACI,WAAW;MAAEC,OAAO,GAAGlC,MAAM,CAACkC,OAAO;MAAEC,OAAO,GAAGnC,MAAM,CAACmC,OAAO;MAAEC,QAAQ,GAAG;QAC7OC,KAAK,EAAET,mBAAmB,CAACrB,IAAI,GAAG2B,OAAO;QACzCI,MAAM,EAAEN,YAAY;QACpBtB,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE;MACV,CAAC;MAAEgC,OAAO,GAAG;QACTF,KAAK,EAAET,mBAAmB,CAACS,KAAK;QAChCC,MAAM,EAAEV,mBAAmB,CAAClB,GAAG,GAAGyB,OAAO;QACzCzB,GAAG,EAAE,CAAC;QACNH,IAAI,EAAEqB,mBAAmB,CAACrB,IAAI,GAAG2B;MACrC,CAAC;MAAEM,UAAU,GAAG;QACZH,KAAK,EAAET,mBAAmB,CAACS,KAAK;QAChCC,MAAM,EAAEN,YAAY,IAAIJ,mBAAmB,CAAChB,MAAM,GAAGuB,OAAO,CAAC;QAC7DzB,GAAG,EAAEkB,mBAAmB,CAAChB,MAAM,GAAGuB,OAAO;QACzC5B,IAAI,EAAEqB,mBAAmB,CAACrB,IAAI,GAAG2B;MACrC,CAAC;MAAEO,SAAS,GAAG;QACXJ,KAAK,EAAEJ,WAAW,IAAIL,mBAAmB,CAACpB,KAAK,GAAG0B,OAAO,CAAC;QAC1DI,MAAM,EAAEN,YAAY;QACpBtB,GAAG,EAAE,CAAC;QACNH,IAAI,EAAEqB,mBAAmB,CAACpB,KAAK,GAAG0B;MACtC,CAAC;MAAEQ,UAAU,GAAG,CAACN,QAAQ,EAAEG,OAAO,EAAEC,UAAU,EAAEC,SAAS,CAAC;IAC1D,KAAK,IAAIpI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqI,UAAU,CAACvK,MAAM,EAAEkC,CAAC,EAAE,EAAE;MACxC,MAAMsI,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAACF,UAAU,CAACrI,CAAC,CAAC,CAAC;MACvD,IAAI,CAACwI,WAAW,CAACF,MAAM,EAAE,IAAI,CAACnB,gBAAgB,CAACnH,CAAC,CAAC,CAAC;IACtD;EACJ;EACAqH,4BAA4BA,CAAA,EAAG;IAC3B,MAAMoB,iBAAiB,GAAGjN,SAAS,CAACmK,MAAM,EAAE,QAAQ,CAAC;IACrD,IAAI,CAAC+C,yBAAyB,GAAGD,iBAAiB,CAC7CzL,IAAI,CAAClB,QAAQ,CAAC,MAAML,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAClCoD,SAAS,CAAC,MAAM;MACjB,IAAI,CAACyI,mBAAmB,CAAC,CAAC;MAC1BZ,aAAa,CAACC,aAAa,CAAC,IAAI,CAACO,iBAAiB,CAAC;IACvD,CAAC,CAAC;EACN;EACAyB,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACxB,gBAAgB,EAAE;MACvB,IAAI,CAACyB,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACF,yBAAyB,CAACG,WAAW,CAAC,CAAC;IAChD;EACJ;EACAD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACzB,gBAAgB,CAAC2B,OAAO,CAACC,eAAe,IAAI,IAAI,CAACrL,QAAQ,CAACsL,WAAW,CAACvB,QAAQ,CAACwB,IAAI,EAAEF,eAAe,CAAC,CAAC;IAC3G,IAAI,CAAC5B,gBAAgB,GAAGjI,SAAS;EACrC;EACAsJ,WAAWA,CAACF,MAAM,EAAEhH,OAAO,EAAE;IACzB,KAAK,MAAMpE,IAAI,IAAIc,MAAM,CAACkL,IAAI,CAACZ,MAAM,CAAC,EAAE;MACpC,IAAI,CAAC5K,QAAQ,CAACyL,QAAQ,CAAC7H,OAAO,EAAEpE,IAAI,EAAEoL,MAAM,CAACpL,IAAI,CAAC,CAAC;IACvD;EACJ;EACAqL,oBAAoBA,CAACa,SAAS,EAAE;IAC5B,OAAO;MACHC,QAAQ,EAAE,IAAI,CAACpC,kBAAkB,GAAG,UAAU,GAAG,OAAO;MACxDe,KAAK,EAAG,GAAEoB,SAAS,CAACpB,KAAM,IAAG;MAC7BC,MAAM,EAAG,GAAEmB,SAAS,CAACnB,MAAO,IAAG;MAC/B5B,GAAG,EAAG,GAAE+C,SAAS,CAAC/C,GAAI,IAAG;MACzBH,IAAI,EAAG,GAAEkD,SAAS,CAAClD,IAAK,IAAG;MAC3BoD,eAAe,EAAE,oBAAoB;MACrCC,MAAM,EAAE;IACZ,CAAC;EACL;EACAC,qBAAqBA,CAAA,EAAG;IACpB,MAAMT,eAAe,GAAG,IAAI,CAACrL,QAAQ,CAAC+L,aAAa,CAAC,KAAK,CAAC;IAC1D,IAAI,CAAC/L,QAAQ,CAACgM,QAAQ,CAACX,eAAe,EAAE,sBAAsB,CAAC;IAC/D,IAAI,CAACrL,QAAQ,CAACiM,WAAW,CAAClC,QAAQ,CAACwB,IAAI,EAAEF,eAAe,CAAC;IACzD,OAAOA,eAAe;EAC1B;EACA3B,sBAAsBA,CAAA,EAAG;IACrB,OAAOwC,KAAK,CACPC,IAAI,CAAC;MAAE/L,MAAM,EAAE;IAAE,CAAC,CAAC,CACnBpC,GAAG,CAAC,MAAM,IAAI,CAAC8N,qBAAqB,CAAC,CAAC,CAAC;EAChD;AACJ;AACA1C,mBAAmB,CAACnE,IAAI,YAAAmH,4BAAAjH,CAAA;EAAA,YAAAA,CAAA,IAAyFiE,mBAAmB,EAxKrChM,EAAE,CAAAgI,QAAA,CAwKqDhI,EAAE,CAACkI,gBAAgB;AAAA,CAA6C;AACtN8D,mBAAmB,CAAC7D,KAAK,kBAzKsEnI,EAAE,CAAAoI,kBAAA;EAAAC,KAAA,EAyKoB2D,mBAAmB;EAAA1D,OAAA,EAAnB0D,mBAAmB,CAAAnE;AAAA,EAAG;AAC3I;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KA1K+FvI,EAAE,CAAAwI,iBAAA,CA0KLwD,mBAAmB,EAAc,CAAC;IAClHvD,IAAI,EAAExI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEwI,IAAI,EAAEzI,EAAE,CAACkI;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;AAEnF,MAAM+G,UAAU,CAAC;EACb,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,UAAU;MACpBG,SAAS,EAAE,CACPhO,WAAW,EACX4K,mBAAmB;IAE3B,CAAC;EACL;AACJ;AACAiD,UAAU,CAACpH,IAAI,YAAAwH,mBAAAtH,CAAA;EAAA,YAAAA,CAAA,IAAyFkH,UAAU;AAAA,CAAkD;AACpKA,UAAU,CAACK,IAAI,kBA1LgFtP,EAAE,CAAAuP,gBAAA;EAAA9G,IAAA,EA0LQwG;AAAU,EAA+H;AAClPA,UAAU,CAACO,IAAI,kBA3LgFxP,EAAE,CAAAyP,gBAAA;EAAAC,OAAA,GA2L8B,CAAC3P,YAAY,EAAEQ,YAAY,CAAC;AAAA,EAAI;AAC/J;EAAA,QAAAgI,SAAA,oBAAAA,SAAA,KA5L+FvI,EAAE,CAAAwI,iBAAA,CA4LLyG,UAAU,EAAc,CAAC;IACzGxG,IAAI,EAAErI,QAAQ;IACdiK,IAAI,EAAE,CAAC;MACCsF,YAAY,EAAE,CAACjH,2BAA2B,CAAC;MAC3CkH,OAAO,EAAE,CAAClH,2BAA2B,CAAC;MACtCgH,OAAO,EAAE,CAAC3P,YAAY,EAAEQ,YAAY;IACxC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASgK,YAAY,EAAEqB,aAAa,EAAEI,mBAAmB,EAAEtD,2BAA2B,EAAEuG,UAAU,EAAE7N,WAAW,EAAEF,SAAS,EAAEsJ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
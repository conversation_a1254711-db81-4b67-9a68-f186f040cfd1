{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Capacitor } from '@capacitor/core';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport { StatusBar } from '@capacitor/status-bar';\nimport { NavigationBar } from '@hugotomazi/capacitor-navigation-bar';\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/tournament.service\";\nimport * as i2 from \"@core/services/config.service\";\nimport * as i3 from \"app/components/overlays/overlays.service\";\nimport * as i4 from \"app/services/livekit.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"@angular/platform-browser\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"app/services/commons.service\";\nimport * as i10 from \"app/services/settings.service\";\nexport class SetupStreamComponent {\n  onMessage(event) {\n    const receivedData = event.data;\n    // console.log('receivedData', receivedData);\n    if (receivedData.hasOwnProperty('is_back') && receivedData.is_back === true) {\n      this.removeIframe();\n    }\n  }\n  constructor(_tournamentService, _coreConfigService, overlaysService, livekitService, router, route, _loadingService, title, location, commonService, settingService) {\n    var _this = this;\n    this._tournamentService = _tournamentService;\n    this._coreConfigService = _coreConfigService;\n    this.overlaysService = overlaysService;\n    this.livekitService = livekitService;\n    this.router = router;\n    this.route = route;\n    this._loadingService = _loadingService;\n    this.title = title;\n    this.location = location;\n    this.commonService = commonService;\n    this.settingService = settingService;\n    this.iframeUrl = `${environment.ezstreamUrl}/setup-iframe`;\n    this.broadcast_data = {};\n    this.hideStatusBar = /*#__PURE__*/_asyncToGenerator(function* () {\n      yield StatusBar.hide();\n      yield NavigationBar.hide();\n    });\n    title.setTitle('Setup Stream');\n    this._coreConfigService.setConfig({\n      layout: {\n        navbar: {\n          hidden: true\n        },\n        menu: {\n          hidden: true\n        },\n        footer: {\n          hidden: true\n        },\n        enableLocalStorage: false\n      }\n    });\n    if (Capacitor.isNativePlatform()) {\n      ScreenOrientation.lock({\n        orientation: 'landscape'\n      });\n      this.hideStatusBar();\n      setInterval(() => {\n        StatusBar.getInfo().then( /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (info) {\n            if (info.visible) {\n              yield _this.hideStatusBar();\n            }\n          });\n          return function (_x) {\n            return _ref2.apply(this, arguments);\n          };\n        }()).catch(err => {\n          console.log('error', err);\n        });\n      }, 4000);\n    }\n  }\n  ngOnInit() {\n    this.getMetadata();\n  }\n  getMetadata() {\n    this.settingService.getMetadaSettings().subscribe(res => {\n      this.metadata = res;\n      this.initIframeViewer();\n    });\n  }\n  initIframeViewer() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('iframeUrl', _this2.iframeUrl);\n      _this2._loadingService.show();\n      let iframe = document.createElement('iframe');\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'logoSponsorUrl', `${environment.apiUrl}/logosponsor-webhook`);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'updateMetadataURL', `${environment.apiUrl}/metadata-webhook`);\n      _this2.iframeUrl = _this2.addParamToUrl(_this2.iframeUrl, 'metadata', JSON.stringify(_this2.metadata));\n      iframe.src = _this2.iframeUrl;\n      iframe.classList.add('iframe-fullscreen');\n      iframe.setAttribute('allow', 'autoplay; fullscreen; picture-in-picture;camera *;microphone *');\n      iframe.style.backgroundColor = 'transparent';\n      iframe.style.zIndex = '9999';\n      iframe.style.width = '100%';\n      iframe.style.height = '100%';\n      iframe.style.border = 'none';\n      iframe.style.margin = '0';\n      iframe.style.padding = '0';\n      iframe.style.overflow = 'hidden';\n      iframe.style.position = 'fixed';\n      iframe.style.top = '0';\n      iframe.style.left = '0';\n      iframe.style.backgroundColor = 'white';\n      iframe.setAttribute('crossorigin', 'anonymous');\n      // append iframe to html\n      _this2.iframe = iframe;\n      iframe.onload = () => {\n        setTimeout(() => {\n          _this2._loadingService.dismiss();\n        }, 2000);\n      };\n      iframe.onerror = () => {\n        _this2._loadingService.dismiss();\n        Swal.fire({\n          icon: 'error',\n          title: 'Error',\n          text: 'Error loading the broadcast page. Please try again'\n        }).then(() => {\n          _this2.removeIframe();\n        });\n      };\n      document.body.parentElement.appendChild(_this2.iframe);\n    })();\n  }\n  addParamToUrl(url, key, value) {\n    let urlObj = new URL(url);\n    urlObj.searchParams.append(key, value);\n    return urlObj.toString();\n  }\n  removeIframe() {\n    if (this.iframe) {\n      this.iframe.remove();\n    }\n    this.router.navigate(['/streaming']);\n  }\n  ngAfterViewInit() {\n    console.log('ngAfterViewInit');\n  }\n  getMatchById(id) {\n    this._loadingService.show();\n    this._tournamentService.getMatchById(id).subscribe(res => {\n      this.match_info = res;\n      this.initIframeViewer();\n      // this.getMatchDetails(id);\n    });\n  }\n\n  ngOnDestroy() {\n    this.removeIframe();\n  }\n  static #_ = this.ɵfac = function SetupStreamComponent_Factory(t) {\n    return new (t || SetupStreamComponent)(i0.ɵɵdirectiveInject(i1.TournamentService), i0.ɵɵdirectiveInject(i2.CoreConfigService), i0.ɵɵdirectiveInject(i3.OverlaysService), i0.ɵɵdirectiveInject(i4.LivekitService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i8.Location), i0.ɵɵdirectiveInject(i9.CommonsService), i0.ɵɵdirectiveInject(i10.SettingsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SetupStreamComponent,\n    selectors: [[\"setup-stream\"]],\n    hostBindings: function SetupStreamComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"message\", function SetupStreamComponent_message_HostBindingHandler($event) {\n          return ctx.onMessage($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 0,\n    vars: 0,\n    template: function SetupStreamComponent_Template(rf, ctx) {},\n    styles: [\".iframe-fullscreen {\\n  width: 100%;\\n  height: 100vh;\\n  border: none;\\n  margin: 0;\\n  padding: 0;\\n  overflow: hidden;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  z-index: 9999;\\n  background-color: white;\\n  display: block;\\n  transition: all 0.3s ease;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3RyZWFtaW5nL3NldHVwLXN0cmVhbS9zZXR1cC1zdHJlYW0uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxXQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7RUFDQSxTQUFBO0VBQ0EsVUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuaWZyYW1lLWZ1bGxzY3JlZW4ge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBoZWlnaHQ6IDEwMHZoO1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gICAgcGFkZGluZzogMDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgei1pbmRleDogOTk5OTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAiBA,SAASA,SAAS,QAAQ,iBAAiB;AAI3C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,aAAa,QAAQ,sCAAsC;AAEpE,SAASC,iBAAiB,QAAQ,+BAA+B;;;;;;;;;;;;AAQjE,OAAM,MAAOC,oBAAoB;EAE7BC,SAASA,CAACC,KAAmB;IAC3B,MAAMC,YAAY,GAAGD,KAAK,CAACE,IAAI;IAC/B;IACA,IACED,YAAY,CAACE,cAAc,CAAC,SAAS,CAAC,IACtCF,YAAY,CAACG,OAAO,KAAK,IAAI,EAC7B;MACA,IAAI,CAACC,YAAY,EAAE;;EAEvB;EAQFC,YACSC,kBAAqC,EACrCC,kBAAqC,EACrCC,eAAgC,EAChCC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,eAA+B,EAC/BC,KAAY,EACZC,QAAkB,EAClBC,aAA6B,EAC7BC,cAA+B;IAAA,IAAAC,KAAA;IAV/B,KAAAX,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAjBvB,KAAAE,SAAS,GAAG,GAAG1B,WAAW,CAAC2B,WAAW,eAAe;IAGrD,KAAAC,cAAc,GAAQ,EAAE;IAoDxB,KAAAC,aAAa,gBAAAC,iBAAA,CAAG,aAAW;MACzB,MAAM5B,SAAS,CAAC6B,IAAI,EAAE;MACtB,MAAM5B,aAAa,CAAC4B,IAAI,EAAE;IAC5B,CAAC;IAvCCV,KAAK,CAACW,QAAQ,CAAC,cAAc,CAAC;IAC9B,IAAI,CAACjB,kBAAkB,CAACkB,SAAS,CAAC;MAChCC,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,MAAM,EAAE;SACT;QACDC,IAAI,EAAE;UACJD,MAAM,EAAE;SACT;QACDE,MAAM,EAAE;UACNF,MAAM,EAAE;SACT;QACDG,kBAAkB,EAAE;;KAEvB,CAAC;IACF,IAAIxC,SAAS,CAACyC,gBAAgB,EAAE,EAAE;MAChCpC,iBAAiB,CAACqC,IAAI,CAAC;QAAEC,WAAW,EAAE;MAAW,CAAE,CAAC;MACpD,IAAI,CAACb,aAAa,EAAE;MACpBc,WAAW,CAAC,MAAK;QACfzC,SAAS,CAAC0C,OAAO,EAAE,CAChBC,IAAI;UAAA,IAAAC,KAAA,GAAAhB,iBAAA,CAAC,WAAOiB,IAAI,EAAI;YACnB,IAAIA,IAAI,CAACC,OAAO,EAAE;cAChB,MAAMvB,KAAI,CAACI,aAAa,EAAE;;UAE9B,CAAC;UAAA,iBAAAoB,EAAA;YAAA,OAAAH,KAAA,CAAAI,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC,CACDC,KAAK,CAAEC,GAAG,IAAI;UACbC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,GAAG,CAAC;QAC3B,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAAC;;EAGZ;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAMAA,WAAWA,CAAA;IACT,IAAI,CAACjC,cAAc,CAACkC,iBAAiB,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;MAC7D,IAAI,CAACC,QAAQ,GAAGD,GAAG;MACnB,IAAI,CAACE,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACJ;EACMA,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA;MACpBwB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,MAAI,CAACrC,SAAS,CAAC;MACxCqC,MAAI,CAAC3C,eAAe,CAAC4C,IAAI,EAAE;MAC3B,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC7CJ,MAAI,CAACrC,SAAS,GAAGqC,MAAI,CAACK,aAAa,CACjCL,MAAI,CAACrC,SAAS,EACd,gBAAgB,EAChB,GAAG1B,WAAW,CAACqE,MAAM,sBAAsB,CAC5C;MACDN,MAAI,CAACrC,SAAS,GAAGqC,MAAI,CAACK,aAAa,CACjCL,MAAI,CAACrC,SAAS,EACd,mBAAmB,EACnB,GAAG1B,WAAW,CAACqE,MAAM,mBAAmB,CACzC;MACDN,MAAI,CAACrC,SAAS,GAAGqC,MAAI,CAACK,aAAa,CACjCL,MAAI,CAACrC,SAAS,EACd,UAAU,EACV4C,IAAI,CAACC,SAAS,CAACR,MAAI,CAACF,QAAQ,CAAC,CAC9B;MACDI,MAAM,CAACO,GAAG,GAAGT,MAAI,CAACrC,SAAS;MAC3BuC,MAAM,CAACQ,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;MACzCT,MAAM,CAACU,YAAY,CACjB,OAAO,EACP,gEAAgE,CACjE;MACDV,MAAM,CAACW,KAAK,CAACC,eAAe,GAAG,aAAa;MAC5CZ,MAAM,CAACW,KAAK,CAACE,MAAM,GAAE,MAAM;MAC3Bb,MAAM,CAACW,KAAK,CAACG,KAAK,GAAG,MAAM;MAC3Bd,MAAM,CAACW,KAAK,CAACI,MAAM,GAAG,MAAM;MAC5Bf,MAAM,CAACW,KAAK,CAACK,MAAM,GAAG,MAAM;MAC5BhB,MAAM,CAACW,KAAK,CAACM,MAAM,GAAG,GAAG;MACzBjB,MAAM,CAACW,KAAK,CAACO,OAAO,GAAG,GAAG;MAC1BlB,MAAM,CAACW,KAAK,CAACQ,QAAQ,GAAG,QAAQ;MAChCnB,MAAM,CAACW,KAAK,CAACS,QAAQ,GAAG,OAAO;MAC/BpB,MAAM,CAACW,KAAK,CAACU,GAAG,GAAG,GAAG;MACtBrB,MAAM,CAACW,KAAK,CAACW,IAAI,GAAG,GAAG;MACvBtB,MAAM,CAACW,KAAK,CAACC,eAAe,GAAG,OAAO;MACtCZ,MAAM,CAACU,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC;MAC/C;MACAZ,MAAI,CAACE,MAAM,GAAGA,MAAM;MACpBA,MAAM,CAACuB,MAAM,GAAG,MAAK;QACnBC,UAAU,CAAC,MAAK;UACd1B,MAAI,CAAC3C,eAAe,CAACsE,OAAO,EAAE;QAChC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDzB,MAAM,CAAC0B,OAAO,GAAG,MAAK;QACpB5B,MAAI,CAAC3C,eAAe,CAACsE,OAAO,EAAE;QAC9BzF,IAAI,CAAC2F,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACbxE,KAAK,EAAE,OAAO;UACdyE,IAAI,EAAE;SACP,CAAC,CAACjD,IAAI,CAAC,MAAK;UACXkB,MAAI,CAACnD,YAAY,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC;MAEDsD,QAAQ,CAAC6B,IAAI,CAACC,aAAa,CAACC,WAAW,CAAClC,MAAI,CAACE,MAAM,CAAC;IAAC;EACvD;EAEAG,aAAaA,CAAC8B,GAAG,EAAEC,GAAG,EAAEC,KAAK;IAC3B,IAAIC,MAAM,GAAG,IAAIC,GAAG,CAACJ,GAAG,CAAC;IACzBG,MAAM,CAACE,YAAY,CAACC,MAAM,CAACL,GAAG,EAAEC,KAAK,CAAC;IACtC,OAAOC,MAAM,CAACI,QAAQ,EAAE;EAC1B;EAEA7F,YAAYA,CAAA;IACV,IAAI,IAAI,CAACqD,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACyC,MAAM,EAAE;;IAEtB,IAAI,CAACxF,MAAM,CAACyF,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAC,eAAeA,CAAA;IACbtD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC;EAEAsD,YAAYA,CAACC,EAAE;IACb,IAAI,CAAC1F,eAAe,CAAC4C,IAAI,EAAE;IAC3B,IAAI,CAAClD,kBAAkB,CAAC+F,YAAY,CAACC,EAAE,CAAC,CAACnD,SAAS,CAAEC,GAAG,IAAI;MACzD,IAAI,CAACmD,UAAU,GAAGnD,GAAG;MACrB,IAAI,CAACE,gBAAgB,EAAE;MACvB;IACF,CAAC,CAAC;EACJ;;EAEAkD,WAAWA,CAAA;IACT,IAAI,CAACpG,YAAY,EAAE;EACrB;EAAC,QAAAqG,CAAA;qBAvKU5G,oBAAoB,EAAA6G,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,MAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAE,cAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAd,EAAA,CAAAC,iBAAA,CAAAc,EAAA,CAAAC,KAAA,GAAAhB,EAAA,CAAAC,iBAAA,CAAAgB,EAAA,CAAAC,QAAA,GAAAlB,EAAA,CAAAC,iBAAA,CAAAkB,EAAA,CAAAC,cAAA,GAAApB,EAAA,CAAAC,iBAAA,CAAAoB,GAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA;UAApBpI,oBAAoB;IAAAqI,SAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAApBC,GAAA,CAAAxI,SAAA,CAAAyI,MAAA,CAAiB;QAAA,UAAA7B,EAAA,CAAA8B,eAAA", "names": ["Capacitor", "environment", "<PERSON><PERSON>", "StatusBar", "NavigationBar", "ScreenOrientation", "SetupStreamComponent", "onMessage", "event", "receivedData", "data", "hasOwnProperty", "is_back", "removeIframe", "constructor", "_tournamentService", "_coreConfigService", "overlaysService", "livekitService", "router", "route", "_loadingService", "title", "location", "commonService", "settingService", "_this", "iframeUrl", "ezstreamUrl", "broadcast_data", "hideStatusBar", "_asyncToGenerator", "hide", "setTitle", "setConfig", "layout", "navbar", "hidden", "menu", "footer", "enableLocalStorage", "isNativePlatform", "lock", "orientation", "setInterval", "getInfo", "then", "_ref2", "info", "visible", "_x", "apply", "arguments", "catch", "err", "console", "log", "ngOnInit", "getMetadata", "getMetadaSettings", "subscribe", "res", "metadata", "initIframeViewer", "_this2", "show", "iframe", "document", "createElement", "addParamToUrl", "apiUrl", "JSON", "stringify", "src", "classList", "add", "setAttribute", "style", "backgroundColor", "zIndex", "width", "height", "border", "margin", "padding", "overflow", "position", "top", "left", "onload", "setTimeout", "dismiss", "onerror", "fire", "icon", "text", "body", "parentElement", "append<PERSON><PERSON><PERSON>", "url", "key", "value", "url<PERSON>bj", "URL", "searchParams", "append", "toString", "remove", "navigate", "ngAfterViewInit", "getMatchById", "id", "match_info", "ngOnDestroy", "_", "i0", "ɵɵdirectiveInject", "i1", "TournamentService", "i2", "CoreConfigService", "i3", "OverlaysService", "i4", "LivekitService", "i5", "Router", "ActivatedRoute", "i6", "LoadingService", "i7", "Title", "i8", "Location", "i9", "CommonsService", "i10", "SettingsService", "_2", "selectors", "hostBindings", "SetupStreamComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\setup-stream\\setup-stream.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  ElementRef,\r\n  HostListener,\r\n  Input,\r\n  NgZone,\r\n  OnInit,\r\n  ViewChild,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { MuxService } from 'app/services/mux.service';\r\nimport { Location } from '@angular/common';\r\nimport { OverlaysService } from 'app/components/overlays/overlays.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { LivekitService } from 'app/services/livekit.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { environment } from 'environments/environment';\r\nimport Swal from 'sweetalert2';\r\nimport { StatusBar } from '@capacitor/status-bar';\r\nimport { NavigationBar } from '@hugotomazi/capacitor-navigation-bar';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\r\n\r\n@Component({\r\n  selector: 'setup-stream',\r\n  templateUrl: './setup-stream.component.html',\r\n  styleUrls: ['./setup-stream.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class SetupStreamComponent {\r\n  @HostListener('window:message', ['$event'])\r\n    onMessage(event: MessageEvent): void {\r\n      const receivedData = event.data;\r\n      // console.log('receivedData', receivedData);\r\n      if (\r\n        receivedData.hasOwnProperty('is_back') &&\r\n        receivedData.is_back === true\r\n      ) {\r\n        this.removeIframe();\r\n      }\r\n    }\r\n  iframe: HTMLIFrameElement;\r\n  iframeUrl = `${environment.ezstreamUrl}/setup-iframe`;\r\n  matchId: any;\r\n  match_info: any;\r\n  broadcast_data: any = {};\r\n  metadata: any;\r\n\r\n  constructor(\r\n    public _tournamentService: TournamentService,\r\n    public _coreConfigService: CoreConfigService,\r\n    public overlaysService: OverlaysService,\r\n    public livekitService: LivekitService,\r\n    public router: Router,\r\n    public route: ActivatedRoute,\r\n    public _loadingService: LoadingService,\r\n    public title: Title,\r\n    public location: Location,\r\n    public commonService: CommonsService,\r\n    public settingService: SettingsService\r\n  ) {\r\n    title.setTitle('Setup Stream');\r\n    this._coreConfigService.setConfig({\r\n      layout: {\r\n        navbar: {\r\n          hidden: true,\r\n        },\r\n        menu: {\r\n          hidden: true,\r\n        },\r\n        footer: {\r\n          hidden: true,\r\n        },\r\n        enableLocalStorage: false,\r\n      },\r\n    });\r\n    if (Capacitor.isNativePlatform()) {\r\n      ScreenOrientation.lock({ orientation: 'landscape' });\r\n      this.hideStatusBar();\r\n      setInterval(() => {\r\n        StatusBar.getInfo()\r\n          .then(async (info) => {\r\n            if (info.visible) {\r\n              await this.hideStatusBar();\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log('error', err);\r\n          });\r\n      }, 4000);\r\n    }\r\n    \r\n  }\r\n\r\n  ngOnInit() {\r\n    this.getMetadata();\r\n  }\r\n  hideStatusBar = async () => {\r\n    await StatusBar.hide();\r\n    await NavigationBar.hide();\r\n  };\r\n  \r\n  getMetadata() {\r\n    this.settingService.getMetadaSettings().subscribe((res: any) => {\r\n      this.metadata = res;\r\n      this.initIframeViewer();\r\n    })\r\n  }\r\n  async initIframeViewer() {\r\n    console.log('iframeUrl', this.iframeUrl);\r\n    this._loadingService.show();\r\n    let iframe = document.createElement('iframe');\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'logoSponsorUrl',\r\n      `${environment.apiUrl}/logosponsor-webhook`\r\n    );\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'updateMetadataURL',\r\n      `${environment.apiUrl}/metadata-webhook`\r\n    );\r\n    this.iframeUrl = this.addParamToUrl(\r\n      this.iframeUrl,\r\n      'metadata',\r\n      JSON.stringify(this.metadata)\r\n    )\r\n    iframe.src = this.iframeUrl;\r\n    iframe.classList.add('iframe-fullscreen');\r\n    iframe.setAttribute(\r\n      'allow',\r\n      'autoplay; fullscreen; picture-in-picture;camera *;microphone *'\r\n    );\r\n    iframe.style.backgroundColor = 'transparent';\r\n    iframe.style.zIndex= '9999';\r\n    iframe.style.width = '100%';\r\n    iframe.style.height = '100%';\r\n    iframe.style.border = 'none';\r\n    iframe.style.margin = '0';\r\n    iframe.style.padding = '0';\r\n    iframe.style.overflow = 'hidden';\r\n    iframe.style.position = 'fixed';\r\n    iframe.style.top = '0';\r\n    iframe.style.left = '0';\r\n    iframe.style.backgroundColor = 'white';\r\n    iframe.setAttribute('crossorigin', 'anonymous');\r\n    // append iframe to html\r\n    this.iframe = iframe;\r\n    iframe.onload = () => {\r\n      setTimeout(() => {\r\n        this._loadingService.dismiss();\r\n      }, 2000);\r\n    };\r\n\r\n    iframe.onerror = () => {\r\n      this._loadingService.dismiss();\r\n      Swal.fire({\r\n        icon: 'error',\r\n        title: 'Error',\r\n        text: 'Error loading the broadcast page. Please try again',\r\n      }).then(() => {\r\n        this.removeIframe();\r\n      });\r\n    };\r\n\r\n    document.body.parentElement.appendChild(this.iframe);\r\n  }\r\n\r\n  addParamToUrl(url, key, value) {\r\n    let urlObj = new URL(url);\r\n    urlObj.searchParams.append(key, value);\r\n    return urlObj.toString();\r\n  }\r\n\r\n  removeIframe() {\r\n    if (this.iframe) {\r\n      this.iframe.remove();\r\n    }\r\n    this.router.navigate(['/streaming']);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('ngAfterViewInit');\r\n  }\r\n\r\n  getMatchById(id) {\r\n    this._loadingService.show();\r\n    this._tournamentService.getMatchById(id).subscribe((res) => {\r\n      this.match_info = res;\r\n      this.initIframeViewer();\r\n      // this.getMatchDetails(id);\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.removeIframe();\r\n  }\r\n  \r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
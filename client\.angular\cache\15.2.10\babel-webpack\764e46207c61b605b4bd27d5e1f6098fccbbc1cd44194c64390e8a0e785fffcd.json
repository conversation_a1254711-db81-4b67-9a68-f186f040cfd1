{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"@angular/platform-browser\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i8 from \"angular-datatables\";\nimport * as i9 from \"../../components/editor-sidebar/editor-sidebar.component\";\nexport class ReleasesComponent {\n  constructor(_http, _coreSidebarService, _translateService, _commonsService, _titleService, render, router) {\n    this._http = _http;\n    this._coreSidebarService = _coreSidebarService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._titleService = _titleService;\n    this.render = render;\n    this.router = router;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.table_name = 'releases-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: 'Create new Release',\n        edit: 'Edit Release',\n        remove: 'Delete Release'\n      },\n      url: `${environment.apiUrl}/releases/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.model = {};\n    this.fields = [{\n      type: 'input',\n      key: 'name',\n      props: {\n        translate: true,\n        label: 'Name',\n        required: true\n      },\n      expressions: {\n        hide: 'model.show_download_link'\n      }\n    }, {\n      type: 'input',\n      key: 'version',\n      props: {\n        translate: true,\n        label: 'Version',\n        required: true,\n        pattern: /[\\S]/\n      },\n      expressions: {\n        hide: 'model.show_download_link'\n      }\n    }, {\n      type: 'input',\n      key: 'towards_version',\n      props: {\n        translate: true,\n        label: 'Towards Version',\n        required: true,\n        pattern: /[\\S]/\n      },\n      expressions: {\n        hide: 'model.show_download_link'\n      }\n    }, {\n      type: 'radio',\n      key: 'type',\n      props: {\n        translate: true,\n        label: 'Type',\n        required: true,\n        options: [{\n          label: 'Stable',\n          value: 'stable'\n        }, {\n          label: 'Beta',\n          value: 'beta'\n        }, {\n          label: 'Alpha',\n          value: 'alpha'\n        }]\n      },\n      expressions: {\n        hide: 'model.show_download_link'\n      },\n      defaultValue: 'alpha'\n    }, {\n      type: 'file',\n      key: 'download_link',\n      props: {\n        translate: true,\n        label: 'Download Link',\n        required: true,\n        upload_url: `${environment.apiUrl}/releases/editor`,\n        accept: '.zip',\n        multiple: false,\n        custom_params: {\n          release_id: 'model.id'\n        }\n      },\n      expressions: {\n        hide: '!model.show_download_link'\n      }\n    }, {\n      type: 'input',\n      key: 'changelog',\n      props: {\n        translate: true,\n        label: 'Change log'\n      },\n      expressions: {\n        hide: 'model.show_download_link'\n      }\n    }, {\n      type: 'textarea',\n      key: 'description',\n      props: {\n        translate: true,\n        label: 'Description'\n      },\n      expressions: {\n        hide: 'model.show_download_link'\n      }\n    }, {\n      type: 'input',\n      key: 'id',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      type: 'input',\n      key: 'show_download_link',\n      props: {\n        type: 'hidden'\n      },\n      defaultValue: false\n    }];\n    this._titleService.setTitle('Releases');\n  }\n  ngOnInit() {\n    this.buildTable();\n  }\n  editor(action, row_id, show_download_link) {\n    this.params.action = action;\n    this.params.row_id = row_id ? row_id : null;\n    this.model.show_download_link = show_download_link ? show_download_link : false;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  buildTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        this._http.post(`${environment.apiUrl}/releases/all`, dataTablesParameters).subscribe(resp => {\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [\n        // { targets: 0, responsivePriority: 1 },\n        // { targets: -1, responsivePriority: 2 },\n        // { targets: -2, responsivePriority: 3 },\n      ],\n      columns: [{\n        title: this._translateService.instant('Name'),\n        data: 'name',\n        className: 'font-weight-bolder'\n      }, {\n        title: this._translateService.instant('Version'),\n        data: 'version'\n      }, {\n        title: this._translateService.instant('Towards Version'),\n        data: 'towards_version'\n      }, {\n        title: this._translateService.instant('Type'),\n        data: 'type'\n      }, {\n        title: this._translateService.instant('Download Link'),\n        data: 'download_link',\n        render: (data, type, row) => {\n          if (!data) {\n            return `<button realease_id=\"${row.id}\" class=\"btn btn-sm btn-danger\">${this._translateService.instant('Upload')} <i class=\"fa-regular fa-upload\"></i> </button>`;\n          } else {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Change log'),\n        data: 'changelog'\n      }, {\n        title: this._translateService.instant('Description'),\n        data: 'description'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"feather icon-plus\"></i> ' + this._translateService.instant('Add'),\n          action: () => this.editor('create')\n        }]\n      }\n    };\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.render.listen(document, 'click', event => {\n      if (event.target.hasAttribute('realease_id')) {\n        let row_id = event.target.getAttribute('realease_id');\n        this.editor('edit', row_id, true);\n      }\n      this.fields;\n    });\n    setTimeout(() => {\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  ngOnDestroy() {\n    this.unlistener();\n    this.dtTrigger.unsubscribe();\n  }\n  static #_ = this.ɵfac = function ReleasesComponent_Factory(t) {\n    return new (t || ReleasesComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CoreSidebarService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.Title), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i6.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ReleasesComponent,\n    selectors: [[\"app-releases\"]],\n    viewQuery: function ReleasesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 4,\n    vars: 7,\n    consts: [[1, \"card\", \"pt-1\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"new_model\"]],\n    template: function ReleasesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"table\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"core-sidebar\", 2);\n        i0.ɵɵelement(3, \"app-editor-sidebar\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params)(\"new_model\", ctx.model);\n      }\n    },\n    dependencies: [i7.CoreSidebarComponent, i8.DataTableDirective, i9.EditorSidebarComponent],\n    styles: [\".upload-btn-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  display: inline-block;\\n}\\n\\n.upload-btn-wrapper[_ngcontent-%COMP%]   input[type=file][_ngcontent-%COMP%] {\\n  font-size: 100px;\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  opacity: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVsZWFzZXMvcmVsZWFzZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxPQUFBO0VBQ0EsTUFBQTtFQUNBLFVBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi51cGxvYWQtYnRuLXdyYXBwZXIge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxufVxyXG5cclxuLnVwbG9hZC1idG4td3JhcHBlciBpbnB1dFt0eXBlPWZpbGVdIHtcclxuICAgIGZvbnQtc2l6ZTogMTAwcHg7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgdG9wOiAwO1xyXG4gICAgb3BhY2l0eTogMDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAQA,SAASA,kBAAkB,QAAQ,oBAAoB;AAIvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;;;;;;;;;;;AAO9B,OAAM,MAAOC,iBAAiB;EAC5BC,YACUC,KAAiB,EAClBC,mBAAuC,EACvCC,iBAAmC,EACnCC,eAA+B,EAC/BC,aAAoB,EACpBC,MAAiB,EACjBC,MAAc;IANb,KAAAN,KAAK,GAALA,KAAK;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IAKf,KAAAC,SAAS,GAAQZ,kBAAkB;IACnC,KAAAa,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAIZ,OAAO,EAAe;IACrD,KAAAa,UAAU,GAAG,gBAAgB;IAC7B,KAAAC,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACF,UAAU;MAC1BG,KAAK,EAAE;QACLC,MAAM,EAAE,oBAAoB;QAC5BC,IAAI,EAAE,cAAc;QACpBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAGrB,WAAW,CAACsB,MAAM,kBAAkB;MAC5CC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IACD,KAAAC,KAAK,GAAQ,EAAE;IACR,KAAAC,MAAM,GAAwB,CACnC;MACEC,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;OACX;MACDC,WAAW,EAAE;QACXC,IAAI,EAAE;;KAET,EACD;MACEP,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,IAAI;QACdG,OAAO,EAAE;OACV;MACDF,WAAW,EAAE;QACXC,IAAI,EAAE;;KAET,EACD;MACEP,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,iBAAiB;QACxBC,QAAQ,EAAE,IAAI;QACdG,OAAO,EAAE;OACV;MACDF,WAAW,EAAE;QACXC,IAAI,EAAE;;KAET,EACD;MACEP,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdI,OAAO,EAAE,CACP;UAAEL,KAAK,EAAE,QAAQ;UAAEM,KAAK,EAAE;QAAQ,CAAE,EACpC;UAAEN,KAAK,EAAE,MAAM;UAAEM,KAAK,EAAE;QAAM,CAAE,EAChC;UAAEN,KAAK,EAAE,OAAO;UAAEM,KAAK,EAAE;QAAO,CAAE;OAErC;MACDJ,WAAW,EAAE;QACXC,IAAI,EAAE;OACP;MACDI,YAAY,EAAE;KACf,EACD;MACEX,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE,IAAI;QACdO,UAAU,EAAE,GAAGvC,WAAW,CAACsB,MAAM,kBAAkB;QACnDkB,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE;UACbC,UAAU,EAAE;;OAEf;MACDV,WAAW,EAAE;QACXC,IAAI,EAAE;;KAET,EAED;MACEP,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;OACR;MACDE,WAAW,EAAE;QACXC,IAAI,EAAE;;KAET,EACD;MACEP,IAAI,EAAE,UAAU;MAChBC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;QACLC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;OACR;MACDE,WAAW,EAAE;QACXC,IAAI,EAAE;;KAET,EACD;MACEP,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE;QACLF,IAAI,EAAE;;KAET,EACD;MACEA,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;QACLF,IAAI,EAAE;OACP;MACDW,YAAY,EAAE;KACf,CACF;IApIC,IAAI,CAAC9B,aAAa,CAACoC,QAAQ,CAAC,UAAU,CAAC;EACzC;EAoIAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,MAAMA,CAACvB,MAAM,EAAEwB,MAAO,EAAEC,kBAAmB;IACzC,IAAI,CAAClC,MAAM,CAACS,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACT,MAAM,CAACiC,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAG,IAAI;IAC3C,IAAI,CAACvB,KAAK,CAACwB,kBAAkB,GAAGA,kBAAkB,GAC9CA,kBAAkB,GAClB,KAAK;IACT,IAAI,CAAC5C,mBAAmB,CAAC6C,kBAAkB,CAAC,IAAI,CAACpC,UAAU,CAAC,CAACqC,UAAU,EAAE;EAC3E;EAEAL,UAAUA,CAAA;IACR,IAAI,CAAClC,SAAS,GAAG;MACfwC,GAAG,EAAE,IAAI,CAAC7C,eAAe,CAAC8C,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAACtD,KAAK,CACPuD,IAAI,CAAM,GAAG3D,WAAW,CAACsB,MAAM,eAAe,EAAEmC,oBAAoB,CAAC,CACrEG,SAAS,CAAEC,IAAS,IAAI;UACvBH,QAAQ,CAAC;YACPI,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAAC5D,eAAe,CAAC8C,iBAAiB,CAACe,IAAI;MACrDC,UAAU,EAAE;QACV;QACA;QACA;MAAA,CACD;MACDC,OAAO,EAAE,CACP;QACErD,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,MAAM,CAAC;QAC7CP,IAAI,EAAE,MAAM;QACZQ,SAAS,EAAE;OACZ,EACD;QACEvD,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,SAAS,CAAC;QAChDP,IAAI,EAAE;OACP,EACD;QACE/C,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,iBAAiB,CAAC;QACxDP,IAAI,EAAE;OACP,EACD;QACE/C,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,MAAM,CAAC;QAC7CP,IAAI,EAAE;OACP,EACD;QACE/C,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,eAAe,CAAC;QACtDP,IAAI,EAAE,eAAe;QACrBvD,MAAM,EAAEA,CAACuD,IAAI,EAAErC,IAAI,EAAE8C,GAAG,KAAI;UAC1B,IAAI,CAACT,IAAI,EAAE;YACT,OAAO,wBACLS,GAAG,CAACC,EACN,mCAAmC,IAAI,CAACpE,iBAAiB,CAACiE,OAAO,CAC/D,QAAQ,CACT,iDAAiD;WACnD,MAAM;YACL,OAAOP,IAAI;;QAEf;OACD,EACD;QACE/C,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,YAAY,CAAC;QACnDP,IAAI,EAAE;OACP,EACD;QACE/C,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACiE,OAAO,CAAC,aAAa,CAAC;QACpDP,IAAI,EAAE;OACP,CACF;MACDW,OAAO,EAAE;QACPvB,GAAG,EAAE,IAAI,CAAC7C,eAAe,CAAC8C,iBAAiB,CAACsB,OAAO,CAACvB,GAAG;QACvDuB,OAAO,EAAE,CACP;UACEC,IAAI,EACF,oCAAoC,GACpC,IAAI,CAACtE,iBAAiB,CAACiE,OAAO,CAAC,KAAK,CAAC;UACvC/C,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACuB,MAAM,CAAC,QAAQ;SACnC;;KAGN;EACH;EAGA8B,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAACrE,MAAM,CAACsE,MAAM,CAACC,QAAQ,EAAE,OAAO,EAAGC,KAAU,IAAI;MACrE,IAAIA,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,aAAa,CAAC,EAAE;QAC5C,IAAInC,MAAM,GAAGiC,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,aAAa,CAAC;QACrD,IAAI,CAACrC,MAAM,CAAC,MAAM,EAAEC,MAAM,EAAE,IAAI,CAAC;;MAGnC,IAAI,CAACtB,MAAM;IACb,CAAC,CAAC;IAEF2D,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACxE,SAAS,CAACyE,IAAI,CAAC,IAAI,CAAC1E,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA2E,WAAWA,CAAA;IACT,IAAI,CAACT,UAAU,EAAE;IACjB,IAAI,CAACjE,SAAS,CAAC2E,WAAW,EAAE;EAC9B;EAAC,QAAAC,CAAA;qBAjQUvF,iBAAiB,EAAAwF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,KAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAY,SAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;UAAjBvG,iBAAiB;IAAAwG,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAYjB9G,kBAAkB;;;;;;;;;;;;QChC/B2F,EAAA,CAAAqB,cAAA,aAAuB;QACnBrB,EAAA,CAAAsB,SAAA,eACQ;QACZtB,EAAA,CAAAuB,YAAA,EAAM;QACNvB,EAAA,CAAAqB,cAAA,sBAAqH;QACjHrB,EAAA,CAAAsB,SAAA,4BACqB;QACzBtB,EAAA,CAAAuB,YAAA,EAAe;;;QANMvB,EAAA,CAAAwB,SAAA,GAAuB;QAAvBxB,EAAA,CAAAyB,UAAA,cAAAL,GAAA,CAAAlG,SAAA,CAAuB,cAAAkG,GAAA,CAAAjG,SAAA;QAGuB6E,EAAA,CAAAwB,SAAA,GAAmB;QAAnBxB,EAAA,CAAAyB,UAAA,SAAAL,GAAA,CAAAhG,UAAA,CAAmB;QAC9D4E,EAAA,CAAAwB,SAAA,GAAmB;QAAnBxB,EAAA,CAAAyB,UAAA,UAAAL,GAAA,CAAAnG,SAAA,CAAmB,WAAAmG,GAAA,CAAApF,MAAA,YAAAoF,GAAA,CAAA/F,MAAA,eAAA+F,GAAA,CAAArF,KAAA", "names": ["DataTableDirective", "environment", "Subject", "ReleasesComponent", "constructor", "_http", "_coreSidebarService", "_translateService", "_commonsService", "_titleService", "render", "router", "dtElement", "dtOptions", "dtTrigger", "table_name", "params", "editor_id", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "model", "fields", "type", "key", "props", "translate", "label", "required", "expressions", "hide", "pattern", "options", "value", "defaultValue", "upload_url", "accept", "multiple", "custom_params", "release_id", "setTitle", "ngOnInit", "buildTable", "editor", "row_id", "show_download_link", "getSidebarRegistry", "toggle<PERSON><PERSON>", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "columnDefs", "columns", "instant", "className", "row", "id", "buttons", "text", "ngAfterViewInit", "unlistener", "listen", "document", "event", "target", "hasAttribute", "getAttribute", "setTimeout", "next", "ngOnDestroy", "unsubscribe", "_", "i0", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CoreSidebarService", "i3", "TranslateService", "i4", "CommonsService", "i5", "Title", "Renderer2", "i6", "Router", "_2", "selectors", "viewQuery", "ReleasesComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\releases\\releases.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\releases\\releases.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit, Renderer2, ViewChild } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { Router } from '@angular/router';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-releases',\r\n  templateUrl: './releases.component.html',\r\n  styleUrls: ['./releases.component.scss'],\r\n})\r\nexport class ReleasesComponent implements OnInit {\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _titleService: Title,\r\n    public render: Renderer2,\r\n    public router: Router\r\n  ) {\r\n    this._titleService.setTitle('Releases');\r\n  }\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  public table_name = 'releases-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: 'Create new Release',\r\n      edit: 'Edit Release',\r\n      remove: 'Delete Release',\r\n    },\r\n    url: `${environment.apiUrl}/releases/editor`,\r\n    method: 'POST',\r\n    action: 'create',\r\n  };\r\n  model: any = {};\r\n  public fields: FormlyFieldConfig[] = [\r\n    {\r\n      type: 'input',\r\n      key: 'name',\r\n      props: {\r\n        translate: true,\r\n        label: 'Name',\r\n        required: true,\r\n      },\r\n      expressions: {\r\n        hide: 'model.show_download_link',\r\n      },\r\n    },\r\n    {\r\n      type: 'input',\r\n      key: 'version',\r\n      props: {\r\n        translate: true,\r\n        label: 'Version',\r\n        required: true,\r\n        pattern: /[\\S]/,\r\n      },\r\n      expressions: {\r\n        hide: 'model.show_download_link',\r\n      },\r\n    },\r\n    {\r\n      type: 'input',\r\n      key: 'towards_version',\r\n      props: {\r\n        translate: true,\r\n        label: 'Towards Version',\r\n        required: true,\r\n        pattern: /[\\S]/,\r\n      },\r\n      expressions: {\r\n        hide: 'model.show_download_link',\r\n      },\r\n    },\r\n    {\r\n      type: 'radio',\r\n      key: 'type',\r\n      props: {\r\n        translate: true,\r\n        label: 'Type',\r\n        required: true,\r\n        options: [\r\n          { label: 'Stable', value: 'stable' },\r\n          { label: 'Beta', value: 'beta' },\r\n          { label: 'Alpha', value: 'alpha' },\r\n        ],\r\n      },\r\n      expressions: {\r\n        hide: 'model.show_download_link',\r\n      },\r\n      defaultValue: 'alpha',\r\n    },\r\n    {\r\n      type: 'file',\r\n      key: 'download_link',\r\n      props: {\r\n        translate: true,\r\n        label: 'Download Link',\r\n        required: true,\r\n        upload_url: `${environment.apiUrl}/releases/editor`,\r\n        accept: '.zip',\r\n        multiple: false,\r\n        custom_params: {\r\n          release_id: 'model.id',\r\n        },\r\n      },\r\n      expressions: {\r\n        hide: '!model.show_download_link',\r\n      },\r\n    },\r\n\r\n    {\r\n      type: 'input',\r\n      key: 'changelog',\r\n      props: {\r\n        translate: true,\r\n        label: 'Change log',\r\n      },\r\n      expressions: {\r\n        hide: 'model.show_download_link',\r\n      },\r\n    },\r\n    {\r\n      type: 'textarea',\r\n      key: 'description',\r\n      props: {\r\n        translate: true,\r\n        label: 'Description',\r\n      },\r\n      expressions: {\r\n        hide: 'model.show_download_link',\r\n      },\r\n    },\r\n    {\r\n      type: 'input',\r\n      key: 'id',\r\n      props: {\r\n        type: 'hidden',\r\n      },\r\n    },\r\n    {\r\n      type: 'input',\r\n      key: 'show_download_link',\r\n      props: {\r\n        type: 'hidden',\r\n      },\r\n      defaultValue: false,\r\n    },\r\n  ];\r\n  ngOnInit(): void {\r\n    this.buildTable();\r\n  }\r\n\r\n  editor(action, row_id?, show_download_link?) {\r\n    this.params.action = action;\r\n    this.params.row_id = row_id ? row_id : null;\r\n    this.model.show_download_link = show_download_link\r\n      ? show_download_link\r\n      : false;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  buildTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .post<any>(`${environment.apiUrl}/releases/all`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [\r\n        // { targets: 0, responsivePriority: 1 },\r\n        // { targets: -1, responsivePriority: 2 },\r\n        // { targets: -2, responsivePriority: 3 },\r\n      ],\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('Name'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Version'),\r\n          data: 'version',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Towards Version'),\r\n          data: 'towards_version',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Type'),\r\n          data: 'type',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Download Link'),\r\n          data: 'download_link',\r\n          render: (data, type, row) => {\r\n            if (!data) {\r\n              return `<button realease_id=\"${\r\n                row.id\r\n              }\" class=\"btn btn-sm btn-danger\">${this._translateService.instant(\r\n                'Upload'\r\n              )} <i class=\"fa-regular fa-upload\"></i> </button>`;\r\n            } else {\r\n              return data;\r\n            }\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Change log'),\r\n          data: 'changelog',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Description'),\r\n          data: 'description',\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-plus\"></i> ' +\r\n              this._translateService.instant('Add'),\r\n            action: () => this.editor('create'),\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  private unlistener: () => void;\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.render.listen(document, 'click', (event: any) => {\r\n      if (event.target.hasAttribute('realease_id')) {\r\n        let row_id = event.target.getAttribute('realease_id');\r\n        this.editor('edit', row_id, true);\r\n      }\r\n\r\n      this.fields;\r\n    });\r\n\r\n    setTimeout(() => {\r\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\r\n      this.dtTrigger.next(this.dtOptions);\r\n    }, 500);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unlistener();\r\n    this.dtTrigger.unsubscribe();\r\n  }\r\n}\r\n", "<div class=\"card pt-1\">\r\n    <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table row-border hover\">\r\n    </table>\r\n</div>\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n    <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\" [new_model]=\"model\">\r\n    </app-editor-sidebar>\r\n</core-sidebar>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
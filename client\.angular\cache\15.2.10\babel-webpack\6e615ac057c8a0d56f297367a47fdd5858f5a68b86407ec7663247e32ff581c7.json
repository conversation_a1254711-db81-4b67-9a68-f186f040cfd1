{"ast": null, "code": "import baseIsNative from './_baseIsNative.js';\nimport getValue from './_getValue.js';\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\nexport default getNative;", "map": {"version": 3, "names": ["baseIsNative", "getValue", "getNative", "object", "key", "value", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_getNative.js"], "sourcesContent": ["import baseIsNative from './_baseIsNative.js';\nimport getValue from './_getValue.js';\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nexport default getNative;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC9B,IAAIC,KAAK,GAAGJ,QAAQ,CAACE,MAAM,EAAEC,GAAG,CAAC;EACjC,OAAOJ,YAAY,CAACK,KAAK,CAAC,GAAGA,KAAK,GAAGC,SAAS;AAChD;AAEA,eAAeJ,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
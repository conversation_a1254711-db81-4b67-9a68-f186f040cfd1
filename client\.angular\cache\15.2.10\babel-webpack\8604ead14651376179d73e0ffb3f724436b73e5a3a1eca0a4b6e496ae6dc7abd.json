{"ast": null, "code": "import { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AutoScheduleService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getScheduleMatches(tournamentId, date) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/${tournamentId}?date=${date}`);\n  }\n  getListUnScheduledMatches(tournamentId) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/${tournamentId}/unscheduled-matches`);\n  }\n  scheduleTournamentAsync(formData) {\n    return this._http.post(`${environment.apiUrl}/auto-schedule/generate-async`, formData);\n  }\n  getScheduleConfigById(configId) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/schedule-config/${configId}`);\n  }\n  updateScheduleConfig(formData) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/schedule-config`, formData);\n  }\n  deleteSchedule(tournamentId, locationId, timeSlotIds, configId) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/delete-schedule`, {\n      tournament_id: tournamentId,\n      location: locationId,\n      time_slot_ids: timeSlotIds,\n      config_id: configId\n    });\n  }\n  unScheduleMatch(timeSlotId, configId) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/unschedule-time-slot`, {\n      time_slot_id: timeSlotId,\n      config_id: configId\n    });\n  }\n  updateLocationMatch(updateData) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/update-location-match`, updateData);\n  }\n  addBreak(formModel) {\n    return this._http.post(`${environment.apiUrl}/auto-schedule/add-break`, formModel);\n  }\n  updateBreak(formModel) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/update-break`, formModel);\n  }\n  updateMatchReferee(formModel) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/update-referees`, formModel);\n  }\n  clearSchedule(tournamentId) {\n    return this._http.delete(`${environment.apiUrl}/auto-schedule/${tournamentId}/clear-schedule`);\n  }\n  updateTournamentScheduleStatus(tournamentId) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/${tournamentId}/update-schedule-status`, {});\n  }\n  getListDates(tournamentId) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/${tournamentId}/dates`);\n  }\n  static #_ = this.ɵfac = function AutoScheduleService_Factory(t) {\n    return new (t || AutoScheduleService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AutoScheduleService,\n    factory: AutoScheduleService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;;;AAgBtD,OAAM,MAAOC,mBAAmB;EAE5BC,YAAoBC,KAAiB;IAAjB,KAAAA,KAAK,GAALA,KAAK;EACzB;EAEAC,kBAAkBA,CAACC,YAAoB,EAAEC,IAAY;IACjD,OAAO,IAAI,CAACH,KAAK,CAACI,GAAG,CAAM,GAAGP,WAAW,CAACQ,MAAM,kBAAkBH,YAAY,SAASC,IAAI,EAAE,CAAC;EAClG;EAEAG,yBAAyBA,CAACJ,YAAoB;IAC1C,OAAO,IAAI,CAACF,KAAK,CAACI,GAAG,CAAM,GAAGP,WAAW,CAACQ,MAAM,kBAAkBH,YAAY,sBAAsB,CAAC;EACzG;EAEAK,uBAAuBA,CAACC,QAAQ;IAC5B,OAAO,IAAI,CAACR,KAAK,CAACS,IAAI,CAAM,GAAGZ,WAAW,CAACQ,MAAM,+BAA+B,EAAEG,QAAQ,CAAC;EAC/F;EAEAE,qBAAqBA,CAACC,QAAyB;IAC3C,OAAO,IAAI,CAACX,KAAK,CAACI,GAAG,CAAM,GAAGP,WAAW,CAACQ,MAAM,kCAAkCM,QAAQ,EAAE,CAAC;EACjG;EAEAC,oBAAoBA,CAACJ,QAAQ;IACzB,OAAO,IAAI,CAACR,KAAK,CAACa,GAAG,CAAM,GAAGhB,WAAW,CAACQ,MAAM,gCAAgC,EAAEG,QAAQ,CAAC;EAC/F;EAEAM,cAAcA,CAACZ,YAAoB,EAAEa,UAAkB,EAAEC,WAAqB,EAAEL,QAAgB;IAC5F,OAAO,IAAI,CAACX,KAAK,CAACa,GAAG,CAAM,GAAGhB,WAAW,CAACQ,MAAM,gCAAgC,EAAE;MAC9EY,aAAa,EAAEf,YAAY;MAC3BgB,QAAQ,EAAEH,UAAU;MACpBI,aAAa,EAAEH,WAAW;MAC1BI,SAAS,EAAET;KACd,CAAC;EACN;EAEAU,eAAeA,CAACC,UAA2B,EAAEX,QAAyB;IAClE,OAAO,IAAI,CAACX,KAAK,CAACa,GAAG,CAAM,GAAGhB,WAAW,CAACQ,MAAM,qCAAqC,EAAE;MACnFkB,YAAY,EAAED,UAAU;MACxBF,SAAS,EAAET;KACd,CAAC;EACN;EAEAa,mBAAmBA,CAACC,UAAqC;IACrD,OAAO,IAAI,CAACzB,KAAK,CAACa,GAAG,CAAC,GAAGhB,WAAW,CAACQ,MAAM,sCAAsC,EAAEoB,UAAU,CAAC;EAClG;EAEAC,QAAQA,CAACC,SAAS;IACd,OAAO,IAAI,CAAC3B,KAAK,CAACS,IAAI,CAAM,GAAGZ,WAAW,CAACQ,MAAM,0BAA0B,EAAEsB,SAAS,CAAC;EAC3F;EAEAC,WAAWA,CAACD,SAAS;IACjB,OAAO,IAAI,CAAC3B,KAAK,CAACa,GAAG,CAAM,GAAGhB,WAAW,CAACQ,MAAM,6BAA6B,EAAEsB,SAAS,CAAC;EAC7F;EAEAE,kBAAkBA,CAACF,SAAS;IACxB,OAAO,IAAI,CAAC3B,KAAK,CAACa,GAAG,CAAC,GAAGhB,WAAW,CAACQ,MAAM,gCAAgC,EAAEsB,SAAS,CAAC;EAC3F;EAEAG,aAAaA,CAAC5B,YAAoB;IAC9B,OAAO,IAAI,CAACF,KAAK,CAAC+B,MAAM,CAAM,GAAGlC,WAAW,CAACQ,MAAM,kBAAkBH,YAAY,iBAAiB,CAAC;EACvG;EAEA8B,8BAA8BA,CAAC9B,YAAoB;IAC/C,OAAO,IAAI,CAACF,KAAK,CAACa,GAAG,CAAM,GAAGhB,WAAW,CAACQ,MAAM,kBAAkBH,YAAY,yBAAyB,EAAE,EAAE,CAAC;EAChH;EAEA+B,YAAYA,CAAC/B,YAA6B;IACtC,OAAO,IAAI,CAACF,KAAK,CAACI,GAAG,CAAC,GAAGP,WAAW,CAACQ,MAAM,kBAAkBH,YAAY,QAAQ,CAAC;EACtF;EAAC,QAAAgC,CAAA;qBAnEQpC,mBAAmB,EAAAqC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAnBzC,mBAAmB;IAAA0C,OAAA,EAAnB1C,mBAAmB,CAAA2C,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["environment", "AutoScheduleService", "constructor", "_http", "getScheduleMatches", "tournamentId", "date", "get", "apiUrl", "getListUnScheduledMatches", "scheduleTournamentAsync", "formData", "post", "getScheduleConfigById", "configId", "updateScheduleConfig", "put", "deleteSchedule", "locationId", "timeSlotIds", "tournament_id", "location", "time_slot_ids", "config_id", "unScheduleMatch", "timeSlotId", "time_slot_id", "updateLocationMatch", "updateData", "addBreak", "formModel", "updateBreak", "updateMatchReferee", "clearSchedule", "delete", "updateTournamentScheduleStatus", "getListDates", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\auto-schedule.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from 'environments/environment';\r\n\r\nexport type UpdateLocationMatchParams = {\r\n    new_index: { [key: string | number]: number };\r\n    location_id: number;\r\n    prev_location_id: number;\r\n    stage_id: number;\r\n    prev_stage_id: number;\r\n    tournament_id: number;\r\n    config_id: number;\r\n    prev_config_id: number;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class AutoScheduleService {\r\n\r\n    constructor(private _http: HttpClient) {\r\n    }\r\n\r\n    getScheduleMatches(tournamentId: string, date: string) {\r\n        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}?date=${date}`);\r\n    }\r\n\r\n    getListUnScheduledMatches(tournamentId: string) {\r\n        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/unscheduled-matches`);\r\n    }\r\n\r\n    scheduleTournamentAsync(formData) {\r\n        return this._http.post<any>(`${environment.apiUrl}/auto-schedule/generate-async`, formData);\r\n    }\r\n\r\n    getScheduleConfigById(configId: number | string) {\r\n        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/schedule-config/${configId}`);\r\n    }\r\n\r\n    updateScheduleConfig(formData) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/schedule-config`, formData);\r\n    }\r\n\r\n    deleteSchedule(tournamentId: string, locationId: string, timeSlotIds: number[], configId: number) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/delete-schedule`, {\r\n            tournament_id: tournamentId,\r\n            location: locationId,\r\n            time_slot_ids: timeSlotIds,\r\n            config_id: configId,\r\n        });\r\n    }\r\n\r\n    unScheduleMatch(timeSlotId: string | number, configId: string | number) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/unschedule-time-slot`, {\r\n            time_slot_id: timeSlotId,\r\n            config_id: configId,\r\n        });\r\n    }\r\n\r\n    updateLocationMatch(updateData: UpdateLocationMatchParams) {\r\n        return this._http.put(`${environment.apiUrl}/auto-schedule/update-location-match`, updateData);\r\n    }\r\n\r\n    addBreak(formModel) {\r\n        return this._http.post<any>(`${environment.apiUrl}/auto-schedule/add-break`, formModel);\r\n    }\r\n\r\n    updateBreak(formModel) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/update-break`, formModel);\r\n    }\r\n\r\n    updateMatchReferee(formModel) {\r\n        return this._http.put(`${environment.apiUrl}/auto-schedule/update-referees`, formModel);\r\n    }\r\n\r\n    clearSchedule(tournamentId: string) {\r\n        return this._http.delete<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/clear-schedule`);\r\n    }\r\n\r\n    updateTournamentScheduleStatus(tournamentId: string) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/update-schedule-status`, {});\r\n    }\r\n\r\n    getListDates(tournamentId: string | number) {\r\n        return this._http.get(`${environment.apiUrl}/auto-schedule/${tournamentId}/dates`);\r\n    }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
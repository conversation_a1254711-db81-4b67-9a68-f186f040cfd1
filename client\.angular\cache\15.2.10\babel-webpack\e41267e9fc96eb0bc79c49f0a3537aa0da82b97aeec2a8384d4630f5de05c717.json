{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BadgeLiveComponent {\n  constructor() {\n    this.icon_size = 'sm';\n  }\n  static #_ = this.ɵfac = function BadgeLiveComponent_Factory(t) {\n    return new (t || BadgeLiveComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BadgeLiveComponent,\n    selectors: [[\"badge-live\"]],\n    inputs: {\n      icon_size: \"icon_size\"\n    },\n    decls: 9,\n    vars: 3,\n    consts: [[1, \"badge\", \"badge-danger\", \"mr-1\"], [1, \"d-table-cell\", \"align-middle\", 2, \"padding-right\", \"5px\"]],\n    template: function BadgeLiveComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"span\", 0)(1, \"tr\")(2, \"td\", 1);\n        i0.ɵɵtext(3, \" LIVE \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"td\");\n        i0.ɵɵelement(5, \"div\")(6, \"div\")(7, \"div\")(8, \"div\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassMapInterpolate1(\"livenow livenow-\", ctx.icon_size, \"\");\n      }\n    },\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAmBA,OAAM,MAAOA,kBAAkB;EAjB/BC,YAAA;IAkBW,KAAAC,SAAS,GAAW,IAAI;;EAClC,QAAAC,CAAA;qBAFYH,kBAAkB;EAAA;EAAA,QAAAI,EAAA;UAAlBJ,kBAAkB;IAAAK,SAAA;IAAAC,MAAA;MAAAJ,SAAA;IAAA;IAAAK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAflBE,EAAA,CAAAC,cAAA,cAAsC;QAG3CD,EAAA,CAAAE,MAAA,aACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,SAA4C;QAC1CD,EAAA,CAAAI,SAAA,UAAW;QAIbJ,EAAA,CAAAG,YAAA,EAAK;;;QALDH,EAAA,CAAAK,SAAA,GAAuC;QAAvCL,EAAA,CAAAM,sBAAA,qBAAAP,GAAA,CAAAX,SAAA,KAAuC", "names": ["BadgeLiveComponent", "constructor", "icon_size", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "BadgeLiveComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵclassMapInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\widget\\badge-live\\badge-live.ts"], "sourcesContent": ["import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'badge-live',\r\n  template: `<span class=\"badge badge-danger mr-1\">\r\n    <tr>\r\n      <td class=\"d-table-cell align-middle\" style=\"padding-right: 5px;\">\r\n        LIVE\r\n      </td>\r\n      <td class=\"livenow livenow-{{ icon_size }}\">\r\n        <div></div>\r\n        <div></div>\r\n        <div></div>\r\n        <div></div>\r\n      </td>\r\n    </tr>\r\n  </span>`,\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class BadgeLiveComponent {\r\n  @Input() icon_size: string = 'sm';\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
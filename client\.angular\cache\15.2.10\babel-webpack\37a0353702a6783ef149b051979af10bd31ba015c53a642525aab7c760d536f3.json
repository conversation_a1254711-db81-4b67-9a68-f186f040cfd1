{"ast": null, "code": "import { fadeInLeft, zoomIn, fadeIn } from '@core/animations/core.animation';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/config.service\";\nimport * as i2 from \"@angular/router\";\nexport class ContentComponent {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {CoreConfigService} _coreConfigService\r\n   *\r\n   */\n  constructor(_coreConfigService) {\n    this._coreConfigService = _coreConfigService;\n    // Set the private defaults\n    this._unsubscribeAll = new Subject();\n  }\n  /**\r\n   * Fade In Left Animation\r\n   *\r\n   * @param outlet\r\n   */\n  fadeInLeft(outlet) {\n    if (this.animate === 'fadeInLeft') {\n      return outlet.activatedRouteData.animation;\n    }\n    return null;\n  }\n  /**\r\n   * Zoom In Animation\r\n   *\r\n   * @param outlet\r\n   */\n  zoomIn(outlet) {\n    if (this.animate === 'zoomIn') {\n      return outlet.activatedRouteData.animation;\n    }\n    return null;\n  }\n  /**\r\n   * Fade In Animation\r\n   *\r\n   * @param outlet\r\n   */\n  fadeIn(outlet) {\n    if (this.animate === 'fadeIn') {\n      return outlet.activatedRouteData.animation;\n    }\n    return null;\n  }\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On Init\r\n   */\n  ngOnInit() {\n    // Subscribe config change\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.coreConfig = config;\n      this.animate = this.coreConfig.layout.animation;\n    });\n  }\n  static #_ = this.ɵfac = function ContentComponent_Factory(t) {\n    return new (t || ContentComponent)(i0.ɵɵdirectiveInject(i1.CoreConfigService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ContentComponent,\n    selectors: [[\"content\"]],\n    decls: 3,\n    vars: 3,\n    consts: [[\"outlet\", \"outlet\"]],\n    template: function ContentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\");\n        i0.ɵɵelement(1, \"router-outlet\", null, 0);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"@zoomIn\", ctx.zoomIn(_r0))(\"@fadeInLeft\", ctx.fadeInLeft(_r0))(\"@fadeIn\", ctx.fadeIn(_r0));\n      }\n    },\n    dependencies: [i2.RouterOutlet],\n    encapsulation: 2,\n    data: {\n      animation: [fadeInLeft, zoomIn, fadeIn]\n    }\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,UAAU,EAAEC,MAAM,EAAEC,MAAM,QAAQ,iCAAiC;AAC5E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;AAQ1C,OAAM,MAAOC,gBAAgB;EAO3B;;;;;;EAMAC,YAAoBC,kBAAqC;IAArC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,IAAIL,OAAO,EAAE;EACtC;EAEA;;;;;EAKAH,UAAUA,CAACS,MAAM;IACf,IAAI,IAAI,CAACC,OAAO,KAAK,YAAY,EAAE;MACjC,OAAOD,MAAM,CAACE,kBAAkB,CAACC,SAAS;;IAE5C,OAAO,IAAI;EACb;EAEA;;;;;EAKAX,MAAMA,CAACQ,MAAM;IACX,IAAI,IAAI,CAACC,OAAO,KAAK,QAAQ,EAAE;MAC7B,OAAOD,MAAM,CAACE,kBAAkB,CAACC,SAAS;;IAE5C,OAAO,IAAI;EACb;EAEA;;;;;EAKAV,MAAMA,CAACO,MAAM;IACX,IAAI,IAAI,CAACC,OAAO,KAAK,QAAQ,EAAE;MAC7B,OAAOD,MAAM,CAACE,kBAAkB,CAACC,SAAS;;IAE5C,OAAO,IAAI;EACb;EAEA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,kBAAkB,CAACO,MAAM,CAACC,IAAI,CAACX,SAAS,CAAC,IAAI,CAACI,eAAe,CAAC,CAAC,CAACQ,SAAS,CAACF,MAAM,IAAG;MACtF,IAAI,CAACG,UAAU,GAAGH,MAAM;MACxB,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACO,UAAU,CAACC,MAAM,CAACN,SAAS;IACjD,CAAC,CAAC;EACJ;EAAC,QAAAO,CAAA;qBAlEUd,gBAAgB,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAAhBnB,gBAAgB;IAAAoB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV7BX,EAAA,CAAAa,cAAA,UAA8F;QAC5Fb,EAAA,CAAAc,SAAA,6BAAgD;QAClDd,EAAA,CAAAe,YAAA,EAAM;;;;QAFDf,EAAA,CAAAgB,UAAA,YAAAJ,GAAA,CAAA/B,MAAA,CAAAoC,GAAA,EAA0B,gBAAAL,GAAA,CAAAhC,UAAA,CAAAqC,GAAA,cAAAL,GAAA,CAAA9B,MAAA,CAAAmC,GAAA;;;;;;iBDQjB,CAACrC,UAAU,EAAEC,MAAM,EAAEC,MAAM;IAAC;EAAA", "names": ["fadeInLeft", "zoomIn", "fadeIn", "Subject", "takeUntil", "ContentComponent", "constructor", "_coreConfigService", "_unsubscribeAll", "outlet", "animate", "activatedRouteData", "animation", "ngOnInit", "config", "pipe", "subscribe", "coreConfig", "layout", "_", "i0", "ɵɵdirectiveInject", "i1", "CoreConfigService", "_2", "selectors", "decls", "vars", "consts", "template", "ContentComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "_r0"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content\\content.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content\\content.component.html"], "sourcesContent": ["import { Component, ViewEncapsulation } from '@angular/core';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { fadeInLeft, zoomIn, fadeIn } from '@core/animations/core.animation';\r\nimport { Subject } from 'rxjs';\r\nimport { takeUntil } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'content',\r\n  templateUrl: './content.component.html',\r\n  encapsulation: ViewEncapsulation.None,\r\n  animations: [fadeInLeft, zoomIn, fadeIn]\r\n})\r\nexport class ContentComponent {\r\n  public coreConfig: any;\r\n  public animate;\r\n\r\n  // Private\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {CoreConfigService} _coreConfigService\r\n   *\r\n   */\r\n  constructor(private _coreConfigService: CoreConfigService) {\r\n    // Set the private defaults\r\n    this._unsubscribeAll = new Subject();\r\n  }\r\n\r\n  /**\r\n   * Fade In Left Animation\r\n   *\r\n   * @param outlet\r\n   */\r\n  fadeInLeft(outlet) {\r\n    if (this.animate === 'fadeInLeft') {\r\n      return outlet.activatedRouteData.animation;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Zoom In Animation\r\n   *\r\n   * @param outlet\r\n   */\r\n  zoomIn(outlet) {\r\n    if (this.animate === 'zoomIn') {\r\n      return outlet.activatedRouteData.animation;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Fade In Animation\r\n   *\r\n   * @param outlet\r\n   */\r\n  fadeIn(outlet) {\r\n    if (this.animate === 'fadeIn') {\r\n      return outlet.activatedRouteData.animation;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On Init\r\n   */\r\n  ngOnInit(): void {\r\n    // Subscribe config change\r\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\r\n      this.coreConfig = config;\r\n      this.animate = this.coreConfig.layout.animation;\r\n    });\r\n  }\r\n}\r\n", "<!-- Create a template reference to the outlet directive with the template variable assignment #outlet=\"outlet\".\r\nWith this reference to the outlet directive, we can get the information of when the router outlet is active to trigger our animations. -->\r\n<div [@zoomIn]=\"zoomIn(outlet)\" [@fadeInLeft]=\"fadeInLeft(outlet)\" [@fadeIn]=\"fadeIn(outlet)\">\r\n  <router-outlet #outlet=\"outlet\"></router-outlet>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
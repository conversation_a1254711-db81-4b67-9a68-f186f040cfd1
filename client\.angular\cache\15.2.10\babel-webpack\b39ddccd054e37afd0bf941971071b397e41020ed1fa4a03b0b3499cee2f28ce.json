{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n  if (subscriptionDelay) {\n    return source => new SubscriptionDelayObservable(source, subscriptionDelay).lift(new DelayWhenOperator(delayDurationSelector));\n  }\n  return source => source.lift(new DelayWhenOperator(delayDurationSelector));\n}\nclass DelayWhenOperator {\n  constructor(delayDurationSelector) {\n    this.delayDurationSelector = delayDurationSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new DelayWhenSubscriber(subscriber, this.delayDurationSelector));\n  }\n}\nclass DelayWhenSubscriber extends OuterSubscriber {\n  constructor(destination, delayDurationSelector) {\n    super(destination);\n    this.delayDurationSelector = delayDurationSelector;\n    this.completed = false;\n    this.delayNotifierSubscriptions = [];\n    this.index = 0;\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.destination.next(outerValue);\n    this.removeSubscription(innerSub);\n    this.tryComplete();\n  }\n  notifyError(error, innerSub) {\n    this._error(error);\n  }\n  notifyComplete(innerSub) {\n    const value = this.removeSubscription(innerSub);\n    if (value) {\n      this.destination.next(value);\n    }\n    this.tryComplete();\n  }\n  _next(value) {\n    const index = this.index++;\n    try {\n      const delayNotifier = this.delayDurationSelector(value, index);\n      if (delayNotifier) {\n        this.tryDelay(delayNotifier, value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n  _complete() {\n    this.completed = true;\n    this.tryComplete();\n    this.unsubscribe();\n  }\n  removeSubscription(subscription) {\n    subscription.unsubscribe();\n    const subscriptionIdx = this.delayNotifierSubscriptions.indexOf(subscription);\n    if (subscriptionIdx !== -1) {\n      this.delayNotifierSubscriptions.splice(subscriptionIdx, 1);\n    }\n    return subscription.outerValue;\n  }\n  tryDelay(delayNotifier, value) {\n    const notifierSubscription = subscribeToResult(this, delayNotifier, value);\n    if (notifierSubscription && !notifierSubscription.closed) {\n      const destination = this.destination;\n      destination.add(notifierSubscription);\n      this.delayNotifierSubscriptions.push(notifierSubscription);\n    }\n  }\n  tryComplete() {\n    if (this.completed && this.delayNotifierSubscriptions.length === 0) {\n      this.destination.complete();\n    }\n  }\n}\nclass SubscriptionDelayObservable extends Observable {\n  constructor(source, subscriptionDelay) {\n    super();\n    this.source = source;\n    this.subscriptionDelay = subscriptionDelay;\n  }\n  _subscribe(subscriber) {\n    this.subscriptionDelay.subscribe(new SubscriptionDelaySubscriber(subscriber, this.source));\n  }\n}\nclass SubscriptionDelaySubscriber extends Subscriber {\n  constructor(parent, source) {\n    super();\n    this.parent = parent;\n    this.source = source;\n    this.sourceSubscribed = false;\n  }\n  _next(unused) {\n    this.subscribeToSource();\n  }\n  _error(err) {\n    this.unsubscribe();\n    this.parent.error(err);\n  }\n  _complete() {\n    this.unsubscribe();\n    this.subscribeToSource();\n  }\n  subscribeToSource() {\n    if (!this.sourceSubscribed) {\n      this.sourceSubscribed = true;\n      this.unsubscribe();\n      this.source.subscribe(this.parent);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "Observable", "OuterSubscriber", "subscribeToResult", "<PERSON><PERSON>hen", "delayDurationSelector", "subscriptionDelay", "source", "SubscriptionDelayObservable", "lift", "DelayWhenOperator", "constructor", "call", "subscriber", "subscribe", "DelayWhenSubscriber", "destination", "completed", "delayNotifierSubscriptions", "index", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "next", "removeSubscription", "tryComplete", "notifyError", "error", "_error", "notifyComplete", "value", "_next", "delayNotifier", "<PERSON><PERSON><PERSON><PERSON>", "err", "_complete", "unsubscribe", "subscription", "subscriptionIdx", "indexOf", "splice", "notifierSubscription", "closed", "add", "push", "length", "complete", "_subscribe", "SubscriptionDelaySubscriber", "parent", "sourceSubscribed", "unused", "subscribeToSource"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/delayWhen.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return (source) => new SubscriptionDelayObservable(source, subscriptionDelay)\n            .lift(new DelayWhenOperator(delayDurationSelector));\n    }\n    return (source) => source.lift(new DelayWhenOperator(delayDurationSelector));\n}\nclass DelayWhenOperator {\n    constructor(delayDurationSelector) {\n        this.delayDurationSelector = delayDurationSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DelayWhenSubscriber(subscriber, this.delayDurationSelector));\n    }\n}\nclass DelayWhenSubscriber extends OuterSubscriber {\n    constructor(destination, delayDurationSelector) {\n        super(destination);\n        this.delayDurationSelector = delayDurationSelector;\n        this.completed = false;\n        this.delayNotifierSubscriptions = [];\n        this.index = 0;\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.destination.next(outerValue);\n        this.removeSubscription(innerSub);\n        this.tryComplete();\n    }\n    notifyError(error, innerSub) {\n        this._error(error);\n    }\n    notifyComplete(innerSub) {\n        const value = this.removeSubscription(innerSub);\n        if (value) {\n            this.destination.next(value);\n        }\n        this.tryComplete();\n    }\n    _next(value) {\n        const index = this.index++;\n        try {\n            const delayNotifier = this.delayDurationSelector(value, index);\n            if (delayNotifier) {\n                this.tryDelay(delayNotifier, value);\n            }\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n    _complete() {\n        this.completed = true;\n        this.tryComplete();\n        this.unsubscribe();\n    }\n    removeSubscription(subscription) {\n        subscription.unsubscribe();\n        const subscriptionIdx = this.delayNotifierSubscriptions.indexOf(subscription);\n        if (subscriptionIdx !== -1) {\n            this.delayNotifierSubscriptions.splice(subscriptionIdx, 1);\n        }\n        return subscription.outerValue;\n    }\n    tryDelay(delayNotifier, value) {\n        const notifierSubscription = subscribeToResult(this, delayNotifier, value);\n        if (notifierSubscription && !notifierSubscription.closed) {\n            const destination = this.destination;\n            destination.add(notifierSubscription);\n            this.delayNotifierSubscriptions.push(notifierSubscription);\n        }\n    }\n    tryComplete() {\n        if (this.completed && this.delayNotifierSubscriptions.length === 0) {\n            this.destination.complete();\n        }\n    }\n}\nclass SubscriptionDelayObservable extends Observable {\n    constructor(source, subscriptionDelay) {\n        super();\n        this.source = source;\n        this.subscriptionDelay = subscriptionDelay;\n    }\n    _subscribe(subscriber) {\n        this.subscriptionDelay.subscribe(new SubscriptionDelaySubscriber(subscriber, this.source));\n    }\n}\nclass SubscriptionDelaySubscriber extends Subscriber {\n    constructor(parent, source) {\n        super();\n        this.parent = parent;\n        this.source = source;\n        this.sourceSubscribed = false;\n    }\n    _next(unused) {\n        this.subscribeToSource();\n    }\n    _error(err) {\n        this.unsubscribe();\n        this.parent.error(err);\n    }\n    _complete() {\n        this.unsubscribe();\n        this.subscribeToSource();\n    }\n    subscribeToSource() {\n        if (!this.sourceSubscribed) {\n            this.sourceSubscribed = true;\n            this.unsubscribe();\n            this.source.subscribe(this.parent);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,SAASA,CAACC,qBAAqB,EAAEC,iBAAiB,EAAE;EAChE,IAAIA,iBAAiB,EAAE;IACnB,OAAQC,MAAM,IAAK,IAAIC,2BAA2B,CAACD,MAAM,EAAED,iBAAiB,CAAC,CACxEG,IAAI,CAAC,IAAIC,iBAAiB,CAACL,qBAAqB,CAAC,CAAC;EAC3D;EACA,OAAQE,MAAM,IAAKA,MAAM,CAACE,IAAI,CAAC,IAAIC,iBAAiB,CAACL,qBAAqB,CAAC,CAAC;AAChF;AACA,MAAMK,iBAAiB,CAAC;EACpBC,WAAWA,CAACN,qBAAqB,EAAE;IAC/B,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;EACtD;EACAO,IAAIA,CAACC,UAAU,EAAEN,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACO,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACR,qBAAqB,CAAC,CAAC;EAC5F;AACJ;AACA,MAAMU,mBAAmB,SAASb,eAAe,CAAC;EAC9CS,WAAWA,CAACK,WAAW,EAAEX,qBAAqB,EAAE;IAC5C,KAAK,CAACW,WAAW,CAAC;IAClB,IAAI,CAACX,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACY,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,0BAA0B,GAAG,EAAE;IACpC,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACT,WAAW,CAACU,IAAI,CAACL,UAAU,CAAC;IACjC,IAAI,CAACM,kBAAkB,CAACF,QAAQ,CAAC;IACjC,IAAI,CAACG,WAAW,CAAC,CAAC;EACtB;EACAC,WAAWA,CAACC,KAAK,EAAEL,QAAQ,EAAE;IACzB,IAAI,CAACM,MAAM,CAACD,KAAK,CAAC;EACtB;EACAE,cAAcA,CAACP,QAAQ,EAAE;IACrB,MAAMQ,KAAK,GAAG,IAAI,CAACN,kBAAkB,CAACF,QAAQ,CAAC;IAC/C,IAAIQ,KAAK,EAAE;MACP,IAAI,CAACjB,WAAW,CAACU,IAAI,CAACO,KAAK,CAAC;IAChC;IACA,IAAI,CAACL,WAAW,CAAC,CAAC;EACtB;EACAM,KAAKA,CAACD,KAAK,EAAE;IACT,MAAMd,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;IAC1B,IAAI;MACA,MAAMgB,aAAa,GAAG,IAAI,CAAC9B,qBAAqB,CAAC4B,KAAK,EAAEd,KAAK,CAAC;MAC9D,IAAIgB,aAAa,EAAE;QACf,IAAI,CAACC,QAAQ,CAACD,aAAa,EAAEF,KAAK,CAAC;MACvC;IACJ,CAAC,CACD,OAAOI,GAAG,EAAE;MACR,IAAI,CAACrB,WAAW,CAACc,KAAK,CAACO,GAAG,CAAC;IAC/B;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACrB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACW,WAAW,CAAC,CAAC;IAClB,IAAI,CAACW,WAAW,CAAC,CAAC;EACtB;EACAZ,kBAAkBA,CAACa,YAAY,EAAE;IAC7BA,YAAY,CAACD,WAAW,CAAC,CAAC;IAC1B,MAAME,eAAe,GAAG,IAAI,CAACvB,0BAA0B,CAACwB,OAAO,CAACF,YAAY,CAAC;IAC7E,IAAIC,eAAe,KAAK,CAAC,CAAC,EAAE;MACxB,IAAI,CAACvB,0BAA0B,CAACyB,MAAM,CAACF,eAAe,EAAE,CAAC,CAAC;IAC9D;IACA,OAAOD,YAAY,CAACnB,UAAU;EAClC;EACAe,QAAQA,CAACD,aAAa,EAAEF,KAAK,EAAE;IAC3B,MAAMW,oBAAoB,GAAGzC,iBAAiB,CAAC,IAAI,EAAEgC,aAAa,EAAEF,KAAK,CAAC;IAC1E,IAAIW,oBAAoB,IAAI,CAACA,oBAAoB,CAACC,MAAM,EAAE;MACtD,MAAM7B,WAAW,GAAG,IAAI,CAACA,WAAW;MACpCA,WAAW,CAAC8B,GAAG,CAACF,oBAAoB,CAAC;MACrC,IAAI,CAAC1B,0BAA0B,CAAC6B,IAAI,CAACH,oBAAoB,CAAC;IAC9D;EACJ;EACAhB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACX,SAAS,IAAI,IAAI,CAACC,0BAA0B,CAAC8B,MAAM,KAAK,CAAC,EAAE;MAChE,IAAI,CAAChC,WAAW,CAACiC,QAAQ,CAAC,CAAC;IAC/B;EACJ;AACJ;AACA,MAAMzC,2BAA2B,SAASP,UAAU,CAAC;EACjDU,WAAWA,CAACJ,MAAM,EAAED,iBAAiB,EAAE;IACnC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA4C,UAAUA,CAACrC,UAAU,EAAE;IACnB,IAAI,CAACP,iBAAiB,CAACQ,SAAS,CAAC,IAAIqC,2BAA2B,CAACtC,UAAU,EAAE,IAAI,CAACN,MAAM,CAAC,CAAC;EAC9F;AACJ;AACA,MAAM4C,2BAA2B,SAASnD,UAAU,CAAC;EACjDW,WAAWA,CAACyC,MAAM,EAAE7C,MAAM,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAAC6C,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC7C,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC8C,gBAAgB,GAAG,KAAK;EACjC;EACAnB,KAAKA,CAACoB,MAAM,EAAE;IACV,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAxB,MAAMA,CAACM,GAAG,EAAE;IACR,IAAI,CAACE,WAAW,CAAC,CAAC;IAClB,IAAI,CAACa,MAAM,CAACtB,KAAK,CAACO,GAAG,CAAC;EAC1B;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACgB,iBAAiB,CAAC,CAAC;EAC5B;EACAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACd,WAAW,CAAC,CAAC;MAClB,IAAI,CAAChC,MAAM,CAACO,SAAS,CAAC,IAAI,CAACsC,MAAM,CAAC;IACtC;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
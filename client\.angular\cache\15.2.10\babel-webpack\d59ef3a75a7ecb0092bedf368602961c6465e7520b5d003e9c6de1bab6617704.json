{"ast": null, "code": "let nextHandle = 1;\nconst RESOLVED = (() => Promise.resolve())();\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n  return false;\n}\nexport const Immediate = {\n  setImmediate(cb) {\n    const handle = nextHandle++;\n    activeHandles[handle] = true;\n    RESOLVED.then(() => findAndClearHandle(handle) && cb());\n    return handle;\n  },\n  clearImmediate(handle) {\n    findAndClearHandle(handle);\n  }\n};\nexport const TestTools = {\n  pending() {\n    return Object.keys(activeHandles).length;\n  }\n};", "map": {"version": 3, "names": ["nextH<PERSON>le", "RESOLVED", "Promise", "resolve", "active<PERSON><PERSON><PERSON>", "findAndClearHandle", "handle", "Immediate", "setImmediate", "cb", "then", "clearImmediate", "TestTools", "pending", "Object", "keys", "length"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/Immediate.js"], "sourcesContent": ["let nextHandle = 1;\nconst RESOLVED = (() => Promise.resolve())();\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n    if (handle in activeHandles) {\n        delete activeHandles[handle];\n        return true;\n    }\n    return false;\n}\nexport const Immediate = {\n    setImmediate(cb) {\n        const handle = nextHandle++;\n        activeHandles[handle] = true;\n        RESOLVED.then(() => findAndClearHandle(handle) && cb());\n        return handle;\n    },\n    clearImmediate(handle) {\n        findAndClearHandle(handle);\n    },\n};\nexport const TestTools = {\n    pending() {\n        return Object.keys(activeHandles).length;\n    }\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAG,CAAC;AAClB,MAAMC,QAAQ,GAAG,CAAC,MAAMC,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE,CAAC;AAC5C,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC,IAAIA,MAAM,IAAIF,aAAa,EAAE;IACzB,OAAOA,aAAa,CAACE,MAAM,CAAC;IAC5B,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,OAAO,MAAMC,SAAS,GAAG;EACrBC,YAAYA,CAACC,EAAE,EAAE;IACb,MAAMH,MAAM,GAAGN,UAAU,EAAE;IAC3BI,aAAa,CAACE,MAAM,CAAC,GAAG,IAAI;IAC5BL,QAAQ,CAACS,IAAI,CAAC,MAAML,kBAAkB,CAACC,MAAM,CAAC,IAAIG,EAAE,CAAC,CAAC,CAAC;IACvD,OAAOH,MAAM;EACjB,CAAC;EACDK,cAAcA,CAACL,MAAM,EAAE;IACnBD,kBAAkB,CAACC,MAAM,CAAC;EAC9B;AACJ,CAAC;AACD,OAAO,MAAMM,SAAS,GAAG;EACrBC,OAAOA,CAAA,EAAG;IACN,OAAOC,MAAM,CAACC,IAAI,CAACX,aAAa,CAAC,CAACY,MAAM;EAC5C;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
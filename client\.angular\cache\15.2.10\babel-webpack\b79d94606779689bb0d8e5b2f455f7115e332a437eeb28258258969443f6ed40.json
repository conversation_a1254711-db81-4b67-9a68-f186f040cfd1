{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../scoreboard/scoreboard.component\";\nimport * as i2 from \"../sponsors-overlay/sponsors-overlay.component\";\nimport * as i3 from \"../srolling-text/srolling-text.component\";\nexport class ScreenOverlaysComponent {\n  constructor() {}\n  ngOnInit() {\n    this.resizeEvent.subscribe(() => {\n      this.resizeScreen();\n    });\n  }\n  ngAfterViewInit() {\n    this.resizeScreen();\n    // listen for resize event\n    window.addEventListener('resize', () => {\n      this.resizeScreen();\n    });\n  }\n  resizeScreen() {\n    let screen = document.getElementsByClassName('screen')[0];\n    // resize screen to fit parent container\n    // scale screen to fit parent container\n    let ratio = 16 / 9;\n    let parent = screen.parentElement.parentElement;\n    let prent_width = parent.clientWidth;\n    let prent_height = parent.clientHeight;\n    let width = prent_width;\n    let height = prent_height;\n    if (width / height > ratio) {\n      width = height * ratio;\n    } else {\n      height = width / ratio;\n    }\n    let scale_w = width / 1920;\n    let scale_h = height / 1080;\n    screen.style.transform = `scale(${scale_w}, ${scale_h})`;\n    // get element position\n    let screen_rect = screen.getBoundingClientRect();\n    let screen_width = screen_rect.width;\n    let screen_height = screen_rect.height;\n    let screen_x = screen_rect.x;\n    let screen_y = screen_rect.y;\n    screen.style.transformOrigin = '0px 0px';\n    // calculate top and left position\n    let top = 0;\n    let left = 0;\n    if (screen_width < prent_width) {\n      left = (prent_width - screen_width) / 2;\n    }\n    if (screen_height < prent_height) {\n      top = (prent_height - screen_height) / 2;\n    }\n    screen.style.top = `${top}px`;\n    screen.style.left = `${left}px`;\n  }\n  static #_ = this.ɵfac = function ScreenOverlaysComponent_Factory(t) {\n    return new (t || ScreenOverlaysComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ScreenOverlaysComponent,\n    selectors: [[\"screen-overlays\"]],\n    inputs: {\n      resizeEvent: \"resizeEvent\"\n    },\n    decls: 4,\n    vars: 0,\n    consts: [[1, \"screen\"]],\n    template: function ScreenOverlaysComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"overlay-scoreboard\")(2, \"sponsors-overlay\")(3, \"srolling-text\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [i1.ScoreboardComponent, i2.SponsorsOverlayComponent, i3.SrollingTextComponent],\n    styles: [\".screen[_ngcontent-%COMP%] {\\n  background-color: transparent !important;\\n  visibility: inherit;\\n  width: 1920px;\\n  height: 1080px;\\n  z-index: 1;\\n  pointer-events: none;\\n  position: absolute;\\n  overflow: hidden;\\n  touch-action: none;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  -webkit-user-drag: none;\\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9vdmVybGF5cy9zY3JlZW4tb3ZlcmxheXMvc2NyZWVuLW92ZXJsYXlzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBO0VBQ0ksd0NBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0VBQ0EsVUFBQTtFQUNBLG9CQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7VUFBQSxpQkFBQTtFQUNBLHVCQUFBO0VBQ0EsNkNBQUE7QUFESiIsInNvdXJjZXNDb250ZW50IjpbIiRiYWNrZ3JvdW5kLWNvbG9yXzQ6IHRyYW5zcGFyZW50O1xyXG5cclxuLnNjcmVlbiB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkYmFja2dyb3VuZC1jb2xvcl80ICFpbXBvcnRhbnQ7XHJcbiAgICB2aXNpYmlsaXR5OiBpbmhlcml0O1xyXG4gICAgd2lkdGg6IDE5MjBweDtcclxuICAgIGhlaWdodDogMTA4MHB4O1xyXG4gICAgei1pbmRleDogMTtcclxuICAgIHBvaW50ZXItZXZlbnRzOiBub25lO1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIHRvdWNoLWFjdGlvbjogbm9uZTtcclxuICAgIHVzZXItc2VsZWN0OiBub25lO1xyXG4gICAgLXdlYmtpdC11c2VyLWRyYWc6IG5vbmU7XHJcbiAgICAtd2Via2l0LXRhcC1oaWdobGlnaHQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMCk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;;AAOA,OAAM,MAAOA,uBAAuB;EAGlCC,YAAA,GAAe;EACfC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,MAAK;MAC9B,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACD,YAAY,EAAE;IACnB;IACAE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACH,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAA,YAAYA,CAAA;IACV,IAAII,MAAM,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAgB;IACxE;IACA;IACA,IAAIC,KAAK,GAAG,EAAE,GAAG,CAAC;IAClB,IAAIC,MAAM,GAAGJ,MAAM,CAACK,aAAa,CAACA,aAAa;IAC/C,IAAIC,WAAW,GAAGF,MAAM,CAACG,WAAW;IACpC,IAAIC,YAAY,GAAGJ,MAAM,CAACK,YAAY;IACtC,IAAIC,KAAK,GAAGJ,WAAW;IACvB,IAAIK,MAAM,GAAGH,YAAY;IACzB,IAAIE,KAAK,GAAGC,MAAM,GAAGR,KAAK,EAAE;MAC1BO,KAAK,GAAGC,MAAM,GAAGR,KAAK;KACvB,MAAM;MACLQ,MAAM,GAAGD,KAAK,GAAGP,KAAK;;IAExB,IAAIS,OAAO,GAAGF,KAAK,GAAG,IAAI;IAC1B,IAAIG,OAAO,GAAGF,MAAM,GAAG,IAAI;IAC3BX,MAAM,CAACc,KAAK,CAACC,SAAS,GAAG,SAASH,OAAO,KAAKC,OAAO,GAAG;IACxD;IACA,IAAIG,WAAW,GAAGhB,MAAM,CAACiB,qBAAqB,EAAE;IAChD,IAAIC,YAAY,GAAGF,WAAW,CAACN,KAAK;IACpC,IAAIS,aAAa,GAAGH,WAAW,CAACL,MAAM;IACtC,IAAIS,QAAQ,GAAGJ,WAAW,CAACK,CAAC;IAC5B,IAAIC,QAAQ,GAAGN,WAAW,CAACO,CAAC;IAC5BvB,MAAM,CAACc,KAAK,CAACU,eAAe,GAAG,SAAS;IACxC;IACA,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAG,CAAC;IAEZ,IAAIR,YAAY,GAAGZ,WAAW,EAAE;MAC9BoB,IAAI,GAAG,CAACpB,WAAW,GAAGY,YAAY,IAAI,CAAC;;IAGzC,IAAIC,aAAa,GAAGX,YAAY,EAAE;MAChCiB,GAAG,GAAG,CAACjB,YAAY,GAAGW,aAAa,IAAI,CAAC;;IAG1CnB,MAAM,CAACc,KAAK,CAACW,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC7BzB,MAAM,CAACc,KAAK,CAACY,IAAI,GAAG,GAAGA,IAAI,IAAI;EACjC;EAAC,QAAAC,CAAA;qBAzDUpC,uBAAuB;EAAA;EAAA,QAAAqC,EAAA;UAAvBrC,uBAAuB;IAAAsC,SAAA;IAAAC,MAAA;MAAApC,WAAA;IAAA;IAAAqC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPpCE,EAAA,CAAAC,cAAA,aAAoB;QAChBD,EAAA,CAAAE,SAAA,yBAAyC;QAG7CF,EAAA,CAAAG,YAAA,EAAM", "names": ["ScreenOverlaysComponent", "constructor", "ngOnInit", "resizeEvent", "subscribe", "resizeScreen", "ngAfterViewInit", "window", "addEventListener", "screen", "document", "getElementsByClassName", "ratio", "parent", "parentElement", "prent_width", "clientWidth", "prent_height", "clientHeight", "width", "height", "scale_w", "scale_h", "style", "transform", "screen_rect", "getBoundingClientRect", "screen_width", "screen_height", "screen_x", "x", "screen_y", "y", "transform<PERSON><PERSON>in", "top", "left", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ScreenOverlaysComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\screen-overlays\\screen-overlays.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\screen-overlays\\screen-overlays.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'screen-overlays',\r\n  templateUrl: './screen-overlays.component.html',\r\n  styleUrls: ['./screen-overlays.component.scss'],\r\n})\r\nexport class ScreenOverlaysComponent {\r\n  @Input() resizeEvent: EventEmitter<any>;\r\n\r\n  constructor() {}\r\n  ngOnInit() {\r\n    this.resizeEvent.subscribe(() => {\r\n      this.resizeScreen();\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.resizeScreen();\r\n    // listen for resize event\r\n    window.addEventListener('resize', () => {\r\n      this.resizeScreen();\r\n    });\r\n  }\r\n\r\n  resizeScreen() {\r\n    let screen = document.getElementsByClassName('screen')[0] as HTMLElement;\r\n    // resize screen to fit parent container\r\n    // scale screen to fit parent container\r\n    let ratio = 16 / 9;\r\n    let parent = screen.parentElement.parentElement;\r\n    let prent_width = parent.clientWidth;\r\n    let prent_height = parent.clientHeight;\r\n    let width = prent_width;\r\n    let height = prent_height;\r\n    if (width / height > ratio) {\r\n      width = height * ratio;\r\n    } else {\r\n      height = width / ratio;\r\n    }\r\n    let scale_w = width / 1920;\r\n    let scale_h = height / 1080;\r\n    screen.style.transform = `scale(${scale_w}, ${scale_h})`;\r\n    // get element position\r\n    let screen_rect = screen.getBoundingClientRect();\r\n    let screen_width = screen_rect.width;\r\n    let screen_height = screen_rect.height;\r\n    let screen_x = screen_rect.x;\r\n    let screen_y = screen_rect.y;\r\n    screen.style.transformOrigin = '0px 0px';\r\n    // calculate top and left position\r\n    let top = 0;\r\n    let left = 0;\r\n\r\n    if (screen_width < prent_width) {\r\n      left = (prent_width - screen_width) / 2;\r\n    }\r\n\r\n    if (screen_height < prent_height) {\r\n      top = (prent_height - screen_height) / 2;\r\n    }\r\n\r\n    screen.style.top = `${top}px`;\r\n    screen.style.left = `${left}px`;\r\n  }\r\n}\r\n", "<div class=\"screen\">\r\n    <overlay-scoreboard></overlay-scoreboard>\r\n    <sponsors-overlay></sponsors-overlay>\r\n    <srolling-text></srolling-text>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
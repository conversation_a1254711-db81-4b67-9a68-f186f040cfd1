{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as XLSX from 'xlsx';\nimport jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport { Filesystem, Directory } from '@capacitor/filesystem';\nimport { Share } from '@capacitor/share';\nimport { Capacitor } from '@capacitor/core';\nimport * as i0 from \"@angular/core\";\nexport class ExportService {\n  constructor() {}\n  exportExcel(data, fileName) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const header = data.header;\n        const body = data.body;\n        const worksheetData = [header, ...body];\n        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);\n        const workbook = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');\n        const excelBuffer = XLSX.write(workbook, {\n          bookType: 'xlsx',\n          type: 'array'\n        });\n        const blob = new Blob([excelBuffer], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        yield _this.handleFileExport(blob, fileName);\n      } catch (error) {\n        console.error('Error exporting Excel:', error);\n        alert('Error exporting Excel.');\n      }\n    })();\n  }\n  exportCsv(data, fileName) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const header = data.header;\n        const body = data.body;\n        const csvData = [header.join(','), ...body.map(row => row.join(','))].join('\\n');\n        const blob = new Blob([csvData], {\n          type: 'text/csv'\n        });\n        yield _this2.handleFileExport(blob, fileName);\n      } catch (error) {\n        console.error('Error exporting CSV:', error);\n        alert('Error exporting CSV.');\n      }\n    })();\n  }\n  exportPDF(data, fileName) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const header = data.header;\n        const body = data.body;\n        const doc = new jsPDF();\n        autoTable(doc, {\n          head: [header],\n          body: body\n        });\n        const pdfBlob = doc.output('blob');\n        yield _this3.handleFileExport(pdfBlob, fileName);\n      } catch (error) {\n        console.error('Error exporting PDF:', error);\n        alert('Error exporting PDF.');\n      }\n    })();\n  }\n  handleFileExport(blob, fileName) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (Capacitor.isNativePlatform()) {\n          const base64Data = yield _this4.convertBlobToBase64(blob);\n          const writeFileResult = yield Filesystem.writeFile({\n            path: fileName,\n            data: base64Data,\n            directory: Directory.Documents\n          });\n          yield Share.share({\n            title: 'Export File',\n            url: writeFileResult.uri,\n            dialogTitle: 'Share Exported File'\n          });\n          alert('File exported successfully!');\n        } else {\n          const url = window.URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = fileName;\n          document.body.appendChild(a);\n          a.click();\n          document.body.removeChild(a);\n          window.URL.revokeObjectURL(url);\n        }\n      } catch (error) {\n        console.error('Error handling file export:', error);\n        alert('Error handling file export.');\n      }\n    })();\n  }\n  convertBlobToBase64(blob) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(blob);\n      reader.onloadend = () => resolve(reader.result);\n      reader.onerror = reject;\n    });\n  }\n  static #_ = this.ɵfac = function ExportService_Factory(t) {\n    return new (t || ExportService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ExportService,\n    factory: ExportService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AACA,OAAO,KAAKA,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,UAAU,EAAEC,SAAS,QAAkB,uBAAuB;AACvE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,iBAAiB;;AAK3C,OAAM,MAAOC,aAAa;EAExBC,YAAA,GAAgB;EAEVC,WAAWA,CAACC,IAAS,EAAEC,QAAgB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC3C,IAAI;QACF,MAAMC,MAAM,GAAGJ,IAAI,CAACI,MAAM;QAC1B,MAAMC,IAAI,GAAGL,IAAI,CAACK,IAAI;QACtB,MAAMC,aAAa,GAAG,CAACF,MAAM,EAAE,GAAGC,IAAI,CAAC;QACvC,MAAME,SAAS,GAAGjB,IAAI,CAACkB,KAAK,CAACC,YAAY,CAACH,aAAa,CAAC;QACxD,MAAMI,QAAQ,GAAGpB,IAAI,CAACkB,KAAK,CAACG,QAAQ,EAAE;QACtCrB,IAAI,CAACkB,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,QAAQ,CAAC;QAC3D,MAAMM,WAAW,GAAGvB,IAAI,CAACwB,KAAK,CAACJ,QAAQ,EAAE;UAAEK,QAAQ,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAO,CAAE,CAAC;QAE7E,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,WAAW,CAAC,EAAE;UAAEG,IAAI,EAAE;QAAmE,CAAE,CAAC;QACnH,MAAMd,KAAI,CAACiB,gBAAgB,CAACF,IAAI,EAAEhB,QAAQ,CAAC;OAC5C,CAAC,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CE,KAAK,CAAC,wBAAwB,CAAC;;IAChC;EACH;EAEMC,SAASA,CAACvB,IAAS,EAAEC,QAAgB;IAAA,IAAAuB,MAAA;IAAA,OAAArB,iBAAA;MACzC,IAAI;QACF,MAAMC,MAAM,GAAGJ,IAAI,CAACI,MAAM;QAC1B,MAAMC,IAAI,GAAGL,IAAI,CAACK,IAAI;QACtB,MAAMoB,OAAO,GAAG,CAACrB,MAAM,CAACsB,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGrB,IAAI,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;QAEhF,MAAMT,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACO,OAAO,CAAC,EAAE;UAAET,IAAI,EAAE;QAAU,CAAE,CAAC;QACtD,MAAMQ,MAAI,CAACL,gBAAgB,CAACF,IAAI,EAAEhB,QAAQ,CAAC;OAC5C,CAAC,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CE,KAAK,CAAC,sBAAsB,CAAC;;IAC9B;EACH;EAEMO,SAASA,CAAC7B,IAAS,EAAEC,QAAgB;IAAA,IAAA6B,MAAA;IAAA,OAAA3B,iBAAA;MACzC,IAAI;QACF,MAAMC,MAAM,GAAGJ,IAAI,CAACI,MAAM;QAC1B,MAAMC,IAAI,GAAGL,IAAI,CAACK,IAAI;QAEtB,MAAM0B,GAAG,GAAG,IAAIxC,KAAK,EAAE;QACvBC,SAAS,CAACuC,GAAG,EAAE;UACbC,IAAI,EAAE,CAAC5B,MAAM,CAAC;UACdC,IAAI,EAAEA;SACP,CAAC;QAEF,MAAM4B,OAAO,GAAGF,GAAG,CAACG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAMJ,MAAI,CAACX,gBAAgB,CAACc,OAAO,EAAEhC,QAAQ,CAAC;OAC/C,CAAC,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CE,KAAK,CAAC,sBAAsB,CAAC;;IAC9B;EACH;EAEcH,gBAAgBA,CAACF,IAAU,EAAEhB,QAAgB;IAAA,IAAAkC,MAAA;IAAA,OAAAhC,iBAAA;MACzD,IAAI;QACF,IAAIP,SAAS,CAACwC,gBAAgB,EAAE,EAAE;UAChC,MAAMC,UAAU,SAASF,MAAI,CAACG,mBAAmB,CAACrB,IAAI,CAAC;UAEvD,MAAMsB,eAAe,SAAS9C,UAAU,CAAC+C,SAAS,CAAC;YACjDC,IAAI,EAAExC,QAAQ;YACdD,IAAI,EAAEqC,UAAU;YAChBK,SAAS,EAAEhD,SAAS,CAACiD;WACtB,CAAC;UAEF,MAAMhD,KAAK,CAACiD,KAAK,CAAC;YAChBC,KAAK,EAAE,aAAa;YACpBC,GAAG,EAAEP,eAAe,CAACQ,GAAG;YACxBC,WAAW,EAAE;WACd,CAAC;UAEF1B,KAAK,CAAC,6BAA6B,CAAC;SACrC,MAAM;UACL,MAAMwB,GAAG,GAAGG,MAAM,CAACC,GAAG,CAACC,eAAe,CAAClC,IAAI,CAAC;UAC5C,MAAMmC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGT,GAAG;UACZM,CAAC,CAACI,QAAQ,GAAGvD,QAAQ;UACrBoD,QAAQ,CAAChD,IAAI,CAACoD,WAAW,CAACL,CAAC,CAAC;UAC5BA,CAAC,CAACM,KAAK,EAAE;UACTL,QAAQ,CAAChD,IAAI,CAACsD,WAAW,CAACP,CAAC,CAAC;UAC5BH,MAAM,CAACC,GAAG,CAACU,eAAe,CAACd,GAAG,CAAC;;OAElC,CAAC,OAAO1B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDE,KAAK,CAAC,6BAA6B,CAAC;;IACrC;EACH;EAEQgB,mBAAmBA,CAACrB,IAAU;IACpC,OAAO,IAAI4C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,aAAa,CAACjD,IAAI,CAAC;MAC1B+C,MAAM,CAACG,SAAS,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAgB,CAAC;MACzDJ,MAAM,CAACK,OAAO,GAAGN,MAAM;IACzB,CAAC,CAAC;EACJ;EAAC,QAAAO,CAAA;qBAhGUzE,aAAa;EAAA;EAAA,QAAA0E,EAAA;WAAb1E,aAAa;IAAA2E,OAAA,EAAb3E,aAAa,CAAA4E,IAAA;IAAAC,UAAA,EAFZ;EAAM", "names": ["XLSX", "jsPDF", "autoTable", "Filesystem", "Directory", "Share", "Capacitor", "ExportService", "constructor", "exportExcel", "data", "fileName", "_this", "_asyncToGenerator", "header", "body", "worksheetData", "worksheet", "utils", "aoa_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "write", "bookType", "type", "blob", "Blob", "handleFileExport", "error", "console", "alert", "exportCsv", "_this2", "csvData", "join", "map", "row", "exportPDF", "_this3", "doc", "head", "pdfBlob", "output", "_this4", "isNativePlatform", "base64Data", "convertBlobToBase64", "writeFileResult", "writeFile", "path", "directory", "Documents", "share", "title", "url", "uri", "dialogTitle", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onloadend", "result", "onerror", "_", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\export.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport * as XLSX from 'xlsx';\r\nimport jsPDF from 'jspdf';\r\nimport autoTable from 'jspdf-autotable';\r\nimport { Filesystem, Directory, Encoding } from '@capacitor/filesystem';\r\nimport { Share } from '@capacitor/share';\r\nimport { Capacitor } from '@capacitor/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ExportService {\r\n\r\n  constructor() { }\r\n\r\n  async exportExcel(data: any, fileName: string): Promise<void> {\r\n    try {\r\n      const header = data.header;\r\n      const body = data.body;\r\n      const worksheetData = [header, ...body];\r\n      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');\r\n      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\r\n\r\n      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n      await this.handleFileExport(blob, fileName);\r\n    } catch (error) {\r\n      console.error('Error exporting Excel:', error);\r\n      alert('Error exporting Excel.');\r\n    }\r\n  }\r\n\r\n  async exportCsv(data: any, fileName: string): Promise<void> {\r\n    try {\r\n      const header = data.header;\r\n      const body = data.body;\r\n      const csvData = [header.join(','), ...body.map(row => row.join(','))].join('\\n');\r\n\r\n      const blob = new Blob([csvData], { type: 'text/csv' });\r\n      await this.handleFileExport(blob, fileName);\r\n    } catch (error) {\r\n      console.error('Error exporting CSV:', error);\r\n      alert('Error exporting CSV.');\r\n    }\r\n  }\r\n\r\n  async exportPDF(data: any, fileName: string): Promise<void> {\r\n    try {\r\n      const header = data.header;\r\n      const body = data.body;\r\n\r\n      const doc = new jsPDF();\r\n      autoTable(doc, {\r\n        head: [header],\r\n        body: body,\r\n      });\r\n\r\n      const pdfBlob = doc.output('blob');\r\n      await this.handleFileExport(pdfBlob, fileName);\r\n    } catch (error) {\r\n      console.error('Error exporting PDF:', error);\r\n      alert('Error exporting PDF.');\r\n    }\r\n  }\r\n\r\n  private async handleFileExport(blob: Blob, fileName: string): Promise<void> {\r\n    try {\r\n      if (Capacitor.isNativePlatform()) {\r\n        const base64Data = await this.convertBlobToBase64(blob);\r\n\r\n        const writeFileResult = await Filesystem.writeFile({\r\n          path: fileName,\r\n          data: base64Data,\r\n          directory: Directory.Documents\r\n        });\r\n\r\n        await Share.share({\r\n          title: 'Export File',\r\n          url: writeFileResult.uri,\r\n          dialogTitle: 'Share Exported File'\r\n        });\r\n\r\n        alert('File exported successfully!');\r\n      } else {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const a = document.createElement('a');\r\n        a.href = url;\r\n        a.download = fileName;\r\n        document.body.appendChild(a);\r\n        a.click();\r\n        document.body.removeChild(a);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling file export:', error);\r\n      alert('Error handling file export.');\r\n    }\r\n  }\r\n\r\n  private convertBlobToBase64(blob: Blob): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(blob);\r\n      reader.onloadend = () => resolve(reader.result as string);\r\n      reader.onerror = reject;\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
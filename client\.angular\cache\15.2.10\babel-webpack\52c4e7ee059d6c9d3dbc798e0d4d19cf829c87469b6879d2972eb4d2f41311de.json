{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\nimport LodashWrapper from './_LodashWrapper.js';\nimport reverse from './reverse.js';\nimport thru from './thru.js';\n\n/**\n * This method is the wrapper version of `_.reverse`.\n *\n * **Note:** This method mutates the wrapped array.\n *\n * @name reverse\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _(array).reverse().value()\n * // => [3, 2, 1]\n *\n * console.log(array);\n * // => [3, 2, 1]\n */\nfunction wrapperReverse() {\n  var value = this.__wrapped__;\n  if (value instanceof LazyWrapper) {\n    var wrapped = value;\n    if (this.__actions__.length) {\n      wrapped = new LazyWrapper(this);\n    }\n    wrapped = wrapped.reverse();\n    wrapped.__actions__.push({\n      'func': thru,\n      'args': [reverse],\n      'thisArg': undefined\n    });\n    return new LodashWrapper(wrapped, this.__chain__);\n  }\n  return this.thru(reverse);\n}\nexport default wrapperReverse;", "map": {"version": 3, "names": ["LazyWrapper", "LodashWrapper", "reverse", "thru", "wrapperReverse", "value", "__wrapped__", "wrapped", "__actions__", "length", "push", "undefined", "__chain__"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/wrapperReverse.js"], "sourcesContent": ["import LazyWrapper from './_LazyWrapper.js';\nimport LodashWrapper from './_LodashWrapper.js';\nimport reverse from './reverse.js';\nimport thru from './thru.js';\n\n/**\n * This method is the wrapper version of `_.reverse`.\n *\n * **Note:** This method mutates the wrapped array.\n *\n * @name reverse\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _(array).reverse().value()\n * // => [3, 2, 1]\n *\n * console.log(array);\n * // => [3, 2, 1]\n */\nfunction wrapperReverse() {\n  var value = this.__wrapped__;\n  if (value instanceof LazyWrapper) {\n    var wrapped = value;\n    if (this.__actions__.length) {\n      wrapped = new LazyWrapper(this);\n    }\n    wrapped = wrapped.reverse();\n    wrapped.__actions__.push({\n      'func': thru,\n      'args': [reverse],\n      'thisArg': undefined\n    });\n    return new LodashWrapper(wrapped, this.__chain__);\n  }\n  return this.thru(reverse);\n}\n\nexport default wrapperReverse;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAA,EAAG;EACxB,IAAIC,KAAK,GAAG,IAAI,CAACC,WAAW;EAC5B,IAAID,KAAK,YAAYL,WAAW,EAAE;IAChC,IAAIO,OAAO,GAAGF,KAAK;IACnB,IAAI,IAAI,CAACG,WAAW,CAACC,MAAM,EAAE;MAC3BF,OAAO,GAAG,IAAIP,WAAW,CAAC,IAAI,CAAC;IACjC;IACAO,OAAO,GAAGA,OAAO,CAACL,OAAO,CAAC,CAAC;IAC3BK,OAAO,CAACC,WAAW,CAACE,IAAI,CAAC;MACvB,MAAM,EAAEP,IAAI;MACZ,MAAM,EAAE,CAACD,OAAO,CAAC;MACjB,SAAS,EAAES;IACb,CAAC,CAAC;IACF,OAAO,IAAIV,aAAa,CAACM,OAAO,EAAE,IAAI,CAACK,SAAS,CAAC;EACnD;EACA,OAAO,IAAI,CAACT,IAAI,CAACD,OAAO,CAAC;AAC3B;AAEA,eAAeE,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport { DataTableDirective } from 'angular-datatables';\nimport { AssignNewCoachComponent } from '../assign-coaches/assign-new-coach/assign-new-coach.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"app/services/export.service\";\nimport * as i10 from \"angular-datatables\";\nconst _c0 = [\"modalValidator\"];\nexport class TeamCoachesComponent {\n  constructor(route, _router, _commonsService, _http, _translateService, renderer, _teamService, _modalService, _loadingService, _toastService, _exportService) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._translateService = _translateService;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this._exportService = _exportService;\n    this.onSubmitted = new EventEmitter();\n    this.dtOptions = {};\n    this.dtTrigger = {};\n    this.dtElement = DataTableDirective;\n    this.seasonId = this.route.snapshot.paramMap.get('seasonId');\n    this.teamId = this.route.snapshot.paramMap.get('teamId');\n    this.route.queryParams.subscribe(params => {\n      this.clubId = params['clubId'];\n      this.groupId = params['groupId'];\n    });\n  }\n  ngOnInit() {\n    $.fx.off = true; //this is for disable jquery animation\n    let team_coaches_url = `${environment.apiUrl}/teams/${this.teamId}/coaches`;\n    let params = {\n      season_id: this.seasonId,\n      club_id: this.clubId,\n      group_id: this.groupId\n    };\n    // this.dtOptions[0] = this.buildDtOptions1(current_season_coach_url, params, buttons_assign);\n    this.dtOptions = this.buildDtOptions(team_coaches_url, false);\n  }\n  buildDtOptions(url, params, buttons) {\n    var _this = this;\n    return {\n      dom: this._commonsService.dataTableDefaults.dom,\n      ajax: (dataTablesParameters, callback) => {\n        if (params) {\n          dataTablesParameters['season_id'] = parseInt(params.season_id);\n          dataTablesParameters['club_id'] = parseInt(params.club_id);\n          dataTablesParameters['group_id'] = parseInt(params.group_id);\n        }\n        this._http.post(`${url}`, dataTablesParameters).subscribe(resp => {\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      // fake data\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\n      displayLength: -1,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }, {\n        responsivePriority: 2,\n        targets: 0\n      }, {\n        responsivePriority: 3,\n        targets: 1\n      }],\n      columns: [{\n        // name\n        data: null,\n        className: 'font-weight-bolder',\n        render: (data, type, row) => {\n          const name_settings = JSON.parse(localStorage.getItem('name_settings'));\n          if (row.user.first_name && row.user.last_name) {\n            if (name_settings && name_settings.is_on == 1) {\n              return row.user.first_name + ' ' + row.user.last_name;\n            } else {\n              return row.user.last_name + ' ' + row.user.first_name;\n            }\n          } else {\n            return '';\n          }\n        }\n      }, {\n        data: 'user.email'\n      }, {\n        data: 'user.phone'\n      }, {\n        data: 'id',\n        render: (data, type, row) => {\n          return `<button class=\"btn btn-outline-danger btn-sm\" \n            data-row = '${JSON.stringify(row)}'\n            action=\"remove\">` + '<i class=\"fa-solid fa-xmark\"></i>&nbsp;' + this._translateService.instant('Remove') + `</button>`;\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"fa fa-user-plus\"></i> ' + this._translateService.instant('Assign Coach'),\n          titleAttr: this._translateService.instant('Assign Coach'),\n          action: () => this.assignCoachToTeam()\n        }, {\n          extend: 'excel',\n          text: `<i class=\"fa-regular fa-file-excel\" style=\"padding-right: 5px\"></i> ${this._translateService.instant('Export Excel')}`,\n          className: 'float-right mr-2',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              yield _this._exportService.exportExcel(data, 'TeamCoach.xlsx');\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  assignCoachToTeam() {\n    // open modal\n    const modalRef = this._modalService.open(AssignNewCoachComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      // click outside modal to close\n      centered: true,\n      keyboard: true\n      // close button\n    });\n\n    modalRef.componentInstance.params = {\n      team_id: this.team.id,\n      club_id: this.team.club_id\n    };\n    modalRef.result.then(result => {\n      // reload datatable\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }, reason => {\n      // reload datatable\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    });\n  }\n  editor(action, row) {\n    switch (action) {\n      case 'remove':\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          html: `\n        <div class=\"text-center\">\n          <img src=\"assets/images/alerts/are_you_sure.svg\" width=\"200px\" height=\"149px\">\n          <p class=\"text-center\">` + this._translateService.instant('ask_want_to_remove_coach') + `\n          </p>\n        </div>`,\n          reverseButtons: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          showCancelButton: true,\n          confirmButtonColor: '#d33',\n          cancelButtonColor: '#3085d6',\n          // cancel buton text color\n          cancelButtonText: this._translateService.instant('Cancel'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary mr-1',\n            cancelButton: 'btn btn-outline-primary mr-1'\n          }\n        }).then(result => {\n          if (result.isConfirmed) {\n            let row_id = row.id;\n            let user_id = row.user.id;\n            let params = new FormData();\n            params.append('action', 'remove');\n            params.append('data[' + row_id + '][team_id]', this.teamId);\n            params.append('data[' + row_id + '][user_id]', user_id);\n            this._teamService.editorTableTeamCoaches(params).subscribe(resp => {\n              if (resp) {\n                this._toastService.success(this._translateService.instant('Coach removed successfully'));\n              }\n              // reload\n              this.dtElement.dtInstance.then(dtInstance => {\n                dtInstance.ajax.reload();\n              });\n            }, err => {\n              Swal.fire({\n                title: 'Warning!',\n                icon: 'warning',\n                text: err.message,\n                confirmButtonText: this._translateService.instant('OK')\n              });\n            });\n          } else {\n            this.dtElement.dtInstance.then(dtInstance => {\n              dtInstance.ajax.reload();\n            });\n          }\n        });\n        break;\n      default:\n        break;\n    }\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('data-row')) {\n        let row = event.target.getAttribute('data-row');\n        row = JSON.parse(row);\n        this.editor(event.target.getAttribute('action'), row);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function TeamCoachesComponent_Factory(t) {\n    return new (t || TeamCoachesComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.ToastrService), i0.ɵɵdirectiveInject(i9.ExportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamCoachesComponent,\n    selectors: [[\"app-assign-coaches\"]],\n    viewQuery: function TeamCoachesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalValidator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      team: \"team\"\n    },\n    decls: 20,\n    vars: 13,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [1, \"text-capitalize\"]],\n    template: function TeamCoachesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"h4\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"table\", 5)(6, \"thead\")(7, \"tr\")(8, \"th\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"th\", 6);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"th\", 6);\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"th\", 6);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 5, \"Coach Name\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 7, \"Email\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 9, \"Phone\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 11, \"Action\"));\n      }\n    },\n    dependencies: [i10.DataTableDirective, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AACA,SAIEA,YAAY,QAMP,eAAe;AAMtB,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,kBAAkB,QAAQ,oBAAoB;AAGvD,SAASC,uBAAuB,QAAQ,+DAA+D;;;;;;;;;;;;;AASvG,OAAM,MAAOC,oBAAoB;EAe/BC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,iBAAmC,EACnCC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,aAA4B,EAC3BC,cAA6B;IAV7B,KAAAV,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IAxBxB,KAAAC,WAAW,GAAsB,IAAIlB,YAAY,EAAE;IAQnD,KAAAmB,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAQ,EAAE;IAGnB,KAAAC,SAAS,GAAQlB,kBAAkB;IAcjC,IAAI,CAACmB,QAAQ,GAAG,IAAI,CAACf,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC;IAC5D,IAAI,CAACC,MAAM,GAAG,IAAI,CAACnB,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,QAAQ,CAAC;IAExD,IAAI,CAAClB,KAAK,CAACoB,WAAW,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC1C,IAAI,CAACC,MAAM,GAAGD,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,CAACE,OAAO,GAAGF,MAAM,CAAC,SAAS,CAAC;IAClC,CAAC,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACNC,CAAC,CAACC,EAAE,CAACC,GAAG,GAAG,IAAI,CAAC,CAAC;IAEjB,IAAIC,gBAAgB,GAAG,GAAGnC,WAAW,CAACoC,MAAM,UAAU,IAAI,CAACX,MAAM,UAAU;IAC3E,IAAIG,MAAM,GAAG;MACXS,SAAS,EAAE,IAAI,CAAChB,QAAQ;MACxBiB,OAAO,EAAE,IAAI,CAACT,MAAM;MACpBU,QAAQ,EAAE,IAAI,CAACT;KAChB;IAED;IACA,IAAI,CAACZ,SAAS,GAAG,IAAI,CAACsB,cAAc,CAACL,gBAAgB,EAAE,KAAK,CAAC;EAC/D;EAEAK,cAAcA,CAACC,GAAG,EAAEb,MAAW,EAAEc,OAAe;IAAA,IAAAC,KAAA;IAC9C,OAAO;MACLC,GAAG,EAAE,IAAI,CAACpC,eAAe,CAACqC,iBAAiB,CAACD,GAAG;MAC/CE,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAIpB,MAAM,EAAE;UACVmB,oBAAoB,CAAC,WAAW,CAAC,GAAGE,QAAQ,CAACrB,MAAM,CAACS,SAAS,CAAC;UAC9DU,oBAAoB,CAAC,SAAS,CAAC,GAAGE,QAAQ,CAACrB,MAAM,CAACU,OAAO,CAAC;UAC1DS,oBAAoB,CAAC,UAAU,CAAC,GAAGE,QAAQ,CAACrB,MAAM,CAACW,QAAQ,CAAC;;QAE9D,IAAI,CAAC9B,KAAK,CACPyC,IAAI,CAAM,GAAGT,GAAG,EAAE,EAAEM,oBAAoB,CAAC,CACzCpB,SAAS,CAAEwB,IAAS,IAAI;UACvBH,QAAQ,CAAC;YACP;YACAI,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MAEDC,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACX;MACAC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAACnD,eAAe,CAACqC,iBAAiB,CAACe,IAAI;MACrDC,UAAU,EAAE,IAAI,CAACrD,eAAe,CAACqC,iBAAiB,CAACgB,UAAU;MAC7DC,aAAa,EAAE,CAAC,CAAC;MACjBC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,EACrC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,CACtC;MACDC,OAAO,EAAE,CACP;QACE;QACAZ,IAAI,EAAE,IAAI;QACVa,SAAS,EAAE,oBAAoB;QAC/BC,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1B,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;UACvE,IAAIL,GAAG,CAACM,IAAI,CAACC,UAAU,IAAIP,GAAG,CAACM,IAAI,CAACE,SAAS,EAAE;YAC7C,IAAIP,aAAa,IAAIA,aAAa,CAACQ,KAAK,IAAI,CAAC,EAAE;cAC7C,OAAOT,GAAG,CAACM,IAAI,CAACC,UAAU,GAAG,GAAG,GAAGP,GAAG,CAACM,IAAI,CAACE,SAAS;aACtD,MAAM;cACL,OAAOR,GAAG,CAACM,IAAI,CAACE,SAAS,GAAG,GAAG,GAAGR,GAAG,CAACM,IAAI,CAACC,UAAU;;WAExD,MAAM;YACL,OAAO,EAAE;;QAEb;OACD,EACD;QACEvB,IAAI,EAAE;OACP,EACD;QACEA,IAAI,EAAE;OACP,EACD;QACEA,IAAI,EAAE,IAAI;QACVc,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1B,OACE;0BACYE,IAAI,CAACQ,SAAS,CAACV,GAAG,CAAC;6BAChB,GACf,yCAAyC,GACzC,IAAI,CAAC5D,iBAAiB,CAACuE,OAAO,CAAC,QAAQ,CAAC,GACxC,WAAW;QAEf;OACD,CACF;MACDvC,OAAO,EAAE;QACPE,GAAG,EAAE,IAAI,CAACpC,eAAe,CAACqC,iBAAiB,CAACH,OAAO,CAACE,GAAG;QACvDF,OAAO,EAAE,CACP;UACEwC,IAAI,EACF,kCAAkC,GAClC,IAAI,CAACxE,iBAAiB,CAACuE,OAAO,CAAC,cAAc,CAAC;UAChDE,SAAS,EAAE,IAAI,CAACzE,iBAAiB,CAACuE,OAAO,CAAC,cAAc,CAAC;UACzDG,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,iBAAiB;SACrC,EACD;UACEC,MAAM,EAAE,OAAO;UACfJ,IAAI,EAAE,uEAAuE,IAAI,CAACxE,iBAAiB,CAACuE,OAAO,CACzG,cAAc,CACf,EAAE;UACHd,SAAS,EAAE,kBAAkB;UAC7BiB,MAAM;YAAA,IAAAG,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAMtC,IAAI,GAAGoC,EAAE,CAAChD,OAAO,CAACmD,UAAU,EAAE;cACpC,MAAMlD,KAAI,CAAC3B,cAAc,CAAC8E,WAAW,CAACxC,IAAI,EAAE,gBAAgB,CAAC;YAC/D,CAAC;YAAA,gBAAA8B,OAAAW,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAf,iBAAiBA,CAAA;IACf;IACA,MAAMgB,QAAQ,GAAG,IAAI,CAACxF,aAAa,CAACyF,IAAI,CAACnG,uBAAuB,EAAE;MAChEoG,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,QAAQ;MAClB;MACAC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;MACV;KACD,CAAC;;IAEFL,QAAQ,CAACM,iBAAiB,CAAC/E,MAAM,GAAG;MAClCgF,OAAO,EAAE,IAAI,CAACC,IAAI,CAACC,EAAE;MACrBxE,OAAO,EAAE,IAAI,CAACuE,IAAI,CAACvE;KACpB;IAED+D,QAAQ,CAACU,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAI,CAAC3F,SAAS,CAAC6F,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;QAC5DA,UAAU,CAACnE,IAAI,CAACoE,MAAM,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,EACAC,MAAM,IAAI;MACT;MACA,IAAI,CAAC/F,SAAS,CAAC6F,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;QAC5DA,UAAU,CAACnE,IAAI,CAACoE,MAAM,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CACF;EACH;EAEAE,MAAMA,CAAChC,MAAM,EAAEd,GAAG;IAChB,QAAQc,MAAM;MACZ,KAAK,QAAQ;QACXnF,IAAI,CAACoH,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAAC5G,iBAAiB,CAACuE,OAAO,CAAC,eAAe,CAAC;UACtDsC,IAAI,EACF;;;kCAGsB,GACtB,IAAI,CAAC7G,iBAAiB,CAACuE,OAAO,CAAC,0BAA0B,CAAC,GAC1D;;eAEG;UACLuC,cAAc,EAAE,IAAI;UAEpBC,iBAAiB,EAAE,IAAI,CAAC/G,iBAAiB,CAACuE,OAAO,CAAC,KAAK,CAAC;UACxDyC,gBAAgB,EAAE,IAAI;UACtBC,kBAAkB,EAAE,MAAM;UAC1BC,iBAAiB,EAAE,SAAS;UAC5B;UACAC,gBAAgB,EAAE,IAAI,CAACnH,iBAAiB,CAACuE,OAAO,CAAC,QAAQ,CAAC;UAC1D6C,cAAc,EAAE,KAAK;UACrBC,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCC,YAAY,EAAE;;SAEjB,CAAC,CAACjB,IAAI,CAAED,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACmB,WAAW,EAAE;YACtB,IAAIC,MAAM,GAAG7D,GAAG,CAACwC,EAAE;YACnB,IAAIsB,OAAO,GAAG9D,GAAG,CAACM,IAAI,CAACkC,EAAE;YACzB,IAAIlF,MAAM,GAAa,IAAIyG,QAAQ,EAAE;YACrCzG,MAAM,CAAC0G,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;YACjC1G,MAAM,CAAC0G,MAAM,CAAC,OAAO,GAAGH,MAAM,GAAG,YAAY,EAAE,IAAI,CAAC1G,MAAM,CAAC;YAC3DG,MAAM,CAAC0G,MAAM,CAAC,OAAO,GAAGH,MAAM,GAAG,YAAY,EAAEC,OAAO,CAAC;YAEvD,IAAI,CAACxH,YAAY,CAAC2H,sBAAsB,CAAC3G,MAAM,CAAC,CAACD,SAAS,CACvDwB,IAAS,IAAI;cACZ,IAAIA,IAAI,EAAE;gBACR,IAAI,CAACpC,aAAa,CAACyH,OAAO,CACxB,IAAI,CAAC9H,iBAAiB,CAACuE,OAAO,CAAC,4BAA4B,CAAC,CAC7D;;cAGH;cACA,IAAI,CAAC7D,SAAS,CAAC6F,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;gBAC5DA,UAAU,CAACnE,IAAI,CAACoE,MAAM,EAAE;cAC1B,CAAC,CAAC;YACJ,CAAC,EACAuB,GAAG,IAAI;cACNxI,IAAI,CAACoH,IAAI,CAAC;gBACRC,KAAK,EAAE,UAAU;gBACjBoB,IAAI,EAAE,SAAS;gBACfxD,IAAI,EAAEuD,GAAG,CAACE,OAAO;gBACjBlB,iBAAiB,EAAE,IAAI,CAAC/G,iBAAiB,CAACuE,OAAO,CAAC,IAAI;eACvD,CAAC;YACJ,CAAC,CACF;WACF,MAAM;YACL,IAAI,CAAC7D,SAAS,CAAC6F,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;cAC5DA,UAAU,CAACnE,IAAI,CAACoE,MAAM,EAAE;YAC1B,CAAC,CAAC;;QAEN,CAAC,CAAC;QACF;MAEF;QACE;;EAEN;EAEA0B,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAAClI,QAAQ,CAACmI,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGC,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;QACzC,IAAI3E,GAAG,GAAGyE,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,UAAU,CAAC;QAC/C5E,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;QAErB,IAAI,CAAC8C,MAAM,CAAC2B,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,QAAQ,CAAC,EAAE5E,GAAG,CAAC;;IAEzD,CAAC,CAAC;EACJ;EAAC,QAAA6E,CAAA;qBAtQU/I,oBAAoB,EAAAgJ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAW,SAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,QAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAAjB,EAAA,CAAAC,iBAAA,CAAAiB,EAAA,CAAAC,aAAA,GAAAnB,EAAA,CAAAC,iBAAA,CAAAmB,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAApBtK,oBAAoB;IAAAuK,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBAapB5K,kBAAkB;;;;;;;;;;;;;;;;QC7C/BkJ,EAAA,CAAA4B,cAAA,aAA+C;QAI/B5B,EAAA,CAAA6B,SAAA,YAA4B;QAChC7B,EAAA,CAAA8B,YAAA,EAAM;QAEN9B,EAAA,CAAA4B,cAAA,eAA+E;QAGvC5B,EAAA,CAAA+B,MAAA,GAA8B;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QAC/D9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAAyB;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QAC1D9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAAyB;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QAC1D9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAA0B;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;;;QANtD9B,EAAA,CAAAgC,SAAA,GAAuB;QAAvBhC,EAAA,CAAAiC,UAAA,cAAAN,GAAA,CAAA7J,SAAA,CAAuB;QAGAkI,EAAA,CAAAgC,SAAA,GAA8B;QAA9BhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,sBAA8B;QAC9BnC,EAAA,CAAAgC,SAAA,GAAyB;QAAzBhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,iBAAyB;QACzBnC,EAAA,CAAAgC,SAAA,GAAyB;QAAzBhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,iBAAyB;QACzBnC,EAAA,CAAAgC,SAAA,GAA0B;QAA1BhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,mBAA0B", "names": ["EventEmitter", "environment", "<PERSON><PERSON>", "DataTableDirective", "AssignNewCoachComponent", "TeamCoachesComponent", "constructor", "route", "_router", "_commonsService", "_http", "_translateService", "renderer", "_teamService", "_modalService", "_loadingService", "_toastService", "_exportService", "onSubmitted", "dtOptions", "dtTrigger", "dtElement", "seasonId", "snapshot", "paramMap", "get", "teamId", "queryParams", "subscribe", "params", "clubId", "groupId", "ngOnInit", "$", "fx", "off", "team_coaches_url", "apiUrl", "season_id", "club_id", "group_id", "buildDtOptions", "url", "buttons", "_this", "dom", "dataTableDefaults", "ajax", "dataTablesParameters", "callback", "parseInt", "post", "resp", "recordsTotal", "recordsFiltered", "data", "select", "rowId", "responsive", "scrollX", "language", "lang", "lengthMenu", "displayLength", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "type", "row", "name_settings", "JSON", "parse", "localStorage", "getItem", "user", "first_name", "last_name", "is_on", "stringify", "instant", "text", "titleAttr", "action", "assignCoachToTeam", "extend", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "exportExcel", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "modalRef", "open", "size", "backdrop", "centered", "keyboard", "componentInstance", "team_id", "team", "id", "result", "then", "dtInstance", "reload", "reason", "editor", "fire", "title", "html", "reverseButtons", "confirmButtonText", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "cancelButtonText", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "isConfirmed", "row_id", "user_id", "FormData", "append", "editorTableTeamCoaches", "success", "err", "icon", "message", "ngAfterViewInit", "unlistener", "listen", "event", "target", "hasAttribute", "getAttribute", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "CommonsService", "i3", "HttpClient", "i4", "TranslateService", "Renderer2", "i5", "TeamService", "i6", "NgbModal", "i7", "LoadingService", "i8", "ToastrService", "i9", "ExportService", "_2", "selectors", "viewQuery", "TeamCoachesComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "ɵɵpipeBind1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-coaches\\assign-coaches.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-coaches\\assign-coaches.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  AfterViewInit,\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Renderer2,\r\n  TemplateRef,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { environment } from 'environments/environment';\r\nimport Swal from 'sweetalert2';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { AssignNewCoachComponent } from '../assign-coaches/assign-new-coach/assign-new-coach.component';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ExportService } from 'app/services/export.service';\r\n\r\n@Component({\r\n  selector: 'app-assign-coaches',\r\n  templateUrl: './assign-coaches.component.html',\r\n  styleUrls: ['./assign-coaches.component.scss'],\r\n})\r\nexport class TeamCoachesComponent implements AfterViewInit, OnInit {\r\n  @Input() team: any;\r\n  onSubmitted: EventEmitter<any> = new EventEmitter();\r\n  public teamId: any;\r\n  private unlistener: () => void;\r\n\r\n  public seasonId: any;\r\n  public clubId: any;\r\n  public groupId: any;\r\n  @ViewChild('modalValidator') modalValidator: TemplateRef<any>;\r\n  dtOptions: any = {};\r\n  dtTrigger: any = {};\r\n  public modalRef: any;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _translateService: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _toastService: ToastrService,\r\n    private _exportService: ExportService\r\n  ) {\r\n    this.seasonId = this.route.snapshot.paramMap.get('seasonId');\r\n    this.teamId = this.route.snapshot.paramMap.get('teamId');\r\n\r\n    this.route.queryParams.subscribe((params) => {\r\n      this.clubId = params['clubId'];\r\n      this.groupId = params['groupId'];\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    $.fx.off = true; //this is for disable jquery animation\r\n\r\n    let team_coaches_url = `${environment.apiUrl}/teams/${this.teamId}/coaches`;\r\n    let params = {\r\n      season_id: this.seasonId,\r\n      club_id: this.clubId,\r\n      group_id: this.groupId,\r\n    };\r\n\r\n    // this.dtOptions[0] = this.buildDtOptions1(current_season_coach_url, params, buttons_assign);\r\n    this.dtOptions = this.buildDtOptions(team_coaches_url, false);\r\n  }\r\n\r\n  buildDtOptions(url, params: any, buttons?: any[]) {\r\n    return {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        if (params) {\r\n          dataTablesParameters['season_id'] = parseInt(params.season_id);\r\n          dataTablesParameters['club_id'] = parseInt(params.club_id);\r\n          dataTablesParameters['group_id'] = parseInt(params.group_id);\r\n        }\r\n        this._http\r\n          .post<any>(`${url}`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      // fake data\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\r\n      displayLength: -1,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: -1 },\r\n        { responsivePriority: 2, targets: 0 },\r\n        { responsivePriority: 3, targets: 1 },\r\n      ],\r\n      columns: [\r\n        {\r\n          // name\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          render: (data, type, row) => {\r\n            const name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n            if (row.user.first_name && row.user.last_name) {\r\n              if (name_settings && name_settings.is_on == 1) {\r\n                return row.user.first_name + ' ' + row.user.last_name;\r\n              } else {\r\n                return row.user.last_name + ' ' + row.user.first_name;\r\n              }\r\n            } else {\r\n              return '';\r\n            }\r\n          },\r\n        },\r\n        {\r\n          data: 'user.email',\r\n        },\r\n        {\r\n          data: 'user.phone',\r\n        },\r\n        {\r\n          data: 'id',\r\n          render: (data, type, row) => {\r\n            return (\r\n              `<button class=\"btn btn-outline-danger btn-sm\" \r\n            data-row = '${JSON.stringify(row)}'\r\n            action=\"remove\">` +\r\n              '<i class=\"fa-solid fa-xmark\"></i>&nbsp;' +\r\n              this._translateService.instant('Remove') +\r\n              `</button>`\r\n            );\r\n          },\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"fa fa-user-plus\"></i> ' +\r\n              this._translateService.instant('Assign Coach'),\r\n            titleAttr: this._translateService.instant('Assign Coach'),\r\n            action: () => this.assignCoachToTeam(),\r\n          },\r\n          {\r\n            extend: 'excel',\r\n            text: `<i class=\"fa-regular fa-file-excel\" style=\"padding-right: 5px\"></i> ${this._translateService.instant(\r\n              'Export Excel'\r\n            )}`,\r\n            className: 'float-right mr-2',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              await this._exportService.exportExcel(data, 'TeamCoach.xlsx');\r\n            }\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  assignCoachToTeam() {\r\n    // open modal\r\n    const modalRef = this._modalService.open(AssignNewCoachComponent, {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      // click outside modal to close\r\n      centered: true,\r\n      keyboard: true,\r\n      // close button\r\n    });\r\n\r\n    modalRef.componentInstance.params = {\r\n      team_id: this.team.id,\r\n      club_id: this.team.club_id,\r\n    };\r\n\r\n    modalRef.result.then(\r\n      (result) => {\r\n        // reload datatable\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n        });\r\n      },\r\n      (reason) => {\r\n        // reload datatable\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  editor(action, row) {\r\n    switch (action) {\r\n      case 'remove':\r\n        Swal.fire({\r\n          title: this._translateService.instant('Are you sure?'),\r\n          html:\r\n            `\r\n        <div class=\"text-center\">\r\n          <img src=\"assets/images/alerts/are_you_sure.svg\" width=\"200px\" height=\"149px\">\r\n          <p class=\"text-center\">` +\r\n            this._translateService.instant('ask_want_to_remove_coach') +\r\n            `\r\n          </p>\r\n        </div>`,\r\n          reverseButtons: true,\r\n\r\n          confirmButtonText: this._translateService.instant('Yes'),\r\n          showCancelButton: true,\r\n          confirmButtonColor: '#d33',\r\n          cancelButtonColor: '#3085d6',\r\n          // cancel buton text color\r\n          cancelButtonText: this._translateService.instant('Cancel'),\r\n          buttonsStyling: false,\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary mr-1',\r\n            cancelButton: 'btn btn-outline-primary mr-1',\r\n          },\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            let row_id = row.id;\r\n            let user_id = row.user.id;\r\n            let params: FormData = new FormData();\r\n            params.append('action', 'remove');\r\n            params.append('data[' + row_id + '][team_id]', this.teamId);\r\n            params.append('data[' + row_id + '][user_id]', user_id);\r\n\r\n            this._teamService.editorTableTeamCoaches(params).subscribe(\r\n              (resp: any) => {\r\n                if (resp) {\r\n                  this._toastService.success(\r\n                    this._translateService.instant('Coach removed successfully')\r\n                  );\r\n                }\r\n\r\n                // reload\r\n                this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n                  dtInstance.ajax.reload();\r\n                });\r\n              },\r\n              (err) => {\r\n                Swal.fire({\r\n                  title: 'Warning!',\r\n                  icon: 'warning',\r\n                  text: err.message,\r\n                  confirmButtonText: this._translateService.instant('OK'),\r\n                });\r\n              }\r\n            );\r\n          } else {\r\n            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n              dtInstance.ajax.reload();\r\n            });\r\n          }\r\n        });\r\n        break;\r\n\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n  \r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('data-row')) {\r\n        let row = event.target.getAttribute('data-row');\r\n        row = JSON.parse(row);\r\n\r\n        this.editor(event.target.getAttribute('action'), row);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <div class=\"content-body\">\r\n        <div class=\"card\">\r\n            <div class=\"card-header\">\r\n                <h4 class=\"card-title\"></h4>\r\n            </div>\r\n\r\n            <table datatable [dtOptions]=\"dtOptions\" class=\"table border row-border hover\">\r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"text-capitalize\">{{ 'Coach Name' | translate }}</th>\r\n                        <th class=\"text-capitalize\">{{ 'Email' | translate }}</th>\r\n                        <th class=\"text-capitalize\">{{ 'Phone' | translate }}</th>\r\n                        <th class=\"text-capitalize\">{{ 'Action' | translate }}</th>\r\n                    </tr>\r\n                </thead>\r\n            </table>\r\n        </div>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../overlays.service\";\nimport * as i2 from \"app/services/livekit.service\";\nimport * as i3 from \"app/services/world-time.service\";\nimport * as i4 from \"@angular/common/http\";\nexport class ScoreboardComponent {\n  constructor(overlaysService, livekitService, worldTimeService, http) {\n    this.overlaysService = overlaysService;\n    this.livekitService = livekitService;\n    this.worldTimeService = worldTimeService;\n    this.http = http;\n    this.period_scoreboard_time = 500;\n    this.period_hide_scoreboard = 400;\n    this.period_score_time = 300;\n    this.period_timer_time = 200;\n    this.period_stoppage_time = 200;\n    this.isShowScoreboard = false;\n    this.isShowStoppageTime = false;\n    this.isShowTimer = false;\n    this.time = 0;\n    this.server_time = 0;\n    worldTimeService.syncServerTime();\n  }\n  ngOnInit() {\n    this.initScoreBoard();\n    this.syncMetadata();\n  }\n  initScoreBoard() {\n    let scoreboard_container = document.getElementsByClassName('scoreboard_container')[0];\n    scoreboard_container.classList.add('d-none');\n    this.overlaysService.hideStoppageTime();\n    // this.overlaysService.hideTimer();\n    this.overlaysService.hideScoreboard();\n    // setTimeout(() => {\n    //   scoreboard_container.classList.remove('d-none');\n    //   this.overlaysService.toggleScoreboard();\n    // }, 1000);\n  }\n\n  ngAfterViewInit() {}\n  setPeriod(value) {\n    let perStr = '1st';\n    switch (value) {\n      case 1:\n        perStr = '1st';\n        break;\n      case 2:\n        perStr = '2nd';\n        break;\n      case 3:\n        perStr = '3rd';\n        break;\n      default:\n        perStr = `${value}th`;\n        break;\n    }\n    this.overlaysService.setPeriod(perStr);\n  }\n  syncMetadata() {\n    this.livekitService.roomMetadataSubject.subscribe(metadata => {\n      console.log('syncMetadata', metadata);\n      // check if metadata is changed\n      if (JSON.stringify(metadata) === JSON.stringify(this.currentRoomMetadata) || !metadata) {\n        console.log('metadata not changed');\n        return;\n      }\n      this.currentRoomMetadata = metadata;\n      let is_running = metadata.overlay_data.is_running_timer;\n      if (is_running) {\n        if (!this.interval) {\n          this.interval = setInterval(() => {\n            this.livekitService.syncTimerCountUp(metadata);\n          }, 200);\n        }\n      } else {\n        if (this.interval) {\n          clearInterval(this.interval);\n          this.interval = null;\n        }\n        this.livekitService.syncTimerCountUp(metadata);\n      }\n      this.overlaysService.homeScoreChange(this.currentRoomMetadata.match_info.home_team.score);\n      this.overlaysService.awayScoreChange(this.currentRoomMetadata.match_info.away_team.score);\n      this.overlaysService.setHomeLogo(this.currentRoomMetadata.match_info.home_team.logo);\n      this.overlaysService.setAwayLogo(this.currentRoomMetadata.match_info.away_team.logo);\n      this.overlaysService.setHomeTeamName(this.currentRoomMetadata.match_info.home_team.name);\n      this.overlaysService.setPrimaryColor(this.currentRoomMetadata.overlay_data.primary_color);\n      this.setPeriod(this.currentRoomMetadata.overlay_data.period);\n      console.log('secondary_color', this.currentRoomMetadata.overlay_data.secondary_color);\n      this.overlaysService.setSecondaryColor(this.currentRoomMetadata.overlay_data.secondary_color);\n      this.overlaysService.setAccentColor(this.currentRoomMetadata.overlay_data.accent_color);\n      this.overlaysService.setAwayTeamName(this.currentRoomMetadata.match_info.away_team.name);\n      this.overlaysService.setStoppageTime(this.currentRoomMetadata.overlay_data.stoppage_time);\n      let is_show_scoreboard = this.currentRoomMetadata.overlay_data.is_show;\n      let is_show_stoppage_time = this.currentRoomMetadata.overlay_data.is_show_stoppage_time;\n      if (!is_show_scoreboard || is_show_scoreboard == 'false') {\n        this.overlaysService.hideScoreboard();\n      } else {\n        this.overlaysService.showScoreboard();\n      }\n      if (!is_show_stoppage_time || is_show_stoppage_time == 'false') {\n        this.overlaysService.hideStoppageTime();\n      } else {\n        this.overlaysService.showStoppageTime();\n      }\n    });\n  }\n  getServerTime() {\n    // get time from https://worldtimeapi.org/api/timezone/Europe/London\n    return this.http.get('https://worldtimeapi.org/api/timezone/Europe/London').pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return Observable.throw(err);\n    }));\n  }\n  // sync server time and create interval to calculate time\n  syncServerTime() {\n    this.getServerTime().subscribe(res => {\n      console.log('server_time', res);\n      this.server_time = res.unixtime;\n      this.time = this.server_time;\n      this.interval = setInterval(() => {\n        this.time += 1;\n      }, 1000);\n    });\n  }\n  static #_ = this.ɵfac = function ScoreboardComponent_Factory(t) {\n    return new (t || ScoreboardComponent)(i0.ɵɵdirectiveInject(i1.OverlaysService), i0.ɵɵdirectiveInject(i2.LivekitService), i0.ɵɵdirectiveInject(i3.WorldTimeService), i0.ɵɵdirectiveInject(i4.HttpClient));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ScoreboardComponent,\n    selectors: [[\"overlay-scoreboard\"]],\n    decls: 38,\n    vars: 0,\n    consts: [[1, \"scoreboard_container\"], [\"id\", \"division\", 1, \"row\", \"mt-50\", \"mb-50\", \"d-none\"], [1, \"col-12\", \"text-center\", \"text-white\"], [\"id\", \"division-text\", 1, \"h3\", \"text-white\"], [1, \"row\", \"w-100\", \"p-0\", \"m-0\", \"scoreboard\"], [1, \"home_team\", \"col\", \"pt-0\", \"pb-0\", \"pl-0\", \"pr-50\", \"d-flex\", \"align-items-center\"], [1, \"logo\"], [\"src\", \"https://clipartcraft.com/images/transparent-circle-blue.png\", \"alt\", \"\", \"id\", \"home_logo\"], [1, \"flex-grow-1\"], [\"id\", \"home_name\", 1, \"name\", \"text-center\", \"text-nowrap\"], [1, \"scores\", \"col-3\", \"p-0\"], [1, \"score\", \"d-flex\", \"h-100\", \"p-0\", \"align-items-center\"], [1, \"home_score_wrapper\", \"col\", \"p-0\"], [\"id\", \"home_score\", 1, \"text-center\"], [1, \"away_score_wrapper\", \"col\", \"p-0\"], [\"id\", \"away_score\", 1, \"text-center\"], [1, \"timer_wrapper\", \"w-100\", \"h-75\"], [1, \"text-center\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"id\", \"period\", 1, \"h2\", \"mt-50\", \"mr-2\"], [\"id\", \"timer\", 1, \"h2\", \"mt-50\"], [1, \"stoppage-time-wraper\", \"h-75\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\", \"text-white\", \"mt-50\"], [\"id\", \"stoppage_time\"], [1, \"away_team\", \"col\", \"pt-0\", \"pb-0\", \"pl-50\", \"pr-0\", \"d-flex\", \"align-items-center\"], [\"id\", \"away_name\", 1, \"name\", \"text-center\", \"text-nowrap\"], [\"src\", \"https://clipartcraft.com/images/transparent-circle-blue.png\", \"alt\", \"\", \"id\", \"away_logo\"]],\n    template: function ScoreboardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n        i0.ɵɵtext(4, \"heheheheh\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6);\n        i0.ɵɵelement(8, \"img\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"h1\", 9);\n        i0.ɵɵtext(11, \"HOME\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"div\", 12)(15, \"h1\", 13);\n        i0.ɵɵtext(16, \"0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 14)(18, \"h1\", 15);\n        i0.ɵɵtext(19, \"0\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17)(22, \"span\", 18);\n        i0.ɵɵtext(23, \"1st\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"span\", 19);\n        i0.ɵɵtext(25, \"00:00\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"h3\", 22);\n        i0.ɵɵtext(29, \"+\");\n        i0.ɵɵelementStart(30, \"span\", 23);\n        i0.ɵɵtext(31, \"0\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(32, \"div\", 24)(33, \"div\", 8)(34, \"h1\", 25);\n        i0.ɵɵtext(35, \"AWAY\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"div\", 6);\n        i0.ɵɵelement(37, \"img\", 26);\n        i0.ɵɵelementEnd()()()();\n      }\n    },\n    styles: [\"@import url(https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;500&display=swap);/* SCORE BOARD CSS */\\n/* create variable */\\n:root {\\n  --scoreboard-primary-color: #000000;\\n  --scoreboard-secondary-color: #00898c;\\n  --scoreboard-accent-color: #ffffff;\\n}\\n\\n/* hide logo */\\n@keyframes home_score_out {\\n  0% {\\n    left: 150%;\\n  }\\n  100% {\\n    left: 50%;\\n  }\\n}\\n@keyframes home_score_in {\\n  0% {\\n    left: 50%;\\n  }\\n  100% {\\n    left: 150%;\\n  }\\n}\\n@keyframes away_score_out {\\n  0% {\\n    left: -50%;\\n  }\\n  100% {\\n    left: 50%;\\n  }\\n}\\n@keyframes away_score_in {\\n  0% {\\n    left: 50%;\\n  }\\n  100% {\\n    left: -50%;\\n  }\\n}\\n/* Scroll Scoreboard to the left */\\n@keyframes scroll_to_hide {\\n  0% {\\n    transform: translateX(0%);\\n  }\\n  100% {\\n    transform: translateX(-100%);\\n  }\\n}\\n/* Scroll Scoreboard to the right */\\n@keyframes scroll_to_show {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(0%);\\n  }\\n}\\n/* hide timer */\\n/* scroll timer to the top */\\n@keyframes hide_timer {\\n  0% {\\n    transform: translateY(0%);\\n  }\\n  100% {\\n    transform: translateY(-150%);\\n  }\\n}\\n/* scroll timer to the bottom */\\n@keyframes show_timer {\\n  0% {\\n    transform: translateY(-100%);\\n  }\\n  100% {\\n    transform: translateY(0%);\\n  }\\n}\\n/* hide stoppage time */\\n.screen h1 {\\n  margin: 0 !important;\\n}\\n.screen h2 {\\n  margin: 0 !important;\\n}\\n.screen h3 {\\n  margin: 0;\\n}\\n.screen .name {\\n  font-family: \\\"Noto Sans TC\\\", sans-serif;\\n  color: var(--scoreboard-accent-color);\\n}\\n.screen .away_team {\\n  height: 60px;\\n}\\n.screen #home-color {\\n  background-color: red;\\n  width: 10px;\\n  height: 100%;\\n}\\n.screen #away-color {\\n  background-color: blue;\\n  width: 10px;\\n  height: 100%;\\n}\\n.screen .scoreboard_container {\\n  position: absolute;\\n  font-family: \\\"Noto Sans TC\\\", sans-serif;\\n  width: 650px;\\n}\\n.screen .home_score_wrapper {\\n  position: absolute !important;\\n  border-right: 1px solid black;\\n  top: 0;\\n  width: 50% !important;\\n  height: 100%;\\n  overflow: hidden;\\n  background-color: var(--scoreboard-accent-color);\\n  z-index: 2;\\n}\\n.screen .away_score_wrapper {\\n  position: absolute !important;\\n  border-left: 1px solid black;\\n  left: 50%;\\n  top: 0;\\n  width: 50% !important;\\n  height: 100%;\\n  overflow: hidden;\\n  background-color: var(--scoreboard-accent-color);\\n  z-index: 2;\\n}\\n.screen #home_score {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n.screen #away_score {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n.screen .scoreboard {\\n  background-color: var(--scoreboard-primary-color);\\n}\\n.screen .scores {\\n  width: 25%;\\n  max-width: 160px !important;\\n  background-color: var(--scoreboard-accent-color);\\n}\\n.screen .timer_wrapper {\\n  font-family: \\\"Noto Sans TC\\\", sans-serif;\\n  position: absolute;\\n  z-index: 1;\\n  background-color: var(--scoreboard-accent-color);\\n}\\n.screen .timer_wrapper span {\\n  color: var(--scoreboard-primary-color) !important;\\n}\\n.screen .stoppage-time-wraper {\\n  font-family: \\\"Noto Sans TC\\\", sans-serif;\\n  position: absolute;\\n  background-color: var(--scoreboard-secondary-color);\\n  right: -50px;\\n  width: 50px;\\n  color: white;\\n}\\n.screen .score {\\n  color: var(--scoreboard-primary-color) !important;\\n  position: relative !important;\\n}\\n.screen .logo img {\\n  width: 60px;\\n  height: 60px;\\n}\\n.screen .split-score {\\n  width: 3px;\\n  height: 50px;\\n  margin: 0 10px;\\n  background-color: black;\\n}\\n.screen .home_score_in {\\n  animation: home_score_in 0.3s ease-in-out;\\n}\\n.screen .home_score_out {\\n  animation: home_score_out 0.3s ease-in-out;\\n}\\n.screen .away_score_in {\\n  animation: away_score_in 0.3s ease-in-out;\\n}\\n.screen .away_score_out {\\n  animation: away_score_out 0.3s ease-in-out;\\n}\\n.screen .hide_scoreboard {\\n  animation: scroll_to_hide 0.4s ease-in-out;\\n}\\n.screen .show_scoreboard {\\n  animation: scroll_to_show 0.4s ease-in-out;\\n}\\n.screen .hide_logo {\\n  animation: hide_logo 0.2s ease-in-out;\\n}\\n.screen .hide_timer {\\n  animation: hide_timer 0.2s ease-in-out;\\n}\\n.screen .show_timer {\\n  animation: show_timer 0.2s ease-in-out;\\n}\\n.screen .hide_stoppage_time {\\n  animation: scroll_to_hide 0.2s ease-in-out;\\n}\\n.screen .show_stoppage_time {\\n  animation: scroll_to_show 0.2s ease-in-out;\\n}\\n\\n.home_score_wrapper h1 {\\n  color: var(--scoreboard-primary-color) !important;\\n}\\n\\nh2 {\\n  color: var(--scoreboard-primary-color) !important;\\n  color: var(--scoreboard-primary-color) !important;\\n}\\n\\nh3 {\\n  color: var(--scoreboard-primary-color) !important;\\n  color: var(--scoreboard-primary-color) !important;\\n}\\n\\n.away_score_wrapper h1 {\\n  color: var(--scoreboard-primary-color) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAWA,SAASA,UAAU,QAAQ,MAAM;AAEjC,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;AAUhD,OAAM,MAAOC,mBAAmB;EAa9BC,YACSC,eAAgC,EAChCC,cAA8B,EAC9BC,gBAAkC,EAClCC,IAAgB;IAHhB,KAAAH,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,IAAI,GAAJA,IAAI;IAhBb,KAAAC,sBAAsB,GAAG,GAAG;IAC5B,KAAAC,sBAAsB,GAAG,GAAG;IAC5B,KAAAC,iBAAiB,GAAG,GAAG;IACvB,KAAAC,iBAAiB,GAAG,GAAG;IACvB,KAAAC,oBAAoB,GAAG,GAAG;IAC1B,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,WAAW,GAAG,KAAK;IAGnB,KAAAC,IAAI,GAAW,CAAC;IAChB,KAAAC,WAAW,GAAW,CAAC;IAOrBX,gBAAgB,CAACY,cAAc,EAAE;EACnC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAD,cAAcA,CAAA;IACZ,IAAIE,oBAAoB,GAAGC,QAAQ,CAACC,sBAAsB,CACxD,sBAAsB,CACvB,CAAC,CAAC,CAAC;IACJF,oBAAoB,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IAC5C,IAAI,CAACtB,eAAe,CAACuB,gBAAgB,EAAE;IACvC;IACA,IAAI,CAACvB,eAAe,CAACwB,cAAc,EAAE;IACrC;IACA;IACA;IACA;EACF;;EAEAC,eAAeA,CAAA,GAAI;EAEnBC,SAASA,CAACC,KAAK;IACb,IAAIC,MAAM,GAAG,KAAK;IAClB,QAAQD,KAAK;MACX,KAAK,CAAC;QACJC,MAAM,GAAG,KAAK;QACd;MACF,KAAK,CAAC;QACJA,MAAM,GAAG,KAAK;QACd;MACF,KAAK,CAAC;QACJA,MAAM,GAAG,KAAK;QACd;MACF;QACEA,MAAM,GAAG,GAAGD,KAAK,IAAI;QACrB;;IAEJ,IAAI,CAAC3B,eAAe,CAAC0B,SAAS,CAACE,MAAM,CAAC;EACxC;EAEAX,YAAYA,CAAA;IACV,IAAI,CAAChB,cAAc,CAAC4B,mBAAmB,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC7DC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,QAAQ,CAAC;MACrC;MACA,IACEG,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,KAAKG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,mBAAmB,CAAC,IACrE,CAACL,QAAQ,EACT;QACAC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QAEnC;;MAEF,IAAI,CAACG,mBAAmB,GAAGL,QAAQ;MACnC,IAAIM,UAAU,GAAGN,QAAQ,CAACO,YAAY,CAACC,gBAAgB;MACvD,IAAIF,UAAU,EAAE;QACd,IAAI,CAAC,IAAI,CAACG,QAAQ,EAAE;UAClB,IAAI,CAACA,QAAQ,GAAGC,WAAW,CAAC,MAAK;YAC/B,IAAI,CAACxC,cAAc,CAACyC,gBAAgB,CAACX,QAAQ,CAAC;UAChD,CAAC,EAAE,GAAG,CAAC;;OAEV,MAAM;QACL,IAAI,IAAI,CAACS,QAAQ,EAAE;UACjBG,aAAa,CAAC,IAAI,CAACH,QAAQ,CAAC;UAC5B,IAAI,CAACA,QAAQ,GAAG,IAAI;;QAEtB,IAAI,CAACvC,cAAc,CAACyC,gBAAgB,CAACX,QAAQ,CAAC;;MAEhD,IAAI,CAAC/B,eAAe,CAAC4C,eAAe,CAClC,IAAI,CAACR,mBAAmB,CAACS,UAAU,CAACC,SAAS,CAACC,KAAK,CACpD;MACD,IAAI,CAAC/C,eAAe,CAACgD,eAAe,CAClC,IAAI,CAACZ,mBAAmB,CAACS,UAAU,CAACI,SAAS,CAACF,KAAK,CACpD;MACD,IAAI,CAAC/C,eAAe,CAACkD,WAAW,CAC9B,IAAI,CAACd,mBAAmB,CAACS,UAAU,CAACC,SAAS,CAACK,IAAI,CACnD;MACD,IAAI,CAACnD,eAAe,CAACoD,WAAW,CAC9B,IAAI,CAAChB,mBAAmB,CAACS,UAAU,CAACI,SAAS,CAACE,IAAI,CACnD;MACD,IAAI,CAACnD,eAAe,CAACqD,eAAe,CAClC,IAAI,CAACjB,mBAAmB,CAACS,UAAU,CAACC,SAAS,CAACQ,IAAI,CACnD;MACD,IAAI,CAACtD,eAAe,CAACuD,eAAe,CAClC,IAAI,CAACnB,mBAAmB,CAACE,YAAY,CAACkB,aAAa,CACpD;MACD,IAAI,CAAC9B,SAAS,CAAC,IAAI,CAACU,mBAAmB,CAACE,YAAY,CAACmB,MAAM,CAAC;MAC5DzB,OAAO,CAACC,GAAG,CACT,iBAAiB,EACjB,IAAI,CAACG,mBAAmB,CAACE,YAAY,CAACoB,eAAe,CACtD;MAED,IAAI,CAAC1D,eAAe,CAAC2D,iBAAiB,CACpC,IAAI,CAACvB,mBAAmB,CAACE,YAAY,CAACoB,eAAe,CACtD;MACD,IAAI,CAAC1D,eAAe,CAAC4D,cAAc,CACjC,IAAI,CAACxB,mBAAmB,CAACE,YAAY,CAACuB,YAAY,CACnD;MACD,IAAI,CAAC7D,eAAe,CAAC8D,eAAe,CAClC,IAAI,CAAC1B,mBAAmB,CAACS,UAAU,CAACI,SAAS,CAACK,IAAI,CACnD;MACD,IAAI,CAACtD,eAAe,CAAC+D,eAAe,CAClC,IAAI,CAAC3B,mBAAmB,CAACE,YAAY,CAAC0B,aAAa,CACpD;MACD,IAAIC,kBAAkB,GAAG,IAAI,CAAC7B,mBAAmB,CAACE,YAAY,CAC3D4B,OAAc;MACjB,IAAIC,qBAAqB,GAAG,IAAI,CAAC/B,mBAAmB,CAACE,YAAY,CAC9D6B,qBAA4B;MAC/B,IAAI,CAACF,kBAAkB,IAAIA,kBAAkB,IAAI,OAAO,EAAE;QACxD,IAAI,CAACjE,eAAe,CAACwB,cAAc,EAAE;OACtC,MAAM;QACL,IAAI,CAACxB,eAAe,CAACoE,cAAc,EAAE;;MAEvC,IAAI,CAACD,qBAAqB,IAAIA,qBAAqB,IAAI,OAAO,EAAE;QAC9D,IAAI,CAACnE,eAAe,CAACuB,gBAAgB,EAAE;OACxC,MAAM;QACL,IAAI,CAACvB,eAAe,CAACqE,gBAAgB,EAAE;;IAE3C,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA,OAAO,IAAI,CAACnE,IAAI,CACboE,GAAG,CAAC,qDAAqD,CAAC,CAC1DC,IAAI,CACH5E,GAAG,CAAE6E,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACF5E,UAAU,CAAE6E,GAAG,IAAI;MACjB,OAAO/E,UAAU,CAACgF,KAAK,CAACD,GAAG,CAAC;IAC9B,CAAC,CAAC,CACH;EACL;EACA;EACA5D,cAAcA,CAAA;IACZ,IAAI,CAACwD,aAAa,EAAE,CAACxC,SAAS,CAAE2C,GAAQ,IAAI;MAC1CzC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEwC,GAAG,CAAC;MAC/B,IAAI,CAAC5D,WAAW,GAAG4D,GAAG,CAACG,QAAQ;MAC/B,IAAI,CAAChE,IAAI,GAAG,IAAI,CAACC,WAAW;MAC5B,IAAI,CAAC2B,QAAQ,GAAGC,WAAW,CAAC,MAAK;QAC/B,IAAI,CAAC7B,IAAI,IAAI,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EAAC,QAAAiE,CAAA;qBArKU/E,mBAAmB,EAAAgF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;UAAnB1F,mBAAmB;IAAA2F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvBhCjB,EAAA,CAAAmB,cAAA,aAAkC;QAGyBnB,EAAA,CAAAoB,MAAA,gBAAS;QAAApB,EAAA,CAAAqB,YAAA,EAAO;QAGvErB,EAAA,CAAAmB,cAAA,aAA0C;QAI9BnB,EAAA,CAAAsB,SAAA,aAA6F;QACjGtB,EAAA,CAAAqB,YAAA,EAAM;QACNrB,EAAA,CAAAmB,cAAA,aAAyB;QACmCnB,EAAA,CAAAoB,MAAA,YAAI;QAAApB,EAAA,CAAAqB,YAAA,EAAK;QAGzErB,EAAA,CAAAmB,cAAA,eAA8B;QAGsBnB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAK;QAElDrB,EAAA,CAAAmB,cAAA,eAAwC;QACInB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAK;QAGtDrB,EAAA,CAAAmB,cAAA,eAAsC;QAEUnB,EAAA,CAAAoB,MAAA,WAAG;QAAApB,EAAA,CAAAqB,YAAA,EAAO;QAClDrB,EAAA,CAAAmB,cAAA,gBAAkC;QAAAnB,EAAA,CAAAoB,MAAA,aAAK;QAAApB,EAAA,CAAAqB,YAAA,EAAO;QAGtDrB,EAAA,CAAAmB,cAAA,eAAuC;QAEUnB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAmB,cAAA,gBAAyB;QAAAnB,EAAA,CAAAoB,MAAA,SAAC;QAAApB,EAAA,CAAAqB,YAAA,EAAO;QAIvFrB,EAAA,CAAAmB,cAAA,eAA0E;QAETnB,EAAA,CAAAoB,MAAA,YAAI;QAAApB,EAAA,CAAAqB,YAAA,EAAK;QAEtErB,EAAA,CAAAmB,cAAA,cAAkB;QACdnB,EAAA,CAAAsB,SAAA,eAA6F;QACjGtB,EAAA,CAAAqB,YAAA,EAAM", "names": ["Observable", "map", "catchError", "ScoreboardComponent", "constructor", "overlaysService", "livekitService", "worldTimeService", "http", "period_scoreboard_time", "period_hide_scoreboard", "period_score_time", "period_timer_time", "period_stoppage_time", "isShowScoreboard", "isShowStoppageTime", "isShowTimer", "time", "server_time", "syncServerTime", "ngOnInit", "initScoreBoard", "syncMetadata", "scoreboard_container", "document", "getElementsByClassName", "classList", "add", "hideStoppageTime", "hideScoreboard", "ngAfterViewInit", "<PERSON><PERSON><PERSON><PERSON>", "value", "perStr", "roomMetadataSubject", "subscribe", "metadata", "console", "log", "JSON", "stringify", "currentRoomMetadata", "is_running", "overlay_data", "is_running_timer", "interval", "setInterval", "syncTimerCountUp", "clearInterval", "homeScoreChange", "match_info", "home_team", "score", "awayScoreChange", "away_team", "setHomeLogo", "logo", "setAwayLogo", "setHomeTeamName", "name", "setPrimaryColor", "primary_color", "period", "secondary_color", "setSecondaryColor", "setAccentColor", "accent_color", "setAwayTeamName", "setStoppageTime", "stoppage_time", "is_show_scoreboard", "is_show", "is_show_stoppage_time", "showScoreboard", "showStoppageTime", "getServerTime", "get", "pipe", "res", "err", "throw", "unixtime", "_", "i0", "ɵɵdirectiveInject", "i1", "OverlaysService", "i2", "LivekitService", "i3", "WorldTimeService", "i4", "HttpClient", "_2", "selectors", "decls", "vars", "consts", "template", "ScoreboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\scoreboard\\scoreboard.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\scoreboard\\scoreboard.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { OverlaysService } from '../overlays.service';\r\nimport { LivekitService, RoomMetadata } from 'app/services/livekit.service';\r\nimport moment from 'moment';\r\nimport { formatNumber } from '@angular/common';\r\nimport { Observable } from 'rxjs';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { map, catchError } from 'rxjs/operators';\r\nimport { WorldTimeService } from 'app/services/world-time.service';\r\ndeclare var window: any;\r\n\r\n@Component({\r\n  selector: 'overlay-scoreboard',\r\n  templateUrl: './scoreboard.component.html',\r\n  styleUrls: ['./scoreboard.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class ScoreboardComponent implements OnInit {\r\n  period_scoreboard_time = 500;\r\n  period_hide_scoreboard = 400;\r\n  period_score_time = 300;\r\n  period_timer_time = 200;\r\n  period_stoppage_time = 200;\r\n  isShowScoreboard = false;\r\n  isShowStoppageTime = false;\r\n  isShowTimer = false;\r\n  currentRoomMetadata: RoomMetadata;\r\n  interval: any;\r\n  time: number = 0;\r\n  server_time: number = 0;\r\n  constructor(\r\n    public overlaysService: OverlaysService,\r\n    public livekitService: LivekitService,\r\n    public worldTimeService: WorldTimeService,\r\n    public http: HttpClient\r\n  ) {\r\n    worldTimeService.syncServerTime();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initScoreBoard();\r\n    this.syncMetadata();\r\n  }\r\n\r\n  initScoreBoard() {\r\n    let scoreboard_container = document.getElementsByClassName(\r\n      'scoreboard_container'\r\n    )[0];\r\n    scoreboard_container.classList.add('d-none');\r\n    this.overlaysService.hideStoppageTime();\r\n    // this.overlaysService.hideTimer();\r\n    this.overlaysService.hideScoreboard();\r\n    // setTimeout(() => {\r\n    //   scoreboard_container.classList.remove('d-none');\r\n    //   this.overlaysService.toggleScoreboard();\r\n    // }, 1000);\r\n  }\r\n\r\n  ngAfterViewInit() {}\r\n\r\n  setPeriod(value) {\r\n    let perStr = '1st';\r\n    switch (value) {\r\n      case 1:\r\n        perStr = '1st';\r\n        break;\r\n      case 2:\r\n        perStr = '2nd';\r\n        break;\r\n      case 3:\r\n        perStr = '3rd';\r\n        break;\r\n      default:\r\n        perStr = `${value}th`;\r\n        break;\r\n    }\r\n    this.overlaysService.setPeriod(perStr);\r\n  }\r\n\r\n  syncMetadata() {\r\n    this.livekitService.roomMetadataSubject.subscribe((metadata) => {\r\n      console.log('syncMetadata', metadata);\r\n      // check if metadata is changed\r\n      if (\r\n        JSON.stringify(metadata) === JSON.stringify(this.currentRoomMetadata) ||\r\n        !metadata\r\n      ) {\r\n        console.log('metadata not changed');\r\n\r\n        return;\r\n      }\r\n      this.currentRoomMetadata = metadata;\r\n      let is_running = metadata.overlay_data.is_running_timer;\r\n      if (is_running) {\r\n        if (!this.interval) {\r\n          this.interval = setInterval(() => {\r\n            this.livekitService.syncTimerCountUp(metadata);\r\n          }, 200);\r\n        }\r\n      } else {\r\n        if (this.interval) {\r\n          clearInterval(this.interval);\r\n          this.interval = null;\r\n        }\r\n        this.livekitService.syncTimerCountUp(metadata);\r\n      }\r\n      this.overlaysService.homeScoreChange(\r\n        this.currentRoomMetadata.match_info.home_team.score\r\n      );\r\n      this.overlaysService.awayScoreChange(\r\n        this.currentRoomMetadata.match_info.away_team.score\r\n      );\r\n      this.overlaysService.setHomeLogo(\r\n        this.currentRoomMetadata.match_info.home_team.logo\r\n      );\r\n      this.overlaysService.setAwayLogo(\r\n        this.currentRoomMetadata.match_info.away_team.logo\r\n      );\r\n      this.overlaysService.setHomeTeamName(\r\n        this.currentRoomMetadata.match_info.home_team.name\r\n      );\r\n      this.overlaysService.setPrimaryColor(\r\n        this.currentRoomMetadata.overlay_data.primary_color\r\n      );\r\n      this.setPeriod(this.currentRoomMetadata.overlay_data.period);\r\n      console.log(\r\n        'secondary_color',\r\n        this.currentRoomMetadata.overlay_data.secondary_color\r\n      );\r\n\r\n      this.overlaysService.setSecondaryColor(\r\n        this.currentRoomMetadata.overlay_data.secondary_color\r\n      );\r\n      this.overlaysService.setAccentColor(\r\n        this.currentRoomMetadata.overlay_data.accent_color\r\n      );\r\n      this.overlaysService.setAwayTeamName(\r\n        this.currentRoomMetadata.match_info.away_team.name\r\n      );\r\n      this.overlaysService.setStoppageTime(\r\n        this.currentRoomMetadata.overlay_data.stoppage_time\r\n      );\r\n      let is_show_scoreboard = this.currentRoomMetadata.overlay_data\r\n        .is_show as any;\r\n      let is_show_stoppage_time = this.currentRoomMetadata.overlay_data\r\n        .is_show_stoppage_time as any;\r\n      if (!is_show_scoreboard || is_show_scoreboard == 'false') {\r\n        this.overlaysService.hideScoreboard();\r\n      } else {\r\n        this.overlaysService.showScoreboard();\r\n      }\r\n      if (!is_show_stoppage_time || is_show_stoppage_time == 'false') {\r\n        this.overlaysService.hideStoppageTime();\r\n      } else {\r\n        this.overlaysService.showStoppageTime();\r\n      }\r\n    });\r\n  }\r\n\r\n  getServerTime() {\r\n    // get time from https://worldtimeapi.org/api/timezone/Europe/London\r\n    return this.http\r\n      .get('https://worldtimeapi.org/api/timezone/Europe/London')\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return Observable.throw(err);\r\n        })\r\n      );\r\n  }\r\n  // sync server time and create interval to calculate time\r\n  syncServerTime() {\r\n    this.getServerTime().subscribe((res: any) => {\r\n      console.log('server_time', res);\r\n      this.server_time = res.unixtime;\r\n      this.time = this.server_time;\r\n      this.interval = setInterval(() => {\r\n        this.time += 1;\r\n      }, 1000);\r\n    });\r\n  }\r\n}\r\n", "<div class=\"scoreboard_container\">\r\n    <div class=\"row mt-50 mb-50 d-none\" id=\"division\">\r\n        <div class=\"col-12 text-center text-white\">\r\n            <span class=\"h3 text-white\" id=\"division-text\">heheheheh</span>\r\n        </div>\r\n    </div>\r\n    <div class=\"row w-100 p-0 m-0 scoreboard\">\r\n        <div class=\"home_team col pt-0 pb-0 pl-0 pr-50 d-flex align-items-center\">\r\n            <!-- <div id=\"home-color\"></div> -->\r\n            <div class=\"logo\">\r\n                <img src=\"https://clipartcraft.com/images/transparent-circle-blue.png\" alt=\"\" id=\"home_logo\">\r\n            </div>\r\n            <div class=\"flex-grow-1\">\r\n                <h1 class=\"name text-center text-nowrap\" id=\"home_name\">HOME</h1>\r\n            </div>\r\n        </div>\r\n        <div class=\"scores col-3 p-0\">\r\n            <div class=\"score d-flex h-100 p-0 align-items-center \">\r\n                <div class=\"home_score_wrapper col p-0\">\r\n                    <h1 class=\"text-center\" id=\"home_score\">0</h1>\r\n                </div>\r\n                <div class=\"away_score_wrapper col p-0\">\r\n                    <h1 class=\"text-center\" id=\"away_score\">0</h1>\r\n                </div>\r\n            </div>\r\n            <div class=\"timer_wrapper w-100 h-75\">\r\n                <div class=\"text-center d-flex align-items-center justify-content-center\">\r\n                    <span class=\"h2 mt-50 mr-2\" id=\"period\">1st</span>\r\n                    <span class=\"h2 mt-50\" id=\"timer\">00:00</span>\r\n                </div>\r\n            </div>\r\n            <div class=\"stoppage-time-wraper h-75\">\r\n                <div class=\"d-flex align-items-center justify-content-center\">\r\n                    <h3 class=\"text-center text-white mt-50\">+<span id=\"stoppage_time\">0</span></h3>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"away_team col pt-0 pb-0 pl-50 pr-0 d-flex align-items-center\">\r\n            <div class=\"flex-grow-1\">\r\n                <h1 class=\"name text-center  text-nowrap\" id=\"away_name\">AWAY</h1>\r\n            </div>\r\n            <div class=\"logo\">\r\n                <img src=\"https://clipartcraft.com/images/transparent-circle-blue.png\" alt=\"\" id=\"away_logo\">\r\n            </div>\r\n            <!-- <div id=\"away-color\"></div> -->\r\n        </div>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
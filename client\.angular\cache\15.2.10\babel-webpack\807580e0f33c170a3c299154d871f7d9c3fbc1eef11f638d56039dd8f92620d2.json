{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport { AppConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport Stepper from 'bs-stepper';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/settings.service\";\nimport * as i2 from \"app/services/auth.service\";\nimport * as i3 from \"app/services/commons.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"@angular/platform-browser\";\nimport * as i8 from \"app/services/send-messages.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i11 from \"app/layout/components/content-header/content-header.component\";\nimport * as i12 from \"@ngx-formly/core\";\n// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';\nexport class ContentsComponent {\n  constructor(settingsService, _authService, _commonsService, _trans, _toastrService, _loadingService, _titleService, _sendMessagesService) {\n    this.settingsService = settingsService;\n    this._authService = _authService;\n    this._commonsService = _commonsService;\n    this._trans = _trans;\n    this._toastrService = _toastrService;\n    this._loadingService = _loadingService;\n    this._titleService = _titleService;\n    this._sendMessagesService = _sendMessagesService;\n    this.AppConfig = AppConfig;\n    this.steperInit = false;\n    this.currentStep = 0;\n    this.policy_notification_form = new FormGroup({});\n    this.policy_notification_model = {};\n    this.policy_notification_options = {};\n    this.policy_notification_fields = [{\n      fieldGroupClassName: 'row',\n      fieldGroup: [\n      // show title and body\n      {\n        className: 'col-11',\n        type: 'input',\n        key: 'title',\n        props: {\n          pattern: '^(?![ ]+$).+',\n          translate: true,\n          label: 'Title',\n          required: true,\n          placeholder: 'Title'\n        }\n      }, {\n        className: 'col-12',\n        key: 'content',\n        type: 'ckeditor5',\n        props: {\n          required: true,\n          translate: true,\n          label: this._trans.instant('Content'),\n          config: {\n            toolbar: ['heading', '|', 'bold', 'underline', 'italic', 'link', '|', 'fontSize', 'fontColor', 'fontBackgroundColor', 'fontFamily', 'specialCharacters', 'removeFormat', 'findAndReplace', 'bulletedList', 'numberedList', 'todoList', '|', 'undo', 'redo', '|', 'outdent', 'indent', 'alignment', '|', 'imageInsert', 'blockQuote', 'insertTable'],\n            // simpleUpload: this._commonsService.simpleUploadConfig,\n            placeholder: 'Type the content here',\n            htmlSupport: {\n              allow: [{\n                name: /.*/,\n                attributes: false,\n                classes: false,\n                styles: false\n              }]\n            },\n            htmlEmbed: {\n              showPreviews: true\n            },\n            mention: {\n              feeds: [{\n                marker: '{',\n                minimumCharacters: 1\n              }]\n            }\n          }\n        }\n      }]\n    }];\n    this.about_form = new FormGroup({});\n    this.about_model = {};\n    this.about_options = {};\n    this.about_fields = [{\n      fieldGroupClassName: 'row',\n      fieldGroup: [\n      // show title and body\n      {\n        className: 'col-11',\n        type: 'input',\n        key: 'title',\n        props: {\n          pattern: '^(?![ ]+$).+',\n          translate: true,\n          label: 'Title',\n          required: true,\n          placeholder: 'Title'\n        }\n      }, {\n        className: 'col-12',\n        key: 'content',\n        type: 'ckeditor5',\n        props: {\n          required: true,\n          translate: true,\n          label: this._trans.instant('Content'),\n          config: {\n            toolbar: ['heading', '|', 'bold', 'underline', 'italic', 'link', '|', 'fontSize', 'fontColor', 'fontBackgroundColor', 'fontFamily', 'specialCharacters', 'removeFormat', 'findAndReplace', 'bulletedList', 'numberedList', 'todoList', '|', 'undo', 'redo', '|', 'outdent', 'indent', 'alignment', '|', 'imageInsert', 'blockQuote', 'insertTable'],\n            // simpleUpload: this._commonsService.simpleUploadConfig,\n            placeholder: 'Type the content here',\n            htmlSupport: {\n              allow: [{\n                name: /.*/,\n                attributes: false,\n                classes: false,\n                styles: false\n              }]\n            },\n            htmlEmbed: {\n              showPreviews: true\n            },\n            mention: {\n              feeds: [{\n                marker: '{',\n                minimumCharacters: 1\n              }]\n            }\n          }\n        }\n      }]\n    }];\n    // weather policy form\n    this.weather_form = new FormGroup({});\n    this.weather_model = {};\n    this.weather_options = {};\n    this.weather_fields = [{\n      fieldGroupClassName: 'row',\n      fieldGroup: [\n      // show title and body\n      {\n        className: 'col-11',\n        type: 'input',\n        key: 'title',\n        props: {\n          pattern: '^(?![ ]+$).+',\n          translate: true,\n          label: 'Title',\n          required: true,\n          placeholder: 'Title'\n        }\n      }, {\n        className: 'col-12',\n        key: 'content',\n        type: 'ckeditor5',\n        props: {\n          required: true,\n          translate: true,\n          label: this._trans.instant('Content'),\n          config: {\n            toolbar: ['heading', '|', 'bold', 'underline', 'italic', 'link', '|', 'fontSize', 'fontColor', 'fontBackgroundColor', 'fontFamily', 'specialCharacters', 'removeFormat', 'findAndReplace', 'bulletedList', 'numberedList', 'todoList', '|', 'undo', 'redo', '|', 'outdent', 'indent', 'alignment', '|', 'imageInsert', 'blockQuote', 'insertTable'],\n            // simpleUpload: this._commonsService.simpleUploadConfig,\n            placeholder: 'Type the content here',\n            htmlSupport: {\n              allow: [{\n                name: /.*/,\n                attributes: false,\n                classes: false,\n                styles: false\n              }]\n            },\n            htmlEmbed: {\n              showPreviews: true\n            },\n            mention: {\n              feeds: [{\n                marker: '{',\n                minimumCharacters: 1\n              }]\n            }\n          }\n        }\n      }]\n    }];\n    // code of conduct form\n    this.conduct_form = new FormGroup({});\n    this.conduct_model = {};\n    this.conduct_options = {};\n    this.conduct_fields = [{\n      fieldGroupClassName: 'row',\n      fieldGroup: [\n      // show title and body\n      {\n        className: 'col-11',\n        type: 'input',\n        key: 'title',\n        props: {\n          pattern: '^(?![ ]+$).+',\n          translate: true,\n          label: 'Title',\n          required: true,\n          placeholder: 'Title'\n        }\n      }, {\n        className: 'col-12',\n        key: 'content',\n        type: 'ckeditor5',\n        props: {\n          required: true,\n          translate: true,\n          label: this._trans.instant('Content'),\n          config: {\n            toolbar: ['heading', '|', 'bold', 'underline', 'italic', 'link', '|', 'fontSize', 'fontColor', 'fontBackgroundColor', 'fontFamily', 'specialCharacters', 'removeFormat', 'findAndReplace', 'bulletedList', 'numberedList', 'todoList', '|', 'undo', 'redo', '|', 'outdent', 'indent', 'alignment', '|', 'imageInsert', 'blockQuote', 'insertTable'],\n            // simpleUpload: this._commonsService.simpleUploadConfig,\n            placeholder: 'Type the content here',\n            htmlSupport: {\n              allow: [{\n                name: /.*/,\n                attributes: false,\n                classes: false,\n                styles: false\n              }]\n            },\n            htmlEmbed: {\n              showPreviews: true\n            },\n            mention: {\n              feeds: [{\n                marker: '{',\n                minimumCharacters: 1\n              }]\n            }\n          }\n        }\n      }]\n    }];\n    this._titleService.setTitle('Contents');\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Contents'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Settings',\n          isLink: false\n        }, {\n          name: 'Contents',\n          isLink: false\n        }]\n      }\n    };\n    _loadingService.show();\n    this.settingsService.getSettingsData().subscribe(res => {\n      this.settings = res;\n      // policy_notification model\n      let policy_notification = this.settings.find(element => element.key == AppConfig.SETTINGS_KEYS.POLICY_NOTIFICATION);\n      this.policy_notification_model = policy_notification.value;\n      // about model\n      let about = this.settings.find(element => element.key == AppConfig.SETTINGS_KEYS.ABOUT);\n      this.about_model = about.value;\n      // weather policy model\n      let weather_policy = this.settings.find(element => element.key == AppConfig.SETTINGS_KEYS.WEATHER);\n      this.weather_model = weather_policy.value;\n      // code of conduct model\n      let code_of_conduct = this.settings.find(element => element.key == AppConfig.SETTINGS_KEYS.CONDUCT);\n      this.conduct_model = code_of_conduct.value;\n      setTimeout(() => {\n        this.initStepper();\n      }, 1000);\n    });\n    if (!!this._authService.currentUserValue.role.permissions.find(x => x.id == AppConfig.PERMISSIONS.manage_settings)) {\n      this.settingsService.getSettings();\n    }\n  }\n  ngOnInit() {}\n  initStepper() {\n    if (this.verticalWizardStepper) {\n      this.steperInit = false;\n      this.verticalWizardStepper.destroy();\n    }\n    this.verticalWizardStepper = new Stepper(document.querySelector('#stepper2'), {\n      linear: false,\n      animation: true\n    });\n    this.verticalWizardStepper.to(this.currentStep);\n    // get #system-versions\n    const policy_notification = document.getElementById('policy_notification');\n    // remove class fade\n    policy_notification.classList.remove('fade');\n    this.steperInit = true;\n  }\n  saveSetting(key, model, form) {\n    if (form.invalid) {\n      return;\n    }\n    this.settingsService.updateSettings(key, model).subscribe(res => {\n      this._toastrService.success(this._trans.instant('Update successfully'), this._trans.instant('Success'), {\n        toastClass: 'toast ngx-toastr',\n        closeButton: true\n      });\n    }, err => {\n      Swal.fire({\n        icon: 'error',\n        title: this._trans.instant('Error'),\n        text: err.message\n      });\n    });\n  }\n  onChangeStep(step) {\n    this.currentStep = step;\n  }\n  static #_ = this.ɵfac = function ContentsComponent_Factory(t) {\n    return new (t || ContentsComponent)(i0.ɵɵdirectiveInject(i1.SettingsService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CommonsService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i8.SendMessagesService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ContentsComponent,\n    selectors: [[\"app-contents\"]],\n    decls: 109,\n    vars: 81,\n    consts: [[1, \"vertical-wizard\"], [3, \"contentHeader\"], [\"id\", \"stepper2\", 1, \"bs-stepper\", \"vertical\", \"vertical-wizard-example\"], [1, \"bs-stepper-header\"], [\"data-target\", \"#policy_notification\", 1, \"step\"], [\"type\", \"button\", 1, \"step-trigger\", 2, \"width\", \"100%\", 3, \"click\"], [1, \"bs-stepper-box\"], [1, \"bi\", \"bi-send-plus-fill\"], [1, \"bs-stepper-label\"], [1, \"bs-stepper-title\"], [1, \"bs-stepper-subtitle\"], [\"data-target\", \"#about\", 1, \"step\"], [1, \"bi\", \"bi-info-square\"], [\"data-target\", \"#weather\", 1, \"step\"], [1, \"bi\", \"bi-cloud-fog\"], [\"data-target\", \"#conduct\", 1, \"step\"], [1, \"bi\", \"bi-file-earmark-text\"], [1, \"bs-stepper-content\"], [\"id\", \"policy_notification\", 1, \"content\"], [1, \"content-header\"], [1, \"mb-0\"], [2, \"width\", \"99%\", 3, \"formGroup\", \"ngSubmit\"], [3, \"model\", \"fields\", \"options\", \"form\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\", \"btn-next\"], [1, \"align-middle\"], [\"id\", \"about\", 1, \"content\"], [\"id\", \"weather\", 1, \"content\"], [\"id\", \"conduct\", 1, \"content\"]],\n    template: function ContentsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵelement(1, \"app-content-header\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ContentsComponent_Template_button_click_5_listener() {\n          return ctx.onChangeStep(1);\n        });\n        i0.ɵɵelementStart(6, \"span\", 6);\n        i0.ɵɵelement(7, \"i\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"span\", 8)(9, \"span\", 9);\n        i0.ɵɵtext(10);\n        i0.ɵɵpipe(11, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"span\", 10);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(15, \"div\", 11)(16, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ContentsComponent_Template_button_click_16_listener() {\n          return ctx.onChangeStep(2);\n        });\n        i0.ɵɵelementStart(17, \"span\", 6);\n        i0.ɵɵelement(18, \"i\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"span\", 8)(20, \"span\", 9);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"span\", 10);\n        i0.ɵɵtext(24);\n        i0.ɵɵpipe(25, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(26, \"div\", 13)(27, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ContentsComponent_Template_button_click_27_listener() {\n          return ctx.onChangeStep(3);\n        });\n        i0.ɵɵelementStart(28, \"span\", 6);\n        i0.ɵɵelement(29, \"i\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"span\", 8)(31, \"span\", 9);\n        i0.ɵɵtext(32);\n        i0.ɵɵpipe(33, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"span\", 10);\n        i0.ɵɵtext(35);\n        i0.ɵɵpipe(36, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(37, \"div\", 15)(38, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ContentsComponent_Template_button_click_38_listener() {\n          return ctx.onChangeStep(4);\n        });\n        i0.ɵɵelementStart(39, \"span\", 6);\n        i0.ɵɵelement(40, \"i\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"span\", 8)(42, \"span\", 9);\n        i0.ɵɵtext(43);\n        i0.ɵɵpipe(44, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"span\", 10);\n        i0.ɵɵtext(46);\n        i0.ɵɵpipe(47, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(48, \"div\", 17)(49, \"div\", 18)(50, \"div\", 19)(51, \"h5\", 20);\n        i0.ɵɵtext(52);\n        i0.ɵɵpipe(53, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"small\");\n        i0.ɵɵtext(55);\n        i0.ɵɵpipe(56, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(57, \"form\", 21);\n        i0.ɵɵlistener(\"ngSubmit\", function ContentsComponent_Template_form_ngSubmit_57_listener() {\n          return ctx.saveSetting(ctx.AppConfig.SETTINGS_KEYS.POLICY_NOTIFICATION, ctx.policy_notification_model, ctx.policy_notification_form);\n        });\n        i0.ɵɵelement(58, \"formly-form\", 22);\n        i0.ɵɵelementStart(59, \"div\", 23)(60, \"button\", 24)(61, \"span\", 25);\n        i0.ɵɵtext(62);\n        i0.ɵɵpipe(63, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(64, \"div\", 26)(65, \"div\", 19)(66, \"h5\", 20);\n        i0.ɵɵtext(67);\n        i0.ɵɵpipe(68, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"small\");\n        i0.ɵɵtext(70);\n        i0.ɵɵpipe(71, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(72, \"form\", 21);\n        i0.ɵɵlistener(\"ngSubmit\", function ContentsComponent_Template_form_ngSubmit_72_listener() {\n          return ctx.saveSetting(ctx.AppConfig.SETTINGS_KEYS.ABOUT, ctx.about_model, ctx.about_form);\n        });\n        i0.ɵɵelement(73, \"formly-form\", 22);\n        i0.ɵɵelementStart(74, \"div\", 23)(75, \"button\", 24)(76, \"span\", 25);\n        i0.ɵɵtext(77);\n        i0.ɵɵpipe(78, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(79, \"div\", 27)(80, \"div\", 19)(81, \"h5\", 20);\n        i0.ɵɵtext(82);\n        i0.ɵɵpipe(83, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(84, \"small\");\n        i0.ɵɵtext(85);\n        i0.ɵɵpipe(86, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(87, \"form\", 21);\n        i0.ɵɵlistener(\"ngSubmit\", function ContentsComponent_Template_form_ngSubmit_87_listener() {\n          return ctx.saveSetting(ctx.AppConfig.SETTINGS_KEYS.WEATHER, ctx.weather_model, ctx.weather_form);\n        });\n        i0.ɵɵelement(88, \"formly-form\", 22);\n        i0.ɵɵelementStart(89, \"div\", 23)(90, \"button\", 24)(91, \"span\", 25);\n        i0.ɵɵtext(92);\n        i0.ɵɵpipe(93, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(94, \"div\", 28)(95, \"div\", 19)(96, \"h5\", 20);\n        i0.ɵɵtext(97);\n        i0.ɵɵpipe(98, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(99, \"small\");\n        i0.ɵɵtext(100);\n        i0.ɵɵpipe(101, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(102, \"form\", 21);\n        i0.ɵɵlistener(\"ngSubmit\", function ContentsComponent_Template_form_ngSubmit_102_listener() {\n          return ctx.saveSetting(ctx.AppConfig.SETTINGS_KEYS.CONDUCT, ctx.conduct_model, ctx.conduct_form);\n        });\n        i0.ɵɵelement(103, \"formly-form\", 22);\n        i0.ɵɵelementStart(104, \"div\", 23)(105, \"button\", 24)(106, \"span\", 25);\n        i0.ɵɵtext(107);\n        i0.ɵɵpipe(108, \"translate\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 41, \"PICS\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 43, \"Terms of Service and Privacy Policy\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 45, \"About\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(25, 47, \"About of HKJFL\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 49, \"Weather Policy\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(36, 51, \"Adverse weather procedures\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(44, 53, \"Code of conduct\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(47, 55, \"Code of conduct settings\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(53, 57, \"Terms & Conditions and Privacy Information Collection Statement\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(56, 59, \"Terms of Service and Privacy Policy settings\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.policy_notification_form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"model\", ctx.policy_notification_model)(\"fields\", ctx.policy_notification_fields)(\"options\", ctx.policy_notification_options)(\"form\", ctx.policy_notification_form);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(63, 61, \"Save\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(68, 63, \"About\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(71, 65, \"About of settings\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.about_form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"model\", ctx.about_model)(\"fields\", ctx.about_fields)(\"options\", ctx.about_options)(\"form\", ctx.about_form);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(78, 67, \"Save\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(83, 69, \"Weather Policy\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(86, 71, \"Adverse weather procedures settings\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.weather_form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"model\", ctx.weather_model)(\"fields\", ctx.weather_fields)(\"options\", ctx.weather_options)(\"form\", ctx.weather_form);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(93, 73, \"Save\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(98, 75, \"Code of conduct\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(101, 77, \"Code of conduct settings\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.conduct_form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"model\", ctx.conduct_model)(\"fields\", ctx.conduct_fields)(\"options\", ctx.conduct_options)(\"form\", ctx.conduct_form);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(108, 79, \"Save\"));\n      }\n    },\n    dependencies: [i9.ɵNgNoValidate, i9.NgControlStatusGroup, i9.FormGroupDirective, i10.RippleEffectDirective, i11.ContentHeaderComponent, i12.FormlyForm, i4.TranslatePipe],\n    styles: [\".bs-stepper[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]{display:inline-flex;flex-wrap:wrap;align-items:center;justify-content:center;padding:20px;font-size:1rem;font-weight:700;line-height:1.5;color:#6c757d;text-align:center;text-decoration:none;white-space:nowrap;vertical-align:middle;-webkit-user-select:none;user-select:none;background-color:transparent;border:none;border-radius:.25rem;transition:background-color .15s ease-out,color .15s ease-out}.bs-stepper[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]:not(:disabled):not(.disabled){cursor:pointer}.bs-stepper[_ngcontent-%COMP%]   .step-trigger.disabled[_ngcontent-%COMP%], .bs-stepper[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]:disabled{pointer-events:none;opacity:.65}.bs-stepper[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]:focus{color:#007bff;outline:0}.bs-stepper[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]:hover{text-decoration:none;background-color:rgba(0,0,0,.06)}@media (max-width:520px){.bs-stepper[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]{flex-direction:column;padding:10px}}.bs-stepper-label[_ngcontent-%COMP%]{display:inline-block;margin:.25rem}.bs-stepper-header[_ngcontent-%COMP%]{display:flex;align-items:center}@media (max-width:520px){.bs-stepper-header[_ngcontent-%COMP%]{margin:0 -10px;text-align:center}}.bs-stepper[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%], .bs-stepper-line[_ngcontent-%COMP%]{flex:1 0 32px;min-width:1px;min-height:1px;margin:auto;background-color:rgba(0,0,0,.12)}@media (max-width:400px){.bs-stepper[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%], .bs-stepper-line[_ngcontent-%COMP%]{flex-basis:20px}}.bs-stepper-circle[_ngcontent-%COMP%]{display:inline-flex;align-content:center;justify-content:center;width:2em;height:2em;padding:.5em 0;margin:.25rem;line-height:1em;color:#fff;background-color:#6c757d;border-radius:1em}.active[_ngcontent-%COMP%]   .bs-stepper-circle[_ngcontent-%COMP%]{background-color:#007bff}.bs-stepper-content[_ngcontent-%COMP%]{padding:0 20px 20px}@media (max-width:520px){.bs-stepper-content[_ngcontent-%COMP%]{padding:0}}.bs-stepper.vertical[_ngcontent-%COMP%]{display:flex}.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;margin:0}.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-pane[_ngcontent-%COMP%], .bs-stepper.vertical[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:block}.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-pane[_ngcontent-%COMP%]:not(.fade), .bs-stepper.vertical[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:not(.fade){display:block;visibility:hidden}.bs-stepper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:not(.fade), .bs-stepper-pane[_ngcontent-%COMP%]:not(.fade){display:none}.bs-stepper[_ngcontent-%COMP%]   .content.fade[_ngcontent-%COMP%], .bs-stepper-pane.fade[_ngcontent-%COMP%]{visibility:hidden;transition-duration:.3s;transition-property:opacity}.bs-stepper[_ngcontent-%COMP%]   .content.fade.active[_ngcontent-%COMP%], .bs-stepper-pane.fade.active[_ngcontent-%COMP%]{visibility:visible;opacity:1}.bs-stepper[_ngcontent-%COMP%]   .content.active[_ngcontent-%COMP%]:not(.fade), .bs-stepper-pane.active[_ngcontent-%COMP%]:not(.fade){display:block;visibility:visible}.bs-stepper[_ngcontent-%COMP%]   .content.dstepper-block[_ngcontent-%COMP%], .bs-stepper-pane.dstepper-block[_ngcontent-%COMP%]{display:block}.bs-stepper[_ngcontent-%COMP%]:not(.vertical)   .bs-stepper-pane.dstepper-none[_ngcontent-%COMP%], .bs-stepper[_ngcontent-%COMP%]:not(.vertical)   .content.dstepper-none[_ngcontent-%COMP%]{display:none}.vertical[_ngcontent-%COMP%]   .bs-stepper-pane.fade.dstepper-none[_ngcontent-%COMP%], .vertical[_ngcontent-%COMP%]   .content.fade.dstepper-none[_ngcontent-%COMP%]{visibility:hidden}\\r\\n\\n.ng-select.ng-select-opened[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]{background:#fff;border-color:#b3b3b3 #ccc #d9d9d9}.ng-select.ng-select-opened[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]:hover{box-shadow:none}.ng-select.ng-select-opened[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]   .ng-arrow[_ngcontent-%COMP%]{top:-2px;border-color:transparent transparent #999;border-width:0 5px 5px}.ng-select.ng-select-opened[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]   .ng-arrow[_ngcontent-%COMP%]:hover{border-color:transparent transparent #333}.ng-select.ng-select-opened.ng-select-bottom[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]{border-bottom-right-radius:0;border-bottom-left-radius:0}.ng-select.ng-select-opened.ng-select-top[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]{border-top-right-radius:0;border-top-left-radius:0}.ng-select.ng-select-focused[_ngcontent-%COMP%]:not(.ng-select-opened) > .ng-select-container[_ngcontent-%COMP%]{border-color:#007eff;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 0 3px rgba(0,126,255,0.1)}.ng-select.ng-select-disabled[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]{background-color:#f9f9f9}.ng-select[_ngcontent-%COMP%]   .ng-has-value[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%]{display:none}.ng-select[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]{color:#333;background-color:#fff;border-radius:4px;border:1px solid #ccc;min-height:36px;align-items:center}.ng-select[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]:hover{box-shadow:0 1px 0 rgba(0,0,0,0.06)}.ng-select[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]{align-items:center;padding-left:10px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]{padding-right:10px;padding-left:0}.ng-select[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%]{color:#999}.ng-select.ng-select-single[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]{height:36px}.ng-select.ng-select-single[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-input[_ngcontent-%COMP%]{top:5px;left:0;padding-left:10px;padding-right:50px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-single[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-input[_ngcontent-%COMP%]{padding-right:10px;padding-left:50px}.ng-select.ng-select-multiple.ng-select-disabled[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]{background-color:#f9f9f9;border:1px solid #e6e6e6}.ng-select.ng-select-multiple.ng-select-disabled[_ngcontent-%COMP%] > .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-label[_ngcontent-%COMP%]{padding:0 5px}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]{padding-top:5px;padding-left:7px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]{padding-right:7px;padding-left:0}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]{font-size:.9em;margin-bottom:5px;background-color:#ebf5ff;border-radius:2px;margin-right:5px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]{margin-right:0;margin-left:5px}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value.ng-value-disabled[_ngcontent-%COMP%]{background-color:#f9f9f9}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value.ng-value-disabled[_ngcontent-%COMP%]   .ng-value-label[_ngcontent-%COMP%]{padding-left:5px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value.ng-value-disabled[_ngcontent-%COMP%]   .ng-value-label[_ngcontent-%COMP%]{padding-left:0;padding-right:5px}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-label[_ngcontent-%COMP%]{display:inline-block;padding:1px 5px}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon[_ngcontent-%COMP%]{display:inline-block;padding:1px 5px}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon[_ngcontent-%COMP%]:hover{background-color:#d1e8ff}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.left[_ngcontent-%COMP%]{border-right:1px solid #b8dbff}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.left[_ngcontent-%COMP%]{border-left:1px solid #b8dbff;border-right:none}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.right[_ngcontent-%COMP%]{border-left:1px solid #b8dbff}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.right[_ngcontent-%COMP%]{border-left:0;border-right:1px solid #b8dbff}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-input[_ngcontent-%COMP%]{padding:0 0 3px 3px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-input[_ngcontent-%COMP%]{padding:0 3px 3px 0}.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%]{top:5px;padding-bottom:5px;padding-left:3px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value-container[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%]{padding-right:3px;padding-left:0}.ng-select[_ngcontent-%COMP%]   .ng-clear-wrapper[_ngcontent-%COMP%]{color:#999}.ng-select[_ngcontent-%COMP%]   .ng-clear-wrapper[_ngcontent-%COMP%]:hover   .ng-clear[_ngcontent-%COMP%]{color:#D0021B}.ng-select[_ngcontent-%COMP%]   .ng-spinner-zone[_ngcontent-%COMP%]{padding:5px 5px 0 0}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select[_ngcontent-%COMP%]   .ng-spinner-zone[_ngcontent-%COMP%]{padding:5px 0 0 5px}.ng-select[_ngcontent-%COMP%]   .ng-arrow-wrapper[_ngcontent-%COMP%]{width:25px;padding-right:5px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-select[_ngcontent-%COMP%]   .ng-arrow-wrapper[_ngcontent-%COMP%]{padding-left:5px;padding-right:0}.ng-select[_ngcontent-%COMP%]   .ng-arrow-wrapper[_ngcontent-%COMP%]:hover   .ng-arrow[_ngcontent-%COMP%]{border-top-color:#666}.ng-select[_ngcontent-%COMP%]   .ng-arrow-wrapper[_ngcontent-%COMP%]   .ng-arrow[_ngcontent-%COMP%]{border-color:#999 transparent transparent;border-style:solid;border-width:5px 5px 2.5px}.ng-dropdown-panel[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ccc;box-shadow:0 1px 0 rgba(0,0,0,0.06);left:0}.ng-dropdown-panel.ng-select-bottom[_ngcontent-%COMP%]{top:100%;border-bottom-right-radius:4px;border-bottom-left-radius:4px;border-top-color:#e6e6e6;margin-top:-1px}.ng-dropdown-panel.ng-select-bottom[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%]:last-child{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.ng-dropdown-panel.ng-select-top[_ngcontent-%COMP%]{bottom:100%;border-top-right-radius:4px;border-top-left-radius:4px;border-bottom-color:#e6e6e6;margin-bottom:-1px}.ng-dropdown-panel.ng-select-top[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%]:first-child{border-top-right-radius:4px;border-top-left-radius:4px}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-header[_ngcontent-%COMP%]{border-bottom:1px solid #ccc;padding:5px 7px}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-footer[_ngcontent-%COMP%]{border-top:1px solid #ccc;padding:5px 7px}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-optgroup[_ngcontent-%COMP%]{-webkit-user-select:none;user-select:none;padding:8px 10px;font-weight:500;color:rgba(0,0,0,0.54);cursor:pointer}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-optgroup.ng-option-disabled[_ngcontent-%COMP%]{cursor:default}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-optgroup.ng-option-marked[_ngcontent-%COMP%]{background-color:#f5faff}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-optgroup.ng-option-selected[_ngcontent-%COMP%], .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-optgroup.ng-option-selected.ng-option-marked[_ngcontent-%COMP%]{background-color:#ebf5ff;font-weight:600}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%]{background-color:#fff;color:rgba(0,0,0,0.87);padding:8px 10px}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-selected[_ngcontent-%COMP%], .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-selected.ng-option-marked[_ngcontent-%COMP%]{color:#333;background-color:#ebf5ff}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-selected[_ngcontent-%COMP%]   .ng-option-label[_ngcontent-%COMP%], .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-selected.ng-option-marked[_ngcontent-%COMP%]   .ng-option-label[_ngcontent-%COMP%]{font-weight:600}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-marked[_ngcontent-%COMP%]{background-color:#f5faff;color:#333}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-disabled[_ngcontent-%COMP%]{color:#ccc}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-child[_ngcontent-%COMP%]{padding-left:22px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option.ng-option-child[_ngcontent-%COMP%]{padding-right:22px;padding-left:0}.ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%]   .ng-tag-label[_ngcontent-%COMP%]{font-size:80%;font-weight:400;padding-right:5px}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%]   .ng-tag-label[_ngcontent-%COMP%]{padding-left:5px;padding-right:0}[dir=\\\"rtl\\\"][_ngcontent-%COMP%]   .ng-dropdown-panel[_ngcontent-%COMP%]{direction:rtl;text-align:right}\\n\\n.bs-stepper[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);\\n  border-radius: 0.5rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1.5rem;\\n  flex-wrap: wrap;\\n  border-bottom: 1px solid rgba(34, 41, 47, 0.08);\\n  margin: 0;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%] {\\n  flex: 0;\\n  min-width: auto;\\n  min-height: auto;\\n  background-color: transparent;\\n  margin: 0;\\n  padding: 0 1.75rem;\\n  color: #000;\\n  font-size: 1.5rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  margin-top: 0.25rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%] {\\n  padding: 0;\\n  flex-wrap: nowrap;\\n  font-weight: normal;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-box[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 38px;\\n  height: 38px;\\n  padding: 0.5em 0;\\n  font-weight: 500;\\n  color: #babfc7;\\n  background-color: rgba(186, 191, 199, 0.12);\\n  border-radius: 0.35rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%] {\\n  text-align: left;\\n  margin: 0;\\n  margin-top: 0.5rem;\\n  margin-left: 1rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-title[_ngcontent-%COMP%] {\\n  display: inherit;\\n  color: #000;\\n  font-weight: 600;\\n  line-height: 1rem;\\n  margin-bottom: 0rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-subtitle[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-size: 0.85rem;\\n  color: #b9b9c3;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-box[_ngcontent-%COMP%] {\\n  background-color: #1150b1;\\n  color: #fff;\\n  box-shadow: 0 3px 6px 0 rgba(17, 80, 177, 0.4);\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-title[_ngcontent-%COMP%] {\\n  color: #1150b1;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.crossed[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-box[_ngcontent-%COMP%] {\\n  background-color: rgba(17, 80, 177, 0.12);\\n  color: #1150b1 !important;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.crossed[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-title[_ngcontent-%COMP%] {\\n  color: #b9b9c3;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.crossed[_ngcontent-%COMP%]    + .line[_ngcontent-%COMP%] {\\n  color: #1150b1;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1.5rem;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n}\\n.bs-stepper[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ebe9f1;\\n  border-bottom: none;\\n}\\n.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%] {\\n  padding: 1rem 0;\\n}\\n.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding-top: 2.5rem;\\n}\\n.bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:not(.active) {\\n  display: none;\\n}\\n.bs-stepper.vertical.wizard-icons[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.bs-stepper.wizard-modern[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  box-shadow: none;\\n}\\n.bs-stepper.wizard-modern[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.bs-stepper.wizard-modern[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);\\n}\\n\\n.horizontal-wizard[_ngcontent-%COMP%], .vertical-wizard[_ngcontent-%COMP%], .modern-horizontal-wizard[_ngcontent-%COMP%], .modern-vertical-wizard[_ngcontent-%COMP%] {\\n  margin-bottom: 2.2rem;\\n}\\n\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%] {\\n  background-color: #283046;\\n  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(59, 66, 83, 0.08);\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%] {\\n  color: #b4b7bd;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-box[_ngcontent-%COMP%] {\\n  color: #babfc7;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-title[_ngcontent-%COMP%] {\\n  color: #b4b7bd;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-subtitle[_ngcontent-%COMP%] {\\n  color: #676d7d;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-box[_ngcontent-%COMP%] {\\n  background-color: #1150b1;\\n  color: #fff;\\n  box-shadow: 0 3px 6px 0 rgba(17, 80, 177, 0.4);\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%]   .bs-stepper-title[_ngcontent-%COMP%] {\\n  color: #1150b1;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.crossed[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-label[_ngcontent-%COMP%], .dark-layout[_ngcontent-%COMP%]   .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step.crossed[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%]   .bs-stepper-title[_ngcontent-%COMP%] {\\n  color: #676d7d;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n  border-right-color: #3b4253;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper.wizard-modern[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  box-shadow: none;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper.wizard-modern[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bs-stepper.wizard-modern[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%] {\\n  background-color: #283046;\\n  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);\\n}\\n\\nhtml[data-textdirection=rtl][_ngcontent-%COMP%]   .btn-prev[_ngcontent-%COMP%], html[data-textdirection=rtl][_ngcontent-%COMP%]   .btn-next[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\nhtml[data-textdirection=rtl][_ngcontent-%COMP%]   .btn-prev[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], html[data-textdirection=rtl][_ngcontent-%COMP%]   .btn-prev[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], html[data-textdirection=rtl][_ngcontent-%COMP%]   .btn-next[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], html[data-textdirection=rtl][_ngcontent-%COMP%]   .btn-next[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transform: rotate(-180deg);\\n}\\n\\n@media (max-width: 992px) {\\n  .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-trigger[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0 !important;\\n    flex-direction: row;\\n  }\\n  .bs-stepper[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .bs-stepper.vertical[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-header[_ngcontent-%COMP%] {\\n    align-items: flex-start;\\n  }\\n  .bs-stepper.vertical[_ngcontent-%COMP%]   .bs-stepper-content[_ngcontent-%COMP%] {\\n    padding-top: 1.5rem;\\n  }\\n}\\n.bg-white[_ngcontent-%COMP%] {\\n  background-color: #ffffff !important;\\n}\\n.bg-white[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-white[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.border-white[_ngcontent-%COMP%] {\\n  border: 1px solid #ffffff !important;\\n}\\n\\n.border-top-white[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffffff;\\n}\\n\\n.border-bottom-white[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffffff;\\n}\\n\\n.border-left-white[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffffff;\\n}\\n\\n.border-right-white[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffffff;\\n}\\n\\n.bg-white.badge-glow[_ngcontent-%COMP%], .border-white.badge-glow[_ngcontent-%COMP%], .badge-white.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #ffffff;\\n}\\n\\n.overlay-white[_ngcontent-%COMP%] {\\n  background: #ffffff; \\n  background: rgba(255, 255, 255, 0.6);\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-white[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #ffffff !important;\\n}\\n\\n.bg-black[_ngcontent-%COMP%] {\\n  background-color: #000000 !important;\\n}\\n.bg-black[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-black[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.border-black[_ngcontent-%COMP%] {\\n  border: 1px solid #000000 !important;\\n}\\n\\n.border-top-black[_ngcontent-%COMP%] {\\n  border-top: 1px solid #000000;\\n}\\n\\n.border-bottom-black[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #000000;\\n}\\n\\n.border-left-black[_ngcontent-%COMP%] {\\n  border-left: 1px solid #000000;\\n}\\n\\n.border-right-black[_ngcontent-%COMP%] {\\n  border-right: 1px solid #000000;\\n}\\n\\n.bg-black.badge-glow[_ngcontent-%COMP%], .border-black.badge-glow[_ngcontent-%COMP%], .badge-black.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #000000;\\n}\\n\\n.overlay-black[_ngcontent-%COMP%] {\\n  background: #000000; \\n  background: rgba(0, 0, 0, 0.6);\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-black[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #000000 !important;\\n}\\n\\n.bg-dark[_ngcontent-%COMP%] {\\n  background-color: #4b4b4b !important;\\n}\\n.bg-dark[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-dark[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-dark[_ngcontent-%COMP%] {\\n  background: rgba(75, 75, 75, 0.12) !important;\\n  color: #4b4b4b !important;\\n}\\n.alert-dark[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(75, 75, 75, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-dark[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #3e3e3e !important;\\n}\\n.alert-dark[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #4b4b4b !important;\\n}\\n\\n.border-dark[_ngcontent-%COMP%] {\\n  border: 1px solid #4b4b4b !important;\\n}\\n\\n.border-top-dark[_ngcontent-%COMP%] {\\n  border-top: 1px solid #4b4b4b;\\n}\\n\\n.border-bottom-dark[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #4b4b4b;\\n}\\n\\n.border-left-dark[_ngcontent-%COMP%] {\\n  border-left: 1px solid #4b4b4b;\\n}\\n\\n.border-right-dark[_ngcontent-%COMP%] {\\n  border-right: 1px solid #4b4b4b;\\n}\\n\\n.bg-dark.badge-glow[_ngcontent-%COMP%], .border-dark.badge-glow[_ngcontent-%COMP%], .badge-dark.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #4b4b4b;\\n}\\n\\n.badge.badge-light-dark[_ngcontent-%COMP%] {\\n  background-color: rgba(75, 75, 75, 0.12);\\n  color: #4b4b4b !important;\\n}\\n\\n.overlay-dark[_ngcontent-%COMP%] {\\n  background: #4b4b4b; \\n  background: rgba(75, 75, 75, 0.6);\\n}\\n\\n.btn-dark[_ngcontent-%COMP%] {\\n  border-color: #4b4b4b !important;\\n  background-color: #4b4b4b !important;\\n  color: #fff !important;\\n}\\n.btn-dark[_ngcontent-%COMP%]:focus, .btn-dark[_ngcontent-%COMP%]:active, .btn-dark.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #343434 !important;\\n}\\n.btn-dark[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #4b4b4b;\\n}\\n.btn-dark[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-dark[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #4b4b4b;\\n}\\n.btn-flat-dark[_ngcontent-%COMP%]:hover {\\n  color: #4b4b4b;\\n}\\n.btn-flat-dark[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(75, 75, 75, 0.12);\\n}\\n.btn-flat-dark[_ngcontent-%COMP%]:active, .btn-flat-dark.active[_ngcontent-%COMP%], .btn-flat-dark[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(75, 75, 75, 0.2);\\n  color: #4b4b4b;\\n}\\n.btn-flat-dark.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234b4b4b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-dark[_ngcontent-%COMP%] {\\n  background-color: #4b4b4b;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-dark[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #626262;\\n}\\n.btn-relief-dark[_ngcontent-%COMP%]:active, .btn-relief-dark.active[_ngcontent-%COMP%], .btn-relief-dark[_ngcontent-%COMP%]:focus {\\n  background-color: #343434;\\n}\\n.btn-relief-dark[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-dark[_ngcontent-%COMP%]:active, .btn-relief-dark.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-dark[_ngcontent-%COMP%] {\\n  border: 1px solid #4b4b4b !important;\\n  background-color: transparent;\\n  color: #4b4b4b;\\n}\\n.btn-outline-dark[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(75, 75, 75, 0.04);\\n  color: #4b4b4b;\\n}\\n.btn-outline-dark[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-dark[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-dark[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-dark[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(75, 75, 75, 0.2);\\n  color: #4b4b4b;\\n}\\n.btn-outline-dark.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234b4b4b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-dark.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(75, 75, 75, 0.2);\\n  color: #4b4b4b;\\n}\\n\\n.btn-outline-dark.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-dark.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(75, 75, 75, 0.2) 0, rgba(75, 75, 75, 0.3) 40%, rgba(75, 75, 75, 0.4) 50%, rgba(75, 75, 75, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-dark[_ngcontent-%COMP%] {\\n  background-color: #4b4b4b;\\n}\\n\\n.modal.modal-dark[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #4b4b4b;\\n}\\n.modal.modal-dark[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #4b4b4b !important;\\n}\\n\\n.progress-bar-dark[_ngcontent-%COMP%] {\\n  background-color: rgba(75, 75, 75, 0.12);\\n}\\n.progress-bar-dark[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #4b4b4b;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-dark[_ngcontent-%COMP%] {\\n  border-color: #4b4b4b !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-dark[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-dark[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #4b4b4b !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-dark.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #4b4b4b !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-dark.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(75, 75, 75, 0.12) !important;\\n}\\n\\n.divider.divider-dark[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-dark[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #4b4b4b !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-dark[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #4b4b4b !important;\\n}\\n\\n.custom-control-dark[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-dark[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #4b4b4b;\\n  background-color: #4b4b4b;\\n}\\n.custom-control-dark.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-dark.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-dark.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-dark.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-dark.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-dark.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(75, 75, 75, 0.4) !important;\\n}\\n.custom-control-dark[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(75, 75, 75, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-dark[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #4b4b4b !important;\\n}\\n\\n.custom-switch-dark[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #4b4b4b !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-dark[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #4b4b4b !important;\\n  border-color: #4b4b4b !important;\\n}\\n\\n.text-dark.text-darken-1[_ngcontent-%COMP%] {\\n  color: #343434 !important;\\n}\\n\\n.bg-dark.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #343434 !important;\\n}\\n\\n.border-dark.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #343434 !important;\\n}\\n\\n.border-top-dark.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #343434 !important;\\n}\\n\\n.border-bottom-dark.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #343434 !important;\\n}\\n\\n.border-left-dark.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #343434 !important;\\n}\\n\\n.border-right-dark.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #343434 !important;\\n}\\n\\n.overlay-dark.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #343434; \\n  background: rgba(52, 52, 52, 0.6);\\n}\\n\\n.text-dark.text-darken-2[_ngcontent-%COMP%] {\\n  color: #1e1e1e !important;\\n}\\n\\n.bg-dark.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #1e1e1e !important;\\n}\\n\\n.border-dark.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #1e1e1e !important;\\n}\\n\\n.border-top-dark.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #1e1e1e !important;\\n}\\n\\n.border-bottom-dark.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #1e1e1e !important;\\n}\\n\\n.border-left-dark.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #1e1e1e !important;\\n}\\n\\n.border-right-dark.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #1e1e1e !important;\\n}\\n\\n.overlay-dark.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #1e1e1e; \\n  background: rgba(30, 30, 30, 0.6);\\n}\\n\\n.text-dark.text-darken-3[_ngcontent-%COMP%] {\\n  color: #626262 !important;\\n}\\n\\n.bg-dark.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #626262 !important;\\n}\\n\\n.border-dark.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #626262 !important;\\n}\\n\\n.border-top-dark.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #626262 !important;\\n}\\n\\n.border-bottom-dark.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #626262 !important;\\n}\\n\\n.border-left-dark.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #626262 !important;\\n}\\n\\n.border-right-dark.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #626262 !important;\\n}\\n\\n.overlay-dark.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #626262; \\n  background: rgba(98, 98, 98, 0.6);\\n}\\n\\n.bg-light[_ngcontent-%COMP%] {\\n  background-color: #f6f6f6 !important;\\n}\\n.bg-light[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-light[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.border-light[_ngcontent-%COMP%] {\\n  border: 1px solid #f6f6f6 !important;\\n}\\n\\n.border-top-light[_ngcontent-%COMP%] {\\n  border-top: 1px solid #f6f6f6;\\n}\\n\\n.border-bottom-light[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f6f6f6;\\n}\\n\\n.border-left-light[_ngcontent-%COMP%] {\\n  border-left: 1px solid #f6f6f6;\\n}\\n\\n.border-right-light[_ngcontent-%COMP%] {\\n  border-right: 1px solid #f6f6f6;\\n}\\n\\n.bg-light.badge-glow[_ngcontent-%COMP%], .border-light.badge-glow[_ngcontent-%COMP%], .badge-light.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #f6f6f6;\\n}\\n\\n.overlay-light[_ngcontent-%COMP%] {\\n  background: #f6f6f6; \\n  background: rgba(246, 246, 246, 0.6);\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-light[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #f6f6f6 !important;\\n}\\n\\n.text-primary.text-lighten-5[_ngcontent-%COMP%] {\\n  color: #5390ee !important;\\n}\\n\\n.bg-primary.bg-lighten-5[_ngcontent-%COMP%] {\\n  background-color: #5390ee !important;\\n}\\n\\n.border-primary.border-lighten-5[_ngcontent-%COMP%] {\\n  border: 1px solid #5390ee !important;\\n}\\n\\n.border-top-primary.border-top-lighten-5[_ngcontent-%COMP%] {\\n  border-top: 1px solid #5390ee !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-5[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #5390ee !important;\\n}\\n\\n.border-left-primary.border-left-lighten-5[_ngcontent-%COMP%] {\\n  border-left: 1px solid #5390ee !important;\\n}\\n\\n.border-right-primary.border-right-lighten-5[_ngcontent-%COMP%] {\\n  border-right: 1px solid #5390ee !important;\\n}\\n\\n.overlay-primary.overlay-lighten-5[_ngcontent-%COMP%] {\\n  background: #5390ee; \\n  background: rgba(83, 144, 238, 0.6);\\n}\\n\\n.text-primary.text-lighten-4[_ngcontent-%COMP%] {\\n  color: #3c81ec !important;\\n}\\n\\n.bg-primary.bg-lighten-4[_ngcontent-%COMP%] {\\n  background-color: #3c81ec !important;\\n}\\n\\n.border-primary.border-lighten-4[_ngcontent-%COMP%] {\\n  border: 1px solid #3c81ec !important;\\n}\\n\\n.border-top-primary.border-top-lighten-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #3c81ec !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #3c81ec !important;\\n}\\n\\n.border-left-primary.border-left-lighten-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #3c81ec !important;\\n}\\n\\n.border-right-primary.border-right-lighten-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #3c81ec !important;\\n}\\n\\n.overlay-primary.overlay-lighten-4[_ngcontent-%COMP%] {\\n  background: #3c81ec; \\n  background: rgba(60, 129, 236, 0.6);\\n}\\n\\n.text-primary.text-lighten-3[_ngcontent-%COMP%] {\\n  color: #2472ea !important;\\n}\\n\\n.bg-primary.bg-lighten-3[_ngcontent-%COMP%] {\\n  background-color: #2472ea !important;\\n}\\n\\n.border-primary.border-lighten-3[_ngcontent-%COMP%] {\\n  border: 1px solid #2472ea !important;\\n}\\n\\n.border-top-primary.border-top-lighten-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #2472ea !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #2472ea !important;\\n}\\n\\n.border-left-primary.border-left-lighten-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #2472ea !important;\\n}\\n\\n.border-right-primary.border-right-lighten-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #2472ea !important;\\n}\\n\\n.overlay-primary.overlay-lighten-3[_ngcontent-%COMP%] {\\n  background: #2472ea; \\n  background: rgba(36, 114, 234, 0.6);\\n}\\n\\n.text-primary.text-lighten-2[_ngcontent-%COMP%] {\\n  color: #1565e0 !important;\\n}\\n\\n.bg-primary.bg-lighten-2[_ngcontent-%COMP%] {\\n  background-color: #1565e0 !important;\\n}\\n\\n.border-primary.border-lighten-2[_ngcontent-%COMP%] {\\n  border: 1px solid #1565e0 !important;\\n}\\n\\n.border-top-primary.border-top-lighten-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #1565e0 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #1565e0 !important;\\n}\\n\\n.border-left-primary.border-left-lighten-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #1565e0 !important;\\n}\\n\\n.border-right-primary.border-right-lighten-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #1565e0 !important;\\n}\\n\\n.overlay-primary.overlay-lighten-2[_ngcontent-%COMP%] {\\n  background: #1565e0; \\n  background: rgba(21, 101, 224, 0.6);\\n}\\n\\n.text-primary.text-lighten-1[_ngcontent-%COMP%] {\\n  color: #135bc8 !important;\\n}\\n\\n.bg-primary.bg-lighten-1[_ngcontent-%COMP%] {\\n  background-color: #135bc8 !important;\\n}\\n\\n.border-primary.border-lighten-1[_ngcontent-%COMP%] {\\n  border: 1px solid #135bc8 !important;\\n}\\n\\n.border-top-primary.border-top-lighten-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #135bc8 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #135bc8 !important;\\n}\\n\\n.border-left-primary.border-left-lighten-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #135bc8 !important;\\n}\\n\\n.border-right-primary.border-right-lighten-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #135bc8 !important;\\n}\\n\\n.overlay-primary.overlay-lighten-1[_ngcontent-%COMP%] {\\n  background: #135bc8; \\n  background: rgba(19, 91, 200, 0.6);\\n}\\n\\n.bg-primary[_ngcontent-%COMP%] {\\n  background-color: #1150b1 !important;\\n}\\n.bg-primary[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-primary[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-primary[_ngcontent-%COMP%] {\\n  background: rgba(17, 80, 177, 0.12) !important;\\n  color: #1150b1 !important;\\n}\\n.alert-primary[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(17, 80, 177, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-primary[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #0f459a !important;\\n}\\n.alert-primary[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #1150b1 !important;\\n}\\n\\n.bg-light-primary[_ngcontent-%COMP%] {\\n  background: rgba(17, 80, 177, 0.12) !important;\\n  color: #1150b1 !important;\\n}\\n.bg-light-primary.fc-h-event[_ngcontent-%COMP%], .bg-light-primary.fc-v-event[_ngcontent-%COMP%] {\\n  border-color: rgba(17, 80, 177, 0.1);\\n}\\n.bg-light-primary[_ngcontent-%COMP%]   .fc-list-event-dot[_ngcontent-%COMP%], .bg-light-primary[_ngcontent-%COMP%]   .fc-daygrid-event-dot[_ngcontent-%COMP%] {\\n  border-color: #1150b1 !important;\\n}\\n.bg-light-primary.fc-list-event[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  background: rgba(17, 80, 177, 0.1) !important;\\n}\\n.bg-light-primary.fc-list-event[_ngcontent-%COMP%]   .fc-list-event-title[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-primary[_ngcontent-%COMP%] {\\n  color: #1150b1 !important;\\n}\\n\\n.border-primary[_ngcontent-%COMP%] {\\n  border: 1px solid #1150b1 !important;\\n}\\n\\n.border-top-primary[_ngcontent-%COMP%] {\\n  border-top: 1px solid #1150b1;\\n}\\n\\n.border-bottom-primary[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #1150b1;\\n}\\n\\n.border-left-primary[_ngcontent-%COMP%] {\\n  border-left: 1px solid #1150b1;\\n}\\n\\n.border-right-primary[_ngcontent-%COMP%] {\\n  border-right: 1px solid #1150b1;\\n}\\n\\n.bg-primary.badge-glow[_ngcontent-%COMP%], .border-primary.badge-glow[_ngcontent-%COMP%], .badge-primary.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #1150b1;\\n}\\n\\n.badge.badge-light-primary[_ngcontent-%COMP%] {\\n  background-color: rgba(17, 80, 177, 0.12);\\n  color: #1150b1 !important;\\n}\\n\\n.overlay-primary[_ngcontent-%COMP%] {\\n  background: #1150b1; \\n  background: rgba(17, 80, 177, 0.6);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  border-color: #1150b1 !important;\\n  background-color: #1150b1 !important;\\n  color: #fff !important;\\n}\\n.btn-primary[_ngcontent-%COMP%]:focus, .btn-primary[_ngcontent-%COMP%]:active, .btn-primary.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #0f459a !important;\\n}\\n.btn-primary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #1150b1;\\n}\\n.btn-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-primary[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #1150b1;\\n}\\n.btn-flat-primary[_ngcontent-%COMP%]:hover {\\n  color: #1150b1;\\n}\\n.btn-flat-primary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(17, 80, 177, 0.12);\\n}\\n.btn-flat-primary[_ngcontent-%COMP%]:active, .btn-flat-primary.active[_ngcontent-%COMP%], .btn-flat-primary[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(17, 80, 177, 0.2);\\n  color: #1150b1;\\n}\\n.btn-flat-primary.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-primary[_ngcontent-%COMP%] {\\n  background-color: #1150b1;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-primary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #135bc8;\\n}\\n.btn-relief-primary[_ngcontent-%COMP%]:active, .btn-relief-primary.active[_ngcontent-%COMP%], .btn-relief-primary[_ngcontent-%COMP%]:focus {\\n  background-color: #0f459a;\\n}\\n.btn-relief-primary[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-primary[_ngcontent-%COMP%]:active, .btn-relief-primary.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  border: 1px solid #1150b1 !important;\\n  background-color: transparent;\\n  color: #1150b1;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(17, 80, 177, 0.04);\\n  color: #1150b1;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-primary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(17, 80, 177, 0.2);\\n  color: #1150b1;\\n}\\n.btn-outline-primary.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-primary.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(17, 80, 177, 0.2);\\n  color: #1150b1;\\n}\\n\\n.btn-outline-primary.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-primary.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(17, 80, 177, 0.2) 0, rgba(17, 80, 177, 0.3) 40%, rgba(17, 80, 177, 0.4) 50%, rgba(17, 80, 177, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-primary[_ngcontent-%COMP%] {\\n  background-color: #1150b1;\\n}\\n\\n.modal.modal-primary[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #1150b1;\\n}\\n.modal.modal-primary[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #1150b1 !important;\\n}\\n\\n.pagination-primary[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: #1150b1 !important;\\n  color: #fff;\\n}\\n.pagination-primary[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.pagination-primary[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #1150b1;\\n}\\n.pagination-primary[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover, .pagination-primary[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #1150b1;\\n  color: #fff;\\n}\\n.pagination-primary[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-primary[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after, .pagination-primary[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-primary[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-primary[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-primary[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before, .pagination-primary[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-primary[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-primary[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #1150b1 !important;\\n  border-color: #1150b1;\\n  box-shadow: 0 4px 18px -4px rgba(17, 80, 177, 0.65);\\n}\\n\\n.progress-bar-primary[_ngcontent-%COMP%] {\\n  background-color: rgba(17, 80, 177, 0.12);\\n}\\n.progress-bar-primary[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #1150b1;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-primary[_ngcontent-%COMP%] {\\n  border-color: #1150b1 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-primary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-primary[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #1150b1 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-primary.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #1150b1 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-primary.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(17, 80, 177, 0.12) !important;\\n}\\n\\n.divider.divider-primary[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-primary[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #1150b1 !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-primary[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #1150b1 !important;\\n}\\n\\n.custom-control-primary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-primary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #1150b1;\\n  background-color: #1150b1;\\n}\\n.custom-control-primary.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-primary.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-primary.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-primary.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-primary.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-primary.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(17, 80, 177, 0.4) !important;\\n}\\n.custom-control-primary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(17, 80, 177, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-primary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #1150b1 !important;\\n}\\n\\n.custom-switch-primary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #1150b1 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-primary[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #1150b1 !important;\\n  border-color: #1150b1 !important;\\n}\\n\\n.text-primary.text-darken-1[_ngcontent-%COMP%] {\\n  color: #0f459a !important;\\n}\\n\\n.bg-primary.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #0f459a !important;\\n}\\n\\n.border-primary.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #0f459a !important;\\n}\\n\\n.border-top-primary.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #0f459a !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #0f459a !important;\\n}\\n\\n.border-left-primary.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #0f459a !important;\\n}\\n\\n.border-right-primary.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #0f459a !important;\\n}\\n\\n.overlay-primary.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #0f459a; \\n  background: rgba(15, 69, 154, 0.6);\\n}\\n\\n.text-primary.text-darken-2[_ngcontent-%COMP%] {\\n  color: #0d3b82 !important;\\n}\\n\\n.bg-primary.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #0d3b82 !important;\\n}\\n\\n.border-primary.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #0d3b82 !important;\\n}\\n\\n.border-top-primary.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #0d3b82 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #0d3b82 !important;\\n}\\n\\n.border-left-primary.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #0d3b82 !important;\\n}\\n\\n.border-right-primary.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #0d3b82 !important;\\n}\\n\\n.overlay-primary.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #0d3b82; \\n  background: rgba(13, 59, 130, 0.6);\\n}\\n\\n.text-primary.text-darken-3[_ngcontent-%COMP%] {\\n  color: #0a306b !important;\\n}\\n\\n.bg-primary.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #0a306b !important;\\n}\\n\\n.border-primary.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #0a306b !important;\\n}\\n\\n.border-top-primary.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #0a306b !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #0a306b !important;\\n}\\n\\n.border-left-primary.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #0a306b !important;\\n}\\n\\n.border-right-primary.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #0a306b !important;\\n}\\n\\n.overlay-primary.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #0a306b; \\n  background: rgba(10, 48, 107, 0.6);\\n}\\n\\n.text-primary.text-darken-4[_ngcontent-%COMP%] {\\n  color: #082654 !important;\\n}\\n\\n.bg-primary.bg-darken-4[_ngcontent-%COMP%] {\\n  background-color: #082654 !important;\\n}\\n\\n.border-primary.border-darken-4[_ngcontent-%COMP%] {\\n  border: 1px solid #082654 !important;\\n}\\n\\n.border-top-primary.border-top-darken-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #082654 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #082654 !important;\\n}\\n\\n.border-left-primary.border-left-darken-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #082654 !important;\\n}\\n\\n.border-right-primary.border-right-darken-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #082654 !important;\\n}\\n\\n.overlay-primary.overlay-darken-4[_ngcontent-%COMP%] {\\n  background: #082654; \\n  background: rgba(8, 38, 84, 0.6);\\n}\\n\\n.text-primary.text-accent-1[_ngcontent-%COMP%] {\\n  color: #bdfdff !important;\\n}\\n\\n.bg-primary.bg-accent-1[_ngcontent-%COMP%] {\\n  background-color: #bdfdff !important;\\n}\\n\\n.border-primary.border-accent-1[_ngcontent-%COMP%] {\\n  border: 1px solid #bdfdff !important;\\n}\\n\\n.border-top-primary.border-top-accent-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #bdfdff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #bdfdff !important;\\n}\\n\\n.border-left-primary.border-left-accent-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #bdfdff !important;\\n}\\n\\n.border-right-primary.border-right-accent-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #bdfdff !important;\\n}\\n\\n.overlay-primary.overlay-accent-1[_ngcontent-%COMP%] {\\n  background: #bdfdff; \\n  background: rgba(189, 253, 255, 0.6);\\n}\\n\\n.text-primary.text-accent-2[_ngcontent-%COMP%] {\\n  color: #8afbff !important;\\n}\\n\\n.bg-primary.bg-accent-2[_ngcontent-%COMP%] {\\n  background-color: #8afbff !important;\\n}\\n\\n.border-primary.border-accent-2[_ngcontent-%COMP%] {\\n  border: 1px solid #8afbff !important;\\n}\\n\\n.border-top-primary.border-top-accent-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #8afbff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #8afbff !important;\\n}\\n\\n.border-left-primary.border-left-accent-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #8afbff !important;\\n}\\n\\n.border-right-primary.border-right-accent-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #8afbff !important;\\n}\\n\\n.overlay-primary.overlay-accent-2[_ngcontent-%COMP%] {\\n  background: #8afbff; \\n  background: rgba(138, 251, 255, 0.6);\\n}\\n\\n.text-primary.text-accent-3[_ngcontent-%COMP%] {\\n  color: #57faff !important;\\n}\\n\\n.bg-primary.bg-accent-3[_ngcontent-%COMP%] {\\n  background-color: #57faff !important;\\n}\\n\\n.border-primary.border-accent-3[_ngcontent-%COMP%] {\\n  border: 1px solid #57faff !important;\\n}\\n\\n.border-top-primary.border-top-accent-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #57faff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #57faff !important;\\n}\\n\\n.border-left-primary.border-left-accent-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #57faff !important;\\n}\\n\\n.border-right-primary.border-right-accent-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #57faff !important;\\n}\\n\\n.overlay-primary.overlay-accent-3[_ngcontent-%COMP%] {\\n  background: #57faff; \\n  background: rgba(87, 250, 255, 0.6);\\n}\\n\\n.text-primary.text-accent-4[_ngcontent-%COMP%] {\\n  color: #3df9ff !important;\\n}\\n\\n.bg-primary.bg-accent-4[_ngcontent-%COMP%] {\\n  background-color: #3df9ff !important;\\n}\\n\\n.border-primary.border-accent-4[_ngcontent-%COMP%] {\\n  border: 1px solid #3df9ff !important;\\n}\\n\\n.border-top-primary.border-top-accent-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #3df9ff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #3df9ff !important;\\n}\\n\\n.border-left-primary.border-left-accent-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #3df9ff !important;\\n}\\n\\n.border-right-primary.border-right-accent-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #3df9ff !important;\\n}\\n\\n.overlay-primary.overlay-accent-4[_ngcontent-%COMP%] {\\n  background: #3df9ff; \\n  background: rgba(61, 249, 255, 0.6);\\n}\\n\\n.text-secondary.text-lighten-5[_ngcontent-%COMP%] {\\n  color: #c4c6c8 !important;\\n}\\n\\n.bg-secondary.bg-lighten-5[_ngcontent-%COMP%] {\\n  background-color: #c4c6c8 !important;\\n}\\n\\n.border-secondary.border-lighten-5[_ngcontent-%COMP%] {\\n  border: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-5[_ngcontent-%COMP%] {\\n  border-top: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-5[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-5[_ngcontent-%COMP%] {\\n  border-left: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-5[_ngcontent-%COMP%] {\\n  border-right: 1px solid #c4c6c8 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-5[_ngcontent-%COMP%] {\\n  background: #c4c6c8; \\n  background: rgba(196, 198, 200, 0.6);\\n}\\n\\n.text-secondary.text-lighten-4[_ngcontent-%COMP%] {\\n  color: #b7b9bc !important;\\n}\\n\\n.bg-secondary.bg-lighten-4[_ngcontent-%COMP%] {\\n  background-color: #b7b9bc !important;\\n}\\n\\n.border-secondary.border-lighten-4[_ngcontent-%COMP%] {\\n  border: 1px solid #b7b9bc !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #b7b9bc !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #b7b9bc !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #b7b9bc !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #b7b9bc !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-4[_ngcontent-%COMP%] {\\n  background: #b7b9bc; \\n  background: rgba(183, 185, 188, 0.6);\\n}\\n\\n.text-secondary.text-lighten-3[_ngcontent-%COMP%] {\\n  color: #aaacb0 !important;\\n}\\n\\n.bg-secondary.bg-lighten-3[_ngcontent-%COMP%] {\\n  background-color: #aaacb0 !important;\\n}\\n\\n.border-secondary.border-lighten-3[_ngcontent-%COMP%] {\\n  border: 1px solid #aaacb0 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #aaacb0 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #aaacb0 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #aaacb0 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #aaacb0 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-3[_ngcontent-%COMP%] {\\n  background: #aaacb0; \\n  background: rgba(170, 172, 176, 0.6);\\n}\\n\\n.text-secondary.text-lighten-2[_ngcontent-%COMP%] {\\n  color: #9ca0a4 !important;\\n}\\n\\n.bg-secondary.bg-lighten-2[_ngcontent-%COMP%] {\\n  background-color: #9ca0a4 !important;\\n}\\n\\n.border-secondary.border-lighten-2[_ngcontent-%COMP%] {\\n  border: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #9ca0a4 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-2[_ngcontent-%COMP%] {\\n  background: #9ca0a4; \\n  background: rgba(156, 160, 164, 0.6);\\n}\\n\\n.text-secondary.text-lighten-1[_ngcontent-%COMP%] {\\n  color: #8f9397 !important;\\n}\\n\\n.bg-secondary.bg-lighten-1[_ngcontent-%COMP%] {\\n  background-color: #8f9397 !important;\\n}\\n\\n.border-secondary.border-lighten-1[_ngcontent-%COMP%] {\\n  border: 1px solid #8f9397 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #8f9397 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #8f9397 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #8f9397 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #8f9397 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-1[_ngcontent-%COMP%] {\\n  background: #8f9397; \\n  background: rgba(143, 147, 151, 0.6);\\n}\\n\\n.bg-secondary[_ngcontent-%COMP%] {\\n  background-color: #82868b !important;\\n}\\n.bg-secondary[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-secondary[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-secondary[_ngcontent-%COMP%] {\\n  background: rgba(130, 134, 139, 0.12) !important;\\n  color: #82868b !important;\\n}\\n.alert-secondary[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(130, 134, 139, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-secondary[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #75797e !important;\\n}\\n.alert-secondary[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #82868b !important;\\n}\\n\\n.bg-light-secondary[_ngcontent-%COMP%] {\\n  background: rgba(130, 134, 139, 0.12) !important;\\n  color: #82868b !important;\\n}\\n.bg-light-secondary.fc-h-event[_ngcontent-%COMP%], .bg-light-secondary.fc-v-event[_ngcontent-%COMP%] {\\n  border-color: rgba(130, 134, 139, 0.1);\\n}\\n.bg-light-secondary[_ngcontent-%COMP%]   .fc-list-event-dot[_ngcontent-%COMP%], .bg-light-secondary[_ngcontent-%COMP%]   .fc-daygrid-event-dot[_ngcontent-%COMP%] {\\n  border-color: #82868b !important;\\n}\\n.bg-light-secondary.fc-list-event[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  background: rgba(130, 134, 139, 0.1) !important;\\n}\\n.bg-light-secondary.fc-list-event[_ngcontent-%COMP%]   .fc-list-event-title[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-secondary[_ngcontent-%COMP%] {\\n  color: #82868b !important;\\n}\\n\\n.border-secondary[_ngcontent-%COMP%] {\\n  border: 1px solid #82868b !important;\\n}\\n\\n.border-top-secondary[_ngcontent-%COMP%] {\\n  border-top: 1px solid #82868b;\\n}\\n\\n.border-bottom-secondary[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #82868b;\\n}\\n\\n.border-left-secondary[_ngcontent-%COMP%] {\\n  border-left: 1px solid #82868b;\\n}\\n\\n.border-right-secondary[_ngcontent-%COMP%] {\\n  border-right: 1px solid #82868b;\\n}\\n\\n.bg-secondary.badge-glow[_ngcontent-%COMP%], .border-secondary.badge-glow[_ngcontent-%COMP%], .badge-secondary.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #82868b;\\n}\\n\\n.badge.badge-light-secondary[_ngcontent-%COMP%] {\\n  background-color: rgba(130, 134, 139, 0.12);\\n  color: #82868b !important;\\n}\\n\\n.overlay-secondary[_ngcontent-%COMP%] {\\n  background: #82868b; \\n  background: rgba(130, 134, 139, 0.6);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  border-color: #82868b !important;\\n  background-color: #82868b !important;\\n  color: #fff !important;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:focus, .btn-secondary[_ngcontent-%COMP%]:active, .btn-secondary.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #75797e !important;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #82868b;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-secondary[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #82868b;\\n}\\n.btn-flat-secondary[_ngcontent-%COMP%]:hover {\\n  color: #82868b;\\n}\\n.btn-flat-secondary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(130, 134, 139, 0.12);\\n}\\n.btn-flat-secondary[_ngcontent-%COMP%]:active, .btn-flat-secondary.active[_ngcontent-%COMP%], .btn-flat-secondary[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(130, 134, 139, 0.2);\\n  color: #82868b;\\n}\\n.btn-flat-secondary.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-secondary[_ngcontent-%COMP%] {\\n  background-color: #82868b;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-secondary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #8f9397;\\n}\\n.btn-relief-secondary[_ngcontent-%COMP%]:active, .btn-relief-secondary.active[_ngcontent-%COMP%], .btn-relief-secondary[_ngcontent-%COMP%]:focus {\\n  background-color: #75797e;\\n}\\n.btn-relief-secondary[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-secondary[_ngcontent-%COMP%]:active, .btn-relief-secondary.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border: 1px solid #82868b !important;\\n  background-color: transparent;\\n  color: #82868b;\\n}\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(130, 134, 139, 0.04);\\n  color: #82868b;\\n}\\n.btn-outline-secondary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-secondary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-secondary[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-secondary[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(130, 134, 139, 0.2);\\n  color: #82868b;\\n}\\n.btn-outline-secondary.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-secondary.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(130, 134, 139, 0.2);\\n  color: #82868b;\\n}\\n\\n.btn-outline-secondary.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-secondary.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(130, 134, 139, 0.2) 0, rgba(130, 134, 139, 0.3) 40%, rgba(130, 134, 139, 0.4) 50%, rgba(130, 134, 139, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-secondary[_ngcontent-%COMP%] {\\n  background-color: #82868b;\\n}\\n\\n.modal.modal-secondary[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #82868b;\\n}\\n.modal.modal-secondary[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #82868b !important;\\n}\\n\\n.pagination-secondary[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: #82868b !important;\\n  color: #fff;\\n}\\n.pagination-secondary[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.pagination-secondary[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #82868b;\\n}\\n.pagination-secondary[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover, .pagination-secondary[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #82868b;\\n  color: #fff;\\n}\\n.pagination-secondary[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-secondary[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after, .pagination-secondary[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-secondary[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-secondary[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-secondary[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before, .pagination-secondary[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-secondary[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-secondary[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #82868b !important;\\n  border-color: #82868b;\\n  box-shadow: 0 4px 18px -4px rgba(130, 134, 139, 0.65);\\n}\\n\\n.progress-bar-secondary[_ngcontent-%COMP%] {\\n  background-color: rgba(130, 134, 139, 0.12);\\n}\\n.progress-bar-secondary[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #82868b;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-secondary[_ngcontent-%COMP%] {\\n  border-color: #82868b !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-secondary[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #82868b !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-secondary.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #82868b !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-secondary.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(130, 134, 139, 0.12) !important;\\n}\\n\\n.divider.divider-secondary[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-secondary[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #82868b !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-secondary[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #82868b !important;\\n}\\n\\n.custom-control-secondary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-secondary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #82868b;\\n  background-color: #82868b;\\n}\\n.custom-control-secondary.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-secondary.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-secondary.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-secondary.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-secondary.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-secondary.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(130, 134, 139, 0.4) !important;\\n}\\n.custom-control-secondary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(130, 134, 139, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-secondary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #82868b !important;\\n}\\n\\n.custom-switch-secondary[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #82868b !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-secondary[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #82868b !important;\\n  border-color: #82868b !important;\\n}\\n\\n.text-secondary.text-darken-1[_ngcontent-%COMP%] {\\n  color: #75797e !important;\\n}\\n\\n.bg-secondary.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #75797e !important;\\n}\\n\\n.border-secondary.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #75797e !important;\\n}\\n\\n.border-top-secondary.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #75797e !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #75797e !important;\\n}\\n\\n.border-left-secondary.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #75797e !important;\\n}\\n\\n.border-right-secondary.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #75797e !important;\\n}\\n\\n.overlay-secondary.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #75797e; \\n  background: rgba(117, 121, 126, 0.6);\\n}\\n\\n.text-secondary.text-darken-2[_ngcontent-%COMP%] {\\n  color: #696d71 !important;\\n}\\n\\n.bg-secondary.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #696d71 !important;\\n}\\n\\n.border-secondary.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #696d71 !important;\\n}\\n\\n.border-top-secondary.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #696d71 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #696d71 !important;\\n}\\n\\n.border-left-secondary.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #696d71 !important;\\n}\\n\\n.border-right-secondary.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #696d71 !important;\\n}\\n\\n.overlay-secondary.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #696d71; \\n  background: rgba(105, 109, 113, 0.6);\\n}\\n\\n.text-secondary.text-darken-3[_ngcontent-%COMP%] {\\n  color: #5d6064 !important;\\n}\\n\\n.bg-secondary.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #5d6064 !important;\\n}\\n\\n.border-secondary.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #5d6064 !important;\\n}\\n\\n.border-top-secondary.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #5d6064 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #5d6064 !important;\\n}\\n\\n.border-left-secondary.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #5d6064 !important;\\n}\\n\\n.border-right-secondary.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #5d6064 !important;\\n}\\n\\n.overlay-secondary.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #5d6064; \\n  background: rgba(93, 96, 100, 0.6);\\n}\\n\\n.text-secondary.text-darken-4[_ngcontent-%COMP%] {\\n  color: #505357 !important;\\n}\\n\\n.bg-secondary.bg-darken-4[_ngcontent-%COMP%] {\\n  background-color: #505357 !important;\\n}\\n\\n.border-secondary.border-darken-4[_ngcontent-%COMP%] {\\n  border: 1px solid #505357 !important;\\n}\\n\\n.border-top-secondary.border-top-darken-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #505357 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #505357 !important;\\n}\\n\\n.border-left-secondary.border-left-darken-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #505357 !important;\\n}\\n\\n.border-right-secondary.border-right-darken-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #505357 !important;\\n}\\n\\n.overlay-secondary.overlay-darken-4[_ngcontent-%COMP%] {\\n  background: #505357; \\n  background: rgba(80, 83, 87, 0.6);\\n}\\n\\n.text-success.text-lighten-5[_ngcontent-%COMP%] {\\n  color: #88e7b2 !important;\\n}\\n\\n.bg-success.bg-lighten-5[_ngcontent-%COMP%] {\\n  background-color: #88e7b2 !important;\\n}\\n\\n.border-success.border-lighten-5[_ngcontent-%COMP%] {\\n  border: 1px solid #88e7b2 !important;\\n}\\n\\n.border-top-success.border-top-lighten-5[_ngcontent-%COMP%] {\\n  border-top: 1px solid #88e7b2 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-5[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #88e7b2 !important;\\n}\\n\\n.border-left-success.border-left-lighten-5[_ngcontent-%COMP%] {\\n  border-left: 1px solid #88e7b2 !important;\\n}\\n\\n.border-right-success.border-right-lighten-5[_ngcontent-%COMP%] {\\n  border-right: 1px solid #88e7b2 !important;\\n}\\n\\n.overlay-success.overlay-lighten-5[_ngcontent-%COMP%] {\\n  background: #88e7b2; \\n  background: rgba(136, 231, 178, 0.6);\\n}\\n\\n.text-success.text-lighten-4[_ngcontent-%COMP%] {\\n  color: #72e3a4 !important;\\n}\\n\\n.bg-success.bg-lighten-4[_ngcontent-%COMP%] {\\n  background-color: #72e3a4 !important;\\n}\\n\\n.border-success.border-lighten-4[_ngcontent-%COMP%] {\\n  border: 1px solid #72e3a4 !important;\\n}\\n\\n.border-top-success.border-top-lighten-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #72e3a4 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #72e3a4 !important;\\n}\\n\\n.border-left-success.border-left-lighten-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #72e3a4 !important;\\n}\\n\\n.border-right-success.border-right-lighten-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #72e3a4 !important;\\n}\\n\\n.overlay-success.overlay-lighten-4[_ngcontent-%COMP%] {\\n  background: #72e3a4; \\n  background: rgba(114, 227, 164, 0.6);\\n}\\n\\n.text-success.text-lighten-3[_ngcontent-%COMP%] {\\n  color: #5dde97 !important;\\n}\\n\\n.bg-success.bg-lighten-3[_ngcontent-%COMP%] {\\n  background-color: #5dde97 !important;\\n}\\n\\n.border-success.border-lighten-3[_ngcontent-%COMP%] {\\n  border: 1px solid #5dde97 !important;\\n}\\n\\n.border-top-success.border-top-lighten-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #5dde97 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #5dde97 !important;\\n}\\n\\n.border-left-success.border-left-lighten-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #5dde97 !important;\\n}\\n\\n.border-right-success.border-right-lighten-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #5dde97 !important;\\n}\\n\\n.overlay-success.overlay-lighten-3[_ngcontent-%COMP%] {\\n  background: #5dde97; \\n  background: rgba(93, 222, 151, 0.6);\\n}\\n\\n.text-success.text-lighten-2[_ngcontent-%COMP%] {\\n  color: #48da89 !important;\\n}\\n\\n.bg-success.bg-lighten-2[_ngcontent-%COMP%] {\\n  background-color: #48da89 !important;\\n}\\n\\n.border-success.border-lighten-2[_ngcontent-%COMP%] {\\n  border: 1px solid #48da89 !important;\\n}\\n\\n.border-top-success.border-top-lighten-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #48da89 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #48da89 !important;\\n}\\n\\n.border-left-success.border-left-lighten-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #48da89 !important;\\n}\\n\\n.border-right-success.border-right-lighten-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #48da89 !important;\\n}\\n\\n.overlay-success.overlay-lighten-2[_ngcontent-%COMP%] {\\n  background: #48da89; \\n  background: rgba(72, 218, 137, 0.6);\\n}\\n\\n.text-success.text-lighten-1[_ngcontent-%COMP%] {\\n  color: #33d67c !important;\\n}\\n\\n.bg-success.bg-lighten-1[_ngcontent-%COMP%] {\\n  background-color: #33d67c !important;\\n}\\n\\n.border-success.border-lighten-1[_ngcontent-%COMP%] {\\n  border: 1px solid #33d67c !important;\\n}\\n\\n.border-top-success.border-top-lighten-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #33d67c !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #33d67c !important;\\n}\\n\\n.border-left-success.border-left-lighten-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #33d67c !important;\\n}\\n\\n.border-right-success.border-right-lighten-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #33d67c !important;\\n}\\n\\n.overlay-success.overlay-lighten-1[_ngcontent-%COMP%] {\\n  background: #33d67c; \\n  background: rgba(51, 214, 124, 0.6);\\n}\\n\\n.bg-success[_ngcontent-%COMP%] {\\n  background-color: #28c76f !important;\\n}\\n.bg-success[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-success[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  background: rgba(40, 199, 111, 0.12) !important;\\n  color: #28c76f !important;\\n}\\n.alert-success[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(40, 199, 111, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-success[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #24b263 !important;\\n}\\n.alert-success[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #28c76f !important;\\n}\\n\\n.bg-light-success[_ngcontent-%COMP%] {\\n  background: rgba(40, 199, 111, 0.12) !important;\\n  color: #28c76f !important;\\n}\\n.bg-light-success.fc-h-event[_ngcontent-%COMP%], .bg-light-success.fc-v-event[_ngcontent-%COMP%] {\\n  border-color: rgba(40, 199, 111, 0.1);\\n}\\n.bg-light-success[_ngcontent-%COMP%]   .fc-list-event-dot[_ngcontent-%COMP%], .bg-light-success[_ngcontent-%COMP%]   .fc-daygrid-event-dot[_ngcontent-%COMP%] {\\n  border-color: #28c76f !important;\\n}\\n.bg-light-success.fc-list-event[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  background: rgba(40, 199, 111, 0.1) !important;\\n}\\n.bg-light-success.fc-list-event[_ngcontent-%COMP%]   .fc-list-event-title[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-success[_ngcontent-%COMP%] {\\n  color: #28c76f !important;\\n}\\n\\n.border-success[_ngcontent-%COMP%] {\\n  border: 1px solid #28c76f !important;\\n}\\n\\n.border-top-success[_ngcontent-%COMP%] {\\n  border-top: 1px solid #28c76f;\\n}\\n\\n.border-bottom-success[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #28c76f;\\n}\\n\\n.border-left-success[_ngcontent-%COMP%] {\\n  border-left: 1px solid #28c76f;\\n}\\n\\n.border-right-success[_ngcontent-%COMP%] {\\n  border-right: 1px solid #28c76f;\\n}\\n\\n.bg-success.badge-glow[_ngcontent-%COMP%], .border-success.badge-glow[_ngcontent-%COMP%], .badge-success.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #28c76f;\\n}\\n\\n.badge.badge-light-success[_ngcontent-%COMP%] {\\n  background-color: rgba(40, 199, 111, 0.12);\\n  color: #28c76f !important;\\n}\\n\\n.overlay-success[_ngcontent-%COMP%] {\\n  background: #28c76f; \\n  background: rgba(40, 199, 111, 0.6);\\n}\\n\\n.btn-success[_ngcontent-%COMP%] {\\n  border-color: #28c76f !important;\\n  background-color: #28c76f !important;\\n  color: #fff !important;\\n}\\n.btn-success[_ngcontent-%COMP%]:focus, .btn-success[_ngcontent-%COMP%]:active, .btn-success.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #24b263 !important;\\n}\\n.btn-success[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #28c76f;\\n}\\n.btn-success[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-success[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #28c76f;\\n}\\n.btn-flat-success[_ngcontent-%COMP%]:hover {\\n  color: #28c76f;\\n}\\n.btn-flat-success[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(40, 199, 111, 0.12);\\n}\\n.btn-flat-success[_ngcontent-%COMP%]:active, .btn-flat-success.active[_ngcontent-%COMP%], .btn-flat-success[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(40, 199, 111, 0.2);\\n  color: #28c76f;\\n}\\n.btn-flat-success.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-success[_ngcontent-%COMP%] {\\n  background-color: #28c76f;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-success[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #33d67c;\\n}\\n.btn-relief-success[_ngcontent-%COMP%]:active, .btn-relief-success.active[_ngcontent-%COMP%], .btn-relief-success[_ngcontent-%COMP%]:focus {\\n  background-color: #24b263;\\n}\\n.btn-relief-success[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-success[_ngcontent-%COMP%]:active, .btn-relief-success.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-success[_ngcontent-%COMP%] {\\n  border: 1px solid #28c76f !important;\\n  background-color: transparent;\\n  color: #28c76f;\\n}\\n.btn-outline-success[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(40, 199, 111, 0.04);\\n  color: #28c76f;\\n}\\n.btn-outline-success[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-success[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-success[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-success[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(40, 199, 111, 0.2);\\n  color: #28c76f;\\n}\\n.btn-outline-success.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-success.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(40, 199, 111, 0.2);\\n  color: #28c76f;\\n}\\n\\n.btn-outline-success.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-success.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(40, 199, 111, 0.2) 0, rgba(40, 199, 111, 0.3) 40%, rgba(40, 199, 111, 0.4) 50%, rgba(40, 199, 111, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-success[_ngcontent-%COMP%] {\\n  background-color: #28c76f;\\n}\\n\\n.modal.modal-success[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #28c76f;\\n}\\n.modal.modal-success[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #28c76f !important;\\n}\\n\\n.pagination-success[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: #28c76f !important;\\n  color: #fff;\\n}\\n.pagination-success[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.pagination-success[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #28c76f;\\n}\\n.pagination-success[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover, .pagination-success[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #28c76f;\\n  color: #fff;\\n}\\n.pagination-success[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-success[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after, .pagination-success[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-success[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-success[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-success[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before, .pagination-success[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-success[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-success[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #28c76f !important;\\n  border-color: #28c76f;\\n  box-shadow: 0 4px 18px -4px rgba(40, 199, 111, 0.65);\\n}\\n\\n.progress-bar-success[_ngcontent-%COMP%] {\\n  background-color: rgba(40, 199, 111, 0.12);\\n}\\n.progress-bar-success[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #28c76f;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-success[_ngcontent-%COMP%] {\\n  border-color: #28c76f !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-success[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #28c76f !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-success.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #28c76f !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-success.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(40, 199, 111, 0.12) !important;\\n}\\n\\n.divider.divider-success[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-success[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #28c76f !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-success[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #28c76f !important;\\n}\\n\\n.custom-control-success[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-success[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #28c76f;\\n  background-color: #28c76f;\\n}\\n.custom-control-success.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-success.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-success.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-success.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-success.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-success.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(40, 199, 111, 0.4) !important;\\n}\\n.custom-control-success[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(40, 199, 111, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-success[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #28c76f !important;\\n}\\n\\n.custom-switch-success[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #28c76f !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-success[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #28c76f !important;\\n  border-color: #28c76f !important;\\n}\\n\\n.text-success.text-darken-1[_ngcontent-%COMP%] {\\n  color: #24b263 !important;\\n}\\n\\n.bg-success.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #24b263 !important;\\n}\\n\\n.border-success.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #24b263 !important;\\n}\\n\\n.border-top-success.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #24b263 !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #24b263 !important;\\n}\\n\\n.border-left-success.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #24b263 !important;\\n}\\n\\n.border-right-success.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #24b263 !important;\\n}\\n\\n.overlay-success.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #24b263; \\n  background: rgba(36, 178, 99, 0.6);\\n}\\n\\n.text-success.text-darken-2[_ngcontent-%COMP%] {\\n  color: #1f9d57 !important;\\n}\\n\\n.bg-success.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #1f9d57 !important;\\n}\\n\\n.border-success.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #1f9d57 !important;\\n}\\n\\n.border-top-success.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #1f9d57 !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #1f9d57 !important;\\n}\\n\\n.border-left-success.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #1f9d57 !important;\\n}\\n\\n.border-right-success.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #1f9d57 !important;\\n}\\n\\n.overlay-success.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #1f9d57; \\n  background: rgba(31, 157, 87, 0.6);\\n}\\n\\n.text-success.text-darken-3[_ngcontent-%COMP%] {\\n  color: #1b874b !important;\\n}\\n\\n.bg-success.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #1b874b !important;\\n}\\n\\n.border-success.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #1b874b !important;\\n}\\n\\n.border-top-success.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #1b874b !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #1b874b !important;\\n}\\n\\n.border-left-success.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #1b874b !important;\\n}\\n\\n.border-right-success.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #1b874b !important;\\n}\\n\\n.overlay-success.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #1b874b; \\n  background: rgba(27, 135, 75, 0.6);\\n}\\n\\n.text-success.text-darken-4[_ngcontent-%COMP%] {\\n  color: #177240 !important;\\n}\\n\\n.bg-success.bg-darken-4[_ngcontent-%COMP%] {\\n  background-color: #177240 !important;\\n}\\n\\n.border-success.border-darken-4[_ngcontent-%COMP%] {\\n  border: 1px solid #177240 !important;\\n}\\n\\n.border-top-success.border-top-darken-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #177240 !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #177240 !important;\\n}\\n\\n.border-left-success.border-left-darken-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #177240 !important;\\n}\\n\\n.border-right-success.border-right-darken-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #177240 !important;\\n}\\n\\n.overlay-success.overlay-darken-4[_ngcontent-%COMP%] {\\n  background: #177240; \\n  background: rgba(23, 114, 64, 0.6);\\n}\\n\\n.text-success.text-accent-1[_ngcontent-%COMP%] {\\n  color: #e1fff1 !important;\\n}\\n\\n.bg-success.bg-accent-1[_ngcontent-%COMP%] {\\n  background-color: #e1fff1 !important;\\n}\\n\\n.border-success.border-accent-1[_ngcontent-%COMP%] {\\n  border: 1px solid #e1fff1 !important;\\n}\\n\\n.border-top-success.border-top-accent-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e1fff1 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e1fff1 !important;\\n}\\n\\n.border-left-success.border-left-accent-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #e1fff1 !important;\\n}\\n\\n.border-right-success.border-right-accent-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #e1fff1 !important;\\n}\\n\\n.overlay-success.overlay-accent-1[_ngcontent-%COMP%] {\\n  background: #e1fff1; \\n  background: rgba(225, 255, 241, 0.6);\\n}\\n\\n.text-success.text-accent-2[_ngcontent-%COMP%] {\\n  color: #aeffd9 !important;\\n}\\n\\n.bg-success.bg-accent-2[_ngcontent-%COMP%] {\\n  background-color: #aeffd9 !important;\\n}\\n\\n.border-success.border-accent-2[_ngcontent-%COMP%] {\\n  border: 1px solid #aeffd9 !important;\\n}\\n\\n.border-top-success.border-top-accent-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #aeffd9 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #aeffd9 !important;\\n}\\n\\n.border-left-success.border-left-accent-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #aeffd9 !important;\\n}\\n\\n.border-right-success.border-right-accent-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #aeffd9 !important;\\n}\\n\\n.overlay-success.overlay-accent-2[_ngcontent-%COMP%] {\\n  background: #aeffd9; \\n  background: rgba(174, 255, 217, 0.6);\\n}\\n\\n.text-success.text-accent-3[_ngcontent-%COMP%] {\\n  color: #7bffc1 !important;\\n}\\n\\n.bg-success.bg-accent-3[_ngcontent-%COMP%] {\\n  background-color: #7bffc1 !important;\\n}\\n\\n.border-success.border-accent-3[_ngcontent-%COMP%] {\\n  border: 1px solid #7bffc1 !important;\\n}\\n\\n.border-top-success.border-top-accent-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #7bffc1 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #7bffc1 !important;\\n}\\n\\n.border-left-success.border-left-accent-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #7bffc1 !important;\\n}\\n\\n.border-right-success.border-right-accent-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #7bffc1 !important;\\n}\\n\\n.overlay-success.overlay-accent-3[_ngcontent-%COMP%] {\\n  background: #7bffc1; \\n  background: rgba(123, 255, 193, 0.6);\\n}\\n\\n.text-success.text-accent-4[_ngcontent-%COMP%] {\\n  color: #62ffb5 !important;\\n}\\n\\n.bg-success.bg-accent-4[_ngcontent-%COMP%] {\\n  background-color: #62ffb5 !important;\\n}\\n\\n.border-success.border-accent-4[_ngcontent-%COMP%] {\\n  border: 1px solid #62ffb5 !important;\\n}\\n\\n.border-top-success.border-top-accent-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #62ffb5 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #62ffb5 !important;\\n}\\n\\n.border-left-success.border-left-accent-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #62ffb5 !important;\\n}\\n\\n.border-right-success.border-right-accent-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #62ffb5 !important;\\n}\\n\\n.overlay-success.overlay-accent-4[_ngcontent-%COMP%] {\\n  background: #62ffb5; \\n  background: rgba(98, 255, 181, 0.6);\\n}\\n\\n.text-info.text-lighten-5[_ngcontent-%COMP%] {\\n  color: #69efff !important;\\n}\\n\\n.bg-info.bg-lighten-5[_ngcontent-%COMP%] {\\n  background-color: #69efff !important;\\n}\\n\\n.border-info.border-lighten-5[_ngcontent-%COMP%] {\\n  border: 1px solid #69efff !important;\\n}\\n\\n.border-top-info.border-top-lighten-5[_ngcontent-%COMP%] {\\n  border-top: 1px solid #69efff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-5[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #69efff !important;\\n}\\n\\n.border-left-info.border-left-lighten-5[_ngcontent-%COMP%] {\\n  border-left: 1px solid #69efff !important;\\n}\\n\\n.border-right-info.border-right-lighten-5[_ngcontent-%COMP%] {\\n  border-right: 1px solid #69efff !important;\\n}\\n\\n.overlay-info.overlay-lighten-5[_ngcontent-%COMP%] {\\n  background: #69efff; \\n  background: rgba(105, 239, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-4[_ngcontent-%COMP%] {\\n  color: #4fecff !important;\\n}\\n\\n.bg-info.bg-lighten-4[_ngcontent-%COMP%] {\\n  background-color: #4fecff !important;\\n}\\n\\n.border-info.border-lighten-4[_ngcontent-%COMP%] {\\n  border: 1px solid #4fecff !important;\\n}\\n\\n.border-top-info.border-top-lighten-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #4fecff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #4fecff !important;\\n}\\n\\n.border-left-info.border-left-lighten-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #4fecff !important;\\n}\\n\\n.border-right-info.border-right-lighten-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #4fecff !important;\\n}\\n\\n.overlay-info.overlay-lighten-4[_ngcontent-%COMP%] {\\n  background: #4fecff; \\n  background: rgba(79, 236, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-3[_ngcontent-%COMP%] {\\n  color: #36e9ff !important;\\n}\\n\\n.bg-info.bg-lighten-3[_ngcontent-%COMP%] {\\n  background-color: #36e9ff !important;\\n}\\n\\n.border-info.border-lighten-3[_ngcontent-%COMP%] {\\n  border: 1px solid #36e9ff !important;\\n}\\n\\n.border-top-info.border-top-lighten-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #36e9ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #36e9ff !important;\\n}\\n\\n.border-left-info.border-left-lighten-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #36e9ff !important;\\n}\\n\\n.border-right-info.border-right-lighten-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #36e9ff !important;\\n}\\n\\n.overlay-info.overlay-lighten-3[_ngcontent-%COMP%] {\\n  background: #36e9ff; \\n  background: rgba(54, 233, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-2[_ngcontent-%COMP%] {\\n  color: #1ce7ff !important;\\n}\\n\\n.bg-info.bg-lighten-2[_ngcontent-%COMP%] {\\n  background-color: #1ce7ff !important;\\n}\\n\\n.border-info.border-lighten-2[_ngcontent-%COMP%] {\\n  border: 1px solid #1ce7ff !important;\\n}\\n\\n.border-top-info.border-top-lighten-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #1ce7ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #1ce7ff !important;\\n}\\n\\n.border-left-info.border-left-lighten-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #1ce7ff !important;\\n}\\n\\n.border-right-info.border-right-lighten-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #1ce7ff !important;\\n}\\n\\n.overlay-info.overlay-lighten-2[_ngcontent-%COMP%] {\\n  background: #1ce7ff; \\n  background: rgba(28, 231, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-1[_ngcontent-%COMP%] {\\n  color: #03e4ff !important;\\n}\\n\\n.bg-info.bg-lighten-1[_ngcontent-%COMP%] {\\n  background-color: #03e4ff !important;\\n}\\n\\n.border-info.border-lighten-1[_ngcontent-%COMP%] {\\n  border: 1px solid #03e4ff !important;\\n}\\n\\n.border-top-info.border-top-lighten-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #03e4ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #03e4ff !important;\\n}\\n\\n.border-left-info.border-left-lighten-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #03e4ff !important;\\n}\\n\\n.border-right-info.border-right-lighten-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #03e4ff !important;\\n}\\n\\n.overlay-info.overlay-lighten-1[_ngcontent-%COMP%] {\\n  background: #03e4ff; \\n  background: rgba(3, 228, 255, 0.6);\\n}\\n\\n.bg-info[_ngcontent-%COMP%] {\\n  background-color: #00cfe8 !important;\\n}\\n.bg-info[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-info[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-info[_ngcontent-%COMP%] {\\n  background: rgba(0, 207, 232, 0.12) !important;\\n  color: #00cfe8 !important;\\n}\\n.alert-info[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(0, 207, 232, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-info[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #00b8cf !important;\\n}\\n.alert-info[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #00cfe8 !important;\\n}\\n\\n.bg-light-info[_ngcontent-%COMP%] {\\n  background: rgba(0, 207, 232, 0.12) !important;\\n  color: #00cfe8 !important;\\n}\\n.bg-light-info.fc-h-event[_ngcontent-%COMP%], .bg-light-info.fc-v-event[_ngcontent-%COMP%] {\\n  border-color: rgba(0, 207, 232, 0.1);\\n}\\n.bg-light-info[_ngcontent-%COMP%]   .fc-list-event-dot[_ngcontent-%COMP%], .bg-light-info[_ngcontent-%COMP%]   .fc-daygrid-event-dot[_ngcontent-%COMP%] {\\n  border-color: #00cfe8 !important;\\n}\\n.bg-light-info.fc-list-event[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  background: rgba(0, 207, 232, 0.1) !important;\\n}\\n.bg-light-info.fc-list-event[_ngcontent-%COMP%]   .fc-list-event-title[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-info[_ngcontent-%COMP%] {\\n  color: #00cfe8 !important;\\n}\\n\\n.border-info[_ngcontent-%COMP%] {\\n  border: 1px solid #00cfe8 !important;\\n}\\n\\n.border-top-info[_ngcontent-%COMP%] {\\n  border-top: 1px solid #00cfe8;\\n}\\n\\n.border-bottom-info[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #00cfe8;\\n}\\n\\n.border-left-info[_ngcontent-%COMP%] {\\n  border-left: 1px solid #00cfe8;\\n}\\n\\n.border-right-info[_ngcontent-%COMP%] {\\n  border-right: 1px solid #00cfe8;\\n}\\n\\n.bg-info.badge-glow[_ngcontent-%COMP%], .border-info.badge-glow[_ngcontent-%COMP%], .badge-info.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #00cfe8;\\n}\\n\\n.badge.badge-light-info[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 207, 232, 0.12);\\n  color: #00cfe8 !important;\\n}\\n\\n.overlay-info[_ngcontent-%COMP%] {\\n  background: #00cfe8; \\n  background: rgba(0, 207, 232, 0.6);\\n}\\n\\n.btn-info[_ngcontent-%COMP%] {\\n  border-color: #00cfe8 !important;\\n  background-color: #00cfe8 !important;\\n  color: #fff !important;\\n}\\n.btn-info[_ngcontent-%COMP%]:focus, .btn-info[_ngcontent-%COMP%]:active, .btn-info.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #00b8cf !important;\\n}\\n.btn-info[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #00cfe8;\\n}\\n.btn-info[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-info[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #00cfe8;\\n}\\n.btn-flat-info[_ngcontent-%COMP%]:hover {\\n  color: #00cfe8;\\n}\\n.btn-flat-info[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(0, 207, 232, 0.12);\\n}\\n.btn-flat-info[_ngcontent-%COMP%]:active, .btn-flat-info.active[_ngcontent-%COMP%], .btn-flat-info[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(0, 207, 232, 0.2);\\n  color: #00cfe8;\\n}\\n.btn-flat-info.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-info[_ngcontent-%COMP%] {\\n  background-color: #00cfe8;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-info[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #03e4ff;\\n}\\n.btn-relief-info[_ngcontent-%COMP%]:active, .btn-relief-info.active[_ngcontent-%COMP%], .btn-relief-info[_ngcontent-%COMP%]:focus {\\n  background-color: #00b8cf;\\n}\\n.btn-relief-info[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-info[_ngcontent-%COMP%]:active, .btn-relief-info.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-info[_ngcontent-%COMP%] {\\n  border: 1px solid #00cfe8 !important;\\n  background-color: transparent;\\n  color: #00cfe8;\\n}\\n.btn-outline-info[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(0, 207, 232, 0.04);\\n  color: #00cfe8;\\n}\\n.btn-outline-info[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-info[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-info[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-info[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(0, 207, 232, 0.2);\\n  color: #00cfe8;\\n}\\n.btn-outline-info.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-info.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 207, 232, 0.2);\\n  color: #00cfe8;\\n}\\n\\n.btn-outline-info.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-info.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(0, 207, 232, 0.2) 0, rgba(0, 207, 232, 0.3) 40%, rgba(0, 207, 232, 0.4) 50%, rgba(0, 207, 232, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-info[_ngcontent-%COMP%] {\\n  background-color: #00cfe8;\\n}\\n\\n.modal.modal-info[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #00cfe8;\\n}\\n.modal.modal-info[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #00cfe8 !important;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: #00cfe8 !important;\\n  color: #fff;\\n}\\n.pagination-info[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.pagination-info[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #00cfe8;\\n}\\n.pagination-info[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover, .pagination-info[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #00cfe8;\\n  color: #fff;\\n}\\n.pagination-info[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-info[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after, .pagination-info[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-info[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-info[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-info[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before, .pagination-info[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-info[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-info[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #00cfe8 !important;\\n  border-color: #00cfe8;\\n  box-shadow: 0 4px 18px -4px rgba(0, 207, 232, 0.65);\\n}\\n\\n.progress-bar-info[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 207, 232, 0.12);\\n}\\n.progress-bar-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #00cfe8;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-info[_ngcontent-%COMP%] {\\n  border-color: #00cfe8 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-info[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #00cfe8 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-info.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #00cfe8 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-info.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(0, 207, 232, 0.12) !important;\\n}\\n\\n.divider.divider-info[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-info[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #00cfe8 !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-info[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #00cfe8 !important;\\n}\\n\\n.custom-control-info[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-info[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #00cfe8;\\n  background-color: #00cfe8;\\n}\\n.custom-control-info.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-info.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-info.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-info.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-info.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-info.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(0, 207, 232, 0.4) !important;\\n}\\n.custom-control-info[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(0, 207, 232, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-info[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #00cfe8 !important;\\n}\\n\\n.custom-switch-info[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #00cfe8 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-info[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #00cfe8 !important;\\n  border-color: #00cfe8 !important;\\n}\\n\\n.text-info.text-darken-1[_ngcontent-%COMP%] {\\n  color: #00b8cf !important;\\n}\\n\\n.bg-info.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #00b8cf !important;\\n}\\n\\n.border-info.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #00b8cf !important;\\n}\\n\\n.border-top-info.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #00b8cf !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #00b8cf !important;\\n}\\n\\n.border-left-info.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #00b8cf !important;\\n}\\n\\n.border-right-info.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #00b8cf !important;\\n}\\n\\n.overlay-info.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #00b8cf; \\n  background: rgba(0, 184, 207, 0.6);\\n}\\n\\n.text-info.text-darken-2[_ngcontent-%COMP%] {\\n  color: #00a1b5 !important;\\n}\\n\\n.bg-info.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #00a1b5 !important;\\n}\\n\\n.border-info.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #00a1b5 !important;\\n}\\n\\n.border-top-info.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #00a1b5 !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #00a1b5 !important;\\n}\\n\\n.border-left-info.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #00a1b5 !important;\\n}\\n\\n.border-right-info.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #00a1b5 !important;\\n}\\n\\n.overlay-info.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #00a1b5; \\n  background: rgba(0, 161, 181, 0.6);\\n}\\n\\n.text-info.text-darken-3[_ngcontent-%COMP%] {\\n  color: #008b9c !important;\\n}\\n\\n.bg-info.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #008b9c !important;\\n}\\n\\n.border-info.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #008b9c !important;\\n}\\n\\n.border-top-info.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #008b9c !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #008b9c !important;\\n}\\n\\n.border-left-info.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #008b9c !important;\\n}\\n\\n.border-right-info.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #008b9c !important;\\n}\\n\\n.overlay-info.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #008b9c; \\n  background: rgba(0, 139, 156, 0.6);\\n}\\n\\n.text-info.text-darken-4[_ngcontent-%COMP%] {\\n  color: #007482 !important;\\n}\\n\\n.bg-info.bg-darken-4[_ngcontent-%COMP%] {\\n  background-color: #007482 !important;\\n}\\n\\n.border-info.border-darken-4[_ngcontent-%COMP%] {\\n  border: 1px solid #007482 !important;\\n}\\n\\n.border-top-info.border-top-darken-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #007482 !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #007482 !important;\\n}\\n\\n.border-left-info.border-left-darken-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #007482 !important;\\n}\\n\\n.border-right-info.border-right-darken-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #007482 !important;\\n}\\n\\n.overlay-info.overlay-darken-4[_ngcontent-%COMP%] {\\n  background: #007482; \\n  background: rgba(0, 116, 130, 0.6);\\n}\\n\\n.text-info.text-accent-1[_ngcontent-%COMP%] {\\n  color: #feffff !important;\\n}\\n\\n.bg-info.bg-accent-1[_ngcontent-%COMP%] {\\n  background-color: #feffff !important;\\n}\\n\\n.border-info.border-accent-1[_ngcontent-%COMP%] {\\n  border: 1px solid #feffff !important;\\n}\\n\\n.border-top-info.border-top-accent-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #feffff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #feffff !important;\\n}\\n\\n.border-left-info.border-left-accent-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #feffff !important;\\n}\\n\\n.border-right-info.border-right-accent-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #feffff !important;\\n}\\n\\n.overlay-info.overlay-accent-1[_ngcontent-%COMP%] {\\n  background: #feffff; \\n  background: rgba(254, 255, 255, 0.6);\\n}\\n\\n.text-info.text-accent-2[_ngcontent-%COMP%] {\\n  color: #cbf5ff !important;\\n}\\n\\n.bg-info.bg-accent-2[_ngcontent-%COMP%] {\\n  background-color: #cbf5ff !important;\\n}\\n\\n.border-info.border-accent-2[_ngcontent-%COMP%] {\\n  border: 1px solid #cbf5ff !important;\\n}\\n\\n.border-top-info.border-top-accent-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #cbf5ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #cbf5ff !important;\\n}\\n\\n.border-left-info.border-left-accent-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #cbf5ff !important;\\n}\\n\\n.border-right-info.border-right-accent-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #cbf5ff !important;\\n}\\n\\n.overlay-info.overlay-accent-2[_ngcontent-%COMP%] {\\n  background: #cbf5ff; \\n  background: rgba(203, 245, 255, 0.6);\\n}\\n\\n.text-info.text-accent-3[_ngcontent-%COMP%] {\\n  color: #98ecff !important;\\n}\\n\\n.bg-info.bg-accent-3[_ngcontent-%COMP%] {\\n  background-color: #98ecff !important;\\n}\\n\\n.border-info.border-accent-3[_ngcontent-%COMP%] {\\n  border: 1px solid #98ecff !important;\\n}\\n\\n.border-top-info.border-top-accent-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #98ecff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #98ecff !important;\\n}\\n\\n.border-left-info.border-left-accent-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #98ecff !important;\\n}\\n\\n.border-right-info.border-right-accent-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #98ecff !important;\\n}\\n\\n.overlay-info.overlay-accent-3[_ngcontent-%COMP%] {\\n  background: #98ecff; \\n  background: rgba(152, 236, 255, 0.6);\\n}\\n\\n.text-info.text-accent-4[_ngcontent-%COMP%] {\\n  color: #7fe7ff !important;\\n}\\n\\n.bg-info.bg-accent-4[_ngcontent-%COMP%] {\\n  background-color: #7fe7ff !important;\\n}\\n\\n.border-info.border-accent-4[_ngcontent-%COMP%] {\\n  border: 1px solid #7fe7ff !important;\\n}\\n\\n.border-top-info.border-top-accent-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #7fe7ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #7fe7ff !important;\\n}\\n\\n.border-left-info.border-left-accent-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #7fe7ff !important;\\n}\\n\\n.border-right-info.border-right-accent-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #7fe7ff !important;\\n}\\n\\n.overlay-info.overlay-accent-4[_ngcontent-%COMP%] {\\n  background: #7fe7ff; \\n  background: rgba(127, 231, 255, 0.6);\\n}\\n\\n.text-warning.text-lighten-5[_ngcontent-%COMP%] {\\n  color: #ffe0c3 !important;\\n}\\n\\n.bg-warning.bg-lighten-5[_ngcontent-%COMP%] {\\n  background-color: #ffe0c3 !important;\\n}\\n\\n.border-warning.border-lighten-5[_ngcontent-%COMP%] {\\n  border: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-5[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-5[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-5[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-5[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffe0c3 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-5[_ngcontent-%COMP%] {\\n  background: #ffe0c3; \\n  background: rgba(255, 224, 195, 0.6);\\n}\\n\\n.text-warning.text-lighten-4[_ngcontent-%COMP%] {\\n  color: #ffd3a9 !important;\\n}\\n\\n.bg-warning.bg-lighten-4[_ngcontent-%COMP%] {\\n  background-color: #ffd3a9 !important;\\n}\\n\\n.border-warning.border-lighten-4[_ngcontent-%COMP%] {\\n  border: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffd3a9 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-4[_ngcontent-%COMP%] {\\n  background: #ffd3a9; \\n  background: rgba(255, 211, 169, 0.6);\\n}\\n\\n.text-warning.text-lighten-3[_ngcontent-%COMP%] {\\n  color: #ffc690 !important;\\n}\\n\\n.bg-warning.bg-lighten-3[_ngcontent-%COMP%] {\\n  background-color: #ffc690 !important;\\n}\\n\\n.border-warning.border-lighten-3[_ngcontent-%COMP%] {\\n  border: 1px solid #ffc690 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffc690 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffc690 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffc690 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffc690 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-3[_ngcontent-%COMP%] {\\n  background: #ffc690; \\n  background: rgba(255, 198, 144, 0.6);\\n}\\n\\n.text-warning.text-lighten-2[_ngcontent-%COMP%] {\\n  color: #ffb976 !important;\\n}\\n\\n.bg-warning.bg-lighten-2[_ngcontent-%COMP%] {\\n  background-color: #ffb976 !important;\\n}\\n\\n.border-warning.border-lighten-2[_ngcontent-%COMP%] {\\n  border: 1px solid #ffb976 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffb976 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffb976 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffb976 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffb976 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-2[_ngcontent-%COMP%] {\\n  background: #ffb976; \\n  background: rgba(255, 185, 118, 0.6);\\n}\\n\\n.text-warning.text-lighten-1[_ngcontent-%COMP%] {\\n  color: #ffac5d !important;\\n}\\n\\n.bg-warning.bg-lighten-1[_ngcontent-%COMP%] {\\n  background-color: #ffac5d !important;\\n}\\n\\n.border-warning.border-lighten-1[_ngcontent-%COMP%] {\\n  border: 1px solid #ffac5d !important;\\n}\\n\\n.border-top-warning.border-top-lighten-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffac5d !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffac5d !important;\\n}\\n\\n.border-left-warning.border-left-lighten-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffac5d !important;\\n}\\n\\n.border-right-warning.border-right-lighten-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffac5d !important;\\n}\\n\\n.overlay-warning.overlay-lighten-1[_ngcontent-%COMP%] {\\n  background: #ffac5d; \\n  background: rgba(255, 172, 93, 0.6);\\n}\\n\\n.bg-warning[_ngcontent-%COMP%] {\\n  background-color: #ff9f43 !important;\\n}\\n.bg-warning[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-warning[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-warning[_ngcontent-%COMP%] {\\n  background: rgba(255, 159, 67, 0.12) !important;\\n  color: #ff9f43 !important;\\n}\\n.alert-warning[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(255, 159, 67, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-warning[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #ff922a !important;\\n}\\n.alert-warning[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #ff9f43 !important;\\n}\\n\\n.bg-light-warning[_ngcontent-%COMP%] {\\n  background: rgba(255, 159, 67, 0.12) !important;\\n  color: #ff9f43 !important;\\n}\\n.bg-light-warning.fc-h-event[_ngcontent-%COMP%], .bg-light-warning.fc-v-event[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 159, 67, 0.1);\\n}\\n.bg-light-warning[_ngcontent-%COMP%]   .fc-list-event-dot[_ngcontent-%COMP%], .bg-light-warning[_ngcontent-%COMP%]   .fc-daygrid-event-dot[_ngcontent-%COMP%] {\\n  border-color: #ff9f43 !important;\\n}\\n.bg-light-warning.fc-list-event[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  background: rgba(255, 159, 67, 0.1) !important;\\n}\\n.bg-light-warning.fc-list-event[_ngcontent-%COMP%]   .fc-list-event-title[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-warning[_ngcontent-%COMP%] {\\n  color: #ff9f43 !important;\\n}\\n\\n.border-warning[_ngcontent-%COMP%] {\\n  border: 1px solid #ff9f43 !important;\\n}\\n\\n.border-top-warning[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ff9f43;\\n}\\n\\n.border-bottom-warning[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ff9f43;\\n}\\n\\n.border-left-warning[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ff9f43;\\n}\\n\\n.border-right-warning[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ff9f43;\\n}\\n\\n.bg-warning.badge-glow[_ngcontent-%COMP%], .border-warning.badge-glow[_ngcontent-%COMP%], .badge-warning.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #ff9f43;\\n}\\n\\n.badge.badge-light-warning[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 159, 67, 0.12);\\n  color: #ff9f43 !important;\\n}\\n\\n.overlay-warning[_ngcontent-%COMP%] {\\n  background: #ff9f43; \\n  background: rgba(255, 159, 67, 0.6);\\n}\\n\\n.btn-warning[_ngcontent-%COMP%] {\\n  border-color: #ff9f43 !important;\\n  background-color: #ff9f43 !important;\\n  color: #fff !important;\\n}\\n.btn-warning[_ngcontent-%COMP%]:focus, .btn-warning[_ngcontent-%COMP%]:active, .btn-warning.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #ff922a !important;\\n}\\n.btn-warning[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #ff9f43;\\n}\\n.btn-warning[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-warning[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #ff9f43;\\n}\\n.btn-flat-warning[_ngcontent-%COMP%]:hover {\\n  color: #ff9f43;\\n}\\n.btn-flat-warning[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(255, 159, 67, 0.12);\\n}\\n.btn-flat-warning[_ngcontent-%COMP%]:active, .btn-flat-warning.active[_ngcontent-%COMP%], .btn-flat-warning[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(255, 159, 67, 0.2);\\n  color: #ff9f43;\\n}\\n.btn-flat-warning.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-warning[_ngcontent-%COMP%] {\\n  background-color: #ff9f43;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-warning[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #ffac5d;\\n}\\n.btn-relief-warning[_ngcontent-%COMP%]:active, .btn-relief-warning.active[_ngcontent-%COMP%], .btn-relief-warning[_ngcontent-%COMP%]:focus {\\n  background-color: #ff922a;\\n}\\n.btn-relief-warning[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-warning[_ngcontent-%COMP%]:active, .btn-relief-warning.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-warning[_ngcontent-%COMP%] {\\n  border: 1px solid #ff9f43 !important;\\n  background-color: transparent;\\n  color: #ff9f43;\\n}\\n.btn-outline-warning[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(255, 159, 67, 0.04);\\n  color: #ff9f43;\\n}\\n.btn-outline-warning[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-warning[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-warning[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-warning[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(255, 159, 67, 0.2);\\n  color: #ff9f43;\\n}\\n.btn-outline-warning.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-warning.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 159, 67, 0.2);\\n  color: #ff9f43;\\n}\\n\\n.btn-outline-warning.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-warning.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(255, 159, 67, 0.2) 0, rgba(255, 159, 67, 0.3) 40%, rgba(255, 159, 67, 0.4) 50%, rgba(255, 159, 67, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-warning[_ngcontent-%COMP%] {\\n  background-color: #ff9f43;\\n}\\n\\n.modal.modal-warning[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #ff9f43;\\n}\\n.modal.modal-warning[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #ff9f43 !important;\\n}\\n\\n.pagination-warning[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: #ff9f43 !important;\\n  color: #fff;\\n}\\n.pagination-warning[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.pagination-warning[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #ff9f43;\\n}\\n.pagination-warning[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover, .pagination-warning[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #ff9f43;\\n  color: #fff;\\n}\\n.pagination-warning[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-warning[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after, .pagination-warning[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-warning[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-warning[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-warning[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before, .pagination-warning[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-warning[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-warning[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #ff9f43 !important;\\n  border-color: #ff9f43;\\n  box-shadow: 0 4px 18px -4px rgba(255, 159, 67, 0.65);\\n}\\n\\n.progress-bar-warning[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 159, 67, 0.12);\\n}\\n.progress-bar-warning[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #ff9f43;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-warning[_ngcontent-%COMP%] {\\n  border-color: #ff9f43 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-warning[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #ff9f43 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-warning.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #ff9f43 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-warning.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(255, 159, 67, 0.12) !important;\\n}\\n\\n.divider.divider-warning[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-warning[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #ff9f43 !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-warning[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #ff9f43 !important;\\n}\\n\\n.custom-control-warning[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-warning[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #ff9f43;\\n  background-color: #ff9f43;\\n}\\n.custom-control-warning.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-warning.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-warning.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-warning.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-warning.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-warning.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(255, 159, 67, 0.4) !important;\\n}\\n.custom-control-warning[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(255, 159, 67, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-warning[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #ff9f43 !important;\\n}\\n\\n.custom-switch-warning[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #ff9f43 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-warning[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #ff9f43 !important;\\n  border-color: #ff9f43 !important;\\n}\\n\\n.text-warning.text-darken-1[_ngcontent-%COMP%] {\\n  color: #ff922a !important;\\n}\\n\\n.bg-warning.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #ff922a !important;\\n}\\n\\n.border-warning.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #ff922a !important;\\n}\\n\\n.border-top-warning.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ff922a !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ff922a !important;\\n}\\n\\n.border-left-warning.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ff922a !important;\\n}\\n\\n.border-right-warning.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ff922a !important;\\n}\\n\\n.overlay-warning.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #ff922a; \\n  background: rgba(255, 146, 42, 0.6);\\n}\\n\\n.text-warning.text-darken-2[_ngcontent-%COMP%] {\\n  color: #ff8510 !important;\\n}\\n\\n.bg-warning.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #ff8510 !important;\\n}\\n\\n.border-warning.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #ff8510 !important;\\n}\\n\\n.border-top-warning.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ff8510 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ff8510 !important;\\n}\\n\\n.border-left-warning.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ff8510 !important;\\n}\\n\\n.border-right-warning.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ff8510 !important;\\n}\\n\\n.overlay-warning.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #ff8510; \\n  background: rgba(255, 133, 16, 0.6);\\n}\\n\\n.text-warning.text-darken-3[_ngcontent-%COMP%] {\\n  color: #f67800 !important;\\n}\\n\\n.bg-warning.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #f67800 !important;\\n}\\n\\n.border-warning.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #f67800 !important;\\n}\\n\\n.border-top-warning.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #f67800 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f67800 !important;\\n}\\n\\n.border-left-warning.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #f67800 !important;\\n}\\n\\n.border-right-warning.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #f67800 !important;\\n}\\n\\n.overlay-warning.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #f67800; \\n  background: rgba(246, 120, 0, 0.6);\\n}\\n\\n.text-warning.text-darken-4[_ngcontent-%COMP%] {\\n  color: #dc6c00 !important;\\n}\\n\\n.bg-warning.bg-darken-4[_ngcontent-%COMP%] {\\n  background-color: #dc6c00 !important;\\n}\\n\\n.border-warning.border-darken-4[_ngcontent-%COMP%] {\\n  border: 1px solid #dc6c00 !important;\\n}\\n\\n.border-top-warning.border-top-darken-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #dc6c00 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #dc6c00 !important;\\n}\\n\\n.border-left-warning.border-left-darken-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #dc6c00 !important;\\n}\\n\\n.border-right-warning.border-right-darken-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #dc6c00 !important;\\n}\\n\\n.overlay-warning.overlay-darken-4[_ngcontent-%COMP%] {\\n  background: #dc6c00; \\n  background: rgba(220, 108, 0, 0.6);\\n}\\n\\n.text-warning.text-accent-1[_ngcontent-%COMP%] {\\n  color: #fff5ef !important;\\n}\\n\\n.bg-warning.bg-accent-1[_ngcontent-%COMP%] {\\n  background-color: #fff5ef !important;\\n}\\n\\n.border-warning.border-accent-1[_ngcontent-%COMP%] {\\n  border: 1px solid #fff5ef !important;\\n}\\n\\n.border-top-warning.border-top-accent-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #fff5ef !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #fff5ef !important;\\n}\\n\\n.border-left-warning.border-left-accent-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #fff5ef !important;\\n}\\n\\n.border-right-warning.border-right-accent-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #fff5ef !important;\\n}\\n\\n.overlay-warning.overlay-accent-1[_ngcontent-%COMP%] {\\n  background: #fff5ef; \\n  background: rgba(255, 245, 239, 0.6);\\n}\\n\\n.text-warning.text-accent-2[_ngcontent-%COMP%] {\\n  color: #ffe5d8 !important;\\n}\\n\\n.bg-warning.bg-accent-2[_ngcontent-%COMP%] {\\n  background-color: #ffe5d8 !important;\\n}\\n\\n.border-warning.border-accent-2[_ngcontent-%COMP%] {\\n  border: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-top-warning.border-top-accent-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-left-warning.border-left-accent-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-right-warning.border-right-accent-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffe5d8 !important;\\n}\\n\\n.overlay-warning.overlay-accent-2[_ngcontent-%COMP%] {\\n  background: #ffe5d8; \\n  background: rgba(255, 229, 216, 0.6);\\n}\\n\\n.text-warning.text-accent-3[_ngcontent-%COMP%] {\\n  color: #fff6f3 !important;\\n}\\n\\n.bg-warning.bg-accent-3[_ngcontent-%COMP%] {\\n  background-color: #fff6f3 !important;\\n}\\n\\n.border-warning.border-accent-3[_ngcontent-%COMP%] {\\n  border: 1px solid #fff6f3 !important;\\n}\\n\\n.border-top-warning.border-top-accent-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #fff6f3 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #fff6f3 !important;\\n}\\n\\n.border-left-warning.border-left-accent-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #fff6f3 !important;\\n}\\n\\n.border-right-warning.border-right-accent-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #fff6f3 !important;\\n}\\n\\n.overlay-warning.overlay-accent-3[_ngcontent-%COMP%] {\\n  background: #fff6f3; \\n  background: rgba(255, 246, 243, 0.6);\\n}\\n\\n.text-warning.text-accent-4[_ngcontent-%COMP%] {\\n  color: #ffe3da !important;\\n}\\n\\n.bg-warning.bg-accent-4[_ngcontent-%COMP%] {\\n  background-color: #ffe3da !important;\\n}\\n\\n.border-warning.border-accent-4[_ngcontent-%COMP%] {\\n  border: 1px solid #ffe3da !important;\\n}\\n\\n.border-top-warning.border-top-accent-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffe3da !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffe3da !important;\\n}\\n\\n.border-left-warning.border-left-accent-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffe3da !important;\\n}\\n\\n.border-right-warning.border-right-accent-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffe3da !important;\\n}\\n\\n.overlay-warning.overlay-accent-4[_ngcontent-%COMP%] {\\n  background: #ffe3da; \\n  background: rgba(255, 227, 218, 0.6);\\n}\\n\\n.text-danger.text-lighten-5[_ngcontent-%COMP%] {\\n  color: #f8c6c6 !important;\\n}\\n\\n.bg-danger.bg-lighten-5[_ngcontent-%COMP%] {\\n  background-color: #f8c6c6 !important;\\n}\\n\\n.border-danger.border-lighten-5[_ngcontent-%COMP%] {\\n  border: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-top-danger.border-top-lighten-5[_ngcontent-%COMP%] {\\n  border-top: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-5[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-left-danger.border-left-lighten-5[_ngcontent-%COMP%] {\\n  border-left: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-right-danger.border-right-lighten-5[_ngcontent-%COMP%] {\\n  border-right: 1px solid #f8c6c6 !important;\\n}\\n\\n.overlay-danger.overlay-lighten-5[_ngcontent-%COMP%] {\\n  background: #f8c6c6; \\n  background: rgba(248, 198, 198, 0.6);\\n}\\n\\n.text-danger.text-lighten-4[_ngcontent-%COMP%] {\\n  color: #f5afaf !important;\\n}\\n\\n.bg-danger.bg-lighten-4[_ngcontent-%COMP%] {\\n  background-color: #f5afaf !important;\\n}\\n\\n.border-danger.border-lighten-4[_ngcontent-%COMP%] {\\n  border: 1px solid #f5afaf !important;\\n}\\n\\n.border-top-danger.border-top-lighten-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #f5afaf !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f5afaf !important;\\n}\\n\\n.border-left-danger.border-left-lighten-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #f5afaf !important;\\n}\\n\\n.border-right-danger.border-right-lighten-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #f5afaf !important;\\n}\\n\\n.overlay-danger.overlay-lighten-4[_ngcontent-%COMP%] {\\n  background: #f5afaf; \\n  background: rgba(245, 175, 175, 0.6);\\n}\\n\\n.text-danger.text-lighten-3[_ngcontent-%COMP%] {\\n  color: #f29899 !important;\\n}\\n\\n.bg-danger.bg-lighten-3[_ngcontent-%COMP%] {\\n  background-color: #f29899 !important;\\n}\\n\\n.border-danger.border-lighten-3[_ngcontent-%COMP%] {\\n  border: 1px solid #f29899 !important;\\n}\\n\\n.border-top-danger.border-top-lighten-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #f29899 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f29899 !important;\\n}\\n\\n.border-left-danger.border-left-lighten-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #f29899 !important;\\n}\\n\\n.border-right-danger.border-right-lighten-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #f29899 !important;\\n}\\n\\n.overlay-danger.overlay-lighten-3[_ngcontent-%COMP%] {\\n  background: #f29899; \\n  background: rgba(242, 152, 153, 0.6);\\n}\\n\\n.text-danger.text-lighten-2[_ngcontent-%COMP%] {\\n  color: #f08182 !important;\\n}\\n\\n.bg-danger.bg-lighten-2[_ngcontent-%COMP%] {\\n  background-color: #f08182 !important;\\n}\\n\\n.border-danger.border-lighten-2[_ngcontent-%COMP%] {\\n  border: 1px solid #f08182 !important;\\n}\\n\\n.border-top-danger.border-top-lighten-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #f08182 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f08182 !important;\\n}\\n\\n.border-left-danger.border-left-lighten-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #f08182 !important;\\n}\\n\\n.border-right-danger.border-right-lighten-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #f08182 !important;\\n}\\n\\n.overlay-danger.overlay-lighten-2[_ngcontent-%COMP%] {\\n  background: #f08182; \\n  background: rgba(240, 129, 130, 0.6);\\n}\\n\\n.text-danger.text-lighten-1[_ngcontent-%COMP%] {\\n  color: #ed6b6c !important;\\n}\\n\\n.bg-danger.bg-lighten-1[_ngcontent-%COMP%] {\\n  background-color: #ed6b6c !important;\\n}\\n\\n.border-danger.border-lighten-1[_ngcontent-%COMP%] {\\n  border: 1px solid #ed6b6c !important;\\n}\\n\\n.border-top-danger.border-top-lighten-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ed6b6c !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ed6b6c !important;\\n}\\n\\n.border-left-danger.border-left-lighten-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ed6b6c !important;\\n}\\n\\n.border-right-danger.border-right-lighten-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ed6b6c !important;\\n}\\n\\n.overlay-danger.overlay-lighten-1[_ngcontent-%COMP%] {\\n  background: #ed6b6c; \\n  background: rgba(237, 107, 108, 0.6);\\n}\\n\\n.bg-danger[_ngcontent-%COMP%] {\\n  background-color: #ea5455 !important;\\n}\\n.bg-danger[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .bg-danger[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  background: rgba(234, 84, 85, 0.12) !important;\\n  color: #ea5455 !important;\\n}\\n.alert-danger[_ngcontent-%COMP%]   .alert-heading[_ngcontent-%COMP%] {\\n  box-shadow: rgba(234, 84, 85, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-danger[_ngcontent-%COMP%]   .alert-link[_ngcontent-%COMP%] {\\n  color: #e73d3e !important;\\n}\\n.alert-danger[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #ea5455 !important;\\n}\\n\\n.bg-light-danger[_ngcontent-%COMP%] {\\n  background: rgba(234, 84, 85, 0.12) !important;\\n  color: #ea5455 !important;\\n}\\n.bg-light-danger.fc-h-event[_ngcontent-%COMP%], .bg-light-danger.fc-v-event[_ngcontent-%COMP%] {\\n  border-color: rgba(234, 84, 85, 0.1);\\n}\\n.bg-light-danger[_ngcontent-%COMP%]   .fc-list-event-dot[_ngcontent-%COMP%], .bg-light-danger[_ngcontent-%COMP%]   .fc-daygrid-event-dot[_ngcontent-%COMP%] {\\n  border-color: #ea5455 !important;\\n}\\n.bg-light-danger.fc-list-event[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\\n  background: rgba(234, 84, 85, 0.1) !important;\\n}\\n.bg-light-danger.fc-list-event[_ngcontent-%COMP%]   .fc-list-event-title[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-danger[_ngcontent-%COMP%] {\\n  color: #ea5455 !important;\\n}\\n\\n.border-danger[_ngcontent-%COMP%] {\\n  border: 1px solid #ea5455 !important;\\n}\\n\\n.border-top-danger[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ea5455;\\n}\\n\\n.border-bottom-danger[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ea5455;\\n}\\n\\n.border-left-danger[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ea5455;\\n}\\n\\n.border-right-danger[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ea5455;\\n}\\n\\n.bg-danger.badge-glow[_ngcontent-%COMP%], .border-danger.badge-glow[_ngcontent-%COMP%], .badge-danger.badge-glow[_ngcontent-%COMP%] {\\n  box-shadow: 0px 0px 10px #ea5455;\\n}\\n\\n.badge.badge-light-danger[_ngcontent-%COMP%] {\\n  background-color: rgba(234, 84, 85, 0.12);\\n  color: #ea5455 !important;\\n}\\n\\n.overlay-danger[_ngcontent-%COMP%] {\\n  background: #ea5455; \\n  background: rgba(234, 84, 85, 0.6);\\n}\\n\\n.btn-danger[_ngcontent-%COMP%] {\\n  border-color: #ea5455 !important;\\n  background-color: #ea5455 !important;\\n  color: #fff !important;\\n}\\n.btn-danger[_ngcontent-%COMP%]:focus, .btn-danger[_ngcontent-%COMP%]:active, .btn-danger.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #e73d3e !important;\\n}\\n.btn-danger[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #ea5455;\\n}\\n.btn-danger[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-danger[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: #ea5455;\\n}\\n.btn-flat-danger[_ngcontent-%COMP%]:hover {\\n  color: #ea5455;\\n}\\n.btn-flat-danger[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(234, 84, 85, 0.12);\\n}\\n.btn-flat-danger[_ngcontent-%COMP%]:active, .btn-flat-danger.active[_ngcontent-%COMP%], .btn-flat-danger[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(234, 84, 85, 0.2);\\n  color: #ea5455;\\n}\\n.btn-flat-danger.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-danger[_ngcontent-%COMP%] {\\n  background-color: #ea5455;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-danger[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: #ed6b6c;\\n}\\n.btn-relief-danger[_ngcontent-%COMP%]:active, .btn-relief-danger.active[_ngcontent-%COMP%], .btn-relief-danger[_ngcontent-%COMP%]:focus {\\n  background-color: #e73d3e;\\n}\\n.btn-relief-danger[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.btn-relief-danger[_ngcontent-%COMP%]:active, .btn-relief-danger.active[_ngcontent-%COMP%] {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-danger[_ngcontent-%COMP%] {\\n  border: 1px solid #ea5455 !important;\\n  background-color: transparent;\\n  color: #ea5455;\\n}\\n.btn-outline-danger[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(234, 84, 85, 0.04);\\n  color: #ea5455;\\n}\\n.btn-outline-danger[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-danger[_ngcontent-%COMP%]:not(:disabled):not(.disabled):active, .btn-outline-danger[_ngcontent-%COMP%]:not(:disabled):not(.disabled).active, .btn-outline-danger[_ngcontent-%COMP%]:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(234, 84, 85, 0.2);\\n  color: #ea5455;\\n}\\n.btn-outline-danger.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show[_ngcontent-%COMP%]    > .btn-outline-danger.dropdown-toggle[_ngcontent-%COMP%] {\\n  background-color: rgba(234, 84, 85, 0.2);\\n  color: #ea5455;\\n}\\n\\n.btn-outline-danger.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%], .btn-flat-danger.waves-effect[_ngcontent-%COMP%]   .waves-ripple[_ngcontent-%COMP%] {\\n  background: radial-gradient(rgba(234, 84, 85, 0.2) 0, rgba(234, 84, 85, 0.3) 40%, rgba(234, 84, 85, 0.4) 50%, rgba(234, 84, 85, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-danger[_ngcontent-%COMP%] {\\n  background-color: #ea5455;\\n}\\n\\n.modal.modal-danger[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #ea5455;\\n}\\n.modal.modal-danger[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  color: #ea5455 !important;\\n}\\n\\n.pagination-danger[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: #ea5455 !important;\\n  color: #fff;\\n}\\n.pagination-danger[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n}\\n.pagination-danger[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  color: #ea5455;\\n}\\n.pagination-danger[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover, .pagination-danger[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #ea5455;\\n  color: #fff;\\n}\\n.pagination-danger[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-danger[_ngcontent-%COMP%]   .page-item.next-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after, .pagination-danger[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:after, .pagination-danger[_ngcontent-%COMP%]   .page-item.next[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-danger[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-danger[_ngcontent-%COMP%]   .page-item.prev-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before, .pagination-danger[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:active:before, .pagination-danger[_ngcontent-%COMP%]   .page-item.prev[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-danger[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #ea5455 !important;\\n  border-color: #ea5455;\\n  box-shadow: 0 4px 18px -4px rgba(234, 84, 85, 0.65);\\n}\\n\\n.progress-bar-danger[_ngcontent-%COMP%] {\\n  background-color: rgba(234, 84, 85, 0.12);\\n}\\n.progress-bar-danger[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  background-color: #ea5455;\\n}\\n\\n.timeline[_ngcontent-%COMP%]   .timeline-point-danger[_ngcontent-%COMP%] {\\n  border-color: #ea5455 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-danger[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .timeline[_ngcontent-%COMP%]   .timeline-point-danger[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: #ea5455 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-danger.timeline-point-indicator[_ngcontent-%COMP%] {\\n  background-color: #ea5455 !important;\\n}\\n.timeline[_ngcontent-%COMP%]   .timeline-point-danger.timeline-point-indicator[_ngcontent-%COMP%]:before {\\n  background: rgba(234, 84, 85, 0.12) !important;\\n}\\n\\n.divider.divider-danger[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:before, .divider.divider-danger[_ngcontent-%COMP%]   .divider-text[_ngcontent-%COMP%]:after {\\n  border-color: #ea5455 !important;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus    ~ .bg-danger[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #ea5455 !important;\\n}\\n\\n.custom-control-danger[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-danger[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #ea5455;\\n  background-color: #ea5455;\\n}\\n.custom-control-danger.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-danger.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-danger.custom-checkbox[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-danger.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-danger.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:active    ~ .custom-control-label[_ngcontent-%COMP%]::before, .custom-control-danger.custom-radio[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  box-shadow: 0 2px 4px 0 rgba(234, 84, 85, 0.4) !important;\\n}\\n.custom-control-danger[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:disabled:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: rgba(234, 84, 85, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-danger[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:focus    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  border-color: #ea5455 !important;\\n}\\n\\n.custom-switch-danger[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked    ~ .custom-control-label[_ngcontent-%COMP%]::before {\\n  background-color: #ea5455 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-danger[_ngcontent-%COMP%]   .select2-container--default[_ngcontent-%COMP%]   .select2-selection--multiple[_ngcontent-%COMP%]   .select2-selection__choice[_ngcontent-%COMP%] {\\n  background: #ea5455 !important;\\n  border-color: #ea5455 !important;\\n}\\n\\n.text-danger.text-darken-1[_ngcontent-%COMP%] {\\n  color: #e73d3e !important;\\n}\\n\\n.bg-danger.bg-darken-1[_ngcontent-%COMP%] {\\n  background-color: #e73d3e !important;\\n}\\n\\n.border-danger.border-darken-1[_ngcontent-%COMP%] {\\n  border: 1px solid #e73d3e !important;\\n}\\n\\n.border-top-danger.border-top-darken-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e73d3e !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e73d3e !important;\\n}\\n\\n.border-left-danger.border-left-darken-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #e73d3e !important;\\n}\\n\\n.border-right-danger.border-right-darken-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #e73d3e !important;\\n}\\n\\n.overlay-danger.overlay-darken-1[_ngcontent-%COMP%] {\\n  background: #e73d3e; \\n  background: rgba(231, 61, 62, 0.6);\\n}\\n\\n.text-danger.text-darken-2[_ngcontent-%COMP%] {\\n  color: #e42728 !important;\\n}\\n\\n.bg-danger.bg-darken-2[_ngcontent-%COMP%] {\\n  background-color: #e42728 !important;\\n}\\n\\n.border-danger.border-darken-2[_ngcontent-%COMP%] {\\n  border: 1px solid #e42728 !important;\\n}\\n\\n.border-top-danger.border-top-darken-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e42728 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e42728 !important;\\n}\\n\\n.border-left-danger.border-left-darken-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #e42728 !important;\\n}\\n\\n.border-right-danger.border-right-darken-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #e42728 !important;\\n}\\n\\n.overlay-danger.overlay-darken-2[_ngcontent-%COMP%] {\\n  background: #e42728; \\n  background: rgba(228, 39, 40, 0.6);\\n}\\n\\n.text-danger.text-darken-3[_ngcontent-%COMP%] {\\n  color: #d71a1c !important;\\n}\\n\\n.bg-danger.bg-darken-3[_ngcontent-%COMP%] {\\n  background-color: #d71a1c !important;\\n}\\n\\n.border-danger.border-darken-3[_ngcontent-%COMP%] {\\n  border: 1px solid #d71a1c !important;\\n}\\n\\n.border-top-danger.border-top-darken-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #d71a1c !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #d71a1c !important;\\n}\\n\\n.border-left-danger.border-left-darken-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #d71a1c !important;\\n}\\n\\n.border-right-danger.border-right-darken-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #d71a1c !important;\\n}\\n\\n.overlay-danger.overlay-darken-3[_ngcontent-%COMP%] {\\n  background: #d71a1c; \\n  background: rgba(215, 26, 28, 0.6);\\n}\\n\\n.text-danger.text-darken-4[_ngcontent-%COMP%] {\\n  color: #c01819 !important;\\n}\\n\\n.bg-danger.bg-darken-4[_ngcontent-%COMP%] {\\n  background-color: #c01819 !important;\\n}\\n\\n.border-danger.border-darken-4[_ngcontent-%COMP%] {\\n  border: 1px solid #c01819 !important;\\n}\\n\\n.border-top-danger.border-top-darken-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #c01819 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #c01819 !important;\\n}\\n\\n.border-left-danger.border-left-darken-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #c01819 !important;\\n}\\n\\n.border-right-danger.border-right-darken-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #c01819 !important;\\n}\\n\\n.overlay-danger.overlay-darken-4[_ngcontent-%COMP%] {\\n  background: #c01819; \\n  background: rgba(192, 24, 25, 0.6);\\n}\\n\\n.text-danger.text-accent-1[_ngcontent-%COMP%] {\\n  color: #ffeef1 !important;\\n}\\n\\n.bg-danger.bg-accent-1[_ngcontent-%COMP%] {\\n  background-color: #ffeef1 !important;\\n}\\n\\n.border-danger.border-accent-1[_ngcontent-%COMP%] {\\n  border: 1px solid #ffeef1 !important;\\n}\\n\\n.border-top-danger.border-top-accent-1[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffeef1 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-1[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffeef1 !important;\\n}\\n\\n.border-left-danger.border-left-accent-1[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffeef1 !important;\\n}\\n\\n.border-right-danger.border-right-accent-1[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffeef1 !important;\\n}\\n\\n.overlay-danger.overlay-accent-1[_ngcontent-%COMP%] {\\n  background: #ffeef1; \\n  background: rgba(255, 238, 241, 0.6);\\n}\\n\\n.text-danger.text-accent-2[_ngcontent-%COMP%] {\\n  color: #ffd6db !important;\\n}\\n\\n.bg-danger.bg-accent-2[_ngcontent-%COMP%] {\\n  background-color: #ffd6db !important;\\n}\\n\\n.border-danger.border-accent-2[_ngcontent-%COMP%] {\\n  border: 1px solid #ffd6db !important;\\n}\\n\\n.border-top-danger.border-top-accent-2[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffd6db !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-2[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffd6db !important;\\n}\\n\\n.border-left-danger.border-left-accent-2[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffd6db !important;\\n}\\n\\n.border-right-danger.border-right-accent-2[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffd6db !important;\\n}\\n\\n.overlay-danger.overlay-accent-2[_ngcontent-%COMP%] {\\n  background: #ffd6db; \\n  background: rgba(255, 214, 219, 0.6);\\n}\\n\\n.text-danger.text-accent-3[_ngcontent-%COMP%] {\\n  color: #ffecee !important;\\n}\\n\\n.bg-danger.bg-accent-3[_ngcontent-%COMP%] {\\n  background-color: #ffecee !important;\\n}\\n\\n.border-danger.border-accent-3[_ngcontent-%COMP%] {\\n  border: 1px solid #ffecee !important;\\n}\\n\\n.border-top-danger.border-top-accent-3[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffecee !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-3[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffecee !important;\\n}\\n\\n.border-left-danger.border-left-accent-3[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffecee !important;\\n}\\n\\n.border-right-danger.border-right-accent-3[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffecee !important;\\n}\\n\\n.overlay-danger.overlay-accent-3[_ngcontent-%COMP%] {\\n  background: #ffecee; \\n  background: rgba(255, 236, 238, 0.6);\\n}\\n\\n.text-danger.text-accent-4[_ngcontent-%COMP%] {\\n  color: #ffd3d7 !important;\\n}\\n\\n.bg-danger.bg-accent-4[_ngcontent-%COMP%] {\\n  background-color: #ffd3d7 !important;\\n}\\n\\n.border-danger.border-accent-4[_ngcontent-%COMP%] {\\n  border: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-top-danger.border-top-accent-4[_ngcontent-%COMP%] {\\n  border-top: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-4[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-left-danger.border-left-accent-4[_ngcontent-%COMP%] {\\n  border-left: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-right-danger.border-right-accent-4[_ngcontent-%COMP%] {\\n  border-right: 1px solid #ffd3d7 !important;\\n}\\n\\n.overlay-danger.overlay-accent-4[_ngcontent-%COMP%] {\\n  background: #ffd3d7; \\n  background: rgba(255, 211, 215, 0.6);\\n}\\n\\n.bg-gradient-dark[_ngcontent-%COMP%], .btn-gradient-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #4b4b4b, #626262);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .bg-gradient-dark[_ngcontent-%COMP%], .dark-layout[_ngcontent-%COMP%]   .btn-gradient-dark[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(91.47deg, #626262, #4b4b4b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-dark[_ngcontent-%COMP%]:hover, .bg-gradient-dark[_ngcontent-%COMP%]:active, .btn-gradient-dark[_ngcontent-%COMP%]:hover, .btn-gradient-dark[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-dark[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-dark[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-dark[_ngcontent-%COMP%]:active, .btn-gradient-dark[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-dark[_ngcontent-%COMP%]:active, .bg-gradient-dark[_ngcontent-%COMP%]:focus, .btn-gradient-dark[_ngcontent-%COMP%]:active, .btn-gradient-dark[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #626262, #4b4b4b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-primary[_ngcontent-%COMP%], .btn-gradient-primary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #2472ea, #1150b1);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-primary[_ngcontent-%COMP%]:hover, .bg-gradient-primary[_ngcontent-%COMP%]:active, .btn-gradient-primary[_ngcontent-%COMP%]:hover, .btn-gradient-primary[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-primary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-primary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-primary[_ngcontent-%COMP%]:active, .btn-gradient-primary[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-primary[_ngcontent-%COMP%]:active, .bg-gradient-primary[_ngcontent-%COMP%]:focus, .btn-gradient-primary[_ngcontent-%COMP%]:active, .btn-gradient-primary[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #0a306b, #1150b1);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-secondary[_ngcontent-%COMP%], .btn-gradient-secondary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #aaacb0, #82868b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-secondary[_ngcontent-%COMP%]:hover, .bg-gradient-secondary[_ngcontent-%COMP%]:active, .btn-gradient-secondary[_ngcontent-%COMP%]:hover, .btn-gradient-secondary[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-secondary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-secondary[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-secondary[_ngcontent-%COMP%]:active, .btn-gradient-secondary[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-secondary[_ngcontent-%COMP%]:active, .bg-gradient-secondary[_ngcontent-%COMP%]:focus, .btn-gradient-secondary[_ngcontent-%COMP%]:active, .btn-gradient-secondary[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #5d6064, #82868b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-success[_ngcontent-%COMP%], .btn-gradient-success[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #5dde97, #28c76f);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-success[_ngcontent-%COMP%]:hover, .bg-gradient-success[_ngcontent-%COMP%]:active, .btn-gradient-success[_ngcontent-%COMP%]:hover, .btn-gradient-success[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-success[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-success[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-success[_ngcontent-%COMP%]:active, .btn-gradient-success[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-success[_ngcontent-%COMP%]:active, .bg-gradient-success[_ngcontent-%COMP%]:focus, .btn-gradient-success[_ngcontent-%COMP%]:active, .btn-gradient-success[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #1b874b, #28c76f);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-info[_ngcontent-%COMP%], .btn-gradient-info[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #36e9ff, #00cfe8);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-info[_ngcontent-%COMP%]:hover, .bg-gradient-info[_ngcontent-%COMP%]:active, .btn-gradient-info[_ngcontent-%COMP%]:hover, .btn-gradient-info[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-info[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-info[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-info[_ngcontent-%COMP%]:active, .btn-gradient-info[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-info[_ngcontent-%COMP%]:active, .bg-gradient-info[_ngcontent-%COMP%]:focus, .btn-gradient-info[_ngcontent-%COMP%]:active, .btn-gradient-info[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #008b9c, #00cfe8);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-warning[_ngcontent-%COMP%], .btn-gradient-warning[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #ffc690, #ff9f43);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-warning[_ngcontent-%COMP%]:hover, .bg-gradient-warning[_ngcontent-%COMP%]:active, .btn-gradient-warning[_ngcontent-%COMP%]:hover, .btn-gradient-warning[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-warning[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-warning[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-warning[_ngcontent-%COMP%]:active, .btn-gradient-warning[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-warning[_ngcontent-%COMP%]:active, .bg-gradient-warning[_ngcontent-%COMP%]:focus, .btn-gradient-warning[_ngcontent-%COMP%]:active, .btn-gradient-warning[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #f67800, #ff9f43);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-danger[_ngcontent-%COMP%], .btn-gradient-danger[_ngcontent-%COMP%] {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #f29899, #ea5455);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-danger[_ngcontent-%COMP%]:hover, .bg-gradient-danger[_ngcontent-%COMP%]:active, .btn-gradient-danger[_ngcontent-%COMP%]:hover, .btn-gradient-danger[_ngcontent-%COMP%]:active {\\n  color: #fff;\\n}\\n.bg-gradient-danger[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled), .btn-gradient-danger[_ngcontent-%COMP%]:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-danger[_ngcontent-%COMP%]:active, .btn-gradient-danger[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-danger[_ngcontent-%COMP%]:active, .bg-gradient-danger[_ngcontent-%COMP%]:focus, .btn-gradient-danger[_ngcontent-%COMP%]:active, .btn-gradient-danger[_ngcontent-%COMP%]:focus {\\n  background-image: linear-gradient(91.47deg, #d71a1c, #ea5455);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.ng-select.ng-select-focused[_ngcontent-%COMP%] {\\n  outline: 0;\\n  box-shadow: 0 3px 10px 0 rgba(34, 41, 47, 0.1);\\n}\\n.ng-select.ng-select-focused[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%] {\\n  border-color: #1150b1 !important;\\n  z-index: 2000 !important;\\n  box-shadow: none !important;\\n  color: #000 !important;\\n  min-height: 38px !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%] {\\n  color: #000 !important;\\n  min-height: 38px !important;\\n}\\n.ng-select.error[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%] {\\n  border-color: #ea5455 !important;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%] {\\n  background-color: #1150b1 !important;\\n  color: #fff;\\n  border: none !important;\\n  font-size: 0.8rem !important;\\n  border-radius: 4px !important;\\n  display: flex;\\n  align-items: center;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.right[_ngcontent-%COMP%] {\\n  border: 0 !important;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.left[_ngcontent-%COMP%] {\\n  border: 0 !important;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon[_ngcontent-%COMP%]:hover {\\n  background-color: transparent !important;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.left[_ngcontent-%COMP%] {\\n  font-size: 1.1rem !important;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.right[_ngcontent-%COMP%] {\\n  font-size: 1.1rem !important;\\n}\\n.ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%] {\\n  top: 8px !important;\\n}\\n.ng-select.ng-select-size-lg[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%] {\\n  min-height: 48px;\\n  font-size: 1.2rem !important;\\n}\\n.ng-select.ng-select-size-lg[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%] {\\n  font-size: 1.2rem !important;\\n  padding: 7px;\\n}\\n.ng-select.ng-select-size-lg[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.left[_ngcontent-%COMP%] {\\n  font-size: 1.1rem !important;\\n}\\n.ng-select.ng-select-size-lg[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.right[_ngcontent-%COMP%] {\\n  font-size: 1.1rem !important;\\n}\\n.ng-select.ng-select-size-lg[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-clear-wrapper[_ngcontent-%COMP%] {\\n  height: 22px !important;\\n}\\n.ng-select.ng-select-size-sm[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%] {\\n  min-height: 28px !important;\\n  font-size: 0.75rem;\\n}\\n.ng-select.ng-select-size-sm[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%] {\\n  padding: 0px;\\n  font-size: 0.9em !important;\\n}\\n.ng-select.ng-select-size-sm[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.left[_ngcontent-%COMP%] {\\n  font-size: 0.9em !important;\\n}\\n.ng-select.ng-select-size-sm[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%]   .ng-value-icon.right[_ngcontent-%COMP%] {\\n  font-size: 0.9em !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-option.ng-option-selected[_ngcontent-%COMP%] {\\n  background-color: #1150b1 !important;\\n  color: #fff !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-option.ng-option-selected.ng-option-marked[_ngcontent-%COMP%] {\\n  background-color: #1150b1 !important;\\n  color: #fff !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-option.ng-option-selected[_ngcontent-%COMP%]   .ng-option-label[_ngcontent-%COMP%] {\\n  font-weight: inherit !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-option.ng-option-marked[_ngcontent-%COMP%] {\\n  background-color: rgba(17, 80, 177, 0.12) !important;\\n  color: #1150b1 !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-option.ng-option-disabled[_ngcontent-%COMP%] {\\n  color: #b9b9c3 !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-arrow[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaBAMAAABbZFH9AAAAG1BMVEUAAACRkZGRkZGSkpKRkZGSkpKSkpKRkZGRkZHLso+9AAAACHRSTlMA+1JoWo0vLFQDmmkAAABlSURBVBjTY6ALSACTbBAOazOYsggAUxEdBkCSuaMVxGGX6BABUo4djQUgrmJHhwFQqkMIrJJJoqOZwaKjUQHIhkg6g6QggEWiQ7Cj0QHIgkpCpaA6wbrgkiAphKSgArJTXRhoBgB9GRPswyvBqAAAAABJRU5ErkJggg==\\\");\\n  background-size: 12px 12px, 10px 10px;\\n  background-repeat: no-repeat;\\n  height: 0.8rem !important;\\n  padding-right: 1.5rem;\\n  margin-left: 0;\\n  margin-top: 0;\\n  left: 0;\\n  border-style: none !important;\\n}\\n.ng-select.ng-select-opened[_ngcontent-%COMP%]    > .ng-select-container[_ngcontent-%COMP%]   .ng-arrow[_ngcontent-%COMP%] {\\n  top: 0px !important;\\n}\\n.ng-select[_ngcontent-%COMP%]   .ng-clear-wrapper[_ngcontent-%COMP%] {\\n  height: 18px;\\n}\\n\\n.dark-layout[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%] {\\n  background-color: #283046;\\n  border-color: #3b4253;\\n  color: #676d7d;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-select-container[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%] {\\n  color: #676d7d !important;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-value[_ngcontent-%COMP%] {\\n  background-color: rgba(17, 80, 177, 0.12) !important;\\n  color: #1150b1 !important;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-dropdown-header[_ngcontent-%COMP%] {\\n  background-color: #161d31;\\n  border-color: #3b4253;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-dropdown-footer[_ngcontent-%COMP%] {\\n  background-color: #161d31;\\n  border-color: #3b4253;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-select.ng-select-opened[_ngcontent-%COMP%]    > .ng-select-container[_ngcontent-%COMP%] {\\n  background-color: #161d31;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-option[_ngcontent-%COMP%] {\\n  background-color: #283046 !important;\\n  color: #b4b7bd !important;\\n}\\n.dark-layout[_ngcontent-%COMP%]   .ng-option.ng-option-disabled[_ngcontent-%COMP%] {\\n  color: #676d7d !important;\\n}\\n.dark-layout[_ngcontent-%COMP%]   ng-dropdown-panel[_ngcontent-%COMP%] {\\n  border-color: #3b4253 !important;\\n}\\n.dark-layout[_ngcontent-%COMP%]   ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%] {\\n  background-color: #161d31 !important;\\n}\\n.dark-layout[_ngcontent-%COMP%]   ng-dropdown-panel[_ngcontent-%COMP%]   .ng-dropdown-panel-items[_ngcontent-%COMP%]   .ng-optgroup[_ngcontent-%COMP%] {\\n  color: #676d7d !important;\\n}\\n\\n.step-trigger[_ngcontent-%COMP%] {\\n  justify-content: start !important;\\n}\\n/*# sourceMappingURL=bs-stepper.min.css.map */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAO1C,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,OAAO,MAAM,YAAY;;;;;;;;;;;;;;AAKhC;AAOA,OAAM,MAAOC,iBAAiB;EAQ5BC,YACSC,eAAgC,EAChCC,YAAyB,EACzBC,eAA+B,EAC/BC,MAAwB,EACvBC,cAA6B,EAC9BC,eAA+B,EAC/BC,aAAoB,EACpBC,oBAAyC;IAPzC,KAAAP,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IAd7B,KAAAZ,SAAS,GAAGA,SAAS;IACrB,KAAAa,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,CAAC;IAyEf,KAAAC,wBAAwB,GAAG,IAAIhB,SAAS,CAAC,EAAE,CAAC;IAC5C,KAAAiB,yBAAyB,GAAQ,EAAE;IACnC,KAAAC,2BAA2B,GAAsB,EAAE;IACnD,KAAAC,0BAA0B,GAAwB,CAChD;MACEC,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE;MACV;MACA;QACEC,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,OAAO;QACbC,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE;UACLC,OAAO,EAAE,cAAc;UACvBC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE;;OAEhB,EACD;QACER,SAAS,EAAE,QAAQ;QACnBE,GAAG,EAAE,SAAS;QACdD,IAAI,EAAE,WAAW;QACjBE,KAAK,EAAE;UACLI,QAAQ,EAAE,IAAI;UACdF,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACsB,OAAO,CAAC,SAAS,CAAC;UACrCC,MAAM,EAAE;YACNC,OAAO,EAAE,CACP,SAAS,EACT,GAAG,EACH,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,GAAG,EACH,UAAU,EACV,WAAW,EACX,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,UAAU,EACV,GAAG,EACH,MAAM,EACN,MAAM,EACN,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW,EACX,GAAG,EACH,aAAa,EACb,YAAY,EACZ,aAAa,CACd;YACD;YACAH,WAAW,EAAE,uBAAuB;YACpCI,WAAW,EAAE;cACXC,KAAK,EAAE,CACL;gBACEC,IAAI,EAAE,IAAI;gBACVC,UAAU,EAAE,KAAK;gBACjBC,OAAO,EAAE,KAAK;gBACdC,MAAM,EAAE;eACT;aAEJ;YACDC,SAAS,EAAE;cACTC,YAAY,EAAE;aACf;YACDC,OAAO,EAAE;cACPC,KAAK,EAAE,CACL;gBACEC,MAAM,EAAE,GAAG;gBACXC,iBAAiB,EAAE;eACpB;;;;OAKV;KAEJ,CACF;IAED,KAAAC,UAAU,GAAG,IAAI9C,SAAS,CAAC,EAAE,CAAC;IAC9B,KAAA+C,WAAW,GAAQ,EAAE;IACrB,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAAC,YAAY,GAAwB,CAClC;MACE7B,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE;MACV;MACA;QACEC,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,OAAO;QACbC,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE;UACLC,OAAO,EAAE,cAAc;UACvBC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE;;OAEhB,EACD;QACER,SAAS,EAAE,QAAQ;QACnBE,GAAG,EAAE,SAAS;QACdD,IAAI,EAAE,WAAW;QACjBE,KAAK,EAAE;UACLI,QAAQ,EAAE,IAAI;UACdF,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACsB,OAAO,CAAC,SAAS,CAAC;UACrCC,MAAM,EAAE;YACNC,OAAO,EAAE,CACP,SAAS,EACT,GAAG,EACH,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,GAAG,EACH,UAAU,EACV,WAAW,EACX,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,UAAU,EACV,GAAG,EACH,MAAM,EACN,MAAM,EACN,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW,EACX,GAAG,EACH,aAAa,EACb,YAAY,EACZ,aAAa,CACd;YACD;YACAH,WAAW,EAAE,uBAAuB;YACpCI,WAAW,EAAE;cACXC,KAAK,EAAE,CACL;gBACEC,IAAI,EAAE,IAAI;gBACVC,UAAU,EAAE,KAAK;gBACjBC,OAAO,EAAE,KAAK;gBACdC,MAAM,EAAE;eACT;aAEJ;YACDC,SAAS,EAAE;cACTC,YAAY,EAAE;aACf;YACDC,OAAO,EAAE;cACPC,KAAK,EAAE,CACL;gBACEC,MAAM,EAAE,GAAG;gBACXC,iBAAiB,EAAE;eACpB;;;;OAKV;KAEJ,CACF;IAED;IAEA,KAAAK,YAAY,GAAG,IAAIlD,SAAS,CAAC,EAAE,CAAC;IAChC,KAAAmD,aAAa,GAAQ,EAAE;IACvB,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAC,cAAc,GAAwB,CACpC;MACEjC,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE;MACV;MACA;QACEC,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,OAAO;QACbC,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE;UACLC,OAAO,EAAE,cAAc;UACvBC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE;;OAEhB,EACD;QACER,SAAS,EAAE,QAAQ;QACnBE,GAAG,EAAE,SAAS;QACdD,IAAI,EAAE,WAAW;QACjBE,KAAK,EAAE;UACLI,QAAQ,EAAE,IAAI;UACdF,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACsB,OAAO,CAAC,SAAS,CAAC;UACrCC,MAAM,EAAE;YACNC,OAAO,EAAE,CACP,SAAS,EACT,GAAG,EACH,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,GAAG,EACH,UAAU,EACV,WAAW,EACX,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,UAAU,EACV,GAAG,EACH,MAAM,EACN,MAAM,EACN,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW,EACX,GAAG,EACH,aAAa,EACb,YAAY,EACZ,aAAa,CACd;YACD;YACAH,WAAW,EAAE,uBAAuB;YACpCI,WAAW,EAAE;cACXC,KAAK,EAAE,CACL;gBACEC,IAAI,EAAE,IAAI;gBACVC,UAAU,EAAE,KAAK;gBACjBC,OAAO,EAAE,KAAK;gBACdC,MAAM,EAAE;eACT;aAEJ;YACDC,SAAS,EAAE;cACTC,YAAY,EAAE;aACf;YACDC,OAAO,EAAE;cACPC,KAAK,EAAE,CACL;gBACEC,MAAM,EAAE,GAAG;gBACXC,iBAAiB,EAAE;eACpB;;;;OAKV;KAEJ,CACF;IAED;IACA,KAAAS,YAAY,GAAG,IAAItD,SAAS,CAAC,EAAE,CAAC;IAChC,KAAAuD,aAAa,GAAQ,EAAE;IACvB,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAC,cAAc,GAAwB,CACpC;MACErC,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE;MACV;MACA;QACEC,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,OAAO;QACbC,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE;UACLC,OAAO,EAAE,cAAc;UACvBC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE;;OAEhB,EACD;QACER,SAAS,EAAE,QAAQ;QACnBE,GAAG,EAAE,SAAS;QACdD,IAAI,EAAE,WAAW;QACjBE,KAAK,EAAE;UACLI,QAAQ,EAAE,IAAI;UACdF,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACsB,OAAO,CAAC,SAAS,CAAC;UACrCC,MAAM,EAAE;YACNC,OAAO,EAAE,CACP,SAAS,EACT,GAAG,EACH,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,GAAG,EACH,UAAU,EACV,WAAW,EACX,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,UAAU,EACV,GAAG,EACH,MAAM,EACN,MAAM,EACN,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW,EACX,GAAG,EACH,aAAa,EACb,YAAY,EACZ,aAAa,CACd;YACD;YACAH,WAAW,EAAE,uBAAuB;YACpCI,WAAW,EAAE;cACXC,KAAK,EAAE,CACL;gBACEC,IAAI,EAAE,IAAI;gBACVC,UAAU,EAAE,KAAK;gBACjBC,OAAO,EAAE,KAAK;gBACdC,MAAM,EAAE;eACT;aAEJ;YACDC,SAAS,EAAE;cACTC,YAAY,EAAE;aACf;YACDC,OAAO,EAAE;cACPC,KAAK,EAAE,CACL;gBACEC,MAAM,EAAE,GAAG;gBACXC,iBAAiB,EAAE;eACpB;;;;OAKV;KAEJ,CACF;IAhaC,IAAI,CAACjC,aAAa,CAAC8C,QAAQ,CAAC,UAAU,CAAC;IACvC,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACnD,MAAM,CAACsB,OAAO,CAAC,UAAU,CAAC;MAC5C8B,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVvC,IAAI,EAAE,EAAE;QACRwC,KAAK,EAAE,CACL;UACE3B,IAAI,EAAE,UAAU;UAChB4B,MAAM,EAAE;SACT,EACD;UACE5B,IAAI,EAAE,UAAU;UAChB4B,MAAM,EAAE;SACT;;KAGN;IAEDrD,eAAe,CAACsD,IAAI,EAAE;IAEtB,IAAI,CAAC3D,eAAe,CAAC4D,eAAe,EAAE,CAACC,SAAS,CAAEC,GAAG,IAAI;MACvD,IAAI,CAACC,QAAQ,GAAGD,GAAG;MAEnB;MACA,IAAIE,mBAAmB,GAAG,IAAI,CAACD,QAAQ,CAACE,IAAI,CACzCC,OAAO,IAAKA,OAAO,CAAChD,GAAG,IAAIvB,SAAS,CAACwE,aAAa,CAACC,mBAAmB,CACxE;MACD,IAAI,CAACzD,yBAAyB,GAAGqD,mBAAmB,CAACK,KAAK;MAE1D;MACA,IAAIC,KAAK,GAAG,IAAI,CAACP,QAAQ,CAACE,IAAI,CAC3BC,OAAO,IAAKA,OAAO,CAAChD,GAAG,IAAIvB,SAAS,CAACwE,aAAa,CAACI,KAAK,CAC1D;MACD,IAAI,CAAC9B,WAAW,GAAG6B,KAAK,CAACD,KAAK;MAE9B;MACA,IAAIG,cAAc,GAAG,IAAI,CAACT,QAAQ,CAACE,IAAI,CACpCC,OAAO,IAAKA,OAAO,CAAChD,GAAG,IAAIvB,SAAS,CAACwE,aAAa,CAACM,OAAO,CAC5D;MACD,IAAI,CAAC5B,aAAa,GAAG2B,cAAc,CAACH,KAAK;MAEzC;MACA,IAAIK,eAAe,GAAG,IAAI,CAACX,QAAQ,CAACE,IAAI,CACrCC,OAAO,IAAKA,OAAO,CAAChD,GAAG,IAAIvB,SAAS,CAACwE,aAAa,CAACQ,OAAO,CAC5D;MACD,IAAI,CAAC1B,aAAa,GAAGyB,eAAe,CAACL,KAAK;MAE1CO,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAAC,CAAC,IAAI,CAAC5E,YAAY,CAAC6E,gBAAgB,CAACC,IAAI,CAACC,WAAW,CAACf,IAAI,CAAEgB,CAAC,IAAKA,CAAC,CAACC,EAAE,IAAIvF,SAAS,CAACwF,WAAW,CAACC,eAAe,CAAC,EAAE;MACpH,IAAI,CAACpF,eAAe,CAACqF,WAAW,EAAE;;EAEtC;EAyWAC,QAAQA,CAAA,GAAK;EAEbT,WAAWA,CAAA;IACT,IAAI,IAAI,CAACU,qBAAqB,EAAE;MAC9B,IAAI,CAAC/E,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC+E,qBAAqB,CAACC,OAAO,EAAE;;IAGtC,IAAI,CAACD,qBAAqB,GAAG,IAAI1F,OAAO,CACtC4F,QAAQ,CAACC,aAAa,CAAC,WAAW,CAAC,EACnC;MACEC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE;KACZ,CACF;IAED,IAAI,CAACL,qBAAqB,CAACM,EAAE,CAAC,IAAI,CAACpF,WAAW,CAAC;IAC/C;IACA,MAAMuD,mBAAmB,GAAGyB,QAAQ,CAACK,cAAc,CAAC,qBAAqB,CAAC;IAC1E;IACA9B,mBAAmB,CAAC+B,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IAC5C,IAAI,CAACxF,UAAU,GAAG,IAAI;EACxB;EAEAyF,WAAWA,CAAC/E,GAAG,EAAEgF,KAAK,EAAEC,IAAI;IAC1B,IAAIA,IAAI,CAACC,OAAO,EAAE;MAChB;;IAEF,IAAI,CAACpG,eAAe,CAACqG,cAAc,CAACnF,GAAG,EAAEgF,KAAK,CAAC,CAACrC,SAAS,CACtDC,GAAG,IAAI;MACN,IAAI,CAAC1D,cAAc,CAACkG,OAAO,CACzB,IAAI,CAACnG,MAAM,CAACsB,OAAO,CAAC,qBAAqB,CAAC,EAC1C,IAAI,CAACtB,MAAM,CAACsB,OAAO,CAAC,SAAS,CAAC,EAC9B;QACE8E,UAAU,EAAE,kBAAkB;QAC9BC,WAAW,EAAE;OACd,CACF;IACH,CAAC,EACAC,GAAG,IAAI;MACN7G,IAAI,CAAC8G,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,IAAI,CAACzG,MAAM,CAACsB,OAAO,CAAC,OAAO,CAAC;QACnCoF,IAAI,EAAEJ,GAAG,CAACK;OACX,CAAC;IACJ,CAAC,CACF;EACH;EAEAC,YAAYA,CAACC,IAAY;IACvB,IAAI,CAACvG,WAAW,GAAGuG,IAAI;EACzB;EAAC,QAAAC,CAAA;qBAteUnH,iBAAiB,EAAAoH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,KAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAe,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA;UAAjBtI,iBAAiB;IAAAuI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtB9BzB,EAAA,CAAA2B,cAAA,iBAAiC;QAC/B3B,EAAA,CAAA4B,SAAA,4BAAyE;QACzE5B,EAAA,CAAA2B,cAAA,aAAuE;QAM/D3B,EAAA,CAAA6B,UAAA,mBAAAC,mDAAA;UAAA,OAASJ,GAAA,CAAA7B,YAAA,CAAa,CAAC,CAAC;QAAA,EAAC;QAGzBG,EAAA,CAAA2B,cAAA,cACG;QAAA3B,EAAA,CAAA4B,SAAA,WACF;QAAA5B,EAAA,CAAA+B,YAAA,EAAO;QACR/B,EAAA,CAAA2B,cAAA,cAA+B;QACE3B,EAAA,CAAAgC,MAAA,IAAwB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAC9D/B,EAAA,CAAA2B,cAAA,gBAAkC;QAAA3B,EAAA,CAAAgC,MAAA,IAEhC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAIf/B,EAAA,CAAA2B,cAAA,eAAuC;QAInC3B,EAAA,CAAA6B,UAAA,mBAAAI,oDAAA;UAAA,OAASP,GAAA,CAAA7B,YAAA,CAAa,CAAC,CAAC;QAAA,EAAC;QAGzBG,EAAA,CAAA2B,cAAA,eAA6B;QAAA3B,EAAA,CAAA4B,SAAA,aAAiC;QAAA5B,EAAA,CAAA+B,YAAA,EAAO;QACrE/B,EAAA,CAAA2B,cAAA,eAA+B;QACE3B,EAAA,CAAAgC,MAAA,IAAyB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAC/D/B,EAAA,CAAA2B,cAAA,gBAAkC;QAAA3B,EAAA,CAAAgC,MAAA,IAEhC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAIf/B,EAAA,CAAA2B,cAAA,eAAyC;QAIrC3B,EAAA,CAAA6B,UAAA,mBAAAK,oDAAA;UAAA,OAASR,GAAA,CAAA7B,YAAA,CAAa,CAAC,CAAC;QAAA,EAAC;QAGzBG,EAAA,CAAA2B,cAAA,eAA6B;QAAA3B,EAAA,CAAA4B,SAAA,aAA+B;QAAA5B,EAAA,CAAA+B,YAAA,EAAO;QACnE/B,EAAA,CAAA2B,cAAA,eAA+B;QACE3B,EAAA,CAAAgC,MAAA,IAE7B;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QACT/B,EAAA,CAAA2B,cAAA,gBAAkC;QAAA3B,EAAA,CAAAgC,MAAA,IAEhC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAIf/B,EAAA,CAAA2B,cAAA,eAAyC;QAIrC3B,EAAA,CAAA6B,UAAA,mBAAAM,oDAAA;UAAA,OAAST,GAAA,CAAA7B,YAAA,CAAa,CAAC,CAAC;QAAA,EAAC;QAGzBG,EAAA,CAAA2B,cAAA,eACG;QAAA3B,EAAA,CAAA4B,SAAA,aACF;QAAA5B,EAAA,CAAA+B,YAAA,EAAO;QACR/B,EAAA,CAAA2B,cAAA,eAA+B;QACE3B,EAAA,CAAAgC,MAAA,IAE7B;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QACT/B,EAAA,CAAA2B,cAAA,gBAAkC;QAAA3B,EAAA,CAAAgC,MAAA,IAEhC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAKjB/B,EAAA,CAAA2B,cAAA,eAAgC;QAIxB3B,EAAA,CAAAgC,MAAA,IAIF;;QAAAhC,EAAA,CAAA+B,YAAA,EAAK;QACL/B,EAAA,CAAA2B,cAAA,aAAO;QAAA3B,EAAA,CAAAgC,MAAA,IAEL;;QAAAhC,EAAA,CAAA+B,YAAA,EAAQ;QAEZ/B,EAAA,CAAA2B,cAAA,gBAUC;QAPC3B,EAAA,CAAA6B,UAAA,sBAAAO,qDAAA;UAAA,OACeV,GAAA,CAAA3C,WAAA,CAAA2C,GAAA,CAAAjJ,SAAA,CAAAwE,aAAA,CAAAC,mBAAA,EAAAwE,GAAA,CAAAjI,yBAAA,EAAAiI,GAAA,CAAAlI,wBAAA,CAKlB;QAAA,EAAI;QAEDwG,EAAA,CAAA4B,SAAA,uBAOc;QACd5B,EAAA,CAAA2B,cAAA,eAA4C;QAEb3B,EAAA,CAAAgC,MAAA,IAAwB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAMlE/B,EAAA,CAAA2B,cAAA,eAAgC;QAEX3B,EAAA,CAAAgC,MAAA,IAAyB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAK;QAC/C/B,EAAA,CAAA2B,cAAA,aAAO;QAAA3B,EAAA,CAAAgC,MAAA,IAAqC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAQ;QAEtD/B,EAAA,CAAA2B,cAAA,gBAMC;QAHC3B,EAAA,CAAA6B,UAAA,sBAAAQ,qDAAA;UAAA,OACeX,GAAA,CAAA3C,WAAA,CAAA2C,GAAA,CAAAjJ,SAAA,CAAAwE,aAAA,CAAAI,KAAA,EAAAqE,GAAA,CAAAnG,WAAA,EAAAmG,GAAA,CAAApG,UAAA,CACd;QAAA;QAED0E,EAAA,CAAA4B,SAAA,uBAMe;QAEf5B,EAAA,CAAA2B,cAAA,eAA4C;QAEb3B,EAAA,CAAAgC,MAAA,IAAwB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAMlE/B,EAAA,CAAA2B,cAAA,eAAkC;QAEb3B,EAAA,CAAAgC,MAAA,IAAkC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAK;QACxD/B,EAAA,CAAA2B,cAAA,aAAO;QAAA3B,EAAA,CAAAgC,MAAA,IAAuD;;QAAAhC,EAAA,CAAA+B,YAAA,EAAQ;QAExE/B,EAAA,CAAA2B,cAAA,gBAUC;QAPC3B,EAAA,CAAA6B,UAAA,sBAAAS,qDAAA;UAAA,OACeZ,GAAA,CAAA3C,WAAA,CAAA2C,GAAA,CAAAjJ,SAAA,CAAAwE,aAAA,CAAAM,OAAA,EAAAmE,GAAA,CAAA/F,aAAA,EAAA+F,GAAA,CAAAhG,YAAA,CAKlB;QAAA,EAAI;QAEDsE,EAAA,CAAA4B,SAAA,uBAMe;QAEf5B,EAAA,CAAA2B,cAAA,eAA4C;QAEb3B,EAAA,CAAAgC,MAAA,IAAwB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;QAMlE/B,EAAA,CAAA2B,cAAA,eAAkC;QAEb3B,EAAA,CAAAgC,MAAA,IAAmC;;QAAAhC,EAAA,CAAA+B,YAAA,EAAK;QACzD/B,EAAA,CAAA2B,cAAA,aAAO;QAAA3B,EAAA,CAAAgC,MAAA,KAA4C;;QAAAhC,EAAA,CAAA+B,YAAA,EAAQ;QAE7D/B,EAAA,CAAA2B,cAAA,iBAUC;QAPC3B,EAAA,CAAA6B,UAAA,sBAAAU,sDAAA;UAAA,OACeb,GAAA,CAAA3C,WAAA,CAAA2C,GAAA,CAAAjJ,SAAA,CAAAwE,aAAA,CAAAQ,OAAA,EAAAiE,GAAA,CAAA3F,aAAA,EAAA2F,GAAA,CAAA5F,YAAA,CAKlB;QAAA,EAAI;QAEDkE,EAAA,CAAA4B,SAAA,wBAMe;QAEf5B,EAAA,CAAA2B,cAAA,gBAA4C;QAEb3B,EAAA,CAAAgC,MAAA,KAAwB;;QAAAhC,EAAA,CAAA+B,YAAA,EAAO;;;QA1MlD/B,EAAA,CAAAwC,SAAA,GAA+B;QAA/BxC,EAAA,CAAAyC,UAAA,kBAAAf,GAAA,CAAAvF,aAAA,CAA+B;QAcV6D,EAAA,CAAAwC,SAAA,GAAwB;QAAxBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,iBAAwB;QACrB3C,EAAA,CAAAwC,SAAA,GAEhC;QAFgCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,gDAEhC;QAa6B3C,EAAA,CAAAwC,SAAA,GAAyB;QAAzBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,kBAAyB;QACtB3C,EAAA,CAAAwC,SAAA,GAEhC;QAFgCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,2BAEhC;QAa6B3C,EAAA,CAAAwC,SAAA,GAE7B;QAF6BxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,2BAE7B;QACgC3C,EAAA,CAAAwC,SAAA,GAEhC;QAFgCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,uCAEhC;QAe6B3C,EAAA,CAAAwC,SAAA,GAE7B;QAF6BxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,4BAE7B;QACgC3C,EAAA,CAAAwC,SAAA,GAEhC;QAFgCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,qCAEhC;QASF3C,EAAA,CAAAwC,SAAA,GAIF;QAJExC,EAAA,CAAA4C,kBAAA,MAAA5C,EAAA,CAAA2C,WAAA,iFAIF;QACO3C,EAAA,CAAAwC,SAAA,GAEL;QAFKxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,yDAEL;QAGF3C,EAAA,CAAAwC,SAAA,GAAsC;QAAtCxC,EAAA,CAAAyC,UAAA,cAAAf,GAAA,CAAAlI,wBAAA,CAAsC;QAWpCwG,EAAA,CAAAwC,SAAA,GAAmC;QAAnCxC,EAAA,CAAAyC,UAAA,UAAAf,GAAA,CAAAjI,yBAAA,CAAmC,WAAAiI,GAAA,CAAA/H,0BAAA,aAAA+H,GAAA,CAAAhI,2BAAA,UAAAgI,GAAA,CAAAlI,wBAAA;QASNwG,EAAA,CAAAwC,SAAA,GAAwB;QAAxBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,iBAAwB;QAQtC3C,EAAA,CAAAwC,SAAA,GAAyB;QAAzBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,kBAAyB;QACnC3C,EAAA,CAAAwC,SAAA,GAAqC;QAArCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,8BAAqC;QAG5C3C,EAAA,CAAAwC,SAAA,GAAwB;QAAxBxC,EAAA,CAAAyC,UAAA,cAAAf,GAAA,CAAApG,UAAA,CAAwB;QAOtB0E,EAAA,CAAAwC,SAAA,GAAqB;QAArBxC,EAAA,CAAAyC,UAAA,UAAAf,GAAA,CAAAnG,WAAA,CAAqB,WAAAmG,GAAA,CAAAjG,YAAA,aAAAiG,GAAA,CAAAlG,aAAA,UAAAkG,GAAA,CAAApG,UAAA;QASQ0E,EAAA,CAAAwC,SAAA,GAAwB;QAAxBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,iBAAwB;QAQtC3C,EAAA,CAAAwC,SAAA,GAAkC;QAAlCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,2BAAkC;QAC5C3C,EAAA,CAAAwC,SAAA,GAAuD;QAAvDxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,gDAAuD;QAG9D3C,EAAA,CAAAwC,SAAA,GAA0B;QAA1BxC,EAAA,CAAAyC,UAAA,cAAAf,GAAA,CAAAhG,YAAA,CAA0B;QAWxBsE,EAAA,CAAAwC,SAAA,GAAuB;QAAvBxC,EAAA,CAAAyC,UAAA,UAAAf,GAAA,CAAA/F,aAAA,CAAuB,WAAA+F,GAAA,CAAA7F,cAAA,aAAA6F,GAAA,CAAA9F,eAAA,UAAA8F,GAAA,CAAAhG,YAAA;QASMsE,EAAA,CAAAwC,SAAA,GAAwB;QAAxBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,iBAAwB;QAQtC3C,EAAA,CAAAwC,SAAA,GAAmC;QAAnCxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,4BAAmC;QAC7C3C,EAAA,CAAAwC,SAAA,GAA4C;QAA5CxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,sCAA4C;QAGnD3C,EAAA,CAAAwC,SAAA,GAA0B;QAA1BxC,EAAA,CAAAyC,UAAA,cAAAf,GAAA,CAAA5F,YAAA,CAA0B;QAWxBkE,EAAA,CAAAwC,SAAA,GAAuB;QAAvBxC,EAAA,CAAAyC,UAAA,UAAAf,GAAA,CAAA3F,aAAA,CAAuB,WAAA2F,GAAA,CAAAzF,cAAA,aAAAyF,GAAA,CAAA1F,eAAA,UAAA0F,GAAA,CAAA5F,YAAA;QASMkE,EAAA,CAAAwC,SAAA,GAAwB;QAAxBxC,EAAA,CAAA0C,iBAAA,CAAA1C,EAAA,CAAA2C,WAAA,kBAAwB", "names": ["FormGroup", "AppConfig", "<PERSON><PERSON>", "Stepper", "ContentsComponent", "constructor", "settingsService", "_authService", "_commonsService", "_trans", "_toastrService", "_loadingService", "_titleService", "_sendMessagesService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentStep", "policy_notification_form", "policy_notification_model", "policy_notification_options", "policy_notification_fields", "fieldGroupClassName", "fieldGroup", "className", "type", "key", "props", "pattern", "translate", "label", "required", "placeholder", "instant", "config", "toolbar", "htmlSupport", "allow", "name", "attributes", "classes", "styles", "htmlEmbed", "showPreviews", "mention", "feeds", "marker", "minimumCharacters", "about_form", "about_model", "about_options", "about_fields", "weather_form", "weather_model", "weather_options", "weather_fields", "conduct_form", "conduct_model", "conduct_options", "conduct_fields", "setTitle", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "show", "getSettingsData", "subscribe", "res", "settings", "policy_notification", "find", "element", "SETTINGS_KEYS", "POLICY_NOTIFICATION", "value", "about", "ABOUT", "weather_policy", "WEATHER", "code_of_conduct", "CONDUCT", "setTimeout", "initStepper", "currentUserValue", "role", "permissions", "x", "id", "PERMISSIONS", "manage_settings", "getSettings", "ngOnInit", "verticalWizardStepper", "destroy", "document", "querySelector", "linear", "animation", "to", "getElementById", "classList", "remove", "saveSetting", "model", "form", "invalid", "updateSettings", "success", "toastClass", "closeButton", "err", "fire", "icon", "title", "text", "message", "onChangeStep", "step", "_", "i0", "ɵɵdirectiveInject", "i1", "SettingsService", "i2", "AuthService", "i3", "CommonsService", "i4", "TranslateService", "i5", "ToastrService", "i6", "LoadingService", "i7", "Title", "i8", "SendMessagesService", "_2", "selectors", "decls", "vars", "consts", "template", "ContentsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵlistener", "ContentsComponent_Template_button_click_5_listener", "ɵɵelementEnd", "ɵɵtext", "ContentsComponent_Template_button_click_16_listener", "ContentsComponent_Template_button_click_27_listener", "ContentsComponent_Template_button_click_38_listener", "ContentsComponent_Template_form_ngSubmit_57_listener", "ContentsComponent_Template_form_ngSubmit_72_listener", "ContentsComponent_Template_form_ngSubmit_87_listener", "ContentsComponent_Template_form_ngSubmit_102_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\contents\\contents.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\contents\\contents.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport Swal from 'sweetalert2';\r\nimport Stepper from 'bs-stepper';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { SendMessagesService } from 'app/services/send-messages.service';\r\n\r\n// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';\r\n\r\n@Component({\r\n  selector: 'app-contents',\r\n  templateUrl: './contents.component.html',\r\n  styleUrls: ['./contents.component.scss'],\r\n})\r\nexport class ContentsComponent implements OnInit {\r\n  settings: any;\r\n  AppConfig = AppConfig;\r\n  steperInit = false;\r\n  currentStep = 0;\r\n\r\n  public contentHeader: object;\r\n\r\n  constructor(\r\n    public settingsService: SettingsService,\r\n    public _authService: AuthService,\r\n    public _commonsService: CommonsService,\r\n    public _trans: TranslateService,\r\n    private _toastrService: ToastrService,\r\n    public _loadingService: LoadingService,\r\n    public _titleService: Title,\r\n    public _sendMessagesService: SendMessagesService\r\n  ) {\r\n    this._titleService.setTitle('Contents');\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Contents'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Settings',\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: 'Contents',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    _loadingService.show();\r\n\r\n    this.settingsService.getSettingsData().subscribe((res) => {\r\n      this.settings = res;\r\n\r\n      // policy_notification model\r\n      let policy_notification = this.settings.find(\r\n        (element) => element.key == AppConfig.SETTINGS_KEYS.POLICY_NOTIFICATION\r\n      );\r\n      this.policy_notification_model = policy_notification.value;\r\n\r\n      // about model\r\n      let about = this.settings.find(\r\n        (element) => element.key == AppConfig.SETTINGS_KEYS.ABOUT\r\n      );\r\n      this.about_model = about.value;\r\n\r\n      // weather policy model\r\n      let weather_policy = this.settings.find(\r\n        (element) => element.key == AppConfig.SETTINGS_KEYS.WEATHER\r\n      );\r\n      this.weather_model = weather_policy.value;\r\n\r\n      // code of conduct model\r\n      let code_of_conduct = this.settings.find(\r\n        (element) => element.key == AppConfig.SETTINGS_KEYS.CONDUCT\r\n      );\r\n      this.conduct_model = code_of_conduct.value;\r\n\r\n      setTimeout(() => {\r\n        this.initStepper();\r\n      }, 1000);\r\n    });\r\n\r\n    if (!!this._authService.currentUserValue.role.permissions.find((x) => x.id == AppConfig.PERMISSIONS.manage_settings)) {\r\n      this.settingsService.getSettings();\r\n    }\r\n  }\r\n\r\n  private verticalWizardStepper: Stepper;\r\n  policy_notification_form = new FormGroup({});\r\n  policy_notification_model: any = {};\r\n  policy_notification_options: FormlyFormOptions = {};\r\n  policy_notification_fields: FormlyFieldConfig[] = [\r\n    {\r\n      fieldGroupClassName: 'row',\r\n      fieldGroup: [\r\n        // show title and body\r\n        {\r\n          className: 'col-11',\r\n          type: 'input',\r\n          key: 'title',\r\n          props: {\r\n            pattern: '^(?![ ]+$).+',\r\n            translate: true,\r\n            label: 'Title',\r\n            required: true,\r\n            placeholder: 'Title',\r\n          },\r\n        },\r\n        {\r\n          className: 'col-12',\r\n          key: 'content',\r\n          type: 'ckeditor5',\r\n          props: {\r\n            required: true,\r\n            translate: true,\r\n            label: this._trans.instant('Content'),\r\n            config: {\r\n              toolbar: [\r\n                'heading',\r\n                '|',\r\n                'bold',\r\n                'underline',\r\n                'italic',\r\n                'link',\r\n                '|',\r\n                'fontSize',\r\n                'fontColor',\r\n                'fontBackgroundColor',\r\n                'fontFamily',\r\n                'specialCharacters',\r\n                'removeFormat',\r\n                'findAndReplace',\r\n                'bulletedList',\r\n                'numberedList',\r\n                'todoList',\r\n                '|',\r\n                'undo',\r\n                'redo',\r\n                '|',\r\n                'outdent',\r\n                'indent',\r\n                'alignment',\r\n                '|',\r\n                'imageInsert',\r\n                'blockQuote',\r\n                'insertTable',\r\n              ],\r\n              // simpleUpload: this._commonsService.simpleUploadConfig,\r\n              placeholder: 'Type the content here',\r\n              htmlSupport: {\r\n                allow: [\r\n                  {\r\n                    name: /.*/,\r\n                    attributes: false,\r\n                    classes: false,\r\n                    styles: false,\r\n                  },\r\n                ],\r\n              },\r\n              htmlEmbed: {\r\n                showPreviews: true,\r\n              },\r\n              mention: {\r\n                feeds: [\r\n                  {\r\n                    marker: '{',\r\n                    minimumCharacters: 1,\r\n                  },\r\n                ],\r\n              },\r\n            },\r\n          },\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  about_form = new FormGroup({});\r\n  about_model: any = {};\r\n  about_options: FormlyFormOptions = {};\r\n  about_fields: FormlyFieldConfig[] = [\r\n    {\r\n      fieldGroupClassName: 'row',\r\n      fieldGroup: [\r\n        // show title and body\r\n        {\r\n          className: 'col-11',\r\n          type: 'input',\r\n          key: 'title',\r\n          props: {\r\n            pattern: '^(?![ ]+$).+',\r\n            translate: true,\r\n            label: 'Title',\r\n            required: true,\r\n            placeholder: 'Title',\r\n          },\r\n        },\r\n        {\r\n          className: 'col-12',\r\n          key: 'content',\r\n          type: 'ckeditor5',\r\n          props: {\r\n            required: true,\r\n            translate: true,\r\n            label: this._trans.instant('Content'),\r\n            config: {\r\n              toolbar: [\r\n                'heading',\r\n                '|',\r\n                'bold',\r\n                'underline',\r\n                'italic',\r\n                'link',\r\n                '|',\r\n                'fontSize',\r\n                'fontColor',\r\n                'fontBackgroundColor',\r\n                'fontFamily',\r\n                'specialCharacters',\r\n                'removeFormat',\r\n                'findAndReplace',\r\n                'bulletedList',\r\n                'numberedList',\r\n                'todoList',\r\n                '|',\r\n                'undo',\r\n                'redo',\r\n                '|',\r\n                'outdent',\r\n                'indent',\r\n                'alignment',\r\n                '|',\r\n                'imageInsert',\r\n                'blockQuote',\r\n                'insertTable',\r\n              ],\r\n              // simpleUpload: this._commonsService.simpleUploadConfig,\r\n              placeholder: 'Type the content here',\r\n              htmlSupport: {\r\n                allow: [\r\n                  {\r\n                    name: /.*/,\r\n                    attributes: false,\r\n                    classes: false,\r\n                    styles: false,\r\n                  },\r\n                ],\r\n              },\r\n              htmlEmbed: {\r\n                showPreviews: true,\r\n              },\r\n              mention: {\r\n                feeds: [\r\n                  {\r\n                    marker: '{',\r\n                    minimumCharacters: 1,\r\n                  },\r\n                ],\r\n              },\r\n            },\r\n          },\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // weather policy form\r\n\r\n  weather_form = new FormGroup({});\r\n  weather_model: any = {};\r\n  weather_options: FormlyFormOptions = {};\r\n  weather_fields: FormlyFieldConfig[] = [\r\n    {\r\n      fieldGroupClassName: 'row',\r\n      fieldGroup: [\r\n        // show title and body\r\n        {\r\n          className: 'col-11',\r\n          type: 'input',\r\n          key: 'title',\r\n          props: {\r\n            pattern: '^(?![ ]+$).+',\r\n            translate: true,\r\n            label: 'Title',\r\n            required: true,\r\n            placeholder: 'Title',\r\n          },\r\n        },\r\n        {\r\n          className: 'col-12',\r\n          key: 'content',\r\n          type: 'ckeditor5',\r\n          props: {\r\n            required: true,\r\n            translate: true,\r\n            label: this._trans.instant('Content'),\r\n            config: {\r\n              toolbar: [\r\n                'heading',\r\n                '|',\r\n                'bold',\r\n                'underline',\r\n                'italic',\r\n                'link',\r\n                '|',\r\n                'fontSize',\r\n                'fontColor',\r\n                'fontBackgroundColor',\r\n                'fontFamily',\r\n                'specialCharacters',\r\n                'removeFormat',\r\n                'findAndReplace',\r\n                'bulletedList',\r\n                'numberedList',\r\n                'todoList',\r\n                '|',\r\n                'undo',\r\n                'redo',\r\n                '|',\r\n                'outdent',\r\n                'indent',\r\n                'alignment',\r\n                '|',\r\n                'imageInsert',\r\n                'blockQuote',\r\n                'insertTable',\r\n              ],\r\n              // simpleUpload: this._commonsService.simpleUploadConfig,\r\n              placeholder: 'Type the content here',\r\n              htmlSupport: {\r\n                allow: [\r\n                  {\r\n                    name: /.*/,\r\n                    attributes: false,\r\n                    classes: false,\r\n                    styles: false,\r\n                  },\r\n                ],\r\n              },\r\n              htmlEmbed: {\r\n                showPreviews: true,\r\n              },\r\n              mention: {\r\n                feeds: [\r\n                  {\r\n                    marker: '{',\r\n                    minimumCharacters: 1,\r\n                  },\r\n                ],\r\n              },\r\n            },\r\n          },\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // code of conduct form\r\n  conduct_form = new FormGroup({});\r\n  conduct_model: any = {};\r\n  conduct_options: FormlyFormOptions = {};\r\n  conduct_fields: FormlyFieldConfig[] = [\r\n    {\r\n      fieldGroupClassName: 'row',\r\n      fieldGroup: [\r\n        // show title and body\r\n        {\r\n          className: 'col-11',\r\n          type: 'input',\r\n          key: 'title',\r\n          props: {\r\n            pattern: '^(?![ ]+$).+',\r\n            translate: true,\r\n            label: 'Title',\r\n            required: true,\r\n            placeholder: 'Title',\r\n          },\r\n        },\r\n        {\r\n          className: 'col-12',\r\n          key: 'content',\r\n          type: 'ckeditor5',\r\n          props: {\r\n            required: true,\r\n            translate: true,\r\n            label: this._trans.instant('Content'),\r\n            config: {\r\n              toolbar: [\r\n                'heading',\r\n                '|',\r\n                'bold',\r\n                'underline',\r\n                'italic',\r\n                'link',\r\n                '|',\r\n                'fontSize',\r\n                'fontColor',\r\n                'fontBackgroundColor',\r\n                'fontFamily',\r\n                'specialCharacters',\r\n                'removeFormat',\r\n                'findAndReplace',\r\n                'bulletedList',\r\n                'numberedList',\r\n                'todoList',\r\n                '|',\r\n                'undo',\r\n                'redo',\r\n                '|',\r\n                'outdent',\r\n                'indent',\r\n                'alignment',\r\n                '|',\r\n                'imageInsert',\r\n                'blockQuote',\r\n                'insertTable',\r\n              ],\r\n              // simpleUpload: this._commonsService.simpleUploadConfig,\r\n              placeholder: 'Type the content here',\r\n              htmlSupport: {\r\n                allow: [\r\n                  {\r\n                    name: /.*/,\r\n                    attributes: false,\r\n                    classes: false,\r\n                    styles: false,\r\n                  },\r\n                ],\r\n              },\r\n              htmlEmbed: {\r\n                showPreviews: true,\r\n              },\r\n              mention: {\r\n                feeds: [\r\n                  {\r\n                    marker: '{',\r\n                    minimumCharacters: 1,\r\n                  },\r\n                ],\r\n              },\r\n            },\r\n          },\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n  ngOnInit() { }\r\n\r\n  initStepper() {\r\n    if (this.verticalWizardStepper) {\r\n      this.steperInit = false;\r\n      this.verticalWizardStepper.destroy();\r\n    }\r\n\r\n    this.verticalWizardStepper = new Stepper(\r\n      document.querySelector('#stepper2'),\r\n      {\r\n        linear: false,\r\n        animation: true,\r\n      }\r\n    );\r\n\r\n    this.verticalWizardStepper.to(this.currentStep);\r\n    // get #system-versions\r\n    const policy_notification = document.getElementById('policy_notification');\r\n    // remove class fade\r\n    policy_notification.classList.remove('fade');\r\n    this.steperInit = true;\r\n  }\r\n\r\n  saveSetting(key, model, form) {\r\n    if (form.invalid) {\r\n      return;\r\n    }\r\n    this.settingsService.updateSettings(key, model).subscribe(\r\n      (res) => {\r\n        this._toastrService.success(\r\n          this._trans.instant('Update successfully'),\r\n          this._trans.instant('Success'),\r\n          {\r\n            toastClass: 'toast ngx-toastr',\r\n            closeButton: true,\r\n          }\r\n        );\r\n      },\r\n      (err) => {\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: this._trans.instant('Error'),\r\n          text: err.message,\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onChangeStep(step: number) {\r\n    this.currentStep = step;\r\n  }\r\n}\r\n", "<section class=\"vertical-wizard\">\r\n  <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n  <div id=\"stepper2\" class=\"bs-stepper vertical vertical-wizard-example\">\r\n    <div class=\"bs-stepper-header\">\r\n      <div class=\"step\" data-target=\"#policy_notification\">\r\n        <button\r\n          type=\"button\"\r\n          class=\"step-trigger\"\r\n          (click)=\"onChangeStep(1)\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <span class=\"bs-stepper-box\"\r\n            ><i class=\"bi bi-send-plus-fill\"></i\r\n          ></span>\r\n          <span class=\"bs-stepper-label\">\r\n            <span class=\"bs-stepper-title\">{{ 'PICS' | translate }}</span>\r\n            <span class=\"bs-stepper-subtitle\">{{\r\n              'Terms of Service and Privacy Policy' | translate\r\n            }}</span>\r\n          </span>\r\n        </button>\r\n      </div>\r\n      <div class=\"step\" data-target=\"#about\">\r\n        <button\r\n          type=\"button\"\r\n          class=\"step-trigger\"\r\n          (click)=\"onChangeStep(2)\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <span class=\"bs-stepper-box\"><i class=\"bi bi-info-square\"></i></span>\r\n          <span class=\"bs-stepper-label\">\r\n            <span class=\"bs-stepper-title\">{{ 'About' | translate }}</span>\r\n            <span class=\"bs-stepper-subtitle\">{{\r\n              'About of HKJFL' | translate\r\n            }}</span>\r\n          </span>\r\n        </button>\r\n      </div>\r\n      <div class=\"step\" data-target=\"#weather\">\r\n        <button\r\n          type=\"button\"\r\n          class=\"step-trigger\"\r\n          (click)=\"onChangeStep(3)\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <span class=\"bs-stepper-box\"><i class=\"bi bi-cloud-fog\"></i></span>\r\n          <span class=\"bs-stepper-label\">\r\n            <span class=\"bs-stepper-title\">{{\r\n              'Weather Policy' | translate\r\n            }}</span>\r\n            <span class=\"bs-stepper-subtitle\">{{\r\n              'Adverse weather procedures' | translate\r\n            }}</span>\r\n          </span>\r\n        </button>\r\n      </div>\r\n      <div class=\"step\" data-target=\"#conduct\">\r\n        <button\r\n          type=\"button\"\r\n          class=\"step-trigger\"\r\n          (click)=\"onChangeStep(4)\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <span class=\"bs-stepper-box\"\r\n            ><i class=\"bi bi-file-earmark-text\"></i\r\n          ></span>\r\n          <span class=\"bs-stepper-label\">\r\n            <span class=\"bs-stepper-title\">{{\r\n              'Code of conduct' | translate\r\n            }}</span>\r\n            <span class=\"bs-stepper-subtitle\">{{\r\n              'Code of conduct settings' | translate\r\n            }}</span>\r\n          </span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"bs-stepper-content\">\r\n      <div id=\"policy_notification\" class=\"content\">\r\n        <div class=\"content-header\">\r\n          <h5 class=\"mb-0\">\r\n            {{\r\n              'Terms & Conditions and Privacy Information Collection Statement'\r\n                | translate\r\n            }}\r\n          </h5>\r\n          <small>{{\r\n            'Terms of Service and Privacy Policy settings' | translate\r\n          }}</small>\r\n        </div>\r\n        <form\r\n          [formGroup]=\"policy_notification_form\"\r\n          style=\"width: 99%\"\r\n          (ngSubmit)=\"\r\n            saveSetting(\r\n              AppConfig.SETTINGS_KEYS.POLICY_NOTIFICATION,\r\n              policy_notification_model,\r\n              policy_notification_form\r\n            )\r\n          \"\r\n        >\r\n          <formly-form\r\n            [model]=\"policy_notification_model\"\r\n            style=\"\"\r\n            [fields]=\"policy_notification_fields\"\r\n            [options]=\"policy_notification_options\"\r\n            [form]=\"policy_notification_form\"\r\n          >\r\n          </formly-form>\r\n          <div class=\"d-flex justify-content-between\">\r\n            <button type=\"submit\" class=\"btn btn-primary btn-next\" rippleEffect>\r\n              <span class=\"align-middle\">{{ 'Save' | translate }}</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <div id=\"about\" class=\"content\">\r\n        <div class=\"content-header\">\r\n          <h5 class=\"mb-0\">{{ 'About' | translate }}</h5>\r\n          <small>{{ 'About of settings' | translate }}</small>\r\n        </div>\r\n        <form\r\n          [formGroup]=\"about_form\"\r\n          style=\"width: 99%\"\r\n          (ngSubmit)=\"\r\n            saveSetting(AppConfig.SETTINGS_KEYS.ABOUT, about_model, about_form)\r\n          \"\r\n        >\r\n          <formly-form\r\n            [model]=\"about_model\"\r\n            style=\"\"\r\n            [fields]=\"about_fields\"\r\n            [options]=\"about_options\"\r\n            [form]=\"about_form\"\r\n          ></formly-form>\r\n\r\n          <div class=\"d-flex justify-content-between\">\r\n            <button type=\"submit\" class=\"btn btn-primary btn-next\" rippleEffect>\r\n              <span class=\"align-middle\">{{ 'Save' | translate }}</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <div id=\"weather\" class=\"content\">\r\n        <div class=\"content-header\">\r\n          <h5 class=\"mb-0\">{{ 'Weather Policy' | translate }}</h5>\r\n          <small>{{ 'Adverse weather procedures settings' | translate }}</small>\r\n        </div>\r\n        <form\r\n          [formGroup]=\"weather_form\"\r\n          style=\"width: 99%\"\r\n          (ngSubmit)=\"\r\n            saveSetting(\r\n              AppConfig.SETTINGS_KEYS.WEATHER,\r\n              weather_model,\r\n              weather_form\r\n            )\r\n          \"\r\n        >\r\n          <formly-form\r\n            [model]=\"weather_model\"\r\n            style=\"\"\r\n            [fields]=\"weather_fields\"\r\n            [options]=\"weather_options\"\r\n            [form]=\"weather_form\"\r\n          ></formly-form>\r\n\r\n          <div class=\"d-flex justify-content-between\">\r\n            <button type=\"submit\" class=\"btn btn-primary btn-next\" rippleEffect>\r\n              <span class=\"align-middle\">{{ 'Save' | translate }}</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <div id=\"conduct\" class=\"content\">\r\n        <div class=\"content-header\">\r\n          <h5 class=\"mb-0\">{{ 'Code of conduct' | translate }}</h5>\r\n          <small>{{ 'Code of conduct settings' | translate }}</small>\r\n        </div>\r\n        <form\r\n          [formGroup]=\"conduct_form\"\r\n          style=\"width: 99%\"\r\n          (ngSubmit)=\"\r\n            saveSetting(\r\n              AppConfig.SETTINGS_KEYS.CONDUCT,\r\n              conduct_model,\r\n              conduct_form\r\n            )\r\n          \"\r\n        >\r\n          <formly-form\r\n            [model]=\"conduct_model\"\r\n            style=\"\"\r\n            [fields]=\"conduct_fields\"\r\n            [options]=\"conduct_options\"\r\n            [form]=\"conduct_form\"\r\n          ></formly-form>\r\n\r\n          <div class=\"d-flex justify-content-between\">\r\n            <button type=\"submit\" class=\"btn btn-primary btn-next\" rippleEffect>\r\n              <span class=\"align-middle\">{{ 'Save' | translate }}</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
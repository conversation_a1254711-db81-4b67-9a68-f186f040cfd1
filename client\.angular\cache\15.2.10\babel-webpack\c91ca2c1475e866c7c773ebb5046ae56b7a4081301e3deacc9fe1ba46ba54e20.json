{"ast": null, "code": "import { Observable } from '../Observable';\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrObservable, scheduler) {\n  let resultSelector;\n  let initialState;\n  if (arguments.length == 1) {\n    const options = initialStateOrOptions;\n    initialState = options.initialState;\n    condition = options.condition;\n    iterate = options.iterate;\n    resultSelector = options.resultSelector || identity;\n    scheduler = options.scheduler;\n  } else if (resultSelectorOrObservable === undefined || isScheduler(resultSelectorOrObservable)) {\n    initialState = initialStateOrOptions;\n    resultSelector = identity;\n    scheduler = resultSelectorOrObservable;\n  } else {\n    initialState = initialStateOrOptions;\n    resultSelector = resultSelectorOrObservable;\n  }\n  return new Observable(subscriber => {\n    let state = initialState;\n    if (scheduler) {\n      return scheduler.schedule(dispatch, 0, {\n        subscriber,\n        iterate,\n        condition,\n        resultSelector,\n        state\n      });\n    }\n    do {\n      if (condition) {\n        let conditionResult;\n        try {\n          conditionResult = condition(state);\n        } catch (err) {\n          subscriber.error(err);\n          return undefined;\n        }\n        if (!conditionResult) {\n          subscriber.complete();\n          break;\n        }\n      }\n      let value;\n      try {\n        value = resultSelector(state);\n      } catch (err) {\n        subscriber.error(err);\n        return undefined;\n      }\n      subscriber.next(value);\n      if (subscriber.closed) {\n        break;\n      }\n      try {\n        state = iterate(state);\n      } catch (err) {\n        subscriber.error(err);\n        return undefined;\n      }\n    } while (true);\n    return undefined;\n  });\n}\nfunction dispatch(state) {\n  const {\n    subscriber,\n    condition\n  } = state;\n  if (subscriber.closed) {\n    return undefined;\n  }\n  if (state.needIterate) {\n    try {\n      state.state = state.iterate(state.state);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n  } else {\n    state.needIterate = true;\n  }\n  if (condition) {\n    let conditionResult;\n    try {\n      conditionResult = condition(state.state);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n    if (!conditionResult) {\n      subscriber.complete();\n      return undefined;\n    }\n    if (subscriber.closed) {\n      return undefined;\n    }\n  }\n  let value;\n  try {\n    value = state.resultSelector(state.state);\n  } catch (err) {\n    subscriber.error(err);\n    return undefined;\n  }\n  if (subscriber.closed) {\n    return undefined;\n  }\n  subscriber.next(value);\n  if (subscriber.closed) {\n    return undefined;\n  }\n  return this.schedule(state);\n}", "map": {"version": 3, "names": ["Observable", "identity", "isScheduler", "generate", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrObservable", "scheduler", "resultSelector", "initialState", "arguments", "length", "options", "undefined", "subscriber", "state", "schedule", "dispatch", "conditionResult", "err", "error", "complete", "value", "next", "closed", "needIterate"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/generate.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrObservable, scheduler) {\n    let resultSelector;\n    let initialState;\n    if (arguments.length == 1) {\n        const options = initialStateOrOptions;\n        initialState = options.initialState;\n        condition = options.condition;\n        iterate = options.iterate;\n        resultSelector = options.resultSelector || identity;\n        scheduler = options.scheduler;\n    }\n    else if (resultSelectorOrObservable === undefined || isScheduler(resultSelectorOrObservable)) {\n        initialState = initialStateOrOptions;\n        resultSelector = identity;\n        scheduler = resultSelectorOrObservable;\n    }\n    else {\n        initialState = initialStateOrOptions;\n        resultSelector = resultSelectorOrObservable;\n    }\n    return new Observable(subscriber => {\n        let state = initialState;\n        if (scheduler) {\n            return scheduler.schedule(dispatch, 0, {\n                subscriber,\n                iterate,\n                condition,\n                resultSelector,\n                state\n            });\n        }\n        do {\n            if (condition) {\n                let conditionResult;\n                try {\n                    conditionResult = condition(state);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return undefined;\n                }\n                if (!conditionResult) {\n                    subscriber.complete();\n                    break;\n                }\n            }\n            let value;\n            try {\n                value = resultSelector(state);\n            }\n            catch (err) {\n                subscriber.error(err);\n                return undefined;\n            }\n            subscriber.next(value);\n            if (subscriber.closed) {\n                break;\n            }\n            try {\n                state = iterate(state);\n            }\n            catch (err) {\n                subscriber.error(err);\n                return undefined;\n            }\n        } while (true);\n        return undefined;\n    });\n}\nfunction dispatch(state) {\n    const { subscriber, condition } = state;\n    if (subscriber.closed) {\n        return undefined;\n    }\n    if (state.needIterate) {\n        try {\n            state.state = state.iterate(state.state);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n    }\n    else {\n        state.needIterate = true;\n    }\n    if (condition) {\n        let conditionResult;\n        try {\n            conditionResult = condition(state.state);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        if (!conditionResult) {\n            subscriber.complete();\n            return undefined;\n        }\n        if (subscriber.closed) {\n            return undefined;\n        }\n    }\n    let value;\n    try {\n        value = state.resultSelector(state.state);\n    }\n    catch (err) {\n        subscriber.error(err);\n        return undefined;\n    }\n    if (subscriber.closed) {\n        return undefined;\n    }\n    subscriber.next(value);\n    if (subscriber.closed) {\n        return undefined;\n    }\n    return this.schedule(state);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,SAASC,QAAQA,CAACC,qBAAqB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,0BAA0B,EAAEC,SAAS,EAAE;EACvG,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,IAAIC,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE;IACvB,MAAMC,OAAO,GAAGT,qBAAqB;IACrCM,YAAY,GAAGG,OAAO,CAACH,YAAY;IACnCL,SAAS,GAAGQ,OAAO,CAACR,SAAS;IAC7BC,OAAO,GAAGO,OAAO,CAACP,OAAO;IACzBG,cAAc,GAAGI,OAAO,CAACJ,cAAc,IAAIR,QAAQ;IACnDO,SAAS,GAAGK,OAAO,CAACL,SAAS;EACjC,CAAC,MACI,IAAID,0BAA0B,KAAKO,SAAS,IAAIZ,WAAW,CAACK,0BAA0B,CAAC,EAAE;IAC1FG,YAAY,GAAGN,qBAAqB;IACpCK,cAAc,GAAGR,QAAQ;IACzBO,SAAS,GAAGD,0BAA0B;EAC1C,CAAC,MACI;IACDG,YAAY,GAAGN,qBAAqB;IACpCK,cAAc,GAAGF,0BAA0B;EAC/C;EACA,OAAO,IAAIP,UAAU,CAACe,UAAU,IAAI;IAChC,IAAIC,KAAK,GAAGN,YAAY;IACxB,IAAIF,SAAS,EAAE;MACX,OAAOA,SAAS,CAACS,QAAQ,CAACC,QAAQ,EAAE,CAAC,EAAE;QACnCH,UAAU;QACVT,OAAO;QACPD,SAAS;QACTI,cAAc;QACdO;MACJ,CAAC,CAAC;IACN;IACA,GAAG;MACC,IAAIX,SAAS,EAAE;QACX,IAAIc,eAAe;QACnB,IAAI;UACAA,eAAe,GAAGd,SAAS,CAACW,KAAK,CAAC;QACtC,CAAC,CACD,OAAOI,GAAG,EAAE;UACRL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;UACrB,OAAON,SAAS;QACpB;QACA,IAAI,CAACK,eAAe,EAAE;UAClBJ,UAAU,CAACO,QAAQ,CAAC,CAAC;UACrB;QACJ;MACJ;MACA,IAAIC,KAAK;MACT,IAAI;QACAA,KAAK,GAAGd,cAAc,CAACO,KAAK,CAAC;MACjC,CAAC,CACD,OAAOI,GAAG,EAAE;QACRL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;QACrB,OAAON,SAAS;MACpB;MACAC,UAAU,CAACS,IAAI,CAACD,KAAK,CAAC;MACtB,IAAIR,UAAU,CAACU,MAAM,EAAE;QACnB;MACJ;MACA,IAAI;QACAT,KAAK,GAAGV,OAAO,CAACU,KAAK,CAAC;MAC1B,CAAC,CACD,OAAOI,GAAG,EAAE;QACRL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;QACrB,OAAON,SAAS;MACpB;IACJ,CAAC,QAAQ,IAAI;IACb,OAAOA,SAAS;EACpB,CAAC,CAAC;AACN;AACA,SAASI,QAAQA,CAACF,KAAK,EAAE;EACrB,MAAM;IAAED,UAAU;IAAEV;EAAU,CAAC,GAAGW,KAAK;EACvC,IAAID,UAAU,CAACU,MAAM,EAAE;IACnB,OAAOX,SAAS;EACpB;EACA,IAAIE,KAAK,CAACU,WAAW,EAAE;IACnB,IAAI;MACAV,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACV,OAAO,CAACU,KAAK,CAACA,KAAK,CAAC;IAC5C,CAAC,CACD,OAAOI,GAAG,EAAE;MACRL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;MACrB,OAAON,SAAS;IACpB;EACJ,CAAC,MACI;IACDE,KAAK,CAACU,WAAW,GAAG,IAAI;EAC5B;EACA,IAAIrB,SAAS,EAAE;IACX,IAAIc,eAAe;IACnB,IAAI;MACAA,eAAe,GAAGd,SAAS,CAACW,KAAK,CAACA,KAAK,CAAC;IAC5C,CAAC,CACD,OAAOI,GAAG,EAAE;MACRL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;MACrB,OAAON,SAAS;IACpB;IACA,IAAI,CAACK,eAAe,EAAE;MAClBJ,UAAU,CAACO,QAAQ,CAAC,CAAC;MACrB,OAAOR,SAAS;IACpB;IACA,IAAIC,UAAU,CAACU,MAAM,EAAE;MACnB,OAAOX,SAAS;IACpB;EACJ;EACA,IAAIS,KAAK;EACT,IAAI;IACAA,KAAK,GAAGP,KAAK,CAACP,cAAc,CAACO,KAAK,CAACA,KAAK,CAAC;EAC7C,CAAC,CACD,OAAOI,GAAG,EAAE;IACRL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;IACrB,OAAON,SAAS;EACpB;EACA,IAAIC,UAAU,CAACU,MAAM,EAAE;IACnB,OAAOX,SAAS;EACpB;EACAC,UAAU,CAACS,IAAI,CAACD,KAAK,CAAC;EACtB,IAAIR,UAAU,CAACU,MAAM,EAAE;IACnB,OAAOX,SAAS;EACpB;EACA,OAAO,IAAI,CAACG,QAAQ,CAACD,KAAK,CAAC;AAC/B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { take, takeUntil, filter } from 'rxjs/operators';\nimport { PerfectScrollbarDirective } from 'ngx-perfect-scrollbar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/config.service\";\nimport * as i2 from \"@core/components/core-menu/core-menu.service\";\nimport * as i3 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@core/components/core-menu/core-menu.component\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/flex-layout/extended\";\nimport * as i8 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i9 from \"ngx-perfect-scrollbar\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nconst _c1 = function (a0) {\n  return {\n    \"d-block\": a0\n  };\n};\nexport class VerticalMenuComponent {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {CoreMenuService} _coreMenuService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {Router} _router\r\n   */\n  constructor(_coreConfigService, _coreMenuService, _coreSidebarService, _router) {\n    this._coreConfigService = _coreConfigService;\n    this._coreMenuService = _coreMenuService;\n    this._coreSidebarService = _coreSidebarService;\n    this._router = _router;\n    this.isScrolled = false;\n    // Set the private defaults\n    this._unsubscribeAll = new Subject();\n  }\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On Init\r\n   */\n  ngOnInit() {\n    // Subscribe config change\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.coreConfig = config;\n    });\n    this.isCollapsed = this._coreSidebarService.getSidebarRegistry('menu').collapsed;\n    // Close the menu on router NavigationEnd (Required for small screen to close the menu on select)\n    this._router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this._unsubscribeAll)).subscribe(() => {\n      if (this._coreSidebarService.getSidebarRegistry('menu')) {\n        this._coreSidebarService.getSidebarRegistry('menu').close();\n      }\n    });\n    // scroll to active on navigation end\n    this._router.events.pipe(filter(event => event instanceof NavigationEnd), take(1)).subscribe(() => {\n      setTimeout(() => {\n        this.directiveRef?.scrollToElement('.navigation .active', -180, 500);\n      });\n    });\n    // Get current menu\n    this._coreMenuService.onMenuChanged.pipe(filter(value => value !== null), takeUntil(this._unsubscribeAll)).subscribe(() => {\n      this.menu = this._coreMenuService.getCurrentMenu();\n    });\n  }\n  /**\r\n   * On Destroy\r\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  // Public Methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On Sidebar scroll set isScrolled as true\r\n   */\n  onSidebarScroll() {\n    if (Number(this.directiveRef.position(true).y) > 3) {\n      this.isScrolled = true;\n    } else {\n      this.isScrolled = false;\n    }\n  }\n  /**\r\n   * Toggle sidebar expanded status\r\n   */\n  toggleSidebar() {\n    this._coreSidebarService.getSidebarRegistry('menu').toggleOpen();\n  }\n  /**\r\n   * Toggle sidebar collapsed status\r\n   */\n  toggleSidebarCollapsible() {\n    // Get the current menu state\n    this._coreConfigService.getConfig().pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.isCollapsed = config.layout.menu.collapsed;\n    });\n    if (this.isCollapsed) {\n      this._coreConfigService.setConfig({\n        layout: {\n          menu: {\n            collapsed: false\n          }\n        }\n      }, {\n        emitEvent: true\n      });\n    } else {\n      this._coreConfigService.setConfig({\n        layout: {\n          menu: {\n            collapsed: true\n          }\n        }\n      }, {\n        emitEvent: true\n      });\n    }\n  }\n  getBrandFontSize() {\n    const name = this.coreConfig.app.appName || '';\n    const length = name.length;\n    if (length < 20) {\n      return {\n        'font-size': '1.45rem'\n      };\n    } else if (length < 30) {\n      return {\n        'font-size': '1rem'\n      };\n    } else if (length < 50) {\n      return {\n        'font-size': '0.85rem'\n      };\n    } else {\n      return {\n        'font-size': '0.65rem'\n      };\n    }\n  }\n  static #_ = this.ɵfac = function VerticalMenuComponent_Factory(t) {\n    return new (t || VerticalMenuComponent)(i0.ɵɵdirectiveInject(i1.CoreConfigService), i0.ɵɵdirectiveInject(i2.CoreMenuService), i0.ɵɵdirectiveInject(i3.CoreSidebarService), i0.ɵɵdirectiveInject(i4.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: VerticalMenuComponent,\n    selectors: [[\"vertical-menu\"]],\n    viewQuery: function VerticalMenuComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(PerfectScrollbarDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.directiveRef = _t.first);\n      }\n    },\n    decls: 16,\n    vars: 9,\n    consts: [[1, \"navbar-header\"], [1, \"nav\", \"navbar-nav\", \"d-flex\", \"flex-row\"], [1, \"nav-item\", 2, \"width\", \"90%\"], [1, \"navbar-brand\", 2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"0.5rem\", \"height\", \"100%\", 3, \"routerLink\"], [1, \"brand-logo\"], [\"alt\", \"brand-logo\", 3, \"src\"], [1, \"brand-text\", \"mb-0\", 3, \"ngStyle\"], [1, \"nav-item\", \"nav-toggle\", 2, \"width\", \"10%\"], [1, \"nav-link\", \"modern-nav-toggle\", \"d-none\", \"d-xl-block\", \"pr-0\", 3, \"click\"], [1, \"toggle-icon\", \"feather\", \"font-medium-4\", \"collapse-toggle-icon\", \"text-primary\", 3, \"ngClass\"], [1, \"nav-link\", \"modern-nav-toggle\", \"d-block\", \"d-xl-none\", \"pr-0\", 3, \"click\"], [\"data-feather\", \"x\", 1, \"font-medium-4\", \"text-primary\", \"toggle-icon\"], [1, \"shadow-bottom\", 3, \"ngClass\"], [1, \"main-menu-content\", 3, \"perfectScrollbar\", \"scroll\"], [\"layout\", \"vertical\", \"core-menu\", \"\", 1, \"navigation\", \"navigation-main\"]],\n    template: function VerticalMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"ul\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"span\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"h2\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"a\", 8);\n        i0.ɵɵlistener(\"click\", function VerticalMenuComponent_Template_a_click_9_listener() {\n          return ctx.toggleSidebarCollapsible();\n        });\n        i0.ɵɵelement(10, \"i\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"a\", 10);\n        i0.ɵɵlistener(\"click\", function VerticalMenuComponent_Template_a_click_11_listener() {\n          return ctx.toggleSidebar();\n        });\n        i0.ɵɵelement(12, \"i\", 11);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(13, \"div\", 12);\n        i0.ɵɵelementStart(14, \"div\", 13);\n        i0.ɵɵlistener(\"scroll\", function VerticalMenuComponent_Template_div_scroll_14_listener() {\n          return ctx.onSidebarScroll();\n        });\n        i0.ɵɵelement(15, \"ul\", 14);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c0));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"src\", ctx.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", ctx.getBrandFontSize());\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.coreConfig.app.appName, \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", ctx.isCollapsed === true ? \"icon-circle\" : \"icon-disc\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx.isScrolled));\n      }\n    },\n    dependencies: [i5.CoreMenuComponent, i6.NgClass, i6.NgStyle, i7.DefaultClassDirective, i7.DefaultStyleDirective, i8.FeatherIconDirective, i9.PerfectScrollbarDirective, i4.RouterLink],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAQA,SAAiBA,aAAa,QAAQ,iBAAiB;AAEvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,IAAI,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AACxD,SAASC,yBAAyB,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;;;AAYjE,OAAM,MAAOC,qBAAqB;EAShC;;;;;;;;EAQAC,YACUC,kBAAqC,EACrCC,gBAAiC,EACjCC,mBAAuC,EACvCC,OAAe;IAHf,KAAAH,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,OAAO,GAAPA,OAAO;IAjBjB,KAAAC,UAAU,GAAY,KAAK;IAmBzB;IACA,IAAI,CAACC,eAAe,GAAG,IAAIZ,OAAO,EAAE;EACtC;EAKA;EACA;EAEA;;;EAGAa,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,kBAAkB,CAACO,MAAM,CAC3BC,IAAI,CAACb,SAAS,CAAC,IAAI,CAACU,eAAe,CAAC,CAAC,CACrCI,SAAS,CAAEF,MAAM,IAAI;MACpB,IAAI,CAACG,UAAU,GAAGH,MAAM;IAC1B,CAAC,CAAC;IAEJ,IAAI,CAACI,WAAW,GACd,IAAI,CAACT,mBAAmB,CAACU,kBAAkB,CAAC,MAAM,CAAC,CAACC,SAAS;IAE/D;IACA,IAAI,CAACV,OAAO,CAACW,MAAM,CAChBN,IAAI,CACHZ,MAAM,CAAEmB,KAAK,IAAKA,KAAK,YAAYvB,aAAa,CAAC,EACjDG,SAAS,CAAC,IAAI,CAACU,eAAe,CAAC,CAChC,CACAI,SAAS,CAAC,MAAK;MACd,IAAI,IAAI,CAACP,mBAAmB,CAACU,kBAAkB,CAAC,MAAM,CAAC,EAAE;QACvD,IAAI,CAACV,mBAAmB,CAACU,kBAAkB,CAAC,MAAM,CAAC,CAACI,KAAK,EAAE;;IAE/D,CAAC,CAAC;IAEJ;IACA,IAAI,CAACb,OAAO,CAACW,MAAM,CAChBN,IAAI,CACHZ,MAAM,CAAEmB,KAAK,IAAKA,KAAK,YAAYvB,aAAa,CAAC,EACjDE,IAAI,CAAC,CAAC,CAAC,CACR,CACAe,SAAS,CAAC,MAAK;MACdQ,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,YAAY,EAAEC,eAAe,CAAC,qBAAqB,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACtE,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAClB,gBAAgB,CAACmB,aAAa,CAChCZ,IAAI,CACHZ,MAAM,CAAEyB,KAAK,IAAKA,KAAK,KAAK,IAAI,CAAC,EACjC1B,SAAS,CAAC,IAAI,CAACU,eAAe,CAAC,CAChC,CACAI,SAAS,CAAC,MAAK;MACd,IAAI,CAACa,IAAI,GAAG,IAAI,CAACrB,gBAAgB,CAACsB,cAAc,EAAE;IACpD,CAAC,CAAC;EACN;EAEA;;;EAGAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACnB,eAAe,CAACoB,IAAI,EAAE;IAC3B,IAAI,CAACpB,eAAe,CAACqB,QAAQ,EAAE;EACjC;EAEA;EACA;EAEA;;;EAGAC,eAAeA,CAAA;IACb,IAAIC,MAAM,CAAC,IAAI,CAACV,YAAY,CAACW,QAAQ,CAAC,IAAI,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC,EAAE;MAClD,IAAI,CAAC1B,UAAU,GAAG,IAAI;KACvB,MAAM;MACL,IAAI,CAACA,UAAU,GAAG,KAAK;;EAE3B;EAEA;;;EAGA2B,aAAaA,CAAA;IACX,IAAI,CAAC7B,mBAAmB,CAACU,kBAAkB,CAAC,MAAM,CAAC,CAACoB,UAAU,EAAE;EAClE;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB;IACA,IAAI,CAACjC,kBAAkB,CACpBkC,SAAS,EAAE,CACX1B,IAAI,CAACb,SAAS,CAAC,IAAI,CAACU,eAAe,CAAC,CAAC,CACrCI,SAAS,CAAEF,MAAM,IAAI;MACpB,IAAI,CAACI,WAAW,GAAGJ,MAAM,CAAC4B,MAAM,CAACb,IAAI,CAACT,SAAS;IACjD,CAAC,CAAC;IAEJ,IAAI,IAAI,CAACF,WAAW,EAAE;MACpB,IAAI,CAACX,kBAAkB,CAACoC,SAAS,CAC/B;QAAED,MAAM,EAAE;UAAEb,IAAI,EAAE;YAAET,SAAS,EAAE;UAAK;QAAE;MAAE,CAAE,EAC1C;QAAEwB,SAAS,EAAE;MAAI,CAAE,CACpB;KACF,MAAM;MACL,IAAI,CAACrC,kBAAkB,CAACoC,SAAS,CAC/B;QAAED,MAAM,EAAE;UAAEb,IAAI,EAAE;YAAET,SAAS,EAAE;UAAI;QAAE;MAAE,CAAE,EACzC;QAAEwB,SAAS,EAAE;MAAI,CAAE,CACpB;;EAEL;EAEAC,gBAAgBA,CAAA;IACd,MAAMC,IAAI,GAAG,IAAI,CAAC7B,UAAU,CAAC8B,GAAG,CAACC,OAAO,IAAI,EAAE;IAC9C,MAAMC,MAAM,GAAGH,IAAI,CAACG,MAAM;IAE1B,IAAIA,MAAM,GAAG,EAAE,EAAE;MACf,OAAO;QAAE,WAAW,EAAE;MAAS,CAAE;KAClC,MAAM,IAAIA,MAAM,GAAG,EAAE,EAAE;MACtB,OAAO;QAAE,WAAW,EAAE;MAAM,CAAE;KAC/B,MAAM,IAAIA,MAAM,GAAG,EAAE,EAAC;MACrB,OAAO;QAAE,WAAW,EAAE;MAAS,CAAE;KAClC,MAAM;MACL,OAAO;QAAE,WAAW,EAAE;MAAS,CAAE;;EAErC;EAAC,QAAAC,CAAA;qBAtJU7C,qBAAqB,EAAA8C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;UAArBxD,qBAAqB;IAAAyD,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBA2BrB7D,yBAAyB;;;;;;;;;;;;QClDtC+C,EAAA,CAAAgB,cAAA,aAA2B;QAMjBhB,EAAA,CAAAiB,SAAA,aAAgE;QAClEjB,EAAA,CAAAkB,YAAA,EAAO;QAEPlB,EAAA,CAAAgB,cAAA,YACmC;QAEjChB,EAAA,CAAAmB,MAAA,GAEF;QAAAnB,EAAA,CAAAkB,YAAA,EAAK;QAKTlB,EAAA,CAAAgB,cAAA,aAAoD;QAGhDhB,EAAA,CAAAoB,UAAA,mBAAAC,kDAAA;UAAA,OAASN,GAAA,CAAA1B,wBAAA,EAA0B;QAAA,EAAC;QAEpCW,EAAA,CAAAiB,SAAA,YAGK;QACPjB,EAAA,CAAAkB,YAAA,EAAI;QACJlB,EAAA,CAAAgB,cAAA,aAGC;QADChB,EAAA,CAAAoB,UAAA,mBAAAE,mDAAA;UAAA,OAASP,GAAA,CAAA5B,aAAA,EAAe;QAAA,EAAC;QAEzBa,EAAA,CAAAiB,SAAA,aAAuE;QACzEjB,EAAA,CAAAkB,YAAA,EAAI;QAOVlB,EAAA,CAAAiB,SAAA,eAAuE;QAGvEjB,EAAA,CAAAgB,cAAA,eAA+E;QAA7BhB,EAAA,CAAAoB,UAAA,oBAAAG,sDAAA;UAAA,OAAUR,GAAA,CAAAhC,eAAA,EAAiB;QAAA,EAAC;QAC5EiB,EAAA,CAAAiB,SAAA,cAAwE;QAC1EjB,EAAA,CAAAkB,YAAA,EAAM;;;QA1C8FlB,EAAA,CAAAwB,SAAA,GAAoB;QAApBxB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAoB;QAEzG3B,EAAA,CAAAwB,SAAA,GAAuC;QAAvCxB,EAAA,CAAA4B,qBAAA,QAAAb,GAAA,CAAAjD,UAAA,CAAA8B,GAAA,CAAAiC,YAAA,EAAA7B,EAAA,CAAA8B,aAAA,CAAuC;QAI1C9B,EAAA,CAAAwB,SAAA,GAA8B;QAA9BxB,EAAA,CAAAyB,UAAA,YAAAV,GAAA,CAAArB,gBAAA,GAA8B;QAEhCM,EAAA,CAAAwB,SAAA,GAEF;QAFExB,EAAA,CAAA+B,kBAAA,MAAAhB,GAAA,CAAAjD,UAAA,CAAA8B,GAAA,CAAAC,OAAA,MAEF;QAWEG,EAAA,CAAAwB,SAAA,GAA8D;QAA9DxB,EAAA,CAAAyB,UAAA,YAAAV,GAAA,CAAAhD,WAAA,wCAA8D;QAgB7CiC,EAAA,CAAAwB,SAAA,GAAqC;QAArCxB,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAlB,GAAA,CAAAvD,UAAA,EAAqC", "names": ["NavigationEnd", "Subject", "take", "takeUntil", "filter", "PerfectScrollbarDirective", "VerticalMenuComponent", "constructor", "_coreConfigService", "_coreMenuService", "_coreSidebarService", "_router", "isScrolled", "_unsubscribeAll", "ngOnInit", "config", "pipe", "subscribe", "coreConfig", "isCollapsed", "getSidebarRegistry", "collapsed", "events", "event", "close", "setTimeout", "directiveRef", "scrollToElement", "onMenuChanged", "value", "menu", "getCurrentMenu", "ngOnDestroy", "next", "complete", "onSidebarScroll", "Number", "position", "y", "toggleSidebar", "toggle<PERSON><PERSON>", "toggleSidebarCollapsible", "getConfig", "layout", "setConfig", "emitEvent", "getBrandFontSize", "name", "app", "appName", "length", "_", "i0", "ɵɵdirectiveInject", "i1", "CoreConfigService", "i2", "CoreMenuService", "i3", "CoreSidebarService", "i4", "Router", "_2", "selectors", "viewQuery", "VerticalMenuComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "VerticalMenuComponent_Template_a_click_9_listener", "VerticalMenuComponent_Template_a_click_11_listener", "VerticalMenuComponent_Template_div_scroll_14_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵpropertyInterpolate", "appLogoImage", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "ɵɵpureFunction1", "_c1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\menu\\vertical-menu\\vertical-menu.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\menu\\vertical-menu\\vertical-menu.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  On<PERSON>ni<PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON>Child,\r\n  HostListener,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { Router, NavigationEnd } from '@angular/router';\r\n\r\nimport { Subject } from 'rxjs';\r\nimport { take, takeUntil, filter } from 'rxjs/operators';\r\nimport { PerfectScrollbarDirective } from 'ngx-perfect-scrollbar';\r\n\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { CoreMenuService } from '@core/components/core-menu/core-menu.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\n\r\n@Component({\r\n  selector: 'vertical-menu',\r\n  templateUrl: './vertical-menu.component.html',\r\n  styleUrls: ['./vertical-menu.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class VerticalMenuComponent implements OnInit, OnDestroy {\r\n  coreConfig: any;\r\n  menu: any;\r\n  isCollapsed: boolean;\r\n  isScrolled: boolean = false;\r\n\r\n  // Private\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {CoreMenuService} _coreMenuService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {Router} _router\r\n   */\r\n  constructor(\r\n    private _coreConfigService: CoreConfigService,\r\n    private _coreMenuService: CoreMenuService,\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _router: Router\r\n  ) {\r\n    // Set the private defaults\r\n    this._unsubscribeAll = new Subject();\r\n  }\r\n\r\n  @ViewChild(PerfectScrollbarDirective, { static: false })\r\n  directiveRef?: PerfectScrollbarDirective;\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On Init\r\n   */\r\n  ngOnInit(): void {\r\n    // Subscribe config change\r\n    this._coreConfigService.config\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe((config) => {\r\n        this.coreConfig = config;\r\n      });\r\n\r\n    this.isCollapsed =\r\n      this._coreSidebarService.getSidebarRegistry('menu').collapsed;\r\n\r\n    // Close the menu on router NavigationEnd (Required for small screen to close the menu on select)\r\n    this._router.events\r\n      .pipe(\r\n        filter((event) => event instanceof NavigationEnd),\r\n        takeUntil(this._unsubscribeAll)\r\n      )\r\n      .subscribe(() => {\r\n        if (this._coreSidebarService.getSidebarRegistry('menu')) {\r\n          this._coreSidebarService.getSidebarRegistry('menu').close();\r\n        }\r\n      });\r\n\r\n    // scroll to active on navigation end\r\n    this._router.events\r\n      .pipe(\r\n        filter((event) => event instanceof NavigationEnd),\r\n        take(1)\r\n      )\r\n      .subscribe(() => {\r\n        setTimeout(() => {\r\n          this.directiveRef?.scrollToElement('.navigation .active', -180, 500);\r\n        });\r\n      });\r\n\r\n    // Get current menu\r\n    this._coreMenuService.onMenuChanged\r\n      .pipe(\r\n        filter((value) => value !== null),\r\n        takeUntil(this._unsubscribeAll)\r\n      )\r\n      .subscribe(() => {\r\n        this.menu = this._coreMenuService.getCurrentMenu();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * On Destroy\r\n   */\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n\r\n  // Public Methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On Sidebar scroll set isScrolled as true\r\n   */\r\n  onSidebarScroll(): void {\r\n    if (Number(this.directiveRef.position(true).y) > 3) {\r\n      this.isScrolled = true;\r\n    } else {\r\n      this.isScrolled = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Toggle sidebar expanded status\r\n   */\r\n  toggleSidebar(): void {\r\n    this._coreSidebarService.getSidebarRegistry('menu').toggleOpen();\r\n  }\r\n\r\n  /**\r\n   * Toggle sidebar collapsed status\r\n   */\r\n  toggleSidebarCollapsible(): void {\r\n    // Get the current menu state\r\n    this._coreConfigService\r\n      .getConfig()\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe((config) => {\r\n        this.isCollapsed = config.layout.menu.collapsed;\r\n      });\r\n\r\n    if (this.isCollapsed) {\r\n      this._coreConfigService.setConfig(\r\n        { layout: { menu: { collapsed: false } } },\r\n        { emitEvent: true }\r\n      );\r\n    } else {\r\n      this._coreConfigService.setConfig(\r\n        { layout: { menu: { collapsed: true } } },\r\n        { emitEvent: true }\r\n      );\r\n    }\r\n  }\r\n\r\n  getBrandFontSize() {\r\n    const name = this.coreConfig.app.appName || '';\r\n    const length = name.length;\r\n\r\n    if (length < 20) {\r\n      return { 'font-size': '1.45rem' };\r\n    } else if (length < 30) {\r\n      return { 'font-size': '1rem' };\r\n    } else if (length < 50){\r\n      return { 'font-size': '0.85rem' };\r\n    } else {\r\n      return { 'font-size': '0.65rem' };\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- Menu header -->\r\n<div class=\"navbar-header\">\r\n  <ul class=\"nav navbar-nav d-flex flex-row\">\r\n    <div class=\"nav-item\" style=\"width: 90%\">\r\n      <!-- App Branding -->\r\n      <a class=\"navbar-brand\" style=\"display: flex; align-items: center; gap: 0.5rem; height: 100%\" [routerLink]=\"['/']\">\r\n        <span class=\"brand-logo\">\r\n          <img src=\"{{ coreConfig.app.appLogoImage }}\" alt=\"brand-logo\" />\r\n        </span>\r\n        <!-- <h2 class=\"brand-text mb-0\">{{ coreConfig.app.appName }}</h2> -->\r\n        <h2 class=\"brand-text mb-0\"\r\n            [ngStyle]=\"getBrandFontSize()\">\r\n          <!--          {{ sliceAppName(coreConfig.app.appName) }}-->\r\n          {{ coreConfig.app.appName }}\r\n\r\n        </h2>\r\n      </a>\r\n    </div>\r\n\r\n    <!-- Menu Toggler -->\r\n    <div class=\"nav-item nav-toggle\" style=\"width: 10%\">\r\n      <a\r\n        class=\"nav-link modern-nav-toggle d-none d-xl-block pr-0\"\r\n        (click)=\"toggleSidebarCollapsible()\"\r\n      >\r\n        <i\r\n          [ngClass]=\"isCollapsed === true ? 'icon-circle' : 'icon-disc'\"\r\n          class=\"toggle-icon feather font-medium-4 collapse-toggle-icon text-primary\"\r\n        ></i>\r\n      </a>\r\n      <a\r\n        class=\"nav-link modern-nav-toggle d-block d-xl-none pr-0\"\r\n        (click)=\"toggleSidebar()\"\r\n      >\r\n        <i data-feather=\"x\" class=\"font-medium-4 text-primary toggle-icon\"></i>\r\n      </a>\r\n    </div>\r\n  </ul>\r\n</div>\r\n<!--/ Menu header -->\r\n\r\n<!-- Navbar shadow -->\r\n<div class=\"shadow-bottom\" [ngClass]=\"{ 'd-block': isScrolled }\"></div>\r\n\r\n<!-- Main menu -->\r\n<div class=\"main-menu-content\" [perfectScrollbar] (scroll)=\"onSidebarScroll()\">\r\n  <ul class=\"navigation navigation-main\" layout=\"vertical\" core-menu></ul>\r\n</div>\r\n<!--/ Main menu -->\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { onAuthStateChanged, onIdTokenChanged, getIdToken } from 'firebase/auth';\nimport { Observable, from, of } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Create an observable of authentication state. The observer is only\n * triggered on sign-in or sign-out.\n * @param auth firebase.auth.Auth\n */\nfunction authState(auth) {\n  return new Observable(function (subscriber) {\n    var unsubscribe = onAuthStateChanged(auth, subscriber.next.bind(subscriber), subscriber.error.bind(subscriber), subscriber.complete.bind(subscriber));\n    return {\n      unsubscribe: unsubscribe\n    };\n  });\n}\n/**\n * Create an observable of user state. The observer is triggered for sign-in,\n * sign-out, and token refresh events\n * @param auth firebase.auth.Auth\n */\nfunction user(auth) {\n  return new Observable(function (subscriber) {\n    var unsubscribe = onIdTokenChanged(auth, subscriber.next.bind(subscriber), subscriber.error.bind(subscriber), subscriber.complete.bind(subscriber));\n    return {\n      unsubscribe: unsubscribe\n    };\n  });\n}\n/**\n * Create an observable of idToken state. The observer is triggered for sign-in,\n * sign-out, and token refresh events\n * @param auth firebase.auth.Auth\n */\nfunction idToken(auth) {\n  return user(auth).pipe(switchMap(function (user) {\n    return user ? from(getIdToken(user)) : of(null);\n  }));\n}\nexport { authState, idToken, user };", "map": {"version": 3, "names": ["onAuthStateChanged", "onIdTokenChanged", "getIdToken", "Observable", "from", "of", "switchMap", "authState", "auth", "subscriber", "unsubscribe", "next", "bind", "error", "complete", "user", "idToken", "pipe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxfire/auth/index.esm.js"], "sourcesContent": ["import { onAuthStateChanged, onIdTokenChanged, getIdToken } from 'firebase/auth';\nimport { Observable, from, of } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Create an observable of authentication state. The observer is only\n * triggered on sign-in or sign-out.\n * @param auth firebase.auth.Auth\n */\nfunction authState(auth) {\n    return new Observable(function (subscriber) {\n        var unsubscribe = onAuthStateChanged(auth, subscriber.next.bind(subscriber), subscriber.error.bind(subscriber), subscriber.complete.bind(subscriber));\n        return { unsubscribe: unsubscribe };\n    });\n}\n/**\n * Create an observable of user state. The observer is triggered for sign-in,\n * sign-out, and token refresh events\n * @param auth firebase.auth.Auth\n */\nfunction user(auth) {\n    return new Observable(function (subscriber) {\n        var unsubscribe = onIdTokenChanged(auth, subscriber.next.bind(subscriber), subscriber.error.bind(subscriber), subscriber.complete.bind(subscriber));\n        return { unsubscribe: unsubscribe };\n    });\n}\n/**\n * Create an observable of idToken state. The observer is triggered for sign-in,\n * sign-out, and token refresh events\n * @param auth firebase.auth.Auth\n */\nfunction idToken(auth) {\n    return user(auth).pipe(switchMap(function (user) { return (user ? from(getIdToken(user)) : of(null)); }));\n}\n\nexport { authState, idToken, user };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AAChF,SAASC,UAAU,EAAEC,IAAI,EAAEC,EAAE,QAAQ,MAAM;AAC3C,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAE;EACrB,OAAO,IAAIL,UAAU,CAAC,UAAUM,UAAU,EAAE;IACxC,IAAIC,WAAW,GAAGV,kBAAkB,CAACQ,IAAI,EAAEC,UAAU,CAACE,IAAI,CAACC,IAAI,CAACH,UAAU,CAAC,EAAEA,UAAU,CAACI,KAAK,CAACD,IAAI,CAACH,UAAU,CAAC,EAAEA,UAAU,CAACK,QAAQ,CAACF,IAAI,CAACH,UAAU,CAAC,CAAC;IACrJ,OAAO;MAAEC,WAAW,EAAEA;IAAY,CAAC;EACvC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,IAAIA,CAACP,IAAI,EAAE;EAChB,OAAO,IAAIL,UAAU,CAAC,UAAUM,UAAU,EAAE;IACxC,IAAIC,WAAW,GAAGT,gBAAgB,CAACO,IAAI,EAAEC,UAAU,CAACE,IAAI,CAACC,IAAI,CAACH,UAAU,CAAC,EAAEA,UAAU,CAACI,KAAK,CAACD,IAAI,CAACH,UAAU,CAAC,EAAEA,UAAU,CAACK,QAAQ,CAACF,IAAI,CAACH,UAAU,CAAC,CAAC;IACnJ,OAAO;MAAEC,WAAW,EAAEA;IAAY,CAAC;EACvC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,OAAOA,CAACR,IAAI,EAAE;EACnB,OAAOO,IAAI,CAACP,IAAI,CAAC,CAACS,IAAI,CAACX,SAAS,CAAC,UAAUS,IAAI,EAAE;IAAE,OAAQA,IAAI,GAAGX,IAAI,CAACF,UAAU,CAACa,IAAI,CAAC,CAAC,GAAGV,EAAE,CAAC,IAAI,CAAC;EAAG,CAAC,CAAC,CAAC;AAC7G;AAEA,SAASE,SAAS,EAAES,OAAO,EAAED,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
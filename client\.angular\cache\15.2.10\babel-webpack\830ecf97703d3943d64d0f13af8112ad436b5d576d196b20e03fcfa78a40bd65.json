{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nexport function groupBy(keySelector, elementSelector, durationSelector, subjectSelector) {\n  return source => source.lift(new GroupByOperator(keySelector, elementSelector, durationSelector, subjectSelector));\n}\nclass GroupByOperator {\n  constructor(keySelector, elementSelector, durationSelector, subjectSelector) {\n    this.keySelector = keySelector;\n    this.elementSelector = elementSelector;\n    this.durationSelector = durationSelector;\n    this.subjectSelector = subjectSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new GroupBySubscriber(subscriber, this.keySelector, this.elementSelector, this.durationSelector, this.subjectSelector));\n  }\n}\nclass GroupBySubscriber extends Subscriber {\n  constructor(destination, keySelector, elementSelector, durationSelector, subjectSelector) {\n    super(destination);\n    this.keySelector = keySelector;\n    this.elementSelector = elementSelector;\n    this.durationSelector = durationSelector;\n    this.subjectSelector = subjectSelector;\n    this.groups = null;\n    this.attemptedToUnsubscribe = false;\n    this.count = 0;\n  }\n  _next(value) {\n    let key;\n    try {\n      key = this.keySelector(value);\n    } catch (err) {\n      this.error(err);\n      return;\n    }\n    this._group(value, key);\n  }\n  _group(value, key) {\n    let groups = this.groups;\n    if (!groups) {\n      groups = this.groups = new Map();\n    }\n    let group = groups.get(key);\n    let element;\n    if (this.elementSelector) {\n      try {\n        element = this.elementSelector(value);\n      } catch (err) {\n        this.error(err);\n      }\n    } else {\n      element = value;\n    }\n    if (!group) {\n      group = this.subjectSelector ? this.subjectSelector() : new Subject();\n      groups.set(key, group);\n      const groupedObservable = new GroupedObservable(key, group, this);\n      this.destination.next(groupedObservable);\n      if (this.durationSelector) {\n        let duration;\n        try {\n          duration = this.durationSelector(new GroupedObservable(key, group));\n        } catch (err) {\n          this.error(err);\n          return;\n        }\n        this.add(duration.subscribe(new GroupDurationSubscriber(key, group, this)));\n      }\n    }\n    if (!group.closed) {\n      group.next(element);\n    }\n  }\n  _error(err) {\n    const groups = this.groups;\n    if (groups) {\n      groups.forEach((group, key) => {\n        group.error(err);\n      });\n      groups.clear();\n    }\n    this.destination.error(err);\n  }\n  _complete() {\n    const groups = this.groups;\n    if (groups) {\n      groups.forEach((group, key) => {\n        group.complete();\n      });\n      groups.clear();\n    }\n    this.destination.complete();\n  }\n  removeGroup(key) {\n    this.groups.delete(key);\n  }\n  unsubscribe() {\n    if (!this.closed) {\n      this.attemptedToUnsubscribe = true;\n      if (this.count === 0) {\n        super.unsubscribe();\n      }\n    }\n  }\n}\nclass GroupDurationSubscriber extends Subscriber {\n  constructor(key, group, parent) {\n    super(group);\n    this.key = key;\n    this.group = group;\n    this.parent = parent;\n  }\n  _next(value) {\n    this.complete();\n  }\n  _unsubscribe() {\n    const {\n      parent,\n      key\n    } = this;\n    this.key = this.parent = null;\n    if (parent) {\n      parent.removeGroup(key);\n    }\n  }\n}\nexport class GroupedObservable extends Observable {\n  constructor(key, groupSubject, refCountSubscription) {\n    super();\n    this.key = key;\n    this.groupSubject = groupSubject;\n    this.refCountSubscription = refCountSubscription;\n  }\n  _subscribe(subscriber) {\n    const subscription = new Subscription();\n    const {\n      refCountSubscription,\n      groupSubject\n    } = this;\n    if (refCountSubscription && !refCountSubscription.closed) {\n      subscription.add(new InnerRefCountSubscription(refCountSubscription));\n    }\n    subscription.add(groupSubject.subscribe(subscriber));\n    return subscription;\n  }\n}\nclass InnerRefCountSubscription extends Subscription {\n  constructor(parent) {\n    super();\n    this.parent = parent;\n    parent.count++;\n  }\n  unsubscribe() {\n    const parent = this.parent;\n    if (!parent.closed && !this.closed) {\n      super.unsubscribe();\n      parent.count -= 1;\n      if (parent.count === 0 && parent.attemptedToUnsubscribe) {\n        parent.unsubscribe();\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "Subscription", "Observable", "Subject", "groupBy", "keySelector", "elementSelector", "durationSelector", "subjectSelector", "source", "lift", "GroupByOperator", "constructor", "call", "subscriber", "subscribe", "GroupBySubscriber", "destination", "groups", "attemptedToUnsubscribe", "count", "_next", "value", "key", "err", "error", "_group", "Map", "group", "get", "element", "set", "groupedObservable", "GroupedObservable", "next", "duration", "add", "GroupDurationSubscriber", "closed", "_error", "for<PERSON>ach", "clear", "_complete", "complete", "removeGroup", "delete", "unsubscribe", "parent", "_unsubscribe", "groupSubject", "refCountSubscription", "_subscribe", "subscription", "InnerRefCountSubscription"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/groupBy.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nexport function groupBy(keySelector, elementSelector, durationSelector, subjectSelector) {\n    return (source) => source.lift(new GroupByOperator(keySelector, elementSelector, durationSelector, subjectSelector));\n}\nclass GroupByOperator {\n    constructor(keySelector, elementSelector, durationSelector, subjectSelector) {\n        this.keySelector = keySelector;\n        this.elementSelector = elementSelector;\n        this.durationSelector = durationSelector;\n        this.subjectSelector = subjectSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new GroupBySubscriber(subscriber, this.keySelector, this.elementSelector, this.durationSelector, this.subjectSelector));\n    }\n}\nclass GroupBySubscriber extends Subscriber {\n    constructor(destination, keySelector, elementSelector, durationSelector, subjectSelector) {\n        super(destination);\n        this.keySelector = keySelector;\n        this.elementSelector = elementSelector;\n        this.durationSelector = durationSelector;\n        this.subjectSelector = subjectSelector;\n        this.groups = null;\n        this.attemptedToUnsubscribe = false;\n        this.count = 0;\n    }\n    _next(value) {\n        let key;\n        try {\n            key = this.keySelector(value);\n        }\n        catch (err) {\n            this.error(err);\n            return;\n        }\n        this._group(value, key);\n    }\n    _group(value, key) {\n        let groups = this.groups;\n        if (!groups) {\n            groups = this.groups = new Map();\n        }\n        let group = groups.get(key);\n        let element;\n        if (this.elementSelector) {\n            try {\n                element = this.elementSelector(value);\n            }\n            catch (err) {\n                this.error(err);\n            }\n        }\n        else {\n            element = value;\n        }\n        if (!group) {\n            group = (this.subjectSelector ? this.subjectSelector() : new Subject());\n            groups.set(key, group);\n            const groupedObservable = new GroupedObservable(key, group, this);\n            this.destination.next(groupedObservable);\n            if (this.durationSelector) {\n                let duration;\n                try {\n                    duration = this.durationSelector(new GroupedObservable(key, group));\n                }\n                catch (err) {\n                    this.error(err);\n                    return;\n                }\n                this.add(duration.subscribe(new GroupDurationSubscriber(key, group, this)));\n            }\n        }\n        if (!group.closed) {\n            group.next(element);\n        }\n    }\n    _error(err) {\n        const groups = this.groups;\n        if (groups) {\n            groups.forEach((group, key) => {\n                group.error(err);\n            });\n            groups.clear();\n        }\n        this.destination.error(err);\n    }\n    _complete() {\n        const groups = this.groups;\n        if (groups) {\n            groups.forEach((group, key) => {\n                group.complete();\n            });\n            groups.clear();\n        }\n        this.destination.complete();\n    }\n    removeGroup(key) {\n        this.groups.delete(key);\n    }\n    unsubscribe() {\n        if (!this.closed) {\n            this.attemptedToUnsubscribe = true;\n            if (this.count === 0) {\n                super.unsubscribe();\n            }\n        }\n    }\n}\nclass GroupDurationSubscriber extends Subscriber {\n    constructor(key, group, parent) {\n        super(group);\n        this.key = key;\n        this.group = group;\n        this.parent = parent;\n    }\n    _next(value) {\n        this.complete();\n    }\n    _unsubscribe() {\n        const { parent, key } = this;\n        this.key = this.parent = null;\n        if (parent) {\n            parent.removeGroup(key);\n        }\n    }\n}\nexport class GroupedObservable extends Observable {\n    constructor(key, groupSubject, refCountSubscription) {\n        super();\n        this.key = key;\n        this.groupSubject = groupSubject;\n        this.refCountSubscription = refCountSubscription;\n    }\n    _subscribe(subscriber) {\n        const subscription = new Subscription();\n        const { refCountSubscription, groupSubject } = this;\n        if (refCountSubscription && !refCountSubscription.closed) {\n            subscription.add(new InnerRefCountSubscription(refCountSubscription));\n        }\n        subscription.add(groupSubject.subscribe(subscriber));\n        return subscription;\n    }\n}\nclass InnerRefCountSubscription extends Subscription {\n    constructor(parent) {\n        super();\n        this.parent = parent;\n        parent.count++;\n    }\n    unsubscribe() {\n        const parent = this.parent;\n        if (!parent.closed && !this.closed) {\n            super.unsubscribe();\n            parent.count -= 1;\n            if (parent.count === 0 && parent.attemptedToUnsubscribe) {\n                parent.unsubscribe();\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAO,SAASC,OAAOA,CAACC,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,EAAE;EACrF,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,eAAe,CAACN,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,CAAC,CAAC;AACxH;AACA,MAAMG,eAAe,CAAC;EAClBC,WAAWA,CAACP,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,EAAE;IACzE,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,iBAAiB,CAACF,UAAU,EAAE,IAAI,CAACT,WAAW,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;EACnJ;AACJ;AACA,MAAMQ,iBAAiB,SAAShB,UAAU,CAAC;EACvCY,WAAWA,CAACK,WAAW,EAAEZ,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,EAAE;IACtF,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACZ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACU,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIC,GAAG;IACP,IAAI;MACAA,GAAG,GAAG,IAAI,CAAClB,WAAW,CAACiB,KAAK,CAAC;IACjC,CAAC,CACD,OAAOE,GAAG,EAAE;MACR,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;MACf;IACJ;IACA,IAAI,CAACE,MAAM,CAACJ,KAAK,EAAEC,GAAG,CAAC;EAC3B;EACAG,MAAMA,CAACJ,KAAK,EAAEC,GAAG,EAAE;IACf,IAAIL,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI,CAACA,MAAM,EAAE;MACTA,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAIS,GAAG,CAAC,CAAC;IACpC;IACA,IAAIC,KAAK,GAAGV,MAAM,CAACW,GAAG,CAACN,GAAG,CAAC;IAC3B,IAAIO,OAAO;IACX,IAAI,IAAI,CAACxB,eAAe,EAAE;MACtB,IAAI;QACAwB,OAAO,GAAG,IAAI,CAACxB,eAAe,CAACgB,KAAK,CAAC;MACzC,CAAC,CACD,OAAOE,GAAG,EAAE;QACR,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;MACnB;IACJ,CAAC,MACI;MACDM,OAAO,GAAGR,KAAK;IACnB;IACA,IAAI,CAACM,KAAK,EAAE;MACRA,KAAK,GAAI,IAAI,CAACpB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC,GAAG,IAAIL,OAAO,CAAC,CAAE;MACvEe,MAAM,CAACa,GAAG,CAACR,GAAG,EAAEK,KAAK,CAAC;MACtB,MAAMI,iBAAiB,GAAG,IAAIC,iBAAiB,CAACV,GAAG,EAAEK,KAAK,EAAE,IAAI,CAAC;MACjE,IAAI,CAACX,WAAW,CAACiB,IAAI,CAACF,iBAAiB,CAAC;MACxC,IAAI,IAAI,CAACzB,gBAAgB,EAAE;QACvB,IAAI4B,QAAQ;QACZ,IAAI;UACAA,QAAQ,GAAG,IAAI,CAAC5B,gBAAgB,CAAC,IAAI0B,iBAAiB,CAACV,GAAG,EAAEK,KAAK,CAAC,CAAC;QACvE,CAAC,CACD,OAAOJ,GAAG,EAAE;UACR,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;UACf;QACJ;QACA,IAAI,CAACY,GAAG,CAACD,QAAQ,CAACpB,SAAS,CAAC,IAAIsB,uBAAuB,CAACd,GAAG,EAAEK,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;MAC/E;IACJ;IACA,IAAI,CAACA,KAAK,CAACU,MAAM,EAAE;MACfV,KAAK,CAACM,IAAI,CAACJ,OAAO,CAAC;IACvB;EACJ;EACAS,MAAMA,CAACf,GAAG,EAAE;IACR,MAAMN,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACsB,OAAO,CAAC,CAACZ,KAAK,EAAEL,GAAG,KAAK;QAC3BK,KAAK,CAACH,KAAK,CAACD,GAAG,CAAC;MACpB,CAAC,CAAC;MACFN,MAAM,CAACuB,KAAK,CAAC,CAAC;IAClB;IACA,IAAI,CAACxB,WAAW,CAACQ,KAAK,CAACD,GAAG,CAAC;EAC/B;EACAkB,SAASA,CAAA,EAAG;IACR,MAAMxB,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACsB,OAAO,CAAC,CAACZ,KAAK,EAAEL,GAAG,KAAK;QAC3BK,KAAK,CAACe,QAAQ,CAAC,CAAC;MACpB,CAAC,CAAC;MACFzB,MAAM,CAACuB,KAAK,CAAC,CAAC;IAClB;IACA,IAAI,CAACxB,WAAW,CAAC0B,QAAQ,CAAC,CAAC;EAC/B;EACAC,WAAWA,CAACrB,GAAG,EAAE;IACb,IAAI,CAACL,MAAM,CAAC2B,MAAM,CAACtB,GAAG,CAAC;EAC3B;EACAuB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE;MACd,IAAI,CAACnB,sBAAsB,GAAG,IAAI;MAClC,IAAI,IAAI,CAACC,KAAK,KAAK,CAAC,EAAE;QAClB,KAAK,CAAC0B,WAAW,CAAC,CAAC;MACvB;IACJ;EACJ;AACJ;AACA,MAAMT,uBAAuB,SAASrC,UAAU,CAAC;EAC7CY,WAAWA,CAACW,GAAG,EAAEK,KAAK,EAAEmB,MAAM,EAAE;IAC5B,KAAK,CAACnB,KAAK,CAAC;IACZ,IAAI,CAACL,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACmB,MAAM,GAAGA,MAAM;EACxB;EACA1B,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACqB,QAAQ,CAAC,CAAC;EACnB;EACAK,YAAYA,CAAA,EAAG;IACX,MAAM;MAAED,MAAM;MAAExB;IAAI,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACA,GAAG,GAAG,IAAI,CAACwB,MAAM,GAAG,IAAI;IAC7B,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACH,WAAW,CAACrB,GAAG,CAAC;IAC3B;EACJ;AACJ;AACA,OAAO,MAAMU,iBAAiB,SAAS/B,UAAU,CAAC;EAC9CU,WAAWA,CAACW,GAAG,EAAE0B,YAAY,EAAEC,oBAAoB,EAAE;IACjD,KAAK,CAAC,CAAC;IACP,IAAI,CAAC3B,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC0B,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;EACpD;EACAC,UAAUA,CAACrC,UAAU,EAAE;IACnB,MAAMsC,YAAY,GAAG,IAAInD,YAAY,CAAC,CAAC;IACvC,MAAM;MAAEiD,oBAAoB;MAAED;IAAa,CAAC,GAAG,IAAI;IACnD,IAAIC,oBAAoB,IAAI,CAACA,oBAAoB,CAACZ,MAAM,EAAE;MACtDc,YAAY,CAAChB,GAAG,CAAC,IAAIiB,yBAAyB,CAACH,oBAAoB,CAAC,CAAC;IACzE;IACAE,YAAY,CAAChB,GAAG,CAACa,YAAY,CAAClC,SAAS,CAACD,UAAU,CAAC,CAAC;IACpD,OAAOsC,YAAY;EACvB;AACJ;AACA,MAAMC,yBAAyB,SAASpD,YAAY,CAAC;EACjDW,WAAWA,CAACmC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpBA,MAAM,CAAC3B,KAAK,EAAE;EAClB;EACA0B,WAAWA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACA,MAAM,CAACT,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;MAChC,KAAK,CAACQ,WAAW,CAAC,CAAC;MACnBC,MAAM,CAAC3B,KAAK,IAAI,CAAC;MACjB,IAAI2B,MAAM,CAAC3B,KAAK,KAAK,CAAC,IAAI2B,MAAM,CAAC5B,sBAAsB,EAAE;QACrD4B,MAAM,CAACD,WAAW,CAAC,CAAC;MACxB;IACJ;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
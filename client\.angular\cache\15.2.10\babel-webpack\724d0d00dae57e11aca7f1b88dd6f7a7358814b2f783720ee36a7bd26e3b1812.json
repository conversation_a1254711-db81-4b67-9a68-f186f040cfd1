{"ast": null, "code": "import { Observable } from '../Observable';\nexport function throwError(error, scheduler) {\n  if (!scheduler) {\n    return new Observable(subscriber => subscriber.error(error));\n  } else {\n    return new Observable(subscriber => scheduler.schedule(dispatch, 0, {\n      error,\n      subscriber\n    }));\n  }\n}\nfunction dispatch({\n  error,\n  subscriber\n}) {\n  subscriber.error(error);\n}", "map": {"version": 3, "names": ["Observable", "throwError", "error", "scheduler", "subscriber", "schedule", "dispatch"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/throwError.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function throwError(error, scheduler) {\n    if (!scheduler) {\n        return new Observable(subscriber => subscriber.error(error));\n    }\n    else {\n        return new Observable(subscriber => scheduler.schedule(dispatch, 0, { error, subscriber }));\n    }\n}\nfunction dispatch({ error, subscriber }) {\n    subscriber.error(error);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACzC,IAAI,CAACA,SAAS,EAAE;IACZ,OAAO,IAAIH,UAAU,CAACI,UAAU,IAAIA,UAAU,CAACF,KAAK,CAACA,KAAK,CAAC,CAAC;EAChE,CAAC,MACI;IACD,OAAO,IAAIF,UAAU,CAACI,UAAU,IAAID,SAAS,CAACE,QAAQ,CAACC,QAAQ,EAAE,CAAC,EAAE;MAAEJ,KAAK;MAAEE;IAAW,CAAC,CAAC,CAAC;EAC/F;AACJ;AACA,SAASE,QAAQA,CAAC;EAAEJ,KAAK;EAAEE;AAAW,CAAC,EAAE;EACrCA,UAAU,CAACF,KAAK,CAACA,KAAK,CAAC;AAC3B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DecodeHtmlPipe {\n  transform(value) {\n    if (!value) return value;\n    const parser = new DOMParser();\n    const decodedString = parser.parseFromString(value, 'text/html').documentElement.textContent;\n    return decodedString || value;\n  }\n  static #_ = this.ɵfac = function DecodeHtmlPipe_Factory(t) {\n    return new (t || DecodeHtmlPipe)();\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"decodeHtml\",\n    type: DecodeHtmlPipe,\n    pure: true\n  });\n}", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,cAAc;EACzBC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;IACxB,MAAMC,MAAM,GAAG,IAAIC,SAAS,EAAE;IAC9B,MAAMC,aAAa,GAAGF,MAAM,CAACG,eAAe,CAACJ,KAAK,EAAE,WAAW,CAAC,CAACK,eAAe,CAACC,WAAW;IAC5F,OAAOH,aAAa,IAAIH,KAAK;EAC/B;EAAC,QAAAO,CAAA;qBANUT,cAAc;EAAA;EAAA,QAAAU,EAAA;;UAAdV,cAAc;IAAAW,IAAA;EAAA", "names": ["DecodeHtmlPipe", "transform", "value", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decodedString", "parseFromString", "documentElement", "textContent", "_", "_2", "pure"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\pipes\\decode-html.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'decodeHtml'\r\n})\r\nexport class DecodeHtmlPipe implements PipeTransform {\r\n  transform(value: string): string {\r\n    if (!value) return value;\r\n    const parser = new DOMParser();\r\n    const decodedString = parser.parseFromString(value, 'text/html').documentElement.textContent;\r\n    return decodedString || value;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
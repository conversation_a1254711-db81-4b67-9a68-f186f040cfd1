{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nimport { ResolveEnd } from '@angular/router';\nimport { BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as _ from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// Injection token for the core custom settings\nexport const CORE_CUSTOM_CONFIG = new InjectionToken('coreCustomConfig');\nexport class CoreConfigService {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param _config\r\n   * @param {Router} _router\r\n   */\n  constructor(_router, _config) {\n    this._router = _router;\n    this._config = _config;\n    this.visible_layout = true;\n    // Get the config from local storage\n    if (_config.layout.enableLocalStorage) {\n      this.localConfig = JSON.parse(localStorage.getItem('config'));\n    } else {\n      localStorage.removeItem('config');\n    }\n    // Set the defaultConfig to localConfig if we have else appConfig (app-config.ts)\n    this._defaultConfig = this.localConfig ? this.localConfig : _config;\n    // Initialize the config service\n    this._initConfig();\n  }\n  //  Accessors\n  // -----------------------------------------------------------------------------------------------------\n  // Set the config\n  set config(data) {\n    let config;\n    // Set config = localConfig, If we have else defaultConfig\n    if (this.localConfig) {\n      config = this.localConfig;\n    } else {\n      config = this._configSubject.getValue();\n    }\n    // Merge provided data with config, and create new merged config\n    config = _.merge({}, config, data);\n    // Set config to local storage if enableLocalStorage parameter is true\n    if (config.layout.enableLocalStorage) {\n      localStorage.setItem('config', JSON.stringify(config));\n    }\n    // Inform the observers\n    this._configSubject.next(config);\n  }\n  // Get the config\n  get config() {\n    return this._configSubject.asObservable();\n  }\n  /**\r\n   * Get default config\r\n   *\r\n   * @returns {any}\r\n   */\n  get defaultConfig() {\n    return this._defaultConfig;\n  }\n  // Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Initialize\r\n   *\r\n   * @private\r\n   */\n  _initConfig() {\n    // Set the config from the default config\n    this._configSubject = new BehaviorSubject(_.cloneDeep(this._defaultConfig));\n    // On every RoutesRecognized event\n    // Check if localDefault (localStorage if we have else defaultConfig) is different form the default one\n    this._router.events.pipe(filter(event => event instanceof ResolveEnd)).subscribe(() => {\n      // Get the local config from local storage\n      this.localConfig = JSON.parse(localStorage.getItem('config'));\n      // Set localDefault to localConfig if we have else defaultConfig\n      let localDefault = this.localConfig ? this.localConfig : this._defaultConfig;\n      // If localDefault is different form the provided config (page config)\n      if (!_.isEqual(this._configSubject.getValue().layout, localDefault.layout)) {\n        // Clone the current config\n        const config = _.cloneDeep(this._configSubject.getValue());\n        // Reset the layout from the default config\n        config.layout = _.cloneDeep(localDefault.layout);\n        // Set the config\n        this._configSubject.next(config);\n      }\n    });\n  }\n  // Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Set config\r\n   *\r\n   * @param data\r\n   * @param {{emitEvent: boolean}} param\r\n   */\n  setConfig(data, param = {\n    emitEvent: true\n  }) {\n    let config;\n    // Set config = localConfig, If we have else defaultConfig\n    this.localConfig = JSON.parse(localStorage.getItem('config'));\n    if (this.localConfig) {\n      config = this.localConfig;\n    } else {\n      config = this._configSubject.getValue();\n    }\n    // Merge provided value with config, and create new merged config\n    config = _.merge({}, config, data);\n    // Set config to local storage if enableLocalStorage parameter is true\n    if (config.layout.enableLocalStorage) {\n      localStorage.setItem('config', JSON.stringify(config));\n    }\n    // If emitEvent option is true...\n    if (param.emitEvent === true) {\n      // Inform the observers\n      this._configSubject.next(config);\n    }\n  }\n  /**\r\n   * Get config\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\n  getConfig() {\n    return this._configSubject.asObservable();\n  }\n  /**\r\n   * Reset to the default config\r\n   */\n  resetConfig() {\n    this._configSubject.next(_.cloneDeep(this._defaultConfig));\n  }\n  static #_ = this.ɵfac = function CoreConfigService_Factory(t) {\n    return new (t || CoreConfigService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(CORE_CUSTOM_CONFIG));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CoreConfigService,\n    factory: CoreConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAA6BA,cAAc,QAAQ,eAAe;AAClE,SAASC,UAAU,QAAgB,iBAAiB;AAEpD,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,MAAM,QAAQ,gBAAgB;AAEvC,OAAO,KAAKC,CAAC,MAAM,QAAQ;;;AAE3B;AACA,OAAO,MAAMC,kBAAkB,GAAG,IAAIL,cAAc,CAAC,kBAAkB,CAAC;AAKxE,OAAM,MAAOM,iBAAiB;EAM5B;;;;;;EAMAC,YAAoBC,OAAe,EAAsCC,OAAO;IAA5D,KAAAD,OAAO,GAAPA,OAAO;IAA8C,KAAAC,OAAO,GAAPA,OAAO;IAPzE,KAAAC,cAAc,GAAG,IAAI;IAQ1B;IACA,IAAID,OAAO,CAACE,MAAM,CAACC,kBAAkB,EAAE;MACrC,IAAI,CAACC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC9D,MAAM;MACLD,YAAY,CAACE,UAAU,CAAC,QAAQ,CAAC;;IAGnC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAGJ,OAAO;IAEnE;IACA,IAAI,CAACW,WAAW,EAAE;EACpB;EAEA;EACA;EAEA;EACA,IAAIC,MAAMA,CAACC,IAAI;IACb,IAAID,MAAM;IAEV;IACA,IAAI,IAAI,CAACR,WAAW,EAAE;MACpBQ,MAAM,GAAG,IAAI,CAACR,WAAW;KAC1B,MAAM;MACLQ,MAAM,GAAG,IAAI,CAACE,cAAc,CAACC,QAAQ,EAAE;;IAGzC;IACAH,MAAM,GAAGjB,CAAC,CAACqB,KAAK,CAAC,EAAE,EAAEJ,MAAM,EAAEC,IAAI,CAAC;IAElC;IACA,IAAID,MAAM,CAACV,MAAM,CAACC,kBAAkB,EAAE;MACpCI,YAAY,CAACU,OAAO,CAAC,QAAQ,EAAEZ,IAAI,CAACa,SAAS,CAACN,MAAM,CAAC,CAAC;;IAGxD;IACA,IAAI,CAACE,cAAc,CAACK,IAAI,CAACP,MAAM,CAAC;EAClC;EAEA;EACA,IAAIA,MAAMA,CAAA;IACR,OAAO,IAAI,CAACE,cAAc,CAACM,YAAY,EAAE;EAC3C;EAEA;;;;;EAKA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACX,cAAc;EAC5B;EAEA;EACA;EAEA;;;;;EAKQC,WAAWA,CAAA;IACjB;IACA,IAAI,CAACG,cAAc,GAAG,IAAIrB,eAAe,CAACE,CAAC,CAAC2B,SAAS,CAAC,IAAI,CAACZ,cAAc,CAAC,CAAC;IAE3E;IACA;IACA,IAAI,CAACX,OAAO,CAACwB,MAAM,CAACC,IAAI,CAAC9B,MAAM,CAAC+B,KAAK,IAAIA,KAAK,YAAYjC,UAAU,CAAC,CAAC,CAACkC,SAAS,CAAC,MAAK;MACpF;MACA,IAAI,CAACtB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;MAE7D;MACA,IAAImB,YAAY,GAAG,IAAI,CAACvB,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI,CAACM,cAAc;MAE5E;MACA,IAAI,CAACf,CAAC,CAACiC,OAAO,CAAC,IAAI,CAACd,cAAc,CAACC,QAAQ,EAAE,CAACb,MAAM,EAAEyB,YAAY,CAACzB,MAAM,CAAC,EAAE;QAC1E;QACA,MAAMU,MAAM,GAAGjB,CAAC,CAAC2B,SAAS,CAAC,IAAI,CAACR,cAAc,CAACC,QAAQ,EAAE,CAAC;QAE1D;QACAH,MAAM,CAACV,MAAM,GAAGP,CAAC,CAAC2B,SAAS,CAACK,YAAY,CAACzB,MAAM,CAAC;QAEhD;QACA,IAAI,CAACY,cAAc,CAACK,IAAI,CAACP,MAAM,CAAC;;IAEpC,CAAC,CAAC;EACJ;EAEA;EACA;EAEA;;;;;;EAMAiB,SAASA,CAAChB,IAAI,EAAEiB,KAAK,GAAG;IAAEC,SAAS,EAAE;EAAI,CAAE;IACzC,IAAInB,MAAM;IAEV;IACA,IAAI,CAACR,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC7D,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpBQ,MAAM,GAAG,IAAI,CAACR,WAAW;KAC1B,MAAM;MACLQ,MAAM,GAAG,IAAI,CAACE,cAAc,CAACC,QAAQ,EAAE;;IAGzC;IACAH,MAAM,GAAGjB,CAAC,CAACqB,KAAK,CAAC,EAAE,EAAEJ,MAAM,EAAEC,IAAI,CAAC;IAElC;IACA,IAAID,MAAM,CAACV,MAAM,CAACC,kBAAkB,EAAE;MACpCI,YAAY,CAACU,OAAO,CAAC,QAAQ,EAAEZ,IAAI,CAACa,SAAS,CAACN,MAAM,CAAC,CAAC;;IAGxD;IACA,IAAIkB,KAAK,CAACC,SAAS,KAAK,IAAI,EAAE;MAC5B;MACA,IAAI,CAACjB,cAAc,CAACK,IAAI,CAACP,MAAM,CAAC;;EAEpC;EAEA;;;;;EAKAoB,SAASA,CAAA;IACP,OAAO,IAAI,CAAClB,cAAc,CAACM,YAAY,EAAE;EAC3C;EAEA;;;EAGAa,WAAWA,CAAA;IACT,IAAI,CAACnB,cAAc,CAACK,IAAI,CAACxB,CAAC,CAAC2B,SAAS,CAAC,IAAI,CAACZ,cAAc,CAAC,CAAC;EAC5D;EAAC,QAAAf,CAAA;qBAvJUE,iBAAiB,EAAAqC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAYiBvC,kBAAkB;EAAA;EAAA,QAAA0C,EAAA;WAZpDzC,iBAAiB;IAAA0C,OAAA,EAAjB1C,iBAAiB,CAAA2C,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["InjectionToken", "ResolveEnd", "BehaviorSubject", "filter", "_", "CORE_CUSTOM_CONFIG", "CoreConfigService", "constructor", "_router", "_config", "visible_layout", "layout", "enableLocalStorage", "localConfig", "JSON", "parse", "localStorage", "getItem", "removeItem", "_defaultConfig", "_initConfig", "config", "data", "_configSubject", "getValue", "merge", "setItem", "stringify", "next", "asObservable", "defaultConfig", "cloneDeep", "events", "pipe", "event", "subscribe", "localDefault", "isEqual", "setConfig", "param", "emitEvent", "getConfig", "resetConfig", "i0", "ɵɵinject", "i1", "Router", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\services\\config.service.ts"], "sourcesContent": ["import { Inject, Injectable, InjectionToken } from '@angular/core';\r\nimport { ResolveEnd, Router } from '@angular/router';\r\n\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { filter } from 'rxjs/operators';\r\n\r\nimport * as _ from 'lodash';\r\n\r\n// Injection token for the core custom settings\r\nexport const CORE_CUSTOM_CONFIG = new InjectionToken('coreCustomConfig');\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CoreConfigService {\r\n  // Private\r\n  public localConfig: any;\r\n  private readonly _defaultConfig: any;\r\n  private _configSubject: BehaviorSubject<any>;\r\n  public visible_layout = true;\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param _config\r\n   * @param {Router} _router\r\n   */\r\n  constructor(private _router: Router, @Inject(CORE_CUSTOM_CONFIG) private _config) {\r\n    // Get the config from local storage\r\n    if (_config.layout.enableLocalStorage) {\r\n      this.localConfig = JSON.parse(localStorage.getItem('config'));\r\n    } else {\r\n      localStorage.removeItem('config');\r\n    }\r\n\r\n    // Set the defaultConfig to localConfig if we have else appConfig (app-config.ts)\r\n    this._defaultConfig = this.localConfig ? this.localConfig : _config;\r\n\r\n    // Initialize the config service\r\n    this._initConfig();\r\n  }\r\n\r\n  //  Accessors\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  // Set the config\r\n  set config(data) {\r\n    let config;\r\n\r\n    // Set config = localConfig, If we have else defaultConfig\r\n    if (this.localConfig) {\r\n      config = this.localConfig;\r\n    } else {\r\n      config = this._configSubject.getValue();\r\n    }\r\n\r\n    // Merge provided data with config, and create new merged config\r\n    config = _.merge({}, config, data);\r\n\r\n    // Set config to local storage if enableLocalStorage parameter is true\r\n    if (config.layout.enableLocalStorage) {\r\n      localStorage.setItem('config', JSON.stringify(config));\r\n    }\r\n\r\n    // Inform the observers\r\n    this._configSubject.next(config);\r\n  }\r\n\r\n  // Get the config\r\n  get config(): any | Observable<any> {\r\n    return this._configSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Get default config\r\n   *\r\n   * @returns {any}\r\n   */\r\n  get defaultConfig(): any {\r\n    return this._defaultConfig;\r\n  }\r\n\r\n  // Private methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Initialize\r\n   *\r\n   * @private\r\n   */\r\n  private _initConfig(): void {\r\n    // Set the config from the default config\r\n    this._configSubject = new BehaviorSubject(_.cloneDeep(this._defaultConfig));\r\n\r\n    // On every RoutesRecognized event\r\n    // Check if localDefault (localStorage if we have else defaultConfig) is different form the default one\r\n    this._router.events.pipe(filter(event => event instanceof ResolveEnd)).subscribe(() => {\r\n      // Get the local config from local storage\r\n      this.localConfig = JSON.parse(localStorage.getItem('config'));\r\n\r\n      // Set localDefault to localConfig if we have else defaultConfig\r\n      let localDefault = this.localConfig ? this.localConfig : this._defaultConfig;\r\n\r\n      // If localDefault is different form the provided config (page config)\r\n      if (!_.isEqual(this._configSubject.getValue().layout, localDefault.layout)) {\r\n        // Clone the current config\r\n        const config = _.cloneDeep(this._configSubject.getValue());\r\n\r\n        // Reset the layout from the default config\r\n        config.layout = _.cloneDeep(localDefault.layout);\r\n\r\n        // Set the config\r\n        this._configSubject.next(config);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Public methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Set config\r\n   *\r\n   * @param data\r\n   * @param {{emitEvent: boolean}} param\r\n   */\r\n  setConfig(data, param = { emitEvent: true }): void {\r\n    let config;\r\n\r\n    // Set config = localConfig, If we have else defaultConfig\r\n    this.localConfig = JSON.parse(localStorage.getItem('config'));\r\n    if (this.localConfig) {\r\n      config = this.localConfig;\r\n    } else {\r\n      config = this._configSubject.getValue();\r\n    }\r\n\r\n    // Merge provided value with config, and create new merged config\r\n    config = _.merge({}, config, data);\r\n\r\n    // Set config to local storage if enableLocalStorage parameter is true\r\n    if (config.layout.enableLocalStorage) {\r\n      localStorage.setItem('config', JSON.stringify(config));\r\n    }\r\n\r\n    // If emitEvent option is true...\r\n    if (param.emitEvent === true) {\r\n      // Inform the observers\r\n      this._configSubject.next(config);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get config\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\r\n  getConfig(): Observable<any> {\r\n    return this._configSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Reset to the default config\r\n   */\r\n  resetConfig(): void {\r\n    this._configSubject.next(_.cloneDeep(this._defaultConfig));\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
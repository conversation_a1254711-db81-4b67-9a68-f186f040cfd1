{"ast": null, "code": "//! moment.js locale configuration\n//! locale : <PERSON><PERSON> (East Timor) [tet]\n//! author : <PERSON> : https://github.com/joshbrooks\n//! author : <PERSON><PERSON> : https://github.com/marobo\n//! author : <PERSON> : https://github.com/soniasimoes\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var tet = moment.defineLocale('tet', {\n    months: 'Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru'.split('_'),\n    monthsShort: 'Jan_Fev_Mar_<PERSON>r_<PERSON>_<PERSON>_Jul_Ago_Set_Out_Nov_Dez'.split('_'),\n    weekdays: 'Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu'.split('_'),\n    weekdaysShort: 'Dom_Seg_Ters_Kua_Kint_Sest_Sab'.split('_'),\n    weekdaysMin: 'Do_Seg_Te_Ku_Ki_Ses_Sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Ohin iha] LT',\n      nextDay: '[Aban iha] LT',\n      nextWeek: 'dddd [iha] LT',\n      lastDay: '[Horiseik iha] LT',\n      lastWeek: 'dddd [semana kotuk] [iha] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'iha %s',\n      past: '%s liuba',\n      s: 'segundu balun',\n      ss: 'segundu %d',\n      m: 'minutu ida',\n      mm: 'minutu %d',\n      h: 'oras ida',\n      hh: 'oras %d',\n      d: 'loron ida',\n      dd: 'loron %d',\n      M: 'fulan ida',\n      MM: 'fulan %d',\n      y: 'tinan ida',\n      yy: 'tinan %d'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = ~~(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return tet;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "tet", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "b", "output", "week", "dow", "doy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/moment/locale/tet.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : <PERSON><PERSON> (East Timor) [tet]\n//! author : <PERSON> : https://github.com/joshbrooks\n//! author : <PERSON><PERSON> : https://github.com/marobo\n//! author : <PERSON> : https://github.com/soniasimoes\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var tet = moment.defineLocale('tet', {\n        months: 'Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru'.split(\n            '_'\n        ),\n        monthsShort: '<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>r_<PERSON>_<PERSON>_Jul_Ago_Set_Out_Nov_Dez'.split('_'),\n        weekdays: 'Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu'.split('_'),\n        weekdaysShort: 'Dom_Seg_Ters_Kua_Kint_Sest_Sab'.split('_'),\n        weekdaysMin: 'Do_Seg_Te_Ku_Ki_Ses_Sa'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Ohin iha] LT',\n            nextDay: '[Aban iha] LT',\n            nextWeek: 'dddd [iha] LT',\n            lastDay: '[Horiseik iha] LT',\n            lastWeek: 'dddd [semana kotuk] [iha] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'iha %s',\n            past: '%s liuba',\n            s: 'segundu balun',\n            ss: 'segundu %d',\n            m: 'minutu ida',\n            mm: 'minutu %d',\n            h: 'oras ida',\n            hh: 'oras %d',\n            d: 'loron ida',\n            dd: 'loron %d',\n            M: 'fulan ida',\n            MM: 'fulan %d',\n            y: 'tinan ida',\n            yy: 'tinan %d',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n        ordinal: function (number) {\n            var b = number % 10,\n                output =\n                    ~~((number % 100) / 10) === 1\n                        ? 'th'\n                        : b === 1\n                        ? 'st'\n                        : b === 2\n                        ? 'nd'\n                        : b === 3\n                        ? 'rd'\n                        : 'th';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return tet;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,GAAG,GAAGD,MAAM,CAACE,YAAY,CAAC,KAAK,EAAE;IACjCC,MAAM,EAAE,yFAAyF,CAACC,KAAK,CACnG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,iDAAiD,CAACF,KAAK,CAAC,GAAG,CAAC;IACtEG,aAAa,EAAE,gCAAgC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC1DI,WAAW,EAAE,wBAAwB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAChDK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,8BAA8B;MACxCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,sBAAsB;IAC9CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,MAAM,GACF,CAAC,EAAGF,MAAM,GAAG,GAAG,GAAI,EAAE,CAAC,KAAK,CAAC,GACvB,IAAI,GACJC,CAAC,KAAK,CAAC,GACP,IAAI,GACJA,CAAC,KAAK,CAAC,GACP,IAAI,GACJA,CAAC,KAAK,CAAC,GACP,IAAI,GACJ,IAAI;MAClB,OAAOD,MAAM,GAAGE,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAO5C,GAAG;AAEd,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
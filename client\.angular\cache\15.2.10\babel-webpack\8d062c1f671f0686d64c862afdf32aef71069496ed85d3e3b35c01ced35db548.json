{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../services/auto-schedule.service\";\nimport * as i4 from \"../../../../services/loading.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i7 from \"@ngx-formly/core\";\nexport class ModalCrudBreakComponent {\n  constructor(_modalService, _translateService, _autoSchedule, _loadingService) {\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._autoSchedule = _autoSchedule;\n    this._loadingService = _loadingService;\n    this.breakModalParams = {\n      locationId: null,\n      tournamentId: null,\n      timeSlotId: null,\n      lastTimeSlotId: null\n    };\n    this.onSubmit = new EventEmitter();\n    this.addBreakForm = new FormGroup({});\n    this.addBreakModel = {};\n    this.addBreakFields = [{\n      key: 'tournament_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'location_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'description',\n      type: 'input',\n      props: {\n        label: 'Event name',\n        placeholder: 'Enter event name (Default: Break)',\n        required: false,\n        type: 'text',\n        maxLength: 100\n      },\n      defaultValue: 'Break',\n      // validate for it\n      validation: {\n        messages: {\n          maxLength: this._translateService.instant('Event name must be less than 100 characters.')\n        }\n      }\n    }, {\n      key: 'break_durations',\n      type: 'input',\n      props: {\n        label: 'Break duration',\n        placeholder: 'Enter break duration (in minutes)',\n        required: true,\n        type: 'number',\n        min: 1,\n        max: 1440\n      },\n      defaultValue: 30,\n      validation: {\n        messages: {\n          required: this._translateService.instant('Break duration is required.'),\n          min: this._translateService.instant('Break duration must be at least 1 minute.'),\n          max: this._translateService.instant(`The entered duration exceeds the allowable limit. Only 1440 minutes remain until the end of the day.`)\n        }\n      },\n      validators: {\n        intValidator: {\n          expression: control => {\n            const value = control.value;\n            return !value || Number.isInteger(value);\n          },\n          message: this._translateService.instant('Break duration must be an integer number.')\n        }\n      }\n    }, {\n      key: 'time_slot_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'last_time_slot_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'config_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n  }\n  ngOnInit() {\n    this.addBreakFields[0].defaultValue = this.breakModalParams.tournamentId;\n    this.addBreakFields[1].defaultValue = this.breakModalParams.locationId;\n    this.addBreakFields[5].defaultValue = this.breakModalParams.lastTimeSlotId;\n    this.addBreakFields[6].defaultValue = this.breakModalParams.configId;\n    this.addBreakFields[3].defaultValue = Math.min(this.breakModalParams.maxBreakDuration, 30);\n    this.addBreakFields[3].props.max = this.breakModalParams.maxBreakDuration;\n    this.addBreakFields[3].validation.messages.max = `The entered duration exceeds the allowable limit. Only ${this.breakModalParams.maxBreakDuration} minutes remain until the end of the day.`;\n    if (this.breakModalParams.timeSlotId) {\n      this.addBreakFields[2].defaultValue = this.breakModalParams.description;\n      this.addBreakFields[3].defaultValue = this.breakModalParams.breakDurations;\n      this.addBreakFields[4].defaultValue = this.breakModalParams.timeSlotId;\n    }\n  }\n  onSubmitCrudBreak(model) {\n    console.log(model);\n    // return;\n    this._loadingService.show();\n    const action = this.breakModalParams.timeSlotId ? this._autoSchedule.updateBreak(model) : this._autoSchedule.addBreak(model);\n    action.subscribe({\n      next: res => {\n        console.log('res', res);\n        if (this.breakModalParams.timeSlotId) {\n          Swal.fire({\n            title: this._translateService.instant('Success!'),\n            text: this._translateService.instant('Break updated successfully'),\n            icon: 'success'\n          });\n        }\n        this.onSubmit.emit(res);\n        this._modalService.dismissAll();\n      },\n      error: error => {\n        console.error(this.breakModalParams.timeSlotId ? 'Error updating break:' : 'Error adding break:', error);\n      },\n      complete: () => {\n        this._loadingService.dismiss();\n      }\n    });\n  }\n  closeModal() {\n    this.addBreakModel = {};\n    this._modalService.dismissAll();\n  }\n  clearForm() {\n    this.addBreakForm.reset();\n  }\n  static #_ = this.ɵfac = function ModalCrudBreakComponent_Factory(t) {\n    return new (t || ModalCrudBreakComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AutoScheduleService), i0.ɵɵdirectiveInject(i4.LoadingService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalCrudBreakComponent,\n    selectors: [[\"app-modal-crud-break\"]],\n    inputs: {\n      breakModalParams: \"breakModalParams\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalAddBreak\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalCrudBreakComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalCrudBreakComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalCrudBreakComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitCrudBreak(ctx.addBreakModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalCrudBreakComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitCrudBreak(ctx.addBreakModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.breakModalParams.timeSlotId ? \"Update Break\" : i0.ɵɵpipeBind1(3, 7, \"Add Break\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.addBreakForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.addBreakForm)(\"fields\", ctx.addBreakFields)(\"model\", ctx.addBreakModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.addBreakForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Submit\"), \" \");\n      }\n    },\n    dependencies: [i5.ɵNgNoValidate, i5.NgControlStatusGroup, i5.FormGroupDirective, i6.RippleEffectDirective, i7.FormlyForm, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AAEtE,SAASC,SAAS,QAAQ,gBAAgB;AAK1C,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;AAoB9B,OAAM,MAAOC,uBAAuB;EAoGlCC,YACUC,aAAuB,EACvBC,iBAAmC,EACnCC,aAAkC,EAClCC,eAA+B;IAH/B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IAtGhB,KAAAC,gBAAgB,GAAqB;MAC5CC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE;KACjB;IAES,KAAAC,QAAQ,GAAG,IAAId,YAAY,EAAE;IAEvC,KAAAe,YAAY,GAAG,IAAId,SAAS,CAAC,EAAE,CAAC;IAChC,KAAAe,aAAa,GAAG,EAAE;IAClB,KAAAC,cAAc,GAAwB,CACpC;MACEC,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,EACD;MACED,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,EACD;MACED,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,YAAY;QACnBC,WAAW,EAAE,mCAAmC;QAChDC,QAAQ,EAAE,KAAK;QACfJ,IAAI,EAAE,MAAM;QACZK,SAAS,EAAE;OACZ;MACDC,YAAY,EAAE,OAAO;MACrB;MACAC,UAAU,EAAE;QACVC,QAAQ,EAAE;UACRH,SAAS,EAAE,IAAI,CAAClB,iBAAiB,CAACsB,OAAO,CAAC,8CAA8C;;;KAG7F,EACD;MACEV,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,mCAAmC;QAChDC,QAAQ,EAAE,IAAI;QACdJ,IAAI,EAAE,QAAQ;QACdU,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE;OACN;MACDL,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE;QACVC,QAAQ,EAAE;UACRJ,QAAQ,EAAE,IAAI,CAACjB,iBAAiB,CAACsB,OAAO,CAAC,6BAA6B,CAAC;UACvEC,GAAG,EAAE,IAAI,CAACvB,iBAAiB,CAACsB,OAAO,CAAC,2CAA2C,CAAC;UAChFE,GAAG,EAAE,IAAI,CAACxB,iBAAiB,CAACsB,OAAO,CAAC,sGAAsG;;OAE7I;MACDG,UAAU,EAAE;QACVC,YAAY,EAAE;UACZC,UAAU,EAAGC,OAAO,IAAI;YACtB,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;YAC3B,OAAO,CAACA,KAAK,IAAIC,MAAM,CAACC,SAAS,CAACF,KAAK,CAAC;UAC1C,CAAC;UACDG,OAAO,EAAE,IAAI,CAAChC,iBAAiB,CAACsB,OAAO,CAAC,2CAA2C;;;KAGxF,EACD;MACEV,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,EACD;MACED,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,EAED;MACED,GAAG,EAAE,WAAW;MAChBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,CAEF;EASD;EAEAoB,QAAQA,CAAA;IACN,IAAI,CAACtB,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACE,YAAY;IACxE,IAAI,CAACM,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACC,UAAU;IACtE,IAAI,CAACO,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACI,cAAc;IAC1E,IAAI,CAACI,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAAC+B,QAAQ;IACpE,IAAI,CAACvB,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAGgB,IAAI,CAACZ,GAAG,CAAC,IAAI,CAACpB,gBAAgB,CAACiC,gBAAgB,EAAE,EAAE,CAAC;IAC1F,IAAI,CAACzB,cAAc,CAAC,CAAC,CAAC,CAACG,KAAK,CAACU,GAAG,GAAG,IAAI,CAACrB,gBAAgB,CAACiC,gBAAgB;IACzE,IAAI,CAACzB,cAAc,CAAC,CAAC,CAAC,CAACS,UAAU,CAACC,QAAQ,CAACG,GAAG,GAAG,0DAA0D,IAAI,CAACrB,gBAAgB,CAACiC,gBAAgB,2CAA2C;IAE5L,IAAI,IAAI,CAACjC,gBAAgB,CAACG,UAAU,EAAE;MACpC,IAAI,CAACK,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACkC,WAAW;MACvE,IAAI,CAAC1B,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACmC,cAAc;MAC1E,IAAI,CAAC3B,cAAc,CAAC,CAAC,CAAC,CAACQ,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACG,UAAU;;EAE1E;EAEAiC,iBAAiBA,CAACC,KAAK;IACrBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB;IACA,IAAI,CAACtC,eAAe,CAACyC,IAAI,EAAE;IAE3B,MAAMC,MAAM,GAAG,IAAI,CAACzC,gBAAgB,CAACG,UAAU,GAC3C,IAAI,CAACL,aAAa,CAAC4C,WAAW,CAACL,KAAK,CAAC,GACrC,IAAI,CAACvC,aAAa,CAAC6C,QAAQ,CAACN,KAAK,CAAC;IAEtCI,MAAM,CAACG,SAAS,CAAC;MACfC,IAAI,EAAGC,GAAG,IAAI;QACZR,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEO,GAAG,CAAC;QACvB,IAAI,IAAI,CAAC9C,gBAAgB,CAACG,UAAU,EAAE;UACpCV,IAAI,CAACsD,IAAI,CAAC;YACRC,KAAK,EAAE,IAAI,CAACnD,iBAAiB,CAACsB,OAAO,CAAC,UAAU,CAAC;YACjD8B,IAAI,EAAE,IAAI,CAACpD,iBAAiB,CAACsB,OAAO,CAAC,4BAA4B,CAAC;YAClE+B,IAAI,EAAE;WACP,CAAC;;QAEJ,IAAI,CAAC7C,QAAQ,CAAC8C,IAAI,CAACL,GAAG,CAAC;QACvB,IAAI,CAAClD,aAAa,CAACwD,UAAU,EAAE;MACjC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACff,OAAO,CAACe,KAAK,CACX,IAAI,CAACrD,gBAAgB,CAACG,UAAU,GAC5B,uBAAuB,GACvB,qBAAqB,EACzBkD,KAAK,CACN;MACH,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACvD,eAAe,CAACwD,OAAO,EAAE;MAChC;KACD,CAAC;EACJ;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACjD,aAAa,GAAG,EAAE;IACvB,IAAI,CAACX,aAAa,CAACwD,UAAU,EAAE;EACjC;EAEAK,SAASA,CAAA;IACP,IAAI,CAACnD,YAAY,CAACoD,KAAK,EAAE;EAC3B;EAAC,QAAAC,CAAA;qBAxKUjE,uBAAuB,EAAAkE,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAAvB5E,uBAAuB;IAAA6E,SAAA;IAAAC,MAAA;MAAAxE,gBAAA;IAAA;IAAAyE,OAAA;MAAApE,QAAA;IAAA;IAAAqE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3BpCnB,EAAA,CAAAqB,cAAA,aAA0B;QAEDrB,EAAA,CAAAsB,MAAA,GAA4E;;QAAAtB,EAAA,CAAAuB,YAAA,EAAK;QACxGvB,EAAA,CAAAqB,cAAA,gBAKC;QAFCrB,EAAA,CAAAwB,UAAA,mBAAAC,yDAAA;UAAA,OAASL,GAAA,CAAAxB,UAAA,EAAY;QAAA,EAAC;QAGtBI,EAAA,CAAAqB,cAAA,cAAyB;QAAArB,EAAA,CAAAsB,MAAA,aAAO;QAAAtB,EAAA,CAAAuB,YAAA,EAAO;QAG3CvB,EAAA,CAAAqB,cAAA,cAGC;QADCrB,EAAA,CAAAwB,UAAA,sBAAAE,0DAAA;UAAA,OAAYN,GAAA,CAAA5C,iBAAA,CAAA4C,GAAA,CAAAzE,aAAA,CAAgC;QAAA,EAAC;QAE7CqD,EAAA,CAAAqB,cAAA,aAAkD;QAK9CrB,EAAA,CAAAwB,UAAA,oBAAAG,+DAAA;UAAA,OAAUP,GAAA,CAAA5C,iBAAA,CAAA4C,GAAA,CAAAzE,aAAA,CAAgC;QAAA,EAAC;QAC5CqD,EAAA,CAAAuB,YAAA,EAAc;QAEjBvB,EAAA,CAAAqB,cAAA,cAA0B;QAItBrB,EAAA,CAAAsB,MAAA,IACF;;QAAAtB,EAAA,CAAAuB,YAAA,EAAS;;;QA3BYvB,EAAA,CAAA4B,SAAA,GAA4E;QAA5E5B,EAAA,CAAA6B,iBAAA,CAAAT,GAAA,CAAAhF,gBAAA,CAAAG,UAAA,oBAAAyD,EAAA,CAAA8B,WAAA,oBAA4E;QAWnG9B,EAAA,CAAA4B,SAAA,GAA0B;QAA1B5B,EAAA,CAAA+B,UAAA,cAAAX,GAAA,CAAA1E,YAAA,CAA0B;QAKtBsD,EAAA,CAAA4B,SAAA,GAAqB;QAArB5B,EAAA,CAAA+B,UAAA,SAAAX,GAAA,CAAA1E,YAAA,CAAqB,WAAA0E,GAAA,CAAAxE,cAAA,WAAAwE,GAAA,CAAAzE,aAAA;QAQfqD,EAAA,CAAA4B,SAAA,GAAiC;QAAjC5B,EAAA,CAAA+B,UAAA,aAAAX,GAAA,CAAA1E,YAAA,CAAAsF,OAAA,CAAiC;QAEvChC,EAAA,CAAA4B,SAAA,GACF;QADE5B,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAA8B,WAAA,uBACF", "names": ["EventEmitter", "FormGroup", "<PERSON><PERSON>", "ModalCrudBreakComponent", "constructor", "_modalService", "_translateService", "_autoSchedule", "_loadingService", "breakModalParams", "locationId", "tournamentId", "timeSlotId", "lastTimeSlotId", "onSubmit", "addBreakForm", "addBreakModel", "addBreakFields", "key", "type", "props", "label", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "defaultValue", "validation", "messages", "instant", "min", "max", "validators", "intValidator", "expression", "control", "value", "Number", "isInteger", "message", "ngOnInit", "configId", "Math", "maxBreakDuration", "description", "breakDurations", "onSubmitCrudBreak", "model", "console", "log", "show", "action", "updateBreak", "addBreak", "subscribe", "next", "res", "fire", "title", "text", "icon", "emit", "dismissAll", "error", "complete", "dismiss", "closeModal", "clearForm", "reset", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbModal", "i2", "TranslateService", "i3", "AutoScheduleService", "i4", "LoadingService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ModalCrudBreakComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModalCrudBreakComponent_Template_button_click_4_listener", "ModalCrudBreakComponent_Template_form_ngSubmit_7_listener", "ModalCrudBreakComponent_Template_formly_form_submit_9_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "invalid", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-crud-break\\modal-crud-break.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-crud-break\\modal-crud-break.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AutoScheduleService } from '../../../../services/auto-schedule.service';\r\nimport { LoadingService } from '../../../../services/loading.service';\r\nimport Swal from 'sweetalert2';\r\n\r\nexport type BreakModalParams = {\r\n  locationId: number | null;\r\n  tournamentId: number | null;\r\n  timeSlotId?: number | null;\r\n  description?: string | null;\r\n  breakDurations?: number | null;\r\n  lastTimeSlotId?: number | null;\r\n  configId?: number | null;\r\n  maxBreakDuration?: number | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-modal-crud-break',\r\n  templateUrl: './modal-crud-break.component.html',\r\n  styleUrls: ['./modal-crud-break.component.scss']\r\n})\r\n\r\n\r\nexport class ModalCrudBreakComponent {\r\n\r\n  @Input() breakModalParams: BreakModalParams = {\r\n    locationId: null,\r\n    tournamentId: null,\r\n    timeSlotId: null,\r\n    lastTimeSlotId: null\r\n  };\r\n\r\n  @Output() onSubmit = new EventEmitter();\r\n\r\n  addBreakForm = new FormGroup({});\r\n  addBreakModel = {};\r\n  addBreakFields: FormlyFieldConfig[] = [\r\n    {\r\n      key: 'tournament_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    },\r\n    {\r\n      key: 'location_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    },\r\n    {\r\n      key: 'description',\r\n      type: 'input',\r\n      props: {\r\n        label: 'Event name',\r\n        placeholder: 'Enter event name (Default: Break)',\r\n        required: false,\r\n        type: 'text',\r\n        maxLength: 100\r\n      },\r\n      defaultValue: 'Break',\r\n      // validate for it\r\n      validation: {\r\n        messages: {\r\n          maxLength: this._translateService.instant('Event name must be less than 100 characters.')\r\n        }\r\n      }\r\n    },\r\n    {\r\n      key: 'break_durations',\r\n      type: 'input',\r\n      props: {\r\n        label: 'Break duration',\r\n        placeholder: 'Enter break duration (in minutes)',\r\n        required: true,\r\n        type: 'number',\r\n        min: 1,\r\n        max: 1440\r\n      },\r\n      defaultValue: 30,\r\n      validation: {\r\n        messages: {\r\n          required: this._translateService.instant('Break duration is required.'),\r\n          min: this._translateService.instant('Break duration must be at least 1 minute.'),\r\n          max: this._translateService.instant(`The entered duration exceeds the allowable limit. Only 1440 minutes remain until the end of the day.`)\r\n        }\r\n      },\r\n      validators: {\r\n        intValidator: {\r\n          expression: (control) => {\r\n            const value = control.value;\r\n            return !value || Number.isInteger(value);\r\n          },\r\n          message: this._translateService.instant('Break duration must be an integer number.')\r\n        }\r\n      }\r\n    },\r\n    {\r\n      key: 'time_slot_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    },\r\n    {\r\n      key: 'last_time_slot_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    }\r\n    ,\r\n    {\r\n      key: 'config_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    }\r\n\r\n  ];\r\n\r\n  constructor(\r\n    private _modalService: NgbModal,\r\n    private _translateService: TranslateService,\r\n    private _autoSchedule: AutoScheduleService,\r\n    private _loadingService: LoadingService\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.addBreakFields[0].defaultValue = this.breakModalParams.tournamentId;\r\n    this.addBreakFields[1].defaultValue = this.breakModalParams.locationId;\r\n    this.addBreakFields[5].defaultValue = this.breakModalParams.lastTimeSlotId;\r\n    this.addBreakFields[6].defaultValue = this.breakModalParams.configId;\r\n    this.addBreakFields[3].defaultValue = Math.min(this.breakModalParams.maxBreakDuration, 30);\r\n    this.addBreakFields[3].props.max = this.breakModalParams.maxBreakDuration;\r\n    this.addBreakFields[3].validation.messages.max = `The entered duration exceeds the allowable limit. Only ${this.breakModalParams.maxBreakDuration} minutes remain until the end of the day.`;\r\n\r\n    if (this.breakModalParams.timeSlotId) {\r\n      this.addBreakFields[2].defaultValue = this.breakModalParams.description;\r\n      this.addBreakFields[3].defaultValue = this.breakModalParams.breakDurations;\r\n      this.addBreakFields[4].defaultValue = this.breakModalParams.timeSlotId;\r\n    }\r\n  }\r\n\r\n  onSubmitCrudBreak(model) {\r\n    console.log(model);\r\n    // return;\r\n    this._loadingService.show();\r\n\r\n    const action = this.breakModalParams.timeSlotId\r\n      ? this._autoSchedule.updateBreak(model)\r\n      : this._autoSchedule.addBreak(model);\r\n\r\n    action.subscribe({\r\n      next: (res) => {\r\n        console.log('res', res);\r\n        if (this.breakModalParams.timeSlotId) {\r\n          Swal.fire({\r\n            title: this._translateService.instant('Success!'),\r\n            text: this._translateService.instant('Break updated successfully'),\r\n            icon: 'success'\r\n          });\r\n        }\r\n        this.onSubmit.emit(res);\r\n        this._modalService.dismissAll();\r\n      },\r\n      error: (error) => {\r\n        console.error(\r\n          this.breakModalParams.timeSlotId\r\n            ? 'Error updating break:'\r\n            : 'Error adding break:',\r\n          error\r\n        );\r\n      },\r\n      complete: () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    });\r\n  }\r\n\r\n  closeModal() {\r\n    this.addBreakModel = {};\r\n    this._modalService.dismissAll();\r\n  }\r\n\r\n  clearForm() {\r\n    this.addBreakForm.reset();\r\n  }\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\"\r\n      id=\"modalAddBreak\">{{ breakModalParams.timeSlotId ? \"Update Break\" : \"Add Break\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"addBreakForm\"\r\n  (ngSubmit)=\"onSubmitCrudBreak(addBreakModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <formly-form\r\n      [form]=\"addBreakForm\"\r\n      [fields]=\"addBreakFields\"\r\n      [model]=\"addBreakModel\"\r\n      (submit)=\"onSubmitCrudBreak(addBreakModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n            [disabled]=\"addBreakForm.invalid\"\r\n    >\r\n      {{ 'Submit' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport * as QRCode from '@cordobo/qrcode';\nimport * as i1 from '@angular/platform-browser';\nconst _c0 = [\"qrcElement\"];\nclass QRCodeComponent {\n  constructor(renderer, sanitizer) {\n    this.renderer = renderer;\n    this.sanitizer = sanitizer;\n    this.allowEmptyString = false;\n    this.colorDark = \"#000000ff\";\n    this.colorLight = \"#ffffffff\";\n    this.cssClass = \"qrcode\";\n    this.elementType = \"canvas\";\n    this.errorCorrectionLevel = \"M\";\n    this.margin = 4;\n    this.qrdata = \"\";\n    this.scale = 4;\n    this.width = 10;\n    this.qrCodeURL = new EventEmitter();\n    this.context = null;\n  }\n  ngOnChanges() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.createQRCode();\n    })();\n  }\n  isValidQrCodeText(data) {\n    if (this.allowEmptyString === false) {\n      return !(typeof data === \"undefined\" || data === \"\" || data === \"null\" || data === null);\n    }\n    return !(typeof data === \"undefined\");\n  }\n  toDataURL(qrCodeConfig) {\n    return new Promise((resolve, reject) => {\n      QRCode.toDataURL(this.qrdata, qrCodeConfig, (err, url) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(url);\n        }\n      });\n    });\n  }\n  toCanvas(canvas, qrCodeConfig) {\n    return new Promise((resolve, reject) => {\n      QRCode.toCanvas(canvas, this.qrdata, qrCodeConfig, error => {\n        if (error) {\n          reject(error);\n        } else {\n          resolve(\"success\");\n        }\n      });\n    });\n  }\n  toSVG(qrCodeConfig) {\n    return new Promise((resolve, reject) => {\n      QRCode.toString(this.qrdata, qrCodeConfig, (err, url) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(url);\n        }\n      });\n    });\n  }\n  renderElement(element) {\n    for (const node of this.qrcElement.nativeElement.childNodes) {\n      this.renderer.removeChild(this.qrcElement.nativeElement, node);\n    }\n    this.renderer.appendChild(this.qrcElement.nativeElement, element);\n  }\n  createQRCode() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.version && _this2.version > 40) {\n        console.warn(\"[angularx-qrcode] max value for `version` is 40\");\n        _this2.version = 40;\n      } else if (_this2.version && _this2.version < 1) {\n        console.warn(\"[angularx-qrcode]`min value for `version` is 1\");\n        _this2.version = 1;\n      } else if (_this2.version !== undefined && isNaN(_this2.version)) {\n        console.warn(\"[angularx-qrcode] version should be a number, defaulting to auto.\");\n        _this2.version = undefined;\n      }\n      try {\n        if (!_this2.isValidQrCodeText(_this2.qrdata)) {\n          throw new Error(\"[angularx-qrcode] Field `qrdata` is empty, set 'allowEmptyString=\\\"true\\\"' to overwrite this behaviour.\");\n        }\n        if (_this2.isValidQrCodeText(_this2.qrdata) && _this2.qrdata === \"\") {\n          _this2.qrdata = \" \";\n        }\n        const config = {\n          color: {\n            dark: _this2.colorDark,\n            light: _this2.colorLight\n          },\n          errorCorrectionLevel: _this2.errorCorrectionLevel,\n          margin: _this2.margin,\n          scale: _this2.scale,\n          type: _this2.elementType,\n          version: _this2.version,\n          width: _this2.width\n        };\n        const centerImageSrc = _this2.imageSrc;\n        const centerImageHeight = _this2.imageHeight || 40;\n        const centerImageWidth = _this2.imageWidth || 40;\n        switch (_this2.elementType) {\n          case \"canvas\":\n            const canvasElement = _this2.renderer.createElement(\"canvas\");\n            _this2.context = canvasElement.getContext(\"2d\");\n            _this2.toCanvas(canvasElement, config).then(() => {\n              if (_this2.ariaLabel) {\n                _this2.renderer.setAttribute(canvasElement, \"aria-label\", `${_this2.ariaLabel}`);\n              }\n              if (_this2.title) {\n                _this2.renderer.setAttribute(canvasElement, \"title\", `${_this2.title}`);\n              }\n              if (centerImageSrc && _this2.context) {\n                _this2.centerImage = new Image(centerImageWidth, centerImageHeight);\n                if (centerImageSrc !== _this2.centerImage.src) {\n                  _this2.centerImage.src = centerImageSrc;\n                }\n                if (centerImageHeight !== _this2.centerImage.height) {\n                  _this2.centerImage.height = centerImageHeight;\n                }\n                if (centerImageWidth !== _this2.centerImage.width) {\n                  _this2.centerImage.width = centerImageWidth;\n                }\n                const centerImage = _this2.centerImage;\n                if (centerImage) {\n                  centerImage.onload = () => {\n                    _this2.context?.drawImage(centerImage, canvasElement.width / 2 - centerImageWidth / 2, canvasElement.height / 2 - centerImageHeight / 2, centerImageWidth, centerImageHeight);\n                  };\n                }\n              }\n              _this2.renderElement(canvasElement);\n              _this2.emitQRCodeURL(canvasElement);\n            }).catch(e => {\n              console.error(\"[angularx-qrcode] canvas error:\", e);\n            });\n            break;\n          case \"svg\":\n            const svgParentElement = _this2.renderer.createElement(\"div\");\n            _this2.toSVG(config).then(svgString => {\n              _this2.renderer.setProperty(svgParentElement, \"innerHTML\", svgString);\n              const svgElement = svgParentElement.firstChild;\n              _this2.renderer.setAttribute(svgElement, \"height\", `${_this2.width}`);\n              _this2.renderer.setAttribute(svgElement, \"width\", `${_this2.width}`);\n              _this2.renderElement(svgElement);\n              _this2.emitQRCodeURL(svgElement);\n            }).catch(e => {\n              console.error(\"[angularx-qrcode] svg error:\", e);\n            });\n            break;\n          case \"url\":\n          case \"img\":\n          default:\n            const imgElement = _this2.renderer.createElement(\"img\");\n            _this2.toDataURL(config).then(dataUrl => {\n              if (_this2.alt) {\n                imgElement.setAttribute(\"alt\", _this2.alt);\n              }\n              if (_this2.ariaLabel) {\n                imgElement.setAttribute(\"aria-label\", _this2.ariaLabel);\n              }\n              imgElement.setAttribute(\"src\", dataUrl);\n              if (_this2.title) {\n                imgElement.setAttribute(\"title\", _this2.title);\n              }\n              _this2.renderElement(imgElement);\n              _this2.emitQRCodeURL(imgElement);\n            }).catch(e => {\n              console.error(\"[angularx-qrcode] img/url error:\", e);\n            });\n        }\n      } catch (e) {\n        console.error(\"[angularx-qrcode] Error generating QR Code:\", e.message);\n      }\n    })();\n  }\n  emitQRCodeURL(element) {\n    const className = element.constructor.name;\n    if (className === SVGSVGElement.name) {\n      const svgHTML = element.outerHTML;\n      const blob = new Blob([svgHTML], {\n        type: \"image/svg+xml\"\n      });\n      const urlSvg = URL.createObjectURL(blob);\n      const urlSanitized = this.sanitizer.bypassSecurityTrustUrl(urlSvg);\n      this.qrCodeURL.emit(urlSanitized);\n      return;\n    }\n    let urlImage = \"\";\n    if (className === HTMLCanvasElement.name) {\n      urlImage = element.toDataURL(\"image/png\");\n    }\n    if (className === HTMLImageElement.name) {\n      urlImage = element.src;\n    }\n    fetch(urlImage).then(urlResponse => urlResponse.blob()).then(blob => URL.createObjectURL(blob)).then(url => this.sanitizer.bypassSecurityTrustUrl(url)).then(urlSanitized => {\n      this.qrCodeURL.emit(urlSanitized);\n    }).catch(error => {\n      console.error(\"[angularx-qrcode] Error when fetching image/png URL: \" + error);\n    });\n  }\n}\nQRCodeComponent.ɵfac = function QRCodeComponent_Factory(t) {\n  return new (t || QRCodeComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.DomSanitizer));\n};\nQRCodeComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: QRCodeComponent,\n  selectors: [[\"qrcode\"]],\n  viewQuery: function QRCodeComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.qrcElement = _t.first);\n    }\n  },\n  inputs: {\n    allowEmptyString: \"allowEmptyString\",\n    colorDark: \"colorDark\",\n    colorLight: \"colorLight\",\n    cssClass: \"cssClass\",\n    elementType: \"elementType\",\n    errorCorrectionLevel: \"errorCorrectionLevel\",\n    imageSrc: \"imageSrc\",\n    imageHeight: \"imageHeight\",\n    imageWidth: \"imageWidth\",\n    margin: \"margin\",\n    qrdata: \"qrdata\",\n    scale: \"scale\",\n    version: \"version\",\n    width: \"width\",\n    alt: \"alt\",\n    ariaLabel: \"ariaLabel\",\n    title: \"title\"\n  },\n  outputs: {\n    qrCodeURL: \"qrCodeURL\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 2,\n  vars: 2,\n  consts: [[\"qrcElement\", \"\"]],\n  template: function QRCodeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", null, 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.cssClass);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QRCodeComponent, [{\n    type: Component,\n    args: [{\n      selector: \"qrcode\",\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `<div #qrcElement [class]=\"cssClass\"></div>`\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i1.DomSanitizer\n    }];\n  }, {\n    allowEmptyString: [{\n      type: Input\n    }],\n    colorDark: [{\n      type: Input\n    }],\n    colorLight: [{\n      type: Input\n    }],\n    cssClass: [{\n      type: Input\n    }],\n    elementType: [{\n      type: Input\n    }],\n    errorCorrectionLevel: [{\n      type: Input\n    }],\n    imageSrc: [{\n      type: Input\n    }],\n    imageHeight: [{\n      type: Input\n    }],\n    imageWidth: [{\n      type: Input\n    }],\n    margin: [{\n      type: Input\n    }],\n    qrdata: [{\n      type: Input\n    }],\n    scale: [{\n      type: Input\n    }],\n    version: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    alt: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    qrCodeURL: [{\n      type: Output\n    }],\n    qrcElement: [{\n      type: ViewChild,\n      args: [\"qrcElement\", {\n        static: true\n      }]\n    }]\n  });\n})();\nclass QRCodeModule {}\nQRCodeModule.ɵfac = function QRCodeModule_Factory(t) {\n  return new (t || QRCodeModule)();\n};\nQRCodeModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: QRCodeModule\n});\nQRCodeModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: []\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(QRCodeModule, [{\n    type: NgModule,\n    args: [{\n      providers: [],\n      declarations: [QRCodeComponent],\n      exports: [QRCodeComponent]\n    }]\n  }], null, null);\n})();\nexport { QRCodeComponent, QRCodeModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "Input", "Output", "ViewChild", "NgModule", "QRCode", "i1", "_c0", "QRCodeComponent", "constructor", "renderer", "sanitizer", "allowEmptyString", "colorDark", "colorLight", "cssClass", "elementType", "errorCorrectionLevel", "margin", "qrdata", "scale", "width", "qrCodeURL", "context", "ngOnChanges", "_this", "_asyncToGenerator", "createQRCode", "isValidQrCodeText", "data", "toDataURL", "qrCodeConfig", "Promise", "resolve", "reject", "err", "url", "to<PERSON><PERSON><PERSON>", "canvas", "error", "toSVG", "toString", "renderElement", "element", "node", "qrcElement", "nativeElement", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "_this2", "version", "console", "warn", "undefined", "isNaN", "Error", "config", "color", "dark", "light", "type", "centerImageSrc", "imageSrc", "centerImageHeight", "imageHeight", "centerImageWidth", "imageWidth", "canvasElement", "createElement", "getContext", "then", "aria<PERSON><PERSON><PERSON>", "setAttribute", "title", "centerImage", "Image", "src", "height", "onload", "drawImage", "emitQRCodeURL", "catch", "e", "svgParentElement", "svgString", "setProperty", "svgElement", "<PERSON><PERSON><PERSON><PERSON>", "imgElement", "dataUrl", "alt", "message", "className", "name", "SVGSVGElement", "svgHTML", "outerHTML", "blob", "Blob", "urlSvg", "URL", "createObjectURL", "urlSanitized", "bypassSecurityTrustUrl", "emit", "urlImage", "HTMLCanvasElement", "HTMLImageElement", "fetch", "urlResponse", "ɵfac", "QRCodeComponent_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "Dom<PERSON><PERSON><PERSON>zer", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "QRCodeComponent_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "QRCodeComponent_Template", "ɵɵelement", "ɵɵclassMap", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "static", "QRCodeModule", "QRCodeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/angularx-qrcode/fesm2020/angularx-qrcode.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport * as QRCode from '@cordobo/qrcode';\nimport * as i1 from '@angular/platform-browser';\n\nclass QRCodeComponent {\n    constructor(renderer, sanitizer) {\n        this.renderer = renderer;\n        this.sanitizer = sanitizer;\n        this.allowEmptyString = false;\n        this.colorDark = \"#000000ff\";\n        this.colorLight = \"#ffffffff\";\n        this.cssClass = \"qrcode\";\n        this.elementType = \"canvas\";\n        this.errorCorrectionLevel = \"M\";\n        this.margin = 4;\n        this.qrdata = \"\";\n        this.scale = 4;\n        this.width = 10;\n        this.qrCodeURL = new EventEmitter();\n        this.context = null;\n    }\n    async ngOnChanges() {\n        await this.createQRCode();\n    }\n    isValidQrCodeText(data) {\n        if (this.allowEmptyString === false) {\n            return !(typeof data === \"undefined\" ||\n                data === \"\" ||\n                data === \"null\" ||\n                data === null);\n        }\n        return !(typeof data === \"undefined\");\n    }\n    toDataURL(qrCodeConfig) {\n        return new Promise((resolve, reject) => {\n            QRCode.toDataURL(this.qrdata, qrCodeConfig, (err, url) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(url);\n                }\n            });\n        });\n    }\n    toCanvas(canvas, qrCodeConfig) {\n        return new Promise((resolve, reject) => {\n            QRCode.toCanvas(canvas, this.qrdata, qrCodeConfig, (error) => {\n                if (error) {\n                    reject(error);\n                }\n                else {\n                    resolve(\"success\");\n                }\n            });\n        });\n    }\n    toSVG(qrCodeConfig) {\n        return new Promise((resolve, reject) => {\n            QRCode.toString(this.qrdata, qrCodeConfig, (err, url) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(url);\n                }\n            });\n        });\n    }\n    renderElement(element) {\n        for (const node of this.qrcElement.nativeElement.childNodes) {\n            this.renderer.removeChild(this.qrcElement.nativeElement, node);\n        }\n        this.renderer.appendChild(this.qrcElement.nativeElement, element);\n    }\n    async createQRCode() {\n        if (this.version && this.version > 40) {\n            console.warn(\"[angularx-qrcode] max value for `version` is 40\");\n            this.version = 40;\n        }\n        else if (this.version && this.version < 1) {\n            console.warn(\"[angularx-qrcode]`min value for `version` is 1\");\n            this.version = 1;\n        }\n        else if (this.version !== undefined && isNaN(this.version)) {\n            console.warn(\"[angularx-qrcode] version should be a number, defaulting to auto.\");\n            this.version = undefined;\n        }\n        try {\n            if (!this.isValidQrCodeText(this.qrdata)) {\n                throw new Error(\"[angularx-qrcode] Field `qrdata` is empty, set 'allowEmptyString=\\\"true\\\"' to overwrite this behaviour.\");\n            }\n            if (this.isValidQrCodeText(this.qrdata) && this.qrdata === \"\") {\n                this.qrdata = \" \";\n            }\n            const config = {\n                color: {\n                    dark: this.colorDark,\n                    light: this.colorLight,\n                },\n                errorCorrectionLevel: this.errorCorrectionLevel,\n                margin: this.margin,\n                scale: this.scale,\n                type: this.elementType,\n                version: this.version,\n                width: this.width,\n            };\n            const centerImageSrc = this.imageSrc;\n            const centerImageHeight = this.imageHeight || 40;\n            const centerImageWidth = this.imageWidth || 40;\n            switch (this.elementType) {\n                case \"canvas\":\n                    const canvasElement = this.renderer.createElement(\"canvas\");\n                    this.context = canvasElement.getContext(\"2d\");\n                    this.toCanvas(canvasElement, config)\n                        .then(() => {\n                        if (this.ariaLabel) {\n                            this.renderer.setAttribute(canvasElement, \"aria-label\", `${this.ariaLabel}`);\n                        }\n                        if (this.title) {\n                            this.renderer.setAttribute(canvasElement, \"title\", `${this.title}`);\n                        }\n                        if (centerImageSrc && this.context) {\n                            this.centerImage = new Image(centerImageWidth, centerImageHeight);\n                            if (centerImageSrc !== this.centerImage.src) {\n                                this.centerImage.src = centerImageSrc;\n                            }\n                            if (centerImageHeight !== this.centerImage.height) {\n                                this.centerImage.height = centerImageHeight;\n                            }\n                            if (centerImageWidth !== this.centerImage.width) {\n                                this.centerImage.width = centerImageWidth;\n                            }\n                            const centerImage = this.centerImage;\n                            if (centerImage) {\n                                centerImage.onload = () => {\n                                    this.context?.drawImage(centerImage, canvasElement.width / 2 - centerImageWidth / 2, canvasElement.height / 2 - centerImageHeight / 2, centerImageWidth, centerImageHeight);\n                                };\n                            }\n                        }\n                        this.renderElement(canvasElement);\n                        this.emitQRCodeURL(canvasElement);\n                    })\n                        .catch((e) => {\n                        console.error(\"[angularx-qrcode] canvas error:\", e);\n                    });\n                    break;\n                case \"svg\":\n                    const svgParentElement = this.renderer.createElement(\"div\");\n                    this.toSVG(config)\n                        .then((svgString) => {\n                        this.renderer.setProperty(svgParentElement, \"innerHTML\", svgString);\n                        const svgElement = svgParentElement.firstChild;\n                        this.renderer.setAttribute(svgElement, \"height\", `${this.width}`);\n                        this.renderer.setAttribute(svgElement, \"width\", `${this.width}`);\n                        this.renderElement(svgElement);\n                        this.emitQRCodeURL(svgElement);\n                    })\n                        .catch((e) => {\n                        console.error(\"[angularx-qrcode] svg error:\", e);\n                    });\n                    break;\n                case \"url\":\n                case \"img\":\n                default:\n                    const imgElement = this.renderer.createElement(\"img\");\n                    this.toDataURL(config)\n                        .then((dataUrl) => {\n                        if (this.alt) {\n                            imgElement.setAttribute(\"alt\", this.alt);\n                        }\n                        if (this.ariaLabel) {\n                            imgElement.setAttribute(\"aria-label\", this.ariaLabel);\n                        }\n                        imgElement.setAttribute(\"src\", dataUrl);\n                        if (this.title) {\n                            imgElement.setAttribute(\"title\", this.title);\n                        }\n                        this.renderElement(imgElement);\n                        this.emitQRCodeURL(imgElement);\n                    })\n                        .catch((e) => {\n                        console.error(\"[angularx-qrcode] img/url error:\", e);\n                    });\n            }\n        }\n        catch (e) {\n            console.error(\"[angularx-qrcode] Error generating QR Code:\", e.message);\n        }\n    }\n    emitQRCodeURL(element) {\n        const className = element.constructor.name;\n        if (className === SVGSVGElement.name) {\n            const svgHTML = element.outerHTML;\n            const blob = new Blob([svgHTML], { type: \"image/svg+xml\" });\n            const urlSvg = URL.createObjectURL(blob);\n            const urlSanitized = this.sanitizer.bypassSecurityTrustUrl(urlSvg);\n            this.qrCodeURL.emit(urlSanitized);\n            return;\n        }\n        let urlImage = \"\";\n        if (className === HTMLCanvasElement.name) {\n            urlImage = element.toDataURL(\"image/png\");\n        }\n        if (className === HTMLImageElement.name) {\n            urlImage = element.src;\n        }\n        fetch(urlImage)\n            .then((urlResponse) => urlResponse.blob())\n            .then((blob) => URL.createObjectURL(blob))\n            .then((url) => this.sanitizer.bypassSecurityTrustUrl(url))\n            .then((urlSanitized) => {\n            this.qrCodeURL.emit(urlSanitized);\n        })\n            .catch((error) => {\n            console.error(\"[angularx-qrcode] Error when fetching image/png URL: \" + error);\n        });\n    }\n}\nQRCodeComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: QRCodeComponent, deps: [{ token: i0.Renderer2 }, { token: i1.DomSanitizer }], target: i0.ɵɵFactoryTarget.Component });\nQRCodeComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.1\", type: QRCodeComponent, selector: \"qrcode\", inputs: { allowEmptyString: \"allowEmptyString\", colorDark: \"colorDark\", colorLight: \"colorLight\", cssClass: \"cssClass\", elementType: \"elementType\", errorCorrectionLevel: \"errorCorrectionLevel\", imageSrc: \"imageSrc\", imageHeight: \"imageHeight\", imageWidth: \"imageWidth\", margin: \"margin\", qrdata: \"qrdata\", scale: \"scale\", version: \"version\", width: \"width\", alt: \"alt\", ariaLabel: \"ariaLabel\", title: \"title\" }, outputs: { qrCodeURL: \"qrCodeURL\" }, viewQueries: [{ propertyName: \"qrcElement\", first: true, predicate: [\"qrcElement\"], descendants: true, static: true }], usesOnChanges: true, ngImport: i0, template: `<div #qrcElement [class]=\"cssClass\"></div>`, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: QRCodeComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: \"qrcode\",\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `<div #qrcElement [class]=\"cssClass\"></div>`,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i1.DomSanitizer }]; }, propDecorators: { allowEmptyString: [{\n                type: Input\n            }], colorDark: [{\n                type: Input\n            }], colorLight: [{\n                type: Input\n            }], cssClass: [{\n                type: Input\n            }], elementType: [{\n                type: Input\n            }], errorCorrectionLevel: [{\n                type: Input\n            }], imageSrc: [{\n                type: Input\n            }], imageHeight: [{\n                type: Input\n            }], imageWidth: [{\n                type: Input\n            }], margin: [{\n                type: Input\n            }], qrdata: [{\n                type: Input\n            }], scale: [{\n                type: Input\n            }], version: [{\n                type: Input\n            }], width: [{\n                type: Input\n            }], alt: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], title: [{\n                type: Input\n            }], qrCodeURL: [{\n                type: Output\n            }], qrcElement: [{\n                type: ViewChild,\n                args: [\"qrcElement\", { static: true }]\n            }] } });\n\nclass QRCodeModule {\n}\nQRCodeModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: QRCodeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nQRCodeModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.1\", ngImport: i0, type: QRCodeModule, declarations: [QRCodeComponent], exports: [QRCodeComponent] });\nQRCodeModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: QRCodeModule, providers: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: QRCodeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [],\n                    declarations: [QRCodeComponent],\n                    exports: [QRCodeComponent],\n                }]\n        }] });\n\nexport { QRCodeComponent, QRCodeModule };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACpH,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAAC,MAAAC,GAAA;AAEhD,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAE;IAC7B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,SAAS,GAAG,WAAW;IAC5B,IAAI,CAACC,UAAU,GAAG,WAAW;IAC7B,IAAI,CAACC,QAAQ,GAAG,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACC,oBAAoB,GAAG,GAAG;IAC/B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,IAAIxB,YAAY,CAAC,CAAC;IACnC,IAAI,CAACyB,OAAO,GAAG,IAAI;EACvB;EACMC,WAAWA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,MAAMD,KAAI,CAACE,YAAY,CAAC,CAAC;IAAC;EAC9B;EACAC,iBAAiBA,CAACC,IAAI,EAAE;IACpB,IAAI,IAAI,CAACjB,gBAAgB,KAAK,KAAK,EAAE;MACjC,OAAO,EAAE,OAAOiB,IAAI,KAAK,WAAW,IAChCA,IAAI,KAAK,EAAE,IACXA,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,IAAI,CAAC;IACtB;IACA,OAAO,EAAE,OAAOA,IAAI,KAAK,WAAW,CAAC;EACzC;EACAC,SAASA,CAACC,YAAY,EAAE;IACpB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC7B,MAAM,CAACyB,SAAS,CAAC,IAAI,CAACX,MAAM,EAAEY,YAAY,EAAE,CAACI,GAAG,EAAEC,GAAG,KAAK;QACtD,IAAID,GAAG,EAAE;UACLD,MAAM,CAACC,GAAG,CAAC;QACf,CAAC,MACI;UACDF,OAAO,CAACG,GAAG,CAAC;QAChB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,QAAQA,CAACC,MAAM,EAAEP,YAAY,EAAE;IAC3B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC7B,MAAM,CAACgC,QAAQ,CAACC,MAAM,EAAE,IAAI,CAACnB,MAAM,EAAEY,YAAY,EAAGQ,KAAK,IAAK;QAC1D,IAAIA,KAAK,EAAE;UACPL,MAAM,CAACK,KAAK,CAAC;QACjB,CAAC,MACI;UACDN,OAAO,CAAC,SAAS,CAAC;QACtB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAO,KAAKA,CAACT,YAAY,EAAE;IAChB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC7B,MAAM,CAACoC,QAAQ,CAAC,IAAI,CAACtB,MAAM,EAAEY,YAAY,EAAE,CAACI,GAAG,EAAEC,GAAG,KAAK;QACrD,IAAID,GAAG,EAAE;UACLD,MAAM,CAACC,GAAG,CAAC;QACf,CAAC,MACI;UACDF,OAAO,CAACG,GAAG,CAAC;QAChB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAM,aAAaA,CAACC,OAAO,EAAE;IACnB,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACC,UAAU,CAACC,aAAa,CAACC,UAAU,EAAE;MACzD,IAAI,CAACrC,QAAQ,CAACsC,WAAW,CAAC,IAAI,CAACH,UAAU,CAACC,aAAa,EAAEF,IAAI,CAAC;IAClE;IACA,IAAI,CAAClC,QAAQ,CAACuC,WAAW,CAAC,IAAI,CAACJ,UAAU,CAACC,aAAa,EAAEH,OAAO,CAAC;EACrE;EACMhB,YAAYA,CAAA,EAAG;IAAA,IAAAuB,MAAA;IAAA,OAAAxB,iBAAA;MACjB,IAAIwB,MAAI,CAACC,OAAO,IAAID,MAAI,CAACC,OAAO,GAAG,EAAE,EAAE;QACnCC,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAC;QAC/DH,MAAI,CAACC,OAAO,GAAG,EAAE;MACrB,CAAC,MACI,IAAID,MAAI,CAACC,OAAO,IAAID,MAAI,CAACC,OAAO,GAAG,CAAC,EAAE;QACvCC,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;QAC9DH,MAAI,CAACC,OAAO,GAAG,CAAC;MACpB,CAAC,MACI,IAAID,MAAI,CAACC,OAAO,KAAKG,SAAS,IAAIC,KAAK,CAACL,MAAI,CAACC,OAAO,CAAC,EAAE;QACxDC,OAAO,CAACC,IAAI,CAAC,mEAAmE,CAAC;QACjFH,MAAI,CAACC,OAAO,GAAGG,SAAS;MAC5B;MACA,IAAI;QACA,IAAI,CAACJ,MAAI,CAACtB,iBAAiB,CAACsB,MAAI,CAAC/B,MAAM,CAAC,EAAE;UACtC,MAAM,IAAIqC,KAAK,CAAC,yGAAyG,CAAC;QAC9H;QACA,IAAIN,MAAI,CAACtB,iBAAiB,CAACsB,MAAI,CAAC/B,MAAM,CAAC,IAAI+B,MAAI,CAAC/B,MAAM,KAAK,EAAE,EAAE;UAC3D+B,MAAI,CAAC/B,MAAM,GAAG,GAAG;QACrB;QACA,MAAMsC,MAAM,GAAG;UACXC,KAAK,EAAE;YACHC,IAAI,EAAET,MAAI,CAACrC,SAAS;YACpB+C,KAAK,EAAEV,MAAI,CAACpC;UAChB,CAAC;UACDG,oBAAoB,EAAEiC,MAAI,CAACjC,oBAAoB;UAC/CC,MAAM,EAAEgC,MAAI,CAAChC,MAAM;UACnBE,KAAK,EAAE8B,MAAI,CAAC9B,KAAK;UACjByC,IAAI,EAAEX,MAAI,CAAClC,WAAW;UACtBmC,OAAO,EAAED,MAAI,CAACC,OAAO;UACrB9B,KAAK,EAAE6B,MAAI,CAAC7B;QAChB,CAAC;QACD,MAAMyC,cAAc,GAAGZ,MAAI,CAACa,QAAQ;QACpC,MAAMC,iBAAiB,GAAGd,MAAI,CAACe,WAAW,IAAI,EAAE;QAChD,MAAMC,gBAAgB,GAAGhB,MAAI,CAACiB,UAAU,IAAI,EAAE;QAC9C,QAAQjB,MAAI,CAAClC,WAAW;UACpB,KAAK,QAAQ;YACT,MAAMoD,aAAa,GAAGlB,MAAI,CAACxC,QAAQ,CAAC2D,aAAa,CAAC,QAAQ,CAAC;YAC3DnB,MAAI,CAAC3B,OAAO,GAAG6C,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;YAC7CpB,MAAI,CAACb,QAAQ,CAAC+B,aAAa,EAAEX,MAAM,CAAC,CAC/Bc,IAAI,CAAC,MAAM;cACZ,IAAIrB,MAAI,CAACsB,SAAS,EAAE;gBAChBtB,MAAI,CAACxC,QAAQ,CAAC+D,YAAY,CAACL,aAAa,EAAE,YAAY,EAAG,GAAElB,MAAI,CAACsB,SAAU,EAAC,CAAC;cAChF;cACA,IAAItB,MAAI,CAACwB,KAAK,EAAE;gBACZxB,MAAI,CAACxC,QAAQ,CAAC+D,YAAY,CAACL,aAAa,EAAE,OAAO,EAAG,GAAElB,MAAI,CAACwB,KAAM,EAAC,CAAC;cACvE;cACA,IAAIZ,cAAc,IAAIZ,MAAI,CAAC3B,OAAO,EAAE;gBAChC2B,MAAI,CAACyB,WAAW,GAAG,IAAIC,KAAK,CAACV,gBAAgB,EAAEF,iBAAiB,CAAC;gBACjE,IAAIF,cAAc,KAAKZ,MAAI,CAACyB,WAAW,CAACE,GAAG,EAAE;kBACzC3B,MAAI,CAACyB,WAAW,CAACE,GAAG,GAAGf,cAAc;gBACzC;gBACA,IAAIE,iBAAiB,KAAKd,MAAI,CAACyB,WAAW,CAACG,MAAM,EAAE;kBAC/C5B,MAAI,CAACyB,WAAW,CAACG,MAAM,GAAGd,iBAAiB;gBAC/C;gBACA,IAAIE,gBAAgB,KAAKhB,MAAI,CAACyB,WAAW,CAACtD,KAAK,EAAE;kBAC7C6B,MAAI,CAACyB,WAAW,CAACtD,KAAK,GAAG6C,gBAAgB;gBAC7C;gBACA,MAAMS,WAAW,GAAGzB,MAAI,CAACyB,WAAW;gBACpC,IAAIA,WAAW,EAAE;kBACbA,WAAW,CAACI,MAAM,GAAG,MAAM;oBACvB7B,MAAI,CAAC3B,OAAO,EAAEyD,SAAS,CAACL,WAAW,EAAEP,aAAa,CAAC/C,KAAK,GAAG,CAAC,GAAG6C,gBAAgB,GAAG,CAAC,EAAEE,aAAa,CAACU,MAAM,GAAG,CAAC,GAAGd,iBAAiB,GAAG,CAAC,EAAEE,gBAAgB,EAAEF,iBAAiB,CAAC;kBAC/K,CAAC;gBACL;cACJ;cACAd,MAAI,CAACR,aAAa,CAAC0B,aAAa,CAAC;cACjClB,MAAI,CAAC+B,aAAa,CAACb,aAAa,CAAC;YACrC,CAAC,CAAC,CACGc,KAAK,CAAEC,CAAC,IAAK;cACd/B,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAE4C,CAAC,CAAC;YACvD,CAAC,CAAC;YACF;UACJ,KAAK,KAAK;YACN,MAAMC,gBAAgB,GAAGlC,MAAI,CAACxC,QAAQ,CAAC2D,aAAa,CAAC,KAAK,CAAC;YAC3DnB,MAAI,CAACV,KAAK,CAACiB,MAAM,CAAC,CACbc,IAAI,CAAEc,SAAS,IAAK;cACrBnC,MAAI,CAACxC,QAAQ,CAAC4E,WAAW,CAACF,gBAAgB,EAAE,WAAW,EAAEC,SAAS,CAAC;cACnE,MAAME,UAAU,GAAGH,gBAAgB,CAACI,UAAU;cAC9CtC,MAAI,CAACxC,QAAQ,CAAC+D,YAAY,CAACc,UAAU,EAAE,QAAQ,EAAG,GAAErC,MAAI,CAAC7B,KAAM,EAAC,CAAC;cACjE6B,MAAI,CAACxC,QAAQ,CAAC+D,YAAY,CAACc,UAAU,EAAE,OAAO,EAAG,GAAErC,MAAI,CAAC7B,KAAM,EAAC,CAAC;cAChE6B,MAAI,CAACR,aAAa,CAAC6C,UAAU,CAAC;cAC9BrC,MAAI,CAAC+B,aAAa,CAACM,UAAU,CAAC;YAClC,CAAC,CAAC,CACGL,KAAK,CAAEC,CAAC,IAAK;cACd/B,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAE4C,CAAC,CAAC;YACpD,CAAC,CAAC;YACF;UACJ,KAAK,KAAK;UACV,KAAK,KAAK;UACV;YACI,MAAMM,UAAU,GAAGvC,MAAI,CAACxC,QAAQ,CAAC2D,aAAa,CAAC,KAAK,CAAC;YACrDnB,MAAI,CAACpB,SAAS,CAAC2B,MAAM,CAAC,CACjBc,IAAI,CAAEmB,OAAO,IAAK;cACnB,IAAIxC,MAAI,CAACyC,GAAG,EAAE;gBACVF,UAAU,CAAChB,YAAY,CAAC,KAAK,EAAEvB,MAAI,CAACyC,GAAG,CAAC;cAC5C;cACA,IAAIzC,MAAI,CAACsB,SAAS,EAAE;gBAChBiB,UAAU,CAAChB,YAAY,CAAC,YAAY,EAAEvB,MAAI,CAACsB,SAAS,CAAC;cACzD;cACAiB,UAAU,CAAChB,YAAY,CAAC,KAAK,EAAEiB,OAAO,CAAC;cACvC,IAAIxC,MAAI,CAACwB,KAAK,EAAE;gBACZe,UAAU,CAAChB,YAAY,CAAC,OAAO,EAAEvB,MAAI,CAACwB,KAAK,CAAC;cAChD;cACAxB,MAAI,CAACR,aAAa,CAAC+C,UAAU,CAAC;cAC9BvC,MAAI,CAAC+B,aAAa,CAACQ,UAAU,CAAC;YAClC,CAAC,CAAC,CACGP,KAAK,CAAEC,CAAC,IAAK;cACd/B,OAAO,CAACb,KAAK,CAAC,kCAAkC,EAAE4C,CAAC,CAAC;YACxD,CAAC,CAAC;QACV;MACJ,CAAC,CACD,OAAOA,CAAC,EAAE;QACN/B,OAAO,CAACb,KAAK,CAAC,6CAA6C,EAAE4C,CAAC,CAACS,OAAO,CAAC;MAC3E;IAAC;EACL;EACAX,aAAaA,CAACtC,OAAO,EAAE;IACnB,MAAMkD,SAAS,GAAGlD,OAAO,CAAClC,WAAW,CAACqF,IAAI;IAC1C,IAAID,SAAS,KAAKE,aAAa,CAACD,IAAI,EAAE;MAClC,MAAME,OAAO,GAAGrD,OAAO,CAACsD,SAAS;MACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,OAAO,CAAC,EAAE;QAAEnC,IAAI,EAAE;MAAgB,CAAC,CAAC;MAC3D,MAAMuC,MAAM,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACxC,MAAMK,YAAY,GAAG,IAAI,CAAC5F,SAAS,CAAC6F,sBAAsB,CAACJ,MAAM,CAAC;MAClE,IAAI,CAAC9E,SAAS,CAACmF,IAAI,CAACF,YAAY,CAAC;MACjC;IACJ;IACA,IAAIG,QAAQ,GAAG,EAAE;IACjB,IAAIb,SAAS,KAAKc,iBAAiB,CAACb,IAAI,EAAE;MACtCY,QAAQ,GAAG/D,OAAO,CAACb,SAAS,CAAC,WAAW,CAAC;IAC7C;IACA,IAAI+D,SAAS,KAAKe,gBAAgB,CAACd,IAAI,EAAE;MACrCY,QAAQ,GAAG/D,OAAO,CAACkC,GAAG;IAC1B;IACAgC,KAAK,CAACH,QAAQ,CAAC,CACVnC,IAAI,CAAEuC,WAAW,IAAKA,WAAW,CAACZ,IAAI,CAAC,CAAC,CAAC,CACzC3B,IAAI,CAAE2B,IAAI,IAAKG,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CAAC,CACzC3B,IAAI,CAAEnC,GAAG,IAAK,IAAI,CAACzB,SAAS,CAAC6F,sBAAsB,CAACpE,GAAG,CAAC,CAAC,CACzDmC,IAAI,CAAEgC,YAAY,IAAK;MACxB,IAAI,CAACjF,SAAS,CAACmF,IAAI,CAACF,YAAY,CAAC;IACrC,CAAC,CAAC,CACGrB,KAAK,CAAE3C,KAAK,IAAK;MAClBa,OAAO,CAACb,KAAK,CAAC,uDAAuD,GAAGA,KAAK,CAAC;IAClF,CAAC,CAAC;EACN;AACJ;AACA/B,eAAe,CAACuG,IAAI,YAAAC,wBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFzG,eAAe,EAAzBX,EAAE,CAAAqH,iBAAA,CAAyCrH,EAAE,CAACsH,SAAS,GAAvDtH,EAAE,CAAAqH,iBAAA,CAAkE5G,EAAE,CAAC8G,YAAY;AAAA,CAA4C;AACjO5G,eAAe,CAAC6G,IAAI,kBAD8ExH,EAAE,CAAAyH,iBAAA;EAAAzD,IAAA,EACJrD,eAAe;EAAA+G,SAAA;EAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADb7H,EAAE,CAAA+H,WAAA,CAAArH,GAAA;IAAA;IAAA,IAAAmH,EAAA;MAAA,IAAAG,EAAA;MAAFhI,EAAE,CAAAiI,cAAA,CAAAD,EAAA,GAAFhI,EAAE,CAAAkI,WAAA,QAAAJ,GAAA,CAAA9E,UAAA,GAAAgF,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAArH,gBAAA;IAAAC,SAAA;IAAAC,UAAA;IAAAC,QAAA;IAAAC,WAAA;IAAAC,oBAAA;IAAA8C,QAAA;IAAAE,WAAA;IAAAE,UAAA;IAAAjD,MAAA;IAAAC,MAAA;IAAAC,KAAA;IAAA+B,OAAA;IAAA9B,KAAA;IAAAsE,GAAA;IAAAnB,SAAA;IAAAE,KAAA;EAAA;EAAAwD,OAAA;IAAA5G,SAAA;EAAA;EAAA6G,QAAA,GAAFtI,EAAE,CAAAuI,oBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAf,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF7H,EAAE,CAAA6I,SAAA,kBACirB,CAAC;IAAA;IAAA,IAAAhB,EAAA;MADprB7H,EAAE,CAAA8I,UAAA,CAAAhB,GAAA,CAAA5G,QAC0qB,CAAC;IAAA;EAAA;EAAA6H,aAAA;EAAAC,eAAA;AAAA,EAA+E;AAC91B;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFkGjJ,EAAE,CAAAkJ,iBAAA,CAETvI,eAAe,EAAc,CAAC;IAC7GqD,IAAI,EAAE9D,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,QAAQ;MAClBJ,eAAe,EAAE7I,uBAAuB,CAACkJ,MAAM;MAC/CV,QAAQ,EAAG;IACf,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3E,IAAI,EAAEhE,EAAE,CAACsH;IAAU,CAAC,EAAE;MAAEtD,IAAI,EAAEvD,EAAE,CAAC8G;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAExG,gBAAgB,EAAE,CAAC;MAC9HiD,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEY,SAAS,EAAE,CAAC;MACZgD,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEa,UAAU,EAAE,CAAC;MACb+C,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEc,QAAQ,EAAE,CAAC;MACX8C,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEe,WAAW,EAAE,CAAC;MACd6C,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEgB,oBAAoB,EAAE,CAAC;MACvB4C,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAE8D,QAAQ,EAAE,CAAC;MACXF,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEgE,WAAW,EAAE,CAAC;MACdJ,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEkE,UAAU,EAAE,CAAC;MACbN,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEiB,MAAM,EAAE,CAAC;MACT2C,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEkB,MAAM,EAAE,CAAC;MACT0C,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEmB,KAAK,EAAE,CAAC;MACRyC,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEkD,OAAO,EAAE,CAAC;MACVU,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEoB,KAAK,EAAE,CAAC;MACRwC,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAE0F,GAAG,EAAE,CAAC;MACN9B,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEuE,SAAS,EAAE,CAAC;MACZX,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEyE,KAAK,EAAE,CAAC;MACRb,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAEqB,SAAS,EAAE,CAAC;MACZuC,IAAI,EAAE3D;IACV,CAAC,CAAC;IAAE2C,UAAU,EAAE,CAAC;MACbgB,IAAI,EAAE1D,SAAS;MACf6I,IAAI,EAAE,CAAC,YAAY,EAAE;QAAEG,MAAM,EAAE;MAAK,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,YAAY,CAAC;AAEnBA,YAAY,CAACrC,IAAI,YAAAsC,qBAAApC,CAAA;EAAA,YAAAA,CAAA,IAAwFmC,YAAY;AAAA,CAAkD;AACvKA,YAAY,CAACE,IAAI,kBArDiFzJ,EAAE,CAAA0J,gBAAA;EAAA1F,IAAA,EAqDMuF;AAAY,EAAgE;AACtLA,YAAY,CAACI,IAAI,kBAtDiF3J,EAAE,CAAA4J,gBAAA;EAAAC,SAAA,EAsD+B;AAAE,EAAG;AACxI;EAAA,QAAAZ,SAAA,oBAAAA,SAAA,KAvDkGjJ,EAAE,CAAAkJ,iBAAA,CAuDTK,YAAY,EAAc,CAAC;IAC1GvF,IAAI,EAAEzD,QAAQ;IACd4I,IAAI,EAAE,CAAC;MACCU,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,CAACnJ,eAAe,CAAC;MAC/BoJ,OAAO,EAAE,CAACpJ,eAAe;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASA,eAAe,EAAE4I,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { SelectClubModuleModule } from './../../components/select-club-module/select-club-module.module';\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\nimport { ErrorMessageModule } from './../../layout/components/error-message/error-message.module';\nimport { ContentHeaderModule } from './../../layout/components/content-header/content-header.module';\nimport { RouterModule } from '@angular/router';\nimport { SelectEventComponent } from './select-event/select-event.component';\nimport { CommonModule } from '@angular/common';\nimport { SelectPlayerComponent } from './select-player/select-player.component';\nimport { RegisterNewPlayerComponent } from './register-new-player/register-new-player.component';\nimport { FormsModule } from '@angular/forms';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { CoreSidebarModule } from '@core/components';\nimport { SelectPlayerGuard } from 'app/guards/select-player.guard';\nimport { CoreCommonModule } from '@core/common.module';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { CropperWithDialogModule } from 'app/components/cropper-dialog/cropper-with-dialog.module';\nimport { LyCommonModule } from '@alyle/ui';\nimport { Color } from '@alyle/ui/color';\nimport { ModalSuitableGroupComponent } from './register-new-player/modal-suitable-group/modal-suitable-group.component';\nimport { FORMLY_CONFIG, FormlyModule } from '@ngx-formly/core';\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\nimport { NumberTypeComponent } from 'app/components/number-type/number-type.component';\nimport { ImageCropperTypeComponent } from 'app/components/image-cropper-type/image-cropper-type.component';\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { registerTranslateExtension } from 'app/translate.extension';\nimport { StripeModule } from 'stripe-angular';\nimport { environment } from 'environments/environment';\nimport { ShareModule } from 'app/share/share.module';\nimport { ModalSeasonInfoComponent } from './select-event/modal-season-info/modal-season-info.component';\nimport { SeasonDetailComponent } from './season-detail/season-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"stripe-angular\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-formly/core\";\nexport class CustomMinimaLight {\n  constructor() {\n    this.name = 'minima-light';\n    this.demoBg = new Color(0x8c8c8c);\n  }\n  static #_ = this.ɵfac = function CustomMinimaLight_Factory(t) {\n    return new (t || CustomMinimaLight)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomMinimaLight,\n    factory: CustomMinimaLight.ɵfac\n  });\n}\nconst routes = [{\n  path: '',\n  redirectTo: 'select-event',\n  pathMatch: 'full'\n}, {\n  path: 'select-event',\n  component: SelectEventComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.registration\n  }\n}, {\n  path: 'season/:seasonId/select-player',\n  component: SelectPlayerComponent,\n  canActivate: [SelectPlayerGuard]\n}, {\n  path: 'season/:seasonId/register-new-player',\n  component: RegisterNewPlayerComponent\n}, {\n  path: 'season/:seasonId',\n  component: SeasonDetailComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.registration\n  }\n}, {\n  path: 'modal-suitable-group',\n  component: ModalSuitableGroupComponent\n}];\nexport class RegistrationModule {\n  static #_ = this.ɵfac = function RegistrationModule_Factory(t) {\n    return new (t || RegistrationModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: RegistrationModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: FORMLY_CONFIG,\n      multi: true,\n      useFactory: registerTranslateExtension,\n      deps: [TranslateService]\n    }],\n    imports: [StripeModule.forRoot(environment.stripe.publishableKey), EditorSidebarModule, ShareModule, CommonModule, RouterModule.forChild(routes), ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, SelectClubModuleModule, CoreSidebarModule, CoreCommonModule, CropperWithDialogModule, LyCommonModule, FormlyModule.forRoot({\n      types: [{\n        name: 'file',\n        component: FormlyFieldFile,\n        wrappers: ['form-field']\n      }, {\n        name: 'core-touchspin',\n        component: NumberTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'image-cropper',\n        component: ImageCropperTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'ng-select',\n        component: NgSelectTypeComponent,\n        wrappers: ['form-field']\n      }],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    }), FormlyBootstrapModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RegistrationModule, {\n    declarations: [SelectEventComponent, SelectPlayerComponent, ModalSuitableGroupComponent, ModalSeasonInfoComponent, SeasonDetailComponent],\n    imports: [i1.StripeModule, EditorSidebarModule, ShareModule, CommonModule, i2.RouterModule, ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, SelectClubModuleModule, CoreSidebarModule, CoreCommonModule, CropperWithDialogModule, LyCommonModule, i3.FormlyModule, FormlyBootstrapModule],\n    exports: [SelectEventComponent, SelectPlayerComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,sBAAsB,QAAQ,iEAAiE;AACxG,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,qBAAqB;AACvE,SAASC,kBAAkB,QAAQ,8DAA8D;AACjG,SAASC,mBAAmB,QAAQ,gEAAgE;AACpG,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAE5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,cAAc,QAAiC,WAAW;AACnE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,2BAA2B,QAAQ,2EAA2E;AACvH,SAASC,aAAa,EAAEC,YAAY,QAAQ,kBAAkB;AAC9D,SAASC,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,qDAAqD;AAC5D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,0BAA0B,QAAQ,yBAAyB;AAEpE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,wBAAwB,QAAQ,8DAA8D;AACvG,SAASC,qBAAqB,QAAQ,yCAAyC;;;;;AAG/E,OAAM,MAAOC,iBAAiB;EAD9BC,YAAA;IAEE,KAAAC,IAAI,GAAG,cAAc;IACrB,KAAAC,MAAM,GAAG,IAAIpB,KAAK,CAAC,QAAQ,CAAC;;EAC7B,QAAAqB,CAAA;qBAHYJ,iBAAiB;EAAA;EAAA,QAAAK,EAAA;WAAjBL,iBAAiB;IAAAM,OAAA,EAAjBN,iBAAiB,CAAAO;EAAA;;AAK9B,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,cAAc;EAAEC,SAAS,EAAE;AAAM,CAAE,EAC3D;EACEF,IAAI,EAAE,cAAc;EACpBG,SAAS,EAAE1C,oBAAoB;EAC/B2C,WAAW,EAAE,CAAClC,gBAAgB,CAAC;EAC/BmC,IAAI,EAAE;IAAEC,WAAW,EAAEnC,SAAS,CAACoC,WAAW,CAACC;EAAY;CACxD,EACD;EACER,IAAI,EAAE,gCAAgC;EACtCG,SAAS,EAAExC,qBAAqB;EAChCyC,WAAW,EAAE,CAACpC,iBAAiB;CAChC,EACD;EACEgC,IAAI,EAAE,sCAAsC;EAC5CG,SAAS,EAAEvC;CACZ,EACD;EACEoC,IAAI,EAAE,kBAAkB;EACxBG,SAAS,EAAEb,qBAAqB;EAChCc,WAAW,EAAE,CAAClC,gBAAgB,CAAC;EAC/BmC,IAAI,EAAE;IAAEC,WAAW,EAAEnC,SAAS,CAACoC,WAAW,CAACC;EAAY;CACxD,EACD;EACER,IAAI,EAAE,sBAAsB;EAC5BG,SAAS,EAAE5B;CACZ,CACF;AA6DD,OAAM,MAAOkC,kBAAkB;EAAA,QAAAd,CAAA;qBAAlBc,kBAAkB;EAAA;EAAA,QAAAb,EAAA;UAAlBa;EAAkB;EAAA,QAAAC,EAAA;eAVlB,CACT;MACEC,OAAO,EAAEnC,aAAa;MACtBoC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE5B,0BAA0B;MACtC6B,IAAI,EAAE,CAACzD,gBAAgB;KACxB,CACF;IAAA0D,OAAA,GA/CC7B,YAAY,CAAC8B,OAAO,CAAC7B,WAAW,CAAC8B,MAAM,CAACC,cAAc,CAAC,EACvDpC,mBAAmB,EACnBM,WAAW,EACX1B,YAAY,EACZF,YAAY,CAAC2D,QAAQ,CAACpB,MAAM,CAAC,EAC7BxC,mBAAmB,EACnBM,WAAW,EACXC,mBAAmB,EACnBR,kBAAkB,EAClBF,eAAe,EACfD,sBAAsB,EACtBY,iBAAiB,EACjBE,gBAAgB,EAChBG,uBAAuB,EACvBC,cAAc,EACdI,YAAY,CAACuC,OAAO,CAAC;MACnBI,KAAK,EAAE,CACL;QAAE3B,IAAI,EAAE,MAAM;QAAEU,SAAS,EAAEzB,eAAe;QAAE2C,QAAQ,EAAE,CAAC,YAAY;MAAC,CAAE,EACtE;QACE5B,IAAI,EAAE,gBAAgB;QACtBU,SAAS,EAAExB,mBAAmB;QAC9B0C,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACE5B,IAAI,EAAE,eAAe;QACrBU,SAAS,EAAEvB,yBAAyB;QACpCyC,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACE5B,IAAI,EAAE,WAAW;QACjBU,SAAS,EAAEtB,qBAAqB;QAChCwC,QAAQ,EAAE,CAAC,YAAY;OACxB,CACF;MACDC,kBAAkB,EAAE,CAClB;QAAE7B,IAAI,EAAE,aAAa;QAAE8B,OAAO,EAAExC;MAAuB,CAAE;KAE5D,CAAC,EACFC,qBAAqB;EAAA;;;2EAYZyB,kBAAkB;IAAAe,YAAA,GAzD3B/D,oBAAoB,EACpBE,qBAAqB,EACrBY,2BAA2B,EAC3Bc,wBAAwB,EACxBC,qBAAqB;IAAAyB,OAAA,GAAAU,EAAA,CAAAvC,YAAA,EAIrBJ,mBAAmB,EACnBM,WAAW,EACX1B,YAAY,EAAAgE,EAAA,CAAAlE,YAAA,EAEZD,mBAAmB,EACnBM,WAAW,EACXC,mBAAmB,EACnBR,kBAAkB,EAClBF,eAAe,EACfD,sBAAsB,EACtBY,iBAAiB,EACjBE,gBAAgB,EAChBG,uBAAuB,EACvBC,cAAc,EAAAsD,EAAA,CAAAlD,YAAA,EAwBdO,qBAAqB;IAAA4C,OAAA,GAUbnE,oBAAoB,EAAEE,qBAAqB;EAAA;AAAA", "names": ["SelectClubModuleModule", "TranslateModule", "TranslateService", "ErrorMessageModule", "ContentHeaderModule", "RouterModule", "SelectEventComponent", "CommonModule", "SelectPlayerComponent", "RegisterNewPlayerComponent", "FormsModule", "ReactiveFormsModule", "CoreSidebarModule", "SelectPlayerGuard", "CoreCommonModule", "PermissionsGuard", "AppConfig", "CropperWithDialogModule", "LyCommonModule", "Color", "ModalSuitableGroupComponent", "FORMLY_CONFIG", "FormlyModule", "FormlyFieldFile", "NumberTypeComponent", "ImageCropperTypeComponent", "NgSelectTypeComponent", "EditorSidebarModule", "serverValidationMessage", "FormlyBootstrapModule", "registerTranslateExtension", "StripeModule", "environment", "ShareModule", "ModalSeasonInfoComponent", "SeasonDetailComponent", "CustomMinimaLight", "constructor", "name", "demoBg", "_", "_2", "factory", "ɵfac", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "data", "permissions", "PERMISSIONS", "registration", "RegistrationModule", "_3", "provide", "multi", "useFactory", "deps", "imports", "forRoot", "stripe", "publishableKey", "<PERSON><PERSON><PERSON><PERSON>", "types", "wrappers", "validationMessages", "message", "declarations", "i1", "i2", "i3", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\registration\\registration.module.ts"], "sourcesContent": ["import { SelectClubModuleModule } from './../../components/select-club-module/select-club-module.module';\r\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\r\nimport { ErrorMessageModule } from './../../layout/components/error-message/error-message.module';\r\nimport { ContentHeaderModule } from './../../layout/components/content-header/content-header.module';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { SelectEventComponent } from './select-event/select-event.component';\r\nimport { Injectable, NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SelectPlayerComponent } from './select-player/select-player.component';\r\nimport { RegisterNewPlayerComponent } from './register-new-player/register-new-player.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { SelectPlayerGuard } from 'app/guards/select-player.guard';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { CropperWithDialogModule } from 'app/components/cropper-dialog/cropper-with-dialog.module';\r\nimport { LyCommonModule, LyTheme2, StyleRenderer } from '@alyle/ui';\r\nimport { Color } from '@alyle/ui/color';\r\nimport { ModalSuitableGroupComponent } from './register-new-player/modal-suitable-group/modal-suitable-group.component';\r\nimport { FORMLY_CONFIG, FormlyModule } from '@ngx-formly/core';\r\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\r\nimport { NumberTypeComponent } from 'app/components/number-type/number-type.component';\r\nimport { ImageCropperTypeComponent } from 'app/components/image-cropper-type/image-cropper-type.component';\r\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\r\nimport {\r\n  EditorSidebarModule,\r\n  serverValidationMessage,\r\n} from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { registerTranslateExtension } from 'app/translate.extension';\r\nimport { StripeCheckoutModule } from 'app/components/stripe-checkout/stripe-checkout.module';\r\nimport { StripeModule } from 'stripe-angular';\r\nimport { environment } from 'environments/environment';\r\nimport { ShareModule } from 'app/share/share.module';\r\nimport { ModalSeasonInfoComponent } from './select-event/modal-season-info/modal-season-info.component';\r\nimport { SeasonDetailComponent } from './season-detail/season-detail.component';\r\n\r\n@Injectable()\r\nexport class CustomMinimaLight {\r\n  name = 'minima-light';\r\n  demoBg = new Color(0x8c8c8c);\r\n}\r\n\r\nconst routes: Routes = [\r\n  { path: '', redirectTo: 'select-event', pathMatch: 'full' },\r\n  {\r\n    path: 'select-event',\r\n    component: SelectEventComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.registration },\r\n  },\r\n  {\r\n    path: 'season/:seasonId/select-player',\r\n    component: SelectPlayerComponent,\r\n    canActivate: [SelectPlayerGuard],\r\n  },\r\n  {\r\n    path: 'season/:seasonId/register-new-player',\r\n    component: RegisterNewPlayerComponent,\r\n  },\r\n  {\r\n    path: 'season/:seasonId',\r\n    component: SeasonDetailComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.registration },\r\n  },\r\n  {\r\n    path: 'modal-suitable-group',\r\n    component: ModalSuitableGroupComponent,\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SelectEventComponent,\r\n    SelectPlayerComponent,\r\n    ModalSuitableGroupComponent,\r\n    ModalSeasonInfoComponent,\r\n    SeasonDetailComponent,\r\n  ],\r\n  imports: [\r\n    StripeModule.forRoot(environment.stripe.publishableKey),\r\n    EditorSidebarModule,\r\n    ShareModule,\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ErrorMessageModule,\r\n    TranslateModule,\r\n    SelectClubModuleModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    CropperWithDialogModule,\r\n    LyCommonModule,\r\n    FormlyModule.forRoot({\r\n      types: [\r\n        { name: 'file', component: FormlyFieldFile, wrappers: ['form-field'] },\r\n        {\r\n          name: 'core-touchspin',\r\n          component: NumberTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'image-cropper',\r\n          component: ImageCropperTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'ng-select',\r\n          component: NgSelectTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n      ],\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],\r\n    }),\r\n    FormlyBootstrapModule,\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: FORMLY_CONFIG,\r\n      multi: true,\r\n      useFactory: registerTranslateExtension,\r\n      deps: [TranslateService],\r\n    },\r\n  ],\r\n  exports: [SelectEventComponent, SelectPlayerComponent],\r\n})\r\nexport class RegistrationModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { NgbAccordionModule, NgbCollapseModule, NgbDropdownModule, NgbModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { DataTablesModule } from 'angular-datatables';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\nimport { FilterPipe } from '@core/pipes/filter.pipe';\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { PaymentsComponent } from './payments.component';\nimport { PaymentDetailsComponent } from './payment-details/payment-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\nconst routes = [{\n  path: '',\n  component: PaymentsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_payments\n  }\n}, {\n  path: 'details/:id',\n  component: PaymentDetailsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_payments\n  }\n}];\nexport class PaymentsModule {\n  static #_ = this.ɵfac = function PaymentsModule_Factory(t) {\n    return new (t || PaymentsModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: PaymentsModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [FilterPipe],\n    imports: [CKEditorModule, CoreCommonModule, NgbNavModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, RouterModule.forChild(routes), ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, FormlyModule.forRoot({\n      types: [{\n        name: 'ng-select',\n        component: NgSelectTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'file',\n        component: FormlyFieldFile,\n        wrappers: ['form-field']\n      }],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    })]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PaymentsModule, {\n    declarations: [PaymentsComponent, PaymentDetailsComponent],\n    imports: [CKEditorModule, CoreCommonModule, NgbNavModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, i1.RouterModule, ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CoreTouchspinModule, NgbCollapseModule, FormlyBootstrapModule, i2.FormlyModule],\n    exports: [PaymentsComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,kBAAkB,EAClBC,iBAAiB,EACjBC,iBAAiB,EACjBC,SAAS,EACTC,YAAY,QACP,4BAA4B;AACnC,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,eAAe,QAA0B,qBAAqB;AACvE,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,qDAAqD;AAC5D,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAAuBC,YAAY,QAAQ,kBAAkB;AAC7D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,uBAAuB,QAAQ,6CAA6C;;;;AACrF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,iBAAiB;EAC5BK,WAAW,EAAE,CAACP,gBAAgB,CAAC;EAC/BQ,IAAI,EAAE;IAAEC,WAAW,EAAER,SAAS,CAACS,WAAW,CAACC;EAAe;CAC3D,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEH,uBAAuB;EAClCI,WAAW,EAAE,CAACP,gBAAgB,CAAC;EAC/BQ,IAAI,EAAE;IAAEC,WAAW,EAAER,SAAS,CAACS,WAAW,CAACC;EAAe;CAC3D,CACF;AAmDD,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA;UAAdF;EAAc;EAAA,QAAAG,EAAA;eAhDd,CAACnB,UAAU,CAAC;IAAAoB,OAAA,GAMrBlB,cAAc,EACdX,gBAAgB,EAChBP,YAAY,EACZJ,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBJ,YAAY,EACZC,YAAY,CAAC0C,QAAQ,CAACb,MAAM,CAAC,EAC7BvB,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBE,uBAAuB,EACvBG,mBAAmB,EACnBlB,iBAAiB,EACjBiB,qBAAqB,EACrBD,YAAY,CAACyB,OAAO,CAAC;MACnBC,KAAK,EAAE,CACL;QACEC,IAAI,EAAE,WAAW;QACjBd,SAAS,EAAET,qBAAqB;QAChCwB,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACED,IAAI,EAAE,MAAM;QACZd,SAAS,EAAEP,eAAe;QAC1BsB,QAAQ,EAAE,CAAC,YAAY;OACxB,CACF;MACDC,kBAAkB,EAAE,CAClB;QAAEF,IAAI,EAAE,aAAa;QAAEG,OAAO,EAAEhC;MAAuB,CAAE;KAE5D,CAAC;EAAA;;;2EAIOqB,cAAc;IAAAY,YAAA,GA9CvBtB,iBAAiB,EACjBC,uBAAuB;IAAAa,OAAA,GAGvBlB,cAAc,EACdX,gBAAgB,EAChBP,YAAY,EACZJ,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBJ,YAAY,EAAAmD,EAAA,CAAAlD,YAAA,EAEZM,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBE,uBAAuB,EACvBG,mBAAmB,EACnBlB,iBAAiB,EACjBiB,qBAAqB,EAAAgC,EAAA,CAAAjC,YAAA;IAAAkC,OAAA,GAmBbzB,iBAAiB;EAAA;AAAA", "names": ["CommonModule", "RouterModule", "NgbAccordionModule", "NgbCollapseModule", "NgbDropdownModule", "NgbModule", "NgbNavModule", "ContentHeaderModule", "FormsModule", "ReactiveFormsModule", "ErrorMessageModule", "TranslateModule", "CoreSidebarModule", "CoreCommonModule", "DataTablesModule", "NgSelectModule", "EditorSidebarModule", "serverValidationMessage", "BtnDropdownActionModule", "FormlyModule", "FormlyBootstrapModule", "CoreTouchspinModule", "FilterPipe", "NgSelectTypeComponent", "CKEditorModule", "FormlyFieldFile", "PermissionsGuard", "AppConfig", "PaymentsComponent", "PaymentDetailsComponent", "routes", "path", "component", "canActivate", "data", "permissions", "PERMISSIONS", "manage_payments", "PaymentsModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "types", "name", "wrappers", "validationMessages", "message", "declarations", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\payments\\payments.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport {\r\n  NgbAccordionModule,\r\n  NgbCollapseModule,\r\n  NgbDropdownModule,\r\n  NgbModule,\r\n  NgbNavModule,\r\n} from '@ng-bootstrap/ng-bootstrap';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\nimport { TranslateModule, TranslateService } from '@ngx-translate/core';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport {\r\n  EditorSidebarModule,\r\n  serverValidationMessage,\r\n} from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { FormlyConfig, FormlyModule } from '@ngx-formly/core';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\r\nimport { FilterPipe } from '@core/pipes/filter.pipe';\r\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\r\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\r\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\r\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { PaymentsComponent } from './payments.component';\r\nimport { PaymentDetailsComponent } from './payment-details/payment-details.component';\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: PaymentsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_payments },\r\n  },\r\n  {\r\n    path: 'details/:id',\r\n    component: PaymentDetailsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_payments },\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  providers: [FilterPipe],\r\n  declarations: [\r\n    PaymentsComponent,\r\n    PaymentDetailsComponent,\r\n  ],\r\n  imports: [\r\n    CKEditorModule,\r\n    CoreCommonModule,\r\n    NgbNavModule,\r\n    NgbAccordionModule,\r\n    NgbModule,\r\n    NgbDropdownModule,\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ErrorMessageModule,\r\n    TranslateModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    DataTablesModule,\r\n    NgSelectModule,\r\n    EditorSidebarModule,\r\n    BtnDropdownActionModule,\r\n    CoreTouchspinModule,\r\n    NgbCollapseModule,\r\n    FormlyBootstrapModule,\r\n    FormlyModule.forRoot({\r\n      types: [\r\n        {\r\n          name: 'ng-select',\r\n          component: NgSelectTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n        {\r\n          name: 'file',\r\n          component: FormlyFieldFile,\r\n          wrappers: ['form-field'],\r\n        },\r\n      ],\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],\r\n    }),\r\n  ],\r\n  exports: [PaymentsComponent],\r\n})\r\nexport class PaymentsModule {\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
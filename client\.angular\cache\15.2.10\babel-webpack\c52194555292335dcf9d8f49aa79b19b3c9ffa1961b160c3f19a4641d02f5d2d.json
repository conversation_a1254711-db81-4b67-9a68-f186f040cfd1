{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, TemplateRef, ViewContainerRef, Component, ChangeDetectionStrategy, ViewChild, Injector, Directive, NgModule } from '@angular/core';\nimport * as i1 from '@alyle/ui';\nimport { shadowBuilder, st2c, StyleCollection, StyleRenderer, STYLES_BACKDROP_DARK, createStyle, LyStyle, LyOverlayRef, LyCommonModule, LyOverlayModule } from '@alyle/ui';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport { ESCAPE } from '@angular/cdk/keycodes';\nimport { CommonModule } from '@angular/common';\nfunction LyDialogContainer_ng_template_0_Template(rf, ctx) {}\nclass LyDialogRef {\n  constructor(_overlayRef) {\n    this._overlayRef = _overlayRef;\n  }\n  get afterOpened() {\n    return this._overlayRef.componentRef.instance._afterOpened.asObservable();\n  }\n  get beforeClosed() {\n    return this._overlayRef.componentRef.instance._beforeClosed.asObservable();\n  }\n  get afterClosed() {\n    return this._overlayRef.componentRef.instance._afterClosed.asObservable();\n  }\n  /**\n   * @internal\n   * @docs-private\n   */\n  get result() {\n    return this._result;\n  }\n  close(result) {\n    const dialogContainer = this._overlayRef.componentRef.instance;\n    dialogContainer._beforeClosed.next(result);\n    dialogContainer._beforeClosed.complete();\n    dialogContainer._startClose();\n    this._result = result;\n  }\n}\nLyDialogRef.ɵfac = function LyDialogRef_Factory(t) {\n  return new (t || LyDialogRef)(i0.ɵɵinject(i1.LyOverlayRef));\n};\nLyDialogRef.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LyDialogRef,\n  factory: LyDialogRef.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialogRef, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.LyOverlayRef\n    }];\n  }, null);\n})();\n\n/**\n * Configuration for opening a modal dialog with the LyDialog service.\n */\nclass LyDialogConfig {\n  constructor() {\n    /**\n     * Max-height of the dialog container. If a number is provided, pixel units are assumed.\n     * Defaults to `['calc(100vw - 90px)']`\n     * Support beakpoints\n     */\n    this.maxHeight = ['calc(100vh - 64px)'];\n    /**\n     * Max-width of the dialog container. If a number is provided, pixel units are assumed.\n     * Defaults to `['calc(100vw - 90px)']`\n     * Support beakpoints\n     */\n    this.maxWidth = ['calc(100vw - 64px)'];\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n  }\n}\nconst LY_DIALOG_DATA = new InjectionToken('LyDialogData');\nconst STYLE_PRIORITY$3 = -2;\n/** @docs-private */\nconst STYLES = (theme, ref) => {\n  const dialog = ref.selectorsOf(STYLES);\n  return {\n    root: () => _className => `${_className}{display:flex;position:relative;background-color:${theme.background.primary.default};border-radius:4px;box-shadow:${shadowBuilder(12)};overflow:auto;pointer-events:auto;}${_className} > :first-child{display:flex;flex-direction:column;width:100%;}${st2c(theme.dialog && theme.dialog.root && (theme.dialog.root instanceof StyleCollection ? theme.dialog.root.setTransformer(fn => fn(dialog)) : theme.dialog.root(dialog)), `${_className}`)}`\n  };\n};\n/** @docs-private */\nclass LyDialogContainer {\n  constructor(sRenderer, _appRef, _overlayRef, _theme, _el, _cd, _renderer, _trapFactory) {\n    this.sRenderer = sRenderer;\n    this._appRef = _appRef;\n    this._overlayRef = _overlayRef;\n    this._theme = _theme;\n    this._el = _el;\n    this._cd = _cd;\n    this._renderer = _renderer;\n    this._trapFactory = _trapFactory;\n    /** @docs-private */\n    this.classes = this._theme.addStyleSheet(STYLES, STYLE_PRIORITY$3);\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    this._previouslyFocusedElement = null;\n    /** @internal */\n    this._afterOpened = new Subject();\n    /** @internal */\n    this._beforeClosed = new Subject();\n    /** @internal */\n    this._afterClosed = new Subject();\n    /**\n     * State of the dialog animation.\n     * @internal\n     */\n    this._state = 'enter';\n    _renderer.addClass(_el.nativeElement, this.classes.root);\n  }\n  ngOnInit() {\n    if (this._componentFactoryOrTemplate instanceof TemplateRef) {\n      const context = new LyDialogContext(this._newInjector);\n      this._embeddedViewRef = this.viewContainerRef.createEmbeddedView(this._componentFactoryOrTemplate, context);\n    } else {\n      this._componentRef = this.viewContainerRef.createComponent(this._componentFactoryOrTemplate, undefined, this._newInjector);\n    }\n    // If exist dialogStyleBlock apply for this component, else do nothing.\n    const {\n      containerClass\n    } = this._newInjector.get(LyDialogConfig);\n    if (containerClass) {\n      this._renderer.addClass(this._el.nativeElement, containerClass);\n    }\n  }\n  ngAfterContentInit() {\n    this._focusTrap = this._trapFactory.create(this._el.nativeElement);\n    this._captureFocus();\n  }\n  ngOnDestroy() {\n    this._focusTrap.destroy();\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  /** @internal */\n  _init(componentFactoryOrTemplate, newInjector) {\n    this._componentFactoryOrTemplate = componentFactoryOrTemplate;\n    this._newInjector = newInjector;\n  }\n  /**\n   * Start to close, starts the dialog exit animation.\n   * @internal\n   */\n  _startClose() {\n    this._state = 'exit';\n    this._cd.markForCheck();\n  }\n  _onAnimationStart(event) {\n    if (event.toState === 'enter') {\n      this._overlayRef.onResizeScroll();\n    }\n  }\n  /** @internal */\n  _onAnimationDone(event) {\n    if (event.toState === 'exit') {\n      const dialogRef = this._newInjector.get(LyDialogRef);\n      this._destroy();\n      this._overlayRef.destroy();\n      this._afterClosed.next(dialogRef.result);\n      this._afterClosed.complete();\n    } else if (event.toState === 'enter') {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  _destroy() {\n    if (this._componentRef) {\n      this._appRef.detachView(this._componentRef.hostView);\n      this._componentRef.destroy();\n    } else {\n      this._appRef.detachView(this._embeddedViewRef);\n      this._embeddedViewRef.detach();\n      this._embeddedViewRef.destroy();\n    }\n  }\n  /** @internal */\n  _getHostElement() {\n    return this._el.nativeElement;\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this._focusTrap.focusInitialElementWhenReady();\n  }\n}\nLyDialogContainer.ɵfac = function LyDialogContainer_Factory(t) {\n  return new (t || LyDialogContainer)(i0.ɵɵdirectiveInject(i1.StyleRenderer), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i1.LyOverlayRef), i0.ɵɵdirectiveInject(i1.LyTheme2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.FocusTrapFactory));\n};\nLyDialogContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: LyDialogContainer,\n  selectors: [[\"ly-dialog-container\"]],\n  viewQuery: function LyDialogContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7, ViewContainerRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.viewContainerRef = _t.first);\n    }\n  },\n  hostVars: 1,\n  hostBindings: function LyDialogContainer_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵsyntheticHostListener(\"@dialogContainer.start\", function LyDialogContainer_animation_dialogContainer_start_HostBindingHandler($event) {\n        return ctx._onAnimationStart($event);\n      })(\"@dialogContainer.done\", function LyDialogContainer_animation_dialogContainer_done_HostBindingHandler($event) {\n        return ctx._onAnimationDone($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵsyntheticHostProperty(\"@dialogContainer\", ctx._state);\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([StyleRenderer])],\n  decls: 1,\n  vars: 0,\n  template: function LyDialogContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, LyDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\");\n    }\n  },\n  encapsulation: 2,\n  data: {\n    animation: [trigger('dialogContainer', [state('void, exit', style({\n      opacity: 0,\n      transform: 'scale(0.7)'\n    })), state('enter', style({\n      transform: 'none'\n    })), transition('* => enter', animate('150ms cubic-bezier(0, 0, 0.2, 1)', style({\n      transform: 'none',\n      opacity: 1\n    }))), transition('* => void, * => exit', animate('75ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({\n      opacity: 0\n    })))])]\n  },\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'ly-dialog-container',\n      template: '<ng-template></ng-template>',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [trigger('dialogContainer', [state('void, exit', style({\n        opacity: 0,\n        transform: 'scale(0.7)'\n      })), state('enter', style({\n        transform: 'none'\n      })), transition('* => enter', animate('150ms cubic-bezier(0, 0, 0.2, 1)', style({\n        transform: 'none',\n        opacity: 1\n      }))), transition('* => void, * => exit', animate('75ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({\n        opacity: 0\n      })))])],\n      host: {\n        '[@dialogContainer]': '_state',\n        '(@dialogContainer.start)': '_onAnimationStart($event)',\n        '(@dialogContainer.done)': '_onAnimationDone($event)'\n      },\n      providers: [StyleRenderer]\n    }]\n  }], function () {\n    return [{\n      type: i1.StyleRenderer\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: i1.LyOverlayRef\n    }, {\n      type: i1.LyTheme2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i2.FocusTrapFactory\n    }];\n  }, {\n    viewContainerRef: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        read: ViewContainerRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass LyDialogContext {\n  constructor(_injector) {\n    this._injector = _injector;\n    this.$implicit = this._injector.get(LyDialogRef);\n    this.dialogRef = this._injector.get(LyDialogRef);\n  }\n  get data() {\n    return this._injector.get(LY_DIALOG_DATA);\n  }\n}\nclass DynamicInjector {\n  constructor(_newInjector, _parentInjector) {\n    this._newInjector = _newInjector;\n    this._parentInjector = _parentInjector;\n  }\n  get(token, notFoundValue, _flags) {\n    const value = this._newInjector.get(token, notFoundValue);\n    if (value) {\n      return value;\n    }\n    return this._parentInjector.get(token, notFoundValue);\n  }\n}\nconst dialogContainerStyleProperties = ['width', 'maxWidth', 'minWidth', 'height', 'maxHeight', 'minHeight'];\nclass LyDialog {\n  constructor(_overlay, _componentFactoryResolver, _theme, _injector) {\n    this._overlay = _overlay;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._theme = _theme;\n    this._injector = _injector;\n  }\n  open(componentOrTemplateRef, config) {\n    // merge with default config\n    config = {\n      ...new LyDialogConfig(),\n      ...config\n    };\n    let componentFactoryOrTemplate;\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      componentFactoryOrTemplate = componentOrTemplateRef;\n    } else {\n      componentFactoryOrTemplate = this._componentFactoryResolver.resolveComponentFactory(componentOrTemplateRef);\n    }\n    const noop = () => {};\n    const overlayRef = this._overlay.create(LyDialogContainer, null, {\n      styles: {\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0\n      },\n      hasBackdrop: config.hasBackdrop,\n      onResizeScroll: noop,\n      disableClose: config.disableClose,\n      backdropClass: config.backdropClass || this._theme.style(STYLES_BACKDROP_DARK),\n      fnDestroy: () => {\n        dialogRef.close();\n        keydownEventsSuscription.unsubscribe();\n      }\n    });\n    const keydownEvents = overlayRef.keydownEvents();\n    const keydownEventsSuscription = keydownEvents.subscribe(event => {\n      if (!config?.disableClose && event.keyCode === ESCAPE) {\n        dialogRef.close();\n      }\n    });\n    const instance = overlayRef.componentRef.instance;\n    dialogContainerStyleProperties.forEach(property => {\n      if (config[property]) {\n        createStyle(instance, {\n          key: property,\n          и: LyStyle.и\n        }, config[property], LyStyle[property], LyStyle.$priority);\n      }\n    });\n    const providers = [{\n      provide: LyDialogRef,\n      useValue: new LyDialogRef(overlayRef.componentRef.injector.get(LyOverlayRef))\n    }, {\n      provide: LyDialogConfig,\n      useValue: config\n    }];\n    if (config.data != null) {\n      providers.push({\n        provide: LY_DIALOG_DATA,\n        useValue: config.data\n      });\n    }\n    const newInjector = new DynamicInjector(Injector.create(providers, overlayRef.componentRef.injector), this._injector);\n    instance._init(componentFactoryOrTemplate, newInjector);\n    const dialogRef = newInjector.get(LyDialogRef);\n    return dialogRef;\n  }\n}\nLyDialog.ɵfac = function LyDialog_Factory(t) {\n  return new (t || LyDialog)(i0.ɵɵinject(i1.LyOverlay), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i1.LyTheme2), i0.ɵɵinject(i0.Injector));\n};\nLyDialog.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LyDialog,\n  factory: LyDialog.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialog, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.LyOverlay\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i1.LyTheme2\n    }, {\n      type: i0.Injector\n    }];\n  }, null);\n})();\n\n/** @docs-private */\nconst STYLE_PRIORITY$2 = -2;\n/** @docs-private */\nconst STYLES_DIALOG_TITLE = theme => _className => `${_className}{display:block;flex:0 0 auto;margin:20px 0 16px;padding:0 24px;font-size:20px;line-height:24px;font-weight:500;font-family:${theme.typography.fontFamily};}`;\nclass LyDialogTitle {\n  constructor(_renderer, _el, _theme) {\n    this._renderer = _renderer;\n    this._el = _el;\n    this._theme = _theme;\n  }\n  ngOnInit() {\n    this._renderer.addClass(this._el.nativeElement, this._theme.renderStyle(STYLES_DIALOG_TITLE, STYLE_PRIORITY$2));\n  }\n}\nLyDialogTitle.ɵfac = function LyDialogTitle_Factory(t) {\n  return new (t || LyDialogTitle)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.LyTheme2));\n};\nLyDialogTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: LyDialogTitle,\n  selectors: [[\"\", \"ly-dialog-title\", \"\"], [\"\", \"lyDialogTitle\", \"\"]],\n  exportAs: [\"lyDialogTitle\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialogTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[ly-dialog-title], [lyDialogTitle]',\n      exportAs: 'lyDialogTitle'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.LyTheme2\n    }];\n  }, null);\n})();\n\n/** @docs-private */\nconst STYLE_PRIORITY$1 = -2;\n/** @docs-private */\nconst STYLES_DIALOG_CONTENT = () => _className => `${_className}{display:block;overflow-y:auto;flex:1 1 auto;padding:0 24px 24px;-webkit-overflow-scrolling:touch;}`;\nclass LyDialogContent {\n  constructor(_renderer, _el, _theme) {\n    this._renderer = _renderer;\n    this._el = _el;\n    this._theme = _theme;\n  }\n  ngOnInit() {\n    this._renderer.addClass(this._el.nativeElement, this._theme.renderStyle(STYLES_DIALOG_CONTENT, STYLE_PRIORITY$1));\n  }\n}\nLyDialogContent.ɵfac = function LyDialogContent_Factory(t) {\n  return new (t || LyDialogContent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.LyTheme2));\n};\nLyDialogContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: LyDialogContent,\n  selectors: [[\"ly-dialog-content\"], [\"\", \"ly-dialog-content\", \"\"], [\"\", \"lyDialogContent\", \"\"]],\n  exportAs: [\"lyDialogContent\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ly-dialog-content, [ly-dialog-content], [lyDialogContent]',\n      exportAs: 'lyDialogContent'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.LyTheme2\n    }];\n  }, null);\n})();\n\n/** @docs-private */\nconst STYLE_PRIORITY = -2;\n/** @docs-private */\nconst STYLES_DIALOG_ACTIONS = () => _className => `${_className}{display:flex;flex:0 0 auto;padding:8px;flex-wrap:wrap;min-height:52px;align-items:center;}`;\nclass LyDialogActions {\n  constructor(_renderer, _el, _theme) {\n    this._renderer = _renderer;\n    this._el = _el;\n    this._theme = _theme;\n  }\n  ngOnInit() {\n    this._renderer.addClass(this._el.nativeElement, this._theme.renderStyle(STYLES_DIALOG_ACTIONS, STYLE_PRIORITY));\n  }\n}\nLyDialogActions.ɵfac = function LyDialogActions_Factory(t) {\n  return new (t || LyDialogActions)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.LyTheme2));\n};\nLyDialogActions.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: LyDialogActions,\n  selectors: [[\"ly-dialog-actions\"], [\"\", \"ly-dialog-actions\", \"\"], [\"\", \"lyDialogActions\", \"\"]],\n  exportAs: [\"lyDialogActions\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialogActions, [{\n    type: Directive,\n    args: [{\n      selector: 'ly-dialog-actions, [ly-dialog-actions], [lyDialogActions]',\n      exportAs: 'lyDialogActions'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.LyTheme2\n    }];\n  }, null);\n})();\nclass LyDialogModule {}\nLyDialogModule.ɵfac = function LyDialogModule_Factory(t) {\n  return new (t || LyDialogModule)();\n};\nLyDialogModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LyDialogModule\n});\nLyDialogModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [LyDialog],\n  imports: [[CommonModule, LyCommonModule, LyOverlayModule, A11yModule], LyCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyDialogModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [LyDialogContainer, LyDialogTitle, LyDialogContent, LyDialogActions],\n      imports: [CommonModule, LyCommonModule, LyOverlayModule, A11yModule],\n      exports: [LyCommonModule, LyDialogContainer, LyDialogTitle, LyDialogContent, LyDialogActions],\n      providers: [LyDialog]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LY_DIALOG_DATA, LyDialog, LyDialogActions, LyDialogContainer, LyDialogContent, LyDialogContext, LyDialogModule, LyDialogRef, LyDialogTitle };", "map": {"version": 3, "names": ["i0", "Injectable", "InjectionToken", "TemplateRef", "ViewContainerRef", "Component", "ChangeDetectionStrategy", "ViewChild", "Injector", "Directive", "NgModule", "i1", "shadowBuilder", "st2c", "StyleCollection", "<PERSON><PERSON><PERSON><PERSON>", "STYLES_BACKDROP_DARK", "createStyle", "LyStyle", "LyOverlayRef", "LyCommonModule", "LyOverlayModule", "trigger", "state", "style", "transition", "animate", "Subject", "_getFocusedElementPierceShadowDom", "i2", "A11yModule", "ESCAPE", "CommonModule", "LyDialogContainer_ng_template_0_Template", "rf", "ctx", "LyDialogRef", "constructor", "_overlayRef", "afterOpened", "componentRef", "instance", "_afterOpened", "asObservable", "beforeClosed", "_beforeClosed", "afterClosed", "_afterClosed", "result", "_result", "close", "dialogContainer", "next", "complete", "_startClose", "ɵfac", "LyDialogRef_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "LyDialogConfig", "maxHeight", "max<PERSON><PERSON><PERSON>", "hasBackdrop", "LY_DIALOG_DATA", "STYLE_PRIORITY$3", "STYLES", "theme", "ref", "dialog", "selectorsOf", "root", "_className", "background", "primary", "default", "setTransformer", "fn", "LyDialogContainer", "s<PERSON><PERSON><PERSON>", "_appRef", "_theme", "_el", "_cd", "_renderer", "_trapFactory", "classes", "addStyleSheet", "_previouslyFocusedElement", "_state", "addClass", "nativeElement", "ngOnInit", "_componentFactoryOrTemplate", "context", "LyDialogContext", "_newInjector", "_embeddedViewRef", "viewContainerRef", "createEmbeddedView", "_componentRef", "createComponent", "undefined", "containerClass", "get", "ngAfterContentInit", "_focusTrap", "create", "_captureFocus", "ngOnDestroy", "destroy", "focus", "_init", "componentFactoryOrTemplate", "newInjector", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onAnimationStart", "event", "toState", "onResizeScroll", "_onAnimationDone", "dialogRef", "_destroy", "detach<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "detach", "_getHostElement", "focusInitialElementWhenReady", "LyDialogContainer_Factory", "ɵɵdirectiveInject", "ApplicationRef", "LyTheme2", "ElementRef", "ChangeDetectorRef", "Renderer2", "FocusTrapFactory", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "LyDialogContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "LyDialogContainer_HostBindings", "ɵɵsyntheticHostListener", "LyDialogContainer_animation_dialogContainer_start_HostBindingHandler", "$event", "LyDialogContainer_animation_dialogContainer_done_HostBindingHandler", "ɵɵsyntheticHostProperty", "features", "ɵɵProvidersFeature", "decls", "vars", "template", "LyDialogContainer_Template", "ɵɵtemplate", "encapsulation", "data", "animation", "opacity", "transform", "changeDetection", "args", "selector", "OnPush", "animations", "host", "providers", "read", "static", "_injector", "$implicit", "DynamicInjector", "_parentInjector", "notFoundValue", "_flags", "value", "dialogContainerStyleProperties", "LyDialog", "_overlay", "_componentFactoryResolver", "open", "componentOrTemplateRef", "config", "resolveComponentFactory", "noop", "overlayRef", "styles", "top", "left", "right", "bottom", "disableClose", "backdropClass", "fnDestroy", "keydownEventsSuscription", "unsubscribe", "keydownEvents", "subscribe", "keyCode", "for<PERSON>ach", "property", "key", "и", "$priority", "provide", "useValue", "injector", "push", "LyDialog_Factory", "LyOverlay", "ComponentFactoryResolver", "STYLE_PRIORITY$2", "STYLES_DIALOG_TITLE", "typography", "fontFamily", "LyDialogTitle", "renderStyle", "LyDialogTitle_Factory", "ɵdir", "ɵɵdefineDirective", "exportAs", "STYLE_PRIORITY$1", "STYLES_DIALOG_CONTENT", "LyDialogContent", "LyDialogContent_Factory", "STYLE_PRIORITY", "STYLES_DIALOG_ACTIONS", "LyDialogActions", "LyDialogActions_Factory", "LyDialogModule", "LyDialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@alyle/ui/fesm2020/alyle-ui-dialog.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, TemplateRef, ViewContainerRef, Component, ChangeDetectionStrategy, ViewChild, Injector, Directive, NgModule } from '@angular/core';\nimport * as i1 from '@alyle/ui';\nimport { shadowBuilder, st2c, StyleCollection, StyleRenderer, STYLES_BACKDROP_DARK, createStyle, LyStyle, LyOverlayRef, LyCommonModule, LyOverlayModule } from '@alyle/ui';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport { ESCAPE } from '@angular/cdk/keycodes';\nimport { CommonModule } from '@angular/common';\n\nclass LyDialogRef {\n    constructor(_overlayRef) {\n        this._overlayRef = _overlayRef;\n    }\n    get afterOpened() {\n        return this._overlayRef.componentRef.instance._afterOpened.asObservable();\n    }\n    get beforeClosed() {\n        return this._overlayRef.componentRef.instance._beforeClosed.asObservable();\n    }\n    get afterClosed() {\n        return this._overlayRef.componentRef.instance._afterClosed.asObservable();\n    }\n    /**\n     * @internal\n     * @docs-private\n     */\n    get result() {\n        return this._result;\n    }\n    close(result) {\n        const dialogContainer = this._overlayRef.componentRef.instance;\n        dialogContainer._beforeClosed.next(result);\n        dialogContainer._beforeClosed.complete();\n        dialogContainer._startClose();\n        this._result = result;\n    }\n}\nLyDialogRef.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogRef, deps: [{ token: i1.LyOverlayRef }], target: i0.ɵɵFactoryTarget.Injectable });\nLyDialogRef.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogRef });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogRef, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1.LyOverlayRef }]; } });\n\n/**\n * Configuration for opening a modal dialog with the LyDialog service.\n */\nclass LyDialogConfig {\n    constructor() {\n        /**\n         * Max-height of the dialog container. If a number is provided, pixel units are assumed.\n         * Defaults to `['calc(100vw - 90px)']`\n         * Support beakpoints\n         */\n        this.maxHeight = ['calc(100vh - 64px)'];\n        /**\n         * Max-width of the dialog container. If a number is provided, pixel units are assumed.\n         * Defaults to `['calc(100vw - 90px)']`\n         * Support beakpoints\n         */\n        this.maxWidth = ['calc(100vw - 64px)'];\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n    }\n}\n\nconst LY_DIALOG_DATA = new InjectionToken('LyDialogData');\n\nconst STYLE_PRIORITY$3 = -2;\n/** @docs-private */\nconst STYLES = (theme, ref) => {\n    const dialog = ref.selectorsOf(STYLES);\n    return {\n        root: () => (_className) => `${_className}{display:flex;position:relative;background-color:${theme.background.primary.default};border-radius:4px;box-shadow:${shadowBuilder(12)};overflow:auto;pointer-events:auto;}${_className} > :first-child{display:flex;flex-direction:column;width:100%;}${st2c(((theme.dialog\n            && theme.dialog.root\n            && (theme.dialog.root instanceof StyleCollection\n                ? theme.dialog.root.setTransformer(fn => fn(dialog))\n                : theme.dialog.root(dialog)))), `${_className}`)}`\n    };\n};\n/** @docs-private */\nclass LyDialogContainer {\n    constructor(sRenderer, _appRef, _overlayRef, _theme, _el, _cd, _renderer, _trapFactory) {\n        this.sRenderer = sRenderer;\n        this._appRef = _appRef;\n        this._overlayRef = _overlayRef;\n        this._theme = _theme;\n        this._el = _el;\n        this._cd = _cd;\n        this._renderer = _renderer;\n        this._trapFactory = _trapFactory;\n        /** @docs-private */\n        this.classes = this._theme.addStyleSheet(STYLES, STYLE_PRIORITY$3);\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        /** @internal */\n        this._afterOpened = new Subject();\n        /** @internal */\n        this._beforeClosed = new Subject();\n        /** @internal */\n        this._afterClosed = new Subject();\n        /**\n         * State of the dialog animation.\n         * @internal\n         */\n        this._state = 'enter';\n        _renderer.addClass(_el.nativeElement, this.classes.root);\n    }\n    ngOnInit() {\n        if (this._componentFactoryOrTemplate instanceof TemplateRef) {\n            const context = new LyDialogContext(this._newInjector);\n            this._embeddedViewRef = this.viewContainerRef\n                .createEmbeddedView(this._componentFactoryOrTemplate, context);\n        }\n        else {\n            this._componentRef = this.viewContainerRef\n                .createComponent(this._componentFactoryOrTemplate, undefined, this._newInjector);\n        }\n        // If exist dialogStyleBlock apply for this component, else do nothing.\n        const { containerClass } = this._newInjector.get(LyDialogConfig);\n        if (containerClass) {\n            this._renderer.addClass(this._el.nativeElement, containerClass);\n        }\n    }\n    ngAfterContentInit() {\n        this._focusTrap = this._trapFactory.create(this._el.nativeElement);\n        this._captureFocus();\n    }\n    ngOnDestroy() {\n        this._focusTrap.destroy();\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    /** @internal */\n    _init(componentFactoryOrTemplate, newInjector) {\n        this._componentFactoryOrTemplate = componentFactoryOrTemplate;\n        this._newInjector = newInjector;\n    }\n    /**\n     * Start to close, starts the dialog exit animation.\n     * @internal\n     */\n    _startClose() {\n        this._state = 'exit';\n        this._cd.markForCheck();\n    }\n    _onAnimationStart(event) {\n        if (event.toState === 'enter') {\n            this._overlayRef.onResizeScroll();\n        }\n    }\n    /** @internal */\n    _onAnimationDone(event) {\n        if (event.toState === 'exit') {\n            const dialogRef = this._newInjector.get(LyDialogRef);\n            this._destroy();\n            this._overlayRef.destroy();\n            this._afterClosed.next(dialogRef.result);\n            this._afterClosed.complete();\n        }\n        else if (event.toState === 'enter') {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    _destroy() {\n        if (this._componentRef) {\n            this._appRef.detachView(this._componentRef.hostView);\n            this._componentRef.destroy();\n        }\n        else {\n            this._appRef.detachView(this._embeddedViewRef);\n            this._embeddedViewRef.detach();\n            this._embeddedViewRef.destroy();\n        }\n    }\n    /** @internal */\n    _getHostElement() {\n        return this._el.nativeElement;\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this._focusTrap.focusInitialElementWhenReady();\n    }\n}\nLyDialogContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogContainer, deps: [{ token: i1.StyleRenderer }, { token: i0.ApplicationRef }, { token: i1.LyOverlayRef }, { token: i1.LyTheme2 }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.Renderer2 }, { token: i2.FocusTrapFactory }], target: i0.ɵɵFactoryTarget.Component });\nLyDialogContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyDialogContainer, selector: \"ly-dialog-container\", host: { listeners: { \"@dialogContainer.start\": \"_onAnimationStart($event)\", \"@dialogContainer.done\": \"_onAnimationDone($event)\" }, properties: { \"@dialogContainer\": \"_state\" } }, providers: [\n        StyleRenderer\n    ], viewQueries: [{ propertyName: \"viewContainerRef\", first: true, predicate: TemplateRef, descendants: true, read: ViewContainerRef, static: true }], ngImport: i0, template: '<ng-template></ng-template>', isInline: true, animations: [\n        trigger('dialogContainer', [\n            state('void, exit', style({ opacity: 0, transform: 'scale(0.7)' })),\n            state('enter', style({ transform: 'none' })),\n            transition('* => enter', animate('150ms cubic-bezier(0, 0, 0.2, 1)', style({ transform: 'none', opacity: 1 }))),\n            transition('* => void, * => exit', animate('75ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({ opacity: 0 })))\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogContainer, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ly-dialog-container',\n                    template: '<ng-template></ng-template>',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    animations: [\n                        trigger('dialogContainer', [\n                            state('void, exit', style({ opacity: 0, transform: 'scale(0.7)' })),\n                            state('enter', style({ transform: 'none' })),\n                            transition('* => enter', animate('150ms cubic-bezier(0, 0, 0.2, 1)', style({ transform: 'none', opacity: 1 }))),\n                            transition('* => void, * => exit', animate('75ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({ opacity: 0 })))\n                        ])\n                    ],\n                    host: {\n                        '[@dialogContainer]': '_state',\n                        '(@dialogContainer.start)': '_onAnimationStart($event)',\n                        '(@dialogContainer.done)': '_onAnimationDone($event)'\n                    },\n                    providers: [\n                        StyleRenderer\n                    ]\n                }]\n        }], ctorParameters: function () { return [{ type: i1.StyleRenderer }, { type: i0.ApplicationRef }, { type: i1.LyOverlayRef }, { type: i1.LyTheme2 }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.Renderer2 }, { type: i2.FocusTrapFactory }]; }, propDecorators: { viewContainerRef: [{\n                type: ViewChild,\n                args: [TemplateRef, { read: ViewContainerRef, static: true }]\n            }] } });\nclass LyDialogContext {\n    constructor(_injector) {\n        this._injector = _injector;\n        this.$implicit = this._injector.get(LyDialogRef);\n        this.dialogRef = this._injector.get(LyDialogRef);\n    }\n    get data() {\n        return this._injector.get(LY_DIALOG_DATA);\n    }\n}\n\nclass DynamicInjector {\n    constructor(_newInjector, _parentInjector) {\n        this._newInjector = _newInjector;\n        this._parentInjector = _parentInjector;\n    }\n    get(token, notFoundValue, _flags) {\n        const value = this._newInjector.get(token, notFoundValue);\n        if (value) {\n            return value;\n        }\n        return this._parentInjector.get(token, notFoundValue);\n    }\n}\n\nconst dialogContainerStyleProperties = [\n    'width',\n    'maxWidth',\n    'minWidth',\n    'height',\n    'maxHeight',\n    'minHeight',\n];\nclass LyDialog {\n    constructor(_overlay, _componentFactoryResolver, _theme, _injector) {\n        this._overlay = _overlay;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._theme = _theme;\n        this._injector = _injector;\n    }\n    open(componentOrTemplateRef, config) {\n        // merge with default config\n        config = { ...new LyDialogConfig(), ...config };\n        let componentFactoryOrTemplate;\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            componentFactoryOrTemplate = componentOrTemplateRef;\n        }\n        else {\n            componentFactoryOrTemplate = this._componentFactoryResolver.resolveComponentFactory(componentOrTemplateRef);\n        }\n        const noop = () => { };\n        const overlayRef = this._overlay.create(LyDialogContainer, null, {\n            styles: {\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0\n            },\n            hasBackdrop: config.hasBackdrop,\n            onResizeScroll: noop,\n            disableClose: config.disableClose,\n            backdropClass: config.backdropClass || this._theme.style(STYLES_BACKDROP_DARK),\n            fnDestroy: () => {\n                dialogRef.close();\n                keydownEventsSuscription.unsubscribe();\n            }\n        });\n        const keydownEvents = overlayRef.keydownEvents();\n        const keydownEventsSuscription = keydownEvents.subscribe((event) => {\n            if (!(config?.disableClose) && event.keyCode === ESCAPE) {\n                dialogRef.close();\n            }\n        });\n        const instance = overlayRef.componentRef.instance;\n        dialogContainerStyleProperties.forEach(property => {\n            if (config[property]) {\n                createStyle(instance, { key: property, и: LyStyle.и }, config[property], LyStyle[property], LyStyle.$priority);\n            }\n        });\n        const providers = [\n            {\n                provide: LyDialogRef,\n                useValue: new LyDialogRef(overlayRef.componentRef.injector.get(LyOverlayRef))\n            },\n            {\n                provide: LyDialogConfig,\n                useValue: config\n            }\n        ];\n        if (config.data != null) {\n            providers.push({\n                provide: LY_DIALOG_DATA,\n                useValue: config.data\n            });\n        }\n        const newInjector = new DynamicInjector(Injector.create(providers, overlayRef.componentRef.injector), this._injector);\n        instance._init(componentFactoryOrTemplate, newInjector);\n        const dialogRef = newInjector.get(LyDialogRef);\n        return dialogRef;\n    }\n}\nLyDialog.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialog, deps: [{ token: i1.LyOverlay }, { token: i0.ComponentFactoryResolver }, { token: i1.LyTheme2 }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable });\nLyDialog.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialog });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialog, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1.LyOverlay }, { type: i0.ComponentFactoryResolver }, { type: i1.LyTheme2 }, { type: i0.Injector }]; } });\n\n/** @docs-private */\nconst STYLE_PRIORITY$2 = -2;\n/** @docs-private */\nconst STYLES_DIALOG_TITLE = (theme) => (_className) => `${_className}{display:block;flex:0 0 auto;margin:20px 0 16px;padding:0 24px;font-size:20px;line-height:24px;font-weight:500;font-family:${theme.typography.fontFamily};}`;\nclass LyDialogTitle {\n    constructor(_renderer, _el, _theme) {\n        this._renderer = _renderer;\n        this._el = _el;\n        this._theme = _theme;\n    }\n    ngOnInit() {\n        this._renderer.addClass(this._el.nativeElement, this._theme.renderStyle(STYLES_DIALOG_TITLE, STYLE_PRIORITY$2));\n    }\n}\nLyDialogTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogTitle, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i1.LyTheme2 }], target: i0.ɵɵFactoryTarget.Directive });\nLyDialogTitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyDialogTitle, selector: \"[ly-dialog-title], [lyDialogTitle]\", exportAs: [\"lyDialogTitle\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ly-dialog-title], [lyDialogTitle]',\n                    exportAs: 'lyDialogTitle'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i1.LyTheme2 }]; } });\n\n/** @docs-private */\nconst STYLE_PRIORITY$1 = -2;\n/** @docs-private */\nconst STYLES_DIALOG_CONTENT = () => (_className) => `${_className}{display:block;overflow-y:auto;flex:1 1 auto;padding:0 24px 24px;-webkit-overflow-scrolling:touch;}`;\nclass LyDialogContent {\n    constructor(_renderer, _el, _theme) {\n        this._renderer = _renderer;\n        this._el = _el;\n        this._theme = _theme;\n    }\n    ngOnInit() {\n        this._renderer.addClass(this._el.nativeElement, this._theme.renderStyle(STYLES_DIALOG_CONTENT, STYLE_PRIORITY$1));\n    }\n}\nLyDialogContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogContent, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i1.LyTheme2 }], target: i0.ɵɵFactoryTarget.Directive });\nLyDialogContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyDialogContent, selector: \"ly-dialog-content, [ly-dialog-content], [lyDialogContent]\", exportAs: [\"lyDialogContent\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ly-dialog-content, [ly-dialog-content], [lyDialogContent]',\n                    exportAs: 'lyDialogContent'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i1.LyTheme2 }]; } });\n\n/** @docs-private */\nconst STYLE_PRIORITY = -2;\n/** @docs-private */\nconst STYLES_DIALOG_ACTIONS = () => (_className) => `${_className}{display:flex;flex:0 0 auto;padding:8px;flex-wrap:wrap;min-height:52px;align-items:center;}`;\nclass LyDialogActions {\n    constructor(_renderer, _el, _theme) {\n        this._renderer = _renderer;\n        this._el = _el;\n        this._theme = _theme;\n    }\n    ngOnInit() {\n        this._renderer.addClass(this._el.nativeElement, this._theme.renderStyle(STYLES_DIALOG_ACTIONS, STYLE_PRIORITY));\n    }\n}\nLyDialogActions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogActions, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i1.LyTheme2 }], target: i0.ɵɵFactoryTarget.Directive });\nLyDialogActions.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyDialogActions, selector: \"ly-dialog-actions, [ly-dialog-actions], [lyDialogActions]\", exportAs: [\"lyDialogActions\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ly-dialog-actions, [ly-dialog-actions], [lyDialogActions]',\n                    exportAs: 'lyDialogActions'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i1.LyTheme2 }]; } });\n\nclass LyDialogModule {\n}\nLyDialogModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nLyDialogModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogModule, declarations: [LyDialogContainer,\n        LyDialogTitle,\n        LyDialogContent,\n        LyDialogActions], imports: [CommonModule,\n        LyCommonModule,\n        LyOverlayModule,\n        A11yModule], exports: [LyCommonModule,\n        LyDialogContainer,\n        LyDialogTitle,\n        LyDialogContent,\n        LyDialogActions] });\nLyDialogModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogModule, providers: [\n        LyDialog\n    ], imports: [[\n            CommonModule,\n            LyCommonModule,\n            LyOverlayModule,\n            A11yModule\n        ], LyCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyDialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        LyDialogContainer,\n                        LyDialogTitle,\n                        LyDialogContent,\n                        LyDialogActions\n                    ],\n                    imports: [\n                        CommonModule,\n                        LyCommonModule,\n                        LyOverlayModule,\n                        A11yModule\n                    ],\n                    exports: [\n                        LyCommonModule,\n                        LyDialogContainer,\n                        LyDialogTitle,\n                        LyDialogContent,\n                        LyDialogActions\n                    ],\n                    providers: [\n                        LyDialog\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LY_DIALOG_DATA, LyDialog, LyDialogActions, LyDialogContainer, LyDialogContent, LyDialogContext, LyDialogModule, LyDialogRef, LyDialogTitle };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACvK,OAAO,KAAKC,EAAE,MAAM,WAAW;AAC/B,SAASC,aAAa,EAAEC,IAAI,EAAEC,eAAe,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,OAAO,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,WAAW;AAC1K,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,iCAAiC,QAAQ,uBAAuB;AACzE,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;AAE/C,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,WAAW,CAACE,YAAY,CAACC,QAAQ,CAACC,YAAY,CAACC,YAAY,CAAC,CAAC;EAC7E;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACN,WAAW,CAACE,YAAY,CAACC,QAAQ,CAACI,aAAa,CAACF,YAAY,CAAC,CAAC;EAC9E;EACA,IAAIG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACR,WAAW,CAACE,YAAY,CAACC,QAAQ,CAACM,YAAY,CAACJ,YAAY,CAAC,CAAC;EAC7E;EACA;AACJ;AACA;AACA;EACI,IAAIK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACAC,KAAKA,CAACF,MAAM,EAAE;IACV,MAAMG,eAAe,GAAG,IAAI,CAACb,WAAW,CAACE,YAAY,CAACC,QAAQ;IAC9DU,eAAe,CAACN,aAAa,CAACO,IAAI,CAACJ,MAAM,CAAC;IAC1CG,eAAe,CAACN,aAAa,CAACQ,QAAQ,CAAC,CAAC;IACxCF,eAAe,CAACG,WAAW,CAAC,CAAC;IAC7B,IAAI,CAACL,OAAO,GAAGD,MAAM;EACzB;AACJ;AACAZ,WAAW,CAACmB,IAAI,YAAAC,oBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyFrB,WAAW,EAArBpC,EAAE,CAAA0D,QAAA,CAAqC/C,EAAE,CAACQ,YAAY;AAAA,CAA6C;AAClMiB,WAAW,CAACuB,KAAK,kBAD8E3D,EAAE,CAAA4D,kBAAA;EAAAC,KAAA,EACYzB,WAAW;EAAA0B,OAAA,EAAX1B,WAAW,CAAAmB;AAAA,EAAG;AAC3H;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAF+F/D,EAAE,CAAAgE,iBAAA,CAEL5B,WAAW,EAAc,CAAC;IAC1G6B,IAAI,EAAEhE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgE,IAAI,EAAEtD,EAAE,CAACQ;IAAa,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE/E;AACA;AACA;AACA,MAAM+C,cAAc,CAAC;EACjB7B,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC8B,SAAS,GAAG,CAAC,oBAAoB,CAAC;IACvC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,CAAC,oBAAoB,CAAC;IACtC;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;AACJ;AAEA,MAAMC,cAAc,GAAG,IAAIpE,cAAc,CAAC,cAAc,CAAC;AAEzD,MAAMqE,gBAAgB,GAAG,CAAC,CAAC;AAC3B;AACA,MAAMC,MAAM,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC3B,MAAMC,MAAM,GAAGD,GAAG,CAACE,WAAW,CAACJ,MAAM,CAAC;EACtC,OAAO;IACHK,IAAI,EAAEA,CAAA,KAAOC,UAAU,IAAM,GAAEA,UAAW,oDAAmDL,KAAK,CAACM,UAAU,CAACC,OAAO,CAACC,OAAQ,iCAAgCrE,aAAa,CAAC,EAAE,CAAE,uCAAsCkE,UAAW,kEAAiEjE,IAAI,CAAG4D,KAAK,CAACE,MAAM,IAC9SF,KAAK,CAACE,MAAM,CAACE,IAAI,KAChBJ,KAAK,CAACE,MAAM,CAACE,IAAI,YAAY/D,eAAe,GAC1C2D,KAAK,CAACE,MAAM,CAACE,IAAI,CAACK,cAAc,CAACC,EAAE,IAAIA,EAAE,CAACR,MAAM,CAAC,CAAC,GAClDF,KAAK,CAACE,MAAM,CAACE,IAAI,CAACF,MAAM,CAAC,CAAC,EAAK,GAAEG,UAAW,EAAC,CAAE;EAC7D,CAAC;AACL,CAAC;AACD;AACA,MAAMM,iBAAiB,CAAC;EACpB/C,WAAWA,CAACgD,SAAS,EAAEC,OAAO,EAAEhD,WAAW,EAAEiD,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,YAAY,EAAE;IACpF,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACL,MAAM,CAACM,aAAa,CAACrB,MAAM,EAAED,gBAAgB,CAAC;IAClE;IACA,IAAI,CAACuB,yBAAyB,GAAG,IAAI;IACrC;IACA,IAAI,CAACpD,YAAY,GAAG,IAAIf,OAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAACkB,aAAa,GAAG,IAAIlB,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAACoB,YAAY,GAAG,IAAIpB,OAAO,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACoE,MAAM,GAAG,OAAO;IACrBL,SAAS,CAACM,QAAQ,CAACR,GAAG,CAACS,aAAa,EAAE,IAAI,CAACL,OAAO,CAACf,IAAI,CAAC;EAC5D;EACAqB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,2BAA2B,YAAYhG,WAAW,EAAE;MACzD,MAAMiG,OAAO,GAAG,IAAIC,eAAe,CAAC,IAAI,CAACC,YAAY,CAAC;MACtD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,gBAAgB,CACxCC,kBAAkB,CAAC,IAAI,CAACN,2BAA2B,EAAEC,OAAO,CAAC;IACtE,CAAC,MACI;MACD,IAAI,CAACM,aAAa,GAAG,IAAI,CAACF,gBAAgB,CACrCG,eAAe,CAAC,IAAI,CAACR,2BAA2B,EAAES,SAAS,EAAE,IAAI,CAACN,YAAY,CAAC;IACxF;IACA;IACA,MAAM;MAAEO;IAAe,CAAC,GAAG,IAAI,CAACP,YAAY,CAACQ,GAAG,CAAC5C,cAAc,CAAC;IAChE,IAAI2C,cAAc,EAAE;MAChB,IAAI,CAACnB,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACR,GAAG,CAACS,aAAa,EAAEY,cAAc,CAAC;IACnE;EACJ;EACAE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACrB,YAAY,CAACsB,MAAM,CAAC,IAAI,CAACzB,GAAG,CAACS,aAAa,CAAC;IAClE,IAAI,CAACiB,aAAa,CAAC,CAAC;EACxB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,UAAU,CAACI,OAAO,CAAC,CAAC;IACzB,IAAI,IAAI,CAACtB,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACuB,KAAK,CAAC,CAAC;MACtC,IAAI,CAACvB,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACA;EACAwB,KAAKA,CAACC,0BAA0B,EAAEC,WAAW,EAAE;IAC3C,IAAI,CAACrB,2BAA2B,GAAGoB,0BAA0B;IAC7D,IAAI,CAACjB,YAAY,GAAGkB,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIlE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyC,MAAM,GAAG,MAAM;IACpB,IAAI,CAACN,GAAG,CAACgC,YAAY,CAAC,CAAC;EAC3B;EACAC,iBAAiBA,CAACC,KAAK,EAAE;IACrB,IAAIA,KAAK,CAACC,OAAO,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACtF,WAAW,CAACuF,cAAc,CAAC,CAAC;IACrC;EACJ;EACA;EACAC,gBAAgBA,CAACH,KAAK,EAAE;IACpB,IAAIA,KAAK,CAACC,OAAO,KAAK,MAAM,EAAE;MAC1B,MAAMG,SAAS,GAAG,IAAI,CAACzB,YAAY,CAACQ,GAAG,CAAC1E,WAAW,CAAC;MACpD,IAAI,CAAC4F,QAAQ,CAAC,CAAC;MACf,IAAI,CAAC1F,WAAW,CAAC8E,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACrE,YAAY,CAACK,IAAI,CAAC2E,SAAS,CAAC/E,MAAM,CAAC;MACxC,IAAI,CAACD,YAAY,CAACM,QAAQ,CAAC,CAAC;IAChC,CAAC,MACI,IAAIsE,KAAK,CAACC,OAAO,KAAK,OAAO,EAAE;MAChC,IAAI,CAAClF,YAAY,CAACU,IAAI,CAAC,CAAC;MACxB,IAAI,CAACV,YAAY,CAACW,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA2E,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACtB,aAAa,EAAE;MACpB,IAAI,CAACpB,OAAO,CAAC2C,UAAU,CAAC,IAAI,CAACvB,aAAa,CAACwB,QAAQ,CAAC;MACpD,IAAI,CAACxB,aAAa,CAACU,OAAO,CAAC,CAAC;IAChC,CAAC,MACI;MACD,IAAI,CAAC9B,OAAO,CAAC2C,UAAU,CAAC,IAAI,CAAC1B,gBAAgB,CAAC;MAC9C,IAAI,CAACA,gBAAgB,CAAC4B,MAAM,CAAC,CAAC;MAC9B,IAAI,CAAC5B,gBAAgB,CAACa,OAAO,CAAC,CAAC;IACnC;EACJ;EACA;EACAgB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC5C,GAAG,CAACS,aAAa;EACjC;EACAiB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACpB,yBAAyB,GAAGlE,iCAAiC,CAAC,CAAC;IACpE,IAAI,CAACoF,UAAU,CAACqB,4BAA4B,CAAC,CAAC;EAClD;AACJ;AACAjD,iBAAiB,CAAC7B,IAAI,YAAA+E,0BAAA7E,CAAA;EAAA,YAAAA,CAAA,IAAyF2B,iBAAiB,EArJjCpF,EAAE,CAAAuI,iBAAA,CAqJiD5H,EAAE,CAACI,aAAa,GArJnEf,EAAE,CAAAuI,iBAAA,CAqJ8EvI,EAAE,CAACwI,cAAc,GArJjGxI,EAAE,CAAAuI,iBAAA,CAqJ4G5H,EAAE,CAACQ,YAAY,GArJ7HnB,EAAE,CAAAuI,iBAAA,CAqJwI5H,EAAE,CAAC8H,QAAQ,GArJrJzI,EAAE,CAAAuI,iBAAA,CAqJgKvI,EAAE,CAAC0I,UAAU,GArJ/K1I,EAAE,CAAAuI,iBAAA,CAqJ0LvI,EAAE,CAAC2I,iBAAiB,GArJhN3I,EAAE,CAAAuI,iBAAA,CAqJ2NvI,EAAE,CAAC4I,SAAS,GArJzO5I,EAAE,CAAAuI,iBAAA,CAqJoP1G,EAAE,CAACgH,gBAAgB;AAAA,CAA4C;AACpZzD,iBAAiB,CAAC0D,IAAI,kBAtJyE9I,EAAE,CAAA+I,iBAAA;EAAA9E,IAAA,EAsJEmB,iBAAiB;EAAA4D,SAAA;EAAAC,SAAA,WAAAC,wBAAAhH,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtJrBlC,EAAE,CAAAmJ,WAAA,CAwJhBhJ,WAAW,KAA2BC,gBAAgB;IAAA;IAAA,IAAA8B,EAAA;MAAA,IAAAkH,EAAA;MAxJxCpJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAAnH,GAAA,CAAAqE,gBAAA,GAAA4C,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,+BAAAxH,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFlC,EAAE,CAAA2J,uBAAA,oCAAAC,qEAAAC,MAAA;QAAA,OAsJE1H,GAAA,CAAAuF,iBAAA,CAAAmC,MAAwB,CAAC;MAAA,qCAAAC,oEAAAD,MAAA;QAAA,OAAzB1H,GAAA,CAAA2F,gBAAA,CAAA+B,MAAuB,CAAC;MAAA;IAAA;IAAA,IAAA3H,EAAA;MAtJ5BlC,EAAE,CAAA+J,uBAAA,qBAAA5H,GAAA,CAAA4D,MAAA;IAAA;EAAA;EAAAiE,QAAA,GAAFhK,EAAE,CAAAiK,kBAAA,CAsJoP,CAC7UlJ,aAAa,CAChB;EAAAmJ,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,2BAAAnI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAxJ0FlC,EAAE,CAAAsK,UAAA,IAAArI,wCAAA,qBAwJ4G,CAAC;IAAA;EAAA;EAAAsI,aAAA;EAAAC,IAAA;IAAAC,SAAA,EAA+B,CACrOnJ,OAAO,CAAC,iBAAiB,EAAE,CACvBC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC;MAAEkJ,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAa,CAAC,CAAC,CAAC,EACnEpJ,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;MAAEmJ,SAAS,EAAE;IAAO,CAAC,CAAC,CAAC,EAC5ClJ,UAAU,CAAC,YAAY,EAAEC,OAAO,CAAC,kCAAkC,EAAEF,KAAK,CAAC;MAAEmJ,SAAS,EAAE,MAAM;MAAED,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,EAC/GjJ,UAAU,CAAC,sBAAsB,EAAEC,OAAO,CAAC,qCAAqC,EAAEF,KAAK,CAAC;MAAEkJ,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAC5G,CAAC;EACL;EAAAE,eAAA;AAAA,EAAuD;AAC5D;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAhK+F/D,EAAE,CAAAgE,iBAAA,CAgKLoB,iBAAiB,EAAc,CAAC;IAChHnB,IAAI,EAAE5D,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BV,QAAQ,EAAE,6BAA6B;MACvCQ,eAAe,EAAEtK,uBAAuB,CAACyK,MAAM;MAC/CC,UAAU,EAAE,CACR1J,OAAO,CAAC,iBAAiB,EAAE,CACvBC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC;QAAEkJ,OAAO,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAa,CAAC,CAAC,CAAC,EACnEpJ,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;QAAEmJ,SAAS,EAAE;MAAO,CAAC,CAAC,CAAC,EAC5ClJ,UAAU,CAAC,YAAY,EAAEC,OAAO,CAAC,kCAAkC,EAAEF,KAAK,CAAC;QAAEmJ,SAAS,EAAE,MAAM;QAAED,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,EAC/GjJ,UAAU,CAAC,sBAAsB,EAAEC,OAAO,CAAC,qCAAqC,EAAEF,KAAK,CAAC;QAAEkJ,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAC5G,CAAC,CACL;MACDO,IAAI,EAAE;QACF,oBAAoB,EAAE,QAAQ;QAC9B,0BAA0B,EAAE,2BAA2B;QACvD,yBAAyB,EAAE;MAC/B,CAAC;MACDC,SAAS,EAAE,CACPnK,aAAa;IAErB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEkD,IAAI,EAAEtD,EAAE,CAACI;IAAc,CAAC,EAAE;MAAEkD,IAAI,EAAEjE,EAAE,CAACwI;IAAe,CAAC,EAAE;MAAEvE,IAAI,EAAEtD,EAAE,CAACQ;IAAa,CAAC,EAAE;MAAE8C,IAAI,EAAEtD,EAAE,CAAC8H;IAAS,CAAC,EAAE;MAAExE,IAAI,EAAEjE,EAAE,CAAC0I;IAAW,CAAC,EAAE;MAAEzE,IAAI,EAAEjE,EAAE,CAAC2I;IAAkB,CAAC,EAAE;MAAE1E,IAAI,EAAEjE,EAAE,CAAC4I;IAAU,CAAC,EAAE;MAAE3E,IAAI,EAAEpC,EAAE,CAACgH;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAErC,gBAAgB,EAAE,CAAC;MACtSvC,IAAI,EAAE1D,SAAS;MACfsK,IAAI,EAAE,CAAC1K,WAAW,EAAE;QAAEgL,IAAI,EAAE/K,gBAAgB;QAAEgL,MAAM,EAAE;MAAK,CAAC;IAChE,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM/E,eAAe,CAAC;EAClBhE,WAAWA,CAACgJ,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI,CAACD,SAAS,CAACvE,GAAG,CAAC1E,WAAW,CAAC;IAChD,IAAI,CAAC2F,SAAS,GAAG,IAAI,CAACsD,SAAS,CAACvE,GAAG,CAAC1E,WAAW,CAAC;EACpD;EACA,IAAIoI,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACa,SAAS,CAACvE,GAAG,CAACxC,cAAc,CAAC;EAC7C;AACJ;AAEA,MAAMiH,eAAe,CAAC;EAClBlJ,WAAWA,CAACiE,YAAY,EAAEkF,eAAe,EAAE;IACvC,IAAI,CAAClF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACkF,eAAe,GAAGA,eAAe;EAC1C;EACA1E,GAAGA,CAACjD,KAAK,EAAE4H,aAAa,EAAEC,MAAM,EAAE;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACrF,YAAY,CAACQ,GAAG,CAACjD,KAAK,EAAE4H,aAAa,CAAC;IACzD,IAAIE,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACH,eAAe,CAAC1E,GAAG,CAACjD,KAAK,EAAE4H,aAAa,CAAC;EACzD;AACJ;AAEA,MAAMG,8BAA8B,GAAG,CACnC,OAAO,EACP,UAAU,EACV,UAAU,EACV,QAAQ,EACR,WAAW,EACX,WAAW,CACd;AACD,MAAMC,QAAQ,CAAC;EACXxJ,WAAWA,CAACyJ,QAAQ,EAAEC,yBAAyB,EAAExG,MAAM,EAAE8F,SAAS,EAAE;IAChE,IAAI,CAACS,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACxG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC8F,SAAS,GAAGA,SAAS;EAC9B;EACAW,IAAIA,CAACC,sBAAsB,EAAEC,MAAM,EAAE;IACjC;IACAA,MAAM,GAAG;MAAE,GAAG,IAAIhI,cAAc,CAAC,CAAC;MAAE,GAAGgI;IAAO,CAAC;IAC/C,IAAI3E,0BAA0B;IAC9B,IAAI0E,sBAAsB,YAAY9L,WAAW,EAAE;MAC/CoH,0BAA0B,GAAG0E,sBAAsB;IACvD,CAAC,MACI;MACD1E,0BAA0B,GAAG,IAAI,CAACwE,yBAAyB,CAACI,uBAAuB,CAACF,sBAAsB,CAAC;IAC/G;IACA,MAAMG,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACP,QAAQ,CAAC7E,MAAM,CAAC7B,iBAAiB,EAAE,IAAI,EAAE;MAC7DkH,MAAM,EAAE;QACJC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE;MACZ,CAAC;MACDrI,WAAW,EAAE6H,MAAM,CAAC7H,WAAW;MAC/BwD,cAAc,EAAEuE,IAAI;MACpBO,YAAY,EAAET,MAAM,CAACS,YAAY;MACjCC,aAAa,EAAEV,MAAM,CAACU,aAAa,IAAI,IAAI,CAACrH,MAAM,CAAC/D,KAAK,CAACR,oBAAoB,CAAC;MAC9E6L,SAAS,EAAEA,CAAA,KAAM;QACb9E,SAAS,CAAC7E,KAAK,CAAC,CAAC;QACjB4J,wBAAwB,CAACC,WAAW,CAAC,CAAC;MAC1C;IACJ,CAAC,CAAC;IACF,MAAMC,aAAa,GAAGX,UAAU,CAACW,aAAa,CAAC,CAAC;IAChD,MAAMF,wBAAwB,GAAGE,aAAa,CAACC,SAAS,CAAEtF,KAAK,IAAK;MAChE,IAAI,CAAEuE,MAAM,EAAES,YAAa,IAAIhF,KAAK,CAACuF,OAAO,KAAKnL,MAAM,EAAE;QACrDgG,SAAS,CAAC7E,KAAK,CAAC,CAAC;MACrB;IACJ,CAAC,CAAC;IACF,MAAMT,QAAQ,GAAG4J,UAAU,CAAC7J,YAAY,CAACC,QAAQ;IACjDmJ,8BAA8B,CAACuB,OAAO,CAACC,QAAQ,IAAI;MAC/C,IAAIlB,MAAM,CAACkB,QAAQ,CAAC,EAAE;QAClBnM,WAAW,CAACwB,QAAQ,EAAE;UAAE4K,GAAG,EAAED,QAAQ;UAAEE,CAAC,EAAEpM,OAAO,CAACoM;QAAE,CAAC,EAAEpB,MAAM,CAACkB,QAAQ,CAAC,EAAElM,OAAO,CAACkM,QAAQ,CAAC,EAAElM,OAAO,CAACqM,SAAS,CAAC;MAClH;IACJ,CAAC,CAAC;IACF,MAAMrC,SAAS,GAAG,CACd;MACIsC,OAAO,EAAEpL,WAAW;MACpBqL,QAAQ,EAAE,IAAIrL,WAAW,CAACiK,UAAU,CAAC7J,YAAY,CAACkL,QAAQ,CAAC5G,GAAG,CAAC3F,YAAY,CAAC;IAChF,CAAC,EACD;MACIqM,OAAO,EAAEtJ,cAAc;MACvBuJ,QAAQ,EAAEvB;IACd,CAAC,CACJ;IACD,IAAIA,MAAM,CAAC1B,IAAI,IAAI,IAAI,EAAE;MACrBU,SAAS,CAACyC,IAAI,CAAC;QACXH,OAAO,EAAElJ,cAAc;QACvBmJ,QAAQ,EAAEvB,MAAM,CAAC1B;MACrB,CAAC,CAAC;IACN;IACA,MAAMhD,WAAW,GAAG,IAAI+D,eAAe,CAAC/K,QAAQ,CAACyG,MAAM,CAACiE,SAAS,EAAEmB,UAAU,CAAC7J,YAAY,CAACkL,QAAQ,CAAC,EAAE,IAAI,CAACrC,SAAS,CAAC;IACrH5I,QAAQ,CAAC6E,KAAK,CAACC,0BAA0B,EAAEC,WAAW,CAAC;IACvD,MAAMO,SAAS,GAAGP,WAAW,CAACV,GAAG,CAAC1E,WAAW,CAAC;IAC9C,OAAO2F,SAAS;EACpB;AACJ;AACA8D,QAAQ,CAACtI,IAAI,YAAAqK,iBAAAnK,CAAA;EAAA,YAAAA,CAAA,IAAyFoI,QAAQ,EAhSf7L,EAAE,CAAA0D,QAAA,CAgS+B/C,EAAE,CAACkN,SAAS,GAhS7C7N,EAAE,CAAA0D,QAAA,CAgSwD1D,EAAE,CAAC8N,wBAAwB,GAhSrF9N,EAAE,CAAA0D,QAAA,CAgSgG/C,EAAE,CAAC8H,QAAQ,GAhS7GzI,EAAE,CAAA0D,QAAA,CAgSwH1D,EAAE,CAACQ,QAAQ;AAAA,CAA6C;AACjRqL,QAAQ,CAAClI,KAAK,kBAjSiF3D,EAAE,CAAA4D,kBAAA;EAAAC,KAAA,EAiSSgI,QAAQ;EAAA/H,OAAA,EAAR+H,QAAQ,CAAAtI;AAAA,EAAG;AACrH;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAlS+F/D,EAAE,CAAAgE,iBAAA,CAkSL6H,QAAQ,EAAc,CAAC;IACvG5H,IAAI,EAAEhE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgE,IAAI,EAAEtD,EAAE,CAACkN;IAAU,CAAC,EAAE;MAAE5J,IAAI,EAAEjE,EAAE,CAAC8N;IAAyB,CAAC,EAAE;MAAE7J,IAAI,EAAEtD,EAAE,CAAC8H;IAAS,CAAC,EAAE;MAAExE,IAAI,EAAEjE,EAAE,CAACQ;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjK;AACA,MAAMuN,gBAAgB,GAAG,CAAC,CAAC;AAC3B;AACA,MAAMC,mBAAmB,GAAIvJ,KAAK,IAAMK,UAAU,IAAM,GAAEA,UAAW,8HAA6HL,KAAK,CAACwJ,UAAU,CAACC,UAAW,IAAG;AACjO,MAAMC,aAAa,CAAC;EAChB9L,WAAWA,CAACqD,SAAS,EAAEF,GAAG,EAAED,MAAM,EAAE;IAChC,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACAW,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACR,GAAG,CAACS,aAAa,EAAE,IAAI,CAACV,MAAM,CAAC6I,WAAW,CAACJ,mBAAmB,EAAED,gBAAgB,CAAC,CAAC;EACnH;AACJ;AACAI,aAAa,CAAC5K,IAAI,YAAA8K,sBAAA5K,CAAA;EAAA,YAAAA,CAAA,IAAyF0K,aAAa,EApTzBnO,EAAE,CAAAuI,iBAAA,CAoTyCvI,EAAE,CAAC4I,SAAS,GApTvD5I,EAAE,CAAAuI,iBAAA,CAoTkEvI,EAAE,CAAC0I,UAAU,GApTjF1I,EAAE,CAAAuI,iBAAA,CAoT4F5H,EAAE,CAAC8H,QAAQ;AAAA,CAA4C;AACpP0F,aAAa,CAACG,IAAI,kBArT6EtO,EAAE,CAAAuO,iBAAA;EAAAtK,IAAA,EAqTFkK,aAAa;EAAAnF,SAAA;EAAAwF,QAAA;AAAA,EAA8F;AAC1M;EAAA,QAAAzK,SAAA,oBAAAA,SAAA,KAtT+F/D,EAAE,CAAAgE,iBAAA,CAsTLmK,aAAa,EAAc,CAAC;IAC5GlK,IAAI,EAAExD,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9C0D,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvK,IAAI,EAAEjE,EAAE,CAAC4I;IAAU,CAAC,EAAE;MAAE3E,IAAI,EAAEjE,EAAE,CAAC0I;IAAW,CAAC,EAAE;MAAEzE,IAAI,EAAEtD,EAAE,CAAC8H;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE5H;AACA,MAAMgG,gBAAgB,GAAG,CAAC,CAAC;AAC3B;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAO5J,UAAU,IAAM,GAAEA,UAAW,qGAAoG;AACtK,MAAM6J,eAAe,CAAC;EAClBtM,WAAWA,CAACqD,SAAS,EAAEF,GAAG,EAAED,MAAM,EAAE;IAChC,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACAW,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACR,GAAG,CAACS,aAAa,EAAE,IAAI,CAACV,MAAM,CAAC6I,WAAW,CAACM,qBAAqB,EAAED,gBAAgB,CAAC,CAAC;EACrH;AACJ;AACAE,eAAe,CAACpL,IAAI,YAAAqL,wBAAAnL,CAAA;EAAA,YAAAA,CAAA,IAAyFkL,eAAe,EA5U7B3O,EAAE,CAAAuI,iBAAA,CA4U6CvI,EAAE,CAAC4I,SAAS,GA5U3D5I,EAAE,CAAAuI,iBAAA,CA4UsEvI,EAAE,CAAC0I,UAAU,GA5UrF1I,EAAE,CAAAuI,iBAAA,CA4UgG5H,EAAE,CAAC8H,QAAQ;AAAA,CAA4C;AACxPkG,eAAe,CAACL,IAAI,kBA7U2EtO,EAAE,CAAAuO,iBAAA;EAAAtK,IAAA,EA6UA0K,eAAe;EAAA3F,SAAA;EAAAwF,QAAA;AAAA,EAAuH;AACvO;EAAA,QAAAzK,SAAA,oBAAAA,SAAA,KA9U+F/D,EAAE,CAAAgE,iBAAA,CA8UL2K,eAAe,EAAc,CAAC;IAC9G1K,IAAI,EAAExD,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2DAA2D;MACrE0D,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvK,IAAI,EAAEjE,EAAE,CAAC4I;IAAU,CAAC,EAAE;MAAE3E,IAAI,EAAEjE,EAAE,CAAC0I;IAAW,CAAC,EAAE;MAAEzE,IAAI,EAAEtD,EAAE,CAAC8H;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE5H;AACA,MAAMoG,cAAc,GAAG,CAAC,CAAC;AACzB;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAOhK,UAAU,IAAM,GAAEA,UAAW,6FAA4F;AAC9J,MAAMiK,eAAe,CAAC;EAClB1M,WAAWA,CAACqD,SAAS,EAAEF,GAAG,EAAED,MAAM,EAAE;IAChC,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACAW,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACR,GAAG,CAACS,aAAa,EAAE,IAAI,CAACV,MAAM,CAAC6I,WAAW,CAACU,qBAAqB,EAAED,cAAc,CAAC,CAAC;EACnH;AACJ;AACAE,eAAe,CAACxL,IAAI,YAAAyL,wBAAAvL,CAAA;EAAA,YAAAA,CAAA,IAAyFsL,eAAe,EApW7B/O,EAAE,CAAAuI,iBAAA,CAoW6CvI,EAAE,CAAC4I,SAAS,GApW3D5I,EAAE,CAAAuI,iBAAA,CAoWsEvI,EAAE,CAAC0I,UAAU,GApWrF1I,EAAE,CAAAuI,iBAAA,CAoWgG5H,EAAE,CAAC8H,QAAQ;AAAA,CAA4C;AACxPsG,eAAe,CAACT,IAAI,kBArW2EtO,EAAE,CAAAuO,iBAAA;EAAAtK,IAAA,EAqWA8K,eAAe;EAAA/F,SAAA;EAAAwF,QAAA;AAAA,EAAuH;AACvO;EAAA,QAAAzK,SAAA,oBAAAA,SAAA,KAtW+F/D,EAAE,CAAAgE,iBAAA,CAsWL+K,eAAe,EAAc,CAAC;IAC9G9K,IAAI,EAAExD,SAAS;IACfoK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2DAA2D;MACrE0D,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvK,IAAI,EAAEjE,EAAE,CAAC4I;IAAU,CAAC,EAAE;MAAE3E,IAAI,EAAEjE,EAAE,CAAC0I;IAAW,CAAC,EAAE;MAAEzE,IAAI,EAAEtD,EAAE,CAAC8H;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;AAE5H,MAAMwG,cAAc,CAAC;AAErBA,cAAc,CAAC1L,IAAI,YAAA2L,uBAAAzL,CAAA;EAAA,YAAAA,CAAA,IAAyFwL,cAAc;AAAA,CAAkD;AAC5KA,cAAc,CAACE,IAAI,kBAjX4EnP,EAAE,CAAAoP,gBAAA;EAAAnL,IAAA,EAiXYgL;AAAc,EAUhG;AAC3BA,cAAc,CAACI,IAAI,kBA5X4ErP,EAAE,CAAAsP,gBAAA;EAAApE,SAAA,EA4XuC,CAChIW,QAAQ,CACX;EAAA0D,OAAA,GAAY,CACLvN,YAAY,EACZZ,cAAc,EACdC,eAAe,EACfS,UAAU,CACb,EAAEV,cAAc;AAAA,EAAI;AAC7B;EAAA,QAAA2C,SAAA,oBAAAA,SAAA,KApY+F/D,EAAE,CAAAgE,iBAAA,CAoYLiL,cAAc,EAAc,CAAC;IAC7GhL,IAAI,EAAEvD,QAAQ;IACdmK,IAAI,EAAE,CAAC;MACC2E,YAAY,EAAE,CACVpK,iBAAiB,EACjB+I,aAAa,EACbQ,eAAe,EACfI,eAAe,CAClB;MACDQ,OAAO,EAAE,CACLvN,YAAY,EACZZ,cAAc,EACdC,eAAe,EACfS,UAAU,CACb;MACD2N,OAAO,EAAE,CACLrO,cAAc,EACdgE,iBAAiB,EACjB+I,aAAa,EACbQ,eAAe,EACfI,eAAe,CAClB;MACD7D,SAAS,EAAE,CACPW,QAAQ;IAEhB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASvH,cAAc,EAAEuH,QAAQ,EAAEkD,eAAe,EAAE3J,iBAAiB,EAAEuJ,eAAe,EAAEtI,eAAe,EAAE4I,cAAc,EAAE7M,WAAW,EAAE+L,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
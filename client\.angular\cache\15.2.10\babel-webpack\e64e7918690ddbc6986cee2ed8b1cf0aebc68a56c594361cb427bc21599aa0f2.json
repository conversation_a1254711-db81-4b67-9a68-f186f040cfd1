{"ast": null, "code": "export function isScheduler(value) {\n  return value && typeof value.schedule === 'function';\n}", "map": {"version": 3, "names": ["isScheduler", "value", "schedule"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isScheduler.js"], "sourcesContent": ["export function isScheduler(value) {\n    return value && typeof value.schedule === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,QAAQ,KAAK,UAAU;AACxD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
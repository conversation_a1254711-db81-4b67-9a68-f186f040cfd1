{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { AppConfig } from 'app/app-config';\nimport { DataTableDirective } from 'angular-datatables';\nimport { Subject } from 'rxjs';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/stage.service\";\nimport * as i2 from \"app/services/user.service\";\nimport * as i3 from \"app/services/auth.service\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/flex-layout/extended\";\nimport * as i10 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i11 from \"angular-datatables\";\nimport * as i12 from \"../../../../components/editor-sidebar/editor-sidebar.component\";\nfunction StageTablesComponent_div_2_th_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 16);\n    i0.ɵɵtext(1, \" Action \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 25);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 26)(1, \"div\", 27)(2, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const stageTeam_r11 = i0.ɵɵnextContext().$implicit;\n      const group_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.updateOrder(group_r5.name, stageTeam_r11.team_id, \"up\"));\n    });\n    i0.ɵɵelement(3, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const stageTeam_r11 = i0.ɵɵnextContext().$implicit;\n      const group_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.updateOrder(group_r5.name, stageTeam_r11.team_id, \"down\"));\n    });\n    i0.ɵɵelement(5, \"i\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    const i_r12 = ctx_r23.index;\n    const stageTeam_r11 = ctx_r23.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r15.showButton(i_r12, stageTeam_r11.team_id, \"up\") ? \"visible\" : \"invisible\")(\"disabled\", !ctx_r15.canUpdateLeaderboard);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r15.showButton(i_r12, stageTeam_r11.team_id, \"down\") ? \"visible\" : \"invisible\")(\"disabled\", !ctx_r15.canUpdateLeaderboard);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 18)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, StageTablesComponent_div_2_ng_container_32_tr_1_i_5_Template, 1, 0, \"i\", 19);\n    i0.ɵɵtemplate(6, StageTablesComponent_div_2_ng_container_32_tr_1_i_6_Template, 1, 0, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelement(8, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 22);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 11);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 11);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 22);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template, 6, 4, \"td\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stageTeam_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const group_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i_r12 + 1, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) && (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) < 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) && (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", stageTeam_r11.team.club.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.team.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.no_matches);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", stageTeam_r11.no_wins, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", stageTeam_r11.no_draws, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", stageTeam_r11.no_losses, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.goals_for);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.goals_against);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.points);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.showUpdateButton);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StageTablesComponent_div_2_ng_container_32_tr_1_Template, 26, 13, \"tr\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const group_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.groupData[group_r5.name]);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    group: a0\n  };\n};\nfunction StageTablesComponent_div_2_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 32);\n  }\n  if (rf & 2) {\n    const group_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, group_r5));\n  }\n}\nfunction StageTablesComponent_div_2_div_34_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1, \" You cannot update the leaderboard once the knockout stage has started. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StageTablesComponent_div_2_div_34_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function StageTablesComponent_div_2_div_34_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const group_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.submitOrder(group_r5.name));\n    });\n    i0.ɵɵtext(1, \" Submit \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r28.canUpdateLeaderboard);\n  }\n}\nfunction StageTablesComponent_div_2_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, StageTablesComponent_div_2_div_34_p_1_Template, 2, 0, \"p\", 34);\n    i0.ɵɵtemplate(2, StageTablesComponent_div_2_div_34_button_2_Template, 2, 1, \"button\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.canUpdateLeaderboard === false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.stage && ctx_r9.stage.type !== ctx_r9.AppConfig.TOURNAMENT_TYPES.knockout);\n  }\n}\nfunction StageTablesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 5)(2, \"div\", 6)(3, \"h4\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"div\", 8);\n    i0.ɵɵelementStart(6, \"div\", 9)(7, \"table\", 10)(8, \"thead\")(9, \"tr\")(10, \"th\");\n    i0.ɵɵtext(11, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Logo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"P\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 11);\n    i0.ɵɵtext(19, \"W\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 11);\n    i0.ɵɵtext(21, \"D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 11);\n    i0.ɵɵtext(23, \"L\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\");\n    i0.ɵɵtext(27, \"A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\");\n    i0.ɵɵtext(29, \"Pts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, StageTablesComponent_div_2_th_30_Template, 2, 0, \"th\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"tbody\");\n    i0.ɵɵtemplate(32, StageTablesComponent_div_2_ng_container_32_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵtemplate(33, StageTablesComponent_div_2_ng_container_33_Template, 1, 4, \"ng-container\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(34, StageTablesComponent_div_2_div_34_Template, 3, 2, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"col  \", ctx_r0.tableData.length > 1 ? \"col-lg-6\" : \"\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(group_r5.name);\n    i0.ɵɵadvance(26);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showUpdateButton && ctx_r0.stage && ctx_r0.stage.type !== ctx_r0.AppConfig.TOURNAMENT_TYPES.knockout);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.stage && ctx_r0.stage.type !== ctx_r0.AppConfig.TOURNAMENT_TYPES.knockout);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.stage || ctx_r0.stage && ctx_r0.stage.type === ctx_r0.AppConfig.TOURNAMENT_TYPES.knockout);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showUpdateButton);\n  }\n}\nfunction StageTablesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 5)(2, \"div\", 6)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"table\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 3, \"Point Adjustment\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"dtOptions\", ctx_r1.dtOptions)(\"dtTrigger\", ctx_r1.dtTrigger);\n  }\n}\nfunction StageTablesComponent_core_sidebar_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"core-sidebar\", 40)(1, \"app-editor-sidebar\", 41);\n    i0.ɵɵlistener(\"onSuccess\", function StageTablesComponent_core_sidebar_4_Template_app_editor_sidebar_onSuccess_1_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onSuccess($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"name\", ctx_r2.table_name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"table\", ctx_r2.dtElement)(\"fields\", ctx_r2.fields)(\"params\", ctx_r2.params)(\"paramsToPost\", ctx_r2.paramsToPost)(\"fields_subject\", ctx_r2.fields_subject);\n  }\n}\nfunction StageTablesComponent_ng_template_5_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 11);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 22);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stageTeam_r36 = ctx.$implicit;\n    const i_r37 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r37 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", stageTeam_r36.team.club.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.team.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_matches);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_wins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_draws);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_losses);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.goals_for);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.goals_against);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.points);\n  }\n}\nfunction StageTablesComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, StageTablesComponent_ng_template_5_tr_0_Template, 21, 10, \"tr\", 17);\n  }\n  if (rf & 2) {\n    const group_r34 = ctx.group;\n    i0.ɵɵproperty(\"ngForOf\", group_r34.teams);\n  }\n}\nexport class StageTablesComponent {\n  set tableData(value) {\n    this._tableData = value;\n    // this.makeTeamData();\n  }\n\n  get tableData() {\n    return this._tableData;\n  }\n  constructor(stageService, _userService, _authService, _commonsService, _http, _translateService, _coreSidebarService) {\n    this.stageService = stageService;\n    this._userService = _userService;\n    this._authService = _authService;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this.AppConfig = AppConfig;\n    this.dtElement = DataTableDirective;\n    this.onDataChange = new EventEmitter();\n    this.showUpdateButton = true;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.table_name = 'adjust-point-table';\n    this.fields_subject = new Subject();\n    this.paramsToPost = {};\n    this.fields = [{\n      key: 'stage_team_id',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Team'),\n        placeholder: this._translateService.instant('Select team'),\n        required: true,\n        options: []\n      }\n    }, {\n      key: 'user_id',\n      type: 'input',\n      props: {\n        required: true,\n        type: 'hidden'\n      },\n      defaultValue: this._authService.currentUserValue.id\n    }, {\n      key: 'points',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Points'),\n        placeholder: this._translateService.instant('Enter points'),\n        required: true,\n        type: 'number',\n        max: 1000\n      }\n    }, {\n      key: 'reason',\n      type: 'textarea',\n      props: {\n        label: this._translateService.instant('Reason'),\n        placeholder: this._translateService.instant('Enter reason'),\n        required: true\n      }\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Add adjustment point'),\n        edit: this._translateService.instant('Edit point'),\n        remove: this._translateService.instant('Remove')\n      },\n      url: `${environment.apiUrl}/adjustment-points/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.mapEqual = new Map();\n    this.orderChange = new Map();\n    this.canUpdateLeaderboard = null;\n    this.groupData = [];\n    this.alert = alert;\n  }\n  ngOnInit() {\n    if (this.stage) {\n      if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n        this.initAdjustmentsPointTable();\n      }\n      this.stageService.checkCanUpdateLeaderboard(this.stage.id).subscribe(response => {\n        console.log(response);\n        this.canUpdateLeaderboard = response;\n      });\n      this.makeTeamData();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['showUpdateButton'] && !changes['showUpdateButton'].firstChange) {\n      console.log(\"🚀 ~ StageTablesComponent ~ ngOnChanges ~ changes['showUpdateButton'].currentValue:\", changes['showUpdateButton'].currentValue);\n      this.showUpdateButton = changes['showUpdateButton'].currentValue;\n    }\n    if (changes['tableData'] && !changes['tableData'].firstChange) {\n      if (this.showUpdateButton !== false) {\n        this.makeTeamData();\n      }\n    }\n  }\n  makeTeamData() {\n    if (!this.showUpdateButton) {\n      return;\n    }\n    if (!this.showUpdateButton) {\n      return;\n    }\n    if (this.tableData && this.stage.type !== AppConfig.TOURNAMENT_TYPES.knockout) {\n      this.tableData.forEach(group => {\n        this.groupData[group.name] = group.teams;\n      });\n    } else {\n      this.groupData = this.tableData;\n    }\n    this.mapTeamsEqualPoint();\n  }\n  showButton(index, teamId, buttonDirection) {\n    if (buttonDirection === 'up' && index === 0 || buttonDirection === 'down' && index === this.groupData.length - 1) {\n      return false;\n    }\n    const equalTeams = this.mapEqual.get(teamId);\n    if (equalTeams) {\n      return equalTeams[buttonDirection];\n    }\n    return false;\n  }\n  checkCanChangeOrder(equalPointTeams, team, rankingCriteria) {\n    if (!equalPointTeams?.length) return {\n      teamCanUp: null,\n      teamCanDown: null\n    };\n    let filteredTeams = [...equalPointTeams];\n    const currentTotalPoints = team.points;\n    const compareStep = (teams, key) => {\n      return teams.filter(t => t[key] === team[key]);\n    };\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.head_to_head) {\n      filteredTeams = compareStep(filteredTeams, '_h2h_points');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_difference');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\n    }\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.total) {\n      filteredTeams = compareStep(filteredTeams, 'goals_difference');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_points');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\n    }\n    const teamCanUp = filteredTeams.filter(t => t.order < team.order);\n    const teamCanDown = filteredTeams.filter(t => t.order > team.order);\n    return {\n      teamCanUp: teamCanUp.length > 0 ? teamCanUp[teamCanUp.length - 1] : null,\n      teamCanDown: teamCanDown.length > 0 ? teamCanDown[0] : null\n    };\n  }\n  mapTeamsEqualPoint() {\n    this.mapEqual.clear();\n    Object.values(this.groupData).forEach(group => {\n      group.forEach(team => {\n        const equalPointTeams = group.filter(t => t.points === team.points && t.no_matches === team.no_matches && t.no_wins === team.no_wins && t.no_draws === team.no_draws && t.no_losses === team.no_losses && t.goals_for === team.goals_for && t.goals_against === team.goals_against && t.goals_difference === team.goals_difference && t.team_id !== team.team_id);\n        const {\n          teamCanUp,\n          teamCanDown\n        } = this.checkCanChangeOrder(equalPointTeams, team, this.stage.ranking_criteria);\n        this.mapEqual.set(team.team_id, {\n          up: teamCanUp,\n          down: teamCanDown\n        });\n      });\n    });\n    console.log('this.mapEqual', this.mapEqual);\n    return this.mapEqual;\n  }\n  updateOrder(groupName, teamId, direction) {\n    const equalTeams = this.mapEqual.get(teamId);\n    if (equalTeams) {\n      const team = equalTeams[direction];\n      const teamIndex = this.groupData[groupName].findIndex(t => t.team_id === team.team_id);\n      const currentTeamIndex = this.groupData[groupName].findIndex(t => t.team_id === teamId);\n      if (teamIndex !== -1 && currentTeamIndex !== -1) {\n        this.updateOrderChange(groupName, this.groupData[groupName][currentTeamIndex].team_id, direction);\n        this.updateOrderChange(groupName, this.groupData[groupName][teamIndex].team_id, direction === 'up' ? 'down' : 'up');\n        // Swap the order of the two teams\n        const tempOrder = this.groupData[groupName][teamIndex].order;\n        this.groupData[groupName][teamIndex].order = this.groupData[groupName][currentTeamIndex].order;\n        this.groupData[groupName][currentTeamIndex].order = tempOrder;\n        this.groupData[groupName] = this.groupData[groupName].sort((a, b) => a.order - b.order);\n        // Update the map\n        this.mapTeamsEqualPoint();\n      }\n    }\n    ;\n  }\n  updateOrderChange(groupName, teamId, direction) {\n    if (!this.orderChange.get(groupName)) {\n      this.orderChange.set(groupName, new Map());\n    }\n    const currentTeamOrderChange = this.orderChange.get(groupName).get(teamId) || 0;\n    if (direction === 'up') {\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange + 1);\n    } else if (direction === 'down') {\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange - 1);\n    }\n  }\n  makeSubmitOrderData(groupName) {\n    return this.groupData[groupName].map(team => ({\n      team_id: team.team_id,\n      order: team.order,\n      stage_id: this.stage.id\n    }));\n  }\n  submitOrder(groupName) {\n    Swal.fire({\n      title: this._translateService.instant('Submit new Leaderboard'),\n      text: this._translateService.instant('Are you sure you want to submit the new Leaderboard?'),\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Submit'),\n      cancelButtonText: this._translateService.instant('No'),\n      icon: 'warning',\n      reverseButtons: true\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.stageService.submitTeamOrder(this.makeSubmitOrderData(groupName)).subscribe(response => {\n          console.log('Order submitted successfully:', response);\n          Swal.fire({\n            title: this._translateService.instant('Success'),\n            text: this._translateService.instant('The new Leaderboard has been submitted successfully.'),\n            icon: 'success',\n            confirmButtonText: this._translateService.instant('OK')\n          });\n          this.orderChange.get(groupName).clear();\n          this.stageService.checkMatchScore(this.stage.id).subscribe();\n        }, error => {\n          console.error('Error submitting order:', error);\n          Swal.fire({\n            title: this._translateService.instant('Error'),\n            text: this._translateService.instant(error.message),\n            icon: 'error',\n            confirmButtonText: this._translateService.instant('OK')\n          });\n        });\n      }\n    });\n  }\n  editor(action, row) {\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  onSuccess($event) {\n    this.onDataChange.emit($event);\n    this.makeTeamData();\n  }\n  initAdjustmentsPointTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        // add season id\n        this._http.post(`${environment.apiUrl}/adjustment-points/all/${this.stage.id}`, dataTablesParameters).subscribe(resp => {\n          this.fields[0].props.options = resp.options.teams;\n          this.fields_subject.next(this.fields);\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [],\n      columns: [{\n        data: 'id',\n        visible: false\n      }, {\n        title: this._translateService.instant('Team'),\n        data: 'team.name'\n      }, {\n        title: this._translateService.instant('Points'),\n        data: 'points'\n      }, {\n        title: this._translateService.instant('Reason'),\n        data: 'reason'\n      }, {\n        title: this._translateService.instant('Updated by'),\n        data: 'user_id',\n        render: (data, type, row) => {\n          return this._userService.fullName(row.user);\n        }\n      }],\n      lengthMenu: [[25, 50, 100, -1], [25, 50, 100, 'All']],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant('Add')}`,\n          action: (e, dt, node, config) => {\n            this.editor('create');\n          }\n        }, {\n          text: `<i class=\"fa-solid fa-edit\"></i> ${this._translateService.instant('Edit')}`,\n          extend: 'selectedSingle',\n          action: (e, dt, node, config) => {\n            this.editor('edit');\n          }\n        }, {\n          text: `<i class=\"fa-solid fa-trash\"></i> ${this._translateService.instant('Remove')}`,\n          extend: 'selected',\n          action: (e, dt, node, config) => {\n            this.editor('remove');\n          }\n        }]\n      }\n    };\n  }\n  ngAfterViewInit() {\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n      setTimeout(() => {\n        this.dtTrigger.next(this.dtOptions);\n      }, 1000);\n    }\n  }\n  ngOnDestroy() {\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n      this.dtTrigger.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function StageTablesComponent_Factory(t) {\n    return new (t || StageTablesComponent)(i0.ɵɵdirectiveInject(i1.StageService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.CoreSidebarService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageTablesComponent,\n    selectors: [[\"stage-tables\"]],\n    viewQuery: function StageTablesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      showUpdateButton: \"showUpdateButton\",\n      tableData: \"tableData\",\n      stage: \"stage\"\n    },\n    outputs: {\n      onDataChange: \"onDataChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 7,\n    vars: 3,\n    consts: [[1, \"row\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-lg-6\", 4, \"ngIf\"], [\"class\", \"modal modal-slide-in sidebar-todo-modal fade\", \"overlayClass\", \"modal-backdrop\", 3, \"name\", 4, \"ngIf\"], [\"notLeagueTemplate\", \"\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-body\"], [1, \"table-responsive\"], [1, \"table\"], [1, \"d-none\", \"d-sm-table-cell\"], [\"class\", \"text-center\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", 4, \"ngIf\"], [\"class\", \"mt-1 mr-1 mb-3 d-flex flex-column align-items-end\", 4, \"ngIf\"], [1, \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", 2, \"gap\", \"4px\"], [\"class\", \"text-danger bi bi-caret-down-fill\", 4, \"ngIf\"], [\"class\", \"text-success bi bi-caret-up-fill\", 4, \"ngIf\"], [\"alt\", \"logo\", \"width\", \"30\", \"height\", \"30\", 3, \"src\"], [1, \"font-weight-bold\"], [\"class\", \"font-weight-bold text-center\", 4, \"ngIf\"], [1, \"text-danger\", \"bi\", \"bi-caret-down-fill\"], [1, \"text-success\", \"bi\", \"bi-caret-up-fill\"], [1, \"font-weight-bold\", \"text-center\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"gap\", \"4px\"], [1, \"btn\", \"btn-outline-success\", 2, \"padding\", \"4px\", 3, \"ngClass\", \"disabled\", \"click\"], [1, \"text-success\", \"bi\", \"bi-arrow-up\"], [1, \"btn\", \"btn-outline-danger\", 2, \"padding\", \"4px\", 3, \"ngClass\", \"disabled\", \"click\"], [1, \"text-danger\", \"bi\", \"bi-arrow-down\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"mt-1\", \"mr-1\", \"mb-3\", \"d-flex\", \"flex-column\", \"align-items-end\"], [\"class\", \"h5 mw-100 badge badge-light-warning px-2 py-1\", \"style\", \"font-size: 12px\", 4, \"ngIf\"], [\"class\", \"btn btn-primary mw-100\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"h5\", \"mw-100\", \"badge\", \"badge-light-warning\", \"px-2\", \"py-1\", 2, \"font-size\", \"12px\"], [1, \"btn\", \"btn-primary\", \"mw-100\", 3, \"disabled\", \"click\"], [1, \"col-lg-6\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"paramsToPost\", \"fields_subject\", \"onSuccess\"]],\n    template: function StageTablesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n        i0.ɵɵtemplate(2, StageTablesComponent_div_2_Template, 35, 8, \"div\", 1);\n        i0.ɵɵtemplate(3, StageTablesComponent_div_3_Template, 7, 5, \"div\", 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(4, StageTablesComponent_core_sidebar_4_Template, 2, 6, \"core-sidebar\", 3);\n        i0.ɵɵtemplate(5, StageTablesComponent_ng_template_5_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tableData);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage && ctx.stage.type === ctx.AppConfig.TOURNAMENT_TYPES.league);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage && ctx.stage.type === ctx.AppConfig.TOURNAMENT_TYPES.league);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgTemplateOutlet, i9.DefaultClassDirective, i10.CoreSidebarComponent, i11.DataTableDirective, i12.EditorSidebarComponent, i6.TranslatePipe],\n    styles: [\".table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.72rem !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlLXRhYmxlcy9zdGFnZS10YWJsZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7O0VBRUUsMkJBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi50YWJsZSB0aCxcclxuLnRhYmxlIHRkIHtcclxuICBwYWRkaW5nOiAwLjcycmVtICFpbXBvcnRhbnQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAoE,eAAe;AAEnH,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,kBAAkB,QAAQ,oBAAoB;AAEvD,SAASC,OAAO,QAAQ,MAAM;AAI9B,SAASC,WAAW,QAAQ,0BAA0B;AAKtD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;ICWdC,EAAA,CAAAC,cAAA,aAOC;IACCD,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAeCH,EAAA,CAAAI,SAAA,YAUK;;;;;IACLJ,EAAA,CAAAI,SAAA,YAUK;;;;;;IAyBTJ,EAAA,CAAAC,cAAA,aAGC;IAaKD,EAAA,CAAAK,UAAA,mBAAAC,uFAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAC,aAAA,GAAAT,EAAA,CAAAU,aAAA,GAAAC,SAAA;MAAA,MAAAC,QAAA,GAAAZ,EAAA,CAAAU,aAAA,IAAAC,SAAA;MAAA,MAAAE,OAAA,GAAAb,EAAA,CAAAU,aAAA;MAAA,OAC6BV,EAAA,CAAAc,WAAA,CAAAD,OAAA,CAAAE,WAAA,CAAAH,QAAA,CAAAI,IAAA,EAAAP,aAAA,CAAAQ,OAAA,EACjC,IAAI,CAAC;IAAA;IAGDjB,EAAA,CAAAI,SAAA,YAA2C;IAC7CJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAYC;IAJCD,EAAA,CAAAK,UAAA,mBAAAa,uFAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAC,aAAA,GAAAT,EAAA,CAAAU,aAAA,GAAAC,SAAA;MAAA,MAAAC,QAAA,GAAAZ,EAAA,CAAAU,aAAA,IAAAC,SAAA;MAAA,MAAAQ,OAAA,GAAAnB,EAAA,CAAAU,aAAA;MAAA,OAC6BV,EAAA,CAAAc,WAAA,CAAAK,OAAA,CAAAJ,WAAA,CAAAH,QAAA,CAAAI,IAAA,EAAAP,aAAA,CAAAQ,OAAA,EACnC,MAAM,CAAC;IAAA;IAGDjB,EAAA,CAAAI,SAAA,YAA4C;IAC9CJ,EAAA,CAAAG,YAAA,EAAS;;;;;;;IA5BPH,EAAA,CAAAoB,SAAA,GAIC;IAJDpB,EAAA,CAAAqB,UAAA,YAAAC,OAAA,CAAAC,UAAA,CAAAC,KAAA,EAAAf,aAAA,CAAAQ,OAAA,kCAIC,cAAAK,OAAA,CAAAG,oBAAA;IAWDzB,EAAA,CAAAoB,SAAA,GAIC;IAJDpB,EAAA,CAAAqB,UAAA,YAAAC,OAAA,CAAAC,UAAA,CAAAC,KAAA,EAAAf,aAAA,CAAAQ,OAAA,oCAIC,cAAAK,OAAA,CAAAG,oBAAA;;;;;IAhFTzB,EAAA,CAAAC,cAAA,SAAgE;IAIxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA0B,UAAA,IAAAC,4DAAA,gBAUK;IACL3B,EAAA,CAAA0B,UAAA,IAAAE,4DAAA,gBAUK;IACP5B,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAI,SAAA,cAKE;IACJJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAA0B,UAAA,KAAAG,8DAAA,iBAuCK;IACP7B,EAAA,CAAAG,YAAA,EAAK;;;;;;;IAxFGH,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA8B,kBAAA,MAAAN,KAAA,UACF;IAGGxB,EAAA,CAAAoB,SAAA,GAOL;IAPKpB,EAAA,CAAAqB,UAAA,UAAAU,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,IAAAe,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,EAAAiB,GAAA,CAAAxB,aAAA,CAAAQ,OAAA,cAAAc,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,IAAAe,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,EAAAiB,GAAA,CAAAxB,aAAA,CAAAQ,OAAA,cAOL;IAIKjB,EAAA,CAAAoB,SAAA,GAOL;IAPKpB,EAAA,CAAAqB,UAAA,UAAAU,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,IAAAe,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,EAAAiB,GAAA,CAAAxB,aAAA,CAAAQ,OAAA,cAAAc,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,IAAAe,OAAA,CAAAC,WAAA,CAAAC,GAAA,CAAArB,QAAA,CAAAI,IAAA,EAAAiB,GAAA,CAAAxB,aAAA,CAAAQ,OAAA,cAOL;IAMEjB,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,QAAAZ,aAAA,CAAAyB,IAAA,CAAAC,IAAA,CAAAC,IAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAgC;IAMPrC,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAsC,iBAAA,CAAA7B,aAAA,CAAAyB,IAAA,CAAAlB,IAAA,CAAyB;IAClDhB,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAsC,iBAAA,CAAA7B,aAAA,CAAA8B,UAAA,CAA0B;IAE5BvC,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA8B,kBAAA,MAAArB,aAAA,CAAA+B,OAAA,MACF;IAEExC,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA8B,kBAAA,MAAArB,aAAA,CAAAgC,QAAA,MACF;IAEEzC,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA8B,kBAAA,MAAArB,aAAA,CAAAiC,SAAA,MACF;IACI1C,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAsC,iBAAA,CAAA7B,aAAA,CAAAkC,SAAA,CAAyB;IACzB3C,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAsC,iBAAA,CAAA7B,aAAA,CAAAmC,aAAA,CAA6B;IACJ5C,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAsC,iBAAA,CAAA7B,aAAA,CAAAoC,MAAA,CAAsB;IAGhD7C,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAqB,UAAA,SAAAU,OAAA,CAAAe,gBAAA,CAAsB;;;;;IA3D7B9C,EAAA,CAAA+C,uBAAA,GAIC;IACC/C,EAAA,CAAA0B,UAAA,IAAAsB,wDAAA,mBA4FK;IACPhD,EAAA,CAAAiD,qBAAA,EAAe;;;;;IA7FajD,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAqB,UAAA,YAAA6B,MAAA,CAAAC,SAAA,CAAAvC,QAAA,CAAAI,IAAA,EAA0B;;;;;;;;;;IA8FtDhB,EAAA,CAAAoD,kBAAA,OAOgB;;;;;;IAFdpD,EAAA,CAAAqB,UAAA,qBAAAgC,GAAA,CAAsC,4BAAArD,EAAA,CAAAsD,eAAA,IAAAC,GAAA,EAAA3C,QAAA;;;;;IAW5CZ,EAAA,CAAAC,cAAA,YAIC;IACCD,EAAA,CAAAE,MAAA,+EAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IACJH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAK,UAAA,mBAAAmD,4EAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAA7C,QAAA,GAAAZ,EAAA,CAAAU,aAAA,IAAAC,SAAA;MAAA,MAAA+C,OAAA,GAAA1D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAc,WAAA,CAAA4C,OAAA,CAAAC,WAAA,CAAA/C,QAAA,CAAAI,IAAA,CAAuB;IAAA,EAAC;IAIjChB,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAqB,UAAA,cAAAuC,OAAA,CAAAnC,oBAAA,CAAkC;;;;;IAhBtCzB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA0B,UAAA,IAAAmC,8CAAA,gBAOI;IACJ7D,EAAA,CAAA0B,UAAA,IAAAoC,mDAAA,qBAOS;IACX9D,EAAA,CAAAG,YAAA,EAAM;;;;IAfDH,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAqB,UAAA,SAAA0C,MAAA,CAAAtC,oBAAA,WAAoC;IAUpCzB,EAAA,CAAAoB,SAAA,GAAiE;IAAjEpB,EAAA,CAAAqB,UAAA,SAAA0C,MAAA,CAAAC,KAAA,IAAAD,MAAA,CAAAC,KAAA,CAAAC,IAAA,KAAAF,MAAA,CAAApE,SAAA,CAAAuE,gBAAA,CAAAC,QAAA,CAAiE;;;;;IAlK1EnE,EAAA,CAAAC,cAAA,UAGC;IAG4BD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE9CH,EAAA,CAAAI,SAAA,aAA6B;IAC7BJ,EAAA,CAAAC,cAAA,aAA8B;IAIlBD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAA0B,UAAA,KAAA0C,yCAAA,iBASK;IACPpE,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA0B,UAAA,KAAA2C,mDAAA,2BAkGe;IACfrE,EAAA,CAAA0B,UAAA,KAAA4C,mDAAA,2BAOgB;IAClBtE,EAAA,CAAAG,YAAA,EAAQ;IAIZH,EAAA,CAAA0B,UAAA,KAAA6C,0CAAA,kBAoBM;IACRvE,EAAA,CAAAG,YAAA,EAAM;;;;;IAvKNH,EAAA,CAAAwE,sBAAA,UAAAC,MAAA,CAAAC,SAAA,CAAAC,MAAA,2BAAyD;IAK9B3E,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAsC,iBAAA,CAAA1B,QAAA,CAAAI,IAAA,CAAgB;IAmB9BhB,EAAA,CAAAoB,SAAA,IAIF;IAJEpB,EAAA,CAAAqB,UAAA,SAAAoD,MAAA,CAAA3B,gBAAA,IAAA2B,MAAA,CAAAT,KAAA,IAAAS,MAAA,CAAAT,KAAA,CAAAC,IAAA,KAAAQ,MAAA,CAAA9E,SAAA,CAAAuE,gBAAA,CAAAC,QAAA,CAIF;IAQAnE,EAAA,CAAAoB,SAAA,GAEA;IAFApB,EAAA,CAAAqB,UAAA,SAAAoD,MAAA,CAAAT,KAAA,IAAAS,MAAA,CAAAT,KAAA,CAAAC,IAAA,KAAAQ,MAAA,CAAA9E,SAAA,CAAAuE,gBAAA,CAAAC,QAAA,CAEA;IAiGAnE,EAAA,CAAAoB,SAAA,GAGD;IAHCpB,EAAA,CAAAqB,UAAA,UAAAoD,MAAA,CAAAT,KAAA,IAAAS,MAAA,CAAAT,KAAA,IAAAS,MAAA,CAAAT,KAAA,CAAAC,IAAA,KAAAQ,MAAA,CAAA9E,SAAA,CAAAuE,gBAAA,CAAAC,QAAA,CAGD;IAULnE,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAqB,UAAA,SAAAoD,MAAA,CAAA3B,gBAAA,CAAsB;;;;;IAsB7B9C,EAAA,CAAAC,cAAA,cAGC;IAIOD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAI,SAAA,gBAKS;IACXJ,EAAA,CAAAG,YAAA,EAAM;;;;IATAH,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA4E,WAAA,gCACF;IAIA5E,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,cAAAwD,MAAA,CAAAC,SAAA,CAAuB,cAAAD,MAAA,CAAAE,SAAA;;;;;;IASjC/E,EAAA,CAAAC,cAAA,uBAKC;IAKGD,EAAA,CAAAK,UAAA,uBAAA2E,qFAAAC,MAAA;MAAAjF,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAC,OAAA,GAAAnF,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAc,WAAA,CAAAqE,OAAA,CAAAC,SAAA,CAAAH,MAAA,CAAiB;IAAA,EAAC;IAIjCjF,EAAA,CAAAG,YAAA,EAAqB;;;;IAXrBH,EAAA,CAAAqB,UAAA,SAAAgE,MAAA,CAAAC,UAAA,CAAmB;IAIjBtF,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAqB,UAAA,UAAAgE,MAAA,CAAAE,SAAA,CAAmB,WAAAF,MAAA,CAAAG,MAAA,YAAAH,MAAA,CAAAI,MAAA,kBAAAJ,MAAA,CAAAK,YAAA,oBAAAL,MAAA,CAAAM,cAAA;;;;;IAWrB3F,EAAA,CAAAC,cAAA,SAAsD;IAChDD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAI,SAAA,cAA0E;IAC5EJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/DH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAXpDH,EAAA,CAAAoB,SAAA,GAAW;IAAXpB,EAAA,CAAAsC,iBAAA,CAAAsD,KAAA,KAAW;IAER5F,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,QAAAwE,aAAA,CAAA3D,IAAA,CAAAC,IAAA,CAAAC,IAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAgC;IAEVrC,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAA3D,IAAA,CAAAlB,IAAA,CAAyB;IAClDhB,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAAtD,UAAA,CAA0B;IACKvC,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAArD,OAAA,CAAuB;IACvBxC,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAApD,QAAA,CAAwB;IACxBzC,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAAnD,SAAA,CAAyB;IACxD1C,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAAlD,SAAA,CAAyB;IACzB3C,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAAjD,aAAA,CAA6B;IACJ5C,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAsC,iBAAA,CAAAuD,aAAA,CAAAhD,MAAA,CAAsB;;;;;IAZrD7C,EAAA,CAAA0B,UAAA,IAAAoE,gDAAA,mBAaK;;;;IAbqB9F,EAAA,CAAAqB,UAAA,YAAA0E,SAAA,CAAAC,KAAA,CAAgB;;;AD9L5C,OAAM,MAAOC,oBAAoB;EAQ/B,IAAavB,SAASA,CAACwB,KAAU;IAC/B,IAAI,CAACC,UAAU,GAAGD,KAAK;IAEvB;EAEF;;EAEA,IAAIxB,SAASA,CAAA;IACX,OAAO,IAAI,CAACyB,UAAU;EACxB;EAmEAC,YACUC,YAA0B,EAC3BC,YAAyB,EACzBC,YAAyB,EACzBC,eAA+B,EAC9BC,KAAiB,EACjBC,iBAAmC,EACpCC,mBAAuC;IANtC,KAAAN,YAAY,GAAZA,YAAY;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAClB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA1F5B,KAAAhH,SAAS,GAAGA,SAAS;IAErB,KAAA4F,SAAS,GAAQ3F,kBAAkB;IACzB,KAAAgH,YAAY,GAAG,IAAIlH,YAAY,EAAO;IAEvC,KAAAoD,gBAAgB,GAAG,IAAI;IAehC,KAAAiC,SAAS,GAAyB,IAAIlF,OAAO,EAAe;IAC5D,KAAAiF,SAAS,GAAQ,EAAE;IACZ,KAAAQ,UAAU,GAAG,oBAAoB;IACjC,KAAAK,cAAc,GAAG,IAAI9F,OAAO,EAAO;IACnC,KAAA6F,YAAY,GAAG,EAAE;IACjB,KAAAF,MAAM,GAAU,CACrB;MACEqB,GAAG,EAAE,eAAe;MACpB5C,IAAI,EAAE,QAAQ;MACd6C,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACL,iBAAiB,CAACM,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAACP,iBAAiB,CAACM,OAAO,CAAC,aAAa,CAAC;QAC1DE,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;;KAEZ,EACD;MACEN,GAAG,EAAE,SAAS;MACd5C,IAAI,EAAE,OAAO;MACb6C,KAAK,EAAE;QACLI,QAAQ,EAAE,IAAI;QACdjD,IAAI,EAAE;OACP;MACDmD,YAAY,EAAE,IAAI,CAACb,YAAY,CAACc,gBAAgB,CAACC;KAClD,EACD;MACET,GAAG,EAAE,QAAQ;MACb5C,IAAI,EAAE,OAAO;MACb6C,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACL,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/CC,WAAW,EAAE,IAAI,CAACP,iBAAiB,CAACM,OAAO,CAAC,cAAc,CAAC;QAC3DE,QAAQ,EAAE,IAAI;QACdjD,IAAI,EAAE,QAAQ;QACdsD,GAAG,EAAE;;KAER,EACD;MACEV,GAAG,EAAE,QAAQ;MACb5C,IAAI,EAAE,UAAU;MAChB6C,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACL,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/CC,WAAW,EAAE,IAAI,CAACP,iBAAiB,CAACM,OAAO,CAAC,cAAc,CAAC;QAC3DE,QAAQ,EAAE;;KAEb,CACF;IACM,KAAAzB,MAAM,GAAwB;MACnC+B,SAAS,EAAE,IAAI,CAAClC,UAAU;MAC1BmC,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,sBAAsB,CAAC;QAC9DW,IAAI,EAAE,IAAI,CAACjB,iBAAiB,CAACM,OAAO,CAAC,YAAY,CAAC;QAClDY,MAAM,EAAE,IAAI,CAAClB,iBAAiB,CAACM,OAAO,CAAC,QAAQ;OAChD;MACDa,GAAG,EAAE,GAAG/H,WAAW,CAACgI,MAAM,2BAA2B;MACrDC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAED,KAAAC,QAAQ,GAAG,IAAIC,GAAG,EAAE;IACpB,KAAAlG,WAAW,GAAG,IAAIkG,GAAG,EAAE;IAEvB,KAAAzG,oBAAoB,GAAmB,IAAI;IAc3C,KAAA0B,SAAS,GAAG,EAAE;IA4VK,KAAAgF,KAAK,GAAGA,KAAK;EA/VhC;EAKAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpE,KAAK,EAAE;MACd,IAAI,IAAI,CAACA,KAAK,CAACC,IAAI,IAAItE,SAAS,CAACuE,gBAAgB,CAACmE,MAAM,EAAE;QACxD,IAAI,CAACC,yBAAyB,EAAE;;MAElC,IAAI,CAACjC,YAAY,CAACkC,yBAAyB,CAAC,IAAI,CAACvE,KAAK,CAACsD,EAAE,CAAC,CAACkB,SAAS,CAAEC,QAAiB,IAAI;QACzFC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;QACrB,IAAI,CAAChH,oBAAoB,GAAGgH,QAAQ;MACtC,CAAC,CAAC;MAEF,IAAI,CAACG,YAAY,EAAE;;EAKvB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAACA,OAAO,CAAC,kBAAkB,CAAC,CAACC,WAAW,EAAE;MAC3EL,OAAO,CAACC,GAAG,CAAC,qFAAqF,EAAEG,OAAO,CAAC,kBAAkB,CAAC,CAACE,YAAY,CAAC;MAC5I,IAAI,CAAClG,gBAAgB,GAAGgG,OAAO,CAAC,kBAAkB,CAAC,CAACE,YAAY;;IAGlE,IAAIF,OAAO,CAAC,WAAW,CAAC,IAAI,CAACA,OAAO,CAAC,WAAW,CAAC,CAACC,WAAW,EAAE;MAC7D,IAAI,IAAI,CAACjG,gBAAgB,KAAK,KAAK,EAAE;QACnC,IAAI,CAAC8F,YAAY,EAAE;;;EAGzB;EAEAA,YAAYA,CAAA;IAEV,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,EAAE;MAC1B;;IAGF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B;;IAGF,IAAI,IAAI,CAAC4B,SAAS,IAAI,IAAI,CAACV,KAAK,CAACC,IAAI,KAAKtE,SAAS,CAACuE,gBAAgB,CAACC,QAAQ,EAAE;MAC7E,IAAI,CAACO,SAAS,CAACuE,OAAO,CAAEC,KAAK,IAAI;QAC/B,IAAI,CAAC/F,SAAS,CAAC+F,KAAK,CAAClI,IAAI,CAAC,GAAGkI,KAAK,CAAClD,KAAK;MAC1C,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAACuB,SAAS;;IAGjC,IAAI,CAACyE,kBAAkB,EAAE;EAC3B;EAEA5H,UAAUA,CAAC6H,KAAa,EAAEC,MAAc,EAAEC,eAA8B;IACtE,IAAKA,eAAe,KAAK,IAAI,IAAIF,KAAK,KAAK,CAAC,IAAME,eAAe,KAAK,MAAM,IAAIF,KAAK,KAAK,IAAI,CAACjG,SAAS,CAACwB,MAAM,GAAG,CAAE,EAAE;MACpH,OAAO,KAAK;;IAGd,MAAM4E,UAAU,GAAG,IAAI,CAACtB,QAAQ,CAAChG,GAAG,CAACoH,MAAM,CAAC;IAE5C,IAAIE,UAAU,EAAE;MACd,OAAOA,UAAU,CAACD,eAAe,CAAC;;IAGpC,OAAO,KAAK;EAEd;EAEAE,mBAAmBA,CAACC,eAA2B,EAAEvH,IAAc,EAAEwH,eAAuB;IAItF,IAAI,CAACD,eAAe,EAAE9E,MAAM,EAAE,OAAO;MAAEgF,SAAS,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAI,CAAE;IAE3E,IAAIC,aAAa,GAAe,CAAC,GAAGJ,eAAe,CAAC;IAEpD,MAAMK,kBAAkB,GAAG5H,IAAI,CAACW,MAAM;IAEtC,MAAMkH,WAAW,GAAGA,CAAC/D,KAAiB,EAAEa,GAAmB,KAAgB;MACzE,OAAOb,KAAK,CAACgE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,CAAC,KAAK3E,IAAI,CAAC2E,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,IAAI6C,eAAe,KAAK/J,SAAS,CAACuK,gBAAgB,CAACC,YAAY,EAAE;MAC/DN,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,aAAa,CAAC;MACzD,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,SAAS,CAAC;MACnF,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,YAAY,CAAC;MACtF,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,kBAAkB,CAAC;MAC5F,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,WAAW,CAAC;;IAGvF,IAAIH,eAAe,KAAK/J,SAAS,CAACuK,gBAAgB,CAACE,KAAK,EAAE;MACxDP,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,kBAAkB,CAAC;MAE9D,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,WAAW,CAAC;MACrF,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,aAAa,CAAC;MACvF,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,SAAS,CAAC;MACnF,IAAIA,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAEkF,aAAa,GAAGE,WAAW,CAACF,aAAa,EAAE,YAAY,CAAC;;IAGxF,MAAMF,SAAS,GAAGE,aAAa,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACI,KAAK,GAAGnI,IAAI,CAACmI,KAAK,CAAC;IACjE,MAAMT,WAAW,GAAGC,aAAa,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACI,KAAK,GAAGnI,IAAI,CAACmI,KAAK,CAAC;IAEnE,OAAO;MACLV,SAAS,EAAEA,SAAS,CAAChF,MAAM,GAAG,CAAC,GAAGgF,SAAS,CAACA,SAAS,CAAChF,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;MACxEiF,WAAW,EAAEA,WAAW,CAACjF,MAAM,GAAG,CAAC,GAAGiF,WAAW,CAAC,CAAC,CAAC,GAAG;KACxD;EACH;EAEAT,kBAAkBA,CAAA;IAEhB,IAAI,CAAClB,QAAQ,CAACqC,KAAK,EAAE;IAGrBC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACrH,SAAS,CAAC,CAAC8F,OAAO,CAACC,KAAK,IAAG;MAC5CA,KAAK,CAACD,OAAO,CAAE/G,IAAI,IAAI;QAErB,MAAMuH,eAAe,GAAGP,KAAK,CAACc,MAAM,CAACC,CAAC,IACpCA,CAAC,CAACpH,MAAM,KAAKX,IAAI,CAACW,MAAM,IACxBoH,CAAC,CAAC1H,UAAU,KAAKL,IAAI,CAACK,UAAU,IAChC0H,CAAC,CAACzH,OAAO,KAAKN,IAAI,CAACM,OAAO,IAC1ByH,CAAC,CAACxH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,IAC5BwH,CAAC,CAACvH,SAAS,KAAKR,IAAI,CAACQ,SAAS,IAC9BuH,CAAC,CAACtH,SAAS,KAAKT,IAAI,CAACS,SAAS,IAC9BsH,CAAC,CAACrH,aAAa,KAAKV,IAAI,CAACU,aAAa,IACtCqH,CAAC,CAACQ,gBAAgB,KAAKvI,IAAI,CAACuI,gBAAgB,IAC5CR,CAAC,CAAChJ,OAAO,KAAKiB,IAAI,CAACjB,OAAO,CAC3B;QAED,MAAM;UACJ0I,SAAS;UACTC;QAAW,CACZ,GAAG,IAAI,CAACJ,mBAAmB,CAACC,eAAe,EAAEvH,IAAI,EAAE,IAAI,CAAC8B,KAAK,CAAC0G,gBAAgB,CAAC;QAEhF,IAAI,CAACzC,QAAQ,CAAC0C,GAAG,CAACzI,IAAI,CAACjB,OAAO,EAAE;UAC9B2J,EAAE,EAAEjB,SAAS;UACbkB,IAAI,EAAEjB;SACP,CAAC;MACJ,CAAC,CAAC;IAGJ,CAAC,CAAC;IACFlB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACV,QAAQ,CAAC;IAC3C,OAAO,IAAI,CAACA,QAAQ;EAEtB;EAEAlH,WAAWA,CAAC+J,SAAiB,EAAEzB,MAAc,EAAE0B,SAAwB;IACrE,MAAMxB,UAAU,GAAG,IAAI,CAACtB,QAAQ,CAAChG,GAAG,CAACoH,MAAM,CAAC;IAE5C,IAAIE,UAAU,EAAE;MACd,MAAMrH,IAAI,GAAGqH,UAAU,CAACwB,SAAS,CAAC;MAElC,MAAMC,SAAS,GAAG,IAAI,CAAC7H,SAAS,CAAC2H,SAAS,CAAC,CAACG,SAAS,CAAChB,CAAC,IAAIA,CAAC,CAAChJ,OAAO,KAAKiB,IAAI,CAACjB,OAAO,CAAC;MACtF,MAAMiK,gBAAgB,GAAG,IAAI,CAAC/H,SAAS,CAAC2H,SAAS,CAAC,CAACG,SAAS,CAAChB,CAAC,IAAIA,CAAC,CAAChJ,OAAO,KAAKoI,MAAM,CAAC;MAEvF,IAAI2B,SAAS,KAAK,CAAC,CAAC,IAAIE,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAE/C,IAAI,CAACC,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC3H,SAAS,CAAC2H,SAAS,CAAC,CAACI,gBAAgB,CAAC,CAACjK,OAAO,EAAE8J,SAAS,CAAC;QACjG,IAAI,CAACI,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC3H,SAAS,CAAC2H,SAAS,CAAC,CAACE,SAAS,CAAC,CAAC/J,OAAO,EAAE8J,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QAEnH;QACA,MAAMK,SAAS,GAAG,IAAI,CAACjI,SAAS,CAAC2H,SAAS,CAAC,CAACE,SAAS,CAAC,CAACX,KAAK;QAC5D,IAAI,CAAClH,SAAS,CAAC2H,SAAS,CAAC,CAACE,SAAS,CAAC,CAACX,KAAK,GAAG,IAAI,CAAClH,SAAS,CAAC2H,SAAS,CAAC,CAACI,gBAAgB,CAAC,CAACb,KAAK;QAC9F,IAAI,CAAClH,SAAS,CAAC2H,SAAS,CAAC,CAACI,gBAAgB,CAAC,CAACb,KAAK,GAAGe,SAAS;QAE7D,IAAI,CAACjI,SAAS,CAAC2H,SAAS,CAAC,GAAG,IAAI,CAAC3H,SAAS,CAAC2H,SAAS,CAAC,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,KAAK,GAAGkB,CAAC,CAAClB,KAAK,CAAC;QAEvF;QACA,IAAI,CAAClB,kBAAkB,EAAE;;;IAG7B;EAEF;EAEAgC,iBAAiBA,CAACL,SAAiB,EAAEzB,MAAc,EAAE0B,SAAwB;IAG3E,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACC,GAAG,CAAC6I,SAAS,CAAC,EAAE;MACpC,IAAI,CAAC9I,WAAW,CAAC2I,GAAG,CAACG,SAAS,EAAE,IAAI5C,GAAG,EAAE,CAAC;;IAG5C,MAAMsD,sBAAsB,GAAG,IAAI,CAACxJ,WAAW,CAACC,GAAG,CAAC6I,SAAS,CAAC,CAAC7I,GAAG,CAACoH,MAAM,CAAC,IAAI,CAAC;IAE/E,IAAI0B,SAAS,KAAK,IAAI,EAAE;MACtB,IAAI,CAAC/I,WAAW,CAACC,GAAG,CAAC6I,SAAS,CAAC,CAACH,GAAG,CAACtB,MAAM,EAAEmC,sBAAsB,GAAG,CAAC,CAAC;KACxE,MAAM,IAAIT,SAAS,KAAK,MAAM,EAAE;MAC/B,IAAI,CAAC/I,WAAW,CAACC,GAAG,CAAC6I,SAAS,CAAC,CAACH,GAAG,CAACtB,MAAM,EAAEmC,sBAAsB,GAAG,CAAC,CAAC;;EAE3E;EAEAC,mBAAmBA,CAACX,SAAiB;IACnC,OAAO,IAAI,CAAC3H,SAAS,CAAC2H,SAAS,CAAC,CAACY,GAAG,CAAExJ,IAAI,KAAM;MAC9CjB,OAAO,EAAEiB,IAAI,CAACjB,OAAO;MACrBoJ,KAAK,EAAEnI,IAAI,CAACmI,KAAK;MACjBsB,QAAQ,EAAE,IAAI,CAAC3H,KAAK,CAACsD;KACtB,CAAC,CAAC;EACL;EAEA3D,WAAWA,CAACmH,SAAiB;IAE3B/K,IAAI,CAAC6L,IAAI,CAAC;MACRnE,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,wBAAwB,CAAC;MAC/D6E,IAAI,EAAE,IAAI,CAACnF,iBAAiB,CAACM,OAAO,CAAC,sDAAsD,CAAC;MAC5F8E,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAACrF,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;MAC3DgF,gBAAgB,EAAE,IAAI,CAACtF,iBAAiB,CAACM,OAAO,CAAC,IAAI,CAAC;MACtDiF,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;KACjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAAChG,YAAY,CAACiG,eAAe,CAAC,IAAI,CAACb,mBAAmB,CAACX,SAAS,CAAC,CAAC,CAACtC,SAAS,CAAEC,QAAQ,IAAI;UAC5FC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,QAAQ,CAAC;UACtD1I,IAAI,CAAC6L,IAAI,CAAC;YACRnE,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,SAAS,CAAC;YAChD6E,IAAI,EAAE,IAAI,CAACnF,iBAAiB,CAACM,OAAO,CAAC,sDAAsD,CAAC;YAC5FiF,IAAI,EAAE,SAAS;YACfF,iBAAiB,EAAE,IAAI,CAACrF,iBAAiB,CAACM,OAAO,CAAC,IAAI;WACvD,CAAC;UACF,IAAI,CAAChF,WAAW,CAACC,GAAG,CAAC6I,SAAS,CAAC,CAACR,KAAK,EAAE;UACvC,IAAI,CAACjE,YAAY,CAACkG,eAAe,CAAC,IAAI,CAACvI,KAAK,CAACsD,EAAE,CAAC,CAACkB,SAAS,EAAE;QAC9D,CAAC,EAAGgE,KAAK,IAAI;UACX9D,OAAO,CAAC8D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/CzM,IAAI,CAAC6L,IAAI,CAAC;YACRnE,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,OAAO,CAAC;YAC9C6E,IAAI,EAAE,IAAI,CAACnF,iBAAiB,CAACM,OAAO,CAACwF,KAAK,CAACC,OAAO,CAAC;YACnDR,IAAI,EAAE,OAAO;YACbF,iBAAiB,EAAE,IAAI,CAACrF,iBAAiB,CAACM,OAAO,CAAC,IAAI;WACvD,CAAC;QACJ,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA0F,MAAMA,CAAC1E,MAAM,EAAE2E,GAAI;IACjB,IAAI,CAAClH,MAAM,CAACuC,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACvC,MAAM,CAACkH,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAAChG,mBAAmB,CAACiG,kBAAkB,CAAC,IAAI,CAACtH,UAAU,CAAC,CAACuH,UAAU,EAAE;EAC3E;EAEAzH,SAASA,CAACH,MAAM;IACd,IAAI,CAAC2B,YAAY,CAACkG,IAAI,CAAC7H,MAAM,CAAC;IAE9B,IAAI,CAAC2D,YAAY,EAAE;EACrB;EAEAN,yBAAyBA,CAAA;IACvB,IAAI,CAACxD,SAAS,GAAG;MACfiI,GAAG,EAAE,IAAI,CAACvG,eAAe,CAACwG,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAI,CAAC5G,KAAK,CACP6G,IAAI,CACH,GAAGxN,WAAW,CAACgI,MAAM,0BAA0B,IAAI,CAAC9D,KAAK,CAACsD,EAAE,EAAE,EAC9D8F,oBAAoB,CACrB,CACA5E,SAAS,CAAE+E,IAAS,IAAI;UACvB,IAAI,CAAC/H,MAAM,CAAC,CAAC,CAAC,CAACsB,KAAK,CAACK,OAAO,GAAGoG,IAAI,CAACpG,OAAO,CAACnB,KAAK;UACjD,IAAI,CAACL,cAAc,CAAC6H,IAAI,CAAC,IAAI,CAAChI,MAAM,CAAC;UACrC6H,QAAQ,CAAC;YACPI,YAAY,EAAEF,IAAI,CAACE,YAAY;YAC/BC,eAAe,EAAEH,IAAI,CAACG,eAAe;YACrCC,IAAI,EAAEJ,IAAI,CAACI;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAACtH,eAAe,CAACwG,iBAAiB,CAACe,IAAI;MACrDC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,CACP;QAAEN,IAAI,EAAE,IAAI;QAAEO,OAAO,EAAE;MAAK,CAAE,EAC9B;QACEzG,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,MAAM,CAAC;QAC7C2G,IAAI,EAAE;OACP,EACD;QACElG,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/C2G,IAAI,EAAE;OACP,EACD;QACElG,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/C2G,IAAI,EAAE;OACP,EACD;QACElG,KAAK,EAAE,IAAI,CAACf,iBAAiB,CAACM,OAAO,CAAC,YAAY,CAAC;QACnD2G,IAAI,EAAE,SAAS;QACfQ,MAAM,EAAEA,CAACR,IAAI,EAAE1J,IAAI,EAAE0I,GAAG,KAAI;UAC1B,OAAO,IAAI,CAACrG,YAAY,CAAC8H,QAAQ,CAACzB,GAAG,CAAC0B,IAAI,CAAC;QAC7C;OACD,CACF;MACDC,UAAU,EAAE,CACV,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EACjB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CACrB;MACDC,OAAO,EAAE;QACPxB,GAAG,EAAE,IAAI,CAACvG,eAAe,CAACwG,iBAAiB,CAACuB,OAAO,CAACxB,GAAG;QACvDwB,OAAO,EAAE,CACP;UACE1C,IAAI,EAAE,oCAAoC,IAAI,CAACnF,iBAAiB,CAACM,OAAO,CACtE,KAAK,CACN,EAAE;UACHgB,MAAM,EAAEA,CAACwG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,IAAI,CAACjC,MAAM,CAAC,QAAQ,CAAC;UACvB;SACD,EACD;UACEb,IAAI,EAAE,oCAAoC,IAAI,CAACnF,iBAAiB,CAACM,OAAO,CACtE,MAAM,CACP,EAAE;UACH4H,MAAM,EAAE,gBAAgB;UACxB5G,MAAM,EAAEA,CAACwG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,IAAI,CAACjC,MAAM,CAAC,MAAM,CAAC;UACrB;SACD,EACD;UACEb,IAAI,EAAE,qCAAqC,IAAI,CAACnF,iBAAiB,CAACM,OAAO,CACvE,QAAQ,CACT,EAAE;UACH4H,MAAM,EAAE,UAAU;UAClB5G,MAAM,EAAEA,CAACwG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,IAAI,CAACjC,MAAM,CAAC,QAAQ,CAAC;UACvB;SACD;;KAGN;EACH;EAEAmC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC7K,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,IAAI,IAAItE,SAAS,CAACuE,gBAAgB,CAACmE,MAAM,EAAE;MACtEyG,UAAU,CAAC,MAAK;QACd,IAAI,CAAC/J,SAAS,CAACyI,IAAI,CAAC,IAAI,CAAC1I,SAAS,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEAiK,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC/K,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,IAAI,IAAItE,SAAS,CAACuE,gBAAgB,CAACmE,MAAM,EAAE;MACtE,IAAI,CAACtD,SAAS,CAACiK,WAAW,EAAE;;EAEhC;EAAC,QAAAC,CAAA;qBAzbUhJ,oBAAoB,EAAAjG,EAAA,CAAAkP,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAApP,EAAA,CAAAkP,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtP,EAAA,CAAAkP,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAxP,EAAA,CAAAkP,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1P,EAAA,CAAAkP,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAA5P,EAAA,CAAAkP,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA9P,EAAA,CAAAkP,iBAAA,CAAAa,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA;UAApBhK,oBAAoB;IAAAiK,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAEpBzQ,kBAAkB;;;;;;;;;;;;;;;;;;;;;QCxB/BI,EAAA,CAAAC,cAAA,UAAK;QAEDD,EAAA,CAAA0B,UAAA,IAAA6O,mCAAA,kBAyKM;QAENvQ,EAAA,CAAA0B,UAAA,IAAA8O,mCAAA,iBAiBM;QACRxQ,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAA0B,UAAA,IAAA+O,4CAAA,0BAee;QAEfzQ,EAAA,CAAA0B,UAAA,IAAAgP,2CAAA,gCAAA1Q,EAAA,CAAA2Q,sBAAA,CAec;;;QA9NU3Q,EAAA,CAAAoB,SAAA,GAAY;QAAZpB,EAAA,CAAAqB,UAAA,YAAAiP,GAAA,CAAA5L,SAAA,CAAY;QA0K7B1E,EAAA,CAAAoB,SAAA,GAA+D;QAA/DpB,EAAA,CAAAqB,UAAA,SAAAiP,GAAA,CAAAtM,KAAA,IAAAsM,GAAA,CAAAtM,KAAA,CAAAC,IAAA,KAAAqM,GAAA,CAAA3Q,SAAA,CAAAuE,gBAAA,CAAAmE,MAAA,CAA+D;QAqBnErI,EAAA,CAAAoB,SAAA,GAA+D;QAA/DpB,EAAA,CAAAqB,UAAA,SAAAiP,GAAA,CAAAtM,KAAA,IAAAsM,GAAA,CAAAtM,KAAA,CAAAC,IAAA,KAAAqM,GAAA,CAAA3Q,SAAA,CAAAuE,gBAAA,CAAAmE,MAAA,CAA+D", "names": ["EventEmitter", "AppConfig", "DataTableDirective", "Subject", "environment", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template_button_click_2_listener", "ɵɵrestoreView", "_r18", "stageTeam_r11", "ɵɵnextContext", "$implicit", "group_r5", "ctx_r16", "ɵɵresetView", "updateOrder", "name", "team_id", "StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template_button_click_4_listener", "ctx_r20", "ɵɵadvance", "ɵɵproperty", "ctx_r15", "showButton", "i_r12", "canUpdateLeaderboard", "ɵɵtemplate", "StageTablesComponent_div_2_ng_container_32_tr_1_i_5_Template", "StageTablesComponent_div_2_ng_container_32_tr_1_i_6_Template", "StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template", "ɵɵtextInterpolate1", "ctx_r10", "orderChange", "get", "team", "club", "logo", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "no_matches", "no_wins", "no_draws", "no_losses", "goals_for", "goals_against", "points", "showUpdateButton", "ɵɵelementContainerStart", "StageTablesComponent_div_2_ng_container_32_tr_1_Template", "ɵɵelementContainerEnd", "ctx_r7", "groupData", "ɵɵelementContainer", "_r3", "ɵɵpureFunction1", "_c0", "StageTablesComponent_div_2_div_34_button_2_Template_button_click_0_listener", "_r31", "ctx_r29", "submitOrder", "ctx_r28", "StageTablesComponent_div_2_div_34_p_1_Template", "StageTablesComponent_div_2_div_34_button_2_Template", "ctx_r9", "stage", "type", "TOURNAMENT_TYPES", "knockout", "StageTablesComponent_div_2_th_30_Template", "StageTablesComponent_div_2_ng_container_32_Template", "StageTablesComponent_div_2_ng_container_33_Template", "StageTablesComponent_div_2_div_34_Template", "ɵɵclassMapInterpolate1", "ctx_r0", "tableData", "length", "ɵɵpipeBind1", "ctx_r1", "dtOptions", "dtTrigger", "StageTablesComponent_core_sidebar_4_Template_app_editor_sidebar_onSuccess_1_listener", "$event", "_r33", "ctx_r32", "onSuccess", "ctx_r2", "table_name", "dtElement", "fields", "params", "paramsToPost", "fields_subject", "i_r37", "stageTeam_r36", "StageTablesComponent_ng_template_5_tr_0_Template", "group_r34", "teams", "StageTablesComponent", "value", "_tableData", "constructor", "stageService", "_userService", "_authService", "_commonsService", "_http", "_translateService", "_coreSidebarService", "onDataChange", "key", "props", "label", "instant", "placeholder", "required", "options", "defaultValue", "currentUserValue", "id", "max", "editor_id", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "mapEqual", "Map", "alert", "ngOnInit", "league", "initAdjustmentsPointTable", "checkCanUpdateLeaderboard", "subscribe", "response", "console", "log", "makeTeamData", "ngOnChanges", "changes", "firstChange", "currentValue", "for<PERSON>ach", "group", "mapTeamsEqualPoint", "index", "teamId", "buttonDirection", "equalTeams", "checkCanChangeOrder", "equalPointTeams", "rankingCriteria", "teamCanUp", "teamCanDown", "filteredTeams", "currentTotalPoints", "compareStep", "filter", "t", "RANKING_CRITERIA", "head_to_head", "total", "order", "clear", "Object", "values", "goals_difference", "ranking_criteria", "set", "up", "down", "groupName", "direction", "teamIndex", "findIndex", "currentTeamIndex", "updateOrderChange", "tempOrder", "sort", "a", "b", "currentTeamOrderChange", "makeSubmitOrderData", "map", "stage_id", "fire", "text", "showCancelButton", "confirmButtonText", "cancelButtonText", "icon", "reverseButtons", "then", "result", "isConfirmed", "submitTeamOrder", "checkMatchScore", "error", "message", "editor", "row", "getSidebarRegistry", "toggle<PERSON><PERSON>", "emit", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "post", "resp", "next", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "columnDefs", "columns", "visible", "render", "fullName", "user", "lengthMenu", "buttons", "e", "dt", "node", "config", "extend", "ngAfterViewInit", "setTimeout", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "StageService", "i2", "UserService", "i3", "AuthService", "i4", "CommonsService", "i5", "HttpClient", "i6", "TranslateService", "i7", "CoreSidebarService", "_2", "selectors", "viewQuery", "StageTablesComponent_Query", "rf", "ctx", "StageTablesComponent_div_2_Template", "StageTablesComponent_div_3_Template", "StageTablesComponent_core_sidebar_4_Template", "StageTablesComponent_ng_template_5_Template", "ɵɵtemplateRefExtractor"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-tables\\stage-tables.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-tables\\stage-tables.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { Subject } from 'rxjs';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from 'environments/environment';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\nimport { TeamData } from 'app/interfaces/stages';\r\n\r\n@Component({\r\n  selector: 'stage-tables',\r\n  templateUrl: './stage-tables.component.html',\r\n  styleUrls: ['./stage-tables.component.scss']\r\n})\r\nexport class StageTablesComponent implements OnInit, OnChanges {\r\n  AppConfig = AppConfig;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @Output() onDataChange = new EventEmitter<any>();\r\n\r\n  @Input() showUpdateButton = true;\r\n\r\n  @Input() set tableData(value: any) {\r\n    this._tableData = value;\r\n\r\n    // this.makeTeamData();\r\n\r\n  }\r\n\r\n  get tableData(): any {\r\n    return this._tableData;\r\n  }\r\n\r\n  private _tableData: any;\r\n  @Input() stage: any;\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  dtOptions: any = {};\r\n  public table_name = 'adjust-point-table';\r\n  public fields_subject = new Subject<any>();\r\n  public paramsToPost = {};\r\n  public fields: any[] = [\r\n    {\r\n      key: 'stage_team_id',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Team'),\r\n        placeholder: this._translateService.instant('Select team'),\r\n        required: true,\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'user_id',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        type: 'hidden'\r\n      },\r\n      defaultValue: this._authService.currentUserValue.id\r\n    },\r\n    {\r\n      key: 'points',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Points'),\r\n        placeholder: this._translateService.instant('Enter points'),\r\n        required: true,\r\n        type: 'number',\r\n        max: 1000\r\n      }\r\n    },\r\n    {\r\n      key: 'reason',\r\n      type: 'textarea',\r\n      props: {\r\n        label: this._translateService.instant('Reason'),\r\n        placeholder: this._translateService.instant('Enter reason'),\r\n        required: true\r\n      }\r\n    }\r\n  ];\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Add adjustment point'),\r\n      edit: this._translateService.instant('Edit point'),\r\n      remove: this._translateService.instant('Remove')\r\n    },\r\n    url: `${environment.apiUrl}/adjustment-points/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n\r\n  mapEqual = new Map();\r\n  orderChange = new Map();\r\n\r\n  canUpdateLeaderboard: boolean | null = null;\r\n\r\n  constructor(\r\n    private stageService: StageService,\r\n    public _userService: UserService,\r\n    public _authService: AuthService,\r\n    public _commonsService: CommonsService,\r\n    private _http: HttpClient,\r\n    private _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService\r\n  ) {\r\n  }\r\n\r\n\r\n  groupData = [];\r\n\r\n  ngOnInit(): void {\r\n    if (this.stage) {\r\n      if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n        this.initAdjustmentsPointTable();\r\n      }\r\n      this.stageService.checkCanUpdateLeaderboard(this.stage.id).subscribe((response: boolean) => {\r\n        console.log(response);\r\n        this.canUpdateLeaderboard = response;\r\n      });\r\n\r\n      this.makeTeamData();\r\n    }\r\n\r\n\r\n\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['showUpdateButton'] && !changes['showUpdateButton'].firstChange) {\r\n      console.log(\"🚀 ~ StageTablesComponent ~ ngOnChanges ~ changes['showUpdateButton'].currentValue:\", changes['showUpdateButton'].currentValue)\r\n      this.showUpdateButton = changes['showUpdateButton'].currentValue;\r\n    }\r\n\r\n    if (changes['tableData'] && !changes['tableData'].firstChange) {\r\n      if (this.showUpdateButton !== false) {\r\n        this.makeTeamData();\r\n      }\r\n    }\r\n  }\r\n\r\n  makeTeamData() {\r\n\r\n    if (!this.showUpdateButton) {\r\n      return;\r\n    }\r\n\r\n    if (!this.showUpdateButton) {\r\n      return;\r\n    }\r\n\r\n    if (this.tableData && this.stage.type !== AppConfig.TOURNAMENT_TYPES.knockout) {\r\n      this.tableData.forEach((group) => {\r\n        this.groupData[group.name] = group.teams;\r\n      });\r\n    } else {\r\n      this.groupData = this.tableData;\r\n    }\r\n\r\n    this.mapTeamsEqualPoint();\r\n  }\r\n\r\n  showButton(index: number, teamId: number, buttonDirection: 'up' | 'down') {\r\n    if ((buttonDirection === 'up' && index === 0) || (buttonDirection === 'down' && index === this.groupData.length - 1)) {\r\n      return false;\r\n    }\r\n\r\n    const equalTeams = this.mapEqual.get(teamId);\r\n\r\n    if (equalTeams) {\r\n      return equalTeams[buttonDirection];\r\n    }\r\n\r\n    return false;\r\n\r\n  }\r\n\r\n  checkCanChangeOrder(equalPointTeams: TeamData[], team: TeamData, rankingCriteria: string): {\r\n    teamCanUp: TeamData | null;\r\n    teamCanDown: TeamData | null;\r\n  } {\r\n    if (!equalPointTeams?.length) return { teamCanUp: null, teamCanDown: null };\r\n\r\n    let filteredTeams: TeamData[] = [...equalPointTeams];\r\n\r\n    const currentTotalPoints = team.points;\r\n\r\n    const compareStep = (teams: TeamData[], key: keyof TeamData): TeamData[] => {\r\n      return teams.filter(t => t[key] === team[key]);\r\n    };\r\n\r\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.head_to_head) {\r\n      filteredTeams = compareStep(filteredTeams, '_h2h_points');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_difference');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\r\n    }\r\n\r\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.total) {\r\n      filteredTeams = compareStep(filteredTeams, 'goals_difference');\r\n\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_points');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\r\n    }\r\n\r\n    const teamCanUp = filteredTeams.filter(t => t.order < team.order);\r\n    const teamCanDown = filteredTeams.filter(t => t.order > team.order);\r\n\r\n    return {\r\n      teamCanUp: teamCanUp.length > 0 ? teamCanUp[teamCanUp.length - 1] : null,\r\n      teamCanDown: teamCanDown.length > 0 ? teamCanDown[0] : null\r\n    };\r\n  }\r\n\r\n  mapTeamsEqualPoint() {\r\n\r\n    this.mapEqual.clear();\r\n\r\n\r\n    Object.values(this.groupData).forEach(group => {\r\n      group.forEach((team) => {\r\n\r\n        const equalPointTeams = group.filter(t =>\r\n          t.points === team.points &&\r\n          t.no_matches === team.no_matches &&\r\n          t.no_wins === team.no_wins &&\r\n          t.no_draws === team.no_draws &&\r\n          t.no_losses === team.no_losses &&\r\n          t.goals_for === team.goals_for &&\r\n          t.goals_against === team.goals_against &&\r\n          t.goals_difference === team.goals_difference &&\r\n          t.team_id !== team.team_id\r\n        );\r\n\r\n        const {\r\n          teamCanUp,\r\n          teamCanDown\r\n        } = this.checkCanChangeOrder(equalPointTeams, team, this.stage.ranking_criteria);\r\n\r\n        this.mapEqual.set(team.team_id, {\r\n          up: teamCanUp,\r\n          down: teamCanDown\r\n        });\r\n      });\r\n\r\n\r\n    });\r\n    console.log('this.mapEqual', this.mapEqual);\r\n    return this.mapEqual;\r\n\r\n  }\r\n\r\n  updateOrder(groupName: string, teamId: number, direction: 'up' | 'down') {\r\n    const equalTeams = this.mapEqual.get(teamId);\r\n\r\n    if (equalTeams) {\r\n      const team = equalTeams[direction];\r\n\r\n      const teamIndex = this.groupData[groupName].findIndex(t => t.team_id === team.team_id);\r\n      const currentTeamIndex = this.groupData[groupName].findIndex(t => t.team_id === teamId);\r\n\r\n      if (teamIndex !== -1 && currentTeamIndex !== -1) {\r\n\r\n        this.updateOrderChange(groupName, this.groupData[groupName][currentTeamIndex].team_id, direction);\r\n        this.updateOrderChange(groupName, this.groupData[groupName][teamIndex].team_id, direction === 'up' ? 'down' : 'up');\r\n\r\n        // Swap the order of the two teams\r\n        const tempOrder = this.groupData[groupName][teamIndex].order;\r\n        this.groupData[groupName][teamIndex].order = this.groupData[groupName][currentTeamIndex].order;\r\n        this.groupData[groupName][currentTeamIndex].order = tempOrder;\r\n\r\n        this.groupData[groupName] = this.groupData[groupName].sort((a, b) => a.order - b.order);\r\n\r\n        // Update the map\r\n        this.mapTeamsEqualPoint();\r\n      }\r\n    }\r\n    ;\r\n\r\n  }\r\n\r\n  updateOrderChange(groupName: string, teamId: number, direction: 'up' | 'down') {\r\n\r\n\r\n    if (!this.orderChange.get(groupName)) {\r\n      this.orderChange.set(groupName, new Map());\r\n    }\r\n\r\n    const currentTeamOrderChange = this.orderChange.get(groupName).get(teamId) || 0;\r\n\r\n    if (direction === 'up') {\r\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange + 1);\r\n    } else if (direction === 'down') {\r\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange - 1);\r\n    }\r\n  }\r\n\r\n  makeSubmitOrderData(groupName: string) {\r\n    return this.groupData[groupName].map((team) => ({\r\n      team_id: team.team_id,\r\n      order: team.order,\r\n      stage_id: this.stage.id\r\n    }));\r\n  }\r\n\r\n  submitOrder(groupName: string) {\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Submit new Leaderboard'),\r\n      text: this._translateService.instant('Are you sure you want to submit the new Leaderboard?'),\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Submit'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      icon: 'warning',\r\n      reverseButtons: true\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.stageService.submitTeamOrder(this.makeSubmitOrderData(groupName)).subscribe((response) => {\r\n          console.log('Order submitted successfully:', response);\r\n          Swal.fire({\r\n            title: this._translateService.instant('Success'),\r\n            text: this._translateService.instant('The new Leaderboard has been submitted successfully.'),\r\n            icon: 'success',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n          this.orderChange.get(groupName).clear();\r\n          this.stageService.checkMatchScore(this.stage.id).subscribe();\r\n        }, (error) => {\r\n          console.error('Error submitting order:', error);\r\n          Swal.fire({\r\n            title: this._translateService.instant('Error'),\r\n            text: this._translateService.instant(error.message),\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  onSuccess($event) {\r\n    this.onDataChange.emit($event);\r\n\r\n    this.makeTeamData();\r\n  }\r\n\r\n  initAdjustmentsPointTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // add season id\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/adjustment-points/all/${this.stage.id}`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this.fields[0].props.options = resp.options.teams;\r\n            this.fields_subject.next(this.fields);\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [],\r\n      columns: [\r\n        { data: 'id', visible: false },\r\n        {\r\n          title: this._translateService.instant('Team'),\r\n          data: 'team.name'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Points'),\r\n          data: 'points'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Reason'),\r\n          data: 'reason'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Updated by'),\r\n          data: 'user_id',\r\n          render: (data, type, row) => {\r\n            return this._userService.fullName(row.user);\r\n          }\r\n        }\r\n      ],\r\n      lengthMenu: [\r\n        [25, 50, 100, -1],\r\n        [25, 50, 100, 'All']\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant(\r\n              'Add'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              this.editor('create');\r\n            }\r\n          },\r\n          {\r\n            text: `<i class=\"fa-solid fa-edit\"></i> ${this._translateService.instant(\r\n              'Edit'\r\n            )}`,\r\n            extend: 'selectedSingle',\r\n            action: (e, dt, node, config) => {\r\n              this.editor('edit');\r\n            }\r\n          },\r\n          {\r\n            text: `<i class=\"fa-solid fa-trash\"></i> ${this._translateService.instant(\r\n              'Remove'\r\n            )}`,\r\n            extend: 'selected',\r\n            action: (e, dt, node, config) => {\r\n              this.editor('remove');\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n      setTimeout(() => {\r\n        this.dtTrigger.next(this.dtOptions);\r\n      }, 1000);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n      this.dtTrigger.unsubscribe();\r\n    }\r\n  }\r\n\r\n\r\n  protected readonly alert = alert;\r\n}\r\n", "<div>\r\n  <div class=\"row\">\r\n    <div\r\n      class=\"col  {{ tableData.length > 1 ? 'col-lg-6' : '' }}\"\r\n      *ngFor=\"let group of tableData\"\r\n    >\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h4 class=\"card-title\">{{ group.name }}</h4>\r\n        </div>\r\n        <div class=\"card-body\"></div>\r\n        <div class=\"table-responsive\">\r\n          <table class=\"table\">\r\n            <thead>\r\n              <tr>\r\n                <th>#</th>\r\n                <th>Logo</th>\r\n                <th>Team</th>\r\n                <th>P</th>\r\n                <th class=\"d-none d-sm-table-cell\">W</th>\r\n                <th class=\"d-none d-sm-table-cell\">D</th>\r\n                <th class=\"d-none d-sm-table-cell\">L</th>\r\n                <th>F</th>\r\n                <th>A</th>\r\n                <th>Pts</th>\r\n                <th\r\n                  class=\"text-center\"\r\n                  *ngIf=\"\r\n                    showUpdateButton &&\r\n                    stage &&\r\n                    stage.type !== AppConfig.TOURNAMENT_TYPES.knockout\r\n                  \"\r\n                >\r\n                  Action\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <ng-container\r\n                *ngIf=\"\r\n                  stage && stage.type !== AppConfig.TOURNAMENT_TYPES.knockout\r\n                \"\r\n              >\r\n                <tr *ngFor=\"let stageTeam of groupData[group.name]; index as i\">\r\n                  <td>\r\n                    <div class=\"d-flex align-items-center\" style=\"gap: 4px\">\r\n                      <span>\r\n                        {{ i + 1 }}\r\n                      </span>\r\n                      <i\r\n                        class=\"text-danger bi bi-caret-down-fill\"\r\n                        *ngIf=\"\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) &&\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) < 0\r\n                        \"\r\n                      ></i>\r\n                      <i\r\n                        class=\"text-success bi bi-caret-up-fill\"\r\n                        *ngIf=\"\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) &&\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) > 0\r\n                        \"\r\n                      ></i>\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <img\r\n                      [src]=\"stageTeam.team.club.logo\"\r\n                      alt=\"logo\"\r\n                      width=\"30\"\r\n                      height=\"30\"\r\n                    />\r\n                  </td>\r\n                  <td class=\"font-weight-bold\">{{ stageTeam.team.name }}</td>\r\n                  <td>{{ stageTeam.no_matches }}</td>\r\n                  <td class=\"d-none d-sm-table-cell\">\r\n                    {{ stageTeam.no_wins }}\r\n                  </td>\r\n                  <td class=\"d-none d-sm-table-cell\">\r\n                    {{ stageTeam.no_draws }}\r\n                  </td>\r\n                  <td class=\"d-none d-sm-table-cell\">\r\n                    {{ stageTeam.no_losses }}\r\n                  </td>\r\n                  <td>{{ stageTeam.goals_for }}</td>\r\n                  <td>{{ stageTeam.goals_against }}</td>\r\n                  <td class=\"font-weight-bold\">{{ stageTeam.points }}</td>\r\n                  <td\r\n                    class=\"font-weight-bold text-center\"\r\n                    *ngIf=\"showUpdateButton\"\r\n                  >\r\n                    <div\r\n                      class=\"d-flex align-items-center justify-content-center\"\r\n                      style=\"gap: 4px\"\r\n                    >\r\n                      <button\r\n                        [ngClass]=\"\r\n                          showButton(i, stageTeam.team_id, 'up')\r\n                            ? 'visible'\r\n                            : 'invisible'\r\n                        \"\r\n                        class=\"btn btn-outline-success\"\r\n                        style=\"padding: 4px\"\r\n                        (click)=\"\r\n                          updateOrder(group.name, stageTeam.team_id, 'up')\r\n                        \"\r\n                        [disabled]=\"!canUpdateLeaderboard\"\r\n                      >\r\n                        <i class=\"text-success bi bi-arrow-up\"></i>\r\n                      </button>\r\n                      <button\r\n                        [ngClass]=\"\r\n                          showButton(i, stageTeam.team_id, 'down')\r\n                            ? 'visible'\r\n                            : 'invisible'\r\n                        \"\r\n                        class=\"btn btn-outline-danger\"\r\n                        style=\"padding: 4px\"\r\n                        (click)=\"\r\n                          updateOrder(group.name, stageTeam.team_id, 'down')\r\n                        \"\r\n                        [disabled]=\"!canUpdateLeaderboard\"\r\n                      >\r\n                        <i class=\"text-danger bi bi-arrow-down\"></i>\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </ng-container>\r\n              <ng-container\r\n                *ngIf=\"\r\n                  !stage ||\r\n                  (stage && stage.type === AppConfig.TOURNAMENT_TYPES.knockout)\r\n                \"\r\n                [ngTemplateOutlet]=\"notLeagueTemplate\"\r\n                [ngTemplateOutletContext]=\"{ group: group }\"\r\n              ></ng-container>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n\r\n        <div\r\n          class=\"mt-1 mr-1 mb-3 d-flex flex-column align-items-end\"\r\n          *ngIf=\"showUpdateButton\"\r\n        >\r\n          <p\r\n            *ngIf=\"canUpdateLeaderboard === false\"\r\n            class=\"h5 mw-100 badge badge-light-warning px-2 py-1\"\r\n            style=\"font-size: 12px\"\r\n          >\r\n            You cannot update the leaderboard once the knockout stage has\r\n            started.\r\n          </p>\r\n          <button\r\n            class=\"btn btn-primary mw-100\"\r\n            (click)=\"submitOrder(group.name)\"\r\n            *ngIf=\"stage && stage.type !== AppConfig.TOURNAMENT_TYPES.knockout\"\r\n            [disabled]=\"!canUpdateLeaderboard\"\r\n          >\r\n            Submit\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div\r\n      *ngIf=\"stage && stage.type === AppConfig.TOURNAMENT_TYPES.league\"\r\n      class=\"col-lg-6\"\r\n    >\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h4>\r\n            {{ 'Point Adjustment' | translate }}\r\n          </h4>\r\n        </div>\r\n        <table\r\n          datatable\r\n          [dtOptions]=\"dtOptions\"\r\n          [dtTrigger]=\"dtTrigger\"\r\n          class=\"table row-border hover\"\r\n        ></table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<core-sidebar\r\n  *ngIf=\"stage && stage.type === AppConfig.TOURNAMENT_TYPES.league\"\r\n  class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n  [name]=\"table_name\"\r\n  overlayClass=\"modal-backdrop\"\r\n>\r\n  <app-editor-sidebar\r\n    [table]=\"dtElement\"\r\n    [fields]=\"fields\"\r\n    [params]=\"params\"\r\n    (onSuccess)=\"onSuccess($event)\"\r\n    [paramsToPost]=\"paramsToPost\"\r\n    [fields_subject]=\"fields_subject\"\r\n  >\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #notLeagueTemplate let-group=\"group\">\r\n  <tr *ngFor=\"let stageTeam of group.teams; index as i\">\r\n    <td>{{ i + 1 }}</td>\r\n    <td>\r\n      <img [src]=\"stageTeam.team.club.logo\" alt=\"logo\" width=\"30\" height=\"30\" />\r\n    </td>\r\n    <td class=\"font-weight-bold\">{{ stageTeam.team.name }}</td>\r\n    <td>{{ stageTeam.no_matches }}</td>\r\n    <td class=\"d-none d-sm-table-cell\">{{ stageTeam.no_wins }}</td>\r\n    <td class=\"d-none d-sm-table-cell\">{{ stageTeam.no_draws }}</td>\r\n    <td class=\"d-none d-sm-table-cell\">{{ stageTeam.no_losses }}</td>\r\n    <td>{{ stageTeam.goals_for }}</td>\r\n    <td>{{ stageTeam.goals_against }}</td>\r\n    <td class=\"font-weight-bold\">{{ stageTeam.points }}</td>\r\n  </tr>\r\n</ng-template>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { environment } from 'environments/environment';\nimport { throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TeamsheetService {\n  constructor(_http) {\n    this._http = _http;\n    this.season = {\n      name: '2020-2021'\n    };\n  }\n  getTeamSheet2Submit(team_id) {\n    return this._http.get(`${environment.apiUrl}/team-sheets/show/${team_id}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  static #_ = this.ɵfac = function TeamsheetService_Factory(t) {\n    return new (t || TeamsheetService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TeamsheetService,\n    factory: TeamsheetService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAKhD,OAAM,MAAOC,gBAAgB;EAK3BC,YAAmBC,KAAiB;IAAjB,KAAAA,KAAK,GAALA,KAAK;IAHjB,KAAAC,MAAM,GAAQ;MACnBC,IAAI,EAAE;KACP;EACsC;EAEvCC,mBAAmBA,CAACC,OAAO;IACzB,OAAO,IAAI,CAACJ,KAAK,CACdK,GAAG,CAAC,GAAGX,WAAW,CAACY,MAAM,qBAAqBF,OAAO,EAAE,CAAC,CACxDG,IAAI,CACHX,GAAG,CAAEY,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFX,UAAU,CAAEY,GAAG,IAAI;MACjB,OAAOd,UAAU,CAACc,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAAC,QAAAC,CAAA;qBAlBUZ,gBAAgB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAhBjB,gBAAgB;IAAAkB,OAAA,EAAhBlB,gBAAgB,CAAAmB,IAAA;IAAAC,UAAA,EAFf;EAAM", "names": ["environment", "throwError", "map", "catchError", "TeamsheetService", "constructor", "_http", "season", "name", "getTeamSheet2Submit", "team_id", "get", "apiUrl", "pipe", "res", "err", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\teamsheet.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\nimport { throwError } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TeamsheetService {\r\n  public currentTeam: any;\r\n  public season: any = {\r\n    name: '2020-2021',\r\n  };\r\n  constructor(public _http: HttpClient) {}\r\n\r\n  getTeamSheet2Submit(team_id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/team-sheets/show/${team_id}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
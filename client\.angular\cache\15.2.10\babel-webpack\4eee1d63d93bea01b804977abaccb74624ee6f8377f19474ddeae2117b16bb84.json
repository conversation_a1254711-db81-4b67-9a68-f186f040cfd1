{"ast": null, "code": "/*!\n * \n *               jsPDF AutoTable plugin v3.5.28\n *\n *               Copyright (c) 2022 <PERSON>, https://github.com/simonben<PERSON><PERSON>/jsPDF-AutoTable\n *               Licensed under the MIT License.\n *               http://opensource.org/licenses/mit-license\n *\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory(function webpackLoadOptionalExternalModule() {\n    try {\n      return require(\"jspdf\");\n    } catch (e) {}\n  }());else if (typeof define === 'function' && define.amd) define([\"jspdf\"], factory);else {\n    var a = typeof exports === 'object' ? factory(function webpackLoadOptionalExternalModule() {\n      try {\n        return require(\"jspdf\");\n      } catch (e) {}\n    }()) : factory(root[\"jspdf\"]);\n    for (var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n  }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof this !== 'undefined' ? this : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : global, function (__WEBPACK_EXTERNAL_MODULE__84__) {\n  return (/******/function () {\n      // webpackBootstrap\n      /******/\n      \"use strict\";\n\n      /******/\n      var __webpack_modules__ = {\n        /***/662: /***/function (__unused_webpack_module, exports) {\n          var __extends = this && this.__extends || function () {\n            var extendStatics = function (d, b) {\n              extendStatics = Object.setPrototypeOf || {\n                __proto__: []\n              } instanceof Array && function (d, b) {\n                d.__proto__ = b;\n              } || function (d, b) {\n                for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n              };\n              return extendStatics(d, b);\n            };\n            return function (d, b) {\n              if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n              extendStatics(d, b);\n              function __() {\n                this.constructor = d;\n              }\n              d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n            };\n          }();\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.CellHookData = exports.HookData = void 0;\n          var HookData = /** @class */function () {\n            function HookData(doc, table, cursor) {\n              this.table = table;\n              this.pageNumber = table.pageNumber;\n              this.pageCount = this.pageNumber;\n              this.settings = table.settings;\n              this.cursor = cursor;\n              this.doc = doc.getDocument();\n            }\n            return HookData;\n          }();\n          exports.HookData = HookData;\n          var CellHookData = /** @class */function (_super) {\n            __extends(CellHookData, _super);\n            function CellHookData(doc, table, cell, row, column, cursor) {\n              var _this = _super.call(this, doc, table, cursor) || this;\n              _this.cell = cell;\n              _this.row = row;\n              _this.column = column;\n              _this.section = row.section;\n              return _this;\n            }\n            return CellHookData;\n          }(HookData);\n          exports.CellHookData = CellHookData;\n\n          /***/\n        },\n\n        /***/790: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          var htmlParser_1 = __webpack_require__(148);\n          var autoTableText_1 = __webpack_require__(938);\n          var documentHandler_1 = __webpack_require__(323);\n          var inputParser_1 = __webpack_require__(587);\n          var tableDrawer_1 = __webpack_require__(49);\n          var tableCalculator_1 = __webpack_require__(858);\n          function default_1(jsPDF) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            jsPDF.API.autoTable = function () {\n              var args = [];\n              for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n              }\n              var options;\n              if (args.length === 1) {\n                options = args[0];\n              } else {\n                console.error('Use of deprecated autoTable initiation');\n                options = args[2] || {};\n                options.columns = args[0];\n                options.body = args[1];\n              }\n              var input = (0, inputParser_1.parseInput)(this, options);\n              var table = (0, tableCalculator_1.createTable)(this, input);\n              (0, tableDrawer_1.drawTable)(this, table);\n              return this;\n            };\n            // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n            jsPDF.API.lastAutoTable = false;\n            jsPDF.API.previousAutoTable = false; // deprecated in v3\n            jsPDF.API.autoTable.previous = false; // deprecated in v3\n            jsPDF.API.autoTableText = function (text, x, y, styles) {\n              (0, autoTableText_1.default)(text, x, y, styles, this);\n            };\n            jsPDF.API.autoTableSetDefaults = function (defaults) {\n              documentHandler_1.DocHandler.setDefaults(defaults, this);\n              return this;\n            };\n            jsPDF.autoTableSetDefaults = function (defaults, doc) {\n              documentHandler_1.DocHandler.setDefaults(defaults, doc);\n            };\n            jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n              if (includeHiddenElements === void 0) {\n                includeHiddenElements = false;\n              }\n              if (typeof window === 'undefined') {\n                console.error('Cannot run autoTableHtmlToJson in non browser environment');\n                return null;\n              }\n              var doc = new documentHandler_1.DocHandler(this);\n              var _a = (0, htmlParser_1.parseHtml)(doc, tableElem, window, includeHiddenElements, false),\n                head = _a.head,\n                body = _a.body;\n              var columns = head[0].map(function (c) {\n                return c.content;\n              });\n              return {\n                columns: columns,\n                rows: body,\n                data: body\n              };\n            };\n            /**\n             * @deprecated\n             */\n            jsPDF.API.autoTableEndPosY = function () {\n              console.error('Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.');\n              var prev = this.lastAutoTable;\n              if (prev && prev.finalY) {\n                return prev.finalY;\n              } else {\n                return 0;\n              }\n            };\n            /**\n             * @deprecated\n             */\n            jsPDF.API.autoTableAddPageContent = function (hook) {\n              console.error('Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead.');\n              if (!jsPDF.API.autoTable.globalDefaults) {\n                jsPDF.API.autoTable.globalDefaults = {};\n              }\n              jsPDF.API.autoTable.globalDefaults.addPageContent = hook;\n              return this;\n            };\n            /**\n             * @deprecated\n             */\n            jsPDF.API.autoTableAddPage = function () {\n              console.error('Use of deprecated function: autoTableAddPage. Use doc.addPage()');\n              this.addPage();\n              return this;\n            };\n          }\n          exports[\"default\"] = default_1;\n\n          /***/\n        },\n\n        /***/938: /***/function (__unused_webpack_module, exports) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          /**\n           * Improved text function with halign and valign support\n           * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n           */\n          function default_1(text, x, y, styles, doc) {\n            styles = styles || {};\n            var FONT_ROW_RATIO = 1.15;\n            var k = doc.internal.scaleFactor;\n            var fontSize = doc.internal.getFontSize() / k;\n            var splitRegex = /\\r\\n|\\r|\\n/g;\n            var splitText = '';\n            var lineCount = 1;\n            if (styles.valign === 'middle' || styles.valign === 'bottom' || styles.halign === 'center' || styles.halign === 'right') {\n              splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n              lineCount = splitText.length || 1;\n            }\n            // Align the top\n            y += fontSize * (2 - FONT_ROW_RATIO);\n            if (styles.valign === 'middle') y -= lineCount / 2 * fontSize * FONT_ROW_RATIO;else if (styles.valign === 'bottom') y -= lineCount * fontSize * FONT_ROW_RATIO;\n            if (styles.halign === 'center' || styles.halign === 'right') {\n              var alignSize = fontSize;\n              if (styles.halign === 'center') alignSize *= 0.5;\n              if (splitText && lineCount >= 1) {\n                for (var iLine = 0; iLine < splitText.length; iLine++) {\n                  doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                  y += fontSize * FONT_ROW_RATIO;\n                }\n                return doc;\n              }\n              x -= doc.getStringUnitWidth(text) * alignSize;\n            }\n            if (styles.halign === 'justify') {\n              doc.text(text, x, y, {\n                maxWidth: styles.maxWidth || 100,\n                align: 'justify'\n              });\n            } else {\n              doc.text(text, x, y);\n            }\n            return doc;\n          }\n          exports[\"default\"] = default_1;\n\n          /***/\n        },\n\n        /***/200: /***/function (__unused_webpack_module, exports) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.parseSpacing = exports.getFillStyle = exports.addTableBorder = exports.getStringWidth = void 0;\n          function getStringWidth(text, styles, doc) {\n            doc.applyStyles(styles, true);\n            var textArr = Array.isArray(text) ? text : [text];\n            var widestLineWidth = textArr.map(function (text) {\n              return doc.getTextWidth(text);\n            }).reduce(function (a, b) {\n              return Math.max(a, b);\n            }, 0);\n            return widestLineWidth;\n          }\n          exports.getStringWidth = getStringWidth;\n          function addTableBorder(doc, table, startPos, cursor) {\n            var lineWidth = table.settings.tableLineWidth;\n            var lineColor = table.settings.tableLineColor;\n            doc.applyStyles({\n              lineWidth: lineWidth,\n              lineColor: lineColor\n            });\n            var fillStyle = getFillStyle(lineWidth, false);\n            if (fillStyle) {\n              doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n            }\n          }\n          exports.addTableBorder = addTableBorder;\n          function getFillStyle(lineWidth, fillColor) {\n            var drawLine = lineWidth > 0;\n            var drawBackground = fillColor || fillColor === 0;\n            if (drawLine && drawBackground) {\n              return 'DF'; // Fill then stroke\n            } else if (drawLine) {\n              return 'S'; // Only stroke (transparent background)\n            } else if (drawBackground) {\n              return 'F'; // Only fill, no stroke\n            } else {\n              return null;\n            }\n          }\n          exports.getFillStyle = getFillStyle;\n          function parseSpacing(value, defaultValue) {\n            var _a, _b, _c, _d;\n            value = value || defaultValue;\n            if (Array.isArray(value)) {\n              if (value.length >= 4) {\n                return {\n                  top: value[0],\n                  right: value[1],\n                  bottom: value[2],\n                  left: value[3]\n                };\n              } else if (value.length === 3) {\n                return {\n                  top: value[0],\n                  right: value[1],\n                  bottom: value[2],\n                  left: value[1]\n                };\n              } else if (value.length === 2) {\n                return {\n                  top: value[0],\n                  right: value[1],\n                  bottom: value[0],\n                  left: value[1]\n                };\n              } else if (value.length === 1) {\n                value = value[0];\n              } else {\n                value = defaultValue;\n              }\n            }\n            if (typeof value === 'object') {\n              if (typeof value.vertical === 'number') {\n                value.top = value.vertical;\n                value.bottom = value.vertical;\n              }\n              if (typeof value.horizontal === 'number') {\n                value.right = value.horizontal;\n                value.left = value.horizontal;\n              }\n              return {\n                left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n                top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n                right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n                bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue\n              };\n            }\n            if (typeof value !== 'number') {\n              value = defaultValue;\n            }\n            return {\n              top: value,\n              right: value,\n              bottom: value,\n              left: value\n            };\n          }\n          exports.parseSpacing = parseSpacing;\n\n          /***/\n        },\n\n        /***/913: /***/function (__unused_webpack_module, exports) {\n          var __extends = this && this.__extends || function () {\n            var extendStatics = function (d, b) {\n              extendStatics = Object.setPrototypeOf || {\n                __proto__: []\n              } instanceof Array && function (d, b) {\n                d.__proto__ = b;\n              } || function (d, b) {\n                for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n              };\n              return extendStatics(d, b);\n            };\n            return function (d, b) {\n              if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n              extendStatics(d, b);\n              function __() {\n                this.constructor = d;\n              }\n              d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n            };\n          }();\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.getTheme = exports.defaultStyles = exports.HtmlRowInput = exports.FONT_ROW_RATIO = void 0;\n          /**\n           * Ratio between font size and font height. The number comes from jspdf's source code\n           */\n          exports.FONT_ROW_RATIO = 1.15;\n          var HtmlRowInput = /** @class */function (_super) {\n            __extends(HtmlRowInput, _super);\n            function HtmlRowInput(element) {\n              var _this = _super.call(this) || this;\n              _this._element = element;\n              return _this;\n            }\n            return HtmlRowInput;\n          }(Array);\n          exports.HtmlRowInput = HtmlRowInput;\n          // Base style for all themes\n          function defaultStyles(scaleFactor) {\n            return {\n              font: 'helvetica',\n              fontStyle: 'normal',\n              overflow: 'linebreak',\n              fillColor: false,\n              textColor: 20,\n              halign: 'left',\n              valign: 'top',\n              fontSize: 10,\n              cellPadding: 5 / scaleFactor,\n              lineColor: 200,\n              lineWidth: 0,\n              cellWidth: 'auto',\n              minCellHeight: 0,\n              minCellWidth: 0\n            };\n          }\n          exports.defaultStyles = defaultStyles;\n          function getTheme(name) {\n            var themes = {\n              striped: {\n                table: {\n                  fillColor: 255,\n                  textColor: 80,\n                  fontStyle: 'normal'\n                },\n                head: {\n                  textColor: 255,\n                  fillColor: [41, 128, 185],\n                  fontStyle: 'bold'\n                },\n                body: {},\n                foot: {\n                  textColor: 255,\n                  fillColor: [41, 128, 185],\n                  fontStyle: 'bold'\n                },\n                alternateRow: {\n                  fillColor: 245\n                }\n              },\n              grid: {\n                table: {\n                  fillColor: 255,\n                  textColor: 80,\n                  fontStyle: 'normal',\n                  lineWidth: 0.1\n                },\n                head: {\n                  textColor: 255,\n                  fillColor: [26, 188, 156],\n                  fontStyle: 'bold',\n                  lineWidth: 0\n                },\n                body: {},\n                foot: {\n                  textColor: 255,\n                  fillColor: [26, 188, 156],\n                  fontStyle: 'bold',\n                  lineWidth: 0\n                },\n                alternateRow: {}\n              },\n              plain: {\n                head: {\n                  fontStyle: 'bold'\n                },\n                foot: {\n                  fontStyle: 'bold'\n                }\n              }\n            };\n            return themes[name];\n          }\n          exports.getTheme = getTheme;\n\n          /***/\n        },\n\n        /***/259: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.parseCss = void 0;\n          // Limitations\n          // - No support for border spacing\n          // - No support for transparency\n          var common_1 = __webpack_require__(200);\n          function parseCss(supportedFonts, element, scaleFactor, style, window) {\n            var result = {};\n            var pxScaleFactor = 96 / 72;\n            var backgroundColor = parseColor(element, function (elem) {\n              return window.getComputedStyle(elem)['backgroundColor'];\n            });\n            if (backgroundColor != null) result.fillColor = backgroundColor;\n            var textColor = parseColor(element, function (elem) {\n              return window.getComputedStyle(elem)['color'];\n            });\n            if (textColor != null) result.textColor = textColor;\n            var borderColor = parseColor(element, function (elem) {\n              return window.getComputedStyle(elem)['borderTopColor'];\n            });\n            if (borderColor != null) result.lineColor = borderColor;\n            var padding = parsePadding(style, scaleFactor);\n            if (padding) result.cellPadding = padding;\n            // style.borderWidth only works in chrome (borderTopWidth etc works in firefox and ie as well)\n            var bw = parseInt(style.borderTopWidth || '');\n            bw = bw / pxScaleFactor / scaleFactor;\n            if (bw) result.lineWidth = bw;\n            var accepted = ['left', 'right', 'center', 'justify'];\n            if (accepted.indexOf(style.textAlign) !== -1) {\n              result.halign = style.textAlign;\n            }\n            accepted = ['middle', 'bottom', 'top'];\n            if (accepted.indexOf(style.verticalAlign) !== -1) {\n              result.valign = style.verticalAlign;\n            }\n            var res = parseInt(style.fontSize || '');\n            if (!isNaN(res)) result.fontSize = res / pxScaleFactor;\n            var fontStyle = parseFontStyle(style);\n            if (fontStyle) result.fontStyle = fontStyle;\n            var font = (style.fontFamily || '').toLowerCase();\n            if (supportedFonts.indexOf(font) !== -1) {\n              result.font = font;\n            }\n            return result;\n          }\n          exports.parseCss = parseCss;\n          function parseFontStyle(style) {\n            var res = '';\n            if (style.fontWeight === 'bold' || style.fontWeight === 'bolder' || parseInt(style.fontWeight) >= 700) {\n              res = 'bold';\n            }\n            if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n              res += 'italic';\n            }\n            return res;\n          }\n          function parseColor(element, styleGetter) {\n            var cssColor = realColor(element, styleGetter);\n            if (!cssColor) return null;\n            var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n            if (!rgba || !Array.isArray(rgba)) {\n              return null;\n            }\n            var color = [parseInt(rgba[1]), parseInt(rgba[2]), parseInt(rgba[3])];\n            var alpha = parseInt(rgba[4]);\n            if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n              return null;\n            }\n            return color;\n          }\n          function realColor(elem, styleGetter) {\n            var bg = styleGetter(elem);\n            if (bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent' || bg === 'initial' || bg === 'inherit') {\n              if (elem.parentElement == null) {\n                return null;\n              }\n              return realColor(elem.parentElement, styleGetter);\n            } else {\n              return bg;\n            }\n          }\n          function parsePadding(style, scaleFactor) {\n            var val = [style.paddingTop, style.paddingRight, style.paddingBottom, style.paddingLeft];\n            var pxScaleFactor = 96 / (72 / scaleFactor);\n            var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n            var inputPadding = val.map(function (n) {\n              return parseInt(n || '0') / pxScaleFactor;\n            });\n            var padding = (0, common_1.parseSpacing)(inputPadding, 0);\n            if (linePadding > padding.top) {\n              padding.top = linePadding;\n            }\n            if (linePadding > padding.bottom) {\n              padding.bottom = linePadding;\n            }\n            return padding;\n          }\n\n          /***/\n        },\n\n        /***/323: /***/function (__unused_webpack_module, exports) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.DocHandler = void 0;\n          var globalDefaults = {};\n          var DocHandler = /** @class */function () {\n            function DocHandler(jsPDFDocument) {\n              this.jsPDFDocument = jsPDFDocument;\n              this.userStyles = {\n                // Black for versions of jspdf without getTextColor\n                textColor: jsPDFDocument.getTextColor ? this.jsPDFDocument.getTextColor() : 0,\n                fontSize: jsPDFDocument.internal.getFontSize(),\n                fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n                font: jsPDFDocument.internal.getFont().fontName,\n                // 0 for versions of jspdf without getLineWidth\n                lineWidth: jsPDFDocument.getLineWidth ? this.jsPDFDocument.getLineWidth() : 0,\n                // Black for versions of jspdf without getDrawColor\n                lineColor: jsPDFDocument.getDrawColor ? this.jsPDFDocument.getDrawColor() : 0\n              };\n            }\n            DocHandler.setDefaults = function (defaults, doc) {\n              if (doc === void 0) {\n                doc = null;\n              }\n              if (doc) {\n                doc.__autoTableDocumentDefaults = defaults;\n              } else {\n                globalDefaults = defaults;\n              }\n            };\n            DocHandler.unifyColor = function (c) {\n              if (Array.isArray(c)) {\n                return c;\n              } else if (typeof c === 'number') {\n                return [c, c, c];\n              } else if (typeof c === 'string') {\n                return [c];\n              } else {\n                return null;\n              }\n            };\n            DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n              // Font style needs to be applied before font\n              // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n              var _a, _b, _c;\n              if (fontOnly === void 0) {\n                fontOnly = false;\n              }\n              if (styles.fontStyle) this.jsPDFDocument.setFontStyle && this.jsPDFDocument.setFontStyle(styles.fontStyle);\n              var _d = this.jsPDFDocument.internal.getFont(),\n                fontStyle = _d.fontStyle,\n                fontName = _d.fontName;\n              if (styles.font) fontName = styles.font;\n              if (styles.fontStyle) {\n                fontStyle = styles.fontStyle;\n                var availableFontStyles = this.getFontList()[fontName];\n                if (availableFontStyles && availableFontStyles.indexOf(fontStyle) === -1) {\n                  // Common issue was that the default bold in headers\n                  // made custom fonts not work. For example:\n                  // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                  this.jsPDFDocument.setFontStyle && this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                  fontStyle = availableFontStyles[0];\n                }\n              }\n              this.jsPDFDocument.setFont(fontName, fontStyle);\n              if (styles.fontSize) this.jsPDFDocument.setFontSize(styles.fontSize);\n              if (fontOnly) {\n                return; // Performance improvement\n              }\n\n              var color = DocHandler.unifyColor(styles.fillColor);\n              if (color) (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n              color = DocHandler.unifyColor(styles.textColor);\n              if (color) (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n              color = DocHandler.unifyColor(styles.lineColor);\n              if (color) (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n              if (typeof styles.lineWidth === 'number') {\n                this.jsPDFDocument.setLineWidth(styles.lineWidth);\n              }\n            };\n            DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n              return this.jsPDFDocument.splitTextToSize(text, size, opts);\n            };\n            DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n              return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n            };\n            DocHandler.prototype.getLastAutoTable = function () {\n              return this.jsPDFDocument.lastAutoTable || null;\n            };\n            DocHandler.prototype.getTextWidth = function (text) {\n              return this.jsPDFDocument.getTextWidth(text);\n            };\n            DocHandler.prototype.getDocument = function () {\n              return this.jsPDFDocument;\n            };\n            DocHandler.prototype.setPage = function (page) {\n              this.jsPDFDocument.setPage(page);\n            };\n            DocHandler.prototype.addPage = function () {\n              return this.jsPDFDocument.addPage();\n            };\n            DocHandler.prototype.getFontList = function () {\n              return this.jsPDFDocument.getFontList();\n            };\n            DocHandler.prototype.getGlobalOptions = function () {\n              return globalDefaults || {};\n            };\n            DocHandler.prototype.getDocumentOptions = function () {\n              return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n            };\n            DocHandler.prototype.pageSize = function () {\n              var pageSize = this.jsPDFDocument.internal.pageSize;\n              // JSPDF 1.4 uses get functions instead of properties on pageSize\n              if (pageSize.width == null) {\n                pageSize = {\n                  width: pageSize.getWidth(),\n                  height: pageSize.getHeight()\n                };\n              }\n              return pageSize;\n            };\n            DocHandler.prototype.scaleFactor = function () {\n              return this.jsPDFDocument.internal.scaleFactor;\n            };\n            DocHandler.prototype.pageNumber = function () {\n              var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n              if (!pageInfo) {\n                // Only recent versions of jspdf has pageInfo\n                return this.jsPDFDocument.internal.getNumberOfPages();\n              }\n              return pageInfo.pageNumber;\n            };\n            return DocHandler;\n          }();\n          exports.DocHandler = DocHandler;\n\n          /***/\n        },\n\n        /***/148: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.parseHtml = void 0;\n          var cssParser_1 = __webpack_require__(259);\n          var config_1 = __webpack_require__(913);\n          function parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n            var _a, _b;\n            if (includeHiddenHtml === void 0) {\n              includeHiddenHtml = false;\n            }\n            if (useCss === void 0) {\n              useCss = false;\n            }\n            var tableElement;\n            if (typeof input === 'string') {\n              tableElement = window.document.querySelector(input);\n            } else {\n              tableElement = input;\n            }\n            var supportedFonts = Object.keys(doc.getFontList());\n            var scaleFactor = doc.scaleFactor();\n            var head = [],\n              body = [],\n              foot = [];\n            if (!tableElement) {\n              console.error('Html table could not be found with input: ', input);\n              return {\n                head: head,\n                body: body,\n                foot: foot\n              };\n            }\n            for (var i = 0; i < tableElement.rows.length; i++) {\n              var element = tableElement.rows[i];\n              var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n              var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n              if (!row) continue;\n              if (tagName === 'thead') {\n                head.push(row);\n              } else if (tagName === 'tfoot') {\n                foot.push(row);\n              } else {\n                // Add to body both if parent is tbody or table\n                body.push(row);\n              }\n            }\n            return {\n              head: head,\n              body: body,\n              foot: foot\n            };\n          }\n          exports.parseHtml = parseHtml;\n          function parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n            var resultRow = new config_1.HtmlRowInput(row);\n            for (var i = 0; i < row.cells.length; i++) {\n              var cell = row.cells[i];\n              var style_1 = window.getComputedStyle(cell);\n              if (includeHidden || style_1.display !== 'none') {\n                var cellStyles = void 0;\n                if (useCss) {\n                  cellStyles = (0, cssParser_1.parseCss)(supportedFonts, cell, scaleFactor, style_1, window);\n                }\n                resultRow.push({\n                  rowSpan: cell.rowSpan,\n                  colSpan: cell.colSpan,\n                  styles: cellStyles,\n                  _element: cell,\n                  content: parseCellContent(cell)\n                });\n              }\n            }\n            var style = window.getComputedStyle(row);\n            if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n              return resultRow;\n            }\n          }\n          function parseCellContent(orgCell) {\n            // Work on cloned node to make sure no changes are applied to html table\n            var cell = orgCell.cloneNode(true);\n            // Remove extra space and line breaks in markup to make it more similar to\n            // what would be shown in html\n            cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n            // Preserve <br> tags as line breaks in the pdf\n            cell.innerHTML = cell.innerHTML.split(/\\<br.*?\\>/) //start with '<br' and ends with '>'.\n            .map(function (part) {\n              return part.trim();\n            }).join('\\n');\n            // innerText for ie\n            return cell.innerText || cell.textContent || '';\n          }\n\n          /***/\n        },\n\n        /***/587: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.parseInput = void 0;\n          var htmlParser_1 = __webpack_require__(148);\n          var polyfills_1 = __webpack_require__(360);\n          var common_1 = __webpack_require__(200);\n          var documentHandler_1 = __webpack_require__(323);\n          var inputValidator_1 = __webpack_require__(291);\n          function parseInput(d, current) {\n            var doc = new documentHandler_1.DocHandler(d);\n            var document = doc.getDocumentOptions();\n            var global = doc.getGlobalOptions();\n            (0, inputValidator_1.default)(doc, global, document, current);\n            var options = (0, polyfills_1.assign)({}, global, document, current);\n            var win;\n            if (typeof window !== 'undefined') {\n              win = window;\n            }\n            var styles = parseStyles(global, document, current);\n            var hooks = parseHooks(global, document, current);\n            var settings = parseSettings(doc, options);\n            var content = parseContent(doc, options, win);\n            return {\n              id: current.tableId,\n              content: content,\n              hooks: hooks,\n              styles: styles,\n              settings: settings\n            };\n          }\n          exports.parseInput = parseInput;\n          function parseStyles(gInput, dInput, cInput) {\n            var styleOptions = {\n              styles: {},\n              headStyles: {},\n              bodyStyles: {},\n              footStyles: {},\n              alternateRowStyles: {},\n              columnStyles: {}\n            };\n            var _loop_1 = function (prop) {\n              if (prop === 'columnStyles') {\n                var global_1 = gInput[prop];\n                var document_1 = dInput[prop];\n                var current = cInput[prop];\n                styleOptions.columnStyles = (0, polyfills_1.assign)({}, global_1, document_1, current);\n              } else {\n                var allOptions = [gInput, dInput, cInput];\n                var styles = allOptions.map(function (opts) {\n                  return opts[prop] || {};\n                });\n                styleOptions[prop] = (0, polyfills_1.assign)({}, styles[0], styles[1], styles[2]);\n              }\n            };\n            for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n              var prop = _a[_i];\n              _loop_1(prop);\n            }\n            return styleOptions;\n          }\n          function parseHooks(global, document, current) {\n            var allOptions = [global, document, current];\n            var result = {\n              didParseCell: [],\n              willDrawCell: [],\n              didDrawCell: [],\n              didDrawPage: []\n            };\n            for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n              var options = allOptions_1[_i];\n              if (options.didParseCell) result.didParseCell.push(options.didParseCell);\n              if (options.willDrawCell) result.willDrawCell.push(options.willDrawCell);\n              if (options.didDrawCell) result.didDrawCell.push(options.didDrawCell);\n              if (options.didDrawPage) result.didDrawPage.push(options.didDrawPage);\n            }\n            return result;\n          }\n          function parseSettings(doc, options) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n            var margin = (0, common_1.parseSpacing)(options.margin, 40 / doc.scaleFactor());\n            var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n            var showFoot;\n            if (options.showFoot === true) {\n              showFoot = 'everyPage';\n            } else if (options.showFoot === false) {\n              showFoot = 'never';\n            } else {\n              showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n            }\n            var showHead;\n            if (options.showHead === true) {\n              showHead = 'everyPage';\n            } else if (options.showHead === false) {\n              showHead = 'never';\n            } else {\n              showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n            }\n            var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n            var theme = options.theme || (useCss ? 'plain' : 'striped');\n            var horizontalPageBreak = options.horizontalPageBreak ? true : false;\n            var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n            return {\n              includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n              useCss: useCss,\n              theme: theme,\n              startY: startY,\n              margin: margin,\n              pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n              rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n              tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n              showHead: showHead,\n              showFoot: showFoot,\n              tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n              tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n              horizontalPageBreak: horizontalPageBreak,\n              horizontalPageBreakRepeat: horizontalPageBreakRepeat\n            };\n          }\n          function getStartY(doc, userStartY) {\n            var previous = doc.getLastAutoTable();\n            var sf = doc.scaleFactor();\n            var currentPage = doc.pageNumber();\n            var isSamePageAsPreviousTable = false;\n            if (previous && previous.startPageNumber) {\n              var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n              isSamePageAsPreviousTable = endingPage === currentPage;\n            }\n            if (typeof userStartY === 'number') {\n              return userStartY;\n            } else if (userStartY == null || userStartY === false) {\n              if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n                // Some users had issues with overlapping tables when they used multiple\n                // tables without setting startY so setting it here to a sensible default.\n                return previous.finalY + 20 / sf;\n              }\n            }\n            return null;\n          }\n          function parseContent(doc, options, window) {\n            var head = options.head || [];\n            var body = options.body || [];\n            var foot = options.foot || [];\n            if (options.html) {\n              var hidden = options.includeHiddenHtml;\n              if (window) {\n                var htmlContent = (0, htmlParser_1.parseHtml)(doc, options.html, window, hidden, options.useCss) || {};\n                head = htmlContent.head || head;\n                body = htmlContent.body || head;\n                foot = htmlContent.foot || head;\n              } else {\n                console.error('Cannot parse html in non browser environment');\n              }\n            }\n            var columns = options.columns || parseColumns(head, body, foot);\n            return {\n              columns: columns,\n              head: head,\n              body: body,\n              foot: foot\n            };\n          }\n          function parseColumns(head, body, foot) {\n            var firstRow = head[0] || body[0] || foot[0] || [];\n            var result = [];\n            Object.keys(firstRow).filter(function (key) {\n              return key !== '_element';\n            }).forEach(function (key) {\n              var colSpan = 1;\n              var input;\n              if (Array.isArray(firstRow)) {\n                input = firstRow[parseInt(key)];\n              } else {\n                input = firstRow[key];\n              }\n              if (typeof input === 'object' && !Array.isArray(input)) {\n                colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n              }\n              for (var i = 0; i < colSpan; i++) {\n                var id = void 0;\n                if (Array.isArray(firstRow)) {\n                  id = result.length;\n                } else {\n                  id = key + (i > 0 ? \"_\".concat(i) : '');\n                }\n                var rowResult = {\n                  dataKey: id\n                };\n                result.push(rowResult);\n              }\n            });\n            return result;\n          }\n\n          /***/\n        },\n\n        /***/291: /***/function (__unused_webpack_module, exports) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          function default_1(doc, global, document, current) {\n            var _loop_1 = function (options) {\n              if (options && typeof options !== 'object') {\n                console.error('The options parameter should be of type object, is: ' + typeof options);\n              }\n              if (typeof options.extendWidth !== 'undefined') {\n                options.tableWidth = options.extendWidth ? 'auto' : 'wrap';\n                console.error('Use of deprecated option: extendWidth, use tableWidth instead.');\n              }\n              if (typeof options.margins !== 'undefined') {\n                if (typeof options.margin === 'undefined') options.margin = options.margins;\n                console.error('Use of deprecated option: margins, use margin instead.');\n              }\n              if (options.startY && typeof options.startY !== 'number') {\n                console.error('Invalid value for startY option', options.startY);\n                delete options.startY;\n              }\n              if (!options.didDrawPage && (options.afterPageContent || options.beforePageContent || options.afterPageAdd)) {\n                console.error('The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead');\n                options.didDrawPage = function (data) {\n                  doc.applyStyles(doc.userStyles);\n                  if (options.beforePageContent) options.beforePageContent(data);\n                  doc.applyStyles(doc.userStyles);\n                  if (options.afterPageContent) options.afterPageContent(data);\n                  doc.applyStyles(doc.userStyles);\n                  if (options.afterPageAdd && data.pageNumber > 1) {\n                    ;\n                    data.afterPageAdd(data);\n                  }\n                  doc.applyStyles(doc.userStyles);\n                };\n              }\n              ;\n              ['createdHeaderCell', 'drawHeaderRow', 'drawRow', 'drawHeaderCell'].forEach(function (name) {\n                if (options[name]) {\n                  console.error(\"The \\\"\".concat(name, \"\\\" hook has changed in version 3.0, check the changelog for how to migrate.\"));\n                }\n              });\n              [['showFoot', 'showFooter'], ['showHead', 'showHeader'], ['didDrawPage', 'addPageContent'], ['didParseCell', 'createdCell'], ['headStyles', 'headerStyles']].forEach(function (_a) {\n                var current = _a[0],\n                  deprecated = _a[1];\n                if (options[deprecated]) {\n                  console.error(\"Use of deprecated option \".concat(deprecated, \". Use \").concat(current, \" instead\"));\n                  options[current] = options[deprecated];\n                }\n              });\n              [['padding', 'cellPadding'], ['lineHeight', 'rowHeight'], 'fontSize', 'overflow'].forEach(function (o) {\n                var deprecatedOption = typeof o === 'string' ? o : o[0];\n                var style = typeof o === 'string' ? o : o[1];\n                if (typeof options[deprecatedOption] !== 'undefined') {\n                  if (typeof options.styles[style] === 'undefined') {\n                    options.styles[style] = options[deprecatedOption];\n                  }\n                  console.error('Use of deprecated option: ' + deprecatedOption + ', use the style ' + style + ' instead.');\n                }\n              });\n              for (var _b = 0, _c = ['styles', 'bodyStyles', 'headStyles', 'footStyles']; _b < _c.length; _b++) {\n                var styleProp = _c[_b];\n                checkStyles(options[styleProp] || {});\n              }\n              var columnStyles = options['columnStyles'] || {};\n              for (var _d = 0, _e = Object.keys(columnStyles); _d < _e.length; _d++) {\n                var key = _e[_d];\n                checkStyles(columnStyles[key] || {});\n              }\n            };\n            for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n              var options = _a[_i];\n              _loop_1(options);\n            }\n          }\n          exports[\"default\"] = default_1;\n          function checkStyles(styles) {\n            if (styles.rowHeight) {\n              console.error('Use of deprecated style rowHeight. It is renamed to minCellHeight.');\n              if (!styles.minCellHeight) {\n                styles.minCellHeight = styles.rowHeight;\n              }\n            } else if (styles.columnWidth) {\n              console.error('Use of deprecated style columnWidth. It is renamed to cellWidth.');\n              if (!styles.cellWidth) {\n                styles.cellWidth = styles.columnWidth;\n              }\n            }\n          }\n\n          /***/\n        },\n\n        /***/287: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.Column = exports.Cell = exports.Row = exports.Table = void 0;\n          var config_1 = __webpack_require__(913);\n          var HookData_1 = __webpack_require__(662);\n          var common_1 = __webpack_require__(200);\n          var Table = /** @class */function () {\n            function Table(input, content) {\n              this.pageNumber = 1;\n              // Deprecated, use pageNumber instead\n              // Not using getter since:\n              // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/596\n              this.pageCount = 1;\n              this.id = input.id;\n              this.settings = input.settings;\n              this.styles = input.styles;\n              this.hooks = input.hooks;\n              this.columns = content.columns;\n              this.head = content.head;\n              this.body = content.body;\n              this.foot = content.foot;\n            }\n            Table.prototype.getHeadHeight = function (columns) {\n              return this.head.reduce(function (acc, row) {\n                return acc + row.getMaxCellHeight(columns);\n              }, 0);\n            };\n            Table.prototype.getFootHeight = function (columns) {\n              return this.foot.reduce(function (acc, row) {\n                return acc + row.getMaxCellHeight(columns);\n              }, 0);\n            };\n            Table.prototype.allRows = function () {\n              return this.head.concat(this.body).concat(this.foot);\n            };\n            Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n              for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n                var handler = handlers_1[_i];\n                var data = new HookData_1.CellHookData(doc, this, cell, row, column, cursor);\n                var result = handler(data) === false;\n                // Make sure text is always string[] since user can assign string\n                cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n                if (result) {\n                  return false;\n                }\n              }\n              return true;\n            };\n            Table.prototype.callEndPageHooks = function (doc, cursor) {\n              doc.applyStyles(doc.userStyles);\n              for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n                var handler = _a[_i];\n                handler(new HookData_1.HookData(doc, this, cursor));\n              }\n            };\n            Table.prototype.getWidth = function (pageWidth) {\n              if (typeof this.settings.tableWidth === 'number') {\n                return this.settings.tableWidth;\n              } else if (this.settings.tableWidth === 'wrap') {\n                var wrappedWidth = this.columns.reduce(function (total, col) {\n                  return total + col.wrappedWidth;\n                }, 0);\n                return wrappedWidth;\n              } else {\n                var margin = this.settings.margin;\n                return pageWidth - margin.left - margin.right;\n              }\n            };\n            return Table;\n          }();\n          exports.Table = Table;\n          var Row = /** @class */function () {\n            function Row(raw, index, section, cells, spansMultiplePages) {\n              if (spansMultiplePages === void 0) {\n                spansMultiplePages = false;\n              }\n              this.height = 0;\n              this.raw = raw;\n              if (raw instanceof config_1.HtmlRowInput) {\n                this.raw = raw._element;\n                this.element = raw._element;\n              }\n              this.index = index;\n              this.section = section;\n              this.cells = cells;\n              this.spansMultiplePages = spansMultiplePages;\n            }\n            Row.prototype.getMaxCellHeight = function (columns) {\n              var _this = this;\n              return columns.reduce(function (acc, column) {\n                var _a;\n                return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0);\n              }, 0);\n            };\n            Row.prototype.hasRowSpan = function (columns) {\n              var _this = this;\n              return columns.filter(function (column) {\n                var cell = _this.cells[column.index];\n                if (!cell) return false;\n                return cell.rowSpan > 1;\n              }).length > 0;\n            };\n            Row.prototype.canEntireRowFit = function (height, columns) {\n              return this.getMaxCellHeight(columns) <= height;\n            };\n            Row.prototype.getMinimumRowHeight = function (columns, doc) {\n              var _this = this;\n              return columns.reduce(function (acc, column) {\n                var cell = _this.cells[column.index];\n                if (!cell) return 0;\n                var fontHeight = cell.styles.fontSize / doc.scaleFactor() * config_1.FONT_ROW_RATIO;\n                var vPadding = cell.padding('vertical');\n                var oneRowHeight = vPadding + fontHeight;\n                return oneRowHeight > acc ? oneRowHeight : acc;\n              }, 0);\n            };\n            return Row;\n          }();\n          exports.Row = Row;\n          var Cell = /** @class */function () {\n            function Cell(raw, styles, section) {\n              var _a, _b;\n              this.contentHeight = 0;\n              this.contentWidth = 0;\n              this.wrappedWidth = 0;\n              this.minReadableWidth = 0;\n              this.minWidth = 0;\n              this.width = 0;\n              this.height = 0;\n              this.x = 0;\n              this.y = 0;\n              this.styles = styles;\n              this.section = section;\n              this.raw = raw;\n              var content = raw;\n              if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n                this.rowSpan = raw.rowSpan || 1;\n                this.colSpan = raw.colSpan || 1;\n                content = (_b = (_a = raw.content) !== null && _a !== void 0 ? _a : raw.title) !== null && _b !== void 0 ? _b : raw;\n                if (raw._element) {\n                  this.raw = raw._element;\n                }\n              } else {\n                this.rowSpan = 1;\n                this.colSpan = 1;\n              }\n              // Stringify 0 and false, but not undefined or null\n              var text = content != null ? '' + content : '';\n              var splitRegex = /\\r\\n|\\r|\\n/g;\n              this.text = text.split(splitRegex);\n            }\n            Cell.prototype.getTextPos = function () {\n              var y;\n              if (this.styles.valign === 'top') {\n                y = this.y + this.padding('top');\n              } else if (this.styles.valign === 'bottom') {\n                y = this.y + this.height - this.padding('bottom');\n              } else {\n                var netHeight = this.height - this.padding('vertical');\n                y = this.y + netHeight / 2 + this.padding('top');\n              }\n              var x;\n              if (this.styles.halign === 'right') {\n                x = this.x + this.width - this.padding('right');\n              } else if (this.styles.halign === 'center') {\n                var netWidth = this.width - this.padding('horizontal');\n                x = this.x + netWidth / 2 + this.padding('left');\n              } else {\n                x = this.x + this.padding('left');\n              }\n              return {\n                x: x,\n                y: y\n              };\n            };\n            Cell.prototype.getContentHeight = function (scaleFactor) {\n              var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n              var fontHeight = this.styles.fontSize / scaleFactor * config_1.FONT_ROW_RATIO;\n              var height = lineCount * fontHeight + this.padding('vertical');\n              return Math.max(height, this.styles.minCellHeight);\n            };\n            Cell.prototype.padding = function (name) {\n              var padding = (0, common_1.parseSpacing)(this.styles.cellPadding, 0);\n              if (name === 'vertical') {\n                return padding.top + padding.bottom;\n              } else if (name === 'horizontal') {\n                return padding.left + padding.right;\n              } else {\n                return padding[name];\n              }\n            };\n            return Cell;\n          }();\n          exports.Cell = Cell;\n          var Column = /** @class */function () {\n            function Column(dataKey, raw, index) {\n              this.wrappedWidth = 0;\n              this.minReadableWidth = 0;\n              this.minWidth = 0;\n              this.width = 0;\n              this.dataKey = dataKey;\n              this.raw = raw;\n              this.index = index;\n            }\n            Column.prototype.getMaxCustomCellWidth = function (table) {\n              var max = 0;\n              for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n                var row = _a[_i];\n                var cell = row.cells[this.index];\n                if (cell && typeof cell.styles.cellWidth === 'number') {\n                  max = Math.max(max, cell.styles.cellWidth);\n                }\n              }\n              return max;\n            };\n            return Column;\n          }();\n          exports.Column = Column;\n\n          /***/\n        },\n\n        /***/360: /***/function (__unused_webpack_module, exports) {\n          /* eslint-disable @typescript-eslint/no-unused-vars */\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.assign = void 0;\n          // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n          function assign(target, s, s1, s2, s3) {\n            if (target == null) {\n              throw new TypeError('Cannot convert undefined or null to object');\n            }\n            var to = Object(target);\n            for (var index = 1; index < arguments.length; index++) {\n              // eslint-disable-next-line prefer-rest-params\n              var nextSource = arguments[index];\n              if (nextSource != null) {\n                // Skip over if undefined or null\n                for (var nextKey in nextSource) {\n                  // Avoid bugs when hasOwnProperty is shadowed\n                  if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                  }\n                }\n              }\n            }\n            return to;\n          }\n          exports.assign = assign;\n\n          /***/\n        },\n\n        /***/858: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.createTable = void 0;\n          var documentHandler_1 = __webpack_require__(323);\n          var models_1 = __webpack_require__(287);\n          var widthCalculator_1 = __webpack_require__(189);\n          var config_1 = __webpack_require__(913);\n          var polyfills_1 = __webpack_require__(360);\n          function createTable(jsPDFDoc, input) {\n            var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n            var content = parseContent(input, doc.scaleFactor());\n            var table = new models_1.Table(input, content);\n            (0, widthCalculator_1.calculateWidths)(doc, table);\n            doc.applyStyles(doc.userStyles);\n            return table;\n          }\n          exports.createTable = createTable;\n          function parseContent(input, sf) {\n            var content = input.content;\n            var columns = createColumns(content.columns);\n            // If no head or foot is set, try generating it with content from columns\n            if (content.head.length === 0) {\n              var sectionRow = generateSectionRow(columns, 'head');\n              if (sectionRow) content.head.push(sectionRow);\n            }\n            if (content.foot.length === 0) {\n              var sectionRow = generateSectionRow(columns, 'foot');\n              if (sectionRow) content.foot.push(sectionRow);\n            }\n            var theme = input.settings.theme;\n            var styles = input.styles;\n            return {\n              columns: columns,\n              head: parseSection('head', content.head, columns, styles, theme, sf),\n              body: parseSection('body', content.body, columns, styles, theme, sf),\n              foot: parseSection('foot', content.foot, columns, styles, theme, sf)\n            };\n          }\n          function parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n            var rowSpansLeftForColumn = {};\n            var result = sectionRows.map(function (rawRow, rowIndex) {\n              var skippedRowForRowSpans = 0;\n              var cells = {};\n              var colSpansAdded = 0;\n              var columnSpansLeft = 0;\n              for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n                var column = columns_1[_i];\n                if (rowSpansLeftForColumn[column.index] == null || rowSpansLeftForColumn[column.index].left === 0) {\n                  if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                      rawCell = rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    } else {\n                      rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                      cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new models_1.Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                      left: cell.rowSpan - 1,\n                      times: columnSpansLeft\n                    };\n                  } else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                  }\n                } else {\n                  rowSpansLeftForColumn[column.index].left--;\n                  columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                  skippedRowForRowSpans++;\n                }\n              }\n              return new models_1.Row(rawRow, rowIndex, sectionName, cells);\n            });\n            return result;\n          }\n          function generateSectionRow(columns, section) {\n            var sectionRow = {};\n            columns.forEach(function (col) {\n              if (col.raw != null) {\n                var title = getSectionTitle(section, col.raw);\n                if (title != null) sectionRow[col.dataKey] = title;\n              }\n            });\n            return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n          }\n          function getSectionTitle(section, column) {\n            if (section === 'head') {\n              if (typeof column === 'object') {\n                return column.header || column.title || null;\n              } else if (typeof column === 'string' || typeof column === 'number') {\n                return column;\n              }\n            } else if (section === 'foot' && typeof column === 'object') {\n              return column.footer;\n            }\n            return null;\n          }\n          function createColumns(columns) {\n            return columns.map(function (input, index) {\n              var _a, _b;\n              var key;\n              if (typeof input === 'object') {\n                key = (_b = (_a = input.dataKey) !== null && _a !== void 0 ? _a : input.key) !== null && _b !== void 0 ? _b : index;\n              } else {\n                key = index;\n              }\n              return new models_1.Column(key, input, index);\n            });\n          }\n          function cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n            var theme = (0, config_1.getTheme)(themeName);\n            var sectionStyles;\n            if (sectionName === 'head') {\n              sectionStyles = styles.headStyles;\n            } else if (sectionName === 'body') {\n              sectionStyles = styles.bodyStyles;\n            } else if (sectionName === 'foot') {\n              sectionStyles = styles.footStyles;\n            }\n            var otherStyles = (0, polyfills_1.assign)({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n            var columnStyles = styles.columnStyles[column.dataKey] || styles.columnStyles[column.index] || {};\n            var colStyles = sectionName === 'body' ? columnStyles : {};\n            var rowStyles = sectionName === 'body' && rowIndex % 2 === 0 ? (0, polyfills_1.assign)({}, theme.alternateRow, styles.alternateRowStyles) : {};\n            var defaultStyle = (0, config_1.defaultStyles)(scaleFactor);\n            var themeStyles = (0, polyfills_1.assign)({}, defaultStyle, otherStyles, rowStyles, colStyles);\n            return (0, polyfills_1.assign)(themeStyles, cellInputStyles);\n          }\n\n          /***/\n        },\n\n        /***/49: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.addPage = exports.drawTable = void 0;\n          var config_1 = __webpack_require__(913);\n          var common_1 = __webpack_require__(200);\n          var models_1 = __webpack_require__(287);\n          var documentHandler_1 = __webpack_require__(323);\n          var polyfills_1 = __webpack_require__(360);\n          var autoTableText_1 = __webpack_require__(938);\n          var tablePrinter_1 = __webpack_require__(435);\n          function drawTable(jsPDFDoc, table) {\n            var settings = table.settings;\n            var startY = settings.startY;\n            var margin = settings.margin;\n            var cursor = {\n              x: margin.left,\n              y: startY\n            };\n            var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n            var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n            if (settings.pageBreak === 'avoid') {\n              var rows = table.allRows();\n              var tableHeight = rows.reduce(function (acc, row) {\n                return acc + row.height;\n              }, 0);\n              minTableBottomPos += tableHeight;\n            }\n            var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n            if (settings.pageBreak === 'always' || settings.startY != null && minTableBottomPos > doc.pageSize().height) {\n              nextPage(doc);\n              cursor.y = margin.top;\n            }\n            var startPos = (0, polyfills_1.assign)({}, cursor);\n            table.startPageNumber = doc.pageNumber();\n            if (settings.horizontalPageBreak === true) {\n              // managed flow for split columns\n              printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n            } else {\n              // normal flow\n              doc.applyStyles(doc.userStyles);\n              if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n                table.head.forEach(function (row) {\n                  return printRow(doc, table, row, cursor, table.columns);\n                });\n              }\n              doc.applyStyles(doc.userStyles);\n              table.body.forEach(function (row, index) {\n                var isLastRow = index === table.body.length - 1;\n                printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n              });\n              doc.applyStyles(doc.userStyles);\n              if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n                table.foot.forEach(function (row) {\n                  return printRow(doc, table, row, cursor, table.columns);\n                });\n              }\n            }\n            (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n            table.callEndPageHooks(doc, cursor);\n            table.finalY = cursor.y;\n            jsPDFDoc.lastAutoTable = table;\n            jsPDFDoc.previousAutoTable = table; // Deprecated\n            if (jsPDFDoc.autoTable) jsPDFDoc.autoTable.previous = table; // Deprecated\n            doc.applyStyles(doc.userStyles);\n          }\n          exports.drawTable = drawTable;\n          function printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n            // calculate width of columns and render only those which can fit into page\n            var allColumnsCanFitResult = tablePrinter_1.default.calculateAllColumnsCanFitInPage(doc, table);\n            allColumnsCanFitResult.map(function (colsAndIndexes, index) {\n              doc.applyStyles(doc.userStyles);\n              // add page to print next columns in new page\n              if (index > 0) {\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns);\n              } else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n              }\n              // print body for selected columns\n              printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n              // print foot for selected columns\n              printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n          }\n          function printHead(doc, table, cursor, columns) {\n            var settings = table.settings;\n            doc.applyStyles(doc.userStyles);\n            if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n              table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, columns);\n              });\n            }\n          }\n          function printBody(doc, table, startPos, cursor, columns) {\n            doc.applyStyles(doc.userStyles);\n            table.body.forEach(function (row, index) {\n              var isLastRow = index === table.body.length - 1;\n              printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n            });\n          }\n          function printFoot(doc, table, cursor, columns) {\n            var settings = table.settings;\n            doc.applyStyles(doc.userStyles);\n            if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n              table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, columns);\n              });\n            }\n          }\n          function getRemainingLineCount(cell, remainingPageSpace, doc) {\n            var fontHeight = cell.styles.fontSize / doc.scaleFactor() * config_1.FONT_ROW_RATIO;\n            var vPadding = cell.padding('vertical');\n            var remainingLines = Math.floor((remainingPageSpace - vPadding) / fontHeight);\n            return Math.max(0, remainingLines);\n          }\n          function modifyRowToFit(row, remainingPageSpace, table, doc) {\n            var cells = {};\n            row.spansMultiplePages = true;\n            row.height = 0;\n            var rowHeight = 0;\n            for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n              var column = _a[_i];\n              var cell = row.cells[column.index];\n              if (!cell) continue;\n              if (!Array.isArray(cell.text)) {\n                cell.text = [cell.text];\n              }\n              var remainderCell = new models_1.Cell(cell.raw, cell.styles, cell.section);\n              remainderCell = (0, polyfills_1.assign)(remainderCell, cell);\n              remainderCell.text = [];\n              var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n              if (cell.text.length > remainingLineCount) {\n                remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n              }\n              var scaleFactor = doc.scaleFactor();\n              cell.contentHeight = cell.getContentHeight(scaleFactor);\n              if (cell.contentHeight >= remainingPageSpace) {\n                cell.contentHeight = remainingPageSpace;\n                remainderCell.styles.minCellHeight -= remainingPageSpace;\n              }\n              if (cell.contentHeight > row.height) {\n                row.height = cell.contentHeight;\n              }\n              remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor);\n              if (remainderCell.contentHeight > rowHeight) {\n                rowHeight = remainderCell.contentHeight;\n              }\n              cells[column.index] = remainderCell;\n            }\n            var remainderRow = new models_1.Row(row.raw, -1, row.section, cells, true);\n            remainderRow.height = rowHeight;\n            for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n              var column = _c[_b];\n              var remainderCell = remainderRow.cells[column.index];\n              if (remainderCell) {\n                remainderCell.height = remainderRow.height;\n              }\n              var cell = row.cells[column.index];\n              if (cell) {\n                cell.height = row.height;\n              }\n            }\n            return remainderRow;\n          }\n          function shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n            var pageHeight = doc.pageSize().height;\n            var margin = table.settings.margin;\n            var marginHeight = margin.top + margin.bottom;\n            var maxRowHeight = pageHeight - marginHeight;\n            if (row.section === 'body') {\n              // Should also take into account that head and foot is not\n              // on every page with some settings\n              maxRowHeight -= table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n            }\n            var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n            var minRowFits = minRowHeight < remainingPageSpace;\n            if (minRowHeight > maxRowHeight) {\n              console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n              return true;\n            }\n            if (!minRowFits) {\n              return false;\n            }\n            var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n            var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n            if (rowHigherThanPage) {\n              if (rowHasRowSpanCell) {\n                console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n              }\n              return true;\n            }\n            if (rowHasRowSpanCell) {\n              // Currently a new page is required whenever a rowspan row don't fit a page.\n              return false;\n            }\n            if (table.settings.rowPageBreak === 'avoid') {\n              return false;\n            }\n            // In all other cases print the row on current page\n            return true;\n          }\n          function printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n            var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n            if (row.canEntireRowFit(remainingSpace, columns)) {\n              printRow(doc, table, row, cursor, columns);\n            } else {\n              if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n                var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n                printRow(doc, table, row, cursor, columns);\n                addPage(doc, table, startPos, cursor, columns);\n                printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n              } else {\n                addPage(doc, table, startPos, cursor, columns);\n                printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n              }\n            }\n          }\n          function printRow(doc, table, row, cursor, columns) {\n            cursor.x = table.settings.margin.left;\n            for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n              var column = columns_1[_i];\n              var cell = row.cells[column.index];\n              if (!cell) {\n                cursor.x += column.width;\n                continue;\n              }\n              doc.applyStyles(cell.styles);\n              cell.x = cursor.x;\n              cell.y = cursor.y;\n              var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n              if (result === false) {\n                cursor.x += column.width;\n                continue;\n              }\n              drawCellBorders(doc, cell, cursor);\n              var textPos = cell.getTextPos();\n              (0, autoTableText_1.default)(cell.text, textPos.x, textPos.y, {\n                halign: cell.styles.halign,\n                valign: cell.styles.valign,\n                maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right'))\n              }, doc.getDocument());\n              table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n              cursor.x += column.width;\n            }\n            cursor.y += row.height;\n          }\n          function drawCellBorders(doc, cell, cursor) {\n            var cellStyles = cell.styles;\n            doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n            if (typeof cellStyles.lineWidth === 'number') {\n              // prints normal cell border\n              var fillStyle = (0, common_1.getFillStyle)(cellStyles.lineWidth, cellStyles.fillColor);\n              if (fillStyle) {\n                doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n              }\n            } else if (typeof cellStyles.lineWidth === 'object') {\n              doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n              var sides = Object.keys(cellStyles.lineWidth);\n              var lineWidth_1 = cellStyles.lineWidth;\n              sides.map(function (side) {\n                var fillStyle = (0, common_1.getFillStyle)(lineWidth_1[side], cellStyles.fillColor);\n                drawBorderForSide(doc, cell, cursor, side, fillStyle || 'S', lineWidth_1[side]);\n              });\n            }\n          }\n          function drawBorderForSide(doc, cell, cursor, side, fillStyle, lineWidth) {\n            var x1, y1, x2, y2;\n            switch (side) {\n              case 'top':\n                x1 = cursor.x;\n                y1 = cursor.y;\n                x2 = cursor.x + cell.width;\n                y2 = cursor.y;\n                break;\n              case 'left':\n                x1 = cursor.x;\n                y1 = cursor.y;\n                x2 = cursor.x;\n                y2 = cursor.y + cell.height;\n                break;\n              case 'right':\n                x1 = cursor.x + cell.width;\n                y1 = cursor.y;\n                x2 = cursor.x + cell.width;\n                y2 = cursor.y + cell.height;\n                break;\n              default:\n                // default it will print bottom\n                x1 = cursor.x;\n                y1 = cursor.y + cell.height - lineWidth;\n                x2 = cursor.x + cell.width;\n                y2 = cursor.y + cell.height - lineWidth;\n                break;\n            }\n            doc.getDocument().setLineWidth(lineWidth);\n            doc.getDocument().line(x1, y1, x2, y2, fillStyle);\n          }\n          function getRemainingPageSpace(doc, table, isLastRow, cursor) {\n            var bottomContentHeight = table.settings.margin.bottom;\n            var showFoot = table.settings.showFoot;\n            if (showFoot === 'everyPage' || showFoot === 'lastPage' && isLastRow) {\n              bottomContentHeight += table.getFootHeight(table.columns);\n            }\n            return doc.pageSize().height - cursor.y - bottomContentHeight;\n          }\n          function addPage(doc, table, startPos, cursor, columns) {\n            if (columns === void 0) {\n              columns = [];\n            }\n            doc.applyStyles(doc.userStyles);\n            if (table.settings.showFoot === 'everyPage') {\n              table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, columns);\n              });\n            }\n            // Add user content just before adding new page ensure it will\n            // be drawn above other things on the page\n            table.callEndPageHooks(doc, cursor);\n            var margin = table.settings.margin;\n            (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n            nextPage(doc);\n            table.pageNumber++;\n            table.pageCount++;\n            cursor.x = margin.left;\n            cursor.y = margin.top;\n            startPos.y = margin.top;\n            if (table.settings.showHead === 'everyPage') {\n              table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, columns);\n              });\n            }\n          }\n          exports.addPage = addPage;\n          function nextPage(doc) {\n            var current = doc.pageNumber();\n            doc.setPage(current + 1);\n            var newCurrent = doc.pageNumber();\n            if (newCurrent === current) {\n              doc.addPage();\n            }\n          }\n\n          /***/\n        },\n\n        /***/435: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          var common_1 = __webpack_require__(200);\n          var getPageAvailableWidth = function (doc, table) {\n            var margins = (0, common_1.parseSpacing)(table.settings.margin, 0);\n            var availablePageWidth = doc.pageSize().width - (margins.left + margins.right);\n            return availablePageWidth;\n          };\n          // get columns can be fit into page\n          var getColumnsCanFitInPage = function (doc, table, config) {\n            if (config === void 0) {\n              config = {};\n            }\n            // get page width\n            var availablePageWidth = getPageAvailableWidth(doc, table);\n            var remainingWidth = availablePageWidth;\n            // get column data key to repeat\n            var horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n            var repeatColumn = null;\n            var cols = [];\n            var columns = [];\n            var len = table.columns.length;\n            var i = config && config.start ? config.start : 0;\n            // code to repeat the given column in split pages\n            if (horizontalPageBreakRepeat != null) {\n              repeatColumn = table.columns.find(function (item) {\n                return item.dataKey === horizontalPageBreakRepeat || item.index === horizontalPageBreakRepeat;\n              });\n              if (repeatColumn) {\n                cols.push(repeatColumn.index);\n                columns.push(table.columns[repeatColumn.index]);\n                remainingWidth = remainingWidth - repeatColumn.wrappedWidth;\n              }\n            }\n            while (i < len) {\n              if ((repeatColumn === null || repeatColumn === void 0 ? void 0 : repeatColumn.index) === i) {\n                i++; // prevent columnDataKeyToRepeat to be pushed twice in a page\n                continue;\n              }\n              var colWidth = table.columns[i].wrappedWidth;\n              if (remainingWidth < colWidth) {\n                // check if it's first column in the sequence then add it into result\n                if (i === 0 || i === config.start) {\n                  // this cell width is more than page width set it available pagewidth\n                  /* table.columns[i].wrappedWidth = availablePageWidth\n                  table.columns[i].minWidth = availablePageWidth */\n                  cols.push(i);\n                  columns.push(table.columns[i]);\n                }\n                // can't print more columns in same page\n                break;\n              }\n              cols.push(i);\n              columns.push(table.columns[i]);\n              remainingWidth = remainingWidth - colWidth;\n              i++;\n            }\n            return {\n              colIndexes: cols,\n              columns: columns,\n              lastIndex: i\n            };\n          };\n          var calculateAllColumnsCanFitInPage = function (doc, table) {\n            // const margins = table.settings.margin;\n            // const availablePageWidth = doc.pageSize().width - (margins.left + margins.right);\n            var allResults = [];\n            var index = 0;\n            var len = table.columns.length;\n            while (index < len) {\n              var result = getColumnsCanFitInPage(doc, table, {\n                start: index === 0 ? 0 : index\n              });\n              if (result && result.columns && result.columns.length) {\n                index = result.lastIndex;\n                allResults.push(result);\n              } else {\n                index++;\n              }\n            }\n            return allResults;\n          };\n          exports[\"default\"] = {\n            getColumnsCanFitInPage: getColumnsCanFitInPage,\n            calculateAllColumnsCanFitInPage: calculateAllColumnsCanFitInPage,\n            getPageAvailableWidth: getPageAvailableWidth\n          };\n\n          /***/\n        },\n\n        /***/189: /***/function (__unused_webpack_module, exports, __webpack_require__) {\n          Object.defineProperty(exports, \"__esModule\", {\n            value: true\n          });\n          exports.ellipsize = exports.resizeColumns = exports.calculateWidths = void 0;\n          var common_1 = __webpack_require__(200);\n          var tablePrinter_1 = __webpack_require__(435);\n          /**\n           * Calculate the column widths\n           */\n          function calculateWidths(doc, table) {\n            calculate(doc, table);\n            var resizableColumns = [];\n            var initialTableWidth = 0;\n            table.columns.forEach(function (column) {\n              var customWidth = column.getMaxCustomCellWidth(table);\n              if (customWidth) {\n                // final column width\n                column.width = customWidth;\n              } else {\n                // initial column width (will be resized)\n                column.width = column.wrappedWidth;\n                resizableColumns.push(column);\n              }\n              initialTableWidth += column.width;\n            });\n            // width difference that needs to be distributed\n            var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n            // first resize attempt: with respect to minReadableWidth and minWidth\n            if (resizeWidth) {\n              resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n                return Math.max(column.minReadableWidth, column.minWidth);\n              });\n            }\n            // second resize attempt: ignore minReadableWidth but respect minWidth\n            if (resizeWidth) {\n              resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n                return column.minWidth;\n              });\n            }\n            resizeWidth = Math.abs(resizeWidth);\n            if (!table.settings.horizontalPageBreak && resizeWidth > 0.1 / doc.scaleFactor()) {\n              // Table can't get smaller due to custom-width or minWidth restrictions\n              // We can't really do much here. Up to user to for example\n              // reduce font size, increase page size or remove custom cell widths\n              // to allow more columns to be reduced in size\n              resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n              console.error(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n            }\n            applyColSpans(table);\n            fitContent(table, doc);\n            applyRowSpans(table);\n          }\n          exports.calculateWidths = calculateWidths;\n          function calculate(doc, table) {\n            var sf = doc.scaleFactor();\n            var horizontalPageBreak = table.settings.horizontalPageBreak;\n            var availablePageWidth = tablePrinter_1.default.getPageAvailableWidth(doc, table);\n            table.allRows().forEach(function (row) {\n              for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n                var column = _a[_i];\n                var cell = row.cells[column.index];\n                if (!cell) continue;\n                var hooks = table.hooks.didParseCell;\n                table.callCellHooks(doc, hooks, cell, row, column, null);\n                var padding = cell.padding('horizontal');\n                cell.contentWidth = (0, common_1.getStringWidth)(cell.text, cell.styles, doc) + padding;\n                var longestWordWidth = (0, common_1.getStringWidth)(cell.text.join(' ').split(/\\s+/), cell.styles, doc);\n                cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n                if (typeof cell.styles.cellWidth === 'number') {\n                  cell.minWidth = cell.styles.cellWidth;\n                  cell.wrappedWidth = cell.styles.cellWidth;\n                } else if (cell.styles.cellWidth === 'wrap' || horizontalPageBreak === true) {\n                  // cell width should not be more than available page width\n                  if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                  } else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                  }\n                } else {\n                  // auto\n                  var defaultMinWidth = 10 / sf;\n                  cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                  cell.wrappedWidth = cell.contentWidth;\n                  if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                  }\n                }\n              }\n            });\n            table.allRows().forEach(function (row) {\n              for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n                var column = _a[_i];\n                var cell = row.cells[column.index];\n                // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n                // Could probably be improved upon however.\n                if (cell && cell.colSpan === 1) {\n                  column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                  column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                  column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n                } else {\n                  // Respect cellWidth set in columnStyles even if there is no cells for this column\n                  // or if the column only have colspan cells. Since the width of colspan cells\n                  // does not affect the width of columns, setting columnStyles cellWidth enables the\n                  // user to at least do it manually.\n                  // Note that this is not perfect for now since for example row and table styles are\n                  // not accounted for\n                  var columnStyles = table.styles.columnStyles[column.dataKey] || table.styles.columnStyles[column.index] || {};\n                  var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                  if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                  }\n                }\n                if (cell) {\n                  // Make sure all columns get at least min width even though width calculations are not based on them\n                  if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                  }\n                  if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                  }\n                }\n              }\n            });\n          }\n          /**\n           * Distribute resizeWidth on passed resizable columns\n           */\n          function resizeColumns(columns, resizeWidth, getMinWidth) {\n            var initialResizeWidth = resizeWidth;\n            var sumWrappedWidth = columns.reduce(function (acc, column) {\n              return acc + column.wrappedWidth;\n            }, 0);\n            for (var i = 0; i < columns.length; i++) {\n              var column = columns[i];\n              var ratio = column.wrappedWidth / sumWrappedWidth;\n              var suggestedChange = initialResizeWidth * ratio;\n              var suggestedWidth = column.width + suggestedChange;\n              var minWidth = getMinWidth(column);\n              var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n              resizeWidth -= newWidth - column.width;\n              column.width = newWidth;\n            }\n            resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n            // Run the resizer again if there's remaining width needs\n            // to be distributed and there're columns that can be resized\n            if (resizeWidth) {\n              var resizableColumns = columns.filter(function (column) {\n                return resizeWidth < 0 ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n              });\n\n              if (resizableColumns.length) {\n                resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n              }\n            }\n            return resizeWidth;\n          }\n          exports.resizeColumns = resizeColumns;\n          function applyRowSpans(table) {\n            var rowSpanCells = {};\n            var colRowSpansLeft = 1;\n            var all = table.allRows();\n            for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n              var row = all[rowIndex];\n              for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n                var column = _a[_i];\n                var data = rowSpanCells[column.index];\n                if (colRowSpansLeft > 1) {\n                  colRowSpansLeft--;\n                  delete row.cells[column.index];\n                } else if (data) {\n                  data.cell.height += row.height;\n                  colRowSpansLeft = data.cell.colSpan;\n                  delete row.cells[column.index];\n                  data.left--;\n                  if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                  }\n                } else {\n                  var cell = row.cells[column.index];\n                  if (!cell) {\n                    continue;\n                  }\n                  cell.height = row.height;\n                  if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = {\n                      cell: cell,\n                      left: left,\n                      row: row\n                    };\n                  }\n                }\n              }\n            }\n          }\n          function applyColSpans(table) {\n            var all = table.allRows();\n            for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n              var row = all[rowIndex];\n              var colSpanCell = null;\n              var combinedColSpanWidth = 0;\n              var colSpansLeft = 0;\n              for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n                var column = table.columns[columnIndex];\n                // Width and colspan\n                colSpansLeft -= 1;\n                if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                  combinedColSpanWidth += column.width;\n                  delete row.cells[column.index];\n                } else if (colSpanCell) {\n                  var cell = colSpanCell;\n                  delete row.cells[column.index];\n                  colSpanCell = null;\n                  cell.width = column.width + combinedColSpanWidth;\n                } else {\n                  var cell = row.cells[column.index];\n                  if (!cell) continue;\n                  colSpansLeft = cell.colSpan;\n                  combinedColSpanWidth = 0;\n                  if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                  }\n                  cell.width = column.width + combinedColSpanWidth;\n                }\n              }\n            }\n          }\n          function fitContent(table, doc) {\n            var rowSpanHeight = {\n              count: 0,\n              height: 0\n            };\n            for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n              var row = _a[_i];\n              for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n                var column = _c[_b];\n                var cell = row.cells[column.index];\n                if (!cell) continue;\n                doc.applyStyles(cell.styles, true);\n                var textSpace = cell.width - cell.padding('horizontal');\n                if (cell.styles.overflow === 'linebreak') {\n                  // Add one pt to textSpace to fix rounding error\n                  cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), {\n                    fontSize: cell.styles.fontSize\n                  });\n                } else if (cell.styles.overflow === 'ellipsize') {\n                  cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n                } else if (cell.styles.overflow === 'hidden') {\n                  cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n                } else if (typeof cell.styles.overflow === 'function') {\n                  var result = cell.styles.overflow(cell.text, textSpace);\n                  if (typeof result === 'string') {\n                    cell.text = [result];\n                  } else {\n                    cell.text = result;\n                  }\n                }\n                cell.contentHeight = cell.getContentHeight(doc.scaleFactor());\n                var realContentHeight = cell.contentHeight / cell.rowSpan;\n                if (cell.rowSpan > 1 && rowSpanHeight.count * rowSpanHeight.height < realContentHeight * cell.rowSpan) {\n                  rowSpanHeight = {\n                    height: realContentHeight,\n                    count: cell.rowSpan\n                  };\n                } else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                  if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                  }\n                }\n                if (realContentHeight > row.height) {\n                  row.height = realContentHeight;\n                }\n              }\n              rowSpanHeight.count--;\n            }\n          }\n          function ellipsize(text, width, styles, doc, overflow) {\n            return text.map(function (str) {\n              return ellipsizeStr(str, width, styles, doc, overflow);\n            });\n          }\n          exports.ellipsize = ellipsize;\n          function ellipsizeStr(text, width, styles, doc, overflow) {\n            var precision = 10000 * doc.scaleFactor();\n            width = Math.ceil(width * precision) / precision;\n            if (width >= (0, common_1.getStringWidth)(text, styles, doc)) {\n              return text;\n            }\n            while (width < (0, common_1.getStringWidth)(text + overflow, styles, doc)) {\n              if (text.length <= 1) {\n                break;\n              }\n              text = text.substring(0, text.length - 1);\n            }\n            return text.trim() + overflow;\n          }\n\n          /***/\n        },\n\n        /***/84: /***/function (module) {\n          if (typeof __WEBPACK_EXTERNAL_MODULE__84__ === 'undefined') {\n            var e = new Error(\"Cannot find module 'undefined'\");\n            e.code = 'MODULE_NOT_FOUND';\n            throw e;\n          }\n          module.exports = __WEBPACK_EXTERNAL_MODULE__84__;\n\n          /***/\n        }\n\n        /******/\n      };\n      /************************************************************************/\n      /******/ // The module cache\n      /******/\n      var __webpack_module_cache__ = {};\n      /******/\n      /******/ // The require function\n      /******/\n      function __webpack_require__(moduleId) {\n        /******/ // Check if module is in cache\n        /******/var cachedModule = __webpack_module_cache__[moduleId];\n        /******/\n        if (cachedModule !== undefined) {\n          /******/return cachedModule.exports;\n          /******/\n        }\n        /******/ // Create a new module (and put it into the cache)\n        /******/\n        var module = __webpack_module_cache__[moduleId] = {\n          /******/ // no module.id needed\n          /******/ // no module.loaded needed\n          /******/exports: {}\n          /******/\n        };\n        /******/\n        /******/ // Execute the module function\n        /******/\n        __webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n        /******/\n        /******/ // Return the exports of the module\n        /******/\n        return module.exports;\n        /******/\n      }\n      /******/\n      /************************************************************************/\n      var __webpack_exports__ = {};\n      // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n      !function () {\n        var exports = __webpack_exports__;\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.Cell = exports.Column = exports.Row = exports.Table = exports.CellHookData = exports.__drawTable = exports.__createTable = exports.applyPlugin = void 0;\n        var applyPlugin_1 = __webpack_require__(790);\n        var inputParser_1 = __webpack_require__(587);\n        var tableDrawer_1 = __webpack_require__(49);\n        var tableCalculator_1 = __webpack_require__(858);\n        var models_1 = __webpack_require__(287);\n        Object.defineProperty(exports, \"Table\", {\n          enumerable: true,\n          get: function () {\n            return models_1.Table;\n          }\n        });\n        var HookData_1 = __webpack_require__(662);\n        Object.defineProperty(exports, \"CellHookData\", {\n          enumerable: true,\n          get: function () {\n            return HookData_1.CellHookData;\n          }\n        });\n        var models_2 = __webpack_require__(287);\n        Object.defineProperty(exports, \"Cell\", {\n          enumerable: true,\n          get: function () {\n            return models_2.Cell;\n          }\n        });\n        Object.defineProperty(exports, \"Column\", {\n          enumerable: true,\n          get: function () {\n            return models_2.Column;\n          }\n        });\n        Object.defineProperty(exports, \"Row\", {\n          enumerable: true,\n          get: function () {\n            return models_2.Row;\n          }\n        });\n        // export { applyPlugin } didn't export applyPlugin\n        // to index.d.ts for some reason\n        function applyPlugin(jsPDF) {\n          (0, applyPlugin_1.default)(jsPDF);\n        }\n        exports.applyPlugin = applyPlugin;\n        function autoTable(d, options) {\n          var input = (0, inputParser_1.parseInput)(d, options);\n          var table = (0, tableCalculator_1.createTable)(d, input);\n          (0, tableDrawer_1.drawTable)(d, table);\n        }\n        // Experimental export\n        function __createTable(d, options) {\n          var input = (0, inputParser_1.parseInput)(d, options);\n          return (0, tableCalculator_1.createTable)(d, input);\n        }\n        exports.__createTable = __createTable;\n        function __drawTable(d, table) {\n          (0, tableDrawer_1.drawTable)(d, table);\n        }\n        exports.__drawTable = __drawTable;\n        try {\n          // eslint-disable-next-line @typescript-eslint/no-var-requires\n          var jsPDF = __webpack_require__(84);\n          // Webpack imported jspdf instead of jsPDF for some reason\n          // while it seemed to work everywhere else.\n          if (jsPDF.jsPDF) jsPDF = jsPDF.jsPDF;\n          applyPlugin(jsPDF);\n        } catch (error) {\n          // Importing jspdf in nodejs environments does not work as of jspdf\n          // 1.5.3 so we need to silence potential errors to support using for example\n          // the nodejs jspdf dist files with the exported applyPlugin\n        }\n        exports[\"default\"] = autoTable;\n      }();\n      /******/\n      return __webpack_exports__;\n      /******/\n    }()\n  );\n});", "map": {"version": 3, "names": ["webpackUniversalModuleDefinition", "root", "factory", "exports", "module", "webpackLoadOptionalExternalModule", "require", "e", "define", "amd", "a", "i", "globalThis", "window", "self", "global", "__WEBPACK_EXTERNAL_MODULE__84__", "__webpack_modules__", "__unused_webpack_module", "__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "defineProperty", "value", "CellHookData", "HookData", "doc", "table", "cursor", "pageNumber", "pageCount", "settings", "getDocument", "_super", "cell", "row", "column", "_this", "section", "__webpack_require__", "htmlParser_1", "autoTableText_1", "documentHandler_1", "inputParser_1", "tableDrawer_1", "tableCalculator_1", "default_1", "jsPDF", "API", "autoTable", "args", "_i", "arguments", "length", "options", "console", "error", "columns", "body", "input", "parseInput", "createTable", "drawTable", "lastAutoTable", "previousAutoTable", "previous", "autoTableText", "text", "x", "y", "styles", "default", "autoTableSetDefaults", "defaults", "<PERSON><PERSON><PERSON><PERSON>", "setDefaults", "autoTableHtmlToJson", "tableElem", "includeHiddenElements", "_a", "parseHtml", "head", "map", "c", "content", "rows", "data", "autoTableEndPosY", "prev", "finalY", "autoTableAddPageContent", "hook", "globalDefaults", "addPageContent", "autoTableAddPage", "addPage", "FONT_ROW_RATIO", "k", "internal", "scaleFactor", "fontSize", "getFontSize", "splitRegex", "splitText", "lineCount", "valign", "halign", "split", "alignSize", "iLine", "getStringUnitWidth", "max<PERSON><PERSON><PERSON>", "align", "parseSpacing", "getFillStyle", "addTableBorder", "getStringWidth", "applyStyles", "textArr", "isArray", "widestLineWidth", "getTextWidth", "reduce", "Math", "max", "startPos", "lineWidth", "tableLineWidth", "lineColor", "tableLineColor", "fillStyle", "rect", "getWidth", "pageSize", "width", "fillColor", "drawLine", "drawBackground", "defaultValue", "_b", "_c", "_d", "top", "right", "bottom", "left", "vertical", "horizontal", "getTheme", "defaultStyles", "HtmlRowInput", "element", "_element", "font", "fontStyle", "overflow", "textColor", "cellPadding", "cellWidth", "minCellHeight", "min<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "themes", "striped", "foot", "alternateRow", "grid", "plain", "parseCss", "common_1", "supportedFonts", "style", "result", "pxScaleFactor", "backgroundColor", "parseColor", "elem", "getComputedStyle", "borderColor", "padding", "parsePadding", "bw", "parseInt", "borderTopWidth", "accepted", "indexOf", "textAlign", "verticalAlign", "res", "isNaN", "parseFontStyle", "fontFamily", "toLowerCase", "fontWeight", "styleGetter", "cssColor", "realColor", "rgba", "match", "color", "alpha", "bg", "parentElement", "val", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "linePadding", "lineHeight", "inputPadding", "n", "jsPDFDocument", "userStyles", "getTextColor", "getFont", "fontName", "getLineWidth", "getDrawColor", "__autoTableDocumentDefaults", "unifyColor", "fontOnly", "setFontStyle", "availableFontStyles", "getFontList", "setFont", "setFontSize", "setFillColor", "apply", "setTextColor", "setDrawColor", "setLineWidth", "splitTextToSize", "size", "opts", "height", "getLastAutoTable", "setPage", "page", "getGlobalOptions", "getDocumentOptions", "getHeight", "pageInfo", "getCurrentPageInfo", "getNumberOfPages", "cssParser_1", "config_1", "includeHiddenHtml", "useCss", "tableElement", "document", "querySelector", "keys", "tagName", "parse<PERSON>ow<PERSON><PERSON>nt", "push", "includeHidden", "resultRow", "cells", "style_1", "display", "cellStyles", "rowSpan", "colSpan", "<PERSON>se<PERSON><PERSON><PERSON><PERSON><PERSON>", "orgCell", "cloneNode", "innerHTML", "replace", "part", "trim", "join", "innerText", "textContent", "polyfills_1", "inputValidator_1", "current", "assign", "win", "parseStyles", "hooks", "parseHooks", "parseSettings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "tableId", "gInput", "dInput", "cInput", "styleOptions", "headStyles", "bodyStyles", "footStyles", "alternateRowStyles", "columnStyles", "_loop_1", "prop", "global_1", "document_1", "allOptions", "didParseCell", "will<PERSON><PERSON><PERSON>ell", "didDrawCell", "didDrawPage", "allOptions_1", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "margin", "startY", "getStartY", "showFoot", "showHead", "theme", "horizontalPageBreak", "horizontalPageBreakRepeat", "pageBreak", "rowPageBreak", "tableWidth", "userStartY", "sf", "currentPage", "isSamePageAsPreviousTable", "startPageNumber", "endingPage", "html", "hidden", "htmlContent", "parseColumns", "firstRow", "filter", "key", "for<PERSON>ach", "concat", "rowResult", "dataKey", "extendWidth", "margins", "after<PERSON>age<PERSON><PERSON>nt", "beforePageContent", "afterPageAdd", "deprecated", "o", "deprecatedOption", "styleProp", "checkStyles", "rowHeight", "columnWidth", "Column", "Cell", "Row", "Table", "HookData_1", "getHeadHeight", "acc", "getMaxCellHeight", "getFootHeight", "allRows", "callCellHooks", "handlers", "handlers_1", "handler", "callEndPageHooks", "pageWidth", "<PERSON><PERSON><PERSON><PERSON>", "total", "col", "raw", "index", "spansMultiplePages", "hasRowSpan", "canEntireRowFit", "getMinimumRowHeight", "fontHeight", "vPadding", "oneRowHeight", "contentHeight", "contentWidth", "minReadableWidth", "min<PERSON><PERSON><PERSON>", "title", "getTextPos", "netHeight", "netWidth", "getContentHeight", "getMaxCustomCellWidth", "target", "s", "s1", "s2", "s3", "to", "nextSource", "<PERSON><PERSON><PERSON>", "models_1", "widthCalculator_1", "jsPDFDoc", "calculateWidths", "createColumns", "sectionRow", "generateSectionRow", "parseSection", "sectionName", "sectionRows", "styleProps", "rowSpansLeftForColumn", "rawRow", "rowIndex", "skippedRowForRowSpans", "colSpansAdded", "columnSpansLeft", "columns_1", "rawCell", "cellInputStyles", "times", "getSectionTitle", "header", "footer", "themeName", "sectionStyles", "otherStyles", "colStyles", "rowStyles", "defaultStyle", "themeStyles", "tablePrinter_1", "sectionsHeight", "minTableBottomPos", "tableHeight", "nextPage", "printTableWithHorizontalPageBreak", "printRow", "isLastRow", "printFullRow", "allColumnsCanFitResult", "calculateAllColumnsCanFitInPage", "colsAndIndexes", "printHead", "printBody", "printFoot", "getRemainingLineCount", "remainingPageSpace", "remainingLines", "floor", "modifyRowToFit", "remainderCell", "remainingLineCount", "splice", "remainderRow", "shouldPrintOnCurrentPage", "pageHeight", "marginHeight", "maxRowHeight", "minRowHeight", "minRowFits", "rowHasRowSpanCell", "rowHigherThanPage", "remainingSpace", "getRemainingPageSpace", "drawCellBorders", "textPos", "ceil", "getFillColor", "sides", "lineWidth_1", "side", "drawBorderForSide", "x1", "y1", "x2", "y2", "line", "bottomContentHeight", "newCurrent", "getPageAvailableWidth", "availablePageWidth", "getColumnsCanFitInPage", "config", "remainingWidth", "repeatColumn", "cols", "len", "start", "find", "item", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colIndexes", "lastIndex", "allResults", "ellipsize", "resizeColumns", "calculate", "resizableColumns", "initialTableWidth", "customWidth", "resizeWidth", "abs", "round", "applyColSpans", "<PERSON><PERSON><PERSON><PERSON>", "applyRowSpans", "longestWordWidth", "defaultMinWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialResizeWidth", "sumWrappedWidth", "ratio", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "newWidth", "rowSpanCells", "colRowSpansLeft", "all", "remaining", "colSpanCell", "combinedColSpanWidth", "colSpansLeft", "columnIndex", "rowSpanHeight", "count", "textSpace", "realContentHeight", "str", "ellipsizeStr", "precision", "substring", "Error", "code", "__webpack_module_cache__", "moduleId", "cachedModule", "undefined", "__webpack_exports__", "__drawTable", "__createTable", "applyPlugin", "applyPlugin_1", "enumerable", "get", "models_2"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js"], "sourcesContent": ["/*!\n * \n *               jsPDF AutoTable plugin v3.5.28\n *\n *               Copyright (c) 2022 <PERSON>, https://github.com/simonben<PERSON><PERSON>/jsPDF-AutoTable\n *               Licensed under the MIT License.\n *               http://opensource.org/licenses/mit-license\n *\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory((function webpackLoadOptionalExternalModule() { try { return require(\"jspdf\"); } catch(e) {} }()));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jspdf\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory((function webpackLoadOptionalExternalModule() { try { return require(\"jspdf\"); } catch(e) {} }())) : factory(root[\"jspdf\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(typeof globalThis !== 'undefined' ? globalThis : typeof this !== 'undefined' ? this : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : global , function(__WEBPACK_EXTERNAL_MODULE__84__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 662:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CellHookData = exports.HookData = void 0;\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.pageCount = this.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nexports.HookData = HookData;\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\nexports.CellHookData = CellHookData;\n\n\n/***/ }),\n\n/***/ 790:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar htmlParser_1 = __webpack_require__(148);\nvar autoTableText_1 = __webpack_require__(938);\nvar documentHandler_1 = __webpack_require__(323);\nvar inputParser_1 = __webpack_require__(587);\nvar tableDrawer_1 = __webpack_require__(49);\nvar tableCalculator_1 = __webpack_require__(858);\nfunction default_1(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options;\n        if (args.length === 1) {\n            options = args[0];\n        }\n        else {\n            console.error('Use of deprecated autoTable initiation');\n            options = args[2] || {};\n            options.columns = args[0];\n            options.body = args[1];\n        }\n        var input = (0, inputParser_1.parseInput)(this, options);\n        var table = (0, tableCalculator_1.createTable)(this, input);\n        (0, tableDrawer_1.drawTable)(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.previousAutoTable = false; // deprecated in v3\n    jsPDF.API.autoTable.previous = false; // deprecated in v3\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        (0, autoTableText_1.default)(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        documentHandler_1.DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        documentHandler_1.DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new documentHandler_1.DocHandler(this);\n        var _a = (0, htmlParser_1.parseHtml)(doc, tableElem, window, includeHiddenElements, false), head = _a.head, body = _a.body;\n        var columns = head[0].map(function (c) { return c.content; });\n        return { columns: columns, rows: body, data: body };\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableEndPosY = function () {\n        console.error('Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.');\n        var prev = this.lastAutoTable;\n        if (prev && prev.finalY) {\n            return prev.finalY;\n        }\n        else {\n            return 0;\n        }\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableAddPageContent = function (hook) {\n        console.error('Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead.');\n        if (!jsPDF.API.autoTable.globalDefaults) {\n            jsPDF.API.autoTable.globalDefaults = {};\n        }\n        jsPDF.API.autoTable.globalDefaults.addPageContent = hook;\n        return this;\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableAddPage = function () {\n        console.error('Use of deprecated function: autoTableAddPage. Use doc.addPage()');\n        this.addPage();\n        return this;\n    };\n}\nexports[\"default\"] = default_1;\n\n\n/***/ }),\n\n/***/ 938:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction default_1(text, x, y, styles, doc) {\n    styles = styles || {};\n    var FONT_ROW_RATIO = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - FONT_ROW_RATIO);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * fontSize * FONT_ROW_RATIO;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * fontSize * FONT_ROW_RATIO;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += fontSize * FONT_ROW_RATIO;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, {\n            maxWidth: styles.maxWidth || 100,\n            align: 'justify',\n        });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\nexports[\"default\"] = default_1;\n\n\n/***/ }),\n\n/***/ 200:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseSpacing = exports.getFillStyle = exports.addTableBorder = exports.getStringWidth = void 0;\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nexports.getStringWidth = getStringWidth;\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nexports.addTableBorder = addTableBorder;\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nexports.getFillStyle = getFillStyle;\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nexports.parseSpacing = parseSpacing;\n\n\n/***/ }),\n\n/***/ 913:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getTheme = exports.defaultStyles = exports.HtmlRowInput = exports.FONT_ROW_RATIO = void 0;\n/**\n * Ratio between font size and font height. The number comes from jspdf's source code\n */\nexports.FONT_ROW_RATIO = 1.15;\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\nexports.HtmlRowInput = HtmlRowInput;\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica',\n        fontStyle: 'normal',\n        overflow: 'linebreak',\n        fillColor: false,\n        textColor: 20,\n        halign: 'left',\n        valign: 'top',\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor,\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto',\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nexports.defaultStyles = defaultStyles;\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: {\n            head: { fontStyle: 'bold' },\n            foot: { fontStyle: 'bold' },\n        },\n    };\n    return themes[name];\n}\nexports.getTheme = getTheme;\n\n\n/***/ }),\n\n/***/ 259:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseCss = void 0;\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nvar common_1 = __webpack_require__(200);\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['borderTopColor'];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    // style.borderWidth only works in chrome (borderTopWidth etc works in firefox and ie as well)\n    var bw = parseInt(style.borderTopWidth || '');\n    bw = bw / pxScaleFactor / scaleFactor;\n    if (bw)\n        result.lineWidth = bw;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nexports.parseCss = parseCss;\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = (0, common_1.parseSpacing)(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\n\n/***/ }),\n\n/***/ 323:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocHandler = void 0;\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle)\n            this.jsPDFDocument.setFontStyle &&\n                this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle &&\n                    this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = {\n                width: pageSize.getWidth(),\n                height: pageSize.getHeight(),\n            };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\nexports.DocHandler = DocHandler;\n\n\n/***/ }),\n\n/***/ 148:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseHtml = void 0;\nvar cssParser_1 = __webpack_require__(259);\nvar config_1 = __webpack_require__(913);\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nexports.parseHtml = parseHtml;\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new config_1.HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = (0, cssParser_1.parseCss)(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/\\<br.*?\\>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\n\n/***/ }),\n\n/***/ 587:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseInput = void 0;\nvar htmlParser_1 = __webpack_require__(148);\nvar polyfills_1 = __webpack_require__(360);\nvar common_1 = __webpack_require__(200);\nvar documentHandler_1 = __webpack_require__(323);\nvar inputValidator_1 = __webpack_require__(291);\nfunction parseInput(d, current) {\n    var doc = new documentHandler_1.DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    (0, inputValidator_1.default)(doc, global, document, current);\n    var options = (0, polyfills_1.assign)({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent(doc, options, win);\n    return {\n        id: current.tableId,\n        content: content,\n        hooks: hooks,\n        styles: styles,\n        settings: settings,\n    };\n}\nexports.parseInput = parseInput;\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = (0, polyfills_1.assign)({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = (0, polyfills_1.assign)({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n    var margin = (0, common_1.parseSpacing)(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = options.horizontalPageBreak\n        ? true\n        : false;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = (0, htmlParser_1.parseHtml)(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return {\n        columns: columns,\n        head: head,\n        body: body,\n        foot: foot,\n    };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\n\n/***/ }),\n\n/***/ 291:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction default_1(doc, global, document, current) {\n    var _loop_1 = function (options) {\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (typeof options.extendWidth !== 'undefined') {\n            options.tableWidth = options.extendWidth ? 'auto' : 'wrap';\n            console.error('Use of deprecated option: extendWidth, use tableWidth instead.');\n        }\n        if (typeof options.margins !== 'undefined') {\n            if (typeof options.margin === 'undefined')\n                options.margin = options.margins;\n            console.error('Use of deprecated option: margins, use margin instead.');\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n        if (!options.didDrawPage &&\n            (options.afterPageContent ||\n                options.beforePageContent ||\n                options.afterPageAdd)) {\n            console.error('The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead');\n            options.didDrawPage = function (data) {\n                doc.applyStyles(doc.userStyles);\n                if (options.beforePageContent)\n                    options.beforePageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageContent)\n                    options.afterPageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageAdd && data.pageNumber > 1) {\n                    ;\n                    data.afterPageAdd(data);\n                }\n                doc.applyStyles(doc.userStyles);\n            };\n        }\n        ;\n        [\n            'createdHeaderCell',\n            'drawHeaderRow',\n            'drawRow',\n            'drawHeaderCell',\n        ].forEach(function (name) {\n            if (options[name]) {\n                console.error(\"The \\\"\".concat(name, \"\\\" hook has changed in version 3.0, check the changelog for how to migrate.\"));\n            }\n        });\n        [\n            ['showFoot', 'showFooter'],\n            ['showHead', 'showHeader'],\n            ['didDrawPage', 'addPageContent'],\n            ['didParseCell', 'createdCell'],\n            ['headStyles', 'headerStyles'],\n        ].forEach(function (_a) {\n            var current = _a[0], deprecated = _a[1];\n            if (options[deprecated]) {\n                console.error(\"Use of deprecated option \".concat(deprecated, \". Use \").concat(current, \" instead\"));\n                options[current] = options[deprecated];\n            }\n        });\n        [\n            ['padding', 'cellPadding'],\n            ['lineHeight', 'rowHeight'],\n            'fontSize',\n            'overflow',\n        ].forEach(function (o) {\n            var deprecatedOption = typeof o === 'string' ? o : o[0];\n            var style = typeof o === 'string' ? o : o[1];\n            if (typeof options[deprecatedOption] !== 'undefined') {\n                if (typeof options.styles[style] === 'undefined') {\n                    options.styles[style] = options[deprecatedOption];\n                }\n                console.error('Use of deprecated option: ' +\n                    deprecatedOption +\n                    ', use the style ' +\n                    style +\n                    ' instead.');\n            }\n        });\n        for (var _b = 0, _c = [\n            'styles',\n            'bodyStyles',\n            'headStyles',\n            'footStyles',\n        ]; _b < _c.length; _b++) {\n            var styleProp = _c[_b];\n            checkStyles(options[styleProp] || {});\n        }\n        var columnStyles = options['columnStyles'] || {};\n        for (var _d = 0, _e = Object.keys(columnStyles); _d < _e.length; _d++) {\n            var key = _e[_d];\n            checkStyles(columnStyles[key] || {});\n        }\n    };\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        _loop_1(options);\n    }\n}\nexports[\"default\"] = default_1;\nfunction checkStyles(styles) {\n    if (styles.rowHeight) {\n        console.error('Use of deprecated style rowHeight. It is renamed to minCellHeight.');\n        if (!styles.minCellHeight) {\n            styles.minCellHeight = styles.rowHeight;\n        }\n    }\n    else if (styles.columnWidth) {\n        console.error('Use of deprecated style columnWidth. It is renamed to cellWidth.');\n        if (!styles.cellWidth) {\n            styles.cellWidth = styles.columnWidth;\n        }\n    }\n}\n\n\n/***/ }),\n\n/***/ 287:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Column = exports.Cell = exports.Row = exports.Table = void 0;\nvar config_1 = __webpack_require__(913);\nvar HookData_1 = __webpack_require__(662);\nvar common_1 = __webpack_require__(200);\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        // Deprecated, use pageNumber instead\n        // Not using getter since:\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/596\n        this.pageCount = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new HookData_1.CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData_1.HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nexports.Table = Table;\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof config_1.HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var fontHeight = (cell.styles.fontSize / doc.scaleFactor()) * config_1.FONT_ROW_RATIO;\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + fontHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nexports.Row = Row;\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a, _b;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_b = (_a = raw.content) !== null && _a !== void 0 ? _a : raw.title) !== null && _b !== void 0 ? _b : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    Cell.prototype.getContentHeight = function (scaleFactor) {\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var fontHeight = (this.styles.fontSize / scaleFactor) * config_1.FONT_ROW_RATIO;\n        var height = lineCount * fontHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = (0, common_1.parseSpacing)(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nexports.Cell = Cell;\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\nexports.Column = Column;\n\n\n/***/ }),\n\n/***/ 360:\n/***/ (function(__unused_webpack_module, exports) {\n\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assign = void 0;\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\nexports.assign = assign;\n\n\n/***/ }),\n\n/***/ 858:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createTable = void 0;\nvar documentHandler_1 = __webpack_require__(323);\nvar models_1 = __webpack_require__(287);\nvar widthCalculator_1 = __webpack_require__(189);\nvar config_1 = __webpack_require__(913);\nvar polyfills_1 = __webpack_require__(360);\nfunction createTable(jsPDFDoc, input) {\n    var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new models_1.Table(input, content);\n    (0, widthCalculator_1.calculateWidths)(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nexports.createTable = createTable;\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new models_1.Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new models_1.Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || column.title || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a, _b;\n        var key;\n        if (typeof input === 'object') {\n            key = (_b = (_a = input.dataKey) !== null && _a !== void 0 ? _a : input.key) !== null && _b !== void 0 ? _b : index;\n        }\n        else {\n            key = index;\n        }\n        return new models_1.Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = (0, config_1.getTheme)(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = (0, polyfills_1.assign)({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? (0, polyfills_1.assign)({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = (0, config_1.defaultStyles)(scaleFactor);\n    var themeStyles = (0, polyfills_1.assign)({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return (0, polyfills_1.assign)(themeStyles, cellInputStyles);\n}\n\n\n/***/ }),\n\n/***/ 49:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addPage = exports.drawTable = void 0;\nvar config_1 = __webpack_require__(913);\nvar common_1 = __webpack_require__(200);\nvar models_1 = __webpack_require__(287);\nvar documentHandler_1 = __webpack_require__(323);\nvar polyfills_1 = __webpack_require__(360);\nvar autoTableText_1 = __webpack_require__(938);\nvar tablePrinter_1 = __webpack_require__(435);\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = {\n        x: margin.left,\n        y: startY,\n    };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.allRows();\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    var startPos = (0, polyfills_1.assign)({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak === true) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    jsPDFDoc.previousAutoTable = table; // Deprecated\n    if (jsPDFDoc.autoTable)\n        jsPDFDoc.autoTable.previous = table; // Deprecated\n    doc.applyStyles(doc.userStyles);\n}\nexports.drawTable = drawTable;\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = tablePrinter_1.default.calculateAllColumnsCanFitInPage(doc, table);\n    allColumnsCanFitResult.map(function (colsAndIndexes, index) {\n        doc.applyStyles(doc.userStyles);\n        // add page to print next columns in new page\n        if (index > 0) {\n            addPage(doc, table, startPos, cursor, colsAndIndexes.columns);\n        }\n        else {\n            // print head for selected columns\n            printHead(doc, table, cursor, colsAndIndexes.columns);\n        }\n        // print body for selected columns\n        printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n        // print foot for selected columns\n        printFoot(doc, table, cursor, colsAndIndexes.columns);\n    });\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var fontHeight = (cell.styles.fontSize / doc.scaleFactor()) * config_1.FONT_ROW_RATIO;\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / fontHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new models_1.Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = (0, polyfills_1.assign)(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new models_1.Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        printRow(doc, table, row, cursor, columns);\n    }\n    else {\n        if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n            var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n            printRow(doc, table, row, cursor, columns);\n            addPage(doc, table, startPos, cursor, columns);\n            printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n        }\n        else {\n            addPage(doc, table, startPos, cursor, columns);\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n        }\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellBorders(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        (0, autoTableText_1.default)(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellBorders(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // prints normal cell border\n        var fillStyle = (0, common_1.getFillStyle)(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        var sides = Object.keys(cellStyles.lineWidth);\n        var lineWidth_1 = cellStyles.lineWidth;\n        sides.map(function (side) {\n            var fillStyle = (0, common_1.getFillStyle)(lineWidth_1[side], cellStyles.fillColor);\n            drawBorderForSide(doc, cell, cursor, side, fillStyle || 'S', lineWidth_1[side]);\n        });\n    }\n}\nfunction drawBorderForSide(doc, cell, cursor, side, fillStyle, lineWidth) {\n    var x1, y1, x2, y2;\n    switch (side) {\n        case 'top':\n            x1 = cursor.x;\n            y1 = cursor.y;\n            x2 = cursor.x + cell.width;\n            y2 = cursor.y;\n            break;\n        case 'left':\n            x1 = cursor.x;\n            y1 = cursor.y;\n            x2 = cursor.x;\n            y2 = cursor.y + cell.height;\n            break;\n        case 'right':\n            x1 = cursor.x + cell.width;\n            y1 = cursor.y;\n            x2 = cursor.x + cell.width;\n            y2 = cursor.y + cell.height;\n            break;\n        default:\n            // default it will print bottom\n            x1 = cursor.x;\n            y1 = cursor.y + cell.height - lineWidth;\n            x2 = cursor.x + cell.width;\n            y2 = cursor.y + cell.height - lineWidth;\n            break;\n    }\n    doc.getDocument().setLineWidth(lineWidth);\n    doc.getDocument().line(x1, y1, x2, y2, fillStyle);\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns) {\n    if (columns === void 0) { columns = []; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    table.pageCount++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nexports.addPage = addPage;\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n    }\n}\n\n\n/***/ }),\n\n/***/ 435:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar common_1 = __webpack_require__(200);\nvar getPageAvailableWidth = function (doc, table) {\n    var margins = (0, common_1.parseSpacing)(table.settings.margin, 0);\n    var availablePageWidth = doc.pageSize().width - (margins.left + margins.right);\n    return availablePageWidth;\n};\n// get columns can be fit into page\nvar getColumnsCanFitInPage = function (doc, table, config) {\n    if (config === void 0) { config = {}; }\n    // get page width\n    var availablePageWidth = getPageAvailableWidth(doc, table);\n    var remainingWidth = availablePageWidth;\n    // get column data key to repeat\n    var horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n    var repeatColumn = null;\n    var cols = [];\n    var columns = [];\n    var len = table.columns.length;\n    var i = config && config.start ? config.start : 0;\n    // code to repeat the given column in split pages\n    if (horizontalPageBreakRepeat != null) {\n        repeatColumn = table.columns.find(function (item) {\n            return item.dataKey === horizontalPageBreakRepeat ||\n                item.index === horizontalPageBreakRepeat;\n        });\n        if (repeatColumn) {\n            cols.push(repeatColumn.index);\n            columns.push(table.columns[repeatColumn.index]);\n            remainingWidth = remainingWidth - repeatColumn.wrappedWidth;\n        }\n    }\n    while (i < len) {\n        if ((repeatColumn === null || repeatColumn === void 0 ? void 0 : repeatColumn.index) === i) {\n            i++; // prevent columnDataKeyToRepeat to be pushed twice in a page\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        if (remainingWidth < colWidth) {\n            // check if it's first column in the sequence then add it into result\n            if (i === 0 || i === config.start) {\n                // this cell width is more than page width set it available pagewidth\n                /* table.columns[i].wrappedWidth = availablePageWidth\n                table.columns[i].minWidth = availablePageWidth */\n                cols.push(i);\n                columns.push(table.columns[i]);\n            }\n            // can't print more columns in same page\n            break;\n        }\n        cols.push(i);\n        columns.push(table.columns[i]);\n        remainingWidth = remainingWidth - colWidth;\n        i++;\n    }\n    return { colIndexes: cols, columns: columns, lastIndex: i };\n};\nvar calculateAllColumnsCanFitInPage = function (doc, table) {\n    // const margins = table.settings.margin;\n    // const availablePageWidth = doc.pageSize().width - (margins.left + margins.right);\n    var allResults = [];\n    var index = 0;\n    var len = table.columns.length;\n    while (index < len) {\n        var result = getColumnsCanFitInPage(doc, table, {\n            start: index === 0 ? 0 : index,\n        });\n        if (result && result.columns && result.columns.length) {\n            index = result.lastIndex;\n            allResults.push(result);\n        }\n        else {\n            index++;\n        }\n    }\n    return allResults;\n};\nexports[\"default\"] = {\n    getColumnsCanFitInPage: getColumnsCanFitInPage,\n    calculateAllColumnsCanFitInPage: calculateAllColumnsCanFitInPage,\n    getPageAvailableWidth: getPageAvailableWidth,\n};\n\n\n/***/ }),\n\n/***/ 189:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ellipsize = exports.resizeColumns = exports.calculateWidths = void 0;\nvar common_1 = __webpack_require__(200);\nvar tablePrinter_1 = __webpack_require__(435);\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.error(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nexports.calculateWidths = calculateWidths;\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = tablePrinter_1.default.getPageAvailableWidth(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = (0, common_1.getStringWidth)(cell.text, cell.styles, doc) + padding;\n            var longestWordWidth = (0, common_1.getStringWidth)(cell.text.join(' ').split(/\\s+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nexports.resizeColumns = resizeColumns;\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nexports.ellipsize = ellipsize;\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= (0, common_1.getStringWidth)(text, styles, doc)) {\n        return text;\n    }\n    while (width < (0, common_1.getStringWidth)(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\n\n/***/ }),\n\n/***/ 84:\n/***/ (function(module) {\n\nif(typeof __WEBPACK_EXTERNAL_MODULE__84__ === 'undefined') { var e = new Error(\"Cannot find module 'undefined'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__84__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\nvar exports = __webpack_exports__;\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Cell = exports.Column = exports.Row = exports.Table = exports.CellHookData = exports.__drawTable = exports.__createTable = exports.applyPlugin = void 0;\nvar applyPlugin_1 = __webpack_require__(790);\nvar inputParser_1 = __webpack_require__(587);\nvar tableDrawer_1 = __webpack_require__(49);\nvar tableCalculator_1 = __webpack_require__(858);\nvar models_1 = __webpack_require__(287);\nObject.defineProperty(exports, \"Table\", ({ enumerable: true, get: function () { return models_1.Table; } }));\nvar HookData_1 = __webpack_require__(662);\nObject.defineProperty(exports, \"CellHookData\", ({ enumerable: true, get: function () { return HookData_1.CellHookData; } }));\nvar models_2 = __webpack_require__(287);\nObject.defineProperty(exports, \"Cell\", ({ enumerable: true, get: function () { return models_2.Cell; } }));\nObject.defineProperty(exports, \"Column\", ({ enumerable: true, get: function () { return models_2.Column; } }));\nObject.defineProperty(exports, \"Row\", ({ enumerable: true, get: function () { return models_2.Row; } }));\n// export { applyPlugin } didn't export applyPlugin\n// to index.d.ts for some reason\nfunction applyPlugin(jsPDF) {\n    (0, applyPlugin_1.default)(jsPDF);\n}\nexports.applyPlugin = applyPlugin;\nfunction autoTable(d, options) {\n    var input = (0, inputParser_1.parseInput)(d, options);\n    var table = (0, tableCalculator_1.createTable)(d, input);\n    (0, tableDrawer_1.drawTable)(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = (0, inputParser_1.parseInput)(d, options);\n    return (0, tableCalculator_1.createTable)(d, input);\n}\nexports.__createTable = __createTable;\nfunction __drawTable(d, table) {\n    (0, tableDrawer_1.drawTable)(d, table);\n}\nexports.__drawTable = __drawTable;\ntry {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    var jsPDF = __webpack_require__(84);\n    // Webpack imported jspdf instead of jsPDF for some reason\n    // while it seemed to work everywhere else.\n    if (jsPDF.jsPDF)\n        jsPDF = jsPDF.jsPDF;\n    applyPlugin(jsPDF);\n}\ncatch (error) {\n    // Importing jspdf in nodejs environments does not work as of jspdf\n    // 1.5.3 so we need to silence potential errors to support using for example\n    // the nodejs jspdf dist files with the exported applyPlugin\n}\nexports[\"default\"] = autoTable;\n\n}();\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,SAASA,gCAAgCA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzD,IAAG,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAC3DA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAE,SAASG,iCAAiCA,CAAA,EAAG;IAAE,IAAI;MAAE,OAAOC,OAAO,CAAC,OAAO,CAAC;IAAE,CAAC,CAAC,OAAMC,CAAC,EAAE,CAAC;EAAE,CAAC,CAAC,CAAE,CAAC,CAAC,KACxH,IAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EACjDD,MAAM,CAAC,CAAC,OAAO,CAAC,EAAEN,OAAO,CAAC,CAAC,KACvB;IACJ,IAAIQ,CAAC,GAAG,OAAOP,OAAO,KAAK,QAAQ,GAAGD,OAAO,CAAE,SAASG,iCAAiCA,CAAA,EAAG;MAAE,IAAI;QAAE,OAAOC,OAAO,CAAC,OAAO,CAAC;MAAE,CAAC,CAAC,OAAMC,CAAC,EAAE,CAAC;IAAE,CAAC,CAAC,CAAE,CAAC,GAAGL,OAAO,CAACD,IAAI,CAAC,OAAO,CAAC,CAAC;IACzK,KAAI,IAAIU,CAAC,IAAID,CAAC,EAAE,CAAC,OAAOP,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGF,IAAI,EAAEU,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;EACzE;AACD,CAAC,EAAE,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAG,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAGC,MAAM,EAAG,UAASC,+BAA+B,EAAE;EAC1N,OAAO,SAAU,YAAW;MAAE;MAC9B;MAAU,YAAY;;MACtB;MAAU,IAAIC,mBAAmB,GAAI;QAErC,KAAM,GAAG,EACT,KAAO,UAASC,uBAAuB,EAAEf,OAAO,EAAE;UAGlD,IAAIgB,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;YACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;cAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;gBAAEC,SAAS,EAAE;cAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;gBAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;cAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;gBAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;cAAE,CAAC;cACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;cACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;cAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;cACnB,SAASW,EAAEA,CAAA,EAAG;gBAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;cAAE;cACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;YACxF,CAAC;UACL,CAAC,CAAE,CAAC;UACJV,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACmC,YAAY,GAAGnC,OAAO,CAACoC,QAAQ,GAAG,KAAK,CAAC;UAChD,IAAIA,QAAQ,GAAG,aAAe,YAAY;YACtC,SAASA,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;cAClC,IAAI,CAACD,KAAK,GAAGA,KAAK;cAClB,IAAI,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU;cAClC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACD,UAAU;cAChC,IAAI,CAACE,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;cAC9B,IAAI,CAACH,MAAM,GAAGA,MAAM;cACpB,IAAI,CAACF,GAAG,GAAGA,GAAG,CAACM,WAAW,CAAC,CAAC;YAChC;YACA,OAAOP,QAAQ;UACnB,CAAC,CAAC,CAAE;UACJpC,OAAO,CAACoC,QAAQ,GAAGA,QAAQ;UAC3B,IAAID,YAAY,GAAG,aAAe,UAAUS,MAAM,EAAE;YAChD5B,SAAS,CAACmB,YAAY,EAAES,MAAM,CAAC;YAC/B,SAAST,YAAYA,CAACE,GAAG,EAAEC,KAAK,EAAEO,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,EAAE;cACzD,IAAIS,KAAK,GAAGJ,MAAM,CAACjB,IAAI,CAAC,IAAI,EAAEU,GAAG,EAAEC,KAAK,EAAEC,MAAM,CAAC,IAAI,IAAI;cACzDS,KAAK,CAACH,IAAI,GAAGA,IAAI;cACjBG,KAAK,CAACF,GAAG,GAAGA,GAAG;cACfE,KAAK,CAACD,MAAM,GAAGA,MAAM;cACrBC,KAAK,CAACC,OAAO,GAAGH,GAAG,CAACG,OAAO;cAC3B,OAAOD,KAAK;YAChB;YACA,OAAOb,YAAY;UACvB,CAAC,CAACC,QAAQ,CAAE;UACZpC,OAAO,CAACmC,YAAY,GAAGA,YAAY;;UAGnC;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAASpB,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/D,IAAIiB,YAAY,GAAGD,mBAAmB,CAAC,GAAG,CAAC;UAC3C,IAAIE,eAAe,GAAGF,mBAAmB,CAAC,GAAG,CAAC;UAC9C,IAAIG,iBAAiB,GAAGH,mBAAmB,CAAC,GAAG,CAAC;UAChD,IAAII,aAAa,GAAGJ,mBAAmB,CAAC,GAAG,CAAC;UAC5C,IAAIK,aAAa,GAAGL,mBAAmB,CAAC,EAAE,CAAC;UAC3C,IAAIM,iBAAiB,GAAGN,mBAAmB,CAAC,GAAG,CAAC;UAChD,SAASO,SAASA,CAACC,KAAK,EAAE;YACtB;YACAA,KAAK,CAACC,GAAG,CAACC,SAAS,GAAG,YAAY;cAC9B,IAAIC,IAAI,GAAG,EAAE;cACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;gBAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;cAC5B;cACA,IAAIG,OAAO;cACX,IAAIJ,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;gBACnBC,OAAO,GAAGJ,IAAI,CAAC,CAAC,CAAC;cACrB,CAAC,MACI;gBACDK,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;gBACvDF,OAAO,GAAGJ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACvBI,OAAO,CAACG,OAAO,GAAGP,IAAI,CAAC,CAAC,CAAC;gBACzBI,OAAO,CAACI,IAAI,GAAGR,IAAI,CAAC,CAAC,CAAC;cAC1B;cACA,IAAIS,KAAK,GAAG,CAAC,CAAC,EAAEhB,aAAa,CAACiB,UAAU,EAAE,IAAI,EAAEN,OAAO,CAAC;cACxD,IAAI3B,KAAK,GAAG,CAAC,CAAC,EAAEkB,iBAAiB,CAACgB,WAAW,EAAE,IAAI,EAAEF,KAAK,CAAC;cAC3D,CAAC,CAAC,EAAEf,aAAa,CAACkB,SAAS,EAAE,IAAI,EAAEnC,KAAK,CAAC;cACzC,OAAO,IAAI;YACf,CAAC;YACD;YACAoB,KAAK,CAACC,GAAG,CAACe,aAAa,GAAG,KAAK;YAC/BhB,KAAK,CAACC,GAAG,CAACgB,iBAAiB,GAAG,KAAK,CAAC,CAAC;YACrCjB,KAAK,CAACC,GAAG,CAACC,SAAS,CAACgB,QAAQ,GAAG,KAAK,CAAC,CAAC;YACtClB,KAAK,CAACC,GAAG,CAACkB,aAAa,GAAG,UAAUC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE;cACpD,CAAC,CAAC,EAAE7B,eAAe,CAAC8B,OAAO,EAAEJ,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE,IAAI,CAAC;YAC1D,CAAC;YACDvB,KAAK,CAACC,GAAG,CAACwB,oBAAoB,GAAG,UAAUC,QAAQ,EAAE;cACjD/B,iBAAiB,CAACgC,UAAU,CAACC,WAAW,CAACF,QAAQ,EAAE,IAAI,CAAC;cACxD,OAAO,IAAI;YACf,CAAC;YACD1B,KAAK,CAACyB,oBAAoB,GAAG,UAAUC,QAAQ,EAAE/C,GAAG,EAAE;cAClDgB,iBAAiB,CAACgC,UAAU,CAACC,WAAW,CAACF,QAAQ,EAAE/C,GAAG,CAAC;YAC3D,CAAC;YACDqB,KAAK,CAACC,GAAG,CAAC4B,mBAAmB,GAAG,UAAUC,SAAS,EAAEC,qBAAqB,EAAE;cACxE,IAAIA,qBAAqB,KAAK,KAAK,CAAC,EAAE;gBAAEA,qBAAqB,GAAG,KAAK;cAAE;cACvE,IAAI,OAAO/E,MAAM,KAAK,WAAW,EAAE;gBAC/BwD,OAAO,CAACC,KAAK,CAAC,2DAA2D,CAAC;gBAC1E,OAAO,IAAI;cACf;cACA,IAAI9B,GAAG,GAAG,IAAIgB,iBAAiB,CAACgC,UAAU,CAAC,IAAI,CAAC;cAChD,IAAIK,EAAE,GAAG,CAAC,CAAC,EAAEvC,YAAY,CAACwC,SAAS,EAAEtD,GAAG,EAAEmD,SAAS,EAAE9E,MAAM,EAAE+E,qBAAqB,EAAE,KAAK,CAAC;gBAAEG,IAAI,GAAGF,EAAE,CAACE,IAAI;gBAAEvB,IAAI,GAAGqB,EAAE,CAACrB,IAAI;cAC1H,IAAID,OAAO,GAAGwB,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;gBAAE,OAAOA,CAAC,CAACC,OAAO;cAAE,CAAC,CAAC;cAC7D,OAAO;gBAAE3B,OAAO,EAAEA,OAAO;gBAAE4B,IAAI,EAAE3B,IAAI;gBAAE4B,IAAI,EAAE5B;cAAK,CAAC;YACvD,CAAC;YACD;AACJ;AACA;YACIX,KAAK,CAACC,GAAG,CAACuC,gBAAgB,GAAG,YAAY;cACrChC,OAAO,CAACC,KAAK,CAAC,qFAAqF,CAAC;cACpG,IAAIgC,IAAI,GAAG,IAAI,CAACzB,aAAa;cAC7B,IAAIyB,IAAI,IAAIA,IAAI,CAACC,MAAM,EAAE;gBACrB,OAAOD,IAAI,CAACC,MAAM;cACtB,CAAC,MACI;gBACD,OAAO,CAAC;cACZ;YACJ,CAAC;YACD;AACJ;AACA;YACI1C,KAAK,CAACC,GAAG,CAAC0C,uBAAuB,GAAG,UAAUC,IAAI,EAAE;cAChDpC,OAAO,CAACC,KAAK,CAAC,uHAAuH,CAAC;cACtI,IAAI,CAACT,KAAK,CAACC,GAAG,CAACC,SAAS,CAAC2C,cAAc,EAAE;gBACrC7C,KAAK,CAACC,GAAG,CAACC,SAAS,CAAC2C,cAAc,GAAG,CAAC,CAAC;cAC3C;cACA7C,KAAK,CAACC,GAAG,CAACC,SAAS,CAAC2C,cAAc,CAACC,cAAc,GAAGF,IAAI;cACxD,OAAO,IAAI;YACf,CAAC;YACD;AACJ;AACA;YACI5C,KAAK,CAACC,GAAG,CAAC8C,gBAAgB,GAAG,YAAY;cACrCvC,OAAO,CAACC,KAAK,CAAC,iEAAiE,CAAC;cAChF,IAAI,CAACuC,OAAO,CAAC,CAAC;cACd,OAAO,IAAI;YACf,CAAC;UACL;UACA1G,OAAO,CAAC,SAAS,CAAC,GAAGyD,SAAS;;UAG9B;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS1C,uBAAuB,EAAEf,OAAO,EAAE;UAGlDoB,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/D;AACA;AACA;AACA;UACA,SAASuB,SAASA,CAACqB,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE5C,GAAG,EAAE;YACxC4C,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;YACrB,IAAI0B,cAAc,GAAG,IAAI;YACzB,IAAIC,CAAC,GAAGvE,GAAG,CAACwE,QAAQ,CAACC,WAAW;YAChC,IAAIC,QAAQ,GAAG1E,GAAG,CAACwE,QAAQ,CAACG,WAAW,CAAC,CAAC,GAAGJ,CAAC;YAC7C,IAAIK,UAAU,GAAG,aAAa;YAC9B,IAAIC,SAAS,GAAG,EAAE;YAClB,IAAIC,SAAS,GAAG,CAAC;YACjB,IAAIlC,MAAM,CAACmC,MAAM,KAAK,QAAQ,IAC1BnC,MAAM,CAACmC,MAAM,KAAK,QAAQ,IAC1BnC,MAAM,CAACoC,MAAM,KAAK,QAAQ,IAC1BpC,MAAM,CAACoC,MAAM,KAAK,OAAO,EAAE;cAC3BH,SAAS,GAAG,OAAOpC,IAAI,KAAK,QAAQ,GAAGA,IAAI,CAACwC,KAAK,CAACL,UAAU,CAAC,GAAGnC,IAAI;cACpEqC,SAAS,GAAGD,SAAS,CAAClD,MAAM,IAAI,CAAC;YACrC;YACA;YACAgB,CAAC,IAAI+B,QAAQ,IAAI,CAAC,GAAGJ,cAAc,CAAC;YACpC,IAAI1B,MAAM,CAACmC,MAAM,KAAK,QAAQ,EAC1BpC,CAAC,IAAKmC,SAAS,GAAG,CAAC,GAAIJ,QAAQ,GAAGJ,cAAc,CAAC,KAChD,IAAI1B,MAAM,CAACmC,MAAM,KAAK,QAAQ,EAC/BpC,CAAC,IAAImC,SAAS,GAAGJ,QAAQ,GAAGJ,cAAc;YAC9C,IAAI1B,MAAM,CAACoC,MAAM,KAAK,QAAQ,IAAIpC,MAAM,CAACoC,MAAM,KAAK,OAAO,EAAE;cACzD,IAAIE,SAAS,GAAGR,QAAQ;cACxB,IAAI9B,MAAM,CAACoC,MAAM,KAAK,QAAQ,EAC1BE,SAAS,IAAI,GAAG;cACpB,IAAIL,SAAS,IAAIC,SAAS,IAAI,CAAC,EAAE;gBAC7B,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,SAAS,CAAClD,MAAM,EAAEwD,KAAK,EAAE,EAAE;kBACnDnF,GAAG,CAACyC,IAAI,CAACoC,SAAS,CAACM,KAAK,CAAC,EAAEzC,CAAC,GAAG1C,GAAG,CAACoF,kBAAkB,CAACP,SAAS,CAACM,KAAK,CAAC,CAAC,GAAGD,SAAS,EAAEvC,CAAC,CAAC;kBACvFA,CAAC,IAAI+B,QAAQ,GAAGJ,cAAc;gBAClC;gBACA,OAAOtE,GAAG;cACd;cACA0C,CAAC,IAAI1C,GAAG,CAACoF,kBAAkB,CAAC3C,IAAI,CAAC,GAAGyC,SAAS;YACjD;YACA,IAAItC,MAAM,CAACoC,MAAM,KAAK,SAAS,EAAE;cAC7BhF,GAAG,CAACyC,IAAI,CAACA,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAE;gBACjB0C,QAAQ,EAAEzC,MAAM,CAACyC,QAAQ,IAAI,GAAG;gBAChCC,KAAK,EAAE;cACX,CAAC,CAAC;YACN,CAAC,MACI;cACDtF,GAAG,CAACyC,IAAI,CAACA,IAAI,EAAEC,CAAC,EAAEC,CAAC,CAAC;YACxB;YACA,OAAO3C,GAAG;UACd;UACArC,OAAO,CAAC,SAAS,CAAC,GAAGyD,SAAS;;UAG9B;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS1C,uBAAuB,EAAEf,OAAO,EAAE;UAGlDoB,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAAC4H,YAAY,GAAG5H,OAAO,CAAC6H,YAAY,GAAG7H,OAAO,CAAC8H,cAAc,GAAG9H,OAAO,CAAC+H,cAAc,GAAG,KAAK,CAAC;UACtG,SAASA,cAAcA,CAACjD,IAAI,EAAEG,MAAM,EAAE5C,GAAG,EAAE;YACvCA,GAAG,CAAC2F,WAAW,CAAC/C,MAAM,EAAE,IAAI,CAAC;YAC7B,IAAIgD,OAAO,GAAG1G,KAAK,CAAC2G,OAAO,CAACpD,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;YACjD,IAAIqD,eAAe,GAAGF,OAAO,CACxBpC,GAAG,CAAC,UAAUf,IAAI,EAAE;cAAE,OAAOzC,GAAG,CAAC+F,YAAY,CAACtD,IAAI,CAAC;YAAE,CAAC,CAAC,CACvDuD,MAAM,CAAC,UAAU9H,CAAC,EAAEY,CAAC,EAAE;cAAE,OAAOmH,IAAI,CAACC,GAAG,CAAChI,CAAC,EAAEY,CAAC,CAAC;YAAE,CAAC,EAAE,CAAC,CAAC;YAC1D,OAAOgH,eAAe;UAC1B;UACAnI,OAAO,CAAC+H,cAAc,GAAGA,cAAc;UACvC,SAASD,cAAcA,CAACzF,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE;YAClD,IAAIkG,SAAS,GAAGnG,KAAK,CAACI,QAAQ,CAACgG,cAAc;YAC7C,IAAIC,SAAS,GAAGrG,KAAK,CAACI,QAAQ,CAACkG,cAAc;YAC7CvG,GAAG,CAAC2F,WAAW,CAAC;cAAES,SAAS,EAAEA,SAAS;cAAEE,SAAS,EAAEA;YAAU,CAAC,CAAC;YAC/D,IAAIE,SAAS,GAAGhB,YAAY,CAACY,SAAS,EAAE,KAAK,CAAC;YAC9C,IAAII,SAAS,EAAE;cACXxG,GAAG,CAACyG,IAAI,CAACN,QAAQ,CAACzD,CAAC,EAAEyD,QAAQ,CAACxD,CAAC,EAAE1C,KAAK,CAACyG,QAAQ,CAAC1G,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,EAAE1G,MAAM,CAACyC,CAAC,GAAGwD,QAAQ,CAACxD,CAAC,EAAE6D,SAAS,CAAC;YAC5G;UACJ;UACA7I,OAAO,CAAC8H,cAAc,GAAGA,cAAc;UACvC,SAASD,YAAYA,CAACY,SAAS,EAAES,SAAS,EAAE;YACxC,IAAIC,QAAQ,GAAGV,SAAS,GAAG,CAAC;YAC5B,IAAIW,cAAc,GAAGF,SAAS,IAAIA,SAAS,KAAK,CAAC;YACjD,IAAIC,QAAQ,IAAIC,cAAc,EAAE;cAC5B,OAAO,IAAI,CAAC,CAAC;YACjB,CAAC,MACI,IAAID,QAAQ,EAAE;cACf,OAAO,GAAG,CAAC,CAAC;YAChB,CAAC,MACI,IAAIC,cAAc,EAAE;cACrB,OAAO,GAAG,CAAC,CAAC;YAChB,CAAC,MACI;cACD,OAAO,IAAI;YACf;UACJ;UACApJ,OAAO,CAAC6H,YAAY,GAAGA,YAAY;UACnC,SAASD,YAAYA,CAAC1F,KAAK,EAAEmH,YAAY,EAAE;YACvC,IAAI3D,EAAE,EAAE4D,EAAE,EAAEC,EAAE,EAAEC,EAAE;YAClBtH,KAAK,GAAGA,KAAK,IAAImH,YAAY;YAC7B,IAAI9H,KAAK,CAAC2G,OAAO,CAAChG,KAAK,CAAC,EAAE;cACtB,IAAIA,KAAK,CAAC8B,MAAM,IAAI,CAAC,EAAE;gBACnB,OAAO;kBACHyF,GAAG,EAAEvH,KAAK,CAAC,CAAC,CAAC;kBACbwH,KAAK,EAAExH,KAAK,CAAC,CAAC,CAAC;kBACfyH,MAAM,EAAEzH,KAAK,CAAC,CAAC,CAAC;kBAChB0H,IAAI,EAAE1H,KAAK,CAAC,CAAC;gBACjB,CAAC;cACL,CAAC,MACI,IAAIA,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO;kBACHyF,GAAG,EAAEvH,KAAK,CAAC,CAAC,CAAC;kBACbwH,KAAK,EAAExH,KAAK,CAAC,CAAC,CAAC;kBACfyH,MAAM,EAAEzH,KAAK,CAAC,CAAC,CAAC;kBAChB0H,IAAI,EAAE1H,KAAK,CAAC,CAAC;gBACjB,CAAC;cACL,CAAC,MACI,IAAIA,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO;kBACHyF,GAAG,EAAEvH,KAAK,CAAC,CAAC,CAAC;kBACbwH,KAAK,EAAExH,KAAK,CAAC,CAAC,CAAC;kBACfyH,MAAM,EAAEzH,KAAK,CAAC,CAAC,CAAC;kBAChB0H,IAAI,EAAE1H,KAAK,CAAC,CAAC;gBACjB,CAAC;cACL,CAAC,MACI,IAAIA,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;gBACzB9B,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;cACpB,CAAC,MACI;gBACDA,KAAK,GAAGmH,YAAY;cACxB;YACJ;YACA,IAAI,OAAOnH,KAAK,KAAK,QAAQ,EAAE;cAC3B,IAAI,OAAOA,KAAK,CAAC2H,QAAQ,KAAK,QAAQ,EAAE;gBACpC3H,KAAK,CAACuH,GAAG,GAAGvH,KAAK,CAAC2H,QAAQ;gBAC1B3H,KAAK,CAACyH,MAAM,GAAGzH,KAAK,CAAC2H,QAAQ;cACjC;cACA,IAAI,OAAO3H,KAAK,CAAC4H,UAAU,KAAK,QAAQ,EAAE;gBACtC5H,KAAK,CAACwH,KAAK,GAAGxH,KAAK,CAAC4H,UAAU;gBAC9B5H,KAAK,CAAC0H,IAAI,GAAG1H,KAAK,CAAC4H,UAAU;cACjC;cACA,OAAO;gBACHF,IAAI,EAAE,CAAClE,EAAE,GAAGxD,KAAK,CAAC0H,IAAI,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG2D,YAAY;gBACrEI,GAAG,EAAE,CAACH,EAAE,GAAGpH,KAAK,CAACuH,GAAG,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,YAAY;gBACnEK,KAAK,EAAE,CAACH,EAAE,GAAGrH,KAAK,CAACwH,KAAK,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,YAAY;gBACvEM,MAAM,EAAE,CAACH,EAAE,GAAGtH,KAAK,CAACyH,MAAM,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGH;cACjE,CAAC;YACL;YACA,IAAI,OAAOnH,KAAK,KAAK,QAAQ,EAAE;cAC3BA,KAAK,GAAGmH,YAAY;YACxB;YACA,OAAO;cAAEI,GAAG,EAAEvH,KAAK;cAAEwH,KAAK,EAAExH,KAAK;cAAEyH,MAAM,EAAEzH,KAAK;cAAE0H,IAAI,EAAE1H;YAAM,CAAC;UACnE;UACAlC,OAAO,CAAC4H,YAAY,GAAGA,YAAY;;UAGnC;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS7G,uBAAuB,EAAEf,OAAO,EAAE;UAGlD,IAAIgB,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;YACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;cAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;gBAAEC,SAAS,EAAE;cAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;gBAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;cAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;gBAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;cAAE,CAAC;cACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;cACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;cAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;cACnB,SAASW,EAAEA,CAAA,EAAG;gBAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;cAAE;cACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;YACxF,CAAC;UACL,CAAC,CAAE,CAAC;UACJV,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAAC+J,QAAQ,GAAG/J,OAAO,CAACgK,aAAa,GAAGhK,OAAO,CAACiK,YAAY,GAAGjK,OAAO,CAAC2G,cAAc,GAAG,KAAK,CAAC;UACjG;AACA;AACA;UACA3G,OAAO,CAAC2G,cAAc,GAAG,IAAI;UAC7B,IAAIsD,YAAY,GAAG,aAAe,UAAUrH,MAAM,EAAE;YAChD5B,SAAS,CAACiJ,YAAY,EAAErH,MAAM,CAAC;YAC/B,SAASqH,YAAYA,CAACC,OAAO,EAAE;cAC3B,IAAIlH,KAAK,GAAGJ,MAAM,CAACjB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;cACrCqB,KAAK,CAACmH,QAAQ,GAAGD,OAAO;cACxB,OAAOlH,KAAK;YAChB;YACA,OAAOiH,YAAY;UACvB,CAAC,CAAC1I,KAAK,CAAE;UACTvB,OAAO,CAACiK,YAAY,GAAGA,YAAY;UACnC;UACA,SAASD,aAAaA,CAAClD,WAAW,EAAE;YAChC,OAAO;cACHsD,IAAI,EAAE,WAAW;cACjBC,SAAS,EAAE,QAAQ;cACnBC,QAAQ,EAAE,WAAW;cACrBpB,SAAS,EAAE,KAAK;cAChBqB,SAAS,EAAE,EAAE;cACblD,MAAM,EAAE,MAAM;cACdD,MAAM,EAAE,KAAK;cACbL,QAAQ,EAAE,EAAE;cACZyD,WAAW,EAAE,CAAC,GAAG1D,WAAW;cAC5B6B,SAAS,EAAE,GAAG;cACdF,SAAS,EAAE,CAAC;cACZgC,SAAS,EAAE,MAAM;cACjBC,aAAa,EAAE,CAAC;cAChBC,YAAY,EAAE;YAClB,CAAC;UACL;UACA3K,OAAO,CAACgK,aAAa,GAAGA,aAAa;UACrC,SAASD,QAAQA,CAACa,IAAI,EAAE;YACpB,IAAIC,MAAM,GAAG;cACTC,OAAO,EAAE;gBACLxI,KAAK,EAAE;kBAAE4G,SAAS,EAAE,GAAG;kBAAEqB,SAAS,EAAE,EAAE;kBAAEF,SAAS,EAAE;gBAAS,CAAC;gBAC7DzE,IAAI,EAAE;kBAAE2E,SAAS,EAAE,GAAG;kBAAErB,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;kBAAEmB,SAAS,EAAE;gBAAO,CAAC;gBACtEhG,IAAI,EAAE,CAAC,CAAC;gBACR0G,IAAI,EAAE;kBAAER,SAAS,EAAE,GAAG;kBAAErB,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;kBAAEmB,SAAS,EAAE;gBAAO,CAAC;gBACtEW,YAAY,EAAE;kBAAE9B,SAAS,EAAE;gBAAI;cACnC,CAAC;cACD+B,IAAI,EAAE;gBACF3I,KAAK,EAAE;kBACH4G,SAAS,EAAE,GAAG;kBACdqB,SAAS,EAAE,EAAE;kBACbF,SAAS,EAAE,QAAQ;kBACnB5B,SAAS,EAAE;gBACf,CAAC;gBACD7C,IAAI,EAAE;kBACF2E,SAAS,EAAE,GAAG;kBACdrB,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;kBACzBmB,SAAS,EAAE,MAAM;kBACjB5B,SAAS,EAAE;gBACf,CAAC;gBACDpE,IAAI,EAAE,CAAC,CAAC;gBACR0G,IAAI,EAAE;kBACFR,SAAS,EAAE,GAAG;kBACdrB,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;kBACzBmB,SAAS,EAAE,MAAM;kBACjB5B,SAAS,EAAE;gBACf,CAAC;gBACDuC,YAAY,EAAE,CAAC;cACnB,CAAC;cACDE,KAAK,EAAE;gBACHtF,IAAI,EAAE;kBAAEyE,SAAS,EAAE;gBAAO,CAAC;gBAC3BU,IAAI,EAAE;kBAAEV,SAAS,EAAE;gBAAO;cAC9B;YACJ,CAAC;YACD,OAAOQ,MAAM,CAACD,IAAI,CAAC;UACvB;UACA5K,OAAO,CAAC+J,QAAQ,GAAGA,QAAQ;;UAG3B;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAShJ,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACmL,QAAQ,GAAG,KAAK,CAAC;UACzB;UACA;UACA;UACA,IAAIC,QAAQ,GAAGlI,mBAAmB,CAAC,GAAG,CAAC;UACvC,SAASiI,QAAQA,CAACE,cAAc,EAAEnB,OAAO,EAAEpD,WAAW,EAAEwE,KAAK,EAAE5K,MAAM,EAAE;YACnE,IAAI6K,MAAM,GAAG,CAAC,CAAC;YACf,IAAIC,aAAa,GAAG,EAAE,GAAG,EAAE;YAC3B,IAAIC,eAAe,GAAGC,UAAU,CAACxB,OAAO,EAAE,UAAUyB,IAAI,EAAE;cACtD,OAAOjL,MAAM,CAACkL,gBAAgB,CAACD,IAAI,CAAC,CAAC,iBAAiB,CAAC;YAC3D,CAAC,CAAC;YACF,IAAIF,eAAe,IAAI,IAAI,EACvBF,MAAM,CAACrC,SAAS,GAAGuC,eAAe;YACtC,IAAIlB,SAAS,GAAGmB,UAAU,CAACxB,OAAO,EAAE,UAAUyB,IAAI,EAAE;cAChD,OAAOjL,MAAM,CAACkL,gBAAgB,CAACD,IAAI,CAAC,CAAC,OAAO,CAAC;YACjD,CAAC,CAAC;YACF,IAAIpB,SAAS,IAAI,IAAI,EACjBgB,MAAM,CAAChB,SAAS,GAAGA,SAAS;YAChC,IAAIsB,WAAW,GAAGH,UAAU,CAACxB,OAAO,EAAE,UAAUyB,IAAI,EAAE;cAClD,OAAOjL,MAAM,CAACkL,gBAAgB,CAACD,IAAI,CAAC,CAAC,gBAAgB,CAAC;YAC1D,CAAC,CAAC;YACF,IAAIE,WAAW,IAAI,IAAI,EACnBN,MAAM,CAAC5C,SAAS,GAAGkD,WAAW;YAClC,IAAIC,OAAO,GAAGC,YAAY,CAACT,KAAK,EAAExE,WAAW,CAAC;YAC9C,IAAIgF,OAAO,EACPP,MAAM,CAACf,WAAW,GAAGsB,OAAO;YAChC;YACA,IAAIE,EAAE,GAAGC,QAAQ,CAACX,KAAK,CAACY,cAAc,IAAI,EAAE,CAAC;YAC7CF,EAAE,GAAGA,EAAE,GAAGR,aAAa,GAAG1E,WAAW;YACrC,IAAIkF,EAAE,EACFT,MAAM,CAAC9C,SAAS,GAAGuD,EAAE;YACzB,IAAIG,QAAQ,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;YACrD,IAAIA,QAAQ,CAACC,OAAO,CAACd,KAAK,CAACe,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;cAC1Cd,MAAM,CAAClE,MAAM,GAAGiE,KAAK,CAACe,SAAS;YACnC;YACAF,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC;YACtC,IAAIA,QAAQ,CAACC,OAAO,CAACd,KAAK,CAACgB,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;cAC9Cf,MAAM,CAACnE,MAAM,GAAGkE,KAAK,CAACgB,aAAa;YACvC;YACA,IAAIC,GAAG,GAAGN,QAAQ,CAACX,KAAK,CAACvE,QAAQ,IAAI,EAAE,CAAC;YACxC,IAAI,CAACyF,KAAK,CAACD,GAAG,CAAC,EACXhB,MAAM,CAACxE,QAAQ,GAAGwF,GAAG,GAAGf,aAAa;YACzC,IAAInB,SAAS,GAAGoC,cAAc,CAACnB,KAAK,CAAC;YACrC,IAAIjB,SAAS,EACTkB,MAAM,CAAClB,SAAS,GAAGA,SAAS;YAChC,IAAID,IAAI,GAAG,CAACkB,KAAK,CAACoB,UAAU,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;YACjD,IAAItB,cAAc,CAACe,OAAO,CAAChC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;cACrCmB,MAAM,CAACnB,IAAI,GAAGA,IAAI;YACtB;YACA,OAAOmB,MAAM;UACjB;UACAvL,OAAO,CAACmL,QAAQ,GAAGA,QAAQ;UAC3B,SAASsB,cAAcA,CAACnB,KAAK,EAAE;YAC3B,IAAIiB,GAAG,GAAG,EAAE;YACZ,IAAIjB,KAAK,CAACsB,UAAU,KAAK,MAAM,IAC3BtB,KAAK,CAACsB,UAAU,KAAK,QAAQ,IAC7BX,QAAQ,CAACX,KAAK,CAACsB,UAAU,CAAC,IAAI,GAAG,EAAE;cACnCL,GAAG,GAAG,MAAM;YAChB;YACA,IAAIjB,KAAK,CAACjB,SAAS,KAAK,QAAQ,IAAIiB,KAAK,CAACjB,SAAS,KAAK,SAAS,EAAE;cAC/DkC,GAAG,IAAI,QAAQ;YACnB;YACA,OAAOA,GAAG;UACd;UACA,SAASb,UAAUA,CAACxB,OAAO,EAAE2C,WAAW,EAAE;YACtC,IAAIC,QAAQ,GAAGC,SAAS,CAAC7C,OAAO,EAAE2C,WAAW,CAAC;YAC9C,IAAI,CAACC,QAAQ,EACT,OAAO,IAAI;YACf,IAAIE,IAAI,GAAGF,QAAQ,CAACG,KAAK,CAAC,wDAAwD,CAAC;YACnF,IAAI,CAACD,IAAI,IAAI,CAACzL,KAAK,CAAC2G,OAAO,CAAC8E,IAAI,CAAC,EAAE;cAC/B,OAAO,IAAI;YACf;YACA,IAAIE,KAAK,GAAG,CACRjB,QAAQ,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC,EACjBf,QAAQ,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC,EACjBf,QAAQ,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC,CACpB;YACD,IAAIG,KAAK,GAAGlB,QAAQ,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAIG,KAAK,KAAK,CAAC,IAAIX,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIV,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIV,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;cACtE,OAAO,IAAI;YACf;YACA,OAAOA,KAAK;UAChB;UACA,SAASH,SAASA,CAACpB,IAAI,EAAEkB,WAAW,EAAE;YAClC,IAAIO,EAAE,GAAGP,WAAW,CAAClB,IAAI,CAAC;YAC1B,IAAIyB,EAAE,KAAK,kBAAkB,IACzBA,EAAE,KAAK,aAAa,IACpBA,EAAE,KAAK,SAAS,IAChBA,EAAE,KAAK,SAAS,EAAE;cAClB,IAAIzB,IAAI,CAAC0B,aAAa,IAAI,IAAI,EAAE;gBAC5B,OAAO,IAAI;cACf;cACA,OAAON,SAAS,CAACpB,IAAI,CAAC0B,aAAa,EAAER,WAAW,CAAC;YACrD,CAAC,MACI;cACD,OAAOO,EAAE;YACb;UACJ;UACA,SAASrB,YAAYA,CAACT,KAAK,EAAExE,WAAW,EAAE;YACtC,IAAIwG,GAAG,GAAG,CACNhC,KAAK,CAACiC,UAAU,EAChBjC,KAAK,CAACkC,YAAY,EAClBlC,KAAK,CAACmC,aAAa,EACnBnC,KAAK,CAACoC,WAAW,CACpB;YACD,IAAIlC,aAAa,GAAG,EAAE,IAAI,EAAE,GAAG1E,WAAW,CAAC;YAC3C,IAAI6G,WAAW,GAAG,CAAC1B,QAAQ,CAACX,KAAK,CAACsC,UAAU,CAAC,GAAG3B,QAAQ,CAACX,KAAK,CAACvE,QAAQ,CAAC,IAAID,WAAW,GAAG,CAAC;YAC3F,IAAI+G,YAAY,GAAGP,GAAG,CAACzH,GAAG,CAAC,UAAUiI,CAAC,EAAE;cACpC,OAAO7B,QAAQ,CAAC6B,CAAC,IAAI,GAAG,CAAC,GAAGtC,aAAa;YAC7C,CAAC,CAAC;YACF,IAAIM,OAAO,GAAG,CAAC,CAAC,EAAEV,QAAQ,CAACxD,YAAY,EAAEiG,YAAY,EAAE,CAAC,CAAC;YACzD,IAAIF,WAAW,GAAG7B,OAAO,CAACrC,GAAG,EAAE;cAC3BqC,OAAO,CAACrC,GAAG,GAAGkE,WAAW;YAC7B;YACA,IAAIA,WAAW,GAAG7B,OAAO,CAACnC,MAAM,EAAE;cAC9BmC,OAAO,CAACnC,MAAM,GAAGgE,WAAW;YAChC;YACA,OAAO7B,OAAO;UAClB;;UAGA;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS/K,uBAAuB,EAAEf,OAAO,EAAE;UAGlDoB,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACqF,UAAU,GAAG,KAAK,CAAC;UAC3B,IAAIkB,cAAc,GAAG,CAAC,CAAC;UACvB,IAAIlB,UAAU,GAAG,aAAe,YAAY;YACxC,SAASA,UAAUA,CAAC0I,aAAa,EAAE;cAC/B,IAAI,CAACA,aAAa,GAAGA,aAAa;cAClC,IAAI,CAACC,UAAU,GAAG;gBACd;gBACAzD,SAAS,EAAEwD,aAAa,CAACE,YAAY,GAC/B,IAAI,CAACF,aAAa,CAACE,YAAY,CAAC,CAAC,GACjC,CAAC;gBACPlH,QAAQ,EAAEgH,aAAa,CAAClH,QAAQ,CAACG,WAAW,CAAC,CAAC;gBAC9CqD,SAAS,EAAE0D,aAAa,CAAClH,QAAQ,CAACqH,OAAO,CAAC,CAAC,CAAC7D,SAAS;gBACrDD,IAAI,EAAE2D,aAAa,CAAClH,QAAQ,CAACqH,OAAO,CAAC,CAAC,CAACC,QAAQ;gBAC/C;gBACA1F,SAAS,EAAEsF,aAAa,CAACK,YAAY,GAC/B,IAAI,CAACL,aAAa,CAACK,YAAY,CAAC,CAAC,GACjC,CAAC;gBACP;gBACAzF,SAAS,EAAEoF,aAAa,CAACM,YAAY,GAC/B,IAAI,CAACN,aAAa,CAACM,YAAY,CAAC,CAAC,GACjC;cACV,CAAC;YACL;YACAhJ,UAAU,CAACC,WAAW,GAAG,UAAUF,QAAQ,EAAE/C,GAAG,EAAE;cAC9C,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;gBAAEA,GAAG,GAAG,IAAI;cAAE;cAClC,IAAIA,GAAG,EAAE;gBACLA,GAAG,CAACiM,2BAA2B,GAAGlJ,QAAQ;cAC9C,CAAC,MACI;gBACDmB,cAAc,GAAGnB,QAAQ;cAC7B;YACJ,CAAC;YACDC,UAAU,CAACkJ,UAAU,GAAG,UAAUzI,CAAC,EAAE;cACjC,IAAIvE,KAAK,CAAC2G,OAAO,CAACpC,CAAC,CAAC,EAAE;gBAClB,OAAOA,CAAC;cACZ,CAAC,MACI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;gBAC5B,OAAO,CAACA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;cACpB,CAAC,MACI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;gBAC5B,OAAO,CAACA,CAAC,CAAC;cACd,CAAC,MACI;gBACD,OAAO,IAAI;cACf;YACJ,CAAC;YACDT,UAAU,CAAC5D,SAAS,CAACuG,WAAW,GAAG,UAAU/C,MAAM,EAAEuJ,QAAQ,EAAE;cAC3D;cACA;cACA,IAAI9I,EAAE,EAAE4D,EAAE,EAAEC,EAAE;cACd,IAAIiF,QAAQ,KAAK,KAAK,CAAC,EAAE;gBAAEA,QAAQ,GAAG,KAAK;cAAE;cAC7C,IAAIvJ,MAAM,CAACoF,SAAS,EAChB,IAAI,CAAC0D,aAAa,CAACU,YAAY,IAC3B,IAAI,CAACV,aAAa,CAACU,YAAY,CAACxJ,MAAM,CAACoF,SAAS,CAAC;cACzD,IAAIb,EAAE,GAAG,IAAI,CAACuE,aAAa,CAAClH,QAAQ,CAACqH,OAAO,CAAC,CAAC;gBAAE7D,SAAS,GAAGb,EAAE,CAACa,SAAS;gBAAE8D,QAAQ,GAAG3E,EAAE,CAAC2E,QAAQ;cAChG,IAAIlJ,MAAM,CAACmF,IAAI,EACX+D,QAAQ,GAAGlJ,MAAM,CAACmF,IAAI;cAC1B,IAAInF,MAAM,CAACoF,SAAS,EAAE;gBAClBA,SAAS,GAAGpF,MAAM,CAACoF,SAAS;gBAC5B,IAAIqE,mBAAmB,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAAC;gBACtD,IAAIO,mBAAmB,IACnBA,mBAAmB,CAACtC,OAAO,CAAC/B,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;kBAC/C;kBACA;kBACA;kBACA,IAAI,CAAC0D,aAAa,CAACU,YAAY,IAC3B,IAAI,CAACV,aAAa,CAACU,YAAY,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAAC;kBAC3DrE,SAAS,GAAGqE,mBAAmB,CAAC,CAAC,CAAC;gBACtC;cACJ;cACA,IAAI,CAACX,aAAa,CAACa,OAAO,CAACT,QAAQ,EAAE9D,SAAS,CAAC;cAC/C,IAAIpF,MAAM,CAAC8B,QAAQ,EACf,IAAI,CAACgH,aAAa,CAACc,WAAW,CAAC5J,MAAM,CAAC8B,QAAQ,CAAC;cACnD,IAAIyH,QAAQ,EAAE;gBACV,OAAO,CAAC;cACZ;;cACA,IAAItB,KAAK,GAAG7H,UAAU,CAACkJ,UAAU,CAACtJ,MAAM,CAACiE,SAAS,CAAC;cACnD,IAAIgE,KAAK,EACL,CAACxH,EAAE,GAAG,IAAI,CAACqI,aAAa,EAAEe,YAAY,CAACC,KAAK,CAACrJ,EAAE,EAAEwH,KAAK,CAAC;cAC3DA,KAAK,GAAG7H,UAAU,CAACkJ,UAAU,CAACtJ,MAAM,CAACsF,SAAS,CAAC;cAC/C,IAAI2C,KAAK,EACL,CAAC5D,EAAE,GAAG,IAAI,CAACyE,aAAa,EAAEiB,YAAY,CAACD,KAAK,CAACzF,EAAE,EAAE4D,KAAK,CAAC;cAC3DA,KAAK,GAAG7H,UAAU,CAACkJ,UAAU,CAACtJ,MAAM,CAAC0D,SAAS,CAAC;cAC/C,IAAIuE,KAAK,EACL,CAAC3D,EAAE,GAAG,IAAI,CAACwE,aAAa,EAAEkB,YAAY,CAACF,KAAK,CAACxF,EAAE,EAAE2D,KAAK,CAAC;cAC3D,IAAI,OAAOjI,MAAM,CAACwD,SAAS,KAAK,QAAQ,EAAE;gBACtC,IAAI,CAACsF,aAAa,CAACmB,YAAY,CAACjK,MAAM,CAACwD,SAAS,CAAC;cACrD;YACJ,CAAC;YACDpD,UAAU,CAAC5D,SAAS,CAAC0N,eAAe,GAAG,UAAUrK,IAAI,EAAEsK,IAAI,EAAEC,IAAI,EAAE;cAC/D,OAAO,IAAI,CAACtB,aAAa,CAACoB,eAAe,CAACrK,IAAI,EAAEsK,IAAI,EAAEC,IAAI,CAAC;YAC/D,CAAC;YACDhK,UAAU,CAAC5D,SAAS,CAACqH,IAAI,GAAG,UAAU/D,CAAC,EAAEC,CAAC,EAAEiE,KAAK,EAAEqG,MAAM,EAAEzG,SAAS,EAAE;cAClE,OAAO,IAAI,CAACkF,aAAa,CAACjF,IAAI,CAAC/D,CAAC,EAAEC,CAAC,EAAEiE,KAAK,EAAEqG,MAAM,EAAEzG,SAAS,CAAC;YAClE,CAAC;YACDxD,UAAU,CAAC5D,SAAS,CAAC8N,gBAAgB,GAAG,YAAY;cAChD,OAAO,IAAI,CAACxB,aAAa,CAACrJ,aAAa,IAAI,IAAI;YACnD,CAAC;YACDW,UAAU,CAAC5D,SAAS,CAAC2G,YAAY,GAAG,UAAUtD,IAAI,EAAE;cAChD,OAAO,IAAI,CAACiJ,aAAa,CAAC3F,YAAY,CAACtD,IAAI,CAAC;YAChD,CAAC;YACDO,UAAU,CAAC5D,SAAS,CAACkB,WAAW,GAAG,YAAY;cAC3C,OAAO,IAAI,CAACoL,aAAa;YAC7B,CAAC;YACD1I,UAAU,CAAC5D,SAAS,CAAC+N,OAAO,GAAG,UAAUC,IAAI,EAAE;cAC3C,IAAI,CAAC1B,aAAa,CAACyB,OAAO,CAACC,IAAI,CAAC;YACpC,CAAC;YACDpK,UAAU,CAAC5D,SAAS,CAACiF,OAAO,GAAG,YAAY;cACvC,OAAO,IAAI,CAACqH,aAAa,CAACrH,OAAO,CAAC,CAAC;YACvC,CAAC;YACDrB,UAAU,CAAC5D,SAAS,CAACkN,WAAW,GAAG,YAAY;cAC3C,OAAO,IAAI,CAACZ,aAAa,CAACY,WAAW,CAAC,CAAC;YAC3C,CAAC;YACDtJ,UAAU,CAAC5D,SAAS,CAACiO,gBAAgB,GAAG,YAAY;cAChD,OAAOnJ,cAAc,IAAI,CAAC,CAAC;YAC/B,CAAC;YACDlB,UAAU,CAAC5D,SAAS,CAACkO,kBAAkB,GAAG,YAAY;cAClD,OAAO,IAAI,CAAC5B,aAAa,CAACO,2BAA2B,IAAI,CAAC,CAAC;YAC/D,CAAC;YACDjJ,UAAU,CAAC5D,SAAS,CAACuH,QAAQ,GAAG,YAAY;cACxC,IAAIA,QAAQ,GAAG,IAAI,CAAC+E,aAAa,CAAClH,QAAQ,CAACmC,QAAQ;cACnD;cACA,IAAIA,QAAQ,CAACC,KAAK,IAAI,IAAI,EAAE;gBACxBD,QAAQ,GAAG;kBACPC,KAAK,EAAED,QAAQ,CAACD,QAAQ,CAAC,CAAC;kBAC1BuG,MAAM,EAAEtG,QAAQ,CAAC4G,SAAS,CAAC;gBAC/B,CAAC;cACL;cACA,OAAO5G,QAAQ;YACnB,CAAC;YACD3D,UAAU,CAAC5D,SAAS,CAACqF,WAAW,GAAG,YAAY;cAC3C,OAAO,IAAI,CAACiH,aAAa,CAAClH,QAAQ,CAACC,WAAW;YAClD,CAAC;YACDzB,UAAU,CAAC5D,SAAS,CAACe,UAAU,GAAG,YAAY;cAC1C,IAAIqN,QAAQ,GAAG,IAAI,CAAC9B,aAAa,CAAClH,QAAQ,CAACiJ,kBAAkB,CAAC,CAAC;cAC/D,IAAI,CAACD,QAAQ,EAAE;gBACX;gBACA,OAAO,IAAI,CAAC9B,aAAa,CAAClH,QAAQ,CAACkJ,gBAAgB,CAAC,CAAC;cACzD;cACA,OAAOF,QAAQ,CAACrN,UAAU;YAC9B,CAAC;YACD,OAAO6C,UAAU;UACrB,CAAC,CAAC,CAAE;UACJrF,OAAO,CAACqF,UAAU,GAAGA,UAAU;;UAG/B;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAStE,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAAC2F,SAAS,GAAG,KAAK,CAAC;UAC1B,IAAIqK,WAAW,GAAG9M,mBAAmB,CAAC,GAAG,CAAC;UAC1C,IAAI+M,QAAQ,GAAG/M,mBAAmB,CAAC,GAAG,CAAC;UACvC,SAASyC,SAASA,CAACtD,GAAG,EAAEiC,KAAK,EAAE5D,MAAM,EAAEwP,iBAAiB,EAAEC,MAAM,EAAE;YAC9D,IAAIzK,EAAE,EAAE4D,EAAE;YACV,IAAI4G,iBAAiB,KAAK,KAAK,CAAC,EAAE;cAAEA,iBAAiB,GAAG,KAAK;YAAE;YAC/D,IAAIC,MAAM,KAAK,KAAK,CAAC,EAAE;cAAEA,MAAM,GAAG,KAAK;YAAE;YACzC,IAAIC,YAAY;YAChB,IAAI,OAAO9L,KAAK,KAAK,QAAQ,EAAE;cAC3B8L,YAAY,GAAG1P,MAAM,CAAC2P,QAAQ,CAACC,aAAa,CAAChM,KAAK,CAAC;YACvD,CAAC,MACI;cACD8L,YAAY,GAAG9L,KAAK;YACxB;YACA,IAAI+G,cAAc,GAAGjK,MAAM,CAACmP,IAAI,CAAClO,GAAG,CAACsM,WAAW,CAAC,CAAC,CAAC;YACnD,IAAI7H,WAAW,GAAGzE,GAAG,CAACyE,WAAW,CAAC,CAAC;YACnC,IAAIlB,IAAI,GAAG,EAAE;cAAEvB,IAAI,GAAG,EAAE;cAAE0G,IAAI,GAAG,EAAE;YACnC,IAAI,CAACqF,YAAY,EAAE;cACflM,OAAO,CAACC,KAAK,CAAC,4CAA4C,EAAEG,KAAK,CAAC;cAClE,OAAO;gBAAEsB,IAAI,EAAEA,IAAI;gBAAEvB,IAAI,EAAEA,IAAI;gBAAE0G,IAAI,EAAEA;cAAK,CAAC;YACjD;YACA,KAAK,IAAIvK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4P,YAAY,CAACpK,IAAI,CAAChC,MAAM,EAAExD,CAAC,EAAE,EAAE;cAC/C,IAAI0J,OAAO,GAAGkG,YAAY,CAACpK,IAAI,CAACxF,CAAC,CAAC;cAClC,IAAIgQ,OAAO,GAAG,CAAClH,EAAE,GAAG,CAAC5D,EAAE,GAAGwE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmD,aAAa,MAAM,IAAI,IAAI3H,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8K,OAAO,MAAM,IAAI,IAAIlH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,WAAW,CAAC,CAAC;cACzM,IAAI7J,GAAG,GAAG2N,eAAe,CAACpF,cAAc,EAAEvE,WAAW,EAAEpG,MAAM,EAAEwJ,OAAO,EAAEgG,iBAAiB,EAAEC,MAAM,CAAC;cAClG,IAAI,CAACrN,GAAG,EACJ;cACJ,IAAI0N,OAAO,KAAK,OAAO,EAAE;gBACrB5K,IAAI,CAAC8K,IAAI,CAAC5N,GAAG,CAAC;cAClB,CAAC,MACI,IAAI0N,OAAO,KAAK,OAAO,EAAE;gBAC1BzF,IAAI,CAAC2F,IAAI,CAAC5N,GAAG,CAAC;cAClB,CAAC,MACI;gBACD;gBACAuB,IAAI,CAACqM,IAAI,CAAC5N,GAAG,CAAC;cAClB;YACJ;YACA,OAAO;cAAE8C,IAAI,EAAEA,IAAI;cAAEvB,IAAI,EAAEA,IAAI;cAAE0G,IAAI,EAAEA;YAAK,CAAC;UACjD;UACA/K,OAAO,CAAC2F,SAAS,GAAGA,SAAS;UAC7B,SAAS8K,eAAeA,CAACpF,cAAc,EAAEvE,WAAW,EAAEpG,MAAM,EAAEoC,GAAG,EAAE6N,aAAa,EAAER,MAAM,EAAE;YACtF,IAAIS,SAAS,GAAG,IAAIX,QAAQ,CAAChG,YAAY,CAACnH,GAAG,CAAC;YAC9C,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,GAAG,CAAC+N,KAAK,CAAC7M,MAAM,EAAExD,CAAC,EAAE,EAAE;cACvC,IAAIqC,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAACrQ,CAAC,CAAC;cACvB,IAAIsQ,OAAO,GAAGpQ,MAAM,CAACkL,gBAAgB,CAAC/I,IAAI,CAAC;cAC3C,IAAI8N,aAAa,IAAIG,OAAO,CAACC,OAAO,KAAK,MAAM,EAAE;gBAC7C,IAAIC,UAAU,GAAG,KAAK,CAAC;gBACvB,IAAIb,MAAM,EAAE;kBACRa,UAAU,GAAG,CAAC,CAAC,EAAEhB,WAAW,CAAC7E,QAAQ,EAAEE,cAAc,EAAExI,IAAI,EAAEiE,WAAW,EAAEgK,OAAO,EAAEpQ,MAAM,CAAC;gBAC9F;gBACAkQ,SAAS,CAACF,IAAI,CAAC;kBACXO,OAAO,EAAEpO,IAAI,CAACoO,OAAO;kBACrBC,OAAO,EAAErO,IAAI,CAACqO,OAAO;kBACrBjM,MAAM,EAAE+L,UAAU;kBAClB7G,QAAQ,EAAEtH,IAAI;kBACdkD,OAAO,EAAEoL,gBAAgB,CAACtO,IAAI;gBAClC,CAAC,CAAC;cACN;YACJ;YACA,IAAIyI,KAAK,GAAG5K,MAAM,CAACkL,gBAAgB,CAAC9I,GAAG,CAAC;YACxC,IAAI8N,SAAS,CAAC5M,MAAM,GAAG,CAAC,KAAK2M,aAAa,IAAIrF,KAAK,CAACyF,OAAO,KAAK,MAAM,CAAC,EAAE;cACrE,OAAOH,SAAS;YACpB;UACJ;UACA,SAASO,gBAAgBA,CAACC,OAAO,EAAE;YAC/B;YACA,IAAIvO,IAAI,GAAGuO,OAAO,CAACC,SAAS,CAAC,IAAI,CAAC;YAClC;YACA;YACAxO,IAAI,CAACyO,SAAS,GAAGzO,IAAI,CAACyO,SAAS,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;YACtE;YACA1O,IAAI,CAACyO,SAAS,GAAGzO,IAAI,CAACyO,SAAS,CAC1BhK,KAAK,CAAC,WAAW,CAAC,CAAC;YAAA,CACnBzB,GAAG,CAAC,UAAU2L,IAAI,EAAE;cAAE,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;YAAE,CAAC,CAAC,CAC5CC,IAAI,CAAC,IAAI,CAAC;YACf;YACA,OAAO7O,IAAI,CAAC8O,SAAS,IAAI9O,IAAI,CAAC+O,WAAW,IAAI,EAAE;UACnD;;UAGA;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS7Q,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACuE,UAAU,GAAG,KAAK,CAAC;UAC3B,IAAIpB,YAAY,GAAGD,mBAAmB,CAAC,GAAG,CAAC;UAC3C,IAAI2O,WAAW,GAAG3O,mBAAmB,CAAC,GAAG,CAAC;UAC1C,IAAIkI,QAAQ,GAAGlI,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIG,iBAAiB,GAAGH,mBAAmB,CAAC,GAAG,CAAC;UAChD,IAAI4O,gBAAgB,GAAG5O,mBAAmB,CAAC,GAAG,CAAC;UAC/C,SAASqB,UAAUA,CAACrD,CAAC,EAAE6Q,OAAO,EAAE;YAC5B,IAAI1P,GAAG,GAAG,IAAIgB,iBAAiB,CAACgC,UAAU,CAACnE,CAAC,CAAC;YAC7C,IAAImP,QAAQ,GAAGhO,GAAG,CAACsN,kBAAkB,CAAC,CAAC;YACvC,IAAI/O,MAAM,GAAGyB,GAAG,CAACqN,gBAAgB,CAAC,CAAC;YACnC,CAAC,CAAC,EAAEoC,gBAAgB,CAAC5M,OAAO,EAAE7C,GAAG,EAAEzB,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,CAAC;YAC7D,IAAI9N,OAAO,GAAG,CAAC,CAAC,EAAE4N,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAEpR,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,CAAC;YACpE,IAAIE,GAAG;YACP,IAAI,OAAOvR,MAAM,KAAK,WAAW,EAAE;cAC/BuR,GAAG,GAAGvR,MAAM;YAChB;YACA,IAAIuE,MAAM,GAAGiN,WAAW,CAACtR,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,CAAC;YACnD,IAAII,KAAK,GAAGC,UAAU,CAACxR,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,CAAC;YACjD,IAAIrP,QAAQ,GAAG2P,aAAa,CAAChQ,GAAG,EAAE4B,OAAO,CAAC;YAC1C,IAAI8B,OAAO,GAAGuM,YAAY,CAACjQ,GAAG,EAAE4B,OAAO,EAAEgO,GAAG,CAAC;YAC7C,OAAO;cACHM,EAAE,EAAER,OAAO,CAACS,OAAO;cACnBzM,OAAO,EAAEA,OAAO;cAChBoM,KAAK,EAAEA,KAAK;cACZlN,MAAM,EAAEA,MAAM;cACdvC,QAAQ,EAAEA;YACd,CAAC;UACL;UACA1C,OAAO,CAACuE,UAAU,GAAGA,UAAU;UAC/B,SAAS2N,WAAWA,CAACO,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;YACzC,IAAIC,YAAY,GAAG;cACf3N,MAAM,EAAE,CAAC,CAAC;cACV4N,UAAU,EAAE,CAAC,CAAC;cACdC,UAAU,EAAE,CAAC,CAAC;cACdC,UAAU,EAAE,CAAC,CAAC;cACdC,kBAAkB,EAAE,CAAC,CAAC;cACtBC,YAAY,EAAE,CAAC;YACnB,CAAC;YACD,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAE;cAC1B,IAAIA,IAAI,KAAK,cAAc,EAAE;gBACzB,IAAIC,QAAQ,GAAGX,MAAM,CAACU,IAAI,CAAC;gBAC3B,IAAIE,UAAU,GAAGX,MAAM,CAACS,IAAI,CAAC;gBAC7B,IAAIpB,OAAO,GAAGY,MAAM,CAACQ,IAAI,CAAC;gBAC1BP,YAAY,CAACK,YAAY,GAAG,CAAC,CAAC,EAAEpB,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAEoB,QAAQ,EAAEC,UAAU,EAAEtB,OAAO,CAAC;cAC1F,CAAC,MACI;gBACD,IAAIuB,UAAU,GAAG,CAACb,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;gBACzC,IAAI1N,MAAM,GAAGqO,UAAU,CAACzN,GAAG,CAAC,UAAUwJ,IAAI,EAAE;kBAAE,OAAOA,IAAI,CAAC8D,IAAI,CAAC,IAAI,CAAC,CAAC;gBAAE,CAAC,CAAC;gBACzEP,YAAY,CAACO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEtB,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAE/M,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;cACrF;YACJ,CAAC;YACD,KAAK,IAAInB,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGtE,MAAM,CAACmP,IAAI,CAACqC,YAAY,CAAC,EAAE9O,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;cACnE,IAAIqP,IAAI,GAAGzN,EAAE,CAAC5B,EAAE,CAAC;cACjBoP,OAAO,CAACC,IAAI,CAAC;YACjB;YACA,OAAOP,YAAY;UACvB;UACA,SAASR,UAAUA,CAACxR,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,EAAE;YAC3C,IAAIuB,UAAU,GAAG,CAAC1S,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,CAAC;YAC5C,IAAIxG,MAAM,GAAG;cACTgI,YAAY,EAAE,EAAE;cAChBC,YAAY,EAAE,EAAE;cAChBC,WAAW,EAAE,EAAE;cACfC,WAAW,EAAE;YACjB,CAAC;YACD,KAAK,IAAI5P,EAAE,GAAG,CAAC,EAAE6P,YAAY,GAAGL,UAAU,EAAExP,EAAE,GAAG6P,YAAY,CAAC3P,MAAM,EAAEF,EAAE,EAAE,EAAE;cACxE,IAAIG,OAAO,GAAG0P,YAAY,CAAC7P,EAAE,CAAC;cAC9B,IAAIG,OAAO,CAACsP,YAAY,EACpBhI,MAAM,CAACgI,YAAY,CAAC7C,IAAI,CAACzM,OAAO,CAACsP,YAAY,CAAC;cAClD,IAAItP,OAAO,CAACuP,YAAY,EACpBjI,MAAM,CAACiI,YAAY,CAAC9C,IAAI,CAACzM,OAAO,CAACuP,YAAY,CAAC;cAClD,IAAIvP,OAAO,CAACwP,WAAW,EACnBlI,MAAM,CAACkI,WAAW,CAAC/C,IAAI,CAACzM,OAAO,CAACwP,WAAW,CAAC;cAChD,IAAIxP,OAAO,CAACyP,WAAW,EACnBnI,MAAM,CAACmI,WAAW,CAAChD,IAAI,CAACzM,OAAO,CAACyP,WAAW,CAAC;YACpD;YACA,OAAOnI,MAAM;UACjB;UACA,SAAS8G,aAAaA,CAAChQ,GAAG,EAAE4B,OAAO,EAAE;YACjC,IAAIyB,EAAE,EAAE4D,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEoK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;YAC9C,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAE/I,QAAQ,CAACxD,YAAY,EAAE3D,OAAO,CAACkQ,MAAM,EAAE,EAAE,GAAG9R,GAAG,CAACyE,WAAW,CAAC,CAAC,CAAC;YAC/E,IAAIsN,MAAM,GAAG,CAAC1O,EAAE,GAAG2O,SAAS,CAAChS,GAAG,EAAE4B,OAAO,CAACmQ,MAAM,CAAC,MAAM,IAAI,IAAI1O,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGyO,MAAM,CAAC1K,GAAG;YAC9F,IAAI6K,QAAQ;YACZ,IAAIrQ,OAAO,CAACqQ,QAAQ,KAAK,IAAI,EAAE;cAC3BA,QAAQ,GAAG,WAAW;YAC1B,CAAC,MACI,IAAIrQ,OAAO,CAACqQ,QAAQ,KAAK,KAAK,EAAE;cACjCA,QAAQ,GAAG,OAAO;YACtB,CAAC,MACI;cACDA,QAAQ,GAAG,CAAChL,EAAE,GAAGrF,OAAO,CAACqQ,QAAQ,MAAM,IAAI,IAAIhL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,WAAW;YACnF;YACA,IAAIiL,QAAQ;YACZ,IAAItQ,OAAO,CAACsQ,QAAQ,KAAK,IAAI,EAAE;cAC3BA,QAAQ,GAAG,WAAW;YAC1B,CAAC,MACI,IAAItQ,OAAO,CAACsQ,QAAQ,KAAK,KAAK,EAAE;cACjCA,QAAQ,GAAG,OAAO;YACtB,CAAC,MACI;cACDA,QAAQ,GAAG,CAAChL,EAAE,GAAGtF,OAAO,CAACsQ,QAAQ,MAAM,IAAI,IAAIhL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,WAAW;YACnF;YACA,IAAI4G,MAAM,GAAG,CAAC3G,EAAE,GAAGvF,OAAO,CAACkM,MAAM,MAAM,IAAI,IAAI3G,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;YACzE,IAAIgL,KAAK,GAAGvQ,OAAO,CAACuQ,KAAK,KAAKrE,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;YAC3D,IAAIsE,mBAAmB,GAAGxQ,OAAO,CAACwQ,mBAAmB,GAC/C,IAAI,GACJ,KAAK;YACX,IAAIC,yBAAyB,GAAG,CAACd,EAAE,GAAG3P,OAAO,CAACyQ,yBAAyB,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;YAC9G,OAAO;cACH1D,iBAAiB,EAAE,CAAC2D,EAAE,GAAG5P,OAAO,CAACiM,iBAAiB,MAAM,IAAI,IAAI2D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;cAC1F1D,MAAM,EAAEA,MAAM;cACdqE,KAAK,EAAEA,KAAK;cACZJ,MAAM,EAAEA,MAAM;cACdD,MAAM,EAAEA,MAAM;cACdQ,SAAS,EAAE,CAACb,EAAE,GAAG7P,OAAO,CAAC0Q,SAAS,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;cAC3Ec,YAAY,EAAE,CAACb,EAAE,GAAG9P,OAAO,CAAC2Q,YAAY,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;cACjFc,UAAU,EAAE,CAACb,EAAE,GAAG/P,OAAO,CAAC4Q,UAAU,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;cAC7EO,QAAQ,EAAEA,QAAQ;cAClBD,QAAQ,EAAEA,QAAQ;cAClB5L,cAAc,EAAE,CAACuL,EAAE,GAAGhQ,OAAO,CAACyE,cAAc,MAAM,IAAI,IAAIuL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;cAChFrL,cAAc,EAAE,CAACsL,EAAE,GAAGjQ,OAAO,CAAC2E,cAAc,MAAM,IAAI,IAAIsL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;cAClFO,mBAAmB,EAAEA,mBAAmB;cACxCC,yBAAyB,EAAEA;YAC/B,CAAC;UACL;UACA,SAASL,SAASA,CAAChS,GAAG,EAAEyS,UAAU,EAAE;YAChC,IAAIlQ,QAAQ,GAAGvC,GAAG,CAACkN,gBAAgB,CAAC,CAAC;YACrC,IAAIwF,EAAE,GAAG1S,GAAG,CAACyE,WAAW,CAAC,CAAC;YAC1B,IAAIkO,WAAW,GAAG3S,GAAG,CAACG,UAAU,CAAC,CAAC;YAClC,IAAIyS,yBAAyB,GAAG,KAAK;YACrC,IAAIrQ,QAAQ,IAAIA,QAAQ,CAACsQ,eAAe,EAAE;cACtC,IAAIC,UAAU,GAAGvQ,QAAQ,CAACsQ,eAAe,GAAGtQ,QAAQ,CAACpC,UAAU,GAAG,CAAC;cACnEyS,yBAAyB,GAAGE,UAAU,KAAKH,WAAW;YAC1D;YACA,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;cAChC,OAAOA,UAAU;YACrB,CAAC,MACI,IAAIA,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,KAAK,EAAE;cACjD,IAAIG,yBAAyB,IAAI,CAACrQ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACwB,MAAM,KAAK,IAAI,EAAE;gBAC5G;gBACA;gBACA,OAAOxB,QAAQ,CAACwB,MAAM,GAAG,EAAE,GAAG2O,EAAE;cACpC;YACJ;YACA,OAAO,IAAI;UACf;UACA,SAASzC,YAAYA,CAACjQ,GAAG,EAAE4B,OAAO,EAAEvD,MAAM,EAAE;YACxC,IAAIkF,IAAI,GAAG3B,OAAO,CAAC2B,IAAI,IAAI,EAAE;YAC7B,IAAIvB,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,EAAE;YAC7B,IAAI0G,IAAI,GAAG9G,OAAO,CAAC8G,IAAI,IAAI,EAAE;YAC7B,IAAI9G,OAAO,CAACmR,IAAI,EAAE;cACd,IAAIC,MAAM,GAAGpR,OAAO,CAACiM,iBAAiB;cACtC,IAAIxP,MAAM,EAAE;gBACR,IAAI4U,WAAW,GAAG,CAAC,CAAC,EAAEnS,YAAY,CAACwC,SAAS,EAAEtD,GAAG,EAAE4B,OAAO,CAACmR,IAAI,EAAE1U,MAAM,EAAE2U,MAAM,EAAEpR,OAAO,CAACkM,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtGvK,IAAI,GAAG0P,WAAW,CAAC1P,IAAI,IAAIA,IAAI;gBAC/BvB,IAAI,GAAGiR,WAAW,CAACjR,IAAI,IAAIuB,IAAI;gBAC/BmF,IAAI,GAAGuK,WAAW,CAACvK,IAAI,IAAInF,IAAI;cACnC,CAAC,MACI;gBACD1B,OAAO,CAACC,KAAK,CAAC,8CAA8C,CAAC;cACjE;YACJ;YACA,IAAIC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAImR,YAAY,CAAC3P,IAAI,EAAEvB,IAAI,EAAE0G,IAAI,CAAC;YAC/D,OAAO;cACH3G,OAAO,EAAEA,OAAO;cAChBwB,IAAI,EAAEA,IAAI;cACVvB,IAAI,EAAEA,IAAI;cACV0G,IAAI,EAAEA;YACV,CAAC;UACL;UACA,SAASwK,YAAYA,CAAC3P,IAAI,EAAEvB,IAAI,EAAE0G,IAAI,EAAE;YACpC,IAAIyK,QAAQ,GAAG5P,IAAI,CAAC,CAAC,CAAC,IAAIvB,IAAI,CAAC,CAAC,CAAC,IAAI0G,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;YAClD,IAAIQ,MAAM,GAAG,EAAE;YACfnK,MAAM,CAACmP,IAAI,CAACiF,QAAQ,CAAC,CAChBC,MAAM,CAAC,UAAUC,GAAG,EAAE;cAAE,OAAOA,GAAG,KAAK,UAAU;YAAE,CAAC,CAAC,CACrDC,OAAO,CAAC,UAAUD,GAAG,EAAE;cACxB,IAAIxE,OAAO,GAAG,CAAC;cACf,IAAI5M,KAAK;cACT,IAAI/C,KAAK,CAAC2G,OAAO,CAACsN,QAAQ,CAAC,EAAE;gBACzBlR,KAAK,GAAGkR,QAAQ,CAACvJ,QAAQ,CAACyJ,GAAG,CAAC,CAAC;cACnC,CAAC,MACI;gBACDpR,KAAK,GAAGkR,QAAQ,CAACE,GAAG,CAAC;cACzB;cACA,IAAI,OAAOpR,KAAK,KAAK,QAAQ,IAAI,CAAC/C,KAAK,CAAC2G,OAAO,CAAC5D,KAAK,CAAC,EAAE;gBACpD4M,OAAO,GAAG,CAAC5M,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC4M,OAAO,KAAK,CAAC;cAChF;cACA,KAAK,IAAI1Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0Q,OAAO,EAAE1Q,CAAC,EAAE,EAAE;gBAC9B,IAAI+R,EAAE,GAAG,KAAK,CAAC;gBACf,IAAIhR,KAAK,CAAC2G,OAAO,CAACsN,QAAQ,CAAC,EAAE;kBACzBjD,EAAE,GAAGhH,MAAM,CAACvH,MAAM;gBACtB,CAAC,MACI;kBACDuO,EAAE,GAAGmD,GAAG,IAAIlV,CAAC,GAAG,CAAC,GAAG,GAAG,CAACoV,MAAM,CAACpV,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC3C;gBACA,IAAIqV,SAAS,GAAG;kBAAEC,OAAO,EAAEvD;gBAAG,CAAC;gBAC/BhH,MAAM,CAACmF,IAAI,CAACmF,SAAS,CAAC;cAC1B;YACJ,CAAC,CAAC;YACF,OAAOtK,MAAM;UACjB;;UAGA;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAASxK,uBAAuB,EAAEf,OAAO,EAAE;UAGlDoB,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/D,SAASuB,SAASA,CAACpB,GAAG,EAAEzB,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,EAAE;YAC/C,IAAImB,OAAO,GAAG,SAAAA,CAAUjP,OAAO,EAAE;cAC7B,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;gBACxCC,OAAO,CAACC,KAAK,CAAC,sDAAsD,GAAG,OAAOF,OAAO,CAAC;cAC1F;cACA,IAAI,OAAOA,OAAO,CAAC8R,WAAW,KAAK,WAAW,EAAE;gBAC5C9R,OAAO,CAAC4Q,UAAU,GAAG5Q,OAAO,CAAC8R,WAAW,GAAG,MAAM,GAAG,MAAM;gBAC1D7R,OAAO,CAACC,KAAK,CAAC,gEAAgE,CAAC;cACnF;cACA,IAAI,OAAOF,OAAO,CAAC+R,OAAO,KAAK,WAAW,EAAE;gBACxC,IAAI,OAAO/R,OAAO,CAACkQ,MAAM,KAAK,WAAW,EACrClQ,OAAO,CAACkQ,MAAM,GAAGlQ,OAAO,CAAC+R,OAAO;gBACpC9R,OAAO,CAACC,KAAK,CAAC,wDAAwD,CAAC;cAC3E;cACA,IAAIF,OAAO,CAACmQ,MAAM,IAAI,OAAOnQ,OAAO,CAACmQ,MAAM,KAAK,QAAQ,EAAE;gBACtDlQ,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEF,OAAO,CAACmQ,MAAM,CAAC;gBAChE,OAAOnQ,OAAO,CAACmQ,MAAM;cACzB;cACA,IAAI,CAACnQ,OAAO,CAACyP,WAAW,KACnBzP,OAAO,CAACgS,gBAAgB,IACrBhS,OAAO,CAACiS,iBAAiB,IACzBjS,OAAO,CAACkS,YAAY,CAAC,EAAE;gBAC3BjS,OAAO,CAACC,KAAK,CAAC,wGAAwG,CAAC;gBACvHF,OAAO,CAACyP,WAAW,GAAG,UAAUzN,IAAI,EAAE;kBAClC5D,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;kBAC/B,IAAI/J,OAAO,CAACiS,iBAAiB,EACzBjS,OAAO,CAACiS,iBAAiB,CAACjQ,IAAI,CAAC;kBACnC5D,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;kBAC/B,IAAI/J,OAAO,CAACgS,gBAAgB,EACxBhS,OAAO,CAACgS,gBAAgB,CAAChQ,IAAI,CAAC;kBAClC5D,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;kBAC/B,IAAI/J,OAAO,CAACkS,YAAY,IAAIlQ,IAAI,CAACzD,UAAU,GAAG,CAAC,EAAE;oBAC7C;oBACAyD,IAAI,CAACkQ,YAAY,CAAClQ,IAAI,CAAC;kBAC3B;kBACA5D,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;gBACnC,CAAC;cACL;cACA;cACA,CACI,mBAAmB,EACnB,eAAe,EACf,SAAS,EACT,gBAAgB,CACnB,CAAC2H,OAAO,CAAC,UAAU/K,IAAI,EAAE;gBACtB,IAAI3G,OAAO,CAAC2G,IAAI,CAAC,EAAE;kBACf1G,OAAO,CAACC,KAAK,CAAC,QAAQ,CAACyR,MAAM,CAAChL,IAAI,EAAE,6EAA6E,CAAC,CAAC;gBACvH;cACJ,CAAC,CAAC;cACF,CACI,CAAC,UAAU,EAAE,YAAY,CAAC,EAC1B,CAAC,UAAU,EAAE,YAAY,CAAC,EAC1B,CAAC,aAAa,EAAE,gBAAgB,CAAC,EACjC,CAAC,cAAc,EAAE,aAAa,CAAC,EAC/B,CAAC,YAAY,EAAE,cAAc,CAAC,CACjC,CAAC+K,OAAO,CAAC,UAAUjQ,EAAE,EAAE;gBACpB,IAAIqM,OAAO,GAAGrM,EAAE,CAAC,CAAC,CAAC;kBAAE0Q,UAAU,GAAG1Q,EAAE,CAAC,CAAC,CAAC;gBACvC,IAAIzB,OAAO,CAACmS,UAAU,CAAC,EAAE;kBACrBlS,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAACyR,MAAM,CAACQ,UAAU,EAAE,QAAQ,CAAC,CAACR,MAAM,CAAC7D,OAAO,EAAE,UAAU,CAAC,CAAC;kBACnG9N,OAAO,CAAC8N,OAAO,CAAC,GAAG9N,OAAO,CAACmS,UAAU,CAAC;gBAC1C;cACJ,CAAC,CAAC;cACF,CACI,CAAC,SAAS,EAAE,aAAa,CAAC,EAC1B,CAAC,YAAY,EAAE,WAAW,CAAC,EAC3B,UAAU,EACV,UAAU,CACb,CAACT,OAAO,CAAC,UAAUU,CAAC,EAAE;gBACnB,IAAIC,gBAAgB,GAAG,OAAOD,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAI/K,KAAK,GAAG,OAAO+K,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;gBAC5C,IAAI,OAAOpS,OAAO,CAACqS,gBAAgB,CAAC,KAAK,WAAW,EAAE;kBAClD,IAAI,OAAOrS,OAAO,CAACgB,MAAM,CAACqG,KAAK,CAAC,KAAK,WAAW,EAAE;oBAC9CrH,OAAO,CAACgB,MAAM,CAACqG,KAAK,CAAC,GAAGrH,OAAO,CAACqS,gBAAgB,CAAC;kBACrD;kBACApS,OAAO,CAACC,KAAK,CAAC,4BAA4B,GACtCmS,gBAAgB,GAChB,kBAAkB,GAClBhL,KAAK,GACL,WAAW,CAAC;gBACpB;cACJ,CAAC,CAAC;cACF,KAAK,IAAIhC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAClB,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,YAAY,CACf,EAAED,EAAE,GAAGC,EAAE,CAACvF,MAAM,EAAEsF,EAAE,EAAE,EAAE;gBACrB,IAAIiN,SAAS,GAAGhN,EAAE,CAACD,EAAE,CAAC;gBACtBkN,WAAW,CAACvS,OAAO,CAACsS,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;cACzC;cACA,IAAItD,YAAY,GAAGhP,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;cAChD,KAAK,IAAIuF,EAAE,GAAG,CAAC,EAAEoK,EAAE,GAAGxS,MAAM,CAACmP,IAAI,CAAC0C,YAAY,CAAC,EAAEzJ,EAAE,GAAGoK,EAAE,CAAC5P,MAAM,EAAEwF,EAAE,EAAE,EAAE;gBACnE,IAAIkM,GAAG,GAAG9B,EAAE,CAACpK,EAAE,CAAC;gBAChBgN,WAAW,CAACvD,YAAY,CAACyC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;cACxC;YACJ,CAAC;YACD,KAAK,IAAI5R,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAG,CAAC9E,MAAM,EAAEyP,QAAQ,EAAE0B,OAAO,CAAC,EAAEjO,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;cACrE,IAAIG,OAAO,GAAGyB,EAAE,CAAC5B,EAAE,CAAC;cACpBoP,OAAO,CAACjP,OAAO,CAAC;YACpB;UACJ;UACAjE,OAAO,CAAC,SAAS,CAAC,GAAGyD,SAAS;UAC9B,SAAS+S,WAAWA,CAACvR,MAAM,EAAE;YACzB,IAAIA,MAAM,CAACwR,SAAS,EAAE;cAClBvS,OAAO,CAACC,KAAK,CAAC,oEAAoE,CAAC;cACnF,IAAI,CAACc,MAAM,CAACyF,aAAa,EAAE;gBACvBzF,MAAM,CAACyF,aAAa,GAAGzF,MAAM,CAACwR,SAAS;cAC3C;YACJ,CAAC,MACI,IAAIxR,MAAM,CAACyR,WAAW,EAAE;cACzBxS,OAAO,CAACC,KAAK,CAAC,kEAAkE,CAAC;cACjF,IAAI,CAACc,MAAM,CAACwF,SAAS,EAAE;gBACnBxF,MAAM,CAACwF,SAAS,GAAGxF,MAAM,CAACyR,WAAW;cACzC;YACJ;UACJ;;UAGA;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS3V,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAAC2W,MAAM,GAAG3W,OAAO,CAAC4W,IAAI,GAAG5W,OAAO,CAAC6W,GAAG,GAAG7W,OAAO,CAAC8W,KAAK,GAAG,KAAK,CAAC;UACpE,IAAI7G,QAAQ,GAAG/M,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAI6T,UAAU,GAAG7T,mBAAmB,CAAC,GAAG,CAAC;UACzC,IAAIkI,QAAQ,GAAGlI,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAI4T,KAAK,GAAG,aAAe,YAAY;YACnC,SAASA,KAAKA,CAACxS,KAAK,EAAEyB,OAAO,EAAE;cAC3B,IAAI,CAACvD,UAAU,GAAG,CAAC;cACnB;cACA;cACA;cACA,IAAI,CAACC,SAAS,GAAG,CAAC;cAClB,IAAI,CAAC8P,EAAE,GAAGjO,KAAK,CAACiO,EAAE;cAClB,IAAI,CAAC7P,QAAQ,GAAG4B,KAAK,CAAC5B,QAAQ;cAC9B,IAAI,CAACuC,MAAM,GAAGX,KAAK,CAACW,MAAM;cAC1B,IAAI,CAACkN,KAAK,GAAG7N,KAAK,CAAC6N,KAAK;cACxB,IAAI,CAAC/N,OAAO,GAAG2B,OAAO,CAAC3B,OAAO;cAC9B,IAAI,CAACwB,IAAI,GAAGG,OAAO,CAACH,IAAI;cACxB,IAAI,CAACvB,IAAI,GAAG0B,OAAO,CAAC1B,IAAI;cACxB,IAAI,CAAC0G,IAAI,GAAGhF,OAAO,CAACgF,IAAI;YAC5B;YACA+L,KAAK,CAACrV,SAAS,CAACuV,aAAa,GAAG,UAAU5S,OAAO,EAAE;cAC/C,OAAO,IAAI,CAACwB,IAAI,CAACyC,MAAM,CAAC,UAAU4O,GAAG,EAAEnU,GAAG,EAAE;gBAAE,OAAOmU,GAAG,GAAGnU,GAAG,CAACoU,gBAAgB,CAAC9S,OAAO,CAAC;cAAE,CAAC,EAAE,CAAC,CAAC;YACnG,CAAC;YACD0S,KAAK,CAACrV,SAAS,CAAC0V,aAAa,GAAG,UAAU/S,OAAO,EAAE;cAC/C,OAAO,IAAI,CAAC2G,IAAI,CAAC1C,MAAM,CAAC,UAAU4O,GAAG,EAAEnU,GAAG,EAAE;gBAAE,OAAOmU,GAAG,GAAGnU,GAAG,CAACoU,gBAAgB,CAAC9S,OAAO,CAAC;cAAE,CAAC,EAAE,CAAC,CAAC;YACnG,CAAC;YACD0S,KAAK,CAACrV,SAAS,CAAC2V,OAAO,GAAG,YAAY;cAClC,OAAO,IAAI,CAACxR,IAAI,CAACgQ,MAAM,CAAC,IAAI,CAACvR,IAAI,CAAC,CAACuR,MAAM,CAAC,IAAI,CAAC7K,IAAI,CAAC;YACxD,CAAC;YACD+L,KAAK,CAACrV,SAAS,CAAC4V,aAAa,GAAG,UAAUhV,GAAG,EAAEiV,QAAQ,EAAEzU,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,EAAE;cAChF,KAAK,IAAIuB,EAAE,GAAG,CAAC,EAAEyT,UAAU,GAAGD,QAAQ,EAAExT,EAAE,GAAGyT,UAAU,CAACvT,MAAM,EAAEF,EAAE,EAAE,EAAE;gBAClE,IAAI0T,OAAO,GAAGD,UAAU,CAACzT,EAAE,CAAC;gBAC5B,IAAImC,IAAI,GAAG,IAAI8Q,UAAU,CAAC5U,YAAY,CAACE,GAAG,EAAE,IAAI,EAAEQ,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,CAAC;gBAC5E,IAAIgJ,MAAM,GAAGiM,OAAO,CAACvR,IAAI,CAAC,KAAK,KAAK;gBACpC;gBACApD,IAAI,CAACiC,IAAI,GAAGvD,KAAK,CAAC2G,OAAO,CAACrF,IAAI,CAACiC,IAAI,CAAC,GAAGjC,IAAI,CAACiC,IAAI,GAAG,CAACjC,IAAI,CAACiC,IAAI,CAAC;gBAC9D,IAAIyG,MAAM,EAAE;kBACR,OAAO,KAAK;gBAChB;cACJ;cACA,OAAO,IAAI;YACf,CAAC;YACDuL,KAAK,CAACrV,SAAS,CAACgW,gBAAgB,GAAG,UAAUpV,GAAG,EAAEE,MAAM,EAAE;cACtDF,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;cAC/B,KAAK,IAAIlK,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAG,IAAI,CAACyM,KAAK,CAACuB,WAAW,EAAE5P,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;gBAChE,IAAI0T,OAAO,GAAG9R,EAAE,CAAC5B,EAAE,CAAC;gBACpB0T,OAAO,CAAC,IAAIT,UAAU,CAAC3U,QAAQ,CAACC,GAAG,EAAE,IAAI,EAAEE,MAAM,CAAC,CAAC;cACvD;YACJ,CAAC;YACDuU,KAAK,CAACrV,SAAS,CAACsH,QAAQ,GAAG,UAAU2O,SAAS,EAAE;cAC5C,IAAI,OAAO,IAAI,CAAChV,QAAQ,CAACmS,UAAU,KAAK,QAAQ,EAAE;gBAC9C,OAAO,IAAI,CAACnS,QAAQ,CAACmS,UAAU;cACnC,CAAC,MACI,IAAI,IAAI,CAACnS,QAAQ,CAACmS,UAAU,KAAK,MAAM,EAAE;gBAC1C,IAAI8C,YAAY,GAAG,IAAI,CAACvT,OAAO,CAACiE,MAAM,CAAC,UAAUuP,KAAK,EAAEC,GAAG,EAAE;kBAAE,OAAOD,KAAK,GAAGC,GAAG,CAACF,YAAY;gBAAE,CAAC,EAAE,CAAC,CAAC;gBACrG,OAAOA,YAAY;cACvB,CAAC,MACI;gBACD,IAAIxD,MAAM,GAAG,IAAI,CAACzR,QAAQ,CAACyR,MAAM;gBACjC,OAAOuD,SAAS,GAAGvD,MAAM,CAACvK,IAAI,GAAGuK,MAAM,CAACzK,KAAK;cACjD;YACJ,CAAC;YACD,OAAOoN,KAAK;UAChB,CAAC,CAAC,CAAE;UACJ9W,OAAO,CAAC8W,KAAK,GAAGA,KAAK;UACrB,IAAID,GAAG,GAAG,aAAe,YAAY;YACjC,SAASA,GAAGA,CAACiB,GAAG,EAAEC,KAAK,EAAE9U,OAAO,EAAE4N,KAAK,EAAEmH,kBAAkB,EAAE;cACzD,IAAIA,kBAAkB,KAAK,KAAK,CAAC,EAAE;gBAAEA,kBAAkB,GAAG,KAAK;cAAE;cACjE,IAAI,CAAC1I,MAAM,GAAG,CAAC;cACf,IAAI,CAACwI,GAAG,GAAGA,GAAG;cACd,IAAIA,GAAG,YAAY7H,QAAQ,CAAChG,YAAY,EAAE;gBACtC,IAAI,CAAC6N,GAAG,GAAGA,GAAG,CAAC3N,QAAQ;gBACvB,IAAI,CAACD,OAAO,GAAG4N,GAAG,CAAC3N,QAAQ;cAC/B;cACA,IAAI,CAAC4N,KAAK,GAAGA,KAAK;cAClB,IAAI,CAAC9U,OAAO,GAAGA,OAAO;cACtB,IAAI,CAAC4N,KAAK,GAAGA,KAAK;cAClB,IAAI,CAACmH,kBAAkB,GAAGA,kBAAkB;YAChD;YACAnB,GAAG,CAACpV,SAAS,CAACyV,gBAAgB,GAAG,UAAU9S,OAAO,EAAE;cAChD,IAAIpB,KAAK,GAAG,IAAI;cAChB,OAAOoB,OAAO,CAACiE,MAAM,CAAC,UAAU4O,GAAG,EAAElU,MAAM,EAAE;gBAAE,IAAI2C,EAAE;gBAAE,OAAO4C,IAAI,CAACC,GAAG,CAAC0O,GAAG,EAAE,CAAC,CAACvR,EAAE,GAAG1C,KAAK,CAAC6N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC,MAAM,IAAI,IAAIrS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4J,MAAM,KAAK,CAAC,CAAC;cAAE,CAAC,EAAE,CAAC,CAAC;YAC9K,CAAC;YACDuH,GAAG,CAACpV,SAAS,CAACwW,UAAU,GAAG,UAAU7T,OAAO,EAAE;cAC1C,IAAIpB,KAAK,GAAG,IAAI;cAChB,OAAQoB,OAAO,CAACqR,MAAM,CAAC,UAAU1S,MAAM,EAAE;gBACrC,IAAIF,IAAI,GAAGG,KAAK,CAAC6N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBACpC,IAAI,CAAClV,IAAI,EACL,OAAO,KAAK;gBAChB,OAAOA,IAAI,CAACoO,OAAO,GAAG,CAAC;cAC3B,CAAC,CAAC,CAACjN,MAAM,GAAG,CAAC;YACjB,CAAC;YACD6S,GAAG,CAACpV,SAAS,CAACyW,eAAe,GAAG,UAAU5I,MAAM,EAAElL,OAAO,EAAE;cACvD,OAAO,IAAI,CAAC8S,gBAAgB,CAAC9S,OAAO,CAAC,IAAIkL,MAAM;YACnD,CAAC;YACDuH,GAAG,CAACpV,SAAS,CAAC0W,mBAAmB,GAAG,UAAU/T,OAAO,EAAE/B,GAAG,EAAE;cACxD,IAAIW,KAAK,GAAG,IAAI;cAChB,OAAOoB,OAAO,CAACiE,MAAM,CAAC,UAAU4O,GAAG,EAAElU,MAAM,EAAE;gBACzC,IAAIF,IAAI,GAAGG,KAAK,CAAC6N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBACpC,IAAI,CAAClV,IAAI,EACL,OAAO,CAAC;gBACZ,IAAIuV,UAAU,GAAIvV,IAAI,CAACoC,MAAM,CAAC8B,QAAQ,GAAG1E,GAAG,CAACyE,WAAW,CAAC,CAAC,GAAImJ,QAAQ,CAACtJ,cAAc;gBACrF,IAAI0R,QAAQ,GAAGxV,IAAI,CAACiJ,OAAO,CAAC,UAAU,CAAC;gBACvC,IAAIwM,YAAY,GAAGD,QAAQ,GAAGD,UAAU;gBACxC,OAAOE,YAAY,GAAGrB,GAAG,GAAGqB,YAAY,GAAGrB,GAAG;cAClD,CAAC,EAAE,CAAC,CAAC;YACT,CAAC;YACD,OAAOJ,GAAG;UACd,CAAC,CAAC,CAAE;UACJ7W,OAAO,CAAC6W,GAAG,GAAGA,GAAG;UACjB,IAAID,IAAI,GAAG,aAAe,YAAY;YAClC,SAASA,IAAIA,CAACkB,GAAG,EAAE7S,MAAM,EAAEhC,OAAO,EAAE;cAChC,IAAIyC,EAAE,EAAE4D,EAAE;cACV,IAAI,CAACiP,aAAa,GAAG,CAAC;cACtB,IAAI,CAACC,YAAY,GAAG,CAAC;cACrB,IAAI,CAACb,YAAY,GAAG,CAAC;cACrB,IAAI,CAACc,gBAAgB,GAAG,CAAC;cACzB,IAAI,CAACC,QAAQ,GAAG,CAAC;cACjB,IAAI,CAACzP,KAAK,GAAG,CAAC;cACd,IAAI,CAACqG,MAAM,GAAG,CAAC;cACf,IAAI,CAACvK,CAAC,GAAG,CAAC;cACV,IAAI,CAACC,CAAC,GAAG,CAAC;cACV,IAAI,CAACC,MAAM,GAAGA,MAAM;cACpB,IAAI,CAAChC,OAAO,GAAGA,OAAO;cACtB,IAAI,CAAC6U,GAAG,GAAGA,GAAG;cACd,IAAI/R,OAAO,GAAG+R,GAAG;cACjB,IAAIA,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACvW,KAAK,CAAC2G,OAAO,CAAC4P,GAAG,CAAC,EAAE;gBAC/D,IAAI,CAAC7G,OAAO,GAAG6G,GAAG,CAAC7G,OAAO,IAAI,CAAC;gBAC/B,IAAI,CAACC,OAAO,GAAG4G,GAAG,CAAC5G,OAAO,IAAI,CAAC;gBAC/BnL,OAAO,GAAG,CAACuD,EAAE,GAAG,CAAC5D,EAAE,GAAGoS,GAAG,CAAC/R,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGoS,GAAG,CAACa,KAAK,MAAM,IAAI,IAAIrP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGwO,GAAG;gBACnH,IAAIA,GAAG,CAAC3N,QAAQ,EAAE;kBACd,IAAI,CAAC2N,GAAG,GAAGA,GAAG,CAAC3N,QAAQ;gBAC3B;cACJ,CAAC,MACI;gBACD,IAAI,CAAC8G,OAAO,GAAG,CAAC;gBAChB,IAAI,CAACC,OAAO,GAAG,CAAC;cACpB;cACA;cACA,IAAIpM,IAAI,GAAGiB,OAAO,IAAI,IAAI,GAAG,EAAE,GAAGA,OAAO,GAAG,EAAE;cAC9C,IAAIkB,UAAU,GAAG,aAAa;cAC9B,IAAI,CAACnC,IAAI,GAAGA,IAAI,CAACwC,KAAK,CAACL,UAAU,CAAC;YACtC;YACA2P,IAAI,CAACnV,SAAS,CAACmX,UAAU,GAAG,YAAY;cACpC,IAAI5T,CAAC;cACL,IAAI,IAAI,CAACC,MAAM,CAACmC,MAAM,KAAK,KAAK,EAAE;gBAC9BpC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAAC8G,OAAO,CAAC,KAAK,CAAC;cACpC,CAAC,MACI,IAAI,IAAI,CAAC7G,MAAM,CAACmC,MAAM,KAAK,QAAQ,EAAE;gBACtCpC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACsK,MAAM,GAAG,IAAI,CAACxD,OAAO,CAAC,QAAQ,CAAC;cACrD,CAAC,MACI;gBACD,IAAI+M,SAAS,GAAG,IAAI,CAACvJ,MAAM,GAAG,IAAI,CAACxD,OAAO,CAAC,UAAU,CAAC;gBACtD9G,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG6T,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC/M,OAAO,CAAC,KAAK,CAAC;cACpD;cACA,IAAI/G,CAAC;cACL,IAAI,IAAI,CAACE,MAAM,CAACoC,MAAM,KAAK,OAAO,EAAE;gBAChCtC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACkE,KAAK,GAAG,IAAI,CAAC6C,OAAO,CAAC,OAAO,CAAC;cACnD,CAAC,MACI,IAAI,IAAI,CAAC7G,MAAM,CAACoC,MAAM,KAAK,QAAQ,EAAE;gBACtC,IAAIyR,QAAQ,GAAG,IAAI,CAAC7P,KAAK,GAAG,IAAI,CAAC6C,OAAO,CAAC,YAAY,CAAC;gBACtD/G,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG+T,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAChN,OAAO,CAAC,MAAM,CAAC;cACpD,CAAC,MACI;gBACD/G,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAAC+G,OAAO,CAAC,MAAM,CAAC;cACrC;cACA,OAAO;gBAAE/G,CAAC,EAAEA,CAAC;gBAAEC,CAAC,EAAEA;cAAE,CAAC;YACzB,CAAC;YACD4R,IAAI,CAACnV,SAAS,CAACsX,gBAAgB,GAAG,UAAUjS,WAAW,EAAE;cACrD,IAAIK,SAAS,GAAG5F,KAAK,CAAC2G,OAAO,CAAC,IAAI,CAACpD,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,CAACd,MAAM,GAAG,CAAC;cAC/D,IAAIoU,UAAU,GAAI,IAAI,CAACnT,MAAM,CAAC8B,QAAQ,GAAGD,WAAW,GAAImJ,QAAQ,CAACtJ,cAAc;cAC/E,IAAI2I,MAAM,GAAGnI,SAAS,GAAGiR,UAAU,GAAG,IAAI,CAACtM,OAAO,CAAC,UAAU,CAAC;cAC9D,OAAOxD,IAAI,CAACC,GAAG,CAAC+G,MAAM,EAAE,IAAI,CAACrK,MAAM,CAACyF,aAAa,CAAC;YACtD,CAAC;YACDkM,IAAI,CAACnV,SAAS,CAACqK,OAAO,GAAG,UAAUlB,IAAI,EAAE;cACrC,IAAIkB,OAAO,GAAG,CAAC,CAAC,EAAEV,QAAQ,CAACxD,YAAY,EAAE,IAAI,CAAC3C,MAAM,CAACuF,WAAW,EAAE,CAAC,CAAC;cACpE,IAAII,IAAI,KAAK,UAAU,EAAE;gBACrB,OAAOkB,OAAO,CAACrC,GAAG,GAAGqC,OAAO,CAACnC,MAAM;cACvC,CAAC,MACI,IAAIiB,IAAI,KAAK,YAAY,EAAE;gBAC5B,OAAOkB,OAAO,CAAClC,IAAI,GAAGkC,OAAO,CAACpC,KAAK;cACvC,CAAC,MACI;gBACD,OAAOoC,OAAO,CAAClB,IAAI,CAAC;cACxB;YACJ,CAAC;YACD,OAAOgM,IAAI;UACf,CAAC,CAAC,CAAE;UACJ5W,OAAO,CAAC4W,IAAI,GAAGA,IAAI;UACnB,IAAID,MAAM,GAAG,aAAe,YAAY;YACpC,SAASA,MAAMA,CAACb,OAAO,EAAEgC,GAAG,EAAEC,KAAK,EAAE;cACjC,IAAI,CAACJ,YAAY,GAAG,CAAC;cACrB,IAAI,CAACc,gBAAgB,GAAG,CAAC;cACzB,IAAI,CAACC,QAAQ,GAAG,CAAC;cACjB,IAAI,CAACzP,KAAK,GAAG,CAAC;cACd,IAAI,CAAC6M,OAAO,GAAGA,OAAO;cACtB,IAAI,CAACgC,GAAG,GAAGA,GAAG;cACd,IAAI,CAACC,KAAK,GAAGA,KAAK;YACtB;YACApB,MAAM,CAAClV,SAAS,CAACuX,qBAAqB,GAAG,UAAU1W,KAAK,EAAE;cACtD,IAAIiG,GAAG,GAAG,CAAC;cACX,KAAK,IAAIzE,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGpD,KAAK,CAAC8U,OAAO,CAAC,CAAC,EAAEtT,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;gBACzD,IAAIhB,GAAG,GAAG4C,EAAE,CAAC5B,EAAE,CAAC;gBAChB,IAAIjB,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC,IAAI,CAACkH,KAAK,CAAC;gBAChC,IAAIlV,IAAI,IAAI,OAAOA,IAAI,CAACoC,MAAM,CAACwF,SAAS,KAAK,QAAQ,EAAE;kBACnDlC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAE1F,IAAI,CAACoC,MAAM,CAACwF,SAAS,CAAC;gBAC9C;cACJ;cACA,OAAOlC,GAAG;YACd,CAAC;YACD,OAAOoO,MAAM;UACjB,CAAC,CAAC,CAAE;UACJ3W,OAAO,CAAC2W,MAAM,GAAGA,MAAM;;UAGvB;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS5V,uBAAuB,EAAEf,OAAO,EAAE;UAGlD;UACAoB,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACgS,MAAM,GAAG,KAAK,CAAC;UACvB;UACA,SAASA,MAAMA,CAACiH,MAAM,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;YACnC,IAAIJ,MAAM,IAAI,IAAI,EAAE;cAChB,MAAM,IAAIrX,SAAS,CAAC,4CAA4C,CAAC;YACrE;YACA,IAAI0X,EAAE,GAAGlY,MAAM,CAAC6X,MAAM,CAAC;YACvB,KAAK,IAAIlB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGhU,SAAS,CAACC,MAAM,EAAE+T,KAAK,EAAE,EAAE;cACnD;cACA,IAAIwB,UAAU,GAAGxV,SAAS,CAACgU,KAAK,CAAC;cACjC,IAAIwB,UAAU,IAAI,IAAI,EAAE;gBACpB;gBACA,KAAK,IAAIC,OAAO,IAAID,UAAU,EAAE;kBAC5B;kBACA,IAAInY,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC4X,UAAU,EAAEC,OAAO,CAAC,EAAE;oBAC3DF,EAAE,CAACE,OAAO,CAAC,GAAGD,UAAU,CAACC,OAAO,CAAC;kBACrC;gBACJ;cACJ;YACJ;YACA,OAAOF,EAAE;UACb;UACAtZ,OAAO,CAACgS,MAAM,GAAGA,MAAM;;UAGvB;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAASjR,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACwE,WAAW,GAAG,KAAK,CAAC;UAC5B,IAAInB,iBAAiB,GAAGH,mBAAmB,CAAC,GAAG,CAAC;UAChD,IAAIuW,QAAQ,GAAGvW,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIwW,iBAAiB,GAAGxW,mBAAmB,CAAC,GAAG,CAAC;UAChD,IAAI+M,QAAQ,GAAG/M,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAI2O,WAAW,GAAG3O,mBAAmB,CAAC,GAAG,CAAC;UAC1C,SAASsB,WAAWA,CAACmV,QAAQ,EAAErV,KAAK,EAAE;YAClC,IAAIjC,GAAG,GAAG,IAAIgB,iBAAiB,CAACgC,UAAU,CAACsU,QAAQ,CAAC;YACpD,IAAI5T,OAAO,GAAGuM,YAAY,CAAChO,KAAK,EAAEjC,GAAG,CAACyE,WAAW,CAAC,CAAC,CAAC;YACpD,IAAIxE,KAAK,GAAG,IAAImX,QAAQ,CAAC3C,KAAK,CAACxS,KAAK,EAAEyB,OAAO,CAAC;YAC9C,CAAC,CAAC,EAAE2T,iBAAiB,CAACE,eAAe,EAAEvX,GAAG,EAAEC,KAAK,CAAC;YAClDD,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;YAC/B,OAAO1L,KAAK;UAChB;UACAtC,OAAO,CAACwE,WAAW,GAAGA,WAAW;UACjC,SAAS8N,YAAYA,CAAChO,KAAK,EAAEyQ,EAAE,EAAE;YAC7B,IAAIhP,OAAO,GAAGzB,KAAK,CAACyB,OAAO;YAC3B,IAAI3B,OAAO,GAAGyV,aAAa,CAAC9T,OAAO,CAAC3B,OAAO,CAAC;YAC5C;YACA,IAAI2B,OAAO,CAACH,IAAI,CAAC5B,MAAM,KAAK,CAAC,EAAE;cAC3B,IAAI8V,UAAU,GAAGC,kBAAkB,CAAC3V,OAAO,EAAE,MAAM,CAAC;cACpD,IAAI0V,UAAU,EACV/T,OAAO,CAACH,IAAI,CAAC8K,IAAI,CAACoJ,UAAU,CAAC;YACrC;YACA,IAAI/T,OAAO,CAACgF,IAAI,CAAC/G,MAAM,KAAK,CAAC,EAAE;cAC3B,IAAI8V,UAAU,GAAGC,kBAAkB,CAAC3V,OAAO,EAAE,MAAM,CAAC;cACpD,IAAI0V,UAAU,EACV/T,OAAO,CAACgF,IAAI,CAAC2F,IAAI,CAACoJ,UAAU,CAAC;YACrC;YACA,IAAItF,KAAK,GAAGlQ,KAAK,CAAC5B,QAAQ,CAAC8R,KAAK;YAChC,IAAIvP,MAAM,GAAGX,KAAK,CAACW,MAAM;YACzB,OAAO;cACHb,OAAO,EAAEA,OAAO;cAChBwB,IAAI,EAAEoU,YAAY,CAAC,MAAM,EAAEjU,OAAO,CAACH,IAAI,EAAExB,OAAO,EAAEa,MAAM,EAAEuP,KAAK,EAAEO,EAAE,CAAC;cACpE1Q,IAAI,EAAE2V,YAAY,CAAC,MAAM,EAAEjU,OAAO,CAAC1B,IAAI,EAAED,OAAO,EAAEa,MAAM,EAAEuP,KAAK,EAAEO,EAAE,CAAC;cACpEhK,IAAI,EAAEiP,YAAY,CAAC,MAAM,EAAEjU,OAAO,CAACgF,IAAI,EAAE3G,OAAO,EAAEa,MAAM,EAAEuP,KAAK,EAAEO,EAAE;YACvE,CAAC;UACL;UACA,SAASiF,YAAYA,CAACC,WAAW,EAAEC,WAAW,EAAE9V,OAAO,EAAE+V,UAAU,EAAE3F,KAAK,EAAE1N,WAAW,EAAE;YACrF,IAAIsT,qBAAqB,GAAG,CAAC,CAAC;YAC9B,IAAI7O,MAAM,GAAG2O,WAAW,CAACrU,GAAG,CAAC,UAAUwU,MAAM,EAAEC,QAAQ,EAAE;cACrD,IAAIC,qBAAqB,GAAG,CAAC;cAC7B,IAAI1J,KAAK,GAAG,CAAC,CAAC;cACd,IAAI2J,aAAa,GAAG,CAAC;cACrB,IAAIC,eAAe,GAAG,CAAC;cACvB,KAAK,IAAI3W,EAAE,GAAG,CAAC,EAAE4W,SAAS,GAAGtW,OAAO,EAAEN,EAAE,GAAG4W,SAAS,CAAC1W,MAAM,EAAEF,EAAE,EAAE,EAAE;gBAC/D,IAAIf,MAAM,GAAG2X,SAAS,CAAC5W,EAAE,CAAC;gBAC1B,IAAIsW,qBAAqB,CAACrX,MAAM,CAACgV,KAAK,CAAC,IAAI,IAAI,IAC3CqC,qBAAqB,CAACrX,MAAM,CAACgV,KAAK,CAAC,CAACnO,IAAI,KAAK,CAAC,EAAE;kBAChD,IAAI6Q,eAAe,KAAK,CAAC,EAAE;oBACvB,IAAIE,OAAO,GAAG,KAAK,CAAC;oBACpB,IAAIpZ,KAAK,CAAC2G,OAAO,CAACmS,MAAM,CAAC,EAAE;sBACvBM,OAAO,GACHN,MAAM,CAACtX,MAAM,CAACgV,KAAK,GAAGyC,aAAa,GAAGD,qBAAqB,CAAC;oBACpE,CAAC,MACI;sBACDI,OAAO,GAAGN,MAAM,CAACtX,MAAM,CAAC+S,OAAO,CAAC;oBACpC;oBACA,IAAI8E,eAAe,GAAG,CAAC,CAAC;oBACxB,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAAI,CAACpZ,KAAK,CAAC2G,OAAO,CAACyS,OAAO,CAAC,EAAE;sBACxDC,eAAe,GAAG,CAACD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC1V,MAAM,KAAK,CAAC,CAAC;oBAC9F;oBACA,IAAIA,MAAM,GAAG+L,UAAU,CAACiJ,WAAW,EAAElX,MAAM,EAAEuX,QAAQ,EAAE9F,KAAK,EAAE2F,UAAU,EAAErT,WAAW,EAAE8T,eAAe,CAAC;oBACvG,IAAI/X,IAAI,GAAG,IAAI4W,QAAQ,CAAC7C,IAAI,CAAC+D,OAAO,EAAE1V,MAAM,EAAEgV,WAAW,CAAC;oBAC1D;oBACA;oBACApJ,KAAK,CAAC9N,MAAM,CAAC+S,OAAO,CAAC,GAAGjT,IAAI;oBAC5BgO,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC,GAAGlV,IAAI;oBAC1B4X,eAAe,GAAG5X,IAAI,CAACqO,OAAO,GAAG,CAAC;oBAClCkJ,qBAAqB,CAACrX,MAAM,CAACgV,KAAK,CAAC,GAAG;sBAClCnO,IAAI,EAAE/G,IAAI,CAACoO,OAAO,GAAG,CAAC;sBACtB4J,KAAK,EAAEJ;oBACX,CAAC;kBACL,CAAC,MACI;oBACDA,eAAe,EAAE;oBACjBD,aAAa,EAAE;kBACnB;gBACJ,CAAC,MACI;kBACDJ,qBAAqB,CAACrX,MAAM,CAACgV,KAAK,CAAC,CAACnO,IAAI,EAAE;kBAC1C6Q,eAAe,GAAGL,qBAAqB,CAACrX,MAAM,CAACgV,KAAK,CAAC,CAAC8C,KAAK;kBAC3DN,qBAAqB,EAAE;gBAC3B;cACJ;cACA,OAAO,IAAId,QAAQ,CAAC5C,GAAG,CAACwD,MAAM,EAAEC,QAAQ,EAAEL,WAAW,EAAEpJ,KAAK,CAAC;YACjE,CAAC,CAAC;YACF,OAAOtF,MAAM;UACjB;UACA,SAASwO,kBAAkBA,CAAC3V,OAAO,EAAEnB,OAAO,EAAE;YAC1C,IAAI6W,UAAU,GAAG,CAAC,CAAC;YACnB1V,OAAO,CAACuR,OAAO,CAAC,UAAUkC,GAAG,EAAE;cAC3B,IAAIA,GAAG,CAACC,GAAG,IAAI,IAAI,EAAE;gBACjB,IAAIa,KAAK,GAAGmC,eAAe,CAAC7X,OAAO,EAAE4U,GAAG,CAACC,GAAG,CAAC;gBAC7C,IAAIa,KAAK,IAAI,IAAI,EACbmB,UAAU,CAACjC,GAAG,CAAC/B,OAAO,CAAC,GAAG6C,KAAK;cACvC;YACJ,CAAC,CAAC;YACF,OAAOvX,MAAM,CAACmP,IAAI,CAACuJ,UAAU,CAAC,CAAC9V,MAAM,GAAG,CAAC,GAAG8V,UAAU,GAAG,IAAI;UACjE;UACA,SAASgB,eAAeA,CAAC7X,OAAO,EAAEF,MAAM,EAAE;YACtC,IAAIE,OAAO,KAAK,MAAM,EAAE;cACpB,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;gBAC5B,OAAOA,MAAM,CAACgY,MAAM,IAAIhY,MAAM,CAAC4V,KAAK,IAAI,IAAI;cAChD,CAAC,MACI,IAAI,OAAO5V,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;gBAC/D,OAAOA,MAAM;cACjB;YACJ,CAAC,MACI,IAAIE,OAAO,KAAK,MAAM,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;cACvD,OAAOA,MAAM,CAACiY,MAAM;YACxB;YACA,OAAO,IAAI;UACf;UACA,SAASnB,aAAaA,CAACzV,OAAO,EAAE;YAC5B,OAAOA,OAAO,CAACyB,GAAG,CAAC,UAAUvB,KAAK,EAAEyT,KAAK,EAAE;cACvC,IAAIrS,EAAE,EAAE4D,EAAE;cACV,IAAIoM,GAAG;cACP,IAAI,OAAOpR,KAAK,KAAK,QAAQ,EAAE;gBAC3BoR,GAAG,GAAG,CAACpM,EAAE,GAAG,CAAC5D,EAAE,GAAGpB,KAAK,CAACwR,OAAO,MAAM,IAAI,IAAIpQ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGpB,KAAK,CAACoR,GAAG,MAAM,IAAI,IAAIpM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGyO,KAAK;cACvH,CAAC,MACI;gBACDrC,GAAG,GAAGqC,KAAK;cACf;cACA,OAAO,IAAI0B,QAAQ,CAAC9C,MAAM,CAACjB,GAAG,EAAEpR,KAAK,EAAEyT,KAAK,CAAC;YACjD,CAAC,CAAC;UACN;UACA,SAAS/G,UAAUA,CAACiJ,WAAW,EAAElX,MAAM,EAAEuX,QAAQ,EAAEW,SAAS,EAAEhW,MAAM,EAAE6B,WAAW,EAAE8T,eAAe,EAAE;YAChG,IAAIpG,KAAK,GAAG,CAAC,CAAC,EAAEvE,QAAQ,CAAClG,QAAQ,EAAEkR,SAAS,CAAC;YAC7C,IAAIC,aAAa;YACjB,IAAIjB,WAAW,KAAK,MAAM,EAAE;cACxBiB,aAAa,GAAGjW,MAAM,CAAC4N,UAAU;YACrC,CAAC,MACI,IAAIoH,WAAW,KAAK,MAAM,EAAE;cAC7BiB,aAAa,GAAGjW,MAAM,CAAC6N,UAAU;YACrC,CAAC,MACI,IAAImH,WAAW,KAAK,MAAM,EAAE;cAC7BiB,aAAa,GAAGjW,MAAM,CAAC8N,UAAU;YACrC;YACA,IAAIoI,WAAW,GAAG,CAAC,CAAC,EAAEtJ,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAEwC,KAAK,CAAClS,KAAK,EAAEkS,KAAK,CAACyF,WAAW,CAAC,EAAEhV,MAAM,CAACA,MAAM,EAAEiW,aAAa,CAAC;YAC5G,IAAIjI,YAAY,GAAGhO,MAAM,CAACgO,YAAY,CAAClQ,MAAM,CAAC+S,OAAO,CAAC,IAClD7Q,MAAM,CAACgO,YAAY,CAAClQ,MAAM,CAACgV,KAAK,CAAC,IACjC,CAAC,CAAC;YACN,IAAIqD,SAAS,GAAGnB,WAAW,KAAK,MAAM,GAAGhH,YAAY,GAAG,CAAC,CAAC;YAC1D,IAAIoI,SAAS,GAAGpB,WAAW,KAAK,MAAM,IAAIK,QAAQ,GAAG,CAAC,KAAK,CAAC,GACtD,CAAC,CAAC,EAAEzI,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAEwC,KAAK,CAACxJ,YAAY,EAAE/F,MAAM,CAAC+N,kBAAkB,CAAC,GAC1E,CAAC,CAAC;YACR,IAAIsI,YAAY,GAAG,CAAC,CAAC,EAAErL,QAAQ,CAACjG,aAAa,EAAElD,WAAW,CAAC;YAC3D,IAAIyU,WAAW,GAAG,CAAC,CAAC,EAAE1J,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAEsJ,YAAY,EAAEH,WAAW,EAAEE,SAAS,EAAED,SAAS,CAAC;YAC9F,OAAO,CAAC,CAAC,EAAEvJ,WAAW,CAACG,MAAM,EAAEuJ,WAAW,EAAEX,eAAe,CAAC;UAChE;;UAGA;QAAM,CAAE;;QAER,KAAM,EAAE,EACR,KAAO,UAAS7Z,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAAC0G,OAAO,GAAG1G,OAAO,CAACyE,SAAS,GAAG,KAAK,CAAC;UAC5C,IAAIwL,QAAQ,GAAG/M,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIkI,QAAQ,GAAGlI,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIuW,QAAQ,GAAGvW,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIG,iBAAiB,GAAGH,mBAAmB,CAAC,GAAG,CAAC;UAChD,IAAI2O,WAAW,GAAG3O,mBAAmB,CAAC,GAAG,CAAC;UAC1C,IAAIE,eAAe,GAAGF,mBAAmB,CAAC,GAAG,CAAC;UAC9C,IAAIsY,cAAc,GAAGtY,mBAAmB,CAAC,GAAG,CAAC;UAC7C,SAASuB,SAASA,CAACkV,QAAQ,EAAErX,KAAK,EAAE;YAChC,IAAII,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;YAC7B,IAAI0R,MAAM,GAAG1R,QAAQ,CAAC0R,MAAM;YAC5B,IAAID,MAAM,GAAGzR,QAAQ,CAACyR,MAAM;YAC5B,IAAI5R,MAAM,GAAG;cACTwC,CAAC,EAAEoP,MAAM,CAACvK,IAAI;cACd5E,CAAC,EAAEoP;YACP,CAAC;YACD,IAAIqH,cAAc,GAAGnZ,KAAK,CAAC0U,aAAa,CAAC1U,KAAK,CAAC8B,OAAO,CAAC,GAAG9B,KAAK,CAAC6U,aAAa,CAAC7U,KAAK,CAAC8B,OAAO,CAAC;YAC5F,IAAIsX,iBAAiB,GAAGtH,MAAM,GAAGD,MAAM,CAACxK,MAAM,GAAG8R,cAAc;YAC/D,IAAI/Y,QAAQ,CAACiS,SAAS,KAAK,OAAO,EAAE;cAChC,IAAI3O,IAAI,GAAG1D,KAAK,CAAC8U,OAAO,CAAC,CAAC;cAC1B,IAAIuE,WAAW,GAAG3V,IAAI,CAACqC,MAAM,CAAC,UAAU4O,GAAG,EAAEnU,GAAG,EAAE;gBAAE,OAAOmU,GAAG,GAAGnU,GAAG,CAACwM,MAAM;cAAE,CAAC,EAAE,CAAC,CAAC;cAClFoM,iBAAiB,IAAIC,WAAW;YACpC;YACA,IAAItZ,GAAG,GAAG,IAAIgB,iBAAiB,CAACgC,UAAU,CAACsU,QAAQ,CAAC;YACpD,IAAIjX,QAAQ,CAACiS,SAAS,KAAK,QAAQ,IAC9BjS,QAAQ,CAAC0R,MAAM,IAAI,IAAI,IAAIsH,iBAAiB,GAAGrZ,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAACsG,MAAO,EAAE;cACxEsM,QAAQ,CAACvZ,GAAG,CAAC;cACbE,MAAM,CAACyC,CAAC,GAAGmP,MAAM,CAAC1K,GAAG;YACzB;YACA,IAAIjB,QAAQ,GAAG,CAAC,CAAC,EAAEqJ,WAAW,CAACG,MAAM,EAAE,CAAC,CAAC,EAAEzP,MAAM,CAAC;YAClDD,KAAK,CAAC4S,eAAe,GAAG7S,GAAG,CAACG,UAAU,CAAC,CAAC;YACxC,IAAIE,QAAQ,CAAC+R,mBAAmB,KAAK,IAAI,EAAE;cACvC;cACAoH,iCAAiC,CAACxZ,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,CAAC;YACnE,CAAC,MACI;cACD;cACAF,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;cAC/B,IAAItL,QAAQ,CAAC6R,QAAQ,KAAK,WAAW,IACjC7R,QAAQ,CAAC6R,QAAQ,KAAK,WAAW,EAAE;gBACnCjS,KAAK,CAACsD,IAAI,CAAC+P,OAAO,CAAC,UAAU7S,GAAG,EAAE;kBAC9B,OAAOgZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAED,KAAK,CAAC8B,OAAO,CAAC;gBAC3D,CAAC,CAAC;cACN;cACA/B,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;cAC/B1L,KAAK,CAAC+B,IAAI,CAACsR,OAAO,CAAC,UAAU7S,GAAG,EAAEiV,KAAK,EAAE;gBACrC,IAAIgE,SAAS,GAAGhE,KAAK,KAAKzV,KAAK,CAAC+B,IAAI,CAACL,MAAM,GAAG,CAAC;gBAC/CgY,YAAY,CAAC3Z,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEiZ,SAAS,EAAEvT,QAAQ,EAAEjG,MAAM,EAAED,KAAK,CAAC8B,OAAO,CAAC;cAC7E,CAAC,CAAC;cACF/B,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;cAC/B,IAAItL,QAAQ,CAAC4R,QAAQ,KAAK,UAAU,IAAI5R,QAAQ,CAAC4R,QAAQ,KAAK,WAAW,EAAE;gBACvEhS,KAAK,CAACyI,IAAI,CAAC4K,OAAO,CAAC,UAAU7S,GAAG,EAAE;kBAC9B,OAAOgZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAED,KAAK,CAAC8B,OAAO,CAAC;gBAC3D,CAAC,CAAC;cACN;YACJ;YACA,CAAC,CAAC,EAAEgH,QAAQ,CAACtD,cAAc,EAAEzF,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,CAAC;YAC1DD,KAAK,CAACmV,gBAAgB,CAACpV,GAAG,EAAEE,MAAM,CAAC;YACnCD,KAAK,CAAC8D,MAAM,GAAG7D,MAAM,CAACyC,CAAC;YACvB2U,QAAQ,CAACjV,aAAa,GAAGpC,KAAK;YAC9BqX,QAAQ,CAAChV,iBAAiB,GAAGrC,KAAK,CAAC,CAAC;YACpC,IAAIqX,QAAQ,CAAC/V,SAAS,EAClB+V,QAAQ,CAAC/V,SAAS,CAACgB,QAAQ,GAAGtC,KAAK,CAAC,CAAC;YACzCD,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;UACnC;UACAhO,OAAO,CAACyE,SAAS,GAAGA,SAAS;UAC7B,SAASoX,iCAAiCA,CAACxZ,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE;YACrE;YACA,IAAI0Z,sBAAsB,GAAGT,cAAc,CAACtW,OAAO,CAACgX,+BAA+B,CAAC7Z,GAAG,EAAEC,KAAK,CAAC;YAC/F2Z,sBAAsB,CAACpW,GAAG,CAAC,UAAUsW,cAAc,EAAEpE,KAAK,EAAE;cACxD1V,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;cAC/B;cACA,IAAI+J,KAAK,GAAG,CAAC,EAAE;gBACXrR,OAAO,CAACrE,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE4Z,cAAc,CAAC/X,OAAO,CAAC;cACjE,CAAC,MACI;gBACD;gBACAgY,SAAS,CAAC/Z,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE4Z,cAAc,CAAC/X,OAAO,CAAC;cACzD;cACA;cACAiY,SAAS,CAACha,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE4Z,cAAc,CAAC/X,OAAO,CAAC;cAC/D;cACAkY,SAAS,CAACja,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE4Z,cAAc,CAAC/X,OAAO,CAAC;YACzD,CAAC,CAAC;UACN;UACA,SAASgY,SAASA,CAAC/Z,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE6B,OAAO,EAAE;YAC5C,IAAI1B,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;YAC7BL,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;YAC/B,IAAItL,QAAQ,CAAC6R,QAAQ,KAAK,WAAW,IAAI7R,QAAQ,CAAC6R,QAAQ,KAAK,WAAW,EAAE;cACxEjS,KAAK,CAACsD,IAAI,CAAC+P,OAAO,CAAC,UAAU7S,GAAG,EAAE;gBAAE,OAAOgZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,CAAC;cAAE,CAAC,CAAC;YAC7F;UACJ;UACA,SAASiY,SAASA,CAACha,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,EAAE;YACtD/B,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;YAC/B1L,KAAK,CAAC+B,IAAI,CAACsR,OAAO,CAAC,UAAU7S,GAAG,EAAEiV,KAAK,EAAE;cACrC,IAAIgE,SAAS,GAAGhE,KAAK,KAAKzV,KAAK,CAAC+B,IAAI,CAACL,MAAM,GAAG,CAAC;cAC/CgY,YAAY,CAAC3Z,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEiZ,SAAS,EAAEvT,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,CAAC;YACvE,CAAC,CAAC;UACN;UACA,SAASkY,SAASA,CAACja,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE6B,OAAO,EAAE;YAC5C,IAAI1B,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;YAC7BL,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;YAC/B,IAAItL,QAAQ,CAAC4R,QAAQ,KAAK,UAAU,IAAI5R,QAAQ,CAAC4R,QAAQ,KAAK,WAAW,EAAE;cACvEhS,KAAK,CAACyI,IAAI,CAAC4K,OAAO,CAAC,UAAU7S,GAAG,EAAE;gBAAE,OAAOgZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,CAAC;cAAE,CAAC,CAAC;YAC7F;UACJ;UACA,SAASmY,qBAAqBA,CAAC1Z,IAAI,EAAE2Z,kBAAkB,EAAEna,GAAG,EAAE;YAC1D,IAAI+V,UAAU,GAAIvV,IAAI,CAACoC,MAAM,CAAC8B,QAAQ,GAAG1E,GAAG,CAACyE,WAAW,CAAC,CAAC,GAAImJ,QAAQ,CAACtJ,cAAc;YACrF,IAAI0R,QAAQ,GAAGxV,IAAI,CAACiJ,OAAO,CAAC,UAAU,CAAC;YACvC,IAAI2Q,cAAc,GAAGnU,IAAI,CAACoU,KAAK,CAAC,CAACF,kBAAkB,GAAGnE,QAAQ,IAAID,UAAU,CAAC;YAC7E,OAAO9P,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEkU,cAAc,CAAC;UACtC;UACA,SAASE,cAAcA,CAAC7Z,GAAG,EAAE0Z,kBAAkB,EAAEla,KAAK,EAAED,GAAG,EAAE;YACzD,IAAIwO,KAAK,GAAG,CAAC,CAAC;YACd/N,GAAG,CAACkV,kBAAkB,GAAG,IAAI;YAC7BlV,GAAG,CAACwM,MAAM,GAAG,CAAC;YACd,IAAImH,SAAS,GAAG,CAAC;YACjB,KAAK,IAAI3S,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGpD,KAAK,CAAC8B,OAAO,EAAEN,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;cACvD,IAAIf,MAAM,GAAG2C,EAAE,CAAC5B,EAAE,CAAC;cACnB,IAAIjB,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;cAClC,IAAI,CAAClV,IAAI,EACL;cACJ,IAAI,CAACtB,KAAK,CAAC2G,OAAO,CAACrF,IAAI,CAACiC,IAAI,CAAC,EAAE;gBAC3BjC,IAAI,CAACiC,IAAI,GAAG,CAACjC,IAAI,CAACiC,IAAI,CAAC;cAC3B;cACA,IAAI8X,aAAa,GAAG,IAAInD,QAAQ,CAAC7C,IAAI,CAAC/T,IAAI,CAACiV,GAAG,EAAEjV,IAAI,CAACoC,MAAM,EAAEpC,IAAI,CAACI,OAAO,CAAC;cAC1E2Z,aAAa,GAAG,CAAC,CAAC,EAAE/K,WAAW,CAACG,MAAM,EAAE4K,aAAa,EAAE/Z,IAAI,CAAC;cAC5D+Z,aAAa,CAAC9X,IAAI,GAAG,EAAE;cACvB,IAAI+X,kBAAkB,GAAGN,qBAAqB,CAAC1Z,IAAI,EAAE2Z,kBAAkB,EAAEna,GAAG,CAAC;cAC7E,IAAIQ,IAAI,CAACiC,IAAI,CAACd,MAAM,GAAG6Y,kBAAkB,EAAE;gBACvCD,aAAa,CAAC9X,IAAI,GAAGjC,IAAI,CAACiC,IAAI,CAACgY,MAAM,CAACD,kBAAkB,EAAEha,IAAI,CAACiC,IAAI,CAACd,MAAM,CAAC;cAC/E;cACA,IAAI8C,WAAW,GAAGzE,GAAG,CAACyE,WAAW,CAAC,CAAC;cACnCjE,IAAI,CAAC0V,aAAa,GAAG1V,IAAI,CAACkW,gBAAgB,CAACjS,WAAW,CAAC;cACvD,IAAIjE,IAAI,CAAC0V,aAAa,IAAIiE,kBAAkB,EAAE;gBAC1C3Z,IAAI,CAAC0V,aAAa,GAAGiE,kBAAkB;gBACvCI,aAAa,CAAC3X,MAAM,CAACyF,aAAa,IAAI8R,kBAAkB;cAC5D;cACA,IAAI3Z,IAAI,CAAC0V,aAAa,GAAGzV,GAAG,CAACwM,MAAM,EAAE;gBACjCxM,GAAG,CAACwM,MAAM,GAAGzM,IAAI,CAAC0V,aAAa;cACnC;cACAqE,aAAa,CAACrE,aAAa,GAAGqE,aAAa,CAAC7D,gBAAgB,CAACjS,WAAW,CAAC;cACzE,IAAI8V,aAAa,CAACrE,aAAa,GAAG9B,SAAS,EAAE;gBACzCA,SAAS,GAAGmG,aAAa,CAACrE,aAAa;cAC3C;cACA1H,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC,GAAG6E,aAAa;YACvC;YACA,IAAIG,YAAY,GAAG,IAAItD,QAAQ,CAAC5C,GAAG,CAAC/T,GAAG,CAACgV,GAAG,EAAE,CAAC,CAAC,EAAEhV,GAAG,CAACG,OAAO,EAAE4N,KAAK,EAAE,IAAI,CAAC;YAC1EkM,YAAY,CAACzN,MAAM,GAAGmH,SAAS;YAC/B,KAAK,IAAInN,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGjH,KAAK,CAAC8B,OAAO,EAAEkF,EAAE,GAAGC,EAAE,CAACvF,MAAM,EAAEsF,EAAE,EAAE,EAAE;cACvD,IAAIvG,MAAM,GAAGwG,EAAE,CAACD,EAAE,CAAC;cACnB,IAAIsT,aAAa,GAAGG,YAAY,CAAClM,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;cACpD,IAAI6E,aAAa,EAAE;gBACfA,aAAa,CAACtN,MAAM,GAAGyN,YAAY,CAACzN,MAAM;cAC9C;cACA,IAAIzM,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;cAClC,IAAIlV,IAAI,EAAE;gBACNA,IAAI,CAACyM,MAAM,GAAGxM,GAAG,CAACwM,MAAM;cAC5B;YACJ;YACA,OAAOyN,YAAY;UACvB;UACA,SAASC,wBAAwBA,CAAC3a,GAAG,EAAES,GAAG,EAAE0Z,kBAAkB,EAAEla,KAAK,EAAE;YACnE,IAAI2a,UAAU,GAAG5a,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAACsG,MAAM;YACtC,IAAI6E,MAAM,GAAG7R,KAAK,CAACI,QAAQ,CAACyR,MAAM;YAClC,IAAI+I,YAAY,GAAG/I,MAAM,CAAC1K,GAAG,GAAG0K,MAAM,CAACxK,MAAM;YAC7C,IAAIwT,YAAY,GAAGF,UAAU,GAAGC,YAAY;YAC5C,IAAIpa,GAAG,CAACG,OAAO,KAAK,MAAM,EAAE;cACxB;cACA;cACAka,YAAY,IACR7a,KAAK,CAAC0U,aAAa,CAAC1U,KAAK,CAAC8B,OAAO,CAAC,GAAG9B,KAAK,CAAC6U,aAAa,CAAC7U,KAAK,CAAC8B,OAAO,CAAC;YAC/E;YACA,IAAIgZ,YAAY,GAAGta,GAAG,CAACqV,mBAAmB,CAAC7V,KAAK,CAAC8B,OAAO,EAAE/B,GAAG,CAAC;YAC9D,IAAIgb,UAAU,GAAGD,YAAY,GAAGZ,kBAAkB;YAClD,IAAIY,YAAY,GAAGD,YAAY,EAAE;cAC7BjZ,OAAO,CAACC,KAAK,CAAC,gCAAgC,CAACyR,MAAM,CAAC9S,GAAG,CAACiV,KAAK,EAAE,iEAAiE,CAAC,CAAC;cACpI,OAAO,IAAI;YACf;YACA,IAAI,CAACsF,UAAU,EAAE;cACb,OAAO,KAAK;YAChB;YACA,IAAIC,iBAAiB,GAAGxa,GAAG,CAACmV,UAAU,CAAC3V,KAAK,CAAC8B,OAAO,CAAC;YACrD,IAAImZ,iBAAiB,GAAGza,GAAG,CAACoU,gBAAgB,CAAC5U,KAAK,CAAC8B,OAAO,CAAC,GAAG+Y,YAAY;YAC1E,IAAII,iBAAiB,EAAE;cACnB,IAAID,iBAAiB,EAAE;gBACnBpZ,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAACyR,MAAM,CAAC9S,GAAG,CAACiV,KAAK,EAAE,yIAAyI,CAAC,CAAC;cACrM;cACA,OAAO,IAAI;YACf;YACA,IAAIuF,iBAAiB,EAAE;cACnB;cACA,OAAO,KAAK;YAChB;YACA,IAAIhb,KAAK,CAACI,QAAQ,CAACkS,YAAY,KAAK,OAAO,EAAE;cACzC,OAAO,KAAK;YAChB;YACA;YACA,OAAO,IAAI;UACf;UACA,SAASoH,YAAYA,CAAC3Z,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEiZ,SAAS,EAAEvT,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,EAAE;YACzE,IAAIoZ,cAAc,GAAGC,qBAAqB,CAACpb,GAAG,EAAEC,KAAK,EAAEyZ,SAAS,EAAExZ,MAAM,CAAC;YACzE,IAAIO,GAAG,CAACoV,eAAe,CAACsF,cAAc,EAAEpZ,OAAO,CAAC,EAAE;cAC9C0X,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,CAAC;YAC9C,CAAC,MACI;cACD,IAAI4Y,wBAAwB,CAAC3a,GAAG,EAAES,GAAG,EAAE0a,cAAc,EAAElb,KAAK,CAAC,EAAE;gBAC3D,IAAIya,YAAY,GAAGJ,cAAc,CAAC7Z,GAAG,EAAE0a,cAAc,EAAElb,KAAK,EAAED,GAAG,CAAC;gBAClEyZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,CAAC;gBAC1CsC,OAAO,CAACrE,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,CAAC;gBAC9C4X,YAAY,CAAC3Z,GAAG,EAAEC,KAAK,EAAEya,YAAY,EAAEhB,SAAS,EAAEvT,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,CAAC;cAChF,CAAC,MACI;gBACDsC,OAAO,CAACrE,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,CAAC;gBAC9C4X,YAAY,CAAC3Z,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEiZ,SAAS,EAAEvT,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,CAAC;cACvE;YACJ;UACJ;UACA,SAAS0X,QAAQA,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,EAAE;YAChD7B,MAAM,CAACwC,CAAC,GAAGzC,KAAK,CAACI,QAAQ,CAACyR,MAAM,CAACvK,IAAI;YACrC,KAAK,IAAI9F,EAAE,GAAG,CAAC,EAAE4W,SAAS,GAAGtW,OAAO,EAAEN,EAAE,GAAG4W,SAAS,CAAC1W,MAAM,EAAEF,EAAE,EAAE,EAAE;cAC/D,IAAIf,MAAM,GAAG2X,SAAS,CAAC5W,EAAE,CAAC;cAC1B,IAAIjB,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;cAClC,IAAI,CAAClV,IAAI,EAAE;gBACPN,MAAM,CAACwC,CAAC,IAAIhC,MAAM,CAACkG,KAAK;gBACxB;cACJ;cACA5G,GAAG,CAAC2F,WAAW,CAACnF,IAAI,CAACoC,MAAM,CAAC;cAC5BpC,IAAI,CAACkC,CAAC,GAAGxC,MAAM,CAACwC,CAAC;cACjBlC,IAAI,CAACmC,CAAC,GAAGzC,MAAM,CAACyC,CAAC;cACjB,IAAIuG,MAAM,GAAGjJ,KAAK,CAAC+U,aAAa,CAAChV,GAAG,EAAEC,KAAK,CAAC6P,KAAK,CAACqB,YAAY,EAAE3Q,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,CAAC;cAC1F,IAAIgJ,MAAM,KAAK,KAAK,EAAE;gBAClBhJ,MAAM,CAACwC,CAAC,IAAIhC,MAAM,CAACkG,KAAK;gBACxB;cACJ;cACAyU,eAAe,CAACrb,GAAG,EAAEQ,IAAI,EAAEN,MAAM,CAAC;cAClC,IAAIob,OAAO,GAAG9a,IAAI,CAAC+V,UAAU,CAAC,CAAC;cAC/B,CAAC,CAAC,EAAExV,eAAe,CAAC8B,OAAO,EAAErC,IAAI,CAACiC,IAAI,EAAE6Y,OAAO,CAAC5Y,CAAC,EAAE4Y,OAAO,CAAC3Y,CAAC,EAAE;gBAC1DqC,MAAM,EAAExE,IAAI,CAACoC,MAAM,CAACoC,MAAM;gBAC1BD,MAAM,EAAEvE,IAAI,CAACoC,MAAM,CAACmC,MAAM;gBAC1BM,QAAQ,EAAEY,IAAI,CAACsV,IAAI,CAAC/a,IAAI,CAACoG,KAAK,GAAGpG,IAAI,CAACiJ,OAAO,CAAC,MAAM,CAAC,GAAGjJ,IAAI,CAACiJ,OAAO,CAAC,OAAO,CAAC;cACjF,CAAC,EAAEzJ,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC;cACrBL,KAAK,CAAC+U,aAAa,CAAChV,GAAG,EAAEC,KAAK,CAAC6P,KAAK,CAACsB,WAAW,EAAE5Q,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,CAAC;cAC5EA,MAAM,CAACwC,CAAC,IAAIhC,MAAM,CAACkG,KAAK;YAC5B;YACA1G,MAAM,CAACyC,CAAC,IAAIlC,GAAG,CAACwM,MAAM;UAC1B;UACA,SAASoO,eAAeA,CAACrb,GAAG,EAAEQ,IAAI,EAAEN,MAAM,EAAE;YACxC,IAAIyO,UAAU,GAAGnO,IAAI,CAACoC,MAAM;YAC5B5C,GAAG,CAACM,WAAW,CAAC,CAAC,CAACmM,YAAY,CAACzM,GAAG,CAACM,WAAW,CAAC,CAAC,CAACkb,YAAY,CAAC,CAAC,CAAC;YAChE,IAAI,OAAO7M,UAAU,CAACvI,SAAS,KAAK,QAAQ,EAAE;cAC1C;cACA,IAAII,SAAS,GAAG,CAAC,CAAC,EAAEuC,QAAQ,CAACvD,YAAY,EAAEmJ,UAAU,CAACvI,SAAS,EAAEuI,UAAU,CAAC9H,SAAS,CAAC;cACtF,IAAIL,SAAS,EAAE;gBACXxG,GAAG,CAACyG,IAAI,CAACjG,IAAI,CAACkC,CAAC,EAAExC,MAAM,CAACyC,CAAC,EAAEnC,IAAI,CAACoG,KAAK,EAAEpG,IAAI,CAACyM,MAAM,EAAEzG,SAAS,CAAC;cAClE;YACJ,CAAC,MACI,IAAI,OAAOmI,UAAU,CAACvI,SAAS,KAAK,QAAQ,EAAE;cAC/CpG,GAAG,CAACyG,IAAI,CAACjG,IAAI,CAACkC,CAAC,EAAExC,MAAM,CAACyC,CAAC,EAAEnC,IAAI,CAACoG,KAAK,EAAEpG,IAAI,CAACyM,MAAM,EAAE,GAAG,CAAC;cACxD,IAAIwO,KAAK,GAAG1c,MAAM,CAACmP,IAAI,CAACS,UAAU,CAACvI,SAAS,CAAC;cAC7C,IAAIsV,WAAW,GAAG/M,UAAU,CAACvI,SAAS;cACtCqV,KAAK,CAACjY,GAAG,CAAC,UAAUmY,IAAI,EAAE;gBACtB,IAAInV,SAAS,GAAG,CAAC,CAAC,EAAEuC,QAAQ,CAACvD,YAAY,EAAEkW,WAAW,CAACC,IAAI,CAAC,EAAEhN,UAAU,CAAC9H,SAAS,CAAC;gBACnF+U,iBAAiB,CAAC5b,GAAG,EAAEQ,IAAI,EAAEN,MAAM,EAAEyb,IAAI,EAAEnV,SAAS,IAAI,GAAG,EAAEkV,WAAW,CAACC,IAAI,CAAC,CAAC;cACnF,CAAC,CAAC;YACN;UACJ;UACA,SAASC,iBAAiBA,CAAC5b,GAAG,EAAEQ,IAAI,EAAEN,MAAM,EAAEyb,IAAI,EAAEnV,SAAS,EAAEJ,SAAS,EAAE;YACtE,IAAIyV,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;YAClB,QAAQL,IAAI;cACR,KAAK,KAAK;gBACNE,EAAE,GAAG3b,MAAM,CAACwC,CAAC;gBACboZ,EAAE,GAAG5b,MAAM,CAACyC,CAAC;gBACboZ,EAAE,GAAG7b,MAAM,CAACwC,CAAC,GAAGlC,IAAI,CAACoG,KAAK;gBAC1BoV,EAAE,GAAG9b,MAAM,CAACyC,CAAC;gBACb;cACJ,KAAK,MAAM;gBACPkZ,EAAE,GAAG3b,MAAM,CAACwC,CAAC;gBACboZ,EAAE,GAAG5b,MAAM,CAACyC,CAAC;gBACboZ,EAAE,GAAG7b,MAAM,CAACwC,CAAC;gBACbsZ,EAAE,GAAG9b,MAAM,CAACyC,CAAC,GAAGnC,IAAI,CAACyM,MAAM;gBAC3B;cACJ,KAAK,OAAO;gBACR4O,EAAE,GAAG3b,MAAM,CAACwC,CAAC,GAAGlC,IAAI,CAACoG,KAAK;gBAC1BkV,EAAE,GAAG5b,MAAM,CAACyC,CAAC;gBACboZ,EAAE,GAAG7b,MAAM,CAACwC,CAAC,GAAGlC,IAAI,CAACoG,KAAK;gBAC1BoV,EAAE,GAAG9b,MAAM,CAACyC,CAAC,GAAGnC,IAAI,CAACyM,MAAM;gBAC3B;cACJ;gBACI;gBACA4O,EAAE,GAAG3b,MAAM,CAACwC,CAAC;gBACboZ,EAAE,GAAG5b,MAAM,CAACyC,CAAC,GAAGnC,IAAI,CAACyM,MAAM,GAAG7G,SAAS;gBACvC2V,EAAE,GAAG7b,MAAM,CAACwC,CAAC,GAAGlC,IAAI,CAACoG,KAAK;gBAC1BoV,EAAE,GAAG9b,MAAM,CAACyC,CAAC,GAAGnC,IAAI,CAACyM,MAAM,GAAG7G,SAAS;gBACvC;YACR;YACApG,GAAG,CAACM,WAAW,CAAC,CAAC,CAACuM,YAAY,CAACzG,SAAS,CAAC;YACzCpG,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC2b,IAAI,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAExV,SAAS,CAAC;UACrD;UACA,SAAS4U,qBAAqBA,CAACpb,GAAG,EAAEC,KAAK,EAAEyZ,SAAS,EAAExZ,MAAM,EAAE;YAC1D,IAAIgc,mBAAmB,GAAGjc,KAAK,CAACI,QAAQ,CAACyR,MAAM,CAACxK,MAAM;YACtD,IAAI2K,QAAQ,GAAGhS,KAAK,CAACI,QAAQ,CAAC4R,QAAQ;YACtC,IAAIA,QAAQ,KAAK,WAAW,IAAKA,QAAQ,KAAK,UAAU,IAAIyH,SAAU,EAAE;cACpEwC,mBAAmB,IAAIjc,KAAK,CAAC6U,aAAa,CAAC7U,KAAK,CAAC8B,OAAO,CAAC;YAC7D;YACA,OAAO/B,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAACsG,MAAM,GAAG/M,MAAM,CAACyC,CAAC,GAAGuZ,mBAAmB;UACjE;UACA,SAAS7X,OAAOA,CAACrE,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,EAAE6B,OAAO,EAAE;YACpD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;cAAEA,OAAO,GAAG,EAAE;YAAE;YACxC/B,GAAG,CAAC2F,WAAW,CAAC3F,GAAG,CAAC2L,UAAU,CAAC;YAC/B,IAAI1L,KAAK,CAACI,QAAQ,CAAC4R,QAAQ,KAAK,WAAW,EAAE;cACzChS,KAAK,CAACyI,IAAI,CAAC4K,OAAO,CAAC,UAAU7S,GAAG,EAAE;gBAAE,OAAOgZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,CAAC;cAAE,CAAC,CAAC;YAC7F;YACA;YACA;YACA9B,KAAK,CAACmV,gBAAgB,CAACpV,GAAG,EAAEE,MAAM,CAAC;YACnC,IAAI4R,MAAM,GAAG7R,KAAK,CAACI,QAAQ,CAACyR,MAAM;YAClC,CAAC,CAAC,EAAE/I,QAAQ,CAACtD,cAAc,EAAEzF,GAAG,EAAEC,KAAK,EAAEkG,QAAQ,EAAEjG,MAAM,CAAC;YAC1DqZ,QAAQ,CAACvZ,GAAG,CAAC;YACbC,KAAK,CAACE,UAAU,EAAE;YAClBF,KAAK,CAACG,SAAS,EAAE;YACjBF,MAAM,CAACwC,CAAC,GAAGoP,MAAM,CAACvK,IAAI;YACtBrH,MAAM,CAACyC,CAAC,GAAGmP,MAAM,CAAC1K,GAAG;YACrBjB,QAAQ,CAACxD,CAAC,GAAGmP,MAAM,CAAC1K,GAAG;YACvB,IAAInH,KAAK,CAACI,QAAQ,CAAC6R,QAAQ,KAAK,WAAW,EAAE;cACzCjS,KAAK,CAACsD,IAAI,CAAC+P,OAAO,CAAC,UAAU7S,GAAG,EAAE;gBAAE,OAAOgZ,QAAQ,CAACzZ,GAAG,EAAEC,KAAK,EAAEQ,GAAG,EAAEP,MAAM,EAAE6B,OAAO,CAAC;cAAE,CAAC,CAAC;YAC7F;UACJ;UACApE,OAAO,CAAC0G,OAAO,GAAGA,OAAO;UACzB,SAASkV,QAAQA,CAACvZ,GAAG,EAAE;YACnB,IAAI0P,OAAO,GAAG1P,GAAG,CAACG,UAAU,CAAC,CAAC;YAC9BH,GAAG,CAACmN,OAAO,CAACuC,OAAO,GAAG,CAAC,CAAC;YACxB,IAAIyM,UAAU,GAAGnc,GAAG,CAACG,UAAU,CAAC,CAAC;YACjC,IAAIgc,UAAU,KAAKzM,OAAO,EAAE;cACxB1P,GAAG,CAACqE,OAAO,CAAC,CAAC;YACjB;UACJ;;UAGA;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS3F,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/D,IAAIkJ,QAAQ,GAAGlI,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIub,qBAAqB,GAAG,SAAAA,CAAUpc,GAAG,EAAEC,KAAK,EAAE;YAC9C,IAAI0T,OAAO,GAAG,CAAC,CAAC,EAAE5K,QAAQ,CAACxD,YAAY,EAAEtF,KAAK,CAACI,QAAQ,CAACyR,MAAM,EAAE,CAAC,CAAC;YAClE,IAAIuK,kBAAkB,GAAGrc,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAACC,KAAK,IAAI+M,OAAO,CAACpM,IAAI,GAAGoM,OAAO,CAACtM,KAAK,CAAC;YAC9E,OAAOgV,kBAAkB;UAC7B,CAAC;UACD;UACA,IAAIC,sBAAsB,GAAG,SAAAA,CAAUtc,GAAG,EAAEC,KAAK,EAAEsc,MAAM,EAAE;YACvD,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;cAAEA,MAAM,GAAG,CAAC,CAAC;YAAE;YACtC;YACA,IAAIF,kBAAkB,GAAGD,qBAAqB,CAACpc,GAAG,EAAEC,KAAK,CAAC;YAC1D,IAAIuc,cAAc,GAAGH,kBAAkB;YACvC;YACA,IAAIhK,yBAAyB,GAAGpS,KAAK,CAACI,QAAQ,CAACgS,yBAAyB;YACxE,IAAIoK,YAAY,GAAG,IAAI;YACvB,IAAIC,IAAI,GAAG,EAAE;YACb,IAAI3a,OAAO,GAAG,EAAE;YAChB,IAAI4a,GAAG,GAAG1c,KAAK,CAAC8B,OAAO,CAACJ,MAAM;YAC9B,IAAIxD,CAAC,GAAGoe,MAAM,IAAIA,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACK,KAAK,GAAG,CAAC;YACjD;YACA,IAAIvK,yBAAyB,IAAI,IAAI,EAAE;cACnCoK,YAAY,GAAGxc,KAAK,CAAC8B,OAAO,CAAC8a,IAAI,CAAC,UAAUC,IAAI,EAAE;gBAC9C,OAAOA,IAAI,CAACrJ,OAAO,KAAKpB,yBAAyB,IAC7CyK,IAAI,CAACpH,KAAK,KAAKrD,yBAAyB;cAChD,CAAC,CAAC;cACF,IAAIoK,YAAY,EAAE;gBACdC,IAAI,CAACrO,IAAI,CAACoO,YAAY,CAAC/G,KAAK,CAAC;gBAC7B3T,OAAO,CAACsM,IAAI,CAACpO,KAAK,CAAC8B,OAAO,CAAC0a,YAAY,CAAC/G,KAAK,CAAC,CAAC;gBAC/C8G,cAAc,GAAGA,cAAc,GAAGC,YAAY,CAACnH,YAAY;cAC/D;YACJ;YACA,OAAOnX,CAAC,GAAGwe,GAAG,EAAE;cACZ,IAAI,CAACF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC/G,KAAK,MAAMvX,CAAC,EAAE;gBACxFA,CAAC,EAAE,CAAC,CAAC;gBACL;cACJ;cACA,IAAI4e,QAAQ,GAAG9c,KAAK,CAAC8B,OAAO,CAAC5D,CAAC,CAAC,CAACmX,YAAY;cAC5C,IAAIkH,cAAc,GAAGO,QAAQ,EAAE;gBAC3B;gBACA,IAAI5e,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKoe,MAAM,CAACK,KAAK,EAAE;kBAC/B;kBACA;AAChB;kBACgBF,IAAI,CAACrO,IAAI,CAAClQ,CAAC,CAAC;kBACZ4D,OAAO,CAACsM,IAAI,CAACpO,KAAK,CAAC8B,OAAO,CAAC5D,CAAC,CAAC,CAAC;gBAClC;gBACA;gBACA;cACJ;cACAue,IAAI,CAACrO,IAAI,CAAClQ,CAAC,CAAC;cACZ4D,OAAO,CAACsM,IAAI,CAACpO,KAAK,CAAC8B,OAAO,CAAC5D,CAAC,CAAC,CAAC;cAC9Bqe,cAAc,GAAGA,cAAc,GAAGO,QAAQ;cAC1C5e,CAAC,EAAE;YACP;YACA,OAAO;cAAE6e,UAAU,EAAEN,IAAI;cAAE3a,OAAO,EAAEA,OAAO;cAAEkb,SAAS,EAAE9e;YAAE,CAAC;UAC/D,CAAC;UACD,IAAI0b,+BAA+B,GAAG,SAAAA,CAAU7Z,GAAG,EAAEC,KAAK,EAAE;YACxD;YACA;YACA,IAAIid,UAAU,GAAG,EAAE;YACnB,IAAIxH,KAAK,GAAG,CAAC;YACb,IAAIiH,GAAG,GAAG1c,KAAK,CAAC8B,OAAO,CAACJ,MAAM;YAC9B,OAAO+T,KAAK,GAAGiH,GAAG,EAAE;cAChB,IAAIzT,MAAM,GAAGoT,sBAAsB,CAACtc,GAAG,EAAEC,KAAK,EAAE;gBAC5C2c,KAAK,EAAElH,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA;cAC7B,CAAC,CAAC;cACF,IAAIxM,MAAM,IAAIA,MAAM,CAACnH,OAAO,IAAImH,MAAM,CAACnH,OAAO,CAACJ,MAAM,EAAE;gBACnD+T,KAAK,GAAGxM,MAAM,CAAC+T,SAAS;gBACxBC,UAAU,CAAC7O,IAAI,CAACnF,MAAM,CAAC;cAC3B,CAAC,MACI;gBACDwM,KAAK,EAAE;cACX;YACJ;YACA,OAAOwH,UAAU;UACrB,CAAC;UACDvf,OAAO,CAAC,SAAS,CAAC,GAAG;YACjB2e,sBAAsB,EAAEA,sBAAsB;YAC9CzC,+BAA+B,EAAEA,+BAA+B;YAChEuC,qBAAqB,EAAEA;UAC3B,CAAC;;UAGD;QAAM,CAAE;;QAER,KAAM,GAAG,EACT,KAAO,UAAS1d,uBAAuB,EAAEf,OAAO,EAAEkD,mBAAmB,EAAE;UAGvE9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;YAAEkC,KAAK,EAAE;UAAK,CAAE,CAAC;UAC/DlC,OAAO,CAACwf,SAAS,GAAGxf,OAAO,CAACyf,aAAa,GAAGzf,OAAO,CAAC4Z,eAAe,GAAG,KAAK,CAAC;UAC5E,IAAIxO,QAAQ,GAAGlI,mBAAmB,CAAC,GAAG,CAAC;UACvC,IAAIsY,cAAc,GAAGtY,mBAAmB,CAAC,GAAG,CAAC;UAC7C;AACA;AACA;UACA,SAAS0W,eAAeA,CAACvX,GAAG,EAAEC,KAAK,EAAE;YACjCod,SAAS,CAACrd,GAAG,EAAEC,KAAK,CAAC;YACrB,IAAIqd,gBAAgB,GAAG,EAAE;YACzB,IAAIC,iBAAiB,GAAG,CAAC;YACzBtd,KAAK,CAAC8B,OAAO,CAACuR,OAAO,CAAC,UAAU5S,MAAM,EAAE;cACpC,IAAI8c,WAAW,GAAG9c,MAAM,CAACiW,qBAAqB,CAAC1W,KAAK,CAAC;cACrD,IAAIud,WAAW,EAAE;gBACb;gBACA9c,MAAM,CAACkG,KAAK,GAAG4W,WAAW;cAC9B,CAAC,MACI;gBACD;gBACA9c,MAAM,CAACkG,KAAK,GAAGlG,MAAM,CAAC4U,YAAY;gBAClCgI,gBAAgB,CAACjP,IAAI,CAAC3N,MAAM,CAAC;cACjC;cACA6c,iBAAiB,IAAI7c,MAAM,CAACkG,KAAK;YACrC,CAAC,CAAC;YACF;YACA,IAAI6W,WAAW,GAAGxd,KAAK,CAACyG,QAAQ,CAAC1G,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG2W,iBAAiB;YAC1E;YACA,IAAIE,WAAW,EAAE;cACbA,WAAW,GAAGL,aAAa,CAACE,gBAAgB,EAAEG,WAAW,EAAE,UAAU/c,MAAM,EAAE;gBACzE,OAAOuF,IAAI,CAACC,GAAG,CAACxF,MAAM,CAAC0V,gBAAgB,EAAE1V,MAAM,CAAC2V,QAAQ,CAAC;cAC7D,CAAC,CAAC;YACN;YACA;YACA,IAAIoH,WAAW,EAAE;cACbA,WAAW,GAAGL,aAAa,CAACE,gBAAgB,EAAEG,WAAW,EAAE,UAAU/c,MAAM,EAAE;gBAAE,OAAOA,MAAM,CAAC2V,QAAQ;cAAE,CAAC,CAAC;YAC7G;YACAoH,WAAW,GAAGxX,IAAI,CAACyX,GAAG,CAACD,WAAW,CAAC;YACnC,IAAI,CAACxd,KAAK,CAACI,QAAQ,CAAC+R,mBAAmB,IACnCqL,WAAW,GAAG,GAAG,GAAGzd,GAAG,CAACyE,WAAW,CAAC,CAAC,EAAE;cACvC;cACA;cACA;cACA;cACAgZ,WAAW,GAAGA,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAGxX,IAAI,CAAC0X,KAAK,CAACF,WAAW,CAAC;cACrE5b,OAAO,CAACC,KAAK,CAAC,wBAAwB,CAACyR,MAAM,CAACkK,WAAW,EAAE,iCAAiC,CAAC,CAAC;YAClG;YACAG,aAAa,CAAC3d,KAAK,CAAC;YACpB4d,UAAU,CAAC5d,KAAK,EAAED,GAAG,CAAC;YACtB8d,aAAa,CAAC7d,KAAK,CAAC;UACxB;UACAtC,OAAO,CAAC4Z,eAAe,GAAGA,eAAe;UACzC,SAAS8F,SAASA,CAACrd,GAAG,EAAEC,KAAK,EAAE;YAC3B,IAAIyS,EAAE,GAAG1S,GAAG,CAACyE,WAAW,CAAC,CAAC;YAC1B,IAAI2N,mBAAmB,GAAGnS,KAAK,CAACI,QAAQ,CAAC+R,mBAAmB;YAC5D,IAAIiK,kBAAkB,GAAGlD,cAAc,CAACtW,OAAO,CAACuZ,qBAAqB,CAACpc,GAAG,EAAEC,KAAK,CAAC;YACjFA,KAAK,CAAC8U,OAAO,CAAC,CAAC,CAACzB,OAAO,CAAC,UAAU7S,GAAG,EAAE;cACnC,KAAK,IAAIgB,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGpD,KAAK,CAAC8B,OAAO,EAAEN,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;gBACvD,IAAIf,MAAM,GAAG2C,EAAE,CAAC5B,EAAE,CAAC;gBACnB,IAAIjB,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBAClC,IAAI,CAAClV,IAAI,EACL;gBACJ,IAAIsP,KAAK,GAAG7P,KAAK,CAAC6P,KAAK,CAACoB,YAAY;gBACpCjR,KAAK,CAAC+U,aAAa,CAAChV,GAAG,EAAE8P,KAAK,EAAEtP,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAE,IAAI,CAAC;gBACxD,IAAI+I,OAAO,GAAGjJ,IAAI,CAACiJ,OAAO,CAAC,YAAY,CAAC;gBACxCjJ,IAAI,CAAC2V,YAAY,GAAG,CAAC,CAAC,EAAEpN,QAAQ,CAACrD,cAAc,EAAElF,IAAI,CAACiC,IAAI,EAAEjC,IAAI,CAACoC,MAAM,EAAE5C,GAAG,CAAC,GAAGyJ,OAAO;gBACvF,IAAIsU,gBAAgB,GAAG,CAAC,CAAC,EAAEhV,QAAQ,CAACrD,cAAc,EAAElF,IAAI,CAACiC,IAAI,CAAC4M,IAAI,CAAC,GAAG,CAAC,CAACpK,KAAK,CAAC,KAAK,CAAC,EAAEzE,IAAI,CAACoC,MAAM,EAAE5C,GAAG,CAAC;gBACvGQ,IAAI,CAAC4V,gBAAgB,GAAG2H,gBAAgB,GAAGvd,IAAI,CAACiJ,OAAO,CAAC,YAAY,CAAC;gBACrE,IAAI,OAAOjJ,IAAI,CAACoC,MAAM,CAACwF,SAAS,KAAK,QAAQ,EAAE;kBAC3C5H,IAAI,CAAC6V,QAAQ,GAAG7V,IAAI,CAACoC,MAAM,CAACwF,SAAS;kBACrC5H,IAAI,CAAC8U,YAAY,GAAG9U,IAAI,CAACoC,MAAM,CAACwF,SAAS;gBAC7C,CAAC,MACI,IAAI5H,IAAI,CAACoC,MAAM,CAACwF,SAAS,KAAK,MAAM,IACrCgK,mBAAmB,KAAK,IAAI,EAAE;kBAC9B;kBACA,IAAI5R,IAAI,CAAC2V,YAAY,GAAGkG,kBAAkB,EAAE;oBACxC7b,IAAI,CAAC6V,QAAQ,GAAGgG,kBAAkB;oBAClC7b,IAAI,CAAC8U,YAAY,GAAG+G,kBAAkB;kBAC1C,CAAC,MACI;oBACD7b,IAAI,CAAC6V,QAAQ,GAAG7V,IAAI,CAAC2V,YAAY;oBACjC3V,IAAI,CAAC8U,YAAY,GAAG9U,IAAI,CAAC2V,YAAY;kBACzC;gBACJ,CAAC,MACI;kBACD;kBACA,IAAI6H,eAAe,GAAG,EAAE,GAAGtL,EAAE;kBAC7BlS,IAAI,CAAC6V,QAAQ,GAAG7V,IAAI,CAACoC,MAAM,CAAC0F,YAAY,IAAI0V,eAAe;kBAC3Dxd,IAAI,CAAC8U,YAAY,GAAG9U,IAAI,CAAC2V,YAAY;kBACrC,IAAI3V,IAAI,CAAC6V,QAAQ,GAAG7V,IAAI,CAAC8U,YAAY,EAAE;oBACnC9U,IAAI,CAAC8U,YAAY,GAAG9U,IAAI,CAAC6V,QAAQ;kBACrC;gBACJ;cACJ;YACJ,CAAC,CAAC;YACFpW,KAAK,CAAC8U,OAAO,CAAC,CAAC,CAACzB,OAAO,CAAC,UAAU7S,GAAG,EAAE;cACnC,KAAK,IAAIgB,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGpD,KAAK,CAAC8B,OAAO,EAAEN,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;gBACvD,IAAIf,MAAM,GAAG2C,EAAE,CAAC5B,EAAE,CAAC;gBACnB,IAAIjB,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBAClC;gBACA;gBACA,IAAIlV,IAAI,IAAIA,IAAI,CAACqO,OAAO,KAAK,CAAC,EAAE;kBAC5BnO,MAAM,CAAC4U,YAAY,GAAGrP,IAAI,CAACC,GAAG,CAACxF,MAAM,CAAC4U,YAAY,EAAE9U,IAAI,CAAC8U,YAAY,CAAC;kBACtE5U,MAAM,CAAC2V,QAAQ,GAAGpQ,IAAI,CAACC,GAAG,CAACxF,MAAM,CAAC2V,QAAQ,EAAE7V,IAAI,CAAC6V,QAAQ,CAAC;kBAC1D3V,MAAM,CAAC0V,gBAAgB,GAAGnQ,IAAI,CAACC,GAAG,CAACxF,MAAM,CAAC0V,gBAAgB,EAAE5V,IAAI,CAAC4V,gBAAgB,CAAC;gBACtF,CAAC,MACI;kBACD;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA,IAAIxF,YAAY,GAAG3Q,KAAK,CAAC2C,MAAM,CAACgO,YAAY,CAAClQ,MAAM,CAAC+S,OAAO,CAAC,IACxDxT,KAAK,CAAC2C,MAAM,CAACgO,YAAY,CAAClQ,MAAM,CAACgV,KAAK,CAAC,IACvC,CAAC,CAAC;kBACN,IAAItN,SAAS,GAAGwI,YAAY,CAACxI,SAAS,IAAIwI,YAAY,CAACtI,YAAY;kBACnE,IAAIF,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;oBAC5C1H,MAAM,CAAC2V,QAAQ,GAAGjO,SAAS;oBAC3B1H,MAAM,CAAC4U,YAAY,GAAGlN,SAAS;kBACnC;gBACJ;gBACA,IAAI5H,IAAI,EAAE;kBACN;kBACA,IAAIA,IAAI,CAACqO,OAAO,GAAG,CAAC,IAAI,CAACnO,MAAM,CAAC2V,QAAQ,EAAE;oBACtC3V,MAAM,CAAC2V,QAAQ,GAAG7V,IAAI,CAAC6V,QAAQ;kBACnC;kBACA,IAAI7V,IAAI,CAACqO,OAAO,GAAG,CAAC,IAAI,CAACnO,MAAM,CAAC4U,YAAY,EAAE;oBAC1C5U,MAAM,CAAC4U,YAAY,GAAG9U,IAAI,CAAC6V,QAAQ;kBACvC;gBACJ;cACJ;YACJ,CAAC,CAAC;UACN;UACA;AACA;AACA;UACA,SAAS+G,aAAaA,CAACrb,OAAO,EAAE0b,WAAW,EAAEQ,WAAW,EAAE;YACtD,IAAIC,kBAAkB,GAAGT,WAAW;YACpC,IAAIU,eAAe,GAAGpc,OAAO,CAACiE,MAAM,CAAC,UAAU4O,GAAG,EAAElU,MAAM,EAAE;cAAE,OAAOkU,GAAG,GAAGlU,MAAM,CAAC4U,YAAY;YAAE,CAAC,EAAE,CAAC,CAAC;YACrG,KAAK,IAAInX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,OAAO,CAACJ,MAAM,EAAExD,CAAC,EAAE,EAAE;cACrC,IAAIuC,MAAM,GAAGqB,OAAO,CAAC5D,CAAC,CAAC;cACvB,IAAIigB,KAAK,GAAG1d,MAAM,CAAC4U,YAAY,GAAG6I,eAAe;cACjD,IAAIE,eAAe,GAAGH,kBAAkB,GAAGE,KAAK;cAChD,IAAIE,cAAc,GAAG5d,MAAM,CAACkG,KAAK,GAAGyX,eAAe;cACnD,IAAIhI,QAAQ,GAAG4H,WAAW,CAACvd,MAAM,CAAC;cAClC,IAAI6d,QAAQ,GAAGD,cAAc,GAAGjI,QAAQ,GAAGA,QAAQ,GAAGiI,cAAc;cACpEb,WAAW,IAAIc,QAAQ,GAAG7d,MAAM,CAACkG,KAAK;cACtClG,MAAM,CAACkG,KAAK,GAAG2X,QAAQ;YAC3B;YACAd,WAAW,GAAGxX,IAAI,CAAC0X,KAAK,CAACF,WAAW,GAAG,IAAI,CAAC,GAAG,IAAI;YACnD;YACA;YACA,IAAIA,WAAW,EAAE;cACb,IAAIH,gBAAgB,GAAGvb,OAAO,CAACqR,MAAM,CAAC,UAAU1S,MAAM,EAAE;gBACpD,OAAO+c,WAAW,GAAG,CAAC,GAChB/c,MAAM,CAACkG,KAAK,GAAGqX,WAAW,CAACvd,MAAM,CAAC,CAAC;gBAAA,EACnC,IAAI,CAAC,CAAC;cAChB,CAAC,CAAC;;cACF,IAAI4c,gBAAgB,CAAC3b,MAAM,EAAE;gBACzB8b,WAAW,GAAGL,aAAa,CAACE,gBAAgB,EAAEG,WAAW,EAAEQ,WAAW,CAAC;cAC3E;YACJ;YACA,OAAOR,WAAW;UACtB;UACA9f,OAAO,CAACyf,aAAa,GAAGA,aAAa;UACrC,SAASU,aAAaA,CAAC7d,KAAK,EAAE;YAC1B,IAAIue,YAAY,GAAG,CAAC,CAAC;YACrB,IAAIC,eAAe,GAAG,CAAC;YACvB,IAAIC,GAAG,GAAGze,KAAK,CAAC8U,OAAO,CAAC,CAAC;YACzB,KAAK,IAAIkD,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGyG,GAAG,CAAC/c,MAAM,EAAEsW,QAAQ,EAAE,EAAE;cACtD,IAAIxX,GAAG,GAAGie,GAAG,CAACzG,QAAQ,CAAC;cACvB,KAAK,IAAIxW,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGpD,KAAK,CAAC8B,OAAO,EAAEN,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;gBACvD,IAAIf,MAAM,GAAG2C,EAAE,CAAC5B,EAAE,CAAC;gBACnB,IAAImC,IAAI,GAAG4a,YAAY,CAAC9d,MAAM,CAACgV,KAAK,CAAC;gBACrC,IAAI+I,eAAe,GAAG,CAAC,EAAE;kBACrBA,eAAe,EAAE;kBACjB,OAAOhe,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBAClC,CAAC,MACI,IAAI9R,IAAI,EAAE;kBACXA,IAAI,CAACpD,IAAI,CAACyM,MAAM,IAAIxM,GAAG,CAACwM,MAAM;kBAC9BwR,eAAe,GAAG7a,IAAI,CAACpD,IAAI,CAACqO,OAAO;kBACnC,OAAOpO,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;kBAC9B9R,IAAI,CAAC2D,IAAI,EAAE;kBACX,IAAI3D,IAAI,CAAC2D,IAAI,IAAI,CAAC,EAAE;oBAChB,OAAOiX,YAAY,CAAC9d,MAAM,CAACgV,KAAK,CAAC;kBACrC;gBACJ,CAAC,MACI;kBACD,IAAIlV,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;kBAClC,IAAI,CAAClV,IAAI,EAAE;oBACP;kBACJ;kBACAA,IAAI,CAACyM,MAAM,GAAGxM,GAAG,CAACwM,MAAM;kBACxB,IAAIzM,IAAI,CAACoO,OAAO,GAAG,CAAC,EAAE;oBAClB,IAAI+P,SAAS,GAAGD,GAAG,CAAC/c,MAAM,GAAGsW,QAAQ;oBACrC,IAAI1Q,IAAI,GAAG/G,IAAI,CAACoO,OAAO,GAAG+P,SAAS,GAAGA,SAAS,GAAGne,IAAI,CAACoO,OAAO;oBAC9D4P,YAAY,CAAC9d,MAAM,CAACgV,KAAK,CAAC,GAAG;sBAAElV,IAAI,EAAEA,IAAI;sBAAE+G,IAAI,EAAEA,IAAI;sBAAE9G,GAAG,EAAEA;oBAAI,CAAC;kBACrE;gBACJ;cACJ;YACJ;UACJ;UACA,SAASmd,aAAaA,CAAC3d,KAAK,EAAE;YAC1B,IAAIye,GAAG,GAAGze,KAAK,CAAC8U,OAAO,CAAC,CAAC;YACzB,KAAK,IAAIkD,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGyG,GAAG,CAAC/c,MAAM,EAAEsW,QAAQ,EAAE,EAAE;cACtD,IAAIxX,GAAG,GAAGie,GAAG,CAACzG,QAAQ,CAAC;cACvB,IAAI2G,WAAW,GAAG,IAAI;cACtB,IAAIC,oBAAoB,GAAG,CAAC;cAC5B,IAAIC,YAAY,GAAG,CAAC;cACpB,KAAK,IAAIC,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG9e,KAAK,CAAC8B,OAAO,CAACJ,MAAM,EAAEod,WAAW,EAAE,EAAE;gBACzE,IAAIre,MAAM,GAAGT,KAAK,CAAC8B,OAAO,CAACgd,WAAW,CAAC;gBACvC;gBACAD,YAAY,IAAI,CAAC;gBACjB,IAAIA,YAAY,GAAG,CAAC,IAAI7e,KAAK,CAAC8B,OAAO,CAACgd,WAAW,GAAG,CAAC,CAAC,EAAE;kBACpDF,oBAAoB,IAAIne,MAAM,CAACkG,KAAK;kBACpC,OAAOnG,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBAClC,CAAC,MACI,IAAIkJ,WAAW,EAAE;kBAClB,IAAIpe,IAAI,GAAGoe,WAAW;kBACtB,OAAOne,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;kBAC9BkJ,WAAW,GAAG,IAAI;kBAClBpe,IAAI,CAACoG,KAAK,GAAGlG,MAAM,CAACkG,KAAK,GAAGiY,oBAAoB;gBACpD,CAAC,MACI;kBACD,IAAIre,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;kBAClC,IAAI,CAAClV,IAAI,EACL;kBACJse,YAAY,GAAGte,IAAI,CAACqO,OAAO;kBAC3BgQ,oBAAoB,GAAG,CAAC;kBACxB,IAAIre,IAAI,CAACqO,OAAO,GAAG,CAAC,EAAE;oBAClB+P,WAAW,GAAGpe,IAAI;oBAClBqe,oBAAoB,IAAIne,MAAM,CAACkG,KAAK;oBACpC;kBACJ;kBACApG,IAAI,CAACoG,KAAK,GAAGlG,MAAM,CAACkG,KAAK,GAAGiY,oBAAoB;gBACpD;cACJ;YACJ;UACJ;UACA,SAAShB,UAAUA,CAAC5d,KAAK,EAAED,GAAG,EAAE;YAC5B,IAAIgf,aAAa,GAAG;cAAEC,KAAK,EAAE,CAAC;cAAEhS,MAAM,EAAE;YAAE,CAAC;YAC3C,KAAK,IAAIxL,EAAE,GAAG,CAAC,EAAE4B,EAAE,GAAGpD,KAAK,CAAC8U,OAAO,CAAC,CAAC,EAAEtT,EAAE,GAAG4B,EAAE,CAAC1B,MAAM,EAAEF,EAAE,EAAE,EAAE;cACzD,IAAIhB,GAAG,GAAG4C,EAAE,CAAC5B,EAAE,CAAC;cAChB,KAAK,IAAIwF,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGjH,KAAK,CAAC8B,OAAO,EAAEkF,EAAE,GAAGC,EAAE,CAACvF,MAAM,EAAEsF,EAAE,EAAE,EAAE;gBACvD,IAAIvG,MAAM,GAAGwG,EAAE,CAACD,EAAE,CAAC;gBACnB,IAAIzG,IAAI,GAAGC,GAAG,CAAC+N,KAAK,CAAC9N,MAAM,CAACgV,KAAK,CAAC;gBAClC,IAAI,CAAClV,IAAI,EACL;gBACJR,GAAG,CAAC2F,WAAW,CAACnF,IAAI,CAACoC,MAAM,EAAE,IAAI,CAAC;gBAClC,IAAIsc,SAAS,GAAG1e,IAAI,CAACoG,KAAK,GAAGpG,IAAI,CAACiJ,OAAO,CAAC,YAAY,CAAC;gBACvD,IAAIjJ,IAAI,CAACoC,MAAM,CAACqF,QAAQ,KAAK,WAAW,EAAE;kBACtC;kBACAzH,IAAI,CAACiC,IAAI,GAAGzC,GAAG,CAAC8M,eAAe,CAACtM,IAAI,CAACiC,IAAI,EAAEyc,SAAS,GAAG,CAAC,GAAGlf,GAAG,CAACyE,WAAW,CAAC,CAAC,EAAE;oBAAEC,QAAQ,EAAElE,IAAI,CAACoC,MAAM,CAAC8B;kBAAS,CAAC,CAAC;gBACrH,CAAC,MACI,IAAIlE,IAAI,CAACoC,MAAM,CAACqF,QAAQ,KAAK,WAAW,EAAE;kBAC3CzH,IAAI,CAACiC,IAAI,GAAG0a,SAAS,CAAC3c,IAAI,CAACiC,IAAI,EAAEyc,SAAS,EAAE1e,IAAI,CAACoC,MAAM,EAAE5C,GAAG,EAAE,KAAK,CAAC;gBACxE,CAAC,MACI,IAAIQ,IAAI,CAACoC,MAAM,CAACqF,QAAQ,KAAK,QAAQ,EAAE;kBACxCzH,IAAI,CAACiC,IAAI,GAAG0a,SAAS,CAAC3c,IAAI,CAACiC,IAAI,EAAEyc,SAAS,EAAE1e,IAAI,CAACoC,MAAM,EAAE5C,GAAG,EAAE,EAAE,CAAC;gBACrE,CAAC,MACI,IAAI,OAAOQ,IAAI,CAACoC,MAAM,CAACqF,QAAQ,KAAK,UAAU,EAAE;kBACjD,IAAIiB,MAAM,GAAG1I,IAAI,CAACoC,MAAM,CAACqF,QAAQ,CAACzH,IAAI,CAACiC,IAAI,EAAEyc,SAAS,CAAC;kBACvD,IAAI,OAAOhW,MAAM,KAAK,QAAQ,EAAE;oBAC5B1I,IAAI,CAACiC,IAAI,GAAG,CAACyG,MAAM,CAAC;kBACxB,CAAC,MACI;oBACD1I,IAAI,CAACiC,IAAI,GAAGyG,MAAM;kBACtB;gBACJ;gBACA1I,IAAI,CAAC0V,aAAa,GAAG1V,IAAI,CAACkW,gBAAgB,CAAC1W,GAAG,CAACyE,WAAW,CAAC,CAAC,CAAC;gBAC7D,IAAI0a,iBAAiB,GAAG3e,IAAI,CAAC0V,aAAa,GAAG1V,IAAI,CAACoO,OAAO;gBACzD,IAAIpO,IAAI,CAACoO,OAAO,GAAG,CAAC,IAChBoQ,aAAa,CAACC,KAAK,GAAGD,aAAa,CAAC/R,MAAM,GACtCkS,iBAAiB,GAAG3e,IAAI,CAACoO,OAAO,EAAE;kBACtCoQ,aAAa,GAAG;oBAAE/R,MAAM,EAAEkS,iBAAiB;oBAAEF,KAAK,EAAEze,IAAI,CAACoO;kBAAQ,CAAC;gBACtE,CAAC,MACI,IAAIoQ,aAAa,IAAIA,aAAa,CAACC,KAAK,GAAG,CAAC,EAAE;kBAC/C,IAAID,aAAa,CAAC/R,MAAM,GAAGkS,iBAAiB,EAAE;oBAC1CA,iBAAiB,GAAGH,aAAa,CAAC/R,MAAM;kBAC5C;gBACJ;gBACA,IAAIkS,iBAAiB,GAAG1e,GAAG,CAACwM,MAAM,EAAE;kBAChCxM,GAAG,CAACwM,MAAM,GAAGkS,iBAAiB;gBAClC;cACJ;cACAH,aAAa,CAACC,KAAK,EAAE;YACzB;UACJ;UACA,SAAS9B,SAASA,CAAC1a,IAAI,EAAEmE,KAAK,EAAEhE,MAAM,EAAE5C,GAAG,EAAEiI,QAAQ,EAAE;YACnD,OAAOxF,IAAI,CAACe,GAAG,CAAC,UAAU4b,GAAG,EAAE;cAAE,OAAOC,YAAY,CAACD,GAAG,EAAExY,KAAK,EAAEhE,MAAM,EAAE5C,GAAG,EAAEiI,QAAQ,CAAC;YAAE,CAAC,CAAC;UAC/F;UACAtK,OAAO,CAACwf,SAAS,GAAGA,SAAS;UAC7B,SAASkC,YAAYA,CAAC5c,IAAI,EAAEmE,KAAK,EAAEhE,MAAM,EAAE5C,GAAG,EAAEiI,QAAQ,EAAE;YACtD,IAAIqX,SAAS,GAAG,KAAK,GAAGtf,GAAG,CAACyE,WAAW,CAAC,CAAC;YACzCmC,KAAK,GAAGX,IAAI,CAACsV,IAAI,CAAC3U,KAAK,GAAG0Y,SAAS,CAAC,GAAGA,SAAS;YAChD,IAAI1Y,KAAK,IAAI,CAAC,CAAC,EAAEmC,QAAQ,CAACrD,cAAc,EAAEjD,IAAI,EAAEG,MAAM,EAAE5C,GAAG,CAAC,EAAE;cAC1D,OAAOyC,IAAI;YACf;YACA,OAAOmE,KAAK,GAAG,CAAC,CAAC,EAAEmC,QAAQ,CAACrD,cAAc,EAAEjD,IAAI,GAAGwF,QAAQ,EAAErF,MAAM,EAAE5C,GAAG,CAAC,EAAE;cACvE,IAAIyC,IAAI,CAACd,MAAM,IAAI,CAAC,EAAE;gBAClB;cACJ;cACAc,IAAI,GAAGA,IAAI,CAAC8c,SAAS,CAAC,CAAC,EAAE9c,IAAI,CAACd,MAAM,GAAG,CAAC,CAAC;YAC7C;YACA,OAAOc,IAAI,CAAC2M,IAAI,CAAC,CAAC,GAAGnH,QAAQ;UACjC;;UAGA;QAAM,CAAE;;QAER,KAAM,EAAE,EACR,KAAO,UAASrK,MAAM,EAAE;UAExB,IAAG,OAAOY,+BAA+B,KAAK,WAAW,EAAE;YAAE,IAAIT,CAAC,GAAG,IAAIyhB,KAAK,CAAC,gCAAgC,CAAC;YAAEzhB,CAAC,CAAC0hB,IAAI,GAAG,kBAAkB;YAAE,MAAM1hB,CAAC;UAAE;UAExJH,MAAM,CAACD,OAAO,GAAGa,+BAA+B;;UAEhD;QAAM;;QAEN;MAAU,CAAE;MACZ;MACA,SAAU;MACV;MAAU,IAAIkhB,wBAAwB,GAAG,CAAC,CAAC;MAC3C;MACA,SAAU;MACV;MAAU,SAAS7e,mBAAmBA,CAAC8e,QAAQ,EAAE;QACjD,SAAW;QACX,QAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAQ,CAAC;QAChE;QAAW,IAAIC,YAAY,KAAKC,SAAS,EAAE;UAC3C,QAAY,OAAOD,YAAY,CAACjiB,OAAO;UACvC;QAAW;QACX,SAAW;QACX;QAAW,IAAIC,MAAM,GAAG8hB,wBAAwB,CAACC,QAAQ,CAAC,GAAG;UAC7D,SAAY;UACZ,SAAY;UACZ,QAAYhiB,OAAO,EAAE,CAAC;UACtB;QAAW,CAAC;QACZ;QACA,SAAW;QACX;QAAWc,mBAAmB,CAACkhB,QAAQ,CAAC,CAACrgB,IAAI,CAAC1B,MAAM,CAACD,OAAO,EAAEC,MAAM,EAAEA,MAAM,CAACD,OAAO,EAAEkD,mBAAmB,CAAC;QAC1G;QACA,SAAW;QACX;QAAW,OAAOjD,MAAM,CAACD,OAAO;QAChC;MAAU;MACV;MACA;MACA,IAAImiB,mBAAmB,GAAG,CAAC,CAAC;MAC5B;MACA,CAAC,YAAW;QACZ,IAAIniB,OAAO,GAAGmiB,mBAAmB;QAEjC/gB,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,YAAY,EAAG;UAAEkC,KAAK,EAAE;QAAK,CAAE,CAAC;QAC/DlC,OAAO,CAAC4W,IAAI,GAAG5W,OAAO,CAAC2W,MAAM,GAAG3W,OAAO,CAAC6W,GAAG,GAAG7W,OAAO,CAAC8W,KAAK,GAAG9W,OAAO,CAACmC,YAAY,GAAGnC,OAAO,CAACoiB,WAAW,GAAGpiB,OAAO,CAACqiB,aAAa,GAAGriB,OAAO,CAACsiB,WAAW,GAAG,KAAK,CAAC;QAC/J,IAAIC,aAAa,GAAGrf,mBAAmB,CAAC,GAAG,CAAC;QAC5C,IAAII,aAAa,GAAGJ,mBAAmB,CAAC,GAAG,CAAC;QAC5C,IAAIK,aAAa,GAAGL,mBAAmB,CAAC,EAAE,CAAC;QAC3C,IAAIM,iBAAiB,GAAGN,mBAAmB,CAAC,GAAG,CAAC;QAChD,IAAIuW,QAAQ,GAAGvW,mBAAmB,CAAC,GAAG,CAAC;QACvC9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,OAAO,EAAG;UAAEwiB,UAAU,EAAE,IAAI;UAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;YAAE,OAAOhJ,QAAQ,CAAC3C,KAAK;UAAE;QAAE,CAAE,CAAC;QAC5G,IAAIC,UAAU,GAAG7T,mBAAmB,CAAC,GAAG,CAAC;QACzC9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,cAAc,EAAG;UAAEwiB,UAAU,EAAE,IAAI;UAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;YAAE,OAAO1L,UAAU,CAAC5U,YAAY;UAAE;QAAE,CAAE,CAAC;QAC5H,IAAIugB,QAAQ,GAAGxf,mBAAmB,CAAC,GAAG,CAAC;QACvC9B,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,MAAM,EAAG;UAAEwiB,UAAU,EAAE,IAAI;UAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;YAAE,OAAOC,QAAQ,CAAC9L,IAAI;UAAE;QAAE,CAAE,CAAC;QAC1GxV,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,QAAQ,EAAG;UAAEwiB,UAAU,EAAE,IAAI;UAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;YAAE,OAAOC,QAAQ,CAAC/L,MAAM;UAAE;QAAE,CAAE,CAAC;QAC9GvV,MAAM,CAACa,cAAc,CAACjC,OAAO,EAAE,KAAK,EAAG;UAAEwiB,UAAU,EAAE,IAAI;UAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;YAAE,OAAOC,QAAQ,CAAC7L,GAAG;UAAE;QAAE,CAAE,CAAC;QACxG;QACA;QACA,SAASyL,WAAWA,CAAC5e,KAAK,EAAE;UACxB,CAAC,CAAC,EAAE6e,aAAa,CAACrd,OAAO,EAAExB,KAAK,CAAC;QACrC;QACA1D,OAAO,CAACsiB,WAAW,GAAGA,WAAW;QACjC,SAAS1e,SAASA,CAAC1C,CAAC,EAAE+C,OAAO,EAAE;UAC3B,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAEhB,aAAa,CAACiB,UAAU,EAAErD,CAAC,EAAE+C,OAAO,CAAC;UACrD,IAAI3B,KAAK,GAAG,CAAC,CAAC,EAAEkB,iBAAiB,CAACgB,WAAW,EAAEtD,CAAC,EAAEoD,KAAK,CAAC;UACxD,CAAC,CAAC,EAAEf,aAAa,CAACkB,SAAS,EAAEvD,CAAC,EAAEoB,KAAK,CAAC;QAC1C;QACA;QACA,SAAS+f,aAAaA,CAACnhB,CAAC,EAAE+C,OAAO,EAAE;UAC/B,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAEhB,aAAa,CAACiB,UAAU,EAAErD,CAAC,EAAE+C,OAAO,CAAC;UACrD,OAAO,CAAC,CAAC,EAAET,iBAAiB,CAACgB,WAAW,EAAEtD,CAAC,EAAEoD,KAAK,CAAC;QACvD;QACAtE,OAAO,CAACqiB,aAAa,GAAGA,aAAa;QACrC,SAASD,WAAWA,CAAClhB,CAAC,EAAEoB,KAAK,EAAE;UAC3B,CAAC,CAAC,EAAEiB,aAAa,CAACkB,SAAS,EAAEvD,CAAC,EAAEoB,KAAK,CAAC;QAC1C;QACAtC,OAAO,CAACoiB,WAAW,GAAGA,WAAW;QACjC,IAAI;UACA;UACA,IAAI1e,KAAK,GAAGR,mBAAmB,CAAC,EAAE,CAAC;UACnC;UACA;UACA,IAAIQ,KAAK,CAACA,KAAK,EACXA,KAAK,GAAGA,KAAK,CAACA,KAAK;UACvB4e,WAAW,CAAC5e,KAAK,CAAC;QACtB,CAAC,CACD,OAAOS,KAAK,EAAE;UACV;UACA;UACA;QAAA;QAEJnE,OAAO,CAAC,SAAS,CAAC,GAAG4D,SAAS;MAE9B,CAAC,CAAC,CAAC;MACH;MAAU,OAAOue,mBAAmB;MACpC;IAAS,CAAC,CAAE;EAAC;AAEb,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/commons.service\";\nimport * as i4 from \"@angular/common/http\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"angular-datatables\";\nexport class AvailablePlayerModalComponent {\n  constructor(modalService, _translateService, _commonsService, _http, renderer, _teamService, loadingService, toastService) {\n    this.modalService = modalService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this.loadingService = loadingService;\n    this.toastService = toastService;\n    this.dtOptions1 = {};\n    this.dtTrigger = {};\n    this.dtElement = DataTableDirective;\n    this.totalCount = 0;\n    this.successCount = 0;\n    this.failedCount = 0;\n  }\n  ngOnInit() {\n    let params = this.params;\n    const teamManagement = localStorage.getItem('teamManagement');\n    const season_id = teamManagement ? JSON.parse(teamManagement).seasonSelected : null;\n    this.seasonId = season_id;\n    console.log('seasonId', this.seasonId);\n    console.log(params);\n    let current_season_player_url = `${environment.apiUrl}/registrations/club-group-approved`;\n    this.dtOptions1 = this.buildDtOptions1(current_season_player_url, params);\n  }\n  buildDtOptions1(url, params) {\n    return {\n      dom: this._commonsService.dataTableDefaults.dom,\n      ajax: (dataTablesParameters, callback) => {\n        if (params) {\n          dataTablesParameters['team_id'] = parseInt(params.team_id);\n          dataTablesParameters['season_id'] = this.seasonId;\n          dataTablesParameters['club_id'] = parseInt(params.club_id);\n          dataTablesParameters['group_id'] = parseInt(params.group_id);\n        }\n        this._http.post(`${url}`, dataTablesParameters).subscribe(resp => {\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      select: {\n        toggleable: false\n      },\n      // serverSide: true,\n      rowId: 'id',\n      // fake data\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: 1\n      }, {\n        responsivePriority: 1,\n        targets: -1\n      }, {\n        responsivePriority: 2,\n        targets: 1\n      }],\n      columns: [{\n        // photo\n        data: 'photo',\n        className: 'text-center',\n        render: (data, type, row) => {\n          if (data) {\n            return `<img src=\"${data}\" width=\"50px\" height=\"70px\" />`;\n          } else {\n            return `<img src=\"assets/images/avatars/default.png\" width=\"50px\" height=\"70px\" />`;\n          }\n        }\n      }, {\n        // name\n        data: 'user.first_name',\n        className: 'font-weight-bolder',\n        render: (data, type, row) => {\n          const name_settings = JSON.parse(localStorage.getItem('name_settings'));\n          if (row.user.first_name && row.user.last_name) {\n            if (name_settings && name_settings.is_on == 1) {\n              return row.user.first_name + ' ' + row.user.last_name;\n            } else {\n              return row.user.last_name + ' ' + row.user.first_name;\n            }\n          } else {\n            return '';\n          }\n        }\n      }, {\n        //year\n        data: 'dob',\n        render: (data, type, row) => {\n          return new Date(data).getFullYear();\n        }\n      }, {\n        //gender\n        data: 'gender',\n        render: (data, type, row) => {\n          return this._translateService.instant(data);\n        }\n      }, {\n        data: null,\n        render: (data, type, row) => {\n          return `<button class=\"btn btn-primary btn-sm\" \n            data-user-id = '${JSON.stringify(row.user_id)}'\n            data-row-value = '${JSON.stringify(row)}'\n            action=\"assign\">${this._translateService.instant('Assign')}</button>`;\n        }\n      }],\n      buttons: [{\n        text: '<i class=\"fa-duotone fa-check-double\"></i> ' + this._translateService.instant('Assign All'),\n        className: 'btn btn-primary mb-1',\n        action: (e, dt, node, config) => {\n          // if no data\n          if (dt.rows().data().length == 0) {\n            // disable  this button\n            return;\n          }\n          // check if players assigned to this team\n          let rows = dt.rows().data();\n          Swal.fire({\n            title: this._translateService.instant('Are you sure?'),\n            imageUrl: 'assets/images/alerts/Frame1.svg',\n            text: this._translateService.instant('You want to assign all players to this team?'),\n            showCancelButton: true,\n            confirmButtonText: this._translateService.instant('Yes'),\n            confirmButtonColor: '#3085d6',\n            cancelButtonText: '<span class=\"text-primary\">' + this._translateService.instant('Cancel') + '</span>',\n            cancelButtonColor: '#d33',\n            reverseButtons: true,\n            buttonsStyling: false,\n            customClass: {\n              confirmButton: 'btn btn-primary mr-1',\n              cancelButton: 'btn btn-outline-primary mr-1'\n            }\n            // on confirm\n          }).then(result => {\n            if (result.isConfirmed) {\n              this.loadingService.dismiss();\n              this.successCount = 0;\n              this.failedCount = 0;\n              this.totalCount = rows.length;\n              console.log(rows);\n              const mapPromise = Array.from(rows).map(row => this.editor('auto-assign', row));\n              Promise.all(mapPromise).finally(() => {\n                Swal.fire({\n                  title: 'Assignment Complete',\n                  html: `<span class=\"text-success\">${this.successCount} player(s) assigned to this team.</span>\n                <br>\n                <span class=\"text-danger\">${this.failedCount} player(s) failed to be assigned.</span>`,\n                  icon: 'success',\n                  confirmButtonText: 'OK'\n                });\n              });\n            }\n          });\n        }\n      }]\n    };\n  }\n  ngAfterViewInit() {\n    this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('data-row-value')) {\n        let row = event.target.getAttribute('data-row-value');\n        row = JSON.parse(row);\n        console.log(row);\n        this.editor(event.target.getAttribute('action'), row);\n      }\n    });\n  }\n  editor(action, row) {\n    console.log(row);\n    const team_id = this.params.team_id;\n    const player_id = row.id;\n    const registration_id = row.registration_id;\n    const group_id = this.params.group_id;\n    const teamManagement = localStorage.getItem('teamManagement');\n    const season_id = teamManagement ? JSON.parse(teamManagement).seasonSelected : null;\n    switch (action) {\n      case 'assign':\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          imageUrl: 'assets/images/alerts/Frame1.svg',\n          text: this._translateService.instant('You want to assign this player to this team?'),\n          reverseButtons: true,\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          confirmButtonColor: '#3085d6',\n          cancelButtonText: '<span class=\"text-primary\">' + this._translateService.instant('Cancel') + '</span>',\n          cancelButtonColor: '#d33',\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary mr-1',\n            cancelButton: 'btn btn-outline-primary mr-1'\n          }\n        }).then(result => {\n          if (result.isConfirmed) {\n            let params = new FormData();\n            params.append('action', 'create');\n            params.append('data[0][registration_id]', registration_id);\n            params.append('data[0][group_id]', group_id);\n            params.append('data[0][team_id]', team_id);\n            params.append('data[0][player_id]', player_id);\n            params.append('data[0][season_id]', season_id);\n            this._teamService.editorTableTeamPlayers(params).subscribe(resp => {\n              if (resp) {\n                this.toastService.success(this._translateService.instant('Player assigned to this team successfully'));\n                // disable button\n                let button = document.querySelector(`button[action=\"assign\"][data-row-value='${JSON.stringify(row)}']`);\n                button.setAttribute('disabled', 'disabled');\n                button.classList.add('disabled');\n                // change text\n                button.innerHTML = this._translateService.instant('Assigned');\n              }\n            }, err => {\n              this.toastService.error(err.message);\n            });\n          }\n        });\n        break;\n      case 'auto-assign':\n        return new Promise((resolve, reject) => {\n          let params = new FormData();\n          params.append('action', 'create');\n          params.append('data[0][team_id]', team_id);\n          params.append('data[0][player_id]', player_id);\n          params.append('data[0][season_id]', season_id);\n          params.append('data[0][group_id]', group_id);\n          const registration_id = row.registration_id;\n          this._teamService.editorTableTeamPlayers(params).subscribe(resp => {\n            if (resp) {\n              // Disable button\n              let button = document.querySelector(`button[action=\"assign\"][data-user-id='${JSON.stringify(row.user_id)}']`);\n              button.setAttribute('disabled', 'disabled');\n              button.classList.add('disabled');\n              // Change text\n              button.innerHTML = this._translateService.instant('Assigned');\n              resolve(this.successCount++);\n            }\n          }, err => {\n            resolve(this.failedCount++);\n          });\n        });\n      default:\n        break;\n    }\n  }\n  onClose() {\n    this.modalService.dismissAll();\n  }\n  static #_ = this.ɵfac = function AvailablePlayerModalComponent_Factory(t) {\n    return new (t || AvailablePlayerModalComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.CommonsService), i0.ɵɵdirectiveInject(i4.HttpClient), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AvailablePlayerModalComponent,\n    selectors: [[\"app-available-player-modal\"]],\n    viewQuery: function AvailablePlayerModalComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      group_id: \"group_id\"\n    },\n    decls: 29,\n    vars: 19,\n    consts: [[1, \"modal-header\"], [1, \"\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [1, \"text-capitalize\"]],\n    template: function AvailablePlayerModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"div\")(3, \"h4\")(4, \"span\", 1);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function AvailablePlayerModalComponent_Template_button_click_7_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(8, \"span\", 3);\n        i0.ɵɵtext(9, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 4)(11, \"table\", 5)(12, \"thead\")(13, \"tr\")(14, \"th\", 6);\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"th\", 6);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"th\", 6);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"th\", 6);\n        i0.ɵɵtext(24);\n        i0.ɵɵpipe(25, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"th\", 6);\n        i0.ɵɵtext(27);\n        i0.ɵɵpipe(28, \"translate\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 7, \"Assign player to team\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 9, \"Photo\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 11, \"Name\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 13, \"Year\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(25, 15, \"Gender\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 17, \"Action\"));\n      }\n    },\n    dependencies: [i8.DataTableDirective, i2.TranslatePipe],\n    styles: [\".swal2-cancel[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  border-color: #f44336 !important;\\n  color: #fff;\\n}\\n\\n.colname[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdGVhbXMvdGVhbS1hc3NpZ25tZW50L2Fzc2lnbi1wbGF5ZXJzL2F2YWlsYWJsZS1wbGF5ZXItbW9kYWwvYXZhaWxhYmxlLXBsYXllci1tb2RhbC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNJLHlCQUFBO0VBQ0EsZ0NBQUE7RUFDQSxXQUFBO0FBQUo7O0FBR0E7RUFDSSxVQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLnN3YWwyLWNhbmNlbHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNDQzMzY7XHJcbiAgICBib3JkZXItY29sb3I6ICNmNDQzMzYgIWltcG9ydGFudDtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG59XHJcblxyXG4uY29sbmFtZXtcclxuICAgIHdpZHRoOiAyMCU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,kBAAkB,QAAQ,oBAAoB;AAIvD,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;AAO9B,OAAM,MAAOC,6BAA6B;EAaxCC,YACSC,YAAsB,EACtBC,iBAAmC,EACnCC,eAA+B,EAC/BC,KAAiB,EACjBC,QAAmB,EACnBC,YAAyB,EACzBC,cAA8B,EAC9BC,YAA2B;IAP3B,KAAAP,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IApBrB,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,SAAS,GAAQ,EAAE;IAGnB,KAAAC,SAAS,GAAQf,kBAAkB;IAEnC,KAAAgB,UAAU,GAAW,CAAC;IACtB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,WAAW,GAAW,CAAC;EAcvB;EAEAC,QAAQA,CAAA;IACN,IAAIC,MAAM,GAAG,IAAI,CAACA,MAAM;IAExB,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,MAAMC,SAAS,GAAGH,cAAc,GAC5BI,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC,CAACM,cAAc,GACzC,IAAI;IACR,IAAI,CAACC,QAAQ,GAAGJ,SAAS;IACzBK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACF,QAAQ,CAAC;IAEtCC,OAAO,CAACC,GAAG,CAACV,MAAM,CAAC;IAEnB,IAAIW,yBAAyB,GAAG,GAAG9B,WAAW,CAAC+B,MAAM,oCAAoC;IAEzF,IAAI,CAACnB,UAAU,GAAG,IAAI,CAACoB,eAAe,CAACF,yBAAyB,EAAEX,MAAM,CAAC;EAC3E;EAEAa,eAAeA,CAACC,GAAG,EAAEd,MAAW;IAC9B,OAAO;MACLe,GAAG,EAAE,IAAI,CAAC5B,eAAe,CAAC6B,iBAAiB,CAACD,GAAG;MAC/CE,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAInB,MAAM,EAAE;UACVkB,oBAAoB,CAAC,SAAS,CAAC,GAAGE,QAAQ,CAACpB,MAAM,CAACqB,OAAO,CAAC;UAC1DH,oBAAoB,CAAC,WAAW,CAAC,GAAG,IAAI,CAACV,QAAQ;UACjDU,oBAAoB,CAAC,SAAS,CAAC,GAAGE,QAAQ,CAACpB,MAAM,CAACsB,OAAO,CAAC;UAC1DJ,oBAAoB,CAAC,UAAU,CAAC,GAAGE,QAAQ,CAACpB,MAAM,CAACuB,QAAQ,CAAC;;QAG9D,IAAI,CAACnC,KAAK,CACPoC,IAAI,CAAM,GAAGV,GAAG,EAAE,EAAEI,oBAAoB,CAAC,CACzCO,SAAS,CAAEC,IAAS,IAAI;UACvBP,QAAQ,CAAC;YACP;YACAQ,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,MAAM,EAAE;QACNC,UAAU,EAAE;OACb;MACD;MACAC,KAAK,EAAE,IAAI;MACX;MACAC,UAAU,EAAE,IAAI;MAEhBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAAChD,eAAe,CAAC6B,iBAAiB,CAACoB,IAAI;MAErDC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,EACrC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,CACtC;MACDC,OAAO,EAAE,CACP;QACE;QACAX,IAAI,EAAE,OAAO;QACbY,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAEA,CAACb,IAAI,EAAEc,IAAI,EAAEC,GAAG,KAAI;UAC1B,IAAIf,IAAI,EAAE;YACR,OAAO,aAAaA,IAAI,iCAAiC;WAC1D,MAAM;YACL,OAAO,4EAA4E;;QAEvF;OACD,EAED;QACE;QACAA,IAAI,EAAE,iBAAiB;QACvBY,SAAS,EAAE,oBAAoB;QAC/BC,MAAM,EAAEA,CAACb,IAAI,EAAEc,IAAI,EAAEC,GAAG,KAAI;UAC1B,MAAMC,aAAa,GAAGxC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;UACvE,IAAIyC,GAAG,CAACE,IAAI,CAACC,UAAU,IAAIH,GAAG,CAACE,IAAI,CAACE,SAAS,EAAE;YAC7C,IAAIH,aAAa,IAAIA,aAAa,CAACI,KAAK,IAAI,CAAC,EAAE;cAC7C,OAAOL,GAAG,CAACE,IAAI,CAACC,UAAU,GAAG,GAAG,GAAGH,GAAG,CAACE,IAAI,CAACE,SAAS;aACtD,MAAM;cACL,OAAOJ,GAAG,CAACE,IAAI,CAACE,SAAS,GAAG,GAAG,GAAGJ,GAAG,CAACE,IAAI,CAACC,UAAU;;WAExD,MAAM;YACL,OAAO,EAAE;;QAEb;OACD,EAED;QACE;QACAlB,IAAI,EAAE,KAAK;QACXa,MAAM,EAAEA,CAACb,IAAI,EAAEc,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,IAAIM,IAAI,CAACrB,IAAI,CAAC,CAACsB,WAAW,EAAE;QACrC;OACD,EACD;QACE;QACAtB,IAAI,EAAE,QAAQ;QACda,MAAM,EAAEA,CAACb,IAAI,EAAEc,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,IAAI,CAAC1D,iBAAiB,CAACkE,OAAO,CAACvB,IAAI,CAAC;QAC7C;OACD,EACD;QACEA,IAAI,EAAE,IAAI;QACVa,MAAM,EAAEA,CAACb,IAAI,EAAEc,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO;8BACWvC,IAAI,CAACgD,SAAS,CAACT,GAAG,CAACU,OAAO,CAAC;gCACzBjD,IAAI,CAACgD,SAAS,CAACT,GAAG,CAAC;8BACrB,IAAI,CAAC1D,iBAAiB,CAACkE,OAAO,CAC9C,QAAQ,CACT,WAAW;QACd;OACD,CACF;MACDG,OAAO,EAAE,CACP;QACEC,IAAI,EACF,6CAA6C,GAC7C,IAAI,CAACtE,iBAAiB,CAACkE,OAAO,CAAC,YAAY,CAAC;QAC9CX,SAAS,EAAE,sBAAsB;QACjCgB,MAAM,EAAEA,CAACC,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;UAC9B;UACA,IAAIF,EAAE,CAACG,IAAI,EAAE,CAACjC,IAAI,EAAE,CAACkC,MAAM,IAAI,CAAC,EAAE;YAChC;YACA;;UAEF;UACA,IAAID,IAAI,GAAGH,EAAE,CAACG,IAAI,EAAE,CAACjC,IAAI,EAAE;UAE3B/C,IAAI,CAACkF,IAAI,CAAC;YACRC,KAAK,EAAE,IAAI,CAAC/E,iBAAiB,CAACkE,OAAO,CAAC,eAAe,CAAC;YACtDc,QAAQ,EAAE,iCAAiC;YAC3CV,IAAI,EAAE,IAAI,CAACtE,iBAAiB,CAACkE,OAAO,CAClC,8CAA8C,CAC/C;YACDe,gBAAgB,EAAE,IAAI;YACtBC,iBAAiB,EAAE,IAAI,CAAClF,iBAAiB,CAACkE,OAAO,CAAC,KAAK,CAAC;YACxDiB,kBAAkB,EAAE,SAAS;YAC7BC,gBAAgB,EACd,6BAA6B,GAC7B,IAAI,CAACpF,iBAAiB,CAACkE,OAAO,CAAC,QAAQ,CAAC,GACxC,SAAS;YACXmB,iBAAiB,EAAE,MAAM;YACzBC,cAAc,EAAE,IAAI;YACpBC,cAAc,EAAE,KAAK;YACrBC,WAAW,EAAE;cACXC,aAAa,EAAE,sBAAsB;cACrCC,YAAY,EAAE;;YAGhB;WACD,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;YACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;cACtB,IAAI,CAACxF,cAAc,CAACyF,OAAO,EAAE;cAC7B,IAAI,CAACnF,YAAY,GAAG,CAAC;cACrB,IAAI,CAACC,WAAW,GAAG,CAAC;cACpB,IAAI,CAACF,UAAU,GAAGkE,IAAI,CAACC,MAAM;cAE7BtD,OAAO,CAACC,GAAG,CAACoD,IAAI,CAAC;cAEjB,MAAMmB,UAAU,GAAGC,KAAK,CAACC,IAAI,CAACrB,IAAI,CAAC,CAACsB,GAAG,CAAExC,GAAG,IAAK,IAAI,CAACyC,MAAM,CAAC,aAAa,EAAEzC,GAAG,CAAC,CAAC;cAEjF0C,OAAO,CAACC,GAAG,CAACN,UAAU,CAAC,CACpBO,OAAO,CAAC,MAAK;gBACZ1G,IAAI,CAACkF,IAAI,CAAC;kBACRC,KAAK,EAAE,qBAAqB;kBAC5BwB,IAAI,EAAE,8BAA8B,IAAI,CAAC5F,YAAY;;4CAE/B,IAAI,CAACC,WAAW,0CAA0C;kBAChF4F,IAAI,EAAE,SAAS;kBACftB,iBAAiB,EAAE;iBACpB,CAAC;cACJ,CAAC,CAAC;;UAGR,CAAC,CAAC;QACJ;OACD;KAEJ;EACH;EAEAuB,eAAeA,CAAA;IACb,IAAI,CAACtG,QAAQ,CAACuG,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGC,KAAK,IAAI;MAClD,IAAIA,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,gBAAgB,CAAC,EAAE;QAC/C,IAAInD,GAAG,GAAGiD,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,gBAAgB,CAAC;QACrDpD,GAAG,GAAGvC,IAAI,CAACC,KAAK,CAACsC,GAAG,CAAC;QACrBnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAAC;QAEhB,IAAI,CAACyC,MAAM,CAACQ,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,QAAQ,CAAC,EAAEpD,GAAG,CAAC;;IAEzD,CAAC,CAAC;EACJ;EAEAyC,MAAMA,CAAC5B,MAAM,EAAEb,GAAG;IAChBnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAAC;IAEhB,MAAMvB,OAAO,GAAG,IAAI,CAACrB,MAAM,CAACqB,OAAO;IACnC,MAAM4E,SAAS,GAAGrD,GAAG,CAACsD,EAAE;IACxB,MAAMC,eAAe,GAAGvD,GAAG,CAACuD,eAAe;IAC3C,MAAM5E,QAAQ,GAAG,IAAI,CAACvB,MAAM,CAACuB,QAAQ;IAErC,MAAMtB,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,MAAMC,SAAS,GAAGH,cAAc,GAC5BI,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC,CAACM,cAAc,GACzC,IAAI;IAER,QAAQkD,MAAM;MACZ,KAAK,QAAQ;QACX3E,IAAI,CAACkF,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAAC/E,iBAAiB,CAACkE,OAAO,CAAC,eAAe,CAAC;UACtDc,QAAQ,EAAE,iCAAiC;UAC3CV,IAAI,EAAE,IAAI,CAACtE,iBAAiB,CAACkE,OAAO,CAClC,8CAA8C,CAC/C;UACDoB,cAAc,EAAE,IAAI;UACpBL,gBAAgB,EAAE,IAAI;UACtBC,iBAAiB,EAAE,IAAI,CAAClF,iBAAiB,CAACkE,OAAO,CAAC,KAAK,CAAC;UACxDiB,kBAAkB,EAAE,SAAS;UAC7BC,gBAAgB,EACd,6BAA6B,GAC7B,IAAI,CAACpF,iBAAiB,CAACkE,OAAO,CAAC,QAAQ,CAAC,GACxC,SAAS;UACXmB,iBAAiB,EAAE,MAAM;UACzBE,cAAc,EAAE,KAAK;UACrBC,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCC,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UAEjB,IAAIA,MAAM,CAACC,WAAW,EAAE;YACtB,IAAI/E,MAAM,GAAa,IAAIoG,QAAQ,EAAE;YACrCpG,MAAM,CAACqG,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;YACjCrG,MAAM,CAACqG,MAAM,CAAC,0BAA0B,EAAEF,eAAe,CAAC;YAC1DnG,MAAM,CAACqG,MAAM,CAAC,mBAAmB,EAAE9E,QAAQ,CAAC;YAC5CvB,MAAM,CAACqG,MAAM,CAAC,kBAAkB,EAAEhF,OAAO,CAAC;YAC1CrB,MAAM,CAACqG,MAAM,CAAC,oBAAoB,EAAEJ,SAAS,CAAC;YAC9CjG,MAAM,CAACqG,MAAM,CAAC,oBAAoB,EAAEjG,SAAS,CAAC;YAE9C,IAAI,CAACd,YAAY,CAACgH,sBAAsB,CAACtG,MAAM,CAAC,CAACyB,SAAS,CACvDC,IAAS,IAAI;cACZ,IAAIA,IAAI,EAAE;gBACR,IAAI,CAAClC,YAAY,CAAC+G,OAAO,CACvB,IAAI,CAACrH,iBAAiB,CAACkE,OAAO,CAC5B,2CAA2C,CAC5C,CACF;gBAED;gBACA,IAAIoD,MAAM,GAAGC,QAAQ,CAACC,aAAa,CACjC,2CAA2CrG,IAAI,CAACgD,SAAS,CACvDT,GAAG,CACJ,IAAI,CACN;gBACD4D,MAAM,CAACG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;gBAC3CH,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;gBAChC;gBACAL,MAAM,CAACM,SAAS,GAAG,IAAI,CAAC5H,iBAAiB,CAACkE,OAAO,CAAC,UAAU,CAAC;;YAEjE,CAAC,EACA2D,GAAG,IAAI;cACN,IAAI,CAACvH,YAAY,CAACwH,KAAK,CAACD,GAAG,CAACE,OAAO,CAAC;YACtC,CAAC,CACF;;QAEL,CAAC,CAAC;QACF;MACF,KAAK,aAAa;QAChB,OAAO,IAAI3B,OAAO,CAAC,CAAC4B,OAAO,EAAEC,MAAM,KAAI;UAErC,IAAInH,MAAM,GAAa,IAAIoG,QAAQ,EAAE;UACrCpG,MAAM,CAACqG,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;UACjCrG,MAAM,CAACqG,MAAM,CAAC,kBAAkB,EAAEhF,OAAO,CAAC;UAC1CrB,MAAM,CAACqG,MAAM,CAAC,oBAAoB,EAAEJ,SAAS,CAAC;UAC9CjG,MAAM,CAACqG,MAAM,CAAC,oBAAoB,EAAEjG,SAAS,CAAC;UAC9CJ,MAAM,CAACqG,MAAM,CAAC,mBAAmB,EAAE9E,QAAQ,CAAC;UAC5C,MAAM4E,eAAe,GAAGvD,GAAG,CAACuD,eAAe;UAG3C,IAAI,CAAC7G,YAAY,CAACgH,sBAAsB,CAACtG,MAAM,CAAC,CAACyB,SAAS,CACvDC,IAAS,IAAI;YACZ,IAAIA,IAAI,EAAE;cACR;cACA,IAAI8E,MAAM,GAAGC,QAAQ,CAACC,aAAa,CACjC,yCAAyCrG,IAAI,CAACgD,SAAS,CACrDT,GAAG,CAACU,OAAO,CACZ,IAAI,CACN;cACDkD,MAAM,CAACG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;cAC3CH,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;cAEhC;cACAL,MAAM,CAACM,SAAS,GAAG,IAAI,CAAC5H,iBAAiB,CAACkE,OAAO,CAAC,UAAU,CAAC;cAC7D8D,OAAO,CAAC,IAAI,CAACrH,YAAY,EAAE,CAAC;;UAEhC,CAAC,EACAkH,GAAG,IAAI;YACNG,OAAO,CAAC,IAAI,CAACpH,WAAW,EAAE,CAAC;UAC7B,CAAC,CACF;QACH,CAAC,CAAC;MAEJ;QACE;;EAEN;EAEAsH,OAAOA,CAAA;IACL,IAAI,CAACnI,YAAY,CAACoI,UAAU,EAAE;EAChC;EAAC,QAAAC,CAAA;qBA9UUvI,6BAA6B,EAAAwI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,UAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAd,EAAA,CAAAC,iBAAA,CAAAc,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAA7BzJ,6BAA6B;IAAA0J,SAAA;IAAAC,SAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAI7BhK,kBAAkB;;;;;;;;;;;;;;;QCrB/B2I,EAAA,CAAAuB,cAAA,UAAK;QAW0BvB,EAAA,CAAAwB,MAAA,GAAuC;;QAAAxB,EAAA,CAAAyB,YAAA,EAAO;QAIrEzB,EAAA,CAAAuB,cAAA,gBACwB;QAApBvB,EAAA,CAAA0B,UAAA,mBAAAC,+DAAA;UAAA,OAASL,GAAA,CAAAzB,OAAA,EAAS;QAAA,EAAC;QACnBG,EAAA,CAAAuB,cAAA,cAAyB;QAAAvB,EAAA,CAAAwB,MAAA,aAAO;QAAAxB,EAAA,CAAAyB,YAAA,EAAO;QAI/CzB,EAAA,CAAAuB,cAAA,cAAkD;QAINvB,EAAA,CAAAwB,MAAA,IAAuB;;QAAAxB,EAAA,CAAAyB,YAAA,EAAK;QACxDzB,EAAA,CAAAuB,cAAA,aAA4B;QAAAvB,EAAA,CAAAwB,MAAA,IAAsB;;QAAAxB,EAAA,CAAAyB,YAAA,EAAK;QACvDzB,EAAA,CAAAuB,cAAA,aAA4B;QAAAvB,EAAA,CAAAwB,MAAA,IAAsB;;QAAAxB,EAAA,CAAAyB,YAAA,EAAK;QACvDzB,EAAA,CAAAuB,cAAA,aAA4B;QAAAvB,EAAA,CAAAwB,MAAA,IAAwB;;QAAAxB,EAAA,CAAAyB,YAAA,EAAK;QACzDzB,EAAA,CAAAuB,cAAA,aAA4B;QAAAvB,EAAA,CAAAwB,MAAA,IAAwB;;QAAAxB,EAAA,CAAAyB,YAAA,EAAK;;;QAlB9CzB,EAAA,CAAA4B,SAAA,GAAuC;QAAvC5B,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,gCAAuC;QAW7C9B,EAAA,CAAA4B,SAAA,GAAwB;QAAxB5B,EAAA,CAAA+B,UAAA,cAAAT,GAAA,CAAApJ,UAAA,CAAwB;QAGD8H,EAAA,CAAA4B,SAAA,GAAuB;QAAvB5B,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,iBAAuB;QACvB9B,EAAA,CAAA4B,SAAA,GAAsB;QAAtB5B,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,iBAAsB;QACtB9B,EAAA,CAAA4B,SAAA,GAAsB;QAAtB5B,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,iBAAsB;QACtB9B,EAAA,CAAA4B,SAAA,GAAwB;QAAxB5B,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,mBAAwB;QACxB9B,EAAA,CAAA4B,SAAA,GAAwB;QAAxB5B,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,mBAAwB", "names": ["DataTableDirective", "environment", "<PERSON><PERSON>", "AvailablePlayerModalComponent", "constructor", "modalService", "_translateService", "_commonsService", "_http", "renderer", "_teamService", "loadingService", "toastService", "dtOptions1", "dtTrigger", "dtElement", "totalCount", "successCount", "failedCount", "ngOnInit", "params", "teamManagement", "localStorage", "getItem", "season_id", "JSON", "parse", "seasonSelected", "seasonId", "console", "log", "current_season_player_url", "apiUrl", "buildDtOptions1", "url", "dom", "dataTableDefaults", "ajax", "dataTablesParameters", "callback", "parseInt", "team_id", "club_id", "group_id", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "data", "select", "toggleable", "rowId", "responsive", "scrollX", "language", "lang", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "type", "row", "name_settings", "user", "first_name", "last_name", "is_on", "Date", "getFullYear", "instant", "stringify", "user_id", "buttons", "text", "action", "e", "dt", "node", "config", "rows", "length", "fire", "title", "imageUrl", "showCancelButton", "confirmButtonText", "confirmButtonColor", "cancelButtonText", "cancelButtonColor", "reverseButtons", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "then", "result", "isConfirmed", "dismiss", "mapPromise", "Array", "from", "map", "editor", "Promise", "all", "finally", "html", "icon", "ngAfterViewInit", "listen", "event", "target", "hasAttribute", "getAttribute", "player_id", "id", "registration_id", "FormData", "append", "editorTableTeamPlayers", "success", "button", "document", "querySelector", "setAttribute", "classList", "add", "innerHTML", "err", "error", "message", "resolve", "reject", "onClose", "dismissAll", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbModal", "i2", "TranslateService", "i3", "CommonsService", "i4", "HttpClient", "Renderer2", "i5", "TeamService", "i6", "LoadingService", "i7", "ToastrService", "_2", "selectors", "viewQuery", "AvailablePlayerModalComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AvailablePlayerModalComponent_Template_button_click_7_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-players\\available-player-modal\\available-player-modal.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-players\\available-player-modal\\available-player-modal.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, Input, OnInit, Renderer2, ViewChild } from '@angular/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { environment } from 'environments/environment';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-available-player-modal',\r\n  templateUrl: './available-player-modal.component.html',\r\n  styleUrls: ['./available-player-modal.component.scss']\r\n})\r\nexport class AvailablePlayerModalComponent implements OnInit {\r\n  dtOptions1: any = {};\r\n  dtTrigger: any = {};\r\n  public params: any;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  seasonId: any;\r\n  totalCount: number = 0;\r\n  successCount: number = 0;\r\n  failedCount: number = 0;\r\n\r\n  @Input() group_id: any;\r\n\r\n  constructor(\r\n    public modalService: NgbModal,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public loadingService: LoadingService,\r\n    public toastService: ToastrService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    let params = this.params;\r\n\r\n    const teamManagement = localStorage.getItem('teamManagement');\r\n    const season_id = teamManagement\r\n      ? JSON.parse(teamManagement).seasonSelected\r\n      : null;\r\n    this.seasonId = season_id;\r\n    console.log('seasonId', this.seasonId);\r\n\r\n    console.log(params);\r\n\r\n    let current_season_player_url = `${environment.apiUrl}/registrations/club-group-approved`;\r\n\r\n    this.dtOptions1 = this.buildDtOptions1(current_season_player_url, params);\r\n  }\r\n\r\n  buildDtOptions1(url, params: any) {\r\n    return {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        if (params) {\r\n          dataTablesParameters['team_id'] = parseInt(params.team_id);\r\n          dataTablesParameters['season_id'] = this.seasonId;\r\n          dataTablesParameters['club_id'] = parseInt(params.club_id);\r\n          dataTablesParameters['group_id'] = parseInt(params.group_id);\r\n        }\r\n\r\n        this._http\r\n          .post<any>(`${url}`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      select: {\r\n        toggleable: false\r\n      },\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      // fake data\r\n      responsive: true,\r\n\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: 1 },\r\n        { responsivePriority: 1, targets: -1 },\r\n        { responsivePriority: 2, targets: 1 }\r\n      ],\r\n      columns: [\r\n        {\r\n          // photo\r\n          data: 'photo',\r\n          className: 'text-center',\r\n          render: (data, type, row) => {\r\n            if (data) {\r\n              return `<img src=\"${data}\" width=\"50px\" height=\"70px\" />`;\r\n            } else {\r\n              return `<img src=\"assets/images/avatars/default.png\" width=\"50px\" height=\"70px\" />`;\r\n            }\r\n          }\r\n        },\r\n\r\n        {\r\n          // name\r\n          data: 'user.first_name',\r\n          className: 'font-weight-bolder',\r\n          render: (data, type, row) => {\r\n            const name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n            if (row.user.first_name && row.user.last_name) {\r\n              if (name_settings && name_settings.is_on == 1) {\r\n                return row.user.first_name + ' ' + row.user.last_name;\r\n              } else {\r\n                return row.user.last_name + ' ' + row.user.first_name;\r\n              }\r\n            } else {\r\n              return '';\r\n            }\r\n          }\r\n        },\r\n\r\n        {\r\n          //year\r\n          data: 'dob',\r\n          render: (data, type, row) => {\r\n            return new Date(data).getFullYear();\r\n          }\r\n        },\r\n        {\r\n          //gender\r\n          data: 'gender',\r\n          render: (data, type, row) => {\r\n            return this._translateService.instant(data);\r\n          }\r\n        },\r\n        {\r\n          data: null,\r\n          render: (data, type, row) => {\r\n            return `<button class=\"btn btn-primary btn-sm\" \r\n            data-user-id = '${JSON.stringify(row.user_id)}'\r\n            data-row-value = '${JSON.stringify(row)}'\r\n            action=\"assign\">${this._translateService.instant(\r\n              'Assign'\r\n            )}</button>`;\r\n          }\r\n        }\r\n      ],\r\n      buttons: [\r\n        {\r\n          text:\r\n            '<i class=\"fa-duotone fa-check-double\"></i> ' +\r\n            this._translateService.instant('Assign All'),\r\n          className: 'btn btn-primary mb-1',\r\n          action: (e, dt, node, config) => {\r\n            // if no data\r\n            if (dt.rows().data().length == 0) {\r\n              // disable  this button\r\n              return;\r\n            }\r\n            // check if players assigned to this team\r\n            let rows = dt.rows().data();\r\n\r\n            Swal.fire({\r\n              title: this._translateService.instant('Are you sure?'),\r\n              imageUrl: 'assets/images/alerts/Frame1.svg',\r\n              text: this._translateService.instant(\r\n                'You want to assign all players to this team?'\r\n              ),\r\n              showCancelButton: true,\r\n              confirmButtonText: this._translateService.instant('Yes'),\r\n              confirmButtonColor: '#3085d6',\r\n              cancelButtonText:\r\n                '<span class=\"text-primary\">' +\r\n                this._translateService.instant('Cancel') +\r\n                '</span>',\r\n              cancelButtonColor: '#d33',\r\n              reverseButtons: true,\r\n              buttonsStyling: false,\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary mr-1',\r\n                cancelButton: 'btn btn-outline-primary mr-1'\r\n              }\r\n\r\n              // on confirm\r\n            }).then((result) => {\r\n              if (result.isConfirmed) {\r\n                this.loadingService.dismiss();\r\n                this.successCount = 0;\r\n                this.failedCount = 0;\r\n                this.totalCount = rows.length;\r\n\r\n                console.log(rows);\r\n\r\n                const mapPromise = Array.from(rows).map((row) => this.editor('auto-assign', row));\r\n\r\n                Promise.all(mapPromise)\r\n                  .finally(() => {\r\n                    Swal.fire({\r\n                      title: 'Assignment Complete',\r\n                      html: `<span class=\"text-success\">${this.successCount} player(s) assigned to this team.</span>\r\n                <br>\r\n                <span class=\"text-danger\">${this.failedCount} player(s) failed to be assigned.</span>`,\r\n                      icon: 'success',\r\n                      confirmButtonText: 'OK'\r\n                    });\r\n                  });\r\n\r\n              }\r\n            });\r\n          }\r\n        }\r\n      ]\r\n    };\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('data-row-value')) {\r\n        let row = event.target.getAttribute('data-row-value');\r\n        row = JSON.parse(row);\r\n        console.log(row);\r\n\r\n        this.editor(event.target.getAttribute('action'), row);\r\n      }\r\n    });\r\n  }\r\n\r\n  editor(action, row) {\r\n    console.log(row);\r\n\r\n    const team_id = this.params.team_id;\r\n    const player_id = row.id;\r\n    const registration_id = row.registration_id;\r\n    const group_id = this.params.group_id;\r\n\r\n    const teamManagement = localStorage.getItem('teamManagement');\r\n    const season_id = teamManagement\r\n      ? JSON.parse(teamManagement).seasonSelected\r\n      : null;\r\n\r\n    switch (action) {\r\n      case 'assign':\r\n        Swal.fire({\r\n          title: this._translateService.instant('Are you sure?'),\r\n          imageUrl: 'assets/images/alerts/Frame1.svg',\r\n          text: this._translateService.instant(\r\n            'You want to assign this player to this team?'\r\n          ),\r\n          reverseButtons: true,\r\n          showCancelButton: true,\r\n          confirmButtonText: this._translateService.instant('Yes'),\r\n          confirmButtonColor: '#3085d6',\r\n          cancelButtonText:\r\n            '<span class=\"text-primary\">' +\r\n            this._translateService.instant('Cancel') +\r\n            '</span>',\r\n          cancelButtonColor: '#d33',\r\n          buttonsStyling: false,\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary mr-1',\r\n            cancelButton: 'btn btn-outline-primary mr-1'\r\n          }\r\n        }).then((result) => {\r\n\r\n          if (result.isConfirmed) {\r\n            let params: FormData = new FormData();\r\n            params.append('action', 'create');\r\n            params.append('data[0][registration_id]', registration_id);\r\n            params.append('data[0][group_id]', group_id);\r\n            params.append('data[0][team_id]', team_id);\r\n            params.append('data[0][player_id]', player_id);\r\n            params.append('data[0][season_id]', season_id);\r\n\r\n            this._teamService.editorTableTeamPlayers(params).subscribe(\r\n              (resp: any) => {\r\n                if (resp) {\r\n                  this.toastService.success(\r\n                    this._translateService.instant(\r\n                      'Player assigned to this team successfully'\r\n                    )\r\n                  );\r\n\r\n                  // disable button\r\n                  let button = document.querySelector(\r\n                    `button[action=\"assign\"][data-row-value='${JSON.stringify(\r\n                      row\r\n                    )}']`\r\n                  );\r\n                  button.setAttribute('disabled', 'disabled');\r\n                  button.classList.add('disabled');\r\n                  // change text\r\n                  button.innerHTML = this._translateService.instant('Assigned');\r\n                }\r\n              },\r\n              (err) => {\r\n                this.toastService.error(err.message);\r\n              }\r\n            );\r\n          }\r\n        });\r\n        break;\r\n      case 'auto-assign':\r\n        return new Promise((resolve, reject) => {\r\n\r\n          let params: FormData = new FormData();\r\n          params.append('action', 'create');\r\n          params.append('data[0][team_id]', team_id);\r\n          params.append('data[0][player_id]', player_id);\r\n          params.append('data[0][season_id]', season_id);\r\n          params.append('data[0][group_id]', group_id);\r\n          const registration_id = row.registration_id;\r\n\r\n\r\n          this._teamService.editorTableTeamPlayers(params).subscribe(\r\n            (resp: any) => {\r\n              if (resp) {\r\n                // Disable button\r\n                let button = document.querySelector(\r\n                  `button[action=\"assign\"][data-user-id='${JSON.stringify(\r\n                    row.user_id\r\n                  )}']`\r\n                );\r\n                button.setAttribute('disabled', 'disabled');\r\n                button.classList.add('disabled');\r\n\r\n                // Change text\r\n                button.innerHTML = this._translateService.instant('Assigned');\r\n                resolve(this.successCount++);\r\n              }\r\n            },\r\n            (err) => {\r\n              resolve(this.failedCount++);\r\n            }\r\n          );\r\n        });\r\n\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    this.modalService.dismissAll();\r\n  }\r\n}\r\n", "<div>\r\n\r\n    <div class=\"modal-header\">\r\n        <!-- text center title and subtitle -->\r\n        <!-- title is 'Team List 'H2 -->\r\n        <!-- subtitle is 'Assign player to team' h5-->\r\n        <div>\r\n            <!-- <h2>\r\n                <span class=\"text-capitalize\">{{'Player List' | translate}}</span>\r\n            </h2> -->\r\n            <h4>\r\n                <span class=\"\">{{'Assign player to team' | translate}}</span>\r\n            </h4>\r\n        </div>\r\n        \r\n        <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"\r\n            (click)=\"onClose()\">\r\n            <span aria-hidden=\"true\">&times;</span>\r\n        </button>\r\n\r\n    </div>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n        <table datatable [dtOptions]=\"dtOptions1\" class=\"table border row-border hover\">\r\n            <thead>\r\n                <tr>\r\n                    <th class=\"text-capitalize\">{{'Photo' | translate}}</th>\r\n                    <th class=\"text-capitalize\">{{'Name' | translate}}</th>\r\n                    <th class=\"text-capitalize\">{{'Year' | translate}}</th>\r\n                    <th class=\"text-capitalize\">{{'Gender' | translate}}</th>    \r\n                    <th class=\"text-capitalize\">{{'Action' | translate}}</th>    \r\n                </tr>\r\n            </thead>\r\n        </table>\r\n    </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
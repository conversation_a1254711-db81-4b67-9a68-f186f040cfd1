{"ast": null, "code": "import { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nexport function mergeScan(accumulator, seed, concurrent = Number.POSITIVE_INFINITY) {\n  return source => source.lift(new MergeScanOperator(accumulator, seed, concurrent));\n}\nexport class MergeScanOperator {\n  constructor(accumulator, seed, concurrent) {\n    this.accumulator = accumulator;\n    this.seed = seed;\n    this.concurrent = concurrent;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new MergeScanSubscriber(subscriber, this.accumulator, this.seed, this.concurrent));\n  }\n}\nexport class MergeScanSubscriber extends OuterSubscriber {\n  constructor(destination, accumulator, acc, concurrent) {\n    super(destination);\n    this.accumulator = accumulator;\n    this.acc = acc;\n    this.concurrent = concurrent;\n    this.hasValue = false;\n    this.hasCompleted = false;\n    this.buffer = [];\n    this.active = 0;\n    this.index = 0;\n  }\n  _next(value) {\n    if (this.active < this.concurrent) {\n      const index = this.index++;\n      const destination = this.destination;\n      let ish;\n      try {\n        const {\n          accumulator\n        } = this;\n        ish = accumulator(this.acc, value, index);\n      } catch (e) {\n        return destination.error(e);\n      }\n      this.active++;\n      this._innerSub(ish, value, index);\n    } else {\n      this.buffer.push(value);\n    }\n  }\n  _innerSub(ish, value, index) {\n    const innerSubscriber = new InnerSubscriber(this, value, index);\n    const destination = this.destination;\n    destination.add(innerSubscriber);\n    const innerSubscription = subscribeToResult(this, ish, undefined, undefined, innerSubscriber);\n    if (innerSubscription !== innerSubscriber) {\n      destination.add(innerSubscription);\n    }\n  }\n  _complete() {\n    this.hasCompleted = true;\n    if (this.active === 0 && this.buffer.length === 0) {\n      if (this.hasValue === false) {\n        this.destination.next(this.acc);\n      }\n      this.destination.complete();\n    }\n    this.unsubscribe();\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    const {\n      destination\n    } = this;\n    this.acc = innerValue;\n    this.hasValue = true;\n    destination.next(innerValue);\n  }\n  notifyComplete(innerSub) {\n    const buffer = this.buffer;\n    const destination = this.destination;\n    destination.remove(innerSub);\n    this.active--;\n    if (buffer.length > 0) {\n      this._next(buffer.shift());\n    } else if (this.active === 0 && this.hasCompleted) {\n      if (this.hasValue === false) {\n        this.destination.next(this.acc);\n      }\n      this.destination.complete();\n    }\n  }\n}", "map": {"version": 3, "names": ["subscribeToResult", "OuterSubscriber", "InnerSubscriber", "mergeScan", "accumulator", "seed", "concurrent", "Number", "POSITIVE_INFINITY", "source", "lift", "MergeScanOperator", "constructor", "call", "subscriber", "subscribe", "MergeScanSubscriber", "destination", "acc", "hasValue", "hasCompleted", "buffer", "active", "index", "_next", "value", "ish", "e", "error", "_innerSub", "push", "innerSubscriber", "add", "innerSubscription", "undefined", "_complete", "length", "next", "complete", "unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "notifyComplete", "remove", "shift"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/mergeScan.js"], "sourcesContent": ["import { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nexport function mergeScan(accumulator, seed, concurrent = Number.POSITIVE_INFINITY) {\n    return (source) => source.lift(new MergeScanOperator(accumulator, seed, concurrent));\n}\nexport class MergeScanOperator {\n    constructor(accumulator, seed, concurrent) {\n        this.accumulator = accumulator;\n        this.seed = seed;\n        this.concurrent = concurrent;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new MergeScanSubscriber(subscriber, this.accumulator, this.seed, this.concurrent));\n    }\n}\nexport class MergeScanSubscriber extends OuterSubscriber {\n    constructor(destination, accumulator, acc, concurrent) {\n        super(destination);\n        this.accumulator = accumulator;\n        this.acc = acc;\n        this.concurrent = concurrent;\n        this.hasValue = false;\n        this.hasCompleted = false;\n        this.buffer = [];\n        this.active = 0;\n        this.index = 0;\n    }\n    _next(value) {\n        if (this.active < this.concurrent) {\n            const index = this.index++;\n            const destination = this.destination;\n            let ish;\n            try {\n                const { accumulator } = this;\n                ish = accumulator(this.acc, value, index);\n            }\n            catch (e) {\n                return destination.error(e);\n            }\n            this.active++;\n            this._innerSub(ish, value, index);\n        }\n        else {\n            this.buffer.push(value);\n        }\n    }\n    _innerSub(ish, value, index) {\n        const innerSubscriber = new InnerSubscriber(this, value, index);\n        const destination = this.destination;\n        destination.add(innerSubscriber);\n        const innerSubscription = subscribeToResult(this, ish, undefined, undefined, innerSubscriber);\n        if (innerSubscription !== innerSubscriber) {\n            destination.add(innerSubscription);\n        }\n    }\n    _complete() {\n        this.hasCompleted = true;\n        if (this.active === 0 && this.buffer.length === 0) {\n            if (this.hasValue === false) {\n                this.destination.next(this.acc);\n            }\n            this.destination.complete();\n        }\n        this.unsubscribe();\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        const { destination } = this;\n        this.acc = innerValue;\n        this.hasValue = true;\n        destination.next(innerValue);\n    }\n    notifyComplete(innerSub) {\n        const buffer = this.buffer;\n        const destination = this.destination;\n        destination.remove(innerSub);\n        this.active--;\n        if (buffer.length > 0) {\n            this._next(buffer.shift());\n        }\n        else if (this.active === 0 && this.hasCompleted) {\n            if (this.hasValue === false) {\n                this.destination.next(this.acc);\n            }\n            this.destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAO,SAASC,SAASA,CAACC,WAAW,EAAEC,IAAI,EAAEC,UAAU,GAAGC,MAAM,CAACC,iBAAiB,EAAE;EAChF,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACP,WAAW,EAAEC,IAAI,EAAEC,UAAU,CAAC,CAAC;AACxF;AACA,OAAO,MAAMK,iBAAiB,CAAC;EAC3BC,WAAWA,CAACR,WAAW,EAAEC,IAAI,EAAEC,UAAU,EAAE;IACvC,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAO,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACV,WAAW,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;EAC9G;AACJ;AACA,OAAO,MAAMU,mBAAmB,SAASf,eAAe,CAAC;EACrDW,WAAWA,CAACK,WAAW,EAAEb,WAAW,EAAEc,GAAG,EAAEZ,UAAU,EAAE;IACnD,KAAK,CAACW,WAAW,CAAC;IAClB,IAAI,CAACb,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACc,GAAG,GAAGA,GAAG;IACd,IAAI,CAACZ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACa,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,IAAI,CAACH,MAAM,GAAG,IAAI,CAAChB,UAAU,EAAE;MAC/B,MAAMiB,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;MAC1B,MAAMN,WAAW,GAAG,IAAI,CAACA,WAAW;MACpC,IAAIS,GAAG;MACP,IAAI;QACA,MAAM;UAAEtB;QAAY,CAAC,GAAG,IAAI;QAC5BsB,GAAG,GAAGtB,WAAW,CAAC,IAAI,CAACc,GAAG,EAAEO,KAAK,EAAEF,KAAK,CAAC;MAC7C,CAAC,CACD,OAAOI,CAAC,EAAE;QACN,OAAOV,WAAW,CAACW,KAAK,CAACD,CAAC,CAAC;MAC/B;MACA,IAAI,CAACL,MAAM,EAAE;MACb,IAAI,CAACO,SAAS,CAACH,GAAG,EAAED,KAAK,EAAEF,KAAK,CAAC;IACrC,CAAC,MACI;MACD,IAAI,CAACF,MAAM,CAACS,IAAI,CAACL,KAAK,CAAC;IAC3B;EACJ;EACAI,SAASA,CAACH,GAAG,EAAED,KAAK,EAAEF,KAAK,EAAE;IACzB,MAAMQ,eAAe,GAAG,IAAI7B,eAAe,CAAC,IAAI,EAAEuB,KAAK,EAAEF,KAAK,CAAC;IAC/D,MAAMN,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACe,GAAG,CAACD,eAAe,CAAC;IAChC,MAAME,iBAAiB,GAAGjC,iBAAiB,CAAC,IAAI,EAAE0B,GAAG,EAAEQ,SAAS,EAAEA,SAAS,EAAEH,eAAe,CAAC;IAC7F,IAAIE,iBAAiB,KAAKF,eAAe,EAAE;MACvCd,WAAW,CAACe,GAAG,CAACC,iBAAiB,CAAC;IACtC;EACJ;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACf,YAAY,GAAG,IAAI;IACxB,IAAI,IAAI,CAACE,MAAM,KAAK,CAAC,IAAI,IAAI,CAACD,MAAM,CAACe,MAAM,KAAK,CAAC,EAAE;MAC/C,IAAI,IAAI,CAACjB,QAAQ,KAAK,KAAK,EAAE;QACzB,IAAI,CAACF,WAAW,CAACoB,IAAI,CAAC,IAAI,CAACnB,GAAG,CAAC;MACnC;MACA,IAAI,CAACD,WAAW,CAACqB,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACAC,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,MAAM;MAAE5B;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACC,GAAG,GAAGwB,UAAU;IACrB,IAAI,CAACvB,QAAQ,GAAG,IAAI;IACpBF,WAAW,CAACoB,IAAI,CAACK,UAAU,CAAC;EAChC;EACAI,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAMxB,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMJ,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAAC8B,MAAM,CAACF,QAAQ,CAAC;IAC5B,IAAI,CAACvB,MAAM,EAAE;IACb,IAAID,MAAM,CAACe,MAAM,GAAG,CAAC,EAAE;MACnB,IAAI,CAACZ,KAAK,CAACH,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC;IAC9B,CAAC,MACI,IAAI,IAAI,CAAC1B,MAAM,KAAK,CAAC,IAAI,IAAI,CAACF,YAAY,EAAE;MAC7C,IAAI,IAAI,CAACD,QAAQ,KAAK,KAAK,EAAE;QACzB,IAAI,CAACF,WAAW,CAACoB,IAAI,CAAC,IAAI,CAACnB,GAAG,CAAC;MACnC;MACA,IAAI,CAACD,WAAW,CAACqB,QAAQ,CAAC,CAAC;IAC/B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
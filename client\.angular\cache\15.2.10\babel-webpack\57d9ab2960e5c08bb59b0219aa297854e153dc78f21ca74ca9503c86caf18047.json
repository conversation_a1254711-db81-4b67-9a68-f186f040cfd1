{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Component, ComponentContainer } from '@firebase/component';\nimport { Lo<PERSON>, setUserLogHandler, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, getDefaultAppConfig, deepEqual, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass PlatformLoggerServiceImpl {\n  constructor(container) {\n    this.container = container;\n  }\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString() {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers.map(provider => {\n      if (isVersionServiceProvider(provider)) {\n        const service = provider.getImmediate();\n        return `${service.library}/${service.version}`;\n      } else {\n        return null;\n      }\n    }).filter(logString => logString).join(' ');\n  }\n}\n/**\r\n *\r\n * @param provider check if this provider provides a VersionService\r\n *\r\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\r\n * provides VersionService. The provider is not necessarily a 'app-version'\r\n * provider.\r\n */\nfunction isVersionServiceProvider(provider) {\n  const component = provider.getComponent();\n  return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\n\nconst name$o = \"@firebase/app\";\nconst version$1 = \"0.9.13\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/app');\nconst name$n = \"@firebase/app-compat\";\nconst name$m = \"@firebase/analytics-compat\";\nconst name$l = \"@firebase/analytics\";\nconst name$k = \"@firebase/app-check-compat\";\nconst name$j = \"@firebase/app-check\";\nconst name$i = \"@firebase/auth\";\nconst name$h = \"@firebase/auth-compat\";\nconst name$g = \"@firebase/database\";\nconst name$f = \"@firebase/database-compat\";\nconst name$e = \"@firebase/functions\";\nconst name$d = \"@firebase/functions-compat\";\nconst name$c = \"@firebase/installations\";\nconst name$b = \"@firebase/installations-compat\";\nconst name$a = \"@firebase/messaging\";\nconst name$9 = \"@firebase/messaging-compat\";\nconst name$8 = \"@firebase/performance\";\nconst name$7 = \"@firebase/performance-compat\";\nconst name$6 = \"@firebase/remote-config\";\nconst name$5 = \"@firebase/remote-config-compat\";\nconst name$4 = \"@firebase/storage\";\nconst name$3 = \"@firebase/storage-compat\";\nconst name$2 = \"@firebase/firestore\";\nconst name$1 = \"@firebase/firestore-compat\";\nconst name = \"firebase\";\nconst version = \"9.23.0\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The default app name\r\n *\r\n * @internal\r\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n  [name$o]: 'fire-core',\n  [name$n]: 'fire-core-compat',\n  [name$l]: 'fire-analytics',\n  [name$m]: 'fire-analytics-compat',\n  [name$j]: 'fire-app-check',\n  [name$k]: 'fire-app-check-compat',\n  [name$i]: 'fire-auth',\n  [name$h]: 'fire-auth-compat',\n  [name$g]: 'fire-rtdb',\n  [name$f]: 'fire-rtdb-compat',\n  [name$e]: 'fire-fn',\n  [name$d]: 'fire-fn-compat',\n  [name$c]: 'fire-iid',\n  [name$b]: 'fire-iid-compat',\n  [name$a]: 'fire-fcm',\n  [name$9]: 'fire-fcm-compat',\n  [name$8]: 'fire-perf',\n  [name$7]: 'fire-perf-compat',\n  [name$6]: 'fire-rc',\n  [name$5]: 'fire-rc-compat',\n  [name$4]: 'fire-gcs',\n  [name$3]: 'fire-gcs-compat',\n  [name$2]: 'fire-fst',\n  [name$1]: 'fire-fst-compat',\n  'fire-js': 'fire-js',\n  [name]: 'fire-js-all'\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * @internal\r\n */\nconst _apps = new Map();\n/**\r\n * Registered components.\r\n *\r\n * @internal\r\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\r\n * @param component - the component being added to this app's container\r\n *\r\n * @internal\r\n */\nfunction _addComponent(app, component) {\n  try {\n    app.container.addComponent(component);\n  } catch (e) {\n    logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n  }\n}\n/**\r\n *\r\n * @internal\r\n */\nfunction _addOrOverwriteComponent(app, component) {\n  app.container.addOrOverwriteComponent(component);\n}\n/**\r\n *\r\n * @param component - the component to register\r\n * @returns whether or not the component is registered successfully\r\n *\r\n * @internal\r\n */\nfunction _registerComponent(component) {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(`There were multiple attempts to register component ${componentName}.`);\n    return false;\n  }\n  _components.set(componentName, component);\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app, component);\n  }\n  return true;\n}\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n *\r\n * @returns the provider for the service with the matching name\r\n *\r\n * @internal\r\n */\nfunction _getProvider(app, name) {\n  const heartbeatController = app.container.getProvider('heartbeat').getImmediate({\n    optional: true\n  });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return app.container.getProvider(name);\n}\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\r\n *\r\n * @internal\r\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\r\n * Test only\r\n *\r\n * @internal\r\n */\nfunction _clearComponents() {\n  _components.clear();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" + 'call initializeApp() first',\n  [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}\",\n  [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n  [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n  [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n  [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.',\n  [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n  [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass FirebaseAppImpl {\n  constructor(options, config, container) {\n    this._isDeleted = false;\n    this._options = Object.assign({}, options);\n    this._config = Object.assign({}, config);\n    this._name = config.name;\n    this._automaticDataCollectionEnabled = config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  }\n\n  get automaticDataCollectionEnabled() {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n  set automaticDataCollectionEnabled(val) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n  get name() {\n    this.checkDestroyed();\n    return this._name;\n  }\n  get options() {\n    this.checkDestroyed();\n    return this._options;\n  }\n  get config() {\n    this.checkDestroyed();\n    return this._config;\n  }\n  get container() {\n    return this._container;\n  }\n  get isDeleted() {\n    return this._isDeleted;\n  }\n  set isDeleted(val) {\n    this._isDeleted = val;\n  }\n  /**\r\n   * This function will throw an Error if the App has already been deleted -\r\n   * use before performing API actions on the App.\r\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, {\n        appName: this._name\n      });\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The current SDK version.\r\n *\r\n * @public\r\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n  let options = _options;\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = {\n      name\n    };\n  }\n  const config = Object.assign({\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false\n  }, rawConfig);\n  const name = config.name;\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n      appName: String(name)\n    });\n  }\n  options || (options = getDefaultAppConfig());\n  if (!options) {\n    throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n  }\n\n  const existingApp = _apps.get(name);\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (deepEqual(options, existingApp.options) && deepEqual(config, existingApp.config)) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, {\n        appName: name\n      });\n    }\n  }\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseAppImpl(options, config, container);\n  _apps.set(name, newApp);\n  return newApp;\n}\n/**\r\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * When called with no arguments, the default app is returned. When an app name\r\n * is provided, the app corresponding to that name is returned.\r\n *\r\n * An exception is thrown if the app being retrieved has not yet been\r\n * initialized.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return the default app\r\n * const app = getApp();\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return a named app\r\n * const otherApp = getApp(\"otherApp\");\r\n * ```\r\n *\r\n * @param name - Optional name of the app to return. If no name is\r\n *   provided, the default is `\"[DEFAULT]\"`.\r\n *\r\n * @returns The app corresponding to the provided app name.\r\n *   If no app name is provided, the default app is returned.\r\n *\r\n * @public\r\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, {\n      appName: name\n    });\n  }\n  return app;\n}\n/**\r\n * A (read-only) array of all initialized apps.\r\n * @public\r\n */\nfunction getApps() {\n  return Array.from(_apps.values());\n}\n/**\r\n * Renders this app unusable and frees the resources of all associated\r\n * services.\r\n *\r\n * @example\r\n * ```javascript\r\n * deleteApp(app)\r\n *   .then(function() {\r\n *     console.log(\"App deleted successfully\");\r\n *   })\r\n *   .catch(function(error) {\r\n *     console.log(\"Error deleting app:\", error);\r\n *   });\r\n * ```\r\n *\r\n * @public\r\n */\nfunction deleteApp(_x) {\n  return _deleteApp.apply(this, arguments);\n}\n/**\r\n * Registers a library's name and version for platform logging purposes.\r\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\r\n * @param version - Current version of that library.\r\n * @param variant - Bundle variant, e.g., node, rn, etc.\r\n *\r\n * @public\r\n */\nfunction _deleteApp() {\n  _deleteApp = _asyncToGenerator(function* (app) {\n    const name = app.name;\n    if (_apps.has(name)) {\n      _apps.delete(name);\n      yield Promise.all(app.container.getProviders().map(provider => provider.delete()));\n      app.isDeleted = true;\n    }\n  });\n  return _deleteApp.apply(this, arguments);\n}\nfunction registerVersion(libraryKeyOrName, version, variant) {\n  var _a;\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [`Unable to register library \"${library}\" with version \"${version}\":`];\n    if (libraryMismatch) {\n      warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(new Component(`${library}-version`, () => ({\n    library,\n    version\n  }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\r\n * Sets log handler for all Firebase SDKs.\r\n * @param logCallback - An optional custom log handler that executes user code whenever\r\n * the Firebase SDK makes a logging call.\r\n *\r\n * @public\r\n */\nfunction onLog(logCallback, options) {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n  }\n\n  setUserLogHandler(logCallback, options);\n}\n/**\r\n * Sets log level for all Firebase SDKs.\r\n *\r\n * All of the log types above the current log level are captured (i.e. if\r\n * you set the log level to `info`, errors are logged, but `debug` and\r\n * `verbose` logs are not).\r\n *\r\n * @public\r\n */\nfunction setLogLevel(logLevel) {\n  setLogLevel$1(logLevel);\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(STORE_NAME);\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\nfunction readHeartbeatsFromIndexedDB(_x2) {\n  return _readHeartbeatsFromIndexedDB.apply(this, arguments);\n}\nfunction _readHeartbeatsFromIndexedDB() {\n  _readHeartbeatsFromIndexedDB = _asyncToGenerator(function* (app) {\n    try {\n      const db = yield getDbPromise();\n      const result = yield db.transaction(STORE_NAME).objectStore(STORE_NAME).get(computeKey(app));\n      return result;\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        logger.warn(e.message);\n      } else {\n        const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n          originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n        logger.warn(idbGetError.message);\n      }\n    }\n  });\n  return _readHeartbeatsFromIndexedDB.apply(this, arguments);\n}\nfunction writeHeartbeatsToIndexedDB(_x3, _x4) {\n  return _writeHeartbeatsToIndexedDB.apply(this, arguments);\n}\nfunction _writeHeartbeatsToIndexedDB() {\n  _writeHeartbeatsToIndexedDB = _asyncToGenerator(function* (app, heartbeatObject) {\n    try {\n      const db = yield getDbPromise();\n      const tx = db.transaction(STORE_NAME, 'readwrite');\n      const objectStore = tx.objectStore(STORE_NAME);\n      yield objectStore.put(heartbeatObject, computeKey(app));\n      yield tx.done;\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        logger.warn(e.message);\n      } else {\n        const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n          originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n        logger.warn(idbGetError.message);\n      }\n    }\n  });\n  return _writeHeartbeatsToIndexedDB.apply(this, arguments);\n}\nfunction computeKey(app) {\n  return `${app.name}!${app.options.appId}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst MAX_HEADER_BYTES = 1024;\n// 30 days\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\nclass HeartbeatServiceImpl {\n  constructor(container) {\n    this.container = container;\n    /**\r\n     * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\r\n     * the header string.\r\n     * Stores one record per date. This will be consolidated into the standard\r\n     * format of one record per user agent string before being sent as a header.\r\n     * Populated from indexedDB when the controller is instantiated and should\r\n     * be kept in sync with indexedDB.\r\n     * Leave public for easier testing.\r\n     */\n    this._heartbeatsCache = null;\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n  /**\r\n   * Called to report a heartbeat. The function will generate\r\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\r\n   * to IndexedDB.\r\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\r\n   * already logged, subsequent calls to this function in the same day will be ignored.\r\n   */\n  triggerHeartbeat() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const platformLogger = _this.container.getProvider('platform-logger').getImmediate();\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (_this._heartbeatsCache === null) {\n        _this._heartbeatsCache = yield _this._heartbeatsCachePromise;\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (_this._heartbeatsCache.lastSentHeartbeatDate === date || _this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        _this._heartbeatsCache.heartbeats.push({\n          date,\n          agent\n        });\n      }\n      // Remove entries older than 30 days.\n      _this._heartbeatsCache.heartbeats = _this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\n        const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\n        const now = Date.now();\n        return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\n      });\n      return _this._storage.overwrite(_this._heartbeatsCache);\n    })();\n  }\n  /**\r\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\r\n   * It also clears all heartbeats from memory as well as in IndexedDB.\r\n   *\r\n   * NOTE: Consuming product SDKs should not send the header if this method\r\n   * returns an empty string.\r\n   */\n  getHeartbeatsHeader() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2._heartbeatsCache === null) {\n        yield _this2._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (_this2._heartbeatsCache === null || _this2._heartbeatsCache.heartbeats.length === 0) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const {\n        heartbeatsToSend,\n        unsentEntries\n      } = extractHeartbeatsForHeader(_this2._heartbeatsCache.heartbeats);\n      const headerString = base64urlEncodeWithoutPadding(JSON.stringify({\n        version: 2,\n        heartbeats: heartbeatsToSend\n      }));\n      // Store last sent date to prevent another being logged/sent for the same day.\n      _this2._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        _this2._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        yield _this2._storage.overwrite(_this2._heartbeatsCache);\n      } else {\n        _this2._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void _this2._storage.overwrite(_this2._heartbeatsCache);\n      }\n      return headerString;\n    })();\n  }\n}\nfunction getUTCDateString() {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\nclass HeartbeatStorageImpl {\n  constructor(app) {\n    this.app = app;\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  runIndexedDBEnvironmentCheck() {\n    return _asyncToGenerator(function* () {\n      if (!isIndexedDBAvailable()) {\n        return false;\n      } else {\n        return validateIndexedDBOpenable().then(() => true).catch(() => false);\n      }\n    })();\n  }\n  /**\r\n   * Read all heartbeats.\r\n   */\n  read() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const canUseIndexedDB = yield _this3._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return {\n          heartbeats: []\n        };\n      } else {\n        const idbHeartbeatObject = yield readHeartbeatsFromIndexedDB(_this3.app);\n        return idbHeartbeatObject || {\n          heartbeats: []\n        };\n      }\n    })();\n  }\n  // overwrite the storage with the provided heartbeats\n  overwrite(heartbeatsObject) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const canUseIndexedDB = yield _this4._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return;\n      } else {\n        const existingHeartbeatsObject = yield _this4.read();\n        return writeHeartbeatsToIndexedDB(_this4.app, {\n          lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n          heartbeats: heartbeatsObject.heartbeats\n        });\n      }\n    })();\n  }\n  // add heartbeats\n  add(heartbeatsObject) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const canUseIndexedDB = yield _this5._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return;\n      } else {\n        const existingHeartbeatsObject = yield _this5.read();\n        return writeHeartbeatsToIndexedDB(_this5.app, {\n          lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n          heartbeats: [...existingHeartbeatsObject.heartbeats, ...heartbeatsObject.heartbeats]\n        });\n      }\n    })();\n  }\n}\n/**\r\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\r\n * in a platform logging header JSON object, stringified, and converted\r\n * to base 64.\r\n */\nfunction countBytes(heartbeatsCache) {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n  // heartbeatsCache wrapper properties\n  JSON.stringify({\n    version: 2,\n    heartbeats: heartbeatsCache\n  })).length;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction registerCoreComponents(variant) {\n  _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  // Register `app` package.\n  registerVersion(name$o, version$1, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name$o, version$1, 'esm2017');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n\n/**\r\n * Firebase App\r\n *\r\n * @remarks This package coordinates the communication between the different Firebase components\r\n * @packageDocumentation\r\n */\nregisterCoreComponents('');\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _registerComponent, _removeServiceInstance, deleteApp, getApp, getApps, initializeApp, onLog, registerVersion, setLogLevel };", "map": {"version": 3, "names": ["Component", "ComponentContainer", "<PERSON><PERSON>", "setUserLogHandler", "setLogLevel", "setLogLevel$1", "ErrorFactory", "getDefaultAppConfig", "deepEqual", "FirebaseError", "base64urlEncodeWithoutPadding", "isIndexedDBAvailable", "validateIndexedDBOpenable", "openDB", "PlatformLoggerServiceImpl", "constructor", "container", "getPlatformInfoString", "providers", "getProviders", "map", "provider", "isVersionServiceProvider", "service", "getImmediate", "library", "version", "filter", "logString", "join", "component", "getComponent", "type", "name$o", "version$1", "logger", "name$n", "name$m", "name$l", "name$k", "name$j", "name$i", "name$h", "name$g", "name$f", "name$e", "name$d", "name$c", "name$b", "name$a", "name$9", "name$8", "name$7", "name$6", "name$5", "name$4", "name$3", "name$2", "name$1", "name", "DEFAULT_ENTRY_NAME", "PLATFORM_LOG_STRING", "_apps", "Map", "_components", "_addComponent", "app", "addComponent", "e", "debug", "_addOrOverwriteComponent", "addOrOverwriteComponent", "_registerComponent", "componentName", "has", "set", "values", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "get<PERSON><PERSON><PERSON>", "optional", "triggerHeartbeat", "_removeServiceInstance", "instanceIdentifier", "clearInstance", "_clearComponents", "clear", "ERRORS", "ERROR_FACTORY", "FirebaseAppImpl", "options", "config", "_isDeleted", "_options", "Object", "assign", "_config", "_name", "_automaticDataCollectionEnabled", "automaticDataCollectionEnabled", "_container", "checkDestroyed", "val", "isDeleted", "create", "appName", "SDK_VERSION", "initializeApp", "rawConfig", "String", "existingApp", "get", "newApp", "getApp", "getApps", "Array", "from", "deleteApp", "_x", "_deleteApp", "apply", "arguments", "_asyncToGenerator", "delete", "Promise", "all", "registerVersion", "libraryKeyOrName", "variant", "_a", "libraryMismatch", "match", "versionMismatch", "warning", "push", "warn", "onLog", "logCallback", "logLevel", "DB_NAME", "DB_VERSION", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgrade", "db", "oldVersion", "createObjectStore", "catch", "originalErrorMessage", "message", "readHeartbeatsFromIndexedDB", "_x2", "_readHeartbeatsFromIndexedDB", "result", "transaction", "objectStore", "computeKey", "idbGetError", "writeHeartbeatsToIndexedDB", "_x3", "_x4", "_writeHeartbeatsToIndexedDB", "heartbeatObject", "tx", "put", "done", "appId", "MAX_HEADER_BYTES", "STORED_HEARTBEAT_RETENTION_MAX_MILLIS", "HeartbeatServiceImpl", "_heartbeatsCache", "_storage", "HeartbeatStorageImpl", "_heartbeatsCachePromise", "read", "then", "_this", "platformLogger", "agent", "date", "getUTCDateString", "lastSentHeartbeatDate", "heartbeats", "some", "singleDateHeartbeat", "hbTimestamp", "Date", "valueOf", "now", "overwrite", "getHeartbeatsHeader", "_this2", "length", "heartbeatsToSend", "unsentEntries", "extractHeartbeatsForHeader", "headerString", "JSON", "stringify", "today", "toISOString", "substring", "heartbeatsCache", "maxSize", "slice", "heartbeatEntry", "find", "hb", "dates", "countBytes", "pop", "_canUseIndexedDBPromise", "runIndexedDBEnvironmentCheck", "_this3", "canUseIndexedDB", "idbHeartbeatObject", "heartbeatsObject", "_this4", "existingHeartbeatsObject", "add", "_this5", "registerCoreComponents", "_DEFAULT_ENTRY_NAME"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/node_modules/@firebase/app/dist/esm/index.esm2017.js"], "sourcesContent": ["import { Component, ComponentContainer } from '@firebase/component';\nimport { <PERSON><PERSON>, setUser<PERSON>ogH<PERSON><PERSON>, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, getDefaultAppConfig, deepEqual, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass PlatformLoggerServiceImpl {\r\n    constructor(container) {\r\n        this.container = container;\r\n    }\r\n    // In initial implementation, this will be called by installations on\r\n    // auth token refresh, and installations will send this string.\r\n    getPlatformInfoString() {\r\n        const providers = this.container.getProviders();\r\n        // Loop through providers and get library/version pairs from any that are\r\n        // version components.\r\n        return providers\r\n            .map(provider => {\r\n            if (isVersionServiceProvider(provider)) {\r\n                const service = provider.getImmediate();\r\n                return `${service.library}/${service.version}`;\r\n            }\r\n            else {\r\n                return null;\r\n            }\r\n        })\r\n            .filter(logString => logString)\r\n            .join(' ');\r\n    }\r\n}\r\n/**\r\n *\r\n * @param provider check if this provider provides a VersionService\r\n *\r\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\r\n * provides VersionService. The provider is not necessarily a 'app-version'\r\n * provider.\r\n */\r\nfunction isVersionServiceProvider(provider) {\r\n    const component = provider.getComponent();\r\n    return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\r\n}\n\nconst name$o = \"@firebase/app\";\nconst version$1 = \"0.9.13\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst logger = new Logger('@firebase/app');\n\nconst name$n = \"@firebase/app-compat\";\n\nconst name$m = \"@firebase/analytics-compat\";\n\nconst name$l = \"@firebase/analytics\";\n\nconst name$k = \"@firebase/app-check-compat\";\n\nconst name$j = \"@firebase/app-check\";\n\nconst name$i = \"@firebase/auth\";\n\nconst name$h = \"@firebase/auth-compat\";\n\nconst name$g = \"@firebase/database\";\n\nconst name$f = \"@firebase/database-compat\";\n\nconst name$e = \"@firebase/functions\";\n\nconst name$d = \"@firebase/functions-compat\";\n\nconst name$c = \"@firebase/installations\";\n\nconst name$b = \"@firebase/installations-compat\";\n\nconst name$a = \"@firebase/messaging\";\n\nconst name$9 = \"@firebase/messaging-compat\";\n\nconst name$8 = \"@firebase/performance\";\n\nconst name$7 = \"@firebase/performance-compat\";\n\nconst name$6 = \"@firebase/remote-config\";\n\nconst name$5 = \"@firebase/remote-config-compat\";\n\nconst name$4 = \"@firebase/storage\";\n\nconst name$3 = \"@firebase/storage-compat\";\n\nconst name$2 = \"@firebase/firestore\";\n\nconst name$1 = \"@firebase/firestore-compat\";\n\nconst name = \"firebase\";\nconst version = \"9.23.0\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * The default app name\r\n *\r\n * @internal\r\n */\r\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\r\nconst PLATFORM_LOG_STRING = {\r\n    [name$o]: 'fire-core',\r\n    [name$n]: 'fire-core-compat',\r\n    [name$l]: 'fire-analytics',\r\n    [name$m]: 'fire-analytics-compat',\r\n    [name$j]: 'fire-app-check',\r\n    [name$k]: 'fire-app-check-compat',\r\n    [name$i]: 'fire-auth',\r\n    [name$h]: 'fire-auth-compat',\r\n    [name$g]: 'fire-rtdb',\r\n    [name$f]: 'fire-rtdb-compat',\r\n    [name$e]: 'fire-fn',\r\n    [name$d]: 'fire-fn-compat',\r\n    [name$c]: 'fire-iid',\r\n    [name$b]: 'fire-iid-compat',\r\n    [name$a]: 'fire-fcm',\r\n    [name$9]: 'fire-fcm-compat',\r\n    [name$8]: 'fire-perf',\r\n    [name$7]: 'fire-perf-compat',\r\n    [name$6]: 'fire-rc',\r\n    [name$5]: 'fire-rc-compat',\r\n    [name$4]: 'fire-gcs',\r\n    [name$3]: 'fire-gcs-compat',\r\n    [name$2]: 'fire-fst',\r\n    [name$1]: 'fire-fst-compat',\r\n    'fire-js': 'fire-js',\r\n    [name]: 'fire-js-all'\r\n};\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @internal\r\n */\r\nconst _apps = new Map();\r\n/**\r\n * Registered components.\r\n *\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nconst _components = new Map();\r\n/**\r\n * @param component - the component being added to this app's container\r\n *\r\n * @internal\r\n */\r\nfunction _addComponent(app, component) {\r\n    try {\r\n        app.container.addComponent(component);\r\n    }\r\n    catch (e) {\r\n        logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\r\n    }\r\n}\r\n/**\r\n *\r\n * @internal\r\n */\r\nfunction _addOrOverwriteComponent(app, component) {\r\n    app.container.addOrOverwriteComponent(component);\r\n}\r\n/**\r\n *\r\n * @param component - the component to register\r\n * @returns whether or not the component is registered successfully\r\n *\r\n * @internal\r\n */\r\nfunction _registerComponent(component) {\r\n    const componentName = component.name;\r\n    if (_components.has(componentName)) {\r\n        logger.debug(`There were multiple attempts to register component ${componentName}.`);\r\n        return false;\r\n    }\r\n    _components.set(componentName, component);\r\n    // add the component to existing app instances\r\n    for (const app of _apps.values()) {\r\n        _addComponent(app, component);\r\n    }\r\n    return true;\r\n}\r\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n *\r\n * @returns the provider for the service with the matching name\r\n *\r\n * @internal\r\n */\r\nfunction _getProvider(app, name) {\r\n    const heartbeatController = app.container\r\n        .getProvider('heartbeat')\r\n        .getImmediate({ optional: true });\r\n    if (heartbeatController) {\r\n        void heartbeatController.triggerHeartbeat();\r\n    }\r\n    return app.container.getProvider(name);\r\n}\r\n/**\r\n *\r\n * @param app - FirebaseApp instance\r\n * @param name - service name\r\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\r\n *\r\n * @internal\r\n */\r\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\r\n    _getProvider(app, name).clearInstance(instanceIdentifier);\r\n}\r\n/**\r\n * Test only\r\n *\r\n * @internal\r\n */\r\nfunction _clearComponents() {\r\n    _components.clear();\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERRORS = {\r\n    [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" +\r\n        'call initializeApp() first',\r\n    [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}\",\r\n    [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\r\n    [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\r\n    [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\r\n    [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' +\r\n        'Firebase App instance.',\r\n    [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\r\n    [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\r\n    [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass FirebaseAppImpl {\r\n    constructor(options, config, container) {\r\n        this._isDeleted = false;\r\n        this._options = Object.assign({}, options);\r\n        this._config = Object.assign({}, config);\r\n        this._name = config.name;\r\n        this._automaticDataCollectionEnabled =\r\n            config.automaticDataCollectionEnabled;\r\n        this._container = container;\r\n        this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    }\r\n    get automaticDataCollectionEnabled() {\r\n        this.checkDestroyed();\r\n        return this._automaticDataCollectionEnabled;\r\n    }\r\n    set automaticDataCollectionEnabled(val) {\r\n        this.checkDestroyed();\r\n        this._automaticDataCollectionEnabled = val;\r\n    }\r\n    get name() {\r\n        this.checkDestroyed();\r\n        return this._name;\r\n    }\r\n    get options() {\r\n        this.checkDestroyed();\r\n        return this._options;\r\n    }\r\n    get config() {\r\n        this.checkDestroyed();\r\n        return this._config;\r\n    }\r\n    get container() {\r\n        return this._container;\r\n    }\r\n    get isDeleted() {\r\n        return this._isDeleted;\r\n    }\r\n    set isDeleted(val) {\r\n        this._isDeleted = val;\r\n    }\r\n    /**\r\n     * This function will throw an Error if the App has already been deleted -\r\n     * use before performing API actions on the App.\r\n     */\r\n    checkDestroyed() {\r\n        if (this.isDeleted) {\r\n            throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, { appName: this._name });\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * The current SDK version.\r\n *\r\n * @public\r\n */\r\nconst SDK_VERSION = version;\r\nfunction initializeApp(_options, rawConfig = {}) {\r\n    let options = _options;\r\n    if (typeof rawConfig !== 'object') {\r\n        const name = rawConfig;\r\n        rawConfig = { name };\r\n    }\r\n    const config = Object.assign({ name: DEFAULT_ENTRY_NAME, automaticDataCollectionEnabled: false }, rawConfig);\r\n    const name = config.name;\r\n    if (typeof name !== 'string' || !name) {\r\n        throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\r\n            appName: String(name)\r\n        });\r\n    }\r\n    options || (options = getDefaultAppConfig());\r\n    if (!options) {\r\n        throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\r\n    }\r\n    const existingApp = _apps.get(name);\r\n    if (existingApp) {\r\n        // return the existing app if options and config deep equal the ones in the existing app.\r\n        if (deepEqual(options, existingApp.options) &&\r\n            deepEqual(config, existingApp.config)) {\r\n            return existingApp;\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, { appName: name });\r\n        }\r\n    }\r\n    const container = new ComponentContainer(name);\r\n    for (const component of _components.values()) {\r\n        container.addComponent(component);\r\n    }\r\n    const newApp = new FirebaseAppImpl(options, config, container);\r\n    _apps.set(name, newApp);\r\n    return newApp;\r\n}\r\n/**\r\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * When called with no arguments, the default app is returned. When an app name\r\n * is provided, the app corresponding to that name is returned.\r\n *\r\n * An exception is thrown if the app being retrieved has not yet been\r\n * initialized.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return the default app\r\n * const app = getApp();\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Return a named app\r\n * const otherApp = getApp(\"otherApp\");\r\n * ```\r\n *\r\n * @param name - Optional name of the app to return. If no name is\r\n *   provided, the default is `\"[DEFAULT]\"`.\r\n *\r\n * @returns The app corresponding to the provided app name.\r\n *   If no app name is provided, the default app is returned.\r\n *\r\n * @public\r\n */\r\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\r\n    const app = _apps.get(name);\r\n    if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\r\n        return initializeApp();\r\n    }\r\n    if (!app) {\r\n        throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, { appName: name });\r\n    }\r\n    return app;\r\n}\r\n/**\r\n * A (read-only) array of all initialized apps.\r\n * @public\r\n */\r\nfunction getApps() {\r\n    return Array.from(_apps.values());\r\n}\r\n/**\r\n * Renders this app unusable and frees the resources of all associated\r\n * services.\r\n *\r\n * @example\r\n * ```javascript\r\n * deleteApp(app)\r\n *   .then(function() {\r\n *     console.log(\"App deleted successfully\");\r\n *   })\r\n *   .catch(function(error) {\r\n *     console.log(\"Error deleting app:\", error);\r\n *   });\r\n * ```\r\n *\r\n * @public\r\n */\r\nasync function deleteApp(app) {\r\n    const name = app.name;\r\n    if (_apps.has(name)) {\r\n        _apps.delete(name);\r\n        await Promise.all(app.container\r\n            .getProviders()\r\n            .map(provider => provider.delete()));\r\n        app.isDeleted = true;\r\n    }\r\n}\r\n/**\r\n * Registers a library's name and version for platform logging purposes.\r\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\r\n * @param version - Current version of that library.\r\n * @param variant - Bundle variant, e.g., node, rn, etc.\r\n *\r\n * @public\r\n */\r\nfunction registerVersion(libraryKeyOrName, version, variant) {\r\n    var _a;\r\n    // TODO: We can use this check to whitelist strings when/if we set up\r\n    // a good whitelist system.\r\n    let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\r\n    if (variant) {\r\n        library += `-${variant}`;\r\n    }\r\n    const libraryMismatch = library.match(/\\s|\\//);\r\n    const versionMismatch = version.match(/\\s|\\//);\r\n    if (libraryMismatch || versionMismatch) {\r\n        const warning = [\r\n            `Unable to register library \"${library}\" with version \"${version}\":`\r\n        ];\r\n        if (libraryMismatch) {\r\n            warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\r\n        }\r\n        if (libraryMismatch && versionMismatch) {\r\n            warning.push('and');\r\n        }\r\n        if (versionMismatch) {\r\n            warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\r\n        }\r\n        logger.warn(warning.join(' '));\r\n        return;\r\n    }\r\n    _registerComponent(new Component(`${library}-version`, () => ({ library, version }), \"VERSION\" /* ComponentType.VERSION */));\r\n}\r\n/**\r\n * Sets log handler for all Firebase SDKs.\r\n * @param logCallback - An optional custom log handler that executes user code whenever\r\n * the Firebase SDK makes a logging call.\r\n *\r\n * @public\r\n */\r\nfunction onLog(logCallback, options) {\r\n    if (logCallback !== null && typeof logCallback !== 'function') {\r\n        throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\r\n    }\r\n    setUserLogHandler(logCallback, options);\r\n}\r\n/**\r\n * Sets log level for all Firebase SDKs.\r\n *\r\n * All of the log types above the current log level are captured (i.e. if\r\n * you set the log level to `info`, errors are logged, but `debug` and\r\n * `verbose` logs are not).\r\n *\r\n * @public\r\n */\r\nfunction setLogLevel(logLevel) {\r\n    setLogLevel$1(logLevel);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DB_NAME = 'firebase-heartbeat-database';\r\nconst DB_VERSION = 1;\r\nconst STORE_NAME = 'firebase-heartbeat-store';\r\nlet dbPromise = null;\r\nfunction getDbPromise() {\r\n    if (!dbPromise) {\r\n        dbPromise = openDB(DB_NAME, DB_VERSION, {\r\n            upgrade: (db, oldVersion) => {\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (oldVersion) {\r\n                    case 0:\r\n                        db.createObjectStore(STORE_NAME);\r\n                }\r\n            }\r\n        }).catch(e => {\r\n            throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\r\n                originalErrorMessage: e.message\r\n            });\r\n        });\r\n    }\r\n    return dbPromise;\r\n}\r\nasync function readHeartbeatsFromIndexedDB(app) {\r\n    try {\r\n        const db = await getDbPromise();\r\n        const result = await db\r\n            .transaction(STORE_NAME)\r\n            .objectStore(STORE_NAME)\r\n            .get(computeKey(app));\r\n        return result;\r\n    }\r\n    catch (e) {\r\n        if (e instanceof FirebaseError) {\r\n            logger.warn(e.message);\r\n        }\r\n        else {\r\n            const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\r\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n            });\r\n            logger.warn(idbGetError.message);\r\n        }\r\n    }\r\n}\r\nasync function writeHeartbeatsToIndexedDB(app, heartbeatObject) {\r\n    try {\r\n        const db = await getDbPromise();\r\n        const tx = db.transaction(STORE_NAME, 'readwrite');\r\n        const objectStore = tx.objectStore(STORE_NAME);\r\n        await objectStore.put(heartbeatObject, computeKey(app));\r\n        await tx.done;\r\n    }\r\n    catch (e) {\r\n        if (e instanceof FirebaseError) {\r\n            logger.warn(e.message);\r\n        }\r\n        else {\r\n            const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\r\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n            });\r\n            logger.warn(idbGetError.message);\r\n        }\r\n    }\r\n}\r\nfunction computeKey(app) {\r\n    return `${app.name}!${app.options.appId}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst MAX_HEADER_BYTES = 1024;\r\n// 30 days\r\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\r\nclass HeartbeatServiceImpl {\r\n    constructor(container) {\r\n        this.container = container;\r\n        /**\r\n         * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\r\n         * the header string.\r\n         * Stores one record per date. This will be consolidated into the standard\r\n         * format of one record per user agent string before being sent as a header.\r\n         * Populated from indexedDB when the controller is instantiated and should\r\n         * be kept in sync with indexedDB.\r\n         * Leave public for easier testing.\r\n         */\r\n        this._heartbeatsCache = null;\r\n        const app = this.container.getProvider('app').getImmediate();\r\n        this._storage = new HeartbeatStorageImpl(app);\r\n        this._heartbeatsCachePromise = this._storage.read().then(result => {\r\n            this._heartbeatsCache = result;\r\n            return result;\r\n        });\r\n    }\r\n    /**\r\n     * Called to report a heartbeat. The function will generate\r\n     * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\r\n     * to IndexedDB.\r\n     * Note that we only store one heartbeat per day. So if a heartbeat for today is\r\n     * already logged, subsequent calls to this function in the same day will be ignored.\r\n     */\r\n    async triggerHeartbeat() {\r\n        const platformLogger = this.container\r\n            .getProvider('platform-logger')\r\n            .getImmediate();\r\n        // This is the \"Firebase user agent\" string from the platform logger\r\n        // service, not the browser user agent.\r\n        const agent = platformLogger.getPlatformInfoString();\r\n        const date = getUTCDateString();\r\n        if (this._heartbeatsCache === null) {\r\n            this._heartbeatsCache = await this._heartbeatsCachePromise;\r\n        }\r\n        // Do not store a heartbeat if one is already stored for this day\r\n        // or if a header has already been sent today.\r\n        if (this._heartbeatsCache.lastSentHeartbeatDate === date ||\r\n            this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\r\n            return;\r\n        }\r\n        else {\r\n            // There is no entry for this date. Create one.\r\n            this._heartbeatsCache.heartbeats.push({ date, agent });\r\n        }\r\n        // Remove entries older than 30 days.\r\n        this._heartbeatsCache.heartbeats = this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\r\n            const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\r\n            const now = Date.now();\r\n            return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\r\n        });\r\n        return this._storage.overwrite(this._heartbeatsCache);\r\n    }\r\n    /**\r\n     * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\r\n     * It also clears all heartbeats from memory as well as in IndexedDB.\r\n     *\r\n     * NOTE: Consuming product SDKs should not send the header if this method\r\n     * returns an empty string.\r\n     */\r\n    async getHeartbeatsHeader() {\r\n        if (this._heartbeatsCache === null) {\r\n            await this._heartbeatsCachePromise;\r\n        }\r\n        // If it's still null or the array is empty, there is no data to send.\r\n        if (this._heartbeatsCache === null ||\r\n            this._heartbeatsCache.heartbeats.length === 0) {\r\n            return '';\r\n        }\r\n        const date = getUTCDateString();\r\n        // Extract as many heartbeats from the cache as will fit under the size limit.\r\n        const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);\r\n        const headerString = base64urlEncodeWithoutPadding(JSON.stringify({ version: 2, heartbeats: heartbeatsToSend }));\r\n        // Store last sent date to prevent another being logged/sent for the same day.\r\n        this._heartbeatsCache.lastSentHeartbeatDate = date;\r\n        if (unsentEntries.length > 0) {\r\n            // Store any unsent entries if they exist.\r\n            this._heartbeatsCache.heartbeats = unsentEntries;\r\n            // This seems more likely than emptying the array (below) to lead to some odd state\r\n            // since the cache isn't empty and this will be called again on the next request,\r\n            // and is probably safest if we await it.\r\n            await this._storage.overwrite(this._heartbeatsCache);\r\n        }\r\n        else {\r\n            this._heartbeatsCache.heartbeats = [];\r\n            // Do not wait for this, to reduce latency.\r\n            void this._storage.overwrite(this._heartbeatsCache);\r\n        }\r\n        return headerString;\r\n    }\r\n}\r\nfunction getUTCDateString() {\r\n    const today = new Date();\r\n    // Returns date format 'YYYY-MM-DD'\r\n    return today.toISOString().substring(0, 10);\r\n}\r\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\r\n    // Heartbeats grouped by user agent in the standard format to be sent in\r\n    // the header.\r\n    const heartbeatsToSend = [];\r\n    // Single date format heartbeats that are not sent.\r\n    let unsentEntries = heartbeatsCache.slice();\r\n    for (const singleDateHeartbeat of heartbeatsCache) {\r\n        // Look for an existing entry with the same user agent.\r\n        const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\r\n        if (!heartbeatEntry) {\r\n            // If no entry for this user agent exists, create one.\r\n            heartbeatsToSend.push({\r\n                agent: singleDateHeartbeat.agent,\r\n                dates: [singleDateHeartbeat.date]\r\n            });\r\n            if (countBytes(heartbeatsToSend) > maxSize) {\r\n                // If the header would exceed max size, remove the added heartbeat\r\n                // entry and stop adding to the header.\r\n                heartbeatsToSend.pop();\r\n                break;\r\n            }\r\n        }\r\n        else {\r\n            heartbeatEntry.dates.push(singleDateHeartbeat.date);\r\n            // If the header would exceed max size, remove the added date\r\n            // and stop adding to the header.\r\n            if (countBytes(heartbeatsToSend) > maxSize) {\r\n                heartbeatEntry.dates.pop();\r\n                break;\r\n            }\r\n        }\r\n        // Pop unsent entry from queue. (Skipped if adding the entry exceeded\r\n        // quota and the loop breaks early.)\r\n        unsentEntries = unsentEntries.slice(1);\r\n    }\r\n    return {\r\n        heartbeatsToSend,\r\n        unsentEntries\r\n    };\r\n}\r\nclass HeartbeatStorageImpl {\r\n    constructor(app) {\r\n        this.app = app;\r\n        this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\r\n    }\r\n    async runIndexedDBEnvironmentCheck() {\r\n        if (!isIndexedDBAvailable()) {\r\n            return false;\r\n        }\r\n        else {\r\n            return validateIndexedDBOpenable()\r\n                .then(() => true)\r\n                .catch(() => false);\r\n        }\r\n    }\r\n    /**\r\n     * Read all heartbeats.\r\n     */\r\n    async read() {\r\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\r\n        if (!canUseIndexedDB) {\r\n            return { heartbeats: [] };\r\n        }\r\n        else {\r\n            const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\r\n            return idbHeartbeatObject || { heartbeats: [] };\r\n        }\r\n    }\r\n    // overwrite the storage with the provided heartbeats\r\n    async overwrite(heartbeatsObject) {\r\n        var _a;\r\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\r\n        if (!canUseIndexedDB) {\r\n            return;\r\n        }\r\n        else {\r\n            const existingHeartbeatsObject = await this.read();\r\n            return writeHeartbeatsToIndexedDB(this.app, {\r\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\r\n                heartbeats: heartbeatsObject.heartbeats\r\n            });\r\n        }\r\n    }\r\n    // add heartbeats\r\n    async add(heartbeatsObject) {\r\n        var _a;\r\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\r\n        if (!canUseIndexedDB) {\r\n            return;\r\n        }\r\n        else {\r\n            const existingHeartbeatsObject = await this.read();\r\n            return writeHeartbeatsToIndexedDB(this.app, {\r\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\r\n                heartbeats: [\r\n                    ...existingHeartbeatsObject.heartbeats,\r\n                    ...heartbeatsObject.heartbeats\r\n                ]\r\n            });\r\n        }\r\n    }\r\n}\r\n/**\r\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\r\n * in a platform logging header JSON object, stringified, and converted\r\n * to base 64.\r\n */\r\nfunction countBytes(heartbeatsCache) {\r\n    // base64 has a restricted set of characters, all of which should be 1 byte.\r\n    return base64urlEncodeWithoutPadding(\r\n    // heartbeatsCache wrapper properties\r\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })).length;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction registerCoreComponents(variant) {\r\n    _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n    _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n    // Register `app` package.\r\n    registerVersion(name$o, version$1, variant);\r\n    // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\n    registerVersion(name$o, version$1, 'esm2017');\r\n    // Register platform SDK identifier (no version).\r\n    registerVersion('fire-js', '');\r\n}\n\n/**\r\n * Firebase App\r\n *\r\n * @remarks This package coordinates the communication between the different Firebase components\r\n * @packageDocumentation\r\n */\r\nregisterCoreComponents('');\n\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _registerComponent, _removeServiceInstance, deleteApp, getApp, getApps, initializeApp, onLog, registerVersion, setLogLevel };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,kBAAkB,QAAQ,qBAAqB;AACnE,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,WAAW,IAAIC,aAAa,QAAQ,kBAAkB;AAC1F,SAASC,YAAY,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,6BAA6B,EAAEC,oBAAoB,EAAEC,yBAAyB,QAAQ,gBAAgB;AAC5K,SAASH,aAAa,QAAQ,gBAAgB;AAC9C,SAASI,MAAM,QAAQ,KAAK;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;EACA;EACAC,qBAAqBA,CAAA,EAAG;IACpB,MAAMC,SAAS,GAAG,IAAI,CAACF,SAAS,CAACG,YAAY,CAAC,CAAC;IAC/C;IACA;IACA,OAAOD,SAAS,CACXE,GAAG,CAACC,QAAQ,IAAI;MACjB,IAAIC,wBAAwB,CAACD,QAAQ,CAAC,EAAE;QACpC,MAAME,OAAO,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;QACvC,OAAQ,GAAED,OAAO,CAACE,OAAQ,IAAGF,OAAO,CAACG,OAAQ,EAAC;MAClD,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ,CAAC,CAAC,CACGC,MAAM,CAACC,SAAS,IAAIA,SAAS,CAAC,CAC9BC,IAAI,CAAC,GAAG,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,wBAAwBA,CAACD,QAAQ,EAAE;EACxC,MAAMS,SAAS,GAAGT,QAAQ,CAACU,YAAY,CAAC,CAAC;EACzC,OAAO,CAACD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,IAAI,MAAM,SAAS,CAAC;AAChG;;AAEA,MAAMC,MAAM,GAAG,eAAe;AAC9B,MAAMC,SAAS,GAAG,QAAQ;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,IAAIjC,MAAM,CAAC,eAAe,CAAC;AAE1C,MAAMkC,MAAM,GAAG,sBAAsB;AAErC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,gBAAgB;AAE/B,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,MAAMC,MAAM,GAAG,oBAAoB;AAEnC,MAAMC,MAAM,GAAG,2BAA2B;AAE1C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,yBAAyB;AAExC,MAAMC,MAAM,GAAG,gCAAgC;AAE/C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,MAAMC,MAAM,GAAG,8BAA8B;AAE7C,MAAMC,MAAM,GAAG,yBAAyB;AAExC,MAAMC,MAAM,GAAG,gCAAgC;AAE/C,MAAMC,MAAM,GAAG,mBAAmB;AAElC,MAAMC,MAAM,GAAG,0BAA0B;AAEzC,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,IAAI,GAAG,UAAU;AACvB,MAAMjC,OAAO,GAAG,QAAQ;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,kBAAkB,GAAG,WAAW;AACtC,MAAMC,mBAAmB,GAAG;EACxB,CAAC5B,MAAM,GAAG,WAAW;EACrB,CAACG,MAAM,GAAG,kBAAkB;EAC5B,CAACE,MAAM,GAAG,gBAAgB;EAC1B,CAACD,MAAM,GAAG,uBAAuB;EACjC,CAACG,MAAM,GAAG,gBAAgB;EAC1B,CAACD,MAAM,GAAG,uBAAuB;EACjC,CAACE,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,SAAS;EACnB,CAACC,MAAM,GAAG,gBAAgB;EAC1B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,SAAS;EACnB,CAACC,MAAM,GAAG,gBAAgB;EAC1B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,SAAS,EAAE,SAAS;EACpB,CAACC,IAAI,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACC,GAAG,EAAEpC,SAAS,EAAE;EACnC,IAAI;IACAoC,GAAG,CAAClD,SAAS,CAACmD,YAAY,CAACrC,SAAS,CAAC;EACzC,CAAC,CACD,OAAOsC,CAAC,EAAE;IACNjC,MAAM,CAACkC,KAAK,CAAE,aAAYvC,SAAS,CAAC6B,IAAK,wCAAuCO,GAAG,CAACP,IAAK,EAAC,EAAES,CAAC,CAAC;EAClG;AACJ;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAACJ,GAAG,EAAEpC,SAAS,EAAE;EAC9CoC,GAAG,CAAClD,SAAS,CAACuD,uBAAuB,CAACzC,SAAS,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0C,kBAAkBA,CAAC1C,SAAS,EAAE;EACnC,MAAM2C,aAAa,GAAG3C,SAAS,CAAC6B,IAAI;EACpC,IAAIK,WAAW,CAACU,GAAG,CAACD,aAAa,CAAC,EAAE;IAChCtC,MAAM,CAACkC,KAAK,CAAE,sDAAqDI,aAAc,GAAE,CAAC;IACpF,OAAO,KAAK;EAChB;EACAT,WAAW,CAACW,GAAG,CAACF,aAAa,EAAE3C,SAAS,CAAC;EACzC;EACA,KAAK,MAAMoC,GAAG,IAAIJ,KAAK,CAACc,MAAM,CAAC,CAAC,EAAE;IAC9BX,aAAa,CAACC,GAAG,EAAEpC,SAAS,CAAC;EACjC;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+C,YAAYA,CAACX,GAAG,EAAEP,IAAI,EAAE;EAC7B,MAAMmB,mBAAmB,GAAGZ,GAAG,CAAClD,SAAS,CACpC+D,WAAW,CAAC,WAAW,CAAC,CACxBvD,YAAY,CAAC;IAAEwD,QAAQ,EAAE;EAAK,CAAC,CAAC;EACrC,IAAIF,mBAAmB,EAAE;IACrB,KAAKA,mBAAmB,CAACG,gBAAgB,CAAC,CAAC;EAC/C;EACA,OAAOf,GAAG,CAAClD,SAAS,CAAC+D,WAAW,CAACpB,IAAI,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,sBAAsBA,CAAChB,GAAG,EAAEP,IAAI,EAAEwB,kBAAkB,GAAGvB,kBAAkB,EAAE;EAChFiB,YAAY,CAACX,GAAG,EAAEP,IAAI,CAAC,CAACyB,aAAa,CAACD,kBAAkB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAAA,EAAG;EACxBrB,WAAW,CAACsB,KAAK,CAAC,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACX,CAAC,QAAQ,CAAC,wBAAwB,kDAAkD,GAChF,4BAA4B;EAChC,CAAC,cAAc,CAAC,8BAA8B,+BAA+B;EAC7E,CAAC,eAAe,CAAC,+BAA+B,iFAAiF;EACjI,CAAC,aAAa,CAAC,6BAA6B,iDAAiD;EAC7F,CAAC,YAAY,CAAC,4BAA4B,yEAAyE;EACnH,CAAC,sBAAsB,CAAC,sCAAsC,sDAAsD,GAChH,wBAAwB;EAC5B,CAAC,sBAAsB,CAAC,sCAAsC,uDAAuD;EACrH,CAAC,UAAU,CAAC,0BAA0B,+EAA+E;EACrH,CAAC,SAAS,CAAC,yBAAyB,oFAAoF;EACxH,CAAC,SAAS,CAAC,2BAA2B,kFAAkF;EACxH,CAAC,YAAY,CAAC,4BAA4B;AAC9C,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIlF,YAAY,CAAC,KAAK,EAAE,UAAU,EAAEiF,MAAM,CAAC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClB1E,WAAWA,CAAC2E,OAAO,EAAEC,MAAM,EAAE3E,SAAS,EAAE;IACpC,IAAI,CAAC4E,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,OAAO,CAAC;IAC1C,IAAI,CAACM,OAAO,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC;IACxC,IAAI,CAACM,KAAK,GAAGN,MAAM,CAAChC,IAAI;IACxB,IAAI,CAACuC,+BAA+B,GAChCP,MAAM,CAACQ,8BAA8B;IACzC,IAAI,CAACC,UAAU,GAAGpF,SAAS;IAC3B,IAAI,CAACA,SAAS,CAACmD,YAAY,CAAC,IAAInE,SAAS,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACtG;;EACA,IAAImG,8BAA8BA,CAAA,EAAG;IACjC,IAAI,CAACE,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACH,+BAA+B;EAC/C;EACA,IAAIC,8BAA8BA,CAACG,GAAG,EAAE;IACpC,IAAI,CAACD,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,+BAA+B,GAAGI,GAAG;EAC9C;EACA,IAAI3C,IAAIA,CAAA,EAAG;IACP,IAAI,CAAC0C,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACJ,KAAK;EACrB;EACA,IAAIP,OAAOA,CAAA,EAAG;IACV,IAAI,CAACW,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACR,QAAQ;EACxB;EACA,IAAIF,MAAMA,CAAA,EAAG;IACT,IAAI,CAACU,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACL,OAAO;EACvB;EACA,IAAIhF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACoF,UAAU;EAC1B;EACA,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACX,UAAU;EAC1B;EACA,IAAIW,SAASA,CAACD,GAAG,EAAE;IACf,IAAI,CAACV,UAAU,GAAGU,GAAG;EACzB;EACA;AACJ;AACA;AACA;EACID,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,MAAMf,aAAa,CAACgB,MAAM,CAAC,aAAa,CAAC,4BAA4B;QAAEC,OAAO,EAAE,IAAI,CAACR;MAAM,CAAC,CAAC;IACjG;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,WAAW,GAAGhF,OAAO;AAC3B,SAASiF,aAAaA,CAACd,QAAQ,EAAEe,SAAS,GAAG,CAAC,CAAC,EAAE;EAC7C,IAAIlB,OAAO,GAAGG,QAAQ;EACtB,IAAI,OAAOe,SAAS,KAAK,QAAQ,EAAE;IAC/B,MAAMjD,IAAI,GAAGiD,SAAS;IACtBA,SAAS,GAAG;MAAEjD;IAAK,CAAC;EACxB;EACA,MAAMgC,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC;IAAEpC,IAAI,EAAEC,kBAAkB;IAAEuC,8BAA8B,EAAE;EAAM,CAAC,EAAES,SAAS,CAAC;EAC5G,MAAMjD,IAAI,GAAGgC,MAAM,CAAChC,IAAI;EACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IACnC,MAAM6B,aAAa,CAACgB,MAAM,CAAC,cAAc,CAAC,6BAA6B;MACnEC,OAAO,EAAEI,MAAM,CAAClD,IAAI;IACxB,CAAC,CAAC;EACN;EACA+B,OAAO,KAAKA,OAAO,GAAGnF,mBAAmB,CAAC,CAAC,CAAC;EAC5C,IAAI,CAACmF,OAAO,EAAE;IACV,MAAMF,aAAa,CAACgB,MAAM,CAAC,YAAY,CAAC,yBAAyB,CAAC;EACtE;;EACA,MAAMM,WAAW,GAAGhD,KAAK,CAACiD,GAAG,CAACpD,IAAI,CAAC;EACnC,IAAImD,WAAW,EAAE;IACb;IACA,IAAItG,SAAS,CAACkF,OAAO,EAAEoB,WAAW,CAACpB,OAAO,CAAC,IACvClF,SAAS,CAACmF,MAAM,EAAEmB,WAAW,CAACnB,MAAM,CAAC,EAAE;MACvC,OAAOmB,WAAW;IACtB,CAAC,MACI;MACD,MAAMtB,aAAa,CAACgB,MAAM,CAAC,eAAe,CAAC,8BAA8B;QAAEC,OAAO,EAAE9C;MAAK,CAAC,CAAC;IAC/F;EACJ;EACA,MAAM3C,SAAS,GAAG,IAAIf,kBAAkB,CAAC0D,IAAI,CAAC;EAC9C,KAAK,MAAM7B,SAAS,IAAIkC,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;IAC1C5D,SAAS,CAACmD,YAAY,CAACrC,SAAS,CAAC;EACrC;EACA,MAAMkF,MAAM,GAAG,IAAIvB,eAAe,CAACC,OAAO,EAAEC,MAAM,EAAE3E,SAAS,CAAC;EAC9D8C,KAAK,CAACa,GAAG,CAAChB,IAAI,EAAEqD,MAAM,CAAC;EACvB,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACtD,IAAI,GAAGC,kBAAkB,EAAE;EACvC,MAAMM,GAAG,GAAGJ,KAAK,CAACiD,GAAG,CAACpD,IAAI,CAAC;EAC3B,IAAI,CAACO,GAAG,IAAIP,IAAI,KAAKC,kBAAkB,IAAIrD,mBAAmB,CAAC,CAAC,EAAE;IAC9D,OAAOoG,aAAa,CAAC,CAAC;EAC1B;EACA,IAAI,CAACzC,GAAG,EAAE;IACN,MAAMsB,aAAa,CAACgB,MAAM,CAAC,QAAQ,CAAC,uBAAuB;MAAEC,OAAO,EAAE9C;IAAK,CAAC,CAAC;EACjF;EACA,OAAOO,GAAG;AACd;AACA;AACA;AACA;AACA;AACA,SAASgD,OAAOA,CAAA,EAAG;EACf,OAAOC,KAAK,CAACC,IAAI,CAACtD,KAAK,CAACc,MAAM,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,SAiBeyC,SAASA,CAAAC,EAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAUxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CAVA,WAAyBxD,GAAG,EAAE;IAC1B,MAAMP,IAAI,GAAGO,GAAG,CAACP,IAAI;IACrB,IAAIG,KAAK,CAACY,GAAG,CAACf,IAAI,CAAC,EAAE;MACjBG,KAAK,CAAC6D,MAAM,CAAChE,IAAI,CAAC;MAClB,MAAMiE,OAAO,CAACC,GAAG,CAAC3D,GAAG,CAAClD,SAAS,CAC1BG,YAAY,CAAC,CAAC,CACdC,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACsG,MAAM,CAAC,CAAC,CAAC,CAAC;MACxCzD,GAAG,CAACqC,SAAS,GAAG,IAAI;IACxB;EACJ,CAAC;EAAA,OAAAgB,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AASD,SAASK,eAAeA,CAACC,gBAAgB,EAAErG,OAAO,EAAEsG,OAAO,EAAE;EACzD,IAAIC,EAAE;EACN;EACA;EACA,IAAIxG,OAAO,GAAG,CAACwG,EAAE,GAAGpE,mBAAmB,CAACkE,gBAAgB,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,gBAAgB;EAC5G,IAAIC,OAAO,EAAE;IACTvG,OAAO,IAAK,IAAGuG,OAAQ,EAAC;EAC5B;EACA,MAAME,eAAe,GAAGzG,OAAO,CAAC0G,KAAK,CAAC,OAAO,CAAC;EAC9C,MAAMC,eAAe,GAAG1G,OAAO,CAACyG,KAAK,CAAC,OAAO,CAAC;EAC9C,IAAID,eAAe,IAAIE,eAAe,EAAE;IACpC,MAAMC,OAAO,GAAG,CACX,+BAA8B5G,OAAQ,mBAAkBC,OAAQ,IAAG,CACvE;IACD,IAAIwG,eAAe,EAAE;MACjBG,OAAO,CAACC,IAAI,CAAE,iBAAgB7G,OAAQ,mDAAkD,CAAC;IAC7F;IACA,IAAIyG,eAAe,IAAIE,eAAe,EAAE;MACpCC,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC;IACvB;IACA,IAAIF,eAAe,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAE,iBAAgB5G,OAAQ,mDAAkD,CAAC;IAC7F;IACAS,MAAM,CAACoG,IAAI,CAACF,OAAO,CAACxG,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B;EACJ;EACA2C,kBAAkB,CAAC,IAAIxE,SAAS,CAAE,GAAEyB,OAAQ,UAAS,EAAE,OAAO;IAAEA,OAAO;IAAEC;EAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;AAChI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8G,KAAKA,CAACC,WAAW,EAAE/C,OAAO,EAAE;EACjC,IAAI+C,WAAW,KAAK,IAAI,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IAC3D,MAAMjD,aAAa,CAACgB,MAAM,CAAC,sBAAsB,CAAC,mCAAmC,CAAC;EAC1F;;EACArG,iBAAiB,CAACsI,WAAW,EAAE/C,OAAO,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStF,WAAWA,CAACsI,QAAQ,EAAE;EAC3BrI,aAAa,CAACqI,QAAQ,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,6BAA6B;AAC7C,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,UAAU,GAAG,0BAA0B;AAC7C,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAGjI,MAAM,CAAC8H,OAAO,EAAEC,UAAU,EAAE;MACpCI,OAAO,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAK;QACzB;QACA;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACFD,EAAE,CAACE,iBAAiB,CAACN,UAAU,CAAC;QACxC;MACJ;IACJ,CAAC,CAAC,CAACO,KAAK,CAAChF,CAAC,IAAI;MACV,MAAMoB,aAAa,CAACgB,MAAM,CAAC,UAAU,CAAC,yBAAyB;QAC3D6C,oBAAoB,EAAEjF,CAAC,CAACkF;MAC5B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOR,SAAS;AACpB;AAAC,SACcS,2BAA2BA,CAAAC,GAAA;EAAA,OAAAC,4BAAA,CAAAjC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAgC,6BAAA;EAAAA,4BAAA,GAAA/B,iBAAA,CAA1C,WAA2CxD,GAAG,EAAE;IAC5C,IAAI;MACA,MAAM+E,EAAE,SAASF,YAAY,CAAC,CAAC;MAC/B,MAAMW,MAAM,SAAST,EAAE,CAClBU,WAAW,CAACd,UAAU,CAAC,CACvBe,WAAW,CAACf,UAAU,CAAC,CACvB9B,GAAG,CAAC8C,UAAU,CAAC3F,GAAG,CAAC,CAAC;MACzB,OAAOwF,MAAM;IACjB,CAAC,CACD,OAAOtF,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY3D,aAAa,EAAE;QAC5B0B,MAAM,CAACoG,IAAI,CAACnE,CAAC,CAACkF,OAAO,CAAC;MAC1B,CAAC,MACI;QACD,MAAMQ,WAAW,GAAGtE,aAAa,CAACgB,MAAM,CAAC,SAAS,CAAC,wBAAwB;UACvE6C,oBAAoB,EAAEjF,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACkF;QAClE,CAAC,CAAC;QACFnH,MAAM,CAACoG,IAAI,CAACuB,WAAW,CAACR,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC;EAAA,OAAAG,4BAAA,CAAAjC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcsC,0BAA0BA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,2BAAA,CAAA1C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAyC,4BAAA;EAAAA,2BAAA,GAAAxC,iBAAA,CAAzC,WAA0CxD,GAAG,EAAEiG,eAAe,EAAE;IAC5D,IAAI;MACA,MAAMlB,EAAE,SAASF,YAAY,CAAC,CAAC;MAC/B,MAAMqB,EAAE,GAAGnB,EAAE,CAACU,WAAW,CAACd,UAAU,EAAE,WAAW,CAAC;MAClD,MAAMe,WAAW,GAAGQ,EAAE,CAACR,WAAW,CAACf,UAAU,CAAC;MAC9C,MAAMe,WAAW,CAACS,GAAG,CAACF,eAAe,EAAEN,UAAU,CAAC3F,GAAG,CAAC,CAAC;MACvD,MAAMkG,EAAE,CAACE,IAAI;IACjB,CAAC,CACD,OAAOlG,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY3D,aAAa,EAAE;QAC5B0B,MAAM,CAACoG,IAAI,CAACnE,CAAC,CAACkF,OAAO,CAAC;MAC1B,CAAC,MACI;QACD,MAAMQ,WAAW,GAAGtE,aAAa,CAACgB,MAAM,CAAC,SAAS,CAAC,0BAA0B;UACzE6C,oBAAoB,EAAEjF,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACkF;QAClE,CAAC,CAAC;QACFnH,MAAM,CAACoG,IAAI,CAACuB,WAAW,CAACR,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC;EAAA,OAAAY,2BAAA,CAAA1C,KAAA,OAAAC,SAAA;AAAA;AACD,SAASoC,UAAUA,CAAC3F,GAAG,EAAE;EACrB,OAAQ,GAAEA,GAAG,CAACP,IAAK,IAAGO,GAAG,CAACwB,OAAO,CAAC6E,KAAM,EAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAI;AAC7B;AACA,MAAMC,qCAAqC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AACtE,MAAMC,oBAAoB,CAAC;EACvB3J,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC2J,gBAAgB,GAAG,IAAI;IAC5B,MAAMzG,GAAG,GAAG,IAAI,CAAClD,SAAS,CAAC+D,WAAW,CAAC,KAAK,CAAC,CAACvD,YAAY,CAAC,CAAC;IAC5D,IAAI,CAACoJ,QAAQ,GAAG,IAAIC,oBAAoB,CAAC3G,GAAG,CAAC;IAC7C,IAAI,CAAC4G,uBAAuB,GAAG,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAACtB,MAAM,IAAI;MAC/D,IAAI,CAACiB,gBAAgB,GAAGjB,MAAM;MAC9B,OAAOA,MAAM;IACjB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUzE,gBAAgBA,CAAA,EAAG;IAAA,IAAAgG,KAAA;IAAA,OAAAvD,iBAAA;MACrB,MAAMwD,cAAc,GAAGD,KAAI,CAACjK,SAAS,CAChC+D,WAAW,CAAC,iBAAiB,CAAC,CAC9BvD,YAAY,CAAC,CAAC;MACnB;MACA;MACA,MAAM2J,KAAK,GAAGD,cAAc,CAACjK,qBAAqB,CAAC,CAAC;MACpD,MAAMmK,IAAI,GAAGC,gBAAgB,CAAC,CAAC;MAC/B,IAAIJ,KAAI,CAACN,gBAAgB,KAAK,IAAI,EAAE;QAChCM,KAAI,CAACN,gBAAgB,SAASM,KAAI,CAACH,uBAAuB;MAC9D;MACA;MACA;MACA,IAAIG,KAAI,CAACN,gBAAgB,CAACW,qBAAqB,KAAKF,IAAI,IACpDH,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACC,IAAI,CAACC,mBAAmB,IAAIA,mBAAmB,CAACL,IAAI,KAAKA,IAAI,CAAC,EAAE;QACjG;MACJ,CAAC,MACI;QACD;QACAH,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACjD,IAAI,CAAC;UAAE8C,IAAI;UAAED;QAAM,CAAC,CAAC;MAC1D;MACA;MACAF,KAAI,CAACN,gBAAgB,CAACY,UAAU,GAAGN,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAAC5J,MAAM,CAAC8J,mBAAmB,IAAI;QAC9F,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAACF,mBAAmB,CAACL,IAAI,CAAC,CAACQ,OAAO,CAAC,CAAC;QAChE,MAAMC,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC,CAAC;QACtB,OAAOA,GAAG,GAAGH,WAAW,IAAIjB,qCAAqC;MACrE,CAAC,CAAC;MACF,OAAOQ,KAAI,CAACL,QAAQ,CAACkB,SAAS,CAACb,KAAI,CAACN,gBAAgB,CAAC;IAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUoB,mBAAmBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAtE,iBAAA;MACxB,IAAIsE,MAAI,CAACrB,gBAAgB,KAAK,IAAI,EAAE;QAChC,MAAMqB,MAAI,CAAClB,uBAAuB;MACtC;MACA;MACA,IAAIkB,MAAI,CAACrB,gBAAgB,KAAK,IAAI,IAC9BqB,MAAI,CAACrB,gBAAgB,CAACY,UAAU,CAACU,MAAM,KAAK,CAAC,EAAE;QAC/C,OAAO,EAAE;MACb;MACA,MAAMb,IAAI,GAAGC,gBAAgB,CAAC,CAAC;MAC/B;MACA,MAAM;QAAEa,gBAAgB;QAAEC;MAAc,CAAC,GAAGC,0BAA0B,CAACJ,MAAI,CAACrB,gBAAgB,CAACY,UAAU,CAAC;MACxG,MAAMc,YAAY,GAAG3L,6BAA6B,CAAC4L,IAAI,CAACC,SAAS,CAAC;QAAE7K,OAAO,EAAE,CAAC;QAAE6J,UAAU,EAAEW;MAAiB,CAAC,CAAC,CAAC;MAChH;MACAF,MAAI,CAACrB,gBAAgB,CAACW,qBAAqB,GAAGF,IAAI;MAClD,IAAIe,aAAa,CAACF,MAAM,GAAG,CAAC,EAAE;QAC1B;QACAD,MAAI,CAACrB,gBAAgB,CAACY,UAAU,GAAGY,aAAa;QAChD;QACA;QACA;QACA,MAAMH,MAAI,CAACpB,QAAQ,CAACkB,SAAS,CAACE,MAAI,CAACrB,gBAAgB,CAAC;MACxD,CAAC,MACI;QACDqB,MAAI,CAACrB,gBAAgB,CAACY,UAAU,GAAG,EAAE;QACrC;QACA,KAAKS,MAAI,CAACpB,QAAQ,CAACkB,SAAS,CAACE,MAAI,CAACrB,gBAAgB,CAAC;MACvD;MACA,OAAO0B,YAAY;IAAC;EACxB;AACJ;AACA,SAAShB,gBAAgBA,CAAA,EAAG;EACxB,MAAMmB,KAAK,GAAG,IAAIb,IAAI,CAAC,CAAC;EACxB;EACA,OAAOa,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AAC/C;AACA,SAASN,0BAA0BA,CAACO,eAAe,EAAEC,OAAO,GAAGpC,gBAAgB,EAAE;EAC7E;EACA;EACA,MAAM0B,gBAAgB,GAAG,EAAE;EAC3B;EACA,IAAIC,aAAa,GAAGQ,eAAe,CAACE,KAAK,CAAC,CAAC;EAC3C,KAAK,MAAMpB,mBAAmB,IAAIkB,eAAe,EAAE;IAC/C;IACA,MAAMG,cAAc,GAAGZ,gBAAgB,CAACa,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC7B,KAAK,KAAKM,mBAAmB,CAACN,KAAK,CAAC;IAC1F,IAAI,CAAC2B,cAAc,EAAE;MACjB;MACAZ,gBAAgB,CAAC5D,IAAI,CAAC;QAClB6C,KAAK,EAAEM,mBAAmB,CAACN,KAAK;QAChC8B,KAAK,EAAE,CAACxB,mBAAmB,CAACL,IAAI;MACpC,CAAC,CAAC;MACF,IAAI8B,UAAU,CAAChB,gBAAgB,CAAC,GAAGU,OAAO,EAAE;QACxC;QACA;QACAV,gBAAgB,CAACiB,GAAG,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC,MACI;MACDL,cAAc,CAACG,KAAK,CAAC3E,IAAI,CAACmD,mBAAmB,CAACL,IAAI,CAAC;MACnD;MACA;MACA,IAAI8B,UAAU,CAAChB,gBAAgB,CAAC,GAAGU,OAAO,EAAE;QACxCE,cAAc,CAACG,KAAK,CAACE,GAAG,CAAC,CAAC;QAC1B;MACJ;IACJ;IACA;IACA;IACAhB,aAAa,GAAGA,aAAa,CAACU,KAAK,CAAC,CAAC,CAAC;EAC1C;EACA,OAAO;IACHX,gBAAgB;IAChBC;EACJ,CAAC;AACL;AACA,MAAMtB,oBAAoB,CAAC;EACvB9J,WAAWA,CAACmD,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkJ,uBAAuB,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACtE;EACMA,4BAA4BA,CAAA,EAAG;IAAA,OAAA3F,iBAAA;MACjC,IAAI,CAAC/G,oBAAoB,CAAC,CAAC,EAAE;QACzB,OAAO,KAAK;MAChB,CAAC,MACI;QACD,OAAOC,yBAAyB,CAAC,CAAC,CAC7BoK,IAAI,CAAC,MAAM,IAAI,CAAC,CAChB5B,KAAK,CAAC,MAAM,KAAK,CAAC;MAC3B;IAAC;EACL;EACA;AACJ;AACA;EACU2B,IAAIA,CAAA,EAAG;IAAA,IAAAuC,MAAA;IAAA,OAAA5F,iBAAA;MACT,MAAM6F,eAAe,SAASD,MAAI,CAACF,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB,OAAO;UAAEhC,UAAU,EAAE;QAAG,CAAC;MAC7B,CAAC,MACI;QACD,MAAMiC,kBAAkB,SAASjE,2BAA2B,CAAC+D,MAAI,CAACpJ,GAAG,CAAC;QACtE,OAAOsJ,kBAAkB,IAAI;UAAEjC,UAAU,EAAE;QAAG,CAAC;MACnD;IAAC;EACL;EACA;EACMO,SAASA,CAAC2B,gBAAgB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAhG,iBAAA;MAC9B,IAAIO,EAAE;MACN,MAAMsF,eAAe,SAASG,MAAI,CAACN,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB;MACJ,CAAC,MACI;QACD,MAAMI,wBAAwB,SAASD,MAAI,CAAC3C,IAAI,CAAC,CAAC;QAClD,OAAOhB,0BAA0B,CAAC2D,MAAI,CAACxJ,GAAG,EAAE;UACxCoH,qBAAqB,EAAE,CAACrD,EAAE,GAAGwF,gBAAgB,CAACnC,qBAAqB,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0F,wBAAwB,CAACrC,qBAAqB;UACpJC,UAAU,EAAEkC,gBAAgB,CAAClC;QACjC,CAAC,CAAC;MACN;IAAC;EACL;EACA;EACMqC,GAAGA,CAACH,gBAAgB,EAAE;IAAA,IAAAI,MAAA;IAAA,OAAAnG,iBAAA;MACxB,IAAIO,EAAE;MACN,MAAMsF,eAAe,SAASM,MAAI,CAACT,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB;MACJ,CAAC,MACI;QACD,MAAMI,wBAAwB,SAASE,MAAI,CAAC9C,IAAI,CAAC,CAAC;QAClD,OAAOhB,0BAA0B,CAAC8D,MAAI,CAAC3J,GAAG,EAAE;UACxCoH,qBAAqB,EAAE,CAACrD,EAAE,GAAGwF,gBAAgB,CAACnC,qBAAqB,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0F,wBAAwB,CAACrC,qBAAqB;UACpJC,UAAU,EAAE,CACR,GAAGoC,wBAAwB,CAACpC,UAAU,EACtC,GAAGkC,gBAAgB,CAAClC,UAAU;QAEtC,CAAC,CAAC;MACN;IAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,UAAUA,CAACP,eAAe,EAAE;EACjC;EACA,OAAOjM,6BAA6B;EACpC;EACA4L,IAAI,CAACC,SAAS,CAAC;IAAE7K,OAAO,EAAE,CAAC;IAAE6J,UAAU,EAAEoB;EAAgB,CAAC,CAAC,CAAC,CAACV,MAAM;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6B,sBAAsBA,CAAC9F,OAAO,EAAE;EACrCxD,kBAAkB,CAAC,IAAIxE,SAAS,CAAC,iBAAiB,EAAEgB,SAAS,IAAI,IAAIF,yBAAyB,CAACE,SAAS,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EAClJwD,kBAAkB,CAAC,IAAIxE,SAAS,CAAC,WAAW,EAAEgB,SAAS,IAAI,IAAI0J,oBAAoB,CAAC1J,SAAS,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EACvI;EACA8G,eAAe,CAAC7F,MAAM,EAAEC,SAAS,EAAE8F,OAAO,CAAC;EAC3C;EACAF,eAAe,CAAC7F,MAAM,EAAEC,SAAS,EAAE,SAAS,CAAC;EAC7C;EACA4F,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAgG,sBAAsB,CAAC,EAAE,CAAC;AAE1B,SAASpH,WAAW,EAAE9C,kBAAkB,IAAImK,mBAAmB,EAAE9J,aAAa,EAAEK,wBAAwB,EAAER,KAAK,EAAEuB,gBAAgB,EAAErB,WAAW,EAAEa,YAAY,EAAEL,kBAAkB,EAAEU,sBAAsB,EAAEmC,SAAS,EAAEJ,MAAM,EAAEC,OAAO,EAAEP,aAAa,EAAE6B,KAAK,EAAEV,eAAe,EAAE1H,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
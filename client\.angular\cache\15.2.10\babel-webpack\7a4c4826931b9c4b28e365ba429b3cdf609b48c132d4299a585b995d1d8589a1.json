{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BackButtonDirective } from './back-button.directive';\nimport { TeamDetailsDirective } from './team-details/team-details.directive';\nimport { LongPressDirective } from './longpress/longpress.directive';\nimport * as i0 from \"@angular/core\";\nexport class HostListenersModule {\n  static #_ = this.ɵfac = function HostListenersModule_Factory(t) {\n    return new (t || HostListenersModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HostListenersModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HostListenersModule, {\n    declarations: [BackButtonDirective, TeamDetailsDirective, LongPressDirective],\n    imports: [CommonModule],\n    exports: [BackButtonDirective, TeamDetailsDirective, LongPressDirective]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,kBAAkB,QAAQ,iCAAiC;;AAOpE,OAAM,MAAOC,mBAAmB;EAAA,QAAAC,CAAA;qBAAnBD,mBAAmB;EAAA;EAAA,QAAAE,EAAA;UAAnBF;EAAmB;EAAA,QAAAG,EAAA;cAHpBP,YAAY;EAAA;;;2EAGXI,mBAAmB;IAAAI,YAAA,GAJfP,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB;IAAAM,OAAA,GAClET,YAAY;IAAAU,OAAA,GACZT,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB;EAAA;AAAA", "names": ["CommonModule", "BackButtonDirective", "TeamDetailsDirective", "LongPressDirective", "HostListenersModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\hostlisteners\\host-listeners.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { BackButtonDirective } from './back-button.directive';\r\nimport { TeamDetailsDirective } from './team-details/team-details.directive';\r\nimport { LongPressDirective } from './longpress/longpress.directive';\r\n\r\n@NgModule({\r\n  declarations: [BackButtonDirective, TeamDetailsDirective, LongPressDirective],\r\n  imports: [CommonModule],\r\n  exports: [BackButtonDirective, TeamDetailsDirective, LongPressDirective],\r\n})\r\nexport class HostListenersModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ContentHeaderModule } from './../../layout/components/content-header/content-header.module';\nimport { EventsComponent } from './events/events.component';\nimport { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { DataTablesModule } from 'angular-datatables';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ClubsComponent } from './clubs/clubs.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { UsersComponent } from './users/users.component';\nimport { RolesComponent } from './users/role-permissions/roles/roles.component';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { FormsModule } from '@angular/forms';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { ManageClubComponent } from './clubs/manage-club/manage-club.component';\nimport { FormlyFieldButton } from 'app/pages/tables/clubs/manage-club/input-button-type.component';\nimport { LocationsComponent } from './locations/locations.component';\nimport { PlayerUpdatesComponent } from './player-updates/player-updates.component';\nimport { ModalProcessRequestComponent } from './player-updates/modal-process-request/modal-process-request.component';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\n// routing\nconst routes = [{\n  path: 'events',\n  component: EventsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_events\n  }\n}, {\n  path: 'clubs',\n  component: ClubsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_clubs\n  }\n}, {\n  path: 'locations',\n  component: LocationsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_locations\n  }\n}, {\n  path: 'events/:seasonId/groups',\n  component: GroupsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_groups\n  }\n}, {\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_users\n  }\n}, {\n  path: 'player-updates',\n  component: PlayerUpdatesComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_users\n  }\n}];\nFullCalendarModule;\nexport class TablesModule {\n  static #_ = this.ɵfac = function TablesModule_Factory(t) {\n    return new (t || TablesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TablesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [CorePipesModule],\n    imports: [CommonModule, RouterModule.forChild(routes), EditorSidebarModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, ContentHeaderModule, TranslateModule, CoreCommonModule, NgbDropdownModule, BtnDropdownActionModule, ErrorMessageModule, FullCalendarModule, FormlyModule.forRoot({\n      types: [{\n        name: 'inputButton',\n        component: FormlyFieldButton,\n        wrappers: ['form-field'],\n        defaultOptions: {\n          props: {\n            type: 'button'\n          }\n        }\n      }],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    }), FormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TablesModule, {\n    declarations: [EventsComponent, ClubsComponent, GroupsComponent, UsersComponent, RolesComponent, ManageClubComponent, FormlyFieldButton, LocationsComponent, PlayerUpdatesComponent, ModalProcessRequestComponent],\n    imports: [CommonModule, i1.RouterModule, EditorSidebarModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, ContentHeaderModule, TranslateModule, CoreCommonModule, NgbDropdownModule, BtnDropdownActionModule, ErrorMessageModule, FullCalendarModule, i2.FormlyModule, FormsModule],\n    exports: [EventsComponent, ClubsComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,mBAAmB,QAAQ,gEAAgE;AACpG,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAAiBC,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,kBAAkB,QAAQ,uBAAuB;AAK1D,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,qDAAqD;AAC5D,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,gDAAgD;AAC/E,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,iBAAiB,QAAQ,gEAAgE;AAClG,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,4BAA4B,QAAQ,wEAAwE;AACrH,SAASC,eAAe,QAAQ,0BAA0B;;;;AAE1D;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE7B,eAAe;EAC1B8B,WAAW,EAAE,CAACX,gBAAgB,CAAC;EAC/BY,IAAI,EAAE;IAAEC,WAAW,EAAEZ,SAAS,CAACa,WAAW,CAACC;EAAa;CACzD,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEnB,cAAc;EACzBoB,WAAW,EAAE,CAACX,gBAAgB,CAAC;EAC/BY,IAAI,EAAE;IAAEC,WAAW,EAAEZ,SAAS,CAACa,WAAW,CAACE;EAAY;CACxD,EACD;EACEP,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEN,kBAAkB;EAC7BO,WAAW,EAAE,CAACX,gBAAgB,CAAC;EAC/BY,IAAI,EAAE;IAAEC,WAAW,EAAEZ,SAAS,CAACa,WAAW,CAACG;EAAgB;CAC5D,EACD;EACER,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAElB,eAAe;EAC1BmB,WAAW,EAAE,CAACX,gBAAgB,CAAC;EAC/BY,IAAI,EAAE;IAAEC,WAAW,EAAEZ,SAAS,CAACa,WAAW,CAACI;EAAa;CACzD,EACD;EACET,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEf,cAAc;EACzBgB,WAAW,EAAE,CAACX,gBAAgB,CAAC;EAC/BY,IAAI,EAAE;IAAEC,WAAW,EAAEZ,SAAS,CAACa,WAAW,CAACK;EAAY;CACxD,EACD;EACEV,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEL,sBAAsB;EACjCM,WAAW,EAAE,CAACX,gBAAgB,CAAC;EAC/BY,IAAI,EAAE;IAAEC,WAAW,EAAEZ,SAAS,CAACa,WAAW,CAACK;EAAY;CACxD,CAEF;AAEDnC,kBAAkB;AAmDlB,OAAM,MAAOoC,YAAY;EAAA,QAAAC,CAAA;qBAAZD,YAAY;EAAA;EAAA,QAAAE,EAAA;UAAZF;EAAY;EAAA,QAAAG,EAAA;eAHZ,CAAChB,eAAe,CAAC;IAAAiB,OAAA,GAhC1BzC,YAAY,EACZD,YAAY,CAAC2C,QAAQ,CAACjB,MAAM,CAAC,EAC7BvB,mBAAmB,EACnBE,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBT,mBAAmB,EACnBU,eAAe,EACfF,gBAAgB,EAChBK,iBAAiB,EACjBC,uBAAuB,EACvBK,kBAAkB,EAClBf,kBAAkB,EAClBa,YAAY,CAAC6B,OAAO,CAAC;MACnBC,KAAK,EAAE,CACL;QACEC,IAAI,EAAE,aAAa;QACnBlB,SAAS,EAAEP,iBAAiB;QAC5B0B,QAAQ,EAAE,CAAC,YAAY,CAAC;QACxBC,cAAc,EAAE;UACdC,KAAK,EAAE;YACLC,IAAI,EAAE;;;OAGX,CACF;MACDC,kBAAkB,EAAE,CAClB;QAAEL,IAAI,EAAE,aAAa;QAAEM,OAAO,EAAEhD;MAAuB,CAAE;KAE5D,CAAC,EACFY,WAAW;EAAA;;;2EAKFsB,YAAY;IAAAe,YAAA,GA/CrBtD,eAAe,EACfU,cAAc,EACdC,eAAe,EACfG,cAAc,EACdC,cAAc,EACdM,mBAAmB,EACnBC,iBAAiB,EACjBC,kBAAkB,EAClBC,sBAAsB,EACtBC,4BAA4B;IAAAkB,OAAA,GAG5BzC,YAAY,EAAAqD,EAAA,CAAAtD,YAAA,EAEZG,mBAAmB,EACnBE,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBT,mBAAmB,EACnBU,eAAe,EACfF,gBAAgB,EAChBK,iBAAiB,EACjBC,uBAAuB,EACvBK,kBAAkB,EAClBf,kBAAkB,EAAAqD,EAAA,CAAAxC,YAAA,EAkBlBC,WAAW;IAAAwC,OAAA,GAGHzD,eAAe,EAAEU,cAAc;EAAA;AAAA", "names": ["ContentHeaderModule", "EventsComponent", "RouterModule", "CommonModule", "FullCalendarModule", "EditorSidebarModule", "serverValidationMessage", "CoreSidebarModule", "CoreCommonModule", "DataTablesModule", "TranslateModule", "ClubsComponent", "GroupsComponent", "NgbDropdownModule", "BtnDropdownActionModule", "UsersComponent", "RolesComponent", "FormlyModule", "FormsModule", "ErrorMessageModule", "PermissionsGuard", "AppConfig", "ManageClubComponent", "FormlyFieldButton", "LocationsComponent", "PlayerUpdatesComponent", "ModalProcessRequestComponent", "CorePipesModule", "routes", "path", "component", "canActivate", "data", "permissions", "PERMISSIONS", "manage_events", "manage_clubs", "manage_locations", "manage_groups", "manage_users", "TablesModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "types", "name", "wrappers", "defaultOptions", "props", "type", "validationMessages", "message", "declarations", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\tables.module.ts"], "sourcesContent": ["import { ContentHeaderModule } from './../../layout/components/content-header/content-header.module';\r\nimport { EventsComponent } from './events/events.component';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { FullCalendarModule } from '@fullcalendar/angular';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport {\r\n  EditorSidebarModule,\r\n  serverValidationMessage,\r\n} from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { ClubsComponent } from './clubs/clubs.component';\r\nimport { GroupsComponent } from './groups/groups.component';\r\nimport { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { UsersComponent } from './users/users.component';\r\nimport { RolesComponent } from './users/role-permissions/roles/roles.component';\r\nimport { FormlyModule } from '@ngx-formly/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { ManageClubComponent } from './clubs/manage-club/manage-club.component';\r\nimport { FormlyFieldButton } from 'app/pages/tables/clubs/manage-club/input-button-type.component';\r\nimport { LocationsComponent } from './locations/locations.component';\r\nimport { PlayerUpdatesComponent } from './player-updates/player-updates.component';\r\nimport { ModalProcessRequestComponent } from './player-updates/modal-process-request/modal-process-request.component';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\n\r\n// routing\r\nconst routes: Routes = [\r\n  {\r\n    path: 'events',\r\n    component: EventsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_events },\r\n  },\r\n  {\r\n    path: 'clubs',\r\n    component: ClubsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_clubs },\r\n  },\r\n  {\r\n    path: 'locations',\r\n    component: LocationsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_locations },\r\n  },\r\n  {\r\n    path: 'events/:seasonId/groups',\r\n    component: GroupsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_groups },\r\n  },\r\n  {\r\n    path: 'users',\r\n    component: UsersComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_users },\r\n  },\r\n  {\r\n    path: 'player-updates',\r\n    component: PlayerUpdatesComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_users },\r\n  },\r\n\r\n];\r\n\r\nFullCalendarModule\r\n\r\n@NgModule({\r\n  declarations: [\r\n    EventsComponent,\r\n    ClubsComponent,\r\n    GroupsComponent,\r\n    UsersComponent,\r\n    RolesComponent,\r\n    ManageClubComponent,\r\n    FormlyFieldButton,\r\n    LocationsComponent,\r\n    PlayerUpdatesComponent,\r\n    ModalProcessRequestComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    EditorSidebarModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    DataTablesModule,\r\n    ContentHeaderModule,\r\n    TranslateModule,\r\n    CoreCommonModule,\r\n    NgbDropdownModule,\r\n    BtnDropdownActionModule,\r\n    ErrorMessageModule,\r\n    FullCalendarModule,\r\n    FormlyModule.forRoot({\r\n      types: [\r\n        {\r\n          name: 'inputButton',\r\n          component: FormlyFieldButton,\r\n          wrappers: ['form-field'],\r\n          defaultOptions: {\r\n            props: {\r\n              type: 'button',\r\n            },\r\n          },\r\n        },\r\n      ],\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage },\r\n      ],\r\n    }),\r\n    FormsModule,\r\n  ],\r\n  providers: [CorePipesModule],\r\n  exports: [EventsComponent, ClubsComponent],\r\n})\r\nexport class TablesModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bengali [bn]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/kaushikgandhi\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '১',\n      2: '২',\n      3: '৩',\n      4: '৪',\n      5: '৫',\n      6: '৬',\n      7: '৭',\n      8: '৮',\n      9: '৯',\n      0: '০'\n    },\n    numberMap = {\n      '১': '1',\n      '২': '2',\n      '৩': '3',\n      '৪': '4',\n      '৫': '5',\n      '৬': '6',\n      '৭': '7',\n      '৮': '8',\n      '৯': '9',\n      '০': '0'\n    };\n  var bn = moment.defineLocale('bn', {\n    months: 'জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর'.split('_'),\n    monthsShort: 'জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে'.split('_'),\n    weekdays: 'রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার'.split('_'),\n    weekdaysShort: 'রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি'.split('_'),\n    weekdaysMin: 'রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm সময়',\n      LTS: 'A h:mm:ss সময়',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm সময়',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm সময়'\n    },\n    calendar: {\n      sameDay: '[আজ] LT',\n      nextDay: '[আগামীকাল] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[গতকাল] LT',\n      lastWeek: '[গত] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s পরে',\n      past: '%s আগে',\n      s: 'কয়েক সেকেন্ড',\n      ss: '%d সেকেন্ড',\n      m: 'এক মিনিট',\n      mm: '%d মিনিট',\n      h: 'এক ঘন্টা',\n      hh: '%d ঘন্টা',\n      d: 'এক দিন',\n      dd: '%d দিন',\n      M: 'এক মাস',\n      MM: '%d মাস',\n      y: 'এক বছর',\n      yy: '%d বছর'\n    },\n    preparse: function (string) {\n      return string.replace(/[১২৩৪৫৬৭৮৯০]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    meridiemParse: /রাত|সকাল|দুপুর|বিকাল|রাত/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'রাত' && hour >= 4 || meridiem === 'দুপুর' && hour < 5 || meridiem === 'বিকাল') {\n        return hour + 12;\n      } else {\n        return hour;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'রাত';\n      } else if (hour < 10) {\n        return 'সকাল';\n      } else if (hour < 17) {\n        return 'দুপুর';\n      } else if (hour < 20) {\n        return 'বিকাল';\n      } else {\n        return 'রাত';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n\n  return bn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "bn", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "week", "dow", "doy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/moment/locale/bn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Bengali [bn]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/kaushikgandhi\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '১',\n            2: '২',\n            3: '৩',\n            4: '৪',\n            5: '৫',\n            6: '৬',\n            7: '৭',\n            8: '৮',\n            9: '৯',\n            0: '০',\n        },\n        numberMap = {\n            '১': '1',\n            '২': '2',\n            '৩': '3',\n            '৪': '4',\n            '৫': '5',\n            '৬': '6',\n            '৭': '7',\n            '৮': '8',\n            '৯': '9',\n            '০': '0',\n        };\n\n    var bn = moment.defineLocale('bn', {\n        months: 'জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর'.split(\n            '_'\n        ),\n        monthsShort:\n            'জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে'.split(\n                '_'\n            ),\n        weekdays: 'রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার'.split(\n            '_'\n        ),\n        weekdaysShort: 'রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি'.split('_'),\n        weekdaysMin: 'রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm সময়',\n            LTS: 'A h:mm:ss সময়',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm সময়',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm সময়',\n        },\n        calendar: {\n            sameDay: '[আজ] LT',\n            nextDay: '[আগামীকাল] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[গতকাল] LT',\n            lastWeek: '[গত] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s পরে',\n            past: '%s আগে',\n            s: 'কয়েক সেকেন্ড',\n            ss: '%d সেকেন্ড',\n            m: 'এক মিনিট',\n            mm: '%d মিনিট',\n            h: 'এক ঘন্টা',\n            hh: '%d ঘন্টা',\n            d: 'এক দিন',\n            dd: '%d দিন',\n            M: 'এক মাস',\n            MM: '%d মাস',\n            y: 'এক বছর',\n            yy: '%d বছর',\n        },\n        preparse: function (string) {\n            return string.replace(/[১২৩৪৫৬৭৮৯০]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        meridiemParse: /রাত|সকাল|দুপুর|বিকাল|রাত/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (\n                (meridiem === 'রাত' && hour >= 4) ||\n                (meridiem === 'দুপুর' && hour < 5) ||\n                meridiem === 'বিকাল'\n            ) {\n                return hour + 12;\n            } else {\n                return hour;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'রাত';\n            } else if (hour < 10) {\n                return 'সকাল';\n            } else if (hour < 17) {\n                return 'দুপুর';\n            } else if (hour < 20) {\n                return 'বিকাল';\n            } else {\n                return 'রাত';\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return bn;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,yFAAyF,CAACC,KAAK,CACnG,GACJ,CAAC;IACDC,WAAW,EACP,kEAAkE,CAACD,KAAK,CACpE,GACJ,CAAC;IACLE,QAAQ,EAAE,2DAA2D,CAACF,KAAK,CACvE,GACJ,CAAC;IACDG,aAAa,EAAE,sCAAsC,CAACH,KAAK,CAAC,GAAG,CAAC;IAChEI,WAAW,EAAE,iCAAiC,CAACJ,KAAK,CAAC,GAAG,CAAC;IACzDK,cAAc,EAAE;MACZC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,eAAe;MACpBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,eAAe;MACzBC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAOzC,SAAS,CAACyC,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAO1C,SAAS,CAAC0C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDE,aAAa,EAAE,0BAA0B;IACzCC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IACKC,QAAQ,KAAK,KAAK,IAAID,IAAI,IAAI,CAAC,IAC/BC,QAAQ,KAAK,OAAO,IAAID,IAAI,GAAG,CAAE,IAClCC,QAAQ,KAAK,OAAO,EACtB;QACE,OAAOD,IAAI,GAAG,EAAE;MACpB,CAAC,MAAM;QACH,OAAOA,IAAI;MACf;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,KAAK;MAChB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ,CAAC;IACDI,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOlD,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
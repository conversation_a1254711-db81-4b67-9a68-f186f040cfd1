{"ast": null, "code": "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nexport function scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    } else if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    } else if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    } else if (isIterable(input) || typeof input === 'string') {\n      return scheduleIterable(input, scheduler);\n    }\n  }\n  throw new TypeError((input !== null && typeof input || input) + ' is not observable');\n}", "map": {"version": 3, "names": ["scheduleObservable", "schedulePromise", "scheduleArray", "scheduleIterable", "isInteropObservable", "isPromise", "isArrayLike", "isIterable", "scheduled", "input", "scheduler", "TypeError"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduled/scheduled.js"], "sourcesContent": ["import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nexport function scheduled(input, scheduler) {\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return scheduleObservable(input, scheduler);\n        }\n        else if (isPromise(input)) {\n            return schedulePromise(input, scheduler);\n        }\n        else if (isArrayLike(input)) {\n            return scheduleArray(input, scheduler);\n        }\n        else if (isIterable(input) || typeof input === 'string') {\n            return scheduleIterable(input, scheduler);\n        }\n    }\n    throw new TypeError((input !== null && typeof input || input) + ' is not observable');\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxC,IAAID,KAAK,IAAI,IAAI,EAAE;IACf,IAAIL,mBAAmB,CAACK,KAAK,CAAC,EAAE;MAC5B,OAAOT,kBAAkB,CAACS,KAAK,EAAEC,SAAS,CAAC;IAC/C,CAAC,MACI,IAAIL,SAAS,CAACI,KAAK,CAAC,EAAE;MACvB,OAAOR,eAAe,CAACQ,KAAK,EAAEC,SAAS,CAAC;IAC5C,CAAC,MACI,IAAIJ,WAAW,CAACG,KAAK,CAAC,EAAE;MACzB,OAAOP,aAAa,CAACO,KAAK,EAAEC,SAAS,CAAC;IAC1C,CAAC,MACI,IAAIH,UAAU,CAACE,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACrD,OAAON,gBAAgB,CAACM,KAAK,EAAEC,SAAS,CAAC;IAC7C;EACJ;EACA,MAAM,IAAIC,SAAS,CAAC,CAACF,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,IAAIA,KAAK,IAAI,oBAAoB,CAAC;AACzF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
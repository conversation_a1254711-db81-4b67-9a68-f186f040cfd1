{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { map } from './map';\nexport function timestamp(scheduler = async) {\n  return map(value => new Timestamp(value, scheduler.now()));\n}\nexport class Timestamp {\n  constructor(value, timestamp) {\n    this.value = value;\n    this.timestamp = timestamp;\n  }\n}", "map": {"version": 3, "names": ["async", "map", "timestamp", "scheduler", "value", "Timestamp", "now", "constructor"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/timestamp.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { map } from './map';\nexport function timestamp(scheduler = async) {\n    return map((value) => new Timestamp(value, scheduler.now()));\n}\nexport class Timestamp {\n    constructor(value, timestamp) {\n        this.value = value;\n        this.timestamp = timestamp;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,SAASA,CAACC,SAAS,GAAGH,KAAK,EAAE;EACzC,OAAOC,GAAG,CAAEG,KAAK,IAAK,IAAIC,SAAS,CAACD,KAAK,EAAED,SAAS,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChE;AACA,OAAO,MAAMD,SAAS,CAAC;EACnBE,WAAWA,CAACH,KAAK,EAAEF,SAAS,EAAE;IAC1B,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CoreMenuModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { HorizontalMenuComponent } from 'app/layout/components/menu/horizontal-menu/horizontal-menu.component';\nimport * as i0 from \"@angular/core\";\nexport class HorizontalMenuModule {\n  static #_ = this.ɵfac = function HorizontalMenuModule_Factory(t) {\n    return new (t || HorizontalMenuModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HorizontalMenuModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CoreMenuModule, CoreCommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HorizontalMenuModule, {\n    declarations: [HorizontalMenuComponent],\n    imports: [CoreMenuModule, CoreCommonModule],\n    exports: [HorizontalMenuComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAEA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,SAASC,uBAAuB,QAAQ,sEAAsE;;AAO9G,OAAM,MAAOC,oBAAoB;EAAA,QAAAC,CAAA;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA;UAApBF;EAAoB;EAAA,QAAAG,EAAA;cAHrBN,cAAc,EAAEC,gBAAgB;EAAA;;;2EAG/BE,oBAAoB;IAAAI,YAAA,GAJhBL,uBAAuB;IAAAM,OAAA,GAC5BR,cAAc,EAAEC,gBAAgB;IAAAQ,OAAA,GAChCP,uBAAuB;EAAA;AAAA", "names": ["CoreMenuModule", "CoreCommonModule", "HorizontalMenuComponent", "HorizontalMenuModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\menu\\horizontal-menu\\horizontal-menu.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { CoreMenuModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\n\r\nimport { HorizontalMenuComponent } from 'app/layout/components/menu/horizontal-menu/horizontal-menu.component';\r\n\r\n@NgModule({\r\n  declarations: [HorizontalMenuComponent],\r\n  imports: [CoreMenuModule, CoreCommonModule],\r\n  exports: [HorizontalMenuComponent]\r\n})\r\nexport class HorizontalMenuModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
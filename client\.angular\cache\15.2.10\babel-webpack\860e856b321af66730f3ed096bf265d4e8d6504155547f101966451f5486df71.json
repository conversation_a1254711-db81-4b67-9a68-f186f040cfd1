{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function skipWhile(predicate) {\n  return source => source.lift(new Skip<PERSON>hileOperator(predicate));\n}\nclass Skip<PERSON>hileOperator {\n  constructor(predicate) {\n    this.predicate = predicate;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new SkipWhileSubscriber(subscriber, this.predicate));\n  }\n}\nclass Skip<PERSON>hileSubscriber extends Subscriber {\n  constructor(destination, predicate) {\n    super(destination);\n    this.predicate = predicate;\n    this.skipping = true;\n    this.index = 0;\n  }\n  _next(value) {\n    const destination = this.destination;\n    if (this.skipping) {\n      this.tryCallPredicate(value);\n    }\n    if (!this.skipping) {\n      destination.next(value);\n    }\n  }\n  tryCallPredicate(value) {\n    try {\n      const result = this.predicate(value, this.index++);\n      this.skipping = Boolean(result);\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "source", "lift", "SkipWhileOperator", "constructor", "call", "subscriber", "subscribe", "SkipWhileSubscriber", "destination", "skipping", "index", "_next", "value", "tryCallPredicate", "next", "result", "Boolean", "err", "error"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/skipWhile.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function skipWhile(predicate) {\n    return (source) => source.lift(new Skip<PERSON>hileOperator(predicate));\n}\nclass SkipWhileOperator {\n    constructor(predicate) {\n        this.predicate = predicate;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SkipWhileSubscriber(subscriber, this.predicate));\n    }\n}\nclass SkipWhileSubscriber extends Subscriber {\n    constructor(destination, predicate) {\n        super(destination);\n        this.predicate = predicate;\n        this.skipping = true;\n        this.index = 0;\n    }\n    _next(value) {\n        const destination = this.destination;\n        if (this.skipping) {\n            this.tryCallPredicate(value);\n        }\n        if (!this.skipping) {\n            destination.next(value);\n        }\n    }\n    tryCallPredicate(value) {\n        try {\n            const result = this.predicate(value, this.index++);\n            this.skipping = Boolean(result);\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,SAASA,CAACC,SAAS,EAAE;EACjC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACH,SAAS,CAAC,CAAC;AACpE;AACA,MAAMG,iBAAiB,CAAC;EACpBC,WAAWA,CAACJ,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACN,SAAS,CAAC,CAAC;EAChF;AACJ;AACA,MAAMQ,mBAAmB,SAASV,UAAU,CAAC;EACzCM,WAAWA,CAACK,WAAW,EAAET,SAAS,EAAE;IAChC,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACT,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACU,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,MAAMJ,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACI,gBAAgB,CAACD,KAAK,CAAC;IAChC;IACA,IAAI,CAAC,IAAI,CAACH,QAAQ,EAAE;MAChBD,WAAW,CAACM,IAAI,CAACF,KAAK,CAAC;IAC3B;EACJ;EACAC,gBAAgBA,CAACD,KAAK,EAAE;IACpB,IAAI;MACA,MAAMG,MAAM,GAAG,IAAI,CAAChB,SAAS,CAACa,KAAK,EAAE,IAAI,CAACF,KAAK,EAAE,CAAC;MAClD,IAAI,CAACD,QAAQ,GAAGO,OAAO,CAACD,MAAM,CAAC;IACnC,CAAC,CACD,OAAOE,GAAG,EAAE;MACR,IAAI,CAACT,WAAW,CAACU,KAAK,CAACD,GAAG,CAAC;IAC/B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
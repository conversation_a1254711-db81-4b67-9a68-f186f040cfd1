{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { fromArray } from './fromArray';\nconst NONE = {};\nexport function combineLatest(...observables) {\n  let resultSelector = null;\n  let scheduler = null;\n  if (isScheduler(observables[observables.length - 1])) {\n    scheduler = observables.pop();\n  }\n  if (typeof observables[observables.length - 1] === 'function') {\n    resultSelector = observables.pop();\n  }\n  if (observables.length === 1 && isArray(observables[0])) {\n    observables = observables[0];\n  }\n  return fromArray(observables, scheduler).lift(new CombineLatestOperator(resultSelector));\n}\nexport class CombineLatestOperator {\n  constructor(resultSelector) {\n    this.resultSelector = resultSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new CombineLatestSubscriber(subscriber, this.resultSelector));\n  }\n}\nexport class CombineLatestSubscriber extends OuterSubscriber {\n  constructor(destination, resultSelector) {\n    super(destination);\n    this.resultSelector = resultSelector;\n    this.active = 0;\n    this.values = [];\n    this.observables = [];\n  }\n  _next(observable) {\n    this.values.push(NONE);\n    this.observables.push(observable);\n  }\n  _complete() {\n    const observables = this.observables;\n    const len = observables.length;\n    if (len === 0) {\n      this.destination.complete();\n    } else {\n      this.active = len;\n      this.toRespond = len;\n      for (let i = 0; i < len; i++) {\n        const observable = observables[i];\n        this.add(subscribeToResult(this, observable, observable, i));\n      }\n    }\n  }\n  notifyComplete(unused) {\n    if ((this.active -= 1) === 0) {\n      this.destination.complete();\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    const values = this.values;\n    const oldVal = values[outerIndex];\n    const toRespond = !this.toRespond ? 0 : oldVal === NONE ? --this.toRespond : this.toRespond;\n    values[outerIndex] = innerValue;\n    if (toRespond === 0) {\n      if (this.resultSelector) {\n        this._tryResultSelector(values);\n      } else {\n        this.destination.next(values.slice());\n      }\n    }\n  }\n  _tryResultSelector(values) {\n    let result;\n    try {\n      result = this.resultSelector.apply(this, values);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n    this.destination.next(result);\n  }\n}", "map": {"version": 3, "names": ["isScheduler", "isArray", "OuterSubscriber", "subscribeToResult", "fromArray", "NONE", "combineLatest", "observables", "resultSelector", "scheduler", "length", "pop", "lift", "CombineLatestOperator", "constructor", "call", "subscriber", "source", "subscribe", "CombineLatestSubscriber", "destination", "active", "values", "_next", "observable", "push", "_complete", "len", "complete", "toRespond", "i", "add", "notifyComplete", "unused", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "oldVal", "_tryResultSelector", "next", "slice", "result", "apply", "err", "error"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/combineLatest.js"], "sourcesContent": ["import { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { fromArray } from './fromArray';\nconst NONE = {};\nexport function combineLatest(...observables) {\n    let resultSelector = null;\n    let scheduler = null;\n    if (isScheduler(observables[observables.length - 1])) {\n        scheduler = observables.pop();\n    }\n    if (typeof observables[observables.length - 1] === 'function') {\n        resultSelector = observables.pop();\n    }\n    if (observables.length === 1 && isArray(observables[0])) {\n        observables = observables[0];\n    }\n    return fromArray(observables, scheduler).lift(new CombineLatestOperator(resultSelector));\n}\nexport class CombineLatestOperator {\n    constructor(resultSelector) {\n        this.resultSelector = resultSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new CombineLatestSubscriber(subscriber, this.resultSelector));\n    }\n}\nexport class CombineLatestSubscriber extends OuterSubscriber {\n    constructor(destination, resultSelector) {\n        super(destination);\n        this.resultSelector = resultSelector;\n        this.active = 0;\n        this.values = [];\n        this.observables = [];\n    }\n    _next(observable) {\n        this.values.push(NONE);\n        this.observables.push(observable);\n    }\n    _complete() {\n        const observables = this.observables;\n        const len = observables.length;\n        if (len === 0) {\n            this.destination.complete();\n        }\n        else {\n            this.active = len;\n            this.toRespond = len;\n            for (let i = 0; i < len; i++) {\n                const observable = observables[i];\n                this.add(subscribeToResult(this, observable, observable, i));\n            }\n        }\n    }\n    notifyComplete(unused) {\n        if ((this.active -= 1) === 0) {\n            this.destination.complete();\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        const values = this.values;\n        const oldVal = values[outerIndex];\n        const toRespond = !this.toRespond\n            ? 0\n            : oldVal === NONE ? --this.toRespond : this.toRespond;\n        values[outerIndex] = innerValue;\n        if (toRespond === 0) {\n            if (this.resultSelector) {\n                this._tryResultSelector(values);\n            }\n            else {\n                this.destination.next(values.slice());\n            }\n        }\n    }\n    _tryResultSelector(values) {\n        let result;\n        try {\n            result = this.resultSelector.apply(this, values);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.next(result);\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,SAAS,QAAQ,aAAa;AACvC,MAAMC,IAAI,GAAG,CAAC,CAAC;AACf,OAAO,SAASC,aAAaA,CAAC,GAAGC,WAAW,EAAE;EAC1C,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIT,WAAW,CAACO,WAAW,CAACA,WAAW,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;IAClDD,SAAS,GAAGF,WAAW,CAACI,GAAG,CAAC,CAAC;EACjC;EACA,IAAI,OAAOJ,WAAW,CAACA,WAAW,CAACG,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;IAC3DF,cAAc,GAAGD,WAAW,CAACI,GAAG,CAAC,CAAC;EACtC;EACA,IAAIJ,WAAW,CAACG,MAAM,KAAK,CAAC,IAAIT,OAAO,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACrDA,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC;EAChC;EACA,OAAOH,SAAS,CAACG,WAAW,EAAEE,SAAS,CAAC,CAACG,IAAI,CAAC,IAAIC,qBAAqB,CAACL,cAAc,CAAC,CAAC;AAC5F;AACA,OAAO,MAAMK,qBAAqB,CAAC;EAC/BC,WAAWA,CAACN,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACAO,IAAIA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACC,SAAS,CAAC,IAAIC,uBAAuB,CAACH,UAAU,EAAE,IAAI,CAACR,cAAc,CAAC,CAAC;EACzF;AACJ;AACA,OAAO,MAAMW,uBAAuB,SAASjB,eAAe,CAAC;EACzDY,WAAWA,CAACM,WAAW,EAAEZ,cAAc,EAAE;IACrC,KAAK,CAACY,WAAW,CAAC;IAClB,IAAI,CAACZ,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACa,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACf,WAAW,GAAG,EAAE;EACzB;EACAgB,KAAKA,CAACC,UAAU,EAAE;IACd,IAAI,CAACF,MAAM,CAACG,IAAI,CAACpB,IAAI,CAAC;IACtB,IAAI,CAACE,WAAW,CAACkB,IAAI,CAACD,UAAU,CAAC;EACrC;EACAE,SAASA,CAAA,EAAG;IACR,MAAMnB,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMoB,GAAG,GAAGpB,WAAW,CAACG,MAAM;IAC9B,IAAIiB,GAAG,KAAK,CAAC,EAAE;MACX,IAAI,CAACP,WAAW,CAACQ,QAAQ,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACP,MAAM,GAAGM,GAAG;MACjB,IAAI,CAACE,SAAS,GAAGF,GAAG;MACpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;QAC1B,MAAMN,UAAU,GAAGjB,WAAW,CAACuB,CAAC,CAAC;QACjC,IAAI,CAACC,GAAG,CAAC5B,iBAAiB,CAAC,IAAI,EAAEqB,UAAU,EAAEA,UAAU,EAAEM,CAAC,CAAC,CAAC;MAChE;IACJ;EACJ;EACAE,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAAC,IAAI,CAACZ,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACD,WAAW,CAACQ,QAAQ,CAAC,CAAC;IAC/B;EACJ;EACAM,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,MAAMjB,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMkB,MAAM,GAAGlB,MAAM,CAACe,UAAU,CAAC;IACjC,MAAMR,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS,GAC3B,CAAC,GACDW,MAAM,KAAKnC,IAAI,GAAG,EAAE,IAAI,CAACwB,SAAS,GAAG,IAAI,CAACA,SAAS;IACzDP,MAAM,CAACe,UAAU,CAAC,GAAGD,UAAU;IAC/B,IAAIP,SAAS,KAAK,CAAC,EAAE;MACjB,IAAI,IAAI,CAACrB,cAAc,EAAE;QACrB,IAAI,CAACiC,kBAAkB,CAACnB,MAAM,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAACF,WAAW,CAACsB,IAAI,CAACpB,MAAM,CAACqB,KAAK,CAAC,CAAC,CAAC;MACzC;IACJ;EACJ;EACAF,kBAAkBA,CAACnB,MAAM,EAAE;IACvB,IAAIsB,MAAM;IACV,IAAI;MACAA,MAAM,GAAG,IAAI,CAACpC,cAAc,CAACqC,KAAK,CAAC,IAAI,EAAEvB,MAAM,CAAC;IACpD,CAAC,CACD,OAAOwB,GAAG,EAAE;MACR,IAAI,CAAC1B,WAAW,CAAC2B,KAAK,CAACD,GAAG,CAAC;MAC3B;IACJ;IACA,IAAI,CAAC1B,WAAW,CAACsB,IAAI,CAACE,MAAM,CAAC;EACjC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { EventEmitter } from '@angular/core';\nimport Swal from 'sweetalert2';\nimport { AppConfig } from 'app/app-config';\nimport { StripeCheckoutComponent } from 'app/components/stripe-checkout/stripe-checkout.component';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./../../../services/registration.service\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/settings.service\";\nimport * as i8 from \"app/services/commons.service\";\nimport * as i9 from \"@angular/platform-browser\";\nimport * as i10 from \"./../../../services/club.service\";\nimport * as i11 from \"app/pages/registration/register-new-player/register-new-player.component\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"app/layout/components/content-header/content-header.component\";\nimport * as i14 from \"../../../components/select-club-module/select-club-module.component\";\nimport * as i15 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i16 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i17 from \"../../../../@core/pipes/localized-date.pipe\";\nfunction SelectPlayerComponent_h6_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.player_registration ? \"Please click \\\"Register Player\\\" to register\" : i0.ɵɵpipeBind1(2, 1, \"Please click \\\"Register your child\\\" to register\"), \" \");\n  }\n}\nfunction SelectPlayerComponent_h6_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Player registration status\"), \" \");\n  }\n}\nfunction SelectPlayerComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function SelectPlayerComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.openRegisterNewPlayer());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.player_registration ? \"Register Player\" : i0.ɵɵpipeBind1(4, 1, \"Register your child\"));\n  }\n}\nfunction SelectPlayerComponent_div_10_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const player_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r8.getBadgeValidation(player_r7.validate_status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, player_r7.validate_status), \" \");\n  }\n}\nfunction SelectPlayerComponent_div_10_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SelectPlayerComponent_div_10_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const player_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.selectPlayer(player_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const player_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"btn-edit-player-\", player_r7.id, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"Edit\"), \" \");\n  }\n}\nfunction SelectPlayerComponent_div_10_app_select_club_module_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-select-club-module\", 32);\n  }\n  if (rf & 2) {\n    const player_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"playerId\", player_r7[\"id\"])(\"nearestClub\", player_r7.nearestRegis == null ? null : player_r7.nearestRegis.club_id)(\"eventUpdatePlayer\", ctx_r10.eventUpdatePlayer);\n  }\n}\nfunction SelectPlayerComponent_div_10_div_18_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SelectPlayerComponent_div_10_div_18_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const player_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.reRegister(player_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Re-Register\"), \" \");\n  }\n}\nfunction SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const item_r24 = i0.ɵɵnextContext(2).$implicit;\n      const player_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.payment(player_r7, player_r7.nearestRegis.id, item_r24));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Pay now\"), \" \");\n  }\n}\nfunction SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\");\n    i0.ɵɵelement(3, \"i\", 42);\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelement(7, \"span\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtemplate(9, SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_button_9_Template, 3, 3, \"button\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 3, \"Payment status\"), \": \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r25._commonService.getBadgeClassPayment(item_r24.status.toLowerCase()), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (item_r24.type === ctx_r25.PAYMENT_DETAIL_TYPES.registration || item_r24.type.includes(\"EXTRA_GROUP\")) && ctx_r25.PAYMENT_STATUS_SENT.includes(item_r24.status.toLowerCase()));\n  }\n}\nfunction SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_Template, 10, 5, \"ng-container\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (item_r24.type === ctx_r23.PAYMENT_DETAIL_TYPES.registration || item_r24.type.includes(\"EXTRA_GROUP\")) && item_r24.status.toLowerCase() != \"draft\");\n  }\n}\nfunction SelectPlayerComponent_div_10_div_18_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_Template, 2, 1, \"ng-container\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const player_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", player_r7.nearestRegis.payment_details);\n  }\n}\nfunction SelectPlayerComponent_div_10_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 4)(2, \"div\", 33)(3, \"h6\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 35);\n    i0.ɵɵelement(7, \"i\", 36);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 35);\n    i0.ɵɵelement(12, \"i\", 37);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementStart(16, \"small\", 38);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, SelectPlayerComponent_div_10_div_18_button_19_Template, 3, 3, \"button\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, SelectPlayerComponent_div_10_div_18_div_20_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const player_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 9, \"Registration Information\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(10, 11, \"Club\"), \": \", player_r7.nearestRegis == null ? null : player_r7.nearestRegis.club == null ? null : player_r7.nearestRegis.club.name, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 13, \"Status\"), \": \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r11._commonService.getBadgeRegistration(player_r7.nearestRegis.approval_status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 15, player_r7.nearestRegis.approval_status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", player_r7.nearestRegis.approval_status == \"Cancelled\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.season.fee > 0);\n  }\n}\nfunction SelectPlayerComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"div\", 19)(4, \"div\", 20)(5, \"div\", 21)(6, \"img\", 22);\n    i0.ɵɵlistener(\"error\", function SelectPlayerComponent_div_10_Template_img_error_6_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onError($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"h4\", 24);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"small\", 25);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"localizedDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SelectPlayerComponent_div_10_span_13_Template, 3, 5, \"span\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, SelectPlayerComponent_div_10_button_14_Template, 3, 4, \"button\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"hr\");\n    i0.ɵɵelementStart(16, \"div\");\n    i0.ɵɵtemplate(17, SelectPlayerComponent_div_10_app_select_club_module_17_Template, 1, 3, \"app-select-club-module\", 28);\n    i0.ɵɵtemplate(18, SelectPlayerComponent_div_10_div_18_Template, 21, 17, \"div\", 29);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const player_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"card-player-\", player_r7.id, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", player_r7.photo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r3.name_settings == null ? null : ctx_r3.name_settings.is_on) == 1 ? player_r7.user.first_name + \" \" + player_r7.user.last_name : player_r7.user.last_name + \" \" + player_r7.user.first_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 8, player_r7.dob, \"dd-MMM-yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.is_validate_required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.is_validate_required && player_r7.validate_status == ctx_r3.appConfig.VALIDATE_STATUS.AwaitingUpdate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.checkRegistration(player_r7.registrations));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.checkRegistration(player_r7.registrations));\n  }\n}\nfunction SelectPlayerComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SelectPlayerComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.openRegisterNewPlayer());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.player_registration ? \"Register Player\" : i0.ɵɵpipeBind1(4, 1, \"Register your child\"));\n  }\n}\nexport class SelectPlayerComponent {\n  constructor(_router, cd, _registrationService, _loadingService, _coreSidebarService, _translateService, _modalService, _settingsService, _commonService, _titleService, _clubService) {\n    this._router = _router;\n    this.cd = cd;\n    this._registrationService = _registrationService;\n    this._loadingService = _loadingService;\n    this._coreSidebarService = _coreSidebarService;\n    this._translateService = _translateService;\n    this._modalService = _modalService;\n    this._settingsService = _settingsService;\n    this._commonService = _commonService;\n    this._titleService = _titleService;\n    this._clubService = _clubService;\n    // public variables\n    this.is_validate_required = true;\n    this.model = {};\n    this.player_registration = false;\n    this.players = [];\n    this.clubs = [];\n    this.appConfig = AppConfig;\n    this.currentPlayer = {\n      season_id: null,\n      player: null,\n      type: null\n    };\n    this.eventUpdatePlayer = new EventEmitter();\n    this.PAYMENT_DETAIL_TYPES = AppConfig.PAYMENT_DETAIL_TYPES;\n    this.PAYMENT_STATUS_PAID = AppConfig.PAYMENT_STATUS_PAID;\n    this.PAYMENT_STATUS_SENT = AppConfig.PAYMENT_STATUS_SENT;\n    this._titleService.setTitle(`${_translateService.instant(this._registrationService.selectedSeason['type'])} ${this._registrationService.selectedSeason['name']}`);\n    if (_settingsService.initSettingsValue && _settingsService.initSettingsValue.hasOwnProperty('is_validate_required')) {\n      this.is_validate_required = _settingsService.initSettingsValue.is_validate_required;\n      this.player_registration = _settingsService.initSettingsValue.player_registration;\n    }\n    this.getAllClubsIsActive();\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\n    console.log('name_settings: ', this.name_settings);\n  }\n  getAllClubsIsActive() {\n    // get all clubs\n    this._clubService.getAllClubsIsActive().subscribe(data => {\n      // set data to clubs\n      this.clubs = data;\n      // set data to allClubs\n      this._registrationService.allClubs = data;\n    }, error => {\n      // show error\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    });\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: `${this._translateService.instant(this._registrationService.selectedSeason['type'])} ${this._registrationService.selectedSeason['name']}`,\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Application',\n          isLink: true,\n          link: '/registration/select-event'\n        }, {\n          name: `${this._translateService.instant(this._registrationService.selectedSeason['type'])} ${this._registrationService.selectedSeason['name']}`,\n          isLink: false\n        }]\n      }\n    };\n    this.season = this._registrationService.selectedSeason;\n    // define default values for unsubscribe all\n    this._unsubscribeAll = new Subject();\n    // get players of this season\n    this.getAllPlayers();\n    // listen to event update player\n    this.eventUpdatePlayer.subscribe(() => {\n      this.getAllPlayers();\n      this.currentPlayer.type = this.appConfig.REGISTER_TYPE.edit;\n      this.currentPlayer.player = {};\n    });\n    this.cd.detectChanges();\n  }\n  openRegisterNewPlayer() {\n    this.currentPlayer.player = {};\n    this.currentPlayer.type = this.appConfig.REGISTER_TYPE.new;\n    this._coreSidebarService.getSidebarRegistry('register-new-player').toggleOpen();\n  }\n  selectPlayer(player) {\n    this.currentPlayer.season_id = this.season.id;\n    this.currentPlayer.player = player;\n    this.currentPlayer.type = this.appConfig.REGISTER_TYPE.edit;\n    if (player.validate_status == this.appConfig.VALIDATE_STATUS.AwaitingUpdate) {\n      this.currentPlayer.type = this.appConfig.REGISTER_TYPE.awaiting_update;\n    }\n    this._coreSidebarService.getSidebarRegistry('register-new-player').toggleOpen();\n  }\n  reRegister(player) {\n    // this.currentPlayer.season_id = this.season.id;\n    // this.currentPlayer.player = player;\n    // this.currentPlayer.type = this.appConfig.REGISTER_TYPE.reregister;\n    // this._coreSidebarService\n    //   .getSidebarRegistry('register-new-player')\n    //   .toggleOpen();\n    this.model = this.updatePlayerInfo(player);\n    this._registrationService.reRegisterPlayer(this.season.id, this.model).pipe(takeUntil(this._unsubscribeAll)).subscribe(data => {\n      this.getAllPlayers();\n      Swal.fire({\n        title: 'Success!',\n        text: this._translateService.instant('Player re-registered successfully'),\n        icon: 'success',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, error => {\n      Swal.fire({\n        title: 'Error!',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  // get players of this season\n  getAllPlayers() {\n    this._loadingService.show();\n    this._registrationService.getAllPlayers().pipe(takeUntil(this._unsubscribeAll)).subscribe(data => {\n      this.players = data['players'];\n      // find registrations of this season\n      this.players.forEach(player => {\n        if (player.registrations.length == 0) {\n          player.nearestRegis = null;\n          return;\n        }\n        let nearestRegis = player.registrations.find(reg => reg.season_id == this._registrationService.selectedSeason['id']);\n        if (nearestRegis) {\n          const unpaidPayment = nearestRegis.payment_details.find(payment => (payment.type === this.PAYMENT_DETAIL_TYPES.registration || payment.type.includes('EXTRA_GROUP')) && this.PAYMENT_STATUS_SENT.includes(payment.status.toLowerCase()));\n          player.nearestRegis = {\n            ...nearestRegis,\n            payment_details: unpaidPayment ? [nearestRegis.payment_details[nearestRegis.payment_details.length - 1]] : [] // Get latest payment of registration\n          };\n\n          console.log(\"🚀 ~ SelectPlayerComponent ~ this.players.forEach ~ player.nearestRegis:\", player.nearestRegis);\n        } else {\n          player.nearestRegis = player.registrations[0];\n        }\n      });\n    }, error => {\n      Swal.fire({\n        title: 'Error!',\n        text: error.error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  updatePlayerInfo(player) {\n    let temp_model = {};\n    for (const key in player) {\n      let value = player[key];\n      if (key == 'user') {\n        for (const user_key in value) {\n          if (value[user_key]) temp_model[user_key] = value[user_key];\n        }\n      } else if (key == 'custom_fields') {\n        for (const custom_key in value) {\n          if (value[custom_key]) temp_model[custom_key] = value[custom_key];\n        }\n      } else {\n        if (value) temp_model[key] = value;\n      }\n    }\n    let registration = player?.registrations?.find(reg => reg.season_id == this._registrationService.selectedSeason['id']);\n    temp_model['club_id'] = registration?.club_id;\n    return temp_model;\n  }\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n    // detroy eventUpdatePlayer\n    this.eventUpdatePlayer.unsubscribe();\n  }\n  // check if player is registered\n  checkRegistration(registrations) {\n    // check in array of registrations have item = selected season\n    let result = registrations.find(registration => registration.season_id === this._registrationService.selectedSeason.id);\n    if (result) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n  onError($event) {\n    $event.target.src = 'assets/images/logo/ezactive_1024x1024.png';\n  }\n  getBadgeValidation(status) {\n    switch (status) {\n      case AppConfig.VALIDATE_STATUS.Pending:\n        return 'badge-light-info';\n      case AppConfig.VALIDATE_STATUS.AwaitingUpdate:\n        return 'badge-light-danger';\n      case AppConfig.VALIDATE_STATUS.Updated:\n        return 'badge-light-warning';\n      case AppConfig.VALIDATE_STATUS.Validated:\n        return 'badge-light-success';\n      default:\n        return 'badge-light-secondary';\n    }\n  }\n  payment(player, registration_id, paymentDetail = null) {\n    if (paymentDetail && paymentDetail.payment && paymentDetail.payment.payment_url) {\n      // open payment url in new tab\n      window.open(paymentDetail.payment.payment_url, '_blank');\n      return;\n    }\n    // Define modal payment\n    let modalRef = this._modalService.open(StripeCheckoutComponent, {\n      size: 'sm',\n      centered: true,\n      backdrop: 'static'\n    });\n    let description = `${player.user.first_name} ${player.user.last_name} pay for season ${this.season.name}`;\n    modalRef.componentInstance.closeOnSuccess = true;\n    modalRef.componentInstance.products = [{\n      name: description,\n      quantity: 1,\n      price: this.season.fee\n    }];\n    modalRef.componentInstance.description = description;\n    modalRef.componentInstance.api_checkout = `${environment.apiUrl}/stripe/checkout`;\n    modalRef.componentInstance.onSucceeded.subscribe(response => {\n      let data = response.data;\n      let formData = new FormData();\n      formData.append('payment_intent', data.id);\n      formData.append('payment_status', data.status);\n      formData.append('fee', data.amount);\n      formData.append('registration_id', registration_id);\n      this._registrationService.storePayment(formData).subscribe(data => {\n        Swal.fire({\n          title: 'Success!',\n          text: data.message,\n          icon: 'success',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n        this.getAllPlayers();\n      });\n    });\n  }\n  static #_ = this.ɵfac = function SelectPlayerComponent_Factory(t) {\n    return new (t || SelectPlayerComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.RegistrationService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.CoreSidebarService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.SettingsService), i0.ɵɵdirectiveInject(i8.CommonsService), i0.ɵɵdirectiveInject(i9.Title), i0.ɵɵdirectiveInject(i10.ClubService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SelectPlayerComponent,\n    selectors: [[\"app-select-player\"]],\n    decls: 14,\n    vars: 8,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"select-player-page\"], [1, \"row\"], [1, \"col-12\", \"col-sm-6\"], [\"class\", \"my-2 text-secondary\", 4, \"ngIf\"], [\"class\", \"col-6 mt-auto mb-auto d-none d-sm-inline-block\", 4, \"ngIf\"], [1, \"row\", \"mb-4\", \"mb-sm-4\"], [\"class\", \"col-lg-4 col-md-6 col-12 mb-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12 px-1 d-sm-none d-inline-block sticky-sm-bottom\", 4, \"ngIf\"], [\"name\", \"register-new-player\", \"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\"], [3, \"eventUpdatePlayer\", \"registerParam\"], [1, \"my-2\", \"text-secondary\"], [1, \"col-6\", \"mt-auto\", \"mb-auto\", \"d-none\", \"d-sm-inline-block\"], [\"data-testid\", \"btnAddNewPlayer\", \"id\", \"btn-register-top\", 1, \"btn\", \"btn-primary\", \"float-right\", 3, \"click\"], [1, \"col-lg-4\", \"col-md-6\", \"col-12\", \"mb-2\"], [1, \"card\", \"mb-2\", 3, \"id\"], [1, \"card-body\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-1\"], [1, \"media\"], [1, \"avatar\", \"avatar-xl\", \"mr-75\"], [\"alt\", \"Avatar\", 1, \"rounded\", 2, \"object-fit\", \"cover\", 3, \"src\", \"error\"], [1, \"media-body\", \"my-auto\"], [1, \"mb-0\", \"text-break\"], [1, \"text-muted\"], [\"class\", \"d-table badge badge-pill text-capitalize\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-primary btn-sm\", 3, \"id\", \"click\", 4, \"ngIf\"], [\"class\", \"players-description\", 3, \"playerId\", \"nearestClub\", \"eventUpdatePlayer\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-table\", \"badge\", \"badge-pill\", \"text-capitalize\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"id\", \"click\"], [1, \"players-description\", 3, \"playerId\", \"nearestClub\", \"eventUpdatePlayer\"], [1, \"col\"], [1, \"font-weight-bolder\"], [1, \"text-secondary\"], [\"data-feather\", \"map-pin\", 1, \"mr-1\", \"mt-25\"], [\"data-feather\", \"bookmark\", 1, \"mr-1\", \"mt-25\"], [1, \"badge\", \"badge-pill\", \"text-capitalize\"], [\"class\", \"btn btn-block btn-outline-danger btn-sm mt-2\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-block\", \"btn-outline-danger\", \"btn-sm\", \"mt-2\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"fa-light\", \"fa-money-check-dollar\", \"mr-1\", \"mt-25\"], [1, \"text-capitalize\"], [3, \"innerHTML\"], [\"class\", \"btn btn-block btn-outline-warning btn-sm mt-2\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-block\", \"btn-outline-warning\", \"btn-sm\", \"mt-2\", 3, \"click\"], [1, \"col-12\", \"px-1\", \"d-sm-none\", \"d-inline-block\", \"sticky-sm-bottom\"], [\"data-testid\", \"btnAddNewPlayer\", \"id\", \"btn-register-bot\", 1, \"btn\", \"btn-block\", \"btn-primary\", 3, \"click\"]],\n    template: function SelectPlayerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵtemplate(6, SelectPlayerComponent_h6_6_Template, 3, 3, \"h6\", 6);\n        i0.ɵɵtemplate(7, SelectPlayerComponent_h6_7_Template, 3, 3, \"h6\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, SelectPlayerComponent_div_8_Template, 5, 3, \"div\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 8);\n        i0.ɵɵtemplate(10, SelectPlayerComponent_div_10_Template, 19, 11, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(11, SelectPlayerComponent_div_11_Template, 5, 3, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"core-sidebar\", 11);\n        i0.ɵɵelement(13, \"app-register-new-player\", 12);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.players.length == 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.players.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.players.length == 0 || ctx.player_registration === false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.players);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.players.length == 0 || ctx.player_registration === false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"eventUpdatePlayer\", ctx.eventUpdatePlayer)(\"registerParam\", ctx.currentPlayer);\n      }\n    },\n    dependencies: [i11.RegisterNewPlayerComponent, i12.NgForOf, i12.NgIf, i13.ContentHeaderComponent, i14.SelectClubModuleComponent, i15.CoreSidebarComponent, i16.FeatherIconDirective, i5.TranslatePipe, i17.LocalizedDatePipe],\n    styles: [\".sticky-sm-bottom {\\n  position: fixed !important;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 8px 0;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1);\\n  z-index: 1001;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVnaXN0cmF0aW9uL3NlbGVjdC1wbGF5ZXIvc2VsZWN0LXBsYXllci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLDBCQUFBO0VBQ0EsU0FBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsY0FBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSw4Q0FBQTtFQUNBLGFBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi5zdGlja3ktc20tYm90dG9tIHtcclxuICAgIHBvc2l0aW9uOiBmaXhlZCAhaW1wb3J0YW50O1xyXG4gICAgYm90dG9tOiAwO1xyXG4gICAgbGVmdDogMDtcclxuICAgIHJpZ2h0OiAwO1xyXG4gICAgcGFkZGluZzogOHB4IDA7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIGJveC1zaGFkb3c6IDAgNHB4IDI0cHggMCByZ2JhKDM0LCA0MSwgNDcsIDAuMSk7XHJcbiAgICB6LWluZGV4OiAxMDAxO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,OAAO,QAAQ,MAAM;AAG9B,SAAuCC,YAAY,QAAmC,eAAe;AACrG,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,SAAS,QAAQ,gBAAgB;AAI1C,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,WAAW,QAAQ,0BAA0B;;;;;;;;;;;;;;;;;;;;;ICN5CC,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAE,MAAA,GAKF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IALHH,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,mBAAA,oDAAAP,EAAA,CAAAQ,WAAA,gEAKF;;;;;IACAR,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;IADHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,0CACF;;;;;;IAEFR,EAAA,CAAAC,cAAA,cAGC;IAKGD,EAAA,CAAAS,UAAA,mBAAAC,6DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,qBAAA,EAAuB;IAAA,EAAC;IAEjChB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAIF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAJLH,EAAA,CAAAI,SAAA,GAIF;IAJEJ,EAAA,CAAAiB,iBAAA,CAAAC,MAAA,CAAAX,mBAAA,uBAAAP,EAAA,CAAAQ,WAAA,8BAIF;;;;;IAkCIR,EAAA,CAAAC,cAAA,eAIC;IACCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAAmB,UAAA,CAAAC,MAAA,CAAAC,kBAAA,CAAAC,SAAA,CAAAC,eAAA,EAAoD;IAEpDvB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,OAAAc,SAAA,CAAAC,eAAA,OACF;;;;;;IAGJvB,EAAA,CAAAC,cAAA,iBASC;IAFCD,EAAA,CAAAS,UAAA,mBAAAe,wEAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,IAAA;MAAA,MAAAH,SAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAY,SAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAY,OAAA,CAAAC,YAAA,CAAAN,SAAA,CAAoB;IAAA,EAAC;IAG9BtB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAA6B,sBAAA,2BAAAP,SAAA,CAAAQ,EAAA,KAAoC;IAEpC9B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,oBACF;;;;;IAKAR,EAAA,CAAA+B,SAAA,iCAOyB;;;;;IAJvB/B,EAAA,CAAAgC,UAAA,aAAAV,SAAA,OAAyB,gBAAAA,SAAA,CAAAW,YAAA,kBAAAX,SAAA,CAAAW,YAAA,CAAAC,OAAA,uBAAAC,OAAA,CAAAC,iBAAA;;;;;;IAkCnBpC,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAS,UAAA,mBAAA4B,+EAAA;MAAArC,EAAA,CAAAW,aAAA,CAAA2B,IAAA;MAAA,MAAAhB,SAAA,GAAAtB,EAAA,CAAAc,aAAA,IAAAY,SAAA;MAAA,MAAAa,OAAA,GAAAvC,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAwB,OAAA,CAAAC,UAAA,CAAAlB,SAAA,CAAkB;IAAA,EAAC;IAE5BtB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IADPH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,2BACF;;;;;;IAiCMR,EAAA,CAAAC,cAAA,iBAYC;IAHCD,EAAA,CAAAS,UAAA,mBAAAgC,mHAAA;MAAAzC,EAAA,CAAAW,aAAA,CAAA+B,IAAA;MAAA,MAAAC,QAAA,GAAA3C,EAAA,CAAAc,aAAA,IAAAY,SAAA;MAAA,MAAAJ,SAAA,GAAAtB,EAAA,CAAAc,aAAA,IAAAY,SAAA;MAAA,MAAAkB,OAAA,GAAA5C,EAAA,CAAAc,aAAA;MAAA,OACqCd,EAAA,CAAAe,WAAA,CAAA6B,OAAA,CAAAC,OAAA,CAAAvB,SAAA,EAAAA,SAAA,CAAAW,YAAA,CAAAH,EAAA,EAAAa,QAAA,CACpC;IAAA;IAED3C,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IADPH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,uBACF;;;;;IAvCJR,EAAA,CAAA8C,uBAAA,GAMC;IACC9C,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAA+B,SAAA,YAEK;IACL/B,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,GACA;;IAAAF,EAAA,CAAA+B,SAAA,eAMQ;IACV/B,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAA+C,UAAA,IAAAC,0FAAA,qBAcS;IACXhD,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAiD,qBAAA,EAAe;;;;;IA5BPjD,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,+BACA;IACER,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAgC,UAAA,cAAAkB,OAAA,CAAAC,cAAA,CAAAC,oBAAA,CAAAT,QAAA,CAAAU,MAAA,CAAAC,WAAA,KAAAtD,EAAA,CAAAuD,cAAA,CAIC;IAQJvD,EAAA,CAAAI,SAAA,GAMJ;IANIJ,EAAA,CAAAgC,UAAA,UAAAW,QAAA,CAAAa,IAAA,KAAAN,OAAA,CAAAO,oBAAA,CAAAC,YAAA,IAAAf,QAAA,CAAAa,IAAA,CAAAG,QAAA,oBAAAT,OAAA,CAAAU,mBAAA,CAAAD,QAAA,CAAAhB,QAAA,CAAAU,MAAA,CAAAC,WAAA,IAMJ;;;;;IAtCLtD,EAAA,CAAA8C,uBAAA,GAIC;IACC9C,EAAA,CAAA+C,UAAA,IAAAc,iFAAA,4BAyCe;IACjB7D,EAAA,CAAAiD,qBAAA,EAAe;;;;;IAzCVjD,EAAA,CAAAI,SAAA,GAIF;IAJEJ,EAAA,CAAAgC,UAAA,UAAAW,QAAA,CAAAa,IAAA,KAAAM,OAAA,CAAAL,oBAAA,CAAAC,YAAA,IAAAf,QAAA,CAAAa,IAAA,CAAAG,QAAA,oBAAAhB,QAAA,CAAAU,MAAA,CAAAC,WAAA,cAIF;;;;;IAXLtD,EAAA,CAAAC,cAAA,UAA4B;IAC1BD,EAAA,CAAA+C,UAAA,IAAAgB,kEAAA,2BA+Ce;IAKjB/D,EAAA,CAAAG,YAAA,EAAM;;;;IAlDsCH,EAAA,CAAAI,SAAA,GACvC;IADuCJ,EAAA,CAAAgC,UAAA,YAAAV,SAAA,CAAAW,YAAA,CAAA+B,eAAA,CACvC;;;;;IA3CXhE,EAAA,CAAAC,cAAA,UAAqD;IAI7CD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAA+B,SAAA,YAAiD;IACjD/B,EAAA,CAAAC,cAAA,WAAM;IACJD,EAAA,CAAAE,MAAA,GAEF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAA+B,SAAA,aAAkD;IAClD/B,EAAA,CAAAC,cAAA,YAAM;IACJD,EAAA,CAAAE,MAAA,IACA;;IAAAF,EAAA,CAAAC,cAAA,iBAOC;IAAAD,EAAA,CAAAE,MAAA,IAEG;;IAAAF,EAAA,CAAAG,YAAA,EACH;IAEHH,EAAA,CAAA+C,UAAA,KAAAkB,sDAAA,qBAQS;IACXjE,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA+C,UAAA,KAAAmB,mDAAA,kBAqDM;IACRlE,EAAA,CAAAG,YAAA,EAAM;;;;;IAzFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,wCACF;IAIIR,EAAA,CAAAI,SAAA,GAEF;IAFEJ,EAAA,CAAAmE,kBAAA,MAAAnE,EAAA,CAAAQ,WAAA,wBAAAc,SAAA,CAAAW,YAAA,kBAAAX,SAAA,CAAAW,YAAA,CAAAmC,IAAA,kBAAA9C,SAAA,CAAAW,YAAA,CAAAmC,IAAA,CAAAC,IAAA,MAEF;IAKErE,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAQ,WAAA,yBACA;IAEER,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAmB,UAAA,CAAAmD,OAAA,CAAAnB,cAAA,CAAAoB,oBAAA,CAAAjD,SAAA,CAAAW,YAAA,CAAAuC,eAAA,EAIC;IACFxE,EAAA,CAAAI,SAAA,GAEG;IAFHJ,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAQ,WAAA,SAAAc,SAAA,CAAAW,YAAA,CAAAuC,eAAA,EAEG;IAIHxE,EAAA,CAAAI,SAAA,GAEA;IAFAJ,EAAA,CAAAgC,UAAA,SAAAV,SAAA,CAAAW,YAAA,CAAAuC,eAAA,gBAEA;IAOCxE,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAgC,UAAA,SAAAsC,OAAA,CAAAG,MAAA,CAAAC,GAAA,KAAoB;;;;;;IApGxC1E,EAAA,CAAAC,cAAA,cAGC;IAaaD,EAAA,CAAAS,UAAA,mBAAAkE,2DAAAC,MAAA;MAAA5E,EAAA,CAAAW,aAAA,CAAAkE,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAA+D,OAAA,CAAAC,OAAA,CAAAH,MAAA,CAAe;IAAA,EAAC;IAL3B5E,EAAA,CAAAG,YAAA,EAME;IAEJH,EAAA,CAAAC,cAAA,cAAgC;IAE5BD,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAEtB;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACZH,EAAA,CAAA+C,UAAA,KAAAiC,6CAAA,mBAMO;IACThF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAA+C,UAAA,KAAAkC,+CAAA,qBAWS;IACXjF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA+B,SAAA,UAAM;IACN/B,EAAA,CAAAC,cAAA,WAAK;IAEHD,EAAA,CAAA+C,UAAA,KAAAmC,+DAAA,qCAOyB;IACzBlF,EAAA,CAAA+C,UAAA,KAAAoC,4CAAA,oBA+FM;IACRnF,EAAA,CAAAG,YAAA,EAAM;;;;;IAzJaH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA6B,sBAAA,uBAAAP,SAAA,CAAAQ,EAAA,KAAgC;IAQ3C9B,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAgC,UAAA,QAAAV,SAAA,CAAA8D,KAAA,EAAApF,EAAA,CAAAqF,aAAA,CAAoB;IASpBrF,EAAA,CAAAI,SAAA,GAGF;IAHEJ,EAAA,CAAAK,kBAAA,OAAAiF,MAAA,CAAAC,aAAA,kBAAAD,MAAA,CAAAC,aAAA,CAAAC,KAAA,SAAAlE,SAAA,CAAAmE,IAAA,CAAAC,UAAA,SAAApE,SAAA,CAAAmE,IAAA,CAAAE,SAAA,GAAArE,SAAA,CAAAmE,IAAA,CAAAE,SAAA,SAAArE,SAAA,CAAAmE,IAAA,CAAAC,UAAA,MAGF;IAC0B1F,EAAA,CAAAI,SAAA,GAEtB;IAFsBJ,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAA4F,WAAA,QAAAtE,SAAA,CAAAuE,GAAA,iBAEtB;IAED7F,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAgC,UAAA,SAAAsD,MAAA,CAAAQ,oBAAA,CAA0B;IAS9B9F,EAAA,CAAAI,SAAA,GAIF;IAJEJ,EAAA,CAAAgC,UAAA,SAAAsD,MAAA,CAAAQ,oBAAA,IAAAxE,SAAA,CAAAC,eAAA,IAAA+D,MAAA,CAAAS,SAAA,CAAAC,eAAA,CAAAC,cAAA,CAIF;IAYEjG,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAgC,UAAA,UAAAsD,MAAA,CAAAY,iBAAA,CAAA5E,SAAA,CAAA6E,aAAA,EAA8C;IAO3CnG,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAgC,UAAA,SAAAsD,MAAA,CAAAY,iBAAA,CAAA5E,SAAA,CAAA6E,aAAA,EAA6C;;;;;;IAuG7DnG,EAAA,CAAAC,cAAA,cAGC;IAKGD,EAAA,CAAAS,UAAA,mBAAA2F,8DAAA;MAAApG,EAAA,CAAAW,aAAA,CAAA0F,IAAA;MAAA,MAAAC,OAAA,GAAAtG,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAuF,OAAA,CAAAtF,qBAAA,EAAuB;IAAA,EAAC;IAEjChB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAIF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAJLH,EAAA,CAAAI,SAAA,GAIF;IAJEJ,EAAA,CAAAiB,iBAAA,CAAAsF,MAAA,CAAAhG,mBAAA,uBAAAP,EAAA,CAAAQ,WAAA,8BAIF;;;ADhMd,OAAM,MAAOgG,qBAAqB;EAwBhCC,YACUC,OAAe,EACfC,EAAqB,EACrBC,oBAAyC,EACzCC,eAA+B,EAC/BC,mBAAuC,EACxCC,iBAAmC,EACnCC,aAAuB,EACvBC,gBAAiC,EACjC9D,cAA8B,EAC9B+D,aAAoB,EACpBC,YAAyB;IAVxB,KAAAT,OAAO,GAAPA,OAAO;IACP,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAA9D,cAAc,GAAdA,cAAc;IACd,KAAA+D,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IAlCrB;IACA,KAAArB,oBAAoB,GAAG,IAAI;IAC3B,KAAAsB,KAAK,GAAQ,EAAE;IACR,KAAA7G,mBAAmB,GAAY,KAAK;IAEpC,KAAA8G,OAAO,GAAG,EAAE;IACZ,KAAAC,KAAK,GAAG,EAAE;IAEV,KAAAvB,SAAS,GAAGlG,SAAS;IACrB,KAAA0H,aAAa,GAAkB;MACpCC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZjE,IAAI,EAAE;KACP;IAGM,KAAApB,iBAAiB,GAAG,IAAIzC,YAAY,EAAE;IAC7C,KAAA8D,oBAAoB,GAAG5D,SAAS,CAAC4D,oBAAoB;IACrD,KAAAiE,mBAAmB,GAAG7H,SAAS,CAAC6H,mBAAmB;IACnD,KAAA9D,mBAAmB,GAAG/D,SAAS,CAAC+D,mBAAmB;IAiBjD,IAAI,CAACsD,aAAa,CAACS,QAAQ,CACzB,GAAGZ,iBAAiB,CAACa,OAAO,CAC1B,IAAI,CAAChB,oBAAoB,CAACiB,cAAc,CAAC,MAAM,CAAC,CACjD,IAAI,IAAI,CAACjB,oBAAoB,CAACiB,cAAc,CAAC,MAAM,CAAC,EAAE,CACxD;IACD,IACEZ,gBAAgB,CAACa,iBAAiB,IAClCb,gBAAgB,CAACa,iBAAiB,CAACC,cAAc,CAAC,sBAAsB,CAAC,EACzE;MACA,IAAI,CAACjC,oBAAoB,GACvBmB,gBAAgB,CAACa,iBAAiB,CAAChC,oBAAoB;MACzD,IAAI,CAACvF,mBAAmB,GACtB0G,gBAAgB,CAACa,iBAAiB,CAACvH,mBAAmB;;IAE1D,IAAI,CAACyH,mBAAmB,EAAE;IAC1B,IAAI,CAACzC,aAAa,GAAG0C,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;IACtEC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC/C,aAAa,CAAC;EACpD;EAEAyC,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACb,YAAY,CAACa,mBAAmB,EAAE,CAACO,SAAS,CAC9CC,IAAI,IAAI;MACP;MACA,IAAI,CAAClB,KAAK,GAAGkB,IAAI;MACjB;MACA,IAAI,CAAC5B,oBAAoB,CAAC6B,QAAQ,GAAGD,IAAI;IAC3C,CAAC,EACAE,KAAK,IAAI;MACR;MACA9I,IAAI,CAAC+I,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,CACF;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,GAAG,IAAI,CAACpC,iBAAiB,CAACa,OAAO,CAC5C,IAAI,CAAChB,oBAAoB,CAACiB,cAAc,CAAC,MAAM,CAAC,CACjD,IAAI,IAAI,CAACjB,oBAAoB,CAACiB,cAAc,CAAC,MAAM,CAAC,EAAE;MACvDuB,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACV7F,IAAI,EAAE,EAAE;QACR8F,KAAK,EAAE,CACL;UACEjF,IAAI,EAAE,aAAa;UACnBkF,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE;SACP,EACD;UACEnF,IAAI,EAAE,GAAG,IAAI,CAAC0C,iBAAiB,CAACa,OAAO,CACrC,IAAI,CAAChB,oBAAoB,CAACiB,cAAc,CAAC,MAAM,CAAC,CACjD,IAAI,IAAI,CAACjB,oBAAoB,CAACiB,cAAc,CAAC,MAAM,CAAC,EAAE;UACvD0B,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAC9E,MAAM,GAAG,IAAI,CAACmC,oBAAoB,CAACiB,cAAc;IAEtD;IACA,IAAI,CAAC4B,eAAe,GAAG,IAAI/J,OAAO,EAAE;IAEpC;IACA,IAAI,CAACgK,aAAa,EAAE;IAEpB;IACA,IAAI,CAACtH,iBAAiB,CAACmG,SAAS,CAAC,MAAK;MACpC,IAAI,CAACmB,aAAa,EAAE;MACpB,IAAI,CAACnC,aAAa,CAAC/D,IAAI,GAAG,IAAI,CAACuC,SAAS,CAAC4D,aAAa,CAACC,IAAI;MAC3D,IAAI,CAACrC,aAAa,CAACE,MAAM,GAAG,EAAE;IAChC,CAAC,CAAC;IACF,IAAI,CAACd,EAAE,CAACkD,aAAa,EAAE;EACzB;EAEA7I,qBAAqBA,CAAA;IACnB,IAAI,CAACuG,aAAa,CAACE,MAAM,GAAG,EAAE;IAC9B,IAAI,CAACF,aAAa,CAAC/D,IAAI,GAAG,IAAI,CAACuC,SAAS,CAAC4D,aAAa,CAACG,GAAG;IAC1D,IAAI,CAAChD,mBAAmB,CACrBiD,kBAAkB,CAAC,qBAAqB,CAAC,CACzCC,UAAU,EAAE;EACjB;EAEApI,YAAYA,CAAC6F,MAAM;IACjB,IAAI,CAACF,aAAa,CAACC,SAAS,GAAG,IAAI,CAAC/C,MAAM,CAAC3C,EAAE;IAC7C,IAAI,CAACyF,aAAa,CAACE,MAAM,GAAGA,MAAM;IAClC,IAAI,CAACF,aAAa,CAAC/D,IAAI,GAAG,IAAI,CAACuC,SAAS,CAAC4D,aAAa,CAACC,IAAI;IAC3D,IACEnC,MAAM,CAAClG,eAAe,IAAI,IAAI,CAACwE,SAAS,CAACC,eAAe,CAACC,cAAc,EACvE;MACA,IAAI,CAACsB,aAAa,CAAC/D,IAAI,GAAG,IAAI,CAACuC,SAAS,CAAC4D,aAAa,CAACM,eAAe;;IAGxE,IAAI,CAACnD,mBAAmB,CACrBiD,kBAAkB,CAAC,qBAAqB,CAAC,CACzCC,UAAU,EAAE;EACjB;EAEAxH,UAAUA,CAACiF,MAAM;IACf;IACA;IACA;IACA;IACA;IACA;IAEA,IAAI,CAACL,KAAK,GAAG,IAAI,CAAC8C,gBAAgB,CAACzC,MAAM,CAAC;IAC1C,IAAI,CAACb,oBAAoB,CACtBuD,gBAAgB,CAAC,IAAI,CAAC1F,MAAM,CAAC3C,EAAE,EAAE,IAAI,CAACsF,KAAK,CAAC,CAC5CgD,IAAI,CAAC3K,SAAS,CAAC,IAAI,CAACgK,eAAe,CAAC,CAAC,CACrClB,SAAS,CACPC,IAAI,IAAI;MACP,IAAI,CAACkB,aAAa,EAAE;MACpB9J,IAAI,CAAC+I,IAAI,CAAC;QACRC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,IAAI,CAAC9B,iBAAiB,CAACa,OAAO,CAClC,mCAAmC,CACpC;QACDmB,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE,IAAI,CAACjC,iBAAiB,CAACa,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACAc,KAAK,IAAI;MACR9I,IAAI,CAAC+I,IAAI,CAAC;QACRC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACjC,iBAAiB,CAACa,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACL;EAEA;EACA8B,aAAaA,CAAA;IACX,IAAI,CAAC7C,eAAe,CAACwD,IAAI,EAAE;IAC3B,IAAI,CAACzD,oBAAoB,CACtB8C,aAAa,EAAE,CACfU,IAAI,CAAC3K,SAAS,CAAC,IAAI,CAACgK,eAAe,CAAC,CAAC,CACrClB,SAAS,CACPC,IAAI,IAAI;MACP,IAAI,CAACnB,OAAO,GAAGmB,IAAI,CAAC,SAAS,CAAC;MAC9B;MACA,IAAI,CAACnB,OAAO,CAACiD,OAAO,CAAE7C,MAAM,IAAI;QAC9B,IAAIA,MAAM,CAACtB,aAAa,CAACoE,MAAM,IAAI,CAAC,EAAE;UACpC9C,MAAM,CAACxF,YAAY,GAAG,IAAI;UAC1B;;QAEF,IAAIA,YAAY,GAAGwF,MAAM,CAACtB,aAAa,CAACqE,IAAI,CACzCC,GAAG,IACFA,GAAG,CAACjD,SAAS,IAAI,IAAI,CAACZ,oBAAoB,CAACiB,cAAc,CAAC,IAAI,CAAC,CAClE;QACD,IAAI5F,YAAY,EAAE;UAChB,MAAMyI,aAAa,GAAGzI,YAAY,CAAC+B,eAAe,CAACwG,IAAI,CAAE3H,OAAO,IAAK,CAACA,OAAO,CAACW,IAAI,KAAK,IAAI,CAACC,oBAAoB,CAACC,YAAY,IAAIb,OAAO,CAACW,IAAI,CAACG,QAAQ,CAAC,aAAa,CAAC,KACnK,IAAI,CAACC,mBAAmB,CAACD,QAAQ,CAC/Bd,OAAO,CAACQ,MAAM,CAACC,WAAW,EAAE,CAC7B,CAAC;UAIJmE,MAAM,CAACxF,YAAY,GAAG;YACpB,GAAGA,YAAY;YACf+B,eAAe,EAAE0G,aAAa,GAAG,CAACzI,YAAY,CAAC+B,eAAe,CAAC/B,YAAY,CAAC+B,eAAe,CAACuG,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;WAC/G;;UAEDlC,OAAO,CAACC,GAAG,CAAC,0EAA0E,EAAEb,MAAM,CAACxF,YAAY,CAAC;SAI7G,MAAM;UACLwF,MAAM,CAACxF,YAAY,GAAGwF,MAAM,CAACtB,aAAa,CAAC,CAAC,CAAC;;MAGjD,CAAC,CAAC;IACJ,CAAC,EACAuC,KAAK,IAAI;MACR9I,IAAI,CAAC+I,IAAI,CAAC;QACRC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAEH,KAAK,CAACA,KAAK,CAACI,OAAO;QACzBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACjC,iBAAiB,CAACa,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACL;EAEAsC,gBAAgBA,CAACzC,MAAM;IACrB,IAAIkD,UAAU,GAAG,EAAE;IACnB,KAAK,MAAMC,GAAG,IAAInD,MAAM,EAAE;MACxB,IAAIoD,KAAK,GAAGpD,MAAM,CAACmD,GAAG,CAAC;MACvB,IAAIA,GAAG,IAAI,MAAM,EAAE;QACjB,KAAK,MAAME,QAAQ,IAAID,KAAK,EAAE;UAC5B,IAAIA,KAAK,CAACC,QAAQ,CAAC,EAAEH,UAAU,CAACG,QAAQ,CAAC,GAAGD,KAAK,CAACC,QAAQ,CAAC;;OAE9D,MAAM,IAAIF,GAAG,IAAI,eAAe,EAAE;QACjC,KAAK,MAAMG,UAAU,IAAIF,KAAK,EAAE;UAC9B,IAAIA,KAAK,CAACE,UAAU,CAAC,EAAEJ,UAAU,CAACI,UAAU,CAAC,GAAGF,KAAK,CAACE,UAAU,CAAC;;OAEpE,MAAM;QACL,IAAIF,KAAK,EAAEF,UAAU,CAACC,GAAG,CAAC,GAAGC,KAAK;;;IAItC,IAAInH,YAAY,GAAG+D,MAAM,EAAEtB,aAAa,EAAEqE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACjD,SAAS,IAAI,IAAI,CAACZ,oBAAoB,CAACiB,cAAc,CAAC,IAAI,CAAC,CACzE;IACD8C,UAAU,CAAC,SAAS,CAAC,GAAGjH,YAAY,EAAExB,OAAO;IAE7C,OAAOyI,UAAU;EACnB;EAEAK,WAAWA,CAAA;IACT;IACA,IAAI,CAACvB,eAAe,CAACwB,IAAI,EAAE;IAC3B,IAAI,CAACxB,eAAe,CAACyB,QAAQ,EAAE;IAC/B;IACA,IAAI,CAAC9I,iBAAiB,CAAC+I,WAAW,EAAE;EACtC;EAEA;EACAjF,iBAAiBA,CAACC,aAAa;IAC7B;IACA,IAAIiF,MAAM,GAAGjF,aAAa,CAACqE,IAAI,CAC5B9G,YAAY,IACXA,YAAY,CAAC8D,SAAS,KAAK,IAAI,CAACZ,oBAAoB,CAACiB,cAAc,CAAC/F,EAAE,CACzE;IACD,IAAIsJ,MAAM,EAAE;MACV,OAAO,IAAI;KACZ,MAAM;MACL,OAAO,KAAK;;EAEhB;EAEArG,OAAOA,CAACH,MAAM;IACZA,MAAM,CAACyG,MAAM,CAACC,GAAG,GAAG,2CAA2C;EACjE;EAEAjK,kBAAkBA,CAACgC,MAAM;IACvB,QAAQA,MAAM;MACZ,KAAKxD,SAAS,CAACmG,eAAe,CAACuF,OAAO;QACpC,OAAO,kBAAkB;MAC3B,KAAK1L,SAAS,CAACmG,eAAe,CAACC,cAAc;QAC3C,OAAO,oBAAoB;MAC7B,KAAKpG,SAAS,CAACmG,eAAe,CAACwF,OAAO;QACpC,OAAO,qBAAqB;MAC9B,KAAK3L,SAAS,CAACmG,eAAe,CAACyF,SAAS;QACtC,OAAO,qBAAqB;MAC9B;QACE,OAAO,uBAAuB;;EAEpC;EAEA5I,OAAOA,CAAC4E,MAAM,EAAEiE,eAAe,EAAEC,aAAa,GAAG,IAAI;IAGnD,IACEA,aAAa,IACbA,aAAa,CAAC9I,OAAO,IACrB8I,aAAa,CAAC9I,OAAO,CAAC+I,WAAW,EACjC;MACA;MACAC,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC9I,OAAO,CAAC+I,WAAW,EAAE,QAAQ,CAAC;MACxD;;IAEF;IACA,IAAIG,QAAQ,GAAG,IAAI,CAAC/E,aAAa,CAAC8E,IAAI,CAAChM,uBAAuB,EAAE;MAC9DkM,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,CAAC;IACF,IAAIC,WAAW,GAAG,GAAG1E,MAAM,CAAChC,IAAI,CAACC,UAAU,IAAI+B,MAAM,CAAChC,IAAI,CAACE,SAAS,mBAAmB,IAAI,CAAClB,MAAM,CAACJ,IAAI,EAAE;IACzG0H,QAAQ,CAACK,iBAAiB,CAACC,cAAc,GAAG,IAAI;IAChDN,QAAQ,CAACK,iBAAiB,CAACE,QAAQ,GAAG,CACpC;MACEjI,IAAI,EAAE8H,WAAW;MACjBI,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,IAAI,CAAC/H,MAAM,CAACC;KACpB,CACF;IACDqH,QAAQ,CAACK,iBAAiB,CAACD,WAAW,GAAGA,WAAW;IACpDJ,QAAQ,CAACK,iBAAiB,CAACK,YAAY,GAAG,GAAG1M,WAAW,CAAC2M,MAAM,kBAAkB;IACjFX,QAAQ,CAACK,iBAAiB,CAACO,WAAW,CAACpE,SAAS,CAAEqE,QAAQ,IAAI;MAE5D,IAAIpE,IAAI,GAAGoE,QAAQ,CAACpE,IAAI;MACxB,IAAIqE,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC7BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEvE,IAAI,CAAC1G,EAAE,CAAC;MAC1C+K,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEvE,IAAI,CAACnF,MAAM,CAAC;MAC9CwJ,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEvE,IAAI,CAACwE,MAAM,CAAC;MACnCH,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAErB,eAAe,CAAC;MACnD,IAAI,CAAC9E,oBAAoB,CAACqG,YAAY,CAACJ,QAAQ,CAAC,CAACtE,SAAS,CAAEC,IAAI,IAAI;QAClE5I,IAAI,CAAC+I,IAAI,CAAC;UACRC,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAEL,IAAI,CAACM,OAAO;UAClBC,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE,IAAI,CAACjC,iBAAiB,CAACa,OAAO,CAAC,IAAI;SACvD,CAAC;QACF,IAAI,CAAC8B,aAAa,EAAE;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAAC,QAAAwD,CAAA;qBAtVU1G,qBAAqB,EAAAxG,EAAA,CAAAmN,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArN,EAAA,CAAAmN,iBAAA,CAAAnN,EAAA,CAAAsN,iBAAA,GAAAtN,EAAA,CAAAmN,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAAxN,EAAA,CAAAmN,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA1N,EAAA,CAAAmN,iBAAA,CAAAQ,EAAA,CAAAC,kBAAA,GAAA5N,EAAA,CAAAmN,iBAAA,CAAAU,EAAA,CAAAC,gBAAA,GAAA9N,EAAA,CAAAmN,iBAAA,CAAAY,EAAA,CAAAC,QAAA,GAAAhO,EAAA,CAAAmN,iBAAA,CAAAc,EAAA,CAAAC,eAAA,GAAAlO,EAAA,CAAAmN,iBAAA,CAAAgB,EAAA,CAAAC,cAAA,GAAApO,EAAA,CAAAmN,iBAAA,CAAAkB,EAAA,CAAAC,KAAA,GAAAtO,EAAA,CAAAmN,iBAAA,CAAAoB,GAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;UAArBjI,qBAAqB;IAAAkI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzBlChP,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAA+B,SAAA,4BAAyE;QAEzE/B,EAAA,CAAAC,cAAA,iBAAiC;QAG3BD,EAAA,CAAA+C,UAAA,IAAAmM,mCAAA,gBAMK;QACLlP,EAAA,CAAA+C,UAAA,IAAAoM,mCAAA,gBAEK;QACPnP,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAA+C,UAAA,IAAAqM,oCAAA,iBAgBM;QACRpP,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAA8B;QAC5BD,EAAA,CAAA+C,UAAA,KAAAsM,qCAAA,mBAgKM;QACRrP,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAA+C,UAAA,KAAAuM,qCAAA,kBAgBM;QACRtP,EAAA,CAAAG,YAAA,EAAU;QAIVH,EAAA,CAAAC,cAAA,wBAIC;QACCD,EAAA,CAAA+B,SAAA,mCAI0B;QAC5B/B,EAAA,CAAAG,YAAA,EAAe;;;QAvOKH,EAAA,CAAAI,SAAA,GAA+B;QAA/BJ,EAAA,CAAAgC,UAAA,kBAAAiN,GAAA,CAAA/F,aAAA,CAA+B;QAKxClJ,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAgC,UAAA,SAAAiN,GAAA,CAAA5H,OAAA,CAAAkD,MAAA,MAAyB;QAOzBvK,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAgC,UAAA,SAAAiN,GAAA,CAAA5H,OAAA,CAAAkD,MAAA,KAAwB;QAM5BvK,EAAA,CAAAI,SAAA,GAA0D;QAA1DJ,EAAA,CAAAgC,UAAA,SAAAiN,GAAA,CAAA5H,OAAA,CAAAkD,MAAA,SAAA0E,GAAA,CAAA1O,mBAAA,WAA0D;QAoBxCP,EAAA,CAAAI,SAAA,GAAU;QAAVJ,EAAA,CAAAgC,UAAA,YAAAiN,GAAA,CAAA5H,OAAA,CAAU;QAoK9BrH,EAAA,CAAAI,SAAA,GAA0D;QAA1DJ,EAAA,CAAAgC,UAAA,SAAAiN,GAAA,CAAA5H,OAAA,CAAAkD,MAAA,SAAA0E,GAAA,CAAA1O,mBAAA,WAA0D;QAyB3DP,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAgC,UAAA,sBAAAiN,GAAA,CAAA7M,iBAAA,CAAuC,kBAAA6M,GAAA,CAAA1H,aAAA", "names": ["takeUntil", "Subject", "EventEmitter", "<PERSON><PERSON>", "AppConfig", "StripeCheckoutComponent", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "player_registration", "ɵɵpipeBind1", "ɵɵlistener", "SelectPlayerComponent_div_8_Template_button_click_1_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "openRegisterNewPlayer", "ɵɵtextInterpolate", "ctx_r2", "ɵɵclassMap", "ctx_r8", "getBadgeValidation", "player_r7", "validate_status", "SelectPlayerComponent_div_10_button_14_Template_button_click_0_listener", "_r15", "$implicit", "ctx_r13", "selectPlayer", "ɵɵpropertyInterpolate1", "id", "ɵɵelement", "ɵɵproperty", "nearestRegis", "club_id", "ctx_r10", "eventUpdatePlayer", "SelectPlayerComponent_div_10_div_18_button_19_Template_button_click_0_listener", "_r22", "ctx_r20", "reRegister", "SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_button_9_Template_button_click_0_listener", "_r29", "item_r24", "ctx_r27", "payment", "ɵɵelementContainerStart", "ɵɵtemplate", "SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_button_9_Template", "ɵɵelementContainerEnd", "ctx_r25", "_commonService", "getBadgeClassPayment", "status", "toLowerCase", "ɵɵsanitizeHtml", "type", "PAYMENT_DETAIL_TYPES", "registration", "includes", "PAYMENT_STATUS_SENT", "SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_ng_container_1_Template", "ctx_r23", "SelectPlayerComponent_div_10_div_18_div_20_ng_container_1_Template", "payment_details", "SelectPlayerComponent_div_10_div_18_button_19_Template", "SelectPlayerComponent_div_10_div_18_div_20_Template", "ɵɵtextInterpolate2", "club", "name", "ctx_r11", "getBadgeRegistration", "approval_status", "season", "fee", "SelectPlayerComponent_div_10_Template_img_error_6_listener", "$event", "_r35", "ctx_r34", "onError", "SelectPlayerComponent_div_10_span_13_Template", "SelectPlayerComponent_div_10_button_14_Template", "SelectPlayerComponent_div_10_app_select_club_module_17_Template", "SelectPlayerComponent_div_10_div_18_Template", "photo", "ɵɵsanitizeUrl", "ctx_r3", "name_settings", "is_on", "user", "first_name", "last_name", "ɵɵpipeBind2", "dob", "is_validate_required", "appConfig", "VALIDATE_STATUS", "AwaitingUpdate", "checkRegistration", "registrations", "SelectPlayerComponent_div_11_Template_button_click_1_listener", "_r37", "ctx_r36", "ctx_r4", "SelectPlayerComponent", "constructor", "_router", "cd", "_registrationService", "_loadingService", "_coreSidebarService", "_translateService", "_modalService", "_settingsService", "_titleService", "_clubService", "model", "players", "clubs", "currentPlayer", "season_id", "player", "PAYMENT_STATUS_PAID", "setTitle", "instant", "selectedS<PERSON>on", "initSettingsValue", "hasOwnProperty", "getAllClubsIsActive", "JSON", "parse", "localStorage", "getItem", "console", "log", "subscribe", "data", "allClubs", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "link", "_unsubscribeAll", "getAllPlayers", "REGISTER_TYPE", "edit", "detectChanges", "new", "getSidebarRegistry", "toggle<PERSON><PERSON>", "awaiting_update", "updatePlayerInfo", "reRegisterPlayer", "pipe", "show", "for<PERSON>ach", "length", "find", "reg", "unpaidPayment", "temp_model", "key", "value", "user_key", "custom_key", "ngOnDestroy", "next", "complete", "unsubscribe", "result", "target", "src", "Pending", "Updated", "Validated", "registration_id", "paymentDetail", "payment_url", "window", "open", "modalRef", "size", "centered", "backdrop", "description", "componentInstance", "closeOnSuccess", "products", "quantity", "price", "api_checkout", "apiUrl", "onSucceeded", "response", "formData", "FormData", "append", "amount", "storePayment", "_", "ɵɵdirectiveInject", "i1", "Router", "ChangeDetectorRef", "i2", "RegistrationService", "i3", "LoadingService", "i4", "CoreSidebarService", "i5", "TranslateService", "i6", "NgbModal", "i7", "SettingsService", "i8", "CommonsService", "i9", "Title", "i10", "ClubService", "_2", "selectors", "decls", "vars", "consts", "template", "SelectPlayerComponent_Template", "rf", "ctx", "SelectPlayerComponent_h6_6_Template", "SelectPlayerComponent_h6_7_Template", "SelectPlayerComponent_div_8_Template", "SelectPlayerComponent_div_10_Template", "SelectPlayerComponent_div_11_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\registration\\select-player\\select-player.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\registration\\select-player\\select-player.component.html"], "sourcesContent": ["import { ClubService } from './../../../services/club.service';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { Subject } from 'rxjs';\r\nimport { RegistrationService } from './../../../services/registration.service';\r\nimport { Router } from '@angular/router';\r\nimport { ChangeDetectorRef, Component, EventEmitter, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport Swal from 'sweetalert2';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { StripeCheckoutComponent } from 'app/components/stripe-checkout/stripe-checkout.component';\r\nimport { environment } from 'environments/environment';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { CurrentPlayer } from 'app/interfaces/players';\r\n\r\n@Component({\r\n  selector: 'app-select-player',\r\n  templateUrl: './select-player.component.html',\r\n  styleUrls: ['./select-player.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class SelectPlayerComponent implements OnInit {\r\n  // public variables\r\n  is_validate_required = true;\r\n  model: any = {};\r\n  public player_registration: boolean = false;\r\n  public contentHeader: object;\r\n  public players = [];\r\n  public clubs = [];\r\n  public season: any;\r\n  public appConfig = AppConfig;\r\n  public currentPlayer: CurrentPlayer = {\r\n    season_id: null,\r\n    player: null,\r\n    type: null\r\n  };\r\n  public name_settings : any;\r\n\r\n  public eventUpdatePlayer = new EventEmitter();\r\n  PAYMENT_DETAIL_TYPES = AppConfig.PAYMENT_DETAIL_TYPES;\r\n  PAYMENT_STATUS_PAID = AppConfig.PAYMENT_STATUS_PAID;\r\n  PAYMENT_STATUS_SENT = AppConfig.PAYMENT_STATUS_SENT;\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  constructor(\r\n    private _router: Router,\r\n    private cd: ChangeDetectorRef,\r\n    private _registrationService: RegistrationService,\r\n    private _loadingService: LoadingService,\r\n    private _coreSidebarService: CoreSidebarService,\r\n    public _translateService: TranslateService,\r\n    public _modalService: NgbModal,\r\n    public _settingsService: SettingsService,\r\n    public _commonService: CommonsService,\r\n    public _titleService: Title,\r\n    public _clubService: ClubService\r\n  ) {\r\n    this._titleService.setTitle(\r\n      `${_translateService.instant(\r\n        this._registrationService.selectedSeason['type']\r\n      )} ${this._registrationService.selectedSeason['name']}`\r\n    );\r\n    if (\r\n      _settingsService.initSettingsValue &&\r\n      _settingsService.initSettingsValue.hasOwnProperty('is_validate_required')\r\n    ) {\r\n      this.is_validate_required =\r\n        _settingsService.initSettingsValue.is_validate_required;\r\n      this.player_registration =\r\n        _settingsService.initSettingsValue.player_registration;\r\n    }\r\n    this.getAllClubsIsActive();\r\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n    console.log('name_settings: ', this.name_settings);\r\n  }\r\n\r\n  getAllClubsIsActive() {\r\n    // get all clubs\r\n    this._clubService.getAllClubsIsActive().subscribe(\r\n      (data) => {\r\n        // set data to clubs\r\n        this.clubs = data;\r\n        // set data to allClubs\r\n        this._registrationService.allClubs = data;\r\n      },\r\n      (error) => {\r\n        // show error\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: 'OK'\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: `${this._translateService.instant(\r\n        this._registrationService.selectedSeason['type']\r\n      )} ${this._registrationService.selectedSeason['name']}`,\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Application',\r\n            isLink: true,\r\n            link: '/registration/select-event'\r\n          },\r\n          {\r\n            name: `${this._translateService.instant(\r\n              this._registrationService.selectedSeason['type']\r\n            )} ${this._registrationService.selectedSeason['name']}`,\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this.season = this._registrationService.selectedSeason;\r\n\r\n    // define default values for unsubscribe all\r\n    this._unsubscribeAll = new Subject();\r\n\r\n    // get players of this season\r\n    this.getAllPlayers();\r\n\r\n    // listen to event update player\r\n    this.eventUpdatePlayer.subscribe(() => {\r\n      this.getAllPlayers();\r\n      this.currentPlayer.type = this.appConfig.REGISTER_TYPE.edit;\r\n      this.currentPlayer.player = {};\r\n    });\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  openRegisterNewPlayer() {\r\n    this.currentPlayer.player = {};\r\n    this.currentPlayer.type = this.appConfig.REGISTER_TYPE.new;\r\n    this._coreSidebarService\r\n      .getSidebarRegistry('register-new-player')\r\n      .toggleOpen();\r\n  }\r\n\r\n  selectPlayer(player) {\r\n    this.currentPlayer.season_id = this.season.id;\r\n    this.currentPlayer.player = player;\r\n    this.currentPlayer.type = this.appConfig.REGISTER_TYPE.edit;\r\n    if (\r\n      player.validate_status == this.appConfig.VALIDATE_STATUS.AwaitingUpdate\r\n    ) {\r\n      this.currentPlayer.type = this.appConfig.REGISTER_TYPE.awaiting_update;\r\n    }\r\n\r\n    this._coreSidebarService\r\n      .getSidebarRegistry('register-new-player')\r\n      .toggleOpen();\r\n  }\r\n\r\n  reRegister(player) {\r\n    // this.currentPlayer.season_id = this.season.id;\r\n    // this.currentPlayer.player = player;\r\n    // this.currentPlayer.type = this.appConfig.REGISTER_TYPE.reregister;\r\n    // this._coreSidebarService\r\n    //   .getSidebarRegistry('register-new-player')\r\n    //   .toggleOpen();\r\n\r\n    this.model = this.updatePlayerInfo(player);\r\n    this._registrationService\r\n      .reRegisterPlayer(this.season.id, this.model)\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe(\r\n        (data) => {\r\n          this.getAllPlayers();\r\n          Swal.fire({\r\n            title: 'Success!',\r\n            text: this._translateService.instant(\r\n              'Player re-registered successfully'\r\n            ),\r\n            icon: 'success',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n        },\r\n        (error) => {\r\n          Swal.fire({\r\n            title: 'Error!',\r\n            text: error.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n        }\r\n      );\r\n  }\r\n\r\n  // get players of this season\r\n  getAllPlayers() {\r\n    this._loadingService.show();\r\n    this._registrationService\r\n      .getAllPlayers()\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe(\r\n        (data) => {\r\n          this.players = data['players'];\r\n          // find registrations of this season\r\n          this.players.forEach((player) => {\r\n            if (player.registrations.length == 0) {\r\n              player.nearestRegis = null;\r\n              return;\r\n            }\r\n            let nearestRegis = player.registrations.find(\r\n              (reg) =>\r\n                reg.season_id == this._registrationService.selectedSeason['id']\r\n            );\r\n            if (nearestRegis) {\r\n              const unpaidPayment = nearestRegis.payment_details.find((payment) => (payment.type === this.PAYMENT_DETAIL_TYPES.registration || payment.type.includes('EXTRA_GROUP')) &&\r\n                this.PAYMENT_STATUS_SENT.includes(\r\n                  payment.status.toLowerCase()\r\n                ));\r\n\r\n\r\n\r\n              player.nearestRegis = {\r\n                ...nearestRegis,\r\n                payment_details: unpaidPayment ? [nearestRegis.payment_details[nearestRegis.payment_details.length - 1]] : [] // Get latest payment of registration\r\n              };\r\n\r\n              console.log(\"🚀 ~ SelectPlayerComponent ~ this.players.forEach ~ player.nearestRegis:\", player.nearestRegis)\r\n\r\n\r\n\r\n            } else {\r\n              player.nearestRegis = player.registrations[0];\r\n\r\n            }\r\n          });\r\n        },\r\n        (error) => {\r\n          Swal.fire({\r\n            title: 'Error!',\r\n            text: error.error.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n        }\r\n      );\r\n  }\r\n\r\n  updatePlayerInfo(player) {\r\n    let temp_model = {};\r\n    for (const key in player) {\r\n      let value = player[key];\r\n      if (key == 'user') {\r\n        for (const user_key in value) {\r\n          if (value[user_key]) temp_model[user_key] = value[user_key];\r\n        }\r\n      } else if (key == 'custom_fields') {\r\n        for (const custom_key in value) {\r\n          if (value[custom_key]) temp_model[custom_key] = value[custom_key];\r\n        }\r\n      } else {\r\n        if (value) temp_model[key] = value;\r\n      }\r\n    }\r\n\r\n    let registration = player?.registrations?.find(\r\n      (reg) => reg.season_id == this._registrationService.selectedSeason['id']\r\n    );\r\n    temp_model['club_id'] = registration?.club_id;\r\n\r\n    return temp_model;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n    // detroy eventUpdatePlayer\r\n    this.eventUpdatePlayer.unsubscribe();\r\n  }\r\n\r\n  // check if player is registered\r\n  checkRegistration(registrations) {\r\n    // check in array of registrations have item = selected season\r\n    let result = registrations.find(\r\n      (registration) =>\r\n        registration.season_id === this._registrationService.selectedSeason.id\r\n    );\r\n    if (result) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  onError($event) {\r\n    $event.target.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n  }\r\n\r\n  getBadgeValidation(status) {\r\n    switch (status) {\r\n      case AppConfig.VALIDATE_STATUS.Pending:\r\n        return 'badge-light-info';\r\n      case AppConfig.VALIDATE_STATUS.AwaitingUpdate:\r\n        return 'badge-light-danger';\r\n      case AppConfig.VALIDATE_STATUS.Updated:\r\n        return 'badge-light-warning';\r\n      case AppConfig.VALIDATE_STATUS.Validated:\r\n        return 'badge-light-success';\r\n      default:\r\n        return 'badge-light-secondary';\r\n    }\r\n  }\r\n\r\n  payment(player, registration_id, paymentDetail = null) {\r\n\r\n\r\n    if (\r\n      paymentDetail &&\r\n      paymentDetail.payment &&\r\n      paymentDetail.payment.payment_url\r\n    ) {\r\n      // open payment url in new tab\r\n      window.open(paymentDetail.payment.payment_url, '_blank');\r\n      return;\r\n    }\r\n    // Define modal payment\r\n    let modalRef = this._modalService.open(StripeCheckoutComponent, {\r\n      size: 'sm',\r\n      centered: true,\r\n      backdrop: 'static'\r\n    });\r\n    let description = `${player.user.first_name} ${player.user.last_name} pay for season ${this.season.name}`;\r\n    modalRef.componentInstance.closeOnSuccess = true;\r\n    modalRef.componentInstance.products = [\r\n      {\r\n        name: description,\r\n        quantity: 1,\r\n        price: this.season.fee\r\n      }\r\n    ];\r\n    modalRef.componentInstance.description = description;\r\n    modalRef.componentInstance.api_checkout = `${environment.apiUrl}/stripe/checkout`;\r\n    modalRef.componentInstance.onSucceeded.subscribe((response) => {\r\n\r\n      let data = response.data;\r\n      let formData = new FormData();\r\n      formData.append('payment_intent', data.id);\r\n      formData.append('payment_status', data.status);\r\n      formData.append('fee', data.amount);\r\n      formData.append('registration_id', registration_id);\r\n      this._registrationService.storePayment(formData).subscribe((data) => {\r\n        Swal.fire({\r\n          title: 'Success!',\r\n          text: data.message,\r\n          icon: 'success',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n        this.getAllPlayers();\r\n      });\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <!-- Player registration status start -->\r\n    <section id=\"select-player-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-sm-6\">\r\n          <h6 *ngIf=\"players.length == 0\" class=\"my-2 text-secondary\">\r\n            {{\r\n              player_registration\r\n                ? 'Please click \"Register Player\" to register'\r\n                : ('Please click \"Register your child\" to register' | translate)\r\n            }}\r\n          </h6>\r\n          <h6 *ngIf=\"players.length > 0\" class=\"my-2 text-secondary\">\r\n            {{ 'Player registration status' | translate }}\r\n          </h6>\r\n        </div>\r\n        <div\r\n          class=\"col-6 mt-auto mb-auto d-none d-sm-inline-block\"\r\n          *ngIf=\"players.length == 0 || player_registration === false\"\r\n        >\r\n          <button\r\n            data-testid=\"btnAddNewPlayer\"\r\n            id=\"btn-register-top\"\r\n            class=\"btn btn-primary float-right\"\r\n            (click)=\"openRegisterNewPlayer()\"\r\n          >\r\n            <span>{{\r\n                player_registration\r\n                  ? 'Register Player'\r\n                  : ('Register your child' | translate)\r\n              }}</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row mb-4 mb-sm-4\">\r\n        <div\r\n          class=\"col-lg-4 col-md-6 col-12 mb-2\"\r\n          *ngFor=\"let player of players\"\r\n        >\r\n          <div class=\"card mb-2\" id=\"card-player-{{ player.id }}\">\r\n            <div class=\"card-body\">\r\n              <div\r\n                class=\"d-flex justify-content-between align-items-center mb-1\"\r\n              >\r\n                <div class=\"media\">\r\n                  <div class=\"avatar avatar-xl mr-75\">\r\n                    <img\r\n                      [src]=\"player.photo\"\r\n                      style=\"object-fit: cover\"\r\n                      class=\"rounded\"\r\n                      alt=\"Avatar\"\r\n                      (error)=\"onError($event)\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"media-body my-auto\">\r\n                    <h4 class=\"mb-0 text-break\">\r\n                      {{ name_settings?.is_on == 1 \r\n                        ? player.user.first_name + ' ' + player.user.last_name \r\n                        : player.user.last_name + ' ' + player.user.first_name }}\r\n                    </h4>\r\n                    <small class=\"text-muted\">{{\r\n                        player.dob | localizedDate : 'dd-MMM-yyyy'\r\n                      }}</small>\r\n                    <span\r\n                      *ngIf=\"is_validate_required\"\r\n                      class=\"d-table badge badge-pill text-capitalize\"\r\n                      [class]=\"getBadgeValidation(player.validate_status)\"\r\n                    >\r\n                      {{ player.validate_status | translate }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  *ngIf=\"\r\n                    is_validate_required &&\r\n                    player.validate_status ==\r\n                      appConfig.VALIDATE_STATUS.AwaitingUpdate\r\n                  \"\r\n                  class=\"btn btn-outline-primary btn-sm\"\r\n                  (click)=\"selectPlayer(player)\"\r\n                  id=\"btn-edit-player-{{ player.id }}\"\r\n                >\r\n                  {{ 'Edit' | translate }}\r\n                </button>\r\n              </div>\r\n              <hr />\r\n              <div>\r\n                <!-- Register old player modal -->\r\n                <app-select-club-module\r\n                  *ngIf=\"!checkRegistration(player.registrations)\"\r\n                  class=\"players-description\"\r\n                  [playerId]=\"player['id']\"\r\n                  [nearestClub]=\"player.nearestRegis?.club_id\"\r\n                  [eventUpdatePlayer]=\"eventUpdatePlayer\"\r\n                >\r\n                </app-select-club-module>\r\n                <div *ngIf=\"checkRegistration(player.registrations)\">\r\n                  <div class=\"row\">\r\n                    <div class=\"col\">\r\n                      <h6 class=\"font-weight-bolder\">\r\n                        {{ 'Registration Information' | translate }}\r\n                      </h6>\r\n                      <div class=\"text-secondary\">\r\n                        <i data-feather=\"map-pin\" class=\"mr-1 mt-25\"></i>\r\n                        <span>\r\n                          {{ 'Club' | translate }}:\r\n                          {{ player.nearestRegis?.club?.name }}\r\n                        </span>\r\n                      </div>\r\n                      <div class=\"text-secondary\">\r\n                        <i data-feather=\"bookmark\" class=\"mr-1 mt-25\"></i>\r\n                        <span>\r\n                          {{ 'Status' | translate }}:\r\n                          <small\r\n                            class=\"badge badge-pill text-capitalize\"\r\n                            [class]=\"\r\n                              _commonService.getBadgeRegistration(\r\n                                player.nearestRegis.approval_status\r\n                              )\r\n                            \"\r\n                          >{{\r\n                              player.nearestRegis.approval_status | translate\r\n                            }}</small\r\n                          >\r\n                        </span>\r\n                        <button\r\n                          *ngIf=\"\r\n                            player.nearestRegis.approval_status == 'Cancelled'\r\n                          \"\r\n                          class=\"btn btn-block btn-outline-danger btn-sm mt-2\"\r\n                          (click)=\"reRegister(player)\"\r\n                        >\r\n                          {{ 'Re-Register' | translate }}\r\n                        </button>\r\n                      </div>\r\n                      <div *ngIf=\"season.fee > 0\">\r\n                        <ng-container\r\n                          *ngFor=\"\r\n                            let item of player.nearestRegis.payment_details\r\n                          \"\r\n                        >\r\n                          <ng-container\r\n                            *ngIf=\"\r\n                              (item.type === PAYMENT_DETAIL_TYPES.registration ||\r\n                              item.type.includes('EXTRA_GROUP'))  &&\r\n                              item.status.toLowerCase() != 'draft'\r\n                            \"\r\n                          >\r\n                            <div class=\"text-secondary\">\r\n                              <div>\r\n                                <i\r\n                                  class=\"fa-light fa-money-check-dollar mr-1 mt-25\"\r\n                                ></i>\r\n                                <span class=\"text-capitalize\">\r\n                                  {{ 'Payment status' | translate }}:\r\n                                  <span\r\n                                    [innerHTML]=\"\r\n                                      _commonService.getBadgeClassPayment(\r\n                                        item.status.toLowerCase()\r\n                                      )\r\n                                    \"\r\n                                  ></span>\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                            <div>\r\n                              <button\r\n                                class=\"btn btn-block btn-outline-warning btn-sm mt-2\"\r\n                                *ngIf=\"\r\n                                  (item.type === PAYMENT_DETAIL_TYPES.registration ||\r\n                              item.type.includes('EXTRA_GROUP')) &&\r\n                                  PAYMENT_STATUS_SENT.includes(\r\n                                    item.status.toLowerCase()\r\n                                  )\r\n                                \"\r\n                                (click)=\"\r\n                                  payment(player, player.nearestRegis.id, item)\r\n                                \"\r\n                              >\r\n                                {{ 'Pay now' | translate }}\r\n                              </button>\r\n                            </div>\r\n                          </ng-container>\r\n                        </ng-container>\r\n                        <!-- <button class=\"btn btn-outline-warning btn-sm\"\r\n                        *ngIf=\"player.nearestRegis.payment_details.length == 0\" (click)=\"payment(player,player.nearestRegis.id)\">\r\n                        {{ 'Pay now' | translate }}\r\n                      </button> -->\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Add new player button -->\r\n      <div\r\n        class=\"col-12 px-1 d-sm-none d-inline-block sticky-sm-bottom\"\r\n        *ngIf=\"players.length == 0 || player_registration === false\"\r\n      >\r\n        <button\r\n          data-testid=\"btnAddNewPlayer\"\r\n          id=\"btn-register-bot\"\r\n          class=\"btn btn-block btn-primary\"\r\n          (click)=\"openRegisterNewPlayer()\"\r\n        >\r\n          <span>{{\r\n              player_registration\r\n                ? 'Register Player'\r\n                : ('Register your child' | translate)\r\n            }}</span>\r\n        </button>\r\n      </div>\r\n    </section>\r\n    <!-- Player registration status end -->\r\n\r\n    <!-- Register new player modal -->\r\n    <core-sidebar\r\n      class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n      name=\"register-new-player\"\r\n      overlayClass=\"modal-backdrop\"\r\n    >\r\n      <app-register-new-player\r\n        [eventUpdatePlayer]=\"eventUpdatePlayer\"\r\n        [registerParam]=\"currentPlayer\"\r\n      >\r\n      </app-register-new-player>\r\n    </core-sidebar>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
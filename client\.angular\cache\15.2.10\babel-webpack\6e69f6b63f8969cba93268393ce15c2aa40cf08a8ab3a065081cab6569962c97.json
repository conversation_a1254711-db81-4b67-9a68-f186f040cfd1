{"ast": null, "code": "import { EventEmitter } from \"@angular/core\";\nimport { animate, style } from \"@angular/animations\";\nimport { DOCUMENT } from \"@angular/common\";\nimport { Subject } from \"rxjs\";\nimport { takeUntil } from \"rxjs/operators\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/config.service\";\nimport * as i2 from \"@core/services/media.service\";\nimport * as i3 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i4 from \"@angular/animations\";\nimport * as i5 from \"@angular/flex-layout\";\nconst _c0 = [\"*\"];\nexport class CoreSidebarComponent {\n  // set overlayClickEvent function\n  setOverlayClickEvent(event) {\n    // console.log(\"setOverlayClickEvent\", event);\n    this.overlayClickEvent = event;\n  }\n  // set onOpenEvent function\n  setOnOpenEvent(event) {\n    // console.log(\"setOnOpenEvent\", event);\n    this.onOpenEvent = event;\n  }\n  onKeydownHandler(event) {\n    if (this.hideOnEsc) {\n      this.close();\n    }\n  }\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {DOCUMENT} document\r\n   * @param {Renderer2} _renderer\r\n   * @param {ElementRef} _elementRef\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {ChangeDetectorRef} _changeDetectorRef\r\n   * @param {CoreMediaService} _coreMediaService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {AnimationBuilder} _animationBuilder\r\n   * @param {MediaObserver} _mediaObserver\r\n   */\n  constructor(document, _renderer, _elementRef, _coreConfigService, _changeDetectorRef, _coreMediaService, _coreSidebarService, _animationBuilder, _mediaObserver) {\n    this.document = document;\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n    this._coreConfigService = _coreConfigService;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._coreMediaService = _coreMediaService;\n    this._coreSidebarService = _coreSidebarService;\n    this._animationBuilder = _animationBuilder;\n    this._mediaObserver = _mediaObserver;\n    this._overlay = null;\n    // Set Defaults\n    this.isOpened = false;\n    this.overlayVisibility = true;\n    this.hideOnEsc = false;\n    // Layout root element\n    this.rootElement = this.document.querySelectorAll(\".vertical-layout\")[0] || this.document.querySelectorAll(\".horizontal-layout\")[0];\n    this.collapsedChangedEvent = new EventEmitter();\n    this.openedChangedEvent = new EventEmitter();\n    // Set Private Defaults\n    this._collapsed = false;\n    this._unsubscribeAll = new Subject();\n  }\n  // Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Collapsed\r\n   *\r\n   * @param {boolean} value\r\n   */\n  set collapsed(value) {\n    // Set the collapsed value\n    this._collapsed = value;\n    // If the sidebar is closed, return\n    if (!this.isOpened) {\n      this.rootElement.classList.add(\"menu-expanded\"); // Add menu expanded class default\n      return;\n    }\n    // If Collapsed\n    if (value) {\n      // Collapse the sidebar\n      this.collapse();\n      // Add menu-collapsed in body and remove menu-expanded\n      this.rootElement.classList.add(\"menu-collapsed\");\n      this.rootElement.classList.remove(\"menu-expanded\");\n    }\n    // If Expanded\n    else {\n      // Expanded the sidebar\n      this.expand();\n      // Add menu-expanded in body and remove menu-collapsed\n      this.rootElement.classList.add(\"menu-expanded\");\n      this.rootElement.classList.remove(\"menu-collapsed\");\n    }\n    // Emit the 'collapsedChangedEvent' event\n    this.collapsedChangedEvent.emit(this.collapsed);\n  }\n  get collapsed() {\n    return this._collapsed;\n  }\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    // Subscribe to app-config changes\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this._coreConfig = config;\n      if (config.layout.type == \"vertical\") {\n        this.menuClass = \"vertical-menu-modern\";\n      } else {\n        this.menuClass = \"horizontal-menu\";\n      }\n    });\n    // Register the sidebar\n    this._coreSidebarService.setSidebarRegistry(this.name, this);\n    // Setup collapsibleSidebar\n    this._setupCollapsibleSidebar();\n    // Default collapsed\n    this._defaultCollapsed();\n  }\n  /**\r\n   * On destroy\r\n   */\n  ngOnDestroy() {\n    // If the sidebar is collapsed, expand it to reset changes\n    if (this.collapsed) {\n      this.expand();\n    }\n    // Remove sidebar registry\n    this._coreSidebarService.removeSidebarRegistry(this.name);\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  // Private Methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Setup the collapsible sidebar handler\r\n   *\r\n   * @private\r\n   */\n  _setupCollapsibleSidebar() {\n    // Return if the collapsible sidebar breakpoint was not set from the layout\n    if (!this.collapsibleSidebar) {\n      return;\n    }\n    // Set the _wasCollapsible false for the first time\n    this._wasCollapsible = false;\n    // Set the wasCollapsed from the layout\n    this._wasCollapsed = this.collapsed;\n    // On every media(screen) change\n    this._coreMediaService.onMediaUpdate.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {\n      // Get the collapsible status\n      const isCollapsible = this._mediaObserver.isActive(this.collapsibleSidebar);\n      //! On screen resize set the config collapsed state if we have else this.collapsed\n      this._wasCollapsed = this._coreConfig.layout.menu.collapsed || this.collapsed;\n      // If sidebar is not collapsible, switch to overlay menu (On page load without resize the window)\n      // ? Improve this menu condition\n      if (!isCollapsible && this.name === \"menu\") {\n        this.rootElement.classList.remove(this.menuClass);\n        this.rootElement.classList.add(\"vertical-overlay-menu\");\n      }\n      // If the both status are the same, then return\n      if (this._wasCollapsible === isCollapsible) {\n        return;\n      }\n      // If isCollapsible is true, use collapsible sidebar\n      if (isCollapsible) {\n        // Set the collapsibleSidebar status\n        this.iscollapsibleSidebar = true;\n        // Set the the opened status to true\n        this.isOpened = true;\n        this.expanded = true; // Adde expanded class init\n        // Emit the 'openedChangedEvent' event\n        this.openedChangedEvent.emit(this.isOpened);\n        // If the sidebar was collapsed, forcefully collapse it again\n        if (this._wasCollapsed) {\n          // Collapse\n          this.collapsed = true;\n          this.expanded = false; // Remove expanded class\n          // Change detector\n          this._changeDetectorRef.markForCheck();\n        }\n        // If sidebar is collapsible, switch to collapsible menu (modern-menu)\n        if (this.name === \"menu\") {\n          this.rootElement.classList.add(this.menuClass);\n          this.rootElement.classList.remove(\"vertical-overlay-menu\", \"menu-hide\");\n        }\n        // Hide the overlay if any exists\n        this._hideOverlay();\n      }\n      // Else use overlay sidebar\n      else {\n        // Set the collapsibleSidebar status\n        this.iscollapsibleSidebar = false;\n        // Expanded the sidebar in case if it was collapsed\n        this.expand();\n        // Force the the opened status to close\n        this.isOpened = false;\n        // Emit the 'openedChangedEvent' event\n        this.openedChangedEvent.emit(this.isOpened);\n        // If sidebar is not collapsible, switch to overlay menu (On window resize)\n        this.rootElement.classList.remove(this.menuClass);\n        this.rootElement.classList.add(\"vertical-overlay-menu\");\n        // Hide the sidebar\n        this._hideSidebar();\n      }\n      // Set the new active status\n      this._wasCollapsible = isCollapsible;\n    });\n  }\n  /**\r\n   * Setup the initial collapsed status\r\n   *\r\n   * @private\r\n   */\n  _defaultCollapsed() {\n    // Return, if sidebar is not collapsed\n    if (!this.collapsed) {\n      return;\n    }\n    // Return if the sidebar is closed\n    if (!this.isOpened) {\n      return;\n    }\n    // Collapse the sidebar\n    this.collapse();\n  }\n  /**\r\n   * Show the overlay\r\n   *\r\n   * @private\r\n   */\n  _showOverlay() {\n    // Create the overlay element\n    this._overlay = this._renderer.createElement(\"div\");\n    // Add a class to the overlay element and make it visible\n    this._overlay.classList.add(this.overlayClass);\n    this._overlay.classList.add(\"show\");\n    // If overlayVisibility is false, set the bg transparent\n    if (!this.overlayVisibility) {\n      this._overlay.classList.add(\"bg-transparent\");\n    }\n    // Append the overlay element to the parent element of the sidebar\n    this._renderer.appendChild(this._elementRef.nativeElement.parentElement, this._overlay);\n    // Overlay enter animation and attach it to the animationPlayer\n    this._animationPlayer = this._animationBuilder.build([animate(\"300ms ease\", style({\n      opacity: 1\n    }))]).create(this._overlay);\n    // Play the overlay animation\n    this._animationPlayer.play();\n    // Add an event listener to the overlay, on click of it close the sidebar\n    this._overlay.addEventListener(\"click\", () => {\n      this.close();\n      // run passed function if any exists on click\n    });\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Hide the overlay\r\n   *\r\n   * @private\r\n   */\n  _hideOverlay() {\n    // If overlay is already hidden, return\n    if (!this._overlay) {\n      return;\n    }\n    // Overlay leave animation and attach it to the animationPlayer\n    this._animationPlayer = this._animationBuilder.build([animate(\"300ms ease\", style({\n      opacity: 0\n    }))]).create(this._overlay);\n    // Play the overlay leave animation\n    this._animationPlayer.play();\n    // Once the animation is done...\n    this._animationPlayer.onDone(() => {\n      // If the overlay still exists...\n      if (this._overlay) {\n        // Remove the overlay\n        this._overlay.parentNode.removeChild(this._overlay);\n        this._overlay = null;\n      }\n    });\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Change sidebar properties to make it visible\r\n   *\r\n   * @private\r\n   */\n  _showSidebar() {\n    // If menu as sidebar, add relevant classes to body to show menu\n    if (this.name == \"menu\") {\n      // Open overlay menu\n      this.rootElement.classList.add(\"menu-open\");\n      this.rootElement.classList.remove(\"menu-hide\");\n    }\n    // For default sidebar add show class to make it visible\n    else {\n      this._renderer.addClass(this._elementRef.nativeElement, \"show\");\n      // Add .modal-open from body to remove browser scroll\n      if (this.overlayClass === \"modal-backdrop\") {\n        this.rootElement.classList.add(\"modal-open\");\n      }\n    }\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Change sidebar properties to make it invisible\r\n   *\r\n   * @private\r\n   */\n  _hideSidebar() {\n    // If menu as sidebar, add relevant classes to body to show menu\n    if (this.name == \"menu\") {\n      // Hide overlay menu\n      this.rootElement.classList.remove(\"menu-open\");\n      this.rootElement.classList.add(\"menu-hide\");\n    }\n    // For default sidebar remove show class to make it visible\n    else {\n      this._renderer.removeClass(this._elementRef.nativeElement, \"show\");\n      // Remove .modal-open from body\n      if (this.overlayClass === \"modal-backdrop\") {\n        this.rootElement.classList.remove(\"modal-open\");\n      }\n    }\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  // Public Methods\n  // -----------------------------------------------------------------------------------------------------\n  // For Collapsible Sidebar\n  /**\r\n   * Collapse the temporarily expanded sidebar\r\n   */\n  collapseTemporarily() {\n    // Only work if the sidebar is collapsed\n    if (!this.collapsed) {\n      return;\n    }\n    // Collapse the sidebar back\n    this.expanded = false;\n    this.collapsed = true; // Set the menu collapsed while collapsed temp.\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Expanded the sidebar temporarily\r\n   */\n  expandTemporarily() {\n    // Only work if the sidebar is collapsed\n    if (!this.collapsed) {\n      return;\n    }\n    // Expanded the sidebar temporarily\n    this.expanded = true;\n    this.collapsed = true; // Set the menu collapsed while collapsed temp.\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * On Sidebar's Mouseenter Event\r\n   */\n  onMouseEnter() {\n    // Expand the sidebar temporarily\n    this.expandTemporarily();\n  }\n  /**\r\n   * On Sidebar's Mouseleave Event\r\n   */\n  onMouseLeave() {\n    // Collapse the sidebar temporarily\n    this.collapseTemporarily();\n  }\n  /**\r\n   * Collapse the sidebar permanently\r\n   */\n  collapse() {\n    // If the sidebar is not collapsed\n    if (this.collapsed) {\n      return;\n    }\n    // Set collapse true\n    this.collapsed = true;\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Expanded the sidebar permanently\r\n   */\n  expand() {\n    // If the sidebar is collapsed\n    if (!this.collapsed) {\n      return;\n    }\n    // Set collapse false (expanded)\n    this.collapsed = false;\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Toggle the sidebar expand/collapse permanently\r\n   */\n  toggleCollapsible() {\n    if (this.collapsed) {\n      this.expand();\n    } else {\n      this.collapse();\n    }\n  }\n  // For Overlay Sidebar\n  /**\r\n   * Open the sidebar\r\n   */\n  open() {\n    // If sidebar already open or collapsible, then return\n    if (this.isOpened || this.iscollapsibleSidebar) {\n      return;\n    }\n    // Show the sidebar\n    this._showSidebar();\n    // Show the overlay\n    this._showOverlay();\n    // Set the sidebar opened status\n    this.isOpened = true;\n    // Emit the 'openedChangedEvent' event\n    this.openedChangedEvent.emit(this.isOpened);\n    if (this.onOpenEvent) {\n      this.onOpenEvent.emit();\n    }\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Close the sidebar\r\n   */\n  close() {\n    if (this.overlayClickEvent) this.overlayClickEvent.emit();\n    // If sidebar is not open or collapsible, then return\n    if (!this.isOpened || this.iscollapsibleSidebar) {\n      return;\n    }\n    // Hide the overlay\n    this._hideOverlay();\n    // Set the opened status\n    this.isOpened = false;\n    // Emit the 'openedChangedEvent' event\n    this.openedChangedEvent.emit(this.isOpened);\n    // Hide overlay menu\n    this._hideSidebar();\n    // Change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\r\n   * Toggle open/close the sidebar\r\n   */\n  toggleOpen(open) {\n    // console.log(open);\n    if (open !== undefined) {\n      if (open) {\n        this.open();\n      } else {\n        this.close();\n      }\n      return;\n    } else {\n      if (this.isOpened) {\n        this.close();\n      } else {\n        this.open();\n      }\n    }\n  }\n  static #_ = this.ɵfac = function CoreSidebarComponent_Factory(t) {\n    return new (t || CoreSidebarComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CoreConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.CoreMediaService), i0.ɵɵdirectiveInject(i3.CoreSidebarService), i0.ɵɵdirectiveInject(i4.AnimationBuilder), i0.ɵɵdirectiveInject(i5.MediaObserver));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CoreSidebarComponent,\n    selectors: [[\"core-sidebar\"]],\n    hostVars: 2,\n    hostBindings: function CoreSidebarComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.escape\", function CoreSidebarComponent_keydown_escape_HostBindingHandler($event) {\n          return ctx.onKeydownHandler($event);\n        }, false, i0.ɵɵresolveDocument)(\"mouseenter\", function CoreSidebarComponent_mouseenter_HostBindingHandler() {\n          return ctx.onMouseEnter();\n        })(\"mouseleave\", function CoreSidebarComponent_mouseleave_HostBindingHandler() {\n          return ctx.onMouseLeave();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"expanded\", ctx.expanded);\n      }\n    },\n    inputs: {\n      name: \"name\",\n      overlayClass: \"overlayClass\",\n      collapsibleSidebar: \"collapsibleSidebar\",\n      overlayVisibility: \"overlayVisibility\",\n      hideOnEsc: \"hideOnEsc\",\n      collapsed: \"collapsed\"\n    },\n    outputs: {\n      collapsedChangedEvent: \"collapsedChangedEvent\",\n      openedChangedEvent: \"openedChangedEvent\"\n    },\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function CoreSidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAKEA,YAAY,QASP,eAAe;AACtB,SACEC,OAAO,EAGPC,KAAK,QACA,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,iBAAiB;AAG1C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;AAY1C,OAAM,MAAOC,oBAAoB;EA0C/B;EACOC,oBAAoBA,CAACC,KAAU;IACpC;IACA,IAAI,CAACC,iBAAiB,GAAGD,KAAK;EAChC;EAEA;EACOE,cAAcA,CAACF,KAAU;IAC9B;IACA,IAAI,CAACG,WAAW,GAAGH,KAAK;EAC1B;EAEqDI,gBAAgBA,CACnEJ,KAAoB;IAEpB,IAAI,IAAI,CAACK,SAAS,EAAE;MAClB,IAAI,CAACC,KAAK,EAAE;;EAEhB;EAgBA;;;;;;;;;;;;;EAaAC,YAC4BC,QAAa,EAC/BC,SAAoB,EACpBC,WAAuB,EACvBC,kBAAqC,EACrCC,kBAAqC,EACrCC,iBAAmC,EACnCC,mBAAuC,EACvCC,iBAAmC,EACnCC,cAA6B;IARX,KAAAR,QAAQ,GAARA,QAAQ;IAC1B,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IAzBhB,KAAAC,QAAQ,GAAuB,IAAI;IA2BzC;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACd,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACe,WAAW,GACd,IAAI,CAACZ,QAAQ,CAACa,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IACrD,IAAI,CAACb,QAAQ,CAACa,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAEzD,IAAI,CAACC,qBAAqB,GAAG,IAAI9B,YAAY,EAAE;IAC/C,IAAI,CAAC+B,kBAAkB,GAAG,IAAI/B,YAAY,EAAE;IAE5C;IACA,IAAI,CAACgC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,eAAe,GAAG,IAAI7B,OAAO,EAAE;EACtC;EAEA;EACA;EAEA;;;;;EAKA,IACI8B,SAASA,CAACC,KAAc;IAC1B;IACA,IAAI,CAACH,UAAU,GAAGG,KAAK;IAEvB;IACA,IAAI,CAAC,IAAI,CAACT,QAAQ,EAAE;MAClB,IAAI,CAACE,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;MACjD;;IAGF;IACA,IAAIF,KAAK,EAAE;MACT;MACA,IAAI,CAACG,QAAQ,EAAE;MAEf;MACA,IAAI,CAACV,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAChD,IAAI,CAACT,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,eAAe,CAAC;;IAEpD;IAAA,KACK;MACH;MACA,IAAI,CAACC,MAAM,EAAE;MAEb;MACA,IAAI,CAACZ,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;MAC/C,IAAI,CAACT,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,gBAAgB,CAAC;;IAGrD;IACA,IAAI,CAACT,qBAAqB,CAACW,IAAI,CAAC,IAAI,CAACP,SAAS,CAAC;EACjD;EAEA,IAAIA,SAASA,CAAA;IACX,OAAO,IAAI,CAACF,UAAU;EACxB;EAEA;EACA;EAEA;;;EAGAU,QAAQA,CAAA;IACN;IACA,IAAI,CAACvB,kBAAkB,CAACwB,MAAM,CAC3BC,IAAI,CAACvC,SAAS,CAAC,IAAI,CAAC4B,eAAe,CAAC,CAAC,CACrCY,SAAS,CAAEF,MAAM,IAAI;MACpB,IAAI,CAACG,WAAW,GAAGH,MAAM;MACzB,IAAIA,MAAM,CAACI,MAAM,CAACC,IAAI,IAAI,UAAU,EAAE;QACpC,IAAI,CAACC,SAAS,GAAG,sBAAsB;OACxC,MAAM;QACL,IAAI,CAACA,SAAS,GAAG,iBAAiB;;IAEtC,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC3B,mBAAmB,CAAC4B,kBAAkB,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC;IAE5D;IACA,IAAI,CAACC,wBAAwB,EAAE;IAE/B;IACA,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEA;;;EAGAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACpB,SAAS,EAAE;MAClB,IAAI,CAACM,MAAM,EAAE;;IAGf;IACA,IAAI,CAAClB,mBAAmB,CAACiC,qBAAqB,CAAC,IAAI,CAACJ,IAAI,CAAC;IAEzD;IACA,IAAI,CAAClB,eAAe,CAACuB,IAAI,EAAE;IAC3B,IAAI,CAACvB,eAAe,CAACwB,QAAQ,EAAE;EACjC;EAEA;EACA;EAEA;;;;;EAKQL,wBAAwBA,CAAA;IAC9B;IACA,IAAI,CAAC,IAAI,CAACM,kBAAkB,EAAE;MAC5B;;IAGF;IACA,IAAI,CAACC,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAAC1B,SAAS;IAEnC;IACA,IAAI,CAACb,iBAAiB,CAACwC,aAAa,CACjCjB,IAAI,CAACvC,SAAS,CAAC,IAAI,CAAC4B,eAAe,CAAC,CAAC,CACrCY,SAAS,CAAC,MAAK;MACd;MACA,MAAMiB,aAAa,GAAG,IAAI,CAACtC,cAAc,CAACuC,QAAQ,CAChD,IAAI,CAACL,kBAAkB,CACxB;MACD;MACA,IAAI,CAACE,aAAa,GAChB,IAAI,CAACd,WAAW,CAACC,MAAM,CAACiB,IAAI,CAAC9B,SAAS,IAAI,IAAI,CAACA,SAAS;MAE1D;MACA;MACA,IAAI,CAAC4B,aAAa,IAAI,IAAI,CAACX,IAAI,KAAK,MAAM,EAAE;QAC1C,IAAI,CAACvB,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,IAAI,CAACU,SAAS,CAAC;QACjD,IAAI,CAACrB,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;;MAGzD;MACA,IAAI,IAAI,CAACsB,eAAe,KAAKG,aAAa,EAAE;QAC1C;;MAGF;MACA,IAAIA,aAAa,EAAE;QACjB;QACA,IAAI,CAACG,oBAAoB,GAAG,IAAI;QAEhC;QACA,IAAI,CAACvC,QAAQ,GAAG,IAAI;QAEpB,IAAI,CAACwC,QAAQ,GAAG,IAAI,CAAC,CAAC;QAEtB;QACA,IAAI,CAACnC,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAACf,QAAQ,CAAC;QAE3C;QACA,IAAI,IAAI,CAACkC,aAAa,EAAE;UACtB;UACA,IAAI,CAAC1B,SAAS,GAAG,IAAI;UAErB,IAAI,CAACgC,QAAQ,GAAG,KAAK,CAAC,CAAC;UACvB;UACA,IAAI,CAAC9C,kBAAkB,CAAC+C,YAAY,EAAE;;QAGxC;QACA,IAAI,IAAI,CAAChB,IAAI,KAAK,MAAM,EAAE;UACxB,IAAI,CAACvB,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,IAAI,CAACY,SAAS,CAAC;UAC9C,IAAI,CAACrB,WAAW,CAACQ,SAAS,CAACG,MAAM,CAC/B,uBAAuB,EACvB,WAAW,CACZ;;QAGH;QACA,IAAI,CAAC6B,YAAY,EAAE;;MAErB;MAAA,KACK;QACH;QACA,IAAI,CAACH,oBAAoB,GAAG,KAAK;QAEjC;QACA,IAAI,CAACzB,MAAM,EAAE;QAEb;QACA,IAAI,CAACd,QAAQ,GAAG,KAAK;QAErB;QACA,IAAI,CAACK,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAACf,QAAQ,CAAC;QAE3C;QACA,IAAI,CAACE,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,IAAI,CAACU,SAAS,CAAC;QACjD,IAAI,CAACrB,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;QAEvD;QACA,IAAI,CAACgC,YAAY,EAAE;;MAGrB;MACA,IAAI,CAACV,eAAe,GAAGG,aAAa;IACtC,CAAC,CAAC;EACN;EAEA;;;;;EAKQT,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC,IAAI,CAACnB,SAAS,EAAE;MACnB;;IAGF;IACA,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAClB;;IAGF;IACA,IAAI,CAACY,QAAQ,EAAE;EACjB;EAEA;;;;;EAKQgC,YAAYA,CAAA;IAClB;IACA,IAAI,CAAC7C,QAAQ,GAAG,IAAI,CAACR,SAAS,CAACsD,aAAa,CAAC,KAAK,CAAC;IAEnD;IACA,IAAI,CAAC9C,QAAQ,CAACW,SAAS,CAACC,GAAG,CAAC,IAAI,CAACmC,YAAY,CAAC;IAC9C,IAAI,CAAC/C,QAAQ,CAACW,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAEnC;IACA,IAAI,CAAC,IAAI,CAACV,iBAAiB,EAAE;MAC3B,IAAI,CAACF,QAAQ,CAACW,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;;IAG/C;IACA,IAAI,CAACpB,SAAS,CAACwD,WAAW,CACxB,IAAI,CAACvD,WAAW,CAACwD,aAAa,CAACC,aAAa,EAC5C,IAAI,CAAClD,QAAQ,CACd;IAED;IACA,IAAI,CAACmD,gBAAgB,GAAG,IAAI,CAACrD,iBAAiB,CAC3CsD,KAAK,CAAC,CAAC5E,OAAO,CAAC,YAAY,EAAEC,KAAK,CAAC;MAAE4E,OAAO,EAAE;IAAC,CAAE,CAAC,CAAC,CAAC,CAAC,CACrDC,MAAM,CAAC,IAAI,CAACtD,QAAQ,CAAC;IAExB;IACA,IAAI,CAACmD,gBAAgB,CAACI,IAAI,EAAE;IAE5B;IACA,IAAI,CAACvD,QAAQ,CAACwD,gBAAgB,CAAC,OAAO,EAAE,MAAK;MAC3C,IAAI,CAACnE,KAAK,EAAE;MACZ;IACF,CAAC,CAAC;IACF;IACA,IAAI,CAACM,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;;;EAKQC,YAAYA,CAAA;IAClB;IACA,IAAI,CAAC,IAAI,CAAC3C,QAAQ,EAAE;MAClB;;IAGF;IACA,IAAI,CAACmD,gBAAgB,GAAG,IAAI,CAACrD,iBAAiB,CAC3CsD,KAAK,CAAC,CAAC5E,OAAO,CAAC,YAAY,EAAEC,KAAK,CAAC;MAAE4E,OAAO,EAAE;IAAC,CAAE,CAAC,CAAC,CAAC,CAAC,CACrDC,MAAM,CAAC,IAAI,CAACtD,QAAQ,CAAC;IAExB;IACA,IAAI,CAACmD,gBAAgB,CAACI,IAAI,EAAE;IAE5B;IACA,IAAI,CAACJ,gBAAgB,CAACM,MAAM,CAAC,MAAK;MAChC;MACA,IAAI,IAAI,CAACzD,QAAQ,EAAE;QACjB;QACA,IAAI,CAACA,QAAQ,CAAC0D,UAAU,CAACC,WAAW,CAAC,IAAI,CAAC3D,QAAQ,CAAC;QACnD,IAAI,CAACA,QAAQ,GAAG,IAAI;;IAExB,CAAC,CAAC;IACF;IACA,IAAI,CAACL,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;;;EAKQkB,YAAYA,CAAA;IAClB;IACA,IAAI,IAAI,CAAClC,IAAI,IAAI,MAAM,EAAE;MACvB;MACA,IAAI,CAACvB,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;MAC3C,IAAI,CAACT,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAEhD;IAAA,KACK;MACH,IAAI,CAACtB,SAAS,CAACqE,QAAQ,CAAC,IAAI,CAACpE,WAAW,CAACwD,aAAa,EAAE,MAAM,CAAC;MAC/D;MACA,IAAI,IAAI,CAACF,YAAY,KAAK,gBAAgB,EAAE;QAC1C,IAAI,CAAC5C,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;;;IAIhD;IACA,IAAI,CAACjB,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;;;EAKQE,YAAYA,CAAA;IAClB;IACA,IAAI,IAAI,CAAClB,IAAI,IAAI,MAAM,EAAE;MACvB;MACA,IAAI,CAACvB,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;MAC9C,IAAI,CAACX,WAAW,CAACQ,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAE7C;IAAA,KACK;MACH,IAAI,CAACpB,SAAS,CAACsE,WAAW,CAAC,IAAI,CAACrE,WAAW,CAACwD,aAAa,EAAE,MAAM,CAAC;MAElE;MACA,IAAI,IAAI,CAACF,YAAY,KAAK,gBAAgB,EAAE;QAC1C,IAAI,CAAC5C,WAAW,CAACQ,SAAS,CAACG,MAAM,CAAC,YAAY,CAAC;;;IAInD;IACA,IAAI,CAACnB,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;EACA;EAEA;EAEA;;;EAGAqB,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC,IAAI,CAACtD,SAAS,EAAE;MACnB;;IAGF;IACA,IAAI,CAACgC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAChC,SAAS,GAAG,IAAI,CAAC,CAAC;IAEvB;IACA,IAAI,CAACd,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;EAGAsB,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC,IAAI,CAACvD,SAAS,EAAE;MACnB;;IAGF;IACA,IAAI,CAACgC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAChC,SAAS,GAAG,IAAI,CAAC,CAAC;IAEvB;IACA,IAAI,CAACd,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;EAIAuB,YAAYA,CAAA;IACV;IACA,IAAI,CAACD,iBAAiB,EAAE;EAC1B;EAEA;;;EAIAE,YAAYA,CAAA;IACV;IACA,IAAI,CAACH,mBAAmB,EAAE;EAC5B;EAEA;;;EAGAlD,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACJ,SAAS,EAAE;MAClB;;IAGF;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACd,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;EAGA3B,MAAMA,CAAA;IACJ;IACA,IAAI,CAAC,IAAI,CAACN,SAAS,EAAE;MACnB;;IAGF;IACA,IAAI,CAACA,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACd,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;EAGAyB,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC1D,SAAS,EAAE;MAClB,IAAI,CAACM,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACF,QAAQ,EAAE;;EAEnB;EAEA;EAEA;;;EAGAuD,IAAIA,CAAA;IACF;IACA,IAAI,IAAI,CAACnE,QAAQ,IAAI,IAAI,CAACuC,oBAAoB,EAAE;MAC9C;;IAGF;IACA,IAAI,CAACoB,YAAY,EAAE;IAEnB;IACA,IAAI,CAACf,YAAY,EAAE;IAEnB;IACA,IAAI,CAAC5C,QAAQ,GAAG,IAAI;IAEpB;IACA,IAAI,CAACK,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAACf,QAAQ,CAAC;IAC3C,IAAI,IAAI,CAACf,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC8B,IAAI,EAAE;;IAGzB;IACA,IAAI,CAACrB,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;EAGArD,KAAKA,CAAA;IACH,IAAI,IAAI,CAACL,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACgC,IAAI,EAAE;IACzD;IACA,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,IAAI,CAACuC,oBAAoB,EAAE;MAC/C;;IAGF;IACA,IAAI,CAACG,YAAY,EAAE;IAEnB;IACA,IAAI,CAAC1C,QAAQ,GAAG,KAAK;IAErB;IACA,IAAI,CAACK,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAACf,QAAQ,CAAC;IAE3C;IACA,IAAI,CAAC2C,YAAY,EAAE;IAEnB;IACA,IAAI,CAACjD,kBAAkB,CAAC+C,YAAY,EAAE;EACxC;EAEA;;;EAGA2B,UAAUA,CAACD,IAAqB;IAC9B;IACA,IAAIA,IAAI,KAAKE,SAAS,EAAE;MACtB,IAAIF,IAAI,EAAE;QACR,IAAI,CAACA,IAAI,EAAE;OACZ,MAAM;QACL,IAAI,CAAC/E,KAAK,EAAE;;MAEd;KACD,MAAM;MACL,IAAI,IAAI,CAACY,QAAQ,EAAE;QACjB,IAAI,CAACZ,KAAK,EAAE;OACb,MAAM;QACL,IAAI,CAAC+E,IAAI,EAAE;;;EAGjB;EAAC,QAAAG,CAAA;qBA3nBU1F,oBAAoB,EAAA2F,EAAA,CAAAC,iBAAA,CA0FrB/F,QAAQ,GAAA8F,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,SAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,UAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAM,iBAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAQ,EAAA,CAAAC,kBAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAU,EAAA,CAAAC,gBAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UA1FP1G,oBAAoB;IAAA2G,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAApBC,GAAA,CAAA1G,gBAAA,CAAA2G,MAAA,CAAwB;QAAA,UAAAtB,EAAA,CAAAuB,iBAAA,yBAAAC,mDAAA;UAAA,OAAxBH,GAAA,CAAA5B,YAAA,EAAc;QAAA,0BAAAgC,mDAAA;UAAA,OAAdJ,GAAA,CAAA3B,YAAA,EAAc;QAAA;;;;;;;;;;;;;;;;;;;;;;;;QCrC3BM,EAAA,CAAA0B,YAAA,GAAyB", "names": ["EventEmitter", "animate", "style", "DOCUMENT", "Subject", "takeUntil", "CoreSidebarComponent", "setOverlayClickEvent", "event", "overlayClickEvent", "setOnOpenEvent", "onOpenEvent", "onKeydownHandler", "hideOnEsc", "close", "constructor", "document", "_renderer", "_elementRef", "_coreConfigService", "_changeDetectorRef", "_coreMediaService", "_coreSidebarService", "_animationBuilder", "_mediaObserver", "_overlay", "isOpened", "overlayVisibility", "rootElement", "querySelectorAll", "collapsedChangedEvent", "openedChangedEvent", "_collapsed", "_unsubscribeAll", "collapsed", "value", "classList", "add", "collapse", "remove", "expand", "emit", "ngOnInit", "config", "pipe", "subscribe", "_coreConfig", "layout", "type", "menuClass", "setSidebarRegistry", "name", "_setupCollapsibleSidebar", "_defaultCollapsed", "ngOnDestroy", "removeSidebarRegistry", "next", "complete", "collapsibleSidebar", "_wasCollapsible", "_wasCollapsed", "onMediaUpdate", "isCollapsible", "isActive", "menu", "iscollapsibleSidebar", "expanded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_hideOverlay", "_hideSidebar", "_showOverlay", "createElement", "overlayClass", "append<PERSON><PERSON><PERSON>", "nativeElement", "parentElement", "_animationPlayer", "build", "opacity", "create", "play", "addEventListener", "onDone", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_showSidebar", "addClass", "removeClass", "collapseTemporarily", "expandTemporarily", "onMouseEnter", "onMouseLeave", "toggleCollapsible", "open", "toggle<PERSON><PERSON>", "undefined", "_", "i0", "ɵɵdirectiveInject", "Renderer2", "ElementRef", "i1", "CoreConfigService", "ChangeDetectorRef", "i2", "CoreMediaService", "i3", "CoreSidebarService", "i4", "AnimationBuilder", "i5", "MediaObserver", "_2", "selectors", "hostVars", "hostBindings", "CoreSidebarComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveDocument", "CoreSidebarComponent_mouseenter_HostBindingHandler", "CoreSidebarComponent_mouseleave_HostBindingHandler", "ɵɵprojection"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-sidebar\\core-sidebar.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-sidebar\\core-sidebar.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Inject,\r\n  ChangeDetectorRef,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostBinding,\r\n  HostListener,\r\n  Input,\r\n  OnDestroy,\r\n  OnInit,\r\n  Output,\r\n  Renderer2,\r\n  ViewEncapsulation,\r\n} from \"@angular/core\";\r\nimport {\r\n  animate,\r\n  AnimationBuilder,\r\n  AnimationPlayer,\r\n  style,\r\n} from \"@angular/animations\";\r\nimport { DOCUMENT } from \"@angular/common\";\r\nimport { MediaObserver } from \"@angular/flex-layout\";\r\n\r\nimport { Subject } from \"rxjs\";\r\nimport { takeUntil } from \"rxjs/operators\";\r\n\r\nimport { CoreMediaService } from \"@core/services/media.service\";\r\nimport { CoreConfigService } from \"@core/services/config.service\";\r\n\r\nimport { CoreSidebarService } from \"@core/components/core-sidebar/core-sidebar.service\";\r\n\r\n@Component({\r\n  selector: \"core-sidebar\",\r\n  templateUrl: \"./core-sidebar.component.html\",\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class CoreSidebarComponent implements OnInit, OnDestroy {\r\n  // Sidebar name (Component input)\r\n  @Input()\r\n  name: string;\r\n\r\n  // Class name for the overlay (Component input)\r\n  @Input()\r\n  overlayClass: string;\r\n\r\n  // Sidebar Opened\r\n  isOpened: boolean;\r\n\r\n  // Collapsible sidebar (Component input)\r\n  @Input()\r\n  collapsibleSidebar: string;\r\n\r\n  // iscollapsibleSidebar\r\n  iscollapsibleSidebar: boolean;\r\n\r\n  // Collapsible Sidebar expanded\r\n  @HostBinding(\"class.expanded\")\r\n  expanded: boolean;\r\n\r\n  // Collapsed changed event\r\n  @Output()\r\n  collapsedChangedEvent: EventEmitter<boolean>;\r\n\r\n  // Opened changed event\r\n  @Output()\r\n  openedChangedEvent: EventEmitter<boolean>;\r\n\r\n  // Set overlay visibility\r\n  @Input()\r\n  overlayVisibility: boolean;\r\n\r\n  // Hide sidebar on esc key press\r\n  @Input()\r\n  hideOnEsc: boolean;\r\n\r\n  public overlayClickEvent: EventEmitter<any>;\r\n  public onOpenEvent: EventEmitter<any>;\r\n\r\n  // set overlayClickEvent function\r\n  public setOverlayClickEvent(event: any): void {\r\n    // console.log(\"setOverlayClickEvent\", event);\r\n    this.overlayClickEvent = event;\r\n  }\r\n\r\n  // set onOpenEvent function\r\n  public setOnOpenEvent(event: any): void {\r\n    // console.log(\"setOnOpenEvent\", event);\r\n    this.onOpenEvent = event;\r\n  }\r\n\r\n  @HostListener(\"document:keydown.escape\", [\"$event\"]) onKeydownHandler(\r\n    event: KeyboardEvent\r\n  ) {\r\n    if (this.hideOnEsc) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  // Set menu class for current menu type\r\n  menuClass: string;\r\n\r\n  rootElement: any;\r\n\r\n  // Private\r\n  private _coreConfig: any;\r\n  private _collapsed: boolean;\r\n  private _wasCollapsible: boolean;\r\n  private _wasCollapsed: boolean;\r\n  private _animationPlayer: AnimationPlayer;\r\n  private _overlay: HTMLElement | null = null;\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {DOCUMENT} document\r\n   * @param {Renderer2} _renderer\r\n   * @param {ElementRef} _elementRef\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {ChangeDetectorRef} _changeDetectorRef\r\n   * @param {CoreMediaService} _coreMediaService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {AnimationBuilder} _animationBuilder\r\n   * @param {MediaObserver} _mediaObserver\r\n   */\r\n  constructor(\r\n    @Inject(DOCUMENT) private document: any,\r\n    private _renderer: Renderer2,\r\n    private _elementRef: ElementRef,\r\n    private _coreConfigService: CoreConfigService,\r\n    private _changeDetectorRef: ChangeDetectorRef,\r\n    private _coreMediaService: CoreMediaService,\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _animationBuilder: AnimationBuilder,\r\n    private _mediaObserver: MediaObserver\r\n  ) {\r\n    // Set Defaults\r\n    this.isOpened = false;\r\n    this.overlayVisibility = true;\r\n    this.hideOnEsc = false;\r\n\r\n    // Layout root element\r\n    this.rootElement =\r\n      this.document.querySelectorAll(\".vertical-layout\")[0] ||\r\n      this.document.querySelectorAll(\".horizontal-layout\")[0];\r\n\r\n    this.collapsedChangedEvent = new EventEmitter();\r\n    this.openedChangedEvent = new EventEmitter();\r\n\r\n    // Set Private Defaults\r\n    this._collapsed = false;\r\n    this._unsubscribeAll = new Subject();\r\n  }\r\n\r\n  // Accessors\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Collapsed\r\n   *\r\n   * @param {boolean} value\r\n   */\r\n  @Input()\r\n  set collapsed(value: boolean) {\r\n    // Set the collapsed value\r\n    this._collapsed = value;\r\n\r\n    // If the sidebar is closed, return\r\n    if (!this.isOpened) {\r\n      this.rootElement.classList.add(\"menu-expanded\"); // Add menu expanded class default\r\n      return;\r\n    }\r\n\r\n    // If Collapsed\r\n    if (value) {\r\n      // Collapse the sidebar\r\n      this.collapse();\r\n\r\n      // Add menu-collapsed in body and remove menu-expanded\r\n      this.rootElement.classList.add(\"menu-collapsed\");\r\n      this.rootElement.classList.remove(\"menu-expanded\");\r\n    }\r\n    // If Expanded\r\n    else {\r\n      // Expanded the sidebar\r\n      this.expand();\r\n\r\n      // Add menu-expanded in body and remove menu-collapsed\r\n      this.rootElement.classList.add(\"menu-expanded\");\r\n      this.rootElement.classList.remove(\"menu-collapsed\");\r\n    }\r\n\r\n    // Emit the 'collapsedChangedEvent' event\r\n    this.collapsedChangedEvent.emit(this.collapsed);\r\n  }\r\n\r\n  get collapsed(): boolean {\r\n    return this._collapsed;\r\n  }\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit(): void {\r\n    // Subscribe to app-config changes\r\n    this._coreConfigService.config\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe((config) => {\r\n        this._coreConfig = config;\r\n        if (config.layout.type == \"vertical\") {\r\n          this.menuClass = \"vertical-menu-modern\";\r\n        } else {\r\n          this.menuClass = \"horizontal-menu\";\r\n        }\r\n      });\r\n\r\n    // Register the sidebar\r\n    this._coreSidebarService.setSidebarRegistry(this.name, this);\r\n\r\n    // Setup collapsibleSidebar\r\n    this._setupCollapsibleSidebar();\r\n\r\n    // Default collapsed\r\n    this._defaultCollapsed();\r\n  }\r\n\r\n  /**\r\n   * On destroy\r\n   */\r\n  ngOnDestroy(): void {\r\n    // If the sidebar is collapsed, expand it to reset changes\r\n    if (this.collapsed) {\r\n      this.expand();\r\n    }\r\n\r\n    // Remove sidebar registry\r\n    this._coreSidebarService.removeSidebarRegistry(this.name);\r\n\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n\r\n  // Private Methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Setup the collapsible sidebar handler\r\n   *\r\n   * @private\r\n   */\r\n  private _setupCollapsibleSidebar(): void {\r\n    // Return if the collapsible sidebar breakpoint was not set from the layout\r\n    if (!this.collapsibleSidebar) {\r\n      return;\r\n    }\r\n\r\n    // Set the _wasCollapsible false for the first time\r\n    this._wasCollapsible = false;\r\n\r\n    // Set the wasCollapsed from the layout\r\n    this._wasCollapsed = this.collapsed;\r\n\r\n    // On every media(screen) change\r\n    this._coreMediaService.onMediaUpdate\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe(() => {\r\n        // Get the collapsible status\r\n        const isCollapsible = this._mediaObserver.isActive(\r\n          this.collapsibleSidebar\r\n        );\r\n        //! On screen resize set the config collapsed state if we have else this.collapsed\r\n        this._wasCollapsed =\r\n          this._coreConfig.layout.menu.collapsed || this.collapsed;\r\n\r\n        // If sidebar is not collapsible, switch to overlay menu (On page load without resize the window)\r\n        // ? Improve this menu condition\r\n        if (!isCollapsible && this.name === \"menu\") {\r\n          this.rootElement.classList.remove(this.menuClass);\r\n          this.rootElement.classList.add(\"vertical-overlay-menu\");\r\n        }\r\n\r\n        // If the both status are the same, then return\r\n        if (this._wasCollapsible === isCollapsible) {\r\n          return;\r\n        }\r\n\r\n        // If isCollapsible is true, use collapsible sidebar\r\n        if (isCollapsible) {\r\n          // Set the collapsibleSidebar status\r\n          this.iscollapsibleSidebar = true;\r\n\r\n          // Set the the opened status to true\r\n          this.isOpened = true;\r\n\r\n          this.expanded = true; // Adde expanded class init\r\n\r\n          // Emit the 'openedChangedEvent' event\r\n          this.openedChangedEvent.emit(this.isOpened);\r\n\r\n          // If the sidebar was collapsed, forcefully collapse it again\r\n          if (this._wasCollapsed) {\r\n            // Collapse\r\n            this.collapsed = true;\r\n\r\n            this.expanded = false; // Remove expanded class\r\n            // Change detector\r\n            this._changeDetectorRef.markForCheck();\r\n          }\r\n\r\n          // If sidebar is collapsible, switch to collapsible menu (modern-menu)\r\n          if (this.name === \"menu\") {\r\n            this.rootElement.classList.add(this.menuClass);\r\n            this.rootElement.classList.remove(\r\n              \"vertical-overlay-menu\",\r\n              \"menu-hide\"\r\n            );\r\n          }\r\n\r\n          // Hide the overlay if any exists\r\n          this._hideOverlay();\r\n        }\r\n        // Else use overlay sidebar\r\n        else {\r\n          // Set the collapsibleSidebar status\r\n          this.iscollapsibleSidebar = false;\r\n\r\n          // Expanded the sidebar in case if it was collapsed\r\n          this.expand();\r\n\r\n          // Force the the opened status to close\r\n          this.isOpened = false;\r\n\r\n          // Emit the 'openedChangedEvent' event\r\n          this.openedChangedEvent.emit(this.isOpened);\r\n\r\n          // If sidebar is not collapsible, switch to overlay menu (On window resize)\r\n          this.rootElement.classList.remove(this.menuClass);\r\n          this.rootElement.classList.add(\"vertical-overlay-menu\");\r\n\r\n          // Hide the sidebar\r\n          this._hideSidebar();\r\n        }\r\n\r\n        // Set the new active status\r\n        this._wasCollapsible = isCollapsible;\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Setup the initial collapsed status\r\n   *\r\n   * @private\r\n   */\r\n  private _defaultCollapsed(): void {\r\n    // Return, if sidebar is not collapsed\r\n    if (!this.collapsed) {\r\n      return;\r\n    }\r\n\r\n    // Return if the sidebar is closed\r\n    if (!this.isOpened) {\r\n      return;\r\n    }\r\n\r\n    // Collapse the sidebar\r\n    this.collapse();\r\n  }\r\n\r\n  /**\r\n   * Show the overlay\r\n   *\r\n   * @private\r\n   */\r\n  private _showOverlay(): void {\r\n    // Create the overlay element\r\n    this._overlay = this._renderer.createElement(\"div\");\r\n\r\n    // Add a class to the overlay element and make it visible\r\n    this._overlay.classList.add(this.overlayClass);\r\n    this._overlay.classList.add(\"show\");\r\n\r\n    // If overlayVisibility is false, set the bg transparent\r\n    if (!this.overlayVisibility) {\r\n      this._overlay.classList.add(\"bg-transparent\");\r\n    }\r\n\r\n    // Append the overlay element to the parent element of the sidebar\r\n    this._renderer.appendChild(\r\n      this._elementRef.nativeElement.parentElement,\r\n      this._overlay\r\n    );\r\n\r\n    // Overlay enter animation and attach it to the animationPlayer\r\n    this._animationPlayer = this._animationBuilder\r\n      .build([animate(\"300ms ease\", style({ opacity: 1 }))])\r\n      .create(this._overlay);\r\n\r\n    // Play the overlay animation\r\n    this._animationPlayer.play();\r\n\r\n    // Add an event listener to the overlay, on click of it close the sidebar\r\n    this._overlay.addEventListener(\"click\", () => {\r\n      this.close();\r\n      // run passed function if any exists on click\r\n    });\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Hide the overlay\r\n   *\r\n   * @private\r\n   */\r\n  private _hideOverlay(): void {\r\n    // If overlay is already hidden, return\r\n    if (!this._overlay) {\r\n      return;\r\n    }\r\n\r\n    // Overlay leave animation and attach it to the animationPlayer\r\n    this._animationPlayer = this._animationBuilder\r\n      .build([animate(\"300ms ease\", style({ opacity: 0 }))])\r\n      .create(this._overlay);\r\n\r\n    // Play the overlay leave animation\r\n    this._animationPlayer.play();\r\n\r\n    // Once the animation is done...\r\n    this._animationPlayer.onDone(() => {\r\n      // If the overlay still exists...\r\n      if (this._overlay) {\r\n        // Remove the overlay\r\n        this._overlay.parentNode.removeChild(this._overlay);\r\n        this._overlay = null;\r\n      }\r\n    });\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Change sidebar properties to make it visible\r\n   *\r\n   * @private\r\n   */\r\n  private _showSidebar(): void {\r\n    // If menu as sidebar, add relevant classes to body to show menu\r\n    if (this.name == \"menu\") {\r\n      // Open overlay menu\r\n      this.rootElement.classList.add(\"menu-open\");\r\n      this.rootElement.classList.remove(\"menu-hide\");\r\n    }\r\n    // For default sidebar add show class to make it visible\r\n    else {\r\n      this._renderer.addClass(this._elementRef.nativeElement, \"show\");\r\n      // Add .modal-open from body to remove browser scroll\r\n      if (this.overlayClass === \"modal-backdrop\") {\r\n        this.rootElement.classList.add(\"modal-open\");\r\n      }\r\n    }\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Change sidebar properties to make it invisible\r\n   *\r\n   * @private\r\n   */\r\n  private _hideSidebar(): void {\r\n    // If menu as sidebar, add relevant classes to body to show menu\r\n    if (this.name == \"menu\") {\r\n      // Hide overlay menu\r\n      this.rootElement.classList.remove(\"menu-open\");\r\n      this.rootElement.classList.add(\"menu-hide\");\r\n    }\r\n    // For default sidebar remove show class to make it visible\r\n    else {\r\n      this._renderer.removeClass(this._elementRef.nativeElement, \"show\");\r\n\r\n      // Remove .modal-open from body\r\n      if (this.overlayClass === \"modal-backdrop\") {\r\n        this.rootElement.classList.remove(\"modal-open\");\r\n      }\r\n    }\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  // Public Methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  // For Collapsible Sidebar\r\n\r\n  /**\r\n   * Collapse the temporarily expanded sidebar\r\n   */\r\n  collapseTemporarily(): void {\r\n    // Only work if the sidebar is collapsed\r\n    if (!this.collapsed) {\r\n      return;\r\n    }\r\n\r\n    // Collapse the sidebar back\r\n    this.expanded = false;\r\n    this.collapsed = true; // Set the menu collapsed while collapsed temp.\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Expanded the sidebar temporarily\r\n   */\r\n  expandTemporarily(): void {\r\n    // Only work if the sidebar is collapsed\r\n    if (!this.collapsed) {\r\n      return;\r\n    }\r\n\r\n    // Expanded the sidebar temporarily\r\n    this.expanded = true;\r\n    this.collapsed = true; // Set the menu collapsed while collapsed temp.\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * On Sidebar's Mouseenter Event\r\n   */\r\n  @HostListener(\"mouseenter\")\r\n  onMouseEnter(): void {\r\n    // Expand the sidebar temporarily\r\n    this.expandTemporarily();\r\n  }\r\n\r\n  /**\r\n   * On Sidebar's Mouseleave Event\r\n   */\r\n  @HostListener(\"mouseleave\")\r\n  onMouseLeave(): void {\r\n    // Collapse the sidebar temporarily\r\n    this.collapseTemporarily();\r\n  }\r\n\r\n  /**\r\n   * Collapse the sidebar permanently\r\n   */\r\n  collapse(): void {\r\n    // If the sidebar is not collapsed\r\n    if (this.collapsed) {\r\n      return;\r\n    }\r\n\r\n    // Set collapse true\r\n    this.collapsed = true;\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Expanded the sidebar permanently\r\n   */\r\n  expand(): void {\r\n    // If the sidebar is collapsed\r\n    if (!this.collapsed) {\r\n      return;\r\n    }\r\n\r\n    // Set collapse false (expanded)\r\n    this.collapsed = false;\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Toggle the sidebar expand/collapse permanently\r\n   */\r\n  toggleCollapsible(): void {\r\n    if (this.collapsed) {\r\n      this.expand();\r\n    } else {\r\n      this.collapse();\r\n    }\r\n  }\r\n\r\n  // For Overlay Sidebar\r\n\r\n  /**\r\n   * Open the sidebar\r\n   */\r\n  open(): void {\r\n    // If sidebar already open or collapsible, then return\r\n    if (this.isOpened || this.iscollapsibleSidebar) {\r\n      return;\r\n    }\r\n\r\n    // Show the sidebar\r\n    this._showSidebar();\r\n\r\n    // Show the overlay\r\n    this._showOverlay();\r\n\r\n    // Set the sidebar opened status\r\n    this.isOpened = true;\r\n\r\n    // Emit the 'openedChangedEvent' event\r\n    this.openedChangedEvent.emit(this.isOpened);\r\n    if (this.onOpenEvent) {\r\n      this.onOpenEvent.emit();\r\n    }\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Close the sidebar\r\n   */\r\n  close(): void {\r\n    if (this.overlayClickEvent) this.overlayClickEvent.emit();\r\n    // If sidebar is not open or collapsible, then return\r\n    if (!this.isOpened || this.iscollapsibleSidebar) {\r\n      return;\r\n    }\r\n\r\n    // Hide the overlay\r\n    this._hideOverlay();\r\n\r\n    // Set the opened status\r\n    this.isOpened = false;\r\n\r\n    // Emit the 'openedChangedEvent' event\r\n    this.openedChangedEvent.emit(this.isOpened);\r\n\r\n    // Hide overlay menu\r\n    this._hideSidebar();\r\n\r\n    // Change detector\r\n    this._changeDetectorRef.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Toggle open/close the sidebar\r\n   */\r\n  toggleOpen(open?: boolean | null): void {\r\n    // console.log(open);\r\n    if (open !== undefined) {\r\n      if (open) {\r\n        this.open();\r\n      } else {\r\n        this.close();\r\n      }\r\n      return;\r\n    } else {\r\n      if (this.isOpened) {\r\n        this.close();\r\n      } else {\r\n        this.open();\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<ng-content></ng-content>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
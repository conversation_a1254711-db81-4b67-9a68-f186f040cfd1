{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { map } from '../operators/map';\nimport { isObject } from '../util/isObject';\nimport { from } from './from';\nexport function forkJoin(...sources) {\n  if (sources.length === 1) {\n    const first = sources[0];\n    if (isArray(first)) {\n      return forkJoinInternal(first, null);\n    }\n    if (isObject(first) && Object.getPrototypeOf(first) === Object.prototype) {\n      const keys = Object.keys(first);\n      return forkJoinInternal(keys.map(key => first[key]), keys);\n    }\n  }\n  if (typeof sources[sources.length - 1] === 'function') {\n    const resultSelector = sources.pop();\n    sources = sources.length === 1 && isArray(sources[0]) ? sources[0] : sources;\n    return forkJoinInternal(sources, null).pipe(map(args => resultSelector(...args)));\n  }\n  return forkJoinInternal(sources, null);\n}\nfunction forkJoinInternal(sources, keys) {\n  return new Observable(subscriber => {\n    const len = sources.length;\n    if (len === 0) {\n      subscriber.complete();\n      return;\n    }\n    const values = new Array(len);\n    let completed = 0;\n    let emitted = 0;\n    for (let i = 0; i < len; i++) {\n      const source = from(sources[i]);\n      let hasValue = false;\n      subscriber.add(source.subscribe({\n        next: value => {\n          if (!hasValue) {\n            hasValue = true;\n            emitted++;\n          }\n          values[i] = value;\n        },\n        error: err => subscriber.error(err),\n        complete: () => {\n          completed++;\n          if (completed === len || !hasValue) {\n            if (emitted === len) {\n              subscriber.next(keys ? keys.reduce((result, key, i) => (result[key] = values[i], result), {}) : values);\n            }\n            subscriber.complete();\n          }\n        }\n      }));\n    }\n  });\n}", "map": {"version": 3, "names": ["Observable", "isArray", "map", "isObject", "from", "fork<PERSON><PERSON>n", "sources", "length", "first", "forkJoinInternal", "Object", "getPrototypeOf", "prototype", "keys", "key", "resultSelector", "pop", "pipe", "args", "subscriber", "len", "complete", "values", "Array", "completed", "emitted", "i", "source", "hasValue", "add", "subscribe", "next", "value", "error", "err", "reduce", "result"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/forkJoin.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { map } from '../operators/map';\nimport { isObject } from '../util/isObject';\nimport { from } from './from';\nexport function forkJoin(...sources) {\n    if (sources.length === 1) {\n        const first = sources[0];\n        if (isArray(first)) {\n            return forkJoinInternal(first, null);\n        }\n        if (isObject(first) && Object.getPrototypeOf(first) === Object.prototype) {\n            const keys = Object.keys(first);\n            return forkJoinInternal(keys.map(key => first[key]), keys);\n        }\n    }\n    if (typeof sources[sources.length - 1] === 'function') {\n        const resultSelector = sources.pop();\n        sources = (sources.length === 1 && isArray(sources[0])) ? sources[0] : sources;\n        return forkJoinInternal(sources, null).pipe(map((args) => resultSelector(...args)));\n    }\n    return forkJoinInternal(sources, null);\n}\nfunction forkJoinInternal(sources, keys) {\n    return new Observable(subscriber => {\n        const len = sources.length;\n        if (len === 0) {\n            subscriber.complete();\n            return;\n        }\n        const values = new Array(len);\n        let completed = 0;\n        let emitted = 0;\n        for (let i = 0; i < len; i++) {\n            const source = from(sources[i]);\n            let hasValue = false;\n            subscriber.add(source.subscribe({\n                next: value => {\n                    if (!hasValue) {\n                        hasValue = true;\n                        emitted++;\n                    }\n                    values[i] = value;\n                },\n                error: err => subscriber.error(err),\n                complete: () => {\n                    completed++;\n                    if (completed === len || !hasValue) {\n                        if (emitted === len) {\n                            subscriber.next(keys ?\n                                keys.reduce((result, key, i) => (result[key] = values[i], result), {}) :\n                                values);\n                        }\n                        subscriber.complete();\n                    }\n                }\n            }));\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,QAAQA,CAAC,GAAGC,OAAO,EAAE;EACjC,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACtB,MAAMC,KAAK,GAAGF,OAAO,CAAC,CAAC,CAAC;IACxB,IAAIL,OAAO,CAACO,KAAK,CAAC,EAAE;MAChB,OAAOC,gBAAgB,CAACD,KAAK,EAAE,IAAI,CAAC;IACxC;IACA,IAAIL,QAAQ,CAACK,KAAK,CAAC,IAAIE,MAAM,CAACC,cAAc,CAACH,KAAK,CAAC,KAAKE,MAAM,CAACE,SAAS,EAAE;MACtE,MAAMC,IAAI,GAAGH,MAAM,CAACG,IAAI,CAACL,KAAK,CAAC;MAC/B,OAAOC,gBAAgB,CAACI,IAAI,CAACX,GAAG,CAACY,GAAG,IAAIN,KAAK,CAACM,GAAG,CAAC,CAAC,EAAED,IAAI,CAAC;IAC9D;EACJ;EACA,IAAI,OAAOP,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;IACnD,MAAMQ,cAAc,GAAGT,OAAO,CAACU,GAAG,CAAC,CAAC;IACpCV,OAAO,GAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,IAAIN,OAAO,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAIA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO;IAC9E,OAAOG,gBAAgB,CAACH,OAAO,EAAE,IAAI,CAAC,CAACW,IAAI,CAACf,GAAG,CAAEgB,IAAI,IAAKH,cAAc,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC;EACvF;EACA,OAAOT,gBAAgB,CAACH,OAAO,EAAE,IAAI,CAAC;AAC1C;AACA,SAASG,gBAAgBA,CAACH,OAAO,EAAEO,IAAI,EAAE;EACrC,OAAO,IAAIb,UAAU,CAACmB,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAGd,OAAO,CAACC,MAAM;IAC1B,IAAIa,GAAG,KAAK,CAAC,EAAE;MACXD,UAAU,CAACE,QAAQ,CAAC,CAAC;MACrB;IACJ;IACA,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACH,GAAG,CAAC;IAC7B,IAAII,SAAS,GAAG,CAAC;IACjB,IAAIC,OAAO,GAAG,CAAC;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,GAAG,EAAEM,CAAC,EAAE,EAAE;MAC1B,MAAMC,MAAM,GAAGvB,IAAI,CAACE,OAAO,CAACoB,CAAC,CAAC,CAAC;MAC/B,IAAIE,QAAQ,GAAG,KAAK;MACpBT,UAAU,CAACU,GAAG,CAACF,MAAM,CAACG,SAAS,CAAC;QAC5BC,IAAI,EAAEC,KAAK,IAAI;UACX,IAAI,CAACJ,QAAQ,EAAE;YACXA,QAAQ,GAAG,IAAI;YACfH,OAAO,EAAE;UACb;UACAH,MAAM,CAACI,CAAC,CAAC,GAAGM,KAAK;QACrB,CAAC;QACDC,KAAK,EAAEC,GAAG,IAAIf,UAAU,CAACc,KAAK,CAACC,GAAG,CAAC;QACnCb,QAAQ,EAAEA,CAAA,KAAM;UACZG,SAAS,EAAE;UACX,IAAIA,SAAS,KAAKJ,GAAG,IAAI,CAACQ,QAAQ,EAAE;YAChC,IAAIH,OAAO,KAAKL,GAAG,EAAE;cACjBD,UAAU,CAACY,IAAI,CAAClB,IAAI,GAChBA,IAAI,CAACsB,MAAM,CAAC,CAACC,MAAM,EAAEtB,GAAG,EAAEY,CAAC,MAAMU,MAAM,CAACtB,GAAG,CAAC,GAAGQ,MAAM,CAACI,CAAC,CAAC,EAAEU,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GACtEd,MAAM,CAAC;YACf;YACAH,UAAU,CAACE,QAAQ,CAAC,CAAC;UACzB;QACJ;MACJ,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,CAAC;AACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
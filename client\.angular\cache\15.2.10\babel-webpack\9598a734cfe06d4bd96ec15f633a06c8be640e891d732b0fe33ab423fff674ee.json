{"ast": null, "code": "import Swal from 'sweetalert2';\nimport { FormGroup } from '@angular/forms';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"app/services/registration.service\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"app/services/settings.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@ngx-formly/core\";\nexport class ValidatorComponent {\n  constructor(modalService, _registrationService, _loadingService, _commonsService, _translateService, _settingsService) {\n    this.modalService = modalService;\n    this._registrationService = _registrationService;\n    this._loadingService = _loadingService;\n    this._commonsService = _commonsService;\n    this._translateService = _translateService;\n    this._settingsService = _settingsService;\n    this.form = new FormGroup({});\n    this.model = {};\n    this.fields = [{\n      key: 'registration_id',\n      type: 'input',\n      props: {\n        type: 'hidden',\n        accepted: true\n      }\n    }];\n    this.keepOrder = (a, b) => {\n      return a;\n    };\n    this.customField = [];\n    this.customField = _settingsService.customFieldsValue;\n    console.log('cf', this.customField);\n    this.customField.forEach(field => {\n      let validateField = this.conver2ValidateField(field);\n      if (field.key != 'club_id') {\n        this.fields.push(validateField);\n      }\n    });\n  }\n  conver2ValidateField(field) {\n    switch (field.type) {\n      case 'image-cropper':\n        field.type = 'file';\n        let props = {\n          uploaded: true,\n          hidden_input: true,\n          upload_url: `${environment.apiUrl}/s3`,\n          label: field.props.label,\n          accept: 'image/*',\n          accepted: false\n        };\n        // merge field.props to props\n        field.props = {\n          ...field.props,\n          ...props\n        };\n        break;\n    }\n    field.props = {\n      ...field.props,\n      ...{\n        translate: true,\n        accepted: false\n      }\n    };\n    let validateField = {\n      key: field.key,\n      type: field.type,\n      wrappers: ['validate'],\n      props: field.props,\n      expressions: field.expressions ? field.expressions : {},\n      defaultValue: field.defaultValue ? field.defaultValue : ''\n    };\n    return validateField;\n  }\n  ngOnInit() {\n    let player = this.registrations.player;\n    // split validated fields to array\n    let validated_fields = player.validated_fields.split('|');\n    // console.log('validated_fields', validated_fields);\n    // if key has in validated_fields then set accepted = true\n    this.fields.forEach(field => {\n      if (validated_fields.includes(field.key)) {\n        field.props.accepted = true;\n        // disable field\n        if (field.type != 'file') field.props.disabled = true;\n      }\n    });\n    // set model\n    this.model['registration_id'] = this.registrations.id;\n    for (const key in player) {\n      let value = player[key];\n      if (key == 'user') {\n        for (const user_key in value) {\n          this.model[user_key] = value[user_key];\n        }\n      } else if (key == 'custom_fields') {\n        // Decode custom field values\n        Object.entries(value).forEach(([customKey, customValue]) => {\n          // custome fields are not validate\n          this.model[customKey] = this.decodeHtml(customValue);\n        });\n      } else {\n        this.model[key] = value;\n      }\n    }\n  }\n  decodeHtml(html) {\n    var txt = document.createElement('textarea');\n    txt.innerHTML = html;\n    return txt.value;\n  }\n  acceptAll() {\n    this.fields.forEach(field => {\n      if (field.props.hasOwnProperty('accepted')) {\n        field.props.accepted = true;\n      }\n    });\n  }\n  onSubmit() {\n    let data_submit = {};\n    console.log('fields', this.fields);\n    this.fields.forEach(field => {\n      if (field.key != 'registration_id' && !field.props.hidden) {\n        if (field.props.hasOwnProperty('accepted')) {\n          data_submit[field.key] = {\n            accepted: field.props.accepted,\n            message: field.props.hasOwnProperty('message') ? field.props.message : ''\n          };\n        } else {\n          data_submit[field.key] = {\n            accepted: false,\n            message: field.props.hasOwnProperty('message') ? field.props.message : ''\n          };\n        }\n      }\n    });\n    console.log('data_submit', data_submit);\n    // send to server\n    this._loadingService.show();\n    this._registrationService.validateRegistration(this.registrations.id, data_submit).toPromise().then(data => {\n      console.log('data', data);\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n        this.modalService.dismissAll();\n        Swal.fire({\n          title: this._translateService.instant('Success'),\n          text: this._translateService.instant('Validation and send email success'),\n          icon: 'success',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n      });\n    }).catch(error => {\n      console.log('error', error);\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onUpdatePlayer() {\n    // console.log('form', this.form);\n    // console.log('fields', this.fields);\n    // console.log('status', this.form.status);\n    // if form is valid\n    if (this.form.valid) {\n      this._loadingService.show();\n      this._registrationService.updatePlayerValidation(this.form.value).toPromise().then(data => {\n        console.log('data', data);\n        Swal.fire({\n          title: this._translateService.instant('Success'),\n          text: data.message,\n          icon: 'success',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }).catch(error => {\n        console.log('error', error);\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    } else {\n      // show error message\n      return;\n    }\n  }\n  static #_ = this.ɵfac = function ValidatorComponent_Factory(t) {\n    return new (t || ValidatorComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.RegistrationService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.SettingsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ValidatorComponent,\n    selectors: [[\"app-validator\"]],\n    inputs: {\n      registrations: \"registrations\",\n      status: \"status\",\n      dtElement: \"dtElement\"\n    },\n    decls: 25,\n    vars: 16,\n    consts: [[3, \"formGroup\", \"ngSubmit\"], [\"formDirective\", \"ngForm\"], [1, \"modal-header\"], [\"id\", \"myModalLabel160\", 1, \"modal-title\", \"text-capitalize\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\"], [1, \"modal-footer\", \"d-block\", \"p-0\"], [1, \"row\", \"mb-1\"], [1, \"col-12\", \"col-sm-4\", \"mt-1\"], [\"type\", \"submit\", \"id\", \"btnUpdate\", 1, \"btn\", \"btn-block\", \"btn-outline-warning\"], [\"type\", \"button\", \"id\", \"btnAccept\", 1, \"btn\", \"btn-block\", \"btn-outline-success\", \"text-capitalize\", 3, \"click\"], [\"type\", \"button\", \"id\", \"btnSubmit\", 1, \"btn\", \"btn-block\", \"btn-primary\", 3, \"click\"]],\n    template: function ValidatorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0, 1);\n        i0.ɵɵlistener(\"ngSubmit\", function ValidatorComponent_Template_form_ngSubmit_0_listener() {\n          return ctx.onUpdatePlayer();\n        });\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"h5\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵpipe(5, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ValidatorComponent_Template_button_click_6_listener() {\n          return ctx.modalService.dismissAll();\n        });\n        i0.ɵɵelementStart(7, \"span\", 5);\n        i0.ɵɵtext(8, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelement(10, \"formly-form\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"button\", 11);\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 10)(18, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function ValidatorComponent_Template_button_click_18_listener() {\n          return ctx.acceptAll();\n        });\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 10)(22, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function ValidatorComponent_Template_button_click_22_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtext(23);\n        i0.ɵɵpipe(24, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 8, \"Validate player\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 10, \"Update\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 12, \"Accept all\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(24, 14, \"Submit\"), \" \");\n      }\n    },\n    dependencies: [i7.ɵNgNoValidate, i7.NgControlStatusGroup, i7.FormGroupDirective, i8.FormlyForm, i5.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAIA,OAAOA,IAAI,MAAM,aAAa;AAC9B,SAAsBC,SAAS,QAAoB,gBAAgB;AAGnE,SAASC,WAAW,QAAQ,0BAA0B;;;;;;;;;;AAUtD,OAAM,MAAOC,kBAAkB;EAqB7BC,YACSC,YAAsB,EACtBC,oBAAyC,EACzCC,eAA+B,EAC/BC,eAA+B,EAC/BC,iBAAmC,EACnCC,gBAAiC;IALjC,KAAAL,YAAY,GAAZA,YAAY;IACZ,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAvBzB,KAAAC,IAAI,GAAG,IAAIV,SAAS,CAAC,EAAE,CAAC;IACjB,KAAAW,KAAK,GAAG,EAAE;IACjB,KAAAC,MAAM,GAAU,CACd;MACEC,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,QAAQ;QACdE,QAAQ,EAAE;;KAEb,CACF;IACD,KAAAC,SAAS,GAAG,CAACC,CAAC,EAAEC,CAAC,KAAI;MACnB,OAAOD,CAAC;IACV,CAAC;IAED,KAAAE,WAAW,GAAU,EAAE;IASrB,IAAI,CAACA,WAAW,GAAGX,gBAAgB,CAACY,iBAAiB;IACrDC,OAAO,CAACC,GAAG,CAAC,IAAI,EAAE,IAAI,CAACH,WAAW,CAAC;IAEnC,IAAI,CAACA,WAAW,CAACI,OAAO,CAAEC,KAAK,IAAI;MACjC,IAAIC,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAACF,KAAK,CAAC;MACpD,IAAIA,KAAK,CAACZ,GAAG,IAAI,SAAS,EAAE;QAC1B,IAAI,CAACD,MAAM,CAACgB,IAAI,CAACF,aAAa,CAAC;;IAEnC,CAAC,CAAC;EACJ;EAEAC,oBAAoBA,CAACF,KAAK;IACxB,QAAQA,KAAK,CAACX,IAAI;MAChB,KAAK,eAAe;QAClBW,KAAK,CAACX,IAAI,GAAG,MAAM;QACnB,IAAIC,KAAK,GAAG;UACVc,QAAQ,EAAE,IAAI;UACdC,YAAY,EAAE,IAAI;UAClBC,UAAU,EAAE,GAAG9B,WAAW,CAAC+B,MAAM,KAAK;UACtCC,KAAK,EAAER,KAAK,CAACV,KAAK,CAACkB,KAAK;UACxBC,MAAM,EAAE,SAAS;UACjBlB,QAAQ,EAAE;SACX;QACD;QACAS,KAAK,CAACV,KAAK,GAAG;UAAE,GAAGU,KAAK,CAACV,KAAK;UAAE,GAAGA;QAAK,CAAE;QAC1C;;IAEJU,KAAK,CAACV,KAAK,GAAG;MAAE,GAAGU,KAAK,CAACV,KAAK;MAAE,GAAG;QAAEoB,SAAS,EAAE,IAAI;QAAEnB,QAAQ,EAAE;MAAK;IAAE,CAAE;IACzE,IAAIU,aAAa,GAAG;MAClBb,GAAG,EAAEY,KAAK,CAACZ,GAAG;MACdC,IAAI,EAAEW,KAAK,CAACX,IAAI;MAChBsB,QAAQ,EAAE,CAAC,UAAU,CAAC;MACtBrB,KAAK,EAAEU,KAAK,CAACV,KAAK;MAClBsB,WAAW,EAAEZ,KAAK,CAACY,WAAW,GAAGZ,KAAK,CAACY,WAAW,GAAG,EAAE;MACvDC,YAAY,EAAEb,KAAK,CAACa,YAAY,GAAGb,KAAK,CAACa,YAAY,GAAG;KACzD;IACD,OAAOZ,aAAa;EACtB;EAEAa,QAAQA,CAAA;IACN,IAAIC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACD,MAAM;IACtC;IACA,IAAIE,gBAAgB,GAAGF,MAAM,CAACE,gBAAgB,CAACC,KAAK,CAAC,GAAG,CAAC;IACzD;IACA;IACA,IAAI,CAAC/B,MAAM,CAACY,OAAO,CAAEC,KAAK,IAAI;MAC5B,IAAIiB,gBAAgB,CAACE,QAAQ,CAACnB,KAAK,CAACZ,GAAG,CAAC,EAAE;QACxCY,KAAK,CAACV,KAAK,CAACC,QAAQ,GAAG,IAAI;QAC3B;QACA,IAAIS,KAAK,CAACX,IAAI,IAAI,MAAM,EAAEW,KAAK,CAACV,KAAK,CAAC8B,QAAQ,GAAG,IAAI;;IAEzD,CAAC,CAAC;IACF;IACA,IAAI,CAAClC,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC8B,aAAa,CAACK,EAAE;IACrD,KAAK,MAAMjC,GAAG,IAAI2B,MAAM,EAAE;MACxB,IAAIO,KAAK,GAAGP,MAAM,CAAC3B,GAAG,CAAC;MACvB,IAAIA,GAAG,IAAI,MAAM,EAAE;QACjB,KAAK,MAAMmC,QAAQ,IAAID,KAAK,EAAE;UAC5B,IAAI,CAACpC,KAAK,CAACqC,QAAQ,CAAC,GAAGD,KAAK,CAACC,QAAQ,CAAC;;OAEzC,MAAM,IAAInC,GAAG,IAAI,eAAe,EAAE;QACjC;QACAoC,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,CAACvB,OAAO,CAAC,CAAC,CAAC2B,SAAS,EAAEC,WAAW,CAAC,KAAI;UACzD;UACA,IAAI,CAACzC,KAAK,CAACwC,SAAS,CAAC,GAAG,IAAI,CAACE,UAAU,CAACD,WAAW,CAAC;QACtD,CAAC,CAAC;OACH,MAAM;QACL,IAAI,CAACzC,KAAK,CAACE,GAAG,CAAC,GAAGkC,KAAK;;;EAG7B;EAEAM,UAAUA,CAACC,IAAI;IACb,IAAIC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IAC5CF,GAAG,CAACG,SAAS,GAAGJ,IAAI;IACpB,OAAOC,GAAG,CAACR,KAAK;EAClB;EAEAY,SAASA,CAAA;IACP,IAAI,CAAC/C,MAAM,CAACY,OAAO,CAAEC,KAAK,IAAI;MAC5B,IAAIA,KAAK,CAACV,KAAK,CAAC6C,cAAc,CAAC,UAAU,CAAC,EAAE;QAC1CnC,KAAK,CAACV,KAAK,CAACC,QAAQ,GAAG,IAAI;;IAE/B,CAAC,CAAC;EACJ;EAEA6C,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG,EAAE;IACpBxC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACX,MAAM,CAAC;IAClC,IAAI,CAACA,MAAM,CAACY,OAAO,CAAEC,KAAK,IAAI;MAC5B,IAAIA,KAAK,CAACZ,GAAG,IAAI,iBAAiB,IAAI,CAACY,KAAK,CAACV,KAAK,CAACgD,MAAM,EAAE;QACzD,IAAItC,KAAK,CAACV,KAAK,CAAC6C,cAAc,CAAC,UAAU,CAAC,EAAE;UAC1CE,WAAW,CAACrC,KAAK,CAACZ,GAAG,CAAC,GAAG;YACvBG,QAAQ,EAAES,KAAK,CAACV,KAAK,CAACC,QAAQ;YAC9BgD,OAAO,EAAEvC,KAAK,CAACV,KAAK,CAAC6C,cAAc,CAAC,SAAS,CAAC,GAC1CnC,KAAK,CAACV,KAAK,CAACiD,OAAO,GACnB;WACL;SACF,MAAM;UACLF,WAAW,CAACrC,KAAK,CAACZ,GAAG,CAAC,GAAG;YACvBG,QAAQ,EAAE,KAAK;YACfgD,OAAO,EAAEvC,KAAK,CAACV,KAAK,CAAC6C,cAAc,CAAC,SAAS,CAAC,GAC1CnC,KAAK,CAACV,KAAK,CAACiD,OAAO,GACnB;WACL;;;IAGP,CAAC,CAAC;IACF1C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEuC,WAAW,CAAC;IAEvC;IACA,IAAI,CAACxD,eAAe,CAAC2D,IAAI,EAAE;IAC3B,IAAI,CAAC5D,oBAAoB,CACtB6D,oBAAoB,CAAC,IAAI,CAACzB,aAAa,CAACK,EAAE,EAAEgB,WAAkB,CAAC,CAC/DK,SAAS,EAAE,CACXC,IAAI,CAAEC,IAAI,IAAI;MACb/C,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE8C,IAAI,CAAC;MACzB,IAAI,CAACC,SAAS,CAACC,UAAU,CAACH,IAAI,CAAEG,UAA0B,IAAI;QAC5DA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;QACxB,IAAI,CAACrE,YAAY,CAACsE,UAAU,EAAE;QAC9B3E,IAAI,CAAC4E,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACpE,iBAAiB,CAACqE,OAAO,CAAC,SAAS,CAAC;UAChDC,IAAI,EAAE,IAAI,CAACtE,iBAAiB,CAACqE,OAAO,CAClC,mCAAmC,CACpC;UACDE,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACqE,OAAO,CAAC,IAAI,CAAC;UACvDI,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,CACDC,KAAK,CAAEC,KAAK,IAAI;MACf9D,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE6D,KAAK,CAAC;MAC3BrF,IAAI,CAAC4E,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdE,IAAI,EAAEM,KAAK,CAACpB,OAAO;QACnBe,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACqE,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CAAC;EACN;EAEAQ,cAAcA,CAAA;IACZ;IACA;IACA;IAEA;IACA,IAAI,IAAI,CAAC3E,IAAI,CAAC4E,KAAK,EAAE;MACnB,IAAI,CAAChF,eAAe,CAAC2D,IAAI,EAAE;MAC3B,IAAI,CAAC5D,oBAAoB,CACtBkF,sBAAsB,CAAC,IAAI,CAAC7E,IAAI,CAACqC,KAAY,CAAC,CAC9CoB,SAAS,EAAE,CACXC,IAAI,CAAEC,IAAI,IAAI;QACb/C,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE8C,IAAI,CAAC;QACzBtE,IAAI,CAAC4E,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACpE,iBAAiB,CAACqE,OAAO,CAAC,SAAS,CAAC;UAChDC,IAAI,EAAET,IAAI,CAACL,OAAO;UAClBe,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACqE,OAAO,CAAC,IAAI,CAAC;UACvDI,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;QACF,IAAI,CAACZ,SAAS,CAACC,UAAU,CAACH,IAAI,CAAEG,UAA0B,IAAI;UAC5DA,UAAU,CAACC,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;MACJ,CAAC,CAAC,CACDU,KAAK,CAAEC,KAAK,IAAI;QACf9D,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE6D,KAAK,CAAC;QAC3BrF,IAAI,CAAC4E,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdE,IAAI,EAAEM,KAAK,CAACpB,OAAO;UACnBe,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACqE,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CAAC;KACL,MAAM;MACL;MACA;;EAEJ;EAAC,QAAAW,CAAA;qBApNUtF,kBAAkB,EAAAuF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA;UAAlBrG,kBAAkB;IAAAsG,SAAA;IAAAC,MAAA;MAAAhE,aAAA;MAAAiE,MAAA;MAAApC,SAAA;IAAA;IAAAqC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB/BvB,EAAA,CAAAyB,cAAA,iBAA+E;QAAtDzB,EAAA,CAAA0B,UAAA,sBAAAC,qDAAA;UAAA,OAAYH,GAAA,CAAA5B,cAAA,EAAgB;QAAA,EAAC;QAClDI,EAAA,CAAAyB,cAAA,aAA0B;QACuCzB,EAAA,CAAA4B,MAAA,GAAiC;;QAAA5B,EAAA,CAAA6B,YAAA,EAAK;QAEnG7B,EAAA,CAAAyB,cAAA,gBACwC;QAApCzB,EAAA,CAAA0B,UAAA,mBAAAI,oDAAA;UAAA,OAASN,GAAA,CAAA7G,YAAA,CAAAsE,UAAA,EAAyB;QAAA,EAAC;QACnCe,EAAA,CAAAyB,cAAA,cAAyB;QAAAzB,EAAA,CAAA4B,MAAA,aAAO;QAAA5B,EAAA,CAAA6B,YAAA,EAAO;QAG/C7B,EAAA,CAAAyB,cAAA,aAAkD;QAC9CzB,EAAA,CAAA+B,SAAA,sBAA2E;QAC/E/B,EAAA,CAAA6B,YAAA,EAAM;QAEN7B,EAAA,CAAAyB,cAAA,cAAsC;QAItBzB,EAAA,CAAA4B,MAAA,IACJ;;QAAA5B,EAAA,CAAA6B,YAAA,EAAS;QAEb7B,EAAA,CAAAyB,cAAA,eAAkC;QACiEzB,EAAA,CAAA0B,UAAA,mBAAAM,qDAAA;UAAA,OAASR,GAAA,CAAAtD,SAAA,EAAW;QAAA,EAAC;QAChH8B,EAAA,CAAA4B,MAAA,IACJ;;QAAA5B,EAAA,CAAA6B,YAAA,EAAS;QAEb7B,EAAA,CAAAyB,cAAA,eAAkC;QAC0BzB,EAAA,CAAA0B,UAAA,mBAAAO,qDAAA;UAAA,OAAST,GAAA,CAAApD,QAAA,EAAU;QAAA,EAAC;QACxE4B,EAAA,CAAA4B,MAAA,IACJ;;QAAA5B,EAAA,CAAA6B,YAAA,EAAS;;;QA5BnB7B,EAAA,CAAAkC,UAAA,cAAAV,GAAA,CAAAvG,IAAA,CAAkB;QAE6C+E,EAAA,CAAAmC,SAAA,GAAiC;QAAjCnC,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAAqC,WAAA,0BAAiC;QAQjFrC,EAAA,CAAAmC,SAAA,GAAa;QAAbnC,EAAA,CAAAkC,UAAA,SAAAV,GAAA,CAAAvG,IAAA,CAAa,WAAAuG,GAAA,CAAArG,MAAA,WAAAqG,GAAA,CAAAtG,KAAA;QAOd8E,EAAA,CAAAmC,SAAA,GACJ;QADInC,EAAA,CAAAsC,kBAAA,MAAAtC,EAAA,CAAAqC,WAAA,wBACJ;QAIIrC,EAAA,CAAAmC,SAAA,GACJ;QADInC,EAAA,CAAAsC,kBAAA,MAAAtC,EAAA,CAAAqC,WAAA,4BACJ;QAIIrC,EAAA,CAAAmC,SAAA,GACJ;QADInC,EAAA,CAAAsC,kBAAA,MAAAtC,EAAA,CAAAqC,WAAA,wBACJ", "names": ["<PERSON><PERSON>", "FormGroup", "environment", "ValidatorComponent", "constructor", "modalService", "_registrationService", "_loadingService", "_commonsService", "_translateService", "_settingsService", "form", "model", "fields", "key", "type", "props", "accepted", "keepOrder", "a", "b", "customField", "customFieldsValue", "console", "log", "for<PERSON>ach", "field", "validateField", "conver2ValidateField", "push", "uploaded", "hidden_input", "upload_url", "apiUrl", "label", "accept", "translate", "wrappers", "expressions", "defaultValue", "ngOnInit", "player", "registrations", "validated_fields", "split", "includes", "disabled", "id", "value", "user_key", "Object", "entries", "customKey", "customValue", "decodeHtml", "html", "txt", "document", "createElement", "innerHTML", "acceptAll", "hasOwnProperty", "onSubmit", "data_submit", "hidden", "message", "show", "validateRegistration", "to<PERSON>romise", "then", "data", "dtElement", "dtInstance", "ajax", "reload", "dismissAll", "fire", "title", "instant", "text", "icon", "confirmButtonText", "customClass", "confirmButton", "catch", "error", "onUpdatePlayer", "valid", "updatePlayerValidation", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbModal", "i2", "RegistrationService", "i3", "LoadingService", "i4", "CommonsService", "i5", "TranslateService", "i6", "SettingsService", "_2", "selectors", "inputs", "status", "decls", "vars", "consts", "template", "ValidatorComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "ValidatorComponent_Template_form_ngSubmit_0_listener", "ɵɵtext", "ɵɵelementEnd", "ValidatorComponent_Template_button_click_6_listener", "ɵɵelement", "ValidatorComponent_Template_button_click_18_listener", "ValidatorComponent_Template_button_click_22_listener", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\validator\\validator.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\validator\\validator.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport Swal from 'sweetalert2';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { environment } from 'environments/environment';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { _ } from 'core-js';\r\n\r\n@Component({\r\n  selector: 'app-validator',\r\n  templateUrl: './validator.component.html',\r\n  styleUrls: ['./validator.component.scss'],\r\n})\r\nexport class ValidatorComponent implements OnInit {\r\n  @Input() registrations: any;\r\n  @Input() status: any;\r\n  @Input() dtElement: any;\r\n  form = new FormGroup({});\r\n  public model = {};\r\n  fields: any[] = [\r\n    {\r\n      key: 'registration_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden',\r\n        accepted: true,\r\n      },\r\n    },\r\n  ];\r\n  keepOrder = (a, b) => {\r\n    return a;\r\n  };\r\n\r\n  customField: any[] = [];\r\n  constructor(\r\n    public modalService: NgbModal,\r\n    public _registrationService: RegistrationService,\r\n    public _loadingService: LoadingService,\r\n    public _commonsService: CommonsService,\r\n    public _translateService: TranslateService,\r\n    public _settingsService: SettingsService\r\n  ) {\r\n    this.customField = _settingsService.customFieldsValue;\r\n    console.log('cf', this.customField);\r\n\r\n    this.customField.forEach((field) => {\r\n      let validateField = this.conver2ValidateField(field);\r\n      if (field.key != 'club_id') {\r\n        this.fields.push(validateField);\r\n      }\r\n    });\r\n  }\r\n\r\n  conver2ValidateField(field) {\r\n    switch (field.type) {\r\n      case 'image-cropper':\r\n        field.type = 'file';\r\n        let props = {\r\n          uploaded: true,\r\n          hidden_input: true,\r\n          upload_url: `${environment.apiUrl}/s3`,\r\n          label: field.props.label,\r\n          accept: 'image/*',\r\n          accepted: false,\r\n        };\r\n        // merge field.props to props\r\n        field.props = { ...field.props, ...props };\r\n        break;\r\n    }\r\n    field.props = { ...field.props, ...{ translate: true, accepted: false } };\r\n    let validateField = {\r\n      key: field.key,\r\n      type: field.type,\r\n      wrappers: ['validate'],\r\n      props: field.props,\r\n      expressions: field.expressions ? field.expressions : {},\r\n      defaultValue: field.defaultValue ? field.defaultValue : '',\r\n    };\r\n    return validateField;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    let player = this.registrations.player;\r\n    // split validated fields to array\r\n    let validated_fields = player.validated_fields.split('|');\r\n    // console.log('validated_fields', validated_fields);\r\n    // if key has in validated_fields then set accepted = true\r\n    this.fields.forEach((field) => {\r\n      if (validated_fields.includes(field.key)) {\r\n        field.props.accepted = true;\r\n        // disable field\r\n        if (field.type != 'file') field.props.disabled = true;\r\n      }\r\n    });\r\n    // set model\r\n    this.model['registration_id'] = this.registrations.id;\r\n    for (const key in player) {\r\n      let value = player[key];\r\n      if (key == 'user') {\r\n        for (const user_key in value) {\r\n          this.model[user_key] = value[user_key];\r\n        }\r\n      } else if (key == 'custom_fields') {\r\n        // Decode custom field values\r\n        Object.entries(value).forEach(([customKey, customValue]) => {\r\n          // custome fields are not validate\r\n          this.model[customKey] = this.decodeHtml(customValue);\r\n        });\r\n      } else {\r\n        this.model[key] = value;\r\n      }\r\n    }\r\n  }\r\n\r\n  decodeHtml(html) {\r\n    var txt = document.createElement('textarea');\r\n    txt.innerHTML = html;\r\n    return txt.value;\r\n  }\r\n\r\n  acceptAll() {\r\n    this.fields.forEach((field) => {\r\n      if (field.props.hasOwnProperty('accepted')) {\r\n        field.props.accepted = true;\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    let data_submit = {};\r\n    console.log('fields', this.fields);\r\n    this.fields.forEach((field) => {\r\n      if (field.key != 'registration_id' && !field.props.hidden) {\r\n        if (field.props.hasOwnProperty('accepted')) {\r\n          data_submit[field.key] = {\r\n            accepted: field.props.accepted,\r\n            message: field.props.hasOwnProperty('message')\r\n              ? field.props.message\r\n              : '',\r\n          };\r\n        } else {\r\n          data_submit[field.key] = {\r\n            accepted: false,\r\n            message: field.props.hasOwnProperty('message')\r\n              ? field.props.message\r\n              : '',\r\n          };\r\n        }\r\n      }\r\n    });\r\n    console.log('data_submit', data_submit);\r\n\r\n    // send to server\r\n    this._loadingService.show();\r\n    this._registrationService\r\n      .validateRegistration(this.registrations.id, data_submit as any)\r\n      .toPromise()\r\n      .then((data) => {\r\n        console.log('data', data);\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n          this.modalService.dismissAll();\r\n          Swal.fire({\r\n            title: this._translateService.instant('Success'),\r\n            text: this._translateService.instant(\r\n              'Validation and send email success'\r\n            ),\r\n            icon: 'success',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary',\r\n            },\r\n          });\r\n        });\r\n      })\r\n      .catch((error) => {\r\n        console.log('error', error);\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n        });\r\n      });\r\n  }\r\n\r\n  onUpdatePlayer() {\r\n    // console.log('form', this.form);\r\n    // console.log('fields', this.fields);\r\n    // console.log('status', this.form.status);\r\n\r\n    // if form is valid\r\n    if (this.form.valid) {\r\n      this._loadingService.show();\r\n      this._registrationService\r\n        .updatePlayerValidation(this.form.value as any)\r\n        .toPromise()\r\n        .then((data) => {\r\n          console.log('data', data);\r\n          Swal.fire({\r\n            title: this._translateService.instant('Success'),\r\n            text: data.message,\r\n            icon: 'success',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary',\r\n            },\r\n          });\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        })\r\n        .catch((error) => {\r\n          console.log('error', error);\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: error.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n          });\r\n        });\r\n    } else {\r\n      // show error message\r\n      return;\r\n    }\r\n  }\r\n}\r\n", "<form [formGroup]=\"form\" (ngSubmit)=\"onUpdatePlayer()\" #formDirective=\"ngForm\">\r\n    <div class=\"modal-header\">\r\n        <h5 class=\"modal-title text-capitalize\" id=\"myModalLabel160\">{{'Validate player' | translate}}</h5>\r\n        <!-- close -->\r\n        <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"\r\n            (click)=\"modalService.dismissAll()\">\r\n            <span aria-hidden=\"true\">&times;</span>\r\n        </button>\r\n    </div>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n        <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n    </div>\r\n\r\n    <div class=\"modal-footer d-block p-0\">\r\n        <div class=\"row mb-1\">\r\n            <div class=\"col-12 col-sm-4 mt-1\">\r\n                <button type=\"submit\" class=\"btn btn-block btn-outline-warning\" id=\"btnUpdate\">\r\n                    {{ 'Update' | translate }}\r\n                </button>\r\n            </div>\r\n            <div class=\"col-12 col-sm-4 mt-1\">\r\n                <button type=\"button\" class=\"btn btn-block btn-outline-success text-capitalize\" id=\"btnAccept\" (click)=\"acceptAll()\">\r\n                    {{'Accept all' | translate }}\r\n                </button>\r\n            </div>\r\n            <div class=\"col-12 col-sm-4 mt-1\">\r\n                <button type=\"button\" class=\"btn btn-block btn-primary\" (click)=\"onSubmit()\" id=\"btnSubmit\">\r\n                    {{ 'Submit' | translate }}\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
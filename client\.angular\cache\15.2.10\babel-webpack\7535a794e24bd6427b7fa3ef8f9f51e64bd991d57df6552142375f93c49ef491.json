{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class PermissionsGuard {\n  constructor(_authService, _router) {\n    this._authService = _authService;\n    this._router = _router;\n  }\n  canActivate(route, state) {\n    if (this._authService.currentUserValue) {\n      let isArr = false;\n      let permission_page = route.data.permissions;\n      // if permission_page is an array\n      if (Array.isArray(permission_page)) {\n        isArr = true;\n      }\n      // check permissions\n      let user = this._authService.currentUserValue;\n      let permissions = user.role.permissions;\n      // get array of permission ids\n      let permissionIds = permissions.map(permission => {\n        return permission.id;\n      });\n      // if permission_page is not in permissionIds\n      if (isArr) {\n        let hasPermission = permission_page.some(permission => {\n          return permissionIds.includes(permission);\n        });\n        if (!hasPermission) {\n          this._router.navigate(['/']);\n          return false;\n        }\n        return true;\n      } else {\n        if (!permissionIds.includes(permission_page)) {\n          this._router.navigate(['/']);\n          return false;\n        }\n        return true;\n      }\n      return true;\n    }\n    this._router.navigate(['/']);\n    return false;\n  }\n  static #_ = this.ɵfac = function PermissionsGuard_Factory(t) {\n    return new (t || PermissionsGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PermissionsGuard,\n    factory: PermissionsGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";;;AAgBA,OAAM,MAAOA,gBAAgB;EAC3BC,YAAoBC,YAAyB,EAAUC,OAAe;IAAlD,KAAAD,YAAY,GAAZA,YAAY;IAAuB,KAAAC,OAAO,GAAPA,OAAO;EAAW;EACzEC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAM1B,IAAI,IAAI,CAACJ,YAAY,CAACK,gBAAgB,EAAE;MACtC,IAAIC,KAAK,GAAG,KAAK;MACjB,IAAIC,eAAe,GAAGJ,KAAK,CAACK,IAAI,CAACC,WAAW;MAC5C;MACA,IAAIC,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE;QAClCD,KAAK,GAAG,IAAI;;MAEd;MACA,IAAIM,IAAI,GAAG,IAAI,CAACZ,YAAY,CAACK,gBAAgB;MAC7C,IAAII,WAAW,GAAGG,IAAI,CAACC,IAAI,CAACJ,WAAW;MACvC;MACA,IAAIK,aAAa,GAAGL,WAAW,CAACM,GAAG,CAAEC,UAAU,IAAI;QACjD,OAAOA,UAAU,CAACC,EAAE;MACtB,CAAC,CAAC;MACF;MACA,IAAIX,KAAK,EAAE;QACT,IAAIY,aAAa,GAAGX,eAAe,CAACY,IAAI,CAAEH,UAAU,IAAI;UACtD,OAAOF,aAAa,CAACM,QAAQ,CAACJ,UAAU,CAAC;QAC3C,CAAC,CAAC;QACF,IAAI,CAACE,aAAa,EAAE;UAClB,IAAI,CAACjB,OAAO,CAACoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UAC5B,OAAO,KAAK;;QAEd,OAAO,IAAI;OACZ,MAAM;QACL,IAAI,CAACP,aAAa,CAACM,QAAQ,CAACb,eAAe,CAAC,EAAE;UAC5C,IAAI,CAACN,OAAO,CAACoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UAC5B,OAAO,KAAK;;QAEd,OAAO,IAAI;;MAGb,OAAO,IAAI;;IAEb,IAAI,CAACpB,OAAO,CAACoB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,KAAK;EACd;EAAC,QAAAC,CAAA;qBA9CUxB,gBAAgB,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;WAAhB/B,gBAAgB;IAAAgC,OAAA,EAAhBhC,gBAAgB,CAAAiC,IAAA;IAAAC,UAAA,EAFf;EAAM", "names": ["PermissionsGuard", "constructor", "_authService", "_router", "canActivate", "route", "state", "currentUserValue", "isArr", "permission_page", "data", "permissions", "Array", "isArray", "user", "role", "permissionIds", "map", "permission", "id", "hasPermission", "some", "includes", "navigate", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\guards\\permissions.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  ActivatedRouteSnapshot,\r\n  CanActivate,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { Observable } from 'rxjs';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { AppConfig } from 'app/app-config';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PermissionsGuard implements CanActivate {\r\n  constructor(private _authService: AuthService, private _router: Router) {}\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ):\r\n    | Observable<boolean | UrlTree>\r\n    | Promise<boolean | UrlTree>\r\n    | boolean\r\n    | UrlTree {\r\n    if (this._authService.currentUserValue) {\r\n      let isArr = false;\r\n      let permission_page = route.data.permissions;\r\n      // if permission_page is an array\r\n      if (Array.isArray(permission_page)) {\r\n        isArr = true;\r\n      }\r\n      // check permissions\r\n      let user = this._authService.currentUserValue;\r\n      let permissions = user.role.permissions;\r\n      // get array of permission ids\r\n      let permissionIds = permissions.map((permission) => {\r\n        return permission.id;\r\n      });\r\n      // if permission_page is not in permissionIds\r\n      if (isArr) {\r\n        let hasPermission = permission_page.some((permission) => {\r\n          return permissionIds.includes(permission);\r\n        });\r\n        if (!hasPermission) {\r\n          this._router.navigate(['/']);\r\n          return false;\r\n        }\r\n        return true;\r\n      } else {\r\n        if (!permissionIds.includes(permission_page)) {\r\n          this._router.navigate(['/']);\r\n          return false;\r\n        }\r\n        return true;\r\n      }\r\n\r\n      return true;\r\n    }\r\n    this._router.navigate(['/']);\r\n    return false;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
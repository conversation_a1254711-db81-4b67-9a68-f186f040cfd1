{"ast": null, "code": "class TranslateHttpLoader {\n  constructor(http, prefix = \"/assets/i18n/\", suffix = \".json\") {\n    this.http = http;\n    this.prefix = prefix;\n    this.suffix = suffix;\n  }\n  /**\r\n   * Gets the translations from the server\r\n   */\n  getTranslation(lang) {\n    return this.http.get(`${this.prefix}${lang}${this.suffix}`);\n  }\n}\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { TranslateHttpLoader };", "map": {"version": 3, "names": ["TranslateHttpLoader", "constructor", "http", "prefix", "suffix", "getTranslation", "lang", "get"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@ngx-translate/http-loader/fesm2020/ngx-translate-http-loader.mjs"], "sourcesContent": ["class TranslateHttpLoader {\r\n    constructor(http, prefix = \"/assets/i18n/\", suffix = \".json\") {\r\n        this.http = http;\r\n        this.prefix = prefix;\r\n        this.suffix = suffix;\r\n    }\r\n    /**\r\n     * Gets the translations from the server\r\n     */\r\n    getTranslation(lang) {\r\n        return this.http.get(`${this.prefix}${lang}${this.suffix}`);\r\n    }\r\n}\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { TranslateHttpLoader };\n"], "mappings": "AAAA,MAAMA,mBAAmB,CAAC;EACtBC,WAAWA,CAACC,IAAI,EAAEC,MAAM,GAAG,eAAe,EAAEC,MAAM,GAAG,OAAO,EAAE;IAC1D,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACIC,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAE,GAAE,IAAI,CAACJ,MAAO,GAAEG,IAAK,GAAE,IAAI,CAACF,MAAO,EAAC,CAAC;EAC/D;AACJ;;AAEA;AACA;AACA;;AAEA,SAASJ,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport { DataTableDirective } from 'angular-datatables';\nimport { AvailablePlayerModalComponent } from '../assign-players/available-player-modal/available-player-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"app/services/export.service\";\nimport * as i10 from \"angular-datatables\";\nconst _c0 = [\"modalValidator\"];\nexport class TeamPlayersComponent {\n  constructor(route, _router, _commonsService, _http, _translateService, renderer, _teamService, _modalService, _loadingService, _toastService, _exportService) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._translateService = _translateService;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this._exportService = _exportService;\n    this.onSubmitted = new EventEmitter();\n    this.availablePlayers = AvailablePlayerModalComponent;\n    this.dtOptions = {};\n    this.dtTrigger = {};\n    this.dtElement = DataTableDirective;\n    this.isProcessing = false;\n    this.params = {\n      season_id: null,\n      club_id: null,\n      group_id: null\n    };\n    this.team_player_url = '';\n    const teamManagement = localStorage.getItem('teamManagement');\n    const season_id = teamManagement ? JSON.parse(teamManagement).seasonSelected : null;\n    this.seasonId = season_id;\n    this.teamId = this.route.snapshot.paramMap.get('teamId');\n    this.team_player_url = `${environment.apiUrl}/teams/${this.teamId}/players`;\n    this.route.queryParams.subscribe(params => {\n      this.clubId = params['clubId'];\n    });\n  }\n  ngOnInit() {\n    $.fx.off = true; //this is for disable jquery animation\n    // this.dtOptions[0] = this.buildDtOptions1(current_season_player_url, params, buttons_assign);\n    this.dtOptions = this.buildDtOptions(this.team_player_url, this.params);\n  }\n  ngOnChanges(changes) {\n    if (changes['team']) {\n      if (this.team) {\n        let current_season_player_url = `${environment.apiUrl}/registrations/club-group-approved`;\n        this.params = {\n          season_id: this.team.group.season_id,\n          club_id: this.team.club_id,\n          group_id: this.team.group_id\n        };\n        this.dtOptions = this.buildDtOptions(this.team_player_url, this.params);\n      }\n    }\n  }\n  buildDtOptions(url, params, buttons) {\n    var _this = this;\n    return {\n      dom: this._commonsService.dataTableDefaults.dom,\n      ajax: (dataTablesParameters, callback) => {\n        if (params) {\n          dataTablesParameters['season_id'] = this.seasonId;\n          dataTablesParameters['club_id'] = parseInt(params.club_id);\n          dataTablesParameters['group_id'] = parseInt(params.group_id);\n        }\n        this._http.post(`${url}`, dataTablesParameters).subscribe(resp => {\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      select: {\n        toggleable: false\n      },\n      // serverSide: true,\n      rowId: 'id',\n      // fake data\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\n      displayLength: -1,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }, {\n        responsivePriority: 2,\n        targets: 0\n      }, {\n        responsivePriority: 3,\n        targets: 1\n      }],\n      columns: [{\n        //photo\n        data: 'player.photo',\n        render: (data, type, row) => {\n          if (data) {\n            return `<img src=\"${data}\" width=\"50px\" height=\"70px\" />`;\n          } else {\n            return `<img src=\"assets/images/avatars/default.png\" width=\"50px\" height=\"70px\" />`;\n          }\n        }\n      }, {\n        // name\n        data: null,\n        className: 'font-weight-bolder',\n        render: (data, type, row) => {\n          row = row.player.user;\n          const name_settings = JSON.parse(localStorage.getItem('name_settings'));\n          if (row.first_name && row.last_name) {\n            if (name_settings && name_settings.is_on == 1) {\n              return row.first_name + ' ' + row.last_name;\n            } else {\n              return row.last_name + ' ' + row.first_name;\n            }\n          } else {\n            return '';\n          }\n        }\n      }, {\n        //year\n        data: 'player.dob',\n        render: (data, type, row) => {\n          return new Date(data).getFullYear();\n        }\n      }, {\n        //gender\n        data: 'player.gender',\n        render: (data, type, row) => {\n          return data == 'Male' ? this._translateService.instant('Male') : this._translateService.instant('Female');\n        }\n      }, {\n        data: 'player.id',\n        render: (data, type, row) => {\n          return `<button class=\"btn btn-outline-danger btn-sm\" \n            data-row = '${JSON.stringify(row)}'\n            action=\"remove\">` + '<i class=\"fa-solid fa-xmark\"></i>&nbsp;' + this._translateService.instant('Remove') + `</button>`;\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"fa fa-user-plus\"></i> ' + this._translateService.instant('Assign Player'),\n          titleAttr: this._translateService.instant('Assign Player'),\n          action: () => this.assignPlayerToTeam()\n        }, {\n          text: '<i class=\"fa fa-check\"></i> ' + this._translateService.instant('Submit Teamsheet'),\n          titleAttr: this._translateService.instant('Submit Teamsheet'),\n          action: () => this.submitTeamSheet()\n        }, {\n          extend: 'excel',\n          text: `<i class=\"fa-regular fa-file-excel\"></i> ${this._translateService.instant('Export')}`,\n          className: 'float-right mr-2',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              yield _this._exportService.exportExcel(data, 'TeamPlayer.xlsx');\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  assignPlayerToTeam() {\n    // open modal\n    const modalRef = this._modalService.open(AvailablePlayerModalComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      centered: true,\n      keyboard: true\n    });\n    modalRef.componentInstance.params = {\n      team_id: this.teamId,\n      season_id: this.params.season_id,\n      club_id: this.params.club_id,\n      group_id: this.params.group_id\n    };\n    modalRef.result.then(result => {\n      // reload datatable\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }, reason => {\n      // reload datatable\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    });\n  }\n  submitTeamSheet() {\n    // this.onSubmitted.emit();\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: `<div class=\"text-center\">\n              <img src=\"assets/images/alerts/r_u_sure.svg\" width=\"200px\" height=\"149px\">\n              <p class=\"text-center\">` + this._translateService.instant('ask_want_to_submit_teamsheet') + `</p>\n            </div>`,\n      reverseButtons: true,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      confirmButtonColor: '#3085d6',\n      cancelButtonText: '<span class=\"text-primary\">' + this._translateService.instant('No') + '</span>',\n      cancelButtonColor: '#d33',\n      buttonsStyling: false,\n      customClass: {\n        confirmButton: 'btn btn-primary mr-1',\n        cancelButton: 'btn btn-outline-primary mr-1'\n      }\n    }).then(result => {\n      if (result.isConfirmed) {\n        console.log('submit team sheet');\n        this._loadingService.show();\n        this._teamService.submitTeamSheet(this.teamId).subscribe(resp => {\n          this._loadingService.dismiss();\n          // reload datatable\n          this.dtElement.dtInstance.then(dtInstance => {\n            dtInstance.ajax.reload();\n          });\n          console.log('resp', resp);\n          this._toastService.success(this._translateService.instant('Team sheet submitted successfully'));\n        }, err => {\n          this._loadingService.dismiss();\n          Swal.fire({\n            title: 'Warning!',\n            icon: 'warning',\n            text: err.message,\n            confirmButtonText: this._translateService.instant('OK')\n          });\n        });\n      }\n    });\n  }\n  editor(action, row, target = null) {\n    this.isProcessing = true;\n    switch (action) {\n      case 'remove':\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          html: `\n        <div class=\"text-center\">\n          <img src=\"assets/images/alerts/are_you_sure.svg\" width=\"200px\" height=\"149px\">\n          <p class=\"text-center\">` + this._translateService.instant('ask_want_to_remove_player') + `\n          </p>\n        </div>`,\n          reverseButtons: true,\n          allowOutsideClick: false,\n          allowEscapeKey: false,\n          confirmButtonText: this._translateService.instant('Yes'),\n          showCancelButton: true,\n          confirmButtonColor: '#d33',\n          cancelButtonColor: '#3085d6',\n          // cancel buton text color\n          cancelButtonText: this._translateService.instant('Cancel'),\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary mr-1',\n            cancelButton: 'btn btn-outline-primary mr-1'\n          }\n        }).then(result => {\n          if (result.isConfirmed) {\n            let row_id = row.id;\n            let player_id = row.player.id;\n            let params = new FormData();\n            const teamManagement = localStorage.getItem('teamManagement');\n            const season_id = teamManagement ? JSON.parse(teamManagement).seasonSelected : null;\n            params.append('action', 'remove');\n            params.append('data[' + row_id + '][team_id]', this.teamId);\n            params.append('data[' + row_id + '][player_id]', player_id);\n            params.append('data[' + row_id + '][season_id]', season_id);\n            this._teamService.editorTableTeamPlayers(params).subscribe(resp => {\n              if (resp) {\n                this._toastService.success(this._translateService.instant('Player removed successfully'));\n              }\n              // reload\n              this.dtElement.dtInstance.then(dtInstance => {\n                dtInstance.ajax.reload();\n                setTimeout(() => {\n                  this.toggleRemoveButton(target, false);\n                }, 1000);\n              });\n            }, err => {\n              this.toggleRemoveButton(target, false);\n              let message = '';\n              if (err.hasOwnProperty('error')) {\n                if (typeof err.error === 'string') {\n                  message = err.error;\n                } else {\n                  message = err.error[0];\n                }\n              } else {\n                message = err.message;\n              }\n              Swal.fire({\n                title: 'Warning!',\n                icon: 'warning',\n                html: message,\n                confirmButtonText: this._translateService.instant('OK')\n              });\n            });\n          } else {\n            this.toggleRemoveButton(target, false);\n          }\n        });\n        break;\n      default:\n        break;\n    }\n  }\n  toggleRemoveButton(target, action) {\n    if (action) {\n      this.isProcessing = true;\n      target.setAttribute('disabled', 'disabled');\n    } else {\n      this.isProcessing = false;\n      target.removeAttribute('disabled');\n    }\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('data-row')) {\n        let target = event.target;\n        let row = target.getAttribute('data-row');\n        row = JSON.parse(row);\n        this.editor(target.getAttribute('action'), row, target);\n        // disable remove button\n        target.setAttribute('disabled', 'disabled');\n      }\n    });\n  }\n  static #_ = this.ɵfac = function TeamPlayersComponent_Factory(t) {\n    return new (t || TeamPlayersComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.ToastrService), i0.ɵɵdirectiveInject(i9.ExportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamPlayersComponent,\n    selectors: [[\"app-assign-players\"]],\n    viewQuery: function TeamPlayersComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalValidator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      team: \"team\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 23,\n    vars: 16,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [1, \"text-capitalize\"]],\n    template: function TeamPlayersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"h4\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"table\", 5)(6, \"thead\")(7, \"tr\")(8, \"th\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"th\", 6);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"th\", 6);\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"th\", 6);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"th\", 6);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 6, \"photo\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 8, \"Player\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 10, \"Year\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 12, \"gender\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 14, \"Action\"));\n      }\n    },\n    dependencies: [i10.DataTableDirective, i4.TranslatePipe],\n    styles: [\".btn_group[_ngcontent-%COMP%] {\\n  gap: 1em;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdGVhbXMvdGVhbS1hc3NpZ25tZW50L2Fzc2lnbi1wbGF5ZXJzL2Fzc2lnbi1wbGF5ZXJzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksUUFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLmJ0bl9ncm91cHtcclxuICAgIGdhcDoxZW1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AACA,SAGEA,YAAY,QAQP,eAAe;AAItB,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,kBAAkB,QAAQ,oBAAoB;AAGvD,SACEC,6BAA6B,QACxB,2EAA2E;;;;;;;;;;;;;AASlF,OAAM,MAAOC,oBAAoB;EA4B/BC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,iBAAmC,EACnCC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,aAA4B,EAC3BC,cAA6B;IAV7B,KAAAV,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IAnCxB,KAAAC,WAAW,GAAsB,IAAIlB,YAAY,EAAE;IAI5C,KAAAmB,gBAAgB,GAAGf,6BAA6B;IAMvD,KAAAgB,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAQ,EAAE;IAGnB,KAAAC,SAAS,GAAQnB,kBAAkB;IACnC,KAAAoB,YAAY,GAAY,KAAK;IAE7B,KAAAC,MAAM,GAAG;MACPC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX;IACD,KAAAC,eAAe,GAAG,EAAE;IAelB,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,MAAMN,SAAS,GAAGI,cAAc,GAC5BG,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC,CAACK,cAAc,GACzC,IAAI;IACR,IAAI,CAACC,QAAQ,GAAGV,SAAS;IACzB,IAAI,CAACW,MAAM,GAAG,IAAI,CAAC7B,KAAK,CAAC8B,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,QAAQ,CAAC;IACxD,IAAI,CAACX,eAAe,GAAG,GAAG3B,WAAW,CAACuC,MAAM,UAAU,IAAI,CAACJ,MAAM,UAAU;IAC3E,IAAI,CAAC7B,KAAK,CAACkC,WAAW,CAACC,SAAS,CAAElB,MAAM,IAAI;MAC1C,IAAI,CAACmB,MAAM,GAAGnB,MAAM,CAAC,QAAQ,CAAC;IAChC,CAAC,CAAC;EACJ;EAEAoB,QAAQA,CAAA;IACNC,CAAC,CAACC,EAAE,CAACC,GAAG,GAAG,IAAI,CAAC,CAAC;IAGjB;IACA,IAAI,CAAC3B,SAAS,GAAG,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAACpB,eAAe,EAAE,IAAI,CAACJ,MAAM,CAAC;EACzE;EAEAyB,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,MAAM,CAAC,EAAE;MACnB,IAAI,IAAI,CAACC,IAAI,EAAE;QACb,IAAIC,yBAAyB,GAAG,GAAGnD,WAAW,CAACuC,MAAM,oCAAoC;QACzF,IAAI,CAAChB,MAAM,GAAG;UACZC,SAAS,EAAE,IAAI,CAAC0B,IAAI,CAACE,KAAK,CAAC5B,SAAS;UACpCC,OAAO,EAAE,IAAI,CAACyB,IAAI,CAACzB,OAAO;UAC1BC,QAAQ,EAAE,IAAI,CAACwB,IAAI,CAACxB;SACrB;QACD,IAAI,CAACP,SAAS,GAAG,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAACpB,eAAe,EAAE,IAAI,CAACJ,MAAM,CAAC;;;EAG7E;EAEAwB,cAAcA,CAACM,GAAG,EAAE9B,MAAW,EAAE+B,OAAe;IAAA,IAAAC,KAAA;IAC9C,OAAO;MACLC,GAAG,EAAE,IAAI,CAAChD,eAAe,CAACiD,iBAAiB,CAACD,GAAG;MAC/CE,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAIrC,MAAM,EAAE;UACVoC,oBAAoB,CAAC,WAAW,CAAC,GAAG,IAAI,CAACzB,QAAQ;UACjDyB,oBAAoB,CAAC,SAAS,CAAC,GAAGE,QAAQ,CAACtC,MAAM,CAACE,OAAO,CAAC;UAC1DkC,oBAAoB,CAAC,UAAU,CAAC,GAAGE,QAAQ,CAACtC,MAAM,CAACG,QAAQ,CAAC;;QAE9D,IAAI,CAACjB,KAAK,CACPqD,IAAI,CAAM,GAAGT,GAAG,EAAE,EAAEM,oBAAoB,CAAC,CACzClB,SAAS,CAAEsB,IAAS,IAAI;UACvBH,QAAQ,CAAC;YACP;YACAI,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MAEDC,MAAM,EAAE;QACNC,UAAU,EAAE;OACb;MACD;MACAC,KAAK,EAAE,IAAI;MACX;MACAC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAAChE,eAAe,CAACiD,iBAAiB,CAACgB,IAAI;MACrDC,UAAU,EAAE,IAAI,CAAClE,eAAe,CAACiD,iBAAiB,CAACiB,UAAU;MAC7DC,aAAa,EAAE,CAAC,CAAC;MACjBC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,EACrC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,CACtC;MACDC,OAAO,EAAE,CACP;QACE;QACAb,IAAI,EAAE,cAAc;QACpBc,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1B,IAAIhB,IAAI,EAAE;YACR,OAAO,aAAaA,IAAI,iCAAiC;WAC1D,MAAM;YACL,OAAO,4EAA4E;;QAEvF;OACD,EACD;QACE;QACAA,IAAI,EAAE,IAAI;QACViB,SAAS,EAAE,oBAAoB;QAC/BH,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1BA,GAAG,GAAGA,GAAG,CAACE,MAAM,CAACC,IAAI;UACrB,MAAMC,aAAa,GAAGvD,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;UACvE,IAAIoD,GAAG,CAACK,UAAU,IAAIL,GAAG,CAACM,SAAS,EAAE;YACnC,IAAIF,aAAa,IAAIA,aAAa,CAACG,KAAK,IAAI,CAAC,EAAE;cAC7C,OAAOP,GAAG,CAACK,UAAU,GAAG,GAAG,GAAGL,GAAG,CAACM,SAAS;aAC5C,MAAM;cACL,OAAON,GAAG,CAACM,SAAS,GAAG,GAAG,GAAGN,GAAG,CAACK,UAAU;;WAE9C,MAAM;YACL,OAAO,EAAE;;QAEb;OACD,EACD;QACE;QACArB,IAAI,EAAE,YAAY;QAClBc,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAO,IAAIQ,IAAI,CAACxB,IAAI,CAAC,CAACyB,WAAW,EAAE;QACrC;OACD,EACD;QACE;QACAzB,IAAI,EAAE,eAAe;QACrBc,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1B,OAAOhB,IAAI,IAAI,MAAM,GACjB,IAAI,CAACxD,iBAAiB,CAACkF,OAAO,CAAC,MAAM,CAAC,GACtC,IAAI,CAAClF,iBAAiB,CAACkF,OAAO,CAAC,QAAQ,CAAC;QAC9C;OACD,EAED;QACE1B,IAAI,EAAE,WAAW;QACjBc,MAAM,EAAEA,CAACd,IAAI,EAAEe,IAAI,EAAEC,GAAG,KAAI;UAC1B,OACE;0BACYnD,IAAI,CAAC8D,SAAS,CAACX,GAAG,CAAC;6BAChB,GACf,yCAAyC,GACzC,IAAI,CAACxE,iBAAiB,CAACkF,OAAO,CAAC,QAAQ,CAAC,GACxC,WAAW;QAEf;OACD,CACF;MACDtC,OAAO,EAAE;QACPE,GAAG,EAAE,IAAI,CAAChD,eAAe,CAACiD,iBAAiB,CAACH,OAAO,CAACE,GAAG;QACvDF,OAAO,EAAE,CACP;UACEwC,IAAI,EACF,kCAAkC,GAClC,IAAI,CAACpF,iBAAiB,CAACkF,OAAO,CAAC,eAAe,CAAC;UACjDG,SAAS,EAAE,IAAI,CAACrF,iBAAiB,CAACkF,OAAO,CAAC,eAAe,CAAC;UAC1DI,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,kBAAkB;SACtC,EACD;UACEH,IAAI,EACF,8BAA8B,GAC9B,IAAI,CAACpF,iBAAiB,CAACkF,OAAO,CAAC,kBAAkB,CAAC;UACpDG,SAAS,EAAE,IAAI,CAACrF,iBAAiB,CAACkF,OAAO,CAAC,kBAAkB,CAAC;UAC7DI,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACE,eAAe;SACnC,EACD;UACEC,MAAM,EAAE,OAAO;UACfL,IAAI,EAAE,4CAA4C,IAAI,CAACpF,iBAAiB,CAACkF,OAAO,CAC9E,QAAQ,CACT,EAAE;UACHT,SAAS,EAAE,kBAAkB;UAC7Ba,MAAM;YAAA,IAAAI,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAMvC,IAAI,GAAGqC,EAAE,CAACjD,OAAO,CAACoD,UAAU,EAAE;cACpC,MAAMnD,KAAI,CAACvC,cAAc,CAAC2F,WAAW,CAACzC,IAAI,EAAE,iBAAiB,CAAC;YAChE,CAAC;YAAA,gBAAA8B,OAAAY,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAhB,kBAAkBA,CAAA;IAChB;IACA,MAAMiB,QAAQ,GAAG,IAAI,CAACrG,aAAa,CAACsG,IAAI,CAAChH,6BAA6B,EAAE;MACtEiH,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,CAAC;IAEFL,QAAQ,CAACM,iBAAiB,CAACjG,MAAM,GAAG;MAClCkG,OAAO,EAAE,IAAI,CAACtF,MAAM;MACpBX,SAAS,EAAE,IAAI,CAACD,MAAM,CAACC,SAAS;MAChCC,OAAO,EAAE,IAAI,CAACF,MAAM,CAACE,OAAO;MAC5BC,QAAQ,EAAE,IAAI,CAACH,MAAM,CAACG;KACvB;IAEDwF,QAAQ,CAACQ,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAI,CAACrG,SAAS,CAACuG,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;QAC5DA,UAAU,CAAClE,IAAI,CAACmE,MAAM,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,EACAC,MAAM,IAAI;MACT;MACA,IAAI,CAACzG,SAAS,CAACuG,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;QAC5DA,UAAU,CAAClE,IAAI,CAACmE,MAAM,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CACF;EACH;EAEA3B,eAAeA,CAAA;IACb;IACAjG,IAAI,CAAC8H,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACtH,iBAAiB,CAACkF,OAAO,CAAC,eAAe,CAAC;MACtDqC,IAAI,EACF;;sCAE8B,GAC9B,IAAI,CAACvH,iBAAiB,CAACkF,OAAO,CAAC,8BAA8B,CAAC,GAC9D;mBACW;MACbsC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAAC1H,iBAAiB,CAACkF,OAAO,CAAC,KAAK,CAAC;MACxDyC,kBAAkB,EAAE,SAAS;MAC7BC,gBAAgB,EACd,6BAA6B,GAC7B,IAAI,CAAC5H,iBAAiB,CAACkF,OAAO,CAAC,IAAI,CAAC,GACpC,SAAS;MACX2C,iBAAiB,EAAE,MAAM;MACzBC,cAAc,EAAE,KAAK;MACrBC,WAAW,EAAE;QACXC,aAAa,EAAE,sBAAsB;QACrCC,YAAY,EAAE;;KAEjB,CAAC,CAAChB,IAAI,CAAED,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACkB,WAAW,EAAE;QACtBC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC,IAAI,CAAChI,eAAe,CAACiI,IAAI,EAAE;QAC3B,IAAI,CAACnI,YAAY,CAACsF,eAAe,CAAC,IAAI,CAAC/D,MAAM,CAAC,CAACM,SAAS,CACrDsB,IAAI,IAAI;UACP,IAAI,CAACjD,eAAe,CAACkI,OAAO,EAAE;UAC9B;UACA,IAAI,CAAC3H,SAAS,CAACuG,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;YAC5DA,UAAU,CAAClE,IAAI,CAACmE,MAAM,EAAE;UAC1B,CAAC,CAAC;UACFgB,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE/E,IAAI,CAAC;UACzB,IAAI,CAAChD,aAAa,CAACkI,OAAO,CACxB,IAAI,CAACvI,iBAAiB,CAACkF,OAAO,CAC5B,mCAAmC,CACpC,CACF;QACH,CAAC,EACAsD,GAAG,IAAI;UACN,IAAI,CAACpI,eAAe,CAACkI,OAAO,EAAE;UAC9B/I,IAAI,CAAC8H,IAAI,CAAC;YACRC,KAAK,EAAE,UAAU;YACjBmB,IAAI,EAAE,SAAS;YACfrD,IAAI,EAAEoD,GAAG,CAACE,OAAO;YACjBhB,iBAAiB,EAAE,IAAI,CAAC1H,iBAAiB,CAACkF,OAAO,CAAC,IAAI;WACvD,CAAC;QACJ,CAAC,CACF;;IAEL,CAAC,CAAC;EACJ;EAEAyD,MAAMA,CAACrD,MAAM,EAAEd,GAAG,EAAEoE,MAAM,GAAG,IAAI;IAC/B,IAAI,CAAChI,YAAY,GAAG,IAAI;IACxB,QAAQ0E,MAAM;MACZ,KAAK,QAAQ;QACX/F,IAAI,CAAC8H,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACtH,iBAAiB,CAACkF,OAAO,CAAC,eAAe,CAAC;UACtDqC,IAAI,EACF;;;kCAGsB,GACtB,IAAI,CAACvH,iBAAiB,CAACkF,OAAO,CAAC,2BAA2B,CAAC,GAC3D;;eAEG;UACLsC,cAAc,EAAE,IAAI;UACpBqB,iBAAiB,EAAE,KAAK;UACxBC,cAAc,EAAE,KAAK;UACrBpB,iBAAiB,EAAE,IAAI,CAAC1H,iBAAiB,CAACkF,OAAO,CAAC,KAAK,CAAC;UACxDuC,gBAAgB,EAAE,IAAI;UACtBE,kBAAkB,EAAE,MAAM;UAC1BE,iBAAiB,EAAE,SAAS;UAC5B;UACAD,gBAAgB,EAAE,IAAI,CAAC5H,iBAAiB,CAACkF,OAAO,CAAC,QAAQ,CAAC;UAC1D4C,cAAc,EAAE,KAAK;UACrBC,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCC,YAAY,EAAE;;SAEjB,CAAC,CAAChB,IAAI,CAAED,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACkB,WAAW,EAAE;YACtB,IAAIa,MAAM,GAAGvE,GAAG,CAACwE,EAAE;YACnB,IAAIC,SAAS,GAAGzE,GAAG,CAACE,MAAM,CAACsE,EAAE;YAC7B,IAAInI,MAAM,GAAa,IAAIqI,QAAQ,EAAE;YACrC,MAAMhI,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;YAC7D,MAAMN,SAAS,GAAGI,cAAc,GAC5BG,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC,CAACK,cAAc,GACzC,IAAI;YACRV,MAAM,CAACsI,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;YACjCtI,MAAM,CAACsI,MAAM,CAAC,OAAO,GAAGJ,MAAM,GAAG,YAAY,EAAE,IAAI,CAACtH,MAAM,CAAC;YAC3DZ,MAAM,CAACsI,MAAM,CAAC,OAAO,GAAGJ,MAAM,GAAG,cAAc,EAAEE,SAAS,CAAC;YAC3DpI,MAAM,CAACsI,MAAM,CAAC,OAAO,GAAGJ,MAAM,GAAG,cAAc,EAAEjI,SAAS,CAAC;YAE3D,IAAI,CAACZ,YAAY,CAACkJ,sBAAsB,CAACvI,MAAM,CAAC,CAACkB,SAAS,CACvDsB,IAAS,IAAI;cACZ,IAAIA,IAAI,EAAE;gBACR,IAAI,CAAChD,aAAa,CAACkI,OAAO,CACxB,IAAI,CAACvI,iBAAiB,CAACkF,OAAO,CAC5B,6BAA6B,CAC9B,CACF;;cAGH;cACA,IAAI,CAACvE,SAAS,CAACuG,UAAU,CAACD,IAAI,CAAEC,UAA0B,IAAI;gBAC5DA,UAAU,CAAClE,IAAI,CAACmE,MAAM,EAAE;gBACxBkC,UAAU,CAAC,MAAK;kBACd,IAAI,CAACC,kBAAkB,CAACV,MAAM,EAAE,KAAK,CAAC;gBACxC,CAAC,EAAE,IAAI,CAAC;cACV,CAAC,CAAC;YACJ,CAAC,EACAJ,GAAG,IAAI;cACN,IAAI,CAACc,kBAAkB,CAACV,MAAM,EAAE,KAAK,CAAC;cACtC,IAAIF,OAAO,GAAG,EAAE;cAChB,IAAIF,GAAG,CAACe,cAAc,CAAC,OAAO,CAAC,EAAE;gBAC/B,IAAI,OAAOf,GAAG,CAACgB,KAAK,KAAK,QAAQ,EAAE;kBACjCd,OAAO,GAAGF,GAAG,CAACgB,KAAK;iBACpB,MAAM;kBACLd,OAAO,GAAGF,GAAG,CAACgB,KAAK,CAAC,CAAC,CAAC;;eAEzB,MAAM;gBACLd,OAAO,GAAGF,GAAG,CAACE,OAAO;;cAEvBnJ,IAAI,CAAC8H,IAAI,CAAC;gBACRC,KAAK,EAAE,UAAU;gBACjBmB,IAAI,EAAE,SAAS;gBACflB,IAAI,EAAEmB,OAAO;gBACbhB,iBAAiB,EAAE,IAAI,CAAC1H,iBAAiB,CAACkF,OAAO,CAAC,IAAI;eACvD,CAAC;YACJ,CAAC,CACF;WACF,MAAM;YACL,IAAI,CAACoE,kBAAkB,CAACV,MAAM,EAAE,KAAK,CAAC;;QAE1C,CAAC,CAAC;QACF;MAEF;QACE;;EAEN;EAEAU,kBAAkBA,CAACV,MAAM,EAAEtD,MAAM;IAC/B,IAAIA,MAAM,EAAE;MACV,IAAI,CAAC1E,YAAY,GAAG,IAAI;MACxBgI,MAAM,CAACa,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;KAC5C,MAAM;MACL,IAAI,CAAC7I,YAAY,GAAG,KAAK;MACzBgI,MAAM,CAACc,eAAe,CAAC,UAAU,CAAC;;EAEtC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC3J,QAAQ,CAAC4J,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGC,KAAK,IAAI;MACpE,IAAIA,KAAK,CAAClB,MAAM,CAACmB,YAAY,CAAC,UAAU,CAAC,EAAE;QACzC,IAAInB,MAAM,GAAGkB,KAAK,CAAClB,MAAM;QACzB,IAAIpE,GAAG,GAAGoE,MAAM,CAACoB,YAAY,CAAC,UAAU,CAAC;QACzCxF,GAAG,GAAGnD,IAAI,CAACC,KAAK,CAACkD,GAAG,CAAC;QACrB,IAAI,CAACmE,MAAM,CAACC,MAAM,CAACoB,YAAY,CAAC,QAAQ,CAAC,EAAExF,GAAG,EAAEoE,MAAM,CAAC;QACvD;QACAA,MAAM,CAACa,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;;IAE/C,CAAC,CAAC;EACJ;EAAC,QAAAQ,CAAA;qBAxZUvK,oBAAoB,EAAAwK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAW,SAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,QAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAAjB,EAAA,CAAAC,iBAAA,CAAAiB,EAAA,CAAAC,aAAA,GAAAnB,EAAA,CAAAC,iBAAA,CAAAmB,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAApB9L,oBAAoB;IAAA+L,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBAiBpBpM,kBAAkB;;;;;;;;;;;;;;;;;QClD/B0K,EAAA,CAAA4B,cAAA,aAA+C;QAOvC5B,EAAA,CAAA6B,SAAA,YAGK;QACP7B,EAAA,CAAA8B,YAAA,EAAM;QAEN9B,EAAA,CAAA4B,cAAA,eAIC;QAGiC5B,EAAA,CAAA+B,MAAA,GAAyB;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QAC1D9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAA0B;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QAC3D9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAAwB;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QACzD9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAA0B;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;QAC3D9B,EAAA,CAAA4B,cAAA,aAA4B;QAAA5B,EAAA,CAAA+B,MAAA,IAA0B;;QAAA/B,EAAA,CAAA8B,YAAA,EAAK;;;QAT/D9B,EAAA,CAAAgC,SAAA,GAAuB;QAAvBhC,EAAA,CAAAiC,UAAA,cAAAN,GAAA,CAAApL,SAAA,CAAuB;QAKSyJ,EAAA,CAAAgC,SAAA,GAAyB;QAAzBhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,iBAAyB;QACzBnC,EAAA,CAAAgC,SAAA,GAA0B;QAA1BhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,kBAA0B;QAC1BnC,EAAA,CAAAgC,SAAA,GAAwB;QAAxBhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,iBAAwB;QACxBnC,EAAA,CAAAgC,SAAA,GAA0B;QAA1BhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,mBAA0B;QAC1BnC,EAAA,CAAAgC,SAAA,GAA0B;QAA1BhC,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAmC,WAAA,mBAA0B", "names": ["EventEmitter", "environment", "<PERSON><PERSON>", "DataTableDirective", "AvailablePlayerModalComponent", "TeamPlayersComponent", "constructor", "route", "_router", "_commonsService", "_http", "_translateService", "renderer", "_teamService", "_modalService", "_loadingService", "_toastService", "_exportService", "onSubmitted", "availablePlayers", "dtOptions", "dtTrigger", "dtElement", "isProcessing", "params", "season_id", "club_id", "group_id", "team_player_url", "teamManagement", "localStorage", "getItem", "JSON", "parse", "seasonSelected", "seasonId", "teamId", "snapshot", "paramMap", "get", "apiUrl", "queryParams", "subscribe", "clubId", "ngOnInit", "$", "fx", "off", "buildDtOptions", "ngOnChanges", "changes", "team", "current_season_player_url", "group", "url", "buttons", "_this", "dom", "dataTableDefaults", "ajax", "dataTablesParameters", "callback", "parseInt", "post", "resp", "recordsTotal", "recordsFiltered", "data", "select", "toggleable", "rowId", "responsive", "scrollX", "language", "lang", "lengthMenu", "displayLength", "columnDefs", "responsivePriority", "targets", "columns", "render", "type", "row", "className", "player", "user", "name_settings", "first_name", "last_name", "is_on", "Date", "getFullYear", "instant", "stringify", "text", "titleAttr", "action", "assignPlayerToTeam", "submitTeamSheet", "extend", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "exportExcel", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "modalRef", "open", "size", "backdrop", "centered", "keyboard", "componentInstance", "team_id", "result", "then", "dtInstance", "reload", "reason", "fire", "title", "html", "reverseButtons", "showCancelButton", "confirmButtonText", "confirmButtonColor", "cancelButtonText", "cancelButtonColor", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "isConfirmed", "console", "log", "show", "dismiss", "success", "err", "icon", "message", "editor", "target", "allowOutsideClick", "allowEscapeKey", "row_id", "id", "player_id", "FormData", "append", "editorTableTeamPlayers", "setTimeout", "toggleRemoveButton", "hasOwnProperty", "error", "setAttribute", "removeAttribute", "ngAfterViewInit", "unlistener", "listen", "event", "hasAttribute", "getAttribute", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "CommonsService", "i3", "HttpClient", "i4", "TranslateService", "Renderer2", "i5", "TeamService", "i6", "NgbModal", "i7", "LoadingService", "i8", "ToastrService", "i9", "ExportService", "_2", "selectors", "viewQuery", "TeamPlayersComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "ɵɵpipeBind1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-players\\assign-players.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-players\\assign-players.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  AfterViewInit,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnChanges,\r\n  OnInit,\r\n  Renderer2,\r\n  SimpleChanges,\r\n  TemplateRef,\r\n  ViewChild\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { environment } from 'environments/environment';\r\nimport Swal from 'sweetalert2';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport {\r\n  AvailablePlayerModalComponent\r\n} from '../assign-players/available-player-modal/available-player-modal.component';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ExportService } from 'app/services/export.service';\r\n\r\n@Component({\r\n  selector: 'app-assign-players',\r\n  templateUrl: './assign-players.component.html',\r\n  styleUrls: ['./assign-players.component.scss']\r\n})\r\nexport class TeamPlayersComponent implements AfterViewInit, OnInit, OnChanges {\r\n\r\n  @Input() team: any;\r\n\r\n  onSubmitted: EventEmitter<any> = new EventEmitter();\r\n  public teamId: any;\r\n  private unlistener: () => void;\r\n\r\n  public availablePlayers = AvailablePlayerModalComponent;\r\n\r\n  public seasonId: any;\r\n  public clubId: any;\r\n  public groupId: any;\r\n  @ViewChild('modalValidator') modalValidator: TemplateRef<any>;\r\n  dtOptions: any = {};\r\n  dtTrigger: any = {};\r\n  public modalRef: any;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  isProcessing: boolean = false;\r\n\r\n  params = {\r\n    season_id: null,\r\n    club_id: null,\r\n    group_id: null\r\n  };\r\n  team_player_url = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _translateService: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _toastService: ToastrService,\r\n    private _exportService: ExportService\r\n  ) {\r\n    const teamManagement = localStorage.getItem('teamManagement');\r\n    const season_id = teamManagement\r\n      ? JSON.parse(teamManagement).seasonSelected\r\n      : null;\r\n    this.seasonId = season_id;\r\n    this.teamId = this.route.snapshot.paramMap.get('teamId');\r\n    this.team_player_url = `${environment.apiUrl}/teams/${this.teamId}/players`;\r\n    this.route.queryParams.subscribe((params) => {\r\n      this.clubId = params['clubId'];\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    $.fx.off = true; //this is for disable jquery animation\r\n\r\n\r\n    // this.dtOptions[0] = this.buildDtOptions1(current_season_player_url, params, buttons_assign);\r\n    this.dtOptions = this.buildDtOptions(this.team_player_url, this.params);\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['team']) {\r\n      if (this.team) {\r\n        let current_season_player_url = `${environment.apiUrl}/registrations/club-group-approved`;\r\n        this.params = {\r\n          season_id: this.team.group.season_id,\r\n          club_id: this.team.club_id,\r\n          group_id: this.team.group_id\r\n        };\r\n        this.dtOptions = this.buildDtOptions(this.team_player_url, this.params);\r\n      }\r\n    }\r\n  }\r\n\r\n  buildDtOptions(url, params: any, buttons?: any[]) {\r\n    return {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        if (params) {\r\n          dataTablesParameters['season_id'] = this.seasonId;\r\n          dataTablesParameters['club_id'] = parseInt(params.club_id);\r\n          dataTablesParameters['group_id'] = parseInt(params.group_id);\r\n        }\r\n        this._http\r\n          .post<any>(`${url}`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n\r\n      select: {\r\n        toggleable: false\r\n      },\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      // fake data\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\r\n      displayLength: -1,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: -1 },\r\n        { responsivePriority: 2, targets: 0 },\r\n        { responsivePriority: 3, targets: 1 }\r\n      ],\r\n      columns: [\r\n        {\r\n          //photo\r\n          data: 'player.photo',\r\n          render: (data, type, row) => {\r\n            if (data) {\r\n              return `<img src=\"${data}\" width=\"50px\" height=\"70px\" />`;\r\n            } else {\r\n              return `<img src=\"assets/images/avatars/default.png\" width=\"50px\" height=\"70px\" />`;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          // name\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          render: (data, type, row) => {\r\n            row = row.player.user;\r\n            const name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n            if (row.first_name && row.last_name) {\r\n              if (name_settings && name_settings.is_on == 1) {\r\n                return row.first_name + ' ' + row.last_name;\r\n              } else {\r\n                return row.last_name + ' ' + row.first_name;\r\n              }\r\n            } else {\r\n              return '';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          //year\r\n          data: 'player.dob',\r\n          render: (data, type, row) => {\r\n            return new Date(data).getFullYear();\r\n          }\r\n        },\r\n        {\r\n          //gender\r\n          data: 'player.gender',\r\n          render: (data, type, row) => {\r\n            return data == 'Male'\r\n              ? this._translateService.instant('Male')\r\n              : this._translateService.instant('Female');\r\n          }\r\n        },\r\n\r\n        {\r\n          data: 'player.id',\r\n          render: (data, type, row) => {\r\n            return (\r\n              `<button class=\"btn btn-outline-danger btn-sm\" \r\n            data-row = '${JSON.stringify(row)}'\r\n            action=\"remove\">` +\r\n              '<i class=\"fa-solid fa-xmark\"></i>&nbsp;' +\r\n              this._translateService.instant('Remove') +\r\n              `</button>`\r\n            );\r\n          }\r\n        }\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"fa fa-user-plus\"></i> ' +\r\n              this._translateService.instant('Assign Player'),\r\n            titleAttr: this._translateService.instant('Assign Player'),\r\n            action: () => this.assignPlayerToTeam()\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"fa fa-check\"></i> ' +\r\n              this._translateService.instant('Submit Teamsheet'),\r\n            titleAttr: this._translateService.instant('Submit Teamsheet'),\r\n            action: () => this.submitTeamSheet()\r\n          },\r\n          {\r\n            extend: 'excel',\r\n            text: `<i class=\"fa-regular fa-file-excel\"></i> ${this._translateService.instant(\r\n              'Export'\r\n            )}`,\r\n            className: 'float-right mr-2',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              await this._exportService.exportExcel(data, 'TeamPlayer.xlsx');\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  assignPlayerToTeam() {\r\n    // open modal\r\n    const modalRef = this._modalService.open(AvailablePlayerModalComponent, {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      centered: true,\r\n      keyboard: true\r\n    });\r\n\r\n    modalRef.componentInstance.params = {\r\n      team_id: this.teamId,\r\n      season_id: this.params.season_id,\r\n      club_id: this.params.club_id,\r\n      group_id: this.params.group_id\r\n    };\r\n\r\n    modalRef.result.then(\r\n      (result) => {\r\n        // reload datatable\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n        });\r\n      },\r\n      (reason) => {\r\n        // reload datatable\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  submitTeamSheet() {\r\n    // this.onSubmitted.emit();\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        `<div class=\"text-center\">\r\n              <img src=\"assets/images/alerts/r_u_sure.svg\" width=\"200px\" height=\"149px\">\r\n              <p class=\"text-center\">` +\r\n        this._translateService.instant('ask_want_to_submit_teamsheet') +\r\n        `</p>\r\n            </div>`,\r\n      reverseButtons: true,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      confirmButtonColor: '#3085d6',\r\n      cancelButtonText:\r\n        '<span class=\"text-primary\">' +\r\n        this._translateService.instant('No') +\r\n        '</span>',\r\n      cancelButtonColor: '#d33',\r\n      buttonsStyling: false,\r\n      customClass: {\r\n        confirmButton: 'btn btn-primary mr-1',\r\n        cancelButton: 'btn btn-outline-primary mr-1'\r\n      }\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        console.log('submit team sheet');\r\n        this._loadingService.show();\r\n        this._teamService.submitTeamSheet(this.teamId).subscribe(\r\n          (resp) => {\r\n            this._loadingService.dismiss();\r\n            // reload datatable\r\n            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n              dtInstance.ajax.reload();\r\n            });\r\n            console.log('resp', resp);\r\n            this._toastService.success(\r\n              this._translateService.instant(\r\n                'Team sheet submitted successfully'\r\n              )\r\n            );\r\n          },\r\n          (err) => {\r\n            this._loadingService.dismiss();\r\n            Swal.fire({\r\n              title: 'Warning!',\r\n              icon: 'warning',\r\n              text: err.message,\r\n              confirmButtonText: this._translateService.instant('OK')\r\n            });\r\n          }\r\n        );\r\n      }\r\n    });\r\n  }\r\n\r\n  editor(action, row, target = null) {\r\n    this.isProcessing = true;\r\n    switch (action) {\r\n      case 'remove':\r\n        Swal.fire({\r\n          title: this._translateService.instant('Are you sure?'),\r\n          html:\r\n            `\r\n        <div class=\"text-center\">\r\n          <img src=\"assets/images/alerts/are_you_sure.svg\" width=\"200px\" height=\"149px\">\r\n          <p class=\"text-center\">` +\r\n            this._translateService.instant('ask_want_to_remove_player') +\r\n            `\r\n          </p>\r\n        </div>`,\r\n          reverseButtons: true,\r\n          allowOutsideClick: false,\r\n          allowEscapeKey: false,\r\n          confirmButtonText: this._translateService.instant('Yes'),\r\n          showCancelButton: true,\r\n          confirmButtonColor: '#d33',\r\n          cancelButtonColor: '#3085d6',\r\n          // cancel buton text color\r\n          cancelButtonText: this._translateService.instant('Cancel'),\r\n          buttonsStyling: false,\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary mr-1',\r\n            cancelButton: 'btn btn-outline-primary mr-1'\r\n          }\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            let row_id = row.id;\r\n            let player_id = row.player.id;\r\n            let params: FormData = new FormData();\r\n            const teamManagement = localStorage.getItem('teamManagement');\r\n            const season_id = teamManagement\r\n              ? JSON.parse(teamManagement).seasonSelected\r\n              : null;\r\n            params.append('action', 'remove');\r\n            params.append('data[' + row_id + '][team_id]', this.teamId);\r\n            params.append('data[' + row_id + '][player_id]', player_id);\r\n            params.append('data[' + row_id + '][season_id]', season_id);\r\n\r\n            this._teamService.editorTableTeamPlayers(params).subscribe(\r\n              (resp: any) => {\r\n                if (resp) {\r\n                  this._toastService.success(\r\n                    this._translateService.instant(\r\n                      'Player removed successfully'\r\n                    )\r\n                  );\r\n                }\r\n\r\n                // reload\r\n                this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n                  dtInstance.ajax.reload();\r\n                  setTimeout(() => {\r\n                    this.toggleRemoveButton(target, false);\r\n                  }, 1000);\r\n                });\r\n              },\r\n              (err) => {\r\n                this.toggleRemoveButton(target, false);\r\n                let message = '';\r\n                if (err.hasOwnProperty('error')) {\r\n                  if (typeof err.error === 'string') {\r\n                    message = err.error;\r\n                  } else {\r\n                    message = err.error[0];\r\n                  }\r\n                } else {\r\n                  message = err.message;\r\n                }\r\n                Swal.fire({\r\n                  title: 'Warning!',\r\n                  icon: 'warning',\r\n                  html: message,\r\n                  confirmButtonText: this._translateService.instant('OK')\r\n                });\r\n              }\r\n            );\r\n          } else {\r\n            this.toggleRemoveButton(target, false);\r\n          }\r\n        });\r\n        break;\r\n\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  toggleRemoveButton(target, action) {\r\n    if (action) {\r\n      this.isProcessing = true;\r\n      target.setAttribute('disabled', 'disabled');\r\n    } else {\r\n      this.isProcessing = false;\r\n      target.removeAttribute('disabled');\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('data-row')) {\r\n        let target = event.target;\r\n        let row = target.getAttribute('data-row');\r\n        row = JSON.parse(row);\r\n        this.editor(target.getAttribute('action'), row, target);\r\n        // disable remove button\r\n        target.setAttribute('disabled', 'disabled');\r\n      }\r\n    });\r\n  }\r\n}", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <!-- <app-content-header [contentHeader]=\"contentHeader\"></app-content-header> -->\r\n\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h4 class=\"card-title\">\r\n          <!-- {{ 'Team' | translate }} {{ currentTeam?.name }} -\r\n          {{ currentTeam?.group?.name }} -->\r\n        </h4>\r\n      </div>\r\n\r\n      <table\r\n        datatable\r\n        [dtOptions]=\"dtOptions\"\r\n        class=\"table border row-border hover\"\r\n      >\r\n        <thead>\r\n          <tr>\r\n            <th class=\"text-capitalize\">{{ 'photo' | translate }}</th>\r\n            <th class=\"text-capitalize\">{{ 'Player' | translate }}</th>\r\n            <th class=\"text-capitalize\">{{ 'Year' | translate }}</th>\r\n            <th class=\"text-capitalize\">{{ 'gender' | translate }}</th>\r\n            <th class=\"text-capitalize\">{{ 'Action' | translate }}</th>\r\n          </tr>\r\n        </thead>\r\n      </table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
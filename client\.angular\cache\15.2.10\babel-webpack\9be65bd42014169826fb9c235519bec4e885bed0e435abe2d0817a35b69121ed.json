{"ast": null, "code": "import { multicast } from './multicast';\nimport { refCount } from './refCount';\nimport { Subject } from '../Subject';\nfunction shareSubjectFactory() {\n  return new Subject();\n}\nexport function share() {\n  return source => refCount()(multicast(shareSubjectFactory)(source));\n}", "map": {"version": 3, "names": ["multicast", "refCount", "Subject", "shareSubjectFactory", "share", "source"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/share.js"], "sourcesContent": ["import { multicast } from './multicast';\nimport { refCount } from './refCount';\nimport { Subject } from '../Subject';\nfunction shareSubjectFactory() {\n    return new Subject();\n}\nexport function share() {\n    return (source) => refCount()(multicast(shareSubjectFactory)(source));\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,mBAAmBA,CAAA,EAAG;EAC3B,OAAO,IAAID,OAAO,CAAC,CAAC;AACxB;AACA,OAAO,SAASE,KAAKA,CAAA,EAAG;EACpB,OAAQC,MAAM,IAAKJ,QAAQ,CAAC,CAAC,CAACD,SAAS,CAACG,mBAAmB,CAAC,CAACE,MAAM,CAAC,CAAC;AACzE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { defaults as defaultOptions, HOOKS } from \"./types/options\";\nimport English from \"./l10n/default\";\nimport { arrayify, debounce, int, pad } from \"./utils\";\nimport { clearNode, createElement, createNumberInput, findParent, toggleClass, getEventTarget } from \"./utils/dom\";\nimport { compareDates, createDateParser, createDateFormatter, duration, isBetween, getDefaultHours } from \"./utils/dates\";\nimport { tokenRegex, monthToStr } from \"./utils/formatting\";\nimport \"./utils/polyfills\";\nconst DEBOUNCED_CHANGE_MS = 300;\nfunction FlatpickrInstance(element, instanceConfig) {\n  const self = {\n    config: Object.assign(Object.assign({}, defaultOptions), flatpickr.defaultConfig),\n    l10n: English\n  };\n  self.parseDate = createDateParser({\n    config: self.config,\n    l10n: self.l10n\n  });\n  self._handlers = [];\n  self.pluginElements = [];\n  self.loadedPlugins = [];\n  self._bind = bind;\n  self._setHoursFromDate = setHoursFromDate;\n  self._positionCalendar = positionCalendar;\n  self.changeMonth = changeMonth;\n  self.changeYear = changeYear;\n  self.clear = clear;\n  self.close = close;\n  self._createElement = createElement;\n  self.destroy = destroy;\n  self.isEnabled = isEnabled;\n  self.jumpToDate = jumpToDate;\n  self.open = open;\n  self.redraw = redraw;\n  self.set = set;\n  self.setDate = setDate;\n  self.toggle = toggle;\n  function setupHelperFunctions() {\n    self.utils = {\n      getDaysInMonth(month = self.currentMonth, yr = self.currentYear) {\n        if (month === 1 && (yr % 4 === 0 && yr % 100 !== 0 || yr % 400 === 0)) return 29;\n        return self.l10n.daysInMonth[month];\n      }\n    };\n  }\n  function init() {\n    self.element = self.input = element;\n    self.isOpen = false;\n    parseConfig();\n    setupLocale();\n    setupInputs();\n    setupDates();\n    setupHelperFunctions();\n    if (!self.isMobile) build();\n    bindEvents();\n    if (self.selectedDates.length || self.config.noCalendar) {\n      if (self.config.enableTime) {\n        setHoursFromDate(self.config.noCalendar ? self.latestSelectedDateObj : undefined);\n      }\n      updateValue(false);\n    }\n    setCalendarWidth();\n    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n    if (!self.isMobile && isSafari) {\n      positionCalendar();\n    }\n    triggerEvent(\"onReady\");\n  }\n  function bindToInstance(fn) {\n    return fn.bind(self);\n  }\n  function setCalendarWidth() {\n    const config = self.config;\n    if (config.weekNumbers === false && config.showMonths === 1) {\n      return;\n    } else if (config.noCalendar !== true) {\n      window.requestAnimationFrame(function () {\n        if (self.calendarContainer !== undefined) {\n          self.calendarContainer.style.visibility = \"hidden\";\n          self.calendarContainer.style.display = \"block\";\n        }\n        if (self.daysContainer !== undefined) {\n          const daysWidth = (self.days.offsetWidth + 1) * config.showMonths;\n          self.daysContainer.style.width = daysWidth + \"px\";\n          self.calendarContainer.style.width = daysWidth + (self.weekWrapper !== undefined ? self.weekWrapper.offsetWidth : 0) + \"px\";\n          self.calendarContainer.style.removeProperty(\"visibility\");\n          self.calendarContainer.style.removeProperty(\"display\");\n        }\n      });\n    }\n  }\n  function updateTime(e) {\n    if (self.selectedDates.length === 0) {\n      const defaultDate = self.config.minDate === undefined || compareDates(new Date(), self.config.minDate) >= 0 ? new Date() : new Date(self.config.minDate.getTime());\n      const defaults = getDefaultHours(self.config);\n      defaultDate.setHours(defaults.hours, defaults.minutes, defaults.seconds, defaultDate.getMilliseconds());\n      self.selectedDates = [defaultDate];\n      self.latestSelectedDateObj = defaultDate;\n    }\n    if (e !== undefined && e.type !== \"blur\") {\n      timeWrapper(e);\n    }\n    const prevValue = self._input.value;\n    setHoursFromInputs();\n    updateValue();\n    if (self._input.value !== prevValue) {\n      self._debouncedChange();\n    }\n  }\n  function ampm2military(hour, amPM) {\n    return hour % 12 + 12 * int(amPM === self.l10n.amPM[1]);\n  }\n  function military2ampm(hour) {\n    switch (hour % 24) {\n      case 0:\n      case 12:\n        return 12;\n      default:\n        return hour % 12;\n    }\n  }\n  function setHoursFromInputs() {\n    if (self.hourElement === undefined || self.minuteElement === undefined) return;\n    let hours = (parseInt(self.hourElement.value.slice(-2), 10) || 0) % 24,\n      minutes = (parseInt(self.minuteElement.value, 10) || 0) % 60,\n      seconds = self.secondElement !== undefined ? (parseInt(self.secondElement.value, 10) || 0) % 60 : 0;\n    if (self.amPM !== undefined) {\n      hours = ampm2military(hours, self.amPM.textContent);\n    }\n    const limitMinHours = self.config.minTime !== undefined || self.config.minDate && self.minDateHasTime && self.latestSelectedDateObj && compareDates(self.latestSelectedDateObj, self.config.minDate, true) === 0;\n    const limitMaxHours = self.config.maxTime !== undefined || self.config.maxDate && self.maxDateHasTime && self.latestSelectedDateObj && compareDates(self.latestSelectedDateObj, self.config.maxDate, true) === 0;\n    if (limitMaxHours) {\n      const maxTime = self.config.maxTime !== undefined ? self.config.maxTime : self.config.maxDate;\n      hours = Math.min(hours, maxTime.getHours());\n      if (hours === maxTime.getHours()) minutes = Math.min(minutes, maxTime.getMinutes());\n      if (minutes === maxTime.getMinutes()) seconds = Math.min(seconds, maxTime.getSeconds());\n    }\n    if (limitMinHours) {\n      const minTime = self.config.minTime !== undefined ? self.config.minTime : self.config.minDate;\n      hours = Math.max(hours, minTime.getHours());\n      if (hours === minTime.getHours() && minutes < minTime.getMinutes()) minutes = minTime.getMinutes();\n      if (minutes === minTime.getMinutes()) seconds = Math.max(seconds, minTime.getSeconds());\n    }\n    setHours(hours, minutes, seconds);\n  }\n  function setHoursFromDate(dateObj) {\n    const date = dateObj || self.latestSelectedDateObj;\n    if (date) {\n      setHours(date.getHours(), date.getMinutes(), date.getSeconds());\n    }\n  }\n  function setHours(hours, minutes, seconds) {\n    if (self.latestSelectedDateObj !== undefined) {\n      self.latestSelectedDateObj.setHours(hours % 24, minutes, seconds || 0, 0);\n    }\n    if (!self.hourElement || !self.minuteElement || self.isMobile) return;\n    self.hourElement.value = pad(!self.config.time_24hr ? (12 + hours) % 12 + 12 * int(hours % 12 === 0) : hours);\n    self.minuteElement.value = pad(minutes);\n    if (self.amPM !== undefined) self.amPM.textContent = self.l10n.amPM[int(hours >= 12)];\n    if (self.secondElement !== undefined) self.secondElement.value = pad(seconds);\n  }\n  function onYearInput(event) {\n    const eventTarget = getEventTarget(event);\n    const year = parseInt(eventTarget.value) + (event.delta || 0);\n    if (year / 1000 > 1 || event.key === \"Enter\" && !/[^\\d]/.test(year.toString())) {\n      changeYear(year);\n    }\n  }\n  function bind(element, event, handler, options) {\n    if (event instanceof Array) return event.forEach(ev => bind(element, ev, handler, options));\n    if (element instanceof Array) return element.forEach(el => bind(el, event, handler, options));\n    element.addEventListener(event, handler, options);\n    self._handlers.push({\n      remove: () => element.removeEventListener(event, handler)\n    });\n  }\n  function triggerChange() {\n    triggerEvent(\"onChange\");\n  }\n  function bindEvents() {\n    if (self.config.wrap) {\n      [\"open\", \"close\", \"toggle\", \"clear\"].forEach(evt => {\n        Array.prototype.forEach.call(self.element.querySelectorAll(`[data-${evt}]`), el => bind(el, \"click\", self[evt]));\n      });\n    }\n    if (self.isMobile) {\n      setupMobile();\n      return;\n    }\n    const debouncedResize = debounce(onResize, 50);\n    self._debouncedChange = debounce(triggerChange, DEBOUNCED_CHANGE_MS);\n    if (self.daysContainer && !/iPhone|iPad|iPod/i.test(navigator.userAgent)) bind(self.daysContainer, \"mouseover\", e => {\n      if (self.config.mode === \"range\") onMouseOver(getEventTarget(e));\n    });\n    bind(window.document.body, \"keydown\", onKeyDown);\n    if (!self.config.inline && !self.config.static) bind(window, \"resize\", debouncedResize);\n    if (window.ontouchstart !== undefined) bind(window.document, \"touchstart\", documentClick);else bind(window.document, \"mousedown\", documentClick);\n    bind(window.document, \"focus\", documentClick, {\n      capture: true\n    });\n    if (self.config.clickOpens === true) {\n      bind(self._input, \"focus\", self.open);\n      bind(self._input, \"click\", self.open);\n    }\n    if (self.daysContainer !== undefined) {\n      bind(self.monthNav, \"click\", onMonthNavClick);\n      bind(self.monthNav, [\"keyup\", \"increment\"], onYearInput);\n      bind(self.daysContainer, \"click\", selectDate);\n    }\n    if (self.timeContainer !== undefined && self.minuteElement !== undefined && self.hourElement !== undefined) {\n      const selText = e => getEventTarget(e).select();\n      bind(self.timeContainer, [\"increment\"], updateTime);\n      bind(self.timeContainer, \"blur\", updateTime, {\n        capture: true\n      });\n      bind(self.timeContainer, \"click\", timeIncrement);\n      bind([self.hourElement, self.minuteElement], [\"focus\", \"click\"], selText);\n      if (self.secondElement !== undefined) bind(self.secondElement, \"focus\", () => self.secondElement && self.secondElement.select());\n      if (self.amPM !== undefined) {\n        bind(self.amPM, \"click\", e => {\n          updateTime(e);\n          triggerChange();\n        });\n      }\n    }\n    if (self.config.allowInput) {\n      bind(self._input, \"blur\", onBlur);\n    }\n  }\n  function jumpToDate(jumpDate, triggerChange) {\n    const jumpTo = jumpDate !== undefined ? self.parseDate(jumpDate) : self.latestSelectedDateObj || (self.config.minDate && self.config.minDate > self.now ? self.config.minDate : self.config.maxDate && self.config.maxDate < self.now ? self.config.maxDate : self.now);\n    const oldYear = self.currentYear;\n    const oldMonth = self.currentMonth;\n    try {\n      if (jumpTo !== undefined) {\n        self.currentYear = jumpTo.getFullYear();\n        self.currentMonth = jumpTo.getMonth();\n      }\n    } catch (e) {\n      e.message = \"Invalid date supplied: \" + jumpTo;\n      self.config.errorHandler(e);\n    }\n    if (triggerChange && self.currentYear !== oldYear) {\n      triggerEvent(\"onYearChange\");\n      buildMonthSwitch();\n    }\n    if (triggerChange && (self.currentYear !== oldYear || self.currentMonth !== oldMonth)) {\n      triggerEvent(\"onMonthChange\");\n    }\n    self.redraw();\n  }\n  function timeIncrement(e) {\n    const eventTarget = getEventTarget(e);\n    if (~eventTarget.className.indexOf(\"arrow\")) incrementNumInput(e, eventTarget.classList.contains(\"arrowUp\") ? 1 : -1);\n  }\n  function incrementNumInput(e, delta, inputElem) {\n    const target = e && getEventTarget(e);\n    const input = inputElem || target && target.parentNode && target.parentNode.firstChild;\n    const event = createEvent(\"increment\");\n    event.delta = delta;\n    input && input.dispatchEvent(event);\n  }\n  function build() {\n    const fragment = window.document.createDocumentFragment();\n    self.calendarContainer = createElement(\"div\", \"flatpickr-calendar\");\n    self.calendarContainer.tabIndex = -1;\n    if (!self.config.noCalendar) {\n      fragment.appendChild(buildMonthNav());\n      self.innerContainer = createElement(\"div\", \"flatpickr-innerContainer\");\n      if (self.config.weekNumbers) {\n        const {\n          weekWrapper,\n          weekNumbers\n        } = buildWeeks();\n        self.innerContainer.appendChild(weekWrapper);\n        self.weekNumbers = weekNumbers;\n        self.weekWrapper = weekWrapper;\n      }\n      self.rContainer = createElement(\"div\", \"flatpickr-rContainer\");\n      self.rContainer.appendChild(buildWeekdays());\n      if (!self.daysContainer) {\n        self.daysContainer = createElement(\"div\", \"flatpickr-days\");\n        self.daysContainer.tabIndex = -1;\n      }\n      buildDays();\n      self.rContainer.appendChild(self.daysContainer);\n      self.innerContainer.appendChild(self.rContainer);\n      fragment.appendChild(self.innerContainer);\n    }\n    if (self.config.enableTime) {\n      fragment.appendChild(buildTime());\n    }\n    toggleClass(self.calendarContainer, \"rangeMode\", self.config.mode === \"range\");\n    toggleClass(self.calendarContainer, \"animate\", self.config.animate === true);\n    toggleClass(self.calendarContainer, \"multiMonth\", self.config.showMonths > 1);\n    self.calendarContainer.appendChild(fragment);\n    const customAppend = self.config.appendTo !== undefined && self.config.appendTo.nodeType !== undefined;\n    if (self.config.inline || self.config.static) {\n      self.calendarContainer.classList.add(self.config.inline ? \"inline\" : \"static\");\n      if (self.config.inline) {\n        if (!customAppend && self.element.parentNode) self.element.parentNode.insertBefore(self.calendarContainer, self._input.nextSibling);else if (self.config.appendTo !== undefined) self.config.appendTo.appendChild(self.calendarContainer);\n      }\n      if (self.config.static) {\n        const wrapper = createElement(\"div\", \"flatpickr-wrapper\");\n        if (self.element.parentNode) self.element.parentNode.insertBefore(wrapper, self.element);\n        wrapper.appendChild(self.element);\n        if (self.altInput) wrapper.appendChild(self.altInput);\n        wrapper.appendChild(self.calendarContainer);\n      }\n    }\n    if (!self.config.static && !self.config.inline) (self.config.appendTo !== undefined ? self.config.appendTo : window.document.body).appendChild(self.calendarContainer);\n  }\n  function createDay(className, date, dayNumber, i) {\n    const dateIsEnabled = isEnabled(date, true),\n      dayElement = createElement(\"span\", \"flatpickr-day \" + className, date.getDate().toString());\n    dayElement.dateObj = date;\n    dayElement.$i = i;\n    dayElement.setAttribute(\"aria-label\", self.formatDate(date, self.config.ariaDateFormat));\n    if (className.indexOf(\"hidden\") === -1 && compareDates(date, self.now) === 0) {\n      self.todayDateElem = dayElement;\n      dayElement.classList.add(\"today\");\n      dayElement.setAttribute(\"aria-current\", \"date\");\n    }\n    if (dateIsEnabled) {\n      dayElement.tabIndex = -1;\n      if (isDateSelected(date)) {\n        dayElement.classList.add(\"selected\");\n        self.selectedDateElem = dayElement;\n        if (self.config.mode === \"range\") {\n          toggleClass(dayElement, \"startRange\", self.selectedDates[0] && compareDates(date, self.selectedDates[0], true) === 0);\n          toggleClass(dayElement, \"endRange\", self.selectedDates[1] && compareDates(date, self.selectedDates[1], true) === 0);\n          if (className === \"nextMonthDay\") dayElement.classList.add(\"inRange\");\n        }\n      }\n    } else {\n      dayElement.classList.add(\"flatpickr-disabled\");\n    }\n    if (self.config.mode === \"range\") {\n      if (isDateInRange(date) && !isDateSelected(date)) dayElement.classList.add(\"inRange\");\n    }\n    if (self.weekNumbers && self.config.showMonths === 1 && className !== \"prevMonthDay\" && dayNumber % 7 === 1) {\n      self.weekNumbers.insertAdjacentHTML(\"beforeend\", \"<span class='flatpickr-day'>\" + self.config.getWeek(date) + \"</span>\");\n    }\n    triggerEvent(\"onDayCreate\", dayElement);\n    return dayElement;\n  }\n  function focusOnDayElem(targetNode) {\n    targetNode.focus();\n    if (self.config.mode === \"range\") onMouseOver(targetNode);\n  }\n  function getFirstAvailableDay(delta) {\n    const startMonth = delta > 0 ? 0 : self.config.showMonths - 1;\n    const endMonth = delta > 0 ? self.config.showMonths : -1;\n    for (let m = startMonth; m != endMonth; m += delta) {\n      const month = self.daysContainer.children[m];\n      const startIndex = delta > 0 ? 0 : month.children.length - 1;\n      const endIndex = delta > 0 ? month.children.length : -1;\n      for (let i = startIndex; i != endIndex; i += delta) {\n        const c = month.children[i];\n        if (c.className.indexOf(\"hidden\") === -1 && isEnabled(c.dateObj)) return c;\n      }\n    }\n    return undefined;\n  }\n  function getNextAvailableDay(current, delta) {\n    const givenMonth = current.className.indexOf(\"Month\") === -1 ? current.dateObj.getMonth() : self.currentMonth;\n    const endMonth = delta > 0 ? self.config.showMonths : -1;\n    const loopDelta = delta > 0 ? 1 : -1;\n    for (let m = givenMonth - self.currentMonth; m != endMonth; m += loopDelta) {\n      const month = self.daysContainer.children[m];\n      const startIndex = givenMonth - self.currentMonth === m ? current.$i + delta : delta < 0 ? month.children.length - 1 : 0;\n      const numMonthDays = month.children.length;\n      for (let i = startIndex; i >= 0 && i < numMonthDays && i != (delta > 0 ? numMonthDays : -1); i += loopDelta) {\n        const c = month.children[i];\n        if (c.className.indexOf(\"hidden\") === -1 && isEnabled(c.dateObj) && Math.abs(current.$i - i) >= Math.abs(delta)) return focusOnDayElem(c);\n      }\n    }\n    self.changeMonth(loopDelta);\n    focusOnDay(getFirstAvailableDay(loopDelta), 0);\n    return undefined;\n  }\n  function focusOnDay(current, offset) {\n    const dayFocused = isInView(document.activeElement || document.body);\n    const startElem = current !== undefined ? current : dayFocused ? document.activeElement : self.selectedDateElem !== undefined && isInView(self.selectedDateElem) ? self.selectedDateElem : self.todayDateElem !== undefined && isInView(self.todayDateElem) ? self.todayDateElem : getFirstAvailableDay(offset > 0 ? 1 : -1);\n    if (startElem === undefined) {\n      self._input.focus();\n    } else if (!dayFocused) {\n      focusOnDayElem(startElem);\n    } else {\n      getNextAvailableDay(startElem, offset);\n    }\n  }\n  function buildMonthDays(year, month) {\n    const firstOfMonth = (new Date(year, month, 1).getDay() - self.l10n.firstDayOfWeek + 7) % 7;\n    const prevMonthDays = self.utils.getDaysInMonth((month - 1 + 12) % 12, year);\n    const daysInMonth = self.utils.getDaysInMonth(month, year),\n      days = window.document.createDocumentFragment(),\n      isMultiMonth = self.config.showMonths > 1,\n      prevMonthDayClass = isMultiMonth ? \"prevMonthDay hidden\" : \"prevMonthDay\",\n      nextMonthDayClass = isMultiMonth ? \"nextMonthDay hidden\" : \"nextMonthDay\";\n    let dayNumber = prevMonthDays + 1 - firstOfMonth,\n      dayIndex = 0;\n    for (; dayNumber <= prevMonthDays; dayNumber++, dayIndex++) {\n      days.appendChild(createDay(prevMonthDayClass, new Date(year, month - 1, dayNumber), dayNumber, dayIndex));\n    }\n    for (dayNumber = 1; dayNumber <= daysInMonth; dayNumber++, dayIndex++) {\n      days.appendChild(createDay(\"\", new Date(year, month, dayNumber), dayNumber, dayIndex));\n    }\n    for (let dayNum = daysInMonth + 1; dayNum <= 42 - firstOfMonth && (self.config.showMonths === 1 || dayIndex % 7 !== 0); dayNum++, dayIndex++) {\n      days.appendChild(createDay(nextMonthDayClass, new Date(year, month + 1, dayNum % daysInMonth), dayNum, dayIndex));\n    }\n    const dayContainer = createElement(\"div\", \"dayContainer\");\n    dayContainer.appendChild(days);\n    return dayContainer;\n  }\n  function buildDays() {\n    if (self.daysContainer === undefined) {\n      return;\n    }\n    clearNode(self.daysContainer);\n    if (self.weekNumbers) clearNode(self.weekNumbers);\n    const frag = document.createDocumentFragment();\n    for (let i = 0; i < self.config.showMonths; i++) {\n      const d = new Date(self.currentYear, self.currentMonth, 1);\n      d.setMonth(self.currentMonth + i);\n      frag.appendChild(buildMonthDays(d.getFullYear(), d.getMonth()));\n    }\n    self.daysContainer.appendChild(frag);\n    self.days = self.daysContainer.firstChild;\n    if (self.config.mode === \"range\" && self.selectedDates.length === 1) {\n      onMouseOver();\n    }\n  }\n  function buildMonthSwitch() {\n    if (self.config.showMonths > 1 || self.config.monthSelectorType !== \"dropdown\") return;\n    const shouldBuildMonth = function (month) {\n      if (self.config.minDate !== undefined && self.currentYear === self.config.minDate.getFullYear() && month < self.config.minDate.getMonth()) {\n        return false;\n      }\n      return !(self.config.maxDate !== undefined && self.currentYear === self.config.maxDate.getFullYear() && month > self.config.maxDate.getMonth());\n    };\n    self.monthsDropdownContainer.tabIndex = -1;\n    self.monthsDropdownContainer.innerHTML = \"\";\n    for (let i = 0; i < 12; i++) {\n      if (!shouldBuildMonth(i)) continue;\n      const month = createElement(\"option\", \"flatpickr-monthDropdown-month\");\n      month.value = new Date(self.currentYear, i).getMonth().toString();\n      month.textContent = monthToStr(i, self.config.shorthandCurrentMonth, self.l10n);\n      month.tabIndex = -1;\n      if (self.currentMonth === i) {\n        month.selected = true;\n      }\n      self.monthsDropdownContainer.appendChild(month);\n    }\n  }\n  function buildMonth() {\n    const container = createElement(\"div\", \"flatpickr-month\");\n    const monthNavFragment = window.document.createDocumentFragment();\n    let monthElement;\n    if (self.config.showMonths > 1 || self.config.monthSelectorType === \"static\") {\n      monthElement = createElement(\"span\", \"cur-month\");\n    } else {\n      self.monthsDropdownContainer = createElement(\"select\", \"flatpickr-monthDropdown-months\");\n      self.monthsDropdownContainer.setAttribute(\"aria-label\", self.l10n.monthAriaLabel);\n      bind(self.monthsDropdownContainer, \"change\", e => {\n        const target = getEventTarget(e);\n        const selectedMonth = parseInt(target.value, 10);\n        self.changeMonth(selectedMonth - self.currentMonth);\n        triggerEvent(\"onMonthChange\");\n      });\n      buildMonthSwitch();\n      monthElement = self.monthsDropdownContainer;\n    }\n    const yearInput = createNumberInput(\"cur-year\", {\n      tabindex: \"-1\"\n    });\n    const yearElement = yearInput.getElementsByTagName(\"input\")[0];\n    yearElement.setAttribute(\"aria-label\", self.l10n.yearAriaLabel);\n    if (self.config.minDate) {\n      yearElement.setAttribute(\"min\", self.config.minDate.getFullYear().toString());\n    }\n    if (self.config.maxDate) {\n      yearElement.setAttribute(\"max\", self.config.maxDate.getFullYear().toString());\n      yearElement.disabled = !!self.config.minDate && self.config.minDate.getFullYear() === self.config.maxDate.getFullYear();\n    }\n    const currentMonth = createElement(\"div\", \"flatpickr-current-month\");\n    currentMonth.appendChild(monthElement);\n    currentMonth.appendChild(yearInput);\n    monthNavFragment.appendChild(currentMonth);\n    container.appendChild(monthNavFragment);\n    return {\n      container,\n      yearElement,\n      monthElement\n    };\n  }\n  function buildMonths() {\n    clearNode(self.monthNav);\n    self.monthNav.appendChild(self.prevMonthNav);\n    if (self.config.showMonths) {\n      self.yearElements = [];\n      self.monthElements = [];\n    }\n    for (let m = self.config.showMonths; m--;) {\n      const month = buildMonth();\n      self.yearElements.push(month.yearElement);\n      self.monthElements.push(month.monthElement);\n      self.monthNav.appendChild(month.container);\n    }\n    self.monthNav.appendChild(self.nextMonthNav);\n  }\n  function buildMonthNav() {\n    self.monthNav = createElement(\"div\", \"flatpickr-months\");\n    self.yearElements = [];\n    self.monthElements = [];\n    self.prevMonthNav = createElement(\"span\", \"flatpickr-prev-month\");\n    self.prevMonthNav.innerHTML = self.config.prevArrow;\n    self.nextMonthNav = createElement(\"span\", \"flatpickr-next-month\");\n    self.nextMonthNav.innerHTML = self.config.nextArrow;\n    buildMonths();\n    Object.defineProperty(self, \"_hidePrevMonthArrow\", {\n      get: () => self.__hidePrevMonthArrow,\n      set(bool) {\n        if (self.__hidePrevMonthArrow !== bool) {\n          toggleClass(self.prevMonthNav, \"flatpickr-disabled\", bool);\n          self.__hidePrevMonthArrow = bool;\n        }\n      }\n    });\n    Object.defineProperty(self, \"_hideNextMonthArrow\", {\n      get: () => self.__hideNextMonthArrow,\n      set(bool) {\n        if (self.__hideNextMonthArrow !== bool) {\n          toggleClass(self.nextMonthNav, \"flatpickr-disabled\", bool);\n          self.__hideNextMonthArrow = bool;\n        }\n      }\n    });\n    self.currentYearElement = self.yearElements[0];\n    updateNavigationCurrentMonth();\n    return self.monthNav;\n  }\n  function buildTime() {\n    self.calendarContainer.classList.add(\"hasTime\");\n    if (self.config.noCalendar) self.calendarContainer.classList.add(\"noCalendar\");\n    const defaults = getDefaultHours(self.config);\n    self.timeContainer = createElement(\"div\", \"flatpickr-time\");\n    self.timeContainer.tabIndex = -1;\n    const separator = createElement(\"span\", \"flatpickr-time-separator\", \":\");\n    const hourInput = createNumberInput(\"flatpickr-hour\", {\n      \"aria-label\": self.l10n.hourAriaLabel\n    });\n    self.hourElement = hourInput.getElementsByTagName(\"input\")[0];\n    const minuteInput = createNumberInput(\"flatpickr-minute\", {\n      \"aria-label\": self.l10n.minuteAriaLabel\n    });\n    self.minuteElement = minuteInput.getElementsByTagName(\"input\")[0];\n    self.hourElement.tabIndex = self.minuteElement.tabIndex = -1;\n    self.hourElement.value = pad(self.latestSelectedDateObj ? self.latestSelectedDateObj.getHours() : self.config.time_24hr ? defaults.hours : military2ampm(defaults.hours));\n    self.minuteElement.value = pad(self.latestSelectedDateObj ? self.latestSelectedDateObj.getMinutes() : defaults.minutes);\n    self.hourElement.setAttribute(\"step\", self.config.hourIncrement.toString());\n    self.minuteElement.setAttribute(\"step\", self.config.minuteIncrement.toString());\n    self.hourElement.setAttribute(\"min\", self.config.time_24hr ? \"0\" : \"1\");\n    self.hourElement.setAttribute(\"max\", self.config.time_24hr ? \"23\" : \"12\");\n    self.hourElement.setAttribute(\"maxlength\", \"2\");\n    self.minuteElement.setAttribute(\"min\", \"0\");\n    self.minuteElement.setAttribute(\"max\", \"59\");\n    self.minuteElement.setAttribute(\"maxlength\", \"2\");\n    self.timeContainer.appendChild(hourInput);\n    self.timeContainer.appendChild(separator);\n    self.timeContainer.appendChild(minuteInput);\n    if (self.config.time_24hr) self.timeContainer.classList.add(\"time24hr\");\n    if (self.config.enableSeconds) {\n      self.timeContainer.classList.add(\"hasSeconds\");\n      const secondInput = createNumberInput(\"flatpickr-second\");\n      self.secondElement = secondInput.getElementsByTagName(\"input\")[0];\n      self.secondElement.value = pad(self.latestSelectedDateObj ? self.latestSelectedDateObj.getSeconds() : defaults.seconds);\n      self.secondElement.setAttribute(\"step\", self.minuteElement.getAttribute(\"step\"));\n      self.secondElement.setAttribute(\"min\", \"0\");\n      self.secondElement.setAttribute(\"max\", \"59\");\n      self.secondElement.setAttribute(\"maxlength\", \"2\");\n      self.timeContainer.appendChild(createElement(\"span\", \"flatpickr-time-separator\", \":\"));\n      self.timeContainer.appendChild(secondInput);\n    }\n    if (!self.config.time_24hr) {\n      self.amPM = createElement(\"span\", \"flatpickr-am-pm\", self.l10n.amPM[int((self.latestSelectedDateObj ? self.hourElement.value : self.config.defaultHour) > 11)]);\n      self.amPM.title = self.l10n.toggleTitle;\n      self.amPM.tabIndex = -1;\n      self.timeContainer.appendChild(self.amPM);\n    }\n    return self.timeContainer;\n  }\n  function buildWeekdays() {\n    if (!self.weekdayContainer) self.weekdayContainer = createElement(\"div\", \"flatpickr-weekdays\");else clearNode(self.weekdayContainer);\n    for (let i = self.config.showMonths; i--;) {\n      const container = createElement(\"div\", \"flatpickr-weekdaycontainer\");\n      self.weekdayContainer.appendChild(container);\n    }\n    updateWeekdays();\n    return self.weekdayContainer;\n  }\n  function updateWeekdays() {\n    if (!self.weekdayContainer) {\n      return;\n    }\n    const firstDayOfWeek = self.l10n.firstDayOfWeek;\n    let weekdays = [...self.l10n.weekdays.shorthand];\n    if (firstDayOfWeek > 0 && firstDayOfWeek < weekdays.length) {\n      weekdays = [...weekdays.splice(firstDayOfWeek, weekdays.length), ...weekdays.splice(0, firstDayOfWeek)];\n    }\n    for (let i = self.config.showMonths; i--;) {\n      self.weekdayContainer.children[i].innerHTML = `\n      <span class='flatpickr-weekday'>\n        ${weekdays.join(\"</span><span class='flatpickr-weekday'>\")}\n      </span>\n      `;\n    }\n  }\n  function buildWeeks() {\n    self.calendarContainer.classList.add(\"hasWeeks\");\n    const weekWrapper = createElement(\"div\", \"flatpickr-weekwrapper\");\n    weekWrapper.appendChild(createElement(\"span\", \"flatpickr-weekday\", self.l10n.weekAbbreviation));\n    const weekNumbers = createElement(\"div\", \"flatpickr-weeks\");\n    weekWrapper.appendChild(weekNumbers);\n    return {\n      weekWrapper,\n      weekNumbers\n    };\n  }\n  function changeMonth(value, isOffset = true) {\n    const delta = isOffset ? value : value - self.currentMonth;\n    if (delta < 0 && self._hidePrevMonthArrow === true || delta > 0 && self._hideNextMonthArrow === true) return;\n    self.currentMonth += delta;\n    if (self.currentMonth < 0 || self.currentMonth > 11) {\n      self.currentYear += self.currentMonth > 11 ? 1 : -1;\n      self.currentMonth = (self.currentMonth + 12) % 12;\n      triggerEvent(\"onYearChange\");\n      buildMonthSwitch();\n    }\n    buildDays();\n    triggerEvent(\"onMonthChange\");\n    updateNavigationCurrentMonth();\n  }\n  function clear(triggerChangeEvent = true, toInitial = true) {\n    self.input.value = \"\";\n    if (self.altInput !== undefined) self.altInput.value = \"\";\n    if (self.mobileInput !== undefined) self.mobileInput.value = \"\";\n    self.selectedDates = [];\n    self.latestSelectedDateObj = undefined;\n    if (toInitial === true) {\n      self.currentYear = self._initialDate.getFullYear();\n      self.currentMonth = self._initialDate.getMonth();\n    }\n    if (self.config.enableTime === true) {\n      const {\n        hours,\n        minutes,\n        seconds\n      } = getDefaultHours(self.config);\n      setHours(hours, minutes, seconds);\n    }\n    self.redraw();\n    if (triggerChangeEvent) triggerEvent(\"onChange\");\n  }\n  function close() {\n    self.isOpen = false;\n    if (!self.isMobile) {\n      if (self.calendarContainer !== undefined) {\n        self.calendarContainer.classList.remove(\"open\");\n      }\n      if (self._input !== undefined) {\n        self._input.classList.remove(\"active\");\n      }\n    }\n    triggerEvent(\"onClose\");\n  }\n  function destroy() {\n    if (self.config !== undefined) triggerEvent(\"onDestroy\");\n    for (let i = self._handlers.length; i--;) {\n      self._handlers[i].remove();\n    }\n    self._handlers = [];\n    if (self.mobileInput) {\n      if (self.mobileInput.parentNode) self.mobileInput.parentNode.removeChild(self.mobileInput);\n      self.mobileInput = undefined;\n    } else if (self.calendarContainer && self.calendarContainer.parentNode) {\n      if (self.config.static && self.calendarContainer.parentNode) {\n        const wrapper = self.calendarContainer.parentNode;\n        wrapper.lastChild && wrapper.removeChild(wrapper.lastChild);\n        if (wrapper.parentNode) {\n          while (wrapper.firstChild) wrapper.parentNode.insertBefore(wrapper.firstChild, wrapper);\n          wrapper.parentNode.removeChild(wrapper);\n        }\n      } else self.calendarContainer.parentNode.removeChild(self.calendarContainer);\n    }\n    if (self.altInput) {\n      self.input.type = \"text\";\n      if (self.altInput.parentNode) self.altInput.parentNode.removeChild(self.altInput);\n      delete self.altInput;\n    }\n    if (self.input) {\n      self.input.type = self.input._type;\n      self.input.classList.remove(\"flatpickr-input\");\n      self.input.removeAttribute(\"readonly\");\n    }\n    [\"_showTimeInput\", \"latestSelectedDateObj\", \"_hideNextMonthArrow\", \"_hidePrevMonthArrow\", \"__hideNextMonthArrow\", \"__hidePrevMonthArrow\", \"isMobile\", \"isOpen\", \"selectedDateElem\", \"minDateHasTime\", \"maxDateHasTime\", \"days\", \"daysContainer\", \"_input\", \"_positionElement\", \"innerContainer\", \"rContainer\", \"monthNav\", \"todayDateElem\", \"calendarContainer\", \"weekdayContainer\", \"prevMonthNav\", \"nextMonthNav\", \"monthsDropdownContainer\", \"currentMonthElement\", \"currentYearElement\", \"navigationCurrentMonth\", \"selectedDateElem\", \"config\"].forEach(k => {\n      try {\n        delete self[k];\n      } catch (_) {}\n    });\n  }\n  function isCalendarElem(elem) {\n    if (self.config.appendTo && self.config.appendTo.contains(elem)) return true;\n    return self.calendarContainer.contains(elem);\n  }\n  function documentClick(e) {\n    if (self.isOpen && !self.config.inline) {\n      const eventTarget = getEventTarget(e);\n      const isCalendarElement = isCalendarElem(eventTarget);\n      const isInput = eventTarget === self.input || eventTarget === self.altInput || self.element.contains(eventTarget) || e.path && e.path.indexOf && (~e.path.indexOf(self.input) || ~e.path.indexOf(self.altInput));\n      const lostFocus = e.type === \"blur\" ? isInput && e.relatedTarget && !isCalendarElem(e.relatedTarget) : !isInput && !isCalendarElement && !isCalendarElem(e.relatedTarget);\n      const isIgnored = !self.config.ignoredFocusElements.some(elem => elem.contains(eventTarget));\n      if (lostFocus && isIgnored) {\n        if (self.timeContainer !== undefined && self.minuteElement !== undefined && self.hourElement !== undefined && self.input.value !== \"\" && self.input.value !== undefined) {\n          updateTime();\n        }\n        self.close();\n        if (self.config && self.config.mode === \"range\" && self.selectedDates.length === 1) {\n          self.clear(false);\n          self.redraw();\n        }\n      }\n    }\n  }\n  function changeYear(newYear) {\n    if (!newYear || self.config.minDate && newYear < self.config.minDate.getFullYear() || self.config.maxDate && newYear > self.config.maxDate.getFullYear()) return;\n    const newYearNum = newYear,\n      isNewYear = self.currentYear !== newYearNum;\n    self.currentYear = newYearNum || self.currentYear;\n    if (self.config.maxDate && self.currentYear === self.config.maxDate.getFullYear()) {\n      self.currentMonth = Math.min(self.config.maxDate.getMonth(), self.currentMonth);\n    } else if (self.config.minDate && self.currentYear === self.config.minDate.getFullYear()) {\n      self.currentMonth = Math.max(self.config.minDate.getMonth(), self.currentMonth);\n    }\n    if (isNewYear) {\n      self.redraw();\n      triggerEvent(\"onYearChange\");\n      buildMonthSwitch();\n    }\n  }\n  function isEnabled(date, timeless = true) {\n    var _a;\n    const dateToCheck = self.parseDate(date, undefined, timeless);\n    if (self.config.minDate && dateToCheck && compareDates(dateToCheck, self.config.minDate, timeless !== undefined ? timeless : !self.minDateHasTime) < 0 || self.config.maxDate && dateToCheck && compareDates(dateToCheck, self.config.maxDate, timeless !== undefined ? timeless : !self.maxDateHasTime) > 0) return false;\n    if (!self.config.enable && self.config.disable.length === 0) return true;\n    if (dateToCheck === undefined) return false;\n    const bool = !!self.config.enable,\n      array = (_a = self.config.enable) !== null && _a !== void 0 ? _a : self.config.disable;\n    for (let i = 0, d; i < array.length; i++) {\n      d = array[i];\n      if (typeof d === \"function\" && d(dateToCheck)) return bool;else if (d instanceof Date && dateToCheck !== undefined && d.getTime() === dateToCheck.getTime()) return bool;else if (typeof d === \"string\") {\n        const parsed = self.parseDate(d, undefined, true);\n        return parsed && parsed.getTime() === dateToCheck.getTime() ? bool : !bool;\n      } else if (typeof d === \"object\" && dateToCheck !== undefined && d.from && d.to && dateToCheck.getTime() >= d.from.getTime() && dateToCheck.getTime() <= d.to.getTime()) return bool;\n    }\n    return !bool;\n  }\n  function isInView(elem) {\n    if (self.daysContainer !== undefined) return elem.className.indexOf(\"hidden\") === -1 && elem.className.indexOf(\"flatpickr-disabled\") === -1 && self.daysContainer.contains(elem);\n    return false;\n  }\n  function onBlur(e) {\n    const isInput = e.target === self._input;\n    if (isInput && (self.selectedDates.length > 0 || self._input.value.length > 0) && !(e.relatedTarget && isCalendarElem(e.relatedTarget))) {\n      self.setDate(self._input.value, true, e.target === self.altInput ? self.config.altFormat : self.config.dateFormat);\n    }\n  }\n  function onKeyDown(e) {\n    const eventTarget = getEventTarget(e);\n    const isInput = self.config.wrap ? element.contains(eventTarget) : eventTarget === self._input;\n    const allowInput = self.config.allowInput;\n    const allowKeydown = self.isOpen && (!allowInput || !isInput);\n    const allowInlineKeydown = self.config.inline && isInput && !allowInput;\n    if (e.keyCode === 13 && isInput) {\n      if (allowInput) {\n        self.setDate(self._input.value, true, eventTarget === self.altInput ? self.config.altFormat : self.config.dateFormat);\n        return eventTarget.blur();\n      } else {\n        self.open();\n      }\n    } else if (isCalendarElem(eventTarget) || allowKeydown || allowInlineKeydown) {\n      const isTimeObj = !!self.timeContainer && self.timeContainer.contains(eventTarget);\n      switch (e.keyCode) {\n        case 13:\n          if (isTimeObj) {\n            e.preventDefault();\n            updateTime();\n            focusAndClose();\n          } else selectDate(e);\n          break;\n        case 27:\n          e.preventDefault();\n          focusAndClose();\n          break;\n        case 8:\n        case 46:\n          if (isInput && !self.config.allowInput) {\n            e.preventDefault();\n            self.clear();\n          }\n          break;\n        case 37:\n        case 39:\n          if (!isTimeObj && !isInput) {\n            e.preventDefault();\n            if (self.daysContainer !== undefined && (allowInput === false || document.activeElement && isInView(document.activeElement))) {\n              const delta = e.keyCode === 39 ? 1 : -1;\n              if (!e.ctrlKey) focusOnDay(undefined, delta);else {\n                e.stopPropagation();\n                changeMonth(delta);\n                focusOnDay(getFirstAvailableDay(1), 0);\n              }\n            }\n          } else if (self.hourElement) self.hourElement.focus();\n          break;\n        case 38:\n        case 40:\n          e.preventDefault();\n          const delta = e.keyCode === 40 ? 1 : -1;\n          if (self.daysContainer && eventTarget.$i !== undefined || eventTarget === self.input || eventTarget === self.altInput) {\n            if (e.ctrlKey) {\n              e.stopPropagation();\n              changeYear(self.currentYear - delta);\n              focusOnDay(getFirstAvailableDay(1), 0);\n            } else if (!isTimeObj) focusOnDay(undefined, delta * 7);\n          } else if (eventTarget === self.currentYearElement) {\n            changeYear(self.currentYear - delta);\n          } else if (self.config.enableTime) {\n            if (!isTimeObj && self.hourElement) self.hourElement.focus();\n            updateTime(e);\n            self._debouncedChange();\n          }\n          break;\n        case 9:\n          if (isTimeObj) {\n            const elems = [self.hourElement, self.minuteElement, self.secondElement, self.amPM].concat(self.pluginElements).filter(x => x);\n            const i = elems.indexOf(eventTarget);\n            if (i !== -1) {\n              const target = elems[i + (e.shiftKey ? -1 : 1)];\n              e.preventDefault();\n              (target || self._input).focus();\n            }\n          } else if (!self.config.noCalendar && self.daysContainer && self.daysContainer.contains(eventTarget) && e.shiftKey) {\n            e.preventDefault();\n            self._input.focus();\n          }\n          break;\n        default:\n          break;\n      }\n    }\n    if (self.amPM !== undefined && eventTarget === self.amPM) {\n      switch (e.key) {\n        case self.l10n.amPM[0].charAt(0):\n        case self.l10n.amPM[0].charAt(0).toLowerCase():\n          self.amPM.textContent = self.l10n.amPM[0];\n          setHoursFromInputs();\n          updateValue();\n          break;\n        case self.l10n.amPM[1].charAt(0):\n        case self.l10n.amPM[1].charAt(0).toLowerCase():\n          self.amPM.textContent = self.l10n.amPM[1];\n          setHoursFromInputs();\n          updateValue();\n          break;\n      }\n    }\n    if (isInput || isCalendarElem(eventTarget)) {\n      triggerEvent(\"onKeyDown\", e);\n    }\n  }\n  function onMouseOver(elem) {\n    if (self.selectedDates.length !== 1 || elem && (!elem.classList.contains(\"flatpickr-day\") || elem.classList.contains(\"flatpickr-disabled\"))) return;\n    const hoverDate = elem ? elem.dateObj.getTime() : self.days.firstElementChild.dateObj.getTime(),\n      initialDate = self.parseDate(self.selectedDates[0], undefined, true).getTime(),\n      rangeStartDate = Math.min(hoverDate, self.selectedDates[0].getTime()),\n      rangeEndDate = Math.max(hoverDate, self.selectedDates[0].getTime());\n    let containsDisabled = false;\n    let minRange = 0,\n      maxRange = 0;\n    for (let t = rangeStartDate; t < rangeEndDate; t += duration.DAY) {\n      if (!isEnabled(new Date(t), true)) {\n        containsDisabled = containsDisabled || t > rangeStartDate && t < rangeEndDate;\n        if (t < initialDate && (!minRange || t > minRange)) minRange = t;else if (t > initialDate && (!maxRange || t < maxRange)) maxRange = t;\n      }\n    }\n    for (let m = 0; m < self.config.showMonths; m++) {\n      const month = self.daysContainer.children[m];\n      for (let i = 0, l = month.children.length; i < l; i++) {\n        const dayElem = month.children[i],\n          date = dayElem.dateObj;\n        const timestamp = date.getTime();\n        const outOfRange = minRange > 0 && timestamp < minRange || maxRange > 0 && timestamp > maxRange;\n        if (outOfRange) {\n          dayElem.classList.add(\"notAllowed\");\n          [\"inRange\", \"startRange\", \"endRange\"].forEach(c => {\n            dayElem.classList.remove(c);\n          });\n          continue;\n        } else if (containsDisabled && !outOfRange) continue;\n        [\"startRange\", \"inRange\", \"endRange\", \"notAllowed\"].forEach(c => {\n          dayElem.classList.remove(c);\n        });\n        if (elem !== undefined) {\n          elem.classList.add(hoverDate <= self.selectedDates[0].getTime() ? \"startRange\" : \"endRange\");\n          if (initialDate < hoverDate && timestamp === initialDate) dayElem.classList.add(\"startRange\");else if (initialDate > hoverDate && timestamp === initialDate) dayElem.classList.add(\"endRange\");\n          if (timestamp >= minRange && (maxRange === 0 || timestamp <= maxRange) && isBetween(timestamp, initialDate, hoverDate)) dayElem.classList.add(\"inRange\");\n        }\n      }\n    }\n  }\n  function onResize() {\n    if (self.isOpen && !self.config.static && !self.config.inline) positionCalendar();\n  }\n  function open(e, positionElement = self._positionElement) {\n    if (self.isMobile === true) {\n      if (e) {\n        e.preventDefault();\n        const eventTarget = getEventTarget(e);\n        if (eventTarget) {\n          eventTarget.blur();\n        }\n      }\n      if (self.mobileInput !== undefined) {\n        self.mobileInput.focus();\n        self.mobileInput.click();\n      }\n      triggerEvent(\"onOpen\");\n      return;\n    } else if (self._input.disabled || self.config.inline) {\n      return;\n    }\n    const wasOpen = self.isOpen;\n    self.isOpen = true;\n    if (!wasOpen) {\n      self.calendarContainer.classList.add(\"open\");\n      self._input.classList.add(\"active\");\n      triggerEvent(\"onOpen\");\n      positionCalendar(positionElement);\n    }\n    if (self.config.enableTime === true && self.config.noCalendar === true) {\n      if (self.config.allowInput === false && (e === undefined || !self.timeContainer.contains(e.relatedTarget))) {\n        setTimeout(() => self.hourElement.select(), 50);\n      }\n    }\n  }\n  function minMaxDateSetter(type) {\n    return date => {\n      const dateObj = self.config[`_${type}Date`] = self.parseDate(date, self.config.dateFormat);\n      const inverseDateObj = self.config[`_${type === \"min\" ? \"max\" : \"min\"}Date`];\n      if (dateObj !== undefined) {\n        self[type === \"min\" ? \"minDateHasTime\" : \"maxDateHasTime\"] = dateObj.getHours() > 0 || dateObj.getMinutes() > 0 || dateObj.getSeconds() > 0;\n      }\n      if (self.selectedDates) {\n        self.selectedDates = self.selectedDates.filter(d => isEnabled(d));\n        if (!self.selectedDates.length && type === \"min\") setHoursFromDate(dateObj);\n        updateValue();\n      }\n      if (self.daysContainer) {\n        redraw();\n        if (dateObj !== undefined) self.currentYearElement[type] = dateObj.getFullYear().toString();else self.currentYearElement.removeAttribute(type);\n        self.currentYearElement.disabled = !!inverseDateObj && dateObj !== undefined && inverseDateObj.getFullYear() === dateObj.getFullYear();\n      }\n    };\n  }\n  function parseConfig() {\n    const boolOpts = [\"wrap\", \"weekNumbers\", \"allowInput\", \"allowInvalidPreload\", \"clickOpens\", \"time_24hr\", \"enableTime\", \"noCalendar\", \"altInput\", \"shorthandCurrentMonth\", \"inline\", \"static\", \"enableSeconds\", \"disableMobile\"];\n    const userConfig = Object.assign(Object.assign({}, JSON.parse(JSON.stringify(element.dataset || {}))), instanceConfig);\n    const formats = {};\n    self.config.parseDate = userConfig.parseDate;\n    self.config.formatDate = userConfig.formatDate;\n    Object.defineProperty(self.config, \"enable\", {\n      get: () => self.config._enable,\n      set: dates => {\n        self.config._enable = parseDateRules(dates);\n      }\n    });\n    Object.defineProperty(self.config, \"disable\", {\n      get: () => self.config._disable,\n      set: dates => {\n        self.config._disable = parseDateRules(dates);\n      }\n    });\n    const timeMode = userConfig.mode === \"time\";\n    if (!userConfig.dateFormat && (userConfig.enableTime || timeMode)) {\n      const defaultDateFormat = flatpickr.defaultConfig.dateFormat || defaultOptions.dateFormat;\n      formats.dateFormat = userConfig.noCalendar || timeMode ? \"H:i\" + (userConfig.enableSeconds ? \":S\" : \"\") : defaultDateFormat + \" H:i\" + (userConfig.enableSeconds ? \":S\" : \"\");\n    }\n    if (userConfig.altInput && (userConfig.enableTime || timeMode) && !userConfig.altFormat) {\n      const defaultAltFormat = flatpickr.defaultConfig.altFormat || defaultOptions.altFormat;\n      formats.altFormat = userConfig.noCalendar || timeMode ? \"h:i\" + (userConfig.enableSeconds ? \":S K\" : \" K\") : defaultAltFormat + ` h:i${userConfig.enableSeconds ? \":S\" : \"\"} K`;\n    }\n    Object.defineProperty(self.config, \"minDate\", {\n      get: () => self.config._minDate,\n      set: minMaxDateSetter(\"min\")\n    });\n    Object.defineProperty(self.config, \"maxDate\", {\n      get: () => self.config._maxDate,\n      set: minMaxDateSetter(\"max\")\n    });\n    const minMaxTimeSetter = type => val => {\n      self.config[type === \"min\" ? \"_minTime\" : \"_maxTime\"] = self.parseDate(val, \"H:i:S\");\n    };\n    Object.defineProperty(self.config, \"minTime\", {\n      get: () => self.config._minTime,\n      set: minMaxTimeSetter(\"min\")\n    });\n    Object.defineProperty(self.config, \"maxTime\", {\n      get: () => self.config._maxTime,\n      set: minMaxTimeSetter(\"max\")\n    });\n    if (userConfig.mode === \"time\") {\n      self.config.noCalendar = true;\n      self.config.enableTime = true;\n    }\n    Object.assign(self.config, formats, userConfig);\n    for (let i = 0; i < boolOpts.length; i++) self.config[boolOpts[i]] = self.config[boolOpts[i]] === true || self.config[boolOpts[i]] === \"true\";\n    HOOKS.filter(hook => self.config[hook] !== undefined).forEach(hook => {\n      self.config[hook] = arrayify(self.config[hook] || []).map(bindToInstance);\n    });\n    self.isMobile = !self.config.disableMobile && !self.config.inline && self.config.mode === \"single\" && !self.config.disable.length && !self.config.enable && !self.config.weekNumbers && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n    for (let i = 0; i < self.config.plugins.length; i++) {\n      const pluginConf = self.config.plugins[i](self) || {};\n      for (const key in pluginConf) {\n        if (HOOKS.indexOf(key) > -1) {\n          self.config[key] = arrayify(pluginConf[key]).map(bindToInstance).concat(self.config[key]);\n        } else if (typeof userConfig[key] === \"undefined\") self.config[key] = pluginConf[key];\n      }\n    }\n    if (!userConfig.altInputClass) {\n      self.config.altInputClass = getInputElem().className + \" \" + self.config.altInputClass;\n    }\n    triggerEvent(\"onParseConfig\");\n  }\n  function getInputElem() {\n    return self.config.wrap ? element.querySelector(\"[data-input]\") : element;\n  }\n  function setupLocale() {\n    if (typeof self.config.locale !== \"object\" && typeof flatpickr.l10ns[self.config.locale] === \"undefined\") self.config.errorHandler(new Error(`flatpickr: invalid locale ${self.config.locale}`));\n    self.l10n = Object.assign(Object.assign({}, flatpickr.l10ns.default), typeof self.config.locale === \"object\" ? self.config.locale : self.config.locale !== \"default\" ? flatpickr.l10ns[self.config.locale] : undefined);\n    tokenRegex.K = `(${self.l10n.amPM[0]}|${self.l10n.amPM[1]}|${self.l10n.amPM[0].toLowerCase()}|${self.l10n.amPM[1].toLowerCase()})`;\n    const userConfig = Object.assign(Object.assign({}, instanceConfig), JSON.parse(JSON.stringify(element.dataset || {})));\n    if (userConfig.time_24hr === undefined && flatpickr.defaultConfig.time_24hr === undefined) {\n      self.config.time_24hr = self.l10n.time_24hr;\n    }\n    self.formatDate = createDateFormatter(self);\n    self.parseDate = createDateParser({\n      config: self.config,\n      l10n: self.l10n\n    });\n  }\n  function positionCalendar(customPositionElement) {\n    if (typeof self.config.position === \"function\") {\n      return void self.config.position(self, customPositionElement);\n    }\n    if (self.calendarContainer === undefined) return;\n    triggerEvent(\"onPreCalendarPosition\");\n    const positionElement = customPositionElement || self._positionElement;\n    const calendarHeight = Array.prototype.reduce.call(self.calendarContainer.children, (acc, child) => acc + child.offsetHeight, 0),\n      calendarWidth = self.calendarContainer.offsetWidth,\n      configPos = self.config.position.split(\" \"),\n      configPosVertical = configPos[0],\n      configPosHorizontal = configPos.length > 1 ? configPos[1] : null,\n      inputBounds = positionElement.getBoundingClientRect(),\n      distanceFromBottom = window.innerHeight - inputBounds.bottom,\n      showOnTop = configPosVertical === \"above\" || configPosVertical !== \"below\" && distanceFromBottom < calendarHeight && inputBounds.top > calendarHeight;\n    const top = window.pageYOffset + inputBounds.top + (!showOnTop ? positionElement.offsetHeight + 2 : -calendarHeight - 2);\n    toggleClass(self.calendarContainer, \"arrowTop\", !showOnTop);\n    toggleClass(self.calendarContainer, \"arrowBottom\", showOnTop);\n    if (self.config.inline) return;\n    let left = window.pageXOffset + inputBounds.left;\n    let isCenter = false;\n    let isRight = false;\n    if (configPosHorizontal === \"center\") {\n      left -= (calendarWidth - inputBounds.width) / 2;\n      isCenter = true;\n    } else if (configPosHorizontal === \"right\") {\n      left -= calendarWidth - inputBounds.width;\n      isRight = true;\n    }\n    toggleClass(self.calendarContainer, \"arrowLeft\", !isCenter && !isRight);\n    toggleClass(self.calendarContainer, \"arrowCenter\", isCenter);\n    toggleClass(self.calendarContainer, \"arrowRight\", isRight);\n    const right = window.document.body.offsetWidth - (window.pageXOffset + inputBounds.right);\n    const rightMost = left + calendarWidth > window.document.body.offsetWidth;\n    const centerMost = right + calendarWidth > window.document.body.offsetWidth;\n    toggleClass(self.calendarContainer, \"rightMost\", rightMost);\n    if (self.config.static) return;\n    self.calendarContainer.style.top = `${top}px`;\n    if (!rightMost) {\n      self.calendarContainer.style.left = `${left}px`;\n      self.calendarContainer.style.right = \"auto\";\n    } else if (!centerMost) {\n      self.calendarContainer.style.left = \"auto\";\n      self.calendarContainer.style.right = `${right}px`;\n    } else {\n      const doc = getDocumentStyleSheet();\n      if (doc === undefined) return;\n      const bodyWidth = window.document.body.offsetWidth;\n      const centerLeft = Math.max(0, bodyWidth / 2 - calendarWidth / 2);\n      const centerBefore = \".flatpickr-calendar.centerMost:before\";\n      const centerAfter = \".flatpickr-calendar.centerMost:after\";\n      const centerIndex = doc.cssRules.length;\n      const centerStyle = `{left:${inputBounds.left}px;right:auto;}`;\n      toggleClass(self.calendarContainer, \"rightMost\", false);\n      toggleClass(self.calendarContainer, \"centerMost\", true);\n      doc.insertRule(`${centerBefore},${centerAfter}${centerStyle}`, centerIndex);\n      self.calendarContainer.style.left = `${centerLeft}px`;\n      self.calendarContainer.style.right = \"auto\";\n    }\n  }\n  function getDocumentStyleSheet() {\n    let editableSheet = null;\n    for (let i = 0; i < document.styleSheets.length; i++) {\n      const sheet = document.styleSheets[i];\n      try {\n        sheet.cssRules;\n      } catch (err) {\n        continue;\n      }\n      editableSheet = sheet;\n      break;\n    }\n    return editableSheet != null ? editableSheet : createStyleSheet();\n  }\n  function createStyleSheet() {\n    const style = document.createElement(\"style\");\n    document.head.appendChild(style);\n    return style.sheet;\n  }\n  function redraw() {\n    if (self.config.noCalendar || self.isMobile) return;\n    buildMonthSwitch();\n    updateNavigationCurrentMonth();\n    buildDays();\n  }\n  function focusAndClose() {\n    self._input.focus();\n    if (window.navigator.userAgent.indexOf(\"MSIE\") !== -1 || navigator.msMaxTouchPoints !== undefined) {\n      setTimeout(self.close, 0);\n    } else {\n      self.close();\n    }\n  }\n  function selectDate(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    const isSelectable = day => day.classList && day.classList.contains(\"flatpickr-day\") && !day.classList.contains(\"flatpickr-disabled\") && !day.classList.contains(\"notAllowed\");\n    const t = findParent(getEventTarget(e), isSelectable);\n    if (t === undefined) return;\n    const target = t;\n    const selectedDate = self.latestSelectedDateObj = new Date(target.dateObj.getTime());\n    const shouldChangeMonth = (selectedDate.getMonth() < self.currentMonth || selectedDate.getMonth() > self.currentMonth + self.config.showMonths - 1) && self.config.mode !== \"range\";\n    self.selectedDateElem = target;\n    if (self.config.mode === \"single\") self.selectedDates = [selectedDate];else if (self.config.mode === \"multiple\") {\n      const selectedIndex = isDateSelected(selectedDate);\n      if (selectedIndex) self.selectedDates.splice(parseInt(selectedIndex), 1);else self.selectedDates.push(selectedDate);\n    } else if (self.config.mode === \"range\") {\n      if (self.selectedDates.length === 2) {\n        self.clear(false, false);\n      }\n      self.latestSelectedDateObj = selectedDate;\n      self.selectedDates.push(selectedDate);\n      if (compareDates(selectedDate, self.selectedDates[0], true) !== 0) self.selectedDates.sort((a, b) => a.getTime() - b.getTime());\n    }\n    setHoursFromInputs();\n    if (shouldChangeMonth) {\n      const isNewYear = self.currentYear !== selectedDate.getFullYear();\n      self.currentYear = selectedDate.getFullYear();\n      self.currentMonth = selectedDate.getMonth();\n      if (isNewYear) {\n        triggerEvent(\"onYearChange\");\n        buildMonthSwitch();\n      }\n      triggerEvent(\"onMonthChange\");\n    }\n    updateNavigationCurrentMonth();\n    buildDays();\n    updateValue();\n    if (!shouldChangeMonth && self.config.mode !== \"range\" && self.config.showMonths === 1) focusOnDayElem(target);else if (self.selectedDateElem !== undefined && self.hourElement === undefined) {\n      self.selectedDateElem && self.selectedDateElem.focus();\n    }\n    if (self.hourElement !== undefined) self.hourElement !== undefined && self.hourElement.focus();\n    if (self.config.closeOnSelect) {\n      const single = self.config.mode === \"single\" && !self.config.enableTime;\n      const range = self.config.mode === \"range\" && self.selectedDates.length === 2 && !self.config.enableTime;\n      if (single || range) {\n        focusAndClose();\n      }\n    }\n    triggerChange();\n  }\n  const CALLBACKS = {\n    locale: [setupLocale, updateWeekdays],\n    showMonths: [buildMonths, setCalendarWidth, buildWeekdays],\n    minDate: [jumpToDate],\n    maxDate: [jumpToDate],\n    clickOpens: [() => {\n      if (self.config.clickOpens === true) {\n        bind(self._input, \"focus\", self.open);\n        bind(self._input, \"click\", self.open);\n      } else {\n        self._input.removeEventListener(\"focus\", self.open);\n        self._input.removeEventListener(\"click\", self.open);\n      }\n    }]\n  };\n  function set(option, value) {\n    if (option !== null && typeof option === \"object\") {\n      Object.assign(self.config, option);\n      for (const key in option) {\n        if (CALLBACKS[key] !== undefined) CALLBACKS[key].forEach(x => x());\n      }\n    } else {\n      self.config[option] = value;\n      if (CALLBACKS[option] !== undefined) CALLBACKS[option].forEach(x => x());else if (HOOKS.indexOf(option) > -1) self.config[option] = arrayify(value);\n    }\n    self.redraw();\n    updateValue(true);\n  }\n  function setSelectedDate(inputDate, format) {\n    let dates = [];\n    if (inputDate instanceof Array) dates = inputDate.map(d => self.parseDate(d, format));else if (inputDate instanceof Date || typeof inputDate === \"number\") dates = [self.parseDate(inputDate, format)];else if (typeof inputDate === \"string\") {\n      switch (self.config.mode) {\n        case \"single\":\n        case \"time\":\n          dates = [self.parseDate(inputDate, format)];\n          break;\n        case \"multiple\":\n          dates = inputDate.split(self.config.conjunction).map(date => self.parseDate(date, format));\n          break;\n        case \"range\":\n          dates = inputDate.split(self.l10n.rangeSeparator).map(date => self.parseDate(date, format));\n          break;\n        default:\n          break;\n      }\n    } else self.config.errorHandler(new Error(`Invalid date supplied: ${JSON.stringify(inputDate)}`));\n    self.selectedDates = self.config.allowInvalidPreload ? dates : dates.filter(d => d instanceof Date && isEnabled(d, false));\n    if (self.config.mode === \"range\") self.selectedDates.sort((a, b) => a.getTime() - b.getTime());\n  }\n  function setDate(date, triggerChange = false, format = self.config.dateFormat) {\n    if (date !== 0 && !date || date instanceof Array && date.length === 0) return self.clear(triggerChange);\n    setSelectedDate(date, format);\n    self.latestSelectedDateObj = self.selectedDates[self.selectedDates.length - 1];\n    self.redraw();\n    jumpToDate(undefined, triggerChange);\n    setHoursFromDate();\n    if (self.selectedDates.length === 0) {\n      self.clear(false);\n    }\n    updateValue(triggerChange);\n    if (triggerChange) triggerEvent(\"onChange\");\n  }\n  function parseDateRules(arr) {\n    return arr.slice().map(rule => {\n      if (typeof rule === \"string\" || typeof rule === \"number\" || rule instanceof Date) {\n        return self.parseDate(rule, undefined, true);\n      } else if (rule && typeof rule === \"object\" && rule.from && rule.to) return {\n        from: self.parseDate(rule.from, undefined),\n        to: self.parseDate(rule.to, undefined)\n      };\n      return rule;\n    }).filter(x => x);\n  }\n  function setupDates() {\n    self.selectedDates = [];\n    self.now = self.parseDate(self.config.now) || new Date();\n    const preloadedDate = self.config.defaultDate || ((self.input.nodeName === \"INPUT\" || self.input.nodeName === \"TEXTAREA\") && self.input.placeholder && self.input.value === self.input.placeholder ? null : self.input.value);\n    if (preloadedDate) setSelectedDate(preloadedDate, self.config.dateFormat);\n    self._initialDate = self.selectedDates.length > 0 ? self.selectedDates[0] : self.config.minDate && self.config.minDate.getTime() > self.now.getTime() ? self.config.minDate : self.config.maxDate && self.config.maxDate.getTime() < self.now.getTime() ? self.config.maxDate : self.now;\n    self.currentYear = self._initialDate.getFullYear();\n    self.currentMonth = self._initialDate.getMonth();\n    if (self.selectedDates.length > 0) self.latestSelectedDateObj = self.selectedDates[0];\n    if (self.config.minTime !== undefined) self.config.minTime = self.parseDate(self.config.minTime, \"H:i\");\n    if (self.config.maxTime !== undefined) self.config.maxTime = self.parseDate(self.config.maxTime, \"H:i\");\n    self.minDateHasTime = !!self.config.minDate && (self.config.minDate.getHours() > 0 || self.config.minDate.getMinutes() > 0 || self.config.minDate.getSeconds() > 0);\n    self.maxDateHasTime = !!self.config.maxDate && (self.config.maxDate.getHours() > 0 || self.config.maxDate.getMinutes() > 0 || self.config.maxDate.getSeconds() > 0);\n  }\n  function setupInputs() {\n    self.input = getInputElem();\n    if (!self.input) {\n      self.config.errorHandler(new Error(\"Invalid input element specified\"));\n      return;\n    }\n    self.input._type = self.input.type;\n    self.input.type = \"text\";\n    self.input.classList.add(\"flatpickr-input\");\n    self._input = self.input;\n    if (self.config.altInput) {\n      self.altInput = createElement(self.input.nodeName, self.config.altInputClass);\n      self._input = self.altInput;\n      self.altInput.placeholder = self.input.placeholder;\n      self.altInput.disabled = self.input.disabled;\n      self.altInput.required = self.input.required;\n      self.altInput.tabIndex = self.input.tabIndex;\n      self.altInput.type = \"text\";\n      self.input.setAttribute(\"type\", \"hidden\");\n      if (!self.config.static && self.input.parentNode) self.input.parentNode.insertBefore(self.altInput, self.input.nextSibling);\n    }\n    if (!self.config.allowInput) self._input.setAttribute(\"readonly\", \"readonly\");\n    self._positionElement = self.config.positionElement || self._input;\n  }\n  function setupMobile() {\n    const inputType = self.config.enableTime ? self.config.noCalendar ? \"time\" : \"datetime-local\" : \"date\";\n    self.mobileInput = createElement(\"input\", self.input.className + \" flatpickr-mobile\");\n    self.mobileInput.tabIndex = 1;\n    self.mobileInput.type = inputType;\n    self.mobileInput.disabled = self.input.disabled;\n    self.mobileInput.required = self.input.required;\n    self.mobileInput.placeholder = self.input.placeholder;\n    self.mobileFormatStr = inputType === \"datetime-local\" ? \"Y-m-d\\\\TH:i:S\" : inputType === \"date\" ? \"Y-m-d\" : \"H:i:S\";\n    if (self.selectedDates.length > 0) {\n      self.mobileInput.defaultValue = self.mobileInput.value = self.formatDate(self.selectedDates[0], self.mobileFormatStr);\n    }\n    if (self.config.minDate) self.mobileInput.min = self.formatDate(self.config.minDate, \"Y-m-d\");\n    if (self.config.maxDate) self.mobileInput.max = self.formatDate(self.config.maxDate, \"Y-m-d\");\n    if (self.input.getAttribute(\"step\")) self.mobileInput.step = String(self.input.getAttribute(\"step\"));\n    self.input.type = \"hidden\";\n    if (self.altInput !== undefined) self.altInput.type = \"hidden\";\n    try {\n      if (self.input.parentNode) self.input.parentNode.insertBefore(self.mobileInput, self.input.nextSibling);\n    } catch (_a) {}\n    bind(self.mobileInput, \"change\", e => {\n      self.setDate(getEventTarget(e).value, false, self.mobileFormatStr);\n      triggerEvent(\"onChange\");\n      triggerEvent(\"onClose\");\n    });\n  }\n  function toggle(e) {\n    if (self.isOpen === true) return self.close();\n    self.open(e);\n  }\n  function triggerEvent(event, data) {\n    if (self.config === undefined) return;\n    const hooks = self.config[event];\n    if (hooks !== undefined && hooks.length > 0) {\n      for (let i = 0; hooks[i] && i < hooks.length; i++) hooks[i](self.selectedDates, self.input.value, self, data);\n    }\n    if (event === \"onChange\") {\n      self.input.dispatchEvent(createEvent(\"change\"));\n      self.input.dispatchEvent(createEvent(\"input\"));\n    }\n  }\n  function createEvent(name) {\n    const e = document.createEvent(\"Event\");\n    e.initEvent(name, true, true);\n    return e;\n  }\n  function isDateSelected(date) {\n    for (let i = 0; i < self.selectedDates.length; i++) {\n      if (compareDates(self.selectedDates[i], date) === 0) return \"\" + i;\n    }\n    return false;\n  }\n  function isDateInRange(date) {\n    if (self.config.mode !== \"range\" || self.selectedDates.length < 2) return false;\n    return compareDates(date, self.selectedDates[0]) >= 0 && compareDates(date, self.selectedDates[1]) <= 0;\n  }\n  function updateNavigationCurrentMonth() {\n    if (self.config.noCalendar || self.isMobile || !self.monthNav) return;\n    self.yearElements.forEach((yearElement, i) => {\n      const d = new Date(self.currentYear, self.currentMonth, 1);\n      d.setMonth(self.currentMonth + i);\n      if (self.config.showMonths > 1 || self.config.monthSelectorType === \"static\") {\n        self.monthElements[i].textContent = monthToStr(d.getMonth(), self.config.shorthandCurrentMonth, self.l10n) + \" \";\n      } else {\n        self.monthsDropdownContainer.value = d.getMonth().toString();\n      }\n      yearElement.value = d.getFullYear().toString();\n    });\n    self._hidePrevMonthArrow = self.config.minDate !== undefined && (self.currentYear === self.config.minDate.getFullYear() ? self.currentMonth <= self.config.minDate.getMonth() : self.currentYear < self.config.minDate.getFullYear());\n    self._hideNextMonthArrow = self.config.maxDate !== undefined && (self.currentYear === self.config.maxDate.getFullYear() ? self.currentMonth + 1 > self.config.maxDate.getMonth() : self.currentYear > self.config.maxDate.getFullYear());\n  }\n  function getDateStr(format) {\n    return self.selectedDates.map(dObj => self.formatDate(dObj, format)).filter((d, i, arr) => self.config.mode !== \"range\" || self.config.enableTime || arr.indexOf(d) === i).join(self.config.mode !== \"range\" ? self.config.conjunction : self.l10n.rangeSeparator);\n  }\n  function updateValue(triggerChange = true) {\n    if (self.mobileInput !== undefined && self.mobileFormatStr) {\n      self.mobileInput.value = self.latestSelectedDateObj !== undefined ? self.formatDate(self.latestSelectedDateObj, self.mobileFormatStr) : \"\";\n    }\n    self.input.value = getDateStr(self.config.dateFormat);\n    if (self.altInput !== undefined) {\n      self.altInput.value = getDateStr(self.config.altFormat);\n    }\n    if (triggerChange !== false) triggerEvent(\"onValueUpdate\");\n  }\n  function onMonthNavClick(e) {\n    const eventTarget = getEventTarget(e);\n    const isPrevMonth = self.prevMonthNav.contains(eventTarget);\n    const isNextMonth = self.nextMonthNav.contains(eventTarget);\n    if (isPrevMonth || isNextMonth) {\n      changeMonth(isPrevMonth ? -1 : 1);\n    } else if (self.yearElements.indexOf(eventTarget) >= 0) {\n      eventTarget.select();\n    } else if (eventTarget.classList.contains(\"arrowUp\")) {\n      self.changeYear(self.currentYear + 1);\n    } else if (eventTarget.classList.contains(\"arrowDown\")) {\n      self.changeYear(self.currentYear - 1);\n    }\n  }\n  function timeWrapper(e) {\n    e.preventDefault();\n    const isKeyDown = e.type === \"keydown\",\n      eventTarget = getEventTarget(e),\n      input = eventTarget;\n    if (self.amPM !== undefined && eventTarget === self.amPM) {\n      self.amPM.textContent = self.l10n.amPM[int(self.amPM.textContent === self.l10n.amPM[0])];\n    }\n    const min = parseFloat(input.getAttribute(\"min\")),\n      max = parseFloat(input.getAttribute(\"max\")),\n      step = parseFloat(input.getAttribute(\"step\")),\n      curValue = parseInt(input.value, 10),\n      delta = e.delta || (isKeyDown ? e.which === 38 ? 1 : -1 : 0);\n    let newValue = curValue + step * delta;\n    if (typeof input.value !== \"undefined\" && input.value.length === 2) {\n      const isHourElem = input === self.hourElement,\n        isMinuteElem = input === self.minuteElement;\n      if (newValue < min) {\n        newValue = max + newValue + int(!isHourElem) + (int(isHourElem) && int(!self.amPM));\n        if (isMinuteElem) incrementNumInput(undefined, -1, self.hourElement);\n      } else if (newValue > max) {\n        newValue = input === self.hourElement ? newValue - max - int(!self.amPM) : min;\n        if (isMinuteElem) incrementNumInput(undefined, 1, self.hourElement);\n      }\n      if (self.amPM && isHourElem && (step === 1 ? newValue + curValue === 23 : Math.abs(newValue - curValue) > step)) {\n        self.amPM.textContent = self.l10n.amPM[int(self.amPM.textContent === self.l10n.amPM[0])];\n      }\n      input.value = pad(newValue);\n    }\n  }\n  init();\n  return self;\n}\nfunction _flatpickr(nodeList, config) {\n  const nodes = Array.prototype.slice.call(nodeList).filter(x => x instanceof HTMLElement);\n  const instances = [];\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    try {\n      if (node.getAttribute(\"data-fp-omit\") !== null) continue;\n      if (node._flatpickr !== undefined) {\n        node._flatpickr.destroy();\n        node._flatpickr = undefined;\n      }\n      node._flatpickr = FlatpickrInstance(node, config || {});\n      instances.push(node._flatpickr);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n  return instances.length === 1 ? instances[0] : instances;\n}\nif (typeof HTMLElement !== \"undefined\" && typeof HTMLCollection !== \"undefined\" && typeof NodeList !== \"undefined\") {\n  HTMLCollection.prototype.flatpickr = NodeList.prototype.flatpickr = function (config) {\n    return _flatpickr(this, config);\n  };\n  HTMLElement.prototype.flatpickr = function (config) {\n    return _flatpickr([this], config);\n  };\n}\nvar flatpickr = function (selector, config) {\n  if (typeof selector === \"string\") {\n    return _flatpickr(window.document.querySelectorAll(selector), config);\n  } else if (selector instanceof Node) {\n    return _flatpickr([selector], config);\n  } else {\n    return _flatpickr(selector, config);\n  }\n};\nflatpickr.defaultConfig = {};\nflatpickr.l10ns = {\n  en: Object.assign({}, English),\n  default: Object.assign({}, English)\n};\nflatpickr.localize = l10n => {\n  flatpickr.l10ns.default = Object.assign(Object.assign({}, flatpickr.l10ns.default), l10n);\n};\nflatpickr.setDefaults = config => {\n  flatpickr.defaultConfig = Object.assign(Object.assign({}, flatpickr.defaultConfig), config);\n};\nflatpickr.parseDate = createDateParser({});\nflatpickr.formatDate = createDateFormatter({});\nflatpickr.compareDates = compareDates;\nif (typeof jQuery !== \"undefined\" && typeof jQuery.fn !== \"undefined\") {\n  jQuery.fn.flatpickr = function (config) {\n    return _flatpickr(this, config);\n  };\n}\nDate.prototype.fp_incr = function (days) {\n  return new Date(this.getFullYear(), this.getMonth(), this.getDate() + (typeof days === \"string\" ? parseInt(days, 10) : days));\n};\nif (typeof window !== \"undefined\") {\n  window.flatpickr = flatpickr;\n}\nexport default flatpickr;", "map": {"version": 3, "names": ["defaults", "defaultOptions", "HOOKS", "English", "arrayify", "debounce", "int", "pad", "clearNode", "createElement", "createNumberInput", "findParent", "toggleClass", "getEventTarget", "compareDates", "createDateParser", "createDateFormatter", "duration", "isBetween", "getDefaultHours", "tokenRegex", "monthToStr", "DEBOUNCED_CHANGE_MS", "FlatpickrInstance", "element", "instanceConfig", "self", "config", "Object", "assign", "flatpickr", "defaultConfig", "l10n", "parseDate", "_handlers", "pluginElements", "loadedPlugins", "_bind", "bind", "_setHoursFromDate", "setHoursFromDate", "_positionCalendar", "positionCalendar", "changeMonth", "changeYear", "clear", "close", "_createElement", "destroy", "isEnabled", "jumpToDate", "open", "redraw", "set", "setDate", "toggle", "setupHelperFunctions", "utils", "getDaysInMonth", "month", "currentMonth", "yr", "currentYear", "daysInMonth", "init", "input", "isOpen", "parseConfig", "setupLocale", "setupInputs", "setupDates", "isMobile", "build", "bindEvents", "selectedDates", "length", "noCalendar", "enableTime", "latestSelectedDateObj", "undefined", "updateValue", "setCalendarWidth", "<PERSON><PERSON><PERSON><PERSON>", "test", "navigator", "userAgent", "triggerEvent", "bindToInstance", "fn", "weekNumbers", "showMonths", "window", "requestAnimationFrame", "calendarContainer", "style", "visibility", "display", "<PERSON><PERSON><PERSON><PERSON>", "daysWidth", "days", "offsetWidth", "width", "weekWrapper", "removeProperty", "updateTime", "e", "defaultDate", "minDate", "Date", "getTime", "setHours", "hours", "minutes", "seconds", "getMilliseconds", "type", "timeWrapper", "prevValue", "_input", "value", "setHoursFromInputs", "_debounced<PERSON><PERSON>e", "ampm2military", "hour", "amPM", "military2ampm", "hourElement", "minuteElement", "parseInt", "slice", "secondElement", "textContent", "limitMinHours", "minTime", "minDateHasTime", "limitMaxHours", "maxTime", "maxDate", "maxDateHasTime", "Math", "min", "getHours", "getMinutes", "getSeconds", "max", "date<PERSON><PERSON>j", "date", "time_24hr", "onYearInput", "event", "eventTarget", "year", "delta", "key", "toString", "handler", "options", "Array", "for<PERSON>ach", "ev", "el", "addEventListener", "push", "remove", "removeEventListener", "trigger<PERSON>hange", "wrap", "evt", "prototype", "call", "querySelectorAll", "setupMobile", "debouncedResize", "onResize", "mode", "onMouseOver", "document", "body", "onKeyDown", "inline", "static", "ontouchstart", "documentClick", "capture", "clickOpens", "monthNav", "onMonthNavClick", "selectDate", "time<PERSON><PERSON><PERSON>", "selText", "select", "timeIncrement", "allowInput", "onBlur", "jumpDate", "jumpTo", "now", "oldYear", "oldMonth", "getFullYear", "getMonth", "message", "<PERSON><PERSON><PERSON><PERSON>", "buildMonthSwitch", "className", "indexOf", "incrementNumInput", "classList", "contains", "inputElem", "target", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createEvent", "dispatchEvent", "fragment", "createDocumentFragment", "tabIndex", "append<PERSON><PERSON><PERSON>", "buildMonthNav", "innerContainer", "buildWeeks", "r<PERSON><PERSON><PERSON>", "buildWeekdays", "buildDays", "buildTime", "animate", "customAppend", "appendTo", "nodeType", "add", "insertBefore", "nextS<PERSON>ling", "wrapper", "altInput", "createDay", "dayNumber", "i", "dateIsEnabled", "dayElement", "getDate", "$i", "setAttribute", "formatDate", "ariaDateFormat", "todayDateElem", "isDateSelected", "selectedDateElem", "isDateInRange", "insertAdjacentHTML", "getWeek", "focusOnDayElem", "targetNode", "focus", "getFirstAvailableDay", "startMonth", "endMonth", "m", "children", "startIndex", "endIndex", "c", "getNextAvailableDay", "current", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "numMonthDays", "abs", "focusOnDay", "offset", "dayFocused", "isInView", "activeElement", "startElem", "buildMonthDays", "firstOfMonth", "getDay", "firstDayOfWeek", "prevMonthDays", "isMultiMonth", "prevMonthDayClass", "nextMonthDayClass", "dayIndex", "day<PERSON>um", "<PERSON><PERSON><PERSON><PERSON>", "frag", "d", "setMonth", "monthSelectorType", "shouldBuildMonth", "monthsDropdownContainer", "innerHTML", "shorthand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected", "buildMonth", "container", "monthNavFragment", "monthElement", "monthAriaLabel", "<PERSON><PERSON><PERSON><PERSON>", "yearInput", "tabindex", "yearElement", "getElementsByTagName", "yearAriaLabel", "disabled", "buildMonths", "prevMonthNav", "yearElements", "monthElements", "nextMonthNav", "prevArrow", "nextArrow", "defineProperty", "get", "__hidePrevMonthArrow", "bool", "__hideNextMonthArrow", "currentYearElement", "updateNavigationCurrentMonth", "separator", "hourInput", "hourAriaLabel", "minuteInput", "minuteAriaLabel", "hourIncrement", "minuteIncrement", "enableSeconds", "secondInput", "getAttribute", "defaultHour", "title", "toggleTitle", "weekdayContainer", "updateWeekdays", "weekdays", "shorthand", "splice", "join", "weekAbbreviation", "isOffset", "_hidePrevMonthArrow", "_hideNextMonthArrow", "triggerChangeEvent", "toInitial", "mobileInput", "_initialDate", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_type", "removeAttribute", "k", "_", "isCalendarElem", "elem", "isCalendarElement", "isInput", "path", "lostFocus", "relatedTarget", "isIgnored", "ignoredFocusElements", "some", "newYear", "newYearNum", "isNewYear", "timeless", "_a", "dateT<PERSON><PERSON><PERSON><PERSON>", "enable", "disable", "array", "parsed", "from", "to", "altFormat", "dateFormat", "<PERSON><PERSON><PERSON><PERSON>", "allowInlineKeydown", "keyCode", "blur", "isTimeObj", "preventDefault", "focusAndClose", "ctrl<PERSON>ey", "stopPropagation", "elems", "concat", "filter", "x", "shift<PERSON>ey", "char<PERSON>t", "toLowerCase", "hoverDate", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialDate", "rangeStartDate", "rangeEndDate", "containsDisabled", "minRange", "max<PERSON><PERSON><PERSON>", "t", "DAY", "l", "dayElem", "timestamp", "outOfRange", "positionElement", "_positionElement", "click", "was<PERSON><PERSON>", "setTimeout", "minMaxDateSetter", "inverseDateObj", "boolOpts", "userConfig", "JSON", "parse", "stringify", "dataset", "formats", "_enable", "dates", "parseDateRules", "_disable", "timeMode", "defaultDateFormat", "defaultAltFormat", "_minDate", "_maxDate", "minMaxTimeSetter", "val", "_minTime", "_maxTime", "hook", "map", "disableMobile", "plugins", "pluginConf", "altInputClass", "getInputElem", "querySelector", "locale", "l10ns", "Error", "default", "K", "customPositionElement", "position", "calendarHeight", "reduce", "acc", "child", "offsetHeight", "calendarWidth", "configPos", "split", "configPosVertical", "configPosHorizontal", "inputBounds", "getBoundingClientRect", "distanceFromBottom", "innerHeight", "bottom", "showOnTop", "top", "pageYOffset", "left", "pageXOffset", "isCenter", "isRight", "right", "rightMost", "centerMost", "doc", "getDocumentStyleSheet", "bodyWidth", "centerLeft", "centerBefore", "centerAfter", "centerIndex", "cssRules", "centerStyle", "insertRule", "editableSheet", "styleSheets", "sheet", "err", "createStyleSheet", "head", "msMaxTouchPoints", "isSelectable", "day", "selectedDate", "shouldChangeMonth", "selectedIndex", "sort", "a", "b", "closeOnSelect", "single", "range", "CALLBACKS", "option", "setSelectedDate", "inputDate", "format", "conjunction", "rangeSeparator", "allowInvalidPreload", "arr", "rule", "preloadedDate", "nodeName", "placeholder", "required", "inputType", "mobileFormatStr", "defaultValue", "step", "String", "data", "hooks", "name", "initEvent", "getDateStr", "dObj", "isPrevMonth", "isNextMonth", "isKeyDown", "parseFloat", "curValue", "which", "newValue", "isHourElem", "isMinuteElem", "_flatpickr", "nodeList", "nodes", "HTMLElement", "instances", "node", "console", "error", "HTMLCollection", "NodeList", "selector", "Node", "en", "localize", "setDefaults", "j<PERSON><PERSON><PERSON>", "fp_incr"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/flatpickr/dist/esm/index.js"], "sourcesContent": ["import { defaults as defaultOptions, HOOKS, } from \"./types/options\";\nimport English from \"./l10n/default\";\nimport { arrayify, debounce, int, pad } from \"./utils\";\nimport { clearNode, createElement, createNumberInput, findParent, toggleClass, getEventTarget, } from \"./utils/dom\";\nimport { compareDates, createDateParser, createDateFormatter, duration, isBetween, getDefaultHours, } from \"./utils/dates\";\nimport { tokenRegex, monthToStr } from \"./utils/formatting\";\nimport \"./utils/polyfills\";\nconst DEBOUNCED_CHANGE_MS = 300;\nfunction FlatpickrInstance(element, instanceConfig) {\n    const self = {\n        config: Object.assign(Object.assign({}, defaultOptions), flatpickr.defaultConfig),\n        l10n: English,\n    };\n    self.parseDate = createDateParser({ config: self.config, l10n: self.l10n });\n    self._handlers = [];\n    self.pluginElements = [];\n    self.loadedPlugins = [];\n    self._bind = bind;\n    self._setHoursFromDate = setHoursFromDate;\n    self._positionCalendar = positionCalendar;\n    self.changeMonth = changeMonth;\n    self.changeYear = changeYear;\n    self.clear = clear;\n    self.close = close;\n    self._createElement = createElement;\n    self.destroy = destroy;\n    self.isEnabled = isEnabled;\n    self.jumpToDate = jumpToDate;\n    self.open = open;\n    self.redraw = redraw;\n    self.set = set;\n    self.setDate = setDate;\n    self.toggle = toggle;\n    function setupHelperFunctions() {\n        self.utils = {\n            getDaysInMonth(month = self.currentMonth, yr = self.currentYear) {\n                if (month === 1 && ((yr % 4 === 0 && yr % 100 !== 0) || yr % 400 === 0))\n                    return 29;\n                return self.l10n.daysInMonth[month];\n            },\n        };\n    }\n    function init() {\n        self.element = self.input = element;\n        self.isOpen = false;\n        parseConfig();\n        setupLocale();\n        setupInputs();\n        setupDates();\n        setupHelperFunctions();\n        if (!self.isMobile)\n            build();\n        bindEvents();\n        if (self.selectedDates.length || self.config.noCalendar) {\n            if (self.config.enableTime) {\n                setHoursFromDate(self.config.noCalendar ? self.latestSelectedDateObj : undefined);\n            }\n            updateValue(false);\n        }\n        setCalendarWidth();\n        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n        if (!self.isMobile && isSafari) {\n            positionCalendar();\n        }\n        triggerEvent(\"onReady\");\n    }\n    function bindToInstance(fn) {\n        return fn.bind(self);\n    }\n    function setCalendarWidth() {\n        const config = self.config;\n        if (config.weekNumbers === false && config.showMonths === 1) {\n            return;\n        }\n        else if (config.noCalendar !== true) {\n            window.requestAnimationFrame(function () {\n                if (self.calendarContainer !== undefined) {\n                    self.calendarContainer.style.visibility = \"hidden\";\n                    self.calendarContainer.style.display = \"block\";\n                }\n                if (self.daysContainer !== undefined) {\n                    const daysWidth = (self.days.offsetWidth + 1) * config.showMonths;\n                    self.daysContainer.style.width = daysWidth + \"px\";\n                    self.calendarContainer.style.width =\n                        daysWidth +\n                            (self.weekWrapper !== undefined\n                                ? self.weekWrapper.offsetWidth\n                                : 0) +\n                            \"px\";\n                    self.calendarContainer.style.removeProperty(\"visibility\");\n                    self.calendarContainer.style.removeProperty(\"display\");\n                }\n            });\n        }\n    }\n    function updateTime(e) {\n        if (self.selectedDates.length === 0) {\n            const defaultDate = self.config.minDate === undefined ||\n                compareDates(new Date(), self.config.minDate) >= 0\n                ? new Date()\n                : new Date(self.config.minDate.getTime());\n            const defaults = getDefaultHours(self.config);\n            defaultDate.setHours(defaults.hours, defaults.minutes, defaults.seconds, defaultDate.getMilliseconds());\n            self.selectedDates = [defaultDate];\n            self.latestSelectedDateObj = defaultDate;\n        }\n        if (e !== undefined && e.type !== \"blur\") {\n            timeWrapper(e);\n        }\n        const prevValue = self._input.value;\n        setHoursFromInputs();\n        updateValue();\n        if (self._input.value !== prevValue) {\n            self._debouncedChange();\n        }\n    }\n    function ampm2military(hour, amPM) {\n        return (hour % 12) + 12 * int(amPM === self.l10n.amPM[1]);\n    }\n    function military2ampm(hour) {\n        switch (hour % 24) {\n            case 0:\n            case 12:\n                return 12;\n            default:\n                return hour % 12;\n        }\n    }\n    function setHoursFromInputs() {\n        if (self.hourElement === undefined || self.minuteElement === undefined)\n            return;\n        let hours = (parseInt(self.hourElement.value.slice(-2), 10) || 0) % 24, minutes = (parseInt(self.minuteElement.value, 10) || 0) % 60, seconds = self.secondElement !== undefined\n            ? (parseInt(self.secondElement.value, 10) || 0) % 60\n            : 0;\n        if (self.amPM !== undefined) {\n            hours = ampm2military(hours, self.amPM.textContent);\n        }\n        const limitMinHours = self.config.minTime !== undefined ||\n            (self.config.minDate &&\n                self.minDateHasTime &&\n                self.latestSelectedDateObj &&\n                compareDates(self.latestSelectedDateObj, self.config.minDate, true) ===\n                    0);\n        const limitMaxHours = self.config.maxTime !== undefined ||\n            (self.config.maxDate &&\n                self.maxDateHasTime &&\n                self.latestSelectedDateObj &&\n                compareDates(self.latestSelectedDateObj, self.config.maxDate, true) ===\n                    0);\n        if (limitMaxHours) {\n            const maxTime = self.config.maxTime !== undefined\n                ? self.config.maxTime\n                : self.config.maxDate;\n            hours = Math.min(hours, maxTime.getHours());\n            if (hours === maxTime.getHours())\n                minutes = Math.min(minutes, maxTime.getMinutes());\n            if (minutes === maxTime.getMinutes())\n                seconds = Math.min(seconds, maxTime.getSeconds());\n        }\n        if (limitMinHours) {\n            const minTime = self.config.minTime !== undefined\n                ? self.config.minTime\n                : self.config.minDate;\n            hours = Math.max(hours, minTime.getHours());\n            if (hours === minTime.getHours() && minutes < minTime.getMinutes())\n                minutes = minTime.getMinutes();\n            if (minutes === minTime.getMinutes())\n                seconds = Math.max(seconds, minTime.getSeconds());\n        }\n        setHours(hours, minutes, seconds);\n    }\n    function setHoursFromDate(dateObj) {\n        const date = dateObj || self.latestSelectedDateObj;\n        if (date) {\n            setHours(date.getHours(), date.getMinutes(), date.getSeconds());\n        }\n    }\n    function setHours(hours, minutes, seconds) {\n        if (self.latestSelectedDateObj !== undefined) {\n            self.latestSelectedDateObj.setHours(hours % 24, minutes, seconds || 0, 0);\n        }\n        if (!self.hourElement || !self.minuteElement || self.isMobile)\n            return;\n        self.hourElement.value = pad(!self.config.time_24hr\n            ? ((12 + hours) % 12) + 12 * int(hours % 12 === 0)\n            : hours);\n        self.minuteElement.value = pad(minutes);\n        if (self.amPM !== undefined)\n            self.amPM.textContent = self.l10n.amPM[int(hours >= 12)];\n        if (self.secondElement !== undefined)\n            self.secondElement.value = pad(seconds);\n    }\n    function onYearInput(event) {\n        const eventTarget = getEventTarget(event);\n        const year = parseInt(eventTarget.value) + (event.delta || 0);\n        if (year / 1000 > 1 ||\n            (event.key === \"Enter\" && !/[^\\d]/.test(year.toString()))) {\n            changeYear(year);\n        }\n    }\n    function bind(element, event, handler, options) {\n        if (event instanceof Array)\n            return event.forEach((ev) => bind(element, ev, handler, options));\n        if (element instanceof Array)\n            return element.forEach((el) => bind(el, event, handler, options));\n        element.addEventListener(event, handler, options);\n        self._handlers.push({\n            remove: () => element.removeEventListener(event, handler),\n        });\n    }\n    function triggerChange() {\n        triggerEvent(\"onChange\");\n    }\n    function bindEvents() {\n        if (self.config.wrap) {\n            [\"open\", \"close\", \"toggle\", \"clear\"].forEach((evt) => {\n                Array.prototype.forEach.call(self.element.querySelectorAll(`[data-${evt}]`), (el) => bind(el, \"click\", self[evt]));\n            });\n        }\n        if (self.isMobile) {\n            setupMobile();\n            return;\n        }\n        const debouncedResize = debounce(onResize, 50);\n        self._debouncedChange = debounce(triggerChange, DEBOUNCED_CHANGE_MS);\n        if (self.daysContainer && !/iPhone|iPad|iPod/i.test(navigator.userAgent))\n            bind(self.daysContainer, \"mouseover\", (e) => {\n                if (self.config.mode === \"range\")\n                    onMouseOver(getEventTarget(e));\n            });\n        bind(window.document.body, \"keydown\", onKeyDown);\n        if (!self.config.inline && !self.config.static)\n            bind(window, \"resize\", debouncedResize);\n        if (window.ontouchstart !== undefined)\n            bind(window.document, \"touchstart\", documentClick);\n        else\n            bind(window.document, \"mousedown\", documentClick);\n        bind(window.document, \"focus\", documentClick, { capture: true });\n        if (self.config.clickOpens === true) {\n            bind(self._input, \"focus\", self.open);\n            bind(self._input, \"click\", self.open);\n        }\n        if (self.daysContainer !== undefined) {\n            bind(self.monthNav, \"click\", onMonthNavClick);\n            bind(self.monthNav, [\"keyup\", \"increment\"], onYearInput);\n            bind(self.daysContainer, \"click\", selectDate);\n        }\n        if (self.timeContainer !== undefined &&\n            self.minuteElement !== undefined &&\n            self.hourElement !== undefined) {\n            const selText = (e) => getEventTarget(e).select();\n            bind(self.timeContainer, [\"increment\"], updateTime);\n            bind(self.timeContainer, \"blur\", updateTime, { capture: true });\n            bind(self.timeContainer, \"click\", timeIncrement);\n            bind([self.hourElement, self.minuteElement], [\"focus\", \"click\"], selText);\n            if (self.secondElement !== undefined)\n                bind(self.secondElement, \"focus\", () => self.secondElement && self.secondElement.select());\n            if (self.amPM !== undefined) {\n                bind(self.amPM, \"click\", (e) => {\n                    updateTime(e);\n                    triggerChange();\n                });\n            }\n        }\n        if (self.config.allowInput) {\n            bind(self._input, \"blur\", onBlur);\n        }\n    }\n    function jumpToDate(jumpDate, triggerChange) {\n        const jumpTo = jumpDate !== undefined\n            ? self.parseDate(jumpDate)\n            : self.latestSelectedDateObj ||\n                (self.config.minDate && self.config.minDate > self.now\n                    ? self.config.minDate\n                    : self.config.maxDate && self.config.maxDate < self.now\n                        ? self.config.maxDate\n                        : self.now);\n        const oldYear = self.currentYear;\n        const oldMonth = self.currentMonth;\n        try {\n            if (jumpTo !== undefined) {\n                self.currentYear = jumpTo.getFullYear();\n                self.currentMonth = jumpTo.getMonth();\n            }\n        }\n        catch (e) {\n            e.message = \"Invalid date supplied: \" + jumpTo;\n            self.config.errorHandler(e);\n        }\n        if (triggerChange && self.currentYear !== oldYear) {\n            triggerEvent(\"onYearChange\");\n            buildMonthSwitch();\n        }\n        if (triggerChange &&\n            (self.currentYear !== oldYear || self.currentMonth !== oldMonth)) {\n            triggerEvent(\"onMonthChange\");\n        }\n        self.redraw();\n    }\n    function timeIncrement(e) {\n        const eventTarget = getEventTarget(e);\n        if (~eventTarget.className.indexOf(\"arrow\"))\n            incrementNumInput(e, eventTarget.classList.contains(\"arrowUp\") ? 1 : -1);\n    }\n    function incrementNumInput(e, delta, inputElem) {\n        const target = e && getEventTarget(e);\n        const input = inputElem ||\n            (target && target.parentNode && target.parentNode.firstChild);\n        const event = createEvent(\"increment\");\n        event.delta = delta;\n        input && input.dispatchEvent(event);\n    }\n    function build() {\n        const fragment = window.document.createDocumentFragment();\n        self.calendarContainer = createElement(\"div\", \"flatpickr-calendar\");\n        self.calendarContainer.tabIndex = -1;\n        if (!self.config.noCalendar) {\n            fragment.appendChild(buildMonthNav());\n            self.innerContainer = createElement(\"div\", \"flatpickr-innerContainer\");\n            if (self.config.weekNumbers) {\n                const { weekWrapper, weekNumbers } = buildWeeks();\n                self.innerContainer.appendChild(weekWrapper);\n                self.weekNumbers = weekNumbers;\n                self.weekWrapper = weekWrapper;\n            }\n            self.rContainer = createElement(\"div\", \"flatpickr-rContainer\");\n            self.rContainer.appendChild(buildWeekdays());\n            if (!self.daysContainer) {\n                self.daysContainer = createElement(\"div\", \"flatpickr-days\");\n                self.daysContainer.tabIndex = -1;\n            }\n            buildDays();\n            self.rContainer.appendChild(self.daysContainer);\n            self.innerContainer.appendChild(self.rContainer);\n            fragment.appendChild(self.innerContainer);\n        }\n        if (self.config.enableTime) {\n            fragment.appendChild(buildTime());\n        }\n        toggleClass(self.calendarContainer, \"rangeMode\", self.config.mode === \"range\");\n        toggleClass(self.calendarContainer, \"animate\", self.config.animate === true);\n        toggleClass(self.calendarContainer, \"multiMonth\", self.config.showMonths > 1);\n        self.calendarContainer.appendChild(fragment);\n        const customAppend = self.config.appendTo !== undefined &&\n            self.config.appendTo.nodeType !== undefined;\n        if (self.config.inline || self.config.static) {\n            self.calendarContainer.classList.add(self.config.inline ? \"inline\" : \"static\");\n            if (self.config.inline) {\n                if (!customAppend && self.element.parentNode)\n                    self.element.parentNode.insertBefore(self.calendarContainer, self._input.nextSibling);\n                else if (self.config.appendTo !== undefined)\n                    self.config.appendTo.appendChild(self.calendarContainer);\n            }\n            if (self.config.static) {\n                const wrapper = createElement(\"div\", \"flatpickr-wrapper\");\n                if (self.element.parentNode)\n                    self.element.parentNode.insertBefore(wrapper, self.element);\n                wrapper.appendChild(self.element);\n                if (self.altInput)\n                    wrapper.appendChild(self.altInput);\n                wrapper.appendChild(self.calendarContainer);\n            }\n        }\n        if (!self.config.static && !self.config.inline)\n            (self.config.appendTo !== undefined\n                ? self.config.appendTo\n                : window.document.body).appendChild(self.calendarContainer);\n    }\n    function createDay(className, date, dayNumber, i) {\n        const dateIsEnabled = isEnabled(date, true), dayElement = createElement(\"span\", \"flatpickr-day \" + className, date.getDate().toString());\n        dayElement.dateObj = date;\n        dayElement.$i = i;\n        dayElement.setAttribute(\"aria-label\", self.formatDate(date, self.config.ariaDateFormat));\n        if (className.indexOf(\"hidden\") === -1 &&\n            compareDates(date, self.now) === 0) {\n            self.todayDateElem = dayElement;\n            dayElement.classList.add(\"today\");\n            dayElement.setAttribute(\"aria-current\", \"date\");\n        }\n        if (dateIsEnabled) {\n            dayElement.tabIndex = -1;\n            if (isDateSelected(date)) {\n                dayElement.classList.add(\"selected\");\n                self.selectedDateElem = dayElement;\n                if (self.config.mode === \"range\") {\n                    toggleClass(dayElement, \"startRange\", self.selectedDates[0] &&\n                        compareDates(date, self.selectedDates[0], true) === 0);\n                    toggleClass(dayElement, \"endRange\", self.selectedDates[1] &&\n                        compareDates(date, self.selectedDates[1], true) === 0);\n                    if (className === \"nextMonthDay\")\n                        dayElement.classList.add(\"inRange\");\n                }\n            }\n        }\n        else {\n            dayElement.classList.add(\"flatpickr-disabled\");\n        }\n        if (self.config.mode === \"range\") {\n            if (isDateInRange(date) && !isDateSelected(date))\n                dayElement.classList.add(\"inRange\");\n        }\n        if (self.weekNumbers &&\n            self.config.showMonths === 1 &&\n            className !== \"prevMonthDay\" &&\n            dayNumber % 7 === 1) {\n            self.weekNumbers.insertAdjacentHTML(\"beforeend\", \"<span class='flatpickr-day'>\" + self.config.getWeek(date) + \"</span>\");\n        }\n        triggerEvent(\"onDayCreate\", dayElement);\n        return dayElement;\n    }\n    function focusOnDayElem(targetNode) {\n        targetNode.focus();\n        if (self.config.mode === \"range\")\n            onMouseOver(targetNode);\n    }\n    function getFirstAvailableDay(delta) {\n        const startMonth = delta > 0 ? 0 : self.config.showMonths - 1;\n        const endMonth = delta > 0 ? self.config.showMonths : -1;\n        for (let m = startMonth; m != endMonth; m += delta) {\n            const month = self.daysContainer.children[m];\n            const startIndex = delta > 0 ? 0 : month.children.length - 1;\n            const endIndex = delta > 0 ? month.children.length : -1;\n            for (let i = startIndex; i != endIndex; i += delta) {\n                const c = month.children[i];\n                if (c.className.indexOf(\"hidden\") === -1 && isEnabled(c.dateObj))\n                    return c;\n            }\n        }\n        return undefined;\n    }\n    function getNextAvailableDay(current, delta) {\n        const givenMonth = current.className.indexOf(\"Month\") === -1\n            ? current.dateObj.getMonth()\n            : self.currentMonth;\n        const endMonth = delta > 0 ? self.config.showMonths : -1;\n        const loopDelta = delta > 0 ? 1 : -1;\n        for (let m = givenMonth - self.currentMonth; m != endMonth; m += loopDelta) {\n            const month = self.daysContainer.children[m];\n            const startIndex = givenMonth - self.currentMonth === m\n                ? current.$i + delta\n                : delta < 0\n                    ? month.children.length - 1\n                    : 0;\n            const numMonthDays = month.children.length;\n            for (let i = startIndex; i >= 0 && i < numMonthDays && i != (delta > 0 ? numMonthDays : -1); i += loopDelta) {\n                const c = month.children[i];\n                if (c.className.indexOf(\"hidden\") === -1 &&\n                    isEnabled(c.dateObj) &&\n                    Math.abs(current.$i - i) >= Math.abs(delta))\n                    return focusOnDayElem(c);\n            }\n        }\n        self.changeMonth(loopDelta);\n        focusOnDay(getFirstAvailableDay(loopDelta), 0);\n        return undefined;\n    }\n    function focusOnDay(current, offset) {\n        const dayFocused = isInView(document.activeElement || document.body);\n        const startElem = current !== undefined\n            ? current\n            : dayFocused\n                ? document.activeElement\n                : self.selectedDateElem !== undefined && isInView(self.selectedDateElem)\n                    ? self.selectedDateElem\n                    : self.todayDateElem !== undefined && isInView(self.todayDateElem)\n                        ? self.todayDateElem\n                        : getFirstAvailableDay(offset > 0 ? 1 : -1);\n        if (startElem === undefined) {\n            self._input.focus();\n        }\n        else if (!dayFocused) {\n            focusOnDayElem(startElem);\n        }\n        else {\n            getNextAvailableDay(startElem, offset);\n        }\n    }\n    function buildMonthDays(year, month) {\n        const firstOfMonth = (new Date(year, month, 1).getDay() - self.l10n.firstDayOfWeek + 7) % 7;\n        const prevMonthDays = self.utils.getDaysInMonth((month - 1 + 12) % 12, year);\n        const daysInMonth = self.utils.getDaysInMonth(month, year), days = window.document.createDocumentFragment(), isMultiMonth = self.config.showMonths > 1, prevMonthDayClass = isMultiMonth ? \"prevMonthDay hidden\" : \"prevMonthDay\", nextMonthDayClass = isMultiMonth ? \"nextMonthDay hidden\" : \"nextMonthDay\";\n        let dayNumber = prevMonthDays + 1 - firstOfMonth, dayIndex = 0;\n        for (; dayNumber <= prevMonthDays; dayNumber++, dayIndex++) {\n            days.appendChild(createDay(prevMonthDayClass, new Date(year, month - 1, dayNumber), dayNumber, dayIndex));\n        }\n        for (dayNumber = 1; dayNumber <= daysInMonth; dayNumber++, dayIndex++) {\n            days.appendChild(createDay(\"\", new Date(year, month, dayNumber), dayNumber, dayIndex));\n        }\n        for (let dayNum = daysInMonth + 1; dayNum <= 42 - firstOfMonth &&\n            (self.config.showMonths === 1 || dayIndex % 7 !== 0); dayNum++, dayIndex++) {\n            days.appendChild(createDay(nextMonthDayClass, new Date(year, month + 1, dayNum % daysInMonth), dayNum, dayIndex));\n        }\n        const dayContainer = createElement(\"div\", \"dayContainer\");\n        dayContainer.appendChild(days);\n        return dayContainer;\n    }\n    function buildDays() {\n        if (self.daysContainer === undefined) {\n            return;\n        }\n        clearNode(self.daysContainer);\n        if (self.weekNumbers)\n            clearNode(self.weekNumbers);\n        const frag = document.createDocumentFragment();\n        for (let i = 0; i < self.config.showMonths; i++) {\n            const d = new Date(self.currentYear, self.currentMonth, 1);\n            d.setMonth(self.currentMonth + i);\n            frag.appendChild(buildMonthDays(d.getFullYear(), d.getMonth()));\n        }\n        self.daysContainer.appendChild(frag);\n        self.days = self.daysContainer.firstChild;\n        if (self.config.mode === \"range\" && self.selectedDates.length === 1) {\n            onMouseOver();\n        }\n    }\n    function buildMonthSwitch() {\n        if (self.config.showMonths > 1 ||\n            self.config.monthSelectorType !== \"dropdown\")\n            return;\n        const shouldBuildMonth = function (month) {\n            if (self.config.minDate !== undefined &&\n                self.currentYear === self.config.minDate.getFullYear() &&\n                month < self.config.minDate.getMonth()) {\n                return false;\n            }\n            return !(self.config.maxDate !== undefined &&\n                self.currentYear === self.config.maxDate.getFullYear() &&\n                month > self.config.maxDate.getMonth());\n        };\n        self.monthsDropdownContainer.tabIndex = -1;\n        self.monthsDropdownContainer.innerHTML = \"\";\n        for (let i = 0; i < 12; i++) {\n            if (!shouldBuildMonth(i))\n                continue;\n            const month = createElement(\"option\", \"flatpickr-monthDropdown-month\");\n            month.value = new Date(self.currentYear, i).getMonth().toString();\n            month.textContent = monthToStr(i, self.config.shorthandCurrentMonth, self.l10n);\n            month.tabIndex = -1;\n            if (self.currentMonth === i) {\n                month.selected = true;\n            }\n            self.monthsDropdownContainer.appendChild(month);\n        }\n    }\n    function buildMonth() {\n        const container = createElement(\"div\", \"flatpickr-month\");\n        const monthNavFragment = window.document.createDocumentFragment();\n        let monthElement;\n        if (self.config.showMonths > 1 ||\n            self.config.monthSelectorType === \"static\") {\n            monthElement = createElement(\"span\", \"cur-month\");\n        }\n        else {\n            self.monthsDropdownContainer = createElement(\"select\", \"flatpickr-monthDropdown-months\");\n            self.monthsDropdownContainer.setAttribute(\"aria-label\", self.l10n.monthAriaLabel);\n            bind(self.monthsDropdownContainer, \"change\", (e) => {\n                const target = getEventTarget(e);\n                const selectedMonth = parseInt(target.value, 10);\n                self.changeMonth(selectedMonth - self.currentMonth);\n                triggerEvent(\"onMonthChange\");\n            });\n            buildMonthSwitch();\n            monthElement = self.monthsDropdownContainer;\n        }\n        const yearInput = createNumberInput(\"cur-year\", { tabindex: \"-1\" });\n        const yearElement = yearInput.getElementsByTagName(\"input\")[0];\n        yearElement.setAttribute(\"aria-label\", self.l10n.yearAriaLabel);\n        if (self.config.minDate) {\n            yearElement.setAttribute(\"min\", self.config.minDate.getFullYear().toString());\n        }\n        if (self.config.maxDate) {\n            yearElement.setAttribute(\"max\", self.config.maxDate.getFullYear().toString());\n            yearElement.disabled =\n                !!self.config.minDate &&\n                    self.config.minDate.getFullYear() === self.config.maxDate.getFullYear();\n        }\n        const currentMonth = createElement(\"div\", \"flatpickr-current-month\");\n        currentMonth.appendChild(monthElement);\n        currentMonth.appendChild(yearInput);\n        monthNavFragment.appendChild(currentMonth);\n        container.appendChild(monthNavFragment);\n        return {\n            container,\n            yearElement,\n            monthElement,\n        };\n    }\n    function buildMonths() {\n        clearNode(self.monthNav);\n        self.monthNav.appendChild(self.prevMonthNav);\n        if (self.config.showMonths) {\n            self.yearElements = [];\n            self.monthElements = [];\n        }\n        for (let m = self.config.showMonths; m--;) {\n            const month = buildMonth();\n            self.yearElements.push(month.yearElement);\n            self.monthElements.push(month.monthElement);\n            self.monthNav.appendChild(month.container);\n        }\n        self.monthNav.appendChild(self.nextMonthNav);\n    }\n    function buildMonthNav() {\n        self.monthNav = createElement(\"div\", \"flatpickr-months\");\n        self.yearElements = [];\n        self.monthElements = [];\n        self.prevMonthNav = createElement(\"span\", \"flatpickr-prev-month\");\n        self.prevMonthNav.innerHTML = self.config.prevArrow;\n        self.nextMonthNav = createElement(\"span\", \"flatpickr-next-month\");\n        self.nextMonthNav.innerHTML = self.config.nextArrow;\n        buildMonths();\n        Object.defineProperty(self, \"_hidePrevMonthArrow\", {\n            get: () => self.__hidePrevMonthArrow,\n            set(bool) {\n                if (self.__hidePrevMonthArrow !== bool) {\n                    toggleClass(self.prevMonthNav, \"flatpickr-disabled\", bool);\n                    self.__hidePrevMonthArrow = bool;\n                }\n            },\n        });\n        Object.defineProperty(self, \"_hideNextMonthArrow\", {\n            get: () => self.__hideNextMonthArrow,\n            set(bool) {\n                if (self.__hideNextMonthArrow !== bool) {\n                    toggleClass(self.nextMonthNav, \"flatpickr-disabled\", bool);\n                    self.__hideNextMonthArrow = bool;\n                }\n            },\n        });\n        self.currentYearElement = self.yearElements[0];\n        updateNavigationCurrentMonth();\n        return self.monthNav;\n    }\n    function buildTime() {\n        self.calendarContainer.classList.add(\"hasTime\");\n        if (self.config.noCalendar)\n            self.calendarContainer.classList.add(\"noCalendar\");\n        const defaults = getDefaultHours(self.config);\n        self.timeContainer = createElement(\"div\", \"flatpickr-time\");\n        self.timeContainer.tabIndex = -1;\n        const separator = createElement(\"span\", \"flatpickr-time-separator\", \":\");\n        const hourInput = createNumberInput(\"flatpickr-hour\", {\n            \"aria-label\": self.l10n.hourAriaLabel,\n        });\n        self.hourElement = hourInput.getElementsByTagName(\"input\")[0];\n        const minuteInput = createNumberInput(\"flatpickr-minute\", {\n            \"aria-label\": self.l10n.minuteAriaLabel,\n        });\n        self.minuteElement = minuteInput.getElementsByTagName(\"input\")[0];\n        self.hourElement.tabIndex = self.minuteElement.tabIndex = -1;\n        self.hourElement.value = pad(self.latestSelectedDateObj\n            ? self.latestSelectedDateObj.getHours()\n            : self.config.time_24hr\n                ? defaults.hours\n                : military2ampm(defaults.hours));\n        self.minuteElement.value = pad(self.latestSelectedDateObj\n            ? self.latestSelectedDateObj.getMinutes()\n            : defaults.minutes);\n        self.hourElement.setAttribute(\"step\", self.config.hourIncrement.toString());\n        self.minuteElement.setAttribute(\"step\", self.config.minuteIncrement.toString());\n        self.hourElement.setAttribute(\"min\", self.config.time_24hr ? \"0\" : \"1\");\n        self.hourElement.setAttribute(\"max\", self.config.time_24hr ? \"23\" : \"12\");\n        self.hourElement.setAttribute(\"maxlength\", \"2\");\n        self.minuteElement.setAttribute(\"min\", \"0\");\n        self.minuteElement.setAttribute(\"max\", \"59\");\n        self.minuteElement.setAttribute(\"maxlength\", \"2\");\n        self.timeContainer.appendChild(hourInput);\n        self.timeContainer.appendChild(separator);\n        self.timeContainer.appendChild(minuteInput);\n        if (self.config.time_24hr)\n            self.timeContainer.classList.add(\"time24hr\");\n        if (self.config.enableSeconds) {\n            self.timeContainer.classList.add(\"hasSeconds\");\n            const secondInput = createNumberInput(\"flatpickr-second\");\n            self.secondElement = secondInput.getElementsByTagName(\"input\")[0];\n            self.secondElement.value = pad(self.latestSelectedDateObj\n                ? self.latestSelectedDateObj.getSeconds()\n                : defaults.seconds);\n            self.secondElement.setAttribute(\"step\", self.minuteElement.getAttribute(\"step\"));\n            self.secondElement.setAttribute(\"min\", \"0\");\n            self.secondElement.setAttribute(\"max\", \"59\");\n            self.secondElement.setAttribute(\"maxlength\", \"2\");\n            self.timeContainer.appendChild(createElement(\"span\", \"flatpickr-time-separator\", \":\"));\n            self.timeContainer.appendChild(secondInput);\n        }\n        if (!self.config.time_24hr) {\n            self.amPM = createElement(\"span\", \"flatpickr-am-pm\", self.l10n.amPM[int((self.latestSelectedDateObj\n                ? self.hourElement.value\n                : self.config.defaultHour) > 11)]);\n            self.amPM.title = self.l10n.toggleTitle;\n            self.amPM.tabIndex = -1;\n            self.timeContainer.appendChild(self.amPM);\n        }\n        return self.timeContainer;\n    }\n    function buildWeekdays() {\n        if (!self.weekdayContainer)\n            self.weekdayContainer = createElement(\"div\", \"flatpickr-weekdays\");\n        else\n            clearNode(self.weekdayContainer);\n        for (let i = self.config.showMonths; i--;) {\n            const container = createElement(\"div\", \"flatpickr-weekdaycontainer\");\n            self.weekdayContainer.appendChild(container);\n        }\n        updateWeekdays();\n        return self.weekdayContainer;\n    }\n    function updateWeekdays() {\n        if (!self.weekdayContainer) {\n            return;\n        }\n        const firstDayOfWeek = self.l10n.firstDayOfWeek;\n        let weekdays = [...self.l10n.weekdays.shorthand];\n        if (firstDayOfWeek > 0 && firstDayOfWeek < weekdays.length) {\n            weekdays = [\n                ...weekdays.splice(firstDayOfWeek, weekdays.length),\n                ...weekdays.splice(0, firstDayOfWeek),\n            ];\n        }\n        for (let i = self.config.showMonths; i--;) {\n            self.weekdayContainer.children[i].innerHTML = `\n      <span class='flatpickr-weekday'>\n        ${weekdays.join(\"</span><span class='flatpickr-weekday'>\")}\n      </span>\n      `;\n        }\n    }\n    function buildWeeks() {\n        self.calendarContainer.classList.add(\"hasWeeks\");\n        const weekWrapper = createElement(\"div\", \"flatpickr-weekwrapper\");\n        weekWrapper.appendChild(createElement(\"span\", \"flatpickr-weekday\", self.l10n.weekAbbreviation));\n        const weekNumbers = createElement(\"div\", \"flatpickr-weeks\");\n        weekWrapper.appendChild(weekNumbers);\n        return {\n            weekWrapper,\n            weekNumbers,\n        };\n    }\n    function changeMonth(value, isOffset = true) {\n        const delta = isOffset ? value : value - self.currentMonth;\n        if ((delta < 0 && self._hidePrevMonthArrow === true) ||\n            (delta > 0 && self._hideNextMonthArrow === true))\n            return;\n        self.currentMonth += delta;\n        if (self.currentMonth < 0 || self.currentMonth > 11) {\n            self.currentYear += self.currentMonth > 11 ? 1 : -1;\n            self.currentMonth = (self.currentMonth + 12) % 12;\n            triggerEvent(\"onYearChange\");\n            buildMonthSwitch();\n        }\n        buildDays();\n        triggerEvent(\"onMonthChange\");\n        updateNavigationCurrentMonth();\n    }\n    function clear(triggerChangeEvent = true, toInitial = true) {\n        self.input.value = \"\";\n        if (self.altInput !== undefined)\n            self.altInput.value = \"\";\n        if (self.mobileInput !== undefined)\n            self.mobileInput.value = \"\";\n        self.selectedDates = [];\n        self.latestSelectedDateObj = undefined;\n        if (toInitial === true) {\n            self.currentYear = self._initialDate.getFullYear();\n            self.currentMonth = self._initialDate.getMonth();\n        }\n        if (self.config.enableTime === true) {\n            const { hours, minutes, seconds } = getDefaultHours(self.config);\n            setHours(hours, minutes, seconds);\n        }\n        self.redraw();\n        if (triggerChangeEvent)\n            triggerEvent(\"onChange\");\n    }\n    function close() {\n        self.isOpen = false;\n        if (!self.isMobile) {\n            if (self.calendarContainer !== undefined) {\n                self.calendarContainer.classList.remove(\"open\");\n            }\n            if (self._input !== undefined) {\n                self._input.classList.remove(\"active\");\n            }\n        }\n        triggerEvent(\"onClose\");\n    }\n    function destroy() {\n        if (self.config !== undefined)\n            triggerEvent(\"onDestroy\");\n        for (let i = self._handlers.length; i--;) {\n            self._handlers[i].remove();\n        }\n        self._handlers = [];\n        if (self.mobileInput) {\n            if (self.mobileInput.parentNode)\n                self.mobileInput.parentNode.removeChild(self.mobileInput);\n            self.mobileInput = undefined;\n        }\n        else if (self.calendarContainer && self.calendarContainer.parentNode) {\n            if (self.config.static && self.calendarContainer.parentNode) {\n                const wrapper = self.calendarContainer.parentNode;\n                wrapper.lastChild && wrapper.removeChild(wrapper.lastChild);\n                if (wrapper.parentNode) {\n                    while (wrapper.firstChild)\n                        wrapper.parentNode.insertBefore(wrapper.firstChild, wrapper);\n                    wrapper.parentNode.removeChild(wrapper);\n                }\n            }\n            else\n                self.calendarContainer.parentNode.removeChild(self.calendarContainer);\n        }\n        if (self.altInput) {\n            self.input.type = \"text\";\n            if (self.altInput.parentNode)\n                self.altInput.parentNode.removeChild(self.altInput);\n            delete self.altInput;\n        }\n        if (self.input) {\n            self.input.type = self.input._type;\n            self.input.classList.remove(\"flatpickr-input\");\n            self.input.removeAttribute(\"readonly\");\n        }\n        [\n            \"_showTimeInput\",\n            \"latestSelectedDateObj\",\n            \"_hideNextMonthArrow\",\n            \"_hidePrevMonthArrow\",\n            \"__hideNextMonthArrow\",\n            \"__hidePrevMonthArrow\",\n            \"isMobile\",\n            \"isOpen\",\n            \"selectedDateElem\",\n            \"minDateHasTime\",\n            \"maxDateHasTime\",\n            \"days\",\n            \"daysContainer\",\n            \"_input\",\n            \"_positionElement\",\n            \"innerContainer\",\n            \"rContainer\",\n            \"monthNav\",\n            \"todayDateElem\",\n            \"calendarContainer\",\n            \"weekdayContainer\",\n            \"prevMonthNav\",\n            \"nextMonthNav\",\n            \"monthsDropdownContainer\",\n            \"currentMonthElement\",\n            \"currentYearElement\",\n            \"navigationCurrentMonth\",\n            \"selectedDateElem\",\n            \"config\",\n        ].forEach((k) => {\n            try {\n                delete self[k];\n            }\n            catch (_) { }\n        });\n    }\n    function isCalendarElem(elem) {\n        if (self.config.appendTo && self.config.appendTo.contains(elem))\n            return true;\n        return self.calendarContainer.contains(elem);\n    }\n    function documentClick(e) {\n        if (self.isOpen && !self.config.inline) {\n            const eventTarget = getEventTarget(e);\n            const isCalendarElement = isCalendarElem(eventTarget);\n            const isInput = eventTarget === self.input ||\n                eventTarget === self.altInput ||\n                self.element.contains(eventTarget) ||\n                (e.path &&\n                    e.path.indexOf &&\n                    (~e.path.indexOf(self.input) ||\n                        ~e.path.indexOf(self.altInput)));\n            const lostFocus = e.type === \"blur\"\n                ? isInput &&\n                    e.relatedTarget &&\n                    !isCalendarElem(e.relatedTarget)\n                : !isInput &&\n                    !isCalendarElement &&\n                    !isCalendarElem(e.relatedTarget);\n            const isIgnored = !self.config.ignoredFocusElements.some((elem) => elem.contains(eventTarget));\n            if (lostFocus && isIgnored) {\n                if (self.timeContainer !== undefined &&\n                    self.minuteElement !== undefined &&\n                    self.hourElement !== undefined &&\n                    self.input.value !== \"\" &&\n                    self.input.value !== undefined) {\n                    updateTime();\n                }\n                self.close();\n                if (self.config &&\n                    self.config.mode === \"range\" &&\n                    self.selectedDates.length === 1) {\n                    self.clear(false);\n                    self.redraw();\n                }\n            }\n        }\n    }\n    function changeYear(newYear) {\n        if (!newYear ||\n            (self.config.minDate && newYear < self.config.minDate.getFullYear()) ||\n            (self.config.maxDate && newYear > self.config.maxDate.getFullYear()))\n            return;\n        const newYearNum = newYear, isNewYear = self.currentYear !== newYearNum;\n        self.currentYear = newYearNum || self.currentYear;\n        if (self.config.maxDate &&\n            self.currentYear === self.config.maxDate.getFullYear()) {\n            self.currentMonth = Math.min(self.config.maxDate.getMonth(), self.currentMonth);\n        }\n        else if (self.config.minDate &&\n            self.currentYear === self.config.minDate.getFullYear()) {\n            self.currentMonth = Math.max(self.config.minDate.getMonth(), self.currentMonth);\n        }\n        if (isNewYear) {\n            self.redraw();\n            triggerEvent(\"onYearChange\");\n            buildMonthSwitch();\n        }\n    }\n    function isEnabled(date, timeless = true) {\n        var _a;\n        const dateToCheck = self.parseDate(date, undefined, timeless);\n        if ((self.config.minDate &&\n            dateToCheck &&\n            compareDates(dateToCheck, self.config.minDate, timeless !== undefined ? timeless : !self.minDateHasTime) < 0) ||\n            (self.config.maxDate &&\n                dateToCheck &&\n                compareDates(dateToCheck, self.config.maxDate, timeless !== undefined ? timeless : !self.maxDateHasTime) > 0))\n            return false;\n        if (!self.config.enable && self.config.disable.length === 0)\n            return true;\n        if (dateToCheck === undefined)\n            return false;\n        const bool = !!self.config.enable, array = (_a = self.config.enable) !== null && _a !== void 0 ? _a : self.config.disable;\n        for (let i = 0, d; i < array.length; i++) {\n            d = array[i];\n            if (typeof d === \"function\" &&\n                d(dateToCheck))\n                return bool;\n            else if (d instanceof Date &&\n                dateToCheck !== undefined &&\n                d.getTime() === dateToCheck.getTime())\n                return bool;\n            else if (typeof d === \"string\") {\n                const parsed = self.parseDate(d, undefined, true);\n                return parsed && parsed.getTime() === dateToCheck.getTime()\n                    ? bool\n                    : !bool;\n            }\n            else if (typeof d === \"object\" &&\n                dateToCheck !== undefined &&\n                d.from &&\n                d.to &&\n                dateToCheck.getTime() >= d.from.getTime() &&\n                dateToCheck.getTime() <= d.to.getTime())\n                return bool;\n        }\n        return !bool;\n    }\n    function isInView(elem) {\n        if (self.daysContainer !== undefined)\n            return (elem.className.indexOf(\"hidden\") === -1 &&\n                elem.className.indexOf(\"flatpickr-disabled\") === -1 &&\n                self.daysContainer.contains(elem));\n        return false;\n    }\n    function onBlur(e) {\n        const isInput = e.target === self._input;\n        if (isInput &&\n            (self.selectedDates.length > 0 || self._input.value.length > 0) &&\n            !(e.relatedTarget && isCalendarElem(e.relatedTarget))) {\n            self.setDate(self._input.value, true, e.target === self.altInput\n                ? self.config.altFormat\n                : self.config.dateFormat);\n        }\n    }\n    function onKeyDown(e) {\n        const eventTarget = getEventTarget(e);\n        const isInput = self.config.wrap\n            ? element.contains(eventTarget)\n            : eventTarget === self._input;\n        const allowInput = self.config.allowInput;\n        const allowKeydown = self.isOpen && (!allowInput || !isInput);\n        const allowInlineKeydown = self.config.inline && isInput && !allowInput;\n        if (e.keyCode === 13 && isInput) {\n            if (allowInput) {\n                self.setDate(self._input.value, true, eventTarget === self.altInput\n                    ? self.config.altFormat\n                    : self.config.dateFormat);\n                return eventTarget.blur();\n            }\n            else {\n                self.open();\n            }\n        }\n        else if (isCalendarElem(eventTarget) ||\n            allowKeydown ||\n            allowInlineKeydown) {\n            const isTimeObj = !!self.timeContainer &&\n                self.timeContainer.contains(eventTarget);\n            switch (e.keyCode) {\n                case 13:\n                    if (isTimeObj) {\n                        e.preventDefault();\n                        updateTime();\n                        focusAndClose();\n                    }\n                    else\n                        selectDate(e);\n                    break;\n                case 27:\n                    e.preventDefault();\n                    focusAndClose();\n                    break;\n                case 8:\n                case 46:\n                    if (isInput && !self.config.allowInput) {\n                        e.preventDefault();\n                        self.clear();\n                    }\n                    break;\n                case 37:\n                case 39:\n                    if (!isTimeObj && !isInput) {\n                        e.preventDefault();\n                        if (self.daysContainer !== undefined &&\n                            (allowInput === false ||\n                                (document.activeElement && isInView(document.activeElement)))) {\n                            const delta = e.keyCode === 39 ? 1 : -1;\n                            if (!e.ctrlKey)\n                                focusOnDay(undefined, delta);\n                            else {\n                                e.stopPropagation();\n                                changeMonth(delta);\n                                focusOnDay(getFirstAvailableDay(1), 0);\n                            }\n                        }\n                    }\n                    else if (self.hourElement)\n                        self.hourElement.focus();\n                    break;\n                case 38:\n                case 40:\n                    e.preventDefault();\n                    const delta = e.keyCode === 40 ? 1 : -1;\n                    if ((self.daysContainer &&\n                        eventTarget.$i !== undefined) ||\n                        eventTarget === self.input ||\n                        eventTarget === self.altInput) {\n                        if (e.ctrlKey) {\n                            e.stopPropagation();\n                            changeYear(self.currentYear - delta);\n                            focusOnDay(getFirstAvailableDay(1), 0);\n                        }\n                        else if (!isTimeObj)\n                            focusOnDay(undefined, delta * 7);\n                    }\n                    else if (eventTarget === self.currentYearElement) {\n                        changeYear(self.currentYear - delta);\n                    }\n                    else if (self.config.enableTime) {\n                        if (!isTimeObj && self.hourElement)\n                            self.hourElement.focus();\n                        updateTime(e);\n                        self._debouncedChange();\n                    }\n                    break;\n                case 9:\n                    if (isTimeObj) {\n                        const elems = [\n                            self.hourElement,\n                            self.minuteElement,\n                            self.secondElement,\n                            self.amPM,\n                        ]\n                            .concat(self.pluginElements)\n                            .filter((x) => x);\n                        const i = elems.indexOf(eventTarget);\n                        if (i !== -1) {\n                            const target = elems[i + (e.shiftKey ? -1 : 1)];\n                            e.preventDefault();\n                            (target || self._input).focus();\n                        }\n                    }\n                    else if (!self.config.noCalendar &&\n                        self.daysContainer &&\n                        self.daysContainer.contains(eventTarget) &&\n                        e.shiftKey) {\n                        e.preventDefault();\n                        self._input.focus();\n                    }\n                    break;\n                default:\n                    break;\n            }\n        }\n        if (self.amPM !== undefined && eventTarget === self.amPM) {\n            switch (e.key) {\n                case self.l10n.amPM[0].charAt(0):\n                case self.l10n.amPM[0].charAt(0).toLowerCase():\n                    self.amPM.textContent = self.l10n.amPM[0];\n                    setHoursFromInputs();\n                    updateValue();\n                    break;\n                case self.l10n.amPM[1].charAt(0):\n                case self.l10n.amPM[1].charAt(0).toLowerCase():\n                    self.amPM.textContent = self.l10n.amPM[1];\n                    setHoursFromInputs();\n                    updateValue();\n                    break;\n            }\n        }\n        if (isInput || isCalendarElem(eventTarget)) {\n            triggerEvent(\"onKeyDown\", e);\n        }\n    }\n    function onMouseOver(elem) {\n        if (self.selectedDates.length !== 1 ||\n            (elem &&\n                (!elem.classList.contains(\"flatpickr-day\") ||\n                    elem.classList.contains(\"flatpickr-disabled\"))))\n            return;\n        const hoverDate = elem\n            ? elem.dateObj.getTime()\n            : self.days.firstElementChild.dateObj.getTime(), initialDate = self.parseDate(self.selectedDates[0], undefined, true).getTime(), rangeStartDate = Math.min(hoverDate, self.selectedDates[0].getTime()), rangeEndDate = Math.max(hoverDate, self.selectedDates[0].getTime());\n        let containsDisabled = false;\n        let minRange = 0, maxRange = 0;\n        for (let t = rangeStartDate; t < rangeEndDate; t += duration.DAY) {\n            if (!isEnabled(new Date(t), true)) {\n                containsDisabled =\n                    containsDisabled || (t > rangeStartDate && t < rangeEndDate);\n                if (t < initialDate && (!minRange || t > minRange))\n                    minRange = t;\n                else if (t > initialDate && (!maxRange || t < maxRange))\n                    maxRange = t;\n            }\n        }\n        for (let m = 0; m < self.config.showMonths; m++) {\n            const month = self.daysContainer.children[m];\n            for (let i = 0, l = month.children.length; i < l; i++) {\n                const dayElem = month.children[i], date = dayElem.dateObj;\n                const timestamp = date.getTime();\n                const outOfRange = (minRange > 0 && timestamp < minRange) ||\n                    (maxRange > 0 && timestamp > maxRange);\n                if (outOfRange) {\n                    dayElem.classList.add(\"notAllowed\");\n                    [\"inRange\", \"startRange\", \"endRange\"].forEach((c) => {\n                        dayElem.classList.remove(c);\n                    });\n                    continue;\n                }\n                else if (containsDisabled && !outOfRange)\n                    continue;\n                [\"startRange\", \"inRange\", \"endRange\", \"notAllowed\"].forEach((c) => {\n                    dayElem.classList.remove(c);\n                });\n                if (elem !== undefined) {\n                    elem.classList.add(hoverDate <= self.selectedDates[0].getTime()\n                        ? \"startRange\"\n                        : \"endRange\");\n                    if (initialDate < hoverDate && timestamp === initialDate)\n                        dayElem.classList.add(\"startRange\");\n                    else if (initialDate > hoverDate && timestamp === initialDate)\n                        dayElem.classList.add(\"endRange\");\n                    if (timestamp >= minRange &&\n                        (maxRange === 0 || timestamp <= maxRange) &&\n                        isBetween(timestamp, initialDate, hoverDate))\n                        dayElem.classList.add(\"inRange\");\n                }\n            }\n        }\n    }\n    function onResize() {\n        if (self.isOpen && !self.config.static && !self.config.inline)\n            positionCalendar();\n    }\n    function open(e, positionElement = self._positionElement) {\n        if (self.isMobile === true) {\n            if (e) {\n                e.preventDefault();\n                const eventTarget = getEventTarget(e);\n                if (eventTarget) {\n                    eventTarget.blur();\n                }\n            }\n            if (self.mobileInput !== undefined) {\n                self.mobileInput.focus();\n                self.mobileInput.click();\n            }\n            triggerEvent(\"onOpen\");\n            return;\n        }\n        else if (self._input.disabled || self.config.inline) {\n            return;\n        }\n        const wasOpen = self.isOpen;\n        self.isOpen = true;\n        if (!wasOpen) {\n            self.calendarContainer.classList.add(\"open\");\n            self._input.classList.add(\"active\");\n            triggerEvent(\"onOpen\");\n            positionCalendar(positionElement);\n        }\n        if (self.config.enableTime === true && self.config.noCalendar === true) {\n            if (self.config.allowInput === false &&\n                (e === undefined ||\n                    !self.timeContainer.contains(e.relatedTarget))) {\n                setTimeout(() => self.hourElement.select(), 50);\n            }\n        }\n    }\n    function minMaxDateSetter(type) {\n        return (date) => {\n            const dateObj = (self.config[`_${type}Date`] = self.parseDate(date, self.config.dateFormat));\n            const inverseDateObj = self.config[`_${type === \"min\" ? \"max\" : \"min\"}Date`];\n            if (dateObj !== undefined) {\n                self[type === \"min\" ? \"minDateHasTime\" : \"maxDateHasTime\"] =\n                    dateObj.getHours() > 0 ||\n                        dateObj.getMinutes() > 0 ||\n                        dateObj.getSeconds() > 0;\n            }\n            if (self.selectedDates) {\n                self.selectedDates = self.selectedDates.filter((d) => isEnabled(d));\n                if (!self.selectedDates.length && type === \"min\")\n                    setHoursFromDate(dateObj);\n                updateValue();\n            }\n            if (self.daysContainer) {\n                redraw();\n                if (dateObj !== undefined)\n                    self.currentYearElement[type] = dateObj.getFullYear().toString();\n                else\n                    self.currentYearElement.removeAttribute(type);\n                self.currentYearElement.disabled =\n                    !!inverseDateObj &&\n                        dateObj !== undefined &&\n                        inverseDateObj.getFullYear() === dateObj.getFullYear();\n            }\n        };\n    }\n    function parseConfig() {\n        const boolOpts = [\n            \"wrap\",\n            \"weekNumbers\",\n            \"allowInput\",\n            \"allowInvalidPreload\",\n            \"clickOpens\",\n            \"time_24hr\",\n            \"enableTime\",\n            \"noCalendar\",\n            \"altInput\",\n            \"shorthandCurrentMonth\",\n            \"inline\",\n            \"static\",\n            \"enableSeconds\",\n            \"disableMobile\",\n        ];\n        const userConfig = Object.assign(Object.assign({}, JSON.parse(JSON.stringify(element.dataset || {}))), instanceConfig);\n        const formats = {};\n        self.config.parseDate = userConfig.parseDate;\n        self.config.formatDate = userConfig.formatDate;\n        Object.defineProperty(self.config, \"enable\", {\n            get: () => self.config._enable,\n            set: (dates) => {\n                self.config._enable = parseDateRules(dates);\n            },\n        });\n        Object.defineProperty(self.config, \"disable\", {\n            get: () => self.config._disable,\n            set: (dates) => {\n                self.config._disable = parseDateRules(dates);\n            },\n        });\n        const timeMode = userConfig.mode === \"time\";\n        if (!userConfig.dateFormat && (userConfig.enableTime || timeMode)) {\n            const defaultDateFormat = flatpickr.defaultConfig.dateFormat || defaultOptions.dateFormat;\n            formats.dateFormat =\n                userConfig.noCalendar || timeMode\n                    ? \"H:i\" + (userConfig.enableSeconds ? \":S\" : \"\")\n                    : defaultDateFormat + \" H:i\" + (userConfig.enableSeconds ? \":S\" : \"\");\n        }\n        if (userConfig.altInput &&\n            (userConfig.enableTime || timeMode) &&\n            !userConfig.altFormat) {\n            const defaultAltFormat = flatpickr.defaultConfig.altFormat || defaultOptions.altFormat;\n            formats.altFormat =\n                userConfig.noCalendar || timeMode\n                    ? \"h:i\" + (userConfig.enableSeconds ? \":S K\" : \" K\")\n                    : defaultAltFormat + ` h:i${userConfig.enableSeconds ? \":S\" : \"\"} K`;\n        }\n        Object.defineProperty(self.config, \"minDate\", {\n            get: () => self.config._minDate,\n            set: minMaxDateSetter(\"min\"),\n        });\n        Object.defineProperty(self.config, \"maxDate\", {\n            get: () => self.config._maxDate,\n            set: minMaxDateSetter(\"max\"),\n        });\n        const minMaxTimeSetter = (type) => (val) => {\n            self.config[type === \"min\" ? \"_minTime\" : \"_maxTime\"] = self.parseDate(val, \"H:i:S\");\n        };\n        Object.defineProperty(self.config, \"minTime\", {\n            get: () => self.config._minTime,\n            set: minMaxTimeSetter(\"min\"),\n        });\n        Object.defineProperty(self.config, \"maxTime\", {\n            get: () => self.config._maxTime,\n            set: minMaxTimeSetter(\"max\"),\n        });\n        if (userConfig.mode === \"time\") {\n            self.config.noCalendar = true;\n            self.config.enableTime = true;\n        }\n        Object.assign(self.config, formats, userConfig);\n        for (let i = 0; i < boolOpts.length; i++)\n            self.config[boolOpts[i]] =\n                self.config[boolOpts[i]] === true ||\n                    self.config[boolOpts[i]] === \"true\";\n        HOOKS.filter((hook) => self.config[hook] !== undefined).forEach((hook) => {\n            self.config[hook] = arrayify(self.config[hook] || []).map(bindToInstance);\n        });\n        self.isMobile =\n            !self.config.disableMobile &&\n                !self.config.inline &&\n                self.config.mode === \"single\" &&\n                !self.config.disable.length &&\n                !self.config.enable &&\n                !self.config.weekNumbers &&\n                /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n        for (let i = 0; i < self.config.plugins.length; i++) {\n            const pluginConf = self.config.plugins[i](self) || {};\n            for (const key in pluginConf) {\n                if (HOOKS.indexOf(key) > -1) {\n                    self.config[key] = arrayify(pluginConf[key])\n                        .map(bindToInstance)\n                        .concat(self.config[key]);\n                }\n                else if (typeof userConfig[key] === \"undefined\")\n                    self.config[key] = pluginConf[key];\n            }\n        }\n        if (!userConfig.altInputClass) {\n            self.config.altInputClass =\n                getInputElem().className + \" \" + self.config.altInputClass;\n        }\n        triggerEvent(\"onParseConfig\");\n    }\n    function getInputElem() {\n        return self.config.wrap\n            ? element.querySelector(\"[data-input]\")\n            : element;\n    }\n    function setupLocale() {\n        if (typeof self.config.locale !== \"object\" &&\n            typeof flatpickr.l10ns[self.config.locale] === \"undefined\")\n            self.config.errorHandler(new Error(`flatpickr: invalid locale ${self.config.locale}`));\n        self.l10n = Object.assign(Object.assign({}, flatpickr.l10ns.default), (typeof self.config.locale === \"object\"\n            ? self.config.locale\n            : self.config.locale !== \"default\"\n                ? flatpickr.l10ns[self.config.locale]\n                : undefined));\n        tokenRegex.K = `(${self.l10n.amPM[0]}|${self.l10n.amPM[1]}|${self.l10n.amPM[0].toLowerCase()}|${self.l10n.amPM[1].toLowerCase()})`;\n        const userConfig = Object.assign(Object.assign({}, instanceConfig), JSON.parse(JSON.stringify(element.dataset || {})));\n        if (userConfig.time_24hr === undefined &&\n            flatpickr.defaultConfig.time_24hr === undefined) {\n            self.config.time_24hr = self.l10n.time_24hr;\n        }\n        self.formatDate = createDateFormatter(self);\n        self.parseDate = createDateParser({ config: self.config, l10n: self.l10n });\n    }\n    function positionCalendar(customPositionElement) {\n        if (typeof self.config.position === \"function\") {\n            return void self.config.position(self, customPositionElement);\n        }\n        if (self.calendarContainer === undefined)\n            return;\n        triggerEvent(\"onPreCalendarPosition\");\n        const positionElement = customPositionElement || self._positionElement;\n        const calendarHeight = Array.prototype.reduce.call(self.calendarContainer.children, ((acc, child) => acc + child.offsetHeight), 0), calendarWidth = self.calendarContainer.offsetWidth, configPos = self.config.position.split(\" \"), configPosVertical = configPos[0], configPosHorizontal = configPos.length > 1 ? configPos[1] : null, inputBounds = positionElement.getBoundingClientRect(), distanceFromBottom = window.innerHeight - inputBounds.bottom, showOnTop = configPosVertical === \"above\" ||\n            (configPosVertical !== \"below\" &&\n                distanceFromBottom < calendarHeight &&\n                inputBounds.top > calendarHeight);\n        const top = window.pageYOffset +\n            inputBounds.top +\n            (!showOnTop ? positionElement.offsetHeight + 2 : -calendarHeight - 2);\n        toggleClass(self.calendarContainer, \"arrowTop\", !showOnTop);\n        toggleClass(self.calendarContainer, \"arrowBottom\", showOnTop);\n        if (self.config.inline)\n            return;\n        let left = window.pageXOffset + inputBounds.left;\n        let isCenter = false;\n        let isRight = false;\n        if (configPosHorizontal === \"center\") {\n            left -= (calendarWidth - inputBounds.width) / 2;\n            isCenter = true;\n        }\n        else if (configPosHorizontal === \"right\") {\n            left -= calendarWidth - inputBounds.width;\n            isRight = true;\n        }\n        toggleClass(self.calendarContainer, \"arrowLeft\", !isCenter && !isRight);\n        toggleClass(self.calendarContainer, \"arrowCenter\", isCenter);\n        toggleClass(self.calendarContainer, \"arrowRight\", isRight);\n        const right = window.document.body.offsetWidth -\n            (window.pageXOffset + inputBounds.right);\n        const rightMost = left + calendarWidth > window.document.body.offsetWidth;\n        const centerMost = right + calendarWidth > window.document.body.offsetWidth;\n        toggleClass(self.calendarContainer, \"rightMost\", rightMost);\n        if (self.config.static)\n            return;\n        self.calendarContainer.style.top = `${top}px`;\n        if (!rightMost) {\n            self.calendarContainer.style.left = `${left}px`;\n            self.calendarContainer.style.right = \"auto\";\n        }\n        else if (!centerMost) {\n            self.calendarContainer.style.left = \"auto\";\n            self.calendarContainer.style.right = `${right}px`;\n        }\n        else {\n            const doc = getDocumentStyleSheet();\n            if (doc === undefined)\n                return;\n            const bodyWidth = window.document.body.offsetWidth;\n            const centerLeft = Math.max(0, bodyWidth / 2 - calendarWidth / 2);\n            const centerBefore = \".flatpickr-calendar.centerMost:before\";\n            const centerAfter = \".flatpickr-calendar.centerMost:after\";\n            const centerIndex = doc.cssRules.length;\n            const centerStyle = `{left:${inputBounds.left}px;right:auto;}`;\n            toggleClass(self.calendarContainer, \"rightMost\", false);\n            toggleClass(self.calendarContainer, \"centerMost\", true);\n            doc.insertRule(`${centerBefore},${centerAfter}${centerStyle}`, centerIndex);\n            self.calendarContainer.style.left = `${centerLeft}px`;\n            self.calendarContainer.style.right = \"auto\";\n        }\n    }\n    function getDocumentStyleSheet() {\n        let editableSheet = null;\n        for (let i = 0; i < document.styleSheets.length; i++) {\n            const sheet = document.styleSheets[i];\n            try {\n                sheet.cssRules;\n            }\n            catch (err) {\n                continue;\n            }\n            editableSheet = sheet;\n            break;\n        }\n        return editableSheet != null ? editableSheet : createStyleSheet();\n    }\n    function createStyleSheet() {\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        return style.sheet;\n    }\n    function redraw() {\n        if (self.config.noCalendar || self.isMobile)\n            return;\n        buildMonthSwitch();\n        updateNavigationCurrentMonth();\n        buildDays();\n    }\n    function focusAndClose() {\n        self._input.focus();\n        if (window.navigator.userAgent.indexOf(\"MSIE\") !== -1 ||\n            navigator.msMaxTouchPoints !== undefined) {\n            setTimeout(self.close, 0);\n        }\n        else {\n            self.close();\n        }\n    }\n    function selectDate(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        const isSelectable = (day) => day.classList &&\n            day.classList.contains(\"flatpickr-day\") &&\n            !day.classList.contains(\"flatpickr-disabled\") &&\n            !day.classList.contains(\"notAllowed\");\n        const t = findParent(getEventTarget(e), isSelectable);\n        if (t === undefined)\n            return;\n        const target = t;\n        const selectedDate = (self.latestSelectedDateObj = new Date(target.dateObj.getTime()));\n        const shouldChangeMonth = (selectedDate.getMonth() < self.currentMonth ||\n            selectedDate.getMonth() >\n                self.currentMonth + self.config.showMonths - 1) &&\n            self.config.mode !== \"range\";\n        self.selectedDateElem = target;\n        if (self.config.mode === \"single\")\n            self.selectedDates = [selectedDate];\n        else if (self.config.mode === \"multiple\") {\n            const selectedIndex = isDateSelected(selectedDate);\n            if (selectedIndex)\n                self.selectedDates.splice(parseInt(selectedIndex), 1);\n            else\n                self.selectedDates.push(selectedDate);\n        }\n        else if (self.config.mode === \"range\") {\n            if (self.selectedDates.length === 2) {\n                self.clear(false, false);\n            }\n            self.latestSelectedDateObj = selectedDate;\n            self.selectedDates.push(selectedDate);\n            if (compareDates(selectedDate, self.selectedDates[0], true) !== 0)\n                self.selectedDates.sort((a, b) => a.getTime() - b.getTime());\n        }\n        setHoursFromInputs();\n        if (shouldChangeMonth) {\n            const isNewYear = self.currentYear !== selectedDate.getFullYear();\n            self.currentYear = selectedDate.getFullYear();\n            self.currentMonth = selectedDate.getMonth();\n            if (isNewYear) {\n                triggerEvent(\"onYearChange\");\n                buildMonthSwitch();\n            }\n            triggerEvent(\"onMonthChange\");\n        }\n        updateNavigationCurrentMonth();\n        buildDays();\n        updateValue();\n        if (!shouldChangeMonth &&\n            self.config.mode !== \"range\" &&\n            self.config.showMonths === 1)\n            focusOnDayElem(target);\n        else if (self.selectedDateElem !== undefined &&\n            self.hourElement === undefined) {\n            self.selectedDateElem && self.selectedDateElem.focus();\n        }\n        if (self.hourElement !== undefined)\n            self.hourElement !== undefined && self.hourElement.focus();\n        if (self.config.closeOnSelect) {\n            const single = self.config.mode === \"single\" && !self.config.enableTime;\n            const range = self.config.mode === \"range\" &&\n                self.selectedDates.length === 2 &&\n                !self.config.enableTime;\n            if (single || range) {\n                focusAndClose();\n            }\n        }\n        triggerChange();\n    }\n    const CALLBACKS = {\n        locale: [setupLocale, updateWeekdays],\n        showMonths: [buildMonths, setCalendarWidth, buildWeekdays],\n        minDate: [jumpToDate],\n        maxDate: [jumpToDate],\n        clickOpens: [\n            () => {\n                if (self.config.clickOpens === true) {\n                    bind(self._input, \"focus\", self.open);\n                    bind(self._input, \"click\", self.open);\n                }\n                else {\n                    self._input.removeEventListener(\"focus\", self.open);\n                    self._input.removeEventListener(\"click\", self.open);\n                }\n            },\n        ],\n    };\n    function set(option, value) {\n        if (option !== null && typeof option === \"object\") {\n            Object.assign(self.config, option);\n            for (const key in option) {\n                if (CALLBACKS[key] !== undefined)\n                    CALLBACKS[key].forEach((x) => x());\n            }\n        }\n        else {\n            self.config[option] = value;\n            if (CALLBACKS[option] !== undefined)\n                CALLBACKS[option].forEach((x) => x());\n            else if (HOOKS.indexOf(option) > -1)\n                self.config[option] = arrayify(value);\n        }\n        self.redraw();\n        updateValue(true);\n    }\n    function setSelectedDate(inputDate, format) {\n        let dates = [];\n        if (inputDate instanceof Array)\n            dates = inputDate.map((d) => self.parseDate(d, format));\n        else if (inputDate instanceof Date || typeof inputDate === \"number\")\n            dates = [self.parseDate(inputDate, format)];\n        else if (typeof inputDate === \"string\") {\n            switch (self.config.mode) {\n                case \"single\":\n                case \"time\":\n                    dates = [self.parseDate(inputDate, format)];\n                    break;\n                case \"multiple\":\n                    dates = inputDate\n                        .split(self.config.conjunction)\n                        .map((date) => self.parseDate(date, format));\n                    break;\n                case \"range\":\n                    dates = inputDate\n                        .split(self.l10n.rangeSeparator)\n                        .map((date) => self.parseDate(date, format));\n                    break;\n                default:\n                    break;\n            }\n        }\n        else\n            self.config.errorHandler(new Error(`Invalid date supplied: ${JSON.stringify(inputDate)}`));\n        self.selectedDates = (self.config.allowInvalidPreload\n            ? dates\n            : dates.filter((d) => d instanceof Date && isEnabled(d, false)));\n        if (self.config.mode === \"range\")\n            self.selectedDates.sort((a, b) => a.getTime() - b.getTime());\n    }\n    function setDate(date, triggerChange = false, format = self.config.dateFormat) {\n        if ((date !== 0 && !date) || (date instanceof Array && date.length === 0))\n            return self.clear(triggerChange);\n        setSelectedDate(date, format);\n        self.latestSelectedDateObj =\n            self.selectedDates[self.selectedDates.length - 1];\n        self.redraw();\n        jumpToDate(undefined, triggerChange);\n        setHoursFromDate();\n        if (self.selectedDates.length === 0) {\n            self.clear(false);\n        }\n        updateValue(triggerChange);\n        if (triggerChange)\n            triggerEvent(\"onChange\");\n    }\n    function parseDateRules(arr) {\n        return arr\n            .slice()\n            .map((rule) => {\n            if (typeof rule === \"string\" ||\n                typeof rule === \"number\" ||\n                rule instanceof Date) {\n                return self.parseDate(rule, undefined, true);\n            }\n            else if (rule &&\n                typeof rule === \"object\" &&\n                rule.from &&\n                rule.to)\n                return {\n                    from: self.parseDate(rule.from, undefined),\n                    to: self.parseDate(rule.to, undefined),\n                };\n            return rule;\n        })\n            .filter((x) => x);\n    }\n    function setupDates() {\n        self.selectedDates = [];\n        self.now = self.parseDate(self.config.now) || new Date();\n        const preloadedDate = self.config.defaultDate ||\n            ((self.input.nodeName === \"INPUT\" ||\n                self.input.nodeName === \"TEXTAREA\") &&\n                self.input.placeholder &&\n                self.input.value === self.input.placeholder\n                ? null\n                : self.input.value);\n        if (preloadedDate)\n            setSelectedDate(preloadedDate, self.config.dateFormat);\n        self._initialDate =\n            self.selectedDates.length > 0\n                ? self.selectedDates[0]\n                : self.config.minDate &&\n                    self.config.minDate.getTime() > self.now.getTime()\n                    ? self.config.minDate\n                    : self.config.maxDate &&\n                        self.config.maxDate.getTime() < self.now.getTime()\n                        ? self.config.maxDate\n                        : self.now;\n        self.currentYear = self._initialDate.getFullYear();\n        self.currentMonth = self._initialDate.getMonth();\n        if (self.selectedDates.length > 0)\n            self.latestSelectedDateObj = self.selectedDates[0];\n        if (self.config.minTime !== undefined)\n            self.config.minTime = self.parseDate(self.config.minTime, \"H:i\");\n        if (self.config.maxTime !== undefined)\n            self.config.maxTime = self.parseDate(self.config.maxTime, \"H:i\");\n        self.minDateHasTime =\n            !!self.config.minDate &&\n                (self.config.minDate.getHours() > 0 ||\n                    self.config.minDate.getMinutes() > 0 ||\n                    self.config.minDate.getSeconds() > 0);\n        self.maxDateHasTime =\n            !!self.config.maxDate &&\n                (self.config.maxDate.getHours() > 0 ||\n                    self.config.maxDate.getMinutes() > 0 ||\n                    self.config.maxDate.getSeconds() > 0);\n    }\n    function setupInputs() {\n        self.input = getInputElem();\n        if (!self.input) {\n            self.config.errorHandler(new Error(\"Invalid input element specified\"));\n            return;\n        }\n        self.input._type = self.input.type;\n        self.input.type = \"text\";\n        self.input.classList.add(\"flatpickr-input\");\n        self._input = self.input;\n        if (self.config.altInput) {\n            self.altInput = createElement(self.input.nodeName, self.config.altInputClass);\n            self._input = self.altInput;\n            self.altInput.placeholder = self.input.placeholder;\n            self.altInput.disabled = self.input.disabled;\n            self.altInput.required = self.input.required;\n            self.altInput.tabIndex = self.input.tabIndex;\n            self.altInput.type = \"text\";\n            self.input.setAttribute(\"type\", \"hidden\");\n            if (!self.config.static && self.input.parentNode)\n                self.input.parentNode.insertBefore(self.altInput, self.input.nextSibling);\n        }\n        if (!self.config.allowInput)\n            self._input.setAttribute(\"readonly\", \"readonly\");\n        self._positionElement = self.config.positionElement || self._input;\n    }\n    function setupMobile() {\n        const inputType = self.config.enableTime\n            ? self.config.noCalendar\n                ? \"time\"\n                : \"datetime-local\"\n            : \"date\";\n        self.mobileInput = createElement(\"input\", self.input.className + \" flatpickr-mobile\");\n        self.mobileInput.tabIndex = 1;\n        self.mobileInput.type = inputType;\n        self.mobileInput.disabled = self.input.disabled;\n        self.mobileInput.required = self.input.required;\n        self.mobileInput.placeholder = self.input.placeholder;\n        self.mobileFormatStr =\n            inputType === \"datetime-local\"\n                ? \"Y-m-d\\\\TH:i:S\"\n                : inputType === \"date\"\n                    ? \"Y-m-d\"\n                    : \"H:i:S\";\n        if (self.selectedDates.length > 0) {\n            self.mobileInput.defaultValue = self.mobileInput.value = self.formatDate(self.selectedDates[0], self.mobileFormatStr);\n        }\n        if (self.config.minDate)\n            self.mobileInput.min = self.formatDate(self.config.minDate, \"Y-m-d\");\n        if (self.config.maxDate)\n            self.mobileInput.max = self.formatDate(self.config.maxDate, \"Y-m-d\");\n        if (self.input.getAttribute(\"step\"))\n            self.mobileInput.step = String(self.input.getAttribute(\"step\"));\n        self.input.type = \"hidden\";\n        if (self.altInput !== undefined)\n            self.altInput.type = \"hidden\";\n        try {\n            if (self.input.parentNode)\n                self.input.parentNode.insertBefore(self.mobileInput, self.input.nextSibling);\n        }\n        catch (_a) { }\n        bind(self.mobileInput, \"change\", (e) => {\n            self.setDate(getEventTarget(e).value, false, self.mobileFormatStr);\n            triggerEvent(\"onChange\");\n            triggerEvent(\"onClose\");\n        });\n    }\n    function toggle(e) {\n        if (self.isOpen === true)\n            return self.close();\n        self.open(e);\n    }\n    function triggerEvent(event, data) {\n        if (self.config === undefined)\n            return;\n        const hooks = self.config[event];\n        if (hooks !== undefined && hooks.length > 0) {\n            for (let i = 0; hooks[i] && i < hooks.length; i++)\n                hooks[i](self.selectedDates, self.input.value, self, data);\n        }\n        if (event === \"onChange\") {\n            self.input.dispatchEvent(createEvent(\"change\"));\n            self.input.dispatchEvent(createEvent(\"input\"));\n        }\n    }\n    function createEvent(name) {\n        const e = document.createEvent(\"Event\");\n        e.initEvent(name, true, true);\n        return e;\n    }\n    function isDateSelected(date) {\n        for (let i = 0; i < self.selectedDates.length; i++) {\n            if (compareDates(self.selectedDates[i], date) === 0)\n                return \"\" + i;\n        }\n        return false;\n    }\n    function isDateInRange(date) {\n        if (self.config.mode !== \"range\" || self.selectedDates.length < 2)\n            return false;\n        return (compareDates(date, self.selectedDates[0]) >= 0 &&\n            compareDates(date, self.selectedDates[1]) <= 0);\n    }\n    function updateNavigationCurrentMonth() {\n        if (self.config.noCalendar || self.isMobile || !self.monthNav)\n            return;\n        self.yearElements.forEach((yearElement, i) => {\n            const d = new Date(self.currentYear, self.currentMonth, 1);\n            d.setMonth(self.currentMonth + i);\n            if (self.config.showMonths > 1 ||\n                self.config.monthSelectorType === \"static\") {\n                self.monthElements[i].textContent =\n                    monthToStr(d.getMonth(), self.config.shorthandCurrentMonth, self.l10n) + \" \";\n            }\n            else {\n                self.monthsDropdownContainer.value = d.getMonth().toString();\n            }\n            yearElement.value = d.getFullYear().toString();\n        });\n        self._hidePrevMonthArrow =\n            self.config.minDate !== undefined &&\n                (self.currentYear === self.config.minDate.getFullYear()\n                    ? self.currentMonth <= self.config.minDate.getMonth()\n                    : self.currentYear < self.config.minDate.getFullYear());\n        self._hideNextMonthArrow =\n            self.config.maxDate !== undefined &&\n                (self.currentYear === self.config.maxDate.getFullYear()\n                    ? self.currentMonth + 1 > self.config.maxDate.getMonth()\n                    : self.currentYear > self.config.maxDate.getFullYear());\n    }\n    function getDateStr(format) {\n        return self.selectedDates\n            .map((dObj) => self.formatDate(dObj, format))\n            .filter((d, i, arr) => self.config.mode !== \"range\" ||\n            self.config.enableTime ||\n            arr.indexOf(d) === i)\n            .join(self.config.mode !== \"range\"\n            ? self.config.conjunction\n            : self.l10n.rangeSeparator);\n    }\n    function updateValue(triggerChange = true) {\n        if (self.mobileInput !== undefined && self.mobileFormatStr) {\n            self.mobileInput.value =\n                self.latestSelectedDateObj !== undefined\n                    ? self.formatDate(self.latestSelectedDateObj, self.mobileFormatStr)\n                    : \"\";\n        }\n        self.input.value = getDateStr(self.config.dateFormat);\n        if (self.altInput !== undefined) {\n            self.altInput.value = getDateStr(self.config.altFormat);\n        }\n        if (triggerChange !== false)\n            triggerEvent(\"onValueUpdate\");\n    }\n    function onMonthNavClick(e) {\n        const eventTarget = getEventTarget(e);\n        const isPrevMonth = self.prevMonthNav.contains(eventTarget);\n        const isNextMonth = self.nextMonthNav.contains(eventTarget);\n        if (isPrevMonth || isNextMonth) {\n            changeMonth(isPrevMonth ? -1 : 1);\n        }\n        else if (self.yearElements.indexOf(eventTarget) >= 0) {\n            eventTarget.select();\n        }\n        else if (eventTarget.classList.contains(\"arrowUp\")) {\n            self.changeYear(self.currentYear + 1);\n        }\n        else if (eventTarget.classList.contains(\"arrowDown\")) {\n            self.changeYear(self.currentYear - 1);\n        }\n    }\n    function timeWrapper(e) {\n        e.preventDefault();\n        const isKeyDown = e.type === \"keydown\", eventTarget = getEventTarget(e), input = eventTarget;\n        if (self.amPM !== undefined && eventTarget === self.amPM) {\n            self.amPM.textContent =\n                self.l10n.amPM[int(self.amPM.textContent === self.l10n.amPM[0])];\n        }\n        const min = parseFloat(input.getAttribute(\"min\")), max = parseFloat(input.getAttribute(\"max\")), step = parseFloat(input.getAttribute(\"step\")), curValue = parseInt(input.value, 10), delta = e.delta ||\n            (isKeyDown ? (e.which === 38 ? 1 : -1) : 0);\n        let newValue = curValue + step * delta;\n        if (typeof input.value !== \"undefined\" && input.value.length === 2) {\n            const isHourElem = input === self.hourElement, isMinuteElem = input === self.minuteElement;\n            if (newValue < min) {\n                newValue =\n                    max +\n                        newValue +\n                        int(!isHourElem) +\n                        (int(isHourElem) && int(!self.amPM));\n                if (isMinuteElem)\n                    incrementNumInput(undefined, -1, self.hourElement);\n            }\n            else if (newValue > max) {\n                newValue =\n                    input === self.hourElement ? newValue - max - int(!self.amPM) : min;\n                if (isMinuteElem)\n                    incrementNumInput(undefined, 1, self.hourElement);\n            }\n            if (self.amPM &&\n                isHourElem &&\n                (step === 1\n                    ? newValue + curValue === 23\n                    : Math.abs(newValue - curValue) > step)) {\n                self.amPM.textContent =\n                    self.l10n.amPM[int(self.amPM.textContent === self.l10n.amPM[0])];\n            }\n            input.value = pad(newValue);\n        }\n    }\n    init();\n    return self;\n}\nfunction _flatpickr(nodeList, config) {\n    const nodes = Array.prototype.slice\n        .call(nodeList)\n        .filter((x) => x instanceof HTMLElement);\n    const instances = [];\n    for (let i = 0; i < nodes.length; i++) {\n        const node = nodes[i];\n        try {\n            if (node.getAttribute(\"data-fp-omit\") !== null)\n                continue;\n            if (node._flatpickr !== undefined) {\n                node._flatpickr.destroy();\n                node._flatpickr = undefined;\n            }\n            node._flatpickr = FlatpickrInstance(node, config || {});\n            instances.push(node._flatpickr);\n        }\n        catch (e) {\n            console.error(e);\n        }\n    }\n    return instances.length === 1 ? instances[0] : instances;\n}\nif (typeof HTMLElement !== \"undefined\" &&\n    typeof HTMLCollection !== \"undefined\" &&\n    typeof NodeList !== \"undefined\") {\n    HTMLCollection.prototype.flatpickr = NodeList.prototype.flatpickr = function (config) {\n        return _flatpickr(this, config);\n    };\n    HTMLElement.prototype.flatpickr = function (config) {\n        return _flatpickr([this], config);\n    };\n}\nvar flatpickr = function (selector, config) {\n    if (typeof selector === \"string\") {\n        return _flatpickr(window.document.querySelectorAll(selector), config);\n    }\n    else if (selector instanceof Node) {\n        return _flatpickr([selector], config);\n    }\n    else {\n        return _flatpickr(selector, config);\n    }\n};\nflatpickr.defaultConfig = {};\nflatpickr.l10ns = {\n    en: Object.assign({}, English),\n    default: Object.assign({}, English),\n};\nflatpickr.localize = (l10n) => {\n    flatpickr.l10ns.default = Object.assign(Object.assign({}, flatpickr.l10ns.default), l10n);\n};\nflatpickr.setDefaults = (config) => {\n    flatpickr.defaultConfig = Object.assign(Object.assign({}, flatpickr.defaultConfig), config);\n};\nflatpickr.parseDate = createDateParser({});\nflatpickr.formatDate = createDateFormatter({});\nflatpickr.compareDates = compareDates;\nif (typeof jQuery !== \"undefined\" && typeof jQuery.fn !== \"undefined\") {\n    jQuery.fn.flatpickr = function (config) {\n        return _flatpickr(this, config);\n    };\n}\nDate.prototype.fp_incr = function (days) {\n    return new Date(this.getFullYear(), this.getMonth(), this.getDate() + (typeof days === \"string\" ? parseInt(days, 10) : days));\n};\nif (typeof window !== \"undefined\") {\n    window.flatpickr = flatpickr;\n}\nexport default flatpickr;\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,cAAc,EAAEC,KAAK,QAAS,iBAAiB;AACpE,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,QAAQ,SAAS;AACtD,SAASC,SAAS,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,cAAc,QAAS,aAAa;AACnH,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,QAAS,eAAe;AAC1H,SAASC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3D,OAAO,mBAAmB;AAC1B,MAAMC,mBAAmB,GAAG,GAAG;AAC/B,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,cAAc,EAAE;EAChD,MAAMC,IAAI,GAAG;IACTC,MAAM,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5B,cAAc,CAAC,EAAE6B,SAAS,CAACC,aAAa,CAAC;IACjFC,IAAI,EAAE7B;EACV,CAAC;EACDuB,IAAI,CAACO,SAAS,GAAGlB,gBAAgB,CAAC;IAAEY,MAAM,EAAED,IAAI,CAACC,MAAM;IAAEK,IAAI,EAAEN,IAAI,CAACM;EAAK,CAAC,CAAC;EAC3EN,IAAI,CAACQ,SAAS,GAAG,EAAE;EACnBR,IAAI,CAACS,cAAc,GAAG,EAAE;EACxBT,IAAI,CAACU,aAAa,GAAG,EAAE;EACvBV,IAAI,CAACW,KAAK,GAAGC,IAAI;EACjBZ,IAAI,CAACa,iBAAiB,GAAGC,gBAAgB;EACzCd,IAAI,CAACe,iBAAiB,GAAGC,gBAAgB;EACzChB,IAAI,CAACiB,WAAW,GAAGA,WAAW;EAC9BjB,IAAI,CAACkB,UAAU,GAAGA,UAAU;EAC5BlB,IAAI,CAACmB,KAAK,GAAGA,KAAK;EAClBnB,IAAI,CAACoB,KAAK,GAAGA,KAAK;EAClBpB,IAAI,CAACqB,cAAc,GAAGtC,aAAa;EACnCiB,IAAI,CAACsB,OAAO,GAAGA,OAAO;EACtBtB,IAAI,CAACuB,SAAS,GAAGA,SAAS;EAC1BvB,IAAI,CAACwB,UAAU,GAAGA,UAAU;EAC5BxB,IAAI,CAACyB,IAAI,GAAGA,IAAI;EAChBzB,IAAI,CAAC0B,MAAM,GAAGA,MAAM;EACpB1B,IAAI,CAAC2B,GAAG,GAAGA,GAAG;EACd3B,IAAI,CAAC4B,OAAO,GAAGA,OAAO;EACtB5B,IAAI,CAAC6B,MAAM,GAAGA,MAAM;EACpB,SAASC,oBAAoBA,CAAA,EAAG;IAC5B9B,IAAI,CAAC+B,KAAK,GAAG;MACTC,cAAcA,CAACC,KAAK,GAAGjC,IAAI,CAACkC,YAAY,EAAEC,EAAE,GAAGnC,IAAI,CAACoC,WAAW,EAAE;QAC7D,IAAIH,KAAK,KAAK,CAAC,KAAME,EAAE,GAAG,CAAC,KAAK,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,CAAC,IAAKA,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,EACnE,OAAO,EAAE;QACb,OAAOnC,IAAI,CAACM,IAAI,CAAC+B,WAAW,CAACJ,KAAK,CAAC;MACvC;IACJ,CAAC;EACL;EACA,SAASK,IAAIA,CAAA,EAAG;IACZtC,IAAI,CAACF,OAAO,GAAGE,IAAI,CAACuC,KAAK,GAAGzC,OAAO;IACnCE,IAAI,CAACwC,MAAM,GAAG,KAAK;IACnBC,WAAW,CAAC,CAAC;IACbC,WAAW,CAAC,CAAC;IACbC,WAAW,CAAC,CAAC;IACbC,UAAU,CAAC,CAAC;IACZd,oBAAoB,CAAC,CAAC;IACtB,IAAI,CAAC9B,IAAI,CAAC6C,QAAQ,EACdC,KAAK,CAAC,CAAC;IACXC,UAAU,CAAC,CAAC;IACZ,IAAI/C,IAAI,CAACgD,aAAa,CAACC,MAAM,IAAIjD,IAAI,CAACC,MAAM,CAACiD,UAAU,EAAE;MACrD,IAAIlD,IAAI,CAACC,MAAM,CAACkD,UAAU,EAAE;QACxBrC,gBAAgB,CAACd,IAAI,CAACC,MAAM,CAACiD,UAAU,GAAGlD,IAAI,CAACoD,qBAAqB,GAAGC,SAAS,CAAC;MACrF;MACAC,WAAW,CAAC,KAAK,CAAC;IACtB;IACAC,gBAAgB,CAAC,CAAC;IAClB,MAAMC,QAAQ,GAAG,gCAAgC,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IAC3E,IAAI,CAAC3D,IAAI,CAAC6C,QAAQ,IAAIW,QAAQ,EAAE;MAC5BxC,gBAAgB,CAAC,CAAC;IACtB;IACA4C,YAAY,CAAC,SAAS,CAAC;EAC3B;EACA,SAASC,cAAcA,CAACC,EAAE,EAAE;IACxB,OAAOA,EAAE,CAAClD,IAAI,CAACZ,IAAI,CAAC;EACxB;EACA,SAASuD,gBAAgBA,CAAA,EAAG;IACxB,MAAMtD,MAAM,GAAGD,IAAI,CAACC,MAAM;IAC1B,IAAIA,MAAM,CAAC8D,WAAW,KAAK,KAAK,IAAI9D,MAAM,CAAC+D,UAAU,KAAK,CAAC,EAAE;MACzD;IACJ,CAAC,MACI,IAAI/D,MAAM,CAACiD,UAAU,KAAK,IAAI,EAAE;MACjCe,MAAM,CAACC,qBAAqB,CAAC,YAAY;QACrC,IAAIlE,IAAI,CAACmE,iBAAiB,KAAKd,SAAS,EAAE;UACtCrD,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACC,UAAU,GAAG,QAAQ;UAClDrE,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACE,OAAO,GAAG,OAAO;QAClD;QACA,IAAItE,IAAI,CAACuE,aAAa,KAAKlB,SAAS,EAAE;UAClC,MAAMmB,SAAS,GAAG,CAACxE,IAAI,CAACyE,IAAI,CAACC,WAAW,GAAG,CAAC,IAAIzE,MAAM,CAAC+D,UAAU;UACjEhE,IAAI,CAACuE,aAAa,CAACH,KAAK,CAACO,KAAK,GAAGH,SAAS,GAAG,IAAI;UACjDxE,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACO,KAAK,GAC9BH,SAAS,IACJxE,IAAI,CAAC4E,WAAW,KAAKvB,SAAS,GACzBrD,IAAI,CAAC4E,WAAW,CAACF,WAAW,GAC5B,CAAC,CAAC,GACR,IAAI;UACZ1E,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACS,cAAc,CAAC,YAAY,CAAC;UACzD7E,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACS,cAAc,CAAC,SAAS,CAAC;QAC1D;MACJ,CAAC,CAAC;IACN;EACJ;EACA,SAASC,UAAUA,CAACC,CAAC,EAAE;IACnB,IAAI/E,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MACjC,MAAM+B,WAAW,GAAGhF,IAAI,CAACC,MAAM,CAACgF,OAAO,KAAK5B,SAAS,IACjDjE,YAAY,CAAC,IAAI8F,IAAI,CAAC,CAAC,EAAElF,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC,IAAI,CAAC,GAChD,IAAIC,IAAI,CAAC,CAAC,GACV,IAAIA,IAAI,CAAClF,IAAI,CAACC,MAAM,CAACgF,OAAO,CAACE,OAAO,CAAC,CAAC,CAAC;MAC7C,MAAM7G,QAAQ,GAAGmB,eAAe,CAACO,IAAI,CAACC,MAAM,CAAC;MAC7C+E,WAAW,CAACI,QAAQ,CAAC9G,QAAQ,CAAC+G,KAAK,EAAE/G,QAAQ,CAACgH,OAAO,EAAEhH,QAAQ,CAACiH,OAAO,EAAEP,WAAW,CAACQ,eAAe,CAAC,CAAC,CAAC;MACvGxF,IAAI,CAACgD,aAAa,GAAG,CAACgC,WAAW,CAAC;MAClChF,IAAI,CAACoD,qBAAqB,GAAG4B,WAAW;IAC5C;IACA,IAAID,CAAC,KAAK1B,SAAS,IAAI0B,CAAC,CAACU,IAAI,KAAK,MAAM,EAAE;MACtCC,WAAW,CAACX,CAAC,CAAC;IAClB;IACA,MAAMY,SAAS,GAAG3F,IAAI,CAAC4F,MAAM,CAACC,KAAK;IACnCC,kBAAkB,CAAC,CAAC;IACpBxC,WAAW,CAAC,CAAC;IACb,IAAItD,IAAI,CAAC4F,MAAM,CAACC,KAAK,KAAKF,SAAS,EAAE;MACjC3F,IAAI,CAAC+F,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACA,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAC/B,OAAQD,IAAI,GAAG,EAAE,GAAI,EAAE,GAAGrH,GAAG,CAACsH,IAAI,KAAKlG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7D;EACA,SAASC,aAAaA,CAACF,IAAI,EAAE;IACzB,QAAQA,IAAI,GAAG,EAAE;MACb,KAAK,CAAC;MACN,KAAK,EAAE;QACH,OAAO,EAAE;MACb;QACI,OAAOA,IAAI,GAAG,EAAE;IACxB;EACJ;EACA,SAASH,kBAAkBA,CAAA,EAAG;IAC1B,IAAI9F,IAAI,CAACoG,WAAW,KAAK/C,SAAS,IAAIrD,IAAI,CAACqG,aAAa,KAAKhD,SAAS,EAClE;IACJ,IAAIgC,KAAK,GAAG,CAACiB,QAAQ,CAACtG,IAAI,CAACoG,WAAW,CAACP,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;MAAEjB,OAAO,GAAG,CAACgB,QAAQ,CAACtG,IAAI,CAACqG,aAAa,CAACR,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;MAAEN,OAAO,GAAGvF,IAAI,CAACwG,aAAa,KAAKnD,SAAS,GAC1K,CAACiD,QAAQ,CAACtG,IAAI,CAACwG,aAAa,CAACX,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAClD,CAAC;IACP,IAAI7F,IAAI,CAACkG,IAAI,KAAK7C,SAAS,EAAE;MACzBgC,KAAK,GAAGW,aAAa,CAACX,KAAK,EAAErF,IAAI,CAACkG,IAAI,CAACO,WAAW,CAAC;IACvD;IACA,MAAMC,aAAa,GAAG1G,IAAI,CAACC,MAAM,CAAC0G,OAAO,KAAKtD,SAAS,IAClDrD,IAAI,CAACC,MAAM,CAACgF,OAAO,IAChBjF,IAAI,CAAC4G,cAAc,IACnB5G,IAAI,CAACoD,qBAAqB,IAC1BhE,YAAY,CAACY,IAAI,CAACoD,qBAAqB,EAAEpD,IAAI,CAACC,MAAM,CAACgF,OAAO,EAAE,IAAI,CAAC,KAC/D,CAAE;IACd,MAAM4B,aAAa,GAAG7G,IAAI,CAACC,MAAM,CAAC6G,OAAO,KAAKzD,SAAS,IAClDrD,IAAI,CAACC,MAAM,CAAC8G,OAAO,IAChB/G,IAAI,CAACgH,cAAc,IACnBhH,IAAI,CAACoD,qBAAqB,IAC1BhE,YAAY,CAACY,IAAI,CAACoD,qBAAqB,EAAEpD,IAAI,CAACC,MAAM,CAAC8G,OAAO,EAAE,IAAI,CAAC,KAC/D,CAAE;IACd,IAAIF,aAAa,EAAE;MACf,MAAMC,OAAO,GAAG9G,IAAI,CAACC,MAAM,CAAC6G,OAAO,KAAKzD,SAAS,GAC3CrD,IAAI,CAACC,MAAM,CAAC6G,OAAO,GACnB9G,IAAI,CAACC,MAAM,CAAC8G,OAAO;MACzB1B,KAAK,GAAG4B,IAAI,CAACC,GAAG,CAAC7B,KAAK,EAAEyB,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAC;MAC3C,IAAI9B,KAAK,KAAKyB,OAAO,CAACK,QAAQ,CAAC,CAAC,EAC5B7B,OAAO,GAAG2B,IAAI,CAACC,GAAG,CAAC5B,OAAO,EAAEwB,OAAO,CAACM,UAAU,CAAC,CAAC,CAAC;MACrD,IAAI9B,OAAO,KAAKwB,OAAO,CAACM,UAAU,CAAC,CAAC,EAChC7B,OAAO,GAAG0B,IAAI,CAACC,GAAG,CAAC3B,OAAO,EAAEuB,OAAO,CAACO,UAAU,CAAC,CAAC,CAAC;IACzD;IACA,IAAIX,aAAa,EAAE;MACf,MAAMC,OAAO,GAAG3G,IAAI,CAACC,MAAM,CAAC0G,OAAO,KAAKtD,SAAS,GAC3CrD,IAAI,CAACC,MAAM,CAAC0G,OAAO,GACnB3G,IAAI,CAACC,MAAM,CAACgF,OAAO;MACzBI,KAAK,GAAG4B,IAAI,CAACK,GAAG,CAACjC,KAAK,EAAEsB,OAAO,CAACQ,QAAQ,CAAC,CAAC,CAAC;MAC3C,IAAI9B,KAAK,KAAKsB,OAAO,CAACQ,QAAQ,CAAC,CAAC,IAAI7B,OAAO,GAAGqB,OAAO,CAACS,UAAU,CAAC,CAAC,EAC9D9B,OAAO,GAAGqB,OAAO,CAACS,UAAU,CAAC,CAAC;MAClC,IAAI9B,OAAO,KAAKqB,OAAO,CAACS,UAAU,CAAC,CAAC,EAChC7B,OAAO,GAAG0B,IAAI,CAACK,GAAG,CAAC/B,OAAO,EAAEoB,OAAO,CAACU,UAAU,CAAC,CAAC,CAAC;IACzD;IACAjC,QAAQ,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACrC;EACA,SAASzE,gBAAgBA,CAACyG,OAAO,EAAE;IAC/B,MAAMC,IAAI,GAAGD,OAAO,IAAIvH,IAAI,CAACoD,qBAAqB;IAClD,IAAIoE,IAAI,EAAE;MACNpC,QAAQ,CAACoC,IAAI,CAACL,QAAQ,CAAC,CAAC,EAAEK,IAAI,CAACJ,UAAU,CAAC,CAAC,EAAEI,IAAI,CAACH,UAAU,CAAC,CAAC,CAAC;IACnE;EACJ;EACA,SAASjC,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACvC,IAAIvF,IAAI,CAACoD,qBAAqB,KAAKC,SAAS,EAAE;MAC1CrD,IAAI,CAACoD,qBAAqB,CAACgC,QAAQ,CAACC,KAAK,GAAG,EAAE,EAAEC,OAAO,EAAEC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7E;IACA,IAAI,CAACvF,IAAI,CAACoG,WAAW,IAAI,CAACpG,IAAI,CAACqG,aAAa,IAAIrG,IAAI,CAAC6C,QAAQ,EACzD;IACJ7C,IAAI,CAACoG,WAAW,CAACP,KAAK,GAAGhH,GAAG,CAAC,CAACmB,IAAI,CAACC,MAAM,CAACwH,SAAS,GAC5C,CAAC,EAAE,GAAGpC,KAAK,IAAI,EAAE,GAAI,EAAE,GAAGzG,GAAG,CAACyG,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC,GAChDA,KAAK,CAAC;IACZrF,IAAI,CAACqG,aAAa,CAACR,KAAK,GAAGhH,GAAG,CAACyG,OAAO,CAAC;IACvC,IAAItF,IAAI,CAACkG,IAAI,KAAK7C,SAAS,EACvBrD,IAAI,CAACkG,IAAI,CAACO,WAAW,GAAGzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAACtH,GAAG,CAACyG,KAAK,IAAI,EAAE,CAAC,CAAC;IAC5D,IAAIrF,IAAI,CAACwG,aAAa,KAAKnD,SAAS,EAChCrD,IAAI,CAACwG,aAAa,CAACX,KAAK,GAAGhH,GAAG,CAAC0G,OAAO,CAAC;EAC/C;EACA,SAASmC,WAAWA,CAACC,KAAK,EAAE;IACxB,MAAMC,WAAW,GAAGzI,cAAc,CAACwI,KAAK,CAAC;IACzC,MAAME,IAAI,GAAGvB,QAAQ,CAACsB,WAAW,CAAC/B,KAAK,CAAC,IAAI8B,KAAK,CAACG,KAAK,IAAI,CAAC,CAAC;IAC7D,IAAID,IAAI,GAAG,IAAI,GAAG,CAAC,IACdF,KAAK,CAACI,GAAG,KAAK,OAAO,IAAI,CAAC,OAAO,CAACtE,IAAI,CAACoE,IAAI,CAACG,QAAQ,CAAC,CAAC,CAAE,EAAE;MAC3D9G,UAAU,CAAC2G,IAAI,CAAC;IACpB;EACJ;EACA,SAASjH,IAAIA,CAACd,OAAO,EAAE6H,KAAK,EAAEM,OAAO,EAAEC,OAAO,EAAE;IAC5C,IAAIP,KAAK,YAAYQ,KAAK,EACtB,OAAOR,KAAK,CAACS,OAAO,CAAEC,EAAE,IAAKzH,IAAI,CAACd,OAAO,EAAEuI,EAAE,EAAEJ,OAAO,EAAEC,OAAO,CAAC,CAAC;IACrE,IAAIpI,OAAO,YAAYqI,KAAK,EACxB,OAAOrI,OAAO,CAACsI,OAAO,CAAEE,EAAE,IAAK1H,IAAI,CAAC0H,EAAE,EAAEX,KAAK,EAAEM,OAAO,EAAEC,OAAO,CAAC,CAAC;IACrEpI,OAAO,CAACyI,gBAAgB,CAACZ,KAAK,EAAEM,OAAO,EAAEC,OAAO,CAAC;IACjDlI,IAAI,CAACQ,SAAS,CAACgI,IAAI,CAAC;MAChBC,MAAM,EAAEA,CAAA,KAAM3I,OAAO,CAAC4I,mBAAmB,CAACf,KAAK,EAAEM,OAAO;IAC5D,CAAC,CAAC;EACN;EACA,SAASU,aAAaA,CAAA,EAAG;IACrB/E,YAAY,CAAC,UAAU,CAAC;EAC5B;EACA,SAASb,UAAUA,CAAA,EAAG;IAClB,IAAI/C,IAAI,CAACC,MAAM,CAAC2I,IAAI,EAAE;MAClB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACR,OAAO,CAAES,GAAG,IAAK;QAClDV,KAAK,CAACW,SAAS,CAACV,OAAO,CAACW,IAAI,CAAC/I,IAAI,CAACF,OAAO,CAACkJ,gBAAgB,CAAE,SAAQH,GAAI,GAAE,CAAC,EAAGP,EAAE,IAAK1H,IAAI,CAAC0H,EAAE,EAAE,OAAO,EAAEtI,IAAI,CAAC6I,GAAG,CAAC,CAAC,CAAC;MACtH,CAAC,CAAC;IACN;IACA,IAAI7I,IAAI,CAAC6C,QAAQ,EAAE;MACfoG,WAAW,CAAC,CAAC;MACb;IACJ;IACA,MAAMC,eAAe,GAAGvK,QAAQ,CAACwK,QAAQ,EAAE,EAAE,CAAC;IAC9CnJ,IAAI,CAAC+F,gBAAgB,GAAGpH,QAAQ,CAACgK,aAAa,EAAE/I,mBAAmB,CAAC;IACpE,IAAII,IAAI,CAACuE,aAAa,IAAI,CAAC,mBAAmB,CAACd,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EACpE/C,IAAI,CAACZ,IAAI,CAACuE,aAAa,EAAE,WAAW,EAAGQ,CAAC,IAAK;MACzC,IAAI/E,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,EAC5BC,WAAW,CAAClK,cAAc,CAAC4F,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;IACNnE,IAAI,CAACqD,MAAM,CAACqF,QAAQ,CAACC,IAAI,EAAE,SAAS,EAAEC,SAAS,CAAC;IAChD,IAAI,CAACxJ,IAAI,CAACC,MAAM,CAACwJ,MAAM,IAAI,CAACzJ,IAAI,CAACC,MAAM,CAACyJ,MAAM,EAC1C9I,IAAI,CAACqD,MAAM,EAAE,QAAQ,EAAEiF,eAAe,CAAC;IAC3C,IAAIjF,MAAM,CAAC0F,YAAY,KAAKtG,SAAS,EACjCzC,IAAI,CAACqD,MAAM,CAACqF,QAAQ,EAAE,YAAY,EAAEM,aAAa,CAAC,CAAC,KAEnDhJ,IAAI,CAACqD,MAAM,CAACqF,QAAQ,EAAE,WAAW,EAAEM,aAAa,CAAC;IACrDhJ,IAAI,CAACqD,MAAM,CAACqF,QAAQ,EAAE,OAAO,EAAEM,aAAa,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAChE,IAAI7J,IAAI,CAACC,MAAM,CAAC6J,UAAU,KAAK,IAAI,EAAE;MACjClJ,IAAI,CAACZ,IAAI,CAAC4F,MAAM,EAAE,OAAO,EAAE5F,IAAI,CAACyB,IAAI,CAAC;MACrCb,IAAI,CAACZ,IAAI,CAAC4F,MAAM,EAAE,OAAO,EAAE5F,IAAI,CAACyB,IAAI,CAAC;IACzC;IACA,IAAIzB,IAAI,CAACuE,aAAa,KAAKlB,SAAS,EAAE;MAClCzC,IAAI,CAACZ,IAAI,CAAC+J,QAAQ,EAAE,OAAO,EAAEC,eAAe,CAAC;MAC7CpJ,IAAI,CAACZ,IAAI,CAAC+J,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,EAAErC,WAAW,CAAC;MACxD9G,IAAI,CAACZ,IAAI,CAACuE,aAAa,EAAE,OAAO,EAAE0F,UAAU,CAAC;IACjD;IACA,IAAIjK,IAAI,CAACkK,aAAa,KAAK7G,SAAS,IAChCrD,IAAI,CAACqG,aAAa,KAAKhD,SAAS,IAChCrD,IAAI,CAACoG,WAAW,KAAK/C,SAAS,EAAE;MAChC,MAAM8G,OAAO,GAAIpF,CAAC,IAAK5F,cAAc,CAAC4F,CAAC,CAAC,CAACqF,MAAM,CAAC,CAAC;MACjDxJ,IAAI,CAACZ,IAAI,CAACkK,aAAa,EAAE,CAAC,WAAW,CAAC,EAAEpF,UAAU,CAAC;MACnDlE,IAAI,CAACZ,IAAI,CAACkK,aAAa,EAAE,MAAM,EAAEpF,UAAU,EAAE;QAAE+E,OAAO,EAAE;MAAK,CAAC,CAAC;MAC/DjJ,IAAI,CAACZ,IAAI,CAACkK,aAAa,EAAE,OAAO,EAAEG,aAAa,CAAC;MAChDzJ,IAAI,CAAC,CAACZ,IAAI,CAACoG,WAAW,EAAEpG,IAAI,CAACqG,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE8D,OAAO,CAAC;MACzE,IAAInK,IAAI,CAACwG,aAAa,KAAKnD,SAAS,EAChCzC,IAAI,CAACZ,IAAI,CAACwG,aAAa,EAAE,OAAO,EAAE,MAAMxG,IAAI,CAACwG,aAAa,IAAIxG,IAAI,CAACwG,aAAa,CAAC4D,MAAM,CAAC,CAAC,CAAC;MAC9F,IAAIpK,IAAI,CAACkG,IAAI,KAAK7C,SAAS,EAAE;QACzBzC,IAAI,CAACZ,IAAI,CAACkG,IAAI,EAAE,OAAO,EAAGnB,CAAC,IAAK;UAC5BD,UAAU,CAACC,CAAC,CAAC;UACb4D,aAAa,CAAC,CAAC;QACnB,CAAC,CAAC;MACN;IACJ;IACA,IAAI3I,IAAI,CAACC,MAAM,CAACqK,UAAU,EAAE;MACxB1J,IAAI,CAACZ,IAAI,CAAC4F,MAAM,EAAE,MAAM,EAAE2E,MAAM,CAAC;IACrC;EACJ;EACA,SAAS/I,UAAUA,CAACgJ,QAAQ,EAAE7B,aAAa,EAAE;IACzC,MAAM8B,MAAM,GAAGD,QAAQ,KAAKnH,SAAS,GAC/BrD,IAAI,CAACO,SAAS,CAACiK,QAAQ,CAAC,GACxBxK,IAAI,CAACoD,qBAAqB,KACvBpD,IAAI,CAACC,MAAM,CAACgF,OAAO,IAAIjF,IAAI,CAACC,MAAM,CAACgF,OAAO,GAAGjF,IAAI,CAAC0K,GAAG,GAChD1K,IAAI,CAACC,MAAM,CAACgF,OAAO,GACnBjF,IAAI,CAACC,MAAM,CAAC8G,OAAO,IAAI/G,IAAI,CAACC,MAAM,CAAC8G,OAAO,GAAG/G,IAAI,CAAC0K,GAAG,GACjD1K,IAAI,CAACC,MAAM,CAAC8G,OAAO,GACnB/G,IAAI,CAAC0K,GAAG,CAAC;IAC3B,MAAMC,OAAO,GAAG3K,IAAI,CAACoC,WAAW;IAChC,MAAMwI,QAAQ,GAAG5K,IAAI,CAACkC,YAAY;IAClC,IAAI;MACA,IAAIuI,MAAM,KAAKpH,SAAS,EAAE;QACtBrD,IAAI,CAACoC,WAAW,GAAGqI,MAAM,CAACI,WAAW,CAAC,CAAC;QACvC7K,IAAI,CAACkC,YAAY,GAAGuI,MAAM,CAACK,QAAQ,CAAC,CAAC;MACzC;IACJ,CAAC,CACD,OAAO/F,CAAC,EAAE;MACNA,CAAC,CAACgG,OAAO,GAAG,yBAAyB,GAAGN,MAAM;MAC9CzK,IAAI,CAACC,MAAM,CAAC+K,YAAY,CAACjG,CAAC,CAAC;IAC/B;IACA,IAAI4D,aAAa,IAAI3I,IAAI,CAACoC,WAAW,KAAKuI,OAAO,EAAE;MAC/C/G,YAAY,CAAC,cAAc,CAAC;MAC5BqH,gBAAgB,CAAC,CAAC;IACtB;IACA,IAAItC,aAAa,KACZ3I,IAAI,CAACoC,WAAW,KAAKuI,OAAO,IAAI3K,IAAI,CAACkC,YAAY,KAAK0I,QAAQ,CAAC,EAAE;MAClEhH,YAAY,CAAC,eAAe,CAAC;IACjC;IACA5D,IAAI,CAAC0B,MAAM,CAAC,CAAC;EACjB;EACA,SAAS2I,aAAaA,CAACtF,CAAC,EAAE;IACtB,MAAM6C,WAAW,GAAGzI,cAAc,CAAC4F,CAAC,CAAC;IACrC,IAAI,CAAC6C,WAAW,CAACsD,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,EACvCC,iBAAiB,CAACrG,CAAC,EAAE6C,WAAW,CAACyD,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAChF;EACA,SAASF,iBAAiBA,CAACrG,CAAC,EAAE+C,KAAK,EAAEyD,SAAS,EAAE;IAC5C,MAAMC,MAAM,GAAGzG,CAAC,IAAI5F,cAAc,CAAC4F,CAAC,CAAC;IACrC,MAAMxC,KAAK,GAAGgJ,SAAS,IAClBC,MAAM,IAAIA,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAACC,UAAW;IACjE,MAAM/D,KAAK,GAAGgE,WAAW,CAAC,WAAW,CAAC;IACtChE,KAAK,CAACG,KAAK,GAAGA,KAAK;IACnBvF,KAAK,IAAIA,KAAK,CAACqJ,aAAa,CAACjE,KAAK,CAAC;EACvC;EACA,SAAS7E,KAAKA,CAAA,EAAG;IACb,MAAM+I,QAAQ,GAAG5H,MAAM,CAACqF,QAAQ,CAACwC,sBAAsB,CAAC,CAAC;IACzD9L,IAAI,CAACmE,iBAAiB,GAAGpF,aAAa,CAAC,KAAK,EAAE,oBAAoB,CAAC;IACnEiB,IAAI,CAACmE,iBAAiB,CAAC4H,QAAQ,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC/L,IAAI,CAACC,MAAM,CAACiD,UAAU,EAAE;MACzB2I,QAAQ,CAACG,WAAW,CAACC,aAAa,CAAC,CAAC,CAAC;MACrCjM,IAAI,CAACkM,cAAc,GAAGnN,aAAa,CAAC,KAAK,EAAE,0BAA0B,CAAC;MACtE,IAAIiB,IAAI,CAACC,MAAM,CAAC8D,WAAW,EAAE;QACzB,MAAM;UAAEa,WAAW;UAAEb;QAAY,CAAC,GAAGoI,UAAU,CAAC,CAAC;QACjDnM,IAAI,CAACkM,cAAc,CAACF,WAAW,CAACpH,WAAW,CAAC;QAC5C5E,IAAI,CAAC+D,WAAW,GAAGA,WAAW;QAC9B/D,IAAI,CAAC4E,WAAW,GAAGA,WAAW;MAClC;MACA5E,IAAI,CAACoM,UAAU,GAAGrN,aAAa,CAAC,KAAK,EAAE,sBAAsB,CAAC;MAC9DiB,IAAI,CAACoM,UAAU,CAACJ,WAAW,CAACK,aAAa,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACrM,IAAI,CAACuE,aAAa,EAAE;QACrBvE,IAAI,CAACuE,aAAa,GAAGxF,aAAa,CAAC,KAAK,EAAE,gBAAgB,CAAC;QAC3DiB,IAAI,CAACuE,aAAa,CAACwH,QAAQ,GAAG,CAAC,CAAC;MACpC;MACAO,SAAS,CAAC,CAAC;MACXtM,IAAI,CAACoM,UAAU,CAACJ,WAAW,CAAChM,IAAI,CAACuE,aAAa,CAAC;MAC/CvE,IAAI,CAACkM,cAAc,CAACF,WAAW,CAAChM,IAAI,CAACoM,UAAU,CAAC;MAChDP,QAAQ,CAACG,WAAW,CAAChM,IAAI,CAACkM,cAAc,CAAC;IAC7C;IACA,IAAIlM,IAAI,CAACC,MAAM,CAACkD,UAAU,EAAE;MACxB0I,QAAQ,CAACG,WAAW,CAACO,SAAS,CAAC,CAAC,CAAC;IACrC;IACArN,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,WAAW,EAAEnE,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,CAAC;IAC9ElK,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,SAAS,EAAEnE,IAAI,CAACC,MAAM,CAACuM,OAAO,KAAK,IAAI,CAAC;IAC5EtN,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,YAAY,EAAEnE,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,CAAC;IAC7EhE,IAAI,CAACmE,iBAAiB,CAAC6H,WAAW,CAACH,QAAQ,CAAC;IAC5C,MAAMY,YAAY,GAAGzM,IAAI,CAACC,MAAM,CAACyM,QAAQ,KAAKrJ,SAAS,IACnDrD,IAAI,CAACC,MAAM,CAACyM,QAAQ,CAACC,QAAQ,KAAKtJ,SAAS;IAC/C,IAAIrD,IAAI,CAACC,MAAM,CAACwJ,MAAM,IAAIzJ,IAAI,CAACC,MAAM,CAACyJ,MAAM,EAAE;MAC1C1J,IAAI,CAACmE,iBAAiB,CAACkH,SAAS,CAACuB,GAAG,CAAC5M,IAAI,CAACC,MAAM,CAACwJ,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;MAC9E,IAAIzJ,IAAI,CAACC,MAAM,CAACwJ,MAAM,EAAE;QACpB,IAAI,CAACgD,YAAY,IAAIzM,IAAI,CAACF,OAAO,CAAC2L,UAAU,EACxCzL,IAAI,CAACF,OAAO,CAAC2L,UAAU,CAACoB,YAAY,CAAC7M,IAAI,CAACmE,iBAAiB,EAAEnE,IAAI,CAAC4F,MAAM,CAACkH,WAAW,CAAC,CAAC,KACrF,IAAI9M,IAAI,CAACC,MAAM,CAACyM,QAAQ,KAAKrJ,SAAS,EACvCrD,IAAI,CAACC,MAAM,CAACyM,QAAQ,CAACV,WAAW,CAAChM,IAAI,CAACmE,iBAAiB,CAAC;MAChE;MACA,IAAInE,IAAI,CAACC,MAAM,CAACyJ,MAAM,EAAE;QACpB,MAAMqD,OAAO,GAAGhO,aAAa,CAAC,KAAK,EAAE,mBAAmB,CAAC;QACzD,IAAIiB,IAAI,CAACF,OAAO,CAAC2L,UAAU,EACvBzL,IAAI,CAACF,OAAO,CAAC2L,UAAU,CAACoB,YAAY,CAACE,OAAO,EAAE/M,IAAI,CAACF,OAAO,CAAC;QAC/DiN,OAAO,CAACf,WAAW,CAAChM,IAAI,CAACF,OAAO,CAAC;QACjC,IAAIE,IAAI,CAACgN,QAAQ,EACbD,OAAO,CAACf,WAAW,CAAChM,IAAI,CAACgN,QAAQ,CAAC;QACtCD,OAAO,CAACf,WAAW,CAAChM,IAAI,CAACmE,iBAAiB,CAAC;MAC/C;IACJ;IACA,IAAI,CAACnE,IAAI,CAACC,MAAM,CAACyJ,MAAM,IAAI,CAAC1J,IAAI,CAACC,MAAM,CAACwJ,MAAM,EAC1C,CAACzJ,IAAI,CAACC,MAAM,CAACyM,QAAQ,KAAKrJ,SAAS,GAC7BrD,IAAI,CAACC,MAAM,CAACyM,QAAQ,GACpBzI,MAAM,CAACqF,QAAQ,CAACC,IAAI,EAAEyC,WAAW,CAAChM,IAAI,CAACmE,iBAAiB,CAAC;EACvE;EACA,SAAS8I,SAASA,CAAC/B,SAAS,EAAE1D,IAAI,EAAE0F,SAAS,EAAEC,CAAC,EAAE;IAC9C,MAAMC,aAAa,GAAG7L,SAAS,CAACiG,IAAI,EAAE,IAAI,CAAC;MAAE6F,UAAU,GAAGtO,aAAa,CAAC,MAAM,EAAE,gBAAgB,GAAGmM,SAAS,EAAE1D,IAAI,CAAC8F,OAAO,CAAC,CAAC,CAACtF,QAAQ,CAAC,CAAC,CAAC;IACxIqF,UAAU,CAAC9F,OAAO,GAAGC,IAAI;IACzB6F,UAAU,CAACE,EAAE,GAAGJ,CAAC;IACjBE,UAAU,CAACG,YAAY,CAAC,YAAY,EAAExN,IAAI,CAACyN,UAAU,CAACjG,IAAI,EAAExH,IAAI,CAACC,MAAM,CAACyN,cAAc,CAAC,CAAC;IACxF,IAAIxC,SAAS,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAClC/L,YAAY,CAACoI,IAAI,EAAExH,IAAI,CAAC0K,GAAG,CAAC,KAAK,CAAC,EAAE;MACpC1K,IAAI,CAAC2N,aAAa,GAAGN,UAAU;MAC/BA,UAAU,CAAChC,SAAS,CAACuB,GAAG,CAAC,OAAO,CAAC;MACjCS,UAAU,CAACG,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;IACnD;IACA,IAAIJ,aAAa,EAAE;MACfC,UAAU,CAACtB,QAAQ,GAAG,CAAC,CAAC;MACxB,IAAI6B,cAAc,CAACpG,IAAI,CAAC,EAAE;QACtB6F,UAAU,CAAChC,SAAS,CAACuB,GAAG,CAAC,UAAU,CAAC;QACpC5M,IAAI,CAAC6N,gBAAgB,GAAGR,UAAU;QAClC,IAAIrN,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,EAAE;UAC9BlK,WAAW,CAACmO,UAAU,EAAE,YAAY,EAAErN,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,IACvD5D,YAAY,CAACoI,IAAI,EAAExH,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;UAC1D9D,WAAW,CAACmO,UAAU,EAAE,UAAU,EAAErN,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,IACrD5D,YAAY,CAACoI,IAAI,EAAExH,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;UAC1D,IAAIkI,SAAS,KAAK,cAAc,EAC5BmC,UAAU,CAAChC,SAAS,CAACuB,GAAG,CAAC,SAAS,CAAC;QAC3C;MACJ;IACJ,CAAC,MACI;MACDS,UAAU,CAAChC,SAAS,CAACuB,GAAG,CAAC,oBAAoB,CAAC;IAClD;IACA,IAAI5M,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,EAAE;MAC9B,IAAI0E,aAAa,CAACtG,IAAI,CAAC,IAAI,CAACoG,cAAc,CAACpG,IAAI,CAAC,EAC5C6F,UAAU,CAAChC,SAAS,CAACuB,GAAG,CAAC,SAAS,CAAC;IAC3C;IACA,IAAI5M,IAAI,CAAC+D,WAAW,IAChB/D,IAAI,CAACC,MAAM,CAAC+D,UAAU,KAAK,CAAC,IAC5BkH,SAAS,KAAK,cAAc,IAC5BgC,SAAS,GAAG,CAAC,KAAK,CAAC,EAAE;MACrBlN,IAAI,CAAC+D,WAAW,CAACgK,kBAAkB,CAAC,WAAW,EAAE,8BAA8B,GAAG/N,IAAI,CAACC,MAAM,CAAC+N,OAAO,CAACxG,IAAI,CAAC,GAAG,SAAS,CAAC;IAC5H;IACA5D,YAAY,CAAC,aAAa,EAAEyJ,UAAU,CAAC;IACvC,OAAOA,UAAU;EACrB;EACA,SAASY,cAAcA,CAACC,UAAU,EAAE;IAChCA,UAAU,CAACC,KAAK,CAAC,CAAC;IAClB,IAAInO,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,EAC5BC,WAAW,CAAC6E,UAAU,CAAC;EAC/B;EACA,SAASE,oBAAoBA,CAACtG,KAAK,EAAE;IACjC,MAAMuG,UAAU,GAAGvG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG9H,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC;IAC7D,MAAMsK,QAAQ,GAAGxG,KAAK,GAAG,CAAC,GAAG9H,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,CAAC;IACxD,KAAK,IAAIuK,CAAC,GAAGF,UAAU,EAAEE,CAAC,IAAID,QAAQ,EAAEC,CAAC,IAAIzG,KAAK,EAAE;MAChD,MAAM7F,KAAK,GAAGjC,IAAI,CAACuE,aAAa,CAACiK,QAAQ,CAACD,CAAC,CAAC;MAC5C,MAAME,UAAU,GAAG3G,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG7F,KAAK,CAACuM,QAAQ,CAACvL,MAAM,GAAG,CAAC;MAC5D,MAAMyL,QAAQ,GAAG5G,KAAK,GAAG,CAAC,GAAG7F,KAAK,CAACuM,QAAQ,CAACvL,MAAM,GAAG,CAAC,CAAC;MACvD,KAAK,IAAIkK,CAAC,GAAGsB,UAAU,EAAEtB,CAAC,IAAIuB,QAAQ,EAAEvB,CAAC,IAAIrF,KAAK,EAAE;QAChD,MAAM6G,CAAC,GAAG1M,KAAK,CAACuM,QAAQ,CAACrB,CAAC,CAAC;QAC3B,IAAIwB,CAAC,CAACzD,SAAS,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI5J,SAAS,CAACoN,CAAC,CAACpH,OAAO,CAAC,EAC5D,OAAOoH,CAAC;MAChB;IACJ;IACA,OAAOtL,SAAS;EACpB;EACA,SAASuL,mBAAmBA,CAACC,OAAO,EAAE/G,KAAK,EAAE;IACzC,MAAMgH,UAAU,GAAGD,OAAO,CAAC3D,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GACtD0D,OAAO,CAACtH,OAAO,CAACuD,QAAQ,CAAC,CAAC,GAC1B9K,IAAI,CAACkC,YAAY;IACvB,MAAMoM,QAAQ,GAAGxG,KAAK,GAAG,CAAC,GAAG9H,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,CAAC;IACxD,MAAM+K,SAAS,GAAGjH,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpC,KAAK,IAAIyG,CAAC,GAAGO,UAAU,GAAG9O,IAAI,CAACkC,YAAY,EAAEqM,CAAC,IAAID,QAAQ,EAAEC,CAAC,IAAIQ,SAAS,EAAE;MACxE,MAAM9M,KAAK,GAAGjC,IAAI,CAACuE,aAAa,CAACiK,QAAQ,CAACD,CAAC,CAAC;MAC5C,MAAME,UAAU,GAAGK,UAAU,GAAG9O,IAAI,CAACkC,YAAY,KAAKqM,CAAC,GACjDM,OAAO,CAACtB,EAAE,GAAGzF,KAAK,GAClBA,KAAK,GAAG,CAAC,GACL7F,KAAK,CAACuM,QAAQ,CAACvL,MAAM,GAAG,CAAC,GACzB,CAAC;MACX,MAAM+L,YAAY,GAAG/M,KAAK,CAACuM,QAAQ,CAACvL,MAAM;MAC1C,KAAK,IAAIkK,CAAC,GAAGsB,UAAU,EAAEtB,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG6B,YAAY,IAAI7B,CAAC,KAAKrF,KAAK,GAAG,CAAC,GAAGkH,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE7B,CAAC,IAAI4B,SAAS,EAAE;QACzG,MAAMJ,CAAC,GAAG1M,KAAK,CAACuM,QAAQ,CAACrB,CAAC,CAAC;QAC3B,IAAIwB,CAAC,CAACzD,SAAS,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IACpC5J,SAAS,CAACoN,CAAC,CAACpH,OAAO,CAAC,IACpBN,IAAI,CAACgI,GAAG,CAACJ,OAAO,CAACtB,EAAE,GAAGJ,CAAC,CAAC,IAAIlG,IAAI,CAACgI,GAAG,CAACnH,KAAK,CAAC,EAC3C,OAAOmG,cAAc,CAACU,CAAC,CAAC;MAChC;IACJ;IACA3O,IAAI,CAACiB,WAAW,CAAC8N,SAAS,CAAC;IAC3BG,UAAU,CAACd,oBAAoB,CAACW,SAAS,CAAC,EAAE,CAAC,CAAC;IAC9C,OAAO1L,SAAS;EACpB;EACA,SAAS6L,UAAUA,CAACL,OAAO,EAAEM,MAAM,EAAE;IACjC,MAAMC,UAAU,GAAGC,QAAQ,CAAC/F,QAAQ,CAACgG,aAAa,IAAIhG,QAAQ,CAACC,IAAI,CAAC;IACpE,MAAMgG,SAAS,GAAGV,OAAO,KAAKxL,SAAS,GACjCwL,OAAO,GACPO,UAAU,GACN9F,QAAQ,CAACgG,aAAa,GACtBtP,IAAI,CAAC6N,gBAAgB,KAAKxK,SAAS,IAAIgM,QAAQ,CAACrP,IAAI,CAAC6N,gBAAgB,CAAC,GAClE7N,IAAI,CAAC6N,gBAAgB,GACrB7N,IAAI,CAAC2N,aAAa,KAAKtK,SAAS,IAAIgM,QAAQ,CAACrP,IAAI,CAAC2N,aAAa,CAAC,GAC5D3N,IAAI,CAAC2N,aAAa,GAClBS,oBAAoB,CAACe,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAII,SAAS,KAAKlM,SAAS,EAAE;MACzBrD,IAAI,CAAC4F,MAAM,CAACuI,KAAK,CAAC,CAAC;IACvB,CAAC,MACI,IAAI,CAACiB,UAAU,EAAE;MAClBnB,cAAc,CAACsB,SAAS,CAAC;IAC7B,CAAC,MACI;MACDX,mBAAmB,CAACW,SAAS,EAAEJ,MAAM,CAAC;IAC1C;EACJ;EACA,SAASK,cAAcA,CAAC3H,IAAI,EAAE5F,KAAK,EAAE;IACjC,MAAMwN,YAAY,GAAG,CAAC,IAAIvK,IAAI,CAAC2C,IAAI,EAAE5F,KAAK,EAAE,CAAC,CAAC,CAACyN,MAAM,CAAC,CAAC,GAAG1P,IAAI,CAACM,IAAI,CAACqP,cAAc,GAAG,CAAC,IAAI,CAAC;IAC3F,MAAMC,aAAa,GAAG5P,IAAI,CAAC+B,KAAK,CAACC,cAAc,CAAC,CAACC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE4F,IAAI,CAAC;IAC5E,MAAMxF,WAAW,GAAGrC,IAAI,CAAC+B,KAAK,CAACC,cAAc,CAACC,KAAK,EAAE4F,IAAI,CAAC;MAAEpD,IAAI,GAAGR,MAAM,CAACqF,QAAQ,CAACwC,sBAAsB,CAAC,CAAC;MAAE+D,YAAY,GAAG7P,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC;MAAE8L,iBAAiB,GAAGD,YAAY,GAAG,qBAAqB,GAAG,cAAc;MAAEE,iBAAiB,GAAGF,YAAY,GAAG,qBAAqB,GAAG,cAAc;IAC5S,IAAI3C,SAAS,GAAG0C,aAAa,GAAG,CAAC,GAAGH,YAAY;MAAEO,QAAQ,GAAG,CAAC;IAC9D,OAAO9C,SAAS,IAAI0C,aAAa,EAAE1C,SAAS,EAAE,EAAE8C,QAAQ,EAAE,EAAE;MACxDvL,IAAI,CAACuH,WAAW,CAACiB,SAAS,CAAC6C,iBAAiB,EAAE,IAAI5K,IAAI,CAAC2C,IAAI,EAAE5F,KAAK,GAAG,CAAC,EAAEiL,SAAS,CAAC,EAAEA,SAAS,EAAE8C,QAAQ,CAAC,CAAC;IAC7G;IACA,KAAK9C,SAAS,GAAG,CAAC,EAAEA,SAAS,IAAI7K,WAAW,EAAE6K,SAAS,EAAE,EAAE8C,QAAQ,EAAE,EAAE;MACnEvL,IAAI,CAACuH,WAAW,CAACiB,SAAS,CAAC,EAAE,EAAE,IAAI/H,IAAI,CAAC2C,IAAI,EAAE5F,KAAK,EAAEiL,SAAS,CAAC,EAAEA,SAAS,EAAE8C,QAAQ,CAAC,CAAC;IAC1F;IACA,KAAK,IAAIC,MAAM,GAAG5N,WAAW,GAAG,CAAC,EAAE4N,MAAM,IAAI,EAAE,GAAGR,YAAY,KACzDzP,IAAI,CAACC,MAAM,CAAC+D,UAAU,KAAK,CAAC,IAAIgM,QAAQ,GAAG,CAAC,KAAK,CAAC,CAAC,EAAEC,MAAM,EAAE,EAAED,QAAQ,EAAE,EAAE;MAC5EvL,IAAI,CAACuH,WAAW,CAACiB,SAAS,CAAC8C,iBAAiB,EAAE,IAAI7K,IAAI,CAAC2C,IAAI,EAAE5F,KAAK,GAAG,CAAC,EAAEgO,MAAM,GAAG5N,WAAW,CAAC,EAAE4N,MAAM,EAAED,QAAQ,CAAC,CAAC;IACrH;IACA,MAAME,YAAY,GAAGnR,aAAa,CAAC,KAAK,EAAE,cAAc,CAAC;IACzDmR,YAAY,CAAClE,WAAW,CAACvH,IAAI,CAAC;IAC9B,OAAOyL,YAAY;EACvB;EACA,SAAS5D,SAASA,CAAA,EAAG;IACjB,IAAItM,IAAI,CAACuE,aAAa,KAAKlB,SAAS,EAAE;MAClC;IACJ;IACAvE,SAAS,CAACkB,IAAI,CAACuE,aAAa,CAAC;IAC7B,IAAIvE,IAAI,CAAC+D,WAAW,EAChBjF,SAAS,CAACkB,IAAI,CAAC+D,WAAW,CAAC;IAC/B,MAAMoM,IAAI,GAAG7G,QAAQ,CAACwC,sBAAsB,CAAC,CAAC;IAC9C,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnN,IAAI,CAACC,MAAM,CAAC+D,UAAU,EAAEmJ,CAAC,EAAE,EAAE;MAC7C,MAAMiD,CAAC,GAAG,IAAIlL,IAAI,CAAClF,IAAI,CAACoC,WAAW,EAAEpC,IAAI,CAACkC,YAAY,EAAE,CAAC,CAAC;MAC1DkO,CAAC,CAACC,QAAQ,CAACrQ,IAAI,CAACkC,YAAY,GAAGiL,CAAC,CAAC;MACjCgD,IAAI,CAACnE,WAAW,CAACwD,cAAc,CAACY,CAAC,CAACvF,WAAW,CAAC,CAAC,EAAEuF,CAAC,CAACtF,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnE;IACA9K,IAAI,CAACuE,aAAa,CAACyH,WAAW,CAACmE,IAAI,CAAC;IACpCnQ,IAAI,CAACyE,IAAI,GAAGzE,IAAI,CAACuE,aAAa,CAACmH,UAAU;IACzC,IAAI1L,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,IAAIpJ,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MACjEoG,WAAW,CAAC,CAAC;IACjB;EACJ;EACA,SAAS4B,gBAAgBA,CAAA,EAAG;IACxB,IAAIjL,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,IAC1BhE,IAAI,CAACC,MAAM,CAACqQ,iBAAiB,KAAK,UAAU,EAC5C;IACJ,MAAMC,gBAAgB,GAAG,SAAAA,CAAUtO,KAAK,EAAE;MACtC,IAAIjC,IAAI,CAACC,MAAM,CAACgF,OAAO,KAAK5B,SAAS,IACjCrD,IAAI,CAACoC,WAAW,KAAKpC,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAC,IACtD5I,KAAK,GAAGjC,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC6F,QAAQ,CAAC,CAAC,EAAE;QACxC,OAAO,KAAK;MAChB;MACA,OAAO,EAAE9K,IAAI,CAACC,MAAM,CAAC8G,OAAO,KAAK1D,SAAS,IACtCrD,IAAI,CAACoC,WAAW,KAAKpC,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAC,IACtD5I,KAAK,GAAGjC,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC+D,QAAQ,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD9K,IAAI,CAACwQ,uBAAuB,CAACzE,QAAQ,GAAG,CAAC,CAAC;IAC1C/L,IAAI,CAACwQ,uBAAuB,CAACC,SAAS,GAAG,EAAE;IAC3C,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAI,CAACoD,gBAAgB,CAACpD,CAAC,CAAC,EACpB;MACJ,MAAMlL,KAAK,GAAGlD,aAAa,CAAC,QAAQ,EAAE,+BAA+B,CAAC;MACtEkD,KAAK,CAAC4D,KAAK,GAAG,IAAIX,IAAI,CAAClF,IAAI,CAACoC,WAAW,EAAE+K,CAAC,CAAC,CAACrC,QAAQ,CAAC,CAAC,CAAC9C,QAAQ,CAAC,CAAC;MACjE/F,KAAK,CAACwE,WAAW,GAAG9G,UAAU,CAACwN,CAAC,EAAEnN,IAAI,CAACC,MAAM,CAACyQ,qBAAqB,EAAE1Q,IAAI,CAACM,IAAI,CAAC;MAC/E2B,KAAK,CAAC8J,QAAQ,GAAG,CAAC,CAAC;MACnB,IAAI/L,IAAI,CAACkC,YAAY,KAAKiL,CAAC,EAAE;QACzBlL,KAAK,CAAC0O,QAAQ,GAAG,IAAI;MACzB;MACA3Q,IAAI,CAACwQ,uBAAuB,CAACxE,WAAW,CAAC/J,KAAK,CAAC;IACnD;EACJ;EACA,SAAS2O,UAAUA,CAAA,EAAG;IAClB,MAAMC,SAAS,GAAG9R,aAAa,CAAC,KAAK,EAAE,iBAAiB,CAAC;IACzD,MAAM+R,gBAAgB,GAAG7M,MAAM,CAACqF,QAAQ,CAACwC,sBAAsB,CAAC,CAAC;IACjE,IAAIiF,YAAY;IAChB,IAAI/Q,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,IAC1BhE,IAAI,CAACC,MAAM,CAACqQ,iBAAiB,KAAK,QAAQ,EAAE;MAC5CS,YAAY,GAAGhS,aAAa,CAAC,MAAM,EAAE,WAAW,CAAC;IACrD,CAAC,MACI;MACDiB,IAAI,CAACwQ,uBAAuB,GAAGzR,aAAa,CAAC,QAAQ,EAAE,gCAAgC,CAAC;MACxFiB,IAAI,CAACwQ,uBAAuB,CAAChD,YAAY,CAAC,YAAY,EAAExN,IAAI,CAACM,IAAI,CAAC0Q,cAAc,CAAC;MACjFpQ,IAAI,CAACZ,IAAI,CAACwQ,uBAAuB,EAAE,QAAQ,EAAGzL,CAAC,IAAK;QAChD,MAAMyG,MAAM,GAAGrM,cAAc,CAAC4F,CAAC,CAAC;QAChC,MAAMkM,aAAa,GAAG3K,QAAQ,CAACkF,MAAM,CAAC3F,KAAK,EAAE,EAAE,CAAC;QAChD7F,IAAI,CAACiB,WAAW,CAACgQ,aAAa,GAAGjR,IAAI,CAACkC,YAAY,CAAC;QACnD0B,YAAY,CAAC,eAAe,CAAC;MACjC,CAAC,CAAC;MACFqH,gBAAgB,CAAC,CAAC;MAClB8F,YAAY,GAAG/Q,IAAI,CAACwQ,uBAAuB;IAC/C;IACA,MAAMU,SAAS,GAAGlS,iBAAiB,CAAC,UAAU,EAAE;MAAEmS,QAAQ,EAAE;IAAK,CAAC,CAAC;IACnE,MAAMC,WAAW,GAAGF,SAAS,CAACG,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC9DD,WAAW,CAAC5D,YAAY,CAAC,YAAY,EAAExN,IAAI,CAACM,IAAI,CAACgR,aAAa,CAAC;IAC/D,IAAItR,IAAI,CAACC,MAAM,CAACgF,OAAO,EAAE;MACrBmM,WAAW,CAAC5D,YAAY,CAAC,KAAK,EAAExN,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAC,CAAC7C,QAAQ,CAAC,CAAC,CAAC;IACjF;IACA,IAAIhI,IAAI,CAACC,MAAM,CAAC8G,OAAO,EAAE;MACrBqK,WAAW,CAAC5D,YAAY,CAAC,KAAK,EAAExN,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAC,CAAC7C,QAAQ,CAAC,CAAC,CAAC;MAC7EoJ,WAAW,CAACG,QAAQ,GAChB,CAAC,CAACvR,IAAI,CAACC,MAAM,CAACgF,OAAO,IACjBjF,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAC,KAAK7K,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAC;IACnF;IACA,MAAM3I,YAAY,GAAGnD,aAAa,CAAC,KAAK,EAAE,yBAAyB,CAAC;IACpEmD,YAAY,CAAC8J,WAAW,CAAC+E,YAAY,CAAC;IACtC7O,YAAY,CAAC8J,WAAW,CAACkF,SAAS,CAAC;IACnCJ,gBAAgB,CAAC9E,WAAW,CAAC9J,YAAY,CAAC;IAC1C2O,SAAS,CAAC7E,WAAW,CAAC8E,gBAAgB,CAAC;IACvC,OAAO;MACHD,SAAS;MACTO,WAAW;MACXL;IACJ,CAAC;EACL;EACA,SAASS,WAAWA,CAAA,EAAG;IACnB1S,SAAS,CAACkB,IAAI,CAAC+J,QAAQ,CAAC;IACxB/J,IAAI,CAAC+J,QAAQ,CAACiC,WAAW,CAAChM,IAAI,CAACyR,YAAY,CAAC;IAC5C,IAAIzR,IAAI,CAACC,MAAM,CAAC+D,UAAU,EAAE;MACxBhE,IAAI,CAAC0R,YAAY,GAAG,EAAE;MACtB1R,IAAI,CAAC2R,aAAa,GAAG,EAAE;IAC3B;IACA,KAAK,IAAIpD,CAAC,GAAGvO,IAAI,CAACC,MAAM,CAAC+D,UAAU,EAAEuK,CAAC,EAAE,GAAG;MACvC,MAAMtM,KAAK,GAAG2O,UAAU,CAAC,CAAC;MAC1B5Q,IAAI,CAAC0R,YAAY,CAAClJ,IAAI,CAACvG,KAAK,CAACmP,WAAW,CAAC;MACzCpR,IAAI,CAAC2R,aAAa,CAACnJ,IAAI,CAACvG,KAAK,CAAC8O,YAAY,CAAC;MAC3C/Q,IAAI,CAAC+J,QAAQ,CAACiC,WAAW,CAAC/J,KAAK,CAAC4O,SAAS,CAAC;IAC9C;IACA7Q,IAAI,CAAC+J,QAAQ,CAACiC,WAAW,CAAChM,IAAI,CAAC4R,YAAY,CAAC;EAChD;EACA,SAAS3F,aAAaA,CAAA,EAAG;IACrBjM,IAAI,CAAC+J,QAAQ,GAAGhL,aAAa,CAAC,KAAK,EAAE,kBAAkB,CAAC;IACxDiB,IAAI,CAAC0R,YAAY,GAAG,EAAE;IACtB1R,IAAI,CAAC2R,aAAa,GAAG,EAAE;IACvB3R,IAAI,CAACyR,YAAY,GAAG1S,aAAa,CAAC,MAAM,EAAE,sBAAsB,CAAC;IACjEiB,IAAI,CAACyR,YAAY,CAAChB,SAAS,GAAGzQ,IAAI,CAACC,MAAM,CAAC4R,SAAS;IACnD7R,IAAI,CAAC4R,YAAY,GAAG7S,aAAa,CAAC,MAAM,EAAE,sBAAsB,CAAC;IACjEiB,IAAI,CAAC4R,YAAY,CAACnB,SAAS,GAAGzQ,IAAI,CAACC,MAAM,CAAC6R,SAAS;IACnDN,WAAW,CAAC,CAAC;IACbtR,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,EAAE,qBAAqB,EAAE;MAC/CgS,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACiS,oBAAoB;MACpCtQ,GAAGA,CAACuQ,IAAI,EAAE;QACN,IAAIlS,IAAI,CAACiS,oBAAoB,KAAKC,IAAI,EAAE;UACpChT,WAAW,CAACc,IAAI,CAACyR,YAAY,EAAE,oBAAoB,EAAES,IAAI,CAAC;UAC1DlS,IAAI,CAACiS,oBAAoB,GAAGC,IAAI;QACpC;MACJ;IACJ,CAAC,CAAC;IACFhS,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,EAAE,qBAAqB,EAAE;MAC/CgS,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACmS,oBAAoB;MACpCxQ,GAAGA,CAACuQ,IAAI,EAAE;QACN,IAAIlS,IAAI,CAACmS,oBAAoB,KAAKD,IAAI,EAAE;UACpChT,WAAW,CAACc,IAAI,CAAC4R,YAAY,EAAE,oBAAoB,EAAEM,IAAI,CAAC;UAC1DlS,IAAI,CAACmS,oBAAoB,GAAGD,IAAI;QACpC;MACJ;IACJ,CAAC,CAAC;IACFlS,IAAI,CAACoS,kBAAkB,GAAGpS,IAAI,CAAC0R,YAAY,CAAC,CAAC,CAAC;IAC9CW,4BAA4B,CAAC,CAAC;IAC9B,OAAOrS,IAAI,CAAC+J,QAAQ;EACxB;EACA,SAASwC,SAASA,CAAA,EAAG;IACjBvM,IAAI,CAACmE,iBAAiB,CAACkH,SAAS,CAACuB,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAI5M,IAAI,CAACC,MAAM,CAACiD,UAAU,EACtBlD,IAAI,CAACmE,iBAAiB,CAACkH,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC;IACtD,MAAMtO,QAAQ,GAAGmB,eAAe,CAACO,IAAI,CAACC,MAAM,CAAC;IAC7CD,IAAI,CAACkK,aAAa,GAAGnL,aAAa,CAAC,KAAK,EAAE,gBAAgB,CAAC;IAC3DiB,IAAI,CAACkK,aAAa,CAAC6B,QAAQ,GAAG,CAAC,CAAC;IAChC,MAAMuG,SAAS,GAAGvT,aAAa,CAAC,MAAM,EAAE,0BAA0B,EAAE,GAAG,CAAC;IACxE,MAAMwT,SAAS,GAAGvT,iBAAiB,CAAC,gBAAgB,EAAE;MAClD,YAAY,EAAEgB,IAAI,CAACM,IAAI,CAACkS;IAC5B,CAAC,CAAC;IACFxS,IAAI,CAACoG,WAAW,GAAGmM,SAAS,CAAClB,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,MAAMoB,WAAW,GAAGzT,iBAAiB,CAAC,kBAAkB,EAAE;MACtD,YAAY,EAAEgB,IAAI,CAACM,IAAI,CAACoS;IAC5B,CAAC,CAAC;IACF1S,IAAI,CAACqG,aAAa,GAAGoM,WAAW,CAACpB,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjErR,IAAI,CAACoG,WAAW,CAAC2F,QAAQ,GAAG/L,IAAI,CAACqG,aAAa,CAAC0F,QAAQ,GAAG,CAAC,CAAC;IAC5D/L,IAAI,CAACoG,WAAW,CAACP,KAAK,GAAGhH,GAAG,CAACmB,IAAI,CAACoD,qBAAqB,GACjDpD,IAAI,CAACoD,qBAAqB,CAAC+D,QAAQ,CAAC,CAAC,GACrCnH,IAAI,CAACC,MAAM,CAACwH,SAAS,GACjBnJ,QAAQ,CAAC+G,KAAK,GACdc,aAAa,CAAC7H,QAAQ,CAAC+G,KAAK,CAAC,CAAC;IACxCrF,IAAI,CAACqG,aAAa,CAACR,KAAK,GAAGhH,GAAG,CAACmB,IAAI,CAACoD,qBAAqB,GACnDpD,IAAI,CAACoD,qBAAqB,CAACgE,UAAU,CAAC,CAAC,GACvC9I,QAAQ,CAACgH,OAAO,CAAC;IACvBtF,IAAI,CAACoG,WAAW,CAACoH,YAAY,CAAC,MAAM,EAAExN,IAAI,CAACC,MAAM,CAAC0S,aAAa,CAAC3K,QAAQ,CAAC,CAAC,CAAC;IAC3EhI,IAAI,CAACqG,aAAa,CAACmH,YAAY,CAAC,MAAM,EAAExN,IAAI,CAACC,MAAM,CAAC2S,eAAe,CAAC5K,QAAQ,CAAC,CAAC,CAAC;IAC/EhI,IAAI,CAACoG,WAAW,CAACoH,YAAY,CAAC,KAAK,EAAExN,IAAI,CAACC,MAAM,CAACwH,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC;IACvEzH,IAAI,CAACoG,WAAW,CAACoH,YAAY,CAAC,KAAK,EAAExN,IAAI,CAACC,MAAM,CAACwH,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IACzEzH,IAAI,CAACoG,WAAW,CAACoH,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;IAC/CxN,IAAI,CAACqG,aAAa,CAACmH,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC;IAC3CxN,IAAI,CAACqG,aAAa,CAACmH,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;IAC5CxN,IAAI,CAACqG,aAAa,CAACmH,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;IACjDxN,IAAI,CAACkK,aAAa,CAAC8B,WAAW,CAACuG,SAAS,CAAC;IACzCvS,IAAI,CAACkK,aAAa,CAAC8B,WAAW,CAACsG,SAAS,CAAC;IACzCtS,IAAI,CAACkK,aAAa,CAAC8B,WAAW,CAACyG,WAAW,CAAC;IAC3C,IAAIzS,IAAI,CAACC,MAAM,CAACwH,SAAS,EACrBzH,IAAI,CAACkK,aAAa,CAACmB,SAAS,CAACuB,GAAG,CAAC,UAAU,CAAC;IAChD,IAAI5M,IAAI,CAACC,MAAM,CAAC4S,aAAa,EAAE;MAC3B7S,IAAI,CAACkK,aAAa,CAACmB,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC;MAC9C,MAAMkG,WAAW,GAAG9T,iBAAiB,CAAC,kBAAkB,CAAC;MACzDgB,IAAI,CAACwG,aAAa,GAAGsM,WAAW,CAACzB,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACjErR,IAAI,CAACwG,aAAa,CAACX,KAAK,GAAGhH,GAAG,CAACmB,IAAI,CAACoD,qBAAqB,GACnDpD,IAAI,CAACoD,qBAAqB,CAACiE,UAAU,CAAC,CAAC,GACvC/I,QAAQ,CAACiH,OAAO,CAAC;MACvBvF,IAAI,CAACwG,aAAa,CAACgH,YAAY,CAAC,MAAM,EAAExN,IAAI,CAACqG,aAAa,CAAC0M,YAAY,CAAC,MAAM,CAAC,CAAC;MAChF/S,IAAI,CAACwG,aAAa,CAACgH,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC;MAC3CxN,IAAI,CAACwG,aAAa,CAACgH,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;MAC5CxN,IAAI,CAACwG,aAAa,CAACgH,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;MACjDxN,IAAI,CAACkK,aAAa,CAAC8B,WAAW,CAACjN,aAAa,CAAC,MAAM,EAAE,0BAA0B,EAAE,GAAG,CAAC,CAAC;MACtFiB,IAAI,CAACkK,aAAa,CAAC8B,WAAW,CAAC8G,WAAW,CAAC;IAC/C;IACA,IAAI,CAAC9S,IAAI,CAACC,MAAM,CAACwH,SAAS,EAAE;MACxBzH,IAAI,CAACkG,IAAI,GAAGnH,aAAa,CAAC,MAAM,EAAE,iBAAiB,EAAEiB,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAACtH,GAAG,CAAC,CAACoB,IAAI,CAACoD,qBAAqB,GAC7FpD,IAAI,CAACoG,WAAW,CAACP,KAAK,GACtB7F,IAAI,CAACC,MAAM,CAAC+S,WAAW,IAAI,EAAE,CAAC,CAAC,CAAC;MACtChT,IAAI,CAACkG,IAAI,CAAC+M,KAAK,GAAGjT,IAAI,CAACM,IAAI,CAAC4S,WAAW;MACvClT,IAAI,CAACkG,IAAI,CAAC6F,QAAQ,GAAG,CAAC,CAAC;MACvB/L,IAAI,CAACkK,aAAa,CAAC8B,WAAW,CAAChM,IAAI,CAACkG,IAAI,CAAC;IAC7C;IACA,OAAOlG,IAAI,CAACkK,aAAa;EAC7B;EACA,SAASmC,aAAaA,CAAA,EAAG;IACrB,IAAI,CAACrM,IAAI,CAACmT,gBAAgB,EACtBnT,IAAI,CAACmT,gBAAgB,GAAGpU,aAAa,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC,KAEnED,SAAS,CAACkB,IAAI,CAACmT,gBAAgB,CAAC;IACpC,KAAK,IAAIhG,CAAC,GAAGnN,IAAI,CAACC,MAAM,CAAC+D,UAAU,EAAEmJ,CAAC,EAAE,GAAG;MACvC,MAAM0D,SAAS,GAAG9R,aAAa,CAAC,KAAK,EAAE,4BAA4B,CAAC;MACpEiB,IAAI,CAACmT,gBAAgB,CAACnH,WAAW,CAAC6E,SAAS,CAAC;IAChD;IACAuC,cAAc,CAAC,CAAC;IAChB,OAAOpT,IAAI,CAACmT,gBAAgB;EAChC;EACA,SAASC,cAAcA,CAAA,EAAG;IACtB,IAAI,CAACpT,IAAI,CAACmT,gBAAgB,EAAE;MACxB;IACJ;IACA,MAAMxD,cAAc,GAAG3P,IAAI,CAACM,IAAI,CAACqP,cAAc;IAC/C,IAAI0D,QAAQ,GAAG,CAAC,GAAGrT,IAAI,CAACM,IAAI,CAAC+S,QAAQ,CAACC,SAAS,CAAC;IAChD,IAAI3D,cAAc,GAAG,CAAC,IAAIA,cAAc,GAAG0D,QAAQ,CAACpQ,MAAM,EAAE;MACxDoQ,QAAQ,GAAG,CACP,GAAGA,QAAQ,CAACE,MAAM,CAAC5D,cAAc,EAAE0D,QAAQ,CAACpQ,MAAM,CAAC,EACnD,GAAGoQ,QAAQ,CAACE,MAAM,CAAC,CAAC,EAAE5D,cAAc,CAAC,CACxC;IACL;IACA,KAAK,IAAIxC,CAAC,GAAGnN,IAAI,CAACC,MAAM,CAAC+D,UAAU,EAAEmJ,CAAC,EAAE,GAAG;MACvCnN,IAAI,CAACmT,gBAAgB,CAAC3E,QAAQ,CAACrB,CAAC,CAAC,CAACsD,SAAS,GAAI;AAC3D;AACA,UAAU4C,QAAQ,CAACG,IAAI,CAAC,yCAAyC,CAAE;AACnE;AACA,OAAO;IACC;EACJ;EACA,SAASrH,UAAUA,CAAA,EAAG;IAClBnM,IAAI,CAACmE,iBAAiB,CAACkH,SAAS,CAACuB,GAAG,CAAC,UAAU,CAAC;IAChD,MAAMhI,WAAW,GAAG7F,aAAa,CAAC,KAAK,EAAE,uBAAuB,CAAC;IACjE6F,WAAW,CAACoH,WAAW,CAACjN,aAAa,CAAC,MAAM,EAAE,mBAAmB,EAAEiB,IAAI,CAACM,IAAI,CAACmT,gBAAgB,CAAC,CAAC;IAC/F,MAAM1P,WAAW,GAAGhF,aAAa,CAAC,KAAK,EAAE,iBAAiB,CAAC;IAC3D6F,WAAW,CAACoH,WAAW,CAACjI,WAAW,CAAC;IACpC,OAAO;MACHa,WAAW;MACXb;IACJ,CAAC;EACL;EACA,SAAS9C,WAAWA,CAAC4E,KAAK,EAAE6N,QAAQ,GAAG,IAAI,EAAE;IACzC,MAAM5L,KAAK,GAAG4L,QAAQ,GAAG7N,KAAK,GAAGA,KAAK,GAAG7F,IAAI,CAACkC,YAAY;IAC1D,IAAK4F,KAAK,GAAG,CAAC,IAAI9H,IAAI,CAAC2T,mBAAmB,KAAK,IAAI,IAC9C7L,KAAK,GAAG,CAAC,IAAI9H,IAAI,CAAC4T,mBAAmB,KAAK,IAAK,EAChD;IACJ5T,IAAI,CAACkC,YAAY,IAAI4F,KAAK;IAC1B,IAAI9H,IAAI,CAACkC,YAAY,GAAG,CAAC,IAAIlC,IAAI,CAACkC,YAAY,GAAG,EAAE,EAAE;MACjDlC,IAAI,CAACoC,WAAW,IAAIpC,IAAI,CAACkC,YAAY,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;MACnDlC,IAAI,CAACkC,YAAY,GAAG,CAAClC,IAAI,CAACkC,YAAY,GAAG,EAAE,IAAI,EAAE;MACjD0B,YAAY,CAAC,cAAc,CAAC;MAC5BqH,gBAAgB,CAAC,CAAC;IACtB;IACAqB,SAAS,CAAC,CAAC;IACX1I,YAAY,CAAC,eAAe,CAAC;IAC7ByO,4BAA4B,CAAC,CAAC;EAClC;EACA,SAASlR,KAAKA,CAAC0S,kBAAkB,GAAG,IAAI,EAAEC,SAAS,GAAG,IAAI,EAAE;IACxD9T,IAAI,CAACuC,KAAK,CAACsD,KAAK,GAAG,EAAE;IACrB,IAAI7F,IAAI,CAACgN,QAAQ,KAAK3J,SAAS,EAC3BrD,IAAI,CAACgN,QAAQ,CAACnH,KAAK,GAAG,EAAE;IAC5B,IAAI7F,IAAI,CAAC+T,WAAW,KAAK1Q,SAAS,EAC9BrD,IAAI,CAAC+T,WAAW,CAAClO,KAAK,GAAG,EAAE;IAC/B7F,IAAI,CAACgD,aAAa,GAAG,EAAE;IACvBhD,IAAI,CAACoD,qBAAqB,GAAGC,SAAS;IACtC,IAAIyQ,SAAS,KAAK,IAAI,EAAE;MACpB9T,IAAI,CAACoC,WAAW,GAAGpC,IAAI,CAACgU,YAAY,CAACnJ,WAAW,CAAC,CAAC;MAClD7K,IAAI,CAACkC,YAAY,GAAGlC,IAAI,CAACgU,YAAY,CAAClJ,QAAQ,CAAC,CAAC;IACpD;IACA,IAAI9K,IAAI,CAACC,MAAM,CAACkD,UAAU,KAAK,IAAI,EAAE;MACjC,MAAM;QAAEkC,KAAK;QAAEC,OAAO;QAAEC;MAAQ,CAAC,GAAG9F,eAAe,CAACO,IAAI,CAACC,MAAM,CAAC;MAChEmF,QAAQ,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;IACrC;IACAvF,IAAI,CAAC0B,MAAM,CAAC,CAAC;IACb,IAAImS,kBAAkB,EAClBjQ,YAAY,CAAC,UAAU,CAAC;EAChC;EACA,SAASxC,KAAKA,CAAA,EAAG;IACbpB,IAAI,CAACwC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACxC,IAAI,CAAC6C,QAAQ,EAAE;MAChB,IAAI7C,IAAI,CAACmE,iBAAiB,KAAKd,SAAS,EAAE;QACtCrD,IAAI,CAACmE,iBAAiB,CAACkH,SAAS,CAAC5C,MAAM,CAAC,MAAM,CAAC;MACnD;MACA,IAAIzI,IAAI,CAAC4F,MAAM,KAAKvC,SAAS,EAAE;QAC3BrD,IAAI,CAAC4F,MAAM,CAACyF,SAAS,CAAC5C,MAAM,CAAC,QAAQ,CAAC;MAC1C;IACJ;IACA7E,YAAY,CAAC,SAAS,CAAC;EAC3B;EACA,SAAStC,OAAOA,CAAA,EAAG;IACf,IAAItB,IAAI,CAACC,MAAM,KAAKoD,SAAS,EACzBO,YAAY,CAAC,WAAW,CAAC;IAC7B,KAAK,IAAIuJ,CAAC,GAAGnN,IAAI,CAACQ,SAAS,CAACyC,MAAM,EAAEkK,CAAC,EAAE,GAAG;MACtCnN,IAAI,CAACQ,SAAS,CAAC2M,CAAC,CAAC,CAAC1E,MAAM,CAAC,CAAC;IAC9B;IACAzI,IAAI,CAACQ,SAAS,GAAG,EAAE;IACnB,IAAIR,IAAI,CAAC+T,WAAW,EAAE;MAClB,IAAI/T,IAAI,CAAC+T,WAAW,CAACtI,UAAU,EAC3BzL,IAAI,CAAC+T,WAAW,CAACtI,UAAU,CAACwI,WAAW,CAACjU,IAAI,CAAC+T,WAAW,CAAC;MAC7D/T,IAAI,CAAC+T,WAAW,GAAG1Q,SAAS;IAChC,CAAC,MACI,IAAIrD,IAAI,CAACmE,iBAAiB,IAAInE,IAAI,CAACmE,iBAAiB,CAACsH,UAAU,EAAE;MAClE,IAAIzL,IAAI,CAACC,MAAM,CAACyJ,MAAM,IAAI1J,IAAI,CAACmE,iBAAiB,CAACsH,UAAU,EAAE;QACzD,MAAMsB,OAAO,GAAG/M,IAAI,CAACmE,iBAAiB,CAACsH,UAAU;QACjDsB,OAAO,CAACmH,SAAS,IAAInH,OAAO,CAACkH,WAAW,CAAClH,OAAO,CAACmH,SAAS,CAAC;QAC3D,IAAInH,OAAO,CAACtB,UAAU,EAAE;UACpB,OAAOsB,OAAO,CAACrB,UAAU,EACrBqB,OAAO,CAACtB,UAAU,CAACoB,YAAY,CAACE,OAAO,CAACrB,UAAU,EAAEqB,OAAO,CAAC;UAChEA,OAAO,CAACtB,UAAU,CAACwI,WAAW,CAAClH,OAAO,CAAC;QAC3C;MACJ,CAAC,MAEG/M,IAAI,CAACmE,iBAAiB,CAACsH,UAAU,CAACwI,WAAW,CAACjU,IAAI,CAACmE,iBAAiB,CAAC;IAC7E;IACA,IAAInE,IAAI,CAACgN,QAAQ,EAAE;MACfhN,IAAI,CAACuC,KAAK,CAACkD,IAAI,GAAG,MAAM;MACxB,IAAIzF,IAAI,CAACgN,QAAQ,CAACvB,UAAU,EACxBzL,IAAI,CAACgN,QAAQ,CAACvB,UAAU,CAACwI,WAAW,CAACjU,IAAI,CAACgN,QAAQ,CAAC;MACvD,OAAOhN,IAAI,CAACgN,QAAQ;IACxB;IACA,IAAIhN,IAAI,CAACuC,KAAK,EAAE;MACZvC,IAAI,CAACuC,KAAK,CAACkD,IAAI,GAAGzF,IAAI,CAACuC,KAAK,CAAC4R,KAAK;MAClCnU,IAAI,CAACuC,KAAK,CAAC8I,SAAS,CAAC5C,MAAM,CAAC,iBAAiB,CAAC;MAC9CzI,IAAI,CAACuC,KAAK,CAAC6R,eAAe,CAAC,UAAU,CAAC;IAC1C;IACA,CACI,gBAAgB,EAChB,uBAAuB,EACvB,qBAAqB,EACrB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,MAAM,EACN,eAAe,EACf,QAAQ,EACR,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,kBAAkB,EAClB,cAAc,EACd,cAAc,EACd,yBAAyB,EACzB,qBAAqB,EACrB,oBAAoB,EACpB,wBAAwB,EACxB,kBAAkB,EAClB,QAAQ,CACX,CAAChM,OAAO,CAAEiM,CAAC,IAAK;MACb,IAAI;QACA,OAAOrU,IAAI,CAACqU,CAAC,CAAC;MAClB,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;IAChB,CAAC,CAAC;EACN;EACA,SAASC,cAAcA,CAACC,IAAI,EAAE;IAC1B,IAAIxU,IAAI,CAACC,MAAM,CAACyM,QAAQ,IAAI1M,IAAI,CAACC,MAAM,CAACyM,QAAQ,CAACpB,QAAQ,CAACkJ,IAAI,CAAC,EAC3D,OAAO,IAAI;IACf,OAAOxU,IAAI,CAACmE,iBAAiB,CAACmH,QAAQ,CAACkJ,IAAI,CAAC;EAChD;EACA,SAAS5K,aAAaA,CAAC7E,CAAC,EAAE;IACtB,IAAI/E,IAAI,CAACwC,MAAM,IAAI,CAACxC,IAAI,CAACC,MAAM,CAACwJ,MAAM,EAAE;MACpC,MAAM7B,WAAW,GAAGzI,cAAc,CAAC4F,CAAC,CAAC;MACrC,MAAM0P,iBAAiB,GAAGF,cAAc,CAAC3M,WAAW,CAAC;MACrD,MAAM8M,OAAO,GAAG9M,WAAW,KAAK5H,IAAI,CAACuC,KAAK,IACtCqF,WAAW,KAAK5H,IAAI,CAACgN,QAAQ,IAC7BhN,IAAI,CAACF,OAAO,CAACwL,QAAQ,CAAC1D,WAAW,CAAC,IACjC7C,CAAC,CAAC4P,IAAI,IACH5P,CAAC,CAAC4P,IAAI,CAACxJ,OAAO,KACb,CAACpG,CAAC,CAAC4P,IAAI,CAACxJ,OAAO,CAACnL,IAAI,CAACuC,KAAK,CAAC,IACxB,CAACwC,CAAC,CAAC4P,IAAI,CAACxJ,OAAO,CAACnL,IAAI,CAACgN,QAAQ,CAAC,CAAE;MAC5C,MAAM4H,SAAS,GAAG7P,CAAC,CAACU,IAAI,KAAK,MAAM,GAC7BiP,OAAO,IACL3P,CAAC,CAAC8P,aAAa,IACf,CAACN,cAAc,CAACxP,CAAC,CAAC8P,aAAa,CAAC,GAClC,CAACH,OAAO,IACN,CAACD,iBAAiB,IAClB,CAACF,cAAc,CAACxP,CAAC,CAAC8P,aAAa,CAAC;MACxC,MAAMC,SAAS,GAAG,CAAC9U,IAAI,CAACC,MAAM,CAAC8U,oBAAoB,CAACC,IAAI,CAAER,IAAI,IAAKA,IAAI,CAAClJ,QAAQ,CAAC1D,WAAW,CAAC,CAAC;MAC9F,IAAIgN,SAAS,IAAIE,SAAS,EAAE;QACxB,IAAI9U,IAAI,CAACkK,aAAa,KAAK7G,SAAS,IAChCrD,IAAI,CAACqG,aAAa,KAAKhD,SAAS,IAChCrD,IAAI,CAACoG,WAAW,KAAK/C,SAAS,IAC9BrD,IAAI,CAACuC,KAAK,CAACsD,KAAK,KAAK,EAAE,IACvB7F,IAAI,CAACuC,KAAK,CAACsD,KAAK,KAAKxC,SAAS,EAAE;UAChCyB,UAAU,CAAC,CAAC;QAChB;QACA9E,IAAI,CAACoB,KAAK,CAAC,CAAC;QACZ,IAAIpB,IAAI,CAACC,MAAM,IACXD,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,IAC5BpJ,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;UACjCjD,IAAI,CAACmB,KAAK,CAAC,KAAK,CAAC;UACjBnB,IAAI,CAAC0B,MAAM,CAAC,CAAC;QACjB;MACJ;IACJ;EACJ;EACA,SAASR,UAAUA,CAAC+T,OAAO,EAAE;IACzB,IAAI,CAACA,OAAO,IACPjV,IAAI,CAACC,MAAM,CAACgF,OAAO,IAAIgQ,OAAO,GAAGjV,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAE,IACnE7K,IAAI,CAACC,MAAM,CAAC8G,OAAO,IAAIkO,OAAO,GAAGjV,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAE,EACpE;IACJ,MAAMqK,UAAU,GAAGD,OAAO;MAAEE,SAAS,GAAGnV,IAAI,CAACoC,WAAW,KAAK8S,UAAU;IACvElV,IAAI,CAACoC,WAAW,GAAG8S,UAAU,IAAIlV,IAAI,CAACoC,WAAW;IACjD,IAAIpC,IAAI,CAACC,MAAM,CAAC8G,OAAO,IACnB/G,IAAI,CAACoC,WAAW,KAAKpC,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAC,EAAE;MACxD7K,IAAI,CAACkC,YAAY,GAAG+E,IAAI,CAACC,GAAG,CAAClH,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC+D,QAAQ,CAAC,CAAC,EAAE9K,IAAI,CAACkC,YAAY,CAAC;IACnF,CAAC,MACI,IAAIlC,IAAI,CAACC,MAAM,CAACgF,OAAO,IACxBjF,IAAI,CAACoC,WAAW,KAAKpC,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAC,EAAE;MACxD7K,IAAI,CAACkC,YAAY,GAAG+E,IAAI,CAACK,GAAG,CAACtH,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC6F,QAAQ,CAAC,CAAC,EAAE9K,IAAI,CAACkC,YAAY,CAAC;IACnF;IACA,IAAIiT,SAAS,EAAE;MACXnV,IAAI,CAAC0B,MAAM,CAAC,CAAC;MACbkC,YAAY,CAAC,cAAc,CAAC;MAC5BqH,gBAAgB,CAAC,CAAC;IACtB;EACJ;EACA,SAAS1J,SAASA,CAACiG,IAAI,EAAE4N,QAAQ,GAAG,IAAI,EAAE;IACtC,IAAIC,EAAE;IACN,MAAMC,WAAW,GAAGtV,IAAI,CAACO,SAAS,CAACiH,IAAI,EAAEnE,SAAS,EAAE+R,QAAQ,CAAC;IAC7D,IAAKpV,IAAI,CAACC,MAAM,CAACgF,OAAO,IACpBqQ,WAAW,IACXlW,YAAY,CAACkW,WAAW,EAAEtV,IAAI,CAACC,MAAM,CAACgF,OAAO,EAAEmQ,QAAQ,KAAK/R,SAAS,GAAG+R,QAAQ,GAAG,CAACpV,IAAI,CAAC4G,cAAc,CAAC,GAAG,CAAC,IAC3G5G,IAAI,CAACC,MAAM,CAAC8G,OAAO,IAChBuO,WAAW,IACXlW,YAAY,CAACkW,WAAW,EAAEtV,IAAI,CAACC,MAAM,CAAC8G,OAAO,EAAEqO,QAAQ,KAAK/R,SAAS,GAAG+R,QAAQ,GAAG,CAACpV,IAAI,CAACgH,cAAc,CAAC,GAAG,CAAE,EACjH,OAAO,KAAK;IAChB,IAAI,CAAChH,IAAI,CAACC,MAAM,CAACsV,MAAM,IAAIvV,IAAI,CAACC,MAAM,CAACuV,OAAO,CAACvS,MAAM,KAAK,CAAC,EACvD,OAAO,IAAI;IACf,IAAIqS,WAAW,KAAKjS,SAAS,EACzB,OAAO,KAAK;IAChB,MAAM6O,IAAI,GAAG,CAAC,CAAClS,IAAI,CAACC,MAAM,CAACsV,MAAM;MAAEE,KAAK,GAAG,CAACJ,EAAE,GAAGrV,IAAI,CAACC,MAAM,CAACsV,MAAM,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGrV,IAAI,CAACC,MAAM,CAACuV,OAAO;IACzH,KAAK,IAAIrI,CAAC,GAAG,CAAC,EAAEiD,CAAC,EAAEjD,CAAC,GAAGsI,KAAK,CAACxS,MAAM,EAAEkK,CAAC,EAAE,EAAE;MACtCiD,CAAC,GAAGqF,KAAK,CAACtI,CAAC,CAAC;MACZ,IAAI,OAAOiD,CAAC,KAAK,UAAU,IACvBA,CAAC,CAACkF,WAAW,CAAC,EACd,OAAOpD,IAAI,CAAC,KACX,IAAI9B,CAAC,YAAYlL,IAAI,IACtBoQ,WAAW,KAAKjS,SAAS,IACzB+M,CAAC,CAACjL,OAAO,CAAC,CAAC,KAAKmQ,WAAW,CAACnQ,OAAO,CAAC,CAAC,EACrC,OAAO+M,IAAI,CAAC,KACX,IAAI,OAAO9B,CAAC,KAAK,QAAQ,EAAE;QAC5B,MAAMsF,MAAM,GAAG1V,IAAI,CAACO,SAAS,CAAC6P,CAAC,EAAE/M,SAAS,EAAE,IAAI,CAAC;QACjD,OAAOqS,MAAM,IAAIA,MAAM,CAACvQ,OAAO,CAAC,CAAC,KAAKmQ,WAAW,CAACnQ,OAAO,CAAC,CAAC,GACrD+M,IAAI,GACJ,CAACA,IAAI;MACf,CAAC,MACI,IAAI,OAAO9B,CAAC,KAAK,QAAQ,IAC1BkF,WAAW,KAAKjS,SAAS,IACzB+M,CAAC,CAACuF,IAAI,IACNvF,CAAC,CAACwF,EAAE,IACJN,WAAW,CAACnQ,OAAO,CAAC,CAAC,IAAIiL,CAAC,CAACuF,IAAI,CAACxQ,OAAO,CAAC,CAAC,IACzCmQ,WAAW,CAACnQ,OAAO,CAAC,CAAC,IAAIiL,CAAC,CAACwF,EAAE,CAACzQ,OAAO,CAAC,CAAC,EACvC,OAAO+M,IAAI;IACnB;IACA,OAAO,CAACA,IAAI;EAChB;EACA,SAAS7C,QAAQA,CAACmF,IAAI,EAAE;IACpB,IAAIxU,IAAI,CAACuE,aAAa,KAAKlB,SAAS,EAChC,OAAQmR,IAAI,CAACtJ,SAAS,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAC3CqJ,IAAI,CAACtJ,SAAS,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IACnDnL,IAAI,CAACuE,aAAa,CAAC+G,QAAQ,CAACkJ,IAAI,CAAC;IACzC,OAAO,KAAK;EAChB;EACA,SAASjK,MAAMA,CAACxF,CAAC,EAAE;IACf,MAAM2P,OAAO,GAAG3P,CAAC,CAACyG,MAAM,KAAKxL,IAAI,CAAC4F,MAAM;IACxC,IAAI8O,OAAO,KACN1U,IAAI,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,IAAIjD,IAAI,CAAC4F,MAAM,CAACC,KAAK,CAAC5C,MAAM,GAAG,CAAC,CAAC,IAC/D,EAAE8B,CAAC,CAAC8P,aAAa,IAAIN,cAAc,CAACxP,CAAC,CAAC8P,aAAa,CAAC,CAAC,EAAE;MACvD7U,IAAI,CAAC4B,OAAO,CAAC5B,IAAI,CAAC4F,MAAM,CAACC,KAAK,EAAE,IAAI,EAAEd,CAAC,CAACyG,MAAM,KAAKxL,IAAI,CAACgN,QAAQ,GAC1DhN,IAAI,CAACC,MAAM,CAAC4V,SAAS,GACrB7V,IAAI,CAACC,MAAM,CAAC6V,UAAU,CAAC;IACjC;EACJ;EACA,SAAStM,SAASA,CAACzE,CAAC,EAAE;IAClB,MAAM6C,WAAW,GAAGzI,cAAc,CAAC4F,CAAC,CAAC;IACrC,MAAM2P,OAAO,GAAG1U,IAAI,CAACC,MAAM,CAAC2I,IAAI,GAC1B9I,OAAO,CAACwL,QAAQ,CAAC1D,WAAW,CAAC,GAC7BA,WAAW,KAAK5H,IAAI,CAAC4F,MAAM;IACjC,MAAM0E,UAAU,GAAGtK,IAAI,CAACC,MAAM,CAACqK,UAAU;IACzC,MAAMyL,YAAY,GAAG/V,IAAI,CAACwC,MAAM,KAAK,CAAC8H,UAAU,IAAI,CAACoK,OAAO,CAAC;IAC7D,MAAMsB,kBAAkB,GAAGhW,IAAI,CAACC,MAAM,CAACwJ,MAAM,IAAIiL,OAAO,IAAI,CAACpK,UAAU;IACvE,IAAIvF,CAAC,CAACkR,OAAO,KAAK,EAAE,IAAIvB,OAAO,EAAE;MAC7B,IAAIpK,UAAU,EAAE;QACZtK,IAAI,CAAC4B,OAAO,CAAC5B,IAAI,CAAC4F,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE+B,WAAW,KAAK5H,IAAI,CAACgN,QAAQ,GAC7DhN,IAAI,CAACC,MAAM,CAAC4V,SAAS,GACrB7V,IAAI,CAACC,MAAM,CAAC6V,UAAU,CAAC;QAC7B,OAAOlO,WAAW,CAACsO,IAAI,CAAC,CAAC;MAC7B,CAAC,MACI;QACDlW,IAAI,CAACyB,IAAI,CAAC,CAAC;MACf;IACJ,CAAC,MACI,IAAI8S,cAAc,CAAC3M,WAAW,CAAC,IAChCmO,YAAY,IACZC,kBAAkB,EAAE;MACpB,MAAMG,SAAS,GAAG,CAAC,CAACnW,IAAI,CAACkK,aAAa,IAClClK,IAAI,CAACkK,aAAa,CAACoB,QAAQ,CAAC1D,WAAW,CAAC;MAC5C,QAAQ7C,CAAC,CAACkR,OAAO;QACb,KAAK,EAAE;UACH,IAAIE,SAAS,EAAE;YACXpR,CAAC,CAACqR,cAAc,CAAC,CAAC;YAClBtR,UAAU,CAAC,CAAC;YACZuR,aAAa,CAAC,CAAC;UACnB,CAAC,MAEGpM,UAAU,CAAClF,CAAC,CAAC;UACjB;QACJ,KAAK,EAAE;UACHA,CAAC,CAACqR,cAAc,CAAC,CAAC;UAClBC,aAAa,CAAC,CAAC;UACf;QACJ,KAAK,CAAC;QACN,KAAK,EAAE;UACH,IAAI3B,OAAO,IAAI,CAAC1U,IAAI,CAACC,MAAM,CAACqK,UAAU,EAAE;YACpCvF,CAAC,CAACqR,cAAc,CAAC,CAAC;YAClBpW,IAAI,CAACmB,KAAK,CAAC,CAAC;UAChB;UACA;QACJ,KAAK,EAAE;QACP,KAAK,EAAE;UACH,IAAI,CAACgV,SAAS,IAAI,CAACzB,OAAO,EAAE;YACxB3P,CAAC,CAACqR,cAAc,CAAC,CAAC;YAClB,IAAIpW,IAAI,CAACuE,aAAa,KAAKlB,SAAS,KAC/BiH,UAAU,KAAK,KAAK,IAChBhB,QAAQ,CAACgG,aAAa,IAAID,QAAQ,CAAC/F,QAAQ,CAACgG,aAAa,CAAE,CAAC,EAAE;cACnE,MAAMxH,KAAK,GAAG/C,CAAC,CAACkR,OAAO,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;cACvC,IAAI,CAAClR,CAAC,CAACuR,OAAO,EACVpH,UAAU,CAAC7L,SAAS,EAAEyE,KAAK,CAAC,CAAC,KAC5B;gBACD/C,CAAC,CAACwR,eAAe,CAAC,CAAC;gBACnBtV,WAAW,CAAC6G,KAAK,CAAC;gBAClBoH,UAAU,CAACd,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC1C;YACJ;UACJ,CAAC,MACI,IAAIpO,IAAI,CAACoG,WAAW,EACrBpG,IAAI,CAACoG,WAAW,CAAC+H,KAAK,CAAC,CAAC;UAC5B;QACJ,KAAK,EAAE;QACP,KAAK,EAAE;UACHpJ,CAAC,CAACqR,cAAc,CAAC,CAAC;UAClB,MAAMtO,KAAK,GAAG/C,CAAC,CAACkR,OAAO,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;UACvC,IAAKjW,IAAI,CAACuE,aAAa,IACnBqD,WAAW,CAAC2F,EAAE,KAAKlK,SAAS,IAC5BuE,WAAW,KAAK5H,IAAI,CAACuC,KAAK,IAC1BqF,WAAW,KAAK5H,IAAI,CAACgN,QAAQ,EAAE;YAC/B,IAAIjI,CAAC,CAACuR,OAAO,EAAE;cACXvR,CAAC,CAACwR,eAAe,CAAC,CAAC;cACnBrV,UAAU,CAAClB,IAAI,CAACoC,WAAW,GAAG0F,KAAK,CAAC;cACpCoH,UAAU,CAACd,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1C,CAAC,MACI,IAAI,CAAC+H,SAAS,EACfjH,UAAU,CAAC7L,SAAS,EAAEyE,KAAK,GAAG,CAAC,CAAC;UACxC,CAAC,MACI,IAAIF,WAAW,KAAK5H,IAAI,CAACoS,kBAAkB,EAAE;YAC9ClR,UAAU,CAAClB,IAAI,CAACoC,WAAW,GAAG0F,KAAK,CAAC;UACxC,CAAC,MACI,IAAI9H,IAAI,CAACC,MAAM,CAACkD,UAAU,EAAE;YAC7B,IAAI,CAACgT,SAAS,IAAInW,IAAI,CAACoG,WAAW,EAC9BpG,IAAI,CAACoG,WAAW,CAAC+H,KAAK,CAAC,CAAC;YAC5BrJ,UAAU,CAACC,CAAC,CAAC;YACb/E,IAAI,CAAC+F,gBAAgB,CAAC,CAAC;UAC3B;UACA;QACJ,KAAK,CAAC;UACF,IAAIoQ,SAAS,EAAE;YACX,MAAMK,KAAK,GAAG,CACVxW,IAAI,CAACoG,WAAW,EAChBpG,IAAI,CAACqG,aAAa,EAClBrG,IAAI,CAACwG,aAAa,EAClBxG,IAAI,CAACkG,IAAI,CACZ,CACIuQ,MAAM,CAACzW,IAAI,CAACS,cAAc,CAAC,CAC3BiW,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC;YACrB,MAAMxJ,CAAC,GAAGqJ,KAAK,CAACrL,OAAO,CAACvD,WAAW,CAAC;YACpC,IAAIuF,CAAC,KAAK,CAAC,CAAC,EAAE;cACV,MAAM3B,MAAM,GAAGgL,KAAK,CAACrJ,CAAC,IAAIpI,CAAC,CAAC6R,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;cAC/C7R,CAAC,CAACqR,cAAc,CAAC,CAAC;cAClB,CAAC5K,MAAM,IAAIxL,IAAI,CAAC4F,MAAM,EAAEuI,KAAK,CAAC,CAAC;YACnC;UACJ,CAAC,MACI,IAAI,CAACnO,IAAI,CAACC,MAAM,CAACiD,UAAU,IAC5BlD,IAAI,CAACuE,aAAa,IAClBvE,IAAI,CAACuE,aAAa,CAAC+G,QAAQ,CAAC1D,WAAW,CAAC,IACxC7C,CAAC,CAAC6R,QAAQ,EAAE;YACZ7R,CAAC,CAACqR,cAAc,CAAC,CAAC;YAClBpW,IAAI,CAAC4F,MAAM,CAACuI,KAAK,CAAC,CAAC;UACvB;UACA;QACJ;UACI;MACR;IACJ;IACA,IAAInO,IAAI,CAACkG,IAAI,KAAK7C,SAAS,IAAIuE,WAAW,KAAK5H,IAAI,CAACkG,IAAI,EAAE;MACtD,QAAQnB,CAAC,CAACgD,GAAG;QACT,KAAK/H,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC2Q,MAAM,CAAC,CAAC,CAAC;QAChC,KAAK7W,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC2Q,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC1C9W,IAAI,CAACkG,IAAI,CAACO,WAAW,GAAGzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC;UACzCJ,kBAAkB,CAAC,CAAC;UACpBxC,WAAW,CAAC,CAAC;UACb;QACJ,KAAKtD,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC2Q,MAAM,CAAC,CAAC,CAAC;QAChC,KAAK7W,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC2Q,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC1C9W,IAAI,CAACkG,IAAI,CAACO,WAAW,GAAGzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC;UACzCJ,kBAAkB,CAAC,CAAC;UACpBxC,WAAW,CAAC,CAAC;UACb;MACR;IACJ;IACA,IAAIoR,OAAO,IAAIH,cAAc,CAAC3M,WAAW,CAAC,EAAE;MACxChE,YAAY,CAAC,WAAW,EAAEmB,CAAC,CAAC;IAChC;EACJ;EACA,SAASsE,WAAWA,CAACmL,IAAI,EAAE;IACvB,IAAIxU,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,IAC9BuR,IAAI,KACA,CAACA,IAAI,CAACnJ,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,IACtCkJ,IAAI,CAACnJ,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,CAAE,EACvD;IACJ,MAAMyL,SAAS,GAAGvC,IAAI,GAChBA,IAAI,CAACjN,OAAO,CAACpC,OAAO,CAAC,CAAC,GACtBnF,IAAI,CAACyE,IAAI,CAACuS,iBAAiB,CAACzP,OAAO,CAACpC,OAAO,CAAC,CAAC;MAAE8R,WAAW,GAAGjX,IAAI,CAACO,SAAS,CAACP,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,EAAEK,SAAS,EAAE,IAAI,CAAC,CAAC8B,OAAO,CAAC,CAAC;MAAE+R,cAAc,GAAGjQ,IAAI,CAACC,GAAG,CAAC6P,SAAS,EAAE/W,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,CAACmC,OAAO,CAAC,CAAC,CAAC;MAAEgS,YAAY,GAAGlQ,IAAI,CAACK,GAAG,CAACyP,SAAS,EAAE/W,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,CAACmC,OAAO,CAAC,CAAC,CAAC;IAC/Q,IAAIiS,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,QAAQ,GAAG,CAAC;MAAEC,QAAQ,GAAG,CAAC;IAC9B,KAAK,IAAIC,CAAC,GAAGL,cAAc,EAAEK,CAAC,GAAGJ,YAAY,EAAEI,CAAC,IAAIhY,QAAQ,CAACiY,GAAG,EAAE;MAC9D,IAAI,CAACjW,SAAS,CAAC,IAAI2D,IAAI,CAACqS,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;QAC/BH,gBAAgB,GACZA,gBAAgB,IAAKG,CAAC,GAAGL,cAAc,IAAIK,CAAC,GAAGJ,YAAa;QAChE,IAAII,CAAC,GAAGN,WAAW,KAAK,CAACI,QAAQ,IAAIE,CAAC,GAAGF,QAAQ,CAAC,EAC9CA,QAAQ,GAAGE,CAAC,CAAC,KACZ,IAAIA,CAAC,GAAGN,WAAW,KAAK,CAACK,QAAQ,IAAIC,CAAC,GAAGD,QAAQ,CAAC,EACnDA,QAAQ,GAAGC,CAAC;MACpB;IACJ;IACA,KAAK,IAAIhJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvO,IAAI,CAACC,MAAM,CAAC+D,UAAU,EAAEuK,CAAC,EAAE,EAAE;MAC7C,MAAMtM,KAAK,GAAGjC,IAAI,CAACuE,aAAa,CAACiK,QAAQ,CAACD,CAAC,CAAC;MAC5C,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEsK,CAAC,GAAGxV,KAAK,CAACuM,QAAQ,CAACvL,MAAM,EAAEkK,CAAC,GAAGsK,CAAC,EAAEtK,CAAC,EAAE,EAAE;QACnD,MAAMuK,OAAO,GAAGzV,KAAK,CAACuM,QAAQ,CAACrB,CAAC,CAAC;UAAE3F,IAAI,GAAGkQ,OAAO,CAACnQ,OAAO;QACzD,MAAMoQ,SAAS,GAAGnQ,IAAI,CAACrC,OAAO,CAAC,CAAC;QAChC,MAAMyS,UAAU,GAAIP,QAAQ,GAAG,CAAC,IAAIM,SAAS,GAAGN,QAAQ,IACnDC,QAAQ,GAAG,CAAC,IAAIK,SAAS,GAAGL,QAAS;QAC1C,IAAIM,UAAU,EAAE;UACZF,OAAO,CAACrM,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC;UACnC,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC,CAACxE,OAAO,CAAEuG,CAAC,IAAK;YACjD+I,OAAO,CAACrM,SAAS,CAAC5C,MAAM,CAACkG,CAAC,CAAC;UAC/B,CAAC,CAAC;UACF;QACJ,CAAC,MACI,IAAIyI,gBAAgB,IAAI,CAACQ,UAAU,EACpC;QACJ,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC,CAACxP,OAAO,CAAEuG,CAAC,IAAK;UAC/D+I,OAAO,CAACrM,SAAS,CAAC5C,MAAM,CAACkG,CAAC,CAAC;QAC/B,CAAC,CAAC;QACF,IAAI6F,IAAI,KAAKnR,SAAS,EAAE;UACpBmR,IAAI,CAACnJ,SAAS,CAACuB,GAAG,CAACmK,SAAS,IAAI/W,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,CAACmC,OAAO,CAAC,CAAC,GACzD,YAAY,GACZ,UAAU,CAAC;UACjB,IAAI8R,WAAW,GAAGF,SAAS,IAAIY,SAAS,KAAKV,WAAW,EACpDS,OAAO,CAACrM,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC,CAAC,KACnC,IAAIqK,WAAW,GAAGF,SAAS,IAAIY,SAAS,KAAKV,WAAW,EACzDS,OAAO,CAACrM,SAAS,CAACuB,GAAG,CAAC,UAAU,CAAC;UACrC,IAAI+K,SAAS,IAAIN,QAAQ,KACpBC,QAAQ,KAAK,CAAC,IAAIK,SAAS,IAAIL,QAAQ,CAAC,IACzC9X,SAAS,CAACmY,SAAS,EAAEV,WAAW,EAAEF,SAAS,CAAC,EAC5CW,OAAO,CAACrM,SAAS,CAACuB,GAAG,CAAC,SAAS,CAAC;QACxC;MACJ;IACJ;EACJ;EACA,SAASzD,QAAQA,CAAA,EAAG;IAChB,IAAInJ,IAAI,CAACwC,MAAM,IAAI,CAACxC,IAAI,CAACC,MAAM,CAACyJ,MAAM,IAAI,CAAC1J,IAAI,CAACC,MAAM,CAACwJ,MAAM,EACzDzI,gBAAgB,CAAC,CAAC;EAC1B;EACA,SAASS,IAAIA,CAACsD,CAAC,EAAE8S,eAAe,GAAG7X,IAAI,CAAC8X,gBAAgB,EAAE;IACtD,IAAI9X,IAAI,CAAC6C,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAIkC,CAAC,EAAE;QACHA,CAAC,CAACqR,cAAc,CAAC,CAAC;QAClB,MAAMxO,WAAW,GAAGzI,cAAc,CAAC4F,CAAC,CAAC;QACrC,IAAI6C,WAAW,EAAE;UACbA,WAAW,CAACsO,IAAI,CAAC,CAAC;QACtB;MACJ;MACA,IAAIlW,IAAI,CAAC+T,WAAW,KAAK1Q,SAAS,EAAE;QAChCrD,IAAI,CAAC+T,WAAW,CAAC5F,KAAK,CAAC,CAAC;QACxBnO,IAAI,CAAC+T,WAAW,CAACgE,KAAK,CAAC,CAAC;MAC5B;MACAnU,YAAY,CAAC,QAAQ,CAAC;MACtB;IACJ,CAAC,MACI,IAAI5D,IAAI,CAAC4F,MAAM,CAAC2L,QAAQ,IAAIvR,IAAI,CAACC,MAAM,CAACwJ,MAAM,EAAE;MACjD;IACJ;IACA,MAAMuO,OAAO,GAAGhY,IAAI,CAACwC,MAAM;IAC3BxC,IAAI,CAACwC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACwV,OAAO,EAAE;MACVhY,IAAI,CAACmE,iBAAiB,CAACkH,SAAS,CAACuB,GAAG,CAAC,MAAM,CAAC;MAC5C5M,IAAI,CAAC4F,MAAM,CAACyF,SAAS,CAACuB,GAAG,CAAC,QAAQ,CAAC;MACnChJ,YAAY,CAAC,QAAQ,CAAC;MACtB5C,gBAAgB,CAAC6W,eAAe,CAAC;IACrC;IACA,IAAI7X,IAAI,CAACC,MAAM,CAACkD,UAAU,KAAK,IAAI,IAAInD,IAAI,CAACC,MAAM,CAACiD,UAAU,KAAK,IAAI,EAAE;MACpE,IAAIlD,IAAI,CAACC,MAAM,CAACqK,UAAU,KAAK,KAAK,KAC/BvF,CAAC,KAAK1B,SAAS,IACZ,CAACrD,IAAI,CAACkK,aAAa,CAACoB,QAAQ,CAACvG,CAAC,CAAC8P,aAAa,CAAC,CAAC,EAAE;QACpDoD,UAAU,CAAC,MAAMjY,IAAI,CAACoG,WAAW,CAACgE,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;MACnD;IACJ;EACJ;EACA,SAAS8N,gBAAgBA,CAACzS,IAAI,EAAE;IAC5B,OAAQ+B,IAAI,IAAK;MACb,MAAMD,OAAO,GAAIvH,IAAI,CAACC,MAAM,CAAE,IAAGwF,IAAK,MAAK,CAAC,GAAGzF,IAAI,CAACO,SAAS,CAACiH,IAAI,EAAExH,IAAI,CAACC,MAAM,CAAC6V,UAAU,CAAE;MAC5F,MAAMqC,cAAc,GAAGnY,IAAI,CAACC,MAAM,CAAE,IAAGwF,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAM,MAAK,CAAC;MAC5E,IAAI8B,OAAO,KAAKlE,SAAS,EAAE;QACvBrD,IAAI,CAACyF,IAAI,KAAK,KAAK,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,GACtD8B,OAAO,CAACJ,QAAQ,CAAC,CAAC,GAAG,CAAC,IAClBI,OAAO,CAACH,UAAU,CAAC,CAAC,GAAG,CAAC,IACxBG,OAAO,CAACF,UAAU,CAAC,CAAC,GAAG,CAAC;MACpC;MACA,IAAIrH,IAAI,CAACgD,aAAa,EAAE;QACpBhD,IAAI,CAACgD,aAAa,GAAGhD,IAAI,CAACgD,aAAa,CAAC0T,MAAM,CAAEtG,CAAC,IAAK7O,SAAS,CAAC6O,CAAC,CAAC,CAAC;QACnE,IAAI,CAACpQ,IAAI,CAACgD,aAAa,CAACC,MAAM,IAAIwC,IAAI,KAAK,KAAK,EAC5C3E,gBAAgB,CAACyG,OAAO,CAAC;QAC7BjE,WAAW,CAAC,CAAC;MACjB;MACA,IAAItD,IAAI,CAACuE,aAAa,EAAE;QACpB7C,MAAM,CAAC,CAAC;QACR,IAAI6F,OAAO,KAAKlE,SAAS,EACrBrD,IAAI,CAACoS,kBAAkB,CAAC3M,IAAI,CAAC,GAAG8B,OAAO,CAACsD,WAAW,CAAC,CAAC,CAAC7C,QAAQ,CAAC,CAAC,CAAC,KAEjEhI,IAAI,CAACoS,kBAAkB,CAACgC,eAAe,CAAC3O,IAAI,CAAC;QACjDzF,IAAI,CAACoS,kBAAkB,CAACb,QAAQ,GAC5B,CAAC,CAAC4G,cAAc,IACZ5Q,OAAO,KAAKlE,SAAS,IACrB8U,cAAc,CAACtN,WAAW,CAAC,CAAC,KAAKtD,OAAO,CAACsD,WAAW,CAAC,CAAC;MAClE;IACJ,CAAC;EACL;EACA,SAASpI,WAAWA,CAAA,EAAG;IACnB,MAAM2V,QAAQ,GAAG,CACb,MAAM,EACN,aAAa,EACb,YAAY,EACZ,qBAAqB,EACrB,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,uBAAuB,EACvB,QAAQ,EACR,QAAQ,EACR,eAAe,EACf,eAAe,CAClB;IACD,MAAMC,UAAU,GAAGnY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEmY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC1Y,OAAO,CAAC2Y,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE1Y,cAAc,CAAC;IACtH,MAAM2Y,OAAO,GAAG,CAAC,CAAC;IAClB1Y,IAAI,CAACC,MAAM,CAACM,SAAS,GAAG8X,UAAU,CAAC9X,SAAS;IAC5CP,IAAI,CAACC,MAAM,CAACwN,UAAU,GAAG4K,UAAU,CAAC5K,UAAU;IAC9CvN,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,CAACC,MAAM,EAAE,QAAQ,EAAE;MACzC+R,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACC,MAAM,CAAC0Y,OAAO;MAC9BhX,GAAG,EAAGiX,KAAK,IAAK;QACZ5Y,IAAI,CAACC,MAAM,CAAC0Y,OAAO,GAAGE,cAAc,CAACD,KAAK,CAAC;MAC/C;IACJ,CAAC,CAAC;IACF1Y,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,CAACC,MAAM,EAAE,SAAS,EAAE;MAC1C+R,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACC,MAAM,CAAC6Y,QAAQ;MAC/BnX,GAAG,EAAGiX,KAAK,IAAK;QACZ5Y,IAAI,CAACC,MAAM,CAAC6Y,QAAQ,GAAGD,cAAc,CAACD,KAAK,CAAC;MAChD;IACJ,CAAC,CAAC;IACF,MAAMG,QAAQ,GAAGV,UAAU,CAACjP,IAAI,KAAK,MAAM;IAC3C,IAAI,CAACiP,UAAU,CAACvC,UAAU,KAAKuC,UAAU,CAAClV,UAAU,IAAI4V,QAAQ,CAAC,EAAE;MAC/D,MAAMC,iBAAiB,GAAG5Y,SAAS,CAACC,aAAa,CAACyV,UAAU,IAAIvX,cAAc,CAACuX,UAAU;MACzF4C,OAAO,CAAC5C,UAAU,GACduC,UAAU,CAACnV,UAAU,IAAI6V,QAAQ,GAC3B,KAAK,IAAIV,UAAU,CAACxF,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC,GAC9CmG,iBAAiB,GAAG,MAAM,IAAIX,UAAU,CAACxF,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;IACjF;IACA,IAAIwF,UAAU,CAACrL,QAAQ,KAClBqL,UAAU,CAAClV,UAAU,IAAI4V,QAAQ,CAAC,IACnC,CAACV,UAAU,CAACxC,SAAS,EAAE;MACvB,MAAMoD,gBAAgB,GAAG7Y,SAAS,CAACC,aAAa,CAACwV,SAAS,IAAItX,cAAc,CAACsX,SAAS;MACtF6C,OAAO,CAAC7C,SAAS,GACbwC,UAAU,CAACnV,UAAU,IAAI6V,QAAQ,GAC3B,KAAK,IAAIV,UAAU,CAACxF,aAAa,GAAG,MAAM,GAAG,IAAI,CAAC,GAClDoG,gBAAgB,GAAI,OAAMZ,UAAU,CAACxF,aAAa,GAAG,IAAI,GAAG,EAAG,IAAG;IAChF;IACA3S,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,CAACC,MAAM,EAAE,SAAS,EAAE;MAC1C+R,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACC,MAAM,CAACiZ,QAAQ;MAC/BvX,GAAG,EAAEuW,gBAAgB,CAAC,KAAK;IAC/B,CAAC,CAAC;IACFhY,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,CAACC,MAAM,EAAE,SAAS,EAAE;MAC1C+R,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACC,MAAM,CAACkZ,QAAQ;MAC/BxX,GAAG,EAAEuW,gBAAgB,CAAC,KAAK;IAC/B,CAAC,CAAC;IACF,MAAMkB,gBAAgB,GAAI3T,IAAI,IAAM4T,GAAG,IAAK;MACxCrZ,IAAI,CAACC,MAAM,CAACwF,IAAI,KAAK,KAAK,GAAG,UAAU,GAAG,UAAU,CAAC,GAAGzF,IAAI,CAACO,SAAS,CAAC8Y,GAAG,EAAE,OAAO,CAAC;IACxF,CAAC;IACDnZ,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,CAACC,MAAM,EAAE,SAAS,EAAE;MAC1C+R,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACC,MAAM,CAACqZ,QAAQ;MAC/B3X,GAAG,EAAEyX,gBAAgB,CAAC,KAAK;IAC/B,CAAC,CAAC;IACFlZ,MAAM,CAAC6R,cAAc,CAAC/R,IAAI,CAACC,MAAM,EAAE,SAAS,EAAE;MAC1C+R,GAAG,EAAEA,CAAA,KAAMhS,IAAI,CAACC,MAAM,CAACsZ,QAAQ;MAC/B5X,GAAG,EAAEyX,gBAAgB,CAAC,KAAK;IAC/B,CAAC,CAAC;IACF,IAAIf,UAAU,CAACjP,IAAI,KAAK,MAAM,EAAE;MAC5BpJ,IAAI,CAACC,MAAM,CAACiD,UAAU,GAAG,IAAI;MAC7BlD,IAAI,CAACC,MAAM,CAACkD,UAAU,GAAG,IAAI;IACjC;IACAjD,MAAM,CAACC,MAAM,CAACH,IAAI,CAACC,MAAM,EAAEyY,OAAO,EAAEL,UAAU,CAAC;IAC/C,KAAK,IAAIlL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiL,QAAQ,CAACnV,MAAM,EAAEkK,CAAC,EAAE,EACpCnN,IAAI,CAACC,MAAM,CAACmY,QAAQ,CAACjL,CAAC,CAAC,CAAC,GACpBnN,IAAI,CAACC,MAAM,CAACmY,QAAQ,CAACjL,CAAC,CAAC,CAAC,KAAK,IAAI,IAC7BnN,IAAI,CAACC,MAAM,CAACmY,QAAQ,CAACjL,CAAC,CAAC,CAAC,KAAK,MAAM;IAC/C3O,KAAK,CAACkY,MAAM,CAAE8C,IAAI,IAAKxZ,IAAI,CAACC,MAAM,CAACuZ,IAAI,CAAC,KAAKnW,SAAS,CAAC,CAAC+E,OAAO,CAAEoR,IAAI,IAAK;MACtExZ,IAAI,CAACC,MAAM,CAACuZ,IAAI,CAAC,GAAG9a,QAAQ,CAACsB,IAAI,CAACC,MAAM,CAACuZ,IAAI,CAAC,IAAI,EAAE,CAAC,CAACC,GAAG,CAAC5V,cAAc,CAAC;IAC7E,CAAC,CAAC;IACF7D,IAAI,CAAC6C,QAAQ,GACT,CAAC7C,IAAI,CAACC,MAAM,CAACyZ,aAAa,IACtB,CAAC1Z,IAAI,CAACC,MAAM,CAACwJ,MAAM,IACnBzJ,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,QAAQ,IAC7B,CAACpJ,IAAI,CAACC,MAAM,CAACuV,OAAO,CAACvS,MAAM,IAC3B,CAACjD,IAAI,CAACC,MAAM,CAACsV,MAAM,IACnB,CAACvV,IAAI,CAACC,MAAM,CAAC8D,WAAW,IACxB,gEAAgE,CAACN,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IAClG,KAAK,IAAIwJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnN,IAAI,CAACC,MAAM,CAAC0Z,OAAO,CAAC1W,MAAM,EAAEkK,CAAC,EAAE,EAAE;MACjD,MAAMyM,UAAU,GAAG5Z,IAAI,CAACC,MAAM,CAAC0Z,OAAO,CAACxM,CAAC,CAAC,CAACnN,IAAI,CAAC,IAAI,CAAC,CAAC;MACrD,KAAK,MAAM+H,GAAG,IAAI6R,UAAU,EAAE;QAC1B,IAAIpb,KAAK,CAAC2M,OAAO,CAACpD,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;UACzB/H,IAAI,CAACC,MAAM,CAAC8H,GAAG,CAAC,GAAGrJ,QAAQ,CAACkb,UAAU,CAAC7R,GAAG,CAAC,CAAC,CACvC0R,GAAG,CAAC5V,cAAc,CAAC,CACnB4S,MAAM,CAACzW,IAAI,CAACC,MAAM,CAAC8H,GAAG,CAAC,CAAC;QACjC,CAAC,MACI,IAAI,OAAOsQ,UAAU,CAACtQ,GAAG,CAAC,KAAK,WAAW,EAC3C/H,IAAI,CAACC,MAAM,CAAC8H,GAAG,CAAC,GAAG6R,UAAU,CAAC7R,GAAG,CAAC;MAC1C;IACJ;IACA,IAAI,CAACsQ,UAAU,CAACwB,aAAa,EAAE;MAC3B7Z,IAAI,CAACC,MAAM,CAAC4Z,aAAa,GACrBC,YAAY,CAAC,CAAC,CAAC5O,SAAS,GAAG,GAAG,GAAGlL,IAAI,CAACC,MAAM,CAAC4Z,aAAa;IAClE;IACAjW,YAAY,CAAC,eAAe,CAAC;EACjC;EACA,SAASkW,YAAYA,CAAA,EAAG;IACpB,OAAO9Z,IAAI,CAACC,MAAM,CAAC2I,IAAI,GACjB9I,OAAO,CAACia,aAAa,CAAC,cAAc,CAAC,GACrCja,OAAO;EACjB;EACA,SAAS4C,WAAWA,CAAA,EAAG;IACnB,IAAI,OAAO1C,IAAI,CAACC,MAAM,CAAC+Z,MAAM,KAAK,QAAQ,IACtC,OAAO5Z,SAAS,CAAC6Z,KAAK,CAACja,IAAI,CAACC,MAAM,CAAC+Z,MAAM,CAAC,KAAK,WAAW,EAC1Dha,IAAI,CAACC,MAAM,CAAC+K,YAAY,CAAC,IAAIkP,KAAK,CAAE,6BAA4Bla,IAAI,CAACC,MAAM,CAAC+Z,MAAO,EAAC,CAAC,CAAC;IAC1Fha,IAAI,CAACM,IAAI,GAAGJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC6Z,KAAK,CAACE,OAAO,CAAC,EAAG,OAAOna,IAAI,CAACC,MAAM,CAAC+Z,MAAM,KAAK,QAAQ,GACvGha,IAAI,CAACC,MAAM,CAAC+Z,MAAM,GAClBha,IAAI,CAACC,MAAM,CAAC+Z,MAAM,KAAK,SAAS,GAC5B5Z,SAAS,CAAC6Z,KAAK,CAACja,IAAI,CAACC,MAAM,CAAC+Z,MAAM,CAAC,GACnC3W,SAAU,CAAC;IACrB3D,UAAU,CAAC0a,CAAC,GAAI,IAAGpa,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAE,IAAGlG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAE,IAAGlG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC4Q,WAAW,CAAC,CAAE,IAAG9W,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC4Q,WAAW,CAAC,CAAE,GAAE;IAClI,MAAMuB,UAAU,GAAGnY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,cAAc,CAAC,EAAEuY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC1Y,OAAO,CAAC2Y,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACtH,IAAIJ,UAAU,CAAC5Q,SAAS,KAAKpE,SAAS,IAClCjD,SAAS,CAACC,aAAa,CAACoH,SAAS,KAAKpE,SAAS,EAAE;MACjDrD,IAAI,CAACC,MAAM,CAACwH,SAAS,GAAGzH,IAAI,CAACM,IAAI,CAACmH,SAAS;IAC/C;IACAzH,IAAI,CAACyN,UAAU,GAAGnO,mBAAmB,CAACU,IAAI,CAAC;IAC3CA,IAAI,CAACO,SAAS,GAAGlB,gBAAgB,CAAC;MAAEY,MAAM,EAAED,IAAI,CAACC,MAAM;MAAEK,IAAI,EAAEN,IAAI,CAACM;IAAK,CAAC,CAAC;EAC/E;EACA,SAASU,gBAAgBA,CAACqZ,qBAAqB,EAAE;IAC7C,IAAI,OAAOra,IAAI,CAACC,MAAM,CAACqa,QAAQ,KAAK,UAAU,EAAE;MAC5C,OAAO,KAAKta,IAAI,CAACC,MAAM,CAACqa,QAAQ,CAACta,IAAI,EAAEqa,qBAAqB,CAAC;IACjE;IACA,IAAIra,IAAI,CAACmE,iBAAiB,KAAKd,SAAS,EACpC;IACJO,YAAY,CAAC,uBAAuB,CAAC;IACrC,MAAMiU,eAAe,GAAGwC,qBAAqB,IAAIra,IAAI,CAAC8X,gBAAgB;IACtE,MAAMyC,cAAc,GAAGpS,KAAK,CAACW,SAAS,CAAC0R,MAAM,CAACzR,IAAI,CAAC/I,IAAI,CAACmE,iBAAiB,CAACqK,QAAQ,EAAG,CAACiM,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,YAAY,EAAG,CAAC,CAAC;MAAEC,aAAa,GAAG5a,IAAI,CAACmE,iBAAiB,CAACO,WAAW;MAAEmW,SAAS,GAAG7a,IAAI,CAACC,MAAM,CAACqa,QAAQ,CAACQ,KAAK,CAAC,GAAG,CAAC;MAAEC,iBAAiB,GAAGF,SAAS,CAAC,CAAC,CAAC;MAAEG,mBAAmB,GAAGH,SAAS,CAAC5X,MAAM,GAAG,CAAC,GAAG4X,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAAEI,WAAW,GAAGpD,eAAe,CAACqD,qBAAqB,CAAC,CAAC;MAAEC,kBAAkB,GAAGlX,MAAM,CAACmX,WAAW,GAAGH,WAAW,CAACI,MAAM;MAAEC,SAAS,GAAGP,iBAAiB,KAAK,OAAO,IACleA,iBAAiB,KAAK,OAAO,IAC1BI,kBAAkB,GAAGZ,cAAc,IACnCU,WAAW,CAACM,GAAG,GAAGhB,cAAe;IACzC,MAAMgB,GAAG,GAAGtX,MAAM,CAACuX,WAAW,GAC1BP,WAAW,CAACM,GAAG,IACd,CAACD,SAAS,GAAGzD,eAAe,CAAC8C,YAAY,GAAG,CAAC,GAAG,CAACJ,cAAc,GAAG,CAAC,CAAC;IACzErb,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,UAAU,EAAE,CAACmX,SAAS,CAAC;IAC3Dpc,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,aAAa,EAAEmX,SAAS,CAAC;IAC7D,IAAItb,IAAI,CAACC,MAAM,CAACwJ,MAAM,EAClB;IACJ,IAAIgS,IAAI,GAAGxX,MAAM,CAACyX,WAAW,GAAGT,WAAW,CAACQ,IAAI;IAChD,IAAIE,QAAQ,GAAG,KAAK;IACpB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIZ,mBAAmB,KAAK,QAAQ,EAAE;MAClCS,IAAI,IAAI,CAACb,aAAa,GAAGK,WAAW,CAACtW,KAAK,IAAI,CAAC;MAC/CgX,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAIX,mBAAmB,KAAK,OAAO,EAAE;MACtCS,IAAI,IAAIb,aAAa,GAAGK,WAAW,CAACtW,KAAK;MACzCiX,OAAO,GAAG,IAAI;IAClB;IACA1c,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,WAAW,EAAE,CAACwX,QAAQ,IAAI,CAACC,OAAO,CAAC;IACvE1c,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,aAAa,EAAEwX,QAAQ,CAAC;IAC5Dzc,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,YAAY,EAAEyX,OAAO,CAAC;IAC1D,MAAMC,KAAK,GAAG5X,MAAM,CAACqF,QAAQ,CAACC,IAAI,CAAC7E,WAAW,IACzCT,MAAM,CAACyX,WAAW,GAAGT,WAAW,CAACY,KAAK,CAAC;IAC5C,MAAMC,SAAS,GAAGL,IAAI,GAAGb,aAAa,GAAG3W,MAAM,CAACqF,QAAQ,CAACC,IAAI,CAAC7E,WAAW;IACzE,MAAMqX,UAAU,GAAGF,KAAK,GAAGjB,aAAa,GAAG3W,MAAM,CAACqF,QAAQ,CAACC,IAAI,CAAC7E,WAAW;IAC3ExF,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,WAAW,EAAE2X,SAAS,CAAC;IAC3D,IAAI9b,IAAI,CAACC,MAAM,CAACyJ,MAAM,EAClB;IACJ1J,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACmX,GAAG,GAAI,GAAEA,GAAI,IAAG;IAC7C,IAAI,CAACO,SAAS,EAAE;MACZ9b,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACqX,IAAI,GAAI,GAAEA,IAAK,IAAG;MAC/Czb,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACyX,KAAK,GAAG,MAAM;IAC/C,CAAC,MACI,IAAI,CAACE,UAAU,EAAE;MAClB/b,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACqX,IAAI,GAAG,MAAM;MAC1Czb,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACyX,KAAK,GAAI,GAAEA,KAAM,IAAG;IACrD,CAAC,MACI;MACD,MAAMG,GAAG,GAAGC,qBAAqB,CAAC,CAAC;MACnC,IAAID,GAAG,KAAK3Y,SAAS,EACjB;MACJ,MAAM6Y,SAAS,GAAGjY,MAAM,CAACqF,QAAQ,CAACC,IAAI,CAAC7E,WAAW;MAClD,MAAMyX,UAAU,GAAGlV,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE4U,SAAS,GAAG,CAAC,GAAGtB,aAAa,GAAG,CAAC,CAAC;MACjE,MAAMwB,YAAY,GAAG,uCAAuC;MAC5D,MAAMC,WAAW,GAAG,sCAAsC;MAC1D,MAAMC,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAACtZ,MAAM;MACvC,MAAMuZ,WAAW,GAAI,SAAQvB,WAAW,CAACQ,IAAK,iBAAgB;MAC9Dvc,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,WAAW,EAAE,KAAK,CAAC;MACvDjF,WAAW,CAACc,IAAI,CAACmE,iBAAiB,EAAE,YAAY,EAAE,IAAI,CAAC;MACvD6X,GAAG,CAACS,UAAU,CAAE,GAAEL,YAAa,IAAGC,WAAY,GAAEG,WAAY,EAAC,EAAEF,WAAW,CAAC;MAC3Etc,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACqX,IAAI,GAAI,GAAEU,UAAW,IAAG;MACrDnc,IAAI,CAACmE,iBAAiB,CAACC,KAAK,CAACyX,KAAK,GAAG,MAAM;IAC/C;EACJ;EACA,SAASI,qBAAqBA,CAAA,EAAG;IAC7B,IAAIS,aAAa,GAAG,IAAI;IACxB,KAAK,IAAIvP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7D,QAAQ,CAACqT,WAAW,CAAC1Z,MAAM,EAAEkK,CAAC,EAAE,EAAE;MAClD,MAAMyP,KAAK,GAAGtT,QAAQ,CAACqT,WAAW,CAACxP,CAAC,CAAC;MACrC,IAAI;QACAyP,KAAK,CAACL,QAAQ;MAClB,CAAC,CACD,OAAOM,GAAG,EAAE;QACR;MACJ;MACAH,aAAa,GAAGE,KAAK;MACrB;IACJ;IACA,OAAOF,aAAa,IAAI,IAAI,GAAGA,aAAa,GAAGI,gBAAgB,CAAC,CAAC;EACrE;EACA,SAASA,gBAAgBA,CAAA,EAAG;IACxB,MAAM1Y,KAAK,GAAGkF,QAAQ,CAACvK,aAAa,CAAC,OAAO,CAAC;IAC7CuK,QAAQ,CAACyT,IAAI,CAAC/Q,WAAW,CAAC5H,KAAK,CAAC;IAChC,OAAOA,KAAK,CAACwY,KAAK;EACtB;EACA,SAASlb,MAAMA,CAAA,EAAG;IACd,IAAI1B,IAAI,CAACC,MAAM,CAACiD,UAAU,IAAIlD,IAAI,CAAC6C,QAAQ,EACvC;IACJoI,gBAAgB,CAAC,CAAC;IAClBoH,4BAA4B,CAAC,CAAC;IAC9B/F,SAAS,CAAC,CAAC;EACf;EACA,SAAS+J,aAAaA,CAAA,EAAG;IACrBrW,IAAI,CAAC4F,MAAM,CAACuI,KAAK,CAAC,CAAC;IACnB,IAAIlK,MAAM,CAACP,SAAS,CAACC,SAAS,CAACwH,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IACjDzH,SAAS,CAACsZ,gBAAgB,KAAK3Z,SAAS,EAAE;MAC1C4U,UAAU,CAACjY,IAAI,CAACoB,KAAK,EAAE,CAAC,CAAC;IAC7B,CAAC,MACI;MACDpB,IAAI,CAACoB,KAAK,CAAC,CAAC;IAChB;EACJ;EACA,SAAS6I,UAAUA,CAAClF,CAAC,EAAE;IACnBA,CAAC,CAACqR,cAAc,CAAC,CAAC;IAClBrR,CAAC,CAACwR,eAAe,CAAC,CAAC;IACnB,MAAM0G,YAAY,GAAIC,GAAG,IAAKA,GAAG,CAAC7R,SAAS,IACvC6R,GAAG,CAAC7R,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,IACvC,CAAC4R,GAAG,CAAC7R,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAC7C,CAAC4R,GAAG,CAAC7R,SAAS,CAACC,QAAQ,CAAC,YAAY,CAAC;IACzC,MAAMiM,CAAC,GAAGtY,UAAU,CAACE,cAAc,CAAC4F,CAAC,CAAC,EAAEkY,YAAY,CAAC;IACrD,IAAI1F,CAAC,KAAKlU,SAAS,EACf;IACJ,MAAMmI,MAAM,GAAG+L,CAAC;IAChB,MAAM4F,YAAY,GAAInd,IAAI,CAACoD,qBAAqB,GAAG,IAAI8B,IAAI,CAACsG,MAAM,CAACjE,OAAO,CAACpC,OAAO,CAAC,CAAC,CAAE;IACtF,MAAMiY,iBAAiB,GAAG,CAACD,YAAY,CAACrS,QAAQ,CAAC,CAAC,GAAG9K,IAAI,CAACkC,YAAY,IAClEib,YAAY,CAACrS,QAAQ,CAAC,CAAC,GACnB9K,IAAI,CAACkC,YAAY,GAAGlC,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,KAClDhE,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO;IAChCpJ,IAAI,CAAC6N,gBAAgB,GAAGrC,MAAM;IAC9B,IAAIxL,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,QAAQ,EAC7BpJ,IAAI,CAACgD,aAAa,GAAG,CAACma,YAAY,CAAC,CAAC,KACnC,IAAInd,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,UAAU,EAAE;MACtC,MAAMiU,aAAa,GAAGzP,cAAc,CAACuP,YAAY,CAAC;MAClD,IAAIE,aAAa,EACbrd,IAAI,CAACgD,aAAa,CAACuQ,MAAM,CAACjN,QAAQ,CAAC+W,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,KAEtDrd,IAAI,CAACgD,aAAa,CAACwF,IAAI,CAAC2U,YAAY,CAAC;IAC7C,CAAC,MACI,IAAInd,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,EAAE;MACnC,IAAIpJ,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;QACjCjD,IAAI,CAACmB,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC;MAC5B;MACAnB,IAAI,CAACoD,qBAAqB,GAAG+Z,YAAY;MACzCnd,IAAI,CAACgD,aAAa,CAACwF,IAAI,CAAC2U,YAAY,CAAC;MACrC,IAAI/d,YAAY,CAAC+d,YAAY,EAAEnd,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAC7DhD,IAAI,CAACgD,aAAa,CAACsa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpY,OAAO,CAAC,CAAC,GAAGqY,CAAC,CAACrY,OAAO,CAAC,CAAC,CAAC;IACpE;IACAW,kBAAkB,CAAC,CAAC;IACpB,IAAIsX,iBAAiB,EAAE;MACnB,MAAMjI,SAAS,GAAGnV,IAAI,CAACoC,WAAW,KAAK+a,YAAY,CAACtS,WAAW,CAAC,CAAC;MACjE7K,IAAI,CAACoC,WAAW,GAAG+a,YAAY,CAACtS,WAAW,CAAC,CAAC;MAC7C7K,IAAI,CAACkC,YAAY,GAAGib,YAAY,CAACrS,QAAQ,CAAC,CAAC;MAC3C,IAAIqK,SAAS,EAAE;QACXvR,YAAY,CAAC,cAAc,CAAC;QAC5BqH,gBAAgB,CAAC,CAAC;MACtB;MACArH,YAAY,CAAC,eAAe,CAAC;IACjC;IACAyO,4BAA4B,CAAC,CAAC;IAC9B/F,SAAS,CAAC,CAAC;IACXhJ,WAAW,CAAC,CAAC;IACb,IAAI,CAAC8Z,iBAAiB,IAClBpd,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,IAC5BpJ,IAAI,CAACC,MAAM,CAAC+D,UAAU,KAAK,CAAC,EAC5BiK,cAAc,CAACzC,MAAM,CAAC,CAAC,KACtB,IAAIxL,IAAI,CAAC6N,gBAAgB,KAAKxK,SAAS,IACxCrD,IAAI,CAACoG,WAAW,KAAK/C,SAAS,EAAE;MAChCrD,IAAI,CAAC6N,gBAAgB,IAAI7N,IAAI,CAAC6N,gBAAgB,CAACM,KAAK,CAAC,CAAC;IAC1D;IACA,IAAInO,IAAI,CAACoG,WAAW,KAAK/C,SAAS,EAC9BrD,IAAI,CAACoG,WAAW,KAAK/C,SAAS,IAAIrD,IAAI,CAACoG,WAAW,CAAC+H,KAAK,CAAC,CAAC;IAC9D,IAAInO,IAAI,CAACC,MAAM,CAACwd,aAAa,EAAE;MAC3B,MAAMC,MAAM,GAAG1d,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,QAAQ,IAAI,CAACpJ,IAAI,CAACC,MAAM,CAACkD,UAAU;MACvE,MAAMwa,KAAK,GAAG3d,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,IACtCpJ,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,IAC/B,CAACjD,IAAI,CAACC,MAAM,CAACkD,UAAU;MAC3B,IAAIua,MAAM,IAAIC,KAAK,EAAE;QACjBtH,aAAa,CAAC,CAAC;MACnB;IACJ;IACA1N,aAAa,CAAC,CAAC;EACnB;EACA,MAAMiV,SAAS,GAAG;IACd5D,MAAM,EAAE,CAACtX,WAAW,EAAE0Q,cAAc,CAAC;IACrCpP,UAAU,EAAE,CAACwN,WAAW,EAAEjO,gBAAgB,EAAE8I,aAAa,CAAC;IAC1DpH,OAAO,EAAE,CAACzD,UAAU,CAAC;IACrBuF,OAAO,EAAE,CAACvF,UAAU,CAAC;IACrBsI,UAAU,EAAE,CACR,MAAM;MACF,IAAI9J,IAAI,CAACC,MAAM,CAAC6J,UAAU,KAAK,IAAI,EAAE;QACjClJ,IAAI,CAACZ,IAAI,CAAC4F,MAAM,EAAE,OAAO,EAAE5F,IAAI,CAACyB,IAAI,CAAC;QACrCb,IAAI,CAACZ,IAAI,CAAC4F,MAAM,EAAE,OAAO,EAAE5F,IAAI,CAACyB,IAAI,CAAC;MACzC,CAAC,MACI;QACDzB,IAAI,CAAC4F,MAAM,CAAC8C,mBAAmB,CAAC,OAAO,EAAE1I,IAAI,CAACyB,IAAI,CAAC;QACnDzB,IAAI,CAAC4F,MAAM,CAAC8C,mBAAmB,CAAC,OAAO,EAAE1I,IAAI,CAACyB,IAAI,CAAC;MACvD;IACJ,CAAC;EAET,CAAC;EACD,SAASE,GAAGA,CAACkc,MAAM,EAAEhY,KAAK,EAAE;IACxB,IAAIgY,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC/C3d,MAAM,CAACC,MAAM,CAACH,IAAI,CAACC,MAAM,EAAE4d,MAAM,CAAC;MAClC,KAAK,MAAM9V,GAAG,IAAI8V,MAAM,EAAE;QACtB,IAAID,SAAS,CAAC7V,GAAG,CAAC,KAAK1E,SAAS,EAC5Bua,SAAS,CAAC7V,GAAG,CAAC,CAACK,OAAO,CAAEuO,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC;MAC1C;IACJ,CAAC,MACI;MACD3W,IAAI,CAACC,MAAM,CAAC4d,MAAM,CAAC,GAAGhY,KAAK;MAC3B,IAAI+X,SAAS,CAACC,MAAM,CAAC,KAAKxa,SAAS,EAC/Bua,SAAS,CAACC,MAAM,CAAC,CAACzV,OAAO,CAAEuO,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,KACrC,IAAInY,KAAK,CAAC2M,OAAO,CAAC0S,MAAM,CAAC,GAAG,CAAC,CAAC,EAC/B7d,IAAI,CAACC,MAAM,CAAC4d,MAAM,CAAC,GAAGnf,QAAQ,CAACmH,KAAK,CAAC;IAC7C;IACA7F,IAAI,CAAC0B,MAAM,CAAC,CAAC;IACb4B,WAAW,CAAC,IAAI,CAAC;EACrB;EACA,SAASwa,eAAeA,CAACC,SAAS,EAAEC,MAAM,EAAE;IACxC,IAAIpF,KAAK,GAAG,EAAE;IACd,IAAImF,SAAS,YAAY5V,KAAK,EAC1ByQ,KAAK,GAAGmF,SAAS,CAACtE,GAAG,CAAErJ,CAAC,IAAKpQ,IAAI,CAACO,SAAS,CAAC6P,CAAC,EAAE4N,MAAM,CAAC,CAAC,CAAC,KACvD,IAAID,SAAS,YAAY7Y,IAAI,IAAI,OAAO6Y,SAAS,KAAK,QAAQ,EAC/DnF,KAAK,GAAG,CAAC5Y,IAAI,CAACO,SAAS,CAACwd,SAAS,EAAEC,MAAM,CAAC,CAAC,CAAC,KAC3C,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;MACpC,QAAQ/d,IAAI,CAACC,MAAM,CAACmJ,IAAI;QACpB,KAAK,QAAQ;QACb,KAAK,MAAM;UACPwP,KAAK,GAAG,CAAC5Y,IAAI,CAACO,SAAS,CAACwd,SAAS,EAAEC,MAAM,CAAC,CAAC;UAC3C;QACJ,KAAK,UAAU;UACXpF,KAAK,GAAGmF,SAAS,CACZjD,KAAK,CAAC9a,IAAI,CAACC,MAAM,CAACge,WAAW,CAAC,CAC9BxE,GAAG,CAAEjS,IAAI,IAAKxH,IAAI,CAACO,SAAS,CAACiH,IAAI,EAAEwW,MAAM,CAAC,CAAC;UAChD;QACJ,KAAK,OAAO;UACRpF,KAAK,GAAGmF,SAAS,CACZjD,KAAK,CAAC9a,IAAI,CAACM,IAAI,CAAC4d,cAAc,CAAC,CAC/BzE,GAAG,CAAEjS,IAAI,IAAKxH,IAAI,CAACO,SAAS,CAACiH,IAAI,EAAEwW,MAAM,CAAC,CAAC;UAChD;QACJ;UACI;MACR;IACJ,CAAC,MAEGhe,IAAI,CAACC,MAAM,CAAC+K,YAAY,CAAC,IAAIkP,KAAK,CAAE,0BAAyB5B,IAAI,CAACE,SAAS,CAACuF,SAAS,CAAE,EAAC,CAAC,CAAC;IAC9F/d,IAAI,CAACgD,aAAa,GAAIhD,IAAI,CAACC,MAAM,CAACke,mBAAmB,GAC/CvF,KAAK,GACLA,KAAK,CAAClC,MAAM,CAAEtG,CAAC,IAAKA,CAAC,YAAYlL,IAAI,IAAI3D,SAAS,CAAC6O,CAAC,EAAE,KAAK,CAAC,CAAE;IACpE,IAAIpQ,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,EAC5BpJ,IAAI,CAACgD,aAAa,CAACsa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpY,OAAO,CAAC,CAAC,GAAGqY,CAAC,CAACrY,OAAO,CAAC,CAAC,CAAC;EACpE;EACA,SAASvD,OAAOA,CAAC4F,IAAI,EAAEmB,aAAa,GAAG,KAAK,EAAEqV,MAAM,GAAGhe,IAAI,CAACC,MAAM,CAAC6V,UAAU,EAAE;IAC3E,IAAKtO,IAAI,KAAK,CAAC,IAAI,CAACA,IAAI,IAAMA,IAAI,YAAYW,KAAK,IAAIX,IAAI,CAACvE,MAAM,KAAK,CAAE,EACrE,OAAOjD,IAAI,CAACmB,KAAK,CAACwH,aAAa,CAAC;IACpCmV,eAAe,CAACtW,IAAI,EAAEwW,MAAM,CAAC;IAC7Bhe,IAAI,CAACoD,qBAAqB,GACtBpD,IAAI,CAACgD,aAAa,CAAChD,IAAI,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,CAAC;IACrDjD,IAAI,CAAC0B,MAAM,CAAC,CAAC;IACbF,UAAU,CAAC6B,SAAS,EAAEsF,aAAa,CAAC;IACpC7H,gBAAgB,CAAC,CAAC;IAClB,IAAId,IAAI,CAACgD,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MACjCjD,IAAI,CAACmB,KAAK,CAAC,KAAK,CAAC;IACrB;IACAmC,WAAW,CAACqF,aAAa,CAAC;IAC1B,IAAIA,aAAa,EACb/E,YAAY,CAAC,UAAU,CAAC;EAChC;EACA,SAASiV,cAAcA,CAACuF,GAAG,EAAE;IACzB,OAAOA,GAAG,CACL7X,KAAK,CAAC,CAAC,CACPkT,GAAG,CAAE4E,IAAI,IAAK;MACf,IAAI,OAAOA,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,QAAQ,IACxBA,IAAI,YAAYnZ,IAAI,EAAE;QACtB,OAAOlF,IAAI,CAACO,SAAS,CAAC8d,IAAI,EAAEhb,SAAS,EAAE,IAAI,CAAC;MAChD,CAAC,MACI,IAAIgb,IAAI,IACT,OAAOA,IAAI,KAAK,QAAQ,IACxBA,IAAI,CAAC1I,IAAI,IACT0I,IAAI,CAACzI,EAAE,EACP,OAAO;QACHD,IAAI,EAAE3V,IAAI,CAACO,SAAS,CAAC8d,IAAI,CAAC1I,IAAI,EAAEtS,SAAS,CAAC;QAC1CuS,EAAE,EAAE5V,IAAI,CAACO,SAAS,CAAC8d,IAAI,CAACzI,EAAE,EAAEvS,SAAS;MACzC,CAAC;MACL,OAAOgb,IAAI;IACf,CAAC,CAAC,CACG3H,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC;EACzB;EACA,SAAS/T,UAAUA,CAAA,EAAG;IAClB5C,IAAI,CAACgD,aAAa,GAAG,EAAE;IACvBhD,IAAI,CAAC0K,GAAG,GAAG1K,IAAI,CAACO,SAAS,CAACP,IAAI,CAACC,MAAM,CAACyK,GAAG,CAAC,IAAI,IAAIxF,IAAI,CAAC,CAAC;IACxD,MAAMoZ,aAAa,GAAGte,IAAI,CAACC,MAAM,CAAC+E,WAAW,KACxC,CAAChF,IAAI,CAACuC,KAAK,CAACgc,QAAQ,KAAK,OAAO,IAC7Bve,IAAI,CAACuC,KAAK,CAACgc,QAAQ,KAAK,UAAU,KAClCve,IAAI,CAACuC,KAAK,CAACic,WAAW,IACtBxe,IAAI,CAACuC,KAAK,CAACsD,KAAK,KAAK7F,IAAI,CAACuC,KAAK,CAACic,WAAW,GACzC,IAAI,GACJxe,IAAI,CAACuC,KAAK,CAACsD,KAAK,CAAC;IAC3B,IAAIyY,aAAa,EACbR,eAAe,CAACQ,aAAa,EAAEte,IAAI,CAACC,MAAM,CAAC6V,UAAU,CAAC;IAC1D9V,IAAI,CAACgU,YAAY,GACbhU,IAAI,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,GACvBjD,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,GACrBhD,IAAI,CAACC,MAAM,CAACgF,OAAO,IACjBjF,IAAI,CAACC,MAAM,CAACgF,OAAO,CAACE,OAAO,CAAC,CAAC,GAAGnF,IAAI,CAAC0K,GAAG,CAACvF,OAAO,CAAC,CAAC,GAChDnF,IAAI,CAACC,MAAM,CAACgF,OAAO,GACnBjF,IAAI,CAACC,MAAM,CAAC8G,OAAO,IACjB/G,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC5B,OAAO,CAAC,CAAC,GAAGnF,IAAI,CAAC0K,GAAG,CAACvF,OAAO,CAAC,CAAC,GAChDnF,IAAI,CAACC,MAAM,CAAC8G,OAAO,GACnB/G,IAAI,CAAC0K,GAAG;IAC1B1K,IAAI,CAACoC,WAAW,GAAGpC,IAAI,CAACgU,YAAY,CAACnJ,WAAW,CAAC,CAAC;IAClD7K,IAAI,CAACkC,YAAY,GAAGlC,IAAI,CAACgU,YAAY,CAAClJ,QAAQ,CAAC,CAAC;IAChD,IAAI9K,IAAI,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,EAC7BjD,IAAI,CAACoD,qBAAqB,GAAGpD,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC;IACtD,IAAIhD,IAAI,CAACC,MAAM,CAAC0G,OAAO,KAAKtD,SAAS,EACjCrD,IAAI,CAACC,MAAM,CAAC0G,OAAO,GAAG3G,IAAI,CAACO,SAAS,CAACP,IAAI,CAACC,MAAM,CAAC0G,OAAO,EAAE,KAAK,CAAC;IACpE,IAAI3G,IAAI,CAACC,MAAM,CAAC6G,OAAO,KAAKzD,SAAS,EACjCrD,IAAI,CAACC,MAAM,CAAC6G,OAAO,GAAG9G,IAAI,CAACO,SAAS,CAACP,IAAI,CAACC,MAAM,CAAC6G,OAAO,EAAE,KAAK,CAAC;IACpE9G,IAAI,CAAC4G,cAAc,GACf,CAAC,CAAC5G,IAAI,CAACC,MAAM,CAACgF,OAAO,KAChBjF,IAAI,CAACC,MAAM,CAACgF,OAAO,CAACkC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAC/BnH,IAAI,CAACC,MAAM,CAACgF,OAAO,CAACmC,UAAU,CAAC,CAAC,GAAG,CAAC,IACpCpH,IAAI,CAACC,MAAM,CAACgF,OAAO,CAACoC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;IACjDrH,IAAI,CAACgH,cAAc,GACf,CAAC,CAAChH,IAAI,CAACC,MAAM,CAAC8G,OAAO,KAChB/G,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,IAC/BnH,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAACK,UAAU,CAAC,CAAC,GAAG,CAAC,IACpCpH,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAACM,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;EACrD;EACA,SAAS1E,WAAWA,CAAA,EAAG;IACnB3C,IAAI,CAACuC,KAAK,GAAGuX,YAAY,CAAC,CAAC;IAC3B,IAAI,CAAC9Z,IAAI,CAACuC,KAAK,EAAE;MACbvC,IAAI,CAACC,MAAM,CAAC+K,YAAY,CAAC,IAAIkP,KAAK,CAAC,iCAAiC,CAAC,CAAC;MACtE;IACJ;IACAla,IAAI,CAACuC,KAAK,CAAC4R,KAAK,GAAGnU,IAAI,CAACuC,KAAK,CAACkD,IAAI;IAClCzF,IAAI,CAACuC,KAAK,CAACkD,IAAI,GAAG,MAAM;IACxBzF,IAAI,CAACuC,KAAK,CAAC8I,SAAS,CAACuB,GAAG,CAAC,iBAAiB,CAAC;IAC3C5M,IAAI,CAAC4F,MAAM,GAAG5F,IAAI,CAACuC,KAAK;IACxB,IAAIvC,IAAI,CAACC,MAAM,CAAC+M,QAAQ,EAAE;MACtBhN,IAAI,CAACgN,QAAQ,GAAGjO,aAAa,CAACiB,IAAI,CAACuC,KAAK,CAACgc,QAAQ,EAAEve,IAAI,CAACC,MAAM,CAAC4Z,aAAa,CAAC;MAC7E7Z,IAAI,CAAC4F,MAAM,GAAG5F,IAAI,CAACgN,QAAQ;MAC3BhN,IAAI,CAACgN,QAAQ,CAACwR,WAAW,GAAGxe,IAAI,CAACuC,KAAK,CAACic,WAAW;MAClDxe,IAAI,CAACgN,QAAQ,CAACuE,QAAQ,GAAGvR,IAAI,CAACuC,KAAK,CAACgP,QAAQ;MAC5CvR,IAAI,CAACgN,QAAQ,CAACyR,QAAQ,GAAGze,IAAI,CAACuC,KAAK,CAACkc,QAAQ;MAC5Cze,IAAI,CAACgN,QAAQ,CAACjB,QAAQ,GAAG/L,IAAI,CAACuC,KAAK,CAACwJ,QAAQ;MAC5C/L,IAAI,CAACgN,QAAQ,CAACvH,IAAI,GAAG,MAAM;MAC3BzF,IAAI,CAACuC,KAAK,CAACiL,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACzC,IAAI,CAACxN,IAAI,CAACC,MAAM,CAACyJ,MAAM,IAAI1J,IAAI,CAACuC,KAAK,CAACkJ,UAAU,EAC5CzL,IAAI,CAACuC,KAAK,CAACkJ,UAAU,CAACoB,YAAY,CAAC7M,IAAI,CAACgN,QAAQ,EAAEhN,IAAI,CAACuC,KAAK,CAACuK,WAAW,CAAC;IACjF;IACA,IAAI,CAAC9M,IAAI,CAACC,MAAM,CAACqK,UAAU,EACvBtK,IAAI,CAAC4F,MAAM,CAAC4H,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IACpDxN,IAAI,CAAC8X,gBAAgB,GAAG9X,IAAI,CAACC,MAAM,CAAC4X,eAAe,IAAI7X,IAAI,CAAC4F,MAAM;EACtE;EACA,SAASqD,WAAWA,CAAA,EAAG;IACnB,MAAMyV,SAAS,GAAG1e,IAAI,CAACC,MAAM,CAACkD,UAAU,GAClCnD,IAAI,CAACC,MAAM,CAACiD,UAAU,GAClB,MAAM,GACN,gBAAgB,GACpB,MAAM;IACZlD,IAAI,CAAC+T,WAAW,GAAGhV,aAAa,CAAC,OAAO,EAAEiB,IAAI,CAACuC,KAAK,CAAC2I,SAAS,GAAG,mBAAmB,CAAC;IACrFlL,IAAI,CAAC+T,WAAW,CAAChI,QAAQ,GAAG,CAAC;IAC7B/L,IAAI,CAAC+T,WAAW,CAACtO,IAAI,GAAGiZ,SAAS;IACjC1e,IAAI,CAAC+T,WAAW,CAACxC,QAAQ,GAAGvR,IAAI,CAACuC,KAAK,CAACgP,QAAQ;IAC/CvR,IAAI,CAAC+T,WAAW,CAAC0K,QAAQ,GAAGze,IAAI,CAACuC,KAAK,CAACkc,QAAQ;IAC/Cze,IAAI,CAAC+T,WAAW,CAACyK,WAAW,GAAGxe,IAAI,CAACuC,KAAK,CAACic,WAAW;IACrDxe,IAAI,CAAC2e,eAAe,GAChBD,SAAS,KAAK,gBAAgB,GACxB,eAAe,GACfA,SAAS,KAAK,MAAM,GAChB,OAAO,GACP,OAAO;IACrB,IAAI1e,IAAI,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/BjD,IAAI,CAAC+T,WAAW,CAAC6K,YAAY,GAAG5e,IAAI,CAAC+T,WAAW,CAAClO,KAAK,GAAG7F,IAAI,CAACyN,UAAU,CAACzN,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,EAAEhD,IAAI,CAAC2e,eAAe,CAAC;IACzH;IACA,IAAI3e,IAAI,CAACC,MAAM,CAACgF,OAAO,EACnBjF,IAAI,CAAC+T,WAAW,CAAC7M,GAAG,GAAGlH,IAAI,CAACyN,UAAU,CAACzN,IAAI,CAACC,MAAM,CAACgF,OAAO,EAAE,OAAO,CAAC;IACxE,IAAIjF,IAAI,CAACC,MAAM,CAAC8G,OAAO,EACnB/G,IAAI,CAAC+T,WAAW,CAACzM,GAAG,GAAGtH,IAAI,CAACyN,UAAU,CAACzN,IAAI,CAACC,MAAM,CAAC8G,OAAO,EAAE,OAAO,CAAC;IACxE,IAAI/G,IAAI,CAACuC,KAAK,CAACwQ,YAAY,CAAC,MAAM,CAAC,EAC/B/S,IAAI,CAAC+T,WAAW,CAAC8K,IAAI,GAAGC,MAAM,CAAC9e,IAAI,CAACuC,KAAK,CAACwQ,YAAY,CAAC,MAAM,CAAC,CAAC;IACnE/S,IAAI,CAACuC,KAAK,CAACkD,IAAI,GAAG,QAAQ;IAC1B,IAAIzF,IAAI,CAACgN,QAAQ,KAAK3J,SAAS,EAC3BrD,IAAI,CAACgN,QAAQ,CAACvH,IAAI,GAAG,QAAQ;IACjC,IAAI;MACA,IAAIzF,IAAI,CAACuC,KAAK,CAACkJ,UAAU,EACrBzL,IAAI,CAACuC,KAAK,CAACkJ,UAAU,CAACoB,YAAY,CAAC7M,IAAI,CAAC+T,WAAW,EAAE/T,IAAI,CAACuC,KAAK,CAACuK,WAAW,CAAC;IACpF,CAAC,CACD,OAAOuI,EAAE,EAAE,CAAE;IACbzU,IAAI,CAACZ,IAAI,CAAC+T,WAAW,EAAE,QAAQ,EAAGhP,CAAC,IAAK;MACpC/E,IAAI,CAAC4B,OAAO,CAACzC,cAAc,CAAC4F,CAAC,CAAC,CAACc,KAAK,EAAE,KAAK,EAAE7F,IAAI,CAAC2e,eAAe,CAAC;MAClE/a,YAAY,CAAC,UAAU,CAAC;MACxBA,YAAY,CAAC,SAAS,CAAC;IAC3B,CAAC,CAAC;EACN;EACA,SAAS/B,MAAMA,CAACkD,CAAC,EAAE;IACf,IAAI/E,IAAI,CAACwC,MAAM,KAAK,IAAI,EACpB,OAAOxC,IAAI,CAACoB,KAAK,CAAC,CAAC;IACvBpB,IAAI,CAACyB,IAAI,CAACsD,CAAC,CAAC;EAChB;EACA,SAASnB,YAAYA,CAAC+D,KAAK,EAAEoX,IAAI,EAAE;IAC/B,IAAI/e,IAAI,CAACC,MAAM,KAAKoD,SAAS,EACzB;IACJ,MAAM2b,KAAK,GAAGhf,IAAI,CAACC,MAAM,CAAC0H,KAAK,CAAC;IAChC,IAAIqX,KAAK,KAAK3b,SAAS,IAAI2b,KAAK,CAAC/b,MAAM,GAAG,CAAC,EAAE;MACzC,KAAK,IAAIkK,CAAC,GAAG,CAAC,EAAE6R,KAAK,CAAC7R,CAAC,CAAC,IAAIA,CAAC,GAAG6R,KAAK,CAAC/b,MAAM,EAAEkK,CAAC,EAAE,EAC7C6R,KAAK,CAAC7R,CAAC,CAAC,CAACnN,IAAI,CAACgD,aAAa,EAAEhD,IAAI,CAACuC,KAAK,CAACsD,KAAK,EAAE7F,IAAI,EAAE+e,IAAI,CAAC;IAClE;IACA,IAAIpX,KAAK,KAAK,UAAU,EAAE;MACtB3H,IAAI,CAACuC,KAAK,CAACqJ,aAAa,CAACD,WAAW,CAAC,QAAQ,CAAC,CAAC;MAC/C3L,IAAI,CAACuC,KAAK,CAACqJ,aAAa,CAACD,WAAW,CAAC,OAAO,CAAC,CAAC;IAClD;EACJ;EACA,SAASA,WAAWA,CAACsT,IAAI,EAAE;IACvB,MAAMla,CAAC,GAAGuE,QAAQ,CAACqC,WAAW,CAAC,OAAO,CAAC;IACvC5G,CAAC,CAACma,SAAS,CAACD,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7B,OAAOla,CAAC;EACZ;EACA,SAAS6I,cAAcA,CAACpG,IAAI,EAAE;IAC1B,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnN,IAAI,CAACgD,aAAa,CAACC,MAAM,EAAEkK,CAAC,EAAE,EAAE;MAChD,IAAI/N,YAAY,CAACY,IAAI,CAACgD,aAAa,CAACmK,CAAC,CAAC,EAAE3F,IAAI,CAAC,KAAK,CAAC,EAC/C,OAAO,EAAE,GAAG2F,CAAC;IACrB;IACA,OAAO,KAAK;EAChB;EACA,SAASW,aAAaA,CAACtG,IAAI,EAAE;IACzB,IAAIxH,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,IAAIpJ,IAAI,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,EAC7D,OAAO,KAAK;IAChB,OAAQ7D,YAAY,CAACoI,IAAI,EAAExH,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAClD5D,YAAY,CAACoI,IAAI,EAAExH,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACtD;EACA,SAASqP,4BAA4BA,CAAA,EAAG;IACpC,IAAIrS,IAAI,CAACC,MAAM,CAACiD,UAAU,IAAIlD,IAAI,CAAC6C,QAAQ,IAAI,CAAC7C,IAAI,CAAC+J,QAAQ,EACzD;IACJ/J,IAAI,CAAC0R,YAAY,CAACtJ,OAAO,CAAC,CAACgJ,WAAW,EAAEjE,CAAC,KAAK;MAC1C,MAAMiD,CAAC,GAAG,IAAIlL,IAAI,CAAClF,IAAI,CAACoC,WAAW,EAAEpC,IAAI,CAACkC,YAAY,EAAE,CAAC,CAAC;MAC1DkO,CAAC,CAACC,QAAQ,CAACrQ,IAAI,CAACkC,YAAY,GAAGiL,CAAC,CAAC;MACjC,IAAInN,IAAI,CAACC,MAAM,CAAC+D,UAAU,GAAG,CAAC,IAC1BhE,IAAI,CAACC,MAAM,CAACqQ,iBAAiB,KAAK,QAAQ,EAAE;QAC5CtQ,IAAI,CAAC2R,aAAa,CAACxE,CAAC,CAAC,CAAC1G,WAAW,GAC7B9G,UAAU,CAACyQ,CAAC,CAACtF,QAAQ,CAAC,CAAC,EAAE9K,IAAI,CAACC,MAAM,CAACyQ,qBAAqB,EAAE1Q,IAAI,CAACM,IAAI,CAAC,GAAG,GAAG;MACpF,CAAC,MACI;QACDN,IAAI,CAACwQ,uBAAuB,CAAC3K,KAAK,GAAGuK,CAAC,CAACtF,QAAQ,CAAC,CAAC,CAAC9C,QAAQ,CAAC,CAAC;MAChE;MACAoJ,WAAW,CAACvL,KAAK,GAAGuK,CAAC,CAACvF,WAAW,CAAC,CAAC,CAAC7C,QAAQ,CAAC,CAAC;IAClD,CAAC,CAAC;IACFhI,IAAI,CAAC2T,mBAAmB,GACpB3T,IAAI,CAACC,MAAM,CAACgF,OAAO,KAAK5B,SAAS,KAC5BrD,IAAI,CAACoC,WAAW,KAAKpC,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAC,GACjD7K,IAAI,CAACkC,YAAY,IAAIlC,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC6F,QAAQ,CAAC,CAAC,GACnD9K,IAAI,CAACoC,WAAW,GAAGpC,IAAI,CAACC,MAAM,CAACgF,OAAO,CAAC4F,WAAW,CAAC,CAAC,CAAC;IACnE7K,IAAI,CAAC4T,mBAAmB,GACpB5T,IAAI,CAACC,MAAM,CAAC8G,OAAO,KAAK1D,SAAS,KAC5BrD,IAAI,CAACoC,WAAW,KAAKpC,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAC,GACjD7K,IAAI,CAACkC,YAAY,GAAG,CAAC,GAAGlC,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC+D,QAAQ,CAAC,CAAC,GACtD9K,IAAI,CAACoC,WAAW,GAAGpC,IAAI,CAACC,MAAM,CAAC8G,OAAO,CAAC8D,WAAW,CAAC,CAAC,CAAC;EACvE;EACA,SAASsU,UAAUA,CAACnB,MAAM,EAAE;IACxB,OAAOhe,IAAI,CAACgD,aAAa,CACpByW,GAAG,CAAE2F,IAAI,IAAKpf,IAAI,CAACyN,UAAU,CAAC2R,IAAI,EAAEpB,MAAM,CAAC,CAAC,CAC5CtH,MAAM,CAAC,CAACtG,CAAC,EAAEjD,CAAC,EAAEiR,GAAG,KAAKpe,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,IACnDpJ,IAAI,CAACC,MAAM,CAACkD,UAAU,IACtBib,GAAG,CAACjT,OAAO,CAACiF,CAAC,CAAC,KAAKjD,CAAC,CAAC,CACpBqG,IAAI,CAACxT,IAAI,CAACC,MAAM,CAACmJ,IAAI,KAAK,OAAO,GAChCpJ,IAAI,CAACC,MAAM,CAACge,WAAW,GACvBje,IAAI,CAACM,IAAI,CAAC4d,cAAc,CAAC;EACnC;EACA,SAAS5a,WAAWA,CAACqF,aAAa,GAAG,IAAI,EAAE;IACvC,IAAI3I,IAAI,CAAC+T,WAAW,KAAK1Q,SAAS,IAAIrD,IAAI,CAAC2e,eAAe,EAAE;MACxD3e,IAAI,CAAC+T,WAAW,CAAClO,KAAK,GAClB7F,IAAI,CAACoD,qBAAqB,KAAKC,SAAS,GAClCrD,IAAI,CAACyN,UAAU,CAACzN,IAAI,CAACoD,qBAAqB,EAAEpD,IAAI,CAAC2e,eAAe,CAAC,GACjE,EAAE;IAChB;IACA3e,IAAI,CAACuC,KAAK,CAACsD,KAAK,GAAGsZ,UAAU,CAACnf,IAAI,CAACC,MAAM,CAAC6V,UAAU,CAAC;IACrD,IAAI9V,IAAI,CAACgN,QAAQ,KAAK3J,SAAS,EAAE;MAC7BrD,IAAI,CAACgN,QAAQ,CAACnH,KAAK,GAAGsZ,UAAU,CAACnf,IAAI,CAACC,MAAM,CAAC4V,SAAS,CAAC;IAC3D;IACA,IAAIlN,aAAa,KAAK,KAAK,EACvB/E,YAAY,CAAC,eAAe,CAAC;EACrC;EACA,SAASoG,eAAeA,CAACjF,CAAC,EAAE;IACxB,MAAM6C,WAAW,GAAGzI,cAAc,CAAC4F,CAAC,CAAC;IACrC,MAAMsa,WAAW,GAAGrf,IAAI,CAACyR,YAAY,CAACnG,QAAQ,CAAC1D,WAAW,CAAC;IAC3D,MAAM0X,WAAW,GAAGtf,IAAI,CAAC4R,YAAY,CAACtG,QAAQ,CAAC1D,WAAW,CAAC;IAC3D,IAAIyX,WAAW,IAAIC,WAAW,EAAE;MAC5Bre,WAAW,CAACoe,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC,MACI,IAAIrf,IAAI,CAAC0R,YAAY,CAACvG,OAAO,CAACvD,WAAW,CAAC,IAAI,CAAC,EAAE;MAClDA,WAAW,CAACwC,MAAM,CAAC,CAAC;IACxB,CAAC,MACI,IAAIxC,WAAW,CAACyD,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChDtL,IAAI,CAACkB,UAAU,CAAClB,IAAI,CAACoC,WAAW,GAAG,CAAC,CAAC;IACzC,CAAC,MACI,IAAIwF,WAAW,CAACyD,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClDtL,IAAI,CAACkB,UAAU,CAAClB,IAAI,CAACoC,WAAW,GAAG,CAAC,CAAC;IACzC;EACJ;EACA,SAASsD,WAAWA,CAACX,CAAC,EAAE;IACpBA,CAAC,CAACqR,cAAc,CAAC,CAAC;IAClB,MAAMmJ,SAAS,GAAGxa,CAAC,CAACU,IAAI,KAAK,SAAS;MAAEmC,WAAW,GAAGzI,cAAc,CAAC4F,CAAC,CAAC;MAAExC,KAAK,GAAGqF,WAAW;IAC5F,IAAI5H,IAAI,CAACkG,IAAI,KAAK7C,SAAS,IAAIuE,WAAW,KAAK5H,IAAI,CAACkG,IAAI,EAAE;MACtDlG,IAAI,CAACkG,IAAI,CAACO,WAAW,GACjBzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAACtH,GAAG,CAACoB,IAAI,CAACkG,IAAI,CAACO,WAAW,KAAKzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE;IACA,MAAMgB,GAAG,GAAGsY,UAAU,CAACjd,KAAK,CAACwQ,YAAY,CAAC,KAAK,CAAC,CAAC;MAAEzL,GAAG,GAAGkY,UAAU,CAACjd,KAAK,CAACwQ,YAAY,CAAC,KAAK,CAAC,CAAC;MAAE8L,IAAI,GAAGW,UAAU,CAACjd,KAAK,CAACwQ,YAAY,CAAC,MAAM,CAAC,CAAC;MAAE0M,QAAQ,GAAGnZ,QAAQ,CAAC/D,KAAK,CAACsD,KAAK,EAAE,EAAE,CAAC;MAAEiC,KAAK,GAAG/C,CAAC,CAAC+C,KAAK,KAC/LyX,SAAS,GAAIxa,CAAC,CAAC2a,KAAK,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAI,CAAC,CAAC;IAC/C,IAAIC,QAAQ,GAAGF,QAAQ,GAAGZ,IAAI,GAAG/W,KAAK;IACtC,IAAI,OAAOvF,KAAK,CAACsD,KAAK,KAAK,WAAW,IAAItD,KAAK,CAACsD,KAAK,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAChE,MAAM2c,UAAU,GAAGrd,KAAK,KAAKvC,IAAI,CAACoG,WAAW;QAAEyZ,YAAY,GAAGtd,KAAK,KAAKvC,IAAI,CAACqG,aAAa;MAC1F,IAAIsZ,QAAQ,GAAGzY,GAAG,EAAE;QAChByY,QAAQ,GACJrY,GAAG,GACCqY,QAAQ,GACR/gB,GAAG,CAAC,CAACghB,UAAU,CAAC,IACfhhB,GAAG,CAACghB,UAAU,CAAC,IAAIhhB,GAAG,CAAC,CAACoB,IAAI,CAACkG,IAAI,CAAC,CAAC;QAC5C,IAAI2Z,YAAY,EACZzU,iBAAiB,CAAC/H,SAAS,EAAE,CAAC,CAAC,EAAErD,IAAI,CAACoG,WAAW,CAAC;MAC1D,CAAC,MACI,IAAIuZ,QAAQ,GAAGrY,GAAG,EAAE;QACrBqY,QAAQ,GACJpd,KAAK,KAAKvC,IAAI,CAACoG,WAAW,GAAGuZ,QAAQ,GAAGrY,GAAG,GAAG1I,GAAG,CAAC,CAACoB,IAAI,CAACkG,IAAI,CAAC,GAAGgB,GAAG;QACvE,IAAI2Y,YAAY,EACZzU,iBAAiB,CAAC/H,SAAS,EAAE,CAAC,EAAErD,IAAI,CAACoG,WAAW,CAAC;MACzD;MACA,IAAIpG,IAAI,CAACkG,IAAI,IACT0Z,UAAU,KACTf,IAAI,KAAK,CAAC,GACLc,QAAQ,GAAGF,QAAQ,KAAK,EAAE,GAC1BxY,IAAI,CAACgI,GAAG,CAAC0Q,QAAQ,GAAGF,QAAQ,CAAC,GAAGZ,IAAI,CAAC,EAAE;QAC7C7e,IAAI,CAACkG,IAAI,CAACO,WAAW,GACjBzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAACtH,GAAG,CAACoB,IAAI,CAACkG,IAAI,CAACO,WAAW,KAAKzG,IAAI,CAACM,IAAI,CAAC4F,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE;MACA3D,KAAK,CAACsD,KAAK,GAAGhH,GAAG,CAAC8gB,QAAQ,CAAC;IAC/B;EACJ;EACArd,IAAI,CAAC,CAAC;EACN,OAAOtC,IAAI;AACf;AACA,SAAS8f,UAAUA,CAACC,QAAQ,EAAE9f,MAAM,EAAE;EAClC,MAAM+f,KAAK,GAAG7X,KAAK,CAACW,SAAS,CAACvC,KAAK,CAC9BwC,IAAI,CAACgX,QAAQ,CAAC,CACdrJ,MAAM,CAAEC,CAAC,IAAKA,CAAC,YAAYsJ,WAAW,CAAC;EAC5C,MAAMC,SAAS,GAAG,EAAE;EACpB,KAAK,IAAI/S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6S,KAAK,CAAC/c,MAAM,EAAEkK,CAAC,EAAE,EAAE;IACnC,MAAMgT,IAAI,GAAGH,KAAK,CAAC7S,CAAC,CAAC;IACrB,IAAI;MACA,IAAIgT,IAAI,CAACpN,YAAY,CAAC,cAAc,CAAC,KAAK,IAAI,EAC1C;MACJ,IAAIoN,IAAI,CAACL,UAAU,KAAKzc,SAAS,EAAE;QAC/B8c,IAAI,CAACL,UAAU,CAACxe,OAAO,CAAC,CAAC;QACzB6e,IAAI,CAACL,UAAU,GAAGzc,SAAS;MAC/B;MACA8c,IAAI,CAACL,UAAU,GAAGjgB,iBAAiB,CAACsgB,IAAI,EAAElgB,MAAM,IAAI,CAAC,CAAC,CAAC;MACvDigB,SAAS,CAAC1X,IAAI,CAAC2X,IAAI,CAACL,UAAU,CAAC;IACnC,CAAC,CACD,OAAO/a,CAAC,EAAE;MACNqb,OAAO,CAACC,KAAK,CAACtb,CAAC,CAAC;IACpB;EACJ;EACA,OAAOmb,SAAS,CAACjd,MAAM,KAAK,CAAC,GAAGid,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;AAC5D;AACA,IAAI,OAAOD,WAAW,KAAK,WAAW,IAClC,OAAOK,cAAc,KAAK,WAAW,IACrC,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACjCD,cAAc,CAACxX,SAAS,CAAC1I,SAAS,GAAGmgB,QAAQ,CAACzX,SAAS,CAAC1I,SAAS,GAAG,UAAUH,MAAM,EAAE;IAClF,OAAO6f,UAAU,CAAC,IAAI,EAAE7f,MAAM,CAAC;EACnC,CAAC;EACDggB,WAAW,CAACnX,SAAS,CAAC1I,SAAS,GAAG,UAAUH,MAAM,EAAE;IAChD,OAAO6f,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE7f,MAAM,CAAC;EACrC,CAAC;AACL;AACA,IAAIG,SAAS,GAAG,SAAAA,CAAUogB,QAAQ,EAAEvgB,MAAM,EAAE;EACxC,IAAI,OAAOugB,QAAQ,KAAK,QAAQ,EAAE;IAC9B,OAAOV,UAAU,CAAC7b,MAAM,CAACqF,QAAQ,CAACN,gBAAgB,CAACwX,QAAQ,CAAC,EAAEvgB,MAAM,CAAC;EACzE,CAAC,MACI,IAAIugB,QAAQ,YAAYC,IAAI,EAAE;IAC/B,OAAOX,UAAU,CAAC,CAACU,QAAQ,CAAC,EAAEvgB,MAAM,CAAC;EACzC,CAAC,MACI;IACD,OAAO6f,UAAU,CAACU,QAAQ,EAAEvgB,MAAM,CAAC;EACvC;AACJ,CAAC;AACDG,SAAS,CAACC,aAAa,GAAG,CAAC,CAAC;AAC5BD,SAAS,CAAC6Z,KAAK,GAAG;EACdyG,EAAE,EAAExgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1B,OAAO,CAAC;EAC9B0b,OAAO,EAAEja,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1B,OAAO;AACtC,CAAC;AACD2B,SAAS,CAACugB,QAAQ,GAAIrgB,IAAI,IAAK;EAC3BF,SAAS,CAAC6Z,KAAK,CAACE,OAAO,GAAGja,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC6Z,KAAK,CAACE,OAAO,CAAC,EAAE7Z,IAAI,CAAC;AAC7F,CAAC;AACDF,SAAS,CAACwgB,WAAW,GAAI3gB,MAAM,IAAK;EAChCG,SAAS,CAACC,aAAa,GAAGH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,CAACC,aAAa,CAAC,EAAEJ,MAAM,CAAC;AAC/F,CAAC;AACDG,SAAS,CAACG,SAAS,GAAGlB,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC1Ce,SAAS,CAACqN,UAAU,GAAGnO,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC9Cc,SAAS,CAAChB,YAAY,GAAGA,YAAY;AACrC,IAAI,OAAOyhB,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAAC/c,EAAE,KAAK,WAAW,EAAE;EACnE+c,MAAM,CAAC/c,EAAE,CAAC1D,SAAS,GAAG,UAAUH,MAAM,EAAE;IACpC,OAAO6f,UAAU,CAAC,IAAI,EAAE7f,MAAM,CAAC;EACnC,CAAC;AACL;AACAiF,IAAI,CAAC4D,SAAS,CAACgY,OAAO,GAAG,UAAUrc,IAAI,EAAE;EACrC,OAAO,IAAIS,IAAI,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACwC,OAAO,CAAC,CAAC,IAAI,OAAO7I,IAAI,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,IAAI,EAAE,EAAE,CAAC,GAAGA,IAAI,CAAC,CAAC;AACjI,CAAC;AACD,IAAI,OAAOR,MAAM,KAAK,WAAW,EAAE;EAC/BA,MAAM,CAAC7D,SAAS,GAAGA,SAAS;AAChC;AACA,eAAeA,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
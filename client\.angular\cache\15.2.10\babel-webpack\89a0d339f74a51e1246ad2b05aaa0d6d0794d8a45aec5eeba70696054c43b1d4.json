{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CustomFieldService {\n  constructor() {}\n  /**\r\n   * This function for compare key of old and new player updates\r\n   * @param metadata\r\n   * @returns\r\n   */\n  getMatchingKeys(metadata) {\n    const matchingKeys = [];\n    const newData = metadata.new_data;\n    const oldData = metadata.old_data;\n    for (const key in newData) {\n      if (newData.hasOwnProperty(key)) {\n        // Check if the key exists in old_data (or in old_data.custom_fields or old_data.user)\n        const oldValue = oldData.custom_fields?.[key] || oldData[key] || oldData.user?.[key];\n        // convert to string to compare\n        const newValue = newData[key];\n        const oldValueString = oldValue;\n        // in case new is null and old is not set => both null \n        if (newValue == null && oldValue == null) continue;\n        if (newValue !== oldValueString) {\n          matchingKeys.push(key);\n        }\n      }\n    }\n    return matchingKeys;\n  }\n  static #_ = this.ɵfac = function CustomFieldService_Factory(t) {\n    return new (t || CustomFieldService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomFieldService,\n    factory: CustomFieldService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,kBAAkB;EAE7BC,YAAA,GAAgB;EAGhB;;;;;EAMAC,eAAeA,CAACC,QAAa;IAC3B,MAAMC,YAAY,GAAa,EAAE;IACjC,MAAMC,OAAO,GAAGF,QAAQ,CAACG,QAAQ;IACjC,MAAMC,OAAO,GAAGJ,QAAQ,CAACK,QAAQ;IAEjC,KAAK,MAAMC,GAAG,IAAIJ,OAAO,EAAE;MACzB,IAAIA,OAAO,CAACK,cAAc,CAACD,GAAG,CAAC,EAAE;QAC/B;QACA,MAAME,QAAQ,GAAGJ,OAAO,CAACK,aAAa,GAAGH,GAAG,CAAC,IAAIF,OAAO,CAACE,GAAG,CAAC,IAAIF,OAAO,CAACM,IAAI,GAAGJ,GAAG,CAAC;QAEpF;QACA,MAAMK,QAAQ,GAAGT,OAAO,CAACI,GAAG,CAAC;QAC7B,MAAMM,cAAc,GAAGJ,QAAQ;QAE/B;QACA,IAAGG,QAAQ,IAAI,IAAI,IAAIH,QAAQ,IAAI,IAAI,EAAE;QAEzC,IAAIG,QAAQ,KAAKC,cAAc,EAAE;UAC/BX,YAAY,CAACY,IAAI,CAACP,GAAG,CAAC;;;;IAI5B,OAAOL,YAAY;EACrB;EAAC,QAAAa,CAAA;qBAlCUjB,kBAAkB;EAAA;EAAA,QAAAkB,EAAA;WAAlBlB,kBAAkB;IAAAmB,OAAA,EAAlBnB,kBAAkB,CAAAoB,IAAA;IAAAC,UAAA,EAFjB;EAAM", "names": ["CustomFieldService", "constructor", "getMatchingKeys", "metadata", "matching<PERSON><PERSON>s", "newData", "new_data", "oldData", "old_data", "key", "hasOwnProperty", "oldValue", "custom_fields", "user", "newValue", "oldValueString", "push", "_", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\customfield.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CustomFieldService {\r\n\r\n  constructor() { }\r\n\r\n\r\n  /**\r\n   * This function for compare key of old and new player updates\r\n   * @param metadata \r\n   * @returns \r\n   */\r\n\r\n  getMatchingKeys(metadata: any): string[] {\r\n    const matchingKeys: string[] = [];\r\n    const newData = metadata.new_data;\r\n    const oldData = metadata.old_data;\r\n  \r\n    for (const key in newData) {\r\n      if (newData.hasOwnProperty(key)) {\r\n        // Check if the key exists in old_data (or in old_data.custom_fields or old_data.user)\r\n        const oldValue = oldData.custom_fields?.[key] || oldData[key] || oldData.user?.[key];\r\n        \r\n        // convert to string to compare\r\n        const newValue = newData[key];\r\n        const oldValueString = oldValue;\r\n\r\n        // in case new is null and old is not set => both null \r\n        if(newValue == null && oldValue == null) continue; \r\n        \r\n        if (newValue !== oldValueString) {\r\n          matchingKeys.push(key);\r\n        }\r\n      }\r\n    }\r\n    return matchingKeys;\r\n  }\r\n  \r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
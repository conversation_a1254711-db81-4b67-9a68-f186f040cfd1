{"ast": null, "code": "import { Immediate } from '../util/Immediate';\nimport { AsyncAction } from './AsyncAction';\nexport class AsapAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler.scheduled || (scheduler.scheduled = Immediate.setImmediate(scheduler.flush.bind(scheduler, null)));\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0 || delay === null && this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n    if (scheduler.actions.length === 0) {\n      Immediate.clearImmediate(id);\n      scheduler.scheduled = undefined;\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["Immediate", "AsyncAction", "AsapAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "scheduled", "setImmediate", "flush", "bind", "recycleAsyncId", "length", "clearImmediate", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduler/AsapAction.js"], "sourcesContent": ["import { Immediate } from '../util/Immediate';\nimport { AsyncAction } from './AsyncAction';\nexport class AsapAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler.scheduled || (scheduler.scheduled = Immediate.setImmediate(scheduler.flush.bind(scheduler, null)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        if ((delay !== null && delay > 0) || (delay === null && this.delay > 0)) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        if (scheduler.actions.length === 0) {\n            Immediate.clearImmediate(id);\n            scheduler.scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAO,MAAMC,UAAU,SAASD,WAAW,CAAC;EACxCE,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAACD,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,cAAcA,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK,CAACF,cAAc,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACAJ,SAAS,CAACK,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5B,OAAON,SAAS,CAACO,SAAS,KAAKP,SAAS,CAACO,SAAS,GAAGX,SAAS,CAACY,YAAY,CAACR,SAAS,CAACS,KAAK,CAACC,IAAI,CAACV,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;EACvH;EACAW,cAAcA,CAACX,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAMA,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAAE,EAAE;MACrE,OAAO,KAAK,CAACO,cAAc,CAACX,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACA,IAAIJ,SAAS,CAACK,OAAO,CAACO,MAAM,KAAK,CAAC,EAAE;MAChChB,SAAS,CAACiB,cAAc,CAACV,EAAE,CAAC;MAC5BH,SAAS,CAACO,SAAS,GAAGO,SAAS;IACnC;IACA,OAAOA,SAAS;EACpB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function timer(dueTime = 0, periodOrScheduler, scheduler) {\n  let period = -1;\n  if (isNumeric(periodOrScheduler)) {\n    period = Number(periodOrScheduler) < 1 && 1 || Number(periodOrScheduler);\n  } else if (isScheduler(periodOrScheduler)) {\n    scheduler = periodOrScheduler;\n  }\n  if (!isScheduler(scheduler)) {\n    scheduler = async;\n  }\n  return new Observable(subscriber => {\n    const due = isNumeric(dueTime) ? dueTime : +dueTime - scheduler.now();\n    return scheduler.schedule(dispatch, due, {\n      index: 0,\n      period,\n      subscriber\n    });\n  });\n}\nfunction dispatch(state) {\n  const {\n    index,\n    period,\n    subscriber\n  } = state;\n  subscriber.next(index);\n  if (subscriber.closed) {\n    return;\n  } else if (period === -1) {\n    return subscriber.complete();\n  }\n  state.index = index + 1;\n  this.schedule(state, period);\n}", "map": {"version": 3, "names": ["Observable", "async", "isNumeric", "isScheduler", "timer", "dueTime", "periodOrScheduler", "scheduler", "period", "Number", "subscriber", "due", "now", "schedule", "dispatch", "index", "state", "next", "closed", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/timer.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function timer(dueTime = 0, periodOrScheduler, scheduler) {\n    let period = -1;\n    if (isNumeric(periodOrScheduler)) {\n        period = Number(periodOrScheduler) < 1 && 1 || Number(periodOrScheduler);\n    }\n    else if (isScheduler(periodOrScheduler)) {\n        scheduler = periodOrScheduler;\n    }\n    if (!isScheduler(scheduler)) {\n        scheduler = async;\n    }\n    return new Observable(subscriber => {\n        const due = isNumeric(dueTime)\n            ? dueTime\n            : (+dueTime - scheduler.now());\n        return scheduler.schedule(dispatch, due, {\n            index: 0, period, subscriber\n        });\n    });\n}\nfunction dispatch(state) {\n    const { index, period, subscriber } = state;\n    subscriber.next(index);\n    if (subscriber.closed) {\n        return;\n    }\n    else if (period === -1) {\n        return subscriber.complete();\n    }\n    state.index = index + 1;\n    this.schedule(state, period);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,SAASC,KAAKA,CAACC,OAAO,GAAG,CAAC,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;EAC7D,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIN,SAAS,CAACI,iBAAiB,CAAC,EAAE;IAC9BE,MAAM,GAAGC,MAAM,CAACH,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAIG,MAAM,CAACH,iBAAiB,CAAC;EAC5E,CAAC,MACI,IAAIH,WAAW,CAACG,iBAAiB,CAAC,EAAE;IACrCC,SAAS,GAAGD,iBAAiB;EACjC;EACA,IAAI,CAACH,WAAW,CAACI,SAAS,CAAC,EAAE;IACzBA,SAAS,GAAGN,KAAK;EACrB;EACA,OAAO,IAAID,UAAU,CAACU,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAGT,SAAS,CAACG,OAAO,CAAC,GACxBA,OAAO,GACN,CAACA,OAAO,GAAGE,SAAS,CAACK,GAAG,CAAC,CAAE;IAClC,OAAOL,SAAS,CAACM,QAAQ,CAACC,QAAQ,EAAEH,GAAG,EAAE;MACrCI,KAAK,EAAE,CAAC;MAAEP,MAAM;MAAEE;IACtB,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASI,QAAQA,CAACE,KAAK,EAAE;EACrB,MAAM;IAAED,KAAK;IAAEP,MAAM;IAAEE;EAAW,CAAC,GAAGM,KAAK;EAC3CN,UAAU,CAACO,IAAI,CAACF,KAAK,CAAC;EACtB,IAAIL,UAAU,CAACQ,MAAM,EAAE;IACnB;EACJ,CAAC,MACI,IAAIV,MAAM,KAAK,CAAC,CAAC,EAAE;IACpB,OAAOE,UAAU,CAACS,QAAQ,CAAC,CAAC;EAChC;EACAH,KAAK,CAACD,KAAK,GAAGA,KAAK,GAAG,CAAC;EACvB,IAAI,CAACF,QAAQ,CAACG,KAAK,EAAER,MAAM,CAAC;AAChC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
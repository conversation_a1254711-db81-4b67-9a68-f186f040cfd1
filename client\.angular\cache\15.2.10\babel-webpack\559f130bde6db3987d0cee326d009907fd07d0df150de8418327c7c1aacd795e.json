{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin } from '@capacitor/core';\nexport class AppWeb extends WebPlugin {\n  constructor() {\n    super();\n    this.handleVisibilityChange = () => {\n      const data = {\n        isActive: document.hidden !== true\n      };\n      this.notifyListeners('appStateChange', data);\n      if (document.hidden) {\n        this.notifyListeners('pause', null);\n      } else {\n        this.notifyListeners('resume', null);\n      }\n    };\n    document.addEventListener('visibilitychange', this.handleVisibilityChange, false);\n  }\n  exitApp() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  getInfo() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      throw _this.unimplemented('Not implemented on web.');\n    })();\n  }\n  getLaunchUrl() {\n    return _asyncToGenerator(function* () {\n      return {\n        url: ''\n      };\n    })();\n  }\n  getState() {\n    return _asyncToGenerator(function* () {\n      return {\n        isActive: document.hidden !== true\n      };\n    })();\n  }\n  minimizeApp() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      throw _this2.unimplemented('Not implemented on web.');\n    })();\n  }\n}", "map": {"version": 3, "names": ["WebPlugin", "AppWeb", "constructor", "handleVisibilityChange", "data", "isActive", "document", "hidden", "notifyListeners", "addEventListener", "exitApp", "unimplemented", "getInfo", "_this", "_asyncToGenerator", "getLaunchUrl", "url", "getState", "minimizeApp", "_this2"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/app/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class AppWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.handleVisibilityChange = () => {\n            const data = {\n                isActive: document.hidden !== true,\n            };\n            this.notifyListeners('appStateChange', data);\n            if (document.hidden) {\n                this.notifyListeners('pause', null);\n            }\n            else {\n                this.notifyListeners('resume', null);\n            }\n        };\n        document.addEventListener('visibilitychange', this.handleVisibilityChange, false);\n    }\n    exitApp() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n    async getInfo() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n    async getLaunchUrl() {\n        return { url: '' };\n    }\n    async getState() {\n        return { isActive: document.hidden !== true };\n    }\n    async minimizeApp() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,MAAM,SAASD,SAAS,CAAC;EAClCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,sBAAsB,GAAG,MAAM;MAChC,MAAMC,IAAI,GAAG;QACTC,QAAQ,EAAEC,QAAQ,CAACC,MAAM,KAAK;MAClC,CAAC;MACD,IAAI,CAACC,eAAe,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;MAC5C,IAAIE,QAAQ,CAACC,MAAM,EAAE;QACjB,IAAI,CAACC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAACA,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC;IACDF,QAAQ,CAACG,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAACN,sBAAsB,EAAE,KAAK,CAAC;EACrF;EACAO,OAAOA,CAAA,EAAG;IACN,MAAM,IAAI,CAACC,aAAa,CAAC,yBAAyB,CAAC;EACvD;EACMC,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMD,KAAI,CAACF,aAAa,CAAC,yBAAyB,CAAC;IAAC;EACxD;EACMI,YAAYA,CAAA,EAAG;IAAA,OAAAD,iBAAA;MACjB,OAAO;QAAEE,GAAG,EAAE;MAAG,CAAC;IAAC;EACvB;EACMC,QAAQA,CAAA,EAAG;IAAA,OAAAH,iBAAA;MACb,OAAO;QAAET,QAAQ,EAAEC,QAAQ,CAACC,MAAM,KAAK;MAAK,CAAC;IAAC;EAClD;EACMW,WAAWA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChB,MAAMK,MAAI,CAACR,aAAa,CAAC,yBAAyB,CAAC;IAAC;EACxD;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
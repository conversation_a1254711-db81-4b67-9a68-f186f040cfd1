{"ast": null, "code": "export function identity(x) {\n  return x;\n}", "map": {"version": 3, "names": ["identity", "x"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/identity.js"], "sourcesContent": ["export function identity(x) {\n    return x;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC;AACZ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CoreSidebarComponent } from '@core/components/core-sidebar/core-sidebar.component';\nimport * as i0 from \"@angular/core\";\nexport class CoreSidebarModule {\n  static #_ = this.ɵfac = function CoreSidebarModule_Factory(t) {\n    return new (t || CoreSidebarModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreSidebarModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({});\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreSidebarModule, {\n    declarations: [CoreSidebarComponent],\n    exports: [CoreSidebarComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAEA,SAASA,oBAAoB,QAAQ,sDAAsD;;AAM3F,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA;UAAjBF;EAAiB;EAAA,QAAAG,EAAA;;;2EAAjBH,iBAAiB;IAAAI,YAAA,GAHbL,oBAAoB;IAAAM,OAAA,GACzBN,oBAAoB;EAAA;AAAA", "names": ["CoreSidebarComponent", "CoreSidebarModule", "_", "_2", "_3", "declarations", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-sidebar\\core-sidebar.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { CoreSidebarComponent } from '@core/components/core-sidebar/core-sidebar.component';\r\n\r\n@NgModule({\r\n  declarations: [CoreSidebarComponent],\r\n  exports: [CoreSidebarComponent]\r\n})\r\nexport class CoreSidebarModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
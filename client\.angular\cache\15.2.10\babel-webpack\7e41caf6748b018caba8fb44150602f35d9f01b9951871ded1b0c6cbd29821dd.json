{"ast": null, "code": "import { FilterPipe } from '@core/pipes/filter.pipe';\nimport { InitialsPipe } from '@core/pipes/initials.pipe';\nimport { SafePipe } from '@core/pipes/safe.pipe';\nimport { StripHtmlPipe } from '@core/pipes/stripHtml.pipe';\nimport { DotStringPipe } from '@core/pipes/dotstring.pipe';\nimport { LocalizedDatePipe } from './localized-date.pipe';\nimport { registerLocaleData } from '@angular/common';\nimport localeZhHantHK from '@angular/common/locales/zh-Hant-HK';\nimport localeVi from '@angular/common/locales/vi';\nimport { SortByNamePipe } from './sort-by-name.pipe';\nimport { DecodeHtmlPipe } from './decode-html.pipe';\nimport * as i0 from \"@angular/core\";\nregisterLocaleData(localeZhHantHK);\nregisterLocaleData(localeVi);\nexport class CorePipesModule {\n  static #_ = this.ɵfac = function CorePipesModule_Factory(t) {\n    return new (t || CorePipesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CorePipesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({});\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CorePipesModule, {\n    declarations: [InitialsPipe, FilterPipe, StripHtmlPipe, SafePipe, DotStringPipe, LocalizedDatePipe, SortByNamePipe, DecodeHtmlPipe],\n    exports: [InitialsPipe, FilterPipe, StripHtmlPipe, SafePipe, DotStringPipe, LocalizedDatePipe, SortByNamePipe, DecodeHtmlPipe]\n  });\n})();", "map": {"version": 3, "mappings": "AAEA,SAASA,UAAU,QAAQ,yBAAyB;AAEpD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,oBAAoB;;AACnDJ,kBAAkB,CAACC,cAAc,CAAC;AAClCD,kBAAkB,CAACE,QAAQ,CAAC;AAwB5B,OAAM,MAAOG,eAAe;EAAA,QAAAC,CAAA;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA;UAAfF;EAAe;EAAA,QAAAG,EAAA;;;2EAAfH,eAAe;IAAAI,YAAA,GArBxBd,YAAY,EACZD,UAAU,EACVG,aAAa,EACbD,QAAQ,EACRE,aAAa,EACbC,iBAAiB,EACjBI,cAAc,EACdC,cAAc;IAAAM,OAAA,GAIdf,YAAY,EACZD,UAAU,EACVG,aAAa,EACbD,QAAQ,EACRE,aAAa,EACbC,iBAAiB,EACjBI,cAAc,EACdC,cAAc;EAAA;AAAA", "names": ["FilterPipe", "InitialsPipe", "SafePipe", "StripHtmlPipe", "DotStringPipe", "LocalizedDatePipe", "registerLocaleData", "localeZhHantHK", "localeVi", "SortByNamePipe", "DecodeHtmlPipe", "CorePipesModule", "_", "_2", "_3", "declarations", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\pipes\\pipes.module.ts"], "sourcesContent": ["import { LOCALE_ID, NgModule } from '@angular/core';\r\n\r\nimport { FilterPipe } from '@core/pipes/filter.pipe';\r\n\r\nimport { InitialsPipe } from '@core/pipes/initials.pipe';\r\nimport { SafePipe } from '@core/pipes/safe.pipe';\r\nimport { StripHtmlPipe } from '@core/pipes/stripHtml.pipe';\r\nimport { DotStringPipe } from '@core/pipes/dotstring.pipe';\r\nimport { LocalizedDatePipe } from './localized-date.pipe';\r\nimport { registerLocaleData } from '@angular/common';\r\nimport localeZhHantHK from '@angular/common/locales/zh-Hant-HK';\r\nimport localeVi from '@angular/common/locales/vi';\r\nimport { SortByNamePipe } from './sort-by-name.pipe';\r\nimport { DecodeHtmlPipe } from './decode-html.pipe';\r\nregisterLocaleData(localeZhHantHK);\r\nregisterLocaleData(localeVi);\r\n@NgModule({\r\n  declarations: [\r\n    InitialsPipe,\r\n    FilterPipe,\r\n    StripHtmlPipe,\r\n    SafePipe,\r\n    DotStringPipe,\r\n    LocalizedDatePipe,\r\n    SortByNamePipe,\r\n    DecodeHtmlPipe\r\n  ],\r\n  imports: [],\r\n  exports: [\r\n    InitialsPipe,\r\n    FilterPipe,\r\n    StripHtmlPipe,\r\n    SafePipe,\r\n    DotStringPipe,\r\n    LocalizedDatePipe,\r\n    SortByNamePipe,\r\n    DecodeHtmlPipe\r\n  ],\r\n})\r\nexport class CorePipesModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
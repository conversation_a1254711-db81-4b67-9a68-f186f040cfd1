{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nexport function finalize(callback) {\n  return source => source.lift(new FinallyOperator(callback));\n}\nclass FinallyOperator {\n  constructor(callback) {\n    this.callback = callback;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new FinallySubscriber(subscriber, this.callback));\n  }\n}\nclass FinallySubscriber extends Subscriber {\n  constructor(destination, callback) {\n    super(destination);\n    this.add(new Subscription(callback));\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "Subscription", "finalize", "callback", "source", "lift", "FinallyOperator", "constructor", "call", "subscriber", "subscribe", "FinallySubscriber", "destination", "add"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/finalize.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nexport function finalize(callback) {\n    return (source) => source.lift(new FinallyOperator(callback));\n}\nclass FinallyOperator {\n    constructor(callback) {\n        this.callback = callback;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new FinallySubscriber(subscriber, this.callback));\n    }\n}\nclass FinallySubscriber extends Subscriber {\n    constructor(destination, callback) {\n        super(destination);\n        this.add(new Subscription(callback));\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,QAAQA,CAACC,QAAQ,EAAE;EAC/B,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,eAAe,CAACH,QAAQ,CAAC,CAAC;AACjE;AACA,MAAMG,eAAe,CAAC;EAClBC,WAAWA,CAACJ,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,iBAAiB,CAACF,UAAU,EAAE,IAAI,CAACN,QAAQ,CAAC,CAAC;EAC7E;AACJ;AACA,MAAMQ,iBAAiB,SAASX,UAAU,CAAC;EACvCO,WAAWA,CAACK,WAAW,EAAET,QAAQ,EAAE;IAC/B,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACC,GAAG,CAAC,IAAIZ,YAAY,CAACE,QAAQ,CAAC,CAAC;EACxC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@videogular/ngx-videogular/core';\nimport { VgCoreModule } from '@videogular/ngx-videogular/core';\nclass VgDashDirective {\n  constructor(ref, API) {\n    this.ref = ref;\n    this.API = API;\n    this.onGetBitrates = new EventEmitter();\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.vgFor = this.ref.nativeElement.getAttribute('vgFor');\n    this.target = this.API.getMediaById(this.vgFor);\n    this.createPlayer();\n  }\n  ngOnChanges(changes) {\n    changes.vgDash?.currentValue ? this.createPlayer() : this.destroyPlayer();\n  }\n  createPlayer() {\n    if (this.dash) {\n      this.destroyPlayer();\n    }\n    // It's a DASH source\n    if (this.vgDash && (this.vgDash.indexOf('.mpd') > -1 || this.vgDash.indexOf('mpd-time-csf') > -1)) {\n      let drmOptions;\n      if (this.vgDRMLicenseServer) {\n        drmOptions = this.vgDRMLicenseServer;\n        if (this.vgDRMToken) {\n          for (const drmServer in drmOptions) {\n            if (drmServer.hasOwnProperty(drmServer)) {\n              drmOptions[drmServer].httpRequestHeaders = {\n                Authorization: this.vgDRMToken\n              };\n            }\n          }\n        }\n      }\n      this.dash = dashjs.MediaPlayer().create();\n      this.dash.updateSettings({\n        debug: {\n          logLevel: dashjs.Debug.LOG_LEVEL_NONE\n        }\n      });\n      this.dash.initialize(this.ref.nativeElement);\n      this.dash.setAutoPlay(false);\n      this.dash.on(dashjs.MediaPlayer.events.STREAM_INITIALIZED, () => {\n        const audioList = this.dash.getBitrateInfoListFor('audio');\n        const videoList = this.dash.getBitrateInfoListFor('video');\n        if (audioList.length > 1) {\n          audioList.forEach(item => item.qualityIndex = ++item.qualityIndex);\n          audioList.unshift({\n            qualityIndex: 0,\n            width: 0,\n            height: 0,\n            bitrate: 0,\n            mediaType: 'video',\n            scanType: 'AUTO'\n          });\n          this.onGetBitrates.emit(audioList);\n        }\n        if (videoList.length > 1) {\n          videoList.forEach(item => item.qualityIndex = ++item.qualityIndex);\n          videoList.unshift({\n            qualityIndex: 0,\n            width: 0,\n            height: 0,\n            bitrate: 0,\n            mediaType: 'video',\n            scanType: 'AUTO'\n          });\n          this.onGetBitrates.emit(videoList);\n        }\n      });\n      if (drmOptions) {\n        this.dash.setProtectionData(drmOptions);\n      }\n      this.dash.attachSource(this.vgDash);\n    } else {\n      if (this.target) {\n        this.target.pause();\n        this.target.seekTime(0);\n        this.ref.nativeElement.src = this.vgDash;\n      }\n    }\n  }\n  setBitrate({\n    mediaType,\n    qualityIndex\n  }) {\n    if (this.dash) {\n      if (qualityIndex > 0) {\n        if (this.dash.getSettings()) {\n          this.dash.updateSettings({\n            streaming: {\n              abr: {\n                autoSwitchBitrate: {\n                  [mediaType]: false\n                }\n              }\n            }\n          });\n        }\n        const nextIndex = qualityIndex - 1;\n        this.dash.setQualityFor(mediaType, nextIndex);\n      } else {\n        this.dash.updateSettings({\n          streaming: {\n            abr: {\n              autoSwitchBitrate: {\n                [mediaType]: true\n              }\n            }\n          }\n        });\n      }\n    }\n  }\n  destroyPlayer() {\n    if (this.dash) {\n      this.dash.reset();\n      this.dash = null;\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n    this.destroyPlayer();\n  }\n}\n/** @nocollapse */\nVgDashDirective.ɵfac = function VgDashDirective_Factory(t) {\n  return new (t || VgDashDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgDashDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: VgDashDirective,\n  selectors: [[\"\", \"vgDash\", \"\"]],\n  inputs: {\n    vgDash: \"vgDash\",\n    vgDRMToken: \"vgDRMToken\",\n    vgDRMLicenseServer: \"vgDRMLicenseServer\"\n  },\n  outputs: {\n    onGetBitrates: \"onGetBitrates\"\n  },\n  exportAs: [\"vgDash\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgDashDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[vgDash]',\n      exportAs: 'vgDash'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgDash: [{\n      type: Input\n    }],\n    vgDRMToken: [{\n      type: Input\n    }],\n    vgDRMLicenseServer: [{\n      type: Input\n    }],\n    onGetBitrates: [{\n      type: Output\n    }]\n  });\n})();\nclass VgHlsDirective {\n  constructor(ref, API) {\n    this.ref = ref;\n    this.API = API;\n    this.vgHlsHeaders = {};\n    this.onGetBitrates = new EventEmitter();\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.crossorigin = this.ref.nativeElement.getAttribute('crossorigin');\n    this.preload = this.ref.nativeElement.getAttribute('preload') !== 'none';\n    this.vgFor = this.ref.nativeElement.getAttribute('vgFor');\n    if (this.vgFor) {\n      this.target = this.API.getMediaById(this.vgFor);\n    } else {\n      this.target = this.API.getDefaultMedia();\n    }\n    this.config = {\n      autoStartLoad: this.preload\n    };\n    // @ts-ignore\n    this.config.xhrSetup = xhr => {\n      // Send cookies\n      if (this.crossorigin === 'use-credentials') {\n        xhr.withCredentials = true;\n      }\n      for (const key of Object.keys(this.vgHlsHeaders)) {\n        xhr.setRequestHeader(key, this.vgHlsHeaders[key]);\n      }\n    };\n    this.createPlayer();\n    if (!this.preload) {\n      this.subscriptions.push(this.API.subscriptions.play.subscribe(() => {\n        if (this.hls) {\n          this.hls.startLoad(0);\n        }\n      }));\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.vgHls?.currentValue) {\n      this.createPlayer();\n    } else if (changes.vgHlsHeaders && changes.vgHlsHeaders.currentValue) {\n      // Do nothing. We don't want to create a or destroy a player if the headers change.\n    } else {\n      this.destroyPlayer();\n    }\n  }\n  createPlayer() {\n    if (this.hls) {\n      this.destroyPlayer();\n    }\n    // It's a HLS source\n    if (this.vgHls && this.vgHls.indexOf('m3u8') > -1 && Hls.isSupported() && this.API.isPlayerReady) {\n      const video = this.ref.nativeElement;\n      this.hls = new Hls(this.config);\n      // @ts-ignore\n      this.hls.on(Hls.Events.MANIFEST_PARSED, (_event, data) => {\n        const videoList = [];\n        videoList.push({\n          qualityIndex: 0,\n          width: 0,\n          height: 0,\n          bitrate: 0,\n          mediaType: 'video',\n          label: 'AUTO'\n        });\n        data.levels.forEach((item, index) => {\n          videoList.push({\n            qualityIndex: ++index,\n            width: item.width,\n            height: item.height,\n            bitrate: item.bitrate,\n            mediaType: 'video',\n            label: item.name\n          });\n        });\n        this.onGetBitrates.emit(videoList);\n      });\n      // @ts-ignore\n      this.hls.on(Hls.Events.LEVEL_LOADED, (_event, data) => {\n        this.target.isLive = data.details.live;\n      });\n      this.hls.loadSource(this.vgHls);\n      this.hls.attachMedia(video);\n    } else {\n      if (this.target && !!this.target.pause) {\n        this.target.pause();\n        this.target.seekTime(0);\n        this.ref.nativeElement.src = this.vgHls;\n      }\n    }\n  }\n  setBitrate(bitrate) {\n    if (this.hls) {\n      this.hls.nextLevel = bitrate.qualityIndex - 1;\n    }\n  }\n  destroyPlayer() {\n    if (this.hls) {\n      this.hls.destroy();\n      this.hls = null;\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n    this.destroyPlayer();\n    delete this.hls;\n  }\n}\n/** @nocollapse */\nVgHlsDirective.ɵfac = function VgHlsDirective_Factory(t) {\n  return new (t || VgHlsDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgHlsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: VgHlsDirective,\n  selectors: [[\"\", \"vgHls\", \"\"]],\n  inputs: {\n    vgHls: \"vgHls\",\n    vgHlsHeaders: \"vgHlsHeaders\"\n  },\n  outputs: {\n    onGetBitrates: \"onGetBitrates\"\n  },\n  exportAs: [\"vgHls\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgHlsDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[vgHls]',\n      exportAs: 'vgHls'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgHls: [{\n      type: Input\n    }],\n    vgHlsHeaders: [{\n      type: Input\n    }],\n    onGetBitrates: [{\n      type: Output\n    }]\n  });\n})();\nclass VgStreamingModule {}\n/** @nocollapse */\nVgStreamingModule.ɵfac = function VgStreamingModule_Factory(t) {\n  return new (t || VgStreamingModule)();\n};\n/** @nocollapse */\nVgStreamingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: VgStreamingModule\n});\n/** @nocollapse */\nVgStreamingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, VgCoreModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgStreamingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, VgCoreModule],\n      declarations: [VgDashDirective, VgHlsDirective],\n      exports: [VgDashDirective, VgHlsDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgDashDirective, VgHlsDirective, VgStreamingModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Directive", "Input", "Output", "NgModule", "CommonModule", "i1", "VgCoreModule", "VgDashDirective", "constructor", "ref", "API", "onGetBitrates", "subscriptions", "ngOnInit", "isPlayerReady", "onPlayerReady", "push", "player<PERSON><PERSON>yEvent", "subscribe", "vgFor", "nativeElement", "getAttribute", "target", "getMediaById", "createPlayer", "ngOnChanges", "changes", "vgDash", "currentValue", "destroyPlayer", "dash", "indexOf", "drmOptions", "vgDRMLicenseServer", "vgDRMToken", "drmServer", "hasOwnProperty", "httpRequestHeaders", "Authorization", "dashjs", "MediaPlayer", "create", "updateSettings", "debug", "logLevel", "Debug", "LOG_LEVEL_NONE", "initialize", "setAutoPlay", "on", "events", "STREAM_INITIALIZED", "audioList", "getBitrateInfoListFor", "videoList", "length", "for<PERSON>ach", "item", "qualityIndex", "unshift", "width", "height", "bitrate", "mediaType", "scanType", "emit", "setProtectionData", "attachSource", "pause", "seekTime", "src", "setBitrate", "getSettings", "streaming", "abr", "autoSwitchBitrate", "nextIndex", "setQualityFor", "reset", "ngOnDestroy", "s", "unsubscribe", "ɵfac", "VgDashDirective_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "VgApiService", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "outputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "VgHlsDirective", "vgHlsHeaders", "crossorigin", "preload", "getDefaultMedia", "config", "autoStartLoad", "xhrSetup", "xhr", "withCredentials", "key", "Object", "keys", "setRequestHeader", "play", "hls", "startLoad", "vgHls", "Hls", "isSupported", "video", "Events", "MANIFEST_PARSED", "_event", "data", "label", "levels", "index", "name", "LEVEL_LOADED", "isLive", "details", "live", "loadSource", "attachMedia", "nextLevel", "destroy", "VgHlsDirective_Factory", "VgStreamingModule", "VgStreamingModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@videogular/ngx-videogular/fesm2020/videogular-ngx-videogular-streaming.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@videogular/ngx-videogular/core';\nimport { VgCoreModule } from '@videogular/ngx-videogular/core';\n\nclass VgDashDirective {\n    constructor(ref, API) {\n        this.ref = ref;\n        this.API = API;\n        this.onGetBitrates = new EventEmitter();\n        this.subscriptions = [];\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.vgFor = this.ref.nativeElement.getAttribute('vgFor');\n        this.target = this.API.getMediaById(this.vgFor);\n        this.createPlayer();\n    }\n    ngOnChanges(changes) {\n        changes.vgDash?.currentValue ? this.createPlayer() : this.destroyPlayer();\n    }\n    createPlayer() {\n        if (this.dash) {\n            this.destroyPlayer();\n        }\n        // It's a DASH source\n        if (this.vgDash &&\n            (this.vgDash.indexOf('.mpd') > -1 ||\n                this.vgDash.indexOf('mpd-time-csf') > -1)) {\n            let drmOptions;\n            if (this.vgDRMLicenseServer) {\n                drmOptions = this.vgDRMLicenseServer;\n                if (this.vgDRMToken) {\n                    for (const drmServer in drmOptions) {\n                        if (drmServer.hasOwnProperty(drmServer)) {\n                            drmOptions[drmServer].httpRequestHeaders = {\n                                Authorization: this.vgDRMToken,\n                            };\n                        }\n                    }\n                }\n            }\n            this.dash = dashjs.MediaPlayer().create();\n            this.dash.updateSettings({ debug: { logLevel: dashjs.Debug.LOG_LEVEL_NONE } });\n            this.dash.initialize(this.ref.nativeElement);\n            this.dash.setAutoPlay(false);\n            this.dash.on(dashjs.MediaPlayer.events.STREAM_INITIALIZED, () => {\n                const audioList = this.dash.getBitrateInfoListFor('audio');\n                const videoList = this.dash.getBitrateInfoListFor('video');\n                if (audioList.length > 1) {\n                    audioList.forEach((item) => (item.qualityIndex = ++item.qualityIndex));\n                    audioList.unshift({\n                        qualityIndex: 0,\n                        width: 0,\n                        height: 0,\n                        bitrate: 0,\n                        mediaType: 'video',\n                        scanType: 'AUTO',\n                    });\n                    this.onGetBitrates.emit(audioList);\n                }\n                if (videoList.length > 1) {\n                    videoList.forEach((item) => (item.qualityIndex = ++item.qualityIndex));\n                    videoList.unshift({\n                        qualityIndex: 0,\n                        width: 0,\n                        height: 0,\n                        bitrate: 0,\n                        mediaType: 'video',\n                        scanType: 'AUTO',\n                    });\n                    this.onGetBitrates.emit(videoList);\n                }\n            });\n            if (drmOptions) {\n                this.dash.setProtectionData(drmOptions);\n            }\n            this.dash.attachSource(this.vgDash);\n        }\n        else {\n            if (this.target) {\n                this.target.pause();\n                this.target.seekTime(0);\n                this.ref.nativeElement.src = this.vgDash;\n            }\n        }\n    }\n    setBitrate({ mediaType, qualityIndex }) {\n        if (this.dash) {\n            if (qualityIndex > 0) {\n                if (this.dash.getSettings()) {\n                    this.dash.updateSettings({\n                        streaming: {\n                            abr: {\n                                autoSwitchBitrate: {\n                                    [mediaType]: false\n                                }\n                            }\n                        }\n                    });\n                }\n                const nextIndex = qualityIndex - 1;\n                this.dash.setQualityFor(mediaType, nextIndex);\n            }\n            else {\n                this.dash.updateSettings({\n                    streaming: {\n                        abr: {\n                            autoSwitchBitrate: {\n                                [mediaType]: true\n                            }\n                        }\n                    }\n                });\n            }\n        }\n    }\n    destroyPlayer() {\n        if (this.dash) {\n            this.dash.reset();\n            this.dash = null;\n        }\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n        this.destroyPlayer();\n    }\n}\n/** @nocollapse */ VgDashDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgDashDirective, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ VgDashDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgDashDirective, selector: \"[vgDash]\", inputs: { vgDash: \"vgDash\", vgDRMToken: \"vgDRMToken\", vgDRMLicenseServer: \"vgDRMLicenseServer\" }, outputs: { onGetBitrates: \"onGetBitrates\" }, exportAs: [\"vgDash\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgDashDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[vgDash]',\n                    exportAs: 'vgDash',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgDash: [{\n                type: Input\n            }], vgDRMToken: [{\n                type: Input\n            }], vgDRMLicenseServer: [{\n                type: Input\n            }], onGetBitrates: [{\n                type: Output\n            }] } });\n\nclass VgHlsDirective {\n    constructor(ref, API) {\n        this.ref = ref;\n        this.API = API;\n        this.vgHlsHeaders = {};\n        this.onGetBitrates = new EventEmitter();\n        this.subscriptions = [];\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.crossorigin = this.ref.nativeElement.getAttribute('crossorigin');\n        this.preload = this.ref.nativeElement.getAttribute('preload') !== 'none';\n        this.vgFor = this.ref.nativeElement.getAttribute('vgFor');\n        if (this.vgFor) {\n            this.target = this.API.getMediaById(this.vgFor);\n        }\n        else {\n            this.target = this.API.getDefaultMedia();\n        }\n        this.config = {\n            autoStartLoad: this.preload,\n        };\n        // @ts-ignore\n        this.config.xhrSetup = (xhr) => {\n            // Send cookies\n            if (this.crossorigin === 'use-credentials') {\n                xhr.withCredentials = true;\n            }\n            for (const key of Object.keys(this.vgHlsHeaders)) {\n                xhr.setRequestHeader(key, this.vgHlsHeaders[key]);\n            }\n        };\n        this.createPlayer();\n        if (!this.preload) {\n            this.subscriptions.push(this.API.subscriptions.play.subscribe(() => {\n                if (this.hls) {\n                    this.hls.startLoad(0);\n                }\n            }));\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes.vgHls?.currentValue) {\n            this.createPlayer();\n        }\n        else if (changes.vgHlsHeaders && changes.vgHlsHeaders.currentValue) {\n            // Do nothing. We don't want to create a or destroy a player if the headers change.\n        }\n        else {\n            this.destroyPlayer();\n        }\n    }\n    createPlayer() {\n        if (this.hls) {\n            this.destroyPlayer();\n        }\n        // It's a HLS source\n        if (this.vgHls &&\n            this.vgHls.indexOf('m3u8') > -1 &&\n            Hls.isSupported() &&\n            this.API.isPlayerReady) {\n            const video = this.ref.nativeElement;\n            this.hls = new Hls(this.config);\n            // @ts-ignore\n            this.hls.on(Hls.Events.MANIFEST_PARSED, (_event, data) => {\n                const videoList = [];\n                videoList.push({\n                    qualityIndex: 0,\n                    width: 0,\n                    height: 0,\n                    bitrate: 0,\n                    mediaType: 'video',\n                    label: 'AUTO',\n                });\n                data.levels.forEach((item, index) => {\n                    videoList.push({\n                        qualityIndex: ++index,\n                        width: item.width,\n                        height: item.height,\n                        bitrate: item.bitrate,\n                        mediaType: 'video',\n                        label: item.name,\n                    });\n                });\n                this.onGetBitrates.emit(videoList);\n            });\n            // @ts-ignore\n            this.hls.on(Hls.Events.LEVEL_LOADED, (_event, data) => {\n                this.target.isLive = data.details.live;\n            });\n            this.hls.loadSource(this.vgHls);\n            this.hls.attachMedia(video);\n        }\n        else {\n            if (this.target && !!this.target.pause) {\n                this.target.pause();\n                this.target.seekTime(0);\n                this.ref.nativeElement.src = this.vgHls;\n            }\n        }\n    }\n    setBitrate(bitrate) {\n        if (this.hls) {\n            this.hls.nextLevel = bitrate.qualityIndex - 1;\n        }\n    }\n    destroyPlayer() {\n        if (this.hls) {\n            this.hls.destroy();\n            this.hls = null;\n        }\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n        this.destroyPlayer();\n        delete this.hls;\n    }\n}\n/** @nocollapse */ VgHlsDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgHlsDirective, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ VgHlsDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgHlsDirective, selector: \"[vgHls]\", inputs: { vgHls: \"vgHls\", vgHlsHeaders: \"vgHlsHeaders\" }, outputs: { onGetBitrates: \"onGetBitrates\" }, exportAs: [\"vgHls\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgHlsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[vgHls]',\n                    exportAs: 'vgHls',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgHls: [{\n                type: Input\n            }], vgHlsHeaders: [{\n                type: Input\n            }], onGetBitrates: [{\n                type: Output\n            }] } });\n\nclass VgStreamingModule {\n}\n/** @nocollapse */ VgStreamingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStreamingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ VgStreamingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStreamingModule, declarations: [VgDashDirective, VgHlsDirective], imports: [CommonModule, VgCoreModule], exports: [VgDashDirective, VgHlsDirective] });\n/** @nocollapse */ VgStreamingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStreamingModule, imports: [CommonModule, VgCoreModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStreamingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, VgCoreModule],\n                    declarations: [VgDashDirective, VgHlsDirective],\n                    exports: [VgDashDirective, VgHlsDirective]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgDashDirective, VgHlsDirective, VgStreamingModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAChF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,YAAY,QAAQ,iCAAiC;AAE9D,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,GAAG,EAAEC,GAAG,EAAE;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,aAAa,GAAG,IAAIZ,YAAY,CAAC,CAAC;IACvC,IAAI,CAACa,aAAa,GAAG,EAAE;EAC3B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACH,GAAG,CAACI,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACH,aAAa,CAACI,IAAI,CAAC,IAAI,CAACN,GAAG,CAACO,gBAAgB,CAACC,SAAS,CAAC,MAAM,IAAI,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACI,KAAK,GAAG,IAAI,CAACV,GAAG,CAACW,aAAa,CAACC,YAAY,CAAC,OAAO,CAAC;IACzD,IAAI,CAACC,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACa,YAAY,CAAC,IAAI,CAACJ,KAAK,CAAC;IAC/C,IAAI,CAACK,YAAY,CAAC,CAAC;EACvB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjBA,OAAO,CAACC,MAAM,EAAEC,YAAY,GAAG,IAAI,CAACJ,YAAY,CAAC,CAAC,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC;EAC7E;EACAL,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACM,IAAI,EAAE;MACX,IAAI,CAACD,aAAa,CAAC,CAAC;IACxB;IACA;IACA,IAAI,IAAI,CAACF,MAAM,KACV,IAAI,CAACA,MAAM,CAACI,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAC7B,IAAI,CAACJ,MAAM,CAACI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/C,IAAIC,UAAU;MACd,IAAI,IAAI,CAACC,kBAAkB,EAAE;QACzBD,UAAU,GAAG,IAAI,CAACC,kBAAkB;QACpC,IAAI,IAAI,CAACC,UAAU,EAAE;UACjB,KAAK,MAAMC,SAAS,IAAIH,UAAU,EAAE;YAChC,IAAIG,SAAS,CAACC,cAAc,CAACD,SAAS,CAAC,EAAE;cACrCH,UAAU,CAACG,SAAS,CAAC,CAACE,kBAAkB,GAAG;gBACvCC,aAAa,EAAE,IAAI,CAACJ;cACxB,CAAC;YACL;UACJ;QACJ;MACJ;MACA,IAAI,CAACJ,IAAI,GAAGS,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACzC,IAAI,CAACX,IAAI,CAACY,cAAc,CAAC;QAAEC,KAAK,EAAE;UAAEC,QAAQ,EAAEL,MAAM,CAACM,KAAK,CAACC;QAAe;MAAE,CAAC,CAAC;MAC9E,IAAI,CAAChB,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACtC,GAAG,CAACW,aAAa,CAAC;MAC5C,IAAI,CAACU,IAAI,CAACkB,WAAW,CAAC,KAAK,CAAC;MAC5B,IAAI,CAAClB,IAAI,CAACmB,EAAE,CAACV,MAAM,CAACC,WAAW,CAACU,MAAM,CAACC,kBAAkB,EAAE,MAAM;QAC7D,MAAMC,SAAS,GAAG,IAAI,CAACtB,IAAI,CAACuB,qBAAqB,CAAC,OAAO,CAAC;QAC1D,MAAMC,SAAS,GAAG,IAAI,CAACxB,IAAI,CAACuB,qBAAqB,CAAC,OAAO,CAAC;QAC1D,IAAID,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;UACtBH,SAAS,CAACI,OAAO,CAAEC,IAAI,IAAMA,IAAI,CAACC,YAAY,GAAG,EAAED,IAAI,CAACC,YAAa,CAAC;UACtEN,SAAS,CAACO,OAAO,CAAC;YACdD,YAAY,EAAE,CAAC;YACfE,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTC,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,OAAO;YAClBC,QAAQ,EAAE;UACd,CAAC,CAAC;UACF,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAACb,SAAS,CAAC;QACtC;QACA,IAAIE,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACtBD,SAAS,CAACE,OAAO,CAAEC,IAAI,IAAMA,IAAI,CAACC,YAAY,GAAG,EAAED,IAAI,CAACC,YAAa,CAAC;UACtEJ,SAAS,CAACK,OAAO,CAAC;YACdD,YAAY,EAAE,CAAC;YACfE,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTC,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,OAAO;YAClBC,QAAQ,EAAE;UACd,CAAC,CAAC;UACF,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAACX,SAAS,CAAC;QACtC;MACJ,CAAC,CAAC;MACF,IAAItB,UAAU,EAAE;QACZ,IAAI,CAACF,IAAI,CAACoC,iBAAiB,CAAClC,UAAU,CAAC;MAC3C;MACA,IAAI,CAACF,IAAI,CAACqC,YAAY,CAAC,IAAI,CAACxC,MAAM,CAAC;IACvC,CAAC,MACI;MACD,IAAI,IAAI,CAACL,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAAC8C,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC5D,GAAG,CAACW,aAAa,CAACkD,GAAG,GAAG,IAAI,CAAC3C,MAAM;MAC5C;IACJ;EACJ;EACA4C,UAAUA,CAAC;IAAER,SAAS;IAAEL;EAAa,CAAC,EAAE;IACpC,IAAI,IAAI,CAAC5B,IAAI,EAAE;MACX,IAAI4B,YAAY,GAAG,CAAC,EAAE;QAClB,IAAI,IAAI,CAAC5B,IAAI,CAAC0C,WAAW,CAAC,CAAC,EAAE;UACzB,IAAI,CAAC1C,IAAI,CAACY,cAAc,CAAC;YACrB+B,SAAS,EAAE;cACPC,GAAG,EAAE;gBACDC,iBAAiB,EAAE;kBACf,CAACZ,SAAS,GAAG;gBACjB;cACJ;YACJ;UACJ,CAAC,CAAC;QACN;QACA,MAAMa,SAAS,GAAGlB,YAAY,GAAG,CAAC;QAClC,IAAI,CAAC5B,IAAI,CAAC+C,aAAa,CAACd,SAAS,EAAEa,SAAS,CAAC;MACjD,CAAC,MACI;QACD,IAAI,CAAC9C,IAAI,CAACY,cAAc,CAAC;UACrB+B,SAAS,EAAE;YACPC,GAAG,EAAE;cACDC,iBAAiB,EAAE;gBACf,CAACZ,SAAS,GAAG;cACjB;YACJ;UACJ;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACAlC,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACgD,KAAK,CAAC,CAAC;MACjB,IAAI,CAAChD,IAAI,GAAG,IAAI;IACpB;EACJ;EACAiD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnE,aAAa,CAAC4C,OAAO,CAAEwB,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IAClD,IAAI,CAACpD,aAAa,CAAC,CAAC;EACxB;AACJ;AACA;AAAmBtB,eAAe,CAAC2E,IAAI,YAAAC,wBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwF7E,eAAe,EAAzBT,EAAE,CAAAuF,iBAAA,CAAyCvF,EAAE,CAACwF,UAAU,GAAxDxF,EAAE,CAAAuF,iBAAA,CAAmEhF,EAAE,CAACkF,YAAY;AAAA,CAA4C;AACrP;AAAmBhF,eAAe,CAACiF,IAAI,kBAD8E1F,EAAE,CAAA2F,iBAAA;EAAAC,IAAA,EACJnF,eAAe;EAAAoF,SAAA;EAAAC,MAAA;IAAAjE,MAAA;IAAAO,UAAA;IAAAD,kBAAA;EAAA;EAAA4D,OAAA;IAAAlF,aAAA;EAAA;EAAAmF,QAAA;EAAAC,QAAA,GADbjG,EAAE,CAAAkG,oBAAA;AAAA,EAC4O;AACnW;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFqHnG,EAAE,CAAAoG,iBAAA,CAE5B3F,eAAe,EAAc,CAAC;IAC7GmF,IAAI,EAAE1F,SAAS;IACfmG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBN,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAE5F,EAAE,CAACwF;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAErF,EAAE,CAACkF;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE5D,MAAM,EAAE,CAAC;MACrH+D,IAAI,EAAEzF;IACV,CAAC,CAAC;IAAEiC,UAAU,EAAE,CAAC;MACbwD,IAAI,EAAEzF;IACV,CAAC,CAAC;IAAEgC,kBAAkB,EAAE,CAAC;MACrByD,IAAI,EAAEzF;IACV,CAAC,CAAC;IAAEU,aAAa,EAAE,CAAC;MAChB+E,IAAI,EAAExF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmG,cAAc,CAAC;EACjB7F,WAAWA,CAACC,GAAG,EAAEC,GAAG,EAAE;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC4F,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAAC3F,aAAa,GAAG,IAAIZ,YAAY,CAAC,CAAC;IACvC,IAAI,CAACa,aAAa,GAAG,EAAE;EAC3B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACH,GAAG,CAACI,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACH,aAAa,CAACI,IAAI,CAAC,IAAI,CAACN,GAAG,CAACO,gBAAgB,CAACC,SAAS,CAAC,MAAM,IAAI,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACwF,WAAW,GAAG,IAAI,CAAC9F,GAAG,CAACW,aAAa,CAACC,YAAY,CAAC,aAAa,CAAC;IACrE,IAAI,CAACmF,OAAO,GAAG,IAAI,CAAC/F,GAAG,CAACW,aAAa,CAACC,YAAY,CAAC,SAAS,CAAC,KAAK,MAAM;IACxE,IAAI,CAACF,KAAK,GAAG,IAAI,CAACV,GAAG,CAACW,aAAa,CAACC,YAAY,CAAC,OAAO,CAAC;IACzD,IAAI,IAAI,CAACF,KAAK,EAAE;MACZ,IAAI,CAACG,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACa,YAAY,CAAC,IAAI,CAACJ,KAAK,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAACG,MAAM,GAAG,IAAI,CAACZ,GAAG,CAAC+F,eAAe,CAAC,CAAC;IAC5C;IACA,IAAI,CAACC,MAAM,GAAG;MACVC,aAAa,EAAE,IAAI,CAACH;IACxB,CAAC;IACD;IACA,IAAI,CAACE,MAAM,CAACE,QAAQ,GAAIC,GAAG,IAAK;MAC5B;MACA,IAAI,IAAI,CAACN,WAAW,KAAK,iBAAiB,EAAE;QACxCM,GAAG,CAACC,eAAe,GAAG,IAAI;MAC9B;MACA,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACX,YAAY,CAAC,EAAE;QAC9CO,GAAG,CAACK,gBAAgB,CAACH,GAAG,EAAE,IAAI,CAACT,YAAY,CAACS,GAAG,CAAC,CAAC;MACrD;IACJ,CAAC;IACD,IAAI,CAACvF,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC,IAAI,CAACgF,OAAO,EAAE;MACf,IAAI,CAAC5F,aAAa,CAACI,IAAI,CAAC,IAAI,CAACN,GAAG,CAACE,aAAa,CAACuG,IAAI,CAACjG,SAAS,CAAC,MAAM;QAChE,IAAI,IAAI,CAACkG,GAAG,EAAE;UACV,IAAI,CAACA,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;IACP;EACJ;EACA5F,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC4F,KAAK,EAAE1F,YAAY,EAAE;MAC7B,IAAI,CAACJ,YAAY,CAAC,CAAC;IACvB,CAAC,MACI,IAAIE,OAAO,CAAC4E,YAAY,IAAI5E,OAAO,CAAC4E,YAAY,CAAC1E,YAAY,EAAE;MAChE;IAAA,CACH,MACI;MACD,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAL,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC4F,GAAG,EAAE;MACV,IAAI,CAACvF,aAAa,CAAC,CAAC;IACxB;IACA;IACA,IAAI,IAAI,CAACyF,KAAK,IACV,IAAI,CAACA,KAAK,CAACvF,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAC/BwF,GAAG,CAACC,WAAW,CAAC,CAAC,IACjB,IAAI,CAAC9G,GAAG,CAACI,aAAa,EAAE;MACxB,MAAM2G,KAAK,GAAG,IAAI,CAAChH,GAAG,CAACW,aAAa;MACpC,IAAI,CAACgG,GAAG,GAAG,IAAIG,GAAG,CAAC,IAAI,CAACb,MAAM,CAAC;MAC/B;MACA,IAAI,CAACU,GAAG,CAACnE,EAAE,CAACsE,GAAG,CAACG,MAAM,CAACC,eAAe,EAAE,CAACC,MAAM,EAAEC,IAAI,KAAK;QACtD,MAAMvE,SAAS,GAAG,EAAE;QACpBA,SAAS,CAACtC,IAAI,CAAC;UACX0C,YAAY,EAAE,CAAC;UACfE,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,OAAO;UAClB+D,KAAK,EAAE;QACX,CAAC,CAAC;QACFD,IAAI,CAACE,MAAM,CAACvE,OAAO,CAAC,CAACC,IAAI,EAAEuE,KAAK,KAAK;UACjC1E,SAAS,CAACtC,IAAI,CAAC;YACX0C,YAAY,EAAE,EAAEsE,KAAK;YACrBpE,KAAK,EAAEH,IAAI,CAACG,KAAK;YACjBC,MAAM,EAAEJ,IAAI,CAACI,MAAM;YACnBC,OAAO,EAAEL,IAAI,CAACK,OAAO;YACrBC,SAAS,EAAE,OAAO;YAClB+D,KAAK,EAAErE,IAAI,CAACwE;UAChB,CAAC,CAAC;QACN,CAAC,CAAC;QACF,IAAI,CAACtH,aAAa,CAACsD,IAAI,CAACX,SAAS,CAAC;MACtC,CAAC,CAAC;MACF;MACA,IAAI,CAAC8D,GAAG,CAACnE,EAAE,CAACsE,GAAG,CAACG,MAAM,CAACQ,YAAY,EAAE,CAACN,MAAM,EAAEC,IAAI,KAAK;QACnD,IAAI,CAACvG,MAAM,CAAC6G,MAAM,GAAGN,IAAI,CAACO,OAAO,CAACC,IAAI;MAC1C,CAAC,CAAC;MACF,IAAI,CAACjB,GAAG,CAACkB,UAAU,CAAC,IAAI,CAAChB,KAAK,CAAC;MAC/B,IAAI,CAACF,GAAG,CAACmB,WAAW,CAACd,KAAK,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,IAAI,CAACnG,MAAM,IAAI,CAAC,CAAC,IAAI,CAACA,MAAM,CAAC8C,KAAK,EAAE;QACpC,IAAI,CAAC9C,MAAM,CAAC8C,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC5D,GAAG,CAACW,aAAa,CAACkD,GAAG,GAAG,IAAI,CAACgD,KAAK;MAC3C;IACJ;EACJ;EACA/C,UAAUA,CAACT,OAAO,EAAE;IAChB,IAAI,IAAI,CAACsD,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAACoB,SAAS,GAAG1E,OAAO,CAACJ,YAAY,GAAG,CAAC;IACjD;EACJ;EACA7B,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACuF,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAACqB,OAAO,CAAC,CAAC;MAClB,IAAI,CAACrB,GAAG,GAAG,IAAI;IACnB;EACJ;EACArC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnE,aAAa,CAAC4C,OAAO,CAAEwB,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IAClD,IAAI,CAACpD,aAAa,CAAC,CAAC;IACpB,OAAO,IAAI,CAACuF,GAAG;EACnB;AACJ;AACA;AAAmBf,cAAc,CAACnB,IAAI,YAAAwD,uBAAAtD,CAAA;EAAA,YAAAA,CAAA,IAAwFiB,cAAc,EA/IvBvG,EAAE,CAAAuF,iBAAA,CA+IuCvF,EAAE,CAACwF,UAAU,GA/ItDxF,EAAE,CAAAuF,iBAAA,CA+IiEhF,EAAE,CAACkF,YAAY;AAAA,CAA4C;AACnP;AAAmBc,cAAc,CAACb,IAAI,kBAhJ+E1F,EAAE,CAAA2F,iBAAA;EAAAC,IAAA,EAgJLW,cAAc;EAAAV,SAAA;EAAAC,MAAA;IAAA0B,KAAA;IAAAhB,YAAA;EAAA;EAAAT,OAAA;IAAAlF,aAAA;EAAA;EAAAmF,QAAA;EAAAC,QAAA,GAhJXjG,EAAE,CAAAkG,oBAAA;AAAA,EAgJgM;AACvT;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjJqHnG,EAAE,CAAAoG,iBAAA,CAiJ5BG,cAAc,EAAc,CAAC;IAC5GX,IAAI,EAAE1F,SAAS;IACfmG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,SAAS;MACnBN,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEJ,IAAI,EAAE5F,EAAE,CAACwF;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAErF,EAAE,CAACkF;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+B,KAAK,EAAE,CAAC;MACpH5B,IAAI,EAAEzF;IACV,CAAC,CAAC;IAAEqG,YAAY,EAAE,CAAC;MACfZ,IAAI,EAAEzF;IACV,CAAC,CAAC;IAAEU,aAAa,EAAE,CAAC;MAChB+E,IAAI,EAAExF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyI,iBAAiB,CAAC;AAExB;AAAmBA,iBAAiB,CAACzD,IAAI,YAAA0D,0BAAAxD,CAAA;EAAA,YAAAA,CAAA,IAAwFuD,iBAAiB;AAAA,CAAkD;AACpM;AAAmBA,iBAAiB,CAACE,IAAI,kBAlK4E/I,EAAE,CAAAgJ,gBAAA;EAAApD,IAAA,EAkKWiD;AAAiB,EAAuI;AAC1R;AAAmBA,iBAAiB,CAACI,IAAI,kBAnK4EjJ,EAAE,CAAAkJ,gBAAA;EAAAC,OAAA,GAmKwC7I,YAAY,EAAEE,YAAY;AAAA,EAAI;AAC7L;EAAA,QAAA2F,SAAA,oBAAAA,SAAA,KApKqHnG,EAAE,CAAAoG,iBAAA,CAoK5ByC,iBAAiB,EAAc,CAAC;IAC/GjD,IAAI,EAAEvF,QAAQ;IACdgG,IAAI,EAAE,CAAC;MACC8C,OAAO,EAAE,CAAC7I,YAAY,EAAEE,YAAY,CAAC;MACrC4I,YAAY,EAAE,CAAC3I,eAAe,EAAE8F,cAAc,CAAC;MAC/C8C,OAAO,EAAE,CAAC5I,eAAe,EAAE8F,cAAc;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS9F,eAAe,EAAE8F,cAAc,EAAEsC,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
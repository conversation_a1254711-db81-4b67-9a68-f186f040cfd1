{"ast": null, "code": "import { DatePipe } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class LocalizedDatePipe {\n  constructor(translateService) {\n    this.translateService = translateService;\n  }\n  transform(value, pattern = 'mediumDate') {\n    let locale = this.translateService.currentLang;\n    switch (this.translateService.currentLang) {\n      case 'zh_HK':\n        locale = 'zh-Hant-HK';\n        break;\n      case 'zh_CN':\n        locale = 'zh-Hans';\n        break;\n      case 'en_US':\n        locale = 'en';\n        break;\n      default:\n        locale = this.translateService.currentLang;\n        break;\n    }\n    const datePipe = new DatePipe(locale);\n    return datePipe.transform(value, pattern);\n  }\n  static #_ = this.ɵfac = function LocalizedDatePipe_Factory(t) {\n    return new (t || LocalizedDatePipe)(i0.ɵɵdirectiveInject(i1.TranslateService, 16));\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"localizedDate\",\n    type: LocalizedDatePipe,\n    pure: false\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;;;AAQ1C,OAAM,MAAOC,iBAAiB;EAC5BC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;EAAqB;EAEzDC,SAASA,CAACC,KAAU,EAAEC,OAAA,GAAkB,YAAY;IAClD,IAAIC,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAACK,WAAW;IAC9C,QAAQ,IAAI,CAACL,gBAAgB,CAACK,WAAW;MACvC,KAAK,OAAO;QACVD,MAAM,GAAG,YAAY;QACrB;MACF,KAAK,OAAO;QACVA,MAAM,GAAG,SAAS;QAClB;MACF,KAAK,OAAO;QACVA,MAAM,GAAG,IAAI;QACb;MACF;QACEA,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAACK,WAAW;QAC1C;;IAGJ,MAAMC,QAAQ,GAAa,IAAIT,QAAQ,CAACO,MAAM,CAAC;IAC/C,OAAOE,QAAQ,CAACL,SAAS,CAACC,KAAK,EAAEC,OAAO,CAAC;EAC3C;EAAC,QAAAI,CAAA;qBAtBUT,iBAAiB,EAAAU,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;;UAAjBd,iBAAiB;IAAAe,IAAA;EAAA", "names": ["DatePipe", "LocalizedDatePipe", "constructor", "translateService", "transform", "value", "pattern", "locale", "currentLang", "datePipe", "_", "i0", "ɵɵdirectiveInject", "i1", "TranslateService", "_2", "pure"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\pipes\\localized-date.pipe.ts"], "sourcesContent": ["import { DatePipe } from '@angular/common';\r\nimport { Pipe, PipeTransform } from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\n@Pipe({\r\n  name: 'localizedDate',\r\n  pure: false,\r\n})\r\nexport class LocalizedDatePipe implements PipeTransform {\r\n  constructor(private translateService: TranslateService) {}\r\n\r\n  transform(value: any, pattern: string = 'mediumDate'): any {\r\n    let locale = this.translateService.currentLang;\r\n    switch (this.translateService.currentLang) {\r\n      case 'zh_HK':\r\n        locale = 'zh-Hant-HK';\r\n        break;\r\n      case 'zh_CN':\r\n        locale = 'zh-Hans';\r\n        break;\r\n      case 'en_US':\r\n        locale = 'en';\r\n        break;\r\n      default:\r\n        locale = this.translateService.currentLang;\r\n        break;\r\n    }\r\n\r\n    const datePipe: DatePipe = new DatePipe(locale);\r\n    return datePipe.transform(value, pattern);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/*!\n * lightgallery | 2.7.2 | September 20th 2023\n * http://www.lightgalleryjs.com/\n * Copyright (c) 2020 Sachin Neravath;\n * @license GPLv3\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n  return r;\n}\n\n/**\r\n * List of lightGallery events\r\n * All events should be documented here\r\n * Below interfaces are used to build the website documentations\r\n * */\nvar lGEvents = {\n  afterAppendSlide: 'lgAfterAppendSlide',\n  init: 'lgInit',\n  hasVideo: 'lgHasVideo',\n  containerResize: 'lgContainerResize',\n  updateSlides: 'lgUpdateSlides',\n  afterAppendSubHtml: 'lgAfterAppendSubHtml',\n  beforeOpen: 'lgBeforeOpen',\n  afterOpen: 'lgAfterOpen',\n  slideItemLoad: 'lgSlideItemLoad',\n  beforeSlide: 'lgBeforeSlide',\n  afterSlide: 'lgAfterSlide',\n  posterClick: 'lgPosterClick',\n  dragStart: 'lgDragStart',\n  dragMove: 'lgDragMove',\n  dragEnd: 'lgDragEnd',\n  beforeNextSlide: 'lgBeforeNextSlide',\n  beforePrevSlide: 'lgBeforePrevSlide',\n  beforeClose: 'lgBeforeClose',\n  afterClose: 'lgAfterClose',\n  rotateLeft: 'lgRotateLeft',\n  rotateRight: 'lgRotateRight',\n  flipHorizontal: 'lgFlipHorizontal',\n  flipVertical: 'lgFlipVertical',\n  autoplay: 'lgAutoplay',\n  autoplayStart: 'lgAutoplayStart',\n  autoplayStop: 'lgAutoplayStop'\n};\nvar lightGalleryCoreSettings = {\n  mode: 'lg-slide',\n  easing: 'ease',\n  speed: 400,\n  licenseKey: '0000-0000-000-0000',\n  height: '100%',\n  width: '100%',\n  addClass: '',\n  startClass: 'lg-start-zoom',\n  backdropDuration: 300,\n  container: '',\n  startAnimationDuration: 400,\n  zoomFromOrigin: true,\n  hideBarsDelay: 0,\n  showBarsAfter: 10000,\n  slideDelay: 0,\n  supportLegacyBrowser: true,\n  allowMediaOverlap: false,\n  videoMaxSize: '1280-720',\n  loadYouTubePoster: true,\n  defaultCaptionHeight: 0,\n  ariaLabelledby: '',\n  ariaDescribedby: '',\n  resetScrollPosition: true,\n  hideScrollbar: false,\n  closable: true,\n  swipeToClose: true,\n  closeOnTap: true,\n  showCloseIcon: true,\n  showMaximizeIcon: false,\n  loop: true,\n  escKey: true,\n  keyPress: true,\n  trapFocus: true,\n  controls: true,\n  slideEndAnimation: true,\n  hideControlOnEnd: false,\n  mousewheel: false,\n  getCaptionFromTitleOrAlt: true,\n  appendSubHtmlTo: '.lg-sub-html',\n  subHtmlSelectorRelative: false,\n  preload: 2,\n  numberOfSlideItemsInDom: 10,\n  selector: '',\n  selectWithin: '',\n  nextHtml: '',\n  prevHtml: '',\n  index: 0,\n  iframeWidth: '100%',\n  iframeHeight: '100%',\n  iframeMaxWidth: '100%',\n  iframeMaxHeight: '100%',\n  download: true,\n  counter: true,\n  appendCounterTo: '.lg-toolbar',\n  swipeThreshold: 50,\n  enableSwipe: true,\n  enableDrag: true,\n  dynamic: false,\n  dynamicEl: [],\n  extraProps: [],\n  exThumbImage: '',\n  isMobile: undefined,\n  mobileSettings: {\n    controls: false,\n    showCloseIcon: false,\n    download: false\n  },\n  plugins: [],\n  strings: {\n    closeGallery: 'Close gallery',\n    toggleMaximize: 'Toggle maximize',\n    previousSlide: 'Previous slide',\n    nextSlide: 'Next slide',\n    download: 'Download',\n    playVideo: 'Play video',\n    mediaLoadingFailed: 'Oops... Failed to load content...'\n  }\n};\nfunction initLgPolyfills() {\n  (function () {\n    if (typeof window.CustomEvent === 'function') return false;\n    function CustomEvent(event, params) {\n      params = params || {\n        bubbles: false,\n        cancelable: false,\n        detail: null\n      };\n      var evt = document.createEvent('CustomEvent');\n      evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);\n      return evt;\n    }\n    window.CustomEvent = CustomEvent;\n  })();\n  (function () {\n    if (!Element.prototype.matches) {\n      Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n    }\n  })();\n}\nvar lgQuery = /** @class */function () {\n  function lgQuery(selector) {\n    this.cssVenderPrefixes = ['TransitionDuration', 'TransitionTimingFunction', 'Transform', 'Transition'];\n    this.selector = this._getSelector(selector);\n    this.firstElement = this._getFirstEl();\n    return this;\n  }\n  lgQuery.generateUUID = function () {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      var r = Math.random() * 16 | 0,\n        v = c == 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  };\n  lgQuery.prototype._getSelector = function (selector, context) {\n    if (context === void 0) {\n      context = document;\n    }\n    if (typeof selector !== 'string') {\n      return selector;\n    }\n    context = context || document;\n    var fl = selector.substring(0, 1);\n    if (fl === '#') {\n      return context.querySelector(selector);\n    } else {\n      return context.querySelectorAll(selector);\n    }\n  };\n  lgQuery.prototype._each = function (func) {\n    if (!this.selector) {\n      return this;\n    }\n    if (this.selector.length !== undefined) {\n      [].forEach.call(this.selector, func);\n    } else {\n      func(this.selector, 0);\n    }\n    return this;\n  };\n  lgQuery.prototype._setCssVendorPrefix = function (el, cssProperty, value) {\n    // prettier-ignore\n    var property = cssProperty.replace(/-([a-z])/gi, function (s, group1) {\n      return group1.toUpperCase();\n    });\n    if (this.cssVenderPrefixes.indexOf(property) !== -1) {\n      el.style[property.charAt(0).toLowerCase() + property.slice(1)] = value;\n      el.style['webkit' + property] = value;\n      el.style['moz' + property] = value;\n      el.style['ms' + property] = value;\n      el.style['o' + property] = value;\n    } else {\n      el.style[property] = value;\n    }\n  };\n  lgQuery.prototype._getFirstEl = function () {\n    if (this.selector && this.selector.length !== undefined) {\n      return this.selector[0];\n    } else {\n      return this.selector;\n    }\n  };\n  lgQuery.prototype.isEventMatched = function (event, eventName) {\n    var eventNamespace = eventName.split('.');\n    return event.split('.').filter(function (e) {\n      return e;\n    }).every(function (e) {\n      return eventNamespace.indexOf(e) !== -1;\n    });\n  };\n  lgQuery.prototype.attr = function (attr, value) {\n    if (value === undefined) {\n      if (!this.firstElement) {\n        return '';\n      }\n      return this.firstElement.getAttribute(attr);\n    }\n    this._each(function (el) {\n      el.setAttribute(attr, value);\n    });\n    return this;\n  };\n  lgQuery.prototype.find = function (selector) {\n    return $LG(this._getSelector(selector, this.selector));\n  };\n  lgQuery.prototype.first = function () {\n    if (this.selector && this.selector.length !== undefined) {\n      return $LG(this.selector[0]);\n    } else {\n      return $LG(this.selector);\n    }\n  };\n  lgQuery.prototype.eq = function (index) {\n    return $LG(this.selector[index]);\n  };\n  lgQuery.prototype.parent = function () {\n    return $LG(this.selector.parentElement);\n  };\n  lgQuery.prototype.get = function () {\n    return this._getFirstEl();\n  };\n  lgQuery.prototype.removeAttr = function (attributes) {\n    var attrs = attributes.split(' ');\n    this._each(function (el) {\n      attrs.forEach(function (attr) {\n        return el.removeAttribute(attr);\n      });\n    });\n    return this;\n  };\n  lgQuery.prototype.wrap = function (className) {\n    if (!this.firstElement) {\n      return this;\n    }\n    var wrapper = document.createElement('div');\n    wrapper.className = className;\n    this.firstElement.parentNode.insertBefore(wrapper, this.firstElement);\n    this.firstElement.parentNode.removeChild(this.firstElement);\n    wrapper.appendChild(this.firstElement);\n    return this;\n  };\n  lgQuery.prototype.addClass = function (classNames) {\n    if (classNames === void 0) {\n      classNames = '';\n    }\n    this._each(function (el) {\n      // IE doesn't support multiple arguments\n      classNames.split(' ').forEach(function (className) {\n        if (className) {\n          el.classList.add(className);\n        }\n      });\n    });\n    return this;\n  };\n  lgQuery.prototype.removeClass = function (classNames) {\n    this._each(function (el) {\n      // IE doesn't support multiple arguments\n      classNames.split(' ').forEach(function (className) {\n        if (className) {\n          el.classList.remove(className);\n        }\n      });\n    });\n    return this;\n  };\n  lgQuery.prototype.hasClass = function (className) {\n    if (!this.firstElement) {\n      return false;\n    }\n    return this.firstElement.classList.contains(className);\n  };\n  lgQuery.prototype.hasAttribute = function (attribute) {\n    if (!this.firstElement) {\n      return false;\n    }\n    return this.firstElement.hasAttribute(attribute);\n  };\n  lgQuery.prototype.toggleClass = function (className) {\n    if (!this.firstElement) {\n      return this;\n    }\n    if (this.hasClass(className)) {\n      this.removeClass(className);\n    } else {\n      this.addClass(className);\n    }\n    return this;\n  };\n  lgQuery.prototype.css = function (property, value) {\n    var _this = this;\n    this._each(function (el) {\n      _this._setCssVendorPrefix(el, property, value);\n    });\n    return this;\n  };\n  // Need to pass separate namespaces for separate elements\n  lgQuery.prototype.on = function (events, listener) {\n    var _this = this;\n    if (!this.selector) {\n      return this;\n    }\n    events.split(' ').forEach(function (event) {\n      if (!Array.isArray(lgQuery.eventListeners[event])) {\n        lgQuery.eventListeners[event] = [];\n      }\n      lgQuery.eventListeners[event].push(listener);\n      _this.selector.addEventListener(event.split('.')[0], listener);\n    });\n    return this;\n  };\n  // @todo - test this\n  lgQuery.prototype.once = function (event, listener) {\n    var _this = this;\n    this.on(event, function () {\n      _this.off(event);\n      listener(event);\n    });\n    return this;\n  };\n  lgQuery.prototype.off = function (event) {\n    var _this = this;\n    if (!this.selector) {\n      return this;\n    }\n    Object.keys(lgQuery.eventListeners).forEach(function (eventName) {\n      if (_this.isEventMatched(event, eventName)) {\n        lgQuery.eventListeners[eventName].forEach(function (listener) {\n          _this.selector.removeEventListener(eventName.split('.')[0], listener);\n        });\n        lgQuery.eventListeners[eventName] = [];\n      }\n    });\n    return this;\n  };\n  lgQuery.prototype.trigger = function (event, detail) {\n    if (!this.firstElement) {\n      return this;\n    }\n    var customEvent = new CustomEvent(event.split('.')[0], {\n      detail: detail || null\n    });\n    this.firstElement.dispatchEvent(customEvent);\n    return this;\n  };\n  // Does not support IE\n  lgQuery.prototype.load = function (url) {\n    var _this = this;\n    fetch(url).then(function (res) {\n      return res.text();\n    }).then(function (html) {\n      _this.selector.innerHTML = html;\n    });\n    return this;\n  };\n  lgQuery.prototype.html = function (html) {\n    if (html === undefined) {\n      if (!this.firstElement) {\n        return '';\n      }\n      return this.firstElement.innerHTML;\n    }\n    this._each(function (el) {\n      el.innerHTML = html;\n    });\n    return this;\n  };\n  lgQuery.prototype.append = function (html) {\n    this._each(function (el) {\n      if (typeof html === 'string') {\n        el.insertAdjacentHTML('beforeend', html);\n      } else {\n        el.appendChild(html);\n      }\n    });\n    return this;\n  };\n  lgQuery.prototype.prepend = function (html) {\n    this._each(function (el) {\n      el.insertAdjacentHTML('afterbegin', html);\n    });\n    return this;\n  };\n  lgQuery.prototype.remove = function () {\n    this._each(function (el) {\n      el.parentNode.removeChild(el);\n    });\n    return this;\n  };\n  lgQuery.prototype.empty = function () {\n    this._each(function (el) {\n      el.innerHTML = '';\n    });\n    return this;\n  };\n  lgQuery.prototype.scrollTop = function (scrollTop) {\n    if (scrollTop !== undefined) {\n      document.body.scrollTop = scrollTop;\n      document.documentElement.scrollTop = scrollTop;\n      return this;\n    } else {\n      return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n    }\n  };\n  lgQuery.prototype.scrollLeft = function (scrollLeft) {\n    if (scrollLeft !== undefined) {\n      document.body.scrollLeft = scrollLeft;\n      document.documentElement.scrollLeft = scrollLeft;\n      return this;\n    } else {\n      return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;\n    }\n  };\n  lgQuery.prototype.offset = function () {\n    if (!this.firstElement) {\n      return {\n        left: 0,\n        top: 0\n      };\n    }\n    var rect = this.firstElement.getBoundingClientRect();\n    var bodyMarginLeft = $LG('body').style().marginLeft;\n    // Minus body margin - https://stackoverflow.com/questions/30711548/is-getboundingclientrect-left-returning-a-wrong-value\n    return {\n      left: rect.left - parseFloat(bodyMarginLeft) + this.scrollLeft(),\n      top: rect.top + this.scrollTop()\n    };\n  };\n  lgQuery.prototype.style = function () {\n    if (!this.firstElement) {\n      return {};\n    }\n    return this.firstElement.currentStyle || window.getComputedStyle(this.firstElement);\n  };\n  // Width without padding and border even if box-sizing is used.\n  lgQuery.prototype.width = function () {\n    var style = this.style();\n    return this.firstElement.clientWidth - parseFloat(style.paddingLeft) - parseFloat(style.paddingRight);\n  };\n  // Height without padding and border even if box-sizing is used.\n  lgQuery.prototype.height = function () {\n    var style = this.style();\n    return this.firstElement.clientHeight - parseFloat(style.paddingTop) - parseFloat(style.paddingBottom);\n  };\n  lgQuery.eventListeners = {};\n  return lgQuery;\n}();\nfunction $LG(selector) {\n  initLgPolyfills();\n  return new lgQuery(selector);\n}\nvar defaultDynamicOptions = ['src', 'sources', 'subHtml', 'subHtmlUrl', 'html', 'video', 'poster', 'slideName', 'responsive', 'srcset', 'sizes', 'iframe', 'downloadUrl', 'download', 'width', 'facebookShareUrl', 'tweetText', 'iframeTitle', 'twitterShareUrl', 'pinterestShareUrl', 'pinterestText', 'fbHtml', 'disqusIdentifier', 'disqusUrl'];\n// Convert html data-attribute to camalcase\nfunction convertToData(attr) {\n  // FInd a way for lgsize\n  if (attr === 'href') {\n    return 'src';\n  }\n  attr = attr.replace('data-', '');\n  attr = attr.charAt(0).toLowerCase() + attr.slice(1);\n  attr = attr.replace(/-([a-z])/g, function (g) {\n    return g[1].toUpperCase();\n  });\n  return attr;\n}\nvar utils = {\n  /**\r\n   * get possible width and height from the lgSize attribute. Used for ZoomFromOrigin option\r\n   */\n  getSize: function (el, container, spacing, defaultLgSize) {\n    if (spacing === void 0) {\n      spacing = 0;\n    }\n    var LGel = $LG(el);\n    var lgSize = LGel.attr('data-lg-size') || defaultLgSize;\n    if (!lgSize) {\n      return;\n    }\n    var isResponsiveSizes = lgSize.split(',');\n    // if at-least two viewport sizes are available\n    if (isResponsiveSizes[1]) {\n      var wWidth = window.innerWidth;\n      for (var i = 0; i < isResponsiveSizes.length; i++) {\n        var size_1 = isResponsiveSizes[i];\n        var responsiveWidth = parseInt(size_1.split('-')[2], 10);\n        if (responsiveWidth > wWidth) {\n          lgSize = size_1;\n          break;\n        }\n        // take last item as last option\n        if (i === isResponsiveSizes.length - 1) {\n          lgSize = size_1;\n        }\n      }\n    }\n    var size = lgSize.split('-');\n    var width = parseInt(size[0], 10);\n    var height = parseInt(size[1], 10);\n    var cWidth = container.width();\n    var cHeight = container.height() - spacing;\n    var maxWidth = Math.min(cWidth, width);\n    var maxHeight = Math.min(cHeight, height);\n    var ratio = Math.min(maxWidth / width, maxHeight / height);\n    return {\n      width: width * ratio,\n      height: height * ratio\n    };\n  },\n  /**\r\n   * @desc Get transform value based on the imageSize. Used for ZoomFromOrigin option\r\n   * @param {jQuery Element}\r\n   * @returns {String} Transform CSS string\r\n   */\n  getTransform: function (el, container, top, bottom, imageSize) {\n    if (!imageSize) {\n      return;\n    }\n    var LGel = $LG(el).find('img').first();\n    if (!LGel.get()) {\n      return;\n    }\n    var containerRect = container.get().getBoundingClientRect();\n    var wWidth = containerRect.width;\n    // using innerWidth to include mobile safari bottom bar\n    var wHeight = container.height() - (top + bottom);\n    var elWidth = LGel.width();\n    var elHeight = LGel.height();\n    var elStyle = LGel.style();\n    var x = (wWidth - elWidth) / 2 - LGel.offset().left + (parseFloat(elStyle.paddingLeft) || 0) + (parseFloat(elStyle.borderLeft) || 0) + $LG(window).scrollLeft() + containerRect.left;\n    var y = (wHeight - elHeight) / 2 - LGel.offset().top + (parseFloat(elStyle.paddingTop) || 0) + (parseFloat(elStyle.borderTop) || 0) + $LG(window).scrollTop() + top;\n    var scX = elWidth / imageSize.width;\n    var scY = elHeight / imageSize.height;\n    var transform = 'translate3d(' + (x *= -1) + 'px, ' + (y *= -1) + 'px, 0) scale3d(' + scX + ', ' + scY + ', 1)';\n    return transform;\n  },\n  getIframeMarkup: function (iframeWidth, iframeHeight, iframeMaxWidth, iframeMaxHeight, src, iframeTitle) {\n    var title = iframeTitle ? 'title=\"' + iframeTitle + '\"' : '';\n    return \"<div class=\\\"lg-video-cont lg-has-iframe\\\" style=\\\"width:\" + iframeWidth + \"; max-width:\" + iframeMaxWidth + \"; height: \" + iframeHeight + \"; max-height:\" + iframeMaxHeight + \"\\\">\\n                    <iframe class=\\\"lg-object\\\" frameborder=\\\"0\\\" \" + title + \" src=\\\"\" + src + \"\\\"  allowfullscreen=\\\"true\\\"></iframe>\\n                </div>\";\n  },\n  getImgMarkup: function (index, src, altAttr, srcset, sizes, sources) {\n    var srcsetAttr = srcset ? \"srcset=\\\"\" + srcset + \"\\\"\" : '';\n    var sizesAttr = sizes ? \"sizes=\\\"\" + sizes + \"\\\"\" : '';\n    var imgMarkup = \"<img \" + altAttr + \" \" + srcsetAttr + \"  \" + sizesAttr + \" class=\\\"lg-object lg-image\\\" data-index=\\\"\" + index + \"\\\" src=\\\"\" + src + \"\\\" />\";\n    var sourceTag = '';\n    if (sources) {\n      var sourceObj = typeof sources === 'string' ? JSON.parse(sources) : sources;\n      sourceTag = sourceObj.map(function (source) {\n        var attrs = '';\n        Object.keys(source).forEach(function (key) {\n          // Do not remove the first space as it is required to separate the attributes\n          attrs += \" \" + key + \"=\\\"\" + source[key] + \"\\\"\";\n        });\n        return \"<source \" + attrs + \"></source>\";\n      });\n    }\n    return \"\" + sourceTag + imgMarkup;\n  },\n  // Get src from responsive src\n  getResponsiveSrc: function (srcItms) {\n    var rsWidth = [];\n    var rsSrc = [];\n    var src = '';\n    for (var i = 0; i < srcItms.length; i++) {\n      var _src = srcItms[i].split(' ');\n      // Manage empty space\n      if (_src[0] === '') {\n        _src.splice(0, 1);\n      }\n      rsSrc.push(_src[0]);\n      rsWidth.push(_src[1]);\n    }\n    var wWidth = window.innerWidth;\n    for (var j = 0; j < rsWidth.length; j++) {\n      if (parseInt(rsWidth[j], 10) > wWidth) {\n        src = rsSrc[j];\n        break;\n      }\n    }\n    return src;\n  },\n  isImageLoaded: function (img) {\n    if (!img) return false;\n    // During the onload event, IE correctly identifies any images that\n    // weren’t downloaded as not complete. Others should too. Gecko-based\n    // browsers act like NS4 in that they report this incorrectly.\n    if (!img.complete) {\n      return false;\n    }\n    // However, they do have two very useful properties: naturalWidth and\n    // naturalHeight. These give the true size of the image. If it failed\n    // to load, either of these should be zero.\n    if (img.naturalWidth === 0) {\n      return false;\n    }\n    // No other way of checking: assume it’s ok.\n    return true;\n  },\n  getVideoPosterMarkup: function (_poster, dummyImg, videoContStyle, playVideoString, _isVideo) {\n    var videoClass = '';\n    if (_isVideo && _isVideo.youtube) {\n      videoClass = 'lg-has-youtube';\n    } else if (_isVideo && _isVideo.vimeo) {\n      videoClass = 'lg-has-vimeo';\n    } else {\n      videoClass = 'lg-has-html5';\n    }\n    return \"<div class=\\\"lg-video-cont \" + videoClass + \"\\\" style=\\\"\" + videoContStyle + \"\\\">\\n                <div class=\\\"lg-video-play-button\\\">\\n                <svg\\n                    viewBox=\\\"0 0 20 20\\\"\\n                    preserveAspectRatio=\\\"xMidYMid\\\"\\n                    focusable=\\\"false\\\"\\n                    aria-labelledby=\\\"\" + playVideoString + \"\\\"\\n                    role=\\\"img\\\"\\n                    class=\\\"lg-video-play-icon\\\"\\n                >\\n                    <title>\" + playVideoString + \"</title>\\n                    <polygon class=\\\"lg-video-play-icon-inner\\\" points=\\\"1,0 20,10 1,20\\\"></polygon>\\n                </svg>\\n                <svg class=\\\"lg-video-play-icon-bg\\\" viewBox=\\\"0 0 50 50\\\" focusable=\\\"false\\\">\\n                    <circle cx=\\\"50%\\\" cy=\\\"50%\\\" r=\\\"20\\\"></circle></svg>\\n                <svg class=\\\"lg-video-play-icon-circle\\\" viewBox=\\\"0 0 50 50\\\" focusable=\\\"false\\\">\\n                    <circle cx=\\\"50%\\\" cy=\\\"50%\\\" r=\\\"20\\\"></circle>\\n                </svg>\\n            </div>\\n            \" + (dummyImg || '') + \"\\n            <img class=\\\"lg-object lg-video-poster\\\" src=\\\"\" + _poster + \"\\\" />\\n        </div>\";\n  },\n  getFocusableElements: function (container) {\n    var elements = container.querySelectorAll('a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type=\"text\"]:not([disabled]), input[type=\"radio\"]:not([disabled]), input[type=\"checkbox\"]:not([disabled]), select:not([disabled])');\n    var visibleElements = [].filter.call(elements, function (element) {\n      var style = window.getComputedStyle(element);\n      return style.display !== 'none' && style.visibility !== 'hidden';\n    });\n    return visibleElements;\n  },\n  /**\r\n   * @desc Create dynamic elements array from gallery items when dynamic option is false\r\n   * It helps to avoid frequent DOM interaction\r\n   * and avoid multiple checks for dynamic elments\r\n   *\r\n   * @returns {Array} dynamicEl\r\n   */\n  getDynamicOptions: function (items, extraProps, getCaptionFromTitleOrAlt, exThumbImage) {\n    var dynamicElements = [];\n    var availableDynamicOptions = __spreadArrays(defaultDynamicOptions, extraProps);\n    [].forEach.call(items, function (item) {\n      var dynamicEl = {};\n      for (var i = 0; i < item.attributes.length; i++) {\n        var attr = item.attributes[i];\n        if (attr.specified) {\n          var dynamicAttr = convertToData(attr.name);\n          var label = '';\n          if (availableDynamicOptions.indexOf(dynamicAttr) > -1) {\n            label = dynamicAttr;\n          }\n          if (label) {\n            dynamicEl[label] = attr.value;\n          }\n        }\n      }\n      var currentItem = $LG(item);\n      var alt = currentItem.find('img').first().attr('alt');\n      var title = currentItem.attr('title');\n      var thumb = exThumbImage ? currentItem.attr(exThumbImage) : currentItem.find('img').first().attr('src');\n      dynamicEl.thumb = thumb;\n      if (getCaptionFromTitleOrAlt && !dynamicEl.subHtml) {\n        dynamicEl.subHtml = title || alt || '';\n      }\n      dynamicEl.alt = alt || title || '';\n      dynamicElements.push(dynamicEl);\n    });\n    return dynamicElements;\n  },\n  isMobile: function () {\n    return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);\n  },\n  /**\r\n   * @desc Check the given src is video\r\n   * @param {String} src\r\n   * @return {Object} video type\r\n   * Ex:{ youtube  :  [\"//www.youtube.com/watch?v=c0asJgSyxcY\", \"c0asJgSyxcY\"] }\r\n   *\r\n   * @todo - this information can be moved to dynamicEl to avoid frequent calls\r\n   */\n  isVideo: function (src, isHTML5VIdeo, index) {\n    if (!src) {\n      if (isHTML5VIdeo) {\n        return {\n          html5: true\n        };\n      } else {\n        console.error('lightGallery :- data-src is not provided on slide item ' + (index + 1) + '. Please make sure the selector property is properly configured. More info - https://www.lightgalleryjs.com/demos/html-markup/');\n        return;\n      }\n    }\n    var youtube = src.match(/\\/\\/(?:www\\.)?youtu(?:\\.be|be\\.com|be-nocookie\\.com)\\/(?:watch\\?v=|embed\\/)?([a-z0-9\\-\\_\\%]+)([\\&|?][\\S]*)*/i);\n    var vimeo = src.match(/\\/\\/(?:www\\.)?(?:player\\.)?vimeo.com\\/(?:video\\/)?([0-9a-z\\-_]+)(.*)?/i);\n    var wistia = src.match(/https?:\\/\\/(.+)?(wistia\\.com|wi\\.st)\\/(medias|embed)\\/([0-9a-z\\-_]+)(.*)/);\n    if (youtube) {\n      return {\n        youtube: youtube\n      };\n    } else if (vimeo) {\n      return {\n        vimeo: vimeo\n      };\n    } else if (wistia) {\n      return {\n        wistia: wistia\n      };\n    }\n  }\n};\n\n// @ref - https://stackoverflow.com/questions/3971841/how-to-resize-images-proportionally-keeping-the-aspect-ratio\n// @ref - https://2ality.com/2017/04/setting-up-multi-platform-packages.html\n// Unique id for each gallery\nvar lgId = 0;\nvar LightGallery = /** @class */function () {\n  function LightGallery(element, options) {\n    this.lgOpened = false;\n    this.index = 0;\n    // lightGallery modules\n    this.plugins = [];\n    // false when lightGallery load first slide content;\n    this.lGalleryOn = false;\n    // True when a slide animation is in progress\n    this.lgBusy = false;\n    this.currentItemsInDom = [];\n    // Scroll top value before lightGallery is opened\n    this.prevScrollTop = 0;\n    this.bodyPaddingRight = 0;\n    this.isDummyImageRemoved = false;\n    this.dragOrSwipeEnabled = false;\n    this.mediaContainerPosition = {\n      top: 0,\n      bottom: 0\n    };\n    if (!element) {\n      return this;\n    }\n    lgId++;\n    this.lgId = lgId;\n    this.el = element;\n    this.LGel = $LG(element);\n    this.generateSettings(options);\n    this.buildModules();\n    // When using dynamic mode, ensure dynamicEl is an array\n    if (this.settings.dynamic && this.settings.dynamicEl !== undefined && !Array.isArray(this.settings.dynamicEl)) {\n      throw 'When using dynamic mode, you must also define dynamicEl as an Array.';\n    }\n    this.galleryItems = this.getItems();\n    this.normalizeSettings();\n    // Gallery items\n    this.init();\n    this.validateLicense();\n    return this;\n  }\n  LightGallery.prototype.generateSettings = function (options) {\n    // lightGallery settings\n    this.settings = __assign(__assign({}, lightGalleryCoreSettings), options);\n    if (this.settings.isMobile && typeof this.settings.isMobile === 'function' ? this.settings.isMobile() : utils.isMobile()) {\n      var mobileSettings = __assign(__assign({}, this.settings.mobileSettings), this.settings.mobileSettings);\n      this.settings = __assign(__assign({}, this.settings), mobileSettings);\n    }\n  };\n  LightGallery.prototype.normalizeSettings = function () {\n    if (this.settings.slideEndAnimation) {\n      this.settings.hideControlOnEnd = false;\n    }\n    if (!this.settings.closable) {\n      this.settings.swipeToClose = false;\n    }\n    // And reset it on close to get the correct value next time\n    this.zoomFromOrigin = this.settings.zoomFromOrigin;\n    // At the moment, Zoom from image doesn't support dynamic options\n    // @todo add zoomFromOrigin support for dynamic images\n    if (this.settings.dynamic) {\n      this.zoomFromOrigin = false;\n    }\n    if (!this.settings.container) {\n      this.settings.container = document.body;\n    }\n    // settings.preload should not be grater than $item.length\n    this.settings.preload = Math.min(this.settings.preload, this.galleryItems.length);\n  };\n  LightGallery.prototype.init = function () {\n    var _this = this;\n    this.addSlideVideoInfo(this.galleryItems);\n    this.buildStructure();\n    this.LGel.trigger(lGEvents.init, {\n      instance: this\n    });\n    if (this.settings.keyPress) {\n      this.keyPress();\n    }\n    setTimeout(function () {\n      _this.enableDrag();\n      _this.enableSwipe();\n      _this.triggerPosterClick();\n    }, 50);\n    this.arrow();\n    if (this.settings.mousewheel) {\n      this.mousewheel();\n    }\n    if (!this.settings.dynamic) {\n      this.openGalleryOnItemClick();\n    }\n  };\n  LightGallery.prototype.openGalleryOnItemClick = function () {\n    var _this = this;\n    var _loop_1 = function (index) {\n      var element = this_1.items[index];\n      var $element = $LG(element);\n      // Using different namespace for click because click event should not unbind if selector is same object('this')\n      // @todo manage all event listners - should have namespace that represent element\n      var uuid = lgQuery.generateUUID();\n      $element.attr('data-lg-id', uuid).on(\"click.lgcustom-item-\" + uuid, function (e) {\n        e.preventDefault();\n        var currentItemIndex = _this.settings.index || index;\n        _this.openGallery(currentItemIndex, element);\n      });\n    };\n    var this_1 = this;\n    // Using for loop instead of using bubbling as the items can be any html element.\n    for (var index = 0; index < this.items.length; index++) {\n      _loop_1(index);\n    }\n  };\n  /**\r\n   * Module constructor\r\n   * Modules are build incrementally.\r\n   * Gallery should be opened only once all the modules are initialized.\r\n   * use moduleBuildTimeout to make sure this\r\n   */\n  LightGallery.prototype.buildModules = function () {\n    var _this = this;\n    this.settings.plugins.forEach(function (plugin) {\n      _this.plugins.push(new plugin(_this, $LG));\n    });\n  };\n  LightGallery.prototype.validateLicense = function () {\n    if (!this.settings.licenseKey) {\n      console.error('Please provide a valid license key');\n    } else if (this.settings.licenseKey === '0000-0000-000-0000') {\n      console.warn(\"lightGallery: \" + this.settings.licenseKey + \" license key is not valid for production use\");\n    }\n  };\n  LightGallery.prototype.getSlideItem = function (index) {\n    return $LG(this.getSlideItemId(index));\n  };\n  LightGallery.prototype.getSlideItemId = function (index) {\n    return \"#lg-item-\" + this.lgId + \"-\" + index;\n  };\n  LightGallery.prototype.getIdName = function (id) {\n    return id + \"-\" + this.lgId;\n  };\n  LightGallery.prototype.getElementById = function (id) {\n    return $LG(\"#\" + this.getIdName(id));\n  };\n  LightGallery.prototype.manageSingleSlideClassName = function () {\n    if (this.galleryItems.length < 2) {\n      this.outer.addClass('lg-single-item');\n    } else {\n      this.outer.removeClass('lg-single-item');\n    }\n  };\n  LightGallery.prototype.buildStructure = function () {\n    var _this = this;\n    var container = this.$container && this.$container.get();\n    if (container) {\n      return;\n    }\n    var controls = '';\n    var subHtmlCont = '';\n    // Create controls\n    if (this.settings.controls) {\n      controls = \"<button type=\\\"button\\\" id=\\\"\" + this.getIdName('lg-prev') + \"\\\" aria-label=\\\"\" + this.settings.strings['previousSlide'] + \"\\\" class=\\\"lg-prev lg-icon\\\"> \" + this.settings.prevHtml + \" </button>\\n                <button type=\\\"button\\\" id=\\\"\" + this.getIdName('lg-next') + \"\\\" aria-label=\\\"\" + this.settings.strings['nextSlide'] + \"\\\" class=\\\"lg-next lg-icon\\\"> \" + this.settings.nextHtml + \" </button>\";\n    }\n    if (this.settings.appendSubHtmlTo !== '.lg-item') {\n      subHtmlCont = '<div class=\"lg-sub-html\" role=\"status\" aria-live=\"polite\"></div>';\n    }\n    var addClasses = '';\n    if (this.settings.allowMediaOverlap) {\n      // Do not remove space before last single quote\n      addClasses += 'lg-media-overlap ';\n    }\n    var ariaLabelledby = this.settings.ariaLabelledby ? 'aria-labelledby=\"' + this.settings.ariaLabelledby + '\"' : '';\n    var ariaDescribedby = this.settings.ariaDescribedby ? 'aria-describedby=\"' + this.settings.ariaDescribedby + '\"' : '';\n    var containerClassName = \"lg-container \" + this.settings.addClass + \" \" + (document.body !== this.settings.container ? 'lg-inline' : '');\n    var closeIcon = this.settings.closable && this.settings.showCloseIcon ? \"<button type=\\\"button\\\" aria-label=\\\"\" + this.settings.strings['closeGallery'] + \"\\\" id=\\\"\" + this.getIdName('lg-close') + \"\\\" class=\\\"lg-close lg-icon\\\"></button>\" : '';\n    var maximizeIcon = this.settings.showMaximizeIcon ? \"<button type=\\\"button\\\" aria-label=\\\"\" + this.settings.strings['toggleMaximize'] + \"\\\" id=\\\"\" + this.getIdName('lg-maximize') + \"\\\" class=\\\"lg-maximize lg-icon\\\"></button>\" : '';\n    var template = \"\\n        <div class=\\\"\" + containerClassName + \"\\\" id=\\\"\" + this.getIdName('lg-container') + \"\\\" tabindex=\\\"-1\\\" aria-modal=\\\"true\\\" \" + ariaLabelledby + \" \" + ariaDescribedby + \" role=\\\"dialog\\\"\\n        >\\n            <div id=\\\"\" + this.getIdName('lg-backdrop') + \"\\\" class=\\\"lg-backdrop\\\"></div>\\n\\n            <div id=\\\"\" + this.getIdName('lg-outer') + \"\\\" class=\\\"lg-outer lg-use-css3 lg-css3 lg-hide-items \" + addClasses + \" \\\">\\n\\n              <div id=\\\"\" + this.getIdName('lg-content') + \"\\\" class=\\\"lg-content\\\">\\n                <div id=\\\"\" + this.getIdName('lg-inner') + \"\\\" class=\\\"lg-inner\\\">\\n                </div>\\n                \" + controls + \"\\n              </div>\\n                <div id=\\\"\" + this.getIdName('lg-toolbar') + \"\\\" class=\\\"lg-toolbar lg-group\\\">\\n                    \" + maximizeIcon + \"\\n                    \" + closeIcon + \"\\n                    </div>\\n                    \" + (this.settings.appendSubHtmlTo === '.lg-outer' ? subHtmlCont : '') + \"\\n                <div id=\\\"\" + this.getIdName('lg-components') + \"\\\" class=\\\"lg-components\\\">\\n                    \" + (this.settings.appendSubHtmlTo === '.lg-sub-html' ? subHtmlCont : '') + \"\\n                </div>\\n            </div>\\n        </div>\\n        \";\n    $LG(this.settings.container).append(template);\n    if (document.body !== this.settings.container) {\n      $LG(this.settings.container).css('position', 'relative');\n    }\n    this.outer = this.getElementById('lg-outer');\n    this.$lgComponents = this.getElementById('lg-components');\n    this.$backdrop = this.getElementById('lg-backdrop');\n    this.$container = this.getElementById('lg-container');\n    this.$inner = this.getElementById('lg-inner');\n    this.$content = this.getElementById('lg-content');\n    this.$toolbar = this.getElementById('lg-toolbar');\n    this.$backdrop.css('transition-duration', this.settings.backdropDuration + 'ms');\n    var outerClassNames = this.settings.mode + \" \";\n    this.manageSingleSlideClassName();\n    if (this.settings.enableDrag) {\n      outerClassNames += 'lg-grab ';\n    }\n    this.outer.addClass(outerClassNames);\n    this.$inner.css('transition-timing-function', this.settings.easing);\n    this.$inner.css('transition-duration', this.settings.speed + 'ms');\n    if (this.settings.download) {\n      this.$toolbar.append(\"<a id=\\\"\" + this.getIdName('lg-download') + \"\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" aria-label=\\\"\" + this.settings.strings['download'] + \"\\\" download class=\\\"lg-download lg-icon\\\"></a>\");\n    }\n    this.counter();\n    $LG(window).on(\"resize.lg.global\" + this.lgId + \" orientationchange.lg.global\" + this.lgId, function () {\n      _this.refreshOnResize();\n    });\n    this.hideBars();\n    this.manageCloseGallery();\n    this.toggleMaximize();\n    this.initModules();\n  };\n  LightGallery.prototype.refreshOnResize = function () {\n    if (this.lgOpened) {\n      var currentGalleryItem = this.galleryItems[this.index];\n      var __slideVideoInfo = currentGalleryItem.__slideVideoInfo;\n      this.mediaContainerPosition = this.getMediaContainerPosition();\n      var _a = this.mediaContainerPosition,\n        top_1 = _a.top,\n        bottom = _a.bottom;\n      this.currentImageSize = utils.getSize(this.items[this.index], this.outer, top_1 + bottom, __slideVideoInfo && this.settings.videoMaxSize);\n      if (__slideVideoInfo) {\n        this.resizeVideoSlide(this.index, this.currentImageSize);\n      }\n      if (this.zoomFromOrigin && !this.isDummyImageRemoved) {\n        var imgStyle = this.getDummyImgStyles(this.currentImageSize);\n        this.outer.find('.lg-current .lg-dummy-img').first().attr('style', imgStyle);\n      }\n      this.LGel.trigger(lGEvents.containerResize);\n    }\n  };\n  LightGallery.prototype.resizeVideoSlide = function (index, imageSize) {\n    var lgVideoStyle = this.getVideoContStyle(imageSize);\n    var currentSlide = this.getSlideItem(index);\n    currentSlide.find('.lg-video-cont').attr('style', lgVideoStyle);\n  };\n  /**\r\n   * Update slides dynamically.\r\n   * Add, edit or delete slides dynamically when lightGallery is opened.\r\n   * Modify the current gallery items and pass it via updateSlides method\r\n   * @note\r\n   * - Do not mutate existing lightGallery items directly.\r\n   * - Always pass new list of gallery items\r\n   * - You need to take care of thumbnails outside the gallery if any\r\n   * - user this method only if you want to update slides when the gallery is opened. Otherwise, use `refresh()` method.\r\n   * @param items Gallery items\r\n   * @param index After the update operation, which slide gallery should navigate to\r\n   * @category lGPublicMethods\r\n   * @example\r\n   * const plugin = lightGallery();\r\n   *\r\n   * // Adding slides dynamically\r\n   * let galleryItems = [\r\n   * // Access existing lightGallery items\r\n   * // galleryItems are automatically generated internally from the gallery HTML markup\r\n   * // or directly from galleryItems when dynamic gallery is used\r\n   *   ...plugin.galleryItems,\r\n   *     ...[\r\n   *       {\r\n   *         src: 'img/img-1.png',\r\n   *           thumb: 'img/thumb1.png',\r\n   *         },\r\n   *     ],\r\n   *   ];\r\n   *   plugin.updateSlides(\r\n   *     galleryItems,\r\n   *     plugin.index,\r\n   *   );\r\n   *\r\n   *\r\n   * // Remove slides dynamically\r\n   * galleryItems = JSON.parse(\r\n   *   JSON.stringify(updateSlideInstance.galleryItems),\r\n   * );\r\n   * galleryItems.shift();\r\n   * updateSlideInstance.updateSlides(galleryItems, 1);\r\n   * @see <a href=\"/demos/update-slides/\">Demo</a>\r\n   */\n  LightGallery.prototype.updateSlides = function (items, index) {\n    if (this.index > items.length - 1) {\n      this.index = items.length - 1;\n    }\n    if (items.length === 1) {\n      this.index = 0;\n    }\n    if (!items.length) {\n      this.closeGallery();\n      return;\n    }\n    var currentSrc = this.galleryItems[index].src;\n    this.galleryItems = items;\n    this.updateControls();\n    this.$inner.empty();\n    this.currentItemsInDom = [];\n    var _index = 0;\n    // Find the current index based on source value of the slide\n    this.galleryItems.some(function (galleryItem, itemIndex) {\n      if (galleryItem.src === currentSrc) {\n        _index = itemIndex;\n        return true;\n      }\n      return false;\n    });\n    this.currentItemsInDom = this.organizeSlideItems(_index, -1);\n    this.loadContent(_index, true);\n    this.getSlideItem(_index).addClass('lg-current');\n    this.index = _index;\n    this.updateCurrentCounter(_index);\n    this.LGel.trigger(lGEvents.updateSlides);\n  };\n  // Get gallery items based on multiple conditions\n  LightGallery.prototype.getItems = function () {\n    // Gallery items\n    this.items = [];\n    if (!this.settings.dynamic) {\n      if (this.settings.selector === 'this') {\n        this.items.push(this.el);\n      } else if (this.settings.selector) {\n        if (typeof this.settings.selector === 'string') {\n          if (this.settings.selectWithin) {\n            var selectWithin = $LG(this.settings.selectWithin);\n            this.items = selectWithin.find(this.settings.selector).get();\n          } else {\n            this.items = this.el.querySelectorAll(this.settings.selector);\n          }\n        } else {\n          this.items = this.settings.selector;\n        }\n      } else {\n        this.items = this.el.children;\n      }\n      return utils.getDynamicOptions(this.items, this.settings.extraProps, this.settings.getCaptionFromTitleOrAlt, this.settings.exThumbImage);\n    } else {\n      return this.settings.dynamicEl || [];\n    }\n  };\n  LightGallery.prototype.shouldHideScrollbar = function () {\n    return this.settings.hideScrollbar && document.body === this.settings.container;\n  };\n  LightGallery.prototype.hideScrollbar = function () {\n    if (!this.shouldHideScrollbar()) {\n      return;\n    }\n    this.bodyPaddingRight = parseFloat($LG('body').style().paddingRight);\n    var bodyRect = document.documentElement.getBoundingClientRect();\n    var scrollbarWidth = window.innerWidth - bodyRect.width;\n    $LG(document.body).css('padding-right', scrollbarWidth + this.bodyPaddingRight + 'px');\n    $LG(document.body).addClass('lg-overlay-open');\n  };\n  LightGallery.prototype.resetScrollBar = function () {\n    if (!this.shouldHideScrollbar()) {\n      return;\n    }\n    $LG(document.body).css('padding-right', this.bodyPaddingRight + 'px');\n    $LG(document.body).removeClass('lg-overlay-open');\n  };\n  /**\r\n   * Open lightGallery.\r\n   * Open gallery with specific slide by passing index of the slide as parameter.\r\n   * @category lGPublicMethods\r\n   * @param {Number} index  - index of the slide\r\n   * @param {HTMLElement} element - Which image lightGallery should zoom from\r\n   *\r\n   * @example\r\n   * const $dynamicGallery = document.getElementById('dynamic-gallery-demo');\r\n   * const dynamicGallery = lightGallery($dynamicGallery, {\r\n   *     dynamic: true,\r\n   *     dynamicEl: [\r\n   *         {\r\n   *              src: 'img/1.jpg',\r\n   *              thumb: 'img/thumb-1.jpg',\r\n   *              subHtml: '<h4>Image 1 title</h4><p>Image 1 descriptions.</p>',\r\n   *         },\r\n   *         ...\r\n   *     ],\r\n   * });\r\n   * $dynamicGallery.addEventListener('click', function () {\r\n   *     // Starts with third item.(Optional).\r\n   *     // This is useful if you want use dynamic mode with\r\n   *     // custom thumbnails (thumbnails outside gallery),\r\n   *     dynamicGallery.openGallery(2);\r\n   * });\r\n   *\r\n   */\n  LightGallery.prototype.openGallery = function (index, element) {\n    var _this = this;\n    if (index === void 0) {\n      index = this.settings.index;\n    }\n    // prevent accidental double execution\n    if (this.lgOpened) return;\n    this.lgOpened = true;\n    this.outer.removeClass('lg-hide-items');\n    this.hideScrollbar();\n    // Add display block, but still has opacity 0\n    this.$container.addClass('lg-show');\n    var itemsToBeInsertedToDom = this.getItemsToBeInsertedToDom(index, index);\n    this.currentItemsInDom = itemsToBeInsertedToDom;\n    var items = '';\n    itemsToBeInsertedToDom.forEach(function (item) {\n      items = items + (\"<div id=\\\"\" + item + \"\\\" class=\\\"lg-item\\\"></div>\");\n    });\n    this.$inner.append(items);\n    this.addHtml(index);\n    var transform = '';\n    this.mediaContainerPosition = this.getMediaContainerPosition();\n    var _a = this.mediaContainerPosition,\n      top = _a.top,\n      bottom = _a.bottom;\n    if (!this.settings.allowMediaOverlap) {\n      this.setMediaContainerPosition(top, bottom);\n    }\n    var __slideVideoInfo = this.galleryItems[index].__slideVideoInfo;\n    if (this.zoomFromOrigin && element) {\n      this.currentImageSize = utils.getSize(element, this.outer, top + bottom, __slideVideoInfo && this.settings.videoMaxSize);\n      transform = utils.getTransform(element, this.outer, top, bottom, this.currentImageSize);\n    }\n    if (!this.zoomFromOrigin || !transform) {\n      this.outer.addClass(this.settings.startClass);\n      this.getSlideItem(index).removeClass('lg-complete');\n    }\n    var timeout = this.settings.zoomFromOrigin ? 100 : this.settings.backdropDuration;\n    setTimeout(function () {\n      _this.outer.addClass('lg-components-open');\n    }, timeout);\n    this.index = index;\n    this.LGel.trigger(lGEvents.beforeOpen);\n    // add class lg-current to remove initial transition\n    this.getSlideItem(index).addClass('lg-current');\n    this.lGalleryOn = false;\n    // Store the current scroll top value to scroll back after closing the gallery..\n    this.prevScrollTop = $LG(window).scrollTop();\n    setTimeout(function () {\n      // Need to check both zoomFromOrigin and transform values as we need to set set the\n      // default opening animation if user missed to add the lg-size attribute\n      if (_this.zoomFromOrigin && transform) {\n        var currentSlide_1 = _this.getSlideItem(index);\n        currentSlide_1.css('transform', transform);\n        setTimeout(function () {\n          currentSlide_1.addClass('lg-start-progress lg-start-end-progress').css('transition-duration', _this.settings.startAnimationDuration + 'ms');\n          _this.outer.addClass('lg-zoom-from-image');\n        });\n        setTimeout(function () {\n          currentSlide_1.css('transform', 'translate3d(0, 0, 0)');\n        }, 100);\n      }\n      setTimeout(function () {\n        _this.$backdrop.addClass('in');\n        _this.$container.addClass('lg-show-in');\n      }, 10);\n      setTimeout(function () {\n        if (_this.settings.trapFocus && document.body === _this.settings.container) {\n          _this.trapFocus();\n        }\n      }, _this.settings.backdropDuration + 50);\n      // lg-visible class resets gallery opacity to 1\n      if (!_this.zoomFromOrigin || !transform) {\n        setTimeout(function () {\n          _this.outer.addClass('lg-visible');\n        }, _this.settings.backdropDuration);\n      }\n      // initiate slide function\n      _this.slide(index, false, false, false);\n      _this.LGel.trigger(lGEvents.afterOpen);\n    });\n    if (document.body === this.settings.container) {\n      $LG('html').addClass('lg-on');\n    }\n  };\n  /**\r\n   * Note - Changing the position of the media on every slide transition creates a flickering effect.\r\n   * Therefore, The height of the caption is calculated dynamically, only once based on the first slide caption.\r\n   * if you have dynamic captions for each media,\r\n   * you can provide an appropriate height for the captions via allowMediaOverlap option\r\n   */\n  LightGallery.prototype.getMediaContainerPosition = function () {\n    if (this.settings.allowMediaOverlap) {\n      return {\n        top: 0,\n        bottom: 0\n      };\n    }\n    var top = this.$toolbar.get().clientHeight || 0;\n    var subHtml = this.outer.find('.lg-components .lg-sub-html').get();\n    var captionHeight = this.settings.defaultCaptionHeight || subHtml && subHtml.clientHeight || 0;\n    var thumbContainer = this.outer.find('.lg-thumb-outer').get();\n    var thumbHeight = thumbContainer ? thumbContainer.clientHeight : 0;\n    var bottom = thumbHeight + captionHeight;\n    return {\n      top: top,\n      bottom: bottom\n    };\n  };\n  LightGallery.prototype.setMediaContainerPosition = function (top, bottom) {\n    if (top === void 0) {\n      top = 0;\n    }\n    if (bottom === void 0) {\n      bottom = 0;\n    }\n    this.$content.css('top', top + 'px').css('bottom', bottom + 'px');\n  };\n  LightGallery.prototype.hideBars = function () {\n    var _this = this;\n    // Hide controllers if mouse doesn't move for some period\n    setTimeout(function () {\n      _this.outer.removeClass('lg-hide-items');\n      if (_this.settings.hideBarsDelay > 0) {\n        _this.outer.on('mousemove.lg click.lg touchstart.lg', function () {\n          _this.outer.removeClass('lg-hide-items');\n          clearTimeout(_this.hideBarTimeout);\n          // Timeout will be cleared on each slide movement also\n          _this.hideBarTimeout = setTimeout(function () {\n            _this.outer.addClass('lg-hide-items');\n          }, _this.settings.hideBarsDelay);\n        });\n        _this.outer.trigger('mousemove.lg');\n      }\n    }, this.settings.showBarsAfter);\n  };\n  LightGallery.prototype.initPictureFill = function ($img) {\n    if (this.settings.supportLegacyBrowser) {\n      try {\n        picturefill({\n          elements: [$img.get()]\n        });\n      } catch (e) {\n        console.warn('lightGallery :- If you want srcset or picture tag to be supported for older browser please include picturefil javascript library in your document.');\n      }\n    }\n  };\n  /**\r\n   *  @desc Create image counter\r\n   *  Ex: 1/10\r\n   */\n  LightGallery.prototype.counter = function () {\n    if (this.settings.counter) {\n      var counterHtml = \"<div class=\\\"lg-counter\\\" role=\\\"status\\\" aria-live=\\\"polite\\\">\\n                <span id=\\\"\" + this.getIdName('lg-counter-current') + \"\\\" class=\\\"lg-counter-current\\\">\" + (this.index + 1) + \" </span> /\\n                <span id=\\\"\" + this.getIdName('lg-counter-all') + \"\\\" class=\\\"lg-counter-all\\\">\" + this.galleryItems.length + \" </span></div>\";\n      this.outer.find(this.settings.appendCounterTo).append(counterHtml);\n    }\n  };\n  /**\r\n   *  @desc add sub-html into the slide\r\n   *  @param {Number} index - index of the slide\r\n   */\n  LightGallery.prototype.addHtml = function (index) {\n    var subHtml;\n    var subHtmlUrl;\n    if (this.galleryItems[index].subHtmlUrl) {\n      subHtmlUrl = this.galleryItems[index].subHtmlUrl;\n    } else {\n      subHtml = this.galleryItems[index].subHtml;\n    }\n    if (!subHtmlUrl) {\n      if (subHtml) {\n        // get first letter of sub-html\n        // if first letter starts with . or # get the html form the jQuery object\n        var fL = subHtml.substring(0, 1);\n        if (fL === '.' || fL === '#') {\n          if (this.settings.subHtmlSelectorRelative && !this.settings.dynamic) {\n            subHtml = $LG(this.items).eq(index).find(subHtml).first().html();\n          } else {\n            subHtml = $LG(subHtml).first().html();\n          }\n        }\n      } else {\n        subHtml = '';\n      }\n    }\n    if (this.settings.appendSubHtmlTo !== '.lg-item') {\n      if (subHtmlUrl) {\n        this.outer.find('.lg-sub-html').load(subHtmlUrl);\n      } else {\n        this.outer.find('.lg-sub-html').html(subHtml);\n      }\n    } else {\n      var currentSlide = $LG(this.getSlideItemId(index));\n      if (subHtmlUrl) {\n        currentSlide.load(subHtmlUrl);\n      } else {\n        currentSlide.append(\"<div class=\\\"lg-sub-html\\\">\" + subHtml + \"</div>\");\n      }\n    }\n    // Add lg-empty-html class if title doesn't exist\n    if (typeof subHtml !== 'undefined' && subHtml !== null) {\n      if (subHtml === '') {\n        this.outer.find(this.settings.appendSubHtmlTo).addClass('lg-empty-html');\n      } else {\n        this.outer.find(this.settings.appendSubHtmlTo).removeClass('lg-empty-html');\n      }\n    }\n    this.LGel.trigger(lGEvents.afterAppendSubHtml, {\n      index: index\n    });\n  };\n  /**\r\n   *  @desc Preload slides\r\n   *  @param {Number} index - index of the slide\r\n   * @todo preload not working for the first slide, Also, should work for the first and last slide as well\r\n   */\n  LightGallery.prototype.preload = function (index) {\n    for (var i = 1; i <= this.settings.preload; i++) {\n      if (i >= this.galleryItems.length - index) {\n        break;\n      }\n      this.loadContent(index + i, false);\n    }\n    for (var j = 1; j <= this.settings.preload; j++) {\n      if (index - j < 0) {\n        break;\n      }\n      this.loadContent(index - j, false);\n    }\n  };\n  LightGallery.prototype.getDummyImgStyles = function (imageSize) {\n    if (!imageSize) return '';\n    return \"width:\" + imageSize.width + \"px;\\n                margin-left: -\" + imageSize.width / 2 + \"px;\\n                margin-top: -\" + imageSize.height / 2 + \"px;\\n                height:\" + imageSize.height + \"px\";\n  };\n  LightGallery.prototype.getVideoContStyle = function (imageSize) {\n    if (!imageSize) return '';\n    return \"width:\" + imageSize.width + \"px;\\n                height:\" + imageSize.height + \"px\";\n  };\n  LightGallery.prototype.getDummyImageContent = function ($currentSlide, index, alt) {\n    var $currentItem;\n    if (!this.settings.dynamic) {\n      $currentItem = $LG(this.items).eq(index);\n    }\n    if ($currentItem) {\n      var _dummyImgSrc = void 0;\n      if (!this.settings.exThumbImage) {\n        _dummyImgSrc = $currentItem.find('img').first().attr('src');\n      } else {\n        _dummyImgSrc = $currentItem.attr(this.settings.exThumbImage);\n      }\n      if (!_dummyImgSrc) return '';\n      var imgStyle = this.getDummyImgStyles(this.currentImageSize);\n      var dummyImgContent = \"<img \" + alt + \" style=\\\"\" + imgStyle + \"\\\" class=\\\"lg-dummy-img\\\" src=\\\"\" + _dummyImgSrc + \"\\\" />\";\n      $currentSlide.addClass('lg-first-slide');\n      this.outer.addClass('lg-first-slide-loading');\n      return dummyImgContent;\n    }\n    return '';\n  };\n  LightGallery.prototype.setImgMarkup = function (src, $currentSlide, index) {\n    var currentGalleryItem = this.galleryItems[index];\n    var alt = currentGalleryItem.alt,\n      srcset = currentGalleryItem.srcset,\n      sizes = currentGalleryItem.sizes,\n      sources = currentGalleryItem.sources;\n    // Use the thumbnail as dummy image which will be resized to actual image size and\n    // displayed on top of actual image\n    var imgContent = '';\n    var altAttr = alt ? 'alt=\"' + alt + '\"' : '';\n    if (this.isFirstSlideWithZoomAnimation()) {\n      imgContent = this.getDummyImageContent($currentSlide, index, altAttr);\n    } else {\n      imgContent = utils.getImgMarkup(index, src, altAttr, srcset, sizes, sources);\n    }\n    var imgMarkup = \"<picture class=\\\"lg-img-wrap\\\"> \" + imgContent + \"</picture>\";\n    $currentSlide.prepend(imgMarkup);\n  };\n  LightGallery.prototype.onSlideObjectLoad = function ($slide, isHTML5VideoWithoutPoster, onLoad, onError) {\n    var mediaObject = $slide.find('.lg-object').first();\n    if (utils.isImageLoaded(mediaObject.get()) || isHTML5VideoWithoutPoster) {\n      onLoad();\n    } else {\n      mediaObject.on('load.lg error.lg', function () {\n        onLoad && onLoad();\n      });\n      mediaObject.on('error.lg', function () {\n        onError && onError();\n      });\n    }\n  };\n  /**\r\n   *\r\n   * @param $el Current slide item\r\n   * @param index\r\n   * @param delay Delay is 0 except first time\r\n   * @param speed Speed is same as delay, except it is 0 if gallery is opened via hash plugin\r\n   * @param isFirstSlide\r\n   */\n  LightGallery.prototype.onLgObjectLoad = function (currentSlide, index, delay, speed, isFirstSlide, isHTML5VideoWithoutPoster) {\n    var _this = this;\n    this.onSlideObjectLoad(currentSlide, isHTML5VideoWithoutPoster, function () {\n      _this.triggerSlideItemLoad(currentSlide, index, delay, speed, isFirstSlide);\n    }, function () {\n      currentSlide.addClass('lg-complete lg-complete_');\n      currentSlide.html('<span class=\"lg-error-msg\">' + _this.settings.strings['mediaLoadingFailed'] + '</span>');\n    });\n  };\n  LightGallery.prototype.triggerSlideItemLoad = function ($currentSlide, index, delay, speed, isFirstSlide) {\n    var _this = this;\n    var currentGalleryItem = this.galleryItems[index];\n    // Adding delay for video slides without poster for better performance and user experience\n    // Videos should start playing once once the gallery is completely loaded\n    var _speed = isFirstSlide && this.getSlideType(currentGalleryItem) === 'video' && !currentGalleryItem.poster ? speed : 0;\n    setTimeout(function () {\n      $currentSlide.addClass('lg-complete lg-complete_');\n      _this.LGel.trigger(lGEvents.slideItemLoad, {\n        index: index,\n        delay: delay || 0,\n        isFirstSlide: isFirstSlide\n      });\n    }, _speed);\n  };\n  LightGallery.prototype.isFirstSlideWithZoomAnimation = function () {\n    return !!(!this.lGalleryOn && this.zoomFromOrigin && this.currentImageSize);\n  };\n  // Add video slideInfo\n  LightGallery.prototype.addSlideVideoInfo = function (items) {\n    var _this = this;\n    items.forEach(function (element, index) {\n      element.__slideVideoInfo = utils.isVideo(element.src, !!element.video, index);\n      if (element.__slideVideoInfo && _this.settings.loadYouTubePoster && !element.poster && element.__slideVideoInfo.youtube) {\n        element.poster = \"//img.youtube.com/vi/\" + element.__slideVideoInfo.youtube[1] + \"/maxresdefault.jpg\";\n      }\n    });\n  };\n  /**\r\n   *  Load slide content into slide.\r\n   *  This is used to load content into slides that is not visible too\r\n   *  @param {Number} index - index of the slide.\r\n   *  @param {Boolean} rec - if true call loadcontent() function again.\r\n   */\n  LightGallery.prototype.loadContent = function (index, rec) {\n    var _this = this;\n    var currentGalleryItem = this.galleryItems[index];\n    var $currentSlide = $LG(this.getSlideItemId(index));\n    var poster = currentGalleryItem.poster,\n      srcset = currentGalleryItem.srcset,\n      sizes = currentGalleryItem.sizes,\n      sources = currentGalleryItem.sources;\n    var src = currentGalleryItem.src;\n    var video = currentGalleryItem.video;\n    var _html5Video = video && typeof video === 'string' ? JSON.parse(video) : video;\n    if (currentGalleryItem.responsive) {\n      var srcDyItms = currentGalleryItem.responsive.split(',');\n      src = utils.getResponsiveSrc(srcDyItms) || src;\n    }\n    var videoInfo = currentGalleryItem.__slideVideoInfo;\n    var lgVideoStyle = '';\n    var iframe = !!currentGalleryItem.iframe;\n    var isFirstSlide = !this.lGalleryOn;\n    // delay for adding complete class. it is 0 except first time.\n    var delay = 0;\n    if (isFirstSlide) {\n      if (this.zoomFromOrigin && this.currentImageSize) {\n        delay = this.settings.startAnimationDuration + 10;\n      } else {\n        delay = this.settings.backdropDuration + 10;\n      }\n    }\n    if (!$currentSlide.hasClass('lg-loaded')) {\n      if (videoInfo) {\n        var _a = this.mediaContainerPosition,\n          top_2 = _a.top,\n          bottom = _a.bottom;\n        var videoSize = utils.getSize(this.items[index], this.outer, top_2 + bottom, videoInfo && this.settings.videoMaxSize);\n        lgVideoStyle = this.getVideoContStyle(videoSize);\n      }\n      if (iframe) {\n        var markup = utils.getIframeMarkup(this.settings.iframeWidth, this.settings.iframeHeight, this.settings.iframeMaxWidth, this.settings.iframeMaxHeight, src, currentGalleryItem.iframeTitle);\n        $currentSlide.prepend(markup);\n      } else if (poster) {\n        var dummyImg = '';\n        var hasStartAnimation = isFirstSlide && this.zoomFromOrigin && this.currentImageSize;\n        if (hasStartAnimation) {\n          dummyImg = this.getDummyImageContent($currentSlide, index, '');\n        }\n        var markup = utils.getVideoPosterMarkup(poster, dummyImg || '', lgVideoStyle, this.settings.strings['playVideo'], videoInfo);\n        $currentSlide.prepend(markup);\n      } else if (videoInfo) {\n        var markup = \"<div class=\\\"lg-video-cont \\\" style=\\\"\" + lgVideoStyle + \"\\\"></div>\";\n        $currentSlide.prepend(markup);\n      } else {\n        this.setImgMarkup(src, $currentSlide, index);\n        if (srcset || sources) {\n          var $img = $currentSlide.find('.lg-object');\n          this.initPictureFill($img);\n        }\n      }\n      if (poster || videoInfo) {\n        this.LGel.trigger(lGEvents.hasVideo, {\n          index: index,\n          src: src,\n          html5Video: _html5Video,\n          hasPoster: !!poster\n        });\n      }\n      this.LGel.trigger(lGEvents.afterAppendSlide, {\n        index: index\n      });\n      if (this.lGalleryOn && this.settings.appendSubHtmlTo === '.lg-item') {\n        this.addHtml(index);\n      }\n    }\n    // For first time add some delay for displaying the start animation.\n    var _speed = 0;\n    // Do not change the delay value because it is required for zoom plugin.\n    // If gallery opened from direct url (hash) speed value should be 0\n    if (delay && !$LG(document.body).hasClass('lg-from-hash')) {\n      _speed = delay;\n    }\n    // Only for first slide and zoomFromOrigin is enabled\n    if (this.isFirstSlideWithZoomAnimation()) {\n      setTimeout(function () {\n        $currentSlide.removeClass('lg-start-end-progress lg-start-progress').removeAttr('style');\n      }, this.settings.startAnimationDuration + 100);\n      if (!$currentSlide.hasClass('lg-loaded')) {\n        setTimeout(function () {\n          if (_this.getSlideType(currentGalleryItem) === 'image') {\n            var alt = currentGalleryItem.alt;\n            var altAttr = alt ? 'alt=\"' + alt + '\"' : '';\n            $currentSlide.find('.lg-img-wrap').append(utils.getImgMarkup(index, src, altAttr, srcset, sizes, currentGalleryItem.sources));\n            if (srcset || sources) {\n              var $img = $currentSlide.find('.lg-object');\n              _this.initPictureFill($img);\n            }\n          }\n          if (_this.getSlideType(currentGalleryItem) === 'image' || _this.getSlideType(currentGalleryItem) === 'video' && poster) {\n            _this.onLgObjectLoad($currentSlide, index, delay, _speed, true, false);\n            // load remaining slides once the slide is completely loaded\n            _this.onSlideObjectLoad($currentSlide, !!(videoInfo && videoInfo.html5 && !poster), function () {\n              _this.loadContentOnFirstSlideLoad(index, $currentSlide, _speed);\n            }, function () {\n              _this.loadContentOnFirstSlideLoad(index, $currentSlide, _speed);\n            });\n          }\n        }, this.settings.startAnimationDuration + 100);\n      }\n    }\n    // SLide content has been added to dom\n    $currentSlide.addClass('lg-loaded');\n    if (!this.isFirstSlideWithZoomAnimation() || this.getSlideType(currentGalleryItem) === 'video' && !poster) {\n      this.onLgObjectLoad($currentSlide, index, delay, _speed, isFirstSlide, !!(videoInfo && videoInfo.html5 && !poster));\n    }\n    // When gallery is opened once content is loaded (second time) need to add lg-complete class for css styling\n    if ((!this.zoomFromOrigin || !this.currentImageSize) && $currentSlide.hasClass('lg-complete_') && !this.lGalleryOn) {\n      setTimeout(function () {\n        $currentSlide.addClass('lg-complete');\n      }, this.settings.backdropDuration);\n    }\n    // Content loaded\n    // Need to set lGalleryOn before calling preload function\n    this.lGalleryOn = true;\n    if (rec === true) {\n      if (!$currentSlide.hasClass('lg-complete_')) {\n        $currentSlide.find('.lg-object').first().on('load.lg error.lg', function () {\n          _this.preload(index);\n        });\n      } else {\n        this.preload(index);\n      }\n    }\n  };\n  /**\r\n   * @desc Remove dummy image content and load next slides\r\n   * Called only for the first time if zoomFromOrigin animation is enabled\r\n   * @param index\r\n   * @param $currentSlide\r\n   * @param speed\r\n   */\n  LightGallery.prototype.loadContentOnFirstSlideLoad = function (index, $currentSlide, speed) {\n    var _this = this;\n    setTimeout(function () {\n      $currentSlide.find('.lg-dummy-img').remove();\n      $currentSlide.removeClass('lg-first-slide');\n      _this.outer.removeClass('lg-first-slide-loading');\n      _this.isDummyImageRemoved = true;\n      _this.preload(index);\n    }, speed + 300);\n  };\n  LightGallery.prototype.getItemsToBeInsertedToDom = function (index, prevIndex, numberOfItems) {\n    var _this = this;\n    if (numberOfItems === void 0) {\n      numberOfItems = 0;\n    }\n    var itemsToBeInsertedToDom = [];\n    // Minimum 2 items should be there\n    var possibleNumberOfItems = Math.max(numberOfItems, 3);\n    possibleNumberOfItems = Math.min(possibleNumberOfItems, this.galleryItems.length);\n    var prevIndexItem = \"lg-item-\" + this.lgId + \"-\" + prevIndex;\n    if (this.galleryItems.length <= 3) {\n      this.galleryItems.forEach(function (_element, index) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + _this.lgId + \"-\" + index);\n      });\n      return itemsToBeInsertedToDom;\n    }\n    if (index < (this.galleryItems.length - 1) / 2) {\n      for (var idx = index; idx > index - possibleNumberOfItems / 2 && idx >= 0; idx--) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + idx);\n      }\n      var numberOfExistingItems = itemsToBeInsertedToDom.length;\n      for (var idx = 0; idx < possibleNumberOfItems - numberOfExistingItems; idx++) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + (index + idx + 1));\n      }\n    } else {\n      for (var idx = index; idx <= this.galleryItems.length - 1 && idx < index + possibleNumberOfItems / 2; idx++) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + idx);\n      }\n      var numberOfExistingItems = itemsToBeInsertedToDom.length;\n      for (var idx = 0; idx < possibleNumberOfItems - numberOfExistingItems; idx++) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + (index - idx - 1));\n      }\n    }\n    if (this.settings.loop) {\n      if (index === this.galleryItems.length - 1) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + 0);\n      } else if (index === 0) {\n        itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + (this.galleryItems.length - 1));\n      }\n    }\n    if (itemsToBeInsertedToDom.indexOf(prevIndexItem) === -1) {\n      itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + prevIndex);\n    }\n    return itemsToBeInsertedToDom;\n  };\n  LightGallery.prototype.organizeSlideItems = function (index, prevIndex) {\n    var _this = this;\n    var itemsToBeInsertedToDom = this.getItemsToBeInsertedToDom(index, prevIndex, this.settings.numberOfSlideItemsInDom);\n    itemsToBeInsertedToDom.forEach(function (item) {\n      if (_this.currentItemsInDom.indexOf(item) === -1) {\n        _this.$inner.append(\"<div id=\\\"\" + item + \"\\\" class=\\\"lg-item\\\"></div>\");\n      }\n    });\n    this.currentItemsInDom.forEach(function (item) {\n      if (itemsToBeInsertedToDom.indexOf(item) === -1) {\n        $LG(\"#\" + item).remove();\n      }\n    });\n    return itemsToBeInsertedToDom;\n  };\n  /**\r\n   * Get previous index of the slide\r\n   */\n  LightGallery.prototype.getPreviousSlideIndex = function () {\n    var prevIndex = 0;\n    try {\n      var currentItemId = this.outer.find('.lg-current').first().attr('id');\n      prevIndex = parseInt(currentItemId.split('-')[3]) || 0;\n    } catch (error) {\n      prevIndex = 0;\n    }\n    return prevIndex;\n  };\n  LightGallery.prototype.setDownloadValue = function (index) {\n    if (this.settings.download) {\n      var currentGalleryItem = this.galleryItems[index];\n      var hideDownloadBtn = currentGalleryItem.downloadUrl === false || currentGalleryItem.downloadUrl === 'false';\n      if (hideDownloadBtn) {\n        this.outer.addClass('lg-hide-download');\n      } else {\n        var $download = this.getElementById('lg-download');\n        this.outer.removeClass('lg-hide-download');\n        $download.attr('href', currentGalleryItem.downloadUrl || currentGalleryItem.src);\n        if (currentGalleryItem.download) {\n          $download.attr('download', currentGalleryItem.download);\n        }\n      }\n    }\n  };\n  LightGallery.prototype.makeSlideAnimation = function (direction, currentSlideItem, previousSlideItem) {\n    var _this = this;\n    if (this.lGalleryOn) {\n      previousSlideItem.addClass('lg-slide-progress');\n    }\n    setTimeout(function () {\n      // remove all transitions\n      _this.outer.addClass('lg-no-trans');\n      _this.outer.find('.lg-item').removeClass('lg-prev-slide lg-next-slide');\n      if (direction === 'prev') {\n        //prevslide\n        currentSlideItem.addClass('lg-prev-slide');\n        previousSlideItem.addClass('lg-next-slide');\n      } else {\n        // next slide\n        currentSlideItem.addClass('lg-next-slide');\n        previousSlideItem.addClass('lg-prev-slide');\n      }\n      // give 50 ms for browser to add/remove class\n      setTimeout(function () {\n        _this.outer.find('.lg-item').removeClass('lg-current');\n        currentSlideItem.addClass('lg-current');\n        // reset all transitions\n        _this.outer.removeClass('lg-no-trans');\n      }, 50);\n    }, this.lGalleryOn ? this.settings.slideDelay : 0);\n  };\n  /**\r\n   * Goto a specific slide.\r\n   * @param {Number} index - index of the slide\r\n   * @param {Boolean} fromTouch - true if slide function called via touch event or mouse drag\r\n   * @param {Boolean} fromThumb - true if slide function called via thumbnail click\r\n   * @param {String} direction - Direction of the slide(next/prev)\r\n   * @category lGPublicMethods\r\n   * @example\r\n   *  const plugin = lightGallery();\r\n   *  // to go to 3rd slide\r\n   *  plugin.slide(2);\r\n   *\r\n   */\n  LightGallery.prototype.slide = function (index, fromTouch, fromThumb, direction) {\n    var _this = this;\n    var prevIndex = this.getPreviousSlideIndex();\n    this.currentItemsInDom = this.organizeSlideItems(index, prevIndex);\n    // Prevent multiple call, Required for hsh plugin\n    if (this.lGalleryOn && prevIndex === index) {\n      return;\n    }\n    var numberOfGalleryItems = this.galleryItems.length;\n    if (!this.lgBusy) {\n      if (this.settings.counter) {\n        this.updateCurrentCounter(index);\n      }\n      var currentSlideItem = this.getSlideItem(index);\n      var previousSlideItem_1 = this.getSlideItem(prevIndex);\n      var currentGalleryItem = this.galleryItems[index];\n      var videoInfo = currentGalleryItem.__slideVideoInfo;\n      this.outer.attr('data-lg-slide-type', this.getSlideType(currentGalleryItem));\n      this.setDownloadValue(index);\n      if (videoInfo) {\n        var _a = this.mediaContainerPosition,\n          top_3 = _a.top,\n          bottom = _a.bottom;\n        var videoSize = utils.getSize(this.items[index], this.outer, top_3 + bottom, videoInfo && this.settings.videoMaxSize);\n        this.resizeVideoSlide(index, videoSize);\n      }\n      this.LGel.trigger(lGEvents.beforeSlide, {\n        prevIndex: prevIndex,\n        index: index,\n        fromTouch: !!fromTouch,\n        fromThumb: !!fromThumb\n      });\n      this.lgBusy = true;\n      clearTimeout(this.hideBarTimeout);\n      this.arrowDisable(index);\n      if (!direction) {\n        if (index < prevIndex) {\n          direction = 'prev';\n        } else if (index > prevIndex) {\n          direction = 'next';\n        }\n      }\n      if (!fromTouch) {\n        this.makeSlideAnimation(direction, currentSlideItem, previousSlideItem_1);\n      } else {\n        this.outer.find('.lg-item').removeClass('lg-prev-slide lg-current lg-next-slide');\n        var touchPrev = void 0;\n        var touchNext = void 0;\n        if (numberOfGalleryItems > 2) {\n          touchPrev = index - 1;\n          touchNext = index + 1;\n          if (index === 0 && prevIndex === numberOfGalleryItems - 1) {\n            // next slide\n            touchNext = 0;\n            touchPrev = numberOfGalleryItems - 1;\n          } else if (index === numberOfGalleryItems - 1 && prevIndex === 0) {\n            // prev slide\n            touchNext = 0;\n            touchPrev = numberOfGalleryItems - 1;\n          }\n        } else {\n          touchPrev = 0;\n          touchNext = 1;\n        }\n        if (direction === 'prev') {\n          this.getSlideItem(touchNext).addClass('lg-next-slide');\n        } else {\n          this.getSlideItem(touchPrev).addClass('lg-prev-slide');\n        }\n        currentSlideItem.addClass('lg-current');\n      }\n      // Do not put load content in set timeout as it needs to load immediately when the gallery is opened\n      if (!this.lGalleryOn) {\n        this.loadContent(index, true);\n      } else {\n        setTimeout(function () {\n          _this.loadContent(index, true);\n          // Add title if this.settings.appendSubHtmlTo === lg-sub-html\n          if (_this.settings.appendSubHtmlTo !== '.lg-item') {\n            _this.addHtml(index);\n          }\n        }, this.settings.speed + 50 + (fromTouch ? 0 : this.settings.slideDelay));\n      }\n      setTimeout(function () {\n        _this.lgBusy = false;\n        previousSlideItem_1.removeClass('lg-slide-progress');\n        _this.LGel.trigger(lGEvents.afterSlide, {\n          prevIndex: prevIndex,\n          index: index,\n          fromTouch: fromTouch,\n          fromThumb: fromThumb\n        });\n      }, (this.lGalleryOn ? this.settings.speed + 100 : 100) + (fromTouch ? 0 : this.settings.slideDelay));\n    }\n    this.index = index;\n  };\n  LightGallery.prototype.updateCurrentCounter = function (index) {\n    this.getElementById('lg-counter-current').html(index + 1 + '');\n  };\n  LightGallery.prototype.updateCounterTotal = function () {\n    this.getElementById('lg-counter-all').html(this.galleryItems.length + '');\n  };\n  LightGallery.prototype.getSlideType = function (item) {\n    if (item.__slideVideoInfo) {\n      return 'video';\n    } else if (item.iframe) {\n      return 'iframe';\n    } else {\n      return 'image';\n    }\n  };\n  LightGallery.prototype.touchMove = function (startCoords, endCoords, e) {\n    var distanceX = endCoords.pageX - startCoords.pageX;\n    var distanceY = endCoords.pageY - startCoords.pageY;\n    var allowSwipe = false;\n    if (this.swipeDirection) {\n      allowSwipe = true;\n    } else {\n      if (Math.abs(distanceX) > 15) {\n        this.swipeDirection = 'horizontal';\n        allowSwipe = true;\n      } else if (Math.abs(distanceY) > 15) {\n        this.swipeDirection = 'vertical';\n        allowSwipe = true;\n      }\n    }\n    if (!allowSwipe) {\n      return;\n    }\n    var $currentSlide = this.getSlideItem(this.index);\n    if (this.swipeDirection === 'horizontal') {\n      e === null || e === void 0 ? void 0 : e.preventDefault();\n      // reset opacity and transition duration\n      this.outer.addClass('lg-dragging');\n      // move current slide\n      this.setTranslate($currentSlide, distanceX, 0);\n      // move next and prev slide with current slide\n      var width = $currentSlide.get().offsetWidth;\n      var slideWidthAmount = width * 15 / 100;\n      var gutter = slideWidthAmount - Math.abs(distanceX * 10 / 100);\n      this.setTranslate(this.outer.find('.lg-prev-slide').first(), -width + distanceX - gutter, 0);\n      this.setTranslate(this.outer.find('.lg-next-slide').first(), width + distanceX + gutter, 0);\n    } else if (this.swipeDirection === 'vertical') {\n      if (this.settings.swipeToClose) {\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        this.$container.addClass('lg-dragging-vertical');\n        var opacity = 1 - Math.abs(distanceY) / window.innerHeight;\n        this.$backdrop.css('opacity', opacity);\n        var scale = 1 - Math.abs(distanceY) / (window.innerWidth * 2);\n        this.setTranslate($currentSlide, 0, distanceY, scale, scale);\n        if (Math.abs(distanceY) > 100) {\n          this.outer.addClass('lg-hide-items').removeClass('lg-components-open');\n        }\n      }\n    }\n  };\n  LightGallery.prototype.touchEnd = function (endCoords, startCoords, event) {\n    var _this = this;\n    var distance;\n    // keep slide animation for any mode while dragg/swipe\n    if (this.settings.mode !== 'lg-slide') {\n      this.outer.addClass('lg-slide');\n    }\n    // set transition duration\n    setTimeout(function () {\n      _this.$container.removeClass('lg-dragging-vertical');\n      _this.outer.removeClass('lg-dragging lg-hide-items').addClass('lg-components-open');\n      var triggerClick = true;\n      if (_this.swipeDirection === 'horizontal') {\n        distance = endCoords.pageX - startCoords.pageX;\n        var distanceAbs = Math.abs(endCoords.pageX - startCoords.pageX);\n        if (distance < 0 && distanceAbs > _this.settings.swipeThreshold) {\n          _this.goToNextSlide(true);\n          triggerClick = false;\n        } else if (distance > 0 && distanceAbs > _this.settings.swipeThreshold) {\n          _this.goToPrevSlide(true);\n          triggerClick = false;\n        }\n      } else if (_this.swipeDirection === 'vertical') {\n        distance = Math.abs(endCoords.pageY - startCoords.pageY);\n        if (_this.settings.closable && _this.settings.swipeToClose && distance > 100) {\n          _this.closeGallery();\n          return;\n        } else {\n          _this.$backdrop.css('opacity', 1);\n        }\n      }\n      _this.outer.find('.lg-item').removeAttr('style');\n      if (triggerClick && Math.abs(endCoords.pageX - startCoords.pageX) < 5) {\n        // Trigger click if distance is less than 5 pix\n        var target = $LG(event.target);\n        if (_this.isPosterElement(target)) {\n          _this.LGel.trigger(lGEvents.posterClick);\n        }\n      }\n      _this.swipeDirection = undefined;\n    });\n    // remove slide class once drag/swipe is completed if mode is not slide\n    setTimeout(function () {\n      if (!_this.outer.hasClass('lg-dragging') && _this.settings.mode !== 'lg-slide') {\n        _this.outer.removeClass('lg-slide');\n      }\n    }, this.settings.speed + 100);\n  };\n  LightGallery.prototype.enableSwipe = function () {\n    var _this = this;\n    var startCoords = {};\n    var endCoords = {};\n    var isMoved = false;\n    var isSwiping = false;\n    if (this.settings.enableSwipe) {\n      this.$inner.on('touchstart.lg', function (e) {\n        _this.dragOrSwipeEnabled = true;\n        var $item = _this.getSlideItem(_this.index);\n        if (($LG(e.target).hasClass('lg-item') || $item.get().contains(e.target)) && !_this.outer.hasClass('lg-zoomed') && !_this.lgBusy && e.touches.length === 1) {\n          isSwiping = true;\n          _this.touchAction = 'swipe';\n          _this.manageSwipeClass();\n          startCoords = {\n            pageX: e.touches[0].pageX,\n            pageY: e.touches[0].pageY\n          };\n        }\n      });\n      this.$inner.on('touchmove.lg', function (e) {\n        if (isSwiping && _this.touchAction === 'swipe' && e.touches.length === 1) {\n          endCoords = {\n            pageX: e.touches[0].pageX,\n            pageY: e.touches[0].pageY\n          };\n          _this.touchMove(startCoords, endCoords, e);\n          isMoved = true;\n        }\n      });\n      this.$inner.on('touchend.lg', function (event) {\n        if (_this.touchAction === 'swipe') {\n          if (isMoved) {\n            isMoved = false;\n            _this.touchEnd(endCoords, startCoords, event);\n          } else if (isSwiping) {\n            var target = $LG(event.target);\n            if (_this.isPosterElement(target)) {\n              _this.LGel.trigger(lGEvents.posterClick);\n            }\n          }\n          _this.touchAction = undefined;\n          isSwiping = false;\n        }\n      });\n    }\n  };\n  LightGallery.prototype.enableDrag = function () {\n    var _this = this;\n    var startCoords = {};\n    var endCoords = {};\n    var isDraging = false;\n    var isMoved = false;\n    if (this.settings.enableDrag) {\n      this.outer.on('mousedown.lg', function (e) {\n        _this.dragOrSwipeEnabled = true;\n        var $item = _this.getSlideItem(_this.index);\n        if ($LG(e.target).hasClass('lg-item') || $item.get().contains(e.target)) {\n          if (!_this.outer.hasClass('lg-zoomed') && !_this.lgBusy) {\n            e.preventDefault();\n            if (!_this.lgBusy) {\n              _this.manageSwipeClass();\n              startCoords = {\n                pageX: e.pageX,\n                pageY: e.pageY\n              };\n              isDraging = true;\n              // ** Fix for webkit cursor issue https://code.google.com/p/chromium/issues/detail?id=26723\n              _this.outer.get().scrollLeft += 1;\n              _this.outer.get().scrollLeft -= 1;\n              // *\n              _this.outer.removeClass('lg-grab').addClass('lg-grabbing');\n              _this.LGel.trigger(lGEvents.dragStart);\n            }\n          }\n        }\n      });\n      $LG(window).on(\"mousemove.lg.global\" + this.lgId, function (e) {\n        if (isDraging && _this.lgOpened) {\n          isMoved = true;\n          endCoords = {\n            pageX: e.pageX,\n            pageY: e.pageY\n          };\n          _this.touchMove(startCoords, endCoords);\n          _this.LGel.trigger(lGEvents.dragMove);\n        }\n      });\n      $LG(window).on(\"mouseup.lg.global\" + this.lgId, function (event) {\n        if (!_this.lgOpened) {\n          return;\n        }\n        var target = $LG(event.target);\n        if (isMoved) {\n          isMoved = false;\n          _this.touchEnd(endCoords, startCoords, event);\n          _this.LGel.trigger(lGEvents.dragEnd);\n        } else if (_this.isPosterElement(target)) {\n          _this.LGel.trigger(lGEvents.posterClick);\n        }\n        // Prevent execution on click\n        if (isDraging) {\n          isDraging = false;\n          _this.outer.removeClass('lg-grabbing').addClass('lg-grab');\n        }\n      });\n    }\n  };\n  LightGallery.prototype.triggerPosterClick = function () {\n    var _this = this;\n    this.$inner.on('click.lg', function (event) {\n      if (!_this.dragOrSwipeEnabled && _this.isPosterElement($LG(event.target))) {\n        _this.LGel.trigger(lGEvents.posterClick);\n      }\n    });\n  };\n  LightGallery.prototype.manageSwipeClass = function () {\n    var _touchNext = this.index + 1;\n    var _touchPrev = this.index - 1;\n    if (this.settings.loop && this.galleryItems.length > 2) {\n      if (this.index === 0) {\n        _touchPrev = this.galleryItems.length - 1;\n      } else if (this.index === this.galleryItems.length - 1) {\n        _touchNext = 0;\n      }\n    }\n    this.outer.find('.lg-item').removeClass('lg-next-slide lg-prev-slide');\n    if (_touchPrev > -1) {\n      this.getSlideItem(_touchPrev).addClass('lg-prev-slide');\n    }\n    this.getSlideItem(_touchNext).addClass('lg-next-slide');\n  };\n  /**\r\n   * Go to next slide\r\n   * @param {Boolean} fromTouch - true if slide function called via touch event\r\n   * @category lGPublicMethods\r\n   * @example\r\n   *  const plugin = lightGallery();\r\n   *  plugin.goToNextSlide();\r\n   * @see <a href=\"/demos/methods/\">Demo</a>\r\n   */\n  LightGallery.prototype.goToNextSlide = function (fromTouch) {\n    var _this = this;\n    var _loop = this.settings.loop;\n    if (fromTouch && this.galleryItems.length < 3) {\n      _loop = false;\n    }\n    if (!this.lgBusy) {\n      if (this.index + 1 < this.galleryItems.length) {\n        this.index++;\n        this.LGel.trigger(lGEvents.beforeNextSlide, {\n          index: this.index\n        });\n        this.slide(this.index, !!fromTouch, false, 'next');\n      } else {\n        if (_loop) {\n          this.index = 0;\n          this.LGel.trigger(lGEvents.beforeNextSlide, {\n            index: this.index\n          });\n          this.slide(this.index, !!fromTouch, false, 'next');\n        } else if (this.settings.slideEndAnimation && !fromTouch) {\n          this.outer.addClass('lg-right-end');\n          setTimeout(function () {\n            _this.outer.removeClass('lg-right-end');\n          }, 400);\n        }\n      }\n    }\n  };\n  /**\r\n   * Go to previous slides\r\n   * @param {Boolean} fromTouch - true if slide function called via touch event\r\n   * @category lGPublicMethods\r\n   * @example\r\n   *  const plugin = lightGallery({});\r\n   *  plugin.goToPrevSlide();\r\n   * @see <a href=\"/demos/methods/\">Demo</a>\r\n   *\r\n   */\n  LightGallery.prototype.goToPrevSlide = function (fromTouch) {\n    var _this = this;\n    var _loop = this.settings.loop;\n    if (fromTouch && this.galleryItems.length < 3) {\n      _loop = false;\n    }\n    if (!this.lgBusy) {\n      if (this.index > 0) {\n        this.index--;\n        this.LGel.trigger(lGEvents.beforePrevSlide, {\n          index: this.index,\n          fromTouch: fromTouch\n        });\n        this.slide(this.index, !!fromTouch, false, 'prev');\n      } else {\n        if (_loop) {\n          this.index = this.galleryItems.length - 1;\n          this.LGel.trigger(lGEvents.beforePrevSlide, {\n            index: this.index,\n            fromTouch: fromTouch\n          });\n          this.slide(this.index, !!fromTouch, false, 'prev');\n        } else if (this.settings.slideEndAnimation && !fromTouch) {\n          this.outer.addClass('lg-left-end');\n          setTimeout(function () {\n            _this.outer.removeClass('lg-left-end');\n          }, 400);\n        }\n      }\n    }\n  };\n  LightGallery.prototype.keyPress = function () {\n    var _this = this;\n    $LG(window).on(\"keydown.lg.global\" + this.lgId, function (e) {\n      if (_this.lgOpened && _this.settings.escKey === true && e.keyCode === 27) {\n        e.preventDefault();\n        if (_this.settings.allowMediaOverlap && _this.outer.hasClass('lg-can-toggle') && _this.outer.hasClass('lg-components-open')) {\n          _this.outer.removeClass('lg-components-open');\n        } else {\n          _this.closeGallery();\n        }\n      }\n      if (_this.lgOpened && _this.galleryItems.length > 1) {\n        if (e.keyCode === 37) {\n          e.preventDefault();\n          _this.goToPrevSlide();\n        }\n        if (e.keyCode === 39) {\n          e.preventDefault();\n          _this.goToNextSlide();\n        }\n      }\n    });\n  };\n  LightGallery.prototype.arrow = function () {\n    var _this = this;\n    this.getElementById('lg-prev').on('click.lg', function () {\n      _this.goToPrevSlide();\n    });\n    this.getElementById('lg-next').on('click.lg', function () {\n      _this.goToNextSlide();\n    });\n  };\n  LightGallery.prototype.arrowDisable = function (index) {\n    // Disable arrows if settings.hideControlOnEnd is true\n    if (!this.settings.loop && this.settings.hideControlOnEnd) {\n      var $prev = this.getElementById('lg-prev');\n      var $next = this.getElementById('lg-next');\n      if (index + 1 === this.galleryItems.length) {\n        $next.attr('disabled', 'disabled').addClass('disabled');\n      } else {\n        $next.removeAttr('disabled').removeClass('disabled');\n      }\n      if (index === 0) {\n        $prev.attr('disabled', 'disabled').addClass('disabled');\n      } else {\n        $prev.removeAttr('disabled').removeClass('disabled');\n      }\n    }\n  };\n  LightGallery.prototype.setTranslate = function ($el, xValue, yValue, scaleX, scaleY) {\n    if (scaleX === void 0) {\n      scaleX = 1;\n    }\n    if (scaleY === void 0) {\n      scaleY = 1;\n    }\n    $el.css('transform', 'translate3d(' + xValue + 'px, ' + yValue + 'px, 0px) scale3d(' + scaleX + ', ' + scaleY + ', 1)');\n  };\n  LightGallery.prototype.mousewheel = function () {\n    var _this = this;\n    var lastCall = 0;\n    this.outer.on('wheel.lg', function (e) {\n      if (!e.deltaY || _this.galleryItems.length < 2) {\n        return;\n      }\n      e.preventDefault();\n      var now = new Date().getTime();\n      if (now - lastCall < 1000) {\n        return;\n      }\n      lastCall = now;\n      if (e.deltaY > 0) {\n        _this.goToNextSlide();\n      } else if (e.deltaY < 0) {\n        _this.goToPrevSlide();\n      }\n    });\n  };\n  LightGallery.prototype.isSlideElement = function (target) {\n    return target.hasClass('lg-outer') || target.hasClass('lg-item') || target.hasClass('lg-img-wrap');\n  };\n  LightGallery.prototype.isPosterElement = function (target) {\n    var playButton = this.getSlideItem(this.index).find('.lg-video-play-button').get();\n    return target.hasClass('lg-video-poster') || target.hasClass('lg-video-play-button') || playButton && playButton.contains(target.get());\n  };\n  /**\r\n   * Maximize minimize inline gallery.\r\n   * @category lGPublicMethods\r\n   */\n  LightGallery.prototype.toggleMaximize = function () {\n    var _this = this;\n    this.getElementById('lg-maximize').on('click.lg', function () {\n      _this.$container.toggleClass('lg-inline');\n      _this.refreshOnResize();\n    });\n  };\n  LightGallery.prototype.invalidateItems = function () {\n    for (var index = 0; index < this.items.length; index++) {\n      var element = this.items[index];\n      var $element = $LG(element);\n      $element.off(\"click.lgcustom-item-\" + $element.attr('data-lg-id'));\n    }\n  };\n  LightGallery.prototype.trapFocus = function () {\n    var _this = this;\n    this.$container.get().focus({\n      preventScroll: true\n    });\n    $LG(window).on(\"keydown.lg.global\" + this.lgId, function (e) {\n      if (!_this.lgOpened) {\n        return;\n      }\n      var isTabPressed = e.key === 'Tab' || e.keyCode === 9;\n      if (!isTabPressed) {\n        return;\n      }\n      var focusableEls = utils.getFocusableElements(_this.$container.get());\n      var firstFocusableEl = focusableEls[0];\n      var lastFocusableEl = focusableEls[focusableEls.length - 1];\n      if (e.shiftKey) {\n        if (document.activeElement === firstFocusableEl) {\n          lastFocusableEl.focus();\n          e.preventDefault();\n        }\n      } else {\n        if (document.activeElement === lastFocusableEl) {\n          firstFocusableEl.focus();\n          e.preventDefault();\n        }\n      }\n    });\n  };\n  LightGallery.prototype.manageCloseGallery = function () {\n    var _this = this;\n    if (!this.settings.closable) return;\n    var mousedown = false;\n    this.getElementById('lg-close').on('click.lg', function () {\n      _this.closeGallery();\n    });\n    if (this.settings.closeOnTap) {\n      // If you drag the slide and release outside gallery gets close on chrome\n      // for preventing this check mousedown and mouseup happened on .lg-item or lg-outer\n      this.outer.on('mousedown.lg', function (e) {\n        var target = $LG(e.target);\n        if (_this.isSlideElement(target)) {\n          mousedown = true;\n        } else {\n          mousedown = false;\n        }\n      });\n      this.outer.on('mousemove.lg', function () {\n        mousedown = false;\n      });\n      this.outer.on('mouseup.lg', function (e) {\n        var target = $LG(e.target);\n        if (_this.isSlideElement(target) && mousedown) {\n          if (!_this.outer.hasClass('lg-dragging')) {\n            _this.closeGallery();\n          }\n        }\n      });\n    }\n  };\n  /**\r\n   * Close lightGallery if it is opened.\r\n   *\r\n   * @description If closable is false in the settings, you need to pass true via closeGallery method to force close gallery\r\n   * @return returns the estimated time to close gallery completely including the close animation duration\r\n   * @category lGPublicMethods\r\n   * @example\r\n   *  const plugin = lightGallery();\r\n   *  plugin.closeGallery();\r\n   *\r\n   */\n  LightGallery.prototype.closeGallery = function (force) {\n    var _this = this;\n    if (!this.lgOpened || !this.settings.closable && !force) {\n      return 0;\n    }\n    this.LGel.trigger(lGEvents.beforeClose);\n    if (this.settings.resetScrollPosition && !this.settings.hideScrollbar) {\n      $LG(window).scrollTop(this.prevScrollTop);\n    }\n    var currentItem = this.items[this.index];\n    var transform;\n    if (this.zoomFromOrigin && currentItem) {\n      var _a = this.mediaContainerPosition,\n        top_4 = _a.top,\n        bottom = _a.bottom;\n      var _b = this.galleryItems[this.index],\n        __slideVideoInfo = _b.__slideVideoInfo,\n        poster = _b.poster;\n      var imageSize = utils.getSize(currentItem, this.outer, top_4 + bottom, __slideVideoInfo && poster && this.settings.videoMaxSize);\n      transform = utils.getTransform(currentItem, this.outer, top_4, bottom, imageSize);\n    }\n    if (this.zoomFromOrigin && transform) {\n      this.outer.addClass('lg-closing lg-zoom-from-image');\n      this.getSlideItem(this.index).addClass('lg-start-end-progress').css('transition-duration', this.settings.startAnimationDuration + 'ms').css('transform', transform);\n    } else {\n      this.outer.addClass('lg-hide-items');\n      // lg-zoom-from-image is used for setting the opacity to 1 if zoomFromOrigin is true\n      // If the closing item doesn't have the lg-size attribute, remove this class to avoid the closing css conflicts\n      this.outer.removeClass('lg-zoom-from-image');\n    }\n    // Unbind all events added by lightGallery\n    // @todo\n    //this.$el.off('.lg.tm');\n    this.destroyModules();\n    this.lGalleryOn = false;\n    this.isDummyImageRemoved = false;\n    this.zoomFromOrigin = this.settings.zoomFromOrigin;\n    clearTimeout(this.hideBarTimeout);\n    this.hideBarTimeout = false;\n    $LG('html').removeClass('lg-on');\n    this.outer.removeClass('lg-visible lg-components-open');\n    // Resetting opacity to 0 isd required as  vertical swipe to close function adds inline opacity.\n    this.$backdrop.removeClass('in').css('opacity', 0);\n    var removeTimeout = this.zoomFromOrigin && transform ? Math.max(this.settings.startAnimationDuration, this.settings.backdropDuration) : this.settings.backdropDuration;\n    this.$container.removeClass('lg-show-in');\n    // Once the closign animation is completed and gallery is invisible\n    setTimeout(function () {\n      if (_this.zoomFromOrigin && transform) {\n        _this.outer.removeClass('lg-zoom-from-image');\n      }\n      _this.$container.removeClass('lg-show');\n      // Reset scrollbar\n      _this.resetScrollBar();\n      // Need to remove inline opacity as it is used in the stylesheet as well\n      _this.$backdrop.removeAttr('style').css('transition-duration', _this.settings.backdropDuration + 'ms');\n      _this.outer.removeClass(\"lg-closing \" + _this.settings.startClass);\n      _this.getSlideItem(_this.index).removeClass('lg-start-end-progress');\n      _this.$inner.empty();\n      if (_this.lgOpened) {\n        _this.LGel.trigger(lGEvents.afterClose, {\n          instance: _this\n        });\n      }\n      if (_this.$container.get()) {\n        _this.$container.get().blur();\n      }\n      _this.lgOpened = false;\n    }, removeTimeout + 100);\n    return removeTimeout + 100;\n  };\n  LightGallery.prototype.initModules = function () {\n    this.plugins.forEach(function (module) {\n      try {\n        module.init();\n      } catch (err) {\n        console.warn(\"lightGallery:- make sure lightGallery module is properly initiated\");\n      }\n    });\n  };\n  LightGallery.prototype.destroyModules = function (destroy) {\n    this.plugins.forEach(function (module) {\n      try {\n        if (destroy) {\n          module.destroy();\n        } else {\n          module.closeGallery && module.closeGallery();\n        }\n      } catch (err) {\n        console.warn(\"lightGallery:- make sure lightGallery module is properly destroyed\");\n      }\n    });\n  };\n  /**\r\n   * Refresh lightGallery with new set of children.\r\n   *\r\n   * @description This is useful to update the gallery when the child elements are changed without calling destroy method.\r\n   *\r\n   * If you are using dynamic mode, you can pass the modified array of dynamicEl as the first parameter to refresh the dynamic gallery\r\n   * @see <a href=\"/demos/dynamic-mode/\">Demo</a>\r\n   * @category lGPublicMethods\r\n   * @example\r\n   *  const plugin = lightGallery();\r\n   *  // Delete or add children, then call\r\n   *  plugin.refresh();\r\n   *\r\n   */\n  LightGallery.prototype.refresh = function (galleryItems) {\n    if (!this.settings.dynamic) {\n      this.invalidateItems();\n    }\n    if (galleryItems) {\n      this.galleryItems = galleryItems;\n    } else {\n      this.galleryItems = this.getItems();\n    }\n    this.updateControls();\n    this.openGalleryOnItemClick();\n    this.LGel.trigger(lGEvents.updateSlides);\n  };\n  LightGallery.prototype.updateControls = function () {\n    this.addSlideVideoInfo(this.galleryItems);\n    this.updateCounterTotal();\n    this.manageSingleSlideClassName();\n  };\n  LightGallery.prototype.destroyGallery = function () {\n    this.destroyModules(true);\n    if (!this.settings.dynamic) {\n      this.invalidateItems();\n    }\n    $LG(window).off(\".lg.global\" + this.lgId);\n    this.LGel.off('.lg');\n    this.$container.remove();\n  };\n  /**\r\n   * Destroy lightGallery.\r\n   * Destroy lightGallery and its plugin instances completely\r\n   *\r\n   * @description This method also calls CloseGallery function internally. Returns the time takes to completely close and destroy the instance.\r\n   * In case if you want to re-initialize lightGallery right after destroying it, initialize it only once the destroy process is completed.\r\n   * You can use refresh method most of the times.\r\n   * @category lGPublicMethods\r\n   * @example\r\n   *  const plugin = lightGallery();\r\n   *  plugin.destroy();\r\n   *\r\n   */\n  LightGallery.prototype.destroy = function () {\n    var closeTimeout = this.closeGallery(true);\n    if (closeTimeout) {\n      setTimeout(this.destroyGallery.bind(this), closeTimeout);\n    } else {\n      this.destroyGallery();\n    }\n    return closeTimeout;\n  };\n  return LightGallery;\n}();\nfunction lightGallery(el, options) {\n  return new LightGallery(el, options);\n}\nexport default lightGallery;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__spreadA<PERSON>ys", "il", "r", "Array", "k", "a", "j", "jl", "lGEvents", "afterAppendSlide", "init", "hasVideo", "containerResize", "updateSlides", "afterAppendSubHtml", "beforeOpen", "afterOpen", "slideItemLoad", "beforeSlide", "afterSlide", "posterClick", "dragStart", "dragMove", "dragEnd", "beforeNextSlide", "beforePrevSlide", "beforeClose", "afterClose", "rotateLeft", "rotateRight", "flipHorizontal", "flipVertical", "autoplay", "autoplayStart", "autoplayStop", "lightGalleryCoreSettings", "mode", "easing", "speed", "licenseKey", "height", "width", "addClass", "startClass", "backdropDuration", "container", "startAnimationDuration", "zoomFromOrigin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showBarsAfter", "<PERSON><PERSON><PERSON><PERSON>", "supportLegacyBrowser", "allowMediaOverlap", "videoMaxSize", "loadYouTubePoster", "defaultCaptionHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "resetScrollPosition", "hideScrollbar", "closable", "swipeToClose", "closeOnTap", "showCloseIcon", "showMaximizeIcon", "loop", "escKey", "keyPress", "trapFocus", "controls", "slideEndAnimation", "hideControlOnEnd", "mousewheel", "getCaptionFromTitleOrAlt", "appendSubHtmlTo", "subHtmlSelectorRelative", "preload", "numberOfSlideItemsInDom", "selector", "selectWithin", "nextHtml", "prevHtml", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iframeHeight", "iframeMaxWidth", "iframeMaxHeight", "download", "counter", "appendCounterTo", "swipe<PERSON><PERSON><PERSON><PERSON>", "enableSwipe", "enableDrag", "dynamic", "dynamicEl", "extraProps", "exThumbImage", "isMobile", "undefined", "mobileSettings", "plugins", "strings", "closeGallery", "toggleMaximize", "previousSlide", "nextSlide", "playVideo", "mediaLoadingFailed", "initLgPolyfills", "window", "CustomEvent", "event", "params", "bubbles", "cancelable", "detail", "evt", "document", "createEvent", "initCustomEvent", "Element", "matches", "msMatchesSelector", "webkitMatchesSelector", "lgQuery", "cssVenderPrefixes", "_getSelector", "firstElement", "_getFirstEl", "generateUUID", "replace", "c", "Math", "random", "v", "toString", "context", "fl", "substring", "querySelector", "querySelectorAll", "_each", "func", "for<PERSON>ach", "_setCssVendorPrefix", "el", "cssProperty", "value", "property", "group1", "toUpperCase", "indexOf", "style", "char<PERSON>t", "toLowerCase", "slice", "isEventMatched", "eventName", "eventNamespace", "split", "filter", "e", "every", "attr", "getAttribute", "setAttribute", "find", "$LG", "first", "eq", "parent", "parentElement", "get", "removeAttr", "attributes", "attrs", "removeAttribute", "wrap", "className", "wrapper", "createElement", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "classNames", "classList", "add", "removeClass", "remove", "hasClass", "contains", "hasAttribute", "attribute", "toggleClass", "css", "_this", "on", "events", "listener", "isArray", "eventListeners", "push", "addEventListener", "once", "off", "keys", "removeEventListener", "trigger", "customEvent", "dispatchEvent", "load", "url", "fetch", "then", "res", "text", "html", "innerHTML", "append", "insertAdjacentHTML", "prepend", "empty", "scrollTop", "body", "documentElement", "pageYOffset", "scrollLeft", "pageXOffset", "offset", "left", "top", "rect", "getBoundingClientRect", "bodyMarginLeft", "marginLeft", "parseFloat", "currentStyle", "getComputedStyle", "clientWidth", "paddingLeft", "paddingRight", "clientHeight", "paddingTop", "paddingBottom", "defaultDynamicOptions", "convertToData", "g", "utils", "getSize", "spacing", "defaultLgSize", "LGel", "lgSize", "isResponsiveSizes", "wWidth", "innerWidth", "size_1", "responsiveWidth", "parseInt", "size", "c<PERSON><PERSON><PERSON>", "cHeight", "max<PERSON><PERSON><PERSON>", "min", "maxHeight", "ratio", "getTransform", "bottom", "imageSize", "containerRect", "wHeight", "<PERSON><PERSON><PERSON><PERSON>", "elHeight", "elStyle", "x", "borderLeft", "y", "borderTop", "scX", "scY", "transform", "getIframeMarkup", "src", "iframeTitle", "title", "getImgMarkup", "altAttr", "srcset", "sizes", "sources", "srcsetAttr", "sizesAttr", "imgMarkup", "sourceTag", "sourceObj", "JSON", "parse", "map", "source", "key", "getResponsiveSrc", "srcItms", "rsWidth", "rsSrc", "_src", "splice", "isImageLoaded", "img", "complete", "naturalWidth", "getVideoPosterMarkup", "_poster", "dummyImg", "videoContStyle", "playVideoString", "_isVideo", "videoClass", "youtube", "vimeo", "getFocusableElements", "elements", "visibleElements", "element", "display", "visibility", "getDynamicOptions", "items", "dynamicElements", "availableDynamicOptions", "item", "specified", "dynamicAttr", "name", "label", "currentItem", "alt", "thumb", "subHtml", "test", "navigator", "userAgent", "isVideo", "isHTML5VIdeo", "html5", "console", "error", "match", "wistia", "lgId", "LightGallery", "options", "lgOpened", "lGalleryOn", "lgBusy", "currentItemsInDom", "prevScrollTop", "bodyPaddingRight", "isDummyImageRemoved", "dragOrSwipe<PERSON><PERSON>bled", "mediaContainerPosition", "generateSettings", "buildModules", "settings", "galleryItems", "getItems", "normalizeSettings", "validateLicense", "addSlideVideoInfo", "buildStructure", "instance", "setTimeout", "triggerPosterClick", "arrow", "openGalleryOnItemClick", "_loop_1", "this_1", "$element", "uuid", "preventDefault", "currentItemIndex", "openGallery", "plugin", "warn", "getSlideItem", "getSlideItemId", "getIdName", "id", "getElementById", "manageSingleSlideClassName", "outer", "$container", "subHtmlCont", "addClasses", "containerClassName", "closeIcon", "maximizeIcon", "template", "$lgComponents", "$backdrop", "$inner", "$content", "$toolbar", "outerClassNames", "refreshOnResize", "hideBars", "manageCloseGallery", "initModules", "currentGalleryItem", "__slideVideoInfo", "getMediaContainerPosition", "_a", "top_1", "currentImageSize", "resizeVideoSlide", "imgStyle", "getDummyImgStyles", "lgVideoStyle", "getVideoContStyle", "currentSlide", "currentSrc", "updateControls", "_index", "some", "galleryItem", "itemIndex", "organizeSlideItems", "loadContent", "updateCurrentCounter", "children", "shouldHideScrollbar", "bodyRect", "scrollbarWidth", "resetScrollBar", "itemsToBeInsertedToDom", "getItemsToBeInsertedToDom", "addHtml", "setMediaContainerPosition", "timeout", "currentSlide_1", "slide", "captionHeight", "thumbContainer", "thumbHeight", "clearTimeout", "hideBarTimeout", "initPictureFill", "$img", "picturefill", "counterHtml", "subHtmlUrl", "fL", "getDummyImageContent", "$currentSlide", "$currentItem", "_dummyImgSrc", "dummy<PERSON>mg<PERSON><PERSON>nt", "setImgMarkup", "imgContent", "isFirstSlideWithZoomAnimation", "onSlideObjectLoad", "$slide", "isHTML5VideoWithoutPoster", "onLoad", "onError", "mediaObject", "onLgObjectLoad", "delay", "isFirstSlide", "triggerSlideItemLoad", "_speed", "getSlideType", "poster", "video", "rec", "_html5Video", "responsive", "srcDyItms", "videoInfo", "iframe", "top_2", "videoSize", "markup", "hasStartAnimation", "html5Video", "<PERSON><PERSON><PERSON><PERSON>", "loadContentOnFirstSlideLoad", "prevIndex", "numberOfItems", "possibleNumberOfItems", "max", "prevIndexItem", "_element", "idx", "numberOfExistingItems", "getPreviousSlideIndex", "currentItemId", "setDownloadValue", "hideDownloadBtn", "downloadUrl", "$download", "makeSlideAnimation", "direction", "currentSlideItem", "previousSlideItem", "fromTouch", "fromThumb", "numberOfGalleryItems", "previousSlideItem_1", "top_3", "arrowDisable", "touchPrev", "touchNext", "updateCounterTotal", "touchMove", "startCoords", "endCoords", "distanceX", "pageX", "distanceY", "pageY", "allowSwipe", "swipeDirection", "abs", "setTranslate", "offsetWidth", "slideWidthAmount", "gutter", "opacity", "innerHeight", "scale", "touchEnd", "distance", "triggerClick", "distanceAbs", "goToNextSlide", "goToPrevSlide", "target", "isPosterElement", "isMoved", "isSwiping", "$item", "touches", "touchAction", "manageSwipeClass", "isDraging", "_touchNext", "_touchPrev", "_loop", "keyCode", "$prev", "$next", "$el", "xValue", "yValue", "scaleX", "scaleY", "lastCall", "deltaY", "now", "Date", "getTime", "isSlideElement", "playButton", "invalidateItems", "focus", "preventScroll", "isTabPressed", "focusableEls", "firstFocusableEl", "lastFocusableEl", "shift<PERSON>ey", "activeElement", "mousedown", "force", "top_4", "_b", "destroyModules", "removeTimeout", "blur", "module", "err", "destroy", "refresh", "destroyGallery", "closeTimeout", "bind", "lightGallery"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lightgallery/lightgallery.es5.js"], "sourcesContent": ["/*!\n * lightgallery | 2.7.2 | September 20th 2023\n * http://www.lightgalleryjs.com/\n * Copyright (c) 2020 Sachin Neravath;\n * @license GPLv3\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\n\n/**\r\n * List of lightGallery events\r\n * All events should be documented here\r\n * Below interfaces are used to build the website documentations\r\n * */\r\nvar lGEvents = {\r\n    afterAppendSlide: 'lgAfterAppendSlide',\r\n    init: 'lgInit',\r\n    hasVideo: 'lgHasVideo',\r\n    containerResize: 'lgContainerResize',\r\n    updateSlides: 'lgUpdateSlides',\r\n    afterAppendSubHtml: 'lgAfterAppendSubHtml',\r\n    beforeOpen: 'lgBeforeOpen',\r\n    afterOpen: 'lgAfterOpen',\r\n    slideItemLoad: 'lgSlideItemLoad',\r\n    beforeSlide: 'lgBeforeSlide',\r\n    afterSlide: 'lgAfterSlide',\r\n    posterClick: 'lgPosterClick',\r\n    dragStart: 'lgDragStart',\r\n    dragMove: 'lgDragMove',\r\n    dragEnd: 'lgDragEnd',\r\n    beforeNextSlide: 'lgBeforeNextSlide',\r\n    beforePrevSlide: 'lgBeforePrevSlide',\r\n    beforeClose: 'lgBeforeClose',\r\n    afterClose: 'lgAfterClose',\r\n    rotateLeft: 'lgRotateLeft',\r\n    rotateRight: 'lgRotateRight',\r\n    flipHorizontal: 'lgFlipHorizontal',\r\n    flipVertical: 'lgFlipVertical',\r\n    autoplay: 'lgAutoplay',\r\n    autoplayStart: 'lgAutoplayStart',\r\n    autoplayStop: 'lgAutoplayStop',\r\n};\n\nvar lightGalleryCoreSettings = {\r\n    mode: 'lg-slide',\r\n    easing: 'ease',\r\n    speed: 400,\r\n    licenseKey: '0000-0000-000-0000',\r\n    height: '100%',\r\n    width: '100%',\r\n    addClass: '',\r\n    startClass: 'lg-start-zoom',\r\n    backdropDuration: 300,\r\n    container: '',\r\n    startAnimationDuration: 400,\r\n    zoomFromOrigin: true,\r\n    hideBarsDelay: 0,\r\n    showBarsAfter: 10000,\r\n    slideDelay: 0,\r\n    supportLegacyBrowser: true,\r\n    allowMediaOverlap: false,\r\n    videoMaxSize: '1280-720',\r\n    loadYouTubePoster: true,\r\n    defaultCaptionHeight: 0,\r\n    ariaLabelledby: '',\r\n    ariaDescribedby: '',\r\n    resetScrollPosition: true,\r\n    hideScrollbar: false,\r\n    closable: true,\r\n    swipeToClose: true,\r\n    closeOnTap: true,\r\n    showCloseIcon: true,\r\n    showMaximizeIcon: false,\r\n    loop: true,\r\n    escKey: true,\r\n    keyPress: true,\r\n    trapFocus: true,\r\n    controls: true,\r\n    slideEndAnimation: true,\r\n    hideControlOnEnd: false,\r\n    mousewheel: false,\r\n    getCaptionFromTitleOrAlt: true,\r\n    appendSubHtmlTo: '.lg-sub-html',\r\n    subHtmlSelectorRelative: false,\r\n    preload: 2,\r\n    numberOfSlideItemsInDom: 10,\r\n    selector: '',\r\n    selectWithin: '',\r\n    nextHtml: '',\r\n    prevHtml: '',\r\n    index: 0,\r\n    iframeWidth: '100%',\r\n    iframeHeight: '100%',\r\n    iframeMaxWidth: '100%',\r\n    iframeMaxHeight: '100%',\r\n    download: true,\r\n    counter: true,\r\n    appendCounterTo: '.lg-toolbar',\r\n    swipeThreshold: 50,\r\n    enableSwipe: true,\r\n    enableDrag: true,\r\n    dynamic: false,\r\n    dynamicEl: [],\r\n    extraProps: [],\r\n    exThumbImage: '',\r\n    isMobile: undefined,\r\n    mobileSettings: {\r\n        controls: false,\r\n        showCloseIcon: false,\r\n        download: false,\r\n    },\r\n    plugins: [],\r\n    strings: {\r\n        closeGallery: 'Close gallery',\r\n        toggleMaximize: 'Toggle maximize',\r\n        previousSlide: 'Previous slide',\r\n        nextSlide: 'Next slide',\r\n        download: 'Download',\r\n        playVideo: 'Play video',\r\n        mediaLoadingFailed: 'Oops... Failed to load content...',\r\n    },\r\n};\n\nfunction initLgPolyfills() {\r\n    (function () {\r\n        if (typeof window.CustomEvent === 'function')\r\n            return false;\r\n        function CustomEvent(event, params) {\r\n            params = params || {\r\n                bubbles: false,\r\n                cancelable: false,\r\n                detail: null,\r\n            };\r\n            var evt = document.createEvent('CustomEvent');\r\n            evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);\r\n            return evt;\r\n        }\r\n        window.CustomEvent = CustomEvent;\r\n    })();\r\n    (function () {\r\n        if (!Element.prototype.matches) {\r\n            Element.prototype.matches =\r\n                Element.prototype.msMatchesSelector ||\r\n                    Element.prototype.webkitMatchesSelector;\r\n        }\r\n    })();\r\n}\r\nvar lgQuery = /** @class */ (function () {\r\n    function lgQuery(selector) {\r\n        this.cssVenderPrefixes = [\r\n            'TransitionDuration',\r\n            'TransitionTimingFunction',\r\n            'Transform',\r\n            'Transition',\r\n        ];\r\n        this.selector = this._getSelector(selector);\r\n        this.firstElement = this._getFirstEl();\r\n        return this;\r\n    }\r\n    lgQuery.generateUUID = function () {\r\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n            var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\r\n            return v.toString(16);\r\n        });\r\n    };\r\n    lgQuery.prototype._getSelector = function (selector, context) {\r\n        if (context === void 0) { context = document; }\r\n        if (typeof selector !== 'string') {\r\n            return selector;\r\n        }\r\n        context = context || document;\r\n        var fl = selector.substring(0, 1);\r\n        if (fl === '#') {\r\n            return context.querySelector(selector);\r\n        }\r\n        else {\r\n            return context.querySelectorAll(selector);\r\n        }\r\n    };\r\n    lgQuery.prototype._each = function (func) {\r\n        if (!this.selector) {\r\n            return this;\r\n        }\r\n        if (this.selector.length !== undefined) {\r\n            [].forEach.call(this.selector, func);\r\n        }\r\n        else {\r\n            func(this.selector, 0);\r\n        }\r\n        return this;\r\n    };\r\n    lgQuery.prototype._setCssVendorPrefix = function (el, cssProperty, value) {\r\n        // prettier-ignore\r\n        var property = cssProperty.replace(/-([a-z])/gi, function (s, group1) {\r\n            return group1.toUpperCase();\r\n        });\r\n        if (this.cssVenderPrefixes.indexOf(property) !== -1) {\r\n            el.style[property.charAt(0).toLowerCase() + property.slice(1)] = value;\r\n            el.style['webkit' + property] = value;\r\n            el.style['moz' + property] = value;\r\n            el.style['ms' + property] = value;\r\n            el.style['o' + property] = value;\r\n        }\r\n        else {\r\n            el.style[property] = value;\r\n        }\r\n    };\r\n    lgQuery.prototype._getFirstEl = function () {\r\n        if (this.selector && this.selector.length !== undefined) {\r\n            return this.selector[0];\r\n        }\r\n        else {\r\n            return this.selector;\r\n        }\r\n    };\r\n    lgQuery.prototype.isEventMatched = function (event, eventName) {\r\n        var eventNamespace = eventName.split('.');\r\n        return event\r\n            .split('.')\r\n            .filter(function (e) { return e; })\r\n            .every(function (e) {\r\n            return eventNamespace.indexOf(e) !== -1;\r\n        });\r\n    };\r\n    lgQuery.prototype.attr = function (attr, value) {\r\n        if (value === undefined) {\r\n            if (!this.firstElement) {\r\n                return '';\r\n            }\r\n            return this.firstElement.getAttribute(attr);\r\n        }\r\n        this._each(function (el) {\r\n            el.setAttribute(attr, value);\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.find = function (selector) {\r\n        return $LG(this._getSelector(selector, this.selector));\r\n    };\r\n    lgQuery.prototype.first = function () {\r\n        if (this.selector && this.selector.length !== undefined) {\r\n            return $LG(this.selector[0]);\r\n        }\r\n        else {\r\n            return $LG(this.selector);\r\n        }\r\n    };\r\n    lgQuery.prototype.eq = function (index) {\r\n        return $LG(this.selector[index]);\r\n    };\r\n    lgQuery.prototype.parent = function () {\r\n        return $LG(this.selector.parentElement);\r\n    };\r\n    lgQuery.prototype.get = function () {\r\n        return this._getFirstEl();\r\n    };\r\n    lgQuery.prototype.removeAttr = function (attributes) {\r\n        var attrs = attributes.split(' ');\r\n        this._each(function (el) {\r\n            attrs.forEach(function (attr) { return el.removeAttribute(attr); });\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.wrap = function (className) {\r\n        if (!this.firstElement) {\r\n            return this;\r\n        }\r\n        var wrapper = document.createElement('div');\r\n        wrapper.className = className;\r\n        this.firstElement.parentNode.insertBefore(wrapper, this.firstElement);\r\n        this.firstElement.parentNode.removeChild(this.firstElement);\r\n        wrapper.appendChild(this.firstElement);\r\n        return this;\r\n    };\r\n    lgQuery.prototype.addClass = function (classNames) {\r\n        if (classNames === void 0) { classNames = ''; }\r\n        this._each(function (el) {\r\n            // IE doesn't support multiple arguments\r\n            classNames.split(' ').forEach(function (className) {\r\n                if (className) {\r\n                    el.classList.add(className);\r\n                }\r\n            });\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.removeClass = function (classNames) {\r\n        this._each(function (el) {\r\n            // IE doesn't support multiple arguments\r\n            classNames.split(' ').forEach(function (className) {\r\n                if (className) {\r\n                    el.classList.remove(className);\r\n                }\r\n            });\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.hasClass = function (className) {\r\n        if (!this.firstElement) {\r\n            return false;\r\n        }\r\n        return this.firstElement.classList.contains(className);\r\n    };\r\n    lgQuery.prototype.hasAttribute = function (attribute) {\r\n        if (!this.firstElement) {\r\n            return false;\r\n        }\r\n        return this.firstElement.hasAttribute(attribute);\r\n    };\r\n    lgQuery.prototype.toggleClass = function (className) {\r\n        if (!this.firstElement) {\r\n            return this;\r\n        }\r\n        if (this.hasClass(className)) {\r\n            this.removeClass(className);\r\n        }\r\n        else {\r\n            this.addClass(className);\r\n        }\r\n        return this;\r\n    };\r\n    lgQuery.prototype.css = function (property, value) {\r\n        var _this = this;\r\n        this._each(function (el) {\r\n            _this._setCssVendorPrefix(el, property, value);\r\n        });\r\n        return this;\r\n    };\r\n    // Need to pass separate namespaces for separate elements\r\n    lgQuery.prototype.on = function (events, listener) {\r\n        var _this = this;\r\n        if (!this.selector) {\r\n            return this;\r\n        }\r\n        events.split(' ').forEach(function (event) {\r\n            if (!Array.isArray(lgQuery.eventListeners[event])) {\r\n                lgQuery.eventListeners[event] = [];\r\n            }\r\n            lgQuery.eventListeners[event].push(listener);\r\n            _this.selector.addEventListener(event.split('.')[0], listener);\r\n        });\r\n        return this;\r\n    };\r\n    // @todo - test this\r\n    lgQuery.prototype.once = function (event, listener) {\r\n        var _this = this;\r\n        this.on(event, function () {\r\n            _this.off(event);\r\n            listener(event);\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.off = function (event) {\r\n        var _this = this;\r\n        if (!this.selector) {\r\n            return this;\r\n        }\r\n        Object.keys(lgQuery.eventListeners).forEach(function (eventName) {\r\n            if (_this.isEventMatched(event, eventName)) {\r\n                lgQuery.eventListeners[eventName].forEach(function (listener) {\r\n                    _this.selector.removeEventListener(eventName.split('.')[0], listener);\r\n                });\r\n                lgQuery.eventListeners[eventName] = [];\r\n            }\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.trigger = function (event, detail) {\r\n        if (!this.firstElement) {\r\n            return this;\r\n        }\r\n        var customEvent = new CustomEvent(event.split('.')[0], {\r\n            detail: detail || null,\r\n        });\r\n        this.firstElement.dispatchEvent(customEvent);\r\n        return this;\r\n    };\r\n    // Does not support IE\r\n    lgQuery.prototype.load = function (url) {\r\n        var _this = this;\r\n        fetch(url)\r\n            .then(function (res) { return res.text(); })\r\n            .then(function (html) {\r\n            _this.selector.innerHTML = html;\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.html = function (html) {\r\n        if (html === undefined) {\r\n            if (!this.firstElement) {\r\n                return '';\r\n            }\r\n            return this.firstElement.innerHTML;\r\n        }\r\n        this._each(function (el) {\r\n            el.innerHTML = html;\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.append = function (html) {\r\n        this._each(function (el) {\r\n            if (typeof html === 'string') {\r\n                el.insertAdjacentHTML('beforeend', html);\r\n            }\r\n            else {\r\n                el.appendChild(html);\r\n            }\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.prepend = function (html) {\r\n        this._each(function (el) {\r\n            el.insertAdjacentHTML('afterbegin', html);\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.remove = function () {\r\n        this._each(function (el) {\r\n            el.parentNode.removeChild(el);\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.empty = function () {\r\n        this._each(function (el) {\r\n            el.innerHTML = '';\r\n        });\r\n        return this;\r\n    };\r\n    lgQuery.prototype.scrollTop = function (scrollTop) {\r\n        if (scrollTop !== undefined) {\r\n            document.body.scrollTop = scrollTop;\r\n            document.documentElement.scrollTop = scrollTop;\r\n            return this;\r\n        }\r\n        else {\r\n            return (window.pageYOffset ||\r\n                document.documentElement.scrollTop ||\r\n                document.body.scrollTop ||\r\n                0);\r\n        }\r\n    };\r\n    lgQuery.prototype.scrollLeft = function (scrollLeft) {\r\n        if (scrollLeft !== undefined) {\r\n            document.body.scrollLeft = scrollLeft;\r\n            document.documentElement.scrollLeft = scrollLeft;\r\n            return this;\r\n        }\r\n        else {\r\n            return (window.pageXOffset ||\r\n                document.documentElement.scrollLeft ||\r\n                document.body.scrollLeft ||\r\n                0);\r\n        }\r\n    };\r\n    lgQuery.prototype.offset = function () {\r\n        if (!this.firstElement) {\r\n            return {\r\n                left: 0,\r\n                top: 0,\r\n            };\r\n        }\r\n        var rect = this.firstElement.getBoundingClientRect();\r\n        var bodyMarginLeft = $LG('body').style().marginLeft;\r\n        // Minus body margin - https://stackoverflow.com/questions/30711548/is-getboundingclientrect-left-returning-a-wrong-value\r\n        return {\r\n            left: rect.left - parseFloat(bodyMarginLeft) + this.scrollLeft(),\r\n            top: rect.top + this.scrollTop(),\r\n        };\r\n    };\r\n    lgQuery.prototype.style = function () {\r\n        if (!this.firstElement) {\r\n            return {};\r\n        }\r\n        return (this.firstElement.currentStyle ||\r\n            window.getComputedStyle(this.firstElement));\r\n    };\r\n    // Width without padding and border even if box-sizing is used.\r\n    lgQuery.prototype.width = function () {\r\n        var style = this.style();\r\n        return (this.firstElement.clientWidth -\r\n            parseFloat(style.paddingLeft) -\r\n            parseFloat(style.paddingRight));\r\n    };\r\n    // Height without padding and border even if box-sizing is used.\r\n    lgQuery.prototype.height = function () {\r\n        var style = this.style();\r\n        return (this.firstElement.clientHeight -\r\n            parseFloat(style.paddingTop) -\r\n            parseFloat(style.paddingBottom));\r\n    };\r\n    lgQuery.eventListeners = {};\r\n    return lgQuery;\r\n}());\r\nfunction $LG(selector) {\r\n    initLgPolyfills();\r\n    return new lgQuery(selector);\r\n}\n\nvar defaultDynamicOptions = [\r\n    'src',\r\n    'sources',\r\n    'subHtml',\r\n    'subHtmlUrl',\r\n    'html',\r\n    'video',\r\n    'poster',\r\n    'slideName',\r\n    'responsive',\r\n    'srcset',\r\n    'sizes',\r\n    'iframe',\r\n    'downloadUrl',\r\n    'download',\r\n    'width',\r\n    'facebookShareUrl',\r\n    'tweetText',\r\n    'iframeTitle',\r\n    'twitterShareUrl',\r\n    'pinterestShareUrl',\r\n    'pinterestText',\r\n    'fbHtml',\r\n    'disqusIdentifier',\r\n    'disqusUrl',\r\n];\r\n// Convert html data-attribute to camalcase\r\nfunction convertToData(attr) {\r\n    // FInd a way for lgsize\r\n    if (attr === 'href') {\r\n        return 'src';\r\n    }\r\n    attr = attr.replace('data-', '');\r\n    attr = attr.charAt(0).toLowerCase() + attr.slice(1);\r\n    attr = attr.replace(/-([a-z])/g, function (g) { return g[1].toUpperCase(); });\r\n    return attr;\r\n}\r\nvar utils = {\r\n    /**\r\n     * get possible width and height from the lgSize attribute. Used for ZoomFromOrigin option\r\n     */\r\n    getSize: function (el, container, spacing, defaultLgSize) {\r\n        if (spacing === void 0) { spacing = 0; }\r\n        var LGel = $LG(el);\r\n        var lgSize = LGel.attr('data-lg-size') || defaultLgSize;\r\n        if (!lgSize) {\r\n            return;\r\n        }\r\n        var isResponsiveSizes = lgSize.split(',');\r\n        // if at-least two viewport sizes are available\r\n        if (isResponsiveSizes[1]) {\r\n            var wWidth = window.innerWidth;\r\n            for (var i = 0; i < isResponsiveSizes.length; i++) {\r\n                var size_1 = isResponsiveSizes[i];\r\n                var responsiveWidth = parseInt(size_1.split('-')[2], 10);\r\n                if (responsiveWidth > wWidth) {\r\n                    lgSize = size_1;\r\n                    break;\r\n                }\r\n                // take last item as last option\r\n                if (i === isResponsiveSizes.length - 1) {\r\n                    lgSize = size_1;\r\n                }\r\n            }\r\n        }\r\n        var size = lgSize.split('-');\r\n        var width = parseInt(size[0], 10);\r\n        var height = parseInt(size[1], 10);\r\n        var cWidth = container.width();\r\n        var cHeight = container.height() - spacing;\r\n        var maxWidth = Math.min(cWidth, width);\r\n        var maxHeight = Math.min(cHeight, height);\r\n        var ratio = Math.min(maxWidth / width, maxHeight / height);\r\n        return { width: width * ratio, height: height * ratio };\r\n    },\r\n    /**\r\n     * @desc Get transform value based on the imageSize. Used for ZoomFromOrigin option\r\n     * @param {jQuery Element}\r\n     * @returns {String} Transform CSS string\r\n     */\r\n    getTransform: function (el, container, top, bottom, imageSize) {\r\n        if (!imageSize) {\r\n            return;\r\n        }\r\n        var LGel = $LG(el).find('img').first();\r\n        if (!LGel.get()) {\r\n            return;\r\n        }\r\n        var containerRect = container.get().getBoundingClientRect();\r\n        var wWidth = containerRect.width;\r\n        // using innerWidth to include mobile safari bottom bar\r\n        var wHeight = container.height() - (top + bottom);\r\n        var elWidth = LGel.width();\r\n        var elHeight = LGel.height();\r\n        var elStyle = LGel.style();\r\n        var x = (wWidth - elWidth) / 2 -\r\n            LGel.offset().left +\r\n            (parseFloat(elStyle.paddingLeft) || 0) +\r\n            (parseFloat(elStyle.borderLeft) || 0) +\r\n            $LG(window).scrollLeft() +\r\n            containerRect.left;\r\n        var y = (wHeight - elHeight) / 2 -\r\n            LGel.offset().top +\r\n            (parseFloat(elStyle.paddingTop) || 0) +\r\n            (parseFloat(elStyle.borderTop) || 0) +\r\n            $LG(window).scrollTop() +\r\n            top;\r\n        var scX = elWidth / imageSize.width;\r\n        var scY = elHeight / imageSize.height;\r\n        var transform = 'translate3d(' +\r\n            (x *= -1) +\r\n            'px, ' +\r\n            (y *= -1) +\r\n            'px, 0) scale3d(' +\r\n            scX +\r\n            ', ' +\r\n            scY +\r\n            ', 1)';\r\n        return transform;\r\n    },\r\n    getIframeMarkup: function (iframeWidth, iframeHeight, iframeMaxWidth, iframeMaxHeight, src, iframeTitle) {\r\n        var title = iframeTitle ? 'title=\"' + iframeTitle + '\"' : '';\r\n        return \"<div class=\\\"lg-video-cont lg-has-iframe\\\" style=\\\"width:\" + iframeWidth + \"; max-width:\" + iframeMaxWidth + \"; height: \" + iframeHeight + \"; max-height:\" + iframeMaxHeight + \"\\\">\\n                    <iframe class=\\\"lg-object\\\" frameborder=\\\"0\\\" \" + title + \" src=\\\"\" + src + \"\\\"  allowfullscreen=\\\"true\\\"></iframe>\\n                </div>\";\r\n    },\r\n    getImgMarkup: function (index, src, altAttr, srcset, sizes, sources) {\r\n        var srcsetAttr = srcset ? \"srcset=\\\"\" + srcset + \"\\\"\" : '';\r\n        var sizesAttr = sizes ? \"sizes=\\\"\" + sizes + \"\\\"\" : '';\r\n        var imgMarkup = \"<img \" + altAttr + \" \" + srcsetAttr + \"  \" + sizesAttr + \" class=\\\"lg-object lg-image\\\" data-index=\\\"\" + index + \"\\\" src=\\\"\" + src + \"\\\" />\";\r\n        var sourceTag = '';\r\n        if (sources) {\r\n            var sourceObj = typeof sources === 'string' ? JSON.parse(sources) : sources;\r\n            sourceTag = sourceObj.map(function (source) {\r\n                var attrs = '';\r\n                Object.keys(source).forEach(function (key) {\r\n                    // Do not remove the first space as it is required to separate the attributes\r\n                    attrs += \" \" + key + \"=\\\"\" + source[key] + \"\\\"\";\r\n                });\r\n                return \"<source \" + attrs + \"></source>\";\r\n            });\r\n        }\r\n        return \"\" + sourceTag + imgMarkup;\r\n    },\r\n    // Get src from responsive src\r\n    getResponsiveSrc: function (srcItms) {\r\n        var rsWidth = [];\r\n        var rsSrc = [];\r\n        var src = '';\r\n        for (var i = 0; i < srcItms.length; i++) {\r\n            var _src = srcItms[i].split(' ');\r\n            // Manage empty space\r\n            if (_src[0] === '') {\r\n                _src.splice(0, 1);\r\n            }\r\n            rsSrc.push(_src[0]);\r\n            rsWidth.push(_src[1]);\r\n        }\r\n        var wWidth = window.innerWidth;\r\n        for (var j = 0; j < rsWidth.length; j++) {\r\n            if (parseInt(rsWidth[j], 10) > wWidth) {\r\n                src = rsSrc[j];\r\n                break;\r\n            }\r\n        }\r\n        return src;\r\n    },\r\n    isImageLoaded: function (img) {\r\n        if (!img)\r\n            return false;\r\n        // During the onload event, IE correctly identifies any images that\r\n        // weren’t downloaded as not complete. Others should too. Gecko-based\r\n        // browsers act like NS4 in that they report this incorrectly.\r\n        if (!img.complete) {\r\n            return false;\r\n        }\r\n        // However, they do have two very useful properties: naturalWidth and\r\n        // naturalHeight. These give the true size of the image. If it failed\r\n        // to load, either of these should be zero.\r\n        if (img.naturalWidth === 0) {\r\n            return false;\r\n        }\r\n        // No other way of checking: assume it’s ok.\r\n        return true;\r\n    },\r\n    getVideoPosterMarkup: function (_poster, dummyImg, videoContStyle, playVideoString, _isVideo) {\r\n        var videoClass = '';\r\n        if (_isVideo && _isVideo.youtube) {\r\n            videoClass = 'lg-has-youtube';\r\n        }\r\n        else if (_isVideo && _isVideo.vimeo) {\r\n            videoClass = 'lg-has-vimeo';\r\n        }\r\n        else {\r\n            videoClass = 'lg-has-html5';\r\n        }\r\n        return \"<div class=\\\"lg-video-cont \" + videoClass + \"\\\" style=\\\"\" + videoContStyle + \"\\\">\\n                <div class=\\\"lg-video-play-button\\\">\\n                <svg\\n                    viewBox=\\\"0 0 20 20\\\"\\n                    preserveAspectRatio=\\\"xMidYMid\\\"\\n                    focusable=\\\"false\\\"\\n                    aria-labelledby=\\\"\" + playVideoString + \"\\\"\\n                    role=\\\"img\\\"\\n                    class=\\\"lg-video-play-icon\\\"\\n                >\\n                    <title>\" + playVideoString + \"</title>\\n                    <polygon class=\\\"lg-video-play-icon-inner\\\" points=\\\"1,0 20,10 1,20\\\"></polygon>\\n                </svg>\\n                <svg class=\\\"lg-video-play-icon-bg\\\" viewBox=\\\"0 0 50 50\\\" focusable=\\\"false\\\">\\n                    <circle cx=\\\"50%\\\" cy=\\\"50%\\\" r=\\\"20\\\"></circle></svg>\\n                <svg class=\\\"lg-video-play-icon-circle\\\" viewBox=\\\"0 0 50 50\\\" focusable=\\\"false\\\">\\n                    <circle cx=\\\"50%\\\" cy=\\\"50%\\\" r=\\\"20\\\"></circle>\\n                </svg>\\n            </div>\\n            \" + (dummyImg || '') + \"\\n            <img class=\\\"lg-object lg-video-poster\\\" src=\\\"\" + _poster + \"\\\" />\\n        </div>\";\r\n    },\r\n    getFocusableElements: function (container) {\r\n        var elements = container.querySelectorAll('a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type=\"text\"]:not([disabled]), input[type=\"radio\"]:not([disabled]), input[type=\"checkbox\"]:not([disabled]), select:not([disabled])');\r\n        var visibleElements = [].filter.call(elements, function (element) {\r\n            var style = window.getComputedStyle(element);\r\n            return style.display !== 'none' && style.visibility !== 'hidden';\r\n        });\r\n        return visibleElements;\r\n    },\r\n    /**\r\n     * @desc Create dynamic elements array from gallery items when dynamic option is false\r\n     * It helps to avoid frequent DOM interaction\r\n     * and avoid multiple checks for dynamic elments\r\n     *\r\n     * @returns {Array} dynamicEl\r\n     */\r\n    getDynamicOptions: function (items, extraProps, getCaptionFromTitleOrAlt, exThumbImage) {\r\n        var dynamicElements = [];\r\n        var availableDynamicOptions = __spreadArrays(defaultDynamicOptions, extraProps);\r\n        [].forEach.call(items, function (item) {\r\n            var dynamicEl = {};\r\n            for (var i = 0; i < item.attributes.length; i++) {\r\n                var attr = item.attributes[i];\r\n                if (attr.specified) {\r\n                    var dynamicAttr = convertToData(attr.name);\r\n                    var label = '';\r\n                    if (availableDynamicOptions.indexOf(dynamicAttr) > -1) {\r\n                        label = dynamicAttr;\r\n                    }\r\n                    if (label) {\r\n                        dynamicEl[label] = attr.value;\r\n                    }\r\n                }\r\n            }\r\n            var currentItem = $LG(item);\r\n            var alt = currentItem.find('img').first().attr('alt');\r\n            var title = currentItem.attr('title');\r\n            var thumb = exThumbImage\r\n                ? currentItem.attr(exThumbImage)\r\n                : currentItem.find('img').first().attr('src');\r\n            dynamicEl.thumb = thumb;\r\n            if (getCaptionFromTitleOrAlt && !dynamicEl.subHtml) {\r\n                dynamicEl.subHtml = title || alt || '';\r\n            }\r\n            dynamicEl.alt = alt || title || '';\r\n            dynamicElements.push(dynamicEl);\r\n        });\r\n        return dynamicElements;\r\n    },\r\n    isMobile: function () {\r\n        return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);\r\n    },\r\n    /**\r\n     * @desc Check the given src is video\r\n     * @param {String} src\r\n     * @return {Object} video type\r\n     * Ex:{ youtube  :  [\"//www.youtube.com/watch?v=c0asJgSyxcY\", \"c0asJgSyxcY\"] }\r\n     *\r\n     * @todo - this information can be moved to dynamicEl to avoid frequent calls\r\n     */\r\n    isVideo: function (src, isHTML5VIdeo, index) {\r\n        if (!src) {\r\n            if (isHTML5VIdeo) {\r\n                return {\r\n                    html5: true,\r\n                };\r\n            }\r\n            else {\r\n                console.error('lightGallery :- data-src is not provided on slide item ' +\r\n                    (index + 1) +\r\n                    '. Please make sure the selector property is properly configured. More info - https://www.lightgalleryjs.com/demos/html-markup/');\r\n                return;\r\n            }\r\n        }\r\n        var youtube = src.match(/\\/\\/(?:www\\.)?youtu(?:\\.be|be\\.com|be-nocookie\\.com)\\/(?:watch\\?v=|embed\\/)?([a-z0-9\\-\\_\\%]+)([\\&|?][\\S]*)*/i);\r\n        var vimeo = src.match(/\\/\\/(?:www\\.)?(?:player\\.)?vimeo.com\\/(?:video\\/)?([0-9a-z\\-_]+)(.*)?/i);\r\n        var wistia = src.match(/https?:\\/\\/(.+)?(wistia\\.com|wi\\.st)\\/(medias|embed)\\/([0-9a-z\\-_]+)(.*)/);\r\n        if (youtube) {\r\n            return {\r\n                youtube: youtube,\r\n            };\r\n        }\r\n        else if (vimeo) {\r\n            return {\r\n                vimeo: vimeo,\r\n            };\r\n        }\r\n        else if (wistia) {\r\n            return {\r\n                wistia: wistia,\r\n            };\r\n        }\r\n    },\r\n};\n\n// @ref - https://stackoverflow.com/questions/3971841/how-to-resize-images-proportionally-keeping-the-aspect-ratio\r\n// @ref - https://2ality.com/2017/04/setting-up-multi-platform-packages.html\r\n// Unique id for each gallery\r\nvar lgId = 0;\r\nvar LightGallery = /** @class */ (function () {\r\n    function LightGallery(element, options) {\r\n        this.lgOpened = false;\r\n        this.index = 0;\r\n        // lightGallery modules\r\n        this.plugins = [];\r\n        // false when lightGallery load first slide content;\r\n        this.lGalleryOn = false;\r\n        // True when a slide animation is in progress\r\n        this.lgBusy = false;\r\n        this.currentItemsInDom = [];\r\n        // Scroll top value before lightGallery is opened\r\n        this.prevScrollTop = 0;\r\n        this.bodyPaddingRight = 0;\r\n        this.isDummyImageRemoved = false;\r\n        this.dragOrSwipeEnabled = false;\r\n        this.mediaContainerPosition = {\r\n            top: 0,\r\n            bottom: 0,\r\n        };\r\n        if (!element) {\r\n            return this;\r\n        }\r\n        lgId++;\r\n        this.lgId = lgId;\r\n        this.el = element;\r\n        this.LGel = $LG(element);\r\n        this.generateSettings(options);\r\n        this.buildModules();\r\n        // When using dynamic mode, ensure dynamicEl is an array\r\n        if (this.settings.dynamic &&\r\n            this.settings.dynamicEl !== undefined &&\r\n            !Array.isArray(this.settings.dynamicEl)) {\r\n            throw 'When using dynamic mode, you must also define dynamicEl as an Array.';\r\n        }\r\n        this.galleryItems = this.getItems();\r\n        this.normalizeSettings();\r\n        // Gallery items\r\n        this.init();\r\n        this.validateLicense();\r\n        return this;\r\n    }\r\n    LightGallery.prototype.generateSettings = function (options) {\r\n        // lightGallery settings\r\n        this.settings = __assign(__assign({}, lightGalleryCoreSettings), options);\r\n        if (this.settings.isMobile &&\r\n            typeof this.settings.isMobile === 'function'\r\n            ? this.settings.isMobile()\r\n            : utils.isMobile()) {\r\n            var mobileSettings = __assign(__assign({}, this.settings.mobileSettings), this.settings.mobileSettings);\r\n            this.settings = __assign(__assign({}, this.settings), mobileSettings);\r\n        }\r\n    };\r\n    LightGallery.prototype.normalizeSettings = function () {\r\n        if (this.settings.slideEndAnimation) {\r\n            this.settings.hideControlOnEnd = false;\r\n        }\r\n        if (!this.settings.closable) {\r\n            this.settings.swipeToClose = false;\r\n        }\r\n        // And reset it on close to get the correct value next time\r\n        this.zoomFromOrigin = this.settings.zoomFromOrigin;\r\n        // At the moment, Zoom from image doesn't support dynamic options\r\n        // @todo add zoomFromOrigin support for dynamic images\r\n        if (this.settings.dynamic) {\r\n            this.zoomFromOrigin = false;\r\n        }\r\n        if (!this.settings.container) {\r\n            this.settings.container = document.body;\r\n        }\r\n        // settings.preload should not be grater than $item.length\r\n        this.settings.preload = Math.min(this.settings.preload, this.galleryItems.length);\r\n    };\r\n    LightGallery.prototype.init = function () {\r\n        var _this = this;\r\n        this.addSlideVideoInfo(this.galleryItems);\r\n        this.buildStructure();\r\n        this.LGel.trigger(lGEvents.init, {\r\n            instance: this,\r\n        });\r\n        if (this.settings.keyPress) {\r\n            this.keyPress();\r\n        }\r\n        setTimeout(function () {\r\n            _this.enableDrag();\r\n            _this.enableSwipe();\r\n            _this.triggerPosterClick();\r\n        }, 50);\r\n        this.arrow();\r\n        if (this.settings.mousewheel) {\r\n            this.mousewheel();\r\n        }\r\n        if (!this.settings.dynamic) {\r\n            this.openGalleryOnItemClick();\r\n        }\r\n    };\r\n    LightGallery.prototype.openGalleryOnItemClick = function () {\r\n        var _this = this;\r\n        var _loop_1 = function (index) {\r\n            var element = this_1.items[index];\r\n            var $element = $LG(element);\r\n            // Using different namespace for click because click event should not unbind if selector is same object('this')\r\n            // @todo manage all event listners - should have namespace that represent element\r\n            var uuid = lgQuery.generateUUID();\r\n            $element\r\n                .attr('data-lg-id', uuid)\r\n                .on(\"click.lgcustom-item-\" + uuid, function (e) {\r\n                e.preventDefault();\r\n                var currentItemIndex = _this.settings.index || index;\r\n                _this.openGallery(currentItemIndex, element);\r\n            });\r\n        };\r\n        var this_1 = this;\r\n        // Using for loop instead of using bubbling as the items can be any html element.\r\n        for (var index = 0; index < this.items.length; index++) {\r\n            _loop_1(index);\r\n        }\r\n    };\r\n    /**\r\n     * Module constructor\r\n     * Modules are build incrementally.\r\n     * Gallery should be opened only once all the modules are initialized.\r\n     * use moduleBuildTimeout to make sure this\r\n     */\r\n    LightGallery.prototype.buildModules = function () {\r\n        var _this = this;\r\n        this.settings.plugins.forEach(function (plugin) {\r\n            _this.plugins.push(new plugin(_this, $LG));\r\n        });\r\n    };\r\n    LightGallery.prototype.validateLicense = function () {\r\n        if (!this.settings.licenseKey) {\r\n            console.error('Please provide a valid license key');\r\n        }\r\n        else if (this.settings.licenseKey === '0000-0000-000-0000') {\r\n            console.warn(\"lightGallery: \" + this.settings.licenseKey + \" license key is not valid for production use\");\r\n        }\r\n    };\r\n    LightGallery.prototype.getSlideItem = function (index) {\r\n        return $LG(this.getSlideItemId(index));\r\n    };\r\n    LightGallery.prototype.getSlideItemId = function (index) {\r\n        return \"#lg-item-\" + this.lgId + \"-\" + index;\r\n    };\r\n    LightGallery.prototype.getIdName = function (id) {\r\n        return id + \"-\" + this.lgId;\r\n    };\r\n    LightGallery.prototype.getElementById = function (id) {\r\n        return $LG(\"#\" + this.getIdName(id));\r\n    };\r\n    LightGallery.prototype.manageSingleSlideClassName = function () {\r\n        if (this.galleryItems.length < 2) {\r\n            this.outer.addClass('lg-single-item');\r\n        }\r\n        else {\r\n            this.outer.removeClass('lg-single-item');\r\n        }\r\n    };\r\n    LightGallery.prototype.buildStructure = function () {\r\n        var _this = this;\r\n        var container = this.$container && this.$container.get();\r\n        if (container) {\r\n            return;\r\n        }\r\n        var controls = '';\r\n        var subHtmlCont = '';\r\n        // Create controls\r\n        if (this.settings.controls) {\r\n            controls = \"<button type=\\\"button\\\" id=\\\"\" + this.getIdName('lg-prev') + \"\\\" aria-label=\\\"\" + this.settings.strings['previousSlide'] + \"\\\" class=\\\"lg-prev lg-icon\\\"> \" + this.settings.prevHtml + \" </button>\\n                <button type=\\\"button\\\" id=\\\"\" + this.getIdName('lg-next') + \"\\\" aria-label=\\\"\" + this.settings.strings['nextSlide'] + \"\\\" class=\\\"lg-next lg-icon\\\"> \" + this.settings.nextHtml + \" </button>\";\r\n        }\r\n        if (this.settings.appendSubHtmlTo !== '.lg-item') {\r\n            subHtmlCont =\r\n                '<div class=\"lg-sub-html\" role=\"status\" aria-live=\"polite\"></div>';\r\n        }\r\n        var addClasses = '';\r\n        if (this.settings.allowMediaOverlap) {\r\n            // Do not remove space before last single quote\r\n            addClasses += 'lg-media-overlap ';\r\n        }\r\n        var ariaLabelledby = this.settings.ariaLabelledby\r\n            ? 'aria-labelledby=\"' + this.settings.ariaLabelledby + '\"'\r\n            : '';\r\n        var ariaDescribedby = this.settings.ariaDescribedby\r\n            ? 'aria-describedby=\"' + this.settings.ariaDescribedby + '\"'\r\n            : '';\r\n        var containerClassName = \"lg-container \" + this.settings.addClass + \" \" + (document.body !== this.settings.container ? 'lg-inline' : '');\r\n        var closeIcon = this.settings.closable && this.settings.showCloseIcon\r\n            ? \"<button type=\\\"button\\\" aria-label=\\\"\" + this.settings.strings['closeGallery'] + \"\\\" id=\\\"\" + this.getIdName('lg-close') + \"\\\" class=\\\"lg-close lg-icon\\\"></button>\"\r\n            : '';\r\n        var maximizeIcon = this.settings.showMaximizeIcon\r\n            ? \"<button type=\\\"button\\\" aria-label=\\\"\" + this.settings.strings['toggleMaximize'] + \"\\\" id=\\\"\" + this.getIdName('lg-maximize') + \"\\\" class=\\\"lg-maximize lg-icon\\\"></button>\"\r\n            : '';\r\n        var template = \"\\n        <div class=\\\"\" + containerClassName + \"\\\" id=\\\"\" + this.getIdName('lg-container') + \"\\\" tabindex=\\\"-1\\\" aria-modal=\\\"true\\\" \" + ariaLabelledby + \" \" + ariaDescribedby + \" role=\\\"dialog\\\"\\n        >\\n            <div id=\\\"\" + this.getIdName('lg-backdrop') + \"\\\" class=\\\"lg-backdrop\\\"></div>\\n\\n            <div id=\\\"\" + this.getIdName('lg-outer') + \"\\\" class=\\\"lg-outer lg-use-css3 lg-css3 lg-hide-items \" + addClasses + \" \\\">\\n\\n              <div id=\\\"\" + this.getIdName('lg-content') + \"\\\" class=\\\"lg-content\\\">\\n                <div id=\\\"\" + this.getIdName('lg-inner') + \"\\\" class=\\\"lg-inner\\\">\\n                </div>\\n                \" + controls + \"\\n              </div>\\n                <div id=\\\"\" + this.getIdName('lg-toolbar') + \"\\\" class=\\\"lg-toolbar lg-group\\\">\\n                    \" + maximizeIcon + \"\\n                    \" + closeIcon + \"\\n                    </div>\\n                    \" + (this.settings.appendSubHtmlTo === '.lg-outer'\r\n            ? subHtmlCont\r\n            : '') + \"\\n                <div id=\\\"\" + this.getIdName('lg-components') + \"\\\" class=\\\"lg-components\\\">\\n                    \" + (this.settings.appendSubHtmlTo === '.lg-sub-html'\r\n            ? subHtmlCont\r\n            : '') + \"\\n                </div>\\n            </div>\\n        </div>\\n        \";\r\n        $LG(this.settings.container).append(template);\r\n        if (document.body !== this.settings.container) {\r\n            $LG(this.settings.container).css('position', 'relative');\r\n        }\r\n        this.outer = this.getElementById('lg-outer');\r\n        this.$lgComponents = this.getElementById('lg-components');\r\n        this.$backdrop = this.getElementById('lg-backdrop');\r\n        this.$container = this.getElementById('lg-container');\r\n        this.$inner = this.getElementById('lg-inner');\r\n        this.$content = this.getElementById('lg-content');\r\n        this.$toolbar = this.getElementById('lg-toolbar');\r\n        this.$backdrop.css('transition-duration', this.settings.backdropDuration + 'ms');\r\n        var outerClassNames = this.settings.mode + \" \";\r\n        this.manageSingleSlideClassName();\r\n        if (this.settings.enableDrag) {\r\n            outerClassNames += 'lg-grab ';\r\n        }\r\n        this.outer.addClass(outerClassNames);\r\n        this.$inner.css('transition-timing-function', this.settings.easing);\r\n        this.$inner.css('transition-duration', this.settings.speed + 'ms');\r\n        if (this.settings.download) {\r\n            this.$toolbar.append(\"<a id=\\\"\" + this.getIdName('lg-download') + \"\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" aria-label=\\\"\" + this.settings.strings['download'] + \"\\\" download class=\\\"lg-download lg-icon\\\"></a>\");\r\n        }\r\n        this.counter();\r\n        $LG(window).on(\"resize.lg.global\" + this.lgId + \" orientationchange.lg.global\" + this.lgId, function () {\r\n            _this.refreshOnResize();\r\n        });\r\n        this.hideBars();\r\n        this.manageCloseGallery();\r\n        this.toggleMaximize();\r\n        this.initModules();\r\n    };\r\n    LightGallery.prototype.refreshOnResize = function () {\r\n        if (this.lgOpened) {\r\n            var currentGalleryItem = this.galleryItems[this.index];\r\n            var __slideVideoInfo = currentGalleryItem.__slideVideoInfo;\r\n            this.mediaContainerPosition = this.getMediaContainerPosition();\r\n            var _a = this.mediaContainerPosition, top_1 = _a.top, bottom = _a.bottom;\r\n            this.currentImageSize = utils.getSize(this.items[this.index], this.outer, top_1 + bottom, __slideVideoInfo && this.settings.videoMaxSize);\r\n            if (__slideVideoInfo) {\r\n                this.resizeVideoSlide(this.index, this.currentImageSize);\r\n            }\r\n            if (this.zoomFromOrigin && !this.isDummyImageRemoved) {\r\n                var imgStyle = this.getDummyImgStyles(this.currentImageSize);\r\n                this.outer\r\n                    .find('.lg-current .lg-dummy-img')\r\n                    .first()\r\n                    .attr('style', imgStyle);\r\n            }\r\n            this.LGel.trigger(lGEvents.containerResize);\r\n        }\r\n    };\r\n    LightGallery.prototype.resizeVideoSlide = function (index, imageSize) {\r\n        var lgVideoStyle = this.getVideoContStyle(imageSize);\r\n        var currentSlide = this.getSlideItem(index);\r\n        currentSlide.find('.lg-video-cont').attr('style', lgVideoStyle);\r\n    };\r\n    /**\r\n     * Update slides dynamically.\r\n     * Add, edit or delete slides dynamically when lightGallery is opened.\r\n     * Modify the current gallery items and pass it via updateSlides method\r\n     * @note\r\n     * - Do not mutate existing lightGallery items directly.\r\n     * - Always pass new list of gallery items\r\n     * - You need to take care of thumbnails outside the gallery if any\r\n     * - user this method only if you want to update slides when the gallery is opened. Otherwise, use `refresh()` method.\r\n     * @param items Gallery items\r\n     * @param index After the update operation, which slide gallery should navigate to\r\n     * @category lGPublicMethods\r\n     * @example\r\n     * const plugin = lightGallery();\r\n     *\r\n     * // Adding slides dynamically\r\n     * let galleryItems = [\r\n     * // Access existing lightGallery items\r\n     * // galleryItems are automatically generated internally from the gallery HTML markup\r\n     * // or directly from galleryItems when dynamic gallery is used\r\n     *   ...plugin.galleryItems,\r\n     *     ...[\r\n     *       {\r\n     *         src: 'img/img-1.png',\r\n     *           thumb: 'img/thumb1.png',\r\n     *         },\r\n     *     ],\r\n     *   ];\r\n     *   plugin.updateSlides(\r\n     *     galleryItems,\r\n     *     plugin.index,\r\n     *   );\r\n     *\r\n     *\r\n     * // Remove slides dynamically\r\n     * galleryItems = JSON.parse(\r\n     *   JSON.stringify(updateSlideInstance.galleryItems),\r\n     * );\r\n     * galleryItems.shift();\r\n     * updateSlideInstance.updateSlides(galleryItems, 1);\r\n     * @see <a href=\"/demos/update-slides/\">Demo</a>\r\n     */\r\n    LightGallery.prototype.updateSlides = function (items, index) {\r\n        if (this.index > items.length - 1) {\r\n            this.index = items.length - 1;\r\n        }\r\n        if (items.length === 1) {\r\n            this.index = 0;\r\n        }\r\n        if (!items.length) {\r\n            this.closeGallery();\r\n            return;\r\n        }\r\n        var currentSrc = this.galleryItems[index].src;\r\n        this.galleryItems = items;\r\n        this.updateControls();\r\n        this.$inner.empty();\r\n        this.currentItemsInDom = [];\r\n        var _index = 0;\r\n        // Find the current index based on source value of the slide\r\n        this.galleryItems.some(function (galleryItem, itemIndex) {\r\n            if (galleryItem.src === currentSrc) {\r\n                _index = itemIndex;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        this.currentItemsInDom = this.organizeSlideItems(_index, -1);\r\n        this.loadContent(_index, true);\r\n        this.getSlideItem(_index).addClass('lg-current');\r\n        this.index = _index;\r\n        this.updateCurrentCounter(_index);\r\n        this.LGel.trigger(lGEvents.updateSlides);\r\n    };\r\n    // Get gallery items based on multiple conditions\r\n    LightGallery.prototype.getItems = function () {\r\n        // Gallery items\r\n        this.items = [];\r\n        if (!this.settings.dynamic) {\r\n            if (this.settings.selector === 'this') {\r\n                this.items.push(this.el);\r\n            }\r\n            else if (this.settings.selector) {\r\n                if (typeof this.settings.selector === 'string') {\r\n                    if (this.settings.selectWithin) {\r\n                        var selectWithin = $LG(this.settings.selectWithin);\r\n                        this.items = selectWithin\r\n                            .find(this.settings.selector)\r\n                            .get();\r\n                    }\r\n                    else {\r\n                        this.items = this.el.querySelectorAll(this.settings.selector);\r\n                    }\r\n                }\r\n                else {\r\n                    this.items = this.settings.selector;\r\n                }\r\n            }\r\n            else {\r\n                this.items = this.el.children;\r\n            }\r\n            return utils.getDynamicOptions(this.items, this.settings.extraProps, this.settings.getCaptionFromTitleOrAlt, this.settings.exThumbImage);\r\n        }\r\n        else {\r\n            return this.settings.dynamicEl || [];\r\n        }\r\n    };\r\n    LightGallery.prototype.shouldHideScrollbar = function () {\r\n        return (this.settings.hideScrollbar &&\r\n            document.body === this.settings.container);\r\n    };\r\n    LightGallery.prototype.hideScrollbar = function () {\r\n        if (!this.shouldHideScrollbar()) {\r\n            return;\r\n        }\r\n        this.bodyPaddingRight = parseFloat($LG('body').style().paddingRight);\r\n        var bodyRect = document.documentElement.getBoundingClientRect();\r\n        var scrollbarWidth = window.innerWidth - bodyRect.width;\r\n        $LG(document.body).css('padding-right', scrollbarWidth + this.bodyPaddingRight + 'px');\r\n        $LG(document.body).addClass('lg-overlay-open');\r\n    };\r\n    LightGallery.prototype.resetScrollBar = function () {\r\n        if (!this.shouldHideScrollbar()) {\r\n            return;\r\n        }\r\n        $LG(document.body).css('padding-right', this.bodyPaddingRight + 'px');\r\n        $LG(document.body).removeClass('lg-overlay-open');\r\n    };\r\n    /**\r\n     * Open lightGallery.\r\n     * Open gallery with specific slide by passing index of the slide as parameter.\r\n     * @category lGPublicMethods\r\n     * @param {Number} index  - index of the slide\r\n     * @param {HTMLElement} element - Which image lightGallery should zoom from\r\n     *\r\n     * @example\r\n     * const $dynamicGallery = document.getElementById('dynamic-gallery-demo');\r\n     * const dynamicGallery = lightGallery($dynamicGallery, {\r\n     *     dynamic: true,\r\n     *     dynamicEl: [\r\n     *         {\r\n     *              src: 'img/1.jpg',\r\n     *              thumb: 'img/thumb-1.jpg',\r\n     *              subHtml: '<h4>Image 1 title</h4><p>Image 1 descriptions.</p>',\r\n     *         },\r\n     *         ...\r\n     *     ],\r\n     * });\r\n     * $dynamicGallery.addEventListener('click', function () {\r\n     *     // Starts with third item.(Optional).\r\n     *     // This is useful if you want use dynamic mode with\r\n     *     // custom thumbnails (thumbnails outside gallery),\r\n     *     dynamicGallery.openGallery(2);\r\n     * });\r\n     *\r\n     */\r\n    LightGallery.prototype.openGallery = function (index, element) {\r\n        var _this = this;\r\n        if (index === void 0) { index = this.settings.index; }\r\n        // prevent accidental double execution\r\n        if (this.lgOpened)\r\n            return;\r\n        this.lgOpened = true;\r\n        this.outer.removeClass('lg-hide-items');\r\n        this.hideScrollbar();\r\n        // Add display block, but still has opacity 0\r\n        this.$container.addClass('lg-show');\r\n        var itemsToBeInsertedToDom = this.getItemsToBeInsertedToDom(index, index);\r\n        this.currentItemsInDom = itemsToBeInsertedToDom;\r\n        var items = '';\r\n        itemsToBeInsertedToDom.forEach(function (item) {\r\n            items = items + (\"<div id=\\\"\" + item + \"\\\" class=\\\"lg-item\\\"></div>\");\r\n        });\r\n        this.$inner.append(items);\r\n        this.addHtml(index);\r\n        var transform = '';\r\n        this.mediaContainerPosition = this.getMediaContainerPosition();\r\n        var _a = this.mediaContainerPosition, top = _a.top, bottom = _a.bottom;\r\n        if (!this.settings.allowMediaOverlap) {\r\n            this.setMediaContainerPosition(top, bottom);\r\n        }\r\n        var __slideVideoInfo = this.galleryItems[index].__slideVideoInfo;\r\n        if (this.zoomFromOrigin && element) {\r\n            this.currentImageSize = utils.getSize(element, this.outer, top + bottom, __slideVideoInfo && this.settings.videoMaxSize);\r\n            transform = utils.getTransform(element, this.outer, top, bottom, this.currentImageSize);\r\n        }\r\n        if (!this.zoomFromOrigin || !transform) {\r\n            this.outer.addClass(this.settings.startClass);\r\n            this.getSlideItem(index).removeClass('lg-complete');\r\n        }\r\n        var timeout = this.settings.zoomFromOrigin\r\n            ? 100\r\n            : this.settings.backdropDuration;\r\n        setTimeout(function () {\r\n            _this.outer.addClass('lg-components-open');\r\n        }, timeout);\r\n        this.index = index;\r\n        this.LGel.trigger(lGEvents.beforeOpen);\r\n        // add class lg-current to remove initial transition\r\n        this.getSlideItem(index).addClass('lg-current');\r\n        this.lGalleryOn = false;\r\n        // Store the current scroll top value to scroll back after closing the gallery..\r\n        this.prevScrollTop = $LG(window).scrollTop();\r\n        setTimeout(function () {\r\n            // Need to check both zoomFromOrigin and transform values as we need to set set the\r\n            // default opening animation if user missed to add the lg-size attribute\r\n            if (_this.zoomFromOrigin && transform) {\r\n                var currentSlide_1 = _this.getSlideItem(index);\r\n                currentSlide_1.css('transform', transform);\r\n                setTimeout(function () {\r\n                    currentSlide_1\r\n                        .addClass('lg-start-progress lg-start-end-progress')\r\n                        .css('transition-duration', _this.settings.startAnimationDuration + 'ms');\r\n                    _this.outer.addClass('lg-zoom-from-image');\r\n                });\r\n                setTimeout(function () {\r\n                    currentSlide_1.css('transform', 'translate3d(0, 0, 0)');\r\n                }, 100);\r\n            }\r\n            setTimeout(function () {\r\n                _this.$backdrop.addClass('in');\r\n                _this.$container.addClass('lg-show-in');\r\n            }, 10);\r\n            setTimeout(function () {\r\n                if (_this.settings.trapFocus &&\r\n                    document.body === _this.settings.container) {\r\n                    _this.trapFocus();\r\n                }\r\n            }, _this.settings.backdropDuration + 50);\r\n            // lg-visible class resets gallery opacity to 1\r\n            if (!_this.zoomFromOrigin || !transform) {\r\n                setTimeout(function () {\r\n                    _this.outer.addClass('lg-visible');\r\n                }, _this.settings.backdropDuration);\r\n            }\r\n            // initiate slide function\r\n            _this.slide(index, false, false, false);\r\n            _this.LGel.trigger(lGEvents.afterOpen);\r\n        });\r\n        if (document.body === this.settings.container) {\r\n            $LG('html').addClass('lg-on');\r\n        }\r\n    };\r\n    /**\r\n     * Note - Changing the position of the media on every slide transition creates a flickering effect.\r\n     * Therefore, The height of the caption is calculated dynamically, only once based on the first slide caption.\r\n     * if you have dynamic captions for each media,\r\n     * you can provide an appropriate height for the captions via allowMediaOverlap option\r\n     */\r\n    LightGallery.prototype.getMediaContainerPosition = function () {\r\n        if (this.settings.allowMediaOverlap) {\r\n            return {\r\n                top: 0,\r\n                bottom: 0,\r\n            };\r\n        }\r\n        var top = this.$toolbar.get().clientHeight || 0;\r\n        var subHtml = this.outer.find('.lg-components .lg-sub-html').get();\r\n        var captionHeight = this.settings.defaultCaptionHeight ||\r\n            (subHtml && subHtml.clientHeight) ||\r\n            0;\r\n        var thumbContainer = this.outer.find('.lg-thumb-outer').get();\r\n        var thumbHeight = thumbContainer ? thumbContainer.clientHeight : 0;\r\n        var bottom = thumbHeight + captionHeight;\r\n        return {\r\n            top: top,\r\n            bottom: bottom,\r\n        };\r\n    };\r\n    LightGallery.prototype.setMediaContainerPosition = function (top, bottom) {\r\n        if (top === void 0) { top = 0; }\r\n        if (bottom === void 0) { bottom = 0; }\r\n        this.$content.css('top', top + 'px').css('bottom', bottom + 'px');\r\n    };\r\n    LightGallery.prototype.hideBars = function () {\r\n        var _this = this;\r\n        // Hide controllers if mouse doesn't move for some period\r\n        setTimeout(function () {\r\n            _this.outer.removeClass('lg-hide-items');\r\n            if (_this.settings.hideBarsDelay > 0) {\r\n                _this.outer.on('mousemove.lg click.lg touchstart.lg', function () {\r\n                    _this.outer.removeClass('lg-hide-items');\r\n                    clearTimeout(_this.hideBarTimeout);\r\n                    // Timeout will be cleared on each slide movement also\r\n                    _this.hideBarTimeout = setTimeout(function () {\r\n                        _this.outer.addClass('lg-hide-items');\r\n                    }, _this.settings.hideBarsDelay);\r\n                });\r\n                _this.outer.trigger('mousemove.lg');\r\n            }\r\n        }, this.settings.showBarsAfter);\r\n    };\r\n    LightGallery.prototype.initPictureFill = function ($img) {\r\n        if (this.settings.supportLegacyBrowser) {\r\n            try {\r\n                picturefill({\r\n                    elements: [$img.get()],\r\n                });\r\n            }\r\n            catch (e) {\r\n                console.warn('lightGallery :- If you want srcset or picture tag to be supported for older browser please include picturefil javascript library in your document.');\r\n            }\r\n        }\r\n    };\r\n    /**\r\n     *  @desc Create image counter\r\n     *  Ex: 1/10\r\n     */\r\n    LightGallery.prototype.counter = function () {\r\n        if (this.settings.counter) {\r\n            var counterHtml = \"<div class=\\\"lg-counter\\\" role=\\\"status\\\" aria-live=\\\"polite\\\">\\n                <span id=\\\"\" + this.getIdName('lg-counter-current') + \"\\\" class=\\\"lg-counter-current\\\">\" + (this.index + 1) + \" </span> /\\n                <span id=\\\"\" + this.getIdName('lg-counter-all') + \"\\\" class=\\\"lg-counter-all\\\">\" + this.galleryItems.length + \" </span></div>\";\r\n            this.outer.find(this.settings.appendCounterTo).append(counterHtml);\r\n        }\r\n    };\r\n    /**\r\n     *  @desc add sub-html into the slide\r\n     *  @param {Number} index - index of the slide\r\n     */\r\n    LightGallery.prototype.addHtml = function (index) {\r\n        var subHtml;\r\n        var subHtmlUrl;\r\n        if (this.galleryItems[index].subHtmlUrl) {\r\n            subHtmlUrl = this.galleryItems[index].subHtmlUrl;\r\n        }\r\n        else {\r\n            subHtml = this.galleryItems[index].subHtml;\r\n        }\r\n        if (!subHtmlUrl) {\r\n            if (subHtml) {\r\n                // get first letter of sub-html\r\n                // if first letter starts with . or # get the html form the jQuery object\r\n                var fL = subHtml.substring(0, 1);\r\n                if (fL === '.' || fL === '#') {\r\n                    if (this.settings.subHtmlSelectorRelative &&\r\n                        !this.settings.dynamic) {\r\n                        subHtml = $LG(this.items)\r\n                            .eq(index)\r\n                            .find(subHtml)\r\n                            .first()\r\n                            .html();\r\n                    }\r\n                    else {\r\n                        subHtml = $LG(subHtml).first().html();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                subHtml = '';\r\n            }\r\n        }\r\n        if (this.settings.appendSubHtmlTo !== '.lg-item') {\r\n            if (subHtmlUrl) {\r\n                this.outer.find('.lg-sub-html').load(subHtmlUrl);\r\n            }\r\n            else {\r\n                this.outer.find('.lg-sub-html').html(subHtml);\r\n            }\r\n        }\r\n        else {\r\n            var currentSlide = $LG(this.getSlideItemId(index));\r\n            if (subHtmlUrl) {\r\n                currentSlide.load(subHtmlUrl);\r\n            }\r\n            else {\r\n                currentSlide.append(\"<div class=\\\"lg-sub-html\\\">\" + subHtml + \"</div>\");\r\n            }\r\n        }\r\n        // Add lg-empty-html class if title doesn't exist\r\n        if (typeof subHtml !== 'undefined' && subHtml !== null) {\r\n            if (subHtml === '') {\r\n                this.outer\r\n                    .find(this.settings.appendSubHtmlTo)\r\n                    .addClass('lg-empty-html');\r\n            }\r\n            else {\r\n                this.outer\r\n                    .find(this.settings.appendSubHtmlTo)\r\n                    .removeClass('lg-empty-html');\r\n            }\r\n        }\r\n        this.LGel.trigger(lGEvents.afterAppendSubHtml, {\r\n            index: index,\r\n        });\r\n    };\r\n    /**\r\n     *  @desc Preload slides\r\n     *  @param {Number} index - index of the slide\r\n     * @todo preload not working for the first slide, Also, should work for the first and last slide as well\r\n     */\r\n    LightGallery.prototype.preload = function (index) {\r\n        for (var i = 1; i <= this.settings.preload; i++) {\r\n            if (i >= this.galleryItems.length - index) {\r\n                break;\r\n            }\r\n            this.loadContent(index + i, false);\r\n        }\r\n        for (var j = 1; j <= this.settings.preload; j++) {\r\n            if (index - j < 0) {\r\n                break;\r\n            }\r\n            this.loadContent(index - j, false);\r\n        }\r\n    };\r\n    LightGallery.prototype.getDummyImgStyles = function (imageSize) {\r\n        if (!imageSize)\r\n            return '';\r\n        return \"width:\" + imageSize.width + \"px;\\n                margin-left: -\" + imageSize.width / 2 + \"px;\\n                margin-top: -\" + imageSize.height / 2 + \"px;\\n                height:\" + imageSize.height + \"px\";\r\n    };\r\n    LightGallery.prototype.getVideoContStyle = function (imageSize) {\r\n        if (!imageSize)\r\n            return '';\r\n        return \"width:\" + imageSize.width + \"px;\\n                height:\" + imageSize.height + \"px\";\r\n    };\r\n    LightGallery.prototype.getDummyImageContent = function ($currentSlide, index, alt) {\r\n        var $currentItem;\r\n        if (!this.settings.dynamic) {\r\n            $currentItem = $LG(this.items).eq(index);\r\n        }\r\n        if ($currentItem) {\r\n            var _dummyImgSrc = void 0;\r\n            if (!this.settings.exThumbImage) {\r\n                _dummyImgSrc = $currentItem.find('img').first().attr('src');\r\n            }\r\n            else {\r\n                _dummyImgSrc = $currentItem.attr(this.settings.exThumbImage);\r\n            }\r\n            if (!_dummyImgSrc)\r\n                return '';\r\n            var imgStyle = this.getDummyImgStyles(this.currentImageSize);\r\n            var dummyImgContent = \"<img \" + alt + \" style=\\\"\" + imgStyle + \"\\\" class=\\\"lg-dummy-img\\\" src=\\\"\" + _dummyImgSrc + \"\\\" />\";\r\n            $currentSlide.addClass('lg-first-slide');\r\n            this.outer.addClass('lg-first-slide-loading');\r\n            return dummyImgContent;\r\n        }\r\n        return '';\r\n    };\r\n    LightGallery.prototype.setImgMarkup = function (src, $currentSlide, index) {\r\n        var currentGalleryItem = this.galleryItems[index];\r\n        var alt = currentGalleryItem.alt, srcset = currentGalleryItem.srcset, sizes = currentGalleryItem.sizes, sources = currentGalleryItem.sources;\r\n        // Use the thumbnail as dummy image which will be resized to actual image size and\r\n        // displayed on top of actual image\r\n        var imgContent = '';\r\n        var altAttr = alt ? 'alt=\"' + alt + '\"' : '';\r\n        if (this.isFirstSlideWithZoomAnimation()) {\r\n            imgContent = this.getDummyImageContent($currentSlide, index, altAttr);\r\n        }\r\n        else {\r\n            imgContent = utils.getImgMarkup(index, src, altAttr, srcset, sizes, sources);\r\n        }\r\n        var imgMarkup = \"<picture class=\\\"lg-img-wrap\\\"> \" + imgContent + \"</picture>\";\r\n        $currentSlide.prepend(imgMarkup);\r\n    };\r\n    LightGallery.prototype.onSlideObjectLoad = function ($slide, isHTML5VideoWithoutPoster, onLoad, onError) {\r\n        var mediaObject = $slide.find('.lg-object').first();\r\n        if (utils.isImageLoaded(mediaObject.get()) ||\r\n            isHTML5VideoWithoutPoster) {\r\n            onLoad();\r\n        }\r\n        else {\r\n            mediaObject.on('load.lg error.lg', function () {\r\n                onLoad && onLoad();\r\n            });\r\n            mediaObject.on('error.lg', function () {\r\n                onError && onError();\r\n            });\r\n        }\r\n    };\r\n    /**\r\n     *\r\n     * @param $el Current slide item\r\n     * @param index\r\n     * @param delay Delay is 0 except first time\r\n     * @param speed Speed is same as delay, except it is 0 if gallery is opened via hash plugin\r\n     * @param isFirstSlide\r\n     */\r\n    LightGallery.prototype.onLgObjectLoad = function (currentSlide, index, delay, speed, isFirstSlide, isHTML5VideoWithoutPoster) {\r\n        var _this = this;\r\n        this.onSlideObjectLoad(currentSlide, isHTML5VideoWithoutPoster, function () {\r\n            _this.triggerSlideItemLoad(currentSlide, index, delay, speed, isFirstSlide);\r\n        }, function () {\r\n            currentSlide.addClass('lg-complete lg-complete_');\r\n            currentSlide.html('<span class=\"lg-error-msg\">' +\r\n                _this.settings.strings['mediaLoadingFailed'] +\r\n                '</span>');\r\n        });\r\n    };\r\n    LightGallery.prototype.triggerSlideItemLoad = function ($currentSlide, index, delay, speed, isFirstSlide) {\r\n        var _this = this;\r\n        var currentGalleryItem = this.galleryItems[index];\r\n        // Adding delay for video slides without poster for better performance and user experience\r\n        // Videos should start playing once once the gallery is completely loaded\r\n        var _speed = isFirstSlide &&\r\n            this.getSlideType(currentGalleryItem) === 'video' &&\r\n            !currentGalleryItem.poster\r\n            ? speed\r\n            : 0;\r\n        setTimeout(function () {\r\n            $currentSlide.addClass('lg-complete lg-complete_');\r\n            _this.LGel.trigger(lGEvents.slideItemLoad, {\r\n                index: index,\r\n                delay: delay || 0,\r\n                isFirstSlide: isFirstSlide,\r\n            });\r\n        }, _speed);\r\n    };\r\n    LightGallery.prototype.isFirstSlideWithZoomAnimation = function () {\r\n        return !!(!this.lGalleryOn &&\r\n            this.zoomFromOrigin &&\r\n            this.currentImageSize);\r\n    };\r\n    // Add video slideInfo\r\n    LightGallery.prototype.addSlideVideoInfo = function (items) {\r\n        var _this = this;\r\n        items.forEach(function (element, index) {\r\n            element.__slideVideoInfo = utils.isVideo(element.src, !!element.video, index);\r\n            if (element.__slideVideoInfo &&\r\n                _this.settings.loadYouTubePoster &&\r\n                !element.poster &&\r\n                element.__slideVideoInfo.youtube) {\r\n                element.poster = \"//img.youtube.com/vi/\" + element.__slideVideoInfo.youtube[1] + \"/maxresdefault.jpg\";\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     *  Load slide content into slide.\r\n     *  This is used to load content into slides that is not visible too\r\n     *  @param {Number} index - index of the slide.\r\n     *  @param {Boolean} rec - if true call loadcontent() function again.\r\n     */\r\n    LightGallery.prototype.loadContent = function (index, rec) {\r\n        var _this = this;\r\n        var currentGalleryItem = this.galleryItems[index];\r\n        var $currentSlide = $LG(this.getSlideItemId(index));\r\n        var poster = currentGalleryItem.poster, srcset = currentGalleryItem.srcset, sizes = currentGalleryItem.sizes, sources = currentGalleryItem.sources;\r\n        var src = currentGalleryItem.src;\r\n        var video = currentGalleryItem.video;\r\n        var _html5Video = video && typeof video === 'string' ? JSON.parse(video) : video;\r\n        if (currentGalleryItem.responsive) {\r\n            var srcDyItms = currentGalleryItem.responsive.split(',');\r\n            src = utils.getResponsiveSrc(srcDyItms) || src;\r\n        }\r\n        var videoInfo = currentGalleryItem.__slideVideoInfo;\r\n        var lgVideoStyle = '';\r\n        var iframe = !!currentGalleryItem.iframe;\r\n        var isFirstSlide = !this.lGalleryOn;\r\n        // delay for adding complete class. it is 0 except first time.\r\n        var delay = 0;\r\n        if (isFirstSlide) {\r\n            if (this.zoomFromOrigin && this.currentImageSize) {\r\n                delay = this.settings.startAnimationDuration + 10;\r\n            }\r\n            else {\r\n                delay = this.settings.backdropDuration + 10;\r\n            }\r\n        }\r\n        if (!$currentSlide.hasClass('lg-loaded')) {\r\n            if (videoInfo) {\r\n                var _a = this.mediaContainerPosition, top_2 = _a.top, bottom = _a.bottom;\r\n                var videoSize = utils.getSize(this.items[index], this.outer, top_2 + bottom, videoInfo && this.settings.videoMaxSize);\r\n                lgVideoStyle = this.getVideoContStyle(videoSize);\r\n            }\r\n            if (iframe) {\r\n                var markup = utils.getIframeMarkup(this.settings.iframeWidth, this.settings.iframeHeight, this.settings.iframeMaxWidth, this.settings.iframeMaxHeight, src, currentGalleryItem.iframeTitle);\r\n                $currentSlide.prepend(markup);\r\n            }\r\n            else if (poster) {\r\n                var dummyImg = '';\r\n                var hasStartAnimation = isFirstSlide &&\r\n                    this.zoomFromOrigin &&\r\n                    this.currentImageSize;\r\n                if (hasStartAnimation) {\r\n                    dummyImg = this.getDummyImageContent($currentSlide, index, '');\r\n                }\r\n                var markup = utils.getVideoPosterMarkup(poster, dummyImg || '', lgVideoStyle, this.settings.strings['playVideo'], videoInfo);\r\n                $currentSlide.prepend(markup);\r\n            }\r\n            else if (videoInfo) {\r\n                var markup = \"<div class=\\\"lg-video-cont \\\" style=\\\"\" + lgVideoStyle + \"\\\"></div>\";\r\n                $currentSlide.prepend(markup);\r\n            }\r\n            else {\r\n                this.setImgMarkup(src, $currentSlide, index);\r\n                if (srcset || sources) {\r\n                    var $img = $currentSlide.find('.lg-object');\r\n                    this.initPictureFill($img);\r\n                }\r\n            }\r\n            if (poster || videoInfo) {\r\n                this.LGel.trigger(lGEvents.hasVideo, {\r\n                    index: index,\r\n                    src: src,\r\n                    html5Video: _html5Video,\r\n                    hasPoster: !!poster,\r\n                });\r\n            }\r\n            this.LGel.trigger(lGEvents.afterAppendSlide, { index: index });\r\n            if (this.lGalleryOn &&\r\n                this.settings.appendSubHtmlTo === '.lg-item') {\r\n                this.addHtml(index);\r\n            }\r\n        }\r\n        // For first time add some delay for displaying the start animation.\r\n        var _speed = 0;\r\n        // Do not change the delay value because it is required for zoom plugin.\r\n        // If gallery opened from direct url (hash) speed value should be 0\r\n        if (delay && !$LG(document.body).hasClass('lg-from-hash')) {\r\n            _speed = delay;\r\n        }\r\n        // Only for first slide and zoomFromOrigin is enabled\r\n        if (this.isFirstSlideWithZoomAnimation()) {\r\n            setTimeout(function () {\r\n                $currentSlide\r\n                    .removeClass('lg-start-end-progress lg-start-progress')\r\n                    .removeAttr('style');\r\n            }, this.settings.startAnimationDuration + 100);\r\n            if (!$currentSlide.hasClass('lg-loaded')) {\r\n                setTimeout(function () {\r\n                    if (_this.getSlideType(currentGalleryItem) === 'image') {\r\n                        var alt = currentGalleryItem.alt;\r\n                        var altAttr = alt ? 'alt=\"' + alt + '\"' : '';\r\n                        $currentSlide\r\n                            .find('.lg-img-wrap')\r\n                            .append(utils.getImgMarkup(index, src, altAttr, srcset, sizes, currentGalleryItem.sources));\r\n                        if (srcset || sources) {\r\n                            var $img = $currentSlide.find('.lg-object');\r\n                            _this.initPictureFill($img);\r\n                        }\r\n                    }\r\n                    if (_this.getSlideType(currentGalleryItem) === 'image' ||\r\n                        (_this.getSlideType(currentGalleryItem) === 'video' &&\r\n                            poster)) {\r\n                        _this.onLgObjectLoad($currentSlide, index, delay, _speed, true, false);\r\n                        // load remaining slides once the slide is completely loaded\r\n                        _this.onSlideObjectLoad($currentSlide, !!(videoInfo && videoInfo.html5 && !poster), function () {\r\n                            _this.loadContentOnFirstSlideLoad(index, $currentSlide, _speed);\r\n                        }, function () {\r\n                            _this.loadContentOnFirstSlideLoad(index, $currentSlide, _speed);\r\n                        });\r\n                    }\r\n                }, this.settings.startAnimationDuration + 100);\r\n            }\r\n        }\r\n        // SLide content has been added to dom\r\n        $currentSlide.addClass('lg-loaded');\r\n        if (!this.isFirstSlideWithZoomAnimation() ||\r\n            (this.getSlideType(currentGalleryItem) === 'video' && !poster)) {\r\n            this.onLgObjectLoad($currentSlide, index, delay, _speed, isFirstSlide, !!(videoInfo && videoInfo.html5 && !poster));\r\n        }\r\n        // When gallery is opened once content is loaded (second time) need to add lg-complete class for css styling\r\n        if ((!this.zoomFromOrigin || !this.currentImageSize) &&\r\n            $currentSlide.hasClass('lg-complete_') &&\r\n            !this.lGalleryOn) {\r\n            setTimeout(function () {\r\n                $currentSlide.addClass('lg-complete');\r\n            }, this.settings.backdropDuration);\r\n        }\r\n        // Content loaded\r\n        // Need to set lGalleryOn before calling preload function\r\n        this.lGalleryOn = true;\r\n        if (rec === true) {\r\n            if (!$currentSlide.hasClass('lg-complete_')) {\r\n                $currentSlide\r\n                    .find('.lg-object')\r\n                    .first()\r\n                    .on('load.lg error.lg', function () {\r\n                    _this.preload(index);\r\n                });\r\n            }\r\n            else {\r\n                this.preload(index);\r\n            }\r\n        }\r\n    };\r\n    /**\r\n     * @desc Remove dummy image content and load next slides\r\n     * Called only for the first time if zoomFromOrigin animation is enabled\r\n     * @param index\r\n     * @param $currentSlide\r\n     * @param speed\r\n     */\r\n    LightGallery.prototype.loadContentOnFirstSlideLoad = function (index, $currentSlide, speed) {\r\n        var _this = this;\r\n        setTimeout(function () {\r\n            $currentSlide.find('.lg-dummy-img').remove();\r\n            $currentSlide.removeClass('lg-first-slide');\r\n            _this.outer.removeClass('lg-first-slide-loading');\r\n            _this.isDummyImageRemoved = true;\r\n            _this.preload(index);\r\n        }, speed + 300);\r\n    };\r\n    LightGallery.prototype.getItemsToBeInsertedToDom = function (index, prevIndex, numberOfItems) {\r\n        var _this = this;\r\n        if (numberOfItems === void 0) { numberOfItems = 0; }\r\n        var itemsToBeInsertedToDom = [];\r\n        // Minimum 2 items should be there\r\n        var possibleNumberOfItems = Math.max(numberOfItems, 3);\r\n        possibleNumberOfItems = Math.min(possibleNumberOfItems, this.galleryItems.length);\r\n        var prevIndexItem = \"lg-item-\" + this.lgId + \"-\" + prevIndex;\r\n        if (this.galleryItems.length <= 3) {\r\n            this.galleryItems.forEach(function (_element, index) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + _this.lgId + \"-\" + index);\r\n            });\r\n            return itemsToBeInsertedToDom;\r\n        }\r\n        if (index < (this.galleryItems.length - 1) / 2) {\r\n            for (var idx = index; idx > index - possibleNumberOfItems / 2 && idx >= 0; idx--) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + idx);\r\n            }\r\n            var numberOfExistingItems = itemsToBeInsertedToDom.length;\r\n            for (var idx = 0; idx < possibleNumberOfItems - numberOfExistingItems; idx++) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + (index + idx + 1));\r\n            }\r\n        }\r\n        else {\r\n            for (var idx = index; idx <= this.galleryItems.length - 1 &&\r\n                idx < index + possibleNumberOfItems / 2; idx++) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + idx);\r\n            }\r\n            var numberOfExistingItems = itemsToBeInsertedToDom.length;\r\n            for (var idx = 0; idx < possibleNumberOfItems - numberOfExistingItems; idx++) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + (index - idx - 1));\r\n            }\r\n        }\r\n        if (this.settings.loop) {\r\n            if (index === this.galleryItems.length - 1) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + 0);\r\n            }\r\n            else if (index === 0) {\r\n                itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + (this.galleryItems.length - 1));\r\n            }\r\n        }\r\n        if (itemsToBeInsertedToDom.indexOf(prevIndexItem) === -1) {\r\n            itemsToBeInsertedToDom.push(\"lg-item-\" + this.lgId + \"-\" + prevIndex);\r\n        }\r\n        return itemsToBeInsertedToDom;\r\n    };\r\n    LightGallery.prototype.organizeSlideItems = function (index, prevIndex) {\r\n        var _this = this;\r\n        var itemsToBeInsertedToDom = this.getItemsToBeInsertedToDom(index, prevIndex, this.settings.numberOfSlideItemsInDom);\r\n        itemsToBeInsertedToDom.forEach(function (item) {\r\n            if (_this.currentItemsInDom.indexOf(item) === -1) {\r\n                _this.$inner.append(\"<div id=\\\"\" + item + \"\\\" class=\\\"lg-item\\\"></div>\");\r\n            }\r\n        });\r\n        this.currentItemsInDom.forEach(function (item) {\r\n            if (itemsToBeInsertedToDom.indexOf(item) === -1) {\r\n                $LG(\"#\" + item).remove();\r\n            }\r\n        });\r\n        return itemsToBeInsertedToDom;\r\n    };\r\n    /**\r\n     * Get previous index of the slide\r\n     */\r\n    LightGallery.prototype.getPreviousSlideIndex = function () {\r\n        var prevIndex = 0;\r\n        try {\r\n            var currentItemId = this.outer\r\n                .find('.lg-current')\r\n                .first()\r\n                .attr('id');\r\n            prevIndex = parseInt(currentItemId.split('-')[3]) || 0;\r\n        }\r\n        catch (error) {\r\n            prevIndex = 0;\r\n        }\r\n        return prevIndex;\r\n    };\r\n    LightGallery.prototype.setDownloadValue = function (index) {\r\n        if (this.settings.download) {\r\n            var currentGalleryItem = this.galleryItems[index];\r\n            var hideDownloadBtn = currentGalleryItem.downloadUrl === false ||\r\n                currentGalleryItem.downloadUrl === 'false';\r\n            if (hideDownloadBtn) {\r\n                this.outer.addClass('lg-hide-download');\r\n            }\r\n            else {\r\n                var $download = this.getElementById('lg-download');\r\n                this.outer.removeClass('lg-hide-download');\r\n                $download.attr('href', currentGalleryItem.downloadUrl ||\r\n                    currentGalleryItem.src);\r\n                if (currentGalleryItem.download) {\r\n                    $download.attr('download', currentGalleryItem.download);\r\n                }\r\n            }\r\n        }\r\n    };\r\n    LightGallery.prototype.makeSlideAnimation = function (direction, currentSlideItem, previousSlideItem) {\r\n        var _this = this;\r\n        if (this.lGalleryOn) {\r\n            previousSlideItem.addClass('lg-slide-progress');\r\n        }\r\n        setTimeout(function () {\r\n            // remove all transitions\r\n            _this.outer.addClass('lg-no-trans');\r\n            _this.outer\r\n                .find('.lg-item')\r\n                .removeClass('lg-prev-slide lg-next-slide');\r\n            if (direction === 'prev') {\r\n                //prevslide\r\n                currentSlideItem.addClass('lg-prev-slide');\r\n                previousSlideItem.addClass('lg-next-slide');\r\n            }\r\n            else {\r\n                // next slide\r\n                currentSlideItem.addClass('lg-next-slide');\r\n                previousSlideItem.addClass('lg-prev-slide');\r\n            }\r\n            // give 50 ms for browser to add/remove class\r\n            setTimeout(function () {\r\n                _this.outer.find('.lg-item').removeClass('lg-current');\r\n                currentSlideItem.addClass('lg-current');\r\n                // reset all transitions\r\n                _this.outer.removeClass('lg-no-trans');\r\n            }, 50);\r\n        }, this.lGalleryOn ? this.settings.slideDelay : 0);\r\n    };\r\n    /**\r\n     * Goto a specific slide.\r\n     * @param {Number} index - index of the slide\r\n     * @param {Boolean} fromTouch - true if slide function called via touch event or mouse drag\r\n     * @param {Boolean} fromThumb - true if slide function called via thumbnail click\r\n     * @param {String} direction - Direction of the slide(next/prev)\r\n     * @category lGPublicMethods\r\n     * @example\r\n     *  const plugin = lightGallery();\r\n     *  // to go to 3rd slide\r\n     *  plugin.slide(2);\r\n     *\r\n     */\r\n    LightGallery.prototype.slide = function (index, fromTouch, fromThumb, direction) {\r\n        var _this = this;\r\n        var prevIndex = this.getPreviousSlideIndex();\r\n        this.currentItemsInDom = this.organizeSlideItems(index, prevIndex);\r\n        // Prevent multiple call, Required for hsh plugin\r\n        if (this.lGalleryOn && prevIndex === index) {\r\n            return;\r\n        }\r\n        var numberOfGalleryItems = this.galleryItems.length;\r\n        if (!this.lgBusy) {\r\n            if (this.settings.counter) {\r\n                this.updateCurrentCounter(index);\r\n            }\r\n            var currentSlideItem = this.getSlideItem(index);\r\n            var previousSlideItem_1 = this.getSlideItem(prevIndex);\r\n            var currentGalleryItem = this.galleryItems[index];\r\n            var videoInfo = currentGalleryItem.__slideVideoInfo;\r\n            this.outer.attr('data-lg-slide-type', this.getSlideType(currentGalleryItem));\r\n            this.setDownloadValue(index);\r\n            if (videoInfo) {\r\n                var _a = this.mediaContainerPosition, top_3 = _a.top, bottom = _a.bottom;\r\n                var videoSize = utils.getSize(this.items[index], this.outer, top_3 + bottom, videoInfo && this.settings.videoMaxSize);\r\n                this.resizeVideoSlide(index, videoSize);\r\n            }\r\n            this.LGel.trigger(lGEvents.beforeSlide, {\r\n                prevIndex: prevIndex,\r\n                index: index,\r\n                fromTouch: !!fromTouch,\r\n                fromThumb: !!fromThumb,\r\n            });\r\n            this.lgBusy = true;\r\n            clearTimeout(this.hideBarTimeout);\r\n            this.arrowDisable(index);\r\n            if (!direction) {\r\n                if (index < prevIndex) {\r\n                    direction = 'prev';\r\n                }\r\n                else if (index > prevIndex) {\r\n                    direction = 'next';\r\n                }\r\n            }\r\n            if (!fromTouch) {\r\n                this.makeSlideAnimation(direction, currentSlideItem, previousSlideItem_1);\r\n            }\r\n            else {\r\n                this.outer\r\n                    .find('.lg-item')\r\n                    .removeClass('lg-prev-slide lg-current lg-next-slide');\r\n                var touchPrev = void 0;\r\n                var touchNext = void 0;\r\n                if (numberOfGalleryItems > 2) {\r\n                    touchPrev = index - 1;\r\n                    touchNext = index + 1;\r\n                    if (index === 0 && prevIndex === numberOfGalleryItems - 1) {\r\n                        // next slide\r\n                        touchNext = 0;\r\n                        touchPrev = numberOfGalleryItems - 1;\r\n                    }\r\n                    else if (index === numberOfGalleryItems - 1 &&\r\n                        prevIndex === 0) {\r\n                        // prev slide\r\n                        touchNext = 0;\r\n                        touchPrev = numberOfGalleryItems - 1;\r\n                    }\r\n                }\r\n                else {\r\n                    touchPrev = 0;\r\n                    touchNext = 1;\r\n                }\r\n                if (direction === 'prev') {\r\n                    this.getSlideItem(touchNext).addClass('lg-next-slide');\r\n                }\r\n                else {\r\n                    this.getSlideItem(touchPrev).addClass('lg-prev-slide');\r\n                }\r\n                currentSlideItem.addClass('lg-current');\r\n            }\r\n            // Do not put load content in set timeout as it needs to load immediately when the gallery is opened\r\n            if (!this.lGalleryOn) {\r\n                this.loadContent(index, true);\r\n            }\r\n            else {\r\n                setTimeout(function () {\r\n                    _this.loadContent(index, true);\r\n                    // Add title if this.settings.appendSubHtmlTo === lg-sub-html\r\n                    if (_this.settings.appendSubHtmlTo !== '.lg-item') {\r\n                        _this.addHtml(index);\r\n                    }\r\n                }, this.settings.speed + 50 + (fromTouch ? 0 : this.settings.slideDelay));\r\n            }\r\n            setTimeout(function () {\r\n                _this.lgBusy = false;\r\n                previousSlideItem_1.removeClass('lg-slide-progress');\r\n                _this.LGel.trigger(lGEvents.afterSlide, {\r\n                    prevIndex: prevIndex,\r\n                    index: index,\r\n                    fromTouch: fromTouch,\r\n                    fromThumb: fromThumb,\r\n                });\r\n            }, (this.lGalleryOn ? this.settings.speed + 100 : 100) + (fromTouch ? 0 : this.settings.slideDelay));\r\n        }\r\n        this.index = index;\r\n    };\r\n    LightGallery.prototype.updateCurrentCounter = function (index) {\r\n        this.getElementById('lg-counter-current').html(index + 1 + '');\r\n    };\r\n    LightGallery.prototype.updateCounterTotal = function () {\r\n        this.getElementById('lg-counter-all').html(this.galleryItems.length + '');\r\n    };\r\n    LightGallery.prototype.getSlideType = function (item) {\r\n        if (item.__slideVideoInfo) {\r\n            return 'video';\r\n        }\r\n        else if (item.iframe) {\r\n            return 'iframe';\r\n        }\r\n        else {\r\n            return 'image';\r\n        }\r\n    };\r\n    LightGallery.prototype.touchMove = function (startCoords, endCoords, e) {\r\n        var distanceX = endCoords.pageX - startCoords.pageX;\r\n        var distanceY = endCoords.pageY - startCoords.pageY;\r\n        var allowSwipe = false;\r\n        if (this.swipeDirection) {\r\n            allowSwipe = true;\r\n        }\r\n        else {\r\n            if (Math.abs(distanceX) > 15) {\r\n                this.swipeDirection = 'horizontal';\r\n                allowSwipe = true;\r\n            }\r\n            else if (Math.abs(distanceY) > 15) {\r\n                this.swipeDirection = 'vertical';\r\n                allowSwipe = true;\r\n            }\r\n        }\r\n        if (!allowSwipe) {\r\n            return;\r\n        }\r\n        var $currentSlide = this.getSlideItem(this.index);\r\n        if (this.swipeDirection === 'horizontal') {\r\n            e === null || e === void 0 ? void 0 : e.preventDefault();\r\n            // reset opacity and transition duration\r\n            this.outer.addClass('lg-dragging');\r\n            // move current slide\r\n            this.setTranslate($currentSlide, distanceX, 0);\r\n            // move next and prev slide with current slide\r\n            var width = $currentSlide.get().offsetWidth;\r\n            var slideWidthAmount = (width * 15) / 100;\r\n            var gutter = slideWidthAmount - Math.abs((distanceX * 10) / 100);\r\n            this.setTranslate(this.outer.find('.lg-prev-slide').first(), -width + distanceX - gutter, 0);\r\n            this.setTranslate(this.outer.find('.lg-next-slide').first(), width + distanceX + gutter, 0);\r\n        }\r\n        else if (this.swipeDirection === 'vertical') {\r\n            if (this.settings.swipeToClose) {\r\n                e === null || e === void 0 ? void 0 : e.preventDefault();\r\n                this.$container.addClass('lg-dragging-vertical');\r\n                var opacity = 1 - Math.abs(distanceY) / window.innerHeight;\r\n                this.$backdrop.css('opacity', opacity);\r\n                var scale = 1 - Math.abs(distanceY) / (window.innerWidth * 2);\r\n                this.setTranslate($currentSlide, 0, distanceY, scale, scale);\r\n                if (Math.abs(distanceY) > 100) {\r\n                    this.outer\r\n                        .addClass('lg-hide-items')\r\n                        .removeClass('lg-components-open');\r\n                }\r\n            }\r\n        }\r\n    };\r\n    LightGallery.prototype.touchEnd = function (endCoords, startCoords, event) {\r\n        var _this = this;\r\n        var distance;\r\n        // keep slide animation for any mode while dragg/swipe\r\n        if (this.settings.mode !== 'lg-slide') {\r\n            this.outer.addClass('lg-slide');\r\n        }\r\n        // set transition duration\r\n        setTimeout(function () {\r\n            _this.$container.removeClass('lg-dragging-vertical');\r\n            _this.outer\r\n                .removeClass('lg-dragging lg-hide-items')\r\n                .addClass('lg-components-open');\r\n            var triggerClick = true;\r\n            if (_this.swipeDirection === 'horizontal') {\r\n                distance = endCoords.pageX - startCoords.pageX;\r\n                var distanceAbs = Math.abs(endCoords.pageX - startCoords.pageX);\r\n                if (distance < 0 &&\r\n                    distanceAbs > _this.settings.swipeThreshold) {\r\n                    _this.goToNextSlide(true);\r\n                    triggerClick = false;\r\n                }\r\n                else if (distance > 0 &&\r\n                    distanceAbs > _this.settings.swipeThreshold) {\r\n                    _this.goToPrevSlide(true);\r\n                    triggerClick = false;\r\n                }\r\n            }\r\n            else if (_this.swipeDirection === 'vertical') {\r\n                distance = Math.abs(endCoords.pageY - startCoords.pageY);\r\n                if (_this.settings.closable &&\r\n                    _this.settings.swipeToClose &&\r\n                    distance > 100) {\r\n                    _this.closeGallery();\r\n                    return;\r\n                }\r\n                else {\r\n                    _this.$backdrop.css('opacity', 1);\r\n                }\r\n            }\r\n            _this.outer.find('.lg-item').removeAttr('style');\r\n            if (triggerClick &&\r\n                Math.abs(endCoords.pageX - startCoords.pageX) < 5) {\r\n                // Trigger click if distance is less than 5 pix\r\n                var target = $LG(event.target);\r\n                if (_this.isPosterElement(target)) {\r\n                    _this.LGel.trigger(lGEvents.posterClick);\r\n                }\r\n            }\r\n            _this.swipeDirection = undefined;\r\n        });\r\n        // remove slide class once drag/swipe is completed if mode is not slide\r\n        setTimeout(function () {\r\n            if (!_this.outer.hasClass('lg-dragging') &&\r\n                _this.settings.mode !== 'lg-slide') {\r\n                _this.outer.removeClass('lg-slide');\r\n            }\r\n        }, this.settings.speed + 100);\r\n    };\r\n    LightGallery.prototype.enableSwipe = function () {\r\n        var _this = this;\r\n        var startCoords = {};\r\n        var endCoords = {};\r\n        var isMoved = false;\r\n        var isSwiping = false;\r\n        if (this.settings.enableSwipe) {\r\n            this.$inner.on('touchstart.lg', function (e) {\r\n                _this.dragOrSwipeEnabled = true;\r\n                var $item = _this.getSlideItem(_this.index);\r\n                if (($LG(e.target).hasClass('lg-item') ||\r\n                    $item.get().contains(e.target)) &&\r\n                    !_this.outer.hasClass('lg-zoomed') &&\r\n                    !_this.lgBusy &&\r\n                    e.touches.length === 1) {\r\n                    isSwiping = true;\r\n                    _this.touchAction = 'swipe';\r\n                    _this.manageSwipeClass();\r\n                    startCoords = {\r\n                        pageX: e.touches[0].pageX,\r\n                        pageY: e.touches[0].pageY,\r\n                    };\r\n                }\r\n            });\r\n            this.$inner.on('touchmove.lg', function (e) {\r\n                if (isSwiping &&\r\n                    _this.touchAction === 'swipe' &&\r\n                    e.touches.length === 1) {\r\n                    endCoords = {\r\n                        pageX: e.touches[0].pageX,\r\n                        pageY: e.touches[0].pageY,\r\n                    };\r\n                    _this.touchMove(startCoords, endCoords, e);\r\n                    isMoved = true;\r\n                }\r\n            });\r\n            this.$inner.on('touchend.lg', function (event) {\r\n                if (_this.touchAction === 'swipe') {\r\n                    if (isMoved) {\r\n                        isMoved = false;\r\n                        _this.touchEnd(endCoords, startCoords, event);\r\n                    }\r\n                    else if (isSwiping) {\r\n                        var target = $LG(event.target);\r\n                        if (_this.isPosterElement(target)) {\r\n                            _this.LGel.trigger(lGEvents.posterClick);\r\n                        }\r\n                    }\r\n                    _this.touchAction = undefined;\r\n                    isSwiping = false;\r\n                }\r\n            });\r\n        }\r\n    };\r\n    LightGallery.prototype.enableDrag = function () {\r\n        var _this = this;\r\n        var startCoords = {};\r\n        var endCoords = {};\r\n        var isDraging = false;\r\n        var isMoved = false;\r\n        if (this.settings.enableDrag) {\r\n            this.outer.on('mousedown.lg', function (e) {\r\n                _this.dragOrSwipeEnabled = true;\r\n                var $item = _this.getSlideItem(_this.index);\r\n                if ($LG(e.target).hasClass('lg-item') ||\r\n                    $item.get().contains(e.target)) {\r\n                    if (!_this.outer.hasClass('lg-zoomed') && !_this.lgBusy) {\r\n                        e.preventDefault();\r\n                        if (!_this.lgBusy) {\r\n                            _this.manageSwipeClass();\r\n                            startCoords = {\r\n                                pageX: e.pageX,\r\n                                pageY: e.pageY,\r\n                            };\r\n                            isDraging = true;\r\n                            // ** Fix for webkit cursor issue https://code.google.com/p/chromium/issues/detail?id=26723\r\n                            _this.outer.get().scrollLeft += 1;\r\n                            _this.outer.get().scrollLeft -= 1;\r\n                            // *\r\n                            _this.outer\r\n                                .removeClass('lg-grab')\r\n                                .addClass('lg-grabbing');\r\n                            _this.LGel.trigger(lGEvents.dragStart);\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n            $LG(window).on(\"mousemove.lg.global\" + this.lgId, function (e) {\r\n                if (isDraging && _this.lgOpened) {\r\n                    isMoved = true;\r\n                    endCoords = {\r\n                        pageX: e.pageX,\r\n                        pageY: e.pageY,\r\n                    };\r\n                    _this.touchMove(startCoords, endCoords);\r\n                    _this.LGel.trigger(lGEvents.dragMove);\r\n                }\r\n            });\r\n            $LG(window).on(\"mouseup.lg.global\" + this.lgId, function (event) {\r\n                if (!_this.lgOpened) {\r\n                    return;\r\n                }\r\n                var target = $LG(event.target);\r\n                if (isMoved) {\r\n                    isMoved = false;\r\n                    _this.touchEnd(endCoords, startCoords, event);\r\n                    _this.LGel.trigger(lGEvents.dragEnd);\r\n                }\r\n                else if (_this.isPosterElement(target)) {\r\n                    _this.LGel.trigger(lGEvents.posterClick);\r\n                }\r\n                // Prevent execution on click\r\n                if (isDraging) {\r\n                    isDraging = false;\r\n                    _this.outer.removeClass('lg-grabbing').addClass('lg-grab');\r\n                }\r\n            });\r\n        }\r\n    };\r\n    LightGallery.prototype.triggerPosterClick = function () {\r\n        var _this = this;\r\n        this.$inner.on('click.lg', function (event) {\r\n            if (!_this.dragOrSwipeEnabled &&\r\n                _this.isPosterElement($LG(event.target))) {\r\n                _this.LGel.trigger(lGEvents.posterClick);\r\n            }\r\n        });\r\n    };\r\n    LightGallery.prototype.manageSwipeClass = function () {\r\n        var _touchNext = this.index + 1;\r\n        var _touchPrev = this.index - 1;\r\n        if (this.settings.loop && this.galleryItems.length > 2) {\r\n            if (this.index === 0) {\r\n                _touchPrev = this.galleryItems.length - 1;\r\n            }\r\n            else if (this.index === this.galleryItems.length - 1) {\r\n                _touchNext = 0;\r\n            }\r\n        }\r\n        this.outer.find('.lg-item').removeClass('lg-next-slide lg-prev-slide');\r\n        if (_touchPrev > -1) {\r\n            this.getSlideItem(_touchPrev).addClass('lg-prev-slide');\r\n        }\r\n        this.getSlideItem(_touchNext).addClass('lg-next-slide');\r\n    };\r\n    /**\r\n     * Go to next slide\r\n     * @param {Boolean} fromTouch - true if slide function called via touch event\r\n     * @category lGPublicMethods\r\n     * @example\r\n     *  const plugin = lightGallery();\r\n     *  plugin.goToNextSlide();\r\n     * @see <a href=\"/demos/methods/\">Demo</a>\r\n     */\r\n    LightGallery.prototype.goToNextSlide = function (fromTouch) {\r\n        var _this = this;\r\n        var _loop = this.settings.loop;\r\n        if (fromTouch && this.galleryItems.length < 3) {\r\n            _loop = false;\r\n        }\r\n        if (!this.lgBusy) {\r\n            if (this.index + 1 < this.galleryItems.length) {\r\n                this.index++;\r\n                this.LGel.trigger(lGEvents.beforeNextSlide, {\r\n                    index: this.index,\r\n                });\r\n                this.slide(this.index, !!fromTouch, false, 'next');\r\n            }\r\n            else {\r\n                if (_loop) {\r\n                    this.index = 0;\r\n                    this.LGel.trigger(lGEvents.beforeNextSlide, {\r\n                        index: this.index,\r\n                    });\r\n                    this.slide(this.index, !!fromTouch, false, 'next');\r\n                }\r\n                else if (this.settings.slideEndAnimation && !fromTouch) {\r\n                    this.outer.addClass('lg-right-end');\r\n                    setTimeout(function () {\r\n                        _this.outer.removeClass('lg-right-end');\r\n                    }, 400);\r\n                }\r\n            }\r\n        }\r\n    };\r\n    /**\r\n     * Go to previous slides\r\n     * @param {Boolean} fromTouch - true if slide function called via touch event\r\n     * @category lGPublicMethods\r\n     * @example\r\n     *  const plugin = lightGallery({});\r\n     *  plugin.goToPrevSlide();\r\n     * @see <a href=\"/demos/methods/\">Demo</a>\r\n     *\r\n     */\r\n    LightGallery.prototype.goToPrevSlide = function (fromTouch) {\r\n        var _this = this;\r\n        var _loop = this.settings.loop;\r\n        if (fromTouch && this.galleryItems.length < 3) {\r\n            _loop = false;\r\n        }\r\n        if (!this.lgBusy) {\r\n            if (this.index > 0) {\r\n                this.index--;\r\n                this.LGel.trigger(lGEvents.beforePrevSlide, {\r\n                    index: this.index,\r\n                    fromTouch: fromTouch,\r\n                });\r\n                this.slide(this.index, !!fromTouch, false, 'prev');\r\n            }\r\n            else {\r\n                if (_loop) {\r\n                    this.index = this.galleryItems.length - 1;\r\n                    this.LGel.trigger(lGEvents.beforePrevSlide, {\r\n                        index: this.index,\r\n                        fromTouch: fromTouch,\r\n                    });\r\n                    this.slide(this.index, !!fromTouch, false, 'prev');\r\n                }\r\n                else if (this.settings.slideEndAnimation && !fromTouch) {\r\n                    this.outer.addClass('lg-left-end');\r\n                    setTimeout(function () {\r\n                        _this.outer.removeClass('lg-left-end');\r\n                    }, 400);\r\n                }\r\n            }\r\n        }\r\n    };\r\n    LightGallery.prototype.keyPress = function () {\r\n        var _this = this;\r\n        $LG(window).on(\"keydown.lg.global\" + this.lgId, function (e) {\r\n            if (_this.lgOpened &&\r\n                _this.settings.escKey === true &&\r\n                e.keyCode === 27) {\r\n                e.preventDefault();\r\n                if (_this.settings.allowMediaOverlap &&\r\n                    _this.outer.hasClass('lg-can-toggle') &&\r\n                    _this.outer.hasClass('lg-components-open')) {\r\n                    _this.outer.removeClass('lg-components-open');\r\n                }\r\n                else {\r\n                    _this.closeGallery();\r\n                }\r\n            }\r\n            if (_this.lgOpened && _this.galleryItems.length > 1) {\r\n                if (e.keyCode === 37) {\r\n                    e.preventDefault();\r\n                    _this.goToPrevSlide();\r\n                }\r\n                if (e.keyCode === 39) {\r\n                    e.preventDefault();\r\n                    _this.goToNextSlide();\r\n                }\r\n            }\r\n        });\r\n    };\r\n    LightGallery.prototype.arrow = function () {\r\n        var _this = this;\r\n        this.getElementById('lg-prev').on('click.lg', function () {\r\n            _this.goToPrevSlide();\r\n        });\r\n        this.getElementById('lg-next').on('click.lg', function () {\r\n            _this.goToNextSlide();\r\n        });\r\n    };\r\n    LightGallery.prototype.arrowDisable = function (index) {\r\n        // Disable arrows if settings.hideControlOnEnd is true\r\n        if (!this.settings.loop && this.settings.hideControlOnEnd) {\r\n            var $prev = this.getElementById('lg-prev');\r\n            var $next = this.getElementById('lg-next');\r\n            if (index + 1 === this.galleryItems.length) {\r\n                $next.attr('disabled', 'disabled').addClass('disabled');\r\n            }\r\n            else {\r\n                $next.removeAttr('disabled').removeClass('disabled');\r\n            }\r\n            if (index === 0) {\r\n                $prev.attr('disabled', 'disabled').addClass('disabled');\r\n            }\r\n            else {\r\n                $prev.removeAttr('disabled').removeClass('disabled');\r\n            }\r\n        }\r\n    };\r\n    LightGallery.prototype.setTranslate = function ($el, xValue, yValue, scaleX, scaleY) {\r\n        if (scaleX === void 0) { scaleX = 1; }\r\n        if (scaleY === void 0) { scaleY = 1; }\r\n        $el.css('transform', 'translate3d(' +\r\n            xValue +\r\n            'px, ' +\r\n            yValue +\r\n            'px, 0px) scale3d(' +\r\n            scaleX +\r\n            ', ' +\r\n            scaleY +\r\n            ', 1)');\r\n    };\r\n    LightGallery.prototype.mousewheel = function () {\r\n        var _this = this;\r\n        var lastCall = 0;\r\n        this.outer.on('wheel.lg', function (e) {\r\n            if (!e.deltaY || _this.galleryItems.length < 2) {\r\n                return;\r\n            }\r\n            e.preventDefault();\r\n            var now = new Date().getTime();\r\n            if (now - lastCall < 1000) {\r\n                return;\r\n            }\r\n            lastCall = now;\r\n            if (e.deltaY > 0) {\r\n                _this.goToNextSlide();\r\n            }\r\n            else if (e.deltaY < 0) {\r\n                _this.goToPrevSlide();\r\n            }\r\n        });\r\n    };\r\n    LightGallery.prototype.isSlideElement = function (target) {\r\n        return (target.hasClass('lg-outer') ||\r\n            target.hasClass('lg-item') ||\r\n            target.hasClass('lg-img-wrap'));\r\n    };\r\n    LightGallery.prototype.isPosterElement = function (target) {\r\n        var playButton = this.getSlideItem(this.index)\r\n            .find('.lg-video-play-button')\r\n            .get();\r\n        return (target.hasClass('lg-video-poster') ||\r\n            target.hasClass('lg-video-play-button') ||\r\n            (playButton && playButton.contains(target.get())));\r\n    };\r\n    /**\r\n     * Maximize minimize inline gallery.\r\n     * @category lGPublicMethods\r\n     */\r\n    LightGallery.prototype.toggleMaximize = function () {\r\n        var _this = this;\r\n        this.getElementById('lg-maximize').on('click.lg', function () {\r\n            _this.$container.toggleClass('lg-inline');\r\n            _this.refreshOnResize();\r\n        });\r\n    };\r\n    LightGallery.prototype.invalidateItems = function () {\r\n        for (var index = 0; index < this.items.length; index++) {\r\n            var element = this.items[index];\r\n            var $element = $LG(element);\r\n            $element.off(\"click.lgcustom-item-\" + $element.attr('data-lg-id'));\r\n        }\r\n    };\r\n    LightGallery.prototype.trapFocus = function () {\r\n        var _this = this;\r\n        this.$container.get().focus({\r\n            preventScroll: true,\r\n        });\r\n        $LG(window).on(\"keydown.lg.global\" + this.lgId, function (e) {\r\n            if (!_this.lgOpened) {\r\n                return;\r\n            }\r\n            var isTabPressed = e.key === 'Tab' || e.keyCode === 9;\r\n            if (!isTabPressed) {\r\n                return;\r\n            }\r\n            var focusableEls = utils.getFocusableElements(_this.$container.get());\r\n            var firstFocusableEl = focusableEls[0];\r\n            var lastFocusableEl = focusableEls[focusableEls.length - 1];\r\n            if (e.shiftKey) {\r\n                if (document.activeElement === firstFocusableEl) {\r\n                    lastFocusableEl.focus();\r\n                    e.preventDefault();\r\n                }\r\n            }\r\n            else {\r\n                if (document.activeElement === lastFocusableEl) {\r\n                    firstFocusableEl.focus();\r\n                    e.preventDefault();\r\n                }\r\n            }\r\n        });\r\n    };\r\n    LightGallery.prototype.manageCloseGallery = function () {\r\n        var _this = this;\r\n        if (!this.settings.closable)\r\n            return;\r\n        var mousedown = false;\r\n        this.getElementById('lg-close').on('click.lg', function () {\r\n            _this.closeGallery();\r\n        });\r\n        if (this.settings.closeOnTap) {\r\n            // If you drag the slide and release outside gallery gets close on chrome\r\n            // for preventing this check mousedown and mouseup happened on .lg-item or lg-outer\r\n            this.outer.on('mousedown.lg', function (e) {\r\n                var target = $LG(e.target);\r\n                if (_this.isSlideElement(target)) {\r\n                    mousedown = true;\r\n                }\r\n                else {\r\n                    mousedown = false;\r\n                }\r\n            });\r\n            this.outer.on('mousemove.lg', function () {\r\n                mousedown = false;\r\n            });\r\n            this.outer.on('mouseup.lg', function (e) {\r\n                var target = $LG(e.target);\r\n                if (_this.isSlideElement(target) && mousedown) {\r\n                    if (!_this.outer.hasClass('lg-dragging')) {\r\n                        _this.closeGallery();\r\n                    }\r\n                }\r\n            });\r\n        }\r\n    };\r\n    /**\r\n     * Close lightGallery if it is opened.\r\n     *\r\n     * @description If closable is false in the settings, you need to pass true via closeGallery method to force close gallery\r\n     * @return returns the estimated time to close gallery completely including the close animation duration\r\n     * @category lGPublicMethods\r\n     * @example\r\n     *  const plugin = lightGallery();\r\n     *  plugin.closeGallery();\r\n     *\r\n     */\r\n    LightGallery.prototype.closeGallery = function (force) {\r\n        var _this = this;\r\n        if (!this.lgOpened || (!this.settings.closable && !force)) {\r\n            return 0;\r\n        }\r\n        this.LGel.trigger(lGEvents.beforeClose);\r\n        if (this.settings.resetScrollPosition && !this.settings.hideScrollbar) {\r\n            $LG(window).scrollTop(this.prevScrollTop);\r\n        }\r\n        var currentItem = this.items[this.index];\r\n        var transform;\r\n        if (this.zoomFromOrigin && currentItem) {\r\n            var _a = this.mediaContainerPosition, top_4 = _a.top, bottom = _a.bottom;\r\n            var _b = this.galleryItems[this.index], __slideVideoInfo = _b.__slideVideoInfo, poster = _b.poster;\r\n            var imageSize = utils.getSize(currentItem, this.outer, top_4 + bottom, __slideVideoInfo && poster && this.settings.videoMaxSize);\r\n            transform = utils.getTransform(currentItem, this.outer, top_4, bottom, imageSize);\r\n        }\r\n        if (this.zoomFromOrigin && transform) {\r\n            this.outer.addClass('lg-closing lg-zoom-from-image');\r\n            this.getSlideItem(this.index)\r\n                .addClass('lg-start-end-progress')\r\n                .css('transition-duration', this.settings.startAnimationDuration + 'ms')\r\n                .css('transform', transform);\r\n        }\r\n        else {\r\n            this.outer.addClass('lg-hide-items');\r\n            // lg-zoom-from-image is used for setting the opacity to 1 if zoomFromOrigin is true\r\n            // If the closing item doesn't have the lg-size attribute, remove this class to avoid the closing css conflicts\r\n            this.outer.removeClass('lg-zoom-from-image');\r\n        }\r\n        // Unbind all events added by lightGallery\r\n        // @todo\r\n        //this.$el.off('.lg.tm');\r\n        this.destroyModules();\r\n        this.lGalleryOn = false;\r\n        this.isDummyImageRemoved = false;\r\n        this.zoomFromOrigin = this.settings.zoomFromOrigin;\r\n        clearTimeout(this.hideBarTimeout);\r\n        this.hideBarTimeout = false;\r\n        $LG('html').removeClass('lg-on');\r\n        this.outer.removeClass('lg-visible lg-components-open');\r\n        // Resetting opacity to 0 isd required as  vertical swipe to close function adds inline opacity.\r\n        this.$backdrop.removeClass('in').css('opacity', 0);\r\n        var removeTimeout = this.zoomFromOrigin && transform\r\n            ? Math.max(this.settings.startAnimationDuration, this.settings.backdropDuration)\r\n            : this.settings.backdropDuration;\r\n        this.$container.removeClass('lg-show-in');\r\n        // Once the closign animation is completed and gallery is invisible\r\n        setTimeout(function () {\r\n            if (_this.zoomFromOrigin && transform) {\r\n                _this.outer.removeClass('lg-zoom-from-image');\r\n            }\r\n            _this.$container.removeClass('lg-show');\r\n            // Reset scrollbar\r\n            _this.resetScrollBar();\r\n            // Need to remove inline opacity as it is used in the stylesheet as well\r\n            _this.$backdrop\r\n                .removeAttr('style')\r\n                .css('transition-duration', _this.settings.backdropDuration + 'ms');\r\n            _this.outer.removeClass(\"lg-closing \" + _this.settings.startClass);\r\n            _this.getSlideItem(_this.index).removeClass('lg-start-end-progress');\r\n            _this.$inner.empty();\r\n            if (_this.lgOpened) {\r\n                _this.LGel.trigger(lGEvents.afterClose, {\r\n                    instance: _this,\r\n                });\r\n            }\r\n            if (_this.$container.get()) {\r\n                _this.$container.get().blur();\r\n            }\r\n            _this.lgOpened = false;\r\n        }, removeTimeout + 100);\r\n        return removeTimeout + 100;\r\n    };\r\n    LightGallery.prototype.initModules = function () {\r\n        this.plugins.forEach(function (module) {\r\n            try {\r\n                module.init();\r\n            }\r\n            catch (err) {\r\n                console.warn(\"lightGallery:- make sure lightGallery module is properly initiated\");\r\n            }\r\n        });\r\n    };\r\n    LightGallery.prototype.destroyModules = function (destroy) {\r\n        this.plugins.forEach(function (module) {\r\n            try {\r\n                if (destroy) {\r\n                    module.destroy();\r\n                }\r\n                else {\r\n                    module.closeGallery && module.closeGallery();\r\n                }\r\n            }\r\n            catch (err) {\r\n                console.warn(\"lightGallery:- make sure lightGallery module is properly destroyed\");\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Refresh lightGallery with new set of children.\r\n     *\r\n     * @description This is useful to update the gallery when the child elements are changed without calling destroy method.\r\n     *\r\n     * If you are using dynamic mode, you can pass the modified array of dynamicEl as the first parameter to refresh the dynamic gallery\r\n     * @see <a href=\"/demos/dynamic-mode/\">Demo</a>\r\n     * @category lGPublicMethods\r\n     * @example\r\n     *  const plugin = lightGallery();\r\n     *  // Delete or add children, then call\r\n     *  plugin.refresh();\r\n     *\r\n     */\r\n    LightGallery.prototype.refresh = function (galleryItems) {\r\n        if (!this.settings.dynamic) {\r\n            this.invalidateItems();\r\n        }\r\n        if (galleryItems) {\r\n            this.galleryItems = galleryItems;\r\n        }\r\n        else {\r\n            this.galleryItems = this.getItems();\r\n        }\r\n        this.updateControls();\r\n        this.openGalleryOnItemClick();\r\n        this.LGel.trigger(lGEvents.updateSlides);\r\n    };\r\n    LightGallery.prototype.updateControls = function () {\r\n        this.addSlideVideoInfo(this.galleryItems);\r\n        this.updateCounterTotal();\r\n        this.manageSingleSlideClassName();\r\n    };\r\n    LightGallery.prototype.destroyGallery = function () {\r\n        this.destroyModules(true);\r\n        if (!this.settings.dynamic) {\r\n            this.invalidateItems();\r\n        }\r\n        $LG(window).off(\".lg.global\" + this.lgId);\r\n        this.LGel.off('.lg');\r\n        this.$container.remove();\r\n    };\r\n    /**\r\n     * Destroy lightGallery.\r\n     * Destroy lightGallery and its plugin instances completely\r\n     *\r\n     * @description This method also calls CloseGallery function internally. Returns the time takes to completely close and destroy the instance.\r\n     * In case if you want to re-initialize lightGallery right after destroying it, initialize it only once the destroy process is completed.\r\n     * You can use refresh method most of the times.\r\n     * @category lGPublicMethods\r\n     * @example\r\n     *  const plugin = lightGallery();\r\n     *  plugin.destroy();\r\n     *\r\n     */\r\n    LightGallery.prototype.destroy = function () {\r\n        var closeTimeout = this.closeGallery(true);\r\n        if (closeTimeout) {\r\n            setTimeout(this.destroyGallery.bind(this), closeTimeout);\r\n        }\r\n        else {\r\n            this.destroyGallery();\r\n        }\r\n        return closeTimeout;\r\n    };\r\n    return LightGallery;\r\n}());\n\nfunction lightGallery(el, options) {\r\n    return new LightGallery(el, options);\r\n}\n\nexport default lightGallery;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,QAAQ,GAAG,SAAAA,CAAA,EAAW;EACtBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASF,QAAQA,CAACG,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAChF;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AAED,SAASO,cAAcA,CAAA,EAAG;EACtB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEU,EAAE,GAAGR,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGU,EAAE,EAAEV,CAAC,EAAE,EAAED,CAAC,IAAIG,SAAS,CAACF,CAAC,CAAC,CAACG,MAAM;EACnF,KAAK,IAAIQ,CAAC,GAAGC,KAAK,CAACb,CAAC,CAAC,EAAEc,CAAC,GAAG,CAAC,EAAEb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,EAAE,EAAEV,CAAC,EAAE,EAC5C,KAAK,IAAIc,CAAC,GAAGZ,SAAS,CAACF,CAAC,CAAC,EAAEe,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGF,CAAC,CAACX,MAAM,EAAEY,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAEF,CAAC,EAAE,EAC7DF,CAAC,CAACE,CAAC,CAAC,GAAGC,CAAC,CAACC,CAAC,CAAC;EACnB,OAAOJ,CAAC;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIM,QAAQ,GAAG;EACXC,gBAAgB,EAAE,oBAAoB;EACtCC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,eAAe,EAAE,mBAAmB;EACpCC,YAAY,EAAE,gBAAgB;EAC9BC,kBAAkB,EAAE,sBAAsB;EAC1CC,UAAU,EAAE,cAAc;EAC1BC,SAAS,EAAE,aAAa;EACxBC,aAAa,EAAE,iBAAiB;EAChCC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,SAAS,EAAE,aAAa;EACxBC,QAAQ,EAAE,YAAY;EACtBC,OAAO,EAAE,WAAW;EACpBC,eAAe,EAAE,mBAAmB;EACpCC,eAAe,EAAE,mBAAmB;EACpCC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,cAAc,EAAE,kBAAkB;EAClCC,YAAY,EAAE,gBAAgB;EAC9BC,QAAQ,EAAE,YAAY;EACtBC,aAAa,EAAE,iBAAiB;EAChCC,YAAY,EAAE;AAClB,CAAC;AAED,IAAIC,wBAAwB,GAAG;EAC3BC,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,GAAG;EACVC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE,eAAe;EAC3BC,gBAAgB,EAAE,GAAG;EACrBC,SAAS,EAAE,EAAE;EACbC,sBAAsB,EAAE,GAAG;EAC3BC,cAAc,EAAE,IAAI;EACpBC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,KAAK;EACpBC,UAAU,EAAE,CAAC;EACbC,oBAAoB,EAAE,IAAI;EAC1BC,iBAAiB,EAAE,KAAK;EACxBC,YAAY,EAAE,UAAU;EACxBC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,CAAC;EACvBC,cAAc,EAAE,EAAE;EAClBC,eAAe,EAAE,EAAE;EACnBC,mBAAmB,EAAE,IAAI;EACzBC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,KAAK;EACvBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,KAAK;EACvBC,UAAU,EAAE,KAAK;EACjBC,wBAAwB,EAAE,IAAI;EAC9BC,eAAe,EAAE,cAAc;EAC/BC,uBAAuB,EAAE,KAAK;EAC9BC,OAAO,EAAE,CAAC;EACVC,uBAAuB,EAAE,EAAE;EAC3BC,QAAQ,EAAE,EAAE;EACZC,YAAY,EAAE,EAAE;EAChBC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,EAAE;EACZC,KAAK,EAAE,CAAC;EACRC,WAAW,EAAE,MAAM;EACnBC,YAAY,EAAE,MAAM;EACpBC,cAAc,EAAE,MAAM;EACtBC,eAAe,EAAE,MAAM;EACvBC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,aAAa;EAC9BC,cAAc,EAAE,EAAE;EAClBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,EAAE;EACdC,YAAY,EAAE,EAAE;EAChBC,QAAQ,EAAEC,SAAS;EACnBC,cAAc,EAAE;IACZ9B,QAAQ,EAAE,KAAK;IACfN,aAAa,EAAE,KAAK;IACpBwB,QAAQ,EAAE;EACd,CAAC;EACDa,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE;IACLC,YAAY,EAAE,eAAe;IAC7BC,cAAc,EAAE,iBAAiB;IACjCC,aAAa,EAAE,gBAAgB;IAC/BC,SAAS,EAAE,YAAY;IACvBlB,QAAQ,EAAE,UAAU;IACpBmB,SAAS,EAAE,YAAY;IACvBC,kBAAkB,EAAE;EACxB;AACJ,CAAC;AAED,SAASC,eAAeA,CAAA,EAAG;EACvB,CAAC,YAAY;IACT,IAAI,OAAOC,MAAM,CAACC,WAAW,KAAK,UAAU,EACxC,OAAO,KAAK;IAChB,SAASA,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;MAChCA,MAAM,GAAGA,MAAM,IAAI;QACfC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK;QACjBC,MAAM,EAAE;MACZ,CAAC;MACD,IAAIC,GAAG,GAAGC,QAAQ,CAACC,WAAW,CAAC,aAAa,CAAC;MAC7CF,GAAG,CAACG,eAAe,CAACR,KAAK,EAAEC,MAAM,CAACC,OAAO,EAAED,MAAM,CAACE,UAAU,EAAEF,MAAM,CAACG,MAAM,CAAC;MAC5E,OAAOC,GAAG;IACd;IACAP,MAAM,CAACC,WAAW,GAAGA,WAAW;EACpC,CAAC,EAAE,CAAC;EACJ,CAAC,YAAY;IACT,IAAI,CAACU,OAAO,CAAC5H,SAAS,CAAC6H,OAAO,EAAE;MAC5BD,OAAO,CAAC5H,SAAS,CAAC6H,OAAO,GACrBD,OAAO,CAAC5H,SAAS,CAAC8H,iBAAiB,IAC/BF,OAAO,CAAC5H,SAAS,CAAC+H,qBAAqB;IACnD;EACJ,CAAC,EAAE,CAAC;AACR;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAC9C,QAAQ,EAAE;IACvB,IAAI,CAAC+C,iBAAiB,GAAG,CACrB,oBAAoB,EACpB,0BAA0B,EAC1B,WAAW,EACX,YAAY,CACf;IACD,IAAI,CAAC/C,QAAQ,GAAG,IAAI,CAACgD,YAAY,CAAChD,QAAQ,CAAC;IAC3C,IAAI,CAACiD,YAAY,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACtC,OAAO,IAAI;EACf;EACAJ,OAAO,CAACK,YAAY,GAAG,YAAY;IAC/B,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;MACxE,IAAIjI,CAAC,GAAIkI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;QAAEC,CAAC,GAAGH,CAAC,IAAI,GAAG,GAAGjI,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;MACpE,OAAOoI,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACzB,CAAC,CAAC;EACN,CAAC;EACDX,OAAO,CAAChI,SAAS,CAACkI,YAAY,GAAG,UAAUhD,QAAQ,EAAE0D,OAAO,EAAE;IAC1D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGnB,QAAQ;IAAE;IAC9C,IAAI,OAAOvC,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAOA,QAAQ;IACnB;IACA0D,OAAO,GAAGA,OAAO,IAAInB,QAAQ;IAC7B,IAAIoB,EAAE,GAAG3D,QAAQ,CAAC4D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,IAAID,EAAE,KAAK,GAAG,EAAE;MACZ,OAAOD,OAAO,CAACG,aAAa,CAAC7D,QAAQ,CAAC;IAC1C,CAAC,MACI;MACD,OAAO0D,OAAO,CAACI,gBAAgB,CAAC9D,QAAQ,CAAC;IAC7C;EACJ,CAAC;EACD8C,OAAO,CAAChI,SAAS,CAACiJ,KAAK,GAAG,UAAUC,IAAI,EAAE;IACtC,IAAI,CAAC,IAAI,CAAChE,QAAQ,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACA,QAAQ,CAACpF,MAAM,KAAKwG,SAAS,EAAE;MACpC,EAAE,CAAC6C,OAAO,CAACjJ,IAAI,CAAC,IAAI,CAACgF,QAAQ,EAAEgE,IAAI,CAAC;IACxC,CAAC,MACI;MACDA,IAAI,CAAC,IAAI,CAAChE,QAAQ,EAAE,CAAC,CAAC;IAC1B;IACA,OAAO,IAAI;EACf,CAAC;EACD8C,OAAO,CAAChI,SAAS,CAACoJ,mBAAmB,GAAG,UAAUC,EAAE,EAAEC,WAAW,EAAEC,KAAK,EAAE;IACtE;IACA,IAAIC,QAAQ,GAAGF,WAAW,CAAChB,OAAO,CAAC,YAAY,EAAE,UAAU5I,CAAC,EAAE+J,MAAM,EAAE;MAClE,OAAOA,MAAM,CAACC,WAAW,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF,IAAI,IAAI,CAACzB,iBAAiB,CAAC0B,OAAO,CAACH,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACjDH,EAAE,CAACO,KAAK,CAACJ,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGN,QAAQ,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGR,KAAK;MACtEF,EAAE,CAACO,KAAK,CAAC,QAAQ,GAAGJ,QAAQ,CAAC,GAAGD,KAAK;MACrCF,EAAE,CAACO,KAAK,CAAC,KAAK,GAAGJ,QAAQ,CAAC,GAAGD,KAAK;MAClCF,EAAE,CAACO,KAAK,CAAC,IAAI,GAAGJ,QAAQ,CAAC,GAAGD,KAAK;MACjCF,EAAE,CAACO,KAAK,CAAC,GAAG,GAAGJ,QAAQ,CAAC,GAAGD,KAAK;IACpC,CAAC,MACI;MACDF,EAAE,CAACO,KAAK,CAACJ,QAAQ,CAAC,GAAGD,KAAK;IAC9B;EACJ,CAAC;EACDvB,OAAO,CAAChI,SAAS,CAACoI,WAAW,GAAG,YAAY;IACxC,IAAI,IAAI,CAAClD,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACpF,MAAM,KAAKwG,SAAS,EAAE;MACrD,OAAO,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,OAAO,IAAI,CAACA,QAAQ;IACxB;EACJ,CAAC;EACD8C,OAAO,CAAChI,SAAS,CAACgK,cAAc,GAAG,UAAU7C,KAAK,EAAE8C,SAAS,EAAE;IAC3D,IAAIC,cAAc,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC;IACzC,OAAOhD,KAAK,CACPgD,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC;IAAE,CAAC,CAAC,CAClCC,KAAK,CAAC,UAAUD,CAAC,EAAE;MACpB,OAAOH,cAAc,CAACP,OAAO,CAACU,CAAC,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC;EACDrC,OAAO,CAAChI,SAAS,CAACuK,IAAI,GAAG,UAAUA,IAAI,EAAEhB,KAAK,EAAE;IAC5C,IAAIA,KAAK,KAAKjD,SAAS,EAAE;MACrB,IAAI,CAAC,IAAI,CAAC6B,YAAY,EAAE;QACpB,OAAO,EAAE;MACb;MACA,OAAO,IAAI,CAACA,YAAY,CAACqC,YAAY,CAACD,IAAI,CAAC;IAC/C;IACA,IAAI,CAACtB,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrBA,EAAE,CAACoB,YAAY,CAACF,IAAI,EAAEhB,KAAK,CAAC;IAChC,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDvB,OAAO,CAAChI,SAAS,CAAC0K,IAAI,GAAG,UAAUxF,QAAQ,EAAE;IACzC,OAAOyF,GAAG,CAAC,IAAI,CAACzC,YAAY,CAAChD,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC,CAAC;EAC1D,CAAC;EACD8C,OAAO,CAAChI,SAAS,CAAC4K,KAAK,GAAG,YAAY;IAClC,IAAI,IAAI,CAAC1F,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACpF,MAAM,KAAKwG,SAAS,EAAE;MACrD,OAAOqE,GAAG,CAAC,IAAI,CAACzF,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,MACI;MACD,OAAOyF,GAAG,CAAC,IAAI,CAACzF,QAAQ,CAAC;IAC7B;EACJ,CAAC;EACD8C,OAAO,CAAChI,SAAS,CAAC6K,EAAE,GAAG,UAAUvF,KAAK,EAAE;IACpC,OAAOqF,GAAG,CAAC,IAAI,CAACzF,QAAQ,CAACI,KAAK,CAAC,CAAC;EACpC,CAAC;EACD0C,OAAO,CAAChI,SAAS,CAAC8K,MAAM,GAAG,YAAY;IACnC,OAAOH,GAAG,CAAC,IAAI,CAACzF,QAAQ,CAAC6F,aAAa,CAAC;EAC3C,CAAC;EACD/C,OAAO,CAAChI,SAAS,CAACgL,GAAG,GAAG,YAAY;IAChC,OAAO,IAAI,CAAC5C,WAAW,CAAC,CAAC;EAC7B,CAAC;EACDJ,OAAO,CAAChI,SAAS,CAACiL,UAAU,GAAG,UAAUC,UAAU,EAAE;IACjD,IAAIC,KAAK,GAAGD,UAAU,CAACf,KAAK,CAAC,GAAG,CAAC;IACjC,IAAI,CAAClB,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrB8B,KAAK,CAAChC,OAAO,CAAC,UAAUoB,IAAI,EAAE;QAAE,OAAOlB,EAAE,CAAC+B,eAAe,CAACb,IAAI,CAAC;MAAE,CAAC,CAAC;IACvE,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDvC,OAAO,CAAChI,SAAS,CAACqL,IAAI,GAAG,UAAUC,SAAS,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACnD,YAAY,EAAE;MACpB,OAAO,IAAI;IACf;IACA,IAAIoD,OAAO,GAAG9D,QAAQ,CAAC+D,aAAa,CAAC,KAAK,CAAC;IAC3CD,OAAO,CAACD,SAAS,GAAGA,SAAS;IAC7B,IAAI,CAACnD,YAAY,CAACsD,UAAU,CAACC,YAAY,CAACH,OAAO,EAAE,IAAI,CAACpD,YAAY,CAAC;IACrE,IAAI,CAACA,YAAY,CAACsD,UAAU,CAACE,WAAW,CAAC,IAAI,CAACxD,YAAY,CAAC;IAC3DoD,OAAO,CAACK,WAAW,CAAC,IAAI,CAACzD,YAAY,CAAC;IACtC,OAAO,IAAI;EACf,CAAC;EACDH,OAAO,CAAChI,SAAS,CAAC8C,QAAQ,GAAG,UAAU+I,UAAU,EAAE;IAC/C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,EAAE;IAAE;IAC9C,IAAI,CAAC5C,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrB;MACAwC,UAAU,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAChB,OAAO,CAAC,UAAUmC,SAAS,EAAE;QAC/C,IAAIA,SAAS,EAAE;UACXjC,EAAE,CAACyC,SAAS,CAACC,GAAG,CAACT,SAAS,CAAC;QAC/B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDtD,OAAO,CAAChI,SAAS,CAACgM,WAAW,GAAG,UAAUH,UAAU,EAAE;IAClD,IAAI,CAAC5C,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrB;MACAwC,UAAU,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAChB,OAAO,CAAC,UAAUmC,SAAS,EAAE;QAC/C,IAAIA,SAAS,EAAE;UACXjC,EAAE,CAACyC,SAAS,CAACG,MAAM,CAACX,SAAS,CAAC;QAClC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDtD,OAAO,CAAChI,SAAS,CAACkM,QAAQ,GAAG,UAAUZ,SAAS,EAAE;IAC9C,IAAI,CAAC,IAAI,CAACnD,YAAY,EAAE;MACpB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,YAAY,CAAC2D,SAAS,CAACK,QAAQ,CAACb,SAAS,CAAC;EAC1D,CAAC;EACDtD,OAAO,CAAChI,SAAS,CAACoM,YAAY,GAAG,UAAUC,SAAS,EAAE;IAClD,IAAI,CAAC,IAAI,CAAClE,YAAY,EAAE;MACpB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,YAAY,CAACiE,YAAY,CAACC,SAAS,CAAC;EACpD,CAAC;EACDrE,OAAO,CAAChI,SAAS,CAACsM,WAAW,GAAG,UAAUhB,SAAS,EAAE;IACjD,IAAI,CAAC,IAAI,CAACnD,YAAY,EAAE;MACpB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC+D,QAAQ,CAACZ,SAAS,CAAC,EAAE;MAC1B,IAAI,CAACU,WAAW,CAACV,SAAS,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACxI,QAAQ,CAACwI,SAAS,CAAC;IAC5B;IACA,OAAO,IAAI;EACf,CAAC;EACDtD,OAAO,CAAChI,SAAS,CAACuM,GAAG,GAAG,UAAU/C,QAAQ,EAAED,KAAK,EAAE;IAC/C,IAAIiD,KAAK,GAAG,IAAI;IAChB,IAAI,CAACvD,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrBmD,KAAK,CAACpD,mBAAmB,CAACC,EAAE,EAAEG,QAAQ,EAAED,KAAK,CAAC;IAClD,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD;EACAvB,OAAO,CAAChI,SAAS,CAACyM,EAAE,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAE;IAC/C,IAAIH,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACtH,QAAQ,EAAE;MAChB,OAAO,IAAI;IACf;IACAwH,MAAM,CAACvC,KAAK,CAAC,GAAG,CAAC,CAAChB,OAAO,CAAC,UAAUhC,KAAK,EAAE;MACvC,IAAI,CAAC5G,KAAK,CAACqM,OAAO,CAAC5E,OAAO,CAAC6E,cAAc,CAAC1F,KAAK,CAAC,CAAC,EAAE;QAC/Ca,OAAO,CAAC6E,cAAc,CAAC1F,KAAK,CAAC,GAAG,EAAE;MACtC;MACAa,OAAO,CAAC6E,cAAc,CAAC1F,KAAK,CAAC,CAAC2F,IAAI,CAACH,QAAQ,CAAC;MAC5CH,KAAK,CAACtH,QAAQ,CAAC6H,gBAAgB,CAAC5F,KAAK,CAACgD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEwC,QAAQ,CAAC;IAClE,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD;EACA3E,OAAO,CAAChI,SAAS,CAACgN,IAAI,GAAG,UAAU7F,KAAK,EAAEwF,QAAQ,EAAE;IAChD,IAAIH,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,EAAE,CAACtF,KAAK,EAAE,YAAY;MACvBqF,KAAK,CAACS,GAAG,CAAC9F,KAAK,CAAC;MAChBwF,QAAQ,CAACxF,KAAK,CAAC;IACnB,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDa,OAAO,CAAChI,SAAS,CAACiN,GAAG,GAAG,UAAU9F,KAAK,EAAE;IACrC,IAAIqF,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACtH,QAAQ,EAAE;MAChB,OAAO,IAAI;IACf;IACA3F,MAAM,CAAC2N,IAAI,CAAClF,OAAO,CAAC6E,cAAc,CAAC,CAAC1D,OAAO,CAAC,UAAUc,SAAS,EAAE;MAC7D,IAAIuC,KAAK,CAACxC,cAAc,CAAC7C,KAAK,EAAE8C,SAAS,CAAC,EAAE;QACxCjC,OAAO,CAAC6E,cAAc,CAAC5C,SAAS,CAAC,CAACd,OAAO,CAAC,UAAUwD,QAAQ,EAAE;UAC1DH,KAAK,CAACtH,QAAQ,CAACiI,mBAAmB,CAAClD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEwC,QAAQ,CAAC;QACzE,CAAC,CAAC;QACF3E,OAAO,CAAC6E,cAAc,CAAC5C,SAAS,CAAC,GAAG,EAAE;MAC1C;IACJ,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDjC,OAAO,CAAChI,SAAS,CAACoN,OAAO,GAAG,UAAUjG,KAAK,EAAEI,MAAM,EAAE;IACjD,IAAI,CAAC,IAAI,CAACY,YAAY,EAAE;MACpB,OAAO,IAAI;IACf;IACA,IAAIkF,WAAW,GAAG,IAAInG,WAAW,CAACC,KAAK,CAACgD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACnD5C,MAAM,EAAEA,MAAM,IAAI;IACtB,CAAC,CAAC;IACF,IAAI,CAACY,YAAY,CAACmF,aAAa,CAACD,WAAW,CAAC;IAC5C,OAAO,IAAI;EACf,CAAC;EACD;EACArF,OAAO,CAAChI,SAAS,CAACuN,IAAI,GAAG,UAAUC,GAAG,EAAE;IACpC,IAAIhB,KAAK,GAAG,IAAI;IAChBiB,KAAK,CAACD,GAAG,CAAC,CACLE,IAAI,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOA,GAAG,CAACC,IAAI,CAAC,CAAC;IAAE,CAAC,CAAC,CAC3CF,IAAI,CAAC,UAAUG,IAAI,EAAE;MACtBrB,KAAK,CAACtH,QAAQ,CAAC4I,SAAS,GAAGD,IAAI;IACnC,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD7F,OAAO,CAAChI,SAAS,CAAC6N,IAAI,GAAG,UAAUA,IAAI,EAAE;IACrC,IAAIA,IAAI,KAAKvH,SAAS,EAAE;MACpB,IAAI,CAAC,IAAI,CAAC6B,YAAY,EAAE;QACpB,OAAO,EAAE;MACb;MACA,OAAO,IAAI,CAACA,YAAY,CAAC2F,SAAS;IACtC;IACA,IAAI,CAAC7E,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrBA,EAAE,CAACyE,SAAS,GAAGD,IAAI;IACvB,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD7F,OAAO,CAAChI,SAAS,CAAC+N,MAAM,GAAG,UAAUF,IAAI,EAAE;IACvC,IAAI,CAAC5E,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrB,IAAI,OAAOwE,IAAI,KAAK,QAAQ,EAAE;QAC1BxE,EAAE,CAAC2E,kBAAkB,CAAC,WAAW,EAAEH,IAAI,CAAC;MAC5C,CAAC,MACI;QACDxE,EAAE,CAACuC,WAAW,CAACiC,IAAI,CAAC;MACxB;IACJ,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD7F,OAAO,CAAChI,SAAS,CAACiO,OAAO,GAAG,UAAUJ,IAAI,EAAE;IACxC,IAAI,CAAC5E,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrBA,EAAE,CAAC2E,kBAAkB,CAAC,YAAY,EAAEH,IAAI,CAAC;IAC7C,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD7F,OAAO,CAAChI,SAAS,CAACiM,MAAM,GAAG,YAAY;IACnC,IAAI,CAAChD,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrBA,EAAE,CAACoC,UAAU,CAACE,WAAW,CAACtC,EAAE,CAAC;IACjC,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACDrB,OAAO,CAAChI,SAAS,CAACkO,KAAK,GAAG,YAAY;IAClC,IAAI,CAACjF,KAAK,CAAC,UAAUI,EAAE,EAAE;MACrBA,EAAE,CAACyE,SAAS,GAAG,EAAE;IACrB,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD9F,OAAO,CAAChI,SAAS,CAACmO,SAAS,GAAG,UAAUA,SAAS,EAAE;IAC/C,IAAIA,SAAS,KAAK7H,SAAS,EAAE;MACzBmB,QAAQ,CAAC2G,IAAI,CAACD,SAAS,GAAGA,SAAS;MACnC1G,QAAQ,CAAC4G,eAAe,CAACF,SAAS,GAAGA,SAAS;MAC9C,OAAO,IAAI;IACf,CAAC,MACI;MACD,OAAQlH,MAAM,CAACqH,WAAW,IACtB7G,QAAQ,CAAC4G,eAAe,CAACF,SAAS,IAClC1G,QAAQ,CAAC2G,IAAI,CAACD,SAAS,IACvB,CAAC;IACT;EACJ,CAAC;EACDnG,OAAO,CAAChI,SAAS,CAACuO,UAAU,GAAG,UAAUA,UAAU,EAAE;IACjD,IAAIA,UAAU,KAAKjI,SAAS,EAAE;MAC1BmB,QAAQ,CAAC2G,IAAI,CAACG,UAAU,GAAGA,UAAU;MACrC9G,QAAQ,CAAC4G,eAAe,CAACE,UAAU,GAAGA,UAAU;MAChD,OAAO,IAAI;IACf,CAAC,MACI;MACD,OAAQtH,MAAM,CAACuH,WAAW,IACtB/G,QAAQ,CAAC4G,eAAe,CAACE,UAAU,IACnC9G,QAAQ,CAAC2G,IAAI,CAACG,UAAU,IACxB,CAAC;IACT;EACJ,CAAC;EACDvG,OAAO,CAAChI,SAAS,CAACyO,MAAM,GAAG,YAAY;IACnC,IAAI,CAAC,IAAI,CAACtG,YAAY,EAAE;MACpB,OAAO;QACHuG,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE;MACT,CAAC;IACL;IACA,IAAIC,IAAI,GAAG,IAAI,CAACzG,YAAY,CAAC0G,qBAAqB,CAAC,CAAC;IACpD,IAAIC,cAAc,GAAGnE,GAAG,CAAC,MAAM,CAAC,CAACf,KAAK,CAAC,CAAC,CAACmF,UAAU;IACnD;IACA,OAAO;MACHL,IAAI,EAAEE,IAAI,CAACF,IAAI,GAAGM,UAAU,CAACF,cAAc,CAAC,GAAG,IAAI,CAACP,UAAU,CAAC,CAAC;MAChEI,GAAG,EAAEC,IAAI,CAACD,GAAG,GAAG,IAAI,CAACR,SAAS,CAAC;IACnC,CAAC;EACL,CAAC;EACDnG,OAAO,CAAChI,SAAS,CAAC4J,KAAK,GAAG,YAAY;IAClC,IAAI,CAAC,IAAI,CAACzB,YAAY,EAAE;MACpB,OAAO,CAAC,CAAC;IACb;IACA,OAAQ,IAAI,CAACA,YAAY,CAAC8G,YAAY,IAClChI,MAAM,CAACiI,gBAAgB,CAAC,IAAI,CAAC/G,YAAY,CAAC;EAClD,CAAC;EACD;EACAH,OAAO,CAAChI,SAAS,CAAC6C,KAAK,GAAG,YAAY;IAClC,IAAI+G,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IACxB,OAAQ,IAAI,CAACzB,YAAY,CAACgH,WAAW,GACjCH,UAAU,CAACpF,KAAK,CAACwF,WAAW,CAAC,GAC7BJ,UAAU,CAACpF,KAAK,CAACyF,YAAY,CAAC;EACtC,CAAC;EACD;EACArH,OAAO,CAAChI,SAAS,CAAC4C,MAAM,GAAG,YAAY;IACnC,IAAIgH,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IACxB,OAAQ,IAAI,CAACzB,YAAY,CAACmH,YAAY,GAClCN,UAAU,CAACpF,KAAK,CAAC2F,UAAU,CAAC,GAC5BP,UAAU,CAACpF,KAAK,CAAC4F,aAAa,CAAC;EACvC,CAAC;EACDxH,OAAO,CAAC6E,cAAc,GAAG,CAAC,CAAC;EAC3B,OAAO7E,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAAS2C,GAAGA,CAACzF,QAAQ,EAAE;EACnB8B,eAAe,CAAC,CAAC;EACjB,OAAO,IAAIgB,OAAO,CAAC9C,QAAQ,CAAC;AAChC;AAEA,IAAIuK,qBAAqB,GAAG,CACxB,KAAK,EACL,SAAS,EACT,SAAS,EACT,YAAY,EACZ,MAAM,EACN,OAAO,EACP,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,aAAa,EACb,UAAU,EACV,OAAO,EACP,kBAAkB,EAClB,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,QAAQ,EACR,kBAAkB,EAClB,WAAW,CACd;AACD;AACA,SAASC,aAAaA,CAACnF,IAAI,EAAE;EACzB;EACA,IAAIA,IAAI,KAAK,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACAA,IAAI,GAAGA,IAAI,CAACjC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;EAChCiC,IAAI,GAAGA,IAAI,CAACV,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGS,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC;EACnDQ,IAAI,GAAGA,IAAI,CAACjC,OAAO,CAAC,WAAW,EAAE,UAAUqH,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC,CAAC,CAAC,CAACjG,WAAW,CAAC,CAAC;EAAE,CAAC,CAAC;EAC7E,OAAOa,IAAI;AACf;AACA,IAAIqF,KAAK,GAAG;EACR;AACJ;AACA;EACIC,OAAO,EAAE,SAAAA,CAAUxG,EAAE,EAAEpG,SAAS,EAAE6M,OAAO,EAAEC,aAAa,EAAE;IACtD,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,CAAC;IAAE;IACvC,IAAIE,IAAI,GAAGrF,GAAG,CAACtB,EAAE,CAAC;IAClB,IAAI4G,MAAM,GAAGD,IAAI,CAACzF,IAAI,CAAC,cAAc,CAAC,IAAIwF,aAAa;IACvD,IAAI,CAACE,MAAM,EAAE;MACT;IACJ;IACA,IAAIC,iBAAiB,GAAGD,MAAM,CAAC9F,KAAK,CAAC,GAAG,CAAC;IACzC;IACA,IAAI+F,iBAAiB,CAAC,CAAC,CAAC,EAAE;MACtB,IAAIC,MAAM,GAAGlJ,MAAM,CAACmJ,UAAU;MAC9B,KAAK,IAAIzQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuQ,iBAAiB,CAACpQ,MAAM,EAAEH,CAAC,EAAE,EAAE;QAC/C,IAAI0Q,MAAM,GAAGH,iBAAiB,CAACvQ,CAAC,CAAC;QACjC,IAAI2Q,eAAe,GAAGC,QAAQ,CAACF,MAAM,CAAClG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxD,IAAImG,eAAe,GAAGH,MAAM,EAAE;UAC1BF,MAAM,GAAGI,MAAM;UACf;QACJ;QACA;QACA,IAAI1Q,CAAC,KAAKuQ,iBAAiB,CAACpQ,MAAM,GAAG,CAAC,EAAE;UACpCmQ,MAAM,GAAGI,MAAM;QACnB;MACJ;IACJ;IACA,IAAIG,IAAI,GAAGP,MAAM,CAAC9F,KAAK,CAAC,GAAG,CAAC;IAC5B,IAAItH,KAAK,GAAG0N,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACjC,IAAI5N,MAAM,GAAG2N,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAClC,IAAIC,MAAM,GAAGxN,SAAS,CAACJ,KAAK,CAAC,CAAC;IAC9B,IAAI6N,OAAO,GAAGzN,SAAS,CAACL,MAAM,CAAC,CAAC,GAAGkN,OAAO;IAC1C,IAAIa,QAAQ,GAAGnI,IAAI,CAACoI,GAAG,CAACH,MAAM,EAAE5N,KAAK,CAAC;IACtC,IAAIgO,SAAS,GAAGrI,IAAI,CAACoI,GAAG,CAACF,OAAO,EAAE9N,MAAM,CAAC;IACzC,IAAIkO,KAAK,GAAGtI,IAAI,CAACoI,GAAG,CAACD,QAAQ,GAAG9N,KAAK,EAAEgO,SAAS,GAAGjO,MAAM,CAAC;IAC1D,OAAO;MAAEC,KAAK,EAAEA,KAAK,GAAGiO,KAAK;MAAElO,MAAM,EAAEA,MAAM,GAAGkO;IAAM,CAAC;EAC3D,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIC,YAAY,EAAE,SAAAA,CAAU1H,EAAE,EAAEpG,SAAS,EAAE0L,GAAG,EAAEqC,MAAM,EAAEC,SAAS,EAAE;IAC3D,IAAI,CAACA,SAAS,EAAE;MACZ;IACJ;IACA,IAAIjB,IAAI,GAAGrF,GAAG,CAACtB,EAAE,CAAC,CAACqB,IAAI,CAAC,KAAK,CAAC,CAACE,KAAK,CAAC,CAAC;IACtC,IAAI,CAACoF,IAAI,CAAChF,GAAG,CAAC,CAAC,EAAE;MACb;IACJ;IACA,IAAIkG,aAAa,GAAGjO,SAAS,CAAC+H,GAAG,CAAC,CAAC,CAAC6D,qBAAqB,CAAC,CAAC;IAC3D,IAAIsB,MAAM,GAAGe,aAAa,CAACrO,KAAK;IAChC;IACA,IAAIsO,OAAO,GAAGlO,SAAS,CAACL,MAAM,CAAC,CAAC,IAAI+L,GAAG,GAAGqC,MAAM,CAAC;IACjD,IAAII,OAAO,GAAGpB,IAAI,CAACnN,KAAK,CAAC,CAAC;IAC1B,IAAIwO,QAAQ,GAAGrB,IAAI,CAACpN,MAAM,CAAC,CAAC;IAC5B,IAAI0O,OAAO,GAAGtB,IAAI,CAACpG,KAAK,CAAC,CAAC;IAC1B,IAAI2H,CAAC,GAAG,CAACpB,MAAM,GAAGiB,OAAO,IAAI,CAAC,GAC1BpB,IAAI,CAACvB,MAAM,CAAC,CAAC,CAACC,IAAI,IACjBM,UAAU,CAACsC,OAAO,CAAClC,WAAW,CAAC,IAAI,CAAC,CAAC,IACrCJ,UAAU,CAACsC,OAAO,CAACE,UAAU,CAAC,IAAI,CAAC,CAAC,GACrC7G,GAAG,CAAC1D,MAAM,CAAC,CAACsH,UAAU,CAAC,CAAC,GACxB2C,aAAa,CAACxC,IAAI;IACtB,IAAI+C,CAAC,GAAG,CAACN,OAAO,GAAGE,QAAQ,IAAI,CAAC,GAC5BrB,IAAI,CAACvB,MAAM,CAAC,CAAC,CAACE,GAAG,IAChBK,UAAU,CAACsC,OAAO,CAAC/B,UAAU,CAAC,IAAI,CAAC,CAAC,IACpCP,UAAU,CAACsC,OAAO,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC,GACpC/G,GAAG,CAAC1D,MAAM,CAAC,CAACkH,SAAS,CAAC,CAAC,GACvBQ,GAAG;IACP,IAAIgD,GAAG,GAAGP,OAAO,GAAGH,SAAS,CAACpO,KAAK;IACnC,IAAI+O,GAAG,GAAGP,QAAQ,GAAGJ,SAAS,CAACrO,MAAM;IACrC,IAAIiP,SAAS,GAAG,cAAc,IACzBN,CAAC,IAAI,CAAC,CAAC,CAAC,GACT,MAAM,IACLE,CAAC,IAAI,CAAC,CAAC,CAAC,GACT,iBAAiB,GACjBE,GAAG,GACH,IAAI,GACJC,GAAG,GACH,MAAM;IACV,OAAOC,SAAS;EACpB,CAAC;EACDC,eAAe,EAAE,SAAAA,CAAUvM,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEqM,GAAG,EAAEC,WAAW,EAAE;IACrG,IAAIC,KAAK,GAAGD,WAAW,GAAG,SAAS,GAAGA,WAAW,GAAG,GAAG,GAAG,EAAE;IAC5D,OAAO,2DAA2D,GAAGzM,WAAW,GAAG,cAAc,GAAGE,cAAc,GAAG,YAAY,GAAGD,YAAY,GAAG,eAAe,GAAGE,eAAe,GAAG,yEAAyE,GAAGuM,KAAK,GAAG,SAAS,GAAGF,GAAG,GAAG,gEAAgE;EACjW,CAAC;EACDG,YAAY,EAAE,SAAAA,CAAU5M,KAAK,EAAEyM,GAAG,EAAEI,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACjE,IAAIC,UAAU,GAAGH,MAAM,GAAG,WAAW,GAAGA,MAAM,GAAG,IAAI,GAAG,EAAE;IAC1D,IAAII,SAAS,GAAGH,KAAK,GAAG,UAAU,GAAGA,KAAK,GAAG,IAAI,GAAG,EAAE;IACtD,IAAII,SAAS,GAAG,OAAO,GAAGN,OAAO,GAAG,GAAG,GAAGI,UAAU,GAAG,IAAI,GAAGC,SAAS,GAAG,6CAA6C,GAAGlN,KAAK,GAAG,WAAW,GAAGyM,GAAG,GAAG,OAAO;IAC7J,IAAIW,SAAS,GAAG,EAAE;IAClB,IAAIJ,OAAO,EAAE;MACT,IAAIK,SAAS,GAAG,OAAOL,OAAO,KAAK,QAAQ,GAAGM,IAAI,CAACC,KAAK,CAACP,OAAO,CAAC,GAAGA,OAAO;MAC3EI,SAAS,GAAGC,SAAS,CAACG,GAAG,CAAC,UAAUC,MAAM,EAAE;QACxC,IAAI5H,KAAK,GAAG,EAAE;QACd5L,MAAM,CAAC2N,IAAI,CAAC6F,MAAM,CAAC,CAAC5J,OAAO,CAAC,UAAU6J,GAAG,EAAE;UACvC;UACA7H,KAAK,IAAI,GAAG,GAAG6H,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI;QACnD,CAAC,CAAC;QACF,OAAO,UAAU,GAAG7H,KAAK,GAAG,YAAY;MAC5C,CAAC,CAAC;IACN;IACA,OAAO,EAAE,GAAGuH,SAAS,GAAGD,SAAS;EACrC,CAAC;EACD;EACAQ,gBAAgB,EAAE,SAAAA,CAAUC,OAAO,EAAE;IACjC,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAG,EAAE;IACd,IAAIrB,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIpS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuT,OAAO,CAACpT,MAAM,EAAEH,CAAC,EAAE,EAAE;MACrC,IAAI0T,IAAI,GAAGH,OAAO,CAACvT,CAAC,CAAC,CAACwK,KAAK,CAAC,GAAG,CAAC;MAChC;MACA,IAAIkJ,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QAChBA,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB;MACAF,KAAK,CAACtG,IAAI,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC;MACnBF,OAAO,CAACrG,IAAI,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,IAAIlD,MAAM,GAAGlJ,MAAM,CAACmJ,UAAU;IAC9B,KAAK,IAAI1P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyS,OAAO,CAACrT,MAAM,EAAEY,CAAC,EAAE,EAAE;MACrC,IAAI6P,QAAQ,CAAC4C,OAAO,CAACzS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGyP,MAAM,EAAE;QACnC4B,GAAG,GAAGqB,KAAK,CAAC1S,CAAC,CAAC;QACd;MACJ;IACJ;IACA,OAAOqR,GAAG;EACd,CAAC;EACDwB,aAAa,EAAE,SAAAA,CAAUC,GAAG,EAAE;IAC1B,IAAI,CAACA,GAAG,EACJ,OAAO,KAAK;IAChB;IACA;IACA;IACA,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA;IACA;IACA;IACA,IAAID,GAAG,CAACE,YAAY,KAAK,CAAC,EAAE;MACxB,OAAO,KAAK;IAChB;IACA;IACA,OAAO,IAAI;EACf,CAAC;EACDC,oBAAoB,EAAE,SAAAA,CAAUC,OAAO,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,eAAe,EAAEC,QAAQ,EAAE;IAC1F,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,OAAO,EAAE;MAC9BD,UAAU,GAAG,gBAAgB;IACjC,CAAC,MACI,IAAID,QAAQ,IAAIA,QAAQ,CAACG,KAAK,EAAE;MACjCF,UAAU,GAAG,cAAc;IAC/B,CAAC,MACI;MACDA,UAAU,GAAG,cAAc;IAC/B;IACA,OAAO,6BAA6B,GAAGA,UAAU,GAAG,aAAa,GAAGH,cAAc,GAAG,mQAAmQ,GAAGC,eAAe,GAAG,wIAAwI,GAAGA,eAAe,GAAG,0hBAA0hB,IAAIF,QAAQ,IAAI,EAAE,CAAC,GAAG,+DAA+D,GAAGD,OAAO,GAAG,uBAAuB;EACjqC,CAAC;EACDQ,oBAAoB,EAAE,SAAAA,CAAUnR,SAAS,EAAE;IACvC,IAAIoR,QAAQ,GAAGpR,SAAS,CAAC+F,gBAAgB,CAAC,oNAAoN,CAAC;IAC/P,IAAIsL,eAAe,GAAG,EAAE,CAAClK,MAAM,CAAClK,IAAI,CAACmU,QAAQ,EAAE,UAAUE,OAAO,EAAE;MAC9D,IAAI3K,KAAK,GAAG3C,MAAM,CAACiI,gBAAgB,CAACqF,OAAO,CAAC;MAC5C,OAAO3K,KAAK,CAAC4K,OAAO,KAAK,MAAM,IAAI5K,KAAK,CAAC6K,UAAU,KAAK,QAAQ;IACpE,CAAC,CAAC;IACF,OAAOH,eAAe;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,iBAAiB,EAAE,SAAAA,CAAUC,KAAK,EAAExO,UAAU,EAAEtB,wBAAwB,EAAEuB,YAAY,EAAE;IACpF,IAAIwO,eAAe,GAAG,EAAE;IACxB,IAAIC,uBAAuB,GAAGzU,cAAc,CAACqP,qBAAqB,EAAEtJ,UAAU,CAAC;IAC/E,EAAE,CAACgD,OAAO,CAACjJ,IAAI,CAACyU,KAAK,EAAE,UAAUG,IAAI,EAAE;MACnC,IAAI5O,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmV,IAAI,CAAC5J,UAAU,CAACpL,MAAM,EAAEH,CAAC,EAAE,EAAE;QAC7C,IAAI4K,IAAI,GAAGuK,IAAI,CAAC5J,UAAU,CAACvL,CAAC,CAAC;QAC7B,IAAI4K,IAAI,CAACwK,SAAS,EAAE;UAChB,IAAIC,WAAW,GAAGtF,aAAa,CAACnF,IAAI,CAAC0K,IAAI,CAAC;UAC1C,IAAIC,KAAK,GAAG,EAAE;UACd,IAAIL,uBAAuB,CAAClL,OAAO,CAACqL,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;YACnDE,KAAK,GAAGF,WAAW;UACvB;UACA,IAAIE,KAAK,EAAE;YACPhP,SAAS,CAACgP,KAAK,CAAC,GAAG3K,IAAI,CAAChB,KAAK;UACjC;QACJ;MACJ;MACA,IAAI4L,WAAW,GAAGxK,GAAG,CAACmK,IAAI,CAAC;MAC3B,IAAIM,GAAG,GAAGD,WAAW,CAACzK,IAAI,CAAC,KAAK,CAAC,CAACE,KAAK,CAAC,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;MACrD,IAAI0H,KAAK,GAAGkD,WAAW,CAAC5K,IAAI,CAAC,OAAO,CAAC;MACrC,IAAI8K,KAAK,GAAGjP,YAAY,GAClB+O,WAAW,CAAC5K,IAAI,CAACnE,YAAY,CAAC,GAC9B+O,WAAW,CAACzK,IAAI,CAAC,KAAK,CAAC,CAACE,KAAK,CAAC,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;MACjDrE,SAAS,CAACmP,KAAK,GAAGA,KAAK;MACvB,IAAIxQ,wBAAwB,IAAI,CAACqB,SAAS,CAACoP,OAAO,EAAE;QAChDpP,SAAS,CAACoP,OAAO,GAAGrD,KAAK,IAAImD,GAAG,IAAI,EAAE;MAC1C;MACAlP,SAAS,CAACkP,GAAG,GAAGA,GAAG,IAAInD,KAAK,IAAI,EAAE;MAClC2C,eAAe,CAAC9H,IAAI,CAAC5G,SAAS,CAAC;IACnC,CAAC,CAAC;IACF,OAAO0O,eAAe;EAC1B,CAAC;EACDvO,QAAQ,EAAE,SAAAA,CAAA,EAAY;IAClB,OAAO,2BAA2B,CAACkP,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EAChE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAO,EAAE,SAAAA,CAAU3D,GAAG,EAAE4D,YAAY,EAAErQ,KAAK,EAAE;IACzC,IAAI,CAACyM,GAAG,EAAE;MACN,IAAI4D,YAAY,EAAE;QACd,OAAO;UACHC,KAAK,EAAE;QACX,CAAC;MACL,CAAC,MACI;QACDC,OAAO,CAACC,KAAK,CAAC,yDAAyD,IAClExQ,KAAK,GAAG,CAAC,CAAC,GACX,gIAAgI,CAAC;QACrI;MACJ;IACJ;IACA,IAAI4O,OAAO,GAAGnC,GAAG,CAACgE,KAAK,CAAC,8GAA8G,CAAC;IACvI,IAAI5B,KAAK,GAAGpC,GAAG,CAACgE,KAAK,CAAC,wEAAwE,CAAC;IAC/F,IAAIC,MAAM,GAAGjE,GAAG,CAACgE,KAAK,CAAC,0EAA0E,CAAC;IAClG,IAAI7B,OAAO,EAAE;MACT,OAAO;QACHA,OAAO,EAAEA;MACb,CAAC;IACL,CAAC,MACI,IAAIC,KAAK,EAAE;MACZ,OAAO;QACHA,KAAK,EAAEA;MACX,CAAC;IACL,CAAC,MACI,IAAI6B,MAAM,EAAE;MACb,OAAO;QACHA,MAAM,EAAEA;MACZ,CAAC;IACL;EACJ;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAIC,IAAI,GAAG,CAAC;AACZ,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAAC3B,OAAO,EAAE4B,OAAO,EAAE;IACpC,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC9Q,KAAK,GAAG,CAAC;IACd;IACA,IAAI,CAACkB,OAAO,GAAG,EAAE;IACjB;IACA,IAAI,CAAC6P,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,sBAAsB,GAAG;MAC1BjI,GAAG,EAAE,CAAC;MACNqC,MAAM,EAAE;IACZ,CAAC;IACD,IAAI,CAACuD,OAAO,EAAE;MACV,OAAO,IAAI;IACf;IACA0B,IAAI,EAAE;IACN,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC5M,EAAE,GAAGkL,OAAO;IACjB,IAAI,CAACvE,IAAI,GAAGrF,GAAG,CAAC4J,OAAO,CAAC;IACxB,IAAI,CAACsC,gBAAgB,CAACV,OAAO,CAAC;IAC9B,IAAI,CAACW,YAAY,CAAC,CAAC;IACnB;IACA,IAAI,IAAI,CAACC,QAAQ,CAAC9Q,OAAO,IACrB,IAAI,CAAC8Q,QAAQ,CAAC7Q,SAAS,KAAKI,SAAS,IACrC,CAAC/F,KAAK,CAACqM,OAAO,CAAC,IAAI,CAACmK,QAAQ,CAAC7Q,SAAS,CAAC,EAAE;MACzC,MAAM,sEAAsE;IAChF;IACA,IAAI,CAAC8Q,YAAY,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnC,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB;IACA,IAAI,CAACpW,IAAI,CAAC,CAAC;IACX,IAAI,CAACqW,eAAe,CAAC,CAAC;IACtB,OAAO,IAAI;EACf;EACAjB,YAAY,CAAClW,SAAS,CAAC6W,gBAAgB,GAAG,UAAUV,OAAO,EAAE;IACzD;IACA,IAAI,CAACY,QAAQ,GAAGzX,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiD,wBAAwB,CAAC,EAAE4T,OAAO,CAAC;IACzE,IAAI,IAAI,CAACY,QAAQ,CAAC1Q,QAAQ,IACtB,OAAO,IAAI,CAAC0Q,QAAQ,CAAC1Q,QAAQ,KAAK,UAAU,GAC1C,IAAI,CAAC0Q,QAAQ,CAAC1Q,QAAQ,CAAC,CAAC,GACxBuJ,KAAK,CAACvJ,QAAQ,CAAC,CAAC,EAAE;MACpB,IAAIE,cAAc,GAAGjH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyX,QAAQ,CAACxQ,cAAc,CAAC,EAAE,IAAI,CAACwQ,QAAQ,CAACxQ,cAAc,CAAC;MACvG,IAAI,CAACwQ,QAAQ,GAAGzX,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyX,QAAQ,CAAC,EAAExQ,cAAc,CAAC;IACzE;EACJ,CAAC;EACD2P,YAAY,CAAClW,SAAS,CAACkX,iBAAiB,GAAG,YAAY;IACnD,IAAI,IAAI,CAACH,QAAQ,CAACrS,iBAAiB,EAAE;MACjC,IAAI,CAACqS,QAAQ,CAACpS,gBAAgB,GAAG,KAAK;IAC1C;IACA,IAAI,CAAC,IAAI,CAACoS,QAAQ,CAAC/S,QAAQ,EAAE;MACzB,IAAI,CAAC+S,QAAQ,CAAC9S,YAAY,GAAG,KAAK;IACtC;IACA;IACA,IAAI,CAACd,cAAc,GAAG,IAAI,CAAC4T,QAAQ,CAAC5T,cAAc;IAClD;IACA;IACA,IAAI,IAAI,CAAC4T,QAAQ,CAAC9Q,OAAO,EAAE;MACvB,IAAI,CAAC9C,cAAc,GAAG,KAAK;IAC/B;IACA,IAAI,CAAC,IAAI,CAAC4T,QAAQ,CAAC9T,SAAS,EAAE;MAC1B,IAAI,CAAC8T,QAAQ,CAAC9T,SAAS,GAAGwE,QAAQ,CAAC2G,IAAI;IAC3C;IACA;IACA,IAAI,CAAC2I,QAAQ,CAAC/R,OAAO,GAAGwD,IAAI,CAACoI,GAAG,CAAC,IAAI,CAACmG,QAAQ,CAAC/R,OAAO,EAAE,IAAI,CAACgS,YAAY,CAAClX,MAAM,CAAC;EACrF,CAAC;EACDoW,YAAY,CAAClW,SAAS,CAACc,IAAI,GAAG,YAAY;IACtC,IAAI0L,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC4K,iBAAiB,CAAC,IAAI,CAACJ,YAAY,CAAC;IACzC,IAAI,CAACK,cAAc,CAAC,CAAC;IACrB,IAAI,CAACrH,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACE,IAAI,EAAE;MAC7BwW,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,IAAI,CAACP,QAAQ,CAACxS,QAAQ,EAAE;MACxB,IAAI,CAACA,QAAQ,CAAC,CAAC;IACnB;IACAgT,UAAU,CAAC,YAAY;MACnB/K,KAAK,CAACxG,UAAU,CAAC,CAAC;MAClBwG,KAAK,CAACzG,WAAW,CAAC,CAAC;MACnByG,KAAK,CAACgL,kBAAkB,CAAC,CAAC;IAC9B,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,IAAI,CAACV,QAAQ,CAACnS,UAAU,EAAE;MAC1B,IAAI,CAACA,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACmS,QAAQ,CAAC9Q,OAAO,EAAE;MACxB,IAAI,CAACyR,sBAAsB,CAAC,CAAC;IACjC;EACJ,CAAC;EACDxB,YAAY,CAAClW,SAAS,CAAC0X,sBAAsB,GAAG,YAAY;IACxD,IAAIlL,KAAK,GAAG,IAAI;IAChB,IAAImL,OAAO,GAAG,SAAAA,CAAUrS,KAAK,EAAE;MAC3B,IAAIiP,OAAO,GAAGqD,MAAM,CAACjD,KAAK,CAACrP,KAAK,CAAC;MACjC,IAAIuS,QAAQ,GAAGlN,GAAG,CAAC4J,OAAO,CAAC;MAC3B;MACA;MACA,IAAIuD,IAAI,GAAG9P,OAAO,CAACK,YAAY,CAAC,CAAC;MACjCwP,QAAQ,CACHtN,IAAI,CAAC,YAAY,EAAEuN,IAAI,CAAC,CACxBrL,EAAE,CAAC,sBAAsB,GAAGqL,IAAI,EAAE,UAAUzN,CAAC,EAAE;QAChDA,CAAC,CAAC0N,cAAc,CAAC,CAAC;QAClB,IAAIC,gBAAgB,GAAGxL,KAAK,CAACuK,QAAQ,CAACzR,KAAK,IAAIA,KAAK;QACpDkH,KAAK,CAACyL,WAAW,CAACD,gBAAgB,EAAEzD,OAAO,CAAC;MAChD,CAAC,CAAC;IACN,CAAC;IACD,IAAIqD,MAAM,GAAG,IAAI;IACjB;IACA,KAAK,IAAItS,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACqP,KAAK,CAAC7U,MAAM,EAAEwF,KAAK,EAAE,EAAE;MACpDqS,OAAO,CAACrS,KAAK,CAAC;IAClB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI4Q,YAAY,CAAClW,SAAS,CAAC8W,YAAY,GAAG,YAAY;IAC9C,IAAItK,KAAK,GAAG,IAAI;IAChB,IAAI,CAACuK,QAAQ,CAACvQ,OAAO,CAAC2C,OAAO,CAAC,UAAU+O,MAAM,EAAE;MAC5C1L,KAAK,CAAChG,OAAO,CAACsG,IAAI,CAAC,IAAIoL,MAAM,CAAC1L,KAAK,EAAE7B,GAAG,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN,CAAC;EACDuL,YAAY,CAAClW,SAAS,CAACmX,eAAe,GAAG,YAAY;IACjD,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACpU,UAAU,EAAE;MAC3BkT,OAAO,CAACC,KAAK,CAAC,oCAAoC,CAAC;IACvD,CAAC,MACI,IAAI,IAAI,CAACiB,QAAQ,CAACpU,UAAU,KAAK,oBAAoB,EAAE;MACxDkT,OAAO,CAACsC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAACpB,QAAQ,CAACpU,UAAU,GAAG,8CAA8C,CAAC;IAC9G;EACJ,CAAC;EACDuT,YAAY,CAAClW,SAAS,CAACoY,YAAY,GAAG,UAAU9S,KAAK,EAAE;IACnD,OAAOqF,GAAG,CAAC,IAAI,CAAC0N,cAAc,CAAC/S,KAAK,CAAC,CAAC;EAC1C,CAAC;EACD4Q,YAAY,CAAClW,SAAS,CAACqY,cAAc,GAAG,UAAU/S,KAAK,EAAE;IACrD,OAAO,WAAW,GAAG,IAAI,CAAC2Q,IAAI,GAAG,GAAG,GAAG3Q,KAAK;EAChD,CAAC;EACD4Q,YAAY,CAAClW,SAAS,CAACsY,SAAS,GAAG,UAAUC,EAAE,EAAE;IAC7C,OAAOA,EAAE,GAAG,GAAG,GAAG,IAAI,CAACtC,IAAI;EAC/B,CAAC;EACDC,YAAY,CAAClW,SAAS,CAACwY,cAAc,GAAG,UAAUD,EAAE,EAAE;IAClD,OAAO5N,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC2N,SAAS,CAACC,EAAE,CAAC,CAAC;EACxC,CAAC;EACDrC,YAAY,CAAClW,SAAS,CAACyY,0BAA0B,GAAG,YAAY;IAC5D,IAAI,IAAI,CAACzB,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAC4Y,KAAK,CAAC5V,QAAQ,CAAC,gBAAgB,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAAC4V,KAAK,CAAC1M,WAAW,CAAC,gBAAgB,CAAC;IAC5C;EACJ,CAAC;EACDkK,YAAY,CAAClW,SAAS,CAACqX,cAAc,GAAG,YAAY;IAChD,IAAI7K,KAAK,GAAG,IAAI;IAChB,IAAIvJ,SAAS,GAAG,IAAI,CAAC0V,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC3N,GAAG,CAAC,CAAC;IACxD,IAAI/H,SAAS,EAAE;MACX;IACJ;IACA,IAAIwB,QAAQ,GAAG,EAAE;IACjB,IAAImU,WAAW,GAAG,EAAE;IACpB;IACA,IAAI,IAAI,CAAC7B,QAAQ,CAACtS,QAAQ,EAAE;MACxBA,QAAQ,GAAG,+BAA+B,GAAG,IAAI,CAAC6T,SAAS,CAAC,SAAS,CAAC,GAAG,kBAAkB,GAAG,IAAI,CAACvB,QAAQ,CAACtQ,OAAO,CAAC,eAAe,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACsQ,QAAQ,CAAC1R,QAAQ,GAAG,2DAA2D,GAAG,IAAI,CAACiT,SAAS,CAAC,SAAS,CAAC,GAAG,kBAAkB,GAAG,IAAI,CAACvB,QAAQ,CAACtQ,OAAO,CAAC,WAAW,CAAC,GAAG,gCAAgC,GAAG,IAAI,CAACsQ,QAAQ,CAAC3R,QAAQ,GAAG,YAAY;IACna;IACA,IAAI,IAAI,CAAC2R,QAAQ,CAACjS,eAAe,KAAK,UAAU,EAAE;MAC9C8T,WAAW,GACP,kEAAkE;IAC1E;IACA,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAI,IAAI,CAAC9B,QAAQ,CAACvT,iBAAiB,EAAE;MACjC;MACAqV,UAAU,IAAI,mBAAmB;IACrC;IACA,IAAIjV,cAAc,GAAG,IAAI,CAACmT,QAAQ,CAACnT,cAAc,GAC3C,mBAAmB,GAAG,IAAI,CAACmT,QAAQ,CAACnT,cAAc,GAAG,GAAG,GACxD,EAAE;IACR,IAAIC,eAAe,GAAG,IAAI,CAACkT,QAAQ,CAAClT,eAAe,GAC7C,oBAAoB,GAAG,IAAI,CAACkT,QAAQ,CAAClT,eAAe,GAAG,GAAG,GAC1D,EAAE;IACR,IAAIiV,kBAAkB,GAAG,eAAe,GAAG,IAAI,CAAC/B,QAAQ,CAACjU,QAAQ,GAAG,GAAG,IAAI2E,QAAQ,CAAC2G,IAAI,KAAK,IAAI,CAAC2I,QAAQ,CAAC9T,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IACxI,IAAI8V,SAAS,GAAG,IAAI,CAAChC,QAAQ,CAAC/S,QAAQ,IAAI,IAAI,CAAC+S,QAAQ,CAAC5S,aAAa,GAC/D,uCAAuC,GAAG,IAAI,CAAC4S,QAAQ,CAACtQ,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC6R,SAAS,CAAC,UAAU,CAAC,GAAG,yCAAyC,GACrK,EAAE;IACR,IAAIU,YAAY,GAAG,IAAI,CAACjC,QAAQ,CAAC3S,gBAAgB,GAC3C,uCAAuC,GAAG,IAAI,CAAC2S,QAAQ,CAACtQ,OAAO,CAAC,gBAAgB,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC6R,SAAS,CAAC,aAAa,CAAC,GAAG,4CAA4C,GAC7K,EAAE;IACR,IAAIW,QAAQ,GAAG,yBAAyB,GAAGH,kBAAkB,GAAG,UAAU,GAAG,IAAI,CAACR,SAAS,CAAC,cAAc,CAAC,GAAG,yCAAyC,GAAG1U,cAAc,GAAG,GAAG,GAAGC,eAAe,GAAG,qDAAqD,GAAG,IAAI,CAACyU,SAAS,CAAC,aAAa,CAAC,GAAG,2DAA2D,GAAG,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC,GAAG,wDAAwD,GAAGO,UAAU,GAAG,kCAAkC,GAAG,IAAI,CAACP,SAAS,CAAC,YAAY,CAAC,GAAG,sDAAsD,GAAG,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC,GAAG,kEAAkE,GAAG7T,QAAQ,GAAG,oDAAoD,GAAG,IAAI,CAAC6T,SAAS,CAAC,YAAY,CAAC,GAAG,yDAAyD,GAAGU,YAAY,GAAG,wBAAwB,GAAGD,SAAS,GAAG,oDAAoD,IAAI,IAAI,CAAChC,QAAQ,CAACjS,eAAe,KAAK,WAAW,GAC/8B8T,WAAW,GACX,EAAE,CAAC,GAAG,8BAA8B,GAAG,IAAI,CAACN,SAAS,CAAC,eAAe,CAAC,GAAG,mDAAmD,IAAI,IAAI,CAACvB,QAAQ,CAACjS,eAAe,KAAK,cAAc,GAChL8T,WAAW,GACX,EAAE,CAAC,GAAG,wEAAwE;IACpFjO,GAAG,CAAC,IAAI,CAACoM,QAAQ,CAAC9T,SAAS,CAAC,CAAC8K,MAAM,CAACkL,QAAQ,CAAC;IAC7C,IAAIxR,QAAQ,CAAC2G,IAAI,KAAK,IAAI,CAAC2I,QAAQ,CAAC9T,SAAS,EAAE;MAC3C0H,GAAG,CAAC,IAAI,CAACoM,QAAQ,CAAC9T,SAAS,CAAC,CAACsJ,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;IAC5D;IACA,IAAI,CAACmM,KAAK,GAAG,IAAI,CAACF,cAAc,CAAC,UAAU,CAAC;IAC5C,IAAI,CAACU,aAAa,GAAG,IAAI,CAACV,cAAc,CAAC,eAAe,CAAC;IACzD,IAAI,CAACW,SAAS,GAAG,IAAI,CAACX,cAAc,CAAC,aAAa,CAAC;IACnD,IAAI,CAACG,UAAU,GAAG,IAAI,CAACH,cAAc,CAAC,cAAc,CAAC;IACrD,IAAI,CAACY,MAAM,GAAG,IAAI,CAACZ,cAAc,CAAC,UAAU,CAAC;IAC7C,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACb,cAAc,CAAC,YAAY,CAAC;IACjD,IAAI,CAACc,QAAQ,GAAG,IAAI,CAACd,cAAc,CAAC,YAAY,CAAC;IACjD,IAAI,CAACW,SAAS,CAAC5M,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACwK,QAAQ,CAAC/T,gBAAgB,GAAG,IAAI,CAAC;IAChF,IAAIuW,eAAe,GAAG,IAAI,CAACxC,QAAQ,CAACvU,IAAI,GAAG,GAAG;IAC9C,IAAI,CAACiW,0BAA0B,CAAC,CAAC;IACjC,IAAI,IAAI,CAAC1B,QAAQ,CAAC/Q,UAAU,EAAE;MAC1BuT,eAAe,IAAI,UAAU;IACjC;IACA,IAAI,CAACb,KAAK,CAAC5V,QAAQ,CAACyW,eAAe,CAAC;IACpC,IAAI,CAACH,MAAM,CAAC7M,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACwK,QAAQ,CAACtU,MAAM,CAAC;IACnE,IAAI,CAAC2W,MAAM,CAAC7M,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACwK,QAAQ,CAACrU,KAAK,GAAG,IAAI,CAAC;IAClE,IAAI,IAAI,CAACqU,QAAQ,CAACpR,QAAQ,EAAE;MACxB,IAAI,CAAC2T,QAAQ,CAACvL,MAAM,CAAC,UAAU,GAAG,IAAI,CAACuK,SAAS,CAAC,aAAa,CAAC,GAAG,qDAAqD,GAAG,IAAI,CAACvB,QAAQ,CAACtQ,OAAO,CAAC,UAAU,CAAC,GAAG,gDAAgD,CAAC;IACnN;IACA,IAAI,CAACb,OAAO,CAAC,CAAC;IACd+E,GAAG,CAAC1D,MAAM,CAAC,CAACwF,EAAE,CAAC,kBAAkB,GAAG,IAAI,CAACwJ,IAAI,GAAG,8BAA8B,GAAG,IAAI,CAACA,IAAI,EAAE,YAAY;MACpGzJ,KAAK,CAACgN,eAAe,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC/S,cAAc,CAAC,CAAC;IACrB,IAAI,CAACgT,WAAW,CAAC,CAAC;EACtB,CAAC;EACDzD,YAAY,CAAClW,SAAS,CAACwZ,eAAe,GAAG,YAAY;IACjD,IAAI,IAAI,CAACpD,QAAQ,EAAE;MACf,IAAIwD,kBAAkB,GAAG,IAAI,CAAC5C,YAAY,CAAC,IAAI,CAAC1R,KAAK,CAAC;MACtD,IAAIuU,gBAAgB,GAAGD,kBAAkB,CAACC,gBAAgB;MAC1D,IAAI,CAACjD,sBAAsB,GAAG,IAAI,CAACkD,yBAAyB,CAAC,CAAC;MAC9D,IAAIC,EAAE,GAAG,IAAI,CAACnD,sBAAsB;QAAEoD,KAAK,GAAGD,EAAE,CAACpL,GAAG;QAAEqC,MAAM,GAAG+I,EAAE,CAAC/I,MAAM;MACxE,IAAI,CAACiJ,gBAAgB,GAAGrK,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC8E,KAAK,CAAC,IAAI,CAACrP,KAAK,CAAC,EAAE,IAAI,CAACoT,KAAK,EAAEsB,KAAK,GAAGhJ,MAAM,EAAE6I,gBAAgB,IAAI,IAAI,CAAC9C,QAAQ,CAACtT,YAAY,CAAC;MACzI,IAAIoW,gBAAgB,EAAE;QAClB,IAAI,CAACK,gBAAgB,CAAC,IAAI,CAAC5U,KAAK,EAAE,IAAI,CAAC2U,gBAAgB,CAAC;MAC5D;MACA,IAAI,IAAI,CAAC9W,cAAc,IAAI,CAAC,IAAI,CAACuT,mBAAmB,EAAE;QAClD,IAAIyD,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACH,gBAAgB,CAAC;QAC5D,IAAI,CAACvB,KAAK,CACLhO,IAAI,CAAC,2BAA2B,CAAC,CACjCE,KAAK,CAAC,CAAC,CACPL,IAAI,CAAC,OAAO,EAAE4P,QAAQ,CAAC;MAChC;MACA,IAAI,CAACnK,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACI,eAAe,CAAC;IAC/C;EACJ,CAAC;EACDkV,YAAY,CAAClW,SAAS,CAACka,gBAAgB,GAAG,UAAU5U,KAAK,EAAE2L,SAAS,EAAE;IAClE,IAAIoJ,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAACrJ,SAAS,CAAC;IACpD,IAAIsJ,YAAY,GAAG,IAAI,CAACnC,YAAY,CAAC9S,KAAK,CAAC;IAC3CiV,YAAY,CAAC7P,IAAI,CAAC,gBAAgB,CAAC,CAACH,IAAI,CAAC,OAAO,EAAE8P,YAAY,CAAC;EACnE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInE,YAAY,CAAClW,SAAS,CAACiB,YAAY,GAAG,UAAU0T,KAAK,EAAErP,KAAK,EAAE;IAC1D,IAAI,IAAI,CAACA,KAAK,GAAGqP,KAAK,CAAC7U,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACwF,KAAK,GAAGqP,KAAK,CAAC7U,MAAM,GAAG,CAAC;IACjC;IACA,IAAI6U,KAAK,CAAC7U,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAACwF,KAAK,GAAG,CAAC;IAClB;IACA,IAAI,CAACqP,KAAK,CAAC7U,MAAM,EAAE;MACf,IAAI,CAAC4G,YAAY,CAAC,CAAC;MACnB;IACJ;IACA,IAAI8T,UAAU,GAAG,IAAI,CAACxD,YAAY,CAAC1R,KAAK,CAAC,CAACyM,GAAG;IAC7C,IAAI,CAACiF,YAAY,GAAGrC,KAAK;IACzB,IAAI,CAAC8F,cAAc,CAAC,CAAC;IACrB,IAAI,CAACrB,MAAM,CAAClL,KAAK,CAAC,CAAC;IACnB,IAAI,CAACqI,iBAAiB,GAAG,EAAE;IAC3B,IAAImE,MAAM,GAAG,CAAC;IACd;IACA,IAAI,CAAC1D,YAAY,CAAC2D,IAAI,CAAC,UAAUC,WAAW,EAAEC,SAAS,EAAE;MACrD,IAAID,WAAW,CAAC7I,GAAG,KAAKyI,UAAU,EAAE;QAChCE,MAAM,GAAGG,SAAS;QAClB,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB,CAAC,CAAC;IACF,IAAI,CAACtE,iBAAiB,GAAG,IAAI,CAACuE,kBAAkB,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACK,WAAW,CAACL,MAAM,EAAE,IAAI,CAAC;IAC9B,IAAI,CAACtC,YAAY,CAACsC,MAAM,CAAC,CAAC5X,QAAQ,CAAC,YAAY,CAAC;IAChD,IAAI,CAACwC,KAAK,GAAGoV,MAAM;IACnB,IAAI,CAACM,oBAAoB,CAACN,MAAM,CAAC;IACjC,IAAI,CAAC1K,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACK,YAAY,CAAC;EAC5C,CAAC;EACD;EACAiV,YAAY,CAAClW,SAAS,CAACiX,QAAQ,GAAG,YAAY;IAC1C;IACA,IAAI,CAACtC,KAAK,GAAG,EAAE;IACf,IAAI,CAAC,IAAI,CAACoC,QAAQ,CAAC9Q,OAAO,EAAE;MACxB,IAAI,IAAI,CAAC8Q,QAAQ,CAAC7R,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAI,CAACyP,KAAK,CAAC7H,IAAI,CAAC,IAAI,CAACzD,EAAE,CAAC;MAC5B,CAAC,MACI,IAAI,IAAI,CAAC0N,QAAQ,CAAC7R,QAAQ,EAAE;QAC7B,IAAI,OAAO,IAAI,CAAC6R,QAAQ,CAAC7R,QAAQ,KAAK,QAAQ,EAAE;UAC5C,IAAI,IAAI,CAAC6R,QAAQ,CAAC5R,YAAY,EAAE;YAC5B,IAAIA,YAAY,GAAGwF,GAAG,CAAC,IAAI,CAACoM,QAAQ,CAAC5R,YAAY,CAAC;YAClD,IAAI,CAACwP,KAAK,GAAGxP,YAAY,CACpBuF,IAAI,CAAC,IAAI,CAACqM,QAAQ,CAAC7R,QAAQ,CAAC,CAC5B8F,GAAG,CAAC,CAAC;UACd,CAAC,MACI;YACD,IAAI,CAAC2J,KAAK,GAAG,IAAI,CAACtL,EAAE,CAACL,gBAAgB,CAAC,IAAI,CAAC+N,QAAQ,CAAC7R,QAAQ,CAAC;UACjE;QACJ,CAAC,MACI;UACD,IAAI,CAACyP,KAAK,GAAG,IAAI,CAACoC,QAAQ,CAAC7R,QAAQ;QACvC;MACJ,CAAC,MACI;QACD,IAAI,CAACyP,KAAK,GAAG,IAAI,CAACtL,EAAE,CAAC4R,QAAQ;MACjC;MACA,OAAOrL,KAAK,CAAC8E,iBAAiB,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACoC,QAAQ,CAAC5Q,UAAU,EAAE,IAAI,CAAC4Q,QAAQ,CAAClS,wBAAwB,EAAE,IAAI,CAACkS,QAAQ,CAAC3Q,YAAY,CAAC;IAC5I,CAAC,MACI;MACD,OAAO,IAAI,CAAC2Q,QAAQ,CAAC7Q,SAAS,IAAI,EAAE;IACxC;EACJ,CAAC;EACDgQ,YAAY,CAAClW,SAAS,CAACkb,mBAAmB,GAAG,YAAY;IACrD,OAAQ,IAAI,CAACnE,QAAQ,CAAChT,aAAa,IAC/B0D,QAAQ,CAAC2G,IAAI,KAAK,IAAI,CAAC2I,QAAQ,CAAC9T,SAAS;EACjD,CAAC;EACDiT,YAAY,CAAClW,SAAS,CAAC+D,aAAa,GAAG,YAAY;IAC/C,IAAI,CAAC,IAAI,CAACmX,mBAAmB,CAAC,CAAC,EAAE;MAC7B;IACJ;IACA,IAAI,CAACzE,gBAAgB,GAAGzH,UAAU,CAACrE,GAAG,CAAC,MAAM,CAAC,CAACf,KAAK,CAAC,CAAC,CAACyF,YAAY,CAAC;IACpE,IAAI8L,QAAQ,GAAG1T,QAAQ,CAAC4G,eAAe,CAACQ,qBAAqB,CAAC,CAAC;IAC/D,IAAIuM,cAAc,GAAGnU,MAAM,CAACmJ,UAAU,GAAG+K,QAAQ,CAACtY,KAAK;IACvD8H,GAAG,CAAClD,QAAQ,CAAC2G,IAAI,CAAC,CAAC7B,GAAG,CAAC,eAAe,EAAE6O,cAAc,GAAG,IAAI,CAAC3E,gBAAgB,GAAG,IAAI,CAAC;IACtF9L,GAAG,CAAClD,QAAQ,CAAC2G,IAAI,CAAC,CAACtL,QAAQ,CAAC,iBAAiB,CAAC;EAClD,CAAC;EACDoT,YAAY,CAAClW,SAAS,CAACqb,cAAc,GAAG,YAAY;IAChD,IAAI,CAAC,IAAI,CAACH,mBAAmB,CAAC,CAAC,EAAE;MAC7B;IACJ;IACAvQ,GAAG,CAAClD,QAAQ,CAAC2G,IAAI,CAAC,CAAC7B,GAAG,CAAC,eAAe,EAAE,IAAI,CAACkK,gBAAgB,GAAG,IAAI,CAAC;IACrE9L,GAAG,CAAClD,QAAQ,CAAC2G,IAAI,CAAC,CAACpC,WAAW,CAAC,iBAAiB,CAAC;EACrD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkK,YAAY,CAAClW,SAAS,CAACiY,WAAW,GAAG,UAAU3S,KAAK,EAAEiP,OAAO,EAAE;IAC3D,IAAI/H,KAAK,GAAG,IAAI;IAChB,IAAIlH,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI,CAACyR,QAAQ,CAACzR,KAAK;IAAE;IACrD;IACA,IAAI,IAAI,CAAC8Q,QAAQ,EACb;IACJ,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACsC,KAAK,CAAC1M,WAAW,CAAC,eAAe,CAAC;IACvC,IAAI,CAACjI,aAAa,CAAC,CAAC;IACpB;IACA,IAAI,CAAC4U,UAAU,CAAC7V,QAAQ,CAAC,SAAS,CAAC;IACnC,IAAIwY,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,CAACjW,KAAK,EAAEA,KAAK,CAAC;IACzE,IAAI,CAACiR,iBAAiB,GAAG+E,sBAAsB;IAC/C,IAAI3G,KAAK,GAAG,EAAE;IACd2G,sBAAsB,CAACnS,OAAO,CAAC,UAAU2L,IAAI,EAAE;MAC3CH,KAAK,GAAGA,KAAK,IAAI,YAAY,GAAGG,IAAI,GAAG,6BAA6B,CAAC;IACzE,CAAC,CAAC;IACF,IAAI,CAACsE,MAAM,CAACrL,MAAM,CAAC4G,KAAK,CAAC;IACzB,IAAI,CAAC6G,OAAO,CAAClW,KAAK,CAAC;IACnB,IAAIuM,SAAS,GAAG,EAAE;IAClB,IAAI,CAAC+E,sBAAsB,GAAG,IAAI,CAACkD,yBAAyB,CAAC,CAAC;IAC9D,IAAIC,EAAE,GAAG,IAAI,CAACnD,sBAAsB;MAAEjI,GAAG,GAAGoL,EAAE,CAACpL,GAAG;MAAEqC,MAAM,GAAG+I,EAAE,CAAC/I,MAAM;IACtE,IAAI,CAAC,IAAI,CAAC+F,QAAQ,CAACvT,iBAAiB,EAAE;MAClC,IAAI,CAACiY,yBAAyB,CAAC9M,GAAG,EAAEqC,MAAM,CAAC;IAC/C;IACA,IAAI6I,gBAAgB,GAAG,IAAI,CAAC7C,YAAY,CAAC1R,KAAK,CAAC,CAACuU,gBAAgB;IAChE,IAAI,IAAI,CAAC1W,cAAc,IAAIoR,OAAO,EAAE;MAChC,IAAI,CAAC0F,gBAAgB,GAAGrK,KAAK,CAACC,OAAO,CAAC0E,OAAO,EAAE,IAAI,CAACmE,KAAK,EAAE/J,GAAG,GAAGqC,MAAM,EAAE6I,gBAAgB,IAAI,IAAI,CAAC9C,QAAQ,CAACtT,YAAY,CAAC;MACxHoO,SAAS,GAAGjC,KAAK,CAACmB,YAAY,CAACwD,OAAO,EAAE,IAAI,CAACmE,KAAK,EAAE/J,GAAG,EAAEqC,MAAM,EAAE,IAAI,CAACiJ,gBAAgB,CAAC;IAC3F;IACA,IAAI,CAAC,IAAI,CAAC9W,cAAc,IAAI,CAAC0O,SAAS,EAAE;MACpC,IAAI,CAAC6G,KAAK,CAAC5V,QAAQ,CAAC,IAAI,CAACiU,QAAQ,CAAChU,UAAU,CAAC;MAC7C,IAAI,CAACqV,YAAY,CAAC9S,KAAK,CAAC,CAAC0G,WAAW,CAAC,aAAa,CAAC;IACvD;IACA,IAAI0P,OAAO,GAAG,IAAI,CAAC3E,QAAQ,CAAC5T,cAAc,GACpC,GAAG,GACH,IAAI,CAAC4T,QAAQ,CAAC/T,gBAAgB;IACpCuU,UAAU,CAAC,YAAY;MACnB/K,KAAK,CAACkM,KAAK,CAAC5V,QAAQ,CAAC,oBAAoB,CAAC;IAC9C,CAAC,EAAE4Y,OAAO,CAAC;IACX,IAAI,CAACpW,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC0K,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACO,UAAU,CAAC;IACtC;IACA,IAAI,CAACiX,YAAY,CAAC9S,KAAK,CAAC,CAACxC,QAAQ,CAAC,YAAY,CAAC;IAC/C,IAAI,CAACuT,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACG,aAAa,GAAG7L,GAAG,CAAC1D,MAAM,CAAC,CAACkH,SAAS,CAAC,CAAC;IAC5CoJ,UAAU,CAAC,YAAY;MACnB;MACA;MACA,IAAI/K,KAAK,CAACrJ,cAAc,IAAI0O,SAAS,EAAE;QACnC,IAAI8J,cAAc,GAAGnP,KAAK,CAAC4L,YAAY,CAAC9S,KAAK,CAAC;QAC9CqW,cAAc,CAACpP,GAAG,CAAC,WAAW,EAAEsF,SAAS,CAAC;QAC1C0F,UAAU,CAAC,YAAY;UACnBoE,cAAc,CACT7Y,QAAQ,CAAC,yCAAyC,CAAC,CACnDyJ,GAAG,CAAC,qBAAqB,EAAEC,KAAK,CAACuK,QAAQ,CAAC7T,sBAAsB,GAAG,IAAI,CAAC;UAC7EsJ,KAAK,CAACkM,KAAK,CAAC5V,QAAQ,CAAC,oBAAoB,CAAC;QAC9C,CAAC,CAAC;QACFyU,UAAU,CAAC,YAAY;UACnBoE,cAAc,CAACpP,GAAG,CAAC,WAAW,EAAE,sBAAsB,CAAC;QAC3D,CAAC,EAAE,GAAG,CAAC;MACX;MACAgL,UAAU,CAAC,YAAY;QACnB/K,KAAK,CAAC2M,SAAS,CAACrW,QAAQ,CAAC,IAAI,CAAC;QAC9B0J,KAAK,CAACmM,UAAU,CAAC7V,QAAQ,CAAC,YAAY,CAAC;MAC3C,CAAC,EAAE,EAAE,CAAC;MACNyU,UAAU,CAAC,YAAY;QACnB,IAAI/K,KAAK,CAACuK,QAAQ,CAACvS,SAAS,IACxBiD,QAAQ,CAAC2G,IAAI,KAAK5B,KAAK,CAACuK,QAAQ,CAAC9T,SAAS,EAAE;UAC5CuJ,KAAK,CAAChI,SAAS,CAAC,CAAC;QACrB;MACJ,CAAC,EAAEgI,KAAK,CAACuK,QAAQ,CAAC/T,gBAAgB,GAAG,EAAE,CAAC;MACxC;MACA,IAAI,CAACwJ,KAAK,CAACrJ,cAAc,IAAI,CAAC0O,SAAS,EAAE;QACrC0F,UAAU,CAAC,YAAY;UACnB/K,KAAK,CAACkM,KAAK,CAAC5V,QAAQ,CAAC,YAAY,CAAC;QACtC,CAAC,EAAE0J,KAAK,CAACuK,QAAQ,CAAC/T,gBAAgB,CAAC;MACvC;MACA;MACAwJ,KAAK,CAACoP,KAAK,CAACtW,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACvCkH,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACQ,SAAS,CAAC;IAC1C,CAAC,CAAC;IACF,IAAIqG,QAAQ,CAAC2G,IAAI,KAAK,IAAI,CAAC2I,QAAQ,CAAC9T,SAAS,EAAE;MAC3C0H,GAAG,CAAC,MAAM,CAAC,CAAC7H,QAAQ,CAAC,OAAO,CAAC;IACjC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIoT,YAAY,CAAClW,SAAS,CAAC8Z,yBAAyB,GAAG,YAAY;IAC3D,IAAI,IAAI,CAAC/C,QAAQ,CAACvT,iBAAiB,EAAE;MACjC,OAAO;QACHmL,GAAG,EAAE,CAAC;QACNqC,MAAM,EAAE;MACZ,CAAC;IACL;IACA,IAAIrC,GAAG,GAAG,IAAI,CAAC2K,QAAQ,CAACtO,GAAG,CAAC,CAAC,CAACsE,YAAY,IAAI,CAAC;IAC/C,IAAIgG,OAAO,GAAG,IAAI,CAACoD,KAAK,CAAChO,IAAI,CAAC,6BAA6B,CAAC,CAACM,GAAG,CAAC,CAAC;IAClE,IAAI6Q,aAAa,GAAG,IAAI,CAAC9E,QAAQ,CAACpT,oBAAoB,IACjD2R,OAAO,IAAIA,OAAO,CAAChG,YAAa,IACjC,CAAC;IACL,IAAIwM,cAAc,GAAG,IAAI,CAACpD,KAAK,CAAChO,IAAI,CAAC,iBAAiB,CAAC,CAACM,GAAG,CAAC,CAAC;IAC7D,IAAI+Q,WAAW,GAAGD,cAAc,GAAGA,cAAc,CAACxM,YAAY,GAAG,CAAC;IAClE,IAAI0B,MAAM,GAAG+K,WAAW,GAAGF,aAAa;IACxC,OAAO;MACHlN,GAAG,EAAEA,GAAG;MACRqC,MAAM,EAAEA;IACZ,CAAC;EACL,CAAC;EACDkF,YAAY,CAAClW,SAAS,CAACyb,yBAAyB,GAAG,UAAU9M,GAAG,EAAEqC,MAAM,EAAE;IACtE,IAAIrC,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAG,CAAC;IAAE;IAC/B,IAAIqC,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,CAAC;IAAE;IACrC,IAAI,CAACqI,QAAQ,CAAC9M,GAAG,CAAC,KAAK,EAAEoC,GAAG,GAAG,IAAI,CAAC,CAACpC,GAAG,CAAC,QAAQ,EAAEyE,MAAM,GAAG,IAAI,CAAC;EACrE,CAAC;EACDkF,YAAY,CAAClW,SAAS,CAACyZ,QAAQ,GAAG,YAAY;IAC1C,IAAIjN,KAAK,GAAG,IAAI;IAChB;IACA+K,UAAU,CAAC,YAAY;MACnB/K,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,eAAe,CAAC;MACxC,IAAIQ,KAAK,CAACuK,QAAQ,CAAC3T,aAAa,GAAG,CAAC,EAAE;QAClCoJ,KAAK,CAACkM,KAAK,CAACjM,EAAE,CAAC,qCAAqC,EAAE,YAAY;UAC9DD,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,eAAe,CAAC;UACxCgQ,YAAY,CAACxP,KAAK,CAACyP,cAAc,CAAC;UAClC;UACAzP,KAAK,CAACyP,cAAc,GAAG1E,UAAU,CAAC,YAAY;YAC1C/K,KAAK,CAACkM,KAAK,CAAC5V,QAAQ,CAAC,eAAe,CAAC;UACzC,CAAC,EAAE0J,KAAK,CAACuK,QAAQ,CAAC3T,aAAa,CAAC;QACpC,CAAC,CAAC;QACFoJ,KAAK,CAACkM,KAAK,CAACtL,OAAO,CAAC,cAAc,CAAC;MACvC;IACJ,CAAC,EAAE,IAAI,CAAC2J,QAAQ,CAAC1T,aAAa,CAAC;EACnC,CAAC;EACD6S,YAAY,CAAClW,SAAS,CAACkc,eAAe,GAAG,UAAUC,IAAI,EAAE;IACrD,IAAI,IAAI,CAACpF,QAAQ,CAACxT,oBAAoB,EAAE;MACpC,IAAI;QACA6Y,WAAW,CAAC;UACR/H,QAAQ,EAAE,CAAC8H,IAAI,CAACnR,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC;MACN,CAAC,CACD,OAAOX,CAAC,EAAE;QACNwL,OAAO,CAACsC,IAAI,CAAC,oJAAoJ,CAAC;MACtK;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACIjC,YAAY,CAAClW,SAAS,CAAC4F,OAAO,GAAG,YAAY;IACzC,IAAI,IAAI,CAACmR,QAAQ,CAACnR,OAAO,EAAE;MACvB,IAAIyW,WAAW,GAAG,8FAA8F,GAAG,IAAI,CAAC/D,SAAS,CAAC,oBAAoB,CAAC,GAAG,kCAAkC,IAAI,IAAI,CAAChT,KAAK,GAAG,CAAC,CAAC,GAAG,yCAAyC,GAAG,IAAI,CAACgT,SAAS,CAAC,gBAAgB,CAAC,GAAG,8BAA8B,GAAG,IAAI,CAACtB,YAAY,CAAClX,MAAM,GAAG,gBAAgB;MAC7W,IAAI,CAAC4Y,KAAK,CAAChO,IAAI,CAAC,IAAI,CAACqM,QAAQ,CAAClR,eAAe,CAAC,CAACkI,MAAM,CAACsO,WAAW,CAAC;IACtE;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACInG,YAAY,CAAClW,SAAS,CAACwb,OAAO,GAAG,UAAUlW,KAAK,EAAE;IAC9C,IAAIgQ,OAAO;IACX,IAAIgH,UAAU;IACd,IAAI,IAAI,CAACtF,YAAY,CAAC1R,KAAK,CAAC,CAACgX,UAAU,EAAE;MACrCA,UAAU,GAAG,IAAI,CAACtF,YAAY,CAAC1R,KAAK,CAAC,CAACgX,UAAU;IACpD,CAAC,MACI;MACDhH,OAAO,GAAG,IAAI,CAAC0B,YAAY,CAAC1R,KAAK,CAAC,CAACgQ,OAAO;IAC9C;IACA,IAAI,CAACgH,UAAU,EAAE;MACb,IAAIhH,OAAO,EAAE;QACT;QACA;QACA,IAAIiH,EAAE,GAAGjH,OAAO,CAACxM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAChC,IAAIyT,EAAE,KAAK,GAAG,IAAIA,EAAE,KAAK,GAAG,EAAE;UAC1B,IAAI,IAAI,CAACxF,QAAQ,CAAChS,uBAAuB,IACrC,CAAC,IAAI,CAACgS,QAAQ,CAAC9Q,OAAO,EAAE;YACxBqP,OAAO,GAAG3K,GAAG,CAAC,IAAI,CAACgK,KAAK,CAAC,CACpB9J,EAAE,CAACvF,KAAK,CAAC,CACToF,IAAI,CAAC4K,OAAO,CAAC,CACb1K,KAAK,CAAC,CAAC,CACPiD,IAAI,CAAC,CAAC;UACf,CAAC,MACI;YACDyH,OAAO,GAAG3K,GAAG,CAAC2K,OAAO,CAAC,CAAC1K,KAAK,CAAC,CAAC,CAACiD,IAAI,CAAC,CAAC;UACzC;QACJ;MACJ,CAAC,MACI;QACDyH,OAAO,GAAG,EAAE;MAChB;IACJ;IACA,IAAI,IAAI,CAACyB,QAAQ,CAACjS,eAAe,KAAK,UAAU,EAAE;MAC9C,IAAIwX,UAAU,EAAE;QACZ,IAAI,CAAC5D,KAAK,CAAChO,IAAI,CAAC,cAAc,CAAC,CAAC6C,IAAI,CAAC+O,UAAU,CAAC;MACpD,CAAC,MACI;QACD,IAAI,CAAC5D,KAAK,CAAChO,IAAI,CAAC,cAAc,CAAC,CAACmD,IAAI,CAACyH,OAAO,CAAC;MACjD;IACJ,CAAC,MACI;MACD,IAAIiF,YAAY,GAAG5P,GAAG,CAAC,IAAI,CAAC0N,cAAc,CAAC/S,KAAK,CAAC,CAAC;MAClD,IAAIgX,UAAU,EAAE;QACZ/B,YAAY,CAAChN,IAAI,CAAC+O,UAAU,CAAC;MACjC,CAAC,MACI;QACD/B,YAAY,CAACxM,MAAM,CAAC,6BAA6B,GAAGuH,OAAO,GAAG,QAAQ,CAAC;MAC3E;IACJ;IACA;IACA,IAAI,OAAOA,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpD,IAAIA,OAAO,KAAK,EAAE,EAAE;QAChB,IAAI,CAACoD,KAAK,CACLhO,IAAI,CAAC,IAAI,CAACqM,QAAQ,CAACjS,eAAe,CAAC,CACnChC,QAAQ,CAAC,eAAe,CAAC;MAClC,CAAC,MACI;QACD,IAAI,CAAC4V,KAAK,CACLhO,IAAI,CAAC,IAAI,CAACqM,QAAQ,CAACjS,eAAe,CAAC,CACnCkH,WAAW,CAAC,eAAe,CAAC;MACrC;IACJ;IACA,IAAI,CAACgE,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACM,kBAAkB,EAAE;MAC3CoE,KAAK,EAAEA;IACX,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI4Q,YAAY,CAAClW,SAAS,CAACgF,OAAO,GAAG,UAAUM,KAAK,EAAE;IAC9C,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACoX,QAAQ,CAAC/R,OAAO,EAAErF,CAAC,EAAE,EAAE;MAC7C,IAAIA,CAAC,IAAI,IAAI,CAACqX,YAAY,CAAClX,MAAM,GAAGwF,KAAK,EAAE;QACvC;MACJ;MACA,IAAI,CAACyV,WAAW,CAACzV,KAAK,GAAG3F,CAAC,EAAE,KAAK,CAAC;IACtC;IACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACqW,QAAQ,CAAC/R,OAAO,EAAEtE,CAAC,EAAE,EAAE;MAC7C,IAAI4E,KAAK,GAAG5E,CAAC,GAAG,CAAC,EAAE;QACf;MACJ;MACA,IAAI,CAACqa,WAAW,CAACzV,KAAK,GAAG5E,CAAC,EAAE,KAAK,CAAC;IACtC;EACJ,CAAC;EACDwV,YAAY,CAAClW,SAAS,CAACoa,iBAAiB,GAAG,UAAUnJ,SAAS,EAAE;IAC5D,IAAI,CAACA,SAAS,EACV,OAAO,EAAE;IACb,OAAO,QAAQ,GAAGA,SAAS,CAACpO,KAAK,GAAG,qCAAqC,GAAGoO,SAAS,CAACpO,KAAK,GAAG,CAAC,GAAG,oCAAoC,GAAGoO,SAAS,CAACrO,MAAM,GAAG,CAAC,GAAG,8BAA8B,GAAGqO,SAAS,CAACrO,MAAM,GAAG,IAAI;EAC5N,CAAC;EACDsT,YAAY,CAAClW,SAAS,CAACsa,iBAAiB,GAAG,UAAUrJ,SAAS,EAAE;IAC5D,IAAI,CAACA,SAAS,EACV,OAAO,EAAE;IACb,OAAO,QAAQ,GAAGA,SAAS,CAACpO,KAAK,GAAG,8BAA8B,GAAGoO,SAAS,CAACrO,MAAM,GAAG,IAAI;EAChG,CAAC;EACDsT,YAAY,CAAClW,SAAS,CAACwc,oBAAoB,GAAG,UAAUC,aAAa,EAAEnX,KAAK,EAAE8P,GAAG,EAAE;IAC/E,IAAIsH,YAAY;IAChB,IAAI,CAAC,IAAI,CAAC3F,QAAQ,CAAC9Q,OAAO,EAAE;MACxByW,YAAY,GAAG/R,GAAG,CAAC,IAAI,CAACgK,KAAK,CAAC,CAAC9J,EAAE,CAACvF,KAAK,CAAC;IAC5C;IACA,IAAIoX,YAAY,EAAE;MACd,IAAIC,YAAY,GAAG,KAAK,CAAC;MACzB,IAAI,CAAC,IAAI,CAAC5F,QAAQ,CAAC3Q,YAAY,EAAE;QAC7BuW,YAAY,GAAGD,YAAY,CAAChS,IAAI,CAAC,KAAK,CAAC,CAACE,KAAK,CAAC,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;MAC/D,CAAC,MACI;QACDoS,YAAY,GAAGD,YAAY,CAACnS,IAAI,CAAC,IAAI,CAACwM,QAAQ,CAAC3Q,YAAY,CAAC;MAChE;MACA,IAAI,CAACuW,YAAY,EACb,OAAO,EAAE;MACb,IAAIxC,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACH,gBAAgB,CAAC;MAC5D,IAAI2C,eAAe,GAAG,OAAO,GAAGxH,GAAG,GAAG,WAAW,GAAG+E,QAAQ,GAAG,kCAAkC,GAAGwC,YAAY,GAAG,OAAO;MAC1HF,aAAa,CAAC3Z,QAAQ,CAAC,gBAAgB,CAAC;MACxC,IAAI,CAAC4V,KAAK,CAAC5V,QAAQ,CAAC,wBAAwB,CAAC;MAC7C,OAAO8Z,eAAe;IAC1B;IACA,OAAO,EAAE;EACb,CAAC;EACD1G,YAAY,CAAClW,SAAS,CAAC6c,YAAY,GAAG,UAAU9K,GAAG,EAAE0K,aAAa,EAAEnX,KAAK,EAAE;IACvE,IAAIsU,kBAAkB,GAAG,IAAI,CAAC5C,YAAY,CAAC1R,KAAK,CAAC;IACjD,IAAI8P,GAAG,GAAGwE,kBAAkB,CAACxE,GAAG;MAAEhD,MAAM,GAAGwH,kBAAkB,CAACxH,MAAM;MAAEC,KAAK,GAAGuH,kBAAkB,CAACvH,KAAK;MAAEC,OAAO,GAAGsH,kBAAkB,CAACtH,OAAO;IAC5I;IACA;IACA,IAAIwK,UAAU,GAAG,EAAE;IACnB,IAAI3K,OAAO,GAAGiD,GAAG,GAAG,OAAO,GAAGA,GAAG,GAAG,GAAG,GAAG,EAAE;IAC5C,IAAI,IAAI,CAAC2H,6BAA6B,CAAC,CAAC,EAAE;MACtCD,UAAU,GAAG,IAAI,CAACN,oBAAoB,CAACC,aAAa,EAAEnX,KAAK,EAAE6M,OAAO,CAAC;IACzE,CAAC,MACI;MACD2K,UAAU,GAAGlN,KAAK,CAACsC,YAAY,CAAC5M,KAAK,EAAEyM,GAAG,EAAEI,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,CAAC;IAChF;IACA,IAAIG,SAAS,GAAG,kCAAkC,GAAGqK,UAAU,GAAG,YAAY;IAC9EL,aAAa,CAACxO,OAAO,CAACwE,SAAS,CAAC;EACpC,CAAC;EACDyD,YAAY,CAAClW,SAAS,CAACgd,iBAAiB,GAAG,UAAUC,MAAM,EAAEC,yBAAyB,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACrG,IAAIC,WAAW,GAAGJ,MAAM,CAACvS,IAAI,CAAC,YAAY,CAAC,CAACE,KAAK,CAAC,CAAC;IACnD,IAAIgF,KAAK,CAAC2D,aAAa,CAAC8J,WAAW,CAACrS,GAAG,CAAC,CAAC,CAAC,IACtCkS,yBAAyB,EAAE;MAC3BC,MAAM,CAAC,CAAC;IACZ,CAAC,MACI;MACDE,WAAW,CAAC5Q,EAAE,CAAC,kBAAkB,EAAE,YAAY;QAC3C0Q,MAAM,IAAIA,MAAM,CAAC,CAAC;MACtB,CAAC,CAAC;MACFE,WAAW,CAAC5Q,EAAE,CAAC,UAAU,EAAE,YAAY;QACnC2Q,OAAO,IAAIA,OAAO,CAAC,CAAC;MACxB,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIlH,YAAY,CAAClW,SAAS,CAACsd,cAAc,GAAG,UAAU/C,YAAY,EAAEjV,KAAK,EAAEiY,KAAK,EAAE7a,KAAK,EAAE8a,YAAY,EAAEN,yBAAyB,EAAE;IAC1H,IAAI1Q,KAAK,GAAG,IAAI;IAChB,IAAI,CAACwQ,iBAAiB,CAACzC,YAAY,EAAE2C,yBAAyB,EAAE,YAAY;MACxE1Q,KAAK,CAACiR,oBAAoB,CAAClD,YAAY,EAAEjV,KAAK,EAAEiY,KAAK,EAAE7a,KAAK,EAAE8a,YAAY,CAAC;IAC/E,CAAC,EAAE,YAAY;MACXjD,YAAY,CAACzX,QAAQ,CAAC,0BAA0B,CAAC;MACjDyX,YAAY,CAAC1M,IAAI,CAAC,6BAA6B,GAC3CrB,KAAK,CAACuK,QAAQ,CAACtQ,OAAO,CAAC,oBAAoB,CAAC,GAC5C,SAAS,CAAC;IAClB,CAAC,CAAC;EACN,CAAC;EACDyP,YAAY,CAAClW,SAAS,CAACyd,oBAAoB,GAAG,UAAUhB,aAAa,EAAEnX,KAAK,EAAEiY,KAAK,EAAE7a,KAAK,EAAE8a,YAAY,EAAE;IACtG,IAAIhR,KAAK,GAAG,IAAI;IAChB,IAAIoN,kBAAkB,GAAG,IAAI,CAAC5C,YAAY,CAAC1R,KAAK,CAAC;IACjD;IACA;IACA,IAAIoY,MAAM,GAAGF,YAAY,IACrB,IAAI,CAACG,YAAY,CAAC/D,kBAAkB,CAAC,KAAK,OAAO,IACjD,CAACA,kBAAkB,CAACgE,MAAM,GACxBlb,KAAK,GACL,CAAC;IACP6U,UAAU,CAAC,YAAY;MACnBkF,aAAa,CAAC3Z,QAAQ,CAAC,0BAA0B,CAAC;MAClD0J,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACS,aAAa,EAAE;QACvCiE,KAAK,EAAEA,KAAK;QACZiY,KAAK,EAAEA,KAAK,IAAI,CAAC;QACjBC,YAAY,EAAEA;MAClB,CAAC,CAAC;IACN,CAAC,EAAEE,MAAM,CAAC;EACd,CAAC;EACDxH,YAAY,CAAClW,SAAS,CAAC+c,6BAA6B,GAAG,YAAY;IAC/D,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC1G,UAAU,IACtB,IAAI,CAAClT,cAAc,IACnB,IAAI,CAAC8W,gBAAgB,CAAC;EAC9B,CAAC;EACD;EACA/D,YAAY,CAAClW,SAAS,CAACoX,iBAAiB,GAAG,UAAUzC,KAAK,EAAE;IACxD,IAAInI,KAAK,GAAG,IAAI;IAChBmI,KAAK,CAACxL,OAAO,CAAC,UAAUoL,OAAO,EAAEjP,KAAK,EAAE;MACpCiP,OAAO,CAACsF,gBAAgB,GAAGjK,KAAK,CAAC8F,OAAO,CAACnB,OAAO,CAACxC,GAAG,EAAE,CAAC,CAACwC,OAAO,CAACsJ,KAAK,EAAEvY,KAAK,CAAC;MAC7E,IAAIiP,OAAO,CAACsF,gBAAgB,IACxBrN,KAAK,CAACuK,QAAQ,CAACrT,iBAAiB,IAChC,CAAC6Q,OAAO,CAACqJ,MAAM,IACfrJ,OAAO,CAACsF,gBAAgB,CAAC3F,OAAO,EAAE;QAClCK,OAAO,CAACqJ,MAAM,GAAG,uBAAuB,GAAGrJ,OAAO,CAACsF,gBAAgB,CAAC3F,OAAO,CAAC,CAAC,CAAC,GAAG,oBAAoB;MACzG;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIgC,YAAY,CAAClW,SAAS,CAAC+a,WAAW,GAAG,UAAUzV,KAAK,EAAEwY,GAAG,EAAE;IACvD,IAAItR,KAAK,GAAG,IAAI;IAChB,IAAIoN,kBAAkB,GAAG,IAAI,CAAC5C,YAAY,CAAC1R,KAAK,CAAC;IACjD,IAAImX,aAAa,GAAG9R,GAAG,CAAC,IAAI,CAAC0N,cAAc,CAAC/S,KAAK,CAAC,CAAC;IACnD,IAAIsY,MAAM,GAAGhE,kBAAkB,CAACgE,MAAM;MAAExL,MAAM,GAAGwH,kBAAkB,CAACxH,MAAM;MAAEC,KAAK,GAAGuH,kBAAkB,CAACvH,KAAK;MAAEC,OAAO,GAAGsH,kBAAkB,CAACtH,OAAO;IAClJ,IAAIP,GAAG,GAAG6H,kBAAkB,CAAC7H,GAAG;IAChC,IAAI8L,KAAK,GAAGjE,kBAAkB,CAACiE,KAAK;IACpC,IAAIE,WAAW,GAAGF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGjL,IAAI,CAACC,KAAK,CAACgL,KAAK,CAAC,GAAGA,KAAK;IAChF,IAAIjE,kBAAkB,CAACoE,UAAU,EAAE;MAC/B,IAAIC,SAAS,GAAGrE,kBAAkB,CAACoE,UAAU,CAAC7T,KAAK,CAAC,GAAG,CAAC;MACxD4H,GAAG,GAAGnC,KAAK,CAACqD,gBAAgB,CAACgL,SAAS,CAAC,IAAIlM,GAAG;IAClD;IACA,IAAImM,SAAS,GAAGtE,kBAAkB,CAACC,gBAAgB;IACnD,IAAIQ,YAAY,GAAG,EAAE;IACrB,IAAI8D,MAAM,GAAG,CAAC,CAACvE,kBAAkB,CAACuE,MAAM;IACxC,IAAIX,YAAY,GAAG,CAAC,IAAI,CAACnH,UAAU;IACnC;IACA,IAAIkH,KAAK,GAAG,CAAC;IACb,IAAIC,YAAY,EAAE;MACd,IAAI,IAAI,CAACra,cAAc,IAAI,IAAI,CAAC8W,gBAAgB,EAAE;QAC9CsD,KAAK,GAAG,IAAI,CAACxG,QAAQ,CAAC7T,sBAAsB,GAAG,EAAE;MACrD,CAAC,MACI;QACDqa,KAAK,GAAG,IAAI,CAACxG,QAAQ,CAAC/T,gBAAgB,GAAG,EAAE;MAC/C;IACJ;IACA,IAAI,CAACyZ,aAAa,CAACvQ,QAAQ,CAAC,WAAW,CAAC,EAAE;MACtC,IAAIgS,SAAS,EAAE;QACX,IAAInE,EAAE,GAAG,IAAI,CAACnD,sBAAsB;UAAEwH,KAAK,GAAGrE,EAAE,CAACpL,GAAG;UAAEqC,MAAM,GAAG+I,EAAE,CAAC/I,MAAM;QACxE,IAAIqN,SAAS,GAAGzO,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC8E,KAAK,CAACrP,KAAK,CAAC,EAAE,IAAI,CAACoT,KAAK,EAAE0F,KAAK,GAAGpN,MAAM,EAAEkN,SAAS,IAAI,IAAI,CAACnH,QAAQ,CAACtT,YAAY,CAAC;QACrH4W,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAAC+D,SAAS,CAAC;MACpD;MACA,IAAIF,MAAM,EAAE;QACR,IAAIG,MAAM,GAAG1O,KAAK,CAACkC,eAAe,CAAC,IAAI,CAACiF,QAAQ,CAACxR,WAAW,EAAE,IAAI,CAACwR,QAAQ,CAACvR,YAAY,EAAE,IAAI,CAACuR,QAAQ,CAACtR,cAAc,EAAE,IAAI,CAACsR,QAAQ,CAACrR,eAAe,EAAEqM,GAAG,EAAE6H,kBAAkB,CAAC5H,WAAW,CAAC;QAC3LyK,aAAa,CAACxO,OAAO,CAACqQ,MAAM,CAAC;MACjC,CAAC,MACI,IAAIV,MAAM,EAAE;QACb,IAAI/J,QAAQ,GAAG,EAAE;QACjB,IAAI0K,iBAAiB,GAAGf,YAAY,IAChC,IAAI,CAACra,cAAc,IACnB,IAAI,CAAC8W,gBAAgB;QACzB,IAAIsE,iBAAiB,EAAE;UACnB1K,QAAQ,GAAG,IAAI,CAAC2I,oBAAoB,CAACC,aAAa,EAAEnX,KAAK,EAAE,EAAE,CAAC;QAClE;QACA,IAAIgZ,MAAM,GAAG1O,KAAK,CAAC+D,oBAAoB,CAACiK,MAAM,EAAE/J,QAAQ,IAAI,EAAE,EAAEwG,YAAY,EAAE,IAAI,CAACtD,QAAQ,CAACtQ,OAAO,CAAC,WAAW,CAAC,EAAEyX,SAAS,CAAC;QAC5HzB,aAAa,CAACxO,OAAO,CAACqQ,MAAM,CAAC;MACjC,CAAC,MACI,IAAIJ,SAAS,EAAE;QAChB,IAAII,MAAM,GAAG,wCAAwC,GAAGjE,YAAY,GAAG,WAAW;QAClFoC,aAAa,CAACxO,OAAO,CAACqQ,MAAM,CAAC;MACjC,CAAC,MACI;QACD,IAAI,CAACzB,YAAY,CAAC9K,GAAG,EAAE0K,aAAa,EAAEnX,KAAK,CAAC;QAC5C,IAAI8M,MAAM,IAAIE,OAAO,EAAE;UACnB,IAAI6J,IAAI,GAAGM,aAAa,CAAC/R,IAAI,CAAC,YAAY,CAAC;UAC3C,IAAI,CAACwR,eAAe,CAACC,IAAI,CAAC;QAC9B;MACJ;MACA,IAAIyB,MAAM,IAAIM,SAAS,EAAE;QACrB,IAAI,CAAClO,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACG,QAAQ,EAAE;UACjCuE,KAAK,EAAEA,KAAK;UACZyM,GAAG,EAAEA,GAAG;UACRyM,UAAU,EAAET,WAAW;UACvBU,SAAS,EAAE,CAAC,CAACb;QACjB,CAAC,CAAC;MACN;MACA,IAAI,CAAC5N,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACC,gBAAgB,EAAE;QAAEyE,KAAK,EAAEA;MAAM,CAAC,CAAC;MAC9D,IAAI,IAAI,CAAC+Q,UAAU,IACf,IAAI,CAACU,QAAQ,CAACjS,eAAe,KAAK,UAAU,EAAE;QAC9C,IAAI,CAAC0W,OAAO,CAAClW,KAAK,CAAC;MACvB;IACJ;IACA;IACA,IAAIoY,MAAM,GAAG,CAAC;IACd;IACA;IACA,IAAIH,KAAK,IAAI,CAAC5S,GAAG,CAAClD,QAAQ,CAAC2G,IAAI,CAAC,CAAClC,QAAQ,CAAC,cAAc,CAAC,EAAE;MACvDwR,MAAM,GAAGH,KAAK;IAClB;IACA;IACA,IAAI,IAAI,CAACR,6BAA6B,CAAC,CAAC,EAAE;MACtCxF,UAAU,CAAC,YAAY;QACnBkF,aAAa,CACRzQ,WAAW,CAAC,yCAAyC,CAAC,CACtDf,UAAU,CAAC,OAAO,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC8L,QAAQ,CAAC7T,sBAAsB,GAAG,GAAG,CAAC;MAC9C,IAAI,CAACuZ,aAAa,CAACvQ,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtCqL,UAAU,CAAC,YAAY;UACnB,IAAI/K,KAAK,CAACmR,YAAY,CAAC/D,kBAAkB,CAAC,KAAK,OAAO,EAAE;YACpD,IAAIxE,GAAG,GAAGwE,kBAAkB,CAACxE,GAAG;YAChC,IAAIjD,OAAO,GAAGiD,GAAG,GAAG,OAAO,GAAGA,GAAG,GAAG,GAAG,GAAG,EAAE;YAC5CqH,aAAa,CACR/R,IAAI,CAAC,cAAc,CAAC,CACpBqD,MAAM,CAAC6B,KAAK,CAACsC,YAAY,CAAC5M,KAAK,EAAEyM,GAAG,EAAEI,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEuH,kBAAkB,CAACtH,OAAO,CAAC,CAAC;YAC/F,IAAIF,MAAM,IAAIE,OAAO,EAAE;cACnB,IAAI6J,IAAI,GAAGM,aAAa,CAAC/R,IAAI,CAAC,YAAY,CAAC;cAC3C8B,KAAK,CAAC0P,eAAe,CAACC,IAAI,CAAC;YAC/B;UACJ;UACA,IAAI3P,KAAK,CAACmR,YAAY,CAAC/D,kBAAkB,CAAC,KAAK,OAAO,IACjDpN,KAAK,CAACmR,YAAY,CAAC/D,kBAAkB,CAAC,KAAK,OAAO,IAC/CgE,MAAO,EAAE;YACbpR,KAAK,CAAC8Q,cAAc,CAACb,aAAa,EAAEnX,KAAK,EAAEiY,KAAK,EAAEG,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;YACtE;YACAlR,KAAK,CAACwQ,iBAAiB,CAACP,aAAa,EAAE,CAAC,EAAEyB,SAAS,IAAIA,SAAS,CAACtI,KAAK,IAAI,CAACgI,MAAM,CAAC,EAAE,YAAY;cAC5FpR,KAAK,CAACkS,2BAA2B,CAACpZ,KAAK,EAAEmX,aAAa,EAAEiB,MAAM,CAAC;YACnE,CAAC,EAAE,YAAY;cACXlR,KAAK,CAACkS,2BAA2B,CAACpZ,KAAK,EAAEmX,aAAa,EAAEiB,MAAM,CAAC;YACnE,CAAC,CAAC;UACN;QACJ,CAAC,EAAE,IAAI,CAAC3G,QAAQ,CAAC7T,sBAAsB,GAAG,GAAG,CAAC;MAClD;IACJ;IACA;IACAuZ,aAAa,CAAC3Z,QAAQ,CAAC,WAAW,CAAC;IACnC,IAAI,CAAC,IAAI,CAACia,6BAA6B,CAAC,CAAC,IACpC,IAAI,CAACY,YAAY,CAAC/D,kBAAkB,CAAC,KAAK,OAAO,IAAI,CAACgE,MAAO,EAAE;MAChE,IAAI,CAACN,cAAc,CAACb,aAAa,EAAEnX,KAAK,EAAEiY,KAAK,EAAEG,MAAM,EAAEF,YAAY,EAAE,CAAC,EAAEU,SAAS,IAAIA,SAAS,CAACtI,KAAK,IAAI,CAACgI,MAAM,CAAC,CAAC;IACvH;IACA;IACA,IAAI,CAAC,CAAC,IAAI,CAACza,cAAc,IAAI,CAAC,IAAI,CAAC8W,gBAAgB,KAC/CwC,aAAa,CAACvQ,QAAQ,CAAC,cAAc,CAAC,IACtC,CAAC,IAAI,CAACmK,UAAU,EAAE;MAClBkB,UAAU,CAAC,YAAY;QACnBkF,aAAa,CAAC3Z,QAAQ,CAAC,aAAa,CAAC;MACzC,CAAC,EAAE,IAAI,CAACiU,QAAQ,CAAC/T,gBAAgB,CAAC;IACtC;IACA;IACA;IACA,IAAI,CAACqT,UAAU,GAAG,IAAI;IACtB,IAAIyH,GAAG,KAAK,IAAI,EAAE;MACd,IAAI,CAACrB,aAAa,CAACvQ,QAAQ,CAAC,cAAc,CAAC,EAAE;QACzCuQ,aAAa,CACR/R,IAAI,CAAC,YAAY,CAAC,CAClBE,KAAK,CAAC,CAAC,CACP6B,EAAE,CAAC,kBAAkB,EAAE,YAAY;UACpCD,KAAK,CAACxH,OAAO,CAACM,KAAK,CAAC;QACxB,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACN,OAAO,CAACM,KAAK,CAAC;MACvB;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI4Q,YAAY,CAAClW,SAAS,CAAC0e,2BAA2B,GAAG,UAAUpZ,KAAK,EAAEmX,aAAa,EAAE/Z,KAAK,EAAE;IACxF,IAAI8J,KAAK,GAAG,IAAI;IAChB+K,UAAU,CAAC,YAAY;MACnBkF,aAAa,CAAC/R,IAAI,CAAC,eAAe,CAAC,CAACuB,MAAM,CAAC,CAAC;MAC5CwQ,aAAa,CAACzQ,WAAW,CAAC,gBAAgB,CAAC;MAC3CQ,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,wBAAwB,CAAC;MACjDQ,KAAK,CAACkK,mBAAmB,GAAG,IAAI;MAChClK,KAAK,CAACxH,OAAO,CAACM,KAAK,CAAC;IACxB,CAAC,EAAE5C,KAAK,GAAG,GAAG,CAAC;EACnB,CAAC;EACDwT,YAAY,CAAClW,SAAS,CAACub,yBAAyB,GAAG,UAAUjW,KAAK,EAAEqZ,SAAS,EAAEC,aAAa,EAAE;IAC1F,IAAIpS,KAAK,GAAG,IAAI;IAChB,IAAIoS,aAAa,KAAK,KAAK,CAAC,EAAE;MAAEA,aAAa,GAAG,CAAC;IAAE;IACnD,IAAItD,sBAAsB,GAAG,EAAE;IAC/B;IACA,IAAIuD,qBAAqB,GAAGrW,IAAI,CAACsW,GAAG,CAACF,aAAa,EAAE,CAAC,CAAC;IACtDC,qBAAqB,GAAGrW,IAAI,CAACoI,GAAG,CAACiO,qBAAqB,EAAE,IAAI,CAAC7H,YAAY,CAAClX,MAAM,CAAC;IACjF,IAAIif,aAAa,GAAG,UAAU,GAAG,IAAI,CAAC9I,IAAI,GAAG,GAAG,GAAG0I,SAAS;IAC5D,IAAI,IAAI,CAAC3H,YAAY,CAAClX,MAAM,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACkX,YAAY,CAAC7N,OAAO,CAAC,UAAU6V,QAAQ,EAAE1Z,KAAK,EAAE;QACjDgW,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAGN,KAAK,CAACyJ,IAAI,GAAG,GAAG,GAAG3Q,KAAK,CAAC;MACtE,CAAC,CAAC;MACF,OAAOgW,sBAAsB;IACjC;IACA,IAAIhW,KAAK,GAAG,CAAC,IAAI,CAAC0R,YAAY,CAAClX,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;MAC5C,KAAK,IAAImf,GAAG,GAAG3Z,KAAK,EAAE2Z,GAAG,GAAG3Z,KAAK,GAAGuZ,qBAAqB,GAAG,CAAC,IAAII,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;QAC9E3D,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,GAAGgJ,GAAG,CAAC;MACnE;MACA,IAAIC,qBAAqB,GAAG5D,sBAAsB,CAACxb,MAAM;MACzD,KAAK,IAAImf,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,qBAAqB,GAAGK,qBAAqB,EAAED,GAAG,EAAE,EAAE;QAC1E3D,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,IAAI3Q,KAAK,GAAG2Z,GAAG,GAAG,CAAC,CAAC,CAAC;MACjF;IACJ,CAAC,MACI;MACD,KAAK,IAAIA,GAAG,GAAG3Z,KAAK,EAAE2Z,GAAG,IAAI,IAAI,CAACjI,YAAY,CAAClX,MAAM,GAAG,CAAC,IACrDmf,GAAG,GAAG3Z,KAAK,GAAGuZ,qBAAqB,GAAG,CAAC,EAAEI,GAAG,EAAE,EAAE;QAChD3D,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,GAAGgJ,GAAG,CAAC;MACnE;MACA,IAAIC,qBAAqB,GAAG5D,sBAAsB,CAACxb,MAAM;MACzD,KAAK,IAAImf,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,qBAAqB,GAAGK,qBAAqB,EAAED,GAAG,EAAE,EAAE;QAC1E3D,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,IAAI3Q,KAAK,GAAG2Z,GAAG,GAAG,CAAC,CAAC,CAAC;MACjF;IACJ;IACA,IAAI,IAAI,CAAClI,QAAQ,CAAC1S,IAAI,EAAE;MACpB,IAAIiB,KAAK,KAAK,IAAI,CAAC0R,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;QACxCwb,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;MACjE,CAAC,MACI,IAAI3Q,KAAK,KAAK,CAAC,EAAE;QAClBgW,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,IAAI,IAAI,CAACe,YAAY,CAAClX,MAAM,GAAG,CAAC,CAAC,CAAC;MAC9F;IACJ;IACA,IAAIwb,sBAAsB,CAAC3R,OAAO,CAACoV,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MACtDzD,sBAAsB,CAACxO,IAAI,CAAC,UAAU,GAAG,IAAI,CAACmJ,IAAI,GAAG,GAAG,GAAG0I,SAAS,CAAC;IACzE;IACA,OAAOrD,sBAAsB;EACjC,CAAC;EACDpF,YAAY,CAAClW,SAAS,CAAC8a,kBAAkB,GAAG,UAAUxV,KAAK,EAAEqZ,SAAS,EAAE;IACpE,IAAInS,KAAK,GAAG,IAAI;IAChB,IAAI8O,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,CAACjW,KAAK,EAAEqZ,SAAS,EAAE,IAAI,CAAC5H,QAAQ,CAAC9R,uBAAuB,CAAC;IACpHqW,sBAAsB,CAACnS,OAAO,CAAC,UAAU2L,IAAI,EAAE;MAC3C,IAAItI,KAAK,CAAC+J,iBAAiB,CAAC5M,OAAO,CAACmL,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9CtI,KAAK,CAAC4M,MAAM,CAACrL,MAAM,CAAC,YAAY,GAAG+G,IAAI,GAAG,6BAA6B,CAAC;MAC5E;IACJ,CAAC,CAAC;IACF,IAAI,CAACyB,iBAAiB,CAACpN,OAAO,CAAC,UAAU2L,IAAI,EAAE;MAC3C,IAAIwG,sBAAsB,CAAC3R,OAAO,CAACmL,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7CnK,GAAG,CAAC,GAAG,GAAGmK,IAAI,CAAC,CAAC7I,MAAM,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,OAAOqP,sBAAsB;EACjC,CAAC;EACD;AACJ;AACA;EACIpF,YAAY,CAAClW,SAAS,CAACmf,qBAAqB,GAAG,YAAY;IACvD,IAAIR,SAAS,GAAG,CAAC;IACjB,IAAI;MACA,IAAIS,aAAa,GAAG,IAAI,CAAC1G,KAAK,CACzBhO,IAAI,CAAC,aAAa,CAAC,CACnBE,KAAK,CAAC,CAAC,CACPL,IAAI,CAAC,IAAI,CAAC;MACfoU,SAAS,GAAGpO,QAAQ,CAAC6O,aAAa,CAACjV,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1D,CAAC,CACD,OAAO2L,KAAK,EAAE;MACV6I,SAAS,GAAG,CAAC;IACjB;IACA,OAAOA,SAAS;EACpB,CAAC;EACDzI,YAAY,CAAClW,SAAS,CAACqf,gBAAgB,GAAG,UAAU/Z,KAAK,EAAE;IACvD,IAAI,IAAI,CAACyR,QAAQ,CAACpR,QAAQ,EAAE;MACxB,IAAIiU,kBAAkB,GAAG,IAAI,CAAC5C,YAAY,CAAC1R,KAAK,CAAC;MACjD,IAAIga,eAAe,GAAG1F,kBAAkB,CAAC2F,WAAW,KAAK,KAAK,IAC1D3F,kBAAkB,CAAC2F,WAAW,KAAK,OAAO;MAC9C,IAAID,eAAe,EAAE;QACjB,IAAI,CAAC5G,KAAK,CAAC5V,QAAQ,CAAC,kBAAkB,CAAC;MAC3C,CAAC,MACI;QACD,IAAI0c,SAAS,GAAG,IAAI,CAAChH,cAAc,CAAC,aAAa,CAAC;QAClD,IAAI,CAACE,KAAK,CAAC1M,WAAW,CAAC,kBAAkB,CAAC;QAC1CwT,SAAS,CAACjV,IAAI,CAAC,MAAM,EAAEqP,kBAAkB,CAAC2F,WAAW,IACjD3F,kBAAkB,CAAC7H,GAAG,CAAC;QAC3B,IAAI6H,kBAAkB,CAACjU,QAAQ,EAAE;UAC7B6Z,SAAS,CAACjV,IAAI,CAAC,UAAU,EAAEqP,kBAAkB,CAACjU,QAAQ,CAAC;QAC3D;MACJ;IACJ;EACJ,CAAC;EACDuQ,YAAY,CAAClW,SAAS,CAACyf,kBAAkB,GAAG,UAAUC,SAAS,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAE;IAClG,IAAIpT,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAAC6J,UAAU,EAAE;MACjBuJ,iBAAiB,CAAC9c,QAAQ,CAAC,mBAAmB,CAAC;IACnD;IACAyU,UAAU,CAAC,YAAY;MACnB;MACA/K,KAAK,CAACkM,KAAK,CAAC5V,QAAQ,CAAC,aAAa,CAAC;MACnC0J,KAAK,CAACkM,KAAK,CACNhO,IAAI,CAAC,UAAU,CAAC,CAChBsB,WAAW,CAAC,6BAA6B,CAAC;MAC/C,IAAI0T,SAAS,KAAK,MAAM,EAAE;QACtB;QACAC,gBAAgB,CAAC7c,QAAQ,CAAC,eAAe,CAAC;QAC1C8c,iBAAiB,CAAC9c,QAAQ,CAAC,eAAe,CAAC;MAC/C,CAAC,MACI;QACD;QACA6c,gBAAgB,CAAC7c,QAAQ,CAAC,eAAe,CAAC;QAC1C8c,iBAAiB,CAAC9c,QAAQ,CAAC,eAAe,CAAC;MAC/C;MACA;MACAyU,UAAU,CAAC,YAAY;QACnB/K,KAAK,CAACkM,KAAK,CAAChO,IAAI,CAAC,UAAU,CAAC,CAACsB,WAAW,CAAC,YAAY,CAAC;QACtD2T,gBAAgB,CAAC7c,QAAQ,CAAC,YAAY,CAAC;QACvC;QACA0J,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,aAAa,CAAC;MAC1C,CAAC,EAAE,EAAE,CAAC;IACV,CAAC,EAAE,IAAI,CAACqK,UAAU,GAAG,IAAI,CAACU,QAAQ,CAACzT,UAAU,GAAG,CAAC,CAAC;EACtD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4S,YAAY,CAAClW,SAAS,CAAC4b,KAAK,GAAG,UAAUtW,KAAK,EAAEua,SAAS,EAAEC,SAAS,EAAEJ,SAAS,EAAE;IAC7E,IAAIlT,KAAK,GAAG,IAAI;IAChB,IAAImS,SAAS,GAAG,IAAI,CAACQ,qBAAqB,CAAC,CAAC;IAC5C,IAAI,CAAC5I,iBAAiB,GAAG,IAAI,CAACuE,kBAAkB,CAACxV,KAAK,EAAEqZ,SAAS,CAAC;IAClE;IACA,IAAI,IAAI,CAACtI,UAAU,IAAIsI,SAAS,KAAKrZ,KAAK,EAAE;MACxC;IACJ;IACA,IAAIya,oBAAoB,GAAG,IAAI,CAAC/I,YAAY,CAAClX,MAAM;IACnD,IAAI,CAAC,IAAI,CAACwW,MAAM,EAAE;MACd,IAAI,IAAI,CAACS,QAAQ,CAACnR,OAAO,EAAE;QACvB,IAAI,CAACoV,oBAAoB,CAAC1V,KAAK,CAAC;MACpC;MACA,IAAIqa,gBAAgB,GAAG,IAAI,CAACvH,YAAY,CAAC9S,KAAK,CAAC;MAC/C,IAAI0a,mBAAmB,GAAG,IAAI,CAAC5H,YAAY,CAACuG,SAAS,CAAC;MACtD,IAAI/E,kBAAkB,GAAG,IAAI,CAAC5C,YAAY,CAAC1R,KAAK,CAAC;MACjD,IAAI4Y,SAAS,GAAGtE,kBAAkB,CAACC,gBAAgB;MACnD,IAAI,CAACnB,KAAK,CAACnO,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAACoT,YAAY,CAAC/D,kBAAkB,CAAC,CAAC;MAC5E,IAAI,CAACyF,gBAAgB,CAAC/Z,KAAK,CAAC;MAC5B,IAAI4Y,SAAS,EAAE;QACX,IAAInE,EAAE,GAAG,IAAI,CAACnD,sBAAsB;UAAEqJ,KAAK,GAAGlG,EAAE,CAACpL,GAAG;UAAEqC,MAAM,GAAG+I,EAAE,CAAC/I,MAAM;QACxE,IAAIqN,SAAS,GAAGzO,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC8E,KAAK,CAACrP,KAAK,CAAC,EAAE,IAAI,CAACoT,KAAK,EAAEuH,KAAK,GAAGjP,MAAM,EAAEkN,SAAS,IAAI,IAAI,CAACnH,QAAQ,CAACtT,YAAY,CAAC;QACrH,IAAI,CAACyW,gBAAgB,CAAC5U,KAAK,EAAE+Y,SAAS,CAAC;MAC3C;MACA,IAAI,CAACrO,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACU,WAAW,EAAE;QACpCqd,SAAS,EAAEA,SAAS;QACpBrZ,KAAK,EAAEA,KAAK;QACZua,SAAS,EAAE,CAAC,CAACA,SAAS;QACtBC,SAAS,EAAE,CAAC,CAACA;MACjB,CAAC,CAAC;MACF,IAAI,CAACxJ,MAAM,GAAG,IAAI;MAClB0F,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;MACjC,IAAI,CAACiE,YAAY,CAAC5a,KAAK,CAAC;MACxB,IAAI,CAACoa,SAAS,EAAE;QACZ,IAAIpa,KAAK,GAAGqZ,SAAS,EAAE;UACnBe,SAAS,GAAG,MAAM;QACtB,CAAC,MACI,IAAIpa,KAAK,GAAGqZ,SAAS,EAAE;UACxBe,SAAS,GAAG,MAAM;QACtB;MACJ;MACA,IAAI,CAACG,SAAS,EAAE;QACZ,IAAI,CAACJ,kBAAkB,CAACC,SAAS,EAAEC,gBAAgB,EAAEK,mBAAmB,CAAC;MAC7E,CAAC,MACI;QACD,IAAI,CAACtH,KAAK,CACLhO,IAAI,CAAC,UAAU,CAAC,CAChBsB,WAAW,CAAC,wCAAwC,CAAC;QAC1D,IAAImU,SAAS,GAAG,KAAK,CAAC;QACtB,IAAIC,SAAS,GAAG,KAAK,CAAC;QACtB,IAAIL,oBAAoB,GAAG,CAAC,EAAE;UAC1BI,SAAS,GAAG7a,KAAK,GAAG,CAAC;UACrB8a,SAAS,GAAG9a,KAAK,GAAG,CAAC;UACrB,IAAIA,KAAK,KAAK,CAAC,IAAIqZ,SAAS,KAAKoB,oBAAoB,GAAG,CAAC,EAAE;YACvD;YACAK,SAAS,GAAG,CAAC;YACbD,SAAS,GAAGJ,oBAAoB,GAAG,CAAC;UACxC,CAAC,MACI,IAAIza,KAAK,KAAKya,oBAAoB,GAAG,CAAC,IACvCpB,SAAS,KAAK,CAAC,EAAE;YACjB;YACAyB,SAAS,GAAG,CAAC;YACbD,SAAS,GAAGJ,oBAAoB,GAAG,CAAC;UACxC;QACJ,CAAC,MACI;UACDI,SAAS,GAAG,CAAC;UACbC,SAAS,GAAG,CAAC;QACjB;QACA,IAAIV,SAAS,KAAK,MAAM,EAAE;UACtB,IAAI,CAACtH,YAAY,CAACgI,SAAS,CAAC,CAACtd,QAAQ,CAAC,eAAe,CAAC;QAC1D,CAAC,MACI;UACD,IAAI,CAACsV,YAAY,CAAC+H,SAAS,CAAC,CAACrd,QAAQ,CAAC,eAAe,CAAC;QAC1D;QACA6c,gBAAgB,CAAC7c,QAAQ,CAAC,YAAY,CAAC;MAC3C;MACA;MACA,IAAI,CAAC,IAAI,CAACuT,UAAU,EAAE;QAClB,IAAI,CAAC0E,WAAW,CAACzV,KAAK,EAAE,IAAI,CAAC;MACjC,CAAC,MACI;QACDiS,UAAU,CAAC,YAAY;UACnB/K,KAAK,CAACuO,WAAW,CAACzV,KAAK,EAAE,IAAI,CAAC;UAC9B;UACA,IAAIkH,KAAK,CAACuK,QAAQ,CAACjS,eAAe,KAAK,UAAU,EAAE;YAC/C0H,KAAK,CAACgP,OAAO,CAAClW,KAAK,CAAC;UACxB;QACJ,CAAC,EAAE,IAAI,CAACyR,QAAQ,CAACrU,KAAK,GAAG,EAAE,IAAImd,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC9I,QAAQ,CAACzT,UAAU,CAAC,CAAC;MAC7E;MACAiU,UAAU,CAAC,YAAY;QACnB/K,KAAK,CAAC8J,MAAM,GAAG,KAAK;QACpB0J,mBAAmB,CAAChU,WAAW,CAAC,mBAAmB,CAAC;QACpDQ,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACW,UAAU,EAAE;UACpCod,SAAS,EAAEA,SAAS;UACpBrZ,KAAK,EAAEA,KAAK;UACZua,SAAS,EAAEA,SAAS;UACpBC,SAAS,EAAEA;QACf,CAAC,CAAC;MACN,CAAC,EAAE,CAAC,IAAI,CAACzJ,UAAU,GAAG,IAAI,CAACU,QAAQ,CAACrU,KAAK,GAAG,GAAG,GAAG,GAAG,KAAKmd,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC9I,QAAQ,CAACzT,UAAU,CAAC,CAAC;IACxG;IACA,IAAI,CAACgC,KAAK,GAAGA,KAAK;EACtB,CAAC;EACD4Q,YAAY,CAAClW,SAAS,CAACgb,oBAAoB,GAAG,UAAU1V,KAAK,EAAE;IAC3D,IAAI,CAACkT,cAAc,CAAC,oBAAoB,CAAC,CAAC3K,IAAI,CAACvI,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;EAClE,CAAC;EACD4Q,YAAY,CAAClW,SAAS,CAACqgB,kBAAkB,GAAG,YAAY;IACpD,IAAI,CAAC7H,cAAc,CAAC,gBAAgB,CAAC,CAAC3K,IAAI,CAAC,IAAI,CAACmJ,YAAY,CAAClX,MAAM,GAAG,EAAE,CAAC;EAC7E,CAAC;EACDoW,YAAY,CAAClW,SAAS,CAAC2d,YAAY,GAAG,UAAU7I,IAAI,EAAE;IAClD,IAAIA,IAAI,CAAC+E,gBAAgB,EAAE;MACvB,OAAO,OAAO;IAClB,CAAC,MACI,IAAI/E,IAAI,CAACqJ,MAAM,EAAE;MAClB,OAAO,QAAQ;IACnB,CAAC,MACI;MACD,OAAO,OAAO;IAClB;EACJ,CAAC;EACDjI,YAAY,CAAClW,SAAS,CAACsgB,SAAS,GAAG,UAAUC,WAAW,EAAEC,SAAS,EAAEnW,CAAC,EAAE;IACpE,IAAIoW,SAAS,GAAGD,SAAS,CAACE,KAAK,GAAGH,WAAW,CAACG,KAAK;IACnD,IAAIC,SAAS,GAAGH,SAAS,CAACI,KAAK,GAAGL,WAAW,CAACK,KAAK;IACnD,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAI,IAAI,CAACC,cAAc,EAAE;MACrBD,UAAU,GAAG,IAAI;IACrB,CAAC,MACI;MACD,IAAIrY,IAAI,CAACuY,GAAG,CAACN,SAAS,CAAC,GAAG,EAAE,EAAE;QAC1B,IAAI,CAACK,cAAc,GAAG,YAAY;QAClCD,UAAU,GAAG,IAAI;MACrB,CAAC,MACI,IAAIrY,IAAI,CAACuY,GAAG,CAACJ,SAAS,CAAC,GAAG,EAAE,EAAE;QAC/B,IAAI,CAACG,cAAc,GAAG,UAAU;QAChCD,UAAU,GAAG,IAAI;MACrB;IACJ;IACA,IAAI,CAACA,UAAU,EAAE;MACb;IACJ;IACA,IAAIpE,aAAa,GAAG,IAAI,CAACrE,YAAY,CAAC,IAAI,CAAC9S,KAAK,CAAC;IACjD,IAAI,IAAI,CAACwb,cAAc,KAAK,YAAY,EAAE;MACtCzW,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0N,cAAc,CAAC,CAAC;MACxD;MACA,IAAI,CAACW,KAAK,CAAC5V,QAAQ,CAAC,aAAa,CAAC;MAClC;MACA,IAAI,CAACke,YAAY,CAACvE,aAAa,EAAEgE,SAAS,EAAE,CAAC,CAAC;MAC9C;MACA,IAAI5d,KAAK,GAAG4Z,aAAa,CAACzR,GAAG,CAAC,CAAC,CAACiW,WAAW;MAC3C,IAAIC,gBAAgB,GAAIre,KAAK,GAAG,EAAE,GAAI,GAAG;MACzC,IAAIse,MAAM,GAAGD,gBAAgB,GAAG1Y,IAAI,CAACuY,GAAG,CAAEN,SAAS,GAAG,EAAE,GAAI,GAAG,CAAC;MAChE,IAAI,CAACO,YAAY,CAAC,IAAI,CAACtI,KAAK,CAAChO,IAAI,CAAC,gBAAgB,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC/H,KAAK,GAAG4d,SAAS,GAAGU,MAAM,EAAE,CAAC,CAAC;MAC5F,IAAI,CAACH,YAAY,CAAC,IAAI,CAACtI,KAAK,CAAChO,IAAI,CAAC,gBAAgB,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE/H,KAAK,GAAG4d,SAAS,GAAGU,MAAM,EAAE,CAAC,CAAC;IAC/F,CAAC,MACI,IAAI,IAAI,CAACL,cAAc,KAAK,UAAU,EAAE;MACzC,IAAI,IAAI,CAAC/J,QAAQ,CAAC9S,YAAY,EAAE;QAC5BoG,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0N,cAAc,CAAC,CAAC;QACxD,IAAI,CAACY,UAAU,CAAC7V,QAAQ,CAAC,sBAAsB,CAAC;QAChD,IAAIse,OAAO,GAAG,CAAC,GAAG5Y,IAAI,CAACuY,GAAG,CAACJ,SAAS,CAAC,GAAG1Z,MAAM,CAACoa,WAAW;QAC1D,IAAI,CAAClI,SAAS,CAAC5M,GAAG,CAAC,SAAS,EAAE6U,OAAO,CAAC;QACtC,IAAIE,KAAK,GAAG,CAAC,GAAG9Y,IAAI,CAACuY,GAAG,CAACJ,SAAS,CAAC,IAAI1Z,MAAM,CAACmJ,UAAU,GAAG,CAAC,CAAC;QAC7D,IAAI,CAAC4Q,YAAY,CAACvE,aAAa,EAAE,CAAC,EAAEkE,SAAS,EAAEW,KAAK,EAAEA,KAAK,CAAC;QAC5D,IAAI9Y,IAAI,CAACuY,GAAG,CAACJ,SAAS,CAAC,GAAG,GAAG,EAAE;UAC3B,IAAI,CAACjI,KAAK,CACL5V,QAAQ,CAAC,eAAe,CAAC,CACzBkJ,WAAW,CAAC,oBAAoB,CAAC;QAC1C;MACJ;IACJ;EACJ,CAAC;EACDkK,YAAY,CAAClW,SAAS,CAACuhB,QAAQ,GAAG,UAAUf,SAAS,EAAED,WAAW,EAAEpZ,KAAK,EAAE;IACvE,IAAIqF,KAAK,GAAG,IAAI;IAChB,IAAIgV,QAAQ;IACZ;IACA,IAAI,IAAI,CAACzK,QAAQ,CAACvU,IAAI,KAAK,UAAU,EAAE;MACnC,IAAI,CAACkW,KAAK,CAAC5V,QAAQ,CAAC,UAAU,CAAC;IACnC;IACA;IACAyU,UAAU,CAAC,YAAY;MACnB/K,KAAK,CAACmM,UAAU,CAAC3M,WAAW,CAAC,sBAAsB,CAAC;MACpDQ,KAAK,CAACkM,KAAK,CACN1M,WAAW,CAAC,2BAA2B,CAAC,CACxClJ,QAAQ,CAAC,oBAAoB,CAAC;MACnC,IAAI2e,YAAY,GAAG,IAAI;MACvB,IAAIjV,KAAK,CAACsU,cAAc,KAAK,YAAY,EAAE;QACvCU,QAAQ,GAAGhB,SAAS,CAACE,KAAK,GAAGH,WAAW,CAACG,KAAK;QAC9C,IAAIgB,WAAW,GAAGlZ,IAAI,CAACuY,GAAG,CAACP,SAAS,CAACE,KAAK,GAAGH,WAAW,CAACG,KAAK,CAAC;QAC/D,IAAIc,QAAQ,GAAG,CAAC,IACZE,WAAW,GAAGlV,KAAK,CAACuK,QAAQ,CAACjR,cAAc,EAAE;UAC7C0G,KAAK,CAACmV,aAAa,CAAC,IAAI,CAAC;UACzBF,YAAY,GAAG,KAAK;QACxB,CAAC,MACI,IAAID,QAAQ,GAAG,CAAC,IACjBE,WAAW,GAAGlV,KAAK,CAACuK,QAAQ,CAACjR,cAAc,EAAE;UAC7C0G,KAAK,CAACoV,aAAa,CAAC,IAAI,CAAC;UACzBH,YAAY,GAAG,KAAK;QACxB;MACJ,CAAC,MACI,IAAIjV,KAAK,CAACsU,cAAc,KAAK,UAAU,EAAE;QAC1CU,QAAQ,GAAGhZ,IAAI,CAACuY,GAAG,CAACP,SAAS,CAACI,KAAK,GAAGL,WAAW,CAACK,KAAK,CAAC;QACxD,IAAIpU,KAAK,CAACuK,QAAQ,CAAC/S,QAAQ,IACvBwI,KAAK,CAACuK,QAAQ,CAAC9S,YAAY,IAC3Bud,QAAQ,GAAG,GAAG,EAAE;UAChBhV,KAAK,CAAC9F,YAAY,CAAC,CAAC;UACpB;QACJ,CAAC,MACI;UACD8F,KAAK,CAAC2M,SAAS,CAAC5M,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;QACrC;MACJ;MACAC,KAAK,CAACkM,KAAK,CAAChO,IAAI,CAAC,UAAU,CAAC,CAACO,UAAU,CAAC,OAAO,CAAC;MAChD,IAAIwW,YAAY,IACZjZ,IAAI,CAACuY,GAAG,CAACP,SAAS,CAACE,KAAK,GAAGH,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,EAAE;QACnD;QACA,IAAImB,MAAM,GAAGlX,GAAG,CAACxD,KAAK,CAAC0a,MAAM,CAAC;QAC9B,IAAIrV,KAAK,CAACsV,eAAe,CAACD,MAAM,CAAC,EAAE;UAC/BrV,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACY,WAAW,CAAC;QAC5C;MACJ;MACAgL,KAAK,CAACsU,cAAc,GAAGxa,SAAS;IACpC,CAAC,CAAC;IACF;IACAiR,UAAU,CAAC,YAAY;MACnB,IAAI,CAAC/K,KAAK,CAACkM,KAAK,CAACxM,QAAQ,CAAC,aAAa,CAAC,IACpCM,KAAK,CAACuK,QAAQ,CAACvU,IAAI,KAAK,UAAU,EAAE;QACpCgK,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,UAAU,CAAC;MACvC;IACJ,CAAC,EAAE,IAAI,CAAC+K,QAAQ,CAACrU,KAAK,GAAG,GAAG,CAAC;EACjC,CAAC;EACDwT,YAAY,CAAClW,SAAS,CAAC+F,WAAW,GAAG,YAAY;IAC7C,IAAIyG,KAAK,GAAG,IAAI;IAChB,IAAI+T,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIuB,OAAO,GAAG,KAAK;IACnB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAI,IAAI,CAACjL,QAAQ,CAAChR,WAAW,EAAE;MAC3B,IAAI,CAACqT,MAAM,CAAC3M,EAAE,CAAC,eAAe,EAAE,UAAUpC,CAAC,EAAE;QACzCmC,KAAK,CAACmK,kBAAkB,GAAG,IAAI;QAC/B,IAAIsL,KAAK,GAAGzV,KAAK,CAAC4L,YAAY,CAAC5L,KAAK,CAAClH,KAAK,CAAC;QAC3C,IAAI,CAACqF,GAAG,CAACN,CAAC,CAACwX,MAAM,CAAC,CAAC3V,QAAQ,CAAC,SAAS,CAAC,IAClC+V,KAAK,CAACjX,GAAG,CAAC,CAAC,CAACmB,QAAQ,CAAC9B,CAAC,CAACwX,MAAM,CAAC,KAC9B,CAACrV,KAAK,CAACkM,KAAK,CAACxM,QAAQ,CAAC,WAAW,CAAC,IAClC,CAACM,KAAK,CAAC8J,MAAM,IACbjM,CAAC,CAAC6X,OAAO,CAACpiB,MAAM,KAAK,CAAC,EAAE;UACxBkiB,SAAS,GAAG,IAAI;UAChBxV,KAAK,CAAC2V,WAAW,GAAG,OAAO;UAC3B3V,KAAK,CAAC4V,gBAAgB,CAAC,CAAC;UACxB7B,WAAW,GAAG;YACVG,KAAK,EAAErW,CAAC,CAAC6X,OAAO,CAAC,CAAC,CAAC,CAACxB,KAAK;YACzBE,KAAK,EAAEvW,CAAC,CAAC6X,OAAO,CAAC,CAAC,CAAC,CAACtB;UACxB,CAAC;QACL;MACJ,CAAC,CAAC;MACF,IAAI,CAACxH,MAAM,CAAC3M,EAAE,CAAC,cAAc,EAAE,UAAUpC,CAAC,EAAE;QACxC,IAAI2X,SAAS,IACTxV,KAAK,CAAC2V,WAAW,KAAK,OAAO,IAC7B9X,CAAC,CAAC6X,OAAO,CAACpiB,MAAM,KAAK,CAAC,EAAE;UACxB0gB,SAAS,GAAG;YACRE,KAAK,EAAErW,CAAC,CAAC6X,OAAO,CAAC,CAAC,CAAC,CAACxB,KAAK;YACzBE,KAAK,EAAEvW,CAAC,CAAC6X,OAAO,CAAC,CAAC,CAAC,CAACtB;UACxB,CAAC;UACDpU,KAAK,CAAC8T,SAAS,CAACC,WAAW,EAAEC,SAAS,EAAEnW,CAAC,CAAC;UAC1C0X,OAAO,GAAG,IAAI;QAClB;MACJ,CAAC,CAAC;MACF,IAAI,CAAC3I,MAAM,CAAC3M,EAAE,CAAC,aAAa,EAAE,UAAUtF,KAAK,EAAE;QAC3C,IAAIqF,KAAK,CAAC2V,WAAW,KAAK,OAAO,EAAE;UAC/B,IAAIJ,OAAO,EAAE;YACTA,OAAO,GAAG,KAAK;YACfvV,KAAK,CAAC+U,QAAQ,CAACf,SAAS,EAAED,WAAW,EAAEpZ,KAAK,CAAC;UACjD,CAAC,MACI,IAAI6a,SAAS,EAAE;YAChB,IAAIH,MAAM,GAAGlX,GAAG,CAACxD,KAAK,CAAC0a,MAAM,CAAC;YAC9B,IAAIrV,KAAK,CAACsV,eAAe,CAACD,MAAM,CAAC,EAAE;cAC/BrV,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACY,WAAW,CAAC;YAC5C;UACJ;UACAgL,KAAK,CAAC2V,WAAW,GAAG7b,SAAS;UAC7B0b,SAAS,GAAG,KAAK;QACrB;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;EACD9L,YAAY,CAAClW,SAAS,CAACgG,UAAU,GAAG,YAAY;IAC5C,IAAIwG,KAAK,GAAG,IAAI;IAChB,IAAI+T,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI6B,SAAS,GAAG,KAAK;IACrB,IAAIN,OAAO,GAAG,KAAK;IACnB,IAAI,IAAI,CAAChL,QAAQ,CAAC/Q,UAAU,EAAE;MAC1B,IAAI,CAAC0S,KAAK,CAACjM,EAAE,CAAC,cAAc,EAAE,UAAUpC,CAAC,EAAE;QACvCmC,KAAK,CAACmK,kBAAkB,GAAG,IAAI;QAC/B,IAAIsL,KAAK,GAAGzV,KAAK,CAAC4L,YAAY,CAAC5L,KAAK,CAAClH,KAAK,CAAC;QAC3C,IAAIqF,GAAG,CAACN,CAAC,CAACwX,MAAM,CAAC,CAAC3V,QAAQ,CAAC,SAAS,CAAC,IACjC+V,KAAK,CAACjX,GAAG,CAAC,CAAC,CAACmB,QAAQ,CAAC9B,CAAC,CAACwX,MAAM,CAAC,EAAE;UAChC,IAAI,CAACrV,KAAK,CAACkM,KAAK,CAACxM,QAAQ,CAAC,WAAW,CAAC,IAAI,CAACM,KAAK,CAAC8J,MAAM,EAAE;YACrDjM,CAAC,CAAC0N,cAAc,CAAC,CAAC;YAClB,IAAI,CAACvL,KAAK,CAAC8J,MAAM,EAAE;cACf9J,KAAK,CAAC4V,gBAAgB,CAAC,CAAC;cACxB7B,WAAW,GAAG;gBACVG,KAAK,EAAErW,CAAC,CAACqW,KAAK;gBACdE,KAAK,EAAEvW,CAAC,CAACuW;cACb,CAAC;cACDyB,SAAS,GAAG,IAAI;cAChB;cACA7V,KAAK,CAACkM,KAAK,CAAC1N,GAAG,CAAC,CAAC,CAACuD,UAAU,IAAI,CAAC;cACjC/B,KAAK,CAACkM,KAAK,CAAC1N,GAAG,CAAC,CAAC,CAACuD,UAAU,IAAI,CAAC;cACjC;cACA/B,KAAK,CAACkM,KAAK,CACN1M,WAAW,CAAC,SAAS,CAAC,CACtBlJ,QAAQ,CAAC,aAAa,CAAC;cAC5B0J,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACa,SAAS,CAAC;YAC1C;UACJ;QACJ;MACJ,CAAC,CAAC;MACFkJ,GAAG,CAAC1D,MAAM,CAAC,CAACwF,EAAE,CAAC,qBAAqB,GAAG,IAAI,CAACwJ,IAAI,EAAE,UAAU5L,CAAC,EAAE;QAC3D,IAAIgY,SAAS,IAAI7V,KAAK,CAAC4J,QAAQ,EAAE;UAC7B2L,OAAO,GAAG,IAAI;UACdvB,SAAS,GAAG;YACRE,KAAK,EAAErW,CAAC,CAACqW,KAAK;YACdE,KAAK,EAAEvW,CAAC,CAACuW;UACb,CAAC;UACDpU,KAAK,CAAC8T,SAAS,CAACC,WAAW,EAAEC,SAAS,CAAC;UACvChU,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACc,QAAQ,CAAC;QACzC;MACJ,CAAC,CAAC;MACFiJ,GAAG,CAAC1D,MAAM,CAAC,CAACwF,EAAE,CAAC,mBAAmB,GAAG,IAAI,CAACwJ,IAAI,EAAE,UAAU9O,KAAK,EAAE;QAC7D,IAAI,CAACqF,KAAK,CAAC4J,QAAQ,EAAE;UACjB;QACJ;QACA,IAAIyL,MAAM,GAAGlX,GAAG,CAACxD,KAAK,CAAC0a,MAAM,CAAC;QAC9B,IAAIE,OAAO,EAAE;UACTA,OAAO,GAAG,KAAK;UACfvV,KAAK,CAAC+U,QAAQ,CAACf,SAAS,EAAED,WAAW,EAAEpZ,KAAK,CAAC;UAC7CqF,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACe,OAAO,CAAC;QACxC,CAAC,MACI,IAAI6K,KAAK,CAACsV,eAAe,CAACD,MAAM,CAAC,EAAE;UACpCrV,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACY,WAAW,CAAC;QAC5C;QACA;QACA,IAAI6gB,SAAS,EAAE;UACXA,SAAS,GAAG,KAAK;UACjB7V,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,aAAa,CAAC,CAAClJ,QAAQ,CAAC,SAAS,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;EACDoT,YAAY,CAAClW,SAAS,CAACwX,kBAAkB,GAAG,YAAY;IACpD,IAAIhL,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC4M,MAAM,CAAC3M,EAAE,CAAC,UAAU,EAAE,UAAUtF,KAAK,EAAE;MACxC,IAAI,CAACqF,KAAK,CAACmK,kBAAkB,IACzBnK,KAAK,CAACsV,eAAe,CAACnX,GAAG,CAACxD,KAAK,CAAC0a,MAAM,CAAC,CAAC,EAAE;QAC1CrV,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACY,WAAW,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN,CAAC;EACD0U,YAAY,CAAClW,SAAS,CAACoiB,gBAAgB,GAAG,YAAY;IAClD,IAAIE,UAAU,GAAG,IAAI,CAAChd,KAAK,GAAG,CAAC;IAC/B,IAAIid,UAAU,GAAG,IAAI,CAACjd,KAAK,GAAG,CAAC;IAC/B,IAAI,IAAI,CAACyR,QAAQ,CAAC1S,IAAI,IAAI,IAAI,CAAC2S,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,IAAI,CAACwF,KAAK,KAAK,CAAC,EAAE;QAClBid,UAAU,GAAG,IAAI,CAACvL,YAAY,CAAClX,MAAM,GAAG,CAAC;MAC7C,CAAC,MACI,IAAI,IAAI,CAACwF,KAAK,KAAK,IAAI,CAAC0R,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;QAClDwiB,UAAU,GAAG,CAAC;MAClB;IACJ;IACA,IAAI,CAAC5J,KAAK,CAAChO,IAAI,CAAC,UAAU,CAAC,CAACsB,WAAW,CAAC,6BAA6B,CAAC;IACtE,IAAIuW,UAAU,GAAG,CAAC,CAAC,EAAE;MACjB,IAAI,CAACnK,YAAY,CAACmK,UAAU,CAAC,CAACzf,QAAQ,CAAC,eAAe,CAAC;IAC3D;IACA,IAAI,CAACsV,YAAY,CAACkK,UAAU,CAAC,CAACxf,QAAQ,CAAC,eAAe,CAAC;EAC3D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoT,YAAY,CAAClW,SAAS,CAAC2hB,aAAa,GAAG,UAAU9B,SAAS,EAAE;IACxD,IAAIrT,KAAK,GAAG,IAAI;IAChB,IAAIgW,KAAK,GAAG,IAAI,CAACzL,QAAQ,CAAC1S,IAAI;IAC9B,IAAIwb,SAAS,IAAI,IAAI,CAAC7I,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;MAC3C0iB,KAAK,GAAG,KAAK;IACjB;IACA,IAAI,CAAC,IAAI,CAAClM,MAAM,EAAE;MACd,IAAI,IAAI,CAAChR,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC0R,YAAY,CAAClX,MAAM,EAAE;QAC3C,IAAI,CAACwF,KAAK,EAAE;QACZ,IAAI,CAAC0K,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACgB,eAAe,EAAE;UACxC0D,KAAK,EAAE,IAAI,CAACA;QAChB,CAAC,CAAC;QACF,IAAI,CAACsW,KAAK,CAAC,IAAI,CAACtW,KAAK,EAAE,CAAC,CAACua,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC;MACtD,CAAC,MACI;QACD,IAAI2C,KAAK,EAAE;UACP,IAAI,CAACld,KAAK,GAAG,CAAC;UACd,IAAI,CAAC0K,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACgB,eAAe,EAAE;YACxC0D,KAAK,EAAE,IAAI,CAACA;UAChB,CAAC,CAAC;UACF,IAAI,CAACsW,KAAK,CAAC,IAAI,CAACtW,KAAK,EAAE,CAAC,CAACua,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC;QACtD,CAAC,MACI,IAAI,IAAI,CAAC9I,QAAQ,CAACrS,iBAAiB,IAAI,CAACmb,SAAS,EAAE;UACpD,IAAI,CAACnH,KAAK,CAAC5V,QAAQ,CAAC,cAAc,CAAC;UACnCyU,UAAU,CAAC,YAAY;YACnB/K,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,cAAc,CAAC;UAC3C,CAAC,EAAE,GAAG,CAAC;QACX;MACJ;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkK,YAAY,CAAClW,SAAS,CAAC4hB,aAAa,GAAG,UAAU/B,SAAS,EAAE;IACxD,IAAIrT,KAAK,GAAG,IAAI;IAChB,IAAIgW,KAAK,GAAG,IAAI,CAACzL,QAAQ,CAAC1S,IAAI;IAC9B,IAAIwb,SAAS,IAAI,IAAI,CAAC7I,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;MAC3C0iB,KAAK,GAAG,KAAK;IACjB;IACA,IAAI,CAAC,IAAI,CAAClM,MAAM,EAAE;MACd,IAAI,IAAI,CAAChR,KAAK,GAAG,CAAC,EAAE;QAChB,IAAI,CAACA,KAAK,EAAE;QACZ,IAAI,CAAC0K,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACiB,eAAe,EAAE;UACxCyD,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBua,SAAS,EAAEA;QACf,CAAC,CAAC;QACF,IAAI,CAACjE,KAAK,CAAC,IAAI,CAACtW,KAAK,EAAE,CAAC,CAACua,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC;MACtD,CAAC,MACI;QACD,IAAI2C,KAAK,EAAE;UACP,IAAI,CAACld,KAAK,GAAG,IAAI,CAAC0R,YAAY,CAAClX,MAAM,GAAG,CAAC;UACzC,IAAI,CAACkQ,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACiB,eAAe,EAAE;YACxCyD,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBua,SAAS,EAAEA;UACf,CAAC,CAAC;UACF,IAAI,CAACjE,KAAK,CAAC,IAAI,CAACtW,KAAK,EAAE,CAAC,CAACua,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC;QACtD,CAAC,MACI,IAAI,IAAI,CAAC9I,QAAQ,CAACrS,iBAAiB,IAAI,CAACmb,SAAS,EAAE;UACpD,IAAI,CAACnH,KAAK,CAAC5V,QAAQ,CAAC,aAAa,CAAC;UAClCyU,UAAU,CAAC,YAAY;YACnB/K,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,aAAa,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACX;MACJ;IACJ;EACJ,CAAC;EACDkK,YAAY,CAAClW,SAAS,CAACuE,QAAQ,GAAG,YAAY;IAC1C,IAAIiI,KAAK,GAAG,IAAI;IAChB7B,GAAG,CAAC1D,MAAM,CAAC,CAACwF,EAAE,CAAC,mBAAmB,GAAG,IAAI,CAACwJ,IAAI,EAAE,UAAU5L,CAAC,EAAE;MACzD,IAAImC,KAAK,CAAC4J,QAAQ,IACd5J,KAAK,CAACuK,QAAQ,CAACzS,MAAM,KAAK,IAAI,IAC9B+F,CAAC,CAACoY,OAAO,KAAK,EAAE,EAAE;QAClBpY,CAAC,CAAC0N,cAAc,CAAC,CAAC;QAClB,IAAIvL,KAAK,CAACuK,QAAQ,CAACvT,iBAAiB,IAChCgJ,KAAK,CAACkM,KAAK,CAACxM,QAAQ,CAAC,eAAe,CAAC,IACrCM,KAAK,CAACkM,KAAK,CAACxM,QAAQ,CAAC,oBAAoB,CAAC,EAAE;UAC5CM,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,oBAAoB,CAAC;QACjD,CAAC,MACI;UACDQ,KAAK,CAAC9F,YAAY,CAAC,CAAC;QACxB;MACJ;MACA,IAAI8F,KAAK,CAAC4J,QAAQ,IAAI5J,KAAK,CAACwK,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;QACjD,IAAIuK,CAAC,CAACoY,OAAO,KAAK,EAAE,EAAE;UAClBpY,CAAC,CAAC0N,cAAc,CAAC,CAAC;UAClBvL,KAAK,CAACoV,aAAa,CAAC,CAAC;QACzB;QACA,IAAIvX,CAAC,CAACoY,OAAO,KAAK,EAAE,EAAE;UAClBpY,CAAC,CAAC0N,cAAc,CAAC,CAAC;UAClBvL,KAAK,CAACmV,aAAa,CAAC,CAAC;QACzB;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EACDzL,YAAY,CAAClW,SAAS,CAACyX,KAAK,GAAG,YAAY;IACvC,IAAIjL,KAAK,GAAG,IAAI;IAChB,IAAI,CAACgM,cAAc,CAAC,SAAS,CAAC,CAAC/L,EAAE,CAAC,UAAU,EAAE,YAAY;MACtDD,KAAK,CAACoV,aAAa,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAACpJ,cAAc,CAAC,SAAS,CAAC,CAAC/L,EAAE,CAAC,UAAU,EAAE,YAAY;MACtDD,KAAK,CAACmV,aAAa,CAAC,CAAC;IACzB,CAAC,CAAC;EACN,CAAC;EACDzL,YAAY,CAAClW,SAAS,CAACkgB,YAAY,GAAG,UAAU5a,KAAK,EAAE;IACnD;IACA,IAAI,CAAC,IAAI,CAACyR,QAAQ,CAAC1S,IAAI,IAAI,IAAI,CAAC0S,QAAQ,CAACpS,gBAAgB,EAAE;MACvD,IAAI+d,KAAK,GAAG,IAAI,CAAClK,cAAc,CAAC,SAAS,CAAC;MAC1C,IAAImK,KAAK,GAAG,IAAI,CAACnK,cAAc,CAAC,SAAS,CAAC;MAC1C,IAAIlT,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC0R,YAAY,CAAClX,MAAM,EAAE;QACxC6iB,KAAK,CAACpY,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAACzH,QAAQ,CAAC,UAAU,CAAC;MAC3D,CAAC,MACI;QACD6f,KAAK,CAAC1X,UAAU,CAAC,UAAU,CAAC,CAACe,WAAW,CAAC,UAAU,CAAC;MACxD;MACA,IAAI1G,KAAK,KAAK,CAAC,EAAE;QACbod,KAAK,CAACnY,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAACzH,QAAQ,CAAC,UAAU,CAAC;MAC3D,CAAC,MACI;QACD4f,KAAK,CAACzX,UAAU,CAAC,UAAU,CAAC,CAACe,WAAW,CAAC,UAAU,CAAC;MACxD;IACJ;EACJ,CAAC;EACDkK,YAAY,CAAClW,SAAS,CAACghB,YAAY,GAAG,UAAU4B,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACjF,IAAID,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,CAAC;IAAE;IACrC,IAAIC,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,CAAC;IAAE;IACrCJ,GAAG,CAACrW,GAAG,CAAC,WAAW,EAAE,cAAc,GAC/BsW,MAAM,GACN,MAAM,GACNC,MAAM,GACN,mBAAmB,GACnBC,MAAM,GACN,IAAI,GACJC,MAAM,GACN,MAAM,CAAC;EACf,CAAC;EACD9M,YAAY,CAAClW,SAAS,CAAC4E,UAAU,GAAG,YAAY;IAC5C,IAAI4H,KAAK,GAAG,IAAI;IAChB,IAAIyW,QAAQ,GAAG,CAAC;IAChB,IAAI,CAACvK,KAAK,CAACjM,EAAE,CAAC,UAAU,EAAE,UAAUpC,CAAC,EAAE;MACnC,IAAI,CAACA,CAAC,CAAC6Y,MAAM,IAAI1W,KAAK,CAACwK,YAAY,CAAClX,MAAM,GAAG,CAAC,EAAE;QAC5C;MACJ;MACAuK,CAAC,CAAC0N,cAAc,CAAC,CAAC;MAClB,IAAIoL,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAC9B,IAAIF,GAAG,GAAGF,QAAQ,GAAG,IAAI,EAAE;QACvB;MACJ;MACAA,QAAQ,GAAGE,GAAG;MACd,IAAI9Y,CAAC,CAAC6Y,MAAM,GAAG,CAAC,EAAE;QACd1W,KAAK,CAACmV,aAAa,CAAC,CAAC;MACzB,CAAC,MACI,IAAItX,CAAC,CAAC6Y,MAAM,GAAG,CAAC,EAAE;QACnB1W,KAAK,CAACoV,aAAa,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC;EACD1L,YAAY,CAAClW,SAAS,CAACsjB,cAAc,GAAG,UAAUzB,MAAM,EAAE;IACtD,OAAQA,MAAM,CAAC3V,QAAQ,CAAC,UAAU,CAAC,IAC/B2V,MAAM,CAAC3V,QAAQ,CAAC,SAAS,CAAC,IAC1B2V,MAAM,CAAC3V,QAAQ,CAAC,aAAa,CAAC;EACtC,CAAC;EACDgK,YAAY,CAAClW,SAAS,CAAC8hB,eAAe,GAAG,UAAUD,MAAM,EAAE;IACvD,IAAI0B,UAAU,GAAG,IAAI,CAACnL,YAAY,CAAC,IAAI,CAAC9S,KAAK,CAAC,CACzCoF,IAAI,CAAC,uBAAuB,CAAC,CAC7BM,GAAG,CAAC,CAAC;IACV,OAAQ6W,MAAM,CAAC3V,QAAQ,CAAC,iBAAiB,CAAC,IACtC2V,MAAM,CAAC3V,QAAQ,CAAC,sBAAsB,CAAC,IACtCqX,UAAU,IAAIA,UAAU,CAACpX,QAAQ,CAAC0V,MAAM,CAAC7W,GAAG,CAAC,CAAC,CAAE;EACzD,CAAC;EACD;AACJ;AACA;AACA;EACIkL,YAAY,CAAClW,SAAS,CAAC2G,cAAc,GAAG,YAAY;IAChD,IAAI6F,KAAK,GAAG,IAAI;IAChB,IAAI,CAACgM,cAAc,CAAC,aAAa,CAAC,CAAC/L,EAAE,CAAC,UAAU,EAAE,YAAY;MAC1DD,KAAK,CAACmM,UAAU,CAACrM,WAAW,CAAC,WAAW,CAAC;MACzCE,KAAK,CAACgN,eAAe,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC;EACDtD,YAAY,CAAClW,SAAS,CAACwjB,eAAe,GAAG,YAAY;IACjD,KAAK,IAAIle,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACqP,KAAK,CAAC7U,MAAM,EAAEwF,KAAK,EAAE,EAAE;MACpD,IAAIiP,OAAO,GAAG,IAAI,CAACI,KAAK,CAACrP,KAAK,CAAC;MAC/B,IAAIuS,QAAQ,GAAGlN,GAAG,CAAC4J,OAAO,CAAC;MAC3BsD,QAAQ,CAAC5K,GAAG,CAAC,sBAAsB,GAAG4K,QAAQ,CAACtN,IAAI,CAAC,YAAY,CAAC,CAAC;IACtE;EACJ,CAAC;EACD2L,YAAY,CAAClW,SAAS,CAACwE,SAAS,GAAG,YAAY;IAC3C,IAAIgI,KAAK,GAAG,IAAI;IAChB,IAAI,CAACmM,UAAU,CAAC3N,GAAG,CAAC,CAAC,CAACyY,KAAK,CAAC;MACxBC,aAAa,EAAE;IACnB,CAAC,CAAC;IACF/Y,GAAG,CAAC1D,MAAM,CAAC,CAACwF,EAAE,CAAC,mBAAmB,GAAG,IAAI,CAACwJ,IAAI,EAAE,UAAU5L,CAAC,EAAE;MACzD,IAAI,CAACmC,KAAK,CAAC4J,QAAQ,EAAE;QACjB;MACJ;MACA,IAAIuN,YAAY,GAAGtZ,CAAC,CAAC2I,GAAG,KAAK,KAAK,IAAI3I,CAAC,CAACoY,OAAO,KAAK,CAAC;MACrD,IAAI,CAACkB,YAAY,EAAE;QACf;MACJ;MACA,IAAIC,YAAY,GAAGhU,KAAK,CAACwE,oBAAoB,CAAC5H,KAAK,CAACmM,UAAU,CAAC3N,GAAG,CAAC,CAAC,CAAC;MACrE,IAAI6Y,gBAAgB,GAAGD,YAAY,CAAC,CAAC,CAAC;MACtC,IAAIE,eAAe,GAAGF,YAAY,CAACA,YAAY,CAAC9jB,MAAM,GAAG,CAAC,CAAC;MAC3D,IAAIuK,CAAC,CAAC0Z,QAAQ,EAAE;QACZ,IAAItc,QAAQ,CAACuc,aAAa,KAAKH,gBAAgB,EAAE;UAC7CC,eAAe,CAACL,KAAK,CAAC,CAAC;UACvBpZ,CAAC,CAAC0N,cAAc,CAAC,CAAC;QACtB;MACJ,CAAC,MACI;QACD,IAAItQ,QAAQ,CAACuc,aAAa,KAAKF,eAAe,EAAE;UAC5CD,gBAAgB,CAACJ,KAAK,CAAC,CAAC;UACxBpZ,CAAC,CAAC0N,cAAc,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EACD7B,YAAY,CAAClW,SAAS,CAAC0Z,kBAAkB,GAAG,YAAY;IACpD,IAAIlN,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACuK,QAAQ,CAAC/S,QAAQ,EACvB;IACJ,IAAIigB,SAAS,GAAG,KAAK;IACrB,IAAI,CAACzL,cAAc,CAAC,UAAU,CAAC,CAAC/L,EAAE,CAAC,UAAU,EAAE,YAAY;MACvDD,KAAK,CAAC9F,YAAY,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,IAAI,CAACqQ,QAAQ,CAAC7S,UAAU,EAAE;MAC1B;MACA;MACA,IAAI,CAACwU,KAAK,CAACjM,EAAE,CAAC,cAAc,EAAE,UAAUpC,CAAC,EAAE;QACvC,IAAIwX,MAAM,GAAGlX,GAAG,CAACN,CAAC,CAACwX,MAAM,CAAC;QAC1B,IAAIrV,KAAK,CAAC8W,cAAc,CAACzB,MAAM,CAAC,EAAE;UAC9BoC,SAAS,GAAG,IAAI;QACpB,CAAC,MACI;UACDA,SAAS,GAAG,KAAK;QACrB;MACJ,CAAC,CAAC;MACF,IAAI,CAACvL,KAAK,CAACjM,EAAE,CAAC,cAAc,EAAE,YAAY;QACtCwX,SAAS,GAAG,KAAK;MACrB,CAAC,CAAC;MACF,IAAI,CAACvL,KAAK,CAACjM,EAAE,CAAC,YAAY,EAAE,UAAUpC,CAAC,EAAE;QACrC,IAAIwX,MAAM,GAAGlX,GAAG,CAACN,CAAC,CAACwX,MAAM,CAAC;QAC1B,IAAIrV,KAAK,CAAC8W,cAAc,CAACzB,MAAM,CAAC,IAAIoC,SAAS,EAAE;UAC3C,IAAI,CAACzX,KAAK,CAACkM,KAAK,CAACxM,QAAQ,CAAC,aAAa,CAAC,EAAE;YACtCM,KAAK,CAAC9F,YAAY,CAAC,CAAC;UACxB;QACJ;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIwP,YAAY,CAAClW,SAAS,CAAC0G,YAAY,GAAG,UAAUwd,KAAK,EAAE;IACnD,IAAI1X,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAAC4J,QAAQ,IAAK,CAAC,IAAI,CAACW,QAAQ,CAAC/S,QAAQ,IAAI,CAACkgB,KAAM,EAAE;MACvD,OAAO,CAAC;IACZ;IACA,IAAI,CAAClU,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACkB,WAAW,CAAC;IACvC,IAAI,IAAI,CAACiV,QAAQ,CAACjT,mBAAmB,IAAI,CAAC,IAAI,CAACiT,QAAQ,CAAChT,aAAa,EAAE;MACnE4G,GAAG,CAAC1D,MAAM,CAAC,CAACkH,SAAS,CAAC,IAAI,CAACqI,aAAa,CAAC;IAC7C;IACA,IAAIrB,WAAW,GAAG,IAAI,CAACR,KAAK,CAAC,IAAI,CAACrP,KAAK,CAAC;IACxC,IAAIuM,SAAS;IACb,IAAI,IAAI,CAAC1O,cAAc,IAAIgS,WAAW,EAAE;MACpC,IAAI4E,EAAE,GAAG,IAAI,CAACnD,sBAAsB;QAAEuN,KAAK,GAAGpK,EAAE,CAACpL,GAAG;QAAEqC,MAAM,GAAG+I,EAAE,CAAC/I,MAAM;MACxE,IAAIoT,EAAE,GAAG,IAAI,CAACpN,YAAY,CAAC,IAAI,CAAC1R,KAAK,CAAC;QAAEuU,gBAAgB,GAAGuK,EAAE,CAACvK,gBAAgB;QAAE+D,MAAM,GAAGwG,EAAE,CAACxG,MAAM;MAClG,IAAI3M,SAAS,GAAGrB,KAAK,CAACC,OAAO,CAACsF,WAAW,EAAE,IAAI,CAACuD,KAAK,EAAEyL,KAAK,GAAGnT,MAAM,EAAE6I,gBAAgB,IAAI+D,MAAM,IAAI,IAAI,CAAC7G,QAAQ,CAACtT,YAAY,CAAC;MAChIoO,SAAS,GAAGjC,KAAK,CAACmB,YAAY,CAACoE,WAAW,EAAE,IAAI,CAACuD,KAAK,EAAEyL,KAAK,EAAEnT,MAAM,EAAEC,SAAS,CAAC;IACrF;IACA,IAAI,IAAI,CAAC9N,cAAc,IAAI0O,SAAS,EAAE;MAClC,IAAI,CAAC6G,KAAK,CAAC5V,QAAQ,CAAC,+BAA+B,CAAC;MACpD,IAAI,CAACsV,YAAY,CAAC,IAAI,CAAC9S,KAAK,CAAC,CACxBxC,QAAQ,CAAC,uBAAuB,CAAC,CACjCyJ,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACwK,QAAQ,CAAC7T,sBAAsB,GAAG,IAAI,CAAC,CACvEqJ,GAAG,CAAC,WAAW,EAAEsF,SAAS,CAAC;IACpC,CAAC,MACI;MACD,IAAI,CAAC6G,KAAK,CAAC5V,QAAQ,CAAC,eAAe,CAAC;MACpC;MACA;MACA,IAAI,CAAC4V,KAAK,CAAC1M,WAAW,CAAC,oBAAoB,CAAC;IAChD;IACA;IACA;IACA;IACA,IAAI,CAACqY,cAAc,CAAC,CAAC;IACrB,IAAI,CAAChO,UAAU,GAAG,KAAK;IACvB,IAAI,CAACK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACvT,cAAc,GAAG,IAAI,CAAC4T,QAAQ,CAAC5T,cAAc;IAClD6Y,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3BtR,GAAG,CAAC,MAAM,CAAC,CAACqB,WAAW,CAAC,OAAO,CAAC;IAChC,IAAI,CAAC0M,KAAK,CAAC1M,WAAW,CAAC,+BAA+B,CAAC;IACvD;IACA,IAAI,CAACmN,SAAS,CAACnN,WAAW,CAAC,IAAI,CAAC,CAACO,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;IAClD,IAAI+X,aAAa,GAAG,IAAI,CAACnhB,cAAc,IAAI0O,SAAS,GAC9CrJ,IAAI,CAACsW,GAAG,CAAC,IAAI,CAAC/H,QAAQ,CAAC7T,sBAAsB,EAAE,IAAI,CAAC6T,QAAQ,CAAC/T,gBAAgB,CAAC,GAC9E,IAAI,CAAC+T,QAAQ,CAAC/T,gBAAgB;IACpC,IAAI,CAAC2V,UAAU,CAAC3M,WAAW,CAAC,YAAY,CAAC;IACzC;IACAuL,UAAU,CAAC,YAAY;MACnB,IAAI/K,KAAK,CAACrJ,cAAc,IAAI0O,SAAS,EAAE;QACnCrF,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,oBAAoB,CAAC;MACjD;MACAQ,KAAK,CAACmM,UAAU,CAAC3M,WAAW,CAAC,SAAS,CAAC;MACvC;MACAQ,KAAK,CAAC6O,cAAc,CAAC,CAAC;MACtB;MACA7O,KAAK,CAAC2M,SAAS,CACVlO,UAAU,CAAC,OAAO,CAAC,CACnBsB,GAAG,CAAC,qBAAqB,EAAEC,KAAK,CAACuK,QAAQ,CAAC/T,gBAAgB,GAAG,IAAI,CAAC;MACvEwJ,KAAK,CAACkM,KAAK,CAAC1M,WAAW,CAAC,aAAa,GAAGQ,KAAK,CAACuK,QAAQ,CAAChU,UAAU,CAAC;MAClEyJ,KAAK,CAAC4L,YAAY,CAAC5L,KAAK,CAAClH,KAAK,CAAC,CAAC0G,WAAW,CAAC,uBAAuB,CAAC;MACpEQ,KAAK,CAAC4M,MAAM,CAAClL,KAAK,CAAC,CAAC;MACpB,IAAI1B,KAAK,CAAC4J,QAAQ,EAAE;QAChB5J,KAAK,CAACwD,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACmB,UAAU,EAAE;UACpCuV,QAAQ,EAAE9K;QACd,CAAC,CAAC;MACN;MACA,IAAIA,KAAK,CAACmM,UAAU,CAAC3N,GAAG,CAAC,CAAC,EAAE;QACxBwB,KAAK,CAACmM,UAAU,CAAC3N,GAAG,CAAC,CAAC,CAACuZ,IAAI,CAAC,CAAC;MACjC;MACA/X,KAAK,CAAC4J,QAAQ,GAAG,KAAK;IAC1B,CAAC,EAAEkO,aAAa,GAAG,GAAG,CAAC;IACvB,OAAOA,aAAa,GAAG,GAAG;EAC9B,CAAC;EACDpO,YAAY,CAAClW,SAAS,CAAC2Z,WAAW,GAAG,YAAY;IAC7C,IAAI,CAACnT,OAAO,CAAC2C,OAAO,CAAC,UAAUqb,MAAM,EAAE;MACnC,IAAI;QACAA,MAAM,CAAC1jB,IAAI,CAAC,CAAC;MACjB,CAAC,CACD,OAAO2jB,GAAG,EAAE;QACR5O,OAAO,CAACsC,IAAI,CAAC,oEAAoE,CAAC;MACtF;IACJ,CAAC,CAAC;EACN,CAAC;EACDjC,YAAY,CAAClW,SAAS,CAACqkB,cAAc,GAAG,UAAUK,OAAO,EAAE;IACvD,IAAI,CAACle,OAAO,CAAC2C,OAAO,CAAC,UAAUqb,MAAM,EAAE;MACnC,IAAI;QACA,IAAIE,OAAO,EAAE;UACTF,MAAM,CAACE,OAAO,CAAC,CAAC;QACpB,CAAC,MACI;UACDF,MAAM,CAAC9d,YAAY,IAAI8d,MAAM,CAAC9d,YAAY,CAAC,CAAC;QAChD;MACJ,CAAC,CACD,OAAO+d,GAAG,EAAE;QACR5O,OAAO,CAACsC,IAAI,CAAC,oEAAoE,CAAC;MACtF;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIjC,YAAY,CAAClW,SAAS,CAAC2kB,OAAO,GAAG,UAAU3N,YAAY,EAAE;IACrD,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC9Q,OAAO,EAAE;MACxB,IAAI,CAACud,eAAe,CAAC,CAAC;IAC1B;IACA,IAAIxM,YAAY,EAAE;MACd,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC,CAAC,MACI;MACD,IAAI,CAACA,YAAY,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IACvC;IACA,IAAI,CAACwD,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC/C,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC1H,IAAI,CAAC5C,OAAO,CAACxM,QAAQ,CAACK,YAAY,CAAC;EAC5C,CAAC;EACDiV,YAAY,CAAClW,SAAS,CAACya,cAAc,GAAG,YAAY;IAChD,IAAI,CAACrD,iBAAiB,CAAC,IAAI,CAACJ,YAAY,CAAC;IACzC,IAAI,CAACqJ,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC5H,0BAA0B,CAAC,CAAC;EACrC,CAAC;EACDvC,YAAY,CAAClW,SAAS,CAAC4kB,cAAc,GAAG,YAAY;IAChD,IAAI,CAACP,cAAc,CAAC,IAAI,CAAC;IACzB,IAAI,CAAC,IAAI,CAACtN,QAAQ,CAAC9Q,OAAO,EAAE;MACxB,IAAI,CAACud,eAAe,CAAC,CAAC;IAC1B;IACA7Y,GAAG,CAAC1D,MAAM,CAAC,CAACgG,GAAG,CAAC,YAAY,GAAG,IAAI,CAACgJ,IAAI,CAAC;IACzC,IAAI,CAACjG,IAAI,CAAC/C,GAAG,CAAC,KAAK,CAAC;IACpB,IAAI,CAAC0L,UAAU,CAAC1M,MAAM,CAAC,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiK,YAAY,CAAClW,SAAS,CAAC0kB,OAAO,GAAG,YAAY;IACzC,IAAIG,YAAY,GAAG,IAAI,CAACne,YAAY,CAAC,IAAI,CAAC;IAC1C,IAAIme,YAAY,EAAE;MACdtN,UAAU,CAAC,IAAI,CAACqN,cAAc,CAACE,IAAI,CAAC,IAAI,CAAC,EAAED,YAAY,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACD,cAAc,CAAC,CAAC;IACzB;IACA,OAAOC,YAAY;EACvB,CAAC;EACD,OAAO3O,YAAY;AACvB,CAAC,CAAC,CAAE;AAEJ,SAAS6O,YAAYA,CAAC1b,EAAE,EAAE8M,OAAO,EAAE;EAC/B,OAAO,IAAID,YAAY,CAAC7M,EAAE,EAAE8M,OAAO,CAAC;AACxC;AAEA,eAAe4O,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, ElementRef, Component, ChangeDetectionStrategy, Inject, ViewChild, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@alyle/ui';\nimport { st2c, StyleCollection, LY_COMMON_STYLES, mergeDeep, StyleRenderer, Style2 } from '@alyle/ui';\nimport { Subject, Observable } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2 from '@angular/cdk/scrolling';\n\n/*\n * Hermite resize - fast image resize/resample using Hermite filter.\n * https://github.com/viliusle/Hermite-resize\n */\nconst _c0 = [\"_imgContainer\"];\nconst _c1 = [\"_area\"];\nconst _c2 = [\"_imgCanvas\"];\nconst _c3 = function (a0, a1) {\n  return {\n    width: a0,\n    height: a1\n  };\n};\nfunction LyImageCropper_ly_cropper_area_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ly-cropper-area\", 5, 6);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"round\", !!ctx_r2.config.round)(\"resizableArea\", !!ctx_r2.config.resizableArea)(\"keepAspectRatio\", !!ctx_r2.config.keepAspectRatio)(\"ngStyle\", i0.ɵɵpureFunction2(4, _c3, ctx_r2.config.width + \"px\", ctx_r2.config.height + \"px\"));\n  }\n}\nfunction LyImageCropper_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"input\", 8, 9);\n    i0.ɵɵlistener(\"change\", function LyImageCropper_ng_template_5_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.selectInputEvent($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵprojection(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"className\", ctx_r4.classes.defaultContent);\n  }\n}\nconst _c4 = [\"*\"];\nconst _c5 = [\"resizer\"];\nfunction LyCropperArea_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", null, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.classes.resizer);\n  }\n}\nfunction resizeCanvas(canvas, width, height) {\n  const width_source = canvas.width;\n  const height_source = canvas.height;\n  width = Math.round(width);\n  height = Math.round(height);\n  const ratio_w = width_source / width;\n  const ratio_h = height_source / height;\n  const ratio_w_half = Math.ceil(ratio_w / 2);\n  const ratio_h_half = Math.ceil(ratio_h / 2);\n  const ctx = canvas.getContext('2d');\n  const img = ctx.getImageData(0, 0, width_source, height_source);\n  const img2 = ctx.createImageData(width, height);\n  const data = img.data;\n  const data2 = img2.data;\n  for (let j = 0; j < height; j++) {\n    for (let i = 0; i < width; i++) {\n      const x2 = (i + j * width) * 4;\n      let weight = 0;\n      let weights = 0;\n      let weights_alpha = 0;\n      let gx_r = 0;\n      let gx_g = 0;\n      let gx_b = 0;\n      let gx_a = 0;\n      const center_y = j * ratio_h;\n      const xx_start = Math.floor(i * ratio_w);\n      let xx_stop = Math.ceil((i + 1) * ratio_w);\n      const yy_start = Math.floor(j * ratio_h);\n      let yy_stop = Math.ceil((j + 1) * ratio_h);\n      xx_stop = Math.min(xx_stop, width_source);\n      yy_stop = Math.min(yy_stop, height_source);\n      for (let yy = yy_start; yy < yy_stop; yy++) {\n        const dy = Math.abs(center_y - yy) / ratio_h_half;\n        const center_x = i * ratio_w;\n        const w0 = dy * dy; // pre-calc part of w\n        for (let xx = xx_start; xx < xx_stop; xx++) {\n          const dx = Math.abs(center_x - xx) / ratio_w_half;\n          const w = Math.sqrt(w0 + dx * dx);\n          if (w >= 1) {\n            // pixel too far\n            continue;\n          }\n          // hermite filter\n          weight = 2 * w * w * w - 3 * w * w + 1;\n          const pos_x = 4 * (xx + yy * width_source);\n          // alpha\n          gx_a += weight * data[pos_x + 3];\n          weights_alpha += weight;\n          // colors\n          if (data[pos_x + 3] < 255) {\n            weight = weight * data[pos_x + 3] / 250;\n          }\n          gx_r += weight * data[pos_x];\n          gx_g += weight * data[pos_x + 1];\n          gx_b += weight * data[pos_x + 2];\n          weights += weight;\n        }\n      }\n      data2[x2] = gx_r / weights;\n      data2[x2 + 1] = gx_g / weights;\n      data2[x2 + 2] = gx_b / weights;\n      data2[x2 + 3] = gx_a / weights_alpha;\n    }\n  }\n  // clear and resize canvas\n  canvas.width = width;\n  canvas.height = height;\n  // draw\n  ctx.putImageData(img2, 0, 0);\n  return ctx;\n}\nconst activeEventOptions = normalizePassiveListenerOptions({\n  passive: false\n});\nconst STYLE_PRIORITY = -2;\nconst DATA_IMAGE_SVG_PREFIX = 'data:image/svg+xml;base64,';\nconst pos = (100 * Math.sqrt(2) - 100) / 2 / Math.sqrt(2);\nconst STYLES = (theme, ref) => {\n  const cropper = ref.selectorsOf(STYLES);\n  const {\n    after\n  } = theme;\n  return {\n    $name: LyImageCropper.и,\n    $priority: STYLE_PRIORITY,\n    root: () => _className => `${_className}{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;overflow:hidden;position:relative;justify-content:center;align-items:center;}${st2c(theme.cropper && theme.cropper.root && (theme.cropper.root instanceof StyleCollection ? theme.cropper.root.setTransformer(fn => fn(cropper)) : theme.cropper.root(cropper)), `${_className}`)}`,\n    imgContainer: _className => `${_className}{cursor:move;position:absolute;top:0;left:0;display:flex;touch-action:none;}${_className} > canvas{display:block;}`,\n    overlay: _className => `${st2c(LY_COMMON_STYLES.fill, `${_className}`)}`,\n    area: _className => `${_className}{pointer-events:none;box-shadow:0 0 0 20000px rgba(0,0,0,0.4);}${st2c(LY_COMMON_STYLES.fill, `${_className}`)}${_className}{margin:auto;}${st2c(LY_COMMON_STYLES.fill, `${_className}:before,${_className}:after`)}${_className}:before,${_className}:after{content:'';}${_className}:before{width:0;height:0;margin:auto;border-radius:50%;background:#fff;border:solid 2px rgb(255,255,255);}${_className}:after{border:solid 2px rgb(255,255,255);border-radius:inherit;}`,\n    resizer: _className => `${_className}{width:10px;height:10px;background:#fff;border-radius:3px;position:absolute;touch-action:none;bottom:0;${after}:0;pointer-events:all;cursor:${after === 'right' ? 'nwse-resize' : 'nesw-resize'};}${st2c(LY_COMMON_STYLES.fill, `${_className}:before`)}${_className}:before{content:'';width:20px;height:20px;transform:translate(-25%,-25%);}`,\n    defaultContent: _className => `${_className}{display:flex;align-items:center;justify-content:center;}${st2c(LY_COMMON_STYLES.fill, `${_className},${_className} > input`)}${_className} *:not(input){pointer-events:none;}${_className} > input{background:transparent;opacity:0;width:100%;height:100%;}`\n  };\n};\n/** Image Cropper Config */\nclass ImgCropperConfig {\n  constructor() {\n    /** Cropper area width */\n    this.width = 250;\n    /** Cropper area height */\n    this.height = 200;\n    this.minWidth = 40;\n    this.minHeight = 40;\n    /**\n     * Set anti-aliased (default: true)\n     * @deprecated this is not necessary as the cropper will automatically resize the image\n     * to the best quality\n     */\n    this.antiAliased = true;\n    this.output = ImgResolution.Default;\n  }\n}\n/** Image output */\nvar ImgResolution;\n(function (ImgResolution) {\n  /**\n   * The output image will be equal to the initial size of the cropper area.\n   */\n  ImgResolution[ImgResolution[\"Default\"] = 0] = \"Default\";\n  /** Just crop the image without resizing */\n  ImgResolution[ImgResolution[\"OriginalImage\"] = 1] = \"OriginalImage\";\n})(ImgResolution || (ImgResolution = {}));\n/** Image output */\nvar ImgCropperError;\n(function (ImgCropperError) {\n  /** The loaded image exceeds the size limit set. */\n  ImgCropperError[ImgCropperError[\"Size\"] = 0] = \"Size\";\n  /** The file loaded is not image. */\n  ImgCropperError[ImgCropperError[\"Type\"] = 1] = \"Type\";\n  /** When the image has not been loaded. */\n  ImgCropperError[ImgCropperError[\"Other\"] = 2] = \"Other\";\n})(ImgCropperError || (ImgCropperError = {}));\nclass LyImageCropper {\n  constructor(sRenderer, _renderer, _elementRef, cd, _ngZone, _document, viewPortRuler) {\n    this.sRenderer = sRenderer;\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n    this.cd = cd;\n    this._ngZone = _ngZone;\n    /**\n     * styles\n     * @docs-private\n     */\n    this.classes = this.sRenderer.renderSheet(STYLES, true);\n    this._imgRect = {};\n    this._rotation = 0;\n    this.scaleChange = new EventEmitter();\n    /** Emits minimum supported image scale */\n    this.minScaleChange = new EventEmitter();\n    /** Emits maximum supported image scale */\n    this.maxScaleChange = new EventEmitter();\n    /** @deprecated Emits when the image is loaded, instead use `ready` */\n    this.loaded = new EventEmitter();\n    /** Emits when the image is loaded */\n    this.imageLoaded = new EventEmitter();\n    /** Emits when the cropper is ready to be interacted */\n    this.ready = new EventEmitter();\n    /** On crop new image */\n    this.cropped = new EventEmitter();\n    /** Emits when the cropper is cleaned */\n    this.cleaned = new EventEmitter();\n    /** Emit an error when the loaded image is not valid */\n    // tslint:disable-next-line: no-output-native\n    this.error = new EventEmitter();\n    /** Emits whenever the component is destroyed. */\n    this._destroy = new Subject();\n    this._pointerDown = event => {\n      // Don't do anything if the\n      // user is using anything other than the main mouse button.\n      if (this._isSliding || !isTouchEvent(event) && event.button !== 0) {\n        return;\n      }\n      this._ngZone.run(() => {\n        this._isSliding = true;\n        this.offset = {\n          x: this._imgRect.x,\n          y: this._imgRect.y,\n          left: this._imgRect.xc,\n          top: this._imgRect.yc\n        };\n        this._lastPointerEvent = event;\n        this._startPointerEvent = getGesturePointFromEvent(event);\n        event.preventDefault();\n        this._bindGlobalEvents(event);\n      });\n    };\n    /**\n     * Called when the user has moved their pointer after\n     * starting to drag.\n     */\n    this._pointerMove = event => {\n      if (this._isSliding) {\n        event.preventDefault();\n        this._lastPointerEvent = event;\n        let x, y;\n        const canvas = this._imgCanvas.nativeElement;\n        const scaleFix = this._scal3Fix;\n        const config = this.config;\n        const startP = this.offset;\n        const point = getGesturePointFromEvent(event);\n        const deltaX = point.x - this._startPointerEvent.x;\n        const deltaY = point.y - this._startPointerEvent.y;\n        if (!scaleFix || !startP) {\n          return;\n        }\n        const isMinScaleY = canvas.height * scaleFix < config.height && config.extraZoomOut;\n        const isMinScaleX = canvas.width * scaleFix < config.width && config.extraZoomOut;\n        const limitLeft = config.width / 2 / scaleFix >= startP.left - deltaX / scaleFix;\n        const limitRight = config.width / 2 / scaleFix + canvas.width - (startP.left - deltaX / scaleFix) <= config.width / scaleFix;\n        const limitTop = config.height / 2 / scaleFix >= startP.top - deltaY / scaleFix;\n        const limitBottom = config.height / 2 / scaleFix + canvas.height - (startP.top - deltaY / scaleFix) <= config.height / scaleFix;\n        // Limit for left\n        if (limitLeft && !isMinScaleX || !limitLeft && isMinScaleX) {\n          x = startP.x + startP.left - config.width / 2 / scaleFix;\n        }\n        // Limit for right\n        if (limitRight && !isMinScaleX || !limitRight && isMinScaleX) {\n          x = startP.x + startP.left + config.width / 2 / scaleFix - canvas.width;\n        }\n        // Limit for top\n        if (limitTop && !isMinScaleY || !limitTop && isMinScaleY) {\n          y = startP.y + startP.top - config.height / 2 / scaleFix;\n        }\n        // Limit for bottom\n        if (limitBottom && !isMinScaleY || !limitBottom && isMinScaleY) {\n          y = startP.y + startP.top + config.height / 2 / scaleFix - canvas.height;\n        }\n        // When press shiftKey, deprecated\n        // if (event.srcEvent && event.srcEvent.shiftKey) {\n        //   if (Math.abs(event.deltaX) === Math.max(Math.abs(event.deltaX), Math.abs(event.deltaY))) {\n        //     y = this.offset.top;\n        //   } else {\n        //     x = this.offset.left;\n        //   }\n        // }\n        if (x === void 0) {\n          x = deltaX / scaleFix + startP.x;\n        }\n        if (y === void 0) {\n          y = deltaY / scaleFix + startP.y;\n        }\n        this._setStylesForContImg({\n          x,\n          y\n        });\n      }\n    };\n    /** Called when the user has lifted their pointer. */\n    this._pointerUp = event => {\n      if (this._isSliding) {\n        event.preventDefault();\n        this._removeGlobalEvents();\n        this._isSliding = false;\n        this._startPointerEvent = null;\n        this._cropIfAutoCrop();\n      }\n    };\n    /** Called when the window has lost focus. */\n    this._windowBlur = () => {\n      // If the window is blurred while dragging we need to stop dragging because the\n      // browser won't dispatch the `mouseup` and `touchend` events anymore.\n      if (this._lastPointerEvent) {\n        this._pointerUp(this._lastPointerEvent);\n      }\n    };\n    this._document = _document;\n    viewPortRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => this._ngZone.run(() => this.updateCropperPosition()));\n  }\n  get config() {\n    return this._config;\n  }\n  set config(val) {\n    this._config = mergeDeep({}, new ImgCropperConfig(), val);\n    this._configPrimary = mergeDeep({}, this._config);\n    this._primaryAreaWidth = this.config.width;\n    this._primaryAreaHeight = this.config.height;\n    if (this._config.round && this.config.width !== this.config.height) {\n      throw new Error(`${LyImageCropper.и}: Both width and height must be equal when using \\`ImgCropperConfig.round = true\\``);\n    }\n    const maxFileSize = this._config.maxFileSize;\n    if (maxFileSize) {\n      this.maxFileSize = maxFileSize;\n    }\n  }\n  /** Set scale */\n  get scale() {\n    return this._scale;\n  }\n  set scale(val) {\n    this.setScale(val);\n  }\n  /** Get min scale */\n  get minScale() {\n    return this._minScale;\n  }\n  ngOnInit() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._imgContainer.nativeElement;\n      element.addEventListener('mousedown', this._pointerDown, activeEventOptions);\n      element.addEventListener('touchstart', this._pointerDown, activeEventOptions);\n    });\n  }\n  ngOnDestroy() {\n    this._destroy.next();\n    this._destroy.complete();\n    const element = this._imgContainer.nativeElement;\n    this._lastPointerEvent = null;\n    this._removeGlobalEvents();\n    element.removeEventListener('mousedown', this._pointerDown, activeEventOptions);\n    element.removeEventListener('touchstart', this._pointerDown, activeEventOptions);\n  }\n  /** Load image with canvas */\n  _imgLoaded(imgElement) {\n    if (imgElement) {\n      this._img = imgElement;\n      const canvas = this._imgCanvas.nativeElement;\n      canvas.width = imgElement.width;\n      canvas.height = imgElement.height;\n      const ctx = canvas.getContext('2d');\n      ctx.clearRect(0, 0, imgElement.width, imgElement.height);\n      ctx.drawImage(imgElement, 0, 0);\n      /** set min scale */\n      this._updateMinScale(canvas);\n      this._updateMaxScale();\n    }\n  }\n  _setStylesForContImg(values) {\n    const newStyles = {};\n    if (values.x != null && values.y != null) {\n      const rootRect = this._rootRect();\n      const x = rootRect.width / 2 - values.x;\n      const y = rootRect.height / 2 - values.y;\n      this._imgRect.x = values.x;\n      this._imgRect.y = values.y;\n      this._imgRect.xc = x;\n      this._imgRect.yc = y;\n    }\n    newStyles.transform = `translate3d(${this._imgRect.x}px,${this._imgRect.y}px, 0)`;\n    newStyles.transform += `scale(${this._scal3Fix})`;\n    newStyles.transformOrigin = `${this._imgRect.xc}px ${this._imgRect.yc}px 0`;\n    newStyles['-webkit-transform'] = newStyles.transform;\n    newStyles['-webkit-transform-origin'] = newStyles.transformOrigin;\n    for (const key in newStyles) {\n      if (newStyles.hasOwnProperty(key)) {\n        this._renderer.setStyle(this._imgContainer.nativeElement, key, newStyles[key]);\n      }\n    }\n  }\n  /**\n   * Update area and image position only if needed,\n   * this is used when window resize\n   */\n  updateCropperPosition() {\n    if (this.isLoaded) {\n      this.updatePosition();\n      this._updateAreaIfNeeded();\n    }\n  }\n  /** Load Image from input event */\n  selectInputEvent(img) {\n    this._currentInputElement = img.target;\n    const _img = img.target;\n    if (_img.files && _img.files.length !== 1) {\n      return;\n    }\n    const fileSize = _img.files[0].size;\n    const fileName = _img.value.replace(/.*(\\/|\\\\)/, '');\n    if (this.maxFileSize && fileSize > this.maxFileSize) {\n      const cropEvent = {\n        name: fileName,\n        type: _img.files[0].type,\n        size: fileSize,\n        error: ImgCropperError.Size\n      };\n      this.clean();\n      this.error.emit(cropEvent);\n      return;\n    }\n    new Observable(observer => {\n      const reader = new FileReader();\n      reader.onerror = err => observer.error(err);\n      reader.onabort = err => observer.error(err);\n      reader.onload = ev => setTimeout(() => {\n        observer.next(ev);\n        observer.complete();\n      });\n      reader.readAsDataURL(_img.files[0]);\n    }).pipe(take(1), takeUntil(this._destroy)).subscribe(loadEvent => {\n      const originalDataURL = loadEvent.target.result;\n      this.loadImage({\n        name: fileName,\n        size: _img.files[0].size,\n        type: this.config.type || _img.files[0].type,\n        originalDataURL\n      });\n      this.cd.markForCheck();\n    }, () => {\n      const cropEvent = {\n        name: fileName,\n        size: fileSize,\n        error: ImgCropperError.Other,\n        errorMsg: 'The File could not be loaded.',\n        type: _img.files[0].type\n      };\n      this.clean();\n      this.error.emit(cropEvent);\n    });\n  }\n  /** Set the size of the image, the values can be 0 between 1, where 1 is the original size */\n  setScale(size, noAutoCrop) {\n    // fix min scale\n    const newSize = size >= this.minScale && size <= 1 ? size : this.minScale;\n    // check\n    const changed = size != null && size !== this.scale && newSize !== this.scale;\n    this._scale = size;\n    if (!changed) {\n      return;\n    }\n    this._scal3Fix = newSize;\n    this._updateAbsoluteScale();\n    if (this.isLoaded) {\n      if (changed) {\n        const originPosition = {\n          ...this._imgRect\n        };\n        this.offset = {\n          x: originPosition.x,\n          y: originPosition.y,\n          left: originPosition.xc,\n          top: originPosition.yc\n        };\n        this._setStylesForContImg({});\n        this._simulatePointerMove();\n      } else {\n        return;\n      }\n    } else if (this.minScale) {\n      this._setStylesForContImg({\n        ...this._getCenterPoints()\n      });\n    } else {\n      return;\n    }\n    this.scaleChange.emit(size);\n    if (!noAutoCrop) {\n      this._cropIfAutoCrop();\n    }\n  }\n  _getCenterPoints() {\n    const root = this._elementRef.nativeElement;\n    const img = this._imgCanvas.nativeElement;\n    const x = (root.offsetWidth - img.width) / 2;\n    const y = (root.offsetHeight - img.height) / 2;\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Fit to screen\n   */\n  fitToScreen() {\n    const container = this._elementRef.nativeElement;\n    const min = {\n      width: container.offsetWidth,\n      height: container.offsetHeight\n    };\n    const {\n      width,\n      height\n    } = this._img;\n    const minScale = {\n      width: min.width / width,\n      height: min.height / height\n    };\n    const result = Math.max(minScale.width, minScale.height);\n    this.setScale(result);\n  }\n  fit() {\n    this.setScale(this.minScale);\n  }\n  /**\n   * Simulate pointerMove with clientX = 0 and clientY = 0,\n   * this is used by `setScale` and `rotate`\n   */\n  _simulatePointerMove() {\n    this._isSliding = true;\n    this._startPointerEvent = {\n      x: 0,\n      y: 0\n    };\n    this._pointerMove({\n      clientX: 0,\n      clientY: 0,\n      type: 'n',\n      preventDefault: () => {}\n    });\n    this._isSliding = false;\n    this._startPointerEvent = null;\n  }\n  _markForCheck() {\n    this.cd.markForCheck();\n  }\n  updatePosition(xOrigin, yOrigin) {\n    const hostRect = this._rootRect();\n    const areaRect = this._areaCropperRect();\n    const areaWidth = areaRect.width > hostRect.width ? hostRect.width : areaRect.width;\n    const areaHeight = areaRect.height > hostRect.height ? hostRect.height : areaRect.height;\n    let x, y;\n    if (xOrigin == null && yOrigin == null) {\n      xOrigin = this._imgRect.xc;\n      yOrigin = this._imgRect.yc;\n    }\n    x = areaRect.left - hostRect.left;\n    y = areaRect.top - hostRect.top;\n    x -= xOrigin - areaWidth / 2;\n    y -= yOrigin - areaHeight / 2;\n    this._setStylesForContImg({\n      x,\n      y\n    });\n  }\n  _slideEnd() {\n    this._cropIfAutoCrop();\n  }\n  _cropIfAutoCrop() {\n    if (this.config.autoCrop) {\n      this.crop();\n    }\n  }\n  /** + */\n  zoomIn() {\n    const scale = this._scal3Fix + .05;\n    if (scale > this.minScale && scale <= this._maxScale) {\n      this.setScale(scale);\n    } else {\n      this.setScale(this._maxScale);\n    }\n  }\n  /** Clean the img cropper */\n  clean() {\n    // fix choosing the same image does not load\n    if (this._currentInputElement) {\n      this._currentInputElement.value = '';\n      this._currentInputElement = null;\n    }\n    if (this.isLoaded) {\n      this._imgRect = {};\n      this.offset = undefined;\n      this.scale = undefined;\n      this._scal3Fix = undefined;\n      this._rotation = 0;\n      this._minScale = undefined;\n      this._isLoadedImg = false;\n      this.isLoaded = false;\n      this.isCropped = false;\n      this._currentLoadConfig = undefined;\n      this.config = this._configPrimary;\n      const canvas = this._imgCanvas.nativeElement;\n      canvas.width = 0;\n      canvas.height = 0;\n      this.cleaned.emit(null);\n      this.cd.markForCheck();\n    }\n  }\n  /** - */\n  zoomOut() {\n    const scale = this._scal3Fix - .05;\n    if (scale > this.minScale && scale <= this._maxScale) {\n      this.setScale(scale);\n    } else {\n      this.fit();\n    }\n  }\n  center() {\n    const newStyles = {\n      ...this._getCenterPoints()\n    };\n    this._setStylesForContImg(newStyles);\n    this._cropIfAutoCrop();\n  }\n  /**\n   * load an image from a given configuration,\n   * or from the result of a cropped image\n   */\n  loadImage(config, fn) {\n    this.clean();\n    const _config = this._currentLoadConfig = typeof config === 'string' ? {\n      originalDataURL: config\n    } : {\n      ...config\n    };\n    let src = _config.originalDataURL;\n    this._primaryAreaWidth = this._configPrimary.width;\n    this._primaryAreaHeight = this._configPrimary.height;\n    if (_config.areaWidth && _config.areaHeight) {\n      this.config.width = _config.areaWidth;\n      this.config.height = _config.areaHeight;\n    }\n    src = normalizeSVG(src);\n    const img = createHtmlImg(src);\n    const cropEvent = {\n      ..._config\n    };\n    new Observable(observer => {\n      img.onerror = err => observer.error(err);\n      img.onabort = err => observer.error(err);\n      img.onload = () => observer.next(null);\n    }).pipe(take(1), takeUntil(this._destroy)).subscribe(() => {\n      this._imgLoaded(img);\n      this._isLoadedImg = true;\n      this.imageLoaded.emit(cropEvent);\n      this.cd.markForCheck();\n      this._ngZone.runOutsideAngular(() => {\n        this._ngZone.onStable.asObservable().pipe(take(1), takeUntil(this._destroy)).subscribe(() => setTimeout(() => this._ngZone.run(() => this._positionImg(cropEvent, fn))));\n      });\n    }, () => {\n      const error = {\n        name: _config.name,\n        error: ImgCropperError.Type,\n        type: _config.type,\n        size: _config.size\n      };\n      this.error.emit(error);\n    });\n  }\n  _updateAreaIfNeeded() {\n    if (!this._config.responsiveArea) {\n      return;\n    }\n    const rootRect = this._rootRect();\n    const areaRect = this._areaCropperRect();\n    const minWidth = this.config.minWidth || 1;\n    const minHeight = this.config.minHeight || 1;\n    if (!(areaRect.width > rootRect.width || areaRect.height > rootRect.height || areaRect.width < this._primaryAreaWidth || areaRect.height < this._primaryAreaHeight)) {\n      return;\n    }\n    const areaWidthConf = Math.max(this.config.width, minWidth);\n    const areaWidthMax = Math.max(rootRect.width, minWidth);\n    const minHost = Math.min(Math.max(rootRect.width, minWidth), Math.max(rootRect.height, minHeight));\n    const currentScale = this._scal3Fix;\n    let newScale = 0;\n    const roundConf = this.config.round;\n    if (roundConf) {\n      this.config.width = this.config.height = minHost;\n    } else {\n      if (areaWidthConf === areaRect.width) {\n        if (areaWidthMax > this._primaryAreaWidth) {\n          this.config.width = this._primaryAreaWidth;\n          this.config.height = this._primaryAreaWidth * areaRect.height / areaRect.width;\n          newScale = currentScale * this._primaryAreaWidth / areaRect.width;\n        } else {\n          this.config.width = areaWidthMax;\n          this.config.height = areaWidthMax * areaRect.height / areaRect.width;\n          newScale = currentScale * areaWidthMax / areaRect.width;\n        }\n        this._updateMinScale();\n        this._updateMaxScale();\n        this.setScale(newScale, true);\n        this._markForCheck();\n      }\n    }\n  }\n  _updateAbsoluteScale() {\n    const scale = this._scal3Fix / (this.config.width / this._primaryAreaWidth);\n    this._absoluteScale = scale;\n  }\n  /**\n   * Load Image from URL\n   * @deprecated Use `loadImage` instead of `setImageUrl`\n   * @param src URL\n   * @param fn function that will be called before emit the event loaded\n   */\n  setImageUrl(src, fn) {\n    this.loadImage(src, fn);\n  }\n  _positionImg(cropEvent, fn) {\n    const loadConfig = this._currentLoadConfig;\n    this._updateMinScale(this._imgCanvas.nativeElement);\n    this._updateMaxScale();\n    this.isLoaded = false;\n    if (fn) {\n      fn();\n    } else {\n      if (loadConfig.scale) {\n        this.setScale(loadConfig.scale, true);\n      } else {\n        this.setScale(this.minScale, true);\n      }\n      this.rotate(loadConfig.rotation || 0);\n      this._updateAreaIfNeeded();\n      this._markForCheck();\n      this._ngZone.runOutsideAngular(() => {\n        this._ngZone.onStable.asObservable().pipe(take(1), takeUntil(this._destroy)).subscribe(() => {\n          if (loadConfig.xOrigin != null && loadConfig.yOrigin != null) {\n            this.updatePosition(loadConfig.xOrigin, loadConfig.yOrigin);\n          }\n          this._updateAreaIfNeeded();\n          this.isLoaded = true;\n          this._cropIfAutoCrop();\n          this._ngZone.run(() => {\n            this._markForCheck();\n            this.ready.emit(cropEvent);\n            // tslint:disable-next-line: deprecation\n            this.loaded.emit(cropEvent);\n          });\n        });\n      });\n    }\n  }\n  rotate(degrees) {\n    let validDegrees = _normalizeDegrees(degrees);\n    // If negative convert to positive\n    if (validDegrees < 0) {\n      validDegrees += 360;\n    }\n    const newRotation = _normalizeDegrees((this._rotation || 0) + validDegrees);\n    if (newRotation === this._rotation) {\n      return;\n    }\n    const degreesRad = validDegrees * Math.PI / 180;\n    const canvas = this._imgCanvas.nativeElement;\n    const canvasClon = createCanvasImg(canvas);\n    const ctx = canvas.getContext('2d');\n    this._rotation = newRotation;\n    // clear\n    ctx.clearRect(0, 0, canvasClon.width, canvasClon.height);\n    // rotate canvas image\n    const transform = `rotate(${validDegrees}deg) scale(${1 / this._scal3Fix})`;\n    const transformOrigin = `${this._imgRect.xc}px ${this._imgRect.yc}px 0`;\n    canvas.style.transform = transform;\n    // tslint:disable-next-line: deprecation\n    canvas.style.webkitTransform = transform;\n    canvas.style.transformOrigin = transformOrigin;\n    // tslint:disable-next-line: deprecation\n    canvas.style.webkitTransformOrigin = transformOrigin;\n    const {\n      left,\n      top\n    } = canvas.getBoundingClientRect();\n    // save rect\n    const canvasRect = canvas.getBoundingClientRect();\n    // remove rotate styles\n    canvas.removeAttribute('style');\n    // set w & h\n    const w = canvasRect.width;\n    const h = canvasRect.height;\n    ctx.canvas.width = w;\n    ctx.canvas.height = h;\n    // clear\n    ctx.clearRect(0, 0, w, h);\n    // translate and rotate\n    ctx.translate(w / 2, h / 2);\n    ctx.rotate(degreesRad);\n    ctx.drawImage(canvasClon, -canvasClon.width / 2, -canvasClon.height / 2);\n    // Update min scale\n    this._updateMinScale(canvas);\n    this._updateMaxScale();\n    // set the minimum scale, only if necessary\n    if (this.scale < this.minScale) {\n      this.setScale(0, true);\n    } //                ↑ no AutoCrop\n    const rootRect = this._rootRect();\n    this._setStylesForContImg({\n      x: left - rootRect.left,\n      y: top - rootRect.top\n    });\n    // keep image inside the frame\n    const originPosition = {\n      ...this._imgRect\n    };\n    this.offset = {\n      x: originPosition.x,\n      y: originPosition.y,\n      left: originPosition.xc,\n      top: originPosition.yc\n    };\n    this._setStylesForContImg({});\n    this._simulatePointerMove();\n    this._cropIfAutoCrop();\n  }\n  _updateMinScale(canvas) {\n    if (!canvas) {\n      canvas = this._imgCanvas.nativeElement;\n    }\n    const config = this.config;\n    const minScale = (config.extraZoomOut ? Math.min : Math.max)(config.width / canvas.width, config.height / canvas.height);\n    this._minScale = minScale;\n    this.minScaleChange.emit(minScale);\n  }\n  _updateMaxScale() {\n    const maxScale = this.config.width / this._primaryAreaWidth;\n    this._maxScale = maxScale;\n    this.maxScaleChange.emit(maxScale);\n  }\n  /**\n   * Resize & crop image\n   */\n  crop(config) {\n    const newConfig = config ? mergeDeep({}, this.config || new ImgCropperConfig(), config) : this.config;\n    const cropEvent = this._imgCrop(newConfig);\n    this.cd.markForCheck();\n    return cropEvent;\n  }\n  /**\n   * @docs-private\n   */\n  _imgCrop(myConfig) {\n    const canvasElement = document.createElement('canvas');\n    const areaRect = this._areaCropperRect();\n    const canvasRect = this._canvasRect();\n    const scaleFix = this._scal3Fix;\n    const left = (areaRect.left - canvasRect.left) / scaleFix;\n    const top = (areaRect.top - canvasRect.top) / scaleFix;\n    const {\n      output\n    } = myConfig;\n    const currentImageLoadConfig = this._currentLoadConfig;\n    const area = {\n      width: myConfig.width,\n      height: myConfig.height\n    };\n    canvasElement.width = area.width / scaleFix;\n    canvasElement.height = area.height / scaleFix;\n    const ctx = canvasElement.getContext('2d');\n    if (myConfig.fill) {\n      ctx.fillStyle = myConfig.fill;\n      ctx.fillRect(0, 0, canvasElement.width, canvasElement.height);\n    }\n    ctx.drawImage(this._imgCanvas.nativeElement, -left, -top);\n    const result = canvasElement;\n    if (myConfig.output === ImgResolution.Default) {\n      resizeCanvas(result, this._configPrimary.width, this._configPrimary.height);\n    } else if (typeof output === 'object') {\n      if (output.width && output.height) {\n        resizeCanvas(result, output.width, output.height);\n      } else if (output.width) {\n        const newHeight = area.height * output.width / area.width;\n        resizeCanvas(result, output.width, newHeight);\n      } else if (output.height) {\n        const newWidth = area.width * output.height / area.height;\n        resizeCanvas(result, newWidth, output.height);\n      }\n    }\n    const type = currentImageLoadConfig.originalDataURL.startsWith('http') ? currentImageLoadConfig.type || myConfig.type : myConfig.type || currentImageLoadConfig.type;\n    const dataURL = result.toDataURL(type);\n    const cropEvent = {\n      dataURL,\n      type,\n      name: currentImageLoadConfig.name,\n      areaWidth: this._primaryAreaWidth,\n      areaHeight: this._primaryAreaHeight,\n      width: result.width,\n      height: result.height,\n      originalDataURL: currentImageLoadConfig.originalDataURL,\n      scale: this._absoluteScale,\n      rotation: this._rotation,\n      left: (areaRect.left - canvasRect.left) / this._scal3Fix,\n      top: (areaRect.top - canvasRect.top) / this._scal3Fix,\n      size: currentImageLoadConfig.size,\n      xOrigin: this._imgRect.xc,\n      yOrigin: this._imgRect.yc,\n      position: {\n        x: this._imgRect.xc,\n        y: this._imgRect.yc\n      }\n    };\n    this.isCropped = true;\n    this.cropped.emit(cropEvent);\n    return cropEvent;\n  }\n  _rootRect() {\n    return this._elementRef.nativeElement.getBoundingClientRect();\n  }\n  _areaCropperRect() {\n    return this._areaRef.nativeElement.getBoundingClientRect();\n  }\n  _canvasRect() {\n    return this._imgCanvas.nativeElement.getBoundingClientRect();\n  }\n  _bindGlobalEvents(triggerEvent) {\n    const element = this._document;\n    const isTouch = isTouchEvent(triggerEvent);\n    const moveEventName = isTouch ? 'touchmove' : 'mousemove';\n    const endEventName = isTouch ? 'touchend' : 'mouseup';\n    element.addEventListener(moveEventName, this._pointerMove, activeEventOptions);\n    element.addEventListener(endEventName, this._pointerUp, activeEventOptions);\n    if (isTouch) {\n      element.addEventListener('touchcancel', this._pointerUp, activeEventOptions);\n    }\n    const window = this._getWindow();\n    if (typeof window !== 'undefined' && window) {\n      window.addEventListener('blur', this._windowBlur);\n    }\n  }\n  /** Removes any global event listeners that we may have added. */\n  _removeGlobalEvents() {\n    const element = this._document;\n    element.removeEventListener('mousemove', this._pointerMove, activeEventOptions);\n    element.removeEventListener('mouseup', this._pointerUp, activeEventOptions);\n    element.removeEventListener('touchmove', this._pointerMove, activeEventOptions);\n    element.removeEventListener('touchend', this._pointerUp, activeEventOptions);\n    element.removeEventListener('touchcancel', this._pointerUp, activeEventOptions);\n    const window = this._getWindow();\n    if (typeof window !== 'undefined' && window) {\n      window.removeEventListener('blur', this._windowBlur);\n    }\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n}\nLyImageCropper.и = 'LyImageCropper';\nLyImageCropper.ɵfac = function LyImageCropper_Factory(t) {\n  return new (t || LyImageCropper)(i0.ɵɵdirectiveInject(i1.StyleRenderer), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i2.ViewportRuler));\n};\nLyImageCropper.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: LyImageCropper,\n  selectors: [[\"ly-img-cropper\"], [\"ly-image-cropper\"]],\n  viewQuery: function LyImageCropper_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 5, ElementRef);\n      i0.ɵɵviewQuery(_c2, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._imgContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._areaRef = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._imgCanvas = _t.first);\n    }\n  },\n  inputs: {\n    config: \"config\",\n    scale: \"scale\",\n    maxFileSize: \"maxFileSize\"\n  },\n  outputs: {\n    scaleChange: \"scaleChange\",\n    minScaleChange: \"minScale\",\n    maxScaleChange: \"maxScale\",\n    loaded: \"loaded\",\n    imageLoaded: \"imageLoaded\",\n    ready: \"ready\",\n    cropped: \"cropped\",\n    cleaned: \"cleaned\",\n    error: \"error\"\n  },\n  features: [i0.ɵɵProvidersFeature([StyleRenderer])],\n  ngContentSelectors: _c4,\n  decls: 7,\n  vars: 3,\n  consts: [[3, \"className\", \"selectstart\"], [\"_imgContainer\", \"\"], [\"_imgCanvas\", \"\"], [3, \"round\", \"resizableArea\", \"keepAspectRatio\", \"ngStyle\", 4, \"ngIf\", \"ngIfElse\"], [\"content\", \"\"], [3, \"round\", \"resizableArea\", \"keepAspectRatio\", \"ngStyle\"], [\"_area\", \"\"], [3, \"className\"], [\"type\", \"file\", \"accept\", \"image/*\", 3, \"change\"], [\"_fileInput\", \"\"]],\n  template: function LyImageCropper_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"selectstart\", function LyImageCropper_Template_div_selectstart_0_listener($event) {\n        return $event.preventDefault();\n      });\n      i0.ɵɵelement(2, \"canvas\", null, 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, LyImageCropper_ly_cropper_area_4_Template, 2, 7, \"ly-cropper-area\", 3);\n      i0.ɵɵtemplate(5, LyImageCropper_ng_template_5_Template, 4, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r3 = i0.ɵɵreference(6);\n      i0.ɵɵproperty(\"className\", ctx.classes.imgContainer);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx._isLoadedImg)(\"ngIfElse\", _r3);\n    }\n  },\n  dependencies: function () {\n    return [LyCropperArea, i3.NgIf, i3.NgStyle];\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyImageCropper, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      selector: 'ly-img-cropper, ly-image-cropper',\n      providers: [StyleRenderer],\n      template: \"<!-- (selectstart): On Safari starting to slide temporarily triggers text selection mode which\\nshow the wrong cursor. We prevent it by stopping the `selectstart` event. -->\\n<div #_imgContainer\\n  [className]=\\\"classes.imgContainer\\\"\\n  (selectstart)=\\\"$event.preventDefault()\\\"\\n>\\n  <canvas #_imgCanvas></canvas>\\n</div>\\n<ly-cropper-area #_area\\n  [round]=\\\"!!config.round\\\"\\n  [resizableArea]=\\\"!!config.resizableArea\\\"\\n  [keepAspectRatio]=\\\"!!config.keepAspectRatio\\\"\\n  *ngIf=\\\"_isLoadedImg; else content\\\"\\n  [ngStyle]=\\\"{\\n    width: config.width + 'px',\\n    height: config.height + 'px'\\n  }\\\"></ly-cropper-area>\\n<ng-template #content>\\n  <div [className]=\\\"classes.defaultContent\\\">\\n    <input #_fileInput type=\\\"file\\\" (change)=\\\"selectInputEvent($event)\\\" accept=\\\"image/*\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i1.StyleRenderer\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i2.ViewportRuler\n    }];\n  }, {\n    _imgContainer: [{\n      type: ViewChild,\n      args: ['_imgContainer', {\n        static: true\n      }]\n    }],\n    _areaRef: [{\n      type: ViewChild,\n      args: ['_area', {\n        read: ElementRef\n      }]\n    }],\n    _imgCanvas: [{\n      type: ViewChild,\n      args: ['_imgCanvas', {\n        static: true\n      }]\n    }],\n    config: [{\n      type: Input\n    }],\n    scale: [{\n      type: Input\n    }],\n    maxFileSize: [{\n      type: Input\n    }],\n    scaleChange: [{\n      type: Output\n    }],\n    minScaleChange: [{\n      type: Output,\n      args: ['minScale']\n    }],\n    maxScaleChange: [{\n      type: Output,\n      args: ['maxScale']\n    }],\n    loaded: [{\n      type: Output\n    }],\n    imageLoaded: [{\n      type: Output\n    }],\n    ready: [{\n      type: Output\n    }],\n    cropped: [{\n      type: Output\n    }],\n    cleaned: [{\n      type: Output\n    }],\n    error: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @dynamic\n */\nclass LyCropperArea {\n  constructor(sRenderer, _elementRef, _ngZone, _cropper, _document) {\n    this.sRenderer = sRenderer;\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._cropper = _cropper;\n    this.classes = this.sRenderer.renderSheet(STYLES, 'area');\n    this._pointerDown = event => {\n      // Don't do anything if the\n      // user is using anything other than the main mouse button.\n      if (this._isSliding || !isTouchEvent(event) && event.button !== 0) {\n        return;\n      }\n      event.preventDefault();\n      this._ngZone.run(() => {\n        this._isSliding = true;\n        this._lastPointerEvent = event;\n        this._startPointerEvent = getGesturePointFromEvent(event);\n        this._startAreaRect = this._cropper._areaCropperRect();\n        this._startImgRect = this._cropper._canvasRect();\n        event.preventDefault();\n        this._bindGlobalEvents(event);\n      });\n    };\n    this._pointerMove = event => {\n      if (this._isSliding) {\n        event.preventDefault();\n        this._lastPointerEvent = event;\n        const element = this._elementRef.nativeElement;\n        const {\n          width,\n          height,\n          minWidth,\n          minHeight\n        } = this._cropper.config;\n        const point = getGesturePointFromEvent(event);\n        const deltaX = point.x - this._startPointerEvent.x;\n        const deltaY = point.y - this._startPointerEvent.y;\n        const startAreaRect = this._startAreaRect;\n        const startImgRect = this._startImgRect;\n        const round = this.round;\n        const keepAspectRatio = this._cropper.config.keepAspectRatio || event.shiftKey;\n        let newWidth = 0;\n        let newHeight = 0;\n        const rootRect = this._cropper._rootRect();\n        if (round) {\n          // The distance from the center of the cropper area to the pointer\n          const originX = width / 2 / Math.sqrt(2) + deltaX;\n          const originY = height / 2 / Math.sqrt(2) + deltaY;\n          // Leg\n          const side = Math.sqrt(originX ** 2 + originY ** 2);\n          newWidth = newHeight = side * 2;\n        } else if (keepAspectRatio) {\n          newWidth = width + deltaX * 2;\n          newHeight = height + deltaY * 2;\n          if (width !== height) {\n            if (width > height) {\n              newHeight = height / (width / newWidth);\n            } else if (height > width) {\n              newWidth = width / (height / newHeight);\n            }\n          } else {\n            newWidth = newHeight = Math.max(newWidth, newHeight);\n          }\n        } else {\n          newWidth = width + deltaX * 2;\n          newHeight = height + deltaY * 2;\n        }\n        // To min width\n        if (newWidth < minWidth) {\n          newWidth = minWidth;\n        }\n        // To min height\n        if (newHeight < minHeight) {\n          newHeight = minHeight;\n        }\n        // Do not overflow the cropper area\n        const centerX = startAreaRect.x + startAreaRect.width / 2;\n        const centerY = startAreaRect.y + startAreaRect.height / 2;\n        const topOverflow = startImgRect.y > centerY - newHeight / 2;\n        const bottomOverflow = centerY + newHeight / 2 > startImgRect.bottom;\n        const minHeightOnOverflow = Math.min((centerY - startImgRect.y) * 2, (startImgRect.bottom - centerY) * 2);\n        const leftOverflow = startImgRect.x > centerX - newWidth / 2;\n        const rightOverflow = centerX + newWidth / 2 > startImgRect.right;\n        const minWidthOnOverflow = Math.min((centerX - startImgRect.x) * 2, (startImgRect.right - centerX) * 2);\n        const minOnOverflow = Math.min(minWidthOnOverflow, minHeightOnOverflow);\n        if (round) {\n          if (topOverflow || bottomOverflow || leftOverflow || rightOverflow) {\n            newHeight = newWidth = minOnOverflow;\n          }\n        } else if (keepAspectRatio) {\n          const newNewWidth = [];\n          const newNewHeight = [];\n          if (topOverflow || bottomOverflow) {\n            newHeight = minHeightOnOverflow;\n            newNewHeight.push(newHeight);\n            newWidth = width / (height / minHeightOnOverflow);\n            newNewWidth.push(newWidth);\n          }\n          if (leftOverflow || rightOverflow) {\n            newWidth = minWidthOnOverflow;\n            newNewWidth.push(newWidth);\n            newHeight = height / (width / minWidthOnOverflow);\n            newNewHeight.push(newHeight);\n          }\n          if (newNewWidth.length === 2) {\n            newWidth = Math.min(...newNewWidth);\n          }\n          if (newNewHeight.length === 2) {\n            newHeight = Math.min(...newNewHeight);\n          }\n        } else {\n          if (topOverflow || bottomOverflow) {\n            newHeight = minHeightOnOverflow;\n          }\n          if (leftOverflow || rightOverflow) {\n            newWidth = minWidthOnOverflow;\n          }\n        }\n        // Do not overflow the container\n        if (round) {\n          const min = Math.min(rootRect.width, rootRect.height);\n          if (newWidth > min) {\n            newWidth = newHeight = min;\n          } else if (newHeight > min) {\n            newWidth = newHeight = min;\n          }\n        } else if (keepAspectRatio) {\n          if (newWidth > rootRect.width) {\n            newWidth = rootRect.width;\n            newHeight = height / (width / rootRect.width);\n          }\n          if (newHeight > rootRect.height) {\n            newWidth = width / (height / rootRect.height);\n            newHeight = rootRect.height;\n          }\n        } else {\n          if (newWidth > rootRect.width) {\n            newWidth = rootRect.width;\n          }\n          if (newHeight > rootRect.height) {\n            newHeight = rootRect.height;\n          }\n        }\n        // round values\n        newWidth = Math.round(newWidth);\n        newHeight = Math.round(newHeight);\n        element.style.width = `${newWidth}px`;\n        element.style.height = `${newHeight}px`;\n        this._currentWidth = newWidth;\n        this._currentHeight = newHeight;\n      }\n    };\n    /** Called when the user has lifted their pointer. */\n    this._pointerUp = event => {\n      if (this._isSliding) {\n        event.preventDefault();\n        this._removeGlobalEvents();\n        this._cropper._primaryAreaWidth = this._cropper.config.width = this._currentWidth;\n        this._cropper._primaryAreaHeight = this._cropper.config.height = this._currentHeight;\n        this._cropper.config = this._cropper.config;\n        this._cropper._updateMinScale();\n        this._isSliding = false;\n        this._startPointerEvent = null;\n      }\n    };\n    /** Called when the window has lost focus. */\n    this._windowBlur = () => {\n      // If the window is blurred while dragging we need to stop dragging because the\n      // browser won't dispatch the `mouseup` and `touchend` events anymore.\n      if (this._lastPointerEvent) {\n        this._pointerUp(this._lastPointerEvent);\n      }\n    };\n    this._document = _document;\n  }\n  set resizableArea(val) {\n    if (val !== this._resizableArea) {\n      this._resizableArea = val;\n      Promise.resolve(null).then(() => {\n        if (val) {\n          this._removeResizableArea();\n          this._addResizableArea();\n        } else {\n          this._removeResizableArea();\n        }\n      });\n    }\n  }\n  get resizableArea() {\n    return this._resizableArea;\n  }\n  ngOnDestroy() {\n    this._removeResizableArea();\n  }\n  _addResizableArea() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._resizer.nativeElement;\n      element.addEventListener('mousedown', this._pointerDown, activeEventOptions);\n      element.addEventListener('touchstart', this._pointerDown, activeEventOptions);\n    });\n  }\n  _removeResizableArea() {\n    const element = this._resizer?.nativeElement;\n    if (element) {\n      this._lastPointerEvent = null;\n      this._removeGlobalEvents();\n      element.removeEventListener('mousedown', this._pointerDown, activeEventOptions);\n      element.removeEventListener('touchstart', this._pointerDown, activeEventOptions);\n    }\n  }\n  _bindGlobalEvents(triggerEvent) {\n    const element = this._document;\n    const isTouch = isTouchEvent(triggerEvent);\n    const moveEventName = isTouch ? 'touchmove' : 'mousemove';\n    const endEventName = isTouch ? 'touchend' : 'mouseup';\n    element.addEventListener(moveEventName, this._pointerMove, activeEventOptions);\n    element.addEventListener(endEventName, this._pointerUp, activeEventOptions);\n    if (isTouch) {\n      element.addEventListener('touchcancel', this._pointerUp, activeEventOptions);\n    }\n    const window = this._getWindow();\n    if (typeof window !== 'undefined' && window) {\n      window.addEventListener('blur', this._windowBlur);\n    }\n  }\n  /** Removes any global event listeners that we may have added. */\n  _removeGlobalEvents() {\n    const element = this._document;\n    element.removeEventListener('mousemove', this._pointerMove, activeEventOptions);\n    element.removeEventListener('mouseup', this._pointerUp, activeEventOptions);\n    element.removeEventListener('touchmove', this._pointerMove, activeEventOptions);\n    element.removeEventListener('touchend', this._pointerUp, activeEventOptions);\n    element.removeEventListener('touchcancel', this._pointerUp, activeEventOptions);\n    const window = this._getWindow();\n    if (typeof window !== 'undefined' && window) {\n      window.removeEventListener('blur', this._windowBlur);\n    }\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n}\nLyCropperArea.ɵfac = function LyCropperArea_Factory(t) {\n  return new (t || LyCropperArea)(i0.ɵɵdirectiveInject(i1.StyleRenderer), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(LyImageCropper), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nLyCropperArea.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: LyCropperArea,\n  selectors: [[\"ly-cropper-area\"]],\n  viewQuery: function LyCropperArea_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c5, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._resizer = _t.first);\n    }\n  },\n  inputs: {\n    resizableArea: \"resizableArea\",\n    keepAspectRatio: \"keepAspectRatio\",\n    round: \"round\"\n  },\n  exportAs: [\"lyCropperArea\"],\n  features: [i0.ɵɵProvidersFeature([StyleRenderer])],\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"class\", 4, \"ngIf\"], [\"resizer\", \"\"]],\n  template: function LyCropperArea_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, LyCropperArea_div_0_Template, 2, 2, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.resizableArea);\n    }\n  },\n  dependencies: [i3.NgIf],\n  encapsulation: 2,\n  changeDetection: 0\n});\n__decorate([Style2((_value, _media) => ({\n  after\n}, ref) => {\n  ref.renderStyleSheet(STYLES);\n  const __ = ref.selectorsOf(STYLES);\n  return _className => `${_className}{border-radius:50%;}${_className} .${__.resizer}{${after}:${pos}%;bottom:${pos}%;transform:translate(4px,4px);}`;\n}, coerceBooleanProperty)], LyCropperArea.prototype, \"round\", void 0);\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyCropperArea, [{\n    type: Component,\n    args: [{\n      selector: 'ly-cropper-area',\n      providers: [StyleRenderer],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      exportAs: 'lyCropperArea',\n      template: \"<div #resizer\\n  *ngIf=\\\"resizableArea\\\"\\n  [class]=\\\"classes.resizer\\\"\\n></div>\"\n    }]\n  }], function () {\n    return [{\n      type: i1.StyleRenderer\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: LyImageCropper\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    _resizer: [{\n      type: ViewChild,\n      args: ['resizer']\n    }],\n    resizableArea: [{\n      type: Input\n    }],\n    keepAspectRatio: [{\n      type: Input\n    }],\n    round: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Normalize degrees for cropper rotation\n * @docs-private\n */\nfunction _normalizeDegrees(n) {\n  const de = n % 360;\n  if (de % 90) {\n    throw new Error(`LyCropper: Invalid \\`${n}\\` degree, only accepted values: 0, 90, 180, 270 & 360.`);\n  }\n  return de;\n}\n/**\n * @docs-private\n */\nfunction createCanvasImg(img) {\n  // create a new canvas\n  const newCanvas = document.createElement('canvas');\n  const context = newCanvas.getContext('2d');\n  // set dimensions\n  newCanvas.width = img.width;\n  newCanvas.height = img.height;\n  // apply the old canvas to the new one\n  context.drawImage(img, 0, 0);\n  // return the new canvas\n  return newCanvas;\n}\nfunction normalizeSVG(dataURL) {\n  if (window.atob && isSvgImage(dataURL)) {\n    const len = dataURL.length / 5;\n    const text = window.atob(dataURL.replace(DATA_IMAGE_SVG_PREFIX, ''));\n    const span = document.createElement('span');\n    span.innerHTML = text;\n    const svg = span.querySelector('svg');\n    span.setAttribute('style', 'display:none');\n    document.body.appendChild(span);\n    const width = parseFloat(getComputedStyle(svg).width) || 1;\n    const height = parseFloat(getComputedStyle(svg).height) || 1;\n    const max = Math.max(width, height);\n    svg.setAttribute('width', `${len / (width / max)}px`);\n    svg.setAttribute('height', `${len / (height / max)}px`);\n    const result = DATA_IMAGE_SVG_PREFIX + window.btoa(span.innerHTML);\n    document.body.removeChild(span);\n    return result;\n  }\n  return dataURL;\n}\nfunction isSvgImage(dataUrl) {\n  return dataUrl.startsWith(DATA_IMAGE_SVG_PREFIX);\n}\nfunction createHtmlImg(src) {\n  const img = new Image();\n  img.crossOrigin = 'anonymous';\n  img.src = src;\n  return img;\n}\nfunction getGesturePointFromEvent(event) {\n  // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n  const point = isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;\n  return {\n    x: point.clientX,\n    y: point.clientY\n  };\n}\n/** Returns whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  return event.type[0] === 't';\n}\nfunction roundNumber(n) {\n  return Math.round(n);\n}\nclass LyImageCropperModule {}\nLyImageCropperModule.ɵfac = function LyImageCropperModule_Factory(t) {\n  return new (t || LyImageCropperModule)();\n};\nLyImageCropperModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LyImageCropperModule\n});\nLyImageCropperModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyImageCropperModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [LyImageCropper],\n      declarations: [LyImageCropper, LyCropperArea]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ImgCropperConfig, ImgCropperError, ImgResolution, LyCropperArea, LyImageCropper, LyImageCropperModule, STYLES, _normalizeDegrees, roundNumber };", "map": {"version": 3, "names": ["__decorate", "i0", "EventEmitter", "ElementRef", "Component", "ChangeDetectionStrategy", "Inject", "ViewChild", "Input", "Output", "NgModule", "i1", "st2c", "StyleCollection", "LY_COMMON_STYLES", "mergeDeep", "<PERSON><PERSON><PERSON><PERSON>", "Style2", "Subject", "Observable", "takeUntil", "take", "normalizePassiveListenerOptions", "i3", "DOCUMENT", "CommonModule", "coerceBooleanProperty", "i2", "_c0", "_c1", "_c2", "_c3", "a0", "a1", "width", "height", "LyImageCropper_ly_cropper_area_4_Template", "rf", "ctx", "ɵɵelement", "ctx_r2", "ɵɵnextContext", "ɵɵproperty", "config", "round", "resizableArea", "keepAspectRatio", "ɵɵpureFunction2", "LyImageCropper_ng_template_5_Template", "_r8", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "LyImageCropper_ng_template_5_Template_input_change_1_listener", "$event", "ɵɵrestoreView", "ctx_r7", "ɵɵresetView", "selectInputEvent", "ɵɵelementEnd", "ɵɵprojection", "ctx_r4", "classes", "defaultContent", "_c4", "_c5", "LyCropperArea_div_0_Template", "ctx_r0", "ɵɵclassMap", "resizer", "resizeCanvas", "canvas", "width_source", "height_source", "Math", "ratio_w", "ratio_h", "ratio_w_half", "ceil", "ratio_h_half", "getContext", "img", "getImageData", "img2", "createImageData", "data", "data2", "j", "i", "x2", "weight", "weights", "weights_alpha", "gx_r", "gx_g", "gx_b", "gx_a", "center_y", "xx_start", "floor", "xx_stop", "yy_start", "yy_stop", "min", "yy", "dy", "abs", "center_x", "w0", "xx", "dx", "w", "sqrt", "pos_x", "putImageData", "activeEventOptions", "passive", "STYLE_PRIORITY", "DATA_IMAGE_SVG_PREFIX", "pos", "STYLES", "theme", "ref", "cropper", "selectorsOf", "after", "$name", "LyImageCropper", "и", "$priority", "root", "_className", "setTransformer", "fn", "imgContainer", "overlay", "fill", "area", "ImgCropperConfig", "constructor", "min<PERSON><PERSON><PERSON>", "minHeight", "antiAliased", "output", "ImgResolution", "<PERSON><PERSON><PERSON>", "ImgCropperError", "s<PERSON><PERSON><PERSON>", "_renderer", "_elementRef", "cd", "_ngZone", "_document", "viewPortRuler", "renderSheet", "_imgRect", "_rotation", "scaleChange", "minScaleChange", "maxScaleChange", "loaded", "imageLoaded", "ready", "cropped", "cleaned", "error", "_destroy", "_pointerDown", "event", "_isSliding", "isTouchEvent", "button", "run", "offset", "x", "y", "left", "xc", "top", "yc", "_lastPointerEvent", "_startPointerEvent", "getGesturePointFromEvent", "preventDefault", "_bindGlobalEvents", "_pointerMove", "_imgCanvas", "nativeElement", "scaleFix", "_scal3Fix", "startP", "point", "deltaX", "deltaY", "isMinScaleY", "extraZoomOut", "isMinScaleX", "limitLeft", "limitRight", "limitTop", "limitBottom", "_setStylesForContImg", "_pointerUp", "_removeGlobalEvents", "_cropIfAutoCrop", "_windowBlur", "change", "pipe", "subscribe", "updateCropperPosition", "_config", "val", "_configPrimary", "_primary<PERSON><PERSON><PERSON><PERSON><PERSON>", "_primaryArea<PERSON><PERSON>ght", "Error", "maxFileSize", "scale", "_scale", "setScale", "minScale", "_minScale", "ngOnInit", "runOutsideAngular", "element", "_imgContainer", "addEventListener", "ngOnDestroy", "next", "complete", "removeEventListener", "_imgLoaded", "imgElement", "_img", "clearRect", "drawImage", "_updateMinScale", "_updateMaxScale", "values", "newStyles", "rootRect", "_rootRect", "transform", "transform<PERSON><PERSON>in", "key", "hasOwnProperty", "setStyle", "isLoaded", "updatePosition", "_updateAreaIfNeeded", "_currentInputElement", "target", "files", "length", "fileSize", "size", "fileName", "value", "replace", "cropEvent", "name", "type", "Size", "clean", "emit", "observer", "reader", "FileReader", "onerror", "err", "<PERSON>ab<PERSON>", "onload", "ev", "setTimeout", "readAsDataURL", "loadEvent", "originalDataURL", "result", "loadImage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Other", "errorMsg", "noAutoCrop", "newSize", "changed", "_updateAbsoluteScale", "originPosition", "_simulatePointerMove", "_getCenterPoints", "offsetWidth", "offsetHeight", "fitToScreen", "container", "max", "fit", "clientX", "clientY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "xOrigin", "y<PERSON><PERSON><PERSON>", "hostRect", "areaRect", "_areaCropperRect", "areaWidth", "areaHeight", "_slideEnd", "autoCrop", "crop", "zoomIn", "_maxScale", "undefined", "_isLoadedImg", "isCropped", "_currentLoadConfig", "zoomOut", "center", "src", "normalizeSVG", "createHtmlImg", "onStable", "asObservable", "_positionImg", "Type", "responsiveArea", "areaWidthConf", "areaWidthMax", "minHost", "currentScale", "newScale", "roundConf", "_absoluteScale", "setImageUrl", "loadConfig", "rotate", "rotation", "degrees", "validDegrees", "_normalizeDegrees", "newRotation", "degreesRad", "PI", "canvasClon", "createCanvasImg", "style", "webkitTransform", "webkitTransformOrigin", "getBoundingClientRect", "canvasRect", "removeAttribute", "h", "translate", "maxScale", "newConfig", "_imgCrop", "myConfig", "canvasElement", "document", "createElement", "_canvasRect", "currentImageLoadConfig", "fillStyle", "fillRect", "newHeight", "newWidth", "startsWith", "dataURL", "toDataURL", "position", "_areaRef", "triggerEvent", "is<PERSON><PERSON>ch", "moveEventName", "endEventName", "window", "_getWindow", "defaultView", "ɵfac", "LyImageCropper_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "ChangeDetectorRef", "NgZone", "ViewportRuler", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "LyImageCropper_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "LyImageCropper_Template", "ɵɵprojectionDef", "LyImageCropper_Template_div_selectstart_0_listener", "ɵɵtemplate", "ɵɵtemplateRefExtractor", "_r3", "ɵɵreference", "ɵɵadvance", "dependencies", "LyCropperArea", "NgIf", "NgStyle", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "preserveWhitespaces", "selector", "providers", "decorators", "static", "read", "_cropper", "_startAreaRect", "_startImgRect", "startAreaRect", "startImgRect", "shift<PERSON>ey", "originX", "originY", "side", "centerX", "centerY", "topOverflow", "bottomOverflow", "bottom", "minHeightOnOverflow", "leftOverflow", "rightOverflow", "right", "minWidthOnOverflow", "minOnOverflow", "newNewWidth", "newNewHeight", "push", "_currentWidth", "_currentHeight", "_resizableArea", "Promise", "resolve", "then", "_removeResizableArea", "_addResizableArea", "_resizer", "LyCropperArea_Factory", "LyCropperArea_Query", "exportAs", "LyCropperArea_Template", "_value", "_media", "renderStyleSheet", "__", "prototype", "n", "de", "newCanvas", "context", "atob", "isSvgImage", "len", "text", "span", "innerHTML", "svg", "querySelector", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "parseFloat", "getComputedStyle", "btoa", "<PERSON><PERSON><PERSON><PERSON>", "dataUrl", "Image", "crossOrigin", "touches", "changedTouches", "roundNumber", "LyImageCropperModule", "LyImageCropperModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@alyle/ui/fesm2020/alyle-ui-image-cropper.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, ElementRef, Component, ChangeDetectionStrategy, Inject, ViewChild, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@alyle/ui';\nimport { st2c, StyleCollection, LY_COMMON_STYLES, mergeDeep, StyleRenderer, Style2 } from '@alyle/ui';\nimport { Subject, Observable } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2 from '@angular/cdk/scrolling';\n\n/*\n * Hermite resize - fast image resize/resample using Hermite filter.\n * https://github.com/viliusle/Hermite-resize\n */\nfunction resizeCanvas(canvas, width, height) {\n    const width_source = canvas.width;\n    const height_source = canvas.height;\n    width = Math.round(width);\n    height = Math.round(height);\n    const ratio_w = width_source / width;\n    const ratio_h = height_source / height;\n    const ratio_w_half = Math.ceil(ratio_w / 2);\n    const ratio_h_half = Math.ceil(ratio_h / 2);\n    const ctx = canvas.getContext('2d');\n    const img = ctx.getImageData(0, 0, width_source, height_source);\n    const img2 = ctx.createImageData(width, height);\n    const data = img.data;\n    const data2 = img2.data;\n    for (let j = 0; j < height; j++) {\n        for (let i = 0; i < width; i++) {\n            const x2 = (i + j * width) * 4;\n            let weight = 0;\n            let weights = 0;\n            let weights_alpha = 0;\n            let gx_r = 0;\n            let gx_g = 0;\n            let gx_b = 0;\n            let gx_a = 0;\n            const center_y = j * ratio_h;\n            const xx_start = Math.floor(i * ratio_w);\n            let xx_stop = Math.ceil((i + 1) * ratio_w);\n            const yy_start = Math.floor(j * ratio_h);\n            let yy_stop = Math.ceil((j + 1) * ratio_h);\n            xx_stop = Math.min(xx_stop, width_source);\n            yy_stop = Math.min(yy_stop, height_source);\n            for (let yy = yy_start; yy < yy_stop; yy++) {\n                const dy = Math.abs(center_y - yy) / ratio_h_half;\n                const center_x = i * ratio_w;\n                const w0 = dy * dy; // pre-calc part of w\n                for (let xx = xx_start; xx < xx_stop; xx++) {\n                    const dx = Math.abs(center_x - xx) / ratio_w_half;\n                    const w = Math.sqrt(w0 + dx * dx);\n                    if (w >= 1) {\n                        // pixel too far\n                        continue;\n                    }\n                    // hermite filter\n                    weight = 2 * w * w * w - 3 * w * w + 1;\n                    const pos_x = 4 * (xx + yy * width_source);\n                    // alpha\n                    gx_a += weight * data[pos_x + 3];\n                    weights_alpha += weight;\n                    // colors\n                    if (data[pos_x + 3] < 255) {\n                        weight = weight * data[pos_x + 3] / 250;\n                    }\n                    gx_r += weight * data[pos_x];\n                    gx_g += weight * data[pos_x + 1];\n                    gx_b += weight * data[pos_x + 2];\n                    weights += weight;\n                }\n            }\n            data2[x2] = gx_r / weights;\n            data2[x2 + 1] = gx_g / weights;\n            data2[x2 + 2] = gx_b / weights;\n            data2[x2 + 3] = gx_a / weights_alpha;\n        }\n    }\n    // clear and resize canvas\n    canvas.width = width;\n    canvas.height = height;\n    // draw\n    ctx.putImageData(img2, 0, 0);\n    return ctx;\n}\n\nconst activeEventOptions = normalizePassiveListenerOptions({ passive: false });\nconst STYLE_PRIORITY = -2;\nconst DATA_IMAGE_SVG_PREFIX = 'data:image/svg+xml;base64,';\nconst pos = (100 * Math.sqrt(2) - 100) / 2 / Math.sqrt(2);\nconst STYLES = (theme, ref) => {\n    const cropper = ref.selectorsOf(STYLES);\n    const { after } = theme;\n    return {\n        $name: LyImageCropper.и,\n        $priority: STYLE_PRIORITY,\n        root: () => (_className) => `${_className}{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;overflow:hidden;position:relative;justify-content:center;align-items:center;}${st2c(((theme.cropper\n            && theme.cropper.root\n            && (theme.cropper.root instanceof StyleCollection\n                ? theme.cropper.root.setTransformer(fn => fn(cropper))\n                : theme.cropper.root(cropper)))), `${_className}`)}`,\n        imgContainer: (_className) => `${_className}{cursor:move;position:absolute;top:0;left:0;display:flex;touch-action:none;}${_className} > canvas{display:block;}`,\n        overlay: (_className) => `${st2c((LY_COMMON_STYLES.fill), `${_className}`)}`,\n        area: (_className) => `${_className}{pointer-events:none;box-shadow:0 0 0 20000px rgba(0,0,0,0.4);}${st2c((LY_COMMON_STYLES.fill), `${_className}`)}${_className}{margin:auto;}${st2c((LY_COMMON_STYLES.fill), `${_className}:before,${_className}:after`)}${_className}:before,${_className}:after{content:'';}${_className}:before{width:0;height:0;margin:auto;border-radius:50%;background:#fff;border:solid 2px rgb(255,255,255);}${_className}:after{border:solid 2px rgb(255,255,255);border-radius:inherit;}`,\n        resizer: (_className) => `${_className}{width:10px;height:10px;background:#fff;border-radius:3px;position:absolute;touch-action:none;bottom:0;${after}:0;pointer-events:all;cursor:${after === 'right'\n            ? 'nwse-resize'\n            : 'nesw-resize'};}${st2c((LY_COMMON_STYLES.fill), `${_className}:before`)}${_className}:before{content:'';width:20px;height:20px;transform:translate(-25%,-25%);}`,\n        defaultContent: (_className) => `${_className}{display:flex;align-items:center;justify-content:center;}${st2c((LY_COMMON_STYLES.fill), `${_className},${_className} > input`)}${_className} *:not(input){pointer-events:none;}${_className} > input{background:transparent;opacity:0;width:100%;height:100%;}`\n    };\n};\n/** Image Cropper Config */\nclass ImgCropperConfig {\n    constructor() {\n        /** Cropper area width */\n        this.width = 250;\n        /** Cropper area height */\n        this.height = 200;\n        this.minWidth = 40;\n        this.minHeight = 40;\n        /**\n         * Set anti-aliased (default: true)\n         * @deprecated this is not necessary as the cropper will automatically resize the image\n         * to the best quality\n         */\n        this.antiAliased = true;\n        this.output = ImgResolution.Default;\n    }\n}\n/** Image output */\nvar ImgResolution;\n(function (ImgResolution) {\n    /**\n     * The output image will be equal to the initial size of the cropper area.\n     */\n    ImgResolution[ImgResolution[\"Default\"] = 0] = \"Default\";\n    /** Just crop the image without resizing */\n    ImgResolution[ImgResolution[\"OriginalImage\"] = 1] = \"OriginalImage\";\n})(ImgResolution || (ImgResolution = {}));\n/** Image output */\nvar ImgCropperError;\n(function (ImgCropperError) {\n    /** The loaded image exceeds the size limit set. */\n    ImgCropperError[ImgCropperError[\"Size\"] = 0] = \"Size\";\n    /** The file loaded is not image. */\n    ImgCropperError[ImgCropperError[\"Type\"] = 1] = \"Type\";\n    /** When the image has not been loaded. */\n    ImgCropperError[ImgCropperError[\"Other\"] = 2] = \"Other\";\n})(ImgCropperError || (ImgCropperError = {}));\nclass LyImageCropper {\n    constructor(sRenderer, _renderer, _elementRef, cd, _ngZone, _document, viewPortRuler) {\n        this.sRenderer = sRenderer;\n        this._renderer = _renderer;\n        this._elementRef = _elementRef;\n        this.cd = cd;\n        this._ngZone = _ngZone;\n        /**\n         * styles\n         * @docs-private\n         */\n        this.classes = this.sRenderer.renderSheet(STYLES, true);\n        this._imgRect = {};\n        this._rotation = 0;\n        this.scaleChange = new EventEmitter();\n        /** Emits minimum supported image scale */\n        this.minScaleChange = new EventEmitter();\n        /** Emits maximum supported image scale */\n        this.maxScaleChange = new EventEmitter();\n        /** @deprecated Emits when the image is loaded, instead use `ready` */\n        this.loaded = new EventEmitter();\n        /** Emits when the image is loaded */\n        this.imageLoaded = new EventEmitter();\n        /** Emits when the cropper is ready to be interacted */\n        this.ready = new EventEmitter();\n        /** On crop new image */\n        this.cropped = new EventEmitter();\n        /** Emits when the cropper is cleaned */\n        this.cleaned = new EventEmitter();\n        /** Emit an error when the loaded image is not valid */\n        // tslint:disable-next-line: no-output-native\n        this.error = new EventEmitter();\n        /** Emits whenever the component is destroyed. */\n        this._destroy = new Subject();\n        this._pointerDown = (event) => {\n            // Don't do anything if the\n            // user is using anything other than the main mouse button.\n            if (this._isSliding || (!isTouchEvent(event) && event.button !== 0)) {\n                return;\n            }\n            this._ngZone.run(() => {\n                this._isSliding = true;\n                this.offset = {\n                    x: this._imgRect.x,\n                    y: this._imgRect.y,\n                    left: this._imgRect.xc,\n                    top: this._imgRect.yc\n                };\n                this._lastPointerEvent = event;\n                this._startPointerEvent = getGesturePointFromEvent(event);\n                event.preventDefault();\n                this._bindGlobalEvents(event);\n            });\n        };\n        /**\n         * Called when the user has moved their pointer after\n         * starting to drag.\n         */\n        this._pointerMove = (event) => {\n            if (this._isSliding) {\n                event.preventDefault();\n                this._lastPointerEvent = event;\n                let x, y;\n                const canvas = this._imgCanvas.nativeElement;\n                const scaleFix = this._scal3Fix;\n                const config = this.config;\n                const startP = this.offset;\n                const point = getGesturePointFromEvent(event);\n                const deltaX = point.x - this._startPointerEvent.x;\n                const deltaY = point.y - this._startPointerEvent.y;\n                if (!scaleFix || !startP) {\n                    return;\n                }\n                const isMinScaleY = canvas.height * scaleFix < config.height && config.extraZoomOut;\n                const isMinScaleX = canvas.width * scaleFix < config.width && config.extraZoomOut;\n                const limitLeft = (config.width / 2 / scaleFix) >= startP.left - (deltaX / scaleFix);\n                const limitRight = (config.width / 2 / scaleFix) + (canvas.width) - (startP.left - (deltaX / scaleFix)) <= config.width / scaleFix;\n                const limitTop = ((config.height / 2 / scaleFix) >= (startP.top - (deltaY / scaleFix)));\n                const limitBottom = (((config.height / 2 / scaleFix) + (canvas.height) - (startP.top - (deltaY / scaleFix))) <= (config.height / scaleFix));\n                // Limit for left\n                if ((limitLeft && !isMinScaleX) || (!limitLeft && isMinScaleX)) {\n                    x = startP.x + (startP.left) - (config.width / 2 / scaleFix);\n                }\n                // Limit for right\n                if ((limitRight && !isMinScaleX) || (!limitRight && isMinScaleX)) {\n                    x = startP.x + (startP.left) + (config.width / 2 / scaleFix) - canvas.width;\n                }\n                // Limit for top\n                if ((limitTop && !isMinScaleY) || (!limitTop && isMinScaleY)) {\n                    y = startP.y + (startP.top) - (config.height / 2 / scaleFix);\n                }\n                // Limit for bottom\n                if ((limitBottom && !isMinScaleY) || (!limitBottom && isMinScaleY)) {\n                    y = startP.y + (startP.top) + (config.height / 2 / scaleFix) - canvas.height;\n                }\n                // When press shiftKey, deprecated\n                // if (event.srcEvent && event.srcEvent.shiftKey) {\n                //   if (Math.abs(event.deltaX) === Math.max(Math.abs(event.deltaX), Math.abs(event.deltaY))) {\n                //     y = this.offset.top;\n                //   } else {\n                //     x = this.offset.left;\n                //   }\n                // }\n                if (x === void 0) {\n                    x = (deltaX / scaleFix) + (startP.x);\n                }\n                if (y === void 0) {\n                    y = (deltaY / scaleFix) + (startP.y);\n                }\n                this._setStylesForContImg({\n                    x, y\n                });\n            }\n        };\n        /** Called when the user has lifted their pointer. */\n        this._pointerUp = (event) => {\n            if (this._isSliding) {\n                event.preventDefault();\n                this._removeGlobalEvents();\n                this._isSliding = false;\n                this._startPointerEvent = null;\n                this._cropIfAutoCrop();\n            }\n        };\n        /** Called when the window has lost focus. */\n        this._windowBlur = () => {\n            // If the window is blurred while dragging we need to stop dragging because the\n            // browser won't dispatch the `mouseup` and `touchend` events anymore.\n            if (this._lastPointerEvent) {\n                this._pointerUp(this._lastPointerEvent);\n            }\n        };\n        this._document = _document;\n        viewPortRuler.change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => this._ngZone.run(() => this.updateCropperPosition()));\n    }\n    get config() {\n        return this._config;\n    }\n    set config(val) {\n        this._config = mergeDeep({}, new ImgCropperConfig(), val);\n        this._configPrimary = mergeDeep({}, this._config);\n        this._primaryAreaWidth = this.config.width;\n        this._primaryAreaHeight = this.config.height;\n        if (this._config.round\n            && this.config.width !== this.config.height) {\n            throw new Error(`${LyImageCropper.и}: Both width and height must be equal when using \\`ImgCropperConfig.round = true\\``);\n        }\n        const maxFileSize = this._config.maxFileSize;\n        if (maxFileSize) {\n            this.maxFileSize = maxFileSize;\n        }\n    }\n    /** Set scale */\n    get scale() {\n        return this._scale;\n    }\n    set scale(val) {\n        this.setScale(val);\n    }\n    /** Get min scale */\n    get minScale() {\n        return this._minScale;\n    }\n    ngOnInit() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this._imgContainer.nativeElement;\n            element.addEventListener('mousedown', this._pointerDown, activeEventOptions);\n            element.addEventListener('touchstart', this._pointerDown, activeEventOptions);\n        });\n    }\n    ngOnDestroy() {\n        this._destroy.next();\n        this._destroy.complete();\n        const element = this._imgContainer.nativeElement;\n        this._lastPointerEvent = null;\n        this._removeGlobalEvents();\n        element.removeEventListener('mousedown', this._pointerDown, activeEventOptions);\n        element.removeEventListener('touchstart', this._pointerDown, activeEventOptions);\n    }\n    /** Load image with canvas */\n    _imgLoaded(imgElement) {\n        if (imgElement) {\n            this._img = imgElement;\n            const canvas = this._imgCanvas.nativeElement;\n            canvas.width = imgElement.width;\n            canvas.height = imgElement.height;\n            const ctx = canvas.getContext('2d');\n            ctx.clearRect(0, 0, imgElement.width, imgElement.height);\n            ctx.drawImage(imgElement, 0, 0);\n            /** set min scale */\n            this._updateMinScale(canvas);\n            this._updateMaxScale();\n        }\n    }\n    _setStylesForContImg(values) {\n        const newStyles = {};\n        if (values.x != null && values.y != null) {\n            const rootRect = this._rootRect();\n            const x = rootRect.width / 2 - (values.x);\n            const y = rootRect.height / 2 - (values.y);\n            this._imgRect.x = (values.x);\n            this._imgRect.y = (values.y);\n            this._imgRect.xc = (x);\n            this._imgRect.yc = (y);\n        }\n        newStyles.transform = `translate3d(${(this._imgRect.x)}px,${(this._imgRect.y)}px, 0)`;\n        newStyles.transform += `scale(${this._scal3Fix})`;\n        newStyles.transformOrigin = `${this._imgRect.xc}px ${this._imgRect.yc}px 0`;\n        newStyles['-webkit-transform'] = newStyles.transform;\n        newStyles['-webkit-transform-origin'] = newStyles.transformOrigin;\n        for (const key in newStyles) {\n            if (newStyles.hasOwnProperty(key)) {\n                this._renderer.setStyle(this._imgContainer.nativeElement, key, newStyles[key]);\n            }\n        }\n    }\n    /**\n     * Update area and image position only if needed,\n     * this is used when window resize\n     */\n    updateCropperPosition() {\n        if (this.isLoaded) {\n            this.updatePosition();\n            this._updateAreaIfNeeded();\n        }\n    }\n    /** Load Image from input event */\n    selectInputEvent(img) {\n        this._currentInputElement = img.target;\n        const _img = img.target;\n        if (_img.files && _img.files.length !== 1) {\n            return;\n        }\n        const fileSize = _img.files[0].size;\n        const fileName = _img.value.replace(/.*(\\/|\\\\)/, '');\n        if (this.maxFileSize && fileSize > this.maxFileSize) {\n            const cropEvent = {\n                name: fileName,\n                type: _img.files[0].type,\n                size: fileSize,\n                error: ImgCropperError.Size\n            };\n            this.clean();\n            this.error.emit(cropEvent);\n            return;\n        }\n        new Observable(observer => {\n            const reader = new FileReader();\n            reader.onerror = err => observer.error(err);\n            reader.onabort = err => observer.error(err);\n            reader.onload = (ev) => setTimeout(() => {\n                observer.next(ev);\n                observer.complete();\n            });\n            reader.readAsDataURL(_img.files[0]);\n        })\n            .pipe(take(1), takeUntil(this._destroy))\n            .subscribe((loadEvent) => {\n            const originalDataURL = loadEvent.target.result;\n            this.loadImage({\n                name: fileName,\n                size: _img.files[0].size,\n                type: this.config.type || _img.files[0].type,\n                originalDataURL\n            });\n            this.cd.markForCheck();\n        }, () => {\n            const cropEvent = {\n                name: fileName,\n                size: fileSize,\n                error: ImgCropperError.Other,\n                errorMsg: 'The File could not be loaded.',\n                type: _img.files[0].type\n            };\n            this.clean();\n            this.error.emit(cropEvent);\n        });\n    }\n    /** Set the size of the image, the values can be 0 between 1, where 1 is the original size */\n    setScale(size, noAutoCrop) {\n        // fix min scale\n        const newSize = size >= this.minScale && size <= 1 ? size : this.minScale;\n        // check\n        const changed = size != null && size !== this.scale && newSize !== this.scale;\n        this._scale = size;\n        if (!changed) {\n            return;\n        }\n        this._scal3Fix = newSize;\n        this._updateAbsoluteScale();\n        if (this.isLoaded) {\n            if (changed) {\n                const originPosition = { ...this._imgRect };\n                this.offset = {\n                    x: originPosition.x,\n                    y: originPosition.y,\n                    left: originPosition.xc,\n                    top: originPosition.yc\n                };\n                this._setStylesForContImg({});\n                this._simulatePointerMove();\n            }\n            else {\n                return;\n            }\n        }\n        else if (this.minScale) {\n            this._setStylesForContImg({\n                ...this._getCenterPoints()\n            });\n        }\n        else {\n            return;\n        }\n        this.scaleChange.emit(size);\n        if (!noAutoCrop) {\n            this._cropIfAutoCrop();\n        }\n    }\n    _getCenterPoints() {\n        const root = this._elementRef.nativeElement;\n        const img = this._imgCanvas.nativeElement;\n        const x = (root.offsetWidth - (img.width)) / 2;\n        const y = (root.offsetHeight - (img.height)) / 2;\n        return {\n            x,\n            y\n        };\n    }\n    /**\n     * Fit to screen\n     */\n    fitToScreen() {\n        const container = this._elementRef.nativeElement;\n        const min = {\n            width: container.offsetWidth,\n            height: container.offsetHeight\n        };\n        const { width, height } = this._img;\n        const minScale = {\n            width: min.width / width,\n            height: min.height / height\n        };\n        const result = Math.max(minScale.width, minScale.height);\n        this.setScale(result);\n    }\n    fit() {\n        this.setScale(this.minScale);\n    }\n    /**\n     * Simulate pointerMove with clientX = 0 and clientY = 0,\n     * this is used by `setScale` and `rotate`\n     */\n    _simulatePointerMove() {\n        this._isSliding = true;\n        this._startPointerEvent = {\n            x: 0,\n            y: 0\n        };\n        this._pointerMove({\n            clientX: 0,\n            clientY: 0,\n            type: 'n',\n            preventDefault: () => { }\n        });\n        this._isSliding = false;\n        this._startPointerEvent = null;\n    }\n    _markForCheck() {\n        this.cd.markForCheck();\n    }\n    updatePosition(xOrigin, yOrigin) {\n        const hostRect = this._rootRect();\n        const areaRect = this._areaCropperRect();\n        const areaWidth = areaRect.width > hostRect.width\n            ? hostRect.width\n            : areaRect.width;\n        const areaHeight = areaRect.height > hostRect.height\n            ? hostRect.height\n            : areaRect.height;\n        let x, y;\n        if (xOrigin == null && yOrigin == null) {\n            xOrigin = this._imgRect.xc;\n            yOrigin = this._imgRect.yc;\n        }\n        x = (areaRect.left - hostRect.left);\n        y = (areaRect.top - hostRect.top);\n        x -= (xOrigin - (areaWidth / 2));\n        y -= (yOrigin - (areaHeight / 2));\n        this._setStylesForContImg({\n            x, y\n        });\n    }\n    _slideEnd() {\n        this._cropIfAutoCrop();\n    }\n    _cropIfAutoCrop() {\n        if (this.config.autoCrop) {\n            this.crop();\n        }\n    }\n    /** + */\n    zoomIn() {\n        const scale = this._scal3Fix + .05;\n        if (scale > this.minScale && scale <= this._maxScale) {\n            this.setScale(scale);\n        }\n        else {\n            this.setScale(this._maxScale);\n        }\n    }\n    /** Clean the img cropper */\n    clean() {\n        // fix choosing the same image does not load\n        if (this._currentInputElement) {\n            this._currentInputElement.value = '';\n            this._currentInputElement = null;\n        }\n        if (this.isLoaded) {\n            this._imgRect = {};\n            this.offset = undefined;\n            this.scale = undefined;\n            this._scal3Fix = undefined;\n            this._rotation = 0;\n            this._minScale = undefined;\n            this._isLoadedImg = false;\n            this.isLoaded = false;\n            this.isCropped = false;\n            this._currentLoadConfig = undefined;\n            this.config = this._configPrimary;\n            const canvas = this._imgCanvas.nativeElement;\n            canvas.width = 0;\n            canvas.height = 0;\n            this.cleaned.emit(null);\n            this.cd.markForCheck();\n        }\n    }\n    /** - */\n    zoomOut() {\n        const scale = this._scal3Fix - .05;\n        if (scale > this.minScale && scale <= this._maxScale) {\n            this.setScale(scale);\n        }\n        else {\n            this.fit();\n        }\n    }\n    center() {\n        const newStyles = {\n            ...this._getCenterPoints()\n        };\n        this._setStylesForContImg(newStyles);\n        this._cropIfAutoCrop();\n    }\n    /**\n     * load an image from a given configuration,\n     * or from the result of a cropped image\n     */\n    loadImage(config, fn) {\n        this.clean();\n        const _config = this._currentLoadConfig = typeof config === 'string'\n            ? { originalDataURL: config }\n            : { ...config };\n        let src = _config.originalDataURL;\n        this._primaryAreaWidth = this._configPrimary.width;\n        this._primaryAreaHeight = this._configPrimary.height;\n        if (_config.areaWidth && _config.areaHeight) {\n            this.config.width = _config.areaWidth;\n            this.config.height = _config.areaHeight;\n        }\n        src = normalizeSVG(src);\n        const img = createHtmlImg(src);\n        const cropEvent = { ..._config };\n        new Observable(observer => {\n            img.onerror = err => observer.error(err);\n            img.onabort = err => observer.error(err);\n            img.onload = () => observer.next(null);\n        })\n            .pipe(take(1), takeUntil(this._destroy))\n            .subscribe(() => {\n            this._imgLoaded(img);\n            this._isLoadedImg = true;\n            this.imageLoaded.emit(cropEvent);\n            this.cd.markForCheck();\n            this._ngZone.runOutsideAngular(() => {\n                this._ngZone\n                    .onStable\n                    .asObservable()\n                    .pipe(take(1), takeUntil(this._destroy))\n                    .subscribe(() => setTimeout(() => this._ngZone.run(() => this._positionImg(cropEvent, fn))));\n            });\n        }, () => {\n            const error = {\n                name: _config.name,\n                error: ImgCropperError.Type,\n                type: _config.type,\n                size: _config.size\n            };\n            this.error.emit(error);\n        });\n    }\n    _updateAreaIfNeeded() {\n        if (!this._config.responsiveArea) {\n            return;\n        }\n        const rootRect = this._rootRect();\n        const areaRect = this._areaCropperRect();\n        const minWidth = this.config.minWidth || 1;\n        const minHeight = this.config.minHeight || 1;\n        if (!(areaRect.width > rootRect.width\n            || areaRect.height > rootRect.height\n            || areaRect.width < this._primaryAreaWidth\n            || areaRect.height < this._primaryAreaHeight)) {\n            return;\n        }\n        const areaWidthConf = Math.max(this.config.width, minWidth);\n        const areaWidthMax = Math.max(rootRect.width, minWidth);\n        const minHost = Math.min(Math.max(rootRect.width, minWidth), Math.max(rootRect.height, minHeight));\n        const currentScale = this._scal3Fix;\n        let newScale = 0;\n        const roundConf = this.config.round;\n        if (roundConf) {\n            this.config.width = this.config.height = minHost;\n        }\n        else {\n            if (areaWidthConf === areaRect.width) {\n                if (areaWidthMax > this._primaryAreaWidth) {\n                    this.config.width = this._primaryAreaWidth;\n                    this.config.height = (this._primaryAreaWidth * areaRect.height) / areaRect.width;\n                    newScale = (currentScale * this._primaryAreaWidth) / areaRect.width;\n                }\n                else {\n                    this.config.width = areaWidthMax;\n                    this.config.height = (areaWidthMax * areaRect.height) / areaRect.width;\n                    newScale = (currentScale * areaWidthMax) / areaRect.width;\n                }\n                this._updateMinScale();\n                this._updateMaxScale();\n                this.setScale(newScale, true);\n                this._markForCheck();\n            }\n        }\n    }\n    _updateAbsoluteScale() {\n        const scale = this._scal3Fix / (this.config.width / this._primaryAreaWidth);\n        this._absoluteScale = scale;\n    }\n    /**\n     * Load Image from URL\n     * @deprecated Use `loadImage` instead of `setImageUrl`\n     * @param src URL\n     * @param fn function that will be called before emit the event loaded\n     */\n    setImageUrl(src, fn) {\n        this.loadImage(src, fn);\n    }\n    _positionImg(cropEvent, fn) {\n        const loadConfig = this._currentLoadConfig;\n        this._updateMinScale(this._imgCanvas.nativeElement);\n        this._updateMaxScale();\n        this.isLoaded = false;\n        if (fn) {\n            fn();\n        }\n        else {\n            if (loadConfig.scale) {\n                this.setScale(loadConfig.scale, true);\n            }\n            else {\n                this.setScale(this.minScale, true);\n            }\n            this.rotate(loadConfig.rotation || 0);\n            this._updateAreaIfNeeded();\n            this._markForCheck();\n            this._ngZone.runOutsideAngular(() => {\n                this._ngZone\n                    .onStable\n                    .asObservable()\n                    .pipe(take(1), takeUntil(this._destroy))\n                    .subscribe(() => {\n                    if (loadConfig.xOrigin != null && loadConfig.yOrigin != null) {\n                        this.updatePosition(loadConfig.xOrigin, loadConfig.yOrigin);\n                    }\n                    this._updateAreaIfNeeded();\n                    this.isLoaded = true;\n                    this._cropIfAutoCrop();\n                    this._ngZone.run(() => {\n                        this._markForCheck();\n                        this.ready.emit(cropEvent);\n                        // tslint:disable-next-line: deprecation\n                        this.loaded.emit(cropEvent);\n                    });\n                });\n            });\n        }\n    }\n    rotate(degrees) {\n        let validDegrees = _normalizeDegrees(degrees);\n        // If negative convert to positive\n        if (validDegrees < 0) {\n            validDegrees += 360;\n        }\n        const newRotation = _normalizeDegrees((this._rotation || 0) + validDegrees);\n        if (newRotation === this._rotation) {\n            return;\n        }\n        const degreesRad = validDegrees * Math.PI / 180;\n        const canvas = this._imgCanvas.nativeElement;\n        const canvasClon = createCanvasImg(canvas);\n        const ctx = canvas.getContext('2d');\n        this._rotation = newRotation;\n        // clear\n        ctx.clearRect(0, 0, canvasClon.width, canvasClon.height);\n        // rotate canvas image\n        const transform = `rotate(${validDegrees}deg) scale(${1 / this._scal3Fix})`;\n        const transformOrigin = `${this._imgRect.xc}px ${this._imgRect.yc}px 0`;\n        canvas.style.transform = transform;\n        // tslint:disable-next-line: deprecation\n        canvas.style.webkitTransform = transform;\n        canvas.style.transformOrigin = transformOrigin;\n        // tslint:disable-next-line: deprecation\n        canvas.style.webkitTransformOrigin = transformOrigin;\n        const { left, top } = canvas.getBoundingClientRect();\n        // save rect\n        const canvasRect = canvas.getBoundingClientRect();\n        // remove rotate styles\n        canvas.removeAttribute('style');\n        // set w & h\n        const w = canvasRect.width;\n        const h = canvasRect.height;\n        ctx.canvas.width = w;\n        ctx.canvas.height = h;\n        // clear\n        ctx.clearRect(0, 0, w, h);\n        // translate and rotate\n        ctx.translate(w / 2, h / 2);\n        ctx.rotate(degreesRad);\n        ctx.drawImage(canvasClon, -canvasClon.width / 2, -canvasClon.height / 2);\n        // Update min scale\n        this._updateMinScale(canvas);\n        this._updateMaxScale();\n        // set the minimum scale, only if necessary\n        if (this.scale < this.minScale) {\n            this.setScale(0, true);\n        } //                ↑ no AutoCrop\n        const rootRect = this._rootRect();\n        this._setStylesForContImg({\n            x: (left - rootRect.left),\n            y: (top - rootRect.top)\n        });\n        // keep image inside the frame\n        const originPosition = { ...this._imgRect };\n        this.offset = {\n            x: originPosition.x,\n            y: originPosition.y,\n            left: originPosition.xc,\n            top: originPosition.yc\n        };\n        this._setStylesForContImg({});\n        this._simulatePointerMove();\n        this._cropIfAutoCrop();\n    }\n    _updateMinScale(canvas) {\n        if (!canvas) {\n            canvas = this._imgCanvas.nativeElement;\n        }\n        const config = this.config;\n        const minScale = (config.extraZoomOut ? Math.min : Math.max)(config.width / canvas.width, config.height / canvas.height);\n        this._minScale = minScale;\n        this.minScaleChange.emit(minScale);\n    }\n    _updateMaxScale() {\n        const maxScale = (this.config.width / this._primaryAreaWidth);\n        this._maxScale = maxScale;\n        this.maxScaleChange.emit(maxScale);\n    }\n    /**\n     * Resize & crop image\n     */\n    crop(config) {\n        const newConfig = config\n            ? mergeDeep({}, this.config || new ImgCropperConfig(), config) : this.config;\n        const cropEvent = this._imgCrop(newConfig);\n        this.cd.markForCheck();\n        return cropEvent;\n    }\n    /**\n     * @docs-private\n     */\n    _imgCrop(myConfig) {\n        const canvasElement = document.createElement('canvas');\n        const areaRect = this._areaCropperRect();\n        const canvasRect = this._canvasRect();\n        const scaleFix = this._scal3Fix;\n        const left = (areaRect.left - canvasRect.left) / scaleFix;\n        const top = (areaRect.top - canvasRect.top) / scaleFix;\n        const { output } = myConfig;\n        const currentImageLoadConfig = this._currentLoadConfig;\n        const area = {\n            width: myConfig.width,\n            height: myConfig.height\n        };\n        canvasElement.width = area.width / scaleFix;\n        canvasElement.height = area.height / scaleFix;\n        const ctx = canvasElement.getContext('2d');\n        if (myConfig.fill) {\n            ctx.fillStyle = myConfig.fill;\n            ctx.fillRect(0, 0, canvasElement.width, canvasElement.height);\n        }\n        ctx.drawImage(this._imgCanvas.nativeElement, -(left), -(top));\n        const result = canvasElement;\n        if (myConfig.output === ImgResolution.Default) {\n            resizeCanvas(result, this._configPrimary.width, this._configPrimary.height);\n        }\n        else if (typeof output === 'object') {\n            if (output.width && output.height) {\n                resizeCanvas(result, output.width, output.height);\n            }\n            else if (output.width) {\n                const newHeight = area.height * output.width / area.width;\n                resizeCanvas(result, output.width, newHeight);\n            }\n            else if (output.height) {\n                const newWidth = area.width * output.height / area.height;\n                resizeCanvas(result, newWidth, output.height);\n            }\n        }\n        const type = currentImageLoadConfig.originalDataURL.startsWith('http')\n            ? currentImageLoadConfig.type || myConfig.type\n            : myConfig.type || currentImageLoadConfig.type;\n        const dataURL = result.toDataURL(type);\n        const cropEvent = {\n            dataURL,\n            type,\n            name: currentImageLoadConfig.name,\n            areaWidth: this._primaryAreaWidth,\n            areaHeight: this._primaryAreaHeight,\n            width: result.width,\n            height: result.height,\n            originalDataURL: currentImageLoadConfig.originalDataURL,\n            scale: this._absoluteScale,\n            rotation: this._rotation,\n            left: (areaRect.left - canvasRect.left) / this._scal3Fix,\n            top: (areaRect.top - canvasRect.top) / this._scal3Fix,\n            size: currentImageLoadConfig.size,\n            xOrigin: this._imgRect.xc,\n            yOrigin: this._imgRect.yc,\n            position: {\n                x: this._imgRect.xc,\n                y: this._imgRect.yc\n            }\n        };\n        this.isCropped = true;\n        this.cropped.emit(cropEvent);\n        return cropEvent;\n    }\n    _rootRect() {\n        return this._elementRef.nativeElement.getBoundingClientRect();\n    }\n    _areaCropperRect() {\n        return this._areaRef.nativeElement.getBoundingClientRect();\n    }\n    _canvasRect() {\n        return this._imgCanvas.nativeElement.getBoundingClientRect();\n    }\n    _bindGlobalEvents(triggerEvent) {\n        const element = this._document;\n        const isTouch = isTouchEvent(triggerEvent);\n        const moveEventName = isTouch ? 'touchmove' : 'mousemove';\n        const endEventName = isTouch ? 'touchend' : 'mouseup';\n        element.addEventListener(moveEventName, this._pointerMove, activeEventOptions);\n        element.addEventListener(endEventName, this._pointerUp, activeEventOptions);\n        if (isTouch) {\n            element.addEventListener('touchcancel', this._pointerUp, activeEventOptions);\n        }\n        const window = this._getWindow();\n        if (typeof window !== 'undefined' && window) {\n            window.addEventListener('blur', this._windowBlur);\n        }\n    }\n    /** Removes any global event listeners that we may have added. */\n    _removeGlobalEvents() {\n        const element = this._document;\n        element.removeEventListener('mousemove', this._pointerMove, activeEventOptions);\n        element.removeEventListener('mouseup', this._pointerUp, activeEventOptions);\n        element.removeEventListener('touchmove', this._pointerMove, activeEventOptions);\n        element.removeEventListener('touchend', this._pointerUp, activeEventOptions);\n        element.removeEventListener('touchcancel', this._pointerUp, activeEventOptions);\n        const window = this._getWindow();\n        if (typeof window !== 'undefined' && window) {\n            window.removeEventListener('blur', this._windowBlur);\n        }\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n}\nLyImageCropper.и = 'LyImageCropper';\nLyImageCropper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyImageCropper, deps: [{ token: i1.StyleRenderer }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: DOCUMENT }, { token: i2.ViewportRuler }], target: i0.ɵɵFactoryTarget.Component });\nLyImageCropper.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyImageCropper, selector: \"ly-img-cropper, ly-image-cropper\", inputs: { config: \"config\", scale: \"scale\", maxFileSize: \"maxFileSize\" }, outputs: { scaleChange: \"scaleChange\", minScaleChange: \"minScale\", maxScaleChange: \"maxScale\", loaded: \"loaded\", imageLoaded: \"imageLoaded\", ready: \"ready\", cropped: \"cropped\", cleaned: \"cleaned\", error: \"error\" }, providers: [\n        StyleRenderer\n    ], viewQueries: [{ propertyName: \"_imgContainer\", first: true, predicate: [\"_imgContainer\"], descendants: true, static: true }, { propertyName: \"_areaRef\", first: true, predicate: [\"_area\"], descendants: true, read: ElementRef }, { propertyName: \"_imgCanvas\", first: true, predicate: [\"_imgCanvas\"], descendants: true, static: true }], ngImport: i0, template: \"<!-- (selectstart): On Safari starting to slide temporarily triggers text selection mode which\\nshow the wrong cursor. We prevent it by stopping the `selectstart` event. -->\\n<div #_imgContainer\\n  [className]=\\\"classes.imgContainer\\\"\\n  (selectstart)=\\\"$event.preventDefault()\\\"\\n>\\n  <canvas #_imgCanvas></canvas>\\n</div>\\n<ly-cropper-area #_area\\n  [round]=\\\"!!config.round\\\"\\n  [resizableArea]=\\\"!!config.resizableArea\\\"\\n  [keepAspectRatio]=\\\"!!config.keepAspectRatio\\\"\\n  *ngIf=\\\"_isLoadedImg; else content\\\"\\n  [ngStyle]=\\\"{\\n    width: config.width + 'px',\\n    height: config.height + 'px'\\n  }\\\"></ly-cropper-area>\\n<ng-template #content>\\n  <div [className]=\\\"classes.defaultContent\\\">\\n    <input #_fileInput type=\\\"file\\\" (change)=\\\"selectInputEvent($event)\\\" accept=\\\"image/*\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", components: [{ type: i0.forwardRef(function () { return LyCropperArea; }), selector: \"ly-cropper-area\", inputs: [\"resizableArea\", \"keepAspectRatio\", \"round\"], exportAs: [\"lyCropperArea\"] }], directives: [{ type: i0.forwardRef(function () { return i3.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i0.forwardRef(function () { return i3.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyImageCropper, decorators: [{\n            type: Component,\n            args: [{ changeDetection: ChangeDetectionStrategy.OnPush, preserveWhitespaces: false, selector: 'ly-img-cropper, ly-image-cropper', providers: [\n                        StyleRenderer\n                    ], template: \"<!-- (selectstart): On Safari starting to slide temporarily triggers text selection mode which\\nshow the wrong cursor. We prevent it by stopping the `selectstart` event. -->\\n<div #_imgContainer\\n  [className]=\\\"classes.imgContainer\\\"\\n  (selectstart)=\\\"$event.preventDefault()\\\"\\n>\\n  <canvas #_imgCanvas></canvas>\\n</div>\\n<ly-cropper-area #_area\\n  [round]=\\\"!!config.round\\\"\\n  [resizableArea]=\\\"!!config.resizableArea\\\"\\n  [keepAspectRatio]=\\\"!!config.keepAspectRatio\\\"\\n  *ngIf=\\\"_isLoadedImg; else content\\\"\\n  [ngStyle]=\\\"{\\n    width: config.width + 'px',\\n    height: config.height + 'px'\\n  }\\\"></ly-cropper-area>\\n<ng-template #content>\\n  <div [className]=\\\"classes.defaultContent\\\">\\n    <input #_fileInput type=\\\"file\\\" (change)=\\\"selectInputEvent($event)\\\" accept=\\\"image/*\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i1.StyleRenderer }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i2.ViewportRuler }]; }, propDecorators: { _imgContainer: [{\n                type: ViewChild,\n                args: ['_imgContainer', { static: true }]\n            }], _areaRef: [{\n                type: ViewChild,\n                args: ['_area', {\n                        read: ElementRef\n                    }]\n            }], _imgCanvas: [{\n                type: ViewChild,\n                args: ['_imgCanvas', { static: true }]\n            }], config: [{\n                type: Input\n            }], scale: [{\n                type: Input\n            }], maxFileSize: [{\n                type: Input\n            }], scaleChange: [{\n                type: Output\n            }], minScaleChange: [{\n                type: Output,\n                args: ['minScale']\n            }], maxScaleChange: [{\n                type: Output,\n                args: ['maxScale']\n            }], loaded: [{\n                type: Output\n            }], imageLoaded: [{\n                type: Output\n            }], ready: [{\n                type: Output\n            }], cropped: [{\n                type: Output\n            }], cleaned: [{\n                type: Output\n            }], error: [{\n                type: Output\n            }] } });\n/**\n * @dynamic\n */\nclass LyCropperArea {\n    constructor(sRenderer, _elementRef, _ngZone, _cropper, _document) {\n        this.sRenderer = sRenderer;\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._cropper = _cropper;\n        this.classes = this.sRenderer.renderSheet(STYLES, 'area');\n        this._pointerDown = (event) => {\n            // Don't do anything if the\n            // user is using anything other than the main mouse button.\n            if (this._isSliding || (!isTouchEvent(event) && event.button !== 0)) {\n                return;\n            }\n            event.preventDefault();\n            this._ngZone.run(() => {\n                this._isSliding = true;\n                this._lastPointerEvent = event;\n                this._startPointerEvent = getGesturePointFromEvent(event);\n                this._startAreaRect = this._cropper._areaCropperRect();\n                this._startImgRect = this._cropper._canvasRect();\n                event.preventDefault();\n                this._bindGlobalEvents(event);\n            });\n        };\n        this._pointerMove = (event) => {\n            if (this._isSliding) {\n                event.preventDefault();\n                this._lastPointerEvent = event;\n                const element = this._elementRef.nativeElement;\n                const { width, height, minWidth, minHeight } = this._cropper.config;\n                const point = getGesturePointFromEvent(event);\n                const deltaX = point.x - this._startPointerEvent.x;\n                const deltaY = point.y - this._startPointerEvent.y;\n                const startAreaRect = this._startAreaRect;\n                const startImgRect = this._startImgRect;\n                const round = this.round;\n                const keepAspectRatio = this._cropper.config.keepAspectRatio || event.shiftKey;\n                let newWidth = 0;\n                let newHeight = 0;\n                const rootRect = this._cropper._rootRect();\n                if (round) {\n                    // The distance from the center of the cropper area to the pointer\n                    const originX = ((width / 2 / Math.sqrt(2)) + deltaX);\n                    const originY = ((height / 2 / Math.sqrt(2)) + deltaY);\n                    // Leg\n                    const side = Math.sqrt(originX ** 2 + originY ** 2);\n                    newWidth = newHeight = side * 2;\n                }\n                else if (keepAspectRatio) {\n                    newWidth = width + deltaX * 2;\n                    newHeight = height + deltaY * 2;\n                    if (width !== height) {\n                        if (width > height) {\n                            newHeight = height / (width / newWidth);\n                        }\n                        else if (height > width) {\n                            newWidth = width / (height / newHeight);\n                        }\n                    }\n                    else {\n                        newWidth = newHeight = Math.max(newWidth, newHeight);\n                    }\n                }\n                else {\n                    newWidth = width + deltaX * 2;\n                    newHeight = height + deltaY * 2;\n                }\n                // To min width\n                if (newWidth < minWidth) {\n                    newWidth = minWidth;\n                }\n                // To min height\n                if (newHeight < minHeight) {\n                    newHeight = minHeight;\n                }\n                // Do not overflow the cropper area\n                const centerX = startAreaRect.x + startAreaRect.width / 2;\n                const centerY = startAreaRect.y + startAreaRect.height / 2;\n                const topOverflow = startImgRect.y > centerY - (newHeight / 2);\n                const bottomOverflow = centerY + (newHeight / 2) > startImgRect.bottom;\n                const minHeightOnOverflow = Math.min((centerY - startImgRect.y) * 2, (startImgRect.bottom - centerY) * 2);\n                const leftOverflow = startImgRect.x > centerX - (newWidth / 2);\n                const rightOverflow = centerX + (newWidth / 2) > startImgRect.right;\n                const minWidthOnOverflow = Math.min((centerX - startImgRect.x) * 2, (startImgRect.right - centerX) * 2);\n                const minOnOverflow = Math.min(minWidthOnOverflow, minHeightOnOverflow);\n                if (round) {\n                    if (topOverflow || bottomOverflow || leftOverflow || rightOverflow) {\n                        newHeight = newWidth = minOnOverflow;\n                    }\n                }\n                else if (keepAspectRatio) {\n                    const newNewWidth = [];\n                    const newNewHeight = [];\n                    if ((topOverflow || bottomOverflow)) {\n                        newHeight = minHeightOnOverflow;\n                        newNewHeight.push(newHeight);\n                        newWidth = width / (height / minHeightOnOverflow);\n                        newNewWidth.push(newWidth);\n                    }\n                    if ((leftOverflow || rightOverflow)) {\n                        newWidth = minWidthOnOverflow;\n                        newNewWidth.push(newWidth);\n                        newHeight = height / (width / minWidthOnOverflow);\n                        newNewHeight.push(newHeight);\n                    }\n                    if (newNewWidth.length === 2) {\n                        newWidth = Math.min(...newNewWidth);\n                    }\n                    if (newNewHeight.length === 2) {\n                        newHeight = Math.min(...newNewHeight);\n                    }\n                }\n                else {\n                    if (topOverflow || bottomOverflow) {\n                        newHeight = minHeightOnOverflow;\n                    }\n                    if (leftOverflow || rightOverflow) {\n                        newWidth = minWidthOnOverflow;\n                    }\n                }\n                // Do not overflow the container\n                if (round) {\n                    const min = Math.min(rootRect.width, rootRect.height);\n                    if (newWidth > min) {\n                        newWidth = newHeight = min;\n                    }\n                    else if (newHeight > min) {\n                        newWidth = newHeight = min;\n                    }\n                }\n                else if (keepAspectRatio) {\n                    if (newWidth > rootRect.width) {\n                        newWidth = rootRect.width;\n                        newHeight = height / (width / rootRect.width);\n                    }\n                    if (newHeight > rootRect.height) {\n                        newWidth = width / (height / rootRect.height);\n                        newHeight = rootRect.height;\n                    }\n                }\n                else {\n                    if (newWidth > rootRect.width) {\n                        newWidth = rootRect.width;\n                    }\n                    if (newHeight > rootRect.height) {\n                        newHeight = rootRect.height;\n                    }\n                }\n                // round values\n                newWidth = Math.round(newWidth);\n                newHeight = Math.round(newHeight);\n                element.style.width = `${newWidth}px`;\n                element.style.height = `${newHeight}px`;\n                this._currentWidth = newWidth;\n                this._currentHeight = newHeight;\n            }\n        };\n        /** Called when the user has lifted their pointer. */\n        this._pointerUp = (event) => {\n            if (this._isSliding) {\n                event.preventDefault();\n                this._removeGlobalEvents();\n                this._cropper._primaryAreaWidth = this._cropper.config.width = this._currentWidth;\n                this._cropper._primaryAreaHeight = this._cropper.config.height = this._currentHeight;\n                this._cropper.config = this._cropper.config;\n                this._cropper._updateMinScale();\n                this._isSliding = false;\n                this._startPointerEvent = null;\n            }\n        };\n        /** Called when the window has lost focus. */\n        this._windowBlur = () => {\n            // If the window is blurred while dragging we need to stop dragging because the\n            // browser won't dispatch the `mouseup` and `touchend` events anymore.\n            if (this._lastPointerEvent) {\n                this._pointerUp(this._lastPointerEvent);\n            }\n        };\n        this._document = _document;\n    }\n    set resizableArea(val) {\n        if (val !== this._resizableArea) {\n            this._resizableArea = val;\n            Promise.resolve(null).then(() => {\n                if (val) {\n                    this._removeResizableArea();\n                    this._addResizableArea();\n                }\n                else {\n                    this._removeResizableArea();\n                }\n            });\n        }\n    }\n    get resizableArea() {\n        return this._resizableArea;\n    }\n    ngOnDestroy() {\n        this._removeResizableArea();\n    }\n    _addResizableArea() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this._resizer.nativeElement;\n            element.addEventListener('mousedown', this._pointerDown, activeEventOptions);\n            element.addEventListener('touchstart', this._pointerDown, activeEventOptions);\n        });\n    }\n    _removeResizableArea() {\n        const element = this._resizer?.nativeElement;\n        if (element) {\n            this._lastPointerEvent = null;\n            this._removeGlobalEvents();\n            element.removeEventListener('mousedown', this._pointerDown, activeEventOptions);\n            element.removeEventListener('touchstart', this._pointerDown, activeEventOptions);\n        }\n    }\n    _bindGlobalEvents(triggerEvent) {\n        const element = this._document;\n        const isTouch = isTouchEvent(triggerEvent);\n        const moveEventName = isTouch ? 'touchmove' : 'mousemove';\n        const endEventName = isTouch ? 'touchend' : 'mouseup';\n        element.addEventListener(moveEventName, this._pointerMove, activeEventOptions);\n        element.addEventListener(endEventName, this._pointerUp, activeEventOptions);\n        if (isTouch) {\n            element.addEventListener('touchcancel', this._pointerUp, activeEventOptions);\n        }\n        const window = this._getWindow();\n        if (typeof window !== 'undefined' && window) {\n            window.addEventListener('blur', this._windowBlur);\n        }\n    }\n    /** Removes any global event listeners that we may have added. */\n    _removeGlobalEvents() {\n        const element = this._document;\n        element.removeEventListener('mousemove', this._pointerMove, activeEventOptions);\n        element.removeEventListener('mouseup', this._pointerUp, activeEventOptions);\n        element.removeEventListener('touchmove', this._pointerMove, activeEventOptions);\n        element.removeEventListener('touchend', this._pointerUp, activeEventOptions);\n        element.removeEventListener('touchcancel', this._pointerUp, activeEventOptions);\n        const window = this._getWindow();\n        if (typeof window !== 'undefined' && window) {\n            window.removeEventListener('blur', this._windowBlur);\n        }\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n}\nLyCropperArea.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyCropperArea, deps: [{ token: i1.StyleRenderer }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: LyImageCropper }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Component });\nLyCropperArea.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyCropperArea, selector: \"ly-cropper-area\", inputs: { resizableArea: \"resizableArea\", keepAspectRatio: \"keepAspectRatio\", round: \"round\" }, providers: [\n        StyleRenderer\n    ], viewQueries: [{ propertyName: \"_resizer\", first: true, predicate: [\"resizer\"], descendants: true }], exportAs: [\"lyCropperArea\"], ngImport: i0, template: \"<div #resizer\\n  *ngIf=\\\"resizableArea\\\"\\n  [class]=\\\"classes.resizer\\\"\\n></div>\", directives: [{ type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n__decorate([\n    Style2((_value, _media) => ({ after }, ref) => {\n        ref.renderStyleSheet(STYLES);\n        const __ = ref.selectorsOf(STYLES);\n        return (_className) => `${_className}{border-radius:50%;}${_className} .${__.resizer}{${after}:${pos}%;bottom:${pos}%;transform:translate(4px,4px);}`;\n    }, coerceBooleanProperty)\n], LyCropperArea.prototype, \"round\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyCropperArea, decorators: [{\n            type: Component,\n            args: [{ selector: 'ly-cropper-area', providers: [\n                        StyleRenderer\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, exportAs: 'lyCropperArea', template: \"<div #resizer\\n  *ngIf=\\\"resizableArea\\\"\\n  [class]=\\\"classes.resizer\\\"\\n></div>\" }]\n        }], ctorParameters: function () { return [{ type: i1.StyleRenderer }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: LyImageCropper }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { _resizer: [{\n                type: ViewChild,\n                args: ['resizer']\n            }], resizableArea: [{\n                type: Input\n            }], keepAspectRatio: [{\n                type: Input\n            }], round: [{\n                type: Input\n            }] } });\n/**\n * Normalize degrees for cropper rotation\n * @docs-private\n */\nfunction _normalizeDegrees(n) {\n    const de = n % 360;\n    if (de % 90) {\n        throw new Error(`LyCropper: Invalid \\`${n}\\` degree, only accepted values: 0, 90, 180, 270 & 360.`);\n    }\n    return de;\n}\n/**\n * @docs-private\n */\nfunction createCanvasImg(img) {\n    // create a new canvas\n    const newCanvas = document.createElement('canvas');\n    const context = newCanvas.getContext('2d');\n    // set dimensions\n    newCanvas.width = img.width;\n    newCanvas.height = img.height;\n    // apply the old canvas to the new one\n    context.drawImage(img, 0, 0);\n    // return the new canvas\n    return newCanvas;\n}\nfunction normalizeSVG(dataURL) {\n    if (window.atob && isSvgImage(dataURL)) {\n        const len = dataURL.length / 5;\n        const text = window.atob(dataURL.replace(DATA_IMAGE_SVG_PREFIX, ''));\n        const span = document.createElement('span');\n        span.innerHTML = text;\n        const svg = span.querySelector('svg');\n        span.setAttribute('style', 'display:none');\n        document.body.appendChild(span);\n        const width = parseFloat(getComputedStyle(svg).width) || 1;\n        const height = parseFloat(getComputedStyle(svg).height) || 1;\n        const max = Math.max(width, height);\n        svg.setAttribute('width', `${len / (width / max)}px`);\n        svg.setAttribute('height', `${len / (height / max)}px`);\n        const result = DATA_IMAGE_SVG_PREFIX + window.btoa(span.innerHTML);\n        document.body.removeChild(span);\n        return result;\n    }\n    return dataURL;\n}\nfunction isSvgImage(dataUrl) {\n    return dataUrl.startsWith(DATA_IMAGE_SVG_PREFIX);\n}\nfunction createHtmlImg(src) {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.src = src;\n    return img;\n}\nfunction getGesturePointFromEvent(event) {\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    const point = isTouchEvent(event)\n        ? (event.touches[0] || event.changedTouches[0])\n        : event;\n    return {\n        x: point.clientX,\n        y: point.clientY\n    };\n}\n/** Returns whether an event is a touch event. */\nfunction isTouchEvent(event) {\n    return event.type[0] === 't';\n}\nfunction roundNumber(n) {\n    return Math.round(n);\n}\n\nclass LyImageCropperModule {\n}\nLyImageCropperModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyImageCropperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nLyImageCropperModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyImageCropperModule, declarations: [LyImageCropper, LyCropperArea], imports: [CommonModule], exports: [LyImageCropper] });\nLyImageCropperModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyImageCropperModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyImageCropperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [LyImageCropper],\n                    declarations: [LyImageCropper, LyCropperArea]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ImgCropperConfig, ImgCropperError, ImgResolution, LyCropperArea, LyImageCropper, LyImageCropperModule, STYLES, _normalizeDegrees, roundNumber };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACxI,OAAO,KAAKC,EAAE,MAAM,WAAW;AAC/B,SAASC,IAAI,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,MAAM,QAAQ,WAAW;AACrG,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,KAAKC,EAAE,MAAM,wBAAwB;;AAE5C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA;IAAAC,KAAA,EAAAF,EAAA;IAAAG,MAAA,EAAAF;EAAA;AAAA;AAAA,SAAAG,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA26BkGpC,EAAE,CAAAsC,SAAA,2BAGw3B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAH33BvC,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAyC,UAAA,YAAAF,MAAA,CAAAG,MAAA,CAAAC,KAGkoB,CAAC,oBAAAJ,MAAA,CAAAG,MAAA,CAAAE,aAAD,CAAC,sBAAAL,MAAA,CAAAG,MAAA,CAAAG,eAAD,CAAC,YAHroB7C,EAAE,CAAA8C,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAG,MAAA,CAAAT,KAAA,SAAAM,MAAA,CAAAG,MAAA,CAAAR,MAAA,QAGkoB,CAAC;EAAA;AAAA;AAAA,SAAAa,sCAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAY,GAAA,GAHroBhD,EAAE,CAAAiD,gBAAA;IAAFjD,EAAE,CAAAkD,cAAA,YAGg8B,CAAC,iBAAD,CAAC;IAHn8BlD,EAAE,CAAAmD,UAAA,oBAAAC,8DAAAC,MAAA;MAAFrD,EAAE,CAAAsD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFvD,EAAE,CAAAwC,aAAA;MAAA,OAAFxC,EAAE,CAAAwD,WAAA,CAGm/BD,MAAA,CAAAE,gBAAA,CAAAJ,MAAuB,EAAC;IAAA,CAAC,CAAC;IAH/gCrD,EAAE,CAAA0D,YAAA,CAGgiC,CAAC;IAHniC1D,EAAE,CAAA2D,YAAA,EAG+jC,CAAC;IAHlkC3D,EAAE,CAAA0D,YAAA,CAGykC,CAAC;EAAA;EAAA,IAAAtB,EAAA;IAAA,MAAAwB,MAAA,GAH5kC5D,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAyC,UAAA,cAAAmB,MAAA,CAAAC,OAAA,CAAAC,cAG+7B,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,6BAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAHl8BpC,EAAE,CAAAsC,SAAA,kBAiT6I,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAA8B,MAAA,GAjThJlE,EAAE,CAAAwC,aAAA;IAAFxC,EAAE,CAAAmE,UAAA,CAAAD,MAAA,CAAAL,OAAA,CAAAO,OAiToI,CAAC;EAAA;AAAA;AAxtCzO,SAASC,YAAYA,CAACC,MAAM,EAAErC,KAAK,EAAEC,MAAM,EAAE;EACzC,MAAMqC,YAAY,GAAGD,MAAM,CAACrC,KAAK;EACjC,MAAMuC,aAAa,GAAGF,MAAM,CAACpC,MAAM;EACnCD,KAAK,GAAGwC,IAAI,CAAC9B,KAAK,CAACV,KAAK,CAAC;EACzBC,MAAM,GAAGuC,IAAI,CAAC9B,KAAK,CAACT,MAAM,CAAC;EAC3B,MAAMwC,OAAO,GAAGH,YAAY,GAAGtC,KAAK;EACpC,MAAM0C,OAAO,GAAGH,aAAa,GAAGtC,MAAM;EACtC,MAAM0C,YAAY,GAAGH,IAAI,CAACI,IAAI,CAACH,OAAO,GAAG,CAAC,CAAC;EAC3C,MAAMI,YAAY,GAAGL,IAAI,CAACI,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC;EAC3C,MAAMtC,GAAG,GAAGiC,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;EACnC,MAAMC,GAAG,GAAG3C,GAAG,CAAC4C,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEV,YAAY,EAAEC,aAAa,CAAC;EAC/D,MAAMU,IAAI,GAAG7C,GAAG,CAAC8C,eAAe,CAAClD,KAAK,EAAEC,MAAM,CAAC;EAC/C,MAAMkD,IAAI,GAAGJ,GAAG,CAACI,IAAI;EACrB,MAAMC,KAAK,GAAGH,IAAI,CAACE,IAAI;EACvB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpD,MAAM,EAAEoD,CAAC,EAAE,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,KAAK,EAAEsD,CAAC,EAAE,EAAE;MAC5B,MAAMC,EAAE,GAAG,CAACD,CAAC,GAAGD,CAAC,GAAGrD,KAAK,IAAI,CAAC;MAC9B,IAAIwD,MAAM,GAAG,CAAC;MACd,IAAIC,OAAO,GAAG,CAAC;MACf,IAAIC,aAAa,GAAG,CAAC;MACrB,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAIC,IAAI,GAAG,CAAC;MACZ,IAAIC,IAAI,GAAG,CAAC;MACZ,MAAMC,QAAQ,GAAGV,CAAC,GAAGX,OAAO;MAC5B,MAAMsB,QAAQ,GAAGxB,IAAI,CAACyB,KAAK,CAACX,CAAC,GAAGb,OAAO,CAAC;MACxC,IAAIyB,OAAO,GAAG1B,IAAI,CAACI,IAAI,CAAC,CAACU,CAAC,GAAG,CAAC,IAAIb,OAAO,CAAC;MAC1C,MAAM0B,QAAQ,GAAG3B,IAAI,CAACyB,KAAK,CAACZ,CAAC,GAAGX,OAAO,CAAC;MACxC,IAAI0B,OAAO,GAAG5B,IAAI,CAACI,IAAI,CAAC,CAACS,CAAC,GAAG,CAAC,IAAIX,OAAO,CAAC;MAC1CwB,OAAO,GAAG1B,IAAI,CAAC6B,GAAG,CAACH,OAAO,EAAE5B,YAAY,CAAC;MACzC8B,OAAO,GAAG5B,IAAI,CAAC6B,GAAG,CAACD,OAAO,EAAE7B,aAAa,CAAC;MAC1C,KAAK,IAAI+B,EAAE,GAAGH,QAAQ,EAAEG,EAAE,GAAGF,OAAO,EAAEE,EAAE,EAAE,EAAE;QACxC,MAAMC,EAAE,GAAG/B,IAAI,CAACgC,GAAG,CAACT,QAAQ,GAAGO,EAAE,CAAC,GAAGzB,YAAY;QACjD,MAAM4B,QAAQ,GAAGnB,CAAC,GAAGb,OAAO;QAC5B,MAAMiC,EAAE,GAAGH,EAAE,GAAGA,EAAE,CAAC,CAAC;QACpB,KAAK,IAAII,EAAE,GAAGX,QAAQ,EAAEW,EAAE,GAAGT,OAAO,EAAES,EAAE,EAAE,EAAE;UACxC,MAAMC,EAAE,GAAGpC,IAAI,CAACgC,GAAG,CAACC,QAAQ,GAAGE,EAAE,CAAC,GAAGhC,YAAY;UACjD,MAAMkC,CAAC,GAAGrC,IAAI,CAACsC,IAAI,CAACJ,EAAE,GAAGE,EAAE,GAAGA,EAAE,CAAC;UACjC,IAAIC,CAAC,IAAI,CAAC,EAAE;YACR;YACA;UACJ;UACA;UACArB,MAAM,GAAG,CAAC,GAAGqB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;UACtC,MAAME,KAAK,GAAG,CAAC,IAAIJ,EAAE,GAAGL,EAAE,GAAGhC,YAAY,CAAC;UAC1C;UACAwB,IAAI,IAAIN,MAAM,GAAGL,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;UAChCrB,aAAa,IAAIF,MAAM;UACvB;UACA,IAAIL,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;YACvBvB,MAAM,GAAGA,MAAM,GAAGL,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG;UAC3C;UACApB,IAAI,IAAIH,MAAM,GAAGL,IAAI,CAAC4B,KAAK,CAAC;UAC5BnB,IAAI,IAAIJ,MAAM,GAAGL,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;UAChClB,IAAI,IAAIL,MAAM,GAAGL,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;UAChCtB,OAAO,IAAID,MAAM;QACrB;MACJ;MACAJ,KAAK,CAACG,EAAE,CAAC,GAAGI,IAAI,GAAGF,OAAO;MAC1BL,KAAK,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGK,IAAI,GAAGH,OAAO;MAC9BL,KAAK,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGM,IAAI,GAAGJ,OAAO;MAC9BL,KAAK,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGO,IAAI,GAAGJ,aAAa;IACxC;EACJ;EACA;EACArB,MAAM,CAACrC,KAAK,GAAGA,KAAK;EACpBqC,MAAM,CAACpC,MAAM,GAAGA,MAAM;EACtB;EACAG,GAAG,CAAC4E,YAAY,CAAC/B,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B,OAAO7C,GAAG;AACd;AAEA,MAAM6E,kBAAkB,GAAG7F,+BAA+B,CAAC;EAAE8F,OAAO,EAAE;AAAM,CAAC,CAAC;AAC9E,MAAMC,cAAc,GAAG,CAAC,CAAC;AACzB,MAAMC,qBAAqB,GAAG,4BAA4B;AAC1D,MAAMC,GAAG,GAAG,CAAC,GAAG,GAAG7C,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAGtC,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC;AACzD,MAAMQ,MAAM,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC3B,MAAMC,OAAO,GAAGD,GAAG,CAACE,WAAW,CAACJ,MAAM,CAAC;EACvC,MAAM;IAAEK;EAAM,CAAC,GAAGJ,KAAK;EACvB,OAAO;IACHK,KAAK,EAAEC,cAAc,CAACC,CAAC;IACvBC,SAAS,EAAEZ,cAAc;IACzBa,IAAI,EAAEA,CAAA,KAAOC,UAAU,IAAM,GAAEA,UAAW,mLAAkLvH,IAAI,CAAG6G,KAAK,CAACE,OAAO,IACzOF,KAAK,CAACE,OAAO,CAACO,IAAI,KACjBT,KAAK,CAACE,OAAO,CAACO,IAAI,YAAYrH,eAAe,GAC3C4G,KAAK,CAACE,OAAO,CAACO,IAAI,CAACE,cAAc,CAACC,EAAE,IAAIA,EAAE,CAACV,OAAO,CAAC,CAAC,GACpDF,KAAK,CAACE,OAAO,CAACO,IAAI,CAACP,OAAO,CAAC,CAAC,EAAK,GAAEQ,UAAW,EAAC,CAAE,EAAC;IAC5DG,YAAY,EAAGH,UAAU,IAAM,GAAEA,UAAW,+EAA8EA,UAAW,2BAA0B;IAC/JI,OAAO,EAAGJ,UAAU,IAAM,GAAEvH,IAAI,CAAEE,gBAAgB,CAAC0H,IAAI,EAAI,GAAEL,UAAW,EAAC,CAAE,EAAC;IAC5EM,IAAI,EAAGN,UAAU,IAAM,GAAEA,UAAW,kEAAiEvH,IAAI,CAAEE,gBAAgB,CAAC0H,IAAI,EAAI,GAAEL,UAAW,EAAC,CAAE,GAAEA,UAAW,iBAAgBvH,IAAI,CAAEE,gBAAgB,CAAC0H,IAAI,EAAI,GAAEL,UAAW,WAAUA,UAAW,QAAO,CAAE,GAAEA,UAAW,WAAUA,UAAW,sBAAqBA,UAAW,6GAA4GA,UAAW,kEAAiE;IACrf9D,OAAO,EAAG8D,UAAU,IAAM,GAAEA,UAAW,0GAAyGN,KAAM,gCAA+BA,KAAK,KAAK,OAAO,GAChM,aAAa,GACb,aAAc,KAAIjH,IAAI,CAAEE,gBAAgB,CAAC0H,IAAI,EAAI,GAAEL,UAAW,SAAQ,CAAE,GAAEA,UAAW,4EAA2E;IACtKpE,cAAc,EAAGoE,UAAU,IAAM,GAAEA,UAAW,4DAA2DvH,IAAI,CAAEE,gBAAgB,CAAC0H,IAAI,EAAI,GAAEL,UAAW,IAAGA,UAAW,UAAS,CAAE,GAAEA,UAAW,sCAAqCA,UAAW;EAC/O,CAAC;AACL,CAAC;AACD;AACA,MAAMO,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACzG,KAAK,GAAG,GAAG;IAChB;IACA,IAAI,CAACC,MAAM,GAAG,GAAG;IACjB,IAAI,CAACyG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,MAAM,GAAGC,aAAa,CAACC,OAAO;EACvC;AACJ;AACA;AACA,IAAID,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvD;EACAA,aAAa,CAACA,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;AACvE,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,IAAIE,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxB;EACAA,eAAe,CAACA,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACrD;EACAA,eAAe,CAACA,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACrD;EACAA,eAAe,CAACA,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AAC3D,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,MAAMnB,cAAc,CAAC;EACjBY,WAAWA,CAACQ,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,EAAE,EAAEC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAE;IAClF,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACzF,OAAO,GAAG,IAAI,CAACqF,SAAS,CAACO,WAAW,CAAClC,MAAM,EAAE,IAAI,CAAC;IACvD,IAAI,CAACmC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,GAAG,IAAI3J,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAAC4J,cAAc,GAAG,IAAI5J,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAAC6J,cAAc,GAAG,IAAI7J,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAAC8J,MAAM,GAAG,IAAI9J,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC+J,WAAW,GAAG,IAAI/J,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACgK,KAAK,GAAG,IAAIhK,YAAY,CAAC,CAAC;IAC/B;IACA,IAAI,CAACiK,OAAO,GAAG,IAAIjK,YAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACkK,OAAO,GAAG,IAAIlK,YAAY,CAAC,CAAC;IACjC;IACA;IACA,IAAI,CAACmK,KAAK,GAAG,IAAInK,YAAY,CAAC,CAAC;IAC/B;IACA,IAAI,CAACoK,QAAQ,GAAG,IAAIpJ,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACqJ,YAAY,GAAIC,KAAK,IAAK;MAC3B;MACA;MACA,IAAI,IAAI,CAACC,UAAU,IAAK,CAACC,YAAY,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAE,EAAE;QACjE;MACJ;MACA,IAAI,CAACpB,OAAO,CAACqB,GAAG,CAAC,MAAM;QACnB,IAAI,CAACH,UAAU,GAAG,IAAI;QACtB,IAAI,CAACI,MAAM,GAAG;UACVC,CAAC,EAAE,IAAI,CAACnB,QAAQ,CAACmB,CAAC;UAClBC,CAAC,EAAE,IAAI,CAACpB,QAAQ,CAACoB,CAAC;UAClBC,IAAI,EAAE,IAAI,CAACrB,QAAQ,CAACsB,EAAE;UACtBC,GAAG,EAAE,IAAI,CAACvB,QAAQ,CAACwB;QACvB,CAAC;QACD,IAAI,CAACC,iBAAiB,GAAGZ,KAAK;QAC9B,IAAI,CAACa,kBAAkB,GAAGC,wBAAwB,CAACd,KAAK,CAAC;QACzDA,KAAK,CAACe,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,iBAAiB,CAAChB,KAAK,CAAC;MACjC,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACiB,YAAY,GAAIjB,KAAK,IAAK;MAC3B,IAAI,IAAI,CAACC,UAAU,EAAE;QACjBD,KAAK,CAACe,cAAc,CAAC,CAAC;QACtB,IAAI,CAACH,iBAAiB,GAAGZ,KAAK;QAC9B,IAAIM,CAAC,EAAEC,CAAC;QACR,MAAMxG,MAAM,GAAG,IAAI,CAACmH,UAAU,CAACC,aAAa;QAC5C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;QAC/B,MAAMlJ,MAAM,GAAG,IAAI,CAACA,MAAM;QAC1B,MAAMmJ,MAAM,GAAG,IAAI,CAACjB,MAAM;QAC1B,MAAMkB,KAAK,GAAGT,wBAAwB,CAACd,KAAK,CAAC;QAC7C,MAAMwB,MAAM,GAAGD,KAAK,CAACjB,CAAC,GAAG,IAAI,CAACO,kBAAkB,CAACP,CAAC;QAClD,MAAMmB,MAAM,GAAGF,KAAK,CAAChB,CAAC,GAAG,IAAI,CAACM,kBAAkB,CAACN,CAAC;QAClD,IAAI,CAACa,QAAQ,IAAI,CAACE,MAAM,EAAE;UACtB;QACJ;QACA,MAAMI,WAAW,GAAG3H,MAAM,CAACpC,MAAM,GAAGyJ,QAAQ,GAAGjJ,MAAM,CAACR,MAAM,IAAIQ,MAAM,CAACwJ,YAAY;QACnF,MAAMC,WAAW,GAAG7H,MAAM,CAACrC,KAAK,GAAG0J,QAAQ,GAAGjJ,MAAM,CAACT,KAAK,IAAIS,MAAM,CAACwJ,YAAY;QACjF,MAAME,SAAS,GAAI1J,MAAM,CAACT,KAAK,GAAG,CAAC,GAAG0J,QAAQ,IAAKE,MAAM,CAACd,IAAI,GAAIgB,MAAM,GAAGJ,QAAS;QACpF,MAAMU,UAAU,GAAI3J,MAAM,CAACT,KAAK,GAAG,CAAC,GAAG0J,QAAQ,GAAKrH,MAAM,CAACrC,KAAM,IAAI4J,MAAM,CAACd,IAAI,GAAIgB,MAAM,GAAGJ,QAAS,CAAC,IAAIjJ,MAAM,CAACT,KAAK,GAAG0J,QAAQ;QAClI,MAAMW,QAAQ,GAAK5J,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGyJ,QAAQ,IAAME,MAAM,CAACZ,GAAG,GAAIe,MAAM,GAAGL,QAAW;QACvF,MAAMY,WAAW,GAAM7J,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGyJ,QAAQ,GAAKrH,MAAM,CAACpC,MAAO,IAAI2J,MAAM,CAACZ,GAAG,GAAIe,MAAM,GAAGL,QAAS,CAAC,IAAMjJ,MAAM,CAACR,MAAM,GAAGyJ,QAAU;QAC3I;QACA,IAAKS,SAAS,IAAI,CAACD,WAAW,IAAM,CAACC,SAAS,IAAID,WAAY,EAAE;UAC5DtB,CAAC,GAAGgB,MAAM,CAAChB,CAAC,GAAIgB,MAAM,CAACd,IAAK,GAAIrI,MAAM,CAACT,KAAK,GAAG,CAAC,GAAG0J,QAAS;QAChE;QACA;QACA,IAAKU,UAAU,IAAI,CAACF,WAAW,IAAM,CAACE,UAAU,IAAIF,WAAY,EAAE;UAC9DtB,CAAC,GAAGgB,MAAM,CAAChB,CAAC,GAAIgB,MAAM,CAACd,IAAK,GAAIrI,MAAM,CAACT,KAAK,GAAG,CAAC,GAAG0J,QAAS,GAAGrH,MAAM,CAACrC,KAAK;QAC/E;QACA;QACA,IAAKqK,QAAQ,IAAI,CAACL,WAAW,IAAM,CAACK,QAAQ,IAAIL,WAAY,EAAE;UAC1DnB,CAAC,GAAGe,MAAM,CAACf,CAAC,GAAIe,MAAM,CAACZ,GAAI,GAAIvI,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGyJ,QAAS;QAChE;QACA;QACA,IAAKY,WAAW,IAAI,CAACN,WAAW,IAAM,CAACM,WAAW,IAAIN,WAAY,EAAE;UAChEnB,CAAC,GAAGe,MAAM,CAACf,CAAC,GAAIe,MAAM,CAACZ,GAAI,GAAIvI,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGyJ,QAAS,GAAGrH,MAAM,CAACpC,MAAM;QAChF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI2I,CAAC,KAAK,KAAK,CAAC,EAAE;UACdA,CAAC,GAAIkB,MAAM,GAAGJ,QAAQ,GAAKE,MAAM,CAAChB,CAAE;QACxC;QACA,IAAIC,CAAC,KAAK,KAAK,CAAC,EAAE;UACdA,CAAC,GAAIkB,MAAM,GAAGL,QAAQ,GAAKE,MAAM,CAACf,CAAE;QACxC;QACA,IAAI,CAAC0B,oBAAoB,CAAC;UACtB3B,CAAC;UAAEC;QACP,CAAC,CAAC;MACN;IACJ,CAAC;IACD;IACA,IAAI,CAAC2B,UAAU,GAAIlC,KAAK,IAAK;MACzB,IAAI,IAAI,CAACC,UAAU,EAAE;QACjBD,KAAK,CAACe,cAAc,CAAC,CAAC;QACtB,IAAI,CAACoB,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAAClC,UAAU,GAAG,KAAK;QACvB,IAAI,CAACY,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACuB,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;IACA,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB;MACA;MACA,IAAI,IAAI,CAACzB,iBAAiB,EAAE;QACxB,IAAI,CAACsB,UAAU,CAAC,IAAI,CAACtB,iBAAiB,CAAC;MAC3C;IACJ,CAAC;IACD,IAAI,CAAC5B,SAAS,GAAGA,SAAS;IAC1BC,aAAa,CAACqD,MAAM,CAAC,CAAC,CACjBC,IAAI,CAAC3L,SAAS,CAAC,IAAI,CAACkJ,QAAQ,CAAC,CAAC,CAC9B0C,SAAS,CAAC,MAAM,IAAI,CAACzD,OAAO,CAACqB,GAAG,CAAC,MAAM,IAAI,CAACqC,qBAAqB,CAAC,CAAC,CAAC,CAAC;EAC9E;EACA,IAAItK,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACuK,OAAO;EACvB;EACA,IAAIvK,MAAMA,CAACwK,GAAG,EAAE;IACZ,IAAI,CAACD,OAAO,GAAGnM,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI2H,gBAAgB,CAAC,CAAC,EAAEyE,GAAG,CAAC;IACzD,IAAI,CAACC,cAAc,GAAGrM,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmM,OAAO,CAAC;IACjD,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAAC1K,MAAM,CAACT,KAAK;IAC1C,IAAI,CAACoL,kBAAkB,GAAG,IAAI,CAAC3K,MAAM,CAACR,MAAM;IAC5C,IAAI,IAAI,CAAC+K,OAAO,CAACtK,KAAK,IACf,IAAI,CAACD,MAAM,CAACT,KAAK,KAAK,IAAI,CAACS,MAAM,CAACR,MAAM,EAAE;MAC7C,MAAM,IAAIoL,KAAK,CAAE,GAAExF,cAAc,CAACC,CAAE,oFAAmF,CAAC;IAC5H;IACA,MAAMwF,WAAW,GAAG,IAAI,CAACN,OAAO,CAACM,WAAW;IAC5C,IAAIA,WAAW,EAAE;MACb,IAAI,CAACA,WAAW,GAAGA,WAAW;IAClC;EACJ;EACA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACN,GAAG,EAAE;IACX,IAAI,CAACQ,QAAQ,CAACR,GAAG,CAAC;EACtB;EACA;EACA,IAAIS,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACvE,OAAO,CAACwE,iBAAiB,CAAC,MAAM;MACjC,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACtC,aAAa;MAChDqC,OAAO,CAACE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC3D,YAAY,EAAEpD,kBAAkB,CAAC;MAC5E6G,OAAO,CAACE,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC3D,YAAY,EAAEpD,kBAAkB,CAAC;IACjF,CAAC,CAAC;EACN;EACAgH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7D,QAAQ,CAAC8D,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC9D,QAAQ,CAAC+D,QAAQ,CAAC,CAAC;IACxB,MAAML,OAAO,GAAG,IAAI,CAACC,aAAa,CAACtC,aAAa;IAChD,IAAI,CAACP,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACuB,mBAAmB,CAAC,CAAC;IAC1BqB,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC/D,YAAY,EAAEpD,kBAAkB,CAAC;IAC/E6G,OAAO,CAACM,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC/D,YAAY,EAAEpD,kBAAkB,CAAC;EACpF;EACA;EACAoH,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAIA,UAAU,EAAE;MACZ,IAAI,CAACC,IAAI,GAAGD,UAAU;MACtB,MAAMjK,MAAM,GAAG,IAAI,CAACmH,UAAU,CAACC,aAAa;MAC5CpH,MAAM,CAACrC,KAAK,GAAGsM,UAAU,CAACtM,KAAK;MAC/BqC,MAAM,CAACpC,MAAM,GAAGqM,UAAU,CAACrM,MAAM;MACjC,MAAMG,GAAG,GAAGiC,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;MACnC1C,GAAG,CAACoM,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEF,UAAU,CAACtM,KAAK,EAAEsM,UAAU,CAACrM,MAAM,CAAC;MACxDG,GAAG,CAACqM,SAAS,CAACH,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;MAC/B;MACA,IAAI,CAACI,eAAe,CAACrK,MAAM,CAAC;MAC5B,IAAI,CAACsK,eAAe,CAAC,CAAC;IAC1B;EACJ;EACApC,oBAAoBA,CAACqC,MAAM,EAAE;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAID,MAAM,CAAChE,CAAC,IAAI,IAAI,IAAIgE,MAAM,CAAC/D,CAAC,IAAI,IAAI,EAAE;MACtC,MAAMiE,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MACjC,MAAMnE,CAAC,GAAGkE,QAAQ,CAAC9M,KAAK,GAAG,CAAC,GAAI4M,MAAM,CAAChE,CAAE;MACzC,MAAMC,CAAC,GAAGiE,QAAQ,CAAC7M,MAAM,GAAG,CAAC,GAAI2M,MAAM,CAAC/D,CAAE;MAC1C,IAAI,CAACpB,QAAQ,CAACmB,CAAC,GAAIgE,MAAM,CAAChE,CAAE;MAC5B,IAAI,CAACnB,QAAQ,CAACoB,CAAC,GAAI+D,MAAM,CAAC/D,CAAE;MAC5B,IAAI,CAACpB,QAAQ,CAACsB,EAAE,GAAIH,CAAE;MACtB,IAAI,CAACnB,QAAQ,CAACwB,EAAE,GAAIJ,CAAE;IAC1B;IACAgE,SAAS,CAACG,SAAS,GAAI,eAAe,IAAI,CAACvF,QAAQ,CAACmB,CAAG,MAAM,IAAI,CAACnB,QAAQ,CAACoB,CAAG,QAAO;IACrFgE,SAAS,CAACG,SAAS,IAAK,SAAQ,IAAI,CAACrD,SAAU,GAAE;IACjDkD,SAAS,CAACI,eAAe,GAAI,GAAE,IAAI,CAACxF,QAAQ,CAACsB,EAAG,MAAK,IAAI,CAACtB,QAAQ,CAACwB,EAAG,MAAK;IAC3E4D,SAAS,CAAC,mBAAmB,CAAC,GAAGA,SAAS,CAACG,SAAS;IACpDH,SAAS,CAAC,0BAA0B,CAAC,GAAGA,SAAS,CAACI,eAAe;IACjE,KAAK,MAAMC,GAAG,IAAIL,SAAS,EAAE;MACzB,IAAIA,SAAS,CAACM,cAAc,CAACD,GAAG,CAAC,EAAE;QAC/B,IAAI,CAAChG,SAAS,CAACkG,QAAQ,CAAC,IAAI,CAACrB,aAAa,CAACtC,aAAa,EAAEyD,GAAG,EAAEL,SAAS,CAACK,GAAG,CAAC,CAAC;MAClF;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACInC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACsC,QAAQ,EAAE;MACf,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B;EACJ;EACA;EACA/L,gBAAgBA,CAACuB,GAAG,EAAE;IAClB,IAAI,CAACyK,oBAAoB,GAAGzK,GAAG,CAAC0K,MAAM;IACtC,MAAMlB,IAAI,GAAGxJ,GAAG,CAAC0K,MAAM;IACvB,IAAIlB,IAAI,CAACmB,KAAK,IAAInB,IAAI,CAACmB,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACvC;IACJ;IACA,MAAMC,QAAQ,GAAGrB,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI;IACnC,MAAMC,QAAQ,GAAGvB,IAAI,CAACwB,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACpD,IAAI,IAAI,CAAC1C,WAAW,IAAIsC,QAAQ,GAAG,IAAI,CAACtC,WAAW,EAAE;MACjD,MAAM2C,SAAS,GAAG;QACdC,IAAI,EAAEJ,QAAQ;QACdK,IAAI,EAAE5B,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAACS,IAAI;QACxBN,IAAI,EAAED,QAAQ;QACdzF,KAAK,EAAEnB,eAAe,CAACoH;MAC3B,CAAC;MACD,IAAI,CAACC,KAAK,CAAC,CAAC;MACZ,IAAI,CAAClG,KAAK,CAACmG,IAAI,CAACL,SAAS,CAAC;MAC1B;IACJ;IACA,IAAIhP,UAAU,CAACsP,QAAQ,IAAI;MACvB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,OAAO,GAAGC,GAAG,IAAIJ,QAAQ,CAACpG,KAAK,CAACwG,GAAG,CAAC;MAC3CH,MAAM,CAACI,OAAO,GAAGD,GAAG,IAAIJ,QAAQ,CAACpG,KAAK,CAACwG,GAAG,CAAC;MAC3CH,MAAM,CAACK,MAAM,GAAIC,EAAE,IAAKC,UAAU,CAAC,MAAM;QACrCR,QAAQ,CAACrC,IAAI,CAAC4C,EAAE,CAAC;QACjBP,QAAQ,CAACpC,QAAQ,CAAC,CAAC;MACvB,CAAC,CAAC;MACFqC,MAAM,CAACQ,aAAa,CAACzC,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CACG7C,IAAI,CAAC1L,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAACkJ,QAAQ,CAAC,CAAC,CACvC0C,SAAS,CAAEmE,SAAS,IAAK;MAC1B,MAAMC,eAAe,GAAGD,SAAS,CAACxB,MAAM,CAAC0B,MAAM;MAC/C,IAAI,CAACC,SAAS,CAAC;QACXlB,IAAI,EAAEJ,QAAQ;QACdD,IAAI,EAAEtB,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI;QACxBM,IAAI,EAAE,IAAI,CAAC1N,MAAM,CAAC0N,IAAI,IAAI5B,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAACS,IAAI;QAC5Ce;MACJ,CAAC,CAAC;MACF,IAAI,CAAC9H,EAAE,CAACiI,YAAY,CAAC,CAAC;IAC1B,CAAC,EAAE,MAAM;MACL,MAAMpB,SAAS,GAAG;QACdC,IAAI,EAAEJ,QAAQ;QACdD,IAAI,EAAED,QAAQ;QACdzF,KAAK,EAAEnB,eAAe,CAACsI,KAAK;QAC5BC,QAAQ,EAAE,+BAA+B;QACzCpB,IAAI,EAAE5B,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAACS;MACxB,CAAC;MACD,IAAI,CAACE,KAAK,CAAC,CAAC;MACZ,IAAI,CAAClG,KAAK,CAACmG,IAAI,CAACL,SAAS,CAAC;IAC9B,CAAC,CAAC;EACN;EACA;EACAxC,QAAQA,CAACoC,IAAI,EAAE2B,UAAU,EAAE;IACvB;IACA,MAAMC,OAAO,GAAG5B,IAAI,IAAI,IAAI,CAACnC,QAAQ,IAAImC,IAAI,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,CAACnC,QAAQ;IACzE;IACA,MAAMgE,OAAO,GAAG7B,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,CAACtC,KAAK,IAAIkE,OAAO,KAAK,IAAI,CAAClE,KAAK;IAC7E,IAAI,CAACC,MAAM,GAAGqC,IAAI;IAClB,IAAI,CAAC6B,OAAO,EAAE;MACV;IACJ;IACA,IAAI,CAAC/F,SAAS,GAAG8F,OAAO;IACxB,IAAI,CAACE,oBAAoB,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACtC,QAAQ,EAAE;MACf,IAAIqC,OAAO,EAAE;QACT,MAAME,cAAc,GAAG;UAAE,GAAG,IAAI,CAACnI;QAAS,CAAC;QAC3C,IAAI,CAACkB,MAAM,GAAG;UACVC,CAAC,EAAEgH,cAAc,CAAChH,CAAC;UACnBC,CAAC,EAAE+G,cAAc,CAAC/G,CAAC;UACnBC,IAAI,EAAE8G,cAAc,CAAC7G,EAAE;UACvBC,GAAG,EAAE4G,cAAc,CAAC3G;QACxB,CAAC;QACD,IAAI,CAACsB,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAACsF,oBAAoB,CAAC,CAAC;MAC/B,CAAC,MACI;QACD;MACJ;IACJ,CAAC,MACI,IAAI,IAAI,CAACnE,QAAQ,EAAE;MACpB,IAAI,CAACnB,oBAAoB,CAAC;QACtB,GAAG,IAAI,CAACuF,gBAAgB,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,MACI;MACD;IACJ;IACA,IAAI,CAACnI,WAAW,CAAC2G,IAAI,CAACT,IAAI,CAAC;IAC3B,IAAI,CAAC2B,UAAU,EAAE;MACb,IAAI,CAAC9E,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAoF,gBAAgBA,CAAA,EAAG;IACf,MAAM9J,IAAI,GAAG,IAAI,CAACmB,WAAW,CAACsC,aAAa;IAC3C,MAAM1G,GAAG,GAAG,IAAI,CAACyG,UAAU,CAACC,aAAa;IACzC,MAAMb,CAAC,GAAG,CAAC5C,IAAI,CAAC+J,WAAW,GAAIhN,GAAG,CAAC/C,KAAM,IAAI,CAAC;IAC9C,MAAM6I,CAAC,GAAG,CAAC7C,IAAI,CAACgK,YAAY,GAAIjN,GAAG,CAAC9C,MAAO,IAAI,CAAC;IAChD,OAAO;MACH2I,CAAC;MACDC;IACJ,CAAC;EACL;EACA;AACJ;AACA;EACIoH,WAAWA,CAAA,EAAG;IACV,MAAMC,SAAS,GAAG,IAAI,CAAC/I,WAAW,CAACsC,aAAa;IAChD,MAAMpF,GAAG,GAAG;MACRrE,KAAK,EAAEkQ,SAAS,CAACH,WAAW;MAC5B9P,MAAM,EAAEiQ,SAAS,CAACF;IACtB,CAAC;IACD,MAAM;MAAEhQ,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACsM,IAAI;IACnC,MAAMb,QAAQ,GAAG;MACb1L,KAAK,EAAEqE,GAAG,CAACrE,KAAK,GAAGA,KAAK;MACxBC,MAAM,EAAEoE,GAAG,CAACpE,MAAM,GAAGA;IACzB,CAAC;IACD,MAAMkP,MAAM,GAAG3M,IAAI,CAAC2N,GAAG,CAACzE,QAAQ,CAAC1L,KAAK,EAAE0L,QAAQ,CAACzL,MAAM,CAAC;IACxD,IAAI,CAACwL,QAAQ,CAAC0D,MAAM,CAAC;EACzB;EACAiB,GAAGA,CAAA,EAAG;IACF,IAAI,CAAC3E,QAAQ,CAAC,IAAI,CAACC,QAAQ,CAAC;EAChC;EACA;AACJ;AACA;AACA;EACImE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACtH,UAAU,GAAG,IAAI;IACtB,IAAI,CAACY,kBAAkB,GAAG;MACtBP,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACP,CAAC;IACD,IAAI,CAACU,YAAY,CAAC;MACd8G,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVnC,IAAI,EAAE,GAAG;MACT9E,cAAc,EAAEA,CAAA,KAAM,CAAE;IAC5B,CAAC,CAAC;IACF,IAAI,CAACd,UAAU,GAAG,KAAK;IACvB,IAAI,CAACY,kBAAkB,GAAG,IAAI;EAClC;EACAoH,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACnJ,EAAE,CAACiI,YAAY,CAAC,CAAC;EAC1B;EACA/B,cAAcA,CAACkD,OAAO,EAAEC,OAAO,EAAE;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAAC3D,SAAS,CAAC,CAAC;IACjC,MAAM4D,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGF,QAAQ,CAAC3Q,KAAK,GAAG0Q,QAAQ,CAAC1Q,KAAK,GAC3C0Q,QAAQ,CAAC1Q,KAAK,GACd2Q,QAAQ,CAAC3Q,KAAK;IACpB,MAAM8Q,UAAU,GAAGH,QAAQ,CAAC1Q,MAAM,GAAGyQ,QAAQ,CAACzQ,MAAM,GAC9CyQ,QAAQ,CAACzQ,MAAM,GACf0Q,QAAQ,CAAC1Q,MAAM;IACrB,IAAI2I,CAAC,EAAEC,CAAC;IACR,IAAI2H,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;MACpCD,OAAO,GAAG,IAAI,CAAC/I,QAAQ,CAACsB,EAAE;MAC1B0H,OAAO,GAAG,IAAI,CAAChJ,QAAQ,CAACwB,EAAE;IAC9B;IACAL,CAAC,GAAI+H,QAAQ,CAAC7H,IAAI,GAAG4H,QAAQ,CAAC5H,IAAK;IACnCD,CAAC,GAAI8H,QAAQ,CAAC3H,GAAG,GAAG0H,QAAQ,CAAC1H,GAAI;IACjCJ,CAAC,IAAK4H,OAAO,GAAIK,SAAS,GAAG,CAAG;IAChChI,CAAC,IAAK4H,OAAO,GAAIK,UAAU,GAAG,CAAG;IACjC,IAAI,CAACvG,oBAAoB,CAAC;MACtB3B,CAAC;MAAEC;IACP,CAAC,CAAC;EACN;EACAkI,SAASA,CAAA,EAAG;IACR,IAAI,CAACrG,eAAe,CAAC,CAAC;EAC1B;EACAA,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjK,MAAM,CAACuQ,QAAQ,EAAE;MACtB,IAAI,CAACC,IAAI,CAAC,CAAC;IACf;EACJ;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM3F,KAAK,GAAG,IAAI,CAAC5B,SAAS,GAAG,GAAG;IAClC,IAAI4B,KAAK,GAAG,IAAI,CAACG,QAAQ,IAAIH,KAAK,IAAI,IAAI,CAAC4F,SAAS,EAAE;MAClD,IAAI,CAAC1F,QAAQ,CAACF,KAAK,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC0F,SAAS,CAAC;IACjC;EACJ;EACA;EACA9C,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACO,KAAK,GAAG,EAAE;MACpC,IAAI,CAACP,oBAAoB,GAAG,IAAI;IACpC;IACA,IAAI,IAAI,CAACH,QAAQ,EAAE;MACf,IAAI,CAAC5F,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAACkB,MAAM,GAAGyI,SAAS;MACvB,IAAI,CAAC7F,KAAK,GAAG6F,SAAS;MACtB,IAAI,CAACzH,SAAS,GAAGyH,SAAS;MAC1B,IAAI,CAAC1J,SAAS,GAAG,CAAC;MAClB,IAAI,CAACiE,SAAS,GAAGyF,SAAS;MAC1B,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAAChE,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACiE,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,kBAAkB,GAAGH,SAAS;MACnC,IAAI,CAAC3Q,MAAM,GAAG,IAAI,CAACyK,cAAc;MACjC,MAAM7I,MAAM,GAAG,IAAI,CAACmH,UAAU,CAACC,aAAa;MAC5CpH,MAAM,CAACrC,KAAK,GAAG,CAAC;MAChBqC,MAAM,CAACpC,MAAM,GAAG,CAAC;MACjB,IAAI,CAACiI,OAAO,CAACoG,IAAI,CAAC,IAAI,CAAC;MACvB,IAAI,CAAClH,EAAE,CAACiI,YAAY,CAAC,CAAC;IAC1B;EACJ;EACA;EACAmC,OAAOA,CAAA,EAAG;IACN,MAAMjG,KAAK,GAAG,IAAI,CAAC5B,SAAS,GAAG,GAAG;IAClC,IAAI4B,KAAK,GAAG,IAAI,CAACG,QAAQ,IAAIH,KAAK,IAAI,IAAI,CAAC4F,SAAS,EAAE;MAClD,IAAI,CAAC1F,QAAQ,CAACF,KAAK,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAAC6E,GAAG,CAAC,CAAC;IACd;EACJ;EACAqB,MAAMA,CAAA,EAAG;IACL,MAAM5E,SAAS,GAAG;MACd,GAAG,IAAI,CAACiD,gBAAgB,CAAC;IAC7B,CAAC;IACD,IAAI,CAACvF,oBAAoB,CAACsC,SAAS,CAAC;IACpC,IAAI,CAACnC,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACI0E,SAASA,CAAC3O,MAAM,EAAE0F,EAAE,EAAE;IAClB,IAAI,CAACkI,KAAK,CAAC,CAAC;IACZ,MAAMrD,OAAO,GAAG,IAAI,CAACuG,kBAAkB,GAAG,OAAO9Q,MAAM,KAAK,QAAQ,GAC9D;MAAEyO,eAAe,EAAEzO;IAAO,CAAC,GAC3B;MAAE,GAAGA;IAAO,CAAC;IACnB,IAAIiR,GAAG,GAAG1G,OAAO,CAACkE,eAAe;IACjC,IAAI,CAAC/D,iBAAiB,GAAG,IAAI,CAACD,cAAc,CAAClL,KAAK;IAClD,IAAI,CAACoL,kBAAkB,GAAG,IAAI,CAACF,cAAc,CAACjL,MAAM;IACpD,IAAI+K,OAAO,CAAC6F,SAAS,IAAI7F,OAAO,CAAC8F,UAAU,EAAE;MACzC,IAAI,CAACrQ,MAAM,CAACT,KAAK,GAAGgL,OAAO,CAAC6F,SAAS;MACrC,IAAI,CAACpQ,MAAM,CAACR,MAAM,GAAG+K,OAAO,CAAC8F,UAAU;IAC3C;IACAY,GAAG,GAAGC,YAAY,CAACD,GAAG,CAAC;IACvB,MAAM3O,GAAG,GAAG6O,aAAa,CAACF,GAAG,CAAC;IAC9B,MAAMzD,SAAS,GAAG;MAAE,GAAGjD;IAAQ,CAAC;IAChC,IAAI/L,UAAU,CAACsP,QAAQ,IAAI;MACvBxL,GAAG,CAAC2L,OAAO,GAAGC,GAAG,IAAIJ,QAAQ,CAACpG,KAAK,CAACwG,GAAG,CAAC;MACxC5L,GAAG,CAAC6L,OAAO,GAAGD,GAAG,IAAIJ,QAAQ,CAACpG,KAAK,CAACwG,GAAG,CAAC;MACxC5L,GAAG,CAAC8L,MAAM,GAAG,MAAMN,QAAQ,CAACrC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC,CACGrB,IAAI,CAAC1L,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAACkJ,QAAQ,CAAC,CAAC,CACvC0C,SAAS,CAAC,MAAM;MACjB,IAAI,CAACuB,UAAU,CAACtJ,GAAG,CAAC;MACpB,IAAI,CAACsO,YAAY,GAAG,IAAI;MACxB,IAAI,CAACtJ,WAAW,CAACuG,IAAI,CAACL,SAAS,CAAC;MAChC,IAAI,CAAC7G,EAAE,CAACiI,YAAY,CAAC,CAAC;MACtB,IAAI,CAAChI,OAAO,CAACwE,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACxE,OAAO,CACPwK,QAAQ,CACRC,YAAY,CAAC,CAAC,CACdjH,IAAI,CAAC1L,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAACkJ,QAAQ,CAAC,CAAC,CACvC0C,SAAS,CAAC,MAAMiE,UAAU,CAAC,MAAM,IAAI,CAAC1H,OAAO,CAACqB,GAAG,CAAC,MAAM,IAAI,CAACqJ,YAAY,CAAC9D,SAAS,EAAE9H,EAAE,CAAC,CAAC,CAAC,CAAC;MACpG,CAAC,CAAC;IACN,CAAC,EAAE,MAAM;MACL,MAAMgC,KAAK,GAAG;QACV+F,IAAI,EAAElD,OAAO,CAACkD,IAAI;QAClB/F,KAAK,EAAEnB,eAAe,CAACgL,IAAI;QAC3B7D,IAAI,EAAEnD,OAAO,CAACmD,IAAI;QAClBN,IAAI,EAAE7C,OAAO,CAAC6C;MAClB,CAAC;MACD,IAAI,CAAC1F,KAAK,CAACmG,IAAI,CAACnG,KAAK,CAAC;IAC1B,CAAC,CAAC;EACN;EACAoF,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACvC,OAAO,CAACiH,cAAc,EAAE;MAC9B;IACJ;IACA,MAAMnF,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,MAAM4D,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACxC,MAAMlK,QAAQ,GAAG,IAAI,CAACjG,MAAM,CAACiG,QAAQ,IAAI,CAAC;IAC1C,MAAMC,SAAS,GAAG,IAAI,CAAClG,MAAM,CAACkG,SAAS,IAAI,CAAC;IAC5C,IAAI,EAAEgK,QAAQ,CAAC3Q,KAAK,GAAG8M,QAAQ,CAAC9M,KAAK,IAC9B2Q,QAAQ,CAAC1Q,MAAM,GAAG6M,QAAQ,CAAC7M,MAAM,IACjC0Q,QAAQ,CAAC3Q,KAAK,GAAG,IAAI,CAACmL,iBAAiB,IACvCwF,QAAQ,CAAC1Q,MAAM,GAAG,IAAI,CAACmL,kBAAkB,CAAC,EAAE;MAC/C;IACJ;IACA,MAAM8G,aAAa,GAAG1P,IAAI,CAAC2N,GAAG,CAAC,IAAI,CAAC1P,MAAM,CAACT,KAAK,EAAE0G,QAAQ,CAAC;IAC3D,MAAMyL,YAAY,GAAG3P,IAAI,CAAC2N,GAAG,CAACrD,QAAQ,CAAC9M,KAAK,EAAE0G,QAAQ,CAAC;IACvD,MAAM0L,OAAO,GAAG5P,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAAC2N,GAAG,CAACrD,QAAQ,CAAC9M,KAAK,EAAE0G,QAAQ,CAAC,EAAElE,IAAI,CAAC2N,GAAG,CAACrD,QAAQ,CAAC7M,MAAM,EAAE0G,SAAS,CAAC,CAAC;IAClG,MAAM0L,YAAY,GAAG,IAAI,CAAC1I,SAAS;IACnC,IAAI2I,QAAQ,GAAG,CAAC;IAChB,MAAMC,SAAS,GAAG,IAAI,CAAC9R,MAAM,CAACC,KAAK;IACnC,IAAI6R,SAAS,EAAE;MACX,IAAI,CAAC9R,MAAM,CAACT,KAAK,GAAG,IAAI,CAACS,MAAM,CAACR,MAAM,GAAGmS,OAAO;IACpD,CAAC,MACI;MACD,IAAIF,aAAa,KAAKvB,QAAQ,CAAC3Q,KAAK,EAAE;QAClC,IAAImS,YAAY,GAAG,IAAI,CAAChH,iBAAiB,EAAE;UACvC,IAAI,CAAC1K,MAAM,CAACT,KAAK,GAAG,IAAI,CAACmL,iBAAiB;UAC1C,IAAI,CAAC1K,MAAM,CAACR,MAAM,GAAI,IAAI,CAACkL,iBAAiB,GAAGwF,QAAQ,CAAC1Q,MAAM,GAAI0Q,QAAQ,CAAC3Q,KAAK;UAChFsS,QAAQ,GAAID,YAAY,GAAG,IAAI,CAAClH,iBAAiB,GAAIwF,QAAQ,CAAC3Q,KAAK;QACvE,CAAC,MACI;UACD,IAAI,CAACS,MAAM,CAACT,KAAK,GAAGmS,YAAY;UAChC,IAAI,CAAC1R,MAAM,CAACR,MAAM,GAAIkS,YAAY,GAAGxB,QAAQ,CAAC1Q,MAAM,GAAI0Q,QAAQ,CAAC3Q,KAAK;UACtEsS,QAAQ,GAAID,YAAY,GAAGF,YAAY,GAAIxB,QAAQ,CAAC3Q,KAAK;QAC7D;QACA,IAAI,CAAC0M,eAAe,CAAC,CAAC;QACtB,IAAI,CAACC,eAAe,CAAC,CAAC;QACtB,IAAI,CAAClB,QAAQ,CAAC6G,QAAQ,EAAE,IAAI,CAAC;QAC7B,IAAI,CAAC/B,aAAa,CAAC,CAAC;MACxB;IACJ;EACJ;EACAZ,oBAAoBA,CAAA,EAAG;IACnB,MAAMpE,KAAK,GAAG,IAAI,CAAC5B,SAAS,IAAI,IAAI,CAAClJ,MAAM,CAACT,KAAK,GAAG,IAAI,CAACmL,iBAAiB,CAAC;IAC3E,IAAI,CAACqH,cAAc,GAAGjH,KAAK;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkH,WAAWA,CAACf,GAAG,EAAEvL,EAAE,EAAE;IACjB,IAAI,CAACiJ,SAAS,CAACsC,GAAG,EAAEvL,EAAE,CAAC;EAC3B;EACA4L,YAAYA,CAAC9D,SAAS,EAAE9H,EAAE,EAAE;IACxB,MAAMuM,UAAU,GAAG,IAAI,CAACnB,kBAAkB;IAC1C,IAAI,CAAC7E,eAAe,CAAC,IAAI,CAAClD,UAAU,CAACC,aAAa,CAAC;IACnD,IAAI,CAACkD,eAAe,CAAC,CAAC;IACtB,IAAI,CAACU,QAAQ,GAAG,KAAK;IACrB,IAAIlH,EAAE,EAAE;MACJA,EAAE,CAAC,CAAC;IACR,CAAC,MACI;MACD,IAAIuM,UAAU,CAACnH,KAAK,EAAE;QAClB,IAAI,CAACE,QAAQ,CAACiH,UAAU,CAACnH,KAAK,EAAE,IAAI,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC;MACtC;MACA,IAAI,CAACiH,MAAM,CAACD,UAAU,CAACE,QAAQ,IAAI,CAAC,CAAC;MACrC,IAAI,CAACrF,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACgD,aAAa,CAAC,CAAC;MACpB,IAAI,CAAClJ,OAAO,CAACwE,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACxE,OAAO,CACPwK,QAAQ,CACRC,YAAY,CAAC,CAAC,CACdjH,IAAI,CAAC1L,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,IAAI,CAACkJ,QAAQ,CAAC,CAAC,CACvC0C,SAAS,CAAC,MAAM;UACjB,IAAI4H,UAAU,CAAClC,OAAO,IAAI,IAAI,IAAIkC,UAAU,CAACjC,OAAO,IAAI,IAAI,EAAE;YAC1D,IAAI,CAACnD,cAAc,CAACoF,UAAU,CAAClC,OAAO,EAAEkC,UAAU,CAACjC,OAAO,CAAC;UAC/D;UACA,IAAI,CAAClD,mBAAmB,CAAC,CAAC;UAC1B,IAAI,CAACF,QAAQ,GAAG,IAAI;UACpB,IAAI,CAAC3C,eAAe,CAAC,CAAC;UACtB,IAAI,CAACrD,OAAO,CAACqB,GAAG,CAAC,MAAM;YACnB,IAAI,CAAC6H,aAAa,CAAC,CAAC;YACpB,IAAI,CAACvI,KAAK,CAACsG,IAAI,CAACL,SAAS,CAAC;YAC1B;YACA,IAAI,CAACnG,MAAM,CAACwG,IAAI,CAACL,SAAS,CAAC;UAC/B,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA0E,MAAMA,CAACE,OAAO,EAAE;IACZ,IAAIC,YAAY,GAAGC,iBAAiB,CAACF,OAAO,CAAC;IAC7C;IACA,IAAIC,YAAY,GAAG,CAAC,EAAE;MAClBA,YAAY,IAAI,GAAG;IACvB;IACA,MAAME,WAAW,GAAGD,iBAAiB,CAAC,CAAC,IAAI,CAACrL,SAAS,IAAI,CAAC,IAAIoL,YAAY,CAAC;IAC3E,IAAIE,WAAW,KAAK,IAAI,CAACtL,SAAS,EAAE;MAChC;IACJ;IACA,MAAMuL,UAAU,GAAGH,YAAY,GAAGtQ,IAAI,CAAC0Q,EAAE,GAAG,GAAG;IAC/C,MAAM7Q,MAAM,GAAG,IAAI,CAACmH,UAAU,CAACC,aAAa;IAC5C,MAAM0J,UAAU,GAAGC,eAAe,CAAC/Q,MAAM,CAAC;IAC1C,MAAMjC,GAAG,GAAGiC,MAAM,CAACS,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAAC4E,SAAS,GAAGsL,WAAW;IAC5B;IACA5S,GAAG,CAACoM,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE2G,UAAU,CAACnT,KAAK,EAAEmT,UAAU,CAAClT,MAAM,CAAC;IACxD;IACA,MAAM+M,SAAS,GAAI,UAAS8F,YAAa,cAAa,CAAC,GAAG,IAAI,CAACnJ,SAAU,GAAE;IAC3E,MAAMsD,eAAe,GAAI,GAAE,IAAI,CAACxF,QAAQ,CAACsB,EAAG,MAAK,IAAI,CAACtB,QAAQ,CAACwB,EAAG,MAAK;IACvE5G,MAAM,CAACgR,KAAK,CAACrG,SAAS,GAAGA,SAAS;IAClC;IACA3K,MAAM,CAACgR,KAAK,CAACC,eAAe,GAAGtG,SAAS;IACxC3K,MAAM,CAACgR,KAAK,CAACpG,eAAe,GAAGA,eAAe;IAC9C;IACA5K,MAAM,CAACgR,KAAK,CAACE,qBAAqB,GAAGtG,eAAe;IACpD,MAAM;MAAEnE,IAAI;MAAEE;IAAI,CAAC,GAAG3G,MAAM,CAACmR,qBAAqB,CAAC,CAAC;IACpD;IACA,MAAMC,UAAU,GAAGpR,MAAM,CAACmR,qBAAqB,CAAC,CAAC;IACjD;IACAnR,MAAM,CAACqR,eAAe,CAAC,OAAO,CAAC;IAC/B;IACA,MAAM7O,CAAC,GAAG4O,UAAU,CAACzT,KAAK;IAC1B,MAAM2T,CAAC,GAAGF,UAAU,CAACxT,MAAM;IAC3BG,GAAG,CAACiC,MAAM,CAACrC,KAAK,GAAG6E,CAAC;IACpBzE,GAAG,CAACiC,MAAM,CAACpC,MAAM,GAAG0T,CAAC;IACrB;IACAvT,GAAG,CAACoM,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE3H,CAAC,EAAE8O,CAAC,CAAC;IACzB;IACAvT,GAAG,CAACwT,SAAS,CAAC/O,CAAC,GAAG,CAAC,EAAE8O,CAAC,GAAG,CAAC,CAAC;IAC3BvT,GAAG,CAACuS,MAAM,CAACM,UAAU,CAAC;IACtB7S,GAAG,CAACqM,SAAS,CAAC0G,UAAU,EAAE,CAACA,UAAU,CAACnT,KAAK,GAAG,CAAC,EAAE,CAACmT,UAAU,CAAClT,MAAM,GAAG,CAAC,CAAC;IACxE;IACA,IAAI,CAACyM,eAAe,CAACrK,MAAM,CAAC;IAC5B,IAAI,CAACsK,eAAe,CAAC,CAAC;IACtB;IACA,IAAI,IAAI,CAACpB,KAAK,GAAG,IAAI,CAACG,QAAQ,EAAE;MAC5B,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;IAC1B,CAAC,CAAC;IACF,MAAMqB,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAI,CAACxC,oBAAoB,CAAC;MACtB3B,CAAC,EAAGE,IAAI,GAAGgE,QAAQ,CAAChE,IAAK;MACzBD,CAAC,EAAGG,GAAG,GAAG8D,QAAQ,CAAC9D;IACvB,CAAC,CAAC;IACF;IACA,MAAM4G,cAAc,GAAG;MAAE,GAAG,IAAI,CAACnI;IAAS,CAAC;IAC3C,IAAI,CAACkB,MAAM,GAAG;MACVC,CAAC,EAAEgH,cAAc,CAAChH,CAAC;MACnBC,CAAC,EAAE+G,cAAc,CAAC/G,CAAC;MACnBC,IAAI,EAAE8G,cAAc,CAAC7G,EAAE;MACvBC,GAAG,EAAE4G,cAAc,CAAC3G;IACxB,CAAC;IACD,IAAI,CAACsB,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACsF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACnF,eAAe,CAAC,CAAC;EAC1B;EACAgC,eAAeA,CAACrK,MAAM,EAAE;IACpB,IAAI,CAACA,MAAM,EAAE;MACTA,MAAM,GAAG,IAAI,CAACmH,UAAU,CAACC,aAAa;IAC1C;IACA,MAAMhJ,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMiL,QAAQ,GAAG,CAACjL,MAAM,CAACwJ,YAAY,GAAGzH,IAAI,CAAC6B,GAAG,GAAG7B,IAAI,CAAC2N,GAAG,EAAE1P,MAAM,CAACT,KAAK,GAAGqC,MAAM,CAACrC,KAAK,EAAES,MAAM,CAACR,MAAM,GAAGoC,MAAM,CAACpC,MAAM,CAAC;IACxH,IAAI,CAAC0L,SAAS,GAAGD,QAAQ;IACzB,IAAI,CAAC9D,cAAc,CAAC0G,IAAI,CAAC5C,QAAQ,CAAC;EACtC;EACAiB,eAAeA,CAAA,EAAG;IACd,MAAMkH,QAAQ,GAAI,IAAI,CAACpT,MAAM,CAACT,KAAK,GAAG,IAAI,CAACmL,iBAAkB;IAC7D,IAAI,CAACgG,SAAS,GAAG0C,QAAQ;IACzB,IAAI,CAAChM,cAAc,CAACyG,IAAI,CAACuF,QAAQ,CAAC;EACtC;EACA;AACJ;AACA;EACI5C,IAAIA,CAACxQ,MAAM,EAAE;IACT,MAAMqT,SAAS,GAAGrT,MAAM,GAClB5B,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC4B,MAAM,IAAI,IAAI+F,gBAAgB,CAAC,CAAC,EAAE/F,MAAM,CAAC,GAAG,IAAI,CAACA,MAAM;IAChF,MAAMwN,SAAS,GAAG,IAAI,CAAC8F,QAAQ,CAACD,SAAS,CAAC;IAC1C,IAAI,CAAC1M,EAAE,CAACiI,YAAY,CAAC,CAAC;IACtB,OAAOpB,SAAS;EACpB;EACA;AACJ;AACA;EACI8F,QAAQA,CAACC,QAAQ,EAAE;IACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACtD,MAAMxD,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACxC,MAAM6C,UAAU,GAAG,IAAI,CAACW,WAAW,CAAC,CAAC;IACrC,MAAM1K,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMb,IAAI,GAAG,CAAC6H,QAAQ,CAAC7H,IAAI,GAAG2K,UAAU,CAAC3K,IAAI,IAAIY,QAAQ;IACzD,MAAMV,GAAG,GAAG,CAAC2H,QAAQ,CAAC3H,GAAG,GAAGyK,UAAU,CAACzK,GAAG,IAAIU,QAAQ;IACtD,MAAM;MAAE7C;IAAO,CAAC,GAAGmN,QAAQ;IAC3B,MAAMK,sBAAsB,GAAG,IAAI,CAAC9C,kBAAkB;IACtD,MAAMhL,IAAI,GAAG;MACTvG,KAAK,EAAEgU,QAAQ,CAAChU,KAAK;MACrBC,MAAM,EAAE+T,QAAQ,CAAC/T;IACrB,CAAC;IACDgU,aAAa,CAACjU,KAAK,GAAGuG,IAAI,CAACvG,KAAK,GAAG0J,QAAQ;IAC3CuK,aAAa,CAAChU,MAAM,GAAGsG,IAAI,CAACtG,MAAM,GAAGyJ,QAAQ;IAC7C,MAAMtJ,GAAG,GAAG6T,aAAa,CAACnR,UAAU,CAAC,IAAI,CAAC;IAC1C,IAAIkR,QAAQ,CAAC1N,IAAI,EAAE;MACflG,GAAG,CAACkU,SAAS,GAAGN,QAAQ,CAAC1N,IAAI;MAC7BlG,GAAG,CAACmU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEN,aAAa,CAACjU,KAAK,EAAEiU,aAAa,CAAChU,MAAM,CAAC;IACjE;IACAG,GAAG,CAACqM,SAAS,CAAC,IAAI,CAACjD,UAAU,CAACC,aAAa,EAAE,CAAEX,IAAK,EAAE,CAAEE,GAAI,CAAC;IAC7D,MAAMmG,MAAM,GAAG8E,aAAa;IAC5B,IAAID,QAAQ,CAACnN,MAAM,KAAKC,aAAa,CAACC,OAAO,EAAE;MAC3C3E,YAAY,CAAC+M,MAAM,EAAE,IAAI,CAACjE,cAAc,CAAClL,KAAK,EAAE,IAAI,CAACkL,cAAc,CAACjL,MAAM,CAAC;IAC/E,CAAC,MACI,IAAI,OAAO4G,MAAM,KAAK,QAAQ,EAAE;MACjC,IAAIA,MAAM,CAAC7G,KAAK,IAAI6G,MAAM,CAAC5G,MAAM,EAAE;QAC/BmC,YAAY,CAAC+M,MAAM,EAAEtI,MAAM,CAAC7G,KAAK,EAAE6G,MAAM,CAAC5G,MAAM,CAAC;MACrD,CAAC,MACI,IAAI4G,MAAM,CAAC7G,KAAK,EAAE;QACnB,MAAMwU,SAAS,GAAGjO,IAAI,CAACtG,MAAM,GAAG4G,MAAM,CAAC7G,KAAK,GAAGuG,IAAI,CAACvG,KAAK;QACzDoC,YAAY,CAAC+M,MAAM,EAAEtI,MAAM,CAAC7G,KAAK,EAAEwU,SAAS,CAAC;MACjD,CAAC,MACI,IAAI3N,MAAM,CAAC5G,MAAM,EAAE;QACpB,MAAMwU,QAAQ,GAAGlO,IAAI,CAACvG,KAAK,GAAG6G,MAAM,CAAC5G,MAAM,GAAGsG,IAAI,CAACtG,MAAM;QACzDmC,YAAY,CAAC+M,MAAM,EAAEsF,QAAQ,EAAE5N,MAAM,CAAC5G,MAAM,CAAC;MACjD;IACJ;IACA,MAAMkO,IAAI,GAAGkG,sBAAsB,CAACnF,eAAe,CAACwF,UAAU,CAAC,MAAM,CAAC,GAChEL,sBAAsB,CAAClG,IAAI,IAAI6F,QAAQ,CAAC7F,IAAI,GAC5C6F,QAAQ,CAAC7F,IAAI,IAAIkG,sBAAsB,CAAClG,IAAI;IAClD,MAAMwG,OAAO,GAAGxF,MAAM,CAACyF,SAAS,CAACzG,IAAI,CAAC;IACtC,MAAMF,SAAS,GAAG;MACd0G,OAAO;MACPxG,IAAI;MACJD,IAAI,EAAEmG,sBAAsB,CAACnG,IAAI;MACjC2C,SAAS,EAAE,IAAI,CAAC1F,iBAAiB;MACjC2F,UAAU,EAAE,IAAI,CAAC1F,kBAAkB;MACnCpL,KAAK,EAAEmP,MAAM,CAACnP,KAAK;MACnBC,MAAM,EAAEkP,MAAM,CAAClP,MAAM;MACrBiP,eAAe,EAAEmF,sBAAsB,CAACnF,eAAe;MACvD3D,KAAK,EAAE,IAAI,CAACiH,cAAc;MAC1BI,QAAQ,EAAE,IAAI,CAAClL,SAAS;MACxBoB,IAAI,EAAE,CAAC6H,QAAQ,CAAC7H,IAAI,GAAG2K,UAAU,CAAC3K,IAAI,IAAI,IAAI,CAACa,SAAS;MACxDX,GAAG,EAAE,CAAC2H,QAAQ,CAAC3H,GAAG,GAAGyK,UAAU,CAACzK,GAAG,IAAI,IAAI,CAACW,SAAS;MACrDkE,IAAI,EAAEwG,sBAAsB,CAACxG,IAAI;MACjC2C,OAAO,EAAE,IAAI,CAAC/I,QAAQ,CAACsB,EAAE;MACzB0H,OAAO,EAAE,IAAI,CAAChJ,QAAQ,CAACwB,EAAE;MACzB4L,QAAQ,EAAE;QACNjM,CAAC,EAAE,IAAI,CAACnB,QAAQ,CAACsB,EAAE;QACnBF,CAAC,EAAE,IAAI,CAACpB,QAAQ,CAACwB;MACrB;IACJ,CAAC;IACD,IAAI,CAACqI,SAAS,GAAG,IAAI;IACrB,IAAI,CAACrJ,OAAO,CAACqG,IAAI,CAACL,SAAS,CAAC;IAC5B,OAAOA,SAAS;EACpB;EACAlB,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC5F,WAAW,CAACsC,aAAa,CAAC+J,qBAAqB,CAAC,CAAC;EACjE;EACA5C,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACkE,QAAQ,CAACrL,aAAa,CAAC+J,qBAAqB,CAAC,CAAC;EAC9D;EACAY,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC5K,UAAU,CAACC,aAAa,CAAC+J,qBAAqB,CAAC,CAAC;EAChE;EACAlK,iBAAiBA,CAACyL,YAAY,EAAE;IAC5B,MAAMjJ,OAAO,GAAG,IAAI,CAACxE,SAAS;IAC9B,MAAM0N,OAAO,GAAGxM,YAAY,CAACuM,YAAY,CAAC;IAC1C,MAAME,aAAa,GAAGD,OAAO,GAAG,WAAW,GAAG,WAAW;IACzD,MAAME,YAAY,GAAGF,OAAO,GAAG,UAAU,GAAG,SAAS;IACrDlJ,OAAO,CAACE,gBAAgB,CAACiJ,aAAa,EAAE,IAAI,CAAC1L,YAAY,EAAEtE,kBAAkB,CAAC;IAC9E6G,OAAO,CAACE,gBAAgB,CAACkJ,YAAY,EAAE,IAAI,CAAC1K,UAAU,EAAEvF,kBAAkB,CAAC;IAC3E,IAAI+P,OAAO,EAAE;MACTlJ,OAAO,CAACE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACxB,UAAU,EAAEvF,kBAAkB,CAAC;IAChF;IACA,MAAMkQ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAE;MACzCA,MAAM,CAACnJ,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACrB,WAAW,CAAC;IACrD;EACJ;EACA;EACAF,mBAAmBA,CAAA,EAAG;IAClB,MAAMqB,OAAO,GAAG,IAAI,CAACxE,SAAS;IAC9BwE,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC7C,YAAY,EAAEtE,kBAAkB,CAAC;IAC/E6G,OAAO,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC5B,UAAU,EAAEvF,kBAAkB,CAAC;IAC3E6G,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC7C,YAAY,EAAEtE,kBAAkB,CAAC;IAC/E6G,OAAO,CAACM,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC5B,UAAU,EAAEvF,kBAAkB,CAAC;IAC5E6G,OAAO,CAACM,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC5B,UAAU,EAAEvF,kBAAkB,CAAC;IAC/E,MAAMkQ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAE;MACzCA,MAAM,CAAC/I,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACzB,WAAW,CAAC;IACxD;EACJ;EACA;EACAyK,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC9N,SAAS,CAAC+N,WAAW,IAAIF,MAAM;EAC/C;AACJ;AACAtP,cAAc,CAACC,CAAC,GAAG,gBAAgB;AACnCD,cAAc,CAACyP,IAAI,YAAAC,uBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyF3P,cAAc,EAAxB9H,EAAE,CAAA0X,iBAAA,CAAwChX,EAAE,CAACK,aAAa,GAA1Df,EAAE,CAAA0X,iBAAA,CAAqE1X,EAAE,CAAC2X,SAAS,GAAnF3X,EAAE,CAAA0X,iBAAA,CAA8F1X,EAAE,CAACE,UAAU,GAA7GF,EAAE,CAAA0X,iBAAA,CAAwH1X,EAAE,CAAC4X,iBAAiB,GAA9I5X,EAAE,CAAA0X,iBAAA,CAAyJ1X,EAAE,CAAC6X,MAAM,GAApK7X,EAAE,CAAA0X,iBAAA,CAA+KnW,QAAQ,GAAzLvB,EAAE,CAAA0X,iBAAA,CAAoMhW,EAAE,CAACoW,aAAa;AAAA,CAA4C;AACpWhQ,cAAc,CAACiQ,IAAI,kBAD+E/X,EAAE,CAAAgY,iBAAA;EAAA5H,IAAA,EACJtI,cAAc;EAAAmQ,SAAA;EAAAC,SAAA,WAAAC,qBAAA/V,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADZpC,EAAE,CAAAoY,WAAA,CAAAzW,GAAA;MAAF3B,EAAE,CAAAoY,WAAA,CAAAxW,GAAA,KAGwH1B,UAAU;MAHpIF,EAAE,CAAAoY,WAAA,CAAAvW,GAAA;IAAA;IAAA,IAAAO,EAAA;MAAA,IAAAiW,EAAA;MAAFrY,EAAE,CAAAsY,cAAA,CAAAD,EAAA,GAAFrY,EAAE,CAAAuY,WAAA,QAAAlW,GAAA,CAAA2L,aAAA,GAAAqK,EAAA,CAAAG,KAAA;MAAFxY,EAAE,CAAAsY,cAAA,CAAAD,EAAA,GAAFrY,EAAE,CAAAuY,WAAA,QAAAlW,GAAA,CAAA0U,QAAA,GAAAsB,EAAA,CAAAG,KAAA;MAAFxY,EAAE,CAAAsY,cAAA,CAAAD,EAAA,GAAFrY,EAAE,CAAAuY,WAAA,QAAAlW,GAAA,CAAAoJ,UAAA,GAAA4M,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAA/V,MAAA;IAAA8K,KAAA;IAAAD,WAAA;EAAA;EAAAmL,OAAA;IAAA9O,WAAA;IAAAC,cAAA;IAAAC,cAAA;IAAAC,MAAA;IAAAC,WAAA;IAAAC,KAAA;IAAAC,OAAA;IAAAC,OAAA;IAAAC,KAAA;EAAA;EAAAuO,QAAA,GAAF3Y,EAAE,CAAA4Y,kBAAA,CACsW,CAClc7X,aAAa,CAChB;EAAA8X,kBAAA,EAAA9U,GAAA;EAAA+U,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAA9W,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAH6FpC,EAAE,CAAAmZ,eAAA;MAAFnZ,EAAE,CAAAkD,cAAA,eAGkiB,CAAC;MAHriBlD,EAAE,CAAAmD,UAAA,yBAAAiW,mDAAA/V,MAAA;QAAA,OAGugBA,MAAA,CAAAiI,cAAA,CAAsB,CAAC;MAAA,CAAC,CAAC;MAHliBtL,EAAE,CAAAsC,SAAA,qBAGmkB,CAAC;MAHtkBtC,EAAE,CAAA0D,YAAA,CAG2kB,CAAC;MAH9kB1D,EAAE,CAAAqZ,UAAA,IAAAlX,yCAAA,4BAGw3B,CAAC;MAH33BnC,EAAE,CAAAqZ,UAAA,IAAAtW,qCAAA,gCAAF/C,EAAE,CAAAsZ,sBAGylC,CAAC;IAAA;IAAA,IAAAlX,EAAA;MAAA,MAAAmX,GAAA,GAH5lCvZ,EAAE,CAAAwZ,WAAA;MAAFxZ,EAAE,CAAAyC,UAAA,cAAAJ,GAAA,CAAAwB,OAAA,CAAAwE,YAGkf,CAAC;MAHrfrI,EAAE,CAAAyZ,SAAA,EAG4vB,CAAC;MAH/vBzZ,EAAE,CAAAyC,UAAA,SAAAJ,GAAA,CAAAiR,YAG4vB,CAAC,aAAAiG,GAAD,CAAC;IAAA;EAAA;EAAAG,YAAA,WAAAA,CAAA;IAAA,QAA0ZC,aAAa,EAAkLrY,EAAE,CAACsY,IAAI,EAAiHtY,EAAE,CAACuY,OAAO;EAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA,EAA0G;AACtqD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAJkGha,EAAE,CAAAia,iBAAA,CAIRnS,cAAc,EAAc,CAAC;IAC7GsI,IAAI,EAAEjQ,SAAS;IACf+Z,IAAI,EAAE,CAAC;MAAEH,eAAe,EAAE3Z,uBAAuB,CAAC+Z,MAAM;MAAEC,mBAAmB,EAAE,KAAK;MAAEC,QAAQ,EAAE,kCAAkC;MAAEC,SAAS,EAAE,CACnIvZ,aAAa,CAChB;MAAEkY,QAAQ,EAAE;IAAs1B,CAAC;EACh3B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7I,IAAI,EAAE1P,EAAE,CAACK;IAAc,CAAC,EAAE;MAAEqP,IAAI,EAAEpQ,EAAE,CAAC2X;IAAU,CAAC,EAAE;MAAEvH,IAAI,EAAEpQ,EAAE,CAACE;IAAW,CAAC,EAAE;MAAEkQ,IAAI,EAAEpQ,EAAE,CAAC4X;IAAkB,CAAC,EAAE;MAAExH,IAAI,EAAEpQ,EAAE,CAAC6X;IAAO,CAAC,EAAE;MAAEzH,IAAI,EAAEiD,SAAS;MAAEkH,UAAU,EAAE,CAAC;QAChMnK,IAAI,EAAE/P,MAAM;QACZ6Z,IAAI,EAAE,CAAC3Y,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE6O,IAAI,EAAE1O,EAAE,CAACoW;IAAc,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE9J,aAAa,EAAE,CAAC;MACxEoC,IAAI,EAAE9P,SAAS;MACf4Z,IAAI,EAAE,CAAC,eAAe,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEzD,QAAQ,EAAE,CAAC;MACX3G,IAAI,EAAE9P,SAAS;MACf4Z,IAAI,EAAE,CAAC,OAAO,EAAE;QACRO,IAAI,EAAEva;MACV,CAAC;IACT,CAAC,CAAC;IAAEuL,UAAU,EAAE,CAAC;MACb2E,IAAI,EAAE9P,SAAS;MACf4Z,IAAI,EAAE,CAAC,YAAY,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAE9X,MAAM,EAAE,CAAC;MACT0N,IAAI,EAAE7P;IACV,CAAC,CAAC;IAAEiN,KAAK,EAAE,CAAC;MACR4C,IAAI,EAAE7P;IACV,CAAC,CAAC;IAAEgN,WAAW,EAAE,CAAC;MACd6C,IAAI,EAAE7P;IACV,CAAC,CAAC;IAAEqJ,WAAW,EAAE,CAAC;MACdwG,IAAI,EAAE5P;IACV,CAAC,CAAC;IAAEqJ,cAAc,EAAE,CAAC;MACjBuG,IAAI,EAAE5P,MAAM;MACZ0Z,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEpQ,cAAc,EAAE,CAAC;MACjBsG,IAAI,EAAE5P,MAAM;MACZ0Z,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEnQ,MAAM,EAAE,CAAC;MACTqG,IAAI,EAAE5P;IACV,CAAC,CAAC;IAAEwJ,WAAW,EAAE,CAAC;MACdoG,IAAI,EAAE5P;IACV,CAAC,CAAC;IAAEyJ,KAAK,EAAE,CAAC;MACRmG,IAAI,EAAE5P;IACV,CAAC,CAAC;IAAE0J,OAAO,EAAE,CAAC;MACVkG,IAAI,EAAE5P;IACV,CAAC,CAAC;IAAE2J,OAAO,EAAE,CAAC;MACViG,IAAI,EAAE5P;IACV,CAAC,CAAC;IAAE4J,KAAK,EAAE,CAAC;MACRgG,IAAI,EAAE5P;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMmZ,aAAa,CAAC;EAChBjR,WAAWA,CAACQ,SAAS,EAAEE,WAAW,EAAEE,OAAO,EAAEoR,QAAQ,EAAEnR,SAAS,EAAE;IAC9D,IAAI,CAACL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoR,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC7W,OAAO,GAAG,IAAI,CAACqF,SAAS,CAACO,WAAW,CAAClC,MAAM,EAAE,MAAM,CAAC;IACzD,IAAI,CAAC+C,YAAY,GAAIC,KAAK,IAAK;MAC3B;MACA;MACA,IAAI,IAAI,CAACC,UAAU,IAAK,CAACC,YAAY,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAE,EAAE;QACjE;MACJ;MACAH,KAAK,CAACe,cAAc,CAAC,CAAC;MACtB,IAAI,CAAChC,OAAO,CAACqB,GAAG,CAAC,MAAM;QACnB,IAAI,CAACH,UAAU,GAAG,IAAI;QACtB,IAAI,CAACW,iBAAiB,GAAGZ,KAAK;QAC9B,IAAI,CAACa,kBAAkB,GAAGC,wBAAwB,CAACd,KAAK,CAAC;QACzD,IAAI,CAACoQ,cAAc,GAAG,IAAI,CAACD,QAAQ,CAAC7H,gBAAgB,CAAC,CAAC;QACtD,IAAI,CAAC+H,aAAa,GAAG,IAAI,CAACF,QAAQ,CAACrE,WAAW,CAAC,CAAC;QAChD9L,KAAK,CAACe,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,iBAAiB,CAAChB,KAAK,CAAC;MACjC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACiB,YAAY,GAAIjB,KAAK,IAAK;MAC3B,IAAI,IAAI,CAACC,UAAU,EAAE;QACjBD,KAAK,CAACe,cAAc,CAAC,CAAC;QACtB,IAAI,CAACH,iBAAiB,GAAGZ,KAAK;QAC9B,MAAMwD,OAAO,GAAG,IAAI,CAAC3E,WAAW,CAACsC,aAAa;QAC9C,MAAM;UAAEzJ,KAAK;UAAEC,MAAM;UAAEyG,QAAQ;UAAEC;QAAU,CAAC,GAAG,IAAI,CAAC8R,QAAQ,CAAChY,MAAM;QACnE,MAAMoJ,KAAK,GAAGT,wBAAwB,CAACd,KAAK,CAAC;QAC7C,MAAMwB,MAAM,GAAGD,KAAK,CAACjB,CAAC,GAAG,IAAI,CAACO,kBAAkB,CAACP,CAAC;QAClD,MAAMmB,MAAM,GAAGF,KAAK,CAAChB,CAAC,GAAG,IAAI,CAACM,kBAAkB,CAACN,CAAC;QAClD,MAAM+P,aAAa,GAAG,IAAI,CAACF,cAAc;QACzC,MAAMG,YAAY,GAAG,IAAI,CAACF,aAAa;QACvC,MAAMjY,KAAK,GAAG,IAAI,CAACA,KAAK;QACxB,MAAME,eAAe,GAAG,IAAI,CAAC6X,QAAQ,CAAChY,MAAM,CAACG,eAAe,IAAI0H,KAAK,CAACwQ,QAAQ;QAC9E,IAAIrE,QAAQ,GAAG,CAAC;QAChB,IAAID,SAAS,GAAG,CAAC;QACjB,MAAM1H,QAAQ,GAAG,IAAI,CAAC2L,QAAQ,CAAC1L,SAAS,CAAC,CAAC;QAC1C,IAAIrM,KAAK,EAAE;UACP;UACA,MAAMqY,OAAO,GAAK/Y,KAAK,GAAG,CAAC,GAAGwC,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,GAAIgF,MAAO;UACrD,MAAMkP,OAAO,GAAK/Y,MAAM,GAAG,CAAC,GAAGuC,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC,GAAIiF,MAAO;UACtD;UACA,MAAMkP,IAAI,GAAGzW,IAAI,CAACsC,IAAI,CAACiU,OAAO,IAAI,CAAC,GAAGC,OAAO,IAAI,CAAC,CAAC;UACnDvE,QAAQ,GAAGD,SAAS,GAAGyE,IAAI,GAAG,CAAC;QACnC,CAAC,MACI,IAAIrY,eAAe,EAAE;UACtB6T,QAAQ,GAAGzU,KAAK,GAAG8J,MAAM,GAAG,CAAC;UAC7B0K,SAAS,GAAGvU,MAAM,GAAG8J,MAAM,GAAG,CAAC;UAC/B,IAAI/J,KAAK,KAAKC,MAAM,EAAE;YAClB,IAAID,KAAK,GAAGC,MAAM,EAAE;cAChBuU,SAAS,GAAGvU,MAAM,IAAID,KAAK,GAAGyU,QAAQ,CAAC;YAC3C,CAAC,MACI,IAAIxU,MAAM,GAAGD,KAAK,EAAE;cACrByU,QAAQ,GAAGzU,KAAK,IAAIC,MAAM,GAAGuU,SAAS,CAAC;YAC3C;UACJ,CAAC,MACI;YACDC,QAAQ,GAAGD,SAAS,GAAGhS,IAAI,CAAC2N,GAAG,CAACsE,QAAQ,EAAED,SAAS,CAAC;UACxD;QACJ,CAAC,MACI;UACDC,QAAQ,GAAGzU,KAAK,GAAG8J,MAAM,GAAG,CAAC;UAC7B0K,SAAS,GAAGvU,MAAM,GAAG8J,MAAM,GAAG,CAAC;QACnC;QACA;QACA,IAAI0K,QAAQ,GAAG/N,QAAQ,EAAE;UACrB+N,QAAQ,GAAG/N,QAAQ;QACvB;QACA;QACA,IAAI8N,SAAS,GAAG7N,SAAS,EAAE;UACvB6N,SAAS,GAAG7N,SAAS;QACzB;QACA;QACA,MAAMuS,OAAO,GAAGN,aAAa,CAAChQ,CAAC,GAAGgQ,aAAa,CAAC5Y,KAAK,GAAG,CAAC;QACzD,MAAMmZ,OAAO,GAAGP,aAAa,CAAC/P,CAAC,GAAG+P,aAAa,CAAC3Y,MAAM,GAAG,CAAC;QAC1D,MAAMmZ,WAAW,GAAGP,YAAY,CAAChQ,CAAC,GAAGsQ,OAAO,GAAI3E,SAAS,GAAG,CAAE;QAC9D,MAAM6E,cAAc,GAAGF,OAAO,GAAI3E,SAAS,GAAG,CAAE,GAAGqE,YAAY,CAACS,MAAM;QACtE,MAAMC,mBAAmB,GAAG/W,IAAI,CAAC6B,GAAG,CAAC,CAAC8U,OAAO,GAAGN,YAAY,CAAChQ,CAAC,IAAI,CAAC,EAAE,CAACgQ,YAAY,CAACS,MAAM,GAAGH,OAAO,IAAI,CAAC,CAAC;QACzG,MAAMK,YAAY,GAAGX,YAAY,CAACjQ,CAAC,GAAGsQ,OAAO,GAAIzE,QAAQ,GAAG,CAAE;QAC9D,MAAMgF,aAAa,GAAGP,OAAO,GAAIzE,QAAQ,GAAG,CAAE,GAAGoE,YAAY,CAACa,KAAK;QACnE,MAAMC,kBAAkB,GAAGnX,IAAI,CAAC6B,GAAG,CAAC,CAAC6U,OAAO,GAAGL,YAAY,CAACjQ,CAAC,IAAI,CAAC,EAAE,CAACiQ,YAAY,CAACa,KAAK,GAAGR,OAAO,IAAI,CAAC,CAAC;QACvG,MAAMU,aAAa,GAAGpX,IAAI,CAAC6B,GAAG,CAACsV,kBAAkB,EAAEJ,mBAAmB,CAAC;QACvE,IAAI7Y,KAAK,EAAE;UACP,IAAI0Y,WAAW,IAAIC,cAAc,IAAIG,YAAY,IAAIC,aAAa,EAAE;YAChEjF,SAAS,GAAGC,QAAQ,GAAGmF,aAAa;UACxC;QACJ,CAAC,MACI,IAAIhZ,eAAe,EAAE;UACtB,MAAMiZ,WAAW,GAAG,EAAE;UACtB,MAAMC,YAAY,GAAG,EAAE;UACvB,IAAKV,WAAW,IAAIC,cAAc,EAAG;YACjC7E,SAAS,GAAG+E,mBAAmB;YAC/BO,YAAY,CAACC,IAAI,CAACvF,SAAS,CAAC;YAC5BC,QAAQ,GAAGzU,KAAK,IAAIC,MAAM,GAAGsZ,mBAAmB,CAAC;YACjDM,WAAW,CAACE,IAAI,CAACtF,QAAQ,CAAC;UAC9B;UACA,IAAK+E,YAAY,IAAIC,aAAa,EAAG;YACjChF,QAAQ,GAAGkF,kBAAkB;YAC7BE,WAAW,CAACE,IAAI,CAACtF,QAAQ,CAAC;YAC1BD,SAAS,GAAGvU,MAAM,IAAID,KAAK,GAAG2Z,kBAAkB,CAAC;YACjDG,YAAY,CAACC,IAAI,CAACvF,SAAS,CAAC;UAChC;UACA,IAAIqF,WAAW,CAAClM,MAAM,KAAK,CAAC,EAAE;YAC1B8G,QAAQ,GAAGjS,IAAI,CAAC6B,GAAG,CAAC,GAAGwV,WAAW,CAAC;UACvC;UACA,IAAIC,YAAY,CAACnM,MAAM,KAAK,CAAC,EAAE;YAC3B6G,SAAS,GAAGhS,IAAI,CAAC6B,GAAG,CAAC,GAAGyV,YAAY,CAAC;UACzC;QACJ,CAAC,MACI;UACD,IAAIV,WAAW,IAAIC,cAAc,EAAE;YAC/B7E,SAAS,GAAG+E,mBAAmB;UACnC;UACA,IAAIC,YAAY,IAAIC,aAAa,EAAE;YAC/BhF,QAAQ,GAAGkF,kBAAkB;UACjC;QACJ;QACA;QACA,IAAIjZ,KAAK,EAAE;UACP,MAAM2D,GAAG,GAAG7B,IAAI,CAAC6B,GAAG,CAACyI,QAAQ,CAAC9M,KAAK,EAAE8M,QAAQ,CAAC7M,MAAM,CAAC;UACrD,IAAIwU,QAAQ,GAAGpQ,GAAG,EAAE;YAChBoQ,QAAQ,GAAGD,SAAS,GAAGnQ,GAAG;UAC9B,CAAC,MACI,IAAImQ,SAAS,GAAGnQ,GAAG,EAAE;YACtBoQ,QAAQ,GAAGD,SAAS,GAAGnQ,GAAG;UAC9B;QACJ,CAAC,MACI,IAAIzD,eAAe,EAAE;UACtB,IAAI6T,QAAQ,GAAG3H,QAAQ,CAAC9M,KAAK,EAAE;YAC3ByU,QAAQ,GAAG3H,QAAQ,CAAC9M,KAAK;YACzBwU,SAAS,GAAGvU,MAAM,IAAID,KAAK,GAAG8M,QAAQ,CAAC9M,KAAK,CAAC;UACjD;UACA,IAAIwU,SAAS,GAAG1H,QAAQ,CAAC7M,MAAM,EAAE;YAC7BwU,QAAQ,GAAGzU,KAAK,IAAIC,MAAM,GAAG6M,QAAQ,CAAC7M,MAAM,CAAC;YAC7CuU,SAAS,GAAG1H,QAAQ,CAAC7M,MAAM;UAC/B;QACJ,CAAC,MACI;UACD,IAAIwU,QAAQ,GAAG3H,QAAQ,CAAC9M,KAAK,EAAE;YAC3ByU,QAAQ,GAAG3H,QAAQ,CAAC9M,KAAK;UAC7B;UACA,IAAIwU,SAAS,GAAG1H,QAAQ,CAAC7M,MAAM,EAAE;YAC7BuU,SAAS,GAAG1H,QAAQ,CAAC7M,MAAM;UAC/B;QACJ;QACA;QACAwU,QAAQ,GAAGjS,IAAI,CAAC9B,KAAK,CAAC+T,QAAQ,CAAC;QAC/BD,SAAS,GAAGhS,IAAI,CAAC9B,KAAK,CAAC8T,SAAS,CAAC;QACjC1I,OAAO,CAACuH,KAAK,CAACrT,KAAK,GAAI,GAAEyU,QAAS,IAAG;QACrC3I,OAAO,CAACuH,KAAK,CAACpT,MAAM,GAAI,GAAEuU,SAAU,IAAG;QACvC,IAAI,CAACwF,aAAa,GAAGvF,QAAQ;QAC7B,IAAI,CAACwF,cAAc,GAAGzF,SAAS;MACnC;IACJ,CAAC;IACD;IACA,IAAI,CAAChK,UAAU,GAAIlC,KAAK,IAAK;MACzB,IAAI,IAAI,CAACC,UAAU,EAAE;QACjBD,KAAK,CAACe,cAAc,CAAC,CAAC;QACtB,IAAI,CAACoB,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAACgO,QAAQ,CAACtN,iBAAiB,GAAG,IAAI,CAACsN,QAAQ,CAAChY,MAAM,CAACT,KAAK,GAAG,IAAI,CAACga,aAAa;QACjF,IAAI,CAACvB,QAAQ,CAACrN,kBAAkB,GAAG,IAAI,CAACqN,QAAQ,CAAChY,MAAM,CAACR,MAAM,GAAG,IAAI,CAACga,cAAc;QACpF,IAAI,CAACxB,QAAQ,CAAChY,MAAM,GAAG,IAAI,CAACgY,QAAQ,CAAChY,MAAM;QAC3C,IAAI,CAACgY,QAAQ,CAAC/L,eAAe,CAAC,CAAC;QAC/B,IAAI,CAACnE,UAAU,GAAG,KAAK;QACvB,IAAI,CAACY,kBAAkB,GAAG,IAAI;MAClC;IACJ,CAAC;IACD;IACA,IAAI,CAACwB,WAAW,GAAG,MAAM;MACrB;MACA;MACA,IAAI,IAAI,CAACzB,iBAAiB,EAAE;QACxB,IAAI,CAACsB,UAAU,CAAC,IAAI,CAACtB,iBAAiB,CAAC;MAC3C;IACJ,CAAC;IACD,IAAI,CAAC5B,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAI3G,aAAaA,CAACsK,GAAG,EAAE;IACnB,IAAIA,GAAG,KAAK,IAAI,CAACiP,cAAc,EAAE;MAC7B,IAAI,CAACA,cAAc,GAAGjP,GAAG;MACzBkP,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7B,IAAIpP,GAAG,EAAE;UACL,IAAI,CAACqP,oBAAoB,CAAC,CAAC;UAC3B,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC5B,CAAC,MACI;UACD,IAAI,CAACD,oBAAoB,CAAC,CAAC;QAC/B;MACJ,CAAC,CAAC;IACN;EACJ;EACA,IAAI3Z,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACuZ,cAAc;EAC9B;EACAjO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqO,oBAAoB,CAAC,CAAC;EAC/B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAClT,OAAO,CAACwE,iBAAiB,CAAC,MAAM;MACjC,MAAMC,OAAO,GAAG,IAAI,CAAC0O,QAAQ,CAAC/Q,aAAa;MAC3CqC,OAAO,CAACE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC3D,YAAY,EAAEpD,kBAAkB,CAAC;MAC5E6G,OAAO,CAACE,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC3D,YAAY,EAAEpD,kBAAkB,CAAC;IACjF,CAAC,CAAC;EACN;EACAqV,oBAAoBA,CAAA,EAAG;IACnB,MAAMxO,OAAO,GAAG,IAAI,CAAC0O,QAAQ,EAAE/Q,aAAa;IAC5C,IAAIqC,OAAO,EAAE;MACT,IAAI,CAAC5C,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACuB,mBAAmB,CAAC,CAAC;MAC1BqB,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC/D,YAAY,EAAEpD,kBAAkB,CAAC;MAC/E6G,OAAO,CAACM,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC/D,YAAY,EAAEpD,kBAAkB,CAAC;IACpF;EACJ;EACAqE,iBAAiBA,CAACyL,YAAY,EAAE;IAC5B,MAAMjJ,OAAO,GAAG,IAAI,CAACxE,SAAS;IAC9B,MAAM0N,OAAO,GAAGxM,YAAY,CAACuM,YAAY,CAAC;IAC1C,MAAME,aAAa,GAAGD,OAAO,GAAG,WAAW,GAAG,WAAW;IACzD,MAAME,YAAY,GAAGF,OAAO,GAAG,UAAU,GAAG,SAAS;IACrDlJ,OAAO,CAACE,gBAAgB,CAACiJ,aAAa,EAAE,IAAI,CAAC1L,YAAY,EAAEtE,kBAAkB,CAAC;IAC9E6G,OAAO,CAACE,gBAAgB,CAACkJ,YAAY,EAAE,IAAI,CAAC1K,UAAU,EAAEvF,kBAAkB,CAAC;IAC3E,IAAI+P,OAAO,EAAE;MACTlJ,OAAO,CAACE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACxB,UAAU,EAAEvF,kBAAkB,CAAC;IAChF;IACA,MAAMkQ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAE;MACzCA,MAAM,CAACnJ,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACrB,WAAW,CAAC;IACrD;EACJ;EACA;EACAF,mBAAmBA,CAAA,EAAG;IAClB,MAAMqB,OAAO,GAAG,IAAI,CAACxE,SAAS;IAC9BwE,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC7C,YAAY,EAAEtE,kBAAkB,CAAC;IAC/E6G,OAAO,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC5B,UAAU,EAAEvF,kBAAkB,CAAC;IAC3E6G,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC7C,YAAY,EAAEtE,kBAAkB,CAAC;IAC/E6G,OAAO,CAACM,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC5B,UAAU,EAAEvF,kBAAkB,CAAC;IAC5E6G,OAAO,CAACM,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC5B,UAAU,EAAEvF,kBAAkB,CAAC;IAC/E,MAAMkQ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAE;MACzCA,MAAM,CAAC/I,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACzB,WAAW,CAAC;IACxD;EACJ;EACA;EACAyK,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC9N,SAAS,CAAC+N,WAAW,IAAIF,MAAM;EAC/C;AACJ;AACAuC,aAAa,CAACpC,IAAI,YAAAmF,sBAAAjF,CAAA;EAAA,YAAAA,CAAA,IAAyFkC,aAAa,EA9StB3Z,EAAE,CAAA0X,iBAAA,CA8SsChX,EAAE,CAACK,aAAa,GA9SxDf,EAAE,CAAA0X,iBAAA,CA8SmE1X,EAAE,CAACE,UAAU,GA9SlFF,EAAE,CAAA0X,iBAAA,CA8S6F1X,EAAE,CAAC6X,MAAM,GA9SxG7X,EAAE,CAAA0X,iBAAA,CA8SmH5P,cAAc,GA9SnI9H,EAAE,CAAA0X,iBAAA,CA8S8InW,QAAQ;AAAA,CAA4C;AACtSoY,aAAa,CAAC5B,IAAI,kBA/SgF/X,EAAE,CAAAgY,iBAAA;EAAA5H,IAAA,EA+SLuJ,aAAa;EAAA1B,SAAA;EAAAC,SAAA,WAAAyE,oBAAAva,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/SVpC,EAAE,CAAAoY,WAAA,CAAApU,GAAA;IAAA;IAAA,IAAA5B,EAAA;MAAA,IAAAiW,EAAA;MAAFrY,EAAE,CAAAsY,cAAA,CAAAD,EAAA,GAAFrY,EAAE,CAAAuY,WAAA,QAAAlW,GAAA,CAAAoa,QAAA,GAAApE,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAA7V,aAAA;IAAAC,eAAA;IAAAF,KAAA;EAAA;EAAAia,QAAA;EAAAjE,QAAA,GAAF3Y,EAAE,CAAA4Y,kBAAA,CA+SkJ,CAC9O7X,aAAa,CAChB;EAAA+X,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA4D,uBAAAza,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAjT6FpC,EAAE,CAAAqZ,UAAA,IAAApV,4BAAA,gBAiT6I,CAAC;IAAA;IAAA,IAAA7B,EAAA;MAjThJpC,EAAE,CAAAyC,UAAA,SAAAJ,GAAA,CAAAO,aAiTmG,CAAC;IAAA;EAAA;EAAA8W,YAAA,GAAkEpY,EAAE,CAACsY,IAAI;EAAAE,aAAA;EAAAC,eAAA;AAAA,EAAwH;AACzYha,UAAU,CAAC,CACPiB,MAAM,CAAC,CAAC8b,MAAM,EAAEC,MAAM,KAAK,CAAC;EAAEnV;AAAM,CAAC,EAAEH,GAAG,KAAK;EAC3CA,GAAG,CAACuV,gBAAgB,CAACzV,MAAM,CAAC;EAC5B,MAAM0V,EAAE,GAAGxV,GAAG,CAACE,WAAW,CAACJ,MAAM,CAAC;EAClC,OAAQW,UAAU,IAAM,GAAEA,UAAW,uBAAsBA,UAAW,KAAI+U,EAAE,CAAC7Y,OAAQ,IAAGwD,KAAM,IAAGN,GAAI,YAAWA,GAAI,kCAAiC;AACzJ,CAAC,EAAE7F,qBAAqB,CAAC,CAC5B,EAAEkY,aAAa,CAACuD,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5C;EAAA,QAAAlD,SAAA,oBAAAA,SAAA,KAzTkGha,EAAE,CAAAia,iBAAA,CAyTRN,aAAa,EAAc,CAAC;IAC5GvJ,IAAI,EAAEjQ,SAAS;IACf+Z,IAAI,EAAE,CAAC;MAAEG,QAAQ,EAAE,iBAAiB;MAAEC,SAAS,EAAE,CACrCvZ,aAAa,CAChB;MAAEgZ,eAAe,EAAE3Z,uBAAuB,CAAC+Z,MAAM;MAAEyC,QAAQ,EAAE,eAAe;MAAE3D,QAAQ,EAAE;IAAmF,CAAC;EACzL,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7I,IAAI,EAAE1P,EAAE,CAACK;IAAc,CAAC,EAAE;MAAEqP,IAAI,EAAEpQ,EAAE,CAACE;IAAW,CAAC,EAAE;MAAEkQ,IAAI,EAAEpQ,EAAE,CAAC6X;IAAO,CAAC,EAAE;MAAEzH,IAAI,EAAEtI;IAAe,CAAC,EAAE;MAAEsI,IAAI,EAAEiD,SAAS;MAAEkH,UAAU,EAAE,CAAC;QAClKnK,IAAI,EAAE/P,MAAM;QACZ6Z,IAAI,EAAE,CAAC3Y,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkb,QAAQ,EAAE,CAAC;MACvCrM,IAAI,EAAE9P,SAAS;MACf4Z,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEtX,aAAa,EAAE,CAAC;MAChBwN,IAAI,EAAE7P;IACV,CAAC,CAAC;IAAEsC,eAAe,EAAE,CAAC;MAClBuN,IAAI,EAAE7P;IACV,CAAC,CAAC;IAAEoC,KAAK,EAAE,CAAC;MACRyN,IAAI,EAAE7P;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAASyU,iBAAiBA,CAACmI,CAAC,EAAE;EAC1B,MAAMC,EAAE,GAAGD,CAAC,GAAG,GAAG;EAClB,IAAIC,EAAE,GAAG,EAAE,EAAE;IACT,MAAM,IAAI9P,KAAK,CAAE,wBAAuB6P,CAAE,yDAAwD,CAAC;EACvG;EACA,OAAOC,EAAE;AACb;AACA;AACA;AACA;AACA,SAAS/H,eAAeA,CAACrQ,GAAG,EAAE;EAC1B;EACA,MAAMqY,SAAS,GAAGlH,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAClD,MAAMkH,OAAO,GAAGD,SAAS,CAACtY,UAAU,CAAC,IAAI,CAAC;EAC1C;EACAsY,SAAS,CAACpb,KAAK,GAAG+C,GAAG,CAAC/C,KAAK;EAC3Bob,SAAS,CAACnb,MAAM,GAAG8C,GAAG,CAAC9C,MAAM;EAC7B;EACAob,OAAO,CAAC5O,SAAS,CAAC1J,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B;EACA,OAAOqY,SAAS;AACpB;AACA,SAASzJ,YAAYA,CAACgD,OAAO,EAAE;EAC3B,IAAIQ,MAAM,CAACmG,IAAI,IAAIC,UAAU,CAAC5G,OAAO,CAAC,EAAE;IACpC,MAAM6G,GAAG,GAAG7G,OAAO,CAAChH,MAAM,GAAG,CAAC;IAC9B,MAAM8N,IAAI,GAAGtG,MAAM,CAACmG,IAAI,CAAC3G,OAAO,CAAC3G,OAAO,CAAC5I,qBAAqB,EAAE,EAAE,CAAC,CAAC;IACpE,MAAMsW,IAAI,GAAGxH,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3CuH,IAAI,CAACC,SAAS,GAAGF,IAAI;IACrB,MAAMG,GAAG,GAAGF,IAAI,CAACG,aAAa,CAAC,KAAK,CAAC;IACrCH,IAAI,CAACI,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC;IAC1C5H,QAAQ,CAAC6H,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/B,MAAM1b,KAAK,GAAGic,UAAU,CAACC,gBAAgB,CAACN,GAAG,CAAC,CAAC5b,KAAK,CAAC,IAAI,CAAC;IAC1D,MAAMC,MAAM,GAAGgc,UAAU,CAACC,gBAAgB,CAACN,GAAG,CAAC,CAAC3b,MAAM,CAAC,IAAI,CAAC;IAC5D,MAAMkQ,GAAG,GAAG3N,IAAI,CAAC2N,GAAG,CAACnQ,KAAK,EAAEC,MAAM,CAAC;IACnC2b,GAAG,CAACE,YAAY,CAAC,OAAO,EAAG,GAAEN,GAAG,IAAIxb,KAAK,GAAGmQ,GAAG,CAAE,IAAG,CAAC;IACrDyL,GAAG,CAACE,YAAY,CAAC,QAAQ,EAAG,GAAEN,GAAG,IAAIvb,MAAM,GAAGkQ,GAAG,CAAE,IAAG,CAAC;IACvD,MAAMhB,MAAM,GAAG/J,qBAAqB,GAAG+P,MAAM,CAACgH,IAAI,CAACT,IAAI,CAACC,SAAS,CAAC;IAClEzH,QAAQ,CAAC6H,IAAI,CAACK,WAAW,CAACV,IAAI,CAAC;IAC/B,OAAOvM,MAAM;EACjB;EACA,OAAOwF,OAAO;AAClB;AACA,SAAS4G,UAAUA,CAACc,OAAO,EAAE;EACzB,OAAOA,OAAO,CAAC3H,UAAU,CAACtP,qBAAqB,CAAC;AACpD;AACA,SAASwM,aAAaA,CAACF,GAAG,EAAE;EACxB,MAAM3O,GAAG,GAAG,IAAIuZ,KAAK,CAAC,CAAC;EACvBvZ,GAAG,CAACwZ,WAAW,GAAG,WAAW;EAC7BxZ,GAAG,CAAC2O,GAAG,GAAGA,GAAG;EACb,OAAO3O,GAAG;AACd;AACA,SAASqG,wBAAwBA,CAACd,KAAK,EAAE;EACrC;EACA,MAAMuB,KAAK,GAAGrB,YAAY,CAACF,KAAK,CAAC,GAC1BA,KAAK,CAACkU,OAAO,CAAC,CAAC,CAAC,IAAIlU,KAAK,CAACmU,cAAc,CAAC,CAAC,CAAC,GAC5CnU,KAAK;EACX,OAAO;IACHM,CAAC,EAAEiB,KAAK,CAACwG,OAAO;IAChBxH,CAAC,EAAEgB,KAAK,CAACyG;EACb,CAAC;AACL;AACA;AACA,SAAS9H,YAAYA,CAACF,KAAK,EAAE;EACzB,OAAOA,KAAK,CAAC6F,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAChC;AACA,SAASuO,WAAWA,CAACxB,CAAC,EAAE;EACpB,OAAO1Y,IAAI,CAAC9B,KAAK,CAACwa,CAAC,CAAC;AACxB;AAEA,MAAMyB,oBAAoB,CAAC;AAE3BA,oBAAoB,CAACrH,IAAI,YAAAsH,6BAAApH,CAAA;EAAA,YAAAA,CAAA,IAAyFmH,oBAAoB;AAAA,CAAkD;AACxLA,oBAAoB,CAACE,IAAI,kBAvZyE9e,EAAE,CAAA+e,gBAAA;EAAA3O,IAAA,EAuZewO;AAAoB,EAAsG;AAC7OA,oBAAoB,CAACI,IAAI,kBAxZyEhf,EAAE,CAAAif,gBAAA;EAAAC,OAAA,GAwZ+C,CAAC1d,YAAY,CAAC;AAAA,EAAI;AACrK;EAAA,QAAAwY,SAAA,oBAAAA,SAAA,KAzZkGha,EAAE,CAAAia,iBAAA,CAyZR2E,oBAAoB,EAAc,CAAC;IACnHxO,IAAI,EAAE3P,QAAQ;IACdyZ,IAAI,EAAE,CAAC;MACCgF,OAAO,EAAE,CAAC1d,YAAY,CAAC;MACvB2d,OAAO,EAAE,CAACrX,cAAc,CAAC;MACzBsX,YAAY,EAAE,CAACtX,cAAc,EAAE6R,aAAa;IAChD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASlR,gBAAgB,EAAEQ,eAAe,EAAEF,aAAa,EAAE4Q,aAAa,EAAE7R,cAAc,EAAE8W,oBAAoB,EAAErX,MAAM,EAAEyN,iBAAiB,EAAE2J,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
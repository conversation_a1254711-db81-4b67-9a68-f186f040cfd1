{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class WorldTimeService {\n  constructor(http) {\n    this.http = http;\n    this.time = 0;\n  }\n  getServerTime() {\n    // get time from https://worldtimeapi.org/api/timezone/Europe/London\n    return this.http.get('https://worldtimeapi.org/api/timezone/Europe/London').pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return Observable.throw(err);\n    }));\n  }\n  // sync server time and create interval to calculate time\n  syncServerTime() {\n    this.getServerTime().subscribe(res => {\n      console.log('server_time', res);\n      this.time = res.unixtime;\n      if (this.interval) clearInterval(this.interval);\n      this.interval = setInterval(() => {\n        this.time += 1;\n      }, 1000);\n    });\n  }\n  static #_ = this.ɵfac = function WorldTimeService_Factory(t) {\n    return new (t || WorldTimeService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: WorldTimeService,\n    factory: WorldTimeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,UAAU,QAAQ,MAAM;AACjC,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAKhD,OAAM,MAAOC,gBAAgB;EAG3BC,YAAmBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFvB,KAAAC,IAAI,GAAG,CAAC;EAE8B;EACtCC,aAAaA,CAAA;IACX;IACA,OAAO,IAAI,CAACF,IAAI,CACbG,GAAG,CAAC,qDAAqD,CAAC,CAC1DC,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACY,KAAK,CAACD,GAAG,CAAC;IAC9B,CAAC,CAAC,CACH;EACL;EACA;EACAE,cAAcA,CAAA;IACZ,IAAI,CAACN,aAAa,EAAE,CAACO,SAAS,CAAEJ,GAAQ,IAAI;MAC1CK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEN,GAAG,CAAC;MAC/B,IAAI,CAACJ,IAAI,GAAGI,GAAG,CAACO,QAAQ;MACxB,IAAI,IAAI,CAACC,QAAQ,EAAEC,aAAa,CAAC,IAAI,CAACD,QAAQ,CAAC;MAC/C,IAAI,CAACA,QAAQ,GAAGE,WAAW,CAAC,MAAK;QAC/B,IAAI,CAACd,IAAI,IAAI,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EAAC,QAAAe,CAAA;qBA3BUlB,gBAAgB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAhBvB,gBAAgB;IAAAwB,OAAA,EAAhBxB,gBAAgB,CAAAyB,IAAA;IAAAC,UAAA,EAFf;EAAM", "names": ["Observable", "map", "catchError", "WorldTimeService", "constructor", "http", "time", "getServerTime", "get", "pipe", "res", "err", "throw", "syncServerTime", "subscribe", "console", "log", "unixtime", "interval", "clearInterval", "setInterval", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\world-time.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class WorldTimeService {\r\n  time = 0;\r\n  interval: any;\r\n  constructor(public http: HttpClient) {}\r\n  getServerTime() {\r\n    // get time from https://worldtimeapi.org/api/timezone/Europe/London\r\n    return this.http\r\n      .get('https://worldtimeapi.org/api/timezone/Europe/London')\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return Observable.throw(err);\r\n        })\r\n      );\r\n  }\r\n  // sync server time and create interval to calculate time\r\n  syncServerTime() {\r\n    this.getServerTime().subscribe((res: any) => {\r\n      console.log('server_time', res);\r\n      this.time = res.unixtime;\r\n      if (this.interval) clearInterval(this.interval);\r\n      this.interval = setInterval(() => {\r\n        this.time += 1;\r\n      }, 1000);\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
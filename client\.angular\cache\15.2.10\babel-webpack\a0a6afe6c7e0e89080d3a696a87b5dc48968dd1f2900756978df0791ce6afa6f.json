{"ast": null, "code": "/*\n     _ _      _       _\n ___| (_) ___| | __  (_)___\n/ __| | |/ __| |/ /  | / __|\n\\__ \\ | | (__|   < _ | \\__ \\\n|___/_|_|\\___|_|\\_(_)/ |___/\n                   |__/\n\n Version: 1.8.1\n  Author: <PERSON>\n Website: http://kenwheeler.github.io\n    Docs: http://kenwheeler.github.io/slick\n    Repo: http://github.com/kenwheeler/slick\n  Issues: http://github.com/kenwheeler/slick/issues\n\n */\n/* global window, document, define, jQuery, setInterval, clearInterval */\n;\n(function (factory) {\n  'use strict';\n\n  if (typeof define === 'function' && define.amd) {\n    define(['jquery'], factory);\n  } else if (typeof exports !== 'undefined') {\n    module.exports = factory(require('jquery'));\n  } else {\n    factory(jQuery);\n  }\n})(function ($) {\n  'use strict';\n\n  var Slick = window.Slick || {};\n  Slick = function () {\n    var instanceUid = 0;\n    function Slick(element, settings) {\n      var _ = this,\n        dataSettings;\n      _.defaults = {\n        accessibility: true,\n        adaptiveHeight: false,\n        appendArrows: $(element),\n        appendDots: $(element),\n        arrows: true,\n        asNavFor: null,\n        prevArrow: '<button class=\"slick-prev\" aria-label=\"Previous\" type=\"button\">Previous</button>',\n        nextArrow: '<button class=\"slick-next\" aria-label=\"Next\" type=\"button\">Next</button>',\n        autoplay: false,\n        autoplaySpeed: 3000,\n        centerMode: false,\n        centerPadding: '50px',\n        cssEase: 'ease',\n        customPaging: function (slider, i) {\n          return $('<button type=\"button\" />').text(i + 1);\n        },\n        dots: false,\n        dotsClass: 'slick-dots',\n        draggable: true,\n        easing: 'linear',\n        edgeFriction: 0.35,\n        fade: false,\n        focusOnSelect: false,\n        focusOnChange: false,\n        infinite: true,\n        initialSlide: 0,\n        lazyLoad: 'ondemand',\n        mobileFirst: false,\n        pauseOnHover: true,\n        pauseOnFocus: true,\n        pauseOnDotsHover: false,\n        respondTo: 'window',\n        responsive: null,\n        rows: 1,\n        rtl: false,\n        slide: '',\n        slidesPerRow: 1,\n        slidesToShow: 1,\n        slidesToScroll: 1,\n        speed: 500,\n        swipe: true,\n        swipeToSlide: false,\n        touchMove: true,\n        touchThreshold: 5,\n        useCSS: true,\n        useTransform: true,\n        variableWidth: false,\n        vertical: false,\n        verticalSwiping: false,\n        waitForAnimate: true,\n        zIndex: 1000\n      };\n      _.initials = {\n        animating: false,\n        dragging: false,\n        autoPlayTimer: null,\n        currentDirection: 0,\n        currentLeft: null,\n        currentSlide: 0,\n        direction: 1,\n        $dots: null,\n        listWidth: null,\n        listHeight: null,\n        loadIndex: 0,\n        $nextArrow: null,\n        $prevArrow: null,\n        scrolling: false,\n        slideCount: null,\n        slideWidth: null,\n        $slideTrack: null,\n        $slides: null,\n        sliding: false,\n        slideOffset: 0,\n        swipeLeft: null,\n        swiping: false,\n        $list: null,\n        touchObject: {},\n        transformsEnabled: false,\n        unslicked: false\n      };\n      $.extend(_, _.initials);\n      _.activeBreakpoint = null;\n      _.animType = null;\n      _.animProp = null;\n      _.breakpoints = [];\n      _.breakpointSettings = [];\n      _.cssTransitions = false;\n      _.focussed = false;\n      _.interrupted = false;\n      _.hidden = 'hidden';\n      _.paused = true;\n      _.positionProp = null;\n      _.respondTo = null;\n      _.rowCount = 1;\n      _.shouldClick = true;\n      _.$slider = $(element);\n      _.$slidesCache = null;\n      _.transformType = null;\n      _.transitionType = null;\n      _.visibilityChange = 'visibilitychange';\n      _.windowWidth = 0;\n      _.windowTimer = null;\n      dataSettings = $(element).data('slick') || {};\n      _.options = $.extend({}, _.defaults, settings, dataSettings);\n      _.currentSlide = _.options.initialSlide;\n      _.originalSettings = _.options;\n      if (typeof document.mozHidden !== 'undefined') {\n        _.hidden = 'mozHidden';\n        _.visibilityChange = 'mozvisibilitychange';\n      } else if (typeof document.webkitHidden !== 'undefined') {\n        _.hidden = 'webkitHidden';\n        _.visibilityChange = 'webkitvisibilitychange';\n      }\n      _.autoPlay = $.proxy(_.autoPlay, _);\n      _.autoPlayClear = $.proxy(_.autoPlayClear, _);\n      _.autoPlayIterator = $.proxy(_.autoPlayIterator, _);\n      _.changeSlide = $.proxy(_.changeSlide, _);\n      _.clickHandler = $.proxy(_.clickHandler, _);\n      _.selectHandler = $.proxy(_.selectHandler, _);\n      _.setPosition = $.proxy(_.setPosition, _);\n      _.swipeHandler = $.proxy(_.swipeHandler, _);\n      _.dragHandler = $.proxy(_.dragHandler, _);\n      _.keyHandler = $.proxy(_.keyHandler, _);\n      _.instanceUid = instanceUid++;\n\n      // A simple way to check for HTML strings\n      // Strict HTML recognition (must start with <)\n      // Extracted from jQuery v1.11 source\n      _.htmlExpr = /^(?:\\s*(<[\\w\\W]+>)[^>]*)$/;\n      _.registerBreakpoints();\n      _.init(true);\n    }\n    return Slick;\n  }();\n  Slick.prototype.activateADA = function () {\n    var _ = this;\n    _.$slideTrack.find('.slick-active').attr({\n      'aria-hidden': 'false'\n    }).find('a, input, button, select').attr({\n      'tabindex': '0'\n    });\n  };\n  Slick.prototype.addSlide = Slick.prototype.slickAdd = function (markup, index, addBefore) {\n    var _ = this;\n    if (typeof index === 'boolean') {\n      addBefore = index;\n      index = null;\n    } else if (index < 0 || index >= _.slideCount) {\n      return false;\n    }\n    _.unload();\n    if (typeof index === 'number') {\n      if (index === 0 && _.$slides.length === 0) {\n        $(markup).appendTo(_.$slideTrack);\n      } else if (addBefore) {\n        $(markup).insertBefore(_.$slides.eq(index));\n      } else {\n        $(markup).insertAfter(_.$slides.eq(index));\n      }\n    } else {\n      if (addBefore === true) {\n        $(markup).prependTo(_.$slideTrack);\n      } else {\n        $(markup).appendTo(_.$slideTrack);\n      }\n    }\n    _.$slides = _.$slideTrack.children(this.options.slide);\n    _.$slideTrack.children(this.options.slide).detach();\n    _.$slideTrack.append(_.$slides);\n    _.$slides.each(function (index, element) {\n      $(element).attr('data-slick-index', index);\n    });\n    _.$slidesCache = _.$slides;\n    _.reinit();\n  };\n  Slick.prototype.animateHeight = function () {\n    var _ = this;\n    if (_.options.slidesToShow === 1 && _.options.adaptiveHeight === true && _.options.vertical === false) {\n      var targetHeight = _.$slides.eq(_.currentSlide).outerHeight(true);\n      _.$list.animate({\n        height: targetHeight\n      }, _.options.speed);\n    }\n  };\n  Slick.prototype.animateSlide = function (targetLeft, callback) {\n    var animProps = {},\n      _ = this;\n    _.animateHeight();\n    if (_.options.rtl === true && _.options.vertical === false) {\n      targetLeft = -targetLeft;\n    }\n    if (_.transformsEnabled === false) {\n      if (_.options.vertical === false) {\n        _.$slideTrack.animate({\n          left: targetLeft\n        }, _.options.speed, _.options.easing, callback);\n      } else {\n        _.$slideTrack.animate({\n          top: targetLeft\n        }, _.options.speed, _.options.easing, callback);\n      }\n    } else {\n      if (_.cssTransitions === false) {\n        if (_.options.rtl === true) {\n          _.currentLeft = -_.currentLeft;\n        }\n        $({\n          animStart: _.currentLeft\n        }).animate({\n          animStart: targetLeft\n        }, {\n          duration: _.options.speed,\n          easing: _.options.easing,\n          step: function (now) {\n            now = Math.ceil(now);\n            if (_.options.vertical === false) {\n              animProps[_.animType] = 'translate(' + now + 'px, 0px)';\n              _.$slideTrack.css(animProps);\n            } else {\n              animProps[_.animType] = 'translate(0px,' + now + 'px)';\n              _.$slideTrack.css(animProps);\n            }\n          },\n          complete: function () {\n            if (callback) {\n              callback.call();\n            }\n          }\n        });\n      } else {\n        _.applyTransition();\n        targetLeft = Math.ceil(targetLeft);\n        if (_.options.vertical === false) {\n          animProps[_.animType] = 'translate3d(' + targetLeft + 'px, 0px, 0px)';\n        } else {\n          animProps[_.animType] = 'translate3d(0px,' + targetLeft + 'px, 0px)';\n        }\n        _.$slideTrack.css(animProps);\n        if (callback) {\n          setTimeout(function () {\n            _.disableTransition();\n            callback.call();\n          }, _.options.speed);\n        }\n      }\n    }\n  };\n  Slick.prototype.getNavTarget = function () {\n    var _ = this,\n      asNavFor = _.options.asNavFor;\n    if (asNavFor && asNavFor !== null) {\n      asNavFor = $(asNavFor).not(_.$slider);\n    }\n    return asNavFor;\n  };\n  Slick.prototype.asNavFor = function (index) {\n    var _ = this,\n      asNavFor = _.getNavTarget();\n    if (asNavFor !== null && typeof asNavFor === 'object') {\n      asNavFor.each(function () {\n        var target = $(this).slick('getSlick');\n        if (!target.unslicked) {\n          target.slideHandler(index, true);\n        }\n      });\n    }\n  };\n  Slick.prototype.applyTransition = function (slide) {\n    var _ = this,\n      transition = {};\n    if (_.options.fade === false) {\n      transition[_.transitionType] = _.transformType + ' ' + _.options.speed + 'ms ' + _.options.cssEase;\n    } else {\n      transition[_.transitionType] = 'opacity ' + _.options.speed + 'ms ' + _.options.cssEase;\n    }\n    if (_.options.fade === false) {\n      _.$slideTrack.css(transition);\n    } else {\n      _.$slides.eq(slide).css(transition);\n    }\n  };\n  Slick.prototype.autoPlay = function () {\n    var _ = this;\n    _.autoPlayClear();\n    if (_.slideCount > _.options.slidesToShow) {\n      _.autoPlayTimer = setInterval(_.autoPlayIterator, _.options.autoplaySpeed);\n    }\n  };\n  Slick.prototype.autoPlayClear = function () {\n    var _ = this;\n    if (_.autoPlayTimer) {\n      clearInterval(_.autoPlayTimer);\n    }\n  };\n  Slick.prototype.autoPlayIterator = function () {\n    var _ = this,\n      slideTo = _.currentSlide + _.options.slidesToScroll;\n    if (!_.paused && !_.interrupted && !_.focussed) {\n      if (_.options.infinite === false) {\n        if (_.direction === 1 && _.currentSlide + 1 === _.slideCount - 1) {\n          _.direction = 0;\n        } else if (_.direction === 0) {\n          slideTo = _.currentSlide - _.options.slidesToScroll;\n          if (_.currentSlide - 1 === 0) {\n            _.direction = 1;\n          }\n        }\n      }\n      _.slideHandler(slideTo);\n    }\n  };\n  Slick.prototype.buildArrows = function () {\n    var _ = this;\n    if (_.options.arrows === true) {\n      _.$prevArrow = $(_.options.prevArrow).addClass('slick-arrow');\n      _.$nextArrow = $(_.options.nextArrow).addClass('slick-arrow');\n      if (_.slideCount > _.options.slidesToShow) {\n        _.$prevArrow.removeClass('slick-hidden').removeAttr('aria-hidden tabindex');\n        _.$nextArrow.removeClass('slick-hidden').removeAttr('aria-hidden tabindex');\n        if (_.htmlExpr.test(_.options.prevArrow)) {\n          _.$prevArrow.prependTo(_.options.appendArrows);\n        }\n        if (_.htmlExpr.test(_.options.nextArrow)) {\n          _.$nextArrow.appendTo(_.options.appendArrows);\n        }\n        if (_.options.infinite !== true) {\n          _.$prevArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n        }\n      } else {\n        _.$prevArrow.add(_.$nextArrow).addClass('slick-hidden').attr({\n          'aria-disabled': 'true',\n          'tabindex': '-1'\n        });\n      }\n    }\n  };\n  Slick.prototype.buildDots = function () {\n    var _ = this,\n      i,\n      dot;\n    if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n      _.$slider.addClass('slick-dotted');\n      dot = $('<ul />').addClass(_.options.dotsClass);\n      for (i = 0; i <= _.getDotCount(); i += 1) {\n        dot.append($('<li />').append(_.options.customPaging.call(this, _, i)));\n      }\n      _.$dots = dot.appendTo(_.options.appendDots);\n      _.$dots.find('li').first().addClass('slick-active');\n    }\n  };\n  Slick.prototype.buildOut = function () {\n    var _ = this;\n    _.$slides = _.$slider.children(_.options.slide + ':not(.slick-cloned)').addClass('slick-slide');\n    _.slideCount = _.$slides.length;\n    _.$slides.each(function (index, element) {\n      $(element).attr('data-slick-index', index).data('originalStyling', $(element).attr('style') || '');\n    });\n    _.$slider.addClass('slick-slider');\n    _.$slideTrack = _.slideCount === 0 ? $('<div class=\"slick-track\"/>').appendTo(_.$slider) : _.$slides.wrapAll('<div class=\"slick-track\"/>').parent();\n    _.$list = _.$slideTrack.wrap('<div class=\"slick-list\"/>').parent();\n    _.$slideTrack.css('opacity', 0);\n    if (_.options.centerMode === true || _.options.swipeToSlide === true) {\n      _.options.slidesToScroll = 1;\n    }\n    $('img[data-lazy]', _.$slider).not('[src]').addClass('slick-loading');\n    _.setupInfinite();\n    _.buildArrows();\n    _.buildDots();\n    _.updateDots();\n    _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);\n    if (_.options.draggable === true) {\n      _.$list.addClass('draggable');\n    }\n  };\n  Slick.prototype.buildRows = function () {\n    var _ = this,\n      a,\n      b,\n      c,\n      newSlides,\n      numOfSlides,\n      originalSlides,\n      slidesPerSection;\n    newSlides = document.createDocumentFragment();\n    originalSlides = _.$slider.children();\n    if (_.options.rows > 0) {\n      slidesPerSection = _.options.slidesPerRow * _.options.rows;\n      numOfSlides = Math.ceil(originalSlides.length / slidesPerSection);\n      for (a = 0; a < numOfSlides; a++) {\n        var slide = document.createElement('div');\n        for (b = 0; b < _.options.rows; b++) {\n          var row = document.createElement('div');\n          for (c = 0; c < _.options.slidesPerRow; c++) {\n            var target = a * slidesPerSection + (b * _.options.slidesPerRow + c);\n            if (originalSlides.get(target)) {\n              row.appendChild(originalSlides.get(target));\n            }\n          }\n          slide.appendChild(row);\n        }\n        newSlides.appendChild(slide);\n      }\n      _.$slider.empty().append(newSlides);\n      _.$slider.children().children().children().css({\n        'width': 100 / _.options.slidesPerRow + '%',\n        'display': 'inline-block'\n      });\n    }\n  };\n  Slick.prototype.checkResponsive = function (initial, forceUpdate) {\n    var _ = this,\n      breakpoint,\n      targetBreakpoint,\n      respondToWidth,\n      triggerBreakpoint = false;\n    var sliderWidth = _.$slider.width();\n    var windowWidth = window.innerWidth || $(window).width();\n    if (_.respondTo === 'window') {\n      respondToWidth = windowWidth;\n    } else if (_.respondTo === 'slider') {\n      respondToWidth = sliderWidth;\n    } else if (_.respondTo === 'min') {\n      respondToWidth = Math.min(windowWidth, sliderWidth);\n    }\n    if (_.options.responsive && _.options.responsive.length && _.options.responsive !== null) {\n      targetBreakpoint = null;\n      for (breakpoint in _.breakpoints) {\n        if (_.breakpoints.hasOwnProperty(breakpoint)) {\n          if (_.originalSettings.mobileFirst === false) {\n            if (respondToWidth < _.breakpoints[breakpoint]) {\n              targetBreakpoint = _.breakpoints[breakpoint];\n            }\n          } else {\n            if (respondToWidth > _.breakpoints[breakpoint]) {\n              targetBreakpoint = _.breakpoints[breakpoint];\n            }\n          }\n        }\n      }\n      if (targetBreakpoint !== null) {\n        if (_.activeBreakpoint !== null) {\n          if (targetBreakpoint !== _.activeBreakpoint || forceUpdate) {\n            _.activeBreakpoint = targetBreakpoint;\n            if (_.breakpointSettings[targetBreakpoint] === 'unslick') {\n              _.unslick(targetBreakpoint);\n            } else {\n              _.options = $.extend({}, _.originalSettings, _.breakpointSettings[targetBreakpoint]);\n              if (initial === true) {\n                _.currentSlide = _.options.initialSlide;\n              }\n              _.refresh(initial);\n            }\n            triggerBreakpoint = targetBreakpoint;\n          }\n        } else {\n          _.activeBreakpoint = targetBreakpoint;\n          if (_.breakpointSettings[targetBreakpoint] === 'unslick') {\n            _.unslick(targetBreakpoint);\n          } else {\n            _.options = $.extend({}, _.originalSettings, _.breakpointSettings[targetBreakpoint]);\n            if (initial === true) {\n              _.currentSlide = _.options.initialSlide;\n            }\n            _.refresh(initial);\n          }\n          triggerBreakpoint = targetBreakpoint;\n        }\n      } else {\n        if (_.activeBreakpoint !== null) {\n          _.activeBreakpoint = null;\n          _.options = _.originalSettings;\n          if (initial === true) {\n            _.currentSlide = _.options.initialSlide;\n          }\n          _.refresh(initial);\n          triggerBreakpoint = targetBreakpoint;\n        }\n      }\n\n      // only trigger breakpoints during an actual break. not on initialize.\n      if (!initial && triggerBreakpoint !== false) {\n        _.$slider.trigger('breakpoint', [_, triggerBreakpoint]);\n      }\n    }\n  };\n  Slick.prototype.changeSlide = function (event, dontAnimate) {\n    var _ = this,\n      $target = $(event.currentTarget),\n      indexOffset,\n      slideOffset,\n      unevenOffset;\n\n    // If target is a link, prevent default action.\n    if ($target.is('a')) {\n      event.preventDefault();\n    }\n\n    // If target is not the <li> element (ie: a child), find the <li>.\n    if (!$target.is('li')) {\n      $target = $target.closest('li');\n    }\n    unevenOffset = _.slideCount % _.options.slidesToScroll !== 0;\n    indexOffset = unevenOffset ? 0 : (_.slideCount - _.currentSlide) % _.options.slidesToScroll;\n    switch (event.data.message) {\n      case 'previous':\n        slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset;\n        if (_.slideCount > _.options.slidesToShow) {\n          _.slideHandler(_.currentSlide - slideOffset, false, dontAnimate);\n        }\n        break;\n      case 'next':\n        slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset;\n        if (_.slideCount > _.options.slidesToShow) {\n          _.slideHandler(_.currentSlide + slideOffset, false, dontAnimate);\n        }\n        break;\n      case 'index':\n        var index = event.data.index === 0 ? 0 : event.data.index || $target.index() * _.options.slidesToScroll;\n        _.slideHandler(_.checkNavigable(index), false, dontAnimate);\n        $target.children().trigger('focus');\n        break;\n      default:\n        return;\n    }\n  };\n  Slick.prototype.checkNavigable = function (index) {\n    var _ = this,\n      navigables,\n      prevNavigable;\n    navigables = _.getNavigableIndexes();\n    prevNavigable = 0;\n    if (index > navigables[navigables.length - 1]) {\n      index = navigables[navigables.length - 1];\n    } else {\n      for (var n in navigables) {\n        if (index < navigables[n]) {\n          index = prevNavigable;\n          break;\n        }\n        prevNavigable = navigables[n];\n      }\n    }\n    return index;\n  };\n  Slick.prototype.cleanUpEvents = function () {\n    var _ = this;\n    if (_.options.dots && _.$dots !== null) {\n      $('li', _.$dots).off('click.slick', _.changeSlide).off('mouseenter.slick', $.proxy(_.interrupt, _, true)).off('mouseleave.slick', $.proxy(_.interrupt, _, false));\n      if (_.options.accessibility === true) {\n        _.$dots.off('keydown.slick', _.keyHandler);\n      }\n    }\n    _.$slider.off('focus.slick blur.slick');\n    if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n      _.$prevArrow && _.$prevArrow.off('click.slick', _.changeSlide);\n      _.$nextArrow && _.$nextArrow.off('click.slick', _.changeSlide);\n      if (_.options.accessibility === true) {\n        _.$prevArrow && _.$prevArrow.off('keydown.slick', _.keyHandler);\n        _.$nextArrow && _.$nextArrow.off('keydown.slick', _.keyHandler);\n      }\n    }\n    _.$list.off('touchstart.slick mousedown.slick', _.swipeHandler);\n    _.$list.off('touchmove.slick mousemove.slick', _.swipeHandler);\n    _.$list.off('touchend.slick mouseup.slick', _.swipeHandler);\n    _.$list.off('touchcancel.slick mouseleave.slick', _.swipeHandler);\n    _.$list.off('click.slick', _.clickHandler);\n    $(document).off(_.visibilityChange, _.visibility);\n    _.cleanUpSlideEvents();\n    if (_.options.accessibility === true) {\n      _.$list.off('keydown.slick', _.keyHandler);\n    }\n    if (_.options.focusOnSelect === true) {\n      $(_.$slideTrack).children().off('click.slick', _.selectHandler);\n    }\n    $(window).off('orientationchange.slick.slick-' + _.instanceUid, _.orientationChange);\n    $(window).off('resize.slick.slick-' + _.instanceUid, _.resize);\n    $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);\n    $(window).off('load.slick.slick-' + _.instanceUid, _.setPosition);\n  };\n  Slick.prototype.cleanUpSlideEvents = function () {\n    var _ = this;\n    _.$list.off('mouseenter.slick', $.proxy(_.interrupt, _, true));\n    _.$list.off('mouseleave.slick', $.proxy(_.interrupt, _, false));\n  };\n  Slick.prototype.cleanUpRows = function () {\n    var _ = this,\n      originalSlides;\n    if (_.options.rows > 0) {\n      originalSlides = _.$slides.children().children();\n      originalSlides.removeAttr('style');\n      _.$slider.empty().append(originalSlides);\n    }\n  };\n  Slick.prototype.clickHandler = function (event) {\n    var _ = this;\n    if (_.shouldClick === false) {\n      event.stopImmediatePropagation();\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n  Slick.prototype.destroy = function (refresh) {\n    var _ = this;\n    _.autoPlayClear();\n    _.touchObject = {};\n    _.cleanUpEvents();\n    $('.slick-cloned', _.$slider).detach();\n    if (_.$dots) {\n      _.$dots.remove();\n    }\n    if (_.$prevArrow && _.$prevArrow.length) {\n      _.$prevArrow.removeClass('slick-disabled slick-arrow slick-hidden').removeAttr('aria-hidden aria-disabled tabindex').css('display', '');\n      if (_.htmlExpr.test(_.options.prevArrow)) {\n        _.$prevArrow.remove();\n      }\n    }\n    if (_.$nextArrow && _.$nextArrow.length) {\n      _.$nextArrow.removeClass('slick-disabled slick-arrow slick-hidden').removeAttr('aria-hidden aria-disabled tabindex').css('display', '');\n      if (_.htmlExpr.test(_.options.nextArrow)) {\n        _.$nextArrow.remove();\n      }\n    }\n    if (_.$slides) {\n      _.$slides.removeClass('slick-slide slick-active slick-center slick-visible slick-current').removeAttr('aria-hidden').removeAttr('data-slick-index').each(function () {\n        $(this).attr('style', $(this).data('originalStyling'));\n      });\n      _.$slideTrack.children(this.options.slide).detach();\n      _.$slideTrack.detach();\n      _.$list.detach();\n      _.$slider.append(_.$slides);\n    }\n    _.cleanUpRows();\n    _.$slider.removeClass('slick-slider');\n    _.$slider.removeClass('slick-initialized');\n    _.$slider.removeClass('slick-dotted');\n    _.unslicked = true;\n    if (!refresh) {\n      _.$slider.trigger('destroy', [_]);\n    }\n  };\n  Slick.prototype.disableTransition = function (slide) {\n    var _ = this,\n      transition = {};\n    transition[_.transitionType] = '';\n    if (_.options.fade === false) {\n      _.$slideTrack.css(transition);\n    } else {\n      _.$slides.eq(slide).css(transition);\n    }\n  };\n  Slick.prototype.fadeSlide = function (slideIndex, callback) {\n    var _ = this;\n    if (_.cssTransitions === false) {\n      _.$slides.eq(slideIndex).css({\n        zIndex: _.options.zIndex\n      });\n      _.$slides.eq(slideIndex).animate({\n        opacity: 1\n      }, _.options.speed, _.options.easing, callback);\n    } else {\n      _.applyTransition(slideIndex);\n      _.$slides.eq(slideIndex).css({\n        opacity: 1,\n        zIndex: _.options.zIndex\n      });\n      if (callback) {\n        setTimeout(function () {\n          _.disableTransition(slideIndex);\n          callback.call();\n        }, _.options.speed);\n      }\n    }\n  };\n  Slick.prototype.fadeSlideOut = function (slideIndex) {\n    var _ = this;\n    if (_.cssTransitions === false) {\n      _.$slides.eq(slideIndex).animate({\n        opacity: 0,\n        zIndex: _.options.zIndex - 2\n      }, _.options.speed, _.options.easing);\n    } else {\n      _.applyTransition(slideIndex);\n      _.$slides.eq(slideIndex).css({\n        opacity: 0,\n        zIndex: _.options.zIndex - 2\n      });\n    }\n  };\n  Slick.prototype.filterSlides = Slick.prototype.slickFilter = function (filter) {\n    var _ = this;\n    if (filter !== null) {\n      _.$slidesCache = _.$slides;\n      _.unload();\n      _.$slideTrack.children(this.options.slide).detach();\n      _.$slidesCache.filter(filter).appendTo(_.$slideTrack);\n      _.reinit();\n    }\n  };\n  Slick.prototype.focusHandler = function () {\n    var _ = this;\n    _.$slider.off('focus.slick blur.slick').on('focus.slick blur.slick', '*', function (event) {\n      event.stopImmediatePropagation();\n      var $sf = $(this);\n      setTimeout(function () {\n        if (_.options.pauseOnFocus) {\n          _.focussed = $sf.is(':focus');\n          _.autoPlay();\n        }\n      }, 0);\n    });\n  };\n  Slick.prototype.getCurrent = Slick.prototype.slickCurrentSlide = function () {\n    var _ = this;\n    return _.currentSlide;\n  };\n  Slick.prototype.getDotCount = function () {\n    var _ = this;\n    var breakPoint = 0;\n    var counter = 0;\n    var pagerQty = 0;\n    if (_.options.infinite === true) {\n      if (_.slideCount <= _.options.slidesToShow) {\n        ++pagerQty;\n      } else {\n        while (breakPoint < _.slideCount) {\n          ++pagerQty;\n          breakPoint = counter + _.options.slidesToScroll;\n          counter += _.options.slidesToScroll <= _.options.slidesToShow ? _.options.slidesToScroll : _.options.slidesToShow;\n        }\n      }\n    } else if (_.options.centerMode === true) {\n      pagerQty = _.slideCount;\n    } else if (!_.options.asNavFor) {\n      pagerQty = 1 + Math.ceil((_.slideCount - _.options.slidesToShow) / _.options.slidesToScroll);\n    } else {\n      while (breakPoint < _.slideCount) {\n        ++pagerQty;\n        breakPoint = counter + _.options.slidesToScroll;\n        counter += _.options.slidesToScroll <= _.options.slidesToShow ? _.options.slidesToScroll : _.options.slidesToShow;\n      }\n    }\n    return pagerQty - 1;\n  };\n  Slick.prototype.getLeft = function (slideIndex) {\n    var _ = this,\n      targetLeft,\n      verticalHeight,\n      verticalOffset = 0,\n      targetSlide,\n      coef;\n    _.slideOffset = 0;\n    verticalHeight = _.$slides.first().outerHeight(true);\n    if (_.options.infinite === true) {\n      if (_.slideCount > _.options.slidesToShow) {\n        _.slideOffset = _.slideWidth * _.options.slidesToShow * -1;\n        coef = -1;\n        if (_.options.vertical === true && _.options.centerMode === true) {\n          if (_.options.slidesToShow === 2) {\n            coef = -1.5;\n          } else if (_.options.slidesToShow === 1) {\n            coef = -2;\n          }\n        }\n        verticalOffset = verticalHeight * _.options.slidesToShow * coef;\n      }\n      if (_.slideCount % _.options.slidesToScroll !== 0) {\n        if (slideIndex + _.options.slidesToScroll > _.slideCount && _.slideCount > _.options.slidesToShow) {\n          if (slideIndex > _.slideCount) {\n            _.slideOffset = (_.options.slidesToShow - (slideIndex - _.slideCount)) * _.slideWidth * -1;\n            verticalOffset = (_.options.slidesToShow - (slideIndex - _.slideCount)) * verticalHeight * -1;\n          } else {\n            _.slideOffset = _.slideCount % _.options.slidesToScroll * _.slideWidth * -1;\n            verticalOffset = _.slideCount % _.options.slidesToScroll * verticalHeight * -1;\n          }\n        }\n      }\n    } else {\n      if (slideIndex + _.options.slidesToShow > _.slideCount) {\n        _.slideOffset = (slideIndex + _.options.slidesToShow - _.slideCount) * _.slideWidth;\n        verticalOffset = (slideIndex + _.options.slidesToShow - _.slideCount) * verticalHeight;\n      }\n    }\n    if (_.slideCount <= _.options.slidesToShow) {\n      _.slideOffset = 0;\n      verticalOffset = 0;\n    }\n    if (_.options.centerMode === true && _.slideCount <= _.options.slidesToShow) {\n      _.slideOffset = _.slideWidth * Math.floor(_.options.slidesToShow) / 2 - _.slideWidth * _.slideCount / 2;\n    } else if (_.options.centerMode === true && _.options.infinite === true) {\n      _.slideOffset += _.slideWidth * Math.floor(_.options.slidesToShow / 2) - _.slideWidth;\n    } else if (_.options.centerMode === true) {\n      _.slideOffset = 0;\n      _.slideOffset += _.slideWidth * Math.floor(_.options.slidesToShow / 2);\n    }\n    if (_.options.vertical === false) {\n      targetLeft = slideIndex * _.slideWidth * -1 + _.slideOffset;\n    } else {\n      targetLeft = slideIndex * verticalHeight * -1 + verticalOffset;\n    }\n    if (_.options.variableWidth === true) {\n      if (_.slideCount <= _.options.slidesToShow || _.options.infinite === false) {\n        targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex);\n      } else {\n        targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex + _.options.slidesToShow);\n      }\n      if (_.options.rtl === true) {\n        if (targetSlide[0]) {\n          targetLeft = (_.$slideTrack.width() - targetSlide[0].offsetLeft - targetSlide.width()) * -1;\n        } else {\n          targetLeft = 0;\n        }\n      } else {\n        targetLeft = targetSlide[0] ? targetSlide[0].offsetLeft * -1 : 0;\n      }\n      if (_.options.centerMode === true) {\n        if (_.slideCount <= _.options.slidesToShow || _.options.infinite === false) {\n          targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex);\n        } else {\n          targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex + _.options.slidesToShow + 1);\n        }\n        if (_.options.rtl === true) {\n          if (targetSlide[0]) {\n            targetLeft = (_.$slideTrack.width() - targetSlide[0].offsetLeft - targetSlide.width()) * -1;\n          } else {\n            targetLeft = 0;\n          }\n        } else {\n          targetLeft = targetSlide[0] ? targetSlide[0].offsetLeft * -1 : 0;\n        }\n        targetLeft += (_.$list.width() - targetSlide.outerWidth()) / 2;\n      }\n    }\n    return targetLeft;\n  };\n  Slick.prototype.getOption = Slick.prototype.slickGetOption = function (option) {\n    var _ = this;\n    return _.options[option];\n  };\n  Slick.prototype.getNavigableIndexes = function () {\n    var _ = this,\n      breakPoint = 0,\n      counter = 0,\n      indexes = [],\n      max;\n    if (_.options.infinite === false) {\n      max = _.slideCount;\n    } else {\n      breakPoint = _.options.slidesToScroll * -1;\n      counter = _.options.slidesToScroll * -1;\n      max = _.slideCount * 2;\n    }\n    while (breakPoint < max) {\n      indexes.push(breakPoint);\n      breakPoint = counter + _.options.slidesToScroll;\n      counter += _.options.slidesToScroll <= _.options.slidesToShow ? _.options.slidesToScroll : _.options.slidesToShow;\n    }\n    return indexes;\n  };\n  Slick.prototype.getSlick = function () {\n    return this;\n  };\n  Slick.prototype.getSlideCount = function () {\n    var _ = this,\n      slidesTraversed,\n      swipedSlide,\n      centerOffset;\n    centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0;\n    if (_.options.swipeToSlide === true) {\n      _.$slideTrack.find('.slick-slide').each(function (index, slide) {\n        if (slide.offsetLeft - centerOffset + $(slide).outerWidth() / 2 > _.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      });\n      slidesTraversed = Math.abs($(swipedSlide).attr('data-slick-index') - _.currentSlide) || 1;\n      return slidesTraversed;\n    } else {\n      return _.options.slidesToScroll;\n    }\n  };\n  Slick.prototype.goTo = Slick.prototype.slickGoTo = function (slide, dontAnimate) {\n    var _ = this;\n    _.changeSlide({\n      data: {\n        message: 'index',\n        index: parseInt(slide)\n      }\n    }, dontAnimate);\n  };\n  Slick.prototype.init = function (creation) {\n    var _ = this;\n    if (!$(_.$slider).hasClass('slick-initialized')) {\n      $(_.$slider).addClass('slick-initialized');\n      _.buildRows();\n      _.buildOut();\n      _.setProps();\n      _.startLoad();\n      _.loadSlider();\n      _.initializeEvents();\n      _.updateArrows();\n      _.updateDots();\n      _.checkResponsive(true);\n      _.focusHandler();\n    }\n    if (creation) {\n      _.$slider.trigger('init', [_]);\n    }\n    if (_.options.accessibility === true) {\n      _.initADA();\n    }\n    if (_.options.autoplay) {\n      _.paused = false;\n      _.autoPlay();\n    }\n  };\n  Slick.prototype.initADA = function () {\n    var _ = this,\n      numDotGroups = Math.ceil(_.slideCount / _.options.slidesToShow),\n      tabControlIndexes = _.getNavigableIndexes().filter(function (val) {\n        return val >= 0 && val < _.slideCount;\n      });\n    _.$slides.add(_.$slideTrack.find('.slick-cloned')).attr({\n      'aria-hidden': 'true',\n      'tabindex': '-1'\n    }).find('a, input, button, select').attr({\n      'tabindex': '-1'\n    });\n    if (_.$dots !== null) {\n      _.$slides.not(_.$slideTrack.find('.slick-cloned')).each(function (i) {\n        var slideControlIndex = tabControlIndexes.indexOf(i);\n        $(this).attr({\n          'role': 'tabpanel',\n          'id': 'slick-slide' + _.instanceUid + i,\n          'tabindex': -1\n        });\n        if (slideControlIndex !== -1) {\n          var ariaButtonControl = 'slick-slide-control' + _.instanceUid + slideControlIndex;\n          if ($('#' + ariaButtonControl).length) {\n            $(this).attr({\n              'aria-describedby': ariaButtonControl\n            });\n          }\n        }\n      });\n      _.$dots.attr('role', 'tablist').find('li').each(function (i) {\n        var mappedSlideIndex = tabControlIndexes[i];\n        $(this).attr({\n          'role': 'presentation'\n        });\n        $(this).find('button').first().attr({\n          'role': 'tab',\n          'id': 'slick-slide-control' + _.instanceUid + i,\n          'aria-controls': 'slick-slide' + _.instanceUid + mappedSlideIndex,\n          'aria-label': i + 1 + ' of ' + numDotGroups,\n          'aria-selected': null,\n          'tabindex': '-1'\n        });\n      }).eq(_.currentSlide).find('button').attr({\n        'aria-selected': 'true',\n        'tabindex': '0'\n      }).end();\n    }\n    for (var i = _.currentSlide, max = i + _.options.slidesToShow; i < max; i++) {\n      if (_.options.focusOnChange) {\n        _.$slides.eq(i).attr({\n          'tabindex': '0'\n        });\n      } else {\n        _.$slides.eq(i).removeAttr('tabindex');\n      }\n    }\n    _.activateADA();\n  };\n  Slick.prototype.initArrowEvents = function () {\n    var _ = this;\n    if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n      _.$prevArrow.off('click.slick').on('click.slick', {\n        message: 'previous'\n      }, _.changeSlide);\n      _.$nextArrow.off('click.slick').on('click.slick', {\n        message: 'next'\n      }, _.changeSlide);\n      if (_.options.accessibility === true) {\n        _.$prevArrow.on('keydown.slick', _.keyHandler);\n        _.$nextArrow.on('keydown.slick', _.keyHandler);\n      }\n    }\n  };\n  Slick.prototype.initDotEvents = function () {\n    var _ = this;\n    if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n      $('li', _.$dots).on('click.slick', {\n        message: 'index'\n      }, _.changeSlide);\n      if (_.options.accessibility === true) {\n        _.$dots.on('keydown.slick', _.keyHandler);\n      }\n    }\n    if (_.options.dots === true && _.options.pauseOnDotsHover === true && _.slideCount > _.options.slidesToShow) {\n      $('li', _.$dots).on('mouseenter.slick', $.proxy(_.interrupt, _, true)).on('mouseleave.slick', $.proxy(_.interrupt, _, false));\n    }\n  };\n  Slick.prototype.initSlideEvents = function () {\n    var _ = this;\n    if (_.options.pauseOnHover) {\n      _.$list.on('mouseenter.slick', $.proxy(_.interrupt, _, true));\n      _.$list.on('mouseleave.slick', $.proxy(_.interrupt, _, false));\n    }\n  };\n  Slick.prototype.initializeEvents = function () {\n    var _ = this;\n    _.initArrowEvents();\n    _.initDotEvents();\n    _.initSlideEvents();\n    _.$list.on('touchstart.slick mousedown.slick', {\n      action: 'start'\n    }, _.swipeHandler);\n    _.$list.on('touchmove.slick mousemove.slick', {\n      action: 'move'\n    }, _.swipeHandler);\n    _.$list.on('touchend.slick mouseup.slick', {\n      action: 'end'\n    }, _.swipeHandler);\n    _.$list.on('touchcancel.slick mouseleave.slick', {\n      action: 'end'\n    }, _.swipeHandler);\n    _.$list.on('click.slick', _.clickHandler);\n    $(document).on(_.visibilityChange, $.proxy(_.visibility, _));\n    if (_.options.accessibility === true) {\n      _.$list.on('keydown.slick', _.keyHandler);\n    }\n    if (_.options.focusOnSelect === true) {\n      $(_.$slideTrack).children().on('click.slick', _.selectHandler);\n    }\n    $(window).on('orientationchange.slick.slick-' + _.instanceUid, $.proxy(_.orientationChange, _));\n    $(window).on('resize.slick.slick-' + _.instanceUid, $.proxy(_.resize, _));\n    $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);\n    $(window).on('load.slick.slick-' + _.instanceUid, _.setPosition);\n    $(_.setPosition);\n  };\n  Slick.prototype.initUI = function () {\n    var _ = this;\n    if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n      _.$prevArrow.show();\n      _.$nextArrow.show();\n    }\n    if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n      _.$dots.show();\n    }\n  };\n  Slick.prototype.keyHandler = function (event) {\n    var _ = this;\n    //Dont slide if the cursor is inside the form fields and arrow keys are pressed\n    if (!event.target.tagName.match('TEXTAREA|INPUT|SELECT')) {\n      if (event.keyCode === 37 && _.options.accessibility === true) {\n        _.changeSlide({\n          data: {\n            message: _.options.rtl === true ? 'next' : 'previous'\n          }\n        });\n      } else if (event.keyCode === 39 && _.options.accessibility === true) {\n        _.changeSlide({\n          data: {\n            message: _.options.rtl === true ? 'previous' : 'next'\n          }\n        });\n      }\n    }\n  };\n  Slick.prototype.lazyLoad = function () {\n    var _ = this,\n      loadRange,\n      cloneRange,\n      rangeStart,\n      rangeEnd;\n    function loadImages(imagesScope) {\n      $('img[data-lazy]', imagesScope).each(function () {\n        var image = $(this),\n          imageSource = $(this).attr('data-lazy'),\n          imageSrcSet = $(this).attr('data-srcset'),\n          imageSizes = $(this).attr('data-sizes') || _.$slider.attr('data-sizes'),\n          imageToLoad = document.createElement('img');\n        imageToLoad.onload = function () {\n          image.animate({\n            opacity: 0\n          }, 100, function () {\n            if (imageSrcSet) {\n              image.attr('srcset', imageSrcSet);\n              if (imageSizes) {\n                image.attr('sizes', imageSizes);\n              }\n            }\n            image.attr('src', imageSource).animate({\n              opacity: 1\n            }, 200, function () {\n              image.removeAttr('data-lazy data-srcset data-sizes').removeClass('slick-loading');\n            });\n            _.$slider.trigger('lazyLoaded', [_, image, imageSource]);\n          });\n        };\n        imageToLoad.onerror = function () {\n          image.removeAttr('data-lazy').removeClass('slick-loading').addClass('slick-lazyload-error');\n          _.$slider.trigger('lazyLoadError', [_, image, imageSource]);\n        };\n        imageToLoad.src = imageSource;\n      });\n    }\n    if (_.options.centerMode === true) {\n      if (_.options.infinite === true) {\n        rangeStart = _.currentSlide + (_.options.slidesToShow / 2 + 1);\n        rangeEnd = rangeStart + _.options.slidesToShow + 2;\n      } else {\n        rangeStart = Math.max(0, _.currentSlide - (_.options.slidesToShow / 2 + 1));\n        rangeEnd = 2 + (_.options.slidesToShow / 2 + 1) + _.currentSlide;\n      }\n    } else {\n      rangeStart = _.options.infinite ? _.options.slidesToShow + _.currentSlide : _.currentSlide;\n      rangeEnd = Math.ceil(rangeStart + _.options.slidesToShow);\n      if (_.options.fade === true) {\n        if (rangeStart > 0) rangeStart--;\n        if (rangeEnd <= _.slideCount) rangeEnd++;\n      }\n    }\n    loadRange = _.$slider.find('.slick-slide').slice(rangeStart, rangeEnd);\n    if (_.options.lazyLoad === 'anticipated') {\n      var prevSlide = rangeStart - 1,\n        nextSlide = rangeEnd,\n        $slides = _.$slider.find('.slick-slide');\n      for (var i = 0; i < _.options.slidesToScroll; i++) {\n        if (prevSlide < 0) prevSlide = _.slideCount - 1;\n        loadRange = loadRange.add($slides.eq(prevSlide));\n        loadRange = loadRange.add($slides.eq(nextSlide));\n        prevSlide--;\n        nextSlide++;\n      }\n    }\n    loadImages(loadRange);\n    if (_.slideCount <= _.options.slidesToShow) {\n      cloneRange = _.$slider.find('.slick-slide');\n      loadImages(cloneRange);\n    } else if (_.currentSlide >= _.slideCount - _.options.slidesToShow) {\n      cloneRange = _.$slider.find('.slick-cloned').slice(0, _.options.slidesToShow);\n      loadImages(cloneRange);\n    } else if (_.currentSlide === 0) {\n      cloneRange = _.$slider.find('.slick-cloned').slice(_.options.slidesToShow * -1);\n      loadImages(cloneRange);\n    }\n  };\n  Slick.prototype.loadSlider = function () {\n    var _ = this;\n    _.setPosition();\n    _.$slideTrack.css({\n      opacity: 1\n    });\n    _.$slider.removeClass('slick-loading');\n    _.initUI();\n    if (_.options.lazyLoad === 'progressive') {\n      _.progressiveLazyLoad();\n    }\n  };\n  Slick.prototype.next = Slick.prototype.slickNext = function () {\n    var _ = this;\n    _.changeSlide({\n      data: {\n        message: 'next'\n      }\n    });\n  };\n  Slick.prototype.orientationChange = function () {\n    var _ = this;\n    _.checkResponsive();\n    _.setPosition();\n  };\n  Slick.prototype.pause = Slick.prototype.slickPause = function () {\n    var _ = this;\n    _.autoPlayClear();\n    _.paused = true;\n  };\n  Slick.prototype.play = Slick.prototype.slickPlay = function () {\n    var _ = this;\n    _.autoPlay();\n    _.options.autoplay = true;\n    _.paused = false;\n    _.focussed = false;\n    _.interrupted = false;\n  };\n  Slick.prototype.postSlide = function (index) {\n    var _ = this;\n    if (!_.unslicked) {\n      _.$slider.trigger('afterChange', [_, index]);\n      _.animating = false;\n      if (_.slideCount > _.options.slidesToShow) {\n        _.setPosition();\n      }\n      _.swipeLeft = null;\n      if (_.options.autoplay) {\n        _.autoPlay();\n      }\n      if (_.options.accessibility === true) {\n        _.initADA();\n        if (_.options.focusOnChange) {\n          var $currentSlide = $(_.$slides.get(_.currentSlide));\n          $currentSlide.attr('tabindex', 0).focus();\n        }\n      }\n    }\n  };\n  Slick.prototype.prev = Slick.prototype.slickPrev = function () {\n    var _ = this;\n    _.changeSlide({\n      data: {\n        message: 'previous'\n      }\n    });\n  };\n  Slick.prototype.preventDefault = function (event) {\n    event.preventDefault();\n  };\n  Slick.prototype.progressiveLazyLoad = function (tryCount) {\n    tryCount = tryCount || 1;\n    var _ = this,\n      $imgsToLoad = $('img[data-lazy]', _.$slider),\n      image,\n      imageSource,\n      imageSrcSet,\n      imageSizes,\n      imageToLoad;\n    if ($imgsToLoad.length) {\n      image = $imgsToLoad.first();\n      imageSource = image.attr('data-lazy');\n      imageSrcSet = image.attr('data-srcset');\n      imageSizes = image.attr('data-sizes') || _.$slider.attr('data-sizes');\n      imageToLoad = document.createElement('img');\n      imageToLoad.onload = function () {\n        if (imageSrcSet) {\n          image.attr('srcset', imageSrcSet);\n          if (imageSizes) {\n            image.attr('sizes', imageSizes);\n          }\n        }\n        image.attr('src', imageSource).removeAttr('data-lazy data-srcset data-sizes').removeClass('slick-loading');\n        if (_.options.adaptiveHeight === true) {\n          _.setPosition();\n        }\n        _.$slider.trigger('lazyLoaded', [_, image, imageSource]);\n        _.progressiveLazyLoad();\n      };\n      imageToLoad.onerror = function () {\n        if (tryCount < 3) {\n          /**\n           * try to load the image 3 times,\n           * leave a slight delay so we don't get\n           * servers blocking the request.\n           */\n          setTimeout(function () {\n            _.progressiveLazyLoad(tryCount + 1);\n          }, 500);\n        } else {\n          image.removeAttr('data-lazy').removeClass('slick-loading').addClass('slick-lazyload-error');\n          _.$slider.trigger('lazyLoadError', [_, image, imageSource]);\n          _.progressiveLazyLoad();\n        }\n      };\n      imageToLoad.src = imageSource;\n    } else {\n      _.$slider.trigger('allImagesLoaded', [_]);\n    }\n  };\n  Slick.prototype.refresh = function (initializing) {\n    var _ = this,\n      currentSlide,\n      lastVisibleIndex;\n    lastVisibleIndex = _.slideCount - _.options.slidesToShow;\n\n    // in non-infinite sliders, we don't want to go past the\n    // last visible index.\n    if (!_.options.infinite && _.currentSlide > lastVisibleIndex) {\n      _.currentSlide = lastVisibleIndex;\n    }\n\n    // if less slides than to show, go to start.\n    if (_.slideCount <= _.options.slidesToShow) {\n      _.currentSlide = 0;\n    }\n    currentSlide = _.currentSlide;\n    _.destroy(true);\n    $.extend(_, _.initials, {\n      currentSlide: currentSlide\n    });\n    _.init();\n    if (!initializing) {\n      _.changeSlide({\n        data: {\n          message: 'index',\n          index: currentSlide\n        }\n      }, false);\n    }\n  };\n  Slick.prototype.registerBreakpoints = function () {\n    var _ = this,\n      breakpoint,\n      currentBreakpoint,\n      l,\n      responsiveSettings = _.options.responsive || null;\n    if ($.type(responsiveSettings) === 'array' && responsiveSettings.length) {\n      _.respondTo = _.options.respondTo || 'window';\n      for (breakpoint in responsiveSettings) {\n        l = _.breakpoints.length - 1;\n        if (responsiveSettings.hasOwnProperty(breakpoint)) {\n          currentBreakpoint = responsiveSettings[breakpoint].breakpoint;\n\n          // loop through the breakpoints and cut out any existing\n          // ones with the same breakpoint number, we don't want dupes.\n          while (l >= 0) {\n            if (_.breakpoints[l] && _.breakpoints[l] === currentBreakpoint) {\n              _.breakpoints.splice(l, 1);\n            }\n            l--;\n          }\n          _.breakpoints.push(currentBreakpoint);\n          _.breakpointSettings[currentBreakpoint] = responsiveSettings[breakpoint].settings;\n        }\n      }\n      _.breakpoints.sort(function (a, b) {\n        return _.options.mobileFirst ? a - b : b - a;\n      });\n    }\n  };\n  Slick.prototype.reinit = function () {\n    var _ = this;\n    _.$slides = _.$slideTrack.children(_.options.slide).addClass('slick-slide');\n    _.slideCount = _.$slides.length;\n    if (_.currentSlide >= _.slideCount && _.currentSlide !== 0) {\n      _.currentSlide = _.currentSlide - _.options.slidesToScroll;\n    }\n    if (_.slideCount <= _.options.slidesToShow) {\n      _.currentSlide = 0;\n    }\n    _.registerBreakpoints();\n    _.setProps();\n    _.setupInfinite();\n    _.buildArrows();\n    _.updateArrows();\n    _.initArrowEvents();\n    _.buildDots();\n    _.updateDots();\n    _.initDotEvents();\n    _.cleanUpSlideEvents();\n    _.initSlideEvents();\n    _.checkResponsive(false, true);\n    if (_.options.focusOnSelect === true) {\n      $(_.$slideTrack).children().on('click.slick', _.selectHandler);\n    }\n    _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);\n    _.setPosition();\n    _.focusHandler();\n    _.paused = !_.options.autoplay;\n    _.autoPlay();\n    _.$slider.trigger('reInit', [_]);\n  };\n  Slick.prototype.resize = function () {\n    var _ = this;\n    if ($(window).width() !== _.windowWidth) {\n      clearTimeout(_.windowDelay);\n      _.windowDelay = window.setTimeout(function () {\n        _.windowWidth = $(window).width();\n        _.checkResponsive();\n        if (!_.unslicked) {\n          _.setPosition();\n        }\n      }, 50);\n    }\n  };\n  Slick.prototype.removeSlide = Slick.prototype.slickRemove = function (index, removeBefore, removeAll) {\n    var _ = this;\n    if (typeof index === 'boolean') {\n      removeBefore = index;\n      index = removeBefore === true ? 0 : _.slideCount - 1;\n    } else {\n      index = removeBefore === true ? --index : index;\n    }\n    if (_.slideCount < 1 || index < 0 || index > _.slideCount - 1) {\n      return false;\n    }\n    _.unload();\n    if (removeAll === true) {\n      _.$slideTrack.children().remove();\n    } else {\n      _.$slideTrack.children(this.options.slide).eq(index).remove();\n    }\n    _.$slides = _.$slideTrack.children(this.options.slide);\n    _.$slideTrack.children(this.options.slide).detach();\n    _.$slideTrack.append(_.$slides);\n    _.$slidesCache = _.$slides;\n    _.reinit();\n  };\n  Slick.prototype.setCSS = function (position) {\n    var _ = this,\n      positionProps = {},\n      x,\n      y;\n    if (_.options.rtl === true) {\n      position = -position;\n    }\n    x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px';\n    y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px';\n    positionProps[_.positionProp] = position;\n    if (_.transformsEnabled === false) {\n      _.$slideTrack.css(positionProps);\n    } else {\n      positionProps = {};\n      if (_.cssTransitions === false) {\n        positionProps[_.animType] = 'translate(' + x + ', ' + y + ')';\n        _.$slideTrack.css(positionProps);\n      } else {\n        positionProps[_.animType] = 'translate3d(' + x + ', ' + y + ', 0px)';\n        _.$slideTrack.css(positionProps);\n      }\n    }\n  };\n  Slick.prototype.setDimensions = function () {\n    var _ = this;\n    if (_.options.vertical === false) {\n      if (_.options.centerMode === true) {\n        _.$list.css({\n          padding: '0px ' + _.options.centerPadding\n        });\n      }\n    } else {\n      _.$list.height(_.$slides.first().outerHeight(true) * _.options.slidesToShow);\n      if (_.options.centerMode === true) {\n        _.$list.css({\n          padding: _.options.centerPadding + ' 0px'\n        });\n      }\n    }\n    _.listWidth = _.$list.width();\n    _.listHeight = _.$list.height();\n    if (_.options.vertical === false && _.options.variableWidth === false) {\n      _.slideWidth = Math.ceil(_.listWidth / _.options.slidesToShow);\n      _.$slideTrack.width(Math.ceil(_.slideWidth * _.$slideTrack.children('.slick-slide').length));\n    } else if (_.options.variableWidth === true) {\n      _.$slideTrack.width(5000 * _.slideCount);\n    } else {\n      _.slideWidth = Math.ceil(_.listWidth);\n      _.$slideTrack.height(Math.ceil(_.$slides.first().outerHeight(true) * _.$slideTrack.children('.slick-slide').length));\n    }\n    var offset = _.$slides.first().outerWidth(true) - _.$slides.first().width();\n    if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);\n  };\n  Slick.prototype.setFade = function () {\n    var _ = this,\n      targetLeft;\n    _.$slides.each(function (index, element) {\n      targetLeft = _.slideWidth * index * -1;\n      if (_.options.rtl === true) {\n        $(element).css({\n          position: 'relative',\n          right: targetLeft,\n          top: 0,\n          zIndex: _.options.zIndex - 2,\n          opacity: 0\n        });\n      } else {\n        $(element).css({\n          position: 'relative',\n          left: targetLeft,\n          top: 0,\n          zIndex: _.options.zIndex - 2,\n          opacity: 0\n        });\n      }\n    });\n    _.$slides.eq(_.currentSlide).css({\n      zIndex: _.options.zIndex - 1,\n      opacity: 1\n    });\n  };\n  Slick.prototype.setHeight = function () {\n    var _ = this;\n    if (_.options.slidesToShow === 1 && _.options.adaptiveHeight === true && _.options.vertical === false) {\n      var targetHeight = _.$slides.eq(_.currentSlide).outerHeight(true);\n      _.$list.css('height', targetHeight);\n    }\n  };\n  Slick.prototype.setOption = Slick.prototype.slickSetOption = function () {\n    /**\n     * accepts arguments in format of:\n     *\n     *  - for changing a single option's value:\n     *     .slick(\"setOption\", option, value, refresh )\n     *\n     *  - for changing a set of responsive options:\n     *     .slick(\"setOption\", 'responsive', [{}, ...], refresh )\n     *\n     *  - for updating multiple values at once (not responsive)\n     *     .slick(\"setOption\", { 'option': value, ... }, refresh )\n     */\n\n    var _ = this,\n      l,\n      item,\n      option,\n      value,\n      refresh = false,\n      type;\n    if ($.type(arguments[0]) === 'object') {\n      option = arguments[0];\n      refresh = arguments[1];\n      type = 'multiple';\n    } else if ($.type(arguments[0]) === 'string') {\n      option = arguments[0];\n      value = arguments[1];\n      refresh = arguments[2];\n      if (arguments[0] === 'responsive' && $.type(arguments[1]) === 'array') {\n        type = 'responsive';\n      } else if (typeof arguments[1] !== 'undefined') {\n        type = 'single';\n      }\n    }\n    if (type === 'single') {\n      _.options[option] = value;\n    } else if (type === 'multiple') {\n      $.each(option, function (opt, val) {\n        _.options[opt] = val;\n      });\n    } else if (type === 'responsive') {\n      for (item in value) {\n        if ($.type(_.options.responsive) !== 'array') {\n          _.options.responsive = [value[item]];\n        } else {\n          l = _.options.responsive.length - 1;\n\n          // loop through the responsive object and splice out duplicates.\n          while (l >= 0) {\n            if (_.options.responsive[l].breakpoint === value[item].breakpoint) {\n              _.options.responsive.splice(l, 1);\n            }\n            l--;\n          }\n          _.options.responsive.push(value[item]);\n        }\n      }\n    }\n    if (refresh) {\n      _.unload();\n      _.reinit();\n    }\n  };\n  Slick.prototype.setPosition = function () {\n    var _ = this;\n    _.setDimensions();\n    _.setHeight();\n    if (_.options.fade === false) {\n      _.setCSS(_.getLeft(_.currentSlide));\n    } else {\n      _.setFade();\n    }\n    _.$slider.trigger('setPosition', [_]);\n  };\n  Slick.prototype.setProps = function () {\n    var _ = this,\n      bodyStyle = document.body.style;\n    _.positionProp = _.options.vertical === true ? 'top' : 'left';\n    if (_.positionProp === 'top') {\n      _.$slider.addClass('slick-vertical');\n    } else {\n      _.$slider.removeClass('slick-vertical');\n    }\n    if (bodyStyle.WebkitTransition !== undefined || bodyStyle.MozTransition !== undefined || bodyStyle.msTransition !== undefined) {\n      if (_.options.useCSS === true) {\n        _.cssTransitions = true;\n      }\n    }\n    if (_.options.fade) {\n      if (typeof _.options.zIndex === 'number') {\n        if (_.options.zIndex < 3) {\n          _.options.zIndex = 3;\n        }\n      } else {\n        _.options.zIndex = _.defaults.zIndex;\n      }\n    }\n    if (bodyStyle.OTransform !== undefined) {\n      _.animType = 'OTransform';\n      _.transformType = '-o-transform';\n      _.transitionType = 'OTransition';\n      if (bodyStyle.perspectiveProperty === undefined && bodyStyle.webkitPerspective === undefined) _.animType = false;\n    }\n    if (bodyStyle.MozTransform !== undefined) {\n      _.animType = 'MozTransform';\n      _.transformType = '-moz-transform';\n      _.transitionType = 'MozTransition';\n      if (bodyStyle.perspectiveProperty === undefined && bodyStyle.MozPerspective === undefined) _.animType = false;\n    }\n    if (bodyStyle.webkitTransform !== undefined) {\n      _.animType = 'webkitTransform';\n      _.transformType = '-webkit-transform';\n      _.transitionType = 'webkitTransition';\n      if (bodyStyle.perspectiveProperty === undefined && bodyStyle.webkitPerspective === undefined) _.animType = false;\n    }\n    if (bodyStyle.msTransform !== undefined) {\n      _.animType = 'msTransform';\n      _.transformType = '-ms-transform';\n      _.transitionType = 'msTransition';\n      if (bodyStyle.msTransform === undefined) _.animType = false;\n    }\n    if (bodyStyle.transform !== undefined && _.animType !== false) {\n      _.animType = 'transform';\n      _.transformType = 'transform';\n      _.transitionType = 'transition';\n    }\n    _.transformsEnabled = _.options.useTransform && _.animType !== null && _.animType !== false;\n  };\n  Slick.prototype.setSlideClasses = function (index) {\n    var _ = this,\n      centerOffset,\n      allSlides,\n      indexOffset,\n      remainder;\n    allSlides = _.$slider.find('.slick-slide').removeClass('slick-active slick-center slick-current').attr('aria-hidden', 'true');\n    _.$slides.eq(index).addClass('slick-current');\n    if (_.options.centerMode === true) {\n      var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0;\n      centerOffset = Math.floor(_.options.slidesToShow / 2);\n      if (_.options.infinite === true) {\n        if (index >= centerOffset && index <= _.slideCount - 1 - centerOffset) {\n          _.$slides.slice(index - centerOffset + evenCoef, index + centerOffset + 1).addClass('slick-active').attr('aria-hidden', 'false');\n        } else {\n          indexOffset = _.options.slidesToShow + index;\n          allSlides.slice(indexOffset - centerOffset + 1 + evenCoef, indexOffset + centerOffset + 2).addClass('slick-active').attr('aria-hidden', 'false');\n        }\n        if (index === 0) {\n          allSlides.eq(allSlides.length - 1 - _.options.slidesToShow).addClass('slick-center');\n        } else if (index === _.slideCount - 1) {\n          allSlides.eq(_.options.slidesToShow).addClass('slick-center');\n        }\n      }\n      _.$slides.eq(index).addClass('slick-center');\n    } else {\n      if (index >= 0 && index <= _.slideCount - _.options.slidesToShow) {\n        _.$slides.slice(index, index + _.options.slidesToShow).addClass('slick-active').attr('aria-hidden', 'false');\n      } else if (allSlides.length <= _.options.slidesToShow) {\n        allSlides.addClass('slick-active').attr('aria-hidden', 'false');\n      } else {\n        remainder = _.slideCount % _.options.slidesToShow;\n        indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index;\n        if (_.options.slidesToShow == _.options.slidesToScroll && _.slideCount - index < _.options.slidesToShow) {\n          allSlides.slice(indexOffset - (_.options.slidesToShow - remainder), indexOffset + remainder).addClass('slick-active').attr('aria-hidden', 'false');\n        } else {\n          allSlides.slice(indexOffset, indexOffset + _.options.slidesToShow).addClass('slick-active').attr('aria-hidden', 'false');\n        }\n      }\n    }\n    if (_.options.lazyLoad === 'ondemand' || _.options.lazyLoad === 'anticipated') {\n      _.lazyLoad();\n    }\n  };\n  Slick.prototype.setupInfinite = function () {\n    var _ = this,\n      i,\n      slideIndex,\n      infiniteCount;\n    if (_.options.fade === true) {\n      _.options.centerMode = false;\n    }\n    if (_.options.infinite === true && _.options.fade === false) {\n      slideIndex = null;\n      if (_.slideCount > _.options.slidesToShow) {\n        if (_.options.centerMode === true) {\n          infiniteCount = _.options.slidesToShow + 1;\n        } else {\n          infiniteCount = _.options.slidesToShow;\n        }\n        for (i = _.slideCount; i > _.slideCount - infiniteCount; i -= 1) {\n          slideIndex = i - 1;\n          $(_.$slides[slideIndex]).clone(true).attr('id', '').attr('data-slick-index', slideIndex - _.slideCount).prependTo(_.$slideTrack).addClass('slick-cloned');\n        }\n        for (i = 0; i < infiniteCount + _.slideCount; i += 1) {\n          slideIndex = i;\n          $(_.$slides[slideIndex]).clone(true).attr('id', '').attr('data-slick-index', slideIndex + _.slideCount).appendTo(_.$slideTrack).addClass('slick-cloned');\n        }\n        _.$slideTrack.find('.slick-cloned').find('[id]').each(function () {\n          $(this).attr('id', '');\n        });\n      }\n    }\n  };\n  Slick.prototype.interrupt = function (toggle) {\n    var _ = this;\n    if (!toggle) {\n      _.autoPlay();\n    }\n    _.interrupted = toggle;\n  };\n  Slick.prototype.selectHandler = function (event) {\n    var _ = this;\n    var targetElement = $(event.target).is('.slick-slide') ? $(event.target) : $(event.target).parents('.slick-slide');\n    var index = parseInt(targetElement.attr('data-slick-index'));\n    if (!index) index = 0;\n    if (_.slideCount <= _.options.slidesToShow) {\n      _.slideHandler(index, false, true);\n      return;\n    }\n    _.slideHandler(index);\n  };\n  Slick.prototype.slideHandler = function (index, sync, dontAnimate) {\n    var targetSlide,\n      animSlide,\n      oldSlide,\n      slideLeft,\n      targetLeft = null,\n      _ = this,\n      navTarget;\n    sync = sync || false;\n    if (_.animating === true && _.options.waitForAnimate === true) {\n      return;\n    }\n    if (_.options.fade === true && _.currentSlide === index) {\n      return;\n    }\n    if (sync === false) {\n      _.asNavFor(index);\n    }\n    targetSlide = index;\n    targetLeft = _.getLeft(targetSlide);\n    slideLeft = _.getLeft(_.currentSlide);\n    _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft;\n    if (_.options.infinite === false && _.options.centerMode === false && (index < 0 || index > _.getDotCount() * _.options.slidesToScroll)) {\n      if (_.options.fade === false) {\n        targetSlide = _.currentSlide;\n        if (dontAnimate !== true && _.slideCount > _.options.slidesToShow) {\n          _.animateSlide(slideLeft, function () {\n            _.postSlide(targetSlide);\n          });\n        } else {\n          _.postSlide(targetSlide);\n        }\n      }\n      return;\n    } else if (_.options.infinite === false && _.options.centerMode === true && (index < 0 || index > _.slideCount - _.options.slidesToScroll)) {\n      if (_.options.fade === false) {\n        targetSlide = _.currentSlide;\n        if (dontAnimate !== true && _.slideCount > _.options.slidesToShow) {\n          _.animateSlide(slideLeft, function () {\n            _.postSlide(targetSlide);\n          });\n        } else {\n          _.postSlide(targetSlide);\n        }\n      }\n      return;\n    }\n    if (_.options.autoplay) {\n      clearInterval(_.autoPlayTimer);\n    }\n    if (targetSlide < 0) {\n      if (_.slideCount % _.options.slidesToScroll !== 0) {\n        animSlide = _.slideCount - _.slideCount % _.options.slidesToScroll;\n      } else {\n        animSlide = _.slideCount + targetSlide;\n      }\n    } else if (targetSlide >= _.slideCount) {\n      if (_.slideCount % _.options.slidesToScroll !== 0) {\n        animSlide = 0;\n      } else {\n        animSlide = targetSlide - _.slideCount;\n      }\n    } else {\n      animSlide = targetSlide;\n    }\n    _.animating = true;\n    _.$slider.trigger('beforeChange', [_, _.currentSlide, animSlide]);\n    oldSlide = _.currentSlide;\n    _.currentSlide = animSlide;\n    _.setSlideClasses(_.currentSlide);\n    if (_.options.asNavFor) {\n      navTarget = _.getNavTarget();\n      navTarget = navTarget.slick('getSlick');\n      if (navTarget.slideCount <= navTarget.options.slidesToShow) {\n        navTarget.setSlideClasses(_.currentSlide);\n      }\n    }\n    _.updateDots();\n    _.updateArrows();\n    if (_.options.fade === true) {\n      if (dontAnimate !== true) {\n        _.fadeSlideOut(oldSlide);\n        _.fadeSlide(animSlide, function () {\n          _.postSlide(animSlide);\n        });\n      } else {\n        _.postSlide(animSlide);\n      }\n      _.animateHeight();\n      return;\n    }\n    if (dontAnimate !== true && _.slideCount > _.options.slidesToShow) {\n      _.animateSlide(targetLeft, function () {\n        _.postSlide(animSlide);\n      });\n    } else {\n      _.postSlide(animSlide);\n    }\n  };\n  Slick.prototype.startLoad = function () {\n    var _ = this;\n    if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n      _.$prevArrow.hide();\n      _.$nextArrow.hide();\n    }\n    if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n      _.$dots.hide();\n    }\n    _.$slider.addClass('slick-loading');\n  };\n  Slick.prototype.swipeDirection = function () {\n    var xDist,\n      yDist,\n      r,\n      swipeAngle,\n      _ = this;\n    xDist = _.touchObject.startX - _.touchObject.curX;\n    yDist = _.touchObject.startY - _.touchObject.curY;\n    r = Math.atan2(yDist, xDist);\n    swipeAngle = Math.round(r * 180 / Math.PI);\n    if (swipeAngle < 0) {\n      swipeAngle = 360 - Math.abs(swipeAngle);\n    }\n    if (swipeAngle <= 45 && swipeAngle >= 0) {\n      return _.options.rtl === false ? 'left' : 'right';\n    }\n    if (swipeAngle <= 360 && swipeAngle >= 315) {\n      return _.options.rtl === false ? 'left' : 'right';\n    }\n    if (swipeAngle >= 135 && swipeAngle <= 225) {\n      return _.options.rtl === false ? 'right' : 'left';\n    }\n    if (_.options.verticalSwiping === true) {\n      if (swipeAngle >= 35 && swipeAngle <= 135) {\n        return 'down';\n      } else {\n        return 'up';\n      }\n    }\n    return 'vertical';\n  };\n  Slick.prototype.swipeEnd = function (event) {\n    var _ = this,\n      slideCount,\n      direction;\n    _.dragging = false;\n    _.swiping = false;\n    if (_.scrolling) {\n      _.scrolling = false;\n      return false;\n    }\n    _.interrupted = false;\n    _.shouldClick = _.touchObject.swipeLength > 10 ? false : true;\n    if (_.touchObject.curX === undefined) {\n      return false;\n    }\n    if (_.touchObject.edgeHit === true) {\n      _.$slider.trigger('edge', [_, _.swipeDirection()]);\n    }\n    if (_.touchObject.swipeLength >= _.touchObject.minSwipe) {\n      direction = _.swipeDirection();\n      switch (direction) {\n        case 'left':\n        case 'down':\n          slideCount = _.options.swipeToSlide ? _.checkNavigable(_.currentSlide + _.getSlideCount()) : _.currentSlide + _.getSlideCount();\n          _.currentDirection = 0;\n          break;\n        case 'right':\n        case 'up':\n          slideCount = _.options.swipeToSlide ? _.checkNavigable(_.currentSlide - _.getSlideCount()) : _.currentSlide - _.getSlideCount();\n          _.currentDirection = 1;\n          break;\n        default:\n      }\n      if (direction != 'vertical') {\n        _.slideHandler(slideCount);\n        _.touchObject = {};\n        _.$slider.trigger('swipe', [_, direction]);\n      }\n    } else {\n      if (_.touchObject.startX !== _.touchObject.curX) {\n        _.slideHandler(_.currentSlide);\n        _.touchObject = {};\n      }\n    }\n  };\n  Slick.prototype.swipeHandler = function (event) {\n    var _ = this;\n    if (_.options.swipe === false || 'ontouchend' in document && _.options.swipe === false) {\n      return;\n    } else if (_.options.draggable === false && event.type.indexOf('mouse') !== -1) {\n      return;\n    }\n    _.touchObject.fingerCount = event.originalEvent && event.originalEvent.touches !== undefined ? event.originalEvent.touches.length : 1;\n    _.touchObject.minSwipe = _.listWidth / _.options.touchThreshold;\n    if (_.options.verticalSwiping === true) {\n      _.touchObject.minSwipe = _.listHeight / _.options.touchThreshold;\n    }\n    switch (event.data.action) {\n      case 'start':\n        _.swipeStart(event);\n        break;\n      case 'move':\n        _.swipeMove(event);\n        break;\n      case 'end':\n        _.swipeEnd(event);\n        break;\n    }\n  };\n  Slick.prototype.swipeMove = function (event) {\n    var _ = this,\n      edgeWasHit = false,\n      curLeft,\n      swipeDirection,\n      swipeLength,\n      positionOffset,\n      touches,\n      verticalSwipeLength;\n    touches = event.originalEvent !== undefined ? event.originalEvent.touches : null;\n    if (!_.dragging || _.scrolling || touches && touches.length !== 1) {\n      return false;\n    }\n    curLeft = _.getLeft(_.currentSlide);\n    _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX;\n    _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY;\n    _.touchObject.swipeLength = Math.round(Math.sqrt(Math.pow(_.touchObject.curX - _.touchObject.startX, 2)));\n    verticalSwipeLength = Math.round(Math.sqrt(Math.pow(_.touchObject.curY - _.touchObject.startY, 2)));\n    if (!_.options.verticalSwiping && !_.swiping && verticalSwipeLength > 4) {\n      _.scrolling = true;\n      return false;\n    }\n    if (_.options.verticalSwiping === true) {\n      _.touchObject.swipeLength = verticalSwipeLength;\n    }\n    swipeDirection = _.swipeDirection();\n    if (event.originalEvent !== undefined && _.touchObject.swipeLength > 4) {\n      _.swiping = true;\n      event.preventDefault();\n    }\n    positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);\n    if (_.options.verticalSwiping === true) {\n      positionOffset = _.touchObject.curY > _.touchObject.startY ? 1 : -1;\n    }\n    swipeLength = _.touchObject.swipeLength;\n    _.touchObject.edgeHit = false;\n    if (_.options.infinite === false) {\n      if (_.currentSlide === 0 && swipeDirection === 'right' || _.currentSlide >= _.getDotCount() && swipeDirection === 'left') {\n        swipeLength = _.touchObject.swipeLength * _.options.edgeFriction;\n        _.touchObject.edgeHit = true;\n      }\n    }\n    if (_.options.vertical === false) {\n      _.swipeLeft = curLeft + swipeLength * positionOffset;\n    } else {\n      _.swipeLeft = curLeft + swipeLength * (_.$list.height() / _.listWidth) * positionOffset;\n    }\n    if (_.options.verticalSwiping === true) {\n      _.swipeLeft = curLeft + swipeLength * positionOffset;\n    }\n    if (_.options.fade === true || _.options.touchMove === false) {\n      return false;\n    }\n    if (_.animating === true) {\n      _.swipeLeft = null;\n      return false;\n    }\n    _.setCSS(_.swipeLeft);\n  };\n  Slick.prototype.swipeStart = function (event) {\n    var _ = this,\n      touches;\n    _.interrupted = true;\n    if (_.touchObject.fingerCount !== 1 || _.slideCount <= _.options.slidesToShow) {\n      _.touchObject = {};\n      return false;\n    }\n    if (event.originalEvent !== undefined && event.originalEvent.touches !== undefined) {\n      touches = event.originalEvent.touches[0];\n    }\n    _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX;\n    _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY;\n    _.dragging = true;\n  };\n  Slick.prototype.unfilterSlides = Slick.prototype.slickUnfilter = function () {\n    var _ = this;\n    if (_.$slidesCache !== null) {\n      _.unload();\n      _.$slideTrack.children(this.options.slide).detach();\n      _.$slidesCache.appendTo(_.$slideTrack);\n      _.reinit();\n    }\n  };\n  Slick.prototype.unload = function () {\n    var _ = this;\n    $('.slick-cloned', _.$slider).remove();\n    if (_.$dots) {\n      _.$dots.remove();\n    }\n    if (_.$prevArrow && _.htmlExpr.test(_.options.prevArrow)) {\n      _.$prevArrow.remove();\n    }\n    if (_.$nextArrow && _.htmlExpr.test(_.options.nextArrow)) {\n      _.$nextArrow.remove();\n    }\n    _.$slides.removeClass('slick-slide slick-active slick-visible slick-current').attr('aria-hidden', 'true').css('width', '');\n  };\n  Slick.prototype.unslick = function (fromBreakpoint) {\n    var _ = this;\n    _.$slider.trigger('unslick', [_, fromBreakpoint]);\n    _.destroy();\n  };\n  Slick.prototype.updateArrows = function () {\n    var _ = this,\n      centerOffset;\n    centerOffset = Math.floor(_.options.slidesToShow / 2);\n    if (_.options.arrows === true && _.slideCount > _.options.slidesToShow && !_.options.infinite) {\n      _.$prevArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n      _.$nextArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n      if (_.currentSlide === 0) {\n        _.$prevArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n        _.$nextArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n      } else if (_.currentSlide >= _.slideCount - _.options.slidesToShow && _.options.centerMode === false) {\n        _.$nextArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n        _.$prevArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n      } else if (_.currentSlide >= _.slideCount - 1 && _.options.centerMode === true) {\n        _.$nextArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n        _.$prevArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n      }\n    }\n  };\n  Slick.prototype.updateDots = function () {\n    var _ = this;\n    if (_.$dots !== null) {\n      _.$dots.find('li').removeClass('slick-active').end();\n      _.$dots.find('li').eq(Math.floor(_.currentSlide / _.options.slidesToScroll)).addClass('slick-active');\n    }\n  };\n  Slick.prototype.visibility = function () {\n    var _ = this;\n    if (_.options.autoplay) {\n      if (document[_.hidden]) {\n        _.interrupted = true;\n      } else {\n        _.interrupted = false;\n      }\n    }\n  };\n  $.fn.slick = function () {\n    var _ = this,\n      opt = arguments[0],\n      args = Array.prototype.slice.call(arguments, 1),\n      l = _.length,\n      i,\n      ret;\n    for (i = 0; i < l; i++) {\n      if (typeof opt == 'object' || typeof opt == 'undefined') _[i].slick = new Slick(_[i], opt);else ret = _[i].slick[opt].apply(_[i].slick, args);\n      if (typeof ret != 'undefined') return ret;\n    }\n    return _;\n  };\n});", "map": {"version": 3, "names": ["factory", "define", "amd", "exports", "module", "require", "j<PERSON><PERSON><PERSON>", "$", "Slick", "window", "instanceUid", "element", "settings", "_", "dataSettings", "defaults", "accessibility", "adaptiveHeight", "appendArrows", "appendDots", "arrows", "asNavFor", "prevArrow", "nextArrow", "autoplay", "autoplaySpeed", "centerMode", "centerPadding", "cssEase", "customPaging", "slider", "i", "text", "dots", "dotsClass", "draggable", "easing", "edgeFriction", "fade", "focusOnSelect", "focusOnChange", "infinite", "initialSlide", "lazyLoad", "mobileFirst", "pauseOnHover", "pauseOnFocus", "pauseOnDotsHover", "respondTo", "responsive", "rows", "rtl", "slide", "slidesPerRow", "slidesToShow", "slidesToScroll", "speed", "swipe", "swipeToSlide", "touchMove", "touchThreshold", "useCSS", "useTransform", "variableWidth", "vertical", "verticalSwiping", "waitForAnimate", "zIndex", "initials", "animating", "dragging", "autoPlayTimer", "currentDirection", "currentLeft", "currentSlide", "direction", "$dots", "listWidth", "listHeight", "loadIndex", "$nextArrow", "$prevArrow", "scrolling", "slideCount", "slideWidth", "$slideTrack", "$slides", "sliding", "slideOffset", "swipeLeft", "swiping", "$list", "touchObject", "transformsEnabled", "unslicked", "extend", "activeBreakpoint", "animType", "animProp", "breakpoints", "breakpointSettings", "cssTransitions", "focussed", "interrupted", "hidden", "paused", "positionProp", "rowCount", "shouldClick", "$slider", "$slidesCache", "transformType", "transitionType", "visibilityChange", "windowWidth", "windowTimer", "data", "options", "originalSettings", "document", "mozHidden", "webkitHidden", "autoPlay", "proxy", "autoPlayClear", "autoPlayIterator", "changeSlide", "clickHandler", "<PERSON><PERSON><PERSON><PERSON>", "setPosition", "swi<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "htmlExpr", "registerBreakpoints", "init", "prototype", "activateADA", "find", "attr", "addSlide", "<PERSON><PERSON><PERSON>", "markup", "index", "addBefore", "unload", "length", "appendTo", "insertBefore", "eq", "insertAfter", "prependTo", "children", "detach", "append", "each", "reinit", "animateHeight", "targetHeight", "outerHeight", "animate", "height", "animateSlide", "targetLeft", "callback", "animProps", "left", "top", "animStart", "duration", "step", "now", "Math", "ceil", "css", "complete", "call", "applyTransition", "setTimeout", "disableTransition", "getNavTarget", "not", "target", "slick", "<PERSON><PERSON><PERSON><PERSON>", "transition", "setInterval", "clearInterval", "slideTo", "buildArrows", "addClass", "removeClass", "removeAttr", "test", "add", "buildDots", "dot", "getDotCount", "first", "buildOut", "wrapAll", "parent", "wrap", "setupInfinite", "updateDots", "setSlideClasses", "buildRows", "a", "b", "c", "newSlides", "numOfSlides", "originalSlides", "slidesPerSection", "createDocumentFragment", "createElement", "row", "get", "append<PERSON><PERSON><PERSON>", "empty", "checkResponsive", "initial", "forceUpdate", "breakpoint", "targetBreakpoint", "respondToWidth", "triggerBreakpoint", "slider<PERSON><PERSON><PERSON>", "width", "innerWidth", "min", "hasOwnProperty", "unslick", "refresh", "trigger", "event", "dontAnimate", "$target", "currentTarget", "indexOffset", "unevenOffset", "is", "preventDefault", "closest", "message", "checkNavigable", "navigables", "prevNavigable", "getNavigableIndexes", "n", "cleanUpEvents", "off", "interrupt", "visibility", "cleanUpSlideEvents", "orientationChange", "resize", "cleanUpRows", "stopImmediatePropagation", "stopPropagation", "destroy", "remove", "fadeSlide", "slideIndex", "opacity", "fadeSlideOut", "filterSlides", "slickFilter", "filter", "focusHandler", "on", "$sf", "get<PERSON>urrent", "slickCurrentSlide", "breakPoint", "counter", "pager<PERSON><PERSON>", "getLeft", "verticalHeight", "verticalOffset", "targetSlide", "coef", "floor", "offsetLeft", "outerWidth", "getOption", "slickGetOption", "option", "indexes", "max", "push", "getSlick", "getSlideCount", "slidesTraversed", "swipedSlide", "centerOffset", "abs", "goTo", "slickGoTo", "parseInt", "creation", "hasClass", "setProps", "startLoad", "loadSlider", "initializeEvents", "updateArrows", "initADA", "numDotGroups", "tabControlIndexes", "val", "slideControlIndex", "indexOf", "ariaButtonControl", "mappedSlideIndex", "end", "initArrowEvents", "initDotEvents", "initSlideEvents", "action", "initUI", "show", "tagName", "match", "keyCode", "loadRange", "cloneRange", "rangeStart", "rangeEnd", "loadImages", "imagesScope", "image", "imageSource", "imageSrcSet", "imageSizes", "imageToLoad", "onload", "onerror", "src", "slice", "prevSlide", "nextSlide", "progressiveLazyLoad", "next", "slickNext", "pause", "slickPause", "play", "slickPlay", "postSlide", "$currentSlide", "focus", "prev", "slick<PERSON>rev", "tryCount", "$imgsToLoad", "initializing", "lastVisibleIndex", "currentBreakpoint", "l", "responsiveSettings", "type", "splice", "sort", "clearTimeout", "windowDelay", "removeSlide", "slickRemove", "removeBefore", "removeAll", "setCSS", "position", "positionProps", "x", "y", "setDimensions", "padding", "offset", "setFade", "right", "setHeight", "setOption", "slickSetOption", "item", "value", "arguments", "opt", "bodyStyle", "body", "style", "WebkitTransition", "undefined", "MozTransition", "msTransition", "OTransform", "perspectiveProperty", "webkitPerspective", "MozTransform", "MozPerspective", "webkitTransform", "msTransform", "transform", "allSlides", "remainder", "<PERSON><PERSON><PERSON><PERSON>", "infiniteCount", "clone", "toggle", "targetElement", "parents", "sync", "animSlide", "oldSlide", "slideLeft", "navTarget", "hide", "swipeDirection", "xDist", "yDist", "r", "swipeAngle", "startX", "curX", "startY", "curY", "atan2", "round", "PI", "swipeEnd", "swipe<PERSON><PERSON><PERSON>", "edgeHit", "minSwipe", "fingerCount", "originalEvent", "touches", "swipeStart", "swipeMove", "edgeWasHit", "curL<PERSON>t", "positionOffset", "verticalSwipeLength", "pageX", "clientX", "pageY", "clientY", "sqrt", "pow", "unfilterSlides", "slickUnfilter", "fromBreakpoint", "fn", "args", "Array", "ret", "apply"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/slick-carousel/slick/slick.js"], "sourcesContent": ["/*\n     _ _      _       _\n ___| (_) ___| | __  (_)___\n/ __| | |/ __| |/ /  | / __|\n\\__ \\ | | (__|   < _ | \\__ \\\n|___/_|_|\\___|_|\\_(_)/ |___/\n                   |__/\n\n Version: 1.8.1\n  Author: <PERSON>\n Website: http://kenwheeler.github.io\n    Docs: http://kenwheeler.github.io/slick\n    Repo: http://github.com/kenwheeler/slick\n  Issues: http://github.com/kenwheeler/slick/issues\n\n */\n/* global window, document, define, jQuery, setInterval, clearInterval */\n;(function(factory) {\n    'use strict';\n    if (typeof define === 'function' && define.amd) {\n        define(['jquery'], factory);\n    } else if (typeof exports !== 'undefined') {\n        module.exports = factory(require('jquery'));\n    } else {\n        factory(jQuery);\n    }\n\n}(function($) {\n    'use strict';\n    var Slick = window.Slick || {};\n\n    Slick = (function() {\n\n        var instanceUid = 0;\n\n        function Slick(element, settings) {\n\n            var _ = this, dataSettings;\n\n            _.defaults = {\n                accessibility: true,\n                adaptiveHeight: false,\n                appendArrows: $(element),\n                appendDots: $(element),\n                arrows: true,\n                asNavFor: null,\n                prevArrow: '<button class=\"slick-prev\" aria-label=\"Previous\" type=\"button\">Previous</button>',\n                nextArrow: '<button class=\"slick-next\" aria-label=\"Next\" type=\"button\">Next</button>',\n                autoplay: false,\n                autoplaySpeed: 3000,\n                centerMode: false,\n                centerPadding: '50px',\n                cssEase: 'ease',\n                customPaging: function(slider, i) {\n                    return $('<button type=\"button\" />').text(i + 1);\n                },\n                dots: false,\n                dotsClass: 'slick-dots',\n                draggable: true,\n                easing: 'linear',\n                edgeFriction: 0.35,\n                fade: false,\n                focusOnSelect: false,\n                focusOnChange: false,\n                infinite: true,\n                initialSlide: 0,\n                lazyLoad: 'ondemand',\n                mobileFirst: false,\n                pauseOnHover: true,\n                pauseOnFocus: true,\n                pauseOnDotsHover: false,\n                respondTo: 'window',\n                responsive: null,\n                rows: 1,\n                rtl: false,\n                slide: '',\n                slidesPerRow: 1,\n                slidesToShow: 1,\n                slidesToScroll: 1,\n                speed: 500,\n                swipe: true,\n                swipeToSlide: false,\n                touchMove: true,\n                touchThreshold: 5,\n                useCSS: true,\n                useTransform: true,\n                variableWidth: false,\n                vertical: false,\n                verticalSwiping: false,\n                waitForAnimate: true,\n                zIndex: 1000\n            };\n\n            _.initials = {\n                animating: false,\n                dragging: false,\n                autoPlayTimer: null,\n                currentDirection: 0,\n                currentLeft: null,\n                currentSlide: 0,\n                direction: 1,\n                $dots: null,\n                listWidth: null,\n                listHeight: null,\n                loadIndex: 0,\n                $nextArrow: null,\n                $prevArrow: null,\n                scrolling: false,\n                slideCount: null,\n                slideWidth: null,\n                $slideTrack: null,\n                $slides: null,\n                sliding: false,\n                slideOffset: 0,\n                swipeLeft: null,\n                swiping: false,\n                $list: null,\n                touchObject: {},\n                transformsEnabled: false,\n                unslicked: false\n            };\n\n            $.extend(_, _.initials);\n\n            _.activeBreakpoint = null;\n            _.animType = null;\n            _.animProp = null;\n            _.breakpoints = [];\n            _.breakpointSettings = [];\n            _.cssTransitions = false;\n            _.focussed = false;\n            _.interrupted = false;\n            _.hidden = 'hidden';\n            _.paused = true;\n            _.positionProp = null;\n            _.respondTo = null;\n            _.rowCount = 1;\n            _.shouldClick = true;\n            _.$slider = $(element);\n            _.$slidesCache = null;\n            _.transformType = null;\n            _.transitionType = null;\n            _.visibilityChange = 'visibilitychange';\n            _.windowWidth = 0;\n            _.windowTimer = null;\n\n            dataSettings = $(element).data('slick') || {};\n\n            _.options = $.extend({}, _.defaults, settings, dataSettings);\n\n            _.currentSlide = _.options.initialSlide;\n\n            _.originalSettings = _.options;\n\n            if (typeof document.mozHidden !== 'undefined') {\n                _.hidden = 'mozHidden';\n                _.visibilityChange = 'mozvisibilitychange';\n            } else if (typeof document.webkitHidden !== 'undefined') {\n                _.hidden = 'webkitHidden';\n                _.visibilityChange = 'webkitvisibilitychange';\n            }\n\n            _.autoPlay = $.proxy(_.autoPlay, _);\n            _.autoPlayClear = $.proxy(_.autoPlayClear, _);\n            _.autoPlayIterator = $.proxy(_.autoPlayIterator, _);\n            _.changeSlide = $.proxy(_.changeSlide, _);\n            _.clickHandler = $.proxy(_.clickHandler, _);\n            _.selectHandler = $.proxy(_.selectHandler, _);\n            _.setPosition = $.proxy(_.setPosition, _);\n            _.swipeHandler = $.proxy(_.swipeHandler, _);\n            _.dragHandler = $.proxy(_.dragHandler, _);\n            _.keyHandler = $.proxy(_.keyHandler, _);\n\n            _.instanceUid = instanceUid++;\n\n            // A simple way to check for HTML strings\n            // Strict HTML recognition (must start with <)\n            // Extracted from jQuery v1.11 source\n            _.htmlExpr = /^(?:\\s*(<[\\w\\W]+>)[^>]*)$/;\n\n\n            _.registerBreakpoints();\n            _.init(true);\n\n        }\n\n        return Slick;\n\n    }());\n\n    Slick.prototype.activateADA = function() {\n        var _ = this;\n\n        _.$slideTrack.find('.slick-active').attr({\n            'aria-hidden': 'false'\n        }).find('a, input, button, select').attr({\n            'tabindex': '0'\n        });\n\n    };\n\n    Slick.prototype.addSlide = Slick.prototype.slickAdd = function(markup, index, addBefore) {\n\n        var _ = this;\n\n        if (typeof(index) === 'boolean') {\n            addBefore = index;\n            index = null;\n        } else if (index < 0 || (index >= _.slideCount)) {\n            return false;\n        }\n\n        _.unload();\n\n        if (typeof(index) === 'number') {\n            if (index === 0 && _.$slides.length === 0) {\n                $(markup).appendTo(_.$slideTrack);\n            } else if (addBefore) {\n                $(markup).insertBefore(_.$slides.eq(index));\n            } else {\n                $(markup).insertAfter(_.$slides.eq(index));\n            }\n        } else {\n            if (addBefore === true) {\n                $(markup).prependTo(_.$slideTrack);\n            } else {\n                $(markup).appendTo(_.$slideTrack);\n            }\n        }\n\n        _.$slides = _.$slideTrack.children(this.options.slide);\n\n        _.$slideTrack.children(this.options.slide).detach();\n\n        _.$slideTrack.append(_.$slides);\n\n        _.$slides.each(function(index, element) {\n            $(element).attr('data-slick-index', index);\n        });\n\n        _.$slidesCache = _.$slides;\n\n        _.reinit();\n\n    };\n\n    Slick.prototype.animateHeight = function() {\n        var _ = this;\n        if (_.options.slidesToShow === 1 && _.options.adaptiveHeight === true && _.options.vertical === false) {\n            var targetHeight = _.$slides.eq(_.currentSlide).outerHeight(true);\n            _.$list.animate({\n                height: targetHeight\n            }, _.options.speed);\n        }\n    };\n\n    Slick.prototype.animateSlide = function(targetLeft, callback) {\n\n        var animProps = {},\n            _ = this;\n\n        _.animateHeight();\n\n        if (_.options.rtl === true && _.options.vertical === false) {\n            targetLeft = -targetLeft;\n        }\n        if (_.transformsEnabled === false) {\n            if (_.options.vertical === false) {\n                _.$slideTrack.animate({\n                    left: targetLeft\n                }, _.options.speed, _.options.easing, callback);\n            } else {\n                _.$slideTrack.animate({\n                    top: targetLeft\n                }, _.options.speed, _.options.easing, callback);\n            }\n\n        } else {\n\n            if (_.cssTransitions === false) {\n                if (_.options.rtl === true) {\n                    _.currentLeft = -(_.currentLeft);\n                }\n                $({\n                    animStart: _.currentLeft\n                }).animate({\n                    animStart: targetLeft\n                }, {\n                    duration: _.options.speed,\n                    easing: _.options.easing,\n                    step: function(now) {\n                        now = Math.ceil(now);\n                        if (_.options.vertical === false) {\n                            animProps[_.animType] = 'translate(' +\n                                now + 'px, 0px)';\n                            _.$slideTrack.css(animProps);\n                        } else {\n                            animProps[_.animType] = 'translate(0px,' +\n                                now + 'px)';\n                            _.$slideTrack.css(animProps);\n                        }\n                    },\n                    complete: function() {\n                        if (callback) {\n                            callback.call();\n                        }\n                    }\n                });\n\n            } else {\n\n                _.applyTransition();\n                targetLeft = Math.ceil(targetLeft);\n\n                if (_.options.vertical === false) {\n                    animProps[_.animType] = 'translate3d(' + targetLeft + 'px, 0px, 0px)';\n                } else {\n                    animProps[_.animType] = 'translate3d(0px,' + targetLeft + 'px, 0px)';\n                }\n                _.$slideTrack.css(animProps);\n\n                if (callback) {\n                    setTimeout(function() {\n\n                        _.disableTransition();\n\n                        callback.call();\n                    }, _.options.speed);\n                }\n\n            }\n\n        }\n\n    };\n\n    Slick.prototype.getNavTarget = function() {\n\n        var _ = this,\n            asNavFor = _.options.asNavFor;\n\n        if ( asNavFor && asNavFor !== null ) {\n            asNavFor = $(asNavFor).not(_.$slider);\n        }\n\n        return asNavFor;\n\n    };\n\n    Slick.prototype.asNavFor = function(index) {\n\n        var _ = this,\n            asNavFor = _.getNavTarget();\n\n        if ( asNavFor !== null && typeof asNavFor === 'object' ) {\n            asNavFor.each(function() {\n                var target = $(this).slick('getSlick');\n                if(!target.unslicked) {\n                    target.slideHandler(index, true);\n                }\n            });\n        }\n\n    };\n\n    Slick.prototype.applyTransition = function(slide) {\n\n        var _ = this,\n            transition = {};\n\n        if (_.options.fade === false) {\n            transition[_.transitionType] = _.transformType + ' ' + _.options.speed + 'ms ' + _.options.cssEase;\n        } else {\n            transition[_.transitionType] = 'opacity ' + _.options.speed + 'ms ' + _.options.cssEase;\n        }\n\n        if (_.options.fade === false) {\n            _.$slideTrack.css(transition);\n        } else {\n            _.$slides.eq(slide).css(transition);\n        }\n\n    };\n\n    Slick.prototype.autoPlay = function() {\n\n        var _ = this;\n\n        _.autoPlayClear();\n\n        if ( _.slideCount > _.options.slidesToShow ) {\n            _.autoPlayTimer = setInterval( _.autoPlayIterator, _.options.autoplaySpeed );\n        }\n\n    };\n\n    Slick.prototype.autoPlayClear = function() {\n\n        var _ = this;\n\n        if (_.autoPlayTimer) {\n            clearInterval(_.autoPlayTimer);\n        }\n\n    };\n\n    Slick.prototype.autoPlayIterator = function() {\n\n        var _ = this,\n            slideTo = _.currentSlide + _.options.slidesToScroll;\n\n        if ( !_.paused && !_.interrupted && !_.focussed ) {\n\n            if ( _.options.infinite === false ) {\n\n                if ( _.direction === 1 && ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {\n                    _.direction = 0;\n                }\n\n                else if ( _.direction === 0 ) {\n\n                    slideTo = _.currentSlide - _.options.slidesToScroll;\n\n                    if ( _.currentSlide - 1 === 0 ) {\n                        _.direction = 1;\n                    }\n\n                }\n\n            }\n\n            _.slideHandler( slideTo );\n\n        }\n\n    };\n\n    Slick.prototype.buildArrows = function() {\n\n        var _ = this;\n\n        if (_.options.arrows === true ) {\n\n            _.$prevArrow = $(_.options.prevArrow).addClass('slick-arrow');\n            _.$nextArrow = $(_.options.nextArrow).addClass('slick-arrow');\n\n            if( _.slideCount > _.options.slidesToShow ) {\n\n                _.$prevArrow.removeClass('slick-hidden').removeAttr('aria-hidden tabindex');\n                _.$nextArrow.removeClass('slick-hidden').removeAttr('aria-hidden tabindex');\n\n                if (_.htmlExpr.test(_.options.prevArrow)) {\n                    _.$prevArrow.prependTo(_.options.appendArrows);\n                }\n\n                if (_.htmlExpr.test(_.options.nextArrow)) {\n                    _.$nextArrow.appendTo(_.options.appendArrows);\n                }\n\n                if (_.options.infinite !== true) {\n                    _.$prevArrow\n                        .addClass('slick-disabled')\n                        .attr('aria-disabled', 'true');\n                }\n\n            } else {\n\n                _.$prevArrow.add( _.$nextArrow )\n\n                    .addClass('slick-hidden')\n                    .attr({\n                        'aria-disabled': 'true',\n                        'tabindex': '-1'\n                    });\n\n            }\n\n        }\n\n    };\n\n    Slick.prototype.buildDots = function() {\n\n        var _ = this,\n            i, dot;\n\n        if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n\n            _.$slider.addClass('slick-dotted');\n\n            dot = $('<ul />').addClass(_.options.dotsClass);\n\n            for (i = 0; i <= _.getDotCount(); i += 1) {\n                dot.append($('<li />').append(_.options.customPaging.call(this, _, i)));\n            }\n\n            _.$dots = dot.appendTo(_.options.appendDots);\n\n            _.$dots.find('li').first().addClass('slick-active');\n\n        }\n\n    };\n\n    Slick.prototype.buildOut = function() {\n\n        var _ = this;\n\n        _.$slides =\n            _.$slider\n                .children( _.options.slide + ':not(.slick-cloned)')\n                .addClass('slick-slide');\n\n        _.slideCount = _.$slides.length;\n\n        _.$slides.each(function(index, element) {\n            $(element)\n                .attr('data-slick-index', index)\n                .data('originalStyling', $(element).attr('style') || '');\n        });\n\n        _.$slider.addClass('slick-slider');\n\n        _.$slideTrack = (_.slideCount === 0) ?\n            $('<div class=\"slick-track\"/>').appendTo(_.$slider) :\n            _.$slides.wrapAll('<div class=\"slick-track\"/>').parent();\n\n        _.$list = _.$slideTrack.wrap(\n            '<div class=\"slick-list\"/>').parent();\n        _.$slideTrack.css('opacity', 0);\n\n        if (_.options.centerMode === true || _.options.swipeToSlide === true) {\n            _.options.slidesToScroll = 1;\n        }\n\n        $('img[data-lazy]', _.$slider).not('[src]').addClass('slick-loading');\n\n        _.setupInfinite();\n\n        _.buildArrows();\n\n        _.buildDots();\n\n        _.updateDots();\n\n\n        _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);\n\n        if (_.options.draggable === true) {\n            _.$list.addClass('draggable');\n        }\n\n    };\n\n    Slick.prototype.buildRows = function() {\n\n        var _ = this, a, b, c, newSlides, numOfSlides, originalSlides,slidesPerSection;\n\n        newSlides = document.createDocumentFragment();\n        originalSlides = _.$slider.children();\n\n        if(_.options.rows > 0) {\n\n            slidesPerSection = _.options.slidesPerRow * _.options.rows;\n            numOfSlides = Math.ceil(\n                originalSlides.length / slidesPerSection\n            );\n\n            for(a = 0; a < numOfSlides; a++){\n                var slide = document.createElement('div');\n                for(b = 0; b < _.options.rows; b++) {\n                    var row = document.createElement('div');\n                    for(c = 0; c < _.options.slidesPerRow; c++) {\n                        var target = (a * slidesPerSection + ((b * _.options.slidesPerRow) + c));\n                        if (originalSlides.get(target)) {\n                            row.appendChild(originalSlides.get(target));\n                        }\n                    }\n                    slide.appendChild(row);\n                }\n                newSlides.appendChild(slide);\n            }\n\n            _.$slider.empty().append(newSlides);\n            _.$slider.children().children().children()\n                .css({\n                    'width':(100 / _.options.slidesPerRow) + '%',\n                    'display': 'inline-block'\n                });\n\n        }\n\n    };\n\n    Slick.prototype.checkResponsive = function(initial, forceUpdate) {\n\n        var _ = this,\n            breakpoint, targetBreakpoint, respondToWidth, triggerBreakpoint = false;\n        var sliderWidth = _.$slider.width();\n        var windowWidth = window.innerWidth || $(window).width();\n\n        if (_.respondTo === 'window') {\n            respondToWidth = windowWidth;\n        } else if (_.respondTo === 'slider') {\n            respondToWidth = sliderWidth;\n        } else if (_.respondTo === 'min') {\n            respondToWidth = Math.min(windowWidth, sliderWidth);\n        }\n\n        if ( _.options.responsive &&\n            _.options.responsive.length &&\n            _.options.responsive !== null) {\n\n            targetBreakpoint = null;\n\n            for (breakpoint in _.breakpoints) {\n                if (_.breakpoints.hasOwnProperty(breakpoint)) {\n                    if (_.originalSettings.mobileFirst === false) {\n                        if (respondToWidth < _.breakpoints[breakpoint]) {\n                            targetBreakpoint = _.breakpoints[breakpoint];\n                        }\n                    } else {\n                        if (respondToWidth > _.breakpoints[breakpoint]) {\n                            targetBreakpoint = _.breakpoints[breakpoint];\n                        }\n                    }\n                }\n            }\n\n            if (targetBreakpoint !== null) {\n                if (_.activeBreakpoint !== null) {\n                    if (targetBreakpoint !== _.activeBreakpoint || forceUpdate) {\n                        _.activeBreakpoint =\n                            targetBreakpoint;\n                        if (_.breakpointSettings[targetBreakpoint] === 'unslick') {\n                            _.unslick(targetBreakpoint);\n                        } else {\n                            _.options = $.extend({}, _.originalSettings,\n                                _.breakpointSettings[\n                                    targetBreakpoint]);\n                            if (initial === true) {\n                                _.currentSlide = _.options.initialSlide;\n                            }\n                            _.refresh(initial);\n                        }\n                        triggerBreakpoint = targetBreakpoint;\n                    }\n                } else {\n                    _.activeBreakpoint = targetBreakpoint;\n                    if (_.breakpointSettings[targetBreakpoint] === 'unslick') {\n                        _.unslick(targetBreakpoint);\n                    } else {\n                        _.options = $.extend({}, _.originalSettings,\n                            _.breakpointSettings[\n                                targetBreakpoint]);\n                        if (initial === true) {\n                            _.currentSlide = _.options.initialSlide;\n                        }\n                        _.refresh(initial);\n                    }\n                    triggerBreakpoint = targetBreakpoint;\n                }\n            } else {\n                if (_.activeBreakpoint !== null) {\n                    _.activeBreakpoint = null;\n                    _.options = _.originalSettings;\n                    if (initial === true) {\n                        _.currentSlide = _.options.initialSlide;\n                    }\n                    _.refresh(initial);\n                    triggerBreakpoint = targetBreakpoint;\n                }\n            }\n\n            // only trigger breakpoints during an actual break. not on initialize.\n            if( !initial && triggerBreakpoint !== false ) {\n                _.$slider.trigger('breakpoint', [_, triggerBreakpoint]);\n            }\n        }\n\n    };\n\n    Slick.prototype.changeSlide = function(event, dontAnimate) {\n\n        var _ = this,\n            $target = $(event.currentTarget),\n            indexOffset, slideOffset, unevenOffset;\n\n        // If target is a link, prevent default action.\n        if($target.is('a')) {\n            event.preventDefault();\n        }\n\n        // If target is not the <li> element (ie: a child), find the <li>.\n        if(!$target.is('li')) {\n            $target = $target.closest('li');\n        }\n\n        unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);\n        indexOffset = unevenOffset ? 0 : (_.slideCount - _.currentSlide) % _.options.slidesToScroll;\n\n        switch (event.data.message) {\n\n            case 'previous':\n                slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset;\n                if (_.slideCount > _.options.slidesToShow) {\n                    _.slideHandler(_.currentSlide - slideOffset, false, dontAnimate);\n                }\n                break;\n\n            case 'next':\n                slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset;\n                if (_.slideCount > _.options.slidesToShow) {\n                    _.slideHandler(_.currentSlide + slideOffset, false, dontAnimate);\n                }\n                break;\n\n            case 'index':\n                var index = event.data.index === 0 ? 0 :\n                    event.data.index || $target.index() * _.options.slidesToScroll;\n\n                _.slideHandler(_.checkNavigable(index), false, dontAnimate);\n                $target.children().trigger('focus');\n                break;\n\n            default:\n                return;\n        }\n\n    };\n\n    Slick.prototype.checkNavigable = function(index) {\n\n        var _ = this,\n            navigables, prevNavigable;\n\n        navigables = _.getNavigableIndexes();\n        prevNavigable = 0;\n        if (index > navigables[navigables.length - 1]) {\n            index = navigables[navigables.length - 1];\n        } else {\n            for (var n in navigables) {\n                if (index < navigables[n]) {\n                    index = prevNavigable;\n                    break;\n                }\n                prevNavigable = navigables[n];\n            }\n        }\n\n        return index;\n    };\n\n    Slick.prototype.cleanUpEvents = function() {\n\n        var _ = this;\n\n        if (_.options.dots && _.$dots !== null) {\n\n            $('li', _.$dots)\n                .off('click.slick', _.changeSlide)\n                .off('mouseenter.slick', $.proxy(_.interrupt, _, true))\n                .off('mouseleave.slick', $.proxy(_.interrupt, _, false));\n\n            if (_.options.accessibility === true) {\n                _.$dots.off('keydown.slick', _.keyHandler);\n            }\n        }\n\n        _.$slider.off('focus.slick blur.slick');\n\n        if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n            _.$prevArrow && _.$prevArrow.off('click.slick', _.changeSlide);\n            _.$nextArrow && _.$nextArrow.off('click.slick', _.changeSlide);\n\n            if (_.options.accessibility === true) {\n                _.$prevArrow && _.$prevArrow.off('keydown.slick', _.keyHandler);\n                _.$nextArrow && _.$nextArrow.off('keydown.slick', _.keyHandler);\n            }\n        }\n\n        _.$list.off('touchstart.slick mousedown.slick', _.swipeHandler);\n        _.$list.off('touchmove.slick mousemove.slick', _.swipeHandler);\n        _.$list.off('touchend.slick mouseup.slick', _.swipeHandler);\n        _.$list.off('touchcancel.slick mouseleave.slick', _.swipeHandler);\n\n        _.$list.off('click.slick', _.clickHandler);\n\n        $(document).off(_.visibilityChange, _.visibility);\n\n        _.cleanUpSlideEvents();\n\n        if (_.options.accessibility === true) {\n            _.$list.off('keydown.slick', _.keyHandler);\n        }\n\n        if (_.options.focusOnSelect === true) {\n            $(_.$slideTrack).children().off('click.slick', _.selectHandler);\n        }\n\n        $(window).off('orientationchange.slick.slick-' + _.instanceUid, _.orientationChange);\n\n        $(window).off('resize.slick.slick-' + _.instanceUid, _.resize);\n\n        $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);\n\n        $(window).off('load.slick.slick-' + _.instanceUid, _.setPosition);\n\n    };\n\n    Slick.prototype.cleanUpSlideEvents = function() {\n\n        var _ = this;\n\n        _.$list.off('mouseenter.slick', $.proxy(_.interrupt, _, true));\n        _.$list.off('mouseleave.slick', $.proxy(_.interrupt, _, false));\n\n    };\n\n    Slick.prototype.cleanUpRows = function() {\n\n        var _ = this, originalSlides;\n\n        if(_.options.rows > 0) {\n            originalSlides = _.$slides.children().children();\n            originalSlides.removeAttr('style');\n            _.$slider.empty().append(originalSlides);\n        }\n\n    };\n\n    Slick.prototype.clickHandler = function(event) {\n\n        var _ = this;\n\n        if (_.shouldClick === false) {\n            event.stopImmediatePropagation();\n            event.stopPropagation();\n            event.preventDefault();\n        }\n\n    };\n\n    Slick.prototype.destroy = function(refresh) {\n\n        var _ = this;\n\n        _.autoPlayClear();\n\n        _.touchObject = {};\n\n        _.cleanUpEvents();\n\n        $('.slick-cloned', _.$slider).detach();\n\n        if (_.$dots) {\n            _.$dots.remove();\n        }\n\n        if ( _.$prevArrow && _.$prevArrow.length ) {\n\n            _.$prevArrow\n                .removeClass('slick-disabled slick-arrow slick-hidden')\n                .removeAttr('aria-hidden aria-disabled tabindex')\n                .css('display','');\n\n            if ( _.htmlExpr.test( _.options.prevArrow )) {\n                _.$prevArrow.remove();\n            }\n        }\n\n        if ( _.$nextArrow && _.$nextArrow.length ) {\n\n            _.$nextArrow\n                .removeClass('slick-disabled slick-arrow slick-hidden')\n                .removeAttr('aria-hidden aria-disabled tabindex')\n                .css('display','');\n\n            if ( _.htmlExpr.test( _.options.nextArrow )) {\n                _.$nextArrow.remove();\n            }\n        }\n\n\n        if (_.$slides) {\n\n            _.$slides\n                .removeClass('slick-slide slick-active slick-center slick-visible slick-current')\n                .removeAttr('aria-hidden')\n                .removeAttr('data-slick-index')\n                .each(function(){\n                    $(this).attr('style', $(this).data('originalStyling'));\n                });\n\n            _.$slideTrack.children(this.options.slide).detach();\n\n            _.$slideTrack.detach();\n\n            _.$list.detach();\n\n            _.$slider.append(_.$slides);\n        }\n\n        _.cleanUpRows();\n\n        _.$slider.removeClass('slick-slider');\n        _.$slider.removeClass('slick-initialized');\n        _.$slider.removeClass('slick-dotted');\n\n        _.unslicked = true;\n\n        if(!refresh) {\n            _.$slider.trigger('destroy', [_]);\n        }\n\n    };\n\n    Slick.prototype.disableTransition = function(slide) {\n\n        var _ = this,\n            transition = {};\n\n        transition[_.transitionType] = '';\n\n        if (_.options.fade === false) {\n            _.$slideTrack.css(transition);\n        } else {\n            _.$slides.eq(slide).css(transition);\n        }\n\n    };\n\n    Slick.prototype.fadeSlide = function(slideIndex, callback) {\n\n        var _ = this;\n\n        if (_.cssTransitions === false) {\n\n            _.$slides.eq(slideIndex).css({\n                zIndex: _.options.zIndex\n            });\n\n            _.$slides.eq(slideIndex).animate({\n                opacity: 1\n            }, _.options.speed, _.options.easing, callback);\n\n        } else {\n\n            _.applyTransition(slideIndex);\n\n            _.$slides.eq(slideIndex).css({\n                opacity: 1,\n                zIndex: _.options.zIndex\n            });\n\n            if (callback) {\n                setTimeout(function() {\n\n                    _.disableTransition(slideIndex);\n\n                    callback.call();\n                }, _.options.speed);\n            }\n\n        }\n\n    };\n\n    Slick.prototype.fadeSlideOut = function(slideIndex) {\n\n        var _ = this;\n\n        if (_.cssTransitions === false) {\n\n            _.$slides.eq(slideIndex).animate({\n                opacity: 0,\n                zIndex: _.options.zIndex - 2\n            }, _.options.speed, _.options.easing);\n\n        } else {\n\n            _.applyTransition(slideIndex);\n\n            _.$slides.eq(slideIndex).css({\n                opacity: 0,\n                zIndex: _.options.zIndex - 2\n            });\n\n        }\n\n    };\n\n    Slick.prototype.filterSlides = Slick.prototype.slickFilter = function(filter) {\n\n        var _ = this;\n\n        if (filter !== null) {\n\n            _.$slidesCache = _.$slides;\n\n            _.unload();\n\n            _.$slideTrack.children(this.options.slide).detach();\n\n            _.$slidesCache.filter(filter).appendTo(_.$slideTrack);\n\n            _.reinit();\n\n        }\n\n    };\n\n    Slick.prototype.focusHandler = function() {\n\n        var _ = this;\n\n        _.$slider\n            .off('focus.slick blur.slick')\n            .on('focus.slick blur.slick', '*', function(event) {\n\n            event.stopImmediatePropagation();\n            var $sf = $(this);\n\n            setTimeout(function() {\n\n                if( _.options.pauseOnFocus ) {\n                    _.focussed = $sf.is(':focus');\n                    _.autoPlay();\n                }\n\n            }, 0);\n\n        });\n    };\n\n    Slick.prototype.getCurrent = Slick.prototype.slickCurrentSlide = function() {\n\n        var _ = this;\n        return _.currentSlide;\n\n    };\n\n    Slick.prototype.getDotCount = function() {\n\n        var _ = this;\n\n        var breakPoint = 0;\n        var counter = 0;\n        var pagerQty = 0;\n\n        if (_.options.infinite === true) {\n            if (_.slideCount <= _.options.slidesToShow) {\n                 ++pagerQty;\n            } else {\n                while (breakPoint < _.slideCount) {\n                    ++pagerQty;\n                    breakPoint = counter + _.options.slidesToScroll;\n                    counter += _.options.slidesToScroll <= _.options.slidesToShow ? _.options.slidesToScroll : _.options.slidesToShow;\n                }\n            }\n        } else if (_.options.centerMode === true) {\n            pagerQty = _.slideCount;\n        } else if(!_.options.asNavFor) {\n            pagerQty = 1 + Math.ceil((_.slideCount - _.options.slidesToShow) / _.options.slidesToScroll);\n        }else {\n            while (breakPoint < _.slideCount) {\n                ++pagerQty;\n                breakPoint = counter + _.options.slidesToScroll;\n                counter += _.options.slidesToScroll <= _.options.slidesToShow ? _.options.slidesToScroll : _.options.slidesToShow;\n            }\n        }\n\n        return pagerQty - 1;\n\n    };\n\n    Slick.prototype.getLeft = function(slideIndex) {\n\n        var _ = this,\n            targetLeft,\n            verticalHeight,\n            verticalOffset = 0,\n            targetSlide,\n            coef;\n\n        _.slideOffset = 0;\n        verticalHeight = _.$slides.first().outerHeight(true);\n\n        if (_.options.infinite === true) {\n            if (_.slideCount > _.options.slidesToShow) {\n                _.slideOffset = (_.slideWidth * _.options.slidesToShow) * -1;\n                coef = -1\n\n                if (_.options.vertical === true && _.options.centerMode === true) {\n                    if (_.options.slidesToShow === 2) {\n                        coef = -1.5;\n                    } else if (_.options.slidesToShow === 1) {\n                        coef = -2\n                    }\n                }\n                verticalOffset = (verticalHeight * _.options.slidesToShow) * coef;\n            }\n            if (_.slideCount % _.options.slidesToScroll !== 0) {\n                if (slideIndex + _.options.slidesToScroll > _.slideCount && _.slideCount > _.options.slidesToShow) {\n                    if (slideIndex > _.slideCount) {\n                        _.slideOffset = ((_.options.slidesToShow - (slideIndex - _.slideCount)) * _.slideWidth) * -1;\n                        verticalOffset = ((_.options.slidesToShow - (slideIndex - _.slideCount)) * verticalHeight) * -1;\n                    } else {\n                        _.slideOffset = ((_.slideCount % _.options.slidesToScroll) * _.slideWidth) * -1;\n                        verticalOffset = ((_.slideCount % _.options.slidesToScroll) * verticalHeight) * -1;\n                    }\n                }\n            }\n        } else {\n            if (slideIndex + _.options.slidesToShow > _.slideCount) {\n                _.slideOffset = ((slideIndex + _.options.slidesToShow) - _.slideCount) * _.slideWidth;\n                verticalOffset = ((slideIndex + _.options.slidesToShow) - _.slideCount) * verticalHeight;\n            }\n        }\n\n        if (_.slideCount <= _.options.slidesToShow) {\n            _.slideOffset = 0;\n            verticalOffset = 0;\n        }\n\n        if (_.options.centerMode === true && _.slideCount <= _.options.slidesToShow) {\n            _.slideOffset = ((_.slideWidth * Math.floor(_.options.slidesToShow)) / 2) - ((_.slideWidth * _.slideCount) / 2);\n        } else if (_.options.centerMode === true && _.options.infinite === true) {\n            _.slideOffset += _.slideWidth * Math.floor(_.options.slidesToShow / 2) - _.slideWidth;\n        } else if (_.options.centerMode === true) {\n            _.slideOffset = 0;\n            _.slideOffset += _.slideWidth * Math.floor(_.options.slidesToShow / 2);\n        }\n\n        if (_.options.vertical === false) {\n            targetLeft = ((slideIndex * _.slideWidth) * -1) + _.slideOffset;\n        } else {\n            targetLeft = ((slideIndex * verticalHeight) * -1) + verticalOffset;\n        }\n\n        if (_.options.variableWidth === true) {\n\n            if (_.slideCount <= _.options.slidesToShow || _.options.infinite === false) {\n                targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex);\n            } else {\n                targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex + _.options.slidesToShow);\n            }\n\n            if (_.options.rtl === true) {\n                if (targetSlide[0]) {\n                    targetLeft = (_.$slideTrack.width() - targetSlide[0].offsetLeft - targetSlide.width()) * -1;\n                } else {\n                    targetLeft =  0;\n                }\n            } else {\n                targetLeft = targetSlide[0] ? targetSlide[0].offsetLeft * -1 : 0;\n            }\n\n            if (_.options.centerMode === true) {\n                if (_.slideCount <= _.options.slidesToShow || _.options.infinite === false) {\n                    targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex);\n                } else {\n                    targetSlide = _.$slideTrack.children('.slick-slide').eq(slideIndex + _.options.slidesToShow + 1);\n                }\n\n                if (_.options.rtl === true) {\n                    if (targetSlide[0]) {\n                        targetLeft = (_.$slideTrack.width() - targetSlide[0].offsetLeft - targetSlide.width()) * -1;\n                    } else {\n                        targetLeft =  0;\n                    }\n                } else {\n                    targetLeft = targetSlide[0] ? targetSlide[0].offsetLeft * -1 : 0;\n                }\n\n                targetLeft += (_.$list.width() - targetSlide.outerWidth()) / 2;\n            }\n        }\n\n        return targetLeft;\n\n    };\n\n    Slick.prototype.getOption = Slick.prototype.slickGetOption = function(option) {\n\n        var _ = this;\n\n        return _.options[option];\n\n    };\n\n    Slick.prototype.getNavigableIndexes = function() {\n\n        var _ = this,\n            breakPoint = 0,\n            counter = 0,\n            indexes = [],\n            max;\n\n        if (_.options.infinite === false) {\n            max = _.slideCount;\n        } else {\n            breakPoint = _.options.slidesToScroll * -1;\n            counter = _.options.slidesToScroll * -1;\n            max = _.slideCount * 2;\n        }\n\n        while (breakPoint < max) {\n            indexes.push(breakPoint);\n            breakPoint = counter + _.options.slidesToScroll;\n            counter += _.options.slidesToScroll <= _.options.slidesToShow ? _.options.slidesToScroll : _.options.slidesToShow;\n        }\n\n        return indexes;\n\n    };\n\n    Slick.prototype.getSlick = function() {\n\n        return this;\n\n    };\n\n    Slick.prototype.getSlideCount = function() {\n\n        var _ = this,\n            slidesTraversed, swipedSlide, centerOffset;\n\n        centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0;\n\n        if (_.options.swipeToSlide === true) {\n            _.$slideTrack.find('.slick-slide').each(function(index, slide) {\n                if (slide.offsetLeft - centerOffset + ($(slide).outerWidth() / 2) > (_.swipeLeft * -1)) {\n                    swipedSlide = slide;\n                    return false;\n                }\n            });\n\n            slidesTraversed = Math.abs($(swipedSlide).attr('data-slick-index') - _.currentSlide) || 1;\n\n            return slidesTraversed;\n\n        } else {\n            return _.options.slidesToScroll;\n        }\n\n    };\n\n    Slick.prototype.goTo = Slick.prototype.slickGoTo = function(slide, dontAnimate) {\n\n        var _ = this;\n\n        _.changeSlide({\n            data: {\n                message: 'index',\n                index: parseInt(slide)\n            }\n        }, dontAnimate);\n\n    };\n\n    Slick.prototype.init = function(creation) {\n\n        var _ = this;\n\n        if (!$(_.$slider).hasClass('slick-initialized')) {\n\n            $(_.$slider).addClass('slick-initialized');\n\n            _.buildRows();\n            _.buildOut();\n            _.setProps();\n            _.startLoad();\n            _.loadSlider();\n            _.initializeEvents();\n            _.updateArrows();\n            _.updateDots();\n            _.checkResponsive(true);\n            _.focusHandler();\n\n        }\n\n        if (creation) {\n            _.$slider.trigger('init', [_]);\n        }\n\n        if (_.options.accessibility === true) {\n            _.initADA();\n        }\n\n        if ( _.options.autoplay ) {\n\n            _.paused = false;\n            _.autoPlay();\n\n        }\n\n    };\n\n    Slick.prototype.initADA = function() {\n        var _ = this,\n                numDotGroups = Math.ceil(_.slideCount / _.options.slidesToShow),\n                tabControlIndexes = _.getNavigableIndexes().filter(function(val) {\n                    return (val >= 0) && (val < _.slideCount);\n                });\n\n        _.$slides.add(_.$slideTrack.find('.slick-cloned')).attr({\n            'aria-hidden': 'true',\n            'tabindex': '-1'\n        }).find('a, input, button, select').attr({\n            'tabindex': '-1'\n        });\n\n        if (_.$dots !== null) {\n            _.$slides.not(_.$slideTrack.find('.slick-cloned')).each(function(i) {\n                var slideControlIndex = tabControlIndexes.indexOf(i);\n\n                $(this).attr({\n                    'role': 'tabpanel',\n                    'id': 'slick-slide' + _.instanceUid + i,\n                    'tabindex': -1\n                });\n\n                if (slideControlIndex !== -1) {\n                   var ariaButtonControl = 'slick-slide-control' + _.instanceUid + slideControlIndex\n                   if ($('#' + ariaButtonControl).length) {\n                     $(this).attr({\n                         'aria-describedby': ariaButtonControl\n                     });\n                   }\n                }\n            });\n\n            _.$dots.attr('role', 'tablist').find('li').each(function(i) {\n                var mappedSlideIndex = tabControlIndexes[i];\n\n                $(this).attr({\n                    'role': 'presentation'\n                });\n\n                $(this).find('button').first().attr({\n                    'role': 'tab',\n                    'id': 'slick-slide-control' + _.instanceUid + i,\n                    'aria-controls': 'slick-slide' + _.instanceUid + mappedSlideIndex,\n                    'aria-label': (i + 1) + ' of ' + numDotGroups,\n                    'aria-selected': null,\n                    'tabindex': '-1'\n                });\n\n            }).eq(_.currentSlide).find('button').attr({\n                'aria-selected': 'true',\n                'tabindex': '0'\n            }).end();\n        }\n\n        for (var i=_.currentSlide, max=i+_.options.slidesToShow; i < max; i++) {\n          if (_.options.focusOnChange) {\n            _.$slides.eq(i).attr({'tabindex': '0'});\n          } else {\n            _.$slides.eq(i).removeAttr('tabindex');\n          }\n        }\n\n        _.activateADA();\n\n    };\n\n    Slick.prototype.initArrowEvents = function() {\n\n        var _ = this;\n\n        if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n            _.$prevArrow\n               .off('click.slick')\n               .on('click.slick', {\n                    message: 'previous'\n               }, _.changeSlide);\n            _.$nextArrow\n               .off('click.slick')\n               .on('click.slick', {\n                    message: 'next'\n               }, _.changeSlide);\n\n            if (_.options.accessibility === true) {\n                _.$prevArrow.on('keydown.slick', _.keyHandler);\n                _.$nextArrow.on('keydown.slick', _.keyHandler);\n            }\n        }\n\n    };\n\n    Slick.prototype.initDotEvents = function() {\n\n        var _ = this;\n\n        if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n            $('li', _.$dots).on('click.slick', {\n                message: 'index'\n            }, _.changeSlide);\n\n            if (_.options.accessibility === true) {\n                _.$dots.on('keydown.slick', _.keyHandler);\n            }\n        }\n\n        if (_.options.dots === true && _.options.pauseOnDotsHover === true && _.slideCount > _.options.slidesToShow) {\n\n            $('li', _.$dots)\n                .on('mouseenter.slick', $.proxy(_.interrupt, _, true))\n                .on('mouseleave.slick', $.proxy(_.interrupt, _, false));\n\n        }\n\n    };\n\n    Slick.prototype.initSlideEvents = function() {\n\n        var _ = this;\n\n        if ( _.options.pauseOnHover ) {\n\n            _.$list.on('mouseenter.slick', $.proxy(_.interrupt, _, true));\n            _.$list.on('mouseleave.slick', $.proxy(_.interrupt, _, false));\n\n        }\n\n    };\n\n    Slick.prototype.initializeEvents = function() {\n\n        var _ = this;\n\n        _.initArrowEvents();\n\n        _.initDotEvents();\n        _.initSlideEvents();\n\n        _.$list.on('touchstart.slick mousedown.slick', {\n            action: 'start'\n        }, _.swipeHandler);\n        _.$list.on('touchmove.slick mousemove.slick', {\n            action: 'move'\n        }, _.swipeHandler);\n        _.$list.on('touchend.slick mouseup.slick', {\n            action: 'end'\n        }, _.swipeHandler);\n        _.$list.on('touchcancel.slick mouseleave.slick', {\n            action: 'end'\n        }, _.swipeHandler);\n\n        _.$list.on('click.slick', _.clickHandler);\n\n        $(document).on(_.visibilityChange, $.proxy(_.visibility, _));\n\n        if (_.options.accessibility === true) {\n            _.$list.on('keydown.slick', _.keyHandler);\n        }\n\n        if (_.options.focusOnSelect === true) {\n            $(_.$slideTrack).children().on('click.slick', _.selectHandler);\n        }\n\n        $(window).on('orientationchange.slick.slick-' + _.instanceUid, $.proxy(_.orientationChange, _));\n\n        $(window).on('resize.slick.slick-' + _.instanceUid, $.proxy(_.resize, _));\n\n        $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);\n\n        $(window).on('load.slick.slick-' + _.instanceUid, _.setPosition);\n        $(_.setPosition);\n\n    };\n\n    Slick.prototype.initUI = function() {\n\n        var _ = this;\n\n        if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n\n            _.$prevArrow.show();\n            _.$nextArrow.show();\n\n        }\n\n        if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n\n            _.$dots.show();\n\n        }\n\n    };\n\n    Slick.prototype.keyHandler = function(event) {\n\n        var _ = this;\n         //Dont slide if the cursor is inside the form fields and arrow keys are pressed\n        if(!event.target.tagName.match('TEXTAREA|INPUT|SELECT')) {\n            if (event.keyCode === 37 && _.options.accessibility === true) {\n                _.changeSlide({\n                    data: {\n                        message: _.options.rtl === true ? 'next' :  'previous'\n                    }\n                });\n            } else if (event.keyCode === 39 && _.options.accessibility === true) {\n                _.changeSlide({\n                    data: {\n                        message: _.options.rtl === true ? 'previous' : 'next'\n                    }\n                });\n            }\n        }\n\n    };\n\n    Slick.prototype.lazyLoad = function() {\n\n        var _ = this,\n            loadRange, cloneRange, rangeStart, rangeEnd;\n\n        function loadImages(imagesScope) {\n\n            $('img[data-lazy]', imagesScope).each(function() {\n\n                var image = $(this),\n                    imageSource = $(this).attr('data-lazy'),\n                    imageSrcSet = $(this).attr('data-srcset'),\n                    imageSizes  = $(this).attr('data-sizes') || _.$slider.attr('data-sizes'),\n                    imageToLoad = document.createElement('img');\n\n                imageToLoad.onload = function() {\n\n                    image\n                        .animate({ opacity: 0 }, 100, function() {\n\n                            if (imageSrcSet) {\n                                image\n                                    .attr('srcset', imageSrcSet );\n\n                                if (imageSizes) {\n                                    image\n                                        .attr('sizes', imageSizes );\n                                }\n                            }\n\n                            image\n                                .attr('src', imageSource)\n                                .animate({ opacity: 1 }, 200, function() {\n                                    image\n                                        .removeAttr('data-lazy data-srcset data-sizes')\n                                        .removeClass('slick-loading');\n                                });\n                            _.$slider.trigger('lazyLoaded', [_, image, imageSource]);\n                        });\n\n                };\n\n                imageToLoad.onerror = function() {\n\n                    image\n                        .removeAttr( 'data-lazy' )\n                        .removeClass( 'slick-loading' )\n                        .addClass( 'slick-lazyload-error' );\n\n                    _.$slider.trigger('lazyLoadError', [ _, image, imageSource ]);\n\n                };\n\n                imageToLoad.src = imageSource;\n\n            });\n\n        }\n\n        if (_.options.centerMode === true) {\n            if (_.options.infinite === true) {\n                rangeStart = _.currentSlide + (_.options.slidesToShow / 2 + 1);\n                rangeEnd = rangeStart + _.options.slidesToShow + 2;\n            } else {\n                rangeStart = Math.max(0, _.currentSlide - (_.options.slidesToShow / 2 + 1));\n                rangeEnd = 2 + (_.options.slidesToShow / 2 + 1) + _.currentSlide;\n            }\n        } else {\n            rangeStart = _.options.infinite ? _.options.slidesToShow + _.currentSlide : _.currentSlide;\n            rangeEnd = Math.ceil(rangeStart + _.options.slidesToShow);\n            if (_.options.fade === true) {\n                if (rangeStart > 0) rangeStart--;\n                if (rangeEnd <= _.slideCount) rangeEnd++;\n            }\n        }\n\n        loadRange = _.$slider.find('.slick-slide').slice(rangeStart, rangeEnd);\n\n        if (_.options.lazyLoad === 'anticipated') {\n            var prevSlide = rangeStart - 1,\n                nextSlide = rangeEnd,\n                $slides = _.$slider.find('.slick-slide');\n\n            for (var i = 0; i < _.options.slidesToScroll; i++) {\n                if (prevSlide < 0) prevSlide = _.slideCount - 1;\n                loadRange = loadRange.add($slides.eq(prevSlide));\n                loadRange = loadRange.add($slides.eq(nextSlide));\n                prevSlide--;\n                nextSlide++;\n            }\n        }\n\n        loadImages(loadRange);\n\n        if (_.slideCount <= _.options.slidesToShow) {\n            cloneRange = _.$slider.find('.slick-slide');\n            loadImages(cloneRange);\n        } else\n        if (_.currentSlide >= _.slideCount - _.options.slidesToShow) {\n            cloneRange = _.$slider.find('.slick-cloned').slice(0, _.options.slidesToShow);\n            loadImages(cloneRange);\n        } else if (_.currentSlide === 0) {\n            cloneRange = _.$slider.find('.slick-cloned').slice(_.options.slidesToShow * -1);\n            loadImages(cloneRange);\n        }\n\n    };\n\n    Slick.prototype.loadSlider = function() {\n\n        var _ = this;\n\n        _.setPosition();\n\n        _.$slideTrack.css({\n            opacity: 1\n        });\n\n        _.$slider.removeClass('slick-loading');\n\n        _.initUI();\n\n        if (_.options.lazyLoad === 'progressive') {\n            _.progressiveLazyLoad();\n        }\n\n    };\n\n    Slick.prototype.next = Slick.prototype.slickNext = function() {\n\n        var _ = this;\n\n        _.changeSlide({\n            data: {\n                message: 'next'\n            }\n        });\n\n    };\n\n    Slick.prototype.orientationChange = function() {\n\n        var _ = this;\n\n        _.checkResponsive();\n        _.setPosition();\n\n    };\n\n    Slick.prototype.pause = Slick.prototype.slickPause = function() {\n\n        var _ = this;\n\n        _.autoPlayClear();\n        _.paused = true;\n\n    };\n\n    Slick.prototype.play = Slick.prototype.slickPlay = function() {\n\n        var _ = this;\n\n        _.autoPlay();\n        _.options.autoplay = true;\n        _.paused = false;\n        _.focussed = false;\n        _.interrupted = false;\n\n    };\n\n    Slick.prototype.postSlide = function(index) {\n\n        var _ = this;\n\n        if( !_.unslicked ) {\n\n            _.$slider.trigger('afterChange', [_, index]);\n\n            _.animating = false;\n\n            if (_.slideCount > _.options.slidesToShow) {\n                _.setPosition();\n            }\n\n            _.swipeLeft = null;\n\n            if ( _.options.autoplay ) {\n                _.autoPlay();\n            }\n\n            if (_.options.accessibility === true) {\n                _.initADA();\n\n                if (_.options.focusOnChange) {\n                    var $currentSlide = $(_.$slides.get(_.currentSlide));\n                    $currentSlide.attr('tabindex', 0).focus();\n                }\n            }\n\n        }\n\n    };\n\n    Slick.prototype.prev = Slick.prototype.slickPrev = function() {\n\n        var _ = this;\n\n        _.changeSlide({\n            data: {\n                message: 'previous'\n            }\n        });\n\n    };\n\n    Slick.prototype.preventDefault = function(event) {\n\n        event.preventDefault();\n\n    };\n\n    Slick.prototype.progressiveLazyLoad = function( tryCount ) {\n\n        tryCount = tryCount || 1;\n\n        var _ = this,\n            $imgsToLoad = $( 'img[data-lazy]', _.$slider ),\n            image,\n            imageSource,\n            imageSrcSet,\n            imageSizes,\n            imageToLoad;\n\n        if ( $imgsToLoad.length ) {\n\n            image = $imgsToLoad.first();\n            imageSource = image.attr('data-lazy');\n            imageSrcSet = image.attr('data-srcset');\n            imageSizes  = image.attr('data-sizes') || _.$slider.attr('data-sizes');\n            imageToLoad = document.createElement('img');\n\n            imageToLoad.onload = function() {\n\n                if (imageSrcSet) {\n                    image\n                        .attr('srcset', imageSrcSet );\n\n                    if (imageSizes) {\n                        image\n                            .attr('sizes', imageSizes );\n                    }\n                }\n\n                image\n                    .attr( 'src', imageSource )\n                    .removeAttr('data-lazy data-srcset data-sizes')\n                    .removeClass('slick-loading');\n\n                if ( _.options.adaptiveHeight === true ) {\n                    _.setPosition();\n                }\n\n                _.$slider.trigger('lazyLoaded', [ _, image, imageSource ]);\n                _.progressiveLazyLoad();\n\n            };\n\n            imageToLoad.onerror = function() {\n\n                if ( tryCount < 3 ) {\n\n                    /**\n                     * try to load the image 3 times,\n                     * leave a slight delay so we don't get\n                     * servers blocking the request.\n                     */\n                    setTimeout( function() {\n                        _.progressiveLazyLoad( tryCount + 1 );\n                    }, 500 );\n\n                } else {\n\n                    image\n                        .removeAttr( 'data-lazy' )\n                        .removeClass( 'slick-loading' )\n                        .addClass( 'slick-lazyload-error' );\n\n                    _.$slider.trigger('lazyLoadError', [ _, image, imageSource ]);\n\n                    _.progressiveLazyLoad();\n\n                }\n\n            };\n\n            imageToLoad.src = imageSource;\n\n        } else {\n\n            _.$slider.trigger('allImagesLoaded', [ _ ]);\n\n        }\n\n    };\n\n    Slick.prototype.refresh = function( initializing ) {\n\n        var _ = this, currentSlide, lastVisibleIndex;\n\n        lastVisibleIndex = _.slideCount - _.options.slidesToShow;\n\n        // in non-infinite sliders, we don't want to go past the\n        // last visible index.\n        if( !_.options.infinite && ( _.currentSlide > lastVisibleIndex )) {\n            _.currentSlide = lastVisibleIndex;\n        }\n\n        // if less slides than to show, go to start.\n        if ( _.slideCount <= _.options.slidesToShow ) {\n            _.currentSlide = 0;\n\n        }\n\n        currentSlide = _.currentSlide;\n\n        _.destroy(true);\n\n        $.extend(_, _.initials, { currentSlide: currentSlide });\n\n        _.init();\n\n        if( !initializing ) {\n\n            _.changeSlide({\n                data: {\n                    message: 'index',\n                    index: currentSlide\n                }\n            }, false);\n\n        }\n\n    };\n\n    Slick.prototype.registerBreakpoints = function() {\n\n        var _ = this, breakpoint, currentBreakpoint, l,\n            responsiveSettings = _.options.responsive || null;\n\n        if ( $.type(responsiveSettings) === 'array' && responsiveSettings.length ) {\n\n            _.respondTo = _.options.respondTo || 'window';\n\n            for ( breakpoint in responsiveSettings ) {\n\n                l = _.breakpoints.length-1;\n\n                if (responsiveSettings.hasOwnProperty(breakpoint)) {\n                    currentBreakpoint = responsiveSettings[breakpoint].breakpoint;\n\n                    // loop through the breakpoints and cut out any existing\n                    // ones with the same breakpoint number, we don't want dupes.\n                    while( l >= 0 ) {\n                        if( _.breakpoints[l] && _.breakpoints[l] === currentBreakpoint ) {\n                            _.breakpoints.splice(l,1);\n                        }\n                        l--;\n                    }\n\n                    _.breakpoints.push(currentBreakpoint);\n                    _.breakpointSettings[currentBreakpoint] = responsiveSettings[breakpoint].settings;\n\n                }\n\n            }\n\n            _.breakpoints.sort(function(a, b) {\n                return ( _.options.mobileFirst ) ? a-b : b-a;\n            });\n\n        }\n\n    };\n\n    Slick.prototype.reinit = function() {\n\n        var _ = this;\n\n        _.$slides =\n            _.$slideTrack\n                .children(_.options.slide)\n                .addClass('slick-slide');\n\n        _.slideCount = _.$slides.length;\n\n        if (_.currentSlide >= _.slideCount && _.currentSlide !== 0) {\n            _.currentSlide = _.currentSlide - _.options.slidesToScroll;\n        }\n\n        if (_.slideCount <= _.options.slidesToShow) {\n            _.currentSlide = 0;\n        }\n\n        _.registerBreakpoints();\n\n        _.setProps();\n        _.setupInfinite();\n        _.buildArrows();\n        _.updateArrows();\n        _.initArrowEvents();\n        _.buildDots();\n        _.updateDots();\n        _.initDotEvents();\n        _.cleanUpSlideEvents();\n        _.initSlideEvents();\n\n        _.checkResponsive(false, true);\n\n        if (_.options.focusOnSelect === true) {\n            $(_.$slideTrack).children().on('click.slick', _.selectHandler);\n        }\n\n        _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);\n\n        _.setPosition();\n        _.focusHandler();\n\n        _.paused = !_.options.autoplay;\n        _.autoPlay();\n\n        _.$slider.trigger('reInit', [_]);\n\n    };\n\n    Slick.prototype.resize = function() {\n\n        var _ = this;\n\n        if ($(window).width() !== _.windowWidth) {\n            clearTimeout(_.windowDelay);\n            _.windowDelay = window.setTimeout(function() {\n                _.windowWidth = $(window).width();\n                _.checkResponsive();\n                if( !_.unslicked ) { _.setPosition(); }\n            }, 50);\n        }\n    };\n\n    Slick.prototype.removeSlide = Slick.prototype.slickRemove = function(index, removeBefore, removeAll) {\n\n        var _ = this;\n\n        if (typeof(index) === 'boolean') {\n            removeBefore = index;\n            index = removeBefore === true ? 0 : _.slideCount - 1;\n        } else {\n            index = removeBefore === true ? --index : index;\n        }\n\n        if (_.slideCount < 1 || index < 0 || index > _.slideCount - 1) {\n            return false;\n        }\n\n        _.unload();\n\n        if (removeAll === true) {\n            _.$slideTrack.children().remove();\n        } else {\n            _.$slideTrack.children(this.options.slide).eq(index).remove();\n        }\n\n        _.$slides = _.$slideTrack.children(this.options.slide);\n\n        _.$slideTrack.children(this.options.slide).detach();\n\n        _.$slideTrack.append(_.$slides);\n\n        _.$slidesCache = _.$slides;\n\n        _.reinit();\n\n    };\n\n    Slick.prototype.setCSS = function(position) {\n\n        var _ = this,\n            positionProps = {},\n            x, y;\n\n        if (_.options.rtl === true) {\n            position = -position;\n        }\n        x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px';\n        y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px';\n\n        positionProps[_.positionProp] = position;\n\n        if (_.transformsEnabled === false) {\n            _.$slideTrack.css(positionProps);\n        } else {\n            positionProps = {};\n            if (_.cssTransitions === false) {\n                positionProps[_.animType] = 'translate(' + x + ', ' + y + ')';\n                _.$slideTrack.css(positionProps);\n            } else {\n                positionProps[_.animType] = 'translate3d(' + x + ', ' + y + ', 0px)';\n                _.$slideTrack.css(positionProps);\n            }\n        }\n\n    };\n\n    Slick.prototype.setDimensions = function() {\n\n        var _ = this;\n\n        if (_.options.vertical === false) {\n            if (_.options.centerMode === true) {\n                _.$list.css({\n                    padding: ('0px ' + _.options.centerPadding)\n                });\n            }\n        } else {\n            _.$list.height(_.$slides.first().outerHeight(true) * _.options.slidesToShow);\n            if (_.options.centerMode === true) {\n                _.$list.css({\n                    padding: (_.options.centerPadding + ' 0px')\n                });\n            }\n        }\n\n        _.listWidth = _.$list.width();\n        _.listHeight = _.$list.height();\n\n\n        if (_.options.vertical === false && _.options.variableWidth === false) {\n            _.slideWidth = Math.ceil(_.listWidth / _.options.slidesToShow);\n            _.$slideTrack.width(Math.ceil((_.slideWidth * _.$slideTrack.children('.slick-slide').length)));\n\n        } else if (_.options.variableWidth === true) {\n            _.$slideTrack.width(5000 * _.slideCount);\n        } else {\n            _.slideWidth = Math.ceil(_.listWidth);\n            _.$slideTrack.height(Math.ceil((_.$slides.first().outerHeight(true) * _.$slideTrack.children('.slick-slide').length)));\n        }\n\n        var offset = _.$slides.first().outerWidth(true) - _.$slides.first().width();\n        if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);\n\n    };\n\n    Slick.prototype.setFade = function() {\n\n        var _ = this,\n            targetLeft;\n\n        _.$slides.each(function(index, element) {\n            targetLeft = (_.slideWidth * index) * -1;\n            if (_.options.rtl === true) {\n                $(element).css({\n                    position: 'relative',\n                    right: targetLeft,\n                    top: 0,\n                    zIndex: _.options.zIndex - 2,\n                    opacity: 0\n                });\n            } else {\n                $(element).css({\n                    position: 'relative',\n                    left: targetLeft,\n                    top: 0,\n                    zIndex: _.options.zIndex - 2,\n                    opacity: 0\n                });\n            }\n        });\n\n        _.$slides.eq(_.currentSlide).css({\n            zIndex: _.options.zIndex - 1,\n            opacity: 1\n        });\n\n    };\n\n    Slick.prototype.setHeight = function() {\n\n        var _ = this;\n\n        if (_.options.slidesToShow === 1 && _.options.adaptiveHeight === true && _.options.vertical === false) {\n            var targetHeight = _.$slides.eq(_.currentSlide).outerHeight(true);\n            _.$list.css('height', targetHeight);\n        }\n\n    };\n\n    Slick.prototype.setOption =\n    Slick.prototype.slickSetOption = function() {\n\n        /**\n         * accepts arguments in format of:\n         *\n         *  - for changing a single option's value:\n         *     .slick(\"setOption\", option, value, refresh )\n         *\n         *  - for changing a set of responsive options:\n         *     .slick(\"setOption\", 'responsive', [{}, ...], refresh )\n         *\n         *  - for updating multiple values at once (not responsive)\n         *     .slick(\"setOption\", { 'option': value, ... }, refresh )\n         */\n\n        var _ = this, l, item, option, value, refresh = false, type;\n\n        if( $.type( arguments[0] ) === 'object' ) {\n\n            option =  arguments[0];\n            refresh = arguments[1];\n            type = 'multiple';\n\n        } else if ( $.type( arguments[0] ) === 'string' ) {\n\n            option =  arguments[0];\n            value = arguments[1];\n            refresh = arguments[2];\n\n            if ( arguments[0] === 'responsive' && $.type( arguments[1] ) === 'array' ) {\n\n                type = 'responsive';\n\n            } else if ( typeof arguments[1] !== 'undefined' ) {\n\n                type = 'single';\n\n            }\n\n        }\n\n        if ( type === 'single' ) {\n\n            _.options[option] = value;\n\n\n        } else if ( type === 'multiple' ) {\n\n            $.each( option , function( opt, val ) {\n\n                _.options[opt] = val;\n\n            });\n\n\n        } else if ( type === 'responsive' ) {\n\n            for ( item in value ) {\n\n                if( $.type( _.options.responsive ) !== 'array' ) {\n\n                    _.options.responsive = [ value[item] ];\n\n                } else {\n\n                    l = _.options.responsive.length-1;\n\n                    // loop through the responsive object and splice out duplicates.\n                    while( l >= 0 ) {\n\n                        if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {\n\n                            _.options.responsive.splice(l,1);\n\n                        }\n\n                        l--;\n\n                    }\n\n                    _.options.responsive.push( value[item] );\n\n                }\n\n            }\n\n        }\n\n        if ( refresh ) {\n\n            _.unload();\n            _.reinit();\n\n        }\n\n    };\n\n    Slick.prototype.setPosition = function() {\n\n        var _ = this;\n\n        _.setDimensions();\n\n        _.setHeight();\n\n        if (_.options.fade === false) {\n            _.setCSS(_.getLeft(_.currentSlide));\n        } else {\n            _.setFade();\n        }\n\n        _.$slider.trigger('setPosition', [_]);\n\n    };\n\n    Slick.prototype.setProps = function() {\n\n        var _ = this,\n            bodyStyle = document.body.style;\n\n        _.positionProp = _.options.vertical === true ? 'top' : 'left';\n\n        if (_.positionProp === 'top') {\n            _.$slider.addClass('slick-vertical');\n        } else {\n            _.$slider.removeClass('slick-vertical');\n        }\n\n        if (bodyStyle.WebkitTransition !== undefined ||\n            bodyStyle.MozTransition !== undefined ||\n            bodyStyle.msTransition !== undefined) {\n            if (_.options.useCSS === true) {\n                _.cssTransitions = true;\n            }\n        }\n\n        if ( _.options.fade ) {\n            if ( typeof _.options.zIndex === 'number' ) {\n                if( _.options.zIndex < 3 ) {\n                    _.options.zIndex = 3;\n                }\n            } else {\n                _.options.zIndex = _.defaults.zIndex;\n            }\n        }\n\n        if (bodyStyle.OTransform !== undefined) {\n            _.animType = 'OTransform';\n            _.transformType = '-o-transform';\n            _.transitionType = 'OTransition';\n            if (bodyStyle.perspectiveProperty === undefined && bodyStyle.webkitPerspective === undefined) _.animType = false;\n        }\n        if (bodyStyle.MozTransform !== undefined) {\n            _.animType = 'MozTransform';\n            _.transformType = '-moz-transform';\n            _.transitionType = 'MozTransition';\n            if (bodyStyle.perspectiveProperty === undefined && bodyStyle.MozPerspective === undefined) _.animType = false;\n        }\n        if (bodyStyle.webkitTransform !== undefined) {\n            _.animType = 'webkitTransform';\n            _.transformType = '-webkit-transform';\n            _.transitionType = 'webkitTransition';\n            if (bodyStyle.perspectiveProperty === undefined && bodyStyle.webkitPerspective === undefined) _.animType = false;\n        }\n        if (bodyStyle.msTransform !== undefined) {\n            _.animType = 'msTransform';\n            _.transformType = '-ms-transform';\n            _.transitionType = 'msTransition';\n            if (bodyStyle.msTransform === undefined) _.animType = false;\n        }\n        if (bodyStyle.transform !== undefined && _.animType !== false) {\n            _.animType = 'transform';\n            _.transformType = 'transform';\n            _.transitionType = 'transition';\n        }\n        _.transformsEnabled = _.options.useTransform && (_.animType !== null && _.animType !== false);\n    };\n\n\n    Slick.prototype.setSlideClasses = function(index) {\n\n        var _ = this,\n            centerOffset, allSlides, indexOffset, remainder;\n\n        allSlides = _.$slider\n            .find('.slick-slide')\n            .removeClass('slick-active slick-center slick-current')\n            .attr('aria-hidden', 'true');\n\n        _.$slides\n            .eq(index)\n            .addClass('slick-current');\n\n        if (_.options.centerMode === true) {\n\n            var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0;\n\n            centerOffset = Math.floor(_.options.slidesToShow / 2);\n\n            if (_.options.infinite === true) {\n\n                if (index >= centerOffset && index <= (_.slideCount - 1) - centerOffset) {\n                    _.$slides\n                        .slice(index - centerOffset + evenCoef, index + centerOffset + 1)\n                        .addClass('slick-active')\n                        .attr('aria-hidden', 'false');\n\n                } else {\n\n                    indexOffset = _.options.slidesToShow + index;\n                    allSlides\n                        .slice(indexOffset - centerOffset + 1 + evenCoef, indexOffset + centerOffset + 2)\n                        .addClass('slick-active')\n                        .attr('aria-hidden', 'false');\n\n                }\n\n                if (index === 0) {\n\n                    allSlides\n                        .eq(allSlides.length - 1 - _.options.slidesToShow)\n                        .addClass('slick-center');\n\n                } else if (index === _.slideCount - 1) {\n\n                    allSlides\n                        .eq(_.options.slidesToShow)\n                        .addClass('slick-center');\n\n                }\n\n            }\n\n            _.$slides\n                .eq(index)\n                .addClass('slick-center');\n\n        } else {\n\n            if (index >= 0 && index <= (_.slideCount - _.options.slidesToShow)) {\n\n                _.$slides\n                    .slice(index, index + _.options.slidesToShow)\n                    .addClass('slick-active')\n                    .attr('aria-hidden', 'false');\n\n            } else if (allSlides.length <= _.options.slidesToShow) {\n\n                allSlides\n                    .addClass('slick-active')\n                    .attr('aria-hidden', 'false');\n\n            } else {\n\n                remainder = _.slideCount % _.options.slidesToShow;\n                indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index;\n\n                if (_.options.slidesToShow == _.options.slidesToScroll && (_.slideCount - index) < _.options.slidesToShow) {\n\n                    allSlides\n                        .slice(indexOffset - (_.options.slidesToShow - remainder), indexOffset + remainder)\n                        .addClass('slick-active')\n                        .attr('aria-hidden', 'false');\n\n                } else {\n\n                    allSlides\n                        .slice(indexOffset, indexOffset + _.options.slidesToShow)\n                        .addClass('slick-active')\n                        .attr('aria-hidden', 'false');\n\n                }\n\n            }\n\n        }\n\n        if (_.options.lazyLoad === 'ondemand' || _.options.lazyLoad === 'anticipated') {\n            _.lazyLoad();\n        }\n    };\n\n    Slick.prototype.setupInfinite = function() {\n\n        var _ = this,\n            i, slideIndex, infiniteCount;\n\n        if (_.options.fade === true) {\n            _.options.centerMode = false;\n        }\n\n        if (_.options.infinite === true && _.options.fade === false) {\n\n            slideIndex = null;\n\n            if (_.slideCount > _.options.slidesToShow) {\n\n                if (_.options.centerMode === true) {\n                    infiniteCount = _.options.slidesToShow + 1;\n                } else {\n                    infiniteCount = _.options.slidesToShow;\n                }\n\n                for (i = _.slideCount; i > (_.slideCount -\n                        infiniteCount); i -= 1) {\n                    slideIndex = i - 1;\n                    $(_.$slides[slideIndex]).clone(true).attr('id', '')\n                        .attr('data-slick-index', slideIndex - _.slideCount)\n                        .prependTo(_.$slideTrack).addClass('slick-cloned');\n                }\n                for (i = 0; i < infiniteCount  + _.slideCount; i += 1) {\n                    slideIndex = i;\n                    $(_.$slides[slideIndex]).clone(true).attr('id', '')\n                        .attr('data-slick-index', slideIndex + _.slideCount)\n                        .appendTo(_.$slideTrack).addClass('slick-cloned');\n                }\n                _.$slideTrack.find('.slick-cloned').find('[id]').each(function() {\n                    $(this).attr('id', '');\n                });\n\n            }\n\n        }\n\n    };\n\n    Slick.prototype.interrupt = function( toggle ) {\n\n        var _ = this;\n\n        if( !toggle ) {\n            _.autoPlay();\n        }\n        _.interrupted = toggle;\n\n    };\n\n    Slick.prototype.selectHandler = function(event) {\n\n        var _ = this;\n\n        var targetElement =\n            $(event.target).is('.slick-slide') ?\n                $(event.target) :\n                $(event.target).parents('.slick-slide');\n\n        var index = parseInt(targetElement.attr('data-slick-index'));\n\n        if (!index) index = 0;\n\n        if (_.slideCount <= _.options.slidesToShow) {\n\n            _.slideHandler(index, false, true);\n            return;\n\n        }\n\n        _.slideHandler(index);\n\n    };\n\n    Slick.prototype.slideHandler = function(index, sync, dontAnimate) {\n\n        var targetSlide, animSlide, oldSlide, slideLeft, targetLeft = null,\n            _ = this, navTarget;\n\n        sync = sync || false;\n\n        if (_.animating === true && _.options.waitForAnimate === true) {\n            return;\n        }\n\n        if (_.options.fade === true && _.currentSlide === index) {\n            return;\n        }\n\n        if (sync === false) {\n            _.asNavFor(index);\n        }\n\n        targetSlide = index;\n        targetLeft = _.getLeft(targetSlide);\n        slideLeft = _.getLeft(_.currentSlide);\n\n        _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft;\n\n        if (_.options.infinite === false && _.options.centerMode === false && (index < 0 || index > _.getDotCount() * _.options.slidesToScroll)) {\n            if (_.options.fade === false) {\n                targetSlide = _.currentSlide;\n                if (dontAnimate !== true && _.slideCount > _.options.slidesToShow) {\n                    _.animateSlide(slideLeft, function() {\n                        _.postSlide(targetSlide);\n                    });\n                } else {\n                    _.postSlide(targetSlide);\n                }\n            }\n            return;\n        } else if (_.options.infinite === false && _.options.centerMode === true && (index < 0 || index > (_.slideCount - _.options.slidesToScroll))) {\n            if (_.options.fade === false) {\n                targetSlide = _.currentSlide;\n                if (dontAnimate !== true && _.slideCount > _.options.slidesToShow) {\n                    _.animateSlide(slideLeft, function() {\n                        _.postSlide(targetSlide);\n                    });\n                } else {\n                    _.postSlide(targetSlide);\n                }\n            }\n            return;\n        }\n\n        if ( _.options.autoplay ) {\n            clearInterval(_.autoPlayTimer);\n        }\n\n        if (targetSlide < 0) {\n            if (_.slideCount % _.options.slidesToScroll !== 0) {\n                animSlide = _.slideCount - (_.slideCount % _.options.slidesToScroll);\n            } else {\n                animSlide = _.slideCount + targetSlide;\n            }\n        } else if (targetSlide >= _.slideCount) {\n            if (_.slideCount % _.options.slidesToScroll !== 0) {\n                animSlide = 0;\n            } else {\n                animSlide = targetSlide - _.slideCount;\n            }\n        } else {\n            animSlide = targetSlide;\n        }\n\n        _.animating = true;\n\n        _.$slider.trigger('beforeChange', [_, _.currentSlide, animSlide]);\n\n        oldSlide = _.currentSlide;\n        _.currentSlide = animSlide;\n\n        _.setSlideClasses(_.currentSlide);\n\n        if ( _.options.asNavFor ) {\n\n            navTarget = _.getNavTarget();\n            navTarget = navTarget.slick('getSlick');\n\n            if ( navTarget.slideCount <= navTarget.options.slidesToShow ) {\n                navTarget.setSlideClasses(_.currentSlide);\n            }\n\n        }\n\n        _.updateDots();\n        _.updateArrows();\n\n        if (_.options.fade === true) {\n            if (dontAnimate !== true) {\n\n                _.fadeSlideOut(oldSlide);\n\n                _.fadeSlide(animSlide, function() {\n                    _.postSlide(animSlide);\n                });\n\n            } else {\n                _.postSlide(animSlide);\n            }\n            _.animateHeight();\n            return;\n        }\n\n        if (dontAnimate !== true && _.slideCount > _.options.slidesToShow) {\n            _.animateSlide(targetLeft, function() {\n                _.postSlide(animSlide);\n            });\n        } else {\n            _.postSlide(animSlide);\n        }\n\n    };\n\n    Slick.prototype.startLoad = function() {\n\n        var _ = this;\n\n        if (_.options.arrows === true && _.slideCount > _.options.slidesToShow) {\n\n            _.$prevArrow.hide();\n            _.$nextArrow.hide();\n\n        }\n\n        if (_.options.dots === true && _.slideCount > _.options.slidesToShow) {\n\n            _.$dots.hide();\n\n        }\n\n        _.$slider.addClass('slick-loading');\n\n    };\n\n    Slick.prototype.swipeDirection = function() {\n\n        var xDist, yDist, r, swipeAngle, _ = this;\n\n        xDist = _.touchObject.startX - _.touchObject.curX;\n        yDist = _.touchObject.startY - _.touchObject.curY;\n        r = Math.atan2(yDist, xDist);\n\n        swipeAngle = Math.round(r * 180 / Math.PI);\n        if (swipeAngle < 0) {\n            swipeAngle = 360 - Math.abs(swipeAngle);\n        }\n\n        if ((swipeAngle <= 45) && (swipeAngle >= 0)) {\n            return (_.options.rtl === false ? 'left' : 'right');\n        }\n        if ((swipeAngle <= 360) && (swipeAngle >= 315)) {\n            return (_.options.rtl === false ? 'left' : 'right');\n        }\n        if ((swipeAngle >= 135) && (swipeAngle <= 225)) {\n            return (_.options.rtl === false ? 'right' : 'left');\n        }\n        if (_.options.verticalSwiping === true) {\n            if ((swipeAngle >= 35) && (swipeAngle <= 135)) {\n                return 'down';\n            } else {\n                return 'up';\n            }\n        }\n\n        return 'vertical';\n\n    };\n\n    Slick.prototype.swipeEnd = function(event) {\n\n        var _ = this,\n            slideCount,\n            direction;\n\n        _.dragging = false;\n        _.swiping = false;\n\n        if (_.scrolling) {\n            _.scrolling = false;\n            return false;\n        }\n\n        _.interrupted = false;\n        _.shouldClick = ( _.touchObject.swipeLength > 10 ) ? false : true;\n\n        if ( _.touchObject.curX === undefined ) {\n            return false;\n        }\n\n        if ( _.touchObject.edgeHit === true ) {\n            _.$slider.trigger('edge', [_, _.swipeDirection() ]);\n        }\n\n        if ( _.touchObject.swipeLength >= _.touchObject.minSwipe ) {\n\n            direction = _.swipeDirection();\n\n            switch ( direction ) {\n\n                case 'left':\n                case 'down':\n\n                    slideCount =\n                        _.options.swipeToSlide ?\n                            _.checkNavigable( _.currentSlide + _.getSlideCount() ) :\n                            _.currentSlide + _.getSlideCount();\n\n                    _.currentDirection = 0;\n\n                    break;\n\n                case 'right':\n                case 'up':\n\n                    slideCount =\n                        _.options.swipeToSlide ?\n                            _.checkNavigable( _.currentSlide - _.getSlideCount() ) :\n                            _.currentSlide - _.getSlideCount();\n\n                    _.currentDirection = 1;\n\n                    break;\n\n                default:\n\n\n            }\n\n            if( direction != 'vertical' ) {\n\n                _.slideHandler( slideCount );\n                _.touchObject = {};\n                _.$slider.trigger('swipe', [_, direction ]);\n\n            }\n\n        } else {\n\n            if ( _.touchObject.startX !== _.touchObject.curX ) {\n\n                _.slideHandler( _.currentSlide );\n                _.touchObject = {};\n\n            }\n\n        }\n\n    };\n\n    Slick.prototype.swipeHandler = function(event) {\n\n        var _ = this;\n\n        if ((_.options.swipe === false) || ('ontouchend' in document && _.options.swipe === false)) {\n            return;\n        } else if (_.options.draggable === false && event.type.indexOf('mouse') !== -1) {\n            return;\n        }\n\n        _.touchObject.fingerCount = event.originalEvent && event.originalEvent.touches !== undefined ?\n            event.originalEvent.touches.length : 1;\n\n        _.touchObject.minSwipe = _.listWidth / _.options\n            .touchThreshold;\n\n        if (_.options.verticalSwiping === true) {\n            _.touchObject.minSwipe = _.listHeight / _.options\n                .touchThreshold;\n        }\n\n        switch (event.data.action) {\n\n            case 'start':\n                _.swipeStart(event);\n                break;\n\n            case 'move':\n                _.swipeMove(event);\n                break;\n\n            case 'end':\n                _.swipeEnd(event);\n                break;\n\n        }\n\n    };\n\n    Slick.prototype.swipeMove = function(event) {\n\n        var _ = this,\n            edgeWasHit = false,\n            curLeft, swipeDirection, swipeLength, positionOffset, touches, verticalSwipeLength;\n\n        touches = event.originalEvent !== undefined ? event.originalEvent.touches : null;\n\n        if (!_.dragging || _.scrolling || touches && touches.length !== 1) {\n            return false;\n        }\n\n        curLeft = _.getLeft(_.currentSlide);\n\n        _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX;\n        _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY;\n\n        _.touchObject.swipeLength = Math.round(Math.sqrt(\n            Math.pow(_.touchObject.curX - _.touchObject.startX, 2)));\n\n        verticalSwipeLength = Math.round(Math.sqrt(\n            Math.pow(_.touchObject.curY - _.touchObject.startY, 2)));\n\n        if (!_.options.verticalSwiping && !_.swiping && verticalSwipeLength > 4) {\n            _.scrolling = true;\n            return false;\n        }\n\n        if (_.options.verticalSwiping === true) {\n            _.touchObject.swipeLength = verticalSwipeLength;\n        }\n\n        swipeDirection = _.swipeDirection();\n\n        if (event.originalEvent !== undefined && _.touchObject.swipeLength > 4) {\n            _.swiping = true;\n            event.preventDefault();\n        }\n\n        positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);\n        if (_.options.verticalSwiping === true) {\n            positionOffset = _.touchObject.curY > _.touchObject.startY ? 1 : -1;\n        }\n\n\n        swipeLength = _.touchObject.swipeLength;\n\n        _.touchObject.edgeHit = false;\n\n        if (_.options.infinite === false) {\n            if ((_.currentSlide === 0 && swipeDirection === 'right') || (_.currentSlide >= _.getDotCount() && swipeDirection === 'left')) {\n                swipeLength = _.touchObject.swipeLength * _.options.edgeFriction;\n                _.touchObject.edgeHit = true;\n            }\n        }\n\n        if (_.options.vertical === false) {\n            _.swipeLeft = curLeft + swipeLength * positionOffset;\n        } else {\n            _.swipeLeft = curLeft + (swipeLength * (_.$list.height() / _.listWidth)) * positionOffset;\n        }\n        if (_.options.verticalSwiping === true) {\n            _.swipeLeft = curLeft + swipeLength * positionOffset;\n        }\n\n        if (_.options.fade === true || _.options.touchMove === false) {\n            return false;\n        }\n\n        if (_.animating === true) {\n            _.swipeLeft = null;\n            return false;\n        }\n\n        _.setCSS(_.swipeLeft);\n\n    };\n\n    Slick.prototype.swipeStart = function(event) {\n\n        var _ = this,\n            touches;\n\n        _.interrupted = true;\n\n        if (_.touchObject.fingerCount !== 1 || _.slideCount <= _.options.slidesToShow) {\n            _.touchObject = {};\n            return false;\n        }\n\n        if (event.originalEvent !== undefined && event.originalEvent.touches !== undefined) {\n            touches = event.originalEvent.touches[0];\n        }\n\n        _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX;\n        _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY;\n\n        _.dragging = true;\n\n    };\n\n    Slick.prototype.unfilterSlides = Slick.prototype.slickUnfilter = function() {\n\n        var _ = this;\n\n        if (_.$slidesCache !== null) {\n\n            _.unload();\n\n            _.$slideTrack.children(this.options.slide).detach();\n\n            _.$slidesCache.appendTo(_.$slideTrack);\n\n            _.reinit();\n\n        }\n\n    };\n\n    Slick.prototype.unload = function() {\n\n        var _ = this;\n\n        $('.slick-cloned', _.$slider).remove();\n\n        if (_.$dots) {\n            _.$dots.remove();\n        }\n\n        if (_.$prevArrow && _.htmlExpr.test(_.options.prevArrow)) {\n            _.$prevArrow.remove();\n        }\n\n        if (_.$nextArrow && _.htmlExpr.test(_.options.nextArrow)) {\n            _.$nextArrow.remove();\n        }\n\n        _.$slides\n            .removeClass('slick-slide slick-active slick-visible slick-current')\n            .attr('aria-hidden', 'true')\n            .css('width', '');\n\n    };\n\n    Slick.prototype.unslick = function(fromBreakpoint) {\n\n        var _ = this;\n        _.$slider.trigger('unslick', [_, fromBreakpoint]);\n        _.destroy();\n\n    };\n\n    Slick.prototype.updateArrows = function() {\n\n        var _ = this,\n            centerOffset;\n\n        centerOffset = Math.floor(_.options.slidesToShow / 2);\n\n        if ( _.options.arrows === true &&\n            _.slideCount > _.options.slidesToShow &&\n            !_.options.infinite ) {\n\n            _.$prevArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n            _.$nextArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n\n            if (_.currentSlide === 0) {\n\n                _.$prevArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n                _.$nextArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n\n            } else if (_.currentSlide >= _.slideCount - _.options.slidesToShow && _.options.centerMode === false) {\n\n                _.$nextArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n                _.$prevArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n\n            } else if (_.currentSlide >= _.slideCount - 1 && _.options.centerMode === true) {\n\n                _.$nextArrow.addClass('slick-disabled').attr('aria-disabled', 'true');\n                _.$prevArrow.removeClass('slick-disabled').attr('aria-disabled', 'false');\n\n            }\n\n        }\n\n    };\n\n    Slick.prototype.updateDots = function() {\n\n        var _ = this;\n\n        if (_.$dots !== null) {\n\n            _.$dots\n                .find('li')\n                    .removeClass('slick-active')\n                    .end();\n\n            _.$dots\n                .find('li')\n                .eq(Math.floor(_.currentSlide / _.options.slidesToScroll))\n                .addClass('slick-active');\n\n        }\n\n    };\n\n    Slick.prototype.visibility = function() {\n\n        var _ = this;\n\n        if ( _.options.autoplay ) {\n\n            if ( document[_.hidden] ) {\n\n                _.interrupted = true;\n\n            } else {\n\n                _.interrupted = false;\n\n            }\n\n        }\n\n    };\n\n    $.fn.slick = function() {\n        var _ = this,\n            opt = arguments[0],\n            args = Array.prototype.slice.call(arguments, 1),\n            l = _.length,\n            i,\n            ret;\n        for (i = 0; i < l; i++) {\n            if (typeof opt == 'object' || typeof opt == 'undefined')\n                _[i].slick = new Slick(_[i], opt);\n            else\n                ret = _[i].slick[opt].apply(_[i].slick, args);\n            if (typeof ret != 'undefined') return ret;\n        }\n        return _;\n    };\n\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAE,WAASA,OAAO,EAAE;EAChB,YAAY;;EACZ,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5CD,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAED,OAAO,CAAC;EAC/B,CAAC,MAAM,IAAI,OAAOG,OAAO,KAAK,WAAW,EAAE;IACvCC,MAAM,CAACD,OAAO,GAAGH,OAAO,CAACK,OAAO,CAAC,QAAQ,CAAC,CAAC;EAC/C,CAAC,MAAM;IACHL,OAAO,CAACM,MAAM,CAAC;EACnB;AAEJ,CAAC,EAAC,UAASC,CAAC,EAAE;EACV,YAAY;;EACZ,IAAIC,KAAK,GAAGC,MAAM,CAACD,KAAK,IAAI,CAAC,CAAC;EAE9BA,KAAK,GAAI,YAAW;IAEhB,IAAIE,WAAW,GAAG,CAAC;IAEnB,SAASF,KAAKA,CAACG,OAAO,EAAEC,QAAQ,EAAE;MAE9B,IAAIC,CAAC,GAAG,IAAI;QAAEC,YAAY;MAE1BD,CAAC,CAACE,QAAQ,GAAG;QACTC,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAEX,CAAC,CAACI,OAAO,CAAC;QACxBQ,UAAU,EAAEZ,CAAC,CAACI,OAAO,CAAC;QACtBS,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,kFAAkF;QAC7FC,SAAS,EAAE,0EAA0E;QACrFC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,IAAI;QACnBC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,MAAM;QACrBC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,SAAAA,CAASC,MAAM,EAAEC,CAAC,EAAE;UAC9B,OAAOxB,CAAC,CAAC,0BAA0B,CAAC,CAACyB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC;QACDE,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE,IAAI;QACfC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,IAAI;QAClBC,IAAI,EAAE,KAAK;QACXC,aAAa,EAAE,KAAK;QACpBC,aAAa,EAAE,KAAK;QACpBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,UAAU;QACpBC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,gBAAgB,EAAE,KAAK;QACvBC,SAAS,EAAE,QAAQ;QACnBC,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,KAAK;QACVC,KAAK,EAAE,EAAE;QACTC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE,CAAC;QACjBC,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,IAAI;QACfC,cAAc,EAAE,CAAC;QACjBC,MAAM,EAAE,IAAI;QACZC,YAAY,EAAE,IAAI;QAClBC,aAAa,EAAE,KAAK;QACpBC,QAAQ,EAAE,KAAK;QACfC,eAAe,EAAE,KAAK;QACtBC,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE;MACZ,CAAC;MAEDtD,CAAC,CAACuD,QAAQ,GAAG;QACTC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,IAAI;QACnBC,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,KAAK;QAChBC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,KAAK;QACdC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,CAAC,CAAC;QACfC,iBAAiB,EAAE,KAAK;QACxBC,SAAS,EAAE;MACf,CAAC;MAEDvF,CAAC,CAACwF,MAAM,CAAClF,CAAC,EAAEA,CAAC,CAACuD,QAAQ,CAAC;MAEvBvD,CAAC,CAACmF,gBAAgB,GAAG,IAAI;MACzBnF,CAAC,CAACoF,QAAQ,GAAG,IAAI;MACjBpF,CAAC,CAACqF,QAAQ,GAAG,IAAI;MACjBrF,CAAC,CAACsF,WAAW,GAAG,EAAE;MAClBtF,CAAC,CAACuF,kBAAkB,GAAG,EAAE;MACzBvF,CAAC,CAACwF,cAAc,GAAG,KAAK;MACxBxF,CAAC,CAACyF,QAAQ,GAAG,KAAK;MAClBzF,CAAC,CAAC0F,WAAW,GAAG,KAAK;MACrB1F,CAAC,CAAC2F,MAAM,GAAG,QAAQ;MACnB3F,CAAC,CAAC4F,MAAM,GAAG,IAAI;MACf5F,CAAC,CAAC6F,YAAY,GAAG,IAAI;MACrB7F,CAAC,CAACmC,SAAS,GAAG,IAAI;MAClBnC,CAAC,CAAC8F,QAAQ,GAAG,CAAC;MACd9F,CAAC,CAAC+F,WAAW,GAAG,IAAI;MACpB/F,CAAC,CAACgG,OAAO,GAAGtG,CAAC,CAACI,OAAO,CAAC;MACtBE,CAAC,CAACiG,YAAY,GAAG,IAAI;MACrBjG,CAAC,CAACkG,aAAa,GAAG,IAAI;MACtBlG,CAAC,CAACmG,cAAc,GAAG,IAAI;MACvBnG,CAAC,CAACoG,gBAAgB,GAAG,kBAAkB;MACvCpG,CAAC,CAACqG,WAAW,GAAG,CAAC;MACjBrG,CAAC,CAACsG,WAAW,GAAG,IAAI;MAEpBrG,YAAY,GAAGP,CAAC,CAACI,OAAO,CAAC,CAACyG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;MAE7CvG,CAAC,CAACwG,OAAO,GAAG9G,CAAC,CAACwF,MAAM,CAAC,CAAC,CAAC,EAAElF,CAAC,CAACE,QAAQ,EAAEH,QAAQ,EAAEE,YAAY,CAAC;MAE5DD,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC3E,YAAY;MAEvC7B,CAAC,CAACyG,gBAAgB,GAAGzG,CAAC,CAACwG,OAAO;MAE9B,IAAI,OAAOE,QAAQ,CAACC,SAAS,KAAK,WAAW,EAAE;QAC3C3G,CAAC,CAAC2F,MAAM,GAAG,WAAW;QACtB3F,CAAC,CAACoG,gBAAgB,GAAG,qBAAqB;MAC9C,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACE,YAAY,KAAK,WAAW,EAAE;QACrD5G,CAAC,CAAC2F,MAAM,GAAG,cAAc;QACzB3F,CAAC,CAACoG,gBAAgB,GAAG,wBAAwB;MACjD;MAEApG,CAAC,CAAC6G,QAAQ,GAAGnH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC6G,QAAQ,EAAE7G,CAAC,CAAC;MACnCA,CAAC,CAAC+G,aAAa,GAAGrH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+G,aAAa,EAAE/G,CAAC,CAAC;MAC7CA,CAAC,CAACgH,gBAAgB,GAAGtH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACgH,gBAAgB,EAAEhH,CAAC,CAAC;MACnDA,CAAC,CAACiH,WAAW,GAAGvH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACiH,WAAW,EAAEjH,CAAC,CAAC;MACzCA,CAAC,CAACkH,YAAY,GAAGxH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACkH,YAAY,EAAElH,CAAC,CAAC;MAC3CA,CAAC,CAACmH,aAAa,GAAGzH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACmH,aAAa,EAAEnH,CAAC,CAAC;MAC7CA,CAAC,CAACoH,WAAW,GAAG1H,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACoH,WAAW,EAAEpH,CAAC,CAAC;MACzCA,CAAC,CAACqH,YAAY,GAAG3H,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACqH,YAAY,EAAErH,CAAC,CAAC;MAC3CA,CAAC,CAACsH,WAAW,GAAG5H,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACsH,WAAW,EAAEtH,CAAC,CAAC;MACzCA,CAAC,CAACuH,UAAU,GAAG7H,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACuH,UAAU,EAAEvH,CAAC,CAAC;MAEvCA,CAAC,CAACH,WAAW,GAAGA,WAAW,EAAE;;MAE7B;MACA;MACA;MACAG,CAAC,CAACwH,QAAQ,GAAG,2BAA2B;MAGxCxH,CAAC,CAACyH,mBAAmB,CAAC,CAAC;MACvBzH,CAAC,CAAC0H,IAAI,CAAC,IAAI,CAAC;IAEhB;IAEA,OAAO/H,KAAK;EAEhB,CAAC,CAAC,CAAE;EAEJA,KAAK,CAACgI,SAAS,CAACC,WAAW,GAAG,YAAW;IACrC,IAAI5H,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACwE,WAAW,CAACqD,IAAI,CAAC,eAAe,CAAC,CAACC,IAAI,CAAC;MACrC,aAAa,EAAE;IACnB,CAAC,CAAC,CAACD,IAAI,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAC;MACrC,UAAU,EAAE;IAChB,CAAC,CAAC;EAEN,CAAC;EAEDnI,KAAK,CAACgI,SAAS,CAACI,QAAQ,GAAGpI,KAAK,CAACgI,SAAS,CAACK,QAAQ,GAAG,UAASC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAE;IAErF,IAAInI,CAAC,GAAG,IAAI;IAEZ,IAAI,OAAOkI,KAAM,KAAK,SAAS,EAAE;MAC7BC,SAAS,GAAGD,KAAK;MACjBA,KAAK,GAAG,IAAI;IAChB,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,IAAKA,KAAK,IAAIlI,CAAC,CAACsE,UAAW,EAAE;MAC7C,OAAO,KAAK;IAChB;IAEAtE,CAAC,CAACoI,MAAM,CAAC,CAAC;IAEV,IAAI,OAAOF,KAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIA,KAAK,KAAK,CAAC,IAAIlI,CAAC,CAACyE,OAAO,CAAC4D,MAAM,KAAK,CAAC,EAAE;QACvC3I,CAAC,CAACuI,MAAM,CAAC,CAACK,QAAQ,CAACtI,CAAC,CAACwE,WAAW,CAAC;MACrC,CAAC,MAAM,IAAI2D,SAAS,EAAE;QAClBzI,CAAC,CAACuI,MAAM,CAAC,CAACM,YAAY,CAACvI,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACN,KAAK,CAAC,CAAC;MAC/C,CAAC,MAAM;QACHxI,CAAC,CAACuI,MAAM,CAAC,CAACQ,WAAW,CAACzI,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACN,KAAK,CAAC,CAAC;MAC9C;IACJ,CAAC,MAAM;MACH,IAAIC,SAAS,KAAK,IAAI,EAAE;QACpBzI,CAAC,CAACuI,MAAM,CAAC,CAACS,SAAS,CAAC1I,CAAC,CAACwE,WAAW,CAAC;MACtC,CAAC,MAAM;QACH9E,CAAC,CAACuI,MAAM,CAAC,CAACK,QAAQ,CAACtI,CAAC,CAACwE,WAAW,CAAC;MACrC;IACJ;IAEAxE,CAAC,CAACyE,OAAO,GAAGzE,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC;IAEtDvC,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC,CAACqG,MAAM,CAAC,CAAC;IAEnD5I,CAAC,CAACwE,WAAW,CAACqE,MAAM,CAAC7I,CAAC,CAACyE,OAAO,CAAC;IAE/BzE,CAAC,CAACyE,OAAO,CAACqE,IAAI,CAAC,UAASZ,KAAK,EAAEpI,OAAO,EAAE;MACpCJ,CAAC,CAACI,OAAO,CAAC,CAACgI,IAAI,CAAC,kBAAkB,EAAEI,KAAK,CAAC;IAC9C,CAAC,CAAC;IAEFlI,CAAC,CAACiG,YAAY,GAAGjG,CAAC,CAACyE,OAAO;IAE1BzE,CAAC,CAAC+I,MAAM,CAAC,CAAC;EAEd,CAAC;EAEDpJ,KAAK,CAACgI,SAAS,CAACqB,aAAa,GAAG,YAAW;IACvC,IAAIhJ,CAAC,GAAG,IAAI;IACZ,IAAIA,CAAC,CAACwG,OAAO,CAAC/D,YAAY,KAAK,CAAC,IAAIzC,CAAC,CAACwG,OAAO,CAACpG,cAAc,KAAK,IAAI,IAAIJ,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;MACnG,IAAI8F,YAAY,GAAGjJ,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACxI,CAAC,CAAC6D,YAAY,CAAC,CAACqF,WAAW,CAAC,IAAI,CAAC;MACjElJ,CAAC,CAAC8E,KAAK,CAACqE,OAAO,CAAC;QACZC,MAAM,EAAEH;MACZ,CAAC,EAAEjJ,CAAC,CAACwG,OAAO,CAAC7D,KAAK,CAAC;IACvB;EACJ,CAAC;EAEDhD,KAAK,CAACgI,SAAS,CAAC0B,YAAY,GAAG,UAASC,UAAU,EAAEC,QAAQ,EAAE;IAE1D,IAAIC,SAAS,GAAG,CAAC,CAAC;MACdxJ,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACgJ,aAAa,CAAC,CAAC;IAEjB,IAAIhJ,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,IAAItC,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;MACxDmG,UAAU,GAAG,CAACA,UAAU;IAC5B;IACA,IAAItJ,CAAC,CAACgF,iBAAiB,KAAK,KAAK,EAAE;MAC/B,IAAIhF,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;QAC9BnD,CAAC,CAACwE,WAAW,CAAC2E,OAAO,CAAC;UAClBM,IAAI,EAAEH;QACV,CAAC,EAAEtJ,CAAC,CAACwG,OAAO,CAAC7D,KAAK,EAAE3C,CAAC,CAACwG,OAAO,CAACjF,MAAM,EAAEgI,QAAQ,CAAC;MACnD,CAAC,MAAM;QACHvJ,CAAC,CAACwE,WAAW,CAAC2E,OAAO,CAAC;UAClBO,GAAG,EAAEJ;QACT,CAAC,EAAEtJ,CAAC,CAACwG,OAAO,CAAC7D,KAAK,EAAE3C,CAAC,CAACwG,OAAO,CAACjF,MAAM,EAAEgI,QAAQ,CAAC;MACnD;IAEJ,CAAC,MAAM;MAEH,IAAIvJ,CAAC,CAACwF,cAAc,KAAK,KAAK,EAAE;QAC5B,IAAIxF,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,EAAE;UACxBtC,CAAC,CAAC4D,WAAW,GAAG,CAAE5D,CAAC,CAAC4D,WAAY;QACpC;QACAlE,CAAC,CAAC;UACEiK,SAAS,EAAE3J,CAAC,CAAC4D;QACjB,CAAC,CAAC,CAACuF,OAAO,CAAC;UACPQ,SAAS,EAAEL;QACf,CAAC,EAAE;UACCM,QAAQ,EAAE5J,CAAC,CAACwG,OAAO,CAAC7D,KAAK;UACzBpB,MAAM,EAAEvB,CAAC,CAACwG,OAAO,CAACjF,MAAM;UACxBsI,IAAI,EAAE,SAAAA,CAASC,GAAG,EAAE;YAChBA,GAAG,GAAGC,IAAI,CAACC,IAAI,CAACF,GAAG,CAAC;YACpB,IAAI9J,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;cAC9BqG,SAAS,CAACxJ,CAAC,CAACoF,QAAQ,CAAC,GAAG,YAAY,GAChC0E,GAAG,GAAG,UAAU;cACpB9J,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACT,SAAS,CAAC;YAChC,CAAC,MAAM;cACHA,SAAS,CAACxJ,CAAC,CAACoF,QAAQ,CAAC,GAAG,gBAAgB,GACpC0E,GAAG,GAAG,KAAK;cACf9J,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACT,SAAS,CAAC;YAChC;UACJ,CAAC;UACDU,QAAQ,EAAE,SAAAA,CAAA,EAAW;YACjB,IAAIX,QAAQ,EAAE;cACVA,QAAQ,CAACY,IAAI,CAAC,CAAC;YACnB;UACJ;QACJ,CAAC,CAAC;MAEN,CAAC,MAAM;QAEHnK,CAAC,CAACoK,eAAe,CAAC,CAAC;QACnBd,UAAU,GAAGS,IAAI,CAACC,IAAI,CAACV,UAAU,CAAC;QAElC,IAAItJ,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;UAC9BqG,SAAS,CAACxJ,CAAC,CAACoF,QAAQ,CAAC,GAAG,cAAc,GAAGkE,UAAU,GAAG,eAAe;QACzE,CAAC,MAAM;UACHE,SAAS,CAACxJ,CAAC,CAACoF,QAAQ,CAAC,GAAG,kBAAkB,GAAGkE,UAAU,GAAG,UAAU;QACxE;QACAtJ,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACT,SAAS,CAAC;QAE5B,IAAID,QAAQ,EAAE;UACVc,UAAU,CAAC,YAAW;YAElBrK,CAAC,CAACsK,iBAAiB,CAAC,CAAC;YAErBf,QAAQ,CAACY,IAAI,CAAC,CAAC;UACnB,CAAC,EAAEnK,CAAC,CAACwG,OAAO,CAAC7D,KAAK,CAAC;QACvB;MAEJ;IAEJ;EAEJ,CAAC;EAEDhD,KAAK,CAACgI,SAAS,CAAC4C,YAAY,GAAG,YAAW;IAEtC,IAAIvK,CAAC,GAAG,IAAI;MACRQ,QAAQ,GAAGR,CAAC,CAACwG,OAAO,CAAChG,QAAQ;IAEjC,IAAKA,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAG;MACjCA,QAAQ,GAAGd,CAAC,CAACc,QAAQ,CAAC,CAACgK,GAAG,CAACxK,CAAC,CAACgG,OAAO,CAAC;IACzC;IAEA,OAAOxF,QAAQ;EAEnB,CAAC;EAEDb,KAAK,CAACgI,SAAS,CAACnH,QAAQ,GAAG,UAAS0H,KAAK,EAAE;IAEvC,IAAIlI,CAAC,GAAG,IAAI;MACRQ,QAAQ,GAAGR,CAAC,CAACuK,YAAY,CAAC,CAAC;IAE/B,IAAK/J,QAAQ,KAAK,IAAI,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAG;MACrDA,QAAQ,CAACsI,IAAI,CAAC,YAAW;QACrB,IAAI2B,MAAM,GAAG/K,CAAC,CAAC,IAAI,CAAC,CAACgL,KAAK,CAAC,UAAU,CAAC;QACtC,IAAG,CAACD,MAAM,CAACxF,SAAS,EAAE;UAClBwF,MAAM,CAACE,YAAY,CAACzC,KAAK,EAAE,IAAI,CAAC;QACpC;MACJ,CAAC,CAAC;IACN;EAEJ,CAAC;EAEDvI,KAAK,CAACgI,SAAS,CAACyC,eAAe,GAAG,UAAS7H,KAAK,EAAE;IAE9C,IAAIvC,CAAC,GAAG,IAAI;MACR4K,UAAU,GAAG,CAAC,CAAC;IAEnB,IAAI5K,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;MAC1BmJ,UAAU,CAAC5K,CAAC,CAACmG,cAAc,CAAC,GAAGnG,CAAC,CAACkG,aAAa,GAAG,GAAG,GAAGlG,CAAC,CAACwG,OAAO,CAAC7D,KAAK,GAAG,KAAK,GAAG3C,CAAC,CAACwG,OAAO,CAACzF,OAAO;IACtG,CAAC,MAAM;MACH6J,UAAU,CAAC5K,CAAC,CAACmG,cAAc,CAAC,GAAG,UAAU,GAAGnG,CAAC,CAACwG,OAAO,CAAC7D,KAAK,GAAG,KAAK,GAAG3C,CAAC,CAACwG,OAAO,CAACzF,OAAO;IAC3F;IAEA,IAAIf,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;MAC1BzB,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACW,UAAU,CAAC;IACjC,CAAC,MAAM;MACH5K,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACjG,KAAK,CAAC,CAAC0H,GAAG,CAACW,UAAU,CAAC;IACvC;EAEJ,CAAC;EAEDjL,KAAK,CAACgI,SAAS,CAACd,QAAQ,GAAG,YAAW;IAElC,IAAI7G,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC+G,aAAa,CAAC,CAAC;IAEjB,IAAK/G,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAG;MACzCzC,CAAC,CAAC0D,aAAa,GAAGmH,WAAW,CAAE7K,CAAC,CAACgH,gBAAgB,EAAEhH,CAAC,CAACwG,OAAO,CAAC5F,aAAc,CAAC;IAChF;EAEJ,CAAC;EAEDjB,KAAK,CAACgI,SAAS,CAACZ,aAAa,GAAG,YAAW;IAEvC,IAAI/G,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAAC0D,aAAa,EAAE;MACjBoH,aAAa,CAAC9K,CAAC,CAAC0D,aAAa,CAAC;IAClC;EAEJ,CAAC;EAED/D,KAAK,CAACgI,SAAS,CAACX,gBAAgB,GAAG,YAAW;IAE1C,IAAIhH,CAAC,GAAG,IAAI;MACR+K,OAAO,GAAG/K,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC9D,cAAc;IAEvD,IAAK,CAAC1C,CAAC,CAAC4F,MAAM,IAAI,CAAC5F,CAAC,CAAC0F,WAAW,IAAI,CAAC1F,CAAC,CAACyF,QAAQ,EAAG;MAE9C,IAAKzF,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,EAAG;QAEhC,IAAK5B,CAAC,CAAC8D,SAAS,KAAK,CAAC,IAAM9D,CAAC,CAAC6D,YAAY,GAAG,CAAC,KAAS7D,CAAC,CAACsE,UAAU,GAAG,CAAG,EAAE;UACvEtE,CAAC,CAAC8D,SAAS,GAAG,CAAC;QACnB,CAAC,MAEI,IAAK9D,CAAC,CAAC8D,SAAS,KAAK,CAAC,EAAG;UAE1BiH,OAAO,GAAG/K,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC9D,cAAc;UAEnD,IAAK1C,CAAC,CAAC6D,YAAY,GAAG,CAAC,KAAK,CAAC,EAAG;YAC5B7D,CAAC,CAAC8D,SAAS,GAAG,CAAC;UACnB;QAEJ;MAEJ;MAEA9D,CAAC,CAAC2K,YAAY,CAAEI,OAAQ,CAAC;IAE7B;EAEJ,CAAC;EAEDpL,KAAK,CAACgI,SAAS,CAACqD,WAAW,GAAG,YAAW;IAErC,IAAIhL,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACjG,MAAM,KAAK,IAAI,EAAG;MAE5BP,CAAC,CAACoE,UAAU,GAAG1E,CAAC,CAACM,CAAC,CAACwG,OAAO,CAAC/F,SAAS,CAAC,CAACwK,QAAQ,CAAC,aAAa,CAAC;MAC7DjL,CAAC,CAACmE,UAAU,GAAGzE,CAAC,CAACM,CAAC,CAACwG,OAAO,CAAC9F,SAAS,CAAC,CAACuK,QAAQ,CAAC,aAAa,CAAC;MAE7D,IAAIjL,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAG;QAExCzC,CAAC,CAACoE,UAAU,CAAC8G,WAAW,CAAC,cAAc,CAAC,CAACC,UAAU,CAAC,sBAAsB,CAAC;QAC3EnL,CAAC,CAACmE,UAAU,CAAC+G,WAAW,CAAC,cAAc,CAAC,CAACC,UAAU,CAAC,sBAAsB,CAAC;QAE3E,IAAInL,CAAC,CAACwH,QAAQ,CAAC4D,IAAI,CAACpL,CAAC,CAACwG,OAAO,CAAC/F,SAAS,CAAC,EAAE;UACtCT,CAAC,CAACoE,UAAU,CAACsE,SAAS,CAAC1I,CAAC,CAACwG,OAAO,CAACnG,YAAY,CAAC;QAClD;QAEA,IAAIL,CAAC,CAACwH,QAAQ,CAAC4D,IAAI,CAACpL,CAAC,CAACwG,OAAO,CAAC9F,SAAS,CAAC,EAAE;UACtCV,CAAC,CAACmE,UAAU,CAACmE,QAAQ,CAACtI,CAAC,CAACwG,OAAO,CAACnG,YAAY,CAAC;QACjD;QAEA,IAAIL,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,EAAE;UAC7B5B,CAAC,CAACoE,UAAU,CACP6G,QAAQ,CAAC,gBAAgB,CAAC,CAC1BnD,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC;QACtC;MAEJ,CAAC,MAAM;QAEH9H,CAAC,CAACoE,UAAU,CAACiH,GAAG,CAAErL,CAAC,CAACmE,UAAW,CAAC,CAE3B8G,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC;UACF,eAAe,EAAE,MAAM;UACvB,UAAU,EAAE;QAChB,CAAC,CAAC;MAEV;IAEJ;EAEJ,CAAC;EAEDnI,KAAK,CAACgI,SAAS,CAAC2D,SAAS,GAAG,YAAW;IAEnC,IAAItL,CAAC,GAAG,IAAI;MACRkB,CAAC;MAAEqK,GAAG;IAEV,IAAIvL,CAAC,CAACwG,OAAO,CAACpF,IAAI,KAAK,IAAI,IAAIpB,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAElEzC,CAAC,CAACgG,OAAO,CAACiF,QAAQ,CAAC,cAAc,CAAC;MAElCM,GAAG,GAAG7L,CAAC,CAAC,QAAQ,CAAC,CAACuL,QAAQ,CAACjL,CAAC,CAACwG,OAAO,CAACnF,SAAS,CAAC;MAE/C,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIlB,CAAC,CAACwL,WAAW,CAAC,CAAC,EAAEtK,CAAC,IAAI,CAAC,EAAE;QACtCqK,GAAG,CAAC1C,MAAM,CAACnJ,CAAC,CAAC,QAAQ,CAAC,CAACmJ,MAAM,CAAC7I,CAAC,CAACwG,OAAO,CAACxF,YAAY,CAACmJ,IAAI,CAAC,IAAI,EAAEnK,CAAC,EAAEkB,CAAC,CAAC,CAAC,CAAC;MAC3E;MAEAlB,CAAC,CAAC+D,KAAK,GAAGwH,GAAG,CAACjD,QAAQ,CAACtI,CAAC,CAACwG,OAAO,CAAClG,UAAU,CAAC;MAE5CN,CAAC,CAAC+D,KAAK,CAAC8D,IAAI,CAAC,IAAI,CAAC,CAAC4D,KAAK,CAAC,CAAC,CAACR,QAAQ,CAAC,cAAc,CAAC;IAEvD;EAEJ,CAAC;EAEDtL,KAAK,CAACgI,SAAS,CAAC+D,QAAQ,GAAG,YAAW;IAElC,IAAI1L,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACyE,OAAO,GACLzE,CAAC,CAACgG,OAAO,CACJ2C,QAAQ,CAAE3I,CAAC,CAACwG,OAAO,CAACjE,KAAK,GAAG,qBAAqB,CAAC,CAClD0I,QAAQ,CAAC,aAAa,CAAC;IAEhCjL,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACyE,OAAO,CAAC4D,MAAM;IAE/BrI,CAAC,CAACyE,OAAO,CAACqE,IAAI,CAAC,UAASZ,KAAK,EAAEpI,OAAO,EAAE;MACpCJ,CAAC,CAACI,OAAO,CAAC,CACLgI,IAAI,CAAC,kBAAkB,EAAEI,KAAK,CAAC,CAC/B3B,IAAI,CAAC,iBAAiB,EAAE7G,CAAC,CAACI,OAAO,CAAC,CAACgI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAChE,CAAC,CAAC;IAEF9H,CAAC,CAACgG,OAAO,CAACiF,QAAQ,CAAC,cAAc,CAAC;IAElCjL,CAAC,CAACwE,WAAW,GAAIxE,CAAC,CAACsE,UAAU,KAAK,CAAC,GAC/B5E,CAAC,CAAC,4BAA4B,CAAC,CAAC4I,QAAQ,CAACtI,CAAC,CAACgG,OAAO,CAAC,GACnDhG,CAAC,CAACyE,OAAO,CAACkH,OAAO,CAAC,4BAA4B,CAAC,CAACC,MAAM,CAAC,CAAC;IAE5D5L,CAAC,CAAC8E,KAAK,GAAG9E,CAAC,CAACwE,WAAW,CAACqH,IAAI,CACxB,2BAA2B,CAAC,CAACD,MAAM,CAAC,CAAC;IACzC5L,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;IAE/B,IAAIjK,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,IAAIb,CAAC,CAACwG,OAAO,CAAC3D,YAAY,KAAK,IAAI,EAAE;MAClE7C,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG,CAAC;IAChC;IAEAhD,CAAC,CAAC,gBAAgB,EAAEM,CAAC,CAACgG,OAAO,CAAC,CAACwE,GAAG,CAAC,OAAO,CAAC,CAACS,QAAQ,CAAC,eAAe,CAAC;IAErEjL,CAAC,CAAC8L,aAAa,CAAC,CAAC;IAEjB9L,CAAC,CAACgL,WAAW,CAAC,CAAC;IAEfhL,CAAC,CAACsL,SAAS,CAAC,CAAC;IAEbtL,CAAC,CAAC+L,UAAU,CAAC,CAAC;IAGd/L,CAAC,CAACgM,eAAe,CAAC,OAAOhM,CAAC,CAAC6D,YAAY,KAAK,QAAQ,GAAG7D,CAAC,CAAC6D,YAAY,GAAG,CAAC,CAAC;IAE1E,IAAI7D,CAAC,CAACwG,OAAO,CAAClF,SAAS,KAAK,IAAI,EAAE;MAC9BtB,CAAC,CAAC8E,KAAK,CAACmG,QAAQ,CAAC,WAAW,CAAC;IACjC;EAEJ,CAAC;EAEDtL,KAAK,CAACgI,SAAS,CAACsE,SAAS,GAAG,YAAW;IAEnC,IAAIjM,CAAC,GAAG,IAAI;MAAEkM,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,SAAS;MAAEC,WAAW;MAAEC,cAAc;MAACC,gBAAgB;IAE9EH,SAAS,GAAG3F,QAAQ,CAAC+F,sBAAsB,CAAC,CAAC;IAC7CF,cAAc,GAAGvM,CAAC,CAACgG,OAAO,CAAC2C,QAAQ,CAAC,CAAC;IAErC,IAAG3I,CAAC,CAACwG,OAAO,CAACnE,IAAI,GAAG,CAAC,EAAE;MAEnBmK,gBAAgB,GAAGxM,CAAC,CAACwG,OAAO,CAAChE,YAAY,GAAGxC,CAAC,CAACwG,OAAO,CAACnE,IAAI;MAC1DiK,WAAW,GAAGvC,IAAI,CAACC,IAAI,CACnBuC,cAAc,CAAClE,MAAM,GAAGmE,gBAC5B,CAAC;MAED,KAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,WAAW,EAAEJ,CAAC,EAAE,EAAC;QAC5B,IAAI3J,KAAK,GAAGmE,QAAQ,CAACgG,aAAa,CAAC,KAAK,CAAC;QACzC,KAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnM,CAAC,CAACwG,OAAO,CAACnE,IAAI,EAAE8J,CAAC,EAAE,EAAE;UAChC,IAAIQ,GAAG,GAAGjG,QAAQ,CAACgG,aAAa,CAAC,KAAK,CAAC;UACvC,KAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpM,CAAC,CAACwG,OAAO,CAAChE,YAAY,EAAE4J,CAAC,EAAE,EAAE;YACxC,IAAI3B,MAAM,GAAIyB,CAAC,GAAGM,gBAAgB,IAAKL,CAAC,GAAGnM,CAAC,CAACwG,OAAO,CAAChE,YAAY,GAAI4J,CAAC,CAAE;YACxE,IAAIG,cAAc,CAACK,GAAG,CAACnC,MAAM,CAAC,EAAE;cAC5BkC,GAAG,CAACE,WAAW,CAACN,cAAc,CAACK,GAAG,CAACnC,MAAM,CAAC,CAAC;YAC/C;UACJ;UACAlI,KAAK,CAACsK,WAAW,CAACF,GAAG,CAAC;QAC1B;QACAN,SAAS,CAACQ,WAAW,CAACtK,KAAK,CAAC;MAChC;MAEAvC,CAAC,CAACgG,OAAO,CAAC8G,KAAK,CAAC,CAAC,CAACjE,MAAM,CAACwD,SAAS,CAAC;MACnCrM,CAAC,CAACgG,OAAO,CAAC2C,QAAQ,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC,CACrCsB,GAAG,CAAC;QACD,OAAO,EAAE,GAAG,GAAGjK,CAAC,CAACwG,OAAO,CAAChE,YAAY,GAAI,GAAG;QAC5C,SAAS,EAAE;MACf,CAAC,CAAC;IAEV;EAEJ,CAAC;EAED7C,KAAK,CAACgI,SAAS,CAACoF,eAAe,GAAG,UAASC,OAAO,EAAEC,WAAW,EAAE;IAE7D,IAAIjN,CAAC,GAAG,IAAI;MACRkN,UAAU;MAAEC,gBAAgB;MAAEC,cAAc;MAAEC,iBAAiB,GAAG,KAAK;IAC3E,IAAIC,WAAW,GAAGtN,CAAC,CAACgG,OAAO,CAACuH,KAAK,CAAC,CAAC;IACnC,IAAIlH,WAAW,GAAGzG,MAAM,CAAC4N,UAAU,IAAI9N,CAAC,CAACE,MAAM,CAAC,CAAC2N,KAAK,CAAC,CAAC;IAExD,IAAIvN,CAAC,CAACmC,SAAS,KAAK,QAAQ,EAAE;MAC1BiL,cAAc,GAAG/G,WAAW;IAChC,CAAC,MAAM,IAAIrG,CAAC,CAACmC,SAAS,KAAK,QAAQ,EAAE;MACjCiL,cAAc,GAAGE,WAAW;IAChC,CAAC,MAAM,IAAItN,CAAC,CAACmC,SAAS,KAAK,KAAK,EAAE;MAC9BiL,cAAc,GAAGrD,IAAI,CAAC0D,GAAG,CAACpH,WAAW,EAAEiH,WAAW,CAAC;IACvD;IAEA,IAAKtN,CAAC,CAACwG,OAAO,CAACpE,UAAU,IACrBpC,CAAC,CAACwG,OAAO,CAACpE,UAAU,CAACiG,MAAM,IAC3BrI,CAAC,CAACwG,OAAO,CAACpE,UAAU,KAAK,IAAI,EAAE;MAE/B+K,gBAAgB,GAAG,IAAI;MAEvB,KAAKD,UAAU,IAAIlN,CAAC,CAACsF,WAAW,EAAE;QAC9B,IAAItF,CAAC,CAACsF,WAAW,CAACoI,cAAc,CAACR,UAAU,CAAC,EAAE;UAC1C,IAAIlN,CAAC,CAACyG,gBAAgB,CAAC1E,WAAW,KAAK,KAAK,EAAE;YAC1C,IAAIqL,cAAc,GAAGpN,CAAC,CAACsF,WAAW,CAAC4H,UAAU,CAAC,EAAE;cAC5CC,gBAAgB,GAAGnN,CAAC,CAACsF,WAAW,CAAC4H,UAAU,CAAC;YAChD;UACJ,CAAC,MAAM;YACH,IAAIE,cAAc,GAAGpN,CAAC,CAACsF,WAAW,CAAC4H,UAAU,CAAC,EAAE;cAC5CC,gBAAgB,GAAGnN,CAAC,CAACsF,WAAW,CAAC4H,UAAU,CAAC;YAChD;UACJ;QACJ;MACJ;MAEA,IAAIC,gBAAgB,KAAK,IAAI,EAAE;QAC3B,IAAInN,CAAC,CAACmF,gBAAgB,KAAK,IAAI,EAAE;UAC7B,IAAIgI,gBAAgB,KAAKnN,CAAC,CAACmF,gBAAgB,IAAI8H,WAAW,EAAE;YACxDjN,CAAC,CAACmF,gBAAgB,GACdgI,gBAAgB;YACpB,IAAInN,CAAC,CAACuF,kBAAkB,CAAC4H,gBAAgB,CAAC,KAAK,SAAS,EAAE;cACtDnN,CAAC,CAAC2N,OAAO,CAACR,gBAAgB,CAAC;YAC/B,CAAC,MAAM;cACHnN,CAAC,CAACwG,OAAO,GAAG9G,CAAC,CAACwF,MAAM,CAAC,CAAC,CAAC,EAAElF,CAAC,CAACyG,gBAAgB,EACvCzG,CAAC,CAACuF,kBAAkB,CAChB4H,gBAAgB,CAAC,CAAC;cAC1B,IAAIH,OAAO,KAAK,IAAI,EAAE;gBAClBhN,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC3E,YAAY;cAC3C;cACA7B,CAAC,CAAC4N,OAAO,CAACZ,OAAO,CAAC;YACtB;YACAK,iBAAiB,GAAGF,gBAAgB;UACxC;QACJ,CAAC,MAAM;UACHnN,CAAC,CAACmF,gBAAgB,GAAGgI,gBAAgB;UACrC,IAAInN,CAAC,CAACuF,kBAAkB,CAAC4H,gBAAgB,CAAC,KAAK,SAAS,EAAE;YACtDnN,CAAC,CAAC2N,OAAO,CAACR,gBAAgB,CAAC;UAC/B,CAAC,MAAM;YACHnN,CAAC,CAACwG,OAAO,GAAG9G,CAAC,CAACwF,MAAM,CAAC,CAAC,CAAC,EAAElF,CAAC,CAACyG,gBAAgB,EACvCzG,CAAC,CAACuF,kBAAkB,CAChB4H,gBAAgB,CAAC,CAAC;YAC1B,IAAIH,OAAO,KAAK,IAAI,EAAE;cAClBhN,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC3E,YAAY;YAC3C;YACA7B,CAAC,CAAC4N,OAAO,CAACZ,OAAO,CAAC;UACtB;UACAK,iBAAiB,GAAGF,gBAAgB;QACxC;MACJ,CAAC,MAAM;QACH,IAAInN,CAAC,CAACmF,gBAAgB,KAAK,IAAI,EAAE;UAC7BnF,CAAC,CAACmF,gBAAgB,GAAG,IAAI;UACzBnF,CAAC,CAACwG,OAAO,GAAGxG,CAAC,CAACyG,gBAAgB;UAC9B,IAAIuG,OAAO,KAAK,IAAI,EAAE;YAClBhN,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC3E,YAAY;UAC3C;UACA7B,CAAC,CAAC4N,OAAO,CAACZ,OAAO,CAAC;UAClBK,iBAAiB,GAAGF,gBAAgB;QACxC;MACJ;;MAEA;MACA,IAAI,CAACH,OAAO,IAAIK,iBAAiB,KAAK,KAAK,EAAG;QAC1CrN,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,YAAY,EAAE,CAAC7N,CAAC,EAAEqN,iBAAiB,CAAC,CAAC;MAC3D;IACJ;EAEJ,CAAC;EAED1N,KAAK,CAACgI,SAAS,CAACV,WAAW,GAAG,UAAS6G,KAAK,EAAEC,WAAW,EAAE;IAEvD,IAAI/N,CAAC,GAAG,IAAI;MACRgO,OAAO,GAAGtO,CAAC,CAACoO,KAAK,CAACG,aAAa,CAAC;MAChCC,WAAW;MAAEvJ,WAAW;MAAEwJ,YAAY;;IAE1C;IACA,IAAGH,OAAO,CAACI,EAAE,CAAC,GAAG,CAAC,EAAE;MAChBN,KAAK,CAACO,cAAc,CAAC,CAAC;IAC1B;;IAEA;IACA,IAAG,CAACL,OAAO,CAACI,EAAE,CAAC,IAAI,CAAC,EAAE;MAClBJ,OAAO,GAAGA,OAAO,CAACM,OAAO,CAAC,IAAI,CAAC;IACnC;IAEAH,YAAY,GAAInO,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAc,KAAK,CAAE;IAC9DwL,WAAW,GAAGC,YAAY,GAAG,CAAC,GAAG,CAACnO,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACwG,OAAO,CAAC9D,cAAc;IAE3F,QAAQoL,KAAK,CAACvH,IAAI,CAACgI,OAAO;MAEtB,KAAK,UAAU;QACX5J,WAAW,GAAGuJ,WAAW,KAAK,CAAC,GAAGlO,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGyL,WAAW;QACjG,IAAIlO,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;UACvCzC,CAAC,CAAC2K,YAAY,CAAC3K,CAAC,CAAC6D,YAAY,GAAGc,WAAW,EAAE,KAAK,EAAEoJ,WAAW,CAAC;QACpE;QACA;MAEJ,KAAK,MAAM;QACPpJ,WAAW,GAAGuJ,WAAW,KAAK,CAAC,GAAGlO,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAGwL,WAAW;QACxE,IAAIlO,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;UACvCzC,CAAC,CAAC2K,YAAY,CAAC3K,CAAC,CAAC6D,YAAY,GAAGc,WAAW,EAAE,KAAK,EAAEoJ,WAAW,CAAC;QACpE;QACA;MAEJ,KAAK,OAAO;QACR,IAAI7F,KAAK,GAAG4F,KAAK,CAACvH,IAAI,CAAC2B,KAAK,KAAK,CAAC,GAAG,CAAC,GAClC4F,KAAK,CAACvH,IAAI,CAAC2B,KAAK,IAAI8F,OAAO,CAAC9F,KAAK,CAAC,CAAC,GAAGlI,CAAC,CAACwG,OAAO,CAAC9D,cAAc;QAElE1C,CAAC,CAAC2K,YAAY,CAAC3K,CAAC,CAACwO,cAAc,CAACtG,KAAK,CAAC,EAAE,KAAK,EAAE6F,WAAW,CAAC;QAC3DC,OAAO,CAACrF,QAAQ,CAAC,CAAC,CAACkF,OAAO,CAAC,OAAO,CAAC;QACnC;MAEJ;QACI;IACR;EAEJ,CAAC;EAEDlO,KAAK,CAACgI,SAAS,CAAC6G,cAAc,GAAG,UAAStG,KAAK,EAAE;IAE7C,IAAIlI,CAAC,GAAG,IAAI;MACRyO,UAAU;MAAEC,aAAa;IAE7BD,UAAU,GAAGzO,CAAC,CAAC2O,mBAAmB,CAAC,CAAC;IACpCD,aAAa,GAAG,CAAC;IACjB,IAAIxG,KAAK,GAAGuG,UAAU,CAACA,UAAU,CAACpG,MAAM,GAAG,CAAC,CAAC,EAAE;MAC3CH,KAAK,GAAGuG,UAAU,CAACA,UAAU,CAACpG,MAAM,GAAG,CAAC,CAAC;IAC7C,CAAC,MAAM;MACH,KAAK,IAAIuG,CAAC,IAAIH,UAAU,EAAE;QACtB,IAAIvG,KAAK,GAAGuG,UAAU,CAACG,CAAC,CAAC,EAAE;UACvB1G,KAAK,GAAGwG,aAAa;UACrB;QACJ;QACAA,aAAa,GAAGD,UAAU,CAACG,CAAC,CAAC;MACjC;IACJ;IAEA,OAAO1G,KAAK;EAChB,CAAC;EAEDvI,KAAK,CAACgI,SAAS,CAACkH,aAAa,GAAG,YAAW;IAEvC,IAAI7O,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACpF,IAAI,IAAIpB,CAAC,CAAC+D,KAAK,KAAK,IAAI,EAAE;MAEpCrE,CAAC,CAAC,IAAI,EAAEM,CAAC,CAAC+D,KAAK,CAAC,CACX+K,GAAG,CAAC,aAAa,EAAE9O,CAAC,CAACiH,WAAW,CAAC,CACjC6H,GAAG,CAAC,kBAAkB,EAAEpP,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,IAAI,CAAC,CAAC,CACtD8O,GAAG,CAAC,kBAAkB,EAAEpP,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,KAAK,CAAC,CAAC;MAE5D,IAAIA,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QAClCH,CAAC,CAAC+D,KAAK,CAAC+K,GAAG,CAAC,eAAe,EAAE9O,CAAC,CAACuH,UAAU,CAAC;MAC9C;IACJ;IAEAvH,CAAC,CAACgG,OAAO,CAAC8I,GAAG,CAAC,wBAAwB,CAAC;IAEvC,IAAI9O,CAAC,CAACwG,OAAO,CAACjG,MAAM,KAAK,IAAI,IAAIP,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACpEzC,CAAC,CAACoE,UAAU,IAAIpE,CAAC,CAACoE,UAAU,CAAC0K,GAAG,CAAC,aAAa,EAAE9O,CAAC,CAACiH,WAAW,CAAC;MAC9DjH,CAAC,CAACmE,UAAU,IAAInE,CAAC,CAACmE,UAAU,CAAC2K,GAAG,CAAC,aAAa,EAAE9O,CAAC,CAACiH,WAAW,CAAC;MAE9D,IAAIjH,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QAClCH,CAAC,CAACoE,UAAU,IAAIpE,CAAC,CAACoE,UAAU,CAAC0K,GAAG,CAAC,eAAe,EAAE9O,CAAC,CAACuH,UAAU,CAAC;QAC/DvH,CAAC,CAACmE,UAAU,IAAInE,CAAC,CAACmE,UAAU,CAAC2K,GAAG,CAAC,eAAe,EAAE9O,CAAC,CAACuH,UAAU,CAAC;MACnE;IACJ;IAEAvH,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,kCAAkC,EAAE9O,CAAC,CAACqH,YAAY,CAAC;IAC/DrH,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,iCAAiC,EAAE9O,CAAC,CAACqH,YAAY,CAAC;IAC9DrH,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,8BAA8B,EAAE9O,CAAC,CAACqH,YAAY,CAAC;IAC3DrH,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,oCAAoC,EAAE9O,CAAC,CAACqH,YAAY,CAAC;IAEjErH,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,aAAa,EAAE9O,CAAC,CAACkH,YAAY,CAAC;IAE1CxH,CAAC,CAACgH,QAAQ,CAAC,CAACoI,GAAG,CAAC9O,CAAC,CAACoG,gBAAgB,EAAEpG,CAAC,CAACgP,UAAU,CAAC;IAEjDhP,CAAC,CAACiP,kBAAkB,CAAC,CAAC;IAEtB,IAAIjP,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;MAClCH,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,eAAe,EAAE9O,CAAC,CAACuH,UAAU,CAAC;IAC9C;IAEA,IAAIvH,CAAC,CAACwG,OAAO,CAAC9E,aAAa,KAAK,IAAI,EAAE;MAClChC,CAAC,CAACM,CAAC,CAACwE,WAAW,CAAC,CAACmE,QAAQ,CAAC,CAAC,CAACmG,GAAG,CAAC,aAAa,EAAE9O,CAAC,CAACmH,aAAa,CAAC;IACnE;IAEAzH,CAAC,CAACE,MAAM,CAAC,CAACkP,GAAG,CAAC,gCAAgC,GAAG9O,CAAC,CAACH,WAAW,EAAEG,CAAC,CAACkP,iBAAiB,CAAC;IAEpFxP,CAAC,CAACE,MAAM,CAAC,CAACkP,GAAG,CAAC,qBAAqB,GAAG9O,CAAC,CAACH,WAAW,EAAEG,CAAC,CAACmP,MAAM,CAAC;IAE9DzP,CAAC,CAAC,mBAAmB,EAAEM,CAAC,CAACwE,WAAW,CAAC,CAACsK,GAAG,CAAC,WAAW,EAAE9O,CAAC,CAACqO,cAAc,CAAC;IAExE3O,CAAC,CAACE,MAAM,CAAC,CAACkP,GAAG,CAAC,mBAAmB,GAAG9O,CAAC,CAACH,WAAW,EAAEG,CAAC,CAACoH,WAAW,CAAC;EAErE,CAAC;EAEDzH,KAAK,CAACgI,SAAS,CAACsH,kBAAkB,GAAG,YAAW;IAE5C,IAAIjP,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,kBAAkB,EAAEpP,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9DA,CAAC,CAAC8E,KAAK,CAACgK,GAAG,CAAC,kBAAkB,EAAEpP,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,KAAK,CAAC,CAAC;EAEnE,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAACyH,WAAW,GAAG,YAAW;IAErC,IAAIpP,CAAC,GAAG,IAAI;MAAEuM,cAAc;IAE5B,IAAGvM,CAAC,CAACwG,OAAO,CAACnE,IAAI,GAAG,CAAC,EAAE;MACnBkK,cAAc,GAAGvM,CAAC,CAACyE,OAAO,CAACkE,QAAQ,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC;MAChD4D,cAAc,CAACpB,UAAU,CAAC,OAAO,CAAC;MAClCnL,CAAC,CAACgG,OAAO,CAAC8G,KAAK,CAAC,CAAC,CAACjE,MAAM,CAAC0D,cAAc,CAAC;IAC5C;EAEJ,CAAC;EAED5M,KAAK,CAACgI,SAAS,CAACT,YAAY,GAAG,UAAS4G,KAAK,EAAE;IAE3C,IAAI9N,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAAC+F,WAAW,KAAK,KAAK,EAAE;MACzB+H,KAAK,CAACuB,wBAAwB,CAAC,CAAC;MAChCvB,KAAK,CAACwB,eAAe,CAAC,CAAC;MACvBxB,KAAK,CAACO,cAAc,CAAC,CAAC;IAC1B;EAEJ,CAAC;EAED1O,KAAK,CAACgI,SAAS,CAAC4H,OAAO,GAAG,UAAS3B,OAAO,EAAE;IAExC,IAAI5N,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC+G,aAAa,CAAC,CAAC;IAEjB/G,CAAC,CAAC+E,WAAW,GAAG,CAAC,CAAC;IAElB/E,CAAC,CAAC6O,aAAa,CAAC,CAAC;IAEjBnP,CAAC,CAAC,eAAe,EAAEM,CAAC,CAACgG,OAAO,CAAC,CAAC4C,MAAM,CAAC,CAAC;IAEtC,IAAI5I,CAAC,CAAC+D,KAAK,EAAE;MACT/D,CAAC,CAAC+D,KAAK,CAACyL,MAAM,CAAC,CAAC;IACpB;IAEA,IAAKxP,CAAC,CAACoE,UAAU,IAAIpE,CAAC,CAACoE,UAAU,CAACiE,MAAM,EAAG;MAEvCrI,CAAC,CAACoE,UAAU,CACP8G,WAAW,CAAC,yCAAyC,CAAC,CACtDC,UAAU,CAAC,oCAAoC,CAAC,CAChDlB,GAAG,CAAC,SAAS,EAAC,EAAE,CAAC;MAEtB,IAAKjK,CAAC,CAACwH,QAAQ,CAAC4D,IAAI,CAAEpL,CAAC,CAACwG,OAAO,CAAC/F,SAAU,CAAC,EAAE;QACzCT,CAAC,CAACoE,UAAU,CAACoL,MAAM,CAAC,CAAC;MACzB;IACJ;IAEA,IAAKxP,CAAC,CAACmE,UAAU,IAAInE,CAAC,CAACmE,UAAU,CAACkE,MAAM,EAAG;MAEvCrI,CAAC,CAACmE,UAAU,CACP+G,WAAW,CAAC,yCAAyC,CAAC,CACtDC,UAAU,CAAC,oCAAoC,CAAC,CAChDlB,GAAG,CAAC,SAAS,EAAC,EAAE,CAAC;MAEtB,IAAKjK,CAAC,CAACwH,QAAQ,CAAC4D,IAAI,CAAEpL,CAAC,CAACwG,OAAO,CAAC9F,SAAU,CAAC,EAAE;QACzCV,CAAC,CAACmE,UAAU,CAACqL,MAAM,CAAC,CAAC;MACzB;IACJ;IAGA,IAAIxP,CAAC,CAACyE,OAAO,EAAE;MAEXzE,CAAC,CAACyE,OAAO,CACJyG,WAAW,CAAC,mEAAmE,CAAC,CAChFC,UAAU,CAAC,aAAa,CAAC,CACzBA,UAAU,CAAC,kBAAkB,CAAC,CAC9BrC,IAAI,CAAC,YAAU;QACZpJ,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC,OAAO,EAAEpI,CAAC,CAAC,IAAI,CAAC,CAAC6G,IAAI,CAAC,iBAAiB,CAAC,CAAC;MAC1D,CAAC,CAAC;MAENvG,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC,CAACqG,MAAM,CAAC,CAAC;MAEnD5I,CAAC,CAACwE,WAAW,CAACoE,MAAM,CAAC,CAAC;MAEtB5I,CAAC,CAAC8E,KAAK,CAAC8D,MAAM,CAAC,CAAC;MAEhB5I,CAAC,CAACgG,OAAO,CAAC6C,MAAM,CAAC7I,CAAC,CAACyE,OAAO,CAAC;IAC/B;IAEAzE,CAAC,CAACoP,WAAW,CAAC,CAAC;IAEfpP,CAAC,CAACgG,OAAO,CAACkF,WAAW,CAAC,cAAc,CAAC;IACrClL,CAAC,CAACgG,OAAO,CAACkF,WAAW,CAAC,mBAAmB,CAAC;IAC1ClL,CAAC,CAACgG,OAAO,CAACkF,WAAW,CAAC,cAAc,CAAC;IAErClL,CAAC,CAACiF,SAAS,GAAG,IAAI;IAElB,IAAG,CAAC2I,OAAO,EAAE;MACT5N,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,SAAS,EAAE,CAAC7N,CAAC,CAAC,CAAC;IACrC;EAEJ,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAAC2C,iBAAiB,GAAG,UAAS/H,KAAK,EAAE;IAEhD,IAAIvC,CAAC,GAAG,IAAI;MACR4K,UAAU,GAAG,CAAC,CAAC;IAEnBA,UAAU,CAAC5K,CAAC,CAACmG,cAAc,CAAC,GAAG,EAAE;IAEjC,IAAInG,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;MAC1BzB,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACW,UAAU,CAAC;IACjC,CAAC,MAAM;MACH5K,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACjG,KAAK,CAAC,CAAC0H,GAAG,CAACW,UAAU,CAAC;IACvC;EAEJ,CAAC;EAEDjL,KAAK,CAACgI,SAAS,CAAC8H,SAAS,GAAG,UAASC,UAAU,EAAEnG,QAAQ,EAAE;IAEvD,IAAIvJ,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwF,cAAc,KAAK,KAAK,EAAE;MAE5BxF,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACkH,UAAU,CAAC,CAACzF,GAAG,CAAC;QACzB3G,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD;MACtB,CAAC,CAAC;MAEFtD,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACkH,UAAU,CAAC,CAACvG,OAAO,CAAC;QAC7BwG,OAAO,EAAE;MACb,CAAC,EAAE3P,CAAC,CAACwG,OAAO,CAAC7D,KAAK,EAAE3C,CAAC,CAACwG,OAAO,CAACjF,MAAM,EAAEgI,QAAQ,CAAC;IAEnD,CAAC,MAAM;MAEHvJ,CAAC,CAACoK,eAAe,CAACsF,UAAU,CAAC;MAE7B1P,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACkH,UAAU,CAAC,CAACzF,GAAG,CAAC;QACzB0F,OAAO,EAAE,CAAC;QACVrM,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD;MACtB,CAAC,CAAC;MAEF,IAAIiG,QAAQ,EAAE;QACVc,UAAU,CAAC,YAAW;UAElBrK,CAAC,CAACsK,iBAAiB,CAACoF,UAAU,CAAC;UAE/BnG,QAAQ,CAACY,IAAI,CAAC,CAAC;QACnB,CAAC,EAAEnK,CAAC,CAACwG,OAAO,CAAC7D,KAAK,CAAC;MACvB;IAEJ;EAEJ,CAAC;EAEDhD,KAAK,CAACgI,SAAS,CAACiI,YAAY,GAAG,UAASF,UAAU,EAAE;IAEhD,IAAI1P,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwF,cAAc,KAAK,KAAK,EAAE;MAE5BxF,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACkH,UAAU,CAAC,CAACvG,OAAO,CAAC;QAC7BwG,OAAO,EAAE,CAAC;QACVrM,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG;MAC/B,CAAC,EAAEtD,CAAC,CAACwG,OAAO,CAAC7D,KAAK,EAAE3C,CAAC,CAACwG,OAAO,CAACjF,MAAM,CAAC;IAEzC,CAAC,MAAM;MAEHvB,CAAC,CAACoK,eAAe,CAACsF,UAAU,CAAC;MAE7B1P,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACkH,UAAU,CAAC,CAACzF,GAAG,CAAC;QACzB0F,OAAO,EAAE,CAAC;QACVrM,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG;MAC/B,CAAC,CAAC;IAEN;EAEJ,CAAC;EAED3D,KAAK,CAACgI,SAAS,CAACkI,YAAY,GAAGlQ,KAAK,CAACgI,SAAS,CAACmI,WAAW,GAAG,UAASC,MAAM,EAAE;IAE1E,IAAI/P,CAAC,GAAG,IAAI;IAEZ,IAAI+P,MAAM,KAAK,IAAI,EAAE;MAEjB/P,CAAC,CAACiG,YAAY,GAAGjG,CAAC,CAACyE,OAAO;MAE1BzE,CAAC,CAACoI,MAAM,CAAC,CAAC;MAEVpI,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC,CAACqG,MAAM,CAAC,CAAC;MAEnD5I,CAAC,CAACiG,YAAY,CAAC8J,MAAM,CAACA,MAAM,CAAC,CAACzH,QAAQ,CAACtI,CAAC,CAACwE,WAAW,CAAC;MAErDxE,CAAC,CAAC+I,MAAM,CAAC,CAAC;IAEd;EAEJ,CAAC;EAEDpJ,KAAK,CAACgI,SAAS,CAACqI,YAAY,GAAG,YAAW;IAEtC,IAAIhQ,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACgG,OAAO,CACJ8I,GAAG,CAAC,wBAAwB,CAAC,CAC7BmB,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE,UAASnC,KAAK,EAAE;MAEnDA,KAAK,CAACuB,wBAAwB,CAAC,CAAC;MAChC,IAAIa,GAAG,GAAGxQ,CAAC,CAAC,IAAI,CAAC;MAEjB2K,UAAU,CAAC,YAAW;QAElB,IAAIrK,CAAC,CAACwG,OAAO,CAACvE,YAAY,EAAG;UACzBjC,CAAC,CAACyF,QAAQ,GAAGyK,GAAG,CAAC9B,EAAE,CAAC,QAAQ,CAAC;UAC7BpO,CAAC,CAAC6G,QAAQ,CAAC,CAAC;QAChB;MAEJ,CAAC,EAAE,CAAC,CAAC;IAET,CAAC,CAAC;EACN,CAAC;EAEDlH,KAAK,CAACgI,SAAS,CAACwI,UAAU,GAAGxQ,KAAK,CAACgI,SAAS,CAACyI,iBAAiB,GAAG,YAAW;IAExE,IAAIpQ,CAAC,GAAG,IAAI;IACZ,OAAOA,CAAC,CAAC6D,YAAY;EAEzB,CAAC;EAEDlE,KAAK,CAACgI,SAAS,CAAC6D,WAAW,GAAG,YAAW;IAErC,IAAIxL,CAAC,GAAG,IAAI;IAEZ,IAAIqQ,UAAU,GAAG,CAAC;IAClB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,CAAC;IAEhB,IAAIvQ,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,EAAE;MAC7B,IAAI5B,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;QACvC,EAAE8N,QAAQ;MACf,CAAC,MAAM;QACH,OAAOF,UAAU,GAAGrQ,CAAC,CAACsE,UAAU,EAAE;UAC9B,EAAEiM,QAAQ;UACVF,UAAU,GAAGC,OAAO,GAAGtQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc;UAC/C4N,OAAO,IAAItQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc,IAAI1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGzC,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY;QACrH;MACJ;IACJ,CAAC,MAAM,IAAIzC,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;MACtC0P,QAAQ,GAAGvQ,CAAC,CAACsE,UAAU;IAC3B,CAAC,MAAM,IAAG,CAACtE,CAAC,CAACwG,OAAO,CAAChG,QAAQ,EAAE;MAC3B+P,QAAQ,GAAG,CAAC,GAAGxG,IAAI,CAACC,IAAI,CAAC,CAAChK,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIzC,CAAC,CAACwG,OAAO,CAAC9D,cAAc,CAAC;IAChG,CAAC,MAAK;MACF,OAAO2N,UAAU,GAAGrQ,CAAC,CAACsE,UAAU,EAAE;QAC9B,EAAEiM,QAAQ;QACVF,UAAU,GAAGC,OAAO,GAAGtQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc;QAC/C4N,OAAO,IAAItQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc,IAAI1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGzC,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY;MACrH;IACJ;IAEA,OAAO8N,QAAQ,GAAG,CAAC;EAEvB,CAAC;EAED5Q,KAAK,CAACgI,SAAS,CAAC6I,OAAO,GAAG,UAASd,UAAU,EAAE;IAE3C,IAAI1P,CAAC,GAAG,IAAI;MACRsJ,UAAU;MACVmH,cAAc;MACdC,cAAc,GAAG,CAAC;MAClBC,WAAW;MACXC,IAAI;IAER5Q,CAAC,CAAC2E,WAAW,GAAG,CAAC;IACjB8L,cAAc,GAAGzQ,CAAC,CAACyE,OAAO,CAACgH,KAAK,CAAC,CAAC,CAACvC,WAAW,CAAC,IAAI,CAAC;IAEpD,IAAIlJ,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,EAAE;MAC7B,IAAI5B,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;QACvCzC,CAAC,CAAC2E,WAAW,GAAI3E,CAAC,CAACuE,UAAU,GAAGvE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAI,CAAC,CAAC;QAC5DmO,IAAI,GAAG,CAAC,CAAC;QAET,IAAI5Q,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,IAAI,IAAInD,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;UAC9D,IAAIb,CAAC,CAACwG,OAAO,CAAC/D,YAAY,KAAK,CAAC,EAAE;YAC9BmO,IAAI,GAAG,CAAC,GAAG;UACf,CAAC,MAAM,IAAI5Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,KAAK,CAAC,EAAE;YACrCmO,IAAI,GAAG,CAAC,CAAC;UACb;QACJ;QACAF,cAAc,GAAID,cAAc,GAAGzQ,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAImO,IAAI;MACrE;MACA,IAAI5Q,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAc,KAAK,CAAC,EAAE;QAC/C,IAAIgN,UAAU,GAAG1P,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG1C,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;UAC/F,IAAIiN,UAAU,GAAG1P,CAAC,CAACsE,UAAU,EAAE;YAC3BtE,CAAC,CAAC2E,WAAW,GAAI,CAAC3E,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIiN,UAAU,GAAG1P,CAAC,CAACsE,UAAU,CAAC,IAAItE,CAAC,CAACuE,UAAU,GAAI,CAAC,CAAC;YAC5FmM,cAAc,GAAI,CAAC1Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIiN,UAAU,GAAG1P,CAAC,CAACsE,UAAU,CAAC,IAAImM,cAAc,GAAI,CAAC,CAAC;UACnG,CAAC,MAAM;YACHzQ,CAAC,CAAC2E,WAAW,GAAK3E,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAI1C,CAAC,CAACuE,UAAU,GAAI,CAAC,CAAC;YAC/EmM,cAAc,GAAK1Q,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAI+N,cAAc,GAAI,CAAC,CAAC;UACtF;QACJ;MACJ;IACJ,CAAC,MAAM;MACH,IAAIf,UAAU,GAAG1P,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGzC,CAAC,CAACsE,UAAU,EAAE;QACpDtE,CAAC,CAAC2E,WAAW,GAAG,CAAE+K,UAAU,GAAG1P,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAIzC,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACuE,UAAU;QACrFmM,cAAc,GAAG,CAAEhB,UAAU,GAAG1P,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAIzC,CAAC,CAACsE,UAAU,IAAImM,cAAc;MAC5F;IACJ;IAEA,IAAIzQ,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACxCzC,CAAC,CAAC2E,WAAW,GAAG,CAAC;MACjB+L,cAAc,GAAG,CAAC;IACtB;IAEA,IAAI1Q,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,IAAIb,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACzEzC,CAAC,CAAC2E,WAAW,GAAK3E,CAAC,CAACuE,UAAU,GAAGwF,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC,GAAI,CAAC,GAAMzC,CAAC,CAACuE,UAAU,GAAGvE,CAAC,CAACsE,UAAU,GAAI,CAAE;IACnH,CAAC,MAAM,IAAItE,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,IAAIb,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,EAAE;MACrE5B,CAAC,CAAC2E,WAAW,IAAI3E,CAAC,CAACuE,UAAU,GAAGwF,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC,GAAGzC,CAAC,CAACuE,UAAU;IACzF,CAAC,MAAM,IAAIvE,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;MACtCb,CAAC,CAAC2E,WAAW,GAAG,CAAC;MACjB3E,CAAC,CAAC2E,WAAW,IAAI3E,CAAC,CAACuE,UAAU,GAAGwF,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC;IAC1E;IAEA,IAAIzC,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;MAC9BmG,UAAU,GAAKoG,UAAU,GAAG1P,CAAC,CAACuE,UAAU,GAAI,CAAC,CAAC,GAAIvE,CAAC,CAAC2E,WAAW;IACnE,CAAC,MAAM;MACH2E,UAAU,GAAKoG,UAAU,GAAGe,cAAc,GAAI,CAAC,CAAC,GAAIC,cAAc;IACtE;IAEA,IAAI1Q,CAAC,CAACwG,OAAO,CAACtD,aAAa,KAAK,IAAI,EAAE;MAElC,IAAIlD,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIzC,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,EAAE;QACxE+O,WAAW,GAAG3Q,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAACH,EAAE,CAACkH,UAAU,CAAC;MACvE,CAAC,MAAM;QACHiB,WAAW,GAAG3Q,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAACH,EAAE,CAACkH,UAAU,GAAG1P,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC;MAChG;MAEA,IAAIzC,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,EAAE;QACxB,IAAIqO,WAAW,CAAC,CAAC,CAAC,EAAE;UAChBrH,UAAU,GAAG,CAACtJ,CAAC,CAACwE,WAAW,CAAC+I,KAAK,CAAC,CAAC,GAAGoD,WAAW,CAAC,CAAC,CAAC,CAACG,UAAU,GAAGH,WAAW,CAACpD,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QAC/F,CAAC,MAAM;UACHjE,UAAU,GAAI,CAAC;QACnB;MACJ,CAAC,MAAM;QACHA,UAAU,GAAGqH,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAACG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;MACpE;MAEA,IAAI9Q,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;QAC/B,IAAIb,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIzC,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,EAAE;UACxE+O,WAAW,GAAG3Q,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAACH,EAAE,CAACkH,UAAU,CAAC;QACvE,CAAC,MAAM;UACHiB,WAAW,GAAG3Q,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAACH,EAAE,CAACkH,UAAU,GAAG1P,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC;QACpG;QAEA,IAAIzC,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,EAAE;UACxB,IAAIqO,WAAW,CAAC,CAAC,CAAC,EAAE;YAChBrH,UAAU,GAAG,CAACtJ,CAAC,CAACwE,WAAW,CAAC+I,KAAK,CAAC,CAAC,GAAGoD,WAAW,CAAC,CAAC,CAAC,CAACG,UAAU,GAAGH,WAAW,CAACpD,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;UAC/F,CAAC,MAAM;YACHjE,UAAU,GAAI,CAAC;UACnB;QACJ,CAAC,MAAM;UACHA,UAAU,GAAGqH,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAACG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;QACpE;QAEAxH,UAAU,IAAI,CAACtJ,CAAC,CAAC8E,KAAK,CAACyI,KAAK,CAAC,CAAC,GAAGoD,WAAW,CAACI,UAAU,CAAC,CAAC,IAAI,CAAC;MAClE;IACJ;IAEA,OAAOzH,UAAU;EAErB,CAAC;EAED3J,KAAK,CAACgI,SAAS,CAACqJ,SAAS,GAAGrR,KAAK,CAACgI,SAAS,CAACsJ,cAAc,GAAG,UAASC,MAAM,EAAE;IAE1E,IAAIlR,CAAC,GAAG,IAAI;IAEZ,OAAOA,CAAC,CAACwG,OAAO,CAAC0K,MAAM,CAAC;EAE5B,CAAC;EAEDvR,KAAK,CAACgI,SAAS,CAACgH,mBAAmB,GAAG,YAAW;IAE7C,IAAI3O,CAAC,GAAG,IAAI;MACRqQ,UAAU,GAAG,CAAC;MACdC,OAAO,GAAG,CAAC;MACXa,OAAO,GAAG,EAAE;MACZC,GAAG;IAEP,IAAIpR,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,EAAE;MAC9BwP,GAAG,GAAGpR,CAAC,CAACsE,UAAU;IACtB,CAAC,MAAM;MACH+L,UAAU,GAAGrQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG,CAAC,CAAC;MAC1C4N,OAAO,GAAGtQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG,CAAC,CAAC;MACvC0O,GAAG,GAAGpR,CAAC,CAACsE,UAAU,GAAG,CAAC;IAC1B;IAEA,OAAO+L,UAAU,GAAGe,GAAG,EAAE;MACrBD,OAAO,CAACE,IAAI,CAAChB,UAAU,CAAC;MACxBA,UAAU,GAAGC,OAAO,GAAGtQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc;MAC/C4N,OAAO,IAAItQ,CAAC,CAACwG,OAAO,CAAC9D,cAAc,IAAI1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGzC,CAAC,CAACwG,OAAO,CAAC9D,cAAc,GAAG1C,CAAC,CAACwG,OAAO,CAAC/D,YAAY;IACrH;IAEA,OAAO0O,OAAO;EAElB,CAAC;EAEDxR,KAAK,CAACgI,SAAS,CAAC2J,QAAQ,GAAG,YAAW;IAElC,OAAO,IAAI;EAEf,CAAC;EAED3R,KAAK,CAACgI,SAAS,CAAC4J,aAAa,GAAG,YAAW;IAEvC,IAAIvR,CAAC,GAAG,IAAI;MACRwR,eAAe;MAAEC,WAAW;MAAEC,YAAY;IAE9CA,YAAY,GAAG1R,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,GAAGb,CAAC,CAACuE,UAAU,GAAGwF,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;IAExG,IAAIzC,CAAC,CAACwG,OAAO,CAAC3D,YAAY,KAAK,IAAI,EAAE;MACjC7C,CAAC,CAACwE,WAAW,CAACqD,IAAI,CAAC,cAAc,CAAC,CAACiB,IAAI,CAAC,UAASZ,KAAK,EAAE3F,KAAK,EAAE;QAC3D,IAAIA,KAAK,CAACuO,UAAU,GAAGY,YAAY,GAAIhS,CAAC,CAAC6C,KAAK,CAAC,CAACwO,UAAU,CAAC,CAAC,GAAG,CAAE,GAAI/Q,CAAC,CAAC4E,SAAS,GAAG,CAAC,CAAE,EAAE;UACpF6M,WAAW,GAAGlP,KAAK;UACnB,OAAO,KAAK;QAChB;MACJ,CAAC,CAAC;MAEFiP,eAAe,GAAGzH,IAAI,CAAC4H,GAAG,CAACjS,CAAC,CAAC+R,WAAW,CAAC,CAAC3J,IAAI,CAAC,kBAAkB,CAAC,GAAG9H,CAAC,CAAC6D,YAAY,CAAC,IAAI,CAAC;MAEzF,OAAO2N,eAAe;IAE1B,CAAC,MAAM;MACH,OAAOxR,CAAC,CAACwG,OAAO,CAAC9D,cAAc;IACnC;EAEJ,CAAC;EAED/C,KAAK,CAACgI,SAAS,CAACiK,IAAI,GAAGjS,KAAK,CAACgI,SAAS,CAACkK,SAAS,GAAG,UAAStP,KAAK,EAAEwL,WAAW,EAAE;IAE5E,IAAI/N,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACiH,WAAW,CAAC;MACVV,IAAI,EAAE;QACFgI,OAAO,EAAE,OAAO;QAChBrG,KAAK,EAAE4J,QAAQ,CAACvP,KAAK;MACzB;IACJ,CAAC,EAAEwL,WAAW,CAAC;EAEnB,CAAC;EAEDpO,KAAK,CAACgI,SAAS,CAACD,IAAI,GAAG,UAASqK,QAAQ,EAAE;IAEtC,IAAI/R,CAAC,GAAG,IAAI;IAEZ,IAAI,CAACN,CAAC,CAACM,CAAC,CAACgG,OAAO,CAAC,CAACgM,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAE7CtS,CAAC,CAACM,CAAC,CAACgG,OAAO,CAAC,CAACiF,QAAQ,CAAC,mBAAmB,CAAC;MAE1CjL,CAAC,CAACiM,SAAS,CAAC,CAAC;MACbjM,CAAC,CAAC0L,QAAQ,CAAC,CAAC;MACZ1L,CAAC,CAACiS,QAAQ,CAAC,CAAC;MACZjS,CAAC,CAACkS,SAAS,CAAC,CAAC;MACblS,CAAC,CAACmS,UAAU,CAAC,CAAC;MACdnS,CAAC,CAACoS,gBAAgB,CAAC,CAAC;MACpBpS,CAAC,CAACqS,YAAY,CAAC,CAAC;MAChBrS,CAAC,CAAC+L,UAAU,CAAC,CAAC;MACd/L,CAAC,CAAC+M,eAAe,CAAC,IAAI,CAAC;MACvB/M,CAAC,CAACgQ,YAAY,CAAC,CAAC;IAEpB;IAEA,IAAI+B,QAAQ,EAAE;MACV/R,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,MAAM,EAAE,CAAC7N,CAAC,CAAC,CAAC;IAClC;IAEA,IAAIA,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;MAClCH,CAAC,CAACsS,OAAO,CAAC,CAAC;IACf;IAEA,IAAKtS,CAAC,CAACwG,OAAO,CAAC7F,QAAQ,EAAG;MAEtBX,CAAC,CAAC4F,MAAM,GAAG,KAAK;MAChB5F,CAAC,CAAC6G,QAAQ,CAAC,CAAC;IAEhB;EAEJ,CAAC;EAEDlH,KAAK,CAACgI,SAAS,CAAC2K,OAAO,GAAG,YAAW;IACjC,IAAItS,CAAC,GAAG,IAAI;MACJuS,YAAY,GAAGxI,IAAI,CAACC,IAAI,CAAChK,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC;MAC/D+P,iBAAiB,GAAGxS,CAAC,CAAC2O,mBAAmB,CAAC,CAAC,CAACoB,MAAM,CAAC,UAAS0C,GAAG,EAAE;QAC7D,OAAQA,GAAG,IAAI,CAAC,IAAMA,GAAG,GAAGzS,CAAC,CAACsE,UAAW;MAC7C,CAAC,CAAC;IAEVtE,CAAC,CAACyE,OAAO,CAAC4G,GAAG,CAACrL,CAAC,CAACwE,WAAW,CAACqD,IAAI,CAAC,eAAe,CAAC,CAAC,CAACC,IAAI,CAAC;MACpD,aAAa,EAAE,MAAM;MACrB,UAAU,EAAE;IAChB,CAAC,CAAC,CAACD,IAAI,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAC;MACrC,UAAU,EAAE;IAChB,CAAC,CAAC;IAEF,IAAI9H,CAAC,CAAC+D,KAAK,KAAK,IAAI,EAAE;MAClB/D,CAAC,CAACyE,OAAO,CAAC+F,GAAG,CAACxK,CAAC,CAACwE,WAAW,CAACqD,IAAI,CAAC,eAAe,CAAC,CAAC,CAACiB,IAAI,CAAC,UAAS5H,CAAC,EAAE;QAChE,IAAIwR,iBAAiB,GAAGF,iBAAiB,CAACG,OAAO,CAACzR,CAAC,CAAC;QAEpDxB,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC;UACT,MAAM,EAAE,UAAU;UAClB,IAAI,EAAE,aAAa,GAAG9H,CAAC,CAACH,WAAW,GAAGqB,CAAC;UACvC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;QAEF,IAAIwR,iBAAiB,KAAK,CAAC,CAAC,EAAE;UAC3B,IAAIE,iBAAiB,GAAG,qBAAqB,GAAG5S,CAAC,CAACH,WAAW,GAAG6S,iBAAiB;UACjF,IAAIhT,CAAC,CAAC,GAAG,GAAGkT,iBAAiB,CAAC,CAACvK,MAAM,EAAE;YACrC3I,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC;cACT,kBAAkB,EAAE8K;YACxB,CAAC,CAAC;UACJ;QACH;MACJ,CAAC,CAAC;MAEF5S,CAAC,CAAC+D,KAAK,CAAC+D,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC,CAACiB,IAAI,CAAC,UAAS5H,CAAC,EAAE;QACxD,IAAI2R,gBAAgB,GAAGL,iBAAiB,CAACtR,CAAC,CAAC;QAE3CxB,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC;UACT,MAAM,EAAE;QACZ,CAAC,CAAC;QAEFpI,CAAC,CAAC,IAAI,CAAC,CAACmI,IAAI,CAAC,QAAQ,CAAC,CAAC4D,KAAK,CAAC,CAAC,CAAC3D,IAAI,CAAC;UAChC,MAAM,EAAE,KAAK;UACb,IAAI,EAAE,qBAAqB,GAAG9H,CAAC,CAACH,WAAW,GAAGqB,CAAC;UAC/C,eAAe,EAAE,aAAa,GAAGlB,CAAC,CAACH,WAAW,GAAGgT,gBAAgB;UACjE,YAAY,EAAG3R,CAAC,GAAG,CAAC,GAAI,MAAM,GAAGqR,YAAY;UAC7C,eAAe,EAAE,IAAI;UACrB,UAAU,EAAE;QAChB,CAAC,CAAC;MAEN,CAAC,CAAC,CAAC/J,EAAE,CAACxI,CAAC,CAAC6D,YAAY,CAAC,CAACgE,IAAI,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC;QACtC,eAAe,EAAE,MAAM;QACvB,UAAU,EAAE;MAChB,CAAC,CAAC,CAACgL,GAAG,CAAC,CAAC;IACZ;IAEA,KAAK,IAAI5R,CAAC,GAAClB,CAAC,CAAC6D,YAAY,EAAEuN,GAAG,GAAClQ,CAAC,GAAClB,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAEvB,CAAC,GAAGkQ,GAAG,EAAElQ,CAAC,EAAE,EAAE;MACrE,IAAIlB,CAAC,CAACwG,OAAO,CAAC7E,aAAa,EAAE;QAC3B3B,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACtH,CAAC,CAAC,CAAC4G,IAAI,CAAC;UAAC,UAAU,EAAE;QAAG,CAAC,CAAC;MACzC,CAAC,MAAM;QACL9H,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACtH,CAAC,CAAC,CAACiK,UAAU,CAAC,UAAU,CAAC;MACxC;IACF;IAEAnL,CAAC,CAAC4H,WAAW,CAAC,CAAC;EAEnB,CAAC;EAEDjI,KAAK,CAACgI,SAAS,CAACoL,eAAe,GAAG,YAAW;IAEzC,IAAI/S,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACjG,MAAM,KAAK,IAAI,IAAIP,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACpEzC,CAAC,CAACoE,UAAU,CACR0K,GAAG,CAAC,aAAa,CAAC,CAClBmB,EAAE,CAAC,aAAa,EAAE;QACd1B,OAAO,EAAE;MACd,CAAC,EAAEvO,CAAC,CAACiH,WAAW,CAAC;MACpBjH,CAAC,CAACmE,UAAU,CACR2K,GAAG,CAAC,aAAa,CAAC,CAClBmB,EAAE,CAAC,aAAa,EAAE;QACd1B,OAAO,EAAE;MACd,CAAC,EAAEvO,CAAC,CAACiH,WAAW,CAAC;MAEpB,IAAIjH,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QAClCH,CAAC,CAACoE,UAAU,CAAC6L,EAAE,CAAC,eAAe,EAAEjQ,CAAC,CAACuH,UAAU,CAAC;QAC9CvH,CAAC,CAACmE,UAAU,CAAC8L,EAAE,CAAC,eAAe,EAAEjQ,CAAC,CAACuH,UAAU,CAAC;MAClD;IACJ;EAEJ,CAAC;EAED5H,KAAK,CAACgI,SAAS,CAACqL,aAAa,GAAG,YAAW;IAEvC,IAAIhT,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACpF,IAAI,KAAK,IAAI,IAAIpB,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAClE/C,CAAC,CAAC,IAAI,EAAEM,CAAC,CAAC+D,KAAK,CAAC,CAACkM,EAAE,CAAC,aAAa,EAAE;QAC/B1B,OAAO,EAAE;MACb,CAAC,EAAEvO,CAAC,CAACiH,WAAW,CAAC;MAEjB,IAAIjH,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QAClCH,CAAC,CAAC+D,KAAK,CAACkM,EAAE,CAAC,eAAe,EAAEjQ,CAAC,CAACuH,UAAU,CAAC;MAC7C;IACJ;IAEA,IAAIvH,CAAC,CAACwG,OAAO,CAACpF,IAAI,KAAK,IAAI,IAAIpB,CAAC,CAACwG,OAAO,CAACtE,gBAAgB,KAAK,IAAI,IAAIlC,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAEzG/C,CAAC,CAAC,IAAI,EAAEM,CAAC,CAAC+D,KAAK,CAAC,CACXkM,EAAE,CAAC,kBAAkB,EAAEvQ,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,IAAI,CAAC,CAAC,CACrDiQ,EAAE,CAAC,kBAAkB,EAAEvQ,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,KAAK,CAAC,CAAC;IAE/D;EAEJ,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAACsL,eAAe,GAAG,YAAW;IAEzC,IAAIjT,CAAC,GAAG,IAAI;IAEZ,IAAKA,CAAC,CAACwG,OAAO,CAACxE,YAAY,EAAG;MAE1BhC,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,kBAAkB,EAAEvQ,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,IAAI,CAAC,CAAC;MAC7DA,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,kBAAkB,EAAEvQ,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAAC+O,SAAS,EAAE/O,CAAC,EAAE,KAAK,CAAC,CAAC;IAElE;EAEJ,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAACyK,gBAAgB,GAAG,YAAW;IAE1C,IAAIpS,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC+S,eAAe,CAAC,CAAC;IAEnB/S,CAAC,CAACgT,aAAa,CAAC,CAAC;IACjBhT,CAAC,CAACiT,eAAe,CAAC,CAAC;IAEnBjT,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,kCAAkC,EAAE;MAC3CiD,MAAM,EAAE;IACZ,CAAC,EAAElT,CAAC,CAACqH,YAAY,CAAC;IAClBrH,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,iCAAiC,EAAE;MAC1CiD,MAAM,EAAE;IACZ,CAAC,EAAElT,CAAC,CAACqH,YAAY,CAAC;IAClBrH,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,8BAA8B,EAAE;MACvCiD,MAAM,EAAE;IACZ,CAAC,EAAElT,CAAC,CAACqH,YAAY,CAAC;IAClBrH,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,oCAAoC,EAAE;MAC7CiD,MAAM,EAAE;IACZ,CAAC,EAAElT,CAAC,CAACqH,YAAY,CAAC;IAElBrH,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,aAAa,EAAEjQ,CAAC,CAACkH,YAAY,CAAC;IAEzCxH,CAAC,CAACgH,QAAQ,CAAC,CAACuJ,EAAE,CAACjQ,CAAC,CAACoG,gBAAgB,EAAE1G,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACgP,UAAU,EAAEhP,CAAC,CAAC,CAAC;IAE5D,IAAIA,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;MAClCH,CAAC,CAAC8E,KAAK,CAACmL,EAAE,CAAC,eAAe,EAAEjQ,CAAC,CAACuH,UAAU,CAAC;IAC7C;IAEA,IAAIvH,CAAC,CAACwG,OAAO,CAAC9E,aAAa,KAAK,IAAI,EAAE;MAClChC,CAAC,CAACM,CAAC,CAACwE,WAAW,CAAC,CAACmE,QAAQ,CAAC,CAAC,CAACsH,EAAE,CAAC,aAAa,EAAEjQ,CAAC,CAACmH,aAAa,CAAC;IAClE;IAEAzH,CAAC,CAACE,MAAM,CAAC,CAACqQ,EAAE,CAAC,gCAAgC,GAAGjQ,CAAC,CAACH,WAAW,EAAEH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACkP,iBAAiB,EAAElP,CAAC,CAAC,CAAC;IAE/FN,CAAC,CAACE,MAAM,CAAC,CAACqQ,EAAE,CAAC,qBAAqB,GAAGjQ,CAAC,CAACH,WAAW,EAAEH,CAAC,CAACoH,KAAK,CAAC9G,CAAC,CAACmP,MAAM,EAAEnP,CAAC,CAAC,CAAC;IAEzEN,CAAC,CAAC,mBAAmB,EAAEM,CAAC,CAACwE,WAAW,CAAC,CAACyL,EAAE,CAAC,WAAW,EAAEjQ,CAAC,CAACqO,cAAc,CAAC;IAEvE3O,CAAC,CAACE,MAAM,CAAC,CAACqQ,EAAE,CAAC,mBAAmB,GAAGjQ,CAAC,CAACH,WAAW,EAAEG,CAAC,CAACoH,WAAW,CAAC;IAChE1H,CAAC,CAACM,CAAC,CAACoH,WAAW,CAAC;EAEpB,CAAC;EAEDzH,KAAK,CAACgI,SAAS,CAACwL,MAAM,GAAG,YAAW;IAEhC,IAAInT,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACjG,MAAM,KAAK,IAAI,IAAIP,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAEpEzC,CAAC,CAACoE,UAAU,CAACgP,IAAI,CAAC,CAAC;MACnBpT,CAAC,CAACmE,UAAU,CAACiP,IAAI,CAAC,CAAC;IAEvB;IAEA,IAAIpT,CAAC,CAACwG,OAAO,CAACpF,IAAI,KAAK,IAAI,IAAIpB,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAElEzC,CAAC,CAAC+D,KAAK,CAACqP,IAAI,CAAC,CAAC;IAElB;EAEJ,CAAC;EAEDzT,KAAK,CAACgI,SAAS,CAACJ,UAAU,GAAG,UAASuG,KAAK,EAAE;IAEzC,IAAI9N,CAAC,GAAG,IAAI;IACX;IACD,IAAG,CAAC8N,KAAK,CAACrD,MAAM,CAAC4I,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC,EAAE;MACrD,IAAIxF,KAAK,CAACyF,OAAO,KAAK,EAAE,IAAIvT,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QAC1DH,CAAC,CAACiH,WAAW,CAAC;UACVV,IAAI,EAAE;YACFgI,OAAO,EAAEvO,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,GAAG,MAAM,GAAI;UAChD;QACJ,CAAC,CAAC;MACN,CAAC,MAAM,IAAIwL,KAAK,CAACyF,OAAO,KAAK,EAAE,IAAIvT,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QACjEH,CAAC,CAACiH,WAAW,CAAC;UACVV,IAAI,EAAE;YACFgI,OAAO,EAAEvO,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,GAAG,UAAU,GAAG;UACnD;QACJ,CAAC,CAAC;MACN;IACJ;EAEJ,CAAC;EAED3C,KAAK,CAACgI,SAAS,CAAC7F,QAAQ,GAAG,YAAW;IAElC,IAAI9B,CAAC,GAAG,IAAI;MACRwT,SAAS;MAAEC,UAAU;MAAEC,UAAU;MAAEC,QAAQ;IAE/C,SAASC,UAAUA,CAACC,WAAW,EAAE;MAE7BnU,CAAC,CAAC,gBAAgB,EAAEmU,WAAW,CAAC,CAAC/K,IAAI,CAAC,YAAW;QAE7C,IAAIgL,KAAK,GAAGpU,CAAC,CAAC,IAAI,CAAC;UACfqU,WAAW,GAAGrU,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC,WAAW,CAAC;UACvCkM,WAAW,GAAGtU,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC,aAAa,CAAC;UACzCmM,UAAU,GAAIvU,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC,YAAY,CAAC,IAAI9H,CAAC,CAACgG,OAAO,CAAC8B,IAAI,CAAC,YAAY,CAAC;UACxEoM,WAAW,GAAGxN,QAAQ,CAACgG,aAAa,CAAC,KAAK,CAAC;QAE/CwH,WAAW,CAACC,MAAM,GAAG,YAAW;UAE5BL,KAAK,CACA3K,OAAO,CAAC;YAAEwG,OAAO,EAAE;UAAE,CAAC,EAAE,GAAG,EAAE,YAAW;YAErC,IAAIqE,WAAW,EAAE;cACbF,KAAK,CACAhM,IAAI,CAAC,QAAQ,EAAEkM,WAAY,CAAC;cAEjC,IAAIC,UAAU,EAAE;gBACZH,KAAK,CACAhM,IAAI,CAAC,OAAO,EAAEmM,UAAW,CAAC;cACnC;YACJ;YAEAH,KAAK,CACAhM,IAAI,CAAC,KAAK,EAAEiM,WAAW,CAAC,CACxB5K,OAAO,CAAC;cAAEwG,OAAO,EAAE;YAAE,CAAC,EAAE,GAAG,EAAE,YAAW;cACrCmE,KAAK,CACA3I,UAAU,CAAC,kCAAkC,CAAC,CAC9CD,WAAW,CAAC,eAAe,CAAC;YACrC,CAAC,CAAC;YACNlL,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,YAAY,EAAE,CAAC7N,CAAC,EAAE8T,KAAK,EAAEC,WAAW,CAAC,CAAC;UAC5D,CAAC,CAAC;QAEV,CAAC;QAEDG,WAAW,CAACE,OAAO,GAAG,YAAW;UAE7BN,KAAK,CACA3I,UAAU,CAAE,WAAY,CAAC,CACzBD,WAAW,CAAE,eAAgB,CAAC,CAC9BD,QAAQ,CAAE,sBAAuB,CAAC;UAEvCjL,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,eAAe,EAAE,CAAE7N,CAAC,EAAE8T,KAAK,EAAEC,WAAW,CAAE,CAAC;QAEjE,CAAC;QAEDG,WAAW,CAACG,GAAG,GAAGN,WAAW;MAEjC,CAAC,CAAC;IAEN;IAEA,IAAI/T,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;MAC/B,IAAIb,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,EAAE;QAC7B8R,UAAU,GAAG1T,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9DkR,QAAQ,GAAGD,UAAU,GAAG1T,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC;MACtD,CAAC,MAAM;QACHiR,UAAU,GAAG3J,IAAI,CAACqH,GAAG,CAAC,CAAC,EAAEpR,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3EkR,QAAQ,GAAG,CAAC,IAAI3T,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGzC,CAAC,CAAC6D,YAAY;MACpE;IACJ,CAAC,MAAM;MACH6P,UAAU,GAAG1T,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,GAAG5B,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGzC,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAAC6D,YAAY;MAC1F8P,QAAQ,GAAG5J,IAAI,CAACC,IAAI,CAAC0J,UAAU,GAAG1T,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC;MACzD,IAAIzC,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,IAAI,EAAE;QACzB,IAAIiS,UAAU,GAAG,CAAC,EAAEA,UAAU,EAAE;QAChC,IAAIC,QAAQ,IAAI3T,CAAC,CAACsE,UAAU,EAAEqP,QAAQ,EAAE;MAC5C;IACJ;IAEAH,SAAS,GAAGxT,CAAC,CAACgG,OAAO,CAAC6B,IAAI,CAAC,cAAc,CAAC,CAACyM,KAAK,CAACZ,UAAU,EAAEC,QAAQ,CAAC;IAEtE,IAAI3T,CAAC,CAACwG,OAAO,CAAC1E,QAAQ,KAAK,aAAa,EAAE;MACtC,IAAIyS,SAAS,GAAGb,UAAU,GAAG,CAAC;QAC1Bc,SAAS,GAAGb,QAAQ;QACpBlP,OAAO,GAAGzE,CAAC,CAACgG,OAAO,CAAC6B,IAAI,CAAC,cAAc,CAAC;MAE5C,KAAK,IAAI3G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,CAAC,CAACwG,OAAO,CAAC9D,cAAc,EAAExB,CAAC,EAAE,EAAE;QAC/C,IAAIqT,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGvU,CAAC,CAACsE,UAAU,GAAG,CAAC;QAC/CkP,SAAS,GAAGA,SAAS,CAACnI,GAAG,CAAC5G,OAAO,CAAC+D,EAAE,CAAC+L,SAAS,CAAC,CAAC;QAChDf,SAAS,GAAGA,SAAS,CAACnI,GAAG,CAAC5G,OAAO,CAAC+D,EAAE,CAACgM,SAAS,CAAC,CAAC;QAChDD,SAAS,EAAE;QACXC,SAAS,EAAE;MACf;IACJ;IAEAZ,UAAU,CAACJ,SAAS,CAAC;IAErB,IAAIxT,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACxCgR,UAAU,GAAGzT,CAAC,CAACgG,OAAO,CAAC6B,IAAI,CAAC,cAAc,CAAC;MAC3C+L,UAAU,CAACH,UAAU,CAAC;IAC1B,CAAC,MACD,IAAIzT,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACzDgR,UAAU,GAAGzT,CAAC,CAACgG,OAAO,CAAC6B,IAAI,CAAC,eAAe,CAAC,CAACyM,KAAK,CAAC,CAAC,EAAEtU,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC;MAC7EmR,UAAU,CAACH,UAAU,CAAC;IAC1B,CAAC,MAAM,IAAIzT,CAAC,CAAC6D,YAAY,KAAK,CAAC,EAAE;MAC7B4P,UAAU,GAAGzT,CAAC,CAACgG,OAAO,CAAC6B,IAAI,CAAC,eAAe,CAAC,CAACyM,KAAK,CAACtU,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC,CAAC;MAC/EmR,UAAU,CAACH,UAAU,CAAC;IAC1B;EAEJ,CAAC;EAED9T,KAAK,CAACgI,SAAS,CAACwK,UAAU,GAAG,YAAW;IAEpC,IAAInS,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACoH,WAAW,CAAC,CAAC;IAEfpH,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAAC;MACd0F,OAAO,EAAE;IACb,CAAC,CAAC;IAEF3P,CAAC,CAACgG,OAAO,CAACkF,WAAW,CAAC,eAAe,CAAC;IAEtClL,CAAC,CAACmT,MAAM,CAAC,CAAC;IAEV,IAAInT,CAAC,CAACwG,OAAO,CAAC1E,QAAQ,KAAK,aAAa,EAAE;MACtC9B,CAAC,CAACyU,mBAAmB,CAAC,CAAC;IAC3B;EAEJ,CAAC;EAED9U,KAAK,CAACgI,SAAS,CAAC+M,IAAI,GAAG/U,KAAK,CAACgI,SAAS,CAACgN,SAAS,GAAG,YAAW;IAE1D,IAAI3U,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACiH,WAAW,CAAC;MACVV,IAAI,EAAE;QACFgI,OAAO,EAAE;MACb;IACJ,CAAC,CAAC;EAEN,CAAC;EAED5O,KAAK,CAACgI,SAAS,CAACuH,iBAAiB,GAAG,YAAW;IAE3C,IAAIlP,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC+M,eAAe,CAAC,CAAC;IACnB/M,CAAC,CAACoH,WAAW,CAAC,CAAC;EAEnB,CAAC;EAEDzH,KAAK,CAACgI,SAAS,CAACiN,KAAK,GAAGjV,KAAK,CAACgI,SAAS,CAACkN,UAAU,GAAG,YAAW;IAE5D,IAAI7U,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC+G,aAAa,CAAC,CAAC;IACjB/G,CAAC,CAAC4F,MAAM,GAAG,IAAI;EAEnB,CAAC;EAEDjG,KAAK,CAACgI,SAAS,CAACmN,IAAI,GAAGnV,KAAK,CAACgI,SAAS,CAACoN,SAAS,GAAG,YAAW;IAE1D,IAAI/U,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC6G,QAAQ,CAAC,CAAC;IACZ7G,CAAC,CAACwG,OAAO,CAAC7F,QAAQ,GAAG,IAAI;IACzBX,CAAC,CAAC4F,MAAM,GAAG,KAAK;IAChB5F,CAAC,CAACyF,QAAQ,GAAG,KAAK;IAClBzF,CAAC,CAAC0F,WAAW,GAAG,KAAK;EAEzB,CAAC;EAED/F,KAAK,CAACgI,SAAS,CAACqN,SAAS,GAAG,UAAS9M,KAAK,EAAE;IAExC,IAAIlI,CAAC,GAAG,IAAI;IAEZ,IAAI,CAACA,CAAC,CAACiF,SAAS,EAAG;MAEfjF,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,aAAa,EAAE,CAAC7N,CAAC,EAAEkI,KAAK,CAAC,CAAC;MAE5ClI,CAAC,CAACwD,SAAS,GAAG,KAAK;MAEnB,IAAIxD,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;QACvCzC,CAAC,CAACoH,WAAW,CAAC,CAAC;MACnB;MAEApH,CAAC,CAAC4E,SAAS,GAAG,IAAI;MAElB,IAAK5E,CAAC,CAACwG,OAAO,CAAC7F,QAAQ,EAAG;QACtBX,CAAC,CAAC6G,QAAQ,CAAC,CAAC;MAChB;MAEA,IAAI7G,CAAC,CAACwG,OAAO,CAACrG,aAAa,KAAK,IAAI,EAAE;QAClCH,CAAC,CAACsS,OAAO,CAAC,CAAC;QAEX,IAAItS,CAAC,CAACwG,OAAO,CAAC7E,aAAa,EAAE;UACzB,IAAIsT,aAAa,GAAGvV,CAAC,CAACM,CAAC,CAACyE,OAAO,CAACmI,GAAG,CAAC5M,CAAC,CAAC6D,YAAY,CAAC,CAAC;UACpDoR,aAAa,CAACnN,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAACoN,KAAK,CAAC,CAAC;QAC7C;MACJ;IAEJ;EAEJ,CAAC;EAEDvV,KAAK,CAACgI,SAAS,CAACwN,IAAI,GAAGxV,KAAK,CAACgI,SAAS,CAACyN,SAAS,GAAG,YAAW;IAE1D,IAAIpV,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACiH,WAAW,CAAC;MACVV,IAAI,EAAE;QACFgI,OAAO,EAAE;MACb;IACJ,CAAC,CAAC;EAEN,CAAC;EAED5O,KAAK,CAACgI,SAAS,CAAC0G,cAAc,GAAG,UAASP,KAAK,EAAE;IAE7CA,KAAK,CAACO,cAAc,CAAC,CAAC;EAE1B,CAAC;EAED1O,KAAK,CAACgI,SAAS,CAAC8M,mBAAmB,GAAG,UAAUY,QAAQ,EAAG;IAEvDA,QAAQ,GAAGA,QAAQ,IAAI,CAAC;IAExB,IAAIrV,CAAC,GAAG,IAAI;MACRsV,WAAW,GAAG5V,CAAC,CAAE,gBAAgB,EAAEM,CAAC,CAACgG,OAAQ,CAAC;MAC9C8N,KAAK;MACLC,WAAW;MACXC,WAAW;MACXC,UAAU;MACVC,WAAW;IAEf,IAAKoB,WAAW,CAACjN,MAAM,EAAG;MAEtByL,KAAK,GAAGwB,WAAW,CAAC7J,KAAK,CAAC,CAAC;MAC3BsI,WAAW,GAAGD,KAAK,CAAChM,IAAI,CAAC,WAAW,CAAC;MACrCkM,WAAW,GAAGF,KAAK,CAAChM,IAAI,CAAC,aAAa,CAAC;MACvCmM,UAAU,GAAIH,KAAK,CAAChM,IAAI,CAAC,YAAY,CAAC,IAAI9H,CAAC,CAACgG,OAAO,CAAC8B,IAAI,CAAC,YAAY,CAAC;MACtEoM,WAAW,GAAGxN,QAAQ,CAACgG,aAAa,CAAC,KAAK,CAAC;MAE3CwH,WAAW,CAACC,MAAM,GAAG,YAAW;QAE5B,IAAIH,WAAW,EAAE;UACbF,KAAK,CACAhM,IAAI,CAAC,QAAQ,EAAEkM,WAAY,CAAC;UAEjC,IAAIC,UAAU,EAAE;YACZH,KAAK,CACAhM,IAAI,CAAC,OAAO,EAAEmM,UAAW,CAAC;UACnC;QACJ;QAEAH,KAAK,CACAhM,IAAI,CAAE,KAAK,EAAEiM,WAAY,CAAC,CAC1B5I,UAAU,CAAC,kCAAkC,CAAC,CAC9CD,WAAW,CAAC,eAAe,CAAC;QAEjC,IAAKlL,CAAC,CAACwG,OAAO,CAACpG,cAAc,KAAK,IAAI,EAAG;UACrCJ,CAAC,CAACoH,WAAW,CAAC,CAAC;QACnB;QAEApH,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,YAAY,EAAE,CAAE7N,CAAC,EAAE8T,KAAK,EAAEC,WAAW,CAAE,CAAC;QAC1D/T,CAAC,CAACyU,mBAAmB,CAAC,CAAC;MAE3B,CAAC;MAEDP,WAAW,CAACE,OAAO,GAAG,YAAW;QAE7B,IAAKiB,QAAQ,GAAG,CAAC,EAAG;UAEhB;AACpB;AACA;AACA;AACA;UACoBhL,UAAU,CAAE,YAAW;YACnBrK,CAAC,CAACyU,mBAAmB,CAAEY,QAAQ,GAAG,CAAE,CAAC;UACzC,CAAC,EAAE,GAAI,CAAC;QAEZ,CAAC,MAAM;UAEHvB,KAAK,CACA3I,UAAU,CAAE,WAAY,CAAC,CACzBD,WAAW,CAAE,eAAgB,CAAC,CAC9BD,QAAQ,CAAE,sBAAuB,CAAC;UAEvCjL,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,eAAe,EAAE,CAAE7N,CAAC,EAAE8T,KAAK,EAAEC,WAAW,CAAE,CAAC;UAE7D/T,CAAC,CAACyU,mBAAmB,CAAC,CAAC;QAE3B;MAEJ,CAAC;MAEDP,WAAW,CAACG,GAAG,GAAGN,WAAW;IAEjC,CAAC,MAAM;MAEH/T,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,iBAAiB,EAAE,CAAE7N,CAAC,CAAE,CAAC;IAE/C;EAEJ,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAACiG,OAAO,GAAG,UAAU2H,YAAY,EAAG;IAE/C,IAAIvV,CAAC,GAAG,IAAI;MAAE6D,YAAY;MAAE2R,gBAAgB;IAE5CA,gBAAgB,GAAGxV,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY;;IAExD;IACA;IACA,IAAI,CAACzC,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,IAAM5B,CAAC,CAAC6D,YAAY,GAAG2R,gBAAkB,EAAE;MAC9DxV,CAAC,CAAC6D,YAAY,GAAG2R,gBAAgB;IACrC;;IAEA;IACA,IAAKxV,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAG;MAC1CzC,CAAC,CAAC6D,YAAY,GAAG,CAAC;IAEtB;IAEAA,YAAY,GAAG7D,CAAC,CAAC6D,YAAY;IAE7B7D,CAAC,CAACuP,OAAO,CAAC,IAAI,CAAC;IAEf7P,CAAC,CAACwF,MAAM,CAAClF,CAAC,EAAEA,CAAC,CAACuD,QAAQ,EAAE;MAAEM,YAAY,EAAEA;IAAa,CAAC,CAAC;IAEvD7D,CAAC,CAAC0H,IAAI,CAAC,CAAC;IAER,IAAI,CAAC6N,YAAY,EAAG;MAEhBvV,CAAC,CAACiH,WAAW,CAAC;QACVV,IAAI,EAAE;UACFgI,OAAO,EAAE,OAAO;UAChBrG,KAAK,EAAErE;QACX;MACJ,CAAC,EAAE,KAAK,CAAC;IAEb;EAEJ,CAAC;EAEDlE,KAAK,CAACgI,SAAS,CAACF,mBAAmB,GAAG,YAAW;IAE7C,IAAIzH,CAAC,GAAG,IAAI;MAAEkN,UAAU;MAAEuI,iBAAiB;MAAEC,CAAC;MAC1CC,kBAAkB,GAAG3V,CAAC,CAACwG,OAAO,CAACpE,UAAU,IAAI,IAAI;IAErD,IAAK1C,CAAC,CAACkW,IAAI,CAACD,kBAAkB,CAAC,KAAK,OAAO,IAAIA,kBAAkB,CAACtN,MAAM,EAAG;MAEvErI,CAAC,CAACmC,SAAS,GAAGnC,CAAC,CAACwG,OAAO,CAACrE,SAAS,IAAI,QAAQ;MAE7C,KAAM+K,UAAU,IAAIyI,kBAAkB,EAAG;QAErCD,CAAC,GAAG1V,CAAC,CAACsF,WAAW,CAAC+C,MAAM,GAAC,CAAC;QAE1B,IAAIsN,kBAAkB,CAACjI,cAAc,CAACR,UAAU,CAAC,EAAE;UAC/CuI,iBAAiB,GAAGE,kBAAkB,CAACzI,UAAU,CAAC,CAACA,UAAU;;UAE7D;UACA;UACA,OAAOwI,CAAC,IAAI,CAAC,EAAG;YACZ,IAAI1V,CAAC,CAACsF,WAAW,CAACoQ,CAAC,CAAC,IAAI1V,CAAC,CAACsF,WAAW,CAACoQ,CAAC,CAAC,KAAKD,iBAAiB,EAAG;cAC7DzV,CAAC,CAACsF,WAAW,CAACuQ,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC;YAC7B;YACAA,CAAC,EAAE;UACP;UAEA1V,CAAC,CAACsF,WAAW,CAAC+L,IAAI,CAACoE,iBAAiB,CAAC;UACrCzV,CAAC,CAACuF,kBAAkB,CAACkQ,iBAAiB,CAAC,GAAGE,kBAAkB,CAACzI,UAAU,CAAC,CAACnN,QAAQ;QAErF;MAEJ;MAEAC,CAAC,CAACsF,WAAW,CAACwQ,IAAI,CAAC,UAAS5J,CAAC,EAAEC,CAAC,EAAE;QAC9B,OAASnM,CAAC,CAACwG,OAAO,CAACzE,WAAW,GAAKmK,CAAC,GAACC,CAAC,GAAGA,CAAC,GAACD,CAAC;MAChD,CAAC,CAAC;IAEN;EAEJ,CAAC;EAEDvM,KAAK,CAACgI,SAAS,CAACoB,MAAM,GAAG,YAAW;IAEhC,IAAI/I,CAAC,GAAG,IAAI;IAEZA,CAAC,CAACyE,OAAO,GACLzE,CAAC,CAACwE,WAAW,CACRmE,QAAQ,CAAC3I,CAAC,CAACwG,OAAO,CAACjE,KAAK,CAAC,CACzB0I,QAAQ,CAAC,aAAa,CAAC;IAEhCjL,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACyE,OAAO,CAAC4D,MAAM;IAE/B,IAAIrI,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAAC6D,YAAY,KAAK,CAAC,EAAE;MACxD7D,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC9D,cAAc;IAC9D;IAEA,IAAI1C,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MACxCzC,CAAC,CAAC6D,YAAY,GAAG,CAAC;IACtB;IAEA7D,CAAC,CAACyH,mBAAmB,CAAC,CAAC;IAEvBzH,CAAC,CAACiS,QAAQ,CAAC,CAAC;IACZjS,CAAC,CAAC8L,aAAa,CAAC,CAAC;IACjB9L,CAAC,CAACgL,WAAW,CAAC,CAAC;IACfhL,CAAC,CAACqS,YAAY,CAAC,CAAC;IAChBrS,CAAC,CAAC+S,eAAe,CAAC,CAAC;IACnB/S,CAAC,CAACsL,SAAS,CAAC,CAAC;IACbtL,CAAC,CAAC+L,UAAU,CAAC,CAAC;IACd/L,CAAC,CAACgT,aAAa,CAAC,CAAC;IACjBhT,CAAC,CAACiP,kBAAkB,CAAC,CAAC;IACtBjP,CAAC,CAACiT,eAAe,CAAC,CAAC;IAEnBjT,CAAC,CAAC+M,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC;IAE9B,IAAI/M,CAAC,CAACwG,OAAO,CAAC9E,aAAa,KAAK,IAAI,EAAE;MAClChC,CAAC,CAACM,CAAC,CAACwE,WAAW,CAAC,CAACmE,QAAQ,CAAC,CAAC,CAACsH,EAAE,CAAC,aAAa,EAAEjQ,CAAC,CAACmH,aAAa,CAAC;IAClE;IAEAnH,CAAC,CAACgM,eAAe,CAAC,OAAOhM,CAAC,CAAC6D,YAAY,KAAK,QAAQ,GAAG7D,CAAC,CAAC6D,YAAY,GAAG,CAAC,CAAC;IAE1E7D,CAAC,CAACoH,WAAW,CAAC,CAAC;IACfpH,CAAC,CAACgQ,YAAY,CAAC,CAAC;IAEhBhQ,CAAC,CAAC4F,MAAM,GAAG,CAAC5F,CAAC,CAACwG,OAAO,CAAC7F,QAAQ;IAC9BX,CAAC,CAAC6G,QAAQ,CAAC,CAAC;IAEZ7G,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,QAAQ,EAAE,CAAC7N,CAAC,CAAC,CAAC;EAEpC,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAACwH,MAAM,GAAG,YAAW;IAEhC,IAAInP,CAAC,GAAG,IAAI;IAEZ,IAAIN,CAAC,CAACE,MAAM,CAAC,CAAC2N,KAAK,CAAC,CAAC,KAAKvN,CAAC,CAACqG,WAAW,EAAE;MACrC0P,YAAY,CAAC/V,CAAC,CAACgW,WAAW,CAAC;MAC3BhW,CAAC,CAACgW,WAAW,GAAGpW,MAAM,CAACyK,UAAU,CAAC,YAAW;QACzCrK,CAAC,CAACqG,WAAW,GAAG3G,CAAC,CAACE,MAAM,CAAC,CAAC2N,KAAK,CAAC,CAAC;QACjCvN,CAAC,CAAC+M,eAAe,CAAC,CAAC;QACnB,IAAI,CAAC/M,CAAC,CAACiF,SAAS,EAAG;UAAEjF,CAAC,CAACoH,WAAW,CAAC,CAAC;QAAE;MAC1C,CAAC,EAAE,EAAE,CAAC;IACV;EACJ,CAAC;EAEDzH,KAAK,CAACgI,SAAS,CAACsO,WAAW,GAAGtW,KAAK,CAACgI,SAAS,CAACuO,WAAW,GAAG,UAAShO,KAAK,EAAEiO,YAAY,EAAEC,SAAS,EAAE;IAEjG,IAAIpW,CAAC,GAAG,IAAI;IAEZ,IAAI,OAAOkI,KAAM,KAAK,SAAS,EAAE;MAC7BiO,YAAY,GAAGjO,KAAK;MACpBA,KAAK,GAAGiO,YAAY,KAAK,IAAI,GAAG,CAAC,GAAGnW,CAAC,CAACsE,UAAU,GAAG,CAAC;IACxD,CAAC,MAAM;MACH4D,KAAK,GAAGiO,YAAY,KAAK,IAAI,GAAG,EAAEjO,KAAK,GAAGA,KAAK;IACnD;IAEA,IAAIlI,CAAC,CAACsE,UAAU,GAAG,CAAC,IAAI4D,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGlI,CAAC,CAACsE,UAAU,GAAG,CAAC,EAAE;MAC3D,OAAO,KAAK;IAChB;IAEAtE,CAAC,CAACoI,MAAM,CAAC,CAAC;IAEV,IAAIgO,SAAS,KAAK,IAAI,EAAE;MACpBpW,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,CAAC,CAAC6G,MAAM,CAAC,CAAC;IACrC,CAAC,MAAM;MACHxP,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC,CAACiG,EAAE,CAACN,KAAK,CAAC,CAACsH,MAAM,CAAC,CAAC;IACjE;IAEAxP,CAAC,CAACyE,OAAO,GAAGzE,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC;IAEtDvC,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC,CAACqG,MAAM,CAAC,CAAC;IAEnD5I,CAAC,CAACwE,WAAW,CAACqE,MAAM,CAAC7I,CAAC,CAACyE,OAAO,CAAC;IAE/BzE,CAAC,CAACiG,YAAY,GAAGjG,CAAC,CAACyE,OAAO;IAE1BzE,CAAC,CAAC+I,MAAM,CAAC,CAAC;EAEd,CAAC;EAEDpJ,KAAK,CAACgI,SAAS,CAAC0O,MAAM,GAAG,UAASC,QAAQ,EAAE;IAExC,IAAItW,CAAC,GAAG,IAAI;MACRuW,aAAa,GAAG,CAAC,CAAC;MAClBC,CAAC;MAAEC,CAAC;IAER,IAAIzW,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,EAAE;MACxBgU,QAAQ,GAAG,CAACA,QAAQ;IACxB;IACAE,CAAC,GAAGxW,CAAC,CAAC6F,YAAY,IAAI,MAAM,GAAGkE,IAAI,CAACC,IAAI,CAACsM,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK;IACjEG,CAAC,GAAGzW,CAAC,CAAC6F,YAAY,IAAI,KAAK,GAAGkE,IAAI,CAACC,IAAI,CAACsM,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK;IAEhEC,aAAa,CAACvW,CAAC,CAAC6F,YAAY,CAAC,GAAGyQ,QAAQ;IAExC,IAAItW,CAAC,CAACgF,iBAAiB,KAAK,KAAK,EAAE;MAC/BhF,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACsM,aAAa,CAAC;IACpC,CAAC,MAAM;MACHA,aAAa,GAAG,CAAC,CAAC;MAClB,IAAIvW,CAAC,CAACwF,cAAc,KAAK,KAAK,EAAE;QAC5B+Q,aAAa,CAACvW,CAAC,CAACoF,QAAQ,CAAC,GAAG,YAAY,GAAGoR,CAAC,GAAG,IAAI,GAAGC,CAAC,GAAG,GAAG;QAC7DzW,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACsM,aAAa,CAAC;MACpC,CAAC,MAAM;QACHA,aAAa,CAACvW,CAAC,CAACoF,QAAQ,CAAC,GAAG,cAAc,GAAGoR,CAAC,GAAG,IAAI,GAAGC,CAAC,GAAG,QAAQ;QACpEzW,CAAC,CAACwE,WAAW,CAACyF,GAAG,CAACsM,aAAa,CAAC;MACpC;IACJ;EAEJ,CAAC;EAED5W,KAAK,CAACgI,SAAS,CAAC+O,aAAa,GAAG,YAAW;IAEvC,IAAI1W,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;MAC9B,IAAInD,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;QAC/Bb,CAAC,CAAC8E,KAAK,CAACmF,GAAG,CAAC;UACR0M,OAAO,EAAG,MAAM,GAAG3W,CAAC,CAACwG,OAAO,CAAC1F;QACjC,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACHd,CAAC,CAAC8E,KAAK,CAACsE,MAAM,CAACpJ,CAAC,CAACyE,OAAO,CAACgH,KAAK,CAAC,CAAC,CAACvC,WAAW,CAAC,IAAI,CAAC,GAAGlJ,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC;MAC5E,IAAIzC,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;QAC/Bb,CAAC,CAAC8E,KAAK,CAACmF,GAAG,CAAC;UACR0M,OAAO,EAAG3W,CAAC,CAACwG,OAAO,CAAC1F,aAAa,GAAG;QACxC,CAAC,CAAC;MACN;IACJ;IAEAd,CAAC,CAACgE,SAAS,GAAGhE,CAAC,CAAC8E,KAAK,CAACyI,KAAK,CAAC,CAAC;IAC7BvN,CAAC,CAACiE,UAAU,GAAGjE,CAAC,CAAC8E,KAAK,CAACsE,MAAM,CAAC,CAAC;IAG/B,IAAIpJ,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,IAAInD,CAAC,CAACwG,OAAO,CAACtD,aAAa,KAAK,KAAK,EAAE;MACnElD,CAAC,CAACuE,UAAU,GAAGwF,IAAI,CAACC,IAAI,CAAChK,CAAC,CAACgE,SAAS,GAAGhE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC;MAC9DzC,CAAC,CAACwE,WAAW,CAAC+I,KAAK,CAACxD,IAAI,CAACC,IAAI,CAAEhK,CAAC,CAACuE,UAAU,GAAGvE,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAACN,MAAO,CAAC,CAAC;IAElG,CAAC,MAAM,IAAIrI,CAAC,CAACwG,OAAO,CAACtD,aAAa,KAAK,IAAI,EAAE;MACzClD,CAAC,CAACwE,WAAW,CAAC+I,KAAK,CAAC,IAAI,GAAGvN,CAAC,CAACsE,UAAU,CAAC;IAC5C,CAAC,MAAM;MACHtE,CAAC,CAACuE,UAAU,GAAGwF,IAAI,CAACC,IAAI,CAAChK,CAAC,CAACgE,SAAS,CAAC;MACrChE,CAAC,CAACwE,WAAW,CAAC4E,MAAM,CAACW,IAAI,CAACC,IAAI,CAAEhK,CAAC,CAACyE,OAAO,CAACgH,KAAK,CAAC,CAAC,CAACvC,WAAW,CAAC,IAAI,CAAC,GAAGlJ,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAACN,MAAO,CAAC,CAAC;IAC1H;IAEA,IAAIuO,MAAM,GAAG5W,CAAC,CAACyE,OAAO,CAACgH,KAAK,CAAC,CAAC,CAACsF,UAAU,CAAC,IAAI,CAAC,GAAG/Q,CAAC,CAACyE,OAAO,CAACgH,KAAK,CAAC,CAAC,CAAC8B,KAAK,CAAC,CAAC;IAC3E,IAAIvN,CAAC,CAACwG,OAAO,CAACtD,aAAa,KAAK,KAAK,EAAElD,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,cAAc,CAAC,CAAC4E,KAAK,CAACvN,CAAC,CAACuE,UAAU,GAAGqS,MAAM,CAAC;EAE9G,CAAC;EAEDjX,KAAK,CAACgI,SAAS,CAACkP,OAAO,GAAG,YAAW;IAEjC,IAAI7W,CAAC,GAAG,IAAI;MACRsJ,UAAU;IAEdtJ,CAAC,CAACyE,OAAO,CAACqE,IAAI,CAAC,UAASZ,KAAK,EAAEpI,OAAO,EAAE;MACpCwJ,UAAU,GAAItJ,CAAC,CAACuE,UAAU,GAAG2D,KAAK,GAAI,CAAC,CAAC;MACxC,IAAIlI,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,IAAI,EAAE;QACxB5C,CAAC,CAACI,OAAO,CAAC,CAACmK,GAAG,CAAC;UACXqM,QAAQ,EAAE,UAAU;UACpBQ,KAAK,EAAExN,UAAU;UACjBI,GAAG,EAAE,CAAC;UACNpG,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG,CAAC;UAC5BqM,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,MAAM;QACHjQ,CAAC,CAACI,OAAO,CAAC,CAACmK,GAAG,CAAC;UACXqM,QAAQ,EAAE,UAAU;UACpB7M,IAAI,EAAEH,UAAU;UAChBI,GAAG,EAAE,CAAC;UACNpG,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG,CAAC;UAC5BqM,OAAO,EAAE;QACb,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IAEF3P,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACxI,CAAC,CAAC6D,YAAY,CAAC,CAACoG,GAAG,CAAC;MAC7B3G,MAAM,EAAEtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG,CAAC;MAC5BqM,OAAO,EAAE;IACb,CAAC,CAAC;EAEN,CAAC;EAEDhQ,KAAK,CAACgI,SAAS,CAACoP,SAAS,GAAG,YAAW;IAEnC,IAAI/W,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAAC/D,YAAY,KAAK,CAAC,IAAIzC,CAAC,CAACwG,OAAO,CAACpG,cAAc,KAAK,IAAI,IAAIJ,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;MACnG,IAAI8F,YAAY,GAAGjJ,CAAC,CAACyE,OAAO,CAAC+D,EAAE,CAACxI,CAAC,CAAC6D,YAAY,CAAC,CAACqF,WAAW,CAAC,IAAI,CAAC;MACjElJ,CAAC,CAAC8E,KAAK,CAACmF,GAAG,CAAC,QAAQ,EAAEhB,YAAY,CAAC;IACvC;EAEJ,CAAC;EAEDtJ,KAAK,CAACgI,SAAS,CAACqP,SAAS,GACzBrX,KAAK,CAACgI,SAAS,CAACsP,cAAc,GAAG,YAAW;IAExC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEQ,IAAIjX,CAAC,GAAG,IAAI;MAAE0V,CAAC;MAAEwB,IAAI;MAAEhG,MAAM;MAAEiG,KAAK;MAAEvJ,OAAO,GAAG,KAAK;MAAEgI,IAAI;IAE3D,IAAIlW,CAAC,CAACkW,IAAI,CAAEwB,SAAS,CAAC,CAAC,CAAE,CAAC,KAAK,QAAQ,EAAG;MAEtClG,MAAM,GAAIkG,SAAS,CAAC,CAAC,CAAC;MACtBxJ,OAAO,GAAGwJ,SAAS,CAAC,CAAC,CAAC;MACtBxB,IAAI,GAAG,UAAU;IAErB,CAAC,MAAM,IAAKlW,CAAC,CAACkW,IAAI,CAAEwB,SAAS,CAAC,CAAC,CAAE,CAAC,KAAK,QAAQ,EAAG;MAE9ClG,MAAM,GAAIkG,SAAS,CAAC,CAAC,CAAC;MACtBD,KAAK,GAAGC,SAAS,CAAC,CAAC,CAAC;MACpBxJ,OAAO,GAAGwJ,SAAS,CAAC,CAAC,CAAC;MAEtB,IAAKA,SAAS,CAAC,CAAC,CAAC,KAAK,YAAY,IAAI1X,CAAC,CAACkW,IAAI,CAAEwB,SAAS,CAAC,CAAC,CAAE,CAAC,KAAK,OAAO,EAAG;QAEvExB,IAAI,GAAG,YAAY;MAEvB,CAAC,MAAM,IAAK,OAAOwB,SAAS,CAAC,CAAC,CAAC,KAAK,WAAW,EAAG;QAE9CxB,IAAI,GAAG,QAAQ;MAEnB;IAEJ;IAEA,IAAKA,IAAI,KAAK,QAAQ,EAAG;MAErB5V,CAAC,CAACwG,OAAO,CAAC0K,MAAM,CAAC,GAAGiG,KAAK;IAG7B,CAAC,MAAM,IAAKvB,IAAI,KAAK,UAAU,EAAG;MAE9BlW,CAAC,CAACoJ,IAAI,CAAEoI,MAAM,EAAG,UAAUmG,GAAG,EAAE5E,GAAG,EAAG;QAElCzS,CAAC,CAACwG,OAAO,CAAC6Q,GAAG,CAAC,GAAG5E,GAAG;MAExB,CAAC,CAAC;IAGN,CAAC,MAAM,IAAKmD,IAAI,KAAK,YAAY,EAAG;MAEhC,KAAMsB,IAAI,IAAIC,KAAK,EAAG;QAElB,IAAIzX,CAAC,CAACkW,IAAI,CAAE5V,CAAC,CAACwG,OAAO,CAACpE,UAAW,CAAC,KAAK,OAAO,EAAG;UAE7CpC,CAAC,CAACwG,OAAO,CAACpE,UAAU,GAAG,CAAE+U,KAAK,CAACD,IAAI,CAAC,CAAE;QAE1C,CAAC,MAAM;UAEHxB,CAAC,GAAG1V,CAAC,CAACwG,OAAO,CAACpE,UAAU,CAACiG,MAAM,GAAC,CAAC;;UAEjC;UACA,OAAOqN,CAAC,IAAI,CAAC,EAAG;YAEZ,IAAI1V,CAAC,CAACwG,OAAO,CAACpE,UAAU,CAACsT,CAAC,CAAC,CAACxI,UAAU,KAAKiK,KAAK,CAACD,IAAI,CAAC,CAAChK,UAAU,EAAG;cAEhElN,CAAC,CAACwG,OAAO,CAACpE,UAAU,CAACyT,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC;YAEpC;YAEAA,CAAC,EAAE;UAEP;UAEA1V,CAAC,CAACwG,OAAO,CAACpE,UAAU,CAACiP,IAAI,CAAE8F,KAAK,CAACD,IAAI,CAAE,CAAC;QAE5C;MAEJ;IAEJ;IAEA,IAAKtJ,OAAO,EAAG;MAEX5N,CAAC,CAACoI,MAAM,CAAC,CAAC;MACVpI,CAAC,CAAC+I,MAAM,CAAC,CAAC;IAEd;EAEJ,CAAC;EAEDpJ,KAAK,CAACgI,SAAS,CAACP,WAAW,GAAG,YAAW;IAErC,IAAIpH,CAAC,GAAG,IAAI;IAEZA,CAAC,CAAC0W,aAAa,CAAC,CAAC;IAEjB1W,CAAC,CAAC+W,SAAS,CAAC,CAAC;IAEb,IAAI/W,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;MAC1BzB,CAAC,CAACqW,MAAM,CAACrW,CAAC,CAACwQ,OAAO,CAACxQ,CAAC,CAAC6D,YAAY,CAAC,CAAC;IACvC,CAAC,MAAM;MACH7D,CAAC,CAAC6W,OAAO,CAAC,CAAC;IACf;IAEA7W,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,aAAa,EAAE,CAAC7N,CAAC,CAAC,CAAC;EAEzC,CAAC;EAEDL,KAAK,CAACgI,SAAS,CAACsK,QAAQ,GAAG,YAAW;IAElC,IAAIjS,CAAC,GAAG,IAAI;MACRsX,SAAS,GAAG5Q,QAAQ,CAAC6Q,IAAI,CAACC,KAAK;IAEnCxX,CAAC,CAAC6F,YAAY,GAAG7F,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,MAAM;IAE7D,IAAInD,CAAC,CAAC6F,YAAY,KAAK,KAAK,EAAE;MAC1B7F,CAAC,CAACgG,OAAO,CAACiF,QAAQ,CAAC,gBAAgB,CAAC;IACxC,CAAC,MAAM;MACHjL,CAAC,CAACgG,OAAO,CAACkF,WAAW,CAAC,gBAAgB,CAAC;IAC3C;IAEA,IAAIoM,SAAS,CAACG,gBAAgB,KAAKC,SAAS,IACxCJ,SAAS,CAACK,aAAa,KAAKD,SAAS,IACrCJ,SAAS,CAACM,YAAY,KAAKF,SAAS,EAAE;MACtC,IAAI1X,CAAC,CAACwG,OAAO,CAACxD,MAAM,KAAK,IAAI,EAAE;QAC3BhD,CAAC,CAACwF,cAAc,GAAG,IAAI;MAC3B;IACJ;IAEA,IAAKxF,CAAC,CAACwG,OAAO,CAAC/E,IAAI,EAAG;MAClB,IAAK,OAAOzB,CAAC,CAACwG,OAAO,CAAClD,MAAM,KAAK,QAAQ,EAAG;QACxC,IAAItD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG,CAAC,EAAG;UACvBtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAG,CAAC;QACxB;MACJ,CAAC,MAAM;QACHtD,CAAC,CAACwG,OAAO,CAAClD,MAAM,GAAGtD,CAAC,CAACE,QAAQ,CAACoD,MAAM;MACxC;IACJ;IAEA,IAAIgU,SAAS,CAACO,UAAU,KAAKH,SAAS,EAAE;MACpC1X,CAAC,CAACoF,QAAQ,GAAG,YAAY;MACzBpF,CAAC,CAACkG,aAAa,GAAG,cAAc;MAChClG,CAAC,CAACmG,cAAc,GAAG,aAAa;MAChC,IAAImR,SAAS,CAACQ,mBAAmB,KAAKJ,SAAS,IAAIJ,SAAS,CAACS,iBAAiB,KAAKL,SAAS,EAAE1X,CAAC,CAACoF,QAAQ,GAAG,KAAK;IACpH;IACA,IAAIkS,SAAS,CAACU,YAAY,KAAKN,SAAS,EAAE;MACtC1X,CAAC,CAACoF,QAAQ,GAAG,cAAc;MAC3BpF,CAAC,CAACkG,aAAa,GAAG,gBAAgB;MAClClG,CAAC,CAACmG,cAAc,GAAG,eAAe;MAClC,IAAImR,SAAS,CAACQ,mBAAmB,KAAKJ,SAAS,IAAIJ,SAAS,CAACW,cAAc,KAAKP,SAAS,EAAE1X,CAAC,CAACoF,QAAQ,GAAG,KAAK;IACjH;IACA,IAAIkS,SAAS,CAACY,eAAe,KAAKR,SAAS,EAAE;MACzC1X,CAAC,CAACoF,QAAQ,GAAG,iBAAiB;MAC9BpF,CAAC,CAACkG,aAAa,GAAG,mBAAmB;MACrClG,CAAC,CAACmG,cAAc,GAAG,kBAAkB;MACrC,IAAImR,SAAS,CAACQ,mBAAmB,KAAKJ,SAAS,IAAIJ,SAAS,CAACS,iBAAiB,KAAKL,SAAS,EAAE1X,CAAC,CAACoF,QAAQ,GAAG,KAAK;IACpH;IACA,IAAIkS,SAAS,CAACa,WAAW,KAAKT,SAAS,EAAE;MACrC1X,CAAC,CAACoF,QAAQ,GAAG,aAAa;MAC1BpF,CAAC,CAACkG,aAAa,GAAG,eAAe;MACjClG,CAAC,CAACmG,cAAc,GAAG,cAAc;MACjC,IAAImR,SAAS,CAACa,WAAW,KAAKT,SAAS,EAAE1X,CAAC,CAACoF,QAAQ,GAAG,KAAK;IAC/D;IACA,IAAIkS,SAAS,CAACc,SAAS,KAAKV,SAAS,IAAI1X,CAAC,CAACoF,QAAQ,KAAK,KAAK,EAAE;MAC3DpF,CAAC,CAACoF,QAAQ,GAAG,WAAW;MACxBpF,CAAC,CAACkG,aAAa,GAAG,WAAW;MAC7BlG,CAAC,CAACmG,cAAc,GAAG,YAAY;IACnC;IACAnG,CAAC,CAACgF,iBAAiB,GAAGhF,CAAC,CAACwG,OAAO,CAACvD,YAAY,IAAKjD,CAAC,CAACoF,QAAQ,KAAK,IAAI,IAAIpF,CAAC,CAACoF,QAAQ,KAAK,KAAM;EACjG,CAAC;EAGDzF,KAAK,CAACgI,SAAS,CAACqE,eAAe,GAAG,UAAS9D,KAAK,EAAE;IAE9C,IAAIlI,CAAC,GAAG,IAAI;MACR0R,YAAY;MAAE2G,SAAS;MAAEnK,WAAW;MAAEoK,SAAS;IAEnDD,SAAS,GAAGrY,CAAC,CAACgG,OAAO,CAChB6B,IAAI,CAAC,cAAc,CAAC,CACpBqD,WAAW,CAAC,yCAAyC,CAAC,CACtDpD,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;IAEhC9H,CAAC,CAACyE,OAAO,CACJ+D,EAAE,CAACN,KAAK,CAAC,CACT+C,QAAQ,CAAC,eAAe,CAAC;IAE9B,IAAIjL,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;MAE/B,IAAI0X,QAAQ,GAAGvY,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MAEvDiP,YAAY,GAAG3H,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC;MAErD,IAAIzC,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,EAAE;QAE7B,IAAIsG,KAAK,IAAIwJ,YAAY,IAAIxJ,KAAK,IAAKlI,CAAC,CAACsE,UAAU,GAAG,CAAC,GAAIoN,YAAY,EAAE;UACrE1R,CAAC,CAACyE,OAAO,CACJ6P,KAAK,CAACpM,KAAK,GAAGwJ,YAAY,GAAG6G,QAAQ,EAAErQ,KAAK,GAAGwJ,YAAY,GAAG,CAAC,CAAC,CAChEzG,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;QAErC,CAAC,MAAM;UAEHoG,WAAW,GAAGlO,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGyF,KAAK;UAC5CmQ,SAAS,CACJ/D,KAAK,CAACpG,WAAW,GAAGwD,YAAY,GAAG,CAAC,GAAG6G,QAAQ,EAAErK,WAAW,GAAGwD,YAAY,GAAG,CAAC,CAAC,CAChFzG,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;QAErC;QAEA,IAAII,KAAK,KAAK,CAAC,EAAE;UAEbmQ,SAAS,CACJ7P,EAAE,CAAC6P,SAAS,CAAChQ,MAAM,GAAG,CAAC,GAAGrI,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC,CACjDwI,QAAQ,CAAC,cAAc,CAAC;QAEjC,CAAC,MAAM,IAAI/C,KAAK,KAAKlI,CAAC,CAACsE,UAAU,GAAG,CAAC,EAAE;UAEnC+T,SAAS,CACJ7P,EAAE,CAACxI,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC,CAC1BwI,QAAQ,CAAC,cAAc,CAAC;QAEjC;MAEJ;MAEAjL,CAAC,CAACyE,OAAO,CACJ+D,EAAE,CAACN,KAAK,CAAC,CACT+C,QAAQ,CAAC,cAAc,CAAC;IAEjC,CAAC,MAAM;MAEH,IAAI/C,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAKlI,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAa,EAAE;QAEhEzC,CAAC,CAACyE,OAAO,CACJ6P,KAAK,CAACpM,KAAK,EAAEA,KAAK,GAAGlI,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC,CAC5CwI,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;MAErC,CAAC,MAAM,IAAIuQ,SAAS,CAAChQ,MAAM,IAAIrI,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;QAEnD4V,SAAS,CACJpN,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;MAErC,CAAC,MAAM;QAEHwQ,SAAS,GAAGtY,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY;QACjDyL,WAAW,GAAGlO,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,GAAG5B,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAGyF,KAAK,GAAGA,KAAK;QAElF,IAAIlI,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIzC,CAAC,CAACwG,OAAO,CAAC9D,cAAc,IAAK1C,CAAC,CAACsE,UAAU,GAAG4D,KAAK,GAAIlI,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;UAEvG4V,SAAS,CACJ/D,KAAK,CAACpG,WAAW,IAAIlO,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG6V,SAAS,CAAC,EAAEpK,WAAW,GAAGoK,SAAS,CAAC,CAClFrN,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;QAErC,CAAC,MAAM;UAEHuQ,SAAS,CACJ/D,KAAK,CAACpG,WAAW,EAAEA,WAAW,GAAGlO,CAAC,CAACwG,OAAO,CAAC/D,YAAY,CAAC,CACxDwI,QAAQ,CAAC,cAAc,CAAC,CACxBnD,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;QAErC;MAEJ;IAEJ;IAEA,IAAI9H,CAAC,CAACwG,OAAO,CAAC1E,QAAQ,KAAK,UAAU,IAAI9B,CAAC,CAACwG,OAAO,CAAC1E,QAAQ,KAAK,aAAa,EAAE;MAC3E9B,CAAC,CAAC8B,QAAQ,CAAC,CAAC;IAChB;EACJ,CAAC;EAEDnC,KAAK,CAACgI,SAAS,CAACmE,aAAa,GAAG,YAAW;IAEvC,IAAI9L,CAAC,GAAG,IAAI;MACRkB,CAAC;MAAEwO,UAAU;MAAE8I,aAAa;IAEhC,IAAIxY,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,IAAI,EAAE;MACzBzB,CAAC,CAACwG,OAAO,CAAC3F,UAAU,GAAG,KAAK;IAChC;IAEA,IAAIb,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,IAAI,IAAI5B,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;MAEzDiO,UAAU,GAAG,IAAI;MAEjB,IAAI1P,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;QAEvC,IAAIzC,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;UAC/B2X,aAAa,GAAGxY,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC;QAC9C,CAAC,MAAM;UACH+V,aAAa,GAAGxY,CAAC,CAACwG,OAAO,CAAC/D,YAAY;QAC1C;QAEA,KAAKvB,CAAC,GAAGlB,CAAC,CAACsE,UAAU,EAAEpD,CAAC,GAAIlB,CAAC,CAACsE,UAAU,GAChCkU,aAAc,EAAEtX,CAAC,IAAI,CAAC,EAAE;UAC5BwO,UAAU,GAAGxO,CAAC,GAAG,CAAC;UAClBxB,CAAC,CAACM,CAAC,CAACyE,OAAO,CAACiL,UAAU,CAAC,CAAC,CAAC+I,KAAK,CAAC,IAAI,CAAC,CAAC3Q,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAC9CA,IAAI,CAAC,kBAAkB,EAAE4H,UAAU,GAAG1P,CAAC,CAACsE,UAAU,CAAC,CACnDoE,SAAS,CAAC1I,CAAC,CAACwE,WAAW,CAAC,CAACyG,QAAQ,CAAC,cAAc,CAAC;QAC1D;QACA,KAAK/J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsX,aAAa,GAAIxY,CAAC,CAACsE,UAAU,EAAEpD,CAAC,IAAI,CAAC,EAAE;UACnDwO,UAAU,GAAGxO,CAAC;UACdxB,CAAC,CAACM,CAAC,CAACyE,OAAO,CAACiL,UAAU,CAAC,CAAC,CAAC+I,KAAK,CAAC,IAAI,CAAC,CAAC3Q,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAC9CA,IAAI,CAAC,kBAAkB,EAAE4H,UAAU,GAAG1P,CAAC,CAACsE,UAAU,CAAC,CACnDgE,QAAQ,CAACtI,CAAC,CAACwE,WAAW,CAAC,CAACyG,QAAQ,CAAC,cAAc,CAAC;QACzD;QACAjL,CAAC,CAACwE,WAAW,CAACqD,IAAI,CAAC,eAAe,CAAC,CAACA,IAAI,CAAC,MAAM,CAAC,CAACiB,IAAI,CAAC,YAAW;UAC7DpJ,CAAC,CAAC,IAAI,CAAC,CAACoI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;QAC1B,CAAC,CAAC;MAEN;IAEJ;EAEJ,CAAC;EAEDnI,KAAK,CAACgI,SAAS,CAACoH,SAAS,GAAG,UAAU2J,MAAM,EAAG;IAE3C,IAAI1Y,CAAC,GAAG,IAAI;IAEZ,IAAI,CAAC0Y,MAAM,EAAG;MACV1Y,CAAC,CAAC6G,QAAQ,CAAC,CAAC;IAChB;IACA7G,CAAC,CAAC0F,WAAW,GAAGgT,MAAM;EAE1B,CAAC;EAED/Y,KAAK,CAACgI,SAAS,CAACR,aAAa,GAAG,UAAS2G,KAAK,EAAE;IAE5C,IAAI9N,CAAC,GAAG,IAAI;IAEZ,IAAI2Y,aAAa,GACbjZ,CAAC,CAACoO,KAAK,CAACrD,MAAM,CAAC,CAAC2D,EAAE,CAAC,cAAc,CAAC,GAC9B1O,CAAC,CAACoO,KAAK,CAACrD,MAAM,CAAC,GACf/K,CAAC,CAACoO,KAAK,CAACrD,MAAM,CAAC,CAACmO,OAAO,CAAC,cAAc,CAAC;IAE/C,IAAI1Q,KAAK,GAAG4J,QAAQ,CAAC6G,aAAa,CAAC7Q,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAE5D,IAAI,CAACI,KAAK,EAAEA,KAAK,GAAG,CAAC;IAErB,IAAIlI,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAExCzC,CAAC,CAAC2K,YAAY,CAACzC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;MAClC;IAEJ;IAEAlI,CAAC,CAAC2K,YAAY,CAACzC,KAAK,CAAC;EAEzB,CAAC;EAEDvI,KAAK,CAACgI,SAAS,CAACgD,YAAY,GAAG,UAASzC,KAAK,EAAE2Q,IAAI,EAAE9K,WAAW,EAAE;IAE9D,IAAI4C,WAAW;MAAEmI,SAAS;MAAEC,QAAQ;MAAEC,SAAS;MAAE1P,UAAU,GAAG,IAAI;MAC9DtJ,CAAC,GAAG,IAAI;MAAEiZ,SAAS;IAEvBJ,IAAI,GAAGA,IAAI,IAAI,KAAK;IAEpB,IAAI7Y,CAAC,CAACwD,SAAS,KAAK,IAAI,IAAIxD,CAAC,CAACwG,OAAO,CAACnD,cAAc,KAAK,IAAI,EAAE;MAC3D;IACJ;IAEA,IAAIrD,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,IAAI,IAAIzB,CAAC,CAAC6D,YAAY,KAAKqE,KAAK,EAAE;MACrD;IACJ;IAEA,IAAI2Q,IAAI,KAAK,KAAK,EAAE;MAChB7Y,CAAC,CAACQ,QAAQ,CAAC0H,KAAK,CAAC;IACrB;IAEAyI,WAAW,GAAGzI,KAAK;IACnBoB,UAAU,GAAGtJ,CAAC,CAACwQ,OAAO,CAACG,WAAW,CAAC;IACnCqI,SAAS,GAAGhZ,CAAC,CAACwQ,OAAO,CAACxQ,CAAC,CAAC6D,YAAY,CAAC;IAErC7D,CAAC,CAAC4D,WAAW,GAAG5D,CAAC,CAAC4E,SAAS,KAAK,IAAI,GAAGoU,SAAS,GAAGhZ,CAAC,CAAC4E,SAAS;IAE9D,IAAI5E,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,IAAI5B,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,KAAK,KAAKqH,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGlI,CAAC,CAACwL,WAAW,CAAC,CAAC,GAAGxL,CAAC,CAACwG,OAAO,CAAC9D,cAAc,CAAC,EAAE;MACrI,IAAI1C,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;QAC1BkP,WAAW,GAAG3Q,CAAC,CAAC6D,YAAY;QAC5B,IAAIkK,WAAW,KAAK,IAAI,IAAI/N,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;UAC/DzC,CAAC,CAACqJ,YAAY,CAAC2P,SAAS,EAAE,YAAW;YACjChZ,CAAC,CAACgV,SAAS,CAACrE,WAAW,CAAC;UAC5B,CAAC,CAAC;QACN,CAAC,MAAM;UACH3Q,CAAC,CAACgV,SAAS,CAACrE,WAAW,CAAC;QAC5B;MACJ;MACA;IACJ,CAAC,MAAM,IAAI3Q,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,IAAI5B,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,KAAKqH,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAIlI,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAe,CAAC,EAAE;MAC1I,IAAI1C,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,KAAK,EAAE;QAC1BkP,WAAW,GAAG3Q,CAAC,CAAC6D,YAAY;QAC5B,IAAIkK,WAAW,KAAK,IAAI,IAAI/N,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;UAC/DzC,CAAC,CAACqJ,YAAY,CAAC2P,SAAS,EAAE,YAAW;YACjChZ,CAAC,CAACgV,SAAS,CAACrE,WAAW,CAAC;UAC5B,CAAC,CAAC;QACN,CAAC,MAAM;UACH3Q,CAAC,CAACgV,SAAS,CAACrE,WAAW,CAAC;QAC5B;MACJ;MACA;IACJ;IAEA,IAAK3Q,CAAC,CAACwG,OAAO,CAAC7F,QAAQ,EAAG;MACtBmK,aAAa,CAAC9K,CAAC,CAAC0D,aAAa,CAAC;IAClC;IAEA,IAAIiN,WAAW,GAAG,CAAC,EAAE;MACjB,IAAI3Q,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAc,KAAK,CAAC,EAAE;QAC/CoW,SAAS,GAAG9Y,CAAC,CAACsE,UAAU,GAAItE,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAe;MACxE,CAAC,MAAM;QACHoW,SAAS,GAAG9Y,CAAC,CAACsE,UAAU,GAAGqM,WAAW;MAC1C;IACJ,CAAC,MAAM,IAAIA,WAAW,IAAI3Q,CAAC,CAACsE,UAAU,EAAE;MACpC,IAAItE,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC9D,cAAc,KAAK,CAAC,EAAE;QAC/CoW,SAAS,GAAG,CAAC;MACjB,CAAC,MAAM;QACHA,SAAS,GAAGnI,WAAW,GAAG3Q,CAAC,CAACsE,UAAU;MAC1C;IACJ,CAAC,MAAM;MACHwU,SAAS,GAAGnI,WAAW;IAC3B;IAEA3Q,CAAC,CAACwD,SAAS,GAAG,IAAI;IAElBxD,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,cAAc,EAAE,CAAC7N,CAAC,EAAEA,CAAC,CAAC6D,YAAY,EAAEiV,SAAS,CAAC,CAAC;IAEjEC,QAAQ,GAAG/Y,CAAC,CAAC6D,YAAY;IACzB7D,CAAC,CAAC6D,YAAY,GAAGiV,SAAS;IAE1B9Y,CAAC,CAACgM,eAAe,CAAChM,CAAC,CAAC6D,YAAY,CAAC;IAEjC,IAAK7D,CAAC,CAACwG,OAAO,CAAChG,QAAQ,EAAG;MAEtByY,SAAS,GAAGjZ,CAAC,CAACuK,YAAY,CAAC,CAAC;MAC5B0O,SAAS,GAAGA,SAAS,CAACvO,KAAK,CAAC,UAAU,CAAC;MAEvC,IAAKuO,SAAS,CAAC3U,UAAU,IAAI2U,SAAS,CAACzS,OAAO,CAAC/D,YAAY,EAAG;QAC1DwW,SAAS,CAACjN,eAAe,CAAChM,CAAC,CAAC6D,YAAY,CAAC;MAC7C;IAEJ;IAEA7D,CAAC,CAAC+L,UAAU,CAAC,CAAC;IACd/L,CAAC,CAACqS,YAAY,CAAC,CAAC;IAEhB,IAAIrS,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,IAAI,EAAE;MACzB,IAAIsM,WAAW,KAAK,IAAI,EAAE;QAEtB/N,CAAC,CAAC4P,YAAY,CAACmJ,QAAQ,CAAC;QAExB/Y,CAAC,CAACyP,SAAS,CAACqJ,SAAS,EAAE,YAAW;UAC9B9Y,CAAC,CAACgV,SAAS,CAAC8D,SAAS,CAAC;QAC1B,CAAC,CAAC;MAEN,CAAC,MAAM;QACH9Y,CAAC,CAACgV,SAAS,CAAC8D,SAAS,CAAC;MAC1B;MACA9Y,CAAC,CAACgJ,aAAa,CAAC,CAAC;MACjB;IACJ;IAEA,IAAI+E,WAAW,KAAK,IAAI,IAAI/N,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAC/DzC,CAAC,CAACqJ,YAAY,CAACC,UAAU,EAAE,YAAW;QAClCtJ,CAAC,CAACgV,SAAS,CAAC8D,SAAS,CAAC;MAC1B,CAAC,CAAC;IACN,CAAC,MAAM;MACH9Y,CAAC,CAACgV,SAAS,CAAC8D,SAAS,CAAC;IAC1B;EAEJ,CAAC;EAEDnZ,KAAK,CAACgI,SAAS,CAACuK,SAAS,GAAG,YAAW;IAEnC,IAAIlS,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACwG,OAAO,CAACjG,MAAM,KAAK,IAAI,IAAIP,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAEpEzC,CAAC,CAACoE,UAAU,CAAC8U,IAAI,CAAC,CAAC;MACnBlZ,CAAC,CAACmE,UAAU,CAAC+U,IAAI,CAAC,CAAC;IAEvB;IAEA,IAAIlZ,CAAC,CAACwG,OAAO,CAACpF,IAAI,KAAK,IAAI,IAAIpB,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAElEzC,CAAC,CAAC+D,KAAK,CAACmV,IAAI,CAAC,CAAC;IAElB;IAEAlZ,CAAC,CAACgG,OAAO,CAACiF,QAAQ,CAAC,eAAe,CAAC;EAEvC,CAAC;EAEDtL,KAAK,CAACgI,SAAS,CAACwR,cAAc,GAAG,YAAW;IAExC,IAAIC,KAAK;MAAEC,KAAK;MAAEC,CAAC;MAAEC,UAAU;MAAEvZ,CAAC,GAAG,IAAI;IAEzCoZ,KAAK,GAAGpZ,CAAC,CAAC+E,WAAW,CAACyU,MAAM,GAAGxZ,CAAC,CAAC+E,WAAW,CAAC0U,IAAI;IACjDJ,KAAK,GAAGrZ,CAAC,CAAC+E,WAAW,CAAC2U,MAAM,GAAG1Z,CAAC,CAAC+E,WAAW,CAAC4U,IAAI;IACjDL,CAAC,GAAGvP,IAAI,CAAC6P,KAAK,CAACP,KAAK,EAAED,KAAK,CAAC;IAE5BG,UAAU,GAAGxP,IAAI,CAAC8P,KAAK,CAACP,CAAC,GAAG,GAAG,GAAGvP,IAAI,CAAC+P,EAAE,CAAC;IAC1C,IAAIP,UAAU,GAAG,CAAC,EAAE;MAChBA,UAAU,GAAG,GAAG,GAAGxP,IAAI,CAAC4H,GAAG,CAAC4H,UAAU,CAAC;IAC3C;IAEA,IAAKA,UAAU,IAAI,EAAE,IAAMA,UAAU,IAAI,CAAE,EAAE;MACzC,OAAQvZ,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IACtD;IACA,IAAKiX,UAAU,IAAI,GAAG,IAAMA,UAAU,IAAI,GAAI,EAAE;MAC5C,OAAQvZ,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IACtD;IACA,IAAKiX,UAAU,IAAI,GAAG,IAAMA,UAAU,IAAI,GAAI,EAAE;MAC5C,OAAQvZ,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IACtD;IACA,IAAItC,CAAC,CAACwG,OAAO,CAACpD,eAAe,KAAK,IAAI,EAAE;MACpC,IAAKmW,UAAU,IAAI,EAAE,IAAMA,UAAU,IAAI,GAAI,EAAE;QAC3C,OAAO,MAAM;MACjB,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ;IAEA,OAAO,UAAU;EAErB,CAAC;EAED5Z,KAAK,CAACgI,SAAS,CAACoS,QAAQ,GAAG,UAASjM,KAAK,EAAE;IAEvC,IAAI9N,CAAC,GAAG,IAAI;MACRsE,UAAU;MACVR,SAAS;IAEb9D,CAAC,CAACyD,QAAQ,GAAG,KAAK;IAClBzD,CAAC,CAAC6E,OAAO,GAAG,KAAK;IAEjB,IAAI7E,CAAC,CAACqE,SAAS,EAAE;MACbrE,CAAC,CAACqE,SAAS,GAAG,KAAK;MACnB,OAAO,KAAK;IAChB;IAEArE,CAAC,CAAC0F,WAAW,GAAG,KAAK;IACrB1F,CAAC,CAAC+F,WAAW,GAAK/F,CAAC,CAAC+E,WAAW,CAACiV,WAAW,GAAG,EAAE,GAAK,KAAK,GAAG,IAAI;IAEjE,IAAKha,CAAC,CAAC+E,WAAW,CAAC0U,IAAI,KAAK/B,SAAS,EAAG;MACpC,OAAO,KAAK;IAChB;IAEA,IAAK1X,CAAC,CAAC+E,WAAW,CAACkV,OAAO,KAAK,IAAI,EAAG;MAClCja,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,MAAM,EAAE,CAAC7N,CAAC,EAAEA,CAAC,CAACmZ,cAAc,CAAC,CAAC,CAAE,CAAC;IACvD;IAEA,IAAKnZ,CAAC,CAAC+E,WAAW,CAACiV,WAAW,IAAIha,CAAC,CAAC+E,WAAW,CAACmV,QAAQ,EAAG;MAEvDpW,SAAS,GAAG9D,CAAC,CAACmZ,cAAc,CAAC,CAAC;MAE9B,QAASrV,SAAS;QAEd,KAAK,MAAM;QACX,KAAK,MAAM;UAEPQ,UAAU,GACNtE,CAAC,CAACwG,OAAO,CAAC3D,YAAY,GAClB7C,CAAC,CAACwO,cAAc,CAAExO,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACuR,aAAa,CAAC,CAAE,CAAC,GACtDvR,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACuR,aAAa,CAAC,CAAC;UAE1CvR,CAAC,CAAC2D,gBAAgB,GAAG,CAAC;UAEtB;QAEJ,KAAK,OAAO;QACZ,KAAK,IAAI;UAELW,UAAU,GACNtE,CAAC,CAACwG,OAAO,CAAC3D,YAAY,GAClB7C,CAAC,CAACwO,cAAc,CAAExO,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACuR,aAAa,CAAC,CAAE,CAAC,GACtDvR,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACuR,aAAa,CAAC,CAAC;UAE1CvR,CAAC,CAAC2D,gBAAgB,GAAG,CAAC;UAEtB;QAEJ;MAGJ;MAEA,IAAIG,SAAS,IAAI,UAAU,EAAG;QAE1B9D,CAAC,CAAC2K,YAAY,CAAErG,UAAW,CAAC;QAC5BtE,CAAC,CAAC+E,WAAW,GAAG,CAAC,CAAC;QAClB/E,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,OAAO,EAAE,CAAC7N,CAAC,EAAE8D,SAAS,CAAE,CAAC;MAE/C;IAEJ,CAAC,MAAM;MAEH,IAAK9D,CAAC,CAAC+E,WAAW,CAACyU,MAAM,KAAKxZ,CAAC,CAAC+E,WAAW,CAAC0U,IAAI,EAAG;QAE/CzZ,CAAC,CAAC2K,YAAY,CAAE3K,CAAC,CAAC6D,YAAa,CAAC;QAChC7D,CAAC,CAAC+E,WAAW,GAAG,CAAC,CAAC;MAEtB;IAEJ;EAEJ,CAAC;EAEDpF,KAAK,CAACgI,SAAS,CAACN,YAAY,GAAG,UAASyG,KAAK,EAAE;IAE3C,IAAI9N,CAAC,GAAG,IAAI;IAEZ,IAAKA,CAAC,CAACwG,OAAO,CAAC5D,KAAK,KAAK,KAAK,IAAM,YAAY,IAAI8D,QAAQ,IAAI1G,CAAC,CAACwG,OAAO,CAAC5D,KAAK,KAAK,KAAM,EAAE;MACxF;IACJ,CAAC,MAAM,IAAI5C,CAAC,CAACwG,OAAO,CAAClF,SAAS,KAAK,KAAK,IAAIwM,KAAK,CAAC8H,IAAI,CAACjD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5E;IACJ;IAEA3S,CAAC,CAAC+E,WAAW,CAACoV,WAAW,GAAGrM,KAAK,CAACsM,aAAa,IAAItM,KAAK,CAACsM,aAAa,CAACC,OAAO,KAAK3C,SAAS,GACxF5J,KAAK,CAACsM,aAAa,CAACC,OAAO,CAAChS,MAAM,GAAG,CAAC;IAE1CrI,CAAC,CAAC+E,WAAW,CAACmV,QAAQ,GAAGla,CAAC,CAACgE,SAAS,GAAGhE,CAAC,CAACwG,OAAO,CAC3CzD,cAAc;IAEnB,IAAI/C,CAAC,CAACwG,OAAO,CAACpD,eAAe,KAAK,IAAI,EAAE;MACpCpD,CAAC,CAAC+E,WAAW,CAACmV,QAAQ,GAAGla,CAAC,CAACiE,UAAU,GAAGjE,CAAC,CAACwG,OAAO,CAC5CzD,cAAc;IACvB;IAEA,QAAQ+K,KAAK,CAACvH,IAAI,CAAC2M,MAAM;MAErB,KAAK,OAAO;QACRlT,CAAC,CAACsa,UAAU,CAACxM,KAAK,CAAC;QACnB;MAEJ,KAAK,MAAM;QACP9N,CAAC,CAACua,SAAS,CAACzM,KAAK,CAAC;QAClB;MAEJ,KAAK,KAAK;QACN9N,CAAC,CAAC+Z,QAAQ,CAACjM,KAAK,CAAC;QACjB;IAER;EAEJ,CAAC;EAEDnO,KAAK,CAACgI,SAAS,CAAC4S,SAAS,GAAG,UAASzM,KAAK,EAAE;IAExC,IAAI9N,CAAC,GAAG,IAAI;MACRwa,UAAU,GAAG,KAAK;MAClBC,OAAO;MAAEtB,cAAc;MAAEa,WAAW;MAAEU,cAAc;MAAEL,OAAO;MAAEM,mBAAmB;IAEtFN,OAAO,GAAGvM,KAAK,CAACsM,aAAa,KAAK1C,SAAS,GAAG5J,KAAK,CAACsM,aAAa,CAACC,OAAO,GAAG,IAAI;IAEhF,IAAI,CAACra,CAAC,CAACyD,QAAQ,IAAIzD,CAAC,CAACqE,SAAS,IAAIgW,OAAO,IAAIA,OAAO,CAAChS,MAAM,KAAK,CAAC,EAAE;MAC/D,OAAO,KAAK;IAChB;IAEAoS,OAAO,GAAGza,CAAC,CAACwQ,OAAO,CAACxQ,CAAC,CAAC6D,YAAY,CAAC;IAEnC7D,CAAC,CAAC+E,WAAW,CAAC0U,IAAI,GAAGY,OAAO,KAAK3C,SAAS,GAAG2C,OAAO,CAAC,CAAC,CAAC,CAACO,KAAK,GAAG9M,KAAK,CAAC+M,OAAO;IAC7E7a,CAAC,CAAC+E,WAAW,CAAC4U,IAAI,GAAGU,OAAO,KAAK3C,SAAS,GAAG2C,OAAO,CAAC,CAAC,CAAC,CAACS,KAAK,GAAGhN,KAAK,CAACiN,OAAO;IAE7E/a,CAAC,CAAC+E,WAAW,CAACiV,WAAW,GAAGjQ,IAAI,CAAC8P,KAAK,CAAC9P,IAAI,CAACiR,IAAI,CAC5CjR,IAAI,CAACkR,GAAG,CAACjb,CAAC,CAAC+E,WAAW,CAAC0U,IAAI,GAAGzZ,CAAC,CAAC+E,WAAW,CAACyU,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IAE5DmB,mBAAmB,GAAG5Q,IAAI,CAAC8P,KAAK,CAAC9P,IAAI,CAACiR,IAAI,CACtCjR,IAAI,CAACkR,GAAG,CAACjb,CAAC,CAAC+E,WAAW,CAAC4U,IAAI,GAAG3Z,CAAC,CAAC+E,WAAW,CAAC2U,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IAE5D,IAAI,CAAC1Z,CAAC,CAACwG,OAAO,CAACpD,eAAe,IAAI,CAACpD,CAAC,CAAC6E,OAAO,IAAI8V,mBAAmB,GAAG,CAAC,EAAE;MACrE3a,CAAC,CAACqE,SAAS,GAAG,IAAI;MAClB,OAAO,KAAK;IAChB;IAEA,IAAIrE,CAAC,CAACwG,OAAO,CAACpD,eAAe,KAAK,IAAI,EAAE;MACpCpD,CAAC,CAAC+E,WAAW,CAACiV,WAAW,GAAGW,mBAAmB;IACnD;IAEAxB,cAAc,GAAGnZ,CAAC,CAACmZ,cAAc,CAAC,CAAC;IAEnC,IAAIrL,KAAK,CAACsM,aAAa,KAAK1C,SAAS,IAAI1X,CAAC,CAAC+E,WAAW,CAACiV,WAAW,GAAG,CAAC,EAAE;MACpEha,CAAC,CAAC6E,OAAO,GAAG,IAAI;MAChBiJ,KAAK,CAACO,cAAc,CAAC,CAAC;IAC1B;IAEAqM,cAAc,GAAG,CAAC1a,CAAC,CAACwG,OAAO,CAAClE,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KAAKtC,CAAC,CAAC+E,WAAW,CAAC0U,IAAI,GAAGzZ,CAAC,CAAC+E,WAAW,CAACyU,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1G,IAAIxZ,CAAC,CAACwG,OAAO,CAACpD,eAAe,KAAK,IAAI,EAAE;MACpCsX,cAAc,GAAG1a,CAAC,CAAC+E,WAAW,CAAC4U,IAAI,GAAG3Z,CAAC,CAAC+E,WAAW,CAAC2U,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACvE;IAGAM,WAAW,GAAGha,CAAC,CAAC+E,WAAW,CAACiV,WAAW;IAEvCha,CAAC,CAAC+E,WAAW,CAACkV,OAAO,GAAG,KAAK;IAE7B,IAAIja,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,KAAK,KAAK,EAAE;MAC9B,IAAK5B,CAAC,CAAC6D,YAAY,KAAK,CAAC,IAAIsV,cAAc,KAAK,OAAO,IAAMnZ,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACwL,WAAW,CAAC,CAAC,IAAI2N,cAAc,KAAK,MAAO,EAAE;QAC1Ha,WAAW,GAAGha,CAAC,CAAC+E,WAAW,CAACiV,WAAW,GAAGha,CAAC,CAACwG,OAAO,CAAChF,YAAY;QAChExB,CAAC,CAAC+E,WAAW,CAACkV,OAAO,GAAG,IAAI;MAChC;IACJ;IAEA,IAAIja,CAAC,CAACwG,OAAO,CAACrD,QAAQ,KAAK,KAAK,EAAE;MAC9BnD,CAAC,CAAC4E,SAAS,GAAG6V,OAAO,GAAGT,WAAW,GAAGU,cAAc;IACxD,CAAC,MAAM;MACH1a,CAAC,CAAC4E,SAAS,GAAG6V,OAAO,GAAIT,WAAW,IAAIha,CAAC,CAAC8E,KAAK,CAACsE,MAAM,CAAC,CAAC,GAAGpJ,CAAC,CAACgE,SAAS,CAAC,GAAI0W,cAAc;IAC7F;IACA,IAAI1a,CAAC,CAACwG,OAAO,CAACpD,eAAe,KAAK,IAAI,EAAE;MACpCpD,CAAC,CAAC4E,SAAS,GAAG6V,OAAO,GAAGT,WAAW,GAAGU,cAAc;IACxD;IAEA,IAAI1a,CAAC,CAACwG,OAAO,CAAC/E,IAAI,KAAK,IAAI,IAAIzB,CAAC,CAACwG,OAAO,CAAC1D,SAAS,KAAK,KAAK,EAAE;MAC1D,OAAO,KAAK;IAChB;IAEA,IAAI9C,CAAC,CAACwD,SAAS,KAAK,IAAI,EAAE;MACtBxD,CAAC,CAAC4E,SAAS,GAAG,IAAI;MAClB,OAAO,KAAK;IAChB;IAEA5E,CAAC,CAACqW,MAAM,CAACrW,CAAC,CAAC4E,SAAS,CAAC;EAEzB,CAAC;EAEDjF,KAAK,CAACgI,SAAS,CAAC2S,UAAU,GAAG,UAASxM,KAAK,EAAE;IAEzC,IAAI9N,CAAC,GAAG,IAAI;MACRqa,OAAO;IAEXra,CAAC,CAAC0F,WAAW,GAAG,IAAI;IAEpB,IAAI1F,CAAC,CAAC+E,WAAW,CAACoV,WAAW,KAAK,CAAC,IAAIna,CAAC,CAACsE,UAAU,IAAItE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,EAAE;MAC3EzC,CAAC,CAAC+E,WAAW,GAAG,CAAC,CAAC;MAClB,OAAO,KAAK;IAChB;IAEA,IAAI+I,KAAK,CAACsM,aAAa,KAAK1C,SAAS,IAAI5J,KAAK,CAACsM,aAAa,CAACC,OAAO,KAAK3C,SAAS,EAAE;MAChF2C,OAAO,GAAGvM,KAAK,CAACsM,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC;IAC5C;IAEAra,CAAC,CAAC+E,WAAW,CAACyU,MAAM,GAAGxZ,CAAC,CAAC+E,WAAW,CAAC0U,IAAI,GAAGY,OAAO,KAAK3C,SAAS,GAAG2C,OAAO,CAACO,KAAK,GAAG9M,KAAK,CAAC+M,OAAO;IACjG7a,CAAC,CAAC+E,WAAW,CAAC2U,MAAM,GAAG1Z,CAAC,CAAC+E,WAAW,CAAC4U,IAAI,GAAGU,OAAO,KAAK3C,SAAS,GAAG2C,OAAO,CAACS,KAAK,GAAGhN,KAAK,CAACiN,OAAO;IAEjG/a,CAAC,CAACyD,QAAQ,GAAG,IAAI;EAErB,CAAC;EAED9D,KAAK,CAACgI,SAAS,CAACuT,cAAc,GAAGvb,KAAK,CAACgI,SAAS,CAACwT,aAAa,GAAG,YAAW;IAExE,IAAInb,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAACiG,YAAY,KAAK,IAAI,EAAE;MAEzBjG,CAAC,CAACoI,MAAM,CAAC,CAAC;MAEVpI,CAAC,CAACwE,WAAW,CAACmE,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACjE,KAAK,CAAC,CAACqG,MAAM,CAAC,CAAC;MAEnD5I,CAAC,CAACiG,YAAY,CAACqC,QAAQ,CAACtI,CAAC,CAACwE,WAAW,CAAC;MAEtCxE,CAAC,CAAC+I,MAAM,CAAC,CAAC;IAEd;EAEJ,CAAC;EAEDpJ,KAAK,CAACgI,SAAS,CAACS,MAAM,GAAG,YAAW;IAEhC,IAAIpI,CAAC,GAAG,IAAI;IAEZN,CAAC,CAAC,eAAe,EAAEM,CAAC,CAACgG,OAAO,CAAC,CAACwJ,MAAM,CAAC,CAAC;IAEtC,IAAIxP,CAAC,CAAC+D,KAAK,EAAE;MACT/D,CAAC,CAAC+D,KAAK,CAACyL,MAAM,CAAC,CAAC;IACpB;IAEA,IAAIxP,CAAC,CAACoE,UAAU,IAAIpE,CAAC,CAACwH,QAAQ,CAAC4D,IAAI,CAACpL,CAAC,CAACwG,OAAO,CAAC/F,SAAS,CAAC,EAAE;MACtDT,CAAC,CAACoE,UAAU,CAACoL,MAAM,CAAC,CAAC;IACzB;IAEA,IAAIxP,CAAC,CAACmE,UAAU,IAAInE,CAAC,CAACwH,QAAQ,CAAC4D,IAAI,CAACpL,CAAC,CAACwG,OAAO,CAAC9F,SAAS,CAAC,EAAE;MACtDV,CAAC,CAACmE,UAAU,CAACqL,MAAM,CAAC,CAAC;IACzB;IAEAxP,CAAC,CAACyE,OAAO,CACJyG,WAAW,CAAC,sDAAsD,CAAC,CACnEpD,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAC3BmC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;EAEzB,CAAC;EAEDtK,KAAK,CAACgI,SAAS,CAACgG,OAAO,GAAG,UAASyN,cAAc,EAAE;IAE/C,IAAIpb,CAAC,GAAG,IAAI;IACZA,CAAC,CAACgG,OAAO,CAAC6H,OAAO,CAAC,SAAS,EAAE,CAAC7N,CAAC,EAAEob,cAAc,CAAC,CAAC;IACjDpb,CAAC,CAACuP,OAAO,CAAC,CAAC;EAEf,CAAC;EAED5P,KAAK,CAACgI,SAAS,CAAC0K,YAAY,GAAG,YAAW;IAEtC,IAAIrS,CAAC,GAAG,IAAI;MACR0R,YAAY;IAEhBA,YAAY,GAAG3H,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAACwG,OAAO,CAAC/D,YAAY,GAAG,CAAC,CAAC;IAErD,IAAKzC,CAAC,CAACwG,OAAO,CAACjG,MAAM,KAAK,IAAI,IAC1BP,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IACrC,CAACzC,CAAC,CAACwG,OAAO,CAAC5E,QAAQ,EAAG;MAEtB5B,CAAC,CAACoE,UAAU,CAAC8G,WAAW,CAAC,gBAAgB,CAAC,CAACpD,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;MACzE9H,CAAC,CAACmE,UAAU,CAAC+G,WAAW,CAAC,gBAAgB,CAAC,CAACpD,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;MAEzE,IAAI9H,CAAC,CAAC6D,YAAY,KAAK,CAAC,EAAE;QAEtB7D,CAAC,CAACoE,UAAU,CAAC6G,QAAQ,CAAC,gBAAgB,CAAC,CAACnD,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC;QACrE9H,CAAC,CAACmE,UAAU,CAAC+G,WAAW,CAAC,gBAAgB,CAAC,CAACpD,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;MAE7E,CAAC,MAAM,IAAI9H,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACsE,UAAU,GAAGtE,CAAC,CAACwG,OAAO,CAAC/D,YAAY,IAAIzC,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,KAAK,EAAE;QAElGb,CAAC,CAACmE,UAAU,CAAC8G,QAAQ,CAAC,gBAAgB,CAAC,CAACnD,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC;QACrE9H,CAAC,CAACoE,UAAU,CAAC8G,WAAW,CAAC,gBAAgB,CAAC,CAACpD,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;MAE7E,CAAC,MAAM,IAAI9H,CAAC,CAAC6D,YAAY,IAAI7D,CAAC,CAACsE,UAAU,GAAG,CAAC,IAAItE,CAAC,CAACwG,OAAO,CAAC3F,UAAU,KAAK,IAAI,EAAE;QAE5Eb,CAAC,CAACmE,UAAU,CAAC8G,QAAQ,CAAC,gBAAgB,CAAC,CAACnD,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC;QACrE9H,CAAC,CAACoE,UAAU,CAAC8G,WAAW,CAAC,gBAAgB,CAAC,CAACpD,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;MAE7E;IAEJ;EAEJ,CAAC;EAEDnI,KAAK,CAACgI,SAAS,CAACoE,UAAU,GAAG,YAAW;IAEpC,IAAI/L,CAAC,GAAG,IAAI;IAEZ,IAAIA,CAAC,CAAC+D,KAAK,KAAK,IAAI,EAAE;MAElB/D,CAAC,CAAC+D,KAAK,CACF8D,IAAI,CAAC,IAAI,CAAC,CACNqD,WAAW,CAAC,cAAc,CAAC,CAC3B4H,GAAG,CAAC,CAAC;MAEd9S,CAAC,CAAC+D,KAAK,CACF8D,IAAI,CAAC,IAAI,CAAC,CACVW,EAAE,CAACuB,IAAI,CAAC8G,KAAK,CAAC7Q,CAAC,CAAC6D,YAAY,GAAG7D,CAAC,CAACwG,OAAO,CAAC9D,cAAc,CAAC,CAAC,CACzDuI,QAAQ,CAAC,cAAc,CAAC;IAEjC;EAEJ,CAAC;EAEDtL,KAAK,CAACgI,SAAS,CAACqH,UAAU,GAAG,YAAW;IAEpC,IAAIhP,CAAC,GAAG,IAAI;IAEZ,IAAKA,CAAC,CAACwG,OAAO,CAAC7F,QAAQ,EAAG;MAEtB,IAAK+F,QAAQ,CAAC1G,CAAC,CAAC2F,MAAM,CAAC,EAAG;QAEtB3F,CAAC,CAAC0F,WAAW,GAAG,IAAI;MAExB,CAAC,MAAM;QAEH1F,CAAC,CAAC0F,WAAW,GAAG,KAAK;MAEzB;IAEJ;EAEJ,CAAC;EAEDhG,CAAC,CAAC2b,EAAE,CAAC3Q,KAAK,GAAG,YAAW;IACpB,IAAI1K,CAAC,GAAG,IAAI;MACRqX,GAAG,GAAGD,SAAS,CAAC,CAAC,CAAC;MAClBkE,IAAI,GAAGC,KAAK,CAAC5T,SAAS,CAAC2M,KAAK,CAACnK,IAAI,CAACiN,SAAS,EAAE,CAAC,CAAC;MAC/C1B,CAAC,GAAG1V,CAAC,CAACqI,MAAM;MACZnH,CAAC;MACDsa,GAAG;IACP,KAAKta,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwU,CAAC,EAAExU,CAAC,EAAE,EAAE;MACpB,IAAI,OAAOmW,GAAG,IAAI,QAAQ,IAAI,OAAOA,GAAG,IAAI,WAAW,EACnDrX,CAAC,CAACkB,CAAC,CAAC,CAACwJ,KAAK,GAAG,IAAI/K,KAAK,CAACK,CAAC,CAACkB,CAAC,CAAC,EAAEmW,GAAG,CAAC,CAAC,KAElCmE,GAAG,GAAGxb,CAAC,CAACkB,CAAC,CAAC,CAACwJ,KAAK,CAAC2M,GAAG,CAAC,CAACoE,KAAK,CAACzb,CAAC,CAACkB,CAAC,CAAC,CAACwJ,KAAK,EAAE4Q,IAAI,CAAC;MACjD,IAAI,OAAOE,GAAG,IAAI,WAAW,EAAE,OAAOA,GAAG;IAC7C;IACA,OAAOxb,CAAC;EACZ,CAAC;AAEL,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
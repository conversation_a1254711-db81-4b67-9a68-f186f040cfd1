{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function takeUntil(notifier) {\n  return source => source.lift(new TakeUntilOperator(notifier));\n}\nclass TakeUntilOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n  call(subscriber, source) {\n    const takeUntilSubscriber = new TakeUntilSubscriber(subscriber);\n    const notifierSubscription = subscribeToResult(takeUntilSubscriber, this.notifier);\n    if (notifierSubscription && !takeUntilSubscriber.seenValue) {\n      takeUntilSubscriber.add(notifierSubscription);\n      return source.subscribe(takeUntilSubscriber);\n    }\n    return takeUntilSubscriber;\n  }\n}\nclass TakeUntilSubscriber extends OuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.seenValue = false;\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.seenValue = true;\n    this.complete();\n  }\n  notifyComplete() {}\n}", "map": {"version": 3, "names": ["OuterSubscriber", "subscribeToResult", "takeUntil", "notifier", "source", "lift", "TakeUntilOperator", "constructor", "call", "subscriber", "takeUntilSubscriber", "TakeUntilSubscriber", "notifierSubscription", "seenValue", "add", "subscribe", "destination", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "complete", "notifyComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/takeUntil.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function takeUntil(notifier) {\n    return (source) => source.lift(new TakeUntilOperator(notifier));\n}\nclass TakeUntilOperator {\n    constructor(notifier) {\n        this.notifier = notifier;\n    }\n    call(subscriber, source) {\n        const takeUntilSubscriber = new TakeUntilSubscriber(subscriber);\n        const notifierSubscription = subscribeToResult(takeUntilSubscriber, this.notifier);\n        if (notifierSubscription && !takeUntilSubscriber.seenValue) {\n            takeUntilSubscriber.add(notifierSubscription);\n            return source.subscribe(takeUntilSubscriber);\n        }\n        return takeUntilSubscriber;\n    }\n}\nclass TakeUntilSubscriber extends OuterSubscriber {\n    constructor(destination) {\n        super(destination);\n        this.seenValue = false;\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.seenValue = true;\n        this.complete();\n    }\n    notifyComplete() {\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAE;EAChC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACH,QAAQ,CAAC,CAAC;AACnE;AACA,MAAMG,iBAAiB,CAAC;EACpBC,WAAWA,CAACJ,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,MAAMM,mBAAmB,GAAG,IAAIC,mBAAmB,CAACF,UAAU,CAAC;IAC/D,MAAMG,oBAAoB,GAAGX,iBAAiB,CAACS,mBAAmB,EAAE,IAAI,CAACP,QAAQ,CAAC;IAClF,IAAIS,oBAAoB,IAAI,CAACF,mBAAmB,CAACG,SAAS,EAAE;MACxDH,mBAAmB,CAACI,GAAG,CAACF,oBAAoB,CAAC;MAC7C,OAAOR,MAAM,CAACW,SAAS,CAACL,mBAAmB,CAAC;IAChD;IACA,OAAOA,mBAAmB;EAC9B;AACJ;AACA,MAAMC,mBAAmB,SAASX,eAAe,CAAC;EAC9CO,WAAWA,CAACS,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;IAClB,IAAI,CAACH,SAAS,GAAG,KAAK;EAC1B;EACAI,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACT,SAAS,GAAG,IAAI;IACrB,IAAI,CAACU,QAAQ,CAAC,CAAC;EACnB;EACAC,cAAcA,CAAA,EAAG,CACjB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
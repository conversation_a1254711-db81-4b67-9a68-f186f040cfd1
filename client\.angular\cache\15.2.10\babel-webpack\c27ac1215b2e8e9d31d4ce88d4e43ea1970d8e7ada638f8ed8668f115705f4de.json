{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DotStringPipe {\n  transform(value, limit) {\n    return value.length > limit ? value.substring(0, limit) + '...' : value;\n  }\n  static #_ = this.ɵfac = function DotStringPipe_Factory(t) {\n    return new (t || DotStringPipe)();\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"dotstring\",\n    type: DotStringPipe,\n    pure: true\n  });\n}", "map": {"version": 3, "mappings": ";AAMA,OAAM,MAAOA,aAAa;EAEtBC,SAASA,CAACC,KAAa,EAAEC,KAAa;IAClC,OAAOD,KAAK,CAACE,MAAM,GAAGD,KAAK,GAAGD,KAAK,CAACG,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC,GAAG,KAAK,GAAGD,KAAK;EAC3E;EAAC,QAAAI,CAAA;qBAJQN,aAAa;EAAA;EAAA,QAAAO,EAAA;;UAAbP,aAAa;IAAAQ,IAAA;EAAA", "names": ["DotStringPipe", "transform", "value", "limit", "length", "substring", "_", "_2", "pure"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\pipes\\dotstring.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from \"@angular/core\";\r\n\r\n@Pipe({\r\n    name: 'dotstring'\r\n})\r\n\r\nexport class DotStringPipe implements PipeTransform {\r\n\r\n    transform(value: string, limit: number): string {\r\n        return value.length > limit ? value.substring(0, limit) + '...' : value;\r\n    }\r\n}"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
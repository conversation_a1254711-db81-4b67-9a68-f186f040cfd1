{"ast": null, "code": "export const pad = (number, length = 2) => `000${number}`.slice(length * -1);\nexport const int = bool => bool === true ? 1 : 0;\nexport function debounce(fn, wait) {\n  let t;\n  return function () {\n    clearTimeout(t);\n    t = setTimeout(() => fn.apply(this, arguments), wait);\n  };\n}\nexport const arrayify = obj => obj instanceof Array ? obj : [obj];", "map": {"version": 3, "names": ["pad", "number", "length", "slice", "int", "bool", "debounce", "fn", "wait", "t", "clearTimeout", "setTimeout", "apply", "arguments", "arrayify", "obj", "Array"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/flatpickr/dist/esm/utils/index.js"], "sourcesContent": ["export const pad = (number, length = 2) => `000${number}`.slice(length * -1);\nexport const int = (bool) => (bool === true ? 1 : 0);\nexport function debounce(fn, wait) {\n    let t;\n    return function () {\n        clearTimeout(t);\n        t = setTimeout(() => fn.apply(this, arguments), wait);\n    };\n}\nexport const arrayify = (obj) => obj instanceof Array ? obj : [obj];\n"], "mappings": "AAAA,OAAO,MAAMA,GAAG,GAAGA,CAACC,MAAM,EAAEC,MAAM,GAAG,CAAC,KAAM,MAAKD,MAAO,EAAC,CAACE,KAAK,CAACD,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5E,OAAO,MAAME,GAAG,GAAIC,IAAI,IAAMA,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAE;AACpD,OAAO,SAASC,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC/B,IAAIC,CAAC;EACL,OAAO,YAAY;IACfC,YAAY,CAACD,CAAC,CAAC;IACfA,CAAC,GAAGE,UAAU,CAAC,MAAMJ,EAAE,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,EAAEL,IAAI,CAAC;EACzD,CAAC;AACL;AACA,OAAO,MAAMM,QAAQ,GAAIC,GAAG,IAAKA,GAAG,YAAYC,KAAK,GAAGD,GAAG,GAAG,CAACA,GAAG,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
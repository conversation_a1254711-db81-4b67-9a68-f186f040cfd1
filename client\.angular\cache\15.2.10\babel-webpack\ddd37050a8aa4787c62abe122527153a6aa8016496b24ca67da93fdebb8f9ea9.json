{"ast": null, "code": "import toString from './toString.js';\nimport upperFirst from './upperFirst.js';\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\nexport default capitalize;", "map": {"version": 3, "names": ["toString", "upperFirst", "capitalize", "string", "toLowerCase"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/capitalize.js"], "sourcesContent": ["import toString from './toString.js';\nimport upperFirst from './upperFirst.js';\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\n\nexport default capitalize;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,iBAAiB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOF,UAAU,CAACD,QAAQ,CAACG,MAAM,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AACnD;AAEA,eAAeF,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
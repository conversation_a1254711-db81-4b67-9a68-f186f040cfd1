{"ast": null, "code": "import { PushNotifications } from '@capacitor/push-notifications';\nimport { Capacitor } from '@capacitor/core';\nimport { BehaviorSubject } from 'rxjs';\nimport firebase from 'firebase/compat/app';\nimport 'firebase/compat/messaging';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./user.service\";\nexport class FcmService {\n  constructor(router, _userService) {\n    this.router = router;\n    this._userService = _userService;\n    this.isPushNotificationsAvailable = Capacitor.isPluginAvailable('PushNotifications');\n    this.currentMessage = new BehaviorSubject(null);\n  }\n  initPush() {\n    if (this.isPushNotificationsAvailable) {\n      this.registerPush();\n    } else {\n      this.initWebPush();\n    }\n  }\n  initWebPush() {\n    const firebaseConfig = environment.firebase;\n    if (!firebase.apps.length) {\n      firebase.initializeApp(firebaseConfig);\n    }\n    const messaging = firebase.messaging();\n    // Request permission using the Notification API\n    Notification.requestPermission().then(permission => {\n      if (permission === 'granted') {\n        console.log('Notification permission granted.');\n        return messaging.getToken({\n          vapidKey: environment.firebase.vapidKey\n        });\n      } else {\n        console.warn('Notification permission not granted.');\n        return null;\n      }\n    }).then(token => {\n      if (token) {\n        localStorage.setItem('fcm_token', token);\n        this._userService.update({\n          firebase_token: token\n        }).subscribe(res => console.log('User token updated on the server:', res), err => console.error('Failed to update user token:', err));\n      }\n    }).catch(err => console.error('Unable to get permission for notifications:', err));\n    // Handle incoming messages\n    messaging.onMessage(payload => {\n      console.log('Web push notification received:', payload);\n      this.currentMessage.next(payload);\n      // Display the notification manually\n      if (Notification.permission === 'granted' && payload.notification) {\n        new Notification(payload.notification.title, {\n          body: payload.notification.body,\n          icon: payload.notification.icon\n        });\n      } else {\n        console.warn('Notification payload is missing or permission not granted.');\n      }\n    });\n  }\n  registerPush() {\n    console.log('Initializing native push notifications');\n    PushNotifications.requestPermissions().then(result => {\n      if (result.receive === 'granted') {\n        console.log('Push notification permission granted');\n        PushNotifications.register();\n        PushNotifications.addListener('registration', token => {\n          console.log('Push registration success, token:', token.value);\n          localStorage.setItem('fcm_token', token.value);\n          this._userService.update({\n            firebase_token: token.value\n          }).subscribe(res => console.log('User token updated on the server:', res), err => console.error('Failed to update user token:', err));\n        });\n        PushNotifications.addListener('registrationError', error => {\n          console.error('Push registration error:', error);\n        });\n        PushNotifications.addListener('pushNotificationReceived', notification => {\n          console.log('Push notification received:', notification);\n          this.currentMessage.next(notification);\n        });\n        PushNotifications.addListener('pushNotificationActionPerformed', notification => {\n          console.log('Push action performed:', notification);\n          const data = notification.notification.data;\n          if (data && data.go_url) {\n            console.log('Navigating to URL:', data.go_url);\n            this.router.navigate([data.go_url]).then(() => {\n              setTimeout(() => window.location.reload(), 200);\n            });\n          }\n        });\n      } else {\n        console.warn('Push notification permission denied');\n      }\n    });\n  }\n  getToken() {\n    return localStorage.getItem('fcm_token');\n  }\n  listenToMessages() {\n    console.log('Listening to incoming messages');\n    this.currentMessage.subscribe(message => {\n      console.log('New message received:', message);\n    });\n  }\n  static #_ = this.ɵfac = function FcmService_Factory(t) {\n    return new (t || FcmService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.UserService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: FcmService,\n    factory: FcmService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AACA,SACEA,iBAAiB,QAGZ,+BAA+B;AACtC,SAASC,SAAS,QAAQ,iBAAiB;AAE3C,SAASC,eAAe,QAAQ,MAAM;AAGtC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAO,2BAA2B;AAClC,SAASC,WAAW,QAAQ,0BAA0B;;;;AAKtD,OAAM,MAAOC,UAAU;EAKrBC,YAAmBC,MAAc,EAAUC,YAAyB;IAAjD,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,YAAY,GAAZA,YAAY;IAJvD,KAAAC,4BAA4B,GAC1BR,SAAS,CAACS,iBAAiB,CAAC,mBAAmB,CAAC;IAClD,KAAAC,cAAc,GAAG,IAAIT,eAAe,CAAgC,IAAI,CAAC;EAED;EAExEU,QAAQA,CAAA;IACN,IAAI,IAAI,CAACH,4BAA4B,EAAE;MACrC,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACC,WAAW,EAAE;;EAEtB;EAEQA,WAAWA,CAAA;IACjB,MAAMC,cAAc,GAAGX,WAAW,CAACD,QAAQ;IAE3C,IAAI,CAACA,QAAQ,CAACa,IAAI,CAACC,MAAM,EAAE;MACzBd,QAAQ,CAACe,aAAa,CAACH,cAAc,CAAC;;IAGxC,MAAMI,SAAS,GAAGhB,QAAQ,CAACgB,SAAS,EAAE;IAEtC;IACAC,YAAY,CAACC,iBAAiB,EAAE,CAC7BC,IAAI,CAAEC,UAAU,IAAI;MACnB,IAAIA,UAAU,KAAK,SAAS,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAON,SAAS,CAACO,QAAQ,CAAC;UACxBC,QAAQ,EAAEvB,WAAW,CAACD,QAAQ,CAACwB;SAChC,CAAC;OACH,MAAM;QACLH,OAAO,CAACI,IAAI,CAAC,sCAAsC,CAAC;QACpD,OAAO,IAAI;;IAEf,CAAC,CAAC,CACDN,IAAI,CAAEO,KAAK,IAAI;MACd,IAAIA,KAAK,EAAE;QACTC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC;QACxC,IAAI,CAACrB,YAAY,CAACwB,MAAM,CAAC;UAAEC,cAAc,EAAEJ;QAAK,CAAE,CAAC,CAACK,SAAS,CAC1DC,GAAG,IAAKX,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEU,GAAG,CAAC,EAC7DC,GAAG,IAAKZ,OAAO,CAACa,KAAK,CAAC,8BAA8B,EAAED,GAAG,CAAC,CAC5D;;IAEL,CAAC,CAAC,CACDE,KAAK,CAAEF,GAAG,IACTZ,OAAO,CAACa,KAAK,CAAC,6CAA6C,EAAED,GAAG,CAAC,CAClE;IAEH;IACAjB,SAAS,CAACoB,SAAS,CAAEC,OAAO,IAAI;MAC9BhB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEe,OAAO,CAAC;MACvD,IAAI,CAAC7B,cAAc,CAAC8B,IAAI,CAACD,OAAO,CAAC;MAEjC;MACA,IAAIpB,YAAY,CAACG,UAAU,KAAK,SAAS,IAAIiB,OAAO,CAACE,YAAY,EAAE;QACjE,IAAItB,YAAY,CAACoB,OAAO,CAACE,YAAY,CAACC,KAAK,EAAE;UAC3CC,IAAI,EAAEJ,OAAO,CAACE,YAAY,CAACE,IAAI;UAC/BC,IAAI,EAAEL,OAAO,CAACE,YAAY,CAACG;SAC5B,CAAC;OACH,MAAM;QACLrB,OAAO,CAACI,IAAI,CAAC,4DAA4D,CAAC;;IAE9E,CAAC,CAAC;EACJ;EAEQf,YAAYA,CAAA;IAClBW,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAErDzB,iBAAiB,CAAC8C,kBAAkB,EAAE,CAACxB,IAAI,CAAEyB,MAAM,IAAI;MACrD,IAAIA,MAAM,CAACC,OAAO,KAAK,SAAS,EAAE;QAChCxB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnDzB,iBAAiB,CAACiD,QAAQ,EAAE;QAE5BjD,iBAAiB,CAACkD,WAAW,CAAC,cAAc,EAAGrB,KAAK,IAAI;UACtDL,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEI,KAAK,CAACsB,KAAK,CAAC;UAC7DrB,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAACsB,KAAK,CAAC;UAC9C,IAAI,CAAC3C,YAAY,CAACwB,MAAM,CAAC;YAAEC,cAAc,EAAEJ,KAAK,CAACsB;UAAK,CAAE,CAAC,CAACjB,SAAS,CAChEC,GAAG,IAAKX,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEU,GAAG,CAAC,EAC7DC,GAAG,IAAKZ,OAAO,CAACa,KAAK,CAAC,8BAA8B,EAAED,GAAG,CAAC,CAC5D;QACH,CAAC,CAAC;QAEFpC,iBAAiB,CAACkD,WAAW,CAAC,mBAAmB,EAAGb,KAAK,IAAI;UAC3Db,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD,CAAC,CAAC;QAEFrC,iBAAiB,CAACkD,WAAW,CAC3B,0BAA0B,EACzBR,YAAoC,IAAI;UACvClB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEiB,YAAY,CAAC;UACxD,IAAI,CAAC/B,cAAc,CAAC8B,IAAI,CAACC,YAAY,CAAC;QACxC,CAAC,CACF;QAED1C,iBAAiB,CAACkD,WAAW,CAC3B,iCAAiC,EAChCR,YAA6B,IAAI;UAChClB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiB,YAAY,CAAC;UACnD,MAAMU,IAAI,GAAGV,YAAY,CAACA,YAAY,CAACU,IAAI;UAC3C,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,EAAE;YACvB7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2B,IAAI,CAACC,MAAM,CAAC;YAC9C,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC/B,IAAI,CAAC,MAAK;cAC5CiC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE,EAAE,GAAG,CAAC;YACjD,CAAC,CAAC;;QAEN,CAAC,CACF;OACF,MAAM;QACLlC,OAAO,CAACI,IAAI,CAAC,qCAAqC,CAAC;;IAEvD,CAAC,CAAC;EACJ;EAEAF,QAAQA,CAAA;IACN,OAAOI,YAAY,CAAC6B,OAAO,CAAC,WAAW,CAAC;EAC1C;EAEAC,gBAAgBA,CAAA;IACdpC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAI,CAACd,cAAc,CAACuB,SAAS,CAAE2B,OAAO,IAAI;MACxCrC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoC,OAAO,CAAC;IAC/C,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA;qBA5HUzD,UAAU,EAAA0D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;WAAVhE,UAAU;IAAAiE,OAAA,EAAVjE,UAAU,CAAAkE,IAAA;IAAAC,UAAA,EAFT;EAAM", "names": ["PushNotifications", "Capacitor", "BehaviorSubject", "firebase", "environment", "FcmService", "constructor", "router", "_userService", "isPushNotificationsAvailable", "isPluginAvailable", "currentMessage", "initPush", "registerPush", "initWebPush", "firebaseConfig", "apps", "length", "initializeApp", "messaging", "Notification", "requestPermission", "then", "permission", "console", "log", "getToken", "vapid<PERSON>ey", "warn", "token", "localStorage", "setItem", "update", "firebase_token", "subscribe", "res", "err", "error", "catch", "onMessage", "payload", "next", "notification", "title", "body", "icon", "requestPermissions", "result", "receive", "register", "addListener", "value", "data", "go_url", "navigate", "setTimeout", "window", "location", "reload", "getItem", "listenToMessages", "message", "_", "i0", "ɵɵinject", "i1", "Router", "i2", "UserService", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\fcm.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  PushNotifications,\r\n  PushNotificationSchema,\r\n  ActionPerformed,\r\n} from '@capacitor/push-notifications';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { UserService } from './user.service';\r\nimport { Platform } from '@angular/cdk/platform';\r\nimport firebase from 'firebase/compat/app';\r\nimport 'firebase/compat/messaging';\r\nimport { environment } from 'environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FcmService {\r\n  isPushNotificationsAvailable =\r\n    Capacitor.isPluginAvailable('PushNotifications');\r\n  currentMessage = new BehaviorSubject<PushNotificationSchema | null>(null);\r\n\r\n  constructor(public router: Router, private _userService: UserService) { }\r\n\r\n  initPush() {\r\n    if (this.isPushNotificationsAvailable) {\r\n      this.registerPush();\r\n    } else {\r\n      this.initWebPush();\r\n    }\r\n  }\r\n\r\n  private initWebPush() {\r\n    const firebaseConfig = environment.firebase;\r\n\r\n    if (!firebase.apps.length) {\r\n      firebase.initializeApp(firebaseConfig);\r\n    }\r\n\r\n    const messaging = firebase.messaging();\r\n\r\n    // Request permission using the Notification API\r\n    Notification.requestPermission()\r\n      .then((permission) => {\r\n        if (permission === 'granted') {\r\n          console.log('Notification permission granted.');\r\n          return messaging.getToken({\r\n            vapidKey: environment.firebase.vapidKey,\r\n          });\r\n        } else {\r\n          console.warn('Notification permission not granted.');\r\n          return null;\r\n        }\r\n      })\r\n      .then((token) => {\r\n        if (token) {\r\n          localStorage.setItem('fcm_token', token);\r\n          this._userService.update({ firebase_token: token }).subscribe(\r\n            (res) => console.log('User token updated on the server:', res),\r\n            (err) => console.error('Failed to update user token:', err)\r\n          );\r\n        }\r\n      })\r\n      .catch((err) =>\r\n        console.error('Unable to get permission for notifications:', err)\r\n      );\r\n\r\n    // Handle incoming messages\r\n    messaging.onMessage((payload) => {\r\n      console.log('Web push notification received:', payload);\r\n      this.currentMessage.next(payload);\r\n\r\n      // Display the notification manually\r\n      if (Notification.permission === 'granted' && payload.notification) {\r\n        new Notification(payload.notification.title, {\r\n          body: payload.notification.body,\r\n          icon: payload.notification.icon,\r\n        });\r\n      } else {\r\n        console.warn('Notification payload is missing or permission not granted.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private registerPush() {\r\n    console.log('Initializing native push notifications');\r\n\r\n    PushNotifications.requestPermissions().then((result) => {\r\n      if (result.receive === 'granted') {\r\n        console.log('Push notification permission granted');\r\n        PushNotifications.register();\r\n\r\n        PushNotifications.addListener('registration', (token) => {\r\n          console.log('Push registration success, token:', token.value);\r\n          localStorage.setItem('fcm_token', token.value);\r\n          this._userService.update({ firebase_token: token.value }).subscribe(\r\n            (res) => console.log('User token updated on the server:', res),\r\n            (err) => console.error('Failed to update user token:', err)\r\n          );\r\n        });\r\n\r\n        PushNotifications.addListener('registrationError', (error) => {\r\n          console.error('Push registration error:', error);\r\n        });\r\n\r\n        PushNotifications.addListener(\r\n          'pushNotificationReceived',\r\n          (notification: PushNotificationSchema) => {\r\n            console.log('Push notification received:', notification);\r\n            this.currentMessage.next(notification);\r\n          }\r\n        );\r\n\r\n        PushNotifications.addListener(\r\n          'pushNotificationActionPerformed',\r\n          (notification: ActionPerformed) => {\r\n            console.log('Push action performed:', notification);\r\n            const data = notification.notification.data;\r\n            if (data && data.go_url) {\r\n              console.log('Navigating to URL:', data.go_url);\r\n              this.router.navigate([data.go_url]).then(() => {\r\n                setTimeout(() => window.location.reload(), 200);\r\n              });\r\n            }\r\n          }\r\n        );\r\n      } else {\r\n        console.warn('Push notification permission denied');\r\n      }\r\n    });\r\n  }\r\n\r\n  getToken(): string | null {\r\n    return localStorage.getItem('fcm_token');\r\n  }\r\n\r\n  listenToMessages() {\r\n    console.log('Listening to incoming messages');\r\n    this.currentMessage.subscribe((message) => {\r\n      console.log('New message received:', message);\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
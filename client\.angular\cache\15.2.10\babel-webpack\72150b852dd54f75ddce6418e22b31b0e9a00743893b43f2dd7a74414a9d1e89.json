{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../overlays.service\";\nimport * as i2 from \"app/services/livekit.service\";\nimport * as i3 from \"@angular/common\";\nfunction SponsorsOverlayComponent_div_0_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.images[0].src, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.images[0].src);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"top\": a0,\n    \"left\": a1\n  };\n};\nfunction SponsorsOverlayComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵtemplate(4, SponsorsOverlayComponent_div_0_img_4_Template, 1, 2, \"img\", 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(2, _c0, (ctx_r0.roomMetadata == null ? null : ctx_r0.roomMetadata.overlay_data == null ? null : ctx_r0.roomMetadata.overlay_data.sponsor_position) ? (ctx_r0.roomMetadata == null ? null : ctx_r0.roomMetadata.overlay_data == null ? null : ctx_r0.roomMetadata.overlay_data.sponsor_position == null ? null : ctx_r0.roomMetadata.overlay_data.sponsor_position.y) + \"%\" : \"83%\", (ctx_r0.roomMetadata == null ? null : ctx_r0.roomMetadata.overlay_data == null ? null : ctx_r0.roomMetadata.overlay_data.sponsor_position) ? (ctx_r0.roomMetadata == null ? null : ctx_r0.roomMetadata.overlay_data == null ? null : ctx_r0.roomMetadata.overlay_data.sponsor_position == null ? null : ctx_r0.roomMetadata.overlay_data.sponsor_position.x) + \"%\" : \"0\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.images.length > 0);\n  }\n}\nexport class SponsorsOverlayComponent {\n  constructor(overlayService, livekitService) {\n    this.overlayService = overlayService;\n    this.livekitService = livekitService;\n    this.images = [];\n    livekitService.roomMetadataSubject.subscribe(roomMetadata => {\n      if (roomMetadata) {\n        this.roomMetadata = roomMetadata;\n        if (roomMetadata && roomMetadata?.overlay_data?.sponsors_logo) {\n          this.images = roomMetadata.overlay_data.sponsors_logo;\n        }\n      }\n    });\n  }\n  ngOnInit() {}\n  ngAfterViewInit() {\n    // flip each sponsor image in images after 5 seconds\n    let i = 0;\n    setInterval(() => {\n      if (this.images.length > 0 && this.images.length >= i && this.roomMetadata?.overlay_data?.is_show_sponsor_logo) {\n        this.flipSponsors(this.images[i]);\n        i = (i + 1) % this.images.length;\n      }\n    }, 5000);\n  }\n  // flip sponsor images\n  flipSponsors(imgage) {\n    let sponsors_container = document.getElementById('sponsors-images-container');\n    // add class flip-360 to the container\n    sponsors_container.classList.add('flip-180');\n    let img = sponsors_container.getElementsByTagName('img');\n    // remove class flip-360 from the container after 1 second\n    setTimeout(() => {\n      img[0].src = imgage.src;\n      img[0].alt = imgage.alt;\n      sponsors_container.classList.remove('flip-180');\n    }, 500);\n  }\n  static #_ = this.ɵfac = function SponsorsOverlayComponent_Factory(t) {\n    return new (t || SponsorsOverlayComponent)(i0.ɵɵdirectiveInject(i1.OverlaysService), i0.ɵɵdirectiveInject(i2.LivekitService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SponsorsOverlayComponent,\n    selectors: [[\"sponsors-overlay\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"sponsors-overlay\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"sponsors-overlay\", 3, \"ngStyle\"], [1, \"sponsors-overlay__content\", \"text-center\"], [1, \"sponsors-overlay-body\"], [\"id\", \"sponsors-images-container\", 1, \"sponsors-overlay-images\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [3, \"src\", \"alt\"]],\n    template: function SponsorsOverlayComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, SponsorsOverlayComponent_div_0_Template, 5, 5, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.roomMetadata == null ? null : ctx.roomMetadata.overlay_data == null ? null : ctx.roomMetadata.overlay_data.is_show_sponsor_logo);\n      }\n    },\n    dependencies: [i3.NgIf, i3.NgStyle],\n    styles: [\".sponsors-overlay[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  position: absolute;\\n  z-index: 1;\\n  pointer-events: none;\\n  display: flex;\\n  color: #fff;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .sponsors-overlay[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   .sponsors-overlay-images[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 5px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 150px;\\n  height: 150px;\\n  overflow: hidden;\\n  transition: all 1s ease-in-out;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   .sponsors-overlay-images.flip-180[_ngcontent-%COMP%] {\\n  transform: rotateY(180deg);\\n  transition: all 1s ease-in-out;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   .sponsors-overlay-images.flip-360[_ngcontent-%COMP%] {\\n  transform: rotateY(360deg);\\n  transition: all 1s ease-in-out;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   .sponsors-overlay-images[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: contain;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   .sponsors-show[_ngcontent-%COMP%] {\\n  display: flex;\\n  animation: _ngcontent-%COMP%_fadeInCenter 0.5s ease-in-out;\\n  transition: all 1s ease-in-out;\\n}\\n.sponsors-overlay[_ngcontent-%COMP%]   .sponsors-hide[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeOutBackward 0.5s ease-in-out;\\n  transition: all 1s ease-in-out;\\n  display: none;\\n}\\n@keyframes _ngcontent-%COMP%_fadeInCenter {\\n  0% {\\n    opacity: 0;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeOutBackward {\\n  0% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  100% {\\n    opacity: 0;\\n    transform: scale(0.6);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;IAUgBA,EAAA,CAAAC,SAAA,aAA6E;;;;IAA9CD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,MAAA,IAAAC,GAAA,EAAAL,EAAA,CAAAM,aAAA,CAAqB,QAAAH,MAAA,CAAAC,MAAA,IAAAC,GAAA;;;;;;;;;;;IATpEL,EAAA,CAAAO,cAAA,aAE0H;IAO1GP,EAAA,CAAAQ,UAAA,IAAAC,6CAAA,iBAA6E;IACjFT,EAAA,CAAAU,YAAA,EAAM;;;;IATdV,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAW,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,KAAAH,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,kBAAAH,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,CAAA,kBAAAJ,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,KAAAH,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,kBAAAH,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAE,CAAA,eACqH;IAOnGlB,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAE,UAAA,SAAAW,MAAA,CAAAT,MAAA,CAAAgB,MAAA,KAAuB;;;ACA7C,OAAM,MAAOC,wBAAwB;EAGnCC,YACSC,cAA+B,EAC/BC,cAA8B;IAD9B,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAHvB,KAAApB,MAAM,GAAQ,EAAE;IAKdoB,cAAc,CAACC,mBAAmB,CAACC,SAAS,CAAEZ,YAAY,IAAI;MAC5D,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAIA,YAAY,IAAIA,YAAY,EAAEC,YAAY,EAAEY,aAAa,EAAE;UAC7D,IAAI,CAACvB,MAAM,GAAGU,YAAY,CAACC,YAAY,CAACY,aAAa;;;IAG3D,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA,GAAI;EACZC,eAAeA,CAAA;IACb;IACA,IAAIC,CAAC,GAAG,CAAC;IACTC,WAAW,CAAC,MAAK;MACf,IACE,IAAI,CAAC3B,MAAM,CAACgB,MAAM,GAAG,CAAC,IACtB,IAAI,CAAChB,MAAM,CAACgB,MAAM,IAAIU,CAAC,IACvB,IAAI,CAAChB,YAAY,EAAEC,YAAY,EAAEiB,oBAAoB,EACrD;QACA,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC7B,MAAM,CAAC0B,CAAC,CAAC,CAAC;QACjCA,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC1B,MAAM,CAACgB,MAAM;;IAEpC,CAAC,EAAE,IAAI,CAAC;EACV;EACA;EACAa,YAAYA,CAACC,MAAW;IACtB,IAAIC,kBAAkB,GAAGC,QAAQ,CAACC,cAAc,CAC9C,2BAA2B,CAC5B;IACD;IACAF,kBAAkB,CAACG,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IAC5C,IAAIC,GAAG,GAAGL,kBAAkB,CAACM,oBAAoB,CAAC,KAAK,CAAC;IACxD;IACAC,UAAU,CAAC,MAAK;MACdF,GAAG,CAAC,CAAC,CAAC,CAACnC,GAAG,GAAG6B,MAAM,CAAC7B,GAAG;MACvBmC,GAAG,CAAC,CAAC,CAAC,CAACG,GAAG,GAAGT,MAAM,CAACS,GAAG;MACvBR,kBAAkB,CAACG,SAAS,CAACM,MAAM,CAAC,UAAU,CAAC;IACjD,CAAC,EAAE,GAAG,CAAC;EACT;EAAC,QAAAC,CAAA;qBA9CUxB,wBAAwB,EAAArB,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAAxB9B,wBAAwB;IAAA+B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDTrC1D,EAAA,CAAAQ,UAAA,IAAAoD,uCAAA,iBAaM;;;QAbyB5D,EAAA,CAAAE,UAAA,SAAAyD,GAAA,CAAA7C,YAAA,kBAAA6C,GAAA,CAAA7C,YAAA,CAAAC,YAAA,kBAAA4C,GAAA,CAAA7C,YAAA,CAAAC,YAAA,CAAAiB,oBAAA,CAAsD", "names": ["i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "images", "src", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "SponsorsOverlayComponent_div_0_img_4_Template", "ɵɵelementEnd", "ɵɵpureFunction2", "_c0", "ctx_r0", "roomMetadata", "overlay_data", "sponsor_position", "y", "x", "ɵɵadvance", "length", "SponsorsOverlayComponent", "constructor", "overlayService", "livekitService", "roomMetadataSubject", "subscribe", "sponsors_logo", "ngOnInit", "ngAfterViewInit", "i", "setInterval", "is_show_sponsor_logo", "flipSponsors", "imgage", "sponsors_container", "document", "getElementById", "classList", "add", "img", "getElementsByTagName", "setTimeout", "alt", "remove", "_", "ɵɵdirectiveInject", "i1", "OverlaysService", "i2", "LivekitService", "_2", "selectors", "decls", "vars", "consts", "template", "SponsorsOverlayComponent_Template", "rf", "ctx", "SponsorsOverlayComponent_div_0_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\sponsors-overlay\\sponsors-overlay.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\sponsors-overlay\\sponsors-overlay.component.ts"], "sourcesContent": ["<!-- show image (get on internet to test) at bottom left -->\r\n<div class=\"sponsors-overlay\" *ngIf=\"roomMetadata?.overlay_data?.is_show_sponsor_logo\"\r\n    [ngStyle]=\"{'top': roomMetadata?.overlay_data?.sponsor_position ? roomMetadata?.overlay_data?.sponsor_position?.y + '%' : '83%',\r\n    'left': roomMetadata?.overlay_data?.sponsor_position ? roomMetadata?.overlay_data?.sponsor_position?.x + '%' : '0' }\">\r\n    <div class=\" sponsors-overlay__content text-center\">\r\n        <!-- <div class=\"sponsors-overlay__content__header\">\r\n            <h1>Sponsors</h1>\r\n        </div> -->\r\n        <div class=\"sponsors-overlay-body\">\r\n            <div class=\"sponsors-overlay-images\" id=\"sponsors-images-container\">\r\n                <img *ngIf=\"images.length > 0\" [src]=\"images[0].src\" [alt]=\"images[0].src\" />\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", "import { Component } from '@angular/core';\r\nimport { OverlaysService } from '../overlays.service';\r\nimport { LivekitService, RoomMetadata } from 'app/services/livekit.service';\r\ndeclare var $: any;\r\ndeclare var window: any;\r\n@Component({\r\n  selector: 'sponsors-overlay',\r\n  templateUrl: './sponsors-overlay.component.html',\r\n  styleUrls: ['./sponsors-overlay.component.scss'],\r\n})\r\nexport class SponsorsOverlayComponent {\r\n  roomMetadata: RoomMetadata;\r\n  images: any = [];\r\n  constructor(\r\n    public overlayService: OverlaysService,\r\n    public livekitService: LivekitService\r\n  ) {\r\n    livekitService.roomMetadataSubject.subscribe((roomMetadata) => {\r\n      if (roomMetadata) {\r\n        this.roomMetadata = roomMetadata;\r\n        if (roomMetadata && roomMetadata?.overlay_data?.sponsors_logo) {\r\n          this.images = roomMetadata.overlay_data.sponsors_logo;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit() {}\r\n  ngAfterViewInit() {\r\n    // flip each sponsor image in images after 5 seconds\r\n    let i = 0;\r\n    setInterval(() => {\r\n      if (\r\n        this.images.length > 0 &&\r\n        this.images.length >= i &&\r\n        this.roomMetadata?.overlay_data?.is_show_sponsor_logo\r\n      ) {\r\n        this.flipSponsors(this.images[i]);\r\n        i = (i + 1) % this.images.length;\r\n      }\r\n    }, 5000);\r\n  }\r\n  // flip sponsor images\r\n  flipSponsors(imgage: any) {\r\n    let sponsors_container = document.getElementById(\r\n      'sponsors-images-container'\r\n    );\r\n    // add class flip-360 to the container\r\n    sponsors_container.classList.add('flip-180');\r\n    let img = sponsors_container.getElementsByTagName('img');\r\n    // remove class flip-360 from the container after 1 second\r\n    setTimeout(() => {\r\n      img[0].src = imgage.src;\r\n      img[0].alt = imgage.alt;\r\n      sponsors_container.classList.remove('flip-180');\r\n    }, 500);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Number.POSITIVE_INFINITY) {\n  if (typeof resultSelector === 'function') {\n    return mergeMap(() => innerObservable, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap(() => innerObservable, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "mergeMapTo", "innerObservable", "resultSelector", "concurrent", "Number", "POSITIVE_INFINITY"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/mergeMapTo.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Number.POSITIVE_INFINITY) {\n    if (typeof resultSelector === 'function') {\n        return mergeMap(() => innerObservable, resultSelector, concurrent);\n    }\n    if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return mergeMap(() => innerObservable, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAEC,cAAc,EAAEC,UAAU,GAAGC,MAAM,CAACC,iBAAiB,EAAE;EAC/F,IAAI,OAAOH,cAAc,KAAK,UAAU,EAAE;IACtC,OAAOH,QAAQ,CAAC,MAAME,eAAe,EAAEC,cAAc,EAAEC,UAAU,CAAC;EACtE;EACA,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IACpCC,UAAU,GAAGD,cAAc;EAC/B;EACA,OAAOH,QAAQ,CAAC,MAAME,eAAe,EAAEE,UAAU,CAAC;AACtD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
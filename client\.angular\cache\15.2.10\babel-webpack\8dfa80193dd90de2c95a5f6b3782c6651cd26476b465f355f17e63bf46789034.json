{"ast": null, "code": "import { concat as concatStatic } from '../observable/concat';\nexport function concat(...observables) {\n  return source => source.lift.call(concatStatic(source, ...observables));\n}", "map": {"version": 3, "names": ["concat", "concatStatic", "observables", "source", "lift", "call"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/concat.js"], "sourcesContent": ["import { concat as concatStatic } from '../observable/concat';\nexport function concat(...observables) {\n    return (source) => source.lift.call(concatStatic(source, ...observables));\n}\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,YAAY,QAAQ,sBAAsB;AAC7D,OAAO,SAASD,MAAMA,CAAC,GAAGE,WAAW,EAAE;EACnC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACJ,YAAY,CAACE,MAAM,EAAE,GAAGD,WAAW,CAAC,CAAC;AAC7E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n  constructor(SchedulerAction, now = Scheduler.now) {\n    super(SchedulerAction, () => {\n      if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n        return AsyncScheduler.delegate.now();\n      } else {\n        return now();\n      }\n    });\n    this.actions = [];\n    this.active = false;\n    this.scheduled = undefined;\n  }\n  schedule(work, delay = 0, state) {\n    if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n      return AsyncScheduler.delegate.schedule(work, delay, state);\n    } else {\n      return super.schedule(work, delay, state);\n    }\n  }\n  flush(action) {\n    const {\n      actions\n    } = this;\n    if (this.active) {\n      actions.push(action);\n      return;\n    }\n    let error;\n    this.active = true;\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (action = actions.shift());\n    this.active = false;\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["Scheduler", "AsyncScheduler", "constructor", "SchedulerAction", "now", "delegate", "actions", "active", "scheduled", "undefined", "schedule", "work", "delay", "state", "flush", "action", "push", "error", "execute", "shift", "unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduler/AsyncScheduler.js"], "sourcesContent": ["import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n    constructor(SchedulerAction, now = Scheduler.now) {\n        super(SchedulerAction, () => {\n            if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n                return AsyncScheduler.delegate.now();\n            }\n            else {\n                return now();\n            }\n        });\n        this.actions = [];\n        this.active = false;\n        this.scheduled = undefined;\n    }\n    schedule(work, delay = 0, state) {\n        if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n            return AsyncScheduler.delegate.schedule(work, delay, state);\n        }\n        else {\n            return super.schedule(work, delay, state);\n        }\n    }\n    flush(action) {\n        const { actions } = this;\n        if (this.active) {\n            actions.push(action);\n            return;\n        }\n        let error;\n        this.active = true;\n        do {\n            if (error = action.execute(action.state, action.delay)) {\n                break;\n            }\n        } while (action = actions.shift());\n        this.active = false;\n        if (error) {\n            while (action = actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,OAAO,MAAMC,cAAc,SAASD,SAAS,CAAC;EAC1CE,WAAWA,CAACC,eAAe,EAAEC,GAAG,GAAGJ,SAAS,CAACI,GAAG,EAAE;IAC9C,KAAK,CAACD,eAAe,EAAE,MAAM;MACzB,IAAIF,cAAc,CAACI,QAAQ,IAAIJ,cAAc,CAACI,QAAQ,KAAK,IAAI,EAAE;QAC7D,OAAOJ,cAAc,CAACI,QAAQ,CAACD,GAAG,CAAC,CAAC;MACxC,CAAC,MACI;QACD,OAAOA,GAAG,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAACE,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAGC,SAAS;EAC9B;EACAC,QAAQA,CAACC,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,KAAK,EAAE;IAC7B,IAAIZ,cAAc,CAACI,QAAQ,IAAIJ,cAAc,CAACI,QAAQ,KAAK,IAAI,EAAE;MAC7D,OAAOJ,cAAc,CAACI,QAAQ,CAACK,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAC/D,CAAC,MACI;MACD,OAAO,KAAK,CAACH,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAC7C;EACJ;EACAC,KAAKA,CAACC,MAAM,EAAE;IACV,MAAM;MAAET;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAI,IAAI,CAACC,MAAM,EAAE;MACbD,OAAO,CAACU,IAAI,CAACD,MAAM,CAAC;MACpB;IACJ;IACA,IAAIE,KAAK;IACT,IAAI,CAACV,MAAM,GAAG,IAAI;IAClB,GAAG;MACC,IAAIU,KAAK,GAAGF,MAAM,CAACG,OAAO,CAACH,MAAM,CAACF,KAAK,EAAEE,MAAM,CAACH,KAAK,CAAC,EAAE;QACpD;MACJ;IACJ,CAAC,QAAQG,MAAM,GAAGT,OAAO,CAACa,KAAK,CAAC,CAAC;IACjC,IAAI,CAACZ,MAAM,GAAG,KAAK;IACnB,IAAIU,KAAK,EAAE;MACP,OAAOF,MAAM,GAAGT,OAAO,CAACa,KAAK,CAAC,CAAC,EAAE;QAC7BJ,MAAM,CAACK,WAAW,CAAC,CAAC;MACxB;MACA,MAAMH,KAAK;IACf;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
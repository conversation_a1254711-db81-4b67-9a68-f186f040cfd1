{"ast": null, "code": "import isArray from './isArray.js';\nimport isKey from './_isKey.js';\nimport stringToPath from './_stringToPath.js';\nimport toString from './toString.js';\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\nexport default castPath;", "map": {"version": 3, "names": ["isArray", "is<PERSON>ey", "stringToPath", "toString", "<PERSON><PERSON><PERSON>", "value", "object"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_castPath.js"], "sourcesContent": ["import isArray from './isArray.js';\nimport isKey from './_isKey.js';\nimport stringToPath from './_stringToPath.js';\nimport toString from './toString.js';\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\n\nexport default castPath;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC/B,IAAIN,OAAO,CAACK,KAAK,CAAC,EAAE;IAClB,OAAOA,KAAK;EACd;EACA,OAAOJ,KAAK,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACD,KAAK,CAAC,GAAGH,YAAY,CAACC,QAAQ,CAACE,KAAK,CAAC,CAAC;AACvE;AAEA,eAAeD,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
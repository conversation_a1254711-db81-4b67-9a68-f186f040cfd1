{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON>, lyl } from '@alyle/ui';\nimport { LY_DIALOG_DATA } from '@alyle/ui/dialog';\nimport { STYLES as SLIDER_STYLES } from '@alyle/ui/slider';\nimport { STYLES as CROPPER_STYLES, LyImageCropper } from '@alyle/ui/image-cropper';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@alyle/ui\";\nimport * as i2 from \"@alyle/ui/dialog\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@alyle/ui/image-cropper\";\nimport * as i6 from \"@alyle/ui/slider\";\nimport * as i7 from \"@ngx-translate/core\";\nfunction CropperDialog_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cropper.zoomIn());\n    });\n    i0.ɵɵelement(2, \"i\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.cropper.zoomOut());\n    });\n    i0.ɵɵelement(4, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cropper.center());\n    });\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.cropper.rotate(-90));\n    });\n    i0.ɵɵelement(8, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cropper.fit());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.cropper.fitToScreen());\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CropperDialog_div_16_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.cropper.setScale(1));\n    });\n    i0.ɵɵtext(16, \" 1:1 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 2, \"Fit\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 4, \"Fit to screen\"), \" \");\n  }\n}\nconst STYLES = (_theme, ref) => {\n  ref.renderStyleSheet(SLIDER_STYLES);\n  ref.renderStyleSheet(CROPPER_STYLES);\n  const slider = ref.selectorsOf(SLIDER_STYLES);\n  const cropper = ref.selectorsOf(CROPPER_STYLES);\n  return {\n    root: lyl`{\n      ${cropper.root} {\n        max-width: 100vw\n        height: 60vh\n      }\n    }`,\n    sliderContainer: lyl`{\n      position: relative\n      ${slider.root} {\n        width: 80%\n        position: absolute\n        left: 0\n        right: 0\n        margin: auto\n        top: -45px\n      }\n    }`,\n    slider: lyl`{\n      padding: 1em\n    }`\n  };\n};\nexport class CropperDialog {\n  constructor(event, sRenderer, dialogRef) {\n    this.event = event;\n    this.sRenderer = sRenderer;\n    this.dialogRef = dialogRef;\n    this.classes = this.sRenderer.renderSheet(STYLES, 'root');\n    this.myConfig = {\n      width: 900,\n      height: 600,\n      // type: 'image/png',\n      keepAspectRatio: true,\n      responsiveArea: true,\n      output: {\n        width: 200,\n        height: 200\n      },\n      resizableArea: true\n    };\n    console.log(event);\n    this.myConfig = {\n      ...this.myConfig,\n      ...event.config\n    };\n  }\n  addOverlayImage() {\n    if (!this.event.overlayImageURL) return;\n    console.log(this.event.overlayImageURL);\n    // get element have tag ly-img-cropper\n    const element = document.querySelector('ly-img-cropper');\n    // create new div\n    const div = document.createElement('div');\n    div.id = 'image-backdrop';\n    // set style for div\n    div.style.position = 'absolute';\n    div.style.top = '-20px';\n    div.style.left = '0';\n    div.style.width = '100%';\n    div.style.height = '100%';\n    div.style.background = `url(${this.event.overlayImageURL})`;\n    div.style.backgroundSize = 'contain';\n    div.style.backgroundRepeat = 'no-repeat';\n    div.style.backgroundPosition = 'center';\n    div.style.pointerEvents = 'none';\n    // add div to element\n    element.appendChild(div);\n  }\n  ngAfterViewInit() {\n    // Load image when dialog animation has finished\n    this.dialogRef.afterOpened.subscribe(() => {\n      this.cropper.selectInputEvent(this.event.event);\n    });\n    this.addOverlayImage();\n  }\n  onCropped(e) {\n    console.log('cropped img: ', e);\n  }\n  onLoaded(e) {\n    console.log('img loaded', e);\n  }\n  onError(e) {\n    console.warn(`'${e.name}' is not a valid image`, e);\n    // Close the dialog if it fails\n    this.dialogRef.close();\n  }\n  onSliderInput(event) {\n    this.scale = event.value;\n  }\n  static #_ = this.ɵfac = function CropperDialog_Factory(t) {\n    return new (t || CropperDialog)(i0.ɵɵdirectiveInject(LY_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.StyleRenderer), i0.ɵɵdirectiveInject(i2.LyDialogRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CropperDialog,\n    selectors: [[\"ng-component\"]],\n    viewQuery: function CropperDialog_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(LyImageCropper, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cropper = _t.first);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([StyleRenderer])],\n    decls: 24,\n    vars: 24,\n    consts: [[\"ly-dialog-content\", \"\", 3, \"lyPx\"], [1, \"row\", \"align-items-center\", \"m-0\", \"p-1\", 2, \"width\", \"100%\"], [1, \"col\"], [1, \"h4\"], [1, \"col-auto\", \"p-0\"], [1, \"btn\", \"btn-icon\", \"btn-flat-dark\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-times\", 2, \"font-size\", \"20px\"], [3, \"config\", \"scale\", \"scaleChange\", \"ready\", \"cleaned\", \"minScale\", \"cropped\", \"loaded\", \"error\"], [3, \"className\"], [\"cdkFocusInitial\", \"\", 3, \"thumbVisible\", \"min\", \"max\", \"ngModel\", \"step\", \"ngModelChange\", \"input\"], [4, \"ngIf\"], [1, \"row\", \"justify-content-end\", \"m-0\", \"mt-1\", \"pb-1\", 2, \"width\", \"100%\"], [1, \"btn\", \"btn-danger\", \"mr-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"mr-1\", 3, \"click\"], [1, \"btn\", \"btn-icon\", \"btn-flat-dark\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"fas\", \"fa-minus\"], [1, \"fa-regular\", \"fa-camera-viewfinder\"], [1, \"fa-regular\", \"fa-rotate-left\"]],\n    template: function CropperDialog_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵpipe(5, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function CropperDialog_Template_button_click_7_listener() {\n          return ctx.dialogRef.close();\n        });\n        i0.ɵɵelement(8, \"i\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"ly-img-cropper\", 7);\n        i0.ɵɵlistener(\"scaleChange\", function CropperDialog_Template_ly_img_cropper_scaleChange_9_listener($event) {\n          return ctx.scale = $event;\n        })(\"ready\", function CropperDialog_Template_ly_img_cropper_ready_9_listener() {\n          return ctx.ready = true;\n        })(\"cleaned\", function CropperDialog_Template_ly_img_cropper_cleaned_9_listener() {\n          return ctx.ready = false;\n        })(\"minScale\", function CropperDialog_Template_ly_img_cropper_minScale_9_listener($event) {\n          return ctx.minScale = $event;\n        })(\"cropped\", function CropperDialog_Template_ly_img_cropper_cropped_9_listener($event) {\n          return ctx.onCropped($event);\n        })(\"loaded\", function CropperDialog_Template_ly_img_cropper_loaded_9_listener($event) {\n          return ctx.onLoaded($event);\n        })(\"error\", function CropperDialog_Template_ly_img_cropper_error_9_listener($event) {\n          return ctx.onError($event);\n        });\n        i0.ɵɵelementStart(10, \"span\");\n        i0.ɵɵtext(11);\n        i0.ɵɵpipe(12, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\")(15, \"ly-slider\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function CropperDialog_Template_ly_slider_ngModelChange_15_listener($event) {\n          return ctx.scale = $event;\n        })(\"input\", function CropperDialog_Template_ly_slider_input_15_listener($event) {\n          return ctx.onSliderInput($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(16, CropperDialog_div_16_Template, 17, 6, \"div\", 10);\n        i0.ɵɵelementStart(17, \"div\", 11)(18, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function CropperDialog_Template_button_click_18_listener() {\n          return ctx.dialogRef.close();\n        });\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function CropperDialog_Template_button_click_21_listener() {\n          return ctx.dialogRef.close(ctx.cropper.crop());\n        });\n        i0.ɵɵtext(22);\n        i0.ɵɵpipe(23, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"lyPx\", 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 16, \"Crop Image\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"config\", ctx.myConfig)(\"scale\", ctx.scale);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 18, \"Drag and drop image\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"className\", ctx.classes.sliderContainer);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassMap(ctx.classes.slider);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"thumbVisible\", false)(\"min\", ctx.minScale)(\"max\", 1)(\"ngModel\", ctx.scale)(\"step\", 0.000001);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.ready);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 20, \"Cancel\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 22, \"Done\"), \" \");\n      }\n    },\n    dependencies: [i3.NgIf, i4.NgControlStatus, i4.NgModel, i5.LyImageCropper, i6.LySlider, i1.LyStyle, i2.LyDialogContent, i7.TranslatePipe],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SACEA,aAAa,EAEbC,GAAG,QAGE,WAAW;AAClB,SAAsBC,cAAc,QAAQ,kBAAkB;AAC9D,SAAyBC,MAAM,IAAIC,aAAa,QAAQ,kBAAkB;AAC1E,SACED,MAAM,IAAIE,cAAc,EACxBC,cAAc,QAIT,yBAAyB;;;;;;;;;;;;ICoB9BC,EAAA,CAAAC,cAAA,UAAmB;IACTD,EAAA,CAAAE,UAAA,mBAAAC,sDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,OAAA,CAAAC,MAAA,EAAgB;IAAA,EAAC;IAChCV,EAAA,CAAAW,SAAA,YAA2B;IAC7BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAuE;IAA/DD,EAAA,CAAAE,UAAA,mBAAAW,sDAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAS,MAAA,GAAAd,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAM,MAAA,CAAAL,OAAA,CAAAM,OAAA,EAAiB;IAAA,EAAC;IACjCf,EAAA,CAAAW,SAAA,YAA4B;IAC9BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAsE;IAA9DD,EAAA,CAAAE,UAAA,mBAAAc,sDAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAY,MAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,MAAA,CAAAR,OAAA,CAAAS,MAAA,EAAgB;IAAA,EAAC;IAChClB,EAAA,CAAAW,SAAA,YAA+C;IACjDX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAyE;IAAjED,EAAA,CAAAE,UAAA,mBAAAiB,sDAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAe,MAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAX,OAAA,CAAAY,MAAA,EAAgB,EAAE,CAAC;IAAA,EAAC;IACnCrB,EAAA,CAAAW,SAAA,YAAyC;IAC3CX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAmE;IAA3DD,EAAA,CAAAE,UAAA,mBAAAoB,sDAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAkB,MAAA,GAAAvB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAe,MAAA,CAAAd,OAAA,CAAAe,GAAA,EAAa;IAAA,EAAC;IAC7BxB,EAAA,CAAAyB,MAAA,IACF;;IAAAzB,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAA2E;IAAnED,EAAA,CAAAE,UAAA,mBAAAwB,uDAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAsB,MAAA,GAAA3B,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmB,MAAA,CAAAlB,OAAA,CAAAmB,WAAA,EAAqB;IAAA,EAAC;IACrC5B,EAAA,CAAAyB,MAAA,IACF;;IAAAzB,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAAyE;IAAjED,EAAA,CAAAE,UAAA,mBAAA2B,uDAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAyB,MAAA,GAAA9B,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsB,MAAA,CAAArB,OAAA,CAAAsB,QAAA,CAAiB,CAAC,CAAC;IAAA,EAAC;IACnC/B,EAAA,CAAAyB,MAAA,aACF;IAAAzB,EAAA,CAAAY,YAAA,EAAS;;;IAPPZ,EAAA,CAAAgC,SAAA,IACF;IADEhC,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,oBACF;IAEElC,EAAA,CAAAgC,SAAA,GACF;IADEhC,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,8BACF;;;ADpCJ,MAAMtC,MAAM,GAAGA,CAACuC,MAAsB,EAAEC,GAAa,KAAI;EACvDA,GAAG,CAACC,gBAAgB,CAACxC,aAAa,CAAC;EACnCuC,GAAG,CAACC,gBAAgB,CAACvC,cAAc,CAAC;EACpC,MAAMwC,MAAM,GAAGF,GAAG,CAACG,WAAW,CAAC1C,aAAa,CAAC;EAC7C,MAAMY,OAAO,GAAG2B,GAAG,CAACG,WAAW,CAACzC,cAAc,CAAC;EAE/C,OAAO;IACL0C,IAAI,EAAE9C,GAAG;QACLe,OAAO,CAAC+B,IAAI;;;;MAId;IACFC,eAAe,EAAE/C,GAAG;;QAEhB4C,MAAM,CAACE,IAAI;;;;;;;;MAQb;IACFF,MAAM,EAAE5C,GAAG;;;GAGZ;AACH,CAAC;AAOD,OAAM,MAAOgD,aAAa;EAmBxBC,YACkCC,KAAU,EACjCC,SAAwB,EAC1BC,SAAsB;IAFG,KAAAF,KAAK,GAALA,KAAK;IAC5B,KAAAC,SAAS,GAATA,SAAS;IACX,KAAAC,SAAS,GAATA,SAAS;IArBT,KAAAC,OAAO,GAAG,IAAI,CAACF,SAAS,CAACG,WAAW,CAACpD,MAAM,EAAE,MAAM,CAAC;IAK7D,KAAAqD,QAAQ,GAAqB;MAC3BC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACX;MACAC,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE;QACNJ,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE;OACT;MACDI,aAAa,EAAE;KAChB;IAOCC,OAAO,CAACC,GAAG,CAACb,KAAK,CAAC;IAClB,IAAI,CAACK,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGL,KAAK,CAACc;IAAM,CAAE;EACvD;EAEAC,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACf,KAAK,CAACgB,eAAe,EAAE;IACjCJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAACb,KAAK,CAACgB,eAAe,CAAC;IAEvC;IACA,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACxD;IACA,MAAMC,GAAG,GAAGF,QAAQ,CAACG,aAAa,CAAC,KAAK,CAAC;IACzCD,GAAG,CAACE,EAAE,GAAG,gBAAgB;IACzB;IACAF,GAAG,CAACG,KAAK,CAACC,QAAQ,GAAG,UAAU;IAC/BJ,GAAG,CAACG,KAAK,CAACE,GAAG,GAAG,OAAO;IACvBL,GAAG,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACpBN,GAAG,CAACG,KAAK,CAACjB,KAAK,GAAG,MAAM;IACxBc,GAAG,CAACG,KAAK,CAAChB,MAAM,GAAG,MAAM;IACzBa,GAAG,CAACG,KAAK,CAACI,UAAU,GAAG,OAAO,IAAI,CAAC3B,KAAK,CAACgB,eAAe,GAAG;IAC3DI,GAAG,CAACG,KAAK,CAACK,cAAc,GAAG,SAAS;IACpCR,GAAG,CAACG,KAAK,CAACM,gBAAgB,GAAG,WAAW;IACxCT,GAAG,CAACG,KAAK,CAACO,kBAAkB,GAAG,QAAQ;IACvCV,GAAG,CAACG,KAAK,CAACQ,aAAa,GAAG,MAAM;IAChC;IACAd,OAAO,CAACe,WAAW,CAACZ,GAAG,CAAC;EAC1B;EAEAa,eAAeA,CAAA;IACb;IACA,IAAI,CAAC/B,SAAS,CAACgC,WAAW,CAACC,SAAS,CAAC,MAAK;MACxC,IAAI,CAACtE,OAAO,CAACuE,gBAAgB,CAAC,IAAI,CAACpC,KAAK,CAACA,KAAK,CAAC;IACjD,CAAC,CAAC;IACF,IAAI,CAACe,eAAe,EAAE;EACxB;EAEAsB,SAASA,CAACC,CAAkB;IAC1B1B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyB,CAAC,CAAC;EACjC;EACAC,QAAQA,CAACD,CAAkB;IACzB1B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyB,CAAC,CAAC;EAC9B;EACAE,OAAOA,CAACF,CAAuB;IAC7B1B,OAAO,CAAC6B,IAAI,CAAC,IAAIH,CAAC,CAACI,IAAI,wBAAwB,EAAEJ,CAAC,CAAC;IACnD;IACA,IAAI,CAACpC,SAAS,CAACyC,KAAK,EAAE;EACxB;EAEAC,aAAaA,CAAC5C,KAAqB;IACjC,IAAI,CAAC6C,KAAK,GAAG7C,KAAK,CAAC8C,KAAe;EACpC;EAAC,QAAAC,CAAA;qBA1EUjD,aAAa,EAAA1C,EAAA,CAAA4F,iBAAA,CAoBdjG,cAAc,GAAAK,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAApG,aAAA,GAAAO,EAAA,CAAA4F,iBAAA,CAAAE,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;UApBbtD,aAAa;IAAAuD,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAKbrG,cAAc;;;;;;;qCAPd,CAACN,aAAa,CAAC;IAAA6G,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzD5BpG,EAAA,CAAAC,cAAA,aAAkC;QAIbD,EAAA,CAAAyB,MAAA,GAA0B;;QAAAzB,EAAA,CAAAY,YAAA,EAAK;QAEhDZ,EAAA,CAAAC,cAAA,aAA0B;QAGtBD,EAAA,CAAAE,UAAA,mBAAAyG,+CAAA;UAAA,OAASN,GAAA,CAAAvD,SAAA,CAAAyC,KAAA,EAAiB;QAAA,EAAC;QAE3BvF,EAAA,CAAAW,SAAA,WAAoD;QACtDX,EAAA,CAAAY,YAAA,EAAS;QAGbZ,EAAA,CAAAC,cAAA,wBASC;QAPCD,EAAA,CAAAE,UAAA,yBAAA0G,6DAAAC,MAAA;UAAA,OAAAR,GAAA,CAAAZ,KAAA,GAAAoB,MAAA;QAAA,EAAiB,mBAAAC,uDAAA;UAAA,OAAAT,GAAA,CAAAU,KAAA,GACA,IAAI;QAAA,EADJ,qBAAAC,yDAAA;UAAA,OAAAX,GAAA,CAAAU,KAAA,GAEE,KAAK;QAAA,EAFP,sBAAAE,0DAAAJ,MAAA;UAAA,OAAAR,GAAA,CAAAa,QAAA,GAAAL,MAAA;QAAA,uBAAAM,yDAAAN,MAAA;UAAA,OAINR,GAAA,CAAApB,SAAA,CAAA4B,MAAA,CAAiB;QAAA,EAJX,oBAAAO,wDAAAP,MAAA;UAAA,OAKPR,GAAA,CAAAlB,QAAA,CAAA0B,MAAA,CAAgB;QAAA,EALT,mBAAAQ,uDAAAR,MAAA;UAAA,OAMRR,GAAA,CAAAjB,OAAA,CAAAyB,MAAA,CAAe;QAAA,EANP;QAQjB7G,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAyB,MAAA,IAAqC;;QAAAzB,EAAA,CAAAY,YAAA,EAAO;QAGpDZ,EAAA,CAAAC,cAAA,cAA2C;QAMrCD,EAAA,CAAAE,UAAA,2BAAAoH,2DAAAT,MAAA;UAAA,OAAAR,GAAA,CAAAZ,KAAA,GAAAoB,MAAA;QAAA,EAAmB,mBAAAU,mDAAAV,MAAA;UAAA,OACVR,GAAA,CAAAb,aAAA,CAAAqB,MAAA,CAAqB;QAAA,EADX;QAIpB7G,EAAA,CAAAY,YAAA,EAAY;QAIjBZ,EAAA,CAAAwH,UAAA,KAAAC,6BAAA,mBAsBM;QAENzH,EAAA,CAAAC,cAAA,eAAuE;QAC7DD,EAAA,CAAAE,UAAA,mBAAAwH,gDAAA;UAAA,OAASrB,GAAA,CAAAvD,SAAA,CAAAyC,KAAA,EAAiB;QAAA,EAAC;QACjCvF,EAAA,CAAAyB,MAAA,IACF;;QAAAzB,EAAA,CAAAY,YAAA,EAAS;QACTZ,EAAA,CAAAC,cAAA,kBAGC;QAFCD,EAAA,CAAAE,UAAA,mBAAAyH,gDAAA;UAAA,OAAStB,GAAA,CAAAvD,SAAA,CAAAyC,KAAA,CAAgBc,GAAA,CAAA5F,OAAA,CAAAmH,IAAA,EAAc,CAAC;QAAA,EAAC;QAGzC5H,EAAA,CAAAyB,MAAA,IACF;;QAAAzB,EAAA,CAAAY,YAAA,EAAS;;;QA3EUZ,EAAA,CAAA6H,UAAA,WAAU;QAIZ7H,EAAA,CAAAgC,SAAA,GAA0B;QAA1BhC,EAAA,CAAA8H,iBAAA,CAAA9H,EAAA,CAAAkC,WAAA,sBAA0B;QAY3ClC,EAAA,CAAAgC,SAAA,GAAmB;QAAnBhC,EAAA,CAAA6H,UAAA,WAAAxB,GAAA,CAAApD,QAAA,CAAmB,UAAAoD,GAAA,CAAAZ,KAAA;QASbzF,EAAA,CAAAgC,SAAA,GAAqC;QAArChC,EAAA,CAAA8H,iBAAA,CAAA9H,EAAA,CAAAkC,WAAA,gCAAqC;QAGxClC,EAAA,CAAAgC,SAAA,GAAqC;QAArChC,EAAA,CAAA6H,UAAA,cAAAxB,GAAA,CAAAtD,OAAA,CAAAN,eAAA,CAAqC;QACnCzC,EAAA,CAAAgC,SAAA,GAAwB;QAAxBhC,EAAA,CAAA+H,UAAA,CAAA1B,GAAA,CAAAtD,OAAA,CAAAT,MAAA,CAAwB;QAEzBtC,EAAA,CAAAgC,SAAA,GAAsB;QAAtBhC,EAAA,CAAA6H,UAAA,uBAAsB,QAAAxB,GAAA,CAAAa,QAAA,uBAAAb,GAAA,CAAAZ,KAAA;QAWtBzF,EAAA,CAAAgC,SAAA,GAAW;QAAXhC,EAAA,CAAA6H,UAAA,SAAAxB,GAAA,CAAAU,KAAA,CAAW;QA0Bb/G,EAAA,CAAAgC,SAAA,GACF;QADEhC,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,wBACF;QAKElC,EAAA,CAAAgC,SAAA,GACF;QADEhC,EAAA,CAAAiC,kBAAA,MAAAjC,EAAA,CAAAkC,WAAA,sBACF", "names": ["<PERSON><PERSON><PERSON><PERSON>", "lyl", "LY_DIALOG_DATA", "STYLES", "SLIDER_STYLES", "CROPPER_STYLES", "LyImageCropper", "i0", "ɵɵelementStart", "ɵɵlistener", "CropperDialog_div_16_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "cropper", "zoomIn", "ɵɵelement", "ɵɵelementEnd", "CropperDialog_div_16_Template_button_click_3_listener", "ctx_r3", "zoomOut", "CropperDialog_div_16_Template_button_click_5_listener", "ctx_r4", "center", "CropperDialog_div_16_Template_button_click_7_listener", "ctx_r5", "rotate", "CropperDialog_div_16_Template_button_click_9_listener", "ctx_r6", "fit", "ɵɵtext", "CropperDialog_div_16_Template_button_click_12_listener", "ctx_r7", "fitToScreen", "CropperDialog_div_16_Template_button_click_15_listener", "ctx_r8", "setScale", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "_theme", "ref", "renderStyleSheet", "slider", "selectorsOf", "root", "slide<PERSON><PERSON><PERSON><PERSON>", "CropperDialog", "constructor", "event", "s<PERSON><PERSON><PERSON>", "dialogRef", "classes", "renderSheet", "myConfig", "width", "height", "keepAspectRatio", "responsiveArea", "output", "resizableArea", "console", "log", "config", "addOverlayImage", "overlayImageURL", "element", "document", "querySelector", "div", "createElement", "id", "style", "position", "top", "left", "background", "backgroundSize", "backgroundRepeat", "backgroundPosition", "pointerEvents", "append<PERSON><PERSON><PERSON>", "ngAfterViewInit", "afterOpened", "subscribe", "selectInputEvent", "onCropped", "e", "onLoaded", "onError", "warn", "name", "close", "onSliderInput", "scale", "value", "_", "ɵɵdirectiveInject", "i1", "i2", "LyDialogRef", "_2", "selectors", "viewQuery", "CropperDialog_Query", "rf", "ctx", "decls", "vars", "consts", "template", "CropperDialog_Template", "CropperDialog_Template_button_click_7_listener", "CropperDialog_Template_ly_img_cropper_scaleChange_9_listener", "$event", "CropperDial<PERSON>_Template_ly_img_cropper_ready_9_listener", "ready", "Cropper<PERSON>ial<PERSON>_Template_ly_img_cropper_cleaned_9_listener", "CropperDialog_Template_ly_img_cropper_minScale_9_listener", "minScale", "CropperDial<PERSON>_Template_ly_img_cropper_cropped_9_listener", "CropperDial<PERSON>_Template_ly_img_cropper_loaded_9_listener", "CropperDialog_Template_ly_img_cropper_error_9_listener", "C<PERSON>per<PERSON><PERSON><PERSON>_Template_ly_slider_ngModelChange_15_listener", "C<PERSON>per<PERSON><PERSON><PERSON>_Template_ly_slider_input_15_listener", "ɵɵtemplate", "CropperDialog_div_16_Template", "CropperDialog_Template_button_click_18_listener", "CropperDialog_Template_button_click_21_listener", "crop", "ɵɵproperty", "ɵɵtextInterpolate", "ɵɵclassMap"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\cropper-dialog\\cropper-dialog.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\cropper-dialog\\cropper-dialog.html"], "sourcesContent": ["import {\r\n  Component,\r\n  ChangeDetectionStrategy,\r\n  Inject,\r\n  ViewChild,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport {\r\n  StyleRenderer,\r\n  WithStyles,\r\n  lyl,\r\n  ThemeRef,\r\n  ThemeVariables,\r\n} from '@alyle/ui';\r\nimport { LyDialogRef, LY_DIALOG_DATA } from '@alyle/ui/dialog';\r\nimport { LySliderChange, STYLES as SLIDER_STYLES } from '@alyle/ui/slider';\r\nimport {\r\n  STYLES as CROPPER_STYLES,\r\n  LyImageCropper,\r\n  ImgCropperConfig,\r\n  ImgCropperEvent,\r\n  ImgCropperErrorEvent,\r\n} from '@alyle/ui/image-cropper';\r\n\r\nconst STYLES = (_theme: ThemeVariables, ref: ThemeRef) => {\r\n  ref.renderStyleSheet(SLIDER_STYLES);\r\n  ref.renderStyleSheet(CROPPER_STYLES);\r\n  const slider = ref.selectorsOf(SLIDER_STYLES);\r\n  const cropper = ref.selectorsOf(CROPPER_STYLES);\r\n\r\n  return {\r\n    root: lyl`{\r\n      ${cropper.root} {\r\n        max-width: 100vw\r\n        height: 60vh\r\n      }\r\n    }`,\r\n    sliderContainer: lyl`{\r\n      position: relative\r\n      ${slider.root} {\r\n        width: 80%\r\n        position: absolute\r\n        left: 0\r\n        right: 0\r\n        margin: auto\r\n        top: -45px\r\n      }\r\n    }`,\r\n    slider: lyl`{\r\n      padding: 1em\r\n    }`,\r\n  };\r\n};\r\n\r\n@Component({\r\n  templateUrl: './cropper-dialog.html',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  providers: [StyleRenderer],\r\n})\r\nexport class CropperDialog implements WithStyles, AfterViewInit {\r\n  readonly classes = this.sRenderer.renderSheet(STYLES, 'root');\r\n  ready: boolean;\r\n  scale: number;\r\n  minScale: number;\r\n  @ViewChild(LyImageCropper, { static: true }) cropper: LyImageCropper;\r\n  myConfig: ImgCropperConfig = {\r\n    width: 900,\r\n    height: 600,\r\n    // type: 'image/png',\r\n    keepAspectRatio: true,\r\n    responsiveArea: true,\r\n    output: {\r\n      width: 200,\r\n      height: 200,\r\n    },\r\n    resizableArea: true,\r\n  };\r\n\r\n  constructor(\r\n    @Inject(LY_DIALOG_DATA) private event: any,\r\n    readonly sRenderer: StyleRenderer,\r\n    public dialogRef: LyDialogRef\r\n  ) {\r\n    console.log(event);\r\n    this.myConfig = { ...this.myConfig, ...event.config };\r\n  }\r\n\r\n  addOverlayImage() {\r\n    if (!this.event.overlayImageURL) return;\r\n    console.log(this.event.overlayImageURL);\r\n\r\n    // get element have tag ly-img-cropper\r\n    const element = document.querySelector('ly-img-cropper');\r\n    // create new div\r\n    const div = document.createElement('div');\r\n    div.id = 'image-backdrop';\r\n    // set style for div\r\n    div.style.position = 'absolute';\r\n    div.style.top = '-20px';\r\n    div.style.left = '0';\r\n    div.style.width = '100%';\r\n    div.style.height = '100%';\r\n    div.style.background = `url(${this.event.overlayImageURL})`;\r\n    div.style.backgroundSize = 'contain';\r\n    div.style.backgroundRepeat = 'no-repeat';\r\n    div.style.backgroundPosition = 'center';\r\n    div.style.pointerEvents = 'none';\r\n    // add div to element\r\n    element.appendChild(div);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    // Load image when dialog animation has finished\r\n    this.dialogRef.afterOpened.subscribe(() => {\r\n      this.cropper.selectInputEvent(this.event.event);\r\n    });\r\n    this.addOverlayImage();\r\n  }\r\n\r\n  onCropped(e: ImgCropperEvent) {\r\n    console.log('cropped img: ', e);\r\n  }\r\n  onLoaded(e: ImgCropperEvent) {\r\n    console.log('img loaded', e);\r\n  }\r\n  onError(e: ImgCropperErrorEvent) {\r\n    console.warn(`'${e.name}' is not a valid image`, e);\r\n    // Close the dialog if it fails\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  onSliderInput(event: LySliderChange) {\r\n    this.scale = event.value as number;\r\n  }\r\n}\r\n", "<div ly-dialog-content [lyPx]=\"0\">\r\n  <!-- header -->\r\n  <div class=\"row align-items-center m-0 p-1\" style=\"width: 100%\">\r\n    <div class=\"col\">\r\n      <h2 class=\"h4\">{{'Crop Image'|translate}}</h2>\r\n    </div>\r\n    <div class=\"col-auto p-0\">\r\n      <button\r\n        class=\"btn btn-icon btn-flat-dark p-0\"\r\n        (click)=\"dialogRef.close()\"\r\n      >\r\n        <i class=\"fas fa-times\" style=\"font-size: 20px\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n  <ly-img-cropper\r\n    [config]=\"myConfig\"\r\n    [(scale)]=\"scale\"\r\n    (ready)=\"ready = true\"\r\n    (cleaned)=\"ready = false\"\r\n    (minScale)=\"minScale = $event\"\r\n    (cropped)=\"onCropped($event)\"\r\n    (loaded)=\"onLoaded($event)\"\r\n    (error)=\"onError($event)\"\r\n  >\r\n    <span>{{'Drag and drop image' | translate}}</span>\r\n  </ly-img-cropper> \r\n\r\n  <div [className]=\"classes.sliderContainer\">\r\n    <div [class]=\"classes.slider\">\r\n      <ly-slider\r\n        [thumbVisible]=\"false\"\r\n        [min]=\"minScale\"\r\n        [max]=\"1\"\r\n        [(ngModel)]=\"scale\"\r\n        (input)=\"onSliderInput($event)\"\r\n        [step]=\"0.000001\"\r\n        cdkFocusInitial\r\n      ></ly-slider>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"ready\">\r\n    <button (click)=\"cropper.zoomIn()\" class=\"btn btn-icon btn-flat-dark\">\r\n      <i class=\"fas fa-plus\"></i>\r\n    </button>\r\n    <button (click)=\"cropper.zoomOut()\" class=\"btn btn-icon btn-flat-dark\">\r\n      <i class=\"fas fa-minus\"></i>\r\n    </button>\r\n    <button (click)=\"cropper.center()\" class=\"btn btn-icon btn-flat-dark\">\r\n      <i class=\"fa-regular fa-camera-viewfinder\"></i>\r\n    </button>\r\n    <button (click)=\"cropper.rotate(-90)\" class=\"btn btn-icon btn-flat-dark\">\r\n      <i class=\"fa-regular fa-rotate-left\"></i>\r\n    </button>\r\n    <button (click)=\"cropper.fit()\" class=\"btn btn-icon btn-flat-dark\">\r\n      {{'Fit' | translate}}\r\n    </button>\r\n    <button (click)=\"cropper.fitToScreen()\" class=\"btn btn-icon btn-flat-dark\">\r\n      {{'Fit to screen' | translate}}\r\n    </button>\r\n    <button (click)=\"cropper.setScale(1)\" class=\"btn btn-icon btn-flat-dark\">\r\n      1:1\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"row justify-content-end m-0 mt-1 pb-1\" style=\"width: 100%\">\r\n    <button (click)=\"dialogRef.close()\" class=\"btn btn-danger mr-1\">\r\n      {{'Cancel' | translate}}\r\n    </button>\r\n    <button\r\n      (click)=\"dialogRef.close(cropper.crop())\"\r\n      class=\"btn btn-success mr-1\"\r\n    >\r\n      {{'Done' | translate}}\r\n    </button>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
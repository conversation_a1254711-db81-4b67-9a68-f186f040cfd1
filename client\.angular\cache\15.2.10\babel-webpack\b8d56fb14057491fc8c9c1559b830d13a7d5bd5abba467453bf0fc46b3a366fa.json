{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ThumbnailStreamComponent } from './thumbnail-stream.component';\nimport * as i0 from \"@angular/core\";\nexport class ThumbnailStreamodule {\n  static #_ = this.ɵfac = function ThumbnailStreamodule_Factory(t) {\n    return new (t || ThumbnailStreamodule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ThumbnailStreamodule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ThumbnailStreamodule, {\n    declarations: [ThumbnailStreamComponent],\n    imports: [CommonModule],\n    exports: [ThumbnailStreamComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,8BAA8B;;AAOvE,OAAM,MAAOC,oBAAoB;EAAA,QAAAC,CAAA;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA;UAApBF;EAAoB;EAAA,QAAAG,EAAA;cAHrBL,YAAY;EAAA;;;2EAGXE,oBAAoB;IAAAI,YAAA,GAJhBL,wBAAwB;IAAAM,OAAA,GAC7BP,YAAY;IAAAQ,OAAA,GACZP,wBAAwB;EAAA;AAAA", "names": ["CommonModule", "ThumbnailStreamComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\thumbnail-stream\\thumbnail-stream.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ThumbnailStreamComponent } from './thumbnail-stream.component';\r\n\r\n@NgModule({\r\n  declarations: [ThumbnailStreamComponent],\r\n  imports: [CommonModule],\r\n  exports: [ThumbnailStreamComponent],\r\n})\r\nexport class ThumbnailStreamodule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
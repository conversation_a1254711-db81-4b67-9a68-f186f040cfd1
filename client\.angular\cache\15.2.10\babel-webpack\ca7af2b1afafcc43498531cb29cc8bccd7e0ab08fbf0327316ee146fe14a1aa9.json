{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormGroup } from '@angular/forms';\nimport { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/registration.service\";\nimport * as i2 from \"app/services/send-messages.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"app/services/season.service\";\nimport * as i6 from \"app/services/auth.service\";\nimport * as i7 from \"app/services/user.service\";\nimport * as i8 from \"app/services/club.service\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../services/settings.service\";\nimport * as i11 from \"@angular/router\";\nimport * as i12 from \"@angular/common/http\";\nimport * as i13 from \"@angular/platform-browser\";\nimport * as i14 from \"app/services/export.service\";\nimport * as i15 from \"@angular/common\";\nimport * as i16 from \"@angular/flex-layout/extended\";\nimport * as i17 from \"app/layout/components/content-header/content-header.component\";\nimport * as i18 from \"angular-datatables\";\nconst _c0 = [\"modalForm\"];\nfunction ReportsComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const season_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"colspan\", (ctx_r0.initSettings == null ? null : ctx_r0.initSettings.is_payment_required) ? 2 : 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", season_r2.name, \" \");\n  }\n}\nfunction ReportsComponent_ng_container_25_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Inv.status\"), \" \");\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"text-start\": a0,\n    \"text-center\": a1\n  };\n};\nfunction ReportsComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ReportsComponent_ng_container_25_th_4_Template, 3, 3, \"th\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c1, ctx_r1.initSettings == null ? null : ctx_r1.initSettings.is_payment_required, !(ctx_r1.initSettings == null ? null : ctx_r1.initSettings.is_payment_required)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"Reg.status\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.initSettings && ctx_r1.initSettings.is_payment_required);\n  }\n}\nexport class ReportsComponent {\n  constructor(_registrationService, _sendMessageService, _translateService, _commonsService, _seasonService, _authService, _userService, _clubService, _modalService, _settingsService, route, _render, _http, _router, _titleService, _exportService) {\n    this._registrationService = _registrationService;\n    this._sendMessageService = _sendMessageService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._seasonService = _seasonService;\n    this._authService = _authService;\n    this._userService = _userService;\n    this._clubService = _clubService;\n    this._modalService = _modalService;\n    this._settingsService = _settingsService;\n    this.route = route;\n    this._render = _render;\n    this._http = _http;\n    this._router = _router;\n    this._titleService = _titleService;\n    this._exportService = _exportService;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.seasons_reports = [];\n    this.form = new FormGroup({});\n    this.model = {};\n    this.fields = [];\n    this.initSettings = null;\n    this._titleService.setTitle('Reports');\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: 'Reports',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Registrations',\n          isLink: false\n        }, {\n          name: 'Reports',\n          isLink: false\n        }]\n      }\n    };\n    this.initSettings = this._settingsService.getInitSettingFromLocalStorage();\n    console.log('this.initSettings', this.initSettings);\n    this.preloadOptions();\n  }\n  buildTable() {\n    var _this = this;\n    let col_season = [];\n    let col_default = [{\n      sortable: false,\n      data: 'photo',\n      render: function (data, type, row) {\n        //create image\n        let img = document.createElement('img');\n        img.src = data;\n        img.id = `img-${row.id}`;\n        img.style.width = '50px';\n        img.style.height = 'auto';\n        img.style.objectFit = 'cover';\n        img.style.backgroundColor = '#fff';\n        img.style.objectFit = 'cover';\n        if (data == null) {\n          img.src = 'assets/images/logo/ezactive_1024x1024.png';\n        }\n        // check get image error\n        img.onerror = function () {\n          img.src = 'assets/images/logo/ezactive_1024x1024.png';\n          // set src by row id\n          $(`#img-${row.id}`).attr('src', img.src);\n        };\n        return img.outerHTML;\n      }\n    }, {\n      data: 'users',\n      render: (data, type, row) => {\n        return this._userService.fullName(data);\n      }\n    }, {\n      data: 'dob'\n    }, {\n      data: 'gender'\n    }, {\n      data: 'validate_status',\n      render: (data, type, row) => {\n        return this._commonsService.getBadgeValidateStatus(data);\n      }\n    }];\n    for (let i = 0; i < this.seasons_reports.length; i++) {\n      col_season.push({\n        data: `seasons_reports.${i}.registrations.approval_status`,\n        render: (data, type, row) => {\n          if (data == null) data = 'No registration';\n          return `<span class=\"badge ${this._commonsService.getBadgeRegistration(data)} text-capitalize\"> ${data}</span>`;\n        }\n      });\n      if (this.initSettings && this.initSettings.is_payment_required) {\n        col_season.push({\n          data: `seasons_reports.${i}.payment_details.status`,\n          render: (data, type, row) => {\n            return this._commonsService.getBadgeClassPayment(data);\n          }\n        });\n      }\n    }\n    // merge columns\n    col_default = col_default.concat(col_season);\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        dataTablesParameters.season_id = this.season_id;\n        this._http.get(`${environment.apiUrl}/players/report`, dataTablesParameters).subscribe(resp => {\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      // responsive: true,\n      scrollX: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      // columnDefs: [\n      //   { targets: 0, responsivePriority: 1 },\n      //   { targets: -1, responsivePriority: 2 },\n      //   { targets: -2, responsivePriority: 3 },\n      // ],\n      columns: col_default,\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa fa-file-excel-o mr-1\"></i> ${this._translateService.instant('Export Excel')}`,\n          extend: 'excel',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              yield _this._exportService.exportExcel(data, 'Report.xlsx');\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  getLatestSeasons(no_season) {\n    this._seasonService.getLatestSeasons(no_season).subscribe(res => {\n      this.seasons_reports = res.data;\n      this.buildTable();\n      this.dtTrigger.next(this.dtOptions);\n    });\n  }\n  preloadOptions() {\n    this.getLatestSeasons(3);\n    this.buildTable();\n    this._settingsService.initSettings.subscribe(res => {\n      this.initSettings = res;\n    });\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n  }\n  static #_ = this.ɵfac = function ReportsComponent_Factory(t) {\n    return new (t || ReportsComponent)(i0.ɵɵdirectiveInject(i1.RegistrationService), i0.ɵɵdirectiveInject(i2.SendMessagesService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.SeasonService), i0.ɵɵdirectiveInject(i6.AuthService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.ClubService), i0.ɵɵdirectiveInject(i9.NgbModal), i0.ɵɵdirectiveInject(i10.SettingsService), i0.ɵɵdirectiveInject(i11.ActivatedRoute), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i12.HttpClient), i0.ɵɵdirectiveInject(i11.Router), i0.ɵɵdirectiveInject(i13.Title), i0.ɵɵdirectiveInject(i14.ExportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ReportsComponent,\n    selectors: [[\"app-reports\"]],\n    viewQuery: function ReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalForm = _t.first);\n      }\n    },\n    decls: 26,\n    vars: 20,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"report\", 1, \"card\"], [1, \"card-header\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", \"mt-2\", 3, \"dtOptions\", \"dtTrigger\"], [\"rowspan\", \"2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-center\"], [3, \"ngClass\"], [4, \"ngIf\"]],\n    template: function ReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"div\", 4);\n        i0.ɵɵelementStart(5, \"table\", 5)(6, \"thead\")(7, \"tr\")(8, \"th\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"th\", 6);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"th\", 6);\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"th\", 6);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"th\", 6);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(23, ReportsComponent_ng_container_23_Template, 3, 2, \"ng-container\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"tr\");\n        i0.ɵɵtemplate(25, ReportsComponent_ng_container_25_Template, 5, 8, \"ng-container\", 7);\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 10, \"Photo\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 12, \"Full Name\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 14, \"Dob\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 16, \"Gender\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 18, \"Validate Status\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons_reports);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons_reports);\n      }\n    },\n    dependencies: [i15.NgClass, i15.NgForOf, i15.NgIf, i16.DefaultClassDirective, i17.ContentHeaderComponent, i18.DataTableDirective, i3.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAQA,SAASA,SAAS,QAAQ,gBAAgB;AAI1C,SAASC,kBAAkB,QAAQ,oBAAoB;AAIvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;ICElBC,EAAA,CAAAC,uBAAA,GAAqD;IACnDD,EAAA,CAAAE,cAAA,YAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACPJ,EAAA,CAAAK,qBAAA,EAAe;;;;;IALXL,EAAA,CAAAM,SAAA,GAA0D;IAA1DN,EAAA,CAAAO,WAAA,aAAAC,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAC,mBAAA,UAA0D;IAG1DV,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAW,kBAAA,MAAAC,SAAA,CAAAC,IAAA,MACF;;;;;IAaAb,EAAA,CAAAE,cAAA,SAA6D;IAC3DF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;IADHJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAW,kBAAA,MAAAX,EAAA,CAAAc,WAAA,0BACF;;;;;;;;;;;IAXFd,EAAA,CAAAC,uBAAA,GAAqD;IACnDD,EAAA,CAAAE,cAAA,YAKC;IACCF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAe,UAAA,IAAAC,8CAAA,iBAEK;IACPhB,EAAA,CAAAK,qBAAA,EAAe;;;;IAVXL,EAAA,CAAAM,SAAA,GAGE;IAHFN,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAX,YAAA,kBAAAW,MAAA,CAAAX,YAAA,CAAAC,mBAAA,IAAAU,MAAA,CAAAX,YAAA,kBAAAW,MAAA,CAAAX,YAAA,CAAAC,mBAAA,GAGE;IAEFV,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAW,kBAAA,MAAAX,EAAA,CAAAc,WAAA,0BACF;IACKd,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAiB,UAAA,SAAAG,MAAA,CAAAX,YAAA,IAAAW,MAAA,CAAAX,YAAA,CAAAC,mBAAA,CAAsD;;;ADJzE,OAAM,MAAOW,gBAAgB;EAG3BC,YACSC,oBAAyC,EACzCC,mBAAwC,EACxCC,iBAAmC,EACnCC,eAA+B,EAC/BC,cAA6B,EAC7BC,YAAyB,EACzBC,YAAyB,EACzBC,YAAyB,EACzBC,aAAuB,EACvBC,gBAAiC,EAChCC,KAAqB,EACtBC,OAAkB,EAClBC,KAAiB,EACjBC,OAAe,EACfC,aAAoB,EACnBC,cAA6B;IAf9B,KAAAf,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACf,KAAAC,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IAMxB,KAAAC,SAAS,GAAQ1C,kBAAkB;IAEnC,KAAA2C,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAI1C,OAAO,EAAe;IAI5D,KAAA2C,eAAe,GAAG,EAAE;IACpB,KAAAC,IAAI,GAAG,IAAI/C,SAAS,CAAC,EAAE,CAAC;IACxB,KAAAgD,KAAK,GAAQ,EAAE;IACf,KAAAC,MAAM,GAAwB,EAAE;IAEhC,KAAApC,YAAY,GAAG,IAAI;IAhBjB,IAAI,CAAC4B,aAAa,CAACS,QAAQ,CAAC,SAAS,CAAC;EACxC;EAiBAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,SAAS;MACtBC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACExC,IAAI,EAAE,eAAe;UACrByC,MAAM,EAAE;SACT,EACD;UACEzC,IAAI,EAAE,SAAS;UACfyC,MAAM,EAAE;SACT;;KAGN;IAGD,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAACuB,gBAAgB,CAACuB,8BAA8B,EAAE;IAC1EC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAChD,YAAY,CAAC;IAGnD,IAAI,CAACiD,cAAc,EAAE;EACvB;EAEAC,UAAUA,CAAA;IAAA,IAAAC,KAAA;IACR,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,CAChB;MACEC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,SAAAA,CAAUD,IAAS,EAAEZ,IAAS,EAAEc,GAAQ;QAC9C;QACA,IAAIC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACvCF,GAAG,CAACG,GAAG,GAAGN,IAAI;QACdG,GAAG,CAACI,EAAE,GAAG,OAAOL,GAAG,CAACK,EAAE,EAAE;QACxBJ,GAAG,CAACK,KAAK,CAACC,KAAK,GAAG,MAAM;QACxBN,GAAG,CAACK,KAAK,CAACE,MAAM,GAAG,MAAM;QACzBP,GAAG,CAACK,KAAK,CAACG,SAAS,GAAG,OAAO;QAC7BR,GAAG,CAACK,KAAK,CAACI,eAAe,GAAG,MAAM;QAClCT,GAAG,CAACK,KAAK,CAACG,SAAS,GAAG,OAAO;QAC7B,IAAIX,IAAI,IAAI,IAAI,EAAE;UAChBG,GAAG,CAACG,GAAG,GAAG,2CAA2C;;QAEvD;QACAH,GAAG,CAACU,OAAO,GAAG;UACZV,GAAG,CAACG,GAAG,GAAG,2CAA2C;UACrD;UACAQ,CAAC,CAAC,QAAQZ,GAAG,CAACK,EAAE,EAAE,CAAC,CAACQ,IAAI,CAAC,KAAK,EAAEZ,GAAG,CAACG,GAAG,CAAC;QAC1C,CAAC;QACD,OAAOH,GAAG,CAACa,SAAS;MACtB;KACD,EACD;MACEhB,IAAI,EAAE,OAAO;MAEbC,MAAM,EAAEA,CAACD,IAAS,EAAEZ,IAAS,EAAEc,GAAQ,KAAI;QACzC,OAAO,IAAI,CAACrC,YAAY,CAACoD,QAAQ,CAACjB,IAAI,CAAC;MACzC;KACD,EACD;MACEA,IAAI,EAAE;KACP,EACD;MACEA,IAAI,EAAE;KACP,EACD;MACEA,IAAI,EAAE,iBAAiB;MAEvBC,MAAM,EAAEA,CAACD,IAAS,EAAEZ,IAAS,EAAEc,GAAQ,KAAI;QACzC,OAAO,IAAI,CAACxC,eAAe,CAACwD,sBAAsB,CAAClB,IAAI,CAAC;MAC1D;KACD,CACF;IAED,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzC,eAAe,CAAC0C,MAAM,EAAED,CAAC,EAAE,EAAE;MACpDtB,UAAU,CAACwB,IAAI,CAAC;QACdrB,IAAI,EAAE,mBAAmBmB,CAAC,gCAAgC;QAC1DlB,MAAM,EAAEA,CAACD,IAAS,EAAEZ,IAAS,EAAEc,GAAQ,KAAI;UACzC,IAAIF,IAAI,IAAI,IAAI,EAAEA,IAAI,GAAG,iBAAiB;UAC1C,OAAO,sBAAsB,IAAI,CAACtC,eAAe,CAAC4D,oBAAoB,CACpEtB,IAAI,CACL,sBAAsBA,IAAI,SAAS;QACtC;OACD,CAAC;MAGF,IAAI,IAAI,CAACvD,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,mBAAmB,EAAE;QAC9DmD,UAAU,CAACwB,IAAI,CAAC;UACdrB,IAAI,EAAE,mBAAmBmB,CAAC,yBAAyB;UACnDlB,MAAM,EAAEA,CAACD,IAAS,EAAEZ,IAAS,EAAEc,GAAQ,KAAI;YACzC,OAAO,IAAI,CAACxC,eAAe,CAAC6D,oBAAoB,CAACvB,IAAI,CAAC;UACxD;SACD,CAAC;;;IAIN;IACAF,WAAW,GAAGA,WAAW,CAAC0B,MAAM,CAAC3B,UAAU,CAAC;IAE5C,IAAI,CAACrB,SAAS,GAAG;MACfiD,GAAG,EAAE,IAAI,CAAC/D,eAAe,CAACgE,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5CD,oBAAoB,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS;QAC/C,IAAI,CAAC7D,KAAK,CACP8D,GAAG,CACF,GAAGnG,WAAW,CAACoG,MAAM,iBAAiB,EACtCJ,oBAAoB,CACrB,CACAK,SAAS,CAAEC,IAAS,IAAI;UACvBL,QAAQ,CAAC;YACPM,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCtC,IAAI,EAAEoC,IAAI,CAACpC;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACD;MACAuC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAAC9E,eAAe,CAACgE,iBAAiB,CAACe,IAAI;MACrD;MACA;MACA;MACA;MACA;MACAC,OAAO,EAAE5C,WAAW;MACpB6C,OAAO,EAAE;QACPlB,GAAG,EAAE,IAAI,CAAC/D,eAAe,CAACgE,iBAAiB,CAACiB,OAAO,CAAClB,GAAG;QACvDkB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,2CAA2C,IAAI,CAACnF,iBAAiB,CAACoF,OAAO,CAC7E,cAAc,CACf,EAAE;UACHC,MAAM,EAAE,OAAO;UACfC,MAAM;YAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAMrD,IAAI,GAAGmD,EAAE,CAACR,OAAO,CAACW,UAAU,EAAE;cACpC,MAAM1D,KAAI,CAACtB,cAAc,CAACiF,WAAW,CAACvD,IAAI,EAAE,aAAa,CAAC;YAC5D,CAAC;YAAA,gBAAA+C,OAAAS,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,gBAAgBA,CAACC,SAAS;IACxB,IAAI,CAACpG,cAAc,CAACmG,gBAAgB,CAACC,SAAS,CAAC,CAAC5B,SAAS,CAAE6B,GAAG,IAAI;MAChE,IAAI,CAACtF,eAAe,GAAGsF,GAAG,CAAChE,IAAI;MAC/B,IAAI,CAACL,UAAU,EAAE;MACjB,IAAI,CAAClB,SAAS,CAACwF,IAAI,CAAC,IAAI,CAACzF,SAAS,CAAC;IACrC,CAAC,CAAC;EACJ;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAACoE,gBAAgB,CAAC,CAAC,CAAC;IACxB,IAAI,CAACnE,UAAU,EAAE;IAEjB,IAAI,CAAC3B,gBAAgB,CAACvB,YAAY,CAAC0F,SAAS,CAAE6B,GAAG,IAAI;MACnD,IAAI,CAACvH,YAAY,GAAGuH,GAAG;IACzB,CAAC,CAAC;EAEJ;EAEAE,eAAeA,CAAA,GACf;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC1F,SAAS,CAAC2F,WAAW,EAAE;EAC9B;EAAC,QAAAC,CAAA;qBAnNUhH,gBAAgB,EAAArB,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA1I,EAAA,CAAAsI,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA5I,EAAA,CAAAsI,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9I,EAAA,CAAAsI,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAhJ,EAAA,CAAAsI,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAAlJ,EAAA,CAAAsI,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAApJ,EAAA,CAAAsI,iBAAA,CAAAe,EAAA,CAAAC,WAAA,GAAAtJ,EAAA,CAAAsI,iBAAA,CAAAiB,EAAA,CAAAC,QAAA,GAAAxJ,EAAA,CAAAsI,iBAAA,CAAAmB,GAAA,CAAAC,eAAA,GAAA1J,EAAA,CAAAsI,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAAsI,iBAAA,CAAAtI,EAAA,CAAA6J,SAAA,GAAA7J,EAAA,CAAAsI,iBAAA,CAAAwB,GAAA,CAAAC,UAAA,GAAA/J,EAAA,CAAAsI,iBAAA,CAAAqB,GAAA,CAAAK,MAAA,GAAAhK,EAAA,CAAAsI,iBAAA,CAAA2B,GAAA,CAAAC,KAAA,GAAAlK,EAAA,CAAAsI,iBAAA,CAAA6B,GAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAAhBhJ,gBAAgB;IAAAiJ,SAAA;IAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAwBhB5K,kBAAkB;;;;;;;;;;;;;;QC1D/BG,EAAA,CAAAE,cAAA,aAA+C;QAG3CF,EAAA,CAAA2K,SAAA,4BAAyE;QACzE3K,EAAA,CAAAE,cAAA,aAA8B;QAC5BF,EAAA,CAAA2K,SAAA,aAA+B;QAC/B3K,EAAA,CAAAE,cAAA,eAKC;QAGqBF,EAAA,CAAAG,MAAA,GAAyB;;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC9CJ,EAAA,CAAAE,cAAA,aAAgB;QAAAF,EAAA,CAAAG,MAAA,IAA6B;;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAClDJ,EAAA,CAAAE,cAAA,aAAgB;QAAAF,EAAA,CAAAG,MAAA,IAAuB;;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC5CJ,EAAA,CAAAE,cAAA,aAAgB;QAAAF,EAAA,CAAAG,MAAA,IAA0B;;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC/CJ,EAAA,CAAAE,cAAA,aAAgB;QAAAF,EAAA,CAAAG,MAAA,IAAmC;;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACxDJ,EAAA,CAAAe,UAAA,KAAA6J,yCAAA,0BAOe;QACjB5K,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAE,cAAA,UAAI;QACFF,EAAA,CAAAe,UAAA,KAAA8J,yCAAA,0BAYe;QACjB7K,EAAA,CAAAI,YAAA,EAAK;;;QAvCSJ,EAAA,CAAAM,SAAA,GAA+B;QAA/BN,EAAA,CAAAiB,UAAA,kBAAAyJ,GAAA,CAAA1H,aAAA,CAA+B;QAK/ChD,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAiB,UAAA,cAAAyJ,GAAA,CAAAlI,SAAA,CAAuB,cAAAkI,GAAA,CAAAjI,SAAA;QAMHzC,EAAA,CAAAM,SAAA,GAAyB;QAAzBN,EAAA,CAAA8K,iBAAA,CAAA9K,EAAA,CAAAc,WAAA,kBAAyB;QACzBd,EAAA,CAAAM,SAAA,GAA6B;QAA7BN,EAAA,CAAA8K,iBAAA,CAAA9K,EAAA,CAAAc,WAAA,sBAA6B;QAC7Bd,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAA8K,iBAAA,CAAA9K,EAAA,CAAAc,WAAA,gBAAuB;QACvBd,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAA8K,iBAAA,CAAA9K,EAAA,CAAAc,WAAA,mBAA0B;QAC1Bd,EAAA,CAAAM,SAAA,GAAmC;QAAnCN,EAAA,CAAA8K,iBAAA,CAAA9K,EAAA,CAAAc,WAAA,4BAAmC;QAClBd,EAAA,CAAAM,SAAA,GAAkB;QAAlBN,EAAA,CAAAiB,UAAA,YAAAyJ,GAAA,CAAAhI,eAAA,CAAkB;QAUlB1C,EAAA,CAAAM,SAAA,GAAkB;QAAlBN,EAAA,CAAAiB,UAAA,YAAAyJ,GAAA,CAAAhI,eAAA,CAAkB", "names": ["FormGroup", "DataTableDirective", "environment", "Subject", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵattribute", "ctx_r0", "initSettings", "is_payment_required", "ɵɵtextInterpolate1", "season_r2", "name", "ɵɵpipeBind1", "ɵɵtemplate", "ReportsComponent_ng_container_25_th_4_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "ctx_r1", "ReportsComponent", "constructor", "_registrationService", "_sendMessageService", "_translateService", "_commonsService", "_seasonService", "_authService", "_userService", "_clubService", "_modalService", "_settingsService", "route", "_render", "_http", "_router", "_titleService", "_exportService", "dtElement", "dtOptions", "dtTrigger", "seasons_reports", "form", "model", "fields", "setTitle", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "isLink", "getInitSettingFromLocalStorage", "console", "log", "preloadOptions", "buildTable", "_this", "col_season", "col_default", "sortable", "data", "render", "row", "img", "document", "createElement", "src", "id", "style", "width", "height", "objectFit", "backgroundColor", "onerror", "$", "attr", "outerHTML", "fullName", "getBadgeValidateStatus", "i", "length", "push", "getBadgeRegistration", "getBadgeClassPayment", "concat", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "season_id", "get", "apiUrl", "subscribe", "resp", "recordsTotal", "recordsFiltered", "scrollX", "language", "lang", "columns", "buttons", "text", "instant", "extend", "action", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "exportExcel", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "getLatestSeasons", "no_season", "res", "next", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "RegistrationService", "i2", "SendMessagesService", "i3", "TranslateService", "i4", "CommonsService", "i5", "SeasonService", "i6", "AuthService", "i7", "UserService", "i8", "ClubService", "i9", "NgbModal", "i10", "SettingsService", "i11", "ActivatedRoute", "Renderer2", "i12", "HttpClient", "Router", "i13", "Title", "i14", "ExportService", "_2", "selectors", "viewQuery", "ReportsComponent_Query", "rf", "ctx", "ɵɵelement", "ReportsComponent_ng_container_23_Template", "ReportsComponent_ng_container_25_Template", "ɵɵtextInterpolate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\reports\\reports.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\reports\\reports.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  ViewEncapsulation\r\n} from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { SendMessagesService } from 'app/services/send-messages.service';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { ExportService } from 'app/services/export.service';\r\nimport { SettingsService } from '../../services/settings.service';\r\n\r\n@Component({\r\n  selector: 'app-reports',\r\n  templateUrl: './reports.component.html',\r\n  styleUrls: ['./reports.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class ReportsComponent implements OnInit {\r\n  public contentHeader: object;\r\n\r\n  constructor(\r\n    public _registrationService: RegistrationService,\r\n    public _sendMessageService: SendMessagesService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _seasonService: SeasonService,\r\n    public _authService: AuthService,\r\n    public _userService: UserService,\r\n    public _clubService: ClubService,\r\n    public _modalService: NgbModal,\r\n    public _settingsService: SettingsService,\r\n    private route: ActivatedRoute,\r\n    public _render: Renderer2,\r\n    public _http: HttpClient,\r\n    public _router: Router,\r\n    public _titleService: Title,\r\n    private _exportService: ExportService\r\n  ) {\r\n    this._titleService.setTitle('Reports');\r\n  }\r\n\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @ViewChild('modalForm') modalForm: any;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  private unlistener: () => void;\r\n  season_id: any;\r\n  modalRef: any;\r\n  seasons_reports = [];\r\n  form = new FormGroup({});\r\n  model: any = {};\r\n  fields: FormlyFieldConfig[] = [];\r\n\r\n  initSettings = null;\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: 'Reports',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Registrations',\r\n            isLink: false\r\n          },\r\n          {\r\n            name: 'Reports',\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n\r\n    this.initSettings = this._settingsService.getInitSettingFromLocalStorage();\r\n    console.log('this.initSettings', this.initSettings);\r\n\r\n\r\n    this.preloadOptions();\r\n  }\r\n\r\n  buildTable() {\r\n    let col_season = [];\r\n    let col_default = [\r\n      {\r\n        sortable: false,\r\n        data: 'photo',\r\n        render: function (data: any, type: any, row: any) {\r\n          //create image\r\n          let img = document.createElement('img');\r\n          img.src = data;\r\n          img.id = `img-${row.id}`;\r\n          img.style.width = '50px';\r\n          img.style.height = 'auto';\r\n          img.style.objectFit = 'cover';\r\n          img.style.backgroundColor = '#fff';\r\n          img.style.objectFit = 'cover';\r\n          if (data == null) {\r\n            img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n          }\r\n          // check get image error\r\n          img.onerror = function () {\r\n            img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n            // set src by row id\r\n            $(`#img-${row.id}`).attr('src', img.src);\r\n          };\r\n          return img.outerHTML;\r\n        }\r\n      },\r\n      {\r\n        data: 'users',\r\n\r\n        render: (data: any, type: any, row: any) => {\r\n          return this._userService.fullName(data);\r\n        }\r\n      },\r\n      {\r\n        data: 'dob'\r\n      },\r\n      {\r\n        data: 'gender'\r\n      },\r\n      {\r\n        data: 'validate_status',\r\n\r\n        render: (data: any, type: any, row: any) => {\r\n          return this._commonsService.getBadgeValidateStatus(data);\r\n        }\r\n      }\r\n    ];\r\n\r\n    for (let i = 0; i < this.seasons_reports.length; i++) {\r\n      col_season.push({\r\n        data: `seasons_reports.${i}.registrations.approval_status`,\r\n        render: (data: any, type: any, row: any) => {\r\n          if (data == null) data = 'No registration';\r\n          return `<span class=\"badge ${this._commonsService.getBadgeRegistration(\r\n            data\r\n          )} text-capitalize\"> ${data}</span>`;\r\n        }\r\n      });\r\n\r\n\r\n      if (this.initSettings && this.initSettings.is_payment_required) {\r\n        col_season.push({\r\n          data: `seasons_reports.${i}.payment_details.status`,\r\n          render: (data: any, type: any, row: any) => {\r\n            return this._commonsService.getBadgeClassPayment(data);\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // merge columns\r\n    col_default = col_default.concat(col_season);\r\n\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        dataTablesParameters.season_id = this.season_id;\r\n        this._http\r\n          .get<any>(\r\n            `${environment.apiUrl}/players/report`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      // responsive: true,\r\n      scrollX: true,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      // columnDefs: [\r\n      //   { targets: 0, responsivePriority: 1 },\r\n      //   { targets: -1, responsivePriority: 2 },\r\n      //   { targets: -2, responsivePriority: 3 },\r\n      // ],\r\n      columns: col_default,\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa fa-file-excel-o mr-1\"></i> ${this._translateService.instant(\r\n              'Export Excel'\r\n            )}`,\r\n            extend: 'excel',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              await this._exportService.exportExcel(data, 'Report.xlsx');\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  getLatestSeasons(no_season) {\r\n    this._seasonService.getLatestSeasons(no_season).subscribe((res) => {\r\n      this.seasons_reports = res.data;\r\n      this.buildTable();\r\n      this.dtTrigger.next(this.dtOptions);\r\n    });\r\n  }\r\n\r\n  preloadOptions() {\r\n    this.getLatestSeasons(3);\r\n    this.buildTable();\r\n\r\n    this._settingsService.initSettings.subscribe((res) => {\r\n      this.initSettings = res;\r\n    });\r\n\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n  }\r\n\r\n\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div id=\"report\" class=\"card\">\r\n      <div class=\"card-header\"></div>\r\n      <table\r\n        datatable\r\n        [dtOptions]=\"dtOptions\"\r\n        [dtTrigger]=\"dtTrigger\"\r\n        class=\"table row-border hover mt-2\"\r\n      >\r\n        <thead>\r\n          <tr>\r\n            <th rowspan=\"2\">{{ 'Photo' | translate }}</th>\r\n            <th rowspan=\"2\">{{ 'Full Name' | translate }}</th>\r\n            <th rowspan=\"2\">{{ 'Dob' | translate }}</th>\r\n            <th rowspan=\"2\">{{ 'Gender' | translate }}</th>\r\n            <th rowspan=\"2\">{{ 'Validate Status' | translate }}</th>\r\n            <ng-container *ngFor=\"let season of seasons_reports\">\r\n              <th\r\n                [attr.colspan]=\"initSettings?.is_payment_required ? 2 : 1\"\r\n                class=\"text-center\"\r\n              >\r\n                {{ season.name }}\r\n              </th>\r\n            </ng-container>\r\n          </tr>\r\n          <tr>\r\n            <ng-container *ngFor=\"let season of seasons_reports\">\r\n              <th\r\n                [ngClass]=\"{\r\n                  'text-start': initSettings?.is_payment_required,\r\n                  'text-center': !initSettings?.is_payment_required\r\n                }\"\r\n              >\r\n                {{ 'Reg.status' | translate }}\r\n              </th>\r\n              <th *ngIf=\"initSettings && initSettings.is_payment_required\">\r\n                {{ 'Inv.status' | translate }}\r\n              </th>\r\n            </ng-container>\r\n          </tr>\r\n        </thead>\r\n      </table>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
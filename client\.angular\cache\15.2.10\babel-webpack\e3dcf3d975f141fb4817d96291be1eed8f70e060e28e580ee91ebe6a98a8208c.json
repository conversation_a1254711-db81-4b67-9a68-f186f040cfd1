{"ast": null, "code": "export class Scheduler {\n  constructor(SchedulerAction, now = Scheduler.now) {\n    this.SchedulerAction = SchedulerAction;\n    this.now = now;\n  }\n  schedule(work, delay = 0, state) {\n    return new this.SchedulerAction(this, work).schedule(state, delay);\n  }\n}\nScheduler.now = () => Date.now();", "map": {"version": 3, "names": ["Scheduler", "constructor", "SchedulerAction", "now", "schedule", "work", "delay", "state", "Date"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/Scheduler.js"], "sourcesContent": ["export class Scheduler {\n    constructor(SchedulerAction, now = Scheduler.now) {\n        this.SchedulerAction = SchedulerAction;\n        this.now = now;\n    }\n    schedule(work, delay = 0, state) {\n        return new this.SchedulerAction(this, work).schedule(state, delay);\n    }\n}\nScheduler.now = () => Date.now();\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,CAAC;EACnBC,WAAWA,CAACC,eAAe,EAAEC,GAAG,GAAGH,SAAS,CAACG,GAAG,EAAE;IAC9C,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAC,QAAQA,CAACC,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,KAAK,EAAE;IAC7B,OAAO,IAAI,IAAI,CAACL,eAAe,CAAC,IAAI,EAAEG,IAAI,CAAC,CAACD,QAAQ,CAACG,KAAK,EAAED,KAAK,CAAC;EACtE;AACJ;AACAN,SAAS,CAACG,GAAG,GAAG,MAAMK,IAAI,CAACL,GAAG,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import isFunction from './isFunction.js';\nimport isMasked from './_isMasked.js';\nimport isObject from './isObject.js';\nimport toSource from './_toSource.js';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n  objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' + funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&').replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$');\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\nexport default baseIsNative;", "map": {"version": 3, "names": ["isFunction", "isMasked", "isObject", "toSource", "reRegExpChar", "reIsHostCtor", "funcProto", "Function", "prototype", "objectProto", "Object", "funcToString", "toString", "hasOwnProperty", "reIsNative", "RegExp", "call", "replace", "baseIsNative", "value", "pattern", "test"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_baseIsNative.js"], "sourcesContent": ["import isFunction from './isFunction.js';\nimport isMasked from './_isMasked.js';\nimport isObject from './isObject.js';\nimport toSource from './_toSource.js';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nexport default baseIsNative;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,qBAAqB;;AAExC;AACA,IAAIC,YAAY,GAAG,6BAA6B;;AAEhD;AACA,IAAIC,SAAS,GAAGC,QAAQ,CAACC,SAAS;EAC9BC,WAAW,GAAGC,MAAM,CAACF,SAAS;;AAElC;AACA,IAAIG,YAAY,GAAGL,SAAS,CAACM,QAAQ;;AAErC;AACA,IAAIC,cAAc,GAAGJ,WAAW,CAACI,cAAc;;AAE/C;AACA,IAAIC,UAAU,GAAGC,MAAM,CAAC,GAAG,GACzBJ,YAAY,CAACK,IAAI,CAACH,cAAc,CAAC,CAACI,OAAO,CAACb,YAAY,EAAE,MAAM,CAAC,CAC9Da,OAAO,CAAC,wDAAwD,EAAE,OAAO,CAAC,GAAG,GAChF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAI,CAACjB,QAAQ,CAACiB,KAAK,CAAC,IAAIlB,QAAQ,CAACkB,KAAK,CAAC,EAAE;IACvC,OAAO,KAAK;EACd;EACA,IAAIC,OAAO,GAAGpB,UAAU,CAACmB,KAAK,CAAC,GAAGL,UAAU,GAAGT,YAAY;EAC3D,OAAOe,OAAO,CAACC,IAAI,CAAClB,QAAQ,CAACgB,KAAK,CAAC,CAAC;AACtC;AAEA,eAAeD,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
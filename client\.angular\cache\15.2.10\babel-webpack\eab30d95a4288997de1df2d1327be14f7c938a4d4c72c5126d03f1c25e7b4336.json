{"ast": null, "code": "export const subscribeToArray = array => subscriber => {\n  for (let i = 0, len = array.length; i < len && !subscriber.closed; i++) {\n    subscriber.next(array[i]);\n  }\n  subscriber.complete();\n};", "map": {"version": 3, "names": ["subscribeToArray", "array", "subscriber", "i", "len", "length", "closed", "next", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/subscribeToArray.js"], "sourcesContent": ["export const subscribeToArray = (array) => (subscriber) => {\n    for (let i = 0, len = array.length; i < len && !subscriber.closed; i++) {\n        subscriber.next(array[i]);\n    }\n    subscriber.complete();\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAIC,KAAK,IAAMC,UAAU,IAAK;EACvD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,KAAK,CAACI,MAAM,EAAEF,CAAC,GAAGC,GAAG,IAAI,CAACF,UAAU,CAACI,MAAM,EAAEH,CAAC,EAAE,EAAE;IACpED,UAAU,CAACK,IAAI,CAACN,KAAK,CAACE,CAAC,CAAC,CAAC;EAC7B;EACAD,UAAU,CAACM,QAAQ,CAAC,CAAC;AACzB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
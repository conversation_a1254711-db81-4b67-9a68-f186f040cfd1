{"ast": null, "code": "import { ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, NgZone, Injector } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { authState as authState$1, user as user$1, idToken as idToken$1 } from 'rxfire/auth';\nimport { applyActionCode as applyActionCode$1, beforeAuthStateChanged as beforeAuthStateChanged$1, checkActionCode as checkActionCode$1, confirmPasswordReset as confirmPasswordReset$1, connectAuthEmulator as connectAuthEmulator$1, createUserWithEmailAndPassword as createUserWithEmailAndPassword$1, deleteUser as deleteUser$1, fetchSignInMethodsForEmail as fetchSignInMethodsForEmail$1, getAdditionalUserInfo as getAdditionalUserInfo$1, getAuth as getAuth$1, getIdToken as getIdToken$1, getIdTokenResult as getIdTokenResult$1, getMultiFactorResolver as getMultiFactorResolver$1, getRedirectResult as getRedirectResult$1, initializeAuth as initializeAuth$1, isSignInWithEmailLink as isSignInWithEmailLink$1, linkWithCredential as linkWithCredential$1, linkWithPhoneNumber as linkWithPhoneNumber$1, linkWithPopup as linkWithPopup$1, linkWithRedirect as linkWithRedirect$1, multiFactor as multiFactor$1, onAuthStateChanged as onAuthStateChanged$1, onIdTokenChanged as onIdTokenChanged$1, parseActionCodeURL as parseActionCodeURL$1, reauthenticateWithCredential as reauthenticateWithCredential$1, reauthenticateWithPhoneNumber as reauthenticateWithPhoneNumber$1, reauthenticateWithPopup as reauthenticateWithPopup$1, reauthenticateWithRedirect as reauthenticateWithRedirect$1, reload as reload$1, sendEmailVerification as sendEmailVerification$1, sendPasswordResetEmail as sendPasswordResetEmail$1, sendSignInLinkToEmail as sendSignInLinkToEmail$1, setPersistence as setPersistence$1, signInAnonymously as signInAnonymously$1, signInWithCredential as signInWithCredential$1, signInWithCustomToken as signInWithCustomToken$1, signInWithEmailAndPassword as signInWithEmailAndPassword$1, signInWithEmailLink as signInWithEmailLink$1, signInWithPhoneNumber as signInWithPhoneNumber$1, signInWithPopup as signInWithPopup$1, signInWithRedirect as signInWithRedirect$1, signOut as signOut$1, unlink as unlink$1, updateCurrentUser as updateCurrentUser$1, updateEmail as updateEmail$1, updatePassword as updatePassword$1, updatePhoneNumber as updatePhoneNumber$1, updateProfile as updateProfile$1, useDeviceLanguage as useDeviceLanguage$1, verifyBeforeUpdateEmail as verifyBeforeUpdateEmail$1, verifyPasswordResetCode as verifyPasswordResetCode$1 } from 'firebase/auth';\nexport * from 'firebase/auth';\nconst AUTH_PROVIDER_NAME = 'auth';\nclass Auth {\n  constructor(auth) {\n    return auth;\n  }\n}\nclass AuthInstances {\n  constructor() {\n    return ɵgetAllInstancesOf(AUTH_PROVIDER_NAME);\n  }\n}\nconst authInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(AUTH_PROVIDER_NAME))), distinct());\nconst PROVIDED_AUTH_INSTANCES = new InjectionToken('angularfire2.auth-instances');\nfunction defaultAuthInstanceFactory(provided, defaultApp) {\n  const defaultAuth = ɵgetDefaultInstanceOf(AUTH_PROVIDER_NAME, provided, defaultApp);\n  return defaultAuth && new Auth(defaultAuth);\n}\nfunction authInstanceFactory(fn) {\n  return (zone, injector) => {\n    const auth = zone.runOutsideAngular(() => fn(injector));\n    return new Auth(auth);\n  };\n}\nconst AUTH_INSTANCES_PROVIDER = {\n  provide: AuthInstances,\n  deps: [[new Optional(), PROVIDED_AUTH_INSTANCES]]\n};\nconst DEFAULT_AUTH_INSTANCE_PROVIDER = {\n  provide: Auth,\n  useFactory: defaultAuthInstanceFactory,\n  deps: [[new Optional(), PROVIDED_AUTH_INSTANCES], FirebaseApp]\n};\nclass AuthModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'auth');\n  }\n}\nAuthModule.ɵfac = function AuthModule_Factory(t) {\n  return new (t || AuthModule)();\n};\nAuthModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AuthModule\n});\nAuthModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DEFAULT_AUTH_INSTANCE_PROVIDER, AUTH_INSTANCES_PROVIDER]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_AUTH_INSTANCE_PROVIDER, AUTH_INSTANCES_PROVIDER]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction provideAuth(fn, ...deps) {\n  return {\n    ngModule: AuthModule,\n    providers: [{\n      provide: PROVIDED_AUTH_INSTANCES,\n      useFactory: authInstanceFactory(fn),\n      multi: true,\n      deps: [NgZone, Injector, ɵAngularFireSchedulers, FirebaseApps, [new Optional(), AppCheckInstances], ...deps]\n    }]\n  };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst authState = ɵzoneWrap(authState$1, true);\nconst user = ɵzoneWrap(user$1, true);\nconst idToken = ɵzoneWrap(idToken$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst applyActionCode = ɵzoneWrap(applyActionCode$1, true);\nconst beforeAuthStateChanged = ɵzoneWrap(beforeAuthStateChanged$1, true);\nconst checkActionCode = ɵzoneWrap(checkActionCode$1, true);\nconst confirmPasswordReset = ɵzoneWrap(confirmPasswordReset$1, true);\nconst connectAuthEmulator = ɵzoneWrap(connectAuthEmulator$1, true);\nconst createUserWithEmailAndPassword = ɵzoneWrap(createUserWithEmailAndPassword$1, true);\nconst deleteUser = ɵzoneWrap(deleteUser$1, true);\nconst fetchSignInMethodsForEmail = ɵzoneWrap(fetchSignInMethodsForEmail$1, true);\nconst getAdditionalUserInfo = ɵzoneWrap(getAdditionalUserInfo$1, true);\nconst getAuth = ɵzoneWrap(getAuth$1, true);\nconst getIdToken = ɵzoneWrap(getIdToken$1, true);\nconst getIdTokenResult = ɵzoneWrap(getIdTokenResult$1, true);\nconst getMultiFactorResolver = ɵzoneWrap(getMultiFactorResolver$1, true);\nconst getRedirectResult = ɵzoneWrap(getRedirectResult$1, true);\nconst initializeAuth = ɵzoneWrap(initializeAuth$1, true);\nconst isSignInWithEmailLink = ɵzoneWrap(isSignInWithEmailLink$1, true);\nconst linkWithCredential = ɵzoneWrap(linkWithCredential$1, true);\nconst linkWithPhoneNumber = ɵzoneWrap(linkWithPhoneNumber$1, true);\nconst linkWithPopup = ɵzoneWrap(linkWithPopup$1, true);\nconst linkWithRedirect = ɵzoneWrap(linkWithRedirect$1, true);\nconst multiFactor = ɵzoneWrap(multiFactor$1, true);\nconst onAuthStateChanged = ɵzoneWrap(onAuthStateChanged$1, true);\nconst onIdTokenChanged = ɵzoneWrap(onIdTokenChanged$1, true);\nconst parseActionCodeURL = ɵzoneWrap(parseActionCodeURL$1, true);\nconst reauthenticateWithCredential = ɵzoneWrap(reauthenticateWithCredential$1, true);\nconst reauthenticateWithPhoneNumber = ɵzoneWrap(reauthenticateWithPhoneNumber$1, true);\nconst reauthenticateWithPopup = ɵzoneWrap(reauthenticateWithPopup$1, true);\nconst reauthenticateWithRedirect = ɵzoneWrap(reauthenticateWithRedirect$1, true);\nconst reload = ɵzoneWrap(reload$1, true);\nconst sendEmailVerification = ɵzoneWrap(sendEmailVerification$1, true);\nconst sendPasswordResetEmail = ɵzoneWrap(sendPasswordResetEmail$1, true);\nconst sendSignInLinkToEmail = ɵzoneWrap(sendSignInLinkToEmail$1, true);\nconst setPersistence = ɵzoneWrap(setPersistence$1, true);\nconst signInAnonymously = ɵzoneWrap(signInAnonymously$1, true);\nconst signInWithCredential = ɵzoneWrap(signInWithCredential$1, true);\nconst signInWithCustomToken = ɵzoneWrap(signInWithCustomToken$1, true);\nconst signInWithEmailAndPassword = ɵzoneWrap(signInWithEmailAndPassword$1, true);\nconst signInWithEmailLink = ɵzoneWrap(signInWithEmailLink$1, true);\nconst signInWithPhoneNumber = ɵzoneWrap(signInWithPhoneNumber$1, true);\nconst signInWithPopup = ɵzoneWrap(signInWithPopup$1, true);\nconst signInWithRedirect = ɵzoneWrap(signInWithRedirect$1, true);\nconst signOut = ɵzoneWrap(signOut$1, true);\nconst unlink = ɵzoneWrap(unlink$1, true);\nconst updateCurrentUser = ɵzoneWrap(updateCurrentUser$1, true);\nconst updateEmail = ɵzoneWrap(updateEmail$1, true);\nconst updatePassword = ɵzoneWrap(updatePassword$1, true);\nconst updatePhoneNumber = ɵzoneWrap(updatePhoneNumber$1, true);\nconst updateProfile = ɵzoneWrap(updateProfile$1, true);\nconst useDeviceLanguage = ɵzoneWrap(useDeviceLanguage$1, true);\nconst verifyBeforeUpdateEmail = ɵzoneWrap(verifyBeforeUpdateEmail$1, true);\nconst verifyPasswordResetCode = ɵzoneWrap(verifyPasswordResetCode$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Auth, AuthInstances, AuthModule, applyActionCode, authInstance$, authState, beforeAuthStateChanged, checkActionCode, confirmPasswordReset, connectAuthEmulator, createUserWithEmailAndPassword, deleteUser, fetchSignInMethodsForEmail, getAdditionalUserInfo, getAuth, getIdToken, getIdTokenResult, getMultiFactorResolver, getRedirectResult, idToken, initializeAuth, isSignInWithEmailLink, linkWithCredential, linkWithPhoneNumber, linkWithPopup, linkWithRedirect, multiFactor, onAuthStateChanged, onIdTokenChanged, parseActionCodeURL, provideAuth, reauthenticateWithCredential, reauthenticateWithPhoneNumber, reauthenticateWithPopup, reauthenticateWithRedirect, reload, sendEmailVerification, sendPasswordResetEmail, sendSignInLinkToEmail, setPersistence, signInAnonymously, signInWithCredential, signInWithCustomToken, signInWithEmailAndPassword, signInWithEmailLink, signInWithPhoneNumber, signInWithPopup, signInWithRedirect, signOut, unlink, updateCurrentUser, updateEmail, updatePassword, updatePhoneNumber, updateProfile, useDeviceLanguage, user, verifyBeforeUpdateEmail, verifyPasswordResetCode };", "map": {"version": 3, "names": ["ɵgetAllInstancesOf", "ɵgetDefaultInstanceOf", "VERSION", "ɵAngularFireSchedulers", "ɵzoneWrap", "timer", "from", "concatMap", "distinct", "i0", "InjectionToken", "Optional", "NgModule", "NgZone", "Injector", "FirebaseApp", "FirebaseApps", "registerVersion", "AppCheckInstances", "authState", "authState$1", "user", "user$1", "idToken", "idToken$1", "applyActionCode", "applyActionCode$1", "beforeAuthStateChanged", "beforeAuthStateChanged$1", "checkActionCode", "checkActionCode$1", "confirmPasswordReset", "confirmPasswordReset$1", "connectAuthEmulator", "connectAuthEmulator$1", "createUserWithEmailAndPassword", "createUserWithEmailAndPassword$1", "deleteUser", "deleteUser$1", "fetchSignInMethodsForEmail", "fetchSignInMethodsForEmail$1", "getAdditionalUserInfo", "getAdditionalUserInfo$1", "getAuth", "getAuth$1", "getIdToken", "getIdToken$1", "getIdTokenResult", "getIdTokenResult$1", "getMultiFactorResolver", "getMultiFactorResolver$1", "getRedirectResult", "getRedirectResult$1", "initializeAuth", "initializeAuth$1", "isSignInWithEmailLink", "isSignInWithEmailLink$1", "linkWithCredential", "linkWithCredential$1", "linkWithPhoneNumber", "linkWithPhoneNumber$1", "linkWithPopup", "linkWithPopup$1", "linkWithRedirect", "linkWithRedirect$1", "multiFactor", "multiFactor$1", "onAuthStateChanged", "onAuthStateChanged$1", "onIdTokenChanged", "onIdTokenChanged$1", "parseActionCodeURL", "parseActionCodeURL$1", "reauthenticateWithCredential", "reauthenticateWithCredential$1", "reauthenticateWithPhoneNumber", "reauthenticateWithPhoneNumber$1", "reauthenticateWithPopup", "reauthenticateWithPopup$1", "reauthenticateWithRedirect", "reauthenticateWithRedirect$1", "reload", "reload$1", "sendEmailVerification", "sendEmailVerification$1", "sendPasswordResetEmail", "sendPasswordResetEmail$1", "sendSignInLinkToEmail", "sendSignInLinkToEmail$1", "setPersistence", "setPersistence$1", "signInAnonymously", "signInAnonymously$1", "signInWithCredential", "signInWithCredential$1", "signInWithCustomToken", "signInWithCustomToken$1", "signInWithEmailAndPassword", "signInWithEmailAndPassword$1", "signInWithEmailLink", "signInWithEmailLink$1", "signInWithPhoneNumber", "signInWithPhoneNumber$1", "signInWithPopup", "signInWithPopup$1", "signInWithRedirect", "signInWithRedirect$1", "signOut", "signOut$1", "unlink", "unlink$1", "updateCurrentUser", "updateCurrentUser$1", "updateEmail", "updateEmail$1", "updatePassword", "updatePassword$1", "updatePhoneNumber", "updatePhoneNumber$1", "updateProfile", "updateProfile$1", "useDeviceLanguage", "useDeviceLanguage$1", "verifyBeforeUpdateEmail", "verifyBeforeUpdateEmail$1", "verifyPasswordResetCode", "verifyPasswordResetCode$1", "AUTH_PROVIDER_NAME", "<PERSON><PERSON>", "constructor", "auth", "AuthInstances", "authInstance$", "pipe", "PROVIDED_AUTH_INSTANCES", "defaultAuthInstanceFactory", "provided", "defaultApp", "defaultAuth", "authInstanceFactory", "fn", "zone", "injector", "runOutsideAngular", "AUTH_INSTANCES_PROVIDER", "provide", "deps", "DEFAULT_AUTH_INSTANCE_PROVIDER", "useFactory", "AuthModule", "full", "ɵfac", "AuthModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "ngDevMode", "ɵsetClassMetadata", "args", "provideAuth", "ngModule", "multi"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/fesm2015/angular-fire-auth.js"], "sourcesContent": ["import { ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, NgZone, Injector } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { authState as authState$1, user as user$1, idToken as idToken$1 } from 'rxfire/auth';\nimport { applyActionCode as applyActionCode$1, beforeAuthStateChanged as beforeAuthStateChanged$1, checkActionCode as checkActionCode$1, confirmPasswordReset as confirmPasswordReset$1, connectAuthEmulator as connectAuthEmulator$1, createUserWithEmailAndPassword as createUserWithEmailAndPassword$1, deleteUser as deleteUser$1, fetchSignInMethodsForEmail as fetchSignInMethodsForEmail$1, getAdditionalUserInfo as getAdditionalUserInfo$1, getAuth as getAuth$1, getIdToken as getIdToken$1, getIdTokenResult as getIdTokenResult$1, getMultiFactorResolver as getMultiFactorResolver$1, getRedirectResult as getRedirectResult$1, initializeAuth as initializeAuth$1, isSignInWithEmailLink as isSignInWithEmailLink$1, linkWithCredential as linkWithCredential$1, linkWithPhoneNumber as linkWithPhoneNumber$1, linkWithPopup as linkWithPopup$1, linkWithRedirect as linkWithRedirect$1, multiFactor as multiFactor$1, onAuthStateChanged as onAuthStateChanged$1, onIdTokenChanged as onIdTokenChanged$1, parseActionCodeURL as parseActionCodeURL$1, reauthenticateWithCredential as reauthenticateWithCredential$1, reauthenticateWithPhoneNumber as reauthenticateWithPhoneNumber$1, reauthenticateWithPopup as reauthenticateWithPopup$1, reauthenticateWithRedirect as reauthenticateWithRedirect$1, reload as reload$1, sendEmailVerification as sendEmailVerification$1, sendPasswordResetEmail as sendPasswordResetEmail$1, sendSignInLinkToEmail as sendSignInLinkToEmail$1, setPersistence as setPersistence$1, signInAnonymously as signInAnonymously$1, signInWithCredential as signInWithCredential$1, signInWithCustomToken as signInWithCustomToken$1, signInWithEmailAndPassword as signInWithEmailAndPassword$1, signInWithEmailLink as signInWithEmailLink$1, signInWithPhoneNumber as signInWithPhoneNumber$1, signInWithPopup as signInWithPopup$1, signInWithRedirect as signInWithRedirect$1, signOut as signOut$1, unlink as unlink$1, updateCurrentUser as updateCurrentUser$1, updateEmail as updateEmail$1, updatePassword as updatePassword$1, updatePhoneNumber as updatePhoneNumber$1, updateProfile as updateProfile$1, useDeviceLanguage as useDeviceLanguage$1, verifyBeforeUpdateEmail as verifyBeforeUpdateEmail$1, verifyPasswordResetCode as verifyPasswordResetCode$1 } from 'firebase/auth';\nexport * from 'firebase/auth';\n\nconst AUTH_PROVIDER_NAME = 'auth';\nclass Auth {\n    constructor(auth) {\n        return auth;\n    }\n}\nclass AuthInstances {\n    constructor() {\n        return ɵgetAllInstancesOf(AUTH_PROVIDER_NAME);\n    }\n}\nconst authInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(AUTH_PROVIDER_NAME))), distinct());\n\nconst PROVIDED_AUTH_INSTANCES = new InjectionToken('angularfire2.auth-instances');\nfunction defaultAuthInstanceFactory(provided, defaultApp) {\n    const defaultAuth = ɵgetDefaultInstanceOf(AUTH_PROVIDER_NAME, provided, defaultApp);\n    return defaultAuth && new Auth(defaultAuth);\n}\nfunction authInstanceFactory(fn) {\n    return (zone, injector) => {\n        const auth = zone.runOutsideAngular(() => fn(injector));\n        return new Auth(auth);\n    };\n}\nconst AUTH_INSTANCES_PROVIDER = {\n    provide: AuthInstances,\n    deps: [\n        [new Optional(), PROVIDED_AUTH_INSTANCES],\n    ]\n};\nconst DEFAULT_AUTH_INSTANCE_PROVIDER = {\n    provide: Auth,\n    useFactory: defaultAuthInstanceFactory,\n    deps: [\n        [new Optional(), PROVIDED_AUTH_INSTANCES],\n        FirebaseApp,\n    ]\n};\nclass AuthModule {\n    constructor() {\n        registerVersion('angularfire', VERSION.full, 'auth');\n    }\n}\nAuthModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AuthModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAuthModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AuthModule });\nAuthModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AuthModule, providers: [\n        DEFAULT_AUTH_INSTANCE_PROVIDER,\n        AUTH_INSTANCES_PROVIDER,\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AuthModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        DEFAULT_AUTH_INSTANCE_PROVIDER,\n                        AUTH_INSTANCES_PROVIDER,\n                    ]\n                }]\n        }], ctorParameters: function () { return []; } });\nfunction provideAuth(fn, ...deps) {\n    return {\n        ngModule: AuthModule,\n        providers: [{\n                provide: PROVIDED_AUTH_INSTANCES,\n                useFactory: authInstanceFactory(fn),\n                multi: true,\n                deps: [\n                    NgZone,\n                    Injector,\n                    ɵAngularFireSchedulers,\n                    FirebaseApps,\n                    [new Optional(), AppCheckInstances],\n                    ...deps,\n                ]\n            }]\n    };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst authState = ɵzoneWrap(authState$1, true);\nconst user = ɵzoneWrap(user$1, true);\nconst idToken = ɵzoneWrap(idToken$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst applyActionCode = ɵzoneWrap(applyActionCode$1, true);\nconst beforeAuthStateChanged = ɵzoneWrap(beforeAuthStateChanged$1, true);\nconst checkActionCode = ɵzoneWrap(checkActionCode$1, true);\nconst confirmPasswordReset = ɵzoneWrap(confirmPasswordReset$1, true);\nconst connectAuthEmulator = ɵzoneWrap(connectAuthEmulator$1, true);\nconst createUserWithEmailAndPassword = ɵzoneWrap(createUserWithEmailAndPassword$1, true);\nconst deleteUser = ɵzoneWrap(deleteUser$1, true);\nconst fetchSignInMethodsForEmail = ɵzoneWrap(fetchSignInMethodsForEmail$1, true);\nconst getAdditionalUserInfo = ɵzoneWrap(getAdditionalUserInfo$1, true);\nconst getAuth = ɵzoneWrap(getAuth$1, true);\nconst getIdToken = ɵzoneWrap(getIdToken$1, true);\nconst getIdTokenResult = ɵzoneWrap(getIdTokenResult$1, true);\nconst getMultiFactorResolver = ɵzoneWrap(getMultiFactorResolver$1, true);\nconst getRedirectResult = ɵzoneWrap(getRedirectResult$1, true);\nconst initializeAuth = ɵzoneWrap(initializeAuth$1, true);\nconst isSignInWithEmailLink = ɵzoneWrap(isSignInWithEmailLink$1, true);\nconst linkWithCredential = ɵzoneWrap(linkWithCredential$1, true);\nconst linkWithPhoneNumber = ɵzoneWrap(linkWithPhoneNumber$1, true);\nconst linkWithPopup = ɵzoneWrap(linkWithPopup$1, true);\nconst linkWithRedirect = ɵzoneWrap(linkWithRedirect$1, true);\nconst multiFactor = ɵzoneWrap(multiFactor$1, true);\nconst onAuthStateChanged = ɵzoneWrap(onAuthStateChanged$1, true);\nconst onIdTokenChanged = ɵzoneWrap(onIdTokenChanged$1, true);\nconst parseActionCodeURL = ɵzoneWrap(parseActionCodeURL$1, true);\nconst reauthenticateWithCredential = ɵzoneWrap(reauthenticateWithCredential$1, true);\nconst reauthenticateWithPhoneNumber = ɵzoneWrap(reauthenticateWithPhoneNumber$1, true);\nconst reauthenticateWithPopup = ɵzoneWrap(reauthenticateWithPopup$1, true);\nconst reauthenticateWithRedirect = ɵzoneWrap(reauthenticateWithRedirect$1, true);\nconst reload = ɵzoneWrap(reload$1, true);\nconst sendEmailVerification = ɵzoneWrap(sendEmailVerification$1, true);\nconst sendPasswordResetEmail = ɵzoneWrap(sendPasswordResetEmail$1, true);\nconst sendSignInLinkToEmail = ɵzoneWrap(sendSignInLinkToEmail$1, true);\nconst setPersistence = ɵzoneWrap(setPersistence$1, true);\nconst signInAnonymously = ɵzoneWrap(signInAnonymously$1, true);\nconst signInWithCredential = ɵzoneWrap(signInWithCredential$1, true);\nconst signInWithCustomToken = ɵzoneWrap(signInWithCustomToken$1, true);\nconst signInWithEmailAndPassword = ɵzoneWrap(signInWithEmailAndPassword$1, true);\nconst signInWithEmailLink = ɵzoneWrap(signInWithEmailLink$1, true);\nconst signInWithPhoneNumber = ɵzoneWrap(signInWithPhoneNumber$1, true);\nconst signInWithPopup = ɵzoneWrap(signInWithPopup$1, true);\nconst signInWithRedirect = ɵzoneWrap(signInWithRedirect$1, true);\nconst signOut = ɵzoneWrap(signOut$1, true);\nconst unlink = ɵzoneWrap(unlink$1, true);\nconst updateCurrentUser = ɵzoneWrap(updateCurrentUser$1, true);\nconst updateEmail = ɵzoneWrap(updateEmail$1, true);\nconst updatePassword = ɵzoneWrap(updatePassword$1, true);\nconst updatePhoneNumber = ɵzoneWrap(updatePhoneNumber$1, true);\nconst updateProfile = ɵzoneWrap(updateProfile$1, true);\nconst useDeviceLanguage = ɵzoneWrap(useDeviceLanguage$1, true);\nconst verifyBeforeUpdateEmail = ɵzoneWrap(verifyBeforeUpdateEmail$1, true);\nconst verifyPasswordResetCode = ɵzoneWrap(verifyPasswordResetCode$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Auth, AuthInstances, AuthModule, applyActionCode, authInstance$, authState, beforeAuthStateChanged, checkActionCode, confirmPasswordReset, connectAuthEmulator, createUserWithEmailAndPassword, deleteUser, fetchSignInMethodsForEmail, getAdditionalUserInfo, getAuth, getIdToken, getIdTokenResult, getMultiFactorResolver, getRedirectResult, idToken, initializeAuth, isSignInWithEmailLink, linkWithCredential, linkWithPhoneNumber, linkWithPopup, linkWithRedirect, multiFactor, onAuthStateChanged, onIdTokenChanged, parseActionCodeURL, provideAuth, reauthenticateWithCredential, reauthenticateWithPhoneNumber, reauthenticateWithPopup, reauthenticateWithRedirect, reload, sendEmailVerification, sendPasswordResetEmail, sendSignInLinkToEmail, setPersistence, signInAnonymously, signInWithCredential, signInWithCustomToken, signInWithEmailAndPassword, signInWithEmailLink, signInWithPhoneNumber, signInWithPopup, signInWithRedirect, signOut, unlink, updateCurrentUser, updateEmail, updatePassword, updatePhoneNumber, updateProfile, useDeviceLanguage, user, verifyBeforeUpdateEmail, verifyPasswordResetCode };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,SAAS,QAAQ,eAAe;AACrH,SAASC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACpF,SAASC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAC7D,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,SAAS,IAAIC,WAAW,EAAEC,IAAI,IAAIC,MAAM,EAAEC,OAAO,IAAIC,SAAS,QAAQ,aAAa;AAC5F,SAASC,eAAe,IAAIC,iBAAiB,EAAEC,sBAAsB,IAAIC,wBAAwB,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,oBAAoB,IAAIC,sBAAsB,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,8BAA8B,IAAIC,gCAAgC,EAAEC,UAAU,IAAIC,YAAY,EAAEC,0BAA0B,IAAIC,4BAA4B,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,OAAO,IAAIC,SAAS,EAAEC,UAAU,IAAIC,YAAY,EAAEC,gBAAgB,IAAIC,kBAAkB,EAAEC,sBAAsB,IAAIC,wBAAwB,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,kBAAkB,IAAIC,oBAAoB,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,aAAa,IAAIC,eAAe,EAAEC,gBAAgB,IAAIC,kBAAkB,EAAEC,WAAW,IAAIC,aAAa,EAAEC,kBAAkB,IAAIC,oBAAoB,EAAEC,gBAAgB,IAAIC,kBAAkB,EAAEC,kBAAkB,IAAIC,oBAAoB,EAAEC,4BAA4B,IAAIC,8BAA8B,EAAEC,6BAA6B,IAAIC,+BAA+B,EAAEC,uBAAuB,IAAIC,yBAAyB,EAAEC,0BAA0B,IAAIC,4BAA4B,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,sBAAsB,IAAIC,wBAAwB,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,oBAAoB,IAAIC,sBAAsB,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,0BAA0B,IAAIC,4BAA4B,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,kBAAkB,IAAIC,oBAAoB,EAAEC,OAAO,IAAIC,SAAS,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,WAAW,IAAIC,aAAa,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,aAAa,IAAIC,eAAe,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,uBAAuB,IAAIC,yBAAyB,EAAEC,uBAAuB,IAAIC,yBAAyB,QAAQ,eAAe;AAChtE,cAAc,eAAe;AAE7B,MAAMC,kBAAkB,GAAG,MAAM;AACjC,MAAMC,IAAI,CAAC;EACPC,WAAWA,CAACC,IAAI,EAAE;IACd,OAAOA,IAAI;EACf;AACJ;AACA,MAAMC,aAAa,CAAC;EAChBF,WAAWA,CAAA,EAAG;IACV,OAAOjI,kBAAkB,CAAC+H,kBAAkB,CAAC;EACjD;AACJ;AACA,MAAMK,aAAa,GAAG/H,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACgI,IAAI,CAAC9H,SAAS,CAAC,MAAMD,IAAI,CAACN,kBAAkB,CAAC+H,kBAAkB,CAAC,CAAC,CAAC,EAAEvH,QAAQ,CAAC,CAAC,CAAC;AAEnH,MAAM8H,uBAAuB,GAAG,IAAI5H,cAAc,CAAC,6BAA6B,CAAC;AACjF,SAAS6H,0BAA0BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACtD,MAAMC,WAAW,GAAGzI,qBAAqB,CAAC8H,kBAAkB,EAAES,QAAQ,EAAEC,UAAU,CAAC;EACnF,OAAOC,WAAW,IAAI,IAAIV,IAAI,CAACU,WAAW,CAAC;AAC/C;AACA,SAASC,mBAAmBA,CAACC,EAAE,EAAE;EAC7B,OAAO,CAACC,IAAI,EAAEC,QAAQ,KAAK;IACvB,MAAMZ,IAAI,GAAGW,IAAI,CAACE,iBAAiB,CAAC,MAAMH,EAAE,CAACE,QAAQ,CAAC,CAAC;IACvD,OAAO,IAAId,IAAI,CAACE,IAAI,CAAC;EACzB,CAAC;AACL;AACA,MAAMc,uBAAuB,GAAG;EAC5BC,OAAO,EAAEd,aAAa;EACtBe,IAAI,EAAE,CACF,CAAC,IAAIvI,QAAQ,CAAC,CAAC,EAAE2H,uBAAuB,CAAC;AAEjD,CAAC;AACD,MAAMa,8BAA8B,GAAG;EACnCF,OAAO,EAAEjB,IAAI;EACboB,UAAU,EAAEb,0BAA0B;EACtCW,IAAI,EAAE,CACF,CAAC,IAAIvI,QAAQ,CAAC,CAAC,EAAE2H,uBAAuB,CAAC,EACzCvH,WAAW;AAEnB,CAAC;AACD,MAAMsI,UAAU,CAAC;EACbpB,WAAWA,CAAA,EAAG;IACVhH,eAAe,CAAC,aAAa,EAAEf,OAAO,CAACoJ,IAAI,EAAE,MAAM,CAAC;EACxD;AACJ;AACAD,UAAU,CAACE,IAAI,YAAAC,mBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFJ,UAAU;AAAA,CAAkD;AACnKA,UAAU,CAACK,IAAI,kBAD8EjJ,EAAE,CAAAkJ,gBAAA;EAAAC,IAAA,EACSP;AAAU,EAAG;AACrHA,UAAU,CAACQ,IAAI,kBAF8EpJ,EAAE,CAAAqJ,gBAAA;EAAAC,SAAA,EAEgC,CACvHZ,8BAA8B,EAC9BH,uBAAuB;AAC1B,EAAG;AACR;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAN6FvJ,EAAE,CAAAwJ,iBAAA,CAMJZ,UAAU,EAAc,CAAC;IACxGO,IAAI,EAAEhJ,QAAQ;IACdsJ,IAAI,EAAE,CAAC;MACCH,SAAS,EAAE,CACPZ,8BAA8B,EAC9BH,uBAAuB;IAE/B,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASmB,WAAWA,CAACvB,EAAE,EAAE,GAAGM,IAAI,EAAE;EAC9B,OAAO;IACHkB,QAAQ,EAAEf,UAAU;IACpBU,SAAS,EAAE,CAAC;MACJd,OAAO,EAAEX,uBAAuB;MAChCc,UAAU,EAAET,mBAAmB,CAACC,EAAE,CAAC;MACnCyB,KAAK,EAAE,IAAI;MACXnB,IAAI,EAAE,CACFrI,MAAM,EACNC,QAAQ,EACRX,sBAAsB,EACtBa,YAAY,EACZ,CAAC,IAAIL,QAAQ,CAAC,CAAC,EAAEO,iBAAiB,CAAC,EACnC,GAAGgI,IAAI;IAEf,CAAC;EACT,CAAC;AACL;;AAEA;AACA,MAAM/H,SAAS,GAAGf,SAAS,CAACgB,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAMC,IAAI,GAAGjB,SAAS,CAACkB,MAAM,EAAE,IAAI,CAAC;AACpC,MAAMC,OAAO,GAAGnB,SAAS,CAACoB,SAAS,EAAE,IAAI,CAAC;;AAE1C;AACA,MAAMC,eAAe,GAAGrB,SAAS,CAACsB,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMC,sBAAsB,GAAGvB,SAAS,CAACwB,wBAAwB,EAAE,IAAI,CAAC;AACxE,MAAMC,eAAe,GAAGzB,SAAS,CAAC0B,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMC,oBAAoB,GAAG3B,SAAS,CAAC4B,sBAAsB,EAAE,IAAI,CAAC;AACpE,MAAMC,mBAAmB,GAAG7B,SAAS,CAAC8B,qBAAqB,EAAE,IAAI,CAAC;AAClE,MAAMC,8BAA8B,GAAG/B,SAAS,CAACgC,gCAAgC,EAAE,IAAI,CAAC;AACxF,MAAMC,UAAU,GAAGjC,SAAS,CAACkC,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,0BAA0B,GAAGnC,SAAS,CAACoC,4BAA4B,EAAE,IAAI,CAAC;AAChF,MAAMC,qBAAqB,GAAGrC,SAAS,CAACsC,uBAAuB,EAAE,IAAI,CAAC;AACtE,MAAMC,OAAO,GAAGvC,SAAS,CAACwC,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMC,UAAU,GAAGzC,SAAS,CAAC0C,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,gBAAgB,GAAG3C,SAAS,CAAC4C,kBAAkB,EAAE,IAAI,CAAC;AAC5D,MAAMC,sBAAsB,GAAG7C,SAAS,CAAC8C,wBAAwB,EAAE,IAAI,CAAC;AACxE,MAAMC,iBAAiB,GAAG/C,SAAS,CAACgD,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,cAAc,GAAGjD,SAAS,CAACkD,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAMC,qBAAqB,GAAGnD,SAAS,CAACoD,uBAAuB,EAAE,IAAI,CAAC;AACtE,MAAMC,kBAAkB,GAAGrD,SAAS,CAACsD,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,mBAAmB,GAAGvD,SAAS,CAACwD,qBAAqB,EAAE,IAAI,CAAC;AAClE,MAAMC,aAAa,GAAGzD,SAAS,CAAC0D,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,gBAAgB,GAAG3D,SAAS,CAAC4D,kBAAkB,EAAE,IAAI,CAAC;AAC5D,MAAMC,WAAW,GAAG7D,SAAS,CAAC8D,aAAa,EAAE,IAAI,CAAC;AAClD,MAAMC,kBAAkB,GAAG/D,SAAS,CAACgE,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,gBAAgB,GAAGjE,SAAS,CAACkE,kBAAkB,EAAE,IAAI,CAAC;AAC5D,MAAMC,kBAAkB,GAAGnE,SAAS,CAACoE,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,4BAA4B,GAAGrE,SAAS,CAACsE,8BAA8B,EAAE,IAAI,CAAC;AACpF,MAAMC,6BAA6B,GAAGvE,SAAS,CAACwE,+BAA+B,EAAE,IAAI,CAAC;AACtF,MAAMC,uBAAuB,GAAGzE,SAAS,CAAC0E,yBAAyB,EAAE,IAAI,CAAC;AAC1E,MAAMC,0BAA0B,GAAG3E,SAAS,CAAC4E,4BAA4B,EAAE,IAAI,CAAC;AAChF,MAAMC,MAAM,GAAG7E,SAAS,CAAC8E,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMC,qBAAqB,GAAG/E,SAAS,CAACgF,uBAAuB,EAAE,IAAI,CAAC;AACtE,MAAMC,sBAAsB,GAAGjF,SAAS,CAACkF,wBAAwB,EAAE,IAAI,CAAC;AACxE,MAAMC,qBAAqB,GAAGnF,SAAS,CAACoF,uBAAuB,EAAE,IAAI,CAAC;AACtE,MAAMC,cAAc,GAAGrF,SAAS,CAACsF,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAMC,iBAAiB,GAAGvF,SAAS,CAACwF,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,oBAAoB,GAAGzF,SAAS,CAAC0F,sBAAsB,EAAE,IAAI,CAAC;AACpE,MAAMC,qBAAqB,GAAG3F,SAAS,CAAC4F,uBAAuB,EAAE,IAAI,CAAC;AACtE,MAAMC,0BAA0B,GAAG7F,SAAS,CAAC8F,4BAA4B,EAAE,IAAI,CAAC;AAChF,MAAMC,mBAAmB,GAAG/F,SAAS,CAACgG,qBAAqB,EAAE,IAAI,CAAC;AAClE,MAAMC,qBAAqB,GAAGjG,SAAS,CAACkG,uBAAuB,EAAE,IAAI,CAAC;AACtE,MAAMC,eAAe,GAAGnG,SAAS,CAACoG,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMC,kBAAkB,GAAGrG,SAAS,CAACsG,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,OAAO,GAAGvG,SAAS,CAACwG,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMC,MAAM,GAAGzG,SAAS,CAAC0G,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMC,iBAAiB,GAAG3G,SAAS,CAAC4G,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,WAAW,GAAG7G,SAAS,CAAC8G,aAAa,EAAE,IAAI,CAAC;AAClD,MAAMC,cAAc,GAAG/G,SAAS,CAACgH,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAMC,iBAAiB,GAAGjH,SAAS,CAACkH,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,aAAa,GAAGnH,SAAS,CAACoH,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,iBAAiB,GAAGrH,SAAS,CAACsH,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,uBAAuB,GAAGvH,SAAS,CAACwH,yBAAyB,EAAE,IAAI,CAAC;AAC1E,MAAMC,uBAAuB,GAAGzH,SAAS,CAAC0H,yBAAyB,EAAE,IAAI,CAAC;;AAE1E;AACA;AACA;;AAEA,SAASE,IAAI,EAAEG,aAAa,EAAEkB,UAAU,EAAE5H,eAAe,EAAE2G,aAAa,EAAEjH,SAAS,EAAEQ,sBAAsB,EAAEE,eAAe,EAAEE,oBAAoB,EAAEE,mBAAmB,EAAEE,8BAA8B,EAAEE,UAAU,EAAEE,0BAA0B,EAAEE,qBAAqB,EAAEE,OAAO,EAAEE,UAAU,EAAEE,gBAAgB,EAAEE,sBAAsB,EAAEE,iBAAiB,EAAE5B,OAAO,EAAE8B,cAAc,EAAEE,qBAAqB,EAAEE,kBAAkB,EAAEE,mBAAmB,EAAEE,aAAa,EAAEE,gBAAgB,EAAEE,WAAW,EAAEE,kBAAkB,EAAEE,gBAAgB,EAAEE,kBAAkB,EAAE4F,WAAW,EAAE1F,4BAA4B,EAAEE,6BAA6B,EAAEE,uBAAuB,EAAEE,0BAA0B,EAAEE,MAAM,EAAEE,qBAAqB,EAAEE,sBAAsB,EAAEE,qBAAqB,EAAEE,cAAc,EAAEE,iBAAiB,EAAEE,oBAAoB,EAAEE,qBAAqB,EAAEE,0BAA0B,EAAEE,mBAAmB,EAAEE,qBAAqB,EAAEE,eAAe,EAAEE,kBAAkB,EAAEE,OAAO,EAAEE,MAAM,EAAEE,iBAAiB,EAAEE,WAAW,EAAEE,cAAc,EAAEE,iBAAiB,EAAEE,aAAa,EAAEE,iBAAiB,EAAEpG,IAAI,EAAEsG,uBAAuB,EAAEE,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
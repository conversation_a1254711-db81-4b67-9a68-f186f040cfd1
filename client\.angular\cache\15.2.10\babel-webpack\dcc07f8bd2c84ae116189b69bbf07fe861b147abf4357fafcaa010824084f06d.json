{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PerfectScrollbarModule, PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';\nimport { CoreDirectivesModule } from '@core/directives/directives';\nimport { CoreSidebarModule } from '@core/components/core-sidebar/core-sidebar.module';\nimport { CoreThemeCustomizerComponent } from '@core/components/theme-customizer/theme-customizer.component';\nimport * as i0 from \"@angular/core\";\nconst DEFAULT_PERFECT_SCROLLBAR_CONFIG = {\n  suppressScrollX: true,\n  wheelPropagation: false\n};\nexport class CoreThemeCustomizerModule {\n  static #_ = this.ɵfac = function CoreThemeCustomizerModule_Factory(t) {\n    return new (t || CoreThemeCustomizerModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreThemeCustomizerModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: PERFECT_SCROLLBAR_CONFIG,\n      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG\n    }],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, FlexLayoutModule, PerfectScrollbarModule, CoreDirectivesModule, CoreSidebarModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreThemeCustomizerModule, {\n    declarations: [CoreThemeCustomizerComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, FlexLayoutModule, PerfectScrollbarModule, CoreDirectivesModule, CoreSidebarModule],\n    exports: [CoreThemeCustomizerComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAEEC,sBAAsB,EACtBC,wBAAwB,QACnB,uBAAuB;AAE9B,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,iBAAiB,QAAQ,mDAAmD;AAErF,SAASC,4BAA4B,QAAQ,8DAA8D;;AAE3G,MAAMC,gCAAgC,GAAoC;EACxEC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE;CACnB;AAqBD,OAAM,MAAOC,yBAAyB;EAAA,QAAAC,CAAA;qBAAzBD,yBAAyB;EAAA;EAAA,QAAAE,EAAA;UAAzBF;EAAyB;EAAA,QAAAG,EAAA;eARzB,CACT;MACEC,OAAO,EAAEX,wBAAwB;MACjCY,QAAQ,EAAER;KACX,CACF;IAAAS,OAAA,GAbClB,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBF,gBAAgB,EAChBG,sBAAsB,EACtBE,oBAAoB,EACpBC,iBAAiB;EAAA;;;2EAURK,yBAAyB;IAAAO,YAAA,GAlBrBX,4BAA4B;IAAAU,OAAA,GAEzClB,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBF,gBAAgB,EAChBG,sBAAsB,EACtBE,oBAAoB,EACpBC,iBAAiB;IAAAa,OAAA,GAQTZ,4BAA4B;EAAA;AAAA", "names": ["CommonModule", "FlexLayoutModule", "FormsModule", "ReactiveFormsModule", "PerfectScrollbarModule", "PERFECT_SCROLLBAR_CONFIG", "CoreDirectivesModule", "CoreSidebarModule", "CoreThemeCustomizerComponent", "DEFAULT_PERFECT_SCROLLBAR_CONFIG", "suppressScrollX", "wheelPropagation", "CoreThemeCustomizerModule", "_", "_2", "_3", "provide", "useValue", "imports", "declarations", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\theme-customizer\\theme-customizer.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FlexLayoutModule } from '@angular/flex-layout';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport {\r\n  PerfectScrollbarConfigInterface,\r\n  PerfectScrollbarModule,\r\n  PERFECT_SCROLLBAR_CONFIG\r\n} from 'ngx-perfect-scrollbar';\r\n\r\nimport { CoreDirectivesModule } from '@core/directives/directives';\r\nimport { CoreSidebarModule } from '@core/components/core-sidebar/core-sidebar.module';\r\n\r\nimport { CoreThemeCustomizerComponent } from '@core/components/theme-customizer/theme-customizer.component';\r\n\r\nconst DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {\r\n  suppressScrollX: true,\r\n  wheelPropagation: false\r\n};\r\n\r\n@NgModule({\r\n  declarations: [CoreThemeCustomizerComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    FlexLayoutModule,\r\n    PerfectScrollbarModule,\r\n    CoreDirectivesModule,\r\n    CoreSidebarModule\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: PERFECT_SCROLLBAR_CONFIG,\r\n      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG\r\n    }\r\n  ],\r\n  exports: [CoreThemeCustomizerComponent]\r\n})\r\nexport class CoreThemeCustomizerModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
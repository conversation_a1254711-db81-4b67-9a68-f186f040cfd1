{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function count(predicate) {\n  return source => source.lift(new CountOperator(predicate, source));\n}\nclass CountOperator {\n  constructor(predicate, source) {\n    this.predicate = predicate;\n    this.source = source;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new CountSubscriber(subscriber, this.predicate, this.source));\n  }\n}\nclass CountSubscriber extends Subscriber {\n  constructor(destination, predicate, source) {\n    super(destination);\n    this.predicate = predicate;\n    this.source = source;\n    this.count = 0;\n    this.index = 0;\n  }\n  _next(value) {\n    if (this.predicate) {\n      this._tryPredicate(value);\n    } else {\n      this.count++;\n    }\n  }\n  _tryPredicate(value) {\n    let result;\n    try {\n      result = this.predicate(value, this.index++, this.source);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n    if (result) {\n      this.count++;\n    }\n  }\n  _complete() {\n    this.destination.next(this.count);\n    this.destination.complete();\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "count", "predicate", "source", "lift", "CountOperator", "constructor", "call", "subscriber", "subscribe", "CountSubscriber", "destination", "index", "_next", "value", "_tryPredicate", "result", "err", "error", "_complete", "next", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/count.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function count(predicate) {\n    return (source) => source.lift(new CountOperator(predicate, source));\n}\nclass CountOperator {\n    constructor(predicate, source) {\n        this.predicate = predicate;\n        this.source = source;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new CountSubscriber(subscriber, this.predicate, this.source));\n    }\n}\nclass CountSubscriber extends Subscriber {\n    constructor(destination, predicate, source) {\n        super(destination);\n        this.predicate = predicate;\n        this.source = source;\n        this.count = 0;\n        this.index = 0;\n    }\n    _next(value) {\n        if (this.predicate) {\n            this._tryPredicate(value);\n        }\n        else {\n            this.count++;\n        }\n    }\n    _tryPredicate(value) {\n        let result;\n        try {\n            result = this.predicate(value, this.index++, this.source);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        if (result) {\n            this.count++;\n        }\n    }\n    _complete() {\n        this.destination.next(this.count);\n        this.destination.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAE;EAC7B,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,aAAa,CAACH,SAAS,EAAEC,MAAM,CAAC,CAAC;AACxE;AACA,MAAME,aAAa,CAAC;EAChBC,WAAWA,CAACJ,SAAS,EAAEC,MAAM,EAAE;IAC3B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAI,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,eAAe,CAACF,UAAU,EAAE,IAAI,CAACN,SAAS,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC;EACzF;AACJ;AACA,MAAMO,eAAe,SAASV,UAAU,CAAC;EACrCM,WAAWA,CAACK,WAAW,EAAET,SAAS,EAAEC,MAAM,EAAE;IACxC,KAAK,CAACQ,WAAW,CAAC;IAClB,IAAI,CAACT,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACF,KAAK,GAAG,CAAC;IACd,IAAI,CAACW,KAAK,GAAG,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,IAAI,CAACZ,SAAS,EAAE;MAChB,IAAI,CAACa,aAAa,CAACD,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACb,KAAK,EAAE;IAChB;EACJ;EACAc,aAAaA,CAACD,KAAK,EAAE;IACjB,IAAIE,MAAM;IACV,IAAI;MACAA,MAAM,GAAG,IAAI,CAACd,SAAS,CAACY,KAAK,EAAE,IAAI,CAACF,KAAK,EAAE,EAAE,IAAI,CAACT,MAAM,CAAC;IAC7D,CAAC,CACD,OAAOc,GAAG,EAAE;MACR,IAAI,CAACN,WAAW,CAACO,KAAK,CAACD,GAAG,CAAC;MAC3B;IACJ;IACA,IAAID,MAAM,EAAE;MACR,IAAI,CAACf,KAAK,EAAE;IAChB;EACJ;EACAkB,SAASA,CAAA,EAAG;IACR,IAAI,CAACR,WAAW,CAACS,IAAI,CAAC,IAAI,CAACnB,KAAK,CAAC;IACjC,IAAI,CAACU,WAAW,CAACU,QAAQ,CAAC,CAAC;EAC/B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
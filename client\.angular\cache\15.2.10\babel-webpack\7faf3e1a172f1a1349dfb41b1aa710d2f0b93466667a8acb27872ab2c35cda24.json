{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, removeHandler, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addHand<PERSON>, removeHandler).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n  }\n  return new Observable(subscriber => {\n    const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n    let retValue;\n    try {\n      retValue = addHandler(handler);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n    if (!isFunction(removeHandler)) {\n      return undefined;\n    }\n    return () => removeHandler(handler, retValue);\n  });\n}", "map": {"version": 3, "names": ["Observable", "isArray", "isFunction", "map", "fromEventPattern", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resultSelector", "pipe", "args", "subscriber", "handler", "e", "next", "length", "retValue", "err", "error", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/fromEventPattern.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, removeHandler, resultSelector) {\n    if (resultSelector) {\n        return fromEventPattern(addHand<PERSON>, removeHandler).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n    return new Observable(subscriber => {\n        const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n        let retValue;\n        try {\n            retValue = addHandler(handler);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        if (!isFunction(removeHandler)) {\n            return undefined;\n        }\n        return () => removeHandler(handler, retValue);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,GAAG,QAAQ,kBAAkB;AACtC,OAAO,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,aAAa,EAAEC,cAAc,EAAE;EACxE,IAAIA,cAAc,EAAE;IAChB,OAAOH,gBAAgB,CAACC,UAAU,EAAEC,aAAa,CAAC,CAACE,IAAI,CAACL,GAAG,CAACM,IAAI,IAAIR,OAAO,CAACQ,IAAI,CAAC,GAAGF,cAAc,CAAC,GAAGE,IAAI,CAAC,GAAGF,cAAc,CAACE,IAAI,CAAC,CAAC,CAAC;EACxI;EACA,OAAO,IAAIT,UAAU,CAACU,UAAU,IAAI;IAChC,MAAMC,OAAO,GAAGA,CAAC,GAAGC,CAAC,KAAKF,UAAU,CAACG,IAAI,CAACD,CAAC,CAACE,MAAM,KAAK,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC;IACpE,IAAIG,QAAQ;IACZ,IAAI;MACAA,QAAQ,GAAGV,UAAU,CAACM,OAAO,CAAC;IAClC,CAAC,CACD,OAAOK,GAAG,EAAE;MACRN,UAAU,CAACO,KAAK,CAACD,GAAG,CAAC;MACrB,OAAOE,SAAS;IACpB;IACA,IAAI,CAAChB,UAAU,CAACI,aAAa,CAAC,EAAE;MAC5B,OAAOY,SAAS;IACpB;IACA,OAAO,MAAMZ,aAAa,CAACK,OAAO,EAAEI,QAAQ,CAAC;EACjD,CAAC,CAAC;AACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
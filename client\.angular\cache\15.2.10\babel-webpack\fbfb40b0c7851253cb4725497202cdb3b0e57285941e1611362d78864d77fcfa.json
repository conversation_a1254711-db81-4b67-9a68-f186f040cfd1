{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TeamsComponent } from './teams.component';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport { CoreSidebarModule } from '@core/components';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreCommonModule } from '@core/common.module';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { TeamManagementComponent } from './team-management/team-management.component';\nimport { DataTablesModule } from 'angular-datatables';\nimport { EditorSidebarModule } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AvailablePlayerModalComponent } from './team-assignment/assign-players/available-player-modal/available-player-modal.component';\nimport { AssignNewCoachComponent } from './team-assignment/assign-coaches/assign-new-coach/assign-new-coach.component';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { TeamsheetTemplateComponent } from './teamsheet-template/teamsheet-template.component';\nimport { TeamSelectionComponent } from './team-selection/team-selection.component';\nimport { TeamAssignmentComponent } from './team-assignment/team-assignment.component';\nimport { TeamCoachesComponent } from './team-assignment/assign-coaches/assign-coaches.component';\nimport { TeamPlayersComponent } from './team-assignment/assign-players/assign-players.component';\nimport { TeamSheetsComponent } from './teamsheets/teamsheets.component';\nimport { ModalTeamsheetHistoryComponent } from './teamsheets/modal-teamsheet-history/modal-teamsheet-history.component';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { ModalAssignPlayersComponent } from './team-management/modal-assign-players/modal-assign-players.component';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport { ModalImportTeamsComponent } from './team-management/modal-import-teams/modal-import-teams.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'team-management',\n  component: TeamManagementComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: [AppConfig.PERMISSIONS.team_management, AppConfig.PERMISSIONS.assign_coaches, AppConfig.PERMISSIONS.assign_players]\n  }\n}, {\n  path: 'team-assignment',\n  component: TeamSelectionComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: [AppConfig.PERMISSIONS.assign_players, AppConfig.PERMISSIONS.assign_coaches]\n  }\n}, {\n  path: 'team-assignment/:teamId',\n  component: TeamAssignmentComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: [AppConfig.PERMISSIONS.assign_players, AppConfig.PERMISSIONS.assign_coaches]\n  }\n}, {\n  path: 'teamsheets',\n  component: TeamSheetsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_teamsheets\n  }\n}];\nexport class TeamModule {\n  static #_ = this.ɵfac = function TeamModule_Factory(t) {\n    return new (t || TeamModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TeamModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [NgbModule, CommonModule, RouterModule.forChild(routes), ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CorePipesModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TeamModule, {\n    declarations: [TeamsComponent, TeamManagementComponent, AvailablePlayerModalComponent, AssignNewCoachComponent, TeamsheetTemplateComponent, TeamSelectionComponent, TeamAssignmentComponent, TeamCoachesComponent, TeamPlayersComponent, TeamSheetsComponent, ModalTeamsheetHistoryComponent, ModalAssignPlayersComponent, ModalImportTeamsComponent],\n    imports: [NgbModule, CommonModule, i1.RouterModule, ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, NgSelectModule, EditorSidebarModule, BtnDropdownActionModule, CorePipesModule],\n    exports: [TeamsComponent, TeamManagementComponent, AvailablePlayerModalComponent, AssignNewCoachComponent, TeamsheetTemplateComponent, TeamSheetsComponent, ModalTeamsheetHistoryComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,6BAA6B,QAAQ,0FAA0F;AACxI,SAASC,uBAAuB,QAAQ,8EAA8E;AACtH,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,8BAA8B,QAAQ,wEAAwE;AACvH,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,yBAAyB,QAAQ,mEAAmE;;;AAE7G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAErB,uBAAuB;EAClCsB,WAAW,EAAE,CAACR,gBAAgB,CAAC;EAC/BS,IAAI,EAAE;IACJC,WAAW,EAAE,CACXT,SAAS,CAACU,WAAW,CAACC,eAAe,EACrCX,SAAS,CAACU,WAAW,CAACE,cAAc,EACpCZ,SAAS,CAACU,WAAW,CAACG,cAAc;;CAGzC,EACD;EACER,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEb,sBAAsB;EACjCc,WAAW,EAAE,CAACR,gBAAgB,CAAC;EAC/BS,IAAI,EAAE;IACJC,WAAW,EAAE,CACXT,SAAS,CAACU,WAAW,CAACG,cAAc,EACpCb,SAAS,CAACU,WAAW,CAACE,cAAc;;CAGzC,EACD;EACEP,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEZ,uBAAuB;EAClCa,WAAW,EAAE,CAACR,gBAAgB,CAAC;EAC/BS,IAAI,EAAE;IACJC,WAAW,EAAE,CACXT,SAAS,CAACU,WAAW,CAACG,cAAc,EACpCb,SAAS,CAACU,WAAW,CAACE,cAAc;;CAGzC,EACD;EACEP,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAET,mBAAmB;EAC9BU,WAAW,EAAE,CAACR,gBAAgB,CAAC;EAC/BS,IAAI,EAAE;IAAEC,WAAW,EAAET,SAAS,CAACU,WAAW,CAACI;EAAiB;CAC7D,CACF;AA8CD,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA;UAAVF;EAAU;EAAA,QAAAG,EAAA;cA1BnBlC,SAAS,EACTV,YAAY,EACZC,YAAY,CAAC4C,QAAQ,CAACf,MAAM,CAAC,EAC7B3B,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBE,eAAe,EACfD,iBAAiB,EACjBE,gBAAgB,EAChBG,gBAAgB,EAChBE,cAAc,EACdD,mBAAmB,EACnBI,uBAAuB,EACvBW,eAAe;EAAA;;;2EAYNa,UAAU;IAAAK,YAAA,GAzCnB5C,cAAc,EACdS,uBAAuB,EACvBI,6BAA6B,EAC7BC,uBAAuB,EACvBE,0BAA0B,EAC1BC,sBAAsB,EACtBC,uBAAuB,EACvBC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB,EACnBC,8BAA8B,EAC9BG,2BAA2B,EAC3BE,yBAAyB;IAAAkB,OAAA,GAGzBrC,SAAS,EACTV,YAAY,EAAAgD,EAAA,CAAA/C,YAAA,EAEZE,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBE,eAAe,EACfD,iBAAiB,EACjBE,gBAAgB,EAChBG,gBAAgB,EAChBE,cAAc,EACdD,mBAAmB,EACnBI,uBAAuB,EACvBW,eAAe;IAAAqB,OAAA,GAGf/C,cAAc,EACdS,uBAAuB,EACvBI,6BAA6B,EAC7BC,uBAAuB,EACvBE,0BAA0B,EAC1BK,mBAAmB,EACnBC,8BAA8B;EAAA;AAAA", "names": ["CommonModule", "RouterModule", "TeamsComponent", "ContentHeaderModule", "FormsModule", "ReactiveFormsModule", "ErrorMessageModule", "CoreSidebarModule", "TranslateModule", "CoreCommonModule", "NgbModule", "TeamManagementComponent", "DataTablesModule", "EditorSidebarModule", "NgSelectModule", "AvailablePlayerModalComponent", "AssignNewCoachComponent", "BtnDropdownActionModule", "TeamsheetTemplateComponent", "TeamSelectionComponent", "TeamAssignmentComponent", "TeamCoachesComponent", "TeamPlayersComponent", "TeamSheetsComponent", "ModalTeamsheetHistoryComponent", "PermissionsGuard", "AppConfig", "ModalAssignPlayersComponent", "CorePipesModule", "ModalImportTeamsComponent", "routes", "path", "component", "canActivate", "data", "permissions", "PERMISSIONS", "team_management", "assign_coaches", "assign_players", "manage_teamsheets", "TeamModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\teams.module.ts"], "sourcesContent": ["import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TeamsComponent } from './teams.component';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TeamManagementComponent } from './team-management/team-management.component';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { EditorSidebarModule } from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { AvailablePlayerModalComponent } from './team-assignment/assign-players/available-player-modal/available-player-modal.component';\r\nimport { AssignNewCoachComponent } from './team-assignment/assign-coaches/assign-new-coach/assign-new-coach.component';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { TeamsheetTemplateComponent } from './teamsheet-template/teamsheet-template.component';\r\nimport { TeamSelectionComponent } from './team-selection/team-selection.component';\r\nimport { TeamAssignmentComponent } from './team-assignment/team-assignment.component';\r\nimport { TeamCoachesComponent } from './team-assignment/assign-coaches/assign-coaches.component';\r\nimport { TeamPlayersComponent } from './team-assignment/assign-players/assign-players.component';\r\nimport { TeamSheetsComponent } from './teamsheets/teamsheets.component';\r\nimport { ModalTeamsheetHistoryComponent } from './teamsheets/modal-teamsheet-history/modal-teamsheet-history.component';\r\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { ModalAssignPlayersComponent } from './team-management/modal-assign-players/modal-assign-players.component';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\nimport { ModalImportTeamsComponent } from './team-management/modal-import-teams/modal-import-teams.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'team-management',\r\n    component: TeamManagementComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: {\r\n      permissions: [\r\n        AppConfig.PERMISSIONS.team_management,\r\n        AppConfig.PERMISSIONS.assign_coaches,\r\n        AppConfig.PERMISSIONS.assign_players,\r\n      ],\r\n    },\r\n  },\r\n  {\r\n    path: 'team-assignment',\r\n    component: TeamSelectionComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: {\r\n      permissions: [\r\n        AppConfig.PERMISSIONS.assign_players,\r\n        AppConfig.PERMISSIONS.assign_coaches,\r\n      ],\r\n    },\r\n  },\r\n  {\r\n    path: 'team-assignment/:teamId',\r\n    component: TeamAssignmentComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: {\r\n      permissions: [\r\n        AppConfig.PERMISSIONS.assign_players,\r\n        AppConfig.PERMISSIONS.assign_coaches,\r\n      ],\r\n    },\r\n  },\r\n  {\r\n    path: 'teamsheets',\r\n    component: TeamSheetsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_teamsheets },\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [\r\n    TeamsComponent,\r\n    TeamManagementComponent,\r\n    AvailablePlayerModalComponent,\r\n    AssignNewCoachComponent,\r\n    TeamsheetTemplateComponent,\r\n    TeamSelectionComponent,\r\n    TeamAssignmentComponent,\r\n    TeamCoachesComponent,\r\n    TeamPlayersComponent,\r\n    TeamSheetsComponent,\r\n    ModalTeamsheetHistoryComponent,\r\n    ModalAssignPlayersComponent,\r\n    ModalImportTeamsComponent,\r\n  ],\r\n  imports: [\r\n    NgbModule,\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ErrorMessageModule,\r\n    TranslateModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    DataTablesModule,\r\n    NgSelectModule,\r\n    EditorSidebarModule,\r\n    BtnDropdownActionModule,\r\n    CorePipesModule\r\n  ],\r\n  exports: [\r\n    TeamsComponent,\r\n    TeamManagementComponent,\r\n    AvailablePlayerModalComponent,\r\n    AssignNewCoachComponent,\r\n    TeamsheetTemplateComponent,\r\n    TeamSheetsComponent,\r\n    ModalTeamsheetHistoryComponent,\r\n  ],\r\n})\r\nexport class TeamModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
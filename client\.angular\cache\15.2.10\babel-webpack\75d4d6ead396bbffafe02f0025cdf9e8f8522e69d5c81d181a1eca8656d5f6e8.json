{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Vietnamese [vi]\n//! author : <PERSON> : https://github.com/bangnk\n//! author : <PERSON><PERSON> Kira : https://github.com/chienkira\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var vi = moment.defineLocale('vi', {\n    months: 'tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12'.split('_'),\n    monthsShort: 'Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy'.split('_'),\n    weekdaysShort: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n    weekdaysMin: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n    weekdaysParseExact: true,\n    meridiemParse: /sa|ch/i,\n    isPM: function (input) {\n      return /^ch$/i.test(input);\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'sa' : 'SA';\n      } else {\n        return isLower ? 'ch' : 'CH';\n      }\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [năm] YYYY',\n      LLL: 'D MMMM [năm] YYYY HH:mm',\n      LLLL: 'dddd, D MMMM [năm] YYYY HH:mm',\n      l: 'DD/M/YYYY',\n      ll: 'D MMM YYYY',\n      lll: 'D MMM YYYY HH:mm',\n      llll: 'ddd, D MMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Hôm nay lúc] LT',\n      nextDay: '[Ngày mai lúc] LT',\n      nextWeek: 'dddd [tuần tới lúc] LT',\n      lastDay: '[Hôm qua lúc] LT',\n      lastWeek: 'dddd [tuần trước lúc] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s tới',\n      past: '%s trước',\n      s: 'vài giây',\n      ss: '%d giây',\n      m: 'một phút',\n      mm: '%d phút',\n      h: 'một giờ',\n      hh: '%d giờ',\n      d: 'một ngày',\n      dd: '%d ngày',\n      w: 'một tuần',\n      ww: '%d tuần',\n      M: 'một tháng',\n      MM: '%d tháng',\n      y: 'một năm',\n      yy: '%d năm'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}/,\n    ordinal: function (number) {\n      return number;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return vi;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "vi", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "meridiemParse", "isPM", "input", "test", "meridiem", "hours", "minutes", "isLower", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/moment/locale/vi.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Vietnamese [vi]\n//! author : <PERSON> : https://github.com/bangnk\n//! author : <PERSON><PERSON> Kira : https://github.com/chienkira\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var vi = moment.defineLocale('vi', {\n        months: 'tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12'.split(\n            '_'\n        ),\n        monthsShort:\n            'Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy'.split(\n            '_'\n        ),\n        weekdaysShort: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n        weekdaysMin: 'CN_T2_T3_T4_T5_T6_T7'.split('_'),\n        weekdaysParseExact: true,\n        meridiemParse: /sa|ch/i,\n        isPM: function (input) {\n            return /^ch$/i.test(input);\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 12) {\n                return isLower ? 'sa' : 'SA';\n            } else {\n                return isLower ? 'ch' : 'CH';\n            }\n        },\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM [năm] YYYY',\n            LLL: 'D MMMM [năm] YYYY HH:mm',\n            LLLL: 'dddd, D MMMM [năm] YYYY HH:mm',\n            l: 'DD/M/YYYY',\n            ll: 'D MMM YYYY',\n            lll: 'D MMM YYYY HH:mm',\n            llll: 'ddd, D MMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Hôm nay lúc] LT',\n            nextDay: '[Ngày mai lúc] LT',\n            nextWeek: 'dddd [tuần tới lúc] LT',\n            lastDay: '[Hôm qua lúc] LT',\n            lastWeek: 'dddd [tuần trước lúc] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s tới',\n            past: '%s trước',\n            s: 'vài giây',\n            ss: '%d giây',\n            m: 'một phút',\n            mm: '%d phút',\n            h: 'một giờ',\n            hh: '%d giờ',\n            d: 'một ngày',\n            dd: '%d ngày',\n            w: 'một tuần',\n            ww: '%d tuần',\n            M: 'một tháng',\n            MM: '%d tháng',\n            y: 'một năm',\n            yy: '%d năm',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}/,\n        ordinal: function (number) {\n            return number;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return vi;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oGAAoG,CAACC,KAAK,CAC9G,GACJ,CAAC;IACDC,WAAW,EACP,qFAAqF,CAACD,KAAK,CACvF,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,wDAAwD,CAACH,KAAK,CACpE,GACJ,CAAC;IACDI,aAAa,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAChDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,OAAO,CAACC,IAAI,CAACD,KAAK,CAAC;IAC9B,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC;IACJ,CAAC;IACDC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,mBAAmB;MACvBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE,+BAA+B;MACrCC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,wBAAwB;MAClCC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,0BAA0B;MACpCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,SAAS;IACjCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OAAOA,MAAM;IACjB,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAO1D,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/layout/components/navbar/navbar-search/search.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/flex-layout/extended\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i7 from \"ngx-perfect-scrollbar\";\nimport * as i8 from \"@core/pipes/filter.pipe\";\nconst _c0 = [\"openSearch\"];\nconst _c1 = [\"pageList\"];\nconst _c2 = function (a0) {\n  return {\n    current_item: a0\n  };\n};\nfunction NavbarSearchComponent_ng_container_16_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18);\n    i0.ɵɵlistener(\"mouseover\", function NavbarSearchComponent_ng_container_16_li_1_Template_li_mouseover_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const page_r12 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(page_r12.hover = true);\n    })(\"mouseout\", function NavbarSearchComponent_ng_container_16_li_1_Template_li_mouseout_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const page_r12 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(page_r12.hover = false);\n    });\n    i0.ɵɵelementStart(1, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function NavbarSearchComponent_ng_container_16_li_1_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.toggleSearch());\n    })(\"keyup.enter\", function NavbarSearchComponent_ng_container_16_li_1_Template_a_keyup_enter_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const page_r12 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.navigate(page_r12.link));\n    });\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵelement(3, \"i\", 21);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const page_r12 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current_item\", page_r12.hover);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c2, i_r13 === ctx_r11.activeIndex));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", page_r12.link);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data-feather\", page_r12.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r12.title);\n  }\n}\nfunction NavbarSearchComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarSearchComponent_ng_container_16_li_1_Template, 6, 8, \"li\", 17);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵpipe(3, \"filter\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, i0.ɵɵpipeBind3(3, 5, ctx_r2.pages, ctx_r2.searchText, \"title\"), 0, 4));\n  }\n}\nfunction NavbarSearchComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"i\", 25);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"No results found.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction NavbarSearchComponent_ng_container_24_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 27)(1, \"a\", 28)(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"p\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"small\", 34);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"small\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c2, ctx_r19.i === ctx_r19.activeIndex));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", file_r20.file, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(file_r20.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r20.by);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r20.size);\n  }\n}\nfunction NavbarSearchComponent_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarSearchComponent_ng_container_24_li_1_Template, 12, 7, \"li\", 26);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵpipe(3, \"filter\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, i0.ɵɵpipeBind3(3, 5, ctx_r5.files, ctx_r5.searchText, \"title\"), 0, 4));\n  }\n}\nfunction NavbarSearchComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"i\", 25);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"No results found.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction NavbarSearchComponent_ng_container_32_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 37)(1, \"a\", 38)(2, \"div\", 10)(3, \"div\", 39);\n    i0.ɵɵelement(4, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"p\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"small\", 34);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"small\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r22 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", contact_r22.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(contact_r22.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r22.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r22.date);\n  }\n}\nfunction NavbarSearchComponent_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarSearchComponent_ng_container_32_li_1_Template, 12, 4, \"li\", 36);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵpipe(3, \"filter\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, i0.ɵɵpipeBind3(3, 5, ctx_r8.contacts, ctx_r8.searchText, \"title\"), 0, 4));\n  }\n}\nfunction NavbarSearchComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"i\", 25);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"No results found.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class NavbarSearchComponent {\n  fn() {\n    this.removeOverlay();\n    this.openSearchRef = false;\n    this.searchText = '';\n  }\n  clickout(event) {\n    if (event.target.className === 'content-overlay') {\n      this.removeOverlay();\n      this.openSearchRef = false;\n      this.searchText = '';\n    }\n  }\n  /**\r\n   *\r\n   * @param document\r\n   * @param router\r\n   * @param _searchService\r\n   */\n  constructor(document, _elementRef, router, _searchService) {\n    this.document = document;\n    this._elementRef = _elementRef;\n    this.router = router;\n    this._searchService = _searchService;\n    // Public\n    this.searchText = '';\n    this.openSearchRef = false;\n    this.activeIndex = 0;\n    this.pages = [];\n    this.files = [];\n    this.contacts = [];\n  }\n  // Public Methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Next Active Match\r\n   */\n  nextActiveMatch() {\n    this.activeIndex = this.activeIndex < this.pageSearchLimit - 1 ? ++this.activeIndex : this.activeIndex;\n  }\n  /**\r\n   * Previous Active Match\r\n   */\n  prevActiveMatch() {\n    this.activeIndex = this.activeIndex > 0 ? --this.activeIndex : 0;\n  }\n  /**\r\n   * Remove Overlay\r\n   */\n  removeOverlay() {\n    this.document.querySelector('.app-content').classList.remove('show-overlay');\n  }\n  /**\r\n   * Auto Suggestion\r\n   *\r\n   * @param event\r\n   */\n  autoSuggestion(event) {\n    if (38 === event.keyCode) {\n      return this.prevActiveMatch();\n    }\n    if (40 === event.keyCode) {\n      return this.nextActiveMatch();\n    }\n    if (13 === event.keyCode) {\n      // Navigate to activeIndex\n      // ! Todo: Improve this code\n      let current_item = this._pageListElement.nativeElement.getElementsByClassName('current_item');\n      current_item[0]?.children[0].click();\n    }\n  }\n  /**\r\n   * Toggle Search\r\n   */\n  toggleSearch() {\n    this._searchService.onIsBookmarkOpenChange.next(false);\n    this.removeOverlay();\n    this.openSearchRef = !this.openSearchRef;\n    this.activeIndex = 0;\n    setTimeout(() => {\n      this._inputElement.nativeElement.focus();\n    });\n    if (this.openSearchRef === false) {\n      this.document.querySelector('.app-content').classList.remove('show-overlay');\n      this.searchText = '';\n    }\n  }\n  /**\r\n   * Search Update\r\n   *\r\n   * @param event\r\n   */\n  searchUpdate(event) {\n    const val = event.target.value.toLowerCase();\n    if (val !== '') {\n      this.document.querySelector('.app-content').classList.add('show-overlay');\n    } else {\n      this.document.querySelector('.app-content').classList.remove('show-overlay');\n    }\n    this.autoSuggestion(event);\n  }\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    this._searchService.onApiDataChange.subscribe(res => {\n      this.apiData = res;\n      this.pages = this.apiData[0].data;\n      this.pageSearchLimit = this.apiData[0].searchLimit;\n      this.files = this.apiData[1].data;\n      this.contacts = this.apiData[2].data;\n    });\n  }\n  static #_ = this.ɵfac = function NavbarSearchComponent_Factory(t) {\n    return new (t || NavbarSearchComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SearchService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NavbarSearchComponent,\n    selectors: [[\"app-navbar-search\"]],\n    viewQuery: function NavbarSearchComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._pageListElement = _t.first);\n      }\n    },\n    hostBindings: function NavbarSearchComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.escape\", function NavbarSearchComponent_keydown_escape_HostBindingHandler() {\n          return ctx.fn();\n        })(\"click\", function NavbarSearchComponent_click_HostBindingHandler($event) {\n          return ctx.clickout($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 36,\n    vars: 28,\n    consts: [[1, \"nav-item\", \"nav-search\"], [1, \"nav-link\", \"nav-link-search\", 3, \"click\"], [3, \"data-feather\"], [1, \"search-input\"], [1, \"search-input-icon\"], [\"type\", \"text\", \"placeholder\", \"Explore Vuexy...\", \"tabindex\", \"-1\", \"data-search\", \"search\", 1, \"form-control\", \"input\", 3, \"ngModel\", \"keyup\", \"ngModelChange\"], [\"openSearch\", \"\"], [1, \"btn\", \"search-input-close\", \"p-0\", 3, \"click\"], [1, \"search-list\", \"search-list-main\", 3, \"perfectScrollbar\"], [\"pageList\", \"\"], [1, \"d-flex\", \"align-items-center\"], [\"href\", \"javascript:void(0)\"], [1, \"section-label\", \"mt-75\", \"mb-0\"], [4, \"ngIf\", \"ngIfElse\"], [\"noResultsPages\", \"\"], [\"noResultsFiles\", \"\"], [\"noResultsMembers\", \"\"], [\"class\", \"auto-suggestion\", 3, \"ngClass\", \"current_item\", \"mouseover\", \"mouseout\", 4, \"ngFor\", \"ngForOf\"], [1, \"auto-suggestion\", 3, \"ngClass\", \"mouseover\", \"mouseout\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"w-100\", 3, \"routerLink\", \"click\", \"keyup.enter\"], [1, \"d-flex\", \"justify-content-start\", \"align-items-center\"], [1, \"mr-75\", 3, \"data-feather\"], [1, \"auto-suggestion\", \"justify-content-between\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"w-100\", \"py-50\"], [1, \"d-flex\", \"justify-content-start\"], [\"data-feather\", \"alert-circle\", 1, \"mr-25\"], [\"class\", \"auto-suggestion\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"auto-suggestion\", 3, \"ngClass\"], [\"href\", \"javascript:void(0)\", 1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"w-100\"], [1, \"d-flex\"], [1, \"mr-75\"], [\"alt\", \"png\", \"height\", \"32\", 3, \"src\"], [1, \"search-data\"], [1, \"search-data-title\", \"mb-0\"], [1, \"text-muted\"], [1, \"search-data-size\", \"mr-50\", \"text-muted\"], [\"class\", \"auto-suggestion\", 4, \"ngFor\", \"ngForOf\"], [1, \"auto-suggestion\"], [\"href\", \"javascript:void(0)\", 1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"py-50\", \"w-100\"], [1, \"avatar\", \"mr-75\"]],\n    template: function NavbarSearchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"li\", 0)(1, \"a\", 1);\n        i0.ɵɵlistener(\"click\", function NavbarSearchComponent_Template_a_click_1_listener() {\n          return ctx.toggleSearch();\n        });\n        i0.ɵɵelement(2, \"span\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"span\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"input\", 5, 6);\n        i0.ɵɵlistener(\"keyup\", function NavbarSearchComponent_Template_input_keyup_6_listener($event) {\n          return ctx.searchUpdate($event);\n        })(\"ngModelChange\", function NavbarSearchComponent_Template_input_ngModelChange_6_listener($event) {\n          return ctx.searchText = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function NavbarSearchComponent_Template_button_click_8_listener() {\n          return ctx.toggleSearch();\n        });\n        i0.ɵɵelement(9, \"span\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"ul\", 8, 9)(12, \"li\", 10)(13, \"a\", 11)(14, \"h6\", 12);\n        i0.ɵɵtext(15, \"Pages\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(16, NavbarSearchComponent_ng_container_16_Template, 4, 9, \"ng-container\", 13);\n        i0.ɵɵpipe(17, \"filter\");\n        i0.ɵɵtemplate(18, NavbarSearchComponent_ng_template_18_Template, 6, 0, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(20, \"li\", 10)(21, \"a\", 11)(22, \"h6\", 12);\n        i0.ɵɵtext(23, \"Files\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(24, NavbarSearchComponent_ng_container_24_Template, 4, 9, \"ng-container\", 13);\n        i0.ɵɵpipe(25, \"filter\");\n        i0.ɵɵtemplate(26, NavbarSearchComponent_ng_template_26_Template, 6, 0, \"ng-template\", null, 15, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(28, \"li\", 10)(29, \"a\", 11)(30, \"h6\", 12);\n        i0.ɵɵtext(31, \"Members\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(32, NavbarSearchComponent_ng_container_32_Template, 4, 9, \"ng-container\", 13);\n        i0.ɵɵpipe(33, \"filter\");\n        i0.ɵɵtemplate(34, NavbarSearchComponent_ng_template_34_Template, 6, 0, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const _r3 = i0.ɵɵreference(19);\n        const _r6 = i0.ɵɵreference(27);\n        const _r9 = i0.ɵɵreference(35);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassMap(\"ficon\");\n        i0.ɵɵproperty(\"data-feather\", \"search\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"open\", ctx.openSearchRef === true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data-feather\", \"search\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchText);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"data-feather\", \"x\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"show\", ctx.searchText !== \"\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind3(17, 16, ctx.pages, ctx.searchText, \"title\").length)(\"ngIfElse\", _r3);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind3(25, 20, ctx.files, ctx.searchText, \"title\").length)(\"ngIfElse\", _r6);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind3(33, 24, ctx.contacts, ctx.searchText, \"title\").length)(\"ngIfElse\", _r9);\n      }\n    },\n    dependencies: [i1.RouterLink, i3.NgClass, i3.NgForOf, i3.NgIf, i4.DefaultClassDirective, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.FeatherIconDirective, i7.PerfectScrollbarDirective, i3.SlicePipe, i8.FilterPipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;;;;;;;;;;;;;;;;;;;;IC4BlCC,EAAA,CAAAC,cAAA,aAOC;IAFCD,EAAA,CAAAE,UAAA,uBAAAC,4EAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAaR,EAAA,CAAAS,WAAA,CAAAF,QAAA,CAAAG,KAAA,GAAa,IAAI;IAAA,EAAC,sBAAAC,2EAAA;MAAA,MAAAP,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OACnBR,EAAA,CAAAS,WAAA,CAAAF,QAAA,CAAAG,KAAA,GAAa,KAAK;IAAA,EADC;IAG/BV,EAAA,CAAAC,cAAA,YAKG;IAHDD,EAAA,CAAAE,UAAA,mBAAAU,uEAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAS,WAAA,CAAAI,OAAA,CAAAE,YAAA,EAAc;IAAA,EAAC,yBAAAC,6EAAA;MAAA,MAAAZ,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAS,OAAA,GAAAjB,EAAA,CAAAc,aAAA;MAAA,OACTd,EAAA,CAAAS,WAAA,CAAAQ,OAAA,CAAAC,QAAA,CAAAX,QAAA,CAAAY,IAAA,CAAmB;IAAA,EADV;IAGvBnB,EAAA,CAAAC,cAAA,cAA6D;IAC5DD,EAAA,CAAAoB,SAAA,YAAgD;IAAApB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAqB,MAAA,GAAgB;IAAArB,EAAA,CAAAsB,YAAA,EAAO;;;;;;IAVjFtB,EAAA,CAAAuB,WAAA,iBAAAhB,QAAA,CAAAG,KAAA,CAAiC;IADjCV,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,EAAAC,KAAA,KAAAC,OAAA,CAAAC,WAAA,EAA+C;IAS7C7B,EAAA,CAAA8B,SAAA,GAAwB;IAAxB9B,EAAA,CAAAwB,UAAA,eAAAjB,QAAA,CAAAY,IAAA,CAAwB;IAEnBnB,EAAA,CAAA8B,SAAA,GAA0B;IAA1B9B,EAAA,CAAAwB,UAAA,iBAAAjB,QAAA,CAAAwB,IAAA,CAA0B;IAAyB/B,EAAA,CAAA8B,SAAA,GAAgB;IAAhB9B,EAAA,CAAAgC,iBAAA,CAAAzB,QAAA,CAAA0B,KAAA,CAAgB;;;;;IAf9EjC,EAAA,CAAAkC,uBAAA,GAAuF;IACrFlC,EAAA,CAAAmC,UAAA,IAAAC,mDAAA,iBAiBK;;;IACPpC,EAAA,CAAAqC,qBAAA,EAAe;;;;IAhBMrC,EAAA,CAAA8B,SAAA,GAAoD;IAApD9B,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAsC,WAAA,OAAAtC,EAAA,CAAAsC,WAAA,OAAAC,MAAA,CAAAC,KAAA,EAAAD,MAAA,CAAAE,UAAA,kBAAoD;;;;;IAoBvEzC,EAAA,CAAAC,cAAA,aAAoD;IAG9CD,EAAA,CAAAoB,SAAA,YAAiD;IACjDpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAqB,MAAA,wBAAiB;IAAArB,EAAA,CAAAsB,YAAA,EAAO;;;;;IAYpCtB,EAAA,CAAAC,cAAA,aAIC;IAGwBD,EAAA,CAAAoB,SAAA,cAA+C;IAAApB,EAAA,CAAAsB,YAAA,EAAM;IACxEtB,EAAA,CAAAC,cAAA,cAAyB;IACWD,EAAA,CAAAqB,MAAA,GAAgB;IAAArB,EAAA,CAAAsB,YAAA,EAAI;IACtDtB,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAqB,MAAA,GAAa;IAAArB,EAAA,CAAAsB,YAAA,EAAQ;IAGnDtB,EAAA,CAAAC,cAAA,iBAAiD;IAAAD,EAAA,CAAAqB,MAAA,IAAe;IAAArB,EAAA,CAAAsB,YAAA,EAAQ;;;;;IAV1EtB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,EAAAgB,OAAA,CAAAC,CAAA,KAAAD,OAAA,CAAAb,WAAA,EAA+C;IAInB7B,EAAA,CAAA8B,SAAA,GAAiB;IAAjB9B,EAAA,CAAAwB,UAAA,QAAAoB,QAAA,CAAAC,IAAA,EAAA7C,EAAA,CAAA8C,aAAA,CAAiB;IAEL9C,EAAA,CAAA8B,SAAA,GAAgB;IAAhB9B,EAAA,CAAAgC,iBAAA,CAAAY,QAAA,CAAAX,KAAA,CAAgB;IACxBjC,EAAA,CAAA8B,SAAA,GAAa;IAAb9B,EAAA,CAAAgC,iBAAA,CAAAY,QAAA,CAAAG,EAAA,CAAa;IAGM/C,EAAA,CAAA8B,SAAA,GAAe;IAAf9B,EAAA,CAAAgC,iBAAA,CAAAY,QAAA,CAAAI,IAAA,CAAe;;;;;IAdtEhD,EAAA,CAAAkC,uBAAA,GAAuF;IACrFlC,EAAA,CAAAmC,UAAA,IAAAc,mDAAA,kBAeK;;;IACPjD,EAAA,CAAAqC,qBAAA,EAAe;;;;IAdMrC,EAAA,CAAA8B,SAAA,GAAkD;IAAlD9B,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAsC,WAAA,OAAAtC,EAAA,CAAAsC,WAAA,OAAAY,MAAA,CAAAC,KAAA,EAAAD,MAAA,CAAAT,UAAA,kBAAkD;;;;;IAiBrEzC,EAAA,CAAAC,cAAA,aAAoD;IAG9CD,EAAA,CAAAoB,SAAA,YAAiD;IACjDpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAqB,MAAA,wBAAiB;IAAArB,EAAA,CAAAsB,YAAA,EAAO;;;;;IAYpCtB,EAAA,CAAAC,cAAA,aAAuG;IAGvED,EAAA,CAAAoB,SAAA,cAAiD;IAAApB,EAAA,CAAAsB,YAAA,EAAM;IACjFtB,EAAA,CAAAC,cAAA,cAAyB;IACWD,EAAA,CAAAqB,MAAA,GAAmB;IAAArB,EAAA,CAAAsB,YAAA,EAAI;IACzDtB,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAqB,MAAA,GAAmB;IAAArB,EAAA,CAAAsB,YAAA,EAAQ;IAGzDtB,EAAA,CAAAC,cAAA,iBAAiD;IAAAD,EAAA,CAAAqB,MAAA,IAAkB;IAAArB,EAAA,CAAAsB,YAAA,EAAQ;;;;IAN1CtB,EAAA,CAAA8B,SAAA,GAAmB;IAAnB9B,EAAA,CAAAwB,UAAA,QAAA4B,WAAA,CAAAC,GAAA,EAAArD,EAAA,CAAA8C,aAAA,CAAmB;IAEd9C,EAAA,CAAA8B,SAAA,GAAmB;IAAnB9B,EAAA,CAAAgC,iBAAA,CAAAoB,WAAA,CAAAnB,KAAA,CAAmB;IAC3BjC,EAAA,CAAA8B,SAAA,GAAmB;IAAnB9B,EAAA,CAAAgC,iBAAA,CAAAoB,WAAA,CAAAE,KAAA,CAAmB;IAGAtD,EAAA,CAAA8B,SAAA,GAAkB;IAAlB9B,EAAA,CAAAgC,iBAAA,CAAAoB,WAAA,CAAAG,IAAA,CAAkB;;;;;IAVzEvD,EAAA,CAAAkC,uBAAA,GAA4F;IAC1FlC,EAAA,CAAAmC,UAAA,IAAAqB,mDAAA,kBAWK;;;IACPxD,EAAA,CAAAqC,qBAAA,EAAe;;;;IAZmCrC,EAAA,CAAA8B,SAAA,GAAqD;IAArD9B,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAsC,WAAA,OAAAtC,EAAA,CAAAsC,WAAA,OAAAmB,MAAA,CAAAC,QAAA,EAAAD,MAAA,CAAAhB,UAAA,kBAAqD;;;;;IAerGzC,EAAA,CAAAC,cAAA,aAAoD;IAG9CD,EAAA,CAAAoB,SAAA,YAAiD;IACjDpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAqB,MAAA,wBAAiB;IAAArB,EAAA,CAAAsB,YAAA,EAAO;;;AD9G5C,OAAM,MAAOqC,qBAAqB;EAeAC,EAAEA,CAAA;IAChC,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACrB,UAAU,GAAG,EAAE;EACtB;EAC4CsB,QAAQA,CAACC,KAAK;IACxD,IAAIA,KAAK,CAACC,MAAM,CAACC,SAAS,KAAK,iBAAiB,EAAE;MAChD,IAAI,CAACL,aAAa,EAAE;MACpB,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACrB,UAAU,GAAG,EAAE;;EAExB;EAEA;;;;;;EAMA0B,YAC4BC,QAAQ,EAC1BC,WAAuB,EACvBC,MAAc,EACfC,cAA6B;IAHV,KAAAH,QAAQ,GAARA,QAAQ;IAC1B,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,cAAc,GAAdA,cAAc;IArCvB;IACO,KAAA9B,UAAU,GAAG,EAAE;IACf,KAAAqB,aAAa,GAAG,KAAK;IACrB,KAAAjC,WAAW,GAAG,CAAC;IAEf,KAAAW,KAAK,GAAG,EAAE;IACV,KAAAW,KAAK,GAAG,EAAE;IACV,KAAAO,QAAQ,GAAG,EAAE;EA+BjB;EAEH;EACA;EAEA;;;EAGAc,eAAeA,CAAA;IACb,IAAI,CAAC3C,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI,CAAC4C,eAAe,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC5C,WAAW,GAAG,IAAI,CAACA,WAAW;EACxG;EAEA;;;EAGA6C,eAAeA,CAAA;IACb,IAAI,CAAC7C,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC,GAAG,EAAE,IAAI,CAACA,WAAW,GAAG,CAAC;EAClE;EAEA;;;EAGAgC,aAAaA,CAAA;IACX,IAAI,CAACO,QAAQ,CAACO,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;EAC9E;EAEA;;;;;EAKAC,cAAcA,CAACd,KAAK;IAClB,IAAI,EAAE,KAAKA,KAAK,CAACe,OAAO,EAAE;MACxB,OAAO,IAAI,CAACL,eAAe,EAAE;;IAE/B,IAAI,EAAE,KAAKV,KAAK,CAACe,OAAO,EAAE;MACxB,OAAO,IAAI,CAACP,eAAe,EAAE;;IAE/B,IAAI,EAAE,KAAKR,KAAK,CAACe,OAAO,EAAE;MACxB;MACA;MACA,IAAIC,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACC,aAAa,CAACC,sBAAsB,CAAC,cAAc,CAAC;MAC7FH,YAAY,CAAC,CAAC,CAAC,EAAEI,QAAQ,CAAC,CAAC,CAAC,CAACC,KAAK,EAAE;;EAExC;EAEA;;;EAGAtE,YAAYA,CAAA;IACV,IAAI,CAACwD,cAAc,CAACe,sBAAsB,CAACC,IAAI,CAAC,KAAK,CAAC;IACtD,IAAI,CAAC1B,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACjC,WAAW,GAAG,CAAC;IACpB2D,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,aAAa,CAACP,aAAa,CAACQ,KAAK,EAAE;IAC1C,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC5B,aAAa,KAAK,KAAK,EAAE;MAChC,IAAI,CAACM,QAAQ,CAACO,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;MAC5E,IAAI,CAACpC,UAAU,GAAG,EAAE;;EAExB;EAEA;;;;;EAKAkD,YAAYA,CAAC3B,KAAK;IAChB,MAAM4B,GAAG,GAAG5B,KAAK,CAACC,MAAM,CAAC4B,KAAK,CAACC,WAAW,EAAE;IAC5C,IAAIF,GAAG,KAAK,EAAE,EAAE;MACd,IAAI,CAACxB,QAAQ,CAACO,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACmB,GAAG,CAAC,cAAc,CAAC;KAC1E,MAAM;MACL,IAAI,CAAC3B,QAAQ,CAACO,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;;IAE9E,IAAI,CAACC,cAAc,CAACd,KAAK,CAAC;EAC5B;EAEA;EACA;EAEA;;;EAGAgC,QAAQA,CAAA;IACN,IAAI,CAACzB,cAAc,CAAC0B,eAAe,CAACC,SAAS,CAACC,GAAG,IAAG;MAClD,IAAI,CAACC,OAAO,GAAGD,GAAG;MAClB,IAAI,CAAC3D,KAAK,GAAG,IAAI,CAAC4D,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;MACjC,IAAI,CAAC5B,eAAe,GAAG,IAAI,CAAC2B,OAAO,CAAC,CAAC,CAAC,CAACE,WAAW;MAClD,IAAI,CAACnD,KAAK,GAAG,IAAI,CAACiD,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;MACjC,IAAI,CAAC3C,QAAQ,GAAG,IAAI,CAAC0C,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;IACtC,CAAC,CAAC;EACJ;EAAC,QAAAE,CAAA;qBApIU5C,qBAAqB,EAAA3D,EAAA,CAAAwG,iBAAA,CAmCtBzG,QAAQ,GAAAC,EAAA,CAAAwG,iBAAA,CAAAxG,EAAA,CAAAyG,UAAA,GAAAzG,EAAA,CAAAwG,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAA3G,EAAA,CAAAwG,iBAAA,CAAAI,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAnCPnD,qBAAqB;IAAAoD,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;iBAArBC,GAAA,CAAAvD,EAAA,EAAI;QAAA,qBAAAwD,+CAAAC,MAAA;UAAA,OAAJF,GAAA,CAAApD,QAAA,CAAAsD,MAAA,CAAgB;QAAA,UAAArH,EAAA,CAAAsH,iBAAA;;;;;;;;QCV7BtH,EAAA,CAAAC,cAAA,YAAgC;QAEMD,EAAA,CAAAE,UAAA,mBAAAqH,kDAAA;UAAA,OAASJ,GAAA,CAAApG,YAAA,EAAc;QAAA,EAAC;QACzDf,EAAA,CAAAoB,SAAA,cACF;QAAApB,EAAA,CAAAsB,YAAA,EAAI;QAGLtB,EAAA,CAAAC,cAAA,aAAgE;QAE/BD,EAAA,CAAAoB,SAAA,cAAuC;QAAApB,EAAA,CAAAsB,YAAA,EAAM;QAC5EtB,EAAA,CAAAC,cAAA,kBASE;QAHAD,EAAA,CAAAE,UAAA,mBAAAsH,sDAAAH,MAAA;UAAA,OAASF,GAAA,CAAAxB,YAAA,CAAA0B,MAAA,CAAoB;QAAA,EAAC,2BAAAI,8DAAAJ,MAAA;UAAA,OAAAF,GAAA,CAAA1E,UAAA,GAAA4E,MAAA;QAAA;QANhCrH,EAAA,CAAAsB,YAAA,EASE;QAAAtB,EAAA,CAAAC,cAAA,gBAAoE;QAAzBD,EAAA,CAAAE,UAAA,mBAAAwH,uDAAA;UAAA,OAASP,GAAA,CAAApG,YAAA,EAAc;QAAA,EAAC;QAACf,EAAA,CAAAoB,SAAA,cAAkC;QAAApB,EAAA,CAAAsB,YAAA,EAAS;QAGjHtB,EAAA,CAAAC,cAAA,gBAAuG;QAGjCD,EAAA,CAAAqB,MAAA,aAAK;QAAArB,EAAA,CAAAsB,YAAA,EAAK;QAE9EtB,EAAA,CAAAmC,UAAA,KAAAwF,8CAAA,2BAmBe;;QAGf3H,EAAA,CAAAmC,UAAA,KAAAyF,6CAAA,iCAAA5H,EAAA,CAAA6H,sBAAA,CASc;QAId7H,EAAA,CAAAC,cAAA,cAAsC;QAC8BD,EAAA,CAAAqB,MAAA,aAAK;QAAArB,EAAA,CAAAsB,YAAA,EAAK;QAE9EtB,EAAA,CAAAmC,UAAA,KAAA2F,8CAAA,2BAiBe;;QAEf9H,EAAA,CAAAmC,UAAA,KAAA4F,6CAAA,iCAAA/H,EAAA,CAAA6H,sBAAA,CASc;QAId7H,EAAA,CAAAC,cAAA,cAAsC;QAC8BD,EAAA,CAAAqB,MAAA,eAAO;QAAArB,EAAA,CAAAsB,YAAA,EAAK;QAEhFtB,EAAA,CAAAmC,UAAA,KAAA6F,8CAAA,2BAae;;QAEfhI,EAAA,CAAAmC,UAAA,KAAA8F,6CAAA,iCAAAjI,EAAA,CAAA6H,sBAAA,CASc;QAEhB7H,EAAA,CAAAsB,YAAA,EAAK;;;;;;QA3H4BtB,EAAA,CAAA8B,SAAA,GAAiB;QAAjB9B,EAAA,CAAAkI,UAAA,SAAiB;QAA3ClI,EAAA,CAAAwB,UAAA,0BAAyB;QAIRxB,EAAA,CAAA8B,SAAA,GAAqC;QAArC9B,EAAA,CAAAuB,WAAA,SAAA4F,GAAA,CAAArD,aAAA,UAAqC;QAExB9D,EAAA,CAAA8B,SAAA,GAAyB;QAAzB9B,EAAA,CAAAwB,UAAA,0BAAyB;QAQ5DxB,EAAA,CAAA8B,SAAA,GAAwB;QAAxB9B,EAAA,CAAAwB,UAAA,YAAA2F,GAAA,CAAA1E,UAAA,CAAwB;QAEkDzC,EAAA,CAAA8B,SAAA,GAAoB;QAApB9B,EAAA,CAAAwB,UAAA,qBAAoB;QAGpCxB,EAAA,CAAA8B,SAAA,GAAgC;QAAhC9B,EAAA,CAAAuB,WAAA,SAAA4F,GAAA,CAAA1E,UAAA,QAAgC;QAK3EzC,EAAA,CAAA8B,SAAA,GAAmD;QAAnD9B,EAAA,CAAAwB,UAAA,SAAAxB,EAAA,CAAAsC,WAAA,SAAA6E,GAAA,CAAA3E,KAAA,EAAA2E,GAAA,CAAA1E,UAAA,WAAA0F,MAAA,CAAmD,aAAAC,GAAA;QAsCnDpI,EAAA,CAAA8B,SAAA,GAAmD;QAAnD9B,EAAA,CAAAwB,UAAA,SAAAxB,EAAA,CAAAsC,WAAA,SAAA6E,GAAA,CAAAhE,KAAA,EAAAgE,GAAA,CAAA1E,UAAA,WAAA0F,MAAA,CAAmD,aAAAE,GAAA;QAmCnDrI,EAAA,CAAA8B,SAAA,GAAsD;QAAtD9B,EAAA,CAAAwB,UAAA,SAAAxB,EAAA,CAAAsC,WAAA,SAAA6E,GAAA,CAAAzD,QAAA,EAAAyD,GAAA,CAAA1E,UAAA,WAAA0F,MAAA,CAAsD,aAAAG,GAAA", "names": ["DOCUMENT", "i0", "ɵɵelementStart", "ɵɵlistener", "NavbarSearchComponent_ng_container_16_li_1_Template_li_mouseover_0_listener", "restoredCtx", "ɵɵrestoreView", "_r15", "page_r12", "$implicit", "ɵɵresetView", "hover", "NavbarSearchComponent_ng_container_16_li_1_Template_li_mouseout_0_listener", "NavbarSearchComponent_ng_container_16_li_1_Template_a_click_1_listener", "ctx_r17", "ɵɵnextContext", "toggleSearch", "NavbarSearchComponent_ng_container_16_li_1_Template_a_keyup_enter_1_listener", "ctx_r18", "navigate", "link", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "i_r13", "ctx_r11", "activeIndex", "ɵɵadvance", "icon", "ɵɵtextInterpolate", "title", "ɵɵelementContainerStart", "ɵɵtemplate", "NavbarSearchComponent_ng_container_16_li_1_Template", "ɵɵelementContainerEnd", "ɵɵpipeBind3", "ctx_r2", "pages", "searchText", "ctx_r19", "i", "file_r20", "file", "ɵɵsanitizeUrl", "by", "size", "NavbarSearchComponent_ng_container_24_li_1_Template", "ctx_r5", "files", "contact_r22", "img", "email", "date", "NavbarSearchComponent_ng_container_32_li_1_Template", "ctx_r8", "contacts", "NavbarSearchComponent", "fn", "removeOverlay", "openSearchRef", "clickout", "event", "target", "className", "constructor", "document", "_elementRef", "router", "_searchService", "nextActiveMatch", "pageSearchLimit", "prevActiveMatch", "querySelector", "classList", "remove", "autoSuggestion", "keyCode", "current_item", "_pageListElement", "nativeElement", "getElementsByClassName", "children", "click", "onIsBookmarkOpenChange", "next", "setTimeout", "_inputElement", "focus", "searchUpdate", "val", "value", "toLowerCase", "add", "ngOnInit", "onApiDataChange", "subscribe", "res", "apiData", "data", "searchLimit", "_", "ɵɵdirectiveInject", "ElementRef", "i1", "Router", "i2", "SearchService", "_2", "selectors", "viewQuery", "NavbarSearchComponent_Query", "rf", "ctx", "NavbarSearchComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "NavbarSearchComponent_Template_a_click_1_listener", "NavbarSearchComponent_Template_input_keyup_6_listener", "NavbarSearchComponent_Template_input_ngModelChange_6_listener", "NavbarSearchComponent_Template_button_click_8_listener", "NavbarSearchComponent_ng_container_16_Template", "NavbarSearchComponent_ng_template_18_Template", "ɵɵtemplateRefExtractor", "NavbarSearchComponent_ng_container_24_Template", "NavbarSearchComponent_ng_template_26_Template", "NavbarSearchComponent_ng_container_32_Template", "NavbarSearchComponent_ng_template_34_Template", "ɵɵclassMap", "length", "_r3", "_r6", "_r9"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar-search\\navbar-search.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar-search\\navbar-search.component.html"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { Component, ElementRef, HostListener, Inject, OnInit, ViewChild } from '@angular/core';\r\n\r\nimport { SearchService } from 'app/layout/components/navbar/navbar-search/search.service';\r\n\r\n@Component({\r\n  selector: 'app-navbar-search',\r\n  templateUrl: './navbar-search.component.html'\r\n})\r\nexport class NavbarSearchComponent implements OnInit {\r\n  // Public\r\n  public searchText = '';\r\n  public openSearchRef = false;\r\n  public activeIndex = 0;\r\n  public apiData;\r\n  public pages = [];\r\n  public files = [];\r\n  public contacts = [];\r\n  public pageSearchLimit;\r\n\r\n  // Decorators\r\n  @ViewChild('openSearch') private _inputElement: ElementRef;\r\n  @ViewChild('pageList') private _pageListElement: ElementRef;\r\n\r\n  @HostListener('keydown.escape') fn() {\r\n    this.removeOverlay();\r\n    this.openSearchRef = false;\r\n    this.searchText = '';\r\n  }\r\n  @HostListener('document:click', ['$event']) clickout(event) {\r\n    if (event.target.className === 'content-overlay') {\r\n      this.removeOverlay();\r\n      this.openSearchRef = false;\r\n      this.searchText = '';\r\n    }\r\n  }\r\n\r\n  /**\r\n   *\r\n   * @param document\r\n   * @param router\r\n   * @param _searchService\r\n   */\r\n  constructor(\r\n    @Inject(DOCUMENT) private document,\r\n    private _elementRef: ElementRef,\r\n    private router: Router,\r\n    public _searchService: SearchService\r\n  ) {}\r\n\r\n  // Public Methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Next Active Match\r\n   */\r\n  nextActiveMatch() {\r\n    this.activeIndex = this.activeIndex < this.pageSearchLimit - 1 ? ++this.activeIndex : this.activeIndex;\r\n  }\r\n\r\n  /**\r\n   * Previous Active Match\r\n   */\r\n  prevActiveMatch() {\r\n    this.activeIndex = this.activeIndex > 0 ? --this.activeIndex : 0;\r\n  }\r\n\r\n  /**\r\n   * Remove Overlay\r\n   */\r\n  removeOverlay() {\r\n    this.document.querySelector('.app-content').classList.remove('show-overlay');\r\n  }\r\n\r\n  /**\r\n   * Auto Suggestion\r\n   *\r\n   * @param event\r\n   */\r\n  autoSuggestion(event) {\r\n    if (38 === event.keyCode) {\r\n      return this.prevActiveMatch();\r\n    }\r\n    if (40 === event.keyCode) {\r\n      return this.nextActiveMatch();\r\n    }\r\n    if (13 === event.keyCode) {\r\n      // Navigate to activeIndex\r\n      // ! Todo: Improve this code\r\n      let current_item = this._pageListElement.nativeElement.getElementsByClassName('current_item');\r\n      current_item[0]?.children[0].click();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Toggle Search\r\n   */\r\n  toggleSearch() {\r\n    this._searchService.onIsBookmarkOpenChange.next(false);\r\n    this.removeOverlay();\r\n    this.openSearchRef = !this.openSearchRef;\r\n    this.activeIndex = 0;\r\n    setTimeout(() => {\r\n      this._inputElement.nativeElement.focus();\r\n    });\r\n\r\n    if (this.openSearchRef === false) {\r\n      this.document.querySelector('.app-content').classList.remove('show-overlay');\r\n      this.searchText = '';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search Update\r\n   *\r\n   * @param event\r\n   */\r\n  searchUpdate(event) {\r\n    const val = event.target.value.toLowerCase();\r\n    if (val !== '') {\r\n      this.document.querySelector('.app-content').classList.add('show-overlay');\r\n    } else {\r\n      this.document.querySelector('.app-content').classList.remove('show-overlay');\r\n    }\r\n    this.autoSuggestion(event);\r\n  }\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit(): void {\r\n    this._searchService.onApiDataChange.subscribe(res => {\r\n      this.apiData = res;\r\n      this.pages = this.apiData[0].data;\r\n      this.pageSearchLimit = this.apiData[0].searchLimit;\r\n      this.files = this.apiData[1].data;\r\n      this.contacts = this.apiData[2].data;\r\n    });\r\n  }\r\n}\r\n", "<li class=\"nav-item nav-search\">\r\n  <!-- Search icon -->\r\n  <a class=\"nav-link nav-link-search\" (click)=\"toggleSearch()\"\r\n    ><span [data-feather]=\"'search'\" [class]=\"'ficon'\"></span\r\n  ></a>\r\n\r\n  <!-- Search -->\r\n  <div class=\"search-input\" [class.open]=\"openSearchRef === true\">\r\n    <!-- Search Input -->\r\n    <div class=\"search-input-icon\"><span [data-feather]=\"'search'\"></span></div>\r\n    <input\r\n      class=\"form-control input\"\r\n      type=\"text\"\r\n      placeholder=\"Explore Vuexy...\"\r\n      tabindex=\"-1\"\r\n      data-search=\"search\"\r\n      (keyup)=\"searchUpdate($event)\"\r\n      [(ngModel)]=\"searchText\"\r\n      #openSearch\r\n    /><button class=\"btn search-input-close p-0\" (click)=\"toggleSearch()\"><span [data-feather]=\"'x'\"></span></button>\r\n\r\n    <!-- Search List -->\r\n    <ul class=\"search-list search-list-main\" [perfectScrollbar] [class.show]=\"searchText !== ''\" #pageList>\r\n      <!-- Pages -->\r\n      <li class=\"d-flex align-items-center\">\r\n        <a href=\"javascript:void(0)\"><h6 class=\"section-label mt-75 mb-0\">Pages</h6></a>\r\n      </li>\r\n      <ng-container *ngIf=\"(pages | filter: searchText:'title').length; else noResultsPages\">\r\n        <li\r\n          class=\"auto-suggestion\"\r\n          *ngFor=\"let page of pages | filter: searchText:'title' | slice: 0:4; let i = index\"\r\n          [ngClass]=\"{ current_item: i === activeIndex }\"\r\n          [class.current_item]=\"page.hover\"\r\n          (mouseover)=\"page.hover = true\"\r\n          (mouseout)=\"page.hover = false\"\r\n        >\r\n          <a\r\n            class=\"d-flex align-items-center justify-content-between w-100\"\r\n            (click)=\"toggleSearch()\"\r\n            (keyup.enter)=\"navigate(page.link)\"\r\n            [routerLink]=\"page.link\"\r\n            ><div class=\"d-flex justify-content-start align-items-center\">\r\n              <i [data-feather]=\"page.icon\" class=\"mr-75\"></i><span>{{ page.title }}</span>\r\n            </div></a\r\n          >\r\n        </li>\r\n      </ng-container>\r\n\r\n      <!-- No results found -->\r\n      <ng-template #noResultsPages>\r\n        <li class=\"auto-suggestion justify-content-between\">\r\n          <a class=\"d-flex align-items-center justify-content-between w-100 py-50\">\r\n            <div class=\"d-flex justify-content-start\">\r\n              <i data-feather=\"alert-circle\" class=\"mr-25\"></i>\r\n              <span>No results found.</span>\r\n            </div></a\r\n          >\r\n        </li>\r\n      </ng-template>\r\n      <!--/ Pages -->\r\n\r\n      <!-- Files -->\r\n      <li class=\"d-flex align-items-center\">\r\n        <a href=\"javascript:void(0)\"><h6 class=\"section-label mt-75 mb-0\">Files</h6></a>\r\n      </li>\r\n      <ng-container *ngIf=\"(files | filter: searchText:'title').length; else noResultsFiles\">\r\n        <li\r\n          class=\"auto-suggestion\"\r\n          *ngFor=\"let file of files | filter: searchText:'title' | slice: 0:4\"\r\n          [ngClass]=\"{ current_item: i === activeIndex }\"\r\n        >\r\n          <a class=\"d-flex align-items-center justify-content-between w-100\" href=\"javascript:void(0)\"\r\n            ><div class=\"d-flex\">\r\n              <div class=\"mr-75\"><img [src]=\"file.file\" alt=\"png\" height=\"32\" /></div>\r\n              <div class=\"search-data\">\r\n                <p class=\"search-data-title mb-0\">{{ file.title }}</p>\r\n                <small class=\"text-muted\">{{ file.by }}</small>\r\n              </div>\r\n            </div>\r\n            <small class=\"search-data-size mr-50 text-muted\">{{ file.size }}</small></a\r\n          >\r\n        </li>\r\n      </ng-container>\r\n      <!-- No results found -->\r\n      <ng-template #noResultsFiles>\r\n        <li class=\"auto-suggestion justify-content-between\">\r\n          <a class=\"d-flex align-items-center justify-content-between w-100 py-50\">\r\n            <div class=\"d-flex justify-content-start\">\r\n              <i data-feather=\"alert-circle\" class=\"mr-25\"></i>\r\n              <span>No results found.</span>\r\n            </div></a\r\n          >\r\n        </li>\r\n      </ng-template>\r\n      <!--/ Files -->\r\n\r\n      <!-- Members -->\r\n      <li class=\"d-flex align-items-center\">\r\n        <a href=\"javascript:void(0)\"><h6 class=\"section-label mt-75 mb-0\">Members</h6></a>\r\n      </li>\r\n      <ng-container *ngIf=\"(contacts | filter: searchText:'title').length; else noResultsMembers\">\r\n        <li class=\"auto-suggestion\" *ngFor=\"let contact of contacts | filter: searchText:'title' | slice: 0:4\">\r\n          <a class=\"d-flex align-items-center justify-content-between py-50 w-100\" href=\"javascript:void(0)\"\r\n            ><div class=\"d-flex align-items-center\">\r\n              <div class=\"avatar mr-75\"><img [src]=\"contact.img\" alt=\"png\" height=\"32\" /></div>\r\n              <div class=\"search-data\">\r\n                <p class=\"search-data-title mb-0\">{{ contact.title }}</p>\r\n                <small class=\"text-muted\">{{ contact.email }}</small>\r\n              </div>\r\n            </div>\r\n            <small class=\"search-data-size mr-50 text-muted\">{{ contact.date }}</small></a\r\n          >\r\n        </li>\r\n      </ng-container>\r\n      <!-- No results found -->\r\n      <ng-template #noResultsMembers>\r\n        <li class=\"auto-suggestion justify-content-between\">\r\n          <a class=\"d-flex align-items-center justify-content-between w-100 py-50\">\r\n            <div class=\"d-flex justify-content-start\">\r\n              <i data-feather=\"alert-circle\" class=\"mr-25\"></i>\r\n              <span>No results found.</span>\r\n            </div></a\r\n          >\r\n        </li>\r\n      </ng-template>\r\n      <!--/ Members -->\r\n    </ul>\r\n    <!--/ Search List -->\r\n  </div>\r\n</li>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { tokenRegex, revFormat, formats } from \"./formatting\";\nimport { defaults } from \"../types/options\";\nimport { english } from \"../l10n/default\";\nexport const createDateFormatter = ({\n  config = defaults,\n  l10n = english,\n  isMobile = false\n}) => (dateObj, frmt, overrideLocale) => {\n  const locale = overrideLocale || l10n;\n  if (config.formatDate !== undefined && !isMobile) {\n    return config.formatDate(dateObj, frmt, locale);\n  }\n  return frmt.split(\"\").map((c, i, arr) => formats[c] && arr[i - 1] !== \"\\\\\" ? formats[c](dateObj, locale, config) : c !== \"\\\\\" ? c : \"\").join(\"\");\n};\nexport const createDateParser = ({\n  config = defaults,\n  l10n = english\n}) => (date, givenFormat, timeless, customLocale) => {\n  if (date !== 0 && !date) return undefined;\n  const locale = customLocale || l10n;\n  let parsedDate;\n  const dateOrig = date;\n  if (date instanceof Date) parsedDate = new Date(date.getTime());else if (typeof date !== \"string\" && date.toFixed !== undefined) parsedDate = new Date(date);else if (typeof date === \"string\") {\n    const format = givenFormat || (config || defaults).dateFormat;\n    const datestr = String(date).trim();\n    if (datestr === \"today\") {\n      parsedDate = new Date();\n      timeless = true;\n    } else if (/Z$/.test(datestr) || /GMT$/.test(datestr)) parsedDate = new Date(date);else if (config && config.parseDate) parsedDate = config.parseDate(date, format);else {\n      parsedDate = !config || !config.noCalendar ? new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0) : new Date(new Date().setHours(0, 0, 0, 0));\n      let matched,\n        ops = [];\n      for (let i = 0, matchIndex = 0, regexStr = \"\"; i < format.length; i++) {\n        const token = format[i];\n        const isBackSlash = token === \"\\\\\";\n        const escaped = format[i - 1] === \"\\\\\" || isBackSlash;\n        if (tokenRegex[token] && !escaped) {\n          regexStr += tokenRegex[token];\n          const match = new RegExp(regexStr).exec(date);\n          if (match && (matched = true)) {\n            ops[token !== \"Y\" ? \"push\" : \"unshift\"]({\n              fn: revFormat[token],\n              val: match[++matchIndex]\n            });\n          }\n        } else if (!isBackSlash) regexStr += \".\";\n        ops.forEach(({\n          fn,\n          val\n        }) => parsedDate = fn(parsedDate, val, locale) || parsedDate);\n      }\n      parsedDate = matched ? parsedDate : undefined;\n    }\n  }\n  if (!(parsedDate instanceof Date && !isNaN(parsedDate.getTime()))) {\n    config.errorHandler(new Error(`Invalid date provided: ${dateOrig}`));\n    return undefined;\n  }\n  if (timeless === true) parsedDate.setHours(0, 0, 0, 0);\n  return parsedDate;\n};\nexport function compareDates(date1, date2, timeless = true) {\n  if (timeless !== false) {\n    return new Date(date1.getTime()).setHours(0, 0, 0, 0) - new Date(date2.getTime()).setHours(0, 0, 0, 0);\n  }\n  return date1.getTime() - date2.getTime();\n}\nexport function compareTimes(date1, date2) {\n  return 3600 * (date1.getHours() - date2.getHours()) + 60 * (date1.getMinutes() - date2.getMinutes()) + date1.getSeconds() - date2.getSeconds();\n}\nexport const isBetween = (ts, ts1, ts2) => {\n  return ts > Math.min(ts1, ts2) && ts < Math.max(ts1, ts2);\n};\nexport const duration = {\n  DAY: 86400000\n};\nexport function getDefaultHours(config) {\n  let hours = config.defaultHour;\n  let minutes = config.defaultMinute;\n  let seconds = config.defaultSeconds;\n  if (config.minDate !== undefined) {\n    const minHour = config.minDate.getHours();\n    const minMinutes = config.minDate.getMinutes();\n    const minSeconds = config.minDate.getSeconds();\n    if (hours < minHour) {\n      hours = minHour;\n    }\n    if (hours === minHour && minutes < minMinutes) {\n      minutes = minMinutes;\n    }\n    if (hours === minHour && minutes === minMinutes && seconds < minSeconds) seconds = config.minDate.getSeconds();\n  }\n  if (config.maxDate !== undefined) {\n    const maxHr = config.maxDate.getHours();\n    const maxMinutes = config.maxDate.getMinutes();\n    hours = Math.min(hours, maxHr);\n    if (hours === maxHr) minutes = Math.min(maxMinutes, minutes);\n    if (hours === maxHr && minutes === maxMinutes) seconds = config.maxDate.getSeconds();\n  }\n  return {\n    hours,\n    minutes,\n    seconds\n  };\n}", "map": {"version": 3, "names": ["tokenRegex", "revFormat", "formats", "defaults", "english", "createDateFormatter", "config", "l10n", "isMobile", "date<PERSON><PERSON>j", "frmt", "overrideLocale", "locale", "formatDate", "undefined", "split", "map", "c", "i", "arr", "join", "createDateParser", "date", "givenFormat", "timeless", "customLocale", "parsedDate", "date<PERSON><PERSON>", "Date", "getTime", "toFixed", "format", "dateFormat", "datestr", "String", "trim", "test", "parseDate", "noCalendar", "getFullYear", "setHours", "matched", "ops", "matchIndex", "regexStr", "length", "token", "isBackSlash", "escaped", "match", "RegExp", "exec", "fn", "val", "for<PERSON>ach", "isNaN", "<PERSON><PERSON><PERSON><PERSON>", "Error", "compareDates", "date1", "date2", "compareTimes", "getHours", "getMinutes", "getSeconds", "isBetween", "ts", "ts1", "ts2", "Math", "min", "max", "duration", "DAY", "getDefaultHours", "hours", "defaultHour", "minutes", "defaultMinute", "seconds", "defaultSeconds", "minDate", "minHour", "minMinutes", "minSeconds", "maxDate", "maxHr", "maxMinutes"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/flatpickr/dist/esm/utils/dates.js"], "sourcesContent": ["import { tokenRegex, revFormat, formats, } from \"./formatting\";\nimport { defaults } from \"../types/options\";\nimport { english } from \"../l10n/default\";\nexport const createDateFormatter = ({ config = defaults, l10n = english, isMobile = false, }) => (dateObj, frmt, overrideLocale) => {\n    const locale = overrideLocale || l10n;\n    if (config.formatDate !== undefined && !isMobile) {\n        return config.formatDate(dateObj, frmt, locale);\n    }\n    return frmt\n        .split(\"\")\n        .map((c, i, arr) => formats[c] && arr[i - 1] !== \"\\\\\"\n        ? formats[c](dateObj, locale, config)\n        : c !== \"\\\\\"\n            ? c\n            : \"\")\n        .join(\"\");\n};\nexport const createDateParser = ({ config = defaults, l10n = english }) => (date, givenFormat, timeless, customLocale) => {\n    if (date !== 0 && !date)\n        return undefined;\n    const locale = customLocale || l10n;\n    let parsedDate;\n    const dateOrig = date;\n    if (date instanceof Date)\n        parsedDate = new Date(date.getTime());\n    else if (typeof date !== \"string\" &&\n        date.toFixed !== undefined)\n        parsedDate = new Date(date);\n    else if (typeof date === \"string\") {\n        const format = givenFormat || (config || defaults).dateFormat;\n        const datestr = String(date).trim();\n        if (datestr === \"today\") {\n            parsedDate = new Date();\n            timeless = true;\n        }\n        else if (/Z$/.test(datestr) ||\n            /GMT$/.test(datestr))\n            parsedDate = new Date(date);\n        else if (config && config.parseDate)\n            parsedDate = config.parseDate(date, format);\n        else {\n            parsedDate =\n                !config || !config.noCalendar\n                    ? new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0)\n                    : new Date(new Date().setHours(0, 0, 0, 0));\n            let matched, ops = [];\n            for (let i = 0, matchIndex = 0, regexStr = \"\"; i < format.length; i++) {\n                const token = format[i];\n                const isBackSlash = token === \"\\\\\";\n                const escaped = format[i - 1] === \"\\\\\" || isBackSlash;\n                if (tokenRegex[token] && !escaped) {\n                    regexStr += tokenRegex[token];\n                    const match = new RegExp(regexStr).exec(date);\n                    if (match && (matched = true)) {\n                        ops[token !== \"Y\" ? \"push\" : \"unshift\"]({\n                            fn: revFormat[token],\n                            val: match[++matchIndex],\n                        });\n                    }\n                }\n                else if (!isBackSlash)\n                    regexStr += \".\";\n                ops.forEach(({ fn, val }) => (parsedDate = fn(parsedDate, val, locale) || parsedDate));\n            }\n            parsedDate = matched ? parsedDate : undefined;\n        }\n    }\n    if (!(parsedDate instanceof Date && !isNaN(parsedDate.getTime()))) {\n        config.errorHandler(new Error(`Invalid date provided: ${dateOrig}`));\n        return undefined;\n    }\n    if (timeless === true)\n        parsedDate.setHours(0, 0, 0, 0);\n    return parsedDate;\n};\nexport function compareDates(date1, date2, timeless = true) {\n    if (timeless !== false) {\n        return (new Date(date1.getTime()).setHours(0, 0, 0, 0) -\n            new Date(date2.getTime()).setHours(0, 0, 0, 0));\n    }\n    return date1.getTime() - date2.getTime();\n}\nexport function compareTimes(date1, date2) {\n    return (3600 * (date1.getHours() - date2.getHours()) +\n        60 * (date1.getMinutes() - date2.getMinutes()) +\n        date1.getSeconds() -\n        date2.getSeconds());\n}\nexport const isBetween = (ts, ts1, ts2) => {\n    return ts > Math.min(ts1, ts2) && ts < Math.max(ts1, ts2);\n};\nexport const duration = {\n    DAY: 86400000,\n};\nexport function getDefaultHours(config) {\n    let hours = config.defaultHour;\n    let minutes = config.defaultMinute;\n    let seconds = config.defaultSeconds;\n    if (config.minDate !== undefined) {\n        const minHour = config.minDate.getHours();\n        const minMinutes = config.minDate.getMinutes();\n        const minSeconds = config.minDate.getSeconds();\n        if (hours < minHour) {\n            hours = minHour;\n        }\n        if (hours === minHour && minutes < minMinutes) {\n            minutes = minMinutes;\n        }\n        if (hours === minHour && minutes === minMinutes && seconds < minSeconds)\n            seconds = config.minDate.getSeconds();\n    }\n    if (config.maxDate !== undefined) {\n        const maxHr = config.maxDate.getHours();\n        const maxMinutes = config.maxDate.getMinutes();\n        hours = Math.min(hours, maxHr);\n        if (hours === maxHr)\n            minutes = Math.min(maxMinutes, minutes);\n        if (hours === maxHr && minutes === maxMinutes)\n            seconds = config.maxDate.getSeconds();\n    }\n    return { hours, minutes, seconds };\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,OAAO,QAAS,cAAc;AAC9D,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAO,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,MAAM,GAAGH,QAAQ;EAAEI,IAAI,GAAGH,OAAO;EAAEI,QAAQ,GAAG;AAAO,CAAC,KAAK,CAACC,OAAO,EAAEC,IAAI,EAAEC,cAAc,KAAK;EAChI,MAAMC,MAAM,GAAGD,cAAc,IAAIJ,IAAI;EACrC,IAAID,MAAM,CAACO,UAAU,KAAKC,SAAS,IAAI,CAACN,QAAQ,EAAE;IAC9C,OAAOF,MAAM,CAACO,UAAU,CAACJ,OAAO,EAAEC,IAAI,EAAEE,MAAM,CAAC;EACnD;EACA,OAAOF,IAAI,CACNK,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,GAAG,KAAKjB,OAAO,CAACe,CAAC,CAAC,IAAIE,GAAG,CAACD,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,GACnDhB,OAAO,CAACe,CAAC,CAAC,CAACR,OAAO,EAAEG,MAAM,EAAEN,MAAM,CAAC,GACnCW,CAAC,KAAK,IAAI,GACNA,CAAC,GACD,EAAE,CAAC,CACRG,IAAI,CAAC,EAAE,CAAC;AACjB,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGA,CAAC;EAAEf,MAAM,GAAGH,QAAQ;EAAEI,IAAI,GAAGH;AAAQ,CAAC,KAAK,CAACkB,IAAI,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,YAAY,KAAK;EACtH,IAAIH,IAAI,KAAK,CAAC,IAAI,CAACA,IAAI,EACnB,OAAOR,SAAS;EACpB,MAAMF,MAAM,GAAGa,YAAY,IAAIlB,IAAI;EACnC,IAAImB,UAAU;EACd,MAAMC,QAAQ,GAAGL,IAAI;EACrB,IAAIA,IAAI,YAAYM,IAAI,EACpBF,UAAU,GAAG,IAAIE,IAAI,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,KACrC,IAAI,OAAOP,IAAI,KAAK,QAAQ,IAC7BA,IAAI,CAACQ,OAAO,KAAKhB,SAAS,EAC1BY,UAAU,GAAG,IAAIE,IAAI,CAACN,IAAI,CAAC,CAAC,KAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC/B,MAAMS,MAAM,GAAGR,WAAW,IAAI,CAACjB,MAAM,IAAIH,QAAQ,EAAE6B,UAAU;IAC7D,MAAMC,OAAO,GAAGC,MAAM,CAACZ,IAAI,CAAC,CAACa,IAAI,CAAC,CAAC;IACnC,IAAIF,OAAO,KAAK,OAAO,EAAE;MACrBP,UAAU,GAAG,IAAIE,IAAI,CAAC,CAAC;MACvBJ,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAI,IAAI,CAACY,IAAI,CAACH,OAAO,CAAC,IACvB,MAAM,CAACG,IAAI,CAACH,OAAO,CAAC,EACpBP,UAAU,GAAG,IAAIE,IAAI,CAACN,IAAI,CAAC,CAAC,KAC3B,IAAIhB,MAAM,IAAIA,MAAM,CAAC+B,SAAS,EAC/BX,UAAU,GAAGpB,MAAM,CAAC+B,SAAS,CAACf,IAAI,EAAES,MAAM,CAAC,CAAC,KAC3C;MACDL,UAAU,GACN,CAACpB,MAAM,IAAI,CAACA,MAAM,CAACgC,UAAU,GACvB,IAAIV,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GACpD,IAAIX,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACY,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACnD,IAAIC,OAAO;QAAEC,GAAG,GAAG,EAAE;MACrB,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEyB,UAAU,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAE1B,CAAC,GAAGa,MAAM,CAACc,MAAM,EAAE3B,CAAC,EAAE,EAAE;QACnE,MAAM4B,KAAK,GAAGf,MAAM,CAACb,CAAC,CAAC;QACvB,MAAM6B,WAAW,GAAGD,KAAK,KAAK,IAAI;QAClC,MAAME,OAAO,GAAGjB,MAAM,CAACb,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI6B,WAAW;QACrD,IAAI/C,UAAU,CAAC8C,KAAK,CAAC,IAAI,CAACE,OAAO,EAAE;UAC/BJ,QAAQ,IAAI5C,UAAU,CAAC8C,KAAK,CAAC;UAC7B,MAAMG,KAAK,GAAG,IAAIC,MAAM,CAACN,QAAQ,CAAC,CAACO,IAAI,CAAC7B,IAAI,CAAC;UAC7C,IAAI2B,KAAK,KAAKR,OAAO,GAAG,IAAI,CAAC,EAAE;YAC3BC,GAAG,CAACI,KAAK,KAAK,GAAG,GAAG,MAAM,GAAG,SAAS,CAAC,CAAC;cACpCM,EAAE,EAAEnD,SAAS,CAAC6C,KAAK,CAAC;cACpBO,GAAG,EAAEJ,KAAK,CAAC,EAAEN,UAAU;YAC3B,CAAC,CAAC;UACN;QACJ,CAAC,MACI,IAAI,CAACI,WAAW,EACjBH,QAAQ,IAAI,GAAG;QACnBF,GAAG,CAACY,OAAO,CAAC,CAAC;UAAEF,EAAE;UAAEC;QAAI,CAAC,KAAM3B,UAAU,GAAG0B,EAAE,CAAC1B,UAAU,EAAE2B,GAAG,EAAEzC,MAAM,CAAC,IAAIc,UAAW,CAAC;MAC1F;MACAA,UAAU,GAAGe,OAAO,GAAGf,UAAU,GAAGZ,SAAS;IACjD;EACJ;EACA,IAAI,EAAEY,UAAU,YAAYE,IAAI,IAAI,CAAC2B,KAAK,CAAC7B,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/DvB,MAAM,CAACkD,YAAY,CAAC,IAAIC,KAAK,CAAE,0BAAyB9B,QAAS,EAAC,CAAC,CAAC;IACpE,OAAOb,SAAS;EACpB;EACA,IAAIU,QAAQ,KAAK,IAAI,EACjBE,UAAU,CAACc,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnC,OAAOd,UAAU;AACrB,CAAC;AACD,OAAO,SAASgC,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAEpC,QAAQ,GAAG,IAAI,EAAE;EACxD,IAAIA,QAAQ,KAAK,KAAK,EAAE;IACpB,OAAQ,IAAII,IAAI,CAAC+B,KAAK,CAAC9B,OAAO,CAAC,CAAC,CAAC,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAClD,IAAIZ,IAAI,CAACgC,KAAK,CAAC/B,OAAO,CAAC,CAAC,CAAC,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtD;EACA,OAAOmB,KAAK,CAAC9B,OAAO,CAAC,CAAC,GAAG+B,KAAK,CAAC/B,OAAO,CAAC,CAAC;AAC5C;AACA,OAAO,SAASgC,YAAYA,CAACF,KAAK,EAAEC,KAAK,EAAE;EACvC,OAAQ,IAAI,IAAID,KAAK,CAACG,QAAQ,CAAC,CAAC,GAAGF,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC,GAChD,EAAE,IAAIH,KAAK,CAACI,UAAU,CAAC,CAAC,GAAGH,KAAK,CAACG,UAAU,CAAC,CAAC,CAAC,GAC9CJ,KAAK,CAACK,UAAU,CAAC,CAAC,GAClBJ,KAAK,CAACI,UAAU,CAAC,CAAC;AAC1B;AACA,OAAO,MAAMC,SAAS,GAAGA,CAACC,EAAE,EAAEC,GAAG,EAAEC,GAAG,KAAK;EACvC,OAAOF,EAAE,GAAGG,IAAI,CAACC,GAAG,CAACH,GAAG,EAAEC,GAAG,CAAC,IAAIF,EAAE,GAAGG,IAAI,CAACE,GAAG,CAACJ,GAAG,EAAEC,GAAG,CAAC;AAC7D,CAAC;AACD,OAAO,MAAMI,QAAQ,GAAG;EACpBC,GAAG,EAAE;AACT,CAAC;AACD,OAAO,SAASC,eAAeA,CAACpE,MAAM,EAAE;EACpC,IAAIqE,KAAK,GAAGrE,MAAM,CAACsE,WAAW;EAC9B,IAAIC,OAAO,GAAGvE,MAAM,CAACwE,aAAa;EAClC,IAAIC,OAAO,GAAGzE,MAAM,CAAC0E,cAAc;EACnC,IAAI1E,MAAM,CAAC2E,OAAO,KAAKnE,SAAS,EAAE;IAC9B,MAAMoE,OAAO,GAAG5E,MAAM,CAAC2E,OAAO,CAACnB,QAAQ,CAAC,CAAC;IACzC,MAAMqB,UAAU,GAAG7E,MAAM,CAAC2E,OAAO,CAAClB,UAAU,CAAC,CAAC;IAC9C,MAAMqB,UAAU,GAAG9E,MAAM,CAAC2E,OAAO,CAACjB,UAAU,CAAC,CAAC;IAC9C,IAAIW,KAAK,GAAGO,OAAO,EAAE;MACjBP,KAAK,GAAGO,OAAO;IACnB;IACA,IAAIP,KAAK,KAAKO,OAAO,IAAIL,OAAO,GAAGM,UAAU,EAAE;MAC3CN,OAAO,GAAGM,UAAU;IACxB;IACA,IAAIR,KAAK,KAAKO,OAAO,IAAIL,OAAO,KAAKM,UAAU,IAAIJ,OAAO,GAAGK,UAAU,EACnEL,OAAO,GAAGzE,MAAM,CAAC2E,OAAO,CAACjB,UAAU,CAAC,CAAC;EAC7C;EACA,IAAI1D,MAAM,CAAC+E,OAAO,KAAKvE,SAAS,EAAE;IAC9B,MAAMwE,KAAK,GAAGhF,MAAM,CAAC+E,OAAO,CAACvB,QAAQ,CAAC,CAAC;IACvC,MAAMyB,UAAU,GAAGjF,MAAM,CAAC+E,OAAO,CAACtB,UAAU,CAAC,CAAC;IAC9CY,KAAK,GAAGN,IAAI,CAACC,GAAG,CAACK,KAAK,EAAEW,KAAK,CAAC;IAC9B,IAAIX,KAAK,KAAKW,KAAK,EACfT,OAAO,GAAGR,IAAI,CAACC,GAAG,CAACiB,UAAU,EAAEV,OAAO,CAAC;IAC3C,IAAIF,KAAK,KAAKW,KAAK,IAAIT,OAAO,KAAKU,UAAU,EACzCR,OAAO,GAAGzE,MAAM,CAAC+E,OAAO,CAACrB,UAAU,CAAC,CAAC;EAC7C;EACA,OAAO;IAAEW,KAAK;IAAEE,OAAO;IAAEE;EAAQ,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function catchError(selector) {\n  return function catchErrorOperatorFunction(source) {\n    const operator = new CatchOperator(selector);\n    const caught = source.lift(operator);\n    return operator.caught = caught;\n  };\n}\nclass CatchOperator {\n  constructor(selector) {\n    this.selector = selector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new CatchSubscriber(subscriber, this.selector, this.caught));\n  }\n}\nclass CatchSubscriber extends OuterSubscriber {\n  constructor(destination, selector, caught) {\n    super(destination);\n    this.selector = selector;\n    this.caught = caught;\n  }\n  error(err) {\n    if (!this.isStopped) {\n      let result;\n      try {\n        result = this.selector(err, this.caught);\n      } catch (err2) {\n        super.error(err2);\n        return;\n      }\n      this._unsubscribeAndRecycle();\n      const innerSubscriber = new InnerSubscriber(this, undefined, undefined);\n      this.add(innerSubscriber);\n      const innerSubscription = subscribeToResult(this, result, undefined, undefined, innerSubscriber);\n      if (innerSubscription !== innerSubscriber) {\n        this.add(innerSubscription);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "InnerSubscriber", "subscribeToResult", "catchError", "selector", "catchErrorOperatorFunction", "source", "operator", "CatchOperator", "caught", "lift", "constructor", "call", "subscriber", "subscribe", "CatchSubscriber", "destination", "error", "err", "isStopped", "result", "err2", "_unsubscribeAndRecycle", "innerSubscriber", "undefined", "add", "innerSubscription"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/catchError.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function catchError(selector) {\n    return function catchErrorOperatorFunction(source) {\n        const operator = new CatchOperator(selector);\n        const caught = source.lift(operator);\n        return (operator.caught = caught);\n    };\n}\nclass CatchOperator {\n    constructor(selector) {\n        this.selector = selector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new CatchSubscriber(subscriber, this.selector, this.caught));\n    }\n}\nclass CatchSubscriber extends OuterSubscriber {\n    constructor(destination, selector, caught) {\n        super(destination);\n        this.selector = selector;\n        this.caught = caught;\n    }\n    error(err) {\n        if (!this.isStopped) {\n            let result;\n            try {\n                result = this.selector(err, this.caught);\n            }\n            catch (err2) {\n                super.error(err2);\n                return;\n            }\n            this._unsubscribeAndRecycle();\n            const innerSubscriber = new InnerSubscriber(this, undefined, undefined);\n            this.add(innerSubscriber);\n            const innerSubscription = subscribeToResult(this, result, undefined, undefined, innerSubscriber);\n            if (innerSubscription !== innerSubscriber) {\n                this.add(innerSubscription);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAE;EACjC,OAAO,SAASC,0BAA0BA,CAACC,MAAM,EAAE;IAC/C,MAAMC,QAAQ,GAAG,IAAIC,aAAa,CAACJ,QAAQ,CAAC;IAC5C,MAAMK,MAAM,GAAGH,MAAM,CAACI,IAAI,CAACH,QAAQ,CAAC;IACpC,OAAQA,QAAQ,CAACE,MAAM,GAAGA,MAAM;EACpC,CAAC;AACL;AACA,MAAMD,aAAa,CAAC;EAChBG,WAAWA,CAACP,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAQ,IAAIA,CAACC,UAAU,EAAEP,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACQ,SAAS,CAAC,IAAIC,eAAe,CAACF,UAAU,EAAE,IAAI,CAACT,QAAQ,EAAE,IAAI,CAACK,MAAM,CAAC,CAAC;EACxF;AACJ;AACA,MAAMM,eAAe,SAASf,eAAe,CAAC;EAC1CW,WAAWA,CAACK,WAAW,EAAEZ,QAAQ,EAAEK,MAAM,EAAE;IACvC,KAAK,CAACO,WAAW,CAAC;IAClB,IAAI,CAACZ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACK,MAAM,GAAGA,MAAM;EACxB;EACAQ,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAIC,MAAM;MACV,IAAI;QACAA,MAAM,GAAG,IAAI,CAAChB,QAAQ,CAACc,GAAG,EAAE,IAAI,CAACT,MAAM,CAAC;MAC5C,CAAC,CACD,OAAOY,IAAI,EAAE;QACT,KAAK,CAACJ,KAAK,CAACI,IAAI,CAAC;QACjB;MACJ;MACA,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,MAAMC,eAAe,GAAG,IAAItB,eAAe,CAAC,IAAI,EAAEuB,SAAS,EAAEA,SAAS,CAAC;MACvE,IAAI,CAACC,GAAG,CAACF,eAAe,CAAC;MACzB,MAAMG,iBAAiB,GAAGxB,iBAAiB,CAAC,IAAI,EAAEkB,MAAM,EAAEI,SAAS,EAAEA,SAAS,EAAED,eAAe,CAAC;MAChG,IAAIG,iBAAiB,KAAKH,eAAe,EAAE;QACvC,IAAI,CAACE,GAAG,CAACC,iBAAiB,CAAC;MAC/B;IACJ;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
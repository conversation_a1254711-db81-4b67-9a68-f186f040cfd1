{"ast": null, "code": "import { isArray } from './isArray';\nexport function isNumeric(val) {\n  return !isArray(val) && val - parseFloat(val) + 1 >= 0;\n}", "map": {"version": 3, "names": ["isArray", "isNumeric", "val", "parseFloat"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isNumeric.js"], "sourcesContent": ["import { isArray } from './isArray';\nexport function isNumeric(val) {\n    return !isArray(val) && (val - parseFloat(val) + 1) >= 0;\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,OAAO,SAASC,SAASA,CAACC,GAAG,EAAE;EAC3B,OAAO,CAACF,OAAO,CAACE,GAAG,CAAC,IAAKA,GAAG,GAAGC,UAAU,CAACD,GAAG,CAAC,GAAG,CAAC,IAAK,CAAC;AAC5D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport { environment } from 'environments/environment';\nexport class CustomUploadAdapter {\n  constructor(loader, _s3Service, _translateService) {\n    this._s3Service = _s3Service;\n    this._translateService = _translateService;\n    this.maxSize = AppConfig.MAX_UPLOAD_SIZE * 1024 * 1024;\n    this.loader = loader;\n  }\n  upload() {\n    return this.loader.file.then(file => {\n      return new Promise((resolve, reject) => {\n        this._s3Service.getMaxUploadSize().subscribe(response => {\n          const maxUploadSize = response['maxUploadSizeInBytes'];\n          if (file.size > maxUploadSize) {\n            Swal.fire({\n              icon: 'error',\n              title: this._translateService.instant('Error'),\n              text: this._translateService.instant(`File too large! Maximum {{maxUploadSize}}MB allowed`, {\n                maxUploadSize: response['maxUploadSizeInMB']\n              })\n            });\n            reject();\n          } else {\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = () => {\n              // resolve({\n              //   default: reader.result as string,\n              // });\n            };\n            reader.onerror = error => reject(error);\n            const formData = new FormData();\n            formData.append('upload', file);\n            formData.append('dir', 'ckeditor');\n            this._s3Service.uploadImage(formData).subscribe({\n              next: response => {\n                resolve({\n                  default: `${environment.apiUrl}/s3?key=${response.files.filename}`\n                });\n              },\n              error: err => {\n                const uploadError = err.fieldErrors?.find(fieldError => fieldError.name === 'upload') ?? null;\n                console.log('uploadError', uploadError);\n                if (uploadError) {\n                  Swal.fire({\n                    icon: 'error',\n                    title: this._translateService.instant('Warning'),\n                    text: this._translateService.instant(uploadError.status, {\n                      maxUploadSize: uploadError.maxUploadSize\n                    })\n                  });\n                }\n                if (err.errors.hasOwnProperty('upload')) {\n                  Swal.fire({\n                    icon: 'error',\n                    title: this._translateService.instant('Error'),\n                    text: this._translateService.instant(`Have some problem `, {\n                      maxUploadSize: AppConfig.MAX_UPLOAD_SIZE\n                    })\n                  });\n                }\n                reject();\n              }\n            });\n          }\n        });\n      });\n    });\n  }\n  abort() {}\n  removeImageFromEditor(imageUrl) {\n    document.querySelectorAll('img').forEach(img => {\n      if (img.src === imageUrl) {\n        img.remove();\n      }\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAE1C,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,OAAM,MAAOC,mBAAmB;EAI9BC,YACEC,MAAW,EACHC,UAAqB,EACrBC,iBAAmC;IADnC,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAL3B,KAAAC,OAAO,GAAWR,SAAS,CAACS,eAAe,GAAG,IAAI,GAAG,IAAI;IAOvD,IAAI,CAACJ,MAAM,GAAGA,MAAM;EACtB;EAEAK,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACL,MAAM,CAACM,IAAI,CAACC,IAAI,CAAED,IAAU,IAAI;MAC1C,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QAErC,IAAI,CAACT,UAAU,CAACU,gBAAgB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;UACxD,MAAMC,aAAa,GAAGD,QAAQ,CAAC,sBAAsB,CAAC;UAEtD,IAAIP,IAAI,CAACS,IAAI,GAAGD,aAAa,EAAE;YAC7BlB,IAAI,CAACoB,IAAI,CAAC;cACRC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACiB,OAAO,CAAC,OAAO,CAAC;cAC9CC,IAAI,EAAE,IAAI,CAAClB,iBAAiB,CAACiB,OAAO,CAClC,qDAAqD,EACrD;gBAAEL,aAAa,EAAED,QAAQ,CAAC,mBAAmB;cAAC,CAAE;aAEnD,CAAC;YACFH,MAAM,EAAE;WACT,MAAM;YACL,MAAMW,MAAM,GAAG,IAAIC,UAAU,EAAE;YAC/BD,MAAM,CAACE,aAAa,CAACjB,IAAI,CAAC;YAC1Be,MAAM,CAACG,MAAM,GAAG,MAAK;cACnB;cACA;cACA;YAAA,CACD;YACDH,MAAM,CAACI,OAAO,GAAIC,KAAK,IAAKhB,MAAM,CAACgB,KAAK,CAAC;YAEzC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;YAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEvB,IAAI,CAAC;YAC/BqB,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC;YAElC,IAAI,CAAC5B,UAAU,CAAC6B,WAAW,CAACH,QAAQ,CAAC,CAACf,SAAS,CAAC;cAC9CmB,IAAI,EAAGlB,QAAa,IAAI;gBACtBJ,OAAO,CAAC;kBACNuB,OAAO,EAAE,GAAGnC,WAAW,CAACoC,MAAM,WAAWpB,QAAQ,CAACqB,KAAK,CAACC,QAAQ;iBACjE,CAAC;cACJ,CAAC;cACDT,KAAK,EAAGU,GAAG,IAAI;gBAEb,MAAMC,WAAW,GAAGD,GAAG,CAACE,WAAW,EAAEC,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACC,IAAI,KAAK,QAAQ,CAAC,IAAI,IAAI;gBAE7FC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEN,WAAW,CAAC;gBAEvC,IAAIA,WAAW,EAAE;kBACfzC,IAAI,CAACoB,IAAI,CAAC;oBACRC,IAAI,EAAE,OAAO;oBACbC,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACiB,OAAO,CAAC,SAAS,CAAC;oBAChDC,IAAI,EAAE,IAAI,CAAClB,iBAAiB,CAACiB,OAAO,CAACkB,WAAW,CAACO,MAAM,EAAE;sBACvD9B,aAAa,EAAEuB,WAAW,CAACvB;qBAC5B;mBACF,CAAC;;gBAGJ,IAAIsB,GAAG,CAACS,MAAM,CAACC,cAAc,CAAC,QAAQ,CAAC,EAAE;kBACvClD,IAAI,CAACoB,IAAI,CAAC;oBACRC,IAAI,EAAE,OAAO;oBACbC,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACiB,OAAO,CAAC,OAAO,CAAC;oBAC9CC,IAAI,EAAE,IAAI,CAAClB,iBAAiB,CAACiB,OAAO,CAClC,oBAAoB,EACpB;sBAAEL,aAAa,EAAEnB,SAAS,CAACS;oBAAe,CAAE;mBAE/C,CAAC;;gBAGJM,MAAM,EAAE;cACV;aACD,CAAC;;QAEN,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAqC,KAAKA,CAAA,GACL;EAEQC,qBAAqBA,CAACC,QAAgB;IAC5CC,QAAQ,CAACC,gBAAgB,CAAC,KAAK,CAAC,CAACC,OAAO,CAAEC,GAAqB,IAAI;MACjE,IAAIA,GAAG,CAACC,GAAG,KAAKL,QAAQ,EAAE;QACxBI,GAAG,CAACE,MAAM,EAAE;;IAEhB,CAAC,CAAC;EACJ", "names": ["AppConfig", "<PERSON><PERSON>", "environment", "CustomUploadAdapter", "constructor", "loader", "_s3Service", "_translateService", "maxSize", "MAX_UPLOAD_SIZE", "upload", "file", "then", "Promise", "resolve", "reject", "getMaxUploadSize", "subscribe", "response", "maxUploadSize", "size", "fire", "icon", "title", "instant", "text", "reader", "FileReader", "readAsDataURL", "onload", "onerror", "error", "formData", "FormData", "append", "uploadImage", "next", "default", "apiUrl", "files", "filename", "err", "uploadError", "fieldErrors", "find", "fieldError", "name", "console", "log", "status", "errors", "hasOwnProperty", "abort", "removeImageFromEditor", "imageUrl", "document", "querySelectorAll", "for<PERSON>ach", "img", "src", "remove"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\ckeditor5-type\\custom-upload-adapter.ts"], "sourcesContent": ["import { AppConfig } from 'app/app-config';\r\nimport { SendMessagesService } from 'app/services/send-messages.service';\r\nimport Swal from 'sweetalert2';\r\nimport { title } from 'process';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { S3Service } from 'app/services/s3.service';\r\nimport { environment } from 'environments/environment';\r\n\r\nexport class CustomUploadAdapter {\r\n  loader: any;\r\n  maxSize: number = AppConfig.MAX_UPLOAD_SIZE * 1024 * 1024;\r\n\r\n  constructor(\r\n    loader: any,\r\n    private _s3Service: S3Service,\r\n    private _translateService: TranslateService\r\n  ) {\r\n    this.loader = loader;\r\n  }\r\n\r\n  upload() {\r\n    return this.loader.file.then((file: File) => {\r\n      return new Promise((resolve, reject) => {\r\n\r\n        this._s3Service.getMaxUploadSize().subscribe((response) => {\r\n          const maxUploadSize = response['maxUploadSizeInBytes'];\r\n\r\n          if (file.size > maxUploadSize) {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: this._translateService.instant('Error'),\r\n              text: this._translateService.instant(\r\n                `File too large! Maximum {{maxUploadSize}}MB allowed`,\r\n                { maxUploadSize: response['maxUploadSizeInMB'] }\r\n              )\r\n            });\r\n            reject();\r\n          } else {\r\n            const reader = new FileReader();\r\n            reader.readAsDataURL(file);\r\n            reader.onload = () => {\r\n              // resolve({\r\n              //   default: reader.result as string,\r\n              // });\r\n            };\r\n            reader.onerror = (error) => reject(error);\r\n\r\n            const formData = new FormData();\r\n            formData.append('upload', file);\r\n            formData.append('dir', 'ckeditor');\r\n\r\n            this._s3Service.uploadImage(formData).subscribe({\r\n              next: (response: any) => {\r\n                resolve({\r\n                  default: `${environment.apiUrl}/s3?key=${response.files.filename}`\r\n                });\r\n              },\r\n              error: (err) => {\r\n\r\n                const uploadError = err.fieldErrors?.find(fieldError => fieldError.name === 'upload') ?? null;\r\n\r\n                console.log('uploadError', uploadError);\r\n\r\n                if (uploadError) {\r\n                  Swal.fire({\r\n                    icon: 'error',\r\n                    title: this._translateService.instant('Warning'),\r\n                    text: this._translateService.instant(uploadError.status, {\r\n                      maxUploadSize: uploadError.maxUploadSize\r\n                    })\r\n                  });\r\n                }\r\n\r\n                if (err.errors.hasOwnProperty('upload')) {\r\n                  Swal.fire({\r\n                    icon: 'error',\r\n                    title: this._translateService.instant('Error'),\r\n                    text: this._translateService.instant(\r\n                      `Have some problem `,\r\n                      { maxUploadSize: AppConfig.MAX_UPLOAD_SIZE }\r\n                    )\r\n                  });\r\n                }\r\n\r\n                reject();\r\n              }\r\n            });\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  abort() {\r\n  }\r\n\r\n  private removeImageFromEditor(imageUrl: string) {\r\n    document.querySelectorAll('img').forEach((img: HTMLImageElement) => {\r\n      if (img.src === imageUrl) {\r\n        img.remove();\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/settings.service\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"ngx-toastr\";\nexport class TermConditionComponent {\n  constructor(settingsService, _modalActive, toastrService) {\n    this.settingsService = settingsService;\n    this._modalActive = _modalActive;\n    this.toastrService = toastrService;\n    this.parentalConsent = false;\n    this.allowCheck = false;\n  }\n  ngOnInit() {\n    console.log(this.title);\n    console.log(this.content);\n    let contentEl = document.getElementById('content');\n    contentEl.innerHTML = this.content;\n    this.handleScrollToBottom();\n  }\n  onParentalConsentChange(event) {\n    if (this.allowCheck == false) {\n      const modal_body = document.querySelector('.modal-body');\n      // click to modal body\n      const position = {\n        top: modal_body.scrollTop,\n        bottom: modal_body.scrollTop + modal_body.offsetHeight,\n        left: modal_body.scrollLeft,\n        right: modal_body.scrollLeft + modal_body.offsetWidth\n      };\n      if (position.bottom >= modal_body.scrollHeight) {\n        this.allowCheck = true;\n      } else {\n        this.allowCheck = false;\n        event.target.checked = false;\n        this.toastrService.error(\"Please scroll to the bottom of the page to accept the terms of service and privacy policy\");\n        return;\n      }\n    }\n    this.parentalConsent = event.target.checked;\n    console.log(this.parentalConsent, \"---\");\n  }\n  postAssignment(user_id) {\n    return this.settingsService.postAssignPolicy(user_id);\n  }\n  onClose() {\n    if (this.type == 'register') {\n      return this._modalActive.close(this.parentalConsent);\n    }\n    this.postAssignment(this.user.id).subscribe(res => {\n      this._modalActive.close(res);\n    });\n    console.log('close');\n  }\n  handleScrollToBottom() {\n    const modal_body = document.querySelector('.modal-body');\n    const overflow = modal_body.style;\n    console.log(overflow);\n    modal_body.addEventListener('scroll', () => {\n      // Check if the bottom of the element is reached\n      const position = {\n        top: modal_body.scrollTop,\n        bottom: modal_body.scrollTop + modal_body.offsetHeight,\n        left: modal_body.scrollLeft,\n        right: modal_body.scrollLeft + modal_body.offsetWidth,\n        scrollHeight: modal_body.scrollHeight\n      };\n      if (position.bottom / position.scrollHeight >= 0.9) {\n        console.log('bottom reached');\n        this.allowCheck = true;\n      } else {\n        this.allowCheck = false;\n      }\n    });\n  }\n  static #_ = this.ɵfac = function TermConditionComponent_Factory(t) {\n    return new (t || TermConditionComponent)(i0.ɵɵdirectiveInject(i1.SettingsService), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TermConditionComponent,\n    selectors: [[\"term-condition\"]],\n    inputs: {\n      user: \"user\",\n      type: \"type\",\n      title: \"title\",\n      content: \"content\"\n    },\n    decls: 19,\n    vars: 7,\n    consts: [[1, \"modal-content\"], [1, \"modal-header\"], [1, \"text-center\", \"w-100\"], [1, \"text-capitalize\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [\"id\", \"content\", 1, \"ck-content\"], [1, \"modal-footer\", \"justify-content-between\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", \"id\", \"customCheck1\", 1, \"custom-control-input\", 3, \"checked\", \"disabled\", \"change\"], [\"for\", \"customCheck1\", 1, \"custom-control-label\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"]],\n    template: function TermConditionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\")(4, \"span\", 3);\n        i0.ɵɵtext(5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"h5\")(7, \"span\");\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(9, \"div\", 4);\n        i0.ɵɵelement(10, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"input\", 8);\n        i0.ɵɵlistener(\"change\", function TermConditionComponent_Template_input_change_13_listener($event) {\n          return ctx.onParentalConsentChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"label\", 9);\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function TermConditionComponent_Template_button_click_16_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(17, \"span\", 3);\n        i0.ɵɵtext(18);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(\"Terms of Service and Privacy Policy\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.title);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"checked\", ctx.parentalConsent);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate2(\" \", \"I agree to the\", \" \", \"Terms of Service and Privacy Policy\", \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", !ctx.parentalConsent);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(\"OK\");\n      }\n    },\n    styles: [\":host {\\n  display: block;\\n}\\n\\n.modal-dialog-scrollable .modal-content {\\n  max-height: calc(100vh - 3rem) !important;\\n  overflow: hidden;\\n}\\n\\nspan#term_img img {\\n  width: 100%;\\n}\\n\\nspan#term_img {\\n  height: 50vh;\\n  overflow: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy90ZXJtLWNvbmRpdGlvbi90ZXJtLWNvbmRpdGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQUE7QUFDRjs7QUFFQTtFQUNFLHlDQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7QUFDRjs7QUFFQTtFQUNFLFlBQUE7RUFDQSxjQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuXHJcbi5tb2RhbC1kaWFsb2ctc2Nyb2xsYWJsZSAubW9kYWwtY29udGVudCB7XHJcbiAgbWF4LWhlaWdodDogY2FsYygxMDB2aCAtIDNyZW0pICFpbXBvcnRhbnQ7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuc3BhbiN0ZXJtX2ltZyBpbWcge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG5zcGFuI3Rlcm1faW1nIHtcclxuICBoZWlnaHQ6IDUwdmg7XHJcbiAgb3ZlcmZsb3c6IGF1dG87XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "mappings": ";;;;AAwBA,OAAM,MAAOA,sBAAsB;EAS/BC,YACWC,eAAgC,EAC/BC,YAA4B,EAC5BC,aAA4B;IAF7B,KAAAF,eAAe,GAAfA,eAAe;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IAPzB,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,UAAU,GAAY,KAAK;EAS3B;EACAC,QAAQA,CAAA;IACJC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;IACvBF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACE,OAAO,CAAC;IACzB,IAAIC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;IAClDF,SAAS,CAACG,SAAS,GAAG,IAAI,CAACJ,OAAO;IAClC,IAAI,CAACK,oBAAoB,EAAE;EAC/B;EAEAC,uBAAuBA,CAACC,KAAU;IAC9B,IAAG,IAAI,CAACZ,UAAU,IAAI,KAAK,EAAE;MACzB,MAAMa,UAAU,GAAGN,QAAQ,CAACO,aAAa,CAAC,aAAa,CAAgB;MAEvE;MAKA,MAAMC,QAAQ,GAAG;QACbC,GAAG,EAAEH,UAAU,CAACI,SAAS;QACzBC,MAAM,EAAEL,UAAU,CAACI,SAAS,GAAGJ,UAAU,CAACM,YAAY;QACtDC,IAAI,EAAEP,UAAU,CAACQ,UAAU;QAC3BC,KAAK,EAAET,UAAU,CAACQ,UAAU,GAAGR,UAAU,CAACU;OAC7C;MAED,IAAIR,QAAQ,CAACG,MAAM,IAAIL,UAAU,CAACW,YAAY,EAAE;QAC5C,IAAI,CAACxB,UAAU,GAAG,IAAI;OACzB,MAAI;QACD,IAAI,CAACA,UAAU,GAAG,KAAK;QACvBY,KAAK,CAACa,MAAM,CAACC,OAAO,GAAG,KAAK;QAC5B,IAAI,CAAC5B,aAAa,CAAC6B,KAAK,CAAC,2FAA2F,CAAC;QACrH;;;IAKR,IAAI,CAAC5B,eAAe,GAAGa,KAAK,CAACa,MAAM,CAACC,OAAO;IAC3CxB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACJ,eAAe,EAAE,KAAK,CAAC;EAC5C;EAEA6B,cAAcA,CAACC,OAAO;IAClB,OAAO,IAAI,CAACjC,eAAe,CAACkC,gBAAgB,CAACD,OAAO,CAAC;EACzD;EACAE,OAAOA,CAAA;IACH,IAAI,IAAI,CAACC,IAAI,IAAI,UAAU,EAAE;MACzB,OAAO,IAAI,CAACnC,YAAY,CAACoC,KAAK,CAAC,IAAI,CAAClC,eAAe,CAAC;;IAExD,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAACM,IAAI,CAACC,EAAE,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAI;MAChD,IAAI,CAACxC,YAAY,CAACoC,KAAK,CAACI,GAAG,CAAC;IAChC,CAAC,CAAC;IACFnC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;EACxB;EAEAO,oBAAoBA,CAAA;IAChB,MAAMG,UAAU,GAAGN,QAAQ,CAACO,aAAa,CAAC,aAAa,CAAgB;IAEvE,MAAMwB,QAAQ,GAAGzB,UAAU,CAAC0B,KAAK;IAEjCrC,OAAO,CAACC,GAAG,CAACmC,QAAQ,CAAC;IAErBzB,UAAU,CAAC2B,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MAEvC;MACA,MAAMzB,QAAQ,GAAG;QACbC,GAAG,EAAEH,UAAU,CAACI,SAAS;QACzBC,MAAM,EAAEL,UAAU,CAACI,SAAS,GAAGJ,UAAU,CAACM,YAAY;QACtDC,IAAI,EAAEP,UAAU,CAACQ,UAAU;QAC3BC,KAAK,EAAET,UAAU,CAACQ,UAAU,GAAGR,UAAU,CAACU,WAAW;QACrDC,YAAY,EAAEX,UAAU,CAACW;OAC5B;MAED,IAAKT,QAAQ,CAACG,MAAM,GAAGH,QAAQ,CAACS,YAAY,IAAK,GAAG,EAAE;QAClDtB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC7B,IAAI,CAACH,UAAU,GAAG,IAAI;OACzB,MAAM;QACH,IAAI,CAACA,UAAU,GAAG,KAAK;;IAE/B,CAAC,CAAC;EACN;EAAC,QAAAyC,CAAA;qBA7FQ/C,sBAAsB,EAAAgD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBxD,sBAAsB;IAAAyD,SAAA;IAAAC,MAAA;MAAAlB,IAAA;MAAAF,IAAA;MAAA5B,KAAA;MAAAC,OAAA;IAAA;IAAAgD,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCxBnChB,EAAA,CAAAkB,cAAA,aAA2B;QASalB,EAAA,CAAAmB,MAAA,GAAyC;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAEhFpB,EAAA,CAAAkB,cAAA,SAAI;QACOlB,EAAA,CAAAmB,MAAA,GAAS;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAI/BpB,EAAA,CAAAkB,cAAA,aAAkD;QAC9ClB,EAAA,CAAAqB,SAAA,cAA4C;QAEhDrB,EAAA,CAAAoB,YAAA,EAAM;QAGNpB,EAAA,CAAAkB,cAAA,cAAkD;QAE4BlB,EAAA,CAAAsB,UAAA,oBAAAC,yDAAAC,MAAA;UAAA,OAAUP,GAAA,CAAAhD,uBAAA,CAAAuD,MAAA,CAA+B;QAAA,EAAC;QAAhHxB,EAAA,CAAAoB,YAAA,EAA0J;QAC1JpB,EAAA,CAAAkB,cAAA,gBAAuD;QACnDlB,EAAA,CAAAmB,MAAA,IAEJ;QAAAnB,EAAA,CAAAoB,YAAA,EAAQ;QAGdpB,EAAA,CAAAkB,cAAA,kBAAgG;QAApBlB,EAAA,CAAAsB,UAAA,mBAAAG,yDAAA;UAAA,OAASR,GAAA,CAAA5B,OAAA,EAAS;QAAA,EAAC;QAC7FW,EAAA,CAAAkB,cAAA,eAA8B;QAAAlB,EAAA,CAAAmB,MAAA,IAAW;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;;;QAvBhBpB,EAAA,CAAA0B,SAAA,GAAyC;QAAzC1B,EAAA,CAAA2B,iBAAA,uCAAyC;QAG9D3B,EAAA,CAAA0B,SAAA,GAAS;QAAT1B,EAAA,CAAA2B,iBAAA,CAAAV,GAAA,CAAAvD,KAAA,CAAS;QAYiGsC,EAAA,CAAA0B,SAAA,GAA2B;QAA3B1B,EAAA,CAAA4B,UAAA,YAAAX,GAAA,CAAA5D,eAAA,CAA2B;QAExI2C,EAAA,CAAA0B,SAAA,GAEJ;QAFI1B,EAAA,CAAA6B,kBAAA,wEAEJ;QAGgB7B,EAAA,CAAA0B,SAAA,GAA6B;QAA7B1B,EAAA,CAAA4B,UAAA,cAAAX,GAAA,CAAA5D,eAAA,CAA6B;QACnB2C,EAAA,CAAA0B,SAAA,GAAW;QAAX1B,EAAA,CAAA2B,iBAAA,MAAW", "names": ["TermConditionComponent", "constructor", "settingsService", "_modalActive", "toastrService", "parentalConsent", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "console", "log", "title", "content", "contentEl", "document", "getElementById", "innerHTML", "handleScrollToBottom", "onParentalConsentChange", "event", "modal_body", "querySelector", "position", "top", "scrollTop", "bottom", "offsetHeight", "left", "scrollLeft", "right", "offsetWidth", "scrollHeight", "target", "checked", "error", "postAssignment", "user_id", "postAssignPolicy", "onClose", "type", "close", "user", "id", "subscribe", "res", "overflow", "style", "addEventListener", "_", "i0", "ɵɵdirectiveInject", "i1", "SettingsService", "i2", "NgbActiveModal", "i3", "ToastrService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "TermConditionComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "TermConditionComponent_Template_input_change_13_listener", "$event", "TermConditionComponent_Template_button_click_16_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵtextInterpolate2"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\term-condition\\term-condition.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\term-condition\\term-condition.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { ChangeDetectionStrategy, Component, Directive, ElementRef, HostListener, Input, Renderer2 } from '@angular/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { SettingsService } from 'app/services/settings.service';\r\nimport { ViewEncapsulation } from '@angular/core'\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { title } from 'process';\r\nimport { ToastrService } from 'ngx-toastr';\r\n\r\n// interface policy_notification \r\n\r\nexport interface IPolicyNotification {\r\n    title: string;\r\n    content: string;\r\n    is_on: boolean;\r\n}\r\n\r\n@Component({\r\n    selector: 'term-condition',\r\n    templateUrl: './term-condition.component.html',\r\n    styleUrls: ['./term-condition.component.scss'],\r\n    changeDetection: ChangeDetectionStrategy.OnPush,\r\n    encapsulation: ViewEncapsulation.None\r\n})\r\nexport class TermConditionComponent {\r\n    @Input() user: any;\r\n    @Input() type: any;\r\n    @Input() title: any;\r\n    @Input() content: any;\r\n    parentalConsent: boolean = false;\r\n    allowCheck: boolean = false;\r\n    policy_notification_modal: IPolicyNotification;\r\n\r\n    constructor(\r\n        public settingsService: SettingsService,\r\n        private _modalActive: NgbActiveModal,\r\n        private toastrService: ToastrService\r\n    ) {\r\n\r\n    }\r\n    ngOnInit() {\r\n        console.log(this.title);\r\n        console.log(this.content);\r\n        let contentEl = document.getElementById('content');\r\n        contentEl.innerHTML = this.content;\r\n        this.handleScrollToBottom();\r\n    }\r\n\r\n    onParentalConsentChange(event: any): void {\r\n        if(this.allowCheck == false) {\r\n            const modal_body = document.querySelector('.modal-body') as HTMLElement;\r\n\r\n            // click to modal body\r\n\r\n        \r\n\r\n\r\n            const position = {\r\n                top: modal_body.scrollTop,\r\n                bottom: modal_body.scrollTop + modal_body.offsetHeight,\r\n                left: modal_body.scrollLeft,\r\n                right: modal_body.scrollLeft + modal_body.offsetWidth,\r\n            };\r\n\r\n            if (position.bottom >= modal_body.scrollHeight) {\r\n                this.allowCheck = true;\r\n            }else{\r\n                this.allowCheck = false;\r\n                event.target.checked = false;\r\n                this.toastrService.error(\"Please scroll to the bottom of the page to accept the terms of service and privacy policy\");\r\n                return;\r\n            }\r\n\r\n           \r\n        }\r\n        this.parentalConsent = event.target.checked;\r\n        console.log(this.parentalConsent, \"---\")\r\n    }\r\n\r\n    postAssignment(user_id) {\r\n        return this.settingsService.postAssignPolicy(user_id);\r\n    }\r\n    onClose() {\r\n        if (this.type == 'register') {\r\n            return this._modalActive.close(this.parentalConsent);\r\n        }\r\n        this.postAssignment(this.user.id).subscribe((res) => {\r\n            this._modalActive.close(res);\r\n        });\r\n        console.log('close');\r\n    }\r\n\r\n    handleScrollToBottom() {\r\n        const modal_body = document.querySelector('.modal-body') as HTMLElement;\r\n    \r\n        const overflow = modal_body.style;\r\n\r\n        console.log(overflow);\r\n        \r\n        modal_body.addEventListener('scroll', () => {\r\n            \r\n            // Check if the bottom of the element is reached\r\n            const position = {\r\n                top: modal_body.scrollTop,\r\n                bottom: modal_body.scrollTop + modal_body.offsetHeight,\r\n                left: modal_body.scrollLeft,\r\n                right: modal_body.scrollLeft + modal_body.offsetWidth,\r\n                scrollHeight: modal_body.scrollHeight,\r\n            };\r\n    \r\n            if ((position.bottom / position.scrollHeight) >= 0.9) {\r\n                console.log('bottom reached');\r\n                this.allowCheck = true;\r\n            } else {\r\n                this.allowCheck = false;\r\n            }\r\n        });\r\n    }\r\n    \r\n}\r\n", "<div class=\"modal-content\">\r\n    <div class=\"modal-header\">\r\n      <!-- close modal -->\r\n      \r\n      <!-- text center title and subtitle -->\r\n      <!-- title is 'Team List 'H2 -->\r\n      <!-- subtitle is 'Assign player to team' h5-->\r\n      <div class=\"text-center w-100\">\r\n        <h2>\r\n          <span class=\"text-capitalize\">{{'Terms of Service and Privacy Policy'}}</span>\r\n        </h2>\r\n        <h5>\r\n            <span >{{title}}</span>\r\n          </h5>\r\n      </div>\r\n    </div>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n        <div class=\"ck-content \" id=\"content\"></div>\r\n        <!-- <p>dđ<img src=\"http://localhost:8000/storage/uploads/ckeditor/Background Complete_1714613511.png\"></p> -->\r\n    </div>\r\n  \r\n    <!-- button accept -->\r\n    <div class=\"modal-footer justify-content-between\">\r\n        <div class=\"custom-control custom-checkbox\">\r\n            <input type=\"checkbox\" class=\"custom-control-input\" id=\"customCheck1\" (change)=\"onParentalConsentChange($event)\" [checked]=\"parentalConsent\" [disabled] />\r\n            <label class=\"custom-control-label\" for=\"customCheck1\">\r\n                {{'I agree to the'}} \r\n           {{'Terms of Service and Privacy Policy'}}\r\n            </label>\r\n            <!-- you must read all the terms of service and privacy policy -->\r\n          </div>\r\n      <button type=\"button\" [disabled]=\"!parentalConsent\" class=\"btn btn-primary\" (click)=\"onClose()\">\r\n        <span class=\"text-capitalize\">{{ 'OK'  }}</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n  "]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"app/services/registration.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ngx-formly/core\";\nexport class ModalMessageReminderComponent {\n  constructor(activeModal, registrationService, _translateService) {\n    this.activeModal = activeModal;\n    this.registrationService = registrationService;\n    this._translateService = _translateService;\n    this.form = new FormGroup({});\n    this.model = {\n      title: \"Player registration is incomplete\",\n      message: ''\n    };\n    this.fields = [{\n      key: 'title',\n      type: 'input',\n      props: {\n        type: 'text',\n        label: this._translateService.instant('Title'),\n        required: true\n      }\n    }, {\n      key: 'content',\n      type: 'ckeditor5',\n      props: {\n        required: true,\n        label: this._translateService.instant('Content'),\n        config: {\n          simpleUpload: {\n            // The URL that the images are uploaded to.\n            uploadUrl: `${environment.apiUrl}/clubs/editor`\n          },\n          placeholder: this._translateService.instant('Type the content here'),\n          htmlSupport: {\n            allow: [{\n              name: /.*/,\n              attributes: true,\n              classes: true,\n              styles: true\n            }]\n          },\n          htmlEmbed: {\n            showPreviews: true\n          },\n          mention: {\n            feeds: [{\n              marker: '{',\n              feed: this.getFeedItems,\n              minimumCharacters: 1\n            }]\n          }\n        }\n      },\n      defaultValue: `<p><strong>Dear parent,</strong></p>\n      <p>Please note that the registration for player registration is currently incomplete and pending the successful submission of the following information:</p>\n      <ul>\n        <li>Please go to Registration in the app and resubmit the information.</li>\n      </ul>`\n    }];\n    this.options = {};\n  }\n  getFeedItems(queryText) {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        const itemsToDisplay = ['{{user_first_name}}', '{{user_last_name}}', '{{user_email}}'].filter(isItemMatching).slice(0, 10);\n        resolve(itemsToDisplay);\n      }, 100);\n    });\n    function isItemMatching(item) {\n      const searchString = queryText.toLowerCase();\n      return item.toLowerCase().includes(searchString);\n      // ||        item.id.toLowerCase().includes(searchString)\n    }\n  }\n\n  ngOnInit() {}\n  onSubmit() {\n    if (this.form.valid) {\n      // Perform your submit logic here\n      this.activeModal.close(this.model);\n    }\n  }\n  dismissModal() {\n    this.activeModal.dismiss('Close click');\n  }\n  static #_ = this.ɵfac = function ModalMessageReminderComponent_Factory(t) {\n    return new (t || ModalMessageReminderComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.RegistrationService), i0.ɵɵdirectiveInject(i3.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalMessageReminderComponent,\n    selectors: [[\"app-modal-message-reminder\"]],\n    inputs: {\n      registrations: \"registrations\"\n    },\n    decls: 19,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [3, \"formGroup\", \"ngSubmit\"], [3, \"form\", \"fields\", \"model\", \"options\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function ModalMessageReminderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelement(4, \"br\");\n        i0.ɵɵelementStart(5, \"small\");\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalMessageReminderComponent_Template_button_click_8_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵelementStart(9, \"span\", 3);\n        i0.ɵɵtext(10, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalMessageReminderComponent_Template_form_ngSubmit_12_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelement(13, \"formly-form\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 7)(15, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function ModalMessageReminderComponent_Template_button_click_15_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵtext(16, \"Close\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ModalMessageReminderComponent_Template_button_click_17_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtext(18, \"Submit\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 7, \"Message Reminder\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 9, \"Please only use for awaiting update registration\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model)(\"options\", ctx.options);\n      }\n    },\n    dependencies: [i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, i5.FormlyForm, i3.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;AAM1C,SAASC,WAAW,QAAQ,0BAA0B;;;;;;;AAMtD,OAAM,MAAOC,6BAA6B;EA+DxCC,YACUC,WAA2B,EAC3BC,mBAAwC,EACxCC,iBAAmC;IAFnC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAhE3B,KAAAC,IAAI,GAAG,IAAIP,SAAS,CAAC,EAAE,CAAC;IAExB,KAAAQ,KAAK,GAAG;MACNC,KAAK,EAAE,mCAAmC;MAC1CC,OAAO,EAAE;KAEV;IACD,KAAAC,MAAM,GAAwB,CAC5B;MACEC,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,MAAM;QACZE,KAAK,EAAE,IAAI,CAACT,iBAAiB,CAACU,OAAO,CAAC,OAAO,CAAC;QAC9CC,QAAQ,EAAE;;KAEb,EACD;MACEL,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLG,QAAQ,EAAE,IAAI;QACdF,KAAK,EAAE,IAAI,CAACT,iBAAiB,CAACU,OAAO,CAAC,SAAS,CAAC;QAChDE,MAAM,EAAE;UACNC,YAAY,EAAE;YACZ;YACAC,SAAS,EAAE,GAAGnB,WAAW,CAACoB,MAAM;WACjC;UACDC,WAAW,EAAE,IAAI,CAAChB,iBAAiB,CAACU,OAAO,CAAC,uBAAuB,CAAC;UACpEO,WAAW,EAAE;YACXC,KAAK,EAAE,CACL;cACEC,IAAI,EAAE,IAAI;cACVC,UAAU,EAAE,IAAI;cAChBC,OAAO,EAAE,IAAI;cACbC,MAAM,EAAE;aACT;WAEJ;UACDC,SAAS,EAAE;YACTC,YAAY,EAAE;WACf;UACDC,OAAO,EAAE;YACPC,KAAK,EAAE,CACL;cACEC,MAAM,EAAE,GAAG;cACXC,IAAI,EAAE,IAAI,CAACC,YAAY;cACvBC,iBAAiB,EAAE;aACpB;;;OAIR;MACDC,YAAY,EAAE;;;;;KAKf,CACF;IACD,KAAAC,OAAO,GAAsB,EAAE;EAK3B;EAEJH,YAAYA,CAACI,SAAS;IACpB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;MAC7BC,UAAU,CAAC,MAAK;QACd,MAAMC,cAAc,GAAG,CACrB,qBAAqB,EACrB,oBAAoB,EACpB,gBAAgB,CACjB,CACEC,MAAM,CAACC,cAAc,CAAC,CACtBC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QAEfL,OAAO,CAACE,cAAc,CAAC;MACzB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,SAASE,cAAcA,CAACE,IAAI;MAC1B,MAAMC,YAAY,GAAGT,SAAS,CAACU,WAAW,EAAE;MAC5C,OAAOF,IAAI,CAACE,WAAW,EAAE,CAACC,QAAQ,CAACF,YAAY,CAAC;MAChD;IACF;EACF;;EAGAG,QAAQA,CAAA,GACR;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7C,IAAI,CAAC8C,KAAK,EAAE;MACnB;MACA,IAAI,CAACjD,WAAW,CAACkD,KAAK,CAAC,IAAI,CAAC9C,KAAK,CAAC;;EAEtC;EAEA+C,YAAYA,CAAA;IACV,IAAI,CAACnD,WAAW,CAACoD,OAAO,CAAC,aAAa,CAAC;EACzC;EAAC,QAAAC,CAAA;qBAxGUvD,6BAA6B,EAAAwD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAA7BhE,6BAA6B;IAAAiE,SAAA;IAAAC,MAAA;MAAAC,aAAA;IAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb1CjB,EAAA,CAAAmB,cAAA,aAA0B;QACEnB,EAAA,CAAAoB,MAAA,GACpB;;QAAApB,EAAA,CAAAqB,SAAA,SAAI;QACJrB,EAAA,CAAAmB,cAAA,YAAO;QAAAnB,EAAA,CAAAoB,MAAA,GAAkE;;QAAApB,EAAA,CAAAsB,YAAA,EAAQ;QAErFtB,EAAA,CAAAmB,cAAA,gBAAgF;QAAzBnB,EAAA,CAAAuB,UAAA,mBAAAC,+DAAA;UAAA,OAASN,GAAA,CAAArB,YAAA,EAAc;QAAA,EAAC;QAC7EG,EAAA,CAAAmB,cAAA,cAAyB;QAAAnB,EAAA,CAAAoB,MAAA,cAAO;QAAApB,EAAA,CAAAsB,YAAA,EAAO;QAG3CtB,EAAA,CAAAmB,cAAA,cAAwB;QACGnB,EAAA,CAAAuB,UAAA,sBAAAE,iEAAA;UAAA,OAAYP,GAAA,CAAAxB,QAAA,EAAU;QAAA,EAAC;QAC9CM,EAAA,CAAAqB,SAAA,sBAA+F;QACjGrB,EAAA,CAAAsB,YAAA,EAAO;QAETtB,EAAA,CAAAmB,cAAA,cAA0B;QACwBnB,EAAA,CAAAuB,UAAA,mBAAAG,gEAAA;UAAA,OAASR,GAAA,CAAArB,YAAA,EAAc;QAAA,EAAC;QAACG,EAAA,CAAAoB,MAAA,aAAK;QAAApB,EAAA,CAAAsB,YAAA,EAAS;QACvFtB,EAAA,CAAAmB,cAAA,iBAAmE;QAArBnB,EAAA,CAAAuB,UAAA,mBAAAI,gEAAA;UAAA,OAAST,GAAA,CAAAxB,QAAA,EAAU;QAAA,EAAC;QAACM,EAAA,CAAAoB,MAAA,cAAM;QAAApB,EAAA,CAAAsB,YAAA,EAAS;;;QAf1DtB,EAAA,CAAA4B,SAAA,GACpB;QADoB5B,EAAA,CAAA6B,kBAAA,KAAA7B,EAAA,CAAA8B,WAAA,gCACpB;QACO9B,EAAA,CAAA4B,SAAA,GAAkE;QAAlE5B,EAAA,CAAA+B,iBAAA,CAAA/B,EAAA,CAAA8B,WAAA,2DAAkE;QAOvE9B,EAAA,CAAA4B,SAAA,GAAkB;QAAlB5B,EAAA,CAAAgC,UAAA,cAAAd,GAAA,CAAArE,IAAA,CAAkB;QACTmD,EAAA,CAAA4B,SAAA,GAAa;QAAb5B,EAAA,CAAAgC,UAAA,SAAAd,GAAA,CAAArE,IAAA,CAAa,WAAAqE,GAAA,CAAAjE,MAAA,WAAAiE,GAAA,CAAApE,KAAA,aAAAoE,GAAA,CAAAtC,OAAA", "names": ["FormGroup", "environment", "ModalMessageReminderComponent", "constructor", "activeModal", "registrationService", "_translateService", "form", "model", "title", "message", "fields", "key", "type", "props", "label", "instant", "required", "config", "simpleUpload", "uploadUrl", "apiUrl", "placeholder", "htmlSupport", "allow", "name", "attributes", "classes", "styles", "htmlEmbed", "showPreviews", "mention", "feeds", "marker", "feed", "getFeedItems", "minimumCharacters", "defaultValue", "options", "queryText", "Promise", "resolve", "setTimeout", "itemsToDisplay", "filter", "isItemMatching", "slice", "item", "searchString", "toLowerCase", "includes", "ngOnInit", "onSubmit", "valid", "close", "dismissModal", "dismiss", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "RegistrationService", "i3", "TranslateService", "_2", "selectors", "inputs", "registrations", "decls", "vars", "consts", "template", "ModalMessageReminderComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "ModalMessageReminderComponent_Template_button_click_8_listener", "ModalMessageReminderComponent_Template_form_ngSubmit_12_listener", "ModalMessageReminderComponent_Template_button_click_15_listener", "ModalMessageReminderComponent_Template_button_click_17_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\modal-message-reminder\\modal-message-reminder.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\modal-message-reminder\\modal-message-reminder.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig, coreConfig } from 'app/app-config';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { environment } from 'environments/environment';\r\n@Component({\r\n  selector: 'app-modal-message-reminder',\r\n  templateUrl: './modal-message-reminder.component.html',\r\n  styleUrls: ['./modal-message-reminder.component.scss']\r\n})\r\nexport class ModalMessageReminderComponent implements OnInit {\r\n  @Input() registrations: any;\r\n  form = new FormGroup({});\r\n  modalRef: any;\r\n  model = {\r\n    title: \"Player registration is incomplete\",\r\n    message: '',\r\n\r\n  }\r\n  fields: FormlyFieldConfig[] = [\r\n    {\r\n      key: 'title',\r\n      type: 'input',\r\n      props: {\r\n        type: 'text',\r\n        label: this._translateService.instant('Title'),\r\n        required: true,\r\n      },\r\n    },\r\n    {\r\n      key: 'content',\r\n      type: 'ckeditor5',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Content'),\r\n        config: {\r\n          simpleUpload: {\r\n            // The URL that the images are uploaded to.\r\n            uploadUrl: `${environment.apiUrl}/clubs/editor`,\r\n          },\r\n          placeholder: this._translateService.instant('Type the content here'),\r\n          htmlSupport: {\r\n            allow: [\r\n              {\r\n                name: /.*/,\r\n                attributes: true,\r\n                classes: true,\r\n                styles: true,\r\n              },\r\n            ],\r\n          },\r\n          htmlEmbed: {\r\n            showPreviews: true,\r\n          },\r\n          mention: {\r\n            feeds: [\r\n              {\r\n                marker: '{',\r\n                feed: this.getFeedItems,\r\n                minimumCharacters: 1,\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      defaultValue: `<p><strong>Dear parent,</strong></p>\r\n      <p>Please note that the registration for player registration is currently incomplete and pending the successful submission of the following information:</p>\r\n      <ul>\r\n        <li>Please go to Registration in the app and resubmit the information.</li>\r\n      </ul>`\r\n    }\r\n  ];\r\n  options: FormlyFormOptions = {};\r\n  constructor(\r\n    private activeModal: NgbActiveModal,\r\n    private registrationService: RegistrationService,\r\n    private _translateService: TranslateService\r\n  ) { }\r\n\r\n  getFeedItems(queryText) {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const itemsToDisplay = [\r\n          '{{user_first_name}}',\r\n          '{{user_last_name}}',\r\n          '{{user_email}}',\r\n        ]\r\n          .filter(isItemMatching)\r\n          .slice(0, 10);\r\n\r\n        resolve(itemsToDisplay);\r\n      }, 100);\r\n    });\r\n\r\n    function isItemMatching(item) {\r\n      const searchString = queryText.toLowerCase();\r\n      return item.toLowerCase().includes(searchString);\r\n      // ||        item.id.toLowerCase().includes(searchString)\r\n    }\r\n  }\r\n\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.form.valid) {\r\n      // Perform your submit logic here\r\n      this.activeModal.close(this.model);\r\n    }\r\n  }\r\n\r\n  dismissModal() {\r\n    this.activeModal.dismiss('Close click');\r\n  }\r\n\r\n\r\n}\r\n", "<div class=\"modal-header\">\r\n    <h4 class=\"modal-title\">{{'Message Reminder' | translate}}\r\n        <br>\r\n        <small>{{'Please only use for awaiting update registration' | translate}}</small>\r\n    </h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"dismissModal()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <div class=\"modal-body\">\r\n    <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\">\r\n      <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\" [options]=\"options\"></formly-form>\r\n    </form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"button\" class=\"btn btn-secondary\" (click)=\"dismissModal()\">Close</button>\r\n    <button type=\"button\" class=\"btn btn-primary\" (click)=\"onSubmit()\">Submit</button>\r\n  </div>\r\n  "]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
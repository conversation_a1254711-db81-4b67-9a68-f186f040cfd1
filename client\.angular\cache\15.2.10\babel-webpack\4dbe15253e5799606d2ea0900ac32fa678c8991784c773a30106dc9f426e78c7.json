{"ast": null, "code": "import { not } from '../util/not';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { filter } from '../operators/filter';\nimport { Observable } from '../Observable';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(new Observable(subscribeTo(source))), filter(not(predicate, thisArg))(new Observable(subscribeTo(source)))];\n}", "map": {"version": 3, "names": ["not", "subscribeTo", "filter", "Observable", "partition", "source", "predicate", "thisArg"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/partition.js"], "sourcesContent": ["import { not } from '../util/not';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { filter } from '../operators/filter';\nimport { Observable } from '../Observable';\nexport function partition(source, predicate, thisArg) {\n    return [\n        filter(predicate, thisArg)(new Observable(subscribeTo(source))),\n        filter(not(predicate, thisArg))(new Observable(subscribeTo(source)))\n    ];\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAClD,OAAO,CACHL,MAAM,CAACI,SAAS,EAAEC,OAAO,CAAC,CAAC,IAAIJ,UAAU,CAACF,WAAW,CAACI,MAAM,CAAC,CAAC,CAAC,EAC/DH,MAAM,CAACF,GAAG,CAACM,SAAS,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAIJ,UAAU,CAACF,WAAW,CAACI,MAAM,CAAC,CAAC,CAAC,CACvE;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
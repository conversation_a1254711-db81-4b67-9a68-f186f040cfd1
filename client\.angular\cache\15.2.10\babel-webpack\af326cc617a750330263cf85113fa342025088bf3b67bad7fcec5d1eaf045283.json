{"ast": null, "code": "import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { DataTablesModule } from 'angular-datatables';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AdminRegistrationComponent } from './admin-registration.component';\nimport { ValidatorComponent } from './validator/validator.component';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { CustomWrapperComponent } from './validator/custom-wraper.component';\nimport { ModalInputCancelReasonComponent } from './modal-input-cancel-reason/modal-input-cancel-reason.component';\nimport { ModalSuitableGroupsComponent } from './modal-suitable-groups/modal-suitable-groups.component';\nimport { ModalMessageReminderComponent } from './modal-message-reminder/modal-message-reminder.component';\nimport { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\nimport { FormlyFieldFile } from 'app/components/file-type/file-type.component';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { ModalChangeClubComponent } from './modal-change-club/modal-change-club.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\n// routing\nconst routes = [{\n  path: '',\n  component: AdminRegistrationComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_registrations\n  }\n}];\nexport class AdminRegistrationModule {\n  static #_ = this.ɵfac = function AdminRegistrationModule_Factory(t) {\n    return new (t || AdminRegistrationModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdminRegistrationModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), CoreSidebarModule, CoreCommonModule, DataTablesModule, ContentHeaderModule, TranslateModule, FormsModule, NgSelectModule, EditorSidebarModule, ReactiveFormsModule, FormlyModule.forRoot({\n      wrappers: [{\n        name: 'validate',\n        component: CustomWrapperComponent\n      }],\n      types: [{\n        name: 'ng-select',\n        component: NgSelectTypeComponent,\n        wrappers: ['form-field']\n      }, {\n        name: 'file',\n        component: FormlyFieldFile,\n        wrappers: ['form-field']\n      }],\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }]\n    }), FormlyBootstrapModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminRegistrationModule, {\n    declarations: [AdminRegistrationComponent, ValidatorComponent, CustomWrapperComponent, ModalSuitableGroupsComponent, ModalInputCancelReasonComponent, ModalMessageReminderComponent, ModalChangeClubComponent],\n    imports: [CommonModule, i1.RouterModule, CoreSidebarModule, CoreCommonModule, DataTablesModule, ContentHeaderModule, TranslateModule, FormsModule, NgSelectModule, EditorSidebarModule, ReactiveFormsModule, i2.FormlyModule, FormlyBootstrapModule],\n    exports: [AdminRegistrationComponent, ValidatorComponent, ModalInputCancelReasonComponent, ModalSuitableGroupsComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAAE,SAASA,mBAAmB,QAAQ,4DAA4D;AAChG,SAAiBC,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,eAAe,QAA0B,qBAAqB;AACvE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AAErD,SACEC,mBAAmB,EACnBC,uBAAuB,QAClB,qDAAqD;AAC5D,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAAuBC,YAAY,QAAQ,kBAAkB;AAC7D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,+BAA+B,QAAQ,iEAAiE;AACjH,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,qBAAqB,QAAQ,wDAAwD;AAE9F,SAASC,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC5C,SAASC,wBAAwB,QAAQ,iDAAiD;;;;AAExF;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEf,0BAA0B;EACrCgB,WAAW,EAAE,CAACN,gBAAgB,CAAC;EAC/BO,IAAI,EAAE;IAAEC,WAAW,EAAEP,SAAS,CAACQ,WAAW,CAACC;EAAoB;CAChE,CACF;AAiDD,OAAM,MAAOC,uBAAuB;EAAA,QAAAC,CAAA;qBAAvBD,uBAAuB;EAAA;EAAA,QAAAE,EAAA;UAAvBF;EAAuB;EAAA,QAAAG,EAAA;cApChClC,YAAY,EACZD,YAAY,CAACoC,QAAQ,CAACZ,MAAM,CAAC,EAC7BtB,iBAAiB,EACjBC,gBAAgB,EAChBE,gBAAgB,EAChBN,mBAAmB,EACnBK,eAAe,EACfK,WAAW,EACXH,cAAc,EACdC,mBAAmB,EACnBG,mBAAmB,EACnBG,YAAY,CAACwB,OAAO,CAAC;MACnBC,QAAQ,EAAE,CAAC;QAAEC,IAAI,EAAE,UAAU;QAAEb,SAAS,EAAEX;MAAsB,CAAE,CAAC;MACnEyB,KAAK,EAAE,CACL;QACED,IAAI,EAAE,WAAW;QACjBb,SAAS,EAAEP,qBAAqB;QAChCmB,QAAQ,EAAE,CAAC,YAAY;OACxB,EACD;QACEC,IAAI,EAAE,MAAM;QACZb,SAAS,EAAEN,eAAe;QAC1BkB,QAAQ,EAAE,CAAC,YAAY;OACxB,CACF;MAGDG,kBAAkB,EAAE,CAClB;QAAEF,IAAI,EAAE,aAAa;QAAEG,OAAO,EAAElC;MAAuB,CAAE;KAE5D,CAAC,EACFM,qBAAqB;EAAA;;;2EAKZkB,uBAAuB;IAAAW,YAAA,GA7ChChC,0BAA0B,EAC1BC,kBAAkB,EAClBG,sBAAsB,EACtBE,4BAA4B,EAC5BD,+BAA+B,EAC/BE,6BAA6B,EAC7BK,wBAAwB;IAAAqB,OAAA,GAGxB3C,YAAY,EAAA4C,EAAA,CAAA7C,YAAA,EAEZE,iBAAiB,EACjBC,gBAAgB,EAChBE,gBAAgB,EAChBN,mBAAmB,EACnBK,eAAe,EACfK,WAAW,EACXH,cAAc,EACdC,mBAAmB,EACnBG,mBAAmB,EAAAoC,EAAA,CAAAjC,YAAA,EAqBnBC,qBAAqB;IAAAiC,OAAA,GAEbpC,0BAA0B,EAAEC,kBAAkB,EAAEI,+BAA+B,EAAEC,4BAA4B;EAAA;AAAA", "names": ["ContentHeaderModule", "RouterModule", "CommonModule", "CoreSidebarModule", "CoreCommonModule", "TranslateModule", "DataTablesModule", "NgSelectModule", "EditorSidebarModule", "serverValidationMessage", "FormsModule", "ReactiveFormsModule", "AdminRegistrationComponent", "ValidatorComponent", "FormlyModule", "FormlyBootstrapModule", "CustomWrapperComponent", "ModalInputCancelReasonComponent", "ModalSuitableGroupsComponent", "ModalMessageReminderComponent", "NgSelectTypeComponent", "FormlyFieldFile", "PermissionsGuard", "AppConfig", "ModalChangeClubComponent", "routes", "path", "component", "canActivate", "data", "permissions", "PERMISSIONS", "manage_registrations", "AdminRegistrationModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "wrappers", "name", "types", "validationMessages", "message", "declarations", "imports", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\admin-registration.module.ts"], "sourcesContent": ["  import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\n  import { Routes, RouterModule } from '@angular/router';\r\n  import { NgModule } from '@angular/core';\r\n  import { CommonModule } from '@angular/common';\r\n  import { CoreSidebarModule } from '@core/components';\r\n  import { CoreCommonModule } from '@core/common.module';\r\n  import { TranslateModule, TranslateService } from '@ngx-translate/core';\r\n  import { DataTablesModule } from 'angular-datatables';\r\n  import { NgSelectModule } from '@ng-select/ng-select';\r\n\r\n  import {\r\n    EditorSidebarModule,\r\n    serverValidationMessage,\r\n  } from 'app/components/editor-sidebar/editor-sidebar.module';\r\n  import { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n  import { AdminRegistrationComponent } from './admin-registration.component';\r\n  import { ValidatorComponent } from './validator/validator.component';\r\n  import { FormlyConfig, FormlyModule } from '@ngx-formly/core';\r\n  import { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\n  import { CustomWrapperComponent } from './validator/custom-wraper.component';\r\n  import { ModalInputCancelReasonComponent } from './modal-input-cancel-reason/modal-input-cancel-reason.component';\r\n  import { ModalSuitableGroupsComponent } from './modal-suitable-groups/modal-suitable-groups.component';\r\n  import { ModalMessageReminderComponent } from './modal-message-reminder/modal-message-reminder.component';\r\n  import { NgSelectTypeComponent } from 'app/components/ng-select-type/ng-select-type.component';\r\n\r\n  import { FormlyFieldFile } from 'app/components/file-type/file-type.component';\r\n  import { PermissionsGuard } from 'app/guards/permissions.guard';\r\n  import { AppConfig } from 'app/app-config';\r\nimport { ModalChangeClubComponent } from './modal-change-club/modal-change-club.component';\r\n\r\n  // routing\r\n  const routes: Routes = [\r\n    {\r\n      path: '',\r\n      component: AdminRegistrationComponent,\r\n      canActivate: [PermissionsGuard],\r\n      data: { permissions: AppConfig.PERMISSIONS.manage_registrations },\r\n    },\r\n  ];\r\n\r\n  @NgModule({\r\n    declarations: [\r\n      AdminRegistrationComponent,\r\n      ValidatorComponent,\r\n      CustomWrapperComponent,\r\n      ModalSuitableGroupsComponent,\r\n      ModalInputCancelReasonComponent,\r\n      ModalMessageReminderComponent,\r\n      ModalChangeClubComponent,\r\n    ],\r\n    imports: [\r\n      CommonModule,\r\n      RouterModule.forChild(routes),\r\n      CoreSidebarModule,\r\n      CoreCommonModule,\r\n      DataTablesModule,\r\n      ContentHeaderModule,\r\n      TranslateModule,\r\n      FormsModule,\r\n      NgSelectModule,\r\n      EditorSidebarModule,\r\n      ReactiveFormsModule,\r\n      FormlyModule.forRoot({\r\n        wrappers: [{ name: 'validate', component: CustomWrapperComponent }],\r\n        types: [\r\n          {\r\n            name: 'ng-select',\r\n            component: NgSelectTypeComponent,\r\n            wrappers: ['form-field'],\r\n          },\r\n          {\r\n            name: 'file',\r\n            component: FormlyFieldFile,\r\n            wrappers: ['form-field'],\r\n          }\r\n        ],\r\n\r\n\r\n        validationMessages: [\r\n          { name: 'serverError', message: serverValidationMessage },\r\n        ],\r\n      }),\r\n      FormlyBootstrapModule,\r\n    ],\r\n    exports: [AdminRegistrationComponent, ValidatorComponent, ModalInputCancelReasonComponent, ModalSuitableGroupsComponent],\r\n    providers: [],\r\n  })\r\n  export class AdminRegistrationModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
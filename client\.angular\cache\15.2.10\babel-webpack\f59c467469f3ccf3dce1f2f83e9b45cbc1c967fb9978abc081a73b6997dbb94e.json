{"ast": null, "code": "import realNames from './_realNames.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the name of `func`.\n *\n * @private\n * @param {Function} func The function to query.\n * @returns {string} Returns the function name.\n */\nfunction getFuncName(func) {\n  var result = func.name + '',\n    array = realNames[result],\n    length = hasOwnProperty.call(realNames, result) ? array.length : 0;\n  while (length--) {\n    var data = array[length],\n      otherFunc = data.func;\n    if (otherFunc == null || otherFunc == func) {\n      return data.name;\n    }\n  }\n  return result;\n}\nexport default getFuncName;", "map": {"version": 3, "names": ["realNames", "objectProto", "Object", "prototype", "hasOwnProperty", "getFuncName", "func", "result", "name", "array", "length", "call", "data", "otherFunc"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_getFuncName.js"], "sourcesContent": ["import realNames from './_realNames.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the name of `func`.\n *\n * @private\n * @param {Function} func The function to query.\n * @returns {string} Returns the function name.\n */\nfunction getFuncName(func) {\n  var result = (func.name + ''),\n      array = realNames[result],\n      length = hasOwnProperty.call(realNames, result) ? array.length : 0;\n\n  while (length--) {\n    var data = array[length],\n        otherFunc = data.func;\n    if (otherFunc == null || otherFunc == func) {\n      return data.name;\n    }\n  }\n  return result;\n}\n\nexport default getFuncName;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,MAAM,GAAID,IAAI,CAACE,IAAI,GAAG,EAAG;IACzBC,KAAK,GAAGT,SAAS,CAACO,MAAM,CAAC;IACzBG,MAAM,GAAGN,cAAc,CAACO,IAAI,CAACX,SAAS,EAAEO,MAAM,CAAC,GAAGE,KAAK,CAACC,MAAM,GAAG,CAAC;EAEtE,OAAOA,MAAM,EAAE,EAAE;IACf,IAAIE,IAAI,GAAGH,KAAK,CAACC,MAAM,CAAC;MACpBG,SAAS,GAAGD,IAAI,CAACN,IAAI;IACzB,IAAIO,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAIP,IAAI,EAAE;MAC1C,OAAOM,IAAI,CAACJ,IAAI;IAClB;EACF;EACA,OAAOD,MAAM;AACf;AAEA,eAAeF,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
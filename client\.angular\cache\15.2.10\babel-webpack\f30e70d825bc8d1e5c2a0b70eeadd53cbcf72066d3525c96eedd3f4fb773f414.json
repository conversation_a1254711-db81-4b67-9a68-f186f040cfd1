{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function ignoreElements() {\n  return function ignoreElementsOperatorFunction(source) {\n    return source.lift(new IgnoreElementsOperator());\n  };\n}\nclass IgnoreElementsOperator {\n  call(subscriber, source) {\n    return source.subscribe(new IgnoreElementsSubscriber(subscriber));\n  }\n}\nclass IgnoreElementsSubscriber extends Subscriber {\n  _next(unused) {}\n}", "map": {"version": 3, "names": ["Subscriber", "ignoreElements", "ignoreElementsOperatorFunction", "source", "lift", "IgnoreElementsOperator", "call", "subscriber", "subscribe", "IgnoreElementsSubscriber", "_next", "unused"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/ignoreElements.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function ignoreElements() {\n    return function ignoreElementsOperatorFunction(source) {\n        return source.lift(new IgnoreElementsOperator());\n    };\n}\nclass IgnoreElementsOperator {\n    call(subscriber, source) {\n        return source.subscribe(new IgnoreElementsSubscriber(subscriber));\n    }\n}\nclass IgnoreElementsSubscriber extends Subscriber {\n    _next(unused) {\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC7B,OAAO,SAASC,8BAA8BA,CAACC,MAAM,EAAE;IACnD,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,sBAAsB,CAAC,CAAC,CAAC;EACpD,CAAC;AACL;AACA,MAAMA,sBAAsB,CAAC;EACzBC,IAAIA,CAACC,UAAU,EAAEJ,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACK,SAAS,CAAC,IAAIC,wBAAwB,CAACF,UAAU,CAAC,CAAC;EACrE;AACJ;AACA,MAAME,wBAAwB,SAAST,UAAU,CAAC;EAC9CU,KAAKA,CAACC,MAAM,EAAE,CACd;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
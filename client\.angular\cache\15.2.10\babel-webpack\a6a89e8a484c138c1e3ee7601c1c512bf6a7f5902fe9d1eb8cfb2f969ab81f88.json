{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"app/services/auth.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction VerifyTwofaComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"d-block\": a0\n  };\n};\nfunction VerifyTwofaComponent_ng_template_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r6.submitted && ctx_r6.err_message));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.err_message, \" \");\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid error\": a0\n  };\n};\nfunction VerifyTwofaComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"h4\", 4);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function VerifyTwofaComponent_ng_template_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.close());\n    });\n    i0.ɵɵelementStart(5, \"span\", 6);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"form\")(8, \"div\", 7)(9, \"div\", 8)(10, \"input\", 9);\n    i0.ɵɵlistener(\"ngModelChange\", function VerifyTwofaComponent_ng_template_1_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.code = $event);\n    });\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, VerifyTwofaComponent_ng_template_1_div_12_Template, 2, 4, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function VerifyTwofaComponent_ng_template_1_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.sendMailDisable2FA());\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function VerifyTwofaComponent_ng_template_1_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.verifyCode());\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 10, \"Two Factor Authentication\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(11, 12, \"Enter authentication code\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.code)(\"ngClass\", i0.ɵɵpureFunction1(18, _c1, ctx_r2.submitted && ctx_r2.err_message && !ctx_r2.code));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitted && ctx_r2.err_message && !ctx_r2.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r2.canRequestNewCode);\n    i0.ɵɵattribute(\"aria-disabled\", !ctx_r2.canRequestNewCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 14, \"Request to disable 2FA ?\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 16, \"Submit\"));\n  }\n}\nfunction VerifyTwofaComponent_ng_template_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r13.submitted && ctx_r13.err_message));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.err_message, \" \");\n  }\n}\nfunction VerifyTwofaComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"h4\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function VerifyTwofaComponent_ng_template_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.close());\n    });\n    i0.ɵɵelementStart(5, \"span\", 6);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"form\")(8, \"div\", 7)(9, \"div\", 8)(10, \"input\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function VerifyTwofaComponent_ng_template_3_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.code = $event);\n    });\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, VerifyTwofaComponent_ng_template_3_div_12_Template, 2, 4, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function VerifyTwofaComponent_ng_template_3_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.sendMailDisable2FA());\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function VerifyTwofaComponent_ng_template_3_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.verifyDisable2FA());\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 7, \"Verify Disable 2FA\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(11, 9, \"Enter your OTP code\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.code)(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, ctx_r4.submitted && ctx_r4.err_message && !ctx_r4.code));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.submitted && ctx_r4.err_message && !ctx_r4.code);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.requestNewCodeText, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 11, \"Submit\"));\n  }\n}\nexport class VerifyTwofaComponent {\n  constructor(_modalService, _authService, _translateService, _loadingService) {\n    this._modalService = _modalService;\n    this._authService = _authService;\n    this._translateService = _translateService;\n    this._loadingService = _loadingService;\n    this.submitted = false;\n    this.isDisable2FA = false;\n    this.requestNewCodeText = 'Didn\\'t receive the code? Request a new one';\n    this.canRequestNewCode = true;\n  }\n  ngOnInit() {\n    console.log('user', this.user);\n  }\n  close() {\n    this._modalService.dismiss();\n  }\n  verifyCode() {\n    this.err_message = '';\n    this.submitted = true;\n    if (!this.code) {\n      this.err_message = this._translateService.instant('Please enter your verification code!');\n      return;\n    }\n    this._loadingService.show();\n    this._authService.verify2FA(this.user.id, this.code).subscribe(res => {\n      if (res) {\n        this._authService.loginSuccess(this.user, this.authToken);\n      }\n    }, err => {\n      Swal.fire({\n        icon: 'error',\n        title: this._translateService.instant('Error'),\n        text: err.message\n      });\n    }, () => {\n      this._loadingService.dismiss();\n      this.close();\n    });\n  }\n  sendMailDisable2FA() {\n    if (!this.canRequestNewCode) {\n      return;\n    } else {\n      this.canRequestNewCode = false;\n    }\n    let counter = 30;\n    const textBase = this.requestNewCodeText;\n    this.requestNewCodeText = textBase + ' (' + counter + 's)';\n    const handleCounter = () => {\n      counter--;\n      this.requestNewCodeText = textBase + ' (' + counter + 's)';\n      if (counter <= 0) {\n        clearInterval(interval);\n        this.requestNewCodeText = textBase;\n        this.canRequestNewCode = true;\n      }\n    };\n    let interval = setInterval(handleCounter, 1000);\n    this._loadingService.show();\n    this._authService.sendMailDisable2FA(this.user.id).subscribe(res => {\n      this._loadingService.dismiss();\n      Swal.fire({\n        icon: 'success',\n        title: this._translateService.instant('Success'),\n        text: res.message,\n        showConfirmButton: true\n      }).then(result => {\n        if (result.isConfirmed) {\n          this.isDisable2FA = true;\n        }\n      });\n    }, error => {\n      this._loadingService.dismiss();\n      Swal.fire({\n        icon: 'error',\n        title: this._translateService.instant('Error'),\n        text: error.message\n      }).then(() => {\n        this.close();\n      });\n    });\n  }\n  verifyDisable2FA() {\n    this.err_message = '';\n    this.submitted = true;\n    if (this.code.trim() === '') {\n      this.err_message = 'Please enter your verification code!';\n      return;\n    }\n    this._loadingService.show();\n    this._authService.verifyDisable2FA(this.email, this.code).subscribe(res => {\n      Swal.fire({\n        icon: 'success',\n        title: this._translateService.instant('Success'),\n        text: res.message\n      });\n      this.close();\n    }, error => {\n      Swal.fire({\n        icon: 'error',\n        title: this._translateService.instant('Error'),\n        text: error.message\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  static #_ = this.ɵfac = function VerifyTwofaComponent_Factory(t) {\n    return new (t || VerifyTwofaComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.LoadingService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: VerifyTwofaComponent,\n    selectors: [[\"app-verify-twofa\"]],\n    inputs: {\n      email: \"email\",\n      password: \"password\",\n      user: \"user\",\n      authToken: \"authToken\"\n    },\n    decls: 5,\n    vars: 1,\n    consts: [[4, \"ngTemplateOutlet\"], [\"modalVerifyTwofa\", \"\"], [\"modalDisable2FA\", \"\"], [1, \"modal-header\", \"bg-white\"], [\"id\", \"label_follows\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"code\", \"name\", \"code\", \"maxlength\", \"6\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"placeholder\", \"ngClass\", \"ngModelChange\"], [\"class\", \"invalid-feedback\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"align-items-center\", 2, \"gap\", \"1rem\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"invalid-feedback\", 3, \"ngClass\"], [1, \"modal-title\"], [\"type\", \"text\", \"name\", \"code\", \"maxlength\", \"4\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"placeholder\", \"ngClass\", \"ngModelChange\"]],\n    template: function VerifyTwofaComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, VerifyTwofaComponent_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, VerifyTwofaComponent_ng_template_1_Template, 20, 20, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(3, VerifyTwofaComponent_ng_template_3_Template, 19, 15, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        const _r3 = i0.ɵɵreference(4);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", !ctx.isDisable2FA ? _r1 : _r3);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i5.NgTemplateOutlet, i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.RequiredValidator, i6.MaxLengthValidator, i6.NgModel, i6.NgForm, i3.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAKA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;ICL9BC,EAAA,CAAAC,kBAAA,GACe;;;;;;;;;;IAkBPD,EAAA,CAAAE,cAAA,cACyD;IACvDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAFDJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,WAAA,EAAmD;IACtDV,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAY,kBAAA,MAAAJ,MAAA,CAAAE,WAAA,MACF;;;;;;;;;;;IAlBNV,EAAA,CAAAE,cAAA,aAAmC;IAE/BF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,gBAAyE;IAAlBF,EAAA,CAAAa,UAAA,mBAAAC,oEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IACtEpB,EAAA,CAAAE,cAAA,cAAyB;IAAAF,EAAA,CAAAG,MAAA,aAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG3CJ,EAAA,CAAAE,cAAA,WAAM;IAG8DF,EAAA,CAAAa,UAAA,2BAAAQ,4EAAAC,MAAA;MAAAtB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAI,MAAA,CAAAC,IAAA,GAAAF,MAAA;IAAA,EAAkB;;IAAhFtB,EAAA,CAAAI,YAAA,EAGuF;IACvFJ,EAAA,CAAAyB,UAAA,KAAAC,kDAAA,kBAGM;IACR1B,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAA6E;IAC9CF,EAAA,CAAAa,UAAA,mBAAAc,gEAAA;MAAA3B,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAY,OAAA,GAAA5B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAS,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAEzD7B,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,kBAAqE;IAAvBF,EAAA,CAAAa,UAAA,mBAAAiB,qEAAA;MAAA9B,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAe,OAAA,GAAA/B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAY,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAAChC,EAAA,CAAAG,MAAA,IAA0B;;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAxB1GJ,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAiC,WAAA,0CACF;IAUWjC,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAAkC,qBAAA,gBAAAlC,EAAA,CAAAiC,WAAA,sCAAyD;IAFFjC,EAAA,CAAAK,UAAA,YAAA8B,MAAA,CAAAX,IAAA,CAAkB,YAAAxB,EAAA,CAAAM,eAAA,KAAA8B,GAAA,EAAAD,MAAA,CAAA1B,SAAA,IAAA0B,MAAA,CAAAzB,WAAA,KAAAyB,MAAA,CAAAX,IAAA;IAI1ExB,EAAA,CAAAW,SAAA,GAAuC;IAAvCX,EAAA,CAAAK,UAAA,SAAA8B,MAAA,CAAA1B,SAAA,IAAA0B,MAAA,CAAAzB,WAAA,KAAAyB,MAAA,CAAAX,IAAA,CAAuC;IAOexB,EAAA,CAAAW,SAAA,GAAqC;IAArCX,EAAA,CAAAqC,WAAA,cAAAF,MAAA,CAAAG,iBAAA,CAAqC;IAC9FtC,EAAA,CAAAuC,WAAA,mBAAAJ,MAAA,CAAAG,iBAAA,CAAyC;IAC1CtC,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAiC,WAAA,0CACF;IACqEjC,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAwC,iBAAA,CAAAxC,EAAA,CAAAiC,WAAA,mBAA0B;;;;;IAsB/FjC,EAAA,CAAAE,cAAA,cACyD;IACvDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAFDJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAkC,OAAA,CAAAhC,SAAA,IAAAgC,OAAA,CAAA/B,WAAA,EAAmD;IACtDV,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAY,kBAAA,MAAA6B,OAAA,CAAA/B,WAAA,MACF;;;;;;IAlBNV,EAAA,CAAAE,cAAA,aAAmC;IAE/BF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,gBAAyE;IAAlBF,EAAA,CAAAa,UAAA,mBAAA6B,oEAAA;MAAA1C,EAAA,CAAAe,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAyB,OAAA,CAAAxB,KAAA,EAAO;IAAA,EAAC;IACtEpB,EAAA,CAAAE,cAAA,cAAyB;IAAAF,EAAA,CAAAG,MAAA,aAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG3CJ,EAAA,CAAAE,cAAA,WAAM;IAGoDF,EAAA,CAAAa,UAAA,2BAAAgC,4EAAAvB,MAAA;MAAAtB,EAAA,CAAAe,aAAA,CAAA4B,IAAA;MAAA,MAAAG,OAAA,GAAA9C,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAA2B,OAAA,CAAAtB,IAAA,GAAAF,MAAA;IAAA,EAAkB;;IAAtEtB,EAAA,CAAAI,YAAA,EAGuF;IACvFJ,EAAA,CAAAyB,UAAA,KAAAsB,kDAAA,kBAGM;IACR/C,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAA6E;IAC9CF,EAAA,CAAAa,UAAA,mBAAAmC,gEAAA;MAAAhD,EAAA,CAAAe,aAAA,CAAA4B,IAAA;MAAA,MAAAM,OAAA,GAAAjD,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAA8B,OAAA,CAAApB,kBAAA,EAAoB;IAAA,EAAC;IACzD7B,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,kBAA2E;IAA7BF,EAAA,CAAAa,UAAA,mBAAAqC,qEAAA;MAAAlD,EAAA,CAAAe,aAAA,CAAA4B,IAAA;MAAA,MAAAQ,OAAA,GAAAnD,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAgC,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAACpD,EAAA,CAAAG,MAAA,IAA0B;;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAvBhHJ,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAiC,WAAA,kCACF;IAUWjC,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAkC,qBAAA,gBAAAlC,EAAA,CAAAiC,WAAA,+BAAmD;IAFNjC,EAAA,CAAAK,UAAA,YAAAgD,MAAA,CAAA7B,IAAA,CAAkB,YAAAxB,EAAA,CAAAM,eAAA,KAAA8B,GAAA,EAAAiB,MAAA,CAAA5C,SAAA,IAAA4C,MAAA,CAAA3C,WAAA,KAAA2C,MAAA,CAAA7B,IAAA;IAIhExB,EAAA,CAAAW,SAAA,GAAuC;IAAvCX,EAAA,CAAAK,UAAA,SAAAgD,MAAA,CAAA5C,SAAA,IAAA4C,MAAA,CAAA3C,WAAA,KAAA2C,MAAA,CAAA7B,IAAA,CAAuC;IAQ3CxB,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAY,kBAAA,MAAAyC,MAAA,CAAAC,kBAAA,MACF;IAC2EtD,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAwC,iBAAA,CAAAxC,EAAA,CAAAiC,WAAA,mBAA0B;;;ADjD7G,OAAM,MAAOsB,oBAAoB;EAC7BC,YACWC,aAA6B,EAC7BC,YAAyB,EACzBC,iBAAmC,EACnCC,eAA+B;IAH/B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAM1B,KAAAnD,SAAS,GAAG,KAAK;IAMjB,KAAAoD,YAAY,GAAG,KAAK;IACpB,KAAAP,kBAAkB,GAAG,6CAA6C;IAClE,KAAAhB,iBAAiB,GAAG,IAAI;EAZxB;EAcAwB,QAAQA,CAAA;IACJC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,IAAI,CAAC;EAClC;EAEA7C,KAAKA,CAAA;IACD,IAAI,CAACqC,aAAa,CAACS,OAAO,EAAE;EAChC;EAEAlC,UAAUA,CAAA;IACN,IAAI,CAACtB,WAAW,GAAG,EAAE;IAErB,IAAI,CAACD,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC,IAAI,CAACe,IAAI,EAAE;MACZ,IAAI,CAACd,WAAW,GAAG,IAAI,CAACiD,iBAAiB,CAACQ,OAAO,CAAC,sCAAsC,CAAC;MACzF;;IAGJ,IAAI,CAACP,eAAe,CAACQ,IAAI,EAAE;IAE3B,IAAI,CAACV,YAAY,CACZW,SAAS,CAAC,IAAI,CAACJ,IAAI,CAACK,EAAE,EAAE,IAAI,CAAC9C,IAAI,CAAC,CAClC+C,SAAS,CACLC,GAAG,IAAI;MACJ,IAAIA,GAAG,EAAE;QACL,IAAI,CAACd,YAAY,CAACe,YAAY,CAAC,IAAI,CAACR,IAAI,EAAE,IAAI,CAACS,SAAS,CAAC;;IAEjE,CAAC,EACAC,GAAG,IAAI;MACJ5E,IAAI,CAAC6E,IAAI,CAAC;QACNC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,OAAO,CAAC;QAC9CY,IAAI,EAAEJ,GAAG,CAACK;OACb,CAAC;IACN,CAAC,EACD,MAAK;MACD,IAAI,CAACpB,eAAe,CAACM,OAAO,EAAE;MAC9B,IAAI,CAAC9C,KAAK,EAAE;IAChB,CAAC,CACJ;EACT;EAEAS,kBAAkBA,CAAA;IAEd,IAAI,CAAC,IAAI,CAACS,iBAAiB,EAAE;MACzB;KACH,MAAM;MACH,IAAI,CAACA,iBAAiB,GAAG,KAAK;;IAGlC,IAAI2C,OAAO,GAAG,EAAE;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAAC5B,kBAAkB;IACxC,IAAI,CAACA,kBAAkB,GAAG4B,QAAQ,GAAG,IAAI,GAAGD,OAAO,GAAG,IAAI;IAE1D,MAAME,aAAa,GAAGA,CAAA,KAAK;MACvBF,OAAO,EAAE;MACT,IAAI,CAAC3B,kBAAkB,GAAG4B,QAAQ,GAAG,IAAI,GAAGD,OAAO,GAAG,IAAI;MAE1D,IAAIA,OAAO,IAAI,CAAC,EAAE;QACdG,aAAa,CAACC,QAAQ,CAAC;QACvB,IAAI,CAAC/B,kBAAkB,GAAG4B,QAAQ;QAClC,IAAI,CAAC5C,iBAAiB,GAAG,IAAI;;IAErC,CAAC;IAED,IAAI+C,QAAQ,GAAGC,WAAW,CAACH,aAAa,EAAE,IAAI,CAAC;IAE/C,IAAI,CAACvB,eAAe,CAACQ,IAAI,EAAE;IAC3B,IAAI,CAACV,YAAY,CAAC7B,kBAAkB,CAAC,IAAI,CAACoC,IAAI,CAACK,EAAE,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAI;MACjE,IAAI,CAACZ,eAAe,CAACM,OAAO,EAAE;MAC9BnE,IAAI,CAAC6E,IAAI,CAAC;QACNC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,SAAS,CAAC;QAChDY,IAAI,EAAEP,GAAG,CAACQ,OAAO;QACjBO,iBAAiB,EAAE;OACtB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,WAAW,EAAE;UACpB,IAAI,CAAC7B,YAAY,GAAG,IAAI;;MAEhC,CAAC,CAAC;IACN,CAAC,EAAG8B,KAAK,IAAI;MACT,IAAI,CAAC/B,eAAe,CAACM,OAAO,EAAE;MAC9BnE,IAAI,CAAC6E,IAAI,CAAC;QACNC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,OAAO,CAAC;QAC9CY,IAAI,EAAEY,KAAK,CAACX;OACf,CAAC,CAACQ,IAAI,CAAC,MAAK;QACT,IAAI,CAACpE,KAAK,EAAE;MAChB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAgC,gBAAgBA,CAAA;IACZ,IAAI,CAAC1C,WAAW,GAAG,EAAE;IACrB,IAAI,CAACD,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACe,IAAI,CAACoE,IAAI,EAAE,KAAK,EAAE,EAAE;MACzB,IAAI,CAAClF,WAAW,GAAG,sCAAsC;MACzD;;IAGJ,IAAI,CAACkD,eAAe,CAACQ,IAAI,EAAE;IAC3B,IAAI,CAACV,YAAY,CAACN,gBAAgB,CAAC,IAAI,CAACyC,KAAK,EAAE,IAAI,CAACrE,IAAI,CAAC,CAAC+C,SAAS,CAAEC,GAAG,IAAI;MACxEzE,IAAI,CAAC6E,IAAI,CAAC;QACNC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,SAAS,CAAC;QAChDY,IAAI,EAAEP,GAAG,CAACQ;OACb,CAAC;MACF,IAAI,CAAC5D,KAAK,EAAE;IAChB,CAAC,EAAGuE,KAAK,IAAI;MACT5F,IAAI,CAAC6E,IAAI,CAAC;QACNC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,OAAO,CAAC;QAC9CY,IAAI,EAAEY,KAAK,CAACX;OACf,CAAC;IACN,CAAC,EAAE,MAAK;MACJ,IAAI,CAACpB,eAAe,CAACM,OAAO,EAAE;IAClC,CAAC,CAAC;EACN;EAAC,QAAA4B,CAAA;qBAzIQvC,oBAAoB,EAAAvD,EAAA,CAAA+F,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjG,EAAA,CAAA+F,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnG,EAAA,CAAA+F,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAArG,EAAA,CAAA+F,iBAAA,CAAAO,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAApBjD,oBAAoB;IAAAkD,SAAA;IAAAC,MAAA;MAAAb,KAAA;MAAAc,QAAA;MAAA1C,IAAA;MAAAS,SAAA;IAAA;IAAAkC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbjCjH,EAAA,CAAAyB,UAAA,IAAA0F,4CAAA,0BACe;QAEfnH,EAAA,CAAAyB,UAAA,IAAA2F,2CAAA,kCAAApH,EAAA,CAAAqH,sBAAA,CA+Bc;QAEdrH,EAAA,CAAAyB,UAAA,IAAA6F,2CAAA,kCAAAtH,EAAA,CAAAqH,sBAAA,CA8Bc;;;;;QAlECrH,EAAA,CAAAK,UAAA,sBAAA6G,GAAA,CAAArD,YAAA,GAAA0D,GAAA,GAAAC,GAAA,CAAoE", "names": ["<PERSON><PERSON>", "i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r6", "submitted", "err_message", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵlistener", "VerifyTwofaComponent_ng_template_1_Template_button_click_4_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "close", "VerifyTwofaComponent_ng_template_1_Template_input_ngModelChange_10_listener", "$event", "ctx_r9", "code", "ɵɵtemplate", "VerifyTwofaComponent_ng_template_1_div_12_Template", "VerifyTwofaComponent_ng_template_1_Template_a_click_14_listener", "ctx_r10", "sendMailDisable2FA", "VerifyTwofaComponent_ng_template_1_Template_button_click_17_listener", "ctx_r11", "verifyCode", "ɵɵpipeBind1", "ɵɵpropertyInterpolate", "ctx_r2", "_c1", "ɵɵclassProp", "canRequestNewCode", "ɵɵattribute", "ɵɵtextInterpolate", "ctx_r13", "VerifyTwofaComponent_ng_template_3_Template_button_click_4_listener", "_r15", "ctx_r14", "VerifyTwofaComponent_ng_template_3_Template_input_ngModelChange_10_listener", "ctx_r16", "VerifyTwofaComponent_ng_template_3_div_12_Template", "VerifyTwofaComponent_ng_template_3_Template_a_click_14_listener", "ctx_r17", "VerifyTwofaComponent_ng_template_3_Template_button_click_16_listener", "ctx_r18", "verifyDisable2FA", "ctx_r4", "requestNewCodeText", "VerifyTwofaComponent", "constructor", "_modalService", "_authService", "_translateService", "_loadingService", "isDisable2FA", "ngOnInit", "console", "log", "user", "dismiss", "instant", "show", "verify2FA", "id", "subscribe", "res", "loginSuccess", "authToken", "err", "fire", "icon", "title", "text", "message", "counter", "textBase", "handleCounter", "clearInterval", "interval", "setInterval", "showConfirmButton", "then", "result", "isConfirmed", "error", "trim", "email", "_", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "AuthService", "i3", "TranslateService", "i4", "LoadingService", "_2", "selectors", "inputs", "password", "decls", "vars", "consts", "template", "VerifyTwofaComponent_Template", "rf", "ctx", "VerifyTwofaComponent_ng_container_0_Template", "VerifyTwofaComponent_ng_template_1_Template", "ɵɵtemplateRefExtractor", "VerifyTwofaComponent_ng_template_3_Template", "_r1", "_r3"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\verify-twofa\\verify-twofa.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\verify-twofa\\verify-twofa.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport Swal from 'sweetalert2';\r\nimport { User } from '../../interfaces/user';\r\n\r\n@Component({\r\n    selector: 'app-verify-twofa',\r\n    templateUrl: './verify-twofa.component.html',\r\n    styleUrls: ['./verify-twofa.component.scss']\r\n})\r\nexport class VerifyTwofaComponent implements OnInit {\r\n    constructor(\r\n        public _modalService: NgbActiveModal,\r\n        public _authService: AuthService,\r\n        public _translateService: TranslateService,\r\n        public _loadingService: LoadingService\r\n    ) {\r\n    }\r\n    \r\n    code: string;\r\n    err_message: string;\r\n    submitted = false;\r\n    @Input() email: string;\r\n    @Input() password: string;\r\n    @Input() user: User;\r\n    @Input() authToken: any;\r\n    \r\n    isDisable2FA = false;\r\n    requestNewCodeText = 'Didn\\'t receive the code? Request a new one';\r\n    canRequestNewCode = true;\r\n    \r\n    ngOnInit(): void {\r\n        console.log('user', this.user);\r\n    }\r\n    \r\n    close() {\r\n        this._modalService.dismiss();\r\n    }\r\n    \r\n    verifyCode() {\r\n        this.err_message = '';\r\n        \r\n        this.submitted = true;\r\n        if (!this.code) {\r\n            this.err_message = this._translateService.instant('Please enter your verification code!');\r\n            return;\r\n        }\r\n        \r\n        this._loadingService.show();\r\n        \r\n        this._authService\r\n            .verify2FA(this.user.id, this.code)\r\n            .subscribe(\r\n                (res) => {\r\n                    if (res) {\r\n                        this._authService.loginSuccess(this.user, this.authToken);\r\n                    }\r\n                },\r\n                (err) => {\r\n                    Swal.fire({\r\n                        icon: 'error',\r\n                        title: this._translateService.instant('Error'),\r\n                        text: err.message\r\n                    });\r\n                },\r\n                () => {\r\n                    this._loadingService.dismiss();\r\n                    this.close();\r\n                }\r\n            );\r\n    }\r\n    \r\n    sendMailDisable2FA() {\r\n        \r\n        if (!this.canRequestNewCode) {\r\n            return;\r\n        } else {\r\n            this.canRequestNewCode = false;\r\n        }\r\n        \r\n        let counter = 30;\r\n        const textBase = this.requestNewCodeText;\r\n        this.requestNewCodeText = textBase + ' (' + counter + 's)';\r\n        \r\n        const handleCounter = () => {\r\n            counter--;\r\n            this.requestNewCodeText = textBase + ' (' + counter + 's)';\r\n            \r\n            if (counter <= 0) {\r\n                clearInterval(interval);\r\n                this.requestNewCodeText = textBase;\r\n                this.canRequestNewCode = true;\r\n            }\r\n        };\r\n        \r\n        let interval = setInterval(handleCounter, 1000);\r\n        \r\n        this._loadingService.show();\r\n        this._authService.sendMailDisable2FA(this.user.id).subscribe((res) => {\r\n            this._loadingService.dismiss();\r\n            Swal.fire({\r\n                icon: 'success',\r\n                title: this._translateService.instant('Success'),\r\n                text: res.message,\r\n                showConfirmButton: true\r\n            }).then((result) => {\r\n                if (result.isConfirmed) {\r\n                    this.isDisable2FA = true;\r\n                }\r\n            });\r\n        }, (error) => {\r\n            this._loadingService.dismiss();\r\n            Swal.fire({\r\n                icon: 'error',\r\n                title: this._translateService.instant('Error'),\r\n                text: error.message\r\n            }).then(() => {\r\n                this.close();\r\n            });\r\n        });\r\n    }\r\n    \r\n    verifyDisable2FA() {\r\n        this.err_message = '';\r\n        this.submitted = true;\r\n        if (this.code.trim() === '') {\r\n            this.err_message = 'Please enter your verification code!';\r\n            return;\r\n        }\r\n        \r\n        this._loadingService.show();\r\n        this._authService.verifyDisable2FA(this.email, this.code).subscribe((res) => {\r\n            Swal.fire({\r\n                icon: 'success',\r\n                title: this._translateService.instant('Success'),\r\n                text: res.message\r\n            });\r\n            this.close();\r\n        }, (error) => {\r\n            Swal.fire({\r\n                icon: 'error',\r\n                title: this._translateService.instant('Error'),\r\n                text: error.message\r\n            });\r\n        }, () => {\r\n            this._loadingService.dismiss();\r\n        });\r\n    }\r\n}\r\n", "<ng-container *ngTemplateOutlet=\"!isDisable2FA ? modalVerifyTwofa : modalDisable2FA\">\r\n</ng-container>\r\n\r\n<ng-template #modalVerifyTwofa let-modal>\r\n  <div class=\"modal-header bg-white\">\r\n    <h4 class=\"modal-title\" id=\"label_follows\">\r\n      {{ 'Two Factor Authentication' | translate }}\r\n    </h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"close()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <form>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n      <div class=\"form-group\">\r\n        <input type=\"text\" class=\"form-control\" id=\"code\" name=\"code\" [(ngModel)]=\"code\"\r\n               maxlength=\"6\"\r\n               placeholder=\"{{'Enter authentication code' | translate}}\"\r\n               [ngClass]=\"{ 'is-invalid error': submitted && err_message  && !code }\" required>\r\n        <div *ngIf=\"submitted && err_message && !code\" class=\"invalid-feedback\"\r\n             [ngClass]=\"{ 'd-block': submitted && err_message }\">\r\n          {{ err_message }}\r\n        </div>\r\n      </div>\r\n      <!-- you can't get the code? -->\r\n      <div class=\"d-flex justify-content-end align-items-center\" style=\"gap: 1rem\">\r\n        <a href=\"javascript:void(0)\" (click)=\"sendMailDisable2FA()\" [class.disabled]=\"!canRequestNewCode\"\r\n           [attr.aria-disabled]=\"!canRequestNewCode\">\r\n          {{ 'Request to disable 2FA ?' | translate }}\r\n        </a>\r\n        <button type=\"submit\" class=\"btn btn-primary\" (click)=\"verifyCode()\">{{ 'Submit' | translate }}</button>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</ng-template>\r\n\r\n<ng-template #modalDisable2FA let-modal>\r\n  <div class=\"modal-header bg-white\">\r\n    <h4 class=\"modal-title\">\r\n      {{ 'Verify Disable 2FA' | translate }}\r\n    </h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"close()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <form>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n      <div class=\"form-group\">\r\n        <input type=\"text\" class=\"form-control\" name=\"code\" [(ngModel)]=\"code\"\r\n               maxlength=\"4\"\r\n               placeholder=\"{{'Enter your OTP code' | translate}}\"\r\n               [ngClass]=\"{ 'is-invalid error': submitted && err_message  && !code }\" required>\r\n        <div *ngIf=\"submitted && err_message && !code\" class=\"invalid-feedback\"\r\n             [ngClass]=\"{ 'd-block': submitted && err_message }\">\r\n          {{ err_message }}\r\n        </div>\r\n      </div>\r\n      <!-- you can't get the code? -->\r\n      <div class=\"d-flex justify-content-end align-items-center\" style=\"gap: 1rem\">\r\n        <a href=\"javascript:void(0)\" (click)=\"sendMailDisable2FA()\">\r\n          {{ requestNewCodeText }}\r\n        </a>\r\n        <button type=\"submit\" class=\"btn btn-primary\" (click)=\"verifyDisable2FA()\">{{ 'Submit' | translate }}</button>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MediaDeviceFailure, Room, RoomEvent, Track, VideoPresets, createLocalAudioTrack, createLocalTracks, createLocalVideoTrack } from 'livekit-client';\nimport { Subject } from 'rxjs';\nimport { environment } from './../../environments/environment';\nimport { map } from 'rxjs/operators';\nimport moment from 'moment';\nimport { formatNumber } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./world-time.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"app/components/overlays/overlays.service\";\nimport * as i4 from \"./settings.service\";\nexport class LivekitService {\n  constructor(worldTimeService, http, overlaysService, _settingsService) {\n    this.worldTimeService = worldTimeService;\n    this.http = http;\n    this.overlaysService = overlaysService;\n    this._settingsService = _settingsService;\n    this.listCameras = [];\n    this.listMicrophones = [];\n    this.cameras = new Subject();\n    this.microphones = new Subject();\n    this.current_camera = null;\n    this.current_microphone = null;\n    this.encoder = new TextEncoder();\n    this.decoder = new TextDecoder();\n    this.current_video_track = null;\n    this.current_audio_track = null;\n    this.is_connected = new Subject();\n    this.is_publishing = new Subject();\n    this.total_viewers = new Subject();\n    this.localVideoInitOptions = {\n      facingMode: 'environment',\n      resolution: VideoPresets.h2160\n    };\n    this.environment_camera = null;\n    this.localAudioInitOptions = {\n      echoCancellation: true,\n      noiseSuppression: true,\n      autoGainControl: true\n    };\n    this.videoPublishOptions = {\n      simulcast: false,\n      videoCodec: 'av1',\n      videoEncoding: {\n        maxBitrate: 8500000\n      }\n    };\n    this.roomMetadataSubject = new Subject();\n    this.match_seconds = 0;\n    this.room = new Room({\n      adaptiveStream: true\n    });\n    this.roomMetadataSubject.subscribe(metadata => {\n      this.currentRoomMetadata = metadata;\n    });\n    // this.requestMediaPermissions().then((stream) => {\n    //   //   setTimeout(() => {\n    // this.getCameras().then((cameras) => {\n    //   if (cameras.length > 0) {\n    //     this.listCameras = cameras;\n    //     this.cameras.next(cameras);\n    //   }\n    // });\n    // this.getMicrophones().then((microphones) => {\n    //   if (microphones.length > 0) {\n    //     this.listMicrophones = microphones;\n    //     this.microphones.next(microphones);\n    //   }\n    // });\n    //   //   }, 1000);\n    // });\n  }\n\n  requestMediaPermissions() {\n    return _asyncToGenerator(function* () {\n      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n        // Request access to the camera\n        return yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        }).then(function (stream) {\n          console.log('getUserMedia success', stream);\n          // stop the stream\n          setTimeout(() => {\n            stream.getTracks().forEach(track => {\n              track.stop();\n            });\n          }, 1000);\n          return stream;\n        }).catch(function (error) {\n          // User denied access, or an error occurred\n          console.error('getUserMedia error:', error);\n        });\n      } else {\n        console.error('getUserMedia is not supported');\n      }\n    })();\n  }\n  changeMediaDevice() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.createLocalTracks();\n      yield _this.changeMicrophone();\n      yield _this.changeCamera();\n    })();\n  }\n  // change camera\n  changeCamera() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      let videoOptions = _this2.localVideoInitOptions;\n      if (_this2.current_camera) {\n        videoOptions.deviceId = _this2.current_camera.deviceId;\n        // delete facingMode\n        delete videoOptions.facingMode;\n        yield _this2.current_video_track.restartTrack(videoOptions);\n      }\n      return _this2.current_camera;\n    })();\n  }\n  // change microphone\n  changeMicrophone() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      let audioOptions = _this3.localAudioInitOptions;\n      if (_this3.current_microphone) {\n        audioOptions.deviceId = _this3.current_microphone.deviceId;\n        yield _this3.current_audio_track.restartTrack(audioOptions);\n      }\n      return _this3.current_microphone;\n    })();\n  }\n  switchAudioInput(deviceId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      yield _this4.room.switchActiveDevice('audioinput', deviceId).catch(error => {\n        console.log('switch audio input error', error);\n      });\n    })();\n  }\n  switchVideoInput(deviceId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      let video = yield _this5.room.switchActiveDevice('videoinput', deviceId).catch(error => {\n        console.log('switch video input error', error);\n      });\n      console.log('switch video input', video);\n      // attach to video element\n    })();\n  }\n\n  stopCurrentStream() {\n    // stop tracks\n    this.stopAudioTrack();\n    this.stopVideoTrack();\n  }\n  stopAudioTrack() {\n    if (this.current_audio_track) {\n      this.current_audio_track.stop();\n    }\n  }\n  stopVideoTrack() {\n    if (this.current_video_track) {\n      this.current_video_track.stop();\n    }\n  }\n  // get media devices\n  getMediaDevices() {\n    return _asyncToGenerator(function* () {\n      const devices = yield navigator.mediaDevices.enumerateDevices();\n      return devices;\n    })();\n  }\n  // get cameras\n  getCameras() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const devices = yield _this6.getMediaDevices();\n      return devices.filter(d => d.kind === 'videoinput');\n    })();\n  }\n  // get microphones\n  getMicrophones() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const devices = yield _this7.getMediaDevices();\n      return devices.filter(d => d.kind === 'audioinput');\n    })();\n  }\n  joinRoom(token, wsURL = JSON.parse(localStorage.getItem('livekit')).wsURL) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      _this8.room.on(RoomEvent.Connected, () => {\n        _this8.is_connected.next(true);\n        _this8.calculateTotalViewers();\n        if (_this8.room.metadata) {\n          let metadata = JSON.parse(_this8.room.metadata);\n          console.log('connected to room', _this8.room.name, metadata);\n          _this8.roomMetadataSubject.next(metadata);\n        }\n      });\n      _this8.room.on(RoomEvent.Disconnected, () => {\n        _this8.is_connected.next(false);\n        _this8.is_publishing.next(false);\n        _this8.checkAllUsersOut();\n        console.log('disconnected from room', _this8.room.name);\n      });\n      _this8.room.on(RoomEvent.RoomMetadataChanged, metadata => {\n        metadata = JSON.parse(metadata);\n        console.log('room metadata changed', metadata);\n        _this8.roomMetadataSubject.next(metadata);\n      });\n      _this8.room.on(RoomEvent.ParticipantConnected, participant => {\n        console.log('participant connected', participant.identity);\n        _this8.calculateTotalViewers();\n      });\n      _this8.room.on(RoomEvent.ParticipantDisconnected, participant => {\n        console.log('participant disconnected', participant.identity);\n        _this8.checkAllUsersOut();\n        _this8.calculateTotalViewers();\n        // remove video element\n        let video_id = `${participant.identity} - video`;\n        let videoElement = document.getElementById(video_id);\n        if (videoElement) {\n          videoElement.remove();\n        }\n        // remove audio element\n        let audio_id = `${participant.identity} - audio`;\n        let audioElement = document.getElementById(audio_id);\n        if (audioElement) {\n          audioElement.remove();\n        }\n      });\n      _this8.subscribeToTracks();\n      _this8.subcribeTracksPublished();\n      _this8.listenMessages();\n      // join a room\n      yield _this8.room.connect(wsURL, token);\n      // log local participant\n      console.log('local participant', _this8.room.localParticipant.sid);\n    })();\n  }\n  listenMessages() {\n    this.room.on(RoomEvent.DataReceived, (payload, participant, kind) => {\n      const strData = this.decoder.decode(payload);\n      console.log('Received message:', strData);\n    });\n  }\n  sendMessage(message) {\n    const data = this.encoder.encode(message);\n    this.room.localParticipant.publishData(data);\n  }\n  checkAllUsersOut() {\n    // this.room.remoteParticipants.size == 1 and user is User-record\n    console.log('remote participants', this.room.remoteParticipants.size);\n    if (this.room.remoteParticipants.size == 1 && this.room.localParticipant.identity == 'User-record') {\n      // outroom\n      this.room.disconnect();\n      // delete room\n      this.deleteRoom(this.room.name).subscribe(res => {\n        console.log('room deleted', res);\n      });\n    }\n  }\n  calculateTotalViewers() {\n    this.total_viewers.next(this.room.remoteParticipants.size);\n  }\n  publishTracks() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      let videoPublication = yield _this9.publishVideoTrack();\n      let audioPublication = yield _this9.publishAudioTrack();\n      _this9.is_publishing.next(true);\n      return {\n        videoPublication,\n        audioPublication\n      };\n    })();\n  }\n  publishVideoTrack() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this10.current_video_track) {\n        _this10.current_video_track = yield _this10.createLocalVideoTrack();\n      }\n      const videoPublication = yield _this10.room.localParticipant.publishTrack(_this10.current_video_track, _this10.videoPublishOptions);\n      return videoPublication;\n    })();\n  }\n  unpublishVideoTrack() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      if (_this11.current_video_track) {\n        _this11.room.localParticipant.unpublishTrack(_this11.current_video_track);\n      }\n    })();\n  }\n  publishAudioTrack() {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this12.current_audio_track) {\n        _this12.current_audio_track = yield createLocalAudioTrack();\n      }\n      const audioPublication = yield _this12.room.localParticipant.publishTrack(_this12.current_audio_track);\n      return audioPublication;\n    })();\n  }\n  unPublishAudioTrack() {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      if (_this13.current_audio_track) {\n        _this13.room.localParticipant.unpublishTrack(_this13.current_audio_track);\n      }\n    })();\n  }\n  muteAudio() {\n    if (this.current_audio_track && !this.current_audio_track.isMuted) {\n      this.current_audio_track.mute();\n    }\n  }\n  unmuteAudio() {\n    if (this.current_audio_track && this.current_audio_track.isMuted) {\n      this.current_audio_track.unmute();\n    }\n  }\n  muteVideo() {\n    if (this.current_video_track && !this.current_video_track.isMuted) {\n      this.current_video_track.mute();\n    }\n  }\n  unmuteVideo() {\n    if (this.current_video_track && this.current_video_track.isMuted) {\n      this.current_video_track.unmute();\n    }\n  }\n  toggleMuteVideo() {\n    if (this.current_video_track) {\n      if (this.current_video_track.isMuted) {\n        this.current_video_track.unmute();\n      } else {\n        this.current_video_track.mute();\n      }\n    }\n  }\n  toggleMuteAudio() {\n    if (this.current_audio_track) {\n      if (this.current_audio_track.isMuted) {\n        this.current_audio_track.unmute();\n      } else {\n        this.current_audio_track.mute();\n      }\n    }\n  }\n  unPublishTracks() {\n    if (this.current_video_track) {\n      this.room.localParticipant.unpublishTrack(this.current_video_track);\n    }\n    if (this.current_audio_track) {\n      this.room.localParticipant.unpublishTrack(this.current_audio_track);\n    }\n  }\n  // end room\n  endRoom() {\n    this.roomMetadataSubject.unsubscribe();\n    this.room.disconnect();\n  }\n  createLocalTracks(elm_id = 'livekit-publisher-video') {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      let videoOptions = _this14.localVideoInitOptions;\n      if (_this14.current_camera) {\n        videoOptions.deviceId = _this14.current_camera.deviceId;\n        // delete facingMode\n        delete videoOptions.facingMode;\n      }\n      let audioOptions = _this14.localAudioInitOptions;\n      if (_this14.current_microphone) {\n        audioOptions.deviceId = _this14.current_microphone.deviceId;\n      }\n      _this14.stopCurrentStream();\n      // wait 0.8 second\n      yield new Promise(resolve => setTimeout(resolve, 800));\n      let tracks = yield createLocalTracks({\n        audio: audioOptions,\n        video: videoOptions\n      }).catch(error => {\n        console.log(MediaDeviceFailure.getFailure(error));\n      });\n      if (!tracks) {\n        return {\n          videoTrack: null,\n          audioTrack: null\n        };\n      }\n      const videoTrack = tracks.find(t => t.kind === Track.Kind.Video);\n      const audioTrack = tracks.find(t => t.kind === Track.Kind.Audio);\n      _this14.current_video_track = videoTrack;\n      _this14.current_audio_track = audioTrack;\n      // attach to video element\n      const videoElement = document.getElementById(elm_id);\n      videoTrack.attach(videoElement);\n      return {\n        videoTrack,\n        audioTrack\n      };\n    })();\n  }\n  // create local video track\n  createLocalVideoTrack(elm_id = 'livekit-publisher-video') {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      let videoOptions = _this15.localVideoInitOptions;\n      if (_this15.current_camera) {\n        videoOptions.deviceId = _this15.current_camera.deviceId;\n        // delete facingMode\n        delete videoOptions.facingMode;\n      }\n      _this15.stopVideoTrack();\n      let videoTrack = yield createLocalVideoTrack(videoOptions).catch(error => {\n        console.log(MediaDeviceFailure.getFailure(error));\n      });\n      if (!videoTrack) {\n        return null;\n      }\n      _this15.current_video_track = videoTrack;\n      // attach to video element\n      const videoElement = document.getElementById(elm_id);\n      videoTrack.attach(videoElement);\n      return videoTrack;\n    })();\n  }\n  createLocalAudioTrack() {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      let audioOptions = {\n        echoCancellation: true,\n        noiseSuppression: true,\n        autoGainControl: true\n      };\n      if (_this16.current_microphone) {\n        audioOptions.deviceId = _this16.current_microphone.deviceId;\n      }\n      _this16.stopAudioTrack();\n      let audioTrack = yield createLocalAudioTrack(audioOptions).catch(error => {\n        console.log(MediaDeviceFailure.getFailure(error));\n      });\n      if (!audioTrack) {\n        return null;\n      }\n      _this16.current_audio_track = audioTrack;\n      return audioTrack;\n    })();\n  }\n  subscribeToTracks() {\n    var _this17 = this;\n    return _asyncToGenerator(function* () {\n      _this17.room.on(RoomEvent.TrackSubscribed, _this17.handleTrackSubscribed.bind(_this17));\n    })();\n  }\n  handleTrackSubscribed(track, publication, participant) {\n    console.log('track subscribed', track.sid, publication.trackSid, participant.sid);\n    this.attachTrack(track, participant);\n  }\n  subcribeTracksPublished() {\n    this.room.on(RoomEvent.TrackPublished, (publication, participant) => {\n      console.log('track published', publication.trackSid, participant.identity);\n      publication.setSubscribed(true);\n    });\n    // also subscribe to tracks published before participant joined\n    this.room.remoteParticipants.forEach(participant => {\n      participant.trackPublications.forEach(publication => {\n        console.log('existing publication', publication.trackSid);\n        publication.setSubscribed(true);\n        // attach to video element\n        const track = publication.track;\n        if (track) {\n          this.attachTrack(track, participant);\n        }\n      });\n    });\n  }\n  attachTrack(track, participant, elementId = 'livekit-subscriber-video') {\n    // let parentElement = document.getElementById(elementId) as HTMLElement;\n    // const element = track.attach();\n    // parentElement.appendChild(element);\n    if (track.kind === Track.Kind.Video) {\n      let video_id = `${participant.identity} - video`;\n      console.log('attach track', track.sid, elementId);\n      // find video element by participant identity if exists remove it\n      let videoElement = document.getElementById(video_id);\n      if (videoElement) {\n        videoElement.remove();\n      }\n      let parentElement = document.getElementById(elementId);\n      if (!parentElement) {\n        console.log('not found \"livekit-subscriber-video\" element');\n        return;\n      }\n      /* do things with track, publication or participant */\n      const element = document.createElement('video');\n      element.id = video_id;\n      element.autoplay = true;\n      element.muted = false;\n      element.playsInline = true;\n      // element.controls = true;\n      // set width 100% and height auto\n      element.style.width = '100%';\n      element.style.height = 'auto';\n      // add class to video element\n      element.classList.add('livekit-video-subscriber');\n      track.attach(element);\n      parentElement.appendChild(element);\n    } else if (track.kind === Track.Kind.Audio) {\n      // add track to audio element\n      let audio_id = `${participant.identity} - audio`;\n      console.log('attach track', track.sid, elementId);\n      // find audio element by participant identity if exists remove it\n      let audioElement = document.getElementById(audio_id);\n      if (audioElement) {\n        audioElement.remove();\n      }\n      let parentElement = document.getElementById(elementId);\n      if (!parentElement) {\n        console.log('not found \"livekit-subscriber-video\" element');\n        return;\n      }\n      /* do things with track, publication or participant */\n      const element = document.createElement('audio');\n      element.id = `${participant.identity} - audio`;\n      element.autoplay = true;\n      track.attach(element);\n      parentElement.appendChild(element);\n    }\n  }\n  generateToken(room_id, user_id = null) {\n    let data = {\n      room_id,\n      user_id\n    };\n    if (!user_id) {\n      delete data.user_id;\n    }\n    // generate token\n    return this.http.post(`${environment.apiUrl}/livekit/generate-token`, data).pipe(map(res => res.token));\n  }\n  // get list of rooms\n  getRooms() {\n    return this.http.get(`${environment.apiUrl}/livekit/list-rooms`);\n  }\n  // delete room\n  deleteRoom(room_id) {\n    this.roomMetadataSubject.unsubscribe();\n    return this.http.post(`${environment.apiUrl}/livekit/delete-room`, {\n      room_id\n    });\n  }\n  stopEgressRoom(egressID) {\n    return this.http.post(`${environment.apiUrl}/livekit/stop-egress`, {\n      egressID\n    });\n  }\n  setParticipantMetadata(data) {\n    this.room.localParticipant.setMetadata(data);\n  }\n  // update-room-metadata\n  updateRoomMetadata(room_id, metadata) {\n    return this.http.post(`${environment.apiUrl}/livekit/update-room-metadata`, {\n      room_id,\n      metadata\n    }).pipe(map(res => res));\n  }\n  startWebEgress(token, host_url = environment.ezstreamUrl) {\n    // generate token\n    return this.http.post(`${environment.apiUrl}/livekit/start-web-egress`, {\n      token,\n      host_url\n    });\n  }\n  startWebEgressHLS(token, host_url = environment.ezstreamUrl) {\n    // generate token\n    return this.http.post(`${environment.apiUrl}/livekit/start-web-egress-hls`, {\n      token,\n      host_url\n    });\n  }\n  startWebEgressRTMP(token, host_url = environment.ezstreamUrl) {\n    // generate token\n    return this.http.post(`${environment.apiUrl}/livekit/start-web-egress-rtmp`, {\n      token,\n      host_url\n    });\n  }\n  syncTimerCountUp(metadata) {\n    if (!metadata) return;\n    let is_running = metadata.overlay_data.is_running_timer;\n    let timer_started_at = metadata.overlay_data.timer_started_at;\n    let timer_stoped_at = metadata.overlay_data.timer_stoped_at;\n    let current_time = this.worldTimeService.time;\n    let timer_duration = metadata.overlay_data.timer_duration;\n    if (timer_started_at == 0 && timer_stoped_at == 0 && !is_running) {\n      this.overlaysService.setTimer('00:00');\n      return;\n    }\n    let time = 0;\n    if (timer_stoped_at > timer_started_at) {\n      time = timer_stoped_at - timer_started_at + timer_duration;\n    } else {\n      time = current_time - timer_started_at + timer_duration;\n    }\n    // use moment to format time\n    let time_seconds = moment.duration(time, 'seconds');\n    let hours = time_seconds.hours();\n    let minutes = time_seconds.minutes();\n    let seconds = time_seconds.seconds();\n    // if hours > 0, add hours to minutes\n    if (hours > 0) {\n      minutes += hours * 60;\n    }\n    this.match_seconds = time;\n    this.setStringTime(minutes, seconds);\n  }\n  setStringTime(minutes, seconds) {\n    let formatted_time = `${formatNumber(minutes, 'en', '2.0-0')}:${formatNumber(seconds, 'en', '2.0-0')}`;\n    this.overlaysService.setTimer(formatted_time);\n  }\n  static #_ = this.ɵfac = function LivekitService_Factory(t) {\n    return new (t || LivekitService)(i0.ɵɵinject(i1.WorldTimeService), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.OverlaysService), i0.ɵɵinject(i4.SettingsService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LivekitService,\n    factory: LivekitService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AAEA,SAMEA,kBAAkB,EAKlBC,IAAI,EACJC,SAAS,EACTC,KAAK,EAELC,YAAY,EACZC,qBAAqB,EACrBC,iBAAiB,EACjBC,qBAAqB,QAChB,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,kCAAkC;AAG9D,SAASC,GAAG,QAAQ,gBAAgB;AAGpC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,YAAY,QAAQ,iBAAiB;;;;;;AAO9C,OAAM,MAAOC,cAAc;EAqCzBC,YACSC,gBAAkC,EAClCC,IAAgB,EAChBC,eAAgC,EAC/BC,gBAAiC;IAHlC,KAAAH,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,eAAe,GAAfA,eAAe;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAxC1B,KAAAC,WAAW,GAAsB,EAAE;IACnC,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAC,OAAO,GAAG,IAAIb,OAAO,EAAqB;IAC1C,KAAAc,WAAW,GAAG,IAAId,OAAO,EAAqB;IAC9C,KAAAe,cAAc,GAA2B,IAAI;IAC7C,KAAAC,kBAAkB,GAA2B,IAAI;IAEjD,KAAAC,OAAO,GAAG,IAAIC,WAAW,EAAE;IAC3B,KAAAC,OAAO,GAAG,IAAIC,WAAW,EAAE;IAC3B,KAAAC,mBAAmB,GAA2B,IAAI;IAClD,KAAAC,mBAAmB,GAA2B,IAAI;IAClD,KAAAC,YAAY,GAAqB,IAAIvB,OAAO,EAAW;IACvD,KAAAwB,aAAa,GAAqB,IAAIxB,OAAO,EAAW;IACxD,KAAAyB,aAAa,GAAoB,IAAIzB,OAAO,EAAU;IACtD,KAAA0B,qBAAqB,GAAwB;MAC3CC,UAAU,EAAE,aAAa;MACzBC,UAAU,EAAEhC,YAAY,CAACiC;KAC1B;IACD,KAAAC,kBAAkB,GAA2B,IAAI;IACjD,KAAAC,qBAAqB,GAAwB;MAC3CC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,eAAe,EAAE;KAClB;IAED,KAAAC,mBAAmB,GAAQ;MACzBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE;QACbC,UAAU,EAAE;;KAEf;IACD,KAAAC,mBAAmB,GAA0B,IAAIxC,OAAO,EAAgB;IAExE,KAAAyC,aAAa,GAAG,CAAC;IAQf,IAAI,CAACC,IAAI,GAAG,IAAIjD,IAAI,CAAC;MACnBkD,cAAc,EAAE;KACjB,CAAC;IACF,IAAI,CAACH,mBAAmB,CAACI,SAAS,CAAEC,QAAQ,IAAI;MAC9C,IAAI,CAACC,mBAAmB,GAAGD,QAAQ;IACrC,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEME,uBAAuBA,CAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAIC,SAAS,CAACC,YAAY,IAAID,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;QACjE;QACA,aAAaF,SAAS,CAACC,YAAY,CAChCC,YAAY,CAAC;UAAEC,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC,CAC1CC,IAAI,CAAC,UAAUC,MAAM;UACpBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,MAAM,CAAC;UAC3C;UACAG,UAAU,CAAC,MAAK;YACdH,MAAM,CAACI,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAI;cACnCA,KAAK,CAACC,IAAI,EAAE;YACd,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;UACR,OAAOP,MAAM;QACf,CAAC,CAAC,CACDQ,KAAK,CAAC,UAAUC,KAAK;UACpB;UACAR,OAAO,CAACQ,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC7C,CAAC,CAAC;OACL,MAAM;QACLR,OAAO,CAACQ,KAAK,CAAC,+BAA+B,CAAC;;IAC/C;EACH;EAEMC,iBAAiBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAlB,iBAAA;MACrB,MAAMkB,KAAI,CAACpE,iBAAiB,EAAE;MAC9B,MAAMoE,KAAI,CAACC,gBAAgB,EAAE;MAC7B,MAAMD,KAAI,CAACE,YAAY,EAAE;IAAC;EAC5B;EAEA;EACMA,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArB,iBAAA;MAChB,IAAIsB,YAAY,GAAwBD,MAAI,CAAC3C,qBAAqB;MAClE,IAAI2C,MAAI,CAACtD,cAAc,EAAE;QACvBuD,YAAY,CAACC,QAAQ,GAAGF,MAAI,CAACtD,cAAc,CAACwD,QAAQ;QACpD;QACA,OAAOD,YAAY,CAAC3C,UAAU;QAC9B,MAAM0C,MAAI,CAAChD,mBAAmB,CAACmD,YAAY,CAACF,YAAY,CAAC;;MAE3D,OAAOD,MAAI,CAACtD,cAAc;IAAC;EAC7B;EAEA;EACMoD,gBAAgBA,CAAA;IAAA,IAAAM,MAAA;IAAA,OAAAzB,iBAAA;MACpB,IAAI0B,YAAY,GAAwBD,MAAI,CAAC1C,qBAAqB;MAClE,IAAI0C,MAAI,CAACzD,kBAAkB,EAAE;QAC3B0D,YAAY,CAACH,QAAQ,GAAGE,MAAI,CAACzD,kBAAkB,CAACuD,QAAQ;QACxD,MAAME,MAAI,CAACnD,mBAAmB,CAACkD,YAAY,CAACE,YAAY,CAAC;;MAE3D,OAAOD,MAAI,CAACzD,kBAAkB;IAAC;EACjC;EAEM2D,gBAAgBA,CAACJ,QAAgB;IAAA,IAAAK,MAAA;IAAA,OAAA5B,iBAAA;MACrC,MAAM4B,MAAI,CAAClC,IAAI,CACZmC,kBAAkB,CAAC,YAAY,EAAEN,QAAQ,CAAC,CAC1CR,KAAK,CAAEC,KAAK,IAAI;QACfR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEO,KAAK,CAAC;MAChD,CAAC,CAAC;IAAC;EACP;EAEMc,gBAAgBA,CAACP,QAAgB;IAAA,IAAAQ,MAAA;IAAA,OAAA/B,iBAAA;MACrC,IAAII,KAAK,SAAS2B,MAAI,CAACrC,IAAI,CACxBmC,kBAAkB,CAAC,YAAY,EAAEN,QAAQ,CAAC,CAC1CR,KAAK,CAAEC,KAAK,IAAI;QACfR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEO,KAAK,CAAC;MAChD,CAAC,CAAC;MACJR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEL,KAAK,CAAC;MAExC;IAAA;EACF;;EAEA4B,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAD,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC3D,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACwC,IAAI,EAAE;;EAEnC;EAEAoB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC7D,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACyC,IAAI,EAAE;;EAEnC;EAEA;EACMqB,eAAeA,CAAA;IAAA,OAAAnC,iBAAA;MACnB,MAAMoC,OAAO,SAASnC,SAAS,CAACC,YAAY,CAACmC,gBAAgB,EAAE;MAC/D,OAAOD,OAAO;IAAC;EACjB;EAEA;EACME,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvC,iBAAA;MACd,MAAMoC,OAAO,SAASG,MAAI,CAACJ,eAAe,EAAE;MAC5C,OAAOC,OAAO,CAACI,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAK,YAAY,CAAC;IAAC;EACxD;EAEA;EACMC,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5C,iBAAA;MAClB,MAAMoC,OAAO,SAASQ,MAAI,CAACT,eAAe,EAAE;MAC5C,OAAOC,OAAO,CAACI,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAK,YAAY,CAAC;IAAC;EACxD;EAGMG,QAAQA,CAACC,KAAa,EAAEC,KAAA,GAAgBC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC,CAACJ,KAAK;IAAA,IAAAK,MAAA;IAAA,OAAApD,iBAAA;MAE3FoD,MAAI,CAAC1D,IAAI,CAAC2D,EAAE,CAAC3G,SAAS,CAAC4G,SAAS,EAAE,MAAK;QACrCF,MAAI,CAAC7E,YAAY,CAACgF,IAAI,CAAC,IAAI,CAAC;QAC5BH,MAAI,CAACI,qBAAqB,EAAE;QAC5B,IAAIJ,MAAI,CAAC1D,IAAI,CAACG,QAAQ,EAAE;UACtB,IAAIA,QAAQ,GAAGmD,IAAI,CAACC,KAAK,CAACG,MAAI,CAAC1D,IAAI,CAACG,QAAQ,CAAQ;UACpDW,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2C,MAAI,CAAC1D,IAAI,CAAC+D,IAAI,EAAE5D,QAAQ,CAAC;UAC1DuD,MAAI,CAAC5D,mBAAmB,CAAC+D,IAAI,CAAC1D,QAAQ,CAAC;;MAE3C,CAAC,CAAC;MACFuD,MAAI,CAAC1D,IAAI,CAAC2D,EAAE,CAAC3G,SAAS,CAACgH,YAAY,EAAE,MAAK;QACxCN,MAAI,CAAC7E,YAAY,CAACgF,IAAI,CAAC,KAAK,CAAC;QAC7BH,MAAI,CAAC5E,aAAa,CAAC+E,IAAI,CAAC,KAAK,CAAC;QAC9BH,MAAI,CAACO,gBAAgB,EAAE;QACvBnD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2C,MAAI,CAAC1D,IAAI,CAAC+D,IAAI,CAAC;MACvD,CAAC,CAAC;MAIJL,MAAI,CAAC1D,IAAI,CAAC2D,EAAE,CAAC3G,SAAS,CAACkH,mBAAmB,EAAG/D,QAAa,IAAI;QAC5DA,QAAQ,GAAGmD,IAAI,CAACC,KAAK,CAACpD,QAAQ,CAAQ;QACtCW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEZ,QAAQ,CAAC;QAC9CuD,MAAI,CAAC5D,mBAAmB,CAAC+D,IAAI,CAAC1D,QAAQ,CAAC;MACzC,CAAC,CAAC;MAEFuD,MAAI,CAAC1D,IAAI,CAAC2D,EAAE,CAAC3G,SAAS,CAACmH,oBAAoB,EAAGC,WAAW,IAAI;QAC3DtD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqD,WAAW,CAACC,QAAQ,CAAC;QAC1DX,MAAI,CAACI,qBAAqB,EAAE;MAC9B,CAAC,CAAC;MAEFJ,MAAI,CAAC1D,IAAI,CAAC2D,EAAE,CAAC3G,SAAS,CAACsH,uBAAuB,EAAGF,WAAW,IAAI;QAC9DtD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEqD,WAAW,CAACC,QAAQ,CAAC;QAC7DX,MAAI,CAACO,gBAAgB,EAAE;QACvBP,MAAI,CAACI,qBAAqB,EAAE;QAC5B;QACA,IAAIS,QAAQ,GAAG,GAAGH,WAAW,CAACC,QAAQ,UAAU;QAChD,IAAIG,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAACH,QAAQ,CAAC;QACpD,IAAIC,YAAY,EAAE;UAChBA,YAAY,CAACG,MAAM,EAAE;;QAEvB;QACA,IAAIC,QAAQ,GAAG,GAAGR,WAAW,CAACC,QAAQ,UAAU;QAChD,IAAIQ,YAAY,GAAGJ,QAAQ,CAACC,cAAc,CAACE,QAAQ,CAAC;QACpD,IAAIC,YAAY,EAAE;UAChBA,YAAY,CAACF,MAAM,EAAE;;MAEzB,CAAC,CAAC;MACFjB,MAAI,CAACoB,iBAAiB,EAAE;MACxBpB,MAAI,CAACqB,uBAAuB,EAAE;MAC9BrB,MAAI,CAACsB,cAAc,EAAE;MACrB;MACA,MAAMtB,MAAI,CAAC1D,IAAI,CAACiF,OAAO,CAAC5B,KAAK,EAAED,KAAK,CAAC;MACrC;MACAtC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2C,MAAI,CAAC1D,IAAI,CAACkF,gBAAgB,CAACC,GAAG,CAAC;IAAC;EACnE;EAEAH,cAAcA,CAAA;IACZ,IAAI,CAAChF,IAAI,CAAC2D,EAAE,CACV3G,SAAS,CAACoI,YAAY,EACtB,CACEC,OAAmB,EACnBjB,WAAwB,EACxBpB,IAAqB,KACnB;MACF,MAAMsC,OAAO,GAAG,IAAI,CAAC7G,OAAO,CAAC8G,MAAM,CAACF,OAAO,CAAC;MAC5CvE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEuE,OAAO,CAAC;IAC3C,CAAC,CACF;EACH;EAEAE,WAAWA,CAACC,OAAe;IACzB,MAAMC,IAAI,GAAG,IAAI,CAACnH,OAAO,CAACoH,MAAM,CAACF,OAAO,CAAC;IACzC,IAAI,CAACzF,IAAI,CAACkF,gBAAgB,CAACU,WAAW,CAACF,IAAI,CAAC;EAC9C;EAEAzB,gBAAgBA,CAAA;IACd;IACAnD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACf,IAAI,CAAC6F,kBAAkB,CAACC,IAAI,CAAC;IACrE,IACE,IAAI,CAAC9F,IAAI,CAAC6F,kBAAkB,CAACC,IAAI,IAAI,CAAC,IACtC,IAAI,CAAC9F,IAAI,CAACkF,gBAAgB,CAACb,QAAQ,IAAI,aAAa,EACpD;MACA;MACA,IAAI,CAACrE,IAAI,CAAC+F,UAAU,EAAE;MACtB;MACA,IAAI,CAACC,UAAU,CAAC,IAAI,CAAChG,IAAI,CAAC+D,IAAI,CAAC,CAAC7D,SAAS,CAAE+F,GAAG,IAAI;QAChDnF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkF,GAAG,CAAC;MAClC,CAAC,CAAC;;EAEN;EAEAnC,qBAAqBA,CAAA;IACnB,IAAI,CAAC/E,aAAa,CAAC8E,IAAI,CAAC,IAAI,CAAC7D,IAAI,CAAC6F,kBAAkB,CAACC,IAAI,CAAC;EAC5D;EAEMI,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7F,iBAAA;MACjB,IAAI8F,gBAAgB,SAASD,MAAI,CAACE,iBAAiB,EAAE;MACrD,IAAIC,gBAAgB,SAASH,MAAI,CAACI,iBAAiB,EAAE;MACrDJ,MAAI,CAACrH,aAAa,CAAC+E,IAAI,CAAC,IAAI,CAAC;MAC7B,OAAO;QAAEuC,gBAAgB;QAAEE;MAAgB,CAAE;IAAC;EAChD;EAEMD,iBAAiBA,CAAA;IAAA,IAAAG,OAAA;IAAA,OAAAlG,iBAAA;MACrB,IAAI,CAACkG,OAAI,CAAC7H,mBAAmB,EAAE;QAC7B6H,OAAI,CAAC7H,mBAAmB,SAAS6H,OAAI,CAACnJ,qBAAqB,EAAE;;MAE/D,MAAM+I,gBAAgB,SAASI,OAAI,CAACxG,IAAI,CAACkF,gBAAgB,CAACuB,YAAY,CACpED,OAAI,CAAC7H,mBAAmB,EACxB6H,OAAI,CAAC/G,mBAAmB,CACzB;MACD,OAAO2G,gBAAgB;IAAC;EAC1B;EAEMM,mBAAmBA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAArG,iBAAA;MACvB,IAAIqG,OAAI,CAAChI,mBAAmB,EAAE;QAC5BgI,OAAI,CAAC3G,IAAI,CAACkF,gBAAgB,CAAC0B,cAAc,CAACD,OAAI,CAAChI,mBAAmB,CAAC;;IACpE;EACH;EAEM4H,iBAAiBA,CAAA;IAAA,IAAAM,OAAA;IAAA,OAAAvG,iBAAA;MACrB,IAAI,CAACuG,OAAI,CAACjI,mBAAmB,EAAE;QAC7BiI,OAAI,CAACjI,mBAAmB,SAASzB,qBAAqB,EAAE;;MAE1D,MAAMmJ,gBAAgB,SAASO,OAAI,CAAC7G,IAAI,CAACkF,gBAAgB,CAACuB,YAAY,CACpEI,OAAI,CAACjI,mBAAmB,CACzB;MACD,OAAO0H,gBAAgB;IAAC;EAC1B;EAEMQ,mBAAmBA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAzG,iBAAA;MACvB,IAAIyG,OAAI,CAACnI,mBAAmB,EAAE;QAC5BmI,OAAI,CAAC/G,IAAI,CAACkF,gBAAgB,CAAC0B,cAAc,CAACG,OAAI,CAACnI,mBAAmB,CAAC;;IACpE;EACH;EAEAoI,SAASA,CAAA;IACP,IAAI,IAAI,CAACpI,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAACqI,OAAO,EAAE;MACjE,IAAI,CAACrI,mBAAmB,CAACsI,IAAI,EAAE;;EAEnC;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvI,mBAAmB,IAAI,IAAI,CAACA,mBAAmB,CAACqI,OAAO,EAAE;MAChE,IAAI,CAACrI,mBAAmB,CAACwI,MAAM,EAAE;;EAErC;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1I,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAACsI,OAAO,EAAE;MACjE,IAAI,CAACtI,mBAAmB,CAACuI,IAAI,EAAE;;EAEnC;EAEAI,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC3I,mBAAmB,IAAI,IAAI,CAACA,mBAAmB,CAACsI,OAAO,EAAE;MAChE,IAAI,CAACtI,mBAAmB,CAACyI,MAAM,EAAE;;EAErC;EAEAG,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5I,mBAAmB,EAAE;MAC5B,IAAI,IAAI,CAACA,mBAAmB,CAACsI,OAAO,EAAE;QACpC,IAAI,CAACtI,mBAAmB,CAACyI,MAAM,EAAE;OAClC,MAAM;QACL,IAAI,CAACzI,mBAAmB,CAACuI,IAAI,EAAE;;;EAGrC;EAEAM,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5I,mBAAmB,EAAE;MAC5B,IAAI,IAAI,CAACA,mBAAmB,CAACqI,OAAO,EAAE;QACpC,IAAI,CAACrI,mBAAmB,CAACwI,MAAM,EAAE;OAClC,MAAM;QACL,IAAI,CAACxI,mBAAmB,CAACsI,IAAI,EAAE;;;EAGrC;EAEAO,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC9I,mBAAmB,EAAE;MAC5B,IAAI,CAACqB,IAAI,CAACkF,gBAAgB,CAAC0B,cAAc,CAAC,IAAI,CAACjI,mBAAmB,CAAC;;IAErE,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACoB,IAAI,CAACkF,gBAAgB,CAAC0B,cAAc,CAAC,IAAI,CAAChI,mBAAmB,CAAC;;EAEvE;EAEA;EACA8I,OAAOA,CAAA;IACL,IAAI,CAAC5H,mBAAmB,CAAC6H,WAAW,EAAE;IACtC,IAAI,CAAC3H,IAAI,CAAC+F,UAAU,EAAE;EACxB;EAEM3I,iBAAiBA,CAACwK,MAAM,GAAG,yBAAyB;IAAA,IAAAC,OAAA;IAAA,OAAAvH,iBAAA;MACxD,IAAIsB,YAAY,GAAwBiG,OAAI,CAAC7I,qBAAqB;MAClE,IAAI6I,OAAI,CAACxJ,cAAc,EAAE;QACvBuD,YAAY,CAACC,QAAQ,GAAGgG,OAAI,CAACxJ,cAAc,CAACwD,QAAQ;QACpD;QACA,OAAOD,YAAY,CAAC3C,UAAU;;MAGhC,IAAI+C,YAAY,GAAwB6F,OAAI,CAACxI,qBAAqB;MAElE,IAAIwI,OAAI,CAACvJ,kBAAkB,EAAE;QAC3B0D,YAAY,CAACH,QAAQ,GAAGgG,OAAI,CAACvJ,kBAAkB,CAACuD,QAAQ;;MAG1DgG,OAAI,CAACvF,iBAAiB,EAAE;MACxB;MACA,MAAM,IAAIwF,OAAO,CAAEC,OAAO,IAAK/G,UAAU,CAAC+G,OAAO,EAAE,GAAG,CAAC,CAAC;MACxD,IAAIC,MAAM,SAAS5K,iBAAiB,CAAC;QACnCuD,KAAK,EAAEqB,YAAY;QACnBtB,KAAK,EAAEkB;OACR,CAAC,CAACP,KAAK,CAAEC,KAAK,IAAI;QACjBR,OAAO,CAACC,GAAG,CAACjE,kBAAkB,CAACmL,UAAU,CAAC3G,KAAK,CAAC,CAAC;MACnD,CAAC,CAAC;MAEF,IAAI,CAAC0G,MAAM,EAAE;QACX,OAAO;UAAEE,UAAU,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAI,CAAE;;MAE/C,MAAMD,UAAU,GAAGF,MAAM,CAACI,IAAI,CAC3BC,CAAC,IAAKA,CAAC,CAACrF,IAAI,KAAK/F,KAAK,CAACqL,IAAI,CAACC,KAAK,CAChB;MACpB,MAAMJ,UAAU,GAAGH,MAAM,CAACI,IAAI,CAC3BC,CAAC,IAAKA,CAAC,CAACrF,IAAI,KAAK/F,KAAK,CAACqL,IAAI,CAACE,KAAK,CAChB;MACpBX,OAAI,CAAClJ,mBAAmB,GAAGuJ,UAAU;MACrCL,OAAI,CAACjJ,mBAAmB,GAAGuJ,UAAU;MACrC;MACA,MAAM3D,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAACkD,MAAM,CAAqB;MACxEM,UAAU,CAACO,MAAM,CAACjE,YAAY,CAAC;MAC/B,OAAO;QAAE0D,UAAU;QAAEC;MAAU,CAAE;IAAC;EACpC;EAEA;EACM9K,qBAAqBA,CAACuK,MAAM,GAAG,yBAAyB;IAAA,IAAAc,OAAA;IAAA,OAAApI,iBAAA;MAC5D,IAAIsB,YAAY,GAAwB8G,OAAI,CAAC1J,qBAAqB;MAClE,IAAI0J,OAAI,CAACrK,cAAc,EAAE;QACvBuD,YAAY,CAACC,QAAQ,GAAG6G,OAAI,CAACrK,cAAc,CAACwD,QAAQ;QACpD;QACA,OAAOD,YAAY,CAAC3C,UAAU;;MAEhCyJ,OAAI,CAAClG,cAAc,EAAE;MACrB,IAAI0F,UAAU,SAAS7K,qBAAqB,CAACuE,YAAY,CAAC,CAACP,KAAK,CAC7DC,KAAK,IAAI;QACRR,OAAO,CAACC,GAAG,CAACjE,kBAAkB,CAACmL,UAAU,CAAC3G,KAAK,CAAC,CAAC;MACnD,CAAC,CACF;MACD,IAAI,CAAC4G,UAAU,EAAE;QACf,OAAO,IAAI;;MAEbQ,OAAI,CAAC/J,mBAAmB,GAAGuJ,UAAU;MACrC;MACA,MAAM1D,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAACkD,MAAM,CAAqB;MACxEM,UAAU,CAACO,MAAM,CAACjE,YAAY,CAAC;MAC/B,OAAO0D,UAAU;IAAC;EACpB;EAEM/K,qBAAqBA,CAAA;IAAA,IAAAwL,OAAA;IAAA,OAAArI,iBAAA;MACzB,IAAI0B,YAAY,GAAwB;QACtC1C,gBAAgB,EAAE,IAAI;QACtBC,gBAAgB,EAAE,IAAI;QACtBC,eAAe,EAAE;OAClB;MACD,IAAImJ,OAAI,CAACrK,kBAAkB,EAAE;QAC3B0D,YAAY,CAACH,QAAQ,GAAG8G,OAAI,CAACrK,kBAAkB,CAACuD,QAAQ;;MAE1D8G,OAAI,CAACpG,cAAc,EAAE;MACrB,IAAI4F,UAAU,SAAShL,qBAAqB,CAAC6E,YAAY,CAAC,CAACX,KAAK,CAC7DC,KAAK,IAAI;QACRR,OAAO,CAACC,GAAG,CAACjE,kBAAkB,CAACmL,UAAU,CAAC3G,KAAK,CAAC,CAAC;MACnD,CAAC,CACF;MACD,IAAI,CAAC6G,UAAU,EAAE;QACf,OAAO,IAAI;;MAEbQ,OAAI,CAAC/J,mBAAmB,GAAGuJ,UAAU;MACrC,OAAOA,UAAU;IAAC;EACpB;EAEMrD,iBAAiBA,CAAA;IAAA,IAAA8D,OAAA;IAAA,OAAAtI,iBAAA;MACrBsI,OAAI,CAAC5I,IAAI,CAAC2D,EAAE,CACV3G,SAAS,CAAC6L,eAAe,EACzBD,OAAI,CAACE,qBAAqB,CAACC,IAAI,CAACH,OAAI,CAAC,CACtC;IAAC;EACJ;EAEAE,qBAAqBA,CACnB3H,KAAkB,EAClB6H,WAAmC,EACnC5E,WAA8B;IAE9BtD,OAAO,CAACC,GAAG,CACT,kBAAkB,EAClBI,KAAK,CAACgE,GAAG,EACT6D,WAAW,CAACC,QAAQ,EACpB7E,WAAW,CAACe,GAAG,CAChB;IACD,IAAI,CAAC+D,WAAW,CAAC/H,KAAK,EAAEiD,WAAW,CAAC;EACtC;EAEAW,uBAAuBA,CAAA;IACrB,IAAI,CAAC/E,IAAI,CAAC2D,EAAE,CAAC3G,SAAS,CAACmM,cAAc,EAAE,CAACH,WAAW,EAAE5E,WAAW,KAAI;MAClEtD,OAAO,CAACC,GAAG,CACT,iBAAiB,EACjBiI,WAAW,CAACC,QAAQ,EACpB7E,WAAW,CAACC,QAAQ,CACrB;MACD2E,WAAW,CAACI,aAAa,CAAC,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF;IACA,IAAI,CAACpJ,IAAI,CAAC6F,kBAAkB,CAAC3E,OAAO,CAAEkD,WAAW,IAAI;MACnDA,WAAW,CAACiF,iBAAiB,CAACnI,OAAO,CAAE8H,WAAW,IAAI;QACpDlI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiI,WAAW,CAACC,QAAQ,CAAC;QACzDD,WAAW,CAACI,aAAa,CAAC,IAAI,CAAC;QAC/B;QACA,MAAMjI,KAAK,GAAG6H,WAAW,CAAC7H,KAAK;QAC/B,IAAIA,KAAK,EAAE;UACT,IAAI,CAAC+H,WAAW,CAAC/H,KAAK,EAAEiD,WAAW,CAAC;;MAExC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA8E,WAAWA,CACT/H,KAAkB,EAClBiD,WAA8B,EAC9BkF,SAAA,GAAoB,0BAA0B;IAE9C;IACA;IACA;IACA,IAAInI,KAAK,CAAC6B,IAAI,KAAK/F,KAAK,CAACqL,IAAI,CAACC,KAAK,EAAE;MACnC,IAAIhE,QAAQ,GAAG,GAAGH,WAAW,CAACC,QAAQ,UAAU;MAChDvD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEI,KAAK,CAACgE,GAAG,EAAEmE,SAAS,CAAC;MACjD;MACA,IAAI9E,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAACH,QAAQ,CAAC;MACpD,IAAIC,YAAY,EAAE;QAChBA,YAAY,CAACG,MAAM,EAAE;;MAEvB,IAAI4E,aAAa,GAAG9E,QAAQ,CAACC,cAAc,CAAC4E,SAAS,CAAC;MACtD,IAAI,CAACC,aAAa,EAAE;QAClBzI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3D;;MAEF;MACA,MAAMyI,OAAO,GAAG/E,QAAQ,CAACgF,aAAa,CAAC,OAAO,CAAqB;MACnED,OAAO,CAACE,EAAE,GAAGnF,QAAQ;MACrBiF,OAAO,CAACG,QAAQ,GAAG,IAAI;MACvBH,OAAO,CAACI,KAAK,GAAG,KAAK;MACrBJ,OAAO,CAACK,WAAW,GAAG,IAAI;MAC1B;MACA;MACAL,OAAO,CAACM,KAAK,CAACC,KAAK,GAAG,MAAM;MAC5BP,OAAO,CAACM,KAAK,CAACE,MAAM,GAAG,MAAM;MAC7B;MACAR,OAAO,CAACS,SAAS,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACjD/I,KAAK,CAACsH,MAAM,CAACe,OAAO,CAAC;MACrBD,aAAa,CAACY,WAAW,CAACX,OAAO,CAAC;KACnC,MAAM,IAAIrI,KAAK,CAAC6B,IAAI,KAAK/F,KAAK,CAACqL,IAAI,CAACE,KAAK,EAAE;MAC1C;MACA,IAAI5D,QAAQ,GAAG,GAAGR,WAAW,CAACC,QAAQ,UAAU;MAChDvD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEI,KAAK,CAACgE,GAAG,EAAEmE,SAAS,CAAC;MACjD;MACA,IAAIzE,YAAY,GAAGJ,QAAQ,CAACC,cAAc,CAACE,QAAQ,CAAC;MACpD,IAAIC,YAAY,EAAE;QAChBA,YAAY,CAACF,MAAM,EAAE;;MAEvB,IAAI4E,aAAa,GAAG9E,QAAQ,CAACC,cAAc,CAAC4E,SAAS,CAAC;MACtD,IAAI,CAACC,aAAa,EAAE;QAClBzI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3D;;MAEF;MACA,MAAMyI,OAAO,GAAG/E,QAAQ,CAACgF,aAAa,CAAC,OAAO,CAAqB;MACnED,OAAO,CAACE,EAAE,GAAG,GAAGtF,WAAW,CAACC,QAAQ,UAAU;MAC9CmF,OAAO,CAACG,QAAQ,GAAG,IAAI;MACvBxI,KAAK,CAACsH,MAAM,CAACe,OAAO,CAAC;MACrBD,aAAa,CAACY,WAAW,CAACX,OAAO,CAAC;;EAEtC;EAEAY,aAAaA,CAACC,OAAe,EAAEC,OAAA,GAAkB,IAAI;IACnD,IAAI5E,IAAI,GAAG;MACT2E,OAAO;MACPC;KACD;IAED,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO5E,IAAI,CAAC4E,OAAO;;IAErB;IACA,OAAO,IAAI,CAACxM,IAAI,CACbyM,IAAI,CAAC,GAAGhN,WAAW,CAACiN,MAAM,yBAAyB,EAAE9E,IAAI,CAAC,CAC1D+E,IAAI,CAACjN,GAAG,CAAEyI,GAAQ,IAAKA,GAAG,CAAC7C,KAAK,CAAC,CAAC;EACvC;EAEA;EACAsH,QAAQA,CAAA;IACN,OAAO,IAAI,CAAC5M,IAAI,CAAC6M,GAAG,CAAC,GAAGpN,WAAW,CAACiN,MAAM,qBAAqB,CAAC;EAClE;EAEA;EACAxE,UAAUA,CAACqE,OAAe;IACxB,IAAI,CAACvK,mBAAmB,CAAC6H,WAAW,EAAE;IACtC,OAAO,IAAI,CAAC7J,IAAI,CAACyM,IAAI,CAAC,GAAGhN,WAAW,CAACiN,MAAM,sBAAsB,EAAE;MACjEH;KACD,CAAC;EACJ;EAEAO,cAAcA,CAACC,QAAgB;IAC7B,OAAO,IAAI,CAAC/M,IAAI,CAACyM,IAAI,CAAC,GAAGhN,WAAW,CAACiN,MAAM,sBAAsB,EAAE;MACjEK;KACD,CAAC;EACJ;EAEAC,sBAAsBA,CAACpF,IAAS;IAC9B,IAAI,CAAC1F,IAAI,CAACkF,gBAAgB,CAAC6F,WAAW,CAACrF,IAAI,CAAC;EAC9C;EAEA;EACAsF,kBAAkBA,CAACX,OAAe,EAAElK,QAAsB;IACxD,OAAO,IAAI,CAACrC,IAAI,CACbyM,IAAI,CAAC,GAAGhN,WAAW,CAACiN,MAAM,+BAA+B,EAAE;MAC1DH,OAAO;MACPlK;KACD,CAAC,CACDsK,IAAI,CAACjN,GAAG,CAAEyI,GAAQ,IAAKA,GAAG,CAAC,CAAC;EACjC;EAEAgF,cAAcA,CAAC7H,KAAa,EAAE8H,QAAA,GAAmB3N,WAAW,CAAC4N,WAAW;IACtE;IACA,OAAO,IAAI,CAACrN,IAAI,CAACyM,IAAI,CAAC,GAAGhN,WAAW,CAACiN,MAAM,2BAA2B,EAAE;MACtEpH,KAAK;MACL8H;KACD,CAAC;EACJ;EAEAE,iBAAiBA,CAAChI,KAAa,EAAE8H,QAAA,GAAmB3N,WAAW,CAAC4N,WAAW;IACzE;IACA,OAAO,IAAI,CAACrN,IAAI,CAACyM,IAAI,CACnB,GAAGhN,WAAW,CAACiN,MAAM,+BAA+B,EACpD;MACEpH,KAAK;MACL8H;KACD,CACF;EACH;EAEAG,kBAAkBA,CAChBjI,KAAa,EACb8H,QAAA,GAAmB3N,WAAW,CAAC4N,WAAW;IAE1C;IACA,OAAO,IAAI,CAACrN,IAAI,CAACyM,IAAI,CACnB,GAAGhN,WAAW,CAACiN,MAAM,gCAAgC,EACrD;MACEpH,KAAK;MACL8H;KACD,CACF;EACH;EAEAI,gBAAgBA,CAACnL,QAAQ;IACvB,IAAI,CAACA,QAAQ,EAAE;IACf,IAAIoL,UAAU,GAAGpL,QAAQ,CAACqL,YAAY,CAACC,gBAAgB;IACvD,IAAIC,gBAAgB,GAAGvL,QAAQ,CAACqL,YAAY,CAACE,gBAAgB;IAC7D,IAAIC,eAAe,GAAGxL,QAAQ,CAACqL,YAAY,CAACG,eAAe;IAC3D,IAAIC,YAAY,GAAG,IAAI,CAAC/N,gBAAgB,CAACgO,IAAI;IAC7C,IAAIC,cAAc,GAAG3L,QAAQ,CAACqL,YAAY,CAACM,cAAc;IACzD,IAAIJ,gBAAgB,IAAI,CAAC,IAAIC,eAAe,IAAI,CAAC,IAAI,CAACJ,UAAU,EAAE;MAChE,IAAI,CAACxN,eAAe,CAACgO,QAAQ,CAAC,OAAO,CAAC;MACtC;;IAGF,IAAIF,IAAI,GAAG,CAAC;IACZ,IAAIF,eAAe,GAAGD,gBAAgB,EAAE;MACtCG,IAAI,GAAGF,eAAe,GAAGD,gBAAgB,GAAGI,cAAc;KAC3D,MAAM;MACLD,IAAI,GAAGD,YAAY,GAAGF,gBAAgB,GAAGI,cAAc;;IAEzD;IACA,IAAIE,YAAY,GAAGvO,MAAM,CAACwO,QAAQ,CAACJ,IAAI,EAAE,SAAS,CAAC;IACnD,IAAIK,KAAK,GAAGF,YAAY,CAACE,KAAK,EAAE;IAChC,IAAIC,OAAO,GAAGH,YAAY,CAACG,OAAO,EAAE;IACpC,IAAIC,OAAO,GAAGJ,YAAY,CAACI,OAAO,EAAE;IACpC;IACA,IAAIF,KAAK,GAAG,CAAC,EAAE;MACbC,OAAO,IAAID,KAAK,GAAG,EAAE;;IAEvB,IAAI,CAACnM,aAAa,GAAG8L,IAAI;IACzB,IAAI,CAACQ,aAAa,CAACF,OAAO,EAAEC,OAAO,CAAC;EACtC;EAEAC,aAAaA,CAACF,OAAO,EAAEC,OAAO;IAC5B,IAAIE,cAAc,GAAG,GAAG5O,YAAY,CAClCyO,OAAO,EACP,IAAI,EACJ,OAAO,CACR,IAAIzO,YAAY,CAAC0O,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE;IAC3C,IAAI,CAACrO,eAAe,CAACgO,QAAQ,CAACO,cAAc,CAAC;EAC/C;EAAC,QAAAC,CAAA;qBAvqBU5O,cAAc,EAAA6O,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA;WAAdvP,cAAc;IAAAwP,OAAA,EAAdxP,cAAc,CAAAyP,IAAA;IAAAC,UAAA,EAFb;EAAM", "names": ["MediaDeviceFailure", "Room", "RoomEvent", "Track", "VideoPresets", "createLocalAudioTrack", "createLocalTracks", "createLocalVideoTrack", "Subject", "environment", "map", "moment", "formatNumber", "LivekitService", "constructor", "worldTimeService", "http", "overlaysService", "_settingsService", "listCameras", "listMicrophones", "cameras", "microphones", "current_camera", "current_microphone", "encoder", "TextEncoder", "decoder", "TextDecoder", "current_video_track", "current_audio_track", "is_connected", "is_publishing", "total_viewers", "localVideoInitOptions", "facingMode", "resolution", "h2160", "environment_camera", "localAudioInitOptions", "echoCancellation", "noiseSuppression", "autoGainControl", "videoPublishOptions", "simulcast", "videoCodec", "videoEncoding", "maxBitrate", "roomMetadataSubject", "match_seconds", "room", "adaptiveStream", "subscribe", "metadata", "currentRoomMetadata", "requestMediaPermissions", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "video", "audio", "then", "stream", "console", "log", "setTimeout", "getTracks", "for<PERSON>ach", "track", "stop", "catch", "error", "changeMediaDevice", "_this", "changeMicrophone", "changeCamera", "_this2", "videoOptions", "deviceId", "restartTrack", "_this3", "audioOptions", "switchAudioInput", "_this4", "switchActiveDevice", "switchVideoInput", "_this5", "stopCurrentStream", "stopAudioTrack", "stopVideoTrack", "getMediaDevices", "devices", "enumerateDevices", "getCameras", "_this6", "filter", "d", "kind", "getMicrophones", "_this7", "joinRoom", "token", "wsURL", "JSON", "parse", "localStorage", "getItem", "_this8", "on", "Connected", "next", "calculateTotalViewers", "name", "Disconnected", "checkAllUsersOut", "RoomMetadataChanged", "ParticipantConnected", "participant", "identity", "ParticipantDisconnected", "video_id", "videoElement", "document", "getElementById", "remove", "audio_id", "audioElement", "subscribeToTracks", "subcribeTracksPublished", "listenMessages", "connect", "localParticipant", "sid", "DataReceived", "payload", "strData", "decode", "sendMessage", "message", "data", "encode", "publishData", "remoteParticipants", "size", "disconnect", "deleteRoom", "res", "publishTracks", "_this9", "videoPublication", "publishVideoTrack", "audioPublication", "publishAudioTrack", "_this10", "publishTrack", "unpublishVideoTrack", "_this11", "unpublishTrack", "_this12", "unPublishAudioTrack", "_this13", "muteAudio", "isMuted", "mute", "unmuteAudio", "unmute", "muteVideo", "unmuteVideo", "toggleMuteVideo", "toggleMuteAudio", "unPublishTracks", "endRoom", "unsubscribe", "elm_id", "_this14", "Promise", "resolve", "tracks", "getFailure", "videoTrack", "audioTrack", "find", "t", "Kind", "Video", "Audio", "attach", "_this15", "_this16", "_this17", "TrackSubscribed", "handleTrackSubscribed", "bind", "publication", "trackSid", "attachTrack", "TrackPublished", "setSubscribed", "trackPublications", "elementId", "parentElement", "element", "createElement", "id", "autoplay", "muted", "playsInline", "style", "width", "height", "classList", "add", "append<PERSON><PERSON><PERSON>", "generateToken", "room_id", "user_id", "post", "apiUrl", "pipe", "getRooms", "get", "stopEgressRoom", "egressID", "setParticipantMetadata", "setMetadata", "updateRoomMetadata", "startWebEgress", "host_url", "ezstreamUrl", "startWebEgressHLS", "startWebEgressRTMP", "syncTimerCountUp", "is_running", "overlay_data", "is_running_timer", "timer_started_at", "timer_stoped_at", "current_time", "time", "timer_duration", "setTimer", "time_seconds", "duration", "hours", "minutes", "seconds", "setStringTime", "formatted_time", "_", "i0", "ɵɵinject", "i1", "WorldTimeService", "i2", "HttpClient", "i3", "OverlaysService", "i4", "SettingsService", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\livekit.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport {\r\n  AudioCaptureOptions,\r\n  DataPacket_Kind,\r\n  LocalAudioTrack,\r\n  LocalTrackPublication,\r\n  LocalVideoTrack,\r\n  MediaDeviceFailure,\r\n  Participant,\r\n  RemoteParticipant,\r\n  RemoteTrack,\r\n  RemoteTrackPublication,\r\n  Room,\r\n  RoomEvent,\r\n  Track,\r\n  VideoCaptureOptions,\r\n  VideoPresets,\r\n  createLocalAudioTrack,\r\n  createLocalTracks,\r\n  createLocalVideoTrack,\r\n} from 'livekit-client';\r\nimport { Subject } from 'rxjs';\r\nimport { environment } from './../../environments/environment';\r\nimport { Camera, CameraResultType } from '@capacitor/camera';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { map } from 'rxjs/operators';\r\nimport { OverlaysService } from 'app/components/overlays/overlays.service';\r\nimport { WorldTimeService } from './world-time.service';\r\nimport moment from 'moment';\r\nimport { formatNumber } from '@angular/common';\r\nimport { SettingsService } from './settings.service';\r\nimport { AppConfig } from 'app/app-config';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LivekitService {\r\n  listCameras: MediaDeviceInfo[] = [];\r\n  listMicrophones: MediaDeviceInfo[] = [];\r\n  cameras = new Subject<MediaDeviceInfo[]>();\r\n  microphones = new Subject<MediaDeviceInfo[]>();\r\n  current_camera: MediaDeviceInfo | null = null;\r\n  current_microphone: MediaDeviceInfo | null = null;\r\n  room: Room;\r\n  encoder = new TextEncoder();\r\n  decoder = new TextDecoder();\r\n  current_video_track: LocalVideoTrack | null = null;\r\n  current_audio_track: LocalAudioTrack | null = null;\r\n  is_connected: Subject<boolean> = new Subject<boolean>();\r\n  is_publishing: Subject<boolean> = new Subject<boolean>();\r\n  total_viewers: Subject<number> = new Subject<number>();\r\n  localVideoInitOptions: VideoCaptureOptions = {\r\n    facingMode: 'environment',\r\n    resolution: VideoPresets.h2160,\r\n  };\r\n  environment_camera: MediaDeviceInfo | null = null;\r\n  localAudioInitOptions: AudioCaptureOptions = {\r\n    echoCancellation: true,\r\n    noiseSuppression: true,\r\n    autoGainControl: true,\r\n  };\r\n\r\n  videoPublishOptions: any = {\r\n    simulcast: false,\r\n    videoCodec: 'av1',\r\n    videoEncoding: {\r\n      maxBitrate: 8500000,\r\n    },\r\n  };\r\n  roomMetadataSubject: Subject<RoomMetadata> = new Subject<RoomMetadata>();\r\n  currentRoomMetadata: RoomMetadata;\r\n  match_seconds = 0;\r\n  livekitConfig: any;\r\n  constructor(\r\n    public worldTimeService: WorldTimeService,\r\n    public http: HttpClient,\r\n    public overlaysService: OverlaysService,\r\n    private _settingsService: SettingsService\r\n  ) {\r\n    this.room = new Room({\r\n      adaptiveStream: true,\r\n    });\r\n    this.roomMetadataSubject.subscribe((metadata) => {\r\n      this.currentRoomMetadata = metadata;\r\n    });\r\n    // this.requestMediaPermissions().then((stream) => {\r\n    //   //   setTimeout(() => {\r\n    // this.getCameras().then((cameras) => {\r\n    //   if (cameras.length > 0) {\r\n    //     this.listCameras = cameras;\r\n    //     this.cameras.next(cameras);\r\n    //   }\r\n    // });\r\n    // this.getMicrophones().then((microphones) => {\r\n    //   if (microphones.length > 0) {\r\n    //     this.listMicrophones = microphones;\r\n    //     this.microphones.next(microphones);\r\n    //   }\r\n    // });\r\n    //   //   }, 1000);\r\n    // });\r\n  }\r\n\r\n  async requestMediaPermissions() {\r\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\r\n      // Request access to the camera\r\n      return await navigator.mediaDevices\r\n        .getUserMedia({ video: true, audio: true })\r\n        .then(function (stream) {\r\n          console.log('getUserMedia success', stream);\r\n          // stop the stream\r\n          setTimeout(() => {\r\n            stream.getTracks().forEach((track) => {\r\n              track.stop();\r\n            });\r\n          }, 1000);\r\n          return stream;\r\n        })\r\n        .catch(function (error) {\r\n          // User denied access, or an error occurred\r\n          console.error('getUserMedia error:', error);\r\n        });\r\n    } else {\r\n      console.error('getUserMedia is not supported');\r\n    }\r\n  }\r\n\r\n  async changeMediaDevice() {\r\n    await this.createLocalTracks();\r\n    await this.changeMicrophone();\r\n    await this.changeCamera();\r\n  }\r\n\r\n  // change camera\r\n  async changeCamera() {\r\n    let videoOptions: VideoCaptureOptions = this.localVideoInitOptions;\r\n    if (this.current_camera) {\r\n      videoOptions.deviceId = this.current_camera.deviceId;\r\n      // delete facingMode\r\n      delete videoOptions.facingMode;\r\n      await this.current_video_track.restartTrack(videoOptions);\r\n    }\r\n    return this.current_camera;\r\n  }\r\n\r\n  // change microphone\r\n  async changeMicrophone() {\r\n    let audioOptions: AudioCaptureOptions = this.localAudioInitOptions;\r\n    if (this.current_microphone) {\r\n      audioOptions.deviceId = this.current_microphone.deviceId;\r\n      await this.current_audio_track.restartTrack(audioOptions);\r\n    }\r\n    return this.current_microphone;\r\n  }\r\n\r\n  async switchAudioInput(deviceId: string) {\r\n    await this.room\r\n      .switchActiveDevice('audioinput', deviceId)\r\n      .catch((error) => {\r\n        console.log('switch audio input error', error);\r\n      });\r\n  }\r\n\r\n  async switchVideoInput(deviceId: string) {\r\n    let video = await this.room\r\n      .switchActiveDevice('videoinput', deviceId)\r\n      .catch((error) => {\r\n        console.log('switch video input error', error);\r\n      });\r\n    console.log('switch video input', video);\r\n\r\n    // attach to video element\r\n  }\r\n\r\n  stopCurrentStream() {\r\n    // stop tracks\r\n    this.stopAudioTrack();\r\n    this.stopVideoTrack();\r\n  }\r\n\r\n  stopAudioTrack() {\r\n    if (this.current_audio_track) {\r\n      this.current_audio_track.stop();\r\n    }\r\n  }\r\n\r\n  stopVideoTrack() {\r\n    if (this.current_video_track) {\r\n      this.current_video_track.stop();\r\n    }\r\n  }\r\n\r\n  // get media devices\r\n  async getMediaDevices() {\r\n    const devices = await navigator.mediaDevices.enumerateDevices();\r\n    return devices;\r\n  }\r\n\r\n  // get cameras\r\n  async getCameras() {\r\n    const devices = await this.getMediaDevices();\r\n    return devices.filter((d) => d.kind === 'videoinput');\r\n  }\r\n\r\n  // get microphones\r\n  async getMicrophones() {\r\n    const devices = await this.getMediaDevices();\r\n    return devices.filter((d) => d.kind === 'audioinput');\r\n  }\r\n\r\n\r\n  async joinRoom(token: string, wsURL: string = JSON.parse(localStorage.getItem('livekit')).wsURL) {\r\n      \r\n      this.room.on(RoomEvent.Connected, () => {\r\n        this.is_connected.next(true);\r\n        this.calculateTotalViewers();\r\n        if (this.room.metadata) {\r\n          let metadata = JSON.parse(this.room.metadata) as any;\r\n          console.log('connected to room', this.room.name, metadata);\r\n          this.roomMetadataSubject.next(metadata);\r\n        }\r\n      });\r\n      this.room.on(RoomEvent.Disconnected, () => {\r\n        this.is_connected.next(false);\r\n        this.is_publishing.next(false);\r\n        this.checkAllUsersOut();\r\n        console.log('disconnected from room', this.room.name);\r\n      });\r\n   \r\n    \r\n\r\n    this.room.on(RoomEvent.RoomMetadataChanged, (metadata: any) => {\r\n      metadata = JSON.parse(metadata) as any;\r\n      console.log('room metadata changed', metadata);\r\n      this.roomMetadataSubject.next(metadata);\r\n    });\r\n\r\n    this.room.on(RoomEvent.ParticipantConnected, (participant) => {\r\n      console.log('participant connected', participant.identity);\r\n      this.calculateTotalViewers();\r\n    });\r\n\r\n    this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {\r\n      console.log('participant disconnected', participant.identity);\r\n      this.checkAllUsersOut();\r\n      this.calculateTotalViewers();\r\n      // remove video element\r\n      let video_id = `${participant.identity} - video`;\r\n      let videoElement = document.getElementById(video_id);\r\n      if (videoElement) {\r\n        videoElement.remove();\r\n      }\r\n      // remove audio element\r\n      let audio_id = `${participant.identity} - audio`;\r\n      let audioElement = document.getElementById(audio_id);\r\n      if (audioElement) {\r\n        audioElement.remove();\r\n      }\r\n    });\r\n    this.subscribeToTracks();\r\n    this.subcribeTracksPublished();\r\n    this.listenMessages();\r\n    // join a room\r\n    await this.room.connect(wsURL, token);\r\n    // log local participant\r\n    console.log('local participant', this.room.localParticipant.sid);\r\n  }\r\n\r\n  listenMessages() {\r\n    this.room.on(\r\n      RoomEvent.DataReceived,\r\n      (\r\n        payload: Uint8Array,\r\n        participant: Participant,\r\n        kind: DataPacket_Kind\r\n      ) => {\r\n        const strData = this.decoder.decode(payload);\r\n        console.log('Received message:', strData);\r\n      }\r\n    );\r\n  }\r\n\r\n  sendMessage(message: string) {\r\n    const data = this.encoder.encode(message);\r\n    this.room.localParticipant.publishData(data);\r\n  }\r\n\r\n  checkAllUsersOut() {\r\n    // this.room.remoteParticipants.size == 1 and user is User-record\r\n    console.log('remote participants', this.room.remoteParticipants.size);\r\n    if (\r\n      this.room.remoteParticipants.size == 1 &&\r\n      this.room.localParticipant.identity == 'User-record'\r\n    ) {\r\n      // outroom\r\n      this.room.disconnect();\r\n      // delete room\r\n      this.deleteRoom(this.room.name).subscribe((res) => {\r\n        console.log('room deleted', res);\r\n      });\r\n    }\r\n  }\r\n\r\n  calculateTotalViewers() {\r\n    this.total_viewers.next(this.room.remoteParticipants.size);\r\n  }\r\n\r\n  async publishTracks() {\r\n    let videoPublication = await this.publishVideoTrack();\r\n    let audioPublication = await this.publishAudioTrack();\r\n    this.is_publishing.next(true);\r\n    return { videoPublication, audioPublication };\r\n  }\r\n\r\n  async publishVideoTrack() {\r\n    if (!this.current_video_track) {\r\n      this.current_video_track = await this.createLocalVideoTrack();\r\n    }\r\n    const videoPublication = await this.room.localParticipant.publishTrack(\r\n      this.current_video_track,\r\n      this.videoPublishOptions\r\n    );\r\n    return videoPublication;\r\n  }\r\n\r\n  async unpublishVideoTrack() {\r\n    if (this.current_video_track) {\r\n      this.room.localParticipant.unpublishTrack(this.current_video_track);\r\n    }\r\n  }\r\n\r\n  async publishAudioTrack() {\r\n    if (!this.current_audio_track) {\r\n      this.current_audio_track = await createLocalAudioTrack();\r\n    }\r\n    const audioPublication = await this.room.localParticipant.publishTrack(\r\n      this.current_audio_track\r\n    );\r\n    return audioPublication;\r\n  }\r\n\r\n  async unPublishAudioTrack() {\r\n    if (this.current_audio_track) {\r\n      this.room.localParticipant.unpublishTrack(this.current_audio_track);\r\n    }\r\n  }\r\n\r\n  muteAudio() {\r\n    if (this.current_audio_track && !this.current_audio_track.isMuted) {\r\n      this.current_audio_track.mute();\r\n    }\r\n  }\r\n\r\n  unmuteAudio() {\r\n    if (this.current_audio_track && this.current_audio_track.isMuted) {\r\n      this.current_audio_track.unmute();\r\n    }\r\n  }\r\n\r\n  muteVideo() {\r\n    if (this.current_video_track && !this.current_video_track.isMuted) {\r\n      this.current_video_track.mute();\r\n    }\r\n  }\r\n\r\n  unmuteVideo() {\r\n    if (this.current_video_track && this.current_video_track.isMuted) {\r\n      this.current_video_track.unmute();\r\n    }\r\n  }\r\n\r\n  toggleMuteVideo() {\r\n    if (this.current_video_track) {\r\n      if (this.current_video_track.isMuted) {\r\n        this.current_video_track.unmute();\r\n      } else {\r\n        this.current_video_track.mute();\r\n      }\r\n    }\r\n  }\r\n\r\n  toggleMuteAudio() {\r\n    if (this.current_audio_track) {\r\n      if (this.current_audio_track.isMuted) {\r\n        this.current_audio_track.unmute();\r\n      } else {\r\n        this.current_audio_track.mute();\r\n      }\r\n    }\r\n  }\r\n\r\n  unPublishTracks() {\r\n    if (this.current_video_track) {\r\n      this.room.localParticipant.unpublishTrack(this.current_video_track);\r\n    }\r\n    if (this.current_audio_track) {\r\n      this.room.localParticipant.unpublishTrack(this.current_audio_track);\r\n    }\r\n  }\r\n\r\n  // end room\r\n  endRoom() {\r\n    this.roomMetadataSubject.unsubscribe();\r\n    this.room.disconnect();\r\n  }\r\n\r\n  async createLocalTracks(elm_id = 'livekit-publisher-video') {\r\n    let videoOptions: VideoCaptureOptions = this.localVideoInitOptions;\r\n    if (this.current_camera) {\r\n      videoOptions.deviceId = this.current_camera.deviceId;\r\n      // delete facingMode\r\n      delete videoOptions.facingMode;\r\n    }\r\n\r\n    let audioOptions: AudioCaptureOptions = this.localAudioInitOptions;\r\n\r\n    if (this.current_microphone) {\r\n      audioOptions.deviceId = this.current_microphone.deviceId;\r\n    }\r\n\r\n    this.stopCurrentStream();\r\n    // wait 0.8 second\r\n    await new Promise((resolve) => setTimeout(resolve, 800));\r\n    let tracks = await createLocalTracks({\r\n      audio: audioOptions,\r\n      video: videoOptions,\r\n    }).catch((error) => {\r\n      console.log(MediaDeviceFailure.getFailure(error));\r\n    });\r\n\r\n    if (!tracks) {\r\n      return { videoTrack: null, audioTrack: null };\r\n    }\r\n    const videoTrack = tracks.find(\r\n      (t) => t.kind === Track.Kind.Video\r\n    ) as LocalVideoTrack;\r\n    const audioTrack = tracks.find(\r\n      (t) => t.kind === Track.Kind.Audio\r\n    ) as LocalAudioTrack;\r\n    this.current_video_track = videoTrack;\r\n    this.current_audio_track = audioTrack;\r\n    // attach to video element\r\n    const videoElement = document.getElementById(elm_id) as HTMLVideoElement;\r\n    videoTrack.attach(videoElement);\r\n    return { videoTrack, audioTrack };\r\n  }\r\n\r\n  // create local video track\r\n  async createLocalVideoTrack(elm_id = 'livekit-publisher-video') {\r\n    let videoOptions: VideoCaptureOptions = this.localVideoInitOptions;\r\n    if (this.current_camera) {\r\n      videoOptions.deviceId = this.current_camera.deviceId;\r\n      // delete facingMode\r\n      delete videoOptions.facingMode;\r\n    }\r\n    this.stopVideoTrack();\r\n    let videoTrack = await createLocalVideoTrack(videoOptions).catch(\r\n      (error) => {\r\n        console.log(MediaDeviceFailure.getFailure(error));\r\n      }\r\n    );\r\n    if (!videoTrack) {\r\n      return null;\r\n    }\r\n    this.current_video_track = videoTrack;\r\n    // attach to video element\r\n    const videoElement = document.getElementById(elm_id) as HTMLVideoElement;\r\n    videoTrack.attach(videoElement);\r\n    return videoTrack;\r\n  }\r\n\r\n  async createLocalAudioTrack() {\r\n    let audioOptions: AudioCaptureOptions = {\r\n      echoCancellation: true,\r\n      noiseSuppression: true,\r\n      autoGainControl: true,\r\n    };\r\n    if (this.current_microphone) {\r\n      audioOptions.deviceId = this.current_microphone.deviceId;\r\n    }\r\n    this.stopAudioTrack();\r\n    let audioTrack = await createLocalAudioTrack(audioOptions).catch(\r\n      (error) => {\r\n        console.log(MediaDeviceFailure.getFailure(error));\r\n      }\r\n    );\r\n    if (!audioTrack) {\r\n      return null;\r\n    }\r\n    this.current_audio_track = audioTrack;\r\n    return audioTrack;\r\n  }\r\n\r\n  async subscribeToTracks() {\r\n    this.room.on(\r\n      RoomEvent.TrackSubscribed,\r\n      this.handleTrackSubscribed.bind(this)\r\n    );\r\n  }\r\n\r\n  handleTrackSubscribed(\r\n    track: RemoteTrack,\r\n    publication: RemoteTrackPublication,\r\n    participant: RemoteParticipant\r\n  ) {\r\n    console.log(\r\n      'track subscribed',\r\n      track.sid,\r\n      publication.trackSid,\r\n      participant.sid\r\n    );\r\n    this.attachTrack(track, participant);\r\n  }\r\n\r\n  subcribeTracksPublished() {\r\n    this.room.on(RoomEvent.TrackPublished, (publication, participant) => {\r\n      console.log(\r\n        'track published',\r\n        publication.trackSid,\r\n        participant.identity\r\n      );\r\n      publication.setSubscribed(true);\r\n    });\r\n\r\n    // also subscribe to tracks published before participant joined\r\n    this.room.remoteParticipants.forEach((participant) => {\r\n      participant.trackPublications.forEach((publication) => {\r\n        console.log('existing publication', publication.trackSid);\r\n        publication.setSubscribed(true);\r\n        // attach to video element\r\n        const track = publication.track;\r\n        if (track) {\r\n          this.attachTrack(track, participant);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  attachTrack(\r\n    track: RemoteTrack,\r\n    participant: RemoteParticipant,\r\n    elementId: string = 'livekit-subscriber-video'\r\n  ) {\r\n    // let parentElement = document.getElementById(elementId) as HTMLElement;\r\n    // const element = track.attach();\r\n    // parentElement.appendChild(element);\r\n    if (track.kind === Track.Kind.Video) {\r\n      let video_id = `${participant.identity} - video`;\r\n      console.log('attach track', track.sid, elementId);\r\n      // find video element by participant identity if exists remove it\r\n      let videoElement = document.getElementById(video_id);\r\n      if (videoElement) {\r\n        videoElement.remove();\r\n      }\r\n      let parentElement = document.getElementById(elementId);\r\n      if (!parentElement) {\r\n        console.log('not found \"livekit-subscriber-video\" element');\r\n        return;\r\n      }\r\n      /* do things with track, publication or participant */\r\n      const element = document.createElement('video') as HTMLVideoElement;\r\n      element.id = video_id;\r\n      element.autoplay = true;\r\n      element.muted = false;\r\n      element.playsInline = true;\r\n      // element.controls = true;\r\n      // set width 100% and height auto\r\n      element.style.width = '100%';\r\n      element.style.height = 'auto';\r\n      // add class to video element\r\n      element.classList.add('livekit-video-subscriber');\r\n      track.attach(element);\r\n      parentElement.appendChild(element);\r\n    } else if (track.kind === Track.Kind.Audio) {\r\n      // add track to audio element\r\n      let audio_id = `${participant.identity} - audio`;\r\n      console.log('attach track', track.sid, elementId);\r\n      // find audio element by participant identity if exists remove it\r\n      let audioElement = document.getElementById(audio_id);\r\n      if (audioElement) {\r\n        audioElement.remove();\r\n      }\r\n      let parentElement = document.getElementById(elementId);\r\n      if (!parentElement) {\r\n        console.log('not found \"livekit-subscriber-video\" element');\r\n        return;\r\n      }\r\n      /* do things with track, publication or participant */\r\n      const element = document.createElement('audio') as HTMLAudioElement;\r\n      element.id = `${participant.identity} - audio`;\r\n      element.autoplay = true;\r\n      track.attach(element);\r\n      parentElement.appendChild(element);\r\n    }\r\n  }\r\n\r\n  generateToken(room_id: string, user_id: string = null) {\r\n    let data = {\r\n      room_id,\r\n      user_id,\r\n    };\r\n\r\n    if (!user_id) {\r\n      delete data.user_id;\r\n    }\r\n    // generate token\r\n    return this.http\r\n      .post(`${environment.apiUrl}/livekit/generate-token`, data)\r\n      .pipe(map((res: any) => res.token));\r\n  }\r\n\r\n  // get list of rooms\r\n  getRooms() {\r\n    return this.http.get(`${environment.apiUrl}/livekit/list-rooms`);\r\n  }\r\n\r\n  // delete room\r\n  deleteRoom(room_id: string) {\r\n    this.roomMetadataSubject.unsubscribe();\r\n    return this.http.post(`${environment.apiUrl}/livekit/delete-room`, {\r\n      room_id,\r\n    });\r\n  }\r\n\r\n  stopEgressRoom(egressID: string) {\r\n    return this.http.post(`${environment.apiUrl}/livekit/stop-egress`, {\r\n      egressID,\r\n    });\r\n  }\r\n\r\n  setParticipantMetadata(data: any) {\r\n    this.room.localParticipant.setMetadata(data);\r\n  }\r\n\r\n  // update-room-metadata\r\n  updateRoomMetadata(room_id: string, metadata: RoomMetadata) {\r\n    return this.http\r\n      .post(`${environment.apiUrl}/livekit/update-room-metadata`, {\r\n        room_id,\r\n        metadata,\r\n      })\r\n      .pipe(map((res: any) => res));\r\n  }\r\n\r\n  startWebEgress(token: string, host_url: string = environment.ezstreamUrl) {\r\n    // generate token\r\n    return this.http.post(`${environment.apiUrl}/livekit/start-web-egress`, {\r\n      token,\r\n      host_url,\r\n    });\r\n  }\r\n\r\n  startWebEgressHLS(token: string, host_url: string = environment.ezstreamUrl) {\r\n    // generate token\r\n    return this.http.post(\r\n      `${environment.apiUrl}/livekit/start-web-egress-hls`,\r\n      {\r\n        token,\r\n        host_url,\r\n      }\r\n    );\r\n  }\r\n\r\n  startWebEgressRTMP(\r\n    token: string,\r\n    host_url: string = environment.ezstreamUrl\r\n  ) {\r\n    // generate token\r\n    return this.http.post(\r\n      `${environment.apiUrl}/livekit/start-web-egress-rtmp`,\r\n      {\r\n        token,\r\n        host_url,\r\n      }\r\n    );\r\n  }\r\n\r\n  syncTimerCountUp(metadata) {\r\n    if (!metadata) return;\r\n    let is_running = metadata.overlay_data.is_running_timer;\r\n    let timer_started_at = metadata.overlay_data.timer_started_at;\r\n    let timer_stoped_at = metadata.overlay_data.timer_stoped_at;\r\n    let current_time = this.worldTimeService.time;\r\n    let timer_duration = metadata.overlay_data.timer_duration;\r\n    if (timer_started_at == 0 && timer_stoped_at == 0 && !is_running) {\r\n      this.overlaysService.setTimer('00:00');\r\n      return;\r\n    }\r\n\r\n    let time = 0;\r\n    if (timer_stoped_at > timer_started_at) {\r\n      time = timer_stoped_at - timer_started_at + timer_duration;\r\n    } else {\r\n      time = current_time - timer_started_at + timer_duration;\r\n    }\r\n    // use moment to format time\r\n    let time_seconds = moment.duration(time, 'seconds');\r\n    let hours = time_seconds.hours();\r\n    let minutes = time_seconds.minutes();\r\n    let seconds = time_seconds.seconds();\r\n    // if hours > 0, add hours to minutes\r\n    if (hours > 0) {\r\n      minutes += hours * 60;\r\n    }\r\n    this.match_seconds = time;\r\n    this.setStringTime(minutes, seconds);\r\n  }\r\n\r\n  setStringTime(minutes, seconds) {\r\n    let formatted_time = `${formatNumber(\r\n      minutes,\r\n      'en',\r\n      '2.0-0'\r\n    )}:${formatNumber(seconds, 'en', '2.0-0')}`;\r\n    this.overlaysService.setTimer(formatted_time);\r\n  }\r\n\r\n  // recordRoom(room: string) {\r\n  //   return this.http.get(`${environment.apiUrl}/livekit/start-web-egress`, {\r\n  //     params: {\r\n  //       room_id: room,\r\n  //     },\r\n  //   });\r\n  // }\r\n}\r\n\r\nexport interface TeamPropsOverlay {\r\n  name: string;\r\n  logo: string;\r\n  color?: string;\r\n  score?: number;\r\n  penalty?: number;\r\n}\r\n\r\nexport interface MatchInfo {\r\n  home_team: TeamPropsOverlay;\r\n  away_team: TeamPropsOverlay;\r\n}\r\n\r\nexport interface OverlayData {\r\n  is_show: boolean;\r\n  is_show_stoppage_time: boolean;\r\n  is_show_penalty: boolean;\r\n  is_show_sponsor_logo?: boolean;\r\n  is_show_scroll_text?: boolean;\r\n  is_running_timer: boolean;\r\n  timer_started_at?: number; //timestamp\r\n  timer_stoped_at?: number; //timestamp\r\n  timer_duration?: number; //in seconds\r\n  time?: number; //in seconds\r\n  stoppage_time?: number; //in seconds\r\n  period?: number;\r\n  scroll_text?: {\r\n    text: string;\r\n    color?: string;\r\n    background_color?: string;\r\n  };\r\n  primary_color?: string;\r\n  secondary_color?: string;\r\n  accent_color?: string;\r\n  sponsor_position?: {\r\n    x: number;\r\n    y: number;\r\n  };\r\n  sponsors_logo?: {\r\n    src: string;\r\n    alt: string;\r\n  }[];\r\n}\r\nexport interface RoomMetadata {\r\n  match_info: MatchInfo;\r\n  overlay_data: OverlayData;\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
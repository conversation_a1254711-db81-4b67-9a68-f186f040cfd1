{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nexport class LoadingService {\n  constructor(modalService) {\n    this.modalService = modalService;\n    this.buttonLoading = false;\n    this.is_loading = false;\n  }\n  show() {\n    // console.log(\"open\", this.modalLoading);\n    if (!this.is_loading) {\n      this.modalRef = this.modalService.open(this.modalLoading, {\n        centered: true,\n        backdrop: 'static',\n        size: 'xxs'\n      });\n      this.is_loading = true;\n    }\n  }\n  dismiss() {\n    // console.log(\"close\", this.modalLoading);\n    if (this.modalRef && this.is_loading == true) this.modalRef.close();\n    this.is_loading = false;\n  }\n  showButtonLoading() {\n    this.buttonLoading = true;\n  }\n  dismissButtonLoading() {\n    this.buttonLoading = false;\n  }\n  static #_ = this.ɵfac = function LoadingService_Factory(t) {\n    return new (t || LoadingService)(i0.ɵɵinject(i1.NgbModal));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoadingService,\n    factory: LoadingService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";;AAMA,OAAM,MAAOA,cAAc;EAIzBC,YAAmBC,YAAsB;IAAtB,KAAAA,YAAY,GAAZA,YAAY;IAHxB,KAAAC,aAAa,GAAY,KAAK;IAI9B,KAAAC,UAAU,GAAY,KAAK;EADU;EAErCC,IAAIA,CAAA;IACT;IACA,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MACpB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACJ,YAAY,CAACK,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACxDC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,QAAQ;QAClBC,IAAI,EAAE;OACP,CAAC;MACF,IAAI,CAACP,UAAU,GAAG,IAAI;;EAE1B;EAEOQ,OAAOA,CAAA;IACZ;IACA,IAAI,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACF,UAAU,IAAI,IAAI,EAAE,IAAI,CAACE,QAAQ,CAACO,KAAK,EAAE;IACnE,IAAI,CAACT,UAAU,GAAG,KAAK;EACzB;EAEOU,iBAAiBA,CAAA;IACtB,IAAI,CAACX,aAAa,GAAG,IAAI;EAC3B;EAEOY,oBAAoBA,CAAA;IACzB,IAAI,CAACZ,aAAa,GAAG,KAAK;EAC5B;EAAC,QAAAa,CAAA;qBA9BUhB,cAAc,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA;WAAdrB,cAAc;IAAAsB,OAAA,EAAdtB,cAAc,CAAAuB,IAAA;IAAAC,UAAA,EAFb;EAAM", "names": ["LoadingService", "constructor", "modalService", "buttonLoading", "is_loading", "show", "modalRef", "open", "modalLoading", "centered", "backdrop", "size", "dismiss", "close", "showButtonLoading", "dismissButtonLoading", "_", "i0", "ɵɵinject", "i1", "NgbModal", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\loading.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LoadingService {\r\n  public buttonLoading: boolean = false;\r\n  public modalLoading: any;\r\n  public modalRef: any;\r\n  constructor(public modalService: NgbModal) {}\r\n  public is_loading: boolean = false;\r\n  public show() {\r\n    // console.log(\"open\", this.modalLoading);\r\n    if (!this.is_loading) {\r\n      this.modalRef = this.modalService.open(this.modalLoading, {\r\n        centered: true,\r\n        backdrop: 'static',\r\n        size: 'xxs',\r\n      });\r\n      this.is_loading = true;\r\n    }\r\n  }\r\n\r\n  public dismiss() {\r\n    // console.log(\"close\", this.modalLoading);\r\n    if (this.modalRef && this.is_loading == true) this.modalRef.close();\r\n    this.is_loading = false;\r\n  }\r\n\r\n  public showButtonLoading() {\r\n    this.buttonLoading = true;\r\n  }\r\n\r\n  public dismissButtonLoading() {\r\n    this.buttonLoading = false;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
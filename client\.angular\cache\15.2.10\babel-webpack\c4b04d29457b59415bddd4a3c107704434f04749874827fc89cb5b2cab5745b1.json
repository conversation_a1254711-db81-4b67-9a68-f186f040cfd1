{"ast": null, "code": "import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function retryWhen(notifier) {\n  return source => source.lift(new RetryWhenOperator(notifier, source));\n}\nclass RetryWhenOperator {\n  constructor(notifier, source) {\n    this.notifier = notifier;\n    this.source = source;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new RetryWhenSubscriber(subscriber, this.notifier, this.source));\n  }\n}\nclass RetryWhenSubscriber extends OuterSubscriber {\n  constructor(destination, notifier, source) {\n    super(destination);\n    this.notifier = notifier;\n    this.source = source;\n  }\n  error(err) {\n    if (!this.isStopped) {\n      let errors = this.errors;\n      let retries = this.retries;\n      let retriesSubscription = this.retriesSubscription;\n      if (!retries) {\n        errors = new Subject();\n        try {\n          const {\n            notifier\n          } = this;\n          retries = notifier(errors);\n        } catch (e) {\n          return super.error(e);\n        }\n        retriesSubscription = subscribeToResult(this, retries);\n      } else {\n        this.errors = null;\n        this.retriesSubscription = null;\n      }\n      this._unsubscribeAndRecycle();\n      this.errors = errors;\n      this.retries = retries;\n      this.retriesSubscription = retriesSubscription;\n      errors.next(err);\n    }\n  }\n  _unsubscribe() {\n    const {\n      errors,\n      retriesSubscription\n    } = this;\n    if (errors) {\n      errors.unsubscribe();\n      this.errors = null;\n    }\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = null;\n    }\n    this.retries = null;\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    const {\n      _unsubscribe\n    } = this;\n    this._unsubscribe = null;\n    this._unsubscribeAndRecycle();\n    this._unsubscribe = _unsubscribe;\n    this.source.subscribe(this);\n  }\n}", "map": {"version": 3, "names": ["Subject", "OuterSubscriber", "subscribeToResult", "retry<PERSON><PERSON>", "notifier", "source", "lift", "RetryWhenOperator", "constructor", "call", "subscriber", "subscribe", "RetryWhenSubscriber", "destination", "error", "err", "isStopped", "errors", "retries", "retriesSubscription", "e", "_unsubscribeAndRecycle", "next", "_unsubscribe", "unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/retryWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function retryWhen(notifier) {\n    return (source) => source.lift(new RetryWhenOperator(notifier, source));\n}\nclass RetryWhenOperator {\n    constructor(notifier, source) {\n        this.notifier = notifier;\n        this.source = source;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new RetryWhenSubscriber(subscriber, this.notifier, this.source));\n    }\n}\nclass RetryWhenSubscriber extends OuterSubscriber {\n    constructor(destination, notifier, source) {\n        super(destination);\n        this.notifier = notifier;\n        this.source = source;\n    }\n    error(err) {\n        if (!this.isStopped) {\n            let errors = this.errors;\n            let retries = this.retries;\n            let retriesSubscription = this.retriesSubscription;\n            if (!retries) {\n                errors = new Subject();\n                try {\n                    const { notifier } = this;\n                    retries = notifier(errors);\n                }\n                catch (e) {\n                    return super.error(e);\n                }\n                retriesSubscription = subscribeToResult(this, retries);\n            }\n            else {\n                this.errors = null;\n                this.retriesSubscription = null;\n            }\n            this._unsubscribeAndRecycle();\n            this.errors = errors;\n            this.retries = retries;\n            this.retriesSubscription = retriesSubscription;\n            errors.next(err);\n        }\n    }\n    _unsubscribe() {\n        const { errors, retriesSubscription } = this;\n        if (errors) {\n            errors.unsubscribe();\n            this.errors = null;\n        }\n        if (retriesSubscription) {\n            retriesSubscription.unsubscribe();\n            this.retriesSubscription = null;\n        }\n        this.retries = null;\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        const { _unsubscribe } = this;\n        this._unsubscribe = null;\n        this._unsubscribeAndRecycle();\n        this._unsubscribe = _unsubscribe;\n        this.source.subscribe(this);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAE;EAChC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACH,QAAQ,EAAEC,MAAM,CAAC,CAAC;AAC3E;AACA,MAAME,iBAAiB,CAAC;EACpBC,WAAWA,CAACJ,QAAQ,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAI,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,mBAAmB,CAACF,UAAU,EAAE,IAAI,CAACN,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC;EAC5F;AACJ;AACA,MAAMO,mBAAmB,SAASX,eAAe,CAAC;EAC9CO,WAAWA,CAACK,WAAW,EAAET,QAAQ,EAAEC,MAAM,EAAE;IACvC,KAAK,CAACQ,WAAW,CAAC;IAClB,IAAI,CAACT,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAS,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAIC,MAAM,GAAG,IAAI,CAACA,MAAM;MACxB,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB;MAClD,IAAI,CAACD,OAAO,EAAE;QACVD,MAAM,GAAG,IAAIjB,OAAO,CAAC,CAAC;QACtB,IAAI;UACA,MAAM;YAAEI;UAAS,CAAC,GAAG,IAAI;UACzBc,OAAO,GAAGd,QAAQ,CAACa,MAAM,CAAC;QAC9B,CAAC,CACD,OAAOG,CAAC,EAAE;UACN,OAAO,KAAK,CAACN,KAAK,CAACM,CAAC,CAAC;QACzB;QACAD,mBAAmB,GAAGjB,iBAAiB,CAAC,IAAI,EAAEgB,OAAO,CAAC;MAC1D,CAAC,MACI;QACD,IAAI,CAACD,MAAM,GAAG,IAAI;QAClB,IAAI,CAACE,mBAAmB,GAAG,IAAI;MACnC;MACA,IAAI,CAACE,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACJ,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;MAC9CF,MAAM,CAACK,IAAI,CAACP,GAAG,CAAC;IACpB;EACJ;EACAQ,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEN,MAAM;MAAEE;IAAoB,CAAC,GAAG,IAAI;IAC5C,IAAIF,MAAM,EAAE;MACRA,MAAM,CAACO,WAAW,CAAC,CAAC;MACpB,IAAI,CAACP,MAAM,GAAG,IAAI;IACtB;IACA,IAAIE,mBAAmB,EAAE;MACrBA,mBAAmB,CAACK,WAAW,CAAC,CAAC;MACjC,IAAI,CAACL,mBAAmB,GAAG,IAAI;IACnC;IACA,IAAI,CAACD,OAAO,GAAG,IAAI;EACvB;EACAO,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,MAAM;MAAEP;IAAa,CAAC,GAAG,IAAI;IAC7B,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAACF,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAClB,MAAM,CAACM,SAAS,CAAC,IAAI,CAAC;EAC/B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
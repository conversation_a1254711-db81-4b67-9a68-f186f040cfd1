{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { isScheduler } from '../util/isScheduler';\nexport function startWith(...array) {\n  const scheduler = array[array.length - 1];\n  if (isScheduler(scheduler)) {\n    array.pop();\n    return source => concat(array, source, scheduler);\n  } else {\n    return source => concat(array, source);\n  }\n}", "map": {"version": 3, "names": ["concat", "isScheduler", "startWith", "array", "scheduler", "length", "pop", "source"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/startWith.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { isScheduler } from '../util/isScheduler';\nexport function startWith(...array) {\n    const scheduler = array[array.length - 1];\n    if (isScheduler(scheduler)) {\n        array.pop();\n        return (source) => concat(array, source, scheduler);\n    }\n    else {\n        return (source) => concat(array, source);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,SAASC,SAASA,CAAC,GAAGC,KAAK,EAAE;EAChC,MAAMC,SAAS,GAAGD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;EACzC,IAAIJ,WAAW,CAACG,SAAS,CAAC,EAAE;IACxBD,KAAK,CAACG,GAAG,CAAC,CAAC;IACX,OAAQC,MAAM,IAAKP,MAAM,CAACG,KAAK,EAAEI,MAAM,EAAEH,SAAS,CAAC;EACvD,CAAC,MACI;IACD,OAAQG,MAAM,IAAKP,MAAM,CAACG,KAAK,EAAEI,MAAM,CAAC;EAC5C;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FieldType } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-select/ng-select\";\nimport * as i2 from \"@angular/forms\";\nexport class NgSelectTypeComponent extends FieldType {\n  constructor() {\n    super();\n  }\n  ngOnInit() {\n    // console.log(this.field);\n    if (!this.to.hasOwnProperty('searchable')) {\n      this.to.searchable = true;\n    }\n  }\n  compareWith(o1, o2) {\n    if (o2.value === undefined) {\n      o2 = {\n        value: o2\n      };\n    }\n    // compare the values of the objects\n    // check is the value is an object\n    if (typeof o1.value === 'object') {\n      let value1 = JSON.stringify(o1.value);\n      let value2 = JSON.stringify(o2.value);\n      return value1.toLowerCase() === value2.toLowerCase();\n    } else {\n      return o1.value === o2.value;\n    }\n  }\n  static #_ = this.ɵfac = function NgSelectTypeComponent_Factory(t) {\n    return new (t || NgSelectTypeComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NgSelectTypeComponent,\n    selectors: [[\"app-ng-select-type\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 10,\n    consts: [[\"bindLabel\", \"label\", \"bindValue\", \"value\", 3, \"clearable\", \"searchable\", \"formControl\", \"multiple\", \"maxSelectedItems\", \"hideSelected\", \"closeOnSelect\", \"placeholder\", \"items\", \"compareWith\"]],\n    template: function NgSelectTypeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"ng-select\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"clearable\", ctx.to.clearable)(\"searchable\", ctx.to.searchable)(\"formControl\", ctx.formControl)(\"multiple\", ctx.to.multiple)(\"maxSelectedItems\", ctx.to.maxSelectedItems)(\"hideSelected\", ctx.to.hideSelected)(\"closeOnSelect\", ctx.to.closeOnSelect ? true : false)(\"placeholder\", ctx.to.placeholder)(\"items\", ctx.to.options)(\"compareWith\", ctx.compareWith);\n      }\n    },\n    dependencies: [i1.NgSelectComponent, i2.NgControlStatus, i2.FormControlDirective],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAA0BA,SAAS,QAAQ,kBAAkB;;;;AAO7D,OAAM,MAAOC,qBACX,SAAQD,SAA0B;EAGlCE,YAAA;IACE,KAAK,EAAE;EACT;EACAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACC,EAAE,CAACC,cAAc,CAAC,YAAY,CAAC,EAAE;MACzC,IAAI,CAACD,EAAE,CAACE,UAAU,GAAG,IAAI;;EAE7B;EAEAC,WAAWA,CAACC,EAAE,EAAEC,EAAE;IAChB,IAAIA,EAAE,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1BF,EAAE,GAAG;QAAEC,KAAK,EAAED;MAAE,CAAE;;IAEpB;IACA;IACA,IAAI,OAAOD,EAAE,CAACE,KAAK,KAAK,QAAQ,EAAE;MAChC,IAAIE,MAAM,GAAGC,IAAI,CAACC,SAAS,CAACN,EAAE,CAACE,KAAK,CAAC;MACrC,IAAIK,MAAM,GAAGF,IAAI,CAACC,SAAS,CAACL,EAAE,CAACC,KAAK,CAAC;MACrC,OAAOE,MAAM,CAACI,WAAW,EAAE,KAAKD,MAAM,CAACC,WAAW,EAAE;KACrD,MAAM;MACL,OAAOR,EAAE,CAACE,KAAK,KAAKD,EAAE,CAACC,KAAK;;EAEhC;EAAC,QAAAO,CAAA;qBA3BUhB,qBAAqB;EAAA;EAAA,QAAAiB,EAAA;UAArBjB,qBAAqB;IAAAkB,SAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRlCP,EAAA,CAAAS,SAAA,mBAOY;;;QAPCT,EAAA,CAAAU,UAAA,cAAAF,GAAA,CAAAzB,EAAA,CAAA4B,SAAA,CAA0B,eAAAH,GAAA,CAAAzB,EAAA,CAAAE,UAAA,iBAAAuB,GAAA,CAAAI,WAAA,cAAAJ,GAAA,CAAAzB,EAAA,CAAA8B,QAAA,sBAAAL,GAAA,CAAAzB,EAAA,CAAA+B,gBAAA,kBAAAN,GAAA,CAAAzB,EAAA,CAAAgC,YAAA,mBAAAP,GAAA,CAAAzB,EAAA,CAAAiC,aAAA,gCAAAR,GAAA,CAAAzB,EAAA,CAAAkC,WAAA,WAAAT,GAAA,CAAAzB,EAAA,CAAAmC,OAAA,iBAAAV,GAAA,CAAAtB,WAAA", "names": ["FieldType", "NgSelectTypeComponent", "constructor", "ngOnInit", "to", "hasOwnProperty", "searchable", "compareWith", "o1", "o2", "value", "undefined", "value1", "JSON", "stringify", "value2", "toLowerCase", "_", "_2", "selectors", "features", "i0", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "NgSelectTypeComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵproperty", "clearable", "formControl", "multiple", "maxSelectedItems", "hideSelected", "closeOnSelect", "placeholder", "options"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\ng-select-type\\ng-select-type.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\ng-select-type\\ng-select-type.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FieldTypeConfig, FieldType } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'app-ng-select-type',\r\n  templateUrl: './ng-select-type.component.html',\r\n  styleUrls: ['./ng-select-type.component.scss'],\r\n})\r\nexport class NgSelectTypeComponent\r\n  extends FieldType<FieldTypeConfig>\r\n  implements OnInit\r\n{\r\n  constructor() {\r\n    super();\r\n  }\r\n  ngOnInit(): void {\r\n    // console.log(this.field);\r\n    if (!this.to.hasOwnProperty('searchable')) {\r\n      this.to.searchable = true;\r\n    }\r\n  }\r\n\r\n  compareWith(o1, o2) {\r\n    if (o2.value === undefined) {\r\n      o2 = { value: o2 };\r\n    }\r\n    // compare the values of the objects\r\n    // check is the value is an object\r\n    if (typeof o1.value === 'object') {\r\n      let value1 = JSON.stringify(o1.value);\r\n      let value2 = JSON.stringify(o2.value);\r\n      return value1.toLowerCase() === value2.toLowerCase();\r\n    } else {\r\n      return o1.value === o2.value;\r\n    }\r\n  }\r\n}\r\n", "<ng-select   [clearable]=\"to.clearable\" [searchable]=\"to.searchable\" [formControl]=\"formControl\" [multiple]=\"to.multiple\"\r\n  [maxSelectedItems]=\"to.maxSelectedItems\" [hideSelected]=\"to.hideSelected\"\r\n  [closeOnSelect]=\"to.closeOnSelect? true : false\" [placeholder]=\"to.placeholder\" [items]=\"to.options\" bindLabel=\"label\"\r\n  bindValue=\"value\" [compareWith]=\"compareWith\">\r\n  <!-- <ng-option *ngFor=\"let item of to.options\" [value]=\"item.value\">\r\n    {{item.label}}\r\n  </ng-option> -->\r\n</ng-select>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
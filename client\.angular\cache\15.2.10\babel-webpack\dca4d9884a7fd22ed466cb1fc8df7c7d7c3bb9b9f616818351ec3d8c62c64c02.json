{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Subject } from '../Subject';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n  return function windowCountOperatorFunction(source) {\n    return source.lift(new WindowCountOperator(windowSize, startWindowEvery));\n  };\n}\nclass WindowCountOperator {\n  constructor(windowSize, startWindowEvery) {\n    this.windowSize = windowSize;\n    this.startWindowEvery = startWindowEvery;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new WindowCountSubscriber(subscriber, this.windowSize, this.startWindowEvery));\n  }\n}\nclass WindowCountSubscriber extends Subscriber {\n  constructor(destination, windowSize, startWindowEvery) {\n    super(destination);\n    this.destination = destination;\n    this.windowSize = windowSize;\n    this.startWindowEvery = startWindowEvery;\n    this.windows = [new Subject()];\n    this.count = 0;\n    destination.next(this.windows[0]);\n  }\n  _next(value) {\n    const startWindowEvery = this.startWindowEvery > 0 ? this.startWindowEvery : this.windowSize;\n    const destination = this.destination;\n    const windowSize = this.windowSize;\n    const windows = this.windows;\n    const len = windows.length;\n    for (let i = 0; i < len && !this.closed; i++) {\n      windows[i].next(value);\n    }\n    const c = this.count - windowSize + 1;\n    if (c >= 0 && c % startWindowEvery === 0 && !this.closed) {\n      windows.shift().complete();\n    }\n    if (++this.count % startWindowEvery === 0 && !this.closed) {\n      const window = new Subject();\n      windows.push(window);\n      destination.next(window);\n    }\n  }\n  _error(err) {\n    const windows = this.windows;\n    if (windows) {\n      while (windows.length > 0 && !this.closed) {\n        windows.shift().error(err);\n      }\n    }\n    this.destination.error(err);\n  }\n  _complete() {\n    const windows = this.windows;\n    if (windows) {\n      while (windows.length > 0 && !this.closed) {\n        windows.shift().complete();\n      }\n    }\n    this.destination.complete();\n  }\n  _unsubscribe() {\n    this.count = 0;\n    this.windows = null;\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "Subject", "windowCount", "windowSize", "startWindowEvery", "windowCountOperatorFunction", "source", "lift", "WindowCountOperator", "constructor", "call", "subscriber", "subscribe", "WindowCountSubscriber", "destination", "windows", "count", "next", "_next", "value", "len", "length", "i", "closed", "c", "shift", "complete", "window", "push", "_error", "err", "error", "_complete", "_unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/windowCount.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Subject } from '../Subject';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n    return function windowCountOperatorFunction(source) {\n        return source.lift(new WindowCountOperator(windowSize, startWindowEvery));\n    };\n}\nclass WindowCountOperator {\n    constructor(windowSize, startWindowEvery) {\n        this.windowSize = windowSize;\n        this.startWindowEvery = startWindowEvery;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WindowCountSubscriber(subscriber, this.windowSize, this.startWindowEvery));\n    }\n}\nclass WindowCountSubscriber extends Subscriber {\n    constructor(destination, windowSize, startWindowEvery) {\n        super(destination);\n        this.destination = destination;\n        this.windowSize = windowSize;\n        this.startWindowEvery = startWindowEvery;\n        this.windows = [new Subject()];\n        this.count = 0;\n        destination.next(this.windows[0]);\n    }\n    _next(value) {\n        const startWindowEvery = (this.startWindowEvery > 0) ? this.startWindowEvery : this.windowSize;\n        const destination = this.destination;\n        const windowSize = this.windowSize;\n        const windows = this.windows;\n        const len = windows.length;\n        for (let i = 0; i < len && !this.closed; i++) {\n            windows[i].next(value);\n        }\n        const c = this.count - windowSize + 1;\n        if (c >= 0 && c % startWindowEvery === 0 && !this.closed) {\n            windows.shift().complete();\n        }\n        if (++this.count % startWindowEvery === 0 && !this.closed) {\n            const window = new Subject();\n            windows.push(window);\n            destination.next(window);\n        }\n    }\n    _error(err) {\n        const windows = this.windows;\n        if (windows) {\n            while (windows.length > 0 && !this.closed) {\n                windows.shift().error(err);\n            }\n        }\n        this.destination.error(err);\n    }\n    _complete() {\n        const windows = this.windows;\n        if (windows) {\n            while (windows.length > 0 && !this.closed) {\n                windows.shift().complete();\n            }\n        }\n        this.destination.complete();\n    }\n    _unsubscribe() {\n        this.count = 0;\n        this.windows = null;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAEC,gBAAgB,GAAG,CAAC,EAAE;EAC1D,OAAO,SAASC,2BAA2BA,CAACC,MAAM,EAAE;IAChD,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,mBAAmB,CAACL,UAAU,EAAEC,gBAAgB,CAAC,CAAC;EAC7E,CAAC;AACL;AACA,MAAMI,mBAAmB,CAAC;EACtBC,WAAWA,CAACN,UAAU,EAAEC,gBAAgB,EAAE;IACtC,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAM,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,qBAAqB,CAACF,UAAU,EAAE,IAAI,CAACR,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC1G;AACJ;AACA,MAAMS,qBAAqB,SAASb,UAAU,CAAC;EAC3CS,WAAWA,CAACK,WAAW,EAAEX,UAAU,EAAEC,gBAAgB,EAAE;IACnD,KAAK,CAACU,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACX,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACW,OAAO,GAAG,CAAC,IAAId,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACe,KAAK,GAAG,CAAC;IACdF,WAAW,CAACG,IAAI,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;EACrC;EACAG,KAAKA,CAACC,KAAK,EAAE;IACT,MAAMf,gBAAgB,GAAI,IAAI,CAACA,gBAAgB,GAAG,CAAC,GAAI,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACD,UAAU;IAC9F,MAAMW,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMX,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMY,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMK,GAAG,GAAGL,OAAO,CAACM,MAAM;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,IAAI,CAAC,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1CP,OAAO,CAACO,CAAC,CAAC,CAACL,IAAI,CAACE,KAAK,CAAC;IAC1B;IACA,MAAMK,CAAC,GAAG,IAAI,CAACR,KAAK,GAAGb,UAAU,GAAG,CAAC;IACrC,IAAIqB,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGpB,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAACmB,MAAM,EAAE;MACtDR,OAAO,CAACU,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAC9B;IACA,IAAI,EAAE,IAAI,CAACV,KAAK,GAAGZ,gBAAgB,KAAK,CAAC,IAAI,CAAC,IAAI,CAACmB,MAAM,EAAE;MACvD,MAAMI,MAAM,GAAG,IAAI1B,OAAO,CAAC,CAAC;MAC5Bc,OAAO,CAACa,IAAI,CAACD,MAAM,CAAC;MACpBb,WAAW,CAACG,IAAI,CAACU,MAAM,CAAC;IAC5B;EACJ;EACAE,MAAMA,CAACC,GAAG,EAAE;IACR,MAAMf,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACT,OAAOA,OAAO,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACE,MAAM,EAAE;QACvCR,OAAO,CAACU,KAAK,CAAC,CAAC,CAACM,KAAK,CAACD,GAAG,CAAC;MAC9B;IACJ;IACA,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAACD,GAAG,CAAC;EAC/B;EACAE,SAASA,CAAA,EAAG;IACR,MAAMjB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACT,OAAOA,OAAO,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACE,MAAM,EAAE;QACvCR,OAAO,CAACU,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;IACJ;IACA,IAAI,CAACZ,WAAW,CAACY,QAAQ,CAAC,CAAC;EAC/B;EACAO,YAAYA,CAAA,EAAG;IACX,IAAI,CAACjB,KAAK,GAAG,CAAC;IACd,IAAI,CAACD,OAAO,GAAG,IAAI;EACvB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EditorSidebarModule } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { RegisterNewPlayerComponent } from 'app/pages/registration/register-new-player/register-new-player.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreCommonModule } from '@core/common.module';\nimport * as i0 from \"@angular/core\";\nexport class ShareModule {\n  static #_ = this.ɵfac = function ShareModule_Factory(t) {\n    return new (t || ShareModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ShareModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, EditorSidebarModule, TranslateModule, CoreCommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ShareModule, {\n    declarations: [RegisterNewPlayerComponent],\n    imports: [CommonModule, EditorSidebarModule, TranslateModule, CoreCommonModule],\n    exports: [RegisterNewPlayerComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,0BAA0B,QAAQ,0EAA0E;AACrH,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,gBAAgB,QAAQ,qBAAqB;;AAYtD,OAAM,MAAOC,WAAW;EAAA,QAAAC,CAAA;qBAAXD,WAAW;EAAA;EAAA,QAAAE,EAAA;UAAXF;EAAW;EAAA,QAAAG,EAAA;cAPpBR,YAAY,EACZC,mBAAmB,EACnBE,eAAe,EACfC,gBAAgB;EAAA;;;2EAIPC,WAAW;IAAAI,YAAA,GATPP,0BAA0B;IAAAQ,OAAA,GAEvCV,YAAY,EACZC,mBAAmB,EACnBE,eAAe,EACfC,gBAAgB;IAAAO,OAAA,GAERT,0BAA0B;EAAA;AAAA", "names": ["CommonModule", "EditorSidebarModule", "RegisterNewPlayerComponent", "TranslateModule", "CoreCommonModule", "ShareModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\share\\share.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { EditorSidebarModule } from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { RegisterNewPlayerComponent } from 'app/pages/registration/register-new-player/register-new-player.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { CoreCommonModule } from '@core/common.module';\r\n\r\n@NgModule({\r\n  declarations: [RegisterNewPlayerComponent],\r\n  imports: [\r\n    CommonModule,\r\n    EditorSidebarModule,\r\n    TranslateModule,\r\n    CoreCommonModule,\r\n  ],\r\n  exports: [RegisterNewPlayerComponent],\r\n})\r\nexport class ShareModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
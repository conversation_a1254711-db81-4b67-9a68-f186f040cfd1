{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function dematerialize() {\n  return function dematerializeOperatorFunction(source) {\n    return source.lift(new DeMaterializeOperator());\n  };\n}\nclass DeMaterializeOperator {\n  call(subscriber, source) {\n    return source.subscribe(new DeMaterializeSubscriber(subscriber));\n  }\n}\nclass DeMaterializeSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n  _next(value) {\n    value.observe(this.destination);\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "dematerialize", "dematerializeOperatorFunction", "source", "lift", "DeMaterializeOperator", "call", "subscriber", "subscribe", "DeMaterializeSubscriber", "constructor", "destination", "_next", "value", "observe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/dematerialize.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function dematerialize() {\n    return function dematerializeOperatorFunction(source) {\n        return source.lift(new DeMaterializeOperator());\n    };\n}\nclass DeMaterializeOperator {\n    call(subscriber, source) {\n        return source.subscribe(new DeMaterializeSubscriber(subscriber));\n    }\n}\nclass DeMaterializeSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n    }\n    _next(value) {\n        value.observe(this.destination);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC5B,OAAO,SAASC,6BAA6BA,CAACC,MAAM,EAAE;IAClD,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,qBAAqB,CAAC,CAAC,CAAC;EACnD,CAAC;AACL;AACA,MAAMA,qBAAqB,CAAC;EACxBC,IAAIA,CAACC,UAAU,EAAEJ,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACK,SAAS,CAAC,IAAIC,uBAAuB,CAACF,UAAU,CAAC,CAAC;EACpE;AACJ;AACA,MAAME,uBAAuB,SAAST,UAAU,CAAC;EAC7CU,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;EACtB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,OAAO,CAAC,IAAI,CAACH,WAAW,CAAC;EACnC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
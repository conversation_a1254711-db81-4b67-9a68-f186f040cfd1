{"ast": null, "code": "/*!\n * lightgallery | 2.7.2 | September 20th 2023\n * http://www.lightgalleryjs.com/\n * Copyright (c) 2020 Sachin Neravath;\n * @license GPLv3\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar thumbnailsSettings = {\n  thumbnail: true,\n  animateThumb: true,\n  currentPagerPosition: 'middle',\n  alignThumbnails: 'middle',\n  thumbWidth: 100,\n  thumbHeight: '80px',\n  thumbMargin: 5,\n  appendThumbnailsTo: '.lg-components',\n  toggleThumb: false,\n  enableThumbDrag: true,\n  enableThumbSwipe: true,\n  thumbnailSwipeThreshold: 10,\n  loadYouTubeThumbnail: true,\n  youTubeThumbSize: 1,\n  thumbnailPluginStrings: {\n    toggleThumbnails: 'Toggle thumbnails'\n  }\n};\n\n/**\r\n * List of lightGallery events\r\n * All events should be documented here\r\n * Below interfaces are used to build the website documentations\r\n * */\nvar lGEvents = {\n  afterAppendSlide: 'lgAfterAppendSlide',\n  init: 'lgInit',\n  hasVideo: 'lgHasVideo',\n  containerResize: 'lgContainerResize',\n  updateSlides: 'lgUpdateSlides',\n  afterAppendSubHtml: 'lgAfterAppendSubHtml',\n  beforeOpen: 'lgBeforeOpen',\n  afterOpen: 'lgAfterOpen',\n  slideItemLoad: 'lgSlideItemLoad',\n  beforeSlide: 'lgBeforeSlide',\n  afterSlide: 'lgAfterSlide',\n  posterClick: 'lgPosterClick',\n  dragStart: 'lgDragStart',\n  dragMove: 'lgDragMove',\n  dragEnd: 'lgDragEnd',\n  beforeNextSlide: 'lgBeforeNextSlide',\n  beforePrevSlide: 'lgBeforePrevSlide',\n  beforeClose: 'lgBeforeClose',\n  afterClose: 'lgAfterClose',\n  rotateLeft: 'lgRotateLeft',\n  rotateRight: 'lgRotateRight',\n  flipHorizontal: 'lgFlipHorizontal',\n  flipVertical: 'lgFlipVertical',\n  autoplay: 'lgAutoplay',\n  autoplayStart: 'lgAutoplayStart',\n  autoplayStop: 'lgAutoplayStop'\n};\nvar Thumbnail = /** @class */function () {\n  function Thumbnail(instance, $LG) {\n    this.thumbOuterWidth = 0;\n    this.thumbTotalWidth = 0;\n    this.translateX = 0;\n    this.thumbClickable = false;\n    // get lightGallery core plugin instance\n    this.core = instance;\n    this.$LG = $LG;\n    return this;\n  }\n  Thumbnail.prototype.init = function () {\n    // extend module default settings with lightGallery core settings\n    this.settings = __assign(__assign({}, thumbnailsSettings), this.core.settings);\n    this.thumbOuterWidth = 0;\n    this.thumbTotalWidth = this.core.galleryItems.length * (this.settings.thumbWidth + this.settings.thumbMargin);\n    // Thumbnail animation value\n    this.translateX = 0;\n    this.setAnimateThumbStyles();\n    if (!this.core.settings.allowMediaOverlap) {\n      this.settings.toggleThumb = false;\n    }\n    if (this.settings.thumbnail) {\n      this.build();\n      if (this.settings.animateThumb) {\n        if (this.settings.enableThumbDrag) {\n          this.enableThumbDrag();\n        }\n        if (this.settings.enableThumbSwipe) {\n          this.enableThumbSwipe();\n        }\n        this.thumbClickable = false;\n      } else {\n        this.thumbClickable = true;\n      }\n      this.toggleThumbBar();\n      this.thumbKeyPress();\n    }\n  };\n  Thumbnail.prototype.build = function () {\n    var _this = this;\n    this.setThumbMarkup();\n    this.manageActiveClassOnSlideChange();\n    this.$lgThumb.first().on('click.lg touchend.lg', function (e) {\n      var $target = _this.$LG(e.target);\n      if (!$target.hasAttribute('data-lg-item-id')) {\n        return;\n      }\n      setTimeout(function () {\n        // In IE9 and bellow touch does not support\n        // Go to slide if browser does not support css transitions\n        if (_this.thumbClickable && !_this.core.lgBusy) {\n          var index = parseInt($target.attr('data-lg-item-id'));\n          _this.core.slide(index, false, true, false);\n        }\n      }, 50);\n    });\n    this.core.LGel.on(lGEvents.beforeSlide + \".thumb\", function (event) {\n      var index = event.detail.index;\n      _this.animateThumb(index);\n    });\n    this.core.LGel.on(lGEvents.beforeOpen + \".thumb\", function () {\n      _this.thumbOuterWidth = _this.core.outer.get().offsetWidth;\n    });\n    this.core.LGel.on(lGEvents.updateSlides + \".thumb\", function () {\n      _this.rebuildThumbnails();\n    });\n    this.core.LGel.on(lGEvents.containerResize + \".thumb\", function () {\n      if (!_this.core.lgOpened) return;\n      setTimeout(function () {\n        _this.thumbOuterWidth = _this.core.outer.get().offsetWidth;\n        _this.animateThumb(_this.core.index);\n        _this.thumbOuterWidth = _this.core.outer.get().offsetWidth;\n      }, 50);\n    });\n  };\n  Thumbnail.prototype.setThumbMarkup = function () {\n    var thumbOuterClassNames = 'lg-thumb-outer ';\n    if (this.settings.alignThumbnails) {\n      thumbOuterClassNames += \"lg-thumb-align-\" + this.settings.alignThumbnails;\n    }\n    var html = \"<div class=\\\"\" + thumbOuterClassNames + \"\\\">\\n        <div class=\\\"lg-thumb lg-group\\\">\\n        </div>\\n        </div>\";\n    this.core.outer.addClass('lg-has-thumb');\n    if (this.settings.appendThumbnailsTo === '.lg-components') {\n      this.core.$lgComponents.append(html);\n    } else {\n      this.core.outer.append(html);\n    }\n    this.$thumbOuter = this.core.outer.find('.lg-thumb-outer').first();\n    this.$lgThumb = this.core.outer.find('.lg-thumb').first();\n    if (this.settings.animateThumb) {\n      this.core.outer.find('.lg-thumb').css('transition-duration', this.core.settings.speed + 'ms').css('width', this.thumbTotalWidth + 'px').css('position', 'relative');\n    }\n    this.setThumbItemHtml(this.core.galleryItems);\n  };\n  Thumbnail.prototype.enableThumbDrag = function () {\n    var _this = this;\n    var thumbDragUtils = {\n      cords: {\n        startX: 0,\n        endX: 0\n      },\n      isMoved: false,\n      newTranslateX: 0,\n      startTime: new Date(),\n      endTime: new Date(),\n      touchMoveTime: 0\n    };\n    var isDragging = false;\n    this.$thumbOuter.addClass('lg-grab');\n    this.core.outer.find('.lg-thumb').first().on('mousedown.lg.thumb', function (e) {\n      if (_this.thumbTotalWidth > _this.thumbOuterWidth) {\n        // execute only on .lg-object\n        e.preventDefault();\n        thumbDragUtils.cords.startX = e.pageX;\n        thumbDragUtils.startTime = new Date();\n        _this.thumbClickable = false;\n        isDragging = true;\n        // ** Fix for webkit cursor issue https://code.google.com/p/chromium/issues/detail?id=26723\n        _this.core.outer.get().scrollLeft += 1;\n        _this.core.outer.get().scrollLeft -= 1;\n        // *\n        _this.$thumbOuter.removeClass('lg-grab').addClass('lg-grabbing');\n      }\n    });\n    this.$LG(window).on(\"mousemove.lg.thumb.global\" + this.core.lgId, function (e) {\n      if (!_this.core.lgOpened) return;\n      if (isDragging) {\n        thumbDragUtils.cords.endX = e.pageX;\n        thumbDragUtils = _this.onThumbTouchMove(thumbDragUtils);\n      }\n    });\n    this.$LG(window).on(\"mouseup.lg.thumb.global\" + this.core.lgId, function () {\n      if (!_this.core.lgOpened) return;\n      if (thumbDragUtils.isMoved) {\n        thumbDragUtils = _this.onThumbTouchEnd(thumbDragUtils);\n      } else {\n        _this.thumbClickable = true;\n      }\n      if (isDragging) {\n        isDragging = false;\n        _this.$thumbOuter.removeClass('lg-grabbing').addClass('lg-grab');\n      }\n    });\n  };\n  Thumbnail.prototype.enableThumbSwipe = function () {\n    var _this = this;\n    var thumbDragUtils = {\n      cords: {\n        startX: 0,\n        endX: 0\n      },\n      isMoved: false,\n      newTranslateX: 0,\n      startTime: new Date(),\n      endTime: new Date(),\n      touchMoveTime: 0\n    };\n    this.$lgThumb.on('touchstart.lg', function (e) {\n      if (_this.thumbTotalWidth > _this.thumbOuterWidth) {\n        e.preventDefault();\n        thumbDragUtils.cords.startX = e.targetTouches[0].pageX;\n        _this.thumbClickable = false;\n        thumbDragUtils.startTime = new Date();\n      }\n    });\n    this.$lgThumb.on('touchmove.lg', function (e) {\n      if (_this.thumbTotalWidth > _this.thumbOuterWidth) {\n        e.preventDefault();\n        thumbDragUtils.cords.endX = e.targetTouches[0].pageX;\n        thumbDragUtils = _this.onThumbTouchMove(thumbDragUtils);\n      }\n    });\n    this.$lgThumb.on('touchend.lg', function () {\n      if (thumbDragUtils.isMoved) {\n        thumbDragUtils = _this.onThumbTouchEnd(thumbDragUtils);\n      } else {\n        _this.thumbClickable = true;\n      }\n    });\n  };\n  // Rebuild thumbnails\n  Thumbnail.prototype.rebuildThumbnails = function () {\n    var _this = this;\n    // Remove transitions\n    this.$thumbOuter.addClass('lg-rebuilding-thumbnails');\n    setTimeout(function () {\n      _this.thumbTotalWidth = _this.core.galleryItems.length * (_this.settings.thumbWidth + _this.settings.thumbMargin);\n      _this.$lgThumb.css('width', _this.thumbTotalWidth + 'px');\n      _this.$lgThumb.empty();\n      _this.setThumbItemHtml(_this.core.galleryItems);\n      _this.animateThumb(_this.core.index);\n    }, 50);\n    setTimeout(function () {\n      _this.$thumbOuter.removeClass('lg-rebuilding-thumbnails');\n    }, 200);\n  };\n  // @ts-check\n  Thumbnail.prototype.setTranslate = function (value) {\n    this.$lgThumb.css('transform', 'translate3d(-' + value + 'px, 0px, 0px)');\n  };\n  Thumbnail.prototype.getPossibleTransformX = function (left) {\n    if (left > this.thumbTotalWidth - this.thumbOuterWidth) {\n      left = this.thumbTotalWidth - this.thumbOuterWidth;\n    }\n    if (left < 0) {\n      left = 0;\n    }\n    return left;\n  };\n  Thumbnail.prototype.animateThumb = function (index) {\n    this.$lgThumb.css('transition-duration', this.core.settings.speed + 'ms');\n    if (this.settings.animateThumb) {\n      var position = 0;\n      switch (this.settings.currentPagerPosition) {\n        case 'left':\n          position = 0;\n          break;\n        case 'middle':\n          position = this.thumbOuterWidth / 2 - this.settings.thumbWidth / 2;\n          break;\n        case 'right':\n          position = this.thumbOuterWidth - this.settings.thumbWidth;\n      }\n      this.translateX = (this.settings.thumbWidth + this.settings.thumbMargin) * index - 1 - position;\n      if (this.translateX > this.thumbTotalWidth - this.thumbOuterWidth) {\n        this.translateX = this.thumbTotalWidth - this.thumbOuterWidth;\n      }\n      if (this.translateX < 0) {\n        this.translateX = 0;\n      }\n      this.setTranslate(this.translateX);\n    }\n  };\n  Thumbnail.prototype.onThumbTouchMove = function (thumbDragUtils) {\n    thumbDragUtils.newTranslateX = this.translateX;\n    thumbDragUtils.isMoved = true;\n    thumbDragUtils.touchMoveTime = new Date().valueOf();\n    thumbDragUtils.newTranslateX -= thumbDragUtils.cords.endX - thumbDragUtils.cords.startX;\n    thumbDragUtils.newTranslateX = this.getPossibleTransformX(thumbDragUtils.newTranslateX);\n    // move current slide\n    this.setTranslate(thumbDragUtils.newTranslateX);\n    this.$thumbOuter.addClass('lg-dragging');\n    return thumbDragUtils;\n  };\n  Thumbnail.prototype.onThumbTouchEnd = function (thumbDragUtils) {\n    thumbDragUtils.isMoved = false;\n    thumbDragUtils.endTime = new Date();\n    this.$thumbOuter.removeClass('lg-dragging');\n    var touchDuration = thumbDragUtils.endTime.valueOf() - thumbDragUtils.startTime.valueOf();\n    var distanceXnew = thumbDragUtils.cords.endX - thumbDragUtils.cords.startX;\n    var speedX = Math.abs(distanceXnew) / touchDuration;\n    // Some magical numbers\n    // Can be improved\n    if (speedX > 0.15 && thumbDragUtils.endTime.valueOf() - thumbDragUtils.touchMoveTime < 30) {\n      speedX += 1;\n      if (speedX > 2) {\n        speedX += 1;\n      }\n      speedX = speedX + speedX * (Math.abs(distanceXnew) / this.thumbOuterWidth);\n      this.$lgThumb.css('transition-duration', Math.min(speedX - 1, 2) + 'settings');\n      distanceXnew = distanceXnew * speedX;\n      this.translateX = this.getPossibleTransformX(this.translateX - distanceXnew);\n      this.setTranslate(this.translateX);\n    } else {\n      this.translateX = thumbDragUtils.newTranslateX;\n    }\n    if (Math.abs(thumbDragUtils.cords.endX - thumbDragUtils.cords.startX) < this.settings.thumbnailSwipeThreshold) {\n      this.thumbClickable = true;\n    }\n    return thumbDragUtils;\n  };\n  Thumbnail.prototype.getThumbHtml = function (thumb, index, alt) {\n    var slideVideoInfo = this.core.galleryItems[index].__slideVideoInfo || {};\n    var thumbImg;\n    if (slideVideoInfo.youtube) {\n      if (this.settings.loadYouTubeThumbnail) {\n        thumbImg = '//img.youtube.com/vi/' + slideVideoInfo.youtube[1] + '/' + this.settings.youTubeThumbSize + '.jpg';\n      } else {\n        thumbImg = thumb;\n      }\n    } else {\n      thumbImg = thumb;\n    }\n    var altAttr = alt ? 'alt=\"' + alt + '\"' : '';\n    return \"<div data-lg-item-id=\\\"\" + index + \"\\\" class=\\\"lg-thumb-item \" + (index === this.core.index ? ' active' : '') + \"\\\"\\n        style=\\\"width:\" + this.settings.thumbWidth + \"px; height: \" + this.settings.thumbHeight + \";\\n            margin-right: \" + this.settings.thumbMargin + \"px;\\\">\\n            <img \" + altAttr + \" data-lg-item-id=\\\"\" + index + \"\\\" src=\\\"\" + thumbImg + \"\\\" />\\n        </div>\";\n  };\n  Thumbnail.prototype.getThumbItemHtml = function (items) {\n    var thumbList = '';\n    for (var i = 0; i < items.length; i++) {\n      thumbList += this.getThumbHtml(items[i].thumb, i, items[i].alt);\n    }\n    return thumbList;\n  };\n  Thumbnail.prototype.setThumbItemHtml = function (items) {\n    var thumbList = this.getThumbItemHtml(items);\n    this.$lgThumb.html(thumbList);\n  };\n  Thumbnail.prototype.setAnimateThumbStyles = function () {\n    if (this.settings.animateThumb) {\n      this.core.outer.addClass('lg-animate-thumb');\n    }\n  };\n  // Manage thumbnail active calss\n  Thumbnail.prototype.manageActiveClassOnSlideChange = function () {\n    var _this = this;\n    // manage active class for thumbnail\n    this.core.LGel.on(lGEvents.beforeSlide + \".thumb\", function (event) {\n      var $thumb = _this.core.outer.find('.lg-thumb-item');\n      var index = event.detail.index;\n      $thumb.removeClass('active');\n      $thumb.eq(index).addClass('active');\n    });\n  };\n  // Toggle thumbnail bar\n  Thumbnail.prototype.toggleThumbBar = function () {\n    var _this = this;\n    if (this.settings.toggleThumb) {\n      this.core.outer.addClass('lg-can-toggle');\n      this.core.$toolbar.append('<button type=\"button\" aria-label=\"' + this.settings.thumbnailPluginStrings['toggleThumbnails'] + '\" class=\"lg-toggle-thumb lg-icon\"></button>');\n      this.core.outer.find('.lg-toggle-thumb').first().on('click.lg', function () {\n        _this.core.outer.toggleClass('lg-components-open');\n      });\n    }\n  };\n  Thumbnail.prototype.thumbKeyPress = function () {\n    var _this = this;\n    this.$LG(window).on(\"keydown.lg.thumb.global\" + this.core.lgId, function (e) {\n      if (!_this.core.lgOpened || !_this.settings.toggleThumb) return;\n      if (e.keyCode === 38) {\n        e.preventDefault();\n        _this.core.outer.addClass('lg-components-open');\n      } else if (e.keyCode === 40) {\n        e.preventDefault();\n        _this.core.outer.removeClass('lg-components-open');\n      }\n    });\n  };\n  Thumbnail.prototype.destroy = function () {\n    if (this.settings.thumbnail) {\n      this.$LG(window).off(\".lg.thumb.global\" + this.core.lgId);\n      this.core.LGel.off('.lg.thumb');\n      this.core.LGel.off('.thumb');\n      this.$thumbOuter.remove();\n      this.core.outer.removeClass('lg-has-thumb');\n    }\n  };\n  return Thumbnail;\n}();\nexport default Thumbnail;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "thumbnailsSettings", "thumbnail", "animateThumb", "currentPagerPosition", "alignThumbnails", "thumbWidth", "thumbHeight", "<PERSON><PERSON><PERSON><PERSON>", "appendThumbnailsTo", "toggleThumb", "enableThumbDrag", "enableThumbSwipe", "thumbnailSwipe<PERSON><PERSON><PERSON><PERSON>", "loadYouTubeThumbnail", "youTubeThumbSize", "thumbnailPluginStrings", "toggleThumbnails", "lGEvents", "afterAppendSlide", "init", "hasVideo", "containerResize", "updateSlides", "afterAppendSubHtml", "beforeOpen", "afterOpen", "slideItemLoad", "beforeSlide", "afterSlide", "posterClick", "dragStart", "dragMove", "dragEnd", "beforeNextSlide", "beforePrevSlide", "beforeClose", "afterClose", "rotateLeft", "rotateRight", "flipHorizontal", "flipVertical", "autoplay", "autoplayStart", "autoplayStop", "<PERSON><PERSON><PERSON><PERSON>", "instance", "$LG", "thumbOuterWidth", "thumbTotalWidth", "translateX", "thumbClickable", "core", "settings", "galleryItems", "setAnimateThumbStyles", "allowMediaOverlap", "build", "toggle<PERSON><PERSON><PERSON><PERSON><PERSON>", "thumb<PERSON>eyPress", "_this", "setThumbMarkup", "manageActiveClassOnSlideChange", "$lgThumb", "first", "on", "e", "$target", "target", "hasAttribute", "setTimeout", "lgBusy", "index", "parseInt", "attr", "slide", "LGel", "event", "detail", "outer", "get", "offsetWidth", "rebuildThumbnails", "lgOpened", "thumbOuterClassNames", "html", "addClass", "$lgComponents", "append", "$thumbOuter", "find", "css", "speed", "setThumbItemHtml", "thumbDragUtils", "cords", "startX", "endX", "isMoved", "newTranslateX", "startTime", "Date", "endTime", "touchMoveTime", "isDragging", "preventDefault", "pageX", "scrollLeft", "removeClass", "window", "lgId", "onThumbTouchMove", "onThumbTouchEnd", "targetTouches", "empty", "setTranslate", "value", "getPossibleTransformX", "left", "position", "valueOf", "touchDuration", "distanceXnew", "speedX", "Math", "abs", "min", "getThumbHtml", "thumb", "alt", "slideVideoInfo", "__slideVideoInfo", "thumbImg", "youtube", "altAttr", "getThumbItemHtml", "items", "thumbList", "$thumb", "eq", "$toolbar", "toggleClass", "keyCode", "destroy", "off", "remove"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lightgallery/plugins/thumbnail/lg-thumbnail.es5.js"], "sourcesContent": ["/*!\n * lightgallery | 2.7.2 | September 20th 2023\n * http://www.lightgalleryjs.com/\n * Copyright (c) 2020 Sachin Neravath;\n * @license GPLv3\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar thumbnailsSettings = {\r\n    thumbnail: true,\r\n    animateThumb: true,\r\n    currentPagerPosition: 'middle',\r\n    alignThumbnails: 'middle',\r\n    thumbWidth: 100,\r\n    thumbHeight: '80px',\r\n    thumbMargin: 5,\r\n    appendThumbnailsTo: '.lg-components',\r\n    toggleThumb: false,\r\n    enableThumbDrag: true,\r\n    enableThumbSwipe: true,\r\n    thumbnailSwipeThreshold: 10,\r\n    loadYouTubeThumbnail: true,\r\n    youTubeThumbSize: 1,\r\n    thumbnailPluginStrings: {\r\n        toggleThumbnails: 'Toggle thumbnails',\r\n    },\r\n};\n\n/**\r\n * List of lightGallery events\r\n * All events should be documented here\r\n * Below interfaces are used to build the website documentations\r\n * */\r\nvar lGEvents = {\r\n    afterAppendSlide: 'lgAfterAppendSlide',\r\n    init: 'lgInit',\r\n    hasVideo: 'lgHasVideo',\r\n    containerResize: 'lgContainerResize',\r\n    updateSlides: 'lgUpdateSlides',\r\n    afterAppendSubHtml: 'lgAfterAppendSubHtml',\r\n    beforeOpen: 'lgBeforeOpen',\r\n    afterOpen: 'lgAfterOpen',\r\n    slideItemLoad: 'lgSlideItemLoad',\r\n    beforeSlide: 'lgBeforeSlide',\r\n    afterSlide: 'lgAfterSlide',\r\n    posterClick: 'lgPosterClick',\r\n    dragStart: 'lgDragStart',\r\n    dragMove: 'lgDragMove',\r\n    dragEnd: 'lgDragEnd',\r\n    beforeNextSlide: 'lgBeforeNextSlide',\r\n    beforePrevSlide: 'lgBeforePrevSlide',\r\n    beforeClose: 'lgBeforeClose',\r\n    afterClose: 'lgAfterClose',\r\n    rotateLeft: 'lgRotateLeft',\r\n    rotateRight: 'lgRotateRight',\r\n    flipHorizontal: 'lgFlipHorizontal',\r\n    flipVertical: 'lgFlipVertical',\r\n    autoplay: 'lgAutoplay',\r\n    autoplayStart: 'lgAutoplayStart',\r\n    autoplayStop: 'lgAutoplayStop',\r\n};\n\nvar Thumbnail = /** @class */ (function () {\r\n    function Thumbnail(instance, $LG) {\r\n        this.thumbOuterWidth = 0;\r\n        this.thumbTotalWidth = 0;\r\n        this.translateX = 0;\r\n        this.thumbClickable = false;\r\n        // get lightGallery core plugin instance\r\n        this.core = instance;\r\n        this.$LG = $LG;\r\n        return this;\r\n    }\r\n    Thumbnail.prototype.init = function () {\r\n        // extend module default settings with lightGallery core settings\r\n        this.settings = __assign(__assign({}, thumbnailsSettings), this.core.settings);\r\n        this.thumbOuterWidth = 0;\r\n        this.thumbTotalWidth =\r\n            this.core.galleryItems.length *\r\n                (this.settings.thumbWidth + this.settings.thumbMargin);\r\n        // Thumbnail animation value\r\n        this.translateX = 0;\r\n        this.setAnimateThumbStyles();\r\n        if (!this.core.settings.allowMediaOverlap) {\r\n            this.settings.toggleThumb = false;\r\n        }\r\n        if (this.settings.thumbnail) {\r\n            this.build();\r\n            if (this.settings.animateThumb) {\r\n                if (this.settings.enableThumbDrag) {\r\n                    this.enableThumbDrag();\r\n                }\r\n                if (this.settings.enableThumbSwipe) {\r\n                    this.enableThumbSwipe();\r\n                }\r\n                this.thumbClickable = false;\r\n            }\r\n            else {\r\n                this.thumbClickable = true;\r\n            }\r\n            this.toggleThumbBar();\r\n            this.thumbKeyPress();\r\n        }\r\n    };\r\n    Thumbnail.prototype.build = function () {\r\n        var _this = this;\r\n        this.setThumbMarkup();\r\n        this.manageActiveClassOnSlideChange();\r\n        this.$lgThumb.first().on('click.lg touchend.lg', function (e) {\r\n            var $target = _this.$LG(e.target);\r\n            if (!$target.hasAttribute('data-lg-item-id')) {\r\n                return;\r\n            }\r\n            setTimeout(function () {\r\n                // In IE9 and bellow touch does not support\r\n                // Go to slide if browser does not support css transitions\r\n                if (_this.thumbClickable && !_this.core.lgBusy) {\r\n                    var index = parseInt($target.attr('data-lg-item-id'));\r\n                    _this.core.slide(index, false, true, false);\r\n                }\r\n            }, 50);\r\n        });\r\n        this.core.LGel.on(lGEvents.beforeSlide + \".thumb\", function (event) {\r\n            var index = event.detail.index;\r\n            _this.animateThumb(index);\r\n        });\r\n        this.core.LGel.on(lGEvents.beforeOpen + \".thumb\", function () {\r\n            _this.thumbOuterWidth = _this.core.outer.get().offsetWidth;\r\n        });\r\n        this.core.LGel.on(lGEvents.updateSlides + \".thumb\", function () {\r\n            _this.rebuildThumbnails();\r\n        });\r\n        this.core.LGel.on(lGEvents.containerResize + \".thumb\", function () {\r\n            if (!_this.core.lgOpened)\r\n                return;\r\n            setTimeout(function () {\r\n                _this.thumbOuterWidth = _this.core.outer.get().offsetWidth;\r\n                _this.animateThumb(_this.core.index);\r\n                _this.thumbOuterWidth = _this.core.outer.get().offsetWidth;\r\n            }, 50);\r\n        });\r\n    };\r\n    Thumbnail.prototype.setThumbMarkup = function () {\r\n        var thumbOuterClassNames = 'lg-thumb-outer ';\r\n        if (this.settings.alignThumbnails) {\r\n            thumbOuterClassNames += \"lg-thumb-align-\" + this.settings.alignThumbnails;\r\n        }\r\n        var html = \"<div class=\\\"\" + thumbOuterClassNames + \"\\\">\\n        <div class=\\\"lg-thumb lg-group\\\">\\n        </div>\\n        </div>\";\r\n        this.core.outer.addClass('lg-has-thumb');\r\n        if (this.settings.appendThumbnailsTo === '.lg-components') {\r\n            this.core.$lgComponents.append(html);\r\n        }\r\n        else {\r\n            this.core.outer.append(html);\r\n        }\r\n        this.$thumbOuter = this.core.outer.find('.lg-thumb-outer').first();\r\n        this.$lgThumb = this.core.outer.find('.lg-thumb').first();\r\n        if (this.settings.animateThumb) {\r\n            this.core.outer\r\n                .find('.lg-thumb')\r\n                .css('transition-duration', this.core.settings.speed + 'ms')\r\n                .css('width', this.thumbTotalWidth + 'px')\r\n                .css('position', 'relative');\r\n        }\r\n        this.setThumbItemHtml(this.core.galleryItems);\r\n    };\r\n    Thumbnail.prototype.enableThumbDrag = function () {\r\n        var _this = this;\r\n        var thumbDragUtils = {\r\n            cords: {\r\n                startX: 0,\r\n                endX: 0,\r\n            },\r\n            isMoved: false,\r\n            newTranslateX: 0,\r\n            startTime: new Date(),\r\n            endTime: new Date(),\r\n            touchMoveTime: 0,\r\n        };\r\n        var isDragging = false;\r\n        this.$thumbOuter.addClass('lg-grab');\r\n        this.core.outer\r\n            .find('.lg-thumb')\r\n            .first()\r\n            .on('mousedown.lg.thumb', function (e) {\r\n            if (_this.thumbTotalWidth > _this.thumbOuterWidth) {\r\n                // execute only on .lg-object\r\n                e.preventDefault();\r\n                thumbDragUtils.cords.startX = e.pageX;\r\n                thumbDragUtils.startTime = new Date();\r\n                _this.thumbClickable = false;\r\n                isDragging = true;\r\n                // ** Fix for webkit cursor issue https://code.google.com/p/chromium/issues/detail?id=26723\r\n                _this.core.outer.get().scrollLeft += 1;\r\n                _this.core.outer.get().scrollLeft -= 1;\r\n                // *\r\n                _this.$thumbOuter\r\n                    .removeClass('lg-grab')\r\n                    .addClass('lg-grabbing');\r\n            }\r\n        });\r\n        this.$LG(window).on(\"mousemove.lg.thumb.global\" + this.core.lgId, function (e) {\r\n            if (!_this.core.lgOpened)\r\n                return;\r\n            if (isDragging) {\r\n                thumbDragUtils.cords.endX = e.pageX;\r\n                thumbDragUtils = _this.onThumbTouchMove(thumbDragUtils);\r\n            }\r\n        });\r\n        this.$LG(window).on(\"mouseup.lg.thumb.global\" + this.core.lgId, function () {\r\n            if (!_this.core.lgOpened)\r\n                return;\r\n            if (thumbDragUtils.isMoved) {\r\n                thumbDragUtils = _this.onThumbTouchEnd(thumbDragUtils);\r\n            }\r\n            else {\r\n                _this.thumbClickable = true;\r\n            }\r\n            if (isDragging) {\r\n                isDragging = false;\r\n                _this.$thumbOuter.removeClass('lg-grabbing').addClass('lg-grab');\r\n            }\r\n        });\r\n    };\r\n    Thumbnail.prototype.enableThumbSwipe = function () {\r\n        var _this = this;\r\n        var thumbDragUtils = {\r\n            cords: {\r\n                startX: 0,\r\n                endX: 0,\r\n            },\r\n            isMoved: false,\r\n            newTranslateX: 0,\r\n            startTime: new Date(),\r\n            endTime: new Date(),\r\n            touchMoveTime: 0,\r\n        };\r\n        this.$lgThumb.on('touchstart.lg', function (e) {\r\n            if (_this.thumbTotalWidth > _this.thumbOuterWidth) {\r\n                e.preventDefault();\r\n                thumbDragUtils.cords.startX = e.targetTouches[0].pageX;\r\n                _this.thumbClickable = false;\r\n                thumbDragUtils.startTime = new Date();\r\n            }\r\n        });\r\n        this.$lgThumb.on('touchmove.lg', function (e) {\r\n            if (_this.thumbTotalWidth > _this.thumbOuterWidth) {\r\n                e.preventDefault();\r\n                thumbDragUtils.cords.endX = e.targetTouches[0].pageX;\r\n                thumbDragUtils = _this.onThumbTouchMove(thumbDragUtils);\r\n            }\r\n        });\r\n        this.$lgThumb.on('touchend.lg', function () {\r\n            if (thumbDragUtils.isMoved) {\r\n                thumbDragUtils = _this.onThumbTouchEnd(thumbDragUtils);\r\n            }\r\n            else {\r\n                _this.thumbClickable = true;\r\n            }\r\n        });\r\n    };\r\n    // Rebuild thumbnails\r\n    Thumbnail.prototype.rebuildThumbnails = function () {\r\n        var _this = this;\r\n        // Remove transitions\r\n        this.$thumbOuter.addClass('lg-rebuilding-thumbnails');\r\n        setTimeout(function () {\r\n            _this.thumbTotalWidth =\r\n                _this.core.galleryItems.length *\r\n                    (_this.settings.thumbWidth + _this.settings.thumbMargin);\r\n            _this.$lgThumb.css('width', _this.thumbTotalWidth + 'px');\r\n            _this.$lgThumb.empty();\r\n            _this.setThumbItemHtml(_this.core.galleryItems);\r\n            _this.animateThumb(_this.core.index);\r\n        }, 50);\r\n        setTimeout(function () {\r\n            _this.$thumbOuter.removeClass('lg-rebuilding-thumbnails');\r\n        }, 200);\r\n    };\r\n    // @ts-check\r\n    Thumbnail.prototype.setTranslate = function (value) {\r\n        this.$lgThumb.css('transform', 'translate3d(-' + value + 'px, 0px, 0px)');\r\n    };\r\n    Thumbnail.prototype.getPossibleTransformX = function (left) {\r\n        if (left > this.thumbTotalWidth - this.thumbOuterWidth) {\r\n            left = this.thumbTotalWidth - this.thumbOuterWidth;\r\n        }\r\n        if (left < 0) {\r\n            left = 0;\r\n        }\r\n        return left;\r\n    };\r\n    Thumbnail.prototype.animateThumb = function (index) {\r\n        this.$lgThumb.css('transition-duration', this.core.settings.speed + 'ms');\r\n        if (this.settings.animateThumb) {\r\n            var position = 0;\r\n            switch (this.settings.currentPagerPosition) {\r\n                case 'left':\r\n                    position = 0;\r\n                    break;\r\n                case 'middle':\r\n                    position =\r\n                        this.thumbOuterWidth / 2 - this.settings.thumbWidth / 2;\r\n                    break;\r\n                case 'right':\r\n                    position = this.thumbOuterWidth - this.settings.thumbWidth;\r\n            }\r\n            this.translateX =\r\n                (this.settings.thumbWidth + this.settings.thumbMargin) * index -\r\n                    1 -\r\n                    position;\r\n            if (this.translateX > this.thumbTotalWidth - this.thumbOuterWidth) {\r\n                this.translateX = this.thumbTotalWidth - this.thumbOuterWidth;\r\n            }\r\n            if (this.translateX < 0) {\r\n                this.translateX = 0;\r\n            }\r\n            this.setTranslate(this.translateX);\r\n        }\r\n    };\r\n    Thumbnail.prototype.onThumbTouchMove = function (thumbDragUtils) {\r\n        thumbDragUtils.newTranslateX = this.translateX;\r\n        thumbDragUtils.isMoved = true;\r\n        thumbDragUtils.touchMoveTime = new Date().valueOf();\r\n        thumbDragUtils.newTranslateX -=\r\n            thumbDragUtils.cords.endX - thumbDragUtils.cords.startX;\r\n        thumbDragUtils.newTranslateX = this.getPossibleTransformX(thumbDragUtils.newTranslateX);\r\n        // move current slide\r\n        this.setTranslate(thumbDragUtils.newTranslateX);\r\n        this.$thumbOuter.addClass('lg-dragging');\r\n        return thumbDragUtils;\r\n    };\r\n    Thumbnail.prototype.onThumbTouchEnd = function (thumbDragUtils) {\r\n        thumbDragUtils.isMoved = false;\r\n        thumbDragUtils.endTime = new Date();\r\n        this.$thumbOuter.removeClass('lg-dragging');\r\n        var touchDuration = thumbDragUtils.endTime.valueOf() -\r\n            thumbDragUtils.startTime.valueOf();\r\n        var distanceXnew = thumbDragUtils.cords.endX - thumbDragUtils.cords.startX;\r\n        var speedX = Math.abs(distanceXnew) / touchDuration;\r\n        // Some magical numbers\r\n        // Can be improved\r\n        if (speedX > 0.15 &&\r\n            thumbDragUtils.endTime.valueOf() - thumbDragUtils.touchMoveTime < 30) {\r\n            speedX += 1;\r\n            if (speedX > 2) {\r\n                speedX += 1;\r\n            }\r\n            speedX =\r\n                speedX +\r\n                    speedX * (Math.abs(distanceXnew) / this.thumbOuterWidth);\r\n            this.$lgThumb.css('transition-duration', Math.min(speedX - 1, 2) + 'settings');\r\n            distanceXnew = distanceXnew * speedX;\r\n            this.translateX = this.getPossibleTransformX(this.translateX - distanceXnew);\r\n            this.setTranslate(this.translateX);\r\n        }\r\n        else {\r\n            this.translateX = thumbDragUtils.newTranslateX;\r\n        }\r\n        if (Math.abs(thumbDragUtils.cords.endX - thumbDragUtils.cords.startX) <\r\n            this.settings.thumbnailSwipeThreshold) {\r\n            this.thumbClickable = true;\r\n        }\r\n        return thumbDragUtils;\r\n    };\r\n    Thumbnail.prototype.getThumbHtml = function (thumb, index, alt) {\r\n        var slideVideoInfo = this.core.galleryItems[index].__slideVideoInfo || {};\r\n        var thumbImg;\r\n        if (slideVideoInfo.youtube) {\r\n            if (this.settings.loadYouTubeThumbnail) {\r\n                thumbImg =\r\n                    '//img.youtube.com/vi/' +\r\n                        slideVideoInfo.youtube[1] +\r\n                        '/' +\r\n                        this.settings.youTubeThumbSize +\r\n                        '.jpg';\r\n            }\r\n            else {\r\n                thumbImg = thumb;\r\n            }\r\n        }\r\n        else {\r\n            thumbImg = thumb;\r\n        }\r\n        var altAttr = alt ? 'alt=\"' + alt + '\"' : '';\r\n        return \"<div data-lg-item-id=\\\"\" + index + \"\\\" class=\\\"lg-thumb-item \" + (index === this.core.index ? ' active' : '') + \"\\\"\\n        style=\\\"width:\" + this.settings.thumbWidth + \"px; height: \" + this.settings.thumbHeight + \";\\n            margin-right: \" + this.settings.thumbMargin + \"px;\\\">\\n            <img \" + altAttr + \" data-lg-item-id=\\\"\" + index + \"\\\" src=\\\"\" + thumbImg + \"\\\" />\\n        </div>\";\r\n    };\r\n    Thumbnail.prototype.getThumbItemHtml = function (items) {\r\n        var thumbList = '';\r\n        for (var i = 0; i < items.length; i++) {\r\n            thumbList += this.getThumbHtml(items[i].thumb, i, items[i].alt);\r\n        }\r\n        return thumbList;\r\n    };\r\n    Thumbnail.prototype.setThumbItemHtml = function (items) {\r\n        var thumbList = this.getThumbItemHtml(items);\r\n        this.$lgThumb.html(thumbList);\r\n    };\r\n    Thumbnail.prototype.setAnimateThumbStyles = function () {\r\n        if (this.settings.animateThumb) {\r\n            this.core.outer.addClass('lg-animate-thumb');\r\n        }\r\n    };\r\n    // Manage thumbnail active calss\r\n    Thumbnail.prototype.manageActiveClassOnSlideChange = function () {\r\n        var _this = this;\r\n        // manage active class for thumbnail\r\n        this.core.LGel.on(lGEvents.beforeSlide + \".thumb\", function (event) {\r\n            var $thumb = _this.core.outer.find('.lg-thumb-item');\r\n            var index = event.detail.index;\r\n            $thumb.removeClass('active');\r\n            $thumb.eq(index).addClass('active');\r\n        });\r\n    };\r\n    // Toggle thumbnail bar\r\n    Thumbnail.prototype.toggleThumbBar = function () {\r\n        var _this = this;\r\n        if (this.settings.toggleThumb) {\r\n            this.core.outer.addClass('lg-can-toggle');\r\n            this.core.$toolbar.append('<button type=\"button\" aria-label=\"' +\r\n                this.settings.thumbnailPluginStrings['toggleThumbnails'] +\r\n                '\" class=\"lg-toggle-thumb lg-icon\"></button>');\r\n            this.core.outer\r\n                .find('.lg-toggle-thumb')\r\n                .first()\r\n                .on('click.lg', function () {\r\n                _this.core.outer.toggleClass('lg-components-open');\r\n            });\r\n        }\r\n    };\r\n    Thumbnail.prototype.thumbKeyPress = function () {\r\n        var _this = this;\r\n        this.$LG(window).on(\"keydown.lg.thumb.global\" + this.core.lgId, function (e) {\r\n            if (!_this.core.lgOpened || !_this.settings.toggleThumb)\r\n                return;\r\n            if (e.keyCode === 38) {\r\n                e.preventDefault();\r\n                _this.core.outer.addClass('lg-components-open');\r\n            }\r\n            else if (e.keyCode === 40) {\r\n                e.preventDefault();\r\n                _this.core.outer.removeClass('lg-components-open');\r\n            }\r\n        });\r\n    };\r\n    Thumbnail.prototype.destroy = function () {\r\n        if (this.settings.thumbnail) {\r\n            this.$LG(window).off(\".lg.thumb.global\" + this.core.lgId);\r\n            this.core.LGel.off('.lg.thumb');\r\n            this.core.LGel.off('.thumb');\r\n            this.$thumbOuter.remove();\r\n            this.core.outer.removeClass('lg-has-thumb');\r\n        }\r\n    };\r\n    return Thumbnail;\r\n}());\n\nexport default Thumbnail;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,QAAQ,GAAG,SAAAA,CAAA,EAAW;EACtBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASF,QAAQA,CAACG,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAChF;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AAED,IAAIO,kBAAkB,GAAG;EACrBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,oBAAoB,EAAE,QAAQ;EAC9BC,eAAe,EAAE,QAAQ;EACzBC,UAAU,EAAE,GAAG;EACfC,WAAW,EAAE,MAAM;EACnBC,WAAW,EAAE,CAAC;EACdC,kBAAkB,EAAE,gBAAgB;EACpCC,WAAW,EAAE,KAAK;EAClBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,uBAAuB,EAAE,EAAE;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,gBAAgB,EAAE,CAAC;EACnBC,sBAAsB,EAAE;IACpBC,gBAAgB,EAAE;EACtB;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG;EACXC,gBAAgB,EAAE,oBAAoB;EACtCC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,eAAe,EAAE,mBAAmB;EACpCC,YAAY,EAAE,gBAAgB;EAC9BC,kBAAkB,EAAE,sBAAsB;EAC1CC,UAAU,EAAE,cAAc;EAC1BC,SAAS,EAAE,aAAa;EACxBC,aAAa,EAAE,iBAAiB;EAChCC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,SAAS,EAAE,aAAa;EACxBC,QAAQ,EAAE,YAAY;EACtBC,OAAO,EAAE,WAAW;EACpBC,eAAe,EAAE,mBAAmB;EACpCC,eAAe,EAAE,mBAAmB;EACpCC,WAAW,EAAE,eAAe;EAC5BC,UAAU,EAAE,cAAc;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,cAAc,EAAE,kBAAkB;EAClCC,YAAY,EAAE,gBAAgB;EAC9BC,QAAQ,EAAE,YAAY;EACtBC,aAAa,EAAE,iBAAiB;EAChCC,YAAY,EAAE;AAClB,CAAC;AAED,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,QAAQ,EAAEC,GAAG,EAAE;IAC9B,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,IAAI,GAAGN,QAAQ;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,OAAO,IAAI;EACf;EACAF,SAAS,CAAChD,SAAS,CAACuB,IAAI,GAAG,YAAY;IACnC;IACA,IAAI,CAACiC,QAAQ,GAAGlE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEc,kBAAkB,CAAC,EAAE,IAAI,CAACmD,IAAI,CAACC,QAAQ,CAAC;IAC9E,IAAI,CAACL,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAChB,IAAI,CAACG,IAAI,CAACE,YAAY,CAAC3D,MAAM,IACxB,IAAI,CAAC0D,QAAQ,CAAC/C,UAAU,GAAG,IAAI,CAAC+C,QAAQ,CAAC7C,WAAW,CAAC;IAC9D;IACA,IAAI,CAAC0C,UAAU,GAAG,CAAC;IACnB,IAAI,CAACK,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAC,IAAI,CAACH,IAAI,CAACC,QAAQ,CAACG,iBAAiB,EAAE;MACvC,IAAI,CAACH,QAAQ,CAAC3C,WAAW,GAAG,KAAK;IACrC;IACA,IAAI,IAAI,CAAC2C,QAAQ,CAACnD,SAAS,EAAE;MACzB,IAAI,CAACuD,KAAK,CAAC,CAAC;MACZ,IAAI,IAAI,CAACJ,QAAQ,CAAClD,YAAY,EAAE;QAC5B,IAAI,IAAI,CAACkD,QAAQ,CAAC1C,eAAe,EAAE;UAC/B,IAAI,CAACA,eAAe,CAAC,CAAC;QAC1B;QACA,IAAI,IAAI,CAAC0C,QAAQ,CAACzC,gBAAgB,EAAE;UAChC,IAAI,CAACA,gBAAgB,CAAC,CAAC;QAC3B;QACA,IAAI,CAACuC,cAAc,GAAG,KAAK;MAC/B,CAAC,MACI;QACD,IAAI,CAACA,cAAc,GAAG,IAAI;MAC9B;MACA,IAAI,CAACO,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ,CAAC;EACDd,SAAS,CAAChD,SAAS,CAAC4D,KAAK,GAAG,YAAY;IACpC,IAAIG,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,8BAA8B,CAAC,CAAC;IACrC,IAAI,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC,CAACC,EAAE,CAAC,sBAAsB,EAAE,UAAUC,CAAC,EAAE;MAC1D,IAAIC,OAAO,GAAGP,KAAK,CAACb,GAAG,CAACmB,CAAC,CAACE,MAAM,CAAC;MACjC,IAAI,CAACD,OAAO,CAACE,YAAY,CAAC,iBAAiB,CAAC,EAAE;QAC1C;MACJ;MACAC,UAAU,CAAC,YAAY;QACnB;QACA;QACA,IAAIV,KAAK,CAACT,cAAc,IAAI,CAACS,KAAK,CAACR,IAAI,CAACmB,MAAM,EAAE;UAC5C,IAAIC,KAAK,GAAGC,QAAQ,CAACN,OAAO,CAACO,IAAI,CAAC,iBAAiB,CAAC,CAAC;UACrDd,KAAK,CAACR,IAAI,CAACuB,KAAK,CAACH,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;QAC/C;MACJ,CAAC,EAAE,EAAE,CAAC;IACV,CAAC,CAAC;IACF,IAAI,CAACpB,IAAI,CAACwB,IAAI,CAACX,EAAE,CAAC/C,QAAQ,CAACU,WAAW,GAAG,QAAQ,EAAE,UAAUiD,KAAK,EAAE;MAChE,IAAIL,KAAK,GAAGK,KAAK,CAACC,MAAM,CAACN,KAAK;MAC9BZ,KAAK,CAACzD,YAAY,CAACqE,KAAK,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAACpB,IAAI,CAACwB,IAAI,CAACX,EAAE,CAAC/C,QAAQ,CAACO,UAAU,GAAG,QAAQ,EAAE,YAAY;MAC1DmC,KAAK,CAACZ,eAAe,GAAGY,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW;IAC9D,CAAC,CAAC;IACF,IAAI,CAAC7B,IAAI,CAACwB,IAAI,CAACX,EAAE,CAAC/C,QAAQ,CAACK,YAAY,GAAG,QAAQ,EAAE,YAAY;MAC5DqC,KAAK,CAACsB,iBAAiB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAAC9B,IAAI,CAACwB,IAAI,CAACX,EAAE,CAAC/C,QAAQ,CAACI,eAAe,GAAG,QAAQ,EAAE,YAAY;MAC/D,IAAI,CAACsC,KAAK,CAACR,IAAI,CAAC+B,QAAQ,EACpB;MACJb,UAAU,CAAC,YAAY;QACnBV,KAAK,CAACZ,eAAe,GAAGY,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW;QAC1DrB,KAAK,CAACzD,YAAY,CAACyD,KAAK,CAACR,IAAI,CAACoB,KAAK,CAAC;QACpCZ,KAAK,CAACZ,eAAe,GAAGY,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW;MAC9D,CAAC,EAAE,EAAE,CAAC;IACV,CAAC,CAAC;EACN,CAAC;EACDpC,SAAS,CAAChD,SAAS,CAACgE,cAAc,GAAG,YAAY;IAC7C,IAAIuB,oBAAoB,GAAG,iBAAiB;IAC5C,IAAI,IAAI,CAAC/B,QAAQ,CAAChD,eAAe,EAAE;MAC/B+E,oBAAoB,IAAI,iBAAiB,GAAG,IAAI,CAAC/B,QAAQ,CAAChD,eAAe;IAC7E;IACA,IAAIgF,IAAI,GAAG,eAAe,GAAGD,oBAAoB,GAAG,gFAAgF;IACpI,IAAI,CAAChC,IAAI,CAAC2B,KAAK,CAACO,QAAQ,CAAC,cAAc,CAAC;IACxC,IAAI,IAAI,CAACjC,QAAQ,CAAC5C,kBAAkB,KAAK,gBAAgB,EAAE;MACvD,IAAI,CAAC2C,IAAI,CAACmC,aAAa,CAACC,MAAM,CAACH,IAAI,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAACjC,IAAI,CAAC2B,KAAK,CAACS,MAAM,CAACH,IAAI,CAAC;IAChC;IACA,IAAI,CAACI,WAAW,GAAG,IAAI,CAACrC,IAAI,CAAC2B,KAAK,CAACW,IAAI,CAAC,iBAAiB,CAAC,CAAC1B,KAAK,CAAC,CAAC;IAClE,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACX,IAAI,CAAC2B,KAAK,CAACW,IAAI,CAAC,WAAW,CAAC,CAAC1B,KAAK,CAAC,CAAC;IACzD,IAAI,IAAI,CAACX,QAAQ,CAAClD,YAAY,EAAE;MAC5B,IAAI,CAACiD,IAAI,CAAC2B,KAAK,CACVW,IAAI,CAAC,WAAW,CAAC,CACjBC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACvC,IAAI,CAACC,QAAQ,CAACuC,KAAK,GAAG,IAAI,CAAC,CAC3DD,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC1C,eAAe,GAAG,IAAI,CAAC,CACzC0C,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;IACpC;IACA,IAAI,CAACE,gBAAgB,CAAC,IAAI,CAACzC,IAAI,CAACE,YAAY,CAAC;EACjD,CAAC;EACDT,SAAS,CAAChD,SAAS,CAACc,eAAe,GAAG,YAAY;IAC9C,IAAIiD,KAAK,GAAG,IAAI;IAChB,IAAIkC,cAAc,GAAG;MACjBC,KAAK,EAAE;QACHC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE;MACV,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,OAAO,EAAE,IAAID,IAAI,CAAC,CAAC;MACnBE,aAAa,EAAE;IACnB,CAAC;IACD,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAI,CAACf,WAAW,CAACH,QAAQ,CAAC,SAAS,CAAC;IACpC,IAAI,CAAClC,IAAI,CAAC2B,KAAK,CACVW,IAAI,CAAC,WAAW,CAAC,CACjB1B,KAAK,CAAC,CAAC,CACPC,EAAE,CAAC,oBAAoB,EAAE,UAAUC,CAAC,EAAE;MACvC,IAAIN,KAAK,CAACX,eAAe,GAAGW,KAAK,CAACZ,eAAe,EAAE;QAC/C;QACAkB,CAAC,CAACuC,cAAc,CAAC,CAAC;QAClBX,cAAc,CAACC,KAAK,CAACC,MAAM,GAAG9B,CAAC,CAACwC,KAAK;QACrCZ,cAAc,CAACM,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;QACrCzC,KAAK,CAACT,cAAc,GAAG,KAAK;QAC5BqD,UAAU,GAAG,IAAI;QACjB;QACA5C,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC2B,UAAU,IAAI,CAAC;QACtC/C,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC2B,UAAU,IAAI,CAAC;QACtC;QACA/C,KAAK,CAAC6B,WAAW,CACZmB,WAAW,CAAC,SAAS,CAAC,CACtBtB,QAAQ,CAAC,aAAa,CAAC;MAChC;IACJ,CAAC,CAAC;IACF,IAAI,CAACvC,GAAG,CAAC8D,MAAM,CAAC,CAAC5C,EAAE,CAAC,2BAA2B,GAAG,IAAI,CAACb,IAAI,CAAC0D,IAAI,EAAE,UAAU5C,CAAC,EAAE;MAC3E,IAAI,CAACN,KAAK,CAACR,IAAI,CAAC+B,QAAQ,EACpB;MACJ,IAAIqB,UAAU,EAAE;QACZV,cAAc,CAACC,KAAK,CAACE,IAAI,GAAG/B,CAAC,CAACwC,KAAK;QACnCZ,cAAc,GAAGlC,KAAK,CAACmD,gBAAgB,CAACjB,cAAc,CAAC;MAC3D;IACJ,CAAC,CAAC;IACF,IAAI,CAAC/C,GAAG,CAAC8D,MAAM,CAAC,CAAC5C,EAAE,CAAC,yBAAyB,GAAG,IAAI,CAACb,IAAI,CAAC0D,IAAI,EAAE,YAAY;MACxE,IAAI,CAAClD,KAAK,CAACR,IAAI,CAAC+B,QAAQ,EACpB;MACJ,IAAIW,cAAc,CAACI,OAAO,EAAE;QACxBJ,cAAc,GAAGlC,KAAK,CAACoD,eAAe,CAAClB,cAAc,CAAC;MAC1D,CAAC,MACI;QACDlC,KAAK,CAACT,cAAc,GAAG,IAAI;MAC/B;MACA,IAAIqD,UAAU,EAAE;QACZA,UAAU,GAAG,KAAK;QAClB5C,KAAK,CAAC6B,WAAW,CAACmB,WAAW,CAAC,aAAa,CAAC,CAACtB,QAAQ,CAAC,SAAS,CAAC;MACpE;IACJ,CAAC,CAAC;EACN,CAAC;EACDzC,SAAS,CAAChD,SAAS,CAACe,gBAAgB,GAAG,YAAY;IAC/C,IAAIgD,KAAK,GAAG,IAAI;IAChB,IAAIkC,cAAc,GAAG;MACjBC,KAAK,EAAE;QACHC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE;MACV,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,OAAO,EAAE,IAAID,IAAI,CAAC,CAAC;MACnBE,aAAa,EAAE;IACnB,CAAC;IACD,IAAI,CAACxC,QAAQ,CAACE,EAAE,CAAC,eAAe,EAAE,UAAUC,CAAC,EAAE;MAC3C,IAAIN,KAAK,CAACX,eAAe,GAAGW,KAAK,CAACZ,eAAe,EAAE;QAC/CkB,CAAC,CAACuC,cAAc,CAAC,CAAC;QAClBX,cAAc,CAACC,KAAK,CAACC,MAAM,GAAG9B,CAAC,CAAC+C,aAAa,CAAC,CAAC,CAAC,CAACP,KAAK;QACtD9C,KAAK,CAACT,cAAc,GAAG,KAAK;QAC5B2C,cAAc,CAACM,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;MACzC;IACJ,CAAC,CAAC;IACF,IAAI,CAACtC,QAAQ,CAACE,EAAE,CAAC,cAAc,EAAE,UAAUC,CAAC,EAAE;MAC1C,IAAIN,KAAK,CAACX,eAAe,GAAGW,KAAK,CAACZ,eAAe,EAAE;QAC/CkB,CAAC,CAACuC,cAAc,CAAC,CAAC;QAClBX,cAAc,CAACC,KAAK,CAACE,IAAI,GAAG/B,CAAC,CAAC+C,aAAa,CAAC,CAAC,CAAC,CAACP,KAAK;QACpDZ,cAAc,GAAGlC,KAAK,CAACmD,gBAAgB,CAACjB,cAAc,CAAC;MAC3D;IACJ,CAAC,CAAC;IACF,IAAI,CAAC/B,QAAQ,CAACE,EAAE,CAAC,aAAa,EAAE,YAAY;MACxC,IAAI6B,cAAc,CAACI,OAAO,EAAE;QACxBJ,cAAc,GAAGlC,KAAK,CAACoD,eAAe,CAAClB,cAAc,CAAC;MAC1D,CAAC,MACI;QACDlC,KAAK,CAACT,cAAc,GAAG,IAAI;MAC/B;IACJ,CAAC,CAAC;EACN,CAAC;EACD;EACAN,SAAS,CAAChD,SAAS,CAACqF,iBAAiB,GAAG,YAAY;IAChD,IAAItB,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,CAAC6B,WAAW,CAACH,QAAQ,CAAC,0BAA0B,CAAC;IACrDhB,UAAU,CAAC,YAAY;MACnBV,KAAK,CAACX,eAAe,GACjBW,KAAK,CAACR,IAAI,CAACE,YAAY,CAAC3D,MAAM,IACzBiE,KAAK,CAACP,QAAQ,CAAC/C,UAAU,GAAGsD,KAAK,CAACP,QAAQ,CAAC7C,WAAW,CAAC;MAChEoD,KAAK,CAACG,QAAQ,CAAC4B,GAAG,CAAC,OAAO,EAAE/B,KAAK,CAACX,eAAe,GAAG,IAAI,CAAC;MACzDW,KAAK,CAACG,QAAQ,CAACmD,KAAK,CAAC,CAAC;MACtBtD,KAAK,CAACiC,gBAAgB,CAACjC,KAAK,CAACR,IAAI,CAACE,YAAY,CAAC;MAC/CM,KAAK,CAACzD,YAAY,CAACyD,KAAK,CAACR,IAAI,CAACoB,KAAK,CAAC;IACxC,CAAC,EAAE,EAAE,CAAC;IACNF,UAAU,CAAC,YAAY;MACnBV,KAAK,CAAC6B,WAAW,CAACmB,WAAW,CAAC,0BAA0B,CAAC;IAC7D,CAAC,EAAE,GAAG,CAAC;EACX,CAAC;EACD;EACA/D,SAAS,CAAChD,SAAS,CAACsH,YAAY,GAAG,UAAUC,KAAK,EAAE;IAChD,IAAI,CAACrD,QAAQ,CAAC4B,GAAG,CAAC,WAAW,EAAE,eAAe,GAAGyB,KAAK,GAAG,eAAe,CAAC;EAC7E,CAAC;EACDvE,SAAS,CAAChD,SAAS,CAACwH,qBAAqB,GAAG,UAAUC,IAAI,EAAE;IACxD,IAAIA,IAAI,GAAG,IAAI,CAACrE,eAAe,GAAG,IAAI,CAACD,eAAe,EAAE;MACpDsE,IAAI,GAAG,IAAI,CAACrE,eAAe,GAAG,IAAI,CAACD,eAAe;IACtD;IACA,IAAIsE,IAAI,GAAG,CAAC,EAAE;MACVA,IAAI,GAAG,CAAC;IACZ;IACA,OAAOA,IAAI;EACf,CAAC;EACDzE,SAAS,CAAChD,SAAS,CAACM,YAAY,GAAG,UAAUqE,KAAK,EAAE;IAChD,IAAI,CAACT,QAAQ,CAAC4B,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACvC,IAAI,CAACC,QAAQ,CAACuC,KAAK,GAAG,IAAI,CAAC;IACzE,IAAI,IAAI,CAACvC,QAAQ,CAAClD,YAAY,EAAE;MAC5B,IAAIoH,QAAQ,GAAG,CAAC;MAChB,QAAQ,IAAI,CAAClE,QAAQ,CAACjD,oBAAoB;QACtC,KAAK,MAAM;UACPmH,QAAQ,GAAG,CAAC;UACZ;QACJ,KAAK,QAAQ;UACTA,QAAQ,GACJ,IAAI,CAACvE,eAAe,GAAG,CAAC,GAAG,IAAI,CAACK,QAAQ,CAAC/C,UAAU,GAAG,CAAC;UAC3D;QACJ,KAAK,OAAO;UACRiH,QAAQ,GAAG,IAAI,CAACvE,eAAe,GAAG,IAAI,CAACK,QAAQ,CAAC/C,UAAU;MAClE;MACA,IAAI,CAAC4C,UAAU,GACX,CAAC,IAAI,CAACG,QAAQ,CAAC/C,UAAU,GAAG,IAAI,CAAC+C,QAAQ,CAAC7C,WAAW,IAAIgE,KAAK,GAC1D,CAAC,GACD+C,QAAQ;MAChB,IAAI,IAAI,CAACrE,UAAU,GAAG,IAAI,CAACD,eAAe,GAAG,IAAI,CAACD,eAAe,EAAE;QAC/D,IAAI,CAACE,UAAU,GAAG,IAAI,CAACD,eAAe,GAAG,IAAI,CAACD,eAAe;MACjE;MACA,IAAI,IAAI,CAACE,UAAU,GAAG,CAAC,EAAE;QACrB,IAAI,CAACA,UAAU,GAAG,CAAC;MACvB;MACA,IAAI,CAACiE,YAAY,CAAC,IAAI,CAACjE,UAAU,CAAC;IACtC;EACJ,CAAC;EACDL,SAAS,CAAChD,SAAS,CAACkH,gBAAgB,GAAG,UAAUjB,cAAc,EAAE;IAC7DA,cAAc,CAACK,aAAa,GAAG,IAAI,CAACjD,UAAU;IAC9C4C,cAAc,CAACI,OAAO,GAAG,IAAI;IAC7BJ,cAAc,CAACS,aAAa,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACmB,OAAO,CAAC,CAAC;IACnD1B,cAAc,CAACK,aAAa,IACxBL,cAAc,CAACC,KAAK,CAACE,IAAI,GAAGH,cAAc,CAACC,KAAK,CAACC,MAAM;IAC3DF,cAAc,CAACK,aAAa,GAAG,IAAI,CAACkB,qBAAqB,CAACvB,cAAc,CAACK,aAAa,CAAC;IACvF;IACA,IAAI,CAACgB,YAAY,CAACrB,cAAc,CAACK,aAAa,CAAC;IAC/C,IAAI,CAACV,WAAW,CAACH,QAAQ,CAAC,aAAa,CAAC;IACxC,OAAOQ,cAAc;EACzB,CAAC;EACDjD,SAAS,CAAChD,SAAS,CAACmH,eAAe,GAAG,UAAUlB,cAAc,EAAE;IAC5DA,cAAc,CAACI,OAAO,GAAG,KAAK;IAC9BJ,cAAc,CAACQ,OAAO,GAAG,IAAID,IAAI,CAAC,CAAC;IACnC,IAAI,CAACZ,WAAW,CAACmB,WAAW,CAAC,aAAa,CAAC;IAC3C,IAAIa,aAAa,GAAG3B,cAAc,CAACQ,OAAO,CAACkB,OAAO,CAAC,CAAC,GAChD1B,cAAc,CAACM,SAAS,CAACoB,OAAO,CAAC,CAAC;IACtC,IAAIE,YAAY,GAAG5B,cAAc,CAACC,KAAK,CAACE,IAAI,GAAGH,cAAc,CAACC,KAAK,CAACC,MAAM;IAC1E,IAAI2B,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACH,YAAY,CAAC,GAAGD,aAAa;IACnD;IACA;IACA,IAAIE,MAAM,GAAG,IAAI,IACb7B,cAAc,CAACQ,OAAO,CAACkB,OAAO,CAAC,CAAC,GAAG1B,cAAc,CAACS,aAAa,GAAG,EAAE,EAAE;MACtEoB,MAAM,IAAI,CAAC;MACX,IAAIA,MAAM,GAAG,CAAC,EAAE;QACZA,MAAM,IAAI,CAAC;MACf;MACAA,MAAM,GACFA,MAAM,GACFA,MAAM,IAAIC,IAAI,CAACC,GAAG,CAACH,YAAY,CAAC,GAAG,IAAI,CAAC1E,eAAe,CAAC;MAChE,IAAI,CAACe,QAAQ,CAAC4B,GAAG,CAAC,qBAAqB,EAAEiC,IAAI,CAACE,GAAG,CAACH,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;MAC9ED,YAAY,GAAGA,YAAY,GAAGC,MAAM;MACpC,IAAI,CAACzE,UAAU,GAAG,IAAI,CAACmE,qBAAqB,CAAC,IAAI,CAACnE,UAAU,GAAGwE,YAAY,CAAC;MAC5E,IAAI,CAACP,YAAY,CAAC,IAAI,CAACjE,UAAU,CAAC;IACtC,CAAC,MACI;MACD,IAAI,CAACA,UAAU,GAAG4C,cAAc,CAACK,aAAa;IAClD;IACA,IAAIyB,IAAI,CAACC,GAAG,CAAC/B,cAAc,CAACC,KAAK,CAACE,IAAI,GAAGH,cAAc,CAACC,KAAK,CAACC,MAAM,CAAC,GACjE,IAAI,CAAC3C,QAAQ,CAACxC,uBAAuB,EAAE;MACvC,IAAI,CAACsC,cAAc,GAAG,IAAI;IAC9B;IACA,OAAO2C,cAAc;EACzB,CAAC;EACDjD,SAAS,CAAChD,SAAS,CAACkI,YAAY,GAAG,UAAUC,KAAK,EAAExD,KAAK,EAAEyD,GAAG,EAAE;IAC5D,IAAIC,cAAc,GAAG,IAAI,CAAC9E,IAAI,CAACE,YAAY,CAACkB,KAAK,CAAC,CAAC2D,gBAAgB,IAAI,CAAC,CAAC;IACzE,IAAIC,QAAQ;IACZ,IAAIF,cAAc,CAACG,OAAO,EAAE;MACxB,IAAI,IAAI,CAAChF,QAAQ,CAACvC,oBAAoB,EAAE;QACpCsH,QAAQ,GACJ,uBAAuB,GACnBF,cAAc,CAACG,OAAO,CAAC,CAAC,CAAC,GACzB,GAAG,GACH,IAAI,CAAChF,QAAQ,CAACtC,gBAAgB,GAC9B,MAAM;MAClB,CAAC,MACI;QACDqH,QAAQ,GAAGJ,KAAK;MACpB;IACJ,CAAC,MACI;MACDI,QAAQ,GAAGJ,KAAK;IACpB;IACA,IAAIM,OAAO,GAAGL,GAAG,GAAG,OAAO,GAAGA,GAAG,GAAG,GAAG,GAAG,EAAE;IAC5C,OAAO,yBAAyB,GAAGzD,KAAK,GAAG,2BAA2B,IAAIA,KAAK,KAAK,IAAI,CAACpB,IAAI,CAACoB,KAAK,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,4BAA4B,GAAG,IAAI,CAACnB,QAAQ,CAAC/C,UAAU,GAAG,cAAc,GAAG,IAAI,CAAC+C,QAAQ,CAAC9C,WAAW,GAAG,+BAA+B,GAAG,IAAI,CAAC8C,QAAQ,CAAC7C,WAAW,GAAG,2BAA2B,GAAG8H,OAAO,GAAG,qBAAqB,GAAG9D,KAAK,GAAG,WAAW,GAAG4D,QAAQ,GAAG,uBAAuB;EACzZ,CAAC;EACDvF,SAAS,CAAChD,SAAS,CAAC0I,gBAAgB,GAAG,UAAUC,KAAK,EAAE;IACpD,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIjJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,KAAK,CAAC7I,MAAM,EAAEH,CAAC,EAAE,EAAE;MACnCiJ,SAAS,IAAI,IAAI,CAACV,YAAY,CAACS,KAAK,CAAChJ,CAAC,CAAC,CAACwI,KAAK,EAAExI,CAAC,EAAEgJ,KAAK,CAAChJ,CAAC,CAAC,CAACyI,GAAG,CAAC;IACnE;IACA,OAAOQ,SAAS;EACpB,CAAC;EACD5F,SAAS,CAAChD,SAAS,CAACgG,gBAAgB,GAAG,UAAU2C,KAAK,EAAE;IACpD,IAAIC,SAAS,GAAG,IAAI,CAACF,gBAAgB,CAACC,KAAK,CAAC;IAC5C,IAAI,CAACzE,QAAQ,CAACsB,IAAI,CAACoD,SAAS,CAAC;EACjC,CAAC;EACD5F,SAAS,CAAChD,SAAS,CAAC0D,qBAAqB,GAAG,YAAY;IACpD,IAAI,IAAI,CAACF,QAAQ,CAAClD,YAAY,EAAE;MAC5B,IAAI,CAACiD,IAAI,CAAC2B,KAAK,CAACO,QAAQ,CAAC,kBAAkB,CAAC;IAChD;EACJ,CAAC;EACD;EACAzC,SAAS,CAAChD,SAAS,CAACiE,8BAA8B,GAAG,YAAY;IAC7D,IAAIF,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,CAACR,IAAI,CAACwB,IAAI,CAACX,EAAE,CAAC/C,QAAQ,CAACU,WAAW,GAAG,QAAQ,EAAE,UAAUiD,KAAK,EAAE;MAChE,IAAI6D,MAAM,GAAG9E,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACW,IAAI,CAAC,gBAAgB,CAAC;MACpD,IAAIlB,KAAK,GAAGK,KAAK,CAACC,MAAM,CAACN,KAAK;MAC9BkE,MAAM,CAAC9B,WAAW,CAAC,QAAQ,CAAC;MAC5B8B,MAAM,CAACC,EAAE,CAACnE,KAAK,CAAC,CAACc,QAAQ,CAAC,QAAQ,CAAC;IACvC,CAAC,CAAC;EACN,CAAC;EACD;EACAzC,SAAS,CAAChD,SAAS,CAAC6D,cAAc,GAAG,YAAY;IAC7C,IAAIE,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAACP,QAAQ,CAAC3C,WAAW,EAAE;MAC3B,IAAI,CAAC0C,IAAI,CAAC2B,KAAK,CAACO,QAAQ,CAAC,eAAe,CAAC;MACzC,IAAI,CAAClC,IAAI,CAACwF,QAAQ,CAACpD,MAAM,CAAC,oCAAoC,GAC1D,IAAI,CAACnC,QAAQ,CAACrC,sBAAsB,CAAC,kBAAkB,CAAC,GACxD,6CAA6C,CAAC;MAClD,IAAI,CAACoC,IAAI,CAAC2B,KAAK,CACVW,IAAI,CAAC,kBAAkB,CAAC,CACxB1B,KAAK,CAAC,CAAC,CACPC,EAAE,CAAC,UAAU,EAAE,YAAY;QAC5BL,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAAC8D,WAAW,CAAC,oBAAoB,CAAC;MACtD,CAAC,CAAC;IACN;EACJ,CAAC;EACDhG,SAAS,CAAChD,SAAS,CAAC8D,aAAa,GAAG,YAAY;IAC5C,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACb,GAAG,CAAC8D,MAAM,CAAC,CAAC5C,EAAE,CAAC,yBAAyB,GAAG,IAAI,CAACb,IAAI,CAAC0D,IAAI,EAAE,UAAU5C,CAAC,EAAE;MACzE,IAAI,CAACN,KAAK,CAACR,IAAI,CAAC+B,QAAQ,IAAI,CAACvB,KAAK,CAACP,QAAQ,CAAC3C,WAAW,EACnD;MACJ,IAAIwD,CAAC,CAAC4E,OAAO,KAAK,EAAE,EAAE;QAClB5E,CAAC,CAACuC,cAAc,CAAC,CAAC;QAClB7C,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAACO,QAAQ,CAAC,oBAAoB,CAAC;MACnD,CAAC,MACI,IAAIpB,CAAC,CAAC4E,OAAO,KAAK,EAAE,EAAE;QACvB5E,CAAC,CAACuC,cAAc,CAAC,CAAC;QAClB7C,KAAK,CAACR,IAAI,CAAC2B,KAAK,CAAC6B,WAAW,CAAC,oBAAoB,CAAC;MACtD;IACJ,CAAC,CAAC;EACN,CAAC;EACD/D,SAAS,CAAChD,SAAS,CAACkJ,OAAO,GAAG,YAAY;IACtC,IAAI,IAAI,CAAC1F,QAAQ,CAACnD,SAAS,EAAE;MACzB,IAAI,CAAC6C,GAAG,CAAC8D,MAAM,CAAC,CAACmC,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC5F,IAAI,CAAC0D,IAAI,CAAC;MACzD,IAAI,CAAC1D,IAAI,CAACwB,IAAI,CAACoE,GAAG,CAAC,WAAW,CAAC;MAC/B,IAAI,CAAC5F,IAAI,CAACwB,IAAI,CAACoE,GAAG,CAAC,QAAQ,CAAC;MAC5B,IAAI,CAACvD,WAAW,CAACwD,MAAM,CAAC,CAAC;MACzB,IAAI,CAAC7F,IAAI,CAAC2B,KAAK,CAAC6B,WAAW,CAAC,cAAc,CAAC;IAC/C;EACJ,CAAC;EACD,OAAO/D,SAAS;AACpB,CAAC,CAAC,CAAE;AAEJ,eAAeA,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
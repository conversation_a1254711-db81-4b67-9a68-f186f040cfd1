{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CoreSidebarService {\n  constructor() {\n    // Private\n    this._registry = {};\n  }\n  /**\r\n   * Get the sidebar with the given key\r\n   *\r\n   * @param key\r\n   * @returns {CoreSidebarComponent}\r\n   */\n  getSidebarRegistry(key) {\n    // Check if the sidebar registered\n    if (!this._registry[key]) {\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\n      return;\n    }\n    // Return the sidebar\n    return this._registry[key];\n  }\n  /**\r\n   * Set the sidebar to the registry\r\n   *\r\n   * @param key\r\n   * @param sidebar\r\n   */\n  setSidebarRegistry(key, sidebar) {\n    // Check if the key already being used\n    if (this._registry[key]) {\n      console.error(`The sidebar with the key '${key}' already exists. Either unregister it first or use a unique key.`);\n      return;\n    }\n    // Set to the registry\n    this._registry[key] = sidebar;\n  }\n  /**\r\n   * Remove the sidebar from the registry\r\n   *\r\n   * @param key\r\n   */\n  removeSidebarRegistry(key) {\n    // Check if the sidebar registered\n    if (!this._registry[key]) {\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\n    }\n    // Unregister the sidebar\n    delete this._registry[key];\n  }\n  setOverlayClickEvent(key, callback) {\n    if (!this._registry[key]) {\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\n      return;\n    }\n    this._registry[key].setOverlayClickEvent(callback);\n  }\n  setOnOpenEvent(key, callback) {\n    if (!this._registry[key]) {\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\n      return;\n    }\n    this._registry[key].setOnOpenEvent(callback);\n  }\n  get sidebarRegistry() {\n    return this._registry;\n  }\n  static #_ = this.ɵfac = function CoreSidebarService_Factory(t) {\n    return new (t || CoreSidebarService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CoreSidebarService,\n    factory: CoreSidebarService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AAMA,OAAM,MAAOA,kBAAkB;EAH/BC,YAAA;IAIE;IACQ,KAAAC,SAAS,GAA4C,EAAE;;EAE/D;;;;;;EAMAC,kBAAkBA,CAACC,GAAG;IACpB;IAEA,IAAI,CAAC,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC,EAAE;MACxBC,OAAO,CAACC,IAAI,CAAC,6BAA6BF,GAAG,kCAAkC,CAAC;MAChF;;IAGF;IACA,OAAO,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC;EAC5B;EAEA;;;;;;EAOAG,kBAAkBA,CAACH,GAAG,EAAEI,OAAO;IAC7B;IACA,IAAI,IAAI,CAACN,SAAS,CAACE,GAAG,CAAC,EAAE;MACvBC,OAAO,CAACI,KAAK,CACX,6BAA6BL,GAAG,mEAAmE,CACpG;MAED;;IAGF;IACA,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC,GAAGI,OAAO;EAC/B;EAEA;;;;;EAKAE,qBAAqBA,CAACN,GAAG;IACvB;IACA,IAAI,CAAC,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC,EAAE;MACxBC,OAAO,CAACC,IAAI,CAAC,6BAA6BF,GAAG,kCAAkC,CAAC;;IAGlF;IACA,OAAO,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC;EAC5B;EAEAO,oBAAoBA,CAACP,GAAG,EAAEQ,QAAQ;IAChC,IAAI,CAAC,IAAI,CAACV,SAAS,CAACE,GAAG,CAAC,EAAE;MACxBC,OAAO,CAACC,IAAI,CAAC,6BAA6BF,GAAG,kCAAkC,CAAC;MAChF;;IAGF,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC,CAACO,oBAAoB,CAACC,QAAQ,CAAC;EACpD;EAEAC,cAAcA,CAACT,GAAG,EAAEQ,QAAQ;IAC1B,IAAI,CAAC,IAAI,CAACV,SAAS,CAACE,GAAG,CAAC,EAAE;MACxBC,OAAO,CAACC,IAAI,CAAC,6BAA6BF,GAAG,kCAAkC,CAAC;MAChF;;IAGF,IAAI,CAACF,SAAS,CAACE,GAAG,CAAC,CAACS,cAAc,CAACD,QAAQ,CAAC;EAC9C;EAEA,IAAIE,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACZ,SAAS;EACvB;EAAC,QAAAa,CAAA;qBA9EUf,kBAAkB;EAAA;EAAA,QAAAgB,EAAA;WAAlBhB,kBAAkB;IAAAiB,OAAA,EAAlBjB,kBAAkB,CAAAkB,IAAA;IAAAC,UAAA,EAFjB;EAAM", "names": ["CoreSidebarService", "constructor", "_registry", "getSidebarRegistry", "key", "console", "warn", "setSidebarRegistry", "sidebar", "error", "removeSidebarRegistry", "setOverlayClickEvent", "callback", "setOnOpenEvent", "sidebarRegistry", "_", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-sidebar\\core-sidebar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CoreSidebarComponent } from '@core/components/core-sidebar/core-sidebar.component';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CoreSidebarService {\r\n  // Private\r\n  private _registry: { [key: string]: CoreSidebarComponent } = {};\r\n\r\n  /**\r\n   * Get the sidebar with the given key\r\n   *\r\n   * @param key\r\n   * @returns {CoreSidebarComponent}\r\n   */\r\n  getSidebarRegistry(key): CoreSidebarComponent {\r\n    // Check if the sidebar registered\r\n\r\n    if (!this._registry[key]) {\r\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\r\n      return;\r\n    }\r\n\r\n    // Return the sidebar\r\n    return this._registry[key];\r\n  }\r\n\r\n  /**\r\n   * Set the sidebar to the registry\r\n   *\r\n   * @param key\r\n   * @param sidebar\r\n   */\r\n\r\n  setSidebarRegistry(key, sidebar): void {\r\n    // Check if the key already being used\r\n    if (this._registry[key]) {\r\n      console.error(\r\n        `The sidebar with the key '${key}' already exists. Either unregister it first or use a unique key.`\r\n      );\r\n\r\n      return;\r\n    }\r\n\r\n    // Set to the registry\r\n    this._registry[key] = sidebar;\r\n  }\r\n\r\n  /**\r\n   * Remove the sidebar from the registry\r\n   *\r\n   * @param key\r\n   */\r\n  removeSidebarRegistry(key): void {\r\n    // Check if the sidebar registered\r\n    if (!this._registry[key]) {\r\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\r\n    }\r\n\r\n    // Unregister the sidebar\r\n    delete this._registry[key];\r\n  }\r\n\r\n  setOverlayClickEvent(key, callback): void {\r\n    if (!this._registry[key]) {\r\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\r\n      return;\r\n    }\r\n\r\n    this._registry[key].setOverlayClickEvent(callback);\r\n  }\r\n\r\n  setOnOpenEvent(key, callback): void {\r\n    if (!this._registry[key]) {\r\n      console.warn(`The sidebar with the key '${key}' doesn't exist in the registry.`);\r\n      return;\r\n    }\r\n\r\n    this._registry[key].setOnOpenEvent(callback);\r\n  }\r\n\r\n  get sidebarRegistry(): { [key: string]: CoreSidebarComponent } {\r\n    return this._registry;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
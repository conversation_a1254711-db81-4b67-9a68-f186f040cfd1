{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ManageSponsorsComponent } from './manage-sponsors.component';\nimport { RouterModule } from '@angular/router';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { Ckeditor5TypeComponent } from 'app/components/ckeditor5-type/ckeditor5-type.component';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { ImageCropperTypeComponent } from 'app/components/image-cropper-type/image-cropper-type.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreSidebarModule } from '@core/components';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { DataTablesModule } from 'angular-datatables';\nimport { ManageSponsorsEditorComponent } from './manage-sponsors-editor/manage-sponsors-editor.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\nconst routes = [{\n  path: '',\n  component: ManageSponsorsComponent\n}];\nexport class ManageSponsorsModule {\n  static #_ = this.ɵfac = function ManageSponsorsModule_Factory(t) {\n    return new (t || ManageSponsorsModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ManageSponsorsModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), ContentHeaderModule, CKEditorModule, FormsModule, ReactiveFormsModule, CoreSidebarModule, DragDropModule, TranslateModule, BtnDropdownActionModule, DataTablesModule, FormlyModule.forRoot({\n      validationMessages: [{\n        name: 'required',\n        message: 'This field is required'\n      }, {\n        name: 'minLength',\n        message: 'Minimum length should be {0}'\n      }, {\n        name: 'maxLength',\n        message: 'Maximum length should be {0}'\n      }, {\n        name: 'pattern',\n        message: 'This field is invalid'\n      }, {\n        name: 'server',\n        message: serverValidationMessage\n      }],\n      types: [{\n        name: 'ckeditor5',\n        component: Ckeditor5TypeComponent\n      }, {\n        name: 'image-cropper',\n        component: ImageCropperTypeComponent,\n        wrappers: ['form-field']\n      }]\n    }), FormlyBootstrapModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ManageSponsorsModule, {\n    declarations: [ManageSponsorsComponent, ManageSponsorsEditorComponent],\n    imports: [CommonModule, i1.RouterModule, ContentHeaderModule, CKEditorModule, FormsModule, ReactiveFormsModule, CoreSidebarModule, DragDropModule, TranslateModule, BtnDropdownActionModule, DataTablesModule, i2.FormlyModule, FormlyBootstrapModule]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAAoBC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,uBAAuB,QAAQ,qDAAqD;AAC7F,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,6BAA6B,QAAQ,2DAA2D;;;;AAEzG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEnB;CACZ,CACF;AAuCD,OAAM,MAAOoB,oBAAoB;EAAA,QAAAC,CAAA;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA;UAApBF;EAAoB;EAAA,QAAAG,EAAA;cA/B7BxB,YAAY,EACZE,YAAY,CAACuB,QAAQ,CAACP,MAAM,CAAC,EAC7Bf,mBAAmB,EACnBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBO,iBAAiB,EACjBE,cAAc,EACdH,eAAe,EACfE,uBAAuB,EACvBE,gBAAgB,EAChBT,YAAY,CAACmB,OAAO,CAAC;MACnBC,kBAAkB,EAAE,CAClB;QAAEC,IAAI,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAwB,CAAE,EACvD;QAAED,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAA8B,CAAE,EAC9D;QAAED,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAA8B,CAAE,EAC9D;QAAED,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAuB,CAAE,EACrD;QAAED,IAAI,EAAE,QAAQ;QAAEC,OAAO,EAAErB;MAAuB,CAAE,CACrD;MACDsB,KAAK,EAAE,CACL;QAAEF,IAAI,EAAE,WAAW;QAAER,SAAS,EAAEX;MAAsB,CAAE,EACxD;QACEmB,IAAI,EAAE,eAAe;QACrBR,SAAS,EAAET,yBAAyB;QACpCoB,QAAQ,EAAE,CAAC,YAAY;OACxB;KAEJ,CAAC,EACFrB,qBAAqB;EAAA;;;2EAGZW,oBAAoB;IAAAW,YAAA,GAnC7B/B,uBAAuB,EACvBgB,6BAA6B;IAAAgB,OAAA,GAG7BjC,YAAY,EAAAkC,EAAA,CAAAhC,YAAA,EAEZC,mBAAmB,EACnBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBO,iBAAiB,EACjBE,cAAc,EACdH,eAAe,EACfE,uBAAuB,EACvBE,gBAAgB,EAAAmB,EAAA,CAAA5B,YAAA,EAkBhBG,qBAAqB;EAAA;AAAA", "names": ["CommonModule", "ManageSponsorsComponent", "RouterModule", "ContentHeaderModule", "CKEditorModule", "FormsModule", "ReactiveFormsModule", "FormlyModule", "serverValidationMessage", "Ckeditor5TypeComponent", "FormlyBootstrapModule", "ImageCropperTypeComponent", "TranslateModule", "CoreSidebarModule", "BtnDropdownActionModule", "DragDropModule", "DataTablesModule", "ManageSponsorsEditorComponent", "routes", "path", "component", "ManageSponsorsModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "validationMessages", "name", "message", "types", "wrappers", "declarations", "imports", "i1", "i2"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\manage-sponsors\\manage-sponsors.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ManageSponsorsComponent } from './manage-sponsors.component';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\r\nimport { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { FormlyModule } from '@ngx-formly/core';\r\nimport { serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { Ckeditor5TypeComponent } from 'app/components/ckeditor5-type/ckeditor5-type.component';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { ImageCropperTypeComponent } from 'app/components/image-cropper-type/image-cropper-type.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { ManageSponsorsEditorComponent } from './manage-sponsors-editor/manage-sponsors-editor.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: ManageSponsorsComponent\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ManageSponsorsComponent,\r\n    ManageSponsorsEditorComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    CKEditorModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CoreSidebarModule,\r\n    DragDropModule,\r\n    TranslateModule,\r\n    BtnDropdownActionModule,\r\n    DataTablesModule,\r\n    FormlyModule.forRoot({\r\n      validationMessages: [\r\n        { name: 'required', message: 'This field is required' },\r\n        { name: 'minLength', message: 'Minimum length should be {0}' },\r\n        { name: 'maxLength', message: 'Maximum length should be {0}' },\r\n        { name: 'pattern', message: 'This field is invalid' },\r\n        { name: 'server', message: serverValidationMessage },\r\n      ],\r\n      types: [\r\n        { name: 'ckeditor5', component: Ckeditor5TypeComponent },\r\n        {\r\n          name: 'image-cropper',\r\n          component: ImageCropperTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n      ],\r\n    }),\r\n    FormlyBootstrapModule\r\n  ]\r\n})\r\nexport class ManageSponsorsModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
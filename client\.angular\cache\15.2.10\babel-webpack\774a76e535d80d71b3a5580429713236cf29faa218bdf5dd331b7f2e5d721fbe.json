{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nexport class NavigationService {\n  constructor(_coreSidebarService, router, location) {\n    this._coreSidebarService = _coreSidebarService;\n    this.router = router;\n    this.location = location;\n    this.history = [];\n    this.previousUrl = null;\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        this.history.push(event.urlAfterRedirects);\n      }\n    });\n  }\n  back() {\n    console.log('back button clicked');\n    // check sidebars and close them if they are open\n    let registerSidebars = this._coreSidebarService.sidebarRegistry;\n    // for loop object\n    for (let key in registerSidebars) {\n      if (registerSidebars[key].isOpened && key != 'menu') {\n        this._coreSidebarService.getSidebarRegistry(key).close();\n        return;\n      }\n    }\n    // check history and navigate back\n    this.previousUrl = this.history[this.history.length - 1] || null;\n    this.history.pop();\n    if (this.history.length > 0) {\n      this.location.back();\n      return;\n    } else {\n      this.router.navigateByUrl('/');\n      return;\n    }\n  }\n  getPreviousUrl() {\n    const currentPreviousUrl = this.previousUrl;\n    this.previousUrl = null;\n    return currentPreviousUrl;\n  }\n  static #_ = this.ɵfac = function NavigationService_Factory(t) {\n    return new (t || NavigationService)(i0.ɵɵinject(i1.CoreSidebarService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.Location));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NavigationService,\n    factory: NavigationService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,aAAa,QAAgB,iBAAiB;;;;;AAIvD,OAAM,MAAOC,iBAAiB;EAG5BC,YACSC,mBAAuC,EACtCC,MAAc,EACfC,QAAkB;IAFlB,KAAAF,mBAAmB,GAAnBA,mBAAmB;IAClB,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,QAAQ,GAARA,QAAQ;IALT,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,WAAW,GAAkB,IAAI;IAMvC,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;MACrC,IAAIA,KAAK,YAAYV,aAAa,EAAE;QAClC,IAAI,CAACM,OAAO,CAACK,IAAI,CAACD,KAAK,CAACE,iBAAiB,CAAC;;IAE9C,CAAC,CAAC;EACJ;EAEAC,IAAIA,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAElC;IACA,IAAIC,gBAAgB,GAAG,IAAI,CAACb,mBAAmB,CAACc,eAAe;IAC/D;IACA,KAAK,IAAIC,GAAG,IAAIF,gBAAgB,EAAE;MAChC,IAAIA,gBAAgB,CAACE,GAAG,CAAC,CAACC,QAAQ,IAAID,GAAG,IAAI,MAAM,EAAE;QACnD,IAAI,CAACf,mBAAmB,CAACiB,kBAAkB,CAACF,GAAG,CAAC,CAACG,KAAK,EAAE;QACxD;;;IAGJ;IACA,IAAI,CAACd,WAAW,GAAG,IAAI,CAACD,OAAO,CAAC,IAAI,CAACA,OAAO,CAACgB,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;IAChE,IAAI,CAAChB,OAAO,CAACiB,GAAG,EAAE;IAClB,IAAI,IAAI,CAACjB,OAAO,CAACgB,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACjB,QAAQ,CAACQ,IAAI,EAAE;MACpB;KACD,MAAM;MACL,IAAI,CAACT,MAAM,CAACoB,aAAa,CAAC,GAAG,CAAC;MAC9B;;EAEJ;EAEAC,cAAcA,CAAA;IACZ,MAAMC,kBAAkB,GAAG,IAAI,CAACnB,WAAW;IAC3C,IAAI,CAACA,WAAW,GAAG,IAAI;IACvB,OAAOmB,kBAAkB;EAC3B;EAAC,QAAAC,CAAA;qBA3CU1B,iBAAiB,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA;WAAjBnC,iBAAiB;IAAAoC,OAAA,EAAjBpC,iBAAiB,CAAAqC,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["NavigationEnd", "NavigationService", "constructor", "_coreSidebarService", "router", "location", "history", "previousUrl", "events", "subscribe", "event", "push", "urlAfterRedirects", "back", "console", "log", "registerSidebars", "sidebarRegistry", "key", "isOpened", "getSidebarRegistry", "close", "length", "pop", "navigateByUrl", "getPreviousUrl", "currentPreviousUrl", "_", "i0", "ɵɵinject", "i1", "CoreSidebarService", "i2", "Router", "i3", "Location", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\navigation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { Location } from '@angular/common';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class NavigationService {\r\n  private history: string[] = [];\r\n  private previousUrl: string | null = null;\r\n  constructor(\r\n    public _coreSidebarService: CoreSidebarService,\r\n    private router: Router,\r\n    public location: Location\r\n  ) {\r\n    this.router.events.subscribe((event) => {\r\n      if (event instanceof NavigationEnd) {\r\n        this.history.push(event.urlAfterRedirects);\r\n      }\r\n    });\r\n  }\r\n\r\n  back() {\r\n    console.log('back button clicked');\r\n\r\n    // check sidebars and close them if they are open\r\n    let registerSidebars = this._coreSidebarService.sidebarRegistry;\r\n    // for loop object\r\n    for (let key in registerSidebars) {\r\n      if (registerSidebars[key].isOpened && key != 'menu') {\r\n        this._coreSidebarService.getSidebarRegistry(key).close();\r\n        return;\r\n      }\r\n    }\r\n    // check history and navigate back\r\n    this.previousUrl = this.history[this.history.length - 1] || null;\r\n    this.history.pop();\r\n    if (this.history.length > 0) {\r\n      this.location.back();\r\n      return;\r\n    } else {\r\n      this.router.navigateByUrl('/');\r\n      return;\r\n    }\r\n  }\r\n\r\n  getPreviousUrl(): string | null {\r\n    const currentPreviousUrl = this.previousUrl;\r\n    this.previousUrl = null;\r\n    return currentPreviousUrl;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
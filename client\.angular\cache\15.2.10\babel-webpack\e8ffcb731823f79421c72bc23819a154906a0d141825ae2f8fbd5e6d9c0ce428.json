{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  hmr: false,\n  app_name: 'EZ<PERSON>',\n  // apiUrl: 'http://localhost:8000/api',\n  apiUrl: 'http://127.0.0.1:8000/api',\n  ezstreamUrl: 'http://localhost:4201',\n  proxyUrl: 'https://live.ezleague.app',\n  livekit: {\n    host: 'ezstream-ftob5wzp.livekit.cloud',\n    wsURL: 'wss://ezstream-ftob5wzp.livekit.cloud',\n    azureStorage: {\n      containerName: 'video',\n      accountName: 'ezleague'\n    }\n  },\n  red5: {\n    protocol: 'ws',\n    port: 5080,\n    host: 'localhost',\n    app: 'live'\n  },\n  storageUrl: 'http://localhost:8000/storage/',\n  firebase: {\n    apiKey: 'AIzaSyBAmAIHI1yqoT028B6cAMGhuEkxNdepC28',\n    authDomain: 'ezactive-ezleague.firebaseapp.com',\n    projectId: 'ezactive-ezleague',\n    storageBucket: 'ezactive-ezleague.firebasestorage.app',\n    messagingSenderId: '************',\n    appId: '1:************:web:8f1c0eccb39b4ba265f731',\n    measurementId: 'G-BD36ZBQX6V',\n    vapidKey: 'BGa8u09LbZk1S9frZCxi_Wb7rXDe44pQ0KlCUYUdKXePB3XagiTDsAs7QZk0bzeyjINOPCwr3RR1l_zdHCghJSo'\n  },\n  stripe: {\n    publishableKey: 'pk_test_51NQ3UmDXDLBGPpQrT99fYhZ3lmv2GrN667trK2Kuyp8Of9z4wqr7n73UWqmIAwlp3pvV0B6wg6Clv3JYmle4bsqV00a7Nb4Lba'\n  }\n};\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,KAAK;EACf;EACAC,MAAM,EAAE,2BAA2B;EACnCC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,2BAA2B;EACrCC,OAAO,EAAE;IACPC,IAAI,EAAE,iCAAiC;IACvCC,KAAK,EAAE,uCAAuC;IAC9CC,YAAY,EAAE;MACZC,aAAa,EAAE,OAAO;MACtBC,WAAW,EAAE;;GAEhB;EACDC,IAAI,EAAE;IACJC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVP,IAAI,EAAE,WAAW;IACjBQ,GAAG,EAAE;GACN;EACDC,UAAU,EAAE,gCAAgC;EAC5CC,QAAQ,EAAE;IACRC,MAAM,EAAE,yCAAyC;IACjDC,UAAU,EAAE,mCAAmC;IAC/CC,SAAS,EAAE,mBAAmB;IAC9BC,aAAa,EAAE,uCAAuC;IACtDC,iBAAiB,EAAE,cAAc;IACjCC,KAAK,EAAE,2CAA2C;IAClDC,aAAa,EAAE,cAAc;IAC7BC,QAAQ,EACN;GACH;EACDC,MAAM,EAAE;IACNC,cAAc,EACZ;;CAEL;AAED;;;;;;;AAOA", "names": ["environment", "production", "hmr", "app_name", "apiUrl", "ezstreamUrl", "proxyUrl", "livekit", "host", "wsURL", "azureStorage", "containerName", "accountName", "red5", "protocol", "port", "app", "storageUrl", "firebase", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "vapid<PERSON>ey", "stripe", "publishableKey"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n  hmr: false,\r\n  app_name: 'EZ<PERSON>',\r\n  // apiUrl: 'http://localhost:8000/api',\r\n  apiUrl: 'http://127.0.0.1:8000/api',\r\n  ezstreamUrl: 'http://localhost:4201',\r\n  proxyUrl: 'https://live.ezleague.app',\r\n  livekit: {\r\n    host: 'ezstream-ftob5wzp.livekit.cloud',\r\n    wsURL: 'wss://ezstream-ftob5wzp.livekit.cloud',\r\n    azureStorage: {\r\n      containerName: 'video',\r\n      accountName: 'ezleague',\r\n    },\r\n  },\r\n  red5: {\r\n    protocol: 'ws',\r\n    port: 5080,\r\n    host: 'localhost',\r\n    app: 'live',\r\n  },\r\n  storageUrl: 'http://localhost:8000/storage/',\r\n  firebase: {\r\n    apiKey: 'AIzaSyBAmAIHI1yqoT028B6cAMGhuEkxNdepC28',\r\n    authDomain: 'ezactive-ezleague.firebaseapp.com',\r\n    projectId: 'ezactive-ezleague',\r\n    storageBucket: 'ezactive-ezleague.firebasestorage.app',\r\n    messagingSenderId: '************',\r\n    appId: '1:************:web:8f1c0eccb39b4ba265f731',\r\n    measurementId: 'G-BD36ZBQX6V',\r\n    vapidKey:\r\n      'BGa8u09LbZk1S9frZCxi_Wb7rXDe44pQ0KlCUYUdKXePB3XagiTDsAs7QZk0bzeyjINOPCwr3RR1l_zdHCghJSo',\r\n  },\r\n  stripe: {\r\n    publishableKey:\r\n      'pk_test_51NQ3UmDXDLBGPpQrT99fYhZ3lmv2GrN667trK2Kuyp8Of9z4wqr7n73UWqmIAwlp3pvV0B6wg6Clv3JYmle4bsqV00a7Nb4Lba',\r\n  },\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
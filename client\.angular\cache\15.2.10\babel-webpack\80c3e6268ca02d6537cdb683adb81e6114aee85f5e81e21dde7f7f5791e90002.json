{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, HostBinding, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@videogular/ngx-videogular/core';\nimport { VgStates, VgCoreModule } from '@videogular/ngx-videogular/core';\nclass VgOverlayPlayComponent {\n  constructor(ref, API, fsAPI, controlsHidden) {\n    this.API = API;\n    this.fsAPI = fsAPI;\n    this.controlsHidden = controlsHidden;\n    this.vgSkipIfControlsHidden = false;\n    this.vgSkipIfControlsHiddenDelay = 0.5;\n    this.isNativeFullscreen = false;\n    this.areControlsHidden = false;\n    this.areControlsHiddenChangeTime = 0;\n    this.subscriptions = [];\n    this.isBuffering = false;\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n    this.subscriptions.push(this.fsAPI.onChangeFullscreen.subscribe(this.onChangeFullscreen.bind(this)));\n    this.subscriptions.push(this.controlsHidden.isHidden.subscribe(this.onHideControls.bind(this)));\n    this.subscriptions.push(this.target.subscriptions.bufferDetected.subscribe(isBuffering => this.onUpdateBuffer(isBuffering)));\n  }\n  onUpdateBuffer(isBuffering) {\n    this.isBuffering = isBuffering;\n  }\n  onChangeFullscreen(fsState) {\n    if (this.fsAPI.nativeFullscreen) {\n      this.isNativeFullscreen = fsState;\n    }\n  }\n  onHideControls(hidden) {\n    if (this.vgSkipIfControlsHidden && this.areControlsHidden != hidden) {\n      this.areControlsHiddenChangeTime = Date.now();\n    }\n    this.areControlsHidden = hidden;\n  }\n  onClick() {\n    if (this.vgSkipIfControlsHidden && (this.areControlsHidden || Date.now() - this.areControlsHiddenChangeTime < this.vgSkipIfControlsHiddenDelay * 1000)) {\n      return;\n    }\n    const state = this.getState();\n    switch (state) {\n      case VgStates.VG_PLAYING:\n        this.target.pause();\n        break;\n      case VgStates.VG_PAUSED:\n      case VgStates.VG_ENDED:\n        this.target.play();\n        break;\n    }\n  }\n  getState() {\n    let state = VgStates.VG_PAUSED;\n    if (this.target) {\n      if (this.target.state instanceof Array) {\n        for (let i = 0, l = this.target.state.length; i < l; i++) {\n          if (this.target.state[i] === VgStates.VG_PLAYING) {\n            state = VgStates.VG_PLAYING;\n            break;\n          }\n        }\n      } else {\n        state = this.target.state;\n      }\n    }\n    return state;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgOverlayPlayComponent.ɵfac = function VgOverlayPlayComponent_Factory(t) {\n  return new (t || VgOverlayPlayComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService), i0.ɵɵdirectiveInject(i1.VgFullscreenApiService), i0.ɵɵdirectiveInject(i1.VgControlsHiddenService));\n};\n/** @nocollapse */\nVgOverlayPlayComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgOverlayPlayComponent,\n  selectors: [[\"vg-overlay-play\"]],\n  hostVars: 2,\n  hostBindings: function VgOverlayPlayComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function VgOverlayPlayComponent_click_HostBindingHandler() {\n        return ctx.onClick();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"is-buffering\", ctx.isBuffering);\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\",\n    vgSkipIfControlsHidden: \"vgSkipIfControlsHidden\",\n    vgSkipIfControlsHiddenDelay: \"vgSkipIfControlsHiddenDelay\"\n  },\n  decls: 2,\n  vars: 6,\n  consts: [[1, \"vg-overlay-play\"], [1, \"overlay-play-container\"]],\n  template: function VgOverlayPlayComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelement(1, \"div\", 1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"native-fullscreen\", ctx.isNativeFullscreen)(\"controls-hidden\", ctx.areControlsHidden);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"vg-icon-play_arrow\", ctx.getState() !== \"playing\");\n    }\n  },\n  styles: [\"vg-overlay-play{z-index:200}vg-overlay-play.is-buffering{display:none}vg-overlay-play .vg-overlay-play{transition:all .5s;cursor:pointer;position:absolute;display:block;color:#fff;width:100%;height:100%;font-size:80px;filter:alpha(opacity=60);opacity:.6}vg-overlay-play .vg-overlay-play.native-fullscreen.controls-hidden{cursor:none}vg-overlay-play .vg-overlay-play .overlay-play-container.vg-icon-play_arrow{pointer-events:none;width:100%;height:100%;position:absolute;display:flex;align-items:center;justify-content:center;font-size:80px}vg-overlay-play .vg-overlay-play:hover{filter:alpha(opacity=100);opacity:1}vg-overlay-play .vg-overlay-play:hover .overlay-play-container.vg-icon-play_arrow:before{transform:scale(1.2)}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgOverlayPlayComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-overlay-play',\n      encapsulation: ViewEncapsulation.None,\n      template: `<div\n    class=\"vg-overlay-play\"\n    [class.native-fullscreen]=\"isNativeFullscreen\"\n    [class.controls-hidden]=\"areControlsHidden\"\n  >\n    <div\n      class=\"overlay-play-container\"\n      [class.vg-icon-play_arrow]=\"getState() !== 'playing'\"\n    ></div>\n  </div>`,\n      styles: [\"vg-overlay-play{z-index:200}vg-overlay-play.is-buffering{display:none}vg-overlay-play .vg-overlay-play{transition:all .5s;cursor:pointer;position:absolute;display:block;color:#fff;width:100%;height:100%;font-size:80px;filter:alpha(opacity=60);opacity:.6}vg-overlay-play .vg-overlay-play.native-fullscreen.controls-hidden{cursor:none}vg-overlay-play .vg-overlay-play .overlay-play-container.vg-icon-play_arrow{pointer-events:none;width:100%;height:100%;position:absolute;display:flex;align-items:center;justify-content:center;font-size:80px}vg-overlay-play .vg-overlay-play:hover{filter:alpha(opacity=100);opacity:1}vg-overlay-play .vg-overlay-play:hover .overlay-play-container.vg-icon-play_arrow:before{transform:scale(1.2)}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }, {\n      type: i1.VgFullscreenApiService\n    }, {\n      type: i1.VgControlsHiddenService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    vgSkipIfControlsHidden: [{\n      type: Input\n    }],\n    vgSkipIfControlsHiddenDelay: [{\n      type: Input\n    }],\n    isBuffering: [{\n      type: HostBinding,\n      args: ['class.is-buffering']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\nclass VgOverlayPlayModule {}\n/** @nocollapse */\nVgOverlayPlayModule.ɵfac = function VgOverlayPlayModule_Factory(t) {\n  return new (t || VgOverlayPlayModule)();\n};\n/** @nocollapse */\nVgOverlayPlayModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: VgOverlayPlayModule\n});\n/** @nocollapse */\nVgOverlayPlayModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, VgCoreModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgOverlayPlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, VgCoreModule],\n      declarations: [VgOverlayPlayComponent],\n      exports: [VgOverlayPlayComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgOverlayPlayComponent, VgOverlayPlayModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "Input", "HostBinding", "HostListener", "NgModule", "CommonModule", "i1", "VgStates", "VgCoreModule", "VgOverlayPlayComponent", "constructor", "ref", "API", "fsAPI", "controlsHidden", "vgSkipIfControlsHidden", "vgSkipIfControlsHiddenDelay", "isNativeFullscreen", "areControlsHidden", "areControlsHiddenChangeTime", "subscriptions", "isBuffering", "elem", "nativeElement", "ngOnInit", "isPlayerReady", "onPlayerReady", "push", "player<PERSON><PERSON>yEvent", "subscribe", "target", "getMediaById", "vgFor", "onChangeFullscreen", "bind", "isHidden", "onHideControls", "bufferDetected", "onUpdateBuffer", "fsState", "nativeFullscreen", "hidden", "Date", "now", "onClick", "state", "getState", "VG_PLAYING", "pause", "VG_PAUSED", "VG_ENDED", "play", "Array", "i", "l", "length", "ngOnDestroy", "for<PERSON>ach", "s", "unsubscribe", "ɵfac", "VgOverlayPlayComponent_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "VgApiService", "VgFullscreenApiService", "VgControlsHiddenService", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "VgOverlayPlayComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "VgOverlayPlayComponent_click_HostBindingHandler", "ɵɵclassProp", "inputs", "decls", "vars", "consts", "template", "VgOverlayPlayComponent_Template", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "VgOverlayPlayModule", "VgOverlayPlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@videogular/ngx-videogular/fesm2020/videogular-ngx-videogular-overlay-play.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, HostBinding, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@videogular/ngx-videogular/core';\nimport { VgStates, VgCoreModule } from '@videogular/ngx-videogular/core';\n\nclass VgOverlayPlayComponent {\n    constructor(ref, API, fsAPI, controlsHidden) {\n        this.API = API;\n        this.fsAPI = fsAPI;\n        this.controlsHidden = controlsHidden;\n        this.vgSkipIfControlsHidden = false;\n        this.vgSkipIfControlsHiddenDelay = 0.5;\n        this.isNativeFullscreen = false;\n        this.areControlsHidden = false;\n        this.areControlsHiddenChangeTime = 0;\n        this.subscriptions = [];\n        this.isBuffering = false;\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n        this.subscriptions.push(this.fsAPI.onChangeFullscreen.subscribe(this.onChangeFullscreen.bind(this)));\n        this.subscriptions.push(this.controlsHidden.isHidden.subscribe(this.onHideControls.bind(this)));\n        this.subscriptions.push(this.target.subscriptions.bufferDetected.subscribe((isBuffering) => this.onUpdateBuffer(isBuffering)));\n    }\n    onUpdateBuffer(isBuffering) {\n        this.isBuffering = isBuffering;\n    }\n    onChangeFullscreen(fsState) {\n        if (this.fsAPI.nativeFullscreen) {\n            this.isNativeFullscreen = fsState;\n        }\n    }\n    onHideControls(hidden) {\n        if (this.vgSkipIfControlsHidden && this.areControlsHidden != hidden) {\n            this.areControlsHiddenChangeTime = Date.now();\n        }\n        this.areControlsHidden = hidden;\n    }\n    onClick() {\n        if (this.vgSkipIfControlsHidden && (this.areControlsHidden || (Date.now() - this.areControlsHiddenChangeTime) < this.vgSkipIfControlsHiddenDelay * 1000)) {\n            return;\n        }\n        const state = this.getState();\n        switch (state) {\n            case VgStates.VG_PLAYING:\n                this.target.pause();\n                break;\n            case VgStates.VG_PAUSED:\n            case VgStates.VG_ENDED:\n                this.target.play();\n                break;\n        }\n    }\n    getState() {\n        let state = VgStates.VG_PAUSED;\n        if (this.target) {\n            if (this.target.state instanceof Array) {\n                for (let i = 0, l = this.target.state.length; i < l; i++) {\n                    if (this.target.state[i] === VgStates.VG_PLAYING) {\n                        state = VgStates.VG_PLAYING;\n                        break;\n                    }\n                }\n            }\n            else {\n                state = this.target.state;\n            }\n        }\n        return state;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgOverlayPlayComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgOverlayPlayComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }, { token: i1.VgFullscreenApiService }, { token: i1.VgControlsHiddenService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgOverlayPlayComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgOverlayPlayComponent, selector: \"vg-overlay-play\", inputs: { vgFor: \"vgFor\", vgSkipIfControlsHidden: \"vgSkipIfControlsHidden\", vgSkipIfControlsHiddenDelay: \"vgSkipIfControlsHiddenDelay\" }, host: { listeners: { \"click\": \"onClick()\" }, properties: { \"class.is-buffering\": \"this.isBuffering\" } }, ngImport: i0, template: `<div\n    class=\"vg-overlay-play\"\n    [class.native-fullscreen]=\"isNativeFullscreen\"\n    [class.controls-hidden]=\"areControlsHidden\"\n  >\n    <div\n      class=\"overlay-play-container\"\n      [class.vg-icon-play_arrow]=\"getState() !== 'playing'\"\n    ></div>\n  </div>`, isInline: true, styles: [\"vg-overlay-play{z-index:200}vg-overlay-play.is-buffering{display:none}vg-overlay-play .vg-overlay-play{transition:all .5s;cursor:pointer;position:absolute;display:block;color:#fff;width:100%;height:100%;font-size:80px;filter:alpha(opacity=60);opacity:.6}vg-overlay-play .vg-overlay-play.native-fullscreen.controls-hidden{cursor:none}vg-overlay-play .vg-overlay-play .overlay-play-container.vg-icon-play_arrow{pointer-events:none;width:100%;height:100%;position:absolute;display:flex;align-items:center;justify-content:center;font-size:80px}vg-overlay-play .vg-overlay-play:hover{filter:alpha(opacity=100);opacity:1}vg-overlay-play .vg-overlay-play:hover .overlay-play-container.vg-icon-play_arrow:before{transform:scale(1.2)}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgOverlayPlayComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-overlay-play', encapsulation: ViewEncapsulation.None, template: `<div\n    class=\"vg-overlay-play\"\n    [class.native-fullscreen]=\"isNativeFullscreen\"\n    [class.controls-hidden]=\"areControlsHidden\"\n  >\n    <div\n      class=\"overlay-play-container\"\n      [class.vg-icon-play_arrow]=\"getState() !== 'playing'\"\n    ></div>\n  </div>`, styles: [\"vg-overlay-play{z-index:200}vg-overlay-play.is-buffering{display:none}vg-overlay-play .vg-overlay-play{transition:all .5s;cursor:pointer;position:absolute;display:block;color:#fff;width:100%;height:100%;font-size:80px;filter:alpha(opacity=60);opacity:.6}vg-overlay-play .vg-overlay-play.native-fullscreen.controls-hidden{cursor:none}vg-overlay-play .vg-overlay-play .overlay-play-container.vg-icon-play_arrow{pointer-events:none;width:100%;height:100%;position:absolute;display:flex;align-items:center;justify-content:center;font-size:80px}vg-overlay-play .vg-overlay-play:hover{filter:alpha(opacity=100);opacity:1}vg-overlay-play .vg-overlay-play:hover .overlay-play-container.vg-icon-play_arrow:before{transform:scale(1.2)}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }, { type: i1.VgFullscreenApiService }, { type: i1.VgControlsHiddenService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], vgSkipIfControlsHidden: [{\n                type: Input\n            }], vgSkipIfControlsHiddenDelay: [{\n                type: Input\n            }], isBuffering: [{\n                type: HostBinding,\n                args: ['class.is-buffering']\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }] } });\n\nclass VgOverlayPlayModule {\n}\n/** @nocollapse */ VgOverlayPlayModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgOverlayPlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ VgOverlayPlayModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: VgOverlayPlayModule, declarations: [VgOverlayPlayComponent], imports: [CommonModule, VgCoreModule], exports: [VgOverlayPlayComponent] });\n/** @nocollapse */ VgOverlayPlayModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgOverlayPlayModule, imports: [CommonModule, VgCoreModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgOverlayPlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, VgCoreModule],\n                    declarations: [VgOverlayPlayComponent],\n                    exports: [VgOverlayPlayComponent],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgOverlayPlayComponent, VgOverlayPlayModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACxG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iCAAiC;AAExE,MAAMC,sBAAsB,CAAC;EACzBC,WAAWA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAE;IACzC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,2BAA2B,GAAG,GAAG;IACtC,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,2BAA2B,GAAG,CAAC;IACpC,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,IAAI,GAAGX,GAAG,CAACY,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACZ,GAAG,CAACa,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACN,aAAa,CAACO,IAAI,CAAC,IAAI,CAACf,GAAG,CAACgB,gBAAgB,CAACC,SAAS,CAAC,MAAM,IAAI,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACI,MAAM,GAAG,IAAI,CAAClB,GAAG,CAACmB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/C,IAAI,CAACZ,aAAa,CAACO,IAAI,CAAC,IAAI,CAACd,KAAK,CAACoB,kBAAkB,CAACJ,SAAS,CAAC,IAAI,CAACI,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpG,IAAI,CAACd,aAAa,CAACO,IAAI,CAAC,IAAI,CAACb,cAAc,CAACqB,QAAQ,CAACN,SAAS,CAAC,IAAI,CAACO,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/F,IAAI,CAACd,aAAa,CAACO,IAAI,CAAC,IAAI,CAACG,MAAM,CAACV,aAAa,CAACiB,cAAc,CAACR,SAAS,CAAER,WAAW,IAAK,IAAI,CAACiB,cAAc,CAACjB,WAAW,CAAC,CAAC,CAAC;EAClI;EACAiB,cAAcA,CAACjB,WAAW,EAAE;IACxB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAY,kBAAkBA,CAACM,OAAO,EAAE;IACxB,IAAI,IAAI,CAAC1B,KAAK,CAAC2B,gBAAgB,EAAE;MAC7B,IAAI,CAACvB,kBAAkB,GAAGsB,OAAO;IACrC;EACJ;EACAH,cAAcA,CAACK,MAAM,EAAE;IACnB,IAAI,IAAI,CAAC1B,sBAAsB,IAAI,IAAI,CAACG,iBAAiB,IAAIuB,MAAM,EAAE;MACjE,IAAI,CAACtB,2BAA2B,GAAGuB,IAAI,CAACC,GAAG,CAAC,CAAC;IACjD;IACA,IAAI,CAACzB,iBAAiB,GAAGuB,MAAM;EACnC;EACAG,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC7B,sBAAsB,KAAK,IAAI,CAACG,iBAAiB,IAAKwB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACxB,2BAA2B,GAAI,IAAI,CAACH,2BAA2B,GAAG,IAAI,CAAC,EAAE;MACtJ;IACJ;IACA,MAAM6B,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,QAAQD,KAAK;MACT,KAAKtC,QAAQ,CAACwC,UAAU;QACpB,IAAI,CAACjB,MAAM,CAACkB,KAAK,CAAC,CAAC;QACnB;MACJ,KAAKzC,QAAQ,CAAC0C,SAAS;MACvB,KAAK1C,QAAQ,CAAC2C,QAAQ;QAClB,IAAI,CAACpB,MAAM,CAACqB,IAAI,CAAC,CAAC;QAClB;IACR;EACJ;EACAL,QAAQA,CAAA,EAAG;IACP,IAAID,KAAK,GAAGtC,QAAQ,CAAC0C,SAAS;IAC9B,IAAI,IAAI,CAACnB,MAAM,EAAE;MACb,IAAI,IAAI,CAACA,MAAM,CAACe,KAAK,YAAYO,KAAK,EAAE;QACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACxB,MAAM,CAACe,KAAK,CAACU,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UACtD,IAAI,IAAI,CAACvB,MAAM,CAACe,KAAK,CAACQ,CAAC,CAAC,KAAK9C,QAAQ,CAACwC,UAAU,EAAE;YAC9CF,KAAK,GAAGtC,QAAQ,CAACwC,UAAU;YAC3B;UACJ;QACJ;MACJ,CAAC,MACI;QACDF,KAAK,GAAG,IAAI,CAACf,MAAM,CAACe,KAAK;MAC7B;IACJ;IACA,OAAOA,KAAK;EAChB;EACAW,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpC,aAAa,CAACqC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBlD,sBAAsB,CAACmD,IAAI,YAAAC,+BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFrD,sBAAsB,EAAhCX,EAAE,CAAAiE,iBAAA,CAAgDjE,EAAE,CAACkE,UAAU,GAA/DlE,EAAE,CAAAiE,iBAAA,CAA0EzD,EAAE,CAAC2D,YAAY,GAA3FnE,EAAE,CAAAiE,iBAAA,CAAsGzD,EAAE,CAAC4D,sBAAsB,GAAjIpE,EAAE,CAAAiE,iBAAA,CAA4IzD,EAAE,CAAC6D,uBAAuB;AAAA,CAA4C;AAChV;AAAmB1D,sBAAsB,CAAC2D,IAAI,kBAD8EtE,EAAE,CAAAuE,iBAAA;EAAAC,IAAA,EACJ7D,sBAAsB;EAAA8D,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADpB7E,EAAE,CAAA+E,UAAA,mBAAAC,gDAAA;QAAA,OACJF,GAAA,CAAAhC,OAAA,CAAQ,CAAC;MAAA;IAAA;IAAA,IAAA+B,EAAA;MADP7E,EAAE,CAAAiF,WAAA,iBAAAH,GAAA,CAAAvD,WAAA;IAAA;EAAA;EAAA2D,MAAA;IAAAhD,KAAA;IAAAjB,sBAAA;IAAAC,2BAAA;EAAA;EAAAiE,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,gCAAAV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF7E,EAAE,CAAAwF,cAAA,YAK5H,CAAC;MALyHxF,EAAE,CAAAyF,SAAA,YASpH,CAAC;MATiHzF,EAAE,CAAA0F,YAAA,CAUvH,CAAC;IAAA;IAAA,IAAAb,EAAA;MAVoH7E,EAAE,CAAAiF,WAAA,sBAAAH,GAAA,CAAA3D,kBAG7E,CAAC,oBAAA2D,GAAA,CAAA1D,iBAAD,CAAC;MAH0EpB,EAAE,CAAA2F,SAAA,EAQpE,CAAC;MARiE3F,EAAE,CAAAiF,WAAA,uBAAAH,GAAA,CAAA9B,QAAA,gBAQpE,CAAC;IAAA;EAAA;EAAA4C,MAAA;EAAAC,aAAA;AAAA,EAEgvB;AAC3yB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAX4H9F,EAAE,CAAA+F,iBAAA,CAWnCpF,sBAAsB,EAAc,CAAC;IACpH6D,IAAI,EAAEvE,SAAS;IACf+F,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEJ,aAAa,EAAE3F,iBAAiB,CAACgG,IAAI;MAAEZ,QAAQ,EAAG;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;MAAEM,MAAM,EAAE,CAAC,ytBAAytB;IAAE,CAAC;EACxuB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAExE,EAAE,CAACkE;IAAW,CAAC,EAAE;MAAEM,IAAI,EAAEhE,EAAE,CAAC2D;IAAa,CAAC,EAAE;MAAEK,IAAI,EAAEhE,EAAE,CAAC4D;IAAuB,CAAC,EAAE;MAAEI,IAAI,EAAEhE,EAAE,CAAC6D;IAAwB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEnC,KAAK,EAAE,CAAC;MAC/LsC,IAAI,EAAErE;IACV,CAAC,CAAC;IAAEc,sBAAsB,EAAE,CAAC;MACzBuD,IAAI,EAAErE;IACV,CAAC,CAAC;IAAEe,2BAA2B,EAAE,CAAC;MAC9BsD,IAAI,EAAErE;IACV,CAAC,CAAC;IAAEoB,WAAW,EAAE,CAAC;MACdiD,IAAI,EAAEpE,WAAW;MACjB4F,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAElD,OAAO,EAAE,CAAC;MACV0B,IAAI,EAAEnE,YAAY;MAClB2F,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMG,mBAAmB,CAAC;AAE1B;AAAmBA,mBAAmB,CAACrC,IAAI,YAAAsC,4BAAApC,CAAA;EAAA,YAAAA,CAAA,IAAwFmC,mBAAmB;AAAA,CAAkD;AACxM;AAAmBA,mBAAmB,CAACE,IAAI,kBAxCiFrG,EAAE,CAAAsG,gBAAA;EAAA9B,IAAA,EAwCM2B;AAAmB,EAAqH;AAC5Q;AAAmBA,mBAAmB,CAACI,IAAI,kBAzCiFvG,EAAE,CAAAwG,gBAAA;EAAAC,OAAA,GAyCqClG,YAAY,EAAEG,YAAY;AAAA,EAAI;AACjM;EAAA,QAAAoF,SAAA,oBAAAA,SAAA,KA1C4H9F,EAAE,CAAA+F,iBAAA,CA0CnCI,mBAAmB,EAAc,CAAC;IACjH3B,IAAI,EAAElE,QAAQ;IACd0F,IAAI,EAAE,CAAC;MACCS,OAAO,EAAE,CAAClG,YAAY,EAAEG,YAAY,CAAC;MACrCgG,YAAY,EAAE,CAAC/F,sBAAsB,CAAC;MACtCgG,OAAO,EAAE,CAAChG,sBAAsB;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,sBAAsB,EAAEwF,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
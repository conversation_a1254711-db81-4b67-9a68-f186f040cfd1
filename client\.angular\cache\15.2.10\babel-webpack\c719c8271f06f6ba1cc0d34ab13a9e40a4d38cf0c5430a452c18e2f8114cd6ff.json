{"ast": null, "code": "import { m as mergeProps, g as guid, i as isArraysEqual, T as Theme, a as mapHash, B as BaseComponent, V as ViewContextType, C as ContentContainer, b as buildViewClassNames, c as greatestDurationDenominator, d as createDuration, e as BASE_OPTION_DEFAULTS, f as arrayToHash, h as filterHash, j as buildEventSourceRefiners, p as parseEventSource, k as formatWithOrdinals, u as unpromisify, l as buildRangeApiWithTimeZone, n as identity, r as requestJson, s as subtractDurations, o as intersectRanges, q as startOfDay, t as addDays, v as hashValuesToArray, w as buildEventApis, D as DelayedRunner, x as createFormatter, y as diffWholeDays, z as memoize, A as memoizeObjArg, E as isPropsEqual, F as Emitter, G as getInitialDate, H as rangeContainsMarker, I as createEmptyEventStore, J as reduceCurrentDate, K as reduceEventStore, L as rezoneEventStoreDates, M as mergeRawOptions, N as BASE_OPTION_REFINERS, O as CALENDAR_LISTENER_REFINERS, P as CALENDAR_OPTION_REFINERS, Q as COMPLEX_OPTION_COMPARATORS, R as VIEW_OPTION_REFINERS, S as DateEnv, U as DateProfileGenerator, W as createEventUi, X as parseBusinessHours, Y as setRef, Z as Interaction, _ as getElSeg, $ as elementClosest, a0 as EventImpl, a1 as listenBySelector, a2 as listenToHoverBySelector, a3 as PureComponent, a4 as buildViewContext, a5 as getUniqueDomId, a6 as parseInteractionSettings, a7 as interactionSettingsStore, a8 as getNow, a9 as CalendarImpl, aa as flushSync, ab as CalendarRoot, ac as RenderId, ad as ensureElHasStyles, ae as applyStyleProp, af as sliceEventStore } from './internal-common.js';\nexport { ag as JsonRequestError } from './internal-common.js';\nimport { createElement, createRef, Fragment, render } from 'preact';\nimport 'preact/compat';\nconst globalLocales = [];\nconst MINIMAL_RAW_EN_LOCALE = {\n  code: 'en',\n  week: {\n    dow: 0,\n    doy: 4 // 4 days need to be within the year to be considered the first week\n  },\n\n  direction: 'ltr',\n  buttonText: {\n    prev: 'prev',\n    next: 'next',\n    prevYear: 'prev year',\n    nextYear: 'next year',\n    year: 'year',\n    today: 'today',\n    month: 'month',\n    week: 'week',\n    day: 'day',\n    list: 'list'\n  },\n  weekText: 'W',\n  weekTextLong: 'Week',\n  closeHint: 'Close',\n  timeHint: 'Time',\n  eventHint: 'Event',\n  allDayText: 'all-day',\n  moreLinkText: 'more',\n  noEventsText: 'No events to display'\n};\nconst RAW_EN_LOCALE = Object.assign(Object.assign({}, MINIMAL_RAW_EN_LOCALE), {\n  // Includes things we don't want other locales to inherit,\n  // things that derive from other translatable strings.\n  buttonHints: {\n    prev: 'Previous $0',\n    next: 'Next $0',\n    today(buttonText, unit) {\n      return unit === 'day' ? 'Today' : `This ${buttonText}`;\n    }\n  },\n  viewHint: '$0 view',\n  navLinkHint: 'Go to $0',\n  moreLinkHint(eventCnt) {\n    return `Show ${eventCnt} more event${eventCnt === 1 ? '' : 's'}`;\n  }\n});\nfunction organizeRawLocales(explicitRawLocales) {\n  let defaultCode = explicitRawLocales.length > 0 ? explicitRawLocales[0].code : 'en';\n  let allRawLocales = globalLocales.concat(explicitRawLocales);\n  let rawLocaleMap = {\n    en: RAW_EN_LOCALE\n  };\n  for (let rawLocale of allRawLocales) {\n    rawLocaleMap[rawLocale.code] = rawLocale;\n  }\n  return {\n    map: rawLocaleMap,\n    defaultCode\n  };\n}\nfunction buildLocale(inputSingular, available) {\n  if (typeof inputSingular === 'object' && !Array.isArray(inputSingular)) {\n    return parseLocale(inputSingular.code, [inputSingular.code], inputSingular);\n  }\n  return queryLocale(inputSingular, available);\n}\nfunction queryLocale(codeArg, available) {\n  let codes = [].concat(codeArg || []); // will convert to array\n  let raw = queryRawLocale(codes, available) || RAW_EN_LOCALE;\n  return parseLocale(codeArg, codes, raw);\n}\nfunction queryRawLocale(codes, available) {\n  for (let i = 0; i < codes.length; i += 1) {\n    let parts = codes[i].toLocaleLowerCase().split('-');\n    for (let j = parts.length; j > 0; j -= 1) {\n      let simpleId = parts.slice(0, j).join('-');\n      if (available[simpleId]) {\n        return available[simpleId];\n      }\n    }\n  }\n  return null;\n}\nfunction parseLocale(codeArg, codes, raw) {\n  let merged = mergeProps([MINIMAL_RAW_EN_LOCALE, raw], ['buttonText']);\n  delete merged.code; // don't want this part of the options\n  let {\n    week\n  } = merged;\n  delete merged.week;\n  return {\n    codeArg,\n    codes,\n    week,\n    simpleNumberFormat: new Intl.NumberFormat(codeArg),\n    options: merged\n  };\n}\n\n// TODO: easier way to add new hooks? need to update a million things\nfunction createPlugin(input) {\n  return {\n    id: guid(),\n    name: input.name,\n    premiumReleaseDate: input.premiumReleaseDate ? new Date(input.premiumReleaseDate) : undefined,\n    deps: input.deps || [],\n    reducers: input.reducers || [],\n    isLoadingFuncs: input.isLoadingFuncs || [],\n    contextInit: [].concat(input.contextInit || []),\n    eventRefiners: input.eventRefiners || {},\n    eventDefMemberAdders: input.eventDefMemberAdders || [],\n    eventSourceRefiners: input.eventSourceRefiners || {},\n    isDraggableTransformers: input.isDraggableTransformers || [],\n    eventDragMutationMassagers: input.eventDragMutationMassagers || [],\n    eventDefMutationAppliers: input.eventDefMutationAppliers || [],\n    dateSelectionTransformers: input.dateSelectionTransformers || [],\n    datePointTransforms: input.datePointTransforms || [],\n    dateSpanTransforms: input.dateSpanTransforms || [],\n    views: input.views || {},\n    viewPropsTransformers: input.viewPropsTransformers || [],\n    isPropsValid: input.isPropsValid || null,\n    externalDefTransforms: input.externalDefTransforms || [],\n    viewContainerAppends: input.viewContainerAppends || [],\n    eventDropTransformers: input.eventDropTransformers || [],\n    componentInteractions: input.componentInteractions || [],\n    calendarInteractions: input.calendarInteractions || [],\n    themeClasses: input.themeClasses || {},\n    eventSourceDefs: input.eventSourceDefs || [],\n    cmdFormatter: input.cmdFormatter,\n    recurringTypes: input.recurringTypes || [],\n    namedTimeZonedImpl: input.namedTimeZonedImpl,\n    initialView: input.initialView || '',\n    elementDraggingImpl: input.elementDraggingImpl,\n    optionChangeHandlers: input.optionChangeHandlers || {},\n    scrollGridImpl: input.scrollGridImpl || null,\n    listenerRefiners: input.listenerRefiners || {},\n    optionRefiners: input.optionRefiners || {},\n    propSetHandlers: input.propSetHandlers || {}\n  };\n}\nfunction buildPluginHooks(pluginDefs, globalDefs) {\n  let currentPluginIds = {};\n  let hooks = {\n    premiumReleaseDate: undefined,\n    reducers: [],\n    isLoadingFuncs: [],\n    contextInit: [],\n    eventRefiners: {},\n    eventDefMemberAdders: [],\n    eventSourceRefiners: {},\n    isDraggableTransformers: [],\n    eventDragMutationMassagers: [],\n    eventDefMutationAppliers: [],\n    dateSelectionTransformers: [],\n    datePointTransforms: [],\n    dateSpanTransforms: [],\n    views: {},\n    viewPropsTransformers: [],\n    isPropsValid: null,\n    externalDefTransforms: [],\n    viewContainerAppends: [],\n    eventDropTransformers: [],\n    componentInteractions: [],\n    calendarInteractions: [],\n    themeClasses: {},\n    eventSourceDefs: [],\n    cmdFormatter: null,\n    recurringTypes: [],\n    namedTimeZonedImpl: null,\n    initialView: '',\n    elementDraggingImpl: null,\n    optionChangeHandlers: {},\n    scrollGridImpl: null,\n    listenerRefiners: {},\n    optionRefiners: {},\n    propSetHandlers: {}\n  };\n  function addDefs(defs) {\n    for (let def of defs) {\n      const pluginName = def.name;\n      const currentId = currentPluginIds[pluginName];\n      if (currentId === undefined) {\n        currentPluginIds[pluginName] = def.id;\n        addDefs(def.deps);\n        hooks = combineHooks(hooks, def);\n      } else if (currentId !== def.id) {\n        // different ID than the one already added\n        console.warn(`Duplicate plugin '${pluginName}'`);\n      }\n    }\n  }\n  if (pluginDefs) {\n    addDefs(pluginDefs);\n  }\n  addDefs(globalDefs);\n  return hooks;\n}\nfunction buildBuildPluginHooks() {\n  let currentOverrideDefs = [];\n  let currentGlobalDefs = [];\n  let currentHooks;\n  return (overrideDefs, globalDefs) => {\n    if (!currentHooks || !isArraysEqual(overrideDefs, currentOverrideDefs) || !isArraysEqual(globalDefs, currentGlobalDefs)) {\n      currentHooks = buildPluginHooks(overrideDefs, globalDefs);\n    }\n    currentOverrideDefs = overrideDefs;\n    currentGlobalDefs = globalDefs;\n    return currentHooks;\n  };\n}\nfunction combineHooks(hooks0, hooks1) {\n  return {\n    premiumReleaseDate: compareOptionalDates(hooks0.premiumReleaseDate, hooks1.premiumReleaseDate),\n    reducers: hooks0.reducers.concat(hooks1.reducers),\n    isLoadingFuncs: hooks0.isLoadingFuncs.concat(hooks1.isLoadingFuncs),\n    contextInit: hooks0.contextInit.concat(hooks1.contextInit),\n    eventRefiners: Object.assign(Object.assign({}, hooks0.eventRefiners), hooks1.eventRefiners),\n    eventDefMemberAdders: hooks0.eventDefMemberAdders.concat(hooks1.eventDefMemberAdders),\n    eventSourceRefiners: Object.assign(Object.assign({}, hooks0.eventSourceRefiners), hooks1.eventSourceRefiners),\n    isDraggableTransformers: hooks0.isDraggableTransformers.concat(hooks1.isDraggableTransformers),\n    eventDragMutationMassagers: hooks0.eventDragMutationMassagers.concat(hooks1.eventDragMutationMassagers),\n    eventDefMutationAppliers: hooks0.eventDefMutationAppliers.concat(hooks1.eventDefMutationAppliers),\n    dateSelectionTransformers: hooks0.dateSelectionTransformers.concat(hooks1.dateSelectionTransformers),\n    datePointTransforms: hooks0.datePointTransforms.concat(hooks1.datePointTransforms),\n    dateSpanTransforms: hooks0.dateSpanTransforms.concat(hooks1.dateSpanTransforms),\n    views: Object.assign(Object.assign({}, hooks0.views), hooks1.views),\n    viewPropsTransformers: hooks0.viewPropsTransformers.concat(hooks1.viewPropsTransformers),\n    isPropsValid: hooks1.isPropsValid || hooks0.isPropsValid,\n    externalDefTransforms: hooks0.externalDefTransforms.concat(hooks1.externalDefTransforms),\n    viewContainerAppends: hooks0.viewContainerAppends.concat(hooks1.viewContainerAppends),\n    eventDropTransformers: hooks0.eventDropTransformers.concat(hooks1.eventDropTransformers),\n    calendarInteractions: hooks0.calendarInteractions.concat(hooks1.calendarInteractions),\n    componentInteractions: hooks0.componentInteractions.concat(hooks1.componentInteractions),\n    themeClasses: Object.assign(Object.assign({}, hooks0.themeClasses), hooks1.themeClasses),\n    eventSourceDefs: hooks0.eventSourceDefs.concat(hooks1.eventSourceDefs),\n    cmdFormatter: hooks1.cmdFormatter || hooks0.cmdFormatter,\n    recurringTypes: hooks0.recurringTypes.concat(hooks1.recurringTypes),\n    namedTimeZonedImpl: hooks1.namedTimeZonedImpl || hooks0.namedTimeZonedImpl,\n    initialView: hooks0.initialView || hooks1.initialView,\n    elementDraggingImpl: hooks0.elementDraggingImpl || hooks1.elementDraggingImpl,\n    optionChangeHandlers: Object.assign(Object.assign({}, hooks0.optionChangeHandlers), hooks1.optionChangeHandlers),\n    scrollGridImpl: hooks1.scrollGridImpl || hooks0.scrollGridImpl,\n    listenerRefiners: Object.assign(Object.assign({}, hooks0.listenerRefiners), hooks1.listenerRefiners),\n    optionRefiners: Object.assign(Object.assign({}, hooks0.optionRefiners), hooks1.optionRefiners),\n    propSetHandlers: Object.assign(Object.assign({}, hooks0.propSetHandlers), hooks1.propSetHandlers)\n  };\n}\nfunction compareOptionalDates(date0, date1) {\n  if (date0 === undefined) {\n    return date1;\n  }\n  if (date1 === undefined) {\n    return date0;\n  }\n  return new Date(Math.max(date0.valueOf(), date1.valueOf()));\n}\nclass StandardTheme extends Theme {}\nStandardTheme.prototype.classes = {\n  root: 'fc-theme-standard',\n  tableCellShaded: 'fc-cell-shaded',\n  buttonGroup: 'fc-button-group',\n  button: 'fc-button fc-button-primary',\n  buttonActive: 'fc-button-active'\n};\nStandardTheme.prototype.baseIconClass = 'fc-icon';\nStandardTheme.prototype.iconClasses = {\n  close: 'fc-icon-x',\n  prev: 'fc-icon-chevron-left',\n  next: 'fc-icon-chevron-right',\n  prevYear: 'fc-icon-chevrons-left',\n  nextYear: 'fc-icon-chevrons-right'\n};\nStandardTheme.prototype.rtlIconClasses = {\n  prev: 'fc-icon-chevron-right',\n  next: 'fc-icon-chevron-left',\n  prevYear: 'fc-icon-chevrons-right',\n  nextYear: 'fc-icon-chevrons-left'\n};\nStandardTheme.prototype.iconOverrideOption = 'buttonIcons'; // TODO: make TS-friendly\nStandardTheme.prototype.iconOverrideCustomButtonOption = 'icon';\nStandardTheme.prototype.iconOverridePrefix = 'fc-icon-';\nfunction compileViewDefs(defaultConfigs, overrideConfigs) {\n  let hash = {};\n  let viewType;\n  for (viewType in defaultConfigs) {\n    ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n  }\n  for (viewType in overrideConfigs) {\n    ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n  }\n  return hash;\n}\nfunction ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs) {\n  if (hash[viewType]) {\n    return hash[viewType];\n  }\n  let viewDef = buildViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n  if (viewDef) {\n    hash[viewType] = viewDef;\n  }\n  return viewDef;\n}\nfunction buildViewDef(viewType, hash, defaultConfigs, overrideConfigs) {\n  let defaultConfig = defaultConfigs[viewType];\n  let overrideConfig = overrideConfigs[viewType];\n  let queryProp = name => defaultConfig && defaultConfig[name] !== null ? defaultConfig[name] : overrideConfig && overrideConfig[name] !== null ? overrideConfig[name] : null;\n  let theComponent = queryProp('component');\n  let superType = queryProp('superType');\n  let superDef = null;\n  if (superType) {\n    if (superType === viewType) {\n      throw new Error('Can\\'t have a custom view type that references itself');\n    }\n    superDef = ensureViewDef(superType, hash, defaultConfigs, overrideConfigs);\n  }\n  if (!theComponent && superDef) {\n    theComponent = superDef.component;\n  }\n  if (!theComponent) {\n    return null; // don't throw a warning, might be settings for a single-unit view\n  }\n\n  return {\n    type: viewType,\n    component: theComponent,\n    defaults: Object.assign(Object.assign({}, superDef ? superDef.defaults : {}), defaultConfig ? defaultConfig.rawOptions : {}),\n    overrides: Object.assign(Object.assign({}, superDef ? superDef.overrides : {}), overrideConfig ? overrideConfig.rawOptions : {})\n  };\n}\nfunction parseViewConfigs(inputs) {\n  return mapHash(inputs, parseViewConfig);\n}\nfunction parseViewConfig(input) {\n  let rawOptions = typeof input === 'function' ? {\n    component: input\n  } : input;\n  let {\n    component\n  } = rawOptions;\n  if (rawOptions.content) {\n    // TODO: remove content/classNames/didMount/etc from options?\n    component = createViewHookComponent(rawOptions);\n  } else if (component && !(component.prototype instanceof BaseComponent)) {\n    // WHY?: people were using `component` property for `content`\n    // TODO: converge on one setting name\n    component = createViewHookComponent(Object.assign(Object.assign({}, rawOptions), {\n      content: component\n    }));\n  }\n  return {\n    superType: rawOptions.type,\n    component: component,\n    rawOptions // includes type and component too :(\n  };\n}\n\nfunction createViewHookComponent(options) {\n  return viewProps => createElement(ViewContextType.Consumer, null, context => createElement(ContentContainer, {\n    elTag: \"div\",\n    elClasses: buildViewClassNames(context.viewSpec),\n    renderProps: Object.assign(Object.assign({}, viewProps), {\n      nextDayThreshold: context.options.nextDayThreshold\n    }),\n    generatorName: undefined,\n    customGenerator: options.content,\n    classNameGenerator: options.classNames,\n    didMount: options.didMount,\n    willUnmount: options.willUnmount\n  }));\n}\nfunction buildViewSpecs(defaultInputs, optionOverrides, dynamicOptionOverrides, localeDefaults) {\n  let defaultConfigs = parseViewConfigs(defaultInputs);\n  let overrideConfigs = parseViewConfigs(optionOverrides.views);\n  let viewDefs = compileViewDefs(defaultConfigs, overrideConfigs);\n  return mapHash(viewDefs, viewDef => buildViewSpec(viewDef, overrideConfigs, optionOverrides, dynamicOptionOverrides, localeDefaults));\n}\nfunction buildViewSpec(viewDef, overrideConfigs, optionOverrides, dynamicOptionOverrides, localeDefaults) {\n  let durationInput = viewDef.overrides.duration || viewDef.defaults.duration || dynamicOptionOverrides.duration || optionOverrides.duration;\n  let duration = null;\n  let durationUnit = '';\n  let singleUnit = '';\n  let singleUnitOverrides = {};\n  if (durationInput) {\n    duration = createDurationCached(durationInput);\n    if (duration) {\n      // valid?\n      let denom = greatestDurationDenominator(duration);\n      durationUnit = denom.unit;\n      if (denom.value === 1) {\n        singleUnit = durationUnit;\n        singleUnitOverrides = overrideConfigs[durationUnit] ? overrideConfigs[durationUnit].rawOptions : {};\n      }\n    }\n  }\n  let queryButtonText = optionsSubset => {\n    let buttonTextMap = optionsSubset.buttonText || {};\n    let buttonTextKey = viewDef.defaults.buttonTextKey;\n    if (buttonTextKey != null && buttonTextMap[buttonTextKey] != null) {\n      return buttonTextMap[buttonTextKey];\n    }\n    if (buttonTextMap[viewDef.type] != null) {\n      return buttonTextMap[viewDef.type];\n    }\n    if (buttonTextMap[singleUnit] != null) {\n      return buttonTextMap[singleUnit];\n    }\n    return null;\n  };\n  let queryButtonTitle = optionsSubset => {\n    let buttonHints = optionsSubset.buttonHints || {};\n    let buttonKey = viewDef.defaults.buttonTextKey; // use same key as text\n    if (buttonKey != null && buttonHints[buttonKey] != null) {\n      return buttonHints[buttonKey];\n    }\n    if (buttonHints[viewDef.type] != null) {\n      return buttonHints[viewDef.type];\n    }\n    if (buttonHints[singleUnit] != null) {\n      return buttonHints[singleUnit];\n    }\n    return null;\n  };\n  return {\n    type: viewDef.type,\n    component: viewDef.component,\n    duration,\n    durationUnit,\n    singleUnit,\n    optionDefaults: viewDef.defaults,\n    optionOverrides: Object.assign(Object.assign({}, singleUnitOverrides), viewDef.overrides),\n    buttonTextOverride: queryButtonText(dynamicOptionOverrides) || queryButtonText(optionOverrides) ||\n    // constructor-specified buttonText lookup hash takes precedence\n    viewDef.overrides.buttonText,\n    buttonTextDefault: queryButtonText(localeDefaults) || viewDef.defaults.buttonText || queryButtonText(BASE_OPTION_DEFAULTS) || viewDef.type,\n    // not DRY\n    buttonTitleOverride: queryButtonTitle(dynamicOptionOverrides) || queryButtonTitle(optionOverrides) || viewDef.overrides.buttonHint,\n    buttonTitleDefault: queryButtonTitle(localeDefaults) || viewDef.defaults.buttonHint || queryButtonTitle(BASE_OPTION_DEFAULTS)\n    // will eventually fall back to buttonText\n  };\n}\n// hack to get memoization working\nlet durationInputMap = {};\nfunction createDurationCached(durationInput) {\n  let json = JSON.stringify(durationInput);\n  let res = durationInputMap[json];\n  if (res === undefined) {\n    res = createDuration(durationInput);\n    durationInputMap[json] = res;\n  }\n  return res;\n}\nfunction reduceViewType(viewType, action) {\n  switch (action.type) {\n    case 'CHANGE_VIEW_TYPE':\n      viewType = action.viewType;\n  }\n  return viewType;\n}\nfunction reduceDynamicOptionOverrides(dynamicOptionOverrides, action) {\n  switch (action.type) {\n    case 'SET_OPTION':\n      return Object.assign(Object.assign({}, dynamicOptionOverrides), {\n        [action.optionName]: action.rawOptionValue\n      });\n    default:\n      return dynamicOptionOverrides;\n  }\n}\nfunction reduceDateProfile(currentDateProfile, action, currentDate, dateProfileGenerator) {\n  let dp;\n  switch (action.type) {\n    case 'CHANGE_VIEW_TYPE':\n      return dateProfileGenerator.build(action.dateMarker || currentDate);\n    case 'CHANGE_DATE':\n      return dateProfileGenerator.build(action.dateMarker);\n    case 'PREV':\n      dp = dateProfileGenerator.buildPrev(currentDateProfile, currentDate);\n      if (dp.isValid) {\n        return dp;\n      }\n      break;\n    case 'NEXT':\n      dp = dateProfileGenerator.buildNext(currentDateProfile, currentDate);\n      if (dp.isValid) {\n        return dp;\n      }\n      break;\n  }\n  return currentDateProfile;\n}\nfunction initEventSources(calendarOptions, dateProfile, context) {\n  let activeRange = dateProfile ? dateProfile.activeRange : null;\n  return addSources({}, parseInitialSources(calendarOptions, context), activeRange, context);\n}\nfunction reduceEventSources(eventSources, action, dateProfile, context) {\n  let activeRange = dateProfile ? dateProfile.activeRange : null; // need this check?\n  switch (action.type) {\n    case 'ADD_EVENT_SOURCES':\n      // already parsed\n      return addSources(eventSources, action.sources, activeRange, context);\n    case 'REMOVE_EVENT_SOURCE':\n      return removeSource(eventSources, action.sourceId);\n    case 'PREV': // TODO: how do we track all actions that affect dateProfile :(\n    case 'NEXT':\n    case 'CHANGE_DATE':\n    case 'CHANGE_VIEW_TYPE':\n      if (dateProfile) {\n        return fetchDirtySources(eventSources, activeRange, context);\n      }\n      return eventSources;\n    case 'FETCH_EVENT_SOURCES':\n      return fetchSourcesByIds(eventSources, action.sourceIds ?\n      // why no type?\n      arrayToHash(action.sourceIds) : excludeStaticSources(eventSources, context), activeRange, action.isRefetch || false, context);\n    case 'RECEIVE_EVENTS':\n    case 'RECEIVE_EVENT_ERROR':\n      return receiveResponse(eventSources, action.sourceId, action.fetchId, action.fetchRange);\n    case 'REMOVE_ALL_EVENT_SOURCES':\n      return {};\n    default:\n      return eventSources;\n  }\n}\nfunction reduceEventSourcesNewTimeZone(eventSources, dateProfile, context) {\n  let activeRange = dateProfile ? dateProfile.activeRange : null; // need this check?\n  return fetchSourcesByIds(eventSources, excludeStaticSources(eventSources, context), activeRange, true, context);\n}\nfunction computeEventSourcesLoading(eventSources) {\n  for (let sourceId in eventSources) {\n    if (eventSources[sourceId].isFetching) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction addSources(eventSourceHash, sources, fetchRange, context) {\n  let hash = {};\n  for (let source of sources) {\n    hash[source.sourceId] = source;\n  }\n  if (fetchRange) {\n    hash = fetchDirtySources(hash, fetchRange, context);\n  }\n  return Object.assign(Object.assign({}, eventSourceHash), hash);\n}\nfunction removeSource(eventSourceHash, sourceId) {\n  return filterHash(eventSourceHash, eventSource => eventSource.sourceId !== sourceId);\n}\nfunction fetchDirtySources(sourceHash, fetchRange, context) {\n  return fetchSourcesByIds(sourceHash, filterHash(sourceHash, eventSource => isSourceDirty(eventSource, fetchRange, context)), fetchRange, false, context);\n}\nfunction isSourceDirty(eventSource, fetchRange, context) {\n  if (!doesSourceNeedRange(eventSource, context)) {\n    return !eventSource.latestFetchId;\n  }\n  return !context.options.lazyFetching || !eventSource.fetchRange || eventSource.isFetching ||\n  // always cancel outdated in-progress fetches\n  fetchRange.start < eventSource.fetchRange.start || fetchRange.end > eventSource.fetchRange.end;\n}\nfunction fetchSourcesByIds(prevSources, sourceIdHash, fetchRange, isRefetch, context) {\n  let nextSources = {};\n  for (let sourceId in prevSources) {\n    let source = prevSources[sourceId];\n    if (sourceIdHash[sourceId]) {\n      nextSources[sourceId] = fetchSource(source, fetchRange, isRefetch, context);\n    } else {\n      nextSources[sourceId] = source;\n    }\n  }\n  return nextSources;\n}\nfunction fetchSource(eventSource, fetchRange, isRefetch, context) {\n  let {\n    options,\n    calendarApi\n  } = context;\n  let sourceDef = context.pluginHooks.eventSourceDefs[eventSource.sourceDefId];\n  let fetchId = guid();\n  sourceDef.fetch({\n    eventSource,\n    range: fetchRange,\n    isRefetch,\n    context\n  }, res => {\n    let {\n      rawEvents\n    } = res;\n    if (options.eventSourceSuccess) {\n      rawEvents = options.eventSourceSuccess.call(calendarApi, rawEvents, res.response) || rawEvents;\n    }\n    if (eventSource.success) {\n      rawEvents = eventSource.success.call(calendarApi, rawEvents, res.response) || rawEvents;\n    }\n    context.dispatch({\n      type: 'RECEIVE_EVENTS',\n      sourceId: eventSource.sourceId,\n      fetchId,\n      fetchRange,\n      rawEvents\n    });\n  }, error => {\n    let errorHandled = false;\n    if (options.eventSourceFailure) {\n      options.eventSourceFailure.call(calendarApi, error);\n      errorHandled = true;\n    }\n    if (eventSource.failure) {\n      eventSource.failure(error);\n      errorHandled = true;\n    }\n    if (!errorHandled) {\n      console.warn(error.message, error);\n    }\n    context.dispatch({\n      type: 'RECEIVE_EVENT_ERROR',\n      sourceId: eventSource.sourceId,\n      fetchId,\n      fetchRange,\n      error\n    });\n  });\n  return Object.assign(Object.assign({}, eventSource), {\n    isFetching: true,\n    latestFetchId: fetchId\n  });\n}\nfunction receiveResponse(sourceHash, sourceId, fetchId, fetchRange) {\n  let eventSource = sourceHash[sourceId];\n  if (eventSource &&\n  // not already removed\n  fetchId === eventSource.latestFetchId) {\n    return Object.assign(Object.assign({}, sourceHash), {\n      [sourceId]: Object.assign(Object.assign({}, eventSource), {\n        isFetching: false,\n        fetchRange\n      })\n    });\n  }\n  return sourceHash;\n}\nfunction excludeStaticSources(eventSources, context) {\n  return filterHash(eventSources, eventSource => doesSourceNeedRange(eventSource, context));\n}\nfunction parseInitialSources(rawOptions, context) {\n  let refiners = buildEventSourceRefiners(context);\n  let rawSources = [].concat(rawOptions.eventSources || []);\n  let sources = []; // parsed\n  if (rawOptions.initialEvents) {\n    rawSources.unshift(rawOptions.initialEvents);\n  }\n  if (rawOptions.events) {\n    rawSources.unshift(rawOptions.events);\n  }\n  for (let rawSource of rawSources) {\n    let source = parseEventSource(rawSource, context, refiners);\n    if (source) {\n      sources.push(source);\n    }\n  }\n  return sources;\n}\nfunction doesSourceNeedRange(eventSource, context) {\n  let defs = context.pluginHooks.eventSourceDefs;\n  return !defs[eventSource.sourceDefId].ignoreRange;\n}\nfunction reduceDateSelection(currentSelection, action) {\n  switch (action.type) {\n    case 'UNSELECT_DATES':\n      return null;\n    case 'SELECT_DATES':\n      return action.selection;\n    default:\n      return currentSelection;\n  }\n}\nfunction reduceSelectedEvent(currentInstanceId, action) {\n  switch (action.type) {\n    case 'UNSELECT_EVENT':\n      return '';\n    case 'SELECT_EVENT':\n      return action.eventInstanceId;\n    default:\n      return currentInstanceId;\n  }\n}\nfunction reduceEventDrag(currentDrag, action) {\n  let newDrag;\n  switch (action.type) {\n    case 'UNSET_EVENT_DRAG':\n      return null;\n    case 'SET_EVENT_DRAG':\n      newDrag = action.state;\n      return {\n        affectedEvents: newDrag.affectedEvents,\n        mutatedEvents: newDrag.mutatedEvents,\n        isEvent: newDrag.isEvent\n      };\n    default:\n      return currentDrag;\n  }\n}\nfunction reduceEventResize(currentResize, action) {\n  let newResize;\n  switch (action.type) {\n    case 'UNSET_EVENT_RESIZE':\n      return null;\n    case 'SET_EVENT_RESIZE':\n      newResize = action.state;\n      return {\n        affectedEvents: newResize.affectedEvents,\n        mutatedEvents: newResize.mutatedEvents,\n        isEvent: newResize.isEvent\n      };\n    default:\n      return currentResize;\n  }\n}\nfunction parseToolbars(calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) {\n  let header = calendarOptions.headerToolbar ? parseToolbar(calendarOptions.headerToolbar, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) : null;\n  let footer = calendarOptions.footerToolbar ? parseToolbar(calendarOptions.footerToolbar, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) : null;\n  return {\n    header,\n    footer\n  };\n}\nfunction parseToolbar(sectionStrHash, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) {\n  let sectionWidgets = {};\n  let viewsWithButtons = [];\n  let hasTitle = false;\n  for (let sectionName in sectionStrHash) {\n    let sectionStr = sectionStrHash[sectionName];\n    let sectionRes = parseSection(sectionStr, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi);\n    sectionWidgets[sectionName] = sectionRes.widgets;\n    viewsWithButtons.push(...sectionRes.viewsWithButtons);\n    hasTitle = hasTitle || sectionRes.hasTitle;\n  }\n  return {\n    sectionWidgets,\n    viewsWithButtons,\n    hasTitle\n  };\n}\n/*\nBAD: querying icons and text here. should be done at render time\n*/\nfunction parseSection(sectionStr, calendarOptions,\n// defaults+overrides, then refined\ncalendarOptionOverrides,\n// overrides only!, unrefined :(\ntheme, viewSpecs, calendarApi) {\n  let isRtl = calendarOptions.direction === 'rtl';\n  let calendarCustomButtons = calendarOptions.customButtons || {};\n  let calendarButtonTextOverrides = calendarOptionOverrides.buttonText || {};\n  let calendarButtonText = calendarOptions.buttonText || {};\n  let calendarButtonHintOverrides = calendarOptionOverrides.buttonHints || {};\n  let calendarButtonHints = calendarOptions.buttonHints || {};\n  let sectionSubstrs = sectionStr ? sectionStr.split(' ') : [];\n  let viewsWithButtons = [];\n  let hasTitle = false;\n  let widgets = sectionSubstrs.map(buttonGroupStr => buttonGroupStr.split(',').map(buttonName => {\n    if (buttonName === 'title') {\n      hasTitle = true;\n      return {\n        buttonName\n      };\n    }\n    let customButtonProps;\n    let viewSpec;\n    let buttonClick;\n    let buttonIcon; // only one of these will be set\n    let buttonText; // \"\n    let buttonHint;\n    // ^ for the title=\"\" attribute, for accessibility\n    if (customButtonProps = calendarCustomButtons[buttonName]) {\n      buttonClick = ev => {\n        if (customButtonProps.click) {\n          customButtonProps.click.call(ev.target, ev, ev.target); // TODO: use Calendar this context?\n        }\n      };\n\n      (buttonIcon = theme.getCustomButtonIconClass(customButtonProps)) || (buttonIcon = theme.getIconClass(buttonName, isRtl)) || (buttonText = customButtonProps.text);\n      buttonHint = customButtonProps.hint || customButtonProps.text;\n    } else if (viewSpec = viewSpecs[buttonName]) {\n      viewsWithButtons.push(buttonName);\n      buttonClick = () => {\n        calendarApi.changeView(buttonName);\n      };\n      (buttonText = viewSpec.buttonTextOverride) || (buttonIcon = theme.getIconClass(buttonName, isRtl)) || (buttonText = viewSpec.buttonTextDefault);\n      let textFallback = viewSpec.buttonTextOverride || viewSpec.buttonTextDefault;\n      buttonHint = formatWithOrdinals(viewSpec.buttonTitleOverride || viewSpec.buttonTitleDefault || calendarOptions.viewHint, [textFallback, buttonName],\n      // view-name = buttonName\n      textFallback);\n    } else if (calendarApi[buttonName]) {\n      // a calendarApi method\n      buttonClick = () => {\n        calendarApi[buttonName]();\n      };\n      (buttonText = calendarButtonTextOverrides[buttonName]) || (buttonIcon = theme.getIconClass(buttonName, isRtl)) || (buttonText = calendarButtonText[buttonName]); // everything else is considered default\n      if (buttonName === 'prevYear' || buttonName === 'nextYear') {\n        let prevOrNext = buttonName === 'prevYear' ? 'prev' : 'next';\n        buttonHint = formatWithOrdinals(calendarButtonHintOverrides[prevOrNext] || calendarButtonHints[prevOrNext], [calendarButtonText.year || 'year', 'year'], calendarButtonText[buttonName]);\n      } else {\n        buttonHint = navUnit => formatWithOrdinals(calendarButtonHintOverrides[buttonName] || calendarButtonHints[buttonName], [calendarButtonText[navUnit] || navUnit, navUnit], calendarButtonText[buttonName]);\n      }\n    }\n    return {\n      buttonName,\n      buttonClick,\n      buttonIcon,\n      buttonText,\n      buttonHint\n    };\n  }));\n  return {\n    widgets,\n    viewsWithButtons,\n    hasTitle\n  };\n}\n\n// always represents the current view. otherwise, it'd need to change value every time date changes\nclass ViewImpl {\n  constructor(type, getCurrentData, dateEnv) {\n    this.type = type;\n    this.getCurrentData = getCurrentData;\n    this.dateEnv = dateEnv;\n  }\n  get calendar() {\n    return this.getCurrentData().calendarApi;\n  }\n  get title() {\n    return this.getCurrentData().viewTitle;\n  }\n  get activeStart() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start);\n  }\n  get activeEnd() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end);\n  }\n  get currentStart() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start);\n  }\n  get currentEnd() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end);\n  }\n  getOption(name) {\n    return this.getCurrentData().options[name]; // are the view-specific options\n  }\n}\n\nlet eventSourceDef$2 = {\n  ignoreRange: true,\n  parseMeta(refined) {\n    if (Array.isArray(refined.events)) {\n      return refined.events;\n    }\n    return null;\n  },\n  fetch(arg, successCallback) {\n    successCallback({\n      rawEvents: arg.eventSource.meta\n    });\n  }\n};\nconst arrayEventSourcePlugin = createPlugin({\n  name: 'array-event-source',\n  eventSourceDefs: [eventSourceDef$2]\n});\nlet eventSourceDef$1 = {\n  parseMeta(refined) {\n    if (typeof refined.events === 'function') {\n      return refined.events;\n    }\n    return null;\n  },\n  fetch(arg, successCallback, errorCallback) {\n    const {\n      dateEnv\n    } = arg.context;\n    const func = arg.eventSource.meta;\n    unpromisify(func.bind(null, buildRangeApiWithTimeZone(arg.range, dateEnv)), rawEvents => successCallback({\n      rawEvents\n    }), errorCallback);\n  }\n};\nconst funcEventSourcePlugin = createPlugin({\n  name: 'func-event-source',\n  eventSourceDefs: [eventSourceDef$1]\n});\nconst JSON_FEED_EVENT_SOURCE_REFINERS = {\n  method: String,\n  extraParams: identity,\n  startParam: String,\n  endParam: String,\n  timeZoneParam: String\n};\nlet eventSourceDef = {\n  parseMeta(refined) {\n    if (refined.url && (refined.format === 'json' || !refined.format)) {\n      return {\n        url: refined.url,\n        format: 'json',\n        method: (refined.method || 'GET').toUpperCase(),\n        extraParams: refined.extraParams,\n        startParam: refined.startParam,\n        endParam: refined.endParam,\n        timeZoneParam: refined.timeZoneParam\n      };\n    }\n    return null;\n  },\n  fetch(arg, successCallback, errorCallback) {\n    const {\n      meta\n    } = arg.eventSource;\n    const requestParams = buildRequestParams(meta, arg.range, arg.context);\n    requestJson(meta.method, meta.url, requestParams).then(([rawEvents, response]) => {\n      successCallback({\n        rawEvents,\n        response\n      });\n    }, errorCallback);\n  }\n};\nconst jsonFeedEventSourcePlugin = createPlugin({\n  name: 'json-event-source',\n  eventSourceRefiners: JSON_FEED_EVENT_SOURCE_REFINERS,\n  eventSourceDefs: [eventSourceDef]\n});\nfunction buildRequestParams(meta, range, context) {\n  let {\n    dateEnv,\n    options\n  } = context;\n  let startParam;\n  let endParam;\n  let timeZoneParam;\n  let customRequestParams;\n  let params = {};\n  startParam = meta.startParam;\n  if (startParam == null) {\n    startParam = options.startParam;\n  }\n  endParam = meta.endParam;\n  if (endParam == null) {\n    endParam = options.endParam;\n  }\n  timeZoneParam = meta.timeZoneParam;\n  if (timeZoneParam == null) {\n    timeZoneParam = options.timeZoneParam;\n  }\n  // retrieve any outbound GET/POST data from the options\n  if (typeof meta.extraParams === 'function') {\n    // supplied as a function that returns a key/value object\n    customRequestParams = meta.extraParams();\n  } else {\n    // probably supplied as a straight key/value object\n    customRequestParams = meta.extraParams || {};\n  }\n  Object.assign(params, customRequestParams);\n  params[startParam] = dateEnv.formatIso(range.start);\n  params[endParam] = dateEnv.formatIso(range.end);\n  if (dateEnv.timeZone !== 'local') {\n    params[timeZoneParam] = dateEnv.timeZone;\n  }\n  return params;\n}\nconst SIMPLE_RECURRING_REFINERS = {\n  daysOfWeek: identity,\n  startTime: createDuration,\n  endTime: createDuration,\n  duration: createDuration,\n  startRecur: identity,\n  endRecur: identity\n};\nlet recurring = {\n  parse(refined, dateEnv) {\n    if (refined.daysOfWeek || refined.startTime || refined.endTime || refined.startRecur || refined.endRecur) {\n      let recurringData = {\n        daysOfWeek: refined.daysOfWeek || null,\n        startTime: refined.startTime || null,\n        endTime: refined.endTime || null,\n        startRecur: refined.startRecur ? dateEnv.createMarker(refined.startRecur) : null,\n        endRecur: refined.endRecur ? dateEnv.createMarker(refined.endRecur) : null\n      };\n      let duration;\n      if (refined.duration) {\n        duration = refined.duration;\n      }\n      if (!duration && refined.startTime && refined.endTime) {\n        duration = subtractDurations(refined.endTime, refined.startTime);\n      }\n      return {\n        allDayGuess: Boolean(!refined.startTime && !refined.endTime),\n        duration,\n        typeData: recurringData // doesn't need endTime anymore but oh well\n      };\n    }\n\n    return null;\n  },\n  expand(typeData, framingRange, dateEnv) {\n    let clippedFramingRange = intersectRanges(framingRange, {\n      start: typeData.startRecur,\n      end: typeData.endRecur\n    });\n    if (clippedFramingRange) {\n      return expandRanges(typeData.daysOfWeek, typeData.startTime, clippedFramingRange, dateEnv);\n    }\n    return [];\n  }\n};\nconst simpleRecurringEventsPlugin = createPlugin({\n  name: 'simple-recurring-event',\n  recurringTypes: [recurring],\n  eventRefiners: SIMPLE_RECURRING_REFINERS\n});\nfunction expandRanges(daysOfWeek, startTime, framingRange, dateEnv) {\n  let dowHash = daysOfWeek ? arrayToHash(daysOfWeek) : null;\n  let dayMarker = startOfDay(framingRange.start);\n  let endMarker = framingRange.end;\n  let instanceStarts = [];\n  while (dayMarker < endMarker) {\n    let instanceStart;\n    // if everyday, or this particular day-of-week\n    if (!dowHash || dowHash[dayMarker.getUTCDay()]) {\n      if (startTime) {\n        instanceStart = dateEnv.add(dayMarker, startTime);\n      } else {\n        instanceStart = dayMarker;\n      }\n      instanceStarts.push(instanceStart);\n    }\n    dayMarker = addDays(dayMarker, 1);\n  }\n  return instanceStarts;\n}\nconst changeHandlerPlugin = createPlugin({\n  name: 'change-handler',\n  optionChangeHandlers: {\n    events(events, context) {\n      handleEventSources([events], context);\n    },\n    eventSources: handleEventSources\n  }\n});\n/*\nBUG: if `event` was supplied, all previously-given `eventSources` will be wiped out\n*/\nfunction handleEventSources(inputs, context) {\n  let unfoundSources = hashValuesToArray(context.getCurrentData().eventSources);\n  if (unfoundSources.length === 1 && inputs.length === 1 && Array.isArray(unfoundSources[0]._raw) && Array.isArray(inputs[0])) {\n    context.dispatch({\n      type: 'RESET_RAW_EVENTS',\n      sourceId: unfoundSources[0].sourceId,\n      rawEvents: inputs[0]\n    });\n    return;\n  }\n  let newInputs = [];\n  for (let input of inputs) {\n    let inputFound = false;\n    for (let i = 0; i < unfoundSources.length; i += 1) {\n      if (unfoundSources[i]._raw === input) {\n        unfoundSources.splice(i, 1); // delete\n        inputFound = true;\n        break;\n      }\n    }\n    if (!inputFound) {\n      newInputs.push(input);\n    }\n  }\n  for (let unfoundSource of unfoundSources) {\n    context.dispatch({\n      type: 'REMOVE_EVENT_SOURCE',\n      sourceId: unfoundSource.sourceId\n    });\n  }\n  for (let newInput of newInputs) {\n    context.calendarApi.addEventSource(newInput);\n  }\n}\nfunction handleDateProfile(dateProfile, context) {\n  context.emitter.trigger('datesSet', Object.assign(Object.assign({}, buildRangeApiWithTimeZone(dateProfile.activeRange, context.dateEnv)), {\n    view: context.viewApi\n  }));\n}\nfunction handleEventStore(eventStore, context) {\n  let {\n    emitter\n  } = context;\n  if (emitter.hasHandlers('eventsSet')) {\n    emitter.trigger('eventsSet', buildEventApis(eventStore, context));\n  }\n}\n\n/*\nthis array is exposed on the root namespace so that UMD plugins can add to it.\nsee the rollup-bundles script.\n*/\nconst globalPlugins = [arrayEventSourcePlugin, funcEventSourcePlugin, jsonFeedEventSourcePlugin, simpleRecurringEventsPlugin, changeHandlerPlugin, createPlugin({\n  name: 'misc',\n  isLoadingFuncs: [state => computeEventSourcesLoading(state.eventSources)],\n  propSetHandlers: {\n    dateProfile: handleDateProfile,\n    eventStore: handleEventStore\n  }\n})];\nclass TaskRunner {\n  constructor(runTaskOption, drainedOption) {\n    this.runTaskOption = runTaskOption;\n    this.drainedOption = drainedOption;\n    this.queue = [];\n    this.delayedRunner = new DelayedRunner(this.drain.bind(this));\n  }\n  request(task, delay) {\n    this.queue.push(task);\n    this.delayedRunner.request(delay);\n  }\n  pause(scope) {\n    this.delayedRunner.pause(scope);\n  }\n  resume(scope, force) {\n    this.delayedRunner.resume(scope, force);\n  }\n  drain() {\n    let {\n      queue\n    } = this;\n    while (queue.length) {\n      let completedTasks = [];\n      let task;\n      while (task = queue.shift()) {\n        this.runTask(task);\n        completedTasks.push(task);\n      }\n      this.drained(completedTasks);\n    } // keep going, in case new tasks were added in the drained handler\n  }\n\n  runTask(task) {\n    if (this.runTaskOption) {\n      this.runTaskOption(task);\n    }\n  }\n  drained(completedTasks) {\n    if (this.drainedOption) {\n      this.drainedOption(completedTasks);\n    }\n  }\n}\n\n// Computes what the title at the top of the calendarApi should be for this view\nfunction buildTitle(dateProfile, viewOptions, dateEnv) {\n  let range;\n  // for views that span a large unit of time, show the proper interval, ignoring stray days before and after\n  if (/^(year|month)$/.test(dateProfile.currentRangeUnit)) {\n    range = dateProfile.currentRange;\n  } else {\n    // for day units or smaller, use the actual day range\n    range = dateProfile.activeRange;\n  }\n  return dateEnv.formatRange(range.start, range.end, createFormatter(viewOptions.titleFormat || buildTitleFormat(dateProfile)), {\n    isEndExclusive: dateProfile.isRangeAllDay,\n    defaultSeparator: viewOptions.titleRangeSeparator\n  });\n}\n// Generates the format string that should be used to generate the title for the current date range.\n// Attempts to compute the most appropriate format if not explicitly specified with `titleFormat`.\nfunction buildTitleFormat(dateProfile) {\n  let {\n    currentRangeUnit\n  } = dateProfile;\n  if (currentRangeUnit === 'year') {\n    return {\n      year: 'numeric'\n    };\n  }\n  if (currentRangeUnit === 'month') {\n    return {\n      year: 'numeric',\n      month: 'long'\n    }; // like \"September 2014\"\n  }\n\n  let days = diffWholeDays(dateProfile.currentRange.start, dateProfile.currentRange.end);\n  if (days !== null && days > 1) {\n    // multi-day range. shorter, like \"Sep 9 - 10 2014\"\n    return {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n  }\n  // one day. longer, like \"September 9 2014\"\n  return {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  };\n}\n\n// in future refactor, do the redux-style function(state=initial) for initial-state\n// also, whatever is happening in constructor, have it happen in action queue too\nclass CalendarDataManager {\n  constructor(props) {\n    this.computeCurrentViewData = memoize(this._computeCurrentViewData);\n    this.organizeRawLocales = memoize(organizeRawLocales);\n    this.buildLocale = memoize(buildLocale);\n    this.buildPluginHooks = buildBuildPluginHooks();\n    this.buildDateEnv = memoize(buildDateEnv$1);\n    this.buildTheme = memoize(buildTheme);\n    this.parseToolbars = memoize(parseToolbars);\n    this.buildViewSpecs = memoize(buildViewSpecs);\n    this.buildDateProfileGenerator = memoizeObjArg(buildDateProfileGenerator);\n    this.buildViewApi = memoize(buildViewApi);\n    this.buildViewUiProps = memoizeObjArg(buildViewUiProps);\n    this.buildEventUiBySource = memoize(buildEventUiBySource, isPropsEqual);\n    this.buildEventUiBases = memoize(buildEventUiBases);\n    this.parseContextBusinessHours = memoizeObjArg(parseContextBusinessHours);\n    this.buildTitle = memoize(buildTitle);\n    this.emitter = new Emitter();\n    this.actionRunner = new TaskRunner(this._handleAction.bind(this), this.updateData.bind(this));\n    this.currentCalendarOptionsInput = {};\n    this.currentCalendarOptionsRefined = {};\n    this.currentViewOptionsInput = {};\n    this.currentViewOptionsRefined = {};\n    this.currentCalendarOptionsRefiners = {};\n    this.optionsForRefining = [];\n    this.optionsForHandling = [];\n    this.getCurrentData = () => this.data;\n    this.dispatch = action => {\n      this.actionRunner.request(action); // protects against recursive calls to _handleAction\n    };\n\n    this.props = props;\n    this.actionRunner.pause();\n    let dynamicOptionOverrides = {};\n    let optionsData = this.computeOptionsData(props.optionOverrides, dynamicOptionOverrides, props.calendarApi);\n    let currentViewType = optionsData.calendarOptions.initialView || optionsData.pluginHooks.initialView;\n    let currentViewData = this.computeCurrentViewData(currentViewType, optionsData, props.optionOverrides, dynamicOptionOverrides);\n    // wire things up\n    // TODO: not DRY\n    props.calendarApi.currentDataManager = this;\n    this.emitter.setThisContext(props.calendarApi);\n    this.emitter.setOptions(currentViewData.options);\n    let currentDate = getInitialDate(optionsData.calendarOptions, optionsData.dateEnv);\n    let dateProfile = currentViewData.dateProfileGenerator.build(currentDate);\n    if (!rangeContainsMarker(dateProfile.activeRange, currentDate)) {\n      currentDate = dateProfile.currentRange.start;\n    }\n    let calendarContext = {\n      dateEnv: optionsData.dateEnv,\n      options: optionsData.calendarOptions,\n      pluginHooks: optionsData.pluginHooks,\n      calendarApi: props.calendarApi,\n      dispatch: this.dispatch,\n      emitter: this.emitter,\n      getCurrentData: this.getCurrentData\n    };\n    // needs to be after setThisContext\n    for (let callback of optionsData.pluginHooks.contextInit) {\n      callback(calendarContext);\n    }\n    // NOT DRY\n    let eventSources = initEventSources(optionsData.calendarOptions, dateProfile, calendarContext);\n    let initialState = {\n      dynamicOptionOverrides,\n      currentViewType,\n      currentDate,\n      dateProfile,\n      businessHours: this.parseContextBusinessHours(calendarContext),\n      eventSources,\n      eventUiBases: {},\n      eventStore: createEmptyEventStore(),\n      renderableEventStore: createEmptyEventStore(),\n      dateSelection: null,\n      eventSelection: '',\n      eventDrag: null,\n      eventResize: null,\n      selectionConfig: this.buildViewUiProps(calendarContext).selectionConfig\n    };\n    let contextAndState = Object.assign(Object.assign({}, calendarContext), initialState);\n    for (let reducer of optionsData.pluginHooks.reducers) {\n      Object.assign(initialState, reducer(null, null, contextAndState));\n    }\n    if (computeIsLoading(initialState, calendarContext)) {\n      this.emitter.trigger('loading', true); // NOT DRY\n    }\n\n    this.state = initialState;\n    this.updateData();\n    this.actionRunner.resume();\n  }\n  resetOptions(optionOverrides, changedOptionNames) {\n    let {\n      props\n    } = this;\n    if (changedOptionNames === undefined) {\n      props.optionOverrides = optionOverrides;\n    } else {\n      props.optionOverrides = Object.assign(Object.assign({}, props.optionOverrides || {}), optionOverrides);\n      this.optionsForRefining.push(...changedOptionNames);\n    }\n    if (changedOptionNames === undefined || changedOptionNames.length) {\n      this.actionRunner.request({\n        type: 'NOTHING'\n      });\n    }\n  }\n  _handleAction(action) {\n    let {\n      props,\n      state,\n      emitter\n    } = this;\n    let dynamicOptionOverrides = reduceDynamicOptionOverrides(state.dynamicOptionOverrides, action);\n    let optionsData = this.computeOptionsData(props.optionOverrides, dynamicOptionOverrides, props.calendarApi);\n    let currentViewType = reduceViewType(state.currentViewType, action);\n    let currentViewData = this.computeCurrentViewData(currentViewType, optionsData, props.optionOverrides, dynamicOptionOverrides);\n    // wire things up\n    // TODO: not DRY\n    props.calendarApi.currentDataManager = this;\n    emitter.setThisContext(props.calendarApi);\n    emitter.setOptions(currentViewData.options);\n    let calendarContext = {\n      dateEnv: optionsData.dateEnv,\n      options: optionsData.calendarOptions,\n      pluginHooks: optionsData.pluginHooks,\n      calendarApi: props.calendarApi,\n      dispatch: this.dispatch,\n      emitter,\n      getCurrentData: this.getCurrentData\n    };\n    let {\n      currentDate,\n      dateProfile\n    } = state;\n    if (this.data && this.data.dateProfileGenerator !== currentViewData.dateProfileGenerator) {\n      // hack\n      dateProfile = currentViewData.dateProfileGenerator.build(currentDate);\n    }\n    currentDate = reduceCurrentDate(currentDate, action);\n    dateProfile = reduceDateProfile(dateProfile, action, currentDate, currentViewData.dateProfileGenerator);\n    if (action.type === 'PREV' ||\n    // TODO: move this logic into DateProfileGenerator\n    action.type === 'NEXT' ||\n    // \"\n    !rangeContainsMarker(dateProfile.currentRange, currentDate)) {\n      currentDate = dateProfile.currentRange.start;\n    }\n    let eventSources = reduceEventSources(state.eventSources, action, dateProfile, calendarContext);\n    let eventStore = reduceEventStore(state.eventStore, action, eventSources, dateProfile, calendarContext);\n    let isEventsLoading = computeEventSourcesLoading(eventSources); // BAD. also called in this func in computeIsLoading\n    let renderableEventStore = isEventsLoading && !currentViewData.options.progressiveEventRendering ? state.renderableEventStore || eventStore :\n    // try from previous state\n    eventStore;\n    let {\n      eventUiSingleBase,\n      selectionConfig\n    } = this.buildViewUiProps(calendarContext); // will memoize obj\n    let eventUiBySource = this.buildEventUiBySource(eventSources);\n    let eventUiBases = this.buildEventUiBases(renderableEventStore.defs, eventUiSingleBase, eventUiBySource);\n    let newState = {\n      dynamicOptionOverrides,\n      currentViewType,\n      currentDate,\n      dateProfile,\n      eventSources,\n      eventStore,\n      renderableEventStore,\n      selectionConfig,\n      eventUiBases,\n      businessHours: this.parseContextBusinessHours(calendarContext),\n      dateSelection: reduceDateSelection(state.dateSelection, action),\n      eventSelection: reduceSelectedEvent(state.eventSelection, action),\n      eventDrag: reduceEventDrag(state.eventDrag, action),\n      eventResize: reduceEventResize(state.eventResize, action)\n    };\n    let contextAndState = Object.assign(Object.assign({}, calendarContext), newState);\n    for (let reducer of optionsData.pluginHooks.reducers) {\n      Object.assign(newState, reducer(state, action, contextAndState)); // give the OLD state, for old value\n    }\n\n    let wasLoading = computeIsLoading(state, calendarContext);\n    let isLoading = computeIsLoading(newState, calendarContext);\n    // TODO: use propSetHandlers in plugin system\n    if (!wasLoading && isLoading) {\n      emitter.trigger('loading', true);\n    } else if (wasLoading && !isLoading) {\n      emitter.trigger('loading', false);\n    }\n    this.state = newState;\n    if (props.onAction) {\n      props.onAction(action);\n    }\n  }\n  updateData() {\n    let {\n      props,\n      state\n    } = this;\n    let oldData = this.data;\n    let optionsData = this.computeOptionsData(props.optionOverrides, state.dynamicOptionOverrides, props.calendarApi);\n    let currentViewData = this.computeCurrentViewData(state.currentViewType, optionsData, props.optionOverrides, state.dynamicOptionOverrides);\n    let data = this.data = Object.assign(Object.assign(Object.assign({\n      viewTitle: this.buildTitle(state.dateProfile, currentViewData.options, optionsData.dateEnv),\n      calendarApi: props.calendarApi,\n      dispatch: this.dispatch,\n      emitter: this.emitter,\n      getCurrentData: this.getCurrentData\n    }, optionsData), currentViewData), state);\n    let changeHandlers = optionsData.pluginHooks.optionChangeHandlers;\n    let oldCalendarOptions = oldData && oldData.calendarOptions;\n    let newCalendarOptions = optionsData.calendarOptions;\n    if (oldCalendarOptions && oldCalendarOptions !== newCalendarOptions) {\n      if (oldCalendarOptions.timeZone !== newCalendarOptions.timeZone) {\n        // hack\n        state.eventSources = data.eventSources = reduceEventSourcesNewTimeZone(data.eventSources, state.dateProfile, data);\n        state.eventStore = data.eventStore = rezoneEventStoreDates(data.eventStore, oldData.dateEnv, data.dateEnv);\n        state.renderableEventStore = data.renderableEventStore = rezoneEventStoreDates(data.renderableEventStore, oldData.dateEnv, data.dateEnv);\n      }\n      for (let optionName in changeHandlers) {\n        if (this.optionsForHandling.indexOf(optionName) !== -1 || oldCalendarOptions[optionName] !== newCalendarOptions[optionName]) {\n          changeHandlers[optionName](newCalendarOptions[optionName], data);\n        }\n      }\n    }\n    this.optionsForHandling = [];\n    if (props.onData) {\n      props.onData(data);\n    }\n  }\n  computeOptionsData(optionOverrides, dynamicOptionOverrides, calendarApi) {\n    // TODO: blacklist options that are handled by optionChangeHandlers\n    if (!this.optionsForRefining.length && optionOverrides === this.stableOptionOverrides && dynamicOptionOverrides === this.stableDynamicOptionOverrides) {\n      return this.stableCalendarOptionsData;\n    }\n    let {\n      refinedOptions,\n      pluginHooks,\n      localeDefaults,\n      availableLocaleData,\n      extra\n    } = this.processRawCalendarOptions(optionOverrides, dynamicOptionOverrides);\n    warnUnknownOptions(extra);\n    let dateEnv = this.buildDateEnv(refinedOptions.timeZone, refinedOptions.locale, refinedOptions.weekNumberCalculation, refinedOptions.firstDay, refinedOptions.weekText, pluginHooks, availableLocaleData, refinedOptions.defaultRangeSeparator);\n    let viewSpecs = this.buildViewSpecs(pluginHooks.views, this.stableOptionOverrides, this.stableDynamicOptionOverrides, localeDefaults);\n    let theme = this.buildTheme(refinedOptions, pluginHooks);\n    let toolbarConfig = this.parseToolbars(refinedOptions, this.stableOptionOverrides, theme, viewSpecs, calendarApi);\n    return this.stableCalendarOptionsData = {\n      calendarOptions: refinedOptions,\n      pluginHooks,\n      dateEnv,\n      viewSpecs,\n      theme,\n      toolbarConfig,\n      localeDefaults,\n      availableRawLocales: availableLocaleData.map\n    };\n  }\n  // always called from behind a memoizer\n  processRawCalendarOptions(optionOverrides, dynamicOptionOverrides) {\n    let {\n      locales,\n      locale\n    } = mergeRawOptions([BASE_OPTION_DEFAULTS, optionOverrides, dynamicOptionOverrides]);\n    let availableLocaleData = this.organizeRawLocales(locales);\n    let availableRawLocales = availableLocaleData.map;\n    let localeDefaults = this.buildLocale(locale || availableLocaleData.defaultCode, availableRawLocales).options;\n    let pluginHooks = this.buildPluginHooks(optionOverrides.plugins || [], globalPlugins);\n    let refiners = this.currentCalendarOptionsRefiners = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, BASE_OPTION_REFINERS), CALENDAR_LISTENER_REFINERS), CALENDAR_OPTION_REFINERS), pluginHooks.listenerRefiners), pluginHooks.optionRefiners);\n    let extra = {};\n    let raw = mergeRawOptions([BASE_OPTION_DEFAULTS, localeDefaults, optionOverrides, dynamicOptionOverrides]);\n    let refined = {};\n    let currentRaw = this.currentCalendarOptionsInput;\n    let currentRefined = this.currentCalendarOptionsRefined;\n    let anyChanges = false;\n    for (let optionName in raw) {\n      if (this.optionsForRefining.indexOf(optionName) === -1 && (raw[optionName] === currentRaw[optionName] || COMPLEX_OPTION_COMPARATORS[optionName] && optionName in currentRaw && COMPLEX_OPTION_COMPARATORS[optionName](currentRaw[optionName], raw[optionName]))) {\n        refined[optionName] = currentRefined[optionName];\n      } else if (refiners[optionName]) {\n        refined[optionName] = refiners[optionName](raw[optionName]);\n        anyChanges = true;\n      } else {\n        extra[optionName] = currentRaw[optionName];\n      }\n    }\n    if (anyChanges) {\n      this.currentCalendarOptionsInput = raw;\n      this.currentCalendarOptionsRefined = refined;\n      this.stableOptionOverrides = optionOverrides;\n      this.stableDynamicOptionOverrides = dynamicOptionOverrides;\n    }\n    this.optionsForHandling.push(...this.optionsForRefining);\n    this.optionsForRefining = [];\n    return {\n      rawOptions: this.currentCalendarOptionsInput,\n      refinedOptions: this.currentCalendarOptionsRefined,\n      pluginHooks,\n      availableLocaleData,\n      localeDefaults,\n      extra\n    };\n  }\n  _computeCurrentViewData(viewType, optionsData, optionOverrides, dynamicOptionOverrides) {\n    let viewSpec = optionsData.viewSpecs[viewType];\n    if (!viewSpec) {\n      throw new Error(`viewType \"${viewType}\" is not available. Please make sure you've loaded all neccessary plugins`);\n    }\n    let {\n      refinedOptions,\n      extra\n    } = this.processRawViewOptions(viewSpec, optionsData.pluginHooks, optionsData.localeDefaults, optionOverrides, dynamicOptionOverrides);\n    warnUnknownOptions(extra);\n    let dateProfileGenerator = this.buildDateProfileGenerator({\n      dateProfileGeneratorClass: viewSpec.optionDefaults.dateProfileGeneratorClass,\n      duration: viewSpec.duration,\n      durationUnit: viewSpec.durationUnit,\n      usesMinMaxTime: viewSpec.optionDefaults.usesMinMaxTime,\n      dateEnv: optionsData.dateEnv,\n      calendarApi: this.props.calendarApi,\n      slotMinTime: refinedOptions.slotMinTime,\n      slotMaxTime: refinedOptions.slotMaxTime,\n      showNonCurrentDates: refinedOptions.showNonCurrentDates,\n      dayCount: refinedOptions.dayCount,\n      dateAlignment: refinedOptions.dateAlignment,\n      dateIncrement: refinedOptions.dateIncrement,\n      hiddenDays: refinedOptions.hiddenDays,\n      weekends: refinedOptions.weekends,\n      nowInput: refinedOptions.now,\n      validRangeInput: refinedOptions.validRange,\n      visibleRangeInput: refinedOptions.visibleRange,\n      fixedWeekCount: refinedOptions.fixedWeekCount\n    });\n    let viewApi = this.buildViewApi(viewType, this.getCurrentData, optionsData.dateEnv);\n    return {\n      viewSpec,\n      options: refinedOptions,\n      dateProfileGenerator,\n      viewApi\n    };\n  }\n  processRawViewOptions(viewSpec, pluginHooks, localeDefaults, optionOverrides, dynamicOptionOverrides) {\n    let raw = mergeRawOptions([BASE_OPTION_DEFAULTS, viewSpec.optionDefaults, localeDefaults, optionOverrides, viewSpec.optionOverrides, dynamicOptionOverrides]);\n    let refiners = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, BASE_OPTION_REFINERS), CALENDAR_LISTENER_REFINERS), CALENDAR_OPTION_REFINERS), VIEW_OPTION_REFINERS), pluginHooks.listenerRefiners), pluginHooks.optionRefiners);\n    let refined = {};\n    let currentRaw = this.currentViewOptionsInput;\n    let currentRefined = this.currentViewOptionsRefined;\n    let anyChanges = false;\n    let extra = {};\n    for (let optionName in raw) {\n      if (raw[optionName] === currentRaw[optionName] || COMPLEX_OPTION_COMPARATORS[optionName] && COMPLEX_OPTION_COMPARATORS[optionName](raw[optionName], currentRaw[optionName])) {\n        refined[optionName] = currentRefined[optionName];\n      } else {\n        if (raw[optionName] === this.currentCalendarOptionsInput[optionName] || COMPLEX_OPTION_COMPARATORS[optionName] && COMPLEX_OPTION_COMPARATORS[optionName](raw[optionName], this.currentCalendarOptionsInput[optionName])) {\n          if (optionName in this.currentCalendarOptionsRefined) {\n            // might be an \"extra\" prop\n            refined[optionName] = this.currentCalendarOptionsRefined[optionName];\n          }\n        } else if (refiners[optionName]) {\n          refined[optionName] = refiners[optionName](raw[optionName]);\n        } else {\n          extra[optionName] = raw[optionName];\n        }\n        anyChanges = true;\n      }\n    }\n    if (anyChanges) {\n      this.currentViewOptionsInput = raw;\n      this.currentViewOptionsRefined = refined;\n    }\n    return {\n      rawOptions: this.currentViewOptionsInput,\n      refinedOptions: this.currentViewOptionsRefined,\n      extra\n    };\n  }\n}\nfunction buildDateEnv$1(timeZone, explicitLocale, weekNumberCalculation, firstDay, weekText, pluginHooks, availableLocaleData, defaultSeparator) {\n  let locale = buildLocale(explicitLocale || availableLocaleData.defaultCode, availableLocaleData.map);\n  return new DateEnv({\n    calendarSystem: 'gregory',\n    timeZone,\n    namedTimeZoneImpl: pluginHooks.namedTimeZonedImpl,\n    locale,\n    weekNumberCalculation,\n    firstDay,\n    weekText,\n    cmdFormatter: pluginHooks.cmdFormatter,\n    defaultSeparator\n  });\n}\nfunction buildTheme(options, pluginHooks) {\n  let ThemeClass = pluginHooks.themeClasses[options.themeSystem] || StandardTheme;\n  return new ThemeClass(options);\n}\nfunction buildDateProfileGenerator(props) {\n  let DateProfileGeneratorClass = props.dateProfileGeneratorClass || DateProfileGenerator;\n  return new DateProfileGeneratorClass(props);\n}\nfunction buildViewApi(type, getCurrentData, dateEnv) {\n  return new ViewImpl(type, getCurrentData, dateEnv);\n}\nfunction buildEventUiBySource(eventSources) {\n  return mapHash(eventSources, eventSource => eventSource.ui);\n}\nfunction buildEventUiBases(eventDefs, eventUiSingleBase, eventUiBySource) {\n  let eventUiBases = {\n    '': eventUiSingleBase\n  };\n  for (let defId in eventDefs) {\n    let def = eventDefs[defId];\n    if (def.sourceId && eventUiBySource[def.sourceId]) {\n      eventUiBases[defId] = eventUiBySource[def.sourceId];\n    }\n  }\n  return eventUiBases;\n}\nfunction buildViewUiProps(calendarContext) {\n  let {\n    options\n  } = calendarContext;\n  return {\n    eventUiSingleBase: createEventUi({\n      display: options.eventDisplay,\n      editable: options.editable,\n      startEditable: options.eventStartEditable,\n      durationEditable: options.eventDurationEditable,\n      constraint: options.eventConstraint,\n      overlap: typeof options.eventOverlap === 'boolean' ? options.eventOverlap : undefined,\n      allow: options.eventAllow,\n      backgroundColor: options.eventBackgroundColor,\n      borderColor: options.eventBorderColor,\n      textColor: options.eventTextColor,\n      color: options.eventColor\n      // classNames: options.eventClassNames // render hook will handle this\n    }, calendarContext),\n    selectionConfig: createEventUi({\n      constraint: options.selectConstraint,\n      overlap: typeof options.selectOverlap === 'boolean' ? options.selectOverlap : undefined,\n      allow: options.selectAllow\n    }, calendarContext)\n  };\n}\nfunction computeIsLoading(state, context) {\n  for (let isLoadingFunc of context.pluginHooks.isLoadingFuncs) {\n    if (isLoadingFunc(state)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction parseContextBusinessHours(calendarContext) {\n  return parseBusinessHours(calendarContext.options.businessHours, calendarContext);\n}\nfunction warnUnknownOptions(options, viewName) {\n  for (let optionName in options) {\n    console.warn(`Unknown option '${optionName}'` + (viewName ? ` for view '${viewName}'` : ''));\n  }\n}\nclass ToolbarSection extends BaseComponent {\n  render() {\n    let children = this.props.widgetGroups.map(widgetGroup => this.renderWidgetGroup(widgetGroup));\n    return createElement('div', {\n      className: 'fc-toolbar-chunk'\n    }, ...children);\n  }\n  renderWidgetGroup(widgetGroup) {\n    let {\n      props\n    } = this;\n    let {\n      theme\n    } = this.context;\n    let children = [];\n    let isOnlyButtons = true;\n    for (let widget of widgetGroup) {\n      let {\n        buttonName,\n        buttonClick,\n        buttonText,\n        buttonIcon,\n        buttonHint\n      } = widget;\n      if (buttonName === 'title') {\n        isOnlyButtons = false;\n        children.push(createElement(\"h2\", {\n          className: \"fc-toolbar-title\",\n          id: props.titleId\n        }, props.title));\n      } else {\n        let isPressed = buttonName === props.activeButton;\n        let isDisabled = !props.isTodayEnabled && buttonName === 'today' || !props.isPrevEnabled && buttonName === 'prev' || !props.isNextEnabled && buttonName === 'next';\n        let buttonClasses = [`fc-${buttonName}-button`, theme.getClass('button')];\n        if (isPressed) {\n          buttonClasses.push(theme.getClass('buttonActive'));\n        }\n        children.push(createElement(\"button\", {\n          type: \"button\",\n          title: typeof buttonHint === 'function' ? buttonHint(props.navUnit) : buttonHint,\n          disabled: isDisabled,\n          \"aria-pressed\": isPressed,\n          className: buttonClasses.join(' '),\n          onClick: buttonClick\n        }, buttonText || (buttonIcon ? createElement(\"span\", {\n          className: buttonIcon,\n          role: \"img\"\n        }) : '')));\n      }\n    }\n    if (children.length > 1) {\n      let groupClassName = isOnlyButtons && theme.getClass('buttonGroup') || '';\n      return createElement('div', {\n        className: groupClassName\n      }, ...children);\n    }\n    return children[0];\n  }\n}\nclass Toolbar extends BaseComponent {\n  render() {\n    let {\n      model,\n      extraClassName\n    } = this.props;\n    let forceLtr = false;\n    let startContent;\n    let endContent;\n    let sectionWidgets = model.sectionWidgets;\n    let centerContent = sectionWidgets.center;\n    if (sectionWidgets.left) {\n      forceLtr = true;\n      startContent = sectionWidgets.left;\n    } else {\n      startContent = sectionWidgets.start;\n    }\n    if (sectionWidgets.right) {\n      forceLtr = true;\n      endContent = sectionWidgets.right;\n    } else {\n      endContent = sectionWidgets.end;\n    }\n    let classNames = [extraClassName || '', 'fc-toolbar', forceLtr ? 'fc-toolbar-ltr' : ''];\n    return createElement(\"div\", {\n      className: classNames.join(' ')\n    }, this.renderSection('start', startContent || []), this.renderSection('center', centerContent || []), this.renderSection('end', endContent || []));\n  }\n  renderSection(key, widgetGroups) {\n    let {\n      props\n    } = this;\n    return createElement(ToolbarSection, {\n      key: key,\n      widgetGroups: widgetGroups,\n      title: props.title,\n      navUnit: props.navUnit,\n      activeButton: props.activeButton,\n      isTodayEnabled: props.isTodayEnabled,\n      isPrevEnabled: props.isPrevEnabled,\n      isNextEnabled: props.isNextEnabled,\n      titleId: props.titleId\n    });\n  }\n}\nclass ViewHarness extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      availableWidth: null\n    };\n    this.handleEl = el => {\n      this.el = el;\n      setRef(this.props.elRef, el);\n      this.updateAvailableWidth();\n    };\n    this.handleResize = () => {\n      this.updateAvailableWidth();\n    };\n  }\n  render() {\n    let {\n      props,\n      state\n    } = this;\n    let {\n      aspectRatio\n    } = props;\n    let classNames = ['fc-view-harness', aspectRatio || props.liquid || props.height ? 'fc-view-harness-active' // harness controls the height\n    : 'fc-view-harness-passive' // let the view do the height\n    ];\n\n    let height = '';\n    let paddingBottom = '';\n    if (aspectRatio) {\n      if (state.availableWidth !== null) {\n        height = state.availableWidth / aspectRatio;\n      } else {\n        // while waiting to know availableWidth, we can't set height to *zero*\n        // because will cause lots of unnecessary scrollbars within scrollgrid.\n        // BETTER: don't start rendering ANYTHING yet until we know container width\n        // NOTE: why not always use paddingBottom? Causes height oscillation (issue 5606)\n        paddingBottom = `${1 / aspectRatio * 100}%`;\n      }\n    } else {\n      height = props.height || '';\n    }\n    return createElement(\"div\", {\n      \"aria-labelledby\": props.labeledById,\n      ref: this.handleEl,\n      className: classNames.join(' '),\n      style: {\n        height,\n        paddingBottom\n      }\n    }, props.children);\n  }\n  componentDidMount() {\n    this.context.addResizeHandler(this.handleResize);\n  }\n  componentWillUnmount() {\n    this.context.removeResizeHandler(this.handleResize);\n  }\n  updateAvailableWidth() {\n    if (this.el &&\n    // needed. but why?\n    this.props.aspectRatio // aspectRatio is the only height setting that needs availableWidth\n    ) {\n      this.setState({\n        availableWidth: this.el.offsetWidth\n      });\n    }\n  }\n}\n\n/*\nDetects when the user clicks on an event within a DateComponent\n*/\nclass EventClicking extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.handleSegClick = (ev, segEl) => {\n      let {\n        component\n      } = this;\n      let {\n        context\n      } = component;\n      let seg = getElSeg(segEl);\n      if (seg &&\n      // might be the <div> surrounding the more link\n      component.isValidSegDownEl(ev.target)) {\n        // our way to simulate a link click for elements that can't be <a> tags\n        // grab before trigger fired in case trigger trashes DOM thru rerendering\n        let hasUrlContainer = elementClosest(ev.target, '.fc-event-forced-url');\n        let url = hasUrlContainer ? hasUrlContainer.querySelector('a[href]').href : '';\n        context.emitter.trigger('eventClick', {\n          el: segEl,\n          event: new EventImpl(component.context, seg.eventRange.def, seg.eventRange.instance),\n          jsEvent: ev,\n          view: context.viewApi\n        });\n        if (url && !ev.defaultPrevented) {\n          window.location.href = url;\n        }\n      }\n    };\n    this.destroy = listenBySelector(settings.el, 'click', '.fc-event',\n    // on both fg and bg events\n    this.handleSegClick);\n  }\n}\n\n/*\nTriggers events and adds/removes core classNames when the user's pointer\nenters/leaves event-elements of a component.\n*/\nclass EventHovering extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // for simulating an eventMouseLeave when the event el is destroyed while mouse is over it\n    this.handleEventElRemove = el => {\n      if (el === this.currentSegEl) {\n        this.handleSegLeave(null, this.currentSegEl);\n      }\n    };\n    this.handleSegEnter = (ev, segEl) => {\n      if (getElSeg(segEl)) {\n        // TODO: better way to make sure not hovering over more+ link or its wrapper\n        this.currentSegEl = segEl;\n        this.triggerEvent('eventMouseEnter', ev, segEl);\n      }\n    };\n    this.handleSegLeave = (ev, segEl) => {\n      if (this.currentSegEl) {\n        this.currentSegEl = null;\n        this.triggerEvent('eventMouseLeave', ev, segEl);\n      }\n    };\n    this.removeHoverListeners = listenToHoverBySelector(settings.el, '.fc-event',\n    // on both fg and bg events\n    this.handleSegEnter, this.handleSegLeave);\n  }\n  destroy() {\n    this.removeHoverListeners();\n  }\n  triggerEvent(publicEvName, ev, segEl) {\n    let {\n      component\n    } = this;\n    let {\n      context\n    } = component;\n    let seg = getElSeg(segEl);\n    if (!ev || component.isValidSegDownEl(ev.target)) {\n      context.emitter.trigger(publicEvName, {\n        el: segEl,\n        event: new EventImpl(context, seg.eventRange.def, seg.eventRange.instance),\n        jsEvent: ev,\n        view: context.viewApi\n      });\n    }\n  }\n}\nclass CalendarContent extends PureComponent {\n  constructor() {\n    super(...arguments);\n    this.buildViewContext = memoize(buildViewContext);\n    this.buildViewPropTransformers = memoize(buildViewPropTransformers);\n    this.buildToolbarProps = memoize(buildToolbarProps);\n    this.headerRef = createRef();\n    this.footerRef = createRef();\n    this.interactionsStore = {};\n    // eslint-disable-next-line\n    this.state = {\n      viewLabelId: getUniqueDomId()\n    };\n    // Component Registration\n    // -----------------------------------------------------------------------------------------------------------------\n    this.registerInteractiveComponent = (component, settingsInput) => {\n      let settings = parseInteractionSettings(component, settingsInput);\n      let DEFAULT_INTERACTIONS = [EventClicking, EventHovering];\n      let interactionClasses = DEFAULT_INTERACTIONS.concat(this.props.pluginHooks.componentInteractions);\n      let interactions = interactionClasses.map(TheInteractionClass => new TheInteractionClass(settings));\n      this.interactionsStore[component.uid] = interactions;\n      interactionSettingsStore[component.uid] = settings;\n    };\n    this.unregisterInteractiveComponent = component => {\n      let listeners = this.interactionsStore[component.uid];\n      if (listeners) {\n        for (let listener of listeners) {\n          listener.destroy();\n        }\n        delete this.interactionsStore[component.uid];\n      }\n      delete interactionSettingsStore[component.uid];\n    };\n    // Resizing\n    // -----------------------------------------------------------------------------------------------------------------\n    this.resizeRunner = new DelayedRunner(() => {\n      this.props.emitter.trigger('_resize', true); // should window resizes be considered \"forced\" ?\n      this.props.emitter.trigger('windowResize', {\n        view: this.props.viewApi\n      });\n    });\n    this.handleWindowResize = ev => {\n      let {\n        options\n      } = this.props;\n      if (options.handleWindowResize && ev.target === window // avoid jqui events\n      ) {\n        this.resizeRunner.request(options.windowResizeDelay);\n      }\n    };\n  }\n  /*\n  renders INSIDE of an outer div\n  */\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      toolbarConfig,\n      options\n    } = props;\n    let toolbarProps = this.buildToolbarProps(props.viewSpec, props.dateProfile, props.dateProfileGenerator, props.currentDate, getNow(props.options.now, props.dateEnv),\n    // TODO: use NowTimer????\n    props.viewTitle);\n    let viewVGrow = false;\n    let viewHeight = '';\n    let viewAspectRatio;\n    if (props.isHeightAuto || props.forPrint) {\n      viewHeight = '';\n    } else if (options.height != null) {\n      viewVGrow = true;\n    } else if (options.contentHeight != null) {\n      viewHeight = options.contentHeight;\n    } else {\n      viewAspectRatio = Math.max(options.aspectRatio, 0.5); // prevent from getting too tall\n    }\n\n    let viewContext = this.buildViewContext(props.viewSpec, props.viewApi, props.options, props.dateProfileGenerator, props.dateEnv, props.theme, props.pluginHooks, props.dispatch, props.getCurrentData, props.emitter, props.calendarApi, this.registerInteractiveComponent, this.unregisterInteractiveComponent);\n    let viewLabelId = toolbarConfig.header && toolbarConfig.header.hasTitle ? this.state.viewLabelId : '';\n    return createElement(ViewContextType.Provider, {\n      value: viewContext\n    }, toolbarConfig.header && createElement(Toolbar, Object.assign({\n      ref: this.headerRef,\n      extraClassName: \"fc-header-toolbar\",\n      model: toolbarConfig.header,\n      titleId: viewLabelId\n    }, toolbarProps)), createElement(ViewHarness, {\n      liquid: viewVGrow,\n      height: viewHeight,\n      aspectRatio: viewAspectRatio,\n      labeledById: viewLabelId\n    }, this.renderView(props), this.buildAppendContent()), toolbarConfig.footer && createElement(Toolbar, Object.assign({\n      ref: this.footerRef,\n      extraClassName: \"fc-footer-toolbar\",\n      model: toolbarConfig.footer,\n      titleId: \"\"\n    }, toolbarProps)));\n  }\n  componentDidMount() {\n    let {\n      props\n    } = this;\n    this.calendarInteractions = props.pluginHooks.calendarInteractions.map(CalendarInteractionClass => new CalendarInteractionClass(props));\n    window.addEventListener('resize', this.handleWindowResize);\n    let {\n      propSetHandlers\n    } = props.pluginHooks;\n    for (let propName in propSetHandlers) {\n      propSetHandlers[propName](props[propName], props);\n    }\n  }\n  componentDidUpdate(prevProps) {\n    let {\n      props\n    } = this;\n    let {\n      propSetHandlers\n    } = props.pluginHooks;\n    for (let propName in propSetHandlers) {\n      if (props[propName] !== prevProps[propName]) {\n        propSetHandlers[propName](props[propName], props);\n      }\n    }\n  }\n  componentWillUnmount() {\n    window.removeEventListener('resize', this.handleWindowResize);\n    this.resizeRunner.clear();\n    for (let interaction of this.calendarInteractions) {\n      interaction.destroy();\n    }\n    this.props.emitter.trigger('_unmount');\n  }\n  buildAppendContent() {\n    let {\n      props\n    } = this;\n    let children = props.pluginHooks.viewContainerAppends.map(buildAppendContent => buildAppendContent(props));\n    return createElement(Fragment, {}, ...children);\n  }\n  renderView(props) {\n    let {\n      pluginHooks\n    } = props;\n    let {\n      viewSpec\n    } = props;\n    let viewProps = {\n      dateProfile: props.dateProfile,\n      businessHours: props.businessHours,\n      eventStore: props.renderableEventStore,\n      eventUiBases: props.eventUiBases,\n      dateSelection: props.dateSelection,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      isHeightAuto: props.isHeightAuto,\n      forPrint: props.forPrint\n    };\n    let transformers = this.buildViewPropTransformers(pluginHooks.viewPropsTransformers);\n    for (let transformer of transformers) {\n      Object.assign(viewProps, transformer.transform(viewProps, props));\n    }\n    let ViewComponent = viewSpec.component;\n    return createElement(ViewComponent, Object.assign({}, viewProps));\n  }\n}\nfunction buildToolbarProps(viewSpec, dateProfile, dateProfileGenerator, currentDate, now, title) {\n  // don't force any date-profiles to valid date profiles (the `false`) so that we can tell if it's invalid\n  let todayInfo = dateProfileGenerator.build(now, undefined, false); // TODO: need `undefined` or else INFINITE LOOP for some reason\n  let prevInfo = dateProfileGenerator.buildPrev(dateProfile, currentDate, false);\n  let nextInfo = dateProfileGenerator.buildNext(dateProfile, currentDate, false);\n  return {\n    title,\n    activeButton: viewSpec.type,\n    navUnit: viewSpec.singleUnit,\n    isTodayEnabled: todayInfo.isValid && !rangeContainsMarker(dateProfile.currentRange, now),\n    isPrevEnabled: prevInfo.isValid,\n    isNextEnabled: nextInfo.isValid\n  };\n}\n// Plugin\n// -----------------------------------------------------------------------------------------------------------------\nfunction buildViewPropTransformers(theClasses) {\n  return theClasses.map(TheClass => new TheClass());\n}\nclass Calendar extends CalendarImpl {\n  constructor(el, optionOverrides = {}) {\n    super();\n    this.isRendering = false;\n    this.isRendered = false;\n    this.currentClassNames = [];\n    this.customContentRenderId = 0;\n    this.handleAction = action => {\n      // actions we know we want to render immediately\n      switch (action.type) {\n        case 'SET_EVENT_DRAG':\n        case 'SET_EVENT_RESIZE':\n          this.renderRunner.tryDrain();\n      }\n    };\n    this.handleData = data => {\n      this.currentData = data;\n      this.renderRunner.request(data.calendarOptions.rerenderDelay);\n    };\n    this.handleRenderRequest = () => {\n      if (this.isRendering) {\n        this.isRendered = true;\n        let {\n          currentData\n        } = this;\n        flushSync(() => {\n          render(createElement(CalendarRoot, {\n            options: currentData.calendarOptions,\n            theme: currentData.theme,\n            emitter: currentData.emitter\n          }, (classNames, height, isHeightAuto, forPrint) => {\n            this.setClassNames(classNames);\n            this.setHeight(height);\n            return createElement(RenderId.Provider, {\n              value: this.customContentRenderId\n            }, createElement(CalendarContent, Object.assign({\n              isHeightAuto: isHeightAuto,\n              forPrint: forPrint\n            }, currentData)));\n          }), this.el);\n        });\n      } else if (this.isRendered) {\n        this.isRendered = false;\n        render(null, this.el);\n        this.setClassNames([]);\n        this.setHeight('');\n      }\n    };\n    ensureElHasStyles(el);\n    this.el = el;\n    this.renderRunner = new DelayedRunner(this.handleRenderRequest);\n    new CalendarDataManager({\n      optionOverrides,\n      calendarApi: this,\n      onAction: this.handleAction,\n      onData: this.handleData\n    });\n  }\n  render() {\n    let wasRendering = this.isRendering;\n    if (!wasRendering) {\n      this.isRendering = true;\n    } else {\n      this.customContentRenderId += 1;\n    }\n    this.renderRunner.request();\n    if (wasRendering) {\n      this.updateSize();\n    }\n  }\n  destroy() {\n    if (this.isRendering) {\n      this.isRendering = false;\n      this.renderRunner.request();\n    }\n  }\n  updateSize() {\n    flushSync(() => {\n      super.updateSize();\n    });\n  }\n  batchRendering(func) {\n    this.renderRunner.pause('batchRendering');\n    func();\n    this.renderRunner.resume('batchRendering');\n  }\n  pauseRendering() {\n    this.renderRunner.pause('pauseRendering');\n  }\n  resumeRendering() {\n    this.renderRunner.resume('pauseRendering', true);\n  }\n  resetOptions(optionOverrides, changedOptionNames) {\n    this.currentDataManager.resetOptions(optionOverrides, changedOptionNames);\n  }\n  setClassNames(classNames) {\n    if (!isArraysEqual(classNames, this.currentClassNames)) {\n      let {\n        classList\n      } = this.el;\n      for (let className of this.currentClassNames) {\n        classList.remove(className);\n      }\n      for (let className of classNames) {\n        classList.add(className);\n      }\n      this.currentClassNames = classNames;\n    }\n  }\n  setHeight(height) {\n    applyStyleProp(this.el, 'height', height);\n  }\n}\nfunction formatDate(dateInput, options = {}) {\n  let dateEnv = buildDateEnv(options);\n  let formatter = createFormatter(options);\n  let dateMeta = dateEnv.createMarkerMeta(dateInput);\n  if (!dateMeta) {\n    // TODO: warning?\n    return '';\n  }\n  return dateEnv.format(dateMeta.marker, formatter, {\n    forcedTzo: dateMeta.forcedTzo\n  });\n}\nfunction formatRange(startInput, endInput, options) {\n  let dateEnv = buildDateEnv(typeof options === 'object' && options ? options : {}); // pass in if non-null object\n  let formatter = createFormatter(options);\n  let startMeta = dateEnv.createMarkerMeta(startInput);\n  let endMeta = dateEnv.createMarkerMeta(endInput);\n  if (!startMeta || !endMeta) {\n    // TODO: warning?\n    return '';\n  }\n  return dateEnv.formatRange(startMeta.marker, endMeta.marker, formatter, {\n    forcedStartTzo: startMeta.forcedTzo,\n    forcedEndTzo: endMeta.forcedTzo,\n    isEndExclusive: options.isEndExclusive,\n    defaultSeparator: BASE_OPTION_DEFAULTS.defaultRangeSeparator\n  });\n}\n// TODO: more DRY and optimized\nfunction buildDateEnv(settings) {\n  let locale = buildLocale(settings.locale || 'en', organizeRawLocales([]).map); // TODO: don't hardcode 'en' everywhere\n  return new DateEnv(Object.assign(Object.assign({\n    timeZone: BASE_OPTION_DEFAULTS.timeZone,\n    calendarSystem: 'gregory'\n  }, settings), {\n    locale\n  }));\n}\n\n// HELPERS\n/*\nif nextDayThreshold is specified, slicing is done in an all-day fashion.\nyou can get nextDayThreshold from context.nextDayThreshold\n*/\nfunction sliceEvents(props, allDay) {\n  return sliceEventStore(props.eventStore, props.eventUiBases, props.dateProfile.activeRange, allDay ? props.nextDayThreshold : null).fg;\n}\nconst version = '6.1.10';\nexport { Calendar, createPlugin, formatDate, formatRange, globalLocales, globalPlugins, sliceEvents, version };", "map": {"version": 3, "names": ["m", "mergeProps", "g", "guid", "i", "isArraysEqual", "T", "Theme", "a", "mapHash", "B", "BaseComponent", "V", "ViewContextType", "C", "ContentContainer", "b", "buildViewClassNames", "c", "greatestDurationDenominator", "d", "createDuration", "e", "BASE_OPTION_DEFAULTS", "f", "arrayToHash", "h", "filterHash", "j", "buildEventSourceRefiners", "p", "parseEventSource", "k", "formatWithOrdinals", "u", "unpromisify", "l", "buildRangeApiWithTimeZone", "n", "identity", "r", "requestJson", "s", "subtractDurations", "o", "intersectRanges", "q", "startOfDay", "t", "addDays", "v", "hashValuesToArray", "w", "buildEventApis", "D", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "createFormatter", "y", "diffWholeDays", "z", "memoize", "A", "memoizeObjArg", "E", "isPropsEqual", "F", "Emitter", "G", "getInitialDate", "H", "rangeContainsMarker", "I", "createEmptyEventStore", "J", "reduceCurrentDate", "K", "reduceEventStore", "L", "rezoneEventStoreDates", "M", "mergeRawOptions", "N", "BASE_OPTION_REFINERS", "O", "CALENDAR_LISTENER_REFINERS", "P", "CALENDAR_OPTION_REFINERS", "Q", "COMPLEX_OPTION_COMPARATORS", "R", "VIEW_OPTION_REFINERS", "S", "DateEnv", "U", "DateProfileGenerator", "W", "createEventUi", "X", "parseBusinessHours", "Y", "setRef", "Z", "Interaction", "_", "getElSeg", "$", "elementClosest", "a0", "EventImpl", "a1", "listenBySelector", "a2", "listenToHoverBySelector", "a3", "PureComponent", "a4", "buildViewContext", "a5", "getUniqueDomId", "a6", "parseInteractionSettings", "a7", "interactionSettingsStore", "a8", "getNow", "a9", "CalendarImpl", "aa", "flushSync", "ab", "CalendarRoot", "ac", "RenderId", "ad", "ensureElHasStyles", "ae", "applyStyleProp", "af", "sliceEventStore", "ag", "JsonRequestError", "createElement", "createRef", "Fragment", "render", "globalLocales", "MINIMAL_RAW_EN_LOCALE", "code", "week", "dow", "doy", "direction", "buttonText", "prev", "next", "prevYear", "nextYear", "year", "today", "month", "day", "list", "weekText", "weekTextLong", "closeHint", "timeHint", "eventHint", "allDayText", "moreLinkText", "noEventsText", "RAW_EN_LOCALE", "Object", "assign", "buttonHints", "unit", "viewHint", "navLinkHint", "moreLinkHint", "eventCnt", "organizeRawLocales", "explicitRawLocales", "defaultCode", "length", "allRawLocales", "concat", "rawLocaleMap", "en", "rawLocale", "map", "buildLocale", "inputSingular", "available", "Array", "isArray", "parseLocale", "queryLocale", "codeArg", "codes", "raw", "queryRawLocale", "parts", "toLocaleLowerCase", "split", "simpleId", "slice", "join", "merged", "simpleNumberFormat", "Intl", "NumberFormat", "options", "createPlugin", "input", "id", "name", "premiumReleaseDate", "Date", "undefined", "deps", "reducers", "isLoadingFuncs", "contextInit", "eventRefiners", "eventDefMemberAdders", "eventSourceRefiners", "isDraggableTransformers", "eventDragMutationMassagers", "eventDefMutationAppliers", "dateSelectionTransformers", "datePointTransforms", "dateSpanTransforms", "views", "viewPropsTransformers", "isPropsValid", "externalDefTransforms", "viewContainerAppends", "eventDropTransformers", "componentInteractions", "calendarInteractions", "themeClasses", "eventSourceDefs", "cmdFormatter", "recurringTypes", "namedTimeZonedImpl", "initialView", "elementDraggingImpl", "optionChangeHandlers", "scrollGridImpl", "listenerRefiners", "optionRefiners", "propSetHandlers", "buildPluginHooks", "pluginDefs", "globalDefs", "currentPluginIds", "hooks", "addDefs", "defs", "def", "pluginName", "currentId", "combineHooks", "console", "warn", "buildBuildPluginHooks", "currentOverrideDefs", "currentGlobalDefs", "currentHooks", "overrideDefs", "hooks0", "hooks1", "compareOptionalDates", "date0", "date1", "Math", "max", "valueOf", "StandardTheme", "prototype", "classes", "root", "tableCellShaded", "buttonGroup", "button", "buttonActive", "baseIconClass", "iconClasses", "close", "rtlIconClasses", "iconOverrideOption", "iconOverrideCustomButtonOption", "iconOverridePrefix", "compileViewDefs", "defaultConfigs", "overrideConfigs", "hash", "viewType", "ensureViewDef", "viewDef", "buildViewDef", "defaultConfig", "overrideConfig", "queryProp", "theComponent", "superType", "superDef", "Error", "component", "type", "defaults", "rawOptions", "overrides", "parseViewConfigs", "inputs", "parseViewConfig", "content", "createViewHookComponent", "viewProps", "Consumer", "context", "elTag", "elClasses", "viewSpec", "renderProps", "nextDayThreshold", "generatorName", "customGenerator", "classNameGenerator", "classNames", "didMount", "will<PERSON>n<PERSON>", "buildViewSpecs", "defaultInputs", "optionOverrides", "dynamicOptionOverrides", "localeDefaults", "viewDefs", "buildViewSpec", "durationInput", "duration", "durationUnit", "singleUnit", "singleUnitOverrides", "createDurationCached", "denom", "value", "queryButtonText", "optionsSubset", "buttonTextMap", "buttonTextKey", "queryButtonTitle", "button<PERSON>ey", "optionDefaults", "buttonTextOverride", "buttonTextDefault", "buttonTitleOverride", "buttonHint", "buttonTitleDefault", "durationInputMap", "json", "JSON", "stringify", "res", "reduceViewType", "action", "reduceDynamicOptionOverrides", "optionName", "rawOptionValue", "reduceDateProfile", "currentDateProfile", "currentDate", "dateProfileGenerator", "dp", "build", "<PERSON><PERSON><PERSON><PERSON>", "buildPrev", "<PERSON><PERSON><PERSON><PERSON>", "buildNext", "initEventSources", "calendarOptions", "dateProfile", "activeRange", "addSources", "parseInitialSources", "reduceEventSources", "eventSources", "sources", "removeSource", "sourceId", "fetchDirtySources", "fetchSourcesByIds", "sourceIds", "excludeStaticSources", "isRefetch", "receiveResponse", "fetchId", "fetchRange", "reduceEventSourcesNewTimeZone", "computeEventSourcesLoading", "isFetching", "eventSourceHash", "source", "eventSource", "sourceHash", "isSourceDirty", "doesSourceNeedRange", "latestFetchId", "lazyFetching", "start", "end", "prevSources", "sourceIdHash", "nextSources", "fetchSource", "calendarApi", "sourceDef", "pluginHooks", "sourceDefId", "fetch", "range", "rawEvents", "eventSourceSuccess", "call", "response", "success", "dispatch", "error", "errorHandled", "eventSourceFailure", "failure", "message", "refiners", "rawSources", "initialEvents", "unshift", "events", "rawSource", "push", "<PERSON><PERSON><PERSON><PERSON>", "reduceDateSelection", "currentSelection", "selection", "reduceSelectedEvent", "currentInstanceId", "eventInstanceId", "reduceEventDrag", "currentDrag", "newDrag", "state", "affectedEvents", "mutatedEvents", "isEvent", "reduceEventResize", "currentResize", "newResize", "parseToolbars", "calendarOptionOverrides", "theme", "viewSpecs", "header", "headerToolbar", "parseToolbar", "footer", "footerT<PERSON>bar", "sectionStrHash", "sectionWidgets", "viewsWithButtons", "hasTitle", "sectionName", "sectionStr", "sectionRes", "parseSection", "widgets", "isRtl", "calendarCustomButtons", "customButtons", "calendarButtonTextOverrides", "calendarButtonText", "calendarButtonHintOverrides", "calendarButtonHints", "sectionSubstrs", "buttonGroupStr", "buttonName", "customButtonProps", "buttonClick", "buttonIcon", "ev", "click", "target", "getCustomButtonIconClass", "getIconClass", "text", "hint", "changeView", "textFallback", "prevOrNext", "navUnit", "ViewImpl", "constructor", "getCurrentData", "dateEnv", "calendar", "title", "viewTitle", "activeStart", "toDate", "activeEnd", "currentStart", "currentRange", "currentEnd", "getOption", "eventSourceDef$2", "parseMeta", "refined", "arg", "success<PERSON>allback", "meta", "arrayEventSourcePlugin", "eventSourceDef$1", "<PERSON><PERSON><PERSON><PERSON>", "func", "bind", "funcEventSourcePlugin", "JSON_FEED_EVENT_SOURCE_REFINERS", "method", "String", "extraParams", "startParam", "endParam", "timeZoneParam", "eventSourceDef", "url", "format", "toUpperCase", "requestParams", "buildRequestParams", "then", "jsonFeedEventSourcePlugin", "customRequestParams", "params", "formatIso", "timeZone", "SIMPLE_RECURRING_REFINERS", "daysOfWeek", "startTime", "endTime", "startRecur", "endRecur", "recurring", "parse", "recurringData", "createMarker", "allDayGuess", "Boolean", "typeData", "expand", "framingRange", "clippedFramingRange", "expandRanges", "simpleRecurringEventsPlugin", "dowHash", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "instanceStarts", "instanceStart", "getUTCDay", "add", "changeHandlerPlugin", "handleEventSources", "unfoundSources", "_raw", "newInputs", "inputFound", "splice", "unfoundSource", "newInput", "addEventSource", "handleDateProfile", "emitter", "trigger", "view", "viewApi", "handleEventStore", "eventStore", "hasHandlers", "globalPlugins", "TaskRunner", "runTaskOption", "drainedOption", "queue", "delayed<PERSON><PERSON>ner", "drain", "request", "task", "delay", "pause", "scope", "resume", "force", "completedTasks", "shift", "runTask", "drained", "buildTitle", "viewOptions", "test", "currentRangeUnit", "formatRange", "titleFormat", "buildTitleFormat", "isEndExclusive", "isRangeAllDay", "defaultSeparator", "titleRangeSeparator", "days", "CalendarDataManager", "props", "computeCurrentViewData", "_computeCurrentViewData", "buildDateEnv", "buildDateEnv$1", "buildTheme", "buildDateProfileGenerator", "buildViewApi", "buildViewUiProps", "buildEventUiBySource", "buildEventUiBases", "parseContextBusinessHours", "actionRunner", "_handleAction", "updateData", "currentCalendarOptionsInput", "currentCalendarOptionsRefined", "currentViewOptionsInput", "currentViewOptionsRefined", "currentCalendarOptionsRefiners", "optionsForRefining", "optionsForHandling", "data", "optionsData", "computeOptionsData", "currentViewType", "currentViewData", "currentDataManager", "setThisContext", "setOptions", "calendarContext", "callback", "initialState", "businessHours", "eventUiBases", "renderableEventStore", "dateSelection", "eventSelection", "eventDrag", "eventResize", "selectionConfig", "contextAndState", "reducer", "computeIsLoading", "resetOptions", "changedOptionNames", "isEventsLoading", "progressiveEventRendering", "eventUiSingleBase", "eventUiBySource", "newState", "wasLoading", "isLoading", "onAction", "oldData", "changeHandlers", "oldCalendarOptions", "newCalendarOptions", "indexOf", "onData", "stableOptionOverrides", "stableDynamicOptionOverrides", "stableCalendarOptionsData", "refinedOptions", "availableLocaleData", "extra", "processRawCalendarOptions", "warnUnknownOptions", "locale", "weekNumberCalculation", "firstDay", "defaultRangeSeparator", "toolbarConfig", "availableRawLocales", "locales", "plugins", "currentRaw", "currentRefined", "anyChanges", "processRawViewOptions", "dateProfileGeneratorClass", "usesMinMaxTime", "slotMinTime", "slotMaxTime", "showNonCurrentDates", "dayCount", "dateAlignment", "dateIncrement", "hiddenDays", "weekends", "nowInput", "now", "validRangeInput", "validRange", "visibleRangeInput", "visibleRange", "fixedWeekCount", "explicitLocale", "calendarSystem", "namedTimeZoneImpl", "ThemeClass", "themeSystem", "DateProfileGeneratorClass", "ui", "eventDefs", "defId", "display", "eventDisplay", "editable", "startEditable", "eventStartEditable", "durationEditable", "eventDurationEditable", "constraint", "eventConstraint", "overlap", "eventOverlap", "allow", "eventAllow", "backgroundColor", "eventBackgroundColor", "borderColor", "eventBorderColor", "textColor", "eventTextColor", "color", "eventColor", "selectConstraint", "selectOverlap", "selectAllow", "isLoadingFunc", "viewName", "ToolbarSection", "children", "widgetGroups", "widgetGroup", "renderWidgetGroup", "className", "isOnlyButtons", "widget", "titleId", "isPressed", "activeButton", "isDisabled", "isTodayEnabled", "isPrevEnabled", "isNextEnabled", "buttonClasses", "getClass", "disabled", "onClick", "role", "groupClassName", "<PERSON><PERSON><PERSON>", "model", "extraClassName", "forceLtr", "startContent", "endContent", "centerContent", "center", "left", "right", "renderSection", "key", "ViewH<PERSON>ness", "arguments", "availableWidth", "handleEl", "el", "elRef", "updateAvailableWidth", "handleResize", "aspectRatio", "liquid", "height", "paddingBottom", "labeledById", "ref", "style", "componentDidMount", "addResizeHandler", "componentWillUnmount", "removeResizeHandler", "setState", "offsetWidth", "EventClicking", "settings", "handleSegClick", "segEl", "seg", "isValidSegDownEl", "hasUrl<PERSON><PERSON>r", "querySelector", "href", "event", "eventRange", "instance", "jsEvent", "defaultPrevented", "window", "location", "destroy", "EventHovering", "handleEventElRemove", "currentSegEl", "handleSegLeave", "handleSegEnter", "triggerEvent", "removeHoverListeners", "publicEvName", "CalendarContent", "buildViewPropTransformers", "buildToolbarProps", "headerRef", "footerRef", "interactionsStore", "viewLabelId", "registerInteractiveComponent", "settingsInput", "DEFAULT_INTERACTIONS", "interactionClasses", "interactions", "TheInteractionClass", "uid", "unregisterInteractiveComponent", "listeners", "listener", "resize<PERSON><PERSON><PERSON>", "handleWindowResize", "windowResizeDelay", "toolbarProps", "viewVGrow", "viewHeight", "viewAspectRatio", "isHeightAuto", "forPrint", "contentHeight", "viewContext", "Provider", "render<PERSON>iew", "buildAppendContent", "CalendarInteractionClass", "addEventListener", "propName", "componentDidUpdate", "prevProps", "removeEventListener", "clear", "interaction", "transformers", "transformer", "transform", "ViewComponent", "todayInfo", "prevInfo", "nextInfo", "theClasses", "TheClass", "Calendar", "isRendering", "isRendered", "currentClassNames", "customContentRenderId", "handleAction", "render<PERSON><PERSON>ner", "tryDrain", "handleData", "currentData", "rerenderDelay", "handleRenderRequest", "setClassNames", "setHeight", "wasRendering", "updateSize", "batchRendering", "pauseRendering", "resumeRendering", "classList", "remove", "formatDate", "dateInput", "formatter", "dateMeta", "createMarkerMeta", "marker", "<PERSON><PERSON><PERSON>", "startInput", "endInput", "startMeta", "endMeta", "forcedStartTzo", "forcedEndTzo", "sliceEvents", "allDay", "fg", "version"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@fullcalendar/core/index.js"], "sourcesContent": ["import { m as mergeProps, g as guid, i as isArraysEqual, T as Theme, a as mapHash, B as BaseComponent, V as ViewContextType, C as ContentContainer, b as buildViewClassNames, c as greatestDurationDenominator, d as createDuration, e as BASE_OPTION_DEFAULTS, f as arrayToHash, h as filterHash, j as buildEventSourceRefiners, p as parseEventSource, k as formatWithOrdinals, u as unpromisify, l as buildRangeApiWithTimeZone, n as identity, r as requestJson, s as subtractDurations, o as intersectRanges, q as startOfDay, t as addDays, v as hashValuesToArray, w as buildEventApis, D as DelayedRunner, x as createFormatter, y as diffWholeDays, z as memoize, A as memoizeObjArg, E as isPropsEqual, F as Emitter, G as getInitialDate, H as rangeContainsMarker, I as createEmptyEventStore, J as reduceCurrentDate, K as reduceEventStore, L as rezoneEventStoreDates, M as mergeRawOptions, N as BASE_OPTION_REFINERS, O as CALENDAR_LISTENER_REFINERS, P as CALENDAR_OPTION_REFINERS, Q as COMPLEX_OPTION_COMPARATORS, R as VIEW_OPTION_REFINERS, S as DateEnv, U as DateProfileGenerator, W as createEventUi, X as parseBusinessHours, Y as setRef, Z as Interaction, _ as getElSeg, $ as elementClosest, a0 as EventImpl, a1 as listenBySelector, a2 as listenToHoverBySelector, a3 as PureComponent, a4 as buildViewContext, a5 as getUniqueDomId, a6 as parseInteractionSettings, a7 as interactionSettingsStore, a8 as getNow, a9 as CalendarImpl, aa as flushSync, ab as CalendarRoot, ac as RenderId, ad as ensureElHasStyles, ae as applyStyleProp, af as sliceEventStore } from './internal-common.js';\nexport { ag as JsonRequestError } from './internal-common.js';\nimport { createElement, createRef, Fragment, render } from 'preact';\nimport 'preact/compat';\n\nconst globalLocales = [];\n\nconst MINIMAL_RAW_EN_LOCALE = {\n    code: 'en',\n    week: {\n        dow: 0,\n        doy: 4, // 4 days need to be within the year to be considered the first week\n    },\n    direction: 'ltr',\n    buttonText: {\n        prev: 'prev',\n        next: 'next',\n        prevYear: 'prev year',\n        nextYear: 'next year',\n        year: 'year',\n        today: 'today',\n        month: 'month',\n        week: 'week',\n        day: 'day',\n        list: 'list',\n    },\n    weekText: 'W',\n    weekTextLong: 'Week',\n    closeHint: 'Close',\n    timeHint: 'Time',\n    eventHint: 'Event',\n    allDayText: 'all-day',\n    moreLinkText: 'more',\n    noEventsText: 'No events to display',\n};\nconst RAW_EN_LOCALE = Object.assign(Object.assign({}, MINIMAL_RAW_EN_LOCALE), { \n    // Includes things we don't want other locales to inherit,\n    // things that derive from other translatable strings.\n    buttonHints: {\n        prev: 'Previous $0',\n        next: 'Next $0',\n        today(buttonText, unit) {\n            return (unit === 'day')\n                ? 'Today'\n                : `This ${buttonText}`;\n        },\n    }, viewHint: '$0 view', navLinkHint: 'Go to $0', moreLinkHint(eventCnt) {\n        return `Show ${eventCnt} more event${eventCnt === 1 ? '' : 's'}`;\n    } });\nfunction organizeRawLocales(explicitRawLocales) {\n    let defaultCode = explicitRawLocales.length > 0 ? explicitRawLocales[0].code : 'en';\n    let allRawLocales = globalLocales.concat(explicitRawLocales);\n    let rawLocaleMap = {\n        en: RAW_EN_LOCALE,\n    };\n    for (let rawLocale of allRawLocales) {\n        rawLocaleMap[rawLocale.code] = rawLocale;\n    }\n    return {\n        map: rawLocaleMap,\n        defaultCode,\n    };\n}\nfunction buildLocale(inputSingular, available) {\n    if (typeof inputSingular === 'object' && !Array.isArray(inputSingular)) {\n        return parseLocale(inputSingular.code, [inputSingular.code], inputSingular);\n    }\n    return queryLocale(inputSingular, available);\n}\nfunction queryLocale(codeArg, available) {\n    let codes = [].concat(codeArg || []); // will convert to array\n    let raw = queryRawLocale(codes, available) || RAW_EN_LOCALE;\n    return parseLocale(codeArg, codes, raw);\n}\nfunction queryRawLocale(codes, available) {\n    for (let i = 0; i < codes.length; i += 1) {\n        let parts = codes[i].toLocaleLowerCase().split('-');\n        for (let j = parts.length; j > 0; j -= 1) {\n            let simpleId = parts.slice(0, j).join('-');\n            if (available[simpleId]) {\n                return available[simpleId];\n            }\n        }\n    }\n    return null;\n}\nfunction parseLocale(codeArg, codes, raw) {\n    let merged = mergeProps([MINIMAL_RAW_EN_LOCALE, raw], ['buttonText']);\n    delete merged.code; // don't want this part of the options\n    let { week } = merged;\n    delete merged.week;\n    return {\n        codeArg,\n        codes,\n        week,\n        simpleNumberFormat: new Intl.NumberFormat(codeArg),\n        options: merged,\n    };\n}\n\n// TODO: easier way to add new hooks? need to update a million things\nfunction createPlugin(input) {\n    return {\n        id: guid(),\n        name: input.name,\n        premiumReleaseDate: input.premiumReleaseDate ? new Date(input.premiumReleaseDate) : undefined,\n        deps: input.deps || [],\n        reducers: input.reducers || [],\n        isLoadingFuncs: input.isLoadingFuncs || [],\n        contextInit: [].concat(input.contextInit || []),\n        eventRefiners: input.eventRefiners || {},\n        eventDefMemberAdders: input.eventDefMemberAdders || [],\n        eventSourceRefiners: input.eventSourceRefiners || {},\n        isDraggableTransformers: input.isDraggableTransformers || [],\n        eventDragMutationMassagers: input.eventDragMutationMassagers || [],\n        eventDefMutationAppliers: input.eventDefMutationAppliers || [],\n        dateSelectionTransformers: input.dateSelectionTransformers || [],\n        datePointTransforms: input.datePointTransforms || [],\n        dateSpanTransforms: input.dateSpanTransforms || [],\n        views: input.views || {},\n        viewPropsTransformers: input.viewPropsTransformers || [],\n        isPropsValid: input.isPropsValid || null,\n        externalDefTransforms: input.externalDefTransforms || [],\n        viewContainerAppends: input.viewContainerAppends || [],\n        eventDropTransformers: input.eventDropTransformers || [],\n        componentInteractions: input.componentInteractions || [],\n        calendarInteractions: input.calendarInteractions || [],\n        themeClasses: input.themeClasses || {},\n        eventSourceDefs: input.eventSourceDefs || [],\n        cmdFormatter: input.cmdFormatter,\n        recurringTypes: input.recurringTypes || [],\n        namedTimeZonedImpl: input.namedTimeZonedImpl,\n        initialView: input.initialView || '',\n        elementDraggingImpl: input.elementDraggingImpl,\n        optionChangeHandlers: input.optionChangeHandlers || {},\n        scrollGridImpl: input.scrollGridImpl || null,\n        listenerRefiners: input.listenerRefiners || {},\n        optionRefiners: input.optionRefiners || {},\n        propSetHandlers: input.propSetHandlers || {},\n    };\n}\nfunction buildPluginHooks(pluginDefs, globalDefs) {\n    let currentPluginIds = {};\n    let hooks = {\n        premiumReleaseDate: undefined,\n        reducers: [],\n        isLoadingFuncs: [],\n        contextInit: [],\n        eventRefiners: {},\n        eventDefMemberAdders: [],\n        eventSourceRefiners: {},\n        isDraggableTransformers: [],\n        eventDragMutationMassagers: [],\n        eventDefMutationAppliers: [],\n        dateSelectionTransformers: [],\n        datePointTransforms: [],\n        dateSpanTransforms: [],\n        views: {},\n        viewPropsTransformers: [],\n        isPropsValid: null,\n        externalDefTransforms: [],\n        viewContainerAppends: [],\n        eventDropTransformers: [],\n        componentInteractions: [],\n        calendarInteractions: [],\n        themeClasses: {},\n        eventSourceDefs: [],\n        cmdFormatter: null,\n        recurringTypes: [],\n        namedTimeZonedImpl: null,\n        initialView: '',\n        elementDraggingImpl: null,\n        optionChangeHandlers: {},\n        scrollGridImpl: null,\n        listenerRefiners: {},\n        optionRefiners: {},\n        propSetHandlers: {},\n    };\n    function addDefs(defs) {\n        for (let def of defs) {\n            const pluginName = def.name;\n            const currentId = currentPluginIds[pluginName];\n            if (currentId === undefined) {\n                currentPluginIds[pluginName] = def.id;\n                addDefs(def.deps);\n                hooks = combineHooks(hooks, def);\n            }\n            else if (currentId !== def.id) {\n                // different ID than the one already added\n                console.warn(`Duplicate plugin '${pluginName}'`);\n            }\n        }\n    }\n    if (pluginDefs) {\n        addDefs(pluginDefs);\n    }\n    addDefs(globalDefs);\n    return hooks;\n}\nfunction buildBuildPluginHooks() {\n    let currentOverrideDefs = [];\n    let currentGlobalDefs = [];\n    let currentHooks;\n    return (overrideDefs, globalDefs) => {\n        if (!currentHooks || !isArraysEqual(overrideDefs, currentOverrideDefs) || !isArraysEqual(globalDefs, currentGlobalDefs)) {\n            currentHooks = buildPluginHooks(overrideDefs, globalDefs);\n        }\n        currentOverrideDefs = overrideDefs;\n        currentGlobalDefs = globalDefs;\n        return currentHooks;\n    };\n}\nfunction combineHooks(hooks0, hooks1) {\n    return {\n        premiumReleaseDate: compareOptionalDates(hooks0.premiumReleaseDate, hooks1.premiumReleaseDate),\n        reducers: hooks0.reducers.concat(hooks1.reducers),\n        isLoadingFuncs: hooks0.isLoadingFuncs.concat(hooks1.isLoadingFuncs),\n        contextInit: hooks0.contextInit.concat(hooks1.contextInit),\n        eventRefiners: Object.assign(Object.assign({}, hooks0.eventRefiners), hooks1.eventRefiners),\n        eventDefMemberAdders: hooks0.eventDefMemberAdders.concat(hooks1.eventDefMemberAdders),\n        eventSourceRefiners: Object.assign(Object.assign({}, hooks0.eventSourceRefiners), hooks1.eventSourceRefiners),\n        isDraggableTransformers: hooks0.isDraggableTransformers.concat(hooks1.isDraggableTransformers),\n        eventDragMutationMassagers: hooks0.eventDragMutationMassagers.concat(hooks1.eventDragMutationMassagers),\n        eventDefMutationAppliers: hooks0.eventDefMutationAppliers.concat(hooks1.eventDefMutationAppliers),\n        dateSelectionTransformers: hooks0.dateSelectionTransformers.concat(hooks1.dateSelectionTransformers),\n        datePointTransforms: hooks0.datePointTransforms.concat(hooks1.datePointTransforms),\n        dateSpanTransforms: hooks0.dateSpanTransforms.concat(hooks1.dateSpanTransforms),\n        views: Object.assign(Object.assign({}, hooks0.views), hooks1.views),\n        viewPropsTransformers: hooks0.viewPropsTransformers.concat(hooks1.viewPropsTransformers),\n        isPropsValid: hooks1.isPropsValid || hooks0.isPropsValid,\n        externalDefTransforms: hooks0.externalDefTransforms.concat(hooks1.externalDefTransforms),\n        viewContainerAppends: hooks0.viewContainerAppends.concat(hooks1.viewContainerAppends),\n        eventDropTransformers: hooks0.eventDropTransformers.concat(hooks1.eventDropTransformers),\n        calendarInteractions: hooks0.calendarInteractions.concat(hooks1.calendarInteractions),\n        componentInteractions: hooks0.componentInteractions.concat(hooks1.componentInteractions),\n        themeClasses: Object.assign(Object.assign({}, hooks0.themeClasses), hooks1.themeClasses),\n        eventSourceDefs: hooks0.eventSourceDefs.concat(hooks1.eventSourceDefs),\n        cmdFormatter: hooks1.cmdFormatter || hooks0.cmdFormatter,\n        recurringTypes: hooks0.recurringTypes.concat(hooks1.recurringTypes),\n        namedTimeZonedImpl: hooks1.namedTimeZonedImpl || hooks0.namedTimeZonedImpl,\n        initialView: hooks0.initialView || hooks1.initialView,\n        elementDraggingImpl: hooks0.elementDraggingImpl || hooks1.elementDraggingImpl,\n        optionChangeHandlers: Object.assign(Object.assign({}, hooks0.optionChangeHandlers), hooks1.optionChangeHandlers),\n        scrollGridImpl: hooks1.scrollGridImpl || hooks0.scrollGridImpl,\n        listenerRefiners: Object.assign(Object.assign({}, hooks0.listenerRefiners), hooks1.listenerRefiners),\n        optionRefiners: Object.assign(Object.assign({}, hooks0.optionRefiners), hooks1.optionRefiners),\n        propSetHandlers: Object.assign(Object.assign({}, hooks0.propSetHandlers), hooks1.propSetHandlers),\n    };\n}\nfunction compareOptionalDates(date0, date1) {\n    if (date0 === undefined) {\n        return date1;\n    }\n    if (date1 === undefined) {\n        return date0;\n    }\n    return new Date(Math.max(date0.valueOf(), date1.valueOf()));\n}\n\nclass StandardTheme extends Theme {\n}\nStandardTheme.prototype.classes = {\n    root: 'fc-theme-standard',\n    tableCellShaded: 'fc-cell-shaded',\n    buttonGroup: 'fc-button-group',\n    button: 'fc-button fc-button-primary',\n    buttonActive: 'fc-button-active',\n};\nStandardTheme.prototype.baseIconClass = 'fc-icon';\nStandardTheme.prototype.iconClasses = {\n    close: 'fc-icon-x',\n    prev: 'fc-icon-chevron-left',\n    next: 'fc-icon-chevron-right',\n    prevYear: 'fc-icon-chevrons-left',\n    nextYear: 'fc-icon-chevrons-right',\n};\nStandardTheme.prototype.rtlIconClasses = {\n    prev: 'fc-icon-chevron-right',\n    next: 'fc-icon-chevron-left',\n    prevYear: 'fc-icon-chevrons-right',\n    nextYear: 'fc-icon-chevrons-left',\n};\nStandardTheme.prototype.iconOverrideOption = 'buttonIcons'; // TODO: make TS-friendly\nStandardTheme.prototype.iconOverrideCustomButtonOption = 'icon';\nStandardTheme.prototype.iconOverridePrefix = 'fc-icon-';\n\nfunction compileViewDefs(defaultConfigs, overrideConfigs) {\n    let hash = {};\n    let viewType;\n    for (viewType in defaultConfigs) {\n        ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n    }\n    for (viewType in overrideConfigs) {\n        ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n    }\n    return hash;\n}\nfunction ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs) {\n    if (hash[viewType]) {\n        return hash[viewType];\n    }\n    let viewDef = buildViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n    if (viewDef) {\n        hash[viewType] = viewDef;\n    }\n    return viewDef;\n}\nfunction buildViewDef(viewType, hash, defaultConfigs, overrideConfigs) {\n    let defaultConfig = defaultConfigs[viewType];\n    let overrideConfig = overrideConfigs[viewType];\n    let queryProp = (name) => ((defaultConfig && defaultConfig[name] !== null) ? defaultConfig[name] :\n        ((overrideConfig && overrideConfig[name] !== null) ? overrideConfig[name] : null));\n    let theComponent = queryProp('component');\n    let superType = queryProp('superType');\n    let superDef = null;\n    if (superType) {\n        if (superType === viewType) {\n            throw new Error('Can\\'t have a custom view type that references itself');\n        }\n        superDef = ensureViewDef(superType, hash, defaultConfigs, overrideConfigs);\n    }\n    if (!theComponent && superDef) {\n        theComponent = superDef.component;\n    }\n    if (!theComponent) {\n        return null; // don't throw a warning, might be settings for a single-unit view\n    }\n    return {\n        type: viewType,\n        component: theComponent,\n        defaults: Object.assign(Object.assign({}, (superDef ? superDef.defaults : {})), (defaultConfig ? defaultConfig.rawOptions : {})),\n        overrides: Object.assign(Object.assign({}, (superDef ? superDef.overrides : {})), (overrideConfig ? overrideConfig.rawOptions : {})),\n    };\n}\n\nfunction parseViewConfigs(inputs) {\n    return mapHash(inputs, parseViewConfig);\n}\nfunction parseViewConfig(input) {\n    let rawOptions = typeof input === 'function' ?\n        { component: input } :\n        input;\n    let { component } = rawOptions;\n    if (rawOptions.content) {\n        // TODO: remove content/classNames/didMount/etc from options?\n        component = createViewHookComponent(rawOptions);\n    }\n    else if (component && !(component.prototype instanceof BaseComponent)) {\n        // WHY?: people were using `component` property for `content`\n        // TODO: converge on one setting name\n        component = createViewHookComponent(Object.assign(Object.assign({}, rawOptions), { content: component }));\n    }\n    return {\n        superType: rawOptions.type,\n        component: component,\n        rawOptions, // includes type and component too :(\n    };\n}\nfunction createViewHookComponent(options) {\n    return (viewProps) => (createElement(ViewContextType.Consumer, null, (context) => (createElement(ContentContainer, { elTag: \"div\", elClasses: buildViewClassNames(context.viewSpec), renderProps: Object.assign(Object.assign({}, viewProps), { nextDayThreshold: context.options.nextDayThreshold }), generatorName: undefined, customGenerator: options.content, classNameGenerator: options.classNames, didMount: options.didMount, willUnmount: options.willUnmount }))));\n}\n\nfunction buildViewSpecs(defaultInputs, optionOverrides, dynamicOptionOverrides, localeDefaults) {\n    let defaultConfigs = parseViewConfigs(defaultInputs);\n    let overrideConfigs = parseViewConfigs(optionOverrides.views);\n    let viewDefs = compileViewDefs(defaultConfigs, overrideConfigs);\n    return mapHash(viewDefs, (viewDef) => buildViewSpec(viewDef, overrideConfigs, optionOverrides, dynamicOptionOverrides, localeDefaults));\n}\nfunction buildViewSpec(viewDef, overrideConfigs, optionOverrides, dynamicOptionOverrides, localeDefaults) {\n    let durationInput = viewDef.overrides.duration ||\n        viewDef.defaults.duration ||\n        dynamicOptionOverrides.duration ||\n        optionOverrides.duration;\n    let duration = null;\n    let durationUnit = '';\n    let singleUnit = '';\n    let singleUnitOverrides = {};\n    if (durationInput) {\n        duration = createDurationCached(durationInput);\n        if (duration) { // valid?\n            let denom = greatestDurationDenominator(duration);\n            durationUnit = denom.unit;\n            if (denom.value === 1) {\n                singleUnit = durationUnit;\n                singleUnitOverrides = overrideConfigs[durationUnit] ? overrideConfigs[durationUnit].rawOptions : {};\n            }\n        }\n    }\n    let queryButtonText = (optionsSubset) => {\n        let buttonTextMap = optionsSubset.buttonText || {};\n        let buttonTextKey = viewDef.defaults.buttonTextKey;\n        if (buttonTextKey != null && buttonTextMap[buttonTextKey] != null) {\n            return buttonTextMap[buttonTextKey];\n        }\n        if (buttonTextMap[viewDef.type] != null) {\n            return buttonTextMap[viewDef.type];\n        }\n        if (buttonTextMap[singleUnit] != null) {\n            return buttonTextMap[singleUnit];\n        }\n        return null;\n    };\n    let queryButtonTitle = (optionsSubset) => {\n        let buttonHints = optionsSubset.buttonHints || {};\n        let buttonKey = viewDef.defaults.buttonTextKey; // use same key as text\n        if (buttonKey != null && buttonHints[buttonKey] != null) {\n            return buttonHints[buttonKey];\n        }\n        if (buttonHints[viewDef.type] != null) {\n            return buttonHints[viewDef.type];\n        }\n        if (buttonHints[singleUnit] != null) {\n            return buttonHints[singleUnit];\n        }\n        return null;\n    };\n    return {\n        type: viewDef.type,\n        component: viewDef.component,\n        duration,\n        durationUnit,\n        singleUnit,\n        optionDefaults: viewDef.defaults,\n        optionOverrides: Object.assign(Object.assign({}, singleUnitOverrides), viewDef.overrides),\n        buttonTextOverride: queryButtonText(dynamicOptionOverrides) ||\n            queryButtonText(optionOverrides) || // constructor-specified buttonText lookup hash takes precedence\n            viewDef.overrides.buttonText,\n        buttonTextDefault: queryButtonText(localeDefaults) ||\n            viewDef.defaults.buttonText ||\n            queryButtonText(BASE_OPTION_DEFAULTS) ||\n            viewDef.type,\n        // not DRY\n        buttonTitleOverride: queryButtonTitle(dynamicOptionOverrides) ||\n            queryButtonTitle(optionOverrides) ||\n            viewDef.overrides.buttonHint,\n        buttonTitleDefault: queryButtonTitle(localeDefaults) ||\n            viewDef.defaults.buttonHint ||\n            queryButtonTitle(BASE_OPTION_DEFAULTS),\n        // will eventually fall back to buttonText\n    };\n}\n// hack to get memoization working\nlet durationInputMap = {};\nfunction createDurationCached(durationInput) {\n    let json = JSON.stringify(durationInput);\n    let res = durationInputMap[json];\n    if (res === undefined) {\n        res = createDuration(durationInput);\n        durationInputMap[json] = res;\n    }\n    return res;\n}\n\nfunction reduceViewType(viewType, action) {\n    switch (action.type) {\n        case 'CHANGE_VIEW_TYPE':\n            viewType = action.viewType;\n    }\n    return viewType;\n}\n\nfunction reduceDynamicOptionOverrides(dynamicOptionOverrides, action) {\n    switch (action.type) {\n        case 'SET_OPTION':\n            return Object.assign(Object.assign({}, dynamicOptionOverrides), { [action.optionName]: action.rawOptionValue });\n        default:\n            return dynamicOptionOverrides;\n    }\n}\n\nfunction reduceDateProfile(currentDateProfile, action, currentDate, dateProfileGenerator) {\n    let dp;\n    switch (action.type) {\n        case 'CHANGE_VIEW_TYPE':\n            return dateProfileGenerator.build(action.dateMarker || currentDate);\n        case 'CHANGE_DATE':\n            return dateProfileGenerator.build(action.dateMarker);\n        case 'PREV':\n            dp = dateProfileGenerator.buildPrev(currentDateProfile, currentDate);\n            if (dp.isValid) {\n                return dp;\n            }\n            break;\n        case 'NEXT':\n            dp = dateProfileGenerator.buildNext(currentDateProfile, currentDate);\n            if (dp.isValid) {\n                return dp;\n            }\n            break;\n    }\n    return currentDateProfile;\n}\n\nfunction initEventSources(calendarOptions, dateProfile, context) {\n    let activeRange = dateProfile ? dateProfile.activeRange : null;\n    return addSources({}, parseInitialSources(calendarOptions, context), activeRange, context);\n}\nfunction reduceEventSources(eventSources, action, dateProfile, context) {\n    let activeRange = dateProfile ? dateProfile.activeRange : null; // need this check?\n    switch (action.type) {\n        case 'ADD_EVENT_SOURCES': // already parsed\n            return addSources(eventSources, action.sources, activeRange, context);\n        case 'REMOVE_EVENT_SOURCE':\n            return removeSource(eventSources, action.sourceId);\n        case 'PREV': // TODO: how do we track all actions that affect dateProfile :(\n        case 'NEXT':\n        case 'CHANGE_DATE':\n        case 'CHANGE_VIEW_TYPE':\n            if (dateProfile) {\n                return fetchDirtySources(eventSources, activeRange, context);\n            }\n            return eventSources;\n        case 'FETCH_EVENT_SOURCES':\n            return fetchSourcesByIds(eventSources, action.sourceIds ? // why no type?\n                arrayToHash(action.sourceIds) :\n                excludeStaticSources(eventSources, context), activeRange, action.isRefetch || false, context);\n        case 'RECEIVE_EVENTS':\n        case 'RECEIVE_EVENT_ERROR':\n            return receiveResponse(eventSources, action.sourceId, action.fetchId, action.fetchRange);\n        case 'REMOVE_ALL_EVENT_SOURCES':\n            return {};\n        default:\n            return eventSources;\n    }\n}\nfunction reduceEventSourcesNewTimeZone(eventSources, dateProfile, context) {\n    let activeRange = dateProfile ? dateProfile.activeRange : null; // need this check?\n    return fetchSourcesByIds(eventSources, excludeStaticSources(eventSources, context), activeRange, true, context);\n}\nfunction computeEventSourcesLoading(eventSources) {\n    for (let sourceId in eventSources) {\n        if (eventSources[sourceId].isFetching) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction addSources(eventSourceHash, sources, fetchRange, context) {\n    let hash = {};\n    for (let source of sources) {\n        hash[source.sourceId] = source;\n    }\n    if (fetchRange) {\n        hash = fetchDirtySources(hash, fetchRange, context);\n    }\n    return Object.assign(Object.assign({}, eventSourceHash), hash);\n}\nfunction removeSource(eventSourceHash, sourceId) {\n    return filterHash(eventSourceHash, (eventSource) => eventSource.sourceId !== sourceId);\n}\nfunction fetchDirtySources(sourceHash, fetchRange, context) {\n    return fetchSourcesByIds(sourceHash, filterHash(sourceHash, (eventSource) => isSourceDirty(eventSource, fetchRange, context)), fetchRange, false, context);\n}\nfunction isSourceDirty(eventSource, fetchRange, context) {\n    if (!doesSourceNeedRange(eventSource, context)) {\n        return !eventSource.latestFetchId;\n    }\n    return !context.options.lazyFetching ||\n        !eventSource.fetchRange ||\n        eventSource.isFetching || // always cancel outdated in-progress fetches\n        fetchRange.start < eventSource.fetchRange.start ||\n        fetchRange.end > eventSource.fetchRange.end;\n}\nfunction fetchSourcesByIds(prevSources, sourceIdHash, fetchRange, isRefetch, context) {\n    let nextSources = {};\n    for (let sourceId in prevSources) {\n        let source = prevSources[sourceId];\n        if (sourceIdHash[sourceId]) {\n            nextSources[sourceId] = fetchSource(source, fetchRange, isRefetch, context);\n        }\n        else {\n            nextSources[sourceId] = source;\n        }\n    }\n    return nextSources;\n}\nfunction fetchSource(eventSource, fetchRange, isRefetch, context) {\n    let { options, calendarApi } = context;\n    let sourceDef = context.pluginHooks.eventSourceDefs[eventSource.sourceDefId];\n    let fetchId = guid();\n    sourceDef.fetch({\n        eventSource,\n        range: fetchRange,\n        isRefetch,\n        context,\n    }, (res) => {\n        let { rawEvents } = res;\n        if (options.eventSourceSuccess) {\n            rawEvents = options.eventSourceSuccess.call(calendarApi, rawEvents, res.response) || rawEvents;\n        }\n        if (eventSource.success) {\n            rawEvents = eventSource.success.call(calendarApi, rawEvents, res.response) || rawEvents;\n        }\n        context.dispatch({\n            type: 'RECEIVE_EVENTS',\n            sourceId: eventSource.sourceId,\n            fetchId,\n            fetchRange,\n            rawEvents,\n        });\n    }, (error) => {\n        let errorHandled = false;\n        if (options.eventSourceFailure) {\n            options.eventSourceFailure.call(calendarApi, error);\n            errorHandled = true;\n        }\n        if (eventSource.failure) {\n            eventSource.failure(error);\n            errorHandled = true;\n        }\n        if (!errorHandled) {\n            console.warn(error.message, error);\n        }\n        context.dispatch({\n            type: 'RECEIVE_EVENT_ERROR',\n            sourceId: eventSource.sourceId,\n            fetchId,\n            fetchRange,\n            error,\n        });\n    });\n    return Object.assign(Object.assign({}, eventSource), { isFetching: true, latestFetchId: fetchId });\n}\nfunction receiveResponse(sourceHash, sourceId, fetchId, fetchRange) {\n    let eventSource = sourceHash[sourceId];\n    if (eventSource && // not already removed\n        fetchId === eventSource.latestFetchId) {\n        return Object.assign(Object.assign({}, sourceHash), { [sourceId]: Object.assign(Object.assign({}, eventSource), { isFetching: false, fetchRange }) });\n    }\n    return sourceHash;\n}\nfunction excludeStaticSources(eventSources, context) {\n    return filterHash(eventSources, (eventSource) => doesSourceNeedRange(eventSource, context));\n}\nfunction parseInitialSources(rawOptions, context) {\n    let refiners = buildEventSourceRefiners(context);\n    let rawSources = [].concat(rawOptions.eventSources || []);\n    let sources = []; // parsed\n    if (rawOptions.initialEvents) {\n        rawSources.unshift(rawOptions.initialEvents);\n    }\n    if (rawOptions.events) {\n        rawSources.unshift(rawOptions.events);\n    }\n    for (let rawSource of rawSources) {\n        let source = parseEventSource(rawSource, context, refiners);\n        if (source) {\n            sources.push(source);\n        }\n    }\n    return sources;\n}\nfunction doesSourceNeedRange(eventSource, context) {\n    let defs = context.pluginHooks.eventSourceDefs;\n    return !defs[eventSource.sourceDefId].ignoreRange;\n}\n\nfunction reduceDateSelection(currentSelection, action) {\n    switch (action.type) {\n        case 'UNSELECT_DATES':\n            return null;\n        case 'SELECT_DATES':\n            return action.selection;\n        default:\n            return currentSelection;\n    }\n}\n\nfunction reduceSelectedEvent(currentInstanceId, action) {\n    switch (action.type) {\n        case 'UNSELECT_EVENT':\n            return '';\n        case 'SELECT_EVENT':\n            return action.eventInstanceId;\n        default:\n            return currentInstanceId;\n    }\n}\n\nfunction reduceEventDrag(currentDrag, action) {\n    let newDrag;\n    switch (action.type) {\n        case 'UNSET_EVENT_DRAG':\n            return null;\n        case 'SET_EVENT_DRAG':\n            newDrag = action.state;\n            return {\n                affectedEvents: newDrag.affectedEvents,\n                mutatedEvents: newDrag.mutatedEvents,\n                isEvent: newDrag.isEvent,\n            };\n        default:\n            return currentDrag;\n    }\n}\n\nfunction reduceEventResize(currentResize, action) {\n    let newResize;\n    switch (action.type) {\n        case 'UNSET_EVENT_RESIZE':\n            return null;\n        case 'SET_EVENT_RESIZE':\n            newResize = action.state;\n            return {\n                affectedEvents: newResize.affectedEvents,\n                mutatedEvents: newResize.mutatedEvents,\n                isEvent: newResize.isEvent,\n            };\n        default:\n            return currentResize;\n    }\n}\n\nfunction parseToolbars(calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) {\n    let header = calendarOptions.headerToolbar ? parseToolbar(calendarOptions.headerToolbar, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) : null;\n    let footer = calendarOptions.footerToolbar ? parseToolbar(calendarOptions.footerToolbar, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) : null;\n    return { header, footer };\n}\nfunction parseToolbar(sectionStrHash, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) {\n    let sectionWidgets = {};\n    let viewsWithButtons = [];\n    let hasTitle = false;\n    for (let sectionName in sectionStrHash) {\n        let sectionStr = sectionStrHash[sectionName];\n        let sectionRes = parseSection(sectionStr, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi);\n        sectionWidgets[sectionName] = sectionRes.widgets;\n        viewsWithButtons.push(...sectionRes.viewsWithButtons);\n        hasTitle = hasTitle || sectionRes.hasTitle;\n    }\n    return { sectionWidgets, viewsWithButtons, hasTitle };\n}\n/*\nBAD: querying icons and text here. should be done at render time\n*/\nfunction parseSection(sectionStr, calendarOptions, // defaults+overrides, then refined\ncalendarOptionOverrides, // overrides only!, unrefined :(\ntheme, viewSpecs, calendarApi) {\n    let isRtl = calendarOptions.direction === 'rtl';\n    let calendarCustomButtons = calendarOptions.customButtons || {};\n    let calendarButtonTextOverrides = calendarOptionOverrides.buttonText || {};\n    let calendarButtonText = calendarOptions.buttonText || {};\n    let calendarButtonHintOverrides = calendarOptionOverrides.buttonHints || {};\n    let calendarButtonHints = calendarOptions.buttonHints || {};\n    let sectionSubstrs = sectionStr ? sectionStr.split(' ') : [];\n    let viewsWithButtons = [];\n    let hasTitle = false;\n    let widgets = sectionSubstrs.map((buttonGroupStr) => (buttonGroupStr.split(',').map((buttonName) => {\n        if (buttonName === 'title') {\n            hasTitle = true;\n            return { buttonName };\n        }\n        let customButtonProps;\n        let viewSpec;\n        let buttonClick;\n        let buttonIcon; // only one of these will be set\n        let buttonText; // \"\n        let buttonHint;\n        // ^ for the title=\"\" attribute, for accessibility\n        if ((customButtonProps = calendarCustomButtons[buttonName])) {\n            buttonClick = (ev) => {\n                if (customButtonProps.click) {\n                    customButtonProps.click.call(ev.target, ev, ev.target); // TODO: use Calendar this context?\n                }\n            };\n            (buttonIcon = theme.getCustomButtonIconClass(customButtonProps)) ||\n                (buttonIcon = theme.getIconClass(buttonName, isRtl)) ||\n                (buttonText = customButtonProps.text);\n            buttonHint = customButtonProps.hint || customButtonProps.text;\n        }\n        else if ((viewSpec = viewSpecs[buttonName])) {\n            viewsWithButtons.push(buttonName);\n            buttonClick = () => {\n                calendarApi.changeView(buttonName);\n            };\n            (buttonText = viewSpec.buttonTextOverride) ||\n                (buttonIcon = theme.getIconClass(buttonName, isRtl)) ||\n                (buttonText = viewSpec.buttonTextDefault);\n            let textFallback = viewSpec.buttonTextOverride ||\n                viewSpec.buttonTextDefault;\n            buttonHint = formatWithOrdinals(viewSpec.buttonTitleOverride ||\n                viewSpec.buttonTitleDefault ||\n                calendarOptions.viewHint, [textFallback, buttonName], // view-name = buttonName\n            textFallback);\n        }\n        else if (calendarApi[buttonName]) { // a calendarApi method\n            buttonClick = () => {\n                calendarApi[buttonName]();\n            };\n            (buttonText = calendarButtonTextOverrides[buttonName]) ||\n                (buttonIcon = theme.getIconClass(buttonName, isRtl)) ||\n                (buttonText = calendarButtonText[buttonName]); // everything else is considered default\n            if (buttonName === 'prevYear' || buttonName === 'nextYear') {\n                let prevOrNext = buttonName === 'prevYear' ? 'prev' : 'next';\n                buttonHint = formatWithOrdinals(calendarButtonHintOverrides[prevOrNext] ||\n                    calendarButtonHints[prevOrNext], [\n                    calendarButtonText.year || 'year',\n                    'year',\n                ], calendarButtonText[buttonName]);\n            }\n            else {\n                buttonHint = (navUnit) => formatWithOrdinals(calendarButtonHintOverrides[buttonName] ||\n                    calendarButtonHints[buttonName], [\n                    calendarButtonText[navUnit] || navUnit,\n                    navUnit,\n                ], calendarButtonText[buttonName]);\n            }\n        }\n        return { buttonName, buttonClick, buttonIcon, buttonText, buttonHint };\n    })));\n    return { widgets, viewsWithButtons, hasTitle };\n}\n\n// always represents the current view. otherwise, it'd need to change value every time date changes\nclass ViewImpl {\n    constructor(type, getCurrentData, dateEnv) {\n        this.type = type;\n        this.getCurrentData = getCurrentData;\n        this.dateEnv = dateEnv;\n    }\n    get calendar() {\n        return this.getCurrentData().calendarApi;\n    }\n    get title() {\n        return this.getCurrentData().viewTitle;\n    }\n    get activeStart() {\n        return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start);\n    }\n    get activeEnd() {\n        return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end);\n    }\n    get currentStart() {\n        return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start);\n    }\n    get currentEnd() {\n        return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end);\n    }\n    getOption(name) {\n        return this.getCurrentData().options[name]; // are the view-specific options\n    }\n}\n\nlet eventSourceDef$2 = {\n    ignoreRange: true,\n    parseMeta(refined) {\n        if (Array.isArray(refined.events)) {\n            return refined.events;\n        }\n        return null;\n    },\n    fetch(arg, successCallback) {\n        successCallback({\n            rawEvents: arg.eventSource.meta,\n        });\n    },\n};\nconst arrayEventSourcePlugin = createPlugin({\n    name: 'array-event-source',\n    eventSourceDefs: [eventSourceDef$2],\n});\n\nlet eventSourceDef$1 = {\n    parseMeta(refined) {\n        if (typeof refined.events === 'function') {\n            return refined.events;\n        }\n        return null;\n    },\n    fetch(arg, successCallback, errorCallback) {\n        const { dateEnv } = arg.context;\n        const func = arg.eventSource.meta;\n        unpromisify(func.bind(null, buildRangeApiWithTimeZone(arg.range, dateEnv)), (rawEvents) => successCallback({ rawEvents }), errorCallback);\n    },\n};\nconst funcEventSourcePlugin = createPlugin({\n    name: 'func-event-source',\n    eventSourceDefs: [eventSourceDef$1],\n});\n\nconst JSON_FEED_EVENT_SOURCE_REFINERS = {\n    method: String,\n    extraParams: identity,\n    startParam: String,\n    endParam: String,\n    timeZoneParam: String,\n};\n\nlet eventSourceDef = {\n    parseMeta(refined) {\n        if (refined.url && (refined.format === 'json' || !refined.format)) {\n            return {\n                url: refined.url,\n                format: 'json',\n                method: (refined.method || 'GET').toUpperCase(),\n                extraParams: refined.extraParams,\n                startParam: refined.startParam,\n                endParam: refined.endParam,\n                timeZoneParam: refined.timeZoneParam,\n            };\n        }\n        return null;\n    },\n    fetch(arg, successCallback, errorCallback) {\n        const { meta } = arg.eventSource;\n        const requestParams = buildRequestParams(meta, arg.range, arg.context);\n        requestJson(meta.method, meta.url, requestParams).then(([rawEvents, response]) => {\n            successCallback({ rawEvents, response });\n        }, errorCallback);\n    },\n};\nconst jsonFeedEventSourcePlugin = createPlugin({\n    name: 'json-event-source',\n    eventSourceRefiners: JSON_FEED_EVENT_SOURCE_REFINERS,\n    eventSourceDefs: [eventSourceDef],\n});\nfunction buildRequestParams(meta, range, context) {\n    let { dateEnv, options } = context;\n    let startParam;\n    let endParam;\n    let timeZoneParam;\n    let customRequestParams;\n    let params = {};\n    startParam = meta.startParam;\n    if (startParam == null) {\n        startParam = options.startParam;\n    }\n    endParam = meta.endParam;\n    if (endParam == null) {\n        endParam = options.endParam;\n    }\n    timeZoneParam = meta.timeZoneParam;\n    if (timeZoneParam == null) {\n        timeZoneParam = options.timeZoneParam;\n    }\n    // retrieve any outbound GET/POST data from the options\n    if (typeof meta.extraParams === 'function') {\n        // supplied as a function that returns a key/value object\n        customRequestParams = meta.extraParams();\n    }\n    else {\n        // probably supplied as a straight key/value object\n        customRequestParams = meta.extraParams || {};\n    }\n    Object.assign(params, customRequestParams);\n    params[startParam] = dateEnv.formatIso(range.start);\n    params[endParam] = dateEnv.formatIso(range.end);\n    if (dateEnv.timeZone !== 'local') {\n        params[timeZoneParam] = dateEnv.timeZone;\n    }\n    return params;\n}\n\nconst SIMPLE_RECURRING_REFINERS = {\n    daysOfWeek: identity,\n    startTime: createDuration,\n    endTime: createDuration,\n    duration: createDuration,\n    startRecur: identity,\n    endRecur: identity,\n};\n\nlet recurring = {\n    parse(refined, dateEnv) {\n        if (refined.daysOfWeek || refined.startTime || refined.endTime || refined.startRecur || refined.endRecur) {\n            let recurringData = {\n                daysOfWeek: refined.daysOfWeek || null,\n                startTime: refined.startTime || null,\n                endTime: refined.endTime || null,\n                startRecur: refined.startRecur ? dateEnv.createMarker(refined.startRecur) : null,\n                endRecur: refined.endRecur ? dateEnv.createMarker(refined.endRecur) : null,\n            };\n            let duration;\n            if (refined.duration) {\n                duration = refined.duration;\n            }\n            if (!duration && refined.startTime && refined.endTime) {\n                duration = subtractDurations(refined.endTime, refined.startTime);\n            }\n            return {\n                allDayGuess: Boolean(!refined.startTime && !refined.endTime),\n                duration,\n                typeData: recurringData, // doesn't need endTime anymore but oh well\n            };\n        }\n        return null;\n    },\n    expand(typeData, framingRange, dateEnv) {\n        let clippedFramingRange = intersectRanges(framingRange, { start: typeData.startRecur, end: typeData.endRecur });\n        if (clippedFramingRange) {\n            return expandRanges(typeData.daysOfWeek, typeData.startTime, clippedFramingRange, dateEnv);\n        }\n        return [];\n    },\n};\nconst simpleRecurringEventsPlugin = createPlugin({\n    name: 'simple-recurring-event',\n    recurringTypes: [recurring],\n    eventRefiners: SIMPLE_RECURRING_REFINERS,\n});\nfunction expandRanges(daysOfWeek, startTime, framingRange, dateEnv) {\n    let dowHash = daysOfWeek ? arrayToHash(daysOfWeek) : null;\n    let dayMarker = startOfDay(framingRange.start);\n    let endMarker = framingRange.end;\n    let instanceStarts = [];\n    while (dayMarker < endMarker) {\n        let instanceStart;\n        // if everyday, or this particular day-of-week\n        if (!dowHash || dowHash[dayMarker.getUTCDay()]) {\n            if (startTime) {\n                instanceStart = dateEnv.add(dayMarker, startTime);\n            }\n            else {\n                instanceStart = dayMarker;\n            }\n            instanceStarts.push(instanceStart);\n        }\n        dayMarker = addDays(dayMarker, 1);\n    }\n    return instanceStarts;\n}\n\nconst changeHandlerPlugin = createPlugin({\n    name: 'change-handler',\n    optionChangeHandlers: {\n        events(events, context) {\n            handleEventSources([events], context);\n        },\n        eventSources: handleEventSources,\n    },\n});\n/*\nBUG: if `event` was supplied, all previously-given `eventSources` will be wiped out\n*/\nfunction handleEventSources(inputs, context) {\n    let unfoundSources = hashValuesToArray(context.getCurrentData().eventSources);\n    if (unfoundSources.length === 1 &&\n        inputs.length === 1 &&\n        Array.isArray(unfoundSources[0]._raw) &&\n        Array.isArray(inputs[0])) {\n        context.dispatch({\n            type: 'RESET_RAW_EVENTS',\n            sourceId: unfoundSources[0].sourceId,\n            rawEvents: inputs[0],\n        });\n        return;\n    }\n    let newInputs = [];\n    for (let input of inputs) {\n        let inputFound = false;\n        for (let i = 0; i < unfoundSources.length; i += 1) {\n            if (unfoundSources[i]._raw === input) {\n                unfoundSources.splice(i, 1); // delete\n                inputFound = true;\n                break;\n            }\n        }\n        if (!inputFound) {\n            newInputs.push(input);\n        }\n    }\n    for (let unfoundSource of unfoundSources) {\n        context.dispatch({\n            type: 'REMOVE_EVENT_SOURCE',\n            sourceId: unfoundSource.sourceId,\n        });\n    }\n    for (let newInput of newInputs) {\n        context.calendarApi.addEventSource(newInput);\n    }\n}\n\nfunction handleDateProfile(dateProfile, context) {\n    context.emitter.trigger('datesSet', Object.assign(Object.assign({}, buildRangeApiWithTimeZone(dateProfile.activeRange, context.dateEnv)), { view: context.viewApi }));\n}\n\nfunction handleEventStore(eventStore, context) {\n    let { emitter } = context;\n    if (emitter.hasHandlers('eventsSet')) {\n        emitter.trigger('eventsSet', buildEventApis(eventStore, context));\n    }\n}\n\n/*\nthis array is exposed on the root namespace so that UMD plugins can add to it.\nsee the rollup-bundles script.\n*/\nconst globalPlugins = [\n    arrayEventSourcePlugin,\n    funcEventSourcePlugin,\n    jsonFeedEventSourcePlugin,\n    simpleRecurringEventsPlugin,\n    changeHandlerPlugin,\n    createPlugin({\n        name: 'misc',\n        isLoadingFuncs: [\n            (state) => computeEventSourcesLoading(state.eventSources),\n        ],\n        propSetHandlers: {\n            dateProfile: handleDateProfile,\n            eventStore: handleEventStore,\n        },\n    }),\n];\n\nclass TaskRunner {\n    constructor(runTaskOption, drainedOption) {\n        this.runTaskOption = runTaskOption;\n        this.drainedOption = drainedOption;\n        this.queue = [];\n        this.delayedRunner = new DelayedRunner(this.drain.bind(this));\n    }\n    request(task, delay) {\n        this.queue.push(task);\n        this.delayedRunner.request(delay);\n    }\n    pause(scope) {\n        this.delayedRunner.pause(scope);\n    }\n    resume(scope, force) {\n        this.delayedRunner.resume(scope, force);\n    }\n    drain() {\n        let { queue } = this;\n        while (queue.length) {\n            let completedTasks = [];\n            let task;\n            while ((task = queue.shift())) {\n                this.runTask(task);\n                completedTasks.push(task);\n            }\n            this.drained(completedTasks);\n        } // keep going, in case new tasks were added in the drained handler\n    }\n    runTask(task) {\n        if (this.runTaskOption) {\n            this.runTaskOption(task);\n        }\n    }\n    drained(completedTasks) {\n        if (this.drainedOption) {\n            this.drainedOption(completedTasks);\n        }\n    }\n}\n\n// Computes what the title at the top of the calendarApi should be for this view\nfunction buildTitle(dateProfile, viewOptions, dateEnv) {\n    let range;\n    // for views that span a large unit of time, show the proper interval, ignoring stray days before and after\n    if (/^(year|month)$/.test(dateProfile.currentRangeUnit)) {\n        range = dateProfile.currentRange;\n    }\n    else { // for day units or smaller, use the actual day range\n        range = dateProfile.activeRange;\n    }\n    return dateEnv.formatRange(range.start, range.end, createFormatter(viewOptions.titleFormat || buildTitleFormat(dateProfile)), {\n        isEndExclusive: dateProfile.isRangeAllDay,\n        defaultSeparator: viewOptions.titleRangeSeparator,\n    });\n}\n// Generates the format string that should be used to generate the title for the current date range.\n// Attempts to compute the most appropriate format if not explicitly specified with `titleFormat`.\nfunction buildTitleFormat(dateProfile) {\n    let { currentRangeUnit } = dateProfile;\n    if (currentRangeUnit === 'year') {\n        return { year: 'numeric' };\n    }\n    if (currentRangeUnit === 'month') {\n        return { year: 'numeric', month: 'long' }; // like \"September 2014\"\n    }\n    let days = diffWholeDays(dateProfile.currentRange.start, dateProfile.currentRange.end);\n    if (days !== null && days > 1) {\n        // multi-day range. shorter, like \"Sep 9 - 10 2014\"\n        return { year: 'numeric', month: 'short', day: 'numeric' };\n    }\n    // one day. longer, like \"September 9 2014\"\n    return { year: 'numeric', month: 'long', day: 'numeric' };\n}\n\n// in future refactor, do the redux-style function(state=initial) for initial-state\n// also, whatever is happening in constructor, have it happen in action queue too\nclass CalendarDataManager {\n    constructor(props) {\n        this.computeCurrentViewData = memoize(this._computeCurrentViewData);\n        this.organizeRawLocales = memoize(organizeRawLocales);\n        this.buildLocale = memoize(buildLocale);\n        this.buildPluginHooks = buildBuildPluginHooks();\n        this.buildDateEnv = memoize(buildDateEnv$1);\n        this.buildTheme = memoize(buildTheme);\n        this.parseToolbars = memoize(parseToolbars);\n        this.buildViewSpecs = memoize(buildViewSpecs);\n        this.buildDateProfileGenerator = memoizeObjArg(buildDateProfileGenerator);\n        this.buildViewApi = memoize(buildViewApi);\n        this.buildViewUiProps = memoizeObjArg(buildViewUiProps);\n        this.buildEventUiBySource = memoize(buildEventUiBySource, isPropsEqual);\n        this.buildEventUiBases = memoize(buildEventUiBases);\n        this.parseContextBusinessHours = memoizeObjArg(parseContextBusinessHours);\n        this.buildTitle = memoize(buildTitle);\n        this.emitter = new Emitter();\n        this.actionRunner = new TaskRunner(this._handleAction.bind(this), this.updateData.bind(this));\n        this.currentCalendarOptionsInput = {};\n        this.currentCalendarOptionsRefined = {};\n        this.currentViewOptionsInput = {};\n        this.currentViewOptionsRefined = {};\n        this.currentCalendarOptionsRefiners = {};\n        this.optionsForRefining = [];\n        this.optionsForHandling = [];\n        this.getCurrentData = () => this.data;\n        this.dispatch = (action) => {\n            this.actionRunner.request(action); // protects against recursive calls to _handleAction\n        };\n        this.props = props;\n        this.actionRunner.pause();\n        let dynamicOptionOverrides = {};\n        let optionsData = this.computeOptionsData(props.optionOverrides, dynamicOptionOverrides, props.calendarApi);\n        let currentViewType = optionsData.calendarOptions.initialView || optionsData.pluginHooks.initialView;\n        let currentViewData = this.computeCurrentViewData(currentViewType, optionsData, props.optionOverrides, dynamicOptionOverrides);\n        // wire things up\n        // TODO: not DRY\n        props.calendarApi.currentDataManager = this;\n        this.emitter.setThisContext(props.calendarApi);\n        this.emitter.setOptions(currentViewData.options);\n        let currentDate = getInitialDate(optionsData.calendarOptions, optionsData.dateEnv);\n        let dateProfile = currentViewData.dateProfileGenerator.build(currentDate);\n        if (!rangeContainsMarker(dateProfile.activeRange, currentDate)) {\n            currentDate = dateProfile.currentRange.start;\n        }\n        let calendarContext = {\n            dateEnv: optionsData.dateEnv,\n            options: optionsData.calendarOptions,\n            pluginHooks: optionsData.pluginHooks,\n            calendarApi: props.calendarApi,\n            dispatch: this.dispatch,\n            emitter: this.emitter,\n            getCurrentData: this.getCurrentData,\n        };\n        // needs to be after setThisContext\n        for (let callback of optionsData.pluginHooks.contextInit) {\n            callback(calendarContext);\n        }\n        // NOT DRY\n        let eventSources = initEventSources(optionsData.calendarOptions, dateProfile, calendarContext);\n        let initialState = {\n            dynamicOptionOverrides,\n            currentViewType,\n            currentDate,\n            dateProfile,\n            businessHours: this.parseContextBusinessHours(calendarContext),\n            eventSources,\n            eventUiBases: {},\n            eventStore: createEmptyEventStore(),\n            renderableEventStore: createEmptyEventStore(),\n            dateSelection: null,\n            eventSelection: '',\n            eventDrag: null,\n            eventResize: null,\n            selectionConfig: this.buildViewUiProps(calendarContext).selectionConfig,\n        };\n        let contextAndState = Object.assign(Object.assign({}, calendarContext), initialState);\n        for (let reducer of optionsData.pluginHooks.reducers) {\n            Object.assign(initialState, reducer(null, null, contextAndState));\n        }\n        if (computeIsLoading(initialState, calendarContext)) {\n            this.emitter.trigger('loading', true); // NOT DRY\n        }\n        this.state = initialState;\n        this.updateData();\n        this.actionRunner.resume();\n    }\n    resetOptions(optionOverrides, changedOptionNames) {\n        let { props } = this;\n        if (changedOptionNames === undefined) {\n            props.optionOverrides = optionOverrides;\n        }\n        else {\n            props.optionOverrides = Object.assign(Object.assign({}, (props.optionOverrides || {})), optionOverrides);\n            this.optionsForRefining.push(...changedOptionNames);\n        }\n        if (changedOptionNames === undefined || changedOptionNames.length) {\n            this.actionRunner.request({\n                type: 'NOTHING',\n            });\n        }\n    }\n    _handleAction(action) {\n        let { props, state, emitter } = this;\n        let dynamicOptionOverrides = reduceDynamicOptionOverrides(state.dynamicOptionOverrides, action);\n        let optionsData = this.computeOptionsData(props.optionOverrides, dynamicOptionOverrides, props.calendarApi);\n        let currentViewType = reduceViewType(state.currentViewType, action);\n        let currentViewData = this.computeCurrentViewData(currentViewType, optionsData, props.optionOverrides, dynamicOptionOverrides);\n        // wire things up\n        // TODO: not DRY\n        props.calendarApi.currentDataManager = this;\n        emitter.setThisContext(props.calendarApi);\n        emitter.setOptions(currentViewData.options);\n        let calendarContext = {\n            dateEnv: optionsData.dateEnv,\n            options: optionsData.calendarOptions,\n            pluginHooks: optionsData.pluginHooks,\n            calendarApi: props.calendarApi,\n            dispatch: this.dispatch,\n            emitter,\n            getCurrentData: this.getCurrentData,\n        };\n        let { currentDate, dateProfile } = state;\n        if (this.data && this.data.dateProfileGenerator !== currentViewData.dateProfileGenerator) { // hack\n            dateProfile = currentViewData.dateProfileGenerator.build(currentDate);\n        }\n        currentDate = reduceCurrentDate(currentDate, action);\n        dateProfile = reduceDateProfile(dateProfile, action, currentDate, currentViewData.dateProfileGenerator);\n        if (action.type === 'PREV' || // TODO: move this logic into DateProfileGenerator\n            action.type === 'NEXT' || // \"\n            !rangeContainsMarker(dateProfile.currentRange, currentDate)) {\n            currentDate = dateProfile.currentRange.start;\n        }\n        let eventSources = reduceEventSources(state.eventSources, action, dateProfile, calendarContext);\n        let eventStore = reduceEventStore(state.eventStore, action, eventSources, dateProfile, calendarContext);\n        let isEventsLoading = computeEventSourcesLoading(eventSources); // BAD. also called in this func in computeIsLoading\n        let renderableEventStore = (isEventsLoading && !currentViewData.options.progressiveEventRendering) ?\n            (state.renderableEventStore || eventStore) : // try from previous state\n            eventStore;\n        let { eventUiSingleBase, selectionConfig } = this.buildViewUiProps(calendarContext); // will memoize obj\n        let eventUiBySource = this.buildEventUiBySource(eventSources);\n        let eventUiBases = this.buildEventUiBases(renderableEventStore.defs, eventUiSingleBase, eventUiBySource);\n        let newState = {\n            dynamicOptionOverrides,\n            currentViewType,\n            currentDate,\n            dateProfile,\n            eventSources,\n            eventStore,\n            renderableEventStore,\n            selectionConfig,\n            eventUiBases,\n            businessHours: this.parseContextBusinessHours(calendarContext),\n            dateSelection: reduceDateSelection(state.dateSelection, action),\n            eventSelection: reduceSelectedEvent(state.eventSelection, action),\n            eventDrag: reduceEventDrag(state.eventDrag, action),\n            eventResize: reduceEventResize(state.eventResize, action),\n        };\n        let contextAndState = Object.assign(Object.assign({}, calendarContext), newState);\n        for (let reducer of optionsData.pluginHooks.reducers) {\n            Object.assign(newState, reducer(state, action, contextAndState)); // give the OLD state, for old value\n        }\n        let wasLoading = computeIsLoading(state, calendarContext);\n        let isLoading = computeIsLoading(newState, calendarContext);\n        // TODO: use propSetHandlers in plugin system\n        if (!wasLoading && isLoading) {\n            emitter.trigger('loading', true);\n        }\n        else if (wasLoading && !isLoading) {\n            emitter.trigger('loading', false);\n        }\n        this.state = newState;\n        if (props.onAction) {\n            props.onAction(action);\n        }\n    }\n    updateData() {\n        let { props, state } = this;\n        let oldData = this.data;\n        let optionsData = this.computeOptionsData(props.optionOverrides, state.dynamicOptionOverrides, props.calendarApi);\n        let currentViewData = this.computeCurrentViewData(state.currentViewType, optionsData, props.optionOverrides, state.dynamicOptionOverrides);\n        let data = this.data = Object.assign(Object.assign(Object.assign({ viewTitle: this.buildTitle(state.dateProfile, currentViewData.options, optionsData.dateEnv), calendarApi: props.calendarApi, dispatch: this.dispatch, emitter: this.emitter, getCurrentData: this.getCurrentData }, optionsData), currentViewData), state);\n        let changeHandlers = optionsData.pluginHooks.optionChangeHandlers;\n        let oldCalendarOptions = oldData && oldData.calendarOptions;\n        let newCalendarOptions = optionsData.calendarOptions;\n        if (oldCalendarOptions && oldCalendarOptions !== newCalendarOptions) {\n            if (oldCalendarOptions.timeZone !== newCalendarOptions.timeZone) {\n                // hack\n                state.eventSources = data.eventSources = reduceEventSourcesNewTimeZone(data.eventSources, state.dateProfile, data);\n                state.eventStore = data.eventStore = rezoneEventStoreDates(data.eventStore, oldData.dateEnv, data.dateEnv);\n                state.renderableEventStore = data.renderableEventStore = rezoneEventStoreDates(data.renderableEventStore, oldData.dateEnv, data.dateEnv);\n            }\n            for (let optionName in changeHandlers) {\n                if (this.optionsForHandling.indexOf(optionName) !== -1 ||\n                    oldCalendarOptions[optionName] !== newCalendarOptions[optionName]) {\n                    changeHandlers[optionName](newCalendarOptions[optionName], data);\n                }\n            }\n        }\n        this.optionsForHandling = [];\n        if (props.onData) {\n            props.onData(data);\n        }\n    }\n    computeOptionsData(optionOverrides, dynamicOptionOverrides, calendarApi) {\n        // TODO: blacklist options that are handled by optionChangeHandlers\n        if (!this.optionsForRefining.length &&\n            optionOverrides === this.stableOptionOverrides &&\n            dynamicOptionOverrides === this.stableDynamicOptionOverrides) {\n            return this.stableCalendarOptionsData;\n        }\n        let { refinedOptions, pluginHooks, localeDefaults, availableLocaleData, extra, } = this.processRawCalendarOptions(optionOverrides, dynamicOptionOverrides);\n        warnUnknownOptions(extra);\n        let dateEnv = this.buildDateEnv(refinedOptions.timeZone, refinedOptions.locale, refinedOptions.weekNumberCalculation, refinedOptions.firstDay, refinedOptions.weekText, pluginHooks, availableLocaleData, refinedOptions.defaultRangeSeparator);\n        let viewSpecs = this.buildViewSpecs(pluginHooks.views, this.stableOptionOverrides, this.stableDynamicOptionOverrides, localeDefaults);\n        let theme = this.buildTheme(refinedOptions, pluginHooks);\n        let toolbarConfig = this.parseToolbars(refinedOptions, this.stableOptionOverrides, theme, viewSpecs, calendarApi);\n        return this.stableCalendarOptionsData = {\n            calendarOptions: refinedOptions,\n            pluginHooks,\n            dateEnv,\n            viewSpecs,\n            theme,\n            toolbarConfig,\n            localeDefaults,\n            availableRawLocales: availableLocaleData.map,\n        };\n    }\n    // always called from behind a memoizer\n    processRawCalendarOptions(optionOverrides, dynamicOptionOverrides) {\n        let { locales, locale } = mergeRawOptions([\n            BASE_OPTION_DEFAULTS,\n            optionOverrides,\n            dynamicOptionOverrides,\n        ]);\n        let availableLocaleData = this.organizeRawLocales(locales);\n        let availableRawLocales = availableLocaleData.map;\n        let localeDefaults = this.buildLocale(locale || availableLocaleData.defaultCode, availableRawLocales).options;\n        let pluginHooks = this.buildPluginHooks(optionOverrides.plugins || [], globalPlugins);\n        let refiners = this.currentCalendarOptionsRefiners = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, BASE_OPTION_REFINERS), CALENDAR_LISTENER_REFINERS), CALENDAR_OPTION_REFINERS), pluginHooks.listenerRefiners), pluginHooks.optionRefiners);\n        let extra = {};\n        let raw = mergeRawOptions([\n            BASE_OPTION_DEFAULTS,\n            localeDefaults,\n            optionOverrides,\n            dynamicOptionOverrides,\n        ]);\n        let refined = {};\n        let currentRaw = this.currentCalendarOptionsInput;\n        let currentRefined = this.currentCalendarOptionsRefined;\n        let anyChanges = false;\n        for (let optionName in raw) {\n            if (this.optionsForRefining.indexOf(optionName) === -1 && (raw[optionName] === currentRaw[optionName] || (COMPLEX_OPTION_COMPARATORS[optionName] &&\n                (optionName in currentRaw) &&\n                COMPLEX_OPTION_COMPARATORS[optionName](currentRaw[optionName], raw[optionName])))) {\n                refined[optionName] = currentRefined[optionName];\n            }\n            else if (refiners[optionName]) {\n                refined[optionName] = refiners[optionName](raw[optionName]);\n                anyChanges = true;\n            }\n            else {\n                extra[optionName] = currentRaw[optionName];\n            }\n        }\n        if (anyChanges) {\n            this.currentCalendarOptionsInput = raw;\n            this.currentCalendarOptionsRefined = refined;\n            this.stableOptionOverrides = optionOverrides;\n            this.stableDynamicOptionOverrides = dynamicOptionOverrides;\n        }\n        this.optionsForHandling.push(...this.optionsForRefining);\n        this.optionsForRefining = [];\n        return {\n            rawOptions: this.currentCalendarOptionsInput,\n            refinedOptions: this.currentCalendarOptionsRefined,\n            pluginHooks,\n            availableLocaleData,\n            localeDefaults,\n            extra,\n        };\n    }\n    _computeCurrentViewData(viewType, optionsData, optionOverrides, dynamicOptionOverrides) {\n        let viewSpec = optionsData.viewSpecs[viewType];\n        if (!viewSpec) {\n            throw new Error(`viewType \"${viewType}\" is not available. Please make sure you've loaded all neccessary plugins`);\n        }\n        let { refinedOptions, extra } = this.processRawViewOptions(viewSpec, optionsData.pluginHooks, optionsData.localeDefaults, optionOverrides, dynamicOptionOverrides);\n        warnUnknownOptions(extra);\n        let dateProfileGenerator = this.buildDateProfileGenerator({\n            dateProfileGeneratorClass: viewSpec.optionDefaults.dateProfileGeneratorClass,\n            duration: viewSpec.duration,\n            durationUnit: viewSpec.durationUnit,\n            usesMinMaxTime: viewSpec.optionDefaults.usesMinMaxTime,\n            dateEnv: optionsData.dateEnv,\n            calendarApi: this.props.calendarApi,\n            slotMinTime: refinedOptions.slotMinTime,\n            slotMaxTime: refinedOptions.slotMaxTime,\n            showNonCurrentDates: refinedOptions.showNonCurrentDates,\n            dayCount: refinedOptions.dayCount,\n            dateAlignment: refinedOptions.dateAlignment,\n            dateIncrement: refinedOptions.dateIncrement,\n            hiddenDays: refinedOptions.hiddenDays,\n            weekends: refinedOptions.weekends,\n            nowInput: refinedOptions.now,\n            validRangeInput: refinedOptions.validRange,\n            visibleRangeInput: refinedOptions.visibleRange,\n            fixedWeekCount: refinedOptions.fixedWeekCount,\n        });\n        let viewApi = this.buildViewApi(viewType, this.getCurrentData, optionsData.dateEnv);\n        return { viewSpec, options: refinedOptions, dateProfileGenerator, viewApi };\n    }\n    processRawViewOptions(viewSpec, pluginHooks, localeDefaults, optionOverrides, dynamicOptionOverrides) {\n        let raw = mergeRawOptions([\n            BASE_OPTION_DEFAULTS,\n            viewSpec.optionDefaults,\n            localeDefaults,\n            optionOverrides,\n            viewSpec.optionOverrides,\n            dynamicOptionOverrides,\n        ]);\n        let refiners = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, BASE_OPTION_REFINERS), CALENDAR_LISTENER_REFINERS), CALENDAR_OPTION_REFINERS), VIEW_OPTION_REFINERS), pluginHooks.listenerRefiners), pluginHooks.optionRefiners);\n        let refined = {};\n        let currentRaw = this.currentViewOptionsInput;\n        let currentRefined = this.currentViewOptionsRefined;\n        let anyChanges = false;\n        let extra = {};\n        for (let optionName in raw) {\n            if (raw[optionName] === currentRaw[optionName] ||\n                (COMPLEX_OPTION_COMPARATORS[optionName] &&\n                    COMPLEX_OPTION_COMPARATORS[optionName](raw[optionName], currentRaw[optionName]))) {\n                refined[optionName] = currentRefined[optionName];\n            }\n            else {\n                if (raw[optionName] === this.currentCalendarOptionsInput[optionName] ||\n                    (COMPLEX_OPTION_COMPARATORS[optionName] &&\n                        COMPLEX_OPTION_COMPARATORS[optionName](raw[optionName], this.currentCalendarOptionsInput[optionName]))) {\n                    if (optionName in this.currentCalendarOptionsRefined) { // might be an \"extra\" prop\n                        refined[optionName] = this.currentCalendarOptionsRefined[optionName];\n                    }\n                }\n                else if (refiners[optionName]) {\n                    refined[optionName] = refiners[optionName](raw[optionName]);\n                }\n                else {\n                    extra[optionName] = raw[optionName];\n                }\n                anyChanges = true;\n            }\n        }\n        if (anyChanges) {\n            this.currentViewOptionsInput = raw;\n            this.currentViewOptionsRefined = refined;\n        }\n        return {\n            rawOptions: this.currentViewOptionsInput,\n            refinedOptions: this.currentViewOptionsRefined,\n            extra,\n        };\n    }\n}\nfunction buildDateEnv$1(timeZone, explicitLocale, weekNumberCalculation, firstDay, weekText, pluginHooks, availableLocaleData, defaultSeparator) {\n    let locale = buildLocale(explicitLocale || availableLocaleData.defaultCode, availableLocaleData.map);\n    return new DateEnv({\n        calendarSystem: 'gregory',\n        timeZone,\n        namedTimeZoneImpl: pluginHooks.namedTimeZonedImpl,\n        locale,\n        weekNumberCalculation,\n        firstDay,\n        weekText,\n        cmdFormatter: pluginHooks.cmdFormatter,\n        defaultSeparator,\n    });\n}\nfunction buildTheme(options, pluginHooks) {\n    let ThemeClass = pluginHooks.themeClasses[options.themeSystem] || StandardTheme;\n    return new ThemeClass(options);\n}\nfunction buildDateProfileGenerator(props) {\n    let DateProfileGeneratorClass = props.dateProfileGeneratorClass || DateProfileGenerator;\n    return new DateProfileGeneratorClass(props);\n}\nfunction buildViewApi(type, getCurrentData, dateEnv) {\n    return new ViewImpl(type, getCurrentData, dateEnv);\n}\nfunction buildEventUiBySource(eventSources) {\n    return mapHash(eventSources, (eventSource) => eventSource.ui);\n}\nfunction buildEventUiBases(eventDefs, eventUiSingleBase, eventUiBySource) {\n    let eventUiBases = { '': eventUiSingleBase };\n    for (let defId in eventDefs) {\n        let def = eventDefs[defId];\n        if (def.sourceId && eventUiBySource[def.sourceId]) {\n            eventUiBases[defId] = eventUiBySource[def.sourceId];\n        }\n    }\n    return eventUiBases;\n}\nfunction buildViewUiProps(calendarContext) {\n    let { options } = calendarContext;\n    return {\n        eventUiSingleBase: createEventUi({\n            display: options.eventDisplay,\n            editable: options.editable,\n            startEditable: options.eventStartEditable,\n            durationEditable: options.eventDurationEditable,\n            constraint: options.eventConstraint,\n            overlap: typeof options.eventOverlap === 'boolean' ? options.eventOverlap : undefined,\n            allow: options.eventAllow,\n            backgroundColor: options.eventBackgroundColor,\n            borderColor: options.eventBorderColor,\n            textColor: options.eventTextColor,\n            color: options.eventColor,\n            // classNames: options.eventClassNames // render hook will handle this\n        }, calendarContext),\n        selectionConfig: createEventUi({\n            constraint: options.selectConstraint,\n            overlap: typeof options.selectOverlap === 'boolean' ? options.selectOverlap : undefined,\n            allow: options.selectAllow,\n        }, calendarContext),\n    };\n}\nfunction computeIsLoading(state, context) {\n    for (let isLoadingFunc of context.pluginHooks.isLoadingFuncs) {\n        if (isLoadingFunc(state)) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction parseContextBusinessHours(calendarContext) {\n    return parseBusinessHours(calendarContext.options.businessHours, calendarContext);\n}\nfunction warnUnknownOptions(options, viewName) {\n    for (let optionName in options) {\n        console.warn(`Unknown option '${optionName}'` +\n            (viewName ? ` for view '${viewName}'` : ''));\n    }\n}\n\nclass ToolbarSection extends BaseComponent {\n    render() {\n        let children = this.props.widgetGroups.map((widgetGroup) => this.renderWidgetGroup(widgetGroup));\n        return createElement('div', { className: 'fc-toolbar-chunk' }, ...children);\n    }\n    renderWidgetGroup(widgetGroup) {\n        let { props } = this;\n        let { theme } = this.context;\n        let children = [];\n        let isOnlyButtons = true;\n        for (let widget of widgetGroup) {\n            let { buttonName, buttonClick, buttonText, buttonIcon, buttonHint } = widget;\n            if (buttonName === 'title') {\n                isOnlyButtons = false;\n                children.push(createElement(\"h2\", { className: \"fc-toolbar-title\", id: props.titleId }, props.title));\n            }\n            else {\n                let isPressed = buttonName === props.activeButton;\n                let isDisabled = (!props.isTodayEnabled && buttonName === 'today') ||\n                    (!props.isPrevEnabled && buttonName === 'prev') ||\n                    (!props.isNextEnabled && buttonName === 'next');\n                let buttonClasses = [`fc-${buttonName}-button`, theme.getClass('button')];\n                if (isPressed) {\n                    buttonClasses.push(theme.getClass('buttonActive'));\n                }\n                children.push(createElement(\"button\", { type: \"button\", title: typeof buttonHint === 'function' ? buttonHint(props.navUnit) : buttonHint, disabled: isDisabled, \"aria-pressed\": isPressed, className: buttonClasses.join(' '), onClick: buttonClick }, buttonText || (buttonIcon ? createElement(\"span\", { className: buttonIcon, role: \"img\" }) : '')));\n            }\n        }\n        if (children.length > 1) {\n            let groupClassName = (isOnlyButtons && theme.getClass('buttonGroup')) || '';\n            return createElement('div', { className: groupClassName }, ...children);\n        }\n        return children[0];\n    }\n}\n\nclass Toolbar extends BaseComponent {\n    render() {\n        let { model, extraClassName } = this.props;\n        let forceLtr = false;\n        let startContent;\n        let endContent;\n        let sectionWidgets = model.sectionWidgets;\n        let centerContent = sectionWidgets.center;\n        if (sectionWidgets.left) {\n            forceLtr = true;\n            startContent = sectionWidgets.left;\n        }\n        else {\n            startContent = sectionWidgets.start;\n        }\n        if (sectionWidgets.right) {\n            forceLtr = true;\n            endContent = sectionWidgets.right;\n        }\n        else {\n            endContent = sectionWidgets.end;\n        }\n        let classNames = [\n            extraClassName || '',\n            'fc-toolbar',\n            forceLtr ? 'fc-toolbar-ltr' : '',\n        ];\n        return (createElement(\"div\", { className: classNames.join(' ') },\n            this.renderSection('start', startContent || []),\n            this.renderSection('center', centerContent || []),\n            this.renderSection('end', endContent || [])));\n    }\n    renderSection(key, widgetGroups) {\n        let { props } = this;\n        return (createElement(ToolbarSection, { key: key, widgetGroups: widgetGroups, title: props.title, navUnit: props.navUnit, activeButton: props.activeButton, isTodayEnabled: props.isTodayEnabled, isPrevEnabled: props.isPrevEnabled, isNextEnabled: props.isNextEnabled, titleId: props.titleId }));\n    }\n}\n\nclass ViewHarness extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.state = {\n            availableWidth: null,\n        };\n        this.handleEl = (el) => {\n            this.el = el;\n            setRef(this.props.elRef, el);\n            this.updateAvailableWidth();\n        };\n        this.handleResize = () => {\n            this.updateAvailableWidth();\n        };\n    }\n    render() {\n        let { props, state } = this;\n        let { aspectRatio } = props;\n        let classNames = [\n            'fc-view-harness',\n            (aspectRatio || props.liquid || props.height)\n                ? 'fc-view-harness-active' // harness controls the height\n                : 'fc-view-harness-passive', // let the view do the height\n        ];\n        let height = '';\n        let paddingBottom = '';\n        if (aspectRatio) {\n            if (state.availableWidth !== null) {\n                height = state.availableWidth / aspectRatio;\n            }\n            else {\n                // while waiting to know availableWidth, we can't set height to *zero*\n                // because will cause lots of unnecessary scrollbars within scrollgrid.\n                // BETTER: don't start rendering ANYTHING yet until we know container width\n                // NOTE: why not always use paddingBottom? Causes height oscillation (issue 5606)\n                paddingBottom = `${(1 / aspectRatio) * 100}%`;\n            }\n        }\n        else {\n            height = props.height || '';\n        }\n        return (createElement(\"div\", { \"aria-labelledby\": props.labeledById, ref: this.handleEl, className: classNames.join(' '), style: { height, paddingBottom } }, props.children));\n    }\n    componentDidMount() {\n        this.context.addResizeHandler(this.handleResize);\n    }\n    componentWillUnmount() {\n        this.context.removeResizeHandler(this.handleResize);\n    }\n    updateAvailableWidth() {\n        if (this.el && // needed. but why?\n            this.props.aspectRatio // aspectRatio is the only height setting that needs availableWidth\n        ) {\n            this.setState({ availableWidth: this.el.offsetWidth });\n        }\n    }\n}\n\n/*\nDetects when the user clicks on an event within a DateComponent\n*/\nclass EventClicking extends Interaction {\n    constructor(settings) {\n        super(settings);\n        this.handleSegClick = (ev, segEl) => {\n            let { component } = this;\n            let { context } = component;\n            let seg = getElSeg(segEl);\n            if (seg && // might be the <div> surrounding the more link\n                component.isValidSegDownEl(ev.target)) {\n                // our way to simulate a link click for elements that can't be <a> tags\n                // grab before trigger fired in case trigger trashes DOM thru rerendering\n                let hasUrlContainer = elementClosest(ev.target, '.fc-event-forced-url');\n                let url = hasUrlContainer ? hasUrlContainer.querySelector('a[href]').href : '';\n                context.emitter.trigger('eventClick', {\n                    el: segEl,\n                    event: new EventImpl(component.context, seg.eventRange.def, seg.eventRange.instance),\n                    jsEvent: ev,\n                    view: context.viewApi,\n                });\n                if (url && !ev.defaultPrevented) {\n                    window.location.href = url;\n                }\n            }\n        };\n        this.destroy = listenBySelector(settings.el, 'click', '.fc-event', // on both fg and bg events\n        this.handleSegClick);\n    }\n}\n\n/*\nTriggers events and adds/removes core classNames when the user's pointer\nenters/leaves event-elements of a component.\n*/\nclass EventHovering extends Interaction {\n    constructor(settings) {\n        super(settings);\n        // for simulating an eventMouseLeave when the event el is destroyed while mouse is over it\n        this.handleEventElRemove = (el) => {\n            if (el === this.currentSegEl) {\n                this.handleSegLeave(null, this.currentSegEl);\n            }\n        };\n        this.handleSegEnter = (ev, segEl) => {\n            if (getElSeg(segEl)) { // TODO: better way to make sure not hovering over more+ link or its wrapper\n                this.currentSegEl = segEl;\n                this.triggerEvent('eventMouseEnter', ev, segEl);\n            }\n        };\n        this.handleSegLeave = (ev, segEl) => {\n            if (this.currentSegEl) {\n                this.currentSegEl = null;\n                this.triggerEvent('eventMouseLeave', ev, segEl);\n            }\n        };\n        this.removeHoverListeners = listenToHoverBySelector(settings.el, '.fc-event', // on both fg and bg events\n        this.handleSegEnter, this.handleSegLeave);\n    }\n    destroy() {\n        this.removeHoverListeners();\n    }\n    triggerEvent(publicEvName, ev, segEl) {\n        let { component } = this;\n        let { context } = component;\n        let seg = getElSeg(segEl);\n        if (!ev || component.isValidSegDownEl(ev.target)) {\n            context.emitter.trigger(publicEvName, {\n                el: segEl,\n                event: new EventImpl(context, seg.eventRange.def, seg.eventRange.instance),\n                jsEvent: ev,\n                view: context.viewApi,\n            });\n        }\n    }\n}\n\nclass CalendarContent extends PureComponent {\n    constructor() {\n        super(...arguments);\n        this.buildViewContext = memoize(buildViewContext);\n        this.buildViewPropTransformers = memoize(buildViewPropTransformers);\n        this.buildToolbarProps = memoize(buildToolbarProps);\n        this.headerRef = createRef();\n        this.footerRef = createRef();\n        this.interactionsStore = {};\n        // eslint-disable-next-line\n        this.state = {\n            viewLabelId: getUniqueDomId(),\n        };\n        // Component Registration\n        // -----------------------------------------------------------------------------------------------------------------\n        this.registerInteractiveComponent = (component, settingsInput) => {\n            let settings = parseInteractionSettings(component, settingsInput);\n            let DEFAULT_INTERACTIONS = [\n                EventClicking,\n                EventHovering,\n            ];\n            let interactionClasses = DEFAULT_INTERACTIONS.concat(this.props.pluginHooks.componentInteractions);\n            let interactions = interactionClasses.map((TheInteractionClass) => new TheInteractionClass(settings));\n            this.interactionsStore[component.uid] = interactions;\n            interactionSettingsStore[component.uid] = settings;\n        };\n        this.unregisterInteractiveComponent = (component) => {\n            let listeners = this.interactionsStore[component.uid];\n            if (listeners) {\n                for (let listener of listeners) {\n                    listener.destroy();\n                }\n                delete this.interactionsStore[component.uid];\n            }\n            delete interactionSettingsStore[component.uid];\n        };\n        // Resizing\n        // -----------------------------------------------------------------------------------------------------------------\n        this.resizeRunner = new DelayedRunner(() => {\n            this.props.emitter.trigger('_resize', true); // should window resizes be considered \"forced\" ?\n            this.props.emitter.trigger('windowResize', { view: this.props.viewApi });\n        });\n        this.handleWindowResize = (ev) => {\n            let { options } = this.props;\n            if (options.handleWindowResize &&\n                ev.target === window // avoid jqui events\n            ) {\n                this.resizeRunner.request(options.windowResizeDelay);\n            }\n        };\n    }\n    /*\n    renders INSIDE of an outer div\n    */\n    render() {\n        let { props } = this;\n        let { toolbarConfig, options } = props;\n        let toolbarProps = this.buildToolbarProps(props.viewSpec, props.dateProfile, props.dateProfileGenerator, props.currentDate, getNow(props.options.now, props.dateEnv), // TODO: use NowTimer????\n        props.viewTitle);\n        let viewVGrow = false;\n        let viewHeight = '';\n        let viewAspectRatio;\n        if (props.isHeightAuto || props.forPrint) {\n            viewHeight = '';\n        }\n        else if (options.height != null) {\n            viewVGrow = true;\n        }\n        else if (options.contentHeight != null) {\n            viewHeight = options.contentHeight;\n        }\n        else {\n            viewAspectRatio = Math.max(options.aspectRatio, 0.5); // prevent from getting too tall\n        }\n        let viewContext = this.buildViewContext(props.viewSpec, props.viewApi, props.options, props.dateProfileGenerator, props.dateEnv, props.theme, props.pluginHooks, props.dispatch, props.getCurrentData, props.emitter, props.calendarApi, this.registerInteractiveComponent, this.unregisterInteractiveComponent);\n        let viewLabelId = (toolbarConfig.header && toolbarConfig.header.hasTitle)\n            ? this.state.viewLabelId\n            : '';\n        return (createElement(ViewContextType.Provider, { value: viewContext },\n            toolbarConfig.header && (createElement(Toolbar, Object.assign({ ref: this.headerRef, extraClassName: \"fc-header-toolbar\", model: toolbarConfig.header, titleId: viewLabelId }, toolbarProps))),\n            createElement(ViewHarness, { liquid: viewVGrow, height: viewHeight, aspectRatio: viewAspectRatio, labeledById: viewLabelId },\n                this.renderView(props),\n                this.buildAppendContent()),\n            toolbarConfig.footer && (createElement(Toolbar, Object.assign({ ref: this.footerRef, extraClassName: \"fc-footer-toolbar\", model: toolbarConfig.footer, titleId: \"\" }, toolbarProps)))));\n    }\n    componentDidMount() {\n        let { props } = this;\n        this.calendarInteractions = props.pluginHooks.calendarInteractions\n            .map((CalendarInteractionClass) => new CalendarInteractionClass(props));\n        window.addEventListener('resize', this.handleWindowResize);\n        let { propSetHandlers } = props.pluginHooks;\n        for (let propName in propSetHandlers) {\n            propSetHandlers[propName](props[propName], props);\n        }\n    }\n    componentDidUpdate(prevProps) {\n        let { props } = this;\n        let { propSetHandlers } = props.pluginHooks;\n        for (let propName in propSetHandlers) {\n            if (props[propName] !== prevProps[propName]) {\n                propSetHandlers[propName](props[propName], props);\n            }\n        }\n    }\n    componentWillUnmount() {\n        window.removeEventListener('resize', this.handleWindowResize);\n        this.resizeRunner.clear();\n        for (let interaction of this.calendarInteractions) {\n            interaction.destroy();\n        }\n        this.props.emitter.trigger('_unmount');\n    }\n    buildAppendContent() {\n        let { props } = this;\n        let children = props.pluginHooks.viewContainerAppends.map((buildAppendContent) => buildAppendContent(props));\n        return createElement(Fragment, {}, ...children);\n    }\n    renderView(props) {\n        let { pluginHooks } = props;\n        let { viewSpec } = props;\n        let viewProps = {\n            dateProfile: props.dateProfile,\n            businessHours: props.businessHours,\n            eventStore: props.renderableEventStore,\n            eventUiBases: props.eventUiBases,\n            dateSelection: props.dateSelection,\n            eventSelection: props.eventSelection,\n            eventDrag: props.eventDrag,\n            eventResize: props.eventResize,\n            isHeightAuto: props.isHeightAuto,\n            forPrint: props.forPrint,\n        };\n        let transformers = this.buildViewPropTransformers(pluginHooks.viewPropsTransformers);\n        for (let transformer of transformers) {\n            Object.assign(viewProps, transformer.transform(viewProps, props));\n        }\n        let ViewComponent = viewSpec.component;\n        return (createElement(ViewComponent, Object.assign({}, viewProps)));\n    }\n}\nfunction buildToolbarProps(viewSpec, dateProfile, dateProfileGenerator, currentDate, now, title) {\n    // don't force any date-profiles to valid date profiles (the `false`) so that we can tell if it's invalid\n    let todayInfo = dateProfileGenerator.build(now, undefined, false); // TODO: need `undefined` or else INFINITE LOOP for some reason\n    let prevInfo = dateProfileGenerator.buildPrev(dateProfile, currentDate, false);\n    let nextInfo = dateProfileGenerator.buildNext(dateProfile, currentDate, false);\n    return {\n        title,\n        activeButton: viewSpec.type,\n        navUnit: viewSpec.singleUnit,\n        isTodayEnabled: todayInfo.isValid && !rangeContainsMarker(dateProfile.currentRange, now),\n        isPrevEnabled: prevInfo.isValid,\n        isNextEnabled: nextInfo.isValid,\n    };\n}\n// Plugin\n// -----------------------------------------------------------------------------------------------------------------\nfunction buildViewPropTransformers(theClasses) {\n    return theClasses.map((TheClass) => new TheClass());\n}\n\nclass Calendar extends CalendarImpl {\n    constructor(el, optionOverrides = {}) {\n        super();\n        this.isRendering = false;\n        this.isRendered = false;\n        this.currentClassNames = [];\n        this.customContentRenderId = 0;\n        this.handleAction = (action) => {\n            // actions we know we want to render immediately\n            switch (action.type) {\n                case 'SET_EVENT_DRAG':\n                case 'SET_EVENT_RESIZE':\n                    this.renderRunner.tryDrain();\n            }\n        };\n        this.handleData = (data) => {\n            this.currentData = data;\n            this.renderRunner.request(data.calendarOptions.rerenderDelay);\n        };\n        this.handleRenderRequest = () => {\n            if (this.isRendering) {\n                this.isRendered = true;\n                let { currentData } = this;\n                flushSync(() => {\n                    render(createElement(CalendarRoot, { options: currentData.calendarOptions, theme: currentData.theme, emitter: currentData.emitter }, (classNames, height, isHeightAuto, forPrint) => {\n                        this.setClassNames(classNames);\n                        this.setHeight(height);\n                        return (createElement(RenderId.Provider, { value: this.customContentRenderId },\n                            createElement(CalendarContent, Object.assign({ isHeightAuto: isHeightAuto, forPrint: forPrint }, currentData))));\n                    }), this.el);\n                });\n            }\n            else if (this.isRendered) {\n                this.isRendered = false;\n                render(null, this.el);\n                this.setClassNames([]);\n                this.setHeight('');\n            }\n        };\n        ensureElHasStyles(el);\n        this.el = el;\n        this.renderRunner = new DelayedRunner(this.handleRenderRequest);\n        new CalendarDataManager({\n            optionOverrides,\n            calendarApi: this,\n            onAction: this.handleAction,\n            onData: this.handleData,\n        });\n    }\n    render() {\n        let wasRendering = this.isRendering;\n        if (!wasRendering) {\n            this.isRendering = true;\n        }\n        else {\n            this.customContentRenderId += 1;\n        }\n        this.renderRunner.request();\n        if (wasRendering) {\n            this.updateSize();\n        }\n    }\n    destroy() {\n        if (this.isRendering) {\n            this.isRendering = false;\n            this.renderRunner.request();\n        }\n    }\n    updateSize() {\n        flushSync(() => {\n            super.updateSize();\n        });\n    }\n    batchRendering(func) {\n        this.renderRunner.pause('batchRendering');\n        func();\n        this.renderRunner.resume('batchRendering');\n    }\n    pauseRendering() {\n        this.renderRunner.pause('pauseRendering');\n    }\n    resumeRendering() {\n        this.renderRunner.resume('pauseRendering', true);\n    }\n    resetOptions(optionOverrides, changedOptionNames) {\n        this.currentDataManager.resetOptions(optionOverrides, changedOptionNames);\n    }\n    setClassNames(classNames) {\n        if (!isArraysEqual(classNames, this.currentClassNames)) {\n            let { classList } = this.el;\n            for (let className of this.currentClassNames) {\n                classList.remove(className);\n            }\n            for (let className of classNames) {\n                classList.add(className);\n            }\n            this.currentClassNames = classNames;\n        }\n    }\n    setHeight(height) {\n        applyStyleProp(this.el, 'height', height);\n    }\n}\n\nfunction formatDate(dateInput, options = {}) {\n    let dateEnv = buildDateEnv(options);\n    let formatter = createFormatter(options);\n    let dateMeta = dateEnv.createMarkerMeta(dateInput);\n    if (!dateMeta) { // TODO: warning?\n        return '';\n    }\n    return dateEnv.format(dateMeta.marker, formatter, {\n        forcedTzo: dateMeta.forcedTzo,\n    });\n}\nfunction formatRange(startInput, endInput, options) {\n    let dateEnv = buildDateEnv(typeof options === 'object' && options ? options : {}); // pass in if non-null object\n    let formatter = createFormatter(options);\n    let startMeta = dateEnv.createMarkerMeta(startInput);\n    let endMeta = dateEnv.createMarkerMeta(endInput);\n    if (!startMeta || !endMeta) { // TODO: warning?\n        return '';\n    }\n    return dateEnv.formatRange(startMeta.marker, endMeta.marker, formatter, {\n        forcedStartTzo: startMeta.forcedTzo,\n        forcedEndTzo: endMeta.forcedTzo,\n        isEndExclusive: options.isEndExclusive,\n        defaultSeparator: BASE_OPTION_DEFAULTS.defaultRangeSeparator,\n    });\n}\n// TODO: more DRY and optimized\nfunction buildDateEnv(settings) {\n    let locale = buildLocale(settings.locale || 'en', organizeRawLocales([]).map); // TODO: don't hardcode 'en' everywhere\n    return new DateEnv(Object.assign(Object.assign({ timeZone: BASE_OPTION_DEFAULTS.timeZone, calendarSystem: 'gregory' }, settings), { locale }));\n}\n\n// HELPERS\n/*\nif nextDayThreshold is specified, slicing is done in an all-day fashion.\nyou can get nextDayThreshold from context.nextDayThreshold\n*/\nfunction sliceEvents(props, allDay) {\n    return sliceEventStore(props.eventStore, props.eventUiBases, props.dateProfile.activeRange, allDay ? props.nextDayThreshold : null).fg;\n}\n\nconst version = '6.1.10';\n\nexport { Calendar, createPlugin, formatDate, formatRange, globalLocales, globalPlugins, sliceEvents, version };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,cAAc,EAAEC,EAAE,IAAIC,SAAS,EAAEC,EAAE,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,uBAAuB,EAAEC,EAAE,IAAIC,aAAa,EAAEC,EAAE,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,cAAc,EAAEC,EAAE,IAAIC,wBAAwB,EAAEC,EAAE,IAAIC,wBAAwB,EAAEC,EAAE,IAAIC,MAAM,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,SAAS,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,QAAQ,EAAEC,EAAE,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,cAAc,EAAEC,EAAE,IAAIC,eAAe,QAAQ,sBAAsB;AAChiD,SAASC,EAAE,IAAIC,gBAAgB,QAAQ,sBAAsB;AAC7D,SAASC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,QAAQ;AACnE,OAAO,eAAe;AAEtB,MAAMC,aAAa,GAAG,EAAE;AAExB,MAAMC,qBAAqB,GAAG;EAC1BC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE;IACFC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC,CAAE;EACZ,CAAC;;EACDC,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE;IACRC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,OAAO;IACdX,IAAI,EAAE,MAAM;IACZY,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE,GAAG;EACbC,YAAY,EAAE,MAAM;EACpBC,SAAS,EAAE,OAAO;EAClBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,SAAS;EACrBC,YAAY,EAAE,MAAM;EACpBC,YAAY,EAAE;AAClB,CAAC;AACD,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1B,qBAAqB,CAAC,EAAE;EAC1E;EACA;EACA2B,WAAW,EAAE;IACTpB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,SAAS;IACfI,KAAKA,CAACN,UAAU,EAAEsB,IAAI,EAAE;MACpB,OAAQA,IAAI,KAAK,KAAK,GAChB,OAAO,GACN,QAAOtB,UAAW,EAAC;IAC9B;EACJ,CAAC;EAAEuB,QAAQ,EAAE,SAAS;EAAEC,WAAW,EAAE,UAAU;EAAEC,YAAYA,CAACC,QAAQ,EAAE;IACpE,OAAQ,QAAOA,QAAS,cAAaA,QAAQ,KAAK,CAAC,GAAG,EAAE,GAAG,GAAI,EAAC;EACpE;AAAE,CAAC,CAAC;AACR,SAASC,kBAAkBA,CAACC,kBAAkB,EAAE;EAC5C,IAAIC,WAAW,GAAGD,kBAAkB,CAACE,MAAM,GAAG,CAAC,GAAGF,kBAAkB,CAAC,CAAC,CAAC,CAACjC,IAAI,GAAG,IAAI;EACnF,IAAIoC,aAAa,GAAGtC,aAAa,CAACuC,MAAM,CAACJ,kBAAkB,CAAC;EAC5D,IAAIK,YAAY,GAAG;IACfC,EAAE,EAAEhB;EACR,CAAC;EACD,KAAK,IAAIiB,SAAS,IAAIJ,aAAa,EAAE;IACjCE,YAAY,CAACE,SAAS,CAACxC,IAAI,CAAC,GAAGwC,SAAS;EAC5C;EACA,OAAO;IACHC,GAAG,EAAEH,YAAY;IACjBJ;EACJ,CAAC;AACL;AACA,SAASQ,WAAWA,CAACC,aAAa,EAAEC,SAAS,EAAE;EAC3C,IAAI,OAAOD,aAAa,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,EAAE;IACpE,OAAOI,WAAW,CAACJ,aAAa,CAAC3C,IAAI,EAAE,CAAC2C,aAAa,CAAC3C,IAAI,CAAC,EAAE2C,aAAa,CAAC;EAC/E;EACA,OAAOK,WAAW,CAACL,aAAa,EAAEC,SAAS,CAAC;AAChD;AACA,SAASI,WAAWA,CAACC,OAAO,EAAEL,SAAS,EAAE;EACrC,IAAIM,KAAK,GAAG,EAAE,CAACb,MAAM,CAACY,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC;EACtC,IAAIE,GAAG,GAAGC,cAAc,CAACF,KAAK,EAAEN,SAAS,CAAC,IAAIrB,aAAa;EAC3D,OAAOwB,WAAW,CAACE,OAAO,EAAEC,KAAK,EAAEC,GAAG,CAAC;AAC3C;AACA,SAASC,cAAcA,CAACF,KAAK,EAAEN,SAAS,EAAE;EACtC,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkM,KAAK,CAACf,MAAM,EAAEnL,CAAC,IAAI,CAAC,EAAE;IACtC,IAAIqM,KAAK,GAAGH,KAAK,CAAClM,CAAC,CAAC,CAACsM,iBAAiB,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACnD,KAAK,IAAI/K,CAAC,GAAG6K,KAAK,CAAClB,MAAM,EAAE3J,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACtC,IAAIgL,QAAQ,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAEjL,CAAC,CAAC,CAACkL,IAAI,CAAC,GAAG,CAAC;MAC1C,IAAId,SAAS,CAACY,QAAQ,CAAC,EAAE;QACrB,OAAOZ,SAAS,CAACY,QAAQ,CAAC;MAC9B;IACJ;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAAST,WAAWA,CAACE,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACtC,IAAIQ,MAAM,GAAG9M,UAAU,CAAC,CAACkJ,qBAAqB,EAAEoD,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;EACrE,OAAOQ,MAAM,CAAC3D,IAAI,CAAC,CAAC;EACpB,IAAI;IAAEC;EAAK,CAAC,GAAG0D,MAAM;EACrB,OAAOA,MAAM,CAAC1D,IAAI;EAClB,OAAO;IACHgD,OAAO;IACPC,KAAK;IACLjD,IAAI;IACJ2D,kBAAkB,EAAE,IAAIC,IAAI,CAACC,YAAY,CAACb,OAAO,CAAC;IAClDc,OAAO,EAAEJ;EACb,CAAC;AACL;;AAEA;AACA,SAASK,YAAYA,CAACC,KAAK,EAAE;EACzB,OAAO;IACHC,EAAE,EAAEnN,IAAI,CAAC,CAAC;IACVoN,IAAI,EAAEF,KAAK,CAACE,IAAI;IAChBC,kBAAkB,EAAEH,KAAK,CAACG,kBAAkB,GAAG,IAAIC,IAAI,CAACJ,KAAK,CAACG,kBAAkB,CAAC,GAAGE,SAAS;IAC7FC,IAAI,EAAEN,KAAK,CAACM,IAAI,IAAI,EAAE;IACtBC,QAAQ,EAAEP,KAAK,CAACO,QAAQ,IAAI,EAAE;IAC9BC,cAAc,EAAER,KAAK,CAACQ,cAAc,IAAI,EAAE;IAC1CC,WAAW,EAAE,EAAE,CAACrC,MAAM,CAAC4B,KAAK,CAACS,WAAW,IAAI,EAAE,CAAC;IAC/CC,aAAa,EAAEV,KAAK,CAACU,aAAa,IAAI,CAAC,CAAC;IACxCC,oBAAoB,EAAEX,KAAK,CAACW,oBAAoB,IAAI,EAAE;IACtDC,mBAAmB,EAAEZ,KAAK,CAACY,mBAAmB,IAAI,CAAC,CAAC;IACpDC,uBAAuB,EAAEb,KAAK,CAACa,uBAAuB,IAAI,EAAE;IAC5DC,0BAA0B,EAAEd,KAAK,CAACc,0BAA0B,IAAI,EAAE;IAClEC,wBAAwB,EAAEf,KAAK,CAACe,wBAAwB,IAAI,EAAE;IAC9DC,yBAAyB,EAAEhB,KAAK,CAACgB,yBAAyB,IAAI,EAAE;IAChEC,mBAAmB,EAAEjB,KAAK,CAACiB,mBAAmB,IAAI,EAAE;IACpDC,kBAAkB,EAAElB,KAAK,CAACkB,kBAAkB,IAAI,EAAE;IAClDC,KAAK,EAAEnB,KAAK,CAACmB,KAAK,IAAI,CAAC,CAAC;IACxBC,qBAAqB,EAAEpB,KAAK,CAACoB,qBAAqB,IAAI,EAAE;IACxDC,YAAY,EAAErB,KAAK,CAACqB,YAAY,IAAI,IAAI;IACxCC,qBAAqB,EAAEtB,KAAK,CAACsB,qBAAqB,IAAI,EAAE;IACxDC,oBAAoB,EAAEvB,KAAK,CAACuB,oBAAoB,IAAI,EAAE;IACtDC,qBAAqB,EAAExB,KAAK,CAACwB,qBAAqB,IAAI,EAAE;IACxDC,qBAAqB,EAAEzB,KAAK,CAACyB,qBAAqB,IAAI,EAAE;IACxDC,oBAAoB,EAAE1B,KAAK,CAAC0B,oBAAoB,IAAI,EAAE;IACtDC,YAAY,EAAE3B,KAAK,CAAC2B,YAAY,IAAI,CAAC,CAAC;IACtCC,eAAe,EAAE5B,KAAK,CAAC4B,eAAe,IAAI,EAAE;IAC5CC,YAAY,EAAE7B,KAAK,CAAC6B,YAAY;IAChCC,cAAc,EAAE9B,KAAK,CAAC8B,cAAc,IAAI,EAAE;IAC1CC,kBAAkB,EAAE/B,KAAK,CAAC+B,kBAAkB;IAC5CC,WAAW,EAAEhC,KAAK,CAACgC,WAAW,IAAI,EAAE;IACpCC,mBAAmB,EAAEjC,KAAK,CAACiC,mBAAmB;IAC9CC,oBAAoB,EAAElC,KAAK,CAACkC,oBAAoB,IAAI,CAAC,CAAC;IACtDC,cAAc,EAAEnC,KAAK,CAACmC,cAAc,IAAI,IAAI;IAC5CC,gBAAgB,EAAEpC,KAAK,CAACoC,gBAAgB,IAAI,CAAC,CAAC;IAC9CC,cAAc,EAAErC,KAAK,CAACqC,cAAc,IAAI,CAAC,CAAC;IAC1CC,eAAe,EAAEtC,KAAK,CAACsC,eAAe,IAAI,CAAC;EAC/C,CAAC;AACL;AACA,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,UAAU,EAAE;EAC9C,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzB,IAAIC,KAAK,GAAG;IACRxC,kBAAkB,EAAEE,SAAS;IAC7BE,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,CAAC,CAAC;IACjBC,oBAAoB,EAAE,EAAE;IACxBC,mBAAmB,EAAE,CAAC,CAAC;IACvBC,uBAAuB,EAAE,EAAE;IAC3BC,0BAA0B,EAAE,EAAE;IAC9BC,wBAAwB,EAAE,EAAE;IAC5BC,yBAAyB,EAAE,EAAE;IAC7BC,mBAAmB,EAAE,EAAE;IACvBC,kBAAkB,EAAE,EAAE;IACtBC,KAAK,EAAE,CAAC,CAAC;IACTC,qBAAqB,EAAE,EAAE;IACzBC,YAAY,EAAE,IAAI;IAClBC,qBAAqB,EAAE,EAAE;IACzBC,oBAAoB,EAAE,EAAE;IACxBC,qBAAqB,EAAE,EAAE;IACzBC,qBAAqB,EAAE,EAAE;IACzBC,oBAAoB,EAAE,EAAE;IACxBC,YAAY,EAAE,CAAC,CAAC;IAChBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,EAAE;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,WAAW,EAAE,EAAE;IACfC,mBAAmB,EAAE,IAAI;IACzBC,oBAAoB,EAAE,CAAC,CAAC;IACxBC,cAAc,EAAE,IAAI;IACpBC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,cAAc,EAAE,CAAC,CAAC;IAClBC,eAAe,EAAE,CAAC;EACtB,CAAC;EACD,SAASM,OAAOA,CAACC,IAAI,EAAE;IACnB,KAAK,IAAIC,GAAG,IAAID,IAAI,EAAE;MAClB,MAAME,UAAU,GAAGD,GAAG,CAAC5C,IAAI;MAC3B,MAAM8C,SAAS,GAAGN,gBAAgB,CAACK,UAAU,CAAC;MAC9C,IAAIC,SAAS,KAAK3C,SAAS,EAAE;QACzBqC,gBAAgB,CAACK,UAAU,CAAC,GAAGD,GAAG,CAAC7C,EAAE;QACrC2C,OAAO,CAACE,GAAG,CAACxC,IAAI,CAAC;QACjBqC,KAAK,GAAGM,YAAY,CAACN,KAAK,EAAEG,GAAG,CAAC;MACpC,CAAC,MACI,IAAIE,SAAS,KAAKF,GAAG,CAAC7C,EAAE,EAAE;QAC3B;QACAiD,OAAO,CAACC,IAAI,CAAE,qBAAoBJ,UAAW,GAAE,CAAC;MACpD;IACJ;EACJ;EACA,IAAIP,UAAU,EAAE;IACZI,OAAO,CAACJ,UAAU,CAAC;EACvB;EACAI,OAAO,CAACH,UAAU,CAAC;EACnB,OAAOE,KAAK;AAChB;AACA,SAASS,qBAAqBA,CAAA,EAAG;EAC7B,IAAIC,mBAAmB,GAAG,EAAE;EAC5B,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,YAAY;EAChB,OAAO,CAACC,YAAY,EAAEf,UAAU,KAAK;IACjC,IAAI,CAACc,YAAY,IAAI,CAACvQ,aAAa,CAACwQ,YAAY,EAAEH,mBAAmB,CAAC,IAAI,CAACrQ,aAAa,CAACyP,UAAU,EAAEa,iBAAiB,CAAC,EAAE;MACrHC,YAAY,GAAGhB,gBAAgB,CAACiB,YAAY,EAAEf,UAAU,CAAC;IAC7D;IACAY,mBAAmB,GAAGG,YAAY;IAClCF,iBAAiB,GAAGb,UAAU;IAC9B,OAAOc,YAAY;EACvB,CAAC;AACL;AACA,SAASN,YAAYA,CAACQ,MAAM,EAAEC,MAAM,EAAE;EAClC,OAAO;IACHvD,kBAAkB,EAAEwD,oBAAoB,CAACF,MAAM,CAACtD,kBAAkB,EAAEuD,MAAM,CAACvD,kBAAkB,CAAC;IAC9FI,QAAQ,EAAEkD,MAAM,CAAClD,QAAQ,CAACnC,MAAM,CAACsF,MAAM,CAACnD,QAAQ,CAAC;IACjDC,cAAc,EAAEiD,MAAM,CAACjD,cAAc,CAACpC,MAAM,CAACsF,MAAM,CAAClD,cAAc,CAAC;IACnEC,WAAW,EAAEgD,MAAM,CAAChD,WAAW,CAACrC,MAAM,CAACsF,MAAM,CAACjD,WAAW,CAAC;IAC1DC,aAAa,EAAEnD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAAC/C,aAAa,CAAC,EAAEgD,MAAM,CAAChD,aAAa,CAAC;IAC3FC,oBAAoB,EAAE8C,MAAM,CAAC9C,oBAAoB,CAACvC,MAAM,CAACsF,MAAM,CAAC/C,oBAAoB,CAAC;IACrFC,mBAAmB,EAAErD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAAC7C,mBAAmB,CAAC,EAAE8C,MAAM,CAAC9C,mBAAmB,CAAC;IAC7GC,uBAAuB,EAAE4C,MAAM,CAAC5C,uBAAuB,CAACzC,MAAM,CAACsF,MAAM,CAAC7C,uBAAuB,CAAC;IAC9FC,0BAA0B,EAAE2C,MAAM,CAAC3C,0BAA0B,CAAC1C,MAAM,CAACsF,MAAM,CAAC5C,0BAA0B,CAAC;IACvGC,wBAAwB,EAAE0C,MAAM,CAAC1C,wBAAwB,CAAC3C,MAAM,CAACsF,MAAM,CAAC3C,wBAAwB,CAAC;IACjGC,yBAAyB,EAAEyC,MAAM,CAACzC,yBAAyB,CAAC5C,MAAM,CAACsF,MAAM,CAAC1C,yBAAyB,CAAC;IACpGC,mBAAmB,EAAEwC,MAAM,CAACxC,mBAAmB,CAAC7C,MAAM,CAACsF,MAAM,CAACzC,mBAAmB,CAAC;IAClFC,kBAAkB,EAAEuC,MAAM,CAACvC,kBAAkB,CAAC9C,MAAM,CAACsF,MAAM,CAACxC,kBAAkB,CAAC;IAC/EC,KAAK,EAAE5D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAACtC,KAAK,CAAC,EAAEuC,MAAM,CAACvC,KAAK,CAAC;IACnEC,qBAAqB,EAAEqC,MAAM,CAACrC,qBAAqB,CAAChD,MAAM,CAACsF,MAAM,CAACtC,qBAAqB,CAAC;IACxFC,YAAY,EAAEqC,MAAM,CAACrC,YAAY,IAAIoC,MAAM,CAACpC,YAAY;IACxDC,qBAAqB,EAAEmC,MAAM,CAACnC,qBAAqB,CAAClD,MAAM,CAACsF,MAAM,CAACpC,qBAAqB,CAAC;IACxFC,oBAAoB,EAAEkC,MAAM,CAAClC,oBAAoB,CAACnD,MAAM,CAACsF,MAAM,CAACnC,oBAAoB,CAAC;IACrFC,qBAAqB,EAAEiC,MAAM,CAACjC,qBAAqB,CAACpD,MAAM,CAACsF,MAAM,CAAClC,qBAAqB,CAAC;IACxFE,oBAAoB,EAAE+B,MAAM,CAAC/B,oBAAoB,CAACtD,MAAM,CAACsF,MAAM,CAAChC,oBAAoB,CAAC;IACrFD,qBAAqB,EAAEgC,MAAM,CAAChC,qBAAqB,CAACrD,MAAM,CAACsF,MAAM,CAACjC,qBAAqB,CAAC;IACxFE,YAAY,EAAEpE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAAC9B,YAAY,CAAC,EAAE+B,MAAM,CAAC/B,YAAY,CAAC;IACxFC,eAAe,EAAE6B,MAAM,CAAC7B,eAAe,CAACxD,MAAM,CAACsF,MAAM,CAAC9B,eAAe,CAAC;IACtEC,YAAY,EAAE6B,MAAM,CAAC7B,YAAY,IAAI4B,MAAM,CAAC5B,YAAY;IACxDC,cAAc,EAAE2B,MAAM,CAAC3B,cAAc,CAAC1D,MAAM,CAACsF,MAAM,CAAC5B,cAAc,CAAC;IACnEC,kBAAkB,EAAE2B,MAAM,CAAC3B,kBAAkB,IAAI0B,MAAM,CAAC1B,kBAAkB;IAC1EC,WAAW,EAAEyB,MAAM,CAACzB,WAAW,IAAI0B,MAAM,CAAC1B,WAAW;IACrDC,mBAAmB,EAAEwB,MAAM,CAACxB,mBAAmB,IAAIyB,MAAM,CAACzB,mBAAmB;IAC7EC,oBAAoB,EAAE3E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAACvB,oBAAoB,CAAC,EAAEwB,MAAM,CAACxB,oBAAoB,CAAC;IAChHC,cAAc,EAAEuB,MAAM,CAACvB,cAAc,IAAIsB,MAAM,CAACtB,cAAc;IAC9DC,gBAAgB,EAAE7E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAACrB,gBAAgB,CAAC,EAAEsB,MAAM,CAACtB,gBAAgB,CAAC;IACpGC,cAAc,EAAE9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAACpB,cAAc,CAAC,EAAEqB,MAAM,CAACrB,cAAc,CAAC;IAC9FC,eAAe,EAAE/E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAACnB,eAAe,CAAC,EAAEoB,MAAM,CAACpB,eAAe;EACpG,CAAC;AACL;AACA,SAASqB,oBAAoBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACxC,IAAID,KAAK,KAAKvD,SAAS,EAAE;IACrB,OAAOwD,KAAK;EAChB;EACA,IAAIA,KAAK,KAAKxD,SAAS,EAAE;IACrB,OAAOuD,KAAK;EAChB;EACA,OAAO,IAAIxD,IAAI,CAAC0D,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,EAAEH,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/D;AAEA,MAAMC,aAAa,SAAS/Q,KAAK,CAAC;AAElC+Q,aAAa,CAACC,SAAS,CAACC,OAAO,GAAG;EAC9BC,IAAI,EAAE,mBAAmB;EACzBC,eAAe,EAAE,gBAAgB;EACjCC,WAAW,EAAE,iBAAiB;EAC9BC,MAAM,EAAE,6BAA6B;EACrCC,YAAY,EAAE;AAClB,CAAC;AACDP,aAAa,CAACC,SAAS,CAACO,aAAa,GAAG,SAAS;AACjDR,aAAa,CAACC,SAAS,CAACQ,WAAW,GAAG;EAClCC,KAAK,EAAE,WAAW;EAClBtI,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,uBAAuB;EAC7BC,QAAQ,EAAE,uBAAuB;EACjCC,QAAQ,EAAE;AACd,CAAC;AACDyH,aAAa,CAACC,SAAS,CAACU,cAAc,GAAG;EACrCvI,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,sBAAsB;EAC5BC,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAE;AACd,CAAC;AACDyH,aAAa,CAACC,SAAS,CAACW,kBAAkB,GAAG,aAAa,CAAC,CAAC;AAC5DZ,aAAa,CAACC,SAAS,CAACY,8BAA8B,GAAG,MAAM;AAC/Db,aAAa,CAACC,SAAS,CAACa,kBAAkB,GAAG,UAAU;AAEvD,SAASC,eAAeA,CAACC,cAAc,EAAEC,eAAe,EAAE;EACtD,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,IAAIC,QAAQ;EACZ,KAAKA,QAAQ,IAAIH,cAAc,EAAE;IAC7BI,aAAa,CAACD,QAAQ,EAAED,IAAI,EAAEF,cAAc,EAAEC,eAAe,CAAC;EAClE;EACA,KAAKE,QAAQ,IAAIF,eAAe,EAAE;IAC9BG,aAAa,CAACD,QAAQ,EAAED,IAAI,EAAEF,cAAc,EAAEC,eAAe,CAAC;EAClE;EACA,OAAOC,IAAI;AACf;AACA,SAASE,aAAaA,CAACD,QAAQ,EAAED,IAAI,EAAEF,cAAc,EAAEC,eAAe,EAAE;EACpE,IAAIC,IAAI,CAACC,QAAQ,CAAC,EAAE;IAChB,OAAOD,IAAI,CAACC,QAAQ,CAAC;EACzB;EACA,IAAIE,OAAO,GAAGC,YAAY,CAACH,QAAQ,EAAED,IAAI,EAAEF,cAAc,EAAEC,eAAe,CAAC;EAC3E,IAAII,OAAO,EAAE;IACTH,IAAI,CAACC,QAAQ,CAAC,GAAGE,OAAO;EAC5B;EACA,OAAOA,OAAO;AAClB;AACA,SAASC,YAAYA,CAACH,QAAQ,EAAED,IAAI,EAAEF,cAAc,EAAEC,eAAe,EAAE;EACnE,IAAIM,aAAa,GAAGP,cAAc,CAACG,QAAQ,CAAC;EAC5C,IAAIK,cAAc,GAAGP,eAAe,CAACE,QAAQ,CAAC;EAC9C,IAAIM,SAAS,GAAIxF,IAAI,IAAOsF,aAAa,IAAIA,aAAa,CAACtF,IAAI,CAAC,KAAK,IAAI,GAAIsF,aAAa,CAACtF,IAAI,CAAC,GAC1FuF,cAAc,IAAIA,cAAc,CAACvF,IAAI,CAAC,KAAK,IAAI,GAAIuF,cAAc,CAACvF,IAAI,CAAC,GAAG,IAAM;EACtF,IAAIyF,YAAY,GAAGD,SAAS,CAAC,WAAW,CAAC;EACzC,IAAIE,SAAS,GAAGF,SAAS,CAAC,WAAW,CAAC;EACtC,IAAIG,QAAQ,GAAG,IAAI;EACnB,IAAID,SAAS,EAAE;IACX,IAAIA,SAAS,KAAKR,QAAQ,EAAE;MACxB,MAAM,IAAIU,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACAD,QAAQ,GAAGR,aAAa,CAACO,SAAS,EAAET,IAAI,EAAEF,cAAc,EAAEC,eAAe,CAAC;EAC9E;EACA,IAAI,CAACS,YAAY,IAAIE,QAAQ,EAAE;IAC3BF,YAAY,GAAGE,QAAQ,CAACE,SAAS;EACrC;EACA,IAAI,CAACJ,YAAY,EAAE;IACf,OAAO,IAAI,CAAC,CAAC;EACjB;;EACA,OAAO;IACHK,IAAI,EAAEZ,QAAQ;IACdW,SAAS,EAAEJ,YAAY;IACvBM,QAAQ,EAAE1I,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAGqI,QAAQ,GAAGA,QAAQ,CAACI,QAAQ,GAAG,CAAC,CAAE,CAAC,EAAGT,aAAa,GAAGA,aAAa,CAACU,UAAU,GAAG,CAAC,CAAE,CAAC;IAChIC,SAAS,EAAE5I,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAGqI,QAAQ,GAAGA,QAAQ,CAACM,SAAS,GAAG,CAAC,CAAE,CAAC,EAAGV,cAAc,GAAGA,cAAc,CAACS,UAAU,GAAG,CAAC,CAAE;EACvI,CAAC;AACL;AAEA,SAASE,gBAAgBA,CAACC,MAAM,EAAE;EAC9B,OAAOjT,OAAO,CAACiT,MAAM,EAAEC,eAAe,CAAC;AAC3C;AACA,SAASA,eAAeA,CAACtG,KAAK,EAAE;EAC5B,IAAIkG,UAAU,GAAG,OAAOlG,KAAK,KAAK,UAAU,GACxC;IAAE+F,SAAS,EAAE/F;EAAM,CAAC,GACpBA,KAAK;EACT,IAAI;IAAE+F;EAAU,CAAC,GAAGG,UAAU;EAC9B,IAAIA,UAAU,CAACK,OAAO,EAAE;IACpB;IACAR,SAAS,GAAGS,uBAAuB,CAACN,UAAU,CAAC;EACnD,CAAC,MACI,IAAIH,SAAS,IAAI,EAAEA,SAAS,CAAC7B,SAAS,YAAY5Q,aAAa,CAAC,EAAE;IACnE;IACA;IACAyS,SAAS,GAAGS,uBAAuB,CAACjJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0I,UAAU,CAAC,EAAE;MAAEK,OAAO,EAAER;IAAU,CAAC,CAAC,CAAC;EAC7G;EACA,OAAO;IACHH,SAAS,EAAEM,UAAU,CAACF,IAAI;IAC1BD,SAAS,EAAEA,SAAS;IACpBG,UAAU,CAAE;EAChB,CAAC;AACL;;AACA,SAASM,uBAAuBA,CAAC1G,OAAO,EAAE;EACtC,OAAQ2G,SAAS,IAAMhL,aAAa,CAACjI,eAAe,CAACkT,QAAQ,EAAE,IAAI,EAAGC,OAAO,IAAMlL,aAAa,CAAC/H,gBAAgB,EAAE;IAAEkT,KAAK,EAAE,KAAK;IAAEC,SAAS,EAAEjT,mBAAmB,CAAC+S,OAAO,CAACG,QAAQ,CAAC;IAAEC,WAAW,EAAExJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiJ,SAAS,CAAC,EAAE;MAAEO,gBAAgB,EAAEL,OAAO,CAAC7G,OAAO,CAACkH;IAAiB,CAAC,CAAC;IAAEC,aAAa,EAAE5G,SAAS;IAAE6G,eAAe,EAAEpH,OAAO,CAACyG,OAAO;IAAEY,kBAAkB,EAAErH,OAAO,CAACsH,UAAU;IAAEC,QAAQ,EAAEvH,OAAO,CAACuH,QAAQ;IAAEC,WAAW,EAAExH,OAAO,CAACwH;EAAY,CAAC,CAAE,CAAE;AACjd;AAEA,SAASC,cAAcA,CAACC,aAAa,EAAEC,eAAe,EAAEC,sBAAsB,EAAEC,cAAc,EAAE;EAC5F,IAAI1C,cAAc,GAAGmB,gBAAgB,CAACoB,aAAa,CAAC;EACpD,IAAItC,eAAe,GAAGkB,gBAAgB,CAACqB,eAAe,CAACtG,KAAK,CAAC;EAC7D,IAAIyG,QAAQ,GAAG5C,eAAe,CAACC,cAAc,EAAEC,eAAe,CAAC;EAC/D,OAAO9R,OAAO,CAACwU,QAAQ,EAAGtC,OAAO,IAAKuC,aAAa,CAACvC,OAAO,EAAEJ,eAAe,EAAEuC,eAAe,EAAEC,sBAAsB,EAAEC,cAAc,CAAC,CAAC;AAC3I;AACA,SAASE,aAAaA,CAACvC,OAAO,EAAEJ,eAAe,EAAEuC,eAAe,EAAEC,sBAAsB,EAAEC,cAAc,EAAE;EACtG,IAAIG,aAAa,GAAGxC,OAAO,CAACa,SAAS,CAAC4B,QAAQ,IAC1CzC,OAAO,CAACW,QAAQ,CAAC8B,QAAQ,IACzBL,sBAAsB,CAACK,QAAQ,IAC/BN,eAAe,CAACM,QAAQ;EAC5B,IAAIA,QAAQ,GAAG,IAAI;EACnB,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,mBAAmB,GAAG,CAAC,CAAC;EAC5B,IAAIJ,aAAa,EAAE;IACfC,QAAQ,GAAGI,oBAAoB,CAACL,aAAa,CAAC;IAC9C,IAAIC,QAAQ,EAAE;MAAE;MACZ,IAAIK,KAAK,GAAGtU,2BAA2B,CAACiU,QAAQ,CAAC;MACjDC,YAAY,GAAGI,KAAK,CAAC1K,IAAI;MACzB,IAAI0K,KAAK,CAACC,KAAK,KAAK,CAAC,EAAE;QACnBJ,UAAU,GAAGD,YAAY;QACzBE,mBAAmB,GAAGhD,eAAe,CAAC8C,YAAY,CAAC,GAAG9C,eAAe,CAAC8C,YAAY,CAAC,CAAC9B,UAAU,GAAG,CAAC,CAAC;MACvG;IACJ;EACJ;EACA,IAAIoC,eAAe,GAAIC,aAAa,IAAK;IACrC,IAAIC,aAAa,GAAGD,aAAa,CAACnM,UAAU,IAAI,CAAC,CAAC;IAClD,IAAIqM,aAAa,GAAGnD,OAAO,CAACW,QAAQ,CAACwC,aAAa;IAClD,IAAIA,aAAa,IAAI,IAAI,IAAID,aAAa,CAACC,aAAa,CAAC,IAAI,IAAI,EAAE;MAC/D,OAAOD,aAAa,CAACC,aAAa,CAAC;IACvC;IACA,IAAID,aAAa,CAAClD,OAAO,CAACU,IAAI,CAAC,IAAI,IAAI,EAAE;MACrC,OAAOwC,aAAa,CAAClD,OAAO,CAACU,IAAI,CAAC;IACtC;IACA,IAAIwC,aAAa,CAACP,UAAU,CAAC,IAAI,IAAI,EAAE;MACnC,OAAOO,aAAa,CAACP,UAAU,CAAC;IACpC;IACA,OAAO,IAAI;EACf,CAAC;EACD,IAAIS,gBAAgB,GAAIH,aAAa,IAAK;IACtC,IAAI9K,WAAW,GAAG8K,aAAa,CAAC9K,WAAW,IAAI,CAAC,CAAC;IACjD,IAAIkL,SAAS,GAAGrD,OAAO,CAACW,QAAQ,CAACwC,aAAa,CAAC,CAAC;IAChD,IAAIE,SAAS,IAAI,IAAI,IAAIlL,WAAW,CAACkL,SAAS,CAAC,IAAI,IAAI,EAAE;MACrD,OAAOlL,WAAW,CAACkL,SAAS,CAAC;IACjC;IACA,IAAIlL,WAAW,CAAC6H,OAAO,CAACU,IAAI,CAAC,IAAI,IAAI,EAAE;MACnC,OAAOvI,WAAW,CAAC6H,OAAO,CAACU,IAAI,CAAC;IACpC;IACA,IAAIvI,WAAW,CAACwK,UAAU,CAAC,IAAI,IAAI,EAAE;MACjC,OAAOxK,WAAW,CAACwK,UAAU,CAAC;IAClC;IACA,OAAO,IAAI;EACf,CAAC;EACD,OAAO;IACHjC,IAAI,EAAEV,OAAO,CAACU,IAAI;IAClBD,SAAS,EAAET,OAAO,CAACS,SAAS;IAC5BgC,QAAQ;IACRC,YAAY;IACZC,UAAU;IACVW,cAAc,EAAEtD,OAAO,CAACW,QAAQ;IAChCwB,eAAe,EAAElK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0K,mBAAmB,CAAC,EAAE5C,OAAO,CAACa,SAAS,CAAC;IACzF0C,kBAAkB,EAAEP,eAAe,CAACZ,sBAAsB,CAAC,IACvDY,eAAe,CAACb,eAAe,CAAC;IAAI;IACpCnC,OAAO,CAACa,SAAS,CAAC/J,UAAU;IAChC0M,iBAAiB,EAAER,eAAe,CAACX,cAAc,CAAC,IAC9CrC,OAAO,CAACW,QAAQ,CAAC7J,UAAU,IAC3BkM,eAAe,CAACpU,oBAAoB,CAAC,IACrCoR,OAAO,CAACU,IAAI;IAChB;IACA+C,mBAAmB,EAAEL,gBAAgB,CAAChB,sBAAsB,CAAC,IACzDgB,gBAAgB,CAACjB,eAAe,CAAC,IACjCnC,OAAO,CAACa,SAAS,CAAC6C,UAAU;IAChCC,kBAAkB,EAAEP,gBAAgB,CAACf,cAAc,CAAC,IAChDrC,OAAO,CAACW,QAAQ,CAAC+C,UAAU,IAC3BN,gBAAgB,CAACxU,oBAAoB;IACzC;EACJ,CAAC;AACL;AACA;AACA,IAAIgV,gBAAgB,GAAG,CAAC,CAAC;AACzB,SAASf,oBAAoBA,CAACL,aAAa,EAAE;EACzC,IAAIqB,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACvB,aAAa,CAAC;EACxC,IAAIwB,GAAG,GAAGJ,gBAAgB,CAACC,IAAI,CAAC;EAChC,IAAIG,GAAG,KAAKjJ,SAAS,EAAE;IACnBiJ,GAAG,GAAGtV,cAAc,CAAC8T,aAAa,CAAC;IACnCoB,gBAAgB,CAACC,IAAI,CAAC,GAAGG,GAAG;EAChC;EACA,OAAOA,GAAG;AACd;AAEA,SAASC,cAAcA,CAACnE,QAAQ,EAAEoE,MAAM,EAAE;EACtC,QAAQA,MAAM,CAACxD,IAAI;IACf,KAAK,kBAAkB;MACnBZ,QAAQ,GAAGoE,MAAM,CAACpE,QAAQ;EAClC;EACA,OAAOA,QAAQ;AACnB;AAEA,SAASqE,4BAA4BA,CAAC/B,sBAAsB,EAAE8B,MAAM,EAAE;EAClE,QAAQA,MAAM,CAACxD,IAAI;IACf,KAAK,YAAY;MACb,OAAOzI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEkK,sBAAsB,CAAC,EAAE;QAAE,CAAC8B,MAAM,CAACE,UAAU,GAAGF,MAAM,CAACG;MAAe,CAAC,CAAC;IACnH;MACI,OAAOjC,sBAAsB;EACrC;AACJ;AAEA,SAASkC,iBAAiBA,CAACC,kBAAkB,EAAEL,MAAM,EAAEM,WAAW,EAAEC,oBAAoB,EAAE;EACtF,IAAIC,EAAE;EACN,QAAQR,MAAM,CAACxD,IAAI;IACf,KAAK,kBAAkB;MACnB,OAAO+D,oBAAoB,CAACE,KAAK,CAACT,MAAM,CAACU,UAAU,IAAIJ,WAAW,CAAC;IACvE,KAAK,aAAa;MACd,OAAOC,oBAAoB,CAACE,KAAK,CAACT,MAAM,CAACU,UAAU,CAAC;IACxD,KAAK,MAAM;MACPF,EAAE,GAAGD,oBAAoB,CAACI,SAAS,CAACN,kBAAkB,EAAEC,WAAW,CAAC;MACpE,IAAIE,EAAE,CAACI,OAAO,EAAE;QACZ,OAAOJ,EAAE;MACb;MACA;IACJ,KAAK,MAAM;MACPA,EAAE,GAAGD,oBAAoB,CAACM,SAAS,CAACR,kBAAkB,EAAEC,WAAW,CAAC;MACpE,IAAIE,EAAE,CAACI,OAAO,EAAE;QACZ,OAAOJ,EAAE;MACb;MACA;EACR;EACA,OAAOH,kBAAkB;AAC7B;AAEA,SAASS,gBAAgBA,CAACC,eAAe,EAAEC,WAAW,EAAE7D,OAAO,EAAE;EAC7D,IAAI8D,WAAW,GAAGD,WAAW,GAAGA,WAAW,CAACC,WAAW,GAAG,IAAI;EAC9D,OAAOC,UAAU,CAAC,CAAC,CAAC,EAAEC,mBAAmB,CAACJ,eAAe,EAAE5D,OAAO,CAAC,EAAE8D,WAAW,EAAE9D,OAAO,CAAC;AAC9F;AACA,SAASiE,kBAAkBA,CAACC,YAAY,EAAErB,MAAM,EAAEgB,WAAW,EAAE7D,OAAO,EAAE;EACpE,IAAI8D,WAAW,GAAGD,WAAW,GAAGA,WAAW,CAACC,WAAW,GAAG,IAAI,CAAC,CAAC;EAChE,QAAQjB,MAAM,CAACxD,IAAI;IACf,KAAK,mBAAmB;MAAE;MACtB,OAAO0E,UAAU,CAACG,YAAY,EAAErB,MAAM,CAACsB,OAAO,EAAEL,WAAW,EAAE9D,OAAO,CAAC;IACzE,KAAK,qBAAqB;MACtB,OAAOoE,YAAY,CAACF,YAAY,EAAErB,MAAM,CAACwB,QAAQ,CAAC;IACtD,KAAK,MAAM,CAAC,CAAC;IACb,KAAK,MAAM;IACX,KAAK,aAAa;IAClB,KAAK,kBAAkB;MACnB,IAAIR,WAAW,EAAE;QACb,OAAOS,iBAAiB,CAACJ,YAAY,EAAEJ,WAAW,EAAE9D,OAAO,CAAC;MAChE;MACA,OAAOkE,YAAY;IACvB,KAAK,qBAAqB;MACtB,OAAOK,iBAAiB,CAACL,YAAY,EAAErB,MAAM,CAAC2B,SAAS;MAAG;MACtD/W,WAAW,CAACoV,MAAM,CAAC2B,SAAS,CAAC,GAC7BC,oBAAoB,CAACP,YAAY,EAAElE,OAAO,CAAC,EAAE8D,WAAW,EAAEjB,MAAM,CAAC6B,SAAS,IAAI,KAAK,EAAE1E,OAAO,CAAC;IACrG,KAAK,gBAAgB;IACrB,KAAK,qBAAqB;MACtB,OAAO2E,eAAe,CAACT,YAAY,EAAErB,MAAM,CAACwB,QAAQ,EAAExB,MAAM,CAAC+B,OAAO,EAAE/B,MAAM,CAACgC,UAAU,CAAC;IAC5F,KAAK,0BAA0B;MAC3B,OAAO,CAAC,CAAC;IACb;MACI,OAAOX,YAAY;EAC3B;AACJ;AACA,SAASY,6BAA6BA,CAACZ,YAAY,EAAEL,WAAW,EAAE7D,OAAO,EAAE;EACvE,IAAI8D,WAAW,GAAGD,WAAW,GAAGA,WAAW,CAACC,WAAW,GAAG,IAAI,CAAC,CAAC;EAChE,OAAOS,iBAAiB,CAACL,YAAY,EAAEO,oBAAoB,CAACP,YAAY,EAAElE,OAAO,CAAC,EAAE8D,WAAW,EAAE,IAAI,EAAE9D,OAAO,CAAC;AACnH;AACA,SAAS+E,0BAA0BA,CAACb,YAAY,EAAE;EAC9C,KAAK,IAAIG,QAAQ,IAAIH,YAAY,EAAE;IAC/B,IAAIA,YAAY,CAACG,QAAQ,CAAC,CAACW,UAAU,EAAE;MACnC,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASjB,UAAUA,CAACkB,eAAe,EAAEd,OAAO,EAAEU,UAAU,EAAE7E,OAAO,EAAE;EAC/D,IAAIxB,IAAI,GAAG,CAAC,CAAC;EACb,KAAK,IAAI0G,MAAM,IAAIf,OAAO,EAAE;IACxB3F,IAAI,CAAC0G,MAAM,CAACb,QAAQ,CAAC,GAAGa,MAAM;EAClC;EACA,IAAIL,UAAU,EAAE;IACZrG,IAAI,GAAG8F,iBAAiB,CAAC9F,IAAI,EAAEqG,UAAU,EAAE7E,OAAO,CAAC;EACvD;EACA,OAAOpJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoO,eAAe,CAAC,EAAEzG,IAAI,CAAC;AAClE;AACA,SAAS4F,YAAYA,CAACa,eAAe,EAAEZ,QAAQ,EAAE;EAC7C,OAAO1W,UAAU,CAACsX,eAAe,EAAGE,WAAW,IAAKA,WAAW,CAACd,QAAQ,KAAKA,QAAQ,CAAC;AAC1F;AACA,SAASC,iBAAiBA,CAACc,UAAU,EAAEP,UAAU,EAAE7E,OAAO,EAAE;EACxD,OAAOuE,iBAAiB,CAACa,UAAU,EAAEzX,UAAU,CAACyX,UAAU,EAAGD,WAAW,IAAKE,aAAa,CAACF,WAAW,EAAEN,UAAU,EAAE7E,OAAO,CAAC,CAAC,EAAE6E,UAAU,EAAE,KAAK,EAAE7E,OAAO,CAAC;AAC9J;AACA,SAASqF,aAAaA,CAACF,WAAW,EAAEN,UAAU,EAAE7E,OAAO,EAAE;EACrD,IAAI,CAACsF,mBAAmB,CAACH,WAAW,EAAEnF,OAAO,CAAC,EAAE;IAC5C,OAAO,CAACmF,WAAW,CAACI,aAAa;EACrC;EACA,OAAO,CAACvF,OAAO,CAAC7G,OAAO,CAACqM,YAAY,IAChC,CAACL,WAAW,CAACN,UAAU,IACvBM,WAAW,CAACH,UAAU;EAAI;EAC1BH,UAAU,CAACY,KAAK,GAAGN,WAAW,CAACN,UAAU,CAACY,KAAK,IAC/CZ,UAAU,CAACa,GAAG,GAAGP,WAAW,CAACN,UAAU,CAACa,GAAG;AACnD;AACA,SAASnB,iBAAiBA,CAACoB,WAAW,EAAEC,YAAY,EAAEf,UAAU,EAAEH,SAAS,EAAE1E,OAAO,EAAE;EAClF,IAAI6F,WAAW,GAAG,CAAC,CAAC;EACpB,KAAK,IAAIxB,QAAQ,IAAIsB,WAAW,EAAE;IAC9B,IAAIT,MAAM,GAAGS,WAAW,CAACtB,QAAQ,CAAC;IAClC,IAAIuB,YAAY,CAACvB,QAAQ,CAAC,EAAE;MACxBwB,WAAW,CAACxB,QAAQ,CAAC,GAAGyB,WAAW,CAACZ,MAAM,EAAEL,UAAU,EAAEH,SAAS,EAAE1E,OAAO,CAAC;IAC/E,CAAC,MACI;MACD6F,WAAW,CAACxB,QAAQ,CAAC,GAAGa,MAAM;IAClC;EACJ;EACA,OAAOW,WAAW;AACtB;AACA,SAASC,WAAWA,CAACX,WAAW,EAAEN,UAAU,EAAEH,SAAS,EAAE1E,OAAO,EAAE;EAC9D,IAAI;IAAE7G,OAAO;IAAE4M;EAAY,CAAC,GAAG/F,OAAO;EACtC,IAAIgG,SAAS,GAAGhG,OAAO,CAACiG,WAAW,CAAChL,eAAe,CAACkK,WAAW,CAACe,WAAW,CAAC;EAC5E,IAAItB,OAAO,GAAGzY,IAAI,CAAC,CAAC;EACpB6Z,SAAS,CAACG,KAAK,CAAC;IACZhB,WAAW;IACXiB,KAAK,EAAEvB,UAAU;IACjBH,SAAS;IACT1E;EACJ,CAAC,EAAG2C,GAAG,IAAK;IACR,IAAI;MAAE0D;IAAU,CAAC,GAAG1D,GAAG;IACvB,IAAIxJ,OAAO,CAACmN,kBAAkB,EAAE;MAC5BD,SAAS,GAAGlN,OAAO,CAACmN,kBAAkB,CAACC,IAAI,CAACR,WAAW,EAAEM,SAAS,EAAE1D,GAAG,CAAC6D,QAAQ,CAAC,IAAIH,SAAS;IAClG;IACA,IAAIlB,WAAW,CAACsB,OAAO,EAAE;MACrBJ,SAAS,GAAGlB,WAAW,CAACsB,OAAO,CAACF,IAAI,CAACR,WAAW,EAAEM,SAAS,EAAE1D,GAAG,CAAC6D,QAAQ,CAAC,IAAIH,SAAS;IAC3F;IACArG,OAAO,CAAC0G,QAAQ,CAAC;MACbrH,IAAI,EAAE,gBAAgB;MACtBgF,QAAQ,EAAEc,WAAW,CAACd,QAAQ;MAC9BO,OAAO;MACPC,UAAU;MACVwB;IACJ,CAAC,CAAC;EACN,CAAC,EAAGM,KAAK,IAAK;IACV,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIzN,OAAO,CAAC0N,kBAAkB,EAAE;MAC5B1N,OAAO,CAAC0N,kBAAkB,CAACN,IAAI,CAACR,WAAW,EAAEY,KAAK,CAAC;MACnDC,YAAY,GAAG,IAAI;IACvB;IACA,IAAIzB,WAAW,CAAC2B,OAAO,EAAE;MACrB3B,WAAW,CAAC2B,OAAO,CAACH,KAAK,CAAC;MAC1BC,YAAY,GAAG,IAAI;IACvB;IACA,IAAI,CAACA,YAAY,EAAE;MACfrK,OAAO,CAACC,IAAI,CAACmK,KAAK,CAACI,OAAO,EAAEJ,KAAK,CAAC;IACtC;IACA3G,OAAO,CAAC0G,QAAQ,CAAC;MACbrH,IAAI,EAAE,qBAAqB;MAC3BgF,QAAQ,EAAEc,WAAW,CAACd,QAAQ;MAC9BO,OAAO;MACPC,UAAU;MACV8B;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAO/P,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsO,WAAW,CAAC,EAAE;IAAEH,UAAU,EAAE,IAAI;IAAEO,aAAa,EAAEX;EAAQ,CAAC,CAAC;AACtG;AACA,SAASD,eAAeA,CAACS,UAAU,EAAEf,QAAQ,EAAEO,OAAO,EAAEC,UAAU,EAAE;EAChE,IAAIM,WAAW,GAAGC,UAAU,CAACf,QAAQ,CAAC;EACtC,IAAIc,WAAW;EAAI;EACfP,OAAO,KAAKO,WAAW,CAACI,aAAa,EAAE;IACvC,OAAO3O,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEuO,UAAU,CAAC,EAAE;MAAE,CAACf,QAAQ,GAAGzN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsO,WAAW,CAAC,EAAE;QAAEH,UAAU,EAAE,KAAK;QAAEH;MAAW,CAAC;IAAE,CAAC,CAAC;EACzJ;EACA,OAAOO,UAAU;AACrB;AACA,SAASX,oBAAoBA,CAACP,YAAY,EAAElE,OAAO,EAAE;EACjD,OAAOrS,UAAU,CAACuW,YAAY,EAAGiB,WAAW,IAAKG,mBAAmB,CAACH,WAAW,EAAEnF,OAAO,CAAC,CAAC;AAC/F;AACA,SAASgE,mBAAmBA,CAACzE,UAAU,EAAES,OAAO,EAAE;EAC9C,IAAIgH,QAAQ,GAAGnZ,wBAAwB,CAACmS,OAAO,CAAC;EAChD,IAAIiH,UAAU,GAAG,EAAE,CAACxP,MAAM,CAAC8H,UAAU,CAAC2E,YAAY,IAAI,EAAE,CAAC;EACzD,IAAIC,OAAO,GAAG,EAAE,CAAC,CAAC;EAClB,IAAI5E,UAAU,CAAC2H,aAAa,EAAE;IAC1BD,UAAU,CAACE,OAAO,CAAC5H,UAAU,CAAC2H,aAAa,CAAC;EAChD;EACA,IAAI3H,UAAU,CAAC6H,MAAM,EAAE;IACnBH,UAAU,CAACE,OAAO,CAAC5H,UAAU,CAAC6H,MAAM,CAAC;EACzC;EACA,KAAK,IAAIC,SAAS,IAAIJ,UAAU,EAAE;IAC9B,IAAI/B,MAAM,GAAGnX,gBAAgB,CAACsZ,SAAS,EAAErH,OAAO,EAAEgH,QAAQ,CAAC;IAC3D,IAAI9B,MAAM,EAAE;MACRf,OAAO,CAACmD,IAAI,CAACpC,MAAM,CAAC;IACxB;EACJ;EACA,OAAOf,OAAO;AAClB;AACA,SAASmB,mBAAmBA,CAACH,WAAW,EAAEnF,OAAO,EAAE;EAC/C,IAAI9D,IAAI,GAAG8D,OAAO,CAACiG,WAAW,CAAChL,eAAe;EAC9C,OAAO,CAACiB,IAAI,CAACiJ,WAAW,CAACe,WAAW,CAAC,CAACqB,WAAW;AACrD;AAEA,SAASC,mBAAmBA,CAACC,gBAAgB,EAAE5E,MAAM,EAAE;EACnD,QAAQA,MAAM,CAACxD,IAAI;IACf,KAAK,gBAAgB;MACjB,OAAO,IAAI;IACf,KAAK,cAAc;MACf,OAAOwD,MAAM,CAAC6E,SAAS;IAC3B;MACI,OAAOD,gBAAgB;EAC/B;AACJ;AAEA,SAASE,mBAAmBA,CAACC,iBAAiB,EAAE/E,MAAM,EAAE;EACpD,QAAQA,MAAM,CAACxD,IAAI;IACf,KAAK,gBAAgB;MACjB,OAAO,EAAE;IACb,KAAK,cAAc;MACf,OAAOwD,MAAM,CAACgF,eAAe;IACjC;MACI,OAAOD,iBAAiB;EAChC;AACJ;AAEA,SAASE,eAAeA,CAACC,WAAW,EAAElF,MAAM,EAAE;EAC1C,IAAImF,OAAO;EACX,QAAQnF,MAAM,CAACxD,IAAI;IACf,KAAK,kBAAkB;MACnB,OAAO,IAAI;IACf,KAAK,gBAAgB;MACjB2I,OAAO,GAAGnF,MAAM,CAACoF,KAAK;MACtB,OAAO;QACHC,cAAc,EAAEF,OAAO,CAACE,cAAc;QACtCC,aAAa,EAAEH,OAAO,CAACG,aAAa;QACpCC,OAAO,EAAEJ,OAAO,CAACI;MACrB,CAAC;IACL;MACI,OAAOL,WAAW;EAC1B;AACJ;AAEA,SAASM,iBAAiBA,CAACC,aAAa,EAAEzF,MAAM,EAAE;EAC9C,IAAI0F,SAAS;EACb,QAAQ1F,MAAM,CAACxD,IAAI;IACf,KAAK,oBAAoB;MACrB,OAAO,IAAI;IACf,KAAK,kBAAkB;MACnBkJ,SAAS,GAAG1F,MAAM,CAACoF,KAAK;MACxB,OAAO;QACHC,cAAc,EAAEK,SAAS,CAACL,cAAc;QACxCC,aAAa,EAAEI,SAAS,CAACJ,aAAa;QACtCC,OAAO,EAAEG,SAAS,CAACH;MACvB,CAAC;IACL;MACI,OAAOE,aAAa;EAC5B;AACJ;AAEA,SAASE,aAAaA,CAAC5E,eAAe,EAAE6E,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAE5C,WAAW,EAAE;EAC5F,IAAI6C,MAAM,GAAGhF,eAAe,CAACiF,aAAa,GAAGC,YAAY,CAAClF,eAAe,CAACiF,aAAa,EAAEjF,eAAe,EAAE6E,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAE5C,WAAW,CAAC,GAAG,IAAI;EACxK,IAAIgD,MAAM,GAAGnF,eAAe,CAACoF,aAAa,GAAGF,YAAY,CAAClF,eAAe,CAACoF,aAAa,EAAEpF,eAAe,EAAE6E,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAE5C,WAAW,CAAC,GAAG,IAAI;EACxK,OAAO;IAAE6C,MAAM;IAAEG;EAAO,CAAC;AAC7B;AACA,SAASD,YAAYA,CAACG,cAAc,EAAErF,eAAe,EAAE6E,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAE5C,WAAW,EAAE;EAC3G,IAAImD,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,QAAQ,GAAG,KAAK;EACpB,KAAK,IAAIC,WAAW,IAAIJ,cAAc,EAAE;IACpC,IAAIK,UAAU,GAAGL,cAAc,CAACI,WAAW,CAAC;IAC5C,IAAIE,UAAU,GAAGC,YAAY,CAACF,UAAU,EAAE1F,eAAe,EAAE6E,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAE5C,WAAW,CAAC;IAClHmD,cAAc,CAACG,WAAW,CAAC,GAAGE,UAAU,CAACE,OAAO;IAChDN,gBAAgB,CAAC7B,IAAI,CAAC,GAAGiC,UAAU,CAACJ,gBAAgB,CAAC;IACrDC,QAAQ,GAAGA,QAAQ,IAAIG,UAAU,CAACH,QAAQ;EAC9C;EACA,OAAO;IAAEF,cAAc;IAAEC,gBAAgB;IAAEC;EAAS,CAAC;AACzD;AACA;AACA;AACA;AACA,SAASI,YAAYA,CAACF,UAAU,EAAE1F,eAAe;AAAE;AACnD6E,uBAAuB;AAAE;AACzBC,KAAK,EAAEC,SAAS,EAAE5C,WAAW,EAAE;EAC3B,IAAI2D,KAAK,GAAG9F,eAAe,CAACpO,SAAS,KAAK,KAAK;EAC/C,IAAImU,qBAAqB,GAAG/F,eAAe,CAACgG,aAAa,IAAI,CAAC,CAAC;EAC/D,IAAIC,2BAA2B,GAAGpB,uBAAuB,CAAChT,UAAU,IAAI,CAAC,CAAC;EAC1E,IAAIqU,kBAAkB,GAAGlG,eAAe,CAACnO,UAAU,IAAI,CAAC,CAAC;EACzD,IAAIsU,2BAA2B,GAAGtB,uBAAuB,CAAC3R,WAAW,IAAI,CAAC,CAAC;EAC3E,IAAIkT,mBAAmB,GAAGpG,eAAe,CAAC9M,WAAW,IAAI,CAAC,CAAC;EAC3D,IAAImT,cAAc,GAAGX,UAAU,GAAGA,UAAU,CAAC3Q,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EAC5D,IAAIwQ,gBAAgB,GAAG,EAAE;EACzB,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIK,OAAO,GAAGQ,cAAc,CAACpS,GAAG,CAAEqS,cAAc,IAAMA,cAAc,CAACvR,KAAK,CAAC,GAAG,CAAC,CAACd,GAAG,CAAEsS,UAAU,IAAK;IAChG,IAAIA,UAAU,KAAK,OAAO,EAAE;MACxBf,QAAQ,GAAG,IAAI;MACf,OAAO;QAAEe;MAAW,CAAC;IACzB;IACA,IAAIC,iBAAiB;IACrB,IAAIjK,QAAQ;IACZ,IAAIkK,WAAW;IACf,IAAIC,UAAU,CAAC,CAAC;IAChB,IAAI7U,UAAU,CAAC,CAAC;IAChB,IAAI4M,UAAU;IACd;IACA,IAAK+H,iBAAiB,GAAGT,qBAAqB,CAACQ,UAAU,CAAC,EAAG;MACzDE,WAAW,GAAIE,EAAE,IAAK;QAClB,IAAIH,iBAAiB,CAACI,KAAK,EAAE;UACzBJ,iBAAiB,CAACI,KAAK,CAACjE,IAAI,CAACgE,EAAE,CAACE,MAAM,EAAEF,EAAE,EAAEA,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;QAC5D;MACJ,CAAC;;MACD,CAACH,UAAU,GAAG5B,KAAK,CAACgC,wBAAwB,CAACN,iBAAiB,CAAC,MAC1DE,UAAU,GAAG5B,KAAK,CAACiC,YAAY,CAACR,UAAU,EAAET,KAAK,CAAC,CAAC,KACnDjU,UAAU,GAAG2U,iBAAiB,CAACQ,IAAI,CAAC;MACzCvI,UAAU,GAAG+H,iBAAiB,CAACS,IAAI,IAAIT,iBAAiB,CAACQ,IAAI;IACjE,CAAC,MACI,IAAKzK,QAAQ,GAAGwI,SAAS,CAACwB,UAAU,CAAC,EAAG;MACzChB,gBAAgB,CAAC7B,IAAI,CAAC6C,UAAU,CAAC;MACjCE,WAAW,GAAGA,CAAA,KAAM;QAChBtE,WAAW,CAAC+E,UAAU,CAACX,UAAU,CAAC;MACtC,CAAC;MACD,CAAC1U,UAAU,GAAG0K,QAAQ,CAAC+B,kBAAkB,MACpCoI,UAAU,GAAG5B,KAAK,CAACiC,YAAY,CAACR,UAAU,EAAET,KAAK,CAAC,CAAC,KACnDjU,UAAU,GAAG0K,QAAQ,CAACgC,iBAAiB,CAAC;MAC7C,IAAI4I,YAAY,GAAG5K,QAAQ,CAAC+B,kBAAkB,IAC1C/B,QAAQ,CAACgC,iBAAiB;MAC9BE,UAAU,GAAGpU,kBAAkB,CAACkS,QAAQ,CAACiC,mBAAmB,IACxDjC,QAAQ,CAACmC,kBAAkB,IAC3BsB,eAAe,CAAC5M,QAAQ,EAAE,CAAC+T,YAAY,EAAEZ,UAAU,CAAC;MAAE;MAC1DY,YAAY,CAAC;IACjB,CAAC,MACI,IAAIhF,WAAW,CAACoE,UAAU,CAAC,EAAE;MAAE;MAChCE,WAAW,GAAGA,CAAA,KAAM;QAChBtE,WAAW,CAACoE,UAAU,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,CAAC1U,UAAU,GAAGoU,2BAA2B,CAACM,UAAU,CAAC,MAChDG,UAAU,GAAG5B,KAAK,CAACiC,YAAY,CAACR,UAAU,EAAET,KAAK,CAAC,CAAC,KACnDjU,UAAU,GAAGqU,kBAAkB,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;MACnD,IAAIA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,UAAU,EAAE;QACxD,IAAIa,UAAU,GAAGb,UAAU,KAAK,UAAU,GAAG,MAAM,GAAG,MAAM;QAC5D9H,UAAU,GAAGpU,kBAAkB,CAAC8b,2BAA2B,CAACiB,UAAU,CAAC,IACnEhB,mBAAmB,CAACgB,UAAU,CAAC,EAAE,CACjClB,kBAAkB,CAAChU,IAAI,IAAI,MAAM,EACjC,MAAM,CACT,EAAEgU,kBAAkB,CAACK,UAAU,CAAC,CAAC;MACtC,CAAC,MACI;QACD9H,UAAU,GAAI4I,OAAO,IAAKhd,kBAAkB,CAAC8b,2BAA2B,CAACI,UAAU,CAAC,IAChFH,mBAAmB,CAACG,UAAU,CAAC,EAAE,CACjCL,kBAAkB,CAACmB,OAAO,CAAC,IAAIA,OAAO,EACtCA,OAAO,CACV,EAAEnB,kBAAkB,CAACK,UAAU,CAAC,CAAC;MACtC;IACJ;IACA,OAAO;MAAEA,UAAU;MAAEE,WAAW;MAAEC,UAAU;MAAE7U,UAAU;MAAE4M;IAAW,CAAC;EAC1E,CAAC,CAAE,CAAC;EACJ,OAAO;IAAEoH,OAAO;IAAEN,gBAAgB;IAAEC;EAAS,CAAC;AAClD;;AAEA;AACA,MAAM8B,QAAQ,CAAC;EACXC,WAAWA,CAAC9L,IAAI,EAAE+L,cAAc,EAAEC,OAAO,EAAE;IACvC,IAAI,CAAChM,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC+L,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,cAAc,CAAC,CAAC,CAACrF,WAAW;EAC5C;EACA,IAAIwF,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACH,cAAc,CAAC,CAAC,CAACI,SAAS;EAC1C;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,OAAO,CAACK,MAAM,CAAC,IAAI,CAACN,cAAc,CAAC,CAAC,CAACvH,WAAW,CAACC,WAAW,CAAC2B,KAAK,CAAC;EACnF;EACA,IAAIkG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACN,OAAO,CAACK,MAAM,CAAC,IAAI,CAACN,cAAc,CAAC,CAAC,CAACvH,WAAW,CAACC,WAAW,CAAC4B,GAAG,CAAC;EACjF;EACA,IAAIkG,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACP,OAAO,CAACK,MAAM,CAAC,IAAI,CAACN,cAAc,CAAC,CAAC,CAACvH,WAAW,CAACgI,YAAY,CAACpG,KAAK,CAAC;EACpF;EACA,IAAIqG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACT,OAAO,CAACK,MAAM,CAAC,IAAI,CAACN,cAAc,CAAC,CAAC,CAACvH,WAAW,CAACgI,YAAY,CAACnG,GAAG,CAAC;EAClF;EACAqG,SAASA,CAACxS,IAAI,EAAE;IACZ,OAAO,IAAI,CAAC6R,cAAc,CAAC,CAAC,CAACjS,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;EAChD;AACJ;;AAEA,IAAIyS,gBAAgB,GAAG;EACnBzE,WAAW,EAAE,IAAI;EACjB0E,SAASA,CAACC,OAAO,EAAE;IACf,IAAIjU,KAAK,CAACC,OAAO,CAACgU,OAAO,CAAC9E,MAAM,CAAC,EAAE;MAC/B,OAAO8E,OAAO,CAAC9E,MAAM;IACzB;IACA,OAAO,IAAI;EACf,CAAC;EACDjB,KAAKA,CAACgG,GAAG,EAAEC,eAAe,EAAE;IACxBA,eAAe,CAAC;MACZ/F,SAAS,EAAE8F,GAAG,CAAChH,WAAW,CAACkH;IAC/B,CAAC,CAAC;EACN;AACJ,CAAC;AACD,MAAMC,sBAAsB,GAAGlT,YAAY,CAAC;EACxCG,IAAI,EAAE,oBAAoB;EAC1B0B,eAAe,EAAE,CAAC+Q,gBAAgB;AACtC,CAAC,CAAC;AAEF,IAAIO,gBAAgB,GAAG;EACnBN,SAASA,CAACC,OAAO,EAAE;IACf,IAAI,OAAOA,OAAO,CAAC9E,MAAM,KAAK,UAAU,EAAE;MACtC,OAAO8E,OAAO,CAAC9E,MAAM;IACzB;IACA,OAAO,IAAI;EACf,CAAC;EACDjB,KAAKA,CAACgG,GAAG,EAAEC,eAAe,EAAEI,aAAa,EAAE;IACvC,MAAM;MAAEnB;IAAQ,CAAC,GAAGc,GAAG,CAACnM,OAAO;IAC/B,MAAMyM,IAAI,GAAGN,GAAG,CAAChH,WAAW,CAACkH,IAAI;IACjCle,WAAW,CAACse,IAAI,CAACC,IAAI,CAAC,IAAI,EAAEre,yBAAyB,CAAC8d,GAAG,CAAC/F,KAAK,EAAEiF,OAAO,CAAC,CAAC,EAAGhF,SAAS,IAAK+F,eAAe,CAAC;MAAE/F;IAAU,CAAC,CAAC,EAAEmG,aAAa,CAAC;EAC7I;AACJ,CAAC;AACD,MAAMG,qBAAqB,GAAGvT,YAAY,CAAC;EACvCG,IAAI,EAAE,mBAAmB;EACzB0B,eAAe,EAAE,CAACsR,gBAAgB;AACtC,CAAC,CAAC;AAEF,MAAMK,+BAA+B,GAAG;EACpCC,MAAM,EAAEC,MAAM;EACdC,WAAW,EAAExe,QAAQ;EACrBye,UAAU,EAAEF,MAAM;EAClBG,QAAQ,EAAEH,MAAM;EAChBI,aAAa,EAAEJ;AACnB,CAAC;AAED,IAAIK,cAAc,GAAG;EACjBlB,SAASA,CAACC,OAAO,EAAE;IACf,IAAIA,OAAO,CAACkB,GAAG,KAAKlB,OAAO,CAACmB,MAAM,KAAK,MAAM,IAAI,CAACnB,OAAO,CAACmB,MAAM,CAAC,EAAE;MAC/D,OAAO;QACHD,GAAG,EAAElB,OAAO,CAACkB,GAAG;QAChBC,MAAM,EAAE,MAAM;QACdR,MAAM,EAAE,CAACX,OAAO,CAACW,MAAM,IAAI,KAAK,EAAES,WAAW,CAAC,CAAC;QAC/CP,WAAW,EAAEb,OAAO,CAACa,WAAW;QAChCC,UAAU,EAAEd,OAAO,CAACc,UAAU;QAC9BC,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1BC,aAAa,EAAEhB,OAAO,CAACgB;MAC3B,CAAC;IACL;IACA,OAAO,IAAI;EACf,CAAC;EACD/G,KAAKA,CAACgG,GAAG,EAAEC,eAAe,EAAEI,aAAa,EAAE;IACvC,MAAM;MAAEH;IAAK,CAAC,GAAGF,GAAG,CAAChH,WAAW;IAChC,MAAMoI,aAAa,GAAGC,kBAAkB,CAACnB,IAAI,EAAEF,GAAG,CAAC/F,KAAK,EAAE+F,GAAG,CAACnM,OAAO,CAAC;IACtEvR,WAAW,CAAC4d,IAAI,CAACQ,MAAM,EAAER,IAAI,CAACe,GAAG,EAAEG,aAAa,CAAC,CAACE,IAAI,CAAC,CAAC,CAACpH,SAAS,EAAEG,QAAQ,CAAC,KAAK;MAC9E4F,eAAe,CAAC;QAAE/F,SAAS;QAAEG;MAAS,CAAC,CAAC;IAC5C,CAAC,EAAEgG,aAAa,CAAC;EACrB;AACJ,CAAC;AACD,MAAMkB,yBAAyB,GAAGtU,YAAY,CAAC;EAC3CG,IAAI,EAAE,mBAAmB;EACzBU,mBAAmB,EAAE2S,+BAA+B;EACpD3R,eAAe,EAAE,CAACkS,cAAc;AACpC,CAAC,CAAC;AACF,SAASK,kBAAkBA,CAACnB,IAAI,EAAEjG,KAAK,EAAEpG,OAAO,EAAE;EAC9C,IAAI;IAAEqL,OAAO;IAAElS;EAAQ,CAAC,GAAG6G,OAAO;EAClC,IAAIgN,UAAU;EACd,IAAIC,QAAQ;EACZ,IAAIC,aAAa;EACjB,IAAIS,mBAAmB;EACvB,IAAIC,MAAM,GAAG,CAAC,CAAC;EACfZ,UAAU,GAAGX,IAAI,CAACW,UAAU;EAC5B,IAAIA,UAAU,IAAI,IAAI,EAAE;IACpBA,UAAU,GAAG7T,OAAO,CAAC6T,UAAU;EACnC;EACAC,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;EACxB,IAAIA,QAAQ,IAAI,IAAI,EAAE;IAClBA,QAAQ,GAAG9T,OAAO,CAAC8T,QAAQ;EAC/B;EACAC,aAAa,GAAGb,IAAI,CAACa,aAAa;EAClC,IAAIA,aAAa,IAAI,IAAI,EAAE;IACvBA,aAAa,GAAG/T,OAAO,CAAC+T,aAAa;EACzC;EACA;EACA,IAAI,OAAOb,IAAI,CAACU,WAAW,KAAK,UAAU,EAAE;IACxC;IACAY,mBAAmB,GAAGtB,IAAI,CAACU,WAAW,CAAC,CAAC;EAC5C,CAAC,MACI;IACD;IACAY,mBAAmB,GAAGtB,IAAI,CAACU,WAAW,IAAI,CAAC,CAAC;EAChD;EACAnW,MAAM,CAACC,MAAM,CAAC+W,MAAM,EAAED,mBAAmB,CAAC;EAC1CC,MAAM,CAACZ,UAAU,CAAC,GAAG3B,OAAO,CAACwC,SAAS,CAACzH,KAAK,CAACX,KAAK,CAAC;EACnDmI,MAAM,CAACX,QAAQ,CAAC,GAAG5B,OAAO,CAACwC,SAAS,CAACzH,KAAK,CAACV,GAAG,CAAC;EAC/C,IAAI2F,OAAO,CAACyC,QAAQ,KAAK,OAAO,EAAE;IAC9BF,MAAM,CAACV,aAAa,CAAC,GAAG7B,OAAO,CAACyC,QAAQ;EAC5C;EACA,OAAOF,MAAM;AACjB;AAEA,MAAMG,yBAAyB,GAAG;EAC9BC,UAAU,EAAEzf,QAAQ;EACpB0f,SAAS,EAAE5gB,cAAc;EACzB6gB,OAAO,EAAE7gB,cAAc;EACvB+T,QAAQ,EAAE/T,cAAc;EACxB8gB,UAAU,EAAE5f,QAAQ;EACpB6f,QAAQ,EAAE7f;AACd,CAAC;AAED,IAAI8f,SAAS,GAAG;EACZC,KAAKA,CAACpC,OAAO,EAAEb,OAAO,EAAE;IACpB,IAAIa,OAAO,CAAC8B,UAAU,IAAI9B,OAAO,CAAC+B,SAAS,IAAI/B,OAAO,CAACgC,OAAO,IAAIhC,OAAO,CAACiC,UAAU,IAAIjC,OAAO,CAACkC,QAAQ,EAAE;MACtG,IAAIG,aAAa,GAAG;QAChBP,UAAU,EAAE9B,OAAO,CAAC8B,UAAU,IAAI,IAAI;QACtCC,SAAS,EAAE/B,OAAO,CAAC+B,SAAS,IAAI,IAAI;QACpCC,OAAO,EAAEhC,OAAO,CAACgC,OAAO,IAAI,IAAI;QAChCC,UAAU,EAAEjC,OAAO,CAACiC,UAAU,GAAG9C,OAAO,CAACmD,YAAY,CAACtC,OAAO,CAACiC,UAAU,CAAC,GAAG,IAAI;QAChFC,QAAQ,EAAElC,OAAO,CAACkC,QAAQ,GAAG/C,OAAO,CAACmD,YAAY,CAACtC,OAAO,CAACkC,QAAQ,CAAC,GAAG;MAC1E,CAAC;MACD,IAAIhN,QAAQ;MACZ,IAAI8K,OAAO,CAAC9K,QAAQ,EAAE;QAClBA,QAAQ,GAAG8K,OAAO,CAAC9K,QAAQ;MAC/B;MACA,IAAI,CAACA,QAAQ,IAAI8K,OAAO,CAAC+B,SAAS,IAAI/B,OAAO,CAACgC,OAAO,EAAE;QACnD9M,QAAQ,GAAGzS,iBAAiB,CAACud,OAAO,CAACgC,OAAO,EAAEhC,OAAO,CAAC+B,SAAS,CAAC;MACpE;MACA,OAAO;QACHQ,WAAW,EAAEC,OAAO,CAAC,CAACxC,OAAO,CAAC+B,SAAS,IAAI,CAAC/B,OAAO,CAACgC,OAAO,CAAC;QAC5D9M,QAAQ;QACRuN,QAAQ,EAAEJ,aAAa,CAAE;MAC7B,CAAC;IACL;;IACA,OAAO,IAAI;EACf,CAAC;EACDK,MAAMA,CAACD,QAAQ,EAAEE,YAAY,EAAExD,OAAO,EAAE;IACpC,IAAIyD,mBAAmB,GAAGjgB,eAAe,CAACggB,YAAY,EAAE;MAAEpJ,KAAK,EAAEkJ,QAAQ,CAACR,UAAU;MAAEzI,GAAG,EAAEiJ,QAAQ,CAACP;IAAS,CAAC,CAAC;IAC/G,IAAIU,mBAAmB,EAAE;MACrB,OAAOC,YAAY,CAACJ,QAAQ,CAACX,UAAU,EAAEW,QAAQ,CAACV,SAAS,EAAEa,mBAAmB,EAAEzD,OAAO,CAAC;IAC9F;IACA,OAAO,EAAE;EACb;AACJ,CAAC;AACD,MAAM2D,2BAA2B,GAAG5V,YAAY,CAAC;EAC7CG,IAAI,EAAE,wBAAwB;EAC9B4B,cAAc,EAAE,CAACkT,SAAS,CAAC;EAC3BtU,aAAa,EAAEgU;AACnB,CAAC,CAAC;AACF,SAASgB,YAAYA,CAACf,UAAU,EAAEC,SAAS,EAAEY,YAAY,EAAExD,OAAO,EAAE;EAChE,IAAI4D,OAAO,GAAGjB,UAAU,GAAGvgB,WAAW,CAACugB,UAAU,CAAC,GAAG,IAAI;EACzD,IAAIkB,SAAS,GAAGngB,UAAU,CAAC8f,YAAY,CAACpJ,KAAK,CAAC;EAC9C,IAAI0J,SAAS,GAAGN,YAAY,CAACnJ,GAAG;EAChC,IAAI0J,cAAc,GAAG,EAAE;EACvB,OAAOF,SAAS,GAAGC,SAAS,EAAE;IAC1B,IAAIE,aAAa;IACjB;IACA,IAAI,CAACJ,OAAO,IAAIA,OAAO,CAACC,SAAS,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE;MAC5C,IAAIrB,SAAS,EAAE;QACXoB,aAAa,GAAGhE,OAAO,CAACkE,GAAG,CAACL,SAAS,EAAEjB,SAAS,CAAC;MACrD,CAAC,MACI;QACDoB,aAAa,GAAGH,SAAS;MAC7B;MACAE,cAAc,CAAC9H,IAAI,CAAC+H,aAAa,CAAC;IACtC;IACAH,SAAS,GAAGjgB,OAAO,CAACigB,SAAS,EAAE,CAAC,CAAC;EACrC;EACA,OAAOE,cAAc;AACzB;AAEA,MAAMI,mBAAmB,GAAGpW,YAAY,CAAC;EACrCG,IAAI,EAAE,gBAAgB;EACtBgC,oBAAoB,EAAE;IAClB6L,MAAMA,CAACA,MAAM,EAAEpH,OAAO,EAAE;MACpByP,kBAAkB,CAAC,CAACrI,MAAM,CAAC,EAAEpH,OAAO,CAAC;IACzC,CAAC;IACDkE,YAAY,EAAEuL;EAClB;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA,SAASA,kBAAkBA,CAAC/P,MAAM,EAAEM,OAAO,EAAE;EACzC,IAAI0P,cAAc,GAAGvgB,iBAAiB,CAAC6Q,OAAO,CAACoL,cAAc,CAAC,CAAC,CAAClH,YAAY,CAAC;EAC7E,IAAIwL,cAAc,CAACnY,MAAM,KAAK,CAAC,IAC3BmI,MAAM,CAACnI,MAAM,KAAK,CAAC,IACnBU,KAAK,CAACC,OAAO,CAACwX,cAAc,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IACrC1X,KAAK,CAACC,OAAO,CAACwH,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1BM,OAAO,CAAC0G,QAAQ,CAAC;MACbrH,IAAI,EAAE,kBAAkB;MACxBgF,QAAQ,EAAEqL,cAAc,CAAC,CAAC,CAAC,CAACrL,QAAQ;MACpCgC,SAAS,EAAE3G,MAAM,CAAC,CAAC;IACvB,CAAC,CAAC;IACF;EACJ;EACA,IAAIkQ,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIvW,KAAK,IAAIqG,MAAM,EAAE;IACtB,IAAImQ,UAAU,GAAG,KAAK;IACtB,KAAK,IAAIzjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsjB,cAAc,CAACnY,MAAM,EAAEnL,CAAC,IAAI,CAAC,EAAE;MAC/C,IAAIsjB,cAAc,CAACtjB,CAAC,CAAC,CAACujB,IAAI,KAAKtW,KAAK,EAAE;QAClCqW,cAAc,CAACI,MAAM,CAAC1jB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7ByjB,UAAU,GAAG,IAAI;QACjB;MACJ;IACJ;IACA,IAAI,CAACA,UAAU,EAAE;MACbD,SAAS,CAACtI,IAAI,CAACjO,KAAK,CAAC;IACzB;EACJ;EACA,KAAK,IAAI0W,aAAa,IAAIL,cAAc,EAAE;IACtC1P,OAAO,CAAC0G,QAAQ,CAAC;MACbrH,IAAI,EAAE,qBAAqB;MAC3BgF,QAAQ,EAAE0L,aAAa,CAAC1L;IAC5B,CAAC,CAAC;EACN;EACA,KAAK,IAAI2L,QAAQ,IAAIJ,SAAS,EAAE;IAC5B5P,OAAO,CAAC+F,WAAW,CAACkK,cAAc,CAACD,QAAQ,CAAC;EAChD;AACJ;AAEA,SAASE,iBAAiBA,CAACrM,WAAW,EAAE7D,OAAO,EAAE;EAC7CA,OAAO,CAACmQ,OAAO,CAACC,OAAO,CAAC,UAAU,EAAExZ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExI,yBAAyB,CAACwV,WAAW,CAACC,WAAW,EAAE9D,OAAO,CAACqL,OAAO,CAAC,CAAC,EAAE;IAAEgF,IAAI,EAAErQ,OAAO,CAACsQ;EAAQ,CAAC,CAAC,CAAC;AACzK;AAEA,SAASC,gBAAgBA,CAACC,UAAU,EAAExQ,OAAO,EAAE;EAC3C,IAAI;IAAEmQ;EAAQ,CAAC,GAAGnQ,OAAO;EACzB,IAAImQ,OAAO,CAACM,WAAW,CAAC,WAAW,CAAC,EAAE;IAClCN,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE/gB,cAAc,CAACmhB,UAAU,EAAExQ,OAAO,CAAC,CAAC;EACrE;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM0Q,aAAa,GAAG,CAClBpE,sBAAsB,EACtBK,qBAAqB,EACrBe,yBAAyB,EACzBsB,2BAA2B,EAC3BQ,mBAAmB,EACnBpW,YAAY,CAAC;EACTG,IAAI,EAAE,MAAM;EACZM,cAAc,EAAE,CACXoO,KAAK,IAAKlD,0BAA0B,CAACkD,KAAK,CAAC/D,YAAY,CAAC,CAC5D;EACDvI,eAAe,EAAE;IACbkI,WAAW,EAAEqM,iBAAiB;IAC9BM,UAAU,EAAED;EAChB;AACJ,CAAC,CAAC,CACL;AAED,MAAMI,UAAU,CAAC;EACbxF,WAAWA,CAACyF,aAAa,EAAEC,aAAa,EAAE;IACtC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,aAAa,GAAG,IAAIxhB,aAAa,CAAC,IAAI,CAACyhB,KAAK,CAACtE,IAAI,CAAC,IAAI,CAAC,CAAC;EACjE;EACAuE,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,IAAI,CAACL,KAAK,CAACxJ,IAAI,CAAC4J,IAAI,CAAC;IACrB,IAAI,CAACH,aAAa,CAACE,OAAO,CAACE,KAAK,CAAC;EACrC;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACN,aAAa,CAACK,KAAK,CAACC,KAAK,CAAC;EACnC;EACAC,MAAMA,CAACD,KAAK,EAAEE,KAAK,EAAE;IACjB,IAAI,CAACR,aAAa,CAACO,MAAM,CAACD,KAAK,EAAEE,KAAK,CAAC;EAC3C;EACAP,KAAKA,CAAA,EAAG;IACJ,IAAI;MAAEF;IAAM,CAAC,GAAG,IAAI;IACpB,OAAOA,KAAK,CAACvZ,MAAM,EAAE;MACjB,IAAIia,cAAc,GAAG,EAAE;MACvB,IAAIN,IAAI;MACR,OAAQA,IAAI,GAAGJ,KAAK,CAACW,KAAK,CAAC,CAAC,EAAG;QAC3B,IAAI,CAACC,OAAO,CAACR,IAAI,CAAC;QAClBM,cAAc,CAAClK,IAAI,CAAC4J,IAAI,CAAC;MAC7B;MACA,IAAI,CAACS,OAAO,CAACH,cAAc,CAAC;IAChC,CAAC,CAAC;EACN;;EACAE,OAAOA,CAACR,IAAI,EAAE;IACV,IAAI,IAAI,CAACN,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACM,IAAI,CAAC;IAC5B;EACJ;EACAS,OAAOA,CAACH,cAAc,EAAE;IACpB,IAAI,IAAI,CAACX,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACW,cAAc,CAAC;IACtC;EACJ;AACJ;;AAEA;AACA,SAASI,UAAUA,CAAC/N,WAAW,EAAEgO,WAAW,EAAExG,OAAO,EAAE;EACnD,IAAIjF,KAAK;EACT;EACA,IAAI,gBAAgB,CAAC0L,IAAI,CAACjO,WAAW,CAACkO,gBAAgB,CAAC,EAAE;IACrD3L,KAAK,GAAGvC,WAAW,CAACgI,YAAY;EACpC,CAAC,MACI;IAAE;IACHzF,KAAK,GAAGvC,WAAW,CAACC,WAAW;EACnC;EACA,OAAOuH,OAAO,CAAC2G,WAAW,CAAC5L,KAAK,CAACX,KAAK,EAAEW,KAAK,CAACV,GAAG,EAAEjW,eAAe,CAACoiB,WAAW,CAACI,WAAW,IAAIC,gBAAgB,CAACrO,WAAW,CAAC,CAAC,EAAE;IAC1HsO,cAAc,EAAEtO,WAAW,CAACuO,aAAa;IACzCC,gBAAgB,EAAER,WAAW,CAACS;EAClC,CAAC,CAAC;AACN;AACA;AACA;AACA,SAASJ,gBAAgBA,CAACrO,WAAW,EAAE;EACnC,IAAI;IAAEkO;EAAiB,CAAC,GAAGlO,WAAW;EACtC,IAAIkO,gBAAgB,KAAK,MAAM,EAAE;IAC7B,OAAO;MAAEjc,IAAI,EAAE;IAAU,CAAC;EAC9B;EACA,IAAIic,gBAAgB,KAAK,OAAO,EAAE;IAC9B,OAAO;MAAEjc,IAAI,EAAE,SAAS;MAAEE,KAAK,EAAE;IAAO,CAAC,CAAC,CAAC;EAC/C;;EACA,IAAIuc,IAAI,GAAG5iB,aAAa,CAACkU,WAAW,CAACgI,YAAY,CAACpG,KAAK,EAAE5B,WAAW,CAACgI,YAAY,CAACnG,GAAG,CAAC;EACtF,IAAI6M,IAAI,KAAK,IAAI,IAAIA,IAAI,GAAG,CAAC,EAAE;IAC3B;IACA,OAAO;MAAEzc,IAAI,EAAE,SAAS;MAAEE,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAU,CAAC;EAC9D;EACA;EACA,OAAO;IAAEH,IAAI,EAAE,SAAS;IAAEE,KAAK,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAU,CAAC;AAC7D;;AAEA;AACA;AACA,MAAMuc,mBAAmB,CAAC;EACtBrH,WAAWA,CAACsH,KAAK,EAAE;IACf,IAAI,CAACC,sBAAsB,GAAG7iB,OAAO,CAAC,IAAI,CAAC8iB,uBAAuB,CAAC;IACnE,IAAI,CAACvb,kBAAkB,GAAGvH,OAAO,CAACuH,kBAAkB,CAAC;IACrD,IAAI,CAACU,WAAW,GAAGjI,OAAO,CAACiI,WAAW,CAAC;IACvC,IAAI,CAAC8D,gBAAgB,GAAGa,qBAAqB,CAAC,CAAC;IAC/C,IAAI,CAACmW,YAAY,GAAG/iB,OAAO,CAACgjB,cAAc,CAAC;IAC3C,IAAI,CAACC,UAAU,GAAGjjB,OAAO,CAACijB,UAAU,CAAC;IACrC,IAAI,CAACtK,aAAa,GAAG3Y,OAAO,CAAC2Y,aAAa,CAAC;IAC3C,IAAI,CAAC5H,cAAc,GAAG/Q,OAAO,CAAC+Q,cAAc,CAAC;IAC7C,IAAI,CAACmS,yBAAyB,GAAGhjB,aAAa,CAACgjB,yBAAyB,CAAC;IACzE,IAAI,CAACC,YAAY,GAAGnjB,OAAO,CAACmjB,YAAY,CAAC;IACzC,IAAI,CAACC,gBAAgB,GAAGljB,aAAa,CAACkjB,gBAAgB,CAAC;IACvD,IAAI,CAACC,oBAAoB,GAAGrjB,OAAO,CAACqjB,oBAAoB,EAAEjjB,YAAY,CAAC;IACvE,IAAI,CAACkjB,iBAAiB,GAAGtjB,OAAO,CAACsjB,iBAAiB,CAAC;IACnD,IAAI,CAACC,yBAAyB,GAAGrjB,aAAa,CAACqjB,yBAAyB,CAAC;IACzE,IAAI,CAACxB,UAAU,GAAG/hB,OAAO,CAAC+hB,UAAU,CAAC;IACrC,IAAI,CAACzB,OAAO,GAAG,IAAIhgB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACkjB,YAAY,GAAG,IAAI1C,UAAU,CAAC,IAAI,CAAC2C,aAAa,CAAC5G,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC6G,UAAU,CAAC7G,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7F,IAAI,CAAC8G,2BAA2B,GAAG,CAAC,CAAC;IACrC,IAAI,CAACC,6BAA6B,GAAG,CAAC,CAAC;IACvC,IAAI,CAACC,uBAAuB,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,yBAAyB,GAAG,CAAC,CAAC;IACnC,IAAI,CAACC,8BAA8B,GAAG,CAAC,CAAC;IACxC,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC1I,cAAc,GAAG,MAAM,IAAI,CAAC2I,IAAI;IACrC,IAAI,CAACrN,QAAQ,GAAI7D,MAAM,IAAK;MACxB,IAAI,CAACwQ,YAAY,CAACpC,OAAO,CAACpO,MAAM,CAAC,CAAC,CAAC;IACvC,CAAC;;IACD,IAAI,CAAC4P,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACY,YAAY,CAACjC,KAAK,CAAC,CAAC;IACzB,IAAIrQ,sBAAsB,GAAG,CAAC,CAAC;IAC/B,IAAIiT,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACxB,KAAK,CAAC3R,eAAe,EAAEC,sBAAsB,EAAE0R,KAAK,CAAC1M,WAAW,CAAC;IAC3G,IAAImO,eAAe,GAAGF,WAAW,CAACpQ,eAAe,CAACvI,WAAW,IAAI2Y,WAAW,CAAC/N,WAAW,CAAC5K,WAAW;IACpG,IAAI8Y,eAAe,GAAG,IAAI,CAACzB,sBAAsB,CAACwB,eAAe,EAAEF,WAAW,EAAEvB,KAAK,CAAC3R,eAAe,EAAEC,sBAAsB,CAAC;IAC9H;IACA;IACA0R,KAAK,CAAC1M,WAAW,CAACqO,kBAAkB,GAAG,IAAI;IAC3C,IAAI,CAACjE,OAAO,CAACkE,cAAc,CAAC5B,KAAK,CAAC1M,WAAW,CAAC;IAC9C,IAAI,CAACoK,OAAO,CAACmE,UAAU,CAACH,eAAe,CAAChb,OAAO,CAAC;IAChD,IAAIgK,WAAW,GAAG9S,cAAc,CAAC2jB,WAAW,CAACpQ,eAAe,EAAEoQ,WAAW,CAAC3I,OAAO,CAAC;IAClF,IAAIxH,WAAW,GAAGsQ,eAAe,CAAC/Q,oBAAoB,CAACE,KAAK,CAACH,WAAW,CAAC;IACzE,IAAI,CAAC5S,mBAAmB,CAACsT,WAAW,CAACC,WAAW,EAAEX,WAAW,CAAC,EAAE;MAC5DA,WAAW,GAAGU,WAAW,CAACgI,YAAY,CAACpG,KAAK;IAChD;IACA,IAAI8O,eAAe,GAAG;MAClBlJ,OAAO,EAAE2I,WAAW,CAAC3I,OAAO;MAC5BlS,OAAO,EAAE6a,WAAW,CAACpQ,eAAe;MACpCqC,WAAW,EAAE+N,WAAW,CAAC/N,WAAW;MACpCF,WAAW,EAAE0M,KAAK,CAAC1M,WAAW;MAC9BW,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvByJ,OAAO,EAAE,IAAI,CAACA,OAAO;MACrB/E,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC;IACD;IACA,KAAK,IAAIoJ,QAAQ,IAAIR,WAAW,CAAC/N,WAAW,CAACnM,WAAW,EAAE;MACtD0a,QAAQ,CAACD,eAAe,CAAC;IAC7B;IACA;IACA,IAAIrQ,YAAY,GAAGP,gBAAgB,CAACqQ,WAAW,CAACpQ,eAAe,EAAEC,WAAW,EAAE0Q,eAAe,CAAC;IAC9F,IAAIE,YAAY,GAAG;MACf1T,sBAAsB;MACtBmT,eAAe;MACf/Q,WAAW;MACXU,WAAW;MACX6Q,aAAa,EAAE,IAAI,CAACtB,yBAAyB,CAACmB,eAAe,CAAC;MAC9DrQ,YAAY;MACZyQ,YAAY,EAAE,CAAC,CAAC;MAChBnE,UAAU,EAAE/f,qBAAqB,CAAC,CAAC;MACnCmkB,oBAAoB,EAAEnkB,qBAAqB,CAAC,CAAC;MAC7CokB,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE,IAAI,CAAChC,gBAAgB,CAACsB,eAAe,CAAC,CAACU;IAC5D,CAAC;IACD,IAAIC,eAAe,GAAGte,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0d,eAAe,CAAC,EAAEE,YAAY,CAAC;IACrF,KAAK,IAAIU,OAAO,IAAInB,WAAW,CAAC/N,WAAW,CAACrM,QAAQ,EAAE;MAClDhD,MAAM,CAACC,MAAM,CAAC4d,YAAY,EAAEU,OAAO,CAAC,IAAI,EAAE,IAAI,EAAED,eAAe,CAAC,CAAC;IACrE;IACA,IAAIE,gBAAgB,CAACX,YAAY,EAAEF,eAAe,CAAC,EAAE;MACjD,IAAI,CAACpE,OAAO,CAACC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3C;;IACA,IAAI,CAACnI,KAAK,GAAGwM,YAAY;IACzB,IAAI,CAAClB,UAAU,CAAC,CAAC;IACjB,IAAI,CAACF,YAAY,CAAC/B,MAAM,CAAC,CAAC;EAC9B;EACA+D,YAAYA,CAACvU,eAAe,EAAEwU,kBAAkB,EAAE;IAC9C,IAAI;MAAE7C;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI6C,kBAAkB,KAAK5b,SAAS,EAAE;MAClC+Y,KAAK,CAAC3R,eAAe,GAAGA,eAAe;IAC3C,CAAC,MACI;MACD2R,KAAK,CAAC3R,eAAe,GAAGlK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAG4b,KAAK,CAAC3R,eAAe,IAAI,CAAC,CAAE,CAAC,EAAEA,eAAe,CAAC;MACxG,IAAI,CAAC+S,kBAAkB,CAACvM,IAAI,CAAC,GAAGgO,kBAAkB,CAAC;IACvD;IACA,IAAIA,kBAAkB,KAAK5b,SAAS,IAAI4b,kBAAkB,CAAC/d,MAAM,EAAE;MAC/D,IAAI,CAAC8b,YAAY,CAACpC,OAAO,CAAC;QACtB5R,IAAI,EAAE;MACV,CAAC,CAAC;IACN;EACJ;EACAiU,aAAaA,CAACzQ,MAAM,EAAE;IAClB,IAAI;MAAE4P,KAAK;MAAExK,KAAK;MAAEkI;IAAQ,CAAC,GAAG,IAAI;IACpC,IAAIpP,sBAAsB,GAAG+B,4BAA4B,CAACmF,KAAK,CAAClH,sBAAsB,EAAE8B,MAAM,CAAC;IAC/F,IAAImR,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACxB,KAAK,CAAC3R,eAAe,EAAEC,sBAAsB,EAAE0R,KAAK,CAAC1M,WAAW,CAAC;IAC3G,IAAImO,eAAe,GAAGtR,cAAc,CAACqF,KAAK,CAACiM,eAAe,EAAErR,MAAM,CAAC;IACnE,IAAIsR,eAAe,GAAG,IAAI,CAACzB,sBAAsB,CAACwB,eAAe,EAAEF,WAAW,EAAEvB,KAAK,CAAC3R,eAAe,EAAEC,sBAAsB,CAAC;IAC9H;IACA;IACA0R,KAAK,CAAC1M,WAAW,CAACqO,kBAAkB,GAAG,IAAI;IAC3CjE,OAAO,CAACkE,cAAc,CAAC5B,KAAK,CAAC1M,WAAW,CAAC;IACzCoK,OAAO,CAACmE,UAAU,CAACH,eAAe,CAAChb,OAAO,CAAC;IAC3C,IAAIob,eAAe,GAAG;MAClBlJ,OAAO,EAAE2I,WAAW,CAAC3I,OAAO;MAC5BlS,OAAO,EAAE6a,WAAW,CAACpQ,eAAe;MACpCqC,WAAW,EAAE+N,WAAW,CAAC/N,WAAW;MACpCF,WAAW,EAAE0M,KAAK,CAAC1M,WAAW;MAC9BW,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvByJ,OAAO;MACP/E,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC;IACD,IAAI;MAAEjI,WAAW;MAAEU;IAAY,CAAC,GAAGoE,KAAK;IACxC,IAAI,IAAI,CAAC8L,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3Q,oBAAoB,KAAK+Q,eAAe,CAAC/Q,oBAAoB,EAAE;MAAE;MACxFS,WAAW,GAAGsQ,eAAe,CAAC/Q,oBAAoB,CAACE,KAAK,CAACH,WAAW,CAAC;IACzE;IACAA,WAAW,GAAGxS,iBAAiB,CAACwS,WAAW,EAAEN,MAAM,CAAC;IACpDgB,WAAW,GAAGZ,iBAAiB,CAACY,WAAW,EAAEhB,MAAM,EAAEM,WAAW,EAAEgR,eAAe,CAAC/Q,oBAAoB,CAAC;IACvG,IAAIP,MAAM,CAACxD,IAAI,KAAK,MAAM;IAAI;IAC1BwD,MAAM,CAACxD,IAAI,KAAK,MAAM;IAAI;IAC1B,CAAC9O,mBAAmB,CAACsT,WAAW,CAACgI,YAAY,EAAE1I,WAAW,CAAC,EAAE;MAC7DA,WAAW,GAAGU,WAAW,CAACgI,YAAY,CAACpG,KAAK;IAChD;IACA,IAAIvB,YAAY,GAAGD,kBAAkB,CAACgE,KAAK,CAAC/D,YAAY,EAAErB,MAAM,EAAEgB,WAAW,EAAE0Q,eAAe,CAAC;IAC/F,IAAI/D,UAAU,GAAG3f,gBAAgB,CAACoX,KAAK,CAACuI,UAAU,EAAE3N,MAAM,EAAEqB,YAAY,EAAEL,WAAW,EAAE0Q,eAAe,CAAC;IACvG,IAAIgB,eAAe,GAAGxQ,0BAA0B,CAACb,YAAY,CAAC,CAAC,CAAC;IAChE,IAAI0Q,oBAAoB,GAAIW,eAAe,IAAI,CAACpB,eAAe,CAAChb,OAAO,CAACqc,yBAAyB,GAC5FvN,KAAK,CAAC2M,oBAAoB,IAAIpE,UAAU;IAAI;IAC7CA,UAAU;IACd,IAAI;MAAEiF,iBAAiB;MAAER;IAAgB,CAAC,GAAG,IAAI,CAAChC,gBAAgB,CAACsB,eAAe,CAAC,CAAC,CAAC;IACrF,IAAImB,eAAe,GAAG,IAAI,CAACxC,oBAAoB,CAAChP,YAAY,CAAC;IAC7D,IAAIyQ,YAAY,GAAG,IAAI,CAACxB,iBAAiB,CAACyB,oBAAoB,CAAC1Y,IAAI,EAAEuZ,iBAAiB,EAAEC,eAAe,CAAC;IACxG,IAAIC,QAAQ,GAAG;MACX5U,sBAAsB;MACtBmT,eAAe;MACf/Q,WAAW;MACXU,WAAW;MACXK,YAAY;MACZsM,UAAU;MACVoE,oBAAoB;MACpBK,eAAe;MACfN,YAAY;MACZD,aAAa,EAAE,IAAI,CAACtB,yBAAyB,CAACmB,eAAe,CAAC;MAC9DM,aAAa,EAAErN,mBAAmB,CAACS,KAAK,CAAC4M,aAAa,EAAEhS,MAAM,CAAC;MAC/DiS,cAAc,EAAEnN,mBAAmB,CAACM,KAAK,CAAC6M,cAAc,EAAEjS,MAAM,CAAC;MACjEkS,SAAS,EAAEjN,eAAe,CAACG,KAAK,CAAC8M,SAAS,EAAElS,MAAM,CAAC;MACnDmS,WAAW,EAAE3M,iBAAiB,CAACJ,KAAK,CAAC+M,WAAW,EAAEnS,MAAM;IAC5D,CAAC;IACD,IAAIqS,eAAe,GAAGte,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0d,eAAe,CAAC,EAAEoB,QAAQ,CAAC;IACjF,KAAK,IAAIR,OAAO,IAAInB,WAAW,CAAC/N,WAAW,CAACrM,QAAQ,EAAE;MAClDhD,MAAM,CAACC,MAAM,CAAC8e,QAAQ,EAAER,OAAO,CAAClN,KAAK,EAAEpF,MAAM,EAAEqS,eAAe,CAAC,CAAC,CAAC,CAAC;IACtE;;IACA,IAAIU,UAAU,GAAGR,gBAAgB,CAACnN,KAAK,EAAEsM,eAAe,CAAC;IACzD,IAAIsB,SAAS,GAAGT,gBAAgB,CAACO,QAAQ,EAAEpB,eAAe,CAAC;IAC3D;IACA,IAAI,CAACqB,UAAU,IAAIC,SAAS,EAAE;MAC1B1F,OAAO,CAACC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;IACpC,CAAC,MACI,IAAIwF,UAAU,IAAI,CAACC,SAAS,EAAE;MAC/B1F,OAAO,CAACC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC;IACrC;IACA,IAAI,CAACnI,KAAK,GAAG0N,QAAQ;IACrB,IAAIlD,KAAK,CAACqD,QAAQ,EAAE;MAChBrD,KAAK,CAACqD,QAAQ,CAACjT,MAAM,CAAC;IAC1B;EACJ;EACA0Q,UAAUA,CAAA,EAAG;IACT,IAAI;MAAEd,KAAK;MAAExK;IAAM,CAAC,GAAG,IAAI;IAC3B,IAAI8N,OAAO,GAAG,IAAI,CAAChC,IAAI;IACvB,IAAIC,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACxB,KAAK,CAAC3R,eAAe,EAAEmH,KAAK,CAAClH,sBAAsB,EAAE0R,KAAK,CAAC1M,WAAW,CAAC;IACjH,IAAIoO,eAAe,GAAG,IAAI,CAACzB,sBAAsB,CAACzK,KAAK,CAACiM,eAAe,EAAEF,WAAW,EAAEvB,KAAK,CAAC3R,eAAe,EAAEmH,KAAK,CAAClH,sBAAsB,CAAC;IAC1I,IAAIgT,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGnd,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAAE2U,SAAS,EAAE,IAAI,CAACoG,UAAU,CAAC3J,KAAK,CAACpE,WAAW,EAAEsQ,eAAe,CAAChb,OAAO,EAAE6a,WAAW,CAAC3I,OAAO,CAAC;MAAEtF,WAAW,EAAE0M,KAAK,CAAC1M,WAAW;MAAEW,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEyJ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE/E,cAAc,EAAE,IAAI,CAACA;IAAe,CAAC,EAAE4I,WAAW,CAAC,EAAEG,eAAe,CAAC,EAAElM,KAAK,CAAC;IAC7T,IAAI+N,cAAc,GAAGhC,WAAW,CAAC/N,WAAW,CAAC1K,oBAAoB;IACjE,IAAI0a,kBAAkB,GAAGF,OAAO,IAAIA,OAAO,CAACnS,eAAe;IAC3D,IAAIsS,kBAAkB,GAAGlC,WAAW,CAACpQ,eAAe;IACpD,IAAIqS,kBAAkB,IAAIA,kBAAkB,KAAKC,kBAAkB,EAAE;MACjE,IAAID,kBAAkB,CAACnI,QAAQ,KAAKoI,kBAAkB,CAACpI,QAAQ,EAAE;QAC7D;QACA7F,KAAK,CAAC/D,YAAY,GAAG6P,IAAI,CAAC7P,YAAY,GAAGY,6BAA6B,CAACiP,IAAI,CAAC7P,YAAY,EAAE+D,KAAK,CAACpE,WAAW,EAAEkQ,IAAI,CAAC;QAClH9L,KAAK,CAACuI,UAAU,GAAGuD,IAAI,CAACvD,UAAU,GAAGzf,qBAAqB,CAACgjB,IAAI,CAACvD,UAAU,EAAEuF,OAAO,CAAC1K,OAAO,EAAE0I,IAAI,CAAC1I,OAAO,CAAC;QAC1GpD,KAAK,CAAC2M,oBAAoB,GAAGb,IAAI,CAACa,oBAAoB,GAAG7jB,qBAAqB,CAACgjB,IAAI,CAACa,oBAAoB,EAAEmB,OAAO,CAAC1K,OAAO,EAAE0I,IAAI,CAAC1I,OAAO,CAAC;MAC5I;MACA,KAAK,IAAItI,UAAU,IAAIiT,cAAc,EAAE;QACnC,IAAI,IAAI,CAAClC,kBAAkB,CAACqC,OAAO,CAACpT,UAAU,CAAC,KAAK,CAAC,CAAC,IAClDkT,kBAAkB,CAAClT,UAAU,CAAC,KAAKmT,kBAAkB,CAACnT,UAAU,CAAC,EAAE;UACnEiT,cAAc,CAACjT,UAAU,CAAC,CAACmT,kBAAkB,CAACnT,UAAU,CAAC,EAAEgR,IAAI,CAAC;QACpE;MACJ;IACJ;IACA,IAAI,CAACD,kBAAkB,GAAG,EAAE;IAC5B,IAAIrB,KAAK,CAAC2D,MAAM,EAAE;MACd3D,KAAK,CAAC2D,MAAM,CAACrC,IAAI,CAAC;IACtB;EACJ;EACAE,kBAAkBA,CAACnT,eAAe,EAAEC,sBAAsB,EAAEgF,WAAW,EAAE;IACrE;IACA,IAAI,CAAC,IAAI,CAAC8N,kBAAkB,CAACtc,MAAM,IAC/BuJ,eAAe,KAAK,IAAI,CAACuV,qBAAqB,IAC9CtV,sBAAsB,KAAK,IAAI,CAACuV,4BAA4B,EAAE;MAC9D,OAAO,IAAI,CAACC,yBAAyB;IACzC;IACA,IAAI;MAAEC,cAAc;MAAEvQ,WAAW;MAAEjF,cAAc;MAAEyV,mBAAmB;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACC,yBAAyB,CAAC7V,eAAe,EAAEC,sBAAsB,CAAC;IAC1J6V,kBAAkB,CAACF,KAAK,CAAC;IACzB,IAAIrL,OAAO,GAAG,IAAI,CAACuH,YAAY,CAAC4D,cAAc,CAAC1I,QAAQ,EAAE0I,cAAc,CAACK,MAAM,EAAEL,cAAc,CAACM,qBAAqB,EAAEN,cAAc,CAACO,QAAQ,EAAEP,cAAc,CAACrgB,QAAQ,EAAE8P,WAAW,EAAEwQ,mBAAmB,EAAED,cAAc,CAACQ,qBAAqB,CAAC;IAC/O,IAAIrO,SAAS,GAAG,IAAI,CAAC/H,cAAc,CAACqF,WAAW,CAACzL,KAAK,EAAE,IAAI,CAAC6b,qBAAqB,EAAE,IAAI,CAACC,4BAA4B,EAAEtV,cAAc,CAAC;IACrI,IAAI0H,KAAK,GAAG,IAAI,CAACoK,UAAU,CAAC0D,cAAc,EAAEvQ,WAAW,CAAC;IACxD,IAAIgR,aAAa,GAAG,IAAI,CAACzO,aAAa,CAACgO,cAAc,EAAE,IAAI,CAACH,qBAAqB,EAAE3N,KAAK,EAAEC,SAAS,EAAE5C,WAAW,CAAC;IACjH,OAAO,IAAI,CAACwQ,yBAAyB,GAAG;MACpC3S,eAAe,EAAE4S,cAAc;MAC/BvQ,WAAW;MACXoF,OAAO;MACP1C,SAAS;MACTD,KAAK;MACLuO,aAAa;MACbjW,cAAc;MACdkW,mBAAmB,EAAET,mBAAmB,CAAC5e;IAC7C,CAAC;EACL;EACA;EACA8e,yBAAyBA,CAAC7V,eAAe,EAAEC,sBAAsB,EAAE;IAC/D,IAAI;MAAEoW,OAAO;MAAEN;IAAO,CAAC,GAAG5lB,eAAe,CAAC,CACtC1D,oBAAoB,EACpBuT,eAAe,EACfC,sBAAsB,CACzB,CAAC;IACF,IAAI0V,mBAAmB,GAAG,IAAI,CAACrf,kBAAkB,CAAC+f,OAAO,CAAC;IAC1D,IAAID,mBAAmB,GAAGT,mBAAmB,CAAC5e,GAAG;IACjD,IAAImJ,cAAc,GAAG,IAAI,CAAClJ,WAAW,CAAC+e,MAAM,IAAIJ,mBAAmB,CAACnf,WAAW,EAAE4f,mBAAmB,CAAC,CAAC/d,OAAO;IAC7G,IAAI8M,WAAW,GAAG,IAAI,CAACrK,gBAAgB,CAACkF,eAAe,CAACsW,OAAO,IAAI,EAAE,EAAE1G,aAAa,CAAC;IACrF,IAAI1J,QAAQ,GAAG,IAAI,CAAC4M,8BAA8B,GAAGhd,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1F,oBAAoB,CAAC,EAAEE,0BAA0B,CAAC,EAAEE,wBAAwB,CAAC,EAAE0U,WAAW,CAACxK,gBAAgB,CAAC,EAAEwK,WAAW,CAACvK,cAAc,CAAC;IACxQ,IAAIgb,KAAK,GAAG,CAAC,CAAC;IACd,IAAIne,GAAG,GAAGtH,eAAe,CAAC,CACtB1D,oBAAoB,EACpByT,cAAc,EACdF,eAAe,EACfC,sBAAsB,CACzB,CAAC;IACF,IAAImL,OAAO,GAAG,CAAC,CAAC;IAChB,IAAImL,UAAU,GAAG,IAAI,CAAC7D,2BAA2B;IACjD,IAAI8D,cAAc,GAAG,IAAI,CAAC7D,6BAA6B;IACvD,IAAI8D,UAAU,GAAG,KAAK;IACtB,KAAK,IAAIxU,UAAU,IAAIxK,GAAG,EAAE;MACxB,IAAI,IAAI,CAACsb,kBAAkB,CAACsC,OAAO,CAACpT,UAAU,CAAC,KAAK,CAAC,CAAC,KAAKxK,GAAG,CAACwK,UAAU,CAAC,KAAKsU,UAAU,CAACtU,UAAU,CAAC,IAAKtR,0BAA0B,CAACsR,UAAU,CAAC,IAC3IA,UAAU,IAAIsU,UAAW,IAC1B5lB,0BAA0B,CAACsR,UAAU,CAAC,CAACsU,UAAU,CAACtU,UAAU,CAAC,EAAExK,GAAG,CAACwK,UAAU,CAAC,CAAE,CAAC,EAAE;QACnFmJ,OAAO,CAACnJ,UAAU,CAAC,GAAGuU,cAAc,CAACvU,UAAU,CAAC;MACpD,CAAC,MACI,IAAIiE,QAAQ,CAACjE,UAAU,CAAC,EAAE;QAC3BmJ,OAAO,CAACnJ,UAAU,CAAC,GAAGiE,QAAQ,CAACjE,UAAU,CAAC,CAACxK,GAAG,CAACwK,UAAU,CAAC,CAAC;QAC3DwU,UAAU,GAAG,IAAI;MACrB,CAAC,MACI;QACDb,KAAK,CAAC3T,UAAU,CAAC,GAAGsU,UAAU,CAACtU,UAAU,CAAC;MAC9C;IACJ;IACA,IAAIwU,UAAU,EAAE;MACZ,IAAI,CAAC/D,2BAA2B,GAAGjb,GAAG;MACtC,IAAI,CAACkb,6BAA6B,GAAGvH,OAAO;MAC5C,IAAI,CAACmK,qBAAqB,GAAGvV,eAAe;MAC5C,IAAI,CAACwV,4BAA4B,GAAGvV,sBAAsB;IAC9D;IACA,IAAI,CAAC+S,kBAAkB,CAACxM,IAAI,CAAC,GAAG,IAAI,CAACuM,kBAAkB,CAAC;IACxD,IAAI,CAACA,kBAAkB,GAAG,EAAE;IAC5B,OAAO;MACHtU,UAAU,EAAE,IAAI,CAACiU,2BAA2B;MAC5CgD,cAAc,EAAE,IAAI,CAAC/C,6BAA6B;MAClDxN,WAAW;MACXwQ,mBAAmB;MACnBzV,cAAc;MACd0V;IACJ,CAAC;EACL;EACA/D,uBAAuBA,CAAClU,QAAQ,EAAEuV,WAAW,EAAElT,eAAe,EAAEC,sBAAsB,EAAE;IACpF,IAAIZ,QAAQ,GAAG6T,WAAW,CAACrL,SAAS,CAAClK,QAAQ,CAAC;IAC9C,IAAI,CAAC0B,QAAQ,EAAE;MACX,MAAM,IAAIhB,KAAK,CAAE,aAAYV,QAAS,2EAA0E,CAAC;IACrH;IACA,IAAI;MAAE+X,cAAc;MAAEE;IAAM,CAAC,GAAG,IAAI,CAACc,qBAAqB,CAACrX,QAAQ,EAAE6T,WAAW,CAAC/N,WAAW,EAAE+N,WAAW,CAAChT,cAAc,EAAEF,eAAe,EAAEC,sBAAsB,CAAC;IAClK6V,kBAAkB,CAACF,KAAK,CAAC;IACzB,IAAItT,oBAAoB,GAAG,IAAI,CAAC2P,yBAAyB,CAAC;MACtD0E,yBAAyB,EAAEtX,QAAQ,CAAC8B,cAAc,CAACwV,yBAAyB;MAC5ErW,QAAQ,EAAEjB,QAAQ,CAACiB,QAAQ;MAC3BC,YAAY,EAAElB,QAAQ,CAACkB,YAAY;MACnCqW,cAAc,EAAEvX,QAAQ,CAAC8B,cAAc,CAACyV,cAAc;MACtDrM,OAAO,EAAE2I,WAAW,CAAC3I,OAAO;MAC5BtF,WAAW,EAAE,IAAI,CAAC0M,KAAK,CAAC1M,WAAW;MACnC4R,WAAW,EAAEnB,cAAc,CAACmB,WAAW;MACvCC,WAAW,EAAEpB,cAAc,CAACoB,WAAW;MACvCC,mBAAmB,EAAErB,cAAc,CAACqB,mBAAmB;MACvDC,QAAQ,EAAEtB,cAAc,CAACsB,QAAQ;MACjCC,aAAa,EAAEvB,cAAc,CAACuB,aAAa;MAC3CC,aAAa,EAAExB,cAAc,CAACwB,aAAa;MAC3CC,UAAU,EAAEzB,cAAc,CAACyB,UAAU;MACrCC,QAAQ,EAAE1B,cAAc,CAAC0B,QAAQ;MACjCC,QAAQ,EAAE3B,cAAc,CAAC4B,GAAG;MAC5BC,eAAe,EAAE7B,cAAc,CAAC8B,UAAU;MAC1CC,iBAAiB,EAAE/B,cAAc,CAACgC,YAAY;MAC9CC,cAAc,EAAEjC,cAAc,CAACiC;IACnC,CAAC,CAAC;IACF,IAAInI,OAAO,GAAG,IAAI,CAAC0C,YAAY,CAACvU,QAAQ,EAAE,IAAI,CAAC2M,cAAc,EAAE4I,WAAW,CAAC3I,OAAO,CAAC;IACnF,OAAO;MAAElL,QAAQ;MAAEhH,OAAO,EAAEqd,cAAc;MAAEpT,oBAAoB;MAAEkN;IAAQ,CAAC;EAC/E;EACAkH,qBAAqBA,CAACrX,QAAQ,EAAE8F,WAAW,EAAEjF,cAAc,EAAEF,eAAe,EAAEC,sBAAsB,EAAE;IAClG,IAAIxI,GAAG,GAAGtH,eAAe,CAAC,CACtB1D,oBAAoB,EACpB4S,QAAQ,CAAC8B,cAAc,EACvBjB,cAAc,EACdF,eAAe,EACfX,QAAQ,CAACW,eAAe,EACxBC,sBAAsB,CACzB,CAAC;IACF,IAAIiG,QAAQ,GAAGpQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1F,oBAAoB,CAAC,EAAEE,0BAA0B,CAAC,EAAEE,wBAAwB,CAAC,EAAEI,oBAAoB,CAAC,EAAEsU,WAAW,CAACxK,gBAAgB,CAAC,EAAEwK,WAAW,CAACvK,cAAc,CAAC;IACvQ,IAAIwQ,OAAO,GAAG,CAAC,CAAC;IAChB,IAAImL,UAAU,GAAG,IAAI,CAAC3D,uBAAuB;IAC7C,IAAI4D,cAAc,GAAG,IAAI,CAAC3D,yBAAyB;IACnD,IAAI4D,UAAU,GAAG,KAAK;IACtB,IAAIb,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI3T,UAAU,IAAIxK,GAAG,EAAE;MACxB,IAAIA,GAAG,CAACwK,UAAU,CAAC,KAAKsU,UAAU,CAACtU,UAAU,CAAC,IACzCtR,0BAA0B,CAACsR,UAAU,CAAC,IACnCtR,0BAA0B,CAACsR,UAAU,CAAC,CAACxK,GAAG,CAACwK,UAAU,CAAC,EAAEsU,UAAU,CAACtU,UAAU,CAAC,CAAE,EAAE;QACtFmJ,OAAO,CAACnJ,UAAU,CAAC,GAAGuU,cAAc,CAACvU,UAAU,CAAC;MACpD,CAAC,MACI;QACD,IAAIxK,GAAG,CAACwK,UAAU,CAAC,KAAK,IAAI,CAACyQ,2BAA2B,CAACzQ,UAAU,CAAC,IAC/DtR,0BAA0B,CAACsR,UAAU,CAAC,IACnCtR,0BAA0B,CAACsR,UAAU,CAAC,CAACxK,GAAG,CAACwK,UAAU,CAAC,EAAE,IAAI,CAACyQ,2BAA2B,CAACzQ,UAAU,CAAC,CAAE,EAAE;UAC5G,IAAIA,UAAU,IAAI,IAAI,CAAC0Q,6BAA6B,EAAE;YAAE;YACpDvH,OAAO,CAACnJ,UAAU,CAAC,GAAG,IAAI,CAAC0Q,6BAA6B,CAAC1Q,UAAU,CAAC;UACxE;QACJ,CAAC,MACI,IAAIiE,QAAQ,CAACjE,UAAU,CAAC,EAAE;UAC3BmJ,OAAO,CAACnJ,UAAU,CAAC,GAAGiE,QAAQ,CAACjE,UAAU,CAAC,CAACxK,GAAG,CAACwK,UAAU,CAAC,CAAC;QAC/D,CAAC,MACI;UACD2T,KAAK,CAAC3T,UAAU,CAAC,GAAGxK,GAAG,CAACwK,UAAU,CAAC;QACvC;QACAwU,UAAU,GAAG,IAAI;MACrB;IACJ;IACA,IAAIA,UAAU,EAAE;MACZ,IAAI,CAAC7D,uBAAuB,GAAGnb,GAAG;MAClC,IAAI,CAACob,yBAAyB,GAAGzH,OAAO;IAC5C;IACA,OAAO;MACH3M,UAAU,EAAE,IAAI,CAACmU,uBAAuB;MACxC8C,cAAc,EAAE,IAAI,CAAC7C,yBAAyB;MAC9C+C;IACJ,CAAC;EACL;AACJ;AACA,SAAS7D,cAAcA,CAAC/E,QAAQ,EAAE4K,cAAc,EAAE5B,qBAAqB,EAAEC,QAAQ,EAAE5gB,QAAQ,EAAE8P,WAAW,EAAEwQ,mBAAmB,EAAEpE,gBAAgB,EAAE;EAC7I,IAAIwE,MAAM,GAAG/e,WAAW,CAAC4gB,cAAc,IAAIjC,mBAAmB,CAACnf,WAAW,EAAEmf,mBAAmB,CAAC5e,GAAG,CAAC;EACpG,OAAO,IAAIhG,OAAO,CAAC;IACf8mB,cAAc,EAAE,SAAS;IACzB7K,QAAQ;IACR8K,iBAAiB,EAAE3S,WAAW,CAAC7K,kBAAkB;IACjDyb,MAAM;IACNC,qBAAqB;IACrBC,QAAQ;IACR5gB,QAAQ;IACR+E,YAAY,EAAE+K,WAAW,CAAC/K,YAAY;IACtCmX;EACJ,CAAC,CAAC;AACN;AACA,SAASS,UAAUA,CAAC3Z,OAAO,EAAE8M,WAAW,EAAE;EACtC,IAAI4S,UAAU,GAAG5S,WAAW,CAACjL,YAAY,CAAC7B,OAAO,CAAC2f,WAAW,CAAC,IAAIxb,aAAa;EAC/E,OAAO,IAAIub,UAAU,CAAC1f,OAAO,CAAC;AAClC;AACA,SAAS4Z,yBAAyBA,CAACN,KAAK,EAAE;EACtC,IAAIsG,yBAAyB,GAAGtG,KAAK,CAACgF,yBAAyB,IAAI1lB,oBAAoB;EACvF,OAAO,IAAIgnB,yBAAyB,CAACtG,KAAK,CAAC;AAC/C;AACA,SAASO,YAAYA,CAAC3T,IAAI,EAAE+L,cAAc,EAAEC,OAAO,EAAE;EACjD,OAAO,IAAIH,QAAQ,CAAC7L,IAAI,EAAE+L,cAAc,EAAEC,OAAO,CAAC;AACtD;AACA,SAAS6H,oBAAoBA,CAAChP,YAAY,EAAE;EACxC,OAAOzX,OAAO,CAACyX,YAAY,EAAGiB,WAAW,IAAKA,WAAW,CAAC6T,EAAE,CAAC;AACjE;AACA,SAAS7F,iBAAiBA,CAAC8F,SAAS,EAAExD,iBAAiB,EAAEC,eAAe,EAAE;EACtE,IAAIf,YAAY,GAAG;IAAE,EAAE,EAAEc;EAAkB,CAAC;EAC5C,KAAK,IAAIyD,KAAK,IAAID,SAAS,EAAE;IACzB,IAAI9c,GAAG,GAAG8c,SAAS,CAACC,KAAK,CAAC;IAC1B,IAAI/c,GAAG,CAACkI,QAAQ,IAAIqR,eAAe,CAACvZ,GAAG,CAACkI,QAAQ,CAAC,EAAE;MAC/CsQ,YAAY,CAACuE,KAAK,CAAC,GAAGxD,eAAe,CAACvZ,GAAG,CAACkI,QAAQ,CAAC;IACvD;EACJ;EACA,OAAOsQ,YAAY;AACvB;AACA,SAAS1B,gBAAgBA,CAACsB,eAAe,EAAE;EACvC,IAAI;IAAEpb;EAAQ,CAAC,GAAGob,eAAe;EACjC,OAAO;IACHkB,iBAAiB,EAAExjB,aAAa,CAAC;MAC7BknB,OAAO,EAAEhgB,OAAO,CAACigB,YAAY;MAC7BC,QAAQ,EAAElgB,OAAO,CAACkgB,QAAQ;MAC1BC,aAAa,EAAEngB,OAAO,CAACogB,kBAAkB;MACzCC,gBAAgB,EAAErgB,OAAO,CAACsgB,qBAAqB;MAC/CC,UAAU,EAAEvgB,OAAO,CAACwgB,eAAe;MACnCC,OAAO,EAAE,OAAOzgB,OAAO,CAAC0gB,YAAY,KAAK,SAAS,GAAG1gB,OAAO,CAAC0gB,YAAY,GAAGngB,SAAS;MACrFogB,KAAK,EAAE3gB,OAAO,CAAC4gB,UAAU;MACzBC,eAAe,EAAE7gB,OAAO,CAAC8gB,oBAAoB;MAC7CC,WAAW,EAAE/gB,OAAO,CAACghB,gBAAgB;MACrCC,SAAS,EAAEjhB,OAAO,CAACkhB,cAAc;MACjCC,KAAK,EAAEnhB,OAAO,CAACohB;MACf;IACJ,CAAC,EAAEhG,eAAe,CAAC;IACnBU,eAAe,EAAEhjB,aAAa,CAAC;MAC3BynB,UAAU,EAAEvgB,OAAO,CAACqhB,gBAAgB;MACpCZ,OAAO,EAAE,OAAOzgB,OAAO,CAACshB,aAAa,KAAK,SAAS,GAAGthB,OAAO,CAACshB,aAAa,GAAG/gB,SAAS;MACvFogB,KAAK,EAAE3gB,OAAO,CAACuhB;IACnB,CAAC,EAAEnG,eAAe;EACtB,CAAC;AACL;AACA,SAASa,gBAAgBA,CAACnN,KAAK,EAAEjI,OAAO,EAAE;EACtC,KAAK,IAAI2a,aAAa,IAAI3a,OAAO,CAACiG,WAAW,CAACpM,cAAc,EAAE;IAC1D,IAAI8gB,aAAa,CAAC1S,KAAK,CAAC,EAAE;MACtB,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASmL,yBAAyBA,CAACmB,eAAe,EAAE;EAChD,OAAOpiB,kBAAkB,CAACoiB,eAAe,CAACpb,OAAO,CAACub,aAAa,EAAEH,eAAe,CAAC;AACrF;AACA,SAASqC,kBAAkBA,CAACzd,OAAO,EAAEyhB,QAAQ,EAAE;EAC3C,KAAK,IAAI7X,UAAU,IAAI5J,OAAO,EAAE;IAC5BoD,OAAO,CAACC,IAAI,CAAE,mBAAkBuG,UAAW,GAAE,IACxC6X,QAAQ,GAAI,cAAaA,QAAS,GAAE,GAAG,EAAE,CAAC,CAAC;EACpD;AACJ;AAEA,MAAMC,cAAc,SAASluB,aAAa,CAAC;EACvCsI,MAAMA,CAAA,EAAG;IACL,IAAI6lB,QAAQ,GAAG,IAAI,CAACrI,KAAK,CAACsI,YAAY,CAACljB,GAAG,CAAEmjB,WAAW,IAAK,IAAI,CAACC,iBAAiB,CAACD,WAAW,CAAC,CAAC;IAChG,OAAOlmB,aAAa,CAAC,KAAK,EAAE;MAAEomB,SAAS,EAAE;IAAmB,CAAC,EAAE,GAAGJ,QAAQ,CAAC;EAC/E;EACAG,iBAAiBA,CAACD,WAAW,EAAE;IAC3B,IAAI;MAAEvI;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI;MAAE/J;IAAM,CAAC,GAAG,IAAI,CAAC1I,OAAO;IAC5B,IAAI8a,QAAQ,GAAG,EAAE;IACjB,IAAIK,aAAa,GAAG,IAAI;IACxB,KAAK,IAAIC,MAAM,IAAIJ,WAAW,EAAE;MAC5B,IAAI;QAAE7Q,UAAU;QAAEE,WAAW;QAAE5U,UAAU;QAAE6U,UAAU;QAAEjI;MAAW,CAAC,GAAG+Y,MAAM;MAC5E,IAAIjR,UAAU,KAAK,OAAO,EAAE;QACxBgR,aAAa,GAAG,KAAK;QACrBL,QAAQ,CAACxT,IAAI,CAACxS,aAAa,CAAC,IAAI,EAAE;UAAEomB,SAAS,EAAE,kBAAkB;UAAE5hB,EAAE,EAAEmZ,KAAK,CAAC4I;QAAQ,CAAC,EAAE5I,KAAK,CAAClH,KAAK,CAAC,CAAC;MACzG,CAAC,MACI;QACD,IAAI+P,SAAS,GAAGnR,UAAU,KAAKsI,KAAK,CAAC8I,YAAY;QACjD,IAAIC,UAAU,GAAI,CAAC/I,KAAK,CAACgJ,cAAc,IAAItR,UAAU,KAAK,OAAO,IAC5D,CAACsI,KAAK,CAACiJ,aAAa,IAAIvR,UAAU,KAAK,MAAO,IAC9C,CAACsI,KAAK,CAACkJ,aAAa,IAAIxR,UAAU,KAAK,MAAO;QACnD,IAAIyR,aAAa,GAAG,CAAE,MAAKzR,UAAW,SAAQ,EAAEzB,KAAK,CAACmT,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzE,IAAIP,SAAS,EAAE;UACXM,aAAa,CAACtU,IAAI,CAACoB,KAAK,CAACmT,QAAQ,CAAC,cAAc,CAAC,CAAC;QACtD;QACAf,QAAQ,CAACxT,IAAI,CAACxS,aAAa,CAAC,QAAQ,EAAE;UAAEuK,IAAI,EAAE,QAAQ;UAAEkM,KAAK,EAAE,OAAOlJ,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACoQ,KAAK,CAACxH,OAAO,CAAC,GAAG5I,UAAU;UAAEyZ,QAAQ,EAAEN,UAAU;UAAE,cAAc,EAAEF,SAAS;UAAEJ,SAAS,EAAEU,aAAa,CAAC9iB,IAAI,CAAC,GAAG,CAAC;UAAEijB,OAAO,EAAE1R;QAAY,CAAC,EAAE5U,UAAU,KAAK6U,UAAU,GAAGxV,aAAa,CAAC,MAAM,EAAE;UAAEomB,SAAS,EAAE5Q,UAAU;UAAE0R,IAAI,EAAE;QAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;MAC5V;IACJ;IACA,IAAIlB,QAAQ,CAACvjB,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI0kB,cAAc,GAAId,aAAa,IAAIzS,KAAK,CAACmT,QAAQ,CAAC,aAAa,CAAC,IAAK,EAAE;MAC3E,OAAO/mB,aAAa,CAAC,KAAK,EAAE;QAAEomB,SAAS,EAAEe;MAAe,CAAC,EAAE,GAAGnB,QAAQ,CAAC;IAC3E;IACA,OAAOA,QAAQ,CAAC,CAAC,CAAC;EACtB;AACJ;AAEA,MAAMoB,OAAO,SAASvvB,aAAa,CAAC;EAChCsI,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEknB,KAAK;MAAEC;IAAe,CAAC,GAAG,IAAI,CAAC3J,KAAK;IAC1C,IAAI4J,QAAQ,GAAG,KAAK;IACpB,IAAIC,YAAY;IAChB,IAAIC,UAAU;IACd,IAAIrT,cAAc,GAAGiT,KAAK,CAACjT,cAAc;IACzC,IAAIsT,aAAa,GAAGtT,cAAc,CAACuT,MAAM;IACzC,IAAIvT,cAAc,CAACwT,IAAI,EAAE;MACrBL,QAAQ,GAAG,IAAI;MACfC,YAAY,GAAGpT,cAAc,CAACwT,IAAI;IACtC,CAAC,MACI;MACDJ,YAAY,GAAGpT,cAAc,CAACzD,KAAK;IACvC;IACA,IAAIyD,cAAc,CAACyT,KAAK,EAAE;MACtBN,QAAQ,GAAG,IAAI;MACfE,UAAU,GAAGrT,cAAc,CAACyT,KAAK;IACrC,CAAC,MACI;MACDJ,UAAU,GAAGrT,cAAc,CAACxD,GAAG;IACnC;IACA,IAAIjF,UAAU,GAAG,CACb2b,cAAc,IAAI,EAAE,EACpB,YAAY,EACZC,QAAQ,GAAG,gBAAgB,GAAG,EAAE,CACnC;IACD,OAAQvnB,aAAa,CAAC,KAAK,EAAE;MAAEomB,SAAS,EAAEza,UAAU,CAAC3H,IAAI,CAAC,GAAG;IAAE,CAAC,EAC5D,IAAI,CAAC8jB,aAAa,CAAC,OAAO,EAAEN,YAAY,IAAI,EAAE,CAAC,EAC/C,IAAI,CAACM,aAAa,CAAC,QAAQ,EAAEJ,aAAa,IAAI,EAAE,CAAC,EACjD,IAAI,CAACI,aAAa,CAAC,KAAK,EAAEL,UAAU,IAAI,EAAE,CAAC,CAAC;EACpD;EACAK,aAAaA,CAACC,GAAG,EAAE9B,YAAY,EAAE;IAC7B,IAAI;MAAEtI;IAAM,CAAC,GAAG,IAAI;IACpB,OAAQ3d,aAAa,CAAC+lB,cAAc,EAAE;MAAEgC,GAAG,EAAEA,GAAG;MAAE9B,YAAY,EAAEA,YAAY;MAAExP,KAAK,EAAEkH,KAAK,CAAClH,KAAK;MAAEN,OAAO,EAAEwH,KAAK,CAACxH,OAAO;MAAEsQ,YAAY,EAAE9I,KAAK,CAAC8I,YAAY;MAAEE,cAAc,EAAEhJ,KAAK,CAACgJ,cAAc;MAAEC,aAAa,EAAEjJ,KAAK,CAACiJ,aAAa;MAAEC,aAAa,EAAElJ,KAAK,CAACkJ,aAAa;MAAEN,OAAO,EAAE5I,KAAK,CAAC4I;IAAQ,CAAC,CAAC;EACvS;AACJ;AAEA,MAAMyB,WAAW,SAASnwB,aAAa,CAAC;EACpCwe,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG4R,SAAS,CAAC;IACnB,IAAI,CAAC9U,KAAK,GAAG;MACT+U,cAAc,EAAE;IACpB,CAAC;IACD,IAAI,CAACC,QAAQ,GAAIC,EAAE,IAAK;MACpB,IAAI,CAACA,EAAE,GAAGA,EAAE;MACZ7qB,MAAM,CAAC,IAAI,CAACogB,KAAK,CAAC0K,KAAK,EAAED,EAAE,CAAC;MAC5B,IAAI,CAACE,oBAAoB,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,CAACC,YAAY,GAAG,MAAM;MACtB,IAAI,CAACD,oBAAoB,CAAC,CAAC;IAC/B,CAAC;EACL;EACAnoB,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEwd,KAAK;MAAExK;IAAM,CAAC,GAAG,IAAI;IAC3B,IAAI;MAAEqV;IAAY,CAAC,GAAG7K,KAAK;IAC3B,IAAIhS,UAAU,GAAG,CACb,iBAAiB,EAChB6c,WAAW,IAAI7K,KAAK,CAAC8K,MAAM,IAAI9K,KAAK,CAAC+K,MAAM,GACtC,wBAAwB,CAAC;IAAA,EACzB,yBAAyB,CAAE;IAAA,CACpC;;IACD,IAAIA,MAAM,GAAG,EAAE;IACf,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIH,WAAW,EAAE;MACb,IAAIrV,KAAK,CAAC+U,cAAc,KAAK,IAAI,EAAE;QAC/BQ,MAAM,GAAGvV,KAAK,CAAC+U,cAAc,GAAGM,WAAW;MAC/C,CAAC,MACI;QACD;QACA;QACA;QACA;QACAG,aAAa,GAAI,GAAG,CAAC,GAAGH,WAAW,GAAI,GAAI,GAAE;MACjD;IACJ,CAAC,MACI;MACDE,MAAM,GAAG/K,KAAK,CAAC+K,MAAM,IAAI,EAAE;IAC/B;IACA,OAAQ1oB,aAAa,CAAC,KAAK,EAAE;MAAE,iBAAiB,EAAE2d,KAAK,CAACiL,WAAW;MAAEC,GAAG,EAAE,IAAI,CAACV,QAAQ;MAAE/B,SAAS,EAAEza,UAAU,CAAC3H,IAAI,CAAC,GAAG,CAAC;MAAE8kB,KAAK,EAAE;QAAEJ,MAAM;QAAEC;MAAc;IAAE,CAAC,EAAEhL,KAAK,CAACqI,QAAQ,CAAC;EACjL;EACA+C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7d,OAAO,CAAC8d,gBAAgB,CAAC,IAAI,CAACT,YAAY,CAAC;EACpD;EACAU,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC/d,OAAO,CAACge,mBAAmB,CAAC,IAAI,CAACX,YAAY,CAAC;EACvD;EACAD,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACF,EAAE;IAAI;IACX,IAAI,CAACzK,KAAK,CAAC6K,WAAW,CAAC;IAAA,EACzB;MACE,IAAI,CAACW,QAAQ,CAAC;QAAEjB,cAAc,EAAE,IAAI,CAACE,EAAE,CAACgB;MAAY,CAAC,CAAC;IAC1D;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,aAAa,SAAS5rB,WAAW,CAAC;EACpC4Y,WAAWA,CAACiT,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf,IAAI,CAACC,cAAc,GAAG,CAAC9T,EAAE,EAAE+T,KAAK,KAAK;MACjC,IAAI;QAAElf;MAAU,CAAC,GAAG,IAAI;MACxB,IAAI;QAAEY;MAAQ,CAAC,GAAGZ,SAAS;MAC3B,IAAImf,GAAG,GAAG9rB,QAAQ,CAAC6rB,KAAK,CAAC;MACzB,IAAIC,GAAG;MAAI;MACPnf,SAAS,CAACof,gBAAgB,CAACjU,EAAE,CAACE,MAAM,CAAC,EAAE;QACvC;QACA;QACA,IAAIgU,eAAe,GAAG9rB,cAAc,CAAC4X,EAAE,CAACE,MAAM,EAAE,sBAAsB,CAAC;QACvE,IAAI2C,GAAG,GAAGqR,eAAe,GAAGA,eAAe,CAACC,aAAa,CAAC,SAAS,CAAC,CAACC,IAAI,GAAG,EAAE;QAC9E3e,OAAO,CAACmQ,OAAO,CAACC,OAAO,CAAC,YAAY,EAAE;UAClC8M,EAAE,EAAEoB,KAAK;UACTM,KAAK,EAAE,IAAI/rB,SAAS,CAACuM,SAAS,CAACY,OAAO,EAAEue,GAAG,CAACM,UAAU,CAAC1iB,GAAG,EAAEoiB,GAAG,CAACM,UAAU,CAACC,QAAQ,CAAC;UACpFC,OAAO,EAAExU,EAAE;UACX8F,IAAI,EAAErQ,OAAO,CAACsQ;QAClB,CAAC,CAAC;QACF,IAAIlD,GAAG,IAAI,CAAC7C,EAAE,CAACyU,gBAAgB,EAAE;UAC7BC,MAAM,CAACC,QAAQ,CAACP,IAAI,GAAGvR,GAAG;QAC9B;MACJ;IACJ,CAAC;IACD,IAAI,CAAC+R,OAAO,GAAGpsB,gBAAgB,CAACqrB,QAAQ,CAAClB,EAAE,EAAE,OAAO,EAAE,WAAW;IAAE;IACnE,IAAI,CAACmB,cAAc,CAAC;EACxB;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMe,aAAa,SAAS7sB,WAAW,CAAC;EACpC4Y,WAAWA,CAACiT,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf;IACA,IAAI,CAACiB,mBAAmB,GAAInC,EAAE,IAAK;MAC/B,IAAIA,EAAE,KAAK,IAAI,CAACoC,YAAY,EAAE;QAC1B,IAAI,CAACC,cAAc,CAAC,IAAI,EAAE,IAAI,CAACD,YAAY,CAAC;MAChD;IACJ,CAAC;IACD,IAAI,CAACE,cAAc,GAAG,CAACjV,EAAE,EAAE+T,KAAK,KAAK;MACjC,IAAI7rB,QAAQ,CAAC6rB,KAAK,CAAC,EAAE;QAAE;QACnB,IAAI,CAACgB,YAAY,GAAGhB,KAAK;QACzB,IAAI,CAACmB,YAAY,CAAC,iBAAiB,EAAElV,EAAE,EAAE+T,KAAK,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAACiB,cAAc,GAAG,CAAChV,EAAE,EAAE+T,KAAK,KAAK;MACjC,IAAI,IAAI,CAACgB,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,GAAG,IAAI;QACxB,IAAI,CAACG,YAAY,CAAC,iBAAiB,EAAElV,EAAE,EAAE+T,KAAK,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAACoB,oBAAoB,GAAGzsB,uBAAuB,CAACmrB,QAAQ,CAAClB,EAAE,EAAE,WAAW;IAAE;IAC9E,IAAI,CAACsC,cAAc,EAAE,IAAI,CAACD,cAAc,CAAC;EAC7C;EACAJ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACO,oBAAoB,CAAC,CAAC;EAC/B;EACAD,YAAYA,CAACE,YAAY,EAAEpV,EAAE,EAAE+T,KAAK,EAAE;IAClC,IAAI;MAAElf;IAAU,CAAC,GAAG,IAAI;IACxB,IAAI;MAAEY;IAAQ,CAAC,GAAGZ,SAAS;IAC3B,IAAImf,GAAG,GAAG9rB,QAAQ,CAAC6rB,KAAK,CAAC;IACzB,IAAI,CAAC/T,EAAE,IAAInL,SAAS,CAACof,gBAAgB,CAACjU,EAAE,CAACE,MAAM,CAAC,EAAE;MAC9CzK,OAAO,CAACmQ,OAAO,CAACC,OAAO,CAACuP,YAAY,EAAE;QAClCzC,EAAE,EAAEoB,KAAK;QACTM,KAAK,EAAE,IAAI/rB,SAAS,CAACmN,OAAO,EAAEue,GAAG,CAACM,UAAU,CAAC1iB,GAAG,EAAEoiB,GAAG,CAACM,UAAU,CAACC,QAAQ,CAAC;QAC1EC,OAAO,EAAExU,EAAE;QACX8F,IAAI,EAAErQ,OAAO,CAACsQ;MAClB,CAAC,CAAC;IACN;EACJ;AACJ;AAEA,MAAMsP,eAAe,SAASzsB,aAAa,CAAC;EACxCgY,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG4R,SAAS,CAAC;IACnB,IAAI,CAAC1pB,gBAAgB,GAAGxD,OAAO,CAACwD,gBAAgB,CAAC;IACjD,IAAI,CAACwsB,yBAAyB,GAAGhwB,OAAO,CAACgwB,yBAAyB,CAAC;IACnE,IAAI,CAACC,iBAAiB,GAAGjwB,OAAO,CAACiwB,iBAAiB,CAAC;IACnD,IAAI,CAACC,SAAS,GAAGhrB,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACirB,SAAS,GAAGjrB,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACkrB,iBAAiB,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAAChY,KAAK,GAAG;MACTiY,WAAW,EAAE3sB,cAAc,CAAC;IAChC,CAAC;IACD;IACA;IACA,IAAI,CAAC4sB,4BAA4B,GAAG,CAAC/gB,SAAS,EAAEghB,aAAa,KAAK;MAC9D,IAAIhC,QAAQ,GAAG3qB,wBAAwB,CAAC2L,SAAS,EAAEghB,aAAa,CAAC;MACjE,IAAIC,oBAAoB,GAAG,CACvBlC,aAAa,EACbiB,aAAa,CAChB;MACD,IAAIkB,kBAAkB,GAAGD,oBAAoB,CAAC5oB,MAAM,CAAC,IAAI,CAACgb,KAAK,CAACxM,WAAW,CAACnL,qBAAqB,CAAC;MAClG,IAAIylB,YAAY,GAAGD,kBAAkB,CAACzoB,GAAG,CAAE2oB,mBAAmB,IAAK,IAAIA,mBAAmB,CAACpC,QAAQ,CAAC,CAAC;MACrG,IAAI,CAAC6B,iBAAiB,CAAC7gB,SAAS,CAACqhB,GAAG,CAAC,GAAGF,YAAY;MACpD5sB,wBAAwB,CAACyL,SAAS,CAACqhB,GAAG,CAAC,GAAGrC,QAAQ;IACtD,CAAC;IACD,IAAI,CAACsC,8BAA8B,GAAIthB,SAAS,IAAK;MACjD,IAAIuhB,SAAS,GAAG,IAAI,CAACV,iBAAiB,CAAC7gB,SAAS,CAACqhB,GAAG,CAAC;MACrD,IAAIE,SAAS,EAAE;QACX,KAAK,IAAIC,QAAQ,IAAID,SAAS,EAAE;UAC5BC,QAAQ,CAACzB,OAAO,CAAC,CAAC;QACtB;QACA,OAAO,IAAI,CAACc,iBAAiB,CAAC7gB,SAAS,CAACqhB,GAAG,CAAC;MAChD;MACA,OAAO9sB,wBAAwB,CAACyL,SAAS,CAACqhB,GAAG,CAAC;IAClD,CAAC;IACD;IACA;IACA,IAAI,CAACI,YAAY,GAAG,IAAItxB,aAAa,CAAC,MAAM;MACxC,IAAI,CAACkjB,KAAK,CAACtC,OAAO,CAACC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACqC,KAAK,CAACtC,OAAO,CAACC,OAAO,CAAC,cAAc,EAAE;QAAEC,IAAI,EAAE,IAAI,CAACoC,KAAK,CAACnC;MAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC;IACF,IAAI,CAACwQ,kBAAkB,GAAIvW,EAAE,IAAK;MAC9B,IAAI;QAAEpR;MAAQ,CAAC,GAAG,IAAI,CAACsZ,KAAK;MAC5B,IAAItZ,OAAO,CAAC2nB,kBAAkB,IAC1BvW,EAAE,CAACE,MAAM,KAAKwU,MAAM,CAAC;MAAA,EACvB;QACE,IAAI,CAAC4B,YAAY,CAAC5P,OAAO,CAAC9X,OAAO,CAAC4nB,iBAAiB,CAAC;MACxD;IACJ,CAAC;EACL;EACA;AACJ;AACA;EACI9rB,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEwd;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI;MAAEwE,aAAa;MAAE9d;IAAQ,CAAC,GAAGsZ,KAAK;IACtC,IAAIuO,YAAY,GAAG,IAAI,CAAClB,iBAAiB,CAACrN,KAAK,CAACtS,QAAQ,EAAEsS,KAAK,CAAC5O,WAAW,EAAE4O,KAAK,CAACrP,oBAAoB,EAAEqP,KAAK,CAACtP,WAAW,EAAEtP,MAAM,CAAC4e,KAAK,CAACtZ,OAAO,CAACif,GAAG,EAAE3F,KAAK,CAACpH,OAAO,CAAC;IAAE;IACtKoH,KAAK,CAACjH,SAAS,CAAC;IAChB,IAAIyV,SAAS,GAAG,KAAK;IACrB,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,eAAe;IACnB,IAAI1O,KAAK,CAAC2O,YAAY,IAAI3O,KAAK,CAAC4O,QAAQ,EAAE;MACtCH,UAAU,GAAG,EAAE;IACnB,CAAC,MACI,IAAI/nB,OAAO,CAACqkB,MAAM,IAAI,IAAI,EAAE;MAC7ByD,SAAS,GAAG,IAAI;IACpB,CAAC,MACI,IAAI9nB,OAAO,CAACmoB,aAAa,IAAI,IAAI,EAAE;MACpCJ,UAAU,GAAG/nB,OAAO,CAACmoB,aAAa;IACtC,CAAC,MACI;MACDH,eAAe,GAAGhkB,IAAI,CAACC,GAAG,CAACjE,OAAO,CAACmkB,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1D;;IACA,IAAIiE,WAAW,GAAG,IAAI,CAACluB,gBAAgB,CAACof,KAAK,CAACtS,QAAQ,EAAEsS,KAAK,CAACnC,OAAO,EAAEmC,KAAK,CAACtZ,OAAO,EAAEsZ,KAAK,CAACrP,oBAAoB,EAAEqP,KAAK,CAACpH,OAAO,EAAEoH,KAAK,CAAC/J,KAAK,EAAE+J,KAAK,CAACxM,WAAW,EAAEwM,KAAK,CAAC/L,QAAQ,EAAE+L,KAAK,CAACrH,cAAc,EAAEqH,KAAK,CAACtC,OAAO,EAAEsC,KAAK,CAAC1M,WAAW,EAAE,IAAI,CAACoa,4BAA4B,EAAE,IAAI,CAACO,8BAA8B,CAAC;IAChT,IAAIR,WAAW,GAAIjJ,aAAa,CAACrO,MAAM,IAAIqO,aAAa,CAACrO,MAAM,CAACQ,QAAQ,GAClE,IAAI,CAACnB,KAAK,CAACiY,WAAW,GACtB,EAAE;IACR,OAAQprB,aAAa,CAACjI,eAAe,CAAC20B,QAAQ,EAAE;MAAE9f,KAAK,EAAE6f;IAAY,CAAC,EAClEtK,aAAa,CAACrO,MAAM,IAAK9T,aAAa,CAAConB,OAAO,EAAEtlB,MAAM,CAACC,MAAM,CAAC;MAAE8mB,GAAG,EAAE,IAAI,CAACoC,SAAS;MAAE3D,cAAc,EAAE,mBAAmB;MAAED,KAAK,EAAElF,aAAa,CAACrO,MAAM;MAAEyS,OAAO,EAAE6E;IAAY,CAAC,EAAEc,YAAY,CAAC,CAAE,EAC9LlsB,aAAa,CAACgoB,WAAW,EAAE;MAAES,MAAM,EAAE0D,SAAS;MAAEzD,MAAM,EAAE0D,UAAU;MAAE5D,WAAW,EAAE6D,eAAe;MAAEzD,WAAW,EAAEwC;IAAY,CAAC,EACxH,IAAI,CAACuB,UAAU,CAAChP,KAAK,CAAC,EACtB,IAAI,CAACiP,kBAAkB,CAAC,CAAC,CAAC,EAC9BzK,aAAa,CAAClO,MAAM,IAAKjU,aAAa,CAAConB,OAAO,EAAEtlB,MAAM,CAACC,MAAM,CAAC;MAAE8mB,GAAG,EAAE,IAAI,CAACqC,SAAS;MAAE5D,cAAc,EAAE,mBAAmB;MAAED,KAAK,EAAElF,aAAa,CAAClO,MAAM;MAAEsS,OAAO,EAAE;IAAG,CAAC,EAAE2F,YAAY,CAAC,CAAE,CAAC;EAC9L;EACAnD,iBAAiBA,CAAA,EAAG;IAChB,IAAI;MAAEpL;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI,CAAC1X,oBAAoB,GAAG0X,KAAK,CAACxM,WAAW,CAAClL,oBAAoB,CAC7DlD,GAAG,CAAE8pB,wBAAwB,IAAK,IAAIA,wBAAwB,CAAClP,KAAK,CAAC,CAAC;IAC3EwM,MAAM,CAAC2C,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACd,kBAAkB,CAAC;IAC1D,IAAI;MAAEnlB;IAAgB,CAAC,GAAG8W,KAAK,CAACxM,WAAW;IAC3C,KAAK,IAAI4b,QAAQ,IAAIlmB,eAAe,EAAE;MAClCA,eAAe,CAACkmB,QAAQ,CAAC,CAACpP,KAAK,CAACoP,QAAQ,CAAC,EAAEpP,KAAK,CAAC;IACrD;EACJ;EACAqP,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,IAAI;MAAEtP;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI;MAAE9W;IAAgB,CAAC,GAAG8W,KAAK,CAACxM,WAAW;IAC3C,KAAK,IAAI4b,QAAQ,IAAIlmB,eAAe,EAAE;MAClC,IAAI8W,KAAK,CAACoP,QAAQ,CAAC,KAAKE,SAAS,CAACF,QAAQ,CAAC,EAAE;QACzClmB,eAAe,CAACkmB,QAAQ,CAAC,CAACpP,KAAK,CAACoP,QAAQ,CAAC,EAAEpP,KAAK,CAAC;MACrD;IACJ;EACJ;EACAsL,oBAAoBA,CAAA,EAAG;IACnBkB,MAAM,CAAC+C,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClB,kBAAkB,CAAC;IAC7D,IAAI,CAACD,YAAY,CAACoB,KAAK,CAAC,CAAC;IACzB,KAAK,IAAIC,WAAW,IAAI,IAAI,CAACnnB,oBAAoB,EAAE;MAC/CmnB,WAAW,CAAC/C,OAAO,CAAC,CAAC;IACzB;IACA,IAAI,CAAC1M,KAAK,CAACtC,OAAO,CAACC,OAAO,CAAC,UAAU,CAAC;EAC1C;EACAsR,kBAAkBA,CAAA,EAAG;IACjB,IAAI;MAAEjP;IAAM,CAAC,GAAG,IAAI;IACpB,IAAIqI,QAAQ,GAAGrI,KAAK,CAACxM,WAAW,CAACrL,oBAAoB,CAAC/C,GAAG,CAAE6pB,kBAAkB,IAAKA,kBAAkB,CAACjP,KAAK,CAAC,CAAC;IAC5G,OAAO3d,aAAa,CAACE,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG8lB,QAAQ,CAAC;EACnD;EACA2G,UAAUA,CAAChP,KAAK,EAAE;IACd,IAAI;MAAExM;IAAY,CAAC,GAAGwM,KAAK;IAC3B,IAAI;MAAEtS;IAAS,CAAC,GAAGsS,KAAK;IACxB,IAAI3S,SAAS,GAAG;MACZ+D,WAAW,EAAE4O,KAAK,CAAC5O,WAAW;MAC9B6Q,aAAa,EAAEjC,KAAK,CAACiC,aAAa;MAClClE,UAAU,EAAEiC,KAAK,CAACmC,oBAAoB;MACtCD,YAAY,EAAElC,KAAK,CAACkC,YAAY;MAChCE,aAAa,EAAEpC,KAAK,CAACoC,aAAa;MAClCC,cAAc,EAAErC,KAAK,CAACqC,cAAc;MACpCC,SAAS,EAAEtC,KAAK,CAACsC,SAAS;MAC1BC,WAAW,EAAEvC,KAAK,CAACuC,WAAW;MAC9BoM,YAAY,EAAE3O,KAAK,CAAC2O,YAAY;MAChCC,QAAQ,EAAE5O,KAAK,CAAC4O;IACpB,CAAC;IACD,IAAIc,YAAY,GAAG,IAAI,CAACtC,yBAAyB,CAAC5Z,WAAW,CAACxL,qBAAqB,CAAC;IACpF,KAAK,IAAI2nB,WAAW,IAAID,YAAY,EAAE;MAClCvrB,MAAM,CAACC,MAAM,CAACiJ,SAAS,EAAEsiB,WAAW,CAACC,SAAS,CAACviB,SAAS,EAAE2S,KAAK,CAAC,CAAC;IACrE;IACA,IAAI6P,aAAa,GAAGniB,QAAQ,CAACf,SAAS;IACtC,OAAQtK,aAAa,CAACwtB,aAAa,EAAE1rB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiJ,SAAS,CAAC,CAAC;EACtE;AACJ;AACA,SAASggB,iBAAiBA,CAAC3f,QAAQ,EAAE0D,WAAW,EAAET,oBAAoB,EAAED,WAAW,EAAEiV,GAAG,EAAE7M,KAAK,EAAE;EAC7F;EACA,IAAIgX,SAAS,GAAGnf,oBAAoB,CAACE,KAAK,CAAC8U,GAAG,EAAE1e,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;EACnE,IAAI8oB,QAAQ,GAAGpf,oBAAoB,CAACI,SAAS,CAACK,WAAW,EAAEV,WAAW,EAAE,KAAK,CAAC;EAC9E,IAAIsf,QAAQ,GAAGrf,oBAAoB,CAACM,SAAS,CAACG,WAAW,EAAEV,WAAW,EAAE,KAAK,CAAC;EAC9E,OAAO;IACHoI,KAAK;IACLgQ,YAAY,EAAEpb,QAAQ,CAACd,IAAI;IAC3B4L,OAAO,EAAE9K,QAAQ,CAACmB,UAAU;IAC5Bma,cAAc,EAAE8G,SAAS,CAAC9e,OAAO,IAAI,CAAClT,mBAAmB,CAACsT,WAAW,CAACgI,YAAY,EAAEuM,GAAG,CAAC;IACxFsD,aAAa,EAAE8G,QAAQ,CAAC/e,OAAO;IAC/BkY,aAAa,EAAE8G,QAAQ,CAAChf;EAC5B,CAAC;AACL;AACA;AACA;AACA,SAASoc,yBAAyBA,CAAC6C,UAAU,EAAE;EAC3C,OAAOA,UAAU,CAAC7qB,GAAG,CAAE8qB,QAAQ,IAAK,IAAIA,QAAQ,CAAC,CAAC,CAAC;AACvD;AAEA,MAAMC,QAAQ,SAAS7uB,YAAY,CAAC;EAChCoX,WAAWA,CAAC+R,EAAE,EAAEpc,eAAe,GAAG,CAAC,CAAC,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAAC+hB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAIpgB,MAAM,IAAK;MAC5B;MACA,QAAQA,MAAM,CAACxD,IAAI;QACf,KAAK,gBAAgB;QACrB,KAAK,kBAAkB;UACnB,IAAI,CAAC6jB,YAAY,CAACC,QAAQ,CAAC,CAAC;MACpC;IACJ,CAAC;IACD,IAAI,CAACC,UAAU,GAAIrP,IAAI,IAAK;MACxB,IAAI,CAACsP,WAAW,GAAGtP,IAAI;MACvB,IAAI,CAACmP,YAAY,CAACjS,OAAO,CAAC8C,IAAI,CAACnQ,eAAe,CAAC0f,aAAa,CAAC;IACjE,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,MAAM;MAC7B,IAAI,IAAI,CAACV,WAAW,EAAE;QAClB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtB,IAAI;UAAEO;QAAY,CAAC,GAAG,IAAI;QAC1BpvB,SAAS,CAAC,MAAM;UACZgB,MAAM,CAACH,aAAa,CAACX,YAAY,EAAE;YAAEgF,OAAO,EAAEkqB,WAAW,CAACzf,eAAe;YAAE8E,KAAK,EAAE2a,WAAW,CAAC3a,KAAK;YAAEyH,OAAO,EAAEkT,WAAW,CAAClT;UAAQ,CAAC,EAAE,CAAC1P,UAAU,EAAE+c,MAAM,EAAE4D,YAAY,EAAEC,QAAQ,KAAK;YACjL,IAAI,CAACmC,aAAa,CAAC/iB,UAAU,CAAC;YAC9B,IAAI,CAACgjB,SAAS,CAACjG,MAAM,CAAC;YACtB,OAAQ1oB,aAAa,CAACT,QAAQ,CAACmtB,QAAQ,EAAE;cAAE9f,KAAK,EAAE,IAAI,CAACshB;YAAsB,CAAC,EAC1EluB,aAAa,CAAC8qB,eAAe,EAAEhpB,MAAM,CAACC,MAAM,CAAC;cAAEuqB,YAAY,EAAEA,YAAY;cAAEC,QAAQ,EAAEA;YAAS,CAAC,EAAEgC,WAAW,CAAC,CAAC,CAAC;UACvH,CAAC,CAAC,EAAE,IAAI,CAACnG,EAAE,CAAC;QAChB,CAAC,CAAC;MACN,CAAC,MACI,IAAI,IAAI,CAAC4F,UAAU,EAAE;QACtB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB7tB,MAAM,CAAC,IAAI,EAAE,IAAI,CAACioB,EAAE,CAAC;QACrB,IAAI,CAACsG,aAAa,CAAC,EAAE,CAAC;QACtB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC;IACDlvB,iBAAiB,CAAC2oB,EAAE,CAAC;IACrB,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACgG,YAAY,GAAG,IAAI3zB,aAAa,CAAC,IAAI,CAACg0B,mBAAmB,CAAC;IAC/D,IAAI/Q,mBAAmB,CAAC;MACpB1R,eAAe;MACfiF,WAAW,EAAE,IAAI;MACjB+P,QAAQ,EAAE,IAAI,CAACmN,YAAY;MAC3B7M,MAAM,EAAE,IAAI,CAACgN;IACjB,CAAC,CAAC;EACN;EACAnuB,MAAMA,CAAA,EAAG;IACL,IAAIyuB,YAAY,GAAG,IAAI,CAACb,WAAW;IACnC,IAAI,CAACa,YAAY,EAAE;MACf,IAAI,CAACb,WAAW,GAAG,IAAI;IAC3B,CAAC,MACI;MACD,IAAI,CAACG,qBAAqB,IAAI,CAAC;IACnC;IACA,IAAI,CAACE,YAAY,CAACjS,OAAO,CAAC,CAAC;IAC3B,IAAIyS,YAAY,EAAE;MACd,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;EACJ;EACAxE,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC0D,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,KAAK;MACxB,IAAI,CAACK,YAAY,CAACjS,OAAO,CAAC,CAAC;IAC/B;EACJ;EACA0S,UAAUA,CAAA,EAAG;IACT1vB,SAAS,CAAC,MAAM;MACZ,KAAK,CAAC0vB,UAAU,CAAC,CAAC;IACtB,CAAC,CAAC;EACN;EACAC,cAAcA,CAACnX,IAAI,EAAE;IACjB,IAAI,CAACyW,YAAY,CAAC9R,KAAK,CAAC,gBAAgB,CAAC;IACzC3E,IAAI,CAAC,CAAC;IACN,IAAI,CAACyW,YAAY,CAAC5R,MAAM,CAAC,gBAAgB,CAAC;EAC9C;EACAuS,cAAcA,CAAA,EAAG;IACb,IAAI,CAACX,YAAY,CAAC9R,KAAK,CAAC,gBAAgB,CAAC;EAC7C;EACA0S,eAAeA,CAAA,EAAG;IACd,IAAI,CAACZ,YAAY,CAAC5R,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;EACpD;EACA+D,YAAYA,CAACvU,eAAe,EAAEwU,kBAAkB,EAAE;IAC9C,IAAI,CAAClB,kBAAkB,CAACiB,YAAY,CAACvU,eAAe,EAAEwU,kBAAkB,CAAC;EAC7E;EACAkO,aAAaA,CAAC/iB,UAAU,EAAE;IACtB,IAAI,CAACpU,aAAa,CAACoU,UAAU,EAAE,IAAI,CAACsiB,iBAAiB,CAAC,EAAE;MACpD,IAAI;QAAEgB;MAAU,CAAC,GAAG,IAAI,CAAC7G,EAAE;MAC3B,KAAK,IAAIhC,SAAS,IAAI,IAAI,CAAC6H,iBAAiB,EAAE;QAC1CgB,SAAS,CAACC,MAAM,CAAC9I,SAAS,CAAC;MAC/B;MACA,KAAK,IAAIA,SAAS,IAAIza,UAAU,EAAE;QAC9BsjB,SAAS,CAACxU,GAAG,CAAC2L,SAAS,CAAC;MAC5B;MACA,IAAI,CAAC6H,iBAAiB,GAAGtiB,UAAU;IACvC;EACJ;EACAgjB,SAASA,CAACjG,MAAM,EAAE;IACd/oB,cAAc,CAAC,IAAI,CAACyoB,EAAE,EAAE,QAAQ,EAAEM,MAAM,CAAC;EAC7C;AACJ;AAEA,SAASyG,UAAUA,CAACC,SAAS,EAAE/qB,OAAO,GAAG,CAAC,CAAC,EAAE;EACzC,IAAIkS,OAAO,GAAGuH,YAAY,CAACzZ,OAAO,CAAC;EACnC,IAAIgrB,SAAS,GAAG10B,eAAe,CAAC0J,OAAO,CAAC;EACxC,IAAIirB,QAAQ,GAAG/Y,OAAO,CAACgZ,gBAAgB,CAACH,SAAS,CAAC;EAClD,IAAI,CAACE,QAAQ,EAAE;IAAE;IACb,OAAO,EAAE;EACb;EACA,OAAO/Y,OAAO,CAACgC,MAAM,CAAC+W,QAAQ,CAACE,MAAM,EAAEH,SAAS,EAAE;IAC9CI,SAAS,EAAEH,QAAQ,CAACG;EACxB,CAAC,CAAC;AACN;AACA,SAASvS,WAAWA,CAACwS,UAAU,EAAEC,QAAQ,EAAEtrB,OAAO,EAAE;EAChD,IAAIkS,OAAO,GAAGuH,YAAY,CAAC,OAAOzZ,OAAO,KAAK,QAAQ,IAAIA,OAAO,GAAGA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACnF,IAAIgrB,SAAS,GAAG10B,eAAe,CAAC0J,OAAO,CAAC;EACxC,IAAIurB,SAAS,GAAGrZ,OAAO,CAACgZ,gBAAgB,CAACG,UAAU,CAAC;EACpD,IAAIG,OAAO,GAAGtZ,OAAO,CAACgZ,gBAAgB,CAACI,QAAQ,CAAC;EAChD,IAAI,CAACC,SAAS,IAAI,CAACC,OAAO,EAAE;IAAE;IAC1B,OAAO,EAAE;EACb;EACA,OAAOtZ,OAAO,CAAC2G,WAAW,CAAC0S,SAAS,CAACJ,MAAM,EAAEK,OAAO,CAACL,MAAM,EAAEH,SAAS,EAAE;IACpES,cAAc,EAAEF,SAAS,CAACH,SAAS;IACnCM,YAAY,EAAEF,OAAO,CAACJ,SAAS;IAC/BpS,cAAc,EAAEhZ,OAAO,CAACgZ,cAAc;IACtCE,gBAAgB,EAAE9kB,oBAAoB,CAACypB;EAC3C,CAAC,CAAC;AACN;AACA;AACA,SAASpE,YAAYA,CAACwL,QAAQ,EAAE;EAC5B,IAAIvH,MAAM,GAAG/e,WAAW,CAACsmB,QAAQ,CAACvH,MAAM,IAAI,IAAI,EAAEzf,kBAAkB,CAAC,EAAE,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC;EAC/E,OAAO,IAAIhG,OAAO,CAAC+E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAAEiX,QAAQ,EAAEvgB,oBAAoB,CAACugB,QAAQ;IAAE6K,cAAc,EAAE;EAAU,CAAC,EAAEyF,QAAQ,CAAC,EAAE;IAAEvH;EAAO,CAAC,CAAC,CAAC;AAClJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASiO,WAAWA,CAACrS,KAAK,EAAEsS,MAAM,EAAE;EAChC,OAAOpwB,eAAe,CAAC8d,KAAK,CAACjC,UAAU,EAAEiC,KAAK,CAACkC,YAAY,EAAElC,KAAK,CAAC5O,WAAW,CAACC,WAAW,EAAEihB,MAAM,GAAGtS,KAAK,CAACpS,gBAAgB,GAAG,IAAI,CAAC,CAAC2kB,EAAE;AAC1I;AAEA,MAAMC,OAAO,GAAG,QAAQ;AAExB,SAASrC,QAAQ,EAAExpB,YAAY,EAAE6qB,UAAU,EAAEjS,WAAW,EAAE9c,aAAa,EAAEwb,aAAa,EAAEoU,WAAW,EAAEG,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
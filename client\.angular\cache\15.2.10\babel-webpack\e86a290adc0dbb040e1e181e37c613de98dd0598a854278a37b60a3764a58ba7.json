{"ast": null, "code": "import { ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, NgZone, Injector } from '@angular/core';\nimport { AuthInstances } from '@angular/fire/auth';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { collectionChanges as collectionChanges$1, collection as collection$1, sortedChanges as sortedChanges$1, auditTrail as auditTrail$1, collectionData as collectionData$1, doc as doc$1, docData as docData$1, snapToData as snapToData$1, fromRef as fromRef$1 } from 'rxfire/firestore';\nimport { addDoc as addDoc$1, arrayRemove as arrayRemove$1, arrayUnion as arrayUnion$1, clearIndexedDbPersistence as clearIndexedDbPersistence$1, collection as collection$2, collectionGroup as collectionGroup$1, connectFirestoreEmulator as connectFirestoreEmulator$1, deleteDoc as deleteDoc$1, deleteField as deleteField$1, disableNetwork as disableNetwork$1, doc as doc$2, documentId as documentId$1, enableIndexedDbPersistence as enableIndexedDbPersistence$1, enableMultiTabIndexedDbPersistence as enableMultiTabIndexedDbPersistence$1, enableNetwork as enableNetwork$1, endAt as endAt$1, endBefore as endBefore$1, getDoc as getDoc$1, getDocFromCache as getDocFromCache$1, getDocFromServer as getDocFromServer$1, getDocs as getDocs$1, getDocsFromCache as getDocsFromCache$1, getDocsFromServer as getDocsFromServer$1, getFirestore as getFirestore$1, increment as increment$1, initializeFirestore as initializeFirestore$1, limit as limit$1, limitToLast as limitToLast$1, loadBundle as loadBundle$1, namedQuery as namedQuery$1, onSnapshot as onSnapshot$1, onSnapshotsInSync as onSnapshotsInSync$1, orderBy as orderBy$1, query as query$1, queryEqual as queryEqual$1, refEqual as refEqual$1, runTransaction as runTransaction$1, serverTimestamp as serverTimestamp$1, setDoc as setDoc$1, setLogLevel as setLogLevel$1, snapshotEqual as snapshotEqual$1, startAfter as startAfter$1, startAt as startAt$1, terminate as terminate$1, updateDoc as updateDoc$1, waitForPendingWrites as waitForPendingWrites$1, where as where$1, writeBatch as writeBatch$1 } from 'firebase/firestore';\nexport * from 'firebase/firestore';\nclass Firestore {\n  constructor(firestore) {\n    return firestore;\n  }\n}\nconst FIRESTORE_PROVIDER_NAME = 'firestore';\nclass FirestoreInstances {\n  constructor() {\n    return ɵgetAllInstancesOf(FIRESTORE_PROVIDER_NAME);\n  }\n}\nconst firestoreInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(FIRESTORE_PROVIDER_NAME))), distinct());\nconst PROVIDED_FIRESTORE_INSTANCES = new InjectionToken('angularfire2.firestore-instances');\nfunction defaultFirestoreInstanceFactory(provided, defaultApp) {\n  const defaultFirestore = ɵgetDefaultInstanceOf(FIRESTORE_PROVIDER_NAME, provided, defaultApp);\n  return defaultFirestore && new Firestore(defaultFirestore);\n}\nfunction firestoreInstanceFactory(fn) {\n  return (zone, injector) => {\n    const firestore = zone.runOutsideAngular(() => fn(injector));\n    return new Firestore(firestore);\n  };\n}\nconst FIRESTORE_INSTANCES_PROVIDER = {\n  provide: FirestoreInstances,\n  deps: [[new Optional(), PROVIDED_FIRESTORE_INSTANCES]]\n};\nconst DEFAULT_FIRESTORE_INSTANCE_PROVIDER = {\n  provide: Firestore,\n  useFactory: defaultFirestoreInstanceFactory,\n  deps: [[new Optional(), PROVIDED_FIRESTORE_INSTANCES], FirebaseApp]\n};\nclass FirestoreModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'fst');\n  }\n}\nFirestoreModule.ɵfac = function FirestoreModule_Factory(t) {\n  return new (t || FirestoreModule)();\n};\nFirestoreModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: FirestoreModule\n});\nFirestoreModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DEFAULT_FIRESTORE_INSTANCE_PROVIDER, FIRESTORE_INSTANCES_PROVIDER]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FirestoreModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_FIRESTORE_INSTANCE_PROVIDER, FIRESTORE_INSTANCES_PROVIDER]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction provideFirestore(fn, ...deps) {\n  return {\n    ngModule: FirestoreModule,\n    providers: [{\n      provide: PROVIDED_FIRESTORE_INSTANCES,\n      useFactory: firestoreInstanceFactory(fn),\n      multi: true,\n      deps: [NgZone, Injector, ɵAngularFireSchedulers, FirebaseApps,\n      // Firestore+Auth work better if Auth is loaded first\n      [new Optional(), AuthInstances], [new Optional(), AppCheckInstances], ...deps]\n    }]\n  };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst collectionChanges = ɵzoneWrap(collectionChanges$1, true);\nconst collectionSnapshots = ɵzoneWrap(collection$1, true);\nconst sortedChanges = ɵzoneWrap(sortedChanges$1, true);\nconst auditTrail = ɵzoneWrap(auditTrail$1, true);\nconst collectionData = ɵzoneWrap(collectionData$1, true);\nconst docSnapshots = ɵzoneWrap(doc$1, true);\nconst docData = ɵzoneWrap(docData$1, true);\nconst snapToData = ɵzoneWrap(snapToData$1, true);\nconst fromRef = ɵzoneWrap(fromRef$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst addDoc = ɵzoneWrap(addDoc$1, true);\nconst arrayRemove = ɵzoneWrap(arrayRemove$1, true);\nconst arrayUnion = ɵzoneWrap(arrayUnion$1, true);\nconst clearIndexedDbPersistence = ɵzoneWrap(clearIndexedDbPersistence$1, true);\nconst collection = ɵzoneWrap(collection$2, true);\nconst collectionGroup = ɵzoneWrap(collectionGroup$1, true);\nconst connectFirestoreEmulator = ɵzoneWrap(connectFirestoreEmulator$1, true);\nconst deleteDoc = ɵzoneWrap(deleteDoc$1, true);\nconst deleteField = ɵzoneWrap(deleteField$1, true);\nconst disableNetwork = ɵzoneWrap(disableNetwork$1, true);\nconst doc = ɵzoneWrap(doc$2, true);\nconst documentId = ɵzoneWrap(documentId$1, true);\nconst enableIndexedDbPersistence = ɵzoneWrap(enableIndexedDbPersistence$1, true);\nconst enableMultiTabIndexedDbPersistence = ɵzoneWrap(enableMultiTabIndexedDbPersistence$1, true);\nconst enableNetwork = ɵzoneWrap(enableNetwork$1, true);\nconst endAt = ɵzoneWrap(endAt$1, true);\nconst endBefore = ɵzoneWrap(endBefore$1, true);\nconst getDoc = ɵzoneWrap(getDoc$1, true);\nconst getDocFromCache = ɵzoneWrap(getDocFromCache$1, true);\nconst getDocFromServer = ɵzoneWrap(getDocFromServer$1, true);\nconst getDocs = ɵzoneWrap(getDocs$1, true);\nconst getDocsFromCache = ɵzoneWrap(getDocsFromCache$1, true);\nconst getDocsFromServer = ɵzoneWrap(getDocsFromServer$1, true);\nconst getFirestore = ɵzoneWrap(getFirestore$1, true);\nconst increment = ɵzoneWrap(increment$1, true);\nconst initializeFirestore = ɵzoneWrap(initializeFirestore$1, true);\nconst limit = ɵzoneWrap(limit$1, true);\nconst limitToLast = ɵzoneWrap(limitToLast$1, true);\nconst loadBundle = ɵzoneWrap(loadBundle$1, true);\nconst namedQuery = ɵzoneWrap(namedQuery$1, true);\nconst onSnapshot = ɵzoneWrap(onSnapshot$1, true);\nconst onSnapshotsInSync = ɵzoneWrap(onSnapshotsInSync$1, true);\nconst orderBy = ɵzoneWrap(orderBy$1, true);\nconst query = ɵzoneWrap(query$1, true);\nconst queryEqual = ɵzoneWrap(queryEqual$1, true);\nconst refEqual = ɵzoneWrap(refEqual$1, true);\nconst runTransaction = ɵzoneWrap(runTransaction$1, true);\nconst serverTimestamp = ɵzoneWrap(serverTimestamp$1, true);\nconst setDoc = ɵzoneWrap(setDoc$1, true);\nconst setLogLevel = ɵzoneWrap(setLogLevel$1, true);\nconst snapshotEqual = ɵzoneWrap(snapshotEqual$1, true);\nconst startAfter = ɵzoneWrap(startAfter$1, true);\nconst startAt = ɵzoneWrap(startAt$1, true);\nconst terminate = ɵzoneWrap(terminate$1, true);\nconst updateDoc = ɵzoneWrap(updateDoc$1, true);\nconst waitForPendingWrites = ɵzoneWrap(waitForPendingWrites$1, true);\nconst where = ɵzoneWrap(where$1, true);\nconst writeBatch = ɵzoneWrap(writeBatch$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Firestore, FirestoreInstances, FirestoreModule, addDoc, arrayRemove, arrayUnion, auditTrail, clearIndexedDbPersistence, collection, collectionChanges, collectionData, collectionGroup, collectionSnapshots, connectFirestoreEmulator, deleteDoc, deleteField, disableNetwork, doc, docData, docSnapshots, documentId, enableIndexedDbPersistence, enableMultiTabIndexedDbPersistence, enableNetwork, endAt, endBefore, firestoreInstance$, fromRef, getDoc, getDocFromCache, getDocFromServer, getDocs, getDocsFromCache, getDocsFromServer, getFirestore, increment, initializeFirestore, limit, limitToLast, loadBundle, namedQuery, onSnapshot, onSnapshotsInSync, orderBy, provideFirestore, query, queryEqual, refEqual, runTransaction, serverTimestamp, setDoc, setLogLevel, snapToData, snapshotEqual, sortedChanges, startAfter, startAt, terminate, updateDoc, waitForPendingWrites, where, writeBatch };", "map": {"version": 3, "names": ["ɵgetAllInstancesOf", "ɵgetDefaultInstanceOf", "VERSION", "ɵAngularFireSchedulers", "ɵzoneWrap", "timer", "from", "concatMap", "distinct", "i0", "InjectionToken", "Optional", "NgModule", "NgZone", "Injector", "AuthInstances", "FirebaseApp", "FirebaseApps", "registerVersion", "AppCheckInstances", "collectionChanges", "collectionChanges$1", "collection", "collection$1", "sortedChanges", "sortedChanges$1", "auditTrail", "auditTrail$1", "collectionData", "collectionData$1", "doc", "doc$1", "docData", "docData$1", "snapToData", "snapToData$1", "fromRef", "fromRef$1", "addDoc", "addDoc$1", "arrayRemove", "arrayRemove$1", "arrayUnion", "arrayUnion$1", "clearIndexedDbPersistence", "clearIndexedDbPersistence$1", "collection$2", "collectionGroup", "collectionGroup$1", "connectFirestoreEmulator", "connectFirestoreEmulator$1", "deleteDoc", "deleteDoc$1", "deleteField", "deleteField$1", "disableNetwork", "disableNetwork$1", "doc$2", "documentId", "documentId$1", "enableIndexedDbPersistence", "enableIndexedDbPersistence$1", "enableMultiTabIndexedDbPersistence", "enableMultiTabIndexedDbPersistence$1", "enableNetwork", "enableNetwork$1", "endAt", "endAt$1", "endBefore", "endBefore$1", "getDoc", "getDoc$1", "getDocFromCache", "getDocFromCache$1", "getDocFromServer", "getDocFromServer$1", "getDocs", "getDocs$1", "getDocsFromCache", "getDocsFromCache$1", "getDocsFromServer", "getDocsFromServer$1", "getFirestore", "getFirestore$1", "increment", "increment$1", "initializeFirestore", "initializeFirestore$1", "limit", "limit$1", "limitToLast", "limitToLast$1", "loadBundle", "loadBundle$1", "<PERSON><PERSON><PERSON><PERSON>", "namedQuery$1", "onSnapshot", "onSnapshot$1", "onSnapshotsInSync", "onSnapshotsInSync$1", "orderBy", "orderBy$1", "query", "query$1", "queryEqual", "queryEqual$1", "refEqual", "refEqual$1", "runTransaction", "runTransaction$1", "serverTimestamp", "serverTimestamp$1", "setDoc", "setDoc$1", "setLogLevel", "setLogLevel$1", "snapshotEqual", "snapshotEqual$1", "startAfter", "startAfter$1", "startAt", "startAt$1", "terminate", "terminate$1", "updateDoc", "updateDoc$1", "waitForPendingWrites", "waitForPendingWrites$1", "where", "where$1", "writeBatch", "writeBatch$1", "Firestore", "constructor", "firestore", "FIRESTORE_PROVIDER_NAME", "FirestoreInstances", "firestoreInstance$", "pipe", "PROVIDED_FIRESTORE_INSTANCES", "defaultFirestoreInstanceFactory", "provided", "defaultApp", "defaultFirestore", "firestoreInstanceFactory", "fn", "zone", "injector", "runOutsideAngular", "FIRESTORE_INSTANCES_PROVIDER", "provide", "deps", "DEFAULT_FIRESTORE_INSTANCE_PROVIDER", "useFactory", "FirestoreModule", "full", "ɵfac", "FirestoreModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "ngDevMode", "ɵsetClassMetadata", "args", "provideFirestore", "ngModule", "multi", "collectionSnapshots", "docSnapshots"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/fesm2015/angular-fire-firestore.js"], "sourcesContent": ["import { ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, NgZone, Injector } from '@angular/core';\nimport { AuthInstances } from '@angular/fire/auth';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { collectionChanges as collectionChanges$1, collection as collection$1, sortedChanges as sortedChanges$1, auditTrail as auditTrail$1, collectionData as collectionData$1, doc as doc$1, docData as docData$1, snapToData as snapToData$1, fromRef as fromRef$1 } from 'rxfire/firestore';\nimport { addDoc as addDoc$1, arrayRemove as arrayRemove$1, arrayUnion as arrayUnion$1, clearIndexedDbPersistence as clearIndexedDbPersistence$1, collection as collection$2, collectionGroup as collectionGroup$1, connectFirestoreEmulator as connectFirestoreEmulator$1, deleteDoc as deleteDoc$1, deleteField as deleteField$1, disableNetwork as disableNetwork$1, doc as doc$2, documentId as documentId$1, enableIndexedDbPersistence as enableIndexedDbPersistence$1, enableMultiTabIndexedDbPersistence as enableMultiTabIndexedDbPersistence$1, enableNetwork as enableNetwork$1, endAt as endAt$1, endBefore as endBefore$1, getDoc as getDoc$1, getDocFromCache as getDocFromCache$1, getDocFromServer as getDocFromServer$1, getDocs as getDocs$1, getDocsFromCache as getDocsFromCache$1, getDocsFromServer as getDocsFromServer$1, getFirestore as getFirestore$1, increment as increment$1, initializeFirestore as initializeFirestore$1, limit as limit$1, limitToLast as limitToLast$1, loadBundle as loadBundle$1, namedQuery as namedQuery$1, onSnapshot as onSnapshot$1, onSnapshotsInSync as onSnapshotsInSync$1, orderBy as orderBy$1, query as query$1, queryEqual as queryEqual$1, refEqual as refEqual$1, runTransaction as runTransaction$1, serverTimestamp as serverTimestamp$1, setDoc as setDoc$1, setLogLevel as setLogLevel$1, snapshotEqual as snapshotEqual$1, startAfter as startAfter$1, startAt as startAt$1, terminate as terminate$1, updateDoc as updateDoc$1, waitForPendingWrites as waitForPendingWrites$1, where as where$1, writeBatch as writeBatch$1 } from 'firebase/firestore';\nexport * from 'firebase/firestore';\n\nclass Firestore {\n    constructor(firestore) {\n        return firestore;\n    }\n}\nconst FIRESTORE_PROVIDER_NAME = 'firestore';\nclass FirestoreInstances {\n    constructor() {\n        return ɵgetAllInstancesOf(FIRESTORE_PROVIDER_NAME);\n    }\n}\nconst firestoreInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(FIRESTORE_PROVIDER_NAME))), distinct());\n\nconst PROVIDED_FIRESTORE_INSTANCES = new InjectionToken('angularfire2.firestore-instances');\nfunction defaultFirestoreInstanceFactory(provided, defaultApp) {\n    const defaultFirestore = ɵgetDefaultInstanceOf(FIRESTORE_PROVIDER_NAME, provided, defaultApp);\n    return defaultFirestore && new Firestore(defaultFirestore);\n}\nfunction firestoreInstanceFactory(fn) {\n    return (zone, injector) => {\n        const firestore = zone.runOutsideAngular(() => fn(injector));\n        return new Firestore(firestore);\n    };\n}\nconst FIRESTORE_INSTANCES_PROVIDER = {\n    provide: FirestoreInstances,\n    deps: [\n        [new Optional(), PROVIDED_FIRESTORE_INSTANCES],\n    ]\n};\nconst DEFAULT_FIRESTORE_INSTANCE_PROVIDER = {\n    provide: Firestore,\n    useFactory: defaultFirestoreInstanceFactory,\n    deps: [\n        [new Optional(), PROVIDED_FIRESTORE_INSTANCES],\n        FirebaseApp,\n    ]\n};\nclass FirestoreModule {\n    constructor() {\n        registerVersion('angularfire', VERSION.full, 'fst');\n    }\n}\nFirestoreModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: FirestoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nFirestoreModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: FirestoreModule });\nFirestoreModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: FirestoreModule, providers: [\n        DEFAULT_FIRESTORE_INSTANCE_PROVIDER,\n        FIRESTORE_INSTANCES_PROVIDER,\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: FirestoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        DEFAULT_FIRESTORE_INSTANCE_PROVIDER,\n                        FIRESTORE_INSTANCES_PROVIDER,\n                    ]\n                }]\n        }], ctorParameters: function () { return []; } });\nfunction provideFirestore(fn, ...deps) {\n    return {\n        ngModule: FirestoreModule,\n        providers: [{\n                provide: PROVIDED_FIRESTORE_INSTANCES,\n                useFactory: firestoreInstanceFactory(fn),\n                multi: true,\n                deps: [\n                    NgZone,\n                    Injector,\n                    ɵAngularFireSchedulers,\n                    FirebaseApps,\n                    // Firestore+Auth work better if Auth is loaded first\n                    [new Optional(), AuthInstances],\n                    [new Optional(), AppCheckInstances],\n                    ...deps,\n                ]\n            }]\n    };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst collectionChanges = ɵzoneWrap(collectionChanges$1, true);\nconst collectionSnapshots = ɵzoneWrap(collection$1, true);\nconst sortedChanges = ɵzoneWrap(sortedChanges$1, true);\nconst auditTrail = ɵzoneWrap(auditTrail$1, true);\nconst collectionData = ɵzoneWrap(collectionData$1, true);\nconst docSnapshots = ɵzoneWrap(doc$1, true);\nconst docData = ɵzoneWrap(docData$1, true);\nconst snapToData = ɵzoneWrap(snapToData$1, true);\nconst fromRef = ɵzoneWrap(fromRef$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst addDoc = ɵzoneWrap(addDoc$1, true);\nconst arrayRemove = ɵzoneWrap(arrayRemove$1, true);\nconst arrayUnion = ɵzoneWrap(arrayUnion$1, true);\nconst clearIndexedDbPersistence = ɵzoneWrap(clearIndexedDbPersistence$1, true);\nconst collection = ɵzoneWrap(collection$2, true);\nconst collectionGroup = ɵzoneWrap(collectionGroup$1, true);\nconst connectFirestoreEmulator = ɵzoneWrap(connectFirestoreEmulator$1, true);\nconst deleteDoc = ɵzoneWrap(deleteDoc$1, true);\nconst deleteField = ɵzoneWrap(deleteField$1, true);\nconst disableNetwork = ɵzoneWrap(disableNetwork$1, true);\nconst doc = ɵzoneWrap(doc$2, true);\nconst documentId = ɵzoneWrap(documentId$1, true);\nconst enableIndexedDbPersistence = ɵzoneWrap(enableIndexedDbPersistence$1, true);\nconst enableMultiTabIndexedDbPersistence = ɵzoneWrap(enableMultiTabIndexedDbPersistence$1, true);\nconst enableNetwork = ɵzoneWrap(enableNetwork$1, true);\nconst endAt = ɵzoneWrap(endAt$1, true);\nconst endBefore = ɵzoneWrap(endBefore$1, true);\nconst getDoc = ɵzoneWrap(getDoc$1, true);\nconst getDocFromCache = ɵzoneWrap(getDocFromCache$1, true);\nconst getDocFromServer = ɵzoneWrap(getDocFromServer$1, true);\nconst getDocs = ɵzoneWrap(getDocs$1, true);\nconst getDocsFromCache = ɵzoneWrap(getDocsFromCache$1, true);\nconst getDocsFromServer = ɵzoneWrap(getDocsFromServer$1, true);\nconst getFirestore = ɵzoneWrap(getFirestore$1, true);\nconst increment = ɵzoneWrap(increment$1, true);\nconst initializeFirestore = ɵzoneWrap(initializeFirestore$1, true);\nconst limit = ɵzoneWrap(limit$1, true);\nconst limitToLast = ɵzoneWrap(limitToLast$1, true);\nconst loadBundle = ɵzoneWrap(loadBundle$1, true);\nconst namedQuery = ɵzoneWrap(namedQuery$1, true);\nconst onSnapshot = ɵzoneWrap(onSnapshot$1, true);\nconst onSnapshotsInSync = ɵzoneWrap(onSnapshotsInSync$1, true);\nconst orderBy = ɵzoneWrap(orderBy$1, true);\nconst query = ɵzoneWrap(query$1, true);\nconst queryEqual = ɵzoneWrap(queryEqual$1, true);\nconst refEqual = ɵzoneWrap(refEqual$1, true);\nconst runTransaction = ɵzoneWrap(runTransaction$1, true);\nconst serverTimestamp = ɵzoneWrap(serverTimestamp$1, true);\nconst setDoc = ɵzoneWrap(setDoc$1, true);\nconst setLogLevel = ɵzoneWrap(setLogLevel$1, true);\nconst snapshotEqual = ɵzoneWrap(snapshotEqual$1, true);\nconst startAfter = ɵzoneWrap(startAfter$1, true);\nconst startAt = ɵzoneWrap(startAt$1, true);\nconst terminate = ɵzoneWrap(terminate$1, true);\nconst updateDoc = ɵzoneWrap(updateDoc$1, true);\nconst waitForPendingWrites = ɵzoneWrap(waitForPendingWrites$1, true);\nconst where = ɵzoneWrap(where$1, true);\nconst writeBatch = ɵzoneWrap(writeBatch$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Firestore, FirestoreInstances, FirestoreModule, addDoc, arrayRemove, arrayUnion, auditTrail, clearIndexedDbPersistence, collection, collectionChanges, collectionData, collectionGroup, collectionSnapshots, connectFirestoreEmulator, deleteDoc, deleteField, disableNetwork, doc, docData, docSnapshots, documentId, enableIndexedDbPersistence, enableMultiTabIndexedDbPersistence, enableNetwork, endAt, endBefore, firestoreInstance$, fromRef, getDoc, getDocFromCache, getDocFromServer, getDocs, getDocsFromCache, getDocsFromServer, getFirestore, increment, initializeFirestore, limit, limitToLast, loadBundle, namedQuery, onSnapshot, onSnapshotsInSync, orderBy, provideFirestore, query, queryEqual, refEqual, runTransaction, serverTimestamp, setDoc, setLogLevel, snapToData, snapshotEqual, sortedChanges, startAfter, startAt, terminate, updateDoc, waitForPendingWrites, where, writeBatch };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,SAAS,QAAQ,eAAe;AACrH,SAASC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACpF,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAC7D,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,iBAAiB,IAAIC,mBAAmB,EAAEC,UAAU,IAAIC,YAAY,EAAEC,aAAa,IAAIC,eAAe,EAAEC,UAAU,IAAIC,YAAY,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,GAAG,IAAIC,KAAK,EAAEC,OAAO,IAAIC,SAAS,EAAEC,UAAU,IAAIC,YAAY,EAAEC,OAAO,IAAIC,SAAS,QAAQ,kBAAkB;AAC/R,SAASC,MAAM,IAAIC,QAAQ,EAAEC,WAAW,IAAIC,aAAa,EAAEC,UAAU,IAAIC,YAAY,EAAEC,yBAAyB,IAAIC,2BAA2B,EAAEvB,UAAU,IAAIwB,YAAY,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,wBAAwB,IAAIC,0BAA0B,EAAEC,SAAS,IAAIC,WAAW,EAAEC,WAAW,IAAIC,aAAa,EAAEC,cAAc,IAAIC,gBAAgB,EAAE1B,GAAG,IAAI2B,KAAK,EAAEC,UAAU,IAAIC,YAAY,EAAEC,0BAA0B,IAAIC,4BAA4B,EAAEC,kCAAkC,IAAIC,oCAAoC,EAAEC,aAAa,IAAIC,eAAe,EAAEC,KAAK,IAAIC,OAAO,EAAEC,SAAS,IAAIC,WAAW,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,gBAAgB,IAAIC,kBAAkB,EAAEC,OAAO,IAAIC,SAAS,EAAEC,gBAAgB,IAAIC,kBAAkB,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,YAAY,IAAIC,cAAc,EAAEC,SAAS,IAAIC,WAAW,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,KAAK,IAAIC,OAAO,EAAEC,WAAW,IAAIC,aAAa,EAAEC,UAAU,IAAIC,YAAY,EAAEC,UAAU,IAAIC,YAAY,EAAEC,UAAU,IAAIC,YAAY,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,OAAO,IAAIC,SAAS,EAAEC,KAAK,IAAIC,OAAO,EAAEC,UAAU,IAAIC,YAAY,EAAEC,QAAQ,IAAIC,UAAU,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,WAAW,IAAIC,aAAa,EAAEC,aAAa,IAAIC,eAAe,EAAEC,UAAU,IAAIC,YAAY,EAAEC,OAAO,IAAIC,SAAS,EAAEC,SAAS,IAAIC,WAAW,EAAEC,SAAS,IAAIC,WAAW,EAAEC,oBAAoB,IAAIC,sBAAsB,EAAEC,KAAK,IAAIC,OAAO,EAAEC,UAAU,IAAIC,YAAY,QAAQ,oBAAoB;AAC/hD,cAAc,oBAAoB;AAElC,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAACC,SAAS,EAAE;IACnB,OAAOA,SAAS;EACpB;AACJ;AACA,MAAMC,uBAAuB,GAAG,WAAW;AAC3C,MAAMC,kBAAkB,CAAC;EACrBH,WAAWA,CAAA,EAAG;IACV,OAAOrI,kBAAkB,CAACuI,uBAAuB,CAAC;EACtD;AACJ;AACA,MAAME,kBAAkB,GAAGpI,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACqI,IAAI,CAACnI,SAAS,CAAC,MAAMD,IAAI,CAACN,kBAAkB,CAACuI,uBAAuB,CAAC,CAAC,CAAC,EAAE/H,QAAQ,CAAC,CAAC,CAAC;AAE7H,MAAMmI,4BAA4B,GAAG,IAAIjI,cAAc,CAAC,kCAAkC,CAAC;AAC3F,SAASkI,+BAA+BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC3D,MAAMC,gBAAgB,GAAG9I,qBAAqB,CAACsI,uBAAuB,EAAEM,QAAQ,EAAEC,UAAU,CAAC;EAC7F,OAAOC,gBAAgB,IAAI,IAAIX,SAAS,CAACW,gBAAgB,CAAC;AAC9D;AACA,SAASC,wBAAwBA,CAACC,EAAE,EAAE;EAClC,OAAO,CAACC,IAAI,EAAEC,QAAQ,KAAK;IACvB,MAAMb,SAAS,GAAGY,IAAI,CAACE,iBAAiB,CAAC,MAAMH,EAAE,CAACE,QAAQ,CAAC,CAAC;IAC5D,OAAO,IAAIf,SAAS,CAACE,SAAS,CAAC;EACnC,CAAC;AACL;AACA,MAAMe,4BAA4B,GAAG;EACjCC,OAAO,EAAEd,kBAAkB;EAC3Be,IAAI,EAAE,CACF,CAAC,IAAI5I,QAAQ,CAAC,CAAC,EAAEgI,4BAA4B,CAAC;AAEtD,CAAC;AACD,MAAMa,mCAAmC,GAAG;EACxCF,OAAO,EAAElB,SAAS;EAClBqB,UAAU,EAAEb,+BAA+B;EAC3CW,IAAI,EAAE,CACF,CAAC,IAAI5I,QAAQ,CAAC,CAAC,EAAEgI,4BAA4B,CAAC,EAC9C3H,WAAW;AAEnB,CAAC;AACD,MAAM0I,eAAe,CAAC;EAClBrB,WAAWA,CAAA,EAAG;IACVnH,eAAe,CAAC,aAAa,EAAEhB,OAAO,CAACyJ,IAAI,EAAE,KAAK,CAAC;EACvD;AACJ;AACAD,eAAe,CAACE,IAAI,YAAAC,wBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFJ,eAAe;AAAA,CAAkD;AAC7KA,eAAe,CAACK,IAAI,kBAD8EtJ,EAAE,CAAAuJ,gBAAA;EAAAC,IAAA,EACSP;AAAe,EAAG;AAC/HA,eAAe,CAACQ,IAAI,kBAF8EzJ,EAAE,CAAA0J,gBAAA;EAAAC,SAAA,EAEqC,CACjIZ,mCAAmC,EACnCH,4BAA4B;AAC/B,EAAG;AACR;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KANkG5J,EAAE,CAAA6J,iBAAA,CAMTZ,eAAe,EAAc,CAAC;IAC7GO,IAAI,EAAErJ,QAAQ;IACd2J,IAAI,EAAE,CAAC;MACCH,SAAS,EAAE,CACPZ,mCAAmC,EACnCH,4BAA4B;IAEpC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASmB,gBAAgBA,CAACvB,EAAE,EAAE,GAAGM,IAAI,EAAE;EACnC,OAAO;IACHkB,QAAQ,EAAEf,eAAe;IACzBU,SAAS,EAAE,CAAC;MACJd,OAAO,EAAEX,4BAA4B;MACrCc,UAAU,EAAET,wBAAwB,CAACC,EAAE,CAAC;MACxCyB,KAAK,EAAE,IAAI;MACXnB,IAAI,EAAE,CACF1I,MAAM,EACNC,QAAQ,EACRX,sBAAsB,EACtBc,YAAY;MACZ;MACA,CAAC,IAAIN,QAAQ,CAAC,CAAC,EAAEI,aAAa,CAAC,EAC/B,CAAC,IAAIJ,QAAQ,CAAC,CAAC,EAAEQ,iBAAiB,CAAC,EACnC,GAAGoI,IAAI;IAEf,CAAC;EACT,CAAC;AACL;;AAEA;AACA,MAAMnI,iBAAiB,GAAGhB,SAAS,CAACiB,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMsJ,mBAAmB,GAAGvK,SAAS,CAACmB,YAAY,EAAE,IAAI,CAAC;AACzD,MAAMC,aAAa,GAAGpB,SAAS,CAACqB,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,UAAU,GAAGtB,SAAS,CAACuB,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,cAAc,GAAGxB,SAAS,CAACyB,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAM+I,YAAY,GAAGxK,SAAS,CAAC2B,KAAK,EAAE,IAAI,CAAC;AAC3C,MAAMC,OAAO,GAAG5B,SAAS,CAAC6B,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMC,UAAU,GAAG9B,SAAS,CAAC+B,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,OAAO,GAAGhC,SAAS,CAACiC,SAAS,EAAE,IAAI,CAAC;;AAE1C;AACA,MAAMC,MAAM,GAAGlC,SAAS,CAACmC,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMC,WAAW,GAAGpC,SAAS,CAACqC,aAAa,EAAE,IAAI,CAAC;AAClD,MAAMC,UAAU,GAAGtC,SAAS,CAACuC,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,yBAAyB,GAAGxC,SAAS,CAACyC,2BAA2B,EAAE,IAAI,CAAC;AAC9E,MAAMvB,UAAU,GAAGlB,SAAS,CAAC0C,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,eAAe,GAAG3C,SAAS,CAAC4C,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMC,wBAAwB,GAAG7C,SAAS,CAAC8C,0BAA0B,EAAE,IAAI,CAAC;AAC5E,MAAMC,SAAS,GAAG/C,SAAS,CAACgD,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAMC,WAAW,GAAGjD,SAAS,CAACkD,aAAa,EAAE,IAAI,CAAC;AAClD,MAAMC,cAAc,GAAGnD,SAAS,CAACoD,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAM1B,GAAG,GAAG1B,SAAS,CAACqD,KAAK,EAAE,IAAI,CAAC;AAClC,MAAMC,UAAU,GAAGtD,SAAS,CAACuD,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,0BAA0B,GAAGxD,SAAS,CAACyD,4BAA4B,EAAE,IAAI,CAAC;AAChF,MAAMC,kCAAkC,GAAG1D,SAAS,CAAC2D,oCAAoC,EAAE,IAAI,CAAC;AAChG,MAAMC,aAAa,GAAG5D,SAAS,CAAC6D,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,KAAK,GAAG9D,SAAS,CAAC+D,OAAO,EAAE,IAAI,CAAC;AACtC,MAAMC,SAAS,GAAGhE,SAAS,CAACiE,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAMC,MAAM,GAAGlE,SAAS,CAACmE,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMC,eAAe,GAAGpE,SAAS,CAACqE,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMC,gBAAgB,GAAGtE,SAAS,CAACuE,kBAAkB,EAAE,IAAI,CAAC;AAC5D,MAAMC,OAAO,GAAGxE,SAAS,CAACyE,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMC,gBAAgB,GAAG1E,SAAS,CAAC2E,kBAAkB,EAAE,IAAI,CAAC;AAC5D,MAAMC,iBAAiB,GAAG5E,SAAS,CAAC6E,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,YAAY,GAAG9E,SAAS,CAAC+E,cAAc,EAAE,IAAI,CAAC;AACpD,MAAMC,SAAS,GAAGhF,SAAS,CAACiF,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAMC,mBAAmB,GAAGlF,SAAS,CAACmF,qBAAqB,EAAE,IAAI,CAAC;AAClE,MAAMC,KAAK,GAAGpF,SAAS,CAACqF,OAAO,EAAE,IAAI,CAAC;AACtC,MAAMC,WAAW,GAAGtF,SAAS,CAACuF,aAAa,EAAE,IAAI,CAAC;AAClD,MAAMC,UAAU,GAAGxF,SAAS,CAACyF,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,UAAU,GAAG1F,SAAS,CAAC2F,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,UAAU,GAAG5F,SAAS,CAAC6F,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,iBAAiB,GAAG9F,SAAS,CAAC+F,mBAAmB,EAAE,IAAI,CAAC;AAC9D,MAAMC,OAAO,GAAGhG,SAAS,CAACiG,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMC,KAAK,GAAGlG,SAAS,CAACmG,OAAO,EAAE,IAAI,CAAC;AACtC,MAAMC,UAAU,GAAGpG,SAAS,CAACqG,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,QAAQ,GAAGtG,SAAS,CAACuG,UAAU,EAAE,IAAI,CAAC;AAC5C,MAAMC,cAAc,GAAGxG,SAAS,CAACyG,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAMC,eAAe,GAAG1G,SAAS,CAAC2G,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMC,MAAM,GAAG5G,SAAS,CAAC6G,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMC,WAAW,GAAG9G,SAAS,CAAC+G,aAAa,EAAE,IAAI,CAAC;AAClD,MAAMC,aAAa,GAAGhH,SAAS,CAACiH,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,UAAU,GAAGlH,SAAS,CAACmH,YAAY,EAAE,IAAI,CAAC;AAChD,MAAMC,OAAO,GAAGpH,SAAS,CAACqH,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMC,SAAS,GAAGtH,SAAS,CAACuH,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAMC,SAAS,GAAGxH,SAAS,CAACyH,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAMC,oBAAoB,GAAG1H,SAAS,CAAC2H,sBAAsB,EAAE,IAAI,CAAC;AACpE,MAAMC,KAAK,GAAG5H,SAAS,CAAC6H,OAAO,EAAE,IAAI,CAAC;AACtC,MAAMC,UAAU,GAAG9H,SAAS,CAAC+H,YAAY,EAAE,IAAI,CAAC;;AAEhD;AACA;AACA;;AAEA,SAASC,SAAS,EAAEI,kBAAkB,EAAEkB,eAAe,EAAEpH,MAAM,EAAEE,WAAW,EAAEE,UAAU,EAAEhB,UAAU,EAAEkB,yBAAyB,EAAEtB,UAAU,EAAEF,iBAAiB,EAAEQ,cAAc,EAAEmB,eAAe,EAAE4H,mBAAmB,EAAE1H,wBAAwB,EAAEE,SAAS,EAAEE,WAAW,EAAEE,cAAc,EAAEzB,GAAG,EAAEE,OAAO,EAAE4I,YAAY,EAAElH,UAAU,EAAEE,0BAA0B,EAAEE,kCAAkC,EAAEE,aAAa,EAAEE,KAAK,EAAEE,SAAS,EAAEqE,kBAAkB,EAAErG,OAAO,EAAEkC,MAAM,EAAEE,eAAe,EAAEE,gBAAgB,EAAEE,OAAO,EAAEE,gBAAgB,EAAEE,iBAAiB,EAAEE,YAAY,EAAEE,SAAS,EAAEE,mBAAmB,EAAEE,KAAK,EAAEE,WAAW,EAAEE,UAAU,EAAEE,UAAU,EAAEE,UAAU,EAAEE,iBAAiB,EAAEE,OAAO,EAAEoE,gBAAgB,EAAElE,KAAK,EAAEE,UAAU,EAAEE,QAAQ,EAAEE,cAAc,EAAEE,eAAe,EAAEE,MAAM,EAAEE,WAAW,EAAEhF,UAAU,EAAEkF,aAAa,EAAE5F,aAAa,EAAE8F,UAAU,EAAEE,OAAO,EAAEE,SAAS,EAAEE,SAAS,EAAEE,oBAAoB,EAAEE,KAAK,EAAEE,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
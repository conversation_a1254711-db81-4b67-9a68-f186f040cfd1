{"ast": null, "code": "import firebase from '@firebase/app-compat';\nexport { default } from '@firebase/app-compat';\nvar name = \"firebase\";\nvar version = \"9.23.0\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfirebase.registerVersion(name, version, 'app-compat');", "map": {"version": 3, "names": ["firebase", "default", "name", "version", "registerVersion"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/node_modules/firebase/compat/app/dist/esm/index.esm.js"], "sourcesContent": ["import firebase from '@firebase/app-compat';\nexport { default } from '@firebase/app-compat';\n\nvar name = \"firebase\";\nvar version = \"9.23.0\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfirebase.registerVersion(name, version, 'app-compat');\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,OAAO,QAAQ,sBAAsB;AAE9C,IAAIC,IAAI,GAAG,UAAU;AACrB,IAAIC,OAAO,GAAG,QAAQ;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAH,QAAQ,CAACI,eAAe,CAACF,IAAI,EAAEC,OAAO,EAAE,YAAY,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
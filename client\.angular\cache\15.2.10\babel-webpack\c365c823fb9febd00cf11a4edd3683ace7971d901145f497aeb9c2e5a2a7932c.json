{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/team.service\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@ng-select/ng-select\";\nconst _c0 = [\"fileInput\"];\nfunction ModalImportTeamsComponent_ng_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r6.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r6.name, \" \");\n  }\n}\nfunction ModalImportTeamsComponent_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.groupError);\n  }\n}\nfunction ModalImportTeamsComponent_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"No file selected\"));\n  }\n}\nfunction ModalImportTeamsComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.file.name);\n  }\n}\nfunction ModalImportTeamsComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.fileError);\n  }\n}\nexport class ModalImportTeamsComponent {\n  constructor(_teamService, _modalService, _translateService, _loadingService) {\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._loadingService = _loadingService;\n    this.groups = [];\n  }\n  ngOnInit() {\n    this.getGroups(this.seasonId);\n  }\n  getGroups(seasonId) {\n    return this._teamService.getGroupBySeason(seasonId).subscribe(res => {\n      this.groups = res;\n    });\n  }\n  validateFile() {\n    if (!this.file) {\n      this.fileError = this._translateService.instant('This field is required', {\n        field: this._translateService.instant('File')\n      });\n    }\n    if (!this.groupId) {\n      this.groupError = this._translateService.instant('This field is required', {\n        field: this._translateService.instant('Group')\n      });\n    }\n    return this.file && this.groupId;\n  }\n  onGroupChange() {\n    this.groupError = '';\n  }\n  onImport() {\n    if (!this.validateFile()) {\n      return;\n    }\n    this._loadingService.show();\n    this._teamService.importTeams(this.file, this.seasonId, this.groupId).subscribe(res => {\n      this._modalService.close(res);\n    });\n  }\n  triggerFileInput() {\n    this.fileError = '';\n    this.fileInput.nativeElement.click();\n  }\n  onClose() {\n    this._modalService.dismiss();\n  }\n  downloadTemplate(filename) {\n    this._teamService.downloadFile(filename).subscribe(res => {\n      this._teamService.saveFile(res.data, res.filename);\n    }, error => {\n      console.error('Error downloading file:', error);\n    });\n  }\n  onFileChange($event) {\n    if ($event.target.files.length > 0) {\n      this.file = $event.target.files[0];\n    }\n  }\n  static #_ = this.ɵfac = function ModalImportTeamsComponent_Factory(t) {\n    return new (t || ModalImportTeamsComponent)(i0.ɵɵdirectiveInject(i1.TeamService), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.LoadingService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalImportTeamsComponent,\n    selectors: [[\"app-modal-import-teams\"]],\n    viewQuery: function ModalImportTeamsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n      }\n    },\n    inputs: {\n      seasonId: \"seasonId\"\n    },\n    decls: 42,\n    vars: 27,\n    consts: [[1, \"modal-header\"], [1, \"text-center\", \"w-100\"], [1, \"text-capitalize\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"m-2\"], [1, \"col\", \"p-0\", \"pr-1\"], [\"for\", \"season\", 1, \"form-label\"], [3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-sm text-danger\", 4, \"ngIf\"], [1, \"col\", \"mt-1\", \"p-0\", \"pr-1\"], [1, \"custom-file-upload\"], [1, \"btn\", \"btn-outline-primary\", \"btn-block\", 3, \"click\"], [1, \"fa-light\", \"fa-upload\"], [\"class\", \"text-secondary\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".csv\", 1, \"form-control-file\", 3, \"change\"], [\"fileInput\", \"\"], [\"class\", \"text-break \", 4, \"ngIf\"], [1, \"m-2\", \"d-flex\", \"justify-content-between\", \"text-center\", \"align-items-center\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"fa-light\", \"fa-download\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"float-right\", 3, \"click\"], [3, \"value\"], [1, \"text-sm\", \"text-danger\"], [1, \"text-secondary\"], [1, \"text-break\"]],\n    template: function ModalImportTeamsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"div\", 1)(3, \"h3\")(4, \"span\", 2);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ModalImportTeamsComponent_Template_button_click_7_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(8, \"span\", 4);\n        i0.ɵɵtext(9, \"\\u00D7 \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"label\", 7);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"ng-select\", 8);\n        i0.ɵɵlistener(\"ngModelChange\", function ModalImportTeamsComponent_Template_ng_select_ngModelChange_15_listener($event) {\n          return ctx.groupId = $event;\n        })(\"ngModelChange\", function ModalImportTeamsComponent_Template_ng_select_ngModelChange_15_listener() {\n          return ctx.onGroupChange();\n        });\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵtemplate(17, ModalImportTeamsComponent_ng_option_17_Template, 2, 2, \"ng-option\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, ModalImportTeamsComponent_span_18_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 11)(20, \"label\", 7);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 12)(24, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function ModalImportTeamsComponent_Template_button_click_24_listener() {\n          return ctx.triggerFileInput();\n        });\n        i0.ɵɵelement(25, \"i\", 14);\n        i0.ɵɵtext(26);\n        i0.ɵɵpipe(27, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(28, ModalImportTeamsComponent_span_28_Template, 3, 3, \"span\", 15);\n        i0.ɵɵelementStart(29, \"input\", 16, 17);\n        i0.ɵɵlistener(\"change\", function ModalImportTeamsComponent_Template_input_change_29_listener($event) {\n          return ctx.onFileChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(31, ModalImportTeamsComponent_span_31_Template, 2, 1, \"span\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, ModalImportTeamsComponent_span_32_Template, 2, 1, \"span\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 19)(34, \"a\", 20);\n        i0.ɵɵlistener(\"click\", function ModalImportTeamsComponent_Template_a_click_34_listener() {\n          return ctx.downloadTemplate(\"sample.csv\");\n        });\n        i0.ɵɵelement(35, \"i\", 21);\n        i0.ɵɵtext(36);\n        i0.ɵɵpipe(37, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"button\", 22);\n        i0.ɵɵlistener(\"click\", function ModalImportTeamsComponent_Template_button_click_38_listener() {\n          return ctx.onImport();\n        });\n        i0.ɵɵelement(39, \"i\", 14);\n        i0.ɵɵtext(40);\n        i0.ɵɵpipe(41, \"translate\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 13, \"Import Teams\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 15, \"Group\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(16, 17, \"Select Group\"));\n        i0.ɵɵproperty(\"ngModel\", ctx.groupId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.groupError);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 19, \"File\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 21, \"Upload File\"), \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.file);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.file);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.fileError);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 23, \"Sample file\"), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(41, 25, \"Import\"), \" \");\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i6.NgControlStatus, i6.NgModel, i7.NgSelectComponent, i7.ɵr, i3.TranslatePipe],\n    styles: [\".custom-file-upload[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.custom-file-upload[_ngcontent-%COMP%]   input[type=file][_ngcontent-%COMP%] {\\n  display: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdGVhbXMvdGVhbS1tYW5hZ2VtZW50L21vZGFsLWltcG9ydC10ZWFtcy9tb2RhbC1pbXBvcnQtdGVhbXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsK0NBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLyogSW4geW91ciBDU1MgZmlsZSAoZS5nLiwgYXBwLmNvbXBvbmVudC5jc3MpICovXHJcbi5jdXN0b20tZmlsZS11cGxvYWQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAyMHB4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbn1cclxuXHJcbi5jdXN0b20tZmlsZS11cGxvYWQgaW5wdXRbdHlwZT1cImZpbGVcIl0ge1xyXG4gIGRpc3BsYXk6IG5vbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;IAkBgBA,EAAA,CAAAC,cAAA,oBAA2D;IACvDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4BH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,EAAA,CAAkB;IACtDN,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,QAAA,CAAAI,IAAA,MACJ;;;;;IAEJT,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAgB;;;;;IAWjEZ,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAAzCH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAa,WAAA,2BAAkC;;;;;IAE7Eb,EAAA,CAAAC,cAAA,eAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAU,iBAAA,CAAAI,MAAA,CAAAC,IAAA,CAAAN,IAAA,CAAe;;;;;IAG1DT,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAU,iBAAA,CAAAM,MAAA,CAAAC,SAAA,CAAe;;;AC3B/E,OAAM,MAAOC,yBAAyB;EASpCC,YACUC,YAAyB,EACzBC,aAA6B,EAC7BC,iBAAmC,EACnCC,eAA+B;IAH/B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IATzB,KAAAC,MAAM,GAAQ,EAAE;EAUb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC;EAC/B;EAEAD,SAASA,CAACC,QAAa;IACrB,OAAO,IAAI,CAACP,YAAY,CAACQ,gBAAgB,CAACD,QAAQ,CAAC,CAACE,SAAS,CAAEC,GAAG,IAAI;MACpE,IAAI,CAACN,MAAM,GAAGM,GAAG;IACnB,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA;IAEV,IAAI,CAAC,IAAI,CAAChB,IAAI,EAAE;MACd,IAAI,CAACE,SAAS,GAAI,IAAI,CAACK,iBAAiB,CAACU,OAAO,CAAC,wBAAwB,EAAE;QAACC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACU,OAAO,CAAC,MAAM;MAAC,CAAC,CAAC;;IAI7H,IAAI,CAAC,IAAI,CAACE,OAAO,EAAE;MACjB,IAAI,CAACtB,UAAU,GAAG,IAAI,CAACU,iBAAiB,CAACU,OAAO,CAAC,wBAAwB,EAAE;QAACC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACU,OAAO,CAAC,OAAO;MAAC,CAAC,CAAC;;IAG9H,OAAO,IAAI,CAACjB,IAAI,IAAI,IAAI,CAACmB,OAAO;EAClC;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACvB,UAAU,GAAG,EAAE;EACtB;EAEAwB,QAAQA,CAAA;IAEN,IAAI,CAAC,IAAI,CAACL,YAAY,EAAE,EAAE;MACxB;;IAGF,IAAI,CAACR,eAAe,CAACc,IAAI,EAAE;IAE3B,IAAI,CAACjB,YAAY,CACdkB,WAAW,CAAC,IAAI,CAACvB,IAAI,EAAE,IAAI,CAACY,QAAQ,EAAE,IAAI,CAACO,OAAO,CAAC,CACnDL,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAACT,aAAa,CAACkB,KAAK,CAACT,GAAG,CAAC;IAC/B,CAAC,CAAC;EACN;EAEAU,gBAAgBA,CAAA;IACd,IAAI,CAACvB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACwB,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACvB,aAAa,CAACwB,OAAO,EAAE;EAC9B;EAEAC,gBAAgBA,CAACC,QAAgB;IAC/B,IAAI,CAAC3B,YAAY,CAAC4B,YAAY,CAACD,QAAQ,CAAC,CAAClB,SAAS,CAC/CC,GAAG,IAAI;MACN,IAAI,CAACV,YAAY,CAAC6B,QAAQ,CAACnB,GAAG,CAACoB,IAAI,EAAEpB,GAAG,CAACiB,QAAQ,CAAC;IACpD,CAAC,EACAI,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,CACF;EACH;EAEAE,YAAYA,CAACC,MAAM;IACjB,IAAIA,MAAM,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAClC,IAAI,CAAC1C,IAAI,GAAGuC,MAAM,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;;EAEtC;EAAC,QAAAE,CAAA;qBAnFUxC,yBAAyB,EAAAlB,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAjE,EAAA,CAAA2D,iBAAA,CAAAO,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAAzBlD,yBAAyB;IAAAmD,SAAA;IAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;QDXtCxE,EAAA,CAAAC,cAAA,UAAK;QAKyCD,EAAA,CAAAE,MAAA,GAA8B;;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAI3EH,EAAA,CAAAC,cAAA,gBAAmG;QAApBD,EAAA,CAAA0E,UAAA,mBAAAC,2DAAA;UAAA,OAASF,GAAA,CAAA7B,OAAA,EAAS;QAAA,EAAC;QAC9F5C,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAE,MAAA,cAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAIhDH,EAAA,CAAAC,cAAA,cAA8D;QAEfD,EAAA,CAAAE,MAAA,IAAyB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxEH,EAAA,CAAAC,cAAA,oBAAoH;QAAxGD,EAAA,CAAA0E,UAAA,2BAAAE,uEAAAtB,MAAA;UAAA,OAAAmB,GAAA,CAAAvC,OAAA,GAAAoB,MAAA;QAAA,EAAqB,2BAAAsB,uEAAA;UAAA,OAAkBH,GAAA,CAAAtC,aAAA,EAAe;QAAA,EAAjC;;QAC7BnC,EAAA,CAAA6E,UAAA,KAAAC,+CAAA,uBAEY;QAChB9E,EAAA,CAAAG,YAAA,EAAY;QACZH,EAAA,CAAA6E,UAAA,KAAAE,0CAAA,mBAA4E;QAGhF/E,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA+B;QACYD,EAAA,CAAAE,MAAA,IAAwB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACvEH,EAAA,CAAAC,cAAA,eAAiC;QACqBD,EAAA,CAAA0E,UAAA,mBAAAM,4DAAA;UAAA,OAASP,GAAA,CAAAjC,gBAAA,EAAkB;QAAA,EAAC;QAC1ExC,EAAA,CAAAiF,SAAA,aAAkC;QAClCjF,EAAA,CAAAE,MAAA,IAA6B;;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1CH,EAAA,CAAA6E,UAAA,KAAAK,0CAAA,mBAAoF;QACpFlF,EAAA,CAAAC,cAAA,qBAAsG;QAAhCD,EAAA,CAAA0E,UAAA,oBAAAS,4DAAA7B,MAAA;UAAA,OAAUmB,GAAA,CAAApB,YAAA,CAAAC,MAAA,CAAoB;QAAA,EAAC;QAArGtD,EAAA,CAAAG,YAAA,EAAsG;QACtGH,EAAA,CAAA6E,UAAA,KAAAO,0CAAA,mBAA6D;QAEjEpF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAA6E,UAAA,KAAAQ,0CAAA,mBAA0E;QAG9ErF,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA+E;QAGxED,EAAA,CAAA0E,UAAA,mBAAAY,uDAAA;UAAA,OAASb,GAAA,CAAA3B,gBAAA,CAAiB,YAAY,CAAC;QAAA,EAAC;QACvC9C,EAAA,CAAAiF,SAAA,aAAoC;QAACjF,EAAA,CAAAE,MAAA,IACzC;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,kBAA+E;QAArBD,EAAA,CAAA0E,UAAA,mBAAAa,4DAAA;UAAA,OAASd,GAAA,CAAArC,QAAA,EAAU;QAAA,EAAC;QAC1EpC,EAAA,CAAAiF,SAAA,aAAkC;QAACjF,EAAA,CAAAE,MAAA,IACvC;;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA9CyBH,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAa,WAAA,wBAA8B;QAWzBb,EAAA,CAAAO,SAAA,GAAyB;QAAzBP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAa,WAAA,kBAAyB;QACIb,EAAA,CAAAO,SAAA,GAA8C;QAA9CP,EAAA,CAAAwF,qBAAA,gBAAAxF,EAAA,CAAAa,WAAA,yBAA8C;QAAtGb,EAAA,CAAAI,UAAA,YAAAqE,GAAA,CAAAvC,OAAA,CAAqB;QACAlC,EAAA,CAAAO,SAAA,GAAS;QAATP,EAAA,CAAAI,UAAA,YAAAqE,GAAA,CAAAjD,MAAA,CAAS;QAIPxB,EAAA,CAAAO,SAAA,GAAgB;QAAhBP,EAAA,CAAAI,UAAA,SAAAqE,GAAA,CAAA7D,UAAA,CAAgB;QAMZZ,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAa,WAAA,iBAAwB;QAIvDb,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAa,WAAA,4BAA6B;QACHb,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAI,UAAA,UAAAqE,GAAA,CAAA1D,IAAA,CAAW;QAElCf,EAAA,CAAAO,SAAA,GAAU;QAAVP,EAAA,CAAAI,UAAA,SAAAqE,GAAA,CAAA1D,IAAA,CAAU;QAGcf,EAAA,CAAAO,SAAA,GAAe;QAAfP,EAAA,CAAAI,UAAA,SAAAqE,GAAA,CAAAxD,SAAA,CAAe;QASTjB,EAAA,CAAAO,SAAA,GACzC;QADyCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAa,WAAA,6BACzC;QAEuCb,EAAA,CAAAO,SAAA,GACvC;QADuCP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAa,WAAA,wBACvC", "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "group_r6", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "ɵɵtextInterpolate", "ctx_r1", "groupError", "ɵɵpipeBind1", "ctx_r4", "file", "ctx_r5", "fileError", "ModalImportTeamsComponent", "constructor", "_teamService", "_modalService", "_translateService", "_loadingService", "groups", "ngOnInit", "getGroups", "seasonId", "getGroupBySeason", "subscribe", "res", "validateFile", "instant", "field", "groupId", "onGroupChange", "onImport", "show", "importTeams", "close", "triggerFileInput", "fileInput", "nativeElement", "click", "onClose", "dismiss", "downloadTemplate", "filename", "downloadFile", "saveFile", "data", "error", "console", "onFileChange", "$event", "target", "files", "length", "_", "ɵɵdirectiveInject", "i1", "TeamService", "i2", "NgbActiveModal", "i3", "TranslateService", "i4", "LoadingService", "_2", "selectors", "viewQuery", "ModalImportTeamsComponent_Query", "rf", "ctx", "ɵɵlistener", "ModalImportTeamsComponent_Template_button_click_7_listener", "ModalImportTeamsComponent_Template_ng_select_ngModelChange_15_listener", "ɵɵtemplate", "ModalImportTeamsComponent_ng_option_17_Template", "ModalImportTeamsComponent_span_18_Template", "ModalImportTeamsComponent_Template_button_click_24_listener", "ɵɵelement", "ModalImportTeamsComponent_span_28_Template", "ModalImportTeamsComponent_Template_input_change_29_listener", "ModalImportTeamsComponent_span_31_Template", "ModalImportTeamsComponent_span_32_Template", "ModalImportTeamsComponent_Template_a_click_34_listener", "ModalImportTeamsComponent_Template_button_click_38_listener", "ɵɵpropertyInterpolate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-management\\modal-import-teams\\modal-import-teams.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-management\\modal-import-teams\\modal-import-teams.component.ts"], "sourcesContent": ["<div>\r\n\r\n    <div class=\"modal-header\">\r\n        <div class=\"text-center w-100\">\r\n            <h3>\r\n                <span class=\"text-capitalize\">{{'Import Teams' | translate}}</span>\r\n            </h3>\r\n        </div>\r\n\r\n        <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\" (click)=\"onClose()\">\r\n            <span aria-hidden=\"true\">&times; </span>\r\n        </button>\r\n    </div>\r\n\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus class=\"m-2\">\r\n        <div class=\"col p-0 pr-1\">\r\n            <label class=\"form-label\" for=\"season\">{{ 'Group' | translate }}</label>\r\n            <ng-select  [(ngModel)]=\"groupId\" (ngModelChange)=\"onGroupChange()\" placeholder=\"{{ 'Select Group' | translate }}\" >\r\n                <ng-option *ngFor=\"let group of groups\" [value]=\"group.id\">\r\n                    {{ group.name }}\r\n                </ng-option>\r\n            </ng-select>\r\n            <span class=\"text-sm text-danger\" *ngIf=\"groupError\">{{ groupError }}</span>\r\n            \r\n\r\n        </div>\r\n\r\n        <div class=\"col mt-1 p-0 pr-1\">\r\n            <label class=\"form-label\" for=\"season\">{{ 'File' | translate }}</label>\r\n            <div class=\"custom-file-upload \">\r\n                <button class=\"btn btn-outline-primary btn-block\" (click)=\"triggerFileInput()\">\r\n                    <i class=\"fa-light fa-upload\"></i>\r\n                    {{'Upload File' | translate}}</button>\r\n                <span class=\"text-secondary\" *ngIf=\"!file\">{{'No file selected' | translate}}</span>\r\n                <input type=\"file\" accept=\".csv\" #fileInput class=\"form-control-file\" (change)=\"onFileChange($event)\">\r\n                <span *ngIf=\"file\" class=\"text-break \">{{ file.name }}</span>\r\n                \r\n            </div>\r\n            <span class=\"text-sm text-danger\" *ngIf=\"fileError\">{{ fileError }}</span>\r\n            \r\n            \r\n        </div>\r\n\r\n        <div class=\"m-2 d-flex justify-content-between text-center align-items-center\">\r\n\r\n         \r\n            <a (click)=\"downloadTemplate('sample.csv')\"  href=\"javascript:void(0)\" >\r\n                <i class=\"fa-light fa-download\"></i> {{'Sample file' | translate}}\r\n            </a>\r\n            <button type=\"button\" class=\"btn btn-primary float-right\" (click)=\"onImport()\">\r\n                <i class=\"fa-light fa-upload\"></i> {{'Import' | translate}}\r\n            </button>\r\n\r\n        </div>\r\n    </div>\r\n\r\n</div>", "import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { Input } from '@angular/core';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\n@Component({\r\n  selector: 'app-modal-import-teams',\r\n  templateUrl: './modal-import-teams.component.html',\r\n  styleUrls: ['./modal-import-teams.component.scss'],\r\n})\r\nexport class ModalImportTeamsComponent {\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  @Input() seasonId: any;\r\n  groupId: any;\r\n  groups: any = [];\r\n  fileError: string;\r\n  groupError: string;\r\n  file: any;\r\n\r\n  constructor(\r\n    private _teamService: TeamService,\r\n    private _modalService: NgbActiveModal,\r\n    private _translateService: TranslateService,\r\n    private _loadingService: LoadingService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.getGroups(this.seasonId);\r\n  }\r\n\r\n  getGroups(seasonId: any) {\r\n    return this._teamService.getGroupBySeason(seasonId).subscribe((res) => {\r\n      this.groups = res;\r\n    })\r\n  }\r\n\r\n  validateFile() {\r\n    \r\n    if (!this.file) {\r\n      this.fileError =  this._translateService.instant('This field is required', {field: this._translateService.instant('File')});\r\n\r\n    }\r\n\r\n    if (!this.groupId) {\r\n      this.groupError = this._translateService.instant('This field is required', {field: this._translateService.instant('Group')});\r\n    }\r\n\r\n    return this.file && this.groupId;\r\n  }\r\n\r\n  onGroupChange() {\r\n    this.groupError = '';\r\n  }\r\n\r\n  onImport() {\r\n    \r\n    if (!this.validateFile()) {\r\n      return;\r\n    }\r\n\r\n    this._loadingService.show();\r\n\r\n    this._teamService\r\n      .importTeams(this.file, this.seasonId, this.groupId)\r\n      .subscribe((res) => {\r\n        this._modalService.close(res);\r\n      });\r\n  }\r\n\r\n  triggerFileInput() {\r\n    this.fileError = '';\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onClose() {\r\n    this._modalService.dismiss();\r\n  }\r\n\r\n  downloadTemplate(filename: string): void {\r\n    this._teamService.downloadFile(filename).subscribe(\r\n      (res) => {\r\n        this._teamService.saveFile(res.data, res.filename);\r\n      },\r\n      (error) => {\r\n        console.error('Error downloading file:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  onFileChange($event) {\r\n    if ($event.target.files.length > 0) {\r\n      this.file = $event.target.files[0];\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n/**\n * @module watchdog/utils/getsubnodes\n */\n/* globals EventTarget, Event */\nexport default function getSubNodes(head, excludedProperties = new Set()) {\n  const nodes = [head];\n  // @if CK_DEBUG_WATCHDOG // const prevNodeMap = new Map();\n  // Nodes are stored to prevent infinite looping.\n  const subNodes = new Set();\n  let nodeIndex = 0;\n  while (nodes.length > nodeIndex) {\n    // Incrementing the iterator is much faster than changing size of the array with Array.prototype.shift().\n    const node = nodes[nodeIndex++];\n    if (subNodes.has(node) || !shouldNodeBeIncluded(node) || excludedProperties.has(node)) {\n      continue;\n    }\n    subNodes.add(node);\n    // Handle arrays, maps, sets, custom collections that implements `[ Symbol.iterator ]()`, etc.\n    if (Symbol.iterator in node) {\n      // The custom editor iterators might cause some problems if the editor is crashed.\n      try {\n        for (const n of node) {\n          nodes.push(n);\n          // @if CK_DEBUG_WATCHDOG // if ( !prevNodeMap.has( n ) ) {\n          // @if CK_DEBUG_WATCHDOG // \tprevNodeMap.set( n, node );\n          // @if CK_DEBUG_WATCHDOG // }\n        }\n      } catch (err) {\n        // Do not log errors for broken structures\n        // since we are in the error handling process already.\n        // eslint-disable-line no-empty\n      }\n    } else {\n      for (const key in node) {\n        // We share a reference via the protobuf library within the editors,\n        // hence the shared value should be skipped. Although, it's not a perfect\n        // solution since new places like that might occur in the future.\n        if (key === 'defaultValue') {\n          continue;\n        }\n        nodes.push(node[key]);\n        // @if CK_DEBUG_WATCHDOG // if ( !prevNodeMap.has( node[ key ] ) ) {\n        // @if CK_DEBUG_WATCHDOG // \tprevNodeMap.set( node[ key ], node );\n        // @if CK_DEBUG_WATCHDOG // }\n      }\n    }\n  }\n  // @if CK_DEBUG_WATCHDOG // return { subNodes, prevNodeMap } as any;\n  return subNodes;\n}\nfunction shouldNodeBeIncluded(node) {\n  const type = Object.prototype.toString.call(node);\n  const typeOfNode = typeof node;\n  return !(typeOfNode === 'number' || typeOfNode === 'boolean' || typeOfNode === 'string' || typeOfNode === 'symbol' || typeOfNode === 'function' || type === '[object Date]' || type === '[object RegExp]' || type === '[object Module]' || node === undefined || node === null ||\n  // This flag is meant to exclude singletons shared across editor instances. So when an error is thrown in one editor,\n  // the other editors connected through the reference to the same singleton are not restarted. This is a temporary workaround\n  // until a better solution is found.\n  // More in https://github.com/ckeditor/ckeditor5/issues/12292.\n  node._watchdogExcluded ||\n  // Skip native DOM objects, e.g. Window, nodes, events, etc.\n  node instanceof EventTarget || node instanceof Event);\n}", "map": {"version": 3, "names": ["getSubNodes", "head", "excludedProperties", "Set", "nodes", "subNodes", "nodeIndex", "length", "node", "has", "shouldNodeBeIncluded", "add", "Symbol", "iterator", "n", "push", "err", "key", "type", "Object", "prototype", "toString", "call", "typeOfNode", "undefined", "_watchdogExcluded", "EventTarget", "Event"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@ckeditor/ckeditor5-watchdog/src/utils/getsubnodes.js"], "sourcesContent": ["/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n/**\n * @module watchdog/utils/getsubnodes\n */\n/* globals EventTarget, Event */\nexport default function getSubNodes(head, excludedProperties = new Set()) {\n    const nodes = [head];\n    // @if CK_DEBUG_WATCHDOG // const prevNodeMap = new Map();\n    // Nodes are stored to prevent infinite looping.\n    const subNodes = new Set();\n    let nodeIndex = 0;\n    while (nodes.length > nodeIndex) {\n        // Incrementing the iterator is much faster than changing size of the array with Array.prototype.shift().\n        const node = nodes[nodeIndex++];\n        if (subNodes.has(node) || !shouldNodeBeIncluded(node) || excludedProperties.has(node)) {\n            continue;\n        }\n        subNodes.add(node);\n        // Handle arrays, maps, sets, custom collections that implements `[ Symbol.iterator ]()`, etc.\n        if (Symbol.iterator in node) {\n            // The custom editor iterators might cause some problems if the editor is crashed.\n            try {\n                for (const n of node) {\n                    nodes.push(n);\n                    // @if CK_DEBUG_WATCHDOG // if ( !prevNodeMap.has( n ) ) {\n                    // @if CK_DEBUG_WATCHDOG // \tprevNodeMap.set( n, node );\n                    // @if CK_DEBUG_WATCHDOG // }\n                }\n            }\n            catch (err) {\n                // Do not log errors for broken structures\n                // since we are in the error handling process already.\n                // eslint-disable-line no-empty\n            }\n        }\n        else {\n            for (const key in node) {\n                // We share a reference via the protobuf library within the editors,\n                // hence the shared value should be skipped. Although, it's not a perfect\n                // solution since new places like that might occur in the future.\n                if (key === 'defaultValue') {\n                    continue;\n                }\n                nodes.push(node[key]);\n                // @if CK_DEBUG_WATCHDOG // if ( !prevNodeMap.has( node[ key ] ) ) {\n                // @if CK_DEBUG_WATCHDOG // \tprevNodeMap.set( node[ key ], node );\n                // @if CK_DEBUG_WATCHDOG // }\n            }\n        }\n    }\n    // @if CK_DEBUG_WATCHDOG // return { subNodes, prevNodeMap } as any;\n    return subNodes;\n}\nfunction shouldNodeBeIncluded(node) {\n    const type = Object.prototype.toString.call(node);\n    const typeOfNode = typeof node;\n    return !(typeOfNode === 'number' ||\n        typeOfNode === 'boolean' ||\n        typeOfNode === 'string' ||\n        typeOfNode === 'symbol' ||\n        typeOfNode === 'function' ||\n        type === '[object Date]' ||\n        type === '[object RegExp]' ||\n        type === '[object Module]' ||\n        node === undefined ||\n        node === null ||\n        // This flag is meant to exclude singletons shared across editor instances. So when an error is thrown in one editor,\n        // the other editors connected through the reference to the same singleton are not restarted. This is a temporary workaround\n        // until a better solution is found.\n        // More in https://github.com/ckeditor/ckeditor5/issues/12292.\n        node._watchdogExcluded ||\n        // Skip native DOM objects, e.g. Window, nodes, events, etc.\n        node instanceof EventTarget ||\n        node instanceof Event);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAAWA,CAACC,IAAI,EAAEC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC,EAAE;EACtE,MAAMC,KAAK,GAAG,CAACH,IAAI,CAAC;EACpB;EACA;EACA,MAAMI,QAAQ,GAAG,IAAIF,GAAG,CAAC,CAAC;EAC1B,IAAIG,SAAS,GAAG,CAAC;EACjB,OAAOF,KAAK,CAACG,MAAM,GAAGD,SAAS,EAAE;IAC7B;IACA,MAAME,IAAI,GAAGJ,KAAK,CAACE,SAAS,EAAE,CAAC;IAC/B,IAAID,QAAQ,CAACI,GAAG,CAACD,IAAI,CAAC,IAAI,CAACE,oBAAoB,CAACF,IAAI,CAAC,IAAIN,kBAAkB,CAACO,GAAG,CAACD,IAAI,CAAC,EAAE;MACnF;IACJ;IACAH,QAAQ,CAACM,GAAG,CAACH,IAAI,CAAC;IAClB;IACA,IAAII,MAAM,CAACC,QAAQ,IAAIL,IAAI,EAAE;MACzB;MACA,IAAI;QACA,KAAK,MAAMM,CAAC,IAAIN,IAAI,EAAE;UAClBJ,KAAK,CAACW,IAAI,CAACD,CAAC,CAAC;UACb;UACA;UACA;QACJ;MACJ,CAAC,CACD,OAAOE,GAAG,EAAE;QACR;QACA;QACA;MAAA;IAER,CAAC,MACI;MACD,KAAK,MAAMC,GAAG,IAAIT,IAAI,EAAE;QACpB;QACA;QACA;QACA,IAAIS,GAAG,KAAK,cAAc,EAAE;UACxB;QACJ;QACAb,KAAK,CAACW,IAAI,CAACP,IAAI,CAACS,GAAG,CAAC,CAAC;QACrB;QACA;QACA;MACJ;IACJ;EACJ;EACA;EACA,OAAOZ,QAAQ;AACnB;AACA,SAASK,oBAAoBA,CAACF,IAAI,EAAE;EAChC,MAAMU,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACd,IAAI,CAAC;EACjD,MAAMe,UAAU,GAAG,OAAOf,IAAI;EAC9B,OAAO,EAAEe,UAAU,KAAK,QAAQ,IAC5BA,UAAU,KAAK,SAAS,IACxBA,UAAU,KAAK,QAAQ,IACvBA,UAAU,KAAK,QAAQ,IACvBA,UAAU,KAAK,UAAU,IACzBL,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,iBAAiB,IAC1BV,IAAI,KAAKgB,SAAS,IAClBhB,IAAI,KAAK,IAAI;EACb;EACA;EACA;EACA;EACAA,IAAI,CAACiB,iBAAiB;EACtB;EACAjB,IAAI,YAAYkB,WAAW,IAC3BlB,IAAI,YAAYmB,KAAK,CAAC;AAC9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
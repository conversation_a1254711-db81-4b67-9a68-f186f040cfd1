{"ast": null, "code": "/**\n * @license\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://raw.githubusercontent.com/l-lin/angular-datatables/master/LICENSE\n */\n/**\n * @module\n * @description\n * Entry point from which you should import all public library APIs.\n */\nexport { DataTableDirective } from './src/angular-datatables.directive';\nexport { DataTablesModule } from './src/angular-datatables.module';", "map": {"version": 3, "names": ["DataTableDirective", "DataTablesModule"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/angular-datatables/index.js"], "sourcesContent": ["/**\n * @license\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://raw.githubusercontent.com/l-lin/angular-datatables/master/LICENSE\n */\n/**\n * @module\n * @description\n * Entry point from which you should import all public library APIs.\n */\nexport { DataTableDirective } from './src/angular-datatables.directive';\nexport { DataTablesModule } from './src/angular-datatables.module';\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,gBAAgB,QAAQ,iCAAiC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
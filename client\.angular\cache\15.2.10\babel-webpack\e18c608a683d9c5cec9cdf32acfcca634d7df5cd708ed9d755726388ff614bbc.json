{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DOCUMENT } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as Waves from 'node-waves';\nimport { App } from '@capacitor/app';\nimport { StatusBar } from '@capacitor/status-bar';\nimport Swal from 'sweetalert2';\nimport { menu } from 'app/menu/menu';\nimport { Capacitor } from '@capacitor/core';\nimport { Camera } from '@capacitor/camera';\nimport { environment } from 'environments/environment';\nimport { AppConfig } from './app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/cdk/platform\";\nimport * as i3 from \"@core/services/config.service\";\nimport * as i4 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i5 from \"@core/components/core-menu/core-menu.service\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"./services/auth.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@core/services/loading-screen.service\";\nimport * as i10 from \"./services/loading.service\";\nimport * as i11 from \"./services/fcm.service\";\nimport * as i12 from \"./layout/components/navbar/navbar-notification/notifications.service\";\nimport * as i13 from \"./services/settings.service\";\nimport * as i14 from \"@angular/common\";\nimport * as i15 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i16 from \"./services/navigation.service\";\nimport * as i17 from \"./services/commons.service\";\nimport * as i18 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i19 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i20 from \"@core/components/theme-customizer/theme-customizer.component\";\nimport * as i21 from \"app/layout/vertical/vertical-layout.component\";\nimport * as i22 from \"app/layout/horizontal/horizontal-layout.component\";\nconst _c0 = [\"modal_loading_screen\"];\nconst _c1 = [\"modal_install_pwa\"];\nfunction AppComponent_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"vertical-layout\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"horizontal-layout\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_ng_container_0_core_sidebar_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"core-sidebar\", 5)(1, \"a\", 6);\n    i0.ɵɵlistener(\"click\", function AppComponent_ng_container_0_core_sidebar_3_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.toggleSidebar(\"themeCustomizer\"));\n    });\n    i0.ɵɵelement(2, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"core-theme-customizer\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"invisibleOverlay\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"spinner\");\n    i0.ɵɵproperty(\"data-feather\", \"settings\");\n  }\n}\nfunction AppComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppComponent_ng_container_0_ng_container_1_Template, 2, 0, \"ng-container\", 0);\n    i0.ɵɵtemplate(2, AppComponent_ng_container_0_ng_container_2_Template, 2, 0, \"ng-container\", 0);\n    i0.ɵɵtemplate(3, AppComponent_ng_container_0_core_sidebar_3_Template, 4, 4, \"core-sidebar\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.coreConfig.layout.type === \"vertical\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.coreConfig.layout.type === \"horizontal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.coreConfig.layout.customizer);\n  }\n}\nfunction AppComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"div\", 11)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelement(7, \"div\", 12)(8, \"div\", 12)(9, \"div\", 12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 1, \"Loading\"), \" \");\n  }\n}\nfunction AppComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h4\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function AppComponent_ng_template_4_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const modal_r12 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r12.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵelementStart(5, \"span\", 16);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 17)(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelement(14, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelement(18, \"i\", 19);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementStart(24, \"b\");\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelement(27, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \"\\\" \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Install App\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 9, \"Install the app on your device to easily access it anytime. No app store. No download. No hasstle\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" 1. \", i0.ɵɵpipeBind1(13, 11, \"Open this page in Safari\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" 2. \", i0.ɵɵpipeBind1(17, 13, \"Tap on share icon\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 15, \"at the toolbar of the browser\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" 3. \", i0.ɵɵpipeBind1(23, 17, \"Find and Select\"), \" \\\"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(26, 19, \"Add to Home Screen\"), \" \");\n  }\n}\nexport class AppComponent {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {DOCUMENT} document\r\n   * @param {Title} _title\r\n   * @param {Renderer2} _renderer\r\n   * @param {ElementRef} _elementRef\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {CoreLoadingScreenService} _coreLoadingScreenService\r\n   * @param {CoreMenuService} _coreMenuService\r\n   * @param {CoreTranslationService} _coreTranslationService\r\n   * @param {TranslateService} _translateService\r\n   */\n  constructor(document, _title, platform, _renderer, _elementRef, _coreConfigService, _coreSidebarService, _coreMenuService, _translateService, _authService, _router, _coreLoadingScreenService, _loadingService, _fcmService, _notificationService, _settingsService, location, _modalService, _navigationService, _commonsService) {\n    this.document = document;\n    this._title = _title;\n    this.platform = platform;\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n    this._coreConfigService = _coreConfigService;\n    this._coreSidebarService = _coreSidebarService;\n    this._coreMenuService = _coreMenuService;\n    this._translateService = _translateService;\n    this._authService = _authService;\n    this._router = _router;\n    this._coreLoadingScreenService = _coreLoadingScreenService;\n    this._loadingService = _loadingService;\n    this._fcmService = _fcmService;\n    this._notificationService = _notificationService;\n    this._settingsService = _settingsService;\n    this.location = location;\n    this._modalService = _modalService;\n    this._navigationService = _navigationService;\n    this._commonsService = _commonsService;\n    this.showBtnInstallPwa = false;\n    this.hideStatusBar = /*#__PURE__*/_asyncToGenerator(function* () {\n      yield StatusBar.setBackgroundColor({\n        color: '#000'\n      });\n      yield StatusBar.hide();\n    });\n    _authService.currentUser.subscribe(x => {\n      if (x) {\n        _notificationService.currentUserValue = x;\n        _commonsService.simpleUploadConfig = {\n          // The URL that the images are uploaded to.\n          uploadUrl: `${environment.apiUrl}/send-messages/upload`,\n          headers: {\n            Accept: 'application/json, text/plain, */*',\n            'X-CSRF-TOKEN': 'CSRF-Token',\n            Authorization: `Bearer ${x.token}`,\n            'X-project-id': AppConfig.PROJECT_ID\n          }\n        };\n      }\n    });\n    // Get the application main menu\n    this.menu = JSON.parse(JSON.stringify(menu));\n    // Register the menu to the menu service\n    this._coreMenuService.register('main', this.menu);\n    // Set the main menu as our current menu\n    this._coreMenuService.setCurrentMenu('main');\n    // Add languages to the translation service\n    this._translateService.addLangs(['en', 'zh_HK']);\n    // This language will be used as a fallback when a translation isn't found in the current language\n    this._translateService.setDefaultLang('en');\n    // Set the private defaults\n    this._unsubscribeAll = new Subject();\n    if (_authService.currentUserValue) {\n      _settingsService.getInitSettings(false);\n    }\n  }\n  // Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    this._authService.getProfile().subscribe(data => {\n      // console.log('data', data);\n      this._coreMenuService.unregister('main');\n      this._coreMenuService.register('main', menu);\n      this._coreMenuService.setCurrentMenu('main');\n    });\n    // this._fcmService.listen();\n    this._notificationService.listen();\n    // This code handles the functionality of the back button\n    App.addListener('backButton', ({\n      canGoBack\n    }) => {\n      console.log('canGoBack', canGoBack);\n      // Check if there is anything in the browsing history\n      if (!canGoBack) {\n        // Exit the app if not\n        App.exitApp();\n      } else {\n        this._navigationService.back();\n      }\n    });\n    // Init wave effect (Ripple effect)\n    Waves.init();\n    this._loadingService.modalLoading = this.modal_loading_screen;\n    // Subscribe to config changes\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.coreConfig = config;\n      const appLanguage = this.coreConfig.app.appLanguage || 'en';\n      this._translateService.use(appLanguage);\n      setTimeout(() => {\n        this._translateService.setDefaultLang('en');\n        this._translateService.setDefaultLang(appLanguage);\n      });\n      // Remove default classes first\n      this._elementRef.nativeElement.classList.remove('vertical-layout', 'vertical-menu-modern', 'horizontal-layout', 'horizontal-menu');\n      // Add class based on config options\n      if (this.coreConfig.layout.type === 'vertical') {\n        this._elementRef.nativeElement.classList.add('vertical-layout', 'vertical-menu-modern');\n      } else if (this.coreConfig.layout.type === 'horizontal') {\n        this._elementRef.nativeElement.classList.add('horizontal-layout', 'horizontal-menu');\n      }\n      // Navbar\n      //--------\n      // Remove default classes first\n      this._elementRef.nativeElement.classList.remove('navbar-floating', 'navbar-static', 'navbar-sticky', 'navbar-hidden');\n      // Add class based on config options\n      if (this.coreConfig.layout.navbar.type === 'navbar-static-top') {\n        this._elementRef.nativeElement.classList.add('navbar-static');\n      } else if (this.coreConfig.layout.navbar.type === 'fixed-top') {\n        this._elementRef.nativeElement.classList.add('navbar-sticky');\n      } else if (this.coreConfig.layout.navbar.type === 'floating-nav') {\n        this._elementRef.nativeElement.classList.add('navbar-floating');\n      } else {\n        this._elementRef.nativeElement.classList.add('navbar-hidden');\n      }\n      // Footer\n      //--------\n      // Remove default classes first\n      this._elementRef.nativeElement.classList.remove('footer-fixed', 'footer-static', 'footer-hidden');\n      // Add class based on config options\n      if (this.coreConfig.layout.footer.type === 'footer-sticky') {\n        this._elementRef.nativeElement.classList.add('footer-fixed');\n      } else if (this.coreConfig.layout.footer.type === 'footer-static') {\n        this._elementRef.nativeElement.classList.add('footer-static');\n      } else {\n        this._elementRef.nativeElement.classList.add('footer-hidden');\n      }\n      // Blank layout\n      if (this.coreConfig.layout.menu.hidden && this.coreConfig.layout.navbar.hidden && this.coreConfig.layout.footer.hidden) {\n        this._elementRef.nativeElement.classList.add('blank-page');\n        // ! Fix: Transition issue while coming from blank page\n        this._renderer.setAttribute(this._elementRef.nativeElement.getElementsByClassName('app-content')[0], 'style', 'transition:none');\n      } else {\n        this._elementRef.nativeElement.classList.remove('blank-page');\n        // ! Fix: Transition issue while coming from blank page\n        setTimeout(() => {\n          if (this._elementRef.nativeElement.getElementsByClassName('app-content')[0]) {\n            this._renderer.setAttribute(this._elementRef.nativeElement.getElementsByClassName('app-content')[0], 'style', 'transition:300ms ease all');\n          }\n        }, 100);\n        // If navbar hidden\n        if (this.coreConfig.layout.navbar.hidden) {\n          this._elementRef.nativeElement.classList.add('navbar-hidden');\n        }\n        // Menu (Vertical menu hidden)\n        if (this.coreConfig.layout.menu.hidden) {\n          this._renderer.setAttribute(this._elementRef.nativeElement, 'data-col', '1-column');\n        } else {\n          this._renderer.removeAttribute(this._elementRef.nativeElement, 'data-col');\n        }\n        // Footer\n        if (this.coreConfig.layout.footer.hidden) {\n          this._elementRef.nativeElement.classList.add('footer-hidden');\n        }\n      }\n      // Skin Class (Adding to body as it requires highest priority)\n      if (this.coreConfig.layout.skin !== '' && this.coreConfig.layout.skin !== undefined) {\n        this.document.body.classList.remove('default-layout', 'bordered-layout', 'dark-layout', 'semi-dark-layout');\n        this.document.body.classList.add(this.coreConfig.layout.skin + '-layout');\n      }\n    });\n    // Set the application page title\n    this._title.setTitle(this.coreConfig.app.appTitle);\n    // create trigger when updating user\n    this._authService.currentUser.subscribe(stage => {\n      if (stage != null) {\n        this._coreConfigService.setConfig({\n          app: {\n            appLanguage: stage.language\n          }\n        }, {\n          emitEvent: true\n        });\n        setTimeout(() => {\n          this._translateService.use(stage.language);\n        }, 200);\n        // this._router.navigate([\"/home\"]);\n      }\n    });\n\n    this._coreConfigService.setConfig({\n      layout: {\n        type: 'vertical'\n      }\n    });\n    this.getStreamConfig();\n    // check url is egress\n    // egress is streaming token\n    if (!this.location.path().includes('egress')) {\n      console.log('not egress');\n      this.requestNotificationPermission();\n      this.requestMediaPermission();\n      this._settingsService.getNotificationSettings().subscribe(data => {\n        console.log(data);\n        const title = data.title;\n        const body = data.body;\n        const is_on = data.is_on;\n        const notification = sessionStorage.getItem('notification');\n        if (!is_on) return;\n        if (notification == 'true') return;\n        Swal.fire({\n          title: title,\n          text: body,\n          confirmButtonText: 'OK'\n        }).then(result => {\n          if (result.isConfirmed) {\n            sessionStorage.setItem('notification', 'true');\n            Swal.close();\n          }\n        });\n      });\n    } else {\n      console.log('egress');\n    }\n  }\n  getStreamConfig() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('getStreamConfig');\n      _this._settingsService.getSettingNoAuth().subscribe(res => {\n        let livekit = res.find(setting => setting.key === AppConfig.SETTINGS_KEYS.LIVEKIT);\n        const name_settings = res.find(setting => setting.key === AppConfig.SETTINGS_KEYS.NAME_SETTINGS);\n        if (name_settings) {\n          localStorage.setItem('name_settings', JSON.stringify(name_settings.value));\n        }\n        if (livekit) {\n          localStorage.setItem('livekit', JSON.stringify(livekit.value));\n        }\n      });\n    })();\n  }\n  requestNotificationPermission() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Initialize push notifications\n      _this2._fcmService.initPush();\n      // Listen for incoming messages\n      _this2._fcmService.listenToMessages();\n      // Optionally log the current FCM token\n      const token = _this2._fcmService.getToken();\n      console.log('FCM Token:', token);\n    })();\n  }\n  requestMediaPermission() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (Capacitor.isNativePlatform()) {\n        _this3.hideStatusBar();\n        let status = yield Camera.checkPermissions();\n        switch (status.camera) {\n          case 'denied':\n          case 'prompt':\n          case 'prompt-with-rationale':\n            return yield Camera.requestPermissions();\n          default:\n            return status.camera;\n        }\n      } else {\n        // let res = await navigator.mediaDevices.getUserMedia({\n        //   video: true,\n        //   audio: true,\n        // });\n        // res.getTracks().forEach((track) => {\n        //   track.stop();\n        // });\n        // return res;\n      }\n    })();\n  }\n  ngAfterViewInit() {\n    // setTimeout(() => {\n    //   if (Capacitor.isNativePlatform()) return;\n    //   if ('serviceWorker' in navigator && 'PushManager' in window) {\n    //     window.addEventListener('beforeinstallprompt', (event) => {\n    //       event.preventDefault();\n    //       this.deferredPrompt = event;\n    //       this.showBtnInstallPwa = true;\n    //     });\n    //     if (\n    //       this.platform.IOS ||\n    //       (this.platform.SAFARI && (window as any).navigator.standalone)\n    //     ) {\n    //       this.showBtnInstallPwa = false;\n    //     }\n    //     console.log('supported', this.platform);\n    //   } else {\n    //     console.log(\n    //       'not supported',\n    //       (window as any).navigator.standalone,\n    //       this.platform\n    //     );\n    //     this.showBtnInstallPwa = true;\n    //     if (\n    //       this.platform.IOS ||\n    //       (this.platform.SAFARI && (window as any).navigator.standalone)\n    //     ) {\n    //       this.showBtnInstallPwa = false;\n    //     }\n    //   }\n    // }, 1000);\n  }\n  onInstallCLick() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      console.log('install button clicked');\n      if (!_this4.platform.IOS && !_this4.platform.SAFARI) {\n        console.log('android', _this4.deferredPrompt);\n        if (_this4.deferredPrompt) {\n          const result = yield _this4.deferredPrompt.prompt();\n          console.log(`Install prompt was: ${result.outcome}`);\n        }\n      } else {\n        _this4._modalService.dismissAll();\n        console.log('ios');\n        let modalRef = _this4._modalService.open(_this4.modal_install_pwa, {\n          centered: true,\n          backdrop: 'static',\n          size: 'md'\n        });\n      }\n    })();\n  }\n  /**\r\n   * On destroy\r\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  // Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Toggle sidebar open\r\n   *\r\n   * @param key\r\n   */\n  toggleSidebar(key) {\n    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.Title), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.CoreConfigService), i0.ɵɵdirectiveInject(i4.CoreSidebarService), i0.ɵɵdirectiveInject(i5.CoreMenuService), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.CoreLoadingScreenService), i0.ɵɵdirectiveInject(i10.LoadingService), i0.ɵɵdirectiveInject(i11.FcmService), i0.ɵɵdirectiveInject(i12.NotificationsService), i0.ɵɵdirectiveInject(i13.SettingsService), i0.ɵɵdirectiveInject(i14.Location), i0.ɵɵdirectiveInject(i15.NgbModal), i0.ɵɵdirectiveInject(i16.NavigationService), i0.ɵɵdirectiveInject(i17.CommonsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    viewQuery: function AppComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modal_loading_screen = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modal_install_pwa = _t.first);\n      }\n    },\n    decls: 7,\n    vars: 2,\n    consts: [[4, \"ngIf\"], [\"modal_loading_screen\", \"\"], [\"modal_install_pwa\", \"\"], [\"name\", \"no_layout\"], [\"name\", \"themeCustomizer\", \"class\", \"customizer d-none d-md-block\", 3, \"invisibleOverlay\", 4, \"ngIf\"], [\"name\", \"themeCustomizer\", 1, \"customizer\", \"d-none\", \"d-md-block\", 3, \"invisibleOverlay\"], [1, \"customizer-toggle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 3, \"click\"], [3, \"data-feather\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border\", \"text-primary\", 2, \"width\", \"4em\", \"height\", \"4em\"], [1, \"row\", \"mt-2\"], [1, \"col\", \"text-center\"], [1, \"spinner-grow\", \"spinner-grow-xs\", \"text-dark\"], [1, \"modal-header\"], [\"id\", \"modal-basic-title\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"fa-lg\", \"fa-brands\", \"fa-safari\"], [1, \"fa-lg\", \"fa-solid\", \"fa-arrow-up-from-square\"], [1, \"fa-regular\", \"fa-square-plus\", \"fa-lg\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AppComponent_ng_container_0_Template, 4, 3, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, AppComponent_ng_container_1_Template, 2, 0, \"ng-container\", 0);\n        i0.ɵɵtemplate(2, AppComponent_ng_template_2_Template, 10, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(4, AppComponent_ng_template_4_Template, 29, 21, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelement(6, \"router-outlet\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx._coreConfigService.visible_layout);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx._coreConfigService.visible_layout);\n      }\n    },\n    dependencies: [i8.RouterOutlet, i14.NgIf, i18.FeatherIconDirective, i19.CoreSidebarComponent, i20.CoreThemeCustomizerComponent, i21.VerticalLayoutComponent, i22.HorizontalLayoutComponent, i6.TranslatePipe],\n    styles: [\"#btn-install-pwa {\\n  position: fixed;\\n  bottom: 50px;\\n  right: 0px;\\n  width: 50px;\\n  height: 50px;\\n  transform: translateX(-50%);\\n  z-index: 10;\\n  padding: 10px;\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksZUFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSwyQkFBQTtFQUNBLFdBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIiNidG4taW5zdGFsbC1wd2Ege1xyXG4gICAgcG9zaXRpb246IGZpeGVkO1xyXG4gICAgYm90dG9tOiA1MHB4O1xyXG4gICAgcmlnaHQ6IDBweDtcclxuICAgIHdpZHRoOiA1MHB4O1xyXG4gICAgaGVpZ2h0OiA1MHB4O1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xyXG4gICAgei1pbmRleDogMTA7XHJcbiAgICBwYWRkaW5nOiAxMHB4O1xyXG4gICAgY29sb3I6IHdoaXRlO1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";AAUA,SAASA,QAAQ,QAAQ,iBAAiB;AAG1C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,SAAS,QAAe,uBAAuB;AACxD,OAAOC,IAAI,MAAM,aAAa;AAQ9B,SAASC,IAAI,QAAQ,eAAe;AAQpC,SAASC,SAAS,QAAQ,iBAAiB;AAE3C,SAASC,MAAM,QAAQ,mBAAmB;AAI1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,SAAS,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICxCtCC,EAAA,CAAAC,uBAAA,GAA4D;IAC1DD,EAAA,CAAAE,SAAA,sBAAmC;IACrCF,EAAA,CAAAG,qBAAA,EAAe;;;;;IAIfH,EAAA,CAAAC,uBAAA,GAA8D;IAC5DD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,qBAAA,EAAe;;;;;;IAUfH,EAAA,CAAAI,cAAA,sBACuC;IAEnCJ,EAAA,CAAAK,UAAA,mBAAAC,uEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAc,iBAAiB,CAAC;IAAA,EAAC;IAC1CZ,EAAA,CAAAE,SAAA,cAA6D;IAC/DF,EAAA,CAAAa,YAAA,EAAI;IACJb,EAAA,CAAAE,SAAA,4BAA+C;IACjDF,EAAA,CAAAa,YAAA,EAAe;;;IAP2Db,EAAA,CAAAc,UAAA,0BAAyB;IAI7Dd,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,UAAA,WAAmB;IAA/ChB,EAAA,CAAAc,UAAA,4BAA2B;;;;;IAxBvCd,EAAA,CAAAC,uBAAA,GAAwD;IAEtDD,EAAA,CAAAiB,UAAA,IAAAC,mDAAA,0BAEe;IAIflB,EAAA,CAAAiB,UAAA,IAAAE,mDAAA,0BAEe;IAUfnB,EAAA,CAAAiB,UAAA,IAAAG,mDAAA,0BAOe;IAEjBpB,EAAA,CAAAG,qBAAA,EAAe;;;;IA3BEH,EAAA,CAAAe,SAAA,GAA2C;IAA3Cf,EAAA,CAAAc,UAAA,SAAAO,MAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAC,IAAA,gBAA2C;IAM3CxB,EAAA,CAAAe,SAAA,GAA6C;IAA7Cf,EAAA,CAAAc,UAAA,SAAAO,MAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAC,IAAA,kBAA6C;IAazDxB,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAc,UAAA,SAAAO,MAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAE,UAAA,CAAkC;;;;;IASvCzB,EAAA,CAAAC,uBAAA,GAAyD;IACvDD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,qBAAA,EAAe;;;;;IAGbH,EAAA,CAAAI,cAAA,aAA8D;IAC5DJ,EAAA,CAAAE,SAAA,aAAiH;IACjHF,EAAA,CAAAI,cAAA,cAAsB;IAGhBJ,EAAA,CAAA0B,MAAA,GACA;;IAAA1B,EAAA,CAAAE,SAAA,cACM;IAKRF,EAAA,CAAAa,YAAA,EAAK;;;IAPHb,EAAA,CAAAe,SAAA,GACA;IADAf,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA4B,WAAA,uBACA;;;;;;IAgBR5B,EAAA,CAAAI,cAAA,cAA0B;IACuBJ,EAAA,CAAA0B,MAAA,GAA6B;;IAAA1B,EAAA,CAAAa,YAAA,EAAK;IACjFb,EAAA,CAAAI,cAAA,iBAA8F;IAAvCJ,EAAA,CAAAK,UAAA,mBAAAwB,4DAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAO,aAAA,CAAAwB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,OAASjC,EAAA,CAAAW,WAAA,CAAAqB,SAAA,CAAAE,OAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAC3FlC,EAAA,CAAAI,cAAA,eAAyB;IAAAJ,EAAA,CAAA0B,MAAA,aAAO;IAAA1B,EAAA,CAAAa,YAAA,EAAO;IAI3Cb,EAAA,CAAAI,cAAA,cAAwB;IACnBJ,EAAA,CAAA0B,MAAA,GACU;;IAAA1B,EAAA,CAAAa,YAAA,EAAI;IACjBb,EAAA,CAAAI,cAAA,SAAG;IACDJ,EAAA,CAAA0B,MAAA,IAA8C;;IAAA1B,EAAA,CAAAE,SAAA,aAAyC;IACzFF,EAAA,CAAAa,YAAA,EAAI;IACJb,EAAA,CAAAI,cAAA,SAAG;IACDJ,EAAA,CAAA0B,MAAA,IACA;;IAAA1B,EAAA,CAAAE,SAAA,aAAsD;IACtDF,EAAA,CAAA0B,MAAA,IACF;;IAAA1B,EAAA,CAAAa,YAAA,EAAI;IACJb,EAAA,CAAAI,cAAA,SAAG;IACDJ,EAAA,CAAA0B,MAAA,IAAsC;;IAAA1B,EAAA,CAAAI,cAAA,SAAG;IAAAJ,EAAA,CAAA0B,MAAA,IAAqC;;IAAA1B,EAAA,CAAAE,SAAA,aAC9B;IAAAF,EAAA,CAAAa,YAAA,EAAI;IAAAb,EAAA,CAAA0B,MAAA,WACxD;IAHE1B,EAAA,CAAAa,YAAA,EAAG;;;IAjB4Cb,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA4B,WAAA,sBAA6B;IAOzE5B,EAAA,CAAAe,SAAA,GACU;IADVf,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA4B,WAAA,6GACU;IAEX5B,EAAA,CAAAe,SAAA,GAA8C;IAA9Cf,EAAA,CAAA2B,kBAAA,SAAA3B,EAAA,CAAA4B,WAAA,0CAA8C;IAG9C5B,EAAA,CAAAe,SAAA,GACA;IADAf,EAAA,CAAA2B,kBAAA,SAAA3B,EAAA,CAAA4B,WAAA,mCACA;IACA5B,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA4B,WAAA,+CACF;IAEE5B,EAAA,CAAAe,SAAA,GAAsC;IAAtCf,EAAA,CAAA2B,kBAAA,SAAA3B,EAAA,CAAA4B,WAAA,mCAAsC;IAAG5B,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAA2B,kBAAA,KAAA3B,EAAA,CAAA4B,WAAA,oCAAqC;;;AD1BpF,OAAM,MAAOQ,YAAY;EAiBvB;;;;;;;;;;;;;;EAcAC,YAC4BC,QAAa,EAC/BC,MAAa,EACdC,QAAkB,EACjBC,SAAoB,EACpBC,WAAuB,EACxBC,kBAAqC,EACpCC,mBAAuC,EACvCC,gBAAiC,EACjCC,iBAAmC,EACnCC,YAAyB,EACzBC,OAAe,EACfC,yBAAmD,EACpDC,eAA+B,EAC/BC,WAAuB,EACvBC,oBAA0C,EAC1CC,gBAAiC,EAChCC,QAAkB,EACnBC,aAAuB,EACvBC,kBAAqC,EACrCC,eAA+B;IAnBZ,KAAAnB,QAAQ,GAARA,QAAQ;IAC1B,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,yBAAyB,GAAzBA,yBAAyB;IAC1B,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACf,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IA9CxB,KAAAC,iBAAiB,GAAY,KAAK;IAiUlC,KAAAC,aAAa,gBAAAC,iBAAA,CAAG,aAAW;MACzB,MAAMnE,SAAS,CAACoE,kBAAkB,CAAC;QAAEC,KAAK,EAAE;MAAM,CAAE,CAAC;MACrD,MAAMrE,SAAS,CAACsE,IAAI,EAAE;IACxB,CAAC;IApRChB,YAAY,CAACiB,WAAW,CAACC,SAAS,CAAEC,CAAC,IAAI;MACvC,IAAIA,CAAC,EAAE;QACLd,oBAAoB,CAACe,gBAAgB,GAAGD,CAAC;QACzCT,eAAe,CAACW,kBAAkB,GAAG;UACnC;UACAC,SAAS,EAAE,GAAGvE,WAAW,CAACwE,MAAM,uBAAuB;UACvDC,OAAO,EAAE;YACPC,MAAM,EAAE,mCAAmC;YAC3C,cAAc,EAAE,YAAY;YAC5BC,aAAa,EAAE,UAAUP,CAAC,CAACQ,KAAK,EAAE;YAClC,cAAc,EAAE3E,SAAS,CAAC4E;;SAE7B;;IAEL,CAAC,CAAC;IACF;IACA,IAAI,CAAChF,IAAI,GAAGiF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACnF,IAAI,CAAC,CAAC;IAE5C;IACA,IAAI,CAACkD,gBAAgB,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpF,IAAI,CAAC;IAEjD;IACA,IAAI,CAACkD,gBAAgB,CAACmC,cAAc,CAAC,MAAM,CAAC;IAE5C;IACA,IAAI,CAAClC,iBAAiB,CAACmC,QAAQ,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEhD;IACA,IAAI,CAACnC,iBAAiB,CAACoC,cAAc,CAAC,IAAI,CAAC;IAE3C;IACA,IAAI,CAACC,eAAe,GAAG,IAAI9F,OAAO,EAAE;IACpC,IAAI0D,YAAY,CAACoB,gBAAgB,EAAE;MACjCd,gBAAgB,CAAC+B,eAAe,CAAC,KAAK,CAAC;;EAE3C;EAEA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACtC,YAAY,CAACuC,UAAU,EAAE,CAACrB,SAAS,CAAEsB,IAAI,IAAI;MAChD;MACA,IAAI,CAAC1C,gBAAgB,CAAC2C,UAAU,CAAC,MAAM,CAAC;MACxC,IAAI,CAAC3C,gBAAgB,CAACkC,QAAQ,CAAC,MAAM,EAAEpF,IAAI,CAAC;MAC5C,IAAI,CAACkD,gBAAgB,CAACmC,cAAc,CAAC,MAAM,CAAC;IAC9C,CAAC,CAAC;IACF;IACA,IAAI,CAAC5B,oBAAoB,CAACqC,MAAM,EAAE;IAClC;IACAjG,GAAG,CAACkG,WAAW,CAAC,YAAY,EAAE,CAAC;MAAEC;IAAS,CAAE,KAAI;MAC9CC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,SAAS,CAAC;MAEnC;MACA,IAAI,CAACA,SAAS,EAAE;QACd;QACAnG,GAAG,CAACsG,OAAO,EAAE;OACd,MAAM;QACL,IAAI,CAACtC,kBAAkB,CAACuC,IAAI,EAAE;;IAElC,CAAC,CAAC;IAEF;IACAxG,KAAK,CAACyG,IAAI,EAAE;IACZ,IAAI,CAAC9C,eAAe,CAAC+C,YAAY,GAAG,IAAI,CAACC,oBAAoB;IAC7D;IACA,IAAI,CAACvD,kBAAkB,CAACwD,MAAM,CAC3BC,IAAI,CAAC9G,SAAS,CAAC,IAAI,CAAC6F,eAAe,CAAC,CAAC,CACrClB,SAAS,CAAEkC,MAAM,IAAI;MACpB,IAAI,CAAC7E,UAAU,GAAG6E,MAAM;MACxB,MAAME,WAAW,GAAG,IAAI,CAAC/E,UAAU,CAACgF,GAAG,CAACD,WAAW,IAAI,IAAI;MAC3D,IAAI,CAACvD,iBAAiB,CAACyD,GAAG,CAACF,WAAW,CAAC;MAEvCG,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1D,iBAAiB,CAACoC,cAAc,CAAC,IAAI,CAAC;QAC3C,IAAI,CAACpC,iBAAiB,CAACoC,cAAc,CAACmB,WAAW,CAAC;MACpD,CAAC,CAAC;MAEF;MACA,IAAI,CAAC3D,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACC,MAAM,CAC7C,iBAAiB,EACjB,sBAAsB,EACtB,mBAAmB,EACnB,iBAAiB,CAClB;MACD;MACA,IAAI,IAAI,CAACrF,UAAU,CAACC,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;QAC9C,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAC1C,iBAAiB,EACjB,sBAAsB,CACvB;OACF,MAAM,IAAI,IAAI,CAACtF,UAAU,CAACC,MAAM,CAACC,IAAI,KAAK,YAAY,EAAE;QACvD,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAC1C,mBAAmB,EACnB,iBAAiB,CAClB;;MAGH;MACA;MAEA;MACA,IAAI,CAAClE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACC,MAAM,CAC7C,iBAAiB,EACjB,eAAe,EACf,eAAe,EACf,eAAe,CAChB;MAED;MACA,IAAI,IAAI,CAACrF,UAAU,CAACC,MAAM,CAACsF,MAAM,CAACrF,IAAI,KAAK,mBAAmB,EAAE;QAC9D,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;OAC9D,MAAM,IAAI,IAAI,CAACtF,UAAU,CAACC,MAAM,CAACsF,MAAM,CAACrF,IAAI,KAAK,WAAW,EAAE;QAC7D,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;OAC9D,MAAM,IAAI,IAAI,CAACtF,UAAU,CAACC,MAAM,CAACsF,MAAM,CAACrF,IAAI,KAAK,cAAc,EAAE;QAChE,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,iBAAiB,CAAC;OAChE,MAAM;QACL,IAAI,CAAClE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;;MAG/D;MACA;MAEA;MACA,IAAI,CAAClE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACC,MAAM,CAC7C,cAAc,EACd,eAAe,EACf,eAAe,CAChB;MAED;MACA,IAAI,IAAI,CAACrF,UAAU,CAACC,MAAM,CAACuF,MAAM,CAACtF,IAAI,KAAK,eAAe,EAAE;QAC1D,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,cAAc,CAAC;OAC7D,MAAM,IAAI,IAAI,CAACtF,UAAU,CAACC,MAAM,CAACuF,MAAM,CAACtF,IAAI,KAAK,eAAe,EAAE;QACjE,IAAI,CAACkB,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;OAC9D,MAAM;QACL,IAAI,CAAClE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;;MAG/D;MACA,IACE,IAAI,CAACtF,UAAU,CAACC,MAAM,CAAC5B,IAAI,CAACoH,MAAM,IAClC,IAAI,CAACzF,UAAU,CAACC,MAAM,CAACsF,MAAM,CAACE,MAAM,IACpC,IAAI,CAACzF,UAAU,CAACC,MAAM,CAACuF,MAAM,CAACC,MAAM,EACpC;QACA,IAAI,CAACrE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;QAC1D;QACA,IAAI,CAACnE,SAAS,CAACuE,YAAY,CACzB,IAAI,CAACtE,WAAW,CAAC+D,aAAa,CAACQ,sBAAsB,CACnD,aAAa,CACd,CAAC,CAAC,CAAC,EACJ,OAAO,EACP,iBAAiB,CAClB;OACF,MAAM;QACL,IAAI,CAACvE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;QAC7D;QACAH,UAAU,CAAC,MAAK;UACd,IACE,IAAI,CAAC9D,WAAW,CAAC+D,aAAa,CAACQ,sBAAsB,CACnD,aAAa,CACd,CAAC,CAAC,CAAC,EACJ;YACA,IAAI,CAACxE,SAAS,CAACuE,YAAY,CACzB,IAAI,CAACtE,WAAW,CAAC+D,aAAa,CAACQ,sBAAsB,CACnD,aAAa,CACd,CAAC,CAAC,CAAC,EACJ,OAAO,EACP,2BAA2B,CAC5B;;QAEL,CAAC,EAAE,GAAG,CAAC;QACP;QACA,IAAI,IAAI,CAAC3F,UAAU,CAACC,MAAM,CAACsF,MAAM,CAACE,MAAM,EAAE;UACxC,IAAI,CAACrE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;;QAE/D;QACA,IAAI,IAAI,CAACtF,UAAU,CAACC,MAAM,CAAC5B,IAAI,CAACoH,MAAM,EAAE;UACtC,IAAI,CAACtE,SAAS,CAACuE,YAAY,CACzB,IAAI,CAACtE,WAAW,CAAC+D,aAAa,EAC9B,UAAU,EACV,UAAU,CACX;SACF,MAAM;UACL,IAAI,CAAChE,SAAS,CAACyE,eAAe,CAC5B,IAAI,CAACxE,WAAW,CAAC+D,aAAa,EAC9B,UAAU,CACX;;QAEH;QACA,IAAI,IAAI,CAACnF,UAAU,CAACC,MAAM,CAACuF,MAAM,CAACC,MAAM,EAAE;UACxC,IAAI,CAACrE,WAAW,CAAC+D,aAAa,CAACC,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;;;MAIjE;MACA,IACE,IAAI,CAACtF,UAAU,CAACC,MAAM,CAAC4F,IAAI,KAAK,EAAE,IAClC,IAAI,CAAC7F,UAAU,CAACC,MAAM,CAAC4F,IAAI,KAAKC,SAAS,EACzC;QACA,IAAI,CAAC9E,QAAQ,CAAC+E,IAAI,CAACX,SAAS,CAACC,MAAM,CACjC,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,kBAAkB,CACnB;QACD,IAAI,CAACrE,QAAQ,CAAC+E,IAAI,CAACX,SAAS,CAACE,GAAG,CAC9B,IAAI,CAACtF,UAAU,CAACC,MAAM,CAAC4F,IAAI,GAAG,SAAS,CACxC;;IAEL,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC5E,MAAM,CAAC+E,QAAQ,CAAC,IAAI,CAAChG,UAAU,CAACgF,GAAG,CAACiB,QAAQ,CAAC;IAElD;IACA,IAAI,CAACxE,YAAY,CAACiB,WAAW,CAACC,SAAS,CAAEuD,KAAU,IAAI;MACrD,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,IAAI,CAAC7E,kBAAkB,CAAC8E,SAAS,CAC/B;UAAEnB,GAAG,EAAE;YAAED,WAAW,EAAEmB,KAAK,CAACE;UAAQ;QAAE,CAAE,EACxC;UAAEC,SAAS,EAAE;QAAI,CAAE,CACpB;QACDnB,UAAU,CAAC,MAAK;UACd,IAAI,CAAC1D,iBAAiB,CAACyD,GAAG,CAACiB,KAAK,CAACE,QAAQ,CAAC;QAE5C,CAAC,EAAE,GAAG,CAAC;QACP;;IAEJ,CAAC,CAAC;;IAEF,IAAI,CAAC/E,kBAAkB,CAAC8E,SAAS,CAAC;MAChClG,MAAM,EAAE;QACNC,IAAI,EAAE;;KAET,CAAC;IAEF,IAAI,CAACoG,eAAe,EAAE;IAEtB;IACA;IACA,IAAI,CAAC,IAAI,CAACtE,QAAQ,CAACuE,IAAI,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5ClC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MACzB,IAAI,CAACkC,6BAA6B,EAAE;MACpC,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAAC3E,gBAAgB,CAAC4E,uBAAuB,EAAE,CAAChE,SAAS,CAAEsB,IAAI,IAAI;QACjEK,OAAO,CAACC,GAAG,CAACN,IAAI,CAAC;QACjB,MAAM2C,KAAK,GAAG3C,IAAI,CAAC2C,KAAK;QACxB,MAAMb,IAAI,GAAG9B,IAAI,CAAC8B,IAAI;QACtB,MAAMc,KAAK,GAAG5C,IAAI,CAAC4C,KAAK;QACxB,MAAMC,YAAY,GAAGC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC;QAE3D,IAAI,CAACH,KAAK,EAAE;QACZ,IAAIC,YAAY,IAAI,MAAM,EAAE;QAE5B1I,IAAI,CAAC6I,IAAI,CAAC;UACRL,KAAK,EAAEA,KAAK;UACZM,IAAI,EAAEnB,IAAI;UACVoB,iBAAiB,EAAE;SACpB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;YACtBP,cAAc,CAACQ,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;YAC9CnJ,IAAI,CAACoJ,KAAK,EAAE;;QAEhB,CAAC,CAAC;MACJ,CAAC,CAAC;KACH,MAAM;MACLlD,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;;EAEzB;EAQM+B,eAAeA,CAAA;IAAA,IAAAmB,KAAA;IAAA,OAAAnF,iBAAA;MACnBgC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9BkD,KAAI,CAAC1F,gBAAgB,CAAC2F,gBAAgB,EAAE,CAAC/E,SAAS,CAAEgF,GAAG,IAAI;QACzD,IAAIC,OAAO,GAAGD,GAAG,CAACE,IAAI,CACnBC,OAAO,IAAKA,OAAO,CAACC,GAAG,KAAKtJ,SAAS,CAACuJ,aAAa,CAACC,OAAO,CAC7D;QACD,MAAMC,aAAa,GAAGP,GAAG,CAACE,IAAI,CAC3BC,OAAO,IAAKA,OAAO,CAACC,GAAG,KAAKtJ,SAAS,CAACuJ,aAAa,CAACG,aAAa,CACnE;QACD,IAAID,aAAa,EAAE;UACjBE,YAAY,CAACb,OAAO,CAAC,eAAe,EAAEjE,IAAI,CAACE,SAAS,CAAC0E,aAAa,CAACG,KAAK,CAAC,CAAC;;QAG5E,IAAIT,OAAO,EAAE;UACXQ,YAAY,CAACb,OAAO,CAAC,SAAS,EAAEjE,IAAI,CAACE,SAAS,CAACoE,OAAO,CAACS,KAAK,CAAC,CAAC;;MAElE,CAAC,CAAC;IAAA;EACJ;EAEM5B,6BAA6BA,CAAA;IAAA,IAAA6B,MAAA;IAAA,OAAAhG,iBAAA;MAChC;MACAgG,MAAI,CAACzG,WAAW,CAAC0G,QAAQ,EAAE;MAE3B;MACAD,MAAI,CAACzG,WAAW,CAAC2G,gBAAgB,EAAE;MAEnC;MACA,MAAMpF,KAAK,GAAGkF,MAAI,CAACzG,WAAW,CAAC4G,QAAQ,EAAE;MACzCnE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEnB,KAAK,CAAC;IAAC;EAEpC;EAEMsD,sBAAsBA,CAAA;IAAA,IAAAgC,MAAA;IAAA,OAAApG,iBAAA;MAC1B,IAAIhE,SAAS,CAACqK,gBAAgB,EAAE,EAAE;QAChCD,MAAI,CAACrG,aAAa,EAAE;QACpB,IAAIuG,MAAM,SAASrK,MAAM,CAACsK,gBAAgB,EAAE;QAC5C,QAAQD,MAAM,CAACE,MAAM;UACnB,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,uBAAuB;YAC1B,aAAavK,MAAM,CAACwK,kBAAkB,EAAE;UAC1C;YACE,OAAOH,MAAM,CAACE,MAAM;;OAEzB,MAAM;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA;IACD;EACH;EAEAE,eAAeA,CAAA;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEIC,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5G,iBAAA;MAClBgC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAAC2E,MAAI,CAAChI,QAAQ,CAACiI,GAAG,IAAI,CAACD,MAAI,CAAChI,QAAQ,CAACkI,MAAM,EAAE;QAC/C9E,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE2E,MAAI,CAACG,cAAc,CAAC;QAC3C,IAAIH,MAAI,CAACG,cAAc,EAAE;UACvB,MAAMhC,MAAM,SAAS6B,MAAI,CAACG,cAAc,CAACC,MAAM,EAAE;UACjDhF,OAAO,CAACC,GAAG,CAAC,uBAAuB8C,MAAM,CAACkC,OAAO,EAAE,CAAC;;OAEvD,MAAM;QACLL,MAAI,CAACjH,aAAa,CAACuH,UAAU,EAAE;QAC/BlF,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;QAClB,IAAIkF,QAAQ,GAAGP,MAAI,CAACjH,aAAa,CAACyH,IAAI,CAACR,MAAI,CAACS,iBAAiB,EAAE;UAC7DC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,IAAI,EAAE;SACP,CAAC;;IACH;EACH;EACA;;;EAGAC,WAAWA,CAAA;IACT;IACA,IAAI,CAAClG,eAAe,CAACmG,IAAI,EAAE;IAC3B,IAAI,CAACnG,eAAe,CAACoG,QAAQ,EAAE;EACjC;EAEA;EACA;EAEA;;;;;EAKA3K,aAAaA,CAACyI,GAAG;IACf,IAAI,CAACzG,mBAAmB,CAAC4I,kBAAkB,CAACnC,GAAG,CAAC,CAACoC,UAAU,EAAE;EAC/D;EAAC,QAAAC,CAAA;qBAzcUtJ,YAAY,EAAApC,EAAA,CAAA2L,iBAAA,CAgCbvM,QAAQ,GAAAY,EAAA,CAAA2L,iBAAA,CAAAC,EAAA,CAAAC,KAAA,GAAA7L,EAAA,CAAA2L,iBAAA,CAAAG,EAAA,CAAAC,QAAA,GAAA/L,EAAA,CAAA2L,iBAAA,CAAA3L,EAAA,CAAAgM,SAAA,GAAAhM,EAAA,CAAA2L,iBAAA,CAAA3L,EAAA,CAAAiM,UAAA,GAAAjM,EAAA,CAAA2L,iBAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAnM,EAAA,CAAA2L,iBAAA,CAAAS,EAAA,CAAAC,kBAAA,GAAArM,EAAA,CAAA2L,iBAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAvM,EAAA,CAAA2L,iBAAA,CAAAa,EAAA,CAAAC,gBAAA,GAAAzM,EAAA,CAAA2L,iBAAA,CAAAe,EAAA,CAAAC,WAAA,GAAA3M,EAAA,CAAA2L,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAA7M,EAAA,CAAA2L,iBAAA,CAAAmB,EAAA,CAAAC,wBAAA,GAAA/M,EAAA,CAAA2L,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAAjN,EAAA,CAAA2L,iBAAA,CAAAuB,GAAA,CAAAC,UAAA,GAAAnN,EAAA,CAAA2L,iBAAA,CAAAyB,GAAA,CAAAC,oBAAA,GAAArN,EAAA,CAAA2L,iBAAA,CAAA2B,GAAA,CAAAC,eAAA,GAAAvN,EAAA,CAAA2L,iBAAA,CAAA6B,GAAA,CAAAC,QAAA,GAAAzN,EAAA,CAAA2L,iBAAA,CAAA+B,GAAA,CAAAC,QAAA,GAAA3N,EAAA,CAAA2L,iBAAA,CAAAiC,GAAA,CAAAC,iBAAA,GAAA7N,EAAA,CAAA2L,iBAAA,CAAAmC,GAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAhCP5L,YAAY;IAAA6L,SAAA;IAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QClDzBpO,EAAA,CAAAiB,UAAA,IAAAqN,oCAAA,0BA6Be;QACftO,EAAA,CAAAiB,UAAA,IAAAsN,oCAAA,0BAEe;QAEfvO,EAAA,CAAAiB,UAAA,IAAAuN,mCAAA,iCAAAxO,EAAA,CAAAyO,sBAAA,CAiBc;QAIdzO,EAAA,CAAAiB,UAAA,IAAAyN,mCAAA,kCAAA1O,EAAA,CAAAyO,sBAAA,CAwBc;QAEdzO,EAAA,CAAAE,SAAA,uBAAgD;;;QAjFjCF,EAAA,CAAAc,UAAA,SAAAuN,GAAA,CAAA1L,kBAAA,CAAAgM,cAAA,CAAuC;QA8BvC3O,EAAA,CAAAe,SAAA,GAAwC;QAAxCf,EAAA,CAAAc,UAAA,UAAAuN,GAAA,CAAA1L,kBAAA,CAAAgM,cAAA,CAAwC", "names": ["DOCUMENT", "Subject", "takeUntil", "Waves", "App", "StatusBar", "<PERSON><PERSON>", "menu", "Capacitor", "Camera", "environment", "AppConfig", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "ɵɵelementStart", "ɵɵlistener", "AppComponent_ng_container_0_core_sidebar_3_Template_a_click_1_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "toggleSidebar", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵclassMap", "ɵɵtemplate", "AppComponent_ng_container_0_ng_container_1_Template", "AppComponent_ng_container_0_ng_container_2_Template", "AppComponent_ng_container_0_core_sidebar_3_Template", "ctx_r0", "coreConfig", "layout", "type", "customizer", "ɵɵtext", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "AppComponent_ng_template_4_Template_button_click_4_listener", "restoredCtx", "_r14", "modal_r12", "$implicit", "dismiss", "ɵɵtextInterpolate", "AppComponent", "constructor", "document", "_title", "platform", "_renderer", "_elementRef", "_coreConfigService", "_coreSidebarService", "_coreMenuService", "_translateService", "_authService", "_router", "_coreLoadingScreenService", "_loadingService", "_fcmService", "_notificationService", "_settingsService", "location", "_modalService", "_navigationService", "_commonsService", "showBtnInstallPwa", "hideStatusBar", "_asyncToGenerator", "setBackgroundColor", "color", "hide", "currentUser", "subscribe", "x", "currentUserValue", "simpleUploadConfig", "uploadUrl", "apiUrl", "headers", "Accept", "Authorization", "token", "PROJECT_ID", "JSON", "parse", "stringify", "register", "setCurrentMenu", "addLangs", "setDefaultLang", "_unsubscribeAll", "getInitSettings", "ngOnInit", "getProfile", "data", "unregister", "listen", "addListener", "canGoBack", "console", "log", "exitApp", "back", "init", "modalLoading", "modal_loading_screen", "config", "pipe", "appLanguage", "app", "use", "setTimeout", "nativeElement", "classList", "remove", "add", "navbar", "footer", "hidden", "setAttribute", "getElementsByClassName", "removeAttribute", "skin", "undefined", "body", "setTitle", "appTitle", "stage", "setConfig", "language", "emitEvent", "getStreamConfig", "path", "includes", "requestNotificationPermission", "requestMediaPermission", "getNotificationSettings", "title", "is_on", "notification", "sessionStorage", "getItem", "fire", "text", "confirmButtonText", "then", "result", "isConfirmed", "setItem", "close", "_this", "getSettingNoAuth", "res", "livekit", "find", "setting", "key", "SETTINGS_KEYS", "LIVEKIT", "name_settings", "NAME_SETTINGS", "localStorage", "value", "_this2", "initPush", "listenToMessages", "getToken", "_this3", "isNativePlatform", "status", "checkPermissions", "camera", "requestPermissions", "ngAfterViewInit", "onInstallCLick", "_this4", "IOS", "SAFARI", "deferred<PERSON>rompt", "prompt", "outcome", "dismissAll", "modalRef", "open", "modal_install_pwa", "centered", "backdrop", "size", "ngOnDestroy", "next", "complete", "getSidebarRegistry", "toggle<PERSON><PERSON>", "_", "ɵɵdirectiveInject", "i1", "Title", "i2", "Platform", "Renderer2", "ElementRef", "i3", "CoreConfigService", "i4", "CoreSidebarService", "i5", "CoreMenuService", "i6", "TranslateService", "i7", "AuthService", "i8", "Router", "i9", "CoreLoadingScreenService", "i10", "LoadingService", "i11", "FcmService", "i12", "NotificationsService", "i13", "SettingsService", "i14", "Location", "i15", "NgbModal", "i16", "NavigationService", "i17", "CommonsService", "_2", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "AppComponent_ng_container_0_Template", "AppComponent_ng_container_1_Template", "AppComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "AppComponent_ng_template_4_Template", "visible_layout"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\app.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\app.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  Inject,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  On<PERSON>nit,\r\n  ElementRef,\r\n  Renderer2,\r\n  ViewChild,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { DOCUMENT } from '@angular/common';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\nimport { Subject } from 'rxjs';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport * as Waves from 'node-waves';\r\nimport { App } from '@capacitor/app';\r\nimport { StatusBar, Style } from '@capacitor/status-bar';\r\nimport Swal from 'sweetalert2';\r\n\r\nimport { CoreMenuService } from '@core/components/core-menu/core-menu.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { CoreLoadingScreenService } from '@core/services/loading-screen.service';\r\nimport { CoreTranslationService } from '@core/services/translation.service';\r\nimport { Location } from '@angular/common';\r\nimport { menu } from 'app/menu/menu';\r\nimport { AuthService } from './services/auth.service';\r\n\r\nimport { Router } from '@angular/router';\r\nimport { LoadingService } from './services/loading.service';\r\nimport { FcmService } from './services/fcm.service';\r\nimport { NotificationsService } from './layout/components/navbar/navbar-notification/notifications.service';\r\nimport { SettingsService } from './services/settings.service';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { NavigationService } from './services/navigation.service';\r\nimport { Camera } from '@capacitor/camera';\r\nimport { Platform } from '@angular/cdk/platform';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { CommonsService } from './services/commons.service';\r\nimport { environment } from 'environments/environment';\r\nimport { AppConfig } from './app-config';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class AppComponent implements OnInit, OnDestroy {\r\n  coreConfig: any;\r\n  menu: any;\r\n  defaultLanguage: 'en'; // This language will be used as a fallback when a translation isn't found in the current language\r\n  appLanguage: 'zh_HK'; // Set application default language i.e fr\r\n  showBtnInstallPwa: boolean = false;\r\n  deferredPrompt: any;\r\n  // Private\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  //Get element by angular view child\r\n  @ViewChild('modal_loading_screen', { static: true })\r\n  modal_loading_screen: ElementRef;\r\n\r\n  @ViewChild('modal_install_pwa', { static: true })\r\n  modal_install_pwa: ElementRef;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {DOCUMENT} document\r\n   * @param {Title} _title\r\n   * @param {Renderer2} _renderer\r\n   * @param {ElementRef} _elementRef\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {CoreLoadingScreenService} _coreLoadingScreenService\r\n   * @param {CoreMenuService} _coreMenuService\r\n   * @param {CoreTranslationService} _coreTranslationService\r\n   * @param {TranslateService} _translateService\r\n   */\r\n  constructor(\r\n    @Inject(DOCUMENT) private document: any,\r\n    private _title: Title,\r\n    public platform: Platform,\r\n    private _renderer: Renderer2,\r\n    private _elementRef: ElementRef,\r\n    public _coreConfigService: CoreConfigService,\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _coreMenuService: CoreMenuService,\r\n    private _translateService: TranslateService,\r\n    private _authService: AuthService,\r\n    private _router: Router,\r\n    private _coreLoadingScreenService: CoreLoadingScreenService,\r\n    public _loadingService: LoadingService,\r\n    public _fcmService: FcmService,\r\n    public _notificationService: NotificationsService,\r\n    public _settingsService: SettingsService,\r\n    private location: Location,\r\n    public _modalService: NgbModal,\r\n    public _navigationService: NavigationService,\r\n    public _commonsService: CommonsService\r\n  ) {\r\n    _authService.currentUser.subscribe((x) => {\r\n      if (x) {\r\n        _notificationService.currentUserValue = x;\r\n        _commonsService.simpleUploadConfig = {\r\n          // The URL that the images are uploaded to.\r\n          uploadUrl: `${environment.apiUrl}/send-messages/upload`,\r\n          headers: {\r\n            Accept: 'application/json, text/plain, */*',\r\n            'X-CSRF-TOKEN': 'CSRF-Token',\r\n            Authorization: `Bearer ${x.token}`,\r\n            'X-project-id': AppConfig.PROJECT_ID,\r\n          },\r\n        };\r\n      }\r\n    });\r\n    // Get the application main menu\r\n    this.menu = JSON.parse(JSON.stringify(menu));\r\n\r\n    // Register the menu to the menu service\r\n    this._coreMenuService.register('main', this.menu);\r\n\r\n    // Set the main menu as our current menu\r\n    this._coreMenuService.setCurrentMenu('main');\r\n\r\n    // Add languages to the translation service\r\n    this._translateService.addLangs(['en', 'zh_HK']);\r\n\r\n    // This language will be used as a fallback when a translation isn't found in the current language\r\n    this._translateService.setDefaultLang('en');\r\n\r\n    // Set the private defaults\r\n    this._unsubscribeAll = new Subject();\r\n    if (_authService.currentUserValue) {\r\n      _settingsService.getInitSettings(false);\r\n    }\r\n  }\r\n\r\n  // Lifecycle hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit(): void {\r\n    this._authService.getProfile().subscribe((data) => {\r\n      // console.log('data', data);\r\n      this._coreMenuService.unregister('main');\r\n      this._coreMenuService.register('main', menu);\r\n      this._coreMenuService.setCurrentMenu('main');\r\n    });\r\n    // this._fcmService.listen();\r\n    this._notificationService.listen();\r\n    // This code handles the functionality of the back button\r\n    App.addListener('backButton', ({ canGoBack }) => {\r\n      console.log('canGoBack', canGoBack);\r\n\r\n      // Check if there is anything in the browsing history\r\n      if (!canGoBack) {\r\n        // Exit the app if not\r\n        App.exitApp();\r\n      } else {\r\n        this._navigationService.back();\r\n      }\r\n    });\r\n\r\n    // Init wave effect (Ripple effect)\r\n    Waves.init();\r\n    this._loadingService.modalLoading = this.modal_loading_screen;\r\n    // Subscribe to config changes\r\n    this._coreConfigService.config\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe((config) => {\r\n        this.coreConfig = config;\r\n        const appLanguage = this.coreConfig.app.appLanguage || 'en';\r\n        this._translateService.use(appLanguage);\r\n\r\n        setTimeout(() => {\r\n          this._translateService.setDefaultLang('en');\r\n          this._translateService.setDefaultLang(appLanguage);\r\n        });\r\n\r\n        // Remove default classes first\r\n        this._elementRef.nativeElement.classList.remove(\r\n          'vertical-layout',\r\n          'vertical-menu-modern',\r\n          'horizontal-layout',\r\n          'horizontal-menu'\r\n        );\r\n        // Add class based on config options\r\n        if (this.coreConfig.layout.type === 'vertical') {\r\n          this._elementRef.nativeElement.classList.add(\r\n            'vertical-layout',\r\n            'vertical-menu-modern'\r\n          );\r\n        } else if (this.coreConfig.layout.type === 'horizontal') {\r\n          this._elementRef.nativeElement.classList.add(\r\n            'horizontal-layout',\r\n            'horizontal-menu'\r\n          );\r\n        }\r\n\r\n        // Navbar\r\n        //--------\r\n\r\n        // Remove default classes first\r\n        this._elementRef.nativeElement.classList.remove(\r\n          'navbar-floating',\r\n          'navbar-static',\r\n          'navbar-sticky',\r\n          'navbar-hidden'\r\n        );\r\n\r\n        // Add class based on config options\r\n        if (this.coreConfig.layout.navbar.type === 'navbar-static-top') {\r\n          this._elementRef.nativeElement.classList.add('navbar-static');\r\n        } else if (this.coreConfig.layout.navbar.type === 'fixed-top') {\r\n          this._elementRef.nativeElement.classList.add('navbar-sticky');\r\n        } else if (this.coreConfig.layout.navbar.type === 'floating-nav') {\r\n          this._elementRef.nativeElement.classList.add('navbar-floating');\r\n        } else {\r\n          this._elementRef.nativeElement.classList.add('navbar-hidden');\r\n        }\r\n\r\n        // Footer\r\n        //--------\r\n\r\n        // Remove default classes first\r\n        this._elementRef.nativeElement.classList.remove(\r\n          'footer-fixed',\r\n          'footer-static',\r\n          'footer-hidden'\r\n        );\r\n\r\n        // Add class based on config options\r\n        if (this.coreConfig.layout.footer.type === 'footer-sticky') {\r\n          this._elementRef.nativeElement.classList.add('footer-fixed');\r\n        } else if (this.coreConfig.layout.footer.type === 'footer-static') {\r\n          this._elementRef.nativeElement.classList.add('footer-static');\r\n        } else {\r\n          this._elementRef.nativeElement.classList.add('footer-hidden');\r\n        }\r\n\r\n        // Blank layout\r\n        if (\r\n          this.coreConfig.layout.menu.hidden &&\r\n          this.coreConfig.layout.navbar.hidden &&\r\n          this.coreConfig.layout.footer.hidden\r\n        ) {\r\n          this._elementRef.nativeElement.classList.add('blank-page');\r\n          // ! Fix: Transition issue while coming from blank page\r\n          this._renderer.setAttribute(\r\n            this._elementRef.nativeElement.getElementsByClassName(\r\n              'app-content'\r\n            )[0],\r\n            'style',\r\n            'transition:none'\r\n          );\r\n        } else {\r\n          this._elementRef.nativeElement.classList.remove('blank-page');\r\n          // ! Fix: Transition issue while coming from blank page\r\n          setTimeout(() => {\r\n            if (\r\n              this._elementRef.nativeElement.getElementsByClassName(\r\n                'app-content'\r\n              )[0]\r\n            ) {\r\n              this._renderer.setAttribute(\r\n                this._elementRef.nativeElement.getElementsByClassName(\r\n                  'app-content'\r\n                )[0],\r\n                'style',\r\n                'transition:300ms ease all'\r\n              );\r\n            }\r\n          }, 100);\r\n          // If navbar hidden\r\n          if (this.coreConfig.layout.navbar.hidden) {\r\n            this._elementRef.nativeElement.classList.add('navbar-hidden');\r\n          }\r\n          // Menu (Vertical menu hidden)\r\n          if (this.coreConfig.layout.menu.hidden) {\r\n            this._renderer.setAttribute(\r\n              this._elementRef.nativeElement,\r\n              'data-col',\r\n              '1-column'\r\n            );\r\n          } else {\r\n            this._renderer.removeAttribute(\r\n              this._elementRef.nativeElement,\r\n              'data-col'\r\n            );\r\n          }\r\n          // Footer\r\n          if (this.coreConfig.layout.footer.hidden) {\r\n            this._elementRef.nativeElement.classList.add('footer-hidden');\r\n          }\r\n        }\r\n\r\n        // Skin Class (Adding to body as it requires highest priority)\r\n        if (\r\n          this.coreConfig.layout.skin !== '' &&\r\n          this.coreConfig.layout.skin !== undefined\r\n        ) {\r\n          this.document.body.classList.remove(\r\n            'default-layout',\r\n            'bordered-layout',\r\n            'dark-layout',\r\n            'semi-dark-layout'\r\n          );\r\n          this.document.body.classList.add(\r\n            this.coreConfig.layout.skin + '-layout'\r\n          );\r\n        }\r\n      });\r\n\r\n    // Set the application page title\r\n    this._title.setTitle(this.coreConfig.app.appTitle);\r\n\r\n    // create trigger when updating user\r\n    this._authService.currentUser.subscribe((stage: any) => {\r\n      if (stage != null) {\r\n        this._coreConfigService.setConfig(\r\n          { app: { appLanguage: stage.language } },\r\n          { emitEvent: true }\r\n        );\r\n        setTimeout(() => {\r\n          this._translateService.use(stage.language);\r\n\r\n        }, 200);\r\n        // this._router.navigate([\"/home\"]);\r\n      }\r\n    });\r\n\r\n    this._coreConfigService.setConfig({\r\n      layout: {\r\n        type: 'vertical',\r\n      },\r\n    });\r\n\r\n    this.getStreamConfig();\r\n\r\n    // check url is egress\r\n    // egress is streaming token\r\n    if (!this.location.path().includes('egress')) {\r\n      console.log('not egress');\r\n      this.requestNotificationPermission();\r\n      this.requestMediaPermission();\r\n      this._settingsService.getNotificationSettings().subscribe((data) => {\r\n        console.log(data);\r\n        const title = data.title;\r\n        const body = data.body;\r\n        const is_on = data.is_on;\r\n        const notification = sessionStorage.getItem('notification');\r\n\r\n        if (!is_on) return;\r\n        if (notification == 'true') return;\r\n\r\n        Swal.fire({\r\n          title: title,\r\n          text: body,\r\n          confirmButtonText: 'OK',\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            sessionStorage.setItem('notification', 'true');\r\n            Swal.close();\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      console.log('egress');\r\n    }\r\n  }\r\n\r\n  hideStatusBar = async () => {\r\n    await StatusBar.setBackgroundColor({ color: '#000' });\r\n    await StatusBar.hide();\r\n  };\r\n\r\n\r\n  async getStreamConfig() {\r\n    console.log('getStreamConfig');\r\n    this._settingsService.getSettingNoAuth().subscribe((res) => {\r\n      let livekit = res.find(\r\n        (setting) => setting.key === AppConfig.SETTINGS_KEYS.LIVEKIT\r\n      )\r\n      const name_settings = res.find(\r\n        (setting) => setting.key === AppConfig.SETTINGS_KEYS.NAME_SETTINGS\r\n      );\r\n      if (name_settings) {\r\n        localStorage.setItem('name_settings', JSON.stringify(name_settings.value));\r\n      }\r\n\r\n      if (livekit) {\r\n        localStorage.setItem('livekit', JSON.stringify(livekit.value));\r\n      }\r\n    })\r\n  }\r\n\r\n  async requestNotificationPermission() {\r\n     // Initialize push notifications\r\n     this._fcmService.initPush();\r\n\r\n     // Listen for incoming messages\r\n     this._fcmService.listenToMessages();\r\n \r\n     // Optionally log the current FCM token\r\n     const token = this._fcmService.getToken();\r\n     console.log('FCM Token:', token);\r\n   \r\n  }\r\n\r\n  async requestMediaPermission() {\r\n    if (Capacitor.isNativePlatform()) {\r\n      this.hideStatusBar();\r\n      let status = await Camera.checkPermissions();\r\n      switch (status.camera) {\r\n        case 'denied':\r\n        case 'prompt':\r\n        case 'prompt-with-rationale':\r\n          return await Camera.requestPermissions();\r\n        default:\r\n          return status.camera;\r\n      }\r\n    } else {\r\n      // let res = await navigator.mediaDevices.getUserMedia({\r\n      //   video: true,\r\n      //   audio: true,\r\n      // });\r\n      // res.getTracks().forEach((track) => {\r\n      //   track.stop();\r\n      // });\r\n      // return res;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    // setTimeout(() => {\r\n    //   if (Capacitor.isNativePlatform()) return;\r\n    //   if ('serviceWorker' in navigator && 'PushManager' in window) {\r\n    //     window.addEventListener('beforeinstallprompt', (event) => {\r\n    //       event.preventDefault();\r\n    //       this.deferredPrompt = event;\r\n    //       this.showBtnInstallPwa = true;\r\n    //     });\r\n    //     if (\r\n    //       this.platform.IOS ||\r\n    //       (this.platform.SAFARI && (window as any).navigator.standalone)\r\n    //     ) {\r\n    //       this.showBtnInstallPwa = false;\r\n    //     }\r\n    //     console.log('supported', this.platform);\r\n    //   } else {\r\n    //     console.log(\r\n    //       'not supported',\r\n    //       (window as any).navigator.standalone,\r\n    //       this.platform\r\n    //     );\r\n    //     this.showBtnInstallPwa = true;\r\n    //     if (\r\n    //       this.platform.IOS ||\r\n    //       (this.platform.SAFARI && (window as any).navigator.standalone)\r\n    //     ) {\r\n    //       this.showBtnInstallPwa = false;\r\n    //     }\r\n    //   }\r\n    // }, 1000);\r\n  }\r\n  async onInstallCLick() {\r\n    console.log('install button clicked');\r\n    if (!this.platform.IOS && !this.platform.SAFARI) {\r\n      console.log('android', this.deferredPrompt);\r\n      if (this.deferredPrompt) {\r\n        const result = await this.deferredPrompt.prompt();\r\n        console.log(`Install prompt was: ${result.outcome}`);\r\n      }\r\n    } else {\r\n      this._modalService.dismissAll();\r\n      console.log('ios');\r\n      let modalRef = this._modalService.open(this.modal_install_pwa, {\r\n        centered: true,\r\n        backdrop: 'static',\r\n        size: 'md',\r\n      });\r\n    }\r\n  }\r\n  /**\r\n   * On destroy\r\n   */\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n\r\n  // Public methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Toggle sidebar open\r\n   *\r\n   * @param key\r\n   */\r\n  toggleSidebar(key): void {\r\n    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();\r\n  }\r\n}\r\n", "<ng-container *ngIf=\"_coreConfigService.visible_layout\">\r\n  <!-- vertical-layout -->\r\n  <ng-container *ngIf=\"coreConfig.layout.type === 'vertical'\">\r\n    <vertical-layout></vertical-layout>\r\n  </ng-container>\r\n  <!-- / vertical-layout -->\r\n\r\n  <!-- horizontal-layout -->\r\n  <ng-container *ngIf=\"coreConfig.layout.type === 'horizontal'\">\r\n    <horizontal-layout></horizontal-layout>\r\n  </ng-container>\r\n  <!-- / horizontal-layout -->\r\n\r\n  <!-- button install PWA -->\r\n  <!-- <button class=\"btn btn-primary\" id=\"btn-install-pwa\" *ngIf=\"showBtnInstallPwa\" (click)=\"onInstallCLick()\">\r\n  <i class=\"fa-xl fa-solid fa-arrow-down-to-line\" aria-hidden=\"true\"></i>\r\n</button> -->\r\n  <!-- / button install PWA -->\r\n\r\n  <!-- theme customizer -->\r\n  <core-sidebar name=\"themeCustomizer\" class=\"customizer d-none d-md-block\" [invisibleOverlay]=\"true\"\r\n    *ngIf=\"coreConfig.layout.customizer\">\r\n    <a class=\"customizer-toggle d-flex align-items-center justify-content-center\"\r\n      (click)=\"toggleSidebar('themeCustomizer')\">\r\n      <span [data-feather]=\"'settings'\" [class]=\"'spinner'\"></span>\r\n    </a>\r\n    <core-theme-customizer></core-theme-customizer>\r\n  </core-sidebar>\r\n  <!-- / theme customizer -->\r\n</ng-container>\r\n<ng-container *ngIf=\"!_coreConfigService.visible_layout\">\r\n  <router-outlet></router-outlet>\r\n</ng-container>\r\n<!-- modal loading screen -->\r\n<ng-template #modal_loading_screen let-modal>\r\n  <div class=\"modal-body text-center\" tabindex=\"0\" ngbAutofocus>\r\n    <div class=\"spinner-border spinner-border text-primary\" style=\"width: 4em;height: 4em;\" aria-hidden=\"true\"></div>\r\n    <div class=\"row mt-2\">\r\n      <div class=\"col text-center\">\r\n        <h4>\r\n          {{'Loading' | translate}}\r\n          <div class=\"spinner-grow spinner-grow-xs text-dark\">\r\n          </div>\r\n          <div class=\"spinner-grow spinner-grow-xs text-dark\">\r\n          </div>\r\n          <div class=\"spinner-grow spinner-grow-xs text-dark\">\r\n          </div>\r\n        </h4>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n<!-- / modal loading screen -->\r\n\r\n<!-- modal Install Pwa -->\r\n<ng-template #modal_install_pwa let-modal>\r\n  <!-- header -->\r\n  <div class=\"modal-header\">\r\n    <h4 class=\"modal-title\" id=\"modal-basic-title\">{{'Install App' | translate}}</h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"modal.dismiss('Cross click')\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <!-- body -->\r\n  <div class=\"modal-body\">\r\n    <p>{{'Install the app on your device to easily access it anytime. No app store. No download. No hasstle' |\r\n      translate}}</p>\r\n    <p>\r\n      1. {{'Open this page in Safari' | translate}} <i class=\"fa-lg fa-brands fa-safari\"></i>\r\n    </p>\r\n    <p>\r\n      2. {{'Tap on share icon' | translate}}\r\n      <i class=\"fa-lg fa-solid fa-arrow-up-from-square\"></i>\r\n      {{'at the toolbar of the browser' | translate}}\r\n    </p>\r\n    <p>\r\n      3. {{'Find and Select' | translate}} \"<b>{{'Add to Home Screen' | translate}} <i\r\n          class=\"fa-regular fa-square-plus fa-lg\"></i></b>\"\r\n  </div>\r\n</ng-template>\r\n\r\n<router-outlet name=\"no_layout\"></router-outlet>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
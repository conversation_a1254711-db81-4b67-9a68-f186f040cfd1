{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function timeoutWith(due, withObservable, scheduler = async) {\n  return source => {\n    let absoluteTimeout = isDate(due);\n    let waitFor = absoluteTimeout ? +due - scheduler.now() : Math.abs(due);\n    return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));\n  };\n}\nclass TimeoutWithOperator {\n  constructor(waitFor, absoluteTimeout, withObservable, scheduler) {\n    this.waitFor = waitFor;\n    this.absoluteTimeout = absoluteTimeout;\n    this.withObservable = withObservable;\n    this.scheduler = scheduler;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new TimeoutWithSubscriber(subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler));\n  }\n}\nclass TimeoutWithSubscriber extends OuterSubscriber {\n  constructor(destination, absoluteTimeout, waitFor, withObservable, scheduler) {\n    super(destination);\n    this.absoluteTimeout = absoluteTimeout;\n    this.waitFor = waitFor;\n    this.withObservable = withObservable;\n    this.scheduler = scheduler;\n    this.action = null;\n    this.scheduleTimeout();\n  }\n  static dispatchTimeout(subscriber) {\n    const {\n      withObservable\n    } = subscriber;\n    subscriber._unsubscribeAndRecycle();\n    subscriber.add(subscribeToResult(subscriber, withObservable));\n  }\n  scheduleTimeout() {\n    const {\n      action\n    } = this;\n    if (action) {\n      this.action = action.schedule(this, this.waitFor);\n    } else {\n      this.add(this.action = this.scheduler.schedule(TimeoutWithSubscriber.dispatchTimeout, this.waitFor, this));\n    }\n  }\n  _next(value) {\n    if (!this.absoluteTimeout) {\n      this.scheduleTimeout();\n    }\n    super._next(value);\n  }\n  _unsubscribe() {\n    this.action = null;\n    this.scheduler = null;\n    this.withObservable = null;\n  }\n}", "map": {"version": 3, "names": ["async", "isDate", "OuterSubscriber", "subscribeToResult", "timeoutWith", "due", "withObservable", "scheduler", "source", "absoluteTimeout", "waitFor", "now", "Math", "abs", "lift", "TimeoutWithOperator", "constructor", "call", "subscriber", "subscribe", "TimeoutWithSubscriber", "destination", "action", "scheduleTimeout", "dispatchTimeout", "_unsubscribeAndRecycle", "add", "schedule", "_next", "value", "_unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/timeoutWith.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function timeoutWith(due, withObservable, scheduler = async) {\n    return (source) => {\n        let absoluteTimeout = isDate(due);\n        let waitFor = absoluteTimeout ? (+due - scheduler.now()) : Math.abs(due);\n        return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));\n    };\n}\nclass TimeoutWithOperator {\n    constructor(waitFor, absoluteTimeout, withObservable, scheduler) {\n        this.waitFor = waitFor;\n        this.absoluteTimeout = absoluteTimeout;\n        this.withObservable = withObservable;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new TimeoutWithSubscriber(subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler));\n    }\n}\nclass TimeoutWithSubscriber extends OuterSubscriber {\n    constructor(destination, absoluteTimeout, waitFor, withObservable, scheduler) {\n        super(destination);\n        this.absoluteTimeout = absoluteTimeout;\n        this.waitFor = waitFor;\n        this.withObservable = withObservable;\n        this.scheduler = scheduler;\n        this.action = null;\n        this.scheduleTimeout();\n    }\n    static dispatchTimeout(subscriber) {\n        const { withObservable } = subscriber;\n        subscriber._unsubscribeAndRecycle();\n        subscriber.add(subscribeToResult(subscriber, withObservable));\n    }\n    scheduleTimeout() {\n        const { action } = this;\n        if (action) {\n            this.action = action.schedule(this, this.waitFor);\n        }\n        else {\n            this.add(this.action = this.scheduler.schedule(TimeoutWithSubscriber.dispatchTimeout, this.waitFor, this));\n        }\n    }\n    _next(value) {\n        if (!this.absoluteTimeout) {\n            this.scheduleTimeout();\n        }\n        super._next(value);\n    }\n    _unsubscribe() {\n        this.action = null;\n        this.scheduler = null;\n        this.withObservable = null;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,cAAc,EAAEC,SAAS,GAAGP,KAAK,EAAE;EAChE,OAAQQ,MAAM,IAAK;IACf,IAAIC,eAAe,GAAGR,MAAM,CAACI,GAAG,CAAC;IACjC,IAAIK,OAAO,GAAGD,eAAe,GAAI,CAACJ,GAAG,GAAGE,SAAS,CAACI,GAAG,CAAC,CAAC,GAAIC,IAAI,CAACC,GAAG,CAACR,GAAG,CAAC;IACxE,OAAOG,MAAM,CAACM,IAAI,CAAC,IAAIC,mBAAmB,CAACL,OAAO,EAAED,eAAe,EAAEH,cAAc,EAAEC,SAAS,CAAC,CAAC;EACpG,CAAC;AACL;AACA,MAAMQ,mBAAmB,CAAC;EACtBC,WAAWA,CAACN,OAAO,EAAED,eAAe,EAAEH,cAAc,EAAEC,SAAS,EAAE;IAC7D,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAU,IAAIA,CAACC,UAAU,EAAEV,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACW,SAAS,CAAC,IAAIC,qBAAqB,CAACF,UAAU,EAAE,IAAI,CAACT,eAAe,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACJ,cAAc,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;EAC3I;AACJ;AACA,MAAMa,qBAAqB,SAASlB,eAAe,CAAC;EAChDc,WAAWA,CAACK,WAAW,EAAEZ,eAAe,EAAEC,OAAO,EAAEJ,cAAc,EAAEC,SAAS,EAAE;IAC1E,KAAK,CAACc,WAAW,CAAC;IAClB,IAAI,CAACZ,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACJ,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACe,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACA,OAAOC,eAAeA,CAACN,UAAU,EAAE;IAC/B,MAAM;MAAEZ;IAAe,CAAC,GAAGY,UAAU;IACrCA,UAAU,CAACO,sBAAsB,CAAC,CAAC;IACnCP,UAAU,CAACQ,GAAG,CAACvB,iBAAiB,CAACe,UAAU,EAAEZ,cAAc,CAAC,CAAC;EACjE;EACAiB,eAAeA,CAAA,EAAG;IACd,MAAM;MAAED;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAGA,MAAM,CAACK,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACjB,OAAO,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACgB,GAAG,CAAC,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACf,SAAS,CAACoB,QAAQ,CAACP,qBAAqB,CAACI,eAAe,EAAE,IAAI,CAACd,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9G;EACJ;EACAkB,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAAC,IAAI,CAACpB,eAAe,EAAE;MACvB,IAAI,CAACc,eAAe,CAAC,CAAC;IAC1B;IACA,KAAK,CAACK,KAAK,CAACC,KAAK,CAAC;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACR,MAAM,GAAG,IAAI;IAClB,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAACD,cAAc,GAAG,IAAI;EAC9B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
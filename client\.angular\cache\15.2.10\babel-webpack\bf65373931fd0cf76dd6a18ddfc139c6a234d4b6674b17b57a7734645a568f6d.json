{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Directive, Host, Input, HostBinding, TemplateRef, Component, ViewEncapsulation, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport * as i3 from 'ngx-ui-tour-core';\nimport { TourService, ScrollingUtil, TourState, TourHotkeyListenerComponent, TourBackdropService, TourModule } from 'ngx-ui-tour-core';\nimport * as i3$1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NgbPopover, NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';\nconst _c0 = [\"tourStep\"];\nfunction TourStepTemplateComponent_ng_template_0_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function TourStepTemplateComponent_ng_template_0_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.tourService.prev());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().step;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\xAB \", step_r2 == null ? null : step_r2.prevBtnTitle, \" \");\n  }\n}\nfunction TourStepTemplateComponent_ng_template_0_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function TourStepTemplateComponent_ng_template_0_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.tourService.next());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().step;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", step_r2 == null ? null : step_r2.nextBtnTitle, \" \\xBB \");\n  }\n}\nfunction TourStepTemplateComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelement(0, \"p\", 1);\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵtemplate(2, TourStepTemplateComponent_ng_template_0_button_2_Template, 2, 1, \"button\", 3);\n    i0.ɵɵtemplate(3, TourStepTemplateComponent_ng_template_0_button_3_Template, 2, 1, \"button\", 3);\n    i0.ɵɵelementStart(4, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function TourStepTemplateComponent_ng_template_0_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.tourService.end());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r2 = ctx.step;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", step_r2 == null ? null : step_r2.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tourService.hasPrev(step_r2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tourService.hasNext(step_r2) && !step_r2.goToNextOnAnchorClick);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", step_r2 == null ? null : step_r2.endBtnTitle, \" \");\n  }\n}\nclass NgbTourService extends TourService {}\nNgbTourService.ɵfac = /* @__PURE__ */function () {\n  let ɵNgbTourService_BaseFactory;\n  return function NgbTourService_Factory(t) {\n    return (ɵNgbTourService_BaseFactory || (ɵNgbTourService_BaseFactory = i0.ɵɵgetInheritedFactory(NgbTourService)))(t || NgbTourService);\n  };\n}();\nNgbTourService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgbTourService,\n  factory: NgbTourService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgbTourService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass TourStepTemplateService {}\nTourStepTemplateService.ɵfac = function TourStepTemplateService_Factory(t) {\n  return new (t || TourStepTemplateService)();\n};\nTourStepTemplateService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TourStepTemplateService,\n  factory: TourStepTemplateService.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourStepTemplateService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TourAnchorNgBootstrapPopoverDirective extends NgbPopover {}\nTourAnchorNgBootstrapPopoverDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵTourAnchorNgBootstrapPopoverDirective_BaseFactory;\n  return function TourAnchorNgBootstrapPopoverDirective_Factory(t) {\n    return (ɵTourAnchorNgBootstrapPopoverDirective_BaseFactory || (ɵTourAnchorNgBootstrapPopoverDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TourAnchorNgBootstrapPopoverDirective)))(t || TourAnchorNgBootstrapPopoverDirective);\n  };\n}();\nTourAnchorNgBootstrapPopoverDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TourAnchorNgBootstrapPopoverDirective,\n  selectors: [[\"\", \"tourAnchor\", \"\"]],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourAnchorNgBootstrapPopoverDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tourAnchor]'\n    }]\n  }], null, null);\n})();\nclass TourAnchorNgBootstrapDirective {\n  constructor(tourService, tourStepTemplate, element, popoverDirective, tourBackdrop) {\n    this.tourService = tourService;\n    this.tourStepTemplate = tourStepTemplate;\n    this.element = element;\n    this.popoverDirective = popoverDirective;\n    this.tourBackdrop = tourBackdrop;\n    this.popoverDirective.autoClose = false;\n    this.popoverDirective.triggers = '';\n    this.popoverDirective.toggle = () => {};\n  }\n  ngOnInit() {\n    this.tourService.register(this.tourAnchor, this);\n  }\n  ngOnDestroy() {\n    this.tourService.unregister(this.tourAnchor);\n  }\n  // noinspection JSUnusedGlobalSymbols\n  showTourStep(step) {\n    const htmlElement = this.element.nativeElement;\n    this.isActive = true;\n    this.popoverDirective.ngbPopover = this.tourStepTemplate.template;\n    this.popoverDirective.popoverTitle = step.title;\n    this.popoverDirective.container = 'body';\n    this.popoverDirective.placement = (step.placement || 'top').replace('before', 'left').replace('after', 'right').replace('below', 'bottom').replace('above', 'top');\n    step.prevBtnTitle = step.prevBtnTitle || 'Prev';\n    step.nextBtnTitle = step.nextBtnTitle || 'Next';\n    step.endBtnTitle = step.endBtnTitle || 'End';\n    this.popoverDirective.open({\n      step\n    });\n    if (!step.disableScrollToAnchor) {\n      ScrollingUtil.ensureVisible(htmlElement);\n    }\n    if (step.enableBackdrop) {\n      this.tourBackdrop.show(this.element);\n    } else {\n      this.tourBackdrop.close();\n    }\n  }\n  hideTourStep() {\n    this.isActive = false;\n    if (this.tourService.getStatus() === TourState.OFF) {\n      this.tourBackdrop.close();\n    }\n    this.popoverDirective.close();\n  }\n}\nTourAnchorNgBootstrapDirective.ɵfac = function TourAnchorNgBootstrapDirective_Factory(t) {\n  return new (t || TourAnchorNgBootstrapDirective)(i0.ɵɵdirectiveInject(NgbTourService), i0.ɵɵdirectiveInject(TourStepTemplateService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(TourAnchorNgBootstrapPopoverDirective, 1), i0.ɵɵdirectiveInject(i3.TourBackdropService));\n};\nTourAnchorNgBootstrapDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TourAnchorNgBootstrapDirective,\n  selectors: [[\"\", \"tourAnchor\", \"\"]],\n  hostVars: 2,\n  hostBindings: function TourAnchorNgBootstrapDirective_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"touranchor--is-active\", ctx.isActive);\n    }\n  },\n  inputs: {\n    tourAnchor: \"tourAnchor\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourAnchorNgBootstrapDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[tourAnchor]'\n    }]\n  }], function () {\n    return [{\n      type: NgbTourService\n    }, {\n      type: TourStepTemplateService\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: TourAnchorNgBootstrapPopoverDirective,\n      decorators: [{\n        type: Host\n      }]\n    }, {\n      type: i3.TourBackdropService\n    }];\n  }, {\n    tourAnchor: [{\n      type: Input\n    }],\n    isActive: [{\n      type: HostBinding,\n      args: ['class.touranchor--is-active']\n    }]\n  });\n})();\nclass TourStepTemplateComponent extends TourHotkeyListenerComponent {\n  constructor(tourStepTemplateService, tourService) {\n    super(tourService);\n    this.tourStepTemplateService = tourStepTemplateService;\n    this.tourService = tourService;\n  }\n  ngAfterContentInit() {\n    this.tourStepTemplateService.template = this.stepTemplate || this.stepTemplateContent || this.defaultTourStepTemplate;\n  }\n}\nTourStepTemplateComponent.ɵfac = function TourStepTemplateComponent_Factory(t) {\n  return new (t || TourStepTemplateComponent)(i0.ɵɵdirectiveInject(TourStepTemplateService), i0.ɵɵdirectiveInject(NgbTourService));\n};\nTourStepTemplateComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TourStepTemplateComponent,\n  selectors: [[\"tour-step-template\"]],\n  contentQueries: function TourStepTemplateComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepTemplateContent = _t.first);\n    }\n  },\n  viewQuery: function TourStepTemplateComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7, TemplateRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultTourStepTemplate = _t.first);\n    }\n  },\n  inputs: {\n    stepTemplate: \"stepTemplate\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 2,\n  vars: 0,\n  consts: [[\"tourStep\", \"\"], [1, \"tour-step-content\", 3, \"innerHTML\"], [1, \"tour-step-navigation\"], [\"class\", \"btn btn-sm btn-default\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-default\", 3, \"click\"]],\n  template: function TourStepTemplateComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, TourStepTemplateComponent_ng_template_0_Template, 6, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    }\n  },\n  dependencies: [i3$1.NgIf],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourStepTemplateComponent, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      selector: 'tour-step-template',\n      template: `\n    <ng-template #tourStep let-step=\"step\">\n      <p class=\"tour-step-content\" [innerHTML]=\"step?.content\"></p>\n      <div class=\"tour-step-navigation\">\n        <button\n          *ngIf=\"tourService.hasPrev(step)\"\n          class=\"btn btn-sm btn-default\"\n          (click)=\"tourService.prev()\"\n        >\n          « {{ step?.prevBtnTitle }}\n        </button>\n        <button\n          *ngIf=\"tourService.hasNext(step) && !step.goToNextOnAnchorClick\"\n          class=\"btn btn-sm btn-default\"\n          (click)=\"tourService.next()\"\n        >\n          {{ step?.nextBtnTitle }} »\n        </button>\n        <button class=\"btn btn-sm btn-default\" (click)=\"tourService.end()\">\n          {{ step?.endBtnTitle }}\n        </button>\n      </div>\n    </ng-template>\n  `\n    }]\n  }], function () {\n    return [{\n      type: TourStepTemplateService\n    }, {\n      type: NgbTourService\n    }];\n  }, {\n    defaultTourStepTemplate: [{\n      type: ViewChild,\n      args: ['tourStep', {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    stepTemplate: [{\n      type: Input\n    }],\n    stepTemplateContent: [{\n      type: ContentChild,\n      args: [TemplateRef]\n    }]\n  });\n})();\nclass TourNgBootstrapModule {\n  static forRoot() {\n    return {\n      ngModule: TourNgBootstrapModule,\n      providers: [TourStepTemplateService, TourService, NgbTourService, TourBackdropService]\n    };\n  }\n}\nTourNgBootstrapModule.ɵfac = function TourNgBootstrapModule_Factory(t) {\n  return new (t || TourNgBootstrapModule)();\n};\nTourNgBootstrapModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TourNgBootstrapModule\n});\nTourNgBootstrapModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule, NgbPopoverModule, TourModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TourNgBootstrapModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourStepTemplateComponent],\n      exports: [TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourStepTemplateComponent],\n      imports: [CommonModule, NgbPopoverModule, TourModule]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-ui-tour-ng-bootstrap\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgbTourService, TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourNgBootstrapModule, NgbTourService as TourService, TourStepTemplateComponent };", "map": {"version": 3, "names": ["i0", "Injectable", "Directive", "Host", "Input", "HostBinding", "TemplateRef", "Component", "ViewEncapsulation", "ViewChild", "ContentChild", "NgModule", "i3", "TourService", "ScrollingUtil", "TourState", "TourHotkeyListenerComponent", "TourBackdropService", "TourModule", "i3$1", "CommonModule", "NgbPopover", "NgbPopoverModule", "_c0", "TourStepTemplateComponent_ng_template_0_button_2_Template", "rf", "ctx", "_r6", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "TourStepTemplateComponent_ng_template_0_button_2_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "tourService", "prev", "ɵɵtext", "ɵɵelementEnd", "step_r2", "step", "ɵɵadvance", "ɵɵtextInterpolate1", "prevBtnTitle", "TourStepTemplateComponent_ng_template_0_button_3_Template", "_r9", "TourStepTemplateComponent_ng_template_0_button_3_Template_button_click_0_listener", "ctx_r8", "next", "nextBtnTitle", "TourStepTemplateComponent_ng_template_0_Template", "_r12", "ɵɵelement", "ɵɵtemplate", "TourStepTemplateComponent_ng_template_0_Template_button_click_4_listener", "ctx_r11", "end", "ctx_r1", "ɵɵproperty", "content", "ɵɵsanitizeHtml", "has<PERSON>rev", "hasNext", "goToNextOnAnchorClick", "endBtnTitle", "NgbTourService", "ɵfac", "ɵNgbTourService_BaseFactory", "NgbTourService_Factory", "t", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "TourStepTemplateService", "TourStepTemplateService_Factory", "TourAnchorNgBootstrapPopoverDirective", "ɵTourAnchorNgBootstrapPopoverDirective_BaseFactory", "TourAnchorNgBootstrapPopoverDirective_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "features", "ɵɵInheritDefinitionFeature", "selector", "TourAnchorNgBootstrapDirective", "constructor", "tourStepTemplate", "element", "popoverDirective", "tourBackdrop", "autoClose", "triggers", "toggle", "ngOnInit", "register", "tourAnchor", "ngOnDestroy", "unregister", "showTourStep", "htmlElement", "nativeElement", "isActive", "ngbPopover", "template", "popoverTitle", "title", "container", "placement", "replace", "open", "disableScrollToAnchor", "ensureVisible", "enableBackdrop", "show", "close", "hideTourStep", "getStatus", "OFF", "TourAnchorNgBootstrapDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "hostVars", "hostBindings", "TourAnchorNgBootstrapDirective_HostBindings", "ɵɵclassProp", "inputs", "decorators", "TourStepTemplateComponent", "tourStepTemplateService", "ngAfterContentInit", "stepTemplate", "step<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultTourStepTemplate", "TourStepTemplateComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "TourStepTemplateComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "TourStepTemplateComponent_Query", "ɵɵviewQuery", "decls", "vars", "consts", "TourStepTemplateComponent_Template", "ɵɵtemplateRefExtractor", "dependencies", "NgIf", "encapsulation", "None", "read", "static", "TourNgBootstrapModule", "forRoot", "ngModule", "providers", "TourNgBootstrapModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/ngx-ui-tour-ng-bootstrap/fesm2020/ngx-ui-tour-ng-bootstrap.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Directive, Host, Input, HostBinding, TemplateRef, Component, ViewEncapsulation, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport * as i3 from 'ngx-ui-tour-core';\nimport { TourService, ScrollingUtil, TourState, TourHotkeyListenerComponent, TourBackdropService, TourModule } from 'ngx-ui-tour-core';\nimport * as i3$1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NgbPopover, NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';\n\nclass NgbTourService extends TourService {\r\n}\r\nNgbTourService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: NgbTourService, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\r\nNgbTourService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: NgbTourService, providedIn: 'root' });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: NgbTourService, decorators: [{\r\n            type: Injectable,\r\n            args: [{\r\n                    providedIn: 'root',\r\n                }]\r\n        }] });\n\nclass TourStepTemplateService {\r\n}\r\nTourStepTemplateService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourStepTemplateService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\r\nTourStepTemplateService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourStepTemplateService });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourStepTemplateService, decorators: [{\r\n            type: Injectable\r\n        }] });\n\nclass TourAnchorNgBootstrapPopoverDirective extends NgbPopover {\r\n}\r\nTourAnchorNgBootstrapPopoverDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourAnchorNgBootstrapPopoverDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\r\nTourAnchorNgBootstrapPopoverDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.11\", type: TourAnchorNgBootstrapPopoverDirective, selector: \"[tourAnchor]\", usesInheritance: true, ngImport: i0 });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourAnchorNgBootstrapPopoverDirective, decorators: [{\r\n            type: Directive,\r\n            args: [{ selector: '[tourAnchor]' }]\r\n        }] });\r\nclass TourAnchorNgBootstrapDirective {\r\n    constructor(tourService, tourStepTemplate, element, popoverDirective, tourBackdrop) {\r\n        this.tourService = tourService;\r\n        this.tourStepTemplate = tourStepTemplate;\r\n        this.element = element;\r\n        this.popoverDirective = popoverDirective;\r\n        this.tourBackdrop = tourBackdrop;\r\n        this.popoverDirective.autoClose = false;\r\n        this.popoverDirective.triggers = '';\r\n        this.popoverDirective.toggle = () => { };\r\n    }\r\n    ngOnInit() {\r\n        this.tourService.register(this.tourAnchor, this);\r\n    }\r\n    ngOnDestroy() {\r\n        this.tourService.unregister(this.tourAnchor);\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols\r\n    showTourStep(step) {\r\n        const htmlElement = this.element.nativeElement;\r\n        this.isActive = true;\r\n        this.popoverDirective.ngbPopover = this.tourStepTemplate.template;\r\n        this.popoverDirective.popoverTitle = step.title;\r\n        this.popoverDirective.container = 'body';\r\n        this.popoverDirective.placement = (step.placement || 'top')\r\n            .replace('before', 'left').replace('after', 'right')\r\n            .replace('below', 'bottom').replace('above', 'top');\r\n        step.prevBtnTitle = step.prevBtnTitle || 'Prev';\r\n        step.nextBtnTitle = step.nextBtnTitle || 'Next';\r\n        step.endBtnTitle = step.endBtnTitle || 'End';\r\n        this.popoverDirective.open({ step });\r\n        if (!step.disableScrollToAnchor) {\r\n            ScrollingUtil.ensureVisible(htmlElement);\r\n        }\r\n        if (step.enableBackdrop) {\r\n            this.tourBackdrop.show(this.element);\r\n        }\r\n        else {\r\n            this.tourBackdrop.close();\r\n        }\r\n    }\r\n    hideTourStep() {\r\n        this.isActive = false;\r\n        if (this.tourService.getStatus() === TourState.OFF) {\r\n            this.tourBackdrop.close();\r\n        }\r\n        this.popoverDirective.close();\r\n    }\r\n}\r\nTourAnchorNgBootstrapDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourAnchorNgBootstrapDirective, deps: [{ token: NgbTourService }, { token: TourStepTemplateService }, { token: i0.ElementRef }, { token: TourAnchorNgBootstrapPopoverDirective, host: true }, { token: i3.TourBackdropService }], target: i0.ɵɵFactoryTarget.Directive });\r\nTourAnchorNgBootstrapDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.11\", type: TourAnchorNgBootstrapDirective, selector: \"[tourAnchor]\", inputs: { tourAnchor: \"tourAnchor\" }, host: { properties: { \"class.touranchor--is-active\": \"this.isActive\" } }, ngImport: i0 });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourAnchorNgBootstrapDirective, decorators: [{\r\n            type: Directive,\r\n            args: [{\r\n                    selector: '[tourAnchor]',\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: NgbTourService }, { type: TourStepTemplateService }, { type: i0.ElementRef }, { type: TourAnchorNgBootstrapPopoverDirective, decorators: [{\r\n                    type: Host\r\n                }] }, { type: i3.TourBackdropService }]; }, propDecorators: { tourAnchor: [{\r\n                type: Input\r\n            }], isActive: [{\r\n                type: HostBinding,\r\n                args: ['class.touranchor--is-active']\r\n            }] } });\n\nclass TourStepTemplateComponent extends TourHotkeyListenerComponent {\r\n    constructor(tourStepTemplateService, tourService) {\r\n        super(tourService);\r\n        this.tourStepTemplateService = tourStepTemplateService;\r\n        this.tourService = tourService;\r\n    }\r\n    ngAfterContentInit() {\r\n        this.tourStepTemplateService.template =\r\n            this.stepTemplate ||\r\n                this.stepTemplateContent ||\r\n                this.defaultTourStepTemplate;\r\n    }\r\n}\r\nTourStepTemplateComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourStepTemplateComponent, deps: [{ token: TourStepTemplateService }, { token: NgbTourService }], target: i0.ɵɵFactoryTarget.Component });\r\nTourStepTemplateComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.11\", type: TourStepTemplateComponent, selector: \"tour-step-template\", inputs: { stepTemplate: \"stepTemplate\" }, queries: [{ propertyName: \"stepTemplateContent\", first: true, predicate: TemplateRef, descendants: true }], viewQueries: [{ propertyName: \"defaultTourStepTemplate\", first: true, predicate: [\"tourStep\"], descendants: true, read: TemplateRef, static: true }], usesInheritance: true, ngImport: i0, template: `\r\n    <ng-template #tourStep let-step=\"step\">\r\n      <p class=\"tour-step-content\" [innerHTML]=\"step?.content\"></p>\r\n      <div class=\"tour-step-navigation\">\r\n        <button\r\n          *ngIf=\"tourService.hasPrev(step)\"\r\n          class=\"btn btn-sm btn-default\"\r\n          (click)=\"tourService.prev()\"\r\n        >\r\n          « {{ step?.prevBtnTitle }}\r\n        </button>\r\n        <button\r\n          *ngIf=\"tourService.hasNext(step) && !step.goToNextOnAnchorClick\"\r\n          class=\"btn btn-sm btn-default\"\r\n          (click)=\"tourService.next()\"\r\n        >\r\n          {{ step?.nextBtnTitle }} »\r\n        </button>\r\n        <button class=\"btn btn-sm btn-default\" (click)=\"tourService.end()\">\r\n          {{ step?.endBtnTitle }}\r\n        </button>\r\n      </div>\r\n    </ng-template>\r\n  `, isInline: true, directives: [{ type: i3$1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], encapsulation: i0.ViewEncapsulation.None });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourStepTemplateComponent, decorators: [{\r\n            type: Component,\r\n            args: [{\r\n                    encapsulation: ViewEncapsulation.None,\r\n                    selector: 'tour-step-template',\r\n                    template: `\r\n    <ng-template #tourStep let-step=\"step\">\r\n      <p class=\"tour-step-content\" [innerHTML]=\"step?.content\"></p>\r\n      <div class=\"tour-step-navigation\">\r\n        <button\r\n          *ngIf=\"tourService.hasPrev(step)\"\r\n          class=\"btn btn-sm btn-default\"\r\n          (click)=\"tourService.prev()\"\r\n        >\r\n          « {{ step?.prevBtnTitle }}\r\n        </button>\r\n        <button\r\n          *ngIf=\"tourService.hasNext(step) && !step.goToNextOnAnchorClick\"\r\n          class=\"btn btn-sm btn-default\"\r\n          (click)=\"tourService.next()\"\r\n        >\r\n          {{ step?.nextBtnTitle }} »\r\n        </button>\r\n        <button class=\"btn btn-sm btn-default\" (click)=\"tourService.end()\">\r\n          {{ step?.endBtnTitle }}\r\n        </button>\r\n      </div>\r\n    </ng-template>\r\n  `\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: TourStepTemplateService }, { type: NgbTourService }]; }, propDecorators: { defaultTourStepTemplate: [{\r\n                type: ViewChild,\r\n                args: ['tourStep', { read: TemplateRef, static: true }]\r\n            }], stepTemplate: [{\r\n                type: Input\r\n            }], stepTemplateContent: [{\r\n                type: ContentChild,\r\n                args: [TemplateRef]\r\n            }] } });\n\nclass TourNgBootstrapModule {\r\n    static forRoot() {\r\n        return {\r\n            ngModule: TourNgBootstrapModule,\r\n            providers: [\r\n                TourStepTemplateService,\r\n                TourService,\r\n                NgbTourService,\r\n                TourBackdropService\r\n            ],\r\n        };\r\n    }\r\n}\r\nTourNgBootstrapModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourNgBootstrapModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nTourNgBootstrapModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourNgBootstrapModule, declarations: [TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourStepTemplateComponent], imports: [CommonModule, NgbPopoverModule, TourModule], exports: [TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourStepTemplateComponent] });\r\nTourNgBootstrapModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourNgBootstrapModule, imports: [[CommonModule, NgbPopoverModule, TourModule]] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.11\", ngImport: i0, type: TourNgBootstrapModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    declarations: [TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourStepTemplateComponent],\r\n                    exports: [TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourStepTemplateComponent],\r\n                    imports: [CommonModule, NgbPopoverModule, TourModule],\r\n                }]\r\n        }] });\n\n/*\r\n * Public API Surface of ngx-ui-tour-ng-bootstrap\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgbTourService, TourAnchorNgBootstrapDirective, TourAnchorNgBootstrapPopoverDirective, TourNgBootstrapModule, NgbTourService as TourService, TourStepTemplateComponent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC7J,OAAO,KAAKC,EAAE,MAAM,kBAAkB;AACtC,SAASC,WAAW,EAAEC,aAAa,EAAEC,SAAS,EAAEC,2BAA2B,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,kBAAkB;AACtI,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,EAAEC,gBAAgB,QAAQ,4BAA4B;AAAC,MAAAC,GAAA;AAAA,SAAAC,0DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAIwB3B,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,eAgH5F,CAAC;IAhHyF7B,EAAE,CAAA8B,UAAA,mBAAAC,kFAAA;MAAF/B,EAAE,CAAAgC,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CA+GjFF,MAAA,CAAAG,WAAA,CAAAC,IAAA,CAAiB,EAAC;IAAA,EAAC;IA/G4DrC,EAAE,CAAAsC,MAAA,EAkH7F,CAAC;IAlH0FtC,EAAE,CAAAuC,YAAA,CAkHpF,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAe,OAAA,GAlHiFxC,EAAE,CAAAkC,aAAA,GAAAO,IAAA;IAAFzC,EAAE,CAAA0C,SAAA,EAkH7F,CAAC;IAlH0F1C,EAAE,CAAA2C,kBAAA,WAAAH,OAAA,kBAAAA,OAAA,CAAAI,YAAA,KAkH7F,CAAC;EAAA;AAAA;AAAA,SAAAC,0DAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqB,GAAA,GAlH0F9C,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAA6B,cAAA,eAuH5F,CAAC;IAvHyF7B,EAAE,CAAA8B,UAAA,mBAAAiB,kFAAA;MAAF/C,EAAE,CAAAgC,aAAA,CAAAc,GAAA;MAAA,MAAAE,MAAA,GAAFhD,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CAsHjFa,MAAA,CAAAZ,WAAA,CAAAa,IAAA,CAAiB,EAAC;IAAA,EAAC;IAtH4DjD,EAAE,CAAAsC,MAAA,EAyH7F,CAAC;IAzH0FtC,EAAE,CAAAuC,YAAA,CAyHpF,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAe,OAAA,GAzHiFxC,EAAE,CAAAkC,aAAA,GAAAO,IAAA;IAAFzC,EAAE,CAAA0C,SAAA,EAyH7F,CAAC;IAzH0F1C,EAAE,CAAA2C,kBAAA,MAAAH,OAAA,kBAAAA,OAAA,CAAAU,YAAA,UAyH7F,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2B,IAAA,GAzH0FpD,EAAE,CAAA4B,gBAAA;IAAF5B,EAAE,CAAAqD,SAAA,UA0GlC,CAAC;IA1G+BrD,EAAE,CAAA6B,cAAA,YA2G7D,CAAC;IA3G0D7B,EAAE,CAAAsD,UAAA,IAAA9B,yDAAA,mBAkHpF,CAAC;IAlHiFxB,EAAE,CAAAsD,UAAA,IAAAT,yDAAA,mBAyHpF,CAAC;IAzHiF7C,EAAE,CAAA6B,cAAA,eA0H1B,CAAC;IA1HuB7B,EAAE,CAAA8B,UAAA,mBAAAyB,yEAAA;MAAFvD,EAAE,CAAAgC,aAAA,CAAAoB,IAAA;MAAA,MAAAI,OAAA,GAAFxD,EAAE,CAAAkC,aAAA;MAAA,OAAFlC,EAAE,CAAAmC,WAAA,CA0H5CqB,OAAA,CAAApB,WAAA,CAAAqB,GAAA,CAAgB,EAAC;IAAA,EAAC;IA1HwBzD,EAAE,CAAAsC,MAAA,EA4H7F,CAAC;IA5H0FtC,EAAE,CAAAuC,YAAA,CA4HpF,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAd,EAAA;IAAA,MAAAe,OAAA,GAAAd,GAAA,CAAAe,IAAA;IAAA,MAAAiB,MAAA,GA5HiF1D,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAA2D,UAAA,cAAAnB,OAAA,kBAAAA,OAAA,CAAAoB,OAAA,EAAF5D,EAAE,CAAA6D,cA0GvC,CAAC;IA1GoC7D,EAAE,CAAA0C,SAAA,EA6G3D,CAAC;IA7GwD1C,EAAE,CAAA2D,UAAA,SAAAD,MAAA,CAAAtB,WAAA,CAAA0B,OAAA,CAAAtB,OAAA,CA6G3D,CAAC;IA7GwDxC,EAAE,CAAA0C,SAAA,EAoH5B,CAAC;IApHyB1C,EAAE,CAAA2D,UAAA,SAAAD,MAAA,CAAAtB,WAAA,CAAA2B,OAAA,CAAAvB,OAAA,MAAAA,OAAA,CAAAwB,qBAoH5B,CAAC;IApHyBhE,EAAE,CAAA0C,SAAA,EA4H7F,CAAC;IA5H0F1C,EAAE,CAAA2C,kBAAA,MAAAH,OAAA,kBAAAA,OAAA,CAAAyB,WAAA,KA4H7F,CAAC;EAAA;AAAA;AA9HR,MAAMC,cAAc,SAASrD,WAAW,CAAC;AAEzCqD,cAAc,CAACC,IAAI;EAAA,IAAAC,2BAAA;EAAA,gBAAAC,uBAAAC,CAAA;IAAA,QAAAF,2BAAA,KAAAA,2BAAA,GAA+EpE,EAAE,CAAAuE,qBAAA,CAAQL,cAAc,IAAAI,CAAA,IAAdJ,cAAc;EAAA;AAAA,GAAsD;AAChLA,cAAc,CAACM,KAAK,kBAD8ExE,EAAE,CAAAyE,kBAAA;EAAAC,KAAA,EACYR,cAAc;EAAAS,OAAA,EAAdT,cAAc,CAAAC,IAAA;EAAAS,UAAA,EAAc;AAAM,EAAG;AACrJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFkG7E,EAAE,CAAA8E,iBAAA,CAERZ,cAAc,EAAc,CAAC;IAC7Ga,IAAI,EAAE9E,UAAU;IAChB+E,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,uBAAuB,CAAC;AAE9BA,uBAAuB,CAACd,IAAI,YAAAe,gCAAAZ,CAAA;EAAA,YAAAA,CAAA,IAAyFW,uBAAuB;AAAA,CAAoD;AAChMA,uBAAuB,CAACT,KAAK,kBAZqExE,EAAE,CAAAyE,kBAAA;EAAAC,KAAA,EAYqBO,uBAAuB;EAAAN,OAAA,EAAvBM,uBAAuB,CAAAd;AAAA,EAAG;AACnJ;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAbkG7E,EAAE,CAAA8E,iBAAA,CAaRG,uBAAuB,EAAc,CAAC;IACtHF,IAAI,EAAE9E;EACV,CAAC,CAAC;AAAA;AAEV,MAAMkF,qCAAqC,SAAS9D,UAAU,CAAC;AAE/D8D,qCAAqC,CAAChB,IAAI;EAAA,IAAAiB,kDAAA;EAAA,gBAAAC,8CAAAf,CAAA;IAAA,QAAAc,kDAAA,KAAAA,kDAAA,GAnBwDpF,EAAE,CAAAuE,qBAAA,CAmB+BY,qCAAqC,IAAAb,CAAA,IAArCa,qCAAqC;EAAA;AAAA,GAAqD;AAC7NA,qCAAqC,CAACG,IAAI,kBApBwDtF,EAAE,CAAAuF,iBAAA;EAAAR,IAAA,EAoBmBI,qCAAqC;EAAAK,SAAA;EAAAC,QAAA,GApB1DzF,EAAE,CAAA0F,0BAAA;AAAA,EAoB0H;AAC9N;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KArBkG7E,EAAE,CAAA8E,iBAAA,CAqBRK,qCAAqC,EAAc,CAAC;IACpIJ,IAAI,EAAE7E,SAAS;IACf8E,IAAI,EAAE,CAAC;MAAEW,QAAQ,EAAE;IAAe,CAAC;EACvC,CAAC,CAAC;AAAA;AACV,MAAMC,8BAA8B,CAAC;EACjCC,WAAWA,CAACzD,WAAW,EAAE0D,gBAAgB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,YAAY,EAAE;IAChF,IAAI,CAAC7D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC0D,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACD,gBAAgB,CAACE,SAAS,GAAG,KAAK;IACvC,IAAI,CAACF,gBAAgB,CAACG,QAAQ,GAAG,EAAE;IACnC,IAAI,CAACH,gBAAgB,CAACI,MAAM,GAAG,MAAM,CAAE,CAAC;EAC5C;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACjE,WAAW,CAACkE,QAAQ,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAAC;EACpD;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpE,WAAW,CAACqE,UAAU,CAAC,IAAI,CAACF,UAAU,CAAC;EAChD;EACA;EACAG,YAAYA,CAACjE,IAAI,EAAE;IACf,MAAMkE,WAAW,GAAG,IAAI,CAACZ,OAAO,CAACa,aAAa;IAC9C,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACb,gBAAgB,CAACc,UAAU,GAAG,IAAI,CAAChB,gBAAgB,CAACiB,QAAQ;IACjE,IAAI,CAACf,gBAAgB,CAACgB,YAAY,GAAGvE,IAAI,CAACwE,KAAK;IAC/C,IAAI,CAACjB,gBAAgB,CAACkB,SAAS,GAAG,MAAM;IACxC,IAAI,CAAClB,gBAAgB,CAACmB,SAAS,GAAG,CAAC1E,IAAI,CAAC0E,SAAS,IAAI,KAAK,EACrDC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CACnDA,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;IACvD3E,IAAI,CAACG,YAAY,GAAGH,IAAI,CAACG,YAAY,IAAI,MAAM;IAC/CH,IAAI,CAACS,YAAY,GAAGT,IAAI,CAACS,YAAY,IAAI,MAAM;IAC/CT,IAAI,CAACwB,WAAW,GAAGxB,IAAI,CAACwB,WAAW,IAAI,KAAK;IAC5C,IAAI,CAAC+B,gBAAgB,CAACqB,IAAI,CAAC;MAAE5E;IAAK,CAAC,CAAC;IACpC,IAAI,CAACA,IAAI,CAAC6E,qBAAqB,EAAE;MAC7BxG,aAAa,CAACyG,aAAa,CAACZ,WAAW,CAAC;IAC5C;IACA,IAAIlE,IAAI,CAAC+E,cAAc,EAAE;MACrB,IAAI,CAACvB,YAAY,CAACwB,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAACE,YAAY,CAACyB,KAAK,CAAC,CAAC;IAC7B;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACd,QAAQ,GAAG,KAAK;IACrB,IAAI,IAAI,CAACzE,WAAW,CAACwF,SAAS,CAAC,CAAC,KAAK7G,SAAS,CAAC8G,GAAG,EAAE;MAChD,IAAI,CAAC5B,YAAY,CAACyB,KAAK,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC1B,gBAAgB,CAAC0B,KAAK,CAAC,CAAC;EACjC;AACJ;AACA9B,8BAA8B,CAACzB,IAAI,YAAA2D,uCAAAxD,CAAA;EAAA,YAAAA,CAAA,IAAyFsB,8BAA8B,EA1ExD5F,EAAE,CAAA+H,iBAAA,CA0EwE7D,cAAc,GA1ExFlE,EAAE,CAAA+H,iBAAA,CA0EmG9C,uBAAuB,GA1E5HjF,EAAE,CAAA+H,iBAAA,CA0EuI/H,EAAE,CAACgI,UAAU,GA1EtJhI,EAAE,CAAA+H,iBAAA,CA0EiK5C,qCAAqC,MA1ExMnF,EAAE,CAAA+H,iBAAA,CA0E+NnH,EAAE,CAACK,mBAAmB;AAAA,CAA4C;AACrY2E,8BAA8B,CAACN,IAAI,kBA3E+DtF,EAAE,CAAAuF,iBAAA;EAAAR,IAAA,EA2EYa,8BAA8B;EAAAJ,SAAA;EAAAyC,QAAA;EAAAC,YAAA,WAAAC,4CAAA1G,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3E5CzB,EAAE,CAAAoI,WAAA,0BAAA1G,GAAA,CAAAmF,QAAA;IAAA;EAAA;EAAAwB,MAAA;IAAA9B,UAAA;EAAA;AAAA,EA2EqM;AACzS;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KA5EkG7E,EAAE,CAAA8E,iBAAA,CA4ERc,8BAA8B,EAAc,CAAC;IAC7Hb,IAAI,EAAE7E,SAAS;IACf8E,IAAI,EAAE,CAAC;MACCW,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEZ,IAAI,EAAEb;IAAe,CAAC,EAAE;MAAEa,IAAI,EAAEE;IAAwB,CAAC,EAAE;MAAEF,IAAI,EAAE/E,EAAE,CAACgI;IAAW,CAAC,EAAE;MAAEjD,IAAI,EAAEI,qCAAqC;MAAEmD,UAAU,EAAE,CAAC;QAChLvD,IAAI,EAAE5E;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4E,IAAI,EAAEnE,EAAE,CAACK;IAAoB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsF,UAAU,EAAE,CAAC;MAC3ExB,IAAI,EAAE3E;IACV,CAAC,CAAC;IAAEyG,QAAQ,EAAE,CAAC;MACX9B,IAAI,EAAE1E,WAAW;MACjB2E,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuD,yBAAyB,SAASvH,2BAA2B,CAAC;EAChE6E,WAAWA,CAAC2C,uBAAuB,EAAEpG,WAAW,EAAE;IAC9C,KAAK,CAACA,WAAW,CAAC;IAClB,IAAI,CAACoG,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACpG,WAAW,GAAGA,WAAW;EAClC;EACAqG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,uBAAuB,CAACzB,QAAQ,GACjC,IAAI,CAAC2B,YAAY,IACb,IAAI,CAACC,mBAAmB,IACxB,IAAI,CAACC,uBAAuB;EACxC;AACJ;AACAL,yBAAyB,CAACpE,IAAI,YAAA0E,kCAAAvE,CAAA;EAAA,YAAAA,CAAA,IAAyFiE,yBAAyB,EAvG9CvI,EAAE,CAAA+H,iBAAA,CAuG8D9C,uBAAuB,GAvGvFjF,EAAE,CAAA+H,iBAAA,CAuGkG7D,cAAc;AAAA,CAA4C;AAChQqE,yBAAyB,CAACO,IAAI,kBAxGoE9I,EAAE,CAAA+I,iBAAA;EAAAhE,IAAA,EAwGOwD,yBAAyB;EAAA/C,SAAA;EAAAwD,cAAA,WAAAC,yCAAAxH,EAAA,EAAAC,GAAA,EAAAwH,QAAA;IAAA,IAAAzH,EAAA;MAxGlCzB,EAAE,CAAAmJ,cAAA,CAAAD,QAAA,EAwGqL5I,WAAW;IAAA;IAAA,IAAAmB,EAAA;MAAA,IAAA2H,EAAA;MAxGlMpJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAA5H,GAAA,CAAAiH,mBAAA,GAAAS,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,SAAA,WAAAC,gCAAAhI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAA0J,WAAA,CAAAnI,GAAA,KAwGgVjB,WAAW;IAAA;IAAA,IAAAmB,EAAA;MAAA,IAAA2H,EAAA;MAxG7VpJ,EAAE,CAAAqJ,cAAA,CAAAD,EAAA,GAAFpJ,EAAE,CAAAsJ,WAAA,QAAA5H,GAAA,CAAAkH,uBAAA,GAAAQ,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAlB,MAAA;IAAAK,YAAA;EAAA;EAAAjD,QAAA,GAAFzF,EAAE,CAAA0F,0BAAA;EAAAiE,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAA9C,QAAA,WAAA+C,mCAAArI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzB,EAAE,CAAAsD,UAAA,IAAAH,gDAAA,gCAAFnD,EAAE,CAAA+J,sBA8HnF,CAAC;IAAA;EAAA;EAAAC,YAAA,GACwB7I,IAAI,CAAC8I,IAAI;EAAAC,aAAA;AAAA,EAA8G;AACjK;EAAA,QAAArF,SAAA,oBAAAA,SAAA,KAhIkG7E,EAAE,CAAA8E,iBAAA,CAgIRyD,yBAAyB,EAAc,CAAC;IACxHxD,IAAI,EAAExE,SAAS;IACfyE,IAAI,EAAE,CAAC;MACCkF,aAAa,EAAE1J,iBAAiB,CAAC2J,IAAI;MACrCxE,QAAQ,EAAE,oBAAoB;MAC9BoB,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhC,IAAI,EAAEE;IAAwB,CAAC,EAAE;MAAEF,IAAI,EAAEb;IAAe,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0E,uBAAuB,EAAE,CAAC;MAC/I7D,IAAI,EAAEtE,SAAS;MACfuE,IAAI,EAAE,CAAC,UAAU,EAAE;QAAEoF,IAAI,EAAE9J,WAAW;QAAE+J,MAAM,EAAE;MAAK,CAAC;IAC1D,CAAC,CAAC;IAAE3B,YAAY,EAAE,CAAC;MACf3D,IAAI,EAAE3E;IACV,CAAC,CAAC;IAAEuI,mBAAmB,EAAE,CAAC;MACtB5D,IAAI,EAAErE,YAAY;MAClBsE,IAAI,EAAE,CAAC1E,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgK,qBAAqB,CAAC;EACxB,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,qBAAqB;MAC/BG,SAAS,EAAE,CACPxF,uBAAuB,EACvBpE,WAAW,EACXqD,cAAc,EACdjD,mBAAmB;IAE3B,CAAC;EACL;AACJ;AACAqJ,qBAAqB,CAACnG,IAAI,YAAAuG,8BAAApG,CAAA;EAAA,YAAAA,CAAA,IAAyFgG,qBAAqB;AAAA,CAAkD;AAC1LA,qBAAqB,CAACK,IAAI,kBAtLwE3K,EAAE,CAAA4K,gBAAA;EAAA7F,IAAA,EAsLgBuF;AAAqB,EAAyR;AAClaA,qBAAqB,CAACO,IAAI,kBAvLwE7K,EAAE,CAAA8K,gBAAA;EAAAC,OAAA,GAuLiD,CAAC3J,YAAY,EAAEE,gBAAgB,EAAEJ,UAAU,CAAC;AAAA,EAAI;AACrM;EAAA,QAAA2D,SAAA,oBAAAA,SAAA,KAxLkG7E,EAAE,CAAA8E,iBAAA,CAwLRwF,qBAAqB,EAAc,CAAC;IACpHvF,IAAI,EAAEpE,QAAQ;IACdqE,IAAI,EAAE,CAAC;MACCgG,YAAY,EAAE,CAACpF,8BAA8B,EAAET,qCAAqC,EAAEoD,yBAAyB,CAAC;MAChH0C,OAAO,EAAE,CAACrF,8BAA8B,EAAET,qCAAqC,EAAEoD,yBAAyB,CAAC;MAC3GwC,OAAO,EAAE,CAAC3J,YAAY,EAAEE,gBAAgB,EAAEJ,UAAU;IACxD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASgD,cAAc,EAAE0B,8BAA8B,EAAET,qCAAqC,EAAEmF,qBAAqB,EAAEpG,cAAc,IAAIrD,WAAW,EAAE0H,yBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
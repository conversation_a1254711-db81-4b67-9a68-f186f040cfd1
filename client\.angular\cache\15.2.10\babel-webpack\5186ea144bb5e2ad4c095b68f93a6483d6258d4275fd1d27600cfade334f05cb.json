{"ast": null, "code": "import { BehaviorSubject, Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/auth.service\";\nexport class CoreMenuService {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {Router} _router\r\n   * @param {AuthenticationService} _authService\r\n   */\n  constructor(_router, _authService) {\n    this._router = _router;\n    this._authService = _authService;\n    this._registry = {};\n    // Set defaults\n    this.onItemCollapsed = new Subject();\n    this.onItemCollapseToggled = new Subject();\n    // Set private defaults\n    this._currentMenuKey = null;\n    this._onMenuRegistered = new BehaviorSubject(null);\n    this._onMenuUnregistered = new BehaviorSubject(null);\n    this._onMenuChanged = new BehaviorSubject(null);\n  }\n  // Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * onMenuRegistered\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\n  get onMenuRegistered() {\n    return this._onMenuRegistered.asObservable();\n  }\n  /**\r\n   * onMenuUnregistered\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\n  get onMenuUnregistered() {\n    return this._onMenuUnregistered.asObservable();\n  }\n  /**\r\n   * onMenuChanged\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\n  get onMenuChanged() {\n    return this._onMenuChanged.asObservable();\n  }\n  // Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Register the provided menu with the provided key\r\n   *\r\n   * @param key\r\n   * @param menu\r\n   */\n  register(key, menu) {\n    // Confirm if the key already used\n    if (this._registry[key]) {\n      console.error(`Menu with the key '${key}' already exists. Either unregister it first or use a unique key.`);\n      return;\n    }\n    // Add to registry\n    this._registry[key] = menu;\n    // Notify subject\n    this._onMenuRegistered.next([key, menu]);\n  }\n  /**\r\n   * Unregister the menu from the registry\r\n   *\r\n   * @param key\r\n   */\n  unregister(key) {\n    // Confirm if the menu exists\n    if (!this._registry[key]) {\n      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);\n    }\n    // Unregister sidebar\n    delete this._registry[key];\n    // Notify subject\n    this._onMenuUnregistered.next(key);\n  }\n  /**\r\n   * Get menu from registry by key\r\n   *\r\n   * @param key\r\n   * @returns {any}\r\n   */\n  getMenu(key) {\n    // Confirm if the menu exists\n    if (!this._registry[key]) {\n      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);\n      return;\n    }\n    // Return sidebar\n    return this._registry[key];\n  }\n  /**\r\n   * Get current menu\r\n   *\r\n   * @returns {any}\r\n   */\n  getCurrentMenu() {\n    if (!this._currentMenuKey) {\n      console.warn(`The current menu is not set.`);\n      return;\n    }\n    return this.getMenu(this._currentMenuKey);\n  }\n  /**\r\n   * Set menu with the key as the current menu\r\n   *\r\n   * @param key\r\n   */\n  setCurrentMenu(key) {\n    // Confirm if the sidebar exists\n    if (!this._registry[key]) {\n      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);\n      return;\n    }\n    // Set current menu key\n    this._currentMenuKey = key;\n    // Notify subject\n    this._onMenuChanged.next(key);\n  }\n  static #_ = this.ɵfac = function CoreMenuService_Factory(t) {\n    return new (t || CoreMenuService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CoreMenuService,\n    factory: CoreMenuService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,eAAe,EAAcC,OAAO,QAAQ,MAAM;;;;AAQ3D,OAAM,MAAOC,eAAe;EAY1B;;;;;;EAMAC,YAAoBC,OAAe,EAAUC,YAAyB;IAAlD,KAAAD,OAAO,GAAPA,OAAO;IAAkB,KAAAC,YAAY,GAAZA,YAAY;IARjD,KAAAC,SAAS,GAA2B,EAAE;IAS5C;IACA,IAAI,CAACC,eAAe,GAAG,IAAIN,OAAO,EAAE;IACpC,IAAI,CAACO,qBAAqB,GAAG,IAAIP,OAAO,EAAE;IAE1C;IACA,IAAI,CAACQ,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,iBAAiB,GAAG,IAAIV,eAAe,CAAC,IAAI,CAAC;IAClD,IAAI,CAACW,mBAAmB,GAAG,IAAIX,eAAe,CAAC,IAAI,CAAC;IACpD,IAAI,CAACY,cAAc,GAAG,IAAIZ,eAAe,CAAC,IAAI,CAAC;EACjD;EAEA;EACA;EAEA;;;;;EAKA,IAAIa,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACH,iBAAiB,CAACI,YAAY,EAAE;EAC9C;EAEA;;;;;EAKA,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACJ,mBAAmB,CAACG,YAAY,EAAE;EAChD;EAEA;;;;;EAKA,IAAIE,aAAaA,CAAA;IACf,OAAO,IAAI,CAACJ,cAAc,CAACE,YAAY,EAAE;EAC3C;EAEA;EACA;EAEA;;;;;;EAMAG,QAAQA,CAACC,GAAG,EAAEC,IAAI;IAChB;IACA,IAAI,IAAI,CAACb,SAAS,CAACY,GAAG,CAAC,EAAE;MACvBE,OAAO,CAACC,KAAK,CAAC,sBAAsBH,GAAG,mEAAmE,CAAC;MAE3G;;IAGF;IACA,IAAI,CAACZ,SAAS,CAACY,GAAG,CAAC,GAAGC,IAAI;IAE1B;IACA,IAAI,CAACT,iBAAiB,CAACY,IAAI,CAAC,CAACJ,GAAG,EAAEC,IAAI,CAAC,CAAC;EAC1C;EAEA;;;;;EAKAI,UAAUA,CAACL,GAAG;IACZ;IACA,IAAI,CAAC,IAAI,CAACZ,SAAS,CAACY,GAAG,CAAC,EAAE;MACxBE,OAAO,CAACI,IAAI,CAAC,sBAAsBN,GAAG,kCAAkC,CAAC;;IAG3E;IACA,OAAO,IAAI,CAACZ,SAAS,CAACY,GAAG,CAAC;IAE1B;IACA,IAAI,CAACP,mBAAmB,CAACW,IAAI,CAACJ,GAAG,CAAC;EACpC;EAEA;;;;;;EAMAO,OAAOA,CAACP,GAAG;IACT;IACA,IAAI,CAAC,IAAI,CAACZ,SAAS,CAACY,GAAG,CAAC,EAAE;MACxBE,OAAO,CAACI,IAAI,CAAC,sBAAsBN,GAAG,kCAAkC,CAAC;MAEzE;;IAGF;IACA,OAAO,IAAI,CAACZ,SAAS,CAACY,GAAG,CAAC;EAC5B;EAEA;;;;;EAKAQ,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjB,eAAe,EAAE;MACzBW,OAAO,CAACI,IAAI,CAAC,8BAA8B,CAAC;MAE5C;;IAGF,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,CAAChB,eAAe,CAAC;EAC3C;EAEA;;;;;EAKAkB,cAAcA,CAACT,GAAG;IAChB;IACA,IAAI,CAAC,IAAI,CAACZ,SAAS,CAACY,GAAG,CAAC,EAAE;MACxBE,OAAO,CAACI,IAAI,CAAC,sBAAsBN,GAAG,kCAAkC,CAAC;MAEzE;;IAGF;IACA,IAAI,CAACT,eAAe,GAAGS,GAAG;IAE1B;IACA,IAAI,CAACN,cAAc,CAACU,IAAI,CAACJ,GAAG,CAAC;EAC/B;EAAC,QAAAU,CAAA;qBAzJU1B,eAAe,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;WAAfjC,eAAe;IAAAkC,OAAA,EAAflC,eAAe,CAAAmC,IAAA;IAAAC,UAAA,EAFd;EAAM", "names": ["BehaviorSubject", "Subject", "CoreMenuService", "constructor", "_router", "_authService", "_registry", "onItemCollapsed", "onItemCollapseToggled", "_currentMenuKey", "_onMenuRegistered", "_onMenuUnregistered", "_onMenuChanged", "onMenuRegistered", "asObservable", "onMenuUnregistered", "onMenuChanged", "register", "key", "menu", "console", "error", "next", "unregister", "warn", "getMenu", "getCurrentMenu", "setCurrentMenu", "_", "i0", "ɵɵinject", "i1", "Router", "i2", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\core-menu.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\r\n\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { User } from 'app/interfaces/user';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CoreMenuService {\r\n  currentUser: User;\r\n  onItemCollapsed: Subject<any>;\r\n  onItemCollapseToggled: Subject<any>;\r\n\r\n  // Private\r\n  private _onMenuRegistered: BehaviorSubject<any>;\r\n  private _onMenuUnregistered: BehaviorSubject<any>;\r\n  private _onMenuChanged: BehaviorSubject<any>;\r\n  private _currentMenuKey: string;\r\n  private _registry: { [key: string]: any } = {};\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {Router} _router\r\n   * @param {AuthenticationService} _authService\r\n   */\r\n  constructor(private _router: Router, private _authService: AuthService) {    \r\n    // Set defaults\r\n    this.onItemCollapsed = new Subject();\r\n    this.onItemCollapseToggled = new Subject();\r\n\r\n    // Set private defaults\r\n    this._currentMenuKey = null;\r\n    this._onMenuRegistered = new BehaviorSubject(null);\r\n    this._onMenuUnregistered = new BehaviorSubject(null);\r\n    this._onMenuChanged = new BehaviorSubject(null);\r\n  }\r\n\r\n  // Accessors\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * onMenuRegistered\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\r\n  get onMenuRegistered(): Observable<any> {\r\n    return this._onMenuRegistered.asObservable();\r\n  }\r\n\r\n  /**\r\n   * onMenuUnregistered\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\r\n  get onMenuUnregistered(): Observable<any> {\r\n    return this._onMenuUnregistered.asObservable();\r\n  }\r\n\r\n  /**\r\n   * onMenuChanged\r\n   *\r\n   * @returns {Observable<any>}\r\n   */\r\n  get onMenuChanged(): Observable<any> {\r\n    return this._onMenuChanged.asObservable();\r\n  }\r\n\r\n  // Public methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Register the provided menu with the provided key\r\n   *\r\n   * @param key\r\n   * @param menu\r\n   */\r\n  register(key, menu): void {\r\n    // Confirm if the key already used\r\n    if (this._registry[key]) {\r\n      console.error(`Menu with the key '${key}' already exists. Either unregister it first or use a unique key.`);\r\n\r\n      return;\r\n    }\r\n\r\n    // Add to registry\r\n    this._registry[key] = menu;\r\n\r\n    // Notify subject\r\n    this._onMenuRegistered.next([key, menu]);\r\n  }\r\n\r\n  /**\r\n   * Unregister the menu from the registry\r\n   *\r\n   * @param key\r\n   */\r\n  unregister(key): void {\r\n    // Confirm if the menu exists\r\n    if (!this._registry[key]) {\r\n      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);\r\n    }\r\n\r\n    // Unregister sidebar\r\n    delete this._registry[key];\r\n\r\n    // Notify subject\r\n    this._onMenuUnregistered.next(key);\r\n  }\r\n\r\n  /**\r\n   * Get menu from registry by key\r\n   *\r\n   * @param key\r\n   * @returns {any}\r\n   */\r\n  getMenu(key): any {\r\n    // Confirm if the menu exists\r\n    if (!this._registry[key]) {\r\n      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);\r\n\r\n      return;\r\n    }\r\n\r\n    // Return sidebar\r\n    return this._registry[key];\r\n  }\r\n\r\n  /**\r\n   * Get current menu\r\n   *\r\n   * @returns {any}\r\n   */\r\n  getCurrentMenu(): any {\r\n    if (!this._currentMenuKey) {\r\n      console.warn(`The current menu is not set.`);\r\n\r\n      return;\r\n    }\r\n\r\n    return this.getMenu(this._currentMenuKey);\r\n  }\r\n\r\n  /**\r\n   * Set menu with the key as the current menu\r\n   *\r\n   * @param key\r\n   */\r\n  setCurrentMenu(key): void {\r\n    // Confirm if the sidebar exists\r\n    if (!this._registry[key]) {\r\n      console.warn(`Menu with the key '${key}' doesn't exist in the registry.`);\r\n\r\n      return;\r\n    }\r\n\r\n    // Set current menu key\r\n    this._currentMenuKey = key;\r\n\r\n    // Notify subject\r\n    this._onMenuChanged.next(key);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const ArgumentOutOfRangeErrorImpl = (() => {\n  function ArgumentOutOfRangeErrorImpl() {\n    Error.call(this);\n    this.message = 'argument out of range';\n    this.name = 'ArgumentOutOfRangeError';\n    return this;\n  }\n  ArgumentOutOfRangeErrorImpl.prototype = Object.create(Error.prototype);\n  return ArgumentOutOfRangeErrorImpl;\n})();\nexport const ArgumentOutOfRangeError = ArgumentOutOfRangeErrorImpl;", "map": {"version": 3, "names": ["ArgumentOutOfRangeErrorImpl", "Error", "call", "message", "name", "prototype", "Object", "create", "ArgumentOutOfRangeError"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/ArgumentOutOfRangeError.js"], "sourcesContent": ["const ArgumentOutOfRangeErrorImpl = (() => {\n    function ArgumentOutOfRangeErrorImpl() {\n        Error.call(this);\n        this.message = 'argument out of range';\n        this.name = 'ArgumentOutOfRangeError';\n        return this;\n    }\n    ArgumentOutOfRangeErrorImpl.prototype = Object.create(Error.prototype);\n    return ArgumentOutOfRangeErrorImpl;\n})();\nexport const ArgumentOutOfRangeError = ArgumentOutOfRangeErrorImpl;\n"], "mappings": "AAAA,MAAMA,2BAA2B,GAAG,CAAC,MAAM;EACvC,SAASA,2BAA2BA,CAAA,EAAG;IACnCC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,uBAAuB;IACtC,IAAI,CAACC,IAAI,GAAG,yBAAyB;IACrC,OAAO,IAAI;EACf;EACAJ,2BAA2B,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,KAAK,CAACI,SAAS,CAAC;EACtE,OAAOL,2BAA2B;AACtC,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMQ,uBAAuB,GAAGR,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
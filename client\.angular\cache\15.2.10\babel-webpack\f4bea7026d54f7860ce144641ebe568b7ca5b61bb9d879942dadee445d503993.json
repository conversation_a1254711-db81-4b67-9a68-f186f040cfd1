{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CropperDialog } from './cropper-dialog';\nimport { Capacitor } from '@capacitor/core';\nimport { Camera, CameraResultType, CameraSource } from '@capacitor/camera';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@alyle/ui/dialog\";\nimport * as i2 from \"@angular/cdk/platform\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nfunction CropperDialogComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"button\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 8)(6, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function CropperDialogComponent_div_3_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.selectPhotoNative(false));\n    });\n    i0.ɵɵelement(7, \"i\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function CropperDialogComponent_div_3_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.selectPhotoNative(true));\n    });\n    i0.ɵɵelement(11, \"i\", 11);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, \"Upload image\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 6, \"Photo Library\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 8, \"Take Photo\"), \" \");\n  }\n}\nfunction CropperDialogComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function CropperDialogComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(_r0.click());\n    });\n    i0.ɵɵelementStart(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"Upload image\"), \"\");\n  }\n}\nfunction CropperDialogComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"button\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 15)(6, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function CropperDialogComponent_div_5_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r9.onSelectOption(null, _r0));\n    });\n    i0.ɵɵelement(7, \"i\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 9);\n    i0.ɵɵlistener(\"click\", function CropperDialogComponent_div_5_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r11.onSelectOption(\"true\", _r0));\n    });\n    i0.ɵɵelement(11, \"i\", 16);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, \"Upload image\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 6, \"Photo Library\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 8, \"Take Photo\"), \" \");\n  }\n}\nexport class CropperDialogComponent {\n  constructor(_dialog, _cd, platform, _translateService) {\n    this._dialog = _dialog;\n    this._cd = _cd;\n    this.platform = platform;\n    this._translateService = _translateService;\n    this.Capacitor = Capacitor;\n    this.isAndroid = this.platform.ANDROID;\n    this.isIOS = this.platform.IOS;\n    this.accept_types = [];\n    this.disabled = false;\n    this.capture = null;\n    this.accept = 'image/*';\n    this.useCropperDialog = true;\n    this.maxFileSize = 25000; //KB\n    this.croppedImage = new EventEmitter();\n    this.acceptMode = 'image/*';\n  }\n  ngOnInit() {\n    this.acceptMode = this.accept;\n  }\n  openCropperDialog(event) {\n    console.log(event);\n    let dialog_config = {\n      data: {\n        event,\n        config: this.cropper_config,\n        overlayImageURL: this.overlayImageURL\n      },\n      width: [420, '100vw@XSmall'],\n      height: '100vh@XSmall',\n      maxWidth: '100vw@XSmall',\n      maxHeight: '90vh@XSmall',\n      disableClose: true\n    };\n    dialog_config = {\n      ...dialog_config,\n      ...this.dialog_config\n    };\n    this.cropped = null;\n    this._dialog.open(CropperDialog, dialog_config).afterClosed.subscribe(result => {\n      if (result) {\n        this.croppedImage.emit({\n          type: 'base64',\n          data: result.dataURL,\n          preview: result.dataURL\n        });\n        this._cd.markForCheck();\n      }\n    });\n  }\n  onChange(event) {\n    console.log('onchange', event);\n    let file = event.target.files[0];\n    if (file) {\n      if (this.checkType(file)) {\n        if (this.checkSize(file)) {\n          if (this.useCropperDialog) {\n            this.openCropperDialog(event);\n          } else {\n            this.getDataUrl(file).then(base64 => {\n              this.croppedImage.emit({\n                type: 'file',\n                data: file,\n                preview: base64\n              });\n            });\n          }\n        } else {\n          Swal.fire({\n            icon: 'error',\n            title: this._translateService.instant('File size is too large!'),\n            text: this._translateService.instant('Please upload a file smaller than {{size}}MB', {\n              size: this.maxFileSize / 1000\n            })\n          });\n        }\n      } else {\n        Swal.fire({\n          icon: 'error',\n          title: 'Invalid file type!',\n          text: this._translateService.instant('Please upload a file with type {{type}}', {\n            type: this.accept\n          })\n        });\n      }\n    }\n  }\n  // check size of file\n  checkSize(file) {\n    console.log('check file', file.size, this.maxFileSize * 1024);\n    if (file.size > this.maxFileSize * 1024) {\n      return false;\n    }\n    return true;\n  }\n  // check type of file\n  checkType(file) {\n    let ext = file.type;\n    console.log('check ext', ext);\n    let type = ext.split('/')[1];\n    // replace regex image/ , video/ , audio/ , */ to empty string\n    let regex = /[^\\/,]+\\//g;\n    let accept = this.accept.replace(regex, '').replace(' ', '');\n    console.log('check type', type, accept);\n    this.accept_types = accept.split(',');\n    if (this.accept_types.length == 1 && this.accept_types[0] == '*') {\n      return true;\n    } else if (this.accept_types.includes(type)) {\n      return true;\n    }\n    return false;\n  }\n  getDataUrl(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = event => {\n        resolve(event.target.result);\n      };\n      reader.onerror = event => {\n        reject(event.target.error);\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n  onSelectOption(is_capture, input) {\n    this.capture = is_capture;\n    if (this.capture) {\n      this.acceptMode = 'image/*';\n    } else {\n      this.acceptMode = this.accept;\n    }\n    // Use native camera API for Capacitor platforms\n    if (Capacitor.isNativePlatform()) {\n      this.selectPhotoNative(is_capture);\n    } else {\n      setTimeout(() => {\n        input.click();\n      }, 300);\n    }\n  }\n  selectPhotoNative(useCamera = false) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Check camera permissions first\n        const permissions = yield Camera.checkPermissions();\n        if (permissions.photos === 'denied') {\n          const requestResult = yield Camera.requestPermissions();\n          if (requestResult.photos === 'denied') {\n            Swal.fire({\n              icon: 'error',\n              title: _this._translateService.instant('Permission Required'),\n              text: _this._translateService.instant('Camera permissions are required to upload photos')\n            });\n            return;\n          }\n        }\n        // Use Camera.getPhoto for single image selection\n        const photo = yield Camera.getPhoto({\n          quality: 90,\n          allowEditing: false,\n          resultType: CameraResultType.DataUrl,\n          source: useCamera ? CameraSource.Camera : CameraSource.Photos\n        });\n        if (photo.dataUrl) {\n          // Convert base64 to File object\n          const file = _this.base64ToFile(photo.dataUrl);\n          if (_this.checkType(file)) {\n            if (_this.checkSize(file)) {\n              if (_this.useCropperDialog) {\n                // Create a mock event for the cropper dialog\n                const mockEvent = _this.createMockFileEvent(file);\n                _this.openCropperDialog(mockEvent);\n              } else {\n                _this.croppedImage.emit({\n                  type: 'file',\n                  data: file,\n                  preview: photo.dataUrl\n                });\n              }\n            } else {\n              Swal.fire({\n                icon: 'error',\n                title: _this._translateService.instant('File size is too large!'),\n                text: _this._translateService.instant('Please upload a file smaller than {{size}}MB', {\n                  size: _this.maxFileSize / 1000\n                })\n              });\n            }\n          } else {\n            Swal.fire({\n              icon: 'error',\n              title: 'Invalid file type!',\n              text: _this._translateService.instant('Please upload a file with type {{type}}', {\n                type: _this.accept\n              })\n            });\n          }\n        }\n      } catch (error) {\n        console.error('Error selecting photo:', error);\n        if (error.message !== 'User cancelled photos app') {\n          Swal.fire({\n            icon: 'error',\n            title: _this._translateService.instant('Error'),\n            text: _this._translateService.instant('Failed to select photo. Please try again.')\n          });\n        }\n      }\n    })();\n  }\n  // Convert base64 to File object\n  base64ToFile(dataUrl, filename) {\n    const timestamp = new Date().getTime();\n    filename = filename || `image_${timestamp}`;\n    const arr = dataUrl.split(',');\n    const mime = arr[0].match(/:(.*?);/)[1];\n    const bstr = atob(arr[1]);\n    const n = bstr.length;\n    const u8arr = new Uint8Array(n);\n    for (let i = 0; i < n; i++) {\n      u8arr[i] = bstr.charCodeAt(i);\n    }\n    const extension = mime.split('/')[1];\n    const finalFilename = `${filename}.${extension}`;\n    return new File([u8arr], finalFilename, {\n      type: mime\n    });\n  }\n  // Create a mock file input event for the cropper dialog\n  createMockFileEvent(file) {\n    const mockInput = document.createElement('input');\n    mockInput.type = 'file';\n    // Create a FileList-like object\n    const fileList = {\n      0: file,\n      length: 1,\n      item: index => index === 0 ? file : null\n    };\n    // Set the files property\n    Object.defineProperty(mockInput, 'files', {\n      value: fileList,\n      writable: false\n    });\n    // Create and return the event\n    const event = new Event('change', {\n      bubbles: true\n    });\n    Object.defineProperty(event, 'target', {\n      value: mockInput,\n      writable: false\n    });\n    return event;\n  }\n  static #_ = this.ɵfac = function CropperDialogComponent_Factory(t) {\n    return new (t || CropperDialogComponent)(i0.ɵɵdirectiveInject(i1.LyDialog), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(i3.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CropperDialogComponent,\n    selectors: [[\"aui-cropper-with-dialog\"]],\n    inputs: {\n      overlayImageURL: \"overlayImageURL\",\n      cropper_config: \"cropper_config\",\n      dialog_config: \"dialog_config\",\n      disabled: \"disabled\",\n      capture: \"capture\",\n      accept: \"accept\",\n      useCropperDialog: \"useCropperDialog\",\n      maxFileSize: \"maxFileSize\"\n    },\n    outputs: {\n      croppedImage: \"croppedImage\"\n    },\n    decls: 6,\n    vars: 6,\n    consts: [[\"type\", \"file\", \"hidden\", \"\", 3, \"disabled\", \"change\"], [\"_fileInput\", \"\"], [1, \"btn-group\"], [\"ngbDropdown\", \"\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary round-10 pl-1 pr-1\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"ngbDropdown\", \"\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", \"id\", \"dropdownMenuButton\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\", \"hide-arrow\", \"round-10\", \"pl-1\", \"pr-1\", 3, \"disabled\"], [1, \"fa\", \"fa-upload\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownMenuButton\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [1, \"fa-regular\", \"fa-image\", \"mr-1\"], [1, \"fa-solid\", \"fa-camera\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"round-10\", \"pl-1\", \"pr-1\", 3, \"disabled\", \"click\"], [2, \"margin-right\", \"3px\"], [\"ngbDropdownToggle\", \"\", \"type\", \"button\", \"id\", \"dropdownMenuButtonWeb\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\", \"hide-arrow\", \"round-10\", \"pl-1\", \"pr-1\", 3, \"disabled\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownMenuButtonWeb\"], [1, \"fa-regular\", \"fa-camera-retro\", \"mr-1\"]],\n    template: function CropperDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"input\", 0, 1);\n        i0.ɵɵlistener(\"change\", function CropperDialogComponent_Template_input_change_0_listener($event) {\n          return ctx.onChange($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtemplate(3, CropperDialogComponent_div_3_Template, 14, 10, \"div\", 3);\n        i0.ɵɵtemplate(4, CropperDialogComponent_button_4_Template, 5, 4, \"button\", 4);\n        i0.ɵɵtemplate(5, CropperDialogComponent_div_5_Template, 14, 10, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"capture\", ctx.capture)(\"accept\", ctx.acceptMode);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.Capacitor.isNativePlatform());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.Capacitor.isNativePlatform() && !ctx.isAndroid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.Capacitor.isNativePlatform() && ctx.isAndroid);\n      }\n    },\n    dependencies: [i4.NgIf, i5.NgbDropdown, i5.NgbDropdownToggle, i5.NgbDropdownMenu, i5.NgbDropdownItem, i3.TranslatePipe],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "mappings": ";AAAA,SAKEA,YAAY,QAGP,eAAe;AAItB,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,mBAAmB;AAC1E,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;ICJ5BC,EAAA,CAAAC,cAAA,aAAsD;IASlDD,EAAA,CAAAE,MAAA,GAAiC;;IAAAF,EAAA,CAAAG,SAAA,WAA4B;IAC/DH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,aAA0D;IACrCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,iBAAA,CAAkB,KAAK,CAAC;IAAA,EAAC;IACnDZ,EAAA,CAAAG,SAAA,YAAwC;IACxCH,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,YAAqD;IAAlCD,EAAA,CAAAK,UAAA,mBAAAQ,0DAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAG,MAAA,CAAAF,iBAAA,CAAkB,IAAI,CAAC;IAAA,EAAC;IAClDZ,EAAA,CAAAG,SAAA,aAAuC;IAACH,EAAA,CAAAE,MAAA,IAC1C;;IAAAF,EAAA,CAAAI,YAAA,EAAI;;;;IAhBJJ,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAgB,UAAA,aAAAC,MAAA,CAAAC,QAAA,CAAqB;IAOrBlB,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,4BAAiC;IAK/BpB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,6BACF;IAE0CpB,EAAA,CAAAe,SAAA,GAC1C;IAD0Cf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,2BAC1C;;;;;;IAKJpB,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAK,UAAA,mBAAAgB,iEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAAtB,EAAA,CAAAU,aAAA;MAAA,MAAAa,GAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAASxB,EAAA,CAAAW,WAAA,CAAAY,GAAA,CAAAE,KAAA,EAAkB;IAAA,EAAC;IAE5BzB,EAAA,CAAAC,cAAA,eAAgC;IAACD,EAAA,CAAAE,MAAA,GAAgC;;IAAAF,EAAA,CAAAI,YAAA,EAAO;IACxEJ,EAAA,CAAAG,SAAA,WAA4B;IAC9BH,EAAA,CAAAI,YAAA,EAAS;;;;IAPPJ,EAAA,CAAAgB,UAAA,aAAAU,MAAA,CAAAR,QAAA,CAAqB;IAKYlB,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,2BAAgC;;;;;;IAGnEpB,EAAA,CAAAC,cAAA,aAAoE;IAShED,EAAA,CAAAE,MAAA,GAAiC;;IAAAF,EAAA,CAAAG,SAAA,WAA4B;IAC/DH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,cAA6D;IACxCD,EAAA,CAAAK,UAAA,mBAAAsB,yDAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAqB,IAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAAU,aAAA;MAAA,MAAAa,GAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAASxB,EAAA,CAAAW,WAAA,CAAAkB,MAAA,CAAAC,cAAA,CAAe,IAAI,EAAAP,GAAA,CAAa;IAAA,EAAC;IAC3DvB,EAAA,CAAAG,SAAA,YAAwC;IACxCH,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,YAAgE;IAA7CD,EAAA,CAAAK,UAAA,mBAAA0B,0DAAA;MAAA/B,EAAA,CAAAO,aAAA,CAAAqB,IAAA;MAAA,MAAAI,OAAA,GAAAhC,EAAA,CAAAU,aAAA;MAAA,MAAAa,GAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAASxB,EAAA,CAAAW,WAAA,CAAAqB,OAAA,CAAAF,cAAA,CAAe,MAAM,EAAAP,GAAA,CAAa;IAAA,EAAC;IAC7DvB,EAAA,CAAAG,SAAA,aAA+C;IAC/CH,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAI,YAAA,EAAI;;;;IAjBJJ,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAgB,UAAA,aAAAiB,MAAA,CAAAf,QAAA,CAAqB;IAOrBlB,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,4BAAiC;IAK/BpB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,6BACF;IAGEpB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoB,WAAA,2BACF;;;ADvCN,OAAM,MAAOc,sBAAsB;EAoBjCC,YACUC,OAAiB,EACjBC,GAAsB,EACvBC,QAAkB,EACjBC,iBAAmC;IAHnC,KAAAH,OAAO,GAAPA,OAAO;IACP,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAC,QAAQ,GAARA,QAAQ;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAvB3B,KAAA5C,SAAS,GAAGA,SAAS;IACrB,KAAA6C,SAAS,GAAG,IAAI,CAACF,QAAQ,CAACG,OAAO;IACjC,KAAAC,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAACK,GAAG;IAEzB,KAAAC,YAAY,GAAa,EAAE;IAIlB,KAAA1B,QAAQ,GAAY,KAAK;IACzB,KAAA2B,OAAO,GAAG,IAAI;IACd,KAAAC,MAAM,GAAW,SAAS;IAC1B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,WAAW,GAAW,KAAK,CAAC,CAAC;IAC5B,KAAAC,YAAY,GAAG,IAAIxD,YAAY,EAIrC;IACJ,KAAAyD,UAAU,GAAW,SAAS;EAM1B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACD,UAAU,GAAG,IAAI,CAACJ,MAAM;EAC/B;EAEAM,iBAAiBA,CAACC,KAAY;IAC5BC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,IAAIG,aAAa,GAAQ;MACvBC,IAAI,EAAE;QACJJ,KAAK;QACLK,MAAM,EAAE,IAAI,CAACC,cAAc;QAC3BC,eAAe,EAAE,IAAI,CAACA;OACvB;MACDC,KAAK,EAAE,CAAC,GAAG,EAAE,cAAc,CAAC;MAC5BC,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,aAAa;MACxBC,YAAY,EAAE;KACf;IACDT,aAAa,GAAG;MAAE,GAAGA,aAAa;MAAE,GAAG,IAAI,CAACA;IAAa,CAAE;IAC3D,IAAI,CAACU,OAAO,GAAG,IAAK;IACpB,IAAI,CAAC9B,OAAO,CACT+B,IAAI,CAACzE,aAAa,EAAE8D,aAAa,CAAC,CAClCY,WAAW,CAACC,SAAS,CAAEC,MAAwB,IAAI;MAClD,IAAIA,MAAM,EAAE;QACV,IAAI,CAACrB,YAAY,CAACsB,IAAI,CAAC;UACrBC,IAAI,EAAE,QAAQ;UACdf,IAAI,EAAEa,MAAM,CAACG,OAAO;UACpBC,OAAO,EAAEJ,MAAM,CAACG;SACjB,CAAC;QACF,IAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;;IAE3B,CAAC,CAAC;EACN;EAEAC,QAAQA,CAACvB,KAAY;IACnBC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,KAAK,CAAC;IAC9B,IAAIwB,IAAI,GAAIxB,KAAK,CAACyB,MAA2B,CAACC,KAAM,CAAC,CAAC,CAAC;IACvD,IAAIF,IAAI,EAAE;MACR,IAAI,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,EAAE;QACxB,IAAI,IAAI,CAACI,SAAS,CAACJ,IAAI,CAAC,EAAE;UACxB,IAAI,IAAI,CAAC9B,gBAAgB,EAAE;YACzB,IAAI,CAACK,iBAAiB,CAACC,KAAK,CAAC;WAC9B,MAAM;YACL,IAAI,CAAC6B,UAAU,CAACL,IAAI,CAAC,CAACM,IAAI,CAAEC,MAAW,IAAI;cACzC,IAAI,CAACnC,YAAY,CAACsB,IAAI,CAAC;gBACrBC,IAAI,EAAE,MAAM;gBACZf,IAAI,EAAEoB,IAAI;gBACVH,OAAO,EAAEU;eACV,CAAC;YACJ,CAAC,CAAC;;SAEL,MAAM;UACLrF,IAAI,CAACsF,IAAI,CAAC;YACRC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,IAAI,CAAChD,iBAAiB,CAACiD,OAAO,CAAC,yBAAyB,CAAC;YAChEC,IAAI,EAAE,IAAI,CAAClD,iBAAiB,CAACiD,OAAO,CAClC,8CAA8C,EAC9C;cAAEE,IAAI,EAAE,IAAI,CAAC1C,WAAW,GAAG;YAAI,CAAE;WAEpC,CAAC;;OAEL,MAAM;QACLjD,IAAI,CAACsF,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,oBAAoB;UAC3BE,IAAI,EAAE,IAAI,CAAClD,iBAAiB,CAACiD,OAAO,CAClC,yCAAyC,EACzC;YAAEhB,IAAI,EAAE,IAAI,CAAC1B;UAAM,CAAE;SAExB,CAAC;;;EAGR;EAEA;EACAmC,SAASA,CAACJ,IAAU;IAClBvB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsB,IAAI,CAACa,IAAI,EAAE,IAAI,CAAC1C,WAAW,GAAG,IAAI,CAAC;IAC7D,IAAI6B,IAAI,CAACa,IAAI,GAAG,IAAI,CAAC1C,WAAW,GAAG,IAAI,EAAE;MACvC,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEA;EACAgC,SAASA,CAACH,IAAU;IAClB,IAAIc,GAAG,GAAGd,IAAI,CAACL,IAAI;IACnBlB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoC,GAAG,CAAC;IAC7B,IAAInB,IAAI,GAAGmB,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B;IACA,IAAIC,KAAK,GAAG,YAAY;IACxB,IAAI/C,MAAM,GAAG,IAAI,CAACA,MAAM,CAACgD,OAAO,CAACD,KAAK,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC5DxC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiB,IAAI,EAAE1B,MAAM,CAAC;IACvC,IAAI,CAACF,YAAY,GAAGE,MAAM,CAAC8C,KAAK,CAAC,GAAG,CAAC;IACrC,IAAI,IAAI,CAAChD,YAAY,CAACmD,MAAM,IAAI,CAAC,IAAI,IAAI,CAACnD,YAAY,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAChE,OAAO,IAAI;KACZ,MAAM,IAAI,IAAI,CAACA,YAAY,CAACoD,QAAQ,CAACxB,IAAI,CAAC,EAAE;MAC3C,OAAO,IAAI;;IAEb,OAAO,KAAK;EACd;EAEAU,UAAUA,CAACL,IAAU;IACnB,OAAO,IAAIoB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIjD,KAAU,IAAI;QAC7B6C,OAAO,CAAC7C,KAAK,CAACyB,MAAM,CAACR,MAAM,CAAC;MAC9B,CAAC;MACD8B,MAAM,CAACG,OAAO,GAAIlD,KAAU,IAAI;QAC9B8C,MAAM,CAAC9C,KAAK,CAACyB,MAAM,CAAC0B,KAAK,CAAC;MAC5B,CAAC;MACDJ,MAAM,CAACK,aAAa,CAAC5B,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA/C,cAAcA,CAAC4E,UAAe,EAAEC,KAAU;IACxC,IAAI,CAAC9D,OAAO,GAAG6D,UAAU;IACzB,IAAI,IAAI,CAAC7D,OAAO,EAAE;MAChB,IAAI,CAACK,UAAU,GAAG,SAAS;KAC5B,MAAM;MACL,IAAI,CAACA,UAAU,GAAG,IAAI,CAACJ,MAAM;;IAG/B;IACA,IAAInD,SAAS,CAACiH,gBAAgB,EAAE,EAAE;MAChC,IAAI,CAAChG,iBAAiB,CAAC8F,UAAU,CAAC;KACnC,MAAM;MACLG,UAAU,CAAC,MAAK;QACdF,KAAK,CAAClF,KAAK,EAAE;MACf,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEMb,iBAAiBA,CAACkG,SAAA,GAAqB,KAAK;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChD,IAAI;QACF;QACA,MAAMC,WAAW,SAASrH,MAAM,CAACsH,gBAAgB,EAAE;QACnD,IAAID,WAAW,CAACE,MAAM,KAAK,QAAQ,EAAE;UACnC,MAAMC,aAAa,SAASxH,MAAM,CAACyH,kBAAkB,EAAE;UACvD,IAAID,aAAa,CAACD,MAAM,KAAK,QAAQ,EAAE;YACrCpH,IAAI,CAACsF,IAAI,CAAC;cACRC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAEwB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAAC,qBAAqB,CAAC;cAC5DC,IAAI,EAAEsB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAAC,kDAAkD;aACxF,CAAC;YACF;;;QAIJ;QACA,MAAM8B,KAAK,SAAS1H,MAAM,CAAC2H,QAAQ,CAAC;UAClCC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE7H,gBAAgB,CAAC8H,OAAO;UACpCC,MAAM,EAAEd,SAAS,GAAGhH,YAAY,CAACF,MAAM,GAAGE,YAAY,CAAC+H;SACxD,CAAC;QAEF,IAAIP,KAAK,CAACQ,OAAO,EAAE;UACjB;UACA,MAAMjD,IAAI,GAAGkC,KAAI,CAACgB,YAAY,CAACT,KAAK,CAACQ,OAAO,CAAC;UAE7C,IAAIf,KAAI,CAAC/B,SAAS,CAACH,IAAI,CAAC,EAAE;YACxB,IAAIkC,KAAI,CAAC9B,SAAS,CAACJ,IAAI,CAAC,EAAE;cACxB,IAAIkC,KAAI,CAAChE,gBAAgB,EAAE;gBACzB;gBACA,MAAMiF,SAAS,GAAGjB,KAAI,CAACkB,mBAAmB,CAACpD,IAAI,CAAC;gBAChDkC,KAAI,CAAC3D,iBAAiB,CAAC4E,SAAS,CAAC;eAClC,MAAM;gBACLjB,KAAI,CAAC9D,YAAY,CAACsB,IAAI,CAAC;kBACrBC,IAAI,EAAE,MAAM;kBACZf,IAAI,EAAEoB,IAAI;kBACVH,OAAO,EAAE4C,KAAK,CAACQ;iBAChB,CAAC;;aAEL,MAAM;cACL/H,IAAI,CAACsF,IAAI,CAAC;gBACRC,IAAI,EAAE,OAAO;gBACbC,KAAK,EAAEwB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAAC,yBAAyB,CAAC;gBAChEC,IAAI,EAAEsB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAClC,8CAA8C,EAC9C;kBAAEE,IAAI,EAAEqB,KAAI,CAAC/D,WAAW,GAAG;gBAAI,CAAE;eAEpC,CAAC;;WAEL,MAAM;YACLjD,IAAI,CAACsF,IAAI,CAAC;cACRC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,oBAAoB;cAC3BE,IAAI,EAAEsB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAClC,yCAAyC,EACzC;gBAAEhB,IAAI,EAAEuC,KAAI,CAACjE;cAAM,CAAE;aAExB,CAAC;;;OAGP,CAAC,OAAO0D,KAAK,EAAE;QACdlD,OAAO,CAACkD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAIA,KAAK,CAAC0B,OAAO,KAAK,2BAA2B,EAAE;UACjDnI,IAAI,CAACsF,IAAI,CAAC;YACRC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAEwB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAAC,OAAO,CAAC;YAC9CC,IAAI,EAAEsB,KAAI,CAACxE,iBAAiB,CAACiD,OAAO,CAAC,2CAA2C;WACjF,CAAC;;;IAEL;EACH;EAEA;EACAuC,YAAYA,CAACD,OAAe,EAAEK,QAAiB;IAC7C,MAAMC,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;IACtCH,QAAQ,GAAGA,QAAQ,IAAI,SAASC,SAAS,EAAE;IAE3C,MAAMG,GAAG,GAAGT,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;IAC9B,MAAM4C,IAAI,GAAGD,GAAG,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMC,IAAI,GAAGC,IAAI,CAACJ,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,MAAMK,CAAC,GAAGF,IAAI,CAAC3C,MAAM;IACrB,MAAM8C,KAAK,GAAG,IAAIC,UAAU,CAACF,CAAC,CAAC;IAE/B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1BF,KAAK,CAACE,CAAC,CAAC,GAAGL,IAAI,CAACM,UAAU,CAACD,CAAC,CAAC;;IAG/B,MAAME,SAAS,GAAGT,IAAI,CAAC5C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,MAAMsD,aAAa,GAAG,GAAGf,QAAQ,IAAIc,SAAS,EAAE;IAEhD,OAAO,IAAIE,IAAI,CAAC,CAACN,KAAK,CAAC,EAAEK,aAAa,EAAE;MAAE1E,IAAI,EAAEgE;IAAI,CAAE,CAAC;EACzD;EAEA;EACAP,mBAAmBA,CAACpD,IAAU;IAC5B,MAAMuE,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACjDF,SAAS,CAAC5E,IAAI,GAAG,MAAM;IAEvB;IACA,MAAM+E,QAAQ,GAAG;MACf,CAAC,EAAE1E,IAAI;MACPkB,MAAM,EAAE,CAAC;MACTyD,IAAI,EAAGC,KAAa,IAAKA,KAAK,KAAK,CAAC,GAAG5E,IAAI,GAAG;KAC/C;IAED;IACA6E,MAAM,CAACC,cAAc,CAACP,SAAS,EAAE,OAAO,EAAE;MACxCQ,KAAK,EAAEL,QAAQ;MACfM,QAAQ,EAAE;KACX,CAAC;IAEF;IACA,MAAMxG,KAAK,GAAG,IAAIyG,KAAK,CAAC,QAAQ,EAAE;MAAEC,OAAO,EAAE;IAAI,CAAE,CAAC;IACpDL,MAAM,CAACC,cAAc,CAACtG,KAAK,EAAE,QAAQ,EAAE;MACrCuG,KAAK,EAAER,SAAS;MAChBS,QAAQ,EAAE;KACX,CAAC;IAEF,OAAOxG,KAAK;EACd;EAAC,QAAA2G,CAAA;qBAxRU9H,sBAAsB,EAAAlC,EAAA,CAAAiK,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAnK,EAAA,CAAAiK,iBAAA,CAAAjK,EAAA,CAAAoK,iBAAA,GAAApK,EAAA,CAAAiK,iBAAA,CAAAI,EAAA,CAAAC,QAAA,GAAAtK,EAAA,CAAAiK,iBAAA,CAAAM,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBvI,sBAAsB;IAAAwI,SAAA;IAAAC,MAAA;MAAA/G,eAAA;MAAAD,cAAA;MAAAH,aAAA;MAAAtC,QAAA;MAAA2B,OAAA;MAAAC,MAAA;MAAAC,gBAAA;MAAAC,WAAA;IAAA;IAAA4H,OAAA;MAAA3H,YAAA;IAAA;IAAA4H,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzBnClL,EAAA,CAAAC,cAAA,kBAQE;QALAD,EAAA,CAAAK,UAAA,oBAAA+K,wDAAAC,MAAA;UAAA,OAAUF,GAAA,CAAAvG,QAAA,CAAAyG,MAAA,CAAgB;QAAA,EAAC;QAH7BrL,EAAA,CAAAI,YAAA,EAQE;QAEFJ,EAAA,CAAAC,cAAA,aAAuB;QAErBD,EAAA,CAAAsL,UAAA,IAAAC,qCAAA,mBAoBM;QAGNvL,EAAA,CAAAsL,UAAA,IAAAE,wCAAA,oBASS;QACTxL,EAAA,CAAAsL,UAAA,IAAAG,qCAAA,mBAqBM;QACRzL,EAAA,CAAAI,YAAA,EAAM;;;QA5DJJ,EAAA,CAAAgB,UAAA,aAAAmK,GAAA,CAAAjK,QAAA,CAAqB;QAFrBlB,EAAA,CAAA0L,WAAA,YAAAP,GAAA,CAAAtI,OAAA,CAAwB,WAAAsI,GAAA,CAAAjI,UAAA;QAONlD,EAAA,CAAAe,SAAA,GAAkC;QAAlCf,EAAA,CAAAgB,UAAA,SAAAmK,GAAA,CAAAxL,SAAA,CAAAiH,gBAAA,GAAkC;QAwBjD5G,EAAA,CAAAe,SAAA,GAAiD;QAAjDf,EAAA,CAAAgB,UAAA,UAAAmK,GAAA,CAAAxL,SAAA,CAAAiH,gBAAA,OAAAuE,GAAA,CAAA3I,SAAA,CAAiD;QASlCxC,EAAA,CAAAe,SAAA,GAAgD;QAAhDf,EAAA,CAAAgB,UAAA,UAAAmK,GAAA,CAAAxL,SAAA,CAAAiH,gBAAA,MAAAuE,GAAA,CAAA3I,SAAA,CAAgD", "names": ["EventEmitter", "CropperDialog", "Capacitor", "Camera", "CameraResultType", "CameraSource", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "CropperDialogComponent_div_3_Template_a_click_6_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "selectPhotoNative", "CropperDialogComponent_div_3_Template_a_click_10_listener", "ctx_r6", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "disabled", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "CropperDialogComponent_button_4_Template_button_click_0_listener", "_r8", "_r0", "ɵɵreference", "click", "ctx_r2", "CropperDialogComponent_div_5_Template_a_click_6_listener", "_r10", "ctx_r9", "onSelectOption", "CropperDialogComponent_div_5_Template_a_click_10_listener", "ctx_r11", "ctx_r3", "CropperDialogComponent", "constructor", "_dialog", "_cd", "platform", "_translateService", "isAndroid", "ANDROID", "isIOS", "IOS", "accept_types", "capture", "accept", "useCropperDialog", "maxFileSize", "croppedImage", "acceptMode", "ngOnInit", "openCropperDialog", "event", "console", "log", "dialog_config", "data", "config", "cropper_config", "overlayImageURL", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "disableClose", "cropped", "open", "afterClosed", "subscribe", "result", "emit", "type", "dataURL", "preview", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "file", "target", "files", "checkType", "checkSize", "getDataUrl", "then", "base64", "fire", "icon", "title", "instant", "text", "size", "ext", "split", "regex", "replace", "length", "includes", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "onerror", "error", "readAsDataURL", "is_capture", "input", "isNativePlatform", "setTimeout", "useCamera", "_this", "_asyncToGenerator", "permissions", "checkPermissions", "photos", "requestResult", "requestPermissions", "photo", "getPhoto", "quality", "allowEditing", "resultType", "DataUrl", "source", "Photos", "dataUrl", "base64ToFile", "mockEvent", "createMockFileEvent", "message", "filename", "timestamp", "Date", "getTime", "arr", "mime", "match", "bstr", "atob", "n", "u8arr", "Uint8Array", "i", "charCodeAt", "extension", "finalFilename", "File", "mockInput", "document", "createElement", "fileList", "item", "index", "Object", "defineProperty", "value", "writable", "Event", "bubbles", "_", "ɵɵdirectiveInject", "i1", "LyDialog", "ChangeDetectorRef", "i2", "Platform", "i3", "TranslateService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "CropperDialogComponent_Template", "rf", "ctx", "CropperDialogComponent_Template_input_change_0_listener", "$event", "ɵɵtemplate", "CropperDialogComponent_div_3_Template", "CropperDialogComponent_button_4_Template", "CropperDialogComponent_div_5_Template", "ɵɵattribute"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\cropper-dialog\\cropper-dialog.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\cropper-dialog\\cropper-dialog.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  ChangeDetectionStrategy,\r\n  ChangeDetectorRef,\r\n  Input,\r\n  EventEmitter,\r\n  Output,\r\n  OnInit,\r\n} from '@angular/core';\r\nimport { LyDialog } from '@alyle/ui/dialog';\r\nimport { ImgCropperConfig, ImgCropperEvent } from '@alyle/ui/image-cropper';\r\n\r\nimport { CropperDialog } from './cropper-dialog';\r\nimport { LyDialogConfig } from '@alyle/ui/dialog/dialog-config';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { Camera, CameraResultType, CameraSource } from '@capacitor/camera';\r\nimport Swal from 'sweetalert2';\r\nimport { Platform } from '@angular/cdk/platform';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\n@Component({\r\n  selector: 'aui-cropper-with-dialog',\r\n  templateUrl: './cropper-dialog.component.html',\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class CropperDialogComponent implements OnInit {\r\n  Capacitor = Capacitor;\r\n  isAndroid = this.platform.ANDROID;\r\n  isIOS = this.platform.IOS;\r\n  cropped?: string;\r\n  accept_types: string[] = [];\r\n  @Input() overlayImageURL: string;\r\n  @Input() cropper_config: ImgCropperConfig;\r\n  @Input() dialog_config: LyDialogConfig;\r\n  @Input() disabled: boolean = false;\r\n  @Input() capture = null;\r\n  @Input() accept: string = 'image/*';\r\n  @Input() useCropperDialog: boolean = true;\r\n  @Input() maxFileSize: number = 25000; //KB\r\n  @Output() croppedImage = new EventEmitter<{\r\n    type: string;\r\n    data: any;\r\n    preview: any;\r\n  }>();\r\n  acceptMode: string = 'image/*';\r\n  constructor(\r\n    private _dialog: LyDialog,\r\n    private _cd: ChangeDetectorRef,\r\n    public platform: Platform,\r\n    private _translateService: TranslateService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.acceptMode = this.accept;\r\n  }\r\n\r\n  openCropperDialog(event: Event) {\r\n    console.log(event);\r\n    let dialog_config: any = {\r\n      data: {\r\n        event,\r\n        config: this.cropper_config,\r\n        overlayImageURL: this.overlayImageURL,\r\n      },\r\n      width: [420, '100vw@XSmall'],\r\n      height: '100vh@XSmall',\r\n      maxWidth: '100vw@XSmall',\r\n      maxHeight: '90vh@XSmall',\r\n      disableClose: true,\r\n    };\r\n    dialog_config = { ...dialog_config, ...this.dialog_config };\r\n    this.cropped = null!;\r\n    this._dialog\r\n      .open(CropperDialog, dialog_config)\r\n      .afterClosed.subscribe((result?: ImgCropperEvent) => {\r\n        if (result) {\r\n          this.croppedImage.emit({\r\n            type: 'base64',\r\n            data: result.dataURL,\r\n            preview: result.dataURL,\r\n          });\r\n          this._cd.markForCheck();\r\n        }\r\n      });\r\n  }\r\n\r\n  onChange(event: Event) {\r\n    console.log('onchange', event);\r\n    let file = (event.target as HTMLInputElement).files![0];\r\n    if (file) {\r\n      if (this.checkType(file)) {\r\n        if (this.checkSize(file)) {\r\n          if (this.useCropperDialog) {\r\n            this.openCropperDialog(event);\r\n          } else {\r\n            this.getDataUrl(file).then((base64: any) => {\r\n              this.croppedImage.emit({\r\n                type: 'file',\r\n                data: file,\r\n                preview: base64,\r\n              });\r\n            });\r\n          }\r\n        } else {\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: this._translateService.instant('File size is too large!'),\r\n            text: this._translateService.instant(\r\n              'Please upload a file smaller than {{size}}MB',\r\n              { size: this.maxFileSize / 1000 }\r\n            ),\r\n          });\r\n        }\r\n      } else {\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Invalid file type!',\r\n          text: this._translateService.instant(\r\n            'Please upload a file with type {{type}}',\r\n            { type: this.accept }\r\n          ),\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  // check size of file\r\n  checkSize(file: File) {\r\n    console.log('check file', file.size, this.maxFileSize * 1024);\r\n    if (file.size > this.maxFileSize * 1024) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  // check type of file\r\n  checkType(file: File) {\r\n    let ext = file.type;\r\n    console.log('check ext', ext);\r\n    let type = ext.split('/')[1];\r\n    // replace regex image/ , video/ , audio/ , */ to empty string\r\n    let regex = /[^\\/,]+\\//g;\r\n    let accept = this.accept.replace(regex, '').replace(' ', '');\r\n    console.log('check type', type, accept);\r\n    this.accept_types = accept.split(',');\r\n    if (this.accept_types.length == 1 && this.accept_types[0] == '*') {\r\n      return true;\r\n    } else if (this.accept_types.includes(type)) {\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  getDataUrl(file: File) {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.onload = (event: any) => {\r\n        resolve(event.target.result);\r\n      };\r\n      reader.onerror = (event: any) => {\r\n        reject(event.target.error);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    });\r\n  }\r\n\r\n  onSelectOption(is_capture: any, input: any) {\r\n    this.capture = is_capture;\r\n    if (this.capture) {\r\n      this.acceptMode = 'image/*';\r\n    } else {\r\n      this.acceptMode = this.accept;\r\n    }\r\n\r\n    // Use native camera API for Capacitor platforms\r\n    if (Capacitor.isNativePlatform()) {\r\n      this.selectPhotoNative(is_capture);\r\n    } else {\r\n      setTimeout(() => {\r\n        input.click();\r\n      }, 300);\r\n    }\r\n  }\r\n\r\n  async selectPhotoNative(useCamera: boolean = false) {\r\n    try {\r\n      // Check camera permissions first\r\n      const permissions = await Camera.checkPermissions();\r\n      if (permissions.photos === 'denied') {\r\n        const requestResult = await Camera.requestPermissions();\r\n        if (requestResult.photos === 'denied') {\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: this._translateService.instant('Permission Required'),\r\n            text: this._translateService.instant('Camera permissions are required to upload photos'),\r\n          });\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Use Camera.getPhoto for single image selection\r\n      const photo = await Camera.getPhoto({\r\n        quality: 90,\r\n        allowEditing: false,\r\n        resultType: CameraResultType.DataUrl,\r\n        source: useCamera ? CameraSource.Camera : CameraSource.Photos\r\n      });\r\n\r\n      if (photo.dataUrl) {\r\n        // Convert base64 to File object\r\n        const file = this.base64ToFile(photo.dataUrl);\r\n\r\n        if (this.checkType(file)) {\r\n          if (this.checkSize(file)) {\r\n            if (this.useCropperDialog) {\r\n              // Create a mock event for the cropper dialog\r\n              const mockEvent = this.createMockFileEvent(file);\r\n              this.openCropperDialog(mockEvent);\r\n            } else {\r\n              this.croppedImage.emit({\r\n                type: 'file',\r\n                data: file,\r\n                preview: photo.dataUrl,\r\n              });\r\n            }\r\n          } else {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: this._translateService.instant('File size is too large!'),\r\n              text: this._translateService.instant(\r\n                'Please upload a file smaller than {{size}}MB',\r\n                { size: this.maxFileSize / 1000 }\r\n              ),\r\n            });\r\n          }\r\n        } else {\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Invalid file type!',\r\n            text: this._translateService.instant(\r\n              'Please upload a file with type {{type}}',\r\n              { type: this.accept }\r\n            ),\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error selecting photo:', error);\r\n      if (error.message !== 'User cancelled photos app') {\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: this._translateService.instant('Error'),\r\n          text: this._translateService.instant('Failed to select photo. Please try again.'),\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  // Convert base64 to File object\r\n  base64ToFile(dataUrl: string, filename?: string): File {\r\n    const timestamp = new Date().getTime();\r\n    filename = filename || `image_${timestamp}`;\r\n\r\n    const arr = dataUrl.split(',');\r\n    const mime = arr[0].match(/:(.*?);/)[1];\r\n    const bstr = atob(arr[1]);\r\n    const n = bstr.length;\r\n    const u8arr = new Uint8Array(n);\r\n\r\n    for (let i = 0; i < n; i++) {\r\n      u8arr[i] = bstr.charCodeAt(i);\r\n    }\r\n\r\n    const extension = mime.split('/')[1];\r\n    const finalFilename = `${filename}.${extension}`;\r\n\r\n    return new File([u8arr], finalFilename, { type: mime });\r\n  }\r\n\r\n  // Create a mock file input event for the cropper dialog\r\n  createMockFileEvent(file: File): Event {\r\n    const mockInput = document.createElement('input');\r\n    mockInput.type = 'file';\r\n\r\n    // Create a FileList-like object\r\n    const fileList = {\r\n      0: file,\r\n      length: 1,\r\n      item: (index: number) => index === 0 ? file : null\r\n    };\r\n\r\n    // Set the files property\r\n    Object.defineProperty(mockInput, 'files', {\r\n      value: fileList,\r\n      writable: false\r\n    });\r\n\r\n    // Create and return the event\r\n    const event = new Event('change', { bubbles: true });\r\n    Object.defineProperty(event, 'target', {\r\n      value: mockInput,\r\n      writable: false\r\n    });\r\n\r\n    return event;\r\n  }\r\n}\r\n", "<input\r\n  #_fileInput\r\n  type=\"file\"\r\n  (change)=\"onChange($event)\"\r\n  hidden\r\n  [attr.capture]=\"capture\"\r\n  [attr.accept]=\"acceptMode\"\r\n  [disabled]=\"disabled\"\r\n/>\r\n\r\n<div class=\"btn-group\">\r\n  <!-- Native platform buttons -->\r\n  <div ngbDropdown *ngIf=\"Capacitor.isNativePlatform()\">\r\n    <button\r\n      [disabled]=\"disabled\"\r\n      ngbDropdownToggle\r\n      class=\"btn btn-primary hide-arrow round-10 pl-1 pr-1\"\r\n      type=\"button\"\r\n      id=\"dropdownMenuButton\"\r\n      rippleEffect\r\n    >\r\n      {{ 'Upload image' | translate }} <i class=\"fa fa-upload\"></i>\r\n    </button>\r\n    <div ngbDropdownMenu aria-labelledby=\"dropdownMenuButton\">\r\n      <a ngbDropdownItem (click)=\"selectPhotoNative(false)\">\r\n        <i class=\"fa-regular fa-image mr-1\"></i>\r\n        {{ 'Photo Library' | translate }}\r\n      </a>\r\n      <a ngbDropdownItem (click)=\"selectPhotoNative(true)\">\r\n        <i class=\"fa-solid fa-camera mr-1\"></i> {{ 'Take Photo' | translate }}\r\n      </a>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Web platform buttons -->\r\n  <button\r\n    *ngIf=\"!Capacitor.isNativePlatform() && !isAndroid\"\r\n    [disabled]=\"disabled\"\r\n    type=\"button\"\r\n    class=\"btn btn-primary round-10 pl-1 pr-1\"\r\n    (click)=\"_fileInput.click()\"\r\n  >\r\n    <span style=\"margin-right: 3px\"> {{ 'Upload image' | translate }}</span>\r\n    <i class=\"fa fa-upload\"></i>\r\n  </button>\r\n  <div ngbDropdown *ngIf=\"!Capacitor.isNativePlatform() && isAndroid\">\r\n    <button\r\n      [disabled]=\"disabled\"\r\n      ngbDropdownToggle\r\n      class=\"btn btn-primary hide-arrow round-10 pl-1 pr-1\"\r\n      type=\"button\"\r\n      id=\"dropdownMenuButtonWeb\"\r\n      rippleEffect\r\n    >\r\n      {{ 'Upload image' | translate }} <i class=\"fa fa-upload\"></i>\r\n    </button>\r\n    <div ngbDropdownMenu aria-labelledby=\"dropdownMenuButtonWeb\">\r\n      <a ngbDropdownItem (click)=\"onSelectOption(null, _fileInput)\">\r\n        <i class=\"fa-regular fa-image mr-1\"></i>\r\n        {{ 'Photo Library' | translate }}\r\n      </a>\r\n      <a ngbDropdownItem (click)=\"onSelectOption('true', _fileInput)\">\r\n        <i class=\"fa-regular fa-camera-retro mr-1\"></i>\r\n        {{ 'Take Photo' | translate }}\r\n      </a>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
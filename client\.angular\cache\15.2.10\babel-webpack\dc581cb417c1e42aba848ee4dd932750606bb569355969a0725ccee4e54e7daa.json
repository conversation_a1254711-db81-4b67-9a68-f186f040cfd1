{"ast": null, "code": "import { environment } from 'environments/environment';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SponsorService {\n  constructor(http) {\n    this.http = http;\n  }\n  // POST: api/sponsors/all\n  getAllSponsors() {\n    return this.http.post(`${environment.apiUrl}/sponsors/all`, {}).pipe(map(res => {\n      return this.decodeHtmlEntities(res);\n    }));\n  }\n  updateSponsorOrder(sponsors) {\n    const order = sponsors.map((sponsor, index) => {\n      return {\n        id: sponsor.id,\n        order: index + 1\n      };\n    });\n    return this.http.post(`${environment.apiUrl}/sponsors/order`, order);\n  }\n  // GET: api/sponsors\n  getSponsors() {\n    return this.http.get(`${environment.apiUrl}/sponsors`).pipe(map(res => {\n      return res;\n    }));\n  }\n  // GET: api/sponsors/{sponsor_id}\n  getSponsorById(sponsorId) {\n    return this.http.get(`${environment.apiUrl}/sponsors/${sponsorId}`);\n  }\n  // POST: api/sponsors/create\n  createSponsor(sponsor) {\n    return this.http.post(`${environment.apiUrl}/sponsors/create`, sponsor);\n  }\n  // POST: api/sponsors/update\n  updateSponsor(sponsor) {\n    return this.http.post(`${environment.apiUrl}/sponsors/update`, sponsor);\n  }\n  // DELETE: api/sponsors/{sponsor_id}\n  deleteSponsor(sponsorId) {\n    return this.http.delete(`${environment.apiUrl}/sponsors/${sponsorId}`);\n  }\n  decodeHtmlEntities(data) {\n    if (typeof data === 'string') {\n      const txt = document.createElement('textarea');\n      txt.innerHTML = data;\n      return txt.value;\n    } else if (Array.isArray(data)) {\n      return data.map(item => this.decodeHtmlEntities(item));\n    } else if (typeof data === 'object' && data !== null) {\n      const decodedData = {};\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          decodedData[key] = this.decodeHtmlEntities(data[key]);\n        }\n      }\n      return decodedData;\n    }\n    return data;\n  }\n  static #_ = this.ɵfac = function SponsorService_Factory(t) {\n    return new (t || SponsorService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SponsorService,\n    factory: SponsorService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;AAGtD,SAASC,GAAG,QAAQ,gBAAgB;;;AAMpC,OAAM,MAAOC,cAAc;EAGzBC,YAAmBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAQtC;EACAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACD,IAAI,CAACE,IAAI,CAAM,GAAGN,WAAW,CAACO,MAAM,eAAe,EAAE,EAAE,CAAC,CAACC,IAAI,CACvEP,GAAG,CAAEQ,GAAQ,IAAI;MACf,OAAO,IAAI,CAACC,kBAAkB,CAACD,GAAG,CAAC;IACrC,CAAC,CAAC,CAEH;EACH;EAEAE,kBAAkBA,CAACC,QAAmB;IACpC,MAAMC,KAAK,GAAGD,QAAQ,CAACX,GAAG,CAAC,CAACa,OAAO,EAAEC,KAAK,KAAI;MAC5C,OAAO;QAAEC,EAAE,EAAEF,OAAO,CAACE,EAAE;QAAEH,KAAK,EAAEE,KAAK,GAAG;MAAC,CAAE;IAC7C,CAAC,CAAC;IACF,OAAO,IAAI,CAACX,IAAI,CAACE,IAAI,CAAY,GAAGN,WAAW,CAACO,MAAM,iBAAiB,EAAEM,KAAK,CAAC;EACjF;EAIA;EACAI,WAAWA,CAAA;IACT,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAY,GAAGlB,WAAW,CAACO,MAAM,WAAW,CAAC,CAACC,IAAI,CACpEP,GAAG,CAAEQ,GAAQ,IAAI;MAEf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEA;EACAU,cAAcA,CAACC,SAAiB;IAC9B,OAAO,IAAI,CAAChB,IAAI,CAACc,GAAG,CAAU,GAAGlB,WAAW,CAACO,MAAM,aAAaa,SAAS,EAAE,CAAC;EAC9E;EAEA;EACAC,aAAaA,CAACP,OAAgB;IAC5B,OAAO,IAAI,CAACV,IAAI,CAACE,IAAI,CAAU,GAAGN,WAAW,CAACO,MAAM,kBAAkB,EAAEO,OAAO,CAAC;EAClF;EAEA;EACAQ,aAAaA,CAACR,OAAgB;IAC5B,OAAO,IAAI,CAACV,IAAI,CAACE,IAAI,CAAU,GAAGN,WAAW,CAACO,MAAM,kBAAkB,EAAEO,OAAO,CAAC;EAClF;EAEA;EACAS,aAAaA,CAACH,SAAiB;IAC7B,OAAO,IAAI,CAAChB,IAAI,CAACoB,MAAM,CAAO,GAAGxB,WAAW,CAACO,MAAM,aAAaa,SAAS,EAAE,CAAC;EAC9E;EAEAV,kBAAkBA,CAACe,IAAS;IAC1B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MAC9CF,GAAG,CAACG,SAAS,GAAGJ,IAAI;MACpB,OAAOC,GAAG,CAACI,KAAK;KACnB,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACP,IAAI,CAAC,EAAE;MAC5B,OAAOA,IAAI,CAACxB,GAAG,CAACgC,IAAI,IAAI,IAAI,CAACvB,kBAAkB,CAACuB,IAAI,CAAC,CAAC;KACzD,MAAM,IAAI,OAAOR,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MAClD,MAAMS,WAAW,GAAG,EAAE;MACtB,KAAK,MAAMC,GAAG,IAAIV,IAAI,EAAE;QACpB,IAAIA,IAAI,CAACW,cAAc,CAACD,GAAG,CAAC,EAAE;UAC1BD,WAAW,CAACC,GAAG,CAAC,GAAG,IAAI,CAACzB,kBAAkB,CAACe,IAAI,CAACU,GAAG,CAAC,CAAC;;;MAG7D,OAAOD,WAAW;;IAEtB,OAAOT,IAAI;EACb;EAAC,QAAAY,CAAA;qBA7EUnC,cAAc,EAAAoC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAdxC,cAAc;IAAAyC,OAAA,EAAdzC,cAAc,CAAA0C,IAAA;IAAAC,UAAA,EAFb;EAAM", "names": ["environment", "map", "SponsorService", "constructor", "http", "getAllSponsors", "post", "apiUrl", "pipe", "res", "decodeHtmlEntities", "updateSponsorOrder", "sponsors", "order", "sponsor", "index", "id", "getSponsors", "get", "getSponsorById", "sponsorId", "createSponsor", "updateSponsor", "deleteSponsor", "delete", "data", "txt", "document", "createElement", "innerHTML", "value", "Array", "isArray", "item", "decodedData", "key", "hasOwnProperty", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\sponsor.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\r\nimport { Sponsor } from 'app/interfaces/sponsor';\r\nimport { map } from 'rxjs/operators';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SponsorService {\r\n\r\n\r\n  constructor(public http: HttpClient) {}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // POST: api/sponsors/all\r\n  getAllSponsors(): Observable<any> {\r\n    return this.http.post<any>(`${environment.apiUrl}/sponsors/all`, {}).pipe(\r\n      map((res: any) => {\r\n        return this.decodeHtmlEntities(res);\r\n      })\r\n\r\n    )\r\n  }\r\n\r\n  updateSponsorOrder(sponsors: Sponsor[]) {\r\n    const order = sponsors.map((sponsor, index) => {\r\n      return { id: sponsor.id, order: index + 1 };\r\n    })\r\n    return this.http.post<Sponsor[]>(`${environment.apiUrl}/sponsors/order`, order);\r\n  }\r\n\r\n\r\n\r\n  // GET: api/sponsors\r\n  getSponsors(): Observable<Sponsor[]> {\r\n    return this.http.get<Sponsor[]>(`${environment.apiUrl}/sponsors`).pipe(\r\n      map((res: any) => {\r\n        \r\n        return res;\r\n      })\r\n    )\r\n  }\r\n\r\n  // GET: api/sponsors/{sponsor_id}\r\n  getSponsorById(sponsorId: string): Observable<Sponsor> {\r\n    return this.http.get<Sponsor>(`${environment.apiUrl}/sponsors/${sponsorId}`);\r\n  }\r\n\r\n  // POST: api/sponsors/create\r\n  createSponsor(sponsor: Sponsor): Observable<Sponsor> {\r\n    return this.http.post<Sponsor>(`${environment.apiUrl}/sponsors/create`, sponsor);\r\n  }\r\n\r\n  // POST: api/sponsors/update\r\n  updateSponsor(sponsor: Sponsor): Observable<Sponsor> {\r\n    return this.http.post<Sponsor>(`${environment.apiUrl}/sponsors/update`, sponsor);\r\n  }\r\n\r\n  // DELETE: api/sponsors/{sponsor_id}\r\n  deleteSponsor(sponsorId: string): Observable<void> {\r\n    return this.http.delete<void>(`${environment.apiUrl}/sponsors/${sponsorId}`);\r\n  }\r\n\r\n  decodeHtmlEntities(data: any): any {\r\n    if (typeof data === 'string') {\r\n        const txt = document.createElement('textarea');\r\n        txt.innerHTML = data;\r\n        return txt.value;\r\n    } else if (Array.isArray(data)) {\r\n        return data.map(item => this.decodeHtmlEntities(item));\r\n    } else if (typeof data === 'object' && data !== null) {\r\n        const decodedData = {};\r\n        for (const key in data) {\r\n            if (data.hasOwnProperty(key)) {\r\n                decodedData[key] = this.decodeHtmlEntities(data[key]);\r\n            }\r\n        }\r\n        return decodedData;\r\n    }\r\n    return data;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export const isArray = (() => Array.isArray || (x => x && typeof x.length === 'number'))();", "map": {"version": 3, "names": ["isArray", "Array", "x", "length"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isArray.js"], "sourcesContent": ["export const isArray = (() => Array.isArray || ((x) => x && typeof x.length === 'number'))();\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,CAAC,MAAMC,KAAK,CAACD,OAAO,KAAME,CAAC,IAAKA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,EAAE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
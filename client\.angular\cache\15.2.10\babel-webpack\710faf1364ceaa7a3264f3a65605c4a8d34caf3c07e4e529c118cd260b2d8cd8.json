{"ast": null, "code": "import { environment } from '../../../../environments/environment';\nimport { DataTableDirective } from 'angular-datatables';\nimport { ManageClubComponent } from './manage-club/manage-club.component';\nimport { REGEX } from 'app/helpers/regex';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"../../../services/commons.service\";\nimport * as i5 from \"app/services/club.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"@angular/platform-browser\";\nimport * as i8 from \"app/services/s3.service\";\nimport * as i9 from \"../../../components/editor-sidebar/editor-sidebar.component\";\nimport * as i10 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i11 from \"angular-datatables\";\nimport * as i12 from \"app/layout/components/content-header/content-header.component\";\nexport class ClubsComponent {\n  constructor(_http, _coreSidebarService, _translateService, _commonsService, renderer, _clubService, _modalService, _titleService, _s3Service) {\n    this._http = _http;\n    this._coreSidebarService = _coreSidebarService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this.renderer = renderer;\n    this._clubService = _clubService;\n    this._modalService = _modalService;\n    this._titleService = _titleService;\n    this._s3Service = _s3Service;\n    this.dtElement = DataTableDirective;\n    this.paramsToPost = {};\n    this.table_name = 'clubs-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: 'Create new club',\n        edit: 'Edit club',\n        remove: 'Delete club'\n      },\n      url: `${environment.apiUrl}/clubs/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'name',\n      type: 'input',\n      props: {\n        required: true,\n        label: this._translateService.instant('Name'),\n        placeholder: this._translateService.instant('Enter name of club')\n      }\n    }, {\n      key: 'code',\n      type: 'input',\n      props: {\n        required: true,\n        label: this._translateService.instant('Code'),\n        placeholder: this._translateService.instant('Enter code of club')\n      }\n    }, {\n      key: 'logo',\n      type: 'image-cropper',\n      props: {\n        // upload_url: `${environment.apiUrl}/files/editor`,\n        upload_url: `${environment.apiUrl}/s3`,\n        dir: 'clubs',\n        label: this._translateService.instant('Logo'),\n        accept: 'image/png, image/jpg, image/jpeg',\n        note: this._translateService.instant('Note: Image ratio must be 1:1'),\n        config: {\n          width: 200,\n          height: 200,\n          resizableArea: false,\n          fillColor: 'transparent',\n          output: {\n            width: 256,\n            height: 256\n          }\n        }\n      },\n      defaultValue: ''\n    }, {\n      key: 'color_code',\n      type: 'input',\n      props: {\n        label: 'Background Color',\n        type: 'color',\n        attributes: {\n          style: 'margin: initial; border: 1px solid #2d67f1; border-radius: 50%; width: 40px; height: 40px;'\n        }\n      },\n      defaultValue: ''\n    }];\n    this.dtOptions = {};\n    this._titleService.setTitle('Table Clubs');\n  }\n  ngOnInit() {\n    console.log('ClubsComponent OnInit');\n    this.contentHeader = {\n      headerTitle: 'Clubs',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Settings',\n          isLink: false\n        }, {\n          name: 'Clubs',\n          isLink: false\n        }]\n      }\n    };\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        this._http.post(`${environment.apiUrl}/clubs/all`, dataTablesParameters).subscribe(resp => {\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }, {\n        responsivePriority: 1,\n        targets: -2\n      }, {\n        responsivePriority: 2,\n        targets: -3\n      }],\n      orderBy: [[1, 'asc']],\n      displayLength: -1,\n      columns: [{\n        title: this._translateService.instant('Logo'),\n        data: 'logo',\n        className: 'p-1',\n        render: function (data, type, row) {\n          let img = document.createElement('img');\n          img.src = data;\n          img.id = `img-${row.id}`;\n          img.className = 'avatar avatar-lg';\n          img.style.maxWidth = '50px';\n          img.style.backgroundColor = '#fff';\n          img.style.objectFit = 'cover';\n          if (data == null) {\n            img.src = 'assets/images/logo/ezactive_1024x1024.png';\n          }\n          // check get image error\n          img.onerror = function () {\n            // img.src = 'assets/images/logo/ezactive_1024x1024.png';\n            // set src by row id\n            $(`#img-${row.id}`).attr('src', img.src);\n          };\n          return img.outerHTML;\n        }\n      }, {\n        title: this._translateService.instant('Name'),\n        data: 'name',\n        className: 'font-weight-bolder p-1'\n      }, {\n        title: this._translateService.instant('Code'),\n        data: 'code',\n        className: 'p-1'\n      }, {\n        title: this._translateService.instant('Color'),\n        data: 'color_code',\n        className: 'text-center p-1',\n        render: function (data, type, row) {\n          if (data) {\n            return `<div style=\"width: 35px; height: 35px; background-color: ${data}; border: 1px solid #ccc; border-radius: 50%; display: inline-block;\"></div>`;\n          } else {\n            return '<span class=\"text-muted\">N/A</span>';\n          }\n        }\n      }, {\n        title: this._translateService.instant('Active'),\n        data: 'is_active',\n        className: 'text-center p-0',\n        render: (data, type, row) => {\n          let checked = '';\n          if (data) {\n            checked = 'checked';\n          }\n          return `<div class=\"custom-control custom-switch custom-control-inline\">\n                    <input ${checked} type=\"checkbox\" class=\"custom-control-input\" club_id=\"${row.id}\" id=\"switch_${row.id}\" />\n                    <label class=\"custom-control-label\" for=\"switch_${row.id}\"></label>\n                  </div>`;\n        }\n      }, {\n        title: this._translateService.instant('Action'),\n        data: 'id',\n        className: 'text-center p-1',\n        render: (data, type, row) => {\n          return `<button class=\"btn btn-icon btn-outline-primary\" btn_manage=\"${data}\">\n            ${this._translateService.instant('Assign Manager')}\n            </button>`;\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"feather icon-plus\"></i> ' + this._translateService.instant('Add'),\n          action: () => this.editor('create')\n        }, {\n          text: '<i class=\"feather icon-edit\"></i> ' + this._translateService.instant('Edit'),\n          action: () => this.editor('edit'),\n          extend: 'selected'\n        }, {\n          text: '<i class=\"feather icon-trash\"></i> ' + this._translateService.instant('Delete'),\n          action: () => this.editor('remove'),\n          extend: 'selected'\n        }]\n      }\n    };\n  }\n  editor(action) {\n    this.params.action = action;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  formChange($event) {\n    this.form = $event;\n  }\n  onSubmitted() {\n    switch ('create') {\n      case 'create':\n        // if form is not empty\n        if (this.form) {\n          // this.paramsToPost = {\n          //   \"data[0][photo]\": \"hehe\",\n          // };\n        }\n        break;\n    }\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('club_id')) {\n        console.log(event.target.getAttribute('club_id'));\n        this._clubService.toggleIsActive(event.target.getAttribute('club_id')).toPromise();\n      }\n      if (event.target.hasAttribute('btn_manage')) {\n        console.log(event.target.getAttribute('btn_manage'));\n        let club_id = event.target.getAttribute('btn_manage');\n        let modalRef = this._modalService.open(ManageClubComponent, {\n          size: 'lg',\n          centered: true,\n          backdrop: 'static'\n        });\n        modalRef.componentInstance.club_id = club_id;\n      }\n    });\n  }\n  ngOnDestroy() {\n    console.log('ClubsComponent destroy');\n    this.unlistener();\n  }\n  onClose(event) {\n    console.log('onClose', event);\n    if (!event.isSubmitted && event.action !== 'remove') {\n      console.log(!REGEX.URL.test(event.formValue.logo), event.formValue.logo !== '');\n      if (!REGEX.URL.test(event.formValue.logo) && event.formValue.logo !== '') {\n        this._s3Service.removeImage(event.formValue.logo).subscribe(res => {\n          console.log(res);\n        });\n      }\n    }\n  }\n  static #_ = this.ɵfac = function ClubsComponent_Factory(t) {\n    return new (t || ClubsComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CoreSidebarService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.ClubService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i8.S3Service));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClubsComponent,\n    selectors: [[\"app-clubs\"]],\n    viewQuery: function ClubsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 8,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"card\"], [1, \"card-header\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"paramsToPost\", \"onSubmit\", \"formChange\", \"onClose\"]],\n    template: function ClubsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"div\", 4)(5, \"table\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"core-sidebar\", 6)(7, \"app-editor-sidebar\", 7);\n        i0.ɵɵlistener(\"formChange\", function ClubsComponent_Template_app_editor_sidebar_formChange_7_listener($event) {\n          return ctx.formChange($event);\n        })(\"onClose\", function ClubsComponent_Template_app_editor_sidebar_onClose_7_listener($event) {\n          return ctx.onClose($event);\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params)(\"paramsToPost\", ctx.paramsToPost)(\"onSubmit\", ctx.onSubmitted);\n      }\n    },\n    dependencies: [i9.EditorSidebarComponent, i10.CoreSidebarComponent, i11.DataTableDirective, i12.ContentHeaderComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,WAAW,QAAQ,sCAAsC;AAUlE,SAASC,kBAAkB,QAAQ,oBAAoB;AAOvD,SAASC,mBAAmB,QAAQ,qCAAqC;AAGzE,SAASC,KAAK,QAAQ,mBAAmB;;;;;;;;;;;;;;AAQzC,OAAM,MAAOC,cAAc;EAgFzBC,YACUC,KAAiB,EAClBC,mBAAuC,EACvCC,iBAAmC,EACnCC,eAA+B,EAC/BC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,aAAoB,EACpBC,UAAqB;IARpB,KAAAR,KAAK,GAALA,KAAK;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,UAAU,GAAVA,UAAU;IAvFnB,KAAAC,SAAS,GAAQd,kBAAkB;IAI5B,KAAAe,YAAY,GAAQ,EAAE;IAEtB,KAAAC,UAAU,GAAG,aAAa;IAC1B,KAAAC,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACF,UAAU;MAC1BG,KAAK,EAAE;QACLC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAGxB,WAAW,CAACyB,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,KAAAC,MAAM,GAAU,CACrB;MACEC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,oBAAoB;;KAEnE,EACD;MACEL,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAAC3B,iBAAiB,CAAC0B,OAAO,CAAC,oBAAoB;;KAEnE,EACD;MACEL,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;QACL;QACAK,UAAU,EAAE,GAAGpC,WAAW,CAACyB,MAAM,KAAK;QACtCY,GAAG,EAAE,OAAO;QACZJ,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CI,MAAM,EAAE,kCAAkC;QAC1CC,IAAI,EAAE,IAAI,CAAC/B,iBAAiB,CAAC0B,OAAO,CAAC,+BAA+B,CAAC;QACrEM,MAAM,EAAE;UACNC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,aAAa;UACxBC,MAAM,EAAE;YACNJ,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE;;;OAGb;MACDI,YAAY,EAAE;KACf,EACD;MACEjB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLE,KAAK,EAAE,kBAAkB;QACzBH,IAAI,EAAE,OAAO;QACbiB,UAAU,EAAE;UACVC,KAAK,EACH;;OAEL;MACDF,YAAY,EAAE;KACf,CACF;IAED,KAAAG,SAAS,GAAQ,EAAE;IAajB,IAAI,CAACpC,aAAa,CAACqC,QAAQ,CAAC,aAAa,CAAC;EAC5C;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,OAAO;MACpBC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACV3B,IAAI,EAAE,EAAE;QACR4B,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,UAAU;UAChBC,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,OAAO;UACbC,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACX,SAAS,GAAG;MACfY,GAAG,EAAE,IAAI,CAACpD,eAAe,CAACqD,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAAC7D,KAAK,CACP8D,IAAI,CAAM,GAAGpE,WAAW,CAACyB,MAAM,YAAY,EAAEyC,oBAAoB,CAAC,CAClEG,SAAS,CAAEC,IAAS,IAAI;UACvBH,QAAQ,CAAC;YACPI,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAACnE,eAAe,CAACqD,iBAAiB,CAACe,IAAI;MACrDC,UAAU,EAAE,IAAI,CAACrE,eAAe,CAACqD,iBAAiB,CAACgB,UAAU;MAC7DC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CACvC;MACDC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MACrBC,aAAa,EAAE,CAAC,CAAC;MACjBC,OAAO,EAAE,CACP;QACEhE,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CuC,IAAI,EAAE,MAAM;QACZY,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAE,SAAAA,CAAUb,IAAS,EAAE3C,IAAS,EAAEyD,GAAQ;UAC9C,IAAIC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACvCF,GAAG,CAACG,GAAG,GAAGlB,IAAI;UACde,GAAG,CAACI,EAAE,GAAG,OAAOL,GAAG,CAACK,EAAE,EAAE;UACxBJ,GAAG,CAACH,SAAS,GAAG,kBAAkB;UAClCG,GAAG,CAACxC,KAAK,CAAC6C,QAAQ,GAAG,MAAM;UAC3BL,GAAG,CAACxC,KAAK,CAAC8C,eAAe,GAAG,MAAM;UAClCN,GAAG,CAACxC,KAAK,CAAC+C,SAAS,GAAG,OAAO;UAC7B,IAAItB,IAAI,IAAI,IAAI,EAAE;YAChBe,GAAG,CAACG,GAAG,GAAG,2CAA2C;;UAEvD;UACAH,GAAG,CAACQ,OAAO,GAAG;YACZ;YACA;YACAC,CAAC,CAAC,QAAQV,GAAG,CAACK,EAAE,EAAE,CAAC,CAACM,IAAI,CAAC,KAAK,EAAEV,GAAG,CAACG,GAAG,CAAC;UAC1C,CAAC;UACD,OAAOH,GAAG,CAACW,SAAS;QACtB;OACD,EACD;QACE/E,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CuC,IAAI,EAAE,MAAM;QACZY,SAAS,EAAE;OACZ,EACD;QACEjE,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;QAC7CuC,IAAI,EAAE,MAAM;QACZY,SAAS,EAAE;OACZ,EACD;QACEjE,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAAC0B,OAAO,CAAC,OAAO,CAAC;QAC9CuC,IAAI,EAAE,YAAY;QAClBY,SAAS,EAAE,iBAAiB;QAC5BC,MAAM,EAAE,SAAAA,CAAUb,IAAS,EAAE3C,IAAS,EAAEyD,GAAQ;UAC9C,IAAId,IAAI,EAAE;YACR,OAAO,4DAA4DA,IAAI,8EAA8E;WACtJ,MAAM;YACL,OAAO,qCAAqC;;QAEhD;OACD,EACD;QACErD,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAAC0B,OAAO,CAAC,QAAQ,CAAC;QAC/CuC,IAAI,EAAE,WAAW;QACjBY,SAAS,EAAE,iBAAiB;QAC5BC,MAAM,EAAEA,CAACb,IAAI,EAAE3C,IAAI,EAAEyD,GAAG,KAAI;UAC1B,IAAIa,OAAO,GAAG,EAAE;UAChB,IAAI3B,IAAI,EAAE;YACR2B,OAAO,GAAG,SAAS;;UAErB,OAAO;6BACUA,OAAO,0DAA0Db,GAAG,CAACK,EAAE,gBAAgBL,GAAG,CAACK,EAAE;sEACpDL,GAAG,CAACK,EAAE;yBACnD;QACf;OACD,EACD;QACExE,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAAC0B,OAAO,CAAC,QAAQ,CAAC;QAC/CuC,IAAI,EAAE,IAAI;QAEVY,SAAS,EAAE,iBAAiB;QAC5BC,MAAM,EAAEA,CAACb,IAAI,EAAE3C,IAAI,EAAEyD,GAAG,KAAI;UAC1B,OAAO,gEAAgEd,IAAI;cACzE,IAAI,CAACjE,iBAAiB,CAAC0B,OAAO,CAAC,gBAAgB,CAAC;sBACxC;QACZ;OACD,CACF;MACDmE,OAAO,EAAE;QACPxC,GAAG,EAAE,IAAI,CAACpD,eAAe,CAACqD,iBAAiB,CAACuC,OAAO,CAACxC,GAAG;QACvDwC,OAAO,EAAE,CACP;UACEC,IAAI,EACF,oCAAoC,GACpC,IAAI,CAAC9F,iBAAiB,CAAC0B,OAAO,CAAC,KAAK,CAAC;UACvCP,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4E,MAAM,CAAC,QAAQ;SACnC,EACD;UACED,IAAI,EACF,oCAAoC,GACpC,IAAI,CAAC9F,iBAAiB,CAAC0B,OAAO,CAAC,MAAM,CAAC;UACxCP,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4E,MAAM,CAAC,MAAM,CAAC;UACjCC,MAAM,EAAE;SACT,EACD;UACEF,IAAI,EACF,qCAAqC,GACrC,IAAI,CAAC9F,iBAAiB,CAAC0B,OAAO,CAAC,QAAQ,CAAC;UAC1CP,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4E,MAAM,CAAC,QAAQ,CAAC;UACnCC,MAAM,EAAE;SACT;;KAGN;EACH;EAEAD,MAAMA,CAAC5E,MAAM;IACX,IAAI,CAACT,MAAM,CAACS,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACpB,mBAAmB,CAACkG,kBAAkB,CAAC,IAAI,CAACxF,UAAU,CAAC,CAACyF,UAAU,EAAE;EAC3E;EAEAC,UAAUA,CAACC,MAAM;IACf,IAAI,CAACC,IAAI,GAAGD,MAAM;EACpB;EAEAE,WAAWA,CAAA;IACT,QAAQ,QAAQ;MACd,KAAK,QAAQ;QACX;QACA,IAAI,IAAI,CAACD,IAAI,EAAE;UACb;UACA;UACA;QAAA;QAEF;;EAEN;EAEAE,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAACtG,QAAQ,CAACuG,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGC,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,SAAS,CAAC,EAAE;QACxChE,OAAO,CAACC,GAAG,CAAC6D,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC1G,YAAY,CACd2G,cAAc,CAACJ,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,SAAS,CAAC,CAAC,CACpDE,SAAS,EAAE;;MAGhB,IAAIL,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,YAAY,CAAC,EAAE;QAC3ChE,OAAO,CAACC,GAAG,CAAC6D,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,YAAY,CAAC,CAAC;QACpD,IAAIG,OAAO,GAAGN,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,YAAY,CAAC;QACrD,IAAII,QAAQ,GAAG,IAAI,CAAC7G,aAAa,CAAC8G,IAAI,CAACxH,mBAAmB,EAAE;UAC1DyH,IAAI,EAAE,IAAI;UACVC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE;SACX,CAAC;QACFJ,QAAQ,CAACK,iBAAiB,CAACN,OAAO,GAAGA,OAAO;;IAEhD,CAAC,CAAC;EACJ;EACAO,WAAWA,CAAA;IACT3E,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAAC2D,UAAU,EAAE;EACnB;EAEAgB,OAAOA,CAACd,KAAK;IACX9D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6D,KAAK,CAAC;IAC7B,IAAI,CAACA,KAAK,CAACe,WAAW,IAAIf,KAAK,CAACvF,MAAM,KAAK,QAAQ,EAAE;MACnDyB,OAAO,CAACC,GAAG,CACT,CAAClD,KAAK,CAAC+H,GAAG,CAACC,IAAI,CAACjB,KAAK,CAACkB,SAAS,CAACC,IAAI,CAAC,EACrCnB,KAAK,CAACkB,SAAS,CAACC,IAAI,KAAK,EAAE,CAC5B;MACD,IACE,CAAClI,KAAK,CAAC+H,GAAG,CAACC,IAAI,CAACjB,KAAK,CAACkB,SAAS,CAACC,IAAI,CAAC,IACrCnB,KAAK,CAACkB,SAAS,CAACC,IAAI,KAAK,EAAE,EAC3B;QACA,IAAI,CAACvH,UAAU,CAACwH,WAAW,CAACpB,KAAK,CAACkB,SAAS,CAACC,IAAI,CAAC,CAAChE,SAAS,CAAEkE,GAAG,IAAI;UAClEnF,OAAO,CAACC,GAAG,CAACkF,GAAG,CAAC;QAClB,CAAC,CAAC;;;EAGR;EAAC,QAAAC,CAAA;qBAnTUpI,cAAc,EAAAqI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,QAAA,GAAAd,EAAA,CAAAC,iBAAA,CAAAc,EAAA,CAAAC,KAAA,GAAAhB,EAAA,CAAAC,iBAAA,CAAAgB,EAAA,CAAAC,SAAA;EAAA;EAAA,QAAAC,EAAA;UAAdxJ,cAAc;IAAAyJ,SAAA;IAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBACd/J,kBAAkB;;;;;;;;;;;;QC7B/BwI,EAAA,CAAAyB,cAAA,aAA+C;QAG3CzB,EAAA,CAAA0B,SAAA,4BAAyE;QACzE1B,EAAA,CAAAyB,cAAA,aAAkB;QAChBzB,EAAA,CAAA0B,SAAA,aAEM;QAMR1B,EAAA,CAAA2B,YAAA,EAAM;QAKV3B,EAAA,CAAAyB,cAAA,sBAIC;QAKGzB,EAAA,CAAA4B,UAAA,wBAAAC,iEAAA1D,MAAA;UAAA,OAAcqD,GAAA,CAAAtD,UAAA,CAAAC,MAAA,CAAkB;QAAA,EAAC,qBAAA2D,8DAAA3D,MAAA;UAAA,OAGtBqD,GAAA,CAAAjC,OAAA,CAAApB,MAAA,CAAe;QAAA,EAHO;QAKnC6B,EAAA,CAAA2B,YAAA,EAAqB;;;QA7BC3B,EAAA,CAAA+B,SAAA,GAA+B;QAA/B/B,EAAA,CAAAgC,UAAA,kBAAAR,GAAA,CAAA3G,aAAA,CAA+B;QAO/CmF,EAAA,CAAA+B,SAAA,GAAuB;QAAvB/B,EAAA,CAAAgC,UAAA,cAAAR,GAAA,CAAAhH,SAAA,CAAuB;QAU7BwF,EAAA,CAAA+B,SAAA,GAAmB;QAAnB/B,EAAA,CAAAgC,UAAA,SAAAR,GAAA,CAAAhJ,UAAA,CAAmB;QAIjBwH,EAAA,CAAA+B,SAAA,GAAmB;QAAnB/B,EAAA,CAAAgC,UAAA,UAAAR,GAAA,CAAAlJ,SAAA,CAAmB,WAAAkJ,GAAA,CAAArI,MAAA,YAAAqI,GAAA,CAAA/I,MAAA,kBAAA+I,GAAA,CAAAjJ,YAAA,cAAAiJ,GAAA,CAAAnD,WAAA", "names": ["environment", "DataTableDirective", "ManageClubComponent", "REGEX", "ClubsComponent", "constructor", "_http", "_coreSidebarService", "_translateService", "_commonsService", "renderer", "_clubService", "_modalService", "_titleService", "_s3Service", "dtElement", "paramsToPost", "table_name", "params", "editor_id", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "required", "label", "instant", "placeholder", "upload_url", "dir", "accept", "note", "config", "width", "height", "resizableArea", "fillColor", "output", "defaultValue", "attributes", "style", "dtOptions", "setTitle", "ngOnInit", "console", "log", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "lengthMenu", "columnDefs", "responsivePriority", "targets", "orderBy", "displayLength", "columns", "className", "render", "row", "img", "document", "createElement", "src", "id", "max<PERSON><PERSON><PERSON>", "backgroundColor", "objectFit", "onerror", "$", "attr", "outerHTML", "checked", "buttons", "text", "editor", "extend", "getSidebarRegistry", "toggle<PERSON><PERSON>", "formChange", "$event", "form", "onSubmitted", "ngAfterViewInit", "unlistener", "listen", "event", "target", "hasAttribute", "getAttribute", "toggleIsActive", "to<PERSON>romise", "club_id", "modalRef", "open", "size", "centered", "backdrop", "componentInstance", "ngOnDestroy", "onClose", "isSubmitted", "URL", "test", "formValue", "logo", "removeImage", "res", "_", "i0", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CoreSidebarService", "i3", "TranslateService", "i4", "CommonsService", "Renderer2", "i5", "ClubService", "i6", "NgbModal", "i7", "Title", "i8", "S3Service", "_2", "selectors", "viewQuery", "ClubsComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "ClubsComponent_Template_app_editor_sidebar_formChange_7_listener", "ClubsComponent_Template_app_editor_sidebar_onClose_7_listener", "ɵɵadvance", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\clubs\\clubs.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\clubs\\clubs.component.html"], "sourcesContent": ["import { environment } from '../../../../environments/environment';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from '../../../services/commons.service';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ManageClubComponent } from './manage-club/manage-club.component';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { S3Service } from 'app/services/s3.service';\r\nimport { REGEX } from 'app/helpers/regex';\r\n\r\n@Component({\r\n  selector: 'app-clubs',\r\n  templateUrl: './clubs.component.html',\r\n  styleUrls: ['./clubs.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class ClubsComponent implements OnInit {\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  private unlistener: () => void;\r\n  // public\r\n  public form: FormGroup;\r\n  public paramsToPost: any = {};\r\n  public contentHeader: object;\r\n  public table_name = 'clubs-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: 'Create new club',\r\n      edit: 'Edit club',\r\n      remove: 'Delete club',\r\n    },\r\n    url: `${environment.apiUrl}/clubs/editor`,\r\n    method: 'POST',\r\n    action: 'create',\r\n  };\r\n\r\n  public fields: any[] = [\r\n    {\r\n      key: 'name',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Name'),\r\n        placeholder: this._translateService.instant('Enter name of club'),\r\n      },\r\n    },\r\n    {\r\n      key: 'code',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Code'),\r\n        placeholder: this._translateService.instant('Enter code of club'),\r\n      },\r\n    },\r\n    {\r\n      key: 'logo',\r\n      type: 'image-cropper',\r\n      props: {\r\n        // upload_url: `${environment.apiUrl}/files/editor`,\r\n        upload_url: `${environment.apiUrl}/s3`,\r\n        dir: 'clubs',\r\n        label: this._translateService.instant('Logo'),\r\n        accept: 'image/png, image/jpg, image/jpeg',\r\n        note: this._translateService.instant('Note: Image ratio must be 1:1'),\r\n        config: {\r\n          width: 200,\r\n          height: 200,\r\n          resizableArea: false,\r\n          fillColor: 'transparent',\r\n          output: {\r\n            width: 256,\r\n            height: 256,\r\n          },\r\n        },\r\n      },\r\n      defaultValue: '',\r\n    },\r\n    {\r\n      key: 'color_code',\r\n      type: 'input',\r\n      props: {\r\n        label: 'Background Color',\r\n        type: 'color',\r\n        attributes: {\r\n          style:\r\n            'margin: initial; border: 1px solid #2d67f1; border-radius: 50%; width: 40px; height: 40px;',\r\n        },\r\n      },\r\n      defaultValue: '',\r\n    },\r\n  ];\r\n\r\n  dtOptions: any = {};\r\n\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public renderer: Renderer2,\r\n    public _clubService: ClubService,\r\n    public _modalService: NgbModal,\r\n    public _titleService: Title,\r\n    public _s3Service: S3Service\r\n  ) {\r\n    this._titleService.setTitle('Table Clubs');\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log('ClubsComponent OnInit');\r\n    this.contentHeader = {\r\n      headerTitle: 'Clubs',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Settings',\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: 'Clubs',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .post<any>(`${environment.apiUrl}/clubs/all`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: -1 },\r\n        { responsivePriority: 1, targets: -2 },\r\n        { responsivePriority: 2, targets: -3 },\r\n      ],\r\n      orderBy: [[1, 'asc']],\r\n      displayLength: -1,\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('Logo'),\r\n          data: 'logo',\r\n          className: 'p-1',\r\n          render: function (data: any, type: any, row: any) {\r\n            let img = document.createElement('img');\r\n            img.src = data;\r\n            img.id = `img-${row.id}`;\r\n            img.className = 'avatar avatar-lg';\r\n            img.style.maxWidth = '50px';\r\n            img.style.backgroundColor = '#fff';\r\n            img.style.objectFit = 'cover';\r\n            if (data == null) {\r\n              img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n            }\r\n            // check get image error\r\n            img.onerror = function () {\r\n              // img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n              // set src by row id\r\n              $(`#img-${row.id}`).attr('src', img.src);\r\n            };\r\n            return img.outerHTML;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Name'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Code'),\r\n          data: 'code',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Color'),\r\n          data: 'color_code',\r\n          className: 'text-center p-1',\r\n          render: function (data: any, type: any, row: any) {\r\n            if (data) {\r\n              return `<div style=\"width: 35px; height: 35px; background-color: ${data}; border: 1px solid #ccc; border-radius: 50%; display: inline-block;\"></div>`;\r\n            } else {\r\n              return '<span class=\"text-muted\">N/A</span>';\r\n            }\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Active'),\r\n          data: 'is_active',\r\n          className: 'text-center p-0',\r\n          render: (data, type, row) => {\r\n            let checked = '';\r\n            if (data) {\r\n              checked = 'checked';\r\n            }\r\n            return `<div class=\"custom-control custom-switch custom-control-inline\">\r\n                    <input ${checked} type=\"checkbox\" class=\"custom-control-input\" club_id=\"${row.id}\" id=\"switch_${row.id}\" />\r\n                    <label class=\"custom-control-label\" for=\"switch_${row.id}\"></label>\r\n                  </div>`;\r\n          },\r\n        },\r\n        {\r\n          title: this._translateService.instant('Action'),\r\n          data: 'id',\r\n\r\n          className: 'text-center p-1',\r\n          render: (data, type, row) => {\r\n            return `<button class=\"btn btn-icon btn-outline-primary\" btn_manage=\"${data}\">\r\n            ${this._translateService.instant('Assign Manager')}\r\n            </button>`;\r\n          },\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-plus\"></i> ' +\r\n              this._translateService.instant('Add'),\r\n            action: () => this.editor('create'),\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-edit\"></i> ' +\r\n              this._translateService.instant('Edit'),\r\n            action: () => this.editor('edit'),\r\n            extend: 'selected',\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-trash\"></i> ' +\r\n              this._translateService.instant('Delete'),\r\n            action: () => this.editor('remove'),\r\n            extend: 'selected',\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  editor(action) {\r\n    this.params.action = action;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  formChange($event) {\r\n    this.form = $event;\r\n  }\r\n\r\n  onSubmitted() {\r\n    switch ('create') {\r\n      case 'create':\r\n        // if form is not empty\r\n        if (this.form) {\r\n          // this.paramsToPost = {\r\n          //   \"data[0][photo]\": \"hehe\",\r\n          // };\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('club_id')) {\r\n        console.log(event.target.getAttribute('club_id'));\r\n        this._clubService\r\n          .toggleIsActive(event.target.getAttribute('club_id'))\r\n          .toPromise();\r\n      }\r\n\r\n      if (event.target.hasAttribute('btn_manage')) {\r\n        console.log(event.target.getAttribute('btn_manage'));\r\n        let club_id = event.target.getAttribute('btn_manage');\r\n        let modalRef = this._modalService.open(ManageClubComponent, {\r\n          size: 'lg',\r\n          centered: true,\r\n          backdrop: 'static',\r\n        });\r\n        modalRef.componentInstance.club_id = club_id;\r\n      }\r\n    });\r\n  }\r\n  ngOnDestroy(): void {\r\n    console.log('ClubsComponent destroy');\r\n    this.unlistener();\r\n  }\r\n\r\n  onClose(event) {\r\n    console.log('onClose', event);\r\n    if (!event.isSubmitted && event.action !== 'remove') {\r\n      console.log(\r\n        !REGEX.URL.test(event.formValue.logo),\r\n        event.formValue.logo !== ''\r\n      );\r\n      if (\r\n        !REGEX.URL.test(event.formValue.logo) &&\r\n        event.formValue.logo !== ''\r\n      ) {\r\n        this._s3Service.removeImage(event.formValue.logo).subscribe((res) => {\r\n          console.log(res);\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <!-- <h4 class=\"card-title\">Table Basic</h4> -->\r\n      </div>\r\n      <table\r\n        datatable\r\n        [dtOptions]=\"dtOptions\"\r\n        class=\"table border row-border hover\"\r\n      ></table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Modal -->\r\n<core-sidebar\r\n  class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n  [name]=\"table_name\"\r\n  overlayClass=\"modal-backdrop\"\r\n>\r\n  <app-editor-sidebar\r\n    [table]=\"dtElement\"\r\n    [fields]=\"fields\"\r\n    [params]=\"params\"\r\n    (formChange)=\"formChange($event)\"\r\n    [paramsToPost]=\"paramsToPost\"\r\n    [onSubmit]=\"onSubmitted\"\r\n    (onClose)=\"onClose($event)\"\r\n  >\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
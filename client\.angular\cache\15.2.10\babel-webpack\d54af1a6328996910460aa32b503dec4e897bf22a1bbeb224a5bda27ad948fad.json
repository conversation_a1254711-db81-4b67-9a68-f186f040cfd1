{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Recursively flattens `array`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flattenDeep([1, [2, [3, [4]], 5]]);\n * // => [1, 2, 3, 4, 5]\n */\nfunction flattenDeep(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, INFINITY) : [];\n}\nexport default flattenDeep;", "map": {"version": 3, "names": ["baseFlatten", "INFINITY", "flattenDeep", "array", "length"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/flattenDeep.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Recursively flattens `array`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flattenDeep([1, [2, [3, [4]], 5]]);\n * // => [1, 2, 3, 4, 5]\n */\nfunction flattenDeep(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, INFINITY) : [];\n}\n\nexport default flattenDeep;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;EAC7C,OAAOA,MAAM,GAAGJ,WAAW,CAACG,KAAK,EAAEF,QAAQ,CAAC,GAAG,EAAE;AACnD;AAEA,eAAeC,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
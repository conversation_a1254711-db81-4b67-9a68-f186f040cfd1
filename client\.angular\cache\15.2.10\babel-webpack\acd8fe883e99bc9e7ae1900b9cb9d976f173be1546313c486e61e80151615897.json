{"ast": null, "code": "import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowWhen(closingSelector) {\n  return function windowWhenOperatorFunction(source) {\n    return source.lift(new WindowOperator(closingSelector));\n  };\n}\nclass WindowOperator {\n  constructor(closingSelector) {\n    this.closingSelector = closingSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new WindowSubscriber(subscriber, this.closingSelector));\n  }\n}\nclass WindowSubscriber extends OuterSubscriber {\n  constructor(destination, closingSelector) {\n    super(destination);\n    this.destination = destination;\n    this.closingSelector = closingSelector;\n    this.openWindow();\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.openWindow(innerSub);\n  }\n  notifyError(error, innerSub) {\n    this._error(error);\n  }\n  notifyComplete(innerSub) {\n    this.openWindow(innerSub);\n  }\n  _next(value) {\n    this.window.next(value);\n  }\n  _error(err) {\n    this.window.error(err);\n    this.destination.error(err);\n    this.unsubscribeClosingNotification();\n  }\n  _complete() {\n    this.window.complete();\n    this.destination.complete();\n    this.unsubscribeClosingNotification();\n  }\n  unsubscribeClosingNotification() {\n    if (this.closingNotification) {\n      this.closingNotification.unsubscribe();\n    }\n  }\n  openWindow(innerSub = null) {\n    if (innerSub) {\n      this.remove(innerSub);\n      innerSub.unsubscribe();\n    }\n    const prevWindow = this.window;\n    if (prevWindow) {\n      prevWindow.complete();\n    }\n    const window = this.window = new Subject();\n    this.destination.next(window);\n    let closingNotifier;\n    try {\n      const {\n        closingSelector\n      } = this;\n      closingNotifier = closingSelector();\n    } catch (e) {\n      this.destination.error(e);\n      this.window.error(e);\n      return;\n    }\n    this.add(this.closingNotification = subscribeToResult(this, closingNotifier));\n  }\n}", "map": {"version": 3, "names": ["Subject", "OuterSubscriber", "subscribeToResult", "windowWhen", "closingSelector", "windowWhenOperatorFunction", "source", "lift", "WindowOperator", "constructor", "call", "subscriber", "subscribe", "WindowSubscriber", "destination", "openWindow", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "notifyError", "error", "_error", "notifyComplete", "_next", "value", "window", "next", "err", "unsubscribeClosingNotification", "_complete", "complete", "closingNotification", "unsubscribe", "remove", "prevWindow", "closingNotifier", "e", "add"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/windowWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowWhen(closingSelector) {\n    return function windowWhenOperatorFunction(source) {\n        return source.lift(new WindowOperator(closingSelector));\n    };\n}\nclass WindowOperator {\n    constructor(closingSelector) {\n        this.closingSelector = closingSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WindowSubscriber(subscriber, this.closingSelector));\n    }\n}\nclass WindowSubscriber extends OuterSubscriber {\n    constructor(destination, closingSelector) {\n        super(destination);\n        this.destination = destination;\n        this.closingSelector = closingSelector;\n        this.openWindow();\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.openWindow(innerSub);\n    }\n    notifyError(error, innerSub) {\n        this._error(error);\n    }\n    notifyComplete(innerSub) {\n        this.openWindow(innerSub);\n    }\n    _next(value) {\n        this.window.next(value);\n    }\n    _error(err) {\n        this.window.error(err);\n        this.destination.error(err);\n        this.unsubscribeClosingNotification();\n    }\n    _complete() {\n        this.window.complete();\n        this.destination.complete();\n        this.unsubscribeClosingNotification();\n    }\n    unsubscribeClosingNotification() {\n        if (this.closingNotification) {\n            this.closingNotification.unsubscribe();\n        }\n    }\n    openWindow(innerSub = null) {\n        if (innerSub) {\n            this.remove(innerSub);\n            innerSub.unsubscribe();\n        }\n        const prevWindow = this.window;\n        if (prevWindow) {\n            prevWindow.complete();\n        }\n        const window = this.window = new Subject();\n        this.destination.next(window);\n        let closingNotifier;\n        try {\n            const { closingSelector } = this;\n            closingNotifier = closingSelector();\n        }\n        catch (e) {\n            this.destination.error(e);\n            this.window.error(e);\n            return;\n        }\n        this.add(this.closingNotification = subscribeToResult(this, closingNotifier));\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAE;EACxC,OAAO,SAASC,0BAA0BA,CAACC,MAAM,EAAE;IAC/C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,cAAc,CAACJ,eAAe,CAAC,CAAC;EAC3D,CAAC;AACL;AACA,MAAMI,cAAc,CAAC;EACjBC,WAAWA,CAACL,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACAM,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,gBAAgB,CAACF,UAAU,EAAE,IAAI,CAACP,eAAe,CAAC,CAAC;EACnF;AACJ;AACA,MAAMS,gBAAgB,SAASZ,eAAe,CAAC;EAC3CQ,WAAWA,CAACK,WAAW,EAAEV,eAAe,EAAE;IACtC,KAAK,CAACU,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACV,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACW,UAAU,CAAC,CAAC;EACrB;EACAC,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACN,UAAU,CAACM,QAAQ,CAAC;EAC7B;EACAC,WAAWA,CAACC,KAAK,EAAEF,QAAQ,EAAE;IACzB,IAAI,CAACG,MAAM,CAACD,KAAK,CAAC;EACtB;EACAE,cAAcA,CAACJ,QAAQ,EAAE;IACrB,IAAI,CAACN,UAAU,CAACM,QAAQ,CAAC;EAC7B;EACAK,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC;EAC3B;EACAH,MAAMA,CAACM,GAAG,EAAE;IACR,IAAI,CAACF,MAAM,CAACL,KAAK,CAACO,GAAG,CAAC;IACtB,IAAI,CAAChB,WAAW,CAACS,KAAK,CAACO,GAAG,CAAC;IAC3B,IAAI,CAACC,8BAA8B,CAAC,CAAC;EACzC;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACnB,WAAW,CAACmB,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACF,8BAA8B,CAAC,CAAC;EACzC;EACAA,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,IAAI,CAACG,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACC,WAAW,CAAC,CAAC;IAC1C;EACJ;EACApB,UAAUA,CAACM,QAAQ,GAAG,IAAI,EAAE;IACxB,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACe,MAAM,CAACf,QAAQ,CAAC;MACrBA,QAAQ,CAACc,WAAW,CAAC,CAAC;IAC1B;IACA,MAAME,UAAU,GAAG,IAAI,CAACT,MAAM;IAC9B,IAAIS,UAAU,EAAE;MACZA,UAAU,CAACJ,QAAQ,CAAC,CAAC;IACzB;IACA,MAAML,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI5B,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACc,WAAW,CAACe,IAAI,CAACD,MAAM,CAAC;IAC7B,IAAIU,eAAe;IACnB,IAAI;MACA,MAAM;QAAElC;MAAgB,CAAC,GAAG,IAAI;MAChCkC,eAAe,GAAGlC,eAAe,CAAC,CAAC;IACvC,CAAC,CACD,OAAOmC,CAAC,EAAE;MACN,IAAI,CAACzB,WAAW,CAACS,KAAK,CAACgB,CAAC,CAAC;MACzB,IAAI,CAACX,MAAM,CAACL,KAAK,CAACgB,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACC,GAAG,CAAC,IAAI,CAACN,mBAAmB,GAAGhC,iBAAiB,CAAC,IAAI,EAAEoC,eAAe,CAAC,CAAC;EACjF;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
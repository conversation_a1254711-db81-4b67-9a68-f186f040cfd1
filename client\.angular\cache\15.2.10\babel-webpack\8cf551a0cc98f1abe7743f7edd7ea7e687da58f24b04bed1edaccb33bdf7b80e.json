{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport jsPDF from 'jspdf';\nimport { AppConfig } from 'app/app-config';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/teamsheet.service\";\nimport * as i2 from \"@angular/common\";\nfunction TeamsheetTemplateComponent_tr_51_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"img\", 27);\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"photo_\" + item_r3.player.id)(\"src\", ctx_r2.replaceImageURL(item_r3.player.photo), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r3.player.user.first_name, \" \", item_r3.player.user.last_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 5, item_r3.player.dob, \"yyyy\"), \" \");\n  }\n}\nfunction TeamsheetTemplateComponent_tr_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, TeamsheetTemplateComponent_tr_51_td_1_Template, 8, 8, \"td\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const items_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", items_r1);\n  }\n}\nexport class TeamsheetTemplateComponent {\n  constructor(_teamsheetService) {\n    this._teamsheetService = _teamsheetService;\n    this.today = moment(new Date()).format('DD-MMM-YYYY');\n    this.APP_NAME = AppConfig.APP_NAME;\n    this.onSubmitted = new EventEmitter();\n    console.log(this.team_player);\n  }\n  ngOnInit() {\n    // this.getTeamSheet2Submit();\n    this.onSubmitted.subscribe(data => {\n      this.getTeamSheet2Submit();\n      setTimeout(() => {\n        this.submitTeamSheet();\n      }, 1000);\n    });\n  }\n  submitTeamSheet() {\n    // export to pdf\n    let html = document.getElementById('team-sheet').innerHTML;\n    let doc = new jsPDF('p', 'px', 'a4');\n    let font = new FontFace('SimSun', 'url(assets/fonts/pdf/SIMSUN.ttf)');\n    let file_name = `Team Sheet-${this.season.name}-${this.currentTeam.group.name}-${this.currentTeam.name}(${this.today}).pdf`;\n    font.load().then(function () {\n      // set font for div content\n      document.fonts.add(font);\n      doc.addFont('assets/fonts/pdf/SIMSUN.ttf', 'SimSun', 'normal');\n      doc.setFont('SimSun');\n      doc.html(html, {\n        callback: doc => {\n          doc.save(file_name);\n        },\n        x: 10,\n        y: 10,\n        html2canvas: {\n          scale: 1\n        }\n      });\n    });\n  }\n  convertImageToBase64(imgUrl, callback) {\n    const image = new Image();\n    image.crossOrigin = 'anonymous';\n    image.onload = () => {\n      // console.log(image);\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.height = image.naturalHeight;\n      canvas.width = image.naturalWidth;\n      ctx.drawImage(image, 0, 0);\n      const dataUrl = canvas.toDataURL();\n      callback && callback(dataUrl);\n    };\n    image.src = imgUrl;\n  }\n  replaceImage(url, image_id) {\n    url = url.replace('https://app.hkjfl.com/hkjfl', '/hkjfl');\n    this.convertImageToBase64(url, dataUrl => {\n      document.getElementById(image_id).setAttribute('src', dataUrl);\n    });\n  }\n  replaceImageURL(url) {\n    if (!url) return url;\n    url = url.replace('https://app.hkjfl.com/hkjfl', '/hkjfl');\n    return url;\n  }\n  ngAfterViewInit() {\n    // if (this.currentTeam?.club?.logo) {\n    //   this.replaceImage(this.currentTeam.club.logo, 'club_logo');\n    // }\n  }\n  getTeamSheet2Submit() {\n    this._teamsheetService.getTeamSheet2Submit(this.team_id).toPromise().then(res => {\n      console.log('team sheet', res);\n      this.team_player = res.team_players;\n      this.currentTeam = res.team;\n      this.season = res.season;\n      // team_player to array\n      this.team_player = Object.keys(this.team_player).map(key => {\n        return this.team_player[key];\n      });\n      this.team_player = this.team_player.reduce((resultArray, item, index) => {\n        const chunkIndex = Math.floor(index / 4);\n        if (!resultArray[chunkIndex]) {\n          resultArray[chunkIndex] = []; // start a new chunk\n        }\n\n        resultArray[chunkIndex].push(item);\n        return resultArray;\n      }, []);\n      console.log(this.team_player);\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  static #_ = this.ɵfac = function TeamsheetTemplateComponent_Factory(t) {\n    return new (t || TeamsheetTemplateComponent)(i0.ɵɵdirectiveInject(i1.TeamsheetService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamsheetTemplateComponent,\n    selectors: [[\"app-teamsheet-template\"]],\n    inputs: {\n      team_id: \"team_id\",\n      season: \"season\",\n      currentTeam: \"currentTeam\",\n      team_player: \"team_player\",\n      onSubmitted: \"onSubmitted\"\n    },\n    decls: 52,\n    vars: 11,\n    consts: [[2, \"display\", \"none\"], [1, \"modal-header\"], [\"id\", \"myModalLabel1\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [1, \"row\", \"justify-content-center\"], [\"id\", \"team-sheet\"], [\"lang\", \"en\"], [\"http-equiv\", \"Content-Type\", \"content\", \"text/html; charset=utf-8\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"http-equiv\", \"X-UA-Compatible\", \"content\", \"ie=edge\"], [2, \"width\", \"425px\"], [2, \"width\", \"425px\", \"border\", \"0\", \"font-family\", \"Arial\", \"color\", \"black !important\"], [2, \"font-size\", \"12px\"], [1, \"p-0\"], [\"src\", \"assets/images/logo/ezactive_1024x1024.png\", \"width\", \"50px\"], [1, \"p-0\", 2, \"width\", \"150px\", \"vertical-align\", \"middle\"], [1, \"text-left\"], [2, \"font-weight\", \"700\"], [\"id\", \"club_logo\", \"width\", \"50px\", 3, \"src\"], [1, \"table\", \"table-bordered\", 2, \"width\", \"425px\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-0\", \"style\", \"font-size: 10px; width: 106px\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-0\", 2, \"font-size\", \"10px\", \"width\", \"106px\"], [1, \"text-center\"], [\"width\", \"80px\", \"height\", \"80px\", \"object-fit\", \"cover\", 1, \"mt-1\", 2, \"margin\", \"0 auto\", 3, \"id\", \"src\"], [1, \"text-center\", 2, \"font-size\", \"10px\", \"padding\", \"2px\", \"font-family\", \"SimSun\"]],\n    template: function TeamsheetTemplateComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n        i0.ɵɵtext(3, \"Preview Team Sheet\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 3)(5, \"span\", 4);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"html\", 8)(11, \"head\");\n        i0.ɵɵelement(12, \"meta\", 9)(13, \"meta\", 10)(14, \"meta\", 11)(15, \"meta\", 12);\n        i0.ɵɵelementStart(16, \"title\");\n        i0.ɵɵtext(17, \"Team Sheet\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"body\")(19, \"div\", 13)(20, \"table\", 14);\n        i0.ɵɵelement(21, \"br\");\n        i0.ɵɵelementStart(22, \"tbody\", 15)(23, \"tr\")(24, \"td\", 16);\n        i0.ɵɵelement(25, \"img\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"td\", 18)(27, \"div\", 19)(28, \"strong\", 20);\n        i0.ɵɵtext(29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(30, \"br\");\n        i0.ɵɵtext(31);\n        i0.ɵɵpipe(32, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"td\", 16)(34, \"div\", 19)(35, \"strong\");\n        i0.ɵɵtext(36, \"Club:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(37);\n        i0.ɵɵelement(38, \"br\");\n        i0.ɵɵelementStart(39, \"strong\");\n        i0.ɵɵtext(40, \"Group:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(41);\n        i0.ɵɵelement(42, \"br\");\n        i0.ɵɵelementStart(43, \"strong\");\n        i0.ɵɵtext(44, \"Team:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(45);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(46, \"td\", 16);\n        i0.ɵɵelement(47, \"img\", 21);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(48, \"hr\");\n        i0.ɵɵelementStart(49, \"table\", 22)(50, \"tbody\");\n        i0.ɵɵtemplate(51, TeamsheetTemplateComponent_tr_51_Template, 2, 1, \"tr\", 23);\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(29);\n        i0.ɵɵtextInterpolate2(\"\", ctx.APP_NAME, \" - \", ctx.season == null ? null : ctx.season.name, \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" Submitted: \", i0.ɵɵpipeBind2(32, 8, ctx.today, \"yyyy-MMM-dd\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentTeam == null ? null : ctx.currentTeam.club == null ? null : ctx.currentTeam.club.name, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentTeam == null ? null : ctx.currentTeam.group == null ? null : ctx.currentTeam.group.name, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentTeam == null ? null : ctx.currentTeam.name, \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"src\", ctx.replaceImageURL(ctx.currentTeam == null ? null : ctx.currentTeam.club == null ? null : ctx.currentTeam.club.logo), i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.team_player);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.DatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAKEA,YAAY,QAEP,eAAe;AACtB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,OAAOC,MAAM,MAAM,QAAQ;;;;;;ICyEPC,EAAA,CAAAC,cAAA,aAIC;IAEGD,EAAA,CAAAE,SAAA,cAQE;IACFF,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAG,MAAA,GAEA;IAAAH,EAAA,CAAAE,SAAA,SAAM;IACNF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAlBJJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,UAAA,kBAAAC,OAAA,CAAAC,MAAA,CAAAC,EAAA,CAAgC,QAAAC,MAAA,CAAAC,eAAA,CAAAJ,OAAA,CAAAC,MAAA,CAAAI,KAAA,GAAAZ,EAAA,CAAAa,aAAA;IAchCb,EAAA,CAAAK,SAAA,GAEA;IAFAL,EAAA,CAAAc,kBAAA,MAAAP,OAAA,CAAAC,MAAA,CAAAO,IAAA,CAAAC,UAAA,OAAAT,OAAA,CAAAC,MAAA,CAAAO,IAAA,CAAAE,SAAA,MAEA;IACAjB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAmB,WAAA,OAAAZ,OAAA,CAAAC,MAAA,CAAAY,GAAA,eACF;;;;;IA5BNpB,EAAA,CAAAC,cAAA,SAAsC;IACpCD,EAAA,CAAAqB,UAAA,IAAAC,8CAAA,iBA6BK;IACPtB,EAAA,CAAAI,YAAA,EAAK;;;;IA5BgBJ,EAAA,CAAAK,SAAA,GAAQ;IAARL,EAAA,CAAAM,UAAA,YAAAiB,QAAA,CAAQ;;;ADpE/C,OAAM,MAAOC,0BAA0B;EASrCC,YAAmBC,iBAAmC;IAAnC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IARpC,KAAAC,KAAK,GAAG5B,MAAM,CAAC,IAAI6B,IAAI,EAAE,CAAC,CAACC,MAAM,CAAC,aAAa,CAAC;IAEhD,KAAAC,QAAQ,GAAGhC,SAAS,CAACgC,QAAQ;IAKpB,KAAAC,WAAW,GAAsB,IAAInC,YAAY,EAAO;IAE/DoC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC;EAC/B;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,CAACK,SAAS,CAAEC,IAAI,IAAI;MAClC,IAAI,CAACC,mBAAmB,EAAE;MAC1BC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EAEAA,eAAeA,CAAA;IACb;IACA,IAAIC,IAAI,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,SAAS;IAC1D,IAAIC,GAAG,GAAG,IAAIhD,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACpC,IAAIiD,IAAI,GAAG,IAAIC,QAAQ,CAAC,QAAQ,EAAE,kCAAkC,CAAC;IACrE,IAAIC,SAAS,GAAG,cAAc,IAAI,CAACC,MAAM,CAACC,IAAI,IAAI,IAAI,CAACC,WAAW,CAACC,KAAK,CAACF,IAAI,IAAI,IAAI,CAACC,WAAW,CAACD,IAAI,IAAI,IAAI,CAACvB,KAAK,OAAO;IAC3HmB,IAAI,CAACO,IAAI,EAAE,CAACC,IAAI,CAAC;MACf;MACCZ,QAAQ,CAACa,KAAa,CAACC,GAAG,CAACV,IAAI,CAAC;MACjCD,GAAG,CAACY,OAAO,CAAC,6BAA6B,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAC9DZ,GAAG,CAACa,OAAO,CAAC,QAAQ,CAAC;MACrBb,GAAG,CAACJ,IAAI,CAACA,IAAI,EAAE;QACbkB,QAAQ,EAAGd,GAAG,IAAI;UAChBA,GAAG,CAACe,IAAI,CAACZ,SAAS,CAAC;QACrB,CAAC;QACDa,CAAC,EAAE,EAAE;QACLC,CAAC,EAAE,EAAE;QACLC,WAAW,EAAE;UACXC,KAAK,EAAE;;OAEV,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,oBAAoBA,CAACC,MAAM,EAAEP,QAAQ;IACnC,MAAMQ,KAAK,GAAG,IAAIC,KAAK,EAAE;IACzBD,KAAK,CAACE,WAAW,GAAG,WAAW;IAC/BF,KAAK,CAACG,MAAM,GAAG,MAAK;MAClB;MACA,MAAMC,MAAM,GAAG7B,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;MACnCH,MAAM,CAACI,MAAM,GAAGR,KAAK,CAACS,aAAa;MACnCL,MAAM,CAACM,KAAK,GAAGV,KAAK,CAACW,YAAY;MACjCL,GAAG,CAACM,SAAS,CAACZ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMa,OAAO,GAAGT,MAAM,CAACU,SAAS,EAAE;MAClCtB,QAAQ,IAAIA,QAAQ,CAACqB,OAAO,CAAC;IAC/B,CAAC;IACDb,KAAK,CAACe,GAAG,GAAGhB,MAAM;EACpB;EAEAiB,YAAYA,CAACC,GAAG,EAAEC,QAAQ;IACxBD,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,6BAA6B,EAAE,QAAQ,CAAC;IAC1D,IAAI,CAACrB,oBAAoB,CAACmB,GAAG,EAAGJ,OAAO,IAAI;MACzCtC,QAAQ,CAACC,cAAc,CAAC0C,QAAQ,CAAC,CAACE,YAAY,CAAC,KAAK,EAAEP,OAAO,CAAC;IAChE,CAAC,CAAC;EACJ;EAEArE,eAAeA,CAACyE,GAAG;IACjB,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;IACpBA,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,6BAA6B,EAAE,QAAQ,CAAC;IAC1D,OAAOF,GAAG;EACZ;EACAI,eAAeA,CAAA;IACb;IACA;IACA;EAAA;EAGFlD,mBAAmBA,CAAA;IACjB,IAAI,CAACZ,iBAAiB,CACnBY,mBAAmB,CAAC,IAAI,CAACmD,OAAO,CAAC,CACjCC,SAAS,EAAE,CACXpC,IAAI,CAAEqC,GAAG,IAAI;MACZ3D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0D,GAAG,CAAC;MAC9B,IAAI,CAACzD,WAAW,GAAGyD,GAAG,CAACC,YAAY;MACnC,IAAI,CAACzC,WAAW,GAAGwC,GAAG,CAACE,IAAI;MAC3B,IAAI,CAAC5C,MAAM,GAAG0C,GAAG,CAAC1C,MAAM;MACxB;MACA,IAAI,CAACf,WAAW,GAAG4D,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7D,WAAW,CAAC,CAAC8D,GAAG,CAAEC,GAAG,IAAI;QAC3D,OAAO,IAAI,CAAC/D,WAAW,CAAC+D,GAAG,CAAC;MAC9B,CAAC,CAAC;MACF,IAAI,CAAC/D,WAAW,GAAG,IAAI,CAACA,WAAW,CAACgE,MAAM,CACxC,CAACC,WAAW,EAAEC,IAAI,EAAEC,KAAK,KAAI;QAC3B,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC;QACxC,IAAI,CAACF,WAAW,CAACG,UAAU,CAAC,EAAE;UAC5BH,WAAW,CAACG,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;;;QAEhCH,WAAW,CAACG,UAAU,CAAC,CAACG,IAAI,CAACL,IAAI,CAAC;QAElC,OAAOD,WAAW;MACpB,CAAC,EACD,EAAE,CACH;MACDnE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC;IAC/B,CAAC,CAAC,CACDwE,KAAK,CAAEC,GAAG,IAAI;MACb3E,OAAO,CAACC,GAAG,CAAC0E,GAAG,CAAC;IAClB,CAAC,CAAC;EACN;EAAC,QAAAC,CAAA;qBA/GUpF,0BAA0B,EAAAxB,EAAA,CAAA6G,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAA1BxF,0BAA0B;IAAAyF,SAAA;IAAAC,MAAA;MAAAzB,OAAA;MAAAxC,MAAA;MAAAE,WAAA;MAAAjB,WAAA;MAAAH,WAAA;IAAA;IAAAoF,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAA/C,GAAA;MAAA,IAAA+C,EAAA;QCjBvCxH,EAAA,CAAAC,cAAA,aAA2B;QAEoBD,EAAA,CAAAG,MAAA,yBAAkB;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAClEJ,EAAA,CAAAC,cAAA,gBAAuD;QAC5BD,EAAA,CAAAG,MAAA,aAAO;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAG3CJ,EAAA,CAAAC,cAAA,aAAkD;QAMxCD,EAAA,CAAAE,SAAA,eAGE;QAOFF,EAAA,CAAAC,cAAA,aAAO;QAAAD,EAAA,CAAAG,MAAA,kBAAU;QAAAH,EAAA,CAAAI,YAAA,EAAQ;QAG3BJ,EAAA,CAAAC,cAAA,YAAM;QAUAD,EAAA,CAAAE,SAAA,UAAM;QACNF,EAAA,CAAAC,cAAA,iBAA+B;QAGzBD,EAAA,CAAAE,SAAA,eAGE;QACJF,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAC,cAAA,cAGC;QAGMD,EAAA,CAAAG,MAAA,IAAmC;QAAAH,EAAA,CAAAI,YAAA,EACrC;QACDJ,EAAA,CAAAE,SAAA,UAAM;QACNF,EAAA,CAAAG,MAAA,IACF;;QAAAH,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAC,cAAA,cAAgB;QAEJD,EAAA,CAAAG,MAAA,aAAK;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IACvB;QAAAH,EAAA,CAAAE,SAAA,UAAM;QACNF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAG,MAAA,cAAM;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IACxB;QAAAH,EAAA,CAAAE,SAAA,UAAM;QACNF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAG,MAAA,aAAK;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IACzB;QAAAH,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAC,cAAA,cAAgB;QACdD,EAAA,CAAAE,SAAA,eAIE;QACJF,EAAA,CAAAI,YAAA,EAAK;QAIXJ,EAAA,CAAAE,SAAA,UAAM;QAINF,EAAA,CAAAC,cAAA,iBAAyD;QAErDD,EAAA,CAAAqB,UAAA,KAAAoG,yCAAA,iBA+BK;QACPzH,EAAA,CAAAI,YAAA,EAAQ;;;QA/DGJ,EAAA,CAAAK,SAAA,IAAmC;QAAnCL,EAAA,CAAAc,kBAAA,KAAA2D,GAAA,CAAA3C,QAAA,SAAA2C,GAAA,CAAAxB,MAAA,kBAAAwB,GAAA,CAAAxB,MAAA,CAAAC,IAAA,KAAmC;QAGtClD,EAAA,CAAAK,SAAA,GACF;QADEL,EAAA,CAAAkB,kBAAA,iBAAAlB,EAAA,CAAAmB,WAAA,QAAAsD,GAAA,CAAA9C,KAAA,sBACF;QAIyB3B,EAAA,CAAAK,SAAA,GACvB;QADuBL,EAAA,CAAAkB,kBAAA,MAAAuD,GAAA,CAAAtB,WAAA,kBAAAsB,GAAA,CAAAtB,WAAA,CAAAuE,IAAA,kBAAAjD,GAAA,CAAAtB,WAAA,CAAAuE,IAAA,CAAAxE,IAAA,MACvB;QACwBlD,EAAA,CAAAK,SAAA,GACxB;QADwBL,EAAA,CAAAkB,kBAAA,MAAAuD,GAAA,CAAAtB,WAAA,kBAAAsB,GAAA,CAAAtB,WAAA,CAAAC,KAAA,kBAAAqB,GAAA,CAAAtB,WAAA,CAAAC,KAAA,CAAAF,IAAA,MACxB;QACuBlD,EAAA,CAAAK,SAAA,GACzB;QADyBL,EAAA,CAAAkB,kBAAA,MAAAuD,GAAA,CAAAtB,WAAA,kBAAAsB,GAAA,CAAAtB,WAAA,CAAAD,IAAA,MACzB;QAMElD,EAAA,CAAAK,SAAA,GAAgD;QAAhDL,EAAA,CAAAM,UAAA,QAAAmE,GAAA,CAAA9D,eAAA,CAAA8D,GAAA,CAAAtB,WAAA,kBAAAsB,GAAA,CAAAtB,WAAA,CAAAuE,IAAA,kBAAAjD,GAAA,CAAAtB,WAAA,CAAAuE,IAAA,CAAAC,IAAA,GAAA3H,EAAA,CAAAa,aAAA,CAAgD;QAYhCb,EAAA,CAAAK,SAAA,GAAc;QAAdL,EAAA,CAAAM,UAAA,YAAAmE,GAAA,CAAAvC,WAAA,CAAc", "names": ["EventEmitter", "jsPDF", "AppConfig", "moment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "item_r3", "player", "id", "ctx_r2", "replaceImageURL", "photo", "ɵɵsanitizeUrl", "ɵɵtextInterpolate2", "user", "first_name", "last_name", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "dob", "ɵɵtemplate", "TeamsheetTemplateComponent_tr_51_td_1_Template", "items_r1", "TeamsheetTemplateComponent", "constructor", "_teamsheetService", "today", "Date", "format", "APP_NAME", "onSubmitted", "console", "log", "team_player", "ngOnInit", "subscribe", "data", "getTeamSheet2Submit", "setTimeout", "submitTeamSheet", "html", "document", "getElementById", "innerHTML", "doc", "font", "FontFace", "file_name", "season", "name", "currentTeam", "group", "load", "then", "fonts", "add", "addFont", "setFont", "callback", "save", "x", "y", "html2canvas", "scale", "convertImageToBase64", "imgUrl", "image", "Image", "crossOrigin", "onload", "canvas", "createElement", "ctx", "getContext", "height", "naturalHeight", "width", "naturalWidth", "drawImage", "dataUrl", "toDataURL", "src", "replaceImage", "url", "image_id", "replace", "setAttribute", "ngAfterViewInit", "team_id", "to<PERSON>romise", "res", "team_players", "team", "Object", "keys", "map", "key", "reduce", "resultArray", "item", "index", "chunkIndex", "Math", "floor", "push", "catch", "err", "_", "ɵɵdirectiveInject", "i1", "TeamsheetService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "TeamsheetTemplateComponent_Template", "rf", "TeamsheetTemplateComponent_tr_51_Template", "club", "logo"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\teamsheet-template\\teamsheet-template.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\teamsheet-template\\teamsheet-template.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  Input,\r\n  OnInit,\r\n  EventEmitter,\r\n  Output,\r\n} from '@angular/core';\r\nimport jsPDF from 'jspdf';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { TeamsheetService } from 'app/services/teamsheet.service';\r\nimport moment from 'moment';\r\n\r\n@Component({\r\n  selector: 'app-teamsheet-template',\r\n  templateUrl: './teamsheet-template.component.html',\r\n  styleUrls: ['./teamsheet-template.component.scss'],\r\n})\r\nexport class TeamsheetTemplateComponent implements AfterViewInit, OnInit {\r\n  today = moment(new Date()).format('DD-MMM-YYYY');\r\n\r\n  APP_NAME = AppConfig.APP_NAME;\r\n  @Input() team_id: any;\r\n  @Input() season: any;\r\n  @Input() currentTeam: any;\r\n  @Input() team_player: any[];\r\n  @Input() onSubmitted: EventEmitter<any> = new EventEmitter<any>();\r\n  constructor(public _teamsheetService: TeamsheetService) {\r\n    console.log(this.team_player);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // this.getTeamSheet2Submit();\r\n    this.onSubmitted.subscribe((data) => {\r\n      this.getTeamSheet2Submit();\r\n      setTimeout(() => {\r\n        this.submitTeamSheet();\r\n      }, 1000);\r\n    });\r\n  }\r\n\r\n  submitTeamSheet() {\r\n    // export to pdf\r\n    let html = document.getElementById('team-sheet').innerHTML;\r\n    let doc = new jsPDF('p', 'px', 'a4');\r\n    let font = new FontFace('SimSun', 'url(assets/fonts/pdf/SIMSUN.ttf)');\r\n    let file_name = `Team Sheet-${this.season.name}-${this.currentTeam.group.name}-${this.currentTeam.name}(${this.today}).pdf`;\r\n    font.load().then(function () {\r\n      // set font for div content\r\n      (document.fonts as any).add(font);\r\n      doc.addFont('assets/fonts/pdf/SIMSUN.ttf', 'SimSun', 'normal');\r\n      doc.setFont('SimSun');\r\n      doc.html(html, {\r\n        callback: (doc) => {\r\n          doc.save(file_name);\r\n        },\r\n        x: 10,\r\n        y: 10,\r\n        html2canvas: {\r\n          scale: 1,\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  convertImageToBase64(imgUrl, callback) {\r\n    const image = new Image();\r\n    image.crossOrigin = 'anonymous';\r\n    image.onload = () => {\r\n      // console.log(image);\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      canvas.height = image.naturalHeight;\r\n      canvas.width = image.naturalWidth;\r\n      ctx.drawImage(image, 0, 0);\r\n      const dataUrl = canvas.toDataURL();\r\n      callback && callback(dataUrl);\r\n    };\r\n    image.src = imgUrl;\r\n  }\r\n\r\n  replaceImage(url, image_id) {\r\n    url = url.replace('https://app.hkjfl.com/hkjfl', '/hkjfl');\r\n    this.convertImageToBase64(url, (dataUrl) => {\r\n      document.getElementById(image_id).setAttribute('src', dataUrl);\r\n    });\r\n  }\r\n\r\n  replaceImageURL(url) {\r\n    if (!url) return url;\r\n    url = url.replace('https://app.hkjfl.com/hkjfl', '/hkjfl');\r\n    return url;\r\n  }\r\n  ngAfterViewInit(): void {\r\n    // if (this.currentTeam?.club?.logo) {\r\n    //   this.replaceImage(this.currentTeam.club.logo, 'club_logo');\r\n    // }\r\n  }\r\n\r\n  getTeamSheet2Submit() {\r\n    this._teamsheetService\r\n      .getTeamSheet2Submit(this.team_id)\r\n      .toPromise()\r\n      .then((res) => {\r\n        console.log('team sheet', res);\r\n        this.team_player = res.team_players;\r\n        this.currentTeam = res.team;\r\n        this.season = res.season;\r\n        // team_player to array\r\n        this.team_player = Object.keys(this.team_player).map((key) => {\r\n          return this.team_player[key];\r\n        });\r\n        this.team_player = this.team_player.reduce(\r\n          (resultArray, item, index) => {\r\n            const chunkIndex = Math.floor(index / 4);\r\n            if (!resultArray[chunkIndex]) {\r\n              resultArray[chunkIndex] = []; // start a new chunk\r\n            }\r\n            resultArray[chunkIndex].push(item);\r\n\r\n            return resultArray;\r\n          },\r\n          []\r\n        );\r\n        console.log(this.team_player);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }\r\n}\r\n", "<!-- <button (click)=\"submitTeamSheet()\"></button> -->\r\n<div style=\"display: none\">\r\n  <div class=\"modal-header\">\r\n    <h4 class=\"modal-title\" id=\"myModalLabel1\">Preview Team Sheet</h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <div class=\"row justify-content-center\">\r\n      <div id=\"team-sheet\">\r\n        <!DOCTYPE html>\r\n        <html lang=\"en\">\r\n          <head>\r\n            <meta\r\n              http-equiv=\"Content-Type\"\r\n              content=\"text/html; charset=utf-8\"\r\n            />\r\n            <meta charset=\"utf-8\" />\r\n            <meta\r\n              name=\"viewport\"\r\n              content=\"width=device-width, initial-scale=1.0\"\r\n            />\r\n            <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\" />\r\n            <title>Team Sheet</title>\r\n          </head>\r\n\r\n          <body>\r\n            <div style=\"width: 425px\">\r\n              <table\r\n                style=\"\r\n                  width: 425px;\r\n                  border: 0;\r\n                  font-family: Arial;\r\n                  color: black !important;\r\n                \"\r\n              >\r\n                <br />\r\n                <tbody style=\"font-size: 12px\">\r\n                  <tr>\r\n                    <td class=\"p-0\">\r\n                      <img\r\n                        src=\"assets/images/logo/ezactive_1024x1024.png\"\r\n                        width=\"50px\"\r\n                      />\r\n                    </td>\r\n                    <td\r\n                      class=\"p-0\"\r\n                      style=\"width: 150px; vertical-align: middle\"\r\n                    >\r\n                      <div class=\"text-left\">\r\n                        <strong style=\"font-weight: 700\"\r\n                          >{{ APP_NAME }} - {{ season?.name }}</strong\r\n                        >\r\n                        <br />\r\n                        Submitted: {{ today | date : 'yyyy-MMM-dd' }}\r\n                      </div>\r\n                    </td>\r\n                    <td class=\"p-0\">\r\n                      <div class=\"text-left\">\r\n                        <strong>Club:</strong> {{ currentTeam?.club?.name }}\r\n                        <br />\r\n                        <strong>Group:</strong> {{ currentTeam?.group?.name }}\r\n                        <br />\r\n                        <strong>Team:</strong> {{ currentTeam?.name }}\r\n                      </div>\r\n                    </td>\r\n                    <td class=\"p-0\">\r\n                      <img\r\n                        id=\"club_logo\"\r\n                        width=\"50px\"\r\n                        [src]=\"replaceImageURL(currentTeam?.club?.logo)\"\r\n                      />\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n              <hr />\r\n\r\n              <!-- <h2 class=\"text-center mb-3\"></h2> -->\r\n\r\n              <table class=\"table table-bordered\" style=\"width: 425px\">\r\n                <tbody>\r\n                  <tr *ngFor=\"let items of team_player\">\r\n                    <td\r\n                      class=\"p-0\"\r\n                      *ngFor=\"let item of items\"\r\n                      style=\"font-size: 10px; width: 106px\"\r\n                    >\r\n                      <div class=\"text-center\">\r\n                        <img\r\n                          class=\"mt-1\"\r\n                          style=\"margin: 0 auto\"\r\n                          [id]=\"'photo_' + item.player.id\"\r\n                          width=\"80px\"\r\n                          height=\"80px\"\r\n                          object-fit=\"cover\"\r\n                          [src]=\"replaceImageURL(item.player.photo)\"\r\n                        />\r\n                        <div\r\n                          class=\"text-center\"\r\n                          style=\"\r\n                            font-size: 10px;\r\n                            padding: 2px;\r\n                            font-family: SimSun;\r\n                          \"\r\n                        >\r\n                          {{ item.player.user.first_name }}\r\n                          {{ item.player.user.last_name }}\r\n                          <br />\r\n                          {{ item.player.dob | date : 'yyyy' }}\r\n                        </div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </body>\r\n        </html>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"app/services/email-template.service\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"app/layout/components/content-header/content-header.component\";\nimport * as i8 from \"@ng-select/ng-select\";\nimport * as i9 from \"@ngx-formly/core\";\nfunction EmailTemplatesComponent_ng_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const template_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", template_r2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 2, template_r2.name), \" \");\n  }\n}\nfunction EmailTemplatesComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesComponent_div_24_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const variable_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.insertVariable(variable_r3.key));\n    });\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"div\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const variable_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", \"{{\" + variable_r3.key + \"}}\", \" \");\n  }\n}\nexport class EmailTemplatesComponent {\n  constructor(_trans, emailTemplateService, _loading, _toast) {\n    this._trans = _trans;\n    this.emailTemplateService = emailTemplateService;\n    this._loading = _loading;\n    this._toast = _toast;\n    this.form = new FormGroup({});\n    this.model = {\n      subject: '',\n      body: '',\n      isActive: true\n    };\n    this.variables = [];\n    this.fields = [{\n      key: 'subject',\n      type: 'input',\n      templateOptions: {\n        label: 'Subject',\n        placeholder: 'Enter email subject',\n        required: true,\n        maxLength: 150\n      }\n    }, {\n      key: 'body',\n      type: 'ckeditor5',\n      props: {\n        required: true,\n        translate: true,\n        label: this._trans.instant('Content'),\n        style: {\n          'min-height': '500px'\n        },\n        config: {\n          toolbar: ['heading', '|', 'bold', 'underline', 'italic', 'link', '|', 'fontSize', 'fontColor', 'fontBackgroundColor', 'fontFamily', 'specialCharacters', 'removeFormat', 'findAndReplace', 'bulletedList', 'numberedList', 'todoList', '|', 'undo', 'redo', '|', 'outdent', 'indent', 'alignment', '|', 'imageInsert', 'blockQuote', 'insertTable'],\n          placeholder: 'Type the content here',\n          htmlSupport: {\n            allow: [{\n              name: /.*/,\n              attributes: false,\n              classes: false,\n              styles: false\n            }]\n          },\n          htmlEmbed: {\n            showPreviews: true\n          },\n          mention: {\n            feeds: [{\n              marker: '{',\n              feed: queryText => {\n                if (queryText.includes(' ')) return [];\n                return this.variables.map(v => ({\n                  id: `{{${v.key}}}`,\n                  name: v.description\n                }));\n              },\n              itemRenderer: item => {\n                const domItem = document.createElement('div');\n                domItem.textContent = `${item.name} - ${item.id}`;\n                return domItem;\n              },\n              minimumCharacters: 1\n            }],\n            renderer: item => {\n              return item.name;\n            }\n          }\n        }\n      }\n    }\n    // {\n    //   key: 'isActive',\n    //   type: 'checkbox',\n    //   templateOptions: {\n    //     label: 'Is Active',\n    //   },\n    // },\n    ];\n\n    this.EmailTemplates = [];\n    this.template = null;\n    this.contentHeader = {\n      headerTitle: 'Email Templates',\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Settings',\n          link: '/settings'\n        }, {\n          name: 'Email Templates',\n          link: '/settings/email-templates'\n        }]\n      }\n    };\n  }\n  ngOnInit() {\n    this.loadTemplates();\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      this._toast.success('Copied to clipboard');\n    });\n  }\n  insertVariable(variable) {\n    const insertFn = this.fields.find(f => f.key === 'body')?.props?.insertVariable;\n    if (typeof insertFn === 'function') {\n      insertFn(`{{${variable}}}`);\n    } else {\n      this._toast.warning('Editor not ready');\n    }\n  }\n  loadTemplates() {\n    this._loading.show();\n    this.emailTemplateService.getTemplates().subscribe({\n      next: templates => {\n        this.EmailTemplates = templates;\n        if (this.template) {\n          this.template = this.EmailTemplates.find(t => t.id === this.template?.id) || null;\n          if (this.template) {\n            this.model.subject = this.template.subject;\n            this.model.body = this.template.content;\n            this.model.isActive = this.template.is_active;\n          }\n        }\n        this._loading.dismiss(); // Ẩn loading khi thành công\n      },\n\n      error: err => {\n        console.error('Failed to load templates', err);\n        this._loading.dismiss(); // Ẩn loading khi lỗi\n      }\n    });\n  }\n\n  onChangeEmail(selectedTemplate) {\n    this.template = selectedTemplate;\n    this.variables = selectedTemplate.variables || [];\n    this.model = {\n      subject: selectedTemplate.subject,\n      body: selectedTemplate.content,\n      isActive: selectedTemplate.is_active\n    };\n    this.form.patchValue(this.model);\n  }\n  onSubmit() {\n    if (this.form.valid && this.template) {\n      const payload = {\n        subject: this.model.subject,\n        content: this.model.body,\n        is_active: this.model.isActive\n      };\n      this.emailTemplateService.updateTemplate(this.template.id, payload).subscribe({\n        next: () => {\n          this._toast.success('Template updated successfully', 'Success'); // ✅ Toast success\n          this.loadTemplates();\n        },\n        error: error => {\n          console.error('Failed to update template', error);\n          this._toast.error('Failed to update template', 'Error'); // ✅ Toast error\n        }\n      });\n    } else {\n      this._toast.warning('Form is invalid or no template selected', 'Warning'); // ✅ Toast warning\n    }\n  }\n\n  ngOnDestroy() {}\n  static #_ = this.ɵfac = function EmailTemplatesComponent_Factory(t) {\n    return new (t || EmailTemplatesComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.EmailTemplateService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmailTemplatesComponent,\n    selectors: [[\"app-email-templates\"]],\n    decls: 25,\n    vars: 16,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\", \"mb-1\"], [1, \"card\", \"p-1\", \"col\"], [\"for\", \"template\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"card\", \"p-1\", \"col-md-8\"], [1, \"form\", \"form-vertical\", 3, \"formGroup\", \"ngSubmit\"], [3, \"form\", \"fields\", \"model\"], [1, \"d-flex\", \"justify-content-end\", \"mt-2\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"col-md-4\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-body\", \"p-0\"], [\"class\", \"border rounded mb-1 ml-1 mr-1 hover-border\", \"style\", \"padding: 0.5rem; cursor: pointer;\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"border\", \"rounded\", \"mb-1\", \"ml-1\", \"mr-1\", \"hover-border\", 2, \"padding\", \"0.5rem\", \"cursor\", \"pointer\", 3, \"click\"], [1, \"col-6\", \"text-primary\"]],\n    template: function EmailTemplatesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"ng-select\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function EmailTemplatesComponent_Template_ng_select_ngModelChange_8_listener($event) {\n          return ctx.template = $event;\n        })(\"change\", function EmailTemplatesComponent_Template_ng_select_change_8_listener($event) {\n          return ctx.onChangeEmail($event);\n        });\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵtemplate(10, EmailTemplatesComponent_ng_option_10_Template, 3, 4, \"ng-option\", 7);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"form\", 10);\n        i0.ɵɵlistener(\"ngSubmit\", function EmailTemplatesComponent_Template_form_ngSubmit_13_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelement(14, \"formly-form\", 11);\n        i0.ɵɵelementStart(15, \"div\", 12)(16, \"button\", 13);\n        i0.ɵɵtext(17, \"Save\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"div\", 16)(21, \"strong\");\n        i0.ɵɵtext(22, \"All Variables\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 17);\n        i0.ɵɵtemplate(24, EmailTemplatesComponent_div_24_Template, 4, 1, \"div\", 18);\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 12, \"Email Templates\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(9, 14, \"Select Template\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.template);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.EmailTemplates);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngForOf\", ctx.variables);\n      }\n    },\n    dependencies: [i5.NgForOf, i6.ɵNgNoValidate, i6.NgControlStatus, i6.NgControlStatusGroup, i6.NgModel, i6.FormGroupDirective, i7.ContentHeaderComponent, i8.NgSelectComponent, i8.ɵr, i9.FormlyForm, i1.TranslatePipe],\n    styles: [\".swal-cancel-btn[_ngcontent-%COMP%] {\\n  background: #a8aaae !important;\\n  color: #fff !important;\\n}\\n.swal-cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: #a8aaae !important;\\n  color: #fff !important;\\n}\\n\\n.hover-border[_ngcontent-%COMP%] {\\n  transition: border 0.2s ease;\\n}\\n\\n.hover-border[_ngcontent-%COMP%]:hover {\\n  background: #a8aaae !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2V0dGluZ3MvZW1haWwtdGVtcGxhdGVzL2VtYWlsLXRlbXBsYXRlcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNFLDhCQUFBO0VBQ0Esc0JBQUE7QUFBRjtBQUVFO0VBQ0UsOEJBQUE7RUFDQSxzQkFBQTtBQUFKOztBQUdBO0VBQ0UsNEJBQUE7QUFBRjs7QUFFQTtFQUNFLDhCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLnN3YWwtY2FuY2VsLWJ0biB7XHJcbiAgYmFja2dyb3VuZDogI2E4YWFhZSAhaW1wb3J0YW50O1xyXG4gIGNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZDogI2E4YWFhZSAhaW1wb3J0YW50O1xyXG4gICAgY29sb3I6ICNmZmYgIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuLmhvdmVyLWJvcmRlciB7XHJcbiAgdHJhbnNpdGlvbjogYm9yZGVyIDAuMnMgZWFzZTtcclxufVxyXG4uaG92ZXItYm9yZGVyOmhvdmVyIHtcclxuICBiYWNrZ3JvdW5kOiAjYThhYWFlICFpbXBvcnRhbnQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;ICUhCC,EAAA,CAAAC,cAAA,oBAAsE;IAAAD,EAAA,CAAAE,MAAA,GACtE;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADuCH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAACL,EAAA,CAAAM,SAAA,GACtE;IADsEN,EAAA,CAAAO,kBAAA,KAAAP,EAAA,CAAAQ,WAAA,OAAAH,WAAA,CAAAI,IAAA,OACtE;;;;;;IA2BET,EAAA,CAAAC,cAAA,cAKX;IADCD,EAAA,CAAAU,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAL,WAAA,CAAAM,GAAA,CAA4B;IAAA,EAAC;IAEtCrB,EAAA,CAAAC,cAAA,aAAiB;IAEbD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,aAAAQ,WAAA,CAAAM,GAAA,aACF;;;ADjCJ,OAAM,MAAOC,uBAAuB;EA6GlCC,YACSC,MAAwB,EACvBC,oBAA0C,EAC1CC,QAAwB,EACxBC,MAAqB;IAHtB,KAAAH,MAAM,GAANA,MAAM;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IA/GhB,KAAAC,IAAI,GAAG,IAAI7B,SAAS,CAAC,EAAE,CAAC;IACxB,KAAA8B,KAAK,GAAQ;MACXC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;KACX;IAED,KAAAC,SAAS,GAA2C,EAAE;IAEtD,KAAAC,MAAM,GAAwB,CAC5B;MACEb,GAAG,EAAE,SAAS;MACdc,IAAI,EAAE,OAAO;MACbC,eAAe,EAAE;QACfC,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,qBAAqB;QAClCC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;;KAEd,EACD;MACEnB,GAAG,EAAE,MAAM;MACXc,IAAI,EAAE,WAAW;MACjBM,KAAK,EAAE;QACLF,QAAQ,EAAE,IAAI;QACdG,SAAS,EAAE,IAAI;QACfL,KAAK,EAAE,IAAI,CAACb,MAAM,CAACmB,OAAO,CAAC,SAAS,CAAC;QACrCC,KAAK,EAAE;UACL,YAAY,EAAE;SACf;QACDC,MAAM,EAAE;UACNC,OAAO,EAAE,CACP,SAAS,EACT,GAAG,EACH,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,GAAG,EACH,UAAU,EACV,WAAW,EACX,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,UAAU,EACV,GAAG,EACH,MAAM,EACN,MAAM,EACN,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW,EACX,GAAG,EACH,aAAa,EACb,YAAY,EACZ,aAAa,CACd;UACDR,WAAW,EAAE,uBAAuB;UACpCS,WAAW,EAAE;YACXC,KAAK,EAAE,CACL;cAAEvC,IAAI,EAAE,IAAI;cAAEwC,UAAU,EAAE,KAAK;cAAEC,OAAO,EAAE,KAAK;cAAEC,MAAM,EAAE;YAAK,CAAE;WAEnE;UACDC,SAAS,EAAE;YAAEC,YAAY,EAAE;UAAI,CAAE;UACnCC,OAAO,EAAE;YACPC,KAAK,EAAE,CACL;cACEC,MAAM,EAAE,GAAG;cACXC,IAAI,EAAGC,SAAiB,IAAI;gBAC1B,IAAIA,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE;gBACtC,OAAO,IAAI,CAAC1B,SAAS,CAAC2B,GAAG,CAAEC,CAAC,KAAM;kBAChCC,EAAE,EAAE,KAAKD,CAAC,CAACxC,GAAG,IAAI;kBAClBZ,IAAI,EAAEoD,CAAC,CAACE;iBACT,CAAC,CAAC;cACL,CAAC;cACDC,YAAY,EAAGC,IAAI,IAAI;gBACrB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBAC7CF,OAAO,CAACG,WAAW,GAAG,GAAGJ,IAAI,CAACxD,IAAI,MAAMwD,IAAI,CAACH,EAAE,EAAE;gBACjD,OAAOI,OAAO;cAChB,CAAC;cACDI,iBAAiB,EAAE;aACpB,CACF;YACDC,QAAQ,EAAGN,IAAI,IAAI;cACjB,OAAOA,IAAI,CAACxD,IAAI;YAClB;;;;;IAMN;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,CACD;;IAED,KAAA+D,cAAc,GAAoB,EAAE;IACpC,KAAAC,QAAQ,GAAyB,IAAI;IAQnC,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,iBAAiB;MAC9BC,UAAU,EAAE;QACVzC,IAAI,EAAE,EAAE;QACR0C,KAAK,EAAE,CACL;UAAEpE,IAAI,EAAE,UAAU;UAAEqE,IAAI,EAAE;QAAW,CAAE,EACvC;UAAErE,IAAI,EAAE,iBAAiB;UAAEqE,IAAI,EAAE;QAA2B,CAAE;;KAGnE;EACH;EAEAC,QAAQA,CAAA;IAEN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,eAAeA,CAACC,IAAY;IAC1BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;MAC5C,IAAI,CAAC3D,MAAM,CAAC4D,OAAO,CAAC,qBAAqB,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEEnE,cAAcA,CAACoE,QAAgB;IAC/B,MAAMC,QAAQ,GAAG,IAAI,CAACvD,MAAM,CAACwD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtE,GAAG,KAAK,MAAM,CAAC,EAAEoB,KAAK,EAAErB,cAAc;IAC/E,IAAI,OAAOqE,QAAQ,KAAK,UAAU,EAAE;MAClCA,QAAQ,CAAC,KAAKD,QAAQ,IAAI,CAAC;KAC5B,MAAM;MACL,IAAI,CAAC7D,MAAM,CAACiE,OAAO,CAAC,kBAAkB,CAAC;;EAE3C;EAGAZ,aAAaA,CAAA;IACX,IAAI,CAACtD,QAAQ,CAACmE,IAAI,EAAE;IACpB,IAAI,CAACpE,oBAAoB,CAACqE,YAAY,EAAE,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,SAAS,IAAI;QAClB,IAAI,CAACzB,cAAc,GAAGyB,SAAS;QAE/B,IAAI,IAAI,CAACxB,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,GACX,IAAI,CAACD,cAAc,CAACkB,IAAI,CAAEQ,CAAC,IAAKA,CAAC,CAACpC,EAAE,KAAK,IAAI,CAACW,QAAQ,EAAEX,EAAE,CAAC,IAAI,IAAI;UAErE,IAAI,IAAI,CAACW,QAAQ,EAAE;YACjB,IAAI,CAAC5C,KAAK,CAACC,OAAO,GAAG,IAAI,CAAC2C,QAAQ,CAAC3C,OAAO;YAC1C,IAAI,CAACD,KAAK,CAACE,IAAI,GAAG,IAAI,CAAC0C,QAAQ,CAAC0B,OAAO;YACvC,IAAI,CAACtE,KAAK,CAACG,QAAQ,GAAG,IAAI,CAACyC,QAAQ,CAAC2B,SAAS;;;QAIjD,IAAI,CAAC1E,QAAQ,CAAC2E,OAAO,EAAE,CAAC,CAAC;MAC3B,CAAC;;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;QAC9C,IAAI,CAAC7E,QAAQ,CAAC2E,OAAO,EAAE,CAAC,CAAC;MAC3B;KACD,CAAC;EACJ;;EAEAI,aAAaA,CAACC,gBAA+B;IAC3C,IAAI,CAACjC,QAAQ,GAAGiC,gBAAgB;IAChC,IAAI,CAACzE,SAAS,GAAGyE,gBAAgB,CAACzE,SAAS,IAAI,EAAE;IAEjD,IAAI,CAACJ,KAAK,GAAG;MACXC,OAAO,EAAE4E,gBAAgB,CAAC5E,OAAO;MACjCC,IAAI,EAAE2E,gBAAgB,CAACP,OAAO;MAC9BnE,QAAQ,EAAE0E,gBAAgB,CAACN;KAC5B;IAED,IAAI,CAACxE,IAAI,CAAC+E,UAAU,CAAC,IAAI,CAAC9E,KAAK,CAAC;EAClC;EAEA+E,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChF,IAAI,CAACiF,KAAK,IAAI,IAAI,CAACpC,QAAQ,EAAE;MACpC,MAAMqC,OAAO,GAAG;QACdhF,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAO;QAC3BqE,OAAO,EAAE,IAAI,CAACtE,KAAK,CAACE,IAAI;QACxBqE,SAAS,EAAE,IAAI,CAACvE,KAAK,CAACG;OACvB;MAED,IAAI,CAACP,oBAAoB,CACtBsF,cAAc,CAAC,IAAI,CAACtC,QAAQ,CAACX,EAAE,EAAEgD,OAAO,CAAC,CACzCf,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACrE,MAAM,CAAC4D,OAAO,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,CAAC;UACjE,IAAI,CAACP,aAAa,EAAE;QACtB,CAAC;QACDsB,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAAC3E,MAAM,CAAC2E,KAAK,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,CAAC;QAC3D;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAAC3E,MAAM,CAACiE,OAAO,CAAC,yCAAyC,EAAE,SAAS,CAAC,CAAC,CAAC;;EAE/E;;EAEAoB,WAAWA,CAAA,GAAU;EAAC,QAAAC,CAAA;qBApNX3F,uBAAuB,EAAAtB,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAtH,EAAA,CAAAkH,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxH,EAAA,CAAAkH,iBAAA,CAAAO,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAAvBrG,uBAAuB;IAAAsG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAtD,QAAA,WAAAuD,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCfpCjI,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAmI,SAAA,4BAAyE;QAEzEnI,EAAA,CAAAC,cAAA,aAAsB;QAGID,EAAA,CAAAE,MAAA,GAAmC;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjEH,EAAA,CAAAC,cAAA,mBAC0D;QAAxDD,EAAA,CAAAU,UAAA,2BAAA0H,oEAAAC,MAAA;UAAA,OAAAH,GAAA,CAAAzD,QAAA,GAAA4D,MAAA;QAAA,EAAsB,oBAAAC,6DAAAD,MAAA;UAAA,OAAWH,GAAA,CAAAzB,aAAA,CAAA4B,MAAA,CAAqB;QAAA,EAAhC;;QACtBrI,EAAA,CAAAuI,UAAA,KAAAC,6CAAA,uBACY;QACdxI,EAAA,CAAAG,YAAA,EAAY;QAMhBH,EAAA,CAAAC,cAAA,cAAiB;QAGYD,EAAA,CAAAU,UAAA,sBAAA+H,2DAAA;UAAA,OAAYP,GAAA,CAAAtB,QAAA,EAAU;QAAA,EAAC;QAC9C5G,EAAA,CAAAmI,SAAA,uBAA2E;QAC3EnI,EAAA,CAAAC,cAAA,eAA6C;QACGD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAMjEH,EAAA,CAAAC,cAAA,eAAsB;QAGRD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEhCH,EAAA,CAAAC,cAAA,eAA2B;QAGzBD,EAAA,CAAAuI,UAAA,KAAAG,uCAAA,kBAWN;QAEI1I,EAAA,CAAAG,YAAA,EAAM;;;QAjDQH,EAAA,CAAAM,SAAA,GAA+B;QAA/BN,EAAA,CAAAI,UAAA,kBAAA8H,GAAA,CAAAxD,aAAA,CAA+B;QAKzB1E,EAAA,CAAAM,SAAA,GAAmC;QAAnCN,EAAA,CAAA2I,iBAAA,CAAA3I,EAAA,CAAAQ,WAAA,2BAAmC;QACNR,EAAA,CAAAM,SAAA,GAAiD;QAAjDN,EAAA,CAAA4I,qBAAA,gBAAA5I,EAAA,CAAAQ,WAAA,2BAAiD;QAAzFR,EAAA,CAAAI,UAAA,oBAAmB,gCAAA8H,GAAA,CAAAzD,QAAA;QAEIzE,EAAA,CAAAM,SAAA,GAAiB;QAAjBN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAA1D,cAAA,CAAiB;QAW7CxE,EAAA,CAAAM,SAAA,GAAkB;QAAlBN,EAAA,CAAAI,UAAA,cAAA8H,GAAA,CAAAtG,IAAA,CAAkB;QACT5B,EAAA,CAAAM,SAAA,GAAa;QAAbN,EAAA,CAAAI,UAAA,SAAA8H,GAAA,CAAAtG,IAAA,CAAa,WAAAsG,GAAA,CAAAhG,MAAA,WAAAgG,GAAA,CAAArG,KAAA;QAmBb7B,EAAA,CAAAM,SAAA,IAAY;QAAZN,EAAA,CAAAI,UAAA,YAAA8H,GAAA,CAAAjG,SAAA,CAAY", "names": ["FormGroup", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "template_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "ɵɵlistener", "EmailTemplatesComponent_div_24_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r5", "variable_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "insertVariable", "key", "EmailTemplatesComponent", "constructor", "_trans", "emailTemplateService", "_loading", "_toast", "form", "model", "subject", "body", "isActive", "variables", "fields", "type", "templateOptions", "label", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "props", "translate", "instant", "style", "config", "toolbar", "htmlSupport", "allow", "attributes", "classes", "styles", "htmlEmbed", "showPreviews", "mention", "feeds", "marker", "feed", "queryText", "includes", "map", "v", "id", "description", "itemRenderer", "item", "domItem", "document", "createElement", "textContent", "minimumCharacters", "renderer", "EmailTemplates", "template", "contentHeader", "headerTitle", "breadcrumb", "links", "link", "ngOnInit", "loadTemplates", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "then", "success", "variable", "insertFn", "find", "f", "warning", "show", "getTemplates", "subscribe", "next", "templates", "t", "content", "is_active", "dismiss", "error", "err", "console", "onChangeEmail", "selectedTemplate", "patchValue", "onSubmit", "valid", "payload", "updateTemplate", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "EmailTemplateService", "i3", "LoadingService", "i4", "ToastrService", "_2", "selectors", "decls", "vars", "consts", "EmailTemplatesComponent_Template", "rf", "ctx", "ɵɵelement", "EmailTemplatesComponent_Template_ng_select_ngModelChange_8_listener", "$event", "EmailTemplatesComponent_Template_ng_select_change_8_listener", "ɵɵtemplate", "EmailTemplatesComponent_ng_option_10_Template", "EmailTemplatesComponent_Template_form_ngSubmit_13_listener", "EmailTemplatesComponent_div_24_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\email-templates\\email-templates.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\email-templates\\email-templates.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { EmailTemplate, EmailTemplateService } from 'app/services/email-template.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { render } from 'preact/compat';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-email-templates',\r\n  templateUrl: './email-templates.component.html',\r\n  styleUrls: ['./email-templates.component.scss'],\r\n})\r\nexport class EmailTemplatesComponent implements OnInit {\r\n  public contentHeader: object;\r\n  form = new FormGroup({});\r\n  model: any = {\r\n    subject: '',\r\n    body: '',\r\n    isActive: true,\r\n  };\r\n\r\n  variables: { key: string; description: string }[] = [];\r\n\r\n  fields: FormlyFieldConfig[] = [\r\n    {\r\n      key: 'subject',\r\n      type: 'input',\r\n      templateOptions: {\r\n        label: 'Subject',\r\n        placeholder: 'Enter email subject',\r\n        required: true,\r\n        maxLength: 150\r\n      }      \r\n    },\r\n    {\r\n      key: 'body',\r\n      type: 'ckeditor5',\r\n      props: {\r\n        required: true,\r\n        translate: true,\r\n        label: this._trans.instant('Content'),\r\n        style: {\r\n          'min-height': '500px',\r\n        },\r\n        config: {\r\n          toolbar: [\r\n            'heading',\r\n            '|',\r\n            'bold',\r\n            'underline',\r\n            'italic',\r\n            'link',\r\n            '|',\r\n            'fontSize',\r\n            'fontColor',\r\n            'fontBackgroundColor',\r\n            'fontFamily',\r\n            'specialCharacters',\r\n            'removeFormat',\r\n            'findAndReplace',\r\n            'bulletedList',\r\n            'numberedList',\r\n            'todoList',\r\n            '|',\r\n            'undo',\r\n            'redo',\r\n            '|',\r\n            'outdent',\r\n            'indent',\r\n            'alignment',\r\n            '|',\r\n            'imageInsert',\r\n            'blockQuote',\r\n            'insertTable',\r\n          ],\r\n          placeholder: 'Type the content here',\r\n          htmlSupport: {\r\n            allow: [\r\n              { name: /.*/, attributes: false, classes: false, styles: false },\r\n            ],\r\n          },\r\n          htmlEmbed: { showPreviews: true },\r\n        mention: {\r\n          feeds: [\r\n            {\r\n              marker: '{',\r\n              feed: (queryText: string) => {\r\n                if (queryText.includes(' ')) return [];\r\n                return this.variables.map((v) => ({\r\n                  id: `{{${v.key}}}`,\r\n                  name: v.description,\r\n                }));\r\n              },\r\n              itemRenderer: (item) => {\r\n                const domItem = document.createElement('div');\r\n                domItem.textContent = `${item.name} - ${item.id}`;\r\n                return domItem;\r\n              },\r\n              minimumCharacters: 1,\r\n            },\r\n          ],\r\n          renderer: (item) => {\r\n            return item.name;\r\n          }\r\n        }\r\n\r\n        },\r\n      },\r\n    },\r\n    // {\r\n    //   key: 'isActive',\r\n    //   type: 'checkbox',\r\n    //   templateOptions: {\r\n    //     label: 'Is Active',\r\n    //   },\r\n    // },\r\n  ];\r\n\r\n  EmailTemplates: EmailTemplate[] = [];\r\n  template: EmailTemplate | null = null;\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    private emailTemplateService: EmailTemplateService,\r\n    private _loading: LoadingService,\r\n    private _toast: ToastrService\r\n  ) {\r\n    this.contentHeader = {\r\n      headerTitle: 'Email Templates',\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          { name: 'Settings', link: '/settings' },\r\n          { name: 'Email Templates', link: '/settings/email-templates' },\r\n        ],\r\n      },\r\n    };\r\n  }\r\n\r\n  ngOnInit(): void {\r\n\r\n    this.loadTemplates();\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text).then(() => {\r\n      this._toast.success('Copied to clipboard');\r\n    });\r\n  }\r\n\r\n    insertVariable(variable: string): void {\r\n    const insertFn = this.fields.find(f => f.key === 'body')?.props?.insertVariable;\r\n    if (typeof insertFn === 'function') {\r\n      insertFn(`{{${variable}}}`);\r\n    } else {\r\n      this._toast.warning('Editor not ready');\r\n    }\r\n  }\r\n\r\n\r\n  loadTemplates(): void {\r\n    this._loading.show();\r\n    this.emailTemplateService.getTemplates().subscribe({\r\n      next: (templates) => {\r\n        this.EmailTemplates = templates;\r\n\r\n        if (this.template) {\r\n          this.template =\r\n            this.EmailTemplates.find((t) => t.id === this.template?.id) || null;\r\n\r\n          if (this.template) {\r\n            this.model.subject = this.template.subject;\r\n            this.model.body = this.template.content;\r\n            this.model.isActive = this.template.is_active;\r\n          }\r\n        }\r\n\r\n        this._loading.dismiss(); // Ẩn loading khi thành công\r\n      },\r\n      error: (err) => {\r\n        console.error('Failed to load templates', err);\r\n        this._loading.dismiss(); // Ẩn loading khi lỗi\r\n      },\r\n    });\r\n  }\r\n\r\n  onChangeEmail(selectedTemplate: EmailTemplate): void {\r\n    this.template = selectedTemplate;\r\n    this.variables = selectedTemplate.variables || [];\r\n\r\n    this.model = {\r\n      subject: selectedTemplate.subject,\r\n      body: selectedTemplate.content,\r\n      isActive: selectedTemplate.is_active,\r\n    };\r\n\r\n    this.form.patchValue(this.model);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.form.valid && this.template) {\r\n      const payload = {\r\n        subject: this.model.subject,\r\n        content: this.model.body,\r\n        is_active: this.model.isActive,\r\n      };\r\n\r\n      this.emailTemplateService\r\n        .updateTemplate(this.template.id, payload)\r\n        .subscribe({\r\n          next: () => {\r\n            this._toast.success('Template updated successfully', 'Success'); // ✅ Toast success\r\n            this.loadTemplates();\r\n          },\r\n          error: (error) => {\r\n            console.error('Failed to update template', error);\r\n            this._toast.error('Failed to update template', 'Error'); // ✅ Toast error\r\n          },\r\n        });\r\n    } else {\r\n      this._toast.warning('Form is invalid or no template selected', 'Warning'); // ✅ Toast warning\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {}\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <div class=\"row mb-1\">\r\n      <!-- ng select season -->\r\n      <div class=\"card p-1 col\">\r\n        <label for=\"template\">{{ 'Email Templates' | translate }}</label>\r\n        <ng-select [searchable]=\"true\" [clearable]=\"false\" placeholder=\"{{ 'Select Template' | translate }}\"\r\n          [(ngModel)]=\"template\" (change)=\"onChangeEmail($event)\">\r\n          <ng-option *ngFor=\"let template of EmailTemplates\" [value]=\"template\">{{ template.name | translate }}\r\n          </ng-option>\r\n        </ng-select>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Formly Form -->\r\n\r\n    <div class=\"row\">\r\n      <!-- Cột form (chiếm 8/12) -->\r\n      <div class=\"card p-1 col-md-8\">\r\n        <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" class=\"form form-vertical\">\r\n          <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n          <div class=\"d-flex justify-content-end mt-2\">\r\n            <button type=\"submit\" class=\"btn btn-primary\">Save</button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <!-- Cột bảng biến (chiếm 4/12) -->\r\n      <div class=\"col-md-4\">\r\n        <div class=\"card\">\r\n          <div class=\"card-header\">\r\n            <strong>All Variables</strong>\r\n          </div>\r\n          <div class=\"card-body p-0\">\r\n            \r\n           <!-- border div *ngFor=\"let variable of variables\"  -->\r\n            <div\r\n  class=\"border rounded mb-1 ml-1 mr-1 hover-border\"\r\n  style=\"padding: 0.5rem; cursor: pointer;\"\r\n  *ngFor=\"let variable of variables\"\r\n  (click)=\"insertVariable(variable.key)\"\r\n>\r\n  <div class=\"row\">\r\n    <div class=\"col-6 text-primary\">\r\n      {{ '{{' + variable.key + '}}' }}\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
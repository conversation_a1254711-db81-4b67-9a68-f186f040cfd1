{"ast": null, "code": "import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { map } from '../operators/map';\nimport { canReportError } from '../util/canReportError';\nimport { isArray } from '../util/isArray';\nimport { isScheduler } from '../util/isScheduler';\nexport function bindCallback(callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return (...args) => bindCallback(callbackFunc, scheduler)(...args).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n  }\n  return function (...args) {\n    const context = this;\n    let subject;\n    const params = {\n      context,\n      subject,\n      callbackFunc,\n      scheduler\n    };\n    return new Observable(subscriber => {\n      if (!scheduler) {\n        if (!subject) {\n          subject = new AsyncSubject();\n          const handler = (...innerArgs) => {\n            subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n            subject.complete();\n          };\n          try {\n            callbackFunc.apply(context, [...args, handler]);\n          } catch (err) {\n            if (canReportError(subject)) {\n              subject.error(err);\n            } else {\n              console.warn(err);\n            }\n          }\n        }\n        return subject.subscribe(subscriber);\n      } else {\n        const state = {\n          args,\n          subscriber,\n          params\n        };\n        return scheduler.schedule(dispatch, 0, state);\n      }\n    });\n  };\n}\nfunction dispatch(state) {\n  const self = this;\n  const {\n    args,\n    subscriber,\n    params\n  } = state;\n  const {\n    callbackFunc,\n    context,\n    scheduler\n  } = params;\n  let {\n    subject\n  } = params;\n  if (!subject) {\n    subject = params.subject = new AsyncSubject();\n    const handler = (...innerArgs) => {\n      const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n      this.add(scheduler.schedule(dispatchNext, 0, {\n        value,\n        subject\n      }));\n    };\n    try {\n      callbackFunc.apply(context, [...args, handler]);\n    } catch (err) {\n      subject.error(err);\n    }\n  }\n  this.add(subject.subscribe(subscriber));\n}\nfunction dispatchNext(state) {\n  const {\n    value,\n    subject\n  } = state;\n  subject.next(value);\n  subject.complete();\n}\nfunction dispatchError(state) {\n  const {\n    err,\n    subject\n  } = state;\n  subject.error(err);\n}", "map": {"version": 3, "names": ["Observable", "AsyncSubject", "map", "canReportError", "isArray", "isScheduler", "bind<PERSON>allback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "args", "pipe", "context", "subject", "params", "subscriber", "handler", "innerArgs", "next", "length", "complete", "apply", "err", "error", "console", "warn", "subscribe", "state", "schedule", "dispatch", "self", "value", "add", "dispatchNext", "dispatchError"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/bindCallback.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { map } from '../operators/map';\nimport { canReportError } from '../util/canReportError';\nimport { isArray } from '../util/isArray';\nimport { isScheduler } from '../util/isScheduler';\nexport function bindCallback(callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return (...args) => bindCallback(callbackFunc, scheduler)(...args).pipe(map((args) => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n        }\n    }\n    return function (...args) {\n        const context = this;\n        let subject;\n        const params = {\n            context,\n            subject,\n            callbackFunc,\n            scheduler,\n        };\n        return new Observable(subscriber => {\n            if (!scheduler) {\n                if (!subject) {\n                    subject = new AsyncSubject();\n                    const handler = (...innerArgs) => {\n                        subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n                        subject.complete();\n                    };\n                    try {\n                        callbackFunc.apply(context, [...args, handler]);\n                    }\n                    catch (err) {\n                        if (canReportError(subject)) {\n                            subject.error(err);\n                        }\n                        else {\n                            console.warn(err);\n                        }\n                    }\n                }\n                return subject.subscribe(subscriber);\n            }\n            else {\n                const state = {\n                    args, subscriber, params,\n                };\n                return scheduler.schedule(dispatch, 0, state);\n            }\n        });\n    };\n}\nfunction dispatch(state) {\n    const self = this;\n    const { args, subscriber, params } = state;\n    const { callbackFunc, context, scheduler } = params;\n    let { subject } = params;\n    if (!subject) {\n        subject = params.subject = new AsyncSubject();\n        const handler = (...innerArgs) => {\n            const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n            this.add(scheduler.schedule(dispatchNext, 0, { value, subject }));\n        };\n        try {\n            callbackFunc.apply(context, [...args, handler]);\n        }\n        catch (err) {\n            subject.error(err);\n        }\n    }\n    this.add(subject.subscribe(subscriber));\n}\nfunction dispatchNext(state) {\n    const { value, subject } = state;\n    subject.next(value);\n    subject.complete();\n}\nfunction dispatchError(state) {\n    const { err, subject } = state;\n    subject.error(err);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,SAASC,YAAYA,CAACC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EAClE,IAAID,cAAc,EAAE;IAChB,IAAIH,WAAW,CAACG,cAAc,CAAC,EAAE;MAC7BC,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI;MACD,OAAO,CAAC,GAAGE,IAAI,KAAKJ,YAAY,CAACC,YAAY,EAAEE,SAAS,CAAC,CAAC,GAAGC,IAAI,CAAC,CAACC,IAAI,CAACT,GAAG,CAAEQ,IAAI,IAAKN,OAAO,CAACM,IAAI,CAAC,GAAGF,cAAc,CAAC,GAAGE,IAAI,CAAC,GAAGF,cAAc,CAACE,IAAI,CAAC,CAAC,CAAC;IAC1J;EACJ;EACA,OAAO,UAAU,GAAGA,IAAI,EAAE;IACtB,MAAME,OAAO,GAAG,IAAI;IACpB,IAAIC,OAAO;IACX,MAAMC,MAAM,GAAG;MACXF,OAAO;MACPC,OAAO;MACPN,YAAY;MACZE;IACJ,CAAC;IACD,OAAO,IAAIT,UAAU,CAACe,UAAU,IAAI;MAChC,IAAI,CAACN,SAAS,EAAE;QACZ,IAAI,CAACI,OAAO,EAAE;UACVA,OAAO,GAAG,IAAIZ,YAAY,CAAC,CAAC;UAC5B,MAAMe,OAAO,GAAGA,CAAC,GAAGC,SAAS,KAAK;YAC9BJ,OAAO,CAACK,IAAI,CAACD,SAAS,CAACE,MAAM,IAAI,CAAC,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC;YAC9DJ,OAAO,CAACO,QAAQ,CAAC,CAAC;UACtB,CAAC;UACD,IAAI;YACAb,YAAY,CAACc,KAAK,CAACT,OAAO,EAAE,CAAC,GAAGF,IAAI,EAAEM,OAAO,CAAC,CAAC;UACnD,CAAC,CACD,OAAOM,GAAG,EAAE;YACR,IAAInB,cAAc,CAACU,OAAO,CAAC,EAAE;cACzBA,OAAO,CAACU,KAAK,CAACD,GAAG,CAAC;YACtB,CAAC,MACI;cACDE,OAAO,CAACC,IAAI,CAACH,GAAG,CAAC;YACrB;UACJ;QACJ;QACA,OAAOT,OAAO,CAACa,SAAS,CAACX,UAAU,CAAC;MACxC,CAAC,MACI;QACD,MAAMY,KAAK,GAAG;UACVjB,IAAI;UAAEK,UAAU;UAAED;QACtB,CAAC;QACD,OAAOL,SAAS,CAACmB,QAAQ,CAACC,QAAQ,EAAE,CAAC,EAAEF,KAAK,CAAC;MACjD;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASE,QAAQA,CAACF,KAAK,EAAE;EACrB,MAAMG,IAAI,GAAG,IAAI;EACjB,MAAM;IAAEpB,IAAI;IAAEK,UAAU;IAAED;EAAO,CAAC,GAAGa,KAAK;EAC1C,MAAM;IAAEpB,YAAY;IAAEK,OAAO;IAAEH;EAAU,CAAC,GAAGK,MAAM;EACnD,IAAI;IAAED;EAAQ,CAAC,GAAGC,MAAM;EACxB,IAAI,CAACD,OAAO,EAAE;IACVA,OAAO,GAAGC,MAAM,CAACD,OAAO,GAAG,IAAIZ,YAAY,CAAC,CAAC;IAC7C,MAAMe,OAAO,GAAGA,CAAC,GAAGC,SAAS,KAAK;MAC9B,MAAMc,KAAK,GAAGd,SAAS,CAACE,MAAM,IAAI,CAAC,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;MAC9D,IAAI,CAACe,GAAG,CAACvB,SAAS,CAACmB,QAAQ,CAACK,YAAY,EAAE,CAAC,EAAE;QAAEF,KAAK;QAAElB;MAAQ,CAAC,CAAC,CAAC;IACrE,CAAC;IACD,IAAI;MACAN,YAAY,CAACc,KAAK,CAACT,OAAO,EAAE,CAAC,GAAGF,IAAI,EAAEM,OAAO,CAAC,CAAC;IACnD,CAAC,CACD,OAAOM,GAAG,EAAE;MACRT,OAAO,CAACU,KAAK,CAACD,GAAG,CAAC;IACtB;EACJ;EACA,IAAI,CAACU,GAAG,CAACnB,OAAO,CAACa,SAAS,CAACX,UAAU,CAAC,CAAC;AAC3C;AACA,SAASkB,YAAYA,CAACN,KAAK,EAAE;EACzB,MAAM;IAAEI,KAAK;IAAElB;EAAQ,CAAC,GAAGc,KAAK;EAChCd,OAAO,CAACK,IAAI,CAACa,KAAK,CAAC;EACnBlB,OAAO,CAACO,QAAQ,CAAC,CAAC;AACtB;AACA,SAASc,aAAaA,CAACP,KAAK,EAAE;EAC1B,MAAM;IAAEL,GAAG;IAAET;EAAQ,CAAC,GAAGc,KAAK;EAC9Bd,OAAO,CAACU,KAAK,CAACD,GAAG,CAAC;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
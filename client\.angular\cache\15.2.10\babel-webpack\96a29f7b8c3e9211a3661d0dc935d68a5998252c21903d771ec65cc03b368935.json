{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AppConfig, coreConfig } from 'app/app-config';\nimport { environment } from 'environments/environment';\nimport { Capacitor } from '@capacitor/core';\nimport { StatusBar } from '@capacitor/status-bar';\nimport { NavigationBar } from '@hugotomazi/capacitor-navigation-bar';\nimport { TermConditionComponent } from 'app/components/term-condition/term-condition.component';\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"./auth.service\";\nexport class CommonsService {\n  constructor(_translateService, _modalService, _authService) {\n    this._translateService = _translateService;\n    this._modalService = _modalService;\n    this._authService = _authService;\n    this.dataTableDefaults = {\n      dom: 'B<\"row ml-2 mr-2\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-2 pr-2\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\n      dom_notB: '<\"row mt-1 ml-2 mr-2\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-2 pr-2\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\n      dom_m25: 'B<\"row ml-25 mr-25\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-25 pr-25\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\n      dom_table_card: '<\"row ml-25 mr-0\"<\"col-12 col-md-9 col-lg-9 p-0\"B><\"col-12 p-0 col-md-3 col-lg-3 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-25 pr-25\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\n      buttons: {\n        dom: {\n          container: {\n            className: 'dt-buttons ml-2 mb-1'\n          },\n          button: {\n            className: 'btn btn-primary mt-25'\n          }\n        },\n        dom_table_card: {\n          container: {\n            className: 'dt-buttons m-0'\n          },\n          button: {\n            className: 'btn btn-primary'\n          }\n        }\n      },\n      lengthMenu: [[10, 25, 50, 100, 200, 500, 1000, -1], [10, 25, 50, 100, 200, 500, 1000, this._translateService.instant('All')]],\n      lang: {\n        decimal: '',\n        emptyTable: this._translateService.instant('No data available in table'),\n        info: this._translateService.instant('Showing _START_ to _END_ of _TOTAL_ entries'),\n        infoEmpty: this._translateService.instant('Showing 0 to 0 of 0 entries'),\n        infoFiltered: this._translateService.instant('(filtered from _MAX_ total entries)'),\n        infoPostFix: '',\n        thousands: ',',\n        lengthMenu: this._translateService.instant('Show _MENU_ entries'),\n        loadingRecords: `${this._translateService.instant('Loading')}...`,\n        processing: this._translateService.instant('Processing...'),\n        search: `${this._translateService.instant('Search')}:`,\n        zeroRecords: this._translateService.instant('No matching records found'),\n        paginate: {\n          first: this._translateService.instant('First'),\n          last: this._translateService.instant('Last'),\n          next: \"<i class=''></i>\",\n          previous: \"<i class=''></i>\"\n        },\n        select: {\n          rows: {\n            _: this._translateService.instant('Selected %d rows'),\n            0: this._translateService.instant('Click a row to select'),\n            1: this._translateService.instant('Selected 1 row')\n          }\n        }\n      }\n    };\n    this.regex_english_name = /^[a-z]+(([\\',.-]?[ ]?[a-z])?[a-z]*)*$/i;\n    this.regex_chinese_name = /^[a-zA-Z \\u3400-\\u9fa5\\ufeff\\u00A0\\u202F\\uFF0C]*$/u;\n    // public regex_name: RegExp =\n    // /^[a-zA-Z\\u3400-\\u9fa5\\u00C0-\\u1EF9\\ufeff\\u00A0\\u202F\\uFF0C]+(([\\'’,. -][a-z \\u3400-\\u9fa5\\u00C0-\\u1EF9\\ufeff\\u00A0\\u202F\\uFF0C])?[a-z \\u3400-\\u9fa5\\u00C0-\\u1EF9\\ufeff\\u00A0\\u202F\\uFF0C]*)*$/i;\n    this.regex_name = /^\\p{L}([.,'’-]?\\s?[\\p{L}\\p{M}]+)*[\\p{L}\\p{M}]*$/u;\n    this.simpleUploadConfig = {\n      // The URL that the images are uploaded to.\n      uploadUrl: `${environment.apiUrl}/send-messages/upload`,\n      headers: {\n        Accept: 'application/json, text/plain, */*',\n        'X-CSRF-TOKEN': 'CSRF-Token',\n        Authorization: `Bearer ${this._authService.currentUserValue?.token}`,\n        'X-project-id': AppConfig.PROJECT_ID\n      }\n    };\n    this.originalOrder = (a, b) => {\n      return 0;\n    };\n  }\n  removeSpaceInName(str) {\n    let strArray = str.split(' ');\n    let newStr = '';\n    //join string together if there is not space\n    if (strArray.length == 1) {\n      newStr = strArray[0];\n    } else {\n      for (let i = 0; i < strArray.length; i++) {\n        if (strArray[i] != '') {\n          // newStr +=strArray[i].charAt(0).toUpperCase() + strArray[i].slice(1) + ' ';\n          newStr += strArray[i] + ' ';\n        }\n      }\n    }\n    return str = newStr.trim();\n  }\n  saveState(tableId, table) {\n    localStorage.setItem(tableId, JSON.stringify(table.state()));\n  }\n  loadState(tableId) {\n    let state = localStorage.getItem(tableId);\n    if (state) {\n      return JSON.parse(state);\n    }\n  }\n  onloadImgErr(event) {\n    event.target.src = coreConfig.app.appLogoImage;\n  }\n  openTNCGroupModal() {\n    const modalRef = this._modalService.open(TermConditionComponent, {\n      centered: true,\n      backdrop: 'static',\n      scrollable: true\n    });\n  }\n  getBadgeClassPayment(status) {\n    if (status == null) {\n      let pending_text = this._translateService.instant('Pending');\n      return `<span class=\"badge badge-light-secondary\">${pending_text}</span>`;\n    }\n    let color = 'secondary';\n    status = this._translateService.instant(status.toLowerCase());\n    switch (status) {\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.succeeded):\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.paid):\n        status = 'paid';\n        color = 'light-success';\n        break;\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.marked_as_paid):\n        color = 'light-success';\n        status = 'marked as paid';\n        break;\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.sent):\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.open):\n        status = 'sent';\n        color = 'light-warning';\n        break;\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.failed):\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.cancelled):\n        color = 'light-danger';\n        break;\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.refunded):\n      case AppConfig.PAYMENT_STATUS.marked_as_refunded:\n        color = 'info';\n        status = this._translateService.instant('refunded');\n        break;\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.partially_refunded):\n        color = 'warning';\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.draft):\n        color = 'light-secondary';\n      default:\n        break;\n    }\n    return `<span class=\"badge badge-${color} text-capitalize\">${this._translateService.instant(status)}</span>`;\n  }\n  getBadgeClassBlacklist(status) {\n    if (status == null) {\n      let pending_text = this._translateService.instant('N/A');\n      return `<span class=\"badge badge-light-secondary\">${pending_text}</span>`;\n    }\n    let color = 'secondary';\n    status = this._translateService.instant(status);\n    switch (status) {\n      case this._translateService.instant(AppConfig.BLACK_LIST_STATUS.unsubscribed):\n        color = 'light-warning';\n        break;\n      case this._translateService.instant(AppConfig.BLACK_LIST_STATUS.block):\n        color = 'light-danger';\n        break;\n      default:\n        color = 'light-secondary';\n        break;\n    }\n    return `<span class=\"badge badge-${color} text-capitalize\">${this._translateService.instant(status)}</span>`;\n  }\n  getTitleColorPayment(status) {\n    let color = 'secondary';\n    if (status == null) return {\n      color: color,\n      title: this._translateService.instant('No invoice')\n    };\n    status = status.toLowerCase();\n    switch (status) {\n      case AppConfig.PAYMENT_STATUS.succeeded:\n      case AppConfig.PAYMENT_STATUS.paid:\n        status = 'paid';\n        color = 'success';\n        break;\n      case AppConfig.PAYMENT_STATUS.marked_as_paid:\n        color = 'success';\n        status = 'marked as paid';\n        break;\n      case AppConfig.PAYMENT_STATUS.sent:\n      case AppConfig.PAYMENT_STATUS.open:\n        status = 'sent';\n        color = 'warning';\n        break;\n      case AppConfig.PAYMENT_STATUS.failed:\n      case AppConfig.PAYMENT_STATUS.cancelled:\n        color = 'danger';\n        status = this._translateService.instant('cancelled');\n        break;\n      case AppConfig.PAYMENT_STATUS.refunded:\n      case AppConfig.PAYMENT_STATUS.marked_as_refunded:\n        color = 'info';\n        status = 'refunded';\n        break;\n      case AppConfig.PAYMENT_STATUS.partially_refunded:\n        color = 'warning';\n      default:\n        color = 'secondary';\n        break;\n    }\n    return {\n      color: color,\n      title: this._translateService.instant(status)\n    };\n  }\n  getBadgeRegistration(status) {\n    switch (status) {\n      case AppConfig.APPROVE_STATUS.Registered:\n        return 'badge-light-info';\n      case AppConfig.APPROVE_STATUS.Rejected:\n        return 'badge-light-danger';\n      case AppConfig.APPROVE_STATUS.Approved:\n        return 'badge-light-success';\n      default:\n        return 'badge-light-secondary';\n    }\n  }\n  getBadgeValidateStatus(status) {\n    let pending_text = this._translateService.instant('Pending');\n    let awaiting_text = this._translateService.instant('Awaiting Update');\n    let updated_text = this._translateService.instant('Updated');\n    let validated_text = this._translateService.instant('Validated');\n    if (status == null) {\n      return ``;\n    }\n    switch (status) {\n      case AppConfig.VALIDATE_STATUS.Pending:\n        return `<span class=\"badge badge-light-info\">${pending_text}</span>`;\n      case AppConfig.VALIDATE_STATUS.AwaitingUpdate:\n        return `<span class=\"badge badge-light-danger\">${awaiting_text}</span>`;\n      case AppConfig.VALIDATE_STATUS.Updated:\n        return `<span class=\"badge badge-light-warning\">${updated_text}</span>`;\n      case AppConfig.VALIDATE_STATUS.Validated:\n        return `<span class=\"badge badge-light-success\">${validated_text}</span>`;\n      default:\n        return `<span class=\"badge badge-light-secondary\">${status.toUpperCase()}</span>`;\n    }\n  }\n  getClassPlayerUpdateStatus(status) {\n    switch (status?.toLowerCase()) {\n      case AppConfig.PLAYER_UPDATE_STATUS.pending.toLowerCase():\n        return 'badge badge-light-info';\n      case AppConfig.PLAYER_UPDATE_STATUS.rejected.toLowerCase():\n        return 'badge badge-light-danger';\n      case AppConfig.PLAYER_UPDATE_STATUS.approved.toLowerCase():\n        return 'badge badge-light-success';\n      default:\n        return 'badge badge-light-secondary';\n    }\n  }\n  scrollToFirstInvalidControl(formID) {\n    let form = document.getElementById(formID);\n    if (!form) return;\n    let firstInvalidControls = form.getElementsByClassName('ng-invalid');\n    if (firstInvalidControls.length == 0) return;\n    let firstInvalidControl = firstInvalidControls[0];\n    firstInvalidControl.scrollIntoView();\n    firstInvalidControl.focus();\n  }\n  // custom search input\n  customSearchInput(tableID) {\n    let search_container = document.getElementById(`${tableID}_filter`);\n    let label = search_container.querySelector('label');\n    let search_input = search_container.querySelector('input[type=\"search\"]');\n    // add class w-100\n    if (search_container) search_container.classList.add('w-100');\n    if (label) label.classList.add('w-100');\n    // remove class form-control-sm\n    if (search_input) search_input.classList.remove('form-control-sm');\n    // add class m-0\n    if (search_input) search_input.classList.add(...['m-0', 'w-100']);\n  }\n  openFullscreen(element_id) {\n    const elem = document.getElementById(element_id);\n    if (elem.requestFullscreen) {\n      elem.requestFullscreen();\n    } else if (elem.webkitRequestFullscreen) {\n      elem.webkitRequestFullscreen();\n    } else if (elem.msRequestFullscreen) {\n      elem.msRequestFullscreen();\n    }\n  }\n  closeFullscreen() {\n    if (document.exitFullscreen) {\n      document.exitFullscreen();\n    } else if (document.webkitExitFullscreen) {\n      document.webkitExitFullscreen();\n    }\n  }\n  toggleFullScreen(element_id) {\n    console.log('toggle fullscreen', element_id);\n    if (!document.fullscreenElement && !document.webkitIsFullScreen && !document.mozFullScreen && !document.msFullscreenElement) {\n      this.openFullscreen(element_id);\n    } else {\n      this.closeFullscreen();\n    }\n  }\n  toggleFullScreenCss(element_id) {\n    return _asyncToGenerator(function* () {\n      // console.log('toggle fullscreen css', element_id);\n      const elem = document.getElementById(element_id);\n      const content = document.getElementsByClassName('app-content')[0];\n      if (elem.classList.contains('fullscreen')) {\n        elem.classList.remove('fullscreen');\n        if (content) {\n          if (Capacitor.isNativePlatform()) {\n            // StatusBar.setBackgroundColor({ color: '#ffffff' });\n          }\n          content.classList.remove('fullscreen');\n        }\n      } else {\n        elem.classList.add('fullscreen');\n        // if has safeAreaInsets, set width height follow safeAreaInsets\n        if (Capacitor.getPlatform() == 'ios') {\n          // add class ios\n          elem.classList.add('ios');\n        }\n        if (content) {\n          if (Capacitor.isNativePlatform()) {\n            yield ScreenOrientation.lock({\n              orientation: 'landscape'\n            });\n            yield StatusBar.hide();\n            StatusBar.setBackgroundColor({\n              color: '#000000'\n            });\n            // hide navigation bar\n            yield NavigationBar.hide();\n          }\n          content.classList.add('fullscreen');\n        }\n      }\n    })();\n  }\n  isElementInFullScreenCss(element_id) {\n    const elem = document.getElementById(element_id);\n    return elem.classList.contains('fullscreen');\n  }\n  // click .video-container to show .video-controls and hide after 1.5s\n  showVideoControls() {\n    let video_container = document.querySelector('.video-container');\n    let video_controls = document.querySelector('.video-controls');\n    // if (video_container && video_controls) {\n    console.log('listen click video container');\n    //   video_container.addEventListener('click', () => {\n    if (video_controls.classList.contains('show')) return;\n    video_controls.classList.add('show');\n    setTimeout(() => {\n      video_controls.classList.remove('show');\n    }, 2500);\n    // });\n    // }\n  }\n\n  checkMediaDevicePermission() {\n    return _asyncToGenerator(function* () {\n      let permission = false;\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        permission = true;\n        stream.getTracks().forEach(track => {\n          track.stop();\n        });\n        console.log('permission', permission);\n      } catch (e) {\n        console.error('error', e);\n      }\n      return permission;\n    })();\n  }\n  getAzurePlayBackUrl(filePath, containerName = environment.livekit.azureStorage.containerName) {\n    return `https://${environment.livekit.azureStorage.accountName}.blob.core.windows.net/${containerName}/${filePath}`;\n  }\n  getAzureHLSPlayBackUrl(playlistName, containerName = environment.livekit.azureStorage.containerName) {\n    return `https://${environment.livekit.azureStorage.accountName}.blob.core.windows.net/${containerName}/${playlistName}`;\n  }\n  getAzureHLSPlayBackUrlDummny(match_id, containerName = environment.livekit.azureStorage.containerName) {\n    return `https://${environment.livekit.azureStorage.accountName}.blob.core.windows.net/${containerName}/${environment.app_name}/${match_id}.m3u8`;\n  }\n  static #_ = this.ɵfac = function CommonsService_Factory(t) {\n    return new (t || CommonsService)(i0.ɵɵinject(i1.TranslateService), i0.ɵɵinject(i2.NgbModal), i0.ɵɵinject(i3.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CommonsService,\n    factory: CommonsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AAGA,SAASA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AACtD,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,aAAa,QAAQ,sCAAsC;AAEpE,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,iBAAiB,QAAQ,+BAA+B;;;;;AAKjE,OAAM,MAAOC,cAAc;EA6EzBC,YACSC,iBAAmC,EACnCC,aAAuB,EACvBC,YAAyB;IAFzB,KAAAF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IA/Ed,KAAAC,iBAAiB,GAAG;MACzBC,GAAG,EAAE,iKAAiK;MACtKC,QAAQ,EACN,qKAAqK;MACvKC,OAAO,EACL,qKAAqK;MACvKC,cAAc,EACZ,gLAAgL;MAClLC,OAAO,EAAE;QACPJ,GAAG,EAAE;UACHK,SAAS,EAAE;YAAEC,SAAS,EAAE;UAAsB,CAAE;UAChDC,MAAM,EAAE;YAAED,SAAS,EAAE;UAAuB;SAC7C;QACDH,cAAc,EAAE;UACdE,SAAS,EAAE;YACTC,SAAS,EAAE;WACZ;UACDC,MAAM,EAAE;YAAED,SAAS,EAAE;UAAiB;;OAEzC;MACDE,UAAU,EAAE,CACV,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EACrC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAACZ,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC,CAAC,CACzE;MACDC,IAAI,EAAE;QACJC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE,IAAI,CAAChB,iBAAiB,CAACa,OAAO,CAAC,4BAA4B,CAAC;QACxEI,IAAI,EAAE,IAAI,CAACjB,iBAAiB,CAACa,OAAO,CAClC,6CAA6C,CAC9C;QACDK,SAAS,EAAE,IAAI,CAAClB,iBAAiB,CAACa,OAAO,CAAC,6BAA6B,CAAC;QACxEM,YAAY,EAAE,IAAI,CAACnB,iBAAiB,CAACa,OAAO,CAC1C,qCAAqC,CACtC;QACDO,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,GAAG;QACdT,UAAU,EAAE,IAAI,CAACZ,iBAAiB,CAACa,OAAO,CAAC,qBAAqB,CAAC;QACjES,cAAc,EAAE,GAAG,IAAI,CAACtB,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC,KAAK;QACjEU,UAAU,EAAE,IAAI,CAACvB,iBAAiB,CAACa,OAAO,CAAC,eAAe,CAAC;QAC3DW,MAAM,EAAE,GAAG,IAAI,CAACxB,iBAAiB,CAACa,OAAO,CAAC,QAAQ,CAAC,GAAG;QACtDY,WAAW,EAAE,IAAI,CAACzB,iBAAiB,CAACa,OAAO,CAAC,2BAA2B,CAAC;QACxEa,QAAQ,EAAE;UACRC,KAAK,EAAE,IAAI,CAAC3B,iBAAiB,CAACa,OAAO,CAAC,OAAO,CAAC;UAC9Ce,IAAI,EAAE,IAAI,CAAC5B,iBAAiB,CAACa,OAAO,CAAC,MAAM,CAAC;UAC5CgB,IAAI,EAAE,kBAAkB;UACxBC,QAAQ,EAAE;SACX;QACDC,MAAM,EAAE;UACNC,IAAI,EAAE;YACJC,CAAC,EAAE,IAAI,CAACjC,iBAAiB,CAACa,OAAO,CAAC,kBAAkB,CAAC;YACrD,CAAC,EAAE,IAAI,CAACb,iBAAiB,CAACa,OAAO,CAAC,uBAAuB,CAAC;YAC1D,CAAC,EAAE,IAAI,CAACb,iBAAiB,CAACa,OAAO,CAAC,gBAAgB;;;;KAIzD;IAEM,KAAAqB,kBAAkB,GAAW,wCAAwC;IACrE,KAAAC,kBAAkB,GACvB,qDAAqD;IACvD;IACA;IACO,KAAAC,UAAU,GACf,kDAAkD;IAE7C,KAAAC,kBAAkB,GAAG;MAC1B;MACAC,SAAS,EAAE,GAAG9C,WAAW,CAAC+C,MAAM,uBAAuB;MACvDC,OAAO,EAAE;QACPC,MAAM,EAAE,mCAAmC;QAC3C,cAAc,EAAE,YAAY;QAC5BC,aAAa,EAAE,UAAU,IAAI,CAACxC,YAAY,CAACyC,gBAAgB,EAAEC,KAAK,EAAE;QACpE,cAAc,EAAEtD,SAAS,CAACuD;;KAE7B;IAkOD,KAAAC,aAAa,GAAG,CACdC,CAA2B,EAC3BC,CAA2B,KACjB;MACV,OAAO,CAAC;IACV,CAAC;EAjOE;EACHC,iBAAiBA,CAACC,GAAG;IACnB,IAAIC,QAAQ,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAIC,MAAM,GAAG,EAAE;IACf;IACA,IAAIF,QAAQ,CAACG,MAAM,IAAI,CAAC,EAAE;MACxBD,MAAM,GAAGF,QAAQ,CAAC,CAAC,CAAC;KACrB,MAAM;MACL,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;QACxC,IAAIJ,QAAQ,CAACI,CAAC,CAAC,IAAI,EAAE,EAAE;UACrB;UACAF,MAAM,IAAIF,QAAQ,CAACI,CAAC,CAAC,GAAG,GAAG;;;;IAIjC,OAAQL,GAAG,GAAGG,MAAM,CAACG,IAAI,EAAE;EAC7B;EAEAC,SAASA,CAACC,OAAO,EAAEC,KAAK;IACtBC,YAAY,CAACC,OAAO,CAACH,OAAO,EAAEI,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC;EAC9D;EAEAC,SAASA,CAACP,OAAO;IACf,IAAIM,KAAK,GAAGJ,YAAY,CAACM,OAAO,CAACR,OAAO,CAAC;IACzC,IAAIM,KAAK,EAAE;MACT,OAAOF,IAAI,CAACK,KAAK,CAACH,KAAK,CAAC;;EAE5B;EAEAI,YAAYA,CAACC,KAAK;IAChBA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAGhF,UAAU,CAACiF,GAAG,CAACC,YAAY;EAChD;EAEAC,iBAAiBA,CAAA;IACf,MAAMC,QAAQ,GAAG,IAAI,CAAC1E,aAAa,CAAC2E,IAAI,CAAChF,sBAAsB,EAAE;MAC/DiF,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;KACb,CAAC;EACJ;EAEAC,oBAAoBA,CAACC,MAAc;IACjC,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB,IAAIC,YAAY,GAAG,IAAI,CAAClF,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC;MAC5D,OAAO,6CAA6CqE,YAAY,SAAS;;IAE3E,IAAIC,KAAK,GAAG,WAAW;IACvBF,MAAM,GAAG,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAACoE,MAAM,CAACG,WAAW,EAAE,CAAC;IAE7D,QAAQH,MAAM;MACZ,KAAK,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACC,SAAS,CAAC;MACvE,KAAK,IAAI,CAACtF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACE,IAAI,CAAC;QAChEN,MAAM,GAAG,MAAM;QACfE,KAAK,GAAG,eAAe;QACvB;MACF,KAAK,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CACjCvB,SAAS,CAAC+F,cAAc,CAACG,cAAc,CACxC;QACCL,KAAK,GAAG,eAAe;QACvBF,MAAM,GAAG,gBAAgB;QACzB;MACF,KAAK,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACI,IAAI,CAAC;MAClE,KAAK,IAAI,CAACzF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACT,IAAI,CAAC;QAChEK,MAAM,GAAG,MAAM;QACfE,KAAK,GAAG,eAAe;QACvB;MACF,KAAK,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACK,MAAM,CAAC;MACpE,KAAK,IAAI,CAAC1F,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACM,SAAS,CAAC;QACrER,KAAK,GAAG,cAAc;QACtB;MACF,KAAK,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACO,QAAQ,CAAC;MACtE,KAAKtG,SAAS,CAAC+F,cAAc,CAACQ,kBAAkB;QAC9CV,KAAK,GAAG,MAAM;QACdF,MAAM,GAAG,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAAC,UAAU,CAAC;QACnD;MACF,KAAK,IAAI,CAACb,iBAAiB,CAACa,OAAO,CACjCvB,SAAS,CAAC+F,cAAc,CAACS,kBAAkB,CAC5C;QACCX,KAAK,GAAG,SAAS;MACnB,KAAK,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC+F,cAAc,CAACU,KAAK,CAAC;QACjEZ,KAAK,GAAG,iBAAiB;MAC3B;QACE;;IAGJ,OAAO,4BAA4BA,KAAK,qBAAqB,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CACzFoE,MAAM,CACP,SAAS;EACZ;EAEAe,sBAAsBA,CAACf,MAAc;IACnC,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB,IAAIC,YAAY,GAAG,IAAI,CAAClF,iBAAiB,CAACa,OAAO,CAAC,KAAK,CAAC;MACxD,OAAO,6CAA6CqE,YAAY,SAAS;;IAE3E,IAAIC,KAAK,GAAG,WAAW;IACvBF,MAAM,GAAG,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAACoE,MAAM,CAAC;IAE/C,QAAQA,MAAM;MACZ,KAAK,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC2G,iBAAiB,CAACC,YAAY,CAAC;QAC3Ef,KAAK,GAAG,eAAe;QACvB;MACF,KAAK,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CAACvB,SAAS,CAAC2G,iBAAiB,CAACE,KAAK,CAAC;QACpEhB,KAAK,GAAG,cAAc;QACtB;MACF;QACEA,KAAK,GAAG,iBAAiB;QACzB;;IAGJ,OAAO,4BAA4BA,KAAK,qBAAqB,IAAI,CAACnF,iBAAiB,CAACa,OAAO,CACzFoE,MAAM,CACP,SAAS;EACZ;EAEAmB,oBAAoBA,CAACnB,MAAc;IACjC,IAAIE,KAAK,GAAG,WAAW;IACvB,IAAIF,MAAM,IAAI,IAAI,EAChB,OAAO;MACLE,KAAK,EAAEA,KAAK;MACZkB,KAAK,EAAE,IAAI,CAACrG,iBAAiB,CAACa,OAAO,CAAC,YAAY;KACnD;IAEHoE,MAAM,GAAGA,MAAM,CAACG,WAAW,EAAE;IAC7B,QAAQH,MAAM;MACZ,KAAK3F,SAAS,CAAC+F,cAAc,CAACC,SAAS;MACvC,KAAKhG,SAAS,CAAC+F,cAAc,CAACE,IAAI;QAChCN,MAAM,GAAG,MAAM;QACfE,KAAK,GAAG,SAAS;QACjB;MACF,KAAK7F,SAAS,CAAC+F,cAAc,CAACG,cAAc;QAC1CL,KAAK,GAAG,SAAS;QACjBF,MAAM,GAAG,gBAAgB;QACzB;MACF,KAAK3F,SAAS,CAAC+F,cAAc,CAACI,IAAI;MAClC,KAAKnG,SAAS,CAAC+F,cAAc,CAACT,IAAI;QAChCK,MAAM,GAAG,MAAM;QACfE,KAAK,GAAG,SAAS;QACjB;MACF,KAAK7F,SAAS,CAAC+F,cAAc,CAACK,MAAM;MACpC,KAAKpG,SAAS,CAAC+F,cAAc,CAACM,SAAS;QACrCR,KAAK,GAAG,QAAQ;QAChBF,MAAM,GAAG,IAAI,CAACjF,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;QACpD;MACF,KAAKvB,SAAS,CAAC+F,cAAc,CAACO,QAAQ;MACtC,KAAKtG,SAAS,CAAC+F,cAAc,CAACQ,kBAAkB;QAC9CV,KAAK,GAAG,MAAM;QACdF,MAAM,GAAG,UAAU;QACnB;MACF,KAAK3F,SAAS,CAAC+F,cAAc,CAACS,kBAAkB;QAC9CX,KAAK,GAAG,SAAS;MACnB;QACEA,KAAK,GAAG,WAAW;QACnB;;IAGJ,OAAO;MACLA,KAAK,EAAEA,KAAK;MACZkB,KAAK,EAAE,IAAI,CAACrG,iBAAiB,CAACa,OAAO,CAACoE,MAAM;KAC7C;EACH;EAEAqB,oBAAoBA,CAACrB,MAAM;IACzB,QAAQA,MAAM;MACZ,KAAK3F,SAAS,CAACiH,cAAc,CAACC,UAAU;QACtC,OAAO,kBAAkB;MAC3B,KAAKlH,SAAS,CAACiH,cAAc,CAACE,QAAQ;QACpC,OAAO,oBAAoB;MAC7B,KAAKnH,SAAS,CAACiH,cAAc,CAACG,QAAQ;QACpC,OAAO,qBAAqB;MAC9B;QACE,OAAO,uBAAuB;;EAEpC;EAEAC,sBAAsBA,CAAC1B,MAAM;IAC3B,IAAIC,YAAY,GAAG,IAAI,CAAClF,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC;IAC5D,IAAI+F,aAAa,GAAG,IAAI,CAAC5G,iBAAiB,CAACa,OAAO,CAAC,iBAAiB,CAAC;IACrE,IAAIgG,YAAY,GAAG,IAAI,CAAC7G,iBAAiB,CAACa,OAAO,CAAC,SAAS,CAAC;IAC5D,IAAIiG,cAAc,GAAG,IAAI,CAAC9G,iBAAiB,CAACa,OAAO,CAAC,WAAW,CAAC;IAChE,IAAIoE,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,EAAE;;IAEX,QAAQA,MAAM;MACZ,KAAK3F,SAAS,CAACyH,eAAe,CAACC,OAAO;QACpC,OAAO,wCAAwC9B,YAAY,SAAS;MACtE,KAAK5F,SAAS,CAACyH,eAAe,CAACE,cAAc;QAC3C,OAAO,0CAA0CL,aAAa,SAAS;MACzE,KAAKtH,SAAS,CAACyH,eAAe,CAACG,OAAO;QACpC,OAAO,2CAA2CL,YAAY,SAAS;MACzE,KAAKvH,SAAS,CAACyH,eAAe,CAACI,SAAS;QACtC,OAAO,2CAA2CL,cAAc,SAAS;MAC3E;QACE,OAAO,6CAA6C7B,MAAM,CAACmC,WAAW,EAAE,SAAS;;EAEvF;EAEAC,0BAA0BA,CAACpC,MAAc;IACvC,QAAQA,MAAM,EAAEG,WAAW,EAAE;MAC3B,KAAK9F,SAAS,CAACgI,oBAAoB,CAACC,OAAO,CAACnC,WAAW,EAAE;QACvD,OAAO,wBAAwB;MACjC,KAAK9F,SAAS,CAACgI,oBAAoB,CAACE,QAAQ,CAACpC,WAAW,EAAE;QACxD,OAAO,0BAA0B;MACnC,KAAK9F,SAAS,CAACgI,oBAAoB,CAACG,QAAQ,CAACrC,WAAW,EAAE;QACxD,OAAO,2BAA2B;MACpC;QACE,OAAO,6BAA6B;;EAE1C;EAEAsC,2BAA2BA,CAACC,MAAM;IAChC,IAAIC,IAAI,GAAGC,QAAQ,CAACC,cAAc,CAACH,MAAM,CAAC;IAC1C,IAAI,CAACC,IAAI,EAAE;IACX,IAAIG,oBAAoB,GAAGH,IAAI,CAACI,sBAAsB,CAAC,YAAY,CAAC;IACpE,IAAID,oBAAoB,CAACzE,MAAM,IAAI,CAAC,EAAE;IACtC,IAAI2E,mBAAmB,GAAGF,oBAAoB,CAAC,CAAC,CAAC;IACjDE,mBAAmB,CAACC,cAAc,EAAE;IACnCD,mBAAmC,CAACE,KAAK,EAAE;EAC9C;EASA;EACAC,iBAAiBA,CAACC,OAAO;IACvB,IAAIC,gBAAgB,GAAGT,QAAQ,CAACC,cAAc,CAAC,GAAGO,OAAO,SAAS,CAAC;IACnE,IAAIE,KAAK,GAAGD,gBAAgB,CAACE,aAAa,CAAC,OAAO,CAAC;IACnD,IAAIC,YAAY,GAAGH,gBAAgB,CAACE,aAAa,CAAC,sBAAsB,CAAC;IACzE;IACA,IAAIF,gBAAgB,EAAEA,gBAAgB,CAACI,SAAS,CAACC,GAAG,CAAC,OAAO,CAAC;IAC7D,IAAIJ,KAAK,EAAEA,KAAK,CAACG,SAAS,CAACC,GAAG,CAAC,OAAO,CAAC;IACvC;IACA,IAAIF,YAAY,EAAEA,YAAY,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IAClE;IACA,IAAIH,YAAY,EAAEA,YAAY,CAACC,SAAS,CAACC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EACnE;EAEAE,cAAcA,CAACC,UAAkB;IAC/B,MAAMC,IAAI,GAAGlB,QAAQ,CAACC,cAAc,CAACgB,UAAU,CAAQ;IACvD,IAAIC,IAAI,CAACC,iBAAiB,EAAE;MAC1BD,IAAI,CAACC,iBAAiB,EAAE;KACzB,MAAM,IAAID,IAAI,CAACE,uBAAuB,EAAE;MACvCF,IAAI,CAACE,uBAAuB,EAAE;KAC/B,MAAM,IAAIF,IAAI,CAACG,mBAAmB,EAAE;MACnCH,IAAI,CAACG,mBAAmB,EAAE;;EAE9B;EAEAC,eAAeA,CAAA;IACb,IAAItB,QAAQ,CAACuB,cAAc,EAAE;MAC3BvB,QAAQ,CAACuB,cAAc,EAAE;KAC1B,MAAM,IAAKvB,QAAgB,CAACwB,oBAAoB,EAAE;MAChDxB,QAAgB,CAACwB,oBAAoB,EAAE;;EAE5C;EAEAC,gBAAgBA,CAACR,UAAkB;IACjCS,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,UAAU,CAAC;IAE5C,IACE,CAACjB,QAAQ,CAAC4B,iBAAiB,IAC3B,CAAE5B,QAAgB,CAAC6B,kBAAkB,IACrC,CAAE7B,QAAgB,CAAC8B,aAAa,IAChC,CAAE9B,QAAgB,CAAC+B,mBAAmB,EACtC;MACA,IAAI,CAACf,cAAc,CAACC,UAAU,CAAC;KAChC,MAAM;MACL,IAAI,CAACK,eAAe,EAAE;;EAE1B;EAEMU,mBAAmBA,CAACf,UAAkB;IAAA,OAAAgB,iBAAA;MAC1C;MACA,MAAMf,IAAI,GAAGlB,QAAQ,CAACC,cAAc,CAACgB,UAAU,CAAQ;MACvD,MAAMiB,OAAO,GAAGlC,QAAQ,CAACG,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAQ;MACxE,IAAIe,IAAI,CAACL,SAAS,CAACsB,QAAQ,CAAC,YAAY,CAAC,EAAE;QACzCjB,IAAI,CAACL,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;QACnC,IAAImB,OAAO,EAAE;UACX,IAAItK,SAAS,CAACwK,gBAAgB,EAAE,EAAE;YAChC;UAAA;UAEFF,OAAO,CAACrB,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;;OAEzC,MAAM;QACLG,IAAI,CAACL,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QAChC;QACA,IAAIlJ,SAAS,CAACyK,WAAW,EAAE,IAAI,KAAK,EAAE;UACpC;UACAnB,IAAI,CAACL,SAAS,CAACC,GAAG,CAAC,KAAK,CAAC;;QAE3B,IAAIoB,OAAO,EAAE;UACX,IAAItK,SAAS,CAACwK,gBAAgB,EAAE,EAAE;YAChC,MAAMpK,iBAAiB,CAACsK,IAAI,CAAC;cAAEC,WAAW,EAAE;YAAW,CAAE,CAAC;YAC1D,MAAM1K,SAAS,CAAC2K,IAAI,EAAE;YACtB3K,SAAS,CAAC4K,kBAAkB,CAAC;cAAEnF,KAAK,EAAE;YAAS,CAAE,CAAC;YAClD;YACA,MAAMxF,aAAa,CAAC0K,IAAI,EAAE;;UAE5BN,OAAO,CAACrB,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;;;IAEtC;EACH;EAEA4B,wBAAwBA,CAACzB,UAAkB;IACzC,MAAMC,IAAI,GAAGlB,QAAQ,CAACC,cAAc,CAACgB,UAAU,CAAQ;IACvD,OAAOC,IAAI,CAACL,SAAS,CAACsB,QAAQ,CAAC,YAAY,CAAC;EAC9C;EAEA;EACAQ,iBAAiBA,CAAA;IACf,IAAIC,eAAe,GAAG5C,QAAQ,CAACW,aAAa,CAAC,kBAAkB,CAAC;IAChE,IAAIkC,cAAc,GAAG7C,QAAQ,CAACW,aAAa,CAAC,iBAAiB,CAAC;IAC9D;IACAe,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C;IACA,IAAIkB,cAAc,CAAChC,SAAS,CAACsB,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC/CU,cAAc,CAAChC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IACpCgC,UAAU,CAAC,MAAK;MACdD,cAAc,CAAChC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;IACzC,CAAC,EAAE,IAAI,CAAC;IACR;IACA;EACF;;EAEMgC,0BAA0BA,CAAA;IAAA,OAAAd,iBAAA;MAC9B,IAAIe,UAAU,GAAG,KAAK;MACtB,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDC,KAAK,EAAE,IAAI;UACXC,KAAK,EAAE;SACR,CAAC;QACFN,UAAU,GAAG,IAAI;QACjBC,MAAM,CAACM,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAI;UACnCA,KAAK,CAACC,IAAI,EAAE;QACd,CAAC,CAAC;QACFhC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqB,UAAU,CAAC;OACtC,CAAC,OAAOW,CAAC,EAAE;QACVjC,OAAO,CAACkC,KAAK,CAAC,OAAO,EAAED,CAAC,CAAC;;MAE3B,OAAOX,UAAU;IAAC;EACpB;EAEAa,mBAAmBA,CACjBC,QAAgB,EAChBC,aAAA,GAAwBpM,WAAW,CAACqM,OAAO,CAACC,YAAY,CAACF,aAAa;IAEtE,OAAO,WAAWpM,WAAW,CAACqM,OAAO,CAACC,YAAY,CAACC,WAAW,0BAA0BH,aAAa,IAAID,QAAQ,EAAE;EACrH;EAEAK,sBAAsBA,CACpBC,YAAoB,EACpBL,aAAA,GAAwBpM,WAAW,CAACqM,OAAO,CAACC,YAAY,CAACF,aAAa;IAEtE,OAAO,WAAWpM,WAAW,CAACqM,OAAO,CAACC,YAAY,CAACC,WAAW,0BAA0BH,aAAa,IAAIK,YAAY,EAAE;EACzH;EAEAC,4BAA4BA,CAC1BC,QAAgB,EAChBP,aAAA,GAAwBpM,WAAW,CAACqM,OAAO,CAACC,YAAY,CAACF,aAAa;IAEtE,OAAO,WAAWpM,WAAW,CAACqM,OAAO,CAACC,YAAY,CAACC,WAAW,0BAA0BH,aAAa,IAAIpM,WAAW,CAAC4M,QAAQ,IAAID,QAAQ,OAAO;EAClJ;EAAC,QAAAlK,CAAA;qBA/bUnC,cAAc,EAAAuM,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,QAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;WAAd/M,cAAc;IAAAgN,OAAA,EAAdhN,cAAc,CAAAiN,IAAA;IAAAC,UAAA,EAFb;EAAM", "names": ["AppConfig", "coreConfig", "environment", "Capacitor", "StatusBar", "NavigationBar", "TermConditionComponent", "ScreenOrientation", "CommonsService", "constructor", "_translateService", "_modalService", "_authService", "dataTableDefaults", "dom", "dom_notB", "dom_m25", "dom_table_card", "buttons", "container", "className", "button", "lengthMenu", "instant", "lang", "decimal", "emptyTable", "info", "infoEmpty", "infoFiltered", "infoPostFix", "thousands", "loadingRecords", "processing", "search", "zeroRecords", "paginate", "first", "last", "next", "previous", "select", "rows", "_", "regex_english_name", "regex_chinese_name", "regex_name", "simpleUploadConfig", "uploadUrl", "apiUrl", "headers", "Accept", "Authorization", "currentUserValue", "token", "PROJECT_ID", "originalOrder", "a", "b", "removeSpaceInName", "str", "strArray", "split", "newStr", "length", "i", "trim", "saveState", "tableId", "table", "localStorage", "setItem", "JSON", "stringify", "state", "loadState", "getItem", "parse", "onloadImgErr", "event", "target", "src", "app", "appLogoImage", "openTNCGroupModal", "modalRef", "open", "centered", "backdrop", "scrollable", "getBadgeClassPayment", "status", "pending_text", "color", "toLowerCase", "PAYMENT_STATUS", "succeeded", "paid", "marked_as_paid", "sent", "failed", "cancelled", "refunded", "marked_as_refunded", "partially_refunded", "draft", "getBadgeClassBlacklist", "BLACK_LIST_STATUS", "unsubscribed", "block", "getTitleColorPayment", "title", "getBadgeRegistration", "APPROVE_STATUS", "Registered", "Rejected", "Approved", "getBadgeValidateStatus", "awaiting_text", "updated_text", "validated_text", "VALIDATE_STATUS", "Pending", "AwaitingUpdate", "Updated", "Validated", "toUpperCase", "getClassPlayerUpdateStatus", "PLAYER_UPDATE_STATUS", "pending", "rejected", "approved", "scrollToFirstInvalidControl", "formID", "form", "document", "getElementById", "firstInvalidControls", "getElementsByClassName", "firstInvalidControl", "scrollIntoView", "focus", "customSearchInput", "tableID", "search_container", "label", "querySelector", "search_input", "classList", "add", "remove", "openFullscreen", "element_id", "elem", "requestFullscreen", "webkitRequestFullscreen", "msRequestFullscreen", "closeFullscreen", "exitFullscreen", "webkitExitFullscreen", "toggleFullScreen", "console", "log", "fullscreenElement", "webkitIsFullScreen", "mozFullScreen", "msFullscreenElement", "toggleFullScreenCss", "_asyncToGenerator", "content", "contains", "isNativePlatform", "getPlatform", "lock", "orientation", "hide", "setBackgroundColor", "isElementInFullScreenCss", "showVideoControls", "video_container", "video_controls", "setTimeout", "checkMediaDevicePermission", "permission", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "audio", "getTracks", "for<PERSON>ach", "track", "stop", "e", "error", "getAzurePlayBackUrl", "filePath", "containerName", "livekit", "azureStorage", "accountName", "getAzureHLSPlayBackUrl", "playlistName", "getAzureHLSPlayBackUrlDummny", "match_id", "app_name", "i0", "ɵɵinject", "i1", "TranslateService", "i2", "NgbModal", "i3", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\commons.service.ts"], "sourcesContent": ["import { KeyValue } from '@angular/common';\r\nimport { Injectable } from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig, coreConfig } from 'app/app-config';\r\nimport { environment } from 'environments/environment';\r\nimport { AuthService } from './auth.service';\r\nimport { Capacitor } from '@capacitor/core';\r\nimport { StatusBar } from '@capacitor/status-bar';\r\nimport { NavigationBar } from '@hugotomazi/capacitor-navigation-bar';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TermConditionComponent } from 'app/components/term-condition/term-condition.component';\r\nimport { ScreenOrientation } from '@capacitor/screen-orientation';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CommonsService {\r\n  public dataTableDefaults = {\r\n    dom: 'B<\"row ml-2 mr-2\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-2 pr-2\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\r\n    dom_notB:\r\n      '<\"row mt-1 ml-2 mr-2\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-2 pr-2\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\r\n    dom_m25:\r\n      'B<\"row ml-25 mr-25\"<\"col-3 p-0 mb-50\"l><\"col-12 p-0 col-md-9 col-lg-9 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-25 pr-25\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\r\n    dom_table_card:\r\n      '<\"row ml-25 mr-0\"<\"col-12 col-md-9 col-lg-9 p-0\"B><\"col-12 p-0 col-md-3 col-lg-3 d-flex d-md-block d-lg-block\"f>>rt<\"row pl-25 pr-25\"<\"col-md-6 col-12\"i><\"col-md-6 col-12\"p>>',\r\n    buttons: {\r\n      dom: {\r\n        container: { className: 'dt-buttons ml-2 mb-1' },\r\n        button: { className: 'btn btn-primary mt-25' },\r\n      },\r\n      dom_table_card: {\r\n        container: {\r\n          className: 'dt-buttons m-0',\r\n        },\r\n        button: { className: 'btn btn-primary' },\r\n      },\r\n    },\r\n    lengthMenu: [\r\n      [10, 25, 50, 100, 200, 500, 1000, -1],\r\n      [10, 25, 50, 100, 200, 500, 1000, this._translateService.instant('All')],\r\n    ],\r\n    lang: {\r\n      decimal: '',\r\n      emptyTable: this._translateService.instant('No data available in table'),\r\n      info: this._translateService.instant(\r\n        'Showing _START_ to _END_ of _TOTAL_ entries'\r\n      ),\r\n      infoEmpty: this._translateService.instant('Showing 0 to 0 of 0 entries'),\r\n      infoFiltered: this._translateService.instant(\r\n        '(filtered from _MAX_ total entries)'\r\n      ),\r\n      infoPostFix: '',\r\n      thousands: ',',\r\n      lengthMenu: this._translateService.instant('Show _MENU_ entries'),\r\n      loadingRecords: `${this._translateService.instant('Loading')}...`,\r\n      processing: this._translateService.instant('Processing...'),\r\n      search: `${this._translateService.instant('Search')}:`,\r\n      zeroRecords: this._translateService.instant('No matching records found'),\r\n      paginate: {\r\n        first: this._translateService.instant('First'),\r\n        last: this._translateService.instant('Last'),\r\n        next: \"<i class=''></i>\",\r\n        previous: \"<i class=''></i>\",\r\n      },\r\n      select: {\r\n        rows: {\r\n          _: this._translateService.instant('Selected %d rows'),\r\n          0: this._translateService.instant('Click a row to select'),\r\n          1: this._translateService.instant('Selected 1 row'),\r\n        },\r\n      },\r\n    },\r\n  };\r\n\r\n  public regex_english_name: RegExp = /^[a-z]+(([\\',.-]?[ ]?[a-z])?[a-z]*)*$/i;\r\n  public regex_chinese_name: RegExp =\r\n    /^[a-zA-Z \\u3400-\\u9fa5\\ufeff\\u00A0\\u202F\\uFF0C]*$/u;\r\n  // public regex_name: RegExp =\r\n  // /^[a-zA-Z\\u3400-\\u9fa5\\u00C0-\\u1EF9\\ufeff\\u00A0\\u202F\\uFF0C]+(([\\'’,. -][a-z \\u3400-\\u9fa5\\u00C0-\\u1EF9\\ufeff\\u00A0\\u202F\\uFF0C])?[a-z \\u3400-\\u9fa5\\u00C0-\\u1EF9\\ufeff\\u00A0\\u202F\\uFF0C]*)*$/i;\r\n  public regex_name: RegExp =\r\n    /^\\p{L}([.,'’-]?\\s?[\\p{L}\\p{M}]+)*[\\p{L}\\p{M}]*$/u;\r\n\r\n  public simpleUploadConfig = {\r\n    // The URL that the images are uploaded to.\r\n    uploadUrl: `${environment.apiUrl}/send-messages/upload`,\r\n    headers: {\r\n      Accept: 'application/json, text/plain, */*',\r\n      'X-CSRF-TOKEN': 'CSRF-Token',\r\n      Authorization: `Bearer ${this._authService.currentUserValue?.token}`,\r\n      'X-project-id': AppConfig.PROJECT_ID,\r\n    },\r\n  };\r\n\r\n  constructor(\r\n    public _translateService: TranslateService,\r\n    public _modalService: NgbModal,\r\n    public _authService: AuthService\r\n  ) {}\r\n  removeSpaceInName(str) {\r\n    let strArray = str.split(' ');\r\n    let newStr = '';\r\n    //join string together if there is not space\r\n    if (strArray.length == 1) {\r\n      newStr = strArray[0];\r\n    } else {\r\n      for (let i = 0; i < strArray.length; i++) {\r\n        if (strArray[i] != '') {\r\n          // newStr +=strArray[i].charAt(0).toUpperCase() + strArray[i].slice(1) + ' ';\r\n          newStr += strArray[i] + ' ';\r\n        }\r\n      }\r\n    }\r\n    return (str = newStr.trim());\r\n  }\r\n\r\n  saveState(tableId, table) {\r\n    localStorage.setItem(tableId, JSON.stringify(table.state()));\r\n  }\r\n\r\n  loadState(tableId) {\r\n    let state = localStorage.getItem(tableId);\r\n    if (state) {\r\n      return JSON.parse(state);\r\n    }\r\n  }\r\n\r\n  onloadImgErr(event) {\r\n    event.target.src = coreConfig.app.appLogoImage;\r\n  }\r\n\r\n  openTNCGroupModal() {\r\n    const modalRef = this._modalService.open(TermConditionComponent, {\r\n      centered: true,\r\n      backdrop: 'static',\r\n      scrollable: true,\r\n    });\r\n  }\r\n\r\n  getBadgeClassPayment(status: string) {\r\n    if (status == null) {\r\n      let pending_text = this._translateService.instant('Pending');\r\n      return `<span class=\"badge badge-light-secondary\">${pending_text}</span>`;\r\n    }\r\n    let color = 'secondary';\r\n    status = this._translateService.instant(status.toLowerCase());\r\n\r\n    switch (status) {\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.succeeded):\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.paid):\r\n        status = 'paid';\r\n        color = 'light-success';\r\n        break;\r\n      case this._translateService.instant(\r\n        AppConfig.PAYMENT_STATUS.marked_as_paid\r\n      ):\r\n        color = 'light-success';\r\n        status = 'marked as paid';\r\n        break;\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.sent):\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.open):\r\n        status = 'sent';\r\n        color = 'light-warning';\r\n        break;\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.failed):\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.cancelled):\r\n        color = 'light-danger';\r\n        break;\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.refunded):\r\n      case AppConfig.PAYMENT_STATUS.marked_as_refunded:\r\n        color = 'info';\r\n        status = this._translateService.instant('refunded');\r\n        break;\r\n      case this._translateService.instant(\r\n        AppConfig.PAYMENT_STATUS.partially_refunded\r\n      ):\r\n        color = 'warning';\r\n      case this._translateService.instant(AppConfig.PAYMENT_STATUS.draft):\r\n        color = 'light-secondary';\r\n      default:\r\n        break;\r\n    }\r\n\r\n    return `<span class=\"badge badge-${color} text-capitalize\">${this._translateService.instant(\r\n      status\r\n    )}</span>`;\r\n  }\r\n\r\n  getBadgeClassBlacklist(status: string) {\r\n    if (status == null) {\r\n      let pending_text = this._translateService.instant('N/A');\r\n      return `<span class=\"badge badge-light-secondary\">${pending_text}</span>`;\r\n    }\r\n    let color = 'secondary';\r\n    status = this._translateService.instant(status);\r\n\r\n    switch (status) {\r\n      case this._translateService.instant(AppConfig.BLACK_LIST_STATUS.unsubscribed):\r\n        color = 'light-warning';\r\n        break;\r\n      case this._translateService.instant(AppConfig.BLACK_LIST_STATUS.block):\r\n        color = 'light-danger';\r\n        break;\r\n      default:\r\n        color = 'light-secondary';\r\n        break;\r\n    }\r\n\r\n    return `<span class=\"badge badge-${color} text-capitalize\">${this._translateService.instant(\r\n      status\r\n    )}</span>`;\r\n  }\r\n\r\n  getTitleColorPayment(status: string) {\r\n    let color = 'secondary';\r\n    if (status == null)\r\n      return {\r\n        color: color,\r\n        title: this._translateService.instant('No invoice'),\r\n      };\r\n\r\n    status = status.toLowerCase();\r\n    switch (status) {\r\n      case AppConfig.PAYMENT_STATUS.succeeded:\r\n      case AppConfig.PAYMENT_STATUS.paid:\r\n        status = 'paid';\r\n        color = 'success';\r\n        break;\r\n      case AppConfig.PAYMENT_STATUS.marked_as_paid:\r\n        color = 'success';\r\n        status = 'marked as paid';\r\n        break;\r\n      case AppConfig.PAYMENT_STATUS.sent:\r\n      case AppConfig.PAYMENT_STATUS.open:\r\n        status = 'sent';\r\n        color = 'warning';\r\n        break;\r\n      case AppConfig.PAYMENT_STATUS.failed:\r\n      case AppConfig.PAYMENT_STATUS.cancelled:\r\n        color = 'danger';\r\n        status = this._translateService.instant('cancelled');\r\n        break;\r\n      case AppConfig.PAYMENT_STATUS.refunded:\r\n      case AppConfig.PAYMENT_STATUS.marked_as_refunded:\r\n        color = 'info';\r\n        status = 'refunded';\r\n        break;\r\n      case AppConfig.PAYMENT_STATUS.partially_refunded:\r\n        color = 'warning';\r\n      default:\r\n        color = 'secondary';\r\n        break;\r\n    }\r\n\r\n    return {\r\n      color: color,\r\n      title: this._translateService.instant(status),\r\n    };\r\n  }\r\n\r\n  getBadgeRegistration(status) {\r\n    switch (status) {\r\n      case AppConfig.APPROVE_STATUS.Registered:\r\n        return 'badge-light-info';\r\n      case AppConfig.APPROVE_STATUS.Rejected:\r\n        return 'badge-light-danger';\r\n      case AppConfig.APPROVE_STATUS.Approved:\r\n        return 'badge-light-success';\r\n      default:\r\n        return 'badge-light-secondary';\r\n    }\r\n  }\r\n\r\n  getBadgeValidateStatus(status) {\r\n    let pending_text = this._translateService.instant('Pending');\r\n    let awaiting_text = this._translateService.instant('Awaiting Update');\r\n    let updated_text = this._translateService.instant('Updated');\r\n    let validated_text = this._translateService.instant('Validated');\r\n    if (status == null) {\r\n      return ``;\r\n    }\r\n    switch (status) {\r\n      case AppConfig.VALIDATE_STATUS.Pending:\r\n        return `<span class=\"badge badge-light-info\">${pending_text}</span>`;\r\n      case AppConfig.VALIDATE_STATUS.AwaitingUpdate:\r\n        return `<span class=\"badge badge-light-danger\">${awaiting_text}</span>`;\r\n      case AppConfig.VALIDATE_STATUS.Updated:\r\n        return `<span class=\"badge badge-light-warning\">${updated_text}</span>`;\r\n      case AppConfig.VALIDATE_STATUS.Validated:\r\n        return `<span class=\"badge badge-light-success\">${validated_text}</span>`;\r\n      default:\r\n        return `<span class=\"badge badge-light-secondary\">${status.toUpperCase()}</span>`;\r\n    }\r\n  }\r\n\r\n  getClassPlayerUpdateStatus(status: string) {\r\n    switch (status?.toLowerCase()) {\r\n      case AppConfig.PLAYER_UPDATE_STATUS.pending.toLowerCase():\r\n        return 'badge badge-light-info';\r\n      case AppConfig.PLAYER_UPDATE_STATUS.rejected.toLowerCase():\r\n        return 'badge badge-light-danger';\r\n      case AppConfig.PLAYER_UPDATE_STATUS.approved.toLowerCase():\r\n        return 'badge badge-light-success';\r\n      default:\r\n        return 'badge badge-light-secondary';\r\n    }\r\n  }\r\n\r\n  scrollToFirstInvalidControl(formID) {\r\n    let form = document.getElementById(formID);\r\n    if (!form) return;\r\n    let firstInvalidControls = form.getElementsByClassName('ng-invalid');\r\n    if (firstInvalidControls.length == 0) return;\r\n    let firstInvalidControl = firstInvalidControls[0];\r\n    firstInvalidControl.scrollIntoView();\r\n    (firstInvalidControl as HTMLElement).focus();\r\n  }\r\n\r\n  originalOrder = (\r\n    a: KeyValue<number, string>,\r\n    b: KeyValue<number, string>\r\n  ): number => {\r\n    return 0;\r\n  };\r\n\r\n  // custom search input\r\n  customSearchInput(tableID) {\r\n    let search_container = document.getElementById(`${tableID}_filter`);\r\n    let label = search_container.querySelector('label');\r\n    let search_input = search_container.querySelector('input[type=\"search\"]');\r\n    // add class w-100\r\n    if (search_container) search_container.classList.add('w-100');\r\n    if (label) label.classList.add('w-100');\r\n    // remove class form-control-sm\r\n    if (search_input) search_input.classList.remove('form-control-sm');\r\n    // add class m-0\r\n    if (search_input) search_input.classList.add(...['m-0', 'w-100']);\r\n  }\r\n\r\n  openFullscreen(element_id: string) {\r\n    const elem = document.getElementById(element_id) as any;\r\n    if (elem.requestFullscreen) {\r\n      elem.requestFullscreen();\r\n    } else if (elem.webkitRequestFullscreen) {\r\n      elem.webkitRequestFullscreen();\r\n    } else if (elem.msRequestFullscreen) {\r\n      elem.msRequestFullscreen();\r\n    }\r\n  }\r\n\r\n  closeFullscreen() {\r\n    if (document.exitFullscreen) {\r\n      document.exitFullscreen();\r\n    } else if ((document as any).webkitExitFullscreen) {\r\n      (document as any).webkitExitFullscreen();\r\n    }\r\n  }\r\n\r\n  toggleFullScreen(element_id: string) {\r\n    console.log('toggle fullscreen', element_id);\r\n\r\n    if (\r\n      !document.fullscreenElement &&\r\n      !(document as any).webkitIsFullScreen &&\r\n      !(document as any).mozFullScreen &&\r\n      !(document as any).msFullscreenElement\r\n    ) {\r\n      this.openFullscreen(element_id);\r\n    } else {\r\n      this.closeFullscreen();\r\n    }\r\n  }\r\n\r\n  async toggleFullScreenCss(element_id: string) {\r\n    // console.log('toggle fullscreen css', element_id);\r\n    const elem = document.getElementById(element_id) as any;\r\n    const content = document.getElementsByClassName('app-content')[0] as any;\r\n    if (elem.classList.contains('fullscreen')) {\r\n      elem.classList.remove('fullscreen');\r\n      if (content) {\r\n        if (Capacitor.isNativePlatform()) {\r\n          // StatusBar.setBackgroundColor({ color: '#ffffff' });\r\n        }\r\n        content.classList.remove('fullscreen');\r\n      }\r\n    } else {\r\n      elem.classList.add('fullscreen');\r\n      // if has safeAreaInsets, set width height follow safeAreaInsets\r\n      if (Capacitor.getPlatform() == 'ios') {\r\n        // add class ios\r\n        elem.classList.add('ios');\r\n      }\r\n      if (content) {\r\n        if (Capacitor.isNativePlatform()) {\r\n          await ScreenOrientation.lock({ orientation: 'landscape' });\r\n          await StatusBar.hide();\r\n          StatusBar.setBackgroundColor({ color: '#000000' });\r\n          // hide navigation bar\r\n          await NavigationBar.hide();\r\n        }\r\n        content.classList.add('fullscreen');\r\n      }\r\n    }\r\n  }\r\n\r\n  isElementInFullScreenCss(element_id: string) {\r\n    const elem = document.getElementById(element_id) as any;\r\n    return elem.classList.contains('fullscreen');\r\n  }\r\n\r\n  // click .video-container to show .video-controls and hide after 1.5s\r\n  showVideoControls() {\r\n    let video_container = document.querySelector('.video-container');\r\n    let video_controls = document.querySelector('.video-controls');\r\n    // if (video_container && video_controls) {\r\n    console.log('listen click video container');\r\n\r\n    //   video_container.addEventListener('click', () => {\r\n    if (video_controls.classList.contains('show')) return;\r\n    video_controls.classList.add('show');\r\n    setTimeout(() => {\r\n      video_controls.classList.remove('show');\r\n    }, 2500);\r\n    // });\r\n    // }\r\n  }\r\n\r\n  async checkMediaDevicePermission() {\r\n    let permission = false;\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        video: true,\r\n        audio: true,\r\n      });\r\n      permission = true;\r\n      stream.getTracks().forEach((track) => {\r\n        track.stop();\r\n      });\r\n      console.log('permission', permission);\r\n    } catch (e) {\r\n      console.error('error', e);\r\n    }\r\n    return permission;\r\n  }\r\n\r\n  getAzurePlayBackUrl(\r\n    filePath: string,\r\n    containerName: string = environment.livekit.azureStorage.containerName\r\n  ) {\r\n    return `https://${environment.livekit.azureStorage.accountName}.blob.core.windows.net/${containerName}/${filePath}`;\r\n  }\r\n\r\n  getAzureHLSPlayBackUrl(\r\n    playlistName: string,\r\n    containerName: string = environment.livekit.azureStorage.containerName\r\n  ) {\r\n    return `https://${environment.livekit.azureStorage.accountName}.blob.core.windows.net/${containerName}/${playlistName}`;\r\n  }\r\n\r\n  getAzureHLSPlayBackUrlDummny(\r\n    match_id: string,\r\n    containerName: string = environment.livekit.azureStorage.containerName\r\n  ) {\r\n    return `https://${environment.livekit.azureStorage.accountName}.blob.core.windows.net/${containerName}/${environment.app_name}/${match_id}.m3u8`;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
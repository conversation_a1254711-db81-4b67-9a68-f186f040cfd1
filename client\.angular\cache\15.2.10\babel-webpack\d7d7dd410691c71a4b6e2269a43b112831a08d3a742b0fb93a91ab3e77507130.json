{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport { SelectRoleModalComponent } from '../select-role-modal/select-role-modal.component';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"app/services/stage.service\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../../@core/pipes/localized-date.pipe\";\nfunction JoinStreamComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h3\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"hr\");\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"p\", 8)(8, \"b\");\n    i0.ɵɵtext(9, \"Match Info\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\");\n    i0.ɵɵelement(12, \"i\", 10);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵelement(15, \"i\", 11);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵelement(18, \"i\", 12);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"localizedDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\");\n    i0.ɵɵelement(22, \"i\", 13);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 14)(25, \"p\", 8)(26, \"b\");\n    i0.ɵɵtext(27, \"Role in stream\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 9)(29, \"div\");\n    i0.ɵɵelement(30, \"i\", 15);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(33, \"svg\", 16);\n    i0.ɵɵelement(34, \"path\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(36, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function JoinStreamComponent_div_4_Template_button_click_36_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const item_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openModalSelectRole(item_r1));\n    });\n    i0.ɵɵtext(37, \"Join Stream\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", item_r1.home_team.name, \" vs \", item_r1.away_team.name, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" League: \", item_r1.tournament.name, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Location: \", item_r1.location.name, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Date: \", i0.ɵɵpipeBind2(20, 9, item_r1.start_time, \"dd MMM yyyy\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" Time: \", item_r1.start_time_short, \" - \", item_r1.end_time_short, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Cameraman: \", item_r1.camera_name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Controller: \", item_r1.controller_name, \"\");\n  }\n}\nexport class JoinStreamComponent {\n  constructor(_authService, _stageService, loadingService, route, _trans, modalService) {\n    this._authService = _authService;\n    this._stageService = _stageService;\n    this.loadingService = loadingService;\n    this.route = route;\n    this._trans = _trans;\n    this.modalService = modalService;\n    this.matches = [];\n    this.permissions = {\n      manage_streaming: false\n    };\n    this.permissions.manage_streaming = this._authService.currentUserValue.role.permissions.find(x => x.id == AppConfig.PERMISSIONS.manage_streaming);\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['id'];\n      if (userId) {\n        this.getStreamingMatches(userId);\n      }\n    });\n  }\n  getStreamingMatches(userId) {\n    this.loadingService.show(); // Show loading spinner\n    this._stageService.getUserStreamingMatches(userId).subscribe(res => {\n      this.matches = res.data;\n    }, error => {\n      console.error('Error fetching streaming matches:', error);\n    });\n  }\n  openModalSelectRole(item) {\n    console.log(item, \"--\");\n    if (this.permissions.manage_streaming) {\n      const modalRef = this.modalService.open(SelectRoleModalComponent, {\n        size: 'base',\n        centered: true,\n        backdrop: 'static'\n      });\n      modalRef.componentInstance.match = item;\n    } else {\n      Swal.fire({\n        imageUrl: \"assets/images/Frame.png\",\n        title: this._trans.instant('Opps...'),\n        text: this._trans.instant('You do not have permission to access this match')\n      });\n    }\n  }\n  static #_ = this.ɵfac = function JoinStreamComponent_Factory(t) {\n    return new (t || JoinStreamComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.StageService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.NgbModal));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: JoinStreamComponent,\n    selectors: [[\"app-join-stream\"]],\n    decls: 5,\n    vars: 1,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"row\"], [\"class\", \"col-12 col-md-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-md-6\"], [1, \"card\"], [1, \"card-header\", \"pb-0\"], [1, \"card-title\"], [1, \"card-body\", \"pt-0\", \"pb-1\"], [1, \"mb-1\"], [1, \"info-text\"], [1, \"bi\", \"bi-trophy\"], [1, \"bi\", \"bi-geo-alt\"], [1, \"bi\", \"bi-calendar\"], [1, \"bi\", \"bi-clock\"], [1, \"card-footer\", \"pt-1\"], [1, \"bi\", \"bi-camera-video\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 20 20\", \"fill\", \"none\"], [\"d\", \"M12.5641 12.2433C12.4008 12.2433 12.2638 12.188 12.1533 12.0775C12.0422 11.9669 11.9866 11.83 11.9866 11.6666V8.3333C11.9866 8.16997 12.0422 8.03302 12.1533 7.92247C12.2638 7.81136 12.4008 7.7558 12.5641 7.7558H14.6474C14.8108 7.7558 14.9477 7.81136 15.0583 7.92247C15.1688 8.03358 15.2244 8.17052 15.2249 8.3333V11.6666C15.2249 11.83 15.1694 11.9669 15.0583 12.0775C14.9472 12.188 14.8102 12.2436 14.6474 12.2441L12.5641 12.2433ZM12.7241 11.5066H14.4874V8.4933H12.7241V11.5066ZM5.51326 11.5066H7.6441C7.75076 11.5066 7.83882 11.5414 7.90826 11.6108C7.97826 11.6802 8.01326 11.768 8.01326 11.8741C8.01326 11.9808 7.97826 12.0689 7.90826 12.1383C7.83882 12.2083 7.75076 12.2433 7.6441 12.2433H5.35243C5.1891 12.2433 5.05215 12.188 4.9416 12.0775C4.83104 11.9669 4.77549 11.83 4.77493 11.6666V10.32C4.77493 10.1589 4.8416 10.0116 4.97493 9.8783C5.10826 9.74608 5.25549 9.67997 5.4166 9.67997H7.27493V8.4933H5.14493C5.03882 8.4933 4.95076 8.45858 4.88076 8.38913C4.81132 8.31969 4.7766 8.23191 4.7766 8.1258C4.7766 8.01969 4.81132 7.93163 4.88076 7.86163C4.95021 7.79163 5.03826 7.75663 5.14493 7.75663H7.4366C7.59993 7.75663 7.73687 7.81191 7.84743 7.92247C7.95854 8.03302 8.0141 8.16997 8.0141 8.3333V9.67997C8.0141 9.84108 7.94743 9.9883 7.8141 10.1216C7.68076 10.2544 7.53354 10.3208 7.37243 10.3208H5.5141L5.51326 11.5066ZM3.8141 15.6566C3.43076 15.6566 3.11049 15.5283 2.85326 15.2716C2.59604 15.015 2.46771 14.6947 2.46826 14.3108V5.7858C2.46826 5.40191 2.5966 5.08163 2.85326 4.82497C3.10993 4.5683 3.43021 4.43969 3.8141 4.43913H6.24993V3.18913C6.24993 3.0708 6.28993 2.97191 6.36993 2.89247C6.44993 2.81302 6.5491 2.77302 6.66743 2.77247C6.78576 2.77191 6.88465 2.81191 6.9641 2.89247C7.04354 2.97302 7.08326 3.07191 7.08326 3.18913V4.43913H12.9166V3.18913C12.9166 3.0708 12.9566 2.97191 13.0366 2.89247C13.1166 2.81302 13.2158 2.77302 13.3341 2.77247C13.4524 2.77191 13.5513 2.81191 13.6308 2.89247C13.7102 2.97302 13.7499 3.07191 13.7499 3.18913V4.43913H16.1858C16.5691 4.43913 16.8894 4.56774 17.1466 4.82497C17.4038 5.08219 17.5324 5.40219 17.5324 5.78497V14.3108C17.5324 14.6941 17.4038 15.0144 17.1466 15.2716C16.8894 15.5289 16.5691 15.6575 16.1858 15.6575L3.8141 15.6566ZM3.8141 14.8233H9.63076V14.135C9.63076 14.0283 9.66576 13.9402 9.73576 13.8708C9.80576 13.8014 9.89354 13.7664 9.9991 13.7658C10.1047 13.7652 10.1927 13.8002 10.2633 13.8708C10.3338 13.9414 10.3688 14.0294 10.3683 14.135V14.8241H16.1858C16.3135 14.8241 16.431 14.7705 16.5383 14.6633C16.6455 14.5566 16.6991 14.4391 16.6991 14.3108V5.7858C16.6991 5.65747 16.6455 5.53969 16.5383 5.43247C16.4316 5.3258 16.3141 5.27247 16.1858 5.27247H10.3691V5.96163C10.3691 6.0683 10.3341 6.15636 10.2641 6.2258C10.1947 6.29524 10.1069 6.32997 10.0008 6.32997C9.89465 6.32997 9.8066 6.29524 9.7366 6.2258C9.6666 6.15636 9.6316 6.0683 9.6316 5.96163V5.27247H3.8141C3.68632 5.27247 3.56882 5.3258 3.4616 5.43247C3.35437 5.53913 3.30104 5.65663 3.3016 5.78497V14.3108C3.3016 14.4386 3.35493 14.5561 3.4616 14.6633C3.56826 14.7705 3.68576 14.8233 3.8141 14.8233ZM9.99993 9.05497C9.89382 9.05497 9.80576 9.01997 9.73576 8.94997C9.66576 8.88052 9.63076 8.79274 9.63076 8.68663C9.63076 8.58052 9.66549 8.49247 9.73493 8.42247C9.80437 8.35247 9.89215 8.31747 9.99826 8.31747C10.1044 8.31747 10.1924 8.35219 10.2624 8.42163C10.3324 8.49108 10.3674 8.57886 10.3674 8.68497C10.3674 8.79108 10.3327 8.87941 10.2633 8.94997C10.1938 9.02052 10.106 9.05552 9.99993 9.05497ZM9.99993 11.7791C9.89382 11.7791 9.80576 11.7444 9.73576 11.675C9.66576 11.6055 9.63076 11.5175 9.63076 11.4108C9.63076 11.3041 9.66549 11.2161 9.73493 11.1466C9.80437 11.0772 9.89215 11.0422 9.99826 11.0416C10.1044 11.0411 10.1924 11.0758 10.2624 11.1458C10.3324 11.2158 10.3674 11.3039 10.3674 11.41C10.3674 11.5161 10.3327 11.6041 10.2633 11.6741C10.1938 11.7441 10.106 11.7791 9.99993 11.7791Z\", \"fill\", \"#4B4B4B\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"btn-block\", \"mt-1\", 3, \"click\"]],\n    template: function JoinStreamComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\");\n        i0.ɵɵtext(2, \"You are assigned to the matches\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 1);\n        i0.ɵɵtemplate(4, JoinStreamComponent_div_4_Template, 38, 12, \"div\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.matches);\n      }\n    },\n    dependencies: [i7.NgForOf, i8.LocalizedDatePipe],\n    styles: [\".info-text[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  opacity: 0.9;\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.info-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1.2rem;\\n}\\n\\n.info-text[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1.2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3RyZWFtaW5nL2pvaW4tc3RyZWFtL2pvaW4tc3RyZWFtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksY0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxxQkFBQTtBQUNKOztBQUdBO0VBQ0ksaUJBQUE7RUFDQSxpQkFBQTtBQUFKOztBQUdBO0VBQ0ksaUJBQUE7RUFDQSxpQkFBQTtBQUFKIiwic291cmNlc0NvbnRlbnQiOlsiLmluZm8tdGV4dCBkaXYge1xyXG4gICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICBvcGFjaXR5OiAwLjk7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxufVxyXG5cclxuXHJcbi5pbmZvLXRleHQgaSB7XHJcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcclxuICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xyXG59XHJcblxyXG4uaW5mby10ZXh0IHN2ZyB7XHJcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcclxuICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,SAAS,QAAQ,gBAAgB;AAI1C,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;ICHtBC,EAAA,CAAAC,cAAA,aAA0D;IAGvBD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAElFH,EAAA,CAAAI,SAAA,SAAI;IACJJ,EAAA,CAAAC,cAAA,aAAiC;IACVD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjCH,EAAA,CAAAC,cAAA,cAAuB;IACdD,EAAA,CAAAI,SAAA,aAA4B;IAACJ,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxEH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAI,SAAA,aAA6B;IAACJ,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAI,SAAA,aAA8B;IAACJ,EAAA,CAAAE,MAAA,IAAuD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjGH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAI,SAAA,aAA2B;IAACJ,EAAA,CAAAE,MAAA,IAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGzGH,EAAA,CAAAC,cAAA,eAA8B;IACPD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrCH,EAAA,CAAAC,cAAA,cAAuB;IACdD,EAAA,CAAAI,SAAA,aAAkC;IAACJ,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAK,cAAA,EAA+F;IAA/FL,EAAA,CAAAC,cAAA,eAA+F;IAChGD,EAAA,CAAAI,SAAA,gBAA4wH;IAC9wHJ,EAAA,CAAAG,YAAA,EAAM;IAAAH,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEtDH,EAAA,CAAAM,eAAA,EAAgH;IAAhHN,EAAA,CAAAC,cAAA,kBAAgH;IAApCD,EAAA,CAAAO,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,WAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,mBAAA,CAAAL,OAAA,CAAyB;IAAA,EAAC;IAACZ,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IApB7GH,EAAA,CAAAkB,SAAA,GAAkD;IAAlDlB,EAAA,CAAAmB,kBAAA,KAAAP,OAAA,CAAAQ,SAAA,CAAAC,IAAA,UAAAT,OAAA,CAAAU,SAAA,CAAAD,IAAA,KAAkD;IAMnCrB,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAAuB,kBAAA,cAAAX,OAAA,CAAAY,UAAA,CAAAH,IAAA,KAAgC;IAC/BrB,EAAA,CAAAkB,SAAA,GAAgC;IAAhClB,EAAA,CAAAuB,kBAAA,gBAAAX,OAAA,CAAAa,QAAA,CAAAJ,IAAA,KAAgC;IAC/BrB,EAAA,CAAAkB,SAAA,GAAuD;IAAvDlB,EAAA,CAAAuB,kBAAA,YAAAvB,EAAA,CAAA0B,WAAA,QAAAd,OAAA,CAAAe,UAAA,qBAAuD;IAC1D3B,EAAA,CAAAkB,SAAA,GAA0D;IAA1DlB,EAAA,CAAAmB,kBAAA,YAAAP,OAAA,CAAAgB,gBAAA,SAAAhB,OAAA,CAAAiB,cAAA,KAA0D;IAMnD7B,EAAA,CAAAkB,SAAA,GAA+B;IAA/BlB,EAAA,CAAAuB,kBAAA,iBAAAX,OAAA,CAAAkB,WAAA,KAA+B;IAG/D9B,EAAA,CAAAkB,SAAA,GAAoC;IAApClB,EAAA,CAAAuB,kBAAA,iBAAAX,OAAA,CAAAmB,eAAA,KAAoC;;;ADTpE,OAAM,MAAOC,mBAAmB;EAK9BC,YACSC,YAAyB,EACzBC,aAA2B,EAC3BC,cAA8B,EAC7BC,KAAqB,EACtBC,MAAwB,EACxBC,YAAsB;IALtB,KAAAL,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,KAAK,GAALA,KAAK;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAVrB,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,WAAW,GAAQ;MACjBC,gBAAgB,EAAE;KACnB;IASC,IAAI,CAACD,WAAW,CAACC,gBAAgB,GAC/B,IAAI,CAACR,YAAY,CAACS,gBAAgB,CAACC,IAAI,CAACH,WAAW,CAACI,IAAI,CACrDC,CAAC,IAAKA,CAAC,CAACC,EAAE,IAAIlD,SAAS,CAACmD,WAAW,CAACN,gBAAgB,CACtD;EACL;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACZ,KAAK,CAACa,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,MAAME,MAAM,GAAGF,MAAM,CAAC,IAAI,CAAC;MAC3B,IAAIE,MAAM,EAAE;QACV,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAAC;;IAEpC,CAAC,CAAC;EACJ;EAEAC,mBAAmBA,CAACD,MAAc;IAChC,IAAI,CAAChB,cAAc,CAACkB,IAAI,EAAE,CAAC,CAAC;IAC5B,IAAI,CAACnB,aAAa,CAACoB,uBAAuB,CAACH,MAAM,CAAC,CAACD,SAAS,CACzDK,GAAG,IAAI;MACN,IAAI,CAAChB,OAAO,GAAGgB,GAAG,CAACC,IAAI;IACzB,CAAC,EACAC,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D,CAAC,CACF;EACH;EAEAzC,mBAAmBA,CAAC2C,IAAI;IACtBD,OAAO,CAACE,GAAG,CAACD,IAAI,EAAC,IAAI,CAAC;IACtB,IAAI,IAAI,CAACnB,WAAW,CAACC,gBAAgB,EAAE;MACrC,MAAMoB,QAAQ,GAAG,IAAI,CAACvB,YAAY,CAACwB,IAAI,CAACjE,wBAAwB,EAAE;QAChEkE,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;OACX,CAAC;MACFJ,QAAQ,CAACK,iBAAiB,CAACC,KAAK,GAAGR,IAAI;KACxC,MAAM;MACL7D,IAAI,CAACsE,IAAI,CAAC;QACRC,QAAQ,EAAE,yBAAyB;QACnCC,KAAK,EAAE,IAAI,CAACjC,MAAM,CAACkC,OAAO,CAAC,SAAS,CAAC;QACrCC,IAAI,EAAE,IAAI,CAACnC,MAAM,CAACkC,OAAO,CAAC,iDAAiD;OAC5E,CAAC;;EAEN;EAAC,QAAAE,CAAA;qBAxDU1C,mBAAmB,EAAAhC,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7E,EAAA,CAAA2E,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA/E,EAAA,CAAA2E,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjF,EAAA,CAAA2E,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAnF,EAAA,CAAA2E,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAArF,EAAA,CAAA2E,iBAAA,CAAAW,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA;UAAnBxD,mBAAmB;IAAAyD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBhC/F,EAAA,CAAAC,cAAA,aAA+C;QACvCD,EAAA,CAAAE,MAAA,sCAA+B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACxCH,EAAA,CAAAC,cAAA,aAAkB;QAEdD,EAAA,CAAAiG,UAAA,IAAAC,kCAAA,mBA0BM;QACVlG,EAAA,CAAAG,YAAA,EAAM;;;QA3B4CH,EAAA,CAAAkB,SAAA,GAAU;QAAVlB,EAAA,CAAAmG,UAAA,YAAAH,GAAA,CAAAxD,OAAA,CAAU", "names": ["AppConfig", "SelectRoleModalComponent", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "JoinStreamComponent_div_4_Template_button_click_36_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "item_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openModalSelectRole", "ɵɵadvance", "ɵɵtextInterpolate2", "home_team", "name", "away_team", "ɵɵtextInterpolate1", "tournament", "location", "ɵɵpipeBind2", "start_time", "start_time_short", "end_time_short", "camera_name", "controller_name", "JoinStreamComponent", "constructor", "_authService", "_stageService", "loadingService", "route", "_trans", "modalService", "matches", "permissions", "manage_streaming", "currentUserValue", "role", "find", "x", "id", "PERMISSIONS", "ngOnInit", "params", "subscribe", "userId", "getStreamingMatches", "show", "getUserStreamingMatches", "res", "data", "error", "console", "item", "log", "modalRef", "open", "size", "centered", "backdrop", "componentInstance", "match", "fire", "imageUrl", "title", "instant", "text", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "StageService", "i3", "LoadingService", "i4", "ActivatedRoute", "i5", "TranslateService", "i6", "NgbModal", "_2", "selectors", "decls", "vars", "consts", "template", "JoinStreamComponent_Template", "rf", "ctx", "ɵɵtemplate", "JoinStreamComponent_div_4_Template", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\join-stream\\join-stream.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\join-stream\\join-stream.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { SelectRoleModalComponent } from '../select-role-modal/select-role-modal.component';\r\nimport Swal from 'sweetalert2';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\n@Component({\r\n  selector: 'app-join-stream',\r\n  templateUrl: './join-stream.component.html',\r\n  styleUrls: ['./join-stream.component.scss']\r\n})\r\nexport class JoinStreamComponent {\r\n  matches = [];\r\n  permissions: any = {\r\n    manage_streaming: false,\r\n  };\r\n  constructor(\r\n    public _authService: AuthService,\r\n    public _stageService: StageService,\r\n    public loadingService: LoadingService,\r\n    private route: ActivatedRoute,\r\n    public _trans: TranslateService,\r\n    public modalService: NgbModal,\r\n  ) {\r\n    this.permissions.manage_streaming =\r\n      this._authService.currentUserValue.role.permissions.find(\r\n        (x) => x.id == AppConfig.PERMISSIONS.manage_streaming\r\n      ); \r\n  }\r\n\r\n  ngOnInit() {\r\n    this.route.params.subscribe(params => {\r\n      const userId = params['id'];\r\n      if (userId) {\r\n        this.getStreamingMatches(userId);\r\n      }\r\n    });\r\n  }\r\n\r\n  getStreamingMatches(userId: string) {\r\n    this.loadingService.show(); // Show loading spinner\r\n    this._stageService.getUserStreamingMatches(userId).subscribe(\r\n      (res) => {\r\n        this.matches = res.data;\r\n      },\r\n      (error) => {\r\n        console.error('Error fetching streaming matches:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  openModalSelectRole(item) {\r\n    console.log(item,\"--\");\r\n    if (this.permissions.manage_streaming) {\r\n      const modalRef = this.modalService.open(SelectRoleModalComponent, {\r\n        size: 'base',\r\n        centered: true,\r\n        backdrop: 'static',\r\n      });\r\n      modalRef.componentInstance.match = item;\r\n    } else {\r\n      Swal.fire({\r\n        imageUrl: \"assets/images/Frame.png\",\r\n        title: this._trans.instant('Opps...'),\r\n        text: this._trans.instant('You do not have permission to access this match'),\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <h4>You are assigned to the matches</h4>\r\n    <div class=\"row\" >\r\n        <!-- Card 1 -->\r\n        <div class=\"col-12 col-md-6\" *ngFor=\"let item of matches\">\r\n            <div class=\"card\">\r\n                <div class=\"card-header pb-0\">\r\n                    <h3 class=\"card-title\">{{item.home_team.name}} vs {{item.away_team.name}}</h3>\r\n                </div>\r\n                <hr>\r\n                <div class=\"card-body pt-0 pb-1\">\r\n                    <p class=\"mb-1\"><b>Match Info</b></p>\r\n                    <div class=\"info-text\">\r\n                        <div><i class=\"bi bi-trophy\"></i> League: {{item.tournament.name}}</div>\r\n                        <div><i class=\"bi bi-geo-alt\"></i> Location: {{item.location.name}}</div>\r\n                        <div><i class=\"bi bi-calendar\"></i> Date: {{item.start_time | localizedDate:'dd MMM yyyy'}}</div>\r\n                        <div><i class=\"bi bi-clock\"></i> Time: {{item.start_time_short }} - {{item.end_time_short}}</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"card-footer pt-1\">\r\n                    <p class=\"mb-1\"><b>Role in stream</b></p>\r\n                    <div class=\"info-text\">\r\n                        <div><i class=\"bi bi-camera-video\"></i> Cameraman: {{item.camera_name}}</div>\r\n                        <div><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\">\r\n                            <path d=\"M12.5641 12.2433C12.4008 12.2433 12.2638 12.188 12.1533 12.0775C12.0422 11.9669 11.9866 11.83 11.9866 11.6666V8.3333C11.9866 8.16997 12.0422 8.03302 12.1533 7.92247C12.2638 7.81136 12.4008 7.7558 12.5641 7.7558H14.6474C14.8108 7.7558 14.9477 7.81136 15.0583 7.92247C15.1688 8.03358 15.2244 8.17052 15.2249 8.3333V11.6666C15.2249 11.83 15.1694 11.9669 15.0583 12.0775C14.9472 12.188 14.8102 12.2436 14.6474 12.2441L12.5641 12.2433ZM12.7241 11.5066H14.4874V8.4933H12.7241V11.5066ZM5.51326 11.5066H7.6441C7.75076 11.5066 7.83882 11.5414 7.90826 11.6108C7.97826 11.6802 8.01326 11.768 8.01326 11.8741C8.01326 11.9808 7.97826 12.0689 7.90826 12.1383C7.83882 12.2083 7.75076 12.2433 7.6441 12.2433H5.35243C5.1891 12.2433 5.05215 12.188 4.9416 12.0775C4.83104 11.9669 4.77549 11.83 4.77493 11.6666V10.32C4.77493 10.1589 4.8416 10.0116 4.97493 9.8783C5.10826 9.74608 5.25549 9.67997 5.4166 9.67997H7.27493V8.4933H5.14493C5.03882 8.4933 4.95076 8.45858 4.88076 8.38913C4.81132 8.31969 4.7766 8.23191 4.7766 8.1258C4.7766 8.01969 4.81132 7.93163 4.88076 7.86163C4.95021 7.79163 5.03826 7.75663 5.14493 7.75663H7.4366C7.59993 7.75663 7.73687 7.81191 7.84743 7.92247C7.95854 8.03302 8.0141 8.16997 8.0141 8.3333V9.67997C8.0141 9.84108 7.94743 9.9883 7.8141 10.1216C7.68076 10.2544 7.53354 10.3208 7.37243 10.3208H5.5141L5.51326 11.5066ZM3.8141 15.6566C3.43076 15.6566 3.11049 15.5283 2.85326 15.2716C2.59604 15.015 2.46771 14.6947 2.46826 14.3108V5.7858C2.46826 5.40191 2.5966 5.08163 2.85326 4.82497C3.10993 4.5683 3.43021 4.43969 3.8141 4.43913H6.24993V3.18913C6.24993 3.0708 6.28993 2.97191 6.36993 2.89247C6.44993 2.81302 6.5491 2.77302 6.66743 2.77247C6.78576 2.77191 6.88465 2.81191 6.9641 2.89247C7.04354 2.97302 7.08326 3.07191 7.08326 3.18913V4.43913H12.9166V3.18913C12.9166 3.0708 12.9566 2.97191 13.0366 2.89247C13.1166 2.81302 13.2158 2.77302 13.3341 2.77247C13.4524 2.77191 13.5513 2.81191 13.6308 2.89247C13.7102 2.97302 13.7499 3.07191 13.7499 3.18913V4.43913H16.1858C16.5691 4.43913 16.8894 4.56774 17.1466 4.82497C17.4038 5.08219 17.5324 5.40219 17.5324 5.78497V14.3108C17.5324 14.6941 17.4038 15.0144 17.1466 15.2716C16.8894 15.5289 16.5691 15.6575 16.1858 15.6575L3.8141 15.6566ZM3.8141 14.8233H9.63076V14.135C9.63076 14.0283 9.66576 13.9402 9.73576 13.8708C9.80576 13.8014 9.89354 13.7664 9.9991 13.7658C10.1047 13.7652 10.1927 13.8002 10.2633 13.8708C10.3338 13.9414 10.3688 14.0294 10.3683 14.135V14.8241H16.1858C16.3135 14.8241 16.431 14.7705 16.5383 14.6633C16.6455 14.5566 16.6991 14.4391 16.6991 14.3108V5.7858C16.6991 5.65747 16.6455 5.53969 16.5383 5.43247C16.4316 5.3258 16.3141 5.27247 16.1858 5.27247H10.3691V5.96163C10.3691 6.0683 10.3341 6.15636 10.2641 6.2258C10.1947 6.29524 10.1069 6.32997 10.0008 6.32997C9.89465 6.32997 9.8066 6.29524 9.7366 6.2258C9.6666 6.15636 9.6316 6.0683 9.6316 5.96163V5.27247H3.8141C3.68632 5.27247 3.56882 5.3258 3.4616 5.43247C3.35437 5.53913 3.30104 5.65663 3.3016 5.78497V14.3108C3.3016 14.4386 3.35493 14.5561 3.4616 14.6633C3.56826 14.7705 3.68576 14.8233 3.8141 14.8233ZM9.99993 9.05497C9.89382 9.05497 9.80576 9.01997 9.73576 8.94997C9.66576 8.88052 9.63076 8.79274 9.63076 8.68663C9.63076 8.58052 9.66549 8.49247 9.73493 8.42247C9.80437 8.35247 9.89215 8.31747 9.99826 8.31747C10.1044 8.31747 10.1924 8.35219 10.2624 8.42163C10.3324 8.49108 10.3674 8.57886 10.3674 8.68497C10.3674 8.79108 10.3327 8.87941 10.2633 8.94997C10.1938 9.02052 10.106 9.05552 9.99993 9.05497ZM9.99993 11.7791C9.89382 11.7791 9.80576 11.7444 9.73576 11.675C9.66576 11.6055 9.63076 11.5175 9.63076 11.4108C9.63076 11.3041 9.66549 11.2161 9.73493 11.1466C9.80437 11.0772 9.89215 11.0422 9.99826 11.0416C10.1044 11.0411 10.1924 11.0758 10.2624 11.1458C10.3324 11.2158 10.3674 11.3039 10.3674 11.41C10.3674 11.5161 10.3327 11.6041 10.2633 11.6741C10.1938 11.7441 10.106 11.7791 9.99993 11.7791Z\" fill=\"#4B4B4B\"/>\r\n                          </svg>Controller: {{item.controller_name}}</div>\r\n                    </div>\r\n                    <button type=\"button\" class=\"btn btn-outline-primary btn-sm btn-block mt-1\" (click)=\"openModalSelectRole(item)\">Join Stream</button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export const english = {\n  weekdays: {\n    shorthand: [\"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"Wed\", \"Thu\", \"<PERSON>i\", \"Sat\"],\n    longhand: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"]\n  },\n  months: {\n    shorthand: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"],\n    longhand: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"]\n  },\n  daysInMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n  firstDayOfWeek: 0,\n  ordinal: nth => {\n    const s = nth % 100;\n    if (s > 3 && s < 21) return \"th\";\n    switch (s % 10) {\n      case 1:\n        return \"st\";\n      case 2:\n        return \"nd\";\n      case 3:\n        return \"rd\";\n      default:\n        return \"th\";\n    }\n  },\n  rangeSeparator: \" to \",\n  weekAbbreviation: \"Wk\",\n  scrollTitle: \"Scroll to increment\",\n  toggleTitle: \"Click to toggle\",\n  amPM: [\"AM\", \"PM\"],\n  yearAriaLabel: \"Year\",\n  monthAriaLabel: \"Month\",\n  hourAriaLabel: \"Hour\",\n  minuteAriaLabel: \"Minute\",\n  time_24hr: false\n};\nexport default english;", "map": {"version": 3, "names": ["english", "weekdays", "shorthand", "longhand", "months", "daysInMonth", "firstDayOfWeek", "ordinal", "nth", "s", "rangeSeparator", "weekAbbreviation", "scrollTitle", "toggleTitle", "amPM", "yearAriaLabel", "monthAriaLabel", "hourAriaLabel", "minuteAriaLabel", "time_24hr"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/flatpickr/dist/esm/l10n/default.js"], "sourcesContent": ["export const english = {\n    weekdays: {\n        shorthand: [\"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"Wed\", \"Thu\", \"<PERSON>i\", \"Sat\"],\n        longhand: [\n            \"Sunday\",\n            \"Monday\",\n            \"Tuesday\",\n            \"Wednesday\",\n            \"Thursday\",\n            \"Friday\",\n            \"Saturday\",\n        ],\n    },\n    months: {\n        shorthand: [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\",\n        ],\n        longhand: [\n            \"January\",\n            \"February\",\n            \"March\",\n            \"April\",\n            \"May\",\n            \"June\",\n            \"July\",\n            \"August\",\n            \"September\",\n            \"October\",\n            \"November\",\n            \"December\",\n        ],\n    },\n    daysInMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n    firstDayOfWeek: 0,\n    ordinal: (nth) => {\n        const s = nth % 100;\n        if (s > 3 && s < 21)\n            return \"th\";\n        switch (s % 10) {\n            case 1:\n                return \"st\";\n            case 2:\n                return \"nd\";\n            case 3:\n                return \"rd\";\n            default:\n                return \"th\";\n        }\n    },\n    rangeSeparator: \" to \",\n    weekAbbreviation: \"Wk\",\n    scrollTitle: \"Scroll to increment\",\n    toggleTitle: \"Click to toggle\",\n    amPM: [\"AM\", \"PM\"],\n    yearAriaLabel: \"Year\",\n    monthAriaLabel: \"Month\",\n    hourAriaLabel: \"Hour\",\n    minuteAriaLabel: \"Minute\",\n    time_24hr: false,\n};\nexport default english;\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG;EACnBC,QAAQ,EAAE;IACNC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5DC,QAAQ,EAAE,CACN,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU;EAElB,CAAC;EACDC,MAAM,EAAE;IACJF,SAAS,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACR;IACDC,QAAQ,EAAE,CACN,SAAS,EACT,UAAU,EACV,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;EAElB,CAAC;EACDE,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC7DC,cAAc,EAAE,CAAC;EACjBC,OAAO,EAAGC,GAAG,IAAK;IACd,MAAMC,CAAC,GAAGD,GAAG,GAAG,GAAG;IACnB,IAAIC,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,EACf,OAAO,IAAI;IACf,QAAQA,CAAC,GAAG,EAAE;MACV,KAAK,CAAC;QACF,OAAO,IAAI;MACf,KAAK,CAAC;QACF,OAAO,IAAI;MACf,KAAK,CAAC;QACF,OAAO,IAAI;MACf;QACI,OAAO,IAAI;IACnB;EACJ,CAAC;EACDC,cAAc,EAAE,MAAM;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,WAAW,EAAE,qBAAqB;EAClCC,WAAW,EAAE,iBAAiB;EAC9BC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,aAAa,EAAE,MAAM;EACrBC,cAAc,EAAE,OAAO;EACvBC,aAAa,EAAE,MAAM;EACrBC,eAAe,EAAE,QAAQ;EACzBC,SAAS,EAAE;AACf,CAAC;AACD,eAAenB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
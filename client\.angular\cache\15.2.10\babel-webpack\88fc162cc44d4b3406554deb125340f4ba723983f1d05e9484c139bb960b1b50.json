{"ast": null, "code": "import { defer } from './defer';\nimport { EMPTY } from './empty';\nexport function iif(condition, trueResult = EMPTY, falseResult = EMPTY) {\n  return defer(() => condition() ? trueResult : falseResult);\n}", "map": {"version": 3, "names": ["defer", "EMPTY", "iif", "condition", "trueResult", "falseResult"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/iif.js"], "sourcesContent": ["import { defer } from './defer';\nimport { EMPTY } from './empty';\nexport function iif(condition, trueResult = EMPTY, falseResult = EMPTY) {\n    return defer(() => condition() ? trueResult : falseResult);\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,GAAGA,CAACC,SAAS,EAAEC,UAAU,GAAGH,KAAK,EAAEI,WAAW,GAAGJ,KAAK,EAAE;EACpE,OAAOD,KAAK,CAAC,MAAMG,SAAS,CAAC,CAAC,GAAGC,UAAU,GAAGC,WAAW,CAAC;AAC9D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreCommonModule } from '@core/common.module';\nimport { AuthLoginComponent } from './auth-login/auth-login.component';\nimport { AuthResetPasswordComponent } from './auth-reset-password/auth-reset-password.component';\nimport { AuthForgotPasswordComponent } from './auth-forgot-password/auth-forgot-password.component';\nimport { AuthRegisterComponent } from './auth-register/auth-register.component';\nimport { TranslateModuleModule } from 'app/components/translate-module/translate-module.module';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'auth/login',\n  component: AuthLoginComponent\n}, {\n  path: 'auth/reset-password/:token/:email',\n  component: AuthResetPasswordComponent\n}, {\n  path: 'auth/forgot-password',\n  component: AuthForgotPasswordComponent\n}, {\n  path: 'auth/register',\n  component: AuthRegisterComponent\n}];\nexport class AuthenticationModule {\n  static #_ = this.ɵfac = function AuthenticationModule_Factory(t) {\n    return new (t || AuthenticationModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthenticationModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), NgbModule, FormsModule, ReactiveFormsModule, CoreCommonModule, TranslateModule, TranslateModuleModule, ErrorMessageModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthenticationModule, {\n    declarations: [AuthLoginComponent, AuthForgotPasswordComponent, AuthResetPasswordComponent, AuthRegisterComponent],\n    imports: [CommonModule, i1.RouterModule, NgbModule, FormsModule, ReactiveFormsModule, CoreCommonModule, TranslateModule, TranslateModuleModule, ErrorMessageModule]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAgB,iBAAiB;AAEtD,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,kBAAkB,QAAQ,0DAA0D;;;AAE7F,MAAMC,MAAM,GAAW,CACnB;EACIC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAER;CACd,EACD;EACIO,IAAI,EAAE,mCAAmC;EACzCC,SAAS,EAAEP;CACd,EACD;EACIM,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEN;CACd,EACD;EACIK,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEL;CACd,CACJ;AAqBD,OAAM,MAAOM,oBAAoB;EAAA,QAAAC,CAAA;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA;UAApBF;EAAoB;EAAA,QAAAG,EAAA;cAX7BnB,YAAY,EACZG,YAAY,CAACiB,QAAQ,CAACP,MAAM,CAAC,EAC7BT,SAAS,EACTH,WAAW,EACXC,mBAAmB,EACnBI,gBAAgB,EAChBD,eAAe,EACfM,qBAAqB,EACrBC,kBAAkB;EAAA;;;2EAGTI,oBAAoB;IAAAK,YAAA,GAjB7Bd,kBAAkB,EAClBE,2BAA2B,EAC3BD,0BAA0B,EAC1BE,qBAAqB;IAAAY,OAAA,GAGrBtB,YAAY,EAAAuB,EAAA,CAAApB,YAAA,EAEZC,SAAS,EACTH,WAAW,EACXC,mBAAmB,EACnBI,gBAAgB,EAChBD,eAAe,EACfM,qBAAqB,EACrBC,kBAAkB;EAAA;AAAA", "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "RouterModule", "NgbModule", "TranslateModule", "CoreCommonModule", "AuthLoginComponent", "AuthResetPasswordComponent", "AuthForgotPasswordComponent", "AuthRegisterComponent", "TranslateModuleModule", "ErrorMessageModule", "routes", "path", "component", "AuthenticationModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\auth\\auth.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\n\r\nimport { CoreCommonModule } from '@core/common.module';\r\n\r\nimport { AuthLoginComponent } from './auth-login/auth-login.component';\r\nimport { AuthResetPasswordComponent } from './auth-reset-password/auth-reset-password.component';\r\nimport { AuthForgotPasswordComponent } from './auth-forgot-password/auth-forgot-password.component';\r\nimport { AuthRegisterComponent } from './auth-register/auth-register.component';\r\nimport { TranslateModuleModule } from 'app/components/translate-module/translate-module.module';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\n\r\nconst routes: Routes = [\r\n    {\r\n        path: 'auth/login',\r\n        component: AuthLoginComponent\r\n    },\r\n    {\r\n        path: 'auth/reset-password/:token/:email',\r\n        component: AuthResetPasswordComponent\r\n    },\r\n    {\r\n        path: 'auth/forgot-password',\r\n        component: AuthForgotPasswordComponent\r\n    },\r\n    {\r\n        path: 'auth/register', \r\n        component: AuthRegisterComponent\r\n    }\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AuthLoginComponent,\r\n    AuthForgotPasswordComponent,\r\n    AuthResetPasswordComponent,\r\n    AuthRegisterComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    NgbModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CoreCommonModule,\r\n    TranslateModule,\r\n    TranslateModuleModule,\r\n    ErrorMessageModule\r\n  ],\r\n})\r\nexport class AuthenticationModule {}"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
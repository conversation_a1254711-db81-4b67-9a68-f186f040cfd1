{"ast": null, "code": "import createRound from './_createRound.js';\n\n/**\n * Computes `number` rounded up to `precision`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Math\n * @param {number} number The number to round up.\n * @param {number} [precision=0] The precision to round up to.\n * @returns {number} Returns the rounded up number.\n * @example\n *\n * _.ceil(4.006);\n * // => 5\n *\n * _.ceil(6.004, 2);\n * // => 6.01\n *\n * _.ceil(6040, -2);\n * // => 6100\n */\nvar ceil = createRound('ceil');\nexport default ceil;", "map": {"version": 3, "names": ["createRound", "ceil"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/ceil.js"], "sourcesContent": ["import createRound from './_createRound.js';\n\n/**\n * Computes `number` rounded up to `precision`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Math\n * @param {number} number The number to round up.\n * @param {number} [precision=0] The precision to round up to.\n * @returns {number} Returns the rounded up number.\n * @example\n *\n * _.ceil(4.006);\n * // => 5\n *\n * _.ceil(6.004, 2);\n * // => 6.01\n *\n * _.ceil(6040, -2);\n * // => 6100\n */\nvar ceil = createRound('ceil');\n\nexport default ceil;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAGD,WAAW,CAAC,MAAM,CAAC;AAE9B,eAAeC,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
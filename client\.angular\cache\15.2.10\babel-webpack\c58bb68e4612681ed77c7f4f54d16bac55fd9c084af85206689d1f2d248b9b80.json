{"ast": null, "code": "import { Subject } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/registration.service\";\nimport * as i3 from \"../../services/loading.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/season.service\";\nimport * as i6 from \"../../services/club.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"app/layout/components/content-header/content-header.component\";\nfunction TeamsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_div_13_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const season_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openRegistrationModal(season_r1));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const season_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(season_r1.name);\n  }\n}\nexport class TeamsComponent {\n  constructor(_router, _registrationService, _loadingService, _translateService, seasonService, _clubService, _trans) {\n    this._router = _router;\n    this._registrationService = _registrationService;\n    this._loadingService = _loadingService;\n    this._translateService = _translateService;\n    this.seasonService = seasonService;\n    this._clubService = _clubService;\n    this._trans = _trans;\n    this.seasons = [];\n    this._clubs = [];\n  }\n  ngOnInit() {\n    // content header\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Select Season'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Team',\n          isLink: false\n        }, {\n          name: this._trans.instant('Assign player'),\n          isLink: false\n        }, {\n          name: 'Select Season',\n          isLink: false\n        }]\n      }\n    };\n    // define default values for unsubscribe all\n    this._unsubscribeAll = new Subject();\n    // get active seasons\n    this.getActiveSeasons();\n    // get all clubs\n    this.getAllClubsIsActive();\n  }\n  openRegistrationModal(season) {\n    this._registrationService.selectedSeason = season;\n    // navigate to select-player\n    setTimeout(() => {\n      this._router.navigate(['team/seasons/', season.id, 'teams']);\n    }, 500);\n  }\n  getActiveSeasons() {\n    let status = 'active';\n    this.seasonService.getSeasons(status).subscribe(data => {\n      console.log(`getActiveSeasons`, data);\n      this.seasons = data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  getAllClubsIsActive() {\n    // get all clubs\n    this._clubService.getAllClubsIsActive().toPromise().then(data => {\n      // set data to clubs\n      this._clubs = data;\n      // set data to allClubs\n      this._registrationService.allClubs = data;\n    }, error => {\n      // show error\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  static #_ = this.ɵfac = function TeamsComponent_Factory(t) {\n    return new (t || TeamsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.RegistrationService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.SeasonService), i0.ɵɵdirectiveInject(i6.ClubService), i0.ɵɵdirectiveInject(i4.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamsComponent,\n    selectors: [[\"app-teams\"]],\n    decls: 14,\n    vars: 5,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"select-event-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"text-muted\"], [1, \"card-body\"], [\"class\", \"col-auto\", \"style\", \"padding: 0 5px;\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-auto\", 2, \"padding\", \"0 5px\"], [\"routerLinkActive\", \"active\", 1, \"btn\", \"btn-lg\", \"btn-outline-primary\", \"text-primary\", \"mb-1\", 3, \"click\"]],\n    template: function TeamsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h6\", 8);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 4);\n        i0.ɵɵtemplate(13, TeamsComponent_div_13_Template, 3, 1, \"div\", 10);\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 3, \"Choose a season to assign player\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n      }\n    },\n    dependencies: [i7.NgForOf, i1.RouterLinkActive, i8.ContentHeaderComponent, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,OAAO,QAAQ,MAAM;AAI9B,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;ICQdC,EAAA,CAAAC,cAAA,cAA6E;IACfD,EAAA,CAAAE,UAAA,mBAAAC,kDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,qBAAA,CAAAL,SAAA,CAA6B;IAAA,EAAC;IACvEP,EAAA,CAAAa,MAAA,GAAe;IAAAb,EAAA,CAAAc,YAAA,EAAI;;;;IAAnBd,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,iBAAA,CAAAT,SAAA,CAAAU,IAAA,CAAe;;;ADC7D,OAAM,MAAOC,cAAc;EAUzBC,YACUC,OAAe,EACfC,oBAAyC,EACzCC,eAA+B,EAC/BC,iBAAmC,EACnCC,aAA4B,EAC5BC,YAAyB,EACzBC,MAAwB;IANxB,KAAAN,OAAO,GAAPA,OAAO;IACP,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IAdhB,KAAAC,OAAO,GAAa,EAAE;IAKd,KAAAC,MAAM,GAAG,EAAE;EAUhB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACL,MAAM,CAACM,OAAO,CAAC,eAAe,CAAC;MACjDC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEnB,IAAI,EAAE,MAAM;UACZoB,MAAM,EAAE;SACT,EACD;UACEpB,IAAI,EAAE,IAAI,CAACS,MAAM,CAACM,OAAO,CAAC,eAAe,CAAC;UAC1CK,MAAM,EAAE;SACT,EACD;UACEpB,IAAI,EAAE,eAAe;UACrBoB,MAAM,EAAE;SACT;;KAGN;IAED;IACA,IAAI,CAACC,eAAe,GAAG,IAAIxC,OAAO,EAAE;IAEpC;IACA,IAAI,CAACyC,gBAAgB,EAAE;IAEvB;IACA,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA5B,qBAAqBA,CAAC6B,MAAM;IAC1B,IAAI,CAACpB,oBAAoB,CAACqB,cAAc,GAAGD,MAAM;IACjD;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACvB,OAAO,CAACwB,QAAQ,CAAC,CAAC,eAAe,EAAEH,MAAM,CAACI,EAAE,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC,EAAE,GAAG,CAAC;EACT;EAEAN,gBAAgBA,CAAA;IACd,IAAIO,MAAM,GAAG,QAAQ;IACrB,IAAI,CAACtB,aAAa,CAACuB,UAAU,CAACD,MAAM,CAAC,CAACE,SAAS,CAC5CC,IAAI,IAAI;MACPC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,IAAI,CAAC;MACrC,IAAI,CAACtB,OAAO,GAAGsB,IAAI;IACrB,CAAC,EACAG,KAAK,IAAI;MACRrD,IAAI,CAACsD,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACnC,iBAAiB,CAACS,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAQ,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACf,YAAY,CACde,mBAAmB,EAAE,CACrBmB,SAAS,EAAE,CACXC,IAAI,CACFX,IAAI,IAAI;MACP;MACA,IAAI,CAACrB,MAAM,GAAGqB,IAAI;MAClB;MACA,IAAI,CAAC5B,oBAAoB,CAACwC,QAAQ,GAAGZ,IAAI;IAC3C,CAAC,EACAG,KAAK,IAAI;MACR;MACArD,IAAI,CAACsD,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACnC,iBAAiB,CAACS,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACL;EAEA8B,WAAWA,CAAA;IACT;IACA,IAAI,CAACxB,eAAe,CAACyB,IAAI,EAAE;IAC3B,IAAI,CAACzB,eAAe,CAAC0B,QAAQ,EAAE;EACjC;EAAC,QAAAC,CAAA;qBA5GU/C,cAAc,EAAAlB,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAtE,EAAA,CAAAkE,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAkE,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA1E,EAAA,CAAAkE,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAA5E,EAAA,CAAAkE,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAA9E,EAAA,CAAAkE,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAK,EAAA;UAAd7D,cAAc;IAAA8D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB3BtF,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAwF,SAAA,4BAAyE;QAGzExF,EAAA,CAAAC,cAAA,iBAAgC;QAKCD,EAAA,CAAAa,MAAA,GAAoD;;QAAAb,EAAA,CAAAc,YAAA,EAAK;QAElFd,EAAA,CAAAC,cAAA,cAAuB;QAEnBD,EAAA,CAAAyF,UAAA,KAAAC,8BAAA,kBAGM;QACR1F,EAAA,CAAAc,YAAA,EAAM;;;QAhBId,EAAA,CAAAe,SAAA,GAA+B;QAA/Bf,EAAA,CAAA2F,UAAA,kBAAAJ,GAAA,CAAAzD,aAAA,CAA+B;QAQlB9B,EAAA,CAAAe,SAAA,GAAoD;QAApDf,EAAA,CAAAgB,iBAAA,CAAAhB,EAAA,CAAA4F,WAAA,4CAAoD;QAIR5F,EAAA,CAAAe,SAAA,GAAU;QAAVf,EAAA,CAAA2F,UAAA,YAAAJ,GAAA,CAAA5D,OAAA,CAAU", "names": ["Subject", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "TeamsComponent_div_13_Template_a_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "season_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openRegistrationModal", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "name", "TeamsComponent", "constructor", "_router", "_registrationService", "_loadingService", "_translateService", "seasonService", "_clubService", "_trans", "seasons", "_clubs", "ngOnInit", "contentHeader", "headerTitle", "instant", "actionButton", "breadcrumb", "type", "links", "isLink", "_unsubscribeAll", "getActiveSeasons", "getAllClubsIsActive", "season", "selectedS<PERSON>on", "setTimeout", "navigate", "id", "status", "getSeasons", "subscribe", "data", "console", "log", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "to<PERSON>romise", "then", "allClubs", "ngOnDestroy", "next", "complete", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "RegistrationService", "i3", "LoadingService", "i4", "TranslateService", "i5", "SeasonService", "i6", "ClubService", "_2", "selectors", "decls", "vars", "consts", "template", "TeamsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "TeamsComponent_div_13_Template", "ɵɵproperty", "ɵɵpipeBind1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\teams.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\teams.component.html"], "sourcesContent": ["import { ClubService } from '../../services/club.service';\r\nimport { LoadingService } from '../../services/loading.service';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { Subject } from 'rxjs';\r\nimport { RegistrationService } from '../../services/registration.service';\r\nimport { Router } from '@angular/router';\r\nimport { Component, ViewEncapsulation } from '@angular/core';\r\nimport Swal from 'sweetalert2';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { Season } from 'app/interfaces/season';\r\n\r\n@Component({\r\n  selector: 'app-teams',\r\n  templateUrl: './teams.component.html',\r\n  styleUrls: ['./teams.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class TeamsComponent {\r\n  // public variables\r\n  public contentHeader: object;\r\n  seasons: Season[] = [];\r\n  public currentSeasons;\r\n\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n  private _clubs = [];\r\n\r\n  constructor(\r\n    private _router: Router,\r\n    private _registrationService: RegistrationService,\r\n    private _loadingService: LoadingService,\r\n    private _translateService: TranslateService,\r\n    private seasonService: SeasonService,\r\n    private _clubService: ClubService,\r\n    private _trans: TranslateService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // content header\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Select Season'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Team',\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: this._trans.instant('Assign player'),\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: 'Select Season',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    // define default values for unsubscribe all\r\n    this._unsubscribeAll = new Subject();\r\n\r\n    // get active seasons\r\n    this.getActiveSeasons();\r\n\r\n    // get all clubs\r\n    this.getAllClubsIsActive();\r\n  }\r\n\r\n  openRegistrationModal(season) {\r\n    this._registrationService.selectedSeason = season;\r\n    // navigate to select-player\r\n    setTimeout(() => {\r\n      this._router.navigate(['team/seasons/', season.id, 'teams']);\r\n    }, 500);\r\n  }\r\n\r\n  getActiveSeasons() {\r\n    let status = 'active';\r\n    this.seasonService.getSeasons(status).subscribe(\r\n      (data) => {\r\n        console.log(`getActiveSeasons`, data);\r\n        this.seasons = data;\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK'),\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  getAllClubsIsActive() {\r\n    // get all clubs\r\n    this._clubService\r\n      .getAllClubsIsActive()\r\n      .toPromise()\r\n      .then(\r\n        (data) => {\r\n          // set data to clubs\r\n          this._clubs = data;\r\n          // set data to allClubs\r\n          this._registrationService.allClubs = data;\r\n        },\r\n        (error) => {\r\n          // show error\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: error.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n          });\r\n        }\r\n      );\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <!-- Season events list start -->\r\n    <section id=\"select-event-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <div class=\"card-header\">\r\n              <h6 class=\"text-muted\">{{ 'Choose a season to assign player' | translate }}</h6>\r\n            </div>\r\n            <div class=\"card-body\">\r\n              <div class=\"row\">\r\n                <div class=\"col-auto\" style=\"padding: 0 5px;\" *ngFor=\"let season of seasons\">\r\n                  <a class=\"btn btn-lg btn-outline-primary text-primary mb-1\" (click)=\"openRegistrationModal(season)\"\r\n                    routerLinkActive=\"active\">{{season.name}}</a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n    <!-- Season events list end -->\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
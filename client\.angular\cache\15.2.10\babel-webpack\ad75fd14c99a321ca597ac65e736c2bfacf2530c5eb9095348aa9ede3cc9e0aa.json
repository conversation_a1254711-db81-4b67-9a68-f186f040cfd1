{"ast": null, "code": "import { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class S3Service {\n  constructor(_http) {\n    this._http = _http;\n  }\n  uploadImage(imageFile) {\n    return this._http.post(`${environment.apiUrl}/s3`, imageFile);\n  }\n  removeImage(filePath) {\n    return this._http.post(`${environment.apiUrl}/s3/delete`, {\n      filePath\n    });\n  }\n  getMaxUploadSize() {\n    return this._http.get(`${environment.apiUrl}/s3/max-upload-size`);\n  }\n  static #_ = this.ɵfac = function S3Service_Factory(t) {\n    return new (t || S3Service)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: S3Service,\n    factory: S3Service.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;;;AAKtD,OAAM,MAAOC,SAAS;EACpBC,YAAoBC,KAAiB;IAAjB,KAAAA,KAAK,GAALA,KAAK;EAAe;EAExCC,WAAWA,CAACC,SAAS;IACnB,OAAO,IAAI,CAACF,KAAK,CAACG,IAAI,CAAC,GAAGN,WAAW,CAACO,MAAM,KAAK,EAAEF,SAAS,CAAC;EAC/D;EAEAG,WAAWA,CAACC,QAAQ;IAClB,OAAO,IAAI,CAACN,KAAK,CAACG,IAAI,CAAC,GAAGN,WAAW,CAACO,MAAM,YAAY,EAAE;MACxDE;KACD,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACP,KAAK,CAACQ,GAAG,CAAC,GAAGX,WAAW,CAACO,MAAM,qBAAqB,CAAC;EACnE;EAAC,QAAAK,CAAA;qBAfUX,SAAS,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAAThB,SAAS;IAAAiB,OAAA,EAATjB,SAAS,CAAAkB,IAAA;IAAAC,UAAA,EAFR;EAAM", "names": ["environment", "S3Service", "constructor", "_http", "uploadImage", "imageFile", "post", "apiUrl", "removeImage", "filePath", "getMaxUploadSize", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\s3.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class S3Service {\r\n  constructor(private _http: HttpClient) {}\r\n\r\n  uploadImage(imageFile) {\r\n    return this._http.post(`${environment.apiUrl}/s3`, imageFile);\r\n  }\r\n\r\n  removeImage(filePath) {\r\n    return this._http.post(`${environment.apiUrl}/s3/delete`, {\r\n      filePath,\r\n    });\r\n  }\r\n\r\n  getMaxUploadSize() {\r\n    return this._http.get(`${environment.apiUrl}/s3/max-upload-size`);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
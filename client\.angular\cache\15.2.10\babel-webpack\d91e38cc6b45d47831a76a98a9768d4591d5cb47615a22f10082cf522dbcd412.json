{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Input, EventEmitter, Optional, SkipSelf, Output, Self, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceArray, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, startWith, map, take, tap, switchMap } from 'rxjs/operators';\nimport * as i3 from '@angular/cdk/bidi';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Gets a mutable version of an element's bounding `ClientRect`. */\nfunction getMutableClientRect(element) {\n  const clientRect = element.getBoundingClientRect();\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `ClientRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: clientRect.top,\n    right: clientRect.right,\n    bottom: clientRect.bottom,\n    left: clientRect.left,\n    width: clientRect.width,\n    height: clientRect.height,\n    x: clientRect.x,\n    y: clientRect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustClientRect(clientRect, top, left) {\n  clientRect.top += top;\n  clientRect.bottom = clientRect.top + clientRect.height;\n  clientRect.left += left;\n  clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n  constructor(_document) {\n    this._document = _document;\n    /** Cached positions of the scrollable parent elements. */\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n    const cachedPosition = this.positions.get(target);\n    if (!cachedPosition) {\n      return null;\n    }\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustClientRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({\n  passive: false\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value) {\n    const newValue = coerceBooleanProperty(value);\n    if (newValue !== this._disabled) {\n      this._disabled = newValue;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n    }\n  }\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n    this._hasStartedDragging = false;\n    /** Emits when the item is being moved. */\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n    this.dragStartDelay = 0;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n    this._pointerDown = event => {\n      this.beforeStarted.next();\n      // Delegate the event based on whether it started from a handle or the element itself.\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      if (!this._hasStartedDragging) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n        // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n          const container = this._dropContainer;\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n            return;\n          }\n          // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            event.preventDefault();\n            this._hasStartedDragging = true;\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n        return;\n      }\n      // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n      event.preventDefault();\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        // If there's a position constraint function, we want the element's top/left to be at the\n        // specific position on the page. Use the initial position as a reference if that's the case.\n        const offset = this.constrainPosition ? this._initialClientRect : this._pickupPositionOnPage;\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      }\n      // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n    /** Handles a native `dragstart` event. */\n    this._nativeDragStart = event => {\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          event.preventDefault();\n        }\n      } else if (!this.disabled) {\n        // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n        // but some cases like dragging of links can slip through (see #24403).\n        event.preventDefault();\n      }\n    };\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners(this._rootElement);\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeSubscriptions();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n  isDragging() {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n  _removeSubscriptions() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n  }\n  /** Destroys the preview element and its ViewRef. */\n  _destroyPreview() {\n    this._preview?.remove();\n    this._previewRef?.destroy();\n    this._preview = this._previewRef = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n  _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n    this._removeSubscriptions();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n    if (!this._hasStartedDragging) {\n      return;\n    }\n    this.released.next({\n      source: this,\n      event\n    });\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n    this._toggleNativeDragInteractions();\n    const dropContainer = this._dropContainer;\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n      const anchor = this._anchor = this._anchor || this._document.createComment('');\n      // Needs to happen before the root element is moved.\n      const shadowRoot = this._getShadowRoot();\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = this._createPreviewElement();\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    }\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event);\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n    this._hasStartedDragging = this._hasMoved = false;\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeSubscriptions();\n    this._initialClientRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialClientRect, referenceElement, event);\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialClientRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined;\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n        this._dropContainer.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer;\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n        // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    }\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Creates the element that will be rendered next to the user's pointer\n   * and will be used as a preview of the element that is being dragged.\n   */\n  _createPreviewElement() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this.previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialClientRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewRef = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialClientRect);\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // We have to reset the margin, because it can throw off positioning relative to the viewport.\n      'margin': '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': `${this._config.zIndex || 1000}`\n    }, dragImportantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('dir', this._direction);\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n    return preview;\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n    // Apply the class that adds a transition to the preview.\n    this._preview.classList.add('cdk-drag-animating');\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = getTransformTransitionDurationInMs(this._preview);\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          if (!event || _getEventTarget(event) === this._preview && event.propertyName === 'transform') {\n            this._preview?.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler, duration * 1.5);\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event) ?\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialClientRect, this._pickupPositionInElement) : point;\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y;\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x;\n    }\n    if (this._boundaryRect) {\n      const {\n        x: pickupX,\n        y: pickupY\n      } = this._pickupPositionInElement;\n      const boundaryRect = this._boundaryRect;\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyRootElementTransform(x, y) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style;\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyPreviewTransform(x, y) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.style.transform = combineTransforms(transform, initialTransform);\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n    if (scrollDifference) {\n      const target = _getEventTarget(event);\n      // ClientRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary ClientRect if the user has scrolled.\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n  _getViewportScrollPosition() {\n    return this._parentPositions.positions.get(this._document)?.scrollPosition || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialClientRect;\n    }\n    return this._previewRect;\n  }\n  /** Gets a handle that is the target of an event. */\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n  if (from === to) {\n    return;\n  }\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n  constructor(_element, _dragDropRegistry) {\n    this._element = _element;\n    this._dragDropRegistry = _dragDropRegistry;\n    /** Cache of the dimensions of all the items inside the container. */\n    this._itemPositions = [];\n    /** Direction in which the list is oriented. */\n    this.orientation = 'vertical';\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement();\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, offset, 0);\n      }\n    });\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ?\n    // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex];\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    }\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this._element).appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables.forEach(item => {\n      const rootElement = item.getRootElement();\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustClientRect(clientRect, topDifference, leftDifference);\n    });\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top;\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n      return isHorizontal ?\n      // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Function that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new Subject();\n    /** Emits when a dragging sequence is started in a list connected to the current one. */\n    this.receivingStarted = new Subject();\n    /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n    this.receivingStopped = new Subject();\n    /** Whether an item in the list is being dragged. */\n    this._isDragging = false;\n    /** Draggable items in the container. */\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n    this._siblings = [];\n    /** Connected siblings that currently have a dragged item. */\n    this._activeSiblings = new Set();\n    /** Subscription to the window being scrolled. */\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n    this._verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    /** Horizontal direction in which the list is currently scrolling. */\n    this._horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n    this._cachedShadowRoot = null;\n    /** Starts the interval that'll auto-scroll the element. */\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n        if (this._verticalScrollDirection === 1 /* AutoScrollVerticalDirection.UP */) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === 2 /* AutoScrollVerticalDirection.DOWN */) {\n          node.scrollBy(0, scrollStep);\n        }\n        if (this._horizontalScrollDirection === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === 2 /* AutoScrollHorizontalDirection.RIGHT */) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n    this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n  }\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n  start() {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted();\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item) {\n    this._reset();\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction) {\n    this._sortStrategy.direction = direction;\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation) {\n    // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n    // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n    this._sortStrategy.orientation = orientation;\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements) {\n    const element = coerceElement(this.element);\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._clientRect || !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n    let scrollNode;\n    let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n      if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    });\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n      const clientRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n      scrollNode = window;\n    }\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n  _draggingStarted() {\n    const styles = coerceElement(this.element).style;\n    this.beforeStarted.next();\n    this._isDragging = true;\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n  _cacheParentPositions() {\n    const element = coerceElement(this.element);\n    this._parentPositions.cache(this._scrollableElements);\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `ClientRect`.\n    this._clientRect = this._parentPositions.positions.get(element).clientRect;\n  }\n  /** Resets the container to its initial state. */\n  _reset() {\n    this._isDragging = false;\n    const styles = coerceElement(this.element).style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x, y) {\n    return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item, x, y) {\n    if (!this._clientRect || !isInsideClientRect(this._clientRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n    const nativeElement = coerceElement(this.element);\n    // The `ClientRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items\n      });\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({\n      initiator: sibling,\n      receiver: this\n    });\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return 1 /* AutoScrollVerticalDirection.UP */;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return 2 /* AutoScrollVerticalDirection.DOWN */;\n  }\n\n  return 0 /* AutoScrollVerticalDirection.NONE */;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return 1 /* AutoScrollHorizontalDirection.LEFT */;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return 2 /* AutoScrollHorizontalDirection.RIGHT */;\n  }\n\n  return 0 /* AutoScrollHorizontalDirection.NONE */;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n  let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n    if (computedVertical === 1 /* AutoScrollVerticalDirection.UP */) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = 1 /* AutoScrollVerticalDirection.UP */;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = 2 /* AutoScrollVerticalDirection.DOWN */;\n    }\n  }\n\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n    if (computedHorizontal === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n      if (scrollLeft > 0) {\n        horizontalScrollDirection = 1 /* AutoScrollHorizontalDirection.LEFT */;\n      }\n    } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n      horizontalScrollDirection = 2 /* AutoScrollHorizontalDirection.RIGHT */;\n    }\n  }\n\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n  constructor(_ngZone, _document) {\n    this._ngZone = _ngZone;\n    /** Registered drop container instances. */\n    this._dropInstances = new Set();\n    /** Registered drag item instances. */\n    this._dragInstances = new Set();\n    /** Drag item instances that are currently being dragged. */\n    this._activeDragInstances = [];\n    /** Keeps track of the event listeners that we've bound to the `document`. */\n    this._globalListeners = new Map();\n    /**\n     * Predicate function to check if an item is being dragged.  Moved out into a property,\n     * because it'll be called a lot and we don't want to create a new function every time.\n     */\n    this._draggingPredicate = item => item.isDragging();\n    /**\n     * Emits the `touchmove` or `mousemove` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerMove = new Subject();\n    /**\n     * Emits the `touchend` or `mouseup` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerUp = new Subject();\n    /**\n     * Emits when the viewport has been scrolled while the user is dragging an item.\n     * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n     * @breaking-change 13.0.0\n     */\n    this.scroll = new Subject();\n    /**\n     * Event listener that will prevent the default browser action while the user is dragging.\n     * @param event Event whose default action should be prevented.\n     */\n    this._preventDefaultWhileDragging = event => {\n      if (this._activeDragInstances.length > 0) {\n        event.preventDefault();\n      }\n    };\n    /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n    this._persistentTouchmoveListener = event => {\n      if (this._activeDragInstances.length > 0) {\n        // Note that we only want to prevent the default action after dragging has actually started.\n        // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n        // but it could be pushed back if the user has set up a drag delay or threshold.\n        if (this._activeDragInstances.some(this._draggingPredicate)) {\n          event.preventDefault();\n        }\n        this.pointerMove.next(event);\n      }\n    };\n    this._document = _document;\n  }\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag) {\n    this._dragInstances.add(drag);\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      });\n    }\n  }\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop) {\n    this._dropInstances.delete(drop);\n  }\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n    if (this._dragInstances.size === 0) {\n      this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n    }\n  }\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag, event) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances.indexOf(drag) > -1) {\n      return;\n    }\n    this._activeDragInstances.push(drag);\n    if (this._activeDragInstances.length === 1) {\n      const isTouchEvent = event.type.startsWith('touch');\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n        handler: e => this.pointerUp.next(e),\n        options: true\n      }).set('scroll', {\n        handler: e => this.scroll.next(e),\n        // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n        // the document. See https://github.com/angular/components/issues/17144.\n        options: true\n      })\n      // Preventing the default action on `mousemove` isn't enough to disable text selection\n      // on Safari so we need to prevent the selection event as well. Alternatively this can\n      // be done by setting `user-select: none` on the `body`, however it has causes a style\n      // recalculation which can be expensive on pages with a lot of elements.\n      .set('selectstart', {\n        handler: this._preventDefaultWhileDragging,\n        options: activeCapturingEventOptions\n      });\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        this._globalListeners.set('mousemove', {\n          handler: e => this.pointerMove.next(e),\n          options: activeCapturingEventOptions\n        });\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners.forEach((config, name) => {\n          this._document.addEventListener(name, config.handler, config.options);\n        });\n      });\n    }\n  }\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag) {\n    const index = this._activeDragInstances.indexOf(drag);\n    if (index > -1) {\n      this._activeDragInstances.splice(index, 1);\n      if (this._activeDragInstances.length === 0) {\n        this._clearGlobalListeners();\n      }\n    }\n  }\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag) {\n    return this._activeDragInstances.indexOf(drag) > -1;\n  }\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot) {\n    const streams = [this.scroll];\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(new Observable(observer => {\n        return this._ngZone.runOutsideAngular(() => {\n          const eventOptions = true;\n          const callback = event => {\n            if (this._activeDragInstances.length) {\n              observer.next(event);\n            }\n          };\n          shadowRoot.addEventListener('scroll', callback, eventOptions);\n          return () => {\n            shadowRoot.removeEventListener('scroll', callback, eventOptions);\n          };\n        });\n      }));\n    }\n    return merge(...streams);\n  }\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n  /** Clears out the global event listeners from the `document`. */\n  _clearGlobalListeners() {\n    this._globalListeners.forEach((config, name) => {\n      this._document.removeEventListener(name, config.handler, config.options);\n    });\n    this._globalListeners.clear();\n  }\n}\nDragDropRegistry.ɵfac = function DragDropRegistry_Factory(t) {\n  return new (t || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\nDragDropRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DragDropRegistry,\n  factory: DragDropRegistry.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n  constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag(element, config = DEFAULT_CONFIG) {\n    return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n  }\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList(element) {\n    return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n  }\n}\nDragDrop.ɵfac = function DragDrop_Factory(t) {\n  return new (t || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n};\nDragDrop.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DragDrop,\n  factory: DragDrop.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDrop, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: DragDropRegistry\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n  constructor() {\n    /** Drop lists registered inside the group. */\n    this._items = new Set();\n    this._disabled = false;\n  }\n  /** Whether starting a dragging sequence from inside this group is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  ngOnDestroy() {\n    this._items.clear();\n  }\n}\nCdkDropListGroup.ɵfac = function CdkDropListGroup_Factory(t) {\n  return new (t || CdkDropListGroup)();\n};\nCdkDropListGroup.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDropListGroup,\n  selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n  inputs: {\n    disabled: [\"cdkDropListGroupDisabled\", \"disabled\"]\n  },\n  exportAs: [\"cdkDropListGroup\"],\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DROP_LIST_GROUP,\n    useExisting: CdkDropListGroup\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropListGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropListGroup]',\n      exportAs: 'cdkDropListGroup',\n      standalone: true,\n      providers: [{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: ['cdkDropListGroupDisabled']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n  /** Whether starting a dragging sequence from this container is disabled. */\n  get disabled() {\n    return this._disabled || !!this._group && this._group.disabled;\n  }\n  set disabled(value) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n  }\n  constructor( /** Element that the drop list is attached to. */\n  element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n    this.element = element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._dir = _dir;\n    this._group = _group;\n    /** Emits when the list has been destroyed. */\n    this._destroyed = new Subject();\n    /**\n     * Other draggable containers that this container is connected to and into which the\n     * container's items can be transferred. Can either be references to other drop containers,\n     * or their unique IDs.\n     */\n    this.connectedTo = [];\n    /**\n     * Unique ID for the drop zone. Can be used as a reference\n     * in the `connectedTo` of another `CdkDropList`.\n     */\n    this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Functions that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new EventEmitter();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new EventEmitter();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new EventEmitter();\n    /**\n     * Keeps track of the items that are registered with this container. Historically we used to\n     * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n     * well which means that we can't handle cases like dragging the headers of a `mat-table`\n     * correctly. What we do instead is to have the items register themselves with the container\n     * and then we sort them based on their position in the DOM.\n     */\n    this._unsortedItems = new Set();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDropList');\n    }\n    this._dropListRef = dragDrop.createDropList(element);\n    this._dropListRef.data = this;\n    if (config) {\n      this._assignDefaults(config);\n    }\n    this._dropListRef.enterPredicate = (drag, drop) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n    this._dropListRef.sortPredicate = (index, drag, drop) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n    if (_group) {\n      _group._items.add(this);\n    }\n  }\n  /** Registers an items with the drop list. */\n  addItem(item) {\n    this._unsortedItems.add(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Removes an item from the drop list. */\n  removeItem(item) {\n    this._unsortedItems.delete(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems() {\n    return Array.from(this._unsortedItems).sort((a, b) => {\n      const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement());\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  _setupInputSyncSubscription(ref) {\n    if (this._dir) {\n      this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n    }\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n          return correspondingDropList;\n        }\n        return drop;\n      });\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n      ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n    });\n  }\n  /** Handles events from the underlying DropListRef. */\n  _handleEvents(ref) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef();\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex\n      });\n    });\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      draggingDisabled,\n      sortingDisabled,\n      listAutoScrollDisabled,\n      listOrientation\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  _syncItemsWithRef() {\n    this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n  }\n}\n/** Keeps track of the drop lists that are currently on the page. */\nCdkDropList._dropLists = [];\nCdkDropList.ɵfac = function CdkDropList_Factory(t) {\n  return new (t || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n};\nCdkDropList.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDropList,\n  selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n  hostAttrs: [1, \"cdk-drop-list\"],\n  hostVars: 7,\n  hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id);\n      i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n    }\n  },\n  inputs: {\n    connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"],\n    data: [\"cdkDropListData\", \"data\"],\n    orientation: [\"cdkDropListOrientation\", \"orientation\"],\n    id: \"id\",\n    lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"],\n    disabled: [\"cdkDropListDisabled\", \"disabled\"],\n    sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"],\n    enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"],\n    sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"],\n    autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"],\n    autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"]\n  },\n  outputs: {\n    dropped: \"cdkDropListDropped\",\n    entered: \"cdkDropListEntered\",\n    exited: \"cdkDropListExited\",\n    sorted: \"cdkDropListSorted\"\n  },\n  exportAs: [\"cdkDropList\"],\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([\n  // Prevent child drop lists from picking up the same group as their parent.\n  {\n    provide: CDK_DROP_LIST_GROUP,\n    useValue: undefined\n  }, {\n    provide: CDK_DROP_LIST,\n    useExisting: CdkDropList\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropList, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropList], cdk-drop-list',\n      exportAs: 'cdkDropList',\n      standalone: true,\n      providers: [\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }],\n      host: {\n        'class': 'cdk-drop-list',\n        '[attr.id]': 'id',\n        '[class.cdk-drop-list-disabled]': 'disabled',\n        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: DragDrop\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: CdkDropListGroup,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_DROP_LIST_GROUP]\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_CONFIG]\n      }]\n    }];\n  }, {\n    connectedTo: [{\n      type: Input,\n      args: ['cdkDropListConnectedTo']\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDropListData']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['cdkDropListOrientation']\n    }],\n    id: [{\n      type: Input\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDropListLockAxis']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkDropListDisabled']\n    }],\n    sortingDisabled: [{\n      type: Input,\n      args: ['cdkDropListSortingDisabled']\n    }],\n    enterPredicate: [{\n      type: Input,\n      args: ['cdkDropListEnterPredicate']\n    }],\n    sortPredicate: [{\n      type: Input,\n      args: ['cdkDropListSortPredicate']\n    }],\n    autoScrollDisabled: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollDisabled']\n    }],\n    autoScrollStep: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollStep']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDropListDropped']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDropListEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDropListExited']\n    }],\n    sorted: [{\n      type: Output,\n      args: ['cdkDropListSorted']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n  /** Whether starting to drag through this handle is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._stateChanges.next(this);\n  }\n  constructor(element, parentDrag) {\n    this.element = element;\n    /** Emits when the state of the handle has changed. */\n    this._stateChanges = new Subject();\n    this._disabled = false;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDragHandle');\n    }\n    this._parentDrag = parentDrag;\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n}\nCdkDragHandle.ɵfac = function CdkDragHandle_Factory(t) {\n  return new (t || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n};\nCdkDragHandle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDragHandle,\n  selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n  hostAttrs: [1, \"cdk-drag-handle\"],\n  inputs: {\n    disabled: [\"cdkDragHandleDisabled\", \"disabled\"]\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_HANDLE,\n    useExisting: CdkDragHandle\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragHandle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDragHandle]',\n      standalone: true,\n      host: {\n        'class': 'cdk-drag-handle'\n      },\n      providers: [{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_DRAG_PARENT]\n      }, {\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }];\n  }, {\n    disabled: [{\n      type: Input,\n      args: ['cdkDragHandleDisabled']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n}\nCdkDragPlaceholder.ɵfac = function CdkDragPlaceholder_Factory(t) {\n  return new (t || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\nCdkDragPlaceholder.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDragPlaceholder,\n  selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n  inputs: {\n    data: \"data\"\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_PLACEHOLDER,\n    useExisting: CdkDragPlaceholder\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPlaceholder]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, {\n    data: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n  /** Whether the preview should preserve the same size as the item that is being dragged. */\n  get matchSize() {\n    return this._matchSize;\n  }\n  set matchSize(value) {\n    this._matchSize = coerceBooleanProperty(value);\n  }\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._matchSize = false;\n  }\n}\nCdkDragPreview.ɵfac = function CdkDragPreview_Factory(t) {\n  return new (t || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\nCdkDragPreview.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDragPreview,\n  selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n  inputs: {\n    data: \"data\",\n    matchSize: \"matchSize\"\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_PREVIEW,\n    useExisting: CdkDragPreview\n  }])]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPreview, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPreview]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, {\n    data: [{\n      type: Input\n    }],\n    matchSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || this.dropContainer && this.dropContainer.disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._dragRef.disabled = this._disabled;\n  }\n  constructor( /** Element that the draggable is attached to. */\n  element, /** Droppable container that the draggable is a part of. */\n  dropContainer,\n  /**\n   * @deprecated `_document` parameter no longer being used and will be removed.\n   * @breaking-change 12.0.0\n   */\n  _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n    this.element = element;\n    this.dropContainer = dropContainer;\n    this._ngZone = _ngZone;\n    this._viewContainerRef = _viewContainerRef;\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._selfHandle = _selfHandle;\n    this._parentDrag = _parentDrag;\n    this._destroyed = new Subject();\n    /** Emits when the user starts dragging the item. */\n    this.started = new EventEmitter();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new EventEmitter();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new EventEmitter();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new EventEmitter();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new EventEmitter();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = new Observable(observer => {\n      const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n        source: this,\n        pointerPosition: movedEvent.pointerPosition,\n        event: movedEvent.event,\n        delta: movedEvent.delta,\n        distance: movedEvent.distance\n      }))).subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n    this._dragRef = dragDrop.createDrag(element, {\n      dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n      zIndex: config?.zIndex\n    });\n    this._dragRef.data = this;\n    // We have to keep track of the drag instances in order to be able to match an element to\n    // a drag instance. We can't go through the global registry of `DragRef`, because the root\n    // element could be different.\n    CdkDrag._dragInstances.push(this);\n    if (config) {\n      this._assignDefaults(config);\n    }\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n      dropContainer.addItem(this);\n    }\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._dragRef.getPlaceholderElement();\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._dragRef.getRootElement();\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._dragRef.reset();\n  }\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    return this._dragRef.getFreeDragPosition();\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._dragRef.setFreeDragPosition(value);\n  }\n  ngAfterViewInit() {\n    // Normally this isn't in the zone, but it can cause major performance regressions for apps\n    // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n    this._ngZone.runOutsideAngular(() => {\n      // We need to wait for the zone to stabilize, in order for the reference\n      // element to be in the proper place in the DOM. This is mostly relevant\n      // for draggable elements inside portals since they get stamped out in\n      // their original DOM position and then they get transferred to the portal.\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        this._updateRootElement();\n        this._setupHandlesListener();\n        if (this.freeDragPosition) {\n          this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n      });\n    });\n  }\n  ngOnChanges(changes) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n    // We don't have to react to the first change since it's being\n    // handled in `ngAfterViewInit` where it needs to be deferred.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n    // Skip the first change since it's being handled in `ngAfterViewInit`.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n    const index = CdkDrag._dragInstances.indexOf(this);\n    if (index > -1) {\n      CdkDrag._dragInstances.splice(index, 1);\n    }\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n  /** Syncs the root element with the `DragRef`. */\n  _updateRootElement() {\n    const element = this.element.nativeElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) :\n      // Comment tag doesn't have closest method, so use parent's one.\n      element.parentElement?.closest(this.rootElementSelector);\n    }\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n    this._dragRef.withRootElement(rootElement || element);\n  }\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n    if (!boundary) {\n      return null;\n    }\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest(boundary);\n    }\n    return coerceElement(boundary);\n  }\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  _syncInputs(ref) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate ? {\n          template: this._placeholderTemplate.templateRef,\n          context: this._placeholderTemplate.data,\n          viewContainer: this._viewContainerRef\n        } : null;\n        const preview = this._previewTemplate ? {\n          template: this._previewTemplate.templateRef,\n          context: this._previewTemplate.data,\n          matchSize: this._previewTemplate.matchSize,\n          viewContainer: this._viewContainerRef\n        } : null;\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        if (parent.classList.contains(DRAG_HOST_CLASS)) {\n          ref.withParent(CdkDrag._dragInstances.find(drag => {\n            return drag.element.nativeElement === parent;\n          })?._dragRef || null);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n  /** Handles the events from the underlying `DragRef`. */\n  _handleEvents(ref) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({\n        source: this,\n        event: startEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({\n        source: this,\n        event: releaseEvent.event\n      });\n    });\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex\n      });\n    });\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles.changes.pipe(startWith(this._handles),\n    // Sync the new handles with the DragRef.\n    tap(handles => {\n      const childHandleElements = handles.filter(handle => handle._parentDrag === this).map(handle => handle.element);\n      // Usually handles are only allowed to be a descendant of the drag element, but if\n      // the consumer defined a different drag root, we should allow the drag element\n      // itself to be a handle too.\n      if (this._selfHandle && this.rootElementSelector) {\n        childHandleElements.push(this.element);\n      }\n      this._dragRef.withHandles(childHandleElements);\n    }),\n    // Listen if the state of any of the handles changes.\n    switchMap(handles => {\n      return merge(...handles.map(item => {\n        return item._stateChanges.pipe(startWith(item));\n      }));\n    }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n      // Enabled/disable the handle that changed in the DragRef.\n      const dragRef = this._dragRef;\n      const handle = handleInstance.element.nativeElement;\n      handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n    });\n  }\n}\nCdkDrag._dragInstances = [];\nCdkDrag.ɵfac = function CdkDrag_Factory(t) {\n  return new (t || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n};\nCdkDrag.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDrag,\n  selectors: [[\"\", \"cdkDrag\", \"\"]],\n  contentQueries: function CdkDrag_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PREVIEW, 5);\n      i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PLACEHOLDER, 5);\n      i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_HANDLE, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previewTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._placeholderTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._handles = _t);\n    }\n  },\n  hostAttrs: [1, \"cdk-drag\"],\n  hostVars: 4,\n  hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n    }\n  },\n  inputs: {\n    data: [\"cdkDragData\", \"data\"],\n    lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"],\n    rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"],\n    boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"],\n    dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"],\n    freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n    disabled: [\"cdkDragDisabled\", \"disabled\"],\n    constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"],\n    previewClass: [\"cdkDragPreviewClass\", \"previewClass\"],\n    previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"]\n  },\n  outputs: {\n    started: \"cdkDragStarted\",\n    released: \"cdkDragReleased\",\n    ended: \"cdkDragEnded\",\n    entered: \"cdkDragEntered\",\n    exited: \"cdkDragExited\",\n    dropped: \"cdkDragDropped\",\n    moved: \"cdkDragMoved\"\n  },\n  exportAs: [\"cdkDrag\"],\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_PARENT,\n    useExisting: CdkDrag\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDrag, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDrag]',\n      exportAs: 'cdkDrag',\n      standalone: true,\n      host: {\n        'class': DRAG_HOST_CLASS,\n        '[class.cdk-drag-disabled]': 'disabled',\n        '[class.cdk-drag-dragging]': '_dragRef.isDragging()'\n      },\n      providers: [{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_DROP_LIST]\n      }, {\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_CONFIG]\n      }]\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: DragDrop\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: CdkDragHandle,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_HANDLE]\n      }]\n    }, {\n      type: CdkDrag,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_PARENT]\n      }]\n    }];\n  }, {\n    _handles: [{\n      type: ContentChildren,\n      args: [CDK_DRAG_HANDLE, {\n        descendants: true\n      }]\n    }],\n    _previewTemplate: [{\n      type: ContentChild,\n      args: [CDK_DRAG_PREVIEW]\n    }],\n    _placeholderTemplate: [{\n      type: ContentChild,\n      args: [CDK_DRAG_PLACEHOLDER]\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDragData']\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDragLockAxis']\n    }],\n    rootElementSelector: [{\n      type: Input,\n      args: ['cdkDragRootElement']\n    }],\n    boundaryElement: [{\n      type: Input,\n      args: ['cdkDragBoundary']\n    }],\n    dragStartDelay: [{\n      type: Input,\n      args: ['cdkDragStartDelay']\n    }],\n    freeDragPosition: [{\n      type: Input,\n      args: ['cdkDragFreeDragPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkDragDisabled']\n    }],\n    constrainPosition: [{\n      type: Input,\n      args: ['cdkDragConstrainPosition']\n    }],\n    previewClass: [{\n      type: Input,\n      args: ['cdkDragPreviewClass']\n    }],\n    previewContainer: [{\n      type: Input,\n      args: ['cdkDragPreviewContainer']\n    }],\n    started: [{\n      type: Output,\n      args: ['cdkDragStarted']\n    }],\n    released: [{\n      type: Output,\n      args: ['cdkDragReleased']\n    }],\n    ended: [{\n      type: Output,\n      args: ['cdkDragEnded']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDragEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDragExited']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDragDropped']\n    }],\n    moved: [{\n      type: Output,\n      args: ['cdkDragMoved']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst DRAG_DROP_DIRECTIVES = [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder];\nclass DragDropModule {}\nDragDropModule.ɵfac = function DragDropModule_Factory(t) {\n  return new (t || DragDropModule)();\n};\nDragDropModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DragDropModule\n});\nDragDropModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DragDrop],\n  imports: [CdkScrollableModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropModule, [{\n    type: NgModule,\n    args: [{\n      imports: DRAG_DROP_DIRECTIVES,\n      exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n      providers: [DragDrop]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "InjectionToken", "Directive", "Input", "EventEmitter", "Optional", "SkipSelf", "Output", "Self", "ContentChildren", "ContentChild", "NgModule", "DOCUMENT", "i1", "CdkScrollableModule", "_getEventTarget", "normalizePassiveListenerOptions", "_getShadowRoot", "coerceBooleanProperty", "coerceElement", "coerce<PERSON><PERSON><PERSON>", "coerceNumberProperty", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "Subject", "Subscription", "interval", "animationFrameScheduler", "Observable", "merge", "takeUntil", "startWith", "map", "take", "tap", "switchMap", "i3", "extendStyles", "dest", "source", "importantProperties", "key", "hasOwnProperty", "value", "setProperty", "has", "removeProperty", "toggleNativeDragInteractions", "element", "enable", "userSelect", "style", "toggleVisibility", "position", "top", "opacity", "left", "combineTransforms", "transform", "initialTransform", "parseCssTimeUnitsToMs", "multiplier", "toLowerCase", "indexOf", "parseFloat", "getTransformTransitionDurationInMs", "computedStyle", "getComputedStyle", "transitionedProperties", "parseCssPropertyValue", "property", "find", "prop", "propertyIndex", "rawDurations", "rawDelays", "name", "getPropertyValue", "split", "part", "trim", "getMutableClientRect", "clientRect", "getBoundingClientRect", "right", "bottom", "width", "height", "x", "y", "isInsideClientRect", "adjustClientRect", "isPointerNearClientRect", "rect", "threshold", "pointerX", "pointerY", "xThreshold", "yT<PERSON><PERSON>old", "ParentPositionTracker", "constructor", "_document", "positions", "Map", "clear", "cache", "elements", "set", "scrollPosition", "getViewportScrollPosition", "for<PERSON>ach", "scrollTop", "scrollLeft", "handleScroll", "event", "target", "cachedPosition", "get", "newTop", "newLeft", "viewportScrollPosition", "topDifference", "leftDifference", "node", "contains", "window", "scrollY", "scrollX", "deepCloneNode", "clone", "cloneNode", "descendantsWithId", "querySelectorAll", "nodeName", "removeAttribute", "i", "length", "transferCanvasData", "transferInputData", "transferData", "selector", "callback", "descendantElements", "cloneElements", "cloneUniqueId", "type", "context", "getContext", "drawImage", "passiveEventListenerOptions", "passive", "activeEventListenerOptions", "MOUSE_EVENT_IGNORE_TIME", "dragImportantProperties", "Set", "DragRef", "disabled", "_disabled", "_dropContainer", "newValue", "_toggleNativeDragInteractions", "_handles", "handle", "_config", "_ngZone", "_viewportRuler", "_dragDropRegistry", "_passiveTransform", "_activeTransform", "_hasStartedDragging", "_moveEvents", "_pointerMoveSubscription", "EMPTY", "_pointerUpSubscription", "_scrollSubscription", "_resizeSubscription", "_boundaryElement", "_nativeInteractionsEnabled", "_<PERSON><PERSON><PERSON><PERSON>", "_direction", "dragStartDelay", "beforeStarted", "started", "released", "ended", "entered", "exited", "dropped", "moved", "_pointerDown", "next", "targetHandle", "_getTar<PERSON><PERSON><PERSON>le", "_initializeDragSequence", "_rootElement", "_pointerMove", "pointerPosition", "_getPointerPositionOnPage", "distanceX", "Math", "abs", "_pickupPositionOnPage", "distanceY", "isOverThreshold", "dragStartThreshold", "isDelayElapsed", "Date", "now", "_dragStartTime", "_getDragStartDelay", "container", "_endDragSequence", "isDragging", "isReceiving", "preventDefault", "run", "_startDragSequence", "constrainedPointerPosition", "_getConstrainedPointerPosition", "_hasMoved", "_lastKnownPointerPosition", "_updatePointerDirectionDelta", "_updateActiveDropContainer", "offset", "constrainPosition", "_initialClientRect", "activeTransform", "_applyRootElementTransform", "observers", "distance", "_getDragDistance", "delta", "_pointerDirectionDelta", "_pointerUp", "_nativeDragStart", "withRootElement", "with<PERSON><PERSON>nt", "parentDragRef", "_parentPositions", "registerDragItem", "getPlaceholderElement", "_placeholder", "getRootElement", "getVisibleElement", "<PERSON><PERSON><PERSON><PERSON>", "handles", "<PERSON><PERSON><PERSON><PERSON>", "add", "withPreviewTemplate", "template", "_previewTemplate", "withPlaceholderTemplate", "_placeholderTemplate", "rootElement", "_removeRootElementListeners", "runOutsideAngular", "addEventListener", "_initialTransform", "undefined", "SVGElement", "_ownerSVGElement", "ownerSVGElement", "withBoundaryElement", "boundaryElement", "unsubscribe", "change", "subscribe", "_containInsideBoundaryOnResize", "parent", "_parentDragRef", "dispose", "remove", "_anchor", "_destroyPreview", "_destroyPlaceholder", "removeDragItem", "_removeSubscriptions", "complete", "reset", "disable<PERSON><PERSON><PERSON>", "enableHandle", "delete", "withDirection", "direction", "_withDropContainer", "getFreeDragPosition", "setFreeDragPosition", "withPreviewContainer", "_previewContainer", "_sortFromLastPointerPosition", "_preview", "_previewRef", "destroy", "_placeholderRef", "stopDragging", "webkitTapHighlightColor", "_rootElementTapHighlight", "_stopScrolling", "_animatePreviewToPlaceholder", "then", "_cleanupDragArtifacts", "_cleanupCachedDimensions", "dropPoint", "isTouchEvent", "_lastTouchEventTime", "dropContainer", "parentNode", "placeholder", "_createPlaceholderElement", "anchor", "createComment", "shadowRoot", "insertBefore", "_createPreviewElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_getPreviewInsertionPoint", "start", "_initialContainer", "_initialIndex", "getItemIndex", "getScrollableParents", "referenceElement", "stopPropagation", "isTouchSequence", "isAuxiliaryMouseButton", "button", "isSyntheticEvent", "isFakeEvent", "draggable", "rootStyles", "pointer<PERSON><PERSON>", "pointerUp", "scrolled", "scrollEvent", "_updateOnScroll", "_boundaryRect", "previewTemplate", "_pickupPositionInElement", "matchSize", "_getPointerPositionInElement", "_pointerPositionAtLastDirectionChange", "startDragging", "_previewRect", "currentIndex", "isPointerOverContainer", "_isOverContainer", "item", "previousIndex", "previousContainer", "drop", "rawX", "rawY", "newContainer", "_getSiblingContainerFromPosition", "exit", "enter", "sortingDisabled", "_startScrollingIfNecessary", "_sortItem", "_applyPreviewTransform", "previewConfig", "previewClass", "preview", "rootRect", "viewRef", "viewContainer", "createEmbeddedView", "detectChanges", "getRootNode", "matchElementSize", "getTransform", "zIndex", "classList", "setAttribute", "Array", "isArray", "className", "Promise", "resolve", "placeholder<PERSON><PERSON><PERSON>", "duration", "handler", "propertyName", "removeEventListener", "clearTimeout", "timeout", "setTimeout", "placeholder<PERSON><PERSON><PERSON>g", "placeholderTemplate", "pointerEvents", "elementRect", "handleElement", "referenceRect", "point", "targetTouches", "_getViewportScrollPosition", "pageX", "pageY", "touches", "changedTouches", "svgMatrix", "getScreenCTM", "svgPoint", "createSVGPoint", "matrixTransform", "inverse", "dropContainerLock", "lockAxis", "pickupX", "pickupY", "boundaryRect", "previewWidth", "previewHeight", "_getPreviewRect", "minY", "maxY", "minX", "maxX", "clamp$1", "pointerPositionOnPage", "positionSinceLastChange", "changeX", "changeY", "pointerDirectionChangeThreshold", "shouldEnable", "styles", "currentPosition", "pickupPosition", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "touch", "mouse", "scrollDifference", "_cachedShadowRoot", "initialParent", "previewContainer", "documentRef", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "round", "min", "max", "rootNodes", "nodeType", "ELEMENT_NODE", "wrapper", "createElement", "sourceRect", "moveItemInArray", "array", "fromIndex", "toIndex", "from", "clamp", "to", "transferArrayItem", "currentArray", "targetArray", "targetIndex", "splice", "copyArrayItem", "SingleAxisSortStrategy", "_element", "_itemPositions", "orientation", "_previousSwap", "drag", "overlaps", "items", "withItems", "sort", "pointer<PERSON><PERSON><PERSON>", "siblings", "newIndex", "_getItemIndexFromPointerPosition", "isHorizontal", "findIndex", "currentItem", "siblingAtNewPosition", "newPosition", "itemOffset", "_getItemOffsetPx", "siblingOffset", "_getSiblingOffsetPx", "oldOrder", "slice", "sibling", "index", "isDraggedItem", "elementToOffset", "activeDraggables", "_activeDraggables", "newPositionReference", "_shouldEnterAsFirstChild", "parentElement", "push", "_cacheItemPositions", "withSortPredicate", "predicate", "_sortPredicate", "p", "getActiveItemsSnapshot", "reverse", "updateOnScroll", "elementToMeasure", "a", "b", "immediateSibling", "end", "itemPositions", "reversed", "lastItemRect", "firstItemRect", "floor", "DROP_PROXIMITY_THRESHOLD", "SCROLL_PROXIMITY_THRESHOLD", "DropListRef", "autoScrollDisabled", "autoScrollStep", "enterPredicate", "sortPredicate", "sorted", "receivingStarted", "receivingStopped", "_isDragging", "_draggables", "_siblings", "_activeSiblings", "_viewportScrollSubscription", "_verticalScrollDirection", "_horizontalScrollDirection", "_stopScrollTimers", "_startScrollInterval", "pipe", "_scrollNode", "scrollStep", "scrollBy", "withScrollableParents", "registerDropContainer", "_sortStrategy", "removeDropContainer", "_draggingStarted", "_notifyReceivingSiblings", "_cacheParentPositions", "_reset", "previousItems", "draggedItems", "filter", "every", "connectedTo", "withOrientation", "_scrollableElements", "size", "_clientRect", "result", "scrollNode", "verticalScrollDirection", "horizontalScrollDirection", "getElementScrollDirections", "getViewportSize", "getVerticalScrollDirection", "getHorizontalScrollDirection", "_initialScrollSnap", "msScrollSnapType", "scrollSnapType", "_listenToScrollEvents", "_stopReceiving", "_canReceive", "elementFromPoint", "nativeElement", "_startReceiving", "activeSiblings", "initiator", "receiver", "computedVertical", "computedHorizontal", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "activeCapturingEventOptions", "capture", "DragDropRegistry", "_dropInstances", "_dragInstances", "_activeDragInstances", "_globalListeners", "_draggingPredicate", "scroll", "_preventDefaultWhileDragging", "_persistentTouchmoveListener", "some", "startsWith", "e", "options", "config", "_clearGlobalListeners", "streams", "observer", "eventOptions", "ngOnDestroy", "instance", "ɵfac", "DragDropRegistry_Factory", "t", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "decorators", "DEFAULT_CONFIG", "DragDrop", "createDrag", "createDropList", "DragDrop_Factory", "ViewportRuler", "CDK_DRAG_PARENT", "CDK_DROP_LIST_GROUP", "CdkDropListGroup", "_items", "CdkDropListGroup_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "CDK_DRAG_CONFIG", "assertElementNode", "Error", "_uniqueIdCounter", "CDK_DROP_LIST", "CdkDropList", "_group", "_dropListRef", "dragDrop", "_changeDetectorRef", "_scrollDispatcher", "_dir", "_destroyed", "id", "_unsortedItems", "data", "_assignDefaults", "_setupInputSyncSubscription", "_handleEvents", "_dropLists", "addItem", "_syncItemsWithRef", "removeItem", "getSortedItems", "documentPosition", "_dragRef", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "ref", "correspondingDropList", "list", "console", "warn", "_scrollableParentsResolved", "scrollableParents", "getAncestorScrollContainers", "scrollable", "getElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emit", "dropEvent", "draggingDisabled", "listAutoScrollDisabled", "listOrientation", "CdkDropList_Factory", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Directionality", "hostAttrs", "hostVars", "hostBindings", "CdkDropList_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassProp", "outputs", "useValue", "host", "CDK_DRAG_HANDLE", "CdkDragHandle", "_stateChanges", "parentDrag", "_parentDrag", "CdkDragHandle_Factory", "CDK_DRAG_PLACEHOLDER", "CdkDragPlaceholder", "templateRef", "CdkDragPlaceholder_Factory", "TemplateRef", "CDK_DRAG_PREVIEW", "CdkDragPreview", "_matchSize", "CdkDragPreview_Factory", "DRAG_HOST_CLASS", "CdkDrag", "_viewContainerRef", "_selfHandle", "subscription", "movedEvent", "_syncInputs", "ngAfterViewInit", "onStable", "_updateRootElement", "_setupHandlesListener", "freeDragPosition", "ngOnChanges", "changes", "rootSelectorChange", "positionChange", "firstChange", "rootElementSelector", "closest", "_getBoundaryElement", "boundary", "dir", "startEvent", "releaseEvent", "endEvent", "enterEvent", "exitEvent", "childHandleElements", "handleInstance", "dragRef", "CdkDrag_Factory", "ViewContainerRef", "contentQueries", "CdkDrag_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "CdkDrag_HostBindings", "ɵɵNgOnChangesFeature", "descendants", "DRAG_DROP_DIRECTIVES", "DragDropModule", "DragDropModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/cdk/fesm2020/drag-drop.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Input, EventEmitter, Optional, SkipSelf, Output, Self, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceArray, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, startWith, map, take, tap, switchMap } from 'rxjs/operators';\nimport * as i3 from '@angular/cdk/bidi';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            const value = source[key];\n            if (value) {\n                dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n            }\n            else {\n                dest.removeProperty(key);\n            }\n        }\n    }\n    return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n    const userSelect = enable ? '' : 'none';\n    extendStyles(element.style, {\n        'touch-action': enable ? '' : 'none',\n        '-webkit-user-drag': enable ? '' : 'none',\n        '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n        'user-select': userSelect,\n        '-ms-user-select': userSelect,\n        '-webkit-user-select': userSelect,\n        '-moz-user-select': userSelect,\n    });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n    extendStyles(element.style, {\n        position: enable ? '' : 'fixed',\n        top: enable ? '' : '0',\n        opacity: enable ? '' : '0',\n        left: enable ? '' : '-999em',\n    }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n    return initialTransform && initialTransform != 'none'\n        ? transform + ' ' + initialTransform\n        : transform;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n    // Some browsers will return it in seconds, whereas others will return milliseconds.\n    const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n    return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n    const computedStyle = getComputedStyle(element);\n    const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n    const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n    // If there's no transition for `all` or `transform`, we shouldn't do anything.\n    if (!property) {\n        return 0;\n    }\n    // Get the index of the property that we're interested in and match\n    // it up to the same index in `transition-delay` and `transition-duration`.\n    const propertyIndex = transitionedProperties.indexOf(property);\n    const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n    const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n    return (parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +\n        parseCssTimeUnitsToMs(rawDelays[propertyIndex]));\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n    const value = computedStyle.getPropertyValue(name);\n    return value.split(',').map(part => part.trim());\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Gets a mutable version of an element's bounding `ClientRect`. */\nfunction getMutableClientRect(element) {\n    const clientRect = element.getBoundingClientRect();\n    // We need to clone the `clientRect` here, because all the values on it are readonly\n    // and we need to be able to update them. Also we can't use a spread here, because\n    // the values on a `ClientRect` aren't own properties. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n    return {\n        top: clientRect.top,\n        right: clientRect.right,\n        bottom: clientRect.bottom,\n        left: clientRect.left,\n        width: clientRect.width,\n        height: clientRect.height,\n        x: clientRect.x,\n        y: clientRect.y,\n    };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n    const { top, bottom, left, right } = clientRect;\n    return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustClientRect(clientRect, top, left) {\n    clientRect.top += top;\n    clientRect.bottom = clientRect.top + clientRect.height;\n    clientRect.left += left;\n    clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n    const { top, right, bottom, left, width, height } = rect;\n    const xThreshold = width * threshold;\n    const yThreshold = height * threshold;\n    return (pointerY > top - yThreshold &&\n        pointerY < bottom + yThreshold &&\n        pointerX > left - xThreshold &&\n        pointerX < right + xThreshold);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n    constructor(_document) {\n        this._document = _document;\n        /** Cached positions of the scrollable parent elements. */\n        this.positions = new Map();\n    }\n    /** Clears the cached positions. */\n    clear() {\n        this.positions.clear();\n    }\n    /** Caches the positions. Should be called at the beginning of a drag sequence. */\n    cache(elements) {\n        this.clear();\n        this.positions.set(this._document, {\n            scrollPosition: this.getViewportScrollPosition(),\n        });\n        elements.forEach(element => {\n            this.positions.set(element, {\n                scrollPosition: { top: element.scrollTop, left: element.scrollLeft },\n                clientRect: getMutableClientRect(element),\n            });\n        });\n    }\n    /** Handles scrolling while a drag is taking place. */\n    handleScroll(event) {\n        const target = _getEventTarget(event);\n        const cachedPosition = this.positions.get(target);\n        if (!cachedPosition) {\n            return null;\n        }\n        const scrollPosition = cachedPosition.scrollPosition;\n        let newTop;\n        let newLeft;\n        if (target === this._document) {\n            const viewportScrollPosition = this.getViewportScrollPosition();\n            newTop = viewportScrollPosition.top;\n            newLeft = viewportScrollPosition.left;\n        }\n        else {\n            newTop = target.scrollTop;\n            newLeft = target.scrollLeft;\n        }\n        const topDifference = scrollPosition.top - newTop;\n        const leftDifference = scrollPosition.left - newLeft;\n        // Go through and update the cached positions of the scroll\n        // parents that are inside the element that was scrolled.\n        this.positions.forEach((position, node) => {\n            if (position.clientRect && target !== node && target.contains(node)) {\n                adjustClientRect(position.clientRect, topDifference, leftDifference);\n            }\n        });\n        scrollPosition.top = newTop;\n        scrollPosition.left = newLeft;\n        return { top: topDifference, left: leftDifference };\n    }\n    /**\n     * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n     * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n     * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n     * if the element is offset by something like the `BlockScrollStrategy`.\n     */\n    getViewportScrollPosition() {\n        return { top: window.scrollY, left: window.scrollX };\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n    const clone = node.cloneNode(true);\n    const descendantsWithId = clone.querySelectorAll('[id]');\n    const nodeName = node.nodeName.toLowerCase();\n    // Remove the `id` to avoid having multiple elements with the same id on the page.\n    clone.removeAttribute('id');\n    for (let i = 0; i < descendantsWithId.length; i++) {\n        descendantsWithId[i].removeAttribute('id');\n    }\n    if (nodeName === 'canvas') {\n        transferCanvasData(node, clone);\n    }\n    else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n        transferInputData(node, clone);\n    }\n    transferData('canvas', node, clone, transferCanvasData);\n    transferData('input, textarea, select', node, clone, transferInputData);\n    return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n    const descendantElements = node.querySelectorAll(selector);\n    if (descendantElements.length) {\n        const cloneElements = clone.querySelectorAll(selector);\n        for (let i = 0; i < descendantElements.length; i++) {\n            callback(descendantElements[i], cloneElements[i]);\n        }\n    }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n    // Browsers throw an error when assigning the value of a file input programmatically.\n    if (clone.type !== 'file') {\n        clone.value = source.value;\n    }\n    // Radio button `name` attributes must be unique for radio button groups\n    // otherwise original radio buttons can lose their checked state\n    // once the clone is inserted in the DOM.\n    if (clone.type === 'radio' && clone.name) {\n        clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n    }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n    const context = clone.getContext('2d');\n    if (context) {\n        // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n        // We can't do much about it so just ignore the error.\n        try {\n            context.drawImage(source, 0, 0);\n        }\n        catch { }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({ passive: false });\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n    // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n    'position',\n]);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n    }\n    set disabled(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._disabled) {\n            this._disabled = newValue;\n            this._toggleNativeDragInteractions();\n            this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n        }\n    }\n    constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._config = _config;\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n        /**\n         * CSS `transform` applied to the element when it isn't being dragged. We need a\n         * passive transform in order for the dragged element to retain its new position\n         * after the user has stopped dragging and because we need to know the relative\n         * position in case they start dragging again. This corresponds to `element.style.transform`.\n         */\n        this._passiveTransform = { x: 0, y: 0 };\n        /** CSS `transform` that is applied to the element while it's being dragged. */\n        this._activeTransform = { x: 0, y: 0 };\n        /**\n         * Whether the dragging sequence has been started. Doesn't\n         * necessarily mean that the element has been moved.\n         */\n        this._hasStartedDragging = false;\n        /** Emits when the item is being moved. */\n        this._moveEvents = new Subject();\n        /** Subscription to pointer movement events. */\n        this._pointerMoveSubscription = Subscription.EMPTY;\n        /** Subscription to the event that is dispatched when the user lifts their pointer. */\n        this._pointerUpSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being scrolled. */\n        this._scrollSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being resized. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Cached reference to the boundary element. */\n        this._boundaryElement = null;\n        /** Whether the native dragging interactions have been enabled on the root element. */\n        this._nativeInteractionsEnabled = true;\n        /** Elements that can be used to drag the draggable item. */\n        this._handles = [];\n        /** Registered handles that are currently disabled. */\n        this._disabledHandles = new Set();\n        /** Layout direction of the item. */\n        this._direction = 'ltr';\n        /**\n         * Amount of milliseconds to wait after the user has put their\n         * pointer down before starting to drag the element.\n         */\n        this.dragStartDelay = 0;\n        this._disabled = false;\n        /** Emits as the drag sequence is being prepared. */\n        this.beforeStarted = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new Subject();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new Subject();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new Subject();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new Subject();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new Subject();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new Subject();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = this._moveEvents;\n        /** Handler for the `mousedown`/`touchstart` events. */\n        this._pointerDown = (event) => {\n            this.beforeStarted.next();\n            // Delegate the event based on whether it started from a handle or the element itself.\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    this._initializeDragSequence(targetHandle, event);\n                }\n            }\n            else if (!this.disabled) {\n                this._initializeDragSequence(this._rootElement, event);\n            }\n        };\n        /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n        this._pointerMove = (event) => {\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            if (!this._hasStartedDragging) {\n                const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n                const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n                const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n                // Only start dragging after the user has moved more than the minimum distance in either\n                // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n                // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n                // per pixel of movement (e.g. if the user moves their pointer quickly).\n                if (isOverThreshold) {\n                    const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n                    const container = this._dropContainer;\n                    if (!isDelayElapsed) {\n                        this._endDragSequence(event);\n                        return;\n                    }\n                    // Prevent other drag sequences from starting while something in the container is still\n                    // being dragged. This can happen while we're waiting for the drop animation to finish\n                    // and can cause errors, because some elements might still be moving around.\n                    if (!container || (!container.isDragging() && !container.isReceiving())) {\n                        // Prevent the default action as soon as the dragging sequence is considered as\n                        // \"started\" since waiting for the next event can allow the device to begin scrolling.\n                        event.preventDefault();\n                        this._hasStartedDragging = true;\n                        this._ngZone.run(() => this._startDragSequence(event));\n                    }\n                }\n                return;\n            }\n            // We prevent the default action down here so that we know that dragging has started. This is\n            // important for touch devices where doing this too early can unnecessarily block scrolling,\n            // if there's a dragging delay.\n            event.preventDefault();\n            const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n            this._hasMoved = true;\n            this._lastKnownPointerPosition = pointerPosition;\n            this._updatePointerDirectionDelta(constrainedPointerPosition);\n            if (this._dropContainer) {\n                this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n            }\n            else {\n                // If there's a position constraint function, we want the element's top/left to be at the\n                // specific position on the page. Use the initial position as a reference if that's the case.\n                const offset = this.constrainPosition ? this._initialClientRect : this._pickupPositionOnPage;\n                const activeTransform = this._activeTransform;\n                activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n                activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n                this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n            }\n            // Since this event gets fired for every pixel while dragging, we only\n            // want to fire it if the consumer opted into it. Also we have to\n            // re-enter the zone because we run all of the events on the outside.\n            if (this._moveEvents.observers.length) {\n                this._ngZone.run(() => {\n                    this._moveEvents.next({\n                        source: this,\n                        pointerPosition: constrainedPointerPosition,\n                        event,\n                        distance: this._getDragDistance(constrainedPointerPosition),\n                        delta: this._pointerDirectionDelta,\n                    });\n                });\n            }\n        };\n        /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n        this._pointerUp = (event) => {\n            this._endDragSequence(event);\n        };\n        /** Handles a native `dragstart` event. */\n        this._nativeDragStart = (event) => {\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    event.preventDefault();\n                }\n            }\n            else if (!this.disabled) {\n                // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n                // but some cases like dragging of links can slip through (see #24403).\n                event.preventDefault();\n            }\n        };\n        this.withRootElement(element).withParent(_config.parentDragRef || null);\n        this._parentPositions = new ParentPositionTracker(_document);\n        _dragDropRegistry.registerDragItem(this);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._placeholder;\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._rootElement;\n    }\n    /**\n     * Gets the currently-visible element that represents the drag item.\n     * While dragging this is the placeholder, otherwise it's the root element.\n     */\n    getVisibleElement() {\n        return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n    }\n    /** Registers the handles that can be used to drag the element. */\n    withHandles(handles) {\n        this._handles = handles.map(handle => coerceElement(handle));\n        this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n        this._toggleNativeDragInteractions();\n        // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n        // the set, rather than iterate over it and filter out the destroyed handles, because while\n        // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n        // use an array internally which may throw an error.\n        const disabledHandles = new Set();\n        this._disabledHandles.forEach(handle => {\n            if (this._handles.indexOf(handle) > -1) {\n                disabledHandles.add(handle);\n            }\n        });\n        this._disabledHandles = disabledHandles;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag preview.\n     * @param template Template that from which to stamp out the preview.\n     */\n    withPreviewTemplate(template) {\n        this._previewTemplate = template;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag placeholder.\n     * @param template Template that from which to stamp out the placeholder.\n     */\n    withPlaceholderTemplate(template) {\n        this._placeholderTemplate = template;\n        return this;\n    }\n    /**\n     * Sets an alternate drag root element. The root element is the element that will be moved as\n     * the user is dragging. Passing an alternate root element is useful when trying to enable\n     * dragging on an element that you might not have access to.\n     */\n    withRootElement(rootElement) {\n        const element = coerceElement(rootElement);\n        if (element !== this._rootElement) {\n            if (this._rootElement) {\n                this._removeRootElementListeners(this._rootElement);\n            }\n            this._ngZone.runOutsideAngular(() => {\n                element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n                element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n                element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n            });\n            this._initialTransform = undefined;\n            this._rootElement = element;\n        }\n        if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n            this._ownerSVGElement = this._rootElement.ownerSVGElement;\n        }\n        return this;\n    }\n    /**\n     * Element to which the draggable's position will be constrained.\n     */\n    withBoundaryElement(boundaryElement) {\n        this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n        this._resizeSubscription.unsubscribe();\n        if (boundaryElement) {\n            this._resizeSubscription = this._viewportRuler\n                .change(10)\n                .subscribe(() => this._containInsideBoundaryOnResize());\n        }\n        return this;\n    }\n    /** Sets the parent ref that the ref is nested in.  */\n    withParent(parent) {\n        this._parentDragRef = parent;\n        return this;\n    }\n    /** Removes the dragging functionality from the DOM element. */\n    dispose() {\n        this._removeRootElementListeners(this._rootElement);\n        // Do this check before removing from the registry since it'll\n        // stop being considered as dragged once it is removed.\n        if (this.isDragging()) {\n            // Since we move out the element to the end of the body while it's being\n            // dragged, we have to make sure that it's removed if it gets destroyed.\n            this._rootElement?.remove();\n        }\n        this._anchor?.remove();\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._dragDropRegistry.removeDragItem(this);\n        this._removeSubscriptions();\n        this.beforeStarted.complete();\n        this.started.complete();\n        this.released.complete();\n        this.ended.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this._moveEvents.complete();\n        this._handles = [];\n        this._disabledHandles.clear();\n        this._dropContainer = undefined;\n        this._resizeSubscription.unsubscribe();\n        this._parentPositions.clear();\n        this._boundaryElement =\n            this._rootElement =\n                this._ownerSVGElement =\n                    this._placeholderTemplate =\n                        this._previewTemplate =\n                            this._anchor =\n                                this._parentDragRef =\n                                    null;\n    }\n    /** Checks whether the element is currently being dragged. */\n    isDragging() {\n        return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._rootElement.style.transform = this._initialTransform || '';\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform = { x: 0, y: 0 };\n    }\n    /**\n     * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n     * @param handle Handle element that should be disabled.\n     */\n    disableHandle(handle) {\n        if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n            this._disabledHandles.add(handle);\n            toggleNativeDragInteractions(handle, true);\n        }\n    }\n    /**\n     * Enables a handle, if it has been disabled.\n     * @param handle Handle element to be enabled.\n     */\n    enableHandle(handle) {\n        if (this._disabledHandles.has(handle)) {\n            this._disabledHandles.delete(handle);\n            toggleNativeDragInteractions(handle, this.disabled);\n        }\n    }\n    /** Sets the layout direction of the draggable item. */\n    withDirection(direction) {\n        this._direction = direction;\n        return this;\n    }\n    /** Sets the container that the item is part of. */\n    _withDropContainer(container) {\n        this._dropContainer = container;\n    }\n    /**\n     * Gets the current position in pixels the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n        return { x: position.x, y: position.y };\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform.x = value.x;\n        this._passiveTransform.y = value.y;\n        if (!this._dropContainer) {\n            this._applyRootElementTransform(value.x, value.y);\n        }\n        return this;\n    }\n    /**\n     * Sets the container into which to insert the preview element.\n     * @param value Container into which to insert the preview.\n     */\n    withPreviewContainer(value) {\n        this._previewContainer = value;\n        return this;\n    }\n    /** Updates the item's sort order based on the last-known pointer position. */\n    _sortFromLastPointerPosition() {\n        const position = this._lastKnownPointerPosition;\n        if (position && this._dropContainer) {\n            this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n        }\n    }\n    /** Unsubscribes from the global subscriptions. */\n    _removeSubscriptions() {\n        this._pointerMoveSubscription.unsubscribe();\n        this._pointerUpSubscription.unsubscribe();\n        this._scrollSubscription.unsubscribe();\n    }\n    /** Destroys the preview element and its ViewRef. */\n    _destroyPreview() {\n        this._preview?.remove();\n        this._previewRef?.destroy();\n        this._preview = this._previewRef = null;\n    }\n    /** Destroys the placeholder element and its ViewRef. */\n    _destroyPlaceholder() {\n        this._placeholder?.remove();\n        this._placeholderRef?.destroy();\n        this._placeholder = this._placeholderRef = null;\n    }\n    /**\n     * Clears subscriptions and stops the dragging sequence.\n     * @param event Browser event object that ended the sequence.\n     */\n    _endDragSequence(event) {\n        // Note that here we use `isDragging` from the service, rather than from `this`.\n        // The difference is that the one from the service reflects whether a dragging sequence\n        // has been initiated, whereas the one on `this` includes whether the user has passed\n        // the minimum dragging threshold.\n        if (!this._dragDropRegistry.isDragging(this)) {\n            return;\n        }\n        this._removeSubscriptions();\n        this._dragDropRegistry.stopDragging(this);\n        this._toggleNativeDragInteractions();\n        if (this._handles) {\n            this._rootElement.style.webkitTapHighlightColor =\n                this._rootElementTapHighlight;\n        }\n        if (!this._hasStartedDragging) {\n            return;\n        }\n        this.released.next({ source: this, event });\n        if (this._dropContainer) {\n            // Stop scrolling immediately, instead of waiting for the animation to finish.\n            this._dropContainer._stopScrolling();\n            this._animatePreviewToPlaceholder().then(() => {\n                this._cleanupDragArtifacts(event);\n                this._cleanupCachedDimensions();\n                this._dragDropRegistry.stopDragging(this);\n            });\n        }\n        else {\n            // Convert the active transform into a passive one. This means that next time\n            // the user starts dragging the item, its position will be calculated relatively\n            // to the new passive transform.\n            this._passiveTransform.x = this._activeTransform.x;\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            this._passiveTransform.y = this._activeTransform.y;\n            this._ngZone.run(() => {\n                this.ended.next({\n                    source: this,\n                    distance: this._getDragDistance(pointerPosition),\n                    dropPoint: pointerPosition,\n                    event,\n                });\n            });\n            this._cleanupCachedDimensions();\n            this._dragDropRegistry.stopDragging(this);\n        }\n    }\n    /** Starts the dragging sequence. */\n    _startDragSequence(event) {\n        if (isTouchEvent(event)) {\n            this._lastTouchEventTime = Date.now();\n        }\n        this._toggleNativeDragInteractions();\n        const dropContainer = this._dropContainer;\n        if (dropContainer) {\n            const element = this._rootElement;\n            const parent = element.parentNode;\n            const placeholder = (this._placeholder = this._createPlaceholderElement());\n            const anchor = (this._anchor = this._anchor || this._document.createComment(''));\n            // Needs to happen before the root element is moved.\n            const shadowRoot = this._getShadowRoot();\n            // Insert an anchor node so that we can restore the element's position in the DOM.\n            parent.insertBefore(anchor, element);\n            // There's no risk of transforms stacking when inside a drop container so\n            // we can keep the initial transform up to date any time dragging starts.\n            this._initialTransform = element.style.transform || '';\n            // Create the preview after the initial transform has\n            // been cached, because it can be affected by the transform.\n            this._preview = this._createPreviewElement();\n            // We move the element out at the end of the body and we make it hidden, because keeping it in\n            // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n            // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n            toggleVisibility(element, false, dragImportantProperties);\n            this._document.body.appendChild(parent.replaceChild(placeholder, element));\n            this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n            this.started.next({ source: this, event }); // Emit before notifying the container.\n            dropContainer.start();\n            this._initialContainer = dropContainer;\n            this._initialIndex = dropContainer.getItemIndex(this);\n        }\n        else {\n            this.started.next({ source: this, event });\n            this._initialContainer = this._initialIndex = undefined;\n        }\n        // Important to run after we've called `start` on the parent container\n        // so that it has had time to resolve its scrollable parents.\n        this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n    }\n    /**\n     * Sets up the different variables and subscriptions\n     * that will be necessary for the dragging sequence.\n     * @param referenceElement Element that started the drag sequence.\n     * @param event Browser event object that started the sequence.\n     */\n    _initializeDragSequence(referenceElement, event) {\n        // Stop propagation if the item is inside another\n        // draggable so we don't start multiple drag sequences.\n        if (this._parentDragRef) {\n            event.stopPropagation();\n        }\n        const isDragging = this.isDragging();\n        const isTouchSequence = isTouchEvent(event);\n        const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n        const rootElement = this._rootElement;\n        const target = _getEventTarget(event);\n        const isSyntheticEvent = !isTouchSequence &&\n            this._lastTouchEventTime &&\n            this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n        const isFakeEvent = isTouchSequence\n            ? isFakeTouchstartFromScreenReader(event)\n            : isFakeMousedownFromScreenReader(event);\n        // If the event started from an element with the native HTML drag&drop, it'll interfere\n        // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n        // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n        // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n        // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n        // events from firing on touch devices.\n        if (target && target.draggable && event.type === 'mousedown') {\n            event.preventDefault();\n        }\n        // Abort if the user is already dragging or is using a mouse button other than the primary one.\n        if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n            return;\n        }\n        // If we've got handles, we need to disable the tap highlight on the entire root element,\n        // otherwise iOS will still add it, even though all the drag interactions on the handle\n        // are disabled.\n        if (this._handles.length) {\n            const rootStyles = rootElement.style;\n            this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n            rootStyles.webkitTapHighlightColor = 'transparent';\n        }\n        this._hasStartedDragging = this._hasMoved = false;\n        // Avoid multiple subscriptions and memory leaks when multi touch\n        // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n        this._removeSubscriptions();\n        this._initialClientRect = this._rootElement.getBoundingClientRect();\n        this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n        this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n        this._scrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n        if (this._boundaryElement) {\n            this._boundaryRect = getMutableClientRect(this._boundaryElement);\n        }\n        // If we have a custom preview we can't know ahead of time how large it'll be so we position\n        // it next to the cursor. The exception is when the consumer has opted into making the preview\n        // the same size as the root element, in which case we do know the size.\n        const previewTemplate = this._previewTemplate;\n        this._pickupPositionInElement =\n            previewTemplate && previewTemplate.template && !previewTemplate.matchSize\n                ? { x: 0, y: 0 }\n                : this._getPointerPositionInElement(this._initialClientRect, referenceElement, event);\n        const pointerPosition = (this._pickupPositionOnPage =\n            this._lastKnownPointerPosition =\n                this._getPointerPositionOnPage(event));\n        this._pointerDirectionDelta = { x: 0, y: 0 };\n        this._pointerPositionAtLastDirectionChange = { x: pointerPosition.x, y: pointerPosition.y };\n        this._dragStartTime = Date.now();\n        this._dragDropRegistry.startDragging(this, event);\n    }\n    /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n    _cleanupDragArtifacts(event) {\n        // Restore the element's visibility and insert it at its old position in the DOM.\n        // It's important that we maintain the position, because moving the element around in the DOM\n        // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n        // while moving the existing elements in all other cases.\n        toggleVisibility(this._rootElement, true, dragImportantProperties);\n        this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._initialClientRect =\n            this._boundaryRect =\n                this._previewRect =\n                    this._initialTransform =\n                        undefined;\n        // Re-enter the NgZone since we bound `document` events on the outside.\n        this._ngZone.run(() => {\n            const container = this._dropContainer;\n            const currentIndex = container.getItemIndex(this);\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            const distance = this._getDragDistance(pointerPosition);\n            const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n            this.ended.next({ source: this, distance, dropPoint: pointerPosition, event });\n            this.dropped.next({\n                item: this,\n                currentIndex,\n                previousIndex: this._initialIndex,\n                container: container,\n                previousContainer: this._initialContainer,\n                isPointerOverContainer,\n                distance,\n                dropPoint: pointerPosition,\n                event,\n            });\n            container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n            this._dropContainer = this._initialContainer;\n        });\n    }\n    /**\n     * Updates the item's position in its drop container, or moves it\n     * into a new one, depending on its current drag position.\n     */\n    _updateActiveDropContainer({ x, y }, { x: rawX, y: rawY }) {\n        // Drop container that draggable has been moved into.\n        let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n        // If we couldn't find a new container to move the item into, and the item has left its\n        // initial container, check whether the it's over the initial container. This handles the\n        // case where two containers are connected one way and the user tries to undo dragging an\n        // item into a new container.\n        if (!newContainer &&\n            this._dropContainer !== this._initialContainer &&\n            this._initialContainer._isOverContainer(x, y)) {\n            newContainer = this._initialContainer;\n        }\n        if (newContainer && newContainer !== this._dropContainer) {\n            this._ngZone.run(() => {\n                // Notify the old container that the item has left.\n                this.exited.next({ item: this, container: this._dropContainer });\n                this._dropContainer.exit(this);\n                // Notify the new container that the item has entered.\n                this._dropContainer = newContainer;\n                this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n                    // If we're re-entering the initial container and sorting is disabled,\n                    // put item the into its starting index to begin with.\n                    newContainer.sortingDisabled\n                    ? this._initialIndex\n                    : undefined);\n                this.entered.next({\n                    item: this,\n                    container: newContainer,\n                    currentIndex: newContainer.getItemIndex(this),\n                });\n            });\n        }\n        // Dragging may have been interrupted as a result of the events above.\n        if (this.isDragging()) {\n            this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n            this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n            if (this.constrainPosition) {\n                this._applyPreviewTransform(x, y);\n            }\n            else {\n                this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n            }\n        }\n    }\n    /**\n     * Creates the element that will be rendered next to the user's pointer\n     * and will be used as a preview of the element that is being dragged.\n     */\n    _createPreviewElement() {\n        const previewConfig = this._previewTemplate;\n        const previewClass = this.previewClass;\n        const previewTemplate = previewConfig ? previewConfig.template : null;\n        let preview;\n        if (previewTemplate && previewConfig) {\n            // Measure the element before we've inserted the preview\n            // since the insertion could throw off the measurement.\n            const rootRect = previewConfig.matchSize ? this._initialClientRect : null;\n            const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n            viewRef.detectChanges();\n            preview = getRootNode(viewRef, this._document);\n            this._previewRef = viewRef;\n            if (previewConfig.matchSize) {\n                matchElementSize(preview, rootRect);\n            }\n            else {\n                preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n            }\n        }\n        else {\n            preview = deepCloneNode(this._rootElement);\n            matchElementSize(preview, this._initialClientRect);\n            if (this._initialTransform) {\n                preview.style.transform = this._initialTransform;\n            }\n        }\n        extendStyles(preview.style, {\n            // It's important that we disable the pointer events on the preview, because\n            // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n            'pointer-events': 'none',\n            // We have to reset the margin, because it can throw off positioning relative to the viewport.\n            'margin': '0',\n            'position': 'fixed',\n            'top': '0',\n            'left': '0',\n            'z-index': `${this._config.zIndex || 1000}`,\n        }, dragImportantProperties);\n        toggleNativeDragInteractions(preview, false);\n        preview.classList.add('cdk-drag-preview');\n        preview.setAttribute('dir', this._direction);\n        if (previewClass) {\n            if (Array.isArray(previewClass)) {\n                previewClass.forEach(className => preview.classList.add(className));\n            }\n            else {\n                preview.classList.add(previewClass);\n            }\n        }\n        return preview;\n    }\n    /**\n     * Animates the preview element from its current position to the location of the drop placeholder.\n     * @returns Promise that resolves when the animation completes.\n     */\n    _animatePreviewToPlaceholder() {\n        // If the user hasn't moved yet, the transitionend event won't fire.\n        if (!this._hasMoved) {\n            return Promise.resolve();\n        }\n        const placeholderRect = this._placeholder.getBoundingClientRect();\n        // Apply the class that adds a transition to the preview.\n        this._preview.classList.add('cdk-drag-animating');\n        // Move the preview to the placeholder position.\n        this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n        // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n        // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n        // apply its style, we take advantage of the available info to figure out whether we need to\n        // bind the event in the first place.\n        const duration = getTransformTransitionDurationInMs(this._preview);\n        if (duration === 0) {\n            return Promise.resolve();\n        }\n        return this._ngZone.runOutsideAngular(() => {\n            return new Promise(resolve => {\n                const handler = ((event) => {\n                    if (!event ||\n                        (_getEventTarget(event) === this._preview && event.propertyName === 'transform')) {\n                        this._preview?.removeEventListener('transitionend', handler);\n                        resolve();\n                        clearTimeout(timeout);\n                    }\n                });\n                // If a transition is short enough, the browser might not fire the `transitionend` event.\n                // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n                // fire if the transition hasn't completed when it was supposed to.\n                const timeout = setTimeout(handler, duration * 1.5);\n                this._preview.addEventListener('transitionend', handler);\n            });\n        });\n    }\n    /** Creates an element that will be shown instead of the current element while dragging. */\n    _createPlaceholderElement() {\n        const placeholderConfig = this._placeholderTemplate;\n        const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n        let placeholder;\n        if (placeholderTemplate) {\n            this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n            this._placeholderRef.detectChanges();\n            placeholder = getRootNode(this._placeholderRef, this._document);\n        }\n        else {\n            placeholder = deepCloneNode(this._rootElement);\n        }\n        // Stop pointer events on the preview so the user can't\n        // interact with it while the preview is animating.\n        placeholder.style.pointerEvents = 'none';\n        placeholder.classList.add('cdk-drag-placeholder');\n        return placeholder;\n    }\n    /**\n     * Figures out the coordinates at which an element was picked up.\n     * @param referenceElement Element that initiated the dragging.\n     * @param event Event that initiated the dragging.\n     */\n    _getPointerPositionInElement(elementRect, referenceElement, event) {\n        const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n        const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n        const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n        const scrollPosition = this._getViewportScrollPosition();\n        const x = point.pageX - referenceRect.left - scrollPosition.left;\n        const y = point.pageY - referenceRect.top - scrollPosition.top;\n        return {\n            x: referenceRect.left - elementRect.left + x,\n            y: referenceRect.top - elementRect.top + y,\n        };\n    }\n    /** Determines the point of the page that was touched by the user. */\n    _getPointerPositionOnPage(event) {\n        const scrollPosition = this._getViewportScrollPosition();\n        const point = isTouchEvent(event)\n            ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n                // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n                // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n                // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n                // throwing an error. The value returned here will be incorrect, but since this only\n                // breaks inside a developer tool and the value is only used for secondary information,\n                // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n                event.touches[0] || event.changedTouches[0] || { pageX: 0, pageY: 0 }\n            : event;\n        const x = point.pageX - scrollPosition.left;\n        const y = point.pageY - scrollPosition.top;\n        // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n        // coordinate system\n        if (this._ownerSVGElement) {\n            const svgMatrix = this._ownerSVGElement.getScreenCTM();\n            if (svgMatrix) {\n                const svgPoint = this._ownerSVGElement.createSVGPoint();\n                svgPoint.x = x;\n                svgPoint.y = y;\n                return svgPoint.matrixTransform(svgMatrix.inverse());\n            }\n        }\n        return { x, y };\n    }\n    /** Gets the pointer position on the page, accounting for any position constraints. */\n    _getConstrainedPointerPosition(point) {\n        const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n        let { x, y } = this.constrainPosition\n            ? this.constrainPosition(point, this, this._initialClientRect, this._pickupPositionInElement)\n            : point;\n        if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n            y = this._pickupPositionOnPage.y;\n        }\n        else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n            x = this._pickupPositionOnPage.x;\n        }\n        if (this._boundaryRect) {\n            const { x: pickupX, y: pickupY } = this._pickupPositionInElement;\n            const boundaryRect = this._boundaryRect;\n            const { width: previewWidth, height: previewHeight } = this._getPreviewRect();\n            const minY = boundaryRect.top + pickupY;\n            const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n            const minX = boundaryRect.left + pickupX;\n            const maxX = boundaryRect.right - (previewWidth - pickupX);\n            x = clamp$1(x, minX, maxX);\n            y = clamp$1(y, minY, maxY);\n        }\n        return { x, y };\n    }\n    /** Updates the current drag delta, based on the user's current pointer position on the page. */\n    _updatePointerDirectionDelta(pointerPositionOnPage) {\n        const { x, y } = pointerPositionOnPage;\n        const delta = this._pointerDirectionDelta;\n        const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n        // Amount of pixels the user has dragged since the last time the direction changed.\n        const changeX = Math.abs(x - positionSinceLastChange.x);\n        const changeY = Math.abs(y - positionSinceLastChange.y);\n        // Because we handle pointer events on a per-pixel basis, we don't want the delta\n        // to change for every pixel, otherwise anything that depends on it can look erratic.\n        // To make the delta more consistent, we track how much the user has moved since the last\n        // delta change and we only update it after it has reached a certain threshold.\n        if (changeX > this._config.pointerDirectionChangeThreshold) {\n            delta.x = x > positionSinceLastChange.x ? 1 : -1;\n            positionSinceLastChange.x = x;\n        }\n        if (changeY > this._config.pointerDirectionChangeThreshold) {\n            delta.y = y > positionSinceLastChange.y ? 1 : -1;\n            positionSinceLastChange.y = y;\n        }\n        return delta;\n    }\n    /** Toggles the native drag interactions, based on how many handles are registered. */\n    _toggleNativeDragInteractions() {\n        if (!this._rootElement || !this._handles) {\n            return;\n        }\n        const shouldEnable = this._handles.length > 0 || !this.isDragging();\n        if (shouldEnable !== this._nativeInteractionsEnabled) {\n            this._nativeInteractionsEnabled = shouldEnable;\n            toggleNativeDragInteractions(this._rootElement, shouldEnable);\n        }\n    }\n    /** Removes the manually-added event listeners from the root element. */\n    _removeRootElementListeners(element) {\n        element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n    }\n    /**\n     * Applies a `transform` to the root element, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyRootElementTransform(x, y) {\n        const transform = getTransform(x, y);\n        const styles = this._rootElement.style;\n        // Cache the previous transform amount only after the first drag sequence, because\n        // we don't want our own transforms to stack on top of each other.\n        // Should be excluded none because none + translate3d(x, y, x) is invalid css\n        if (this._initialTransform == null) {\n            this._initialTransform =\n                styles.transform && styles.transform != 'none' ? styles.transform : '';\n        }\n        // Preserve the previous `transform` value, if there was one. Note that we apply our own\n        // transform before the user's, because things like rotation can affect which direction\n        // the element will be translated towards.\n        styles.transform = combineTransforms(transform, this._initialTransform);\n    }\n    /**\n     * Applies a `transform` to the preview, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyPreviewTransform(x, y) {\n        // Only apply the initial transform if the preview is a clone of the original element, otherwise\n        // it could be completely different and the transform might not make sense anymore.\n        const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n        const transform = getTransform(x, y);\n        this._preview.style.transform = combineTransforms(transform, initialTransform);\n    }\n    /**\n     * Gets the distance that the user has dragged during the current drag sequence.\n     * @param currentPosition Current position of the user's pointer.\n     */\n    _getDragDistance(currentPosition) {\n        const pickupPosition = this._pickupPositionOnPage;\n        if (pickupPosition) {\n            return { x: currentPosition.x - pickupPosition.x, y: currentPosition.y - pickupPosition.y };\n        }\n        return { x: 0, y: 0 };\n    }\n    /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n    _cleanupCachedDimensions() {\n        this._boundaryRect = this._previewRect = undefined;\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the element is still inside its boundary after the viewport has been resized.\n     * If not, the position is adjusted so that the element fits again.\n     */\n    _containInsideBoundaryOnResize() {\n        let { x, y } = this._passiveTransform;\n        if ((x === 0 && y === 0) || this.isDragging() || !this._boundaryElement) {\n            return;\n        }\n        // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n        const elementRect = this._rootElement.getBoundingClientRect();\n        const boundaryRect = this._boundaryElement.getBoundingClientRect();\n        // It's possible that the element got hidden away after dragging (e.g. by switching to a\n        // different tab). Don't do anything in this case so we don't clear the user's position.\n        if ((boundaryRect.width === 0 && boundaryRect.height === 0) ||\n            (elementRect.width === 0 && elementRect.height === 0)) {\n            return;\n        }\n        const leftOverflow = boundaryRect.left - elementRect.left;\n        const rightOverflow = elementRect.right - boundaryRect.right;\n        const topOverflow = boundaryRect.top - elementRect.top;\n        const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n        // If the element has become wider than the boundary, we can't\n        // do much to make it fit so we just anchor it to the left.\n        if (boundaryRect.width > elementRect.width) {\n            if (leftOverflow > 0) {\n                x += leftOverflow;\n            }\n            if (rightOverflow > 0) {\n                x -= rightOverflow;\n            }\n        }\n        else {\n            x = 0;\n        }\n        // If the element has become taller than the boundary, we can't\n        // do much to make it fit so we just anchor it to the top.\n        if (boundaryRect.height > elementRect.height) {\n            if (topOverflow > 0) {\n                y += topOverflow;\n            }\n            if (bottomOverflow > 0) {\n                y -= bottomOverflow;\n            }\n        }\n        else {\n            y = 0;\n        }\n        if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n            this.setFreeDragPosition({ y, x });\n        }\n    }\n    /** Gets the drag start delay, based on the event type. */\n    _getDragStartDelay(event) {\n        const value = this.dragStartDelay;\n        if (typeof value === 'number') {\n            return value;\n        }\n        else if (isTouchEvent(event)) {\n            return value.touch;\n        }\n        return value ? value.mouse : 0;\n    }\n    /** Updates the internal state of the draggable element when scrolling has occurred. */\n    _updateOnScroll(event) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n            const target = _getEventTarget(event);\n            // ClientRect dimensions are based on the scroll position of the page and its parent\n            // node so we have to update the cached boundary ClientRect if the user has scrolled.\n            if (this._boundaryRect &&\n                target !== this._boundaryElement &&\n                target.contains(this._boundaryElement)) {\n                adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n            }\n            this._pickupPositionOnPage.x += scrollDifference.left;\n            this._pickupPositionOnPage.y += scrollDifference.top;\n            // If we're in free drag mode, we have to update the active transform, because\n            // it isn't relative to the viewport like the preview inside a drop list.\n            if (!this._dropContainer) {\n                this._activeTransform.x -= scrollDifference.left;\n                this._activeTransform.y -= scrollDifference.top;\n                this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n            }\n        }\n    }\n    /** Gets the scroll position of the viewport. */\n    _getViewportScrollPosition() {\n        return (this._parentPositions.positions.get(this._document)?.scrollPosition ||\n            this._parentPositions.getViewportScrollPosition());\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (this._cachedShadowRoot === undefined) {\n            this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Gets the element into which the drag preview should be inserted. */\n    _getPreviewInsertionPoint(initialParent, shadowRoot) {\n        const previewContainer = this._previewContainer || 'global';\n        if (previewContainer === 'parent') {\n            return initialParent;\n        }\n        if (previewContainer === 'global') {\n            const documentRef = this._document;\n            // We can't use the body if the user is in fullscreen mode,\n            // because the preview will render under the fullscreen element.\n            // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n            return (shadowRoot ||\n                documentRef.fullscreenElement ||\n                documentRef.webkitFullscreenElement ||\n                documentRef.mozFullScreenElement ||\n                documentRef.msFullscreenElement ||\n                documentRef.body);\n        }\n        return coerceElement(previewContainer);\n    }\n    /** Lazily resolves and returns the dimensions of the preview. */\n    _getPreviewRect() {\n        // Cache the preview element rect if we haven't cached it already or if\n        // we cached it too early before the element dimensions were computed.\n        if (!this._previewRect || (!this._previewRect.width && !this._previewRect.height)) {\n            this._previewRect = this._preview\n                ? this._preview.getBoundingClientRect()\n                : this._initialClientRect;\n        }\n        return this._previewRect;\n    }\n    /** Gets a handle that is the target of an event. */\n    _getTargetHandle(event) {\n        return this._handles.find(handle => {\n            return event.target && (event.target === handle || handle.contains(event.target));\n        });\n    }\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n    // Round the transforms since some browsers will\n    // blur the elements for sub-pixel transforms.\n    return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n    // This function is called for every pixel that the user has dragged so we need it to be\n    // as fast as possible. Since we only bind mouse events and touch events, we can assume\n    // that if the event's name starts with `t`, it's a touch event.\n    return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n    const rootNodes = viewRef.rootNodes;\n    if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n        return rootNodes[0];\n    }\n    const wrapper = _document.createElement('div');\n    rootNodes.forEach(node => wrapper.appendChild(node));\n    return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n    target.style.width = `${sourceRect.width}px`;\n    target.style.height = `${sourceRect.height}px`;\n    target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n    const from = clamp(fromIndex, array.length - 1);\n    const to = clamp(toIndex, array.length - 1);\n    if (from === to) {\n        return;\n    }\n    const target = array[from];\n    const delta = to < from ? -1 : 1;\n    for (let i = from; i !== to; i += delta) {\n        array[i] = array[i + delta];\n    }\n    array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const from = clamp(currentIndex, currentArray.length - 1);\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n    }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray[currentIndex]);\n    }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n    return Math.max(0, Math.min(max, value));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n    constructor(_element, _dragDropRegistry) {\n        this._element = _element;\n        this._dragDropRegistry = _dragDropRegistry;\n        /** Cache of the dimensions of all the items inside the container. */\n        this._itemPositions = [];\n        /** Direction in which the list is oriented. */\n        this.orientation = 'vertical';\n        /**\n         * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n         * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n         * overlap with the swapped item after the swapping occurred.\n         */\n        this._previousSwap = {\n            drag: null,\n            delta: 0,\n            overlaps: false,\n        };\n    }\n    /**\n     * To be called when the drag sequence starts.\n     * @param items Items that are currently in the list.\n     */\n    start(items) {\n        this.withItems(items);\n    }\n    /**\n     * To be called when an item is being sorted.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    sort(item, pointerX, pointerY, pointerDelta) {\n        const siblings = this._itemPositions;\n        const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n        if (newIndex === -1 && siblings.length > 0) {\n            return null;\n        }\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n        const siblingAtNewPosition = siblings[newIndex];\n        const currentPosition = siblings[currentIndex].clientRect;\n        const newPosition = siblingAtNewPosition.clientRect;\n        const delta = currentIndex > newIndex ? 1 : -1;\n        // How many pixels the item's placeholder should be offset.\n        const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n        // How many pixels all the other items should be offset.\n        const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n        // Save the previous order of the items before moving the item to its new index.\n        // We use this to check whether an item has been moved as a result of the sorting.\n        const oldOrder = siblings.slice();\n        // Shuffle the array in place.\n        moveItemInArray(siblings, currentIndex, newIndex);\n        siblings.forEach((sibling, index) => {\n            // Don't do anything if the position hasn't changed.\n            if (oldOrder[index] === sibling) {\n                return;\n            }\n            const isDraggedItem = sibling.drag === item;\n            const offset = isDraggedItem ? itemOffset : siblingOffset;\n            const elementToOffset = isDraggedItem\n                ? item.getPlaceholderElement()\n                : sibling.drag.getRootElement();\n            // Update the offset to reflect the new position.\n            sibling.offset += offset;\n            // Since we're moving the items with a `transform`, we need to adjust their cached\n            // client rects to reflect their new position, as well as swap their positions in the cache.\n            // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n            // elements may be mid-animation which will give us a wrong result.\n            if (isHorizontal) {\n                // Round the transforms since some browsers will\n                // blur the elements, for sub-pixel transforms.\n                elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n                adjustClientRect(sibling.clientRect, 0, offset);\n            }\n            else {\n                elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n                adjustClientRect(sibling.clientRect, offset, 0);\n            }\n        });\n        // Note that it's important that we do this after the client rects have been adjusted.\n        this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n        this._previousSwap.drag = siblingAtNewPosition.drag;\n        this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n        return { previousIndex: currentIndex, currentIndex: newIndex };\n    }\n    /**\n     * Called when an item is being moved into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        const newIndex = index == null || index < 0\n            ? // We use the coordinates of where the item entered the drop\n                // zone to figure out at which index it should be inserted.\n                this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n            : index;\n        const activeDraggables = this._activeDraggables;\n        const currentIndex = activeDraggables.indexOf(item);\n        const placeholder = item.getPlaceholderElement();\n        let newPositionReference = activeDraggables[newIndex];\n        // If the item at the new position is the same as the item that is being dragged,\n        // it means that we're trying to restore the item to its initial position. In this\n        // case we should use the next item from the list as the reference.\n        if (newPositionReference === item) {\n            newPositionReference = activeDraggables[newIndex + 1];\n        }\n        // If we didn't find a new position reference, it means that either the item didn't start off\n        // in this container, or that the item requested to be inserted at the end of the list.\n        if (!newPositionReference &&\n            (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) &&\n            this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n            newPositionReference = activeDraggables[0];\n        }\n        // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n        // into another container and back again), we have to ensure that it isn't duplicated.\n        if (currentIndex > -1) {\n            activeDraggables.splice(currentIndex, 1);\n        }\n        // Don't use items that are being dragged as a reference, because\n        // their element has been moved down to the bottom of the body.\n        if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n            const element = newPositionReference.getRootElement();\n            element.parentElement.insertBefore(placeholder, element);\n            activeDraggables.splice(newIndex, 0, item);\n        }\n        else {\n            coerceElement(this._element).appendChild(placeholder);\n            activeDraggables.push(item);\n        }\n        // The transform needs to be cleared so it doesn't throw off the measurements.\n        placeholder.style.transform = '';\n        // Note that usually `start` is called together with `enter` when an item goes into a new\n        // container. This will cache item positions, but we need to refresh them since the amount\n        // of items has changed.\n        this._cacheItemPositions();\n    }\n    /** Sets the items that are currently part of the list. */\n    withItems(items) {\n        this._activeDraggables = items.slice();\n        this._cacheItemPositions();\n    }\n    /** Assigns a sort predicate to the strategy. */\n    withSortPredicate(predicate) {\n        this._sortPredicate = predicate;\n    }\n    /** Resets the strategy to its initial state before dragging was started. */\n    reset() {\n        // TODO(crisbeto): may have to wait for the animations to finish.\n        this._activeDraggables.forEach(item => {\n            const rootElement = item.getRootElement();\n            if (rootElement) {\n                const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n                rootElement.style.transform = initialTransform || '';\n            }\n        });\n        this._itemPositions = [];\n        this._activeDraggables = [];\n        this._previousSwap.drag = null;\n        this._previousSwap.delta = 0;\n        this._previousSwap.overlaps = false;\n    }\n    /**\n     * Gets a snapshot of items currently in the list.\n     * Can include items that we dragged in from another list.\n     */\n    getActiveItemsSnapshot() {\n        return this._activeDraggables;\n    }\n    /** Gets the index of a specific item. */\n    getItemIndex(item) {\n        // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n        // The rest of the logic still stands no matter what orientation we're in, however\n        // we need to invert the array when determining the index.\n        const items = this.orientation === 'horizontal' && this.direction === 'rtl'\n            ? this._itemPositions.slice().reverse()\n            : this._itemPositions;\n        return items.findIndex(currentItem => currentItem.drag === item);\n    }\n    /** Used to notify the strategy that the scroll position has changed. */\n    updateOnScroll(topDifference, leftDifference) {\n        // Since we know the amount that the user has scrolled we can shift all of the\n        // client rectangles ourselves. This is cheaper than re-measuring everything and\n        // we can avoid inconsistent behavior where we might be measuring the element before\n        // its position has changed.\n        this._itemPositions.forEach(({ clientRect }) => {\n            adjustClientRect(clientRect, topDifference, leftDifference);\n        });\n        // We need two loops for this, because we want all of the cached\n        // positions to be up-to-date before we re-sort the item.\n        this._itemPositions.forEach(({ drag }) => {\n            if (this._dragDropRegistry.isDragging(drag)) {\n                // We need to re-sort the item manually, because the pointer move\n                // events won't be dispatched while the user is scrolling.\n                drag._sortFromLastPointerPosition();\n            }\n        });\n    }\n    /** Refreshes the position cache of the items and sibling containers. */\n    _cacheItemPositions() {\n        const isHorizontal = this.orientation === 'horizontal';\n        this._itemPositions = this._activeDraggables\n            .map(drag => {\n            const elementToMeasure = drag.getVisibleElement();\n            return {\n                drag,\n                offset: 0,\n                initialTransform: elementToMeasure.style.transform || '',\n                clientRect: getMutableClientRect(elementToMeasure),\n            };\n        })\n            .sort((a, b) => {\n            return isHorizontal\n                ? a.clientRect.left - b.clientRect.left\n                : a.clientRect.top - b.clientRect.top;\n        });\n    }\n    /**\n     * Gets the offset in pixels by which the item that is being dragged should be moved.\n     * @param currentPosition Current position of the item.\n     * @param newPosition Position of the item where the current item should be moved.\n     * @param delta Direction in which the user is moving.\n     */\n    _getItemOffsetPx(currentPosition, newPosition, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        let itemOffset = isHorizontal\n            ? newPosition.left - currentPosition.left\n            : newPosition.top - currentPosition.top;\n        // Account for differences in the item width/height.\n        if (delta === -1) {\n            itemOffset += isHorizontal\n                ? newPosition.width - currentPosition.width\n                : newPosition.height - currentPosition.height;\n        }\n        return itemOffset;\n    }\n    /**\n     * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n     * @param currentIndex Index of the item currently being dragged.\n     * @param siblings All of the items in the list.\n     * @param delta Direction in which the user is moving.\n     */\n    _getSiblingOffsetPx(currentIndex, siblings, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentPosition = siblings[currentIndex].clientRect;\n        const immediateSibling = siblings[currentIndex + delta * -1];\n        let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n        if (immediateSibling) {\n            const start = isHorizontal ? 'left' : 'top';\n            const end = isHorizontal ? 'right' : 'bottom';\n            // Get the spacing between the start of the current item and the end of the one immediately\n            // after it in the direction in which the user is dragging, or vice versa. We add it to the\n            // offset in order to push the element to where it will be when it's inline and is influenced\n            // by the `margin` of its siblings.\n            if (delta === -1) {\n                siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n            }\n            else {\n                siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n            }\n        }\n        return siblingOffset;\n    }\n    /**\n     * Checks if pointer is entering in the first position\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     */\n    _shouldEnterAsFirstChild(pointerX, pointerY) {\n        if (!this._activeDraggables.length) {\n            return false;\n        }\n        const itemPositions = this._itemPositions;\n        const isHorizontal = this.orientation === 'horizontal';\n        // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n        // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n        const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n        if (reversed) {\n            const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n            return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n        }\n        else {\n            const firstItemRect = itemPositions[0].clientRect;\n            return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n        }\n    }\n    /**\n     * Gets the index of an item in the drop container, based on the position of the user's pointer.\n     * @param item Item that is being sorted.\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     * @param delta Direction in which the user is moving their pointer.\n     */\n    _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const index = this._itemPositions.findIndex(({ drag, clientRect }) => {\n            // Skip the item itself.\n            if (drag === item) {\n                return false;\n            }\n            if (delta) {\n                const direction = isHorizontal ? delta.x : delta.y;\n                // If the user is still hovering over the same item as last time, their cursor hasn't left\n                // the item after we made the swap, and they didn't change the direction in which they're\n                // dragging, we don't consider it a direction swap.\n                if (drag === this._previousSwap.drag &&\n                    this._previousSwap.overlaps &&\n                    direction === this._previousSwap.delta) {\n                    return false;\n                }\n            }\n            return isHorizontal\n                ? // Round these down since most browsers report client rects with\n                    // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n                    pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right)\n                : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n        });\n        return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n    constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n        this._dragDropRegistry = _dragDropRegistry;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        /** Whether starting a dragging sequence from this container is disabled. */\n        this.disabled = false;\n        /** Whether sorting items within the list is disabled. */\n        this.sortingDisabled = false;\n        /**\n         * Whether auto-scrolling the view when the user\n         * moves their pointer close to the edges is disabled.\n         */\n        this.autoScrollDisabled = false;\n        /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n        this.autoScrollStep = 2;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Function that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits right before dragging has started. */\n        this.beforeStarted = new Subject();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new Subject();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new Subject();\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new Subject();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new Subject();\n        /** Emits when a dragging sequence is started in a list connected to the current one. */\n        this.receivingStarted = new Subject();\n        /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n        this.receivingStopped = new Subject();\n        /** Whether an item in the list is being dragged. */\n        this._isDragging = false;\n        /** Draggable items in the container. */\n        this._draggables = [];\n        /** Drop lists that are connected to the current one. */\n        this._siblings = [];\n        /** Connected siblings that currently have a dragged item. */\n        this._activeSiblings = new Set();\n        /** Subscription to the window being scrolled. */\n        this._viewportScrollSubscription = Subscription.EMPTY;\n        /** Vertical direction in which the list is currently scrolling. */\n        this._verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n        /** Horizontal direction in which the list is currently scrolling. */\n        this._horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n        /** Used to signal to the current auto-scroll sequence when to stop. */\n        this._stopScrollTimers = new Subject();\n        /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n        this._cachedShadowRoot = null;\n        /** Starts the interval that'll auto-scroll the element. */\n        this._startScrollInterval = () => {\n            this._stopScrolling();\n            interval(0, animationFrameScheduler)\n                .pipe(takeUntil(this._stopScrollTimers))\n                .subscribe(() => {\n                const node = this._scrollNode;\n                const scrollStep = this.autoScrollStep;\n                if (this._verticalScrollDirection === 1 /* AutoScrollVerticalDirection.UP */) {\n                    node.scrollBy(0, -scrollStep);\n                }\n                else if (this._verticalScrollDirection === 2 /* AutoScrollVerticalDirection.DOWN */) {\n                    node.scrollBy(0, scrollStep);\n                }\n                if (this._horizontalScrollDirection === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n                    node.scrollBy(-scrollStep, 0);\n                }\n                else if (this._horizontalScrollDirection === 2 /* AutoScrollHorizontalDirection.RIGHT */) {\n                    node.scrollBy(scrollStep, 0);\n                }\n            });\n        };\n        this.element = coerceElement(element);\n        this._document = _document;\n        this.withScrollableParents([this.element]);\n        _dragDropRegistry.registerDropContainer(this);\n        this._parentPositions = new ParentPositionTracker(_document);\n        this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n        this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    }\n    /** Removes the drop list functionality from the DOM element. */\n    dispose() {\n        this._stopScrolling();\n        this._stopScrollTimers.complete();\n        this._viewportScrollSubscription.unsubscribe();\n        this.beforeStarted.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this.sorted.complete();\n        this.receivingStarted.complete();\n        this.receivingStopped.complete();\n        this._activeSiblings.clear();\n        this._scrollNode = null;\n        this._parentPositions.clear();\n        this._dragDropRegistry.removeDropContainer(this);\n    }\n    /** Whether an item from this list is currently being dragged. */\n    isDragging() {\n        return this._isDragging;\n    }\n    /** Starts dragging an item. */\n    start() {\n        this._draggingStarted();\n        this._notifyReceivingSiblings();\n    }\n    /**\n     * Attempts to move an item into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        this._draggingStarted();\n        // If sorting is disabled, we want the item to return to its starting\n        // position if the user is returning it to its initial container.\n        if (index == null && this.sortingDisabled) {\n            index = this._draggables.indexOf(item);\n        }\n        this._sortStrategy.enter(item, pointerX, pointerY, index);\n        // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n        // can change when the sort strategy moves the item around inside `enter`.\n        this._cacheParentPositions();\n        // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n        this._notifyReceivingSiblings();\n        this.entered.next({ item, container: this, currentIndex: this.getItemIndex(item) });\n    }\n    /**\n     * Removes an item from the container after it was dragged into another container by the user.\n     * @param item Item that was dragged out.\n     */\n    exit(item) {\n        this._reset();\n        this.exited.next({ item, container: this });\n    }\n    /**\n     * Drops an item into this container.\n     * @param item Item being dropped into the container.\n     * @param currentIndex Index at which the item should be inserted.\n     * @param previousIndex Index of the item when dragging started.\n     * @param previousContainer Container from which the item got dragged in.\n     * @param isPointerOverContainer Whether the user's pointer was over the\n     *    container when the item was dropped.\n     * @param distance Distance the user has dragged since the start of the dragging sequence.\n     * @param event Event that triggered the dropping sequence.\n     *\n     * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n     */\n    drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n        this._reset();\n        this.dropped.next({\n            item,\n            currentIndex,\n            previousIndex,\n            container: this,\n            previousContainer,\n            isPointerOverContainer,\n            distance,\n            dropPoint,\n            event,\n        });\n    }\n    /**\n     * Sets the draggable items that are a part of this list.\n     * @param items Items that are a part of this list.\n     */\n    withItems(items) {\n        const previousItems = this._draggables;\n        this._draggables = items;\n        items.forEach(item => item._withDropContainer(this));\n        if (this.isDragging()) {\n            const draggedItems = previousItems.filter(item => item.isDragging());\n            // If all of the items being dragged were removed\n            // from the list, abort the current drag sequence.\n            if (draggedItems.every(item => items.indexOf(item) === -1)) {\n                this._reset();\n            }\n            else {\n                this._sortStrategy.withItems(this._draggables);\n            }\n        }\n        return this;\n    }\n    /** Sets the layout direction of the drop list. */\n    withDirection(direction) {\n        this._sortStrategy.direction = direction;\n        return this;\n    }\n    /**\n     * Sets the containers that are connected to this one. When two or more containers are\n     * connected, the user will be allowed to transfer items between them.\n     * @param connectedTo Other containers that the current containers should be connected to.\n     */\n    connectedTo(connectedTo) {\n        this._siblings = connectedTo.slice();\n        return this;\n    }\n    /**\n     * Sets the orientation of the container.\n     * @param orientation New orientation for the container.\n     */\n    withOrientation(orientation) {\n        // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n        // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n        this._sortStrategy.orientation = orientation;\n        return this;\n    }\n    /**\n     * Sets which parent elements are can be scrolled while the user is dragging.\n     * @param elements Elements that can be scrolled.\n     */\n    withScrollableParents(elements) {\n        const element = coerceElement(this.element);\n        // We always allow the current element to be scrollable\n        // so we need to ensure that it's in the array.\n        this._scrollableElements =\n            elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n        return this;\n    }\n    /** Gets the scrollable parents that are registered with this drop container. */\n    getScrollableParents() {\n        return this._scrollableElements;\n    }\n    /**\n     * Figures out the index of an item in the container.\n     * @param item Item whose index should be determined.\n     */\n    getItemIndex(item) {\n        return this._isDragging\n            ? this._sortStrategy.getItemIndex(item)\n            : this._draggables.indexOf(item);\n    }\n    /**\n     * Whether the list is able to receive the item that\n     * is currently being dragged inside a connected drop list.\n     */\n    isReceiving() {\n        return this._activeSiblings.size > 0;\n    }\n    /**\n     * Sorts an item inside the container based on its position.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    _sortItem(item, pointerX, pointerY, pointerDelta) {\n        // Don't sort the item if sorting is disabled or it's out of range.\n        if (this.sortingDisabled ||\n            !this._clientRect ||\n            !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n            return;\n        }\n        const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n        if (result) {\n            this.sorted.next({\n                previousIndex: result.previousIndex,\n                currentIndex: result.currentIndex,\n                container: this,\n                item,\n            });\n        }\n    }\n    /**\n     * Checks whether the user's pointer is close to the edges of either the\n     * viewport or the drop list and starts the auto-scroll sequence.\n     * @param pointerX User's pointer position along the x axis.\n     * @param pointerY User's pointer position along the y axis.\n     */\n    _startScrollingIfNecessary(pointerX, pointerY) {\n        if (this.autoScrollDisabled) {\n            return;\n        }\n        let scrollNode;\n        let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n        let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n        // Check whether we should start scrolling any of the parent containers.\n        this._parentPositions.positions.forEach((position, element) => {\n            // We have special handling for the `document` below. Also this would be\n            // nicer with a  for...of loop, but it requires changing a compiler flag.\n            if (element === this._document || !position.clientRect || scrollNode) {\n                return;\n            }\n            if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n                [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n                if (verticalScrollDirection || horizontalScrollDirection) {\n                    scrollNode = element;\n                }\n            }\n        });\n        // Otherwise check if we can start scrolling the viewport.\n        if (!verticalScrollDirection && !horizontalScrollDirection) {\n            const { width, height } = this._viewportRuler.getViewportSize();\n            const clientRect = {\n                width,\n                height,\n                top: 0,\n                right: width,\n                bottom: height,\n                left: 0,\n            };\n            verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n            horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n            scrollNode = window;\n        }\n        if (scrollNode &&\n            (verticalScrollDirection !== this._verticalScrollDirection ||\n                horizontalScrollDirection !== this._horizontalScrollDirection ||\n                scrollNode !== this._scrollNode)) {\n            this._verticalScrollDirection = verticalScrollDirection;\n            this._horizontalScrollDirection = horizontalScrollDirection;\n            this._scrollNode = scrollNode;\n            if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n                this._ngZone.runOutsideAngular(this._startScrollInterval);\n            }\n            else {\n                this._stopScrolling();\n            }\n        }\n    }\n    /** Stops any currently-running auto-scroll sequences. */\n    _stopScrolling() {\n        this._stopScrollTimers.next();\n    }\n    /** Starts the dragging sequence within the list. */\n    _draggingStarted() {\n        const styles = coerceElement(this.element).style;\n        this.beforeStarted.next();\n        this._isDragging = true;\n        // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n        // scrolling. The browser seems to round the value based on the snapping points which means\n        // that we can't increment/decrement the scroll position.\n        this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n        styles.scrollSnapType = styles.msScrollSnapType = 'none';\n        this._sortStrategy.start(this._draggables);\n        this._cacheParentPositions();\n        this._viewportScrollSubscription.unsubscribe();\n        this._listenToScrollEvents();\n    }\n    /** Caches the positions of the configured scrollable parents. */\n    _cacheParentPositions() {\n        const element = coerceElement(this.element);\n        this._parentPositions.cache(this._scrollableElements);\n        // The list element is always in the `scrollableElements`\n        // so we can take advantage of the cached `ClientRect`.\n        this._clientRect = this._parentPositions.positions.get(element).clientRect;\n    }\n    /** Resets the container to its initial state. */\n    _reset() {\n        this._isDragging = false;\n        const styles = coerceElement(this.element).style;\n        styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n        this._siblings.forEach(sibling => sibling._stopReceiving(this));\n        this._sortStrategy.reset();\n        this._stopScrolling();\n        this._viewportScrollSubscription.unsubscribe();\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the user's pointer is positioned over the container.\n     * @param x Pointer position along the X axis.\n     * @param y Pointer position along the Y axis.\n     */\n    _isOverContainer(x, y) {\n        return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n    }\n    /**\n     * Figures out whether an item should be moved into a sibling\n     * drop container, based on its current position.\n     * @param item Drag item that is being moved.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _getSiblingContainerFromPosition(item, x, y) {\n        return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n    }\n    /**\n     * Checks whether the drop list can receive the passed-in item.\n     * @param item Item that is being dragged into the list.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _canReceive(item, x, y) {\n        if (!this._clientRect ||\n            !isInsideClientRect(this._clientRect, x, y) ||\n            !this.enterPredicate(item, this)) {\n            return false;\n        }\n        const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n        // If there's no element at the pointer position, then\n        // the client rect is probably scrolled out of the view.\n        if (!elementFromPoint) {\n            return false;\n        }\n        const nativeElement = coerceElement(this.element);\n        // The `ClientRect`, that we're using to find the container over which the user is\n        // hovering, doesn't give us any information on whether the element has been scrolled\n        // out of the view or whether it's overlapping with other containers. This means that\n        // we could end up transferring the item into a container that's invisible or is positioned\n        // below another one. We use the result from `elementFromPoint` to get the top-most element\n        // at the pointer position and to find whether it's one of the intersecting drop containers.\n        return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n    }\n    /**\n     * Called by one of the connected drop lists when a dragging sequence has started.\n     * @param sibling Sibling in which dragging has started.\n     */\n    _startReceiving(sibling, items) {\n        const activeSiblings = this._activeSiblings;\n        if (!activeSiblings.has(sibling) &&\n            items.every(item => {\n                // Note that we have to add an exception to the `enterPredicate` for items that started off\n                // in this drop list. The drag ref has logic that allows an item to return to its initial\n                // container, if it has left the initial container and none of the connected containers\n                // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n                return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n            })) {\n            activeSiblings.add(sibling);\n            this._cacheParentPositions();\n            this._listenToScrollEvents();\n            this.receivingStarted.next({\n                initiator: sibling,\n                receiver: this,\n                items,\n            });\n        }\n    }\n    /**\n     * Called by a connected drop list when dragging has stopped.\n     * @param sibling Sibling whose dragging has stopped.\n     */\n    _stopReceiving(sibling) {\n        this._activeSiblings.delete(sibling);\n        this._viewportScrollSubscription.unsubscribe();\n        this.receivingStopped.next({ initiator: sibling, receiver: this });\n    }\n    /**\n     * Starts listening to scroll events on the viewport.\n     * Used for updating the internal state of the list.\n     */\n    _listenToScrollEvents() {\n        this._viewportScrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(event => {\n            if (this.isDragging()) {\n                const scrollDifference = this._parentPositions.handleScroll(event);\n                if (scrollDifference) {\n                    this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n                }\n            }\n            else if (this.isReceiving()) {\n                this._cacheParentPositions();\n            }\n        });\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (!this._cachedShadowRoot) {\n            const shadowRoot = _getShadowRoot(coerceElement(this.element));\n            this._cachedShadowRoot = (shadowRoot || this._document);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Notifies any siblings that may potentially receive the item. */\n    _notifyReceivingSiblings() {\n        const draggedItems = this._sortStrategy\n            .getActiveItemsSnapshot()\n            .filter(item => item.isDragging());\n        this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n    }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n    const { top, bottom, height } = clientRect;\n    const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n        return 1 /* AutoScrollVerticalDirection.UP */;\n    }\n    else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n        return 2 /* AutoScrollVerticalDirection.DOWN */;\n    }\n    return 0 /* AutoScrollVerticalDirection.NONE */;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n    const { left, right, width } = clientRect;\n    const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n        return 1 /* AutoScrollHorizontalDirection.LEFT */;\n    }\n    else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n        return 2 /* AutoScrollHorizontalDirection.RIGHT */;\n    }\n    return 0 /* AutoScrollHorizontalDirection.NONE */;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n    const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n    const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n    let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    // Note that we here we do some extra checks for whether the element is actually scrollable in\n    // a certain direction and we only assign the scroll direction if it is. We do this so that we\n    // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n    // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n    if (computedVertical) {\n        const scrollTop = element.scrollTop;\n        if (computedVertical === 1 /* AutoScrollVerticalDirection.UP */) {\n            if (scrollTop > 0) {\n                verticalScrollDirection = 1 /* AutoScrollVerticalDirection.UP */;\n            }\n        }\n        else if (element.scrollHeight - scrollTop > element.clientHeight) {\n            verticalScrollDirection = 2 /* AutoScrollVerticalDirection.DOWN */;\n        }\n    }\n    if (computedHorizontal) {\n        const scrollLeft = element.scrollLeft;\n        if (computedHorizontal === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n            if (scrollLeft > 0) {\n                horizontalScrollDirection = 1 /* AutoScrollHorizontalDirection.LEFT */;\n            }\n        }\n        else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n            horizontalScrollDirection = 2 /* AutoScrollHorizontalDirection.RIGHT */;\n        }\n    }\n    return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: false,\n    capture: true,\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n    constructor(_ngZone, _document) {\n        this._ngZone = _ngZone;\n        /** Registered drop container instances. */\n        this._dropInstances = new Set();\n        /** Registered drag item instances. */\n        this._dragInstances = new Set();\n        /** Drag item instances that are currently being dragged. */\n        this._activeDragInstances = [];\n        /** Keeps track of the event listeners that we've bound to the `document`. */\n        this._globalListeners = new Map();\n        /**\n         * Predicate function to check if an item is being dragged.  Moved out into a property,\n         * because it'll be called a lot and we don't want to create a new function every time.\n         */\n        this._draggingPredicate = (item) => item.isDragging();\n        /**\n         * Emits the `touchmove` or `mousemove` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerMove = new Subject();\n        /**\n         * Emits the `touchend` or `mouseup` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerUp = new Subject();\n        /**\n         * Emits when the viewport has been scrolled while the user is dragging an item.\n         * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n         * @breaking-change 13.0.0\n         */\n        this.scroll = new Subject();\n        /**\n         * Event listener that will prevent the default browser action while the user is dragging.\n         * @param event Event whose default action should be prevented.\n         */\n        this._preventDefaultWhileDragging = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                event.preventDefault();\n            }\n        };\n        /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n        this._persistentTouchmoveListener = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                // Note that we only want to prevent the default action after dragging has actually started.\n                // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n                // but it could be pushed back if the user has set up a drag delay or threshold.\n                if (this._activeDragInstances.some(this._draggingPredicate)) {\n                    event.preventDefault();\n                }\n                this.pointerMove.next(event);\n            }\n        };\n        this._document = _document;\n    }\n    /** Adds a drop container to the registry. */\n    registerDropContainer(drop) {\n        if (!this._dropInstances.has(drop)) {\n            this._dropInstances.add(drop);\n        }\n    }\n    /** Adds a drag item instance to the registry. */\n    registerDragItem(drag) {\n        this._dragInstances.add(drag);\n        // The `touchmove` event gets bound once, ahead of time, because WebKit\n        // won't preventDefault on a dynamically-added `touchmove` listener.\n        // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n        if (this._dragInstances.size === 1) {\n            this._ngZone.runOutsideAngular(() => {\n                // The event handler has to be explicitly active,\n                // because newer browsers make it passive by default.\n                this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n            });\n        }\n    }\n    /** Removes a drop container from the registry. */\n    removeDropContainer(drop) {\n        this._dropInstances.delete(drop);\n    }\n    /** Removes a drag item instance from the registry. */\n    removeDragItem(drag) {\n        this._dragInstances.delete(drag);\n        this.stopDragging(drag);\n        if (this._dragInstances.size === 0) {\n            this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n        }\n    }\n    /**\n     * Starts the dragging sequence for a drag instance.\n     * @param drag Drag instance which is being dragged.\n     * @param event Event that initiated the dragging.\n     */\n    startDragging(drag, event) {\n        // Do not process the same drag twice to avoid memory leaks and redundant listeners\n        if (this._activeDragInstances.indexOf(drag) > -1) {\n            return;\n        }\n        this._activeDragInstances.push(drag);\n        if (this._activeDragInstances.length === 1) {\n            const isTouchEvent = event.type.startsWith('touch');\n            // We explicitly bind __active__ listeners here, because newer browsers will default to\n            // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n            // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n            this._globalListeners\n                .set(isTouchEvent ? 'touchend' : 'mouseup', {\n                handler: (e) => this.pointerUp.next(e),\n                options: true,\n            })\n                .set('scroll', {\n                handler: (e) => this.scroll.next(e),\n                // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n                // the document. See https://github.com/angular/components/issues/17144.\n                options: true,\n            })\n                // Preventing the default action on `mousemove` isn't enough to disable text selection\n                // on Safari so we need to prevent the selection event as well. Alternatively this can\n                // be done by setting `user-select: none` on the `body`, however it has causes a style\n                // recalculation which can be expensive on pages with a lot of elements.\n                .set('selectstart', {\n                handler: this._preventDefaultWhileDragging,\n                options: activeCapturingEventOptions,\n            });\n            // We don't have to bind a move event for touch drag sequences, because\n            // we already have a persistent global one bound from `registerDragItem`.\n            if (!isTouchEvent) {\n                this._globalListeners.set('mousemove', {\n                    handler: (e) => this.pointerMove.next(e),\n                    options: activeCapturingEventOptions,\n                });\n            }\n            this._ngZone.runOutsideAngular(() => {\n                this._globalListeners.forEach((config, name) => {\n                    this._document.addEventListener(name, config.handler, config.options);\n                });\n            });\n        }\n    }\n    /** Stops dragging a drag item instance. */\n    stopDragging(drag) {\n        const index = this._activeDragInstances.indexOf(drag);\n        if (index > -1) {\n            this._activeDragInstances.splice(index, 1);\n            if (this._activeDragInstances.length === 0) {\n                this._clearGlobalListeners();\n            }\n        }\n    }\n    /** Gets whether a drag item instance is currently being dragged. */\n    isDragging(drag) {\n        return this._activeDragInstances.indexOf(drag) > -1;\n    }\n    /**\n     * Gets a stream that will emit when any element on the page is scrolled while an item is being\n     * dragged.\n     * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n     *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n     *   be used to include an additional top-level listener at the shadow root level.\n     */\n    scrolled(shadowRoot) {\n        const streams = [this.scroll];\n        if (shadowRoot && shadowRoot !== this._document) {\n            // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n            // because we want to guarantee that the event is bound outside of the `NgZone`. With\n            // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n            streams.push(new Observable((observer) => {\n                return this._ngZone.runOutsideAngular(() => {\n                    const eventOptions = true;\n                    const callback = (event) => {\n                        if (this._activeDragInstances.length) {\n                            observer.next(event);\n                        }\n                    };\n                    shadowRoot.addEventListener('scroll', callback, eventOptions);\n                    return () => {\n                        shadowRoot.removeEventListener('scroll', callback, eventOptions);\n                    };\n                });\n            }));\n        }\n        return merge(...streams);\n    }\n    ngOnDestroy() {\n        this._dragInstances.forEach(instance => this.removeDragItem(instance));\n        this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n        this._clearGlobalListeners();\n        this.pointerMove.complete();\n        this.pointerUp.complete();\n    }\n    /** Clears out the global event listeners from the `document`. */\n    _clearGlobalListeners() {\n        this._globalListeners.forEach((config, name) => {\n            this._document.removeEventListener(name, config.handler, config.options);\n        });\n        this._globalListeners.clear();\n    }\n}\nDragDropRegistry.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropRegistry, deps: [{ token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDragDropRegistry.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropRegistry, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n    dragStartThreshold: 5,\n    pointerDirectionChangeThreshold: 5,\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n    constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n    }\n    /**\n     * Turns an element into a draggable item.\n     * @param element Element to which to attach the dragging functionality.\n     * @param config Object used to configure the dragging behavior.\n     */\n    createDrag(element, config = DEFAULT_CONFIG) {\n        return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n    }\n    /**\n     * Turns an element into a drop list.\n     * @param element Element to which to attach the drop list functionality.\n     */\n    createDropList(element) {\n        return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n    }\n}\nDragDrop.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDrop, deps: [{ token: DOCUMENT }, { token: i0.NgZone }, { token: i1.ViewportRuler }, { token: DragDropRegistry }], target: i0.ɵɵFactoryTarget.Injectable });\nDragDrop.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDrop, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDrop, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i1.ViewportRuler }, { type: DragDropRegistry }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n    constructor() {\n        /** Drop lists registered inside the group. */\n        this._items = new Set();\n        this._disabled = false;\n    }\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    ngOnDestroy() {\n        this._items.clear();\n    }\n}\nCdkDropListGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDropListGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkDropListGroup.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDropListGroup, isStandalone: true, selector: \"[cdkDropListGroup]\", inputs: { disabled: [\"cdkDropListGroupDisabled\", \"disabled\"] }, providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }], exportAs: [\"cdkDropListGroup\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDropListGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropListGroup]',\n                    exportAs: 'cdkDropListGroup',\n                    standalone: true,\n                    providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }],\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input,\n                args: ['cdkDropListGroupDisabled']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n    if (node.nodeType !== 1) {\n        throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n    /** Whether starting a dragging sequence from this container is disabled. */\n    get disabled() {\n        return this._disabled || (!!this._group && this._group.disabled);\n    }\n    set disabled(value) {\n        // Usually we sync the directive and ref state right before dragging starts, in order to have\n        // a single point of failure and to avoid having to use setters for everything. `disabled` is\n        // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n        // the user in a disabled state, so we also need to sync it as it's being set.\n        this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(\n    /** Element that the drop list is attached to. */\n    element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n        this.element = element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._dir = _dir;\n        this._group = _group;\n        /** Emits when the list has been destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Other draggable containers that this container is connected to and into which the\n         * container's items can be transferred. Can either be references to other drop containers,\n         * or their unique IDs.\n         */\n        this.connectedTo = [];\n        /**\n         * Unique ID for the drop zone. Can be used as a reference\n         * in the `connectedTo` of another `CdkDropList`.\n         */\n        this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Functions that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new EventEmitter();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new EventEmitter();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new EventEmitter();\n        /**\n         * Keeps track of the items that are registered with this container. Historically we used to\n         * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n         * well which means that we can't handle cases like dragging the headers of a `mat-table`\n         * correctly. What we do instead is to have the items register themselves with the container\n         * and then we sort them based on their position in the DOM.\n         */\n        this._unsortedItems = new Set();\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDropList');\n        }\n        this._dropListRef = dragDrop.createDropList(element);\n        this._dropListRef.data = this;\n        if (config) {\n            this._assignDefaults(config);\n        }\n        this._dropListRef.enterPredicate = (drag, drop) => {\n            return this.enterPredicate(drag.data, drop.data);\n        };\n        this._dropListRef.sortPredicate = (index, drag, drop) => {\n            return this.sortPredicate(index, drag.data, drop.data);\n        };\n        this._setupInputSyncSubscription(this._dropListRef);\n        this._handleEvents(this._dropListRef);\n        CdkDropList._dropLists.push(this);\n        if (_group) {\n            _group._items.add(this);\n        }\n    }\n    /** Registers an items with the drop list. */\n    addItem(item) {\n        this._unsortedItems.add(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Removes an item from the drop list. */\n    removeItem(item) {\n        this._unsortedItems.delete(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Gets the registered items in the list, sorted by their position in the DOM. */\n    getSortedItems() {\n        return Array.from(this._unsortedItems).sort((a, b) => {\n            const documentPosition = a._dragRef\n                .getVisibleElement()\n                .compareDocumentPosition(b._dragRef.getVisibleElement());\n            // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n            // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n            // tslint:disable-next-line:no-bitwise\n            return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        });\n    }\n    ngOnDestroy() {\n        const index = CdkDropList._dropLists.indexOf(this);\n        if (index > -1) {\n            CdkDropList._dropLists.splice(index, 1);\n        }\n        if (this._group) {\n            this._group._items.delete(this);\n        }\n        this._unsortedItems.clear();\n        this._dropListRef.dispose();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n    _setupInputSyncSubscription(ref) {\n        if (this._dir) {\n            this._dir.change\n                .pipe(startWith(this._dir.value), takeUntil(this._destroyed))\n                .subscribe(value => ref.withDirection(value));\n        }\n        ref.beforeStarted.subscribe(() => {\n            const siblings = coerceArray(this.connectedTo).map(drop => {\n                if (typeof drop === 'string') {\n                    const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n                    if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                        console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n                    }\n                    return correspondingDropList;\n                }\n                return drop;\n            });\n            if (this._group) {\n                this._group._items.forEach(drop => {\n                    if (siblings.indexOf(drop) === -1) {\n                        siblings.push(drop);\n                    }\n                });\n            }\n            // Note that we resolve the scrollable parents here so that we delay the resolution\n            // as long as possible, ensuring that the element is in its final place in the DOM.\n            if (!this._scrollableParentsResolved) {\n                const scrollableParents = this._scrollDispatcher\n                    .getAncestorScrollContainers(this.element)\n                    .map(scrollable => scrollable.getElementRef().nativeElement);\n                this._dropListRef.withScrollableParents(scrollableParents);\n                // Only do this once since it involves traversing the DOM and the parents\n                // shouldn't be able to change without the drop list being destroyed.\n                this._scrollableParentsResolved = true;\n            }\n            ref.disabled = this.disabled;\n            ref.lockAxis = this.lockAxis;\n            ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n            ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n            ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n            ref\n                .connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef))\n                .withOrientation(this.orientation);\n        });\n    }\n    /** Handles events from the underlying DropListRef. */\n    _handleEvents(ref) {\n        ref.beforeStarted.subscribe(() => {\n            this._syncItemsWithRef();\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(event => {\n            this.entered.emit({\n                container: this,\n                item: event.item.data,\n                currentIndex: event.currentIndex,\n            });\n        });\n        ref.exited.subscribe(event => {\n            this.exited.emit({\n                container: this,\n                item: event.item.data,\n            });\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.sorted.subscribe(event => {\n            this.sorted.emit({\n                previousIndex: event.previousIndex,\n                currentIndex: event.currentIndex,\n                container: this,\n                item: event.item.data,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                item: dropEvent.item.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n            // Mark for check since all of these events run outside of change\n            // detection and we're not guaranteed for something else to have triggered it.\n            this._changeDetectorRef.markForCheck();\n        });\n        merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, draggingDisabled, sortingDisabled, listAutoScrollDisabled, listOrientation } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n        this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n        this.orientation = listOrientation || 'vertical';\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n    }\n    /** Syncs up the registered drag items with underlying drop list ref. */\n    _syncItemsWithRef() {\n        this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n    }\n}\n/** Keeps track of the drop lists that are currently on the page. */\nCdkDropList._dropLists = [];\nCdkDropList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDropList, deps: [{ token: i0.ElementRef }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: i1.ScrollDispatcher }, { token: i3.Directionality, optional: true }, { token: CDK_DROP_LIST_GROUP, optional: true, skipSelf: true }, { token: CDK_DRAG_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDropList.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDropList, isStandalone: true, selector: \"[cdkDropList], cdk-drop-list\", inputs: { connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"], data: [\"cdkDropListData\", \"data\"], orientation: [\"cdkDropListOrientation\", \"orientation\"], id: \"id\", lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"], disabled: [\"cdkDropListDisabled\", \"disabled\"], sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"], enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"], sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"], autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"], autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"] }, outputs: { dropped: \"cdkDropListDropped\", entered: \"cdkDropListEntered\", exited: \"cdkDropListExited\", sorted: \"cdkDropListSorted\" }, host: { properties: { \"attr.id\": \"id\", \"class.cdk-drop-list-disabled\": \"disabled\", \"class.cdk-drop-list-dragging\": \"_dropListRef.isDragging()\", \"class.cdk-drop-list-receiving\": \"_dropListRef.isReceiving()\" }, classAttribute: \"cdk-drop-list\" }, providers: [\n        // Prevent child drop lists from picking up the same group as their parent.\n        { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n        { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n    ], exportAs: [\"cdkDropList\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDropList, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropList], cdk-drop-list',\n                    exportAs: 'cdkDropList',\n                    standalone: true,\n                    providers: [\n                        // Prevent child drop lists from picking up the same group as their parent.\n                        { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n                        { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n                    ],\n                    host: {\n                        'class': 'cdk-drop-list',\n                        '[attr.id]': 'id',\n                        '[class.cdk-drop-list-disabled]': 'disabled',\n                        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n                        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: i1.ScrollDispatcher }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: CdkDropListGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DROP_LIST_GROUP]\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_CONFIG]\n                }] }]; }, propDecorators: { connectedTo: [{\n                type: Input,\n                args: ['cdkDropListConnectedTo']\n            }], data: [{\n                type: Input,\n                args: ['cdkDropListData']\n            }], orientation: [{\n                type: Input,\n                args: ['cdkDropListOrientation']\n            }], id: [{\n                type: Input\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDropListLockAxis']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkDropListDisabled']\n            }], sortingDisabled: [{\n                type: Input,\n                args: ['cdkDropListSortingDisabled']\n            }], enterPredicate: [{\n                type: Input,\n                args: ['cdkDropListEnterPredicate']\n            }], sortPredicate: [{\n                type: Input,\n                args: ['cdkDropListSortPredicate']\n            }], autoScrollDisabled: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollDisabled']\n            }], autoScrollStep: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollStep']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDropListDropped']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDropListEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDropListExited']\n            }], sorted: [{\n                type: Output,\n                args: ['cdkDropListSorted']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n    /** Whether starting to drag through this handle is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._stateChanges.next(this);\n    }\n    constructor(element, parentDrag) {\n        this.element = element;\n        /** Emits when the state of the handle has changed. */\n        this._stateChanges = new Subject();\n        this._disabled = false;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDragHandle');\n        }\n        this._parentDrag = parentDrag;\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n}\nCdkDragHandle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDragHandle, deps: [{ token: i0.ElementRef }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDragHandle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDragHandle, isStandalone: true, selector: \"[cdkDragHandle]\", inputs: { disabled: [\"cdkDragHandleDisabled\", \"disabled\"] }, host: { classAttribute: \"cdk-drag-handle\" }, providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDragHandle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDragHandle]',\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-drag-handle',\n                    },\n                    providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_DRAG_PARENT]\n                }, {\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }]; }, propDecorators: { disabled: [{\n                type: Input,\n                args: ['cdkDragHandleDisabled']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n    }\n}\nCdkDragPlaceholder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDragPlaceholder, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDragPlaceholder.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDragPlaceholder, isStandalone: true, selector: \"ng-template[cdkDragPlaceholder]\", inputs: { data: \"data\" }, providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDragPlaceholder, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPlaceholder]',\n                    standalone: true,\n                    providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { data: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    get matchSize() {\n        return this._matchSize;\n    }\n    set matchSize(value) {\n        this._matchSize = coerceBooleanProperty(value);\n    }\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._matchSize = false;\n    }\n}\nCdkDragPreview.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDragPreview, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDragPreview.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDragPreview, isStandalone: true, selector: \"ng-template[cdkDragPreview]\", inputs: { data: \"data\", matchSize: \"matchSize\" }, providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDragPreview, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPreview]',\n                    standalone: true,\n                    providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { data: [{\n                type: Input\n            }], matchSize: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || (this.dropContainer && this.dropContainer.disabled);\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._dragRef.disabled = this._disabled;\n    }\n    constructor(\n    /** Element that the draggable is attached to. */\n    element, \n    /** Droppable container that the draggable is a part of. */\n    dropContainer, \n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n        this.element = element;\n        this.dropContainer = dropContainer;\n        this._ngZone = _ngZone;\n        this._viewContainerRef = _viewContainerRef;\n        this._dir = _dir;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._selfHandle = _selfHandle;\n        this._parentDrag = _parentDrag;\n        this._destroyed = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new EventEmitter();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new EventEmitter();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new EventEmitter();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new EventEmitter();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new EventEmitter();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = new Observable((observer) => {\n            const subscription = this._dragRef.moved\n                .pipe(map(movedEvent => ({\n                source: this,\n                pointerPosition: movedEvent.pointerPosition,\n                event: movedEvent.event,\n                delta: movedEvent.delta,\n                distance: movedEvent.distance,\n            })))\n                .subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n            };\n        });\n        this._dragRef = dragDrop.createDrag(element, {\n            dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n            pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null\n                ? config.pointerDirectionChangeThreshold\n                : 5,\n            zIndex: config?.zIndex,\n        });\n        this._dragRef.data = this;\n        // We have to keep track of the drag instances in order to be able to match an element to\n        // a drag instance. We can't go through the global registry of `DragRef`, because the root\n        // element could be different.\n        CdkDrag._dragInstances.push(this);\n        if (config) {\n            this._assignDefaults(config);\n        }\n        // Note that usually the container is assigned when the drop list is picks up the item, but in\n        // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n        // where there are no items on the first change detection pass, but the items get picked up as\n        // soon as the user triggers another pass by dragging. This is a problem, because the item would\n        // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n        // is too late since the two modes save different kinds of information. We work around it by\n        // assigning the drop container both from here and the list.\n        if (dropContainer) {\n            this._dragRef._withDropContainer(dropContainer._dropListRef);\n            dropContainer.addItem(this);\n        }\n        this._syncInputs(this._dragRef);\n        this._handleEvents(this._dragRef);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._dragRef.getPlaceholderElement();\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._dragRef.getRootElement();\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._dragRef.reset();\n    }\n    /**\n     * Gets the pixel coordinates of the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        return this._dragRef.getFreeDragPosition();\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._dragRef.setFreeDragPosition(value);\n    }\n    ngAfterViewInit() {\n        // Normally this isn't in the zone, but it can cause major performance regressions for apps\n        // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n        this._ngZone.runOutsideAngular(() => {\n            // We need to wait for the zone to stabilize, in order for the reference\n            // element to be in the proper place in the DOM. This is mostly relevant\n            // for draggable elements inside portals since they get stamped out in\n            // their original DOM position and then they get transferred to the portal.\n            this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                this._updateRootElement();\n                this._setupHandlesListener();\n                if (this.freeDragPosition) {\n                    this._dragRef.setFreeDragPosition(this.freeDragPosition);\n                }\n            });\n        });\n    }\n    ngOnChanges(changes) {\n        const rootSelectorChange = changes['rootElementSelector'];\n        const positionChange = changes['freeDragPosition'];\n        // We don't have to react to the first change since it's being\n        // handled in `ngAfterViewInit` where it needs to be deferred.\n        if (rootSelectorChange && !rootSelectorChange.firstChange) {\n            this._updateRootElement();\n        }\n        // Skip the first change since it's being handled in `ngAfterViewInit`.\n        if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n            this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n    }\n    ngOnDestroy() {\n        if (this.dropContainer) {\n            this.dropContainer.removeItem(this);\n        }\n        const index = CdkDrag._dragInstances.indexOf(this);\n        if (index > -1) {\n            CdkDrag._dragInstances.splice(index, 1);\n        }\n        // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n        this._ngZone.runOutsideAngular(() => {\n            this._destroyed.next();\n            this._destroyed.complete();\n            this._dragRef.dispose();\n        });\n    }\n    /** Syncs the root element with the `DragRef`. */\n    _updateRootElement() {\n        const element = this.element.nativeElement;\n        let rootElement = element;\n        if (this.rootElementSelector) {\n            rootElement =\n                element.closest !== undefined\n                    ? element.closest(this.rootElementSelector)\n                    : // Comment tag doesn't have closest method, so use parent's one.\n                        element.parentElement?.closest(this.rootElementSelector);\n        }\n        if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            assertElementNode(rootElement, 'cdkDrag');\n        }\n        this._dragRef.withRootElement(rootElement || element);\n    }\n    /** Gets the boundary element, based on the `boundaryElement` value. */\n    _getBoundaryElement() {\n        const boundary = this.boundaryElement;\n        if (!boundary) {\n            return null;\n        }\n        if (typeof boundary === 'string') {\n            return this.element.nativeElement.closest(boundary);\n        }\n        return coerceElement(boundary);\n    }\n    /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n    _syncInputs(ref) {\n        ref.beforeStarted.subscribe(() => {\n            if (!ref.isDragging()) {\n                const dir = this._dir;\n                const dragStartDelay = this.dragStartDelay;\n                const placeholder = this._placeholderTemplate\n                    ? {\n                        template: this._placeholderTemplate.templateRef,\n                        context: this._placeholderTemplate.data,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                const preview = this._previewTemplate\n                    ? {\n                        template: this._previewTemplate.templateRef,\n                        context: this._previewTemplate.data,\n                        matchSize: this._previewTemplate.matchSize,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                ref.disabled = this.disabled;\n                ref.lockAxis = this.lockAxis;\n                ref.dragStartDelay =\n                    typeof dragStartDelay === 'object' && dragStartDelay\n                        ? dragStartDelay\n                        : coerceNumberProperty(dragStartDelay);\n                ref.constrainPosition = this.constrainPosition;\n                ref.previewClass = this.previewClass;\n                ref\n                    .withBoundaryElement(this._getBoundaryElement())\n                    .withPlaceholderTemplate(placeholder)\n                    .withPreviewTemplate(preview)\n                    .withPreviewContainer(this.previewContainer || 'global');\n                if (dir) {\n                    ref.withDirection(dir.value);\n                }\n            }\n        });\n        // This only needs to be resolved once.\n        ref.beforeStarted.pipe(take(1)).subscribe(() => {\n            // If we managed to resolve a parent through DI, use it.\n            if (this._parentDrag) {\n                ref.withParent(this._parentDrag._dragRef);\n                return;\n            }\n            // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n            // the item was projected into another item by something like `ngTemplateOutlet`.\n            let parent = this.element.nativeElement.parentElement;\n            while (parent) {\n                if (parent.classList.contains(DRAG_HOST_CLASS)) {\n                    ref.withParent(CdkDrag._dragInstances.find(drag => {\n                        return drag.element.nativeElement === parent;\n                    })?._dragRef || null);\n                    break;\n                }\n                parent = parent.parentElement;\n            }\n        });\n    }\n    /** Handles the events from the underlying `DragRef`. */\n    _handleEvents(ref) {\n        ref.started.subscribe(startEvent => {\n            this.started.emit({ source: this, event: startEvent.event });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.released.subscribe(releaseEvent => {\n            this.released.emit({ source: this, event: releaseEvent.event });\n        });\n        ref.ended.subscribe(endEvent => {\n            this.ended.emit({\n                source: this,\n                distance: endEvent.distance,\n                dropPoint: endEvent.dropPoint,\n                event: endEvent.event,\n            });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(enterEvent => {\n            this.entered.emit({\n                container: enterEvent.container.data,\n                item: this,\n                currentIndex: enterEvent.currentIndex,\n            });\n        });\n        ref.exited.subscribe(exitEvent => {\n            this.exited.emit({\n                container: exitEvent.container.data,\n                item: this,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                item: this,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n        });\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, dragStartDelay, constrainPosition, previewClass, boundaryElement, draggingDisabled, rootElementSelector, previewContainer, } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.dragStartDelay = dragStartDelay || 0;\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n        if (constrainPosition) {\n            this.constrainPosition = constrainPosition;\n        }\n        if (previewClass) {\n            this.previewClass = previewClass;\n        }\n        if (boundaryElement) {\n            this.boundaryElement = boundaryElement;\n        }\n        if (rootElementSelector) {\n            this.rootElementSelector = rootElementSelector;\n        }\n        if (previewContainer) {\n            this.previewContainer = previewContainer;\n        }\n    }\n    /** Sets up the listener that syncs the handles with the drag ref. */\n    _setupHandlesListener() {\n        // Listen for any newly-added handles.\n        this._handles.changes\n            .pipe(startWith(this._handles), \n        // Sync the new handles with the DragRef.\n        tap((handles) => {\n            const childHandleElements = handles\n                .filter(handle => handle._parentDrag === this)\n                .map(handle => handle.element);\n            // Usually handles are only allowed to be a descendant of the drag element, but if\n            // the consumer defined a different drag root, we should allow the drag element\n            // itself to be a handle too.\n            if (this._selfHandle && this.rootElementSelector) {\n                childHandleElements.push(this.element);\n            }\n            this._dragRef.withHandles(childHandleElements);\n        }), \n        // Listen if the state of any of the handles changes.\n        switchMap((handles) => {\n            return merge(...handles.map(item => {\n                return item._stateChanges.pipe(startWith(item));\n            }));\n        }), takeUntil(this._destroyed))\n            .subscribe(handleInstance => {\n            // Enabled/disable the handle that changed in the DragRef.\n            const dragRef = this._dragRef;\n            const handle = handleInstance.element.nativeElement;\n            handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n        });\n    }\n}\nCdkDrag._dragInstances = [];\nCdkDrag.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDrag, deps: [{ token: i0.ElementRef }, { token: CDK_DROP_LIST, optional: true, skipSelf: true }, { token: DOCUMENT }, { token: i0.NgZone }, { token: i0.ViewContainerRef }, { token: CDK_DRAG_CONFIG, optional: true }, { token: i3.Directionality, optional: true }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: CDK_DRAG_HANDLE, optional: true, self: true }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDrag.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", type: CdkDrag, isStandalone: true, selector: \"[cdkDrag]\", inputs: { data: [\"cdkDragData\", \"data\"], lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"], rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"], boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"], dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"], freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"], disabled: [\"cdkDragDisabled\", \"disabled\"], constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"], previewClass: [\"cdkDragPreviewClass\", \"previewClass\"], previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"] }, outputs: { started: \"cdkDragStarted\", released: \"cdkDragReleased\", ended: \"cdkDragEnded\", entered: \"cdkDragEntered\", exited: \"cdkDragExited\", dropped: \"cdkDragDropped\", moved: \"cdkDragMoved\" }, host: { properties: { \"class.cdk-drag-disabled\": \"disabled\", \"class.cdk-drag-dragging\": \"_dragRef.isDragging()\" }, classAttribute: \"cdk-drag\" }, providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }], queries: [{ propertyName: \"_previewTemplate\", first: true, predicate: CDK_DRAG_PREVIEW, descendants: true }, { propertyName: \"_placeholderTemplate\", first: true, predicate: CDK_DRAG_PLACEHOLDER, descendants: true }, { propertyName: \"_handles\", predicate: CDK_DRAG_HANDLE, descendants: true }], exportAs: [\"cdkDrag\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: CdkDrag, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDrag]',\n                    exportAs: 'cdkDrag',\n                    standalone: true,\n                    host: {\n                        'class': DRAG_HOST_CLASS,\n                        '[class.cdk-drag-disabled]': 'disabled',\n                        '[class.cdk-drag-dragging]': '_dragRef.isDragging()',\n                    },\n                    providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_DROP_LIST]\n                }, {\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_CONFIG]\n                }] }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: CdkDragHandle, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_HANDLE]\n                }] }, { type: CdkDrag, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_PARENT]\n                }] }]; }, propDecorators: { _handles: [{\n                type: ContentChildren,\n                args: [CDK_DRAG_HANDLE, { descendants: true }]\n            }], _previewTemplate: [{\n                type: ContentChild,\n                args: [CDK_DRAG_PREVIEW]\n            }], _placeholderTemplate: [{\n                type: ContentChild,\n                args: [CDK_DRAG_PLACEHOLDER]\n            }], data: [{\n                type: Input,\n                args: ['cdkDragData']\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDragLockAxis']\n            }], rootElementSelector: [{\n                type: Input,\n                args: ['cdkDragRootElement']\n            }], boundaryElement: [{\n                type: Input,\n                args: ['cdkDragBoundary']\n            }], dragStartDelay: [{\n                type: Input,\n                args: ['cdkDragStartDelay']\n            }], freeDragPosition: [{\n                type: Input,\n                args: ['cdkDragFreeDragPosition']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkDragDisabled']\n            }], constrainPosition: [{\n                type: Input,\n                args: ['cdkDragConstrainPosition']\n            }], previewClass: [{\n                type: Input,\n                args: ['cdkDragPreviewClass']\n            }], previewContainer: [{\n                type: Input,\n                args: ['cdkDragPreviewContainer']\n            }], started: [{\n                type: Output,\n                args: ['cdkDragStarted']\n            }], released: [{\n                type: Output,\n                args: ['cdkDragReleased']\n            }], ended: [{\n                type: Output,\n                args: ['cdkDragEnded']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDragEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDragExited']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDragDropped']\n            }], moved: [{\n                type: Output,\n                args: ['cdkDragMoved']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst DRAG_DROP_DIRECTIVES = [\n    CdkDropList,\n    CdkDropListGroup,\n    CdkDrag,\n    CdkDragHandle,\n    CdkDragPreview,\n    CdkDragPlaceholder,\n];\nclass DragDropModule {\n}\nDragDropModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nDragDropModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropModule, imports: [CdkDropList,\n        CdkDropListGroup,\n        CdkDrag,\n        CdkDragHandle,\n        CdkDragPreview,\n        CdkDragPlaceholder], exports: [CdkScrollableModule, CdkDropList,\n        CdkDropListGroup,\n        CdkDrag,\n        CdkDragHandle,\n        CdkDragPreview,\n        CdkDragPlaceholder] });\nDragDropModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropModule, providers: [DragDrop], imports: [CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.0-rc.0\", ngImport: i0, type: DragDropModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: DRAG_DROP_DIRECTIVES,\n                    exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n                    providers: [DragDrop],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,EAAEC,+BAA+B,EAAEC,cAAc,QAAQ,uBAAuB;AACxG,SAASC,qBAAqB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,oBAAoB,QAAQ,uBAAuB;AAC/G,SAASC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AACrG,SAASC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAClG,SAASC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAChF,OAAO,KAAKC,EAAE,MAAM,mBAAmB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAEC,mBAAmB,EAAE;EACrD,KAAK,IAAIC,GAAG,IAAIF,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACG,cAAc,CAACD,GAAG,CAAC,EAAE;MAC5B,MAAME,KAAK,GAAGJ,MAAM,CAACE,GAAG,CAAC;MACzB,IAAIE,KAAK,EAAE;QACPL,IAAI,CAACM,WAAW,CAACH,GAAG,EAAEE,KAAK,EAAEH,mBAAmB,EAAEK,GAAG,CAACJ,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;MAClF,CAAC,MACI;QACDH,IAAI,CAACQ,cAAc,CAACL,GAAG,CAAC;MAC5B;IACJ;EACJ;EACA,OAAOH,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,4BAA4BA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACnD,MAAMC,UAAU,GAAGD,MAAM,GAAG,EAAE,GAAG,MAAM;EACvCZ,YAAY,CAACW,OAAO,CAACG,KAAK,EAAE;IACxB,cAAc,EAAEF,MAAM,GAAG,EAAE,GAAG,MAAM;IACpC,mBAAmB,EAAEA,MAAM,GAAG,EAAE,GAAG,MAAM;IACzC,6BAA6B,EAAEA,MAAM,GAAG,EAAE,GAAG,aAAa;IAC1D,aAAa,EAAEC,UAAU;IACzB,iBAAiB,EAAEA,UAAU;IAC7B,qBAAqB,EAAEA,UAAU;IACjC,kBAAkB,EAAEA;EACxB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACJ,OAAO,EAAEC,MAAM,EAAET,mBAAmB,EAAE;EAC5DH,YAAY,CAACW,OAAO,CAACG,KAAK,EAAE;IACxBE,QAAQ,EAAEJ,MAAM,GAAG,EAAE,GAAG,OAAO;IAC/BK,GAAG,EAAEL,MAAM,GAAG,EAAE,GAAG,GAAG;IACtBM,OAAO,EAAEN,MAAM,GAAG,EAAE,GAAG,GAAG;IAC1BO,IAAI,EAAEP,MAAM,GAAG,EAAE,GAAG;EACxB,CAAC,EAAET,mBAAmB,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,SAASiB,iBAAiBA,CAACC,SAAS,EAAEC,gBAAgB,EAAE;EACpD,OAAOA,gBAAgB,IAAIA,gBAAgB,IAAI,MAAM,GAC/CD,SAAS,GAAG,GAAG,GAAGC,gBAAgB,GAClCD,SAAS;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,qBAAqBA,CAACjB,KAAK,EAAE;EAClC;EACA,MAAMkB,UAAU,GAAGlB,KAAK,CAACmB,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;EACpE,OAAOC,UAAU,CAACrB,KAAK,CAAC,GAAGkB,UAAU;AACzC;AACA;AACA,SAASI,kCAAkCA,CAACjB,OAAO,EAAE;EACjD,MAAMkB,aAAa,GAAGC,gBAAgB,CAACnB,OAAO,CAAC;EAC/C,MAAMoB,sBAAsB,GAAGC,qBAAqB,CAACH,aAAa,EAAE,qBAAqB,CAAC;EAC1F,MAAMI,QAAQ,GAAGF,sBAAsB,CAACG,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,KAAK,CAAC;EAC5F;EACA,IAAI,CAACF,QAAQ,EAAE;IACX,OAAO,CAAC;EACZ;EACA;EACA;EACA,MAAMG,aAAa,GAAGL,sBAAsB,CAACL,OAAO,CAACO,QAAQ,CAAC;EAC9D,MAAMI,YAAY,GAAGL,qBAAqB,CAACH,aAAa,EAAE,qBAAqB,CAAC;EAChF,MAAMS,SAAS,GAAGN,qBAAqB,CAACH,aAAa,EAAE,kBAAkB,CAAC;EAC1E,OAAQN,qBAAqB,CAACc,YAAY,CAACD,aAAa,CAAC,CAAC,GACtDb,qBAAqB,CAACe,SAAS,CAACF,aAAa,CAAC,CAAC;AACvD;AACA;AACA,SAASJ,qBAAqBA,CAACH,aAAa,EAAEU,IAAI,EAAE;EAChD,MAAMjC,KAAK,GAAGuB,aAAa,CAACW,gBAAgB,CAACD,IAAI,CAAC;EAClD,OAAOjC,KAAK,CAACmC,KAAK,CAAC,GAAG,CAAC,CAAC9C,GAAG,CAAC+C,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACjC,OAAO,EAAE;EACnC,MAAMkC,UAAU,GAAGlC,OAAO,CAACmC,qBAAqB,CAAC,CAAC;EAClD;EACA;EACA;EACA;EACA,OAAO;IACH7B,GAAG,EAAE4B,UAAU,CAAC5B,GAAG;IACnB8B,KAAK,EAAEF,UAAU,CAACE,KAAK;IACvBC,MAAM,EAAEH,UAAU,CAACG,MAAM;IACzB7B,IAAI,EAAE0B,UAAU,CAAC1B,IAAI;IACrB8B,KAAK,EAAEJ,UAAU,CAACI,KAAK;IACvBC,MAAM,EAAEL,UAAU,CAACK,MAAM;IACzBC,CAAC,EAAEN,UAAU,CAACM,CAAC;IACfC,CAAC,EAAEP,UAAU,CAACO;EAClB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACR,UAAU,EAAEM,CAAC,EAAEC,CAAC,EAAE;EAC1C,MAAM;IAAEnC,GAAG;IAAE+B,MAAM;IAAE7B,IAAI;IAAE4B;EAAM,CAAC,GAAGF,UAAU;EAC/C,OAAOO,CAAC,IAAInC,GAAG,IAAImC,CAAC,IAAIJ,MAAM,IAAIG,CAAC,IAAIhC,IAAI,IAAIgC,CAAC,IAAIJ,KAAK;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,gBAAgBA,CAACT,UAAU,EAAE5B,GAAG,EAAEE,IAAI,EAAE;EAC7C0B,UAAU,CAAC5B,GAAG,IAAIA,GAAG;EACrB4B,UAAU,CAACG,MAAM,GAAGH,UAAU,CAAC5B,GAAG,GAAG4B,UAAU,CAACK,MAAM;EACtDL,UAAU,CAAC1B,IAAI,IAAIA,IAAI;EACvB0B,UAAU,CAACE,KAAK,GAAGF,UAAU,CAAC1B,IAAI,GAAG0B,UAAU,CAACI,KAAK;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,uBAAuBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAClE,MAAM;IAAE1C,GAAG;IAAE8B,KAAK;IAAEC,MAAM;IAAE7B,IAAI;IAAE8B,KAAK;IAAEC;EAAO,CAAC,GAAGM,IAAI;EACxD,MAAMI,UAAU,GAAGX,KAAK,GAAGQ,SAAS;EACpC,MAAMI,UAAU,GAAGX,MAAM,GAAGO,SAAS;EACrC,OAAQE,QAAQ,GAAG1C,GAAG,GAAG4C,UAAU,IAC/BF,QAAQ,GAAGX,MAAM,GAAGa,UAAU,IAC9BH,QAAQ,GAAGvC,IAAI,GAAGyC,UAAU,IAC5BF,QAAQ,GAAGX,KAAK,GAAGa,UAAU;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACF,SAAS,CAACE,KAAK,CAAC,CAAC;EAC1B;EACA;EACAC,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAACF,KAAK,CAAC,CAAC;IACZ,IAAI,CAACF,SAAS,CAACK,GAAG,CAAC,IAAI,CAACN,SAAS,EAAE;MAC/BO,cAAc,EAAE,IAAI,CAACC,yBAAyB,CAAC;IACnD,CAAC,CAAC;IACFH,QAAQ,CAACI,OAAO,CAAC9D,OAAO,IAAI;MACxB,IAAI,CAACsD,SAAS,CAACK,GAAG,CAAC3D,OAAO,EAAE;QACxB4D,cAAc,EAAE;UAAEtD,GAAG,EAAEN,OAAO,CAAC+D,SAAS;UAAEvD,IAAI,EAAER,OAAO,CAACgE;QAAW,CAAC;QACpE9B,UAAU,EAAED,oBAAoB,CAACjC,OAAO;MAC5C,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAiE,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMC,MAAM,GAAGpG,eAAe,CAACmG,KAAK,CAAC;IACrC,MAAME,cAAc,GAAG,IAAI,CAACd,SAAS,CAACe,GAAG,CAACF,MAAM,CAAC;IACjD,IAAI,CAACC,cAAc,EAAE;MACjB,OAAO,IAAI;IACf;IACA,MAAMR,cAAc,GAAGQ,cAAc,CAACR,cAAc;IACpD,IAAIU,MAAM;IACV,IAAIC,OAAO;IACX,IAAIJ,MAAM,KAAK,IAAI,CAACd,SAAS,EAAE;MAC3B,MAAMmB,sBAAsB,GAAG,IAAI,CAACX,yBAAyB,CAAC,CAAC;MAC/DS,MAAM,GAAGE,sBAAsB,CAAClE,GAAG;MACnCiE,OAAO,GAAGC,sBAAsB,CAAChE,IAAI;IACzC,CAAC,MACI;MACD8D,MAAM,GAAGH,MAAM,CAACJ,SAAS;MACzBQ,OAAO,GAAGJ,MAAM,CAACH,UAAU;IAC/B;IACA,MAAMS,aAAa,GAAGb,cAAc,CAACtD,GAAG,GAAGgE,MAAM;IACjD,MAAMI,cAAc,GAAGd,cAAc,CAACpD,IAAI,GAAG+D,OAAO;IACpD;IACA;IACA,IAAI,CAACjB,SAAS,CAACQ,OAAO,CAAC,CAACzD,QAAQ,EAAEsE,IAAI,KAAK;MACvC,IAAItE,QAAQ,CAAC6B,UAAU,IAAIiC,MAAM,KAAKQ,IAAI,IAAIR,MAAM,CAACS,QAAQ,CAACD,IAAI,CAAC,EAAE;QACjEhC,gBAAgB,CAACtC,QAAQ,CAAC6B,UAAU,EAAEuC,aAAa,EAAEC,cAAc,CAAC;MACxE;IACJ,CAAC,CAAC;IACFd,cAAc,CAACtD,GAAG,GAAGgE,MAAM;IAC3BV,cAAc,CAACpD,IAAI,GAAG+D,OAAO;IAC7B,OAAO;MAAEjE,GAAG,EAAEmE,aAAa;MAAEjE,IAAI,EAAEkE;IAAe,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIb,yBAAyBA,CAAA,EAAG;IACxB,OAAO;MAAEvD,GAAG,EAAEuE,MAAM,CAACC,OAAO;MAAEtE,IAAI,EAAEqE,MAAM,CAACE;IAAQ,CAAC;EACxD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACL,IAAI,EAAE;EACzB,MAAMM,KAAK,GAAGN,IAAI,CAACO,SAAS,CAAC,IAAI,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,KAAK,CAACG,gBAAgB,CAAC,MAAM,CAAC;EACxD,MAAMC,QAAQ,GAAGV,IAAI,CAACU,QAAQ,CAACvE,WAAW,CAAC,CAAC;EAC5C;EACAmE,KAAK,CAACK,eAAe,CAAC,IAAI,CAAC;EAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,iBAAiB,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/CJ,iBAAiB,CAACI,CAAC,CAAC,CAACD,eAAe,CAAC,IAAI,CAAC;EAC9C;EACA,IAAID,QAAQ,KAAK,QAAQ,EAAE;IACvBI,kBAAkB,CAACd,IAAI,EAAEM,KAAK,CAAC;EACnC,CAAC,MACI,IAAII,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,EAAE;IAC/EK,iBAAiB,CAACf,IAAI,EAAEM,KAAK,CAAC;EAClC;EACAU,YAAY,CAAC,QAAQ,EAAEhB,IAAI,EAAEM,KAAK,EAAEQ,kBAAkB,CAAC;EACvDE,YAAY,CAAC,yBAAyB,EAAEhB,IAAI,EAAEM,KAAK,EAAES,iBAAiB,CAAC;EACvE,OAAOT,KAAK;AAChB;AACA;AACA,SAASU,YAAYA,CAACC,QAAQ,EAAEjB,IAAI,EAAEM,KAAK,EAAEY,QAAQ,EAAE;EACnD,MAAMC,kBAAkB,GAAGnB,IAAI,CAACS,gBAAgB,CAACQ,QAAQ,CAAC;EAC1D,IAAIE,kBAAkB,CAACN,MAAM,EAAE;IAC3B,MAAMO,aAAa,GAAGd,KAAK,CAACG,gBAAgB,CAACQ,QAAQ,CAAC;IACtD,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,kBAAkB,CAACN,MAAM,EAAED,CAAC,EAAE,EAAE;MAChDM,QAAQ,CAACC,kBAAkB,CAACP,CAAC,CAAC,EAAEQ,aAAa,CAACR,CAAC,CAAC,CAAC;IACrD;EACJ;AACJ;AACA;AACA,IAAIS,aAAa,GAAG,CAAC;AACrB;AACA,SAASN,iBAAiBA,CAACnG,MAAM,EAAE0F,KAAK,EAAE;EACtC;EACA,IAAIA,KAAK,CAACgB,IAAI,KAAK,MAAM,EAAE;IACvBhB,KAAK,CAACtF,KAAK,GAAGJ,MAAM,CAACI,KAAK;EAC9B;EACA;EACA;EACA;EACA,IAAIsF,KAAK,CAACgB,IAAI,KAAK,OAAO,IAAIhB,KAAK,CAACrD,IAAI,EAAE;IACtCqD,KAAK,CAACrD,IAAI,GAAI,aAAYqD,KAAK,CAACrD,IAAK,IAAGoE,aAAa,EAAG,EAAC;EAC7D;AACJ;AACA;AACA,SAASP,kBAAkBA,CAAClG,MAAM,EAAE0F,KAAK,EAAE;EACvC,MAAMiB,OAAO,GAAGjB,KAAK,CAACkB,UAAU,CAAC,IAAI,CAAC;EACtC,IAAID,OAAO,EAAE;IACT;IACA;IACA,IAAI;MACAA,OAAO,CAACE,SAAS,CAAC7G,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CACD,MAAM,CAAE;EACZ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8G,2BAA2B,GAAGrI,+BAA+B,CAAC;EAAEsI,OAAO,EAAE;AAAK,CAAC,CAAC;AACtF;AACA,MAAMC,0BAA0B,GAAGvI,+BAA+B,CAAC;EAAEsI,OAAO,EAAE;AAAM,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,uBAAuB,GAAG,IAAIC,GAAG,CAAC;AACpC;AACA,UAAU,CACb,CAAC;AACF;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACV;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,CAAC,EAAE,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACF,QAAQ,CAAC;EACpF;EACA,IAAIA,QAAQA,CAACjH,KAAK,EAAE;IAChB,MAAMoH,QAAQ,GAAG7I,qBAAqB,CAACyB,KAAK,CAAC;IAC7C,IAAIoH,QAAQ,KAAK,IAAI,CAACF,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAGE,QAAQ;MACzB,IAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC,IAAI,CAACC,QAAQ,CAACnD,OAAO,CAACoD,MAAM,IAAInH,4BAA4B,CAACmH,MAAM,EAAEH,QAAQ,CAAC,CAAC;IACnF;EACJ;EACA3D,WAAWA,CAACpD,OAAO,EAAEmH,OAAO,EAAE9D,SAAS,EAAE+D,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;IACjF,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9D,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC+D,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG;MAAE/E,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACvC;IACA,IAAI,CAAC+E,gBAAgB,GAAG;MAAEhF,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAACgF,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,WAAW,GAAG,IAAIlJ,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAACmJ,wBAAwB,GAAGlJ,YAAY,CAACmJ,KAAK;IAClD;IACA,IAAI,CAACC,sBAAsB,GAAGpJ,YAAY,CAACmJ,KAAK;IAChD;IACA,IAAI,CAACE,mBAAmB,GAAGrJ,YAAY,CAACmJ,KAAK;IAC7C;IACA,IAAI,CAACG,mBAAmB,GAAGtJ,YAAY,CAACmJ,KAAK;IAC7C;IACA,IAAI,CAACI,gBAAgB,GAAG,IAAI;IAC5B;IACA,IAAI,CAACC,0BAA0B,GAAG,IAAI;IACtC;IACA,IAAI,CAAChB,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACiB,gBAAgB,GAAG,IAAIxB,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACyB,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACvB,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACwB,aAAa,GAAG,IAAI7J,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAAC8J,OAAO,GAAG,IAAI9J,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC+J,QAAQ,GAAG,IAAI/J,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACgK,KAAK,GAAG,IAAIhK,OAAO,CAAC,CAAC;IAC1B;IACA,IAAI,CAACiK,OAAO,GAAG,IAAIjK,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACkK,MAAM,GAAG,IAAIlK,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACmK,OAAO,GAAG,IAAInK,OAAO,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACoK,KAAK,GAAG,IAAI,CAAClB,WAAW;IAC7B;IACA,IAAI,CAACmB,YAAY,GAAI3E,KAAK,IAAK;MAC3B,IAAI,CAACmE,aAAa,CAACS,IAAI,CAAC,CAAC;MACzB;MACA,IAAI,IAAI,CAAC7B,QAAQ,CAACzB,MAAM,EAAE;QACtB,MAAMuD,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAC9E,KAAK,CAAC;QACjD,IAAI6E,YAAY,IAAI,CAAC,IAAI,CAACb,gBAAgB,CAACrI,GAAG,CAACkJ,YAAY,CAAC,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;UAC5E,IAAI,CAACqC,uBAAuB,CAACF,YAAY,EAAE7E,KAAK,CAAC;QACrD;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAC0C,QAAQ,EAAE;QACrB,IAAI,CAACqC,uBAAuB,CAAC,IAAI,CAACC,YAAY,EAAEhF,KAAK,CAAC;MAC1D;IACJ,CAAC;IACD;IACA,IAAI,CAACiF,YAAY,GAAIjF,KAAK,IAAK;MAC3B,MAAMkF,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACnF,KAAK,CAAC;MAC7D,IAAI,CAAC,IAAI,CAACuD,mBAAmB,EAAE;QAC3B,MAAM6B,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACJ,eAAe,CAAC5G,CAAC,GAAG,IAAI,CAACiH,qBAAqB,CAACjH,CAAC,CAAC;QAC5E,MAAMkH,SAAS,GAAGH,IAAI,CAACC,GAAG,CAACJ,eAAe,CAAC3G,CAAC,GAAG,IAAI,CAACgH,qBAAqB,CAAChH,CAAC,CAAC;QAC5E,MAAMkH,eAAe,GAAGL,SAAS,GAAGI,SAAS,IAAI,IAAI,CAACvC,OAAO,CAACyC,kBAAkB;QAChF;QACA;QACA;QACA;QACA,IAAID,eAAe,EAAE;UACjB,MAAME,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC/F,KAAK,CAAC;UACzF,MAAMgG,SAAS,GAAG,IAAI,CAACpD,cAAc;UACrC,IAAI,CAAC+C,cAAc,EAAE;YACjB,IAAI,CAACM,gBAAgB,CAACjG,KAAK,CAAC;YAC5B;UACJ;UACA;UACA;UACA;UACA,IAAI,CAACgG,SAAS,IAAK,CAACA,SAAS,CAACE,UAAU,CAAC,CAAC,IAAI,CAACF,SAAS,CAACG,WAAW,CAAC,CAAE,EAAE;YACrE;YACA;YACAnG,KAAK,CAACoG,cAAc,CAAC,CAAC;YACtB,IAAI,CAAC7C,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAACL,OAAO,CAACmD,GAAG,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAACtG,KAAK,CAAC,CAAC;UAC1D;QACJ;QACA;MACJ;MACA;MACA;MACA;MACAA,KAAK,CAACoG,cAAc,CAAC,CAAC;MACtB,MAAMG,0BAA0B,GAAG,IAAI,CAACC,8BAA8B,CAACtB,eAAe,CAAC;MACvF,IAAI,CAACuB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,yBAAyB,GAAGxB,eAAe;MAChD,IAAI,CAACyB,4BAA4B,CAACJ,0BAA0B,CAAC;MAC7D,IAAI,IAAI,CAAC3D,cAAc,EAAE;QACrB,IAAI,CAACgE,0BAA0B,CAACL,0BAA0B,EAAErB,eAAe,CAAC;MAChF,CAAC,MACI;QACD;QACA;QACA,MAAM2B,MAAM,GAAG,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACxB,qBAAqB;QAC5F,MAAMyB,eAAe,GAAG,IAAI,CAAC1D,gBAAgB;QAC7C0D,eAAe,CAAC1I,CAAC,GAAGiI,0BAA0B,CAACjI,CAAC,GAAGuI,MAAM,CAACvI,CAAC,GAAG,IAAI,CAAC+E,iBAAiB,CAAC/E,CAAC;QACtF0I,eAAe,CAACzI,CAAC,GAAGgI,0BAA0B,CAAChI,CAAC,GAAGsI,MAAM,CAACtI,CAAC,GAAG,IAAI,CAAC8E,iBAAiB,CAAC9E,CAAC;QACtF,IAAI,CAAC0I,0BAA0B,CAACD,eAAe,CAAC1I,CAAC,EAAE0I,eAAe,CAACzI,CAAC,CAAC;MACzE;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACiF,WAAW,CAAC0D,SAAS,CAAC5F,MAAM,EAAE;QACnC,IAAI,CAAC4B,OAAO,CAACmD,GAAG,CAAC,MAAM;UACnB,IAAI,CAAC7C,WAAW,CAACoB,IAAI,CAAC;YAClBvJ,MAAM,EAAE,IAAI;YACZ6J,eAAe,EAAEqB,0BAA0B;YAC3CvG,KAAK;YACLmH,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACb,0BAA0B,CAAC;YAC3Dc,KAAK,EAAE,IAAI,CAACC;UAChB,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC;IACD;IACA,IAAI,CAACC,UAAU,GAAIvH,KAAK,IAAK;MACzB,IAAI,CAACiG,gBAAgB,CAACjG,KAAK,CAAC;IAChC,CAAC;IACD;IACA,IAAI,CAACwH,gBAAgB,GAAIxH,KAAK,IAAK;MAC/B,IAAI,IAAI,CAAC+C,QAAQ,CAACzB,MAAM,EAAE;QACtB,MAAMuD,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAC9E,KAAK,CAAC;QACjD,IAAI6E,YAAY,IAAI,CAAC,IAAI,CAACb,gBAAgB,CAACrI,GAAG,CAACkJ,YAAY,CAAC,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;UAC5E1C,KAAK,CAACoG,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAC1D,QAAQ,EAAE;QACrB;QACA;QACA1C,KAAK,CAACoG,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,CAACqB,eAAe,CAAC3L,OAAO,CAAC,CAAC4L,UAAU,CAACzE,OAAO,CAAC0E,aAAa,IAAI,IAAI,CAAC;IACvE,IAAI,CAACC,gBAAgB,GAAG,IAAI3I,qBAAqB,CAACE,SAAS,CAAC;IAC5DiE,iBAAiB,CAACyE,gBAAgB,CAAC,IAAI,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChD,YAAY;EAC5B;EACA;AACJ;AACA;AACA;EACIiD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC/B,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC4B,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC;EACnF;EACA;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACpF,QAAQ,GAAGoF,OAAO,CAACrN,GAAG,CAACkI,MAAM,IAAI/I,aAAa,CAAC+I,MAAM,CAAC,CAAC;IAC5D,IAAI,CAACD,QAAQ,CAACnD,OAAO,CAACoD,MAAM,IAAInH,4BAA4B,CAACmH,MAAM,EAAE,IAAI,CAACN,QAAQ,CAAC,CAAC;IACpF,IAAI,CAACI,6BAA6B,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA,MAAMsF,eAAe,GAAG,IAAI5F,GAAG,CAAC,CAAC;IACjC,IAAI,CAACwB,gBAAgB,CAACpE,OAAO,CAACoD,MAAM,IAAI;MACpC,IAAI,IAAI,CAACD,QAAQ,CAAClG,OAAO,CAACmG,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;QACpCoF,eAAe,CAACC,GAAG,CAACrF,MAAM,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAACgB,gBAAgB,GAAGoE,eAAe;IACvC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAGD,QAAQ;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACF,QAAQ,EAAE;IAC9B,IAAI,CAACG,oBAAoB,GAAGH,QAAQ;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACId,eAAeA,CAACkB,WAAW,EAAE;IACzB,MAAM7M,OAAO,GAAG7B,aAAa,CAAC0O,WAAW,CAAC;IAC1C,IAAI7M,OAAO,KAAK,IAAI,CAACkJ,YAAY,EAAE;MAC/B,IAAI,IAAI,CAACA,YAAY,EAAE;QACnB,IAAI,CAAC4D,2BAA2B,CAAC,IAAI,CAAC5D,YAAY,CAAC;MACvD;MACA,IAAI,CAAC9B,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;QACjC/M,OAAO,CAACgN,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACnE,YAAY,EAAEtC,0BAA0B,CAAC;QACpFvG,OAAO,CAACgN,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACnE,YAAY,EAAExC,2BAA2B,CAAC;QACtFrG,OAAO,CAACgN,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACtB,gBAAgB,EAAEnF,0BAA0B,CAAC;MAC5F,CAAC,CAAC;MACF,IAAI,CAAC0G,iBAAiB,GAAGC,SAAS;MAClC,IAAI,CAAChE,YAAY,GAAGlJ,OAAO;IAC/B;IACA,IAAI,OAAOmN,UAAU,KAAK,WAAW,IAAI,IAAI,CAACjE,YAAY,YAAYiE,UAAU,EAAE;MAC9E,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAClE,YAAY,CAACmE,eAAe;IAC7D;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,mBAAmBA,CAACC,eAAe,EAAE;IACjC,IAAI,CAACvF,gBAAgB,GAAGuF,eAAe,GAAGpP,aAAa,CAACoP,eAAe,CAAC,GAAG,IAAI;IAC/E,IAAI,CAACxF,mBAAmB,CAACyF,WAAW,CAAC,CAAC;IACtC,IAAID,eAAe,EAAE;MACjB,IAAI,CAACxF,mBAAmB,GAAG,IAAI,CAACV,cAAc,CACzCoG,MAAM,CAAC,EAAE,CAAC,CACVC,SAAS,CAAC,MAAM,IAAI,CAACC,8BAA8B,CAAC,CAAC,CAAC;IAC/D;IACA,OAAO,IAAI;EACf;EACA;EACA/B,UAAUA,CAACgC,MAAM,EAAE;IACf,IAAI,CAACC,cAAc,GAAGD,MAAM;IAC5B,OAAO,IAAI;EACf;EACA;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAChB,2BAA2B,CAAC,IAAI,CAAC5D,YAAY,CAAC;IACnD;IACA;IACA,IAAI,IAAI,CAACkB,UAAU,CAAC,CAAC,EAAE;MACnB;MACA;MACA,IAAI,CAAClB,YAAY,EAAE6E,MAAM,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,OAAO,EAAED,MAAM,CAAC,CAAC;IACtB,IAAI,CAACE,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC5G,iBAAiB,CAAC6G,cAAc,CAAC,IAAI,CAAC;IAC3C,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC/F,aAAa,CAACgG,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC/F,OAAO,CAAC+F,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC9F,QAAQ,CAAC8F,QAAQ,CAAC,CAAC;IACxB,IAAI,CAAC7F,KAAK,CAAC6F,QAAQ,CAAC,CAAC;IACrB,IAAI,CAAC5F,OAAO,CAAC4F,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC3F,MAAM,CAAC2F,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC1F,OAAO,CAAC0F,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC3G,WAAW,CAAC2G,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACpH,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACiB,gBAAgB,CAAC1E,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACsD,cAAc,GAAGoG,SAAS;IAC/B,IAAI,CAACnF,mBAAmB,CAACyF,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC1B,gBAAgB,CAACtI,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACwE,gBAAgB,GACjB,IAAI,CAACkB,YAAY,GACb,IAAI,CAACkE,gBAAgB,GACjB,IAAI,CAACR,oBAAoB,GACrB,IAAI,CAACF,gBAAgB,GACjB,IAAI,CAACsB,OAAO,GACR,IAAI,CAACH,cAAc,GACf,IAAI;EACpC;EACA;EACAzD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC3C,mBAAmB,IAAI,IAAI,CAACH,iBAAiB,CAAC8C,UAAU,CAAC,IAAI,CAAC;EAC9E;EACA;EACAkE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpF,YAAY,CAAC/I,KAAK,CAACO,SAAS,GAAG,IAAI,CAACuM,iBAAiB,IAAI,EAAE;IAChE,IAAI,CAACzF,gBAAgB,GAAG;MAAEhF,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC,IAAI,CAAC8E,iBAAiB,GAAG;MAAE/E,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACI8L,aAAaA,CAACrH,MAAM,EAAE;IAClB,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAACrI,GAAG,CAACqH,MAAM,CAAC,IAAI,IAAI,CAACD,QAAQ,CAAClG,OAAO,CAACmG,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1E,IAAI,CAACgB,gBAAgB,CAACqE,GAAG,CAACrF,MAAM,CAAC;MACjCnH,4BAA4B,CAACmH,MAAM,EAAE,IAAI,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;AACA;EACIsH,YAAYA,CAACtH,MAAM,EAAE;IACjB,IAAI,IAAI,CAACgB,gBAAgB,CAACrI,GAAG,CAACqH,MAAM,CAAC,EAAE;MACnC,IAAI,CAACgB,gBAAgB,CAACuG,MAAM,CAACvH,MAAM,CAAC;MACpCnH,4BAA4B,CAACmH,MAAM,EAAE,IAAI,CAACN,QAAQ,CAAC;IACvD;EACJ;EACA;EACA8H,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACxG,UAAU,GAAGwG,SAAS;IAC3B,OAAO,IAAI;EACf;EACA;EACAC,kBAAkBA,CAAC1E,SAAS,EAAE;IAC1B,IAAI,CAACpD,cAAc,GAAGoD,SAAS;EACnC;EACA;AACJ;AACA;EACI2E,mBAAmBA,CAAA,EAAG;IAClB,MAAMxO,QAAQ,GAAG,IAAI,CAAC+J,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC5C,gBAAgB,GAAG,IAAI,CAACD,iBAAiB;IACnF,OAAO;MAAE/E,CAAC,EAAEnC,QAAQ,CAACmC,CAAC;MAAEC,CAAC,EAAEpC,QAAQ,CAACoC;IAAE,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIqM,mBAAmBA,CAACnP,KAAK,EAAE;IACvB,IAAI,CAAC6H,gBAAgB,GAAG;MAAEhF,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC,IAAI,CAAC8E,iBAAiB,CAAC/E,CAAC,GAAG7C,KAAK,CAAC6C,CAAC;IAClC,IAAI,CAAC+E,iBAAiB,CAAC9E,CAAC,GAAG9C,KAAK,CAAC8C,CAAC;IAClC,IAAI,CAAC,IAAI,CAACqE,cAAc,EAAE;MACtB,IAAI,CAACqE,0BAA0B,CAACxL,KAAK,CAAC6C,CAAC,EAAE7C,KAAK,CAAC8C,CAAC,CAAC;IACrD;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIsM,oBAAoBA,CAACpP,KAAK,EAAE;IACxB,IAAI,CAACqP,iBAAiB,GAAGrP,KAAK;IAC9B,OAAO,IAAI;EACf;EACA;EACAsP,4BAA4BA,CAAA,EAAG;IAC3B,MAAM5O,QAAQ,GAAG,IAAI,CAACuK,yBAAyB;IAC/C,IAAIvK,QAAQ,IAAI,IAAI,CAACyG,cAAc,EAAE;MACjC,IAAI,CAACgE,0BAA0B,CAAC,IAAI,CAACJ,8BAA8B,CAACrK,QAAQ,CAAC,EAAEA,QAAQ,CAAC;IAC5F;EACJ;EACA;EACA+N,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACzG,wBAAwB,CAAC6F,WAAW,CAAC,CAAC;IAC3C,IAAI,CAAC3F,sBAAsB,CAAC2F,WAAW,CAAC,CAAC;IACzC,IAAI,CAAC1F,mBAAmB,CAAC0F,WAAW,CAAC,CAAC;EAC1C;EACA;EACAS,eAAeA,CAAA,EAAG;IACd,IAAI,CAACiB,QAAQ,EAAEnB,MAAM,CAAC,CAAC;IACvB,IAAI,CAACoB,WAAW,EAAEC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3C;EACA;EACAjB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACjC,YAAY,EAAE8B,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACsB,eAAe,EAAED,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACnD,YAAY,GAAG,IAAI,CAACoD,eAAe,GAAG,IAAI;EACnD;EACA;AACJ;AACA;AACA;EACIlF,gBAAgBA,CAACjG,KAAK,EAAE;IACpB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACoD,iBAAiB,CAAC8C,UAAU,CAAC,IAAI,CAAC,EAAE;MAC1C;IACJ;IACA,IAAI,CAACgE,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC9G,iBAAiB,CAACgI,YAAY,CAAC,IAAI,CAAC;IACzC,IAAI,CAACtI,6BAA6B,CAAC,CAAC;IACpC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACiC,YAAY,CAAC/I,KAAK,CAACoP,uBAAuB,GAC3C,IAAI,CAACC,wBAAwB;IACrC;IACA,IAAI,CAAC,IAAI,CAAC/H,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACc,QAAQ,CAACO,IAAI,CAAC;MAAEvJ,MAAM,EAAE,IAAI;MAAE2E;IAAM,CAAC,CAAC;IAC3C,IAAI,IAAI,CAAC4C,cAAc,EAAE;MACrB;MACA,IAAI,CAACA,cAAc,CAAC2I,cAAc,CAAC,CAAC;MACpC,IAAI,CAACC,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC3C,IAAI,CAACC,qBAAqB,CAAC1L,KAAK,CAAC;QACjC,IAAI,CAAC2L,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACvI,iBAAiB,CAACgI,YAAY,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAAC/H,iBAAiB,CAAC/E,CAAC,GAAG,IAAI,CAACgF,gBAAgB,CAAChF,CAAC;MAClD,MAAM4G,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACnF,KAAK,CAAC;MAC7D,IAAI,CAACqD,iBAAiB,CAAC9E,CAAC,GAAG,IAAI,CAAC+E,gBAAgB,CAAC/E,CAAC;MAClD,IAAI,CAAC2E,OAAO,CAACmD,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC/B,KAAK,CAACM,IAAI,CAAC;UACZvJ,MAAM,EAAE,IAAI;UACZ8L,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAAClC,eAAe,CAAC;UAChD0G,SAAS,EAAE1G,eAAe;UAC1BlF;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAAC2L,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACvI,iBAAiB,CAACgI,YAAY,CAAC,IAAI,CAAC;IAC7C;EACJ;EACA;EACA9E,kBAAkBA,CAACtG,KAAK,EAAE;IACtB,IAAI6L,YAAY,CAAC7L,KAAK,CAAC,EAAE;MACrB,IAAI,CAAC8L,mBAAmB,GAAGlG,IAAI,CAACC,GAAG,CAAC,CAAC;IACzC;IACA,IAAI,CAAC/C,6BAA6B,CAAC,CAAC;IACpC,MAAMiJ,aAAa,GAAG,IAAI,CAACnJ,cAAc;IACzC,IAAImJ,aAAa,EAAE;MACf,MAAMjQ,OAAO,GAAG,IAAI,CAACkJ,YAAY;MACjC,MAAM0E,MAAM,GAAG5N,OAAO,CAACkQ,UAAU;MACjC,MAAMC,WAAW,GAAI,IAAI,CAAClE,YAAY,GAAG,IAAI,CAACmE,yBAAyB,CAAC,CAAE;MAC1E,MAAMC,MAAM,GAAI,IAAI,CAACrC,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAAC3K,SAAS,CAACiN,aAAa,CAAC,EAAE,CAAE;MAChF;MACA,MAAMC,UAAU,GAAG,IAAI,CAACtS,cAAc,CAAC,CAAC;MACxC;MACA2P,MAAM,CAAC4C,YAAY,CAACH,MAAM,EAAErQ,OAAO,CAAC;MACpC;MACA;MACA,IAAI,CAACiN,iBAAiB,GAAGjN,OAAO,CAACG,KAAK,CAACO,SAAS,IAAI,EAAE;MACtD;MACA;MACA,IAAI,CAACwO,QAAQ,GAAG,IAAI,CAACuB,qBAAqB,CAAC,CAAC;MAC5C;MACA;MACA;MACArQ,gBAAgB,CAACJ,OAAO,EAAE,KAAK,EAAEyG,uBAAuB,CAAC;MACzD,IAAI,CAACpD,SAAS,CAACqN,IAAI,CAACC,WAAW,CAAC/C,MAAM,CAACgD,YAAY,CAACT,WAAW,EAAEnQ,OAAO,CAAC,CAAC;MAC1E,IAAI,CAAC6Q,yBAAyB,CAACjD,MAAM,EAAE2C,UAAU,CAAC,CAACI,WAAW,CAAC,IAAI,CAACzB,QAAQ,CAAC;MAC7E,IAAI,CAAC5G,OAAO,CAACQ,IAAI,CAAC;QAAEvJ,MAAM,EAAE,IAAI;QAAE2E;MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C+L,aAAa,CAACa,KAAK,CAAC,CAAC;MACrB,IAAI,CAACC,iBAAiB,GAAGd,aAAa;MACtC,IAAI,CAACe,aAAa,GAAGf,aAAa,CAACgB,YAAY,CAAC,IAAI,CAAC;IACzD,CAAC,MACI;MACD,IAAI,CAAC3I,OAAO,CAACQ,IAAI,CAAC;QAAEvJ,MAAM,EAAE,IAAI;QAAE2E;MAAM,CAAC,CAAC;MAC1C,IAAI,CAAC6M,iBAAiB,GAAG,IAAI,CAACC,aAAa,GAAG9D,SAAS;IAC3D;IACA;IACA;IACA,IAAI,CAACpB,gBAAgB,CAACrI,KAAK,CAACwM,aAAa,GAAGA,aAAa,CAACiB,oBAAoB,CAAC,CAAC,GAAG,EAAE,CAAC;EAC1F;EACA;AACJ;AACA;AACA;AACA;AACA;EACIjI,uBAAuBA,CAACkI,gBAAgB,EAAEjN,KAAK,EAAE;IAC7C;IACA;IACA,IAAI,IAAI,CAAC2J,cAAc,EAAE;MACrB3J,KAAK,CAACkN,eAAe,CAAC,CAAC;IAC3B;IACA,MAAMhH,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IACpC,MAAMiH,eAAe,GAAGtB,YAAY,CAAC7L,KAAK,CAAC;IAC3C,MAAMoN,sBAAsB,GAAG,CAACD,eAAe,IAAInN,KAAK,CAACqN,MAAM,KAAK,CAAC;IACrE,MAAM1E,WAAW,GAAG,IAAI,CAAC3D,YAAY;IACrC,MAAM/E,MAAM,GAAGpG,eAAe,CAACmG,KAAK,CAAC;IACrC,MAAMsN,gBAAgB,GAAG,CAACH,eAAe,IACrC,IAAI,CAACrB,mBAAmB,IACxB,IAAI,CAACA,mBAAmB,GAAGxJ,uBAAuB,GAAGsD,IAAI,CAACC,GAAG,CAAC,CAAC;IACnE,MAAM0H,WAAW,GAAGJ,eAAe,GAC7B/S,gCAAgC,CAAC4F,KAAK,CAAC,GACvC3F,+BAA+B,CAAC2F,KAAK,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,MAAM,IAAIA,MAAM,CAACuN,SAAS,IAAIxN,KAAK,CAAC+B,IAAI,KAAK,WAAW,EAAE;MAC1D/B,KAAK,CAACoG,cAAc,CAAC,CAAC;IAC1B;IACA;IACA,IAAIF,UAAU,IAAIkH,sBAAsB,IAAIE,gBAAgB,IAAIC,WAAW,EAAE;MACzE;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACxK,QAAQ,CAACzB,MAAM,EAAE;MACtB,MAAMmM,UAAU,GAAG9E,WAAW,CAAC1M,KAAK;MACpC,IAAI,CAACqP,wBAAwB,GAAGmC,UAAU,CAACpC,uBAAuB,IAAI,EAAE;MACxEoC,UAAU,CAACpC,uBAAuB,GAAG,aAAa;IACtD;IACA,IAAI,CAAC9H,mBAAmB,GAAG,IAAI,CAACkD,SAAS,GAAG,KAAK;IACjD;IACA;IACA,IAAI,CAACyD,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACnD,kBAAkB,GAAG,IAAI,CAAC/B,YAAY,CAAC/G,qBAAqB,CAAC,CAAC;IACnE,IAAI,CAACwF,wBAAwB,GAAG,IAAI,CAACL,iBAAiB,CAACsK,WAAW,CAAClE,SAAS,CAAC,IAAI,CAACvE,YAAY,CAAC;IAC/F,IAAI,CAACtB,sBAAsB,GAAG,IAAI,CAACP,iBAAiB,CAACuK,SAAS,CAACnE,SAAS,CAAC,IAAI,CAACjC,UAAU,CAAC;IACzF,IAAI,CAAC3D,mBAAmB,GAAG,IAAI,CAACR,iBAAiB,CAC5CwK,QAAQ,CAAC,IAAI,CAAC7T,cAAc,CAAC,CAAC,CAAC,CAC/ByP,SAAS,CAACqE,WAAW,IAAI,IAAI,CAACC,eAAe,CAACD,WAAW,CAAC,CAAC;IAChE,IAAI,IAAI,CAAC/J,gBAAgB,EAAE;MACvB,IAAI,CAACiK,aAAa,GAAGhQ,oBAAoB,CAAC,IAAI,CAAC+F,gBAAgB,CAAC;IACpE;IACA;IACA;IACA;IACA,MAAMkK,eAAe,GAAG,IAAI,CAACxF,gBAAgB;IAC7C,IAAI,CAACyF,wBAAwB,GACzBD,eAAe,IAAIA,eAAe,CAACzF,QAAQ,IAAI,CAACyF,eAAe,CAACE,SAAS,GACnE;MAAE5P,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,GACd,IAAI,CAAC4P,4BAA4B,CAAC,IAAI,CAACpH,kBAAkB,EAAEkG,gBAAgB,EAAEjN,KAAK,CAAC;IAC7F,MAAMkF,eAAe,GAAI,IAAI,CAACK,qBAAqB,GAC/C,IAAI,CAACmB,yBAAyB,GAC1B,IAAI,CAACvB,yBAAyB,CAACnF,KAAK,CAAE;IAC9C,IAAI,CAACsH,sBAAsB,GAAG;MAAEhJ,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC5C,IAAI,CAAC6P,qCAAqC,GAAG;MAAE9P,CAAC,EAAE4G,eAAe,CAAC5G,CAAC;MAAEC,CAAC,EAAE2G,eAAe,CAAC3G;IAAE,CAAC;IAC3F,IAAI,CAACuH,cAAc,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC;IAChC,IAAI,CAACzC,iBAAiB,CAACiL,aAAa,CAAC,IAAI,EAAErO,KAAK,CAAC;EACrD;EACA;EACA0L,qBAAqBA,CAAC1L,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACA9D,gBAAgB,CAAC,IAAI,CAAC8I,YAAY,EAAE,IAAI,EAAEzC,uBAAuB,CAAC;IAClE,IAAI,CAACuH,OAAO,CAACkC,UAAU,CAACU,YAAY,CAAC,IAAI,CAAC1H,YAAY,EAAE,IAAI,CAAC8E,OAAO,CAAC;IACrE,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACjD,kBAAkB,GACnB,IAAI,CAACgH,aAAa,GACd,IAAI,CAACO,YAAY,GACb,IAAI,CAACvF,iBAAiB,GAClBC,SAAS;IACzB;IACA,IAAI,CAAC9F,OAAO,CAACmD,GAAG,CAAC,MAAM;MACnB,MAAML,SAAS,GAAG,IAAI,CAACpD,cAAc;MACrC,MAAM2L,YAAY,GAAGvI,SAAS,CAAC+G,YAAY,CAAC,IAAI,CAAC;MACjD,MAAM7H,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACnF,KAAK,CAAC;MAC7D,MAAMmH,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAClC,eAAe,CAAC;MACvD,MAAMsJ,sBAAsB,GAAGxI,SAAS,CAACyI,gBAAgB,CAACvJ,eAAe,CAAC5G,CAAC,EAAE4G,eAAe,CAAC3G,CAAC,CAAC;MAC/F,IAAI,CAAC+F,KAAK,CAACM,IAAI,CAAC;QAAEvJ,MAAM,EAAE,IAAI;QAAE8L,QAAQ;QAAEyE,SAAS,EAAE1G,eAAe;QAAElF;MAAM,CAAC,CAAC;MAC9E,IAAI,CAACyE,OAAO,CAACG,IAAI,CAAC;QACd8J,IAAI,EAAE,IAAI;QACVH,YAAY;QACZI,aAAa,EAAE,IAAI,CAAC7B,aAAa;QACjC9G,SAAS,EAAEA,SAAS;QACpB4I,iBAAiB,EAAE,IAAI,CAAC/B,iBAAiB;QACzC2B,sBAAsB;QACtBrH,QAAQ;QACRyE,SAAS,EAAE1G,eAAe;QAC1BlF;MACJ,CAAC,CAAC;MACFgG,SAAS,CAAC6I,IAAI,CAAC,IAAI,EAAEN,YAAY,EAAE,IAAI,CAACzB,aAAa,EAAE,IAAI,CAACD,iBAAiB,EAAE2B,sBAAsB,EAAErH,QAAQ,EAAEjC,eAAe,EAAElF,KAAK,CAAC;MACxI,IAAI,CAAC4C,cAAc,GAAG,IAAI,CAACiK,iBAAiB;IAChD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIjG,0BAA0BA,CAAC;IAAEtI,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAED,CAAC,EAAEwQ,IAAI;IAAEvQ,CAAC,EAAEwQ;EAAK,CAAC,EAAE;IACvD;IACA,IAAIC,YAAY,GAAG,IAAI,CAACnC,iBAAiB,CAACoC,gCAAgC,CAAC,IAAI,EAAE3Q,CAAC,EAAEC,CAAC,CAAC;IACtF;IACA;IACA;IACA;IACA,IAAI,CAACyQ,YAAY,IACb,IAAI,CAACpM,cAAc,KAAK,IAAI,CAACiK,iBAAiB,IAC9C,IAAI,CAACA,iBAAiB,CAAC4B,gBAAgB,CAACnQ,CAAC,EAAEC,CAAC,CAAC,EAAE;MAC/CyQ,YAAY,GAAG,IAAI,CAACnC,iBAAiB;IACzC;IACA,IAAImC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAACpM,cAAc,EAAE;MACtD,IAAI,CAACM,OAAO,CAACmD,GAAG,CAAC,MAAM;QACnB;QACA,IAAI,CAAC7B,MAAM,CAACI,IAAI,CAAC;UAAE8J,IAAI,EAAE,IAAI;UAAE1I,SAAS,EAAE,IAAI,CAACpD;QAAe,CAAC,CAAC;QAChE,IAAI,CAACA,cAAc,CAACsM,IAAI,CAAC,IAAI,CAAC;QAC9B;QACA,IAAI,CAACtM,cAAc,GAAGoM,YAAY;QAClC,IAAI,CAACpM,cAAc,CAACuM,KAAK,CAAC,IAAI,EAAE7Q,CAAC,EAAEC,CAAC,EAAEyQ,YAAY,KAAK,IAAI,CAACnC,iBAAiB;QACzE;QACA;QACAmC,YAAY,CAACI,eAAe,GAC1B,IAAI,CAACtC,aAAa,GAClB9D,SAAS,CAAC;QAChB,IAAI,CAACzE,OAAO,CAACK,IAAI,CAAC;UACd8J,IAAI,EAAE,IAAI;UACV1I,SAAS,EAAEgJ,YAAY;UACvBT,YAAY,EAAES,YAAY,CAACjC,YAAY,CAAC,IAAI;QAChD,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA;IACA,IAAI,IAAI,CAAC7G,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACtD,cAAc,CAACyM,0BAA0B,CAACP,IAAI,EAAEC,IAAI,CAAC;MAC1D,IAAI,CAACnM,cAAc,CAAC0M,SAAS,CAAC,IAAI,EAAEhR,CAAC,EAAEC,CAAC,EAAE,IAAI,CAAC+I,sBAAsB,CAAC;MACtE,IAAI,IAAI,CAACR,iBAAiB,EAAE;QACxB,IAAI,CAACyI,sBAAsB,CAACjR,CAAC,EAAEC,CAAC,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACgR,sBAAsB,CAACjR,CAAC,GAAG,IAAI,CAAC2P,wBAAwB,CAAC3P,CAAC,EAAEC,CAAC,GAAG,IAAI,CAAC0P,wBAAwB,CAAC1P,CAAC,CAAC;MACzG;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIgO,qBAAqBA,CAAA,EAAG;IACpB,MAAMiD,aAAa,GAAG,IAAI,CAAChH,gBAAgB;IAC3C,MAAMiH,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,MAAMzB,eAAe,GAAGwB,aAAa,GAAGA,aAAa,CAACjH,QAAQ,GAAG,IAAI;IACrE,IAAImH,OAAO;IACX,IAAI1B,eAAe,IAAIwB,aAAa,EAAE;MAClC;MACA;MACA,MAAMG,QAAQ,GAAGH,aAAa,CAACtB,SAAS,GAAG,IAAI,CAACnH,kBAAkB,GAAG,IAAI;MACzE,MAAM6I,OAAO,GAAGJ,aAAa,CAACK,aAAa,CAACC,kBAAkB,CAAC9B,eAAe,EAAEwB,aAAa,CAACxN,OAAO,CAAC;MACtG4N,OAAO,CAACG,aAAa,CAAC,CAAC;MACvBL,OAAO,GAAGM,WAAW,CAACJ,OAAO,EAAE,IAAI,CAACzQ,SAAS,CAAC;MAC9C,IAAI,CAAC8L,WAAW,GAAG2E,OAAO;MAC1B,IAAIJ,aAAa,CAACtB,SAAS,EAAE;QACzB+B,gBAAgB,CAACP,OAAO,EAAEC,QAAQ,CAAC;MACvC,CAAC,MACI;QACDD,OAAO,CAACzT,KAAK,CAACO,SAAS,GAAG0T,YAAY,CAAC,IAAI,CAAC3K,qBAAqB,CAACjH,CAAC,EAAE,IAAI,CAACiH,qBAAqB,CAAChH,CAAC,CAAC;MACtG;IACJ,CAAC,MACI;MACDmR,OAAO,GAAG5O,aAAa,CAAC,IAAI,CAACkE,YAAY,CAAC;MAC1CiL,gBAAgB,CAACP,OAAO,EAAE,IAAI,CAAC3I,kBAAkB,CAAC;MAClD,IAAI,IAAI,CAACgC,iBAAiB,EAAE;QACxB2G,OAAO,CAACzT,KAAK,CAACO,SAAS,GAAG,IAAI,CAACuM,iBAAiB;MACpD;IACJ;IACA5N,YAAY,CAACuU,OAAO,CAACzT,KAAK,EAAE;MACxB;MACA;MACA,gBAAgB,EAAE,MAAM;MACxB;MACA,QAAQ,EAAE,GAAG;MACb,UAAU,EAAE,OAAO;MACnB,KAAK,EAAE,GAAG;MACV,MAAM,EAAE,GAAG;MACX,SAAS,EAAG,GAAE,IAAI,CAACgH,OAAO,CAACkN,MAAM,IAAI,IAAK;IAC9C,CAAC,EAAE5N,uBAAuB,CAAC;IAC3B1G,4BAA4B,CAAC6T,OAAO,EAAE,KAAK,CAAC;IAC5CA,OAAO,CAACU,SAAS,CAAC/H,GAAG,CAAC,kBAAkB,CAAC;IACzCqH,OAAO,CAACW,YAAY,CAAC,KAAK,EAAE,IAAI,CAACpM,UAAU,CAAC;IAC5C,IAAIwL,YAAY,EAAE;MACd,IAAIa,KAAK,CAACC,OAAO,CAACd,YAAY,CAAC,EAAE;QAC7BA,YAAY,CAAC7P,OAAO,CAAC4Q,SAAS,IAAId,OAAO,CAACU,SAAS,CAAC/H,GAAG,CAACmI,SAAS,CAAC,CAAC;MACvE,CAAC,MACI;QACDd,OAAO,CAACU,SAAS,CAAC/H,GAAG,CAACoH,YAAY,CAAC;MACvC;IACJ;IACA,OAAOC,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACIlE,4BAA4BA,CAAA,EAAG;IAC3B;IACA,IAAI,CAAC,IAAI,CAAC/E,SAAS,EAAE;MACjB,OAAOgK,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,MAAMC,eAAe,GAAG,IAAI,CAAC5I,YAAY,CAAC9J,qBAAqB,CAAC,CAAC;IACjE;IACA,IAAI,CAAC+M,QAAQ,CAACoF,SAAS,CAAC/H,GAAG,CAAC,oBAAoB,CAAC;IACjD;IACA,IAAI,CAACkH,sBAAsB,CAACoB,eAAe,CAACrU,IAAI,EAAEqU,eAAe,CAACvU,GAAG,CAAC;IACtE;IACA;IACA;IACA;IACA,MAAMwU,QAAQ,GAAG7T,kCAAkC,CAAC,IAAI,CAACiO,QAAQ,CAAC;IAClE,IAAI4F,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAOH,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACxN,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;MACxC,OAAO,IAAI4H,OAAO,CAACC,OAAO,IAAI;QAC1B,MAAMG,OAAO,GAAK7Q,KAAK,IAAK;UACxB,IAAI,CAACA,KAAK,IACLnG,eAAe,CAACmG,KAAK,CAAC,KAAK,IAAI,CAACgL,QAAQ,IAAIhL,KAAK,CAAC8Q,YAAY,KAAK,WAAY,EAAE;YAClF,IAAI,CAAC9F,QAAQ,EAAE+F,mBAAmB,CAAC,eAAe,EAAEF,OAAO,CAAC;YAC5DH,OAAO,CAAC,CAAC;YACTM,YAAY,CAACC,OAAO,CAAC;UACzB;QACJ,CAAE;QACF;QACA;QACA;QACA,MAAMA,OAAO,GAAGC,UAAU,CAACL,OAAO,EAAED,QAAQ,GAAG,GAAG,CAAC;QACnD,IAAI,CAAC5F,QAAQ,CAAClC,gBAAgB,CAAC,eAAe,EAAE+H,OAAO,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA3E,yBAAyBA,CAAA,EAAG;IACxB,MAAMiF,iBAAiB,GAAG,IAAI,CAACzI,oBAAoB;IACnD,MAAM0I,mBAAmB,GAAGD,iBAAiB,GAAGA,iBAAiB,CAAC5I,QAAQ,GAAG,IAAI;IACjF,IAAI0D,WAAW;IACf,IAAImF,mBAAmB,EAAE;MACrB,IAAI,CAACjG,eAAe,GAAGgG,iBAAiB,CAACtB,aAAa,CAACC,kBAAkB,CAACsB,mBAAmB,EAAED,iBAAiB,CAACnP,OAAO,CAAC;MACzH,IAAI,CAACmJ,eAAe,CAAC4E,aAAa,CAAC,CAAC;MACpC9D,WAAW,GAAG+D,WAAW,CAAC,IAAI,CAAC7E,eAAe,EAAE,IAAI,CAAChM,SAAS,CAAC;IACnE,CAAC,MACI;MACD8M,WAAW,GAAGnL,aAAa,CAAC,IAAI,CAACkE,YAAY,CAAC;IAClD;IACA;IACA;IACAiH,WAAW,CAAChQ,KAAK,CAACoV,aAAa,GAAG,MAAM;IACxCpF,WAAW,CAACmE,SAAS,CAAC/H,GAAG,CAAC,sBAAsB,CAAC;IACjD,OAAO4D,WAAW;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIkC,4BAA4BA,CAACmD,WAAW,EAAErE,gBAAgB,EAAEjN,KAAK,EAAE;IAC/D,MAAMuR,aAAa,GAAGtE,gBAAgB,KAAK,IAAI,CAACjI,YAAY,GAAG,IAAI,GAAGiI,gBAAgB;IACtF,MAAMuE,aAAa,GAAGD,aAAa,GAAGA,aAAa,CAACtT,qBAAqB,CAAC,CAAC,GAAGqT,WAAW;IACzF,MAAMG,KAAK,GAAG5F,YAAY,CAAC7L,KAAK,CAAC,GAAGA,KAAK,CAAC0R,aAAa,CAAC,CAAC,CAAC,GAAG1R,KAAK;IAClE,MAAMN,cAAc,GAAG,IAAI,CAACiS,0BAA0B,CAAC,CAAC;IACxD,MAAMrT,CAAC,GAAGmT,KAAK,CAACG,KAAK,GAAGJ,aAAa,CAAClV,IAAI,GAAGoD,cAAc,CAACpD,IAAI;IAChE,MAAMiC,CAAC,GAAGkT,KAAK,CAACI,KAAK,GAAGL,aAAa,CAACpV,GAAG,GAAGsD,cAAc,CAACtD,GAAG;IAC9D,OAAO;MACHkC,CAAC,EAAEkT,aAAa,CAAClV,IAAI,GAAGgV,WAAW,CAAChV,IAAI,GAAGgC,CAAC;MAC5CC,CAAC,EAAEiT,aAAa,CAACpV,GAAG,GAAGkV,WAAW,CAAClV,GAAG,GAAGmC;IAC7C,CAAC;EACL;EACA;EACA4G,yBAAyBA,CAACnF,KAAK,EAAE;IAC7B,MAAMN,cAAc,GAAG,IAAI,CAACiS,0BAA0B,CAAC,CAAC;IACxD,MAAMF,KAAK,GAAG5F,YAAY,CAAC7L,KAAK,CAAC;IAC3B;IACE;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAAC8R,OAAO,CAAC,CAAC,CAAC,IAAI9R,KAAK,CAAC+R,cAAc,CAAC,CAAC,CAAC,IAAI;MAAEH,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,GACvE7R,KAAK;IACX,MAAM1B,CAAC,GAAGmT,KAAK,CAACG,KAAK,GAAGlS,cAAc,CAACpD,IAAI;IAC3C,MAAMiC,CAAC,GAAGkT,KAAK,CAACI,KAAK,GAAGnS,cAAc,CAACtD,GAAG;IAC1C;IACA;IACA,IAAI,IAAI,CAAC8M,gBAAgB,EAAE;MACvB,MAAM8I,SAAS,GAAG,IAAI,CAAC9I,gBAAgB,CAAC+I,YAAY,CAAC,CAAC;MACtD,IAAID,SAAS,EAAE;QACX,MAAME,QAAQ,GAAG,IAAI,CAAChJ,gBAAgB,CAACiJ,cAAc,CAAC,CAAC;QACvDD,QAAQ,CAAC5T,CAAC,GAAGA,CAAC;QACd4T,QAAQ,CAAC3T,CAAC,GAAGA,CAAC;QACd,OAAO2T,QAAQ,CAACE,eAAe,CAACJ,SAAS,CAACK,OAAO,CAAC,CAAC,CAAC;MACxD;IACJ;IACA,OAAO;MAAE/T,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAiI,8BAA8BA,CAACiL,KAAK,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAAC1P,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC2P,QAAQ,GAAG,IAAI;IACnF,IAAI;MAAEjU,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACuI,iBAAiB,GAC/B,IAAI,CAACA,iBAAiB,CAAC2K,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC1K,kBAAkB,EAAE,IAAI,CAACkH,wBAAwB,CAAC,GAC3FwD,KAAK;IACX,IAAI,IAAI,CAACc,QAAQ,KAAK,GAAG,IAAID,iBAAiB,KAAK,GAAG,EAAE;MACpD/T,CAAC,GAAG,IAAI,CAACgH,qBAAqB,CAAChH,CAAC;IACpC,CAAC,MACI,IAAI,IAAI,CAACgU,QAAQ,KAAK,GAAG,IAAID,iBAAiB,KAAK,GAAG,EAAE;MACzDhU,CAAC,GAAG,IAAI,CAACiH,qBAAqB,CAACjH,CAAC;IACpC;IACA,IAAI,IAAI,CAACyP,aAAa,EAAE;MACpB,MAAM;QAAEzP,CAAC,EAAEkU,OAAO;QAAEjU,CAAC,EAAEkU;MAAQ,CAAC,GAAG,IAAI,CAACxE,wBAAwB;MAChE,MAAMyE,YAAY,GAAG,IAAI,CAAC3E,aAAa;MACvC,MAAM;QAAE3P,KAAK,EAAEuU,YAAY;QAAEtU,MAAM,EAAEuU;MAAc,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAC7E,MAAMC,IAAI,GAAGJ,YAAY,CAACtW,GAAG,GAAGqW,OAAO;MACvC,MAAMM,IAAI,GAAGL,YAAY,CAACvU,MAAM,IAAIyU,aAAa,GAAGH,OAAO,CAAC;MAC5D,MAAMO,IAAI,GAAGN,YAAY,CAACpW,IAAI,GAAGkW,OAAO;MACxC,MAAMS,IAAI,GAAGP,YAAY,CAACxU,KAAK,IAAIyU,YAAY,GAAGH,OAAO,CAAC;MAC1DlU,CAAC,GAAG4U,OAAO,CAAC5U,CAAC,EAAE0U,IAAI,EAAEC,IAAI,CAAC;MAC1B1U,CAAC,GAAG2U,OAAO,CAAC3U,CAAC,EAAEuU,IAAI,EAAEC,IAAI,CAAC;IAC9B;IACA,OAAO;MAAEzU,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAoI,4BAA4BA,CAACwM,qBAAqB,EAAE;IAChD,MAAM;MAAE7U,CAAC;MAAEC;IAAE,CAAC,GAAG4U,qBAAqB;IACtC,MAAM9L,KAAK,GAAG,IAAI,CAACC,sBAAsB;IACzC,MAAM8L,uBAAuB,GAAG,IAAI,CAAChF,qCAAqC;IAC1E;IACA,MAAMiF,OAAO,GAAGhO,IAAI,CAACC,GAAG,CAAChH,CAAC,GAAG8U,uBAAuB,CAAC9U,CAAC,CAAC;IACvD,MAAMgV,OAAO,GAAGjO,IAAI,CAACC,GAAG,CAAC/G,CAAC,GAAG6U,uBAAuB,CAAC7U,CAAC,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAI8U,OAAO,GAAG,IAAI,CAACpQ,OAAO,CAACsQ,+BAA+B,EAAE;MACxDlM,KAAK,CAAC/I,CAAC,GAAGA,CAAC,GAAG8U,uBAAuB,CAAC9U,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD8U,uBAAuB,CAAC9U,CAAC,GAAGA,CAAC;IACjC;IACA,IAAIgV,OAAO,GAAG,IAAI,CAACrQ,OAAO,CAACsQ,+BAA+B,EAAE;MACxDlM,KAAK,CAAC9I,CAAC,GAAGA,CAAC,GAAG6U,uBAAuB,CAAC7U,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD6U,uBAAuB,CAAC7U,CAAC,GAAGA,CAAC;IACjC;IACA,OAAO8I,KAAK;EAChB;EACA;EACAvE,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,CAAC,IAAI,CAACkC,YAAY,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;MACtC;IACJ;IACA,MAAMyQ,YAAY,GAAG,IAAI,CAACzQ,QAAQ,CAACzB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC4E,UAAU,CAAC,CAAC;IACnE,IAAIsN,YAAY,KAAK,IAAI,CAACzP,0BAA0B,EAAE;MAClD,IAAI,CAACA,0BAA0B,GAAGyP,YAAY;MAC9C3X,4BAA4B,CAAC,IAAI,CAACmJ,YAAY,EAAEwO,YAAY,CAAC;IACjE;EACJ;EACA;EACA5K,2BAA2BA,CAAC9M,OAAO,EAAE;IACjCA,OAAO,CAACiV,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACpM,YAAY,EAAEtC,0BAA0B,CAAC;IACvFvG,OAAO,CAACiV,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACpM,YAAY,EAAExC,2BAA2B,CAAC;IACzFrG,OAAO,CAACiV,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACvJ,gBAAgB,EAAEnF,0BAA0B,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;EACI4E,0BAA0BA,CAAC3I,CAAC,EAAEC,CAAC,EAAE;IAC7B,MAAM/B,SAAS,GAAG0T,YAAY,CAAC5R,CAAC,EAAEC,CAAC,CAAC;IACpC,MAAMkV,MAAM,GAAG,IAAI,CAACzO,YAAY,CAAC/I,KAAK;IACtC;IACA;IACA;IACA,IAAI,IAAI,CAAC8M,iBAAiB,IAAI,IAAI,EAAE;MAChC,IAAI,CAACA,iBAAiB,GAClB0K,MAAM,CAACjX,SAAS,IAAIiX,MAAM,CAACjX,SAAS,IAAI,MAAM,GAAGiX,MAAM,CAACjX,SAAS,GAAG,EAAE;IAC9E;IACA;IACA;IACA;IACAiX,MAAM,CAACjX,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAE,IAAI,CAACuM,iBAAiB,CAAC;EAC3E;EACA;AACJ;AACA;AACA;AACA;EACIwG,sBAAsBA,CAACjR,CAAC,EAAEC,CAAC,EAAE;IACzB;IACA;IACA,MAAM9B,gBAAgB,GAAG,IAAI,CAAC+L,gBAAgB,EAAED,QAAQ,GAAGS,SAAS,GAAG,IAAI,CAACD,iBAAiB;IAC7F,MAAMvM,SAAS,GAAG0T,YAAY,CAAC5R,CAAC,EAAEC,CAAC,CAAC;IACpC,IAAI,CAACyM,QAAQ,CAAC/O,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,gBAAgB,CAAC;EAClF;EACA;AACJ;AACA;AACA;EACI2K,gBAAgBA,CAACsM,eAAe,EAAE;IAC9B,MAAMC,cAAc,GAAG,IAAI,CAACpO,qBAAqB;IACjD,IAAIoO,cAAc,EAAE;MAChB,OAAO;QAAErV,CAAC,EAAEoV,eAAe,CAACpV,CAAC,GAAGqV,cAAc,CAACrV,CAAC;QAAEC,CAAC,EAAEmV,eAAe,CAACnV,CAAC,GAAGoV,cAAc,CAACpV;MAAE,CAAC;IAC/F;IACA,OAAO;MAAED,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA;EACAoN,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACoC,aAAa,GAAG,IAAI,CAACO,YAAY,GAAGtF,SAAS;IAClD,IAAI,CAACpB,gBAAgB,CAACtI,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACImK,8BAA8BA,CAAA,EAAG;IAC7B,IAAI;MAAEnL,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAAC8E,iBAAiB;IACrC,IAAK/E,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,IAAK,IAAI,CAAC2H,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAACpC,gBAAgB,EAAE;MACrE;IACJ;IACA;IACA,MAAMwN,WAAW,GAAG,IAAI,CAACtM,YAAY,CAAC/G,qBAAqB,CAAC,CAAC;IAC7D,MAAMyU,YAAY,GAAG,IAAI,CAAC5O,gBAAgB,CAAC7F,qBAAqB,CAAC,CAAC;IAClE;IACA;IACA,IAAKyU,YAAY,CAACtU,KAAK,KAAK,CAAC,IAAIsU,YAAY,CAACrU,MAAM,KAAK,CAAC,IACrDiT,WAAW,CAAClT,KAAK,KAAK,CAAC,IAAIkT,WAAW,CAACjT,MAAM,KAAK,CAAE,EAAE;MACvD;IACJ;IACA,MAAMuV,YAAY,GAAGlB,YAAY,CAACpW,IAAI,GAAGgV,WAAW,CAAChV,IAAI;IACzD,MAAMuX,aAAa,GAAGvC,WAAW,CAACpT,KAAK,GAAGwU,YAAY,CAACxU,KAAK;IAC5D,MAAM4V,WAAW,GAAGpB,YAAY,CAACtW,GAAG,GAAGkV,WAAW,CAAClV,GAAG;IACtD,MAAM2X,cAAc,GAAGzC,WAAW,CAACnT,MAAM,GAAGuU,YAAY,CAACvU,MAAM;IAC/D;IACA;IACA,IAAIuU,YAAY,CAACtU,KAAK,GAAGkT,WAAW,CAAClT,KAAK,EAAE;MACxC,IAAIwV,YAAY,GAAG,CAAC,EAAE;QAClBtV,CAAC,IAAIsV,YAAY;MACrB;MACA,IAAIC,aAAa,GAAG,CAAC,EAAE;QACnBvV,CAAC,IAAIuV,aAAa;MACtB;IACJ,CAAC,MACI;MACDvV,CAAC,GAAG,CAAC;IACT;IACA;IACA;IACA,IAAIoU,YAAY,CAACrU,MAAM,GAAGiT,WAAW,CAACjT,MAAM,EAAE;MAC1C,IAAIyV,WAAW,GAAG,CAAC,EAAE;QACjBvV,CAAC,IAAIuV,WAAW;MACpB;MACA,IAAIC,cAAc,GAAG,CAAC,EAAE;QACpBxV,CAAC,IAAIwV,cAAc;MACvB;IACJ,CAAC,MACI;MACDxV,CAAC,GAAG,CAAC;IACT;IACA,IAAID,CAAC,KAAK,IAAI,CAAC+E,iBAAiB,CAAC/E,CAAC,IAAIC,CAAC,KAAK,IAAI,CAAC8E,iBAAiB,CAAC9E,CAAC,EAAE;MAClE,IAAI,CAACqM,mBAAmB,CAAC;QAAErM,CAAC;QAAED;MAAE,CAAC,CAAC;IACtC;EACJ;EACA;EACAyH,kBAAkBA,CAAC/F,KAAK,EAAE;IACtB,MAAMvE,KAAK,GAAG,IAAI,CAACyI,cAAc;IACjC,IAAI,OAAOzI,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOA,KAAK;IAChB,CAAC,MACI,IAAIoQ,YAAY,CAAC7L,KAAK,CAAC,EAAE;MAC1B,OAAOvE,KAAK,CAACuY,KAAK;IACtB;IACA,OAAOvY,KAAK,GAAGA,KAAK,CAACwY,KAAK,GAAG,CAAC;EAClC;EACA;EACAnG,eAAeA,CAAC9N,KAAK,EAAE;IACnB,MAAMkU,gBAAgB,GAAG,IAAI,CAACtM,gBAAgB,CAAC7H,YAAY,CAACC,KAAK,CAAC;IAClE,IAAIkU,gBAAgB,EAAE;MAClB,MAAMjU,MAAM,GAAGpG,eAAe,CAACmG,KAAK,CAAC;MACrC;MACA;MACA,IAAI,IAAI,CAAC+N,aAAa,IAClB9N,MAAM,KAAK,IAAI,CAAC6D,gBAAgB,IAChC7D,MAAM,CAACS,QAAQ,CAAC,IAAI,CAACoD,gBAAgB,CAAC,EAAE;QACxCrF,gBAAgB,CAAC,IAAI,CAACsP,aAAa,EAAEmG,gBAAgB,CAAC9X,GAAG,EAAE8X,gBAAgB,CAAC5X,IAAI,CAAC;MACrF;MACA,IAAI,CAACiJ,qBAAqB,CAACjH,CAAC,IAAI4V,gBAAgB,CAAC5X,IAAI;MACrD,IAAI,CAACiJ,qBAAqB,CAAChH,CAAC,IAAI2V,gBAAgB,CAAC9X,GAAG;MACpD;MACA;MACA,IAAI,CAAC,IAAI,CAACwG,cAAc,EAAE;QACtB,IAAI,CAACU,gBAAgB,CAAChF,CAAC,IAAI4V,gBAAgB,CAAC5X,IAAI;QAChD,IAAI,CAACgH,gBAAgB,CAAC/E,CAAC,IAAI2V,gBAAgB,CAAC9X,GAAG;QAC/C,IAAI,CAAC6K,0BAA0B,CAAC,IAAI,CAAC3D,gBAAgB,CAAChF,CAAC,EAAE,IAAI,CAACgF,gBAAgB,CAAC/E,CAAC,CAAC;MACrF;IACJ;EACJ;EACA;EACAoT,0BAA0BA,CAAA,EAAG;IACzB,OAAQ,IAAI,CAAC/J,gBAAgB,CAACxI,SAAS,CAACe,GAAG,CAAC,IAAI,CAAChB,SAAS,CAAC,EAAEO,cAAc,IACvE,IAAI,CAACkI,gBAAgB,CAACjI,yBAAyB,CAAC,CAAC;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5F,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACoa,iBAAiB,KAAKnL,SAAS,EAAE;MACtC,IAAI,CAACmL,iBAAiB,GAAGpa,cAAc,CAAC,IAAI,CAACiL,YAAY,CAAC;IAC9D;IACA,OAAO,IAAI,CAACmP,iBAAiB;EACjC;EACA;EACAxH,yBAAyBA,CAACyH,aAAa,EAAE/H,UAAU,EAAE;IACjD,MAAMgI,gBAAgB,GAAG,IAAI,CAACvJ,iBAAiB,IAAI,QAAQ;IAC3D,IAAIuJ,gBAAgB,KAAK,QAAQ,EAAE;MAC/B,OAAOD,aAAa;IACxB;IACA,IAAIC,gBAAgB,KAAK,QAAQ,EAAE;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACnV,SAAS;MAClC;MACA;MACA;MACA,OAAQkN,UAAU,IACdiI,WAAW,CAACC,iBAAiB,IAC7BD,WAAW,CAACE,uBAAuB,IACnCF,WAAW,CAACG,oBAAoB,IAChCH,WAAW,CAACI,mBAAmB,IAC/BJ,WAAW,CAAC9H,IAAI;IACxB;IACA,OAAOvS,aAAa,CAACoa,gBAAgB,CAAC;EAC1C;EACA;EACAxB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAC,IAAI,CAACvE,YAAY,IAAK,CAAC,IAAI,CAACA,YAAY,CAAClQ,KAAK,IAAI,CAAC,IAAI,CAACkQ,YAAY,CAACjQ,MAAO,EAAE;MAC/E,IAAI,CAACiQ,YAAY,GAAG,IAAI,CAACtD,QAAQ,GAC3B,IAAI,CAACA,QAAQ,CAAC/M,qBAAqB,CAAC,CAAC,GACrC,IAAI,CAAC8I,kBAAkB;IACjC;IACA,OAAO,IAAI,CAACuH,YAAY;EAC5B;EACA;EACAxJ,gBAAgBA,CAAC9E,KAAK,EAAE;IACpB,OAAO,IAAI,CAAC+C,QAAQ,CAAC1F,IAAI,CAAC2F,MAAM,IAAI;MAChC,OAAOhD,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACC,MAAM,KAAK+C,MAAM,IAAIA,MAAM,CAACtC,QAAQ,CAACV,KAAK,CAACC,MAAM,CAAC,CAAC;IACrF,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASiQ,YAAYA,CAAC5R,CAAC,EAAEC,CAAC,EAAE;EACxB;EACA;EACA,OAAQ,eAAc8G,IAAI,CAACsP,KAAK,CAACrW,CAAC,CAAE,OAAM+G,IAAI,CAACsP,KAAK,CAACpW,CAAC,CAAE,QAAO;AACnE;AACA;AACA,SAAS2U,OAAOA,CAACzX,KAAK,EAAEmZ,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAOxP,IAAI,CAACwP,GAAG,CAACD,GAAG,EAAEvP,IAAI,CAACuP,GAAG,CAACC,GAAG,EAAEpZ,KAAK,CAAC,CAAC;AAC9C;AACA;AACA,SAASoQ,YAAYA,CAAC7L,KAAK,EAAE;EACzB;EACA;EACA;EACA,OAAOA,KAAK,CAAC+B,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,SAASiO,WAAWA,CAACJ,OAAO,EAAEzQ,SAAS,EAAE;EACrC,MAAM2V,SAAS,GAAGlF,OAAO,CAACkF,SAAS;EACnC,IAAIA,SAAS,CAACxT,MAAM,KAAK,CAAC,IAAIwT,SAAS,CAAC,CAAC,CAAC,CAACC,QAAQ,KAAK5V,SAAS,CAAC6V,YAAY,EAAE;IAC5E,OAAOF,SAAS,CAAC,CAAC,CAAC;EACvB;EACA,MAAMG,OAAO,GAAG9V,SAAS,CAAC+V,aAAa,CAAC,KAAK,CAAC;EAC9CJ,SAAS,CAAClV,OAAO,CAACa,IAAI,IAAIwU,OAAO,CAACxI,WAAW,CAAChM,IAAI,CAAC,CAAC;EACpD,OAAOwU,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,SAAShF,gBAAgBA,CAAChQ,MAAM,EAAEkV,UAAU,EAAE;EAC1ClV,MAAM,CAAChE,KAAK,CAACmC,KAAK,GAAI,GAAE+W,UAAU,CAAC/W,KAAM,IAAG;EAC5C6B,MAAM,CAAChE,KAAK,CAACoC,MAAM,GAAI,GAAE8W,UAAU,CAAC9W,MAAO,IAAG;EAC9C4B,MAAM,CAAChE,KAAK,CAACO,SAAS,GAAG0T,YAAY,CAACiF,UAAU,CAAC7Y,IAAI,EAAE6Y,UAAU,CAAC/Y,GAAG,CAAC;AAC1E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgZ,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAChD,MAAMC,IAAI,GAAGC,KAAK,CAACH,SAAS,EAAED,KAAK,CAAC/T,MAAM,GAAG,CAAC,CAAC;EAC/C,MAAMoU,EAAE,GAAGD,KAAK,CAACF,OAAO,EAAEF,KAAK,CAAC/T,MAAM,GAAG,CAAC,CAAC;EAC3C,IAAIkU,IAAI,KAAKE,EAAE,EAAE;IACb;EACJ;EACA,MAAMzV,MAAM,GAAGoV,KAAK,CAACG,IAAI,CAAC;EAC1B,MAAMnO,KAAK,GAAGqO,EAAE,GAAGF,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EAChC,KAAK,IAAInU,CAAC,GAAGmU,IAAI,EAAEnU,CAAC,KAAKqU,EAAE,EAAErU,CAAC,IAAIgG,KAAK,EAAE;IACrCgO,KAAK,CAAChU,CAAC,CAAC,GAAGgU,KAAK,CAAChU,CAAC,GAAGgG,KAAK,CAAC;EAC/B;EACAgO,KAAK,CAACK,EAAE,CAAC,GAAGzV,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0V,iBAAiBA,CAACC,YAAY,EAAEC,WAAW,EAAEtH,YAAY,EAAEuH,WAAW,EAAE;EAC7E,MAAMN,IAAI,GAAGC,KAAK,CAAClH,YAAY,EAAEqH,YAAY,CAACtU,MAAM,GAAG,CAAC,CAAC;EACzD,MAAMoU,EAAE,GAAGD,KAAK,CAACK,WAAW,EAAED,WAAW,CAACvU,MAAM,CAAC;EACjD,IAAIsU,YAAY,CAACtU,MAAM,EAAE;IACrBuU,WAAW,CAACE,MAAM,CAACL,EAAE,EAAE,CAAC,EAAEE,YAAY,CAACG,MAAM,CAACP,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,aAAaA,CAACJ,YAAY,EAAEC,WAAW,EAAEtH,YAAY,EAAEuH,WAAW,EAAE;EACzE,MAAMJ,EAAE,GAAGD,KAAK,CAACK,WAAW,EAAED,WAAW,CAACvU,MAAM,CAAC;EACjD,IAAIsU,YAAY,CAACtU,MAAM,EAAE;IACrBuU,WAAW,CAACE,MAAM,CAACL,EAAE,EAAE,CAAC,EAAEE,YAAY,CAACrH,YAAY,CAAC,CAAC;EACzD;AACJ;AACA;AACA,SAASkH,KAAKA,CAACha,KAAK,EAAEoZ,GAAG,EAAE;EACvB,OAAOxP,IAAI,CAACwP,GAAG,CAAC,CAAC,EAAExP,IAAI,CAACuP,GAAG,CAACC,GAAG,EAAEpZ,KAAK,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwa,sBAAsB,CAAC;EACzB/W,WAAWA,CAACgX,QAAQ,EAAE9S,iBAAiB,EAAE;IACrC,IAAI,CAAC8S,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9S,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAAC+S,cAAc,GAAG,EAAE;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,UAAU;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG;MACjBC,IAAI,EAAE,IAAI;MACVjP,KAAK,EAAE,CAAC;MACRkP,QAAQ,EAAE;IACd,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI3J,KAAKA,CAAC4J,KAAK,EAAE;IACT,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,IAAIA,CAAChI,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6X,YAAY,EAAE;IACzC,MAAMC,QAAQ,GAAG,IAAI,CAACT,cAAc;IACpC,MAAMU,QAAQ,GAAG,IAAI,CAACC,gCAAgC,CAACpI,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6X,YAAY,CAAC;IAC9F,IAAIE,QAAQ,KAAK,CAAC,CAAC,IAAID,QAAQ,CAACtV,MAAM,GAAG,CAAC,EAAE;MACxC,OAAO,IAAI;IACf;IACA,MAAMyV,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAM7H,YAAY,GAAGqI,QAAQ,CAACI,SAAS,CAACC,WAAW,IAAIA,WAAW,CAACX,IAAI,KAAK5H,IAAI,CAAC;IACjF,MAAMwI,oBAAoB,GAAGN,QAAQ,CAACC,QAAQ,CAAC;IAC/C,MAAMnD,eAAe,GAAGkD,QAAQ,CAACrI,YAAY,CAAC,CAACvQ,UAAU;IACzD,MAAMmZ,WAAW,GAAGD,oBAAoB,CAAClZ,UAAU;IACnD,MAAMqJ,KAAK,GAAGkH,YAAY,GAAGsI,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA,MAAMO,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC3D,eAAe,EAAEyD,WAAW,EAAE9P,KAAK,CAAC;IAC7E;IACA,MAAMiQ,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAAChJ,YAAY,EAAEqI,QAAQ,EAAEvP,KAAK,CAAC;IAC7E;IACA;IACA,MAAMmQ,QAAQ,GAAGZ,QAAQ,CAACa,KAAK,CAAC,CAAC;IACjC;IACArC,eAAe,CAACwB,QAAQ,EAAErI,YAAY,EAAEsI,QAAQ,CAAC;IACjDD,QAAQ,CAAChX,OAAO,CAAC,CAAC8X,OAAO,EAAEC,KAAK,KAAK;MACjC;MACA,IAAIH,QAAQ,CAACG,KAAK,CAAC,KAAKD,OAAO,EAAE;QAC7B;MACJ;MACA,MAAME,aAAa,GAAGF,OAAO,CAACpB,IAAI,KAAK5H,IAAI;MAC3C,MAAM7H,MAAM,GAAG+Q,aAAa,GAAGR,UAAU,GAAGE,aAAa;MACzD,MAAMO,eAAe,GAAGD,aAAa,GAC/BlJ,IAAI,CAAC5G,qBAAqB,CAAC,CAAC,GAC5B4P,OAAO,CAACpB,IAAI,CAACtO,cAAc,CAAC,CAAC;MACnC;MACA0P,OAAO,CAAC7Q,MAAM,IAAIA,MAAM;MACxB;MACA;MACA;MACA;MACA,IAAIkQ,YAAY,EAAE;QACd;QACA;QACAc,eAAe,CAAC5b,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAAE,eAAc8I,IAAI,CAACsP,KAAK,CAAC+C,OAAO,CAAC7Q,MAAM,CAAE,WAAU,EAAE6Q,OAAO,CAACjb,gBAAgB,CAAC;QACnIgC,gBAAgB,CAACiZ,OAAO,CAAC1Z,UAAU,EAAE,CAAC,EAAE6I,MAAM,CAAC;MACnD,CAAC,MACI;QACDgR,eAAe,CAAC5b,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAAE,kBAAiB8I,IAAI,CAACsP,KAAK,CAAC+C,OAAO,CAAC7Q,MAAM,CAAE,QAAO,EAAE6Q,OAAO,CAACjb,gBAAgB,CAAC;QACnIgC,gBAAgB,CAACiZ,OAAO,CAAC1Z,UAAU,EAAE6I,MAAM,EAAE,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACwP,aAAa,CAACE,QAAQ,GAAG/X,kBAAkB,CAAC2Y,WAAW,EAAEtY,QAAQ,EAAEC,QAAQ,CAAC;IACjF,IAAI,CAACuX,aAAa,CAACC,IAAI,GAAGY,oBAAoB,CAACZ,IAAI;IACnD,IAAI,CAACD,aAAa,CAAChP,KAAK,GAAG0P,YAAY,GAAGJ,YAAY,CAACrY,CAAC,GAAGqY,YAAY,CAACpY,CAAC;IACzE,OAAO;MAAEoQ,aAAa,EAAEJ,YAAY;MAAEA,YAAY,EAAEsI;IAAS,CAAC;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI1H,KAAKA,CAACT,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6Y,KAAK,EAAE;IACnC,MAAMd,QAAQ,GAAGc,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC;IACrC;IACE;IACA,IAAI,CAACb,gCAAgC,CAACpI,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,CAAC,GACjE6Y,KAAK;IACX,MAAMG,gBAAgB,GAAG,IAAI,CAACC,iBAAiB;IAC/C,MAAMxJ,YAAY,GAAGuJ,gBAAgB,CAACjb,OAAO,CAAC6R,IAAI,CAAC;IACnD,MAAMzC,WAAW,GAAGyC,IAAI,CAAC5G,qBAAqB,CAAC,CAAC;IAChD,IAAIkQ,oBAAoB,GAAGF,gBAAgB,CAACjB,QAAQ,CAAC;IACrD;IACA;IACA;IACA,IAAImB,oBAAoB,KAAKtJ,IAAI,EAAE;MAC/BsJ,oBAAoB,GAAGF,gBAAgB,CAACjB,QAAQ,GAAG,CAAC,CAAC;IACzD;IACA;IACA;IACA,IAAI,CAACmB,oBAAoB,KACpBnB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,GAAGiB,gBAAgB,CAACxW,MAAM,GAAG,CAAC,CAAC,IAC/E,IAAI,CAAC2W,wBAAwB,CAACpZ,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MACnDkZ,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAC9C;IACA;IACA;IACA,IAAIvJ,YAAY,GAAG,CAAC,CAAC,EAAE;MACnBuJ,gBAAgB,CAAC/B,MAAM,CAACxH,YAAY,EAAE,CAAC,CAAC;IAC5C;IACA;IACA;IACA,IAAIyJ,oBAAoB,IAAI,CAAC,IAAI,CAAC5U,iBAAiB,CAAC8C,UAAU,CAAC8R,oBAAoB,CAAC,EAAE;MAClF,MAAMlc,OAAO,GAAGkc,oBAAoB,CAAChQ,cAAc,CAAC,CAAC;MACrDlM,OAAO,CAACoc,aAAa,CAAC5L,YAAY,CAACL,WAAW,EAAEnQ,OAAO,CAAC;MACxDgc,gBAAgB,CAAC/B,MAAM,CAACc,QAAQ,EAAE,CAAC,EAAEnI,IAAI,CAAC;IAC9C,CAAC,MACI;MACDzU,aAAa,CAAC,IAAI,CAACic,QAAQ,CAAC,CAACzJ,WAAW,CAACR,WAAW,CAAC;MACrD6L,gBAAgB,CAACK,IAAI,CAACzJ,IAAI,CAAC;IAC/B;IACA;IACAzC,WAAW,CAAChQ,KAAK,CAACO,SAAS,GAAG,EAAE;IAChC;IACA;IACA;IACA,IAAI,CAAC4b,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACA3B,SAASA,CAACD,KAAK,EAAE;IACb,IAAI,CAACuB,iBAAiB,GAAGvB,KAAK,CAACiB,KAAK,CAAC,CAAC;IACtC,IAAI,CAACW,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAC,iBAAiBA,CAACC,SAAS,EAAE;IACzB,IAAI,CAACC,cAAc,GAAGD,SAAS;EACnC;EACA;EACAlO,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,CAAC2N,iBAAiB,CAACnY,OAAO,CAAC8O,IAAI,IAAI;MACnC,MAAM/F,WAAW,GAAG+F,IAAI,CAAC1G,cAAc,CAAC,CAAC;MACzC,IAAIW,WAAW,EAAE;QACb,MAAMlM,gBAAgB,GAAG,IAAI,CAAC0Z,cAAc,CAAC9Y,IAAI,CAACmb,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAK5H,IAAI,CAAC,EAAEjS,gBAAgB;QACzFkM,WAAW,CAAC1M,KAAK,CAACO,SAAS,GAAGC,gBAAgB,IAAI,EAAE;MACxD;IACJ,CAAC,CAAC;IACF,IAAI,CAAC0Z,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC4B,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC1B,aAAa,CAACC,IAAI,GAAG,IAAI;IAC9B,IAAI,CAACD,aAAa,CAAChP,KAAK,GAAG,CAAC;IAC5B,IAAI,CAACgP,aAAa,CAACE,QAAQ,GAAG,KAAK;EACvC;EACA;AACJ;AACA;AACA;EACIkC,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACV,iBAAiB;EACjC;EACA;EACAhL,YAAYA,CAAC2B,IAAI,EAAE;IACf;IACA;IACA;IACA,MAAM8H,KAAK,GAAG,IAAI,CAACJ,WAAW,KAAK,YAAY,IAAI,IAAI,CAAC3L,SAAS,KAAK,KAAK,GACrE,IAAI,CAAC0L,cAAc,CAACsB,KAAK,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC,GACrC,IAAI,CAACvC,cAAc;IACzB,OAAOK,KAAK,CAACQ,SAAS,CAACC,WAAW,IAAIA,WAAW,CAACX,IAAI,KAAK5H,IAAI,CAAC;EACpE;EACA;EACAiK,cAAcA,CAACpY,aAAa,EAAEC,cAAc,EAAE;IAC1C;IACA;IACA;IACA;IACA,IAAI,CAAC2V,cAAc,CAACvW,OAAO,CAAC,CAAC;MAAE5B;IAAW,CAAC,KAAK;MAC5CS,gBAAgB,CAACT,UAAU,EAAEuC,aAAa,EAAEC,cAAc,CAAC;IAC/D,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC2V,cAAc,CAACvW,OAAO,CAAC,CAAC;MAAE0W;IAAK,CAAC,KAAK;MACtC,IAAI,IAAI,CAAClT,iBAAiB,CAAC8C,UAAU,CAACoQ,IAAI,CAAC,EAAE;QACzC;QACA;QACAA,IAAI,CAACvL,4BAA4B,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;EACAqN,mBAAmBA,CAAA,EAAG;IAClB,MAAMrB,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,IAAI,CAACD,cAAc,GAAG,IAAI,CAAC4B,iBAAiB,CACvCjd,GAAG,CAACwb,IAAI,IAAI;MACb,MAAMsC,gBAAgB,GAAGtC,IAAI,CAACrO,iBAAiB,CAAC,CAAC;MACjD,OAAO;QACHqO,IAAI;QACJzP,MAAM,EAAE,CAAC;QACTpK,gBAAgB,EAAEmc,gBAAgB,CAAC3c,KAAK,CAACO,SAAS,IAAI,EAAE;QACxDwB,UAAU,EAAED,oBAAoB,CAAC6a,gBAAgB;MACrD,CAAC;IACL,CAAC,CAAC,CACGlC,IAAI,CAAC,CAACmC,CAAC,EAAEC,CAAC,KAAK;MAChB,OAAO/B,YAAY,GACb8B,CAAC,CAAC7a,UAAU,CAAC1B,IAAI,GAAGwc,CAAC,CAAC9a,UAAU,CAAC1B,IAAI,GACrCuc,CAAC,CAAC7a,UAAU,CAAC5B,GAAG,GAAG0c,CAAC,CAAC9a,UAAU,CAAC5B,GAAG;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIib,gBAAgBA,CAAC3D,eAAe,EAAEyD,WAAW,EAAE9P,KAAK,EAAE;IAClD,MAAM0P,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,IAAIgB,UAAU,GAAGL,YAAY,GACvBI,WAAW,CAAC7a,IAAI,GAAGoX,eAAe,CAACpX,IAAI,GACvC6a,WAAW,CAAC/a,GAAG,GAAGsX,eAAe,CAACtX,GAAG;IAC3C;IACA,IAAIiL,KAAK,KAAK,CAAC,CAAC,EAAE;MACd+P,UAAU,IAAIL,YAAY,GACpBI,WAAW,CAAC/Y,KAAK,GAAGsV,eAAe,CAACtV,KAAK,GACzC+Y,WAAW,CAAC9Y,MAAM,GAAGqV,eAAe,CAACrV,MAAM;IACrD;IACA,OAAO+Y,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,mBAAmBA,CAAChJ,YAAY,EAAEqI,QAAQ,EAAEvP,KAAK,EAAE;IAC/C,MAAM0P,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAM1C,eAAe,GAAGkD,QAAQ,CAACrI,YAAY,CAAC,CAACvQ,UAAU;IACzD,MAAM+a,gBAAgB,GAAGnC,QAAQ,CAACrI,YAAY,GAAGlH,KAAK,GAAG,CAAC,CAAC,CAAC;IAC5D,IAAIiQ,aAAa,GAAG5D,eAAe,CAACqD,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG1P,KAAK;IAC9E,IAAI0R,gBAAgB,EAAE;MAClB,MAAMnM,KAAK,GAAGmK,YAAY,GAAG,MAAM,GAAG,KAAK;MAC3C,MAAMiC,GAAG,GAAGjC,YAAY,GAAG,OAAO,GAAG,QAAQ;MAC7C;MACA;MACA;MACA;MACA,IAAI1P,KAAK,KAAK,CAAC,CAAC,EAAE;QACdiQ,aAAa,IAAIyB,gBAAgB,CAAC/a,UAAU,CAAC4O,KAAK,CAAC,GAAG8G,eAAe,CAACsF,GAAG,CAAC;MAC9E,CAAC,MACI;QACD1B,aAAa,IAAI5D,eAAe,CAAC9G,KAAK,CAAC,GAAGmM,gBAAgB,CAAC/a,UAAU,CAACgb,GAAG,CAAC;MAC9E;IACJ;IACA,OAAO1B,aAAa;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIW,wBAAwBA,CAACpZ,QAAQ,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAAC,IAAI,CAACiZ,iBAAiB,CAACzW,MAAM,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,MAAM2X,aAAa,GAAG,IAAI,CAAC9C,cAAc;IACzC,MAAMY,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD;IACA;IACA,MAAM8C,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC3C,IAAI,KAAK,IAAI,CAACyB,iBAAiB,CAAC,CAAC,CAAC;IACpE,IAAImB,QAAQ,EAAE;MACV,MAAMC,YAAY,GAAGF,aAAa,CAACA,aAAa,CAAC3X,MAAM,GAAG,CAAC,CAAC,CAACtD,UAAU;MACvE,OAAO+Y,YAAY,GAAGlY,QAAQ,IAAIsa,YAAY,CAACjb,KAAK,GAAGY,QAAQ,IAAIqa,YAAY,CAAChb,MAAM;IAC1F,CAAC,MACI;MACD,MAAMib,aAAa,GAAGH,aAAa,CAAC,CAAC,CAAC,CAACjb,UAAU;MACjD,OAAO+Y,YAAY,GAAGlY,QAAQ,IAAIua,aAAa,CAAC9c,IAAI,GAAGwC,QAAQ,IAAIsa,aAAa,CAAChd,GAAG;IACxF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0a,gCAAgCA,CAACpI,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAEuI,KAAK,EAAE;IAC9D,MAAM0P,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMuB,KAAK,GAAG,IAAI,CAACxB,cAAc,CAACa,SAAS,CAAC,CAAC;MAAEV,IAAI;MAAEtY;IAAW,CAAC,KAAK;MAClE;MACA,IAAIsY,IAAI,KAAK5H,IAAI,EAAE;QACf,OAAO,KAAK;MAChB;MACA,IAAIrH,KAAK,EAAE;QACP,MAAMoD,SAAS,GAAGsM,YAAY,GAAG1P,KAAK,CAAC/I,CAAC,GAAG+I,KAAK,CAAC9I,CAAC;QAClD;QACA;QACA;QACA,IAAI+X,IAAI,KAAK,IAAI,CAACD,aAAa,CAACC,IAAI,IAChC,IAAI,CAACD,aAAa,CAACE,QAAQ,IAC3B9L,SAAS,KAAK,IAAI,CAAC4L,aAAa,CAAChP,KAAK,EAAE;UACxC,OAAO,KAAK;QAChB;MACJ;MACA,OAAO0P,YAAY;MACb;MACE;MACAlY,QAAQ,IAAIwG,IAAI,CAACgU,KAAK,CAACrb,UAAU,CAAC1B,IAAI,CAAC,IAAIuC,QAAQ,GAAGwG,IAAI,CAACgU,KAAK,CAACrb,UAAU,CAACE,KAAK,CAAC,GACpFY,QAAQ,IAAIuG,IAAI,CAACgU,KAAK,CAACrb,UAAU,CAAC5B,GAAG,CAAC,IAAI0C,QAAQ,GAAGuG,IAAI,CAACgU,KAAK,CAACrb,UAAU,CAACG,MAAM,CAAC;IAC5F,CAAC,CAAC;IACF,OAAOwZ,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACY,cAAc,CAACZ,KAAK,EAAEjJ,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGiJ,KAAK;EACzE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2B,wBAAwB,GAAG,IAAI;AACrC;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAI;AACvC;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdta,WAAWA,CAACpD,OAAO,EAAEsH,iBAAiB,EAAEjE,SAAS,EAAE+D,OAAO,EAAEC,cAAc,EAAE;IACxE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACT,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC0M,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACqK,kBAAkB,GAAG,KAAK;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,MAAM,IAAI;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,MAAM,IAAI;IAC/B;IACA,IAAI,CAACzV,aAAa,GAAG,IAAI7J,OAAO,CAAC,CAAC;IAClC;AACR;AACA;IACQ,IAAI,CAACiK,OAAO,GAAG,IAAIjK,OAAO,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACkK,MAAM,GAAG,IAAIlK,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACmK,OAAO,GAAG,IAAInK,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACuf,MAAM,GAAG,IAAIvf,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACwf,gBAAgB,GAAG,IAAIxf,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAACyf,gBAAgB,GAAG,IAAIzf,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAAC0f,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI3X,GAAG,CAAC,CAAC;IAChC;IACA,IAAI,CAAC4X,2BAA2B,GAAG7f,YAAY,CAACmJ,KAAK;IACrD;IACA,IAAI,CAAC2W,wBAAwB,GAAG,CAAC,CAAC;IAClC;IACA,IAAI,CAACC,0BAA0B,GAAG,CAAC,CAAC;IACpC;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIjgB,OAAO,CAAC,CAAC;IACtC;IACA,IAAI,CAAC6Z,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACqG,oBAAoB,GAAG,MAAM;MAC9B,IAAI,CAACjP,cAAc,CAAC,CAAC;MACrB/Q,QAAQ,CAAC,CAAC,EAAEC,uBAAuB,CAAC,CAC/BggB,IAAI,CAAC7f,SAAS,CAAC,IAAI,CAAC2f,iBAAiB,CAAC,CAAC,CACvC/Q,SAAS,CAAC,MAAM;QACjB,MAAM/I,IAAI,GAAG,IAAI,CAACia,WAAW;QAC7B,MAAMC,UAAU,GAAG,IAAI,CAACjB,cAAc;QACtC,IAAI,IAAI,CAACW,wBAAwB,KAAK,CAAC,CAAC,sCAAsC;UAC1E5Z,IAAI,CAACma,QAAQ,CAAC,CAAC,EAAE,CAACD,UAAU,CAAC;QACjC,CAAC,MACI,IAAI,IAAI,CAACN,wBAAwB,KAAK,CAAC,CAAC,wCAAwC;UACjF5Z,IAAI,CAACma,QAAQ,CAAC,CAAC,EAAED,UAAU,CAAC;QAChC;QACA,IAAI,IAAI,CAACL,0BAA0B,KAAK,CAAC,CAAC,0CAA0C;UAChF7Z,IAAI,CAACma,QAAQ,CAAC,CAACD,UAAU,EAAE,CAAC,CAAC;QACjC,CAAC,MACI,IAAI,IAAI,CAACL,0BAA0B,KAAK,CAAC,CAAC,2CAA2C;UACtF7Z,IAAI,CAACma,QAAQ,CAACD,UAAU,EAAE,CAAC,CAAC;QAChC;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC7e,OAAO,GAAG7B,aAAa,CAAC6B,OAAO,CAAC;IACrC,IAAI,CAACqD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC0b,qBAAqB,CAAC,CAAC,IAAI,CAAC/e,OAAO,CAAC,CAAC;IAC1CsH,iBAAiB,CAAC0X,qBAAqB,CAAC,IAAI,CAAC;IAC7C,IAAI,CAAClT,gBAAgB,GAAG,IAAI3I,qBAAqB,CAACE,SAAS,CAAC;IAC5D,IAAI,CAAC4b,aAAa,GAAG,IAAI9E,sBAAsB,CAAC,IAAI,CAACna,OAAO,EAAEsH,iBAAiB,CAAC;IAChF,IAAI,CAAC2X,aAAa,CAAC1C,iBAAiB,CAAC,CAACV,KAAK,EAAEjJ,IAAI,KAAK,IAAI,CAACkL,aAAa,CAACjC,KAAK,EAAEjJ,IAAI,EAAE,IAAI,CAAC,CAAC;EAChG;EACA;EACA9E,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC2B,cAAc,CAAC,CAAC;IACrB,IAAI,CAACgP,iBAAiB,CAACpQ,QAAQ,CAAC,CAAC;IACjC,IAAI,CAACiQ,2BAA2B,CAAC9Q,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACnF,aAAa,CAACgG,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC5F,OAAO,CAAC4F,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC3F,MAAM,CAAC2F,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC1F,OAAO,CAAC0F,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC0P,MAAM,CAAC1P,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC2P,gBAAgB,CAAC3P,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAC4P,gBAAgB,CAAC5P,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACgQ,eAAe,CAAC7a,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACob,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC9S,gBAAgB,CAACtI,KAAK,CAAC,CAAC;IAC7B,IAAI,CAAC8D,iBAAiB,CAAC4X,mBAAmB,CAAC,IAAI,CAAC;EACpD;EACA;EACA9U,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC8T,WAAW;EAC3B;EACA;EACApN,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACqO,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI/L,KAAKA,CAACT,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6Y,KAAK,EAAE;IACnC,IAAI,CAACsD,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,IAAItD,KAAK,IAAI,IAAI,IAAI,IAAI,CAACvI,eAAe,EAAE;MACvCuI,KAAK,GAAG,IAAI,CAACsC,WAAW,CAACpd,OAAO,CAAC6R,IAAI,CAAC;IAC1C;IACA,IAAI,CAACqM,aAAa,CAAC5L,KAAK,CAACT,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6Y,KAAK,CAAC;IACzD;IACA;IACA,IAAI,CAACwD,qBAAqB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACD,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAC3W,OAAO,CAACK,IAAI,CAAC;MAAE8J,IAAI;MAAE1I,SAAS,EAAE,IAAI;MAAEuI,YAAY,EAAE,IAAI,CAACxB,YAAY,CAAC2B,IAAI;IAAE,CAAC,CAAC;EACvF;EACA;AACJ;AACA;AACA;EACIQ,IAAIA,CAACR,IAAI,EAAE;IACP,IAAI,CAAC0M,MAAM,CAAC,CAAC;IACb,IAAI,CAAC5W,MAAM,CAACI,IAAI,CAAC;MAAE8J,IAAI;MAAE1I,SAAS,EAAE;IAAK,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6I,IAAIA,CAACH,IAAI,EAAEH,YAAY,EAAEI,aAAa,EAAEC,iBAAiB,EAAEJ,sBAAsB,EAAErH,QAAQ,EAAEyE,SAAS,EAAE5L,KAAK,GAAG,CAAC,CAAC,EAAE;IAChH,IAAI,CAACob,MAAM,CAAC,CAAC;IACb,IAAI,CAAC3W,OAAO,CAACG,IAAI,CAAC;MACd8J,IAAI;MACJH,YAAY;MACZI,aAAa;MACb3I,SAAS,EAAE,IAAI;MACf4I,iBAAiB;MACjBJ,sBAAsB;MACtBrH,QAAQ;MACRyE,SAAS;MACT5L;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIyW,SAASA,CAACD,KAAK,EAAE;IACb,MAAM6E,aAAa,GAAG,IAAI,CAACpB,WAAW;IACtC,IAAI,CAACA,WAAW,GAAGzD,KAAK;IACxBA,KAAK,CAAC5W,OAAO,CAAC8O,IAAI,IAAIA,IAAI,CAAChE,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,IAAI,CAACxE,UAAU,CAAC,CAAC,EAAE;MACnB,MAAMoV,YAAY,GAAGD,aAAa,CAACE,MAAM,CAAC7M,IAAI,IAAIA,IAAI,CAACxI,UAAU,CAAC,CAAC,CAAC;MACpE;MACA;MACA,IAAIoV,YAAY,CAACE,KAAK,CAAC9M,IAAI,IAAI8H,KAAK,CAAC3Z,OAAO,CAAC6R,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACxD,IAAI,CAAC0M,MAAM,CAAC,CAAC;MACjB,CAAC,MACI;QACD,IAAI,CAACL,aAAa,CAACtE,SAAS,CAAC,IAAI,CAACwD,WAAW,CAAC;MAClD;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAzP,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACsQ,aAAa,CAACtQ,SAAS,GAAGA,SAAS;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIgR,WAAWA,CAACA,WAAW,EAAE;IACrB,IAAI,CAACvB,SAAS,GAAGuB,WAAW,CAAChE,KAAK,CAAC,CAAC;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIiE,eAAeA,CAACtF,WAAW,EAAE;IACzB;IACA;IACA,IAAI,CAAC2E,aAAa,CAAC3E,WAAW,GAAGA,WAAW;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIyE,qBAAqBA,CAACrb,QAAQ,EAAE;IAC5B,MAAM1D,OAAO,GAAG7B,aAAa,CAAC,IAAI,CAAC6B,OAAO,CAAC;IAC3C;IACA;IACA,IAAI,CAAC6f,mBAAmB,GACpBnc,QAAQ,CAAC3C,OAAO,CAACf,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAACA,OAAO,EAAE,GAAG0D,QAAQ,CAAC,GAAGA,QAAQ,CAACiY,KAAK,CAAC,CAAC;IAChF,OAAO,IAAI;EACf;EACA;EACAzK,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC2O,mBAAmB;EACnC;EACA;AACJ;AACA;AACA;EACI5O,YAAYA,CAAC2B,IAAI,EAAE;IACf,OAAO,IAAI,CAACsL,WAAW,GACjB,IAAI,CAACe,aAAa,CAAChO,YAAY,CAAC2B,IAAI,CAAC,GACrC,IAAI,CAACuL,WAAW,CAACpd,OAAO,CAAC6R,IAAI,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIvI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACgU,eAAe,CAACyB,IAAI,GAAG,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACItM,SAASA,CAACZ,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6X,YAAY,EAAE;IAC9C;IACA,IAAI,IAAI,CAACvH,eAAe,IACpB,CAAC,IAAI,CAACyM,WAAW,IACjB,CAACnd,uBAAuB,CAAC,IAAI,CAACmd,WAAW,EAAEvC,wBAAwB,EAAEza,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MAC1F;IACJ;IACA,MAAMgd,MAAM,GAAG,IAAI,CAACf,aAAa,CAACrE,IAAI,CAAChI,IAAI,EAAE7P,QAAQ,EAAEC,QAAQ,EAAE6X,YAAY,CAAC;IAC9E,IAAImF,MAAM,EAAE;MACR,IAAI,CAACjC,MAAM,CAACjV,IAAI,CAAC;QACb+J,aAAa,EAAEmN,MAAM,CAACnN,aAAa;QACnCJ,YAAY,EAAEuN,MAAM,CAACvN,YAAY;QACjCvI,SAAS,EAAE,IAAI;QACf0I;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,0BAA0BA,CAACxQ,QAAQ,EAAEC,QAAQ,EAAE;IAC3C,IAAI,IAAI,CAAC2a,kBAAkB,EAAE;MACzB;IACJ;IACA,IAAIsC,UAAU;IACd,IAAIC,uBAAuB,GAAG,CAAC,CAAC;IAChC,IAAIC,yBAAyB,GAAG,CAAC,CAAC;IAClC;IACA,IAAI,CAACrU,gBAAgB,CAACxI,SAAS,CAACQ,OAAO,CAAC,CAACzD,QAAQ,EAAEL,OAAO,KAAK;MAC3D;MACA;MACA,IAAIA,OAAO,KAAK,IAAI,CAACqD,SAAS,IAAI,CAAChD,QAAQ,CAAC6B,UAAU,IAAI+d,UAAU,EAAE;QAClE;MACJ;MACA,IAAIrd,uBAAuB,CAACvC,QAAQ,CAAC6B,UAAU,EAAEsb,wBAAwB,EAAEza,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC5F,CAACkd,uBAAuB,EAAEC,yBAAyB,CAAC,GAAGC,0BAA0B,CAACpgB,OAAO,EAAEK,QAAQ,CAAC6B,UAAU,EAAEa,QAAQ,EAAEC,QAAQ,CAAC;QACnI,IAAIkd,uBAAuB,IAAIC,yBAAyB,EAAE;UACtDF,UAAU,GAAGjgB,OAAO;QACxB;MACJ;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACkgB,uBAAuB,IAAI,CAACC,yBAAyB,EAAE;MACxD,MAAM;QAAE7d,KAAK;QAAEC;MAAO,CAAC,GAAG,IAAI,CAAC8E,cAAc,CAACgZ,eAAe,CAAC,CAAC;MAC/D,MAAMne,UAAU,GAAG;QACfI,KAAK;QACLC,MAAM;QACNjC,GAAG,EAAE,CAAC;QACN8B,KAAK,EAAEE,KAAK;QACZD,MAAM,EAAEE,MAAM;QACd/B,IAAI,EAAE;MACV,CAAC;MACD0f,uBAAuB,GAAGI,0BAA0B,CAACpe,UAAU,EAAEc,QAAQ,CAAC;MAC1Emd,yBAAyB,GAAGI,4BAA4B,CAACre,UAAU,EAAEa,QAAQ,CAAC;MAC9Ekd,UAAU,GAAGpb,MAAM;IACvB;IACA,IAAIob,UAAU,KACTC,uBAAuB,KAAK,IAAI,CAAC3B,wBAAwB,IACtD4B,yBAAyB,KAAK,IAAI,CAAC3B,0BAA0B,IAC7DyB,UAAU,KAAK,IAAI,CAACrB,WAAW,CAAC,EAAE;MACtC,IAAI,CAACL,wBAAwB,GAAG2B,uBAAuB;MACvD,IAAI,CAAC1B,0BAA0B,GAAG2B,yBAAyB;MAC3D,IAAI,CAACvB,WAAW,GAAGqB,UAAU;MAC7B,IAAI,CAACC,uBAAuB,IAAIC,yBAAyB,KAAKF,UAAU,EAAE;QACtE,IAAI,CAAC7Y,OAAO,CAAC2F,iBAAiB,CAAC,IAAI,CAAC2R,oBAAoB,CAAC;MAC7D,CAAC,MACI;QACD,IAAI,CAACjP,cAAc,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,CAACgP,iBAAiB,CAAC3V,IAAI,CAAC,CAAC;EACjC;EACA;EACAqW,gBAAgBA,CAAA,EAAG;IACf,MAAMxH,MAAM,GAAGxZ,aAAa,CAAC,IAAI,CAAC6B,OAAO,CAAC,CAACG,KAAK;IAChD,IAAI,CAACkI,aAAa,CAACS,IAAI,CAAC,CAAC;IACzB,IAAI,CAACoV,WAAW,GAAG,IAAI;IACvB;IACA;IACA;IACA,IAAI,CAACsC,kBAAkB,GAAG7I,MAAM,CAAC8I,gBAAgB,IAAI9I,MAAM,CAAC+I,cAAc,IAAI,EAAE;IAChF/I,MAAM,CAAC+I,cAAc,GAAG/I,MAAM,CAAC8I,gBAAgB,GAAG,MAAM;IACxD,IAAI,CAACxB,aAAa,CAACnO,KAAK,CAAC,IAAI,CAACqN,WAAW,CAAC;IAC1C,IAAI,CAACkB,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACf,2BAA2B,CAAC9Q,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACmT,qBAAqB,CAAC,CAAC;EAChC;EACA;EACAtB,qBAAqBA,CAAA,EAAG;IACpB,MAAMrf,OAAO,GAAG7B,aAAa,CAAC,IAAI,CAAC6B,OAAO,CAAC;IAC3C,IAAI,CAAC8L,gBAAgB,CAACrI,KAAK,CAAC,IAAI,CAACoc,mBAAmB,CAAC;IACrD;IACA;IACA,IAAI,CAACE,WAAW,GAAG,IAAI,CAACjU,gBAAgB,CAACxI,SAAS,CAACe,GAAG,CAACrE,OAAO,CAAC,CAACkC,UAAU;EAC9E;EACA;EACAod,MAAMA,CAAA,EAAG;IACL,IAAI,CAACpB,WAAW,GAAG,KAAK;IACxB,MAAMvG,MAAM,GAAGxZ,aAAa,CAAC,IAAI,CAAC6B,OAAO,CAAC,CAACG,KAAK;IAChDwX,MAAM,CAAC+I,cAAc,GAAG/I,MAAM,CAAC8I,gBAAgB,GAAG,IAAI,CAACD,kBAAkB;IACzE,IAAI,CAACpC,SAAS,CAACta,OAAO,CAAC8X,OAAO,IAAIA,OAAO,CAACgF,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAI,CAAC3B,aAAa,CAAC3Q,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACmB,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC6O,2BAA2B,CAAC9Q,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC1B,gBAAgB,CAACtI,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;EACImP,gBAAgBA,CAACnQ,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAO,IAAI,CAACsd,WAAW,IAAI,IAAI,IAAIrd,kBAAkB,CAAC,IAAI,CAACqd,WAAW,EAAEvd,CAAC,EAAEC,CAAC,CAAC;EACjF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0Q,gCAAgCA,CAACP,IAAI,EAAEpQ,CAAC,EAAEC,CAAC,EAAE;IACzC,OAAO,IAAI,CAAC2b,SAAS,CAAC7c,IAAI,CAACqa,OAAO,IAAIA,OAAO,CAACiF,WAAW,CAACjO,IAAI,EAAEpQ,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoe,WAAWA,CAACjO,IAAI,EAAEpQ,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAAC,IAAI,CAACsd,WAAW,IACjB,CAACrd,kBAAkB,CAAC,IAAI,CAACqd,WAAW,EAAEvd,CAAC,EAAEC,CAAC,CAAC,IAC3C,CAAC,IAAI,CAACob,cAAc,CAACjL,IAAI,EAAE,IAAI,CAAC,EAAE;MAClC,OAAO,KAAK;IAChB;IACA,MAAMkO,gBAAgB,GAAG,IAAI,CAAC7iB,cAAc,CAAC,CAAC,CAAC6iB,gBAAgB,CAACte,CAAC,EAAEC,CAAC,CAAC;IACrE;IACA;IACA,IAAI,CAACqe,gBAAgB,EAAE;MACnB,OAAO,KAAK;IAChB;IACA,MAAMC,aAAa,GAAG5iB,aAAa,CAAC,IAAI,CAAC6B,OAAO,CAAC;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO8gB,gBAAgB,KAAKC,aAAa,IAAIA,aAAa,CAACnc,QAAQ,CAACkc,gBAAgB,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIE,eAAeA,CAACpF,OAAO,EAAElB,KAAK,EAAE;IAC5B,MAAMuG,cAAc,GAAG,IAAI,CAAC5C,eAAe;IAC3C,IAAI,CAAC4C,cAAc,CAACphB,GAAG,CAAC+b,OAAO,CAAC,IAC5BlB,KAAK,CAACgF,KAAK,CAAC9M,IAAI,IAAI;MAChB;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACiL,cAAc,CAACjL,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAACuL,WAAW,CAACpd,OAAO,CAAC6R,IAAI,CAAC,GAAG,CAAC,CAAC;IACjF,CAAC,CAAC,EAAE;MACJqO,cAAc,CAAC1U,GAAG,CAACqP,OAAO,CAAC;MAC3B,IAAI,CAACyD,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACsB,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAC3C,gBAAgB,CAAClV,IAAI,CAAC;QACvBoY,SAAS,EAAEtF,OAAO;QAClBuF,QAAQ,EAAE,IAAI;QACdzG;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACIkG,cAAcA,CAAChF,OAAO,EAAE;IACpB,IAAI,CAACyC,eAAe,CAAC5P,MAAM,CAACmN,OAAO,CAAC;IACpC,IAAI,CAAC0C,2BAA2B,CAAC9Q,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACyQ,gBAAgB,CAACnV,IAAI,CAAC;MAAEoY,SAAS,EAAEtF,OAAO;MAAEuF,QAAQ,EAAE;IAAK,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;EACIR,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACrC,2BAA2B,GAAG,IAAI,CAAChX,iBAAiB,CACpDwK,QAAQ,CAAC,IAAI,CAAC7T,cAAc,CAAC,CAAC,CAAC,CAC/ByP,SAAS,CAACxJ,KAAK,IAAI;MACpB,IAAI,IAAI,CAACkG,UAAU,CAAC,CAAC,EAAE;QACnB,MAAMgO,gBAAgB,GAAG,IAAI,CAACtM,gBAAgB,CAAC7H,YAAY,CAACC,KAAK,CAAC;QAClE,IAAIkU,gBAAgB,EAAE;UAClB,IAAI,CAAC6G,aAAa,CAACpC,cAAc,CAACzE,gBAAgB,CAAC9X,GAAG,EAAE8X,gBAAgB,CAAC5X,IAAI,CAAC;QAClF;MACJ,CAAC,MACI,IAAI,IAAI,CAAC6J,WAAW,CAAC,CAAC,EAAE;QACzB,IAAI,CAACgV,qBAAqB,CAAC,CAAC;MAChC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIphB,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACoa,iBAAiB,EAAE;MACzB,MAAM9H,UAAU,GAAGtS,cAAc,CAACE,aAAa,CAAC,IAAI,CAAC6B,OAAO,CAAC,CAAC;MAC9D,IAAI,CAACqY,iBAAiB,GAAI9H,UAAU,IAAI,IAAI,CAAClN,SAAU;IAC3D;IACA,OAAO,IAAI,CAACgV,iBAAiB;EACjC;EACA;EACA+G,wBAAwBA,CAAA,EAAG;IACvB,MAAMI,YAAY,GAAG,IAAI,CAACP,aAAa,CAClCtC,sBAAsB,CAAC,CAAC,CACxB8C,MAAM,CAAC7M,IAAI,IAAIA,IAAI,CAACxI,UAAU,CAAC,CAAC,CAAC;IACtC,IAAI,CAACgU,SAAS,CAACta,OAAO,CAAC8X,OAAO,IAAIA,OAAO,CAACoF,eAAe,CAAC,IAAI,EAAExB,YAAY,CAAC,CAAC;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,0BAA0BA,CAACpe,UAAU,EAAEc,QAAQ,EAAE;EACtD,MAAM;IAAE1C,GAAG;IAAE+B,MAAM;IAAEE;EAAO,CAAC,GAAGL,UAAU;EAC1C,MAAMgB,UAAU,GAAGX,MAAM,GAAGkb,0BAA0B;EACtD,IAAIza,QAAQ,IAAI1C,GAAG,GAAG4C,UAAU,IAAIF,QAAQ,IAAI1C,GAAG,GAAG4C,UAAU,EAAE;IAC9D,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIF,QAAQ,IAAIX,MAAM,GAAGa,UAAU,IAAIF,QAAQ,IAAIX,MAAM,GAAGa,UAAU,EAAE;IACzE,OAAO,CAAC,CAAC;EACb;;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAASqd,4BAA4BA,CAACre,UAAU,EAAEa,QAAQ,EAAE;EACxD,MAAM;IAAEvC,IAAI;IAAE4B,KAAK;IAAEE;EAAM,CAAC,GAAGJ,UAAU;EACzC,MAAMe,UAAU,GAAGX,KAAK,GAAGmb,0BAA0B;EACrD,IAAI1a,QAAQ,IAAIvC,IAAI,GAAGyC,UAAU,IAAIF,QAAQ,IAAIvC,IAAI,GAAGyC,UAAU,EAAE;IAChE,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIF,QAAQ,IAAIX,KAAK,GAAGa,UAAU,IAAIF,QAAQ,IAAIX,KAAK,GAAGa,UAAU,EAAE;IACvE,OAAO,CAAC,CAAC;EACb;;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmd,0BAA0BA,CAACpgB,OAAO,EAAEkC,UAAU,EAAEa,QAAQ,EAAEC,QAAQ,EAAE;EACzE,MAAMoe,gBAAgB,GAAGd,0BAA0B,CAACpe,UAAU,EAAEc,QAAQ,CAAC;EACzE,MAAMqe,kBAAkB,GAAGd,4BAA4B,CAACre,UAAU,EAAEa,QAAQ,CAAC;EAC7E,IAAImd,uBAAuB,GAAG,CAAC,CAAC;EAChC,IAAIC,yBAAyB,GAAG,CAAC,CAAC;EAClC;EACA;EACA;EACA;EACA,IAAIiB,gBAAgB,EAAE;IAClB,MAAMrd,SAAS,GAAG/D,OAAO,CAAC+D,SAAS;IACnC,IAAIqd,gBAAgB,KAAK,CAAC,CAAC,sCAAsC;MAC7D,IAAIrd,SAAS,GAAG,CAAC,EAAE;QACfmc,uBAAuB,GAAG,CAAC,CAAC;MAChC;IACJ,CAAC,MACI,IAAIlgB,OAAO,CAACshB,YAAY,GAAGvd,SAAS,GAAG/D,OAAO,CAACuhB,YAAY,EAAE;MAC9DrB,uBAAuB,GAAG,CAAC,CAAC;IAChC;EACJ;;EACA,IAAImB,kBAAkB,EAAE;IACpB,MAAMrd,UAAU,GAAGhE,OAAO,CAACgE,UAAU;IACrC,IAAIqd,kBAAkB,KAAK,CAAC,CAAC,0CAA0C;MACnE,IAAIrd,UAAU,GAAG,CAAC,EAAE;QAChBmc,yBAAyB,GAAG,CAAC,CAAC;MAClC;IACJ,CAAC,MACI,IAAIngB,OAAO,CAACwhB,WAAW,GAAGxd,UAAU,GAAGhE,OAAO,CAACyhB,WAAW,EAAE;MAC7DtB,yBAAyB,GAAG,CAAC,CAAC;IAClC;EACJ;;EACA,OAAO,CAACD,uBAAuB,EAAEC,yBAAyB,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,2BAA2B,GAAG1jB,+BAA+B,CAAC;EAChEsI,OAAO,EAAE,KAAK;EACdqb,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBxe,WAAWA,CAACgE,OAAO,EAAE/D,SAAS,EAAE;IAC5B,IAAI,CAAC+D,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACya,cAAc,GAAG,IAAInb,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACob,cAAc,GAAG,IAAIpb,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACqb,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIze,GAAG,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAAC0e,kBAAkB,GAAIrP,IAAI,IAAKA,IAAI,CAACxI,UAAU,CAAC,CAAC;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACwH,WAAW,GAAG,IAAIpT,OAAO,CAAC,CAAC;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACqT,SAAS,GAAG,IAAIrT,OAAO,CAAC,CAAC;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0jB,MAAM,GAAG,IAAI1jB,OAAO,CAAC,CAAC;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAAC2jB,4BAA4B,GAAIje,KAAK,IAAK;MAC3C,IAAI,IAAI,CAAC6d,oBAAoB,CAACvc,MAAM,GAAG,CAAC,EAAE;QACtCtB,KAAK,CAACoG,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;IACA,IAAI,CAAC8X,4BAA4B,GAAIle,KAAK,IAAK;MAC3C,IAAI,IAAI,CAAC6d,oBAAoB,CAACvc,MAAM,GAAG,CAAC,EAAE;QACtC;QACA;QACA;QACA,IAAI,IAAI,CAACuc,oBAAoB,CAACM,IAAI,CAAC,IAAI,CAACJ,kBAAkB,CAAC,EAAE;UACzD/d,KAAK,CAACoG,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAACsH,WAAW,CAAC9I,IAAI,CAAC5E,KAAK,CAAC;MAChC;IACJ,CAAC;IACD,IAAI,CAACb,SAAS,GAAGA,SAAS;EAC9B;EACA;EACA2b,qBAAqBA,CAACjM,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC8O,cAAc,CAAChiB,GAAG,CAACkT,IAAI,CAAC,EAAE;MAChC,IAAI,CAAC8O,cAAc,CAACtV,GAAG,CAACwG,IAAI,CAAC;IACjC;EACJ;EACA;EACAhH,gBAAgBA,CAACyO,IAAI,EAAE;IACnB,IAAI,CAACsH,cAAc,CAACvV,GAAG,CAACiO,IAAI,CAAC;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAACsH,cAAc,CAAChC,IAAI,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC1Y,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;QACjC;QACA;QACA,IAAI,CAAC1J,SAAS,CAAC2J,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACoV,4BAA4B,EAAEV,2BAA2B,CAAC;MAChH,CAAC,CAAC;IACN;EACJ;EACA;EACAxC,mBAAmBA,CAACnM,IAAI,EAAE;IACtB,IAAI,CAAC8O,cAAc,CAACpT,MAAM,CAACsE,IAAI,CAAC;EACpC;EACA;EACA5E,cAAcA,CAACqM,IAAI,EAAE;IACjB,IAAI,CAACsH,cAAc,CAACrT,MAAM,CAAC+L,IAAI,CAAC;IAChC,IAAI,CAAClL,YAAY,CAACkL,IAAI,CAAC;IACvB,IAAI,IAAI,CAACsH,cAAc,CAAChC,IAAI,KAAK,CAAC,EAAE;MAChC,IAAI,CAACzc,SAAS,CAAC4R,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACmN,4BAA4B,EAAEV,2BAA2B,CAAC;IACnH;EACJ;EACA;AACJ;AACA;AACA;AACA;EACInP,aAAaA,CAACiI,IAAI,EAAEtW,KAAK,EAAE;IACvB;IACA,IAAI,IAAI,CAAC6d,oBAAoB,CAAChhB,OAAO,CAACyZ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9C;IACJ;IACA,IAAI,CAACuH,oBAAoB,CAAC1F,IAAI,CAAC7B,IAAI,CAAC;IACpC,IAAI,IAAI,CAACuH,oBAAoB,CAACvc,MAAM,KAAK,CAAC,EAAE;MACxC,MAAMuK,YAAY,GAAG7L,KAAK,CAAC+B,IAAI,CAACqc,UAAU,CAAC,OAAO,CAAC;MACnD;MACA;MACA;MACA,IAAI,CAACN,gBAAgB,CAChBre,GAAG,CAACoM,YAAY,GAAG,UAAU,GAAG,SAAS,EAAE;QAC5CgF,OAAO,EAAGwN,CAAC,IAAK,IAAI,CAAC1Q,SAAS,CAAC/I,IAAI,CAACyZ,CAAC,CAAC;QACtCC,OAAO,EAAE;MACb,CAAC,CAAC,CACG7e,GAAG,CAAC,QAAQ,EAAE;QACfoR,OAAO,EAAGwN,CAAC,IAAK,IAAI,CAACL,MAAM,CAACpZ,IAAI,CAACyZ,CAAC,CAAC;QACnC;QACA;QACAC,OAAO,EAAE;MACb,CAAC;MACG;MACA;MACA;MACA;MAAA,CACC7e,GAAG,CAAC,aAAa,EAAE;QACpBoR,OAAO,EAAE,IAAI,CAACoN,4BAA4B;QAC1CK,OAAO,EAAEd;MACb,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC3R,YAAY,EAAE;QACf,IAAI,CAACiS,gBAAgB,CAACre,GAAG,CAAC,WAAW,EAAE;UACnCoR,OAAO,EAAGwN,CAAC,IAAK,IAAI,CAAC3Q,WAAW,CAAC9I,IAAI,CAACyZ,CAAC,CAAC;UACxCC,OAAO,EAAEd;QACb,CAAC,CAAC;MACN;MACA,IAAI,CAACta,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACiV,gBAAgB,CAACle,OAAO,CAAC,CAAC2e,MAAM,EAAE7gB,IAAI,KAAK;UAC5C,IAAI,CAACyB,SAAS,CAAC2J,gBAAgB,CAACpL,IAAI,EAAE6gB,MAAM,CAAC1N,OAAO,EAAE0N,MAAM,CAACD,OAAO,CAAC;QACzE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACAlT,YAAYA,CAACkL,IAAI,EAAE;IACf,MAAMqB,KAAK,GAAG,IAAI,CAACkG,oBAAoB,CAAChhB,OAAO,CAACyZ,IAAI,CAAC;IACrD,IAAIqB,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACkG,oBAAoB,CAAC9H,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,IAAI,CAACkG,oBAAoB,CAACvc,MAAM,KAAK,CAAC,EAAE;QACxC,IAAI,CAACkd,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;EACAtY,UAAUA,CAACoQ,IAAI,EAAE;IACb,OAAO,IAAI,CAACuH,oBAAoB,CAAChhB,OAAO,CAACyZ,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1I,QAAQA,CAACvB,UAAU,EAAE;IACjB,MAAMoS,OAAO,GAAG,CAAC,IAAI,CAACT,MAAM,CAAC;IAC7B,IAAI3R,UAAU,IAAIA,UAAU,KAAK,IAAI,CAAClN,SAAS,EAAE;MAC7C;MACA;MACA;MACAsf,OAAO,CAACtG,IAAI,CAAC,IAAIzd,UAAU,CAAEgkB,QAAQ,IAAK;QACtC,OAAO,IAAI,CAACxb,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;UACxC,MAAM8V,YAAY,GAAG,IAAI;UACzB,MAAMhd,QAAQ,GAAI3B,KAAK,IAAK;YACxB,IAAI,IAAI,CAAC6d,oBAAoB,CAACvc,MAAM,EAAE;cAClCod,QAAQ,CAAC9Z,IAAI,CAAC5E,KAAK,CAAC;YACxB;UACJ,CAAC;UACDqM,UAAU,CAACvD,gBAAgB,CAAC,QAAQ,EAAEnH,QAAQ,EAAEgd,YAAY,CAAC;UAC7D,OAAO,MAAM;YACTtS,UAAU,CAAC0E,mBAAmB,CAAC,QAAQ,EAAEpP,QAAQ,EAAEgd,YAAY,CAAC;UACpE,CAAC;QACL,CAAC,CAAC;MACN,CAAC,CAAC,CAAC;IACP;IACA,OAAOhkB,KAAK,CAAC,GAAG8jB,OAAO,CAAC;EAC5B;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChB,cAAc,CAAChe,OAAO,CAACif,QAAQ,IAAI,IAAI,CAAC5U,cAAc,CAAC4U,QAAQ,CAAC,CAAC;IACtE,IAAI,CAAClB,cAAc,CAAC/d,OAAO,CAACif,QAAQ,IAAI,IAAI,CAAC7D,mBAAmB,CAAC6D,QAAQ,CAAC,CAAC;IAC3E,IAAI,CAACL,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAC9Q,WAAW,CAACvD,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACwD,SAAS,CAACxD,QAAQ,CAAC,CAAC;EAC7B;EACA;EACAqU,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACV,gBAAgB,CAACle,OAAO,CAAC,CAAC2e,MAAM,EAAE7gB,IAAI,KAAK;MAC5C,IAAI,CAACyB,SAAS,CAAC4R,mBAAmB,CAACrT,IAAI,EAAE6gB,MAAM,CAAC1N,OAAO,EAAE0N,MAAM,CAACD,OAAO,CAAC;IAC5E,CAAC,CAAC;IACF,IAAI,CAACR,gBAAgB,CAACxe,KAAK,CAAC,CAAC;EACjC;AACJ;AACAoe,gBAAgB,CAACoB,IAAI,YAAAC,yBAAAC,CAAA;EAAA,YAAAA,CAAA,IAA6FtB,gBAAgB,EAA1B9kB,EAAE,CAAAqmB,QAAA,CAA0CrmB,EAAE,CAACsmB,MAAM,GAArDtmB,EAAE,CAAAqmB,QAAA,CAAgEvlB,QAAQ;AAAA,CAA6C;AAC/NgkB,gBAAgB,CAACyB,KAAK,kBADkFvmB,EAAE,CAAAwmB,kBAAA;EAAAC,KAAA,EACY3B,gBAAgB;EAAA4B,OAAA,EAAhB5B,gBAAgB,CAAAoB,IAAA;EAAAS,UAAA,EAAc;AAAM,EAAG;AAC7J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFwG5mB,EAAE,CAAA6mB,iBAAA,CAEV/B,gBAAgB,EAAc,CAAC;IACnH3b,IAAI,EAAElJ,UAAU;IAChB6mB,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExd,IAAI,EAAEnJ,EAAE,CAACsmB;IAAO,CAAC,EAAE;MAAEnd,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QACnF5d,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAAChmB,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkmB,cAAc,GAAG;EACnBla,kBAAkB,EAAE,CAAC;EACrB6N,+BAA+B,EAAE;AACrC,CAAC;AACD;AACA;AACA;AACA,MAAMsM,QAAQ,CAAC;EACX3gB,WAAWA,CAACC,SAAS,EAAE+D,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;IAC/D,IAAI,CAACjE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC+D,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACI0c,UAAUA,CAAChkB,OAAO,EAAEyiB,MAAM,GAAGqB,cAAc,EAAE;IACzC,OAAO,IAAInd,OAAO,CAAC3G,OAAO,EAAEyiB,MAAM,EAAE,IAAI,CAACpf,SAAS,EAAE,IAAI,CAAC+D,OAAO,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;EAClH;EACA;AACJ;AACA;AACA;EACI2c,cAAcA,CAACjkB,OAAO,EAAE;IACpB,OAAO,IAAI0d,WAAW,CAAC1d,OAAO,EAAE,IAAI,CAACsH,iBAAiB,EAAE,IAAI,CAACjE,SAAS,EAAE,IAAI,CAAC+D,OAAO,EAAE,IAAI,CAACC,cAAc,CAAC;EAC9G;AACJ;AACA0c,QAAQ,CAACf,IAAI,YAAAkB,iBAAAhB,CAAA;EAAA,YAAAA,CAAA,IAA6Fa,QAAQ,EAhDVjnB,EAAE,CAAAqmB,QAAA,CAgD0BvlB,QAAQ,GAhDpCd,EAAE,CAAAqmB,QAAA,CAgD+CrmB,EAAE,CAACsmB,MAAM,GAhD1DtmB,EAAE,CAAAqmB,QAAA,CAgDqEtlB,EAAE,CAACsmB,aAAa,GAhDvFrnB,EAAE,CAAAqmB,QAAA,CAgDkGvB,gBAAgB;AAAA,CAA6C;AACzQmC,QAAQ,CAACV,KAAK,kBAjD0FvmB,EAAE,CAAAwmB,kBAAA;EAAAC,KAAA,EAiDIQ,QAAQ;EAAAP,OAAA,EAARO,QAAQ,CAAAf,IAAA;EAAAS,UAAA,EAAc;AAAM,EAAG;AAC7I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlDwG5mB,EAAE,CAAA6mB,iBAAA,CAkDVI,QAAQ,EAAc,CAAC;IAC3G9d,IAAI,EAAElJ,UAAU;IAChB6mB,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExd,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QAC9D5d,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAAChmB,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEqI,IAAI,EAAEnJ,EAAE,CAACsmB;IAAO,CAAC,EAAE;MAAEnd,IAAI,EAAEpI,EAAE,CAACsmB;IAAc,CAAC,EAAE;MAAEle,IAAI,EAAE2b;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAErG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwC,eAAe,GAAG,IAAInnB,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMonB,mBAAmB,GAAG,IAAIpnB,cAAc,CAAC,kBAAkB,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqnB,gBAAgB,CAAC;EACnBlhB,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACmhB,MAAM,GAAG,IAAI7d,GAAG,CAAC,CAAC;IACvB,IAAI,CAACG,SAAS,GAAG,KAAK;EAC1B;EACA;EACA,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACjH,KAAK,EAAE;IAChB,IAAI,CAACkH,SAAS,GAAG3I,qBAAqB,CAACyB,KAAK,CAAC;EACjD;EACAmjB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyB,MAAM,CAAC/gB,KAAK,CAAC,CAAC;EACvB;AACJ;AACA8gB,gBAAgB,CAACtB,IAAI,YAAAwB,yBAAAtB,CAAA;EAAA,YAAAA,CAAA,IAA6FoB,gBAAgB;AAAA,CAAmD;AACrLA,gBAAgB,CAACG,IAAI,kBAtHmF3nB,EAAE,CAAA4nB,iBAAA;EAAAze,IAAA,EAsHJqe,gBAAgB;EAAAK,SAAA;EAAAC,MAAA;IAAAhe,QAAA;EAAA;EAAAie,QAAA;EAAAC,UAAA;EAAAC,QAAA,GAtHdjoB,EAAE,CAAAkoB,kBAAA,CAsH6I,CAAC;IAAEC,OAAO,EAAEZ,mBAAmB;IAAEa,WAAW,EAAEZ;EAAiB,CAAC,CAAC;AAAA,EAAiD;AACzW;EAAA,QAAAZ,SAAA,oBAAAA,SAAA,KAvHwG5mB,EAAE,CAAA6mB,iBAAA,CAuHVW,gBAAgB,EAAc,CAAC;IACnHre,IAAI,EAAE/I,SAAS;IACf0mB,IAAI,EAAE,CAAC;MACChe,QAAQ,EAAE,oBAAoB;MAC9Bif,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE,IAAI;MAChBK,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEZ,mBAAmB;QAAEa,WAAW,EAAEZ;MAAiB,CAAC;IAC/E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE1d,QAAQ,EAAE,CAAC;MACzBX,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,eAAe,GAAG,IAAInoB,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASooB,iBAAiBA,CAAC1gB,IAAI,EAAE/C,IAAI,EAAE;EACnC,IAAI+C,IAAI,CAACsU,QAAQ,KAAK,CAAC,EAAE;IACrB,MAAMqM,KAAK,CAAE,GAAE1jB,IAAK,wCAAuC,GAAI,0BAAyB+C,IAAI,CAACU,QAAS,IAAG,CAAC;EAC9G;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIkgB,gBAAgB,GAAG,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIvoB,cAAc,CAAC,aAAa,CAAC;AACvD;AACA,MAAMwoB,WAAW,CAAC;EACd;EACA,IAAI7e,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,CAAC,CAAC,IAAI,CAAC6e,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC9e,QAAS;EACpE;EACA,IAAIA,QAAQA,CAACjH,KAAK,EAAE;IAChB;IACA;IACA;IACA;IACA,IAAI,CAACgmB,YAAY,CAAC/e,QAAQ,GAAG,IAAI,CAACC,SAAS,GAAG3I,qBAAqB,CAACyB,KAAK,CAAC;EAC9E;EACAyD,WAAWA,CAAA,CACX;EACApD,OAAO,EAAE4lB,QAAQ,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,IAAI,EAAEL,MAAM,EAAEjD,MAAM,EAAE;IAC5E,IAAI,CAACziB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6lB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACL,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAACM,UAAU,GAAG,IAAIxnB,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmhB,WAAW,GAAG,EAAE;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACsG,EAAE,GAAI,iBAAgBV,gBAAgB,EAAG,EAAC;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAAC1H,cAAc,GAAG,MAAM,IAAI;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,MAAM,IAAI;IAC/B;IACA,IAAI,CAACnV,OAAO,GAAG,IAAIvL,YAAY,CAAC,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACqL,OAAO,GAAG,IAAIrL,YAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACsL,MAAM,GAAG,IAAItL,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC2gB,MAAM,GAAG,IAAI3gB,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC8oB,cAAc,GAAG,IAAIxf,GAAG,CAAC,CAAC;IAC/B,IAAI,OAAOgd,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C2B,iBAAiB,CAACrlB,OAAO,CAAC+gB,aAAa,EAAE,aAAa,CAAC;IAC3D;IACA,IAAI,CAAC4E,YAAY,GAAGC,QAAQ,CAAC3B,cAAc,CAACjkB,OAAO,CAAC;IACpD,IAAI,CAAC2lB,YAAY,CAACQ,IAAI,GAAG,IAAI;IAC7B,IAAI1D,MAAM,EAAE;MACR,IAAI,CAAC2D,eAAe,CAAC3D,MAAM,CAAC;IAChC;IACA,IAAI,CAACkD,YAAY,CAAC9H,cAAc,GAAG,CAACrD,IAAI,EAAEzH,IAAI,KAAK;MAC/C,OAAO,IAAI,CAAC8K,cAAc,CAACrD,IAAI,CAAC2L,IAAI,EAAEpT,IAAI,CAACoT,IAAI,CAAC;IACpD,CAAC;IACD,IAAI,CAACR,YAAY,CAAC7H,aAAa,GAAG,CAACjC,KAAK,EAAErB,IAAI,EAAEzH,IAAI,KAAK;MACrD,OAAO,IAAI,CAAC+K,aAAa,CAACjC,KAAK,EAAErB,IAAI,CAAC2L,IAAI,EAAEpT,IAAI,CAACoT,IAAI,CAAC;IAC1D,CAAC;IACD,IAAI,CAACE,2BAA2B,CAAC,IAAI,CAACV,YAAY,CAAC;IACnD,IAAI,CAACW,aAAa,CAAC,IAAI,CAACX,YAAY,CAAC;IACrCF,WAAW,CAACc,UAAU,CAAClK,IAAI,CAAC,IAAI,CAAC;IACjC,IAAIqJ,MAAM,EAAE;MACRA,MAAM,CAACnB,MAAM,CAAChY,GAAG,CAAC,IAAI,CAAC;IAC3B;EACJ;EACA;EACAia,OAAOA,CAAC5T,IAAI,EAAE;IACV,IAAI,CAACsT,cAAc,CAAC3Z,GAAG,CAACqG,IAAI,CAAC;IAC7B,IAAI,IAAI,CAAC+S,YAAY,CAACvb,UAAU,CAAC,CAAC,EAAE;MAChC,IAAI,CAACqc,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACA;EACAC,UAAUA,CAAC9T,IAAI,EAAE;IACb,IAAI,CAACsT,cAAc,CAACzX,MAAM,CAACmE,IAAI,CAAC;IAChC,IAAI,IAAI,CAAC+S,YAAY,CAACvb,UAAU,CAAC,CAAC,EAAE;MAChC,IAAI,CAACqc,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACA;EACAE,cAAcA,CAAA,EAAG;IACb,OAAOnS,KAAK,CAACkF,IAAI,CAAC,IAAI,CAACwM,cAAc,CAAC,CAACtL,IAAI,CAAC,CAACmC,CAAC,EAAEC,CAAC,KAAK;MAClD,MAAM4J,gBAAgB,GAAG7J,CAAC,CAAC8J,QAAQ,CAC9B1a,iBAAiB,CAAC,CAAC,CACnB2a,uBAAuB,CAAC9J,CAAC,CAAC6J,QAAQ,CAAC1a,iBAAiB,CAAC,CAAC,CAAC;MAC5D;MACA;MACA;MACA,OAAOya,gBAAgB,GAAGG,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;IACvE,CAAC,CAAC;EACN;EACAlE,WAAWA,CAAA,EAAG;IACV,MAAMjH,KAAK,GAAG4J,WAAW,CAACc,UAAU,CAACxlB,OAAO,CAAC,IAAI,CAAC;IAClD,IAAI8a,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ4J,WAAW,CAACc,UAAU,CAACtM,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA,IAAI,IAAI,CAAC6J,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACnB,MAAM,CAAC9V,MAAM,CAAC,IAAI,CAAC;IACnC;IACA,IAAI,CAACyX,cAAc,CAAC1iB,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACmiB,YAAY,CAAC7X,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACkY,UAAU,CAACld,IAAI,CAAC,CAAC;IACtB,IAAI,CAACkd,UAAU,CAAC3X,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAgY,2BAA2BA,CAACY,GAAG,EAAE;IAC7B,IAAI,IAAI,CAAClB,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACtY,MAAM,CACXkR,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAACgnB,IAAI,CAACpmB,KAAK,CAAC,EAAEb,SAAS,CAAC,IAAI,CAACknB,UAAU,CAAC,CAAC,CAC5DtY,SAAS,CAAC/N,KAAK,IAAIsnB,GAAG,CAACvY,aAAa,CAAC/O,KAAK,CAAC,CAAC;IACrD;IACAsnB,GAAG,CAAC5e,aAAa,CAACqF,SAAS,CAAC,MAAM;MAC9B,MAAMoN,QAAQ,GAAG1c,WAAW,CAAC,IAAI,CAACuhB,WAAW,CAAC,CAAC3gB,GAAG,CAAC+T,IAAI,IAAI;QACvD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1B,MAAMmU,qBAAqB,GAAGzB,WAAW,CAACc,UAAU,CAAChlB,IAAI,CAAC4lB,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKlT,IAAI,CAAC;UACnF,IAAI,CAACmU,qBAAqB,KAAK,OAAOxD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;YAC3E0D,OAAO,CAACC,IAAI,CAAE,2DAA0DtU,IAAK,GAAE,CAAC;UACpF;UACA,OAAOmU,qBAAqB;QAChC;QACA,OAAOnU,IAAI;MACf,CAAC,CAAC;MACF,IAAI,IAAI,CAAC2S,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACnB,MAAM,CAACzgB,OAAO,CAACiP,IAAI,IAAI;UAC/B,IAAI+H,QAAQ,CAAC/Z,OAAO,CAACgS,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B+H,QAAQ,CAACuB,IAAI,CAACtJ,IAAI,CAAC;UACvB;QACJ,CAAC,CAAC;MACN;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACuU,0BAA0B,EAAE;QAClC,MAAMC,iBAAiB,GAAG,IAAI,CAACzB,iBAAiB,CAC3C0B,2BAA2B,CAAC,IAAI,CAACxnB,OAAO,CAAC,CACzChB,GAAG,CAACyoB,UAAU,IAAIA,UAAU,CAACC,aAAa,CAAC,CAAC,CAAC3G,aAAa,CAAC;QAChE,IAAI,CAAC4E,YAAY,CAAC5G,qBAAqB,CAACwI,iBAAiB,CAAC;QAC1D;QACA;QACA,IAAI,CAACD,0BAA0B,GAAG,IAAI;MAC1C;MACAL,GAAG,CAACrgB,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5BqgB,GAAG,CAACxQ,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5BwQ,GAAG,CAAC3T,eAAe,GAAGpV,qBAAqB,CAAC,IAAI,CAACoV,eAAe,CAAC;MACjE2T,GAAG,CAACtJ,kBAAkB,GAAGzf,qBAAqB,CAAC,IAAI,CAACyf,kBAAkB,CAAC;MACvEsJ,GAAG,CAACrJ,cAAc,GAAGvf,oBAAoB,CAAC,IAAI,CAACuf,cAAc,EAAE,CAAC,CAAC;MACjEqJ,GAAG,CACEtH,WAAW,CAAC7E,QAAQ,CAAC2E,MAAM,CAAC1M,IAAI,IAAIA,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,CAAC/T,GAAG,CAACmoB,IAAI,IAAIA,IAAI,CAACxB,YAAY,CAAC,CAAC,CAC1F/F,eAAe,CAAC,IAAI,CAACtF,WAAW,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAgM,aAAaA,CAACW,GAAG,EAAE;IACfA,GAAG,CAAC5e,aAAa,CAACqF,SAAS,CAAC,MAAM;MAC9B,IAAI,CAAC+Y,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACZ,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFV,GAAG,CAACxe,OAAO,CAACiF,SAAS,CAACxJ,KAAK,IAAI;MAC3B,IAAI,CAACuE,OAAO,CAACmf,IAAI,CAAC;QACd1d,SAAS,EAAE,IAAI;QACf0I,IAAI,EAAE1O,KAAK,CAAC0O,IAAI,CAACuT,IAAI;QACrB1T,YAAY,EAAEvO,KAAK,CAACuO;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;IACFwU,GAAG,CAACve,MAAM,CAACgF,SAAS,CAACxJ,KAAK,IAAI;MAC1B,IAAI,CAACwE,MAAM,CAACkf,IAAI,CAAC;QACb1d,SAAS,EAAE,IAAI;QACf0I,IAAI,EAAE1O,KAAK,CAAC0O,IAAI,CAACuT;MACrB,CAAC,CAAC;MACF,IAAI,CAACN,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFV,GAAG,CAAClJ,MAAM,CAACrQ,SAAS,CAACxJ,KAAK,IAAI;MAC1B,IAAI,CAAC6Z,MAAM,CAAC6J,IAAI,CAAC;QACb/U,aAAa,EAAE3O,KAAK,CAAC2O,aAAa;QAClCJ,YAAY,EAAEvO,KAAK,CAACuO,YAAY;QAChCvI,SAAS,EAAE,IAAI;QACf0I,IAAI,EAAE1O,KAAK,CAAC0O,IAAI,CAACuT;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;IACFc,GAAG,CAACte,OAAO,CAAC+E,SAAS,CAACma,SAAS,IAAI;MAC/B,IAAI,CAAClf,OAAO,CAACif,IAAI,CAAC;QACd/U,aAAa,EAAEgV,SAAS,CAAChV,aAAa;QACtCJ,YAAY,EAAEoV,SAAS,CAACpV,YAAY;QACpCK,iBAAiB,EAAE+U,SAAS,CAAC/U,iBAAiB,CAACqT,IAAI;QACnDjc,SAAS,EAAE2d,SAAS,CAAC3d,SAAS,CAACic,IAAI;QACnCvT,IAAI,EAAEiV,SAAS,CAACjV,IAAI,CAACuT,IAAI;QACzBzT,sBAAsB,EAAEmV,SAAS,CAACnV,sBAAsB;QACxDrH,QAAQ,EAAEwc,SAAS,CAACxc,QAAQ;QAC5ByE,SAAS,EAAE+X,SAAS,CAAC/X,SAAS;QAC9B5L,KAAK,EAAE2jB,SAAS,CAAC3jB;MACrB,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC2hB,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF9oB,KAAK,CAACooB,GAAG,CAACjJ,gBAAgB,EAAEiJ,GAAG,CAAChJ,gBAAgB,CAAC,CAACvQ,SAAS,CAAC,MAAM,IAAI,CAACmY,kBAAkB,CAAC8B,YAAY,CAAC,CAAC,CAAC;EAC7G;EACA;EACAvB,eAAeA,CAAC3D,MAAM,EAAE;IACpB,MAAM;MAAEhM,QAAQ;MAAEqR,gBAAgB;MAAExU,eAAe;MAAEyU,sBAAsB;MAAEC;IAAgB,CAAC,GAAGvF,MAAM;IACvG,IAAI,CAAC7b,QAAQ,GAAGkhB,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAGA,gBAAgB;IACnE,IAAI,CAACxU,eAAe,GAAGA,eAAe,IAAI,IAAI,GAAG,KAAK,GAAGA,eAAe;IACxE,IAAI,CAACqK,kBAAkB,GAAGoK,sBAAsB,IAAI,IAAI,GAAG,KAAK,GAAGA,sBAAsB;IACzF,IAAI,CAACzN,WAAW,GAAG0N,eAAe,IAAI,UAAU;IAChD,IAAIvR,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;EACJ;EACA;EACAgQ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACd,YAAY,CAAChL,SAAS,CAAC,IAAI,CAACgM,cAAc,CAAC,CAAC,CAAC3nB,GAAG,CAAC4T,IAAI,IAAIA,IAAI,CAACiU,QAAQ,CAAC,CAAC;EACjF;AACJ;AACA;AACApB,WAAW,CAACc,UAAU,GAAG,EAAE;AAC3Bd,WAAW,CAACzC,IAAI,YAAAiF,oBAAA/E,CAAA;EAAA,YAAAA,CAAA,IAA6FuC,WAAW,EA1ZhB3oB,EAAE,CAAAorB,iBAAA,CA0ZgCprB,EAAE,CAACqrB,UAAU,GA1Z/CrrB,EAAE,CAAAorB,iBAAA,CA0Z0DnE,QAAQ,GA1ZpEjnB,EAAE,CAAAorB,iBAAA,CA0Z+EprB,EAAE,CAACsrB,iBAAiB,GA1ZrGtrB,EAAE,CAAAorB,iBAAA,CA0ZgHrqB,EAAE,CAACwqB,gBAAgB,GA1ZrIvrB,EAAE,CAAAorB,iBAAA,CA0ZgJ9oB,EAAE,CAACkpB,cAAc,MA1ZnKxrB,EAAE,CAAAorB,iBAAA,CA0Z8L7D,mBAAmB,OA1ZnNvnB,EAAE,CAAAorB,iBAAA,CA0Z8P9C,eAAe;AAAA,CAA4D;AACnbK,WAAW,CAAChB,IAAI,kBA3ZwF3nB,EAAE,CAAA4nB,iBAAA;EAAAze,IAAA,EA2ZTwf,WAAW;EAAAd,SAAA;EAAA4D,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3ZJ7rB,EAAE,CAAA+rB,WAAA,OAAAD,GAAA,CAAA3C,EAAA;MAAFnpB,EAAE,CAAAgsB,WAAA,2BAAAF,GAAA,CAAAhiB,QAAA,4BAAAgiB,GAAA,CAAAjD,YAAA,CAAAvb,UAAA,+BAAAwe,GAAA,CAAAjD,YAAA,CAAAtb,WAAA;IAAA;EAAA;EAAAua,MAAA;IAAAjF,WAAA;IAAAwG,IAAA;IAAA7L,WAAA;IAAA2L,EAAA;IAAAxP,QAAA;IAAA7P,QAAA;IAAA0M,eAAA;IAAAuK,cAAA;IAAAC,aAAA;IAAAH,kBAAA;IAAAC,cAAA;EAAA;EAAAmL,OAAA;IAAApgB,OAAA;IAAAF,OAAA;IAAAC,MAAA;IAAAqV,MAAA;EAAA;EAAA8G,QAAA;EAAAC,UAAA;EAAAC,QAAA,GAAFjoB,EAAE,CAAAkoB,kBAAA,CA2Z8hC;EAChoC;EACA;IAAEC,OAAO,EAAEZ,mBAAmB;IAAE2E,QAAQ,EAAE9b;EAAU,CAAC,EACrD;IAAE+X,OAAO,EAAEO,aAAa;IAAEN,WAAW,EAAEO;EAAY,CAAC,CACvD;AAAA,EAA4C;AACjD;EAAA,QAAA/B,SAAA,oBAAAA,SAAA,KAhawG5mB,EAAE,CAAA6mB,iBAAA,CAgaV8B,WAAW,EAAc,CAAC;IAC9Gxf,IAAI,EAAE/I,SAAS;IACf0mB,IAAI,EAAE,CAAC;MACChe,QAAQ,EAAE,8BAA8B;MACxCif,QAAQ,EAAE,aAAa;MACvBC,UAAU,EAAE,IAAI;MAChBK,SAAS,EAAE;MACP;MACA;QAAEF,OAAO,EAAEZ,mBAAmB;QAAE2E,QAAQ,EAAE9b;MAAU,CAAC,EACrD;QAAE+X,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEO;MAAY,CAAC,CACvD;MACDwD,IAAI,EAAE;QACF,OAAO,EAAE,eAAe;QACxB,WAAW,EAAE,IAAI;QACjB,gCAAgC,EAAE,UAAU;QAC5C,gCAAgC,EAAE,2BAA2B;QAC7D,iCAAiC,EAAE;MACvC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhjB,IAAI,EAAEnJ,EAAE,CAACqrB;IAAW,CAAC,EAAE;MAAEliB,IAAI,EAAE8d;IAAS,CAAC,EAAE;MAAE9d,IAAI,EAAEnJ,EAAE,CAACsrB;IAAkB,CAAC,EAAE;MAAEniB,IAAI,EAAEpI,EAAE,CAACwqB;IAAiB,CAAC,EAAE;MAAEpiB,IAAI,EAAE7G,EAAE,CAACkpB,cAAc;MAAEzE,UAAU,EAAE,CAAC;QAClL5d,IAAI,EAAE5I;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4I,IAAI,EAAEqe,gBAAgB;MAAET,UAAU,EAAE,CAAC;QACzC5d,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAACS,mBAAmB;MAC9B,CAAC,EAAE;QACCpe,IAAI,EAAE3I;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2I,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QAClC5d,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAACwB,eAAe;MAC1B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEzF,WAAW,EAAE,CAAC;MAC1C1Z,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACPlgB,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEtJ,WAAW,EAAE,CAAC;MACdrU,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAEqC,EAAE,EAAE,CAAC;MACLhgB,IAAI,EAAE9I;IACV,CAAC,CAAC;IAAEsZ,QAAQ,EAAE,CAAC;MACXxQ,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEhd,QAAQ,EAAE,CAAC;MACXX,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEtQ,eAAe,EAAE,CAAC;MAClBrN,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE/F,cAAc,EAAE,CAAC;MACjB5X,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE9F,aAAa,EAAE,CAAC;MAChB7X,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAEjG,kBAAkB,EAAE,CAAC;MACrB1X,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEhG,cAAc,EAAE,CAAC;MACjB3X,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEjb,OAAO,EAAE,CAAC;MACV1C,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEnb,OAAO,EAAE,CAAC;MACVxC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAElb,MAAM,EAAE,CAAC;MACTzC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE7F,MAAM,EAAE,CAAC;MACT9X,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsF,eAAe,GAAG,IAAIjsB,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMksB,aAAa,CAAC;EAChB;EACA,IAAIviB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACjH,KAAK,EAAE;IAChB,IAAI,CAACkH,SAAS,GAAG3I,qBAAqB,CAACyB,KAAK,CAAC;IAC7C,IAAI,CAACypB,aAAa,CAACtgB,IAAI,CAAC,IAAI,CAAC;EACjC;EACA1F,WAAWA,CAACpD,OAAO,EAAEqpB,UAAU,EAAE;IAC7B,IAAI,CAACrpB,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACopB,aAAa,GAAG,IAAI5qB,OAAO,CAAC,CAAC;IAClC,IAAI,CAACqI,SAAS,GAAG,KAAK;IACtB,IAAI,OAAO6c,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C2B,iBAAiB,CAACrlB,OAAO,CAAC+gB,aAAa,EAAE,eAAe,CAAC;IAC7D;IACA,IAAI,CAACuI,WAAW,GAAGD,UAAU;EACjC;EACAvG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsG,aAAa,CAAC/a,QAAQ,CAAC,CAAC;EACjC;AACJ;AACA8a,aAAa,CAACnG,IAAI,YAAAuG,sBAAArG,CAAA;EAAA,YAAAA,CAAA,IAA6FiG,aAAa,EAphBpBrsB,EAAE,CAAAorB,iBAAA,CAohBoCprB,EAAE,CAACqrB,UAAU,GAphBnDrrB,EAAE,CAAAorB,iBAAA,CAohB8D9D,eAAe;AAAA,CAA4E;AACnQ+E,aAAa,CAAC1E,IAAI,kBArhBsF3nB,EAAE,CAAA4nB,iBAAA;EAAAze,IAAA,EAqhBPkjB,aAAa;EAAAxE,SAAA;EAAA4D,SAAA;EAAA3D,MAAA;IAAAhe,QAAA;EAAA;EAAAke,UAAA;EAAAC,QAAA,GArhBRjoB,EAAE,CAAAkoB,kBAAA,CAqhB8K,CAAC;IAAEC,OAAO,EAAEiE,eAAe;IAAEhE,WAAW,EAAEiE;EAAc,CAAC,CAAC;AAAA,EAAiB;AACnW;EAAA,QAAAzF,SAAA,oBAAAA,SAAA,KAthBwG5mB,EAAE,CAAA6mB,iBAAA,CAshBVwF,aAAa,EAAc,CAAC;IAChHljB,IAAI,EAAE/I,SAAS;IACf0mB,IAAI,EAAE,CAAC;MACChe,QAAQ,EAAE,iBAAiB;MAC3Bkf,UAAU,EAAE,IAAI;MAChBmE,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACD9D,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEiE,eAAe;QAAEhE,WAAW,EAAEiE;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEljB,IAAI,EAAEnJ,EAAE,CAACqrB;IAAW,CAAC,EAAE;MAAEliB,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QACvF5d,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAACQ,eAAe;MAC1B,CAAC,EAAE;QACCne,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAE3I;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsJ,QAAQ,EAAE,CAAC;MACvCX,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4F,oBAAoB,GAAG,IAAIvsB,cAAc,CAAC,oBAAoB,CAAC;AACrE;AACA;AACA;AACA;AACA,MAAMwsB,kBAAkB,CAAC;EACrBrmB,WAAWA,CAACsmB,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ;AACAD,kBAAkB,CAACzG,IAAI,YAAA2G,2BAAAzG,CAAA;EAAA,YAAAA,CAAA,IAA6FuG,kBAAkB,EAlkB9B3sB,EAAE,CAAAorB,iBAAA,CAkkB8CprB,EAAE,CAAC8sB,WAAW;AAAA,CAA4C;AAClNH,kBAAkB,CAAChF,IAAI,kBAnkBiF3nB,EAAE,CAAA4nB,iBAAA;EAAAze,IAAA,EAmkBFwjB,kBAAkB;EAAA9E,SAAA;EAAAC,MAAA;IAAAuB,IAAA;EAAA;EAAArB,UAAA;EAAAC,QAAA,GAnkBlBjoB,EAAE,CAAAkoB,kBAAA,CAmkBwH,CAAC;IAAEC,OAAO,EAAEuE,oBAAoB;IAAEtE,WAAW,EAAEuE;EAAmB,CAAC,CAAC;AAAA,EAAiB;AACvT;EAAA,QAAA/F,SAAA,oBAAAA,SAAA,KApkBwG5mB,EAAE,CAAA6mB,iBAAA,CAokBV8F,kBAAkB,EAAc,CAAC;IACrHxjB,IAAI,EAAE/I,SAAS;IACf0mB,IAAI,EAAE,CAAC;MACChe,QAAQ,EAAE,iCAAiC;MAC3Ckf,UAAU,EAAE,IAAI;MAChBK,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEuE,oBAAoB;QAAEtE,WAAW,EAAEuE;MAAmB,CAAC;IAClF,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExjB,IAAI,EAAEnJ,EAAE,CAAC8sB;IAAY,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEzD,IAAI,EAAE,CAAC;MACzFlgB,IAAI,EAAE9I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0sB,gBAAgB,GAAG,IAAI5sB,cAAc,CAAC,gBAAgB,CAAC;AAC7D;AACA;AACA;AACA;AACA,MAAM6sB,cAAc,CAAC;EACjB;EACA,IAAI1X,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC2X,UAAU;EAC1B;EACA,IAAI3X,SAASA,CAACzS,KAAK,EAAE;IACjB,IAAI,CAACoqB,UAAU,GAAG7rB,qBAAqB,CAACyB,KAAK,CAAC;EAClD;EACAyD,WAAWA,CAACsmB,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACK,UAAU,GAAG,KAAK;EAC3B;AACJ;AACAD,cAAc,CAAC9G,IAAI,YAAAgH,uBAAA9G,CAAA;EAAA,YAAAA,CAAA,IAA6F4G,cAAc,EA7mBtBhtB,EAAE,CAAAorB,iBAAA,CA6mBsCprB,EAAE,CAAC8sB,WAAW;AAAA,CAA4C;AAC1ME,cAAc,CAACrF,IAAI,kBA9mBqF3nB,EAAE,CAAA4nB,iBAAA;EAAAze,IAAA,EA8mBN6jB,cAAc;EAAAnF,SAAA;EAAAC,MAAA;IAAAuB,IAAA;IAAA/T,SAAA;EAAA;EAAA0S,UAAA;EAAAC,QAAA,GA9mBVjoB,EAAE,CAAAkoB,kBAAA,CA8mBoI,CAAC;IAAEC,OAAO,EAAE4E,gBAAgB;IAAE3E,WAAW,EAAE4E;EAAe,CAAC,CAAC;AAAA,EAAiB;AAC3T;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KA/mBwG5mB,EAAE,CAAA6mB,iBAAA,CA+mBVmG,cAAc,EAAc,CAAC;IACjH7jB,IAAI,EAAE/I,SAAS;IACf0mB,IAAI,EAAE,CAAC;MACChe,QAAQ,EAAE,6BAA6B;MACvCkf,UAAU,EAAE,IAAI;MAChBK,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE4E,gBAAgB;QAAE3E,WAAW,EAAE4E;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7jB,IAAI,EAAEnJ,EAAE,CAAC8sB;IAAY,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEzD,IAAI,EAAE,CAAC;MACzFlgB,IAAI,EAAE9I;IACV,CAAC,CAAC;IAAEiV,SAAS,EAAE,CAAC;MACZnM,IAAI,EAAE9I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8sB,eAAe,GAAG,UAAU;AAClC;AACA,MAAMC,OAAO,CAAC;EACV;EACA,IAAItjB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACoJ,aAAa,IAAI,IAAI,CAACA,aAAa,CAACrJ,QAAS;EAChF;EACA,IAAIA,QAAQA,CAACjH,KAAK,EAAE;IAChB,IAAI,CAACkH,SAAS,GAAG3I,qBAAqB,CAACyB,KAAK,CAAC;IAC7C,IAAI,CAACknB,QAAQ,CAACjgB,QAAQ,GAAG,IAAI,CAACC,SAAS;EAC3C;EACAzD,WAAWA,CAAA,CACX;EACApD,OAAO,EACP;EACAiQ,aAAa;EACb;AACJ;AACA;AACA;EACI5M,SAAS,EAAE+D,OAAO,EAAE+iB,iBAAiB,EAAE1H,MAAM,EAAEsD,IAAI,EAAEH,QAAQ,EAAEC,kBAAkB,EAAEuE,WAAW,EAAEd,WAAW,EAAE;IACzG,IAAI,CAACtpB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiQ,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC7I,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+iB,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACpE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACuE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACd,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACtD,UAAU,GAAG,IAAIxnB,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAAC8J,OAAO,GAAG,IAAIlL,YAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACmL,QAAQ,GAAG,IAAInL,YAAY,CAAC,CAAC;IAClC;IACA,IAAI,CAACoL,KAAK,GAAG,IAAIpL,YAAY,CAAC,CAAC;IAC/B;IACA,IAAI,CAACqL,OAAO,GAAG,IAAIrL,YAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACsL,MAAM,GAAG,IAAItL,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACuL,OAAO,GAAG,IAAIvL,YAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACwL,KAAK,GAAG,IAAIhK,UAAU,CAAEgkB,QAAQ,IAAK;MACtC,MAAMyH,YAAY,GAAG,IAAI,CAACxD,QAAQ,CAACje,KAAK,CACnC+V,IAAI,CAAC3f,GAAG,CAACsrB,UAAU,KAAK;QACzB/qB,MAAM,EAAE,IAAI;QACZ6J,eAAe,EAAEkhB,UAAU,CAAClhB,eAAe;QAC3ClF,KAAK,EAAEomB,UAAU,CAACpmB,KAAK;QACvBqH,KAAK,EAAE+e,UAAU,CAAC/e,KAAK;QACvBF,QAAQ,EAAEif,UAAU,CAACjf;MACzB,CAAC,CAAC,CAAC,CAAC,CACCqC,SAAS,CAACkV,QAAQ,CAAC;MACxB,OAAO,MAAM;QACTyH,YAAY,CAAC7c,WAAW,CAAC,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACqZ,QAAQ,GAAGjB,QAAQ,CAAC5B,UAAU,CAAChkB,OAAO,EAAE;MACzC4J,kBAAkB,EAAE6Y,MAAM,IAAIA,MAAM,CAAC7Y,kBAAkB,IAAI,IAAI,GAAG6Y,MAAM,CAAC7Y,kBAAkB,GAAG,CAAC;MAC/F6N,+BAA+B,EAAEgL,MAAM,IAAIA,MAAM,CAAChL,+BAA+B,IAAI,IAAI,GACnFgL,MAAM,CAAChL,+BAA+B,GACtC,CAAC;MACPpD,MAAM,EAAEoO,MAAM,EAAEpO;IACpB,CAAC,CAAC;IACF,IAAI,CAACwS,QAAQ,CAACV,IAAI,GAAG,IAAI;IACzB;IACA;IACA;IACA+D,OAAO,CAACpI,cAAc,CAACzF,IAAI,CAAC,IAAI,CAAC;IACjC,IAAIoG,MAAM,EAAE;MACR,IAAI,CAAC2D,eAAe,CAAC3D,MAAM,CAAC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIxS,aAAa,EAAE;MACf,IAAI,CAAC4W,QAAQ,CAACjY,kBAAkB,CAACqB,aAAa,CAAC0V,YAAY,CAAC;MAC5D1V,aAAa,CAACuW,OAAO,CAAC,IAAI,CAAC;IAC/B;IACA,IAAI,CAAC+D,WAAW,CAAC,IAAI,CAAC1D,QAAQ,CAAC;IAC/B,IAAI,CAACP,aAAa,CAAC,IAAI,CAACO,QAAQ,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACI7a,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC6a,QAAQ,CAAC7a,qBAAqB,CAAC,CAAC;EAChD;EACA;EACAE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC2a,QAAQ,CAAC3a,cAAc,CAAC,CAAC;EACzC;EACA;EACAoC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACuY,QAAQ,CAACvY,KAAK,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACIO,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACgY,QAAQ,CAAChY,mBAAmB,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;EACIC,mBAAmBA,CAACnP,KAAK,EAAE;IACvB,IAAI,CAACknB,QAAQ,CAAC/X,mBAAmB,CAACnP,KAAK,CAAC;EAC5C;EACA6qB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAACpjB,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA;MACA,IAAI,CAAC3F,OAAO,CAACqjB,QAAQ,CAAC9L,IAAI,CAAC1f,IAAI,CAAC,CAAC,CAAC,EAAEH,SAAS,CAAC,IAAI,CAACknB,UAAU,CAAC,CAAC,CAACtY,SAAS,CAAC,MAAM;QAC5E,IAAI,CAACgd,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAAC/D,QAAQ,CAAC/X,mBAAmB,CAAC,IAAI,CAAC8b,gBAAgB,CAAC;QAC5D;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,kBAAkB,GAAGD,OAAO,CAAC,qBAAqB,CAAC;IACzD,MAAME,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;IAClD;IACA;IACA,IAAIC,kBAAkB,IAAI,CAACA,kBAAkB,CAACE,WAAW,EAAE;MACvD,IAAI,CAACP,kBAAkB,CAAC,CAAC;IAC7B;IACA;IACA,IAAIM,cAAc,IAAI,CAACA,cAAc,CAACC,WAAW,IAAI,IAAI,CAACL,gBAAgB,EAAE;MACxE,IAAI,CAAC/D,QAAQ,CAAC/X,mBAAmB,CAAC,IAAI,CAAC8b,gBAAgB,CAAC;IAC5D;EACJ;EACA9H,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC7S,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACyW,UAAU,CAAC,IAAI,CAAC;IACvC;IACA,MAAM7K,KAAK,GAAGqO,OAAO,CAACpI,cAAc,CAAC/gB,OAAO,CAAC,IAAI,CAAC;IAClD,IAAI8a,KAAK,GAAG,CAAC,CAAC,EAAE;MACZqO,OAAO,CAACpI,cAAc,CAAC7H,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,CAACzU,OAAO,CAAC2F,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACiZ,UAAU,CAACld,IAAI,CAAC,CAAC;MACtB,IAAI,CAACkd,UAAU,CAAC3X,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAACwY,QAAQ,CAAC/Y,OAAO,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;EACA4c,kBAAkBA,CAAA,EAAG;IACjB,MAAM1qB,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC+gB,aAAa;IAC1C,IAAIlU,WAAW,GAAG7M,OAAO;IACzB,IAAI,IAAI,CAACkrB,mBAAmB,EAAE;MAC1Bre,WAAW,GACP7M,OAAO,CAACmrB,OAAO,KAAKje,SAAS,GACvBlN,OAAO,CAACmrB,OAAO,CAAC,IAAI,CAACD,mBAAmB,CAAC;MACzC;MACElrB,OAAO,CAACoc,aAAa,EAAE+O,OAAO,CAAC,IAAI,CAACD,mBAAmB,CAAC;IACxE;IACA,IAAIre,WAAW,KAAK,OAAO6W,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE2B,iBAAiB,CAACxY,WAAW,EAAE,SAAS,CAAC;IAC7C;IACA,IAAI,CAACga,QAAQ,CAAClb,eAAe,CAACkB,WAAW,IAAI7M,OAAO,CAAC;EACzD;EACA;EACAorB,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAAC9d,eAAe;IACrC,IAAI,CAAC8d,QAAQ,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI,CAACrrB,OAAO,CAAC+gB,aAAa,CAACoK,OAAO,CAACE,QAAQ,CAAC;IACvD;IACA,OAAOltB,aAAa,CAACktB,QAAQ,CAAC;EAClC;EACA;EACAd,WAAWA,CAACtD,GAAG,EAAE;IACbA,GAAG,CAAC5e,aAAa,CAACqF,SAAS,CAAC,MAAM;MAC9B,IAAI,CAACuZ,GAAG,CAAC7c,UAAU,CAAC,CAAC,EAAE;QACnB,MAAMkhB,GAAG,GAAG,IAAI,CAACvF,IAAI;QACrB,MAAM3d,cAAc,GAAG,IAAI,CAACA,cAAc;QAC1C,MAAM+H,WAAW,GAAG,IAAI,CAACvD,oBAAoB,GACvC;UACEH,QAAQ,EAAE,IAAI,CAACG,oBAAoB,CAAC8c,WAAW;UAC/CxjB,OAAO,EAAE,IAAI,CAAC0G,oBAAoB,CAACuZ,IAAI;UACvCpS,aAAa,EAAE,IAAI,CAACoW;QACxB,CAAC,GACC,IAAI;QACV,MAAMvW,OAAO,GAAG,IAAI,CAAClH,gBAAgB,GAC/B;UACED,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACgd,WAAW;UAC3CxjB,OAAO,EAAE,IAAI,CAACwG,gBAAgB,CAACyZ,IAAI;UACnC/T,SAAS,EAAE,IAAI,CAAC1F,gBAAgB,CAAC0F,SAAS;UAC1C2B,aAAa,EAAE,IAAI,CAACoW;QACxB,CAAC,GACC,IAAI;QACVlD,GAAG,CAACrgB,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5BqgB,GAAG,CAACxQ,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5BwQ,GAAG,CAAC7e,cAAc,GACd,OAAOA,cAAc,KAAK,QAAQ,IAAIA,cAAc,GAC9CA,cAAc,GACd/J,oBAAoB,CAAC+J,cAAc,CAAC;QAC9C6e,GAAG,CAACjc,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;QAC9Cic,GAAG,CAACtT,YAAY,GAAG,IAAI,CAACA,YAAY;QACpCsT,GAAG,CACE3Z,mBAAmB,CAAC,IAAI,CAAC8d,mBAAmB,CAAC,CAAC,CAAC,CAC/Cze,uBAAuB,CAACwD,WAAW,CAAC,CACpC3D,mBAAmB,CAACoH,OAAO,CAAC,CAC5B7E,oBAAoB,CAAC,IAAI,CAACwJ,gBAAgB,IAAI,QAAQ,CAAC;QAC5D,IAAI+S,GAAG,EAAE;UACLrE,GAAG,CAACvY,aAAa,CAAC4c,GAAG,CAAC3rB,KAAK,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC;IACF;IACAsnB,GAAG,CAAC5e,aAAa,CAACsW,IAAI,CAAC1f,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyO,SAAS,CAAC,MAAM;MAC5C;MACA,IAAI,IAAI,CAAC4b,WAAW,EAAE;QAClBrC,GAAG,CAACrb,UAAU,CAAC,IAAI,CAAC0d,WAAW,CAACzC,QAAQ,CAAC;QACzC;MACJ;MACA;MACA;MACA,IAAIjZ,MAAM,GAAG,IAAI,CAAC5N,OAAO,CAAC+gB,aAAa,CAAC3E,aAAa;MACrD,OAAOxO,MAAM,EAAE;QACX,IAAIA,MAAM,CAAC0G,SAAS,CAAC1P,QAAQ,CAACqlB,eAAe,CAAC,EAAE;UAC5ChD,GAAG,CAACrb,UAAU,CAACse,OAAO,CAACpI,cAAc,CAACvgB,IAAI,CAACiZ,IAAI,IAAI;YAC/C,OAAOA,IAAI,CAACxa,OAAO,CAAC+gB,aAAa,KAAKnT,MAAM;UAChD,CAAC,CAAC,EAAEiZ,QAAQ,IAAI,IAAI,CAAC;UACrB;QACJ;QACAjZ,MAAM,GAAGA,MAAM,CAACwO,aAAa;MACjC;IACJ,CAAC,CAAC;EACN;EACA;EACAkK,aAAaA,CAACW,GAAG,EAAE;IACfA,GAAG,CAAC3e,OAAO,CAACoF,SAAS,CAAC6d,UAAU,IAAI;MAChC,IAAI,CAACjjB,OAAO,CAACsf,IAAI,CAAC;QAAEroB,MAAM,EAAE,IAAI;QAAE2E,KAAK,EAAEqnB,UAAU,CAACrnB;MAAM,CAAC,CAAC;MAC5D;MACA;MACA,IAAI,CAAC2hB,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFV,GAAG,CAAC1e,QAAQ,CAACmF,SAAS,CAAC8d,YAAY,IAAI;MACnC,IAAI,CAACjjB,QAAQ,CAACqf,IAAI,CAAC;QAAEroB,MAAM,EAAE,IAAI;QAAE2E,KAAK,EAAEsnB,YAAY,CAACtnB;MAAM,CAAC,CAAC;IACnE,CAAC,CAAC;IACF+iB,GAAG,CAACze,KAAK,CAACkF,SAAS,CAAC+d,QAAQ,IAAI;MAC5B,IAAI,CAACjjB,KAAK,CAACof,IAAI,CAAC;QACZroB,MAAM,EAAE,IAAI;QACZ8L,QAAQ,EAAEogB,QAAQ,CAACpgB,QAAQ;QAC3ByE,SAAS,EAAE2b,QAAQ,CAAC3b,SAAS;QAC7B5L,KAAK,EAAEunB,QAAQ,CAACvnB;MACpB,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC2hB,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFV,GAAG,CAACxe,OAAO,CAACiF,SAAS,CAACge,UAAU,IAAI;MAChC,IAAI,CAACjjB,OAAO,CAACmf,IAAI,CAAC;QACd1d,SAAS,EAAEwhB,UAAU,CAACxhB,SAAS,CAACic,IAAI;QACpCvT,IAAI,EAAE,IAAI;QACVH,YAAY,EAAEiZ,UAAU,CAACjZ;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;IACFwU,GAAG,CAACve,MAAM,CAACgF,SAAS,CAACie,SAAS,IAAI;MAC9B,IAAI,CAACjjB,MAAM,CAACkf,IAAI,CAAC;QACb1d,SAAS,EAAEyhB,SAAS,CAACzhB,SAAS,CAACic,IAAI;QACnCvT,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACFqU,GAAG,CAACte,OAAO,CAAC+E,SAAS,CAACma,SAAS,IAAI;MAC/B,IAAI,CAAClf,OAAO,CAACif,IAAI,CAAC;QACd/U,aAAa,EAAEgV,SAAS,CAAChV,aAAa;QACtCJ,YAAY,EAAEoV,SAAS,CAACpV,YAAY;QACpCK,iBAAiB,EAAE+U,SAAS,CAAC/U,iBAAiB,CAACqT,IAAI;QACnDjc,SAAS,EAAE2d,SAAS,CAAC3d,SAAS,CAACic,IAAI;QACnCzT,sBAAsB,EAAEmV,SAAS,CAACnV,sBAAsB;QACxDE,IAAI,EAAE,IAAI;QACVvH,QAAQ,EAAEwc,SAAS,CAACxc,QAAQ;QAC5ByE,SAAS,EAAE+X,SAAS,CAAC/X,SAAS;QAC9B5L,KAAK,EAAE2jB,SAAS,CAAC3jB;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAkiB,eAAeA,CAAC3D,MAAM,EAAE;IACpB,MAAM;MAAEhM,QAAQ;MAAErO,cAAc;MAAE4C,iBAAiB;MAAE2I,YAAY;MAAEpG,eAAe;MAAEua,gBAAgB;MAAEoD,mBAAmB;MAAE3S;IAAkB,CAAC,GAAGkK,MAAM;IACvJ,IAAI,CAAC7b,QAAQ,GAAGkhB,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAGA,gBAAgB;IACnE,IAAI,CAAC1f,cAAc,GAAGA,cAAc,IAAI,CAAC;IACzC,IAAIqO,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;IACA,IAAIzL,iBAAiB,EAAE;MACnB,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC9C;IACA,IAAI2I,YAAY,EAAE;MACd,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC;IACA,IAAIpG,eAAe,EAAE;MACjB,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;IACA,IAAI2d,mBAAmB,EAAE;MACrB,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAClD;IACA,IAAI3S,gBAAgB,EAAE;MAClB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IAC5C;EACJ;EACA;EACAoS,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAAC1jB,QAAQ,CAAC6jB,OAAO,CAChBnM,IAAI,CAAC5f,SAAS,CAAC,IAAI,CAACkI,QAAQ,CAAC;IAClC;IACA/H,GAAG,CAAEmN,OAAO,IAAK;MACb,MAAMuf,mBAAmB,GAAGvf,OAAO,CAC9BoT,MAAM,CAACvY,MAAM,IAAIA,MAAM,CAACoiB,WAAW,KAAK,IAAI,CAAC,CAC7CtqB,GAAG,CAACkI,MAAM,IAAIA,MAAM,CAAClH,OAAO,CAAC;MAClC;MACA;MACA;MACA,IAAI,IAAI,CAACoqB,WAAW,IAAI,IAAI,CAACc,mBAAmB,EAAE;QAC9CU,mBAAmB,CAACvP,IAAI,CAAC,IAAI,CAACrc,OAAO,CAAC;MAC1C;MACA,IAAI,CAAC6mB,QAAQ,CAACza,WAAW,CAACwf,mBAAmB,CAAC;IAClD,CAAC,CAAC;IACF;IACAzsB,SAAS,CAAEkN,OAAO,IAAK;MACnB,OAAOxN,KAAK,CAAC,GAAGwN,OAAO,CAACrN,GAAG,CAAC4T,IAAI,IAAI;QAChC,OAAOA,IAAI,CAACwW,aAAa,CAACzK,IAAI,CAAC5f,SAAS,CAAC6T,IAAI,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,EAAE9T,SAAS,CAAC,IAAI,CAACknB,UAAU,CAAC,CAAC,CAC1BtY,SAAS,CAACme,cAAc,IAAI;MAC7B;MACA,MAAMC,OAAO,GAAG,IAAI,CAACjF,QAAQ;MAC7B,MAAM3f,MAAM,GAAG2kB,cAAc,CAAC7rB,OAAO,CAAC+gB,aAAa;MACnD8K,cAAc,CAACjlB,QAAQ,GAAGklB,OAAO,CAACvd,aAAa,CAACrH,MAAM,CAAC,GAAG4kB,OAAO,CAACtd,YAAY,CAACtH,MAAM,CAAC;IAC1F,CAAC,CAAC;EACN;AACJ;AACAgjB,OAAO,CAACpI,cAAc,GAAG,EAAE;AAC3BoI,OAAO,CAAClH,IAAI,YAAA+I,gBAAA7I,CAAA;EAAA,YAAAA,CAAA,IAA6FgH,OAAO,EAt+BRptB,EAAE,CAAAorB,iBAAA,CAs+BwBprB,EAAE,CAACqrB,UAAU,GAt+BvCrrB,EAAE,CAAAorB,iBAAA,CAs+BkD1C,aAAa,OAt+BjE1oB,EAAE,CAAAorB,iBAAA,CAs+B4GtqB,QAAQ,GAt+BtHd,EAAE,CAAAorB,iBAAA,CAs+BiIprB,EAAE,CAACsmB,MAAM,GAt+B5ItmB,EAAE,CAAAorB,iBAAA,CAs+BuJprB,EAAE,CAACkvB,gBAAgB,GAt+B5KlvB,EAAE,CAAAorB,iBAAA,CAs+BuL9C,eAAe,MAt+BxMtoB,EAAE,CAAAorB,iBAAA,CAs+BmO9oB,EAAE,CAACkpB,cAAc,MAt+BtPxrB,EAAE,CAAAorB,iBAAA,CAs+BiRnE,QAAQ,GAt+B3RjnB,EAAE,CAAAorB,iBAAA,CAs+BsSprB,EAAE,CAACsrB,iBAAiB,GAt+B5TtrB,EAAE,CAAAorB,iBAAA,CAs+BuUgB,eAAe,OAt+BxVpsB,EAAE,CAAAorB,iBAAA,CAs+B+X9D,eAAe;AAAA,CAA4E;AACpkB8F,OAAO,CAACzF,IAAI,kBAv+B4F3nB,EAAE,CAAA4nB,iBAAA;EAAAze,IAAA,EAu+BbikB,OAAO;EAAAvF,SAAA;EAAAsH,cAAA,WAAAC,uBAAAvD,EAAA,EAAAC,GAAA,EAAAuD,QAAA;IAAA,IAAAxD,EAAA;MAv+BI7rB,EAAE,CAAAsvB,cAAA,CAAAD,QAAA,EAu+B2jCtC,gBAAgB;MAv+B7kC/sB,EAAE,CAAAsvB,cAAA,CAAAD,QAAA,EAu+BkqC3C,oBAAoB;MAv+BxrC1sB,EAAE,CAAAsvB,cAAA,CAAAD,QAAA,EAu+BovCjD,eAAe;IAAA;IAAA,IAAAP,EAAA;MAAA,IAAA0D,EAAA;MAv+BrwCvvB,EAAE,CAAAwvB,cAAA,CAAAD,EAAA,GAAFvvB,EAAE,CAAAyvB,WAAA,QAAA3D,GAAA,CAAAlc,gBAAA,GAAA2f,EAAA,CAAAG,KAAA;MAAF1vB,EAAE,CAAAwvB,cAAA,CAAAD,EAAA,GAAFvvB,EAAE,CAAAyvB,WAAA,QAAA3D,GAAA,CAAAhc,oBAAA,GAAAyf,EAAA,CAAAG,KAAA;MAAF1vB,EAAE,CAAAwvB,cAAA,CAAAD,EAAA,GAAFvvB,EAAE,CAAAyvB,WAAA,QAAA3D,GAAA,CAAA3hB,QAAA,GAAAolB,EAAA;IAAA;EAAA;EAAA9D,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAgE,qBAAA9D,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF7rB,EAAE,CAAAgsB,WAAA,sBAAAF,GAAA,CAAAhiB,QAAA,uBAAAgiB,GAAA,CAAA/B,QAAA,CAAAzc,UAAA;IAAA;EAAA;EAAAwa,MAAA;IAAAuB,IAAA;IAAA1P,QAAA;IAAAyU,mBAAA;IAAA3d,eAAA;IAAAnF,cAAA;IAAAwiB,gBAAA;IAAAhkB,QAAA;IAAAoE,iBAAA;IAAA2I,YAAA;IAAA4E,gBAAA;EAAA;EAAAwQ,OAAA;IAAAzgB,OAAA;IAAAC,QAAA;IAAAC,KAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC,OAAA;IAAAC,KAAA;EAAA;EAAAic,QAAA;EAAAC,UAAA;EAAAC,QAAA,GAAFjoB,EAAE,CAAAkoB,kBAAA,CAu+B+7B,CAAC;IAAEC,OAAO,EAAEb,eAAe;IAAEc,WAAW,EAAEgF;EAAQ,CAAC,CAAC,GAv+Br/BptB,EAAE,CAAA4vB,oBAAA;AAAA,EAu+Bs1C;AACh8C;EAAA,QAAAhJ,SAAA,oBAAAA,SAAA,KAx+BwG5mB,EAAE,CAAA6mB,iBAAA,CAw+BVuG,OAAO,EAAc,CAAC;IAC1GjkB,IAAI,EAAE/I,SAAS;IACf0mB,IAAI,EAAE,CAAC;MACChe,QAAQ,EAAE,WAAW;MACrBif,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,IAAI;MAChBmE,IAAI,EAAE;QACF,OAAO,EAAEgB,eAAe;QACxB,2BAA2B,EAAE,UAAU;QACvC,2BAA2B,EAAE;MACjC,CAAC;MACD9E,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEb,eAAe;QAAEc,WAAW,EAAEgF;MAAQ,CAAC;IAClE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjkB,IAAI,EAAEnJ,EAAE,CAACqrB;IAAW,CAAC,EAAE;MAAEliB,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QACvF5d,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAAC4B,aAAa;MACxB,CAAC,EAAE;QACCvf,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAE3I;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2I,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QAClC5d,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAAChmB,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEqI,IAAI,EAAEnJ,EAAE,CAACsmB;IAAO,CAAC,EAAE;MAAEnd,IAAI,EAAEnJ,EAAE,CAACkvB;IAAiB,CAAC,EAAE;MAAE/lB,IAAI,EAAEiH,SAAS;MAAE2W,UAAU,EAAE,CAAC;QACtF5d,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAACwB,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAEnf,IAAI,EAAE7G,EAAE,CAACkpB,cAAc;MAAEzE,UAAU,EAAE,CAAC;QAC1C5d,IAAI,EAAE5I;MACV,CAAC;IAAE,CAAC,EAAE;MAAE4I,IAAI,EAAE8d;IAAS,CAAC,EAAE;MAAE9d,IAAI,EAAEnJ,EAAE,CAACsrB;IAAkB,CAAC,EAAE;MAAEniB,IAAI,EAAEkjB,aAAa;MAAEtF,UAAU,EAAE,CAAC;QAC1F5d,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAEzI;MACV,CAAC,EAAE;QACCyI,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAACsF,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAEjjB,IAAI,EAAEikB,OAAO;MAAErG,UAAU,EAAE,CAAC;QAChC5d,IAAI,EAAE5I;MACV,CAAC,EAAE;QACC4I,IAAI,EAAE3I;MACV,CAAC,EAAE;QACC2I,IAAI,EAAEjJ,MAAM;QACZ4mB,IAAI,EAAE,CAACQ,eAAe;MAC1B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEnd,QAAQ,EAAE,CAAC;MACvChB,IAAI,EAAExI,eAAe;MACrBmmB,IAAI,EAAE,CAACsF,eAAe,EAAE;QAAEyD,WAAW,EAAE;MAAK,CAAC;IACjD,CAAC,CAAC;IAAEjgB,gBAAgB,EAAE,CAAC;MACnBzG,IAAI,EAAEvI,YAAY;MAClBkmB,IAAI,EAAE,CAACiG,gBAAgB;IAC3B,CAAC,CAAC;IAAEjd,oBAAoB,EAAE,CAAC;MACvB3G,IAAI,EAAEvI,YAAY;MAClBkmB,IAAI,EAAE,CAAC4F,oBAAoB;IAC/B,CAAC,CAAC;IAAErD,IAAI,EAAE,CAAC;MACPlgB,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEnN,QAAQ,EAAE,CAAC;MACXxQ,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEsH,mBAAmB,EAAE,CAAC;MACtBjlB,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAErW,eAAe,EAAE,CAAC;MAClBtH,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAExb,cAAc,EAAE,CAAC;MACjBnC,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEgH,gBAAgB,EAAE,CAAC;MACnB3kB,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEhd,QAAQ,EAAE,CAAC;MACXX,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE5Y,iBAAiB,EAAE,CAAC;MACpB/E,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAEjQ,YAAY,EAAE,CAAC;MACf1N,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAErL,gBAAgB,EAAE,CAAC;MACnBtS,IAAI,EAAE9I,KAAK;MACXymB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEtb,OAAO,EAAE,CAAC;MACVrC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAErb,QAAQ,EAAE,CAAC;MACXtC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEpb,KAAK,EAAE,CAAC;MACRvC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEnb,OAAO,EAAE,CAAC;MACVxC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAElb,MAAM,EAAE,CAAC;MACTzC,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEjb,OAAO,EAAE,CAAC;MACV1C,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEhb,KAAK,EAAE,CAAC;MACR3C,IAAI,EAAE1I,MAAM;MACZqmB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgJ,oBAAoB,GAAG,CACzBnH,WAAW,EACXnB,gBAAgB,EAChB4F,OAAO,EACPf,aAAa,EACbW,cAAc,EACdL,kBAAkB,CACrB;AACD,MAAMoD,cAAc,CAAC;AAErBA,cAAc,CAAC7J,IAAI,YAAA8J,uBAAA5J,CAAA;EAAA,YAAAA,CAAA,IAA6F2J,cAAc;AAAA,CAAkD;AAChLA,cAAc,CAACE,IAAI,kBApmCqFjwB,EAAE,CAAAkwB,gBAAA;EAAA/mB,IAAA,EAomCO4mB;AAAc,EAUjG;AAC9BA,cAAc,CAACI,IAAI,kBA/mCqFnwB,EAAE,CAAAowB,gBAAA;EAAA/H,SAAA,EA+mCkC,CAACpB,QAAQ,CAAC;EAAAoJ,OAAA,GAAYrvB,mBAAmB;AAAA,EAAI;AACzL;EAAA,QAAA4lB,SAAA,oBAAAA,SAAA,KAhnCwG5mB,EAAE,CAAA6mB,iBAAA,CAgnCVkJ,cAAc,EAAc,CAAC;IACjH5mB,IAAI,EAAEtI,QAAQ;IACdimB,IAAI,EAAE,CAAC;MACCuJ,OAAO,EAAEP,oBAAoB;MAC7BQ,OAAO,EAAE,CAACtvB,mBAAmB,EAAE,GAAG8uB,oBAAoB,CAAC;MACvDzH,SAAS,EAAE,CAACpB,QAAQ;IACxB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASqB,eAAe,EAAE8D,eAAe,EAAE9E,eAAe,EAAEoF,oBAAoB,EAAEK,gBAAgB,EAAErE,aAAa,EAAEnB,mBAAmB,EAAE6F,OAAO,EAAEf,aAAa,EAAEM,kBAAkB,EAAEK,cAAc,EAAErE,WAAW,EAAEnB,gBAAgB,EAAEP,QAAQ,EAAE8I,cAAc,EAAEjL,gBAAgB,EAAEjb,OAAO,EAAE+W,WAAW,EAAExD,aAAa,EAAEZ,eAAe,EAAEO,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nexport function publish(selector) {\n  return selector ? multicast(() => new Subject(), selector) : multicast(new Subject());\n}", "map": {"version": 3, "names": ["Subject", "multicast", "publish", "selector"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/publish.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nexport function publish(selector) {\n    return selector ?\n        multicast(() => new Subject(), selector) :\n        multicast(new Subject());\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAE;EAC9B,OAAOA,QAAQ,GACXF,SAAS,CAAC,MAAM,IAAID,OAAO,CAAC,CAAC,EAAEG,QAAQ,CAAC,GACxCF,SAAS,CAAC,IAAID,OAAO,CAAC,CAAC,CAAC;AAChC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export function isPromise(value) {\n  return !!value && typeof value.subscribe !== 'function' && typeof value.then === 'function';\n}", "map": {"version": 3, "names": ["isPromise", "value", "subscribe", "then"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isPromise.js"], "sourcesContent": ["export function isPromise(value) {\n    return !!value && typeof value.subscribe !== 'function' && typeof value.then === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAE;EAC7B,OAAO,CAAC,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACC,SAAS,KAAK,UAAU,IAAI,OAAOD,KAAK,CAACE,IAAI,KAAK,UAAU;AAC/F"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import arraySome from './_arraySome.js';\nimport createOver from './_createOver.js';\n\n/**\n * Creates a function that checks if **any** of the `predicates` return\n * truthy when invoked with the arguments it receives.\n *\n * Following shorthands are possible for providing predicates.\n * Pass an `Object` and it will be used as an parameter for `_.matches` to create the predicate.\n * Pass an `Array` of parameters for `_.matchesProperty` and the predicate will be created using them.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {...(Function|Function[])} [predicates=[_.identity]]\n *  The predicates to check.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var func = _.overSome([Boolean, isFinite]);\n *\n * func('1');\n * // => true\n *\n * func(null);\n * // => true\n *\n * func(NaN);\n * // => false\n *\n * var matchesFunc = _.overSome([{ 'a': 1 }, { 'a': 2 }])\n * var matchesPropertyFunc = _.overSome([['a', 1], ['a', 2]])\n */\nvar overSome = createOver(arraySome);\nexport default overSome;", "map": {"version": 3, "names": ["arraySome", "createOver", "overSome"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/overSome.js"], "sourcesContent": ["import arraySome from './_arraySome.js';\nimport createOver from './_createOver.js';\n\n/**\n * Creates a function that checks if **any** of the `predicates` return\n * truthy when invoked with the arguments it receives.\n *\n * Following shorthands are possible for providing predicates.\n * Pass an `Object` and it will be used as an parameter for `_.matches` to create the predicate.\n * Pass an `Array` of parameters for `_.matchesProperty` and the predicate will be created using them.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {...(Function|Function[])} [predicates=[_.identity]]\n *  The predicates to check.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var func = _.overSome([Boolean, isFinite]);\n *\n * func('1');\n * // => true\n *\n * func(null);\n * // => true\n *\n * func(NaN);\n * // => false\n *\n * var matchesFunc = _.overSome([{ 'a': 1 }, { 'a': 2 }])\n * var matchesPropertyFunc = _.overSome([['a', 1], ['a', 2]])\n */\nvar overSome = createOver(arraySome);\n\nexport default overSome;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGD,UAAU,CAACD,SAAS,CAAC;AAEpC,eAAeE,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
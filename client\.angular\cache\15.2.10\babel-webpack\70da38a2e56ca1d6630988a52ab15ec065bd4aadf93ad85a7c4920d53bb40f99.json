{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"app/services/user.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@core/pipes/filter.pipe\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@ng-select/ng-select\";\nfunction ModalFollowsComponent_div_10_ng_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r4.name, \" \");\n  }\n}\nfunction ModalFollowsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"ng-select\", 13);\n    i0.ɵɵlistener(\"ngModelChange\", function ModalFollowsComponent_div_10_Template_ng_select_ngModelChange_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const select_r2 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(select_r2.filter = $event);\n    })(\"change\", function ModalFollowsComponent_div_10_Template_ng_select_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const select_r2 = restoredCtx.$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onSelected($event, select_r2));\n    });\n    i0.ɵɵtemplate(2, ModalFollowsComponent_div_10_ng_option_2_Template, 2, 2, \"ng-option\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const select_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"placeholder\", select_r2.placeholder)(\"clearable\", false)(\"ngModel\", select_r2.filter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", select_r2.options);\n  }\n}\nfunction ModalFollowsComponent_ng_container_18_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 17)(1, \"div\", 5)(2, \"div\", 18)(3, \"img\", 19);\n    i0.ɵɵlistener(\"error\", function ModalFollowsComponent_ng_container_18_li_1_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10._commonsService.onloadImgErr($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 21)(8, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function ModalFollowsComponent_ng_container_18_li_1_Template_input_change_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.changeFollow(item_r8));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"label\", 23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", item_r8.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.name, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"switch_\", item_r8.id, \"\");\n    i0.ɵɵproperty(\"checked\", item_r8.isFollow);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"switch_\", item_r8.id, \"\");\n  }\n}\nfunction ModalFollowsComponent_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ModalFollowsComponent_ng_container_18_li_1_Template, 10, 5, \"li\", 16);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r8.message);\n  }\n}\nexport class ModalFollowsComponent {\n  constructor(_modalService, _commonsService, _userService, _translateService, _filter) {\n    this._modalService = _modalService;\n    this._commonsService = _commonsService;\n    this._userService = _userService;\n    this._translateService = _translateService;\n    this._filter = _filter;\n    this.search = '';\n    this.filteredItems = [];\n    this.onSuccess = new EventEmitter();\n  }\n  ngOnInit() {\n    // deep clone list original to filter\n    this.filteredItems = JSON.parse(JSON.stringify(this.data.list));\n    if (this.data.selects && this.data.selects.length > 0) {\n      this.filterList(true);\n    }\n  }\n  close() {\n    this._modalService.dismiss();\n  }\n  changeFollow(item) {\n    // console.log(this.onToggle);\n    this.onToggle(item);\n    item.isFollow = !item.isFollow;\n    this.data.list.forEach(element => {\n      if (element.id == item.id) {\n        element.isFollow = item.isFollow;\n      }\n    });\n  }\n  onSelected(item, select) {\n    // console.log(item, select);\n    // console.log(select.filter);\n    // clone filteredItems\n    this.filterList();\n  }\n  filterList(defaultFilter = false) {\n    this.filteredItems = JSON.parse(JSON.stringify(this.data.list));\n    this.data.selects.forEach(select => {\n      if (defaultFilter) {\n        select.filter = select.default;\n      }\n      this.filteredItems = this._filter.transform(this.filteredItems, select.filter, select.key, true);\n    });\n  }\n  static #_ = this.ɵfac = function ModalFollowsComponent_Factory(t) {\n    return new (t || ModalFollowsComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.FilterPipe));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalFollowsComponent,\n    selectors: [[\"app-modal-follows\"]],\n    inputs: {\n      data: \"data\",\n      onToggle: \"onToggle\"\n    },\n    outputs: {\n      onSuccess: \"onSuccess\"\n    },\n    decls: 20,\n    vars: 18,\n    consts: [[1, \"modal-header\", \"bg-white\"], [\"id\", \"label_follows\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\", 2, \"height\", \"70vh\"], [1, \"row\"], [\"class\", \"col mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\"], [\"for\", \"search\"], [\"type\", \"text\", \"name\", \"search\", \"id\", \"search\", 1, \"form-control\", 3, \"placeholder\", \"ngModel\", \"ngModelChange\"], [1, \"list-group\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col\", \"mb-1\"], [3, \"placeholder\", \"clearable\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"class\", \"list-group-item border-0\", 4, \"ngIf\"], [1, \"list-group-item\", \"border-0\"], [1, \"col-auto\", \"p-0\"], [\"height\", \"32\", \"width\", \"32\", \"alt\", \"datatable-avatar\", 1, \"rounded-circle\", \"mr-1\", 3, \"src\", \"error\"], [1, \"col\", \"text-left\"], [1, \"custom-control\", \"custom-control-primary\", \"custom-switch\"], [\"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"id\", \"checked\", \"change\"], [1, \"custom-control-label\", 3, \"for\"]],\n    template: function ModalFollowsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalFollowsComponent_Template_button_click_5_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵelementStart(6, \"span\", 3);\n        i0.ɵɵtext(7, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 4)(9, \"div\", 5);\n        i0.ɵɵtemplate(10, ModalFollowsComponent_div_10_Template, 3, 4, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 7)(12, \"label\", 8);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function ModalFollowsComponent_Template_input_ngModelChange_15_listener($event) {\n          return ctx.search = $event;\n        });\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"ul\", 10);\n        i0.ɵɵtemplate(18, ModalFollowsComponent_ng_container_18_Template, 2, 1, \"ng-container\", 11);\n        i0.ɵɵpipe(19, \"filter\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 7, \"Notification\"), \" \", i0.ɵɵpipeBind1(4, 9, ctx.data == null ? null : ctx.data.title), \" \");\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngForOf\", ctx.data.selects);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 11, \"Search\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(16, 13, \"Search\"));\n        i0.ɵɵproperty(\"ngModel\", ctx.search);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(19, 15, ctx.filteredItems, ctx.search));\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i8.NgSelectComponent, i8.ɵr, i5.FilterPipe, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;;;;;;;;;;ICc9DC,EAAA,CAAAC,cAAA,oBAAiE;IAC7DD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;IAC5DN,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,IAAA,MACJ;;;;;;IALRT,EAAA,CAAAC,cAAA,cAA0D;IACYD,EAAA,CAAAU,UAAA,2BAAAC,yEAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAajB,EAAA,CAAAkB,WAAA,CAAAF,SAAA,CAAAG,MAAA,GAAAP,MAAA,CACtF;IAAA,EADoG,oBAAAQ,kEAAAR,MAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAI,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAC/EtB,EAAA,CAAAkB,WAAA,CAAAG,MAAA,CAAAE,UAAA,CAAAX,MAAA,EAAAI,SAAA,CAAyB;IAAA,EADsD;IAEzFhB,EAAA,CAAAwB,UAAA,IAAAC,iDAAA,wBAEY;IAChBzB,EAAA,CAAAG,YAAA,EAAY;;;;IALDH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAI,UAAA,gBAAAY,SAAA,CAAAU,WAAA,CAAkC,gCAAAV,SAAA,CAAAG,MAAA;IAEbnB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAY,SAAA,CAAAW,OAAA,CAAiB;;;;;;IAajD3B,EAAA,CAAAC,cAAA,aAA2D;IAI3CD,EAAA,CAAAU,UAAA,mBAAAkB,yEAAAhB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAkB,WAAA,CAAAY,OAAA,CAAAC,eAAA,CAAAC,YAAA,CAAApB,MAAA,CAAoC;IAAA,EAAC;IADlDZ,EAAA,CAAAG,YAAA,EACmD;IAEvDH,EAAA,CAAAC,cAAA,cAA2B;IACvBD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0B;IAGYD,EAAA,CAAAU,UAAA,oBAAAuB,4EAAA;MAAAjC,EAAA,CAAAc,aAAA,CAAAe,IAAA;MAAA,MAAAK,OAAA,GAAAlC,EAAA,CAAAsB,aAAA,GAAAL,SAAA;MAAA,MAAAkB,OAAA,GAAAnC,EAAA,CAAAsB,aAAA;MAAA,OAAUtB,EAAA,CAAAkB,WAAA,CAAAiB,OAAA,CAAAC,YAAA,CAAAF,OAAA,CAAkB;IAAA,EAAC;IAD3DlC,EAAA,CAAAG,YAAA,EAC4D;IAC5DH,EAAA,CAAAqC,SAAA,gBAAqE;IACzErC,EAAA,CAAAG,YAAA,EAAM;;;;IAXyEH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,QAAA8B,OAAA,CAAAI,IAAA,EAAAtC,EAAA,CAAAuC,aAAA,CAAiB;IAIhGvC,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAA0B,OAAA,CAAAzB,IAAA,MACJ;IAG4DT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAwC,sBAAA,kBAAAN,OAAA,CAAA5B,EAAA,KAAuB;IACvEN,EAAA,CAAAI,UAAA,YAAA8B,OAAA,CAAAO,QAAA,CAAyB;IACOzC,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAwC,sBAAA,mBAAAN,OAAA,CAAA5B,EAAA,KAAwB;;;;;IAdhFN,EAAA,CAAA0C,uBAAA,GAAiE;IAC7D1C,EAAA,CAAAwB,UAAA,IAAAmB,mDAAA,kBAiBK;IACT3C,EAAA,CAAA4C,qBAAA,EAAe;;;;IAlB2B5C,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,UAAA8B,OAAA,CAAAW,OAAA,CAAmB;;;ADRrE,OAAM,MAAOC,qBAAqB;EAUhCC,YACSC,aAA6B,EAC7BjB,eAA+B,EAC/BkB,YAAyB,EACzBC,iBAAmC,EACnCC,OAAmB;IAJnB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAjB,eAAe,GAAfA,eAAe;IACf,KAAAkB,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IAdhB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,aAAa,GAAU,EAAE;IAOf,KAAAC,SAAS,GAAsB,IAAIvD,YAAY,EAAO;EAO7D;EAEHwD,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,aAAa,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC;IAC/D,IAAI,IAAI,CAACD,IAAI,CAACE,OAAO,IAAI,IAAI,CAACF,IAAI,CAACE,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACrD,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;;EAEzB;EAEAC,KAAKA,CAAA;IACH,IAAI,CAAChB,aAAa,CAACiB,OAAO,EAAE;EAC9B;EAEA7B,YAAYA,CAAC8B,IAAI;IACf;IACA,IAAI,CAACC,QAAQ,CAACD,IAAI,CAAC;IACnBA,IAAI,CAACzB,QAAQ,GAAG,CAACyB,IAAI,CAACzB,QAAQ;IAC9B,IAAI,CAACkB,IAAI,CAACC,IAAI,CAACQ,OAAO,CAAEC,OAAO,IAAI;MACjC,IAAIA,OAAO,CAAC/D,EAAE,IAAI4D,IAAI,CAAC5D,EAAE,EAAE;QACzB+D,OAAO,CAAC5B,QAAQ,GAAGyB,IAAI,CAACzB,QAAQ;;IAEpC,CAAC,CAAC;EACJ;EAEAlB,UAAUA,CAAC2C,IAAI,EAAEI,MAAM;IACrB;IACA;IACA;IACA,IAAI,CAACP,UAAU,EAAE;EACnB;EAEAA,UAAUA,CAACQ,aAAa,GAAG,KAAK;IAC9B,IAAI,CAAClB,aAAa,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC;IAC/D,IAAI,CAACD,IAAI,CAACE,OAAO,CAACO,OAAO,CAAEE,MAAM,IAAI;MACnC,IAAIC,aAAa,EAAE;QACjBD,MAAM,CAACnD,MAAM,GAAGmD,MAAM,CAACE,OAAO;;MAEhC,IAAI,CAACnB,aAAa,GAAG,IAAI,CAACF,OAAO,CAACsB,SAAS,CACzC,IAAI,CAACpB,aAAa,EAClBiB,MAAM,CAACnD,MAAM,EACbmD,MAAM,CAACI,GAAG,EACV,IAAI,CACL;IACH,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA;qBA7DU7B,qBAAqB,EAAA9C,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9E,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhF,EAAA,CAAA4E,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlF,EAAA,CAAA4E,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAApF,EAAA,CAAA4E,iBAAA,CAAAS,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;UAArBzC,qBAAqB;IAAA0C,SAAA;IAAAC,MAAA;MAAA9B,IAAA;MAAAQ,QAAA;IAAA;IAAAuB,OAAA;MAAApC,SAAA;IAAA;IAAAqC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnBlChG,EAAA,CAAAC,cAAA,aAAmC;QAE3BD,EAAA,CAAAE,MAAA,GACJ;;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,gBAAyE;QAAlBD,EAAA,CAAAU,UAAA,mBAAAwF,uDAAA;UAAA,OAASD,GAAA,CAAAjC,KAAA,EAAO;QAAA,EAAC;QACpEhE,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAE,MAAA,aAAO;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAI/CH,EAAA,CAAAC,cAAA,aAAwE;QAEhED,EAAA,CAAAwB,UAAA,KAAA2E,qCAAA,iBAOM;QACVnG,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAAwB;QACAD,EAAA,CAAAE,MAAA,IAAwB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpDH,EAAA,CAAAC,cAAA,gBACyB;QAArBD,EAAA,CAAAU,UAAA,2BAAA0F,+DAAAxF,MAAA;UAAA,OAAAqF,GAAA,CAAA7C,MAAA,GAAAxC,MAAA;QAAA,EAAoB;;QADxBZ,EAAA,CAAAG,YAAA,EACyB;QAE7BH,EAAA,CAAAC,cAAA,cAAuB;QACnBD,EAAA,CAAAwB,UAAA,KAAA6E,8CAAA,2BAmBe;;QACnBrG,EAAA,CAAAG,YAAA,EAAK;;;QA5CDH,EAAA,CAAAO,SAAA,GACJ;QADIP,EAAA,CAAAsG,kBAAA,MAAAtG,EAAA,CAAAuG,WAAA,6BAAAvG,EAAA,CAAAuG,WAAA,OAAAN,GAAA,CAAAtC,IAAA,kBAAAsC,GAAA,CAAAtC,IAAA,CAAA6C,KAAA,OACJ;QAQ6CxG,EAAA,CAAAO,SAAA,GAAe;QAAfP,EAAA,CAAAI,UAAA,YAAA6F,GAAA,CAAAtC,IAAA,CAAAE,OAAA,CAAe;QAUpC7D,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAAyG,iBAAA,CAAAzG,EAAA,CAAAuG,WAAA,mBAAwB;QACsBvG,EAAA,CAAAO,SAAA,GAAsC;QAAtCP,EAAA,CAAA0G,qBAAA,gBAAA1G,EAAA,CAAAuG,WAAA,mBAAsC;QACpGvG,EAAA,CAAAI,UAAA,YAAA6F,GAAA,CAAA7C,MAAA,CAAoB;QAGOpD,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA2G,WAAA,SAAAV,GAAA,CAAA5C,aAAA,EAAA4C,GAAA,CAAA7C,MAAA,EAAgC", "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r4", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "ɵɵlistener", "ModalFollowsComponent_div_10_Template_ng_select_ngModelChange_1_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r6", "select_r2", "$implicit", "ɵɵresetView", "filter", "ModalFollowsComponent_div_10_Template_ng_select_change_1_listener", "ctx_r7", "ɵɵnextContext", "onSelected", "ɵɵtemplate", "ModalFollowsComponent_div_10_ng_option_2_Template", "placeholder", "options", "ModalFollowsComponent_ng_container_18_li_1_Template_img_error_3_listener", "_r11", "ctx_r10", "_commonsService", "onloadImgErr", "ModalFollowsComponent_ng_container_18_li_1_Template_input_change_8_listener", "item_r8", "ctx_r12", "changeFollow", "ɵɵelement", "logo", "ɵɵsanitizeUrl", "ɵɵpropertyInterpolate1", "is<PERSON><PERSON>ow", "ɵɵelementContainerStart", "ModalFollowsComponent_ng_container_18_li_1_Template", "ɵɵelementContainerEnd", "message", "ModalFollowsComponent", "constructor", "_modalService", "_userService", "_translateService", "_filter", "search", "filteredItems", "onSuccess", "ngOnInit", "JSON", "parse", "stringify", "data", "list", "selects", "length", "filterList", "close", "dismiss", "item", "onToggle", "for<PERSON>ach", "element", "select", "defaultFilter", "default", "transform", "key", "_", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "CommonsService", "i3", "UserService", "i4", "TranslateService", "i5", "FilterPipe", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ModalFollowsComponent_Template", "rf", "ctx", "ModalFollowsComponent_Template_button_click_5_listener", "ModalFollowsComponent_div_10_Template", "ModalFollowsComponent_Template_input_ngModelChange_15_listener", "ModalFollowsComponent_ng_container_18_Template", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "title", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "ɵɵpipeBind2"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\notification\\modal-follows\\modal-follows.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\notification\\modal-follows\\modal-follows.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { FilterPipe } from '@core/pipes/filter.pipe';\r\n\r\nexport interface SelectInModal {\r\n  placeholder: string;\r\n  options: { id: string; name: string }[];\r\n  filter: string;\r\n  key: string;\r\n  default?: string;\r\n}\r\n@Component({\r\n  selector: 'app-modal-follows',\r\n  templateUrl: './modal-follows.component.html',\r\n  styleUrls: ['./modal-follows.component.scss'],\r\n})\r\nexport class ModalFollowsComponent implements OnInit {\r\n  search: string = '';\r\n  filteredItems: any[] = [];\r\n  @Input() data: {\r\n    title: string;\r\n    list: any[];\r\n    selects: SelectInModal[];\r\n  };\r\n  @Input() onToggle: (item) => void;\r\n  @Output() onSuccess: EventEmitter<any> = new EventEmitter<any>();\r\n  constructor(\r\n    public _modalService: NgbActiveModal,\r\n    public _commonsService: CommonsService,\r\n    public _userService: UserService,\r\n    public _translateService: TranslateService,\r\n    public _filter: FilterPipe\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // deep clone list original to filter\r\n    this.filteredItems = JSON.parse(JSON.stringify(this.data.list));\r\n    if (this.data.selects && this.data.selects.length > 0) {\r\n      this.filterList(true);\r\n    }\r\n  }\r\n  \r\n  close() {\r\n    this._modalService.dismiss();\r\n  }\r\n\r\n  changeFollow(item) {\r\n    // console.log(this.onToggle);\r\n    this.onToggle(item);\r\n    item.isFollow = !item.isFollow;\r\n    this.data.list.forEach((element) => {\r\n      if (element.id == item.id) {\r\n        element.isFollow = item.isFollow;\r\n      }\r\n    });\r\n  }\r\n\r\n  onSelected(item, select) {\r\n    // console.log(item, select);\r\n    // console.log(select.filter);\r\n    // clone filteredItems\r\n    this.filterList();\r\n  }\r\n\r\n  filterList(defaultFilter = false) {\r\n    this.filteredItems = JSON.parse(JSON.stringify(this.data.list));\r\n    this.data.selects.forEach((select) => {\r\n      if (defaultFilter) {\r\n        select.filter = select.default;\r\n      }\r\n      this.filteredItems = this._filter.transform(\r\n        this.filteredItems,\r\n        select.filter,\r\n        select.key,\r\n        true\r\n      );\r\n    });\r\n  }\r\n\r\n  // this.data.selects.forEach((select) => {\r\n  //   this.data.list = this._filter.transform(\r\n  //     this.data.list,\r\n  //     select.filter,\r\n  //     select.key\r\n  //   );\r\n  // });\r\n}\r\n", "<div class=\"modal-header bg-white\">\r\n    <h4 class=\"modal-title\" id=\"label_follows\">\r\n        {{'Notification' | translate}} {{ data?.title | translate }}\r\n    </h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"close()\">\r\n        <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n</div>\r\n\r\n<div class=\"modal-body\" style=\"height: 70vh;\" tabindex=\"0\" ngbAutofocus>\r\n    <div class=\"row\">\r\n        <div class=\"col mb-1\" *ngFor=\"let select of data.selects\">\r\n            <ng-select [placeholder]=\"select.placeholder\" [clearable]=\"false\" [(ngModel)]=\"select.filter\"\r\n                (change)=\"onSelected($event,select)\">\r\n                <ng-option *ngFor=\"let item of select.options\" [value]=\"item.id\">\r\n                    {{item.name}}\r\n                </ng-option>\r\n            </ng-select>\r\n        </div>\r\n    </div>\r\n    <div class=\"form-group\">\r\n        <label for=\"search\">{{'Search'| translate }}</label>\r\n        <input type=\"text\" class=\"form-control\" name=\"search\" id=\"search\" placeholder=\"{{'Search'| translate }}\"\r\n            [(ngModel)]=\"search\">\r\n    </div>\r\n    <ul class=\"list-group\">\r\n        <ng-container *ngFor=\"let item of filteredItems | filter:search\">\r\n            <li class=\"list-group-item border-0\" *ngIf=\"!item.message\">\r\n                <div class=\"row \">\r\n                    <div class=\"col-auto p-0\">\r\n                        <img height=\"32\" width=\"32\" alt=\"datatable-avatar\" class=\"rounded-circle mr-1\" [src]=\"item.logo\"\r\n                            (error)=\"_commonsService.onloadImgErr($event)\">\r\n                    </div>\r\n                    <div class=\"col text-left\">\r\n                        {{item.name}}\r\n                    </div>\r\n                    <div class=\"col-auto p-0\">\r\n                        <div class=\"custom-control custom-control-primary custom-switch\">\r\n                            <input type=\"checkbox\" class=\"custom-control-input\" id=\"switch_{{item.id}}\"\r\n                                [checked]=\"item.isFollow\" (change)=\"changeFollow(item)\">\r\n                            <label class=\"custom-control-label\" for=\"switch_{{item.id}}\"></label>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </li>\r\n        </ng-container>\r\n    </ul>\r\n\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
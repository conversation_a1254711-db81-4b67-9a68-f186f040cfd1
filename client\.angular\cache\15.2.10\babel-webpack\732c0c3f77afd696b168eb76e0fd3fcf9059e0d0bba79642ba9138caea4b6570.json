{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseWhile from './_baseWhile.js';\n\n/**\n * Creates a slice of `array` with elements taken from the end. Elements are\n * taken until `predicate` returns falsey. The predicate is invoked with\n * three arguments: (value, index, array).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.takeRightWhile(users, function(o) { return !o.active; });\n * // => objects for ['fred', 'pebbles']\n *\n * // The `_.matches` iteratee shorthand.\n * _.takeRightWhile(users, { 'user': 'pebbles', 'active': false });\n * // => objects for ['pebbles']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.takeRightWhile(users, ['active', false]);\n * // => objects for ['fred', 'pebbles']\n *\n * // The `_.property` iteratee shorthand.\n * _.takeRightWhile(users, 'active');\n * // => []\n */\nfunction takeRightWhile(array, predicate) {\n  return array && array.length ? baseWhile(array, baseIteratee(predicate, 3), false, true) : [];\n}\nexport default takeRightWhile;", "map": {"version": 3, "names": ["baseIteratee", "<PERSON><PERSON><PERSON><PERSON>", "takeR<PERSON>While", "array", "predicate", "length"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/takeRightWhile.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseWhile from './_baseWhile.js';\n\n/**\n * Creates a slice of `array` with elements taken from the end. Elements are\n * taken until `predicate` returns falsey. The predicate is invoked with\n * three arguments: (value, index, array).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.takeRightWhile(users, function(o) { return !o.active; });\n * // => objects for ['fred', 'pebbles']\n *\n * // The `_.matches` iteratee shorthand.\n * _.takeRightWhile(users, { 'user': 'pebbles', 'active': false });\n * // => objects for ['pebbles']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.takeRightWhile(users, ['active', false]);\n * // => objects for ['fred', 'pebbles']\n *\n * // The `_.property` iteratee shorthand.\n * _.takeRightWhile(users, 'active');\n * // => []\n */\nfunction takeRightWhile(array, predicate) {\n  return (array && array.length)\n    ? baseWhile(array, baseIteratee(predicate, 3), false, true)\n    : [];\n}\n\nexport default takeRightWhile;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxC,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBJ,SAAS,CAACE,KAAK,EAAEH,YAAY,CAACI,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GACzD,EAAE;AACR;AAEA,eAAeF,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
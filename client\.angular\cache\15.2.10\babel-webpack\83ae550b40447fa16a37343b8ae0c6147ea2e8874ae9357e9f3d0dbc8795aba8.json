{"ast": null, "code": "import { Subject } from './Subject';\nimport { Subscription } from './Subscription';\nexport class AsyncSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this.value = null;\n    this.hasNext = false;\n    this.hasCompleted = false;\n  }\n  _subscribe(subscriber) {\n    if (this.hasError) {\n      subscriber.error(this.thrownError);\n      return Subscription.EMPTY;\n    } else if (this.hasCompleted && this.hasNext) {\n      subscriber.next(this.value);\n      subscriber.complete();\n      return Subscription.EMPTY;\n    }\n    return super._subscribe(subscriber);\n  }\n  next(value) {\n    if (!this.hasCompleted) {\n      this.value = value;\n      this.hasNext = true;\n    }\n  }\n  error(error) {\n    if (!this.hasCompleted) {\n      super.error(error);\n    }\n  }\n  complete() {\n    this.hasCompleted = true;\n    if (this.hasNext) {\n      super.next(this.value);\n    }\n    super.complete();\n  }\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "AsyncSubject", "constructor", "arguments", "value", "hasNext", "hasCompleted", "_subscribe", "subscriber", "<PERSON><PERSON><PERSON><PERSON>", "error", "thrownError", "EMPTY", "next", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/AsyncSubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nimport { Subscription } from './Subscription';\nexport class AsyncSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this.value = null;\n        this.hasNext = false;\n        this.hasCompleted = false;\n    }\n    _subscribe(subscriber) {\n        if (this.hasError) {\n            subscriber.error(this.thrownError);\n            return Subscription.EMPTY;\n        }\n        else if (this.hasCompleted && this.hasNext) {\n            subscriber.next(this.value);\n            subscriber.complete();\n            return Subscription.EMPTY;\n        }\n        return super._subscribe(subscriber);\n    }\n    next(value) {\n        if (!this.hasCompleted) {\n            this.value = value;\n            this.hasNext = true;\n        }\n    }\n    error(error) {\n        if (!this.hasCompleted) {\n            super.error(error);\n        }\n    }\n    complete() {\n        this.hasCompleted = true;\n        if (this.hasNext) {\n            super.next(this.value);\n        }\n        super.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,MAAMC,YAAY,SAASF,OAAO,CAAC;EACtCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,IAAI,CAACC,QAAQ,EAAE;MACfD,UAAU,CAACE,KAAK,CAAC,IAAI,CAACC,WAAW,CAAC;MAClC,OAAOX,YAAY,CAACY,KAAK;IAC7B,CAAC,MACI,IAAI,IAAI,CAACN,YAAY,IAAI,IAAI,CAACD,OAAO,EAAE;MACxCG,UAAU,CAACK,IAAI,CAAC,IAAI,CAACT,KAAK,CAAC;MAC3BI,UAAU,CAACM,QAAQ,CAAC,CAAC;MACrB,OAAOd,YAAY,CAACY,KAAK;IAC7B;IACA,OAAO,KAAK,CAACL,UAAU,CAACC,UAAU,CAAC;EACvC;EACAK,IAAIA,CAACT,KAAK,EAAE;IACR,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE;MACpB,IAAI,CAACF,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,OAAO,GAAG,IAAI;IACvB;EACJ;EACAK,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE;MACpB,KAAK,CAACI,KAAK,CAACA,KAAK,CAAC;IACtB;EACJ;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,YAAY,GAAG,IAAI;IACxB,IAAI,IAAI,CAACD,OAAO,EAAE;MACd,KAAK,CAACQ,IAAI,CAAC,IAAI,CAACT,KAAK,CAAC;IAC1B;IACA,KAAK,CAACU,QAAQ,CAAC,CAAC;EACpB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = async) {\n  return audit(() => timer(duration, scheduler));\n}", "map": {"version": 3, "names": ["async", "audit", "timer", "auditTime", "duration", "scheduler"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/auditTime.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = async) {\n    return audit(() => timer(duration, scheduler));\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAEC,SAAS,GAAGL,KAAK,EAAE;EACnD,OAAOC,KAAK,CAAC,MAAMC,KAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC,CAAC;AAClD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport moment from 'moment/moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../services/auto-schedule.service\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i8 from \"@ngx-formly/core\";\nexport class ModalUpdateConfigComponent {\n  constructor(_modalService, _translateService, _autoSchedule, _loadingService, _toastService) {\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._autoSchedule = _autoSchedule;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this.selectedConfig = null;\n    this.editForm = new FormGroup({});\n    this.editModel = {};\n    this.editFields = [{\n      key: 'begin_date',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Date'),\n        placeholder: this._translateService.instant('Enter begin date'),\n        required: true,\n        type: 'date'\n      }\n    }, {\n      key: 'begin_time',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Begin time'),\n        placeholder: this._translateService.instant('Enter begin time'),\n        required: true,\n        type: 'time'\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Begin time is required.')\n        }\n      }\n    }, {\n      key: 'end_time',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Latest End Time'),\n        placeholder: this._translateService.instant('Select end time'),\n        required: true,\n        type: 'time'\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('End time is required.')\n        }\n      },\n      validators: {\n        endTimeValidator: {\n          expression: control => {\n            const form = control.parent;\n            if (!form) return true;\n            const beginTime = form.get('begin_time').value;\n            const endTime = control.value;\n            return !beginTime || !endTime || endTime > beginTime;\n          },\n          message: this._translateService.instant('End time must be later than start time')\n        },\n        matchDurationValidator: {\n          expression: control => {\n            const form = control.parent;\n            if (!form) return true;\n            const beginTime = form.get('begin_time').value;\n            const endTime = control.value;\n            const matchDuration = form.get('match_duration')?.value || 0;\n            if (!beginTime || !endTime) return true;\n            const start = moment(beginTime, 'HH:mm');\n            const end = moment(endTime, 'HH:mm');\n            const diffInMinutes = end.diff(start, 'minutes');\n            return diffInMinutes >= matchDuration;\n          },\n          message: this._translateService.instant('Time slot must be equal or larger than match duration')\n        }\n      }\n    }, {\n      key: 'match_duration',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Match duration'),\n        placeholder: this._translateService.instant('Enter match duration (in minutes)'),\n        required: true,\n        type: 'number',\n        min: 1,\n        max: 1440\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Match duration is required.'),\n          min: this._translateService.instant('Match duration must be at least 1 minute.'),\n          max: this._translateService.instant('Match duration must be less than 1 day (1440 minutes).')\n        }\n      },\n      // add validator int value\n      validators: {\n        intValidator: {\n          expression: control => {\n            const value = control.value;\n            return !value || Number.isInteger(value);\n          },\n          message: this._translateService.instant('Match duration must be an integer number.')\n        }\n      }\n    }, {\n      key: 'break_duration',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Break duration'),\n        placeholder: this._translateService.instant('Enter break between match duration (in minutes)'),\n        required: true,\n        type: 'number',\n        min: 0,\n        max: 1440\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Break duration is required.'),\n          min: this._translateService.instant('Break duration must be at least 0 minutes.'),\n          max: this._translateService.instant('Break duration must be less than 1 day (1440 minutes).')\n        }\n      },\n      validators: {\n        intValidator: {\n          expression: control => {\n            const value = control.value;\n            return !value || Number.isInteger(value);\n          },\n          message: this._translateService.instant('Match duration must be an integer number.')\n        }\n      }\n    }, {\n      key: 'location_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'tournament_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'config_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n    this.onSubmit = new EventEmitter();\n  }\n  ngOnInit() {\n    if (!this.selectedConfig) {\n      console.error('No selected configuration provided');\n      return;\n    }\n    this._loadingService.show();\n    this._autoSchedule.getScheduleConfigById(this.selectedConfig.configId).subscribe(res => {\n      console.log('🚀 ~ ngOnInit ~ res: ', res);\n      // Convert ISO date string to 'YYYY-MM-DD' for input[type=\"date\"]\n      const beginDate = res.data.begin_date ? new Date(res.data.begin_date).toISOString().slice(0, 10) : '';\n      this.editModel = {\n        ...this.editModel,\n        tournament_id: res.data.tournament_id,\n        location_id: res.data.location_id,\n        begin_date: beginDate,\n        begin_time: res.data.begin_time,\n        end_time: res.data.end_time,\n        config_id: res.data.id,\n        match_duration: res.data.match_duration,\n        break_duration: res.data.break_match_duration,\n        date: this.selectedConfig.date,\n        time_slot_ids: this.selectedConfig.timeSlotIds\n      };\n      this._loadingService.dismiss();\n    });\n  }\n  onSubmitEdit(model) {\n    this._autoSchedule.updateScheduleConfig(model).subscribe(res => {\n      this._toastService.success(this._translateService.instant('Schedule configuration updated successfully.'));\n      this.onSubmit.emit(res);\n      this._modalService.dismissAll();\n    }, error => {\n      console.error('Error scheduling tournament:', error);\n    });\n  }\n  closeModal() {\n    this.editModel = {};\n    this._modalService.dismissAll();\n  }\n  clearForm() {\n    this.editForm.reset();\n  }\n  static #_ = this.ɵfac = function ModalUpdateConfigComponent_Factory(t) {\n    return new (t || ModalUpdateConfigComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AutoScheduleService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalUpdateConfigComponent,\n    selectors: [[\"app-modal-update-config\"]],\n    inputs: {\n      selectedConfig: \"selectedConfig\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"editScheduleConfig\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalUpdateConfigComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalUpdateConfigComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalUpdateConfigComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitEdit(ctx.editModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalUpdateConfigComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitEdit(ctx.editModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Edit Schedule Config\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.editForm)(\"fields\", ctx.editFields)(\"model\", ctx.editModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.editForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Re-schedule Match\"), \" \");\n      }\n    },\n    dependencies: [i6.ɵNgNoValidate, i6.NgControlStatusGroup, i6.FormGroupDirective, i7.RippleEffectDirective, i8.FormlyForm, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,SAAS,QAAQ,gBAAgB;AAO1C,OAAOC,MAAM,MAAM,eAAe;;;;;;;;;;AAelC,OAAM,MAAOC,0BAA0B;EAsKnCC,YACYC,aAAuB,EACvBC,iBAAmC,EACnCC,aAAkC,EAClCC,eAA+B,EAC/BC,aAA4B;IAJ5B,KAAAJ,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IAzKhB,KAAAC,cAAc,GAA8B,IAAI;IAEzD,KAAAC,QAAQ,GAAG,IAAIV,SAAS,CAAC,EAAE,CAAC;IAC5B,KAAAW,SAAS,GAAG,EAAE;IAEP,KAAAC,UAAU,GAAwB,CACrC;MACIC,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACY,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACY,OAAO,CAAC,kBAAkB,CAAC;QAC/DE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACY,OAAO,CAAC,YAAY,CAAC;QACnDC,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACY,OAAO,CACvC,kBAAkB,CACrB;QACDE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;OACT;MACDM,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACd,iBAAiB,CAACY,OAAO,CAAC,yBAAyB;;;KAG7E,EACD;MACIJ,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACY,OAAO,CAAC,iBAAiB,CAAC;QACxDC,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACY,OAAO,CAAC,iBAAiB,CAAC;QAC9DE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;OACT;MACDM,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACd,iBAAiB,CAACY,OAAO,CAAC,uBAAuB;;OAEvE;MACDK,UAAU,EAAE;QACRC,gBAAgB,EAAE;UACdC,UAAU,EAAGC,OAAO,IAAI;YACpB,MAAMC,IAAI,GAAGD,OAAO,CAACE,MAAM;YAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;YACtB,MAAME,SAAS,GAAGF,IAAI,CAACG,GAAG,CAAC,YAAY,CAAC,CAACC,KAAK;YAC9C,MAAMC,OAAO,GAAGN,OAAO,CAACK,KAAK;YAC7B,OAAO,CAACF,SAAS,IAAI,CAACG,OAAO,IAAIA,OAAO,GAAGH,SAAS;UACxD,CAAC;UACDI,OAAO,EAAE,IAAI,CAAC3B,iBAAiB,CAACY,OAAO,CAAC,wCAAwC;SACnF;QACDgB,sBAAsB,EAAE;UACpBT,UAAU,EAAGC,OAAO,IAAI;YACpB,MAAMC,IAAI,GAAGD,OAAO,CAACE,MAAM;YAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;YACtB,MAAME,SAAS,GAAGF,IAAI,CAACG,GAAG,CAAC,YAAY,CAAC,CAACC,KAAK;YAC9C,MAAMC,OAAO,GAAGN,OAAO,CAACK,KAAK;YAC7B,MAAMI,aAAa,GAAGR,IAAI,CAACG,GAAG,CAAC,gBAAgB,CAAC,EAAEC,KAAK,IAAI,CAAC;YAE5D,IAAI,CAACF,SAAS,IAAI,CAACG,OAAO,EAAE,OAAO,IAAI;YAEvC,MAAMI,KAAK,GAAGlC,MAAM,CAAC2B,SAAS,EAAE,OAAO,CAAC;YACxC,MAAMQ,GAAG,GAAGnC,MAAM,CAAC8B,OAAO,EAAE,OAAO,CAAC;YACpC,MAAMM,aAAa,GAAGD,GAAG,CAACE,IAAI,CAACH,KAAK,EAAE,SAAS,CAAC;YAEhD,OAAOE,aAAa,IAAIH,aAAa;UACzC,CAAC;UACDF,OAAO,EAAE,IAAI,CAAC3B,iBAAiB,CAACY,OAAO,CAAC,uDAAuD;;;KAG1G,EACD;MACIJ,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACY,OAAO,CAAC,gBAAgB,CAAC;QACvDC,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACY,OAAO,CACvC,mCAAmC,CACtC;QACDE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE,QAAQ;QACdyB,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE;OACR;MACDpB,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACd,iBAAiB,CAACY,OAAO,CAAC,6BAA6B,CAAC;UACvEsB,GAAG,EAAE,IAAI,CAAClC,iBAAiB,CAACY,OAAO,CAAC,2CAA2C,CAAC;UAChFuB,GAAG,EAAE,IAAI,CAACnC,iBAAiB,CAACY,OAAO,CAAC,wDAAwD;;OAEnG;MACD;MACAK,UAAU,EAAE;QACRmB,YAAY,EAAE;UACVjB,UAAU,EAAGC,OAAO,IAAI;YACpB,MAAMK,KAAK,GAAGL,OAAO,CAACK,KAAK;YAC3B,OAAO,CAACA,KAAK,IAAIY,MAAM,CAACC,SAAS,CAACb,KAAK,CAAC;UAC5C,CAAC;UACDE,OAAO,EAAE,IAAI,CAAC3B,iBAAiB,CAACY,OAAO,CAAC,2CAA2C;;;KAG9F,EACD;MACIJ,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACX,iBAAiB,CAACY,OAAO,CAAC,gBAAgB,CAAC;QACvDC,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACY,OAAO,CACvC,iDAAiD,CACpD;QACDE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE,QAAQ;QACdyB,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE;OACR;MACDpB,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACd,iBAAiB,CAACY,OAAO,CAAC,6BAA6B,CAAC;UACvEsB,GAAG,EAAE,IAAI,CAAClC,iBAAiB,CAACY,OAAO,CAAC,4CAA4C,CAAC;UACjFuB,GAAG,EAAE,IAAI,CAACnC,iBAAiB,CAACY,OAAO,CAAC,wDAAwD;;OAEnG;MACDK,UAAU,EAAE;QACRmB,YAAY,EAAE;UACVjB,UAAU,EAAGC,OAAO,IAAI;YACpB,MAAMK,KAAK,GAAGL,OAAO,CAACK,KAAK;YAC3B,OAAO,CAACA,KAAK,IAAIY,MAAM,CAACC,SAAS,CAACb,KAAK,CAAC;UAC5C,CAAC;UACDE,OAAO,EAAE,IAAI,CAAC3B,iBAAiB,CAACY,OAAO,CAAC,2CAA2C;;;KAG9F,EACD;MACIJ,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,WAAW;MAChBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,CACJ;IAES,KAAA8B,QAAQ,GAAG,IAAI7C,YAAY,EAAE;EAUvC;EAEA8C,QAAQA,CAAA;IACJ,IAAI,CAAC,IAAI,CAACpC,cAAc,EAAE;MACtBqC,OAAO,CAACC,KAAK,CAAC,oCAAoC,CAAC;MACnD;;IAIJ,IAAI,CAACxC,eAAe,CAACyC,IAAI,EAAE;IAE3B,IAAI,CAAC1C,aAAa,CAAC2C,qBAAqB,CAAC,IAAI,CAACxC,cAAc,CAACyC,QAAQ,CAAC,CACjEC,SAAS,CAAEC,GAAG,IAAI;MACfN,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAED,GAAG,CAAC;MACzC;MACA,MAAME,SAAS,GAAGF,GAAG,CAACG,IAAI,CAACC,UAAU,GAC/B,IAAIC,IAAI,CAACL,GAAG,CAACG,IAAI,CAACC,UAAU,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GACxD,EAAE;MAER,IAAI,CAAChD,SAAS,GAAG;QACb,GAAG,IAAI,CAACA,SAAS;QACjBiD,aAAa,EAAER,GAAG,CAACG,IAAI,CAACK,aAAa;QACrCC,WAAW,EAAET,GAAG,CAACG,IAAI,CAACM,WAAW;QACjCL,UAAU,EAAEF,SAAS;QACrBQ,UAAU,EAAEV,GAAG,CAACG,IAAI,CAACO,UAAU;QAC/BC,QAAQ,EAAEX,GAAG,CAACG,IAAI,CAACQ,QAAQ;QAC3BC,SAAS,EAAEZ,GAAG,CAACG,IAAI,CAACU,EAAE;QACtBC,cAAc,EAAEd,GAAG,CAACG,IAAI,CAACW,cAAc;QACvCC,cAAc,EAAEf,GAAG,CAACG,IAAI,CAACa,oBAAoB;QAC7CC,IAAI,EAAE,IAAI,CAAC5D,cAAc,CAAC4D,IAAI;QAC9BC,aAAa,EAAE,IAAI,CAAC7D,cAAc,CAAC8D;OACtC;MAED,IAAI,CAAChE,eAAe,CAACiE,OAAO,EAAE;IAElC,CAAC,CAAC;EACV;EAEAC,YAAYA,CAACC,KAAK;IAEd,IAAI,CAACpE,aAAa,CAACqE,oBAAoB,CAACD,KAAK,CAAC,CAACvB,SAAS,CAAEC,GAAG,IAAI;MAC7D,IAAI,CAAC5C,aAAa,CAACoE,OAAO,CAAC,IAAI,CAACvE,iBAAiB,CAACY,OAAO,CAAC,8CAA8C,CAAC,CAAC;MAC1G,IAAI,CAAC2B,QAAQ,CAACiC,IAAI,CAACzB,GAAG,CAAC;MACvB,IAAI,CAAChD,aAAa,CAAC0E,UAAU,EAAE;IACnC,CAAC,EAAG/B,KAAK,IAAI;MACTD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD,CAAC,CAAC;EACN;EAGAgC,UAAUA,CAAA;IACN,IAAI,CAACpE,SAAS,GAAG,EAAE;IACnB,IAAI,CAACP,aAAa,CAAC0E,UAAU,EAAE;EACnC;EAEAE,SAASA,CAAA;IACL,IAAI,CAACtE,QAAQ,CAACuE,KAAK,EAAE;EACzB;EAAC,QAAAC,CAAA;qBAvOQhF,0BAA0B,EAAAiF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAA1B7F,0BAA0B;IAAA8F,SAAA;IAAAC,MAAA;MAAAxF,cAAA;IAAA;IAAAyF,OAAA;MAAAtD,QAAA;IAAA;IAAAuD,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvBvCrB,EAAA,CAAAuB,cAAA,aAA0B;QACwBvB,EAAA,CAAAwB,MAAA,GAAwC;;QAAAxB,EAAA,CAAAyB,YAAA,EAAK;QAC7FzB,EAAA,CAAAuB,cAAA,gBAKC;QAFCvB,EAAA,CAAA0B,UAAA,mBAAAC,4DAAA;UAAA,OAASL,GAAA,CAAA1B,UAAA,EAAY;QAAA,EAAC;QAGtBI,EAAA,CAAAuB,cAAA,cAAyB;QAAAvB,EAAA,CAAAwB,MAAA,aAAO;QAAAxB,EAAA,CAAAyB,YAAA,EAAO;QAG3CzB,EAAA,CAAAuB,cAAA,cAGC;QADCvB,EAAA,CAAA0B,UAAA,sBAAAE,6DAAA;UAAA,OAAYN,GAAA,CAAAhC,YAAA,CAAAgC,GAAA,CAAA9F,SAAA,CAAuB;QAAA,EAAC;QAEpCwE,EAAA,CAAAuB,cAAA,aAAkD;QAK9CvB,EAAA,CAAA0B,UAAA,oBAAAG,kEAAA;UAAA,OAAUP,GAAA,CAAAhC,YAAA,CAAAgC,GAAA,CAAA9F,SAAA,CAAuB;QAAA,EAAC;QACnCwE,EAAA,CAAAyB,YAAA,EAAc;QAEjBzB,EAAA,CAAAuB,cAAA,cAA0B;QAItBvB,EAAA,CAAAwB,MAAA,IACF;;QAAAxB,EAAA,CAAAyB,YAAA,EAAS;;;QA3BqCzB,EAAA,CAAA8B,SAAA,GAAwC;QAAxC9B,EAAA,CAAA+B,iBAAA,CAAA/B,EAAA,CAAAgC,WAAA,+BAAwC;QAWxFhC,EAAA,CAAA8B,SAAA,GAAsB;QAAtB9B,EAAA,CAAAiC,UAAA,cAAAX,GAAA,CAAA/F,QAAA,CAAsB;QAKlByE,EAAA,CAAA8B,SAAA,GAAiB;QAAjB9B,EAAA,CAAAiC,UAAA,SAAAX,GAAA,CAAA/F,QAAA,CAAiB,WAAA+F,GAAA,CAAA7F,UAAA,WAAA6F,GAAA,CAAA9F,SAAA;QAQnBwE,EAAA,CAAA8B,SAAA,GAA6B;QAA7B9B,EAAA,CAAAiC,UAAA,aAAAX,GAAA,CAAA/F,QAAA,CAAA2G,OAAA,CAA6B;QAE3BlC,EAAA,CAAA8B,SAAA,GACF;QADE9B,EAAA,CAAAmC,kBAAA,MAAAnC,EAAA,CAAAgC,WAAA,kCACF", "names": ["EventEmitter", "FormGroup", "moment", "ModalUpdateConfigComponent", "constructor", "_modalService", "_translateService", "_autoSchedule", "_loadingService", "_toastService", "selectedConfig", "editForm", "editModel", "edit<PERSON>ields", "key", "type", "props", "label", "instant", "placeholder", "required", "validation", "messages", "validators", "endTimeValidator", "expression", "control", "form", "parent", "beginTime", "get", "value", "endTime", "message", "matchDurationValidator", "matchDuration", "start", "end", "diffInMinutes", "diff", "min", "max", "intValidator", "Number", "isInteger", "onSubmit", "ngOnInit", "console", "error", "show", "getScheduleConfigById", "configId", "subscribe", "res", "log", "beginDate", "data", "begin_date", "Date", "toISOString", "slice", "tournament_id", "location_id", "begin_time", "end_time", "config_id", "id", "match_duration", "break_duration", "break_match_duration", "date", "time_slot_ids", "timeSlotIds", "dismiss", "onSubmitEdit", "model", "updateScheduleConfig", "success", "emit", "dismissAll", "closeModal", "clearForm", "reset", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbModal", "i2", "TranslateService", "i3", "AutoScheduleService", "i4", "LoadingService", "i5", "ToastrService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ModalUpdateConfigComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModalUpdateConfigComponent_Template_button_click_4_listener", "ModalUpdateConfigComponent_Template_form_ngSubmit_7_listener", "ModalUpdateConfigComponent_Template_formly_form_submit_9_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "invalid", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-update-config\\modal-update-config.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-update-config\\modal-update-config.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AutoScheduleService } from '../../../../services/auto-schedule.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport moment from 'moment/moment';\r\n\r\nexport type UpdateConfigParams = {\r\n    tournamentId: string;\r\n    location: string;\r\n    date: string;\r\n    configId: number;\r\n    timeSlotIds: number[];\r\n}\r\n\r\n@Component({\r\n    selector: 'app-modal-update-config',\r\n    templateUrl: './modal-update-config.component.html',\r\n    styleUrls: ['./modal-update-config.component.scss']\r\n})\r\nexport class ModalUpdateConfigComponent implements OnInit {\r\n    \r\n    @Input() selectedConfig: UpdateConfigParams | null = null;\r\n    \r\n    editForm = new FormGroup({});\r\n    editModel = {};\r\n    \r\n    public editFields: FormlyFieldConfig[] = [\r\n        {\r\n            key: 'begin_date',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Date'),\r\n                placeholder: this._translateService.instant('Enter begin date'),\r\n                required: true,\r\n                type: 'date'\r\n            }\r\n        },\r\n        {\r\n            key: 'begin_time',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Begin time'),\r\n                placeholder: this._translateService.instant(\r\n                    'Enter begin time'\r\n                ),\r\n                required: true,\r\n                type: 'time'\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Begin time is required.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'end_time',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Latest End Time'),\r\n                placeholder: this._translateService.instant('Select end time'),\r\n                required: true,\r\n                type: 'time'\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('End time is required.')\r\n                }\r\n            },\r\n            validators: {\r\n                endTimeValidator: {\r\n                    expression: (control) => {\r\n                        const form = control.parent;\r\n                        if (!form) return true;\r\n                        const beginTime = form.get('begin_time').value;\r\n                        const endTime = control.value;\r\n                        return !beginTime || !endTime || endTime > beginTime;\r\n                    },\r\n                    message: this._translateService.instant('End time must be later than start time')\r\n                },\r\n                matchDurationValidator: {\r\n                    expression: (control) => {\r\n                        const form = control.parent;\r\n                        if (!form) return true;\r\n                        const beginTime = form.get('begin_time').value;\r\n                        const endTime = control.value;\r\n                        const matchDuration = form.get('match_duration')?.value || 0;\r\n                        \r\n                        if (!beginTime || !endTime) return true;\r\n                        \r\n                        const start = moment(beginTime, 'HH:mm');\r\n                        const end = moment(endTime, 'HH:mm');\r\n                        const diffInMinutes = end.diff(start, 'minutes');\r\n                        \r\n                        return diffInMinutes >= matchDuration;\r\n                    },\r\n                    message: this._translateService.instant('Time slot must be equal or larger than match duration')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'match_duration',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Match duration'),\r\n                placeholder: this._translateService.instant(\r\n                    'Enter match duration (in minutes)'\r\n                ),\r\n                required: true,\r\n                type: 'number',\r\n                min: 1,\r\n                max: 1440\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Match duration is required.'),\r\n                    min: this._translateService.instant('Match duration must be at least 1 minute.'),\r\n                    max: this._translateService.instant('Match duration must be less than 1 day (1440 minutes).')\r\n                }\r\n            },\r\n            // add validator int value\r\n            validators: {\r\n                intValidator: {\r\n                    expression: (control) => {\r\n                        const value = control.value;\r\n                        return !value || Number.isInteger(value);\r\n                    },\r\n                    message: this._translateService.instant('Match duration must be an integer number.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'break_duration',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Break duration'),\r\n                placeholder: this._translateService.instant(\r\n                    'Enter break between match duration (in minutes)'\r\n                ),\r\n                required: true,\r\n                type: 'number',\r\n                min: 0,\r\n                max: 1440\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Break duration is required.'),\r\n                    min: this._translateService.instant('Break duration must be at least 0 minutes.'),\r\n                    max: this._translateService.instant('Break duration must be less than 1 day (1440 minutes).')\r\n                }\r\n            },\r\n            validators: {\r\n                intValidator: {\r\n                    expression: (control) => {\r\n                        const value = control.value;\r\n                        return !value || Number.isInteger(value);\r\n                    },\r\n                    message: this._translateService.instant('Match duration must be an integer number.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'location_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'tournament_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'config_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        }\r\n    ];\r\n    \r\n    @Output() onSubmit = new EventEmitter();\r\n    \r\n    constructor(\r\n        private _modalService: NgbModal,\r\n        private _translateService: TranslateService,\r\n        private _autoSchedule: AutoScheduleService,\r\n        private _loadingService: LoadingService,\r\n        private _toastService: ToastrService\r\n    ) {\r\n    \r\n    }\r\n    \r\n    ngOnInit() {\r\n        if (!this.selectedConfig) {\r\n            console.error('No selected configuration provided');\r\n            return;\r\n        }\r\n        \r\n        \r\n        this._loadingService.show();\r\n        \r\n        this._autoSchedule.getScheduleConfigById(this.selectedConfig.configId)\r\n            .subscribe((res) => {\r\n                console.log('🚀 ~ ngOnInit ~ res: ', res);\r\n                // Convert ISO date string to 'YYYY-MM-DD' for input[type=\"date\"]\r\n                const beginDate = res.data.begin_date\r\n                    ? new Date(res.data.begin_date).toISOString().slice(0, 10)\r\n                    : '';\r\n                \r\n                this.editModel = {\r\n                    ...this.editModel,\r\n                    tournament_id: res.data.tournament_id,\r\n                    location_id: res.data.location_id,\r\n                    begin_date: beginDate,\r\n                    begin_time: res.data.begin_time,\r\n                    end_time: res.data.end_time,\r\n                    config_id: res.data.id,\r\n                    match_duration: res.data.match_duration,\r\n                    break_duration: res.data.break_match_duration,\r\n                    date: this.selectedConfig.date,\r\n                    time_slot_ids: this.selectedConfig.timeSlotIds\r\n                };\r\n                \r\n                this._loadingService.dismiss();\r\n                \r\n            });\r\n    }\r\n    \r\n    onSubmitEdit(model) {\r\n        \r\n        this._autoSchedule.updateScheduleConfig(model).subscribe((res) => {\r\n            this._toastService.success(this._translateService.instant('Schedule configuration updated successfully.'));\r\n            this.onSubmit.emit(res);\r\n            this._modalService.dismissAll();\r\n        }, (error) => {\r\n            console.error('Error scheduling tournament:', error);\r\n        });\r\n    }\r\n    \r\n    \r\n    closeModal() {\r\n        this.editModel = {};\r\n        this._modalService.dismissAll();\r\n    }\r\n    \r\n    clearForm() {\r\n        this.editForm.reset();\r\n    }\r\n    \r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"editScheduleConfig\">{{ \"Edit Schedule Config\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"editForm\"\r\n  (ngSubmit)=\"onSubmitEdit(editModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <formly-form\r\n      [form]=\"editForm\"\r\n      [fields]=\"editFields\"\r\n      [model]=\"editModel\"\r\n      (submit)=\"onSubmitEdit(editModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n    [disabled]=\"editForm.invalid\"\r\n    >\r\n      {{ 'Re-schedule Match' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
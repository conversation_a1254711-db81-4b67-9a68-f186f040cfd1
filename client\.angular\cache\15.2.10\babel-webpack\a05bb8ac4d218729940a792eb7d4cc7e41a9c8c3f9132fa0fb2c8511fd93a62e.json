{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, HostBinding, Input, ViewChild, HostListener, Pipe, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { fromEvent } from 'rxjs';\nimport * as i1 from '@videogular/ngx-videogular/core';\nimport { VgStates, VgApiService, VgCoreModule } from '@videogular/ngx-videogular/core';\nconst _c0 = [\"*\"];\nconst _c1 = [\"volumeBar\"];\nconst _c2 = function (a0) {\n  return {\n    dragging: a0\n  };\n};\nfunction VgTrackSelectorComponent_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const track_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", track_r1.id)(\"selected\", track_r1.selected === true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", track_r1.label, \" \");\n  }\n}\nfunction VgTimeDisplayComponent_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"LIVE\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VgTimeDisplayComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"vgUtc\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r1.getTime(), ctx_r1.vgFormat));\n  }\n}\nfunction VgQualitySelectorComponent_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const bitrate_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", bitrate_r1.qualityIndex)(\"selected\", bitrate_r1.qualityIndex === (ctx_r0.bitrateSelected == null ? null : ctx_r0.bitrateSelected.qualityIndex));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", bitrate_r1.label, \" \");\n  }\n}\nfunction VgScrubBarCuePointsComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n  }\n  if (rf & 2) {\n    const cp_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"width\", cp_r1.$$style == null ? null : cp_r1.$$style.width)(\"left\", cp_r1.$$style == null ? null : cp_r1.$$style.left);\n  }\n}\nfunction VgScrubBarCurrentTimeComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n  }\n}\nclass VgControlsComponent {\n  // @ts-ignore\n  constructor(API, ref, hidden) {\n    this.API = API;\n    this.hidden = hidden;\n    this.isAdsPlaying = 'initial';\n    this.hideControls = false;\n    this.vgAutohide = false;\n    this.vgAutohideTime = 3;\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    this.mouseMove$ = fromEvent(this.API.videogularElement, 'mousemove');\n    this.subscriptions.push(this.mouseMove$.subscribe(this.show.bind(this)));\n    this.touchStart$ = fromEvent(this.API.videogularElement, 'touchstart');\n    this.subscriptions.push(this.touchStart$.subscribe(this.show.bind(this)));\n    this.mouseClick$ = fromEvent(this.API.videogularElement, 'click');\n    this.subscriptions.push(this.mouseClick$.subscribe(this.show.bind(this)));\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n    this.subscriptions.push(this.target.subscriptions.play.subscribe(this.onPlay.bind(this)));\n    this.subscriptions.push(this.target.subscriptions.pause.subscribe(this.onPause.bind(this)));\n    this.subscriptions.push(this.target.subscriptions.startAds.subscribe(this.onStartAds.bind(this)));\n    this.subscriptions.push(this.target.subscriptions.endAds.subscribe(this.onEndAds.bind(this)));\n  }\n  ngAfterViewInit() {\n    if (this.vgAutohide) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n  onPlay() {\n    if (this.vgAutohide) {\n      this.hide();\n    }\n  }\n  onPause() {\n    clearTimeout(this.timer);\n    this.hideControls = false;\n    this.hidden.state(false);\n  }\n  onStartAds() {\n    this.isAdsPlaying = 'none';\n  }\n  onEndAds() {\n    this.isAdsPlaying = 'initial';\n  }\n  hide() {\n    if (this.vgAutohide) {\n      clearTimeout(this.timer);\n      this.hideAsync();\n    }\n  }\n  show() {\n    clearTimeout(this.timer);\n    this.hideControls = false;\n    this.hidden.state(false);\n    if (this.vgAutohide) {\n      this.hideAsync();\n    }\n  }\n  hideAsync() {\n    if (this.API.state === VgStates.VG_PLAYING) {\n      this.timer = setTimeout(() => {\n        this.hideControls = true;\n        this.hidden.state(true);\n      }, this.vgAutohideTime * 1000);\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgControlsComponent.ɵfac = function VgControlsComponent_Factory(t) {\n  return new (t || VgControlsComponent)(i0.ɵɵdirectiveInject(i1.VgApiService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgControlsHiddenService));\n};\n/** @nocollapse */\nVgControlsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgControlsComponent,\n  selectors: [[\"vg-controls\"]],\n  hostVars: 4,\n  hostBindings: function VgControlsComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"pointer-events\", ctx.isAdsPlaying);\n      i0.ɵɵclassProp(\"hide\", ctx.hideControls);\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\",\n    vgAutohide: \"vgAutohide\",\n    vgAutohideTime: \"vgAutohideTime\"\n  },\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function VgControlsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\"vg-controls{position:absolute;display:flex;width:100%;height:50px;z-index:300;bottom:0;background-color:#00000080;transition:bottom 1s}vg-controls.hide{bottom:-50px}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgControlsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-controls',\n      encapsulation: ViewEncapsulation.None,\n      template: `<ng-content></ng-content>`,\n      styles: [\"vg-controls{position:absolute;display:flex;width:100%;height:50px;z-index:300;bottom:0;background-color:#00000080;transition:bottom 1s}vg-controls.hide{bottom:-50px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.VgApiService\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.VgControlsHiddenService\n    }];\n  }, {\n    isAdsPlaying: [{\n      type: HostBinding,\n      args: ['style.pointer-events']\n    }],\n    hideControls: [{\n      type: HostBinding,\n      args: ['class.hide']\n    }],\n    vgFor: [{\n      type: Input\n    }],\n    vgAutohide: [{\n      type: Input\n    }],\n    vgAutohideTime: [{\n      type: Input\n    }]\n  });\n})();\nclass VgVolumeComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n    this.isDragging = false;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n    this.ariaValue = this.getVolume() * 100;\n  }\n  onClick(event) {\n    this.setVolume(this.calculateVolume(event.clientX));\n  }\n  onMouseDown(event) {\n    this.mouseDownPosX = event.clientX;\n    this.isDragging = true;\n  }\n  onDrag(event) {\n    if (this.isDragging) {\n      this.setVolume(this.calculateVolume(event.clientX));\n    }\n  }\n  onStopDrag(event) {\n    if (this.isDragging) {\n      this.isDragging = false;\n      if (this.mouseDownPosX === event.clientX) {\n        this.setVolume(this.calculateVolume(event.clientX));\n      }\n    }\n  }\n  arrowAdjustVolume(event) {\n    if (event.keyCode === 38 || event.keyCode === 39) {\n      event.preventDefault();\n      this.setVolume(Math.max(0, Math.min(100, this.getVolume() * 100 + 10)));\n    } else if (event.keyCode === 37 || event.keyCode === 40) {\n      event.preventDefault();\n      this.setVolume(Math.max(0, Math.min(100, this.getVolume() * 100 - 10)));\n    }\n  }\n  calculateVolume(mousePosX) {\n    const recObj = this.volumeBarRef.nativeElement.getBoundingClientRect();\n    const volumeBarOffsetLeft = recObj.left;\n    const volumeBarWidth = recObj.width;\n    return (mousePosX - volumeBarOffsetLeft) / volumeBarWidth * 100;\n  }\n  setVolume(vol) {\n    this.target.volume = Math.max(0, Math.min(1, vol / 100));\n    this.ariaValue = this.target.volume * 100;\n  }\n  getVolume() {\n    return this.target ? this.target.volume : 0;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgVolumeComponent.ɵfac = function VgVolumeComponent_Factory(t) {\n  return new (t || VgVolumeComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgVolumeComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgVolumeComponent,\n  selectors: [[\"vg-volume\"]],\n  viewQuery: function VgVolumeComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.volumeBarRef = _t.first);\n    }\n  },\n  hostBindings: function VgVolumeComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mousemove\", function VgVolumeComponent_mousemove_HostBindingHandler($event) {\n        return ctx.onDrag($event);\n      }, false, i0.ɵɵresolveDocument)(\"mouseup\", function VgVolumeComponent_mouseup_HostBindingHandler($event) {\n        return ctx.onStopDrag($event);\n      }, false, i0.ɵɵresolveDocument)(\"keydown\", function VgVolumeComponent_keydown_HostBindingHandler($event) {\n        return ctx.arrowAdjustVolume($event);\n      });\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\"\n  },\n  decls: 5,\n  vars: 9,\n  consts: [[\"tabindex\", \"0\", \"role\", \"slider\", \"aria-label\", \"volume level\", \"aria-level\", \"polite\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", \"aria-orientation\", \"horizontal\", 1, \"volumeBar\", 3, \"click\", \"mousedown\"], [\"volumeBar\", \"\"], [1, \"volumeBackground\", 3, \"ngClass\"], [1, \"volumeValue\"], [1, \"volumeKnob\"]],\n  template: function VgVolumeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"click\", function VgVolumeComponent_Template_div_click_0_listener($event) {\n        return ctx.onClick($event);\n      })(\"mousedown\", function VgVolumeComponent_Template_div_mousedown_0_listener($event) {\n        return ctx.onMouseDown($event);\n      });\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-valuenow\", ctx.ariaValue)(\"aria-valuetext\", ctx.ariaValue + \"%\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c2, ctx.isDragging));\n      i0.ɵɵadvance(1);\n      i0.ɵɵstyleProp(\"width\", ctx.getVolume() * (100 - 15) + \"%\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵstyleProp(\"left\", ctx.getVolume() * (100 - 15) + \"%\");\n    }\n  },\n  dependencies: [i2.NgClass],\n  styles: [\"vg-volume{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:100px;cursor:pointer;color:#fff;line-height:50px}vg-volume .volumeBar{position:relative;display:flex;flex-grow:1;align-items:center}vg-volume .volumeBackground{display:flex;flex-grow:1;height:5px;pointer-events:none;background-color:#333}vg-volume .volumeValue{display:flex;height:5px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeKnob{position:absolute;width:15px;height:15px;left:0;top:50%;transform:translateY(-50%);border-radius:15px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeBackground.dragging .volumeValue,vg-volume .volumeBackground.dragging .volumeKnob{transition:none}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgVolumeComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-volume',\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      #volumeBar\n      class=\"volumeBar\"\n      tabindex=\"0\"\n      role=\"slider\"\n      aria-label=\"volume level\"\n      aria-level=\"polite\"\n      [attr.aria-valuenow]=\"ariaValue\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      aria-orientation=\"horizontal\"\n      [attr.aria-valuetext]=\"ariaValue + '%'\"\n      (click)=\"onClick($event)\"\n      (mousedown)=\"onMouseDown($event)\"\n    >\n      <div class=\"volumeBackground\" [ngClass]=\"{ dragging: isDragging }\">\n        <div\n          class=\"volumeValue\"\n          [style.width]=\"getVolume() * (100 - 15) + '%'\"\n        ></div>\n        <div\n          class=\"volumeKnob\"\n          [style.left]=\"getVolume() * (100 - 15) + '%'\"\n        ></div>\n      </div>\n    </div>\n  `,\n      styles: [\"vg-volume{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:100px;cursor:pointer;color:#fff;line-height:50px}vg-volume .volumeBar{position:relative;display:flex;flex-grow:1;align-items:center}vg-volume .volumeBackground{display:flex;flex-grow:1;height:5px;pointer-events:none;background-color:#333}vg-volume .volumeValue{display:flex;height:5px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeKnob{position:absolute;width:15px;height:15px;left:0;top:50%;transform:translateY(-50%);border-radius:15px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeBackground.dragging .volumeValue,vg-volume .volumeBackground.dragging .volumeKnob{transition:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    volumeBarRef: [{\n      type: ViewChild,\n      args: ['volumeBar', {\n        static: true\n      }]\n    }],\n    onDrag: [{\n      type: HostListener,\n      args: ['document:mousemove', ['$event']]\n    }],\n    onStopDrag: [{\n      type: HostListener,\n      args: ['document:mouseup', ['$event']]\n    }],\n    arrowAdjustVolume: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass VgTrackSelectorComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n    const subs = Array.from(this.API.getMasterMedia().elem.children).filter(item => item.tagName === 'TRACK').filter(item => item.kind === 'subtitles').map(item => ({\n      label: item.label,\n      selected: item.default === true,\n      id: item.srclang\n    }));\n    this.tracks = [...subs, {\n      id: null,\n      label: 'Off',\n      selected: subs.every(item => item.selected === false)\n    }];\n    const track = this.tracks.filter(item => item.selected === true)[0];\n    this.trackSelected = track.id;\n    this.ariaValue = track.label;\n  }\n  selectTrack(trackId) {\n    this.trackSelected = trackId === 'null' ? null : trackId;\n    this.ariaValue = 'No track selected';\n    Array.from(this.API.getMasterMedia().elem.textTracks).forEach(item => {\n      if (item.language === trackId) {\n        this.ariaValue = item.label;\n        item.mode = 'showing';\n      } else {\n        item.mode = 'hidden';\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgTrackSelectorComponent.ɵfac = function VgTrackSelectorComponent_Factory(t) {\n  return new (t || VgTrackSelectorComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgTrackSelectorComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgTrackSelectorComponent,\n  selectors: [[\"vg-track-selector\"]],\n  inputs: {\n    vgFor: \"vgFor\"\n  },\n  decls: 5,\n  vars: 5,\n  consts: [[1, \"container\"], [1, \"track-selected\"], [\"tabindex\", \"0\", \"aria-label\", \"track selector\", 1, \"trackSelector\", 3, \"change\"], [3, \"value\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"selected\"]],\n  template: function VgTrackSelectorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtext(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"select\", 2);\n      i0.ɵɵlistener(\"change\", function VgTrackSelectorComponent_Template_select_change_3_listener($event) {\n        return ctx.selectTrack($event.target.value);\n      });\n      i0.ɵɵtemplate(4, VgTrackSelectorComponent_option_4_Template, 2, 3, \"option\", 3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"vg-icon-closed_caption\", !ctx.trackSelected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", ctx.trackSelected || \"\", \" \");\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"aria-valuetext\", ctx.ariaValue);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.tracks);\n    }\n  },\n  dependencies: [i2.NgForOf],\n  styles: [\"vg-track-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-track-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-track-selector select.trackSelector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-track-selector select.trackSelector::-ms-expand{display:none}vg-track-selector select.trackSelector option{color:#000}vg-track-selector .track-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-track-selector .vg-icon-closed_caption:before{width:100%}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgTrackSelectorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-track-selector',\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div class=\"container\">\n      <div\n        class=\"track-selected\"\n        [class.vg-icon-closed_caption]=\"!trackSelected\"\n      >\n        {{ trackSelected || '' }}\n      </div>\n      <select\n        class=\"trackSelector\"\n        (change)=\"selectTrack($event.target.value)\"\n        tabindex=\"0\"\n        aria-label=\"track selector\"\n        [attr.aria-valuetext]=\"ariaValue\"\n      >\n        <option\n          *ngFor=\"let track of tracks\"\n          [value]=\"track.id\"\n          [selected]=\"track.selected === true\"\n        >\n          {{ track.label }}\n        </option>\n      </select>\n    </div>\n  `,\n      styles: [\"vg-track-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-track-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-track-selector select.trackSelector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-track-selector select.trackSelector::-ms-expand{display:none}vg-track-selector select.trackSelector option{color:#000}vg-track-selector .track-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-track-selector .vg-icon-closed_caption:before{width:100%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }]\n  });\n})();\n\n// Workaround until we can use UTC with Angular Date Pipe\nclass VgUtcPipe {\n  transform(value, format) {\n    let date = new Date(value);\n    let result = format;\n    let ss = date.getUTCSeconds();\n    let mm = date.getUTCMinutes();\n    let hh = date.getUTCHours();\n    if (ss < 10) {\n      ss = '0' + ss;\n    }\n    if (mm < 10) {\n      mm = '0' + mm;\n    }\n    if (hh < 10) {\n      hh = '0' + hh;\n    }\n    result = result.replace(/ss/g, ss);\n    result = result.replace(/mm/g, mm);\n    result = result.replace(/hh/g, hh);\n    return result;\n  }\n}\n/** @nocollapse */\nVgUtcPipe.ɵfac = function VgUtcPipe_Factory(t) {\n  return new (t || VgUtcPipe)();\n};\n/** @nocollapse */\nVgUtcPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"vgUtc\",\n  type: VgUtcPipe,\n  pure: true\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgUtcPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'vgUtc'\n    }]\n  }], null, null);\n})();\nclass VgTimeDisplayComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.vgProperty = 'current';\n    this.vgFormat = 'mm:ss';\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  getTime() {\n    let t = 0;\n    if (this.target) {\n      t = Math.round(this.target.time[this.vgProperty]);\n      t = isNaN(t) || this.target.isLive ? 0 : t;\n    }\n    return t;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgTimeDisplayComponent.ɵfac = function VgTimeDisplayComponent_Factory(t) {\n  return new (t || VgTimeDisplayComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgTimeDisplayComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgTimeDisplayComponent,\n  selectors: [[\"vg-time-display\"]],\n  inputs: {\n    vgFor: \"vgFor\",\n    vgProperty: \"vgProperty\",\n    vgFormat: \"vgFormat\"\n  },\n  ngContentSelectors: _c0,\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\"]],\n  template: function VgTimeDisplayComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, VgTimeDisplayComponent_span_0_Template, 2, 0, \"span\", 0);\n      i0.ɵɵtemplate(1, VgTimeDisplayComponent_span_1_Template, 3, 4, \"span\", 0);\n      i0.ɵɵprojection(2);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.target == null ? null : ctx.target.isLive);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.target == null ? null : ctx.target.isLive));\n    }\n  },\n  dependencies: [i2.NgIf, VgUtcPipe],\n  styles: [\"vg-time-display{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:60px;cursor:pointer;color:#fff;line-height:50px;pointer-events:none;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgTimeDisplayComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-time-display',\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span *ngIf=\"target?.isLive\">LIVE</span>\n    <span *ngIf=\"!target?.isLive\">{{ getTime() | vgUtc: vgFormat }}</span>\n    <ng-content></ng-content>\n  `,\n      styles: [\"vg-time-display{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:60px;cursor:pointer;color:#fff;line-height:50px;pointer-events:none;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    vgProperty: [{\n      type: Input\n    }],\n    vgFormat: [{\n      type: Input\n    }]\n  });\n})();\nclass VgScrubBarComponent {\n  constructor(ref, API, vgControlsHiddenState) {\n    this.API = API;\n    this.hideScrubBar = false;\n    this.vgSlider = true;\n    this.isSeeking = false;\n    this.wasPlaying = false;\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n    this.subscriptions.push(vgControlsHiddenState.isHidden.subscribe(hide => this.onHideScrubBar(hide)));\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  seekStart() {\n    if (this.target.canPlay) {\n      this.isSeeking = true;\n      if (this.target.state === VgStates.VG_PLAYING) {\n        this.wasPlaying = true;\n      }\n      this.target.pause();\n    }\n  }\n  seekMove(offset) {\n    if (this.isSeeking) {\n      const percentage = Math.max(Math.min(offset * 100 / this.elem.scrollWidth, 99.9), 0);\n      this.target.time.current = percentage * this.target.time.total / 100;\n      this.target.seekTime(percentage, true);\n    }\n  }\n  seekEnd(offset) {\n    this.isSeeking = false;\n    if (this.target.canPlay) {\n      if (offset !== false) {\n        const percentage = Math.max(Math.min(offset * 100 / this.elem.scrollWidth, 99.9), 0);\n        this.target.seekTime(percentage, true);\n      }\n      if (this.wasPlaying) {\n        this.wasPlaying = false;\n        this.target.play();\n      }\n    }\n  }\n  touchEnd() {\n    this.isSeeking = false;\n    if (this.wasPlaying) {\n      this.wasPlaying = false;\n      this.target.play();\n    }\n  }\n  getTouchOffset(event) {\n    let offsetLeft = 0;\n    let element = event.target;\n    while (element) {\n      offsetLeft += element.offsetLeft;\n      element = element.offsetParent;\n    }\n    return event.touches[0].pageX - offsetLeft;\n  }\n  onMouseDownScrubBar($event) {\n    if (this.target) {\n      if (!this.target.isLive) {\n        if (!this.vgSlider) {\n          this.seekEnd($event.offsetX);\n        } else {\n          this.seekStart();\n        }\n      }\n    }\n  }\n  onMouseMoveScrubBar($event) {\n    if (this.target) {\n      if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n        this.seekMove($event.offsetX);\n      }\n    }\n  }\n  onMouseUpScrubBar($event) {\n    if (this.target) {\n      if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n        this.seekEnd($event.offsetX);\n      }\n    }\n  }\n  onTouchStartScrubBar(_$event) {\n    if (this.target) {\n      if (!this.target.isLive) {\n        if (!this.vgSlider) {\n          this.seekEnd(false);\n        } else {\n          this.seekStart();\n        }\n      }\n    }\n  }\n  onTouchMoveScrubBar($event) {\n    if (this.target) {\n      if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n        this.seekMove(this.getTouchOffset($event));\n      }\n    }\n  }\n  // @ts-ignore\n  onTouchCancelScrubBar(_$event) {\n    if (this.target) {\n      if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n        this.touchEnd();\n      }\n    }\n  }\n  // @ts-ignore\n  onTouchEndScrubBar(_$event) {\n    if (this.target) {\n      if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n        this.touchEnd();\n      }\n    }\n  }\n  arrowAdjustVolume(event) {\n    if (this.target) {\n      if (event.keyCode === 38 || event.keyCode === 39) {\n        event.preventDefault();\n        this.target.seekTime((this.target.time.current + 5000) / 1000, false);\n      } else if (event.keyCode === 37 || event.keyCode === 40) {\n        event.preventDefault();\n        this.target.seekTime((this.target.time.current - 5000) / 1000, false);\n      }\n    }\n  }\n  getPercentage() {\n    return this.target ? Math.round(this.target.time.current * 100 / this.target.time.total) + '%' : '0%';\n  }\n  onHideScrubBar(hide) {\n    this.hideScrubBar = hide;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgScrubBarComponent.ɵfac = function VgScrubBarComponent_Factory(t) {\n  return new (t || VgScrubBarComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService), i0.ɵɵdirectiveInject(i1.VgControlsHiddenService));\n};\n/** @nocollapse */\nVgScrubBarComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgScrubBarComponent,\n  selectors: [[\"vg-scrub-bar\"]],\n  hostVars: 2,\n  hostBindings: function VgScrubBarComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mousedown\", function VgScrubBarComponent_mousedown_HostBindingHandler($event) {\n        return ctx.onMouseDownScrubBar($event);\n      })(\"mousemove\", function VgScrubBarComponent_mousemove_HostBindingHandler($event) {\n        return ctx.onMouseMoveScrubBar($event);\n      }, false, i0.ɵɵresolveDocument)(\"mouseup\", function VgScrubBarComponent_mouseup_HostBindingHandler($event) {\n        return ctx.onMouseUpScrubBar($event);\n      }, false, i0.ɵɵresolveDocument)(\"touchstart\", function VgScrubBarComponent_touchstart_HostBindingHandler($event) {\n        return ctx.onTouchStartScrubBar($event);\n      })(\"touchmove\", function VgScrubBarComponent_touchmove_HostBindingHandler($event) {\n        return ctx.onTouchMoveScrubBar($event);\n      }, false, i0.ɵɵresolveDocument)(\"touchcancel\", function VgScrubBarComponent_touchcancel_HostBindingHandler($event) {\n        return ctx.onTouchCancelScrubBar($event);\n      }, false, i0.ɵɵresolveDocument)(\"touchend\", function VgScrubBarComponent_touchend_HostBindingHandler($event) {\n        return ctx.onTouchEndScrubBar($event);\n      }, false, i0.ɵɵresolveDocument)(\"keydown\", function VgScrubBarComponent_keydown_HostBindingHandler($event) {\n        return ctx.arrowAdjustVolume($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"hide\", ctx.hideScrubBar);\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\",\n    vgSlider: \"vgSlider\"\n  },\n  ngContentSelectors: _c0,\n  decls: 2,\n  vars: 2,\n  consts: [[\"tabindex\", \"0\", \"role\", \"slider\", \"aria-label\", \"scrub bar\", \"aria-level\", \"polite\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"scrubBar\"]],\n  template: function VgScrubBarComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-valuenow\", ctx.getPercentage())(\"aria-valuetext\", ctx.getPercentage());\n    }\n  },\n  styles: [\"vg-scrub-bar{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;position:absolute;width:100%;height:5px;bottom:50px;margin:0;cursor:pointer;align-items:center;background:rgba(0,0,0,.75);z-index:250;transition:bottom 1s,opacity .5s}vg-scrub-bar .scrubBar{position:relative;display:flex;flex-grow:1;align-items:center;height:100%}vg-controls vg-scrub-bar{position:relative;bottom:0;background:transparent;height:50px;flex-grow:1;flex-basis:0;margin:0 10px;transition:initial}vg-scrub-bar.hide{bottom:0;opacity:0}vg-controls vg-scrub-bar.hide{bottom:initial;opacity:initial}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgScrubBarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-scrub-bar',\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      class=\"scrubBar\"\n      tabindex=\"0\"\n      role=\"slider\"\n      aria-label=\"scrub bar\"\n      aria-level=\"polite\"\n      [attr.aria-valuenow]=\"getPercentage()\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      [attr.aria-valuetext]=\"getPercentage()\"\n    >\n      <ng-content></ng-content>\n    </div>\n  `,\n      styles: [\"vg-scrub-bar{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;position:absolute;width:100%;height:5px;bottom:50px;margin:0;cursor:pointer;align-items:center;background:rgba(0,0,0,.75);z-index:250;transition:bottom 1s,opacity .5s}vg-scrub-bar .scrubBar{position:relative;display:flex;flex-grow:1;align-items:center;height:100%}vg-controls vg-scrub-bar{position:relative;bottom:0;background:transparent;height:50px;flex-grow:1;flex-basis:0;margin:0 10px;transition:initial}vg-scrub-bar.hide{bottom:0;opacity:0}vg-controls vg-scrub-bar.hide{bottom:initial;opacity:initial}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }, {\n      type: i1.VgControlsHiddenService\n    }];\n  }, {\n    hideScrubBar: [{\n      type: HostBinding,\n      args: ['class.hide']\n    }],\n    vgFor: [{\n      type: Input\n    }],\n    vgSlider: [{\n      type: Input\n    }],\n    onMouseDownScrubBar: [{\n      type: HostListener,\n      args: ['mousedown', ['$event']]\n    }],\n    onMouseMoveScrubBar: [{\n      type: HostListener,\n      args: ['document:mousemove', ['$event']]\n    }],\n    onMouseUpScrubBar: [{\n      type: HostListener,\n      args: ['document:mouseup', ['$event']]\n    }],\n    onTouchStartScrubBar: [{\n      type: HostListener,\n      args: ['touchstart', ['$event']]\n    }],\n    onTouchMoveScrubBar: [{\n      type: HostListener,\n      args: ['document:touchmove', ['$event']]\n    }],\n    onTouchCancelScrubBar: [{\n      type: HostListener,\n      args: ['document:touchcancel', ['$event']]\n    }],\n    onTouchEndScrubBar: [{\n      type: HostListener,\n      args: ['document:touchend', ['$event']]\n    }],\n    arrowAdjustVolume: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass VgQualitySelectorComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.onBitrateChange = new EventEmitter();\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {}\n  ngOnChanges(changes) {\n    if (changes.bitrates.currentValue && changes.bitrates.currentValue.length) {\n      this.bitrates.forEach(item => item.label = item.label || Math.round(item.bitrate / 1000).toString());\n    }\n  }\n  selectBitrate(index) {\n    this.bitrateSelected = this.bitrates[index];\n    this.onBitrateChange.emit(this.bitrates[index]);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgQualitySelectorComponent.ɵfac = function VgQualitySelectorComponent_Factory(t) {\n  return new (t || VgQualitySelectorComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgQualitySelectorComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgQualitySelectorComponent,\n  selectors: [[\"vg-quality-selector\"]],\n  inputs: {\n    bitrates: \"bitrates\"\n  },\n  outputs: {\n    onBitrateChange: \"onBitrateChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 5,\n  vars: 5,\n  consts: [[1, \"container\"], [1, \"quality-selected\"], [\"tabindex\", \"0\", \"aria-label\", \"quality selector\", 1, \"quality-selector\", 3, \"change\"], [3, \"value\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"selected\"]],\n  template: function VgQualitySelectorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtext(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"select\", 2);\n      i0.ɵɵlistener(\"change\", function VgQualitySelectorComponent_Template_select_change_3_listener($event) {\n        return ctx.selectBitrate($event.target.value);\n      });\n      i0.ɵɵtemplate(4, VgQualitySelectorComponent_option_4_Template, 2, 3, \"option\", 3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"vg-icon-hd\", !ctx.bitrateSelected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", ctx.bitrateSelected == null ? null : ctx.bitrateSelected.label, \" \");\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"aria-valuetext\", ctx.ariaValue);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.bitrates);\n    }\n  },\n  dependencies: [i2.NgForOf],\n  styles: [\"vg-quality-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-quality-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-quality-selector select.quality-selector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-quality-selector select.quality-selector::-ms-expand{display:none}vg-quality-selector select.quality-selector option{color:#000}vg-quality-selector .quality-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-quality-selector .vg-icon-closed_caption:before{width:100%}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgQualitySelectorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-quality-selector',\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div class=\"container\">\n      <div class=\"quality-selected\" [class.vg-icon-hd]=\"!bitrateSelected\">\n        {{ bitrateSelected?.label }}\n      </div>\n      <select\n        class=\"quality-selector\"\n        (change)=\"selectBitrate($event.target.value)\"\n        tabindex=\"0\"\n        aria-label=\"quality selector\"\n        [attr.aria-valuetext]=\"ariaValue\"\n      >\n        <option\n          *ngFor=\"let bitrate of bitrates\"\n          [value]=\"bitrate.qualityIndex\"\n          [selected]=\"bitrate.qualityIndex === bitrateSelected?.qualityIndex\"\n        >\n          {{ bitrate.label }}\n        </option>\n      </select>\n    </div>\n  `,\n      styles: [\"vg-quality-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-quality-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-quality-selector select.quality-selector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-quality-selector select.quality-selector::-ms-expand{display:none}vg-quality-selector select.quality-selector option{color:#000}vg-quality-selector .quality-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-quality-selector .vg-icon-closed_caption:before{width:100%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    bitrates: [{\n      type: Input\n    }],\n    onBitrateChange: [{\n      type: Output\n    }]\n  });\n})();\nclass VgPlaybackButtonComponent {\n  constructor(ref, API, cdr) {\n    this.API = API;\n    this.cdr = cdr;\n    this.subscriptions = [];\n    this.ariaValue = 1;\n    this.elem = ref.nativeElement;\n    this.playbackValues = ['0.5', '1.0', '1.5', '2.0'];\n    this.playbackIndex = 1;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  onClick() {\n    this.updatePlaybackSpeed();\n  }\n  onKeyDown(event) {\n    // On press Enter (13) or Space (32)\n    if (event.keyCode === 13 || event.keyCode === 32) {\n      event.preventDefault();\n      this.updatePlaybackSpeed();\n    }\n  }\n  updatePlaybackSpeed() {\n    this.playbackValues.forEach((playbackValue, index) => {\n      if (playbackValue.length === 1) {\n        this.playbackValues[index] = playbackValue + '.0';\n      }\n    });\n    this.playbackIndex = ++this.playbackIndex % this.playbackValues.length;\n    if (this.target instanceof VgApiService) {\n      this.target.playbackRate = this.playbackValues[this.playbackIndex];\n    } else {\n      this.target.playbackRate[this.vgFor] = this.playbackValues[this.playbackIndex];\n    }\n    this.detectChanges();\n  }\n  getPlaybackRate() {\n    this.ariaValue = this.target ? this.target.playbackRate : 1.0;\n    return this.ariaValue;\n  }\n  detectChanges() {\n    try {\n      this.cdr.detectChanges();\n    } catch (e) {\n      console.warn(e);\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgPlaybackButtonComponent.ɵfac = function VgPlaybackButtonComponent_Factory(t) {\n  return new (t || VgPlaybackButtonComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n/** @nocollapse */\nVgPlaybackButtonComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgPlaybackButtonComponent,\n  selectors: [[\"vg-playback-button\"]],\n  hostBindings: function VgPlaybackButtonComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function VgPlaybackButtonComponent_click_HostBindingHandler() {\n        return ctx.onClick();\n      })(\"keydown\", function VgPlaybackButtonComponent_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\",\n    playbackValues: \"playbackValues\"\n  },\n  decls: 2,\n  vars: 2,\n  consts: [[\"tabindex\", \"0\", \"role\", \"button\", \"aria-label\", \"playback speed button\", 1, \"button\"]],\n  template: function VgPlaybackButtonComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵtext(1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-valuetext\", ctx.ariaValue);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", ctx.getPlaybackRate(), \"x \");\n    }\n  },\n  styles: [\"vg-playback-button{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}vg-playback-button .button{display:flex;align-items:center;justify-content:center;width:50px}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgPlaybackButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-playback-button',\n      encapsulation: ViewEncapsulation.None,\n      template: ` <span\n    class=\"button\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"playback speed button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  >\n    {{ getPlaybackRate() }}x\n  </span>`,\n      styles: [\"vg-playback-button{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}vg-playback-button .button{display:flex;align-items:center;justify-content:center;width:50px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    playbackValues: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass VgPlayPauseComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.subscriptions = [];\n    this.ariaValue = VgStates.VG_PAUSED;\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  onClick() {\n    this.playPause();\n  }\n  onKeyDown(event) {\n    // On press Enter (13) or Space (32)\n    if (event.keyCode === 13 || event.keyCode === 32) {\n      event.preventDefault();\n      this.playPause();\n    }\n  }\n  playPause() {\n    const state = this.getState();\n    switch (state) {\n      case VgStates.VG_PLAYING:\n        this.target.pause();\n        break;\n      case VgStates.VG_PAUSED:\n      case VgStates.VG_ENDED:\n        this.target.play();\n        break;\n    }\n  }\n  getState() {\n    this.ariaValue = this.target ? this.target.state : VgStates.VG_PAUSED;\n    return this.ariaValue;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgPlayPauseComponent.ɵfac = function VgPlayPauseComponent_Factory(t) {\n  return new (t || VgPlayPauseComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgPlayPauseComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgPlayPauseComponent,\n  selectors: [[\"vg-play-pause\"]],\n  hostBindings: function VgPlayPauseComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function VgPlayPauseComponent_click_HostBindingHandler() {\n        return ctx.onClick();\n      })(\"keydown\", function VgPlayPauseComponent_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\"\n  },\n  decls: 1,\n  vars: 6,\n  consts: [[\"tabindex\", \"0\", \"role\", \"button\", 1, \"icon\"]],\n  template: function VgPlayPauseComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"vg-icon-pause\", ctx.getState() === \"playing\")(\"vg-icon-play_arrow\", ctx.getState() === \"paused\" || ctx.getState() === \"ended\");\n      i0.ɵɵattribute(\"aria-label\", ctx.getState() === \"paused\" ? \"play\" : \"pause\")(\"aria-valuetext\", ctx.ariaValue);\n    }\n  },\n  styles: [\"vg-play-pause{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-play-pause .icon{pointer-events:none}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgPlayPauseComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-play-pause',\n      encapsulation: ViewEncapsulation.None,\n      template: ` <div\n    class=\"icon\"\n    [class.vg-icon-pause]=\"getState() === 'playing'\"\n    [class.vg-icon-play_arrow]=\"\n      getState() === 'paused' || getState() === 'ended'\n    \"\n    tabindex=\"0\"\n    role=\"button\"\n    [attr.aria-label]=\"getState() === 'paused' ? 'play' : 'pause'\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`,\n      styles: [\"vg-play-pause{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-play-pause .icon{pointer-events:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass VgMuteComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.subscriptions = [];\n    this.ariaValue = 'unmuted';\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n    this.currentVolume = this.target.volume;\n  }\n  onClick() {\n    this.changeMuteState();\n  }\n  onKeyDown(event) {\n    // On press Enter (13) or Space (32)\n    if (event.keyCode === 13 || event.keyCode === 32) {\n      event.preventDefault();\n      this.changeMuteState();\n    }\n  }\n  changeMuteState() {\n    const volume = this.getVolume();\n    if (volume === 0) {\n      if (this.target.volume === 0 && this.currentVolume === 0) {\n        this.currentVolume = 1;\n      }\n      this.target.volume = this.currentVolume;\n    } else {\n      this.currentVolume = volume;\n      this.target.volume = 0;\n    }\n  }\n  getVolume() {\n    const volume = this.target ? this.target.volume : 0;\n    this.ariaValue = volume ? 'unmuted' : 'muted';\n    return volume;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgMuteComponent.ɵfac = function VgMuteComponent_Factory(t) {\n  return new (t || VgMuteComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgMuteComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgMuteComponent,\n  selectors: [[\"vg-mute\"]],\n  hostBindings: function VgMuteComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function VgMuteComponent_click_HostBindingHandler() {\n        return ctx.onClick();\n      })(\"keydown\", function VgMuteComponent_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n  },\n  inputs: {\n    vgFor: \"vgFor\"\n  },\n  decls: 1,\n  vars: 9,\n  consts: [[\"tabindex\", \"0\", \"role\", \"button\", \"aria-label\", \"mute button\", 1, \"icon\"]],\n  template: function VgMuteComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"vg-icon-volume_up\", ctx.getVolume() >= 0.75)(\"vg-icon-volume_down\", ctx.getVolume() >= 0.25 && ctx.getVolume() < 0.75)(\"vg-icon-volume_mute\", ctx.getVolume() > 0 && ctx.getVolume() < 0.25)(\"vg-icon-volume_off\", ctx.getVolume() === 0);\n      i0.ɵɵattribute(\"aria-valuetext\", ctx.ariaValue);\n    }\n  },\n  styles: [\"vg-mute{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-mute .icon{pointer-events:none}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgMuteComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-mute',\n      encapsulation: ViewEncapsulation.None,\n      template: ` <div\n    class=\"icon\"\n    [class.vg-icon-volume_up]=\"getVolume() >= 0.75\"\n    [class.vg-icon-volume_down]=\"getVolume() >= 0.25 && getVolume() < 0.75\"\n    [class.vg-icon-volume_mute]=\"getVolume() > 0 && getVolume() < 0.25\"\n    [class.vg-icon-volume_off]=\"getVolume() === 0\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"mute button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`,\n      styles: [\"vg-mute{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-mute .icon{pointer-events:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass VgFullscreenComponent {\n  constructor(ref, API, fsAPI) {\n    this.API = API;\n    this.fsAPI = fsAPI;\n    this.isFullscreen = false;\n    this.subscriptions = [];\n    this.ariaValue = 'normal mode';\n    this.elem = ref.nativeElement;\n    this.subscriptions.push(this.fsAPI.onChangeFullscreen.subscribe(this.onChangeFullscreen.bind(this)));\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  onChangeFullscreen(fsState) {\n    this.ariaValue = fsState ? 'fullscreen mode' : 'normal mode';\n    this.isFullscreen = fsState;\n  }\n  onClick() {\n    this.changeFullscreenState();\n  }\n  onKeyDown(event) {\n    // On press Enter (13) or Space (32)\n    if (event.keyCode === 13 || event.keyCode === 32) {\n      event.preventDefault();\n      this.changeFullscreenState();\n    }\n  }\n  changeFullscreenState() {\n    let element = this.target;\n    if (this.target instanceof VgApiService) {\n      element = null;\n    }\n    this.fsAPI.toggleFullscreen(element);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgFullscreenComponent.ɵfac = function VgFullscreenComponent_Factory(t) {\n  return new (t || VgFullscreenComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService), i0.ɵɵdirectiveInject(i1.VgFullscreenApiService));\n};\n/** @nocollapse */\nVgFullscreenComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgFullscreenComponent,\n  selectors: [[\"vg-fullscreen\"]],\n  hostBindings: function VgFullscreenComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function VgFullscreenComponent_click_HostBindingHandler() {\n        return ctx.onClick();\n      })(\"keydown\", function VgFullscreenComponent_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n  },\n  decls: 1,\n  vars: 5,\n  consts: [[\"tabindex\", \"0\", \"role\", \"button\", \"aria-label\", \"fullscreen button\", 1, \"icon\"]],\n  template: function VgFullscreenComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"vg-icon-fullscreen\", !ctx.isFullscreen)(\"vg-icon-fullscreen_exit\", ctx.isFullscreen);\n      i0.ɵɵattribute(\"aria-valuetext\", ctx.ariaValue);\n    }\n  },\n  styles: [\"vg-fullscreen{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-fullscreen .icon{pointer-events:none}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgFullscreenComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-fullscreen',\n      encapsulation: ViewEncapsulation.None,\n      template: ` <div\n    class=\"icon\"\n    [class.vg-icon-fullscreen]=\"!isFullscreen\"\n    [class.vg-icon-fullscreen_exit]=\"isFullscreen\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"fullscreen button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`,\n      styles: [\"vg-fullscreen{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-fullscreen .icon{pointer-events:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }, {\n      type: i1.VgFullscreenApiService\n    }];\n  }, {\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass VgScrubBarBufferingTimeComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  getBufferTime() {\n    let bufferTime = '0%';\n    if (this.target?.buffered?.length) {\n      if (this.target.time.total === 0) {\n        bufferTime = '0%';\n      } else {\n        bufferTime = this.target.buffer.end / this.target.time.total * 100 + '%';\n      }\n    }\n    return bufferTime;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgScrubBarBufferingTimeComponent.ɵfac = function VgScrubBarBufferingTimeComponent_Factory(t) {\n  return new (t || VgScrubBarBufferingTimeComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgScrubBarBufferingTimeComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgScrubBarBufferingTimeComponent,\n  selectors: [[\"vg-scrub-bar-buffering-time\"]],\n  inputs: {\n    vgFor: \"vgFor\"\n  },\n  decls: 1,\n  vars: 2,\n  consts: [[1, \"background\"]],\n  template: function VgScrubBarBufferingTimeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.getBufferTime());\n    }\n  },\n  styles: [\"vg-scrub-bar-buffering-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-buffering-time .background{background-color:#ffffff4d}vg-controls vg-scrub-bar-buffering-time{position:absolute;top:calc(50% - 3px)}vg-controls vg-scrub-bar-buffering-time .background{border-radius:2px}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgScrubBarBufferingTimeComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-scrub-bar-buffering-time',\n      encapsulation: ViewEncapsulation.None,\n      template: `<div class=\"background\" [style.width]=\"getBufferTime()\"></div>`,\n      styles: [\"vg-scrub-bar-buffering-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-buffering-time .background{background-color:#ffffff4d}vg-controls vg-scrub-bar-buffering-time{position:absolute;top:calc(50% - 3px)}vg-controls vg-scrub-bar-buffering-time .background{border-radius:2px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }]\n  });\n})();\n\n// tslint:disable-next-line: no-conflicting-lifecycle\nclass VgScrubBarCuePointsComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.onLoadedMetadataCalled = false;\n    this.cuePoints = [];\n    this.subscriptions = [];\n    this.totalCues = 0;\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n    const onTimeUpdate = this.target.subscriptions.loadedMetadata;\n    this.subscriptions.push(onTimeUpdate.subscribe(this.onLoadedMetadata.bind(this)));\n    if (this.onLoadedMetadataCalled) {\n      this.onLoadedMetadata();\n    }\n  }\n  onLoadedMetadata() {\n    if (this.vgCuePoints) {\n      // We need to transform the TextTrackCueList to Array or it doesn't work on IE11/Edge.\n      // See: https://github.com/videogular/videogular2/issues/369\n      this.cuePoints = [];\n      for (let i = 0, l = this.vgCuePoints.length; i < l; i++) {\n        const end = this.vgCuePoints[i].endTime >= 0 ? this.vgCuePoints[i].endTime : this.vgCuePoints[i].startTime + 1;\n        const cuePointDuration = (end - this.vgCuePoints[i].startTime) * 1000;\n        let position = '0';\n        let percentWidth = '0';\n        if (typeof cuePointDuration === 'number' && this.target.time.total) {\n          percentWidth = cuePointDuration * 100 / this.target.time.total + '%';\n          position = this.vgCuePoints[i].startTime * 100 / Math.round(this.target.time.total / 1000) + '%';\n        }\n        this.vgCuePoints[i].$$style = {\n          width: percentWidth,\n          left: position\n        };\n        this.cuePoints.push(this.vgCuePoints[i]);\n      }\n    }\n  }\n  updateCuePoints() {\n    if (!this.target) {\n      this.onLoadedMetadataCalled = true;\n      return;\n    }\n    this.onLoadedMetadata();\n  }\n  ngOnChanges(changes) {\n    if (changes.vgCuePoints.currentValue) {\n      this.updateCuePoints();\n    }\n  }\n  ngDoCheck() {\n    if (this.vgCuePoints) {\n      const changes = this.totalCues !== this.vgCuePoints.length;\n      if (changes) {\n        this.totalCues = this.vgCuePoints.length;\n        this.updateCuePoints();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgScrubBarCuePointsComponent.ɵfac = function VgScrubBarCuePointsComponent_Factory(t) {\n  return new (t || VgScrubBarCuePointsComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgScrubBarCuePointsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgScrubBarCuePointsComponent,\n  selectors: [[\"vg-scrub-bar-cue-points\"]],\n  inputs: {\n    vgCuePoints: \"vgCuePoints\",\n    vgFor: \"vgFor\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 2,\n  vars: 1,\n  consts: [[1, \"cue-point-container\"], [\"class\", \"cue-point\", 3, \"width\", \"left\", 4, \"ngFor\", \"ngForOf\"], [1, \"cue-point\"]],\n  template: function VgScrubBarCuePointsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, VgScrubBarCuePointsComponent_span_1_Template, 1, 4, \"span\", 1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.cuePoints);\n    }\n  },\n  dependencies: [i2.NgForOf],\n  styles: [\"vg-scrub-bar-cue-points{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-cue-points .cue-point-container .cue-point{position:absolute;height:5px;background-color:#ffcc00b3}vg-controls vg-scrub-bar-cue-points{position:absolute;top:calc(50% - 3px)}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgScrubBarCuePointsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-scrub-bar-cue-points',\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div class=\"cue-point-container\">\n      <span\n        *ngFor=\"let cp of cuePoints\"\n        [style.width]=\"cp.$$style?.width\"\n        [style.left]=\"cp.$$style?.left\"\n        class=\"cue-point\"\n      ></span>\n    </div>\n  `,\n      styles: [\"vg-scrub-bar-cue-points{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-cue-points .cue-point-container .cue-point{position:absolute;height:5px;background-color:#ffcc00b3}vg-controls vg-scrub-bar-cue-points{position:absolute;top:calc(50% - 3px)}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgCuePoints: [{\n      type: Input\n    }],\n    vgFor: [{\n      type: Input\n    }]\n  });\n})();\nclass VgScrubBarCurrentTimeComponent {\n  constructor(ref, API) {\n    this.API = API;\n    this.vgSlider = false;\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n  }\n  ngOnInit() {\n    if (this.API.isPlayerReady) {\n      this.onPlayerReady();\n    } else {\n      this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n    }\n  }\n  onPlayerReady() {\n    this.target = this.API.getMediaById(this.vgFor);\n  }\n  getPercentage() {\n    return this.target ? Math.round(this.target.time.current * 100 / this.target.time.total) + '%' : '0%';\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgScrubBarCurrentTimeComponent.ɵfac = function VgScrubBarCurrentTimeComponent_Factory(t) {\n  return new (t || VgScrubBarCurrentTimeComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.VgApiService));\n};\n/** @nocollapse */\nVgScrubBarCurrentTimeComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgScrubBarCurrentTimeComponent,\n  selectors: [[\"vg-scrub-bar-current-time\"]],\n  inputs: {\n    vgFor: \"vgFor\",\n    vgSlider: \"vgSlider\"\n  },\n  decls: 2,\n  vars: 3,\n  consts: [[1, \"background\"], [\"class\", \"slider\", 4, \"ngIf\"], [1, \"slider\"]],\n  template: function VgScrubBarCurrentTimeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n      i0.ɵɵtemplate(1, VgScrubBarCurrentTimeComponent_span_1_Template, 1, 0, \"span\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.getPercentage());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.vgSlider);\n    }\n  },\n  dependencies: [i2.NgIf],\n  styles: [\"vg-scrub-bar-current-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-current-time .background{background-color:#fff}vg-controls vg-scrub-bar-current-time{position:absolute;top:calc(50% - 3px);border-radius:2px}vg-controls vg-scrub-bar-current-time .background{border:1px solid white;border-radius:2px}vg-scrub-bar-current-time .slider{background:white;height:15px;width:15px;border-radius:50%;box-shadow:0 0 10px #000;margin-top:-5px;margin-left:-10px}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgScrubBarCurrentTimeComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-scrub-bar-current-time',\n      encapsulation: ViewEncapsulation.None,\n      template: `<div class=\"background\" [style.width]=\"getPercentage()\"></div>\n    <span class=\"slider\" *ngIf=\"vgSlider\"></span>`,\n      styles: [\"vg-scrub-bar-current-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-current-time .background{background-color:#fff}vg-controls vg-scrub-bar-current-time{position:absolute;top:calc(50% - 3px);border-radius:2px}vg-controls vg-scrub-bar-current-time .background{border:1px solid white;border-radius:2px}vg-scrub-bar-current-time .slider{background:white;height:15px;width:15px;border-radius:50%;box-shadow:0 0 10px #000;margin-top:-5px;margin-left:-10px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.VgApiService\n    }];\n  }, {\n    vgFor: [{\n      type: Input\n    }],\n    vgSlider: [{\n      type: Input\n    }]\n  });\n})();\nconst components = [VgControlsComponent, VgVolumeComponent, VgTrackSelectorComponent, VgTimeDisplayComponent, VgScrubBarComponent, VgQualitySelectorComponent, VgPlaybackButtonComponent, VgPlayPauseComponent, VgMuteComponent, VgFullscreenComponent, VgUtcPipe, VgScrubBarBufferingTimeComponent, VgScrubBarCuePointsComponent, VgScrubBarCurrentTimeComponent];\nclass VgControlsModule {}\n/** @nocollapse */\nVgControlsModule.ɵfac = function VgControlsModule_Factory(t) {\n  return new (t || VgControlsModule)();\n};\n/** @nocollapse */\nVgControlsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: VgControlsModule\n});\n/** @nocollapse */\nVgControlsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, VgCoreModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgControlsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, VgCoreModule],\n      declarations: [...components],\n      exports: [...components]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgControlsComponent, VgControlsModule, VgFullscreenComponent, VgMuteComponent, VgPlayPauseComponent, VgPlaybackButtonComponent, VgQualitySelectorComponent, VgScrubBarBufferingTimeComponent, VgScrubBarComponent, VgScrubBarCuePointsComponent, VgScrubBarCurrentTimeComponent, VgTimeDisplayComponent, VgTrackSelectorComponent, VgUtcPipe, VgVolumeComponent };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "HostBinding", "Input", "ViewChild", "HostListener", "<PERSON><PERSON>", "EventEmitter", "Output", "NgModule", "i2", "CommonModule", "fromEvent", "i1", "VgStates", "VgApiService", "VgCoreModule", "_c0", "_c1", "_c2", "a0", "dragging", "VgTrackSelectorComponent_option_4_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "track_r1", "$implicit", "ɵɵproperty", "id", "selected", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "VgTimeDisplayComponent_span_0_Template", "VgTimeDisplayComponent_span_1_Template", "ɵɵpipe", "ctx_r1", "ɵɵnextContext", "ɵɵtextInterpolate", "ɵɵpipeBind2", "getTime", "vgFormat", "VgQualitySelectorComponent_option_4_Template", "bitrate_r1", "ctx_r0", "qualityIndex", "bitrateSelected", "VgScrubBarCuePointsComponent_span_1_Template", "ɵɵelement", "cp_r1", "ɵɵstyleProp", "$$style", "width", "left", "VgScrubBarCurrentTimeComponent_span_1_Template", "VgControlsComponent", "constructor", "API", "ref", "hidden", "isAdsPlaying", "hideControls", "vgAutohide", "vgAutohideTime", "subscriptions", "elem", "nativeElement", "ngOnInit", "mouseMove$", "videogularElement", "push", "subscribe", "show", "bind", "touchStart$", "mouseClick$", "isPlayerReady", "onPlayerReady", "player<PERSON><PERSON>yEvent", "target", "getMediaById", "vgFor", "play", "onPlay", "pause", "onPause", "startAds", "onStartAds", "endAds", "onEndAds", "ngAfterViewInit", "hide", "clearTimeout", "timer", "state", "<PERSON><PERSON><PERSON>", "VG_PLAYING", "setTimeout", "ngOnDestroy", "for<PERSON>ach", "s", "unsubscribe", "ɵfac", "VgControlsComponent_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "VgControlsHiddenService", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "VgControlsComponent_HostBindings", "ɵɵclassProp", "inputs", "ngContentSelectors", "decls", "vars", "template", "VgControlsComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "VgVolumeComponent", "isDragging", "ariaValue", "getVolume", "onClick", "event", "setVolume", "calculateVolume", "clientX", "onMouseDown", "mouseDownPosX", "onDrag", "onStopDrag", "arrowAdjustVolume", "keyCode", "preventDefault", "Math", "max", "min", "mousePosX", "recObj", "volumeBarRef", "getBoundingClientRect", "volumeBarOffsetLeft", "volumeBarWidth", "vol", "volume", "VgVolumeComponent_Factory", "viewQuery", "VgVolumeComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "VgVolumeComponent_HostBindings", "ɵɵlistener", "VgVolumeComponent_mousemove_HostBindingHandler", "$event", "ɵɵresolveDocument", "VgVolumeComponent_mouseup_HostBindingHandler", "VgVolumeComponent_keydown_HostBindingHandler", "consts", "VgVolumeComponent_Template", "VgVolumeComponent_Template_div_click_0_listener", "VgVolumeComponent_Template_div_mousedown_0_listener", "ɵɵattribute", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "static", "VgTrackSelectorComponent", "subs", "Array", "from", "getMasterMedia", "children", "filter", "item", "tagName", "kind", "map", "default", "srclang", "tracks", "every", "track", "trackSelected", "selectTrack", "trackId", "textTracks", "language", "mode", "VgTrackSelectorComponent_Factory", "VgTrackSelectorComponent_Template", "VgTrackSelectorComponent_Template_select_change_3_listener", "value", "ɵɵtemplate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VgUtcPipe", "transform", "format", "date", "Date", "result", "ss", "getUTCSeconds", "mm", "getUTCMinutes", "hh", "getUTCHours", "replace", "VgUtcPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "VgTimeDisplayComponent", "vgProperty", "round", "time", "isNaN", "isLive", "VgTimeDisplayComponent_Factory", "VgTimeDisplayComponent_Template", "NgIf", "VgScrubBarComponent", "vgControlsHiddenState", "hideScrubBar", "vgSlider", "isSeeking", "wasPlaying", "isHidden", "onHideScrubBar", "seekStart", "canPlay", "seek<PERSON>ove", "offset", "percentage", "scrollWidth", "current", "total", "seekTime", "seekEnd", "touchEnd", "getTouchOffset", "offsetLeft", "element", "offsetParent", "touches", "pageX", "onMouseDownScrubBar", "offsetX", "onMouseMoveScrubBar", "onMouseUpScrubBar", "onTouchStartScrubBar", "_$event", "onTouchMoveScrubBar", "onTouchCancelScrubBar", "onTouchEndScrubBar", "getPercentage", "VgScrubBarComponent_Factory", "VgScrubBarComponent_HostBindings", "VgScrubBarComponent_mousedown_HostBindingHandler", "VgScrubBarComponent_mousemove_HostBindingHandler", "VgScrubBarComponent_mouseup_HostBindingHandler", "VgScrubBarComponent_touchstart_HostBindingHandler", "VgScrubBarComponent_touchmove_HostBindingHandler", "VgScrubBarComponent_touchcancel_HostBindingHandler", "VgScrubBarComponent_touchend_HostBindingHandler", "VgScrubBarComponent_keydown_HostBindingHandler", "VgScrubBarComponent_Template", "VgQualitySelectorComponent", "onBitrateChange", "ngOnChanges", "changes", "bitrates", "currentValue", "length", "bitrate", "toString", "selectBitrate", "index", "emit", "VgQualitySelectorComponent_Factory", "outputs", "features", "ɵɵNgOnChangesFeature", "VgQualitySelectorComponent_Template", "VgQualitySelectorComponent_Template_select_change_3_listener", "VgPlaybackButtonComponent", "cdr", "playback<PERSON><PERSON><PERSON>", "playbackIndex", "updatePlaybackSpeed", "onKeyDown", "playbackValue", "playbackRate", "detectChanges", "getPlaybackRate", "e", "console", "warn", "VgPlaybackButtonComponent_Factory", "ChangeDetectorRef", "VgPlaybackButtonComponent_HostBindings", "VgPlaybackButtonComponent_click_HostBindingHandler", "VgPlaybackButtonComponent_keydown_HostBindingHandler", "VgPlaybackButtonComponent_Template", "VgPlayPauseComponent", "VG_PAUSED", "playPause", "getState", "VG_ENDED", "VgPlayPauseComponent_Factory", "VgPlayPauseComponent_HostBindings", "VgPlayPauseComponent_click_HostBindingHandler", "VgPlayPauseComponent_keydown_HostBindingHandler", "VgPlayPauseComponent_Template", "VgMuteComponent", "currentVolume", "changeMuteState", "VgMuteComponent_Factory", "VgMuteComponent_HostBindings", "VgMuteComponent_click_HostBindingHandler", "VgMuteComponent_keydown_HostBindingHandler", "VgMuteComponent_Template", "VgFullscreenComponent", "fsAPI", "isFullscreen", "onChangeFullscreen", "fsState", "changeFullscreenState", "toggleFullscreen", "VgFullscreenComponent_Factory", "VgFullscreenApiService", "VgFullscreenComponent_HostBindings", "VgFullscreenComponent_click_HostBindingHandler", "VgFullscreenComponent_keydown_HostBindingHandler", "VgFullscreenComponent_Template", "VgScrubBarBufferingTimeComponent", "getBufferTime", "bufferTime", "buffered", "buffer", "end", "VgScrubBarBufferingTimeComponent_Factory", "VgScrubBarBufferingTimeComponent_Template", "VgScrubBarCuePointsComponent", "onLoadedMetadataCalled", "cuePoints", "totalCues", "onTimeUpdate", "loadedMetadata", "onLoadedMetadata", "vgCuePoints", "i", "l", "endTime", "startTime", "cuePointDuration", "position", "percentWidth", "updateCuePoints", "ngDoCheck", "VgScrubBarCuePointsComponent_Factory", "VgScrubBarCuePointsComponent_Template", "VgScrubBarCurrentTimeComponent", "VgScrubBarCurrentTimeComponent_Factory", "VgScrubBarCurrentTimeComponent_Template", "components", "VgControlsModule", "VgControlsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@videogular/ngx-videogular/fesm2020/videogular-ngx-videogular-controls.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, HostBinding, Input, ViewChild, HostListener, Pipe, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { fromEvent } from 'rxjs';\nimport * as i1 from '@videogular/ngx-videogular/core';\nimport { VgStates, VgApiService, VgCoreModule } from '@videogular/ngx-videogular/core';\n\nclass VgControlsComponent {\n    // @ts-ignore\n    constructor(API, ref, hidden) {\n        this.API = API;\n        this.hidden = hidden;\n        this.isAdsPlaying = 'initial';\n        this.hideControls = false;\n        this.vgAutohide = false;\n        this.vgAutohideTime = 3;\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        this.mouseMove$ = fromEvent(this.API.videogularElement, 'mousemove');\n        this.subscriptions.push(this.mouseMove$.subscribe(this.show.bind(this)));\n        this.touchStart$ = fromEvent(this.API.videogularElement, 'touchstart');\n        this.subscriptions.push(this.touchStart$.subscribe(this.show.bind(this)));\n        this.mouseClick$ = fromEvent(this.API.videogularElement, 'click');\n        this.subscriptions.push(this.mouseClick$.subscribe(this.show.bind(this)));\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n        this.subscriptions.push(this.target.subscriptions.play.subscribe(this.onPlay.bind(this)));\n        this.subscriptions.push(this.target.subscriptions.pause.subscribe(this.onPause.bind(this)));\n        this.subscriptions.push(this.target.subscriptions.startAds.subscribe(this.onStartAds.bind(this)));\n        this.subscriptions.push(this.target.subscriptions.endAds.subscribe(this.onEndAds.bind(this)));\n    }\n    ngAfterViewInit() {\n        if (this.vgAutohide) {\n            this.hide();\n        }\n        else {\n            this.show();\n        }\n    }\n    onPlay() {\n        if (this.vgAutohide) {\n            this.hide();\n        }\n    }\n    onPause() {\n        clearTimeout(this.timer);\n        this.hideControls = false;\n        this.hidden.state(false);\n    }\n    onStartAds() {\n        this.isAdsPlaying = 'none';\n    }\n    onEndAds() {\n        this.isAdsPlaying = 'initial';\n    }\n    hide() {\n        if (this.vgAutohide) {\n            clearTimeout(this.timer);\n            this.hideAsync();\n        }\n    }\n    show() {\n        clearTimeout(this.timer);\n        this.hideControls = false;\n        this.hidden.state(false);\n        if (this.vgAutohide) {\n            this.hideAsync();\n        }\n    }\n    hideAsync() {\n        if (this.API.state === VgStates.VG_PLAYING) {\n            this.timer = setTimeout(() => {\n                this.hideControls = true;\n                this.hidden.state(true);\n            }, this.vgAutohideTime * 1000);\n        }\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgControlsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsComponent, deps: [{ token: i1.VgApiService }, { token: i0.ElementRef }, { token: i1.VgControlsHiddenService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgControlsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgControlsComponent, selector: \"vg-controls\", inputs: { vgFor: \"vgFor\", vgAutohide: \"vgAutohide\", vgAutohideTime: \"vgAutohideTime\" }, host: { properties: { \"style.pointer-events\": \"this.isAdsPlaying\", \"class.hide\": \"this.hideControls\" } }, ngImport: i0, template: `<ng-content></ng-content>`, isInline: true, styles: [\"vg-controls{position:absolute;display:flex;width:100%;height:50px;z-index:300;bottom:0;background-color:#00000080;transition:bottom 1s}vg-controls.hide{bottom:-50px}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-controls', encapsulation: ViewEncapsulation.None, template: `<ng-content></ng-content>`, styles: [\"vg-controls{position:absolute;display:flex;width:100%;height:50px;z-index:300;bottom:0;background-color:#00000080;transition:bottom 1s}vg-controls.hide{bottom:-50px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i1.VgApiService }, { type: i0.ElementRef }, { type: i1.VgControlsHiddenService }]; }, propDecorators: { isAdsPlaying: [{\n                type: HostBinding,\n                args: ['style.pointer-events']\n            }], hideControls: [{\n                type: HostBinding,\n                args: ['class.hide']\n            }], vgFor: [{\n                type: Input\n            }], vgAutohide: [{\n                type: Input\n            }], vgAutohideTime: [{\n                type: Input\n            }] } });\n\nclass VgVolumeComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n        this.isDragging = false;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n        this.ariaValue = this.getVolume() * 100;\n    }\n    onClick(event) {\n        this.setVolume(this.calculateVolume(event.clientX));\n    }\n    onMouseDown(event) {\n        this.mouseDownPosX = event.clientX;\n        this.isDragging = true;\n    }\n    onDrag(event) {\n        if (this.isDragging) {\n            this.setVolume(this.calculateVolume(event.clientX));\n        }\n    }\n    onStopDrag(event) {\n        if (this.isDragging) {\n            this.isDragging = false;\n            if (this.mouseDownPosX === event.clientX) {\n                this.setVolume(this.calculateVolume(event.clientX));\n            }\n        }\n    }\n    arrowAdjustVolume(event) {\n        if (event.keyCode === 38 || event.keyCode === 39) {\n            event.preventDefault();\n            this.setVolume(Math.max(0, Math.min(100, this.getVolume() * 100 + 10)));\n        }\n        else if (event.keyCode === 37 || event.keyCode === 40) {\n            event.preventDefault();\n            this.setVolume(Math.max(0, Math.min(100, this.getVolume() * 100 - 10)));\n        }\n    }\n    calculateVolume(mousePosX) {\n        const recObj = this.volumeBarRef.nativeElement.getBoundingClientRect();\n        const volumeBarOffsetLeft = recObj.left;\n        const volumeBarWidth = recObj.width;\n        return ((mousePosX - volumeBarOffsetLeft) / volumeBarWidth) * 100;\n    }\n    setVolume(vol) {\n        this.target.volume = Math.max(0, Math.min(1, vol / 100));\n        this.ariaValue = this.target.volume * 100;\n    }\n    getVolume() {\n        return this.target ? this.target.volume : 0;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgVolumeComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgVolumeComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgVolumeComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgVolumeComponent, selector: \"vg-volume\", inputs: { vgFor: \"vgFor\" }, host: { listeners: { \"document:mousemove\": \"onDrag($event)\", \"document:mouseup\": \"onStopDrag($event)\", \"keydown\": \"arrowAdjustVolume($event)\" } }, viewQueries: [{ propertyName: \"volumeBarRef\", first: true, predicate: [\"volumeBar\"], descendants: true, static: true }], ngImport: i0, template: `\n    <div\n      #volumeBar\n      class=\"volumeBar\"\n      tabindex=\"0\"\n      role=\"slider\"\n      aria-label=\"volume level\"\n      aria-level=\"polite\"\n      [attr.aria-valuenow]=\"ariaValue\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      aria-orientation=\"horizontal\"\n      [attr.aria-valuetext]=\"ariaValue + '%'\"\n      (click)=\"onClick($event)\"\n      (mousedown)=\"onMouseDown($event)\"\n    >\n      <div class=\"volumeBackground\" [ngClass]=\"{ dragging: isDragging }\">\n        <div\n          class=\"volumeValue\"\n          [style.width]=\"getVolume() * (100 - 15) + '%'\"\n        ></div>\n        <div\n          class=\"volumeKnob\"\n          [style.left]=\"getVolume() * (100 - 15) + '%'\"\n        ></div>\n      </div>\n    </div>\n  `, isInline: true, styles: [\"vg-volume{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:100px;cursor:pointer;color:#fff;line-height:50px}vg-volume .volumeBar{position:relative;display:flex;flex-grow:1;align-items:center}vg-volume .volumeBackground{display:flex;flex-grow:1;height:5px;pointer-events:none;background-color:#333}vg-volume .volumeValue{display:flex;height:5px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeKnob{position:absolute;width:15px;height:15px;left:0;top:50%;transform:translateY(-50%);border-radius:15px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeBackground.dragging .volumeValue,vg-volume .volumeBackground.dragging .volumeKnob{transition:none}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgVolumeComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-volume', encapsulation: ViewEncapsulation.None, template: `\n    <div\n      #volumeBar\n      class=\"volumeBar\"\n      tabindex=\"0\"\n      role=\"slider\"\n      aria-label=\"volume level\"\n      aria-level=\"polite\"\n      [attr.aria-valuenow]=\"ariaValue\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      aria-orientation=\"horizontal\"\n      [attr.aria-valuetext]=\"ariaValue + '%'\"\n      (click)=\"onClick($event)\"\n      (mousedown)=\"onMouseDown($event)\"\n    >\n      <div class=\"volumeBackground\" [ngClass]=\"{ dragging: isDragging }\">\n        <div\n          class=\"volumeValue\"\n          [style.width]=\"getVolume() * (100 - 15) + '%'\"\n        ></div>\n        <div\n          class=\"volumeKnob\"\n          [style.left]=\"getVolume() * (100 - 15) + '%'\"\n        ></div>\n      </div>\n    </div>\n  `, styles: [\"vg-volume{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:100px;cursor:pointer;color:#fff;line-height:50px}vg-volume .volumeBar{position:relative;display:flex;flex-grow:1;align-items:center}vg-volume .volumeBackground{display:flex;flex-grow:1;height:5px;pointer-events:none;background-color:#333}vg-volume .volumeValue{display:flex;height:5px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeKnob{position:absolute;width:15px;height:15px;left:0;top:50%;transform:translateY(-50%);border-radius:15px;pointer-events:none;background-color:#fff;transition:all .2s ease-out}vg-volume .volumeBackground.dragging .volumeValue,vg-volume .volumeBackground.dragging .volumeKnob{transition:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], volumeBarRef: [{\n                type: ViewChild,\n                args: ['volumeBar', { static: true }]\n            }], onDrag: [{\n                type: HostListener,\n                args: ['document:mousemove', ['$event']]\n            }], onStopDrag: [{\n                type: HostListener,\n                args: ['document:mouseup', ['$event']]\n            }], arrowAdjustVolume: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass VgTrackSelectorComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n        const subs = Array.from(this.API.getMasterMedia().elem.children)\n            .filter((item) => item.tagName === 'TRACK')\n            .filter((item) => item.kind === 'subtitles')\n            .map((item) => ({\n            label: item.label,\n            selected: item.default === true,\n            id: item.srclang,\n        }));\n        this.tracks = [\n            ...subs,\n            {\n                id: null,\n                label: 'Off',\n                selected: subs.every((item) => item.selected === false),\n            },\n        ];\n        const track = this.tracks.filter((item) => item.selected === true)[0];\n        this.trackSelected = track.id;\n        this.ariaValue = track.label;\n    }\n    selectTrack(trackId) {\n        this.trackSelected = trackId === 'null' ? null : trackId;\n        this.ariaValue = 'No track selected';\n        Array.from(this.API.getMasterMedia().elem.textTracks).forEach((item) => {\n            if (item.language === trackId) {\n                this.ariaValue = item.label;\n                item.mode = 'showing';\n            }\n            else {\n                item.mode = 'hidden';\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgTrackSelectorComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgTrackSelectorComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgTrackSelectorComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgTrackSelectorComponent, selector: \"vg-track-selector\", inputs: { vgFor: \"vgFor\" }, ngImport: i0, template: `\n    <div class=\"container\">\n      <div\n        class=\"track-selected\"\n        [class.vg-icon-closed_caption]=\"!trackSelected\"\n      >\n        {{ trackSelected || '' }}\n      </div>\n      <select\n        class=\"trackSelector\"\n        (change)=\"selectTrack($event.target.value)\"\n        tabindex=\"0\"\n        aria-label=\"track selector\"\n        [attr.aria-valuetext]=\"ariaValue\"\n      >\n        <option\n          *ngFor=\"let track of tracks\"\n          [value]=\"track.id\"\n          [selected]=\"track.selected === true\"\n        >\n          {{ track.label }}\n        </option>\n      </select>\n    </div>\n  `, isInline: true, styles: [\"vg-track-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-track-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-track-selector select.trackSelector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-track-selector select.trackSelector::-ms-expand{display:none}vg-track-selector select.trackSelector option{color:#000}vg-track-selector .track-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-track-selector .vg-icon-closed_caption:before{width:100%}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgTrackSelectorComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-track-selector', encapsulation: ViewEncapsulation.None, template: `\n    <div class=\"container\">\n      <div\n        class=\"track-selected\"\n        [class.vg-icon-closed_caption]=\"!trackSelected\"\n      >\n        {{ trackSelected || '' }}\n      </div>\n      <select\n        class=\"trackSelector\"\n        (change)=\"selectTrack($event.target.value)\"\n        tabindex=\"0\"\n        aria-label=\"track selector\"\n        [attr.aria-valuetext]=\"ariaValue\"\n      >\n        <option\n          *ngFor=\"let track of tracks\"\n          [value]=\"track.id\"\n          [selected]=\"track.selected === true\"\n        >\n          {{ track.label }}\n        </option>\n      </select>\n    </div>\n  `, styles: [\"vg-track-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-track-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-track-selector select.trackSelector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-track-selector select.trackSelector::-ms-expand{display:none}vg-track-selector select.trackSelector option{color:#000}vg-track-selector .track-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-track-selector .vg-icon-closed_caption:before{width:100%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }] } });\n\n// Workaround until we can use UTC with Angular Date Pipe\nclass VgUtcPipe {\n    transform(value, format) {\n        let date = new Date(value);\n        let result = format;\n        let ss = date.getUTCSeconds();\n        let mm = date.getUTCMinutes();\n        let hh = date.getUTCHours();\n        if (ss < 10) {\n            ss = '0' + ss;\n        }\n        if (mm < 10) {\n            mm = '0' + mm;\n        }\n        if (hh < 10) {\n            hh = '0' + hh;\n        }\n        result = result.replace(/ss/g, ss);\n        result = result.replace(/mm/g, mm);\n        result = result.replace(/hh/g, hh);\n        return result;\n    }\n}\n/** @nocollapse */ VgUtcPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgUtcPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\n/** @nocollapse */ VgUtcPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: VgUtcPipe, name: \"vgUtc\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgUtcPipe, decorators: [{\n            type: Pipe,\n            args: [{ name: 'vgUtc' }]\n        }] });\nclass VgTimeDisplayComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.vgProperty = 'current';\n        this.vgFormat = 'mm:ss';\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    getTime() {\n        let t = 0;\n        if (this.target) {\n            t = Math.round(this.target.time[this.vgProperty]);\n            t = isNaN(t) || this.target.isLive ? 0 : t;\n        }\n        return t;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgTimeDisplayComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgTimeDisplayComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgTimeDisplayComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgTimeDisplayComponent, selector: \"vg-time-display\", inputs: { vgFor: \"vgFor\", vgProperty: \"vgProperty\", vgFormat: \"vgFormat\" }, ngImport: i0, template: `\n    <span *ngIf=\"target?.isLive\">LIVE</span>\n    <span *ngIf=\"!target?.isLive\">{{ getTime() | vgUtc: vgFormat }}</span>\n    <ng-content></ng-content>\n  `, isInline: true, styles: [\"vg-time-display{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:60px;cursor:pointer;color:#fff;line-height:50px;pointer-events:none;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"pipe\", type: VgUtcPipe, name: \"vgUtc\" }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgTimeDisplayComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-time-display', encapsulation: ViewEncapsulation.None, template: `\n    <span *ngIf=\"target?.isLive\">LIVE</span>\n    <span *ngIf=\"!target?.isLive\">{{ getTime() | vgUtc: vgFormat }}</span>\n    <ng-content></ng-content>\n  `, styles: [\"vg-time-display{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:60px;cursor:pointer;color:#fff;line-height:50px;pointer-events:none;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], vgProperty: [{\n                type: Input\n            }], vgFormat: [{\n                type: Input\n            }] } });\n\nclass VgScrubBarComponent {\n    constructor(ref, API, vgControlsHiddenState) {\n        this.API = API;\n        this.hideScrubBar = false;\n        this.vgSlider = true;\n        this.isSeeking = false;\n        this.wasPlaying = false;\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n        this.subscriptions.push(vgControlsHiddenState.isHidden.subscribe((hide) => this.onHideScrubBar(hide)));\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    seekStart() {\n        if (this.target.canPlay) {\n            this.isSeeking = true;\n            if (this.target.state === VgStates.VG_PLAYING) {\n                this.wasPlaying = true;\n            }\n            this.target.pause();\n        }\n    }\n    seekMove(offset) {\n        if (this.isSeeking) {\n            const percentage = Math.max(Math.min((offset * 100) / this.elem.scrollWidth, 99.9), 0);\n            this.target.time.current = (percentage * this.target.time.total) / 100;\n            this.target.seekTime(percentage, true);\n        }\n    }\n    seekEnd(offset) {\n        this.isSeeking = false;\n        if (this.target.canPlay) {\n            if (offset !== false) {\n                const percentage = Math.max(Math.min((offset * 100) / this.elem.scrollWidth, 99.9), 0);\n                this.target.seekTime(percentage, true);\n            }\n            if (this.wasPlaying) {\n                this.wasPlaying = false;\n                this.target.play();\n            }\n        }\n    }\n    touchEnd() {\n        this.isSeeking = false;\n        if (this.wasPlaying) {\n            this.wasPlaying = false;\n            this.target.play();\n        }\n    }\n    getTouchOffset(event) {\n        let offsetLeft = 0;\n        let element = event.target;\n        while (element) {\n            offsetLeft += element.offsetLeft;\n            element = element.offsetParent;\n        }\n        return event.touches[0].pageX - offsetLeft;\n    }\n    onMouseDownScrubBar($event) {\n        if (this.target) {\n            if (!this.target.isLive) {\n                if (!this.vgSlider) {\n                    this.seekEnd($event.offsetX);\n                }\n                else {\n                    this.seekStart();\n                }\n            }\n        }\n    }\n    onMouseMoveScrubBar($event) {\n        if (this.target) {\n            if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n                this.seekMove($event.offsetX);\n            }\n        }\n    }\n    onMouseUpScrubBar($event) {\n        if (this.target) {\n            if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n                this.seekEnd($event.offsetX);\n            }\n        }\n    }\n    onTouchStartScrubBar(_$event) {\n        if (this.target) {\n            if (!this.target.isLive) {\n                if (!this.vgSlider) {\n                    this.seekEnd(false);\n                }\n                else {\n                    this.seekStart();\n                }\n            }\n        }\n    }\n    onTouchMoveScrubBar($event) {\n        if (this.target) {\n            if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n                this.seekMove(this.getTouchOffset($event));\n            }\n        }\n    }\n    // @ts-ignore\n    onTouchCancelScrubBar(_$event) {\n        if (this.target) {\n            if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n                this.touchEnd();\n            }\n        }\n    }\n    // @ts-ignore\n    onTouchEndScrubBar(_$event) {\n        if (this.target) {\n            if (!this.target.isLive && this.vgSlider && this.isSeeking) {\n                this.touchEnd();\n            }\n        }\n    }\n    arrowAdjustVolume(event) {\n        if (this.target) {\n            if (event.keyCode === 38 || event.keyCode === 39) {\n                event.preventDefault();\n                this.target.seekTime((this.target.time.current + 5000) / 1000, false);\n            }\n            else if (event.keyCode === 37 || event.keyCode === 40) {\n                event.preventDefault();\n                this.target.seekTime((this.target.time.current - 5000) / 1000, false);\n            }\n        }\n    }\n    getPercentage() {\n        return this.target\n            ? Math.round((this.target.time.current * 100) / this.target.time.total) + '%'\n            : '0%';\n    }\n    onHideScrubBar(hide) {\n        this.hideScrubBar = hide;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgScrubBarComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }, { token: i1.VgControlsHiddenService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgScrubBarComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgScrubBarComponent, selector: \"vg-scrub-bar\", inputs: { vgFor: \"vgFor\", vgSlider: \"vgSlider\" }, host: { listeners: { \"mousedown\": \"onMouseDownScrubBar($event)\", \"document:mousemove\": \"onMouseMoveScrubBar($event)\", \"document:mouseup\": \"onMouseUpScrubBar($event)\", \"touchstart\": \"onTouchStartScrubBar($event)\", \"document:touchmove\": \"onTouchMoveScrubBar($event)\", \"document:touchcancel\": \"onTouchCancelScrubBar($event)\", \"document:touchend\": \"onTouchEndScrubBar($event)\", \"keydown\": \"arrowAdjustVolume($event)\" }, properties: { \"class.hide\": \"this.hideScrubBar\" } }, ngImport: i0, template: `\n    <div\n      class=\"scrubBar\"\n      tabindex=\"0\"\n      role=\"slider\"\n      aria-label=\"scrub bar\"\n      aria-level=\"polite\"\n      [attr.aria-valuenow]=\"getPercentage()\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      [attr.aria-valuetext]=\"getPercentage()\"\n    >\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\"vg-scrub-bar{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;position:absolute;width:100%;height:5px;bottom:50px;margin:0;cursor:pointer;align-items:center;background:rgba(0,0,0,.75);z-index:250;transition:bottom 1s,opacity .5s}vg-scrub-bar .scrubBar{position:relative;display:flex;flex-grow:1;align-items:center;height:100%}vg-controls vg-scrub-bar{position:relative;bottom:0;background:transparent;height:50px;flex-grow:1;flex-basis:0;margin:0 10px;transition:initial}vg-scrub-bar.hide{bottom:0;opacity:0}vg-controls vg-scrub-bar.hide{bottom:initial;opacity:initial}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-scrub-bar', encapsulation: ViewEncapsulation.None, template: `\n    <div\n      class=\"scrubBar\"\n      tabindex=\"0\"\n      role=\"slider\"\n      aria-label=\"scrub bar\"\n      aria-level=\"polite\"\n      [attr.aria-valuenow]=\"getPercentage()\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      [attr.aria-valuetext]=\"getPercentage()\"\n    >\n      <ng-content></ng-content>\n    </div>\n  `, styles: [\"vg-scrub-bar{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;position:absolute;width:100%;height:5px;bottom:50px;margin:0;cursor:pointer;align-items:center;background:rgba(0,0,0,.75);z-index:250;transition:bottom 1s,opacity .5s}vg-scrub-bar .scrubBar{position:relative;display:flex;flex-grow:1;align-items:center;height:100%}vg-controls vg-scrub-bar{position:relative;bottom:0;background:transparent;height:50px;flex-grow:1;flex-basis:0;margin:0 10px;transition:initial}vg-scrub-bar.hide{bottom:0;opacity:0}vg-controls vg-scrub-bar.hide{bottom:initial;opacity:initial}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }, { type: i1.VgControlsHiddenService }]; }, propDecorators: { hideScrubBar: [{\n                type: HostBinding,\n                args: ['class.hide']\n            }], vgFor: [{\n                type: Input\n            }], vgSlider: [{\n                type: Input\n            }], onMouseDownScrubBar: [{\n                type: HostListener,\n                args: ['mousedown', ['$event']]\n            }], onMouseMoveScrubBar: [{\n                type: HostListener,\n                args: ['document:mousemove', ['$event']]\n            }], onMouseUpScrubBar: [{\n                type: HostListener,\n                args: ['document:mouseup', ['$event']]\n            }], onTouchStartScrubBar: [{\n                type: HostListener,\n                args: ['touchstart', ['$event']]\n            }], onTouchMoveScrubBar: [{\n                type: HostListener,\n                args: ['document:touchmove', ['$event']]\n            }], onTouchCancelScrubBar: [{\n                type: HostListener,\n                args: ['document:touchcancel', ['$event']]\n            }], onTouchEndScrubBar: [{\n                type: HostListener,\n                args: ['document:touchend', ['$event']]\n            }], arrowAdjustVolume: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass VgQualitySelectorComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.onBitrateChange = new EventEmitter();\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() { }\n    ngOnChanges(changes) {\n        if (changes.bitrates.currentValue && changes.bitrates.currentValue.length) {\n            this.bitrates.forEach((item) => (item.label =\n                item.label || Math.round(item.bitrate / 1000).toString()));\n        }\n    }\n    selectBitrate(index) {\n        this.bitrateSelected = this.bitrates[index];\n        this.onBitrateChange.emit(this.bitrates[index]);\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgQualitySelectorComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgQualitySelectorComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgQualitySelectorComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgQualitySelectorComponent, selector: \"vg-quality-selector\", inputs: { bitrates: \"bitrates\" }, outputs: { onBitrateChange: \"onBitrateChange\" }, usesOnChanges: true, ngImport: i0, template: `\n    <div class=\"container\">\n      <div class=\"quality-selected\" [class.vg-icon-hd]=\"!bitrateSelected\">\n        {{ bitrateSelected?.label }}\n      </div>\n      <select\n        class=\"quality-selector\"\n        (change)=\"selectBitrate($event.target.value)\"\n        tabindex=\"0\"\n        aria-label=\"quality selector\"\n        [attr.aria-valuetext]=\"ariaValue\"\n      >\n        <option\n          *ngFor=\"let bitrate of bitrates\"\n          [value]=\"bitrate.qualityIndex\"\n          [selected]=\"bitrate.qualityIndex === bitrateSelected?.qualityIndex\"\n        >\n          {{ bitrate.label }}\n        </option>\n      </select>\n    </div>\n  `, isInline: true, styles: [\"vg-quality-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-quality-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-quality-selector select.quality-selector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-quality-selector select.quality-selector::-ms-expand{display:none}vg-quality-selector select.quality-selector option{color:#000}vg-quality-selector .quality-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-quality-selector .vg-icon-closed_caption:before{width:100%}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgQualitySelectorComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-quality-selector', encapsulation: ViewEncapsulation.None, template: `\n    <div class=\"container\">\n      <div class=\"quality-selected\" [class.vg-icon-hd]=\"!bitrateSelected\">\n        {{ bitrateSelected?.label }}\n      </div>\n      <select\n        class=\"quality-selector\"\n        (change)=\"selectBitrate($event.target.value)\"\n        tabindex=\"0\"\n        aria-label=\"quality selector\"\n        [attr.aria-valuetext]=\"ariaValue\"\n      >\n        <option\n          *ngFor=\"let bitrate of bitrates\"\n          [value]=\"bitrate.qualityIndex\"\n          [selected]=\"bitrate.qualityIndex === bitrateSelected?.qualityIndex\"\n        >\n          {{ bitrate.label }}\n        </option>\n      </select>\n    </div>\n  `, styles: [\"vg-quality-selector{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;width:50px;height:50px;cursor:pointer;color:#fff;line-height:50px}vg-quality-selector .container{position:relative;display:flex;flex-grow:1;align-items:center;padding:0;margin:5px}vg-quality-selector select.quality-selector{width:50px;padding:5px 8px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;color:transparent;font-size:16px}vg-quality-selector select.quality-selector::-ms-expand{display:none}vg-quality-selector select.quality-selector option{color:#000}vg-quality-selector .quality-selected{position:absolute;width:100%;height:50px;top:-6px;text-align:center;text-transform:uppercase;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding-top:2px;pointer-events:none}vg-quality-selector .vg-icon-closed_caption:before{width:100%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { bitrates: [{\n                type: Input\n            }], onBitrateChange: [{\n                type: Output\n            }] } });\n\nclass VgPlaybackButtonComponent {\n    constructor(ref, API, cdr) {\n        this.API = API;\n        this.cdr = cdr;\n        this.subscriptions = [];\n        this.ariaValue = 1;\n        this.elem = ref.nativeElement;\n        this.playbackValues = ['0.5', '1.0', '1.5', '2.0'];\n        this.playbackIndex = 1;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    onClick() {\n        this.updatePlaybackSpeed();\n    }\n    onKeyDown(event) {\n        // On press Enter (13) or Space (32)\n        if (event.keyCode === 13 || event.keyCode === 32) {\n            event.preventDefault();\n            this.updatePlaybackSpeed();\n        }\n    }\n    updatePlaybackSpeed() {\n        this.playbackValues.forEach((playbackValue, index) => {\n            if (playbackValue.length === 1) {\n                this.playbackValues[index] = playbackValue + '.0';\n            }\n        });\n        this.playbackIndex = ++this.playbackIndex % this.playbackValues.length;\n        if (this.target instanceof VgApiService) {\n            this.target.playbackRate = this.playbackValues[this.playbackIndex];\n        }\n        else {\n            this.target.playbackRate[this.vgFor] = this.playbackValues[this.playbackIndex];\n        }\n        this.detectChanges();\n    }\n    getPlaybackRate() {\n        this.ariaValue = this.target ? this.target.playbackRate : 1.0;\n        return this.ariaValue;\n    }\n    detectChanges() {\n        try {\n            this.cdr.detectChanges();\n        }\n        catch (e) {\n            console.warn(e);\n        }\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgPlaybackButtonComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgPlaybackButtonComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgPlaybackButtonComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgPlaybackButtonComponent, selector: \"vg-playback-button\", inputs: { vgFor: \"vgFor\", playbackValues: \"playbackValues\" }, host: { listeners: { \"click\": \"onClick()\", \"keydown\": \"onKeyDown($event)\" } }, ngImport: i0, template: ` <span\n    class=\"button\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"playback speed button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  >\n    {{ getPlaybackRate() }}x\n  </span>`, isInline: true, styles: [\"vg-playback-button{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}vg-playback-button .button{display:flex;align-items:center;justify-content:center;width:50px}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgPlaybackButtonComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-playback-button', encapsulation: ViewEncapsulation.None, template: ` <span\n    class=\"button\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"playback speed button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  >\n    {{ getPlaybackRate() }}x\n  </span>`, styles: [\"vg-playback-button{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}vg-playback-button .button{display:flex;align-items:center;justify-content:center;width:50px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], playbackValues: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass VgPlayPauseComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.subscriptions = [];\n        this.ariaValue = VgStates.VG_PAUSED;\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    onClick() {\n        this.playPause();\n    }\n    onKeyDown(event) {\n        // On press Enter (13) or Space (32)\n        if (event.keyCode === 13 || event.keyCode === 32) {\n            event.preventDefault();\n            this.playPause();\n        }\n    }\n    playPause() {\n        const state = this.getState();\n        switch (state) {\n            case VgStates.VG_PLAYING:\n                this.target.pause();\n                break;\n            case VgStates.VG_PAUSED:\n            case VgStates.VG_ENDED:\n                this.target.play();\n                break;\n        }\n    }\n    getState() {\n        this.ariaValue = this.target ? this.target.state : VgStates.VG_PAUSED;\n        return this.ariaValue;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgPlayPauseComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgPlayPauseComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgPlayPauseComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgPlayPauseComponent, selector: \"vg-play-pause\", inputs: { vgFor: \"vgFor\" }, host: { listeners: { \"click\": \"onClick()\", \"keydown\": \"onKeyDown($event)\" } }, ngImport: i0, template: ` <div\n    class=\"icon\"\n    [class.vg-icon-pause]=\"getState() === 'playing'\"\n    [class.vg-icon-play_arrow]=\"\n      getState() === 'paused' || getState() === 'ended'\n    \"\n    tabindex=\"0\"\n    role=\"button\"\n    [attr.aria-label]=\"getState() === 'paused' ? 'play' : 'pause'\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`, isInline: true, styles: [\"vg-play-pause{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-play-pause .icon{pointer-events:none}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgPlayPauseComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-play-pause', encapsulation: ViewEncapsulation.None, template: ` <div\n    class=\"icon\"\n    [class.vg-icon-pause]=\"getState() === 'playing'\"\n    [class.vg-icon-play_arrow]=\"\n      getState() === 'paused' || getState() === 'ended'\n    \"\n    tabindex=\"0\"\n    role=\"button\"\n    [attr.aria-label]=\"getState() === 'paused' ? 'play' : 'pause'\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`, styles: [\"vg-play-pause{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-play-pause .icon{pointer-events:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass VgMuteComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.subscriptions = [];\n        this.ariaValue = 'unmuted';\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n        this.currentVolume = this.target.volume;\n    }\n    onClick() {\n        this.changeMuteState();\n    }\n    onKeyDown(event) {\n        // On press Enter (13) or Space (32)\n        if (event.keyCode === 13 || event.keyCode === 32) {\n            event.preventDefault();\n            this.changeMuteState();\n        }\n    }\n    changeMuteState() {\n        const volume = this.getVolume();\n        if (volume === 0) {\n            if (this.target.volume === 0 && this.currentVolume === 0) {\n                this.currentVolume = 1;\n            }\n            this.target.volume = this.currentVolume;\n        }\n        else {\n            this.currentVolume = volume;\n            this.target.volume = 0;\n        }\n    }\n    getVolume() {\n        const volume = this.target ? this.target.volume : 0;\n        this.ariaValue = volume ? 'unmuted' : 'muted';\n        return volume;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgMuteComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgMuteComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgMuteComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgMuteComponent, selector: \"vg-mute\", inputs: { vgFor: \"vgFor\" }, host: { listeners: { \"click\": \"onClick()\", \"keydown\": \"onKeyDown($event)\" } }, ngImport: i0, template: ` <div\n    class=\"icon\"\n    [class.vg-icon-volume_up]=\"getVolume() >= 0.75\"\n    [class.vg-icon-volume_down]=\"getVolume() >= 0.25 && getVolume() < 0.75\"\n    [class.vg-icon-volume_mute]=\"getVolume() > 0 && getVolume() < 0.25\"\n    [class.vg-icon-volume_off]=\"getVolume() === 0\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"mute button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`, isInline: true, styles: [\"vg-mute{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-mute .icon{pointer-events:none}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgMuteComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-mute', encapsulation: ViewEncapsulation.None, template: ` <div\n    class=\"icon\"\n    [class.vg-icon-volume_up]=\"getVolume() >= 0.75\"\n    [class.vg-icon-volume_down]=\"getVolume() >= 0.25 && getVolume() < 0.75\"\n    [class.vg-icon-volume_mute]=\"getVolume() > 0 && getVolume() < 0.25\"\n    [class.vg-icon-volume_off]=\"getVolume() === 0\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"mute button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`, styles: [\"vg-mute{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-mute .icon{pointer-events:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass VgFullscreenComponent {\n    constructor(ref, API, fsAPI) {\n        this.API = API;\n        this.fsAPI = fsAPI;\n        this.isFullscreen = false;\n        this.subscriptions = [];\n        this.ariaValue = 'normal mode';\n        this.elem = ref.nativeElement;\n        this.subscriptions.push(this.fsAPI.onChangeFullscreen.subscribe(this.onChangeFullscreen.bind(this)));\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    onChangeFullscreen(fsState) {\n        this.ariaValue = fsState ? 'fullscreen mode' : 'normal mode';\n        this.isFullscreen = fsState;\n    }\n    onClick() {\n        this.changeFullscreenState();\n    }\n    onKeyDown(event) {\n        // On press Enter (13) or Space (32)\n        if (event.keyCode === 13 || event.keyCode === 32) {\n            event.preventDefault();\n            this.changeFullscreenState();\n        }\n    }\n    changeFullscreenState() {\n        let element = this.target;\n        if (this.target instanceof VgApiService) {\n            element = null;\n        }\n        this.fsAPI.toggleFullscreen(element);\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgFullscreenComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgFullscreenComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }, { token: i1.VgFullscreenApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgFullscreenComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgFullscreenComponent, selector: \"vg-fullscreen\", host: { listeners: { \"click\": \"onClick()\", \"keydown\": \"onKeyDown($event)\" } }, ngImport: i0, template: ` <div\n    class=\"icon\"\n    [class.vg-icon-fullscreen]=\"!isFullscreen\"\n    [class.vg-icon-fullscreen_exit]=\"isFullscreen\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"fullscreen button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`, isInline: true, styles: [\"vg-fullscreen{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-fullscreen .icon{pointer-events:none}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgFullscreenComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-fullscreen', encapsulation: ViewEncapsulation.None, template: ` <div\n    class=\"icon\"\n    [class.vg-icon-fullscreen]=\"!isFullscreen\"\n    [class.vg-icon-fullscreen_exit]=\"isFullscreen\"\n    tabindex=\"0\"\n    role=\"button\"\n    aria-label=\"fullscreen button\"\n    [attr.aria-valuetext]=\"ariaValue\"\n  ></div>`, styles: [\"vg-fullscreen{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;display:flex;justify-content:center;height:50px;width:50px;cursor:pointer;color:#fff;line-height:50px}vg-fullscreen .icon{pointer-events:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }, { type: i1.VgFullscreenApiService }]; }, propDecorators: { onClick: [{\n                type: HostListener,\n                args: ['click']\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass VgScrubBarBufferingTimeComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    getBufferTime() {\n        let bufferTime = '0%';\n        if (this.target?.buffered?.length) {\n            if (this.target.time.total === 0) {\n                bufferTime = '0%';\n            }\n            else {\n                bufferTime =\n                    (this.target.buffer.end / this.target.time.total) * 100 + '%';\n            }\n        }\n        return bufferTime;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgScrubBarBufferingTimeComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarBufferingTimeComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgScrubBarBufferingTimeComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgScrubBarBufferingTimeComponent, selector: \"vg-scrub-bar-buffering-time\", inputs: { vgFor: \"vgFor\" }, ngImport: i0, template: `<div class=\"background\" [style.width]=\"getBufferTime()\"></div>`, isInline: true, styles: [\"vg-scrub-bar-buffering-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-buffering-time .background{background-color:#ffffff4d}vg-controls vg-scrub-bar-buffering-time{position:absolute;top:calc(50% - 3px)}vg-controls vg-scrub-bar-buffering-time .background{border-radius:2px}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarBufferingTimeComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-scrub-bar-buffering-time', encapsulation: ViewEncapsulation.None, template: `<div class=\"background\" [style.width]=\"getBufferTime()\"></div>`, styles: [\"vg-scrub-bar-buffering-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-buffering-time .background{background-color:#ffffff4d}vg-controls vg-scrub-bar-buffering-time{position:absolute;top:calc(50% - 3px)}vg-controls vg-scrub-bar-buffering-time .background{border-radius:2px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }] } });\n\n// tslint:disable-next-line: no-conflicting-lifecycle\nclass VgScrubBarCuePointsComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.onLoadedMetadataCalled = false;\n        this.cuePoints = [];\n        this.subscriptions = [];\n        this.totalCues = 0;\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n        const onTimeUpdate = this.target.subscriptions.loadedMetadata;\n        this.subscriptions.push(onTimeUpdate.subscribe(this.onLoadedMetadata.bind(this)));\n        if (this.onLoadedMetadataCalled) {\n            this.onLoadedMetadata();\n        }\n    }\n    onLoadedMetadata() {\n        if (this.vgCuePoints) {\n            // We need to transform the TextTrackCueList to Array or it doesn't work on IE11/Edge.\n            // See: https://github.com/videogular/videogular2/issues/369\n            this.cuePoints = [];\n            for (let i = 0, l = this.vgCuePoints.length; i < l; i++) {\n                const end = this.vgCuePoints[i].endTime >= 0\n                    ? this.vgCuePoints[i].endTime\n                    : this.vgCuePoints[i].startTime + 1;\n                const cuePointDuration = (end - this.vgCuePoints[i].startTime) * 1000;\n                let position = '0';\n                let percentWidth = '0';\n                if (typeof cuePointDuration === 'number' && this.target.time.total) {\n                    percentWidth =\n                        (cuePointDuration * 100) / this.target.time.total + '%';\n                    position =\n                        (this.vgCuePoints[i].startTime * 100) /\n                            Math.round(this.target.time.total / 1000) +\n                            '%';\n                }\n                this.vgCuePoints[i].$$style = {\n                    width: percentWidth,\n                    left: position,\n                };\n                this.cuePoints.push(this.vgCuePoints[i]);\n            }\n        }\n    }\n    updateCuePoints() {\n        if (!this.target) {\n            this.onLoadedMetadataCalled = true;\n            return;\n        }\n        this.onLoadedMetadata();\n    }\n    ngOnChanges(changes) {\n        if (changes.vgCuePoints.currentValue) {\n            this.updateCuePoints();\n        }\n    }\n    ngDoCheck() {\n        if (this.vgCuePoints) {\n            const changes = this.totalCues !== this.vgCuePoints.length;\n            if (changes) {\n                this.totalCues = this.vgCuePoints.length;\n                this.updateCuePoints();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgScrubBarCuePointsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarCuePointsComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgScrubBarCuePointsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgScrubBarCuePointsComponent, selector: \"vg-scrub-bar-cue-points\", inputs: { vgCuePoints: \"vgCuePoints\", vgFor: \"vgFor\" }, usesOnChanges: true, ngImport: i0, template: `\n    <div class=\"cue-point-container\">\n      <span\n        *ngFor=\"let cp of cuePoints\"\n        [style.width]=\"cp.$$style?.width\"\n        [style.left]=\"cp.$$style?.left\"\n        class=\"cue-point\"\n      ></span>\n    </div>\n  `, isInline: true, styles: [\"vg-scrub-bar-cue-points{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-cue-points .cue-point-container .cue-point{position:absolute;height:5px;background-color:#ffcc00b3}vg-controls vg-scrub-bar-cue-points{position:absolute;top:calc(50% - 3px)}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarCuePointsComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-scrub-bar-cue-points', encapsulation: ViewEncapsulation.None, template: `\n    <div class=\"cue-point-container\">\n      <span\n        *ngFor=\"let cp of cuePoints\"\n        [style.width]=\"cp.$$style?.width\"\n        [style.left]=\"cp.$$style?.left\"\n        class=\"cue-point\"\n      ></span>\n    </div>\n  `, styles: [\"vg-scrub-bar-cue-points{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-cue-points .cue-point-container .cue-point{position:absolute;height:5px;background-color:#ffcc00b3}vg-controls vg-scrub-bar-cue-points{position:absolute;top:calc(50% - 3px)}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgCuePoints: [{\n                type: Input\n            }], vgFor: [{\n                type: Input\n            }] } });\n\nclass VgScrubBarCurrentTimeComponent {\n    constructor(ref, API) {\n        this.API = API;\n        this.vgSlider = false;\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n    }\n    ngOnInit() {\n        if (this.API.isPlayerReady) {\n            this.onPlayerReady();\n        }\n        else {\n            this.subscriptions.push(this.API.playerReadyEvent.subscribe(() => this.onPlayerReady()));\n        }\n    }\n    onPlayerReady() {\n        this.target = this.API.getMediaById(this.vgFor);\n    }\n    getPercentage() {\n        return this.target\n            ? Math.round((this.target.time.current * 100) / this.target.time.total) +\n                '%'\n            : '0%';\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgScrubBarCurrentTimeComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarCurrentTimeComponent, deps: [{ token: i0.ElementRef }, { token: i1.VgApiService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgScrubBarCurrentTimeComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgScrubBarCurrentTimeComponent, selector: \"vg-scrub-bar-current-time\", inputs: { vgFor: \"vgFor\", vgSlider: \"vgSlider\" }, ngImport: i0, template: `<div class=\"background\" [style.width]=\"getPercentage()\"></div>\n    <span class=\"slider\" *ngIf=\"vgSlider\"></span>`, isInline: true, styles: [\"vg-scrub-bar-current-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-current-time .background{background-color:#fff}vg-controls vg-scrub-bar-current-time{position:absolute;top:calc(50% - 3px);border-radius:2px}vg-controls vg-scrub-bar-current-time .background{border:1px solid white;border-radius:2px}vg-scrub-bar-current-time .slider{background:white;height:15px;width:15px;border-radius:50%;box-shadow:0 0 10px #000;margin-top:-5px;margin-left:-10px}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgScrubBarCurrentTimeComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-scrub-bar-current-time', encapsulation: ViewEncapsulation.None, template: `<div class=\"background\" [style.width]=\"getPercentage()\"></div>\n    <span class=\"slider\" *ngIf=\"vgSlider\"></span>`, styles: [\"vg-scrub-bar-current-time{display:flex;width:100%;height:5px;pointer-events:none;position:absolute}vg-scrub-bar-current-time .background{background-color:#fff}vg-controls vg-scrub-bar-current-time{position:absolute;top:calc(50% - 3px);border-radius:2px}vg-controls vg-scrub-bar-current-time .background{border:1px solid white;border-radius:2px}vg-scrub-bar-current-time .slider{background:white;height:15px;width:15px;border-radius:50%;box-shadow:0 0 10px #000;margin-top:-5px;margin-left:-10px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.VgApiService }]; }, propDecorators: { vgFor: [{\n                type: Input\n            }], vgSlider: [{\n                type: Input\n            }] } });\n\nconst components = [\n    VgControlsComponent,\n    VgVolumeComponent,\n    VgTrackSelectorComponent,\n    VgTimeDisplayComponent,\n    VgScrubBarComponent,\n    VgQualitySelectorComponent,\n    VgPlaybackButtonComponent,\n    VgPlayPauseComponent,\n    VgMuteComponent,\n    VgFullscreenComponent,\n    VgUtcPipe,\n    VgScrubBarBufferingTimeComponent,\n    VgScrubBarCuePointsComponent,\n    VgScrubBarCurrentTimeComponent\n];\nclass VgControlsModule {\n}\n/** @nocollapse */ VgControlsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ VgControlsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsModule, declarations: [VgControlsComponent,\n        VgVolumeComponent,\n        VgTrackSelectorComponent,\n        VgTimeDisplayComponent,\n        VgScrubBarComponent,\n        VgQualitySelectorComponent,\n        VgPlaybackButtonComponent,\n        VgPlayPauseComponent,\n        VgMuteComponent,\n        VgFullscreenComponent,\n        VgUtcPipe,\n        VgScrubBarBufferingTimeComponent,\n        VgScrubBarCuePointsComponent,\n        VgScrubBarCurrentTimeComponent], imports: [CommonModule, VgCoreModule], exports: [VgControlsComponent,\n        VgVolumeComponent,\n        VgTrackSelectorComponent,\n        VgTimeDisplayComponent,\n        VgScrubBarComponent,\n        VgQualitySelectorComponent,\n        VgPlaybackButtonComponent,\n        VgPlayPauseComponent,\n        VgMuteComponent,\n        VgFullscreenComponent,\n        VgUtcPipe,\n        VgScrubBarBufferingTimeComponent,\n        VgScrubBarCuePointsComponent,\n        VgScrubBarCurrentTimeComponent] });\n/** @nocollapse */ VgControlsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsModule, imports: [CommonModule, VgCoreModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, VgCoreModule],\n                    declarations: [...components],\n                    exports: [...components],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgControlsComponent, VgControlsModule, VgFullscreenComponent, VgMuteComponent, VgPlayPauseComponent, VgPlaybackButtonComponent, VgQualitySelectorComponent, VgScrubBarBufferingTimeComponent, VgScrubBarComponent, VgScrubBarCuePointsComponent, VgScrubBarCurrentTimeComponent, VgTimeDisplayComponent, VgTrackSelectorComponent, VgUtcPipe, VgVolumeComponent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,IAAI,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC/I,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,MAAM;AAChC,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,QAAQ,iCAAiC;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAAC,QAAA,EAAAD;EAAA;AAAA;AAAA,SAAAE,2CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAqFkCxB,EAAE,CAAA0B,cAAA,eAyOnH,CAAC;IAzOgH1B,EAAE,CAAA2B,MAAA,EA2OpH,CAAC;IA3OiH3B,EAAE,CAAA4B,YAAA,CA2O3G,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,QAAA,GAAAJ,GAAA,CAAAK,SAAA;IA3OwG9B,EAAE,CAAA+B,UAAA,UAAAF,QAAA,CAAAG,EAuOhG,CAAC,aAAAH,QAAA,CAAAI,QAAA,SAAD,CAAC;IAvO6FjC,EAAE,CAAAkC,SAAA,EA2OpH,CAAC;IA3OiHlC,EAAE,CAAAmC,kBAAA,MAAAN,QAAA,CAAAO,KAAA,KA2OpH,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3OiHxB,EAAE,CAAA0B,cAAA,UA4U3F,CAAC;IA5UwF1B,EAAE,CAAA2B,MAAA,UA4UvF,CAAC;IA5UoF3B,EAAE,CAAA4B,YAAA,CA4UhF,CAAC;EAAA;AAAA;AAAA,SAAAU,uCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5U6ExB,EAAE,CAAA0B,cAAA,UA6U1F,CAAC;IA7UuF1B,EAAE,CAAA2B,MAAA,EA6UzD,CAAC;IA7UsD3B,EAAE,CAAAuC,MAAA;IAAFvC,EAAE,CAAA4B,YAAA,CA6UlD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,MAAA,GA7U+CxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAAkC,SAAA,EA6UzD,CAAC;IA7UsDlC,EAAE,CAAA0C,iBAAA,CAAF1C,EAAE,CAAA2C,WAAA,OAAAH,MAAA,CAAAI,OAAA,IAAAJ,MAAA,CAAAK,QAAA,CA6UzD,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7UsDxB,EAAE,CAAA0B,cAAA,eAgmBnH,CAAC;IAhmBgH1B,EAAE,CAAA2B,MAAA,EAkmBpH,CAAC;IAlmBiH3B,EAAE,CAAA4B,YAAA,CAkmB3G,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAuB,UAAA,GAAAtB,GAAA,CAAAK,SAAA;IAAA,MAAAkB,MAAA,GAlmBwGhD,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA+B,UAAA,UAAAgB,UAAA,CAAAE,YA8lBpF,CAAC,aAAAF,UAAA,CAAAE,YAAA,MAAAD,MAAA,CAAAE,eAAA,kBAAAF,MAAA,CAAAE,eAAA,CAAAD,YAAA,CAAD,CAAC;IA9lBiFjD,EAAE,CAAAkC,SAAA,EAkmBpH,CAAC;IAlmBiHlC,EAAE,CAAAmC,kBAAA,MAAAY,UAAA,CAAAX,KAAA,KAkmBpH,CAAC;EAAA;AAAA;AAAA,SAAAe,6CAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlmBiHxB,EAAE,CAAAoD,SAAA,aAylC9G,CAAC;EAAA;EAAA,IAAA5B,EAAA;IAAA,MAAA6B,KAAA,GAAA5B,GAAA,CAAAK,SAAA;IAzlC2G9B,EAAE,CAAAsD,WAAA,UAAAD,KAAA,CAAAE,OAAA,kBAAAF,KAAA,CAAAE,OAAA,CAAAC,KAslCnF,CAAC,SAAAH,KAAA,CAAAE,OAAA,kBAAAF,KAAA,CAAAE,OAAA,CAAAE,IAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtlCgFxB,EAAE,CAAAoD,SAAA,aA4oC3E,CAAC;EAAA;AAAA;AA/tCjD,MAAMO,mBAAmB,CAAC;EACtB;EACAC,WAAWA,CAACC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,YAAY,GAAG,SAAS;IAC7B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,UAAU,GAAG3D,SAAS,CAAC,IAAI,CAACgD,GAAG,CAACY,iBAAiB,EAAE,WAAW,CAAC;IACpE,IAAI,CAACL,aAAa,CAACM,IAAI,CAAC,IAAI,CAACF,UAAU,CAACG,SAAS,CAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,IAAI,CAACC,WAAW,GAAGjE,SAAS,CAAC,IAAI,CAACgD,GAAG,CAACY,iBAAiB,EAAE,YAAY,CAAC;IACtE,IAAI,CAACL,aAAa,CAACM,IAAI,CAAC,IAAI,CAACI,WAAW,CAACH,SAAS,CAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzE,IAAI,CAACE,WAAW,GAAGlE,SAAS,CAAC,IAAI,CAACgD,GAAG,CAACY,iBAAiB,EAAE,OAAO,CAAC;IACjE,IAAI,CAACL,aAAa,CAACM,IAAI,CAAC,IAAI,CAACK,WAAW,CAACJ,SAAS,CAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzE,IAAI,IAAI,CAAChB,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/C,IAAI,CAACjB,aAAa,CAACM,IAAI,CAAC,IAAI,CAACS,MAAM,CAACf,aAAa,CAACkB,IAAI,CAACX,SAAS,CAAC,IAAI,CAACY,MAAM,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzF,IAAI,CAACT,aAAa,CAACM,IAAI,CAAC,IAAI,CAACS,MAAM,CAACf,aAAa,CAACoB,KAAK,CAACb,SAAS,CAAC,IAAI,CAACc,OAAO,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3F,IAAI,CAACT,aAAa,CAACM,IAAI,CAAC,IAAI,CAACS,MAAM,CAACf,aAAa,CAACsB,QAAQ,CAACf,SAAS,CAAC,IAAI,CAACgB,UAAU,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjG,IAAI,CAACT,aAAa,CAACM,IAAI,CAAC,IAAI,CAACS,MAAM,CAACf,aAAa,CAACwB,MAAM,CAACjB,SAAS,CAAC,IAAI,CAACkB,QAAQ,CAAChB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACjG;EACAiB,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC5B,UAAU,EAAE;MACjB,IAAI,CAAC6B,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACnB,IAAI,CAAC,CAAC;IACf;EACJ;EACAW,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACrB,UAAU,EAAE;MACjB,IAAI,CAAC6B,IAAI,CAAC,CAAC;IACf;EACJ;EACAN,OAAOA,CAAA,EAAG;IACNO,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAChC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACF,MAAM,CAACmC,KAAK,CAAC,KAAK,CAAC;EAC5B;EACAP,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC3B,YAAY,GAAG,MAAM;EAC9B;EACA6B,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC7B,YAAY,GAAG,SAAS;EACjC;EACA+B,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC7B,UAAU,EAAE;MACjB8B,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;MACxB,IAAI,CAACE,SAAS,CAAC,CAAC;IACpB;EACJ;EACAvB,IAAIA,CAAA,EAAG;IACHoB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAChC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACF,MAAM,CAACmC,KAAK,CAAC,KAAK,CAAC;IACxB,IAAI,IAAI,CAAChC,UAAU,EAAE;MACjB,IAAI,CAACiC,SAAS,CAAC,CAAC;IACpB;EACJ;EACAA,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACtC,GAAG,CAACqC,KAAK,KAAKnF,QAAQ,CAACqF,UAAU,EAAE;MACxC,IAAI,CAACH,KAAK,GAAGI,UAAU,CAAC,MAAM;QAC1B,IAAI,CAACpC,YAAY,GAAG,IAAI;QACxB,IAAI,CAACF,MAAM,CAACmC,KAAK,CAAC,IAAI,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC/B,cAAc,GAAG,IAAI,CAAC;IAClC;EACJ;EACAmC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB9C,mBAAmB,CAAC+C,IAAI,YAAAC,4BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFjD,mBAAmB,EAA7B3D,EAAE,CAAA6G,iBAAA,CAA6C/F,EAAE,CAACE,YAAY,GAA9DhB,EAAE,CAAA6G,iBAAA,CAAyE7G,EAAE,CAAC8G,UAAU,GAAxF9G,EAAE,CAAA6G,iBAAA,CAAmG/F,EAAE,CAACiG,uBAAuB;AAAA,CAA4C;AACpS;AAAmBpD,mBAAmB,CAACqD,IAAI,kBAD8EhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EACJvD,mBAAmB;EAAAwD,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,iCAAA9F,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADjBxB,EAAE,CAAAsD,WAAA,mBAAA7B,GAAA,CAAAuC,YAAA;MAAFhE,EAAE,CAAAuH,WAAA,SAAA9F,GAAA,CAAAwC,YAAA;IAAA;EAAA;EAAAuD,MAAA;IAAAnC,KAAA;IAAAnB,UAAA;IAAAC,cAAA;EAAA;EAAAsD,kBAAA,EAAAvG,GAAA;EAAAwG,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,6BAAArG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxB,EAAE,CAAA8H,eAAA;MAAF9H,EAAE,CAAA+H,YAAA,EAC6R,CAAC;IAAA;EAAA;EAAAC,MAAA;EAAAC,aAAA;AAAA,EAAmP;AAC5oB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFyHlI,EAAE,CAAAmI,iBAAA,CAEhCxE,mBAAmB,EAAc,CAAC;IACjHuD,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG,2BAA0B;MAAEI,MAAM,EAAE,CAAC,yKAAyK;IAAE,CAAC;EACzS,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,EAAE;MAAEkG,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACiG;IAAwB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE/C,YAAY,EAAE,CAAC;MACjKkD,IAAI,EAAE/G,WAAW;MACjBiI,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAEnE,YAAY,EAAE,CAAC;MACfiD,IAAI,EAAE/G,WAAW;MACjBiI,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE/C,KAAK,EAAE,CAAC;MACR6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAE8D,UAAU,EAAE,CAAC;MACbgD,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAE+D,cAAc,EAAE,CAAC;MACjB+C,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmI,iBAAiB,CAAC;EACpB3E,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACO,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;IAC7B,IAAI,CAACkE,UAAU,GAAG,KAAK;EAC3B;EACAjE,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/C,IAAI,CAACoD,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,GAAG,GAAG;EAC3C;EACAC,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,eAAe,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC;EACvD;EACAC,WAAWA,CAACJ,KAAK,EAAE;IACf,IAAI,CAACK,aAAa,GAAGL,KAAK,CAACG,OAAO;IAClC,IAAI,CAACP,UAAU,GAAG,IAAI;EAC1B;EACAU,MAAMA,CAACN,KAAK,EAAE;IACV,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjB,IAAI,CAACK,SAAS,CAAC,IAAI,CAACC,eAAe,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC;IACvD;EACJ;EACAI,UAAUA,CAACP,KAAK,EAAE;IACd,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACS,aAAa,KAAKL,KAAK,CAACG,OAAO,EAAE;QACtC,IAAI,CAACF,SAAS,CAAC,IAAI,CAACC,eAAe,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC;MACvD;IACJ;EACJ;EACAK,iBAAiBA,CAACR,KAAK,EAAE;IACrB,IAAIA,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;MAC9CT,KAAK,CAACU,cAAc,CAAC,CAAC;MACtB,IAAI,CAACT,SAAS,CAACU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAE,IAAI,CAACf,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC,MACI,IAAIE,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;MACnDT,KAAK,CAACU,cAAc,CAAC,CAAC;MACtB,IAAI,CAACT,SAAS,CAACU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAE,IAAI,CAACf,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IAC3E;EACJ;EACAI,eAAeA,CAACY,SAAS,EAAE;IACvB,MAAMC,MAAM,GAAG,IAAI,CAACC,YAAY,CAACtF,aAAa,CAACuF,qBAAqB,CAAC,CAAC;IACtE,MAAMC,mBAAmB,GAAGH,MAAM,CAAClG,IAAI;IACvC,MAAMsG,cAAc,GAAGJ,MAAM,CAACnG,KAAK;IACnC,OAAQ,CAACkG,SAAS,GAAGI,mBAAmB,IAAIC,cAAc,GAAI,GAAG;EACrE;EACAlB,SAASA,CAACmB,GAAG,EAAE;IACX,IAAI,CAAC7E,MAAM,CAAC8E,MAAM,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEO,GAAG,GAAG,GAAG,CAAC,CAAC;IACxD,IAAI,CAACvB,SAAS,GAAG,IAAI,CAACtD,MAAM,CAAC8E,MAAM,GAAG,GAAG;EAC7C;EACAvB,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACvD,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC8E,MAAM,GAAG,CAAC;EAC/C;EACA3D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB8B,iBAAiB,CAAC7B,IAAI,YAAAwD,0BAAAtD,CAAA;EAAA,YAAAA,CAAA,IAAwF2B,iBAAiB,EArFzBvI,EAAE,CAAA6G,iBAAA,CAqFyC7G,EAAE,CAAC8G,UAAU,GArFxD9G,EAAE,CAAA6G,iBAAA,CAqFmE/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AACzP;AAAmBuH,iBAAiB,CAACvB,IAAI,kBAtFgFhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAsFNqB,iBAAiB;EAAApB,SAAA;EAAAgD,SAAA,WAAAC,wBAAA5I,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtFbxB,EAAE,CAAAqK,WAAA,CAAAlJ,GAAA;IAAA;IAAA,IAAAK,EAAA;MAAA,IAAA8I,EAAA;MAAFtK,EAAE,CAAAuK,cAAA,CAAAD,EAAA,GAAFtK,EAAE,CAAAwK,WAAA,QAAA/I,GAAA,CAAAmI,YAAA,GAAAU,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAApD,YAAA,WAAAqD,+BAAAlJ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxB,EAAE,CAAA2K,UAAA,uBAAAC,+CAAAC,MAAA;QAAA,OAsFNpJ,GAAA,CAAAyH,MAAA,CAAA2B,MAAa,CAAC;MAAA,UAtFV7K,EAAE,CAAA8K,iBAAA,sBAAAC,6CAAAF,MAAA;QAAA,OAsFNpJ,GAAA,CAAA0H,UAAA,CAAA0B,MAAiB,CAAC;MAAA,UAtFd7K,EAAE,CAAA8K,iBAAA,sBAAAE,6CAAAH,MAAA;QAAA,OAsFNpJ,GAAA,CAAA2H,iBAAA,CAAAyB,MAAwB,CAAC;MAAA;IAAA;EAAA;EAAArD,MAAA;IAAAnC,KAAA;EAAA;EAAAqC,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAsD,2BAAA1J,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtFrBxB,EAAE,CAAA0B,cAAA,eAqGvH,CAAC;MArGoH1B,EAAE,CAAA2K,UAAA,mBAAAQ,gDAAAN,MAAA;QAAA,OAmG5GpJ,GAAA,CAAAkH,OAAA,CAAAkC,MAAc,CAAC;MAAA,EAAC,uBAAAO,oDAAAP,MAAA;QAAA,OACZpJ,GAAA,CAAAuH,WAAA,CAAA6B,MAAkB,CAAC;MAAA,CADR,CAAC;MAnG0F7K,EAAE,CAAA0B,cAAA,YAsGnD,CAAC;MAtGgD1B,EAAE,CAAAoD,SAAA,YA0G7G,CAAC,YAAD,CAAC;MA1G0GpD,EAAE,CAAA4B,YAAA,CA+GhH,CAAC,CAAD,CAAC;IAAA;IAAA,IAAAJ,EAAA;MA/G6GxB,EAAE,CAAAqL,WAAA,kBAAA5J,GAAA,CAAAgH,SA8FtF,CAAC,mBAAAhH,GAAA,CAAAgH,SAAA,MAAD,CAAC;MA9FmFzI,EAAE,CAAAkC,SAAA,EAsGpD,CAAC;MAtGiDlC,EAAE,CAAA+B,UAAA,YAAF/B,EAAE,CAAAsL,eAAA,IAAAlK,GAAA,EAAAK,GAAA,CAAA+G,UAAA,CAsGpD,CAAC;MAtGiDxI,EAAE,CAAAkC,SAAA,EAyGpE,CAAC;MAzGiElC,EAAE,CAAAsD,WAAA,UAAA7B,GAAA,CAAAiH,SAAA,qBAyGpE,CAAC;MAzGiE1I,EAAE,CAAAkC,SAAA,EA6GrE,CAAC;MA7GkElC,EAAE,CAAAsD,WAAA,SAAA7B,GAAA,CAAAiH,SAAA,qBA6GrE,CAAC;IAAA;EAAA;EAAA6C,YAAA,GAI20B5K,EAAE,CAAC6K,OAAO;EAAAxD,MAAA;EAAAC,aAAA;AAAA,EAAqG;AACj/B;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlHyHlI,EAAE,CAAAmI,iBAAA,CAkHhCI,iBAAiB,EAAc,CAAC;IAC/GrB,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,uzBAAuzB;IAAE,CAAC;EACh0B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEwJ,YAAY,EAAE,CAAC;MACf1C,IAAI,EAAE7G,SAAS;MACf+H,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEqD,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEvC,MAAM,EAAE,CAAC;MACThC,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;IAAEe,UAAU,EAAE,CAAC;MACbjC,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC;IACzC,CAAC,CAAC;IAAEgB,iBAAiB,EAAE,CAAC;MACpBlC,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsD,wBAAwB,CAAC;EAC3B9H,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACO,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/C,MAAMsG,IAAI,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAChI,GAAG,CAACiI,cAAc,CAAC,CAAC,CAACzH,IAAI,CAAC0H,QAAQ,CAAC,CAC3DC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,OAAO,KAAK,OAAO,CAAC,CAC1CF,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACE,IAAI,KAAK,WAAW,CAAC,CAC3CC,GAAG,CAAEH,IAAI,KAAM;MAChB7J,KAAK,EAAE6J,IAAI,CAAC7J,KAAK;MACjBH,QAAQ,EAAEgK,IAAI,CAACI,OAAO,KAAK,IAAI;MAC/BrK,EAAE,EAAEiK,IAAI,CAACK;IACb,CAAC,CAAC,CAAC;IACH,IAAI,CAACC,MAAM,GAAG,CACV,GAAGZ,IAAI,EACP;MACI3J,EAAE,EAAE,IAAI;MACRI,KAAK,EAAE,KAAK;MACZH,QAAQ,EAAE0J,IAAI,CAACa,KAAK,CAAEP,IAAI,IAAKA,IAAI,CAAChK,QAAQ,KAAK,KAAK;IAC1D,CAAC,CACJ;IACD,MAAMwK,KAAK,GAAG,IAAI,CAACF,MAAM,CAACP,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAChK,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,CAACyK,aAAa,GAAGD,KAAK,CAACzK,EAAE;IAC7B,IAAI,CAACyG,SAAS,GAAGgE,KAAK,CAACrK,KAAK;EAChC;EACAuK,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACF,aAAa,GAAGE,OAAO,KAAK,MAAM,GAAG,IAAI,GAAGA,OAAO;IACxD,IAAI,CAACnE,SAAS,GAAG,mBAAmB;IACpCmD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAChI,GAAG,CAACiI,cAAc,CAAC,CAAC,CAACzH,IAAI,CAACwI,UAAU,CAAC,CAACtG,OAAO,CAAE0F,IAAI,IAAK;MACpE,IAAIA,IAAI,CAACa,QAAQ,KAAKF,OAAO,EAAE;QAC3B,IAAI,CAACnE,SAAS,GAAGwD,IAAI,CAAC7J,KAAK;QAC3B6J,IAAI,CAACc,IAAI,GAAG,SAAS;MACzB,CAAC,MACI;QACDd,IAAI,CAACc,IAAI,GAAG,QAAQ;MACxB;IACJ,CAAC,CAAC;EACN;EACAzG,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBiF,wBAAwB,CAAChF,IAAI,YAAAsG,iCAAApG,CAAA;EAAA,YAAAA,CAAA,IAAwF8E,wBAAwB,EArNvC1L,EAAE,CAAA6G,iBAAA,CAqNuD7G,EAAE,CAAC8G,UAAU,GArNtE9G,EAAE,CAAA6G,iBAAA,CAqNiF/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AACvQ;AAAmB0K,wBAAwB,CAAC1E,IAAI,kBAtNyEhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAsNCwE,wBAAwB;EAAAvE,SAAA;EAAAK,MAAA;IAAAnC,KAAA;EAAA;EAAAqC,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAqF,kCAAAzL,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAtN3BxB,EAAE,CAAA0B,cAAA,YAuNjG,CAAC,YAAD,CAAC;MAvN8F1B,EAAE,CAAA2B,MAAA,EA6NtH,CAAC;MA7NmH3B,EAAE,CAAA4B,YAAA,CA6NhH,CAAC;MA7N6G5B,EAAE,CAAA0B,cAAA,eAoOrH,CAAC;MApOkH1B,EAAE,CAAA2K,UAAA,oBAAAuC,2DAAArC,MAAA;QAAA,OAgOzGpJ,GAAA,CAAAkL,WAAA,CAAA9B,MAAA,CAAA1F,MAAA,CAAAgI,KAA+B,CAAC;MAAA,EAAC;MAhOsEnN,EAAE,CAAAoN,UAAA,IAAA7L,0CAAA,mBA2O3G,CAAC;MA3OwGvB,EAAE,CAAA4B,YAAA,CA4O7G,CAAC,CAAD,CAAC;IAAA;IAAA,IAAAJ,EAAA;MA5O0GxB,EAAE,CAAAkC,SAAA,EA0NrE,CAAC;MA1NkElC,EAAE,CAAAuH,WAAA,4BAAA9F,GAAA,CAAAiL,aA0NrE,CAAC;MA1NkE1M,EAAE,CAAAkC,SAAA,EA6NtH,CAAC;MA7NmHlC,EAAE,CAAAmC,kBAAA,MAAAV,GAAA,CAAAiL,aAAA,WA6NtH,CAAC;MA7NmH1M,EAAE,CAAAkC,SAAA,EAmOnF,CAAC;MAnOgFlC,EAAE,CAAAqL,WAAA,mBAAA5J,GAAA,CAAAgH,SAmOnF,CAAC;MAnOgFzI,EAAE,CAAAkC,SAAA,EAsOvF,CAAC;MAtOoFlC,EAAE,CAAA+B,UAAA,YAAAN,GAAA,CAAA8K,MAsOvF,CAAC;IAAA;EAAA;EAAAhB,YAAA,GAQu7B5K,EAAE,CAAC0M,OAAO;EAAArF,MAAA;EAAAC,aAAA;AAAA,EAAoI;AAC1mC;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/OyHlI,EAAE,CAAAmI,iBAAA,CA+OhCuD,wBAAwB,EAAc,CAAC;IACtHxE,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,i5BAAi5B;IAAE,CAAC;EAC15B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMkN,SAAS,CAAC;EACZC,SAASA,CAACJ,KAAK,EAAEK,MAAM,EAAE;IACrB,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACP,KAAK,CAAC;IAC1B,IAAIQ,MAAM,GAAGH,MAAM;IACnB,IAAII,EAAE,GAAGH,IAAI,CAACI,aAAa,CAAC,CAAC;IAC7B,IAAIC,EAAE,GAAGL,IAAI,CAACM,aAAa,CAAC,CAAC;IAC7B,IAAIC,EAAE,GAAGP,IAAI,CAACQ,WAAW,CAAC,CAAC;IAC3B,IAAIL,EAAE,GAAG,EAAE,EAAE;MACTA,EAAE,GAAG,GAAG,GAAGA,EAAE;IACjB;IACA,IAAIE,EAAE,GAAG,EAAE,EAAE;MACTA,EAAE,GAAG,GAAG,GAAGA,EAAE;IACjB;IACA,IAAIE,EAAE,GAAG,EAAE,EAAE;MACTA,EAAE,GAAG,GAAG,GAAGA,EAAE;IACjB;IACAL,MAAM,GAAGA,MAAM,CAACO,OAAO,CAAC,KAAK,EAAEN,EAAE,CAAC;IAClCD,MAAM,GAAGA,MAAM,CAACO,OAAO,CAAC,KAAK,EAAEJ,EAAE,CAAC;IAClCH,MAAM,GAAGA,MAAM,CAACO,OAAO,CAAC,KAAK,EAAEF,EAAE,CAAC;IAClC,OAAOL,MAAM;EACjB;AACJ;AACA;AAAmBL,SAAS,CAAC5G,IAAI,YAAAyH,kBAAAvH,CAAA;EAAA,YAAAA,CAAA,IAAwF0G,SAAS;AAAA,CAA8C;AAChL;AAAmBA,SAAS,CAACc,KAAK,kBAtSuFpO,EAAE,CAAAqO,YAAA;EAAAC,IAAA;EAAApH,IAAA,EAsSJoG,SAAS;EAAAiB,IAAA;AAAA,EAAkB;AAClJ;EAAA,QAAArG,SAAA,oBAAAA,SAAA,KAvSyHlI,EAAE,CAAAmI,iBAAA,CAuShCmF,SAAS,EAAc,CAAC;IACvGpG,IAAI,EAAE3G,IAAI;IACV6H,IAAI,EAAE,CAAC;MAAEkG,IAAI,EAAE;IAAQ,CAAC;EAC5B,CAAC,CAAC;AAAA;AACV,MAAME,sBAAsB,CAAC;EACzB5K,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC4K,UAAU,GAAG,SAAS;IAC3B,IAAI,CAAC5L,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACuB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACAzC,OAAOA,CAAA,EAAG;IACN,IAAIgE,CAAC,GAAG,CAAC;IACT,IAAI,IAAI,CAACzB,MAAM,EAAE;MACbyB,CAAC,GAAG2C,IAAI,CAACmF,KAAK,CAAC,IAAI,CAACvJ,MAAM,CAACwJ,IAAI,CAAC,IAAI,CAACF,UAAU,CAAC,CAAC;MACjD7H,CAAC,GAAGgI,KAAK,CAAChI,CAAC,CAAC,IAAI,IAAI,CAACzB,MAAM,CAAC0J,MAAM,GAAG,CAAC,GAAGjI,CAAC;IAC9C;IACA,OAAOA,CAAC;EACZ;EACAN,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB+H,sBAAsB,CAAC9H,IAAI,YAAAoI,+BAAAlI,CAAA;EAAA,YAAAA,CAAA,IAAwF4H,sBAAsB,EA1UnCxO,EAAE,CAAA6G,iBAAA,CA0UmD7G,EAAE,CAAC8G,UAAU,GA1UlE9G,EAAE,CAAA6G,iBAAA,CA0U6E/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AACnQ;AAAmBwN,sBAAsB,CAACxH,IAAI,kBA3U2EhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EA2UDsH,sBAAsB;EAAArH,SAAA;EAAAK,MAAA;IAAAnC,KAAA;IAAAoJ,UAAA;IAAA5L,QAAA;EAAA;EAAA4E,kBAAA,EAAAvG,GAAA;EAAAwG,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAmH,gCAAAvN,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3UvBxB,EAAE,CAAA8H,eAAA;MAAF9H,EAAE,CAAAoN,UAAA,IAAA/K,sCAAA,iBA4UhF,CAAC;MA5U6ErC,EAAE,CAAAoN,UAAA,IAAA9K,sCAAA,iBA6UlD,CAAC;MA7U+CtC,EAAE,CAAA+H,YAAA,EA8U/F,CAAC;IAAA;IAAA,IAAAvG,EAAA;MA9U4FxB,EAAE,CAAA+B,UAAA,SAAAN,GAAA,CAAA0D,MAAA,kBAAA1D,GAAA,CAAA0D,MAAA,CAAA0J,MA4U7F,CAAC;MA5U0F7O,EAAE,CAAAkC,SAAA,EA6U5F,CAAC;MA7UyFlC,EAAE,CAAA+B,UAAA,WAAAN,GAAA,CAAA0D,MAAA,kBAAA1D,GAAA,CAAA0D,MAAA,CAAA0J,MAAA,CA6U5F,CAAC;IAAA;EAAA;EAAAtD,YAAA,GAE0U5K,EAAE,CAACqO,IAAI,EAAwF1B,SAAS;EAAAtF,MAAA;EAAAC,aAAA;AAAA,EAA+D;AACjhB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhVyHlI,EAAE,CAAAmI,iBAAA,CAgVhCqG,sBAAsB,EAAc,CAAC;IACpHtH,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AACpG;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,+RAA+R;IAAE,CAAC;EACxS,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEqO,UAAU,EAAE,CAAC;MACbvH,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEyC,QAAQ,EAAE,CAAC;MACXqE,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6O,mBAAmB,CAAC;EACtBrL,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAEqL,qBAAqB,EAAE;IACzC,IAAI,CAACrL,GAAG,GAAGA,GAAG;IACd,IAAI,CAACsL,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAAClL,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;IAC7B,IAAI,CAACF,aAAa,CAACM,IAAI,CAACwK,qBAAqB,CAACK,QAAQ,CAAC5K,SAAS,CAAEoB,IAAI,IAAK,IAAI,CAACyJ,cAAc,CAACzJ,IAAI,CAAC,CAAC,CAAC;EAC1G;EACAxB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACAoK,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACtK,MAAM,CAACuK,OAAO,EAAE;MACrB,IAAI,CAACL,SAAS,GAAG,IAAI;MACrB,IAAI,IAAI,CAAClK,MAAM,CAACe,KAAK,KAAKnF,QAAQ,CAACqF,UAAU,EAAE;QAC3C,IAAI,CAACkJ,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI,CAACnK,MAAM,CAACK,KAAK,CAAC,CAAC;IACvB;EACJ;EACAmK,QAAQA,CAACC,MAAM,EAAE;IACb,IAAI,IAAI,CAACP,SAAS,EAAE;MAChB,MAAMQ,UAAU,GAAGtG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAEmG,MAAM,GAAG,GAAG,GAAI,IAAI,CAACvL,IAAI,CAACyL,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;MACtF,IAAI,CAAC3K,MAAM,CAACwJ,IAAI,CAACoB,OAAO,GAAIF,UAAU,GAAG,IAAI,CAAC1K,MAAM,CAACwJ,IAAI,CAACqB,KAAK,GAAI,GAAG;MACtE,IAAI,CAAC7K,MAAM,CAAC8K,QAAQ,CAACJ,UAAU,EAAE,IAAI,CAAC;IAC1C;EACJ;EACAK,OAAOA,CAACN,MAAM,EAAE;IACZ,IAAI,CAACP,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAAClK,MAAM,CAACuK,OAAO,EAAE;MACrB,IAAIE,MAAM,KAAK,KAAK,EAAE;QAClB,MAAMC,UAAU,GAAGtG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAEmG,MAAM,GAAG,GAAG,GAAI,IAAI,CAACvL,IAAI,CAACyL,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACtF,IAAI,CAAC3K,MAAM,CAAC8K,QAAQ,CAACJ,UAAU,EAAE,IAAI,CAAC;MAC1C;MACA,IAAI,IAAI,CAACP,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB,IAAI,CAACnK,MAAM,CAACG,IAAI,CAAC,CAAC;MACtB;IACJ;EACJ;EACA6K,QAAQA,CAAA,EAAG;IACP,IAAI,CAACd,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACnK,MAAM,CAACG,IAAI,CAAC,CAAC;IACtB;EACJ;EACA8K,cAAcA,CAACxH,KAAK,EAAE;IAClB,IAAIyH,UAAU,GAAG,CAAC;IAClB,IAAIC,OAAO,GAAG1H,KAAK,CAACzD,MAAM;IAC1B,OAAOmL,OAAO,EAAE;MACZD,UAAU,IAAIC,OAAO,CAACD,UAAU;MAChCC,OAAO,GAAGA,OAAO,CAACC,YAAY;IAClC;IACA,OAAO3H,KAAK,CAAC4H,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGJ,UAAU;EAC9C;EACAK,mBAAmBA,CAAC7F,MAAM,EAAE;IACxB,IAAI,IAAI,CAAC1F,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,EAAE;QACrB,IAAI,CAAC,IAAI,CAACO,QAAQ,EAAE;UAChB,IAAI,CAACc,OAAO,CAACrF,MAAM,CAAC8F,OAAO,CAAC;QAChC,CAAC,MACI;UACD,IAAI,CAAClB,SAAS,CAAC,CAAC;QACpB;MACJ;IACJ;EACJ;EACAmB,mBAAmBA,CAAC/F,MAAM,EAAE;IACxB,IAAI,IAAI,CAAC1F,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACxD,IAAI,CAACM,QAAQ,CAAC9E,MAAM,CAAC8F,OAAO,CAAC;MACjC;IACJ;EACJ;EACAE,iBAAiBA,CAAChG,MAAM,EAAE;IACtB,IAAI,IAAI,CAAC1F,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACxD,IAAI,CAACa,OAAO,CAACrF,MAAM,CAAC8F,OAAO,CAAC;MAChC;IACJ;EACJ;EACAG,oBAAoBA,CAACC,OAAO,EAAE;IAC1B,IAAI,IAAI,CAAC5L,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,EAAE;QACrB,IAAI,CAAC,IAAI,CAACO,QAAQ,EAAE;UAChB,IAAI,CAACc,OAAO,CAAC,KAAK,CAAC;QACvB,CAAC,MACI;UACD,IAAI,CAACT,SAAS,CAAC,CAAC;QACpB;MACJ;IACJ;EACJ;EACAuB,mBAAmBA,CAACnG,MAAM,EAAE;IACxB,IAAI,IAAI,CAAC1F,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACxD,IAAI,CAACM,QAAQ,CAAC,IAAI,CAACS,cAAc,CAACvF,MAAM,CAAC,CAAC;MAC9C;IACJ;EACJ;EACA;EACAoG,qBAAqBA,CAACF,OAAO,EAAE;IAC3B,IAAI,IAAI,CAAC5L,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACxD,IAAI,CAACc,QAAQ,CAAC,CAAC;MACnB;IACJ;EACJ;EACA;EACAe,kBAAkBA,CAACH,OAAO,EAAE;IACxB,IAAI,IAAI,CAAC5L,MAAM,EAAE;MACb,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC0J,MAAM,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACxD,IAAI,CAACc,QAAQ,CAAC,CAAC;MACnB;IACJ;EACJ;EACA/G,iBAAiBA,CAACR,KAAK,EAAE;IACrB,IAAI,IAAI,CAACzD,MAAM,EAAE;MACb,IAAIyD,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;QAC9CT,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB,IAAI,CAACnE,MAAM,CAAC8K,QAAQ,CAAC,CAAC,IAAI,CAAC9K,MAAM,CAACwJ,IAAI,CAACoB,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,KAAK,CAAC;MACzE,CAAC,MACI,IAAInH,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;QACnDT,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB,IAAI,CAACnE,MAAM,CAAC8K,QAAQ,CAAC,CAAC,IAAI,CAAC9K,MAAM,CAACwJ,IAAI,CAACoB,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,KAAK,CAAC;MACzE;IACJ;EACJ;EACAoB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChM,MAAM,GACZoE,IAAI,CAACmF,KAAK,CAAE,IAAI,CAACvJ,MAAM,CAACwJ,IAAI,CAACoB,OAAO,GAAG,GAAG,GAAI,IAAI,CAAC5K,MAAM,CAACwJ,IAAI,CAACqB,KAAK,CAAC,GAAG,GAAG,GAC3E,IAAI;EACd;EACAR,cAAcA,CAACzJ,IAAI,EAAE;IACjB,IAAI,CAACoJ,YAAY,GAAGpJ,IAAI;EAC5B;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBwI,mBAAmB,CAACvI,IAAI,YAAA0K,4BAAAxK,CAAA;EAAA,YAAAA,CAAA,IAAwFqI,mBAAmB,EAvf7BjP,EAAE,CAAA6G,iBAAA,CAuf6C7G,EAAE,CAAC8G,UAAU,GAvf5D9G,EAAE,CAAA6G,iBAAA,CAufuE/F,EAAE,CAACE,YAAY,GAvfxFhB,EAAE,CAAA6G,iBAAA,CAufmG/F,EAAE,CAACiG,uBAAuB;AAAA,CAA4C;AACpS;AAAmBkI,mBAAmB,CAACjI,IAAI,kBAxf8EhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAwfJ+H,mBAAmB;EAAA9H,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAgK,iCAAA7P,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAxfjBxB,EAAE,CAAA2K,UAAA,uBAAA2G,iDAAAzG,MAAA;QAAA,OAwfJpJ,GAAA,CAAAiP,mBAAA,CAAA7F,MAA0B,CAAC;MAAA,yBAAA0G,iDAAA1G,MAAA;QAAA,OAA3BpJ,GAAA,CAAAmP,mBAAA,CAAA/F,MAA0B,CAAC;MAAA,UAxfzB7K,EAAE,CAAA8K,iBAAA,sBAAA0G,+CAAA3G,MAAA;QAAA,OAwfJpJ,GAAA,CAAAoP,iBAAA,CAAAhG,MAAwB,CAAC;MAAA,UAxfvB7K,EAAE,CAAA8K,iBAAA,yBAAA2G,kDAAA5G,MAAA;QAAA,OAwfJpJ,GAAA,CAAAqP,oBAAA,CAAAjG,MAA2B,CAAC;MAAA,yBAAA6G,iDAAA7G,MAAA;QAAA,OAA5BpJ,GAAA,CAAAuP,mBAAA,CAAAnG,MAA0B,CAAC;MAAA,UAxfzB7K,EAAE,CAAA8K,iBAAA,0BAAA6G,mDAAA9G,MAAA;QAAA,OAwfJpJ,GAAA,CAAAwP,qBAAA,CAAApG,MAA4B,CAAC;MAAA,UAxf3B7K,EAAE,CAAA8K,iBAAA,uBAAA8G,gDAAA/G,MAAA;QAAA,OAwfJpJ,GAAA,CAAAyP,kBAAA,CAAArG,MAAyB,CAAC;MAAA,UAxfxB7K,EAAE,CAAA8K,iBAAA,sBAAA+G,+CAAAhH,MAAA;QAAA,OAwfJpJ,GAAA,CAAA2H,iBAAA,CAAAyB,MAAwB,CAAC;MAAA;IAAA;IAAA,IAAArJ,EAAA;MAxfvBxB,EAAE,CAAAuH,WAAA,SAAA9F,GAAA,CAAA0N,YAAA;IAAA;EAAA;EAAA3H,MAAA;IAAAnC,KAAA;IAAA+J,QAAA;EAAA;EAAA3H,kBAAA,EAAAvG,GAAA;EAAAwG,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAkK,6BAAAtQ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxB,EAAE,CAAA8H,eAAA;MAAF9H,EAAE,CAAA0B,cAAA,YAmgBvH,CAAC;MAngBoH1B,EAAE,CAAA+H,YAAA,EAogB7F,CAAC;MApgB0F/H,EAAE,CAAA4B,YAAA,CAqgBlH,CAAC;IAAA;IAAA,IAAAJ,EAAA;MArgB+GxB,EAAE,CAAAqL,WAAA,kBAAA5J,GAAA,CAAA0P,aAAA,EA+fhF,CAAC,mBAAA1P,GAAA,CAAA0P,aAAA,EAAD,CAAC;IAAA;EAAA;EAAAnJ,MAAA;EAAAC,aAAA;AAAA,EAOuoB;AACnrB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvgByHlI,EAAE,CAAAmI,iBAAA,CAugBhC8G,mBAAmB,EAAc,CAAC;IACjH/H,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,umBAAumB;IAAE,CAAC;EAChnB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,EAAE;MAAEkG,IAAI,EAAEpG,EAAE,CAACiG;IAAwB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEoI,YAAY,EAAE,CAAC;MACjKjI,IAAI,EAAE/G,WAAW;MACjBiI,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE/C,KAAK,EAAE,CAAC;MACR6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEgP,QAAQ,EAAE,CAAC;MACXlI,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEsQ,mBAAmB,EAAE,CAAC;MACtBxJ,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAClC,CAAC,CAAC;IAAEwI,mBAAmB,EAAE,CAAC;MACtB1J,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;IAAEyI,iBAAiB,EAAE,CAAC;MACpB3J,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC;IACzC,CAAC,CAAC;IAAE0I,oBAAoB,EAAE,CAAC;MACvB5J,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;IACnC,CAAC,CAAC;IAAE4I,mBAAmB,EAAE,CAAC;MACtB9J,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;IAAE6I,qBAAqB,EAAE,CAAC;MACxB/J,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC;IAC7C,CAAC,CAAC;IAAE8I,kBAAkB,EAAE,CAAC;MACrBhK,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC,CAAC;IAAEgB,iBAAiB,EAAE,CAAC;MACpBlC,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2J,0BAA0B,CAAC;EAC7BnO,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACmO,eAAe,GAAG,IAAIxR,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC4D,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG,CAAE;EACb0N,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,QAAQ,CAACC,YAAY,IAAIF,OAAO,CAACC,QAAQ,CAACC,YAAY,CAACC,MAAM,EAAE;MACvE,IAAI,CAACF,QAAQ,CAAC5L,OAAO,CAAE0F,IAAI,IAAMA,IAAI,CAAC7J,KAAK,GACvC6J,IAAI,CAAC7J,KAAK,IAAImH,IAAI,CAACmF,KAAK,CAACzC,IAAI,CAACqG,OAAO,GAAG,IAAI,CAAC,CAACC,QAAQ,CAAC,CAAE,CAAC;IAClE;EACJ;EACAC,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,CAACvP,eAAe,GAAG,IAAI,CAACiP,QAAQ,CAACM,KAAK,CAAC;IAC3C,IAAI,CAACT,eAAe,CAACU,IAAI,CAAC,IAAI,CAACP,QAAQ,CAACM,KAAK,CAAC,CAAC;EACnD;EACAnM,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBsL,0BAA0B,CAACrL,IAAI,YAAAiM,mCAAA/L,CAAA;EAAA,YAAAA,CAAA,IAAwFmL,0BAA0B,EA/kB3C/R,EAAE,CAAA6G,iBAAA,CA+kB2D7G,EAAE,CAAC8G,UAAU,GA/kB1E9G,EAAE,CAAA6G,iBAAA,CA+kBqF/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AAC3Q;AAAmB+Q,0BAA0B,CAAC/K,IAAI,kBAhlBuEhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAglBG6K,0BAA0B;EAAA5K,SAAA;EAAAK,MAAA;IAAA2K,QAAA;EAAA;EAAAS,OAAA;IAAAZ,eAAA;EAAA;EAAAa,QAAA,GAhlB/B7S,EAAE,CAAA8S,oBAAA;EAAApL,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAmL,oCAAAvR,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxB,EAAE,CAAA0B,cAAA,YAilBjG,CAAC,YAAD,CAAC;MAjlB8F1B,EAAE,CAAA2B,MAAA,EAolBtH,CAAC;MAplBmH3B,EAAE,CAAA4B,YAAA,CAolBhH,CAAC;MAplB6G5B,EAAE,CAAA0B,cAAA,eA2lBrH,CAAC;MA3lBkH1B,EAAE,CAAA2K,UAAA,oBAAAqI,6DAAAnI,MAAA;QAAA,OAulBzGpJ,GAAA,CAAA+Q,aAAA,CAAA3H,MAAA,CAAA1F,MAAA,CAAAgI,KAAiC,CAAC;MAAA,EAAC;MAvlBoEnN,EAAE,CAAAoN,UAAA,IAAAtK,4CAAA,mBAkmB3G,CAAC;MAlmBwG9C,EAAE,CAAA4B,YAAA,CAmmB7G,CAAC,CAAD,CAAC;IAAA;IAAA,IAAAJ,EAAA;MAnmB0GxB,EAAE,CAAAkC,SAAA,EAklBnD,CAAC;MAllBgDlC,EAAE,CAAAuH,WAAA,gBAAA9F,GAAA,CAAAyB,eAklBnD,CAAC;MAllBgDlD,EAAE,CAAAkC,SAAA,EAolBtH,CAAC;MAplBmHlC,EAAE,CAAAmC,kBAAA,MAAAV,GAAA,CAAAyB,eAAA,kBAAAzB,GAAA,CAAAyB,eAAA,CAAAd,KAAA,KAolBtH,CAAC;MAplBmHpC,EAAE,CAAAkC,SAAA,EA0lBnF,CAAC;MA1lBgFlC,EAAE,CAAAqL,WAAA,mBAAA5J,GAAA,CAAAgH,SA0lBnF,CAAC;MA1lBgFzI,EAAE,CAAAkC,SAAA,EA6lBnF,CAAC;MA7lBgFlC,EAAE,CAAA+B,UAAA,YAAAN,GAAA,CAAA0Q,QA6lBnF,CAAC;IAAA;EAAA;EAAA5G,YAAA,GAQ48B5K,EAAE,CAAC0M,OAAO;EAAArF,MAAA;EAAAC,aAAA;AAAA,EAAoI;AACnoC;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtmByHlI,EAAE,CAAAmI,iBAAA,CAsmBhC4J,0BAA0B,EAAc,CAAC;IACxH7K,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,06BAA06B;IAAE,CAAC;EACn7B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmR,QAAQ,EAAE,CAAC;MACvHjL,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAE4R,eAAe,EAAE,CAAC;MAClB9K,IAAI,EAAEzG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwS,yBAAyB,CAAC;EAC5BrP,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAEqP,GAAG,EAAE;IACvB,IAAI,CAACrP,GAAG,GAAGA,GAAG;IACd,IAAI,CAACqP,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC9O,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqE,SAAS,GAAG,CAAC;IAClB,IAAI,CAACpE,IAAI,GAAGP,GAAG,CAACQ,aAAa;IAC7B,IAAI,CAAC6O,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClD,IAAI,CAACC,aAAa,GAAG,CAAC;EAC1B;EACA7O,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACAsD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0K,mBAAmB,CAAC,CAAC;EAC9B;EACAC,SAASA,CAAC1K,KAAK,EAAE;IACb;IACA,IAAIA,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;MAC9CT,KAAK,CAACU,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC+J,mBAAmB,CAAC,CAAC;IAC9B;EACJ;EACAA,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACF,cAAc,CAAC5M,OAAO,CAAC,CAACgN,aAAa,EAAEd,KAAK,KAAK;MAClD,IAAIc,aAAa,CAAClB,MAAM,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACc,cAAc,CAACV,KAAK,CAAC,GAAGc,aAAa,GAAG,IAAI;MACrD;IACJ,CAAC,CAAC;IACF,IAAI,CAACH,aAAa,GAAG,EAAE,IAAI,CAACA,aAAa,GAAG,IAAI,CAACD,cAAc,CAACd,MAAM;IACtE,IAAI,IAAI,CAAClN,MAAM,YAAYnE,YAAY,EAAE;MACrC,IAAI,CAACmE,MAAM,CAACqO,YAAY,GAAG,IAAI,CAACL,cAAc,CAAC,IAAI,CAACC,aAAa,CAAC;IACtE,CAAC,MACI;MACD,IAAI,CAACjO,MAAM,CAACqO,YAAY,CAAC,IAAI,CAACnO,KAAK,CAAC,GAAG,IAAI,CAAC8N,cAAc,CAAC,IAAI,CAACC,aAAa,CAAC;IAClF;IACA,IAAI,CAACK,aAAa,CAAC,CAAC;EACxB;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACjL,SAAS,GAAG,IAAI,CAACtD,MAAM,GAAG,IAAI,CAACA,MAAM,CAACqO,YAAY,GAAG,GAAG;IAC7D,OAAO,IAAI,CAAC/K,SAAS;EACzB;EACAgL,aAAaA,CAAA,EAAG;IACZ,IAAI;MACA,IAAI,CAACP,GAAG,CAACO,aAAa,CAAC,CAAC;IAC5B,CAAC,CACD,OAAOE,CAAC,EAAE;MACNC,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;IACnB;EACJ;EACArN,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBwM,yBAAyB,CAACvM,IAAI,YAAAoN,kCAAAlN,CAAA;EAAA,YAAAA,CAAA,IAAwFqM,yBAAyB,EAlsBzCjT,EAAE,CAAA6G,iBAAA,CAksByD7G,EAAE,CAAC8G,UAAU,GAlsBxE9G,EAAE,CAAA6G,iBAAA,CAksBmF/F,EAAE,CAACE,YAAY,GAlsBpGhB,EAAE,CAAA6G,iBAAA,CAksB+G7G,EAAE,CAAC+T,iBAAiB;AAAA,CAA4C;AAC1S;AAAmBd,yBAAyB,CAACjM,IAAI,kBAnsBwEhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAmsBE+L,yBAAyB;EAAA9L,SAAA;EAAAE,YAAA,WAAA2M,uCAAAxS,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnsB7BxB,EAAE,CAAA2K,UAAA,mBAAAsJ,mDAAA;QAAA,OAmsBExS,GAAA,CAAAkH,OAAA,CAAQ,CAAC;MAAA,uBAAAuL,qDAAArJ,MAAA;QAAA,OAATpJ,GAAA,CAAA6R,SAAA,CAAAzI,MAAgB,CAAC;MAAA;IAAA;EAAA;EAAArD,MAAA;IAAAnC,KAAA;IAAA8N,cAAA;EAAA;EAAAzL,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAuM,mCAAA3S,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnsBrBxB,EAAE,CAAA0B,cAAA,aAysBzH,CAAC;MAzsBsH1B,EAAE,CAAA2B,MAAA,EA2sB1H,CAAC;MA3sBuH3B,EAAE,CAAA4B,YAAA,CA2sBnH,CAAC;IAAA;IAAA,IAAAJ,EAAA;MA3sBgHxB,EAAE,CAAAqL,WAAA,mBAAA5J,GAAA,CAAAgH,SAwsBvF,CAAC;MAxsBoFzI,EAAE,CAAAkC,SAAA,EA2sB1H,CAAC;MA3sBuHlC,EAAE,CAAAmC,kBAAA,MAAAV,GAAA,CAAAiS,eAAA,QA2sB1H,CAAC;IAAA;EAAA;EAAA1L,MAAA;EAAAC,aAAA;AAAA,EAA4b;AAC9b;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5sByHlI,EAAE,CAAAmI,iBAAA,CA4sBhC8K,yBAAyB,EAAc,CAAC;IACvH/L,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MAAEI,MAAM,EAAE,CAAC,2WAA2W;IAAE,CAAC;EAC3X,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,EAAE;MAAEkG,IAAI,EAAElH,EAAE,CAAC+T;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE1O,KAAK,EAAE,CAAC;MACpJ6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAE+S,cAAc,EAAE,CAAC;MACjBjM,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEuI,OAAO,EAAE,CAAC;MACVzB,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkL,SAAS,EAAE,CAAC;MACZpM,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgM,oBAAoB,CAAC;EACvBxQ,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACO,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqE,SAAS,GAAG1H,QAAQ,CAACsT,SAAS;IACnC,IAAI,CAAChQ,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACAsD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC2L,SAAS,CAAC,CAAC;EACpB;EACAhB,SAASA,CAAC1K,KAAK,EAAE;IACb;IACA,IAAIA,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;MAC9CT,KAAK,CAACU,cAAc,CAAC,CAAC;MACtB,IAAI,CAACgL,SAAS,CAAC,CAAC;IACpB;EACJ;EACAA,SAASA,CAAA,EAAG;IACR,MAAMpO,KAAK,GAAG,IAAI,CAACqO,QAAQ,CAAC,CAAC;IAC7B,QAAQrO,KAAK;MACT,KAAKnF,QAAQ,CAACqF,UAAU;QACpB,IAAI,CAACjB,MAAM,CAACK,KAAK,CAAC,CAAC;QACnB;MACJ,KAAKzE,QAAQ,CAACsT,SAAS;MACvB,KAAKtT,QAAQ,CAACyT,QAAQ;QAClB,IAAI,CAACrP,MAAM,CAACG,IAAI,CAAC,CAAC;QAClB;IACR;EACJ;EACAiP,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC9L,SAAS,GAAG,IAAI,CAACtD,MAAM,GAAG,IAAI,CAACA,MAAM,CAACe,KAAK,GAAGnF,QAAQ,CAACsT,SAAS;IACrE,OAAO,IAAI,CAAC5L,SAAS;EACzB;EACAnC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB2N,oBAAoB,CAAC1N,IAAI,YAAA+N,6BAAA7N,CAAA;EAAA,YAAAA,CAAA,IAAwFwN,oBAAoB,EAnxB/BpU,EAAE,CAAA6G,iBAAA,CAmxB+C7G,EAAE,CAAC8G,UAAU,GAnxB9D9G,EAAE,CAAA6G,iBAAA,CAmxByE/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AAC/P;AAAmBoT,oBAAoB,CAACpN,IAAI,kBApxB6EhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAoxBHkN,oBAAoB;EAAAjN,SAAA;EAAAE,YAAA,WAAAqN,kCAAAlT,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApxBnBxB,EAAE,CAAA2K,UAAA,mBAAAgK,8CAAA;QAAA,OAoxBHlT,GAAA,CAAAkH,OAAA,CAAQ,CAAC;MAAA,uBAAAiM,gDAAA/J,MAAA;QAAA,OAATpJ,GAAA,CAAA6R,SAAA,CAAAzI,MAAgB,CAAC;MAAA;IAAA;EAAA;EAAArD,MAAA;IAAAnC,KAAA;EAAA;EAAAqC,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAiN,8BAAArT,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApxBhBxB,EAAE,CAAAoD,SAAA,YA8xBnH,CAAC;IAAA;IAAA,IAAA5B,EAAA;MA9xBgHxB,EAAE,CAAAuH,WAAA,kBAAA9F,GAAA,CAAA8S,QAAA,gBAsxBxE,CAAC,uBAAA9S,GAAA,CAAA8S,QAAA,mBAAA9S,GAAA,CAAA8S,QAAA,cAAD,CAAC;MAtxBqEvU,EAAE,CAAAqL,WAAA,eAAA5J,GAAA,CAAA8S,QAAA,kCA4xB1D,CAAC,mBAAA9S,GAAA,CAAAgH,SAAD,CAAC;IAAA;EAAA;EAAAT,MAAA;EAAAC,aAAA;AAAA,EAE4Q;AAC9U;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/xByHlI,EAAE,CAAAmI,iBAAA,CA+xBhCiM,oBAAoB,EAAc,CAAC;IAClHlN,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MAAEI,MAAM,EAAE,CAAC,2PAA2P;IAAE,CAAC;EAC3Q,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEuI,OAAO,EAAE,CAAC;MACVzB,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkL,SAAS,EAAE,CAAC;MACZpM,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0M,eAAe,CAAC;EAClBlR,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACO,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqE,SAAS,GAAG,SAAS;IAC1B,IAAI,CAACpE,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/C,IAAI,CAAC0P,aAAa,GAAG,IAAI,CAAC5P,MAAM,CAAC8E,MAAM;EAC3C;EACAtB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACqM,eAAe,CAAC,CAAC;EAC1B;EACA1B,SAASA,CAAC1K,KAAK,EAAE;IACb;IACA,IAAIA,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;MAC9CT,KAAK,CAACU,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC0L,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAA,eAAeA,CAAA,EAAG;IACd,MAAM/K,MAAM,GAAG,IAAI,CAACvB,SAAS,CAAC,CAAC;IAC/B,IAAIuB,MAAM,KAAK,CAAC,EAAE;MACd,IAAI,IAAI,CAAC9E,MAAM,CAAC8E,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC8K,aAAa,KAAK,CAAC,EAAE;QACtD,IAAI,CAACA,aAAa,GAAG,CAAC;MAC1B;MACA,IAAI,CAAC5P,MAAM,CAAC8E,MAAM,GAAG,IAAI,CAAC8K,aAAa;IAC3C,CAAC,MACI;MACD,IAAI,CAACA,aAAa,GAAG9K,MAAM;MAC3B,IAAI,CAAC9E,MAAM,CAAC8E,MAAM,GAAG,CAAC;IAC1B;EACJ;EACAvB,SAASA,CAAA,EAAG;IACR,MAAMuB,MAAM,GAAG,IAAI,CAAC9E,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC8E,MAAM,GAAG,CAAC;IACnD,IAAI,CAACxB,SAAS,GAAGwB,MAAM,GAAG,SAAS,GAAG,OAAO;IAC7C,OAAOA,MAAM;EACjB;EACA3D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBqO,eAAe,CAACpO,IAAI,YAAAuO,wBAAArO,CAAA;EAAA,YAAAA,CAAA,IAAwFkO,eAAe,EAz2BrB9U,EAAE,CAAA6G,iBAAA,CAy2BqC7G,EAAE,CAAC8G,UAAU,GAz2BpD9G,EAAE,CAAA6G,iBAAA,CAy2B+D/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AACrP;AAAmB8T,eAAe,CAAC9N,IAAI,kBA12BkFhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EA02BR4N,eAAe;EAAA3N,SAAA;EAAAE,YAAA,WAAA6N,6BAAA1T,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA12BTxB,EAAE,CAAA2K,UAAA,mBAAAwK,yCAAA;QAAA,OA02BR1T,GAAA,CAAAkH,OAAA,CAAQ,CAAC;MAAA,uBAAAyM,2CAAAvK,MAAA;QAAA,OAATpJ,GAAA,CAAA6R,SAAA,CAAAzI,MAAgB,CAAC;MAAA;IAAA;EAAA;EAAArD,MAAA;IAAAnC,KAAA;EAAA;EAAAqC,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAyN,yBAAA7T,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA12BXxB,EAAE,CAAAoD,SAAA,YAo3BnH,CAAC;IAAA;IAAA,IAAA5B,EAAA;MAp3BgHxB,EAAE,CAAAuH,WAAA,sBAAA9F,GAAA,CAAAiH,SAAA,UA42BzE,CAAC,wBAAAjH,GAAA,CAAAiH,SAAA,cAAAjH,GAAA,CAAAiH,SAAA,SAAD,CAAC,wBAAAjH,GAAA,CAAAiH,SAAA,UAAAjH,GAAA,CAAAiH,SAAA,SAAD,CAAC,uBAAAjH,GAAA,CAAAiH,SAAA,QAAD,CAAC;MA52BsE1I,EAAE,CAAAqL,WAAA,mBAAA5J,GAAA,CAAAgH,SAm3BvF,CAAC;IAAA;EAAA;EAAAT,MAAA;EAAAC,aAAA;AAAA,EAC6R;AAClU;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAr3ByHlI,EAAE,CAAAmI,iBAAA,CAq3BhC2M,eAAe,EAAc,CAAC;IAC7G5N,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MAAEI,MAAM,EAAE,CAAC,+OAA+O;IAAE,CAAC;EAC/P,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEuI,OAAO,EAAE,CAAC;MACVzB,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkL,SAAS,EAAE,CAAC;MACZpM,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkN,qBAAqB,CAAC;EACxB1R,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE0R,KAAK,EAAE;IACzB,IAAI,CAAC1R,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC0R,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACpR,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqE,SAAS,GAAG,aAAa;IAC9B,IAAI,CAACpE,IAAI,GAAGP,GAAG,CAACQ,aAAa;IAC7B,IAAI,CAACF,aAAa,CAACM,IAAI,CAAC,IAAI,CAAC6Q,KAAK,CAACE,kBAAkB,CAAC9Q,SAAS,CAAC,IAAI,CAAC8Q,kBAAkB,CAAC5Q,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACxG;EACAN,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACAoQ,kBAAkBA,CAACC,OAAO,EAAE;IACxB,IAAI,CAACjN,SAAS,GAAGiN,OAAO,GAAG,iBAAiB,GAAG,aAAa;IAC5D,IAAI,CAACF,YAAY,GAAGE,OAAO;EAC/B;EACA/M,OAAOA,CAAA,EAAG;IACN,IAAI,CAACgN,qBAAqB,CAAC,CAAC;EAChC;EACArC,SAASA,CAAC1K,KAAK,EAAE;IACb;IACA,IAAIA,KAAK,CAACS,OAAO,KAAK,EAAE,IAAIT,KAAK,CAACS,OAAO,KAAK,EAAE,EAAE;MAC9CT,KAAK,CAACU,cAAc,CAAC,CAAC;MACtB,IAAI,CAACqM,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAA,qBAAqBA,CAAA,EAAG;IACpB,IAAIrF,OAAO,GAAG,IAAI,CAACnL,MAAM;IACzB,IAAI,IAAI,CAACA,MAAM,YAAYnE,YAAY,EAAE;MACrCsP,OAAO,GAAG,IAAI;IAClB;IACA,IAAI,CAACiF,KAAK,CAACK,gBAAgB,CAACtF,OAAO,CAAC;EACxC;EACAhK,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB6O,qBAAqB,CAAC5O,IAAI,YAAAmP,8BAAAjP,CAAA;EAAA,YAAAA,CAAA,IAAwF0O,qBAAqB,EA17BjCtV,EAAE,CAAA6G,iBAAA,CA07BiD7G,EAAE,CAAC8G,UAAU,GA17BhE9G,EAAE,CAAA6G,iBAAA,CA07B2E/F,EAAE,CAACE,YAAY,GA17B5FhB,EAAE,CAAA6G,iBAAA,CA07BuG/F,EAAE,CAACgV,sBAAsB;AAAA,CAA4C;AACvS;AAAmBR,qBAAqB,CAACtO,IAAI,kBA37B4EhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EA27BFoO,qBAAqB;EAAAnO,SAAA;EAAAE,YAAA,WAAA0O,mCAAAvU,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA37BrBxB,EAAE,CAAA2K,UAAA,mBAAAqL,+CAAA;QAAA,OA27BFvU,GAAA,CAAAkH,OAAA,CAAQ,CAAC;MAAA,uBAAAsN,iDAAApL,MAAA;QAAA,OAATpJ,GAAA,CAAA6R,SAAA,CAAAzI,MAAgB,CAAC;MAAA;IAAA;EAAA;EAAAnD,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAsO,+BAAA1U,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA37BjBxB,EAAE,CAAAoD,SAAA,YAm8BnH,CAAC;IAAA;IAAA,IAAA5B,EAAA;MAn8BgHxB,EAAE,CAAAuH,WAAA,wBAAA9F,GAAA,CAAA+T,YA67B9E,CAAC,4BAAA/T,GAAA,CAAA+T,YAAD,CAAC;MA77B2ExV,EAAE,CAAAqL,WAAA,mBAAA5J,GAAA,CAAAgH,SAk8BvF,CAAC;IAAA;EAAA;EAAAT,MAAA;EAAAC,aAAA;AAAA,EACyS;AAC9U;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAp8ByHlI,EAAE,CAAAmI,iBAAA,CAo8BhCmN,qBAAqB,EAAc,CAAC;IACnHpO,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MAAEI,MAAM,EAAE,CAAC,2PAA2P;IAAE,CAAC;EAC3Q,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,EAAE;MAAEkG,IAAI,EAAEpG,EAAE,CAACgV;IAAuB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEnN,OAAO,EAAE,CAAC;MAC3JzB,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEkL,SAAS,EAAE,CAAC;MACZpM,IAAI,EAAE5G,YAAY;MAClB8H,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+N,gCAAgC,CAAC;EACnCvS,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACO,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACA+Q,aAAaA,CAAA,EAAG;IACZ,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAI,IAAI,CAAClR,MAAM,EAAEmR,QAAQ,EAAEjE,MAAM,EAAE;MAC/B,IAAI,IAAI,CAAClN,MAAM,CAACwJ,IAAI,CAACqB,KAAK,KAAK,CAAC,EAAE;QAC9BqG,UAAU,GAAG,IAAI;MACrB,CAAC,MACI;QACDA,UAAU,GACL,IAAI,CAAClR,MAAM,CAACoR,MAAM,CAACC,GAAG,GAAG,IAAI,CAACrR,MAAM,CAACwJ,IAAI,CAACqB,KAAK,GAAI,GAAG,GAAG,GAAG;MACrE;IACJ;IACA,OAAOqG,UAAU;EACrB;EACA/P,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB0P,gCAAgC,CAACzP,IAAI,YAAA+P,yCAAA7P,CAAA;EAAA,YAAAA,CAAA,IAAwFuP,gCAAgC,EAz/BvDnW,EAAE,CAAA6G,iBAAA,CAy/BuE7G,EAAE,CAAC8G,UAAU,GAz/BtF9G,EAAE,CAAA6G,iBAAA,CAy/BiG/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AACvR;AAAmBmV,gCAAgC,CAACnP,IAAI,kBA1/BiEhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EA0/BSiP,gCAAgC;EAAAhP,SAAA;EAAAK,MAAA;IAAAnC,KAAA;EAAA;EAAAqC,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAA8O,0CAAAlV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1/B3CxB,EAAE,CAAAoD,SAAA,YA0/BsM,CAAC;IAAA;IAAA,IAAA5B,EAAA;MA1/BzMxB,EAAE,CAAAsD,WAAA,UAAA7B,GAAA,CAAA2U,aAAA,EA0/B+L,CAAC;IAAA;EAAA;EAAApO,MAAA;EAAAC,aAAA;AAAA,EAAiZ;AAC5sB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3/ByHlI,EAAE,CAAAmI,iBAAA,CA2/BhCgO,gCAAgC,EAAc,CAAC;IAC9HjP,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG,gEAA+D;MAAEI,MAAM,EAAE,CAAC,gUAAgU;IAAE,CAAC;EACrf,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMuW,4BAA4B,CAAC;EAC/B/S,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC+S,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACzS,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC0S,SAAS,GAAG,CAAC;IAClB,IAAI,CAACzS,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/C,MAAM0R,YAAY,GAAG,IAAI,CAAC5R,MAAM,CAACf,aAAa,CAAC4S,cAAc;IAC7D,IAAI,CAAC5S,aAAa,CAACM,IAAI,CAACqS,YAAY,CAACpS,SAAS,CAAC,IAAI,CAACsS,gBAAgB,CAACpS,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjF,IAAI,IAAI,CAAC+R,sBAAsB,EAAE;MAC7B,IAAI,CAACK,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAA,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB;MACA;MACA,IAAI,CAACL,SAAS,GAAG,EAAE;MACnB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC7E,MAAM,EAAE8E,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QACrD,MAAMX,GAAG,GAAG,IAAI,CAACU,WAAW,CAACC,CAAC,CAAC,CAACE,OAAO,IAAI,CAAC,GACtC,IAAI,CAACH,WAAW,CAACC,CAAC,CAAC,CAACE,OAAO,GAC3B,IAAI,CAACH,WAAW,CAACC,CAAC,CAAC,CAACG,SAAS,GAAG,CAAC;QACvC,MAAMC,gBAAgB,GAAG,CAACf,GAAG,GAAG,IAAI,CAACU,WAAW,CAACC,CAAC,CAAC,CAACG,SAAS,IAAI,IAAI;QACrE,IAAIE,QAAQ,GAAG,GAAG;QAClB,IAAIC,YAAY,GAAG,GAAG;QACtB,IAAI,OAAOF,gBAAgB,KAAK,QAAQ,IAAI,IAAI,CAACpS,MAAM,CAACwJ,IAAI,CAACqB,KAAK,EAAE;UAChEyH,YAAY,GACPF,gBAAgB,GAAG,GAAG,GAAI,IAAI,CAACpS,MAAM,CAACwJ,IAAI,CAACqB,KAAK,GAAG,GAAG;UAC3DwH,QAAQ,GACH,IAAI,CAACN,WAAW,CAACC,CAAC,CAAC,CAACG,SAAS,GAAG,GAAG,GAChC/N,IAAI,CAACmF,KAAK,CAAC,IAAI,CAACvJ,MAAM,CAACwJ,IAAI,CAACqB,KAAK,GAAG,IAAI,CAAC,GACzC,GAAG;QACf;QACA,IAAI,CAACkH,WAAW,CAACC,CAAC,CAAC,CAAC5T,OAAO,GAAG;UAC1BC,KAAK,EAAEiU,YAAY;UACnBhU,IAAI,EAAE+T;QACV,CAAC;QACD,IAAI,CAACX,SAAS,CAACnS,IAAI,CAAC,IAAI,CAACwS,WAAW,CAACC,CAAC,CAAC,CAAC;MAC5C;IACJ;EACJ;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACvS,MAAM,EAAE;MACd,IAAI,CAACyR,sBAAsB,GAAG,IAAI;MAClC;IACJ;IACA,IAAI,CAACK,gBAAgB,CAAC,CAAC;EAC3B;EACAhF,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACgF,WAAW,CAAC9E,YAAY,EAAE;MAClC,IAAI,CAACsF,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,WAAW,EAAE;MAClB,MAAMhF,OAAO,GAAG,IAAI,CAAC4E,SAAS,KAAK,IAAI,CAACI,WAAW,CAAC7E,MAAM;MAC1D,IAAIH,OAAO,EAAE;QACT,IAAI,CAAC4E,SAAS,GAAG,IAAI,CAACI,WAAW,CAAC7E,MAAM;QACxC,IAAI,CAACqF,eAAe,CAAC,CAAC;MAC1B;IACJ;EACJ;EACApR,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBkQ,4BAA4B,CAACjQ,IAAI,YAAAkR,qCAAAhR,CAAA;EAAA,YAAAA,CAAA,IAAwF+P,4BAA4B,EAjlC/C3W,EAAE,CAAA6G,iBAAA,CAilC+D7G,EAAE,CAAC8G,UAAU,GAjlC9E9G,EAAE,CAAA6G,iBAAA,CAilCyF/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AAC/Q;AAAmB2V,4BAA4B,CAAC3P,IAAI,kBAllCqEhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EAklCKyP,4BAA4B;EAAAxP,SAAA;EAAAK,MAAA;IAAA0P,WAAA;IAAA7R,KAAA;EAAA;EAAAwN,QAAA,GAllCnC7S,EAAE,CAAA8S,oBAAA;EAAApL,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAiQ,sCAAArW,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxB,EAAE,CAAA0B,cAAA,YAmlCvF,CAAC;MAnlCoF1B,EAAE,CAAAoN,UAAA,IAAAjK,4CAAA,iBAylC9G,CAAC;MAzlC2GnD,EAAE,CAAA4B,YAAA,CA0lClH,CAAC;IAAA;IAAA,IAAAJ,EAAA;MA1lC+GxB,EAAE,CAAAkC,SAAA,EAqlCzF,CAAC;MArlCsFlC,EAAE,CAAA+B,UAAA,YAAAN,GAAA,CAAAoV,SAqlCzF,CAAC;IAAA;EAAA;EAAAtL,YAAA,GAMuU5K,EAAE,CAAC0M,OAAO;EAAArF,MAAA;EAAAC,aAAA;AAAA,EAAoI;AACxf;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5lCyHlI,EAAE,CAAAmI,iBAAA,CA4lChCwO,4BAA4B,EAAc,CAAC;IAC1HzP,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,+RAA+R;IAAE,CAAC;EACxS,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkW,WAAW,EAAE,CAAC;MAC1HhQ,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEiF,KAAK,EAAE,CAAC;MACR6B,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0X,8BAA8B,CAAC;EACjClU,WAAWA,CAACE,GAAG,EAAED,GAAG,EAAE;IAClB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACuL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAChL,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,IAAI,GAAGP,GAAG,CAACQ,aAAa;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,GAAG,CAACmB,aAAa,EAAE;MACxB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACb,aAAa,CAACM,IAAI,CAAC,IAAI,CAACb,GAAG,CAACqB,gBAAgB,CAACP,SAAS,CAAC,MAAM,IAAI,CAACM,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5F;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACuB,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;EACnD;EACA8L,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChM,MAAM,GACZoE,IAAI,CAACmF,KAAK,CAAE,IAAI,CAACvJ,MAAM,CAACwJ,IAAI,CAACoB,OAAO,GAAG,GAAG,GAAI,IAAI,CAAC5K,MAAM,CAACwJ,IAAI,CAACqB,KAAK,CAAC,GACnE,GAAG,GACL,IAAI;EACd;EACA1J,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBqR,8BAA8B,CAACpR,IAAI,YAAAqR,uCAAAnR,CAAA;EAAA,YAAAA,CAAA,IAAwFkR,8BAA8B,EA1oCnD9X,EAAE,CAAA6G,iBAAA,CA0oCmE7G,EAAE,CAAC8G,UAAU,GA1oClF9G,EAAE,CAAA6G,iBAAA,CA0oC6F/F,EAAE,CAACE,YAAY;AAAA,CAA4C;AACnR;AAAmB8W,8BAA8B,CAAC9Q,IAAI,kBA3oCmEhH,EAAE,CAAAiH,iBAAA;EAAAC,IAAA,EA2oCO4Q,8BAA8B;EAAA3Q,SAAA;EAAAK,MAAA;IAAAnC,KAAA;IAAA+J,QAAA;EAAA;EAAA1H,KAAA;EAAAC,IAAA;EAAAsD,MAAA;EAAArD,QAAA,WAAAoQ,wCAAAxW,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3oCvCxB,EAAE,CAAAoD,SAAA,YA2oCsN,CAAC;MA3oCzNpD,EAAE,CAAAoN,UAAA,IAAA1J,8CAAA,iBA4oC3E,CAAC;IAAA;IAAA,IAAAlC,EAAA;MA5oCwExB,EAAE,CAAAsD,WAAA,UAAA7B,GAAA,CAAA0P,aAAA,EA2oC+M,CAAC;MA3oClNnR,EAAE,CAAAkC,SAAA,EA4oCpF,CAAC;MA5oCiFlC,EAAE,CAAA+B,UAAA,SAAAN,GAAA,CAAA2N,QA4oCpF,CAAC;IAAA;EAAA;EAAA7D,YAAA,GAAqkB5K,EAAE,CAACqO,IAAI;EAAAhH,MAAA;EAAAC,aAAA;AAAA,EAA8G;AACluB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7oCyHlI,EAAE,CAAAmI,iBAAA,CA6oChC2P,8BAA8B,EAAc,CAAC;IAC5H5Q,IAAI,EAAEjH,SAAS;IACfmI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,2BAA2B;MAAEJ,aAAa,EAAE/H,iBAAiB,CAACoI,IAAI;MAAEV,QAAQ,EAAG;AAC9G,kDAAkD;MAAEI,MAAM,EAAE,CAAC,mfAAmf;IAAE,CAAC;EAC3iB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAElH,EAAE,CAAC8G;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEpG,EAAE,CAACE;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqE,KAAK,EAAE,CAAC;MACpH6B,IAAI,EAAE9G;IACV,CAAC,CAAC;IAAEgP,QAAQ,EAAE,CAAC;MACXlI,IAAI,EAAE9G;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6X,UAAU,GAAG,CACftU,mBAAmB,EACnB4E,iBAAiB,EACjBmD,wBAAwB,EACxB8C,sBAAsB,EACtBS,mBAAmB,EACnB8C,0BAA0B,EAC1BkB,yBAAyB,EACzBmB,oBAAoB,EACpBU,eAAe,EACfQ,qBAAqB,EACrBhI,SAAS,EACT6I,gCAAgC,EAChCQ,4BAA4B,EAC5BmB,8BAA8B,CACjC;AACD,MAAMI,gBAAgB,CAAC;AAEvB;AAAmBA,gBAAgB,CAACxR,IAAI,YAAAyR,yBAAAvR,CAAA;EAAA,YAAAA,CAAA,IAAwFsR,gBAAgB;AAAA,CAAkD;AAClM;AAAmBA,gBAAgB,CAACE,IAAI,kBA1qCiFpY,EAAE,CAAAqY,gBAAA;EAAAnR,IAAA,EA0qCMgR;AAAgB,EA0BvG;AAC1C;AAAmBA,gBAAgB,CAACI,IAAI,kBArsCiFtY,EAAE,CAAAuY,gBAAA;EAAAC,OAAA,GAqsCkC5X,YAAY,EAAEK,YAAY;AAAA,EAAI;AAC3L;EAAA,QAAAiH,SAAA,oBAAAA,SAAA,KAtsCyHlI,EAAE,CAAAmI,iBAAA,CAssChC+P,gBAAgB,EAAc,CAAC;IAC9GhR,IAAI,EAAExG,QAAQ;IACd0H,IAAI,EAAE,CAAC;MACCoQ,OAAO,EAAE,CAAC5X,YAAY,EAAEK,YAAY,CAAC;MACrCwX,YAAY,EAAE,CAAC,GAAGR,UAAU,CAAC;MAC7BS,OAAO,EAAE,CAAC,GAAGT,UAAU;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAStU,mBAAmB,EAAEuU,gBAAgB,EAAE5C,qBAAqB,EAAER,eAAe,EAAEV,oBAAoB,EAAEnB,yBAAyB,EAAElB,0BAA0B,EAAEoE,gCAAgC,EAAElH,mBAAmB,EAAE0H,4BAA4B,EAAEmB,8BAA8B,EAAEtJ,sBAAsB,EAAE9C,wBAAwB,EAAE4B,SAAS,EAAE/E,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
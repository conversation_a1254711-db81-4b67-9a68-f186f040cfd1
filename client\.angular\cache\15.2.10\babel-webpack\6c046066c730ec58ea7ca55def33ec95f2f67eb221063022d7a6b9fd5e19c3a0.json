{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { VerifyTwofaComponent } from './verify-twofa.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport class VerifyTwofaModule {\n  static #_ = this.ɵfac = function VerifyTwofaModule_Factory(t) {\n    return new (t || VerifyTwofaModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: VerifyTwofaModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, TranslateModule, FormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VerifyTwofaModule, {\n    declarations: [VerifyTwofaComponent],\n    imports: [CommonModule, TranslateModule, FormsModule],\n    exports: [VerifyTwofaComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,WAAW,QAAQ,gBAAgB;;AAO5C,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA;UAAjBF;EAAiB;EAAA,QAAAG,EAAA;cAHlBP,YAAY,EAAEE,eAAe,EAAEC,WAAW;EAAA;;;2EAGzCC,iBAAiB;IAAAI,YAAA,GAJbP,oBAAoB;IAAAQ,OAAA,GACzBT,YAAY,EAAEE,eAAe,EAAEC,WAAW;IAAAO,OAAA,GAC1CT,oBAAoB;EAAA;AAAA", "names": ["CommonModule", "VerifyTwofaComponent", "TranslateModule", "FormsModule", "VerifyTwofaModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\verify-twofa\\verify-twofa.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { VerifyTwofaComponent } from './verify-twofa.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@NgModule({\r\n  declarations: [VerifyTwofaComponent],\r\n  imports: [CommonModule, TranslateModule, FormsModule],\r\n  exports: [VerifyTwofaComponent],\r\n})\r\nexport class VerifyTwofaModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
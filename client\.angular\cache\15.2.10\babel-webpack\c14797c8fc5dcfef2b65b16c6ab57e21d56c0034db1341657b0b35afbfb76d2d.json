{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\n\n/**\n * Creates a flattened array of values by running each element in `collection`\n * thru `iteratee` and flattening the mapped results. The iteratee is invoked\n * with three arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [n, n];\n * }\n *\n * _.flatMap([1, 2], duplicate);\n * // => [1, 1, 2, 2]\n */\nfunction flatMap(collection, iteratee) {\n  return baseFlatten(map(collection, iteratee), 1);\n}\nexport default flatMap;", "map": {"version": 3, "names": ["baseFlatten", "map", "flatMap", "collection", "iteratee"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/flatMap.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\n\n/**\n * Creates a flattened array of values by running each element in `collection`\n * thru `iteratee` and flattening the mapped results. The iteratee is invoked\n * with three arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [n, n];\n * }\n *\n * _.flatMap([1, 2], duplicate);\n * // => [1, 1, 2, 2]\n */\nfunction flatMap(collection, iteratee) {\n  return baseFlatten(map(collection, iteratee), 1);\n}\n\nexport default flatMap;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,GAAG,MAAM,UAAU;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EACrC,OAAOJ,WAAW,CAACC,GAAG,CAACE,UAAU,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAClD;AAEA,eAAeF,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
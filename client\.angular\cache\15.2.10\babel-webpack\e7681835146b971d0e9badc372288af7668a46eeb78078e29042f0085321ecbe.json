{"ast": null, "code": "import baseInRange from './_baseInRange.js';\nimport toFinite from './toFinite.js';\nimport toNumber from './toNumber.js';\n\n/**\n * Checks if `n` is between `start` and up to, but not including, `end`. If\n * `end` is not specified, it's set to `start` with `start` then set to `0`.\n * If `start` is greater than `end` the params are swapped to support\n * negative ranges.\n *\n * @static\n * @memberOf _\n * @since 3.3.0\n * @category Number\n * @param {number} number The number to check.\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n * @see _.range, _.rangeRight\n * @example\n *\n * _.inRange(3, 2, 4);\n * // => true\n *\n * _.inRange(4, 8);\n * // => true\n *\n * _.inRange(4, 2);\n * // => false\n *\n * _.inRange(2, 2);\n * // => false\n *\n * _.inRange(1.2, 2);\n * // => true\n *\n * _.inRange(5.2, 4);\n * // => false\n *\n * _.inRange(-3, -2, -6);\n * // => true\n */\nfunction inRange(number, start, end) {\n  start = toFinite(start);\n  if (end === undefined) {\n    end = start;\n    start = 0;\n  } else {\n    end = toFinite(end);\n  }\n  number = toNumber(number);\n  return baseInRange(number, start, end);\n}\nexport default inRange;", "map": {"version": 3, "names": ["baseInRange", "toFinite", "toNumber", "inRange", "number", "start", "end", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/inRange.js"], "sourcesContent": ["import baseInRange from './_baseInRange.js';\nimport toFinite from './toFinite.js';\nimport toNumber from './toNumber.js';\n\n/**\n * Checks if `n` is between `start` and up to, but not including, `end`. If\n * `end` is not specified, it's set to `start` with `start` then set to `0`.\n * If `start` is greater than `end` the params are swapped to support\n * negative ranges.\n *\n * @static\n * @memberOf _\n * @since 3.3.0\n * @category Number\n * @param {number} number The number to check.\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n * @see _.range, _.rangeRight\n * @example\n *\n * _.inRange(3, 2, 4);\n * // => true\n *\n * _.inRange(4, 8);\n * // => true\n *\n * _.inRange(4, 2);\n * // => false\n *\n * _.inRange(2, 2);\n * // => false\n *\n * _.inRange(1.2, 2);\n * // => true\n *\n * _.inRange(5.2, 4);\n * // => false\n *\n * _.inRange(-3, -2, -6);\n * // => true\n */\nfunction inRange(number, start, end) {\n  start = toFinite(start);\n  if (end === undefined) {\n    end = start;\n    start = 0;\n  } else {\n    end = toFinite(end);\n  }\n  number = toNumber(number);\n  return baseInRange(number, start, end);\n}\n\nexport default inRange;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACnCD,KAAK,GAAGJ,QAAQ,CAACI,KAAK,CAAC;EACvB,IAAIC,GAAG,KAAKC,SAAS,EAAE;IACrBD,GAAG,GAAGD,KAAK;IACXA,KAAK,GAAG,CAAC;EACX,CAAC,MAAM;IACLC,GAAG,GAAGL,QAAQ,CAACK,GAAG,CAAC;EACrB;EACAF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzB,OAAOJ,WAAW,CAACI,MAAM,EAAEC,KAAK,EAAEC,GAAG,CAAC;AACxC;AAEA,eAAeH,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { canReportError } from './util/canReportError';\nimport { toSubscriber } from './util/toSubscriber';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nexport class Observable {\n  constructor(subscribe) {\n    this._isScalar = false;\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n  lift(operator) {\n    const observable = new Observable();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n  subscribe(observerOrNext, error, complete) {\n    const {\n      operator\n    } = this;\n    const sink = toSubscriber(observerOrNext, error, complete);\n    if (operator) {\n      sink.add(operator.call(sink, this.source));\n    } else {\n      sink.add(this.source || config.useDeprecatedSynchronousErrorHandling && !sink.syncErrorThrowable ? this._subscribe(sink) : this._trySubscribe(sink));\n    }\n    if (config.useDeprecatedSynchronousErrorHandling) {\n      if (sink.syncErrorThrowable) {\n        sink.syncErrorThrowable = false;\n        if (sink.syncErrorThrown) {\n          throw sink.syncErrorValue;\n        }\n      }\n    }\n    return sink;\n  }\n  _trySubscribe(sink) {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      if (config.useDeprecatedSynchronousErrorHandling) {\n        sink.syncErrorThrown = true;\n        sink.syncErrorValue = err;\n      }\n      if (canReportError(sink)) {\n        sink.error(err);\n      } else {\n        console.warn(err);\n      }\n    }\n  }\n  forEach(next, promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      let subscription;\n      subscription = this.subscribe(value => {\n        try {\n          next(value);\n        } catch (err) {\n          reject(err);\n          if (subscription) {\n            subscription.unsubscribe();\n          }\n        }\n      }, reject, resolve);\n    });\n  }\n  _subscribe(subscriber) {\n    const {\n      source\n    } = this;\n    return source && source.subscribe(subscriber);\n  }\n  [Symbol_observable]() {\n    return this;\n  }\n  pipe(...operations) {\n    if (operations.length === 0) {\n      return this;\n    }\n    return pipeFromArray(operations)(this);\n  }\n  toPromise(promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      let value;\n      this.subscribe(x => value = x, err => reject(err), () => resolve(value));\n    });\n  }\n}\nObservable.create = subscribe => {\n  return new Observable(subscribe);\n};\nfunction getPromiseCtor(promiseCtor) {\n  if (!promiseCtor) {\n    promiseCtor = config.Promise || Promise;\n  }\n  if (!promiseCtor) {\n    throw new Error('no Promise impl found');\n  }\n  return promiseCtor;\n}", "map": {"version": 3, "names": ["canReportError", "toSubscriber", "observable", "Symbol_observable", "pipeFromArray", "config", "Observable", "constructor", "subscribe", "_isScalar", "_subscribe", "lift", "operator", "source", "observerOrNext", "error", "complete", "sink", "add", "call", "useDeprecatedSynchronousErrorHandling", "syncErrorThrowable", "_trySubscribe", "syncErrorThrown", "syncErrorValue", "err", "console", "warn", "for<PERSON>ach", "next", "promiseCtor", "getPromiseCtor", "resolve", "reject", "subscription", "value", "unsubscribe", "subscriber", "pipe", "operations", "length", "to<PERSON>romise", "x", "create", "Promise", "Error"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/Observable.js"], "sourcesContent": ["import { canReportError } from './util/canReportError';\nimport { toSubscriber } from './util/toSubscriber';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nexport class Observable {\n    constructor(subscribe) {\n        this._isScalar = false;\n        if (subscribe) {\n            this._subscribe = subscribe;\n        }\n    }\n    lift(operator) {\n        const observable = new Observable();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    }\n    subscribe(observerOrNext, error, complete) {\n        const { operator } = this;\n        const sink = toSubscriber(observerOrNext, error, complete);\n        if (operator) {\n            sink.add(operator.call(sink, this.source));\n        }\n        else {\n            sink.add(this.source || (config.useDeprecatedSynchronousErrorHandling && !sink.syncErrorThrowable) ?\n                this._subscribe(sink) :\n                this._trySubscribe(sink));\n        }\n        if (config.useDeprecatedSynchronousErrorHandling) {\n            if (sink.syncErrorThrowable) {\n                sink.syncErrorThrowable = false;\n                if (sink.syncErrorThrown) {\n                    throw sink.syncErrorValue;\n                }\n            }\n        }\n        return sink;\n    }\n    _trySubscribe(sink) {\n        try {\n            return this._subscribe(sink);\n        }\n        catch (err) {\n            if (config.useDeprecatedSynchronousErrorHandling) {\n                sink.syncErrorThrown = true;\n                sink.syncErrorValue = err;\n            }\n            if (canReportError(sink)) {\n                sink.error(err);\n            }\n            else {\n                console.warn(err);\n            }\n        }\n    }\n    forEach(next, promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            let subscription;\n            subscription = this.subscribe((value) => {\n                try {\n                    next(value);\n                }\n                catch (err) {\n                    reject(err);\n                    if (subscription) {\n                        subscription.unsubscribe();\n                    }\n                }\n            }, reject, resolve);\n        });\n    }\n    _subscribe(subscriber) {\n        const { source } = this;\n        return source && source.subscribe(subscriber);\n    }\n    [Symbol_observable]() {\n        return this;\n    }\n    pipe(...operations) {\n        if (operations.length === 0) {\n            return this;\n        }\n        return pipeFromArray(operations)(this);\n    }\n    toPromise(promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            let value;\n            this.subscribe((x) => value = x, (err) => reject(err), () => resolve(value));\n        });\n    }\n}\nObservable.create = (subscribe) => {\n    return new Observable(subscribe);\n};\nfunction getPromiseCtor(promiseCtor) {\n    if (!promiseCtor) {\n        promiseCtor = config.Promise || Promise;\n    }\n    if (!promiseCtor) {\n        throw new Error('no Promise impl found');\n    }\n    return promiseCtor;\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uBAAuB;AACtD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,qBAAqB;AACrE,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAID,SAAS,EAAE;MACX,IAAI,CAACE,UAAU,GAAGF,SAAS;IAC/B;EACJ;EACAG,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMV,UAAU,GAAG,IAAII,UAAU,CAAC,CAAC;IACnCJ,UAAU,CAACW,MAAM,GAAG,IAAI;IACxBX,UAAU,CAACU,QAAQ,GAAGA,QAAQ;IAC9B,OAAOV,UAAU;EACrB;EACAM,SAASA,CAACM,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACvC,MAAM;MAAEJ;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMK,IAAI,GAAGhB,YAAY,CAACa,cAAc,EAAEC,KAAK,EAAEC,QAAQ,CAAC;IAC1D,IAAIJ,QAAQ,EAAE;MACVK,IAAI,CAACC,GAAG,CAACN,QAAQ,CAACO,IAAI,CAACF,IAAI,EAAE,IAAI,CAACJ,MAAM,CAAC,CAAC;IAC9C,CAAC,MACI;MACDI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACL,MAAM,IAAKR,MAAM,CAACe,qCAAqC,IAAI,CAACH,IAAI,CAACI,kBAAmB,GAC9F,IAAI,CAACX,UAAU,CAACO,IAAI,CAAC,GACrB,IAAI,CAACK,aAAa,CAACL,IAAI,CAAC,CAAC;IACjC;IACA,IAAIZ,MAAM,CAACe,qCAAqC,EAAE;MAC9C,IAAIH,IAAI,CAACI,kBAAkB,EAAE;QACzBJ,IAAI,CAACI,kBAAkB,GAAG,KAAK;QAC/B,IAAIJ,IAAI,CAACM,eAAe,EAAE;UACtB,MAAMN,IAAI,CAACO,cAAc;QAC7B;MACJ;IACJ;IACA,OAAOP,IAAI;EACf;EACAK,aAAaA,CAACL,IAAI,EAAE;IAChB,IAAI;MACA,OAAO,IAAI,CAACP,UAAU,CAACO,IAAI,CAAC;IAChC,CAAC,CACD,OAAOQ,GAAG,EAAE;MACR,IAAIpB,MAAM,CAACe,qCAAqC,EAAE;QAC9CH,IAAI,CAACM,eAAe,GAAG,IAAI;QAC3BN,IAAI,CAACO,cAAc,GAAGC,GAAG;MAC7B;MACA,IAAIzB,cAAc,CAACiB,IAAI,CAAC,EAAE;QACtBA,IAAI,CAACF,KAAK,CAACU,GAAG,CAAC;MACnB,CAAC,MACI;QACDC,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC;MACrB;IACJ;EACJ;EACAG,OAAOA,CAACC,IAAI,EAAEC,WAAW,EAAE;IACvBA,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IACzC,OAAO,IAAIA,WAAW,CAAC,CAACE,OAAO,EAAEC,MAAM,KAAK;MACxC,IAAIC,YAAY;MAChBA,YAAY,GAAG,IAAI,CAAC1B,SAAS,CAAE2B,KAAK,IAAK;QACrC,IAAI;UACAN,IAAI,CAACM,KAAK,CAAC;QACf,CAAC,CACD,OAAOV,GAAG,EAAE;UACRQ,MAAM,CAACR,GAAG,CAAC;UACX,IAAIS,YAAY,EAAE;YACdA,YAAY,CAACE,WAAW,CAAC,CAAC;UAC9B;QACJ;MACJ,CAAC,EAAEH,MAAM,EAAED,OAAO,CAAC;IACvB,CAAC,CAAC;EACN;EACAtB,UAAUA,CAAC2B,UAAU,EAAE;IACnB,MAAM;MAAExB;IAAO,CAAC,GAAG,IAAI;IACvB,OAAOA,MAAM,IAAIA,MAAM,CAACL,SAAS,CAAC6B,UAAU,CAAC;EACjD;EACA,CAAClC,iBAAiB,IAAI;IAClB,OAAO,IAAI;EACf;EACAmC,IAAIA,CAAC,GAAGC,UAAU,EAAE;IAChB,IAAIA,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO,IAAI;IACf;IACA,OAAOpC,aAAa,CAACmC,UAAU,CAAC,CAAC,IAAI,CAAC;EAC1C;EACAE,SAASA,CAACX,WAAW,EAAE;IACnBA,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IACzC,OAAO,IAAIA,WAAW,CAAC,CAACE,OAAO,EAAEC,MAAM,KAAK;MACxC,IAAIE,KAAK;MACT,IAAI,CAAC3B,SAAS,CAAEkC,CAAC,IAAKP,KAAK,GAAGO,CAAC,EAAGjB,GAAG,IAAKQ,MAAM,CAACR,GAAG,CAAC,EAAE,MAAMO,OAAO,CAACG,KAAK,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;AACJ;AACA7B,UAAU,CAACqC,MAAM,GAAInC,SAAS,IAAK;EAC/B,OAAO,IAAIF,UAAU,CAACE,SAAS,CAAC;AACpC,CAAC;AACD,SAASuB,cAAcA,CAACD,WAAW,EAAE;EACjC,IAAI,CAACA,WAAW,EAAE;IACdA,WAAW,GAAGzB,MAAM,CAACuC,OAAO,IAAIA,OAAO;EAC3C;EACA,IAAI,CAACd,WAAW,EAAE;IACd,MAAM,IAAIe,KAAK,CAAC,uBAAuB,CAAC;EAC5C;EACA,OAAOf,WAAW;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
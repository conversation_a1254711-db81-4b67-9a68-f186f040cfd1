{"ast": null, "code": "const encodeUtf8 = require('encode-utf8');\nconst Mode = require('./mode');\nfunction ByteData(data) {\n  this.mode = Mode.BYTE;\n  this.data = new Uint8Array(encodeUtf8(data));\n}\nByteData.getBitsLength = function getBitsLength(length) {\n  return length * 8;\n};\nByteData.prototype.getLength = function getLength() {\n  return this.data.length;\n};\nByteData.prototype.getBitsLength = function getBitsLength() {\n  return ByteData.getBitsLength(this.data.length);\n};\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8);\n  }\n};\nmodule.exports = ByteData;", "map": {"version": 3, "names": ["encodeUtf8", "require", "Mode", "ByteData", "data", "mode", "BYTE", "Uint8Array", "getBitsLength", "length", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "write", "bitBuffer", "i", "l", "put", "module", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/core/byte-data.js"], "sourcesContent": ["const encodeUtf8 = require('encode-utf8')\nconst Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  this.data = new Uint8Array(encodeUtf8(data))\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n"], "mappings": "AAAA,MAAMA,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AACzC,MAAMC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAE9B,SAASE,QAAQA,CAAEC,IAAI,EAAE;EACvB,IAAI,CAACC,IAAI,GAAGH,IAAI,CAACI,IAAI;EACrB,IAAI,CAACF,IAAI,GAAG,IAAIG,UAAU,CAACP,UAAU,CAACI,IAAI,CAAC,CAAC;AAC9C;AAEAD,QAAQ,CAACK,aAAa,GAAG,SAASA,aAAaA,CAAEC,MAAM,EAAE;EACvD,OAAOA,MAAM,GAAG,CAAC;AACnB,CAAC;AAEDN,QAAQ,CAACO,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACnD,OAAO,IAAI,CAACP,IAAI,CAACK,MAAM;AACzB,CAAC;AAEDN,QAAQ,CAACO,SAAS,CAACF,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAI;EAC3D,OAAOL,QAAQ,CAACK,aAAa,CAAC,IAAI,CAACJ,IAAI,CAACK,MAAM,CAAC;AACjD,CAAC;AAEDN,QAAQ,CAACO,SAAS,CAACE,KAAK,GAAG,UAAUC,SAAS,EAAE;EAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACX,IAAI,CAACK,MAAM,EAAEK,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAChDD,SAAS,CAACG,GAAG,CAAC,IAAI,CAACZ,IAAI,CAACU,CAAC,CAAC,EAAE,CAAC,CAAC;EAChC;AACF,CAAC;AAEDG,MAAM,CAACC,OAAO,GAAGf,QAAQ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\nimport { ScoreboardComponent } from './scoreboard/scoreboard.component';\nimport { ScreenOverlaysComponent } from './screen-overlays/screen-overlays.component';\nimport { SponsorsOverlayComponent } from './sponsors-overlay/sponsors-overlay.component';\nimport { SrollingTextComponent } from './srolling-text/srolling-text.component';\nimport { AdsVideoComponent } from './ads-video/ads-video.component';\nimport * as i0 from \"@angular/core\";\nconst routes = [{\n  path: 'scoreboard',\n  component: ScoreboardComponent\n}];\nexport class OverLaysModule {\n  static #_ = this.ɵfac = function OverLaysModule_Factory(t) {\n    return new (t || OverLaysModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: OverLaysModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, HostListenersModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OverLaysModule, {\n    declarations: [ScoreboardComponent, ScreenOverlaysComponent, SponsorsOverlayComponent, SrollingTextComponent, AdsVideoComponent],\n    imports: [CommonModule, HostListenersModule],\n    exports: [ScoreboardComponent, ScreenOverlaysComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;;AACnE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEP;CACZ,CACF;AAMD,OAAM,MAAOQ,cAAc;EAAA,QAAAC,CAAA;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA;UAAdF;EAAc;EAAA,QAAAG,EAAA;cAHfb,YAAY,EAAEC,mBAAmB;EAAA;;;2EAGhCS,cAAc;IAAAI,YAAA,GAJVZ,mBAAmB,EAAEC,uBAAuB,EAAEC,wBAAwB,EAAEC,qBAAqB,EAAEC,iBAAiB;IAAAS,OAAA,GACrHf,YAAY,EAAEC,mBAAmB;IAAAe,OAAA,GACjCd,mBAAmB,EAAEC,uBAAuB;EAAA;AAAA", "names": ["CommonModule", "HostListenersModule", "ScoreboardComponent", "ScreenOverlaysComponent", "SponsorsOverlayComponent", "SrollingTextComponent", "AdsVideoComponent", "routes", "path", "component", "OverLaysModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\overlays.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\r\nimport { ScoreboardComponent } from './scoreboard/scoreboard.component';\r\nimport { Routes } from '@angular/router';\r\nimport { ScreenOverlaysComponent } from './screen-overlays/screen-overlays.component';\r\nimport { SponsorsOverlayComponent } from './sponsors-overlay/sponsors-overlay.component';\r\nimport { SrollingTextComponent } from './srolling-text/srolling-text.component';\r\nimport { AdsVideoComponent } from './ads-video/ads-video.component';\r\nconst routes: Routes = [\r\n  {\r\n    path: 'scoreboard',\r\n    component: ScoreboardComponent,\r\n  },\r\n];\r\n@NgModule({\r\n  declarations: [ScoreboardComponent, ScreenOverlaysComponent, SponsorsOverlayComponent, SrollingTextComponent, AdsVideoComponent],\r\n  imports: [CommonModule, HostListenersModule],\r\n  exports: [ScoreboardComponent, ScreenOverlaysComponent],\r\n})\r\nexport class OverLaysModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
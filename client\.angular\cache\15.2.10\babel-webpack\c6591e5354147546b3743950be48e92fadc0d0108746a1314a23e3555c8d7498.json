{"ast": null, "code": "import { Injectable, EventEmitter, InjectionToken, Inject, Directive, ElementRef, ChangeDetectorRef, Input, Pipe, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.loader.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @abstract\n */\nimport * as ɵngcc0 from '@angular/core';\nclass TranslateLoader {}\nif (false) {\n  /**\n   * @abstract\n   * @param {?} lang\n   * @return {?}\n   */\n  TranslateLoader.prototype.getTranslation = function (lang) {};\n}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n  /**\n   * @param {?} lang\n   * @return {?}\n   */\n  getTranslation(lang) {\n    return of({});\n  }\n}\nTranslateFakeLoader.ɵfac = /*@__PURE__*/function () {\n  let ɵTranslateFakeLoader_BaseFactory;\n  return function TranslateFakeLoader_Factory(t) {\n    return (ɵTranslateFakeLoader_BaseFactory || (ɵTranslateFakeLoader_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(TranslateFakeLoader)))(t || TranslateFakeLoader);\n  };\n}();\nTranslateFakeLoader.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: TranslateFakeLoader,\n  factory: TranslateFakeLoader.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateFakeLoader, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/missing-translation-handler.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @record\n */\nfunction MissingTranslationHandlerParams() {}\nif (false) {\n  /**\n   * the key that's missing in translation files\n   * @type {?}\n   */\n  MissingTranslationHandlerParams.prototype.key;\n  /**\n   * an instance of the service that was unable to translate the key.\n   * @type {?}\n   */\n  MissingTranslationHandlerParams.prototype.translateService;\n  /**\n   * interpolation params that were passed along for translating the given key.\n   * @type {?|undefined}\n   */\n  MissingTranslationHandlerParams.prototype.interpolateParams;\n}\n/**\n * @abstract\n */\nclass MissingTranslationHandler {}\nif (false) {\n  /**\n   * A function that handles missing translations.\n   *\n   * @abstract\n   * @param {?} params context for resolving a missing translation\n   * @return {?} a value or an observable\n   * If it returns a value, then this value is used.\n   * If it return an observable, the value returned by this observable will be used (except if the method was \"instant\").\n   * If it doesn't return then the key will be used as a value\n   */\n  MissingTranslationHandler.prototype.handle = function (params) {};\n}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n  /**\n   * @param {?} params\n   * @return {?}\n   */\n  handle(params) {\n    return params.key;\n  }\n}\nFakeMissingTranslationHandler.ɵfac = function FakeMissingTranslationHandler_Factory(t) {\n  return new (t || FakeMissingTranslationHandler)();\n};\nFakeMissingTranslationHandler.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: FakeMissingTranslationHandler,\n  factory: FakeMissingTranslationHandler.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FakeMissingTranslationHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/util.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/* tslint:disable */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param {?} o1 Object or value to compare.\n * @param {?} o2 Object or value to compare.\n * @return {?} true if arguments are equal.\n */\nfunction equals(o1, o2) {\n  if (o1 === o2) return true;\n  if (o1 === null || o2 === null) return false;\n  if (o1 !== o1 && o2 !== o2) return true; // NaN === NaN\n  // NaN === NaN\n  /** @type {?} */\n  let t1 = typeof o1;\n  /** @type {?} */\n  let t2 = typeof o2;\n  /** @type {?} */\n  let length;\n  /** @type {?} */\n  let key;\n  /** @type {?} */\n  let keySet;\n  if (t1 == t2 && t1 == 'object') {\n    if (Array.isArray(o1)) {\n      if (!Array.isArray(o2)) return false;\n      if ((length = o1.length) == o2.length) {\n        for (key = 0; key < length; key++) {\n          if (!equals(o1[key], o2[key])) return false;\n        }\n        return true;\n      }\n    } else {\n      if (Array.isArray(o2)) {\n        return false;\n      }\n      keySet = Object.create(null);\n      for (key in o1) {\n        if (!equals(o1[key], o2[key])) {\n          return false;\n        }\n        keySet[key] = true;\n      }\n      for (key in o2) {\n        if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n}\n/* tslint:enable */\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isDefined(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\n/**\n * @param {?} item\n * @return {?}\n */\nfunction isObject(item) {\n  return item && typeof item === 'object' && !Array.isArray(item);\n}\n/**\n * @param {?} target\n * @param {?} source\n * @return {?}\n */\nfunction mergeDeep(target, source) {\n  /** @type {?} */\n  let output = Object.assign({}, target);\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(\n    /**\n    * @param {?} key\n    * @return {?}\n    */\n    key => {\n      if (isObject(source[key])) {\n        if (!(key in target)) {\n          Object.assign(output, {\n            [key]: source[key]\n          });\n        } else {\n          output[key] = mergeDeep(target[key], source[key]);\n        }\n      } else {\n        Object.assign(output, {\n          [key]: source[key]\n        });\n      }\n    });\n  }\n  return output;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.parser.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @abstract\n */\nclass TranslateParser {}\nif (false) {\n  /**\n   * Interpolates a string to replace parameters\n   * \"This is a {{ key }}\" ==> \"This is a value\", with params = { key: \"value\" }\n   * @abstract\n   * @param {?} expr\n   * @param {?=} params\n   * @return {?}\n   */\n  TranslateParser.prototype.interpolate = function (expr, params) {};\n  /**\n   * Gets a value from an object by composed key\n   * parser.getValue({ key1: { keyA: 'valueI' }}, 'key1.keyA') ==> 'valueI'\n   * @abstract\n   * @param {?} target\n   * @param {?} key\n   * @return {?}\n   */\n  TranslateParser.prototype.getValue = function (target, key) {};\n}\nclass TranslateDefaultParser extends TranslateParser {\n  constructor() {\n    super(...arguments);\n    this.templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n  }\n  /**\n   * @param {?} expr\n   * @param {?=} params\n   * @return {?}\n   */\n  interpolate(expr, params) {\n    /** @type {?} */\n    let result;\n    if (typeof expr === 'string') {\n      result = this.interpolateString(expr, params);\n    } else if (typeof expr === 'function') {\n      result = this.interpolateFunction(expr, params);\n    } else {\n      // this should not happen, but an unrelated TranslateService test depends on it\n      result = /** @type {?} */expr;\n    }\n    return result;\n  }\n  /**\n   * @param {?} target\n   * @param {?} key\n   * @return {?}\n   */\n  getValue(target, key) {\n    /** @type {?} */\n    let keys = typeof key === 'string' ? key.split('.') : [key];\n    key = '';\n    do {\n      key += keys.shift();\n      if (isDefined(target) && isDefined(target[key]) && (typeof target[key] === 'object' || !keys.length)) {\n        target = target[key];\n        key = '';\n      } else if (!keys.length) {\n        target = undefined;\n      } else {\n        key += '.';\n      }\n    } while (keys.length);\n    return target;\n  }\n  /**\n   * @private\n   * @param {?} fn\n   * @param {?=} params\n   * @return {?}\n   */\n  interpolateFunction(fn, params) {\n    return fn(params);\n  }\n  /**\n   * @private\n   * @param {?} expr\n   * @param {?=} params\n   * @return {?}\n   */\n  interpolateString(expr, params) {\n    if (!params) {\n      return expr;\n    }\n    return expr.replace(this.templateMatcher,\n    /**\n    * @param {?} substring\n    * @param {?} b\n    * @return {?}\n    */\n    (substring, b) => {\n      /** @type {?} */\n      let r = this.getValue(params, b);\n      return isDefined(r) ? r : substring;\n    });\n  }\n}\nTranslateDefaultParser.ɵfac = /*@__PURE__*/function () {\n  let ɵTranslateDefaultParser_BaseFactory;\n  return function TranslateDefaultParser_Factory(t) {\n    return (ɵTranslateDefaultParser_BaseFactory || (ɵTranslateDefaultParser_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(TranslateDefaultParser)))(t || TranslateDefaultParser);\n  };\n}();\nTranslateDefaultParser.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: TranslateDefaultParser,\n  factory: TranslateDefaultParser.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateDefaultParser, [{\n    type: Injectable\n  }], null, null);\n})();\nif (false) {\n  /** @type {?} */\n  TranslateDefaultParser.prototype.templateMatcher;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.compiler.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @abstract\n */\nclass TranslateCompiler {}\nif (false) {\n  /**\n   * @abstract\n   * @param {?} value\n   * @param {?} lang\n   * @return {?}\n   */\n  TranslateCompiler.prototype.compile = function (value, lang) {};\n  /**\n   * @abstract\n   * @param {?} translations\n   * @param {?} lang\n   * @return {?}\n   */\n  TranslateCompiler.prototype.compileTranslations = function (translations, lang) {};\n}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n  /**\n   * @param {?} value\n   * @param {?} lang\n   * @return {?}\n   */\n  compile(value, lang) {\n    return value;\n  }\n  /**\n   * @param {?} translations\n   * @param {?} lang\n   * @return {?}\n   */\n  compileTranslations(translations, lang) {\n    return translations;\n  }\n}\nTranslateFakeCompiler.ɵfac = /*@__PURE__*/function () {\n  let ɵTranslateFakeCompiler_BaseFactory;\n  return function TranslateFakeCompiler_Factory(t) {\n    return (ɵTranslateFakeCompiler_BaseFactory || (ɵTranslateFakeCompiler_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(TranslateFakeCompiler)))(t || TranslateFakeCompiler);\n  };\n}();\nTranslateFakeCompiler.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: TranslateFakeCompiler,\n  factory: TranslateFakeCompiler.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateFakeCompiler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.store.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TranslateStore {\n  constructor() {\n    /**\n     * The lang currently used\n     */\n    this.currentLang = this.defaultLang;\n    /**\n     * a list of translations per lang\n     */\n    this.translations = {};\n    /**\n     * an array of langs\n     */\n    this.langs = [];\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n     */\n    this.onTranslationChange = new EventEmitter();\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n     */\n    this.onLangChange = new EventEmitter();\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n     */\n    this.onDefaultLangChange = new EventEmitter();\n  }\n}\nif (false) {\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   * @type {?}\n   */\n  TranslateStore.prototype.defaultLang;\n  /**\n   * The lang currently used\n   * @type {?}\n   */\n  TranslateStore.prototype.currentLang;\n  /**\n   * a list of translations per lang\n   * @type {?}\n   */\n  TranslateStore.prototype.translations;\n  /**\n   * an array of langs\n   * @type {?}\n   */\n  TranslateStore.prototype.langs;\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n   *     // do something\n   * });\n   * @type {?}\n   */\n  TranslateStore.prototype.onTranslationChange;\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n   *     // do something\n   * });\n   * @type {?}\n   */\n  TranslateStore.prototype.onLangChange;\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n   *     // do something\n   * });\n   * @type {?}\n   */\n  TranslateStore.prototype.onDefaultLangChange;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.service.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst USE_STORE = new InjectionToken('USE_STORE');\n/** @type {?} */\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\n/** @type {?} */\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\n/** @type {?} */\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\n/**\n * @record\n */\nfunction TranslationChangeEvent() {}\nif (false) {\n  /** @type {?} */\n  TranslationChangeEvent.prototype.translations;\n  /** @type {?} */\n  TranslationChangeEvent.prototype.lang;\n}\n/**\n * @record\n */\nfunction LangChangeEvent() {}\nif (false) {\n  /** @type {?} */\n  LangChangeEvent.prototype.lang;\n  /** @type {?} */\n  LangChangeEvent.prototype.translations;\n}\n/**\n * @record\n */\nfunction DefaultLangChangeEvent() {}\nif (false) {\n  /** @type {?} */\n  DefaultLangChangeEvent.prototype.lang;\n  /** @type {?} */\n  DefaultLangChangeEvent.prototype.translations;\n}\nclass TranslateService {\n  /**\n   *\n   * @param {?} store an instance of the store (that is supposed to be unique)\n   * @param {?} currentLoader An instance of the loader currently used\n   * @param {?} compiler An instance of the compiler currently used\n   * @param {?} parser An instance of the parser currently used\n   * @param {?} missingTranslationHandler A handler for missing translations.\n   * @param {?=} useDefaultLang whether we should use default language translation when current language translation is missing.\n   * @param {?=} isolate whether this service should use the store or not\n   * @param {?=} extend To make a child module extend (and use) translations from parent modules.\n   * @param {?=} defaultLanguage Set the default language using configuration\n   */\n  constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n    this.store = store;\n    this.currentLoader = currentLoader;\n    this.compiler = compiler;\n    this.parser = parser;\n    this.missingTranslationHandler = missingTranslationHandler;\n    this.useDefaultLang = useDefaultLang;\n    this.isolate = isolate;\n    this.extend = extend;\n    this.pending = false;\n    this._onTranslationChange = new EventEmitter();\n    this._onLangChange = new EventEmitter();\n    this._onDefaultLangChange = new EventEmitter();\n    this._langs = [];\n    this._translations = {};\n    this._translationRequests = {};\n    /** set the default language from configuration */\n    if (defaultLanguage) {\n      this.setDefaultLang(defaultLanguage);\n    }\n  }\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n   *     // do something\n   * });\n   * @return {?}\n   */\n  get onTranslationChange() {\n    return this.isolate ? this._onTranslationChange : this.store.onTranslationChange;\n  }\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n   *     // do something\n   * });\n   * @return {?}\n   */\n  get onLangChange() {\n    return this.isolate ? this._onLangChange : this.store.onLangChange;\n  }\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n   *     // do something\n   * });\n   * @return {?}\n   */\n  get onDefaultLangChange() {\n    return this.isolate ? this._onDefaultLangChange : this.store.onDefaultLangChange;\n  }\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   * @return {?}\n   */\n  get defaultLang() {\n    return this.isolate ? this._defaultLang : this.store.defaultLang;\n  }\n  /**\n   * @param {?} defaultLang\n   * @return {?}\n   */\n  set defaultLang(defaultLang) {\n    if (this.isolate) {\n      this._defaultLang = defaultLang;\n    } else {\n      this.store.defaultLang = defaultLang;\n    }\n  }\n  /**\n   * The lang currently used\n   * @return {?}\n   */\n  get currentLang() {\n    return this.isolate ? this._currentLang : this.store.currentLang;\n  }\n  /**\n   * @param {?} currentLang\n   * @return {?}\n   */\n  set currentLang(currentLang) {\n    if (this.isolate) {\n      this._currentLang = currentLang;\n    } else {\n      this.store.currentLang = currentLang;\n    }\n  }\n  /**\n   * an array of langs\n   * @return {?}\n   */\n  get langs() {\n    return this.isolate ? this._langs : this.store.langs;\n  }\n  /**\n   * @param {?} langs\n   * @return {?}\n   */\n  set langs(langs) {\n    if (this.isolate) {\n      this._langs = langs;\n    } else {\n      this.store.langs = langs;\n    }\n  }\n  /**\n   * a list of translations per lang\n   * @return {?}\n   */\n  get translations() {\n    return this.isolate ? this._translations : this.store.translations;\n  }\n  /**\n   * @param {?} translations\n   * @return {?}\n   */\n  set translations(translations) {\n    if (this.isolate) {\n      this._translations = translations;\n    } else {\n      this.store.translations = translations;\n    }\n  }\n  /**\n   * Sets the default language to use as a fallback\n   * @param {?} lang\n   * @return {?}\n   */\n  setDefaultLang(lang) {\n    if (lang === this.defaultLang) {\n      return;\n    }\n    /** @type {?} */\n    let pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the defaultLang immediately\n      if (this.defaultLang == null) {\n        this.defaultLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(\n      /**\n      * @param {?} res\n      * @return {?}\n      */\n      res => {\n        this.changeDefaultLang(lang);\n      });\n    } else {\n      // we already have this language\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Gets the default language used\n   * @return {?}\n   */\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  /**\n   * Changes the lang currently used\n   * @param {?} lang\n   * @return {?}\n   */\n  use(lang) {\n    // don't change the language if the language given is already selected\n    if (lang === this.currentLang) {\n      return of(this.translations[lang]);\n    }\n    /** @type {?} */\n    let pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the currentLang immediately\n      if (!this.currentLang) {\n        this.currentLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(\n      /**\n      * @param {?} res\n      * @return {?}\n      */\n      res => {\n        this.changeLang(lang);\n      });\n      return pending;\n    } else {\n      // we have this language, return an Observable\n      this.changeLang(lang);\n      return of(this.translations[lang]);\n    }\n  }\n  /**\n   * Retrieves the given translations\n   * @private\n   * @param {?} lang\n   * @return {?}\n   */\n  retrieveTranslations(lang) {\n    /** @type {?} */\n    let pending;\n    // if this language is unavailable or extend is true, ask for it\n    if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n      this._translationRequests[lang] = this._translationRequests[lang] || this.getTranslation(lang);\n      pending = this._translationRequests[lang];\n    }\n    return pending;\n  }\n  /**\n   * Gets an object of translations for a given language with the current loader\n   * and passes it through the compiler\n   * @param {?} lang\n   * @return {?}\n   */\n  getTranslation(lang) {\n    this.pending = true;\n    /** @type {?} */\n    const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n    this.loadingTranslations = loadingTranslations.pipe(map(\n    /**\n    * @param {?} res\n    * @return {?}\n    */\n    res => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n    this.loadingTranslations.subscribe({\n      next:\n      /**\n      * @param {?} res\n      * @return {?}\n      */\n      res => {\n        this.translations[lang] = this.extend && this.translations[lang] ? Object.assign(Object.assign({}, res), this.translations[lang]) : res;\n        this.updateLangs();\n        this.pending = false;\n      },\n      error:\n      /**\n      * @param {?} err\n      * @return {?}\n      */\n      err => {\n        this.pending = false;\n      }\n    });\n    return loadingTranslations;\n  }\n  /**\n   * Manually sets an object of translations for a given language\n   * after passing it through the compiler\n   * @param {?} lang\n   * @param {?} translations\n   * @param {?=} shouldMerge\n   * @return {?}\n   */\n  setTranslation(lang, translations, shouldMerge = false) {\n    translations = this.compiler.compileTranslations(translations, lang);\n    if ((shouldMerge || this.extend) && this.translations[lang]) {\n      this.translations[lang] = mergeDeep(this.translations[lang], translations);\n    } else {\n      this.translations[lang] = translations;\n    }\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Returns an array of currently available langs\n   * @return {?}\n   */\n  getLangs() {\n    return this.langs;\n  }\n  /**\n   * Add available langs\n   * @param {?} langs\n   * @return {?}\n   */\n  addLangs(langs) {\n    langs.forEach(\n    /**\n    * @param {?} lang\n    * @return {?}\n    */\n    lang => {\n      if (this.langs.indexOf(lang) === -1) {\n        this.langs.push(lang);\n      }\n    });\n  }\n  /**\n   * Update the list of available langs\n   * @private\n   * @return {?}\n   */\n  updateLangs() {\n    this.addLangs(Object.keys(this.translations));\n  }\n  /**\n   * Returns the parsed result of the translations\n   * @param {?} translations\n   * @param {?} key\n   * @param {?=} interpolateParams\n   * @return {?}\n   */\n  getParsedResult(translations, key, interpolateParams) {\n    /** @type {?} */\n    let res;\n    if (key instanceof Array) {\n      /** @type {?} */\n      let result = {};\n      /** @type {?} */\n      let observables = false;\n      for (let k of key) {\n        result[k] = this.getParsedResult(translations, k, interpolateParams);\n        if (isObservable(result[k])) {\n          observables = true;\n        }\n      }\n      if (observables) {\n        /** @type {?} */\n        const sources = key.map(\n        /**\n        * @param {?} k\n        * @return {?}\n        */\n        k => isObservable(result[k]) ? result[k] : of( /** @type {?} */result[k]));\n        return forkJoin(sources).pipe(map(\n        /**\n        * @param {?} arr\n        * @return {?}\n        */\n        arr => {\n          /** @type {?} */\n          let obj = {};\n          arr.forEach(\n          /**\n          * @param {?} value\n          * @param {?} index\n          * @return {?}\n          */\n          (value, index) => {\n            obj[key[index]] = value;\n          });\n          return obj;\n        }));\n      }\n      return result;\n    }\n    if (translations) {\n      res = this.parser.interpolate(this.parser.getValue(translations, key), interpolateParams);\n    }\n    if (typeof res === \"undefined\" && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n      res = this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang], key), interpolateParams);\n    }\n    if (typeof res === \"undefined\") {\n      /** @type {?} */\n      let params = {\n        key,\n        translateService: this\n      };\n      if (typeof interpolateParams !== 'undefined') {\n        params.interpolateParams = interpolateParams;\n      }\n      res = this.missingTranslationHandler.handle(params);\n    }\n    return typeof res !== \"undefined\" ? res : key;\n  }\n  /**\n   * Gets the translated value of a key (or an array of keys)\n   * @param {?} key\n   * @param {?=} interpolateParams\n   * @return {?} the translated key, or an object of translated keys\n   */\n  get(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    // check if we are loading a new translation to use\n    if (this.pending) {\n      return this.loadingTranslations.pipe(concatMap(\n      /**\n      * @param {?} res\n      * @return {?}\n      */\n      res => {\n        res = this.getParsedResult(res, key, interpolateParams);\n        return isObservable(res) ? res : of(res);\n      }));\n    } else {\n      /** @type {?} */\n      let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n      return isObservable(res) ? res : of(res);\n    }\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the translation changes.\n   * @param {?} key\n   * @param {?=} interpolateParams\n   * @return {?} A stream of the translated key, or an object of translated keys\n   */\n  getStreamOnTranslationChange(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(\n    /**\n    * @return {?}\n    */\n    () => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap(\n    /**\n    * @param {?} event\n    * @return {?}\n    */\n    event => {\n      /** @type {?} */\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      if (typeof res.subscribe === 'function') {\n        return res;\n      } else {\n        return of(res);\n      }\n    })));\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the language changes.\n   * @param {?} key\n   * @param {?=} interpolateParams\n   * @return {?} A stream of the translated key, or an object of translated keys\n   */\n  stream(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(\n    /**\n    * @return {?}\n    */\n    () => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap(\n    /**\n    * @param {?} event\n    * @return {?}\n    */\n    event => {\n      /** @type {?} */\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return isObservable(res) ? res : of(res);\n    })));\n  }\n  /**\n   * Returns a translation instantly from the internal state of loaded translation.\n   * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.\n   * @param {?} key\n   * @param {?=} interpolateParams\n   * @return {?}\n   */\n  instant(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    /** @type {?} */\n    let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n    if (isObservable(res)) {\n      if (key instanceof Array) {\n        /** @type {?} */\n        let obj = {};\n        key.forEach(\n        /**\n        * @param {?} value\n        * @param {?} index\n        * @return {?}\n        */\n        (value, index) => {\n          obj[key[index]] = key[index];\n        });\n        return obj;\n      }\n      return key;\n    } else {\n      return res;\n    }\n  }\n  /**\n   * Sets the translated value of a key, after compiling it\n   * @param {?} key\n   * @param {?} value\n   * @param {?=} lang\n   * @return {?}\n   */\n  set(key, value, lang = this.currentLang) {\n    this.translations[lang][key] = this.compiler.compile(value, lang);\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Changes the current lang\n   * @private\n   * @param {?} lang\n   * @return {?}\n   */\n  changeLang(lang) {\n    this.currentLang = lang;\n    this.onLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n    // if there is no default lang, use the one that we just set\n    if (this.defaultLang == null) {\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Changes the default lang\n   * @private\n   * @param {?} lang\n   * @return {?}\n   */\n  changeDefaultLang(lang) {\n    this.defaultLang = lang;\n    this.onDefaultLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Allows to reload the lang file from the file\n   * @param {?} lang\n   * @return {?}\n   */\n  reloadLang(lang) {\n    this.resetLang(lang);\n    return this.getTranslation(lang);\n  }\n  /**\n   * Deletes inner translation\n   * @param {?} lang\n   * @return {?}\n   */\n  resetLang(lang) {\n    this._translationRequests[lang] = undefined;\n    this.translations[lang] = undefined;\n  }\n  /**\n   * Returns the language code name from the browser, e.g. \"de\"\n   * @return {?}\n   */\n  getBrowserLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    /** @type {?} */\n    let browserLang = window.navigator.languages ? window.navigator.languages[0] : null;\n    browserLang = browserLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n    if (typeof browserLang === 'undefined') {\n      return undefined;\n    }\n    if (browserLang.indexOf('-') !== -1) {\n      browserLang = browserLang.split('-')[0];\n    }\n    if (browserLang.indexOf('_') !== -1) {\n      browserLang = browserLang.split('_')[0];\n    }\n    return browserLang;\n  }\n  /**\n   * Returns the culture language code name from the browser, e.g. \"de-DE\"\n   * @return {?}\n   */\n  getBrowserCultureLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    /** @type {?} */\n    let browserCultureLang = window.navigator.languages ? window.navigator.languages[0] : null;\n    browserCultureLang = browserCultureLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n    return browserCultureLang;\n  }\n}\nTranslateService.ɵfac = function TranslateService_Factory(t) {\n  return new (t || TranslateService)(ɵngcc0.ɵɵinject(TranslateStore), ɵngcc0.ɵɵinject(TranslateLoader), ɵngcc0.ɵɵinject(TranslateCompiler), ɵngcc0.ɵɵinject(TranslateParser), ɵngcc0.ɵɵinject(MissingTranslationHandler), ɵngcc0.ɵɵinject(USE_DEFAULT_LANG), ɵngcc0.ɵɵinject(USE_STORE), ɵngcc0.ɵɵinject(USE_EXTEND), ɵngcc0.ɵɵinject(DEFAULT_LANGUAGE));\n};\nTranslateService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: TranslateService,\n  factory: TranslateService.ɵfac\n});\n/** @nocollapse */\nTranslateService.ctorParameters = () => [{\n  type: TranslateStore\n}, {\n  type: TranslateLoader\n}, {\n  type: TranslateCompiler\n}, {\n  type: TranslateParser\n}, {\n  type: MissingTranslationHandler\n}, {\n  type: Boolean,\n  decorators: [{\n    type: Inject,\n    args: [USE_DEFAULT_LANG]\n  }]\n}, {\n  type: Boolean,\n  decorators: [{\n    type: Inject,\n    args: [USE_STORE]\n  }]\n}, {\n  type: Boolean,\n  decorators: [{\n    type: Inject,\n    args: [USE_EXTEND]\n  }]\n}, {\n  type: String,\n  decorators: [{\n    type: Inject,\n    args: [DEFAULT_LANGUAGE]\n  }]\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: TranslateStore\n    }, {\n      type: TranslateLoader\n    }, {\n      type: TranslateCompiler\n    }, {\n      type: TranslateParser\n    }, {\n      type: MissingTranslationHandler\n    }, {\n      type: Boolean,\n      decorators: [{\n        type: Inject,\n        args: [USE_DEFAULT_LANG]\n      }]\n    }, {\n      type: Boolean,\n      decorators: [{\n        type: Inject,\n        args: [USE_STORE]\n      }]\n    }, {\n      type: Boolean,\n      decorators: [{\n        type: Inject,\n        args: [USE_EXTEND]\n      }]\n    }, {\n      type: String,\n      decorators: [{\n        type: Inject,\n        args: [DEFAULT_LANGUAGE]\n      }]\n    }];\n  }, null);\n})();\nif (false) {\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype.loadingTranslations;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype.pending;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._onTranslationChange;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._onLangChange;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._onDefaultLangChange;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._defaultLang;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._currentLang;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._langs;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._translations;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype._translationRequests;\n  /** @type {?} */\n  TranslateService.prototype.store;\n  /** @type {?} */\n  TranslateService.prototype.currentLoader;\n  /** @type {?} */\n  TranslateService.prototype.compiler;\n  /** @type {?} */\n  TranslateService.prototype.parser;\n  /** @type {?} */\n  TranslateService.prototype.missingTranslationHandler;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype.useDefaultLang;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype.isolate;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateService.prototype.extend;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.directive.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TranslateDirective {\n  /**\n   * @param {?} translateService\n   * @param {?} element\n   * @param {?} _ref\n   */\n  constructor(translateService, element, _ref) {\n    this.translateService = translateService;\n    this.element = element;\n    this._ref = _ref;\n    // subscribe to onTranslationChange event, in case the translations of the current lang change\n    if (!this.onTranslationChangeSub) {\n      this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe(\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        if (event.lang === this.translateService.currentLang) {\n          this.checkNodes(true, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChangeSub) {\n      this.onLangChangeSub = this.translateService.onLangChange.subscribe(\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        this.checkNodes(true, event.translations);\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe(\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        this.checkNodes(true);\n      });\n    }\n  }\n  /**\n   * @param {?} key\n   * @return {?}\n   */\n  set translate(key) {\n    if (key) {\n      this.key = key;\n      this.checkNodes();\n    }\n  }\n  /**\n   * @param {?} params\n   * @return {?}\n   */\n  set translateParams(params) {\n    if (!equals(this.currentParams, params)) {\n      this.currentParams = params;\n      this.checkNodes(true);\n    }\n  }\n  /**\n   * @return {?}\n   */\n  ngAfterViewChecked() {\n    this.checkNodes();\n  }\n  /**\n   * @param {?=} forceUpdate\n   * @param {?=} translations\n   * @return {?}\n   */\n  checkNodes(forceUpdate = false, translations) {\n    /** @type {?} */\n    let nodes = this.element.nativeElement.childNodes;\n    // if the element is empty\n    if (!nodes.length) {\n      // we add the key as content\n      this.setContent(this.element.nativeElement, this.key);\n      nodes = this.element.nativeElement.childNodes;\n    }\n    for (let i = 0; i < nodes.length; ++i) {\n      /** @type {?} */\n      let node = nodes[i];\n      if (node.nodeType === 3) {\n        // node type 3 is a text node\n        // node type 3 is a text node\n        /** @type {?} */\n        let key;\n        if (forceUpdate) {\n          node.lastKey = null;\n        }\n        if (isDefined(node.lookupKey)) {\n          key = node.lookupKey;\n        } else if (this.key) {\n          key = this.key;\n        } else {\n          /** @type {?} */\n          let content = this.getContent(node);\n          /** @type {?} */\n          let trimmedContent = content.trim();\n          if (trimmedContent.length) {\n            node.lookupKey = trimmedContent;\n            // we want to use the content as a key, not the translation value\n            if (content !== node.currentValue) {\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            } else if (node.originalContent) {\n              // the content seems ok, but the lang has changed\n              // the current content is the translation, not the key, use the last real content as key\n              key = node.originalContent.trim();\n            } else if (content !== node.currentValue) {\n              // we want to use the content as a key, not the translation value\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            }\n          }\n        }\n        this.updateValue(key, node, translations);\n      }\n    }\n  }\n  /**\n   * @param {?} key\n   * @param {?} node\n   * @param {?} translations\n   * @return {?}\n   */\n  updateValue(key, node, translations) {\n    if (key) {\n      if (node.lastKey === key && this.lastParams === this.currentParams) {\n        return;\n      }\n      this.lastParams = this.currentParams;\n      /** @type {?} */\n      let onTranslation =\n      /**\n      * @param {?} res\n      * @return {?}\n      */\n      res => {\n        if (res !== key) {\n          node.lastKey = key;\n        }\n        if (!node.originalContent) {\n          node.originalContent = this.getContent(node);\n        }\n        node.currentValue = isDefined(res) ? res : node.originalContent || key;\n        // we replace in the original content to preserve spaces that we might have trimmed\n        this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n        this._ref.markForCheck();\n      };\n      if (isDefined(translations)) {\n        /** @type {?} */\n        let res = this.translateService.getParsedResult(translations, key, this.currentParams);\n        if (isObservable(res)) {\n          res.subscribe(onTranslation);\n        } else {\n          onTranslation(res);\n        }\n      } else {\n        this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n      }\n    }\n  }\n  /**\n   * @param {?} node\n   * @return {?}\n   */\n  getContent(node) {\n    return isDefined(node.textContent) ? node.textContent : node.data;\n  }\n  /**\n   * @param {?} node\n   * @param {?} content\n   * @return {?}\n   */\n  setContent(node, content) {\n    if (isDefined(node.textContent)) {\n      node.textContent = content;\n    } else {\n      node.data = content;\n    }\n  }\n  /**\n   * @return {?}\n   */\n  ngOnDestroy() {\n    if (this.onLangChangeSub) {\n      this.onLangChangeSub.unsubscribe();\n    }\n    if (this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub.unsubscribe();\n    }\n    if (this.onTranslationChangeSub) {\n      this.onTranslationChangeSub.unsubscribe();\n    }\n  }\n}\nTranslateDirective.ɵfac = function TranslateDirective_Factory(t) {\n  return new (t || TranslateDirective)(ɵngcc0.ɵɵdirectiveInject(TranslateService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef));\n};\nTranslateDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: TranslateDirective,\n  selectors: [[\"\", \"translate\", \"\"], [\"\", \"ngx-translate\", \"\"]],\n  inputs: {\n    translate: \"translate\",\n    translateParams: \"translateParams\"\n  }\n});\n/** @nocollapse */\nTranslateDirective.ctorParameters = () => [{\n  type: TranslateService\n}, {\n  type: ElementRef\n}, {\n  type: ChangeDetectorRef\n}];\nTranslateDirective.propDecorators = {\n  translate: [{\n    type: Input\n  }],\n  translateParams: [{\n    type: Input\n  }]\n};\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[translate],[ngx-translate]'\n    }]\n  }], function () {\n    return [{\n      type: TranslateService\n    }, {\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc0.ChangeDetectorRef\n    }];\n  }, {\n    translate: [{\n      type: Input\n    }],\n    translateParams: [{\n      type: Input\n    }]\n  });\n})();\nif (false) {\n  /** @type {?} */\n  TranslateDirective.prototype.key;\n  /** @type {?} */\n  TranslateDirective.prototype.lastParams;\n  /** @type {?} */\n  TranslateDirective.prototype.currentParams;\n  /** @type {?} */\n  TranslateDirective.prototype.onLangChangeSub;\n  /** @type {?} */\n  TranslateDirective.prototype.onDefaultLangChangeSub;\n  /** @type {?} */\n  TranslateDirective.prototype.onTranslationChangeSub;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateDirective.prototype.translateService;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateDirective.prototype.element;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslateDirective.prototype._ref;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.pipe.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TranslatePipe {\n  /**\n   * @param {?} translate\n   * @param {?} _ref\n   */\n  constructor(translate, _ref) {\n    this.translate = translate;\n    this._ref = _ref;\n    this.value = '';\n  }\n  /**\n   * @param {?} key\n   * @param {?=} interpolateParams\n   * @param {?=} translations\n   * @return {?}\n   */\n  updateValue(key, interpolateParams, translations) {\n    /** @type {?} */\n    let onTranslation =\n    /**\n    * @param {?} res\n    * @return {?}\n    */\n    res => {\n      this.value = res !== undefined ? res : key;\n      this.lastKey = key;\n      this._ref.markForCheck();\n    };\n    if (translations) {\n      /** @type {?} */\n      let res = this.translate.getParsedResult(translations, key, interpolateParams);\n      if (isObservable(res.subscribe)) {\n        res.subscribe(onTranslation);\n      } else {\n        onTranslation(res);\n      }\n    }\n    this.translate.get(key, interpolateParams).subscribe(onTranslation);\n  }\n  /**\n   * @param {?} query\n   * @param {...?} args\n   * @return {?}\n   */\n  transform(query, ...args) {\n    if (!query || !query.length) {\n      return query;\n    }\n    // if we ask another time for the same key, return the last value\n    if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n      return this.value;\n    }\n    /** @type {?} */\n    let interpolateParams;\n    if (isDefined(args[0]) && args.length) {\n      if (typeof args[0] === 'string' && args[0].length) {\n        // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n        // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n        /** @type {?} */\n        let validArgs = args[0].replace(/(\\')?([a-zA-Z0-9_]+)(\\')?(\\s)?:/g, '\"$2\":').replace(/:(\\s)?(\\')(.*?)(\\')/g, ':\"$3\"');\n        try {\n          interpolateParams = JSON.parse(validArgs);\n        } catch (e) {\n          throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n        }\n      } else if (typeof args[0] === 'object' && !Array.isArray(args[0])) {\n        interpolateParams = args[0];\n      }\n    }\n    // store the query, in case it changes\n    this.lastKey = query;\n    // store the params, in case they change\n    this.lastParams = args;\n    // set the value\n    this.updateValue(query, interpolateParams);\n    // if there is a subscription to onLangChange, clean it\n    this._dispose();\n    // subscribe to onTranslationChange event, in case the translations change\n    if (!this.onTranslationChange) {\n      this.onTranslationChange = this.translate.onTranslationChange.subscribe(\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        if (this.lastKey && event.lang === this.translate.currentLang) {\n          this.lastKey = null;\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChange) {\n      this.onLangChange = this.translate.onLangChange.subscribe(\n      /**\n      * @param {?} event\n      * @return {?}\n      */\n      event => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChange) {\n      this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(\n      /**\n      * @return {?}\n      */\n      () => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams);\n        }\n      });\n    }\n    return this.value;\n  }\n  /**\n   * Clean any existing subscription to change events\n   * @private\n   * @return {?}\n   */\n  _dispose() {\n    if (typeof this.onTranslationChange !== 'undefined') {\n      this.onTranslationChange.unsubscribe();\n      this.onTranslationChange = undefined;\n    }\n    if (typeof this.onLangChange !== 'undefined') {\n      this.onLangChange.unsubscribe();\n      this.onLangChange = undefined;\n    }\n    if (typeof this.onDefaultLangChange !== 'undefined') {\n      this.onDefaultLangChange.unsubscribe();\n      this.onDefaultLangChange = undefined;\n    }\n  }\n  /**\n   * @return {?}\n   */\n  ngOnDestroy() {\n    this._dispose();\n  }\n}\nTranslatePipe.ɵfac = function TranslatePipe_Factory(t) {\n  return new (t || TranslatePipe)(ɵngcc0.ɵɵdirectiveInject(TranslateService, 16), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef, 16));\n};\nTranslatePipe.ɵpipe = /*@__PURE__*/ɵngcc0.ɵɵdefinePipe({\n  name: \"translate\",\n  type: TranslatePipe,\n  pure: false\n});\nTranslatePipe.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: TranslatePipe,\n  factory: TranslatePipe.ɵfac\n});\n/** @nocollapse */\nTranslatePipe.ctorParameters = () => [{\n  type: TranslateService\n}, {\n  type: ChangeDetectorRef\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslatePipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'translate',\n      pure: false // required to update the value when the promise is resolved\n    }]\n  }], function () {\n    return [{\n      type: TranslateService\n    }, {\n      type: ɵngcc0.ChangeDetectorRef\n    }];\n  }, null);\n})();\nif (false) {\n  /** @type {?} */\n  TranslatePipe.prototype.value;\n  /** @type {?} */\n  TranslatePipe.prototype.lastKey;\n  /** @type {?} */\n  TranslatePipe.prototype.lastParams;\n  /** @type {?} */\n  TranslatePipe.prototype.onTranslationChange;\n  /** @type {?} */\n  TranslatePipe.prototype.onLangChange;\n  /** @type {?} */\n  TranslatePipe.prototype.onDefaultLangChange;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslatePipe.prototype.translate;\n  /**\n   * @type {?}\n   * @private\n   */\n  TranslatePipe.prototype._ref;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: public_api.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @record\n */\nfunction TranslateModuleConfig() {}\nif (false) {\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.loader;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.compiler;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.parser;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.missingTranslationHandler;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.isolate;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.extend;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.useDefaultLang;\n  /** @type {?|undefined} */\n  TranslateModuleConfig.prototype.defaultLanguage;\n}\nclass TranslateModule {\n  /**\n   * Use this method in your root module to provide the TranslateService\n   * @param {?=} config\n   * @return {?}\n   */\n  static forRoot(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, TranslateStore, {\n        provide: USE_STORE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  /**\n   * Use this method in your other (non root) modules to import the directive/pipe\n   * @param {?=} config\n   * @return {?}\n   */\n  static forChild(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, {\n        provide: USE_STORE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n}\nTranslateModule.ɵfac = function TranslateModule_Factory(t) {\n  return new (t || TranslateModule)();\n};\nTranslateModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: TranslateModule\n});\nTranslateModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TranslatePipe, TranslateDirective],\n      exports: [TranslatePipe, TranslateDirective]\n    }]\n  }], null, null);\n})();\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(TranslateModule, {\n    declarations: [TranslatePipe, TranslateDirective],\n    exports: [TranslatePipe, TranslateDirective]\n  });\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: ngx-translate-core.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE };", "map": {"version": 3, "names": ["Injectable", "EventEmitter", "InjectionToken", "Inject", "Directive", "ElementRef", "ChangeDetectorRef", "Input", "<PERSON><PERSON>", "NgModule", "of", "isObservable", "fork<PERSON><PERSON>n", "concat", "defer", "take", "shareReplay", "map", "concatMap", "switchMap", "ɵngcc0", "Translate<PERSON><PERSON><PERSON>", "prototype", "getTranslation", "lang", "TranslateFakeLoader", "ɵfac", "ɵTranslateFakeLoader_BaseFactory", "TranslateFakeLoader_Factory", "t", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "MissingTranslationHandlerParams", "key", "translateService", "interpolateParams", "MissingTranslationHandler", "handle", "params", "FakeMissingTranslationHandler", "FakeMissingTranslationHandler_Factory", "equals", "o1", "o2", "t1", "t2", "length", "keySet", "Array", "isArray", "Object", "create", "isDefined", "value", "isObject", "item", "mergeDeep", "target", "source", "output", "assign", "keys", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interpolate", "expr", "getValue", "TranslateDefault<PERSON><PERSON><PERSON>", "constructor", "arguments", "templateMatcher", "result", "interpolateString", "interpolateFunction", "split", "shift", "undefined", "fn", "replace", "substring", "b", "r", "ɵTranslateDefaultParser_BaseFactory", "TranslateDefaultParser_Factory", "TranslateCompiler", "compile", "compileTranslations", "translations", "TranslateFakeCompiler", "ɵTranslateFakeCompiler_BaseFactory", "TranslateFakeCompiler_Factory", "TranslateStore", "currentLang", "defaultLang", "langs", "onTranslationChange", "onLangChange", "onDefaultLangChange", "USE_STORE", "USE_DEFAULT_LANG", "DEFAULT_LANGUAGE", "USE_EXTEND", "TranslationChangeEvent", "LangChangeEvent", "DefaultLangChangeEvent", "TranslateService", "store", "<PERSON><PERSON><PERSON><PERSON>", "compiler", "parser", "missingTranslation<PERSON><PERSON><PERSON>", "useDefaultLang", "isolate", "extend", "defaultLanguage", "pending", "_onTranslationChange", "_onLangChange", "_onDefaultLangChange", "_langs", "_translations", "_translationRequests", "setDefaultLang", "_defaultLang", "_currentLang", "retrieveTranslations", "pipe", "subscribe", "res", "changeDefaultLang", "getDefaultLang", "use", "changeLang", "loadingTranslations", "next", "updateLangs", "error", "err", "setTranslation", "shouldMerge", "emit", "get<PERSON>angs", "addLangs", "indexOf", "push", "getParsedResult", "observables", "k", "sources", "arr", "obj", "index", "get", "Error", "getStreamOnTranslationChange", "event", "stream", "instant", "set", "reloadLang", "resetLang", "getBrowserLang", "window", "navigator", "browserLang", "languages", "language", "browserLanguage", "userLanguage", "getBrowserCultureLang", "browserCultureLang", "TranslateService_Factory", "ɵɵinject", "ctorParameters", "Boolean", "decorators", "args", "String", "TranslateDirective", "element", "_ref", "onTranslationChangeSub", "checkNodes", "onLangChangeSub", "onDefaultLangChangeSub", "translate", "translateParams", "currentParams", "ngAfterViewChecked", "forceUpdate", "nodes", "nativeElement", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "i", "node", "nodeType", "last<PERSON>ey", "lookup<PERSON><PERSON>", "content", "get<PERSON>ontent", "<PERSON><PERSON><PERSON>nt", "trim", "currentValue", "originalContent", "updateValue", "lastParams", "onTranslation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "data", "ngOnDestroy", "unsubscribe", "TranslateDirective_Factory", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "propDecorators", "selector", "TranslatePipe", "transform", "query", "validArgs", "JSON", "parse", "e", "SyntaxError", "_dispose", "TranslatePipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "TranslateModuleConfig", "loader", "TranslateModule", "forRoot", "config", "ngModule", "providers", "provide", "useClass", "useValue", "<PERSON><PERSON><PERSON><PERSON>", "TranslateModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports", "ngJitMode", "ɵɵsetNgModuleScope"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@ngx-translate/core/__ivy_ngcc__/fesm2015/ngx-translate-core.js"], "sourcesContent": ["import { Injectable, EventEmitter, InjectionToken, Inject, Directive, ElementRef, ChangeDetectorRef, Input, Pipe, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.loader.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @abstract\n */\nimport * as ɵngcc0 from '@angular/core';\nclass TranslateLoader {\n}\nif (false) {\n    /**\n     * @abstract\n     * @param {?} lang\n     * @return {?}\n     */\n    TranslateLoader.prototype.getTranslation = function (lang) { };\n}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n    /**\n     * @param {?} lang\n     * @return {?}\n     */\n    getTranslation(lang) {\n        return of({});\n    }\n}\nTranslateFakeLoader.ɵfac = /*@__PURE__*/ function () { let ɵTranslateFakeLoader_BaseFactory; return function TranslateFakeLoader_Factory(t) { return (ɵTranslateFakeLoader_BaseFactory || (ɵTranslateFakeLoader_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(TranslateFakeLoader)))(t || TranslateFakeLoader); }; }();\nTranslateFakeLoader.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: TranslateFakeLoader, factory: TranslateFakeLoader.ɵfac });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateFakeLoader, [{\n        type: Injectable\n    }], null, null); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/missing-translation-handler.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @record\n */\nfunction MissingTranslationHandlerParams() { }\nif (false) {\n    /**\n     * the key that's missing in translation files\n     * @type {?}\n     */\n    MissingTranslationHandlerParams.prototype.key;\n    /**\n     * an instance of the service that was unable to translate the key.\n     * @type {?}\n     */\n    MissingTranslationHandlerParams.prototype.translateService;\n    /**\n     * interpolation params that were passed along for translating the given key.\n     * @type {?|undefined}\n     */\n    MissingTranslationHandlerParams.prototype.interpolateParams;\n}\n/**\n * @abstract\n */\nclass MissingTranslationHandler {\n}\nif (false) {\n    /**\n     * A function that handles missing translations.\n     *\n     * @abstract\n     * @param {?} params context for resolving a missing translation\n     * @return {?} a value or an observable\n     * If it returns a value, then this value is used.\n     * If it return an observable, the value returned by this observable will be used (except if the method was \"instant\").\n     * If it doesn't return then the key will be used as a value\n     */\n    MissingTranslationHandler.prototype.handle = function (params) { };\n}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n    /**\n     * @param {?} params\n     * @return {?}\n     */\n    handle(params) {\n        return params.key;\n    }\n}\nFakeMissingTranslationHandler.ɵfac = function FakeMissingTranslationHandler_Factory(t) { return new (t || FakeMissingTranslationHandler)(); };\nFakeMissingTranslationHandler.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: FakeMissingTranslationHandler, factory: FakeMissingTranslationHandler.ɵfac });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FakeMissingTranslationHandler, [{\n        type: Injectable\n    }], null, null); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/util.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/* tslint:disable */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param {?} o1 Object or value to compare.\n * @param {?} o2 Object or value to compare.\n * @return {?} true if arguments are equal.\n */\nfunction equals(o1, o2) {\n    if (o1 === o2)\n        return true;\n    if (o1 === null || o2 === null)\n        return false;\n    if (o1 !== o1 && o2 !== o2)\n        return true; // NaN === NaN\n    // NaN === NaN\n    /** @type {?} */\n    let t1 = typeof o1;\n    /** @type {?} */\n    let t2 = typeof o2;\n    /** @type {?} */\n    let length;\n    /** @type {?} */\n    let key;\n    /** @type {?} */\n    let keySet;\n    if (t1 == t2 && t1 == 'object') {\n        if (Array.isArray(o1)) {\n            if (!Array.isArray(o2))\n                return false;\n            if ((length = o1.length) == o2.length) {\n                for (key = 0; key < length; key++) {\n                    if (!equals(o1[key], o2[key]))\n                        return false;\n                }\n                return true;\n            }\n        }\n        else {\n            if (Array.isArray(o2)) {\n                return false;\n            }\n            keySet = Object.create(null);\n            for (key in o1) {\n                if (!equals(o1[key], o2[key])) {\n                    return false;\n                }\n                keySet[key] = true;\n            }\n            for (key in o2) {\n                if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n                    return false;\n                }\n            }\n            return true;\n        }\n    }\n    return false;\n}\n/* tslint:enable */\n/**\n * @param {?} value\n * @return {?}\n */\nfunction isDefined(value) {\n    return typeof value !== 'undefined' && value !== null;\n}\n/**\n * @param {?} item\n * @return {?}\n */\nfunction isObject(item) {\n    return (item && typeof item === 'object' && !Array.isArray(item));\n}\n/**\n * @param {?} target\n * @param {?} source\n * @return {?}\n */\nfunction mergeDeep(target, source) {\n    /** @type {?} */\n    let output = Object.assign({}, target);\n    if (isObject(target) && isObject(source)) {\n        Object.keys(source).forEach((/**\n         * @param {?} key\n         * @return {?}\n         */\n        (key) => {\n            if (isObject(source[key])) {\n                if (!(key in target)) {\n                    Object.assign(output, { [key]: source[key] });\n                }\n                else {\n                    output[key] = mergeDeep(target[key], source[key]);\n                }\n            }\n            else {\n                Object.assign(output, { [key]: source[key] });\n            }\n        }));\n    }\n    return output;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.parser.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @abstract\n */\nclass TranslateParser {\n}\nif (false) {\n    /**\n     * Interpolates a string to replace parameters\n     * \"This is a {{ key }}\" ==> \"This is a value\", with params = { key: \"value\" }\n     * @abstract\n     * @param {?} expr\n     * @param {?=} params\n     * @return {?}\n     */\n    TranslateParser.prototype.interpolate = function (expr, params) { };\n    /**\n     * Gets a value from an object by composed key\n     * parser.getValue({ key1: { keyA: 'valueI' }}, 'key1.keyA') ==> 'valueI'\n     * @abstract\n     * @param {?} target\n     * @param {?} key\n     * @return {?}\n     */\n    TranslateParser.prototype.getValue = function (target, key) { };\n}\nclass TranslateDefaultParser extends TranslateParser {\n    constructor() {\n        super(...arguments);\n        this.templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n    }\n    /**\n     * @param {?} expr\n     * @param {?=} params\n     * @return {?}\n     */\n    interpolate(expr, params) {\n        /** @type {?} */\n        let result;\n        if (typeof expr === 'string') {\n            result = this.interpolateString(expr, params);\n        }\n        else if (typeof expr === 'function') {\n            result = this.interpolateFunction(expr, params);\n        }\n        else {\n            // this should not happen, but an unrelated TranslateService test depends on it\n            result = (/** @type {?} */ (expr));\n        }\n        return result;\n    }\n    /**\n     * @param {?} target\n     * @param {?} key\n     * @return {?}\n     */\n    getValue(target, key) {\n        /** @type {?} */\n        let keys = typeof key === 'string' ? key.split('.') : [key];\n        key = '';\n        do {\n            key += keys.shift();\n            if (isDefined(target) && isDefined(target[key]) && (typeof target[key] === 'object' || !keys.length)) {\n                target = target[key];\n                key = '';\n            }\n            else if (!keys.length) {\n                target = undefined;\n            }\n            else {\n                key += '.';\n            }\n        } while (keys.length);\n        return target;\n    }\n    /**\n     * @private\n     * @param {?} fn\n     * @param {?=} params\n     * @return {?}\n     */\n    interpolateFunction(fn, params) {\n        return fn(params);\n    }\n    /**\n     * @private\n     * @param {?} expr\n     * @param {?=} params\n     * @return {?}\n     */\n    interpolateString(expr, params) {\n        if (!params) {\n            return expr;\n        }\n        return expr.replace(this.templateMatcher, (/**\n         * @param {?} substring\n         * @param {?} b\n         * @return {?}\n         */\n        (substring, b) => {\n            /** @type {?} */\n            let r = this.getValue(params, b);\n            return isDefined(r) ? r : substring;\n        }));\n    }\n}\nTranslateDefaultParser.ɵfac = /*@__PURE__*/ function () { let ɵTranslateDefaultParser_BaseFactory; return function TranslateDefaultParser_Factory(t) { return (ɵTranslateDefaultParser_BaseFactory || (ɵTranslateDefaultParser_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(TranslateDefaultParser)))(t || TranslateDefaultParser); }; }();\nTranslateDefaultParser.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: TranslateDefaultParser, factory: TranslateDefaultParser.ɵfac });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateDefaultParser, [{\n        type: Injectable\n    }], null, null); })();\nif (false) {\n    /** @type {?} */\n    TranslateDefaultParser.prototype.templateMatcher;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.compiler.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @abstract\n */\nclass TranslateCompiler {\n}\nif (false) {\n    /**\n     * @abstract\n     * @param {?} value\n     * @param {?} lang\n     * @return {?}\n     */\n    TranslateCompiler.prototype.compile = function (value, lang) { };\n    /**\n     * @abstract\n     * @param {?} translations\n     * @param {?} lang\n     * @return {?}\n     */\n    TranslateCompiler.prototype.compileTranslations = function (translations, lang) { };\n}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n    /**\n     * @param {?} value\n     * @param {?} lang\n     * @return {?}\n     */\n    compile(value, lang) {\n        return value;\n    }\n    /**\n     * @param {?} translations\n     * @param {?} lang\n     * @return {?}\n     */\n    compileTranslations(translations, lang) {\n        return translations;\n    }\n}\nTranslateFakeCompiler.ɵfac = /*@__PURE__*/ function () { let ɵTranslateFakeCompiler_BaseFactory; return function TranslateFakeCompiler_Factory(t) { return (ɵTranslateFakeCompiler_BaseFactory || (ɵTranslateFakeCompiler_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(TranslateFakeCompiler)))(t || TranslateFakeCompiler); }; }();\nTranslateFakeCompiler.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: TranslateFakeCompiler, factory: TranslateFakeCompiler.ɵfac });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateFakeCompiler, [{\n        type: Injectable\n    }], null, null); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.store.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TranslateStore {\n    constructor() {\n        /**\n         * The lang currently used\n         */\n        this.currentLang = this.defaultLang;\n        /**\n         * a list of translations per lang\n         */\n        this.translations = {};\n        /**\n         * an array of langs\n         */\n        this.langs = [];\n        /**\n         * An EventEmitter to listen to translation change events\n         * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n         *     // do something\n         * });\n         */\n        this.onTranslationChange = new EventEmitter();\n        /**\n         * An EventEmitter to listen to lang change events\n         * onLangChange.subscribe((params: LangChangeEvent) => {\n         *     // do something\n         * });\n         */\n        this.onLangChange = new EventEmitter();\n        /**\n         * An EventEmitter to listen to default lang change events\n         * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n         *     // do something\n         * });\n         */\n        this.onDefaultLangChange = new EventEmitter();\n    }\n}\nif (false) {\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     * @type {?}\n     */\n    TranslateStore.prototype.defaultLang;\n    /**\n     * The lang currently used\n     * @type {?}\n     */\n    TranslateStore.prototype.currentLang;\n    /**\n     * a list of translations per lang\n     * @type {?}\n     */\n    TranslateStore.prototype.translations;\n    /**\n     * an array of langs\n     * @type {?}\n     */\n    TranslateStore.prototype.langs;\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n     * @type {?}\n     */\n    TranslateStore.prototype.onTranslationChange;\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n     * @type {?}\n     */\n    TranslateStore.prototype.onLangChange;\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n     * @type {?}\n     */\n    TranslateStore.prototype.onDefaultLangChange;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.service.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst USE_STORE = new InjectionToken('USE_STORE');\n/** @type {?} */\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\n/** @type {?} */\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\n/** @type {?} */\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\n/**\n * @record\n */\nfunction TranslationChangeEvent() { }\nif (false) {\n    /** @type {?} */\n    TranslationChangeEvent.prototype.translations;\n    /** @type {?} */\n    TranslationChangeEvent.prototype.lang;\n}\n/**\n * @record\n */\nfunction LangChangeEvent() { }\nif (false) {\n    /** @type {?} */\n    LangChangeEvent.prototype.lang;\n    /** @type {?} */\n    LangChangeEvent.prototype.translations;\n}\n/**\n * @record\n */\nfunction DefaultLangChangeEvent() { }\nif (false) {\n    /** @type {?} */\n    DefaultLangChangeEvent.prototype.lang;\n    /** @type {?} */\n    DefaultLangChangeEvent.prototype.translations;\n}\nclass TranslateService {\n    /**\n     *\n     * @param {?} store an instance of the store (that is supposed to be unique)\n     * @param {?} currentLoader An instance of the loader currently used\n     * @param {?} compiler An instance of the compiler currently used\n     * @param {?} parser An instance of the parser currently used\n     * @param {?} missingTranslationHandler A handler for missing translations.\n     * @param {?=} useDefaultLang whether we should use default language translation when current language translation is missing.\n     * @param {?=} isolate whether this service should use the store or not\n     * @param {?=} extend To make a child module extend (and use) translations from parent modules.\n     * @param {?=} defaultLanguage Set the default language using configuration\n     */\n    constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n        this.store = store;\n        this.currentLoader = currentLoader;\n        this.compiler = compiler;\n        this.parser = parser;\n        this.missingTranslationHandler = missingTranslationHandler;\n        this.useDefaultLang = useDefaultLang;\n        this.isolate = isolate;\n        this.extend = extend;\n        this.pending = false;\n        this._onTranslationChange = new EventEmitter();\n        this._onLangChange = new EventEmitter();\n        this._onDefaultLangChange = new EventEmitter();\n        this._langs = [];\n        this._translations = {};\n        this._translationRequests = {};\n        /** set the default language from configuration */\n        if (defaultLanguage) {\n            this.setDefaultLang(defaultLanguage);\n        }\n    }\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n     * @return {?}\n     */\n    get onTranslationChange() {\n        return this.isolate ? this._onTranslationChange : this.store.onTranslationChange;\n    }\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n     * @return {?}\n     */\n    get onLangChange() {\n        return this.isolate ? this._onLangChange : this.store.onLangChange;\n    }\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n     * @return {?}\n     */\n    get onDefaultLangChange() {\n        return this.isolate ? this._onDefaultLangChange : this.store.onDefaultLangChange;\n    }\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     * @return {?}\n     */\n    get defaultLang() {\n        return this.isolate ? this._defaultLang : this.store.defaultLang;\n    }\n    /**\n     * @param {?} defaultLang\n     * @return {?}\n     */\n    set defaultLang(defaultLang) {\n        if (this.isolate) {\n            this._defaultLang = defaultLang;\n        }\n        else {\n            this.store.defaultLang = defaultLang;\n        }\n    }\n    /**\n     * The lang currently used\n     * @return {?}\n     */\n    get currentLang() {\n        return this.isolate ? this._currentLang : this.store.currentLang;\n    }\n    /**\n     * @param {?} currentLang\n     * @return {?}\n     */\n    set currentLang(currentLang) {\n        if (this.isolate) {\n            this._currentLang = currentLang;\n        }\n        else {\n            this.store.currentLang = currentLang;\n        }\n    }\n    /**\n     * an array of langs\n     * @return {?}\n     */\n    get langs() {\n        return this.isolate ? this._langs : this.store.langs;\n    }\n    /**\n     * @param {?} langs\n     * @return {?}\n     */\n    set langs(langs) {\n        if (this.isolate) {\n            this._langs = langs;\n        }\n        else {\n            this.store.langs = langs;\n        }\n    }\n    /**\n     * a list of translations per lang\n     * @return {?}\n     */\n    get translations() {\n        return this.isolate ? this._translations : this.store.translations;\n    }\n    /**\n     * @param {?} translations\n     * @return {?}\n     */\n    set translations(translations) {\n        if (this.isolate) {\n            this._translations = translations;\n        }\n        else {\n            this.store.translations = translations;\n        }\n    }\n    /**\n     * Sets the default language to use as a fallback\n     * @param {?} lang\n     * @return {?}\n     */\n    setDefaultLang(lang) {\n        if (lang === this.defaultLang) {\n            return;\n        }\n        /** @type {?} */\n        let pending = this.retrieveTranslations(lang);\n        if (typeof pending !== \"undefined\") {\n            // on init set the defaultLang immediately\n            if (this.defaultLang == null) {\n                this.defaultLang = lang;\n            }\n            pending.pipe(take(1))\n                .subscribe((/**\n             * @param {?} res\n             * @return {?}\n             */\n            (res) => {\n                this.changeDefaultLang(lang);\n            }));\n        }\n        else { // we already have this language\n            this.changeDefaultLang(lang);\n        }\n    }\n    /**\n     * Gets the default language used\n     * @return {?}\n     */\n    getDefaultLang() {\n        return this.defaultLang;\n    }\n    /**\n     * Changes the lang currently used\n     * @param {?} lang\n     * @return {?}\n     */\n    use(lang) {\n        // don't change the language if the language given is already selected\n        if (lang === this.currentLang) {\n            return of(this.translations[lang]);\n        }\n        /** @type {?} */\n        let pending = this.retrieveTranslations(lang);\n        if (typeof pending !== \"undefined\") {\n            // on init set the currentLang immediately\n            if (!this.currentLang) {\n                this.currentLang = lang;\n            }\n            pending.pipe(take(1))\n                .subscribe((/**\n             * @param {?} res\n             * @return {?}\n             */\n            (res) => {\n                this.changeLang(lang);\n            }));\n            return pending;\n        }\n        else { // we have this language, return an Observable\n            this.changeLang(lang);\n            return of(this.translations[lang]);\n        }\n    }\n    /**\n     * Retrieves the given translations\n     * @private\n     * @param {?} lang\n     * @return {?}\n     */\n    retrieveTranslations(lang) {\n        /** @type {?} */\n        let pending;\n        // if this language is unavailable or extend is true, ask for it\n        if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n            this._translationRequests[lang] = this._translationRequests[lang] || this.getTranslation(lang);\n            pending = this._translationRequests[lang];\n        }\n        return pending;\n    }\n    /**\n     * Gets an object of translations for a given language with the current loader\n     * and passes it through the compiler\n     * @param {?} lang\n     * @return {?}\n     */\n    getTranslation(lang) {\n        this.pending = true;\n        /** @type {?} */\n        const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n        this.loadingTranslations = loadingTranslations.pipe(map((/**\n         * @param {?} res\n         * @return {?}\n         */\n        (res) => this.compiler.compileTranslations(res, lang))), shareReplay(1), take(1));\n        this.loadingTranslations\n            .subscribe({\n            next: (/**\n             * @param {?} res\n             * @return {?}\n             */\n            (res) => {\n                this.translations[lang] = this.extend && this.translations[lang] ? Object.assign(Object.assign({}, res), this.translations[lang]) : res;\n                this.updateLangs();\n                this.pending = false;\n            }),\n            error: (/**\n             * @param {?} err\n             * @return {?}\n             */\n            (err) => {\n                this.pending = false;\n            })\n        });\n        return loadingTranslations;\n    }\n    /**\n     * Manually sets an object of translations for a given language\n     * after passing it through the compiler\n     * @param {?} lang\n     * @param {?} translations\n     * @param {?=} shouldMerge\n     * @return {?}\n     */\n    setTranslation(lang, translations, shouldMerge = false) {\n        translations = this.compiler.compileTranslations(translations, lang);\n        if ((shouldMerge || this.extend) && this.translations[lang]) {\n            this.translations[lang] = mergeDeep(this.translations[lang], translations);\n        }\n        else {\n            this.translations[lang] = translations;\n        }\n        this.updateLangs();\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Returns an array of currently available langs\n     * @return {?}\n     */\n    getLangs() {\n        return this.langs;\n    }\n    /**\n     * Add available langs\n     * @param {?} langs\n     * @return {?}\n     */\n    addLangs(langs) {\n        langs.forEach((/**\n         * @param {?} lang\n         * @return {?}\n         */\n        (lang) => {\n            if (this.langs.indexOf(lang) === -1) {\n                this.langs.push(lang);\n            }\n        }));\n    }\n    /**\n     * Update the list of available langs\n     * @private\n     * @return {?}\n     */\n    updateLangs() {\n        this.addLangs(Object.keys(this.translations));\n    }\n    /**\n     * Returns the parsed result of the translations\n     * @param {?} translations\n     * @param {?} key\n     * @param {?=} interpolateParams\n     * @return {?}\n     */\n    getParsedResult(translations, key, interpolateParams) {\n        /** @type {?} */\n        let res;\n        if (key instanceof Array) {\n            /** @type {?} */\n            let result = {};\n            /** @type {?} */\n            let observables = false;\n            for (let k of key) {\n                result[k] = this.getParsedResult(translations, k, interpolateParams);\n                if (isObservable(result[k])) {\n                    observables = true;\n                }\n            }\n            if (observables) {\n                /** @type {?} */\n                const sources = key.map((/**\n                 * @param {?} k\n                 * @return {?}\n                 */\n                k => isObservable(result[k]) ? result[k] : of((/** @type {?} */ (result[k])))));\n                return forkJoin(sources).pipe(map((/**\n                 * @param {?} arr\n                 * @return {?}\n                 */\n                (arr) => {\n                    /** @type {?} */\n                    let obj = {};\n                    arr.forEach((/**\n                     * @param {?} value\n                     * @param {?} index\n                     * @return {?}\n                     */\n                    (value, index) => {\n                        obj[key[index]] = value;\n                    }));\n                    return obj;\n                })));\n            }\n            return result;\n        }\n        if (translations) {\n            res = this.parser.interpolate(this.parser.getValue(translations, key), interpolateParams);\n        }\n        if (typeof res === \"undefined\" && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n            res = this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang], key), interpolateParams);\n        }\n        if (typeof res === \"undefined\") {\n            /** @type {?} */\n            let params = { key, translateService: this };\n            if (typeof interpolateParams !== 'undefined') {\n                params.interpolateParams = interpolateParams;\n            }\n            res = this.missingTranslationHandler.handle(params);\n        }\n        return typeof res !== \"undefined\" ? res : key;\n    }\n    /**\n     * Gets the translated value of a key (or an array of keys)\n     * @param {?} key\n     * @param {?=} interpolateParams\n     * @return {?} the translated key, or an object of translated keys\n     */\n    get(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        // check if we are loading a new translation to use\n        if (this.pending) {\n            return this.loadingTranslations.pipe(concatMap((/**\n             * @param {?} res\n             * @return {?}\n             */\n            (res) => {\n                res = this.getParsedResult(res, key, interpolateParams);\n                return isObservable(res) ? res : of(res);\n            })));\n        }\n        else {\n            /** @type {?} */\n            let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n            return isObservable(res) ? res : of(res);\n        }\n    }\n    /**\n     * Returns a stream of translated values of a key (or an array of keys) which updates\n     * whenever the translation changes.\n     * @param {?} key\n     * @param {?=} interpolateParams\n     * @return {?} A stream of the translated key, or an object of translated keys\n     */\n    getStreamOnTranslationChange(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        return concat(defer((/**\n         * @return {?}\n         */\n        () => this.get(key, interpolateParams))), this.onTranslationChange.pipe(switchMap((/**\n         * @param {?} event\n         * @return {?}\n         */\n        (event) => {\n            /** @type {?} */\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\n            if (typeof res.subscribe === 'function') {\n                return res;\n            }\n            else {\n                return of(res);\n            }\n        }))));\n    }\n    /**\n     * Returns a stream of translated values of a key (or an array of keys) which updates\n     * whenever the language changes.\n     * @param {?} key\n     * @param {?=} interpolateParams\n     * @return {?} A stream of the translated key, or an object of translated keys\n     */\n    stream(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        return concat(defer((/**\n         * @return {?}\n         */\n        () => this.get(key, interpolateParams))), this.onLangChange.pipe(switchMap((/**\n         * @param {?} event\n         * @return {?}\n         */\n        (event) => {\n            /** @type {?} */\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\n            return isObservable(res) ? res : of(res);\n        }))));\n    }\n    /**\n     * Returns a translation instantly from the internal state of loaded translation.\n     * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.\n     * @param {?} key\n     * @param {?=} interpolateParams\n     * @return {?}\n     */\n    instant(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        /** @type {?} */\n        let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n        if (isObservable(res)) {\n            if (key instanceof Array) {\n                /** @type {?} */\n                let obj = {};\n                key.forEach((/**\n                 * @param {?} value\n                 * @param {?} index\n                 * @return {?}\n                 */\n                (value, index) => {\n                    obj[key[index]] = key[index];\n                }));\n                return obj;\n            }\n            return key;\n        }\n        else {\n            return res;\n        }\n    }\n    /**\n     * Sets the translated value of a key, after compiling it\n     * @param {?} key\n     * @param {?} value\n     * @param {?=} lang\n     * @return {?}\n     */\n    set(key, value, lang = this.currentLang) {\n        this.translations[lang][key] = this.compiler.compile(value, lang);\n        this.updateLangs();\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Changes the current lang\n     * @private\n     * @param {?} lang\n     * @return {?}\n     */\n    changeLang(lang) {\n        this.currentLang = lang;\n        this.onLangChange.emit({ lang: lang, translations: this.translations[lang] });\n        // if there is no default lang, use the one that we just set\n        if (this.defaultLang == null) {\n            this.changeDefaultLang(lang);\n        }\n    }\n    /**\n     * Changes the default lang\n     * @private\n     * @param {?} lang\n     * @return {?}\n     */\n    changeDefaultLang(lang) {\n        this.defaultLang = lang;\n        this.onDefaultLangChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Allows to reload the lang file from the file\n     * @param {?} lang\n     * @return {?}\n     */\n    reloadLang(lang) {\n        this.resetLang(lang);\n        return this.getTranslation(lang);\n    }\n    /**\n     * Deletes inner translation\n     * @param {?} lang\n     * @return {?}\n     */\n    resetLang(lang) {\n        this._translationRequests[lang] = undefined;\n        this.translations[lang] = undefined;\n    }\n    /**\n     * Returns the language code name from the browser, e.g. \"de\"\n     * @return {?}\n     */\n    getBrowserLang() {\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n            return undefined;\n        }\n        /** @type {?} */\n        let browserLang = window.navigator.languages ? window.navigator.languages[0] : null;\n        browserLang = browserLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n        if (typeof browserLang === 'undefined') {\n            return undefined;\n        }\n        if (browserLang.indexOf('-') !== -1) {\n            browserLang = browserLang.split('-')[0];\n        }\n        if (browserLang.indexOf('_') !== -1) {\n            browserLang = browserLang.split('_')[0];\n        }\n        return browserLang;\n    }\n    /**\n     * Returns the culture language code name from the browser, e.g. \"de-DE\"\n     * @return {?}\n     */\n    getBrowserCultureLang() {\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n            return undefined;\n        }\n        /** @type {?} */\n        let browserCultureLang = window.navigator.languages ? window.navigator.languages[0] : null;\n        browserCultureLang = browserCultureLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n        return browserCultureLang;\n    }\n}\nTranslateService.ɵfac = function TranslateService_Factory(t) { return new (t || TranslateService)(ɵngcc0.ɵɵinject(TranslateStore), ɵngcc0.ɵɵinject(TranslateLoader), ɵngcc0.ɵɵinject(TranslateCompiler), ɵngcc0.ɵɵinject(TranslateParser), ɵngcc0.ɵɵinject(MissingTranslationHandler), ɵngcc0.ɵɵinject(USE_DEFAULT_LANG), ɵngcc0.ɵɵinject(USE_STORE), ɵngcc0.ɵɵinject(USE_EXTEND), ɵngcc0.ɵɵinject(DEFAULT_LANGUAGE)); };\nTranslateService.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: TranslateService, factory: TranslateService.ɵfac });\n/** @nocollapse */\nTranslateService.ctorParameters = () => [\n    { type: TranslateStore },\n    { type: TranslateLoader },\n    { type: TranslateCompiler },\n    { type: TranslateParser },\n    { type: MissingTranslationHandler },\n    { type: Boolean, decorators: [{ type: Inject, args: [USE_DEFAULT_LANG,] }] },\n    { type: Boolean, decorators: [{ type: Inject, args: [USE_STORE,] }] },\n    { type: Boolean, decorators: [{ type: Inject, args: [USE_EXTEND,] }] },\n    { type: String, decorators: [{ type: Inject, args: [DEFAULT_LANGUAGE,] }] }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateService, [{\n        type: Injectable\n    }], function () { return [{ type: TranslateStore }, { type: TranslateLoader }, { type: TranslateCompiler }, { type: TranslateParser }, { type: MissingTranslationHandler }, { type: Boolean, decorators: [{\n                type: Inject,\n                args: [USE_DEFAULT_LANG]\n            }] }, { type: Boolean, decorators: [{\n                type: Inject,\n                args: [USE_STORE]\n            }] }, { type: Boolean, decorators: [{\n                type: Inject,\n                args: [USE_EXTEND]\n            }] }, { type: String, decorators: [{\n                type: Inject,\n                args: [DEFAULT_LANGUAGE]\n            }] }]; }, null); })();\nif (false) {\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype.loadingTranslations;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype.pending;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._onTranslationChange;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._onLangChange;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._onDefaultLangChange;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._defaultLang;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._currentLang;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._langs;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._translations;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype._translationRequests;\n    /** @type {?} */\n    TranslateService.prototype.store;\n    /** @type {?} */\n    TranslateService.prototype.currentLoader;\n    /** @type {?} */\n    TranslateService.prototype.compiler;\n    /** @type {?} */\n    TranslateService.prototype.parser;\n    /** @type {?} */\n    TranslateService.prototype.missingTranslationHandler;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype.useDefaultLang;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype.isolate;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateService.prototype.extend;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.directive.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TranslateDirective {\n    /**\n     * @param {?} translateService\n     * @param {?} element\n     * @param {?} _ref\n     */\n    constructor(translateService, element, _ref) {\n        this.translateService = translateService;\n        this.element = element;\n        this._ref = _ref;\n        // subscribe to onTranslationChange event, in case the translations of the current lang change\n        if (!this.onTranslationChangeSub) {\n            this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe((/**\n             * @param {?} event\n             * @return {?}\n             */\n            (event) => {\n                if (event.lang === this.translateService.currentLang) {\n                    this.checkNodes(true, event.translations);\n                }\n            }));\n        }\n        // subscribe to onLangChange event, in case the language changes\n        if (!this.onLangChangeSub) {\n            this.onLangChangeSub = this.translateService.onLangChange.subscribe((/**\n             * @param {?} event\n             * @return {?}\n             */\n            (event) => {\n                this.checkNodes(true, event.translations);\n            }));\n        }\n        // subscribe to onDefaultLangChange event, in case the default language changes\n        if (!this.onDefaultLangChangeSub) {\n            this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe((/**\n             * @param {?} event\n             * @return {?}\n             */\n            (event) => {\n                this.checkNodes(true);\n            }));\n        }\n    }\n    /**\n     * @param {?} key\n     * @return {?}\n     */\n    set translate(key) {\n        if (key) {\n            this.key = key;\n            this.checkNodes();\n        }\n    }\n    /**\n     * @param {?} params\n     * @return {?}\n     */\n    set translateParams(params) {\n        if (!equals(this.currentParams, params)) {\n            this.currentParams = params;\n            this.checkNodes(true);\n        }\n    }\n    /**\n     * @return {?}\n     */\n    ngAfterViewChecked() {\n        this.checkNodes();\n    }\n    /**\n     * @param {?=} forceUpdate\n     * @param {?=} translations\n     * @return {?}\n     */\n    checkNodes(forceUpdate = false, translations) {\n        /** @type {?} */\n        let nodes = this.element.nativeElement.childNodes;\n        // if the element is empty\n        if (!nodes.length) {\n            // we add the key as content\n            this.setContent(this.element.nativeElement, this.key);\n            nodes = this.element.nativeElement.childNodes;\n        }\n        for (let i = 0; i < nodes.length; ++i) {\n            /** @type {?} */\n            let node = nodes[i];\n            if (node.nodeType === 3) { // node type 3 is a text node\n                // node type 3 is a text node\n                /** @type {?} */\n                let key;\n                if (forceUpdate) {\n                    node.lastKey = null;\n                }\n                if (isDefined(node.lookupKey)) {\n                    key = node.lookupKey;\n                }\n                else if (this.key) {\n                    key = this.key;\n                }\n                else {\n                    /** @type {?} */\n                    let content = this.getContent(node);\n                    /** @type {?} */\n                    let trimmedContent = content.trim();\n                    if (trimmedContent.length) {\n                        node.lookupKey = trimmedContent;\n                        // we want to use the content as a key, not the translation value\n                        if (content !== node.currentValue) {\n                            key = trimmedContent;\n                            // the content was changed from the user, we'll use it as a reference if needed\n                            node.originalContent = content || node.originalContent;\n                        }\n                        else if (node.originalContent) { // the content seems ok, but the lang has changed\n                            // the current content is the translation, not the key, use the last real content as key\n                            key = node.originalContent.trim();\n                        }\n                        else if (content !== node.currentValue) {\n                            // we want to use the content as a key, not the translation value\n                            key = trimmedContent;\n                            // the content was changed from the user, we'll use it as a reference if needed\n                            node.originalContent = content || node.originalContent;\n                        }\n                    }\n                }\n                this.updateValue(key, node, translations);\n            }\n        }\n    }\n    /**\n     * @param {?} key\n     * @param {?} node\n     * @param {?} translations\n     * @return {?}\n     */\n    updateValue(key, node, translations) {\n        if (key) {\n            if (node.lastKey === key && this.lastParams === this.currentParams) {\n                return;\n            }\n            this.lastParams = this.currentParams;\n            /** @type {?} */\n            let onTranslation = (/**\n             * @param {?} res\n             * @return {?}\n             */\n            (res) => {\n                if (res !== key) {\n                    node.lastKey = key;\n                }\n                if (!node.originalContent) {\n                    node.originalContent = this.getContent(node);\n                }\n                node.currentValue = isDefined(res) ? res : (node.originalContent || key);\n                // we replace in the original content to preserve spaces that we might have trimmed\n                this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n                this._ref.markForCheck();\n            });\n            if (isDefined(translations)) {\n                /** @type {?} */\n                let res = this.translateService.getParsedResult(translations, key, this.currentParams);\n                if (isObservable(res)) {\n                    res.subscribe(onTranslation);\n                }\n                else {\n                    onTranslation(res);\n                }\n            }\n            else {\n                this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n            }\n        }\n    }\n    /**\n     * @param {?} node\n     * @return {?}\n     */\n    getContent(node) {\n        return isDefined(node.textContent) ? node.textContent : node.data;\n    }\n    /**\n     * @param {?} node\n     * @param {?} content\n     * @return {?}\n     */\n    setContent(node, content) {\n        if (isDefined(node.textContent)) {\n            node.textContent = content;\n        }\n        else {\n            node.data = content;\n        }\n    }\n    /**\n     * @return {?}\n     */\n    ngOnDestroy() {\n        if (this.onLangChangeSub) {\n            this.onLangChangeSub.unsubscribe();\n        }\n        if (this.onDefaultLangChangeSub) {\n            this.onDefaultLangChangeSub.unsubscribe();\n        }\n        if (this.onTranslationChangeSub) {\n            this.onTranslationChangeSub.unsubscribe();\n        }\n    }\n}\nTranslateDirective.ɵfac = function TranslateDirective_Factory(t) { return new (t || TranslateDirective)(ɵngcc0.ɵɵdirectiveInject(TranslateService), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef)); };\nTranslateDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: TranslateDirective, selectors: [[\"\", \"translate\", \"\"], [\"\", \"ngx-translate\", \"\"]], inputs: { translate: \"translate\", translateParams: \"translateParams\" } });\n/** @nocollapse */\nTranslateDirective.ctorParameters = () => [\n    { type: TranslateService },\n    { type: ElementRef },\n    { type: ChangeDetectorRef }\n];\nTranslateDirective.propDecorators = {\n    translate: [{ type: Input }],\n    translateParams: [{ type: Input }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateDirective, [{\n        type: Directive,\n        args: [{\n                selector: '[translate],[ngx-translate]'\n            }]\n    }], function () { return [{ type: TranslateService }, { type: ɵngcc0.ElementRef }, { type: ɵngcc0.ChangeDetectorRef }]; }, { translate: [{\n            type: Input\n        }], translateParams: [{\n            type: Input\n        }] }); })();\nif (false) {\n    /** @type {?} */\n    TranslateDirective.prototype.key;\n    /** @type {?} */\n    TranslateDirective.prototype.lastParams;\n    /** @type {?} */\n    TranslateDirective.prototype.currentParams;\n    /** @type {?} */\n    TranslateDirective.prototype.onLangChangeSub;\n    /** @type {?} */\n    TranslateDirective.prototype.onDefaultLangChangeSub;\n    /** @type {?} */\n    TranslateDirective.prototype.onTranslationChangeSub;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateDirective.prototype.translateService;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateDirective.prototype.element;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslateDirective.prototype._ref;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: lib/translate.pipe.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass TranslatePipe {\n    /**\n     * @param {?} translate\n     * @param {?} _ref\n     */\n    constructor(translate, _ref) {\n        this.translate = translate;\n        this._ref = _ref;\n        this.value = '';\n    }\n    /**\n     * @param {?} key\n     * @param {?=} interpolateParams\n     * @param {?=} translations\n     * @return {?}\n     */\n    updateValue(key, interpolateParams, translations) {\n        /** @type {?} */\n        let onTranslation = (/**\n         * @param {?} res\n         * @return {?}\n         */\n        (res) => {\n            this.value = res !== undefined ? res : key;\n            this.lastKey = key;\n            this._ref.markForCheck();\n        });\n        if (translations) {\n            /** @type {?} */\n            let res = this.translate.getParsedResult(translations, key, interpolateParams);\n            if (isObservable(res.subscribe)) {\n                res.subscribe(onTranslation);\n            }\n            else {\n                onTranslation(res);\n            }\n        }\n        this.translate.get(key, interpolateParams).subscribe(onTranslation);\n    }\n    /**\n     * @param {?} query\n     * @param {...?} args\n     * @return {?}\n     */\n    transform(query, ...args) {\n        if (!query || !query.length) {\n            return query;\n        }\n        // if we ask another time for the same key, return the last value\n        if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n            return this.value;\n        }\n        /** @type {?} */\n        let interpolateParams;\n        if (isDefined(args[0]) && args.length) {\n            if (typeof args[0] === 'string' && args[0].length) {\n                // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n                // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n                /** @type {?} */\n                let validArgs = args[0]\n                    .replace(/(\\')?([a-zA-Z0-9_]+)(\\')?(\\s)?:/g, '\"$2\":')\n                    .replace(/:(\\s)?(\\')(.*?)(\\')/g, ':\"$3\"');\n                try {\n                    interpolateParams = JSON.parse(validArgs);\n                }\n                catch (e) {\n                    throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n                }\n            }\n            else if (typeof args[0] === 'object' && !Array.isArray(args[0])) {\n                interpolateParams = args[0];\n            }\n        }\n        // store the query, in case it changes\n        this.lastKey = query;\n        // store the params, in case they change\n        this.lastParams = args;\n        // set the value\n        this.updateValue(query, interpolateParams);\n        // if there is a subscription to onLangChange, clean it\n        this._dispose();\n        // subscribe to onTranslationChange event, in case the translations change\n        if (!this.onTranslationChange) {\n            this.onTranslationChange = this.translate.onTranslationChange.subscribe((/**\n             * @param {?} event\n             * @return {?}\n             */\n            (event) => {\n                if (this.lastKey && event.lang === this.translate.currentLang) {\n                    this.lastKey = null;\n                    this.updateValue(query, interpolateParams, event.translations);\n                }\n            }));\n        }\n        // subscribe to onLangChange event, in case the language changes\n        if (!this.onLangChange) {\n            this.onLangChange = this.translate.onLangChange.subscribe((/**\n             * @param {?} event\n             * @return {?}\n             */\n            (event) => {\n                if (this.lastKey) {\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n                    this.updateValue(query, interpolateParams, event.translations);\n                }\n            }));\n        }\n        // subscribe to onDefaultLangChange event, in case the default language changes\n        if (!this.onDefaultLangChange) {\n            this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe((/**\n             * @return {?}\n             */\n            () => {\n                if (this.lastKey) {\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n                    this.updateValue(query, interpolateParams);\n                }\n            }));\n        }\n        return this.value;\n    }\n    /**\n     * Clean any existing subscription to change events\n     * @private\n     * @return {?}\n     */\n    _dispose() {\n        if (typeof this.onTranslationChange !== 'undefined') {\n            this.onTranslationChange.unsubscribe();\n            this.onTranslationChange = undefined;\n        }\n        if (typeof this.onLangChange !== 'undefined') {\n            this.onLangChange.unsubscribe();\n            this.onLangChange = undefined;\n        }\n        if (typeof this.onDefaultLangChange !== 'undefined') {\n            this.onDefaultLangChange.unsubscribe();\n            this.onDefaultLangChange = undefined;\n        }\n    }\n    /**\n     * @return {?}\n     */\n    ngOnDestroy() {\n        this._dispose();\n    }\n}\nTranslatePipe.ɵfac = function TranslatePipe_Factory(t) { return new (t || TranslatePipe)(ɵngcc0.ɵɵdirectiveInject(TranslateService, 16), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ChangeDetectorRef, 16)); };\nTranslatePipe.ɵpipe = /*@__PURE__*/ ɵngcc0.ɵɵdefinePipe({ name: \"translate\", type: TranslatePipe, pure: false });\nTranslatePipe.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: TranslatePipe, factory: TranslatePipe.ɵfac });\n/** @nocollapse */\nTranslatePipe.ctorParameters = () => [\n    { type: TranslateService },\n    { type: ChangeDetectorRef }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslatePipe, [{\n        type: Injectable\n    }, {\n        type: Pipe,\n        args: [{\n                name: 'translate',\n                pure: false // required to update the value when the promise is resolved\n            }]\n    }], function () { return [{ type: TranslateService }, { type: ɵngcc0.ChangeDetectorRef }]; }, null); })();\nif (false) {\n    /** @type {?} */\n    TranslatePipe.prototype.value;\n    /** @type {?} */\n    TranslatePipe.prototype.lastKey;\n    /** @type {?} */\n    TranslatePipe.prototype.lastParams;\n    /** @type {?} */\n    TranslatePipe.prototype.onTranslationChange;\n    /** @type {?} */\n    TranslatePipe.prototype.onLangChange;\n    /** @type {?} */\n    TranslatePipe.prototype.onDefaultLangChange;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslatePipe.prototype.translate;\n    /**\n     * @type {?}\n     * @private\n     */\n    TranslatePipe.prototype._ref;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: public_api.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @record\n */\nfunction TranslateModuleConfig() { }\nif (false) {\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.loader;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.compiler;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.parser;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.missingTranslationHandler;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.isolate;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.extend;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.useDefaultLang;\n    /** @type {?|undefined} */\n    TranslateModuleConfig.prototype.defaultLanguage;\n}\nclass TranslateModule {\n    /**\n     * Use this method in your root module to provide the TranslateService\n     * @param {?=} config\n     * @return {?}\n     */\n    static forRoot(config = {}) {\n        return {\n            ngModule: TranslateModule,\n            providers: [\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n                TranslateStore,\n                { provide: USE_STORE, useValue: config.isolate },\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n                { provide: USE_EXTEND, useValue: config.extend },\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n                TranslateService\n            ]\n        };\n    }\n    /**\n     * Use this method in your other (non root) modules to import the directive/pipe\n     * @param {?=} config\n     * @return {?}\n     */\n    static forChild(config = {}) {\n        return {\n            ngModule: TranslateModule,\n            providers: [\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n                { provide: USE_STORE, useValue: config.isolate },\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n                { provide: USE_EXTEND, useValue: config.extend },\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n                TranslateService\n            ]\n        };\n    }\n}\nTranslateModule.ɵfac = function TranslateModule_Factory(t) { return new (t || TranslateModule)(); };\nTranslateModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: TranslateModule });\nTranslateModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({});\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(TranslateModule, [{\n        type: NgModule,\n        args: [{\n                declarations: [\n                    TranslatePipe,\n                    TranslateDirective\n                ],\n                exports: [\n                    TranslatePipe,\n                    TranslateDirective\n                ]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(TranslateModule, { declarations: [TranslatePipe, TranslateDirective], exports: [TranslatePipe, TranslateDirective] }); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: ngx-translate-core.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE };\n\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AACjJ,SAASC,EAAE,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAChE,SAASC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,MAAM,MAAM,eAAe;AACvC,MAAMC,eAAe,CAAC;AAEtB,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;AACA;EACIA,eAAe,CAACC,SAAS,CAACC,cAAc,GAAG,UAAUC,IAAI,EAAE,CAAE,CAAC;AAClE;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASJ,eAAe,CAAC;EAC9C;AACJ;AACA;AACA;EACIE,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOd,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;AACJ;AACAe,mBAAmB,CAACC,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIC,gCAAgC;EAAE,OAAO,SAASC,2BAA2BA,CAACC,CAAC,EAAE;IAAE,OAAO,CAACF,gCAAgC,KAAKA,gCAAgC,GAAGP,MAAM,CAACU,qBAAqB,CAACL,mBAAmB,CAAC,CAAC,EAAEI,CAAC,IAAIJ,mBAAmB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACnTA,mBAAmB,CAACM,KAAK,GAAG,aAAcX,MAAM,CAACY,kBAAkB,CAAC;EAAEC,KAAK,EAAER,mBAAmB;EAAES,OAAO,EAAET,mBAAmB,CAACC;AAAK,CAAC,CAAC;AACtI,CAAC,YAAY;EAAE,CAAC,OAAOS,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAACX,mBAAmB,EAAE,CAAC;IACzGY,IAAI,EAAErC;EACV,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsC,+BAA+BA,CAAA,EAAG,CAAE;AAC7C,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;EACIA,+BAA+B,CAAChB,SAAS,CAACiB,GAAG;EAC7C;AACJ;AACA;AACA;EACID,+BAA+B,CAAChB,SAAS,CAACkB,gBAAgB;EAC1D;AACJ;AACA;AACA;EACIF,+BAA+B,CAAChB,SAAS,CAACmB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;AAEhC,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,yBAAyB,CAACpB,SAAS,CAACqB,MAAM,GAAG,UAAUC,MAAM,EAAE,CAAE,CAAC;AACtE;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,CAAC;EAChC;AACJ;AACA;AACA;EACIF,MAAMA,CAACC,MAAM,EAAE;IACX,OAAOA,MAAM,CAACL,GAAG;EACrB;AACJ;AACAM,6BAA6B,CAACnB,IAAI,GAAG,SAASoB,qCAAqCA,CAACjB,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIgB,6BAA6B,EAAE,CAAC;AAAE,CAAC;AAC7IA,6BAA6B,CAACd,KAAK,GAAG,aAAcX,MAAM,CAACY,kBAAkB,CAAC;EAAEC,KAAK,EAAEY,6BAA6B;EAAEX,OAAO,EAAEW,6BAA6B,CAACnB;AAAK,CAAC,CAAC;AACpK,CAAC,YAAY;EAAE,CAAC,OAAOS,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAACS,6BAA6B,EAAE,CAAC;IACnHR,IAAI,EAAErC;EACV,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+C,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACpB,IAAID,EAAE,KAAKC,EAAE,EACT,OAAO,IAAI;EACf,IAAID,EAAE,KAAK,IAAI,IAAIC,EAAE,KAAK,IAAI,EAC1B,OAAO,KAAK;EAChB,IAAID,EAAE,KAAKA,EAAE,IAAIC,EAAE,KAAKA,EAAE,EACtB,OAAO,IAAI,CAAC,CAAC;EACjB;EACA;EACA,IAAIC,EAAE,GAAG,OAAOF,EAAE;EAClB;EACA,IAAIG,EAAE,GAAG,OAAOF,EAAE;EAClB;EACA,IAAIG,MAAM;EACV;EACA,IAAIb,GAAG;EACP;EACA,IAAIc,MAAM;EACV,IAAIH,EAAE,IAAIC,EAAE,IAAID,EAAE,IAAI,QAAQ,EAAE;IAC5B,IAAII,KAAK,CAACC,OAAO,CAACP,EAAE,CAAC,EAAE;MACnB,IAAI,CAACM,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAClB,OAAO,KAAK;MAChB,IAAI,CAACG,MAAM,GAAGJ,EAAE,CAACI,MAAM,KAAKH,EAAE,CAACG,MAAM,EAAE;QACnC,KAAKb,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGa,MAAM,EAAEb,GAAG,EAAE,EAAE;UAC/B,IAAI,CAACQ,MAAM,CAACC,EAAE,CAACT,GAAG,CAAC,EAAEU,EAAE,CAACV,GAAG,CAAC,CAAC,EACzB,OAAO,KAAK;QACpB;QACA,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,IAAIe,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAAE;QACnB,OAAO,KAAK;MAChB;MACAI,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC5B,KAAKlB,GAAG,IAAIS,EAAE,EAAE;QACZ,IAAI,CAACD,MAAM,CAACC,EAAE,CAACT,GAAG,CAAC,EAAEU,EAAE,CAACV,GAAG,CAAC,CAAC,EAAE;UAC3B,OAAO,KAAK;QAChB;QACAc,MAAM,CAACd,GAAG,CAAC,GAAG,IAAI;MACtB;MACA,KAAKA,GAAG,IAAIU,EAAE,EAAE;QACZ,IAAI,EAAEV,GAAG,IAAIc,MAAM,CAAC,IAAI,OAAOJ,EAAE,CAACV,GAAG,CAAC,KAAK,WAAW,EAAE;UACpD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,SAASA,CAACC,KAAK,EAAE;EACtB,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI;AACzD;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACpB,OAAQA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACP,KAAK,CAACC,OAAO,CAACM,IAAI,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B;EACA,IAAIC,MAAM,GAAGT,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EACtC,IAAIH,QAAQ,CAACG,MAAM,CAAC,IAAIH,QAAQ,CAACI,MAAM,CAAC,EAAE;IACtCR,MAAM,CAACW,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO;IAAE;AACrC;AACA;AACA;IACS7B,GAAG,IAAK;MACL,IAAIqB,QAAQ,CAACI,MAAM,CAACzB,GAAG,CAAC,CAAC,EAAE;QACvB,IAAI,EAAEA,GAAG,IAAIwB,MAAM,CAAC,EAAE;UAClBP,MAAM,CAACU,MAAM,CAACD,MAAM,EAAE;YAAE,CAAC1B,GAAG,GAAGyB,MAAM,CAACzB,GAAG;UAAE,CAAC,CAAC;QACjD,CAAC,MACI;UACD0B,MAAM,CAAC1B,GAAG,CAAC,GAAGuB,SAAS,CAACC,MAAM,CAACxB,GAAG,CAAC,EAAEyB,MAAM,CAACzB,GAAG,CAAC,CAAC;QACrD;MACJ,CAAC,MACI;QACDiB,MAAM,CAACU,MAAM,CAACD,MAAM,EAAE;UAAE,CAAC1B,GAAG,GAAGyB,MAAM,CAACzB,GAAG;QAAE,CAAC,CAAC;MACjD;IACJ,CAAE,CAAC;EACP;EACA,OAAO0B,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,CAAC;AAEtB,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,eAAe,CAAC/C,SAAS,CAACgD,WAAW,GAAG,UAAUC,IAAI,EAAE3B,MAAM,EAAE,CAAE,CAAC;EACnE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIyB,eAAe,CAAC/C,SAAS,CAACkD,QAAQ,GAAG,UAAUT,MAAM,EAAExB,GAAG,EAAE,CAAE,CAAC;AACnE;AACA,MAAMkC,sBAAsB,SAASJ,eAAe,CAAC;EACjDK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,eAAe,GAAG,uBAAuB;EAClD;EACA;AACJ;AACA;AACA;AACA;EACIN,WAAWA,CAACC,IAAI,EAAE3B,MAAM,EAAE;IACtB;IACA,IAAIiC,MAAM;IACV,IAAI,OAAON,IAAI,KAAK,QAAQ,EAAE;MAC1BM,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACP,IAAI,EAAE3B,MAAM,CAAC;IACjD,CAAC,MACI,IAAI,OAAO2B,IAAI,KAAK,UAAU,EAAE;MACjCM,MAAM,GAAG,IAAI,CAACE,mBAAmB,CAACR,IAAI,EAAE3B,MAAM,CAAC;IACnD,CAAC,MACI;MACD;MACAiC,MAAM,GAAI,gBAAkBN,IAAM;IACtC;IACA,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIL,QAAQA,CAACT,MAAM,EAAExB,GAAG,EAAE;IAClB;IACA,IAAI4B,IAAI,GAAG,OAAO5B,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAACyC,KAAK,CAAC,GAAG,CAAC,GAAG,CAACzC,GAAG,CAAC;IAC3DA,GAAG,GAAG,EAAE;IACR,GAAG;MACCA,GAAG,IAAI4B,IAAI,CAACc,KAAK,CAAC,CAAC;MACnB,IAAIvB,SAAS,CAACK,MAAM,CAAC,IAAIL,SAAS,CAACK,MAAM,CAACxB,GAAG,CAAC,CAAC,KAAK,OAAOwB,MAAM,CAACxB,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC4B,IAAI,CAACf,MAAM,CAAC,EAAE;QAClGW,MAAM,GAAGA,MAAM,CAACxB,GAAG,CAAC;QACpBA,GAAG,GAAG,EAAE;MACZ,CAAC,MACI,IAAI,CAAC4B,IAAI,CAACf,MAAM,EAAE;QACnBW,MAAM,GAAGmB,SAAS;MACtB,CAAC,MACI;QACD3C,GAAG,IAAI,GAAG;MACd;IACJ,CAAC,QAAQ4B,IAAI,CAACf,MAAM;IACpB,OAAOW,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,mBAAmBA,CAACI,EAAE,EAAEvC,MAAM,EAAE;IAC5B,OAAOuC,EAAE,CAACvC,MAAM,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkC,iBAAiBA,CAACP,IAAI,EAAE3B,MAAM,EAAE;IAC5B,IAAI,CAACA,MAAM,EAAE;MACT,OAAO2B,IAAI;IACf;IACA,OAAOA,IAAI,CAACa,OAAO,CAAC,IAAI,CAACR,eAAe;IAAG;AACnD;AACA;AACA;AACA;IACQ,CAACS,SAAS,EAAEC,CAAC,KAAK;MACd;MACA,IAAIC,CAAC,GAAG,IAAI,CAACf,QAAQ,CAAC5B,MAAM,EAAE0C,CAAC,CAAC;MAChC,OAAO5B,SAAS,CAAC6B,CAAC,CAAC,GAAGA,CAAC,GAAGF,SAAS;IACvC,CAAE,CAAC;EACP;AACJ;AACAZ,sBAAsB,CAAC/C,IAAI,GAAG,aAAc,YAAY;EAAE,IAAI8D,mCAAmC;EAAE,OAAO,SAASC,8BAA8BA,CAAC5D,CAAC,EAAE;IAAE,OAAO,CAAC2D,mCAAmC,KAAKA,mCAAmC,GAAGpE,MAAM,CAACU,qBAAqB,CAAC2C,sBAAsB,CAAC,CAAC,EAAE5C,CAAC,IAAI4C,sBAAsB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACxUA,sBAAsB,CAAC1C,KAAK,GAAG,aAAcX,MAAM,CAACY,kBAAkB,CAAC;EAAEC,KAAK,EAAEwC,sBAAsB;EAAEvC,OAAO,EAAEuC,sBAAsB,CAAC/C;AAAK,CAAC,CAAC;AAC/I,CAAC,YAAY;EAAE,CAAC,OAAOS,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAACqC,sBAAsB,EAAE,CAAC;IAC5GpC,IAAI,EAAErC;EACV,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB,IAAI,KAAK,EAAE;EACP;EACAyE,sBAAsB,CAACnD,SAAS,CAACsD,eAAe;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,iBAAiB,CAAC;AAExB,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;AACA;AACA;EACIA,iBAAiB,CAACpE,SAAS,CAACqE,OAAO,GAAG,UAAUhC,KAAK,EAAEnC,IAAI,EAAE,CAAE,CAAC;EAChE;AACJ;AACA;AACA;AACA;AACA;EACIkE,iBAAiB,CAACpE,SAAS,CAACsE,mBAAmB,GAAG,UAAUC,YAAY,EAAErE,IAAI,EAAE,CAAE,CAAC;AACvF;AACA;AACA;AACA;AACA,MAAMsE,qBAAqB,SAASJ,iBAAiB,CAAC;EAClD;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAAChC,KAAK,EAAEnC,IAAI,EAAE;IACjB,OAAOmC,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACIiC,mBAAmBA,CAACC,YAAY,EAAErE,IAAI,EAAE;IACpC,OAAOqE,YAAY;EACvB;AACJ;AACAC,qBAAqB,CAACpE,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIqE,kCAAkC;EAAE,OAAO,SAASC,6BAA6BA,CAACnE,CAAC,EAAE;IAAE,OAAO,CAACkE,kCAAkC,KAAKA,kCAAkC,GAAG3E,MAAM,CAACU,qBAAqB,CAACgE,qBAAqB,CAAC,CAAC,EAAEjE,CAAC,IAAIiE,qBAAqB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACjUA,qBAAqB,CAAC/D,KAAK,GAAG,aAAcX,MAAM,CAACY,kBAAkB,CAAC;EAAEC,KAAK,EAAE6D,qBAAqB;EAAE5D,OAAO,EAAE4D,qBAAqB,CAACpE;AAAK,CAAC,CAAC;AAC5I,CAAC,YAAY;EAAE,CAAC,OAAOS,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAAC0D,qBAAqB,EAAE,CAAC;IAC3GzD,IAAI,EAAErC;EACV,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA,MAAMiG,cAAc,CAAC;EACjBvB,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACwB,WAAW,GAAG,IAAI,CAACC,WAAW;IACnC;AACR;AACA;IACQ,IAAI,CAACN,YAAY,GAAG,CAAC,CAAC;IACtB;AACR;AACA;IACQ,IAAI,CAACO,KAAK,GAAG,EAAE;IACf;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,IAAIpG,YAAY,CAAC,CAAC;IAC7C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqG,YAAY,GAAG,IAAIrG,YAAY,CAAC,CAAC;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACsG,mBAAmB,GAAG,IAAItG,YAAY,CAAC,CAAC;EACjD;AACJ;AACA,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;EACIgG,cAAc,CAAC3E,SAAS,CAAC6E,WAAW;EACpC;AACJ;AACA;AACA;EACIF,cAAc,CAAC3E,SAAS,CAAC4E,WAAW;EACpC;AACJ;AACA;AACA;EACID,cAAc,CAAC3E,SAAS,CAACuE,YAAY;EACrC;AACJ;AACA;AACA;EACII,cAAc,CAAC3E,SAAS,CAAC8E,KAAK;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;EACIH,cAAc,CAAC3E,SAAS,CAAC+E,mBAAmB;EAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,cAAc,CAAC3E,SAAS,CAACgF,YAAY;EACrC;AACJ;AACA;AACA;AACA;AACA;AACA;EACIL,cAAc,CAAC3E,SAAS,CAACiF,mBAAmB;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,IAAItG,cAAc,CAAC,WAAW,CAAC;AACjD;AACA,MAAMuG,gBAAgB,GAAG,IAAIvG,cAAc,CAAC,kBAAkB,CAAC;AAC/D;AACA,MAAMwG,gBAAgB,GAAG,IAAIxG,cAAc,CAAC,kBAAkB,CAAC;AAC/D;AACA,MAAMyG,UAAU,GAAG,IAAIzG,cAAc,CAAC,YAAY,CAAC;AACnD;AACA;AACA;AACA,SAAS0G,sBAAsBA,CAAA,EAAG,CAAE;AACpC,IAAI,KAAK,EAAE;EACP;EACAA,sBAAsB,CAACtF,SAAS,CAACuE,YAAY;EAC7C;EACAe,sBAAsB,CAACtF,SAAS,CAACE,IAAI;AACzC;AACA;AACA;AACA;AACA,SAASqF,eAAeA,CAAA,EAAG,CAAE;AAC7B,IAAI,KAAK,EAAE;EACP;EACAA,eAAe,CAACvF,SAAS,CAACE,IAAI;EAC9B;EACAqF,eAAe,CAACvF,SAAS,CAACuE,YAAY;AAC1C;AACA;AACA;AACA;AACA,SAASiB,sBAAsBA,CAAA,EAAG,CAAE;AACpC,IAAI,KAAK,EAAE;EACP;EACAA,sBAAsB,CAACxF,SAAS,CAACE,IAAI;EACrC;EACAsF,sBAAsB,CAACxF,SAAS,CAACuE,YAAY;AACjD;AACA,MAAMkB,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrC,WAAWA,CAACsC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,yBAAyB,EAAEC,cAAc,GAAG,IAAI,EAAEC,OAAO,GAAG,KAAK,EAAEC,MAAM,GAAG,KAAK,EAAEC,eAAe,EAAE;IACpJ,IAAI,CAACR,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,oBAAoB,GAAG,IAAIzH,YAAY,CAAC,CAAC;IAC9C,IAAI,CAAC0H,aAAa,GAAG,IAAI1H,YAAY,CAAC,CAAC;IACvC,IAAI,CAAC2H,oBAAoB,GAAG,IAAI3H,YAAY,CAAC,CAAC;IAC9C,IAAI,CAAC4H,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;IAC9B;IACA,IAAIP,eAAe,EAAE;MACjB,IAAI,CAACQ,cAAc,CAACR,eAAe,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAInB,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACiB,OAAO,GAAG,IAAI,CAACI,oBAAoB,GAAG,IAAI,CAACV,KAAK,CAACX,mBAAmB;EACpF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACgB,OAAO,GAAG,IAAI,CAACK,aAAa,GAAG,IAAI,CAACX,KAAK,CAACV,YAAY;EACtE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACe,OAAO,GAAG,IAAI,CAACM,oBAAoB,GAAG,IAAI,CAACZ,KAAK,CAACT,mBAAmB;EACpF;EACA;AACJ;AACA;AACA;EACI,IAAIJ,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACmB,OAAO,GAAG,IAAI,CAACW,YAAY,GAAG,IAAI,CAACjB,KAAK,CAACb,WAAW;EACpE;EACA;AACJ;AACA;AACA;EACI,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACmB,OAAO,EAAE;MACd,IAAI,CAACW,YAAY,GAAG9B,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACa,KAAK,CAACb,WAAW,GAAGA,WAAW;IACxC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAID,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoB,OAAO,GAAG,IAAI,CAACY,YAAY,GAAG,IAAI,CAAClB,KAAK,CAACd,WAAW;EACpE;EACA;AACJ;AACA;AACA;EACI,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACoB,OAAO,EAAE;MACd,IAAI,CAACY,YAAY,GAAGhC,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACc,KAAK,CAACd,WAAW,GAAGA,WAAW;IACxC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACkB,OAAO,GAAG,IAAI,CAACO,MAAM,GAAG,IAAI,CAACb,KAAK,CAACZ,KAAK;EACxD;EACA;AACJ;AACA;AACA;EACI,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACkB,OAAO,EAAE;MACd,IAAI,CAACO,MAAM,GAAGzB,KAAK;IACvB,CAAC,MACI;MACD,IAAI,CAACY,KAAK,CAACZ,KAAK,GAAGA,KAAK;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIP,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACyB,OAAO,GAAG,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACd,KAAK,CAACnB,YAAY;EACtE;EACA;AACJ;AACA;AACA;EACI,IAAIA,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACyB,OAAO,EAAE;MACd,IAAI,CAACQ,aAAa,GAAGjC,YAAY;IACrC,CAAC,MACI;MACD,IAAI,CAACmB,KAAK,CAACnB,YAAY,GAAGA,YAAY;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACImC,cAAcA,CAACxG,IAAI,EAAE;IACjB,IAAIA,IAAI,KAAK,IAAI,CAAC2E,WAAW,EAAE;MAC3B;IACJ;IACA;IACA,IAAIsB,OAAO,GAAG,IAAI,CAACU,oBAAoB,CAAC3G,IAAI,CAAC;IAC7C,IAAI,OAAOiG,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,IAAI,CAACtB,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACA,WAAW,GAAG3E,IAAI;MAC3B;MACAiG,OAAO,CAACW,IAAI,CAACrH,IAAI,CAAC,CAAC,CAAC,CAAC,CAChBsH,SAAS;MAAE;AAC5B;AACA;AACA;MACaC,GAAG,IAAK;QACL,IAAI,CAACC,iBAAiB,CAAC/G,IAAI,CAAC;MAChC,CAAE,CAAC;IACP,CAAC,MACI;MAAE;MACH,IAAI,CAAC+G,iBAAiB,CAAC/G,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;AACA;EACIgH,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrC,WAAW;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACIsC,GAAGA,CAACjH,IAAI,EAAE;IACN;IACA,IAAIA,IAAI,KAAK,IAAI,CAAC0E,WAAW,EAAE;MAC3B,OAAOxF,EAAE,CAAC,IAAI,CAACmF,YAAY,CAACrE,IAAI,CAAC,CAAC;IACtC;IACA;IACA,IAAIiG,OAAO,GAAG,IAAI,CAACU,oBAAoB,CAAC3G,IAAI,CAAC;IAC7C,IAAI,OAAOiG,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,CAAC,IAAI,CAACvB,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG1E,IAAI;MAC3B;MACAiG,OAAO,CAACW,IAAI,CAACrH,IAAI,CAAC,CAAC,CAAC,CAAC,CAChBsH,SAAS;MAAE;AAC5B;AACA;AACA;MACaC,GAAG,IAAK;QACL,IAAI,CAACI,UAAU,CAAClH,IAAI,CAAC;MACzB,CAAE,CAAC;MACH,OAAOiG,OAAO;IAClB,CAAC,MACI;MAAE;MACH,IAAI,CAACiB,UAAU,CAAClH,IAAI,CAAC;MACrB,OAAOd,EAAE,CAAC,IAAI,CAACmF,YAAY,CAACrE,IAAI,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2G,oBAAoBA,CAAC3G,IAAI,EAAE;IACvB;IACA,IAAIiG,OAAO;IACX;IACA,IAAI,OAAO,IAAI,CAAC5B,YAAY,CAACrE,IAAI,CAAC,KAAK,WAAW,IAAI,IAAI,CAAC+F,MAAM,EAAE;MAC/D,IAAI,CAACQ,oBAAoB,CAACvG,IAAI,CAAC,GAAG,IAAI,CAACuG,oBAAoB,CAACvG,IAAI,CAAC,IAAI,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;MAC9FiG,OAAO,GAAG,IAAI,CAACM,oBAAoB,CAACvG,IAAI,CAAC;IAC7C;IACA,OAAOiG,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlG,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAACiG,OAAO,GAAG,IAAI;IACnB;IACA,MAAMkB,mBAAmB,GAAG,IAAI,CAAC1B,aAAa,CAAC1F,cAAc,CAACC,IAAI,CAAC,CAAC4G,IAAI,CAACpH,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACjG,IAAI,CAAC4H,mBAAmB,GAAGA,mBAAmB,CAACP,IAAI,CAACnH,GAAG;IAAE;AACjE;AACA;AACA;IACSqH,GAAG,IAAK,IAAI,CAACpB,QAAQ,CAACtB,mBAAmB,CAAC0C,GAAG,EAAE9G,IAAI,CAAE,CAAC,EAAER,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACjF,IAAI,CAAC4H,mBAAmB,CACnBN,SAAS,CAAC;MACXO,IAAI;MAAG;AACnB;AACA;AACA;MACaN,GAAG,IAAK;QACL,IAAI,CAACzC,YAAY,CAACrE,IAAI,CAAC,GAAG,IAAI,CAAC+F,MAAM,IAAI,IAAI,CAAC1B,YAAY,CAACrE,IAAI,CAAC,GAAGgC,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEoE,GAAG,CAAC,EAAE,IAAI,CAACzC,YAAY,CAACrE,IAAI,CAAC,CAAC,GAAG8G,GAAG;QACvI,IAAI,CAACO,WAAW,CAAC,CAAC;QAClB,IAAI,CAACpB,OAAO,GAAG,KAAK;MACxB,CAAE;MACFqB,KAAK;MAAG;AACpB;AACA;AACA;MACaC,GAAG,IAAK;QACL,IAAI,CAACtB,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;IACF,OAAOkB,mBAAmB;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,cAAcA,CAACxH,IAAI,EAAEqE,YAAY,EAAEoD,WAAW,GAAG,KAAK,EAAE;IACpDpD,YAAY,GAAG,IAAI,CAACqB,QAAQ,CAACtB,mBAAmB,CAACC,YAAY,EAAErE,IAAI,CAAC;IACpE,IAAI,CAACyH,WAAW,IAAI,IAAI,CAAC1B,MAAM,KAAK,IAAI,CAAC1B,YAAY,CAACrE,IAAI,CAAC,EAAE;MACzD,IAAI,CAACqE,YAAY,CAACrE,IAAI,CAAC,GAAGsC,SAAS,CAAC,IAAI,CAAC+B,YAAY,CAACrE,IAAI,CAAC,EAAEqE,YAAY,CAAC;IAC9E,CAAC,MACI;MACD,IAAI,CAACA,YAAY,CAACrE,IAAI,CAAC,GAAGqE,YAAY;IAC1C;IACA,IAAI,CAACgD,WAAW,CAAC,CAAC;IAClB,IAAI,CAACxC,mBAAmB,CAAC6C,IAAI,CAAC;MAAE1H,IAAI,EAAEA,IAAI;MAAEqE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACrE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;AACA;EACI2H,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/C,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIgD,QAAQA,CAAChD,KAAK,EAAE;IACZA,KAAK,CAAChC,OAAO;IAAE;AACvB;AACA;AACA;IACS5C,IAAI,IAAK;MACN,IAAI,IAAI,CAAC4E,KAAK,CAACiD,OAAO,CAAC7H,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACjC,IAAI,CAAC4E,KAAK,CAACkD,IAAI,CAAC9H,IAAI,CAAC;MACzB;IACJ,CAAE,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;EACIqH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,QAAQ,CAAC5F,MAAM,CAACW,IAAI,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0D,eAAeA,CAAC1D,YAAY,EAAEtD,GAAG,EAAEE,iBAAiB,EAAE;IAClD;IACA,IAAI6F,GAAG;IACP,IAAI/F,GAAG,YAAYe,KAAK,EAAE;MACtB;MACA,IAAIuB,MAAM,GAAG,CAAC,CAAC;MACf;MACA,IAAI2E,WAAW,GAAG,KAAK;MACvB,KAAK,IAAIC,CAAC,IAAIlH,GAAG,EAAE;QACfsC,MAAM,CAAC4E,CAAC,CAAC,GAAG,IAAI,CAACF,eAAe,CAAC1D,YAAY,EAAE4D,CAAC,EAAEhH,iBAAiB,CAAC;QACpE,IAAI9B,YAAY,CAACkE,MAAM,CAAC4E,CAAC,CAAC,CAAC,EAAE;UACzBD,WAAW,GAAG,IAAI;QACtB;MACJ;MACA,IAAIA,WAAW,EAAE;QACb;QACA,MAAME,OAAO,GAAGnH,GAAG,CAACtB,GAAG;QAAE;AACzC;AACA;AACA;QACgBwI,CAAC,IAAI9I,YAAY,CAACkE,MAAM,CAAC4E,CAAC,CAAC,CAAC,GAAG5E,MAAM,CAAC4E,CAAC,CAAC,GAAG/I,EAAE,EAAE,gBAAkBmE,MAAM,CAAC4E,CAAC,CAAG,CAAE,CAAC;QAC/E,OAAO7I,QAAQ,CAAC8I,OAAO,CAAC,CAACtB,IAAI,CAACnH,GAAG;QAAE;AACnD;AACA;AACA;QACiB0I,GAAG,IAAK;UACL;UACA,IAAIC,GAAG,GAAG,CAAC,CAAC;UACZD,GAAG,CAACvF,OAAO;UAAE;AACjC;AACA;AACA;AACA;UACoB,CAACT,KAAK,EAAEkG,KAAK,KAAK;YACdD,GAAG,CAACrH,GAAG,CAACsH,KAAK,CAAC,CAAC,GAAGlG,KAAK;UAC3B,CAAE,CAAC;UACH,OAAOiG,GAAG;QACd,CAAE,CAAC,CAAC;MACR;MACA,OAAO/E,MAAM;IACjB;IACA,IAAIgB,YAAY,EAAE;MACdyC,GAAG,GAAG,IAAI,CAACnB,MAAM,CAAC7C,WAAW,CAAC,IAAI,CAAC6C,MAAM,CAAC3C,QAAQ,CAACqB,YAAY,EAAEtD,GAAG,CAAC,EAAEE,iBAAiB,CAAC;IAC7F;IACA,IAAI,OAAO6F,GAAG,KAAK,WAAW,IAAI,IAAI,CAACnC,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,KAAK,IAAI,CAACD,WAAW,IAAI,IAAI,CAACmB,cAAc,EAAE;MACxHiB,GAAG,GAAG,IAAI,CAACnB,MAAM,CAAC7C,WAAW,CAAC,IAAI,CAAC6C,MAAM,CAAC3C,QAAQ,CAAC,IAAI,CAACqB,YAAY,CAAC,IAAI,CAACM,WAAW,CAAC,EAAE5D,GAAG,CAAC,EAAEE,iBAAiB,CAAC;IACpH;IACA,IAAI,OAAO6F,GAAG,KAAK,WAAW,EAAE;MAC5B;MACA,IAAI1F,MAAM,GAAG;QAAEL,GAAG;QAAEC,gBAAgB,EAAE;MAAK,CAAC;MAC5C,IAAI,OAAOC,iBAAiB,KAAK,WAAW,EAAE;QAC1CG,MAAM,CAACH,iBAAiB,GAAGA,iBAAiB;MAChD;MACA6F,GAAG,GAAG,IAAI,CAAClB,yBAAyB,CAACzE,MAAM,CAACC,MAAM,CAAC;IACvD;IACA,OAAO,OAAO0F,GAAG,KAAK,WAAW,GAAGA,GAAG,GAAG/F,GAAG;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuH,GAAGA,CAACvH,GAAG,EAAEE,iBAAiB,EAAE;IACxB,IAAI,CAACiB,SAAS,CAACnB,GAAG,CAAC,IAAI,CAACA,GAAG,CAACa,MAAM,EAAE;MAChC,MAAM,IAAI2G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA;IACA,IAAI,IAAI,CAACtC,OAAO,EAAE;MACd,OAAO,IAAI,CAACkB,mBAAmB,CAACP,IAAI,CAAClH,SAAS;MAAE;AAC5D;AACA;AACA;MACaoH,GAAG,IAAK;QACLA,GAAG,GAAG,IAAI,CAACiB,eAAe,CAACjB,GAAG,EAAE/F,GAAG,EAAEE,iBAAiB,CAAC;QACvD,OAAO9B,YAAY,CAAC2H,GAAG,CAAC,GAAGA,GAAG,GAAG5H,EAAE,CAAC4H,GAAG,CAAC;MAC5C,CAAE,CAAC,CAAC;IACR,CAAC,MACI;MACD;MACA,IAAIA,GAAG,GAAG,IAAI,CAACiB,eAAe,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,CAACK,WAAW,CAAC,EAAE3D,GAAG,EAAEE,iBAAiB,CAAC;MAC3F,OAAO9B,YAAY,CAAC2H,GAAG,CAAC,GAAGA,GAAG,GAAG5H,EAAE,CAAC4H,GAAG,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0B,4BAA4BA,CAACzH,GAAG,EAAEE,iBAAiB,EAAE;IACjD,IAAI,CAACiB,SAAS,CAACnB,GAAG,CAAC,IAAI,CAACA,GAAG,CAACa,MAAM,EAAE;MAChC,MAAM,IAAI2G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA,OAAOlJ,MAAM,CAACC,KAAK;IAAE;AAC7B;AACA;IACQ,MAAM,IAAI,CAACgJ,GAAG,CAACvH,GAAG,EAAEE,iBAAiB,CAAE,CAAC,EAAE,IAAI,CAAC4D,mBAAmB,CAAC+B,IAAI,CAACjH,SAAS;IAAE;AAC3F;AACA;AACA;IACS8I,KAAK,IAAK;MACP;MACA,MAAM3B,GAAG,GAAG,IAAI,CAACiB,eAAe,CAACU,KAAK,CAACpE,YAAY,EAAEtD,GAAG,EAAEE,iBAAiB,CAAC;MAC5E,IAAI,OAAO6F,GAAG,CAACD,SAAS,KAAK,UAAU,EAAE;QACrC,OAAOC,GAAG;MACd,CAAC,MACI;QACD,OAAO5H,EAAE,CAAC4H,GAAG,CAAC;MAClB;IACJ,CAAE,CAAC,CAAC,CAAC;EACT;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI4B,MAAMA,CAAC3H,GAAG,EAAEE,iBAAiB,EAAE;IAC3B,IAAI,CAACiB,SAAS,CAACnB,GAAG,CAAC,IAAI,CAACA,GAAG,CAACa,MAAM,EAAE;MAChC,MAAM,IAAI2G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA,OAAOlJ,MAAM,CAACC,KAAK;IAAE;AAC7B;AACA;IACQ,MAAM,IAAI,CAACgJ,GAAG,CAACvH,GAAG,EAAEE,iBAAiB,CAAE,CAAC,EAAE,IAAI,CAAC6D,YAAY,CAAC8B,IAAI,CAACjH,SAAS;IAAE;AACpF;AACA;AACA;IACS8I,KAAK,IAAK;MACP;MACA,MAAM3B,GAAG,GAAG,IAAI,CAACiB,eAAe,CAACU,KAAK,CAACpE,YAAY,EAAEtD,GAAG,EAAEE,iBAAiB,CAAC;MAC5E,OAAO9B,YAAY,CAAC2H,GAAG,CAAC,GAAGA,GAAG,GAAG5H,EAAE,CAAC4H,GAAG,CAAC;IAC5C,CAAE,CAAC,CAAC,CAAC;EACT;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6B,OAAOA,CAAC5H,GAAG,EAAEE,iBAAiB,EAAE;IAC5B,IAAI,CAACiB,SAAS,CAACnB,GAAG,CAAC,IAAI,CAACA,GAAG,CAACa,MAAM,EAAE;MAChC,MAAM,IAAI2G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA;IACA,IAAIzB,GAAG,GAAG,IAAI,CAACiB,eAAe,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,CAACK,WAAW,CAAC,EAAE3D,GAAG,EAAEE,iBAAiB,CAAC;IAC3F,IAAI9B,YAAY,CAAC2H,GAAG,CAAC,EAAE;MACnB,IAAI/F,GAAG,YAAYe,KAAK,EAAE;QACtB;QACA,IAAIsG,GAAG,GAAG,CAAC,CAAC;QACZrH,GAAG,CAAC6B,OAAO;QAAE;AAC7B;AACA;AACA;AACA;QACgB,CAACT,KAAK,EAAEkG,KAAK,KAAK;UACdD,GAAG,CAACrH,GAAG,CAACsH,KAAK,CAAC,CAAC,GAAGtH,GAAG,CAACsH,KAAK,CAAC;QAChC,CAAE,CAAC;QACH,OAAOD,GAAG;MACd;MACA,OAAOrH,GAAG;IACd,CAAC,MACI;MACD,OAAO+F,GAAG;IACd;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8B,GAAGA,CAAC7H,GAAG,EAAEoB,KAAK,EAAEnC,IAAI,GAAG,IAAI,CAAC0E,WAAW,EAAE;IACrC,IAAI,CAACL,YAAY,CAACrE,IAAI,CAAC,CAACe,GAAG,CAAC,GAAG,IAAI,CAAC2E,QAAQ,CAACvB,OAAO,CAAChC,KAAK,EAAEnC,IAAI,CAAC;IACjE,IAAI,CAACqH,WAAW,CAAC,CAAC;IAClB,IAAI,CAACxC,mBAAmB,CAAC6C,IAAI,CAAC;MAAE1H,IAAI,EAAEA,IAAI;MAAEqE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACrE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkH,UAAUA,CAAClH,IAAI,EAAE;IACb,IAAI,CAAC0E,WAAW,GAAG1E,IAAI;IACvB,IAAI,CAAC8E,YAAY,CAAC4C,IAAI,CAAC;MAAE1H,IAAI,EAAEA,IAAI;MAAEqE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACrE,IAAI;IAAE,CAAC,CAAC;IAC7E;IACA,IAAI,IAAI,CAAC2E,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACoC,iBAAiB,CAAC/G,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+G,iBAAiBA,CAAC/G,IAAI,EAAE;IACpB,IAAI,CAAC2E,WAAW,GAAG3E,IAAI;IACvB,IAAI,CAAC+E,mBAAmB,CAAC2C,IAAI,CAAC;MAAE1H,IAAI,EAAEA,IAAI;MAAEqE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACrE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;AACA;AACA;EACI6I,UAAUA,CAAC7I,IAAI,EAAE;IACb,IAAI,CAAC8I,SAAS,CAAC9I,IAAI,CAAC;IACpB,OAAO,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACI8I,SAASA,CAAC9I,IAAI,EAAE;IACZ,IAAI,CAACuG,oBAAoB,CAACvG,IAAI,CAAC,GAAG0D,SAAS;IAC3C,IAAI,CAACW,YAAY,CAACrE,IAAI,CAAC,GAAG0D,SAAS;EACvC;EACA;AACJ;AACA;AACA;EACIqF,cAAcA,CAAA,EAAG;IACb,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAOvF,SAAS;IACpB;IACA;IACA,IAAIwF,WAAW,GAAGF,MAAM,CAACC,SAAS,CAACE,SAAS,GAAGH,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACnFD,WAAW,GAAGA,WAAW,IAAIF,MAAM,CAACC,SAAS,CAACG,QAAQ,IAAIJ,MAAM,CAACC,SAAS,CAACI,eAAe,IAAIL,MAAM,CAACC,SAAS,CAACK,YAAY;IAC3H,IAAI,OAAOJ,WAAW,KAAK,WAAW,EAAE;MACpC,OAAOxF,SAAS;IACpB;IACA,IAAIwF,WAAW,CAACrB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCqB,WAAW,GAAGA,WAAW,CAAC1F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,IAAI0F,WAAW,CAACrB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCqB,WAAW,GAAGA,WAAW,CAAC1F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAO0F,WAAW;EACtB;EACA;AACJ;AACA;AACA;EACIK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,OAAOP,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAOvF,SAAS;IACpB;IACA;IACA,IAAI8F,kBAAkB,GAAGR,MAAM,CAACC,SAAS,CAACE,SAAS,GAAGH,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAC1FK,kBAAkB,GAAGA,kBAAkB,IAAIR,MAAM,CAACC,SAAS,CAACG,QAAQ,IAAIJ,MAAM,CAACC,SAAS,CAACI,eAAe,IAAIL,MAAM,CAACC,SAAS,CAACK,YAAY;IACzI,OAAOE,kBAAkB;EAC7B;AACJ;AACAjE,gBAAgB,CAACrF,IAAI,GAAG,SAASuJ,wBAAwBA,CAACpJ,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIkF,gBAAgB,EAAE3F,MAAM,CAAC8J,QAAQ,CAACjF,cAAc,CAAC,EAAE7E,MAAM,CAAC8J,QAAQ,CAAC7J,eAAe,CAAC,EAAED,MAAM,CAAC8J,QAAQ,CAACxF,iBAAiB,CAAC,EAAEtE,MAAM,CAAC8J,QAAQ,CAAC7G,eAAe,CAAC,EAAEjD,MAAM,CAAC8J,QAAQ,CAACxI,yBAAyB,CAAC,EAAEtB,MAAM,CAAC8J,QAAQ,CAACzE,gBAAgB,CAAC,EAAErF,MAAM,CAAC8J,QAAQ,CAAC1E,SAAS,CAAC,EAAEpF,MAAM,CAAC8J,QAAQ,CAACvE,UAAU,CAAC,EAAEvF,MAAM,CAAC8J,QAAQ,CAACxE,gBAAgB,CAAC,CAAC;AAAE,CAAC;AACxZK,gBAAgB,CAAChF,KAAK,GAAG,aAAcX,MAAM,CAACY,kBAAkB,CAAC;EAAEC,KAAK,EAAE8E,gBAAgB;EAAE7E,OAAO,EAAE6E,gBAAgB,CAACrF;AAAK,CAAC,CAAC;AAC7H;AACAqF,gBAAgB,CAACoE,cAAc,GAAG,MAAM,CACpC;EAAE9I,IAAI,EAAE4D;AAAe,CAAC,EACxB;EAAE5D,IAAI,EAAEhB;AAAgB,CAAC,EACzB;EAAEgB,IAAI,EAAEqD;AAAkB,CAAC,EAC3B;EAAErD,IAAI,EAAEgC;AAAgB,CAAC,EACzB;EAAEhC,IAAI,EAAEK;AAA0B,CAAC,EACnC;EAAEL,IAAI,EAAE+I,OAAO;EAAEC,UAAU,EAAE,CAAC;IAAEhJ,IAAI,EAAElC,MAAM;IAAEmL,IAAI,EAAE,CAAC7E,gBAAgB;EAAG,CAAC;AAAE,CAAC,EAC5E;EAAEpE,IAAI,EAAE+I,OAAO;EAAEC,UAAU,EAAE,CAAC;IAAEhJ,IAAI,EAAElC,MAAM;IAAEmL,IAAI,EAAE,CAAC9E,SAAS;EAAG,CAAC;AAAE,CAAC,EACrE;EAAEnE,IAAI,EAAE+I,OAAO;EAAEC,UAAU,EAAE,CAAC;IAAEhJ,IAAI,EAAElC,MAAM;IAAEmL,IAAI,EAAE,CAAC3E,UAAU;EAAG,CAAC;AAAE,CAAC,EACtE;EAAEtE,IAAI,EAAEkJ,MAAM;EAAEF,UAAU,EAAE,CAAC;IAAEhJ,IAAI,EAAElC,MAAM;IAAEmL,IAAI,EAAE,CAAC5E,gBAAgB;EAAG,CAAC;AAAE,CAAC,CAC9E;AACD,CAAC,YAAY;EAAE,CAAC,OAAOvE,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAAC2E,gBAAgB,EAAE,CAAC;IACtG1E,IAAI,EAAErC;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEqC,IAAI,EAAE4D;IAAe,CAAC,EAAE;MAAE5D,IAAI,EAAEhB;IAAgB,CAAC,EAAE;MAAEgB,IAAI,EAAEqD;IAAkB,CAAC,EAAE;MAAErD,IAAI,EAAEgC;IAAgB,CAAC,EAAE;MAAEhC,IAAI,EAAEK;IAA0B,CAAC,EAAE;MAAEL,IAAI,EAAE+I,OAAO;MAAEC,UAAU,EAAE,CAAC;QAC9LhJ,IAAI,EAAElC,MAAM;QACZmL,IAAI,EAAE,CAAC7E,gBAAgB;MAC3B,CAAC;IAAE,CAAC,EAAE;MAAEpE,IAAI,EAAE+I,OAAO;MAAEC,UAAU,EAAE,CAAC;QAChChJ,IAAI,EAAElC,MAAM;QACZmL,IAAI,EAAE,CAAC9E,SAAS;MACpB,CAAC;IAAE,CAAC,EAAE;MAAEnE,IAAI,EAAE+I,OAAO;MAAEC,UAAU,EAAE,CAAC;QAChChJ,IAAI,EAAElC,MAAM;QACZmL,IAAI,EAAE,CAAC3E,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEtE,IAAI,EAAEkJ,MAAM;MAAEF,UAAU,EAAE,CAAC;QAC/BhJ,IAAI,EAAElC,MAAM;QACZmL,IAAI,EAAE,CAAC5E,gBAAgB;MAC3B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACjC,IAAI,KAAK,EAAE;EACP;AACJ;AACA;AACA;EACIK,gBAAgB,CAACzF,SAAS,CAACqH,mBAAmB;EAC9C;AACJ;AACA;AACA;EACI5B,gBAAgB,CAACzF,SAAS,CAACmG,OAAO;EAClC;AACJ;AACA;AACA;EACIV,gBAAgB,CAACzF,SAAS,CAACoG,oBAAoB;EAC/C;AACJ;AACA;AACA;EACIX,gBAAgB,CAACzF,SAAS,CAACqG,aAAa;EACxC;AACJ;AACA;AACA;EACIZ,gBAAgB,CAACzF,SAAS,CAACsG,oBAAoB;EAC/C;AACJ;AACA;AACA;EACIb,gBAAgB,CAACzF,SAAS,CAAC2G,YAAY;EACvC;AACJ;AACA;AACA;EACIlB,gBAAgB,CAACzF,SAAS,CAAC4G,YAAY;EACvC;AACJ;AACA;AACA;EACInB,gBAAgB,CAACzF,SAAS,CAACuG,MAAM;EACjC;AACJ;AACA;AACA;EACId,gBAAgB,CAACzF,SAAS,CAACwG,aAAa;EACxC;AACJ;AACA;AACA;EACIf,gBAAgB,CAACzF,SAAS,CAACyG,oBAAoB;EAC/C;EACAhB,gBAAgB,CAACzF,SAAS,CAAC0F,KAAK;EAChC;EACAD,gBAAgB,CAACzF,SAAS,CAAC2F,aAAa;EACxC;EACAF,gBAAgB,CAACzF,SAAS,CAAC4F,QAAQ;EACnC;EACAH,gBAAgB,CAACzF,SAAS,CAAC6F,MAAM;EACjC;EACAJ,gBAAgB,CAACzF,SAAS,CAAC8F,yBAAyB;EACpD;AACJ;AACA;AACA;EACIL,gBAAgB,CAACzF,SAAS,CAAC+F,cAAc;EACzC;AACJ;AACA;AACA;EACIN,gBAAgB,CAACzF,SAAS,CAACgG,OAAO;EAClC;AACJ;AACA;AACA;EACIP,gBAAgB,CAACzF,SAAS,CAACiG,MAAM;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMiE,kBAAkB,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI9G,WAAWA,CAAClC,gBAAgB,EAAEiJ,OAAO,EAAEC,IAAI,EAAE;IACzC,IAAI,CAAClJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACiJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC,IAAI,CAACC,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACnJ,gBAAgB,CAAC6D,mBAAmB,CAACgC,SAAS;MAAE;AAC/F;AACA;AACA;MACa4B,KAAK,IAAK;QACP,IAAIA,KAAK,CAACzI,IAAI,KAAK,IAAI,CAACgB,gBAAgB,CAAC0D,WAAW,EAAE;UAClD,IAAI,CAAC0F,UAAU,CAAC,IAAI,EAAE3B,KAAK,CAACpE,YAAY,CAAC;QAC7C;MACJ,CAAE,CAAC;IACP;IACA;IACA,IAAI,CAAC,IAAI,CAACgG,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI,CAACrJ,gBAAgB,CAAC8D,YAAY,CAAC+B,SAAS;MAAE;AACjF;AACA;AACA;MACa4B,KAAK,IAAK;QACP,IAAI,CAAC2B,UAAU,CAAC,IAAI,EAAE3B,KAAK,CAACpE,YAAY,CAAC;MAC7C,CAAE,CAAC;IACP;IACA;IACA,IAAI,CAAC,IAAI,CAACiG,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACtJ,gBAAgB,CAAC+D,mBAAmB,CAAC8B,SAAS;MAAE;AAC/F;AACA;AACA;MACa4B,KAAK,IAAK;QACP,IAAI,CAAC2B,UAAU,CAAC,IAAI,CAAC;MACzB,CAAE,CAAC;IACP;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIG,SAASA,CAACxJ,GAAG,EAAE;IACf,IAAIA,GAAG,EAAE;MACL,IAAI,CAACA,GAAG,GAAGA,GAAG;MACd,IAAI,CAACqJ,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAII,eAAeA,CAACpJ,MAAM,EAAE;IACxB,IAAI,CAACG,MAAM,CAAC,IAAI,CAACkJ,aAAa,EAAErJ,MAAM,CAAC,EAAE;MACrC,IAAI,CAACqJ,aAAa,GAAGrJ,MAAM;MAC3B,IAAI,CAACgJ,UAAU,CAAC,IAAI,CAAC;IACzB;EACJ;EACA;AACJ;AACA;EACIM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIA,UAAUA,CAACO,WAAW,GAAG,KAAK,EAAEtG,YAAY,EAAE;IAC1C;IACA,IAAIuG,KAAK,GAAG,IAAI,CAACX,OAAO,CAACY,aAAa,CAACC,UAAU;IACjD;IACA,IAAI,CAACF,KAAK,CAAChJ,MAAM,EAAE;MACf;MACA,IAAI,CAACmJ,UAAU,CAAC,IAAI,CAACd,OAAO,CAACY,aAAa,EAAE,IAAI,CAAC9J,GAAG,CAAC;MACrD6J,KAAK,GAAG,IAAI,CAACX,OAAO,CAACY,aAAa,CAACC,UAAU;IACjD;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAAChJ,MAAM,EAAE,EAAEoJ,CAAC,EAAE;MACnC;MACA,IAAIC,IAAI,GAAGL,KAAK,CAACI,CAAC,CAAC;MACnB,IAAIC,IAAI,CAACC,QAAQ,KAAK,CAAC,EAAE;QAAE;QACvB;QACA;QACA,IAAInK,GAAG;QACP,IAAI4J,WAAW,EAAE;UACbM,IAAI,CAACE,OAAO,GAAG,IAAI;QACvB;QACA,IAAIjJ,SAAS,CAAC+I,IAAI,CAACG,SAAS,CAAC,EAAE;UAC3BrK,GAAG,GAAGkK,IAAI,CAACG,SAAS;QACxB,CAAC,MACI,IAAI,IAAI,CAACrK,GAAG,EAAE;UACfA,GAAG,GAAG,IAAI,CAACA,GAAG;QAClB,CAAC,MACI;UACD;UACA,IAAIsK,OAAO,GAAG,IAAI,CAACC,UAAU,CAACL,IAAI,CAAC;UACnC;UACA,IAAIM,cAAc,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UACnC,IAAID,cAAc,CAAC3J,MAAM,EAAE;YACvBqJ,IAAI,CAACG,SAAS,GAAGG,cAAc;YAC/B;YACA,IAAIF,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cAC/B1K,GAAG,GAAGwK,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D,CAAC,MACI,IAAIT,IAAI,CAACS,eAAe,EAAE;cAAE;cAC7B;cACA3K,GAAG,GAAGkK,IAAI,CAACS,eAAe,CAACF,IAAI,CAAC,CAAC;YACrC,CAAC,MACI,IAAIH,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cACpC;cACA1K,GAAG,GAAGwK,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D;UACJ;QACJ;QACA,IAAI,CAACC,WAAW,CAAC5K,GAAG,EAAEkK,IAAI,EAAE5G,YAAY,CAAC;MAC7C;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsH,WAAWA,CAAC5K,GAAG,EAAEkK,IAAI,EAAE5G,YAAY,EAAE;IACjC,IAAItD,GAAG,EAAE;MACL,IAAIkK,IAAI,CAACE,OAAO,KAAKpK,GAAG,IAAI,IAAI,CAAC6K,UAAU,KAAK,IAAI,CAACnB,aAAa,EAAE;QAChE;MACJ;MACA,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACnB,aAAa;MACpC;MACA,IAAIoB,aAAa;MAAI;AACjC;AACA;AACA;MACa/E,GAAG,IAAK;QACL,IAAIA,GAAG,KAAK/F,GAAG,EAAE;UACbkK,IAAI,CAACE,OAAO,GAAGpK,GAAG;QACtB;QACA,IAAI,CAACkK,IAAI,CAACS,eAAe,EAAE;UACvBT,IAAI,CAACS,eAAe,GAAG,IAAI,CAACJ,UAAU,CAACL,IAAI,CAAC;QAChD;QACAA,IAAI,CAACQ,YAAY,GAAGvJ,SAAS,CAAC4E,GAAG,CAAC,GAAGA,GAAG,GAAImE,IAAI,CAACS,eAAe,IAAI3K,GAAI;QACxE;QACA,IAAI,CAACgK,UAAU,CAACE,IAAI,EAAE,IAAI,CAAClK,GAAG,GAAGkK,IAAI,CAACQ,YAAY,GAAGR,IAAI,CAACS,eAAe,CAAC9H,OAAO,CAAC7C,GAAG,EAAEkK,IAAI,CAACQ,YAAY,CAAC,CAAC;QAC1G,IAAI,CAACvB,IAAI,CAAC4B,YAAY,CAAC,CAAC;MAC5B,CAAE;MACF,IAAI5J,SAAS,CAACmC,YAAY,CAAC,EAAE;QACzB;QACA,IAAIyC,GAAG,GAAG,IAAI,CAAC9F,gBAAgB,CAAC+G,eAAe,CAAC1D,YAAY,EAAEtD,GAAG,EAAE,IAAI,CAAC0J,aAAa,CAAC;QACtF,IAAItL,YAAY,CAAC2H,GAAG,CAAC,EAAE;UACnBA,GAAG,CAACD,SAAS,CAACgF,aAAa,CAAC;QAChC,CAAC,MACI;UACDA,aAAa,CAAC/E,GAAG,CAAC;QACtB;MACJ,CAAC,MACI;QACD,IAAI,CAAC9F,gBAAgB,CAACsH,GAAG,CAACvH,GAAG,EAAE,IAAI,CAAC0J,aAAa,CAAC,CAAC5D,SAAS,CAACgF,aAAa,CAAC;MAC/E;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIP,UAAUA,CAACL,IAAI,EAAE;IACb,OAAO/I,SAAS,CAAC+I,IAAI,CAACc,WAAW,CAAC,GAAGd,IAAI,CAACc,WAAW,GAAGd,IAAI,CAACe,IAAI;EACrE;EACA;AACJ;AACA;AACA;AACA;EACIjB,UAAUA,CAACE,IAAI,EAAEI,OAAO,EAAE;IACtB,IAAInJ,SAAS,CAAC+I,IAAI,CAACc,WAAW,CAAC,EAAE;MAC7Bd,IAAI,CAACc,WAAW,GAAGV,OAAO;IAC9B,CAAC,MACI;MACDJ,IAAI,CAACe,IAAI,GAAGX,OAAO;IACvB;EACJ;EACA;AACJ;AACA;EACIY,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC5B,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC6B,WAAW,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAAC5B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC4B,WAAW,CAAC,CAAC;IAC7C;IACA,IAAI,IAAI,CAAC/B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC+B,WAAW,CAAC,CAAC;IAC7C;EACJ;AACJ;AACAlC,kBAAkB,CAAC9J,IAAI,GAAG,SAASiM,0BAA0BA,CAAC9L,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAI2J,kBAAkB,EAAEpK,MAAM,CAACwM,iBAAiB,CAAC7G,gBAAgB,CAAC,EAAE3F,MAAM,CAACwM,iBAAiB,CAACxM,MAAM,CAACf,UAAU,CAAC,EAAEe,MAAM,CAACwM,iBAAiB,CAACxM,MAAM,CAACd,iBAAiB,CAAC,CAAC;AAAE,CAAC;AACvPkL,kBAAkB,CAACqC,IAAI,GAAG,aAAczM,MAAM,CAAC0M,iBAAiB,CAAC;EAAEzL,IAAI,EAAEmJ,kBAAkB;EAAEuC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC;EAAEC,MAAM,EAAE;IAAEjC,SAAS,EAAE,WAAW;IAAEC,eAAe,EAAE;EAAkB;AAAE,CAAC,CAAC;AACrO;AACAR,kBAAkB,CAACL,cAAc,GAAG,MAAM,CACtC;EAAE9I,IAAI,EAAE0E;AAAiB,CAAC,EAC1B;EAAE1E,IAAI,EAAEhC;AAAW,CAAC,EACpB;EAAEgC,IAAI,EAAE/B;AAAkB,CAAC,CAC9B;AACDkL,kBAAkB,CAACyC,cAAc,GAAG;EAChClC,SAAS,EAAE,CAAC;IAAE1J,IAAI,EAAE9B;EAAM,CAAC,CAAC;EAC5ByL,eAAe,EAAE,CAAC;IAAE3J,IAAI,EAAE9B;EAAM,CAAC;AACrC,CAAC;AACD,CAAC,YAAY;EAAE,CAAC,OAAO4B,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAACoJ,kBAAkB,EAAE,CAAC;IACxGnJ,IAAI,EAAEjC,SAAS;IACfkL,IAAI,EAAE,CAAC;MACC4C,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAE7L,IAAI,EAAE0E;IAAiB,CAAC,EAAE;MAAE1E,IAAI,EAAEjB,MAAM,CAACf;IAAW,CAAC,EAAE;MAAEgC,IAAI,EAAEjB,MAAM,CAACd;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAE;IAAEyL,SAAS,EAAE,CAAC;MACjI1J,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEyL,eAAe,EAAE,CAAC;MAClB3J,IAAI,EAAE9B;IACV,CAAC;EAAE,CAAC,CAAC;AAAE,CAAC,EAAE,CAAC;AACnB,IAAI,KAAK,EAAE;EACP;EACAiL,kBAAkB,CAAClK,SAAS,CAACiB,GAAG;EAChC;EACAiJ,kBAAkB,CAAClK,SAAS,CAAC8L,UAAU;EACvC;EACA5B,kBAAkB,CAAClK,SAAS,CAAC2K,aAAa;EAC1C;EACAT,kBAAkB,CAAClK,SAAS,CAACuK,eAAe;EAC5C;EACAL,kBAAkB,CAAClK,SAAS,CAACwK,sBAAsB;EACnD;EACAN,kBAAkB,CAAClK,SAAS,CAACqK,sBAAsB;EACnD;AACJ;AACA;AACA;EACIH,kBAAkB,CAAClK,SAAS,CAACkB,gBAAgB;EAC7C;AACJ;AACA;AACA;EACIgJ,kBAAkB,CAAClK,SAAS,CAACmK,OAAO;EACpC;AACJ;AACA;AACA;EACID,kBAAkB,CAAClK,SAAS,CAACoK,IAAI;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMyC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;EACIzJ,WAAWA,CAACqH,SAAS,EAAEL,IAAI,EAAE;IACzB,IAAI,CAACK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC/H,KAAK,GAAG,EAAE;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwJ,WAAWA,CAAC5K,GAAG,EAAEE,iBAAiB,EAAEoD,YAAY,EAAE;IAC9C;IACA,IAAIwH,aAAa;IAAI;AAC7B;AACA;AACA;IACS/E,GAAG,IAAK;MACL,IAAI,CAAC3E,KAAK,GAAG2E,GAAG,KAAKpD,SAAS,GAAGoD,GAAG,GAAG/F,GAAG;MAC1C,IAAI,CAACoK,OAAO,GAAGpK,GAAG;MAClB,IAAI,CAACmJ,IAAI,CAAC4B,YAAY,CAAC,CAAC;IAC5B,CAAE;IACF,IAAIzH,YAAY,EAAE;MACd;MACA,IAAIyC,GAAG,GAAG,IAAI,CAACyD,SAAS,CAACxC,eAAe,CAAC1D,YAAY,EAAEtD,GAAG,EAAEE,iBAAiB,CAAC;MAC9E,IAAI9B,YAAY,CAAC2H,GAAG,CAACD,SAAS,CAAC,EAAE;QAC7BC,GAAG,CAACD,SAAS,CAACgF,aAAa,CAAC;MAChC,CAAC,MACI;QACDA,aAAa,CAAC/E,GAAG,CAAC;MACtB;IACJ;IACA,IAAI,CAACyD,SAAS,CAACjC,GAAG,CAACvH,GAAG,EAAEE,iBAAiB,CAAC,CAAC4F,SAAS,CAACgF,aAAa,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIe,SAASA,CAACC,KAAK,EAAE,GAAG/C,IAAI,EAAE;IACtB,IAAI,CAAC+C,KAAK,IAAI,CAACA,KAAK,CAACjL,MAAM,EAAE;MACzB,OAAOiL,KAAK;IAChB;IACA;IACA,IAAItL,MAAM,CAACsL,KAAK,EAAE,IAAI,CAAC1B,OAAO,CAAC,IAAI5J,MAAM,CAACuI,IAAI,EAAE,IAAI,CAAC8B,UAAU,CAAC,EAAE;MAC9D,OAAO,IAAI,CAACzJ,KAAK;IACrB;IACA;IACA,IAAIlB,iBAAiB;IACrB,IAAIiB,SAAS,CAAC4H,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAClI,MAAM,EAAE;MACnC,IAAI,OAAOkI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAClI,MAAM,EAAE;QAC/C;QACA;QACA;QACA,IAAIkL,SAAS,GAAGhD,IAAI,CAAC,CAAC,CAAC,CAClBlG,OAAO,CAAC,kCAAkC,EAAE,OAAO,CAAC,CACpDA,OAAO,CAAC,sBAAsB,EAAE,OAAO,CAAC;QAC7C,IAAI;UACA3C,iBAAiB,GAAG8L,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC;QAC7C,CAAC,CACD,OAAOG,CAAC,EAAE;UACN,MAAM,IAAIC,WAAW,CAAE,wEAAuEpD,IAAI,CAAC,CAAC,CAAE,EAAC,CAAC;QAC5G;MACJ,CAAC,MACI,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAChI,KAAK,CAACC,OAAO,CAAC+H,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D7I,iBAAiB,GAAG6I,IAAI,CAAC,CAAC,CAAC;MAC/B;IACJ;IACA;IACA,IAAI,CAACqB,OAAO,GAAG0B,KAAK;IACpB;IACA,IAAI,CAACjB,UAAU,GAAG9B,IAAI;IACtB;IACA,IAAI,CAAC6B,WAAW,CAACkB,KAAK,EAAE5L,iBAAiB,CAAC;IAC1C;IACA,IAAI,CAACkM,QAAQ,CAAC,CAAC;IACf;IACA,IAAI,CAAC,IAAI,CAACtI,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAAC0F,SAAS,CAAC1F,mBAAmB,CAACgC,SAAS;MAAE;AACrF;AACA;AACA;MACa4B,KAAK,IAAK;QACP,IAAI,IAAI,CAAC0C,OAAO,IAAI1C,KAAK,CAACzI,IAAI,KAAK,IAAI,CAACuK,SAAS,CAAC7F,WAAW,EAAE;UAC3D,IAAI,CAACyG,OAAO,GAAG,IAAI;UACnB,IAAI,CAACQ,WAAW,CAACkB,KAAK,EAAE5L,iBAAiB,EAAEwH,KAAK,CAACpE,YAAY,CAAC;QAClE;MACJ,CAAE,CAAC;IACP;IACA;IACA,IAAI,CAAC,IAAI,CAACS,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACyF,SAAS,CAACzF,YAAY,CAAC+B,SAAS;MAAE;AACvE;AACA;AACA;MACa4B,KAAK,IAAK;QACP,IAAI,IAAI,CAAC0C,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACkB,KAAK,EAAE5L,iBAAiB,EAAEwH,KAAK,CAACpE,YAAY,CAAC;QAClE;MACJ,CAAE,CAAC;IACP;IACA;IACA,IAAI,CAAC,IAAI,CAACU,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACwF,SAAS,CAACxF,mBAAmB,CAAC8B,SAAS;MAAE;AACrF;AACA;MACY,MAAM;QACF,IAAI,IAAI,CAACsE,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACkB,KAAK,EAAE5L,iBAAiB,CAAC;QAC9C;MACJ,CAAE,CAAC;IACP;IACA,OAAO,IAAI,CAACkB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIgL,QAAQA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAACtI,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACqH,WAAW,CAAC,CAAC;MACtC,IAAI,CAACrH,mBAAmB,GAAGnB,SAAS;IACxC;IACA,IAAI,OAAO,IAAI,CAACoB,YAAY,KAAK,WAAW,EAAE;MAC1C,IAAI,CAACA,YAAY,CAACoH,WAAW,CAAC,CAAC;MAC/B,IAAI,CAACpH,YAAY,GAAGpB,SAAS;IACjC;IACA,IAAI,OAAO,IAAI,CAACqB,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACmH,WAAW,CAAC,CAAC;MACtC,IAAI,CAACnH,mBAAmB,GAAGrB,SAAS;IACxC;EACJ;EACA;AACJ;AACA;EACIuI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkB,QAAQ,CAAC,CAAC;EACnB;AACJ;AACAR,aAAa,CAACzM,IAAI,GAAG,SAASkN,qBAAqBA,CAAC/M,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIsM,aAAa,EAAE/M,MAAM,CAACwM,iBAAiB,CAAC7G,gBAAgB,EAAE,EAAE,CAAC,EAAE3F,MAAM,CAACwM,iBAAiB,CAACxM,MAAM,CAACd,iBAAiB,EAAE,EAAE,CAAC,CAAC;AAAE,CAAC;AACnM6N,aAAa,CAACU,KAAK,GAAG,aAAczN,MAAM,CAAC0N,YAAY,CAAC;EAAEC,IAAI,EAAE,WAAW;EAAE1M,IAAI,EAAE8L,aAAa;EAAEa,IAAI,EAAE;AAAM,CAAC,CAAC;AAChHb,aAAa,CAACpM,KAAK,GAAG,aAAcX,MAAM,CAACY,kBAAkB,CAAC;EAAEC,KAAK,EAAEkM,aAAa;EAAEjM,OAAO,EAAEiM,aAAa,CAACzM;AAAK,CAAC,CAAC;AACpH;AACAyM,aAAa,CAAChD,cAAc,GAAG,MAAM,CACjC;EAAE9I,IAAI,EAAE0E;AAAiB,CAAC,EAC1B;EAAE1E,IAAI,EAAE/B;AAAkB,CAAC,CAC9B;AACD,CAAC,YAAY;EAAE,CAAC,OAAO6B,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAAC+L,aAAa,EAAE,CAAC;IACnG9L,IAAI,EAAErC;EACV,CAAC,EAAE;IACCqC,IAAI,EAAE7B,IAAI;IACV8K,IAAI,EAAE,CAAC;MACCyD,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,KAAK,CAAC;IAChB,CAAC;EACT,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAE3M,IAAI,EAAE0E;IAAiB,CAAC,EAAE;MAAE1E,IAAI,EAAEjB,MAAM,CAACd;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAC7G,IAAI,KAAK,EAAE;EACP;EACA6N,aAAa,CAAC7M,SAAS,CAACqC,KAAK;EAC7B;EACAwK,aAAa,CAAC7M,SAAS,CAACqL,OAAO;EAC/B;EACAwB,aAAa,CAAC7M,SAAS,CAAC8L,UAAU;EAClC;EACAe,aAAa,CAAC7M,SAAS,CAAC+E,mBAAmB;EAC3C;EACA8H,aAAa,CAAC7M,SAAS,CAACgF,YAAY;EACpC;EACA6H,aAAa,CAAC7M,SAAS,CAACiF,mBAAmB;EAC3C;AACJ;AACA;AACA;EACI4H,aAAa,CAAC7M,SAAS,CAACyK,SAAS;EACjC;AACJ;AACA;AACA;EACIoC,aAAa,CAAC7M,SAAS,CAACoK,IAAI;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuD,qBAAqBA,CAAA,EAAG,CAAE;AACnC,IAAI,KAAK,EAAE;EACP;EACAA,qBAAqB,CAAC3N,SAAS,CAAC4N,MAAM;EACtC;EACAD,qBAAqB,CAAC3N,SAAS,CAAC4F,QAAQ;EACxC;EACA+H,qBAAqB,CAAC3N,SAAS,CAAC6F,MAAM;EACtC;EACA8H,qBAAqB,CAAC3N,SAAS,CAAC8F,yBAAyB;EACzD;EACA6H,qBAAqB,CAAC3N,SAAS,CAACgG,OAAO;EACvC;EACA2H,qBAAqB,CAAC3N,SAAS,CAACiG,MAAM;EACtC;EACA0H,qBAAqB,CAAC3N,SAAS,CAAC+F,cAAc;EAC9C;EACA4H,qBAAqB,CAAC3N,SAAS,CAACkG,eAAe;AACnD;AACA,MAAM2H,eAAe,CAAC;EAClB;AACJ;AACA;AACA;AACA;EACI,OAAOC,OAAOA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHC,QAAQ,EAAEH,eAAe;MACzBI,SAAS,EAAE,CACPF,MAAM,CAACH,MAAM,IAAI;QAAEM,OAAO,EAAEnO,eAAe;QAAEoO,QAAQ,EAAEhO;MAAoB,CAAC,EAC5E4N,MAAM,CAACnI,QAAQ,IAAI;QAAEsI,OAAO,EAAE9J,iBAAiB;QAAE+J,QAAQ,EAAE3J;MAAsB,CAAC,EAClFuJ,MAAM,CAAClI,MAAM,IAAI;QAAEqI,OAAO,EAAEnL,eAAe;QAAEoL,QAAQ,EAAEhL;MAAuB,CAAC,EAC/E4K,MAAM,CAACjI,yBAAyB,IAAI;QAAEoI,OAAO,EAAE9M,yBAAyB;QAAE+M,QAAQ,EAAE5M;MAA8B,CAAC,EACnHoD,cAAc,EACd;QAAEuJ,OAAO,EAAEhJ,SAAS;QAAEkJ,QAAQ,EAAEL,MAAM,CAAC/H;MAAQ,CAAC,EAChD;QAAEkI,OAAO,EAAE/I,gBAAgB;QAAEiJ,QAAQ,EAAEL,MAAM,CAAChI;MAAe,CAAC,EAC9D;QAAEmI,OAAO,EAAE7I,UAAU;QAAE+I,QAAQ,EAAEL,MAAM,CAAC9H;MAAO,CAAC,EAChD;QAAEiI,OAAO,EAAE9I,gBAAgB;QAAEgJ,QAAQ,EAAEL,MAAM,CAAC7H;MAAgB,CAAC,EAC/DT,gBAAgB;IAExB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACI,OAAO4I,QAAQA,CAACN,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO;MACHC,QAAQ,EAAEH,eAAe;MACzBI,SAAS,EAAE,CACPF,MAAM,CAACH,MAAM,IAAI;QAAEM,OAAO,EAAEnO,eAAe;QAAEoO,QAAQ,EAAEhO;MAAoB,CAAC,EAC5E4N,MAAM,CAACnI,QAAQ,IAAI;QAAEsI,OAAO,EAAE9J,iBAAiB;QAAE+J,QAAQ,EAAE3J;MAAsB,CAAC,EAClFuJ,MAAM,CAAClI,MAAM,IAAI;QAAEqI,OAAO,EAAEnL,eAAe;QAAEoL,QAAQ,EAAEhL;MAAuB,CAAC,EAC/E4K,MAAM,CAACjI,yBAAyB,IAAI;QAAEoI,OAAO,EAAE9M,yBAAyB;QAAE+M,QAAQ,EAAE5M;MAA8B,CAAC,EACnH;QAAE2M,OAAO,EAAEhJ,SAAS;QAAEkJ,QAAQ,EAAEL,MAAM,CAAC/H;MAAQ,CAAC,EAChD;QAAEkI,OAAO,EAAE/I,gBAAgB;QAAEiJ,QAAQ,EAAEL,MAAM,CAAChI;MAAe,CAAC,EAC9D;QAAEmI,OAAO,EAAE7I,UAAU;QAAE+I,QAAQ,EAAEL,MAAM,CAAC9H;MAAO,CAAC,EAChD;QAAEiI,OAAO,EAAE9I,gBAAgB;QAAEgJ,QAAQ,EAAEL,MAAM,CAAC7H;MAAgB,CAAC,EAC/DT,gBAAgB;IAExB,CAAC;EACL;AACJ;AACAoI,eAAe,CAACzN,IAAI,GAAG,SAASkO,uBAAuBA,CAAC/N,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIsN,eAAe,EAAE,CAAC;AAAE,CAAC;AACnGA,eAAe,CAACU,IAAI,GAAG,aAAczO,MAAM,CAAC0O,gBAAgB,CAAC;EAAEzN,IAAI,EAAE8M;AAAgB,CAAC,CAAC;AACvFA,eAAe,CAACY,IAAI,GAAG,aAAc3O,MAAM,CAAC4O,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,YAAY;EAAE,CAAC,OAAO7N,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKf,MAAM,CAACgB,iBAAiB,CAAC+M,eAAe,EAAE,CAAC;IACrG9M,IAAI,EAAE5B,QAAQ;IACd6K,IAAI,EAAE,CAAC;MACC2E,YAAY,EAAE,CACV9B,aAAa,EACb3C,kBAAkB,CACrB;MACD0E,OAAO,EAAE,CACL/B,aAAa,EACb3C,kBAAkB;IAE1B,CAAC;EACT,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB,CAAC,YAAY;EAAE,CAAC,OAAO2E,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK/O,MAAM,CAACgP,kBAAkB,CAACjB,eAAe,EAAE;IAAEc,YAAY,EAAE,CAAC9B,aAAa,EAAE3C,kBAAkB,CAAC;IAAE0E,OAAO,EAAE,CAAC/B,aAAa,EAAE3C,kBAAkB;EAAE,CAAC,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEvN;AACA;AACA;AACA;AACA;;AAEA,SAAS9E,gBAAgB,EAAE7D,6BAA6B,EAAEH,yBAAyB,EAAEgD,iBAAiB,EAAEjB,sBAAsB,EAAE+G,kBAAkB,EAAE1F,qBAAqB,EAAErE,mBAAmB,EAAEJ,eAAe,EAAE8N,eAAe,EAAE9K,eAAe,EAAE8J,aAAa,EAAEpH,gBAAgB,EAAEd,cAAc,EAAEQ,gBAAgB,EAAEE,UAAU,EAAEH,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
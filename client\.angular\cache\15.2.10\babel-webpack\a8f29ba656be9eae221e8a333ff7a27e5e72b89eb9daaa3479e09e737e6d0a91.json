{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Chuvash [cv]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mirontoli\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var cv = moment.defineLocale('cv', {\n    months: 'кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав'.split('_'),\n    monthsShort: 'кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш'.split('_'),\n    weekdays: 'вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун'.split('_'),\n    weekdaysShort: 'выр_тун_ытл_юн_кӗҫ_эрн_шӑм'.split('_'),\n    weekdaysMin: 'вр_тн_ыт_юн_кҫ_эр_шм'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD-MM-YYYY',\n      LL: 'YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]',\n      LLL: 'YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm',\n      LLLL: 'dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm'\n    },\n    calendar: {\n      sameDay: '[Паян] LT [сехетре]',\n      nextDay: '[Ыран] LT [сехетре]',\n      lastDay: '[Ӗнер] LT [сехетре]',\n      nextWeek: '[Ҫитес] dddd LT [сехетре]',\n      lastWeek: '[Иртнӗ] dddd LT [сехетре]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: function (output) {\n        var affix = /сехет$/i.exec(output) ? 'рен' : /ҫул$/i.exec(output) ? 'тан' : 'ран';\n        return output + affix;\n      },\n      past: '%s каялла',\n      s: 'пӗр-ик ҫеккунт',\n      ss: '%d ҫеккунт',\n      m: 'пӗр минут',\n      mm: '%d минут',\n      h: 'пӗр сехет',\n      hh: '%d сехет',\n      d: 'пӗр кун',\n      dd: '%d кун',\n      M: 'пӗр уйӑх',\n      MM: '%d уйӑх',\n      y: 'пӗр ҫул',\n      yy: '%d ҫул'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-мӗш/,\n    ordinal: '%d-мӗш',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n\n  return cv;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "cv", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "output", "affix", "exec", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/moment/locale/cv.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Chuvash [cv]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mirontoli\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var cv = moment.defineLocale('cv', {\n        months: 'кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав'.split(\n            '_'\n        ),\n        monthsShort: 'кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш'.split('_'),\n        weekdays:\n            'вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун'.split(\n                '_'\n            ),\n        weekdaysShort: 'выр_тун_ытл_юн_кӗҫ_эрн_шӑм'.split('_'),\n        weekdaysMin: 'вр_тн_ыт_юн_кҫ_эр_шм'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD-MM-YYYY',\n            LL: 'YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]',\n            LLL: 'YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm',\n            LLLL: 'dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm',\n        },\n        calendar: {\n            sameDay: '[Паян] LT [сехетре]',\n            nextDay: '[Ыран] LT [сехетре]',\n            lastDay: '[Ӗнер] LT [сехетре]',\n            nextWeek: '[Ҫитес] dddd LT [сехетре]',\n            lastWeek: '[Иртнӗ] dddd LT [сехетре]',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: function (output) {\n                var affix = /сехет$/i.exec(output)\n                    ? 'рен'\n                    : /ҫул$/i.exec(output)\n                    ? 'тан'\n                    : 'ран';\n                return output + affix;\n            },\n            past: '%s каялла',\n            s: 'пӗр-ик ҫеккунт',\n            ss: '%d ҫеккунт',\n            m: 'пӗр минут',\n            mm: '%d минут',\n            h: 'пӗр сехет',\n            hh: '%d сехет',\n            d: 'пӗр кун',\n            dd: '%d кун',\n            M: 'пӗр уйӑх',\n            MM: '%d уйӑх',\n            y: 'пӗр ҫул',\n            yy: '%d ҫул',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-мӗш/,\n        ordinal: '%d-мӗш',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return cv;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,+DAA+D,CAACC,KAAK,CACzE,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EACJ,mEAAmE,CAACF,KAAK,CACrE,GACJ,CAAC;IACLG,aAAa,EAAE,4BAA4B,CAACH,KAAK,CAAC,GAAG,CAAC;IACtDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,qCAAqC;MACzCC,GAAG,EAAE,4CAA4C;MACjDC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,qBAAqB;MAC9BC,OAAO,EAAE,qBAAqB;MAC9BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACtB,IAAIC,KAAK,GAAG,SAAS,CAACC,IAAI,CAACF,MAAM,CAAC,GAC5B,KAAK,GACL,OAAO,CAACE,IAAI,CAACF,MAAM,CAAC,GACpB,KAAK,GACL,KAAK;QACX,OAAOA,MAAM,GAAGC,KAAK;MACzB,CAAC;MACDE,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,aAAa;IACrCC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"app/services/tournament.service\";\nimport * as i3 from \"app/services/season.service\";\nimport * as i4 from \"app/services/stage.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"@core/services/config.service\";\nimport * as i8 from \"@angular/platform-browser\";\nimport * as i9 from \"app/services/navigation.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i12 from \"app/layout/components/content-header/content-header.component\";\nimport * as i13 from \"../stages/stage-tables/stage-tables.component\";\nimport * as i14 from \"./fixtures/fixtures.component\";\nfunction FixturesResultsComponent_app_content_header_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-content-header\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"contentHeader\", ctx_r0.contentHeader);\n  }\n}\nfunction FixturesResultsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tab-fixtures\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matches\", ctx_r2.matches.fixtures);\n  }\n}\nfunction FixturesResultsComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tab-fixtures\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matches\", ctx_r3.matches.results);\n  }\n}\nfunction FixturesResultsComponent_ng_template_23_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"h4\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"No data\"), \" \");\n  }\n}\nfunction FixturesResultsComponent_ng_template_23_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"stage-tables\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"tableData\", ctx_r7.tableData)(\"showUpdateButton\", false);\n  }\n}\nfunction FixturesResultsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FixturesResultsComponent_ng_template_23_ng_container_0_Template, 5, 3, \"ng-container\", 16);\n    i0.ɵɵtemplate(1, FixturesResultsComponent_ng_template_23_ng_template_1_Template, 1, 2, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(2);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.tableData.length === 0)(\"ngIfElse\", _r6);\n  }\n}\nexport class FixturesResultsComponent {\n  constructor(_trans, _tourService, _seasonService, _stageService, _route, _router, _loading, _coreConfigService, _titleService, _navigationService, cdr) {\n    this._trans = _trans;\n    this._tourService = _tourService;\n    this._seasonService = _seasonService;\n    this._stageService = _stageService;\n    this._route = _route;\n    this._router = _router;\n    this._loading = _loading;\n    this._coreConfigService = _coreConfigService;\n    this._titleService = _titleService;\n    this._navigationService = _navigationService;\n    this.cdr = cdr;\n    this.matches = {\n      fixtures: {},\n      results: {}\n    };\n    this.params = {\n      season_id: null,\n      group_id: null,\n      tournament_id: null,\n      is_results: 0\n    };\n    this.activeId = 1;\n    this.regexTest = /^\\/[^\\/]+\\/matches\\/\\d+\\/details$/;\n    this.params.tournament_id = this._route.snapshot.paramMap.get('tournament_id');\n    // this.params.group_id = parseInt(\n    //   this._route.snapshot.queryParamMap.get('group_id')\n    // );\n    // this.params.tournament_id = parseInt(\n    //   this._route.snapshot.queryParamMap.get('tournament_id')\n    // );\n    // this.getCurrentSeason();\n  }\n\n  ngOnInit() {\n    this.showMatches();\n  }\n  showMatches() {\n    this._loading.show();\n    // this._router.navigate([], { queryParams: this.params });\n    // get fixtures\n    this.getFixtureResult(this.params);\n    // get results\n    let prm = JSON.parse(JSON.stringify(this.params));\n    prm.is_results = 1;\n    this.getFixtureResult(prm, 'results');\n  }\n  getFixtureResult(params, type = 'fixtures') {\n    console.log('trigger here');\n    this._tourService.getFixturesResultsByTournament(this.params.tournament_id, params).subscribe(res => {\n      let tournament_name = res.tournament.name;\n      this._titleService.setTitle(tournament_name);\n      this.contentHeader = {\n        headerTitle: tournament_name,\n        actionButton: false,\n        breadcrumb: {\n          type: '',\n          links: [{\n            name: this._trans.instant('Competitions'),\n            isLink: true,\n            link: '/leagues/select-tournament'\n          }, {\n            name: tournament_name,\n            isLink: false\n          }]\n        }\n      };\n      this.matches[type] = res.matches;\n      // Handle tab switching\n      if (type === 'fixtures') {\n        if (res.matches.length === 0) {\n          setTimeout(() => this.activeId = 2);\n        } else {\n          if (this.regexTest.test(this._navigationService.getPreviousUrl())) {\n            setTimeout(() => this.activeId = 2);\n          } else {\n            setTimeout(() => this.activeId = 1);\n          }\n        }\n      } else if (type === 'results') {\n        if (res.matches.length === 0) {\n          setTimeout(() => this.activeId = 1);\n        }\n      }\n      // Handle stage_id\n      if (res.stage_id) {\n        this.stage_id = res.stage_id;\n        this.getTableData(res.stage_id);\n      }\n    });\n  }\n  getTableData(stage_id) {\n    let stageIDs = [];\n    if (!stage_id) return;\n    for (let key in stage_id) {\n      switch (stage_id[key]) {\n        case AppConfig.TOURNAMENT_TYPES.league:\n        case AppConfig.TOURNAMENT_TYPES.groups:\n        case AppConfig.TOURNAMENT_TYPES.knockout:\n          stageIDs.push(key);\n          break;\n      }\n    }\n    if (stageIDs.length === 0 || this.tableData) return;\n    stageIDs.forEach(stageID => {\n      console.log('stageID:', stageID);\n      this._stageService.getTableData(stageID).subscribe(data => {\n        this.tableData = this.tableData ? [...this.tableData, ...data.filter(newItem => !this.tableData.some(existingItem => existingItem.id === newItem.id))] : data;\n        // this.tableData = this.tableData\n        //   ? Array.from(\n        //       new Set(this.tableData.map((item) => JSON.stringify(item)))\n        //     ).map((item) => JSON.parse(item as string))\n        //   : data;\n      }, error => console.error(error));\n    });\n  }\n  static #_ = this.ɵfac = function FixturesResultsComponent_Factory(t) {\n    return new (t || FixturesResultsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.TournamentService), i0.ɵɵdirectiveInject(i3.SeasonService), i0.ɵɵdirectiveInject(i4.StageService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.CoreConfigService), i0.ɵɵdirectiveInject(i8.Title), i0.ɵɵdirectiveInject(i9.NavigationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FixturesResultsComponent,\n    selectors: [[\"app-fixtures-results\"]],\n    decls: 25,\n    vars: 15,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\", 4, \"ngIf\"], [\"id\", \"fixtures-results\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\", 3, \"activeId\", \"activeIdChange\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\", 3, \"ngbNavItem\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [3, \"contentHeader\"], [\"type\", \"fixtures\", 3, \"matches\"], [\"type\", \"results\", 3, \"matches\"], [4, \"ngIf\", \"ngIfElse\"], [\"tableContent\", \"\"], [1, \"text-center\"], [1, \"text-muted\"], [3, \"tableData\", \"showUpdateButton\"]],\n    template: function FixturesResultsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, FixturesResultsComponent_app_content_header_2_Template, 1, 1, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8);\n        i0.ɵɵlistener(\"activeIdChange\", function FixturesResultsComponent_Template_ul_activeIdChange_7_listener($event) {\n          return ctx.activeId = $event;\n        });\n        i0.ɵɵelementStart(9, \"li\", 9)(10, \"a\", 10);\n        i0.ɵɵtext(11);\n        i0.ɵɵpipe(12, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, FixturesResultsComponent_ng_template_13_Template, 1, 1, \"ng-template\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"li\", 9)(15, \"a\", 10);\n        i0.ɵɵtext(16);\n        i0.ɵɵpipe(17, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, FixturesResultsComponent_ng_template_18_Template, 1, 1, \"ng-template\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"li\", 9)(20, \"a\", 10);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(23, FixturesResultsComponent_ng_template_23_Template, 3, 2, \"ng-template\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(24, \"div\", 12);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.contentHeader);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"activeId\", ctx.activeId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngbNavItem\", 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 9, \"Fixtures\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavItem\", 2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 11, \"Results\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavItem\", 3);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 13, \"Tables\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r1);\n      }\n    },\n    dependencies: [i10.NgIf, i11.NgbNavContent, i11.NgbNav, i11.NgbNavItem, i11.NgbNavLink, i11.NgbNavOutlet, i12.ContentHeaderComponent, i13.StageTablesComponent, i14.FixturesComponent, i1.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;ICLtCC,EAAA,CAAAC,SAAA,6BAGsB;;;;IAFpBD,EAAA,CAAAE,UAAA,kBAAAC,MAAA,CAAAC,aAAA,CAA+B;;;;;IAmBnBJ,EAAA,CAAAC,SAAA,uBACe;;;;IADeD,EAAA,CAAAE,UAAA,YAAAG,MAAA,CAAAC,OAAA,CAAAC,QAAA,CAA4B;;;;;IAS1DP,EAAA,CAAAC,SAAA,uBACe;;;;IADcD,EAAA,CAAAE,UAAA,YAAAM,MAAA,CAAAF,OAAA,CAAAG,OAAA,CAA2B;;;;;IAOxDT,EAAA,CAAAU,uBAAA,GAEC;IACCV,EAAA,CAAAW,cAAA,cAAyB;IAErBX,EAAA,CAAAY,MAAA,GACF;;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAETb,EAAA,CAAAc,qBAAA,EAAe;;;IAHTd,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAiB,WAAA,uBACF;;;;;IAIFjB,EAAA,CAAAC,SAAA,uBAGgB;;;;IAFdD,EAAA,CAAAE,UAAA,cAAAgB,MAAA,CAAAC,SAAA,CAAuB;;;;;IAX3BnB,EAAA,CAAAoB,UAAA,IAAAC,+DAAA,2BAQe;IACfrB,EAAA,CAAAoB,UAAA,IAAAE,8DAAA,iCAAAtB,EAAA,CAAAuB,sBAAA,CAKc;;;;;IAbXvB,EAAA,CAAAE,UAAA,SAAAsB,MAAA,CAAAL,SAAA,CAAAM,MAAA,OAA8B,aAAAC,GAAA;;;ADrBnD,OAAM,MAAOC,wBAAwB;EAoBnCC,YACSC,MAAwB,EACxBC,YAA+B,EAC/BC,cAA6B,EAC7BC,aAA2B,EAC3BC,MAAsB,EACtBC,OAAe,EACfC,QAAwB,EACvBC,kBAAqC,EACtCC,aAAoB,EACnBC,kBAAqC,EACrCC,GAAsB;IAVvB,KAAAV,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IA5Bb,KAAAjC,OAAO,GAAG;MACRC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE;KACV;IACD,KAAA+B,MAAM,GAAG;MACPC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE;KACb;IAGD,KAAAC,QAAQ,GAAG,CAAC;IAGZ,KAAAC,SAAS,GAAG,mCAAmC;IAgB7C,IAAI,CAACN,MAAM,CAACG,aAAa,GACvB,IAAI,CAACV,MAAM,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC;IACpD;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB;IACA;IACA,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACb,MAAM,CAAC;IAClC;IACA,IAAIc,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACjB,MAAM,CAAC,CAAC;IACjDc,GAAG,CAACV,UAAU,GAAG,CAAC;IAClB,IAAI,CAACS,gBAAgB,CAACC,GAAG,EAAE,SAAS,CAAC;EACvC;EAEAD,gBAAgBA,CAACb,MAAM,EAAEkB,IAAI,GAAG,UAAU;IACxCC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAE3B,IAAI,CAAC9B,YAAY,CACd+B,8BAA8B,CAAC,IAAI,CAACrB,MAAM,CAACG,aAAa,EAAEH,MAAM,CAAC,CACjEsB,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAIC,eAAe,GAAGD,GAAG,CAACE,UAAU,CAACC,IAAI;MACzC,IAAI,CAAC7B,aAAa,CAAC8B,QAAQ,CAACH,eAAe,CAAC;MAE5C,IAAI,CAAC5D,aAAa,GAAG;QACnBgE,WAAW,EAAEJ,eAAe;QAC5BK,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;UACVZ,IAAI,EAAE,EAAE;UACRa,KAAK,EAAE,CACL;YACEL,IAAI,EAAE,IAAI,CAACrC,MAAM,CAAC2C,OAAO,CAAC,cAAc,CAAC;YACzCC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;WACP,EACD;YACER,IAAI,EAAEF,eAAe;YACrBS,MAAM,EAAE;WACT;;OAGN;MACD,IAAI,CAACnE,OAAO,CAACoD,IAAI,CAAC,GAAGK,GAAG,CAACzD,OAAO;MAEhC;MACA,IAAIoD,IAAI,KAAK,UAAU,EAAE;QACvB,IAAIK,GAAG,CAACzD,OAAO,CAACmB,MAAM,KAAK,CAAC,EAAE;UAC5BkD,UAAU,CAAC,MAAM,IAAI,CAAC9B,QAAQ,GAAG,CAAC,CAAC;SACpC,MAAM;UACL,IAAI,IAAI,CAACC,SAAS,CAAC8B,IAAI,CAAC,IAAI,CAACtC,kBAAkB,CAACuC,cAAc,EAAE,CAAC,EAAE;YACjEF,UAAU,CAAC,MAAM,IAAI,CAAC9B,QAAQ,GAAG,CAAC,CAAC;WACpC,MAAM;YACL8B,UAAU,CAAC,MAAM,IAAI,CAAC9B,QAAQ,GAAG,CAAC,CAAC;;;OAGxC,MAAM,IAAIa,IAAI,KAAK,SAAS,EAAE;QAC7B,IAAIK,GAAG,CAACzD,OAAO,CAACmB,MAAM,KAAK,CAAC,EAAE;UAC5BkD,UAAU,CAAC,MAAM,IAAI,CAAC9B,QAAQ,GAAG,CAAC,CAAC;;;MAIvC;MACA,IAAIkB,GAAG,CAACe,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAGf,GAAG,CAACe,QAAQ;QAC5B,IAAI,CAACC,YAAY,CAAChB,GAAG,CAACe,QAAQ,CAAC;;IAEnC,CAAC,CAAC;EACN;EAGAC,YAAYA,CAACD,QAAQ;IACnB,IAAIE,QAAQ,GAAG,EAAE;IACjB,IAAI,CAACF,QAAQ,EAAE;IAEf,KAAK,IAAIG,GAAG,IAAIH,QAAQ,EAAE;MACxB,QAAQA,QAAQ,CAACG,GAAG,CAAC;QACnB,KAAKlF,SAAS,CAACmF,gBAAgB,CAACC,MAAM;QACtC,KAAKpF,SAAS,CAACmF,gBAAgB,CAACE,MAAM;QACtC,KAAKrF,SAAS,CAACmF,gBAAgB,CAACG,QAAQ;UACtCL,QAAQ,CAACM,IAAI,CAACL,GAAG,CAAC;UAClB;;;IAIN,IAAID,QAAQ,CAACvD,MAAM,KAAK,CAAC,IAAI,IAAI,CAACN,SAAS,EAAE;IAE7C6D,QAAQ,CAACO,OAAO,CAAEC,OAAO,IAAI;MAC3B7B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4B,OAAO,CAAC;MAEhC,IAAI,CAACxD,aAAa,CAAC+C,YAAY,CAACS,OAAO,CAAC,CAAC1B,SAAS,CAC/C2B,IAAS,IAAI;QAEZ,IAAI,CAACtE,SAAS,GAAG,IAAI,CAACA,SAAS,GAC3B,CACA,GAAG,IAAI,CAACA,SAAS,EACjB,GAAGsE,IAAI,CAACC,MAAM,CACZC,OAAO,IAAI,CAAC,IAAI,CAACxE,SAAS,CAACyE,IAAI,CAACC,YAAY,IAAIA,YAAY,CAACC,EAAE,KAAKH,OAAO,CAACG,EAAE,CAAC,CAChF,CACF,GACCL,IAAI;QAER;QACA;QACA;QACA;QACA;MACF,CAAC,EACAM,KAAK,IAAKpC,OAAO,CAACoC,KAAK,CAACA,KAAK,CAAC,CAChC;IACH,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA;qBA3JUrE,wBAAwB,EAAA3B,EAAA,CAAAiG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAnG,EAAA,CAAAiG,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAArG,EAAA,CAAAiG,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAvG,EAAA,CAAAiG,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAzG,EAAA,CAAAiG,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA3G,EAAA,CAAAiG,iBAAA,CAAAS,EAAA,CAAAE,MAAA,GAAA5G,EAAA,CAAAiG,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA9G,EAAA,CAAAiG,iBAAA,CAAAc,EAAA,CAAAC,iBAAA,GAAAhH,EAAA,CAAAiG,iBAAA,CAAAgB,EAAA,CAAAC,KAAA,GAAAlH,EAAA,CAAAiG,iBAAA,CAAAkB,EAAA,CAAAC,iBAAA,GAAApH,EAAA,CAAAiG,iBAAA,CAAAjG,EAAA,CAAAqH,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAAxB3F,wBAAwB;IAAA4F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClBrC7H,EAAA,CAAAW,cAAA,aAA+C;QAE3CX,EAAA,CAAAoB,UAAA,IAAA2G,sDAAA,gCAGsB;QAEtB/H,EAAA,CAAAW,cAAA,iBAA+B;QAMrBX,EAAA,CAAAgI,UAAA,4BAAAC,+DAAAC,MAAA;UAAA,OAAAJ,GAAA,CAAAjF,QAAA,GAAAqF,MAAA;QAAA,EAAuB;QAIvBlI,EAAA,CAAAW,cAAA,YAAgC;QAE5BX,EAAA,CAAAY,MAAA,IACF;;QAAAZ,EAAA,CAAAa,YAAA,EAAI;QACJb,EAAA,CAAAoB,UAAA,KAAA+G,gDAAA,0BAGc;QAChBnI,EAAA,CAAAa,YAAA,EAAK;QACLb,EAAA,CAAAW,cAAA,aAAgC;QAE5BX,EAAA,CAAAY,MAAA,IACF;;QAAAZ,EAAA,CAAAa,YAAA,EAAI;QACJb,EAAA,CAAAoB,UAAA,KAAAgH,gDAAA,0BAGc;QAChBpI,EAAA,CAAAa,YAAA,EAAK;QACLb,EAAA,CAAAW,cAAA,aAAgC;QAChBX,EAAA,CAAAY,MAAA,IAA0B;;QAAAZ,EAAA,CAAAa,YAAA,EAAI;QAC5Cb,EAAA,CAAAoB,UAAA,KAAAiH,gDAAA,0BAgBc;QAChBrI,EAAA,CAAAa,YAAA,EAAK;QAGTb,EAAA,CAAAC,SAAA,eAA6C;QAC/CD,EAAA,CAAAa,YAAA,EAAM;;;;QAtDPb,EAAA,CAAAe,SAAA,GAAmB;QAAnBf,EAAA,CAAAE,UAAA,SAAA4H,GAAA,CAAA1H,aAAA,CAAmB;QASZJ,EAAA,CAAAe,SAAA,GAAuB;QAAvBf,EAAA,CAAAE,UAAA,aAAA4H,GAAA,CAAAjF,QAAA,CAAuB;QAIR7C,EAAA,CAAAe,SAAA,GAAgB;QAAhBf,EAAA,CAAAE,UAAA,iBAAgB;QAE3BF,EAAA,CAAAe,SAAA,GACF;QADEf,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAiB,WAAA,yBACF;QAMajB,EAAA,CAAAe,SAAA,GAAgB;QAAhBf,EAAA,CAAAE,UAAA,iBAAgB;QAE3BF,EAAA,CAAAe,SAAA,GACF;QADEf,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAiB,WAAA,yBACF;QAMajB,EAAA,CAAAe,SAAA,GAAgB;QAAhBf,EAAA,CAAAE,UAAA,iBAAgB;QACfF,EAAA,CAAAe,SAAA,GAA0B;QAA1Bf,EAAA,CAAAsI,iBAAA,CAAAtI,EAAA,CAAAiB,WAAA,mBAA0B;QAqBzCjB,EAAA,CAAAe,SAAA,GAAoB;QAApBf,EAAA,CAAAE,UAAA,iBAAAqI,GAAA,CAAoB", "names": ["AppConfig", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "contentHeader", "ctx_r2", "matches", "fixtures", "ctx_r3", "results", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r7", "tableData", "ɵɵtemplate", "FixturesResultsComponent_ng_template_23_ng_container_0_Template", "FixturesResultsComponent_ng_template_23_ng_template_1_Template", "ɵɵtemplateRefExtractor", "ctx_r4", "length", "_r6", "FixturesResultsComponent", "constructor", "_trans", "_tourService", "_seasonService", "_stageService", "_route", "_router", "_loading", "_coreConfigService", "_titleService", "_navigationService", "cdr", "params", "season_id", "group_id", "tournament_id", "is_results", "activeId", "regexTest", "snapshot", "paramMap", "get", "ngOnInit", "showMatches", "show", "getFixtureResult", "prm", "JSON", "parse", "stringify", "type", "console", "log", "getFixturesResultsByTournament", "subscribe", "res", "tournament_name", "tournament", "name", "setTitle", "headerTitle", "actionButton", "breadcrumb", "links", "instant", "isLink", "link", "setTimeout", "test", "getPreviousUrl", "stage_id", "getTableData", "stageIDs", "key", "TOURNAMENT_TYPES", "league", "groups", "knockout", "push", "for<PERSON>ach", "stageID", "data", "filter", "newItem", "some", "existingItem", "id", "error", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "TournamentService", "i3", "SeasonService", "i4", "StageService", "i5", "ActivatedRoute", "Router", "i6", "LoadingService", "i7", "CoreConfigService", "i8", "Title", "i9", "NavigationService", "ChangeDetectorRef", "_2", "selectors", "decls", "vars", "consts", "template", "FixturesResultsComponent_Template", "rf", "ctx", "FixturesResultsComponent_app_content_header_2_Template", "ɵɵlistener", "FixturesResultsComponent_Template_ul_activeIdChange_7_listener", "$event", "FixturesResultsComponent_ng_template_13_Template", "FixturesResultsComponent_ng_template_18_Template", "FixturesResultsComponent_ng_template_23_Template", "ɵɵtextInterpolate", "_r1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\fixtures-results.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\fixtures-results.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NavigationService } from 'app/services/navigation.service';\r\n\r\n@Component({\r\n  selector: 'app-fixtures-results',\r\n  templateUrl: './fixtures-results.component.html',\r\n  styleUrls: ['./fixtures-results.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class FixturesResultsComponent implements OnInit {\r\n  stage_id: any;\r\n  contentHeader: object;\r\n  matches = {\r\n    fixtures: {},\r\n    results: {},\r\n  };\r\n  params = {\r\n    season_id: null,\r\n    group_id: null,\r\n    tournament_id: null,\r\n    is_results: 0,\r\n  };\r\n  tableData: any;\r\n  selectedTournament: any;\r\n  activeId = 1;\r\n  season_id: any;\r\n\r\n  regexTest = /^\\/[^\\/]+\\/matches\\/\\d+\\/details$/;\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _tourService: TournamentService,\r\n    public _seasonService: SeasonService,\r\n    public _stageService: StageService,\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _loading: LoadingService,\r\n    private _coreConfigService: CoreConfigService,\r\n    public _titleService: Title,\r\n    private _navigationService: NavigationService,\r\n    private cdr: ChangeDetectorRef,\r\n\r\n  ) {\r\n    this.params.tournament_id =\r\n      this._route.snapshot.paramMap.get('tournament_id');\r\n    // this.params.group_id = parseInt(\r\n    //   this._route.snapshot.queryParamMap.get('group_id')\r\n    // );\r\n    // this.params.tournament_id = parseInt(\r\n    //   this._route.snapshot.queryParamMap.get('tournament_id')\r\n    // );\r\n    // this.getCurrentSeason();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.showMatches();\r\n  }\r\n\r\n  showMatches() {\r\n    this._loading.show();\r\n    // this._router.navigate([], { queryParams: this.params });\r\n    // get fixtures\r\n    this.getFixtureResult(this.params);\r\n    // get results\r\n    let prm = JSON.parse(JSON.stringify(this.params));\r\n    prm.is_results = 1;\r\n    this.getFixtureResult(prm, 'results');\r\n  }\r\n\r\n  getFixtureResult(params, type = 'fixtures') {\r\n    console.log('trigger here');\r\n\r\n    this._tourService\r\n      .getFixturesResultsByTournament(this.params.tournament_id, params)\r\n      .subscribe((res) => {\r\n        let tournament_name = res.tournament.name;\r\n        this._titleService.setTitle(tournament_name);\r\n\r\n        this.contentHeader = {\r\n          headerTitle: tournament_name,\r\n          actionButton: false,\r\n          breadcrumb: {\r\n            type: '',\r\n            links: [\r\n              {\r\n                name: this._trans.instant('Competitions'),\r\n                isLink: true,\r\n                link: '/leagues/select-tournament',\r\n              },\r\n              {\r\n                name: tournament_name,\r\n                isLink: false,\r\n              },\r\n            ],\r\n          },\r\n        };\r\n        this.matches[type] = res.matches;\r\n\r\n        // Handle tab switching\r\n        if (type === 'fixtures') {\r\n          if (res.matches.length === 0) {\r\n            setTimeout(() => this.activeId = 2);\r\n          } else {\r\n            if (this.regexTest.test(this._navigationService.getPreviousUrl())) {\r\n              setTimeout(() => this.activeId = 2);\r\n            } else {\r\n              setTimeout(() => this.activeId = 1);\r\n            }\r\n          }\r\n        } else if (type === 'results') {\r\n          if (res.matches.length === 0) {\r\n            setTimeout(() => this.activeId = 1);\r\n          }\r\n        }\r\n\r\n        // Handle stage_id\r\n        if (res.stage_id) {\r\n          this.stage_id = res.stage_id;\r\n          this.getTableData(res.stage_id);\r\n        }\r\n      });\r\n  }\r\n\r\n\r\n  getTableData(stage_id) {\r\n    let stageIDs = [];\r\n    if (!stage_id) return;\r\n\r\n    for (let key in stage_id) {\r\n      switch (stage_id[key]) {\r\n        case AppConfig.TOURNAMENT_TYPES.league:\r\n        case AppConfig.TOURNAMENT_TYPES.groups:\r\n        case AppConfig.TOURNAMENT_TYPES.knockout:\r\n          stageIDs.push(key);\r\n          break;\r\n      }\r\n    }\r\n\r\n    if (stageIDs.length === 0 || this.tableData) return;\r\n\r\n    stageIDs.forEach((stageID) => {\r\n      console.log('stageID:', stageID);\r\n\r\n      this._stageService.getTableData(stageID).subscribe(\r\n        (data: any) => {\r\n\r\n          this.tableData = this.tableData\r\n            ? [\r\n              ...this.tableData,\r\n              ...data.filter(\r\n                newItem => !this.tableData.some(existingItem => existingItem.id === newItem.id)\r\n              )\r\n            ]\r\n            : data;\r\n\r\n          // this.tableData = this.tableData\r\n          //   ? Array.from(\r\n          //       new Set(this.tableData.map((item) => JSON.stringify(item)))\r\n          //     ).map((item) => JSON.parse(item as string))\r\n          //   : data;\r\n        },\r\n        (error) => console.error(error)\r\n      );\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <app-content-header\r\n      [contentHeader]=\"contentHeader\"\r\n      *ngIf=\"contentHeader\"\r\n    ></app-content-header>\r\n\r\n    <section id=\"fixtures-results\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <ul\r\n              ngbNav\r\n              [(activeId)]=\"activeId\"\r\n              #nav=\"ngbNav\"\r\n              class=\"nav-tabs m-0\"\r\n            >\r\n              <li ngbNavItem [ngbNavItem]=\"1\">\r\n                <a ngbNavLink>\r\n                  {{ 'Fixtures' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <tab-fixtures type=\"fixtures\" [matches]=\"matches.fixtures\">\r\n                  </tab-fixtures>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem [ngbNavItem]=\"2\">\r\n                <a ngbNavLink>\r\n                  {{ 'Results' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <tab-fixtures type=\"results\" [matches]=\"matches.results\">\r\n                  </tab-fixtures>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem [ngbNavItem]=\"3\">\r\n                <a ngbNavLink>{{ 'Tables' | translate }}</a>\r\n                <ng-template ngbNavContent>\r\n                  <ng-container\r\n                    *ngIf=\"tableData.length === 0; else tableContent\"\r\n                  >\r\n                    <div class=\"text-center\">\r\n                      <h4 class=\"text-muted\">\r\n                        {{ 'No data' | translate }}\r\n                      </h4>\r\n                    </div>\r\n                  </ng-container>\r\n                  <ng-template #tableContent>\r\n                    <stage-tables\r\n                      [tableData]=\"tableData\"\r\n                      [showUpdateButton]=\"false\"\r\n                    ></stage-tables>\r\n                  </ng-template>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\nconst name = \"@firebase/installations\";\nconst version = \"0.6.4\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst PENDING_TIMEOUT_MS = 10000;\nconst PACKAGE_VERSION = `w:${version}`;\nconst INTERNAL_AUTH_VERSION = 'FIS_v2';\nconst INSTALLATIONS_API_URL = 'https://firebaseinstallations.googleapis.com/v1';\nconst TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\nconst SERVICE = 'installations';\nconst SERVICE_NAME = 'Installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERROR_DESCRIPTION_MAP = {\n  [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\n  [\"not-registered\" /* ErrorCode.NOT_REGISTERED */]: 'Firebase Installation is not registered.',\n  [\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */]: 'Firebase Installation not found.',\n  [\"request-failed\" /* ErrorCode.REQUEST_FAILED */]: '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [\"app-offline\" /* ErrorCode.APP_OFFLINE */]: 'Could not process request. Application offline.',\n  [\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */]: \"Can't delete installation while there is a pending registration request.\"\n};\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nfunction isServerError(error) {\n  return error instanceof FirebaseError && error.code.includes(\"request-failed\" /* ErrorCode.REQUEST_FAILED */);\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction getInstallationsEndpoint({\n  projectId\n}) {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\nfunction extractAuthTokenInfoFromResponse(response) {\n  return {\n    token: response.token,\n    requestStatus: 2 /* RequestStatus.COMPLETED */,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\nfunction getErrorFromResponse(_x, _x2) {\n  return _getErrorFromResponse.apply(this, arguments);\n}\nfunction _getErrorFromResponse() {\n  _getErrorFromResponse = _asyncToGenerator(function* (requestName, response) {\n    const responseJson = yield response.json();\n    const errorData = responseJson.error;\n    return ERROR_FACTORY.create(\"request-failed\" /* ErrorCode.REQUEST_FAILED */, {\n      requestName,\n      serverCode: errorData.code,\n      serverMessage: errorData.message,\n      serverStatus: errorData.status\n    });\n  });\n  return _getErrorFromResponse.apply(this, arguments);\n}\nfunction getHeaders({\n  apiKey\n}) {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\nfunction getHeadersWithAuth(appConfig, {\n  refreshToken\n}) {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n/**\r\n * Calls the passed in fetch wrapper and returns the response.\r\n * If the returned response has a status of 5xx, re-runs the function once and\r\n * returns the response.\r\n */\nfunction retryIfServerError(_x3) {\n  return _retryIfServerError.apply(this, arguments);\n}\nfunction _retryIfServerError() {\n  _retryIfServerError = _asyncToGenerator(function* (fn) {\n    const result = yield fn();\n    if (result.status >= 500 && result.status < 600) {\n      // Internal Server Error. Retry request.\n      return fn();\n    }\n    return result;\n  });\n  return _retryIfServerError.apply(this, arguments);\n}\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn) {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\nfunction getAuthorizationHeader(refreshToken) {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction createInstallationRequest(_x4, _x5) {\n  return _createInstallationRequest.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/** Returns a promise that resolves after given time passes. */\nfunction _createInstallationRequest() {\n  _createInstallationRequest = _asyncToGenerator(function* ({\n    appConfig,\n    heartbeatServiceProvider\n  }, {\n    fid\n  }) {\n    const endpoint = getInstallationsEndpoint(appConfig);\n    const headers = getHeaders(appConfig);\n    // If heartbeat service exists, add the heartbeat string to the header.\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\n      optional: true\n    });\n    if (heartbeatService) {\n      const heartbeatsHeader = yield heartbeatService.getHeartbeatsHeader();\n      if (heartbeatsHeader) {\n        headers.append('x-firebase-client', heartbeatsHeader);\n      }\n    }\n    const body = {\n      fid,\n      authVersion: INTERNAL_AUTH_VERSION,\n      appId: appConfig.appId,\n      sdkVersion: PACKAGE_VERSION\n    };\n    const request = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(body)\n    };\n    const response = yield retryIfServerError(() => fetch(endpoint, request));\n    if (response.ok) {\n      const responseValue = yield response.json();\n      const registeredInstallationEntry = {\n        fid: responseValue.fid || fid,\n        registrationStatus: 2 /* RequestStatus.COMPLETED */,\n        refreshToken: responseValue.refreshToken,\n        authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n      };\n      return registeredInstallationEntry;\n    } else {\n      throw yield getErrorFromResponse('Create Installation', response);\n    }\n  });\n  return _createInstallationRequest.apply(this, arguments);\n}\nfunction sleep(ms) {\n  return new Promise(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction bufferToBase64UrlSafe(array) {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nconst INVALID_FID = '';\n/**\r\n * Generates a new FID using random values from Web Crypto API.\r\n * Returns an empty string if FID generation fails for any reason.\r\n */\nfunction generateFid() {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto = self.crypto || self.msCrypto;\n    crypto.getRandomValues(fidByteArray);\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + fidByteArray[0] % 0b00010000;\n    const fid = encode(fidByteArray);\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch (_a) {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray) {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/** Returns a string key that can be used to identify the app. */\nfunction getKey(appConfig) {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst fidChangeCallbacks = new Map();\n/**\r\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\r\n * change to other tabs.\r\n */\nfunction fidChanged(appConfig, fid) {\n  const key = getKey(appConfig);\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\nfunction addCallback(appConfig, callback) {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n  const key = getKey(appConfig);\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\nfunction removeCallback(appConfig, callback) {\n  const key = getKey(appConfig);\n  const callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    return;\n  }\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\nfunction callFidChangeCallbacks(key, fid) {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\nfunction broadcastFidChange(key, fid) {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({\n      key,\n      fid\n    });\n  }\n  closeBroadcastChannel();\n}\nlet broadcastChannel = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel() {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\nfunction closeBroadcastChannel() {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n/** Assigns or overwrites the record for the given key with the given value. */\nfunction set(_x6, _x7) {\n  return _set.apply(this, arguments);\n}\n/** Removes record(s) from the objectStore that match the given key. */\nfunction _set() {\n  _set = _asyncToGenerator(function* (appConfig, value) {\n    const key = getKey(appConfig);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n    const oldValue = yield objectStore.get(key);\n    yield objectStore.put(value, key);\n    yield tx.done;\n    if (!oldValue || oldValue.fid !== value.fid) {\n      fidChanged(appConfig, value.fid);\n    }\n    return value;\n  });\n  return _set.apply(this, arguments);\n}\nfunction remove(_x8) {\n  return _remove.apply(this, arguments);\n}\n/**\r\n * Atomically updates a record with the result of updateFn, which gets\r\n * called with the current value. If newValue is undefined, the record is\r\n * deleted instead.\r\n * @return Updated value\r\n */\nfunction _remove() {\n  _remove = _asyncToGenerator(function* (appConfig) {\n    const key = getKey(appConfig);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    yield tx.objectStore(OBJECT_STORE_NAME).delete(key);\n    yield tx.done;\n  });\n  return _remove.apply(this, arguments);\n}\nfunction update(_x9, _x10) {\n  return _update.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Updates and returns the InstallationEntry from the database.\r\n * Also triggers a registration request if it is necessary and possible.\r\n */\nfunction _update() {\n  _update = _asyncToGenerator(function* (appConfig, updateFn) {\n    const key = getKey(appConfig);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    const store = tx.objectStore(OBJECT_STORE_NAME);\n    const oldValue = yield store.get(key);\n    const newValue = updateFn(oldValue);\n    if (newValue === undefined) {\n      yield store.delete(key);\n    } else {\n      yield store.put(newValue, key);\n    }\n    yield tx.done;\n    if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n      fidChanged(appConfig, newValue.fid);\n    }\n    return newValue;\n  });\n  return _update.apply(this, arguments);\n}\nfunction getInstallationEntry(_x11) {\n  return _getInstallationEntry.apply(this, arguments);\n}\n/**\r\n * Creates a new Installation Entry if one does not exist.\r\n * Also clears timed out pending requests.\r\n */\nfunction _getInstallationEntry() {\n  _getInstallationEntry = _asyncToGenerator(function* (installations) {\n    let registrationPromise;\n    const installationEntry = yield update(installations.appConfig, oldEntry => {\n      const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n      const entryWithPromise = triggerRegistrationIfNecessary(installations, installationEntry);\n      registrationPromise = entryWithPromise.registrationPromise;\n      return entryWithPromise.installationEntry;\n    });\n    if (installationEntry.fid === INVALID_FID) {\n      // FID generation failed. Waiting for the FID from the server.\n      return {\n        installationEntry: yield registrationPromise\n      };\n    }\n    return {\n      installationEntry,\n      registrationPromise\n    };\n  });\n  return _getInstallationEntry.apply(this, arguments);\n}\nfunction updateOrCreateInstallationEntry(oldEntry) {\n  const entry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n  };\n\n  return clearTimedOutRequest(entry);\n}\n/**\r\n * If the Firebase Installation is not registered yet, this will trigger the\r\n * registration and return an InProgressInstallationEntry.\r\n *\r\n * If registrationPromise does not exist, the installationEntry is guaranteed\r\n * to be registered.\r\n */\nfunction triggerRegistrationIfNecessary(installations, installationEntry) {\n  if (installationEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */));\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: 1 /* RequestStatus.IN_PROGRESS */,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(installations, inProgressEntry);\n    return {\n      installationEntry: inProgressEntry,\n      registrationPromise\n    };\n  } else if (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return {\n      installationEntry\n    };\n  }\n}\n/** This will be executed only once for each new Firebase Installation. */\nfunction registerInstallation(_x12, _x13) {\n  return _registerInstallation.apply(this, arguments);\n}\n/** Call if FID registration is pending in another request. */\nfunction _registerInstallation() {\n  _registerInstallation = _asyncToGenerator(function* (installations, installationEntry) {\n    try {\n      const registeredInstallationEntry = yield createInstallationRequest(installations, installationEntry);\n      return set(installations.appConfig, registeredInstallationEntry);\n    } catch (e) {\n      if (isServerError(e) && e.customData.serverCode === 409) {\n        // Server returned a \"FID can not be used\" error.\n        // Generate a new ID next time.\n        yield remove(installations.appConfig);\n      } else {\n        // Registration failed. Set FID as not registered.\n        yield set(installations.appConfig, {\n          fid: installationEntry.fid,\n          registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n        });\n      }\n\n      throw e;\n    }\n  });\n  return _registerInstallation.apply(this, arguments);\n}\nfunction waitUntilFidRegistration(_x14) {\n  return _waitUntilFidRegistration.apply(this, arguments);\n}\n/**\r\n * Called only if there is a CreateInstallation request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * CreateInstallation request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\nfunction _waitUntilFidRegistration() {\n  _waitUntilFidRegistration = _asyncToGenerator(function* (installations) {\n    // Unfortunately, there is no way of reliably observing when a value in\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n    // so we need to poll.\n    let entry = yield updateInstallationRequest(installations.appConfig);\n    while (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n      // createInstallation request still in progress.\n      yield sleep(100);\n      entry = yield updateInstallationRequest(installations.appConfig);\n    }\n    if (entry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n      // The request timed out or failed in a different call. Try again.\n      const {\n        installationEntry,\n        registrationPromise\n      } = yield getInstallationEntry(installations);\n      if (registrationPromise) {\n        return registrationPromise;\n      } else {\n        // if there is no registrationPromise, entry is registered.\n        return installationEntry;\n      }\n    }\n    return entry;\n  });\n  return _waitUntilFidRegistration.apply(this, arguments);\n}\nfunction updateInstallationRequest(appConfig) {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */);\n    }\n\n    return clearTimedOutRequest(oldEntry);\n  });\n}\nfunction clearTimedOutRequest(entry) {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n    };\n  }\n\n  return entry;\n}\nfunction hasInstallationRequestTimedOut(installationEntry) {\n  return installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */ && installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction generateAuthTokenRequest(_x15, _x16) {\n  return _generateAuthTokenRequest.apply(this, arguments);\n}\nfunction _generateAuthTokenRequest() {\n  _generateAuthTokenRequest = _asyncToGenerator(function* ({\n    appConfig,\n    heartbeatServiceProvider\n  }, installationEntry) {\n    const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\n    // If heartbeat service exists, add the heartbeat string to the header.\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\n      optional: true\n    });\n    if (heartbeatService) {\n      const heartbeatsHeader = yield heartbeatService.getHeartbeatsHeader();\n      if (heartbeatsHeader) {\n        headers.append('x-firebase-client', heartbeatsHeader);\n      }\n    }\n    const body = {\n      installation: {\n        sdkVersion: PACKAGE_VERSION,\n        appId: appConfig.appId\n      }\n    };\n    const request = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(body)\n    };\n    const response = yield retryIfServerError(() => fetch(endpoint, request));\n    if (response.ok) {\n      const responseValue = yield response.json();\n      const completedAuthToken = extractAuthTokenInfoFromResponse(responseValue);\n      return completedAuthToken;\n    } else {\n      throw yield getErrorFromResponse('Generate Auth Token', response);\n    }\n  });\n  return _generateAuthTokenRequest.apply(this, arguments);\n}\nfunction getGenerateAuthTokenEndpoint(appConfig, {\n  fid\n}) {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns a valid authentication token for the installation. Generates a new\r\n * token if one doesn't exist, is expired or about to expire.\r\n *\r\n * Should only be called if the Firebase Installation is registered.\r\n */\nfunction refreshAuthToken(_x17) {\n  return _refreshAuthToken.apply(this, arguments);\n}\n/**\r\n * Call only if FID is registered and Auth Token request is in progress.\r\n *\r\n * Waits until the current pending request finishes. If the request times out,\r\n * tries once in this thread as well.\r\n */\nfunction _refreshAuthToken() {\n  _refreshAuthToken = _asyncToGenerator(function* (installations, forceRefresh = false) {\n    let tokenPromise;\n    const entry = yield update(installations.appConfig, oldEntry => {\n      if (!isEntryRegistered(oldEntry)) {\n        throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\n      }\n\n      const oldAuthToken = oldEntry.authToken;\n      if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n        // There is a valid token in the DB.\n        return oldEntry;\n      } else if (oldAuthToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n        // There already is a token request in progress.\n        tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n        return oldEntry;\n      } else {\n        // No token or token expired.\n        if (!navigator.onLine) {\n          throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\n        }\n\n        const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n        tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n        return inProgressEntry;\n      }\n    });\n    const authToken = tokenPromise ? yield tokenPromise : entry.authToken;\n    return authToken;\n  });\n  return _refreshAuthToken.apply(this, arguments);\n}\nfunction waitUntilAuthTokenRequest(_x18, _x19) {\n  return _waitUntilAuthTokenRequest.apply(this, arguments);\n}\n/**\r\n * Called only if there is a GenerateAuthToken request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * GenerateAuthToken request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\nfunction _waitUntilAuthTokenRequest() {\n  _waitUntilAuthTokenRequest = _asyncToGenerator(function* (installations, forceRefresh) {\n    // Unfortunately, there is no way of reliably observing when a value in\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n    // so we need to poll.\n    let entry = yield updateAuthTokenRequest(installations.appConfig);\n    while (entry.authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n      // generateAuthToken still in progress.\n      yield sleep(100);\n      entry = yield updateAuthTokenRequest(installations.appConfig);\n    }\n    const authToken = entry.authToken;\n    if (authToken.requestStatus === 0 /* RequestStatus.NOT_STARTED */) {\n      // The request timed out or failed in a different call. Try again.\n      return refreshAuthToken(installations, forceRefresh);\n    } else {\n      return authToken;\n    }\n  });\n  return _waitUntilAuthTokenRequest.apply(this, arguments);\n}\nfunction updateAuthTokenRequest(appConfig) {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return Object.assign(Object.assign({}, oldEntry), {\n        authToken: {\n          requestStatus: 0 /* RequestStatus.NOT_STARTED */\n        }\n      });\n    }\n\n    return oldEntry;\n  });\n}\nfunction fetchAuthTokenFromServer(_x20, _x21) {\n  return _fetchAuthTokenFromServer.apply(this, arguments);\n}\nfunction _fetchAuthTokenFromServer() {\n  _fetchAuthTokenFromServer = _asyncToGenerator(function* (installations, installationEntry) {\n    try {\n      const authToken = yield generateAuthTokenRequest(installations, installationEntry);\n      const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), {\n        authToken\n      });\n      yield set(installations.appConfig, updatedInstallationEntry);\n      return authToken;\n    } catch (e) {\n      if (isServerError(e) && (e.customData.serverCode === 401 || e.customData.serverCode === 404)) {\n        // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n        // Generate a new ID next time.\n        yield remove(installations.appConfig);\n      } else {\n        const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), {\n          authToken: {\n            requestStatus: 0 /* RequestStatus.NOT_STARTED */\n          }\n        });\n        yield set(installations.appConfig, updatedInstallationEntry);\n      }\n      throw e;\n    }\n  });\n  return _fetchAuthTokenFromServer.apply(this, arguments);\n}\nfunction isEntryRegistered(installationEntry) {\n  return installationEntry !== undefined && installationEntry.registrationStatus === 2 /* RequestStatus.COMPLETED */;\n}\n\nfunction isAuthTokenValid(authToken) {\n  return authToken.requestStatus === 2 /* RequestStatus.COMPLETED */ && !isAuthTokenExpired(authToken);\n}\nfunction isAuthTokenExpired(authToken) {\n  const now = Date.now();\n  return now < authToken.creationTime || authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER;\n}\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(oldEntry) {\n  const inProgressAuthToken = {\n    requestStatus: 1 /* RequestStatus.IN_PROGRESS */,\n    requestTime: Date.now()\n  };\n  return Object.assign(Object.assign({}, oldEntry), {\n    authToken: inProgressAuthToken\n  });\n}\nfunction hasAuthTokenRequestTimedOut(authToken) {\n  return authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */ && authToken.requestTime + PENDING_TIMEOUT_MS < Date.now();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Creates a Firebase Installation if there isn't one for the app and\r\n * returns the Installation ID.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\nfunction getId(_x22) {\n  return _getId.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns a Firebase Installations auth token, identifying the current\r\n * Firebase Installation.\r\n * @param installations - The `Installations` instance.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\nfunction _getId() {\n  _getId = _asyncToGenerator(function* (installations) {\n    const installationsImpl = installations;\n    const {\n      installationEntry,\n      registrationPromise\n    } = yield getInstallationEntry(installationsImpl);\n    if (registrationPromise) {\n      registrationPromise.catch(console.error);\n    } else {\n      // If the installation is already registered, update the authentication\n      // token if needed.\n      refreshAuthToken(installationsImpl).catch(console.error);\n    }\n    return installationEntry.fid;\n  });\n  return _getId.apply(this, arguments);\n}\nfunction getToken(_x23) {\n  return _getToken.apply(this, arguments);\n}\nfunction _getToken() {\n  _getToken = _asyncToGenerator(function* (installations, forceRefresh = false) {\n    const installationsImpl = installations;\n    yield completeInstallationRegistration(installationsImpl);\n    // At this point we either have a Registered Installation in the DB, or we've\n    // already thrown an error.\n    const authToken = yield refreshAuthToken(installationsImpl, forceRefresh);\n    return authToken.token;\n  });\n  return _getToken.apply(this, arguments);\n}\nfunction completeInstallationRegistration(_x24) {\n  return _completeInstallationRegistration.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _completeInstallationRegistration() {\n  _completeInstallationRegistration = _asyncToGenerator(function* (installations) {\n    const {\n      registrationPromise\n    } = yield getInstallationEntry(installations);\n    if (registrationPromise) {\n      // A createInstallation request is in progress. Wait until it finishes.\n      yield registrationPromise;\n    }\n  });\n  return _completeInstallationRegistration.apply(this, arguments);\n}\nfunction deleteInstallationRequest(_x25, _x26) {\n  return _deleteInstallationRequest.apply(this, arguments);\n}\nfunction _deleteInstallationRequest() {\n  _deleteInstallationRequest = _asyncToGenerator(function* (appConfig, installationEntry) {\n    const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\n    const request = {\n      method: 'DELETE',\n      headers\n    };\n    const response = yield retryIfServerError(() => fetch(endpoint, request));\n    if (!response.ok) {\n      throw yield getErrorFromResponse('Delete Installation', response);\n    }\n  });\n  return _deleteInstallationRequest.apply(this, arguments);\n}\nfunction getDeleteEndpoint(appConfig, {\n  fid\n}) {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Deletes the Firebase Installation and all associated data.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\nfunction deleteInstallations(_x27) {\n  return _deleteInstallations.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Sets a new callback that will get called when Installation ID changes.\r\n * Returns an unsubscribe function that will remove the callback when called.\r\n * @param installations - The `Installations` instance.\r\n * @param callback - The callback function that is invoked when FID changes.\r\n * @returns A function that can be called to unsubscribe.\r\n *\r\n * @public\r\n */\nfunction _deleteInstallations() {\n  _deleteInstallations = _asyncToGenerator(function* (installations) {\n    const {\n      appConfig\n    } = installations;\n    const entry = yield update(appConfig, oldEntry => {\n      if (oldEntry && oldEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n        // Delete the unregistered entry without sending a deleteInstallation request.\n        return undefined;\n      }\n      return oldEntry;\n    });\n    if (entry) {\n      if (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n        // Can't delete while trying to register.\n        throw ERROR_FACTORY.create(\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */);\n      } else if (entry.registrationStatus === 2 /* RequestStatus.COMPLETED */) {\n        if (!navigator.onLine) {\n          throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\n        } else {\n          yield deleteInstallationRequest(appConfig, entry);\n          yield remove(appConfig);\n        }\n      }\n    }\n  });\n  return _deleteInstallations.apply(this, arguments);\n}\nfunction onIdChange(installations, callback) {\n  const {\n    appConfig\n  } = installations;\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns an instance of {@link Installations} associated with the given\r\n * {@link @firebase/app#FirebaseApp} instance.\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * @public\r\n */\nfunction getInstallations(app = getApp()) {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction extractAppConfig(app) {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n  // Required app config keys\n  const configKeys = ['projectId', 'apiKey', 'appId'];\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n  return {\n    appName: app.name,\n    projectId: app.options.projectId,\n    apiKey: app.options.apiKey,\n    appId: app.options.appId\n  };\n}\nfunction getMissingValueError(valueName) {\n  return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\n    valueName\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\nconst publicFactory = container => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n  const installationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\nconst internalFactory = container => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n  const installationsInternal = {\n    getId: () => getId(installations),\n    getToken: forceRefresh => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\nfunction registerInstallations() {\n  _registerComponent(new Component(INSTALLATIONS_NAME, publicFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  _registerComponent(new Component(INSTALLATIONS_NAME_INTERNAL, internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n}\n\n/**\r\n * Firebase Installations\r\n *\r\n * @packageDocumentation\r\n */\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\nregisterVersion(name, version, 'esm2017');\nexport { deleteInstallations, getId, getInstallations, getToken, onIdChange };", "map": {"version": 3, "names": ["_get<PERSON><PERSON><PERSON>", "getApp", "_registerComponent", "registerVersion", "Component", "ErrorFactory", "FirebaseError", "openDB", "name", "version", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "SERVICE", "SERVICE_NAME", "ERROR_DESCRIPTION_MAP", "ERROR_FACTORY", "isServerError", "error", "code", "includes", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "getExpiresInFromResponseExpiresIn", "creationTime", "Date", "now", "getErrorFromResponse", "_x", "_x2", "_getErrorFromResponse", "apply", "arguments", "_asyncToGenerator", "requestName", "responseJson", "json", "errorData", "create", "serverCode", "serverMessage", "message", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "_x3", "_retryIfServerError", "fn", "result", "responseExpiresIn", "Number", "replace", "createInstallationRequest", "_x4", "_x5", "_createInstallationRequest", "heartbeatServiceProvider", "fid", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "appId", "sdkVersion", "request", "method", "JSON", "stringify", "fetch", "ok", "responseValue", "registeredInstallationEntry", "registrationStatus", "authToken", "sleep", "ms", "Promise", "resolve", "setTimeout", "bufferToBase64UrlSafe", "array", "b64", "btoa", "String", "fromCharCode", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "crypto", "self", "msCrypto", "getRandomValues", "encode", "test", "_a", "b64String", "substr", "<PERSON><PERSON><PERSON>", "appName", "fidChangeCallbacks", "Map", "fidChanged", "key", "callFidChangeCallbacks", "broadcastFidChange", "addCallback", "callback", "getBroadcastChannel", "callbackSet", "get", "Set", "set", "add", "removeCallback", "delete", "size", "closeBroadcastChannel", "callbacks", "channel", "postMessage", "broadcastChannel", "BroadcastChannel", "onmessage", "e", "data", "close", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgrade", "db", "oldVersion", "createObjectStore", "_x6", "_x7", "_set", "value", "tx", "transaction", "objectStore", "oldValue", "put", "done", "remove", "_x8", "_remove", "update", "_x9", "_x10", "_update", "updateFn", "store", "newValue", "undefined", "getInstallationEntry", "_x11", "_getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "entryWithPromise", "triggerRegistrationIfNecessary", "entry", "clearTimedOutRequest", "navigator", "onLine", "registrationPromiseWithError", "reject", "inProgressEntry", "registrationTime", "registerInstallation", "waitUntilFidRegistration", "_x12", "_x13", "_registerInstallation", "customData", "_x14", "_waitUntilFidRegistration", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "_x15", "_x16", "_generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "completedAuthToken", "refreshAuthToken", "_x17", "_refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "waitUntilAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "fetchAuthTokenFromServer", "_x18", "_x19", "_waitUntilAuthTokenRequest", "updateAuthTokenRequest", "hasAuthTokenRequestTimedOut", "Object", "assign", "_x20", "_x21", "_fetchAuthTokenFromServer", "updatedInstallationEntry", "isAuthTokenExpired", "inProgressAuthToken", "requestTime", "getId", "_x22", "_getId", "installationsImpl", "catch", "console", "getToken", "_x23", "_getToken", "completeInstallationRegistration", "_x24", "_completeInstallationRegistration", "deleteInstallationRequest", "_x25", "_x26", "_deleteInstallationRequest", "getDeleteEndpoint", "deleteInstallations", "_x27", "_deleteInstallations", "onIdChange", "getInstallations", "app", "extractAppConfig", "options", "getMissingValueError", "config<PERSON><PERSON><PERSON>", "keyName", "valueName", "INSTALLATIONS_NAME", "INSTALLATIONS_NAME_INTERNAL", "publicFactory", "container", "get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "installationsInternal", "registerInstallations"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/node_modules/@firebase/installations/dist/esm/index.esm2017.js"], "sourcesContent": ["import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\nconst name = \"@firebase/installations\";\nconst version = \"0.6.4\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst PENDING_TIMEOUT_MS = 10000;\r\nconst PACKAGE_VERSION = `w:${version}`;\r\nconst INTERNAL_AUTH_VERSION = 'FIS_v2';\r\nconst INSTALLATIONS_API_URL = 'https://firebaseinstallations.googleapis.com/v1';\r\nconst TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\r\nconst SERVICE = 'installations';\r\nconst SERVICE_NAME = 'Installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERROR_DESCRIPTION_MAP = {\r\n    [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\r\n    [\"not-registered\" /* ErrorCode.NOT_REGISTERED */]: 'Firebase Installation is not registered.',\r\n    [\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */]: 'Firebase Installation not found.',\r\n    [\"request-failed\" /* ErrorCode.REQUEST_FAILED */]: '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\r\n    [\"app-offline\" /* ErrorCode.APP_OFFLINE */]: 'Could not process request. Application offline.',\r\n    [\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */]: \"Can't delete installation while there is a pending registration request.\"\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\r\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\r\nfunction isServerError(error) {\r\n    return (error instanceof FirebaseError &&\r\n        error.code.includes(\"request-failed\" /* ErrorCode.REQUEST_FAILED */));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getInstallationsEndpoint({ projectId }) {\r\n    return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\r\n}\r\nfunction extractAuthTokenInfoFromResponse(response) {\r\n    return {\r\n        token: response.token,\r\n        requestStatus: 2 /* RequestStatus.COMPLETED */,\r\n        expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\r\n        creationTime: Date.now()\r\n    };\r\n}\r\nasync function getErrorFromResponse(requestName, response) {\r\n    const responseJson = await response.json();\r\n    const errorData = responseJson.error;\r\n    return ERROR_FACTORY.create(\"request-failed\" /* ErrorCode.REQUEST_FAILED */, {\r\n        requestName,\r\n        serverCode: errorData.code,\r\n        serverMessage: errorData.message,\r\n        serverStatus: errorData.status\r\n    });\r\n}\r\nfunction getHeaders({ apiKey }) {\r\n    return new Headers({\r\n        'Content-Type': 'application/json',\r\n        Accept: 'application/json',\r\n        'x-goog-api-key': apiKey\r\n    });\r\n}\r\nfunction getHeadersWithAuth(appConfig, { refreshToken }) {\r\n    const headers = getHeaders(appConfig);\r\n    headers.append('Authorization', getAuthorizationHeader(refreshToken));\r\n    return headers;\r\n}\r\n/**\r\n * Calls the passed in fetch wrapper and returns the response.\r\n * If the returned response has a status of 5xx, re-runs the function once and\r\n * returns the response.\r\n */\r\nasync function retryIfServerError(fn) {\r\n    const result = await fn();\r\n    if (result.status >= 500 && result.status < 600) {\r\n        // Internal Server Error. Retry request.\r\n        return fn();\r\n    }\r\n    return result;\r\n}\r\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn) {\r\n    // This works because the server will never respond with fractions of a second.\r\n    return Number(responseExpiresIn.replace('s', '000'));\r\n}\r\nfunction getAuthorizationHeader(refreshToken) {\r\n    return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function createInstallationRequest({ appConfig, heartbeatServiceProvider }, { fid }) {\r\n    const endpoint = getInstallationsEndpoint(appConfig);\r\n    const headers = getHeaders(appConfig);\r\n    // If heartbeat service exists, add the heartbeat string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers.append('x-firebase-client', heartbeatsHeader);\r\n        }\r\n    }\r\n    const body = {\r\n        fid,\r\n        authVersion: INTERNAL_AUTH_VERSION,\r\n        appId: appConfig.appId,\r\n        sdkVersion: PACKAGE_VERSION\r\n    };\r\n    const request = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (response.ok) {\r\n        const responseValue = await response.json();\r\n        const registeredInstallationEntry = {\r\n            fid: responseValue.fid || fid,\r\n            registrationStatus: 2 /* RequestStatus.COMPLETED */,\r\n            refreshToken: responseValue.refreshToken,\r\n            authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\r\n        };\r\n        return registeredInstallationEntry;\r\n    }\r\n    else {\r\n        throw await getErrorFromResponse('Create Installation', response);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a promise that resolves after given time passes. */\r\nfunction sleep(ms) {\r\n    return new Promise(resolve => {\r\n        setTimeout(resolve, ms);\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction bufferToBase64UrlSafe(array) {\r\n    const b64 = btoa(String.fromCharCode(...array));\r\n    return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\r\nconst INVALID_FID = '';\r\n/**\r\n * Generates a new FID using random values from Web Crypto API.\r\n * Returns an empty string if FID generation fails for any reason.\r\n */\r\nfunction generateFid() {\r\n    try {\r\n        // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\r\n        // bytes. our implementation generates a 17 byte array instead.\r\n        const fidByteArray = new Uint8Array(17);\r\n        const crypto = self.crypto || self.msCrypto;\r\n        crypto.getRandomValues(fidByteArray);\r\n        // Replace the first 4 random bits with the constant FID header of 0b0111.\r\n        fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\r\n        const fid = encode(fidByteArray);\r\n        return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\r\n    }\r\n    catch (_a) {\r\n        // FID generation errored\r\n        return INVALID_FID;\r\n    }\r\n}\r\n/** Converts a FID Uint8Array to a base64 string representation. */\r\nfunction encode(fidByteArray) {\r\n    const b64String = bufferToBase64UrlSafe(fidByteArray);\r\n    // Remove the 23rd character that was added because of the extra 4 bits at the\r\n    // end of our 17 byte array, and the '=' padding.\r\n    return b64String.substr(0, 22);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a string key that can be used to identify the app. */\r\nfunction getKey(appConfig) {\r\n    return `${appConfig.appName}!${appConfig.appId}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst fidChangeCallbacks = new Map();\r\n/**\r\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\r\n * change to other tabs.\r\n */\r\nfunction fidChanged(appConfig, fid) {\r\n    const key = getKey(appConfig);\r\n    callFidChangeCallbacks(key, fid);\r\n    broadcastFidChange(key, fid);\r\n}\r\nfunction addCallback(appConfig, callback) {\r\n    // Open the broadcast channel if it's not already open,\r\n    // to be able to listen to change events from other tabs.\r\n    getBroadcastChannel();\r\n    const key = getKey(appConfig);\r\n    let callbackSet = fidChangeCallbacks.get(key);\r\n    if (!callbackSet) {\r\n        callbackSet = new Set();\r\n        fidChangeCallbacks.set(key, callbackSet);\r\n    }\r\n    callbackSet.add(callback);\r\n}\r\nfunction removeCallback(appConfig, callback) {\r\n    const key = getKey(appConfig);\r\n    const callbackSet = fidChangeCallbacks.get(key);\r\n    if (!callbackSet) {\r\n        return;\r\n    }\r\n    callbackSet.delete(callback);\r\n    if (callbackSet.size === 0) {\r\n        fidChangeCallbacks.delete(key);\r\n    }\r\n    // Close broadcast channel if there are no more callbacks.\r\n    closeBroadcastChannel();\r\n}\r\nfunction callFidChangeCallbacks(key, fid) {\r\n    const callbacks = fidChangeCallbacks.get(key);\r\n    if (!callbacks) {\r\n        return;\r\n    }\r\n    for (const callback of callbacks) {\r\n        callback(fid);\r\n    }\r\n}\r\nfunction broadcastFidChange(key, fid) {\r\n    const channel = getBroadcastChannel();\r\n    if (channel) {\r\n        channel.postMessage({ key, fid });\r\n    }\r\n    closeBroadcastChannel();\r\n}\r\nlet broadcastChannel = null;\r\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\r\nfunction getBroadcastChannel() {\r\n    if (!broadcastChannel && 'BroadcastChannel' in self) {\r\n        broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\r\n        broadcastChannel.onmessage = e => {\r\n            callFidChangeCallbacks(e.data.key, e.data.fid);\r\n        };\r\n    }\r\n    return broadcastChannel;\r\n}\r\nfunction closeBroadcastChannel() {\r\n    if (fidChangeCallbacks.size === 0 && broadcastChannel) {\r\n        broadcastChannel.close();\r\n        broadcastChannel = null;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DATABASE_NAME = 'firebase-installations-database';\r\nconst DATABASE_VERSION = 1;\r\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\r\nlet dbPromise = null;\r\nfunction getDbPromise() {\r\n    if (!dbPromise) {\r\n        dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\r\n            upgrade: (db, oldVersion) => {\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (oldVersion) {\r\n                    case 0:\r\n                        db.createObjectStore(OBJECT_STORE_NAME);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return dbPromise;\r\n}\r\n/** Assigns or overwrites the record for the given key with the given value. */\r\nasync function set(appConfig, value) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    const objectStore = tx.objectStore(OBJECT_STORE_NAME);\r\n    const oldValue = (await objectStore.get(key));\r\n    await objectStore.put(value, key);\r\n    await tx.done;\r\n    if (!oldValue || oldValue.fid !== value.fid) {\r\n        fidChanged(appConfig, value.fid);\r\n    }\r\n    return value;\r\n}\r\n/** Removes record(s) from the objectStore that match the given key. */\r\nasync function remove(appConfig) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    await tx.objectStore(OBJECT_STORE_NAME).delete(key);\r\n    await tx.done;\r\n}\r\n/**\r\n * Atomically updates a record with the result of updateFn, which gets\r\n * called with the current value. If newValue is undefined, the record is\r\n * deleted instead.\r\n * @return Updated value\r\n */\r\nasync function update(appConfig, updateFn) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    const store = tx.objectStore(OBJECT_STORE_NAME);\r\n    const oldValue = (await store.get(key));\r\n    const newValue = updateFn(oldValue);\r\n    if (newValue === undefined) {\r\n        await store.delete(key);\r\n    }\r\n    else {\r\n        await store.put(newValue, key);\r\n    }\r\n    await tx.done;\r\n    if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\r\n        fidChanged(appConfig, newValue.fid);\r\n    }\r\n    return newValue;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Updates and returns the InstallationEntry from the database.\r\n * Also triggers a registration request if it is necessary and possible.\r\n */\r\nasync function getInstallationEntry(installations) {\r\n    let registrationPromise;\r\n    const installationEntry = await update(installations.appConfig, oldEntry => {\r\n        const installationEntry = updateOrCreateInstallationEntry(oldEntry);\r\n        const entryWithPromise = triggerRegistrationIfNecessary(installations, installationEntry);\r\n        registrationPromise = entryWithPromise.registrationPromise;\r\n        return entryWithPromise.installationEntry;\r\n    });\r\n    if (installationEntry.fid === INVALID_FID) {\r\n        // FID generation failed. Waiting for the FID from the server.\r\n        return { installationEntry: await registrationPromise };\r\n    }\r\n    return {\r\n        installationEntry,\r\n        registrationPromise\r\n    };\r\n}\r\n/**\r\n * Creates a new Installation Entry if one does not exist.\r\n * Also clears timed out pending requests.\r\n */\r\nfunction updateOrCreateInstallationEntry(oldEntry) {\r\n    const entry = oldEntry || {\r\n        fid: generateFid(),\r\n        registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n    };\r\n    return clearTimedOutRequest(entry);\r\n}\r\n/**\r\n * If the Firebase Installation is not registered yet, this will trigger the\r\n * registration and return an InProgressInstallationEntry.\r\n *\r\n * If registrationPromise does not exist, the installationEntry is guaranteed\r\n * to be registered.\r\n */\r\nfunction triggerRegistrationIfNecessary(installations, installationEntry) {\r\n    if (installationEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        if (!navigator.onLine) {\r\n            // Registration required but app is offline.\r\n            const registrationPromiseWithError = Promise.reject(ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */));\r\n            return {\r\n                installationEntry,\r\n                registrationPromise: registrationPromiseWithError\r\n            };\r\n        }\r\n        // Try registering. Change status to IN_PROGRESS.\r\n        const inProgressEntry = {\r\n            fid: installationEntry.fid,\r\n            registrationStatus: 1 /* RequestStatus.IN_PROGRESS */,\r\n            registrationTime: Date.now()\r\n        };\r\n        const registrationPromise = registerInstallation(installations, inProgressEntry);\r\n        return { installationEntry: inProgressEntry, registrationPromise };\r\n    }\r\n    else if (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        return {\r\n            installationEntry,\r\n            registrationPromise: waitUntilFidRegistration(installations)\r\n        };\r\n    }\r\n    else {\r\n        return { installationEntry };\r\n    }\r\n}\r\n/** This will be executed only once for each new Firebase Installation. */\r\nasync function registerInstallation(installations, installationEntry) {\r\n    try {\r\n        const registeredInstallationEntry = await createInstallationRequest(installations, installationEntry);\r\n        return set(installations.appConfig, registeredInstallationEntry);\r\n    }\r\n    catch (e) {\r\n        if (isServerError(e) && e.customData.serverCode === 409) {\r\n            // Server returned a \"FID can not be used\" error.\r\n            // Generate a new ID next time.\r\n            await remove(installations.appConfig);\r\n        }\r\n        else {\r\n            // Registration failed. Set FID as not registered.\r\n            await set(installations.appConfig, {\r\n                fid: installationEntry.fid,\r\n                registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n            });\r\n        }\r\n        throw e;\r\n    }\r\n}\r\n/** Call if FID registration is pending in another request. */\r\nasync function waitUntilFidRegistration(installations) {\r\n    // Unfortunately, there is no way of reliably observing when a value in\r\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\r\n    // so we need to poll.\r\n    let entry = await updateInstallationRequest(installations.appConfig);\r\n    while (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        // createInstallation request still in progress.\r\n        await sleep(100);\r\n        entry = await updateInstallationRequest(installations.appConfig);\r\n    }\r\n    if (entry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        // The request timed out or failed in a different call. Try again.\r\n        const { installationEntry, registrationPromise } = await getInstallationEntry(installations);\r\n        if (registrationPromise) {\r\n            return registrationPromise;\r\n        }\r\n        else {\r\n            // if there is no registrationPromise, entry is registered.\r\n            return installationEntry;\r\n        }\r\n    }\r\n    return entry;\r\n}\r\n/**\r\n * Called only if there is a CreateInstallation request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * CreateInstallation request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\r\nfunction updateInstallationRequest(appConfig) {\r\n    return update(appConfig, oldEntry => {\r\n        if (!oldEntry) {\r\n            throw ERROR_FACTORY.create(\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */);\r\n        }\r\n        return clearTimedOutRequest(oldEntry);\r\n    });\r\n}\r\nfunction clearTimedOutRequest(entry) {\r\n    if (hasInstallationRequestTimedOut(entry)) {\r\n        return {\r\n            fid: entry.fid,\r\n            registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n        };\r\n    }\r\n    return entry;\r\n}\r\nfunction hasInstallationRequestTimedOut(installationEntry) {\r\n    return (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */ &&\r\n        installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now());\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function generateAuthTokenRequest({ appConfig, heartbeatServiceProvider }, installationEntry) {\r\n    const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\r\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\r\n    // If heartbeat service exists, add the heartbeat string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers.append('x-firebase-client', heartbeatsHeader);\r\n        }\r\n    }\r\n    const body = {\r\n        installation: {\r\n            sdkVersion: PACKAGE_VERSION,\r\n            appId: appConfig.appId\r\n        }\r\n    };\r\n    const request = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (response.ok) {\r\n        const responseValue = await response.json();\r\n        const completedAuthToken = extractAuthTokenInfoFromResponse(responseValue);\r\n        return completedAuthToken;\r\n    }\r\n    else {\r\n        throw await getErrorFromResponse('Generate Auth Token', response);\r\n    }\r\n}\r\nfunction getGenerateAuthTokenEndpoint(appConfig, { fid }) {\r\n    return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a valid authentication token for the installation. Generates a new\r\n * token if one doesn't exist, is expired or about to expire.\r\n *\r\n * Should only be called if the Firebase Installation is registered.\r\n */\r\nasync function refreshAuthToken(installations, forceRefresh = false) {\r\n    let tokenPromise;\r\n    const entry = await update(installations.appConfig, oldEntry => {\r\n        if (!isEntryRegistered(oldEntry)) {\r\n            throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\r\n        }\r\n        const oldAuthToken = oldEntry.authToken;\r\n        if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\r\n            // There is a valid token in the DB.\r\n            return oldEntry;\r\n        }\r\n        else if (oldAuthToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n            // There already is a token request in progress.\r\n            tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\r\n            return oldEntry;\r\n        }\r\n        else {\r\n            // No token or token expired.\r\n            if (!navigator.onLine) {\r\n                throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\r\n            }\r\n            const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\r\n            tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\r\n            return inProgressEntry;\r\n        }\r\n    });\r\n    const authToken = tokenPromise\r\n        ? await tokenPromise\r\n        : entry.authToken;\r\n    return authToken;\r\n}\r\n/**\r\n * Call only if FID is registered and Auth Token request is in progress.\r\n *\r\n * Waits until the current pending request finishes. If the request times out,\r\n * tries once in this thread as well.\r\n */\r\nasync function waitUntilAuthTokenRequest(installations, forceRefresh) {\r\n    // Unfortunately, there is no way of reliably observing when a value in\r\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\r\n    // so we need to poll.\r\n    let entry = await updateAuthTokenRequest(installations.appConfig);\r\n    while (entry.authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        // generateAuthToken still in progress.\r\n        await sleep(100);\r\n        entry = await updateAuthTokenRequest(installations.appConfig);\r\n    }\r\n    const authToken = entry.authToken;\r\n    if (authToken.requestStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        // The request timed out or failed in a different call. Try again.\r\n        return refreshAuthToken(installations, forceRefresh);\r\n    }\r\n    else {\r\n        return authToken;\r\n    }\r\n}\r\n/**\r\n * Called only if there is a GenerateAuthToken request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * GenerateAuthToken request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\r\nfunction updateAuthTokenRequest(appConfig) {\r\n    return update(appConfig, oldEntry => {\r\n        if (!isEntryRegistered(oldEntry)) {\r\n            throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\r\n        }\r\n        const oldAuthToken = oldEntry.authToken;\r\n        if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\r\n            return Object.assign(Object.assign({}, oldEntry), { authToken: { requestStatus: 0 /* RequestStatus.NOT_STARTED */ } });\r\n        }\r\n        return oldEntry;\r\n    });\r\n}\r\nasync function fetchAuthTokenFromServer(installations, installationEntry) {\r\n    try {\r\n        const authToken = await generateAuthTokenRequest(installations, installationEntry);\r\n        const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), { authToken });\r\n        await set(installations.appConfig, updatedInstallationEntry);\r\n        return authToken;\r\n    }\r\n    catch (e) {\r\n        if (isServerError(e) &&\r\n            (e.customData.serverCode === 401 || e.customData.serverCode === 404)) {\r\n            // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\r\n            // Generate a new ID next time.\r\n            await remove(installations.appConfig);\r\n        }\r\n        else {\r\n            const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), { authToken: { requestStatus: 0 /* RequestStatus.NOT_STARTED */ } });\r\n            await set(installations.appConfig, updatedInstallationEntry);\r\n        }\r\n        throw e;\r\n    }\r\n}\r\nfunction isEntryRegistered(installationEntry) {\r\n    return (installationEntry !== undefined &&\r\n        installationEntry.registrationStatus === 2 /* RequestStatus.COMPLETED */);\r\n}\r\nfunction isAuthTokenValid(authToken) {\r\n    return (authToken.requestStatus === 2 /* RequestStatus.COMPLETED */ &&\r\n        !isAuthTokenExpired(authToken));\r\n}\r\nfunction isAuthTokenExpired(authToken) {\r\n    const now = Date.now();\r\n    return (now < authToken.creationTime ||\r\n        authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER);\r\n}\r\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\r\nfunction makeAuthTokenRequestInProgressEntry(oldEntry) {\r\n    const inProgressAuthToken = {\r\n        requestStatus: 1 /* RequestStatus.IN_PROGRESS */,\r\n        requestTime: Date.now()\r\n    };\r\n    return Object.assign(Object.assign({}, oldEntry), { authToken: inProgressAuthToken });\r\n}\r\nfunction hasAuthTokenRequestTimedOut(authToken) {\r\n    return (authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */ &&\r\n        authToken.requestTime + PENDING_TIMEOUT_MS < Date.now());\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Creates a Firebase Installation if there isn't one for the app and\r\n * returns the Installation ID.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\r\nasync function getId(installations) {\r\n    const installationsImpl = installations;\r\n    const { installationEntry, registrationPromise } = await getInstallationEntry(installationsImpl);\r\n    if (registrationPromise) {\r\n        registrationPromise.catch(console.error);\r\n    }\r\n    else {\r\n        // If the installation is already registered, update the authentication\r\n        // token if needed.\r\n        refreshAuthToken(installationsImpl).catch(console.error);\r\n    }\r\n    return installationEntry.fid;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a Firebase Installations auth token, identifying the current\r\n * Firebase Installation.\r\n * @param installations - The `Installations` instance.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\r\nasync function getToken(installations, forceRefresh = false) {\r\n    const installationsImpl = installations;\r\n    await completeInstallationRegistration(installationsImpl);\r\n    // At this point we either have a Registered Installation in the DB, or we've\r\n    // already thrown an error.\r\n    const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\r\n    return authToken.token;\r\n}\r\nasync function completeInstallationRegistration(installations) {\r\n    const { registrationPromise } = await getInstallationEntry(installations);\r\n    if (registrationPromise) {\r\n        // A createInstallation request is in progress. Wait until it finishes.\r\n        await registrationPromise;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function deleteInstallationRequest(appConfig, installationEntry) {\r\n    const endpoint = getDeleteEndpoint(appConfig, installationEntry);\r\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\r\n    const request = {\r\n        method: 'DELETE',\r\n        headers\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (!response.ok) {\r\n        throw await getErrorFromResponse('Delete Installation', response);\r\n    }\r\n}\r\nfunction getDeleteEndpoint(appConfig, { fid }) {\r\n    return `${getInstallationsEndpoint(appConfig)}/${fid}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Deletes the Firebase Installation and all associated data.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\r\nasync function deleteInstallations(installations) {\r\n    const { appConfig } = installations;\r\n    const entry = await update(appConfig, oldEntry => {\r\n        if (oldEntry && oldEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n            // Delete the unregistered entry without sending a deleteInstallation request.\r\n            return undefined;\r\n        }\r\n        return oldEntry;\r\n    });\r\n    if (entry) {\r\n        if (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n            // Can't delete while trying to register.\r\n            throw ERROR_FACTORY.create(\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */);\r\n        }\r\n        else if (entry.registrationStatus === 2 /* RequestStatus.COMPLETED */) {\r\n            if (!navigator.onLine) {\r\n                throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\r\n            }\r\n            else {\r\n                await deleteInstallationRequest(appConfig, entry);\r\n                await remove(appConfig);\r\n            }\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Sets a new callback that will get called when Installation ID changes.\r\n * Returns an unsubscribe function that will remove the callback when called.\r\n * @param installations - The `Installations` instance.\r\n * @param callback - The callback function that is invoked when FID changes.\r\n * @returns A function that can be called to unsubscribe.\r\n *\r\n * @public\r\n */\r\nfunction onIdChange(installations, callback) {\r\n    const { appConfig } = installations;\r\n    addCallback(appConfig, callback);\r\n    return () => {\r\n        removeCallback(appConfig, callback);\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns an instance of {@link Installations} associated with the given\r\n * {@link @firebase/app#FirebaseApp} instance.\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * @public\r\n */\r\nfunction getInstallations(app = getApp()) {\r\n    const installationsImpl = _getProvider(app, 'installations').getImmediate();\r\n    return installationsImpl;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction extractAppConfig(app) {\r\n    if (!app || !app.options) {\r\n        throw getMissingValueError('App Configuration');\r\n    }\r\n    if (!app.name) {\r\n        throw getMissingValueError('App Name');\r\n    }\r\n    // Required app config keys\r\n    const configKeys = [\r\n        'projectId',\r\n        'apiKey',\r\n        'appId'\r\n    ];\r\n    for (const keyName of configKeys) {\r\n        if (!app.options[keyName]) {\r\n            throw getMissingValueError(keyName);\r\n        }\r\n    }\r\n    return {\r\n        appName: app.name,\r\n        projectId: app.options.projectId,\r\n        apiKey: app.options.apiKey,\r\n        appId: app.options.appId\r\n    };\r\n}\r\nfunction getMissingValueError(valueName) {\r\n    return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\r\n        valueName\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst INSTALLATIONS_NAME = 'installations';\r\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\r\nconst publicFactory = (container) => {\r\n    const app = container.getProvider('app').getImmediate();\r\n    // Throws if app isn't configured properly.\r\n    const appConfig = extractAppConfig(app);\r\n    const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\r\n    const installationsImpl = {\r\n        app,\r\n        appConfig,\r\n        heartbeatServiceProvider,\r\n        _delete: () => Promise.resolve()\r\n    };\r\n    return installationsImpl;\r\n};\r\nconst internalFactory = (container) => {\r\n    const app = container.getProvider('app').getImmediate();\r\n    // Internal FIS instance relies on public FIS instance.\r\n    const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\r\n    const installationsInternal = {\r\n        getId: () => getId(installations),\r\n        getToken: (forceRefresh) => getToken(installations, forceRefresh)\r\n    };\r\n    return installationsInternal;\r\n};\r\nfunction registerInstallations() {\r\n    _registerComponent(new Component(INSTALLATIONS_NAME, publicFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    _registerComponent(new Component(INSTALLATIONS_NAME_INTERNAL, internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n}\n\n/**\r\n * Firebase Installations\r\n *\r\n * @packageDocumentation\r\n */\r\nregisterInstallations();\r\nregisterVersion(name, version);\r\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\nregisterVersion(name, version, 'esm2017');\n\nexport { deleteInstallations, getId, getInstallations, getToken, onIdChange };\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,eAAe;AACzF,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,YAAY,EAAEC,aAAa,QAAQ,gBAAgB;AAC5D,SAASC,MAAM,QAAQ,KAAK;AAE5B,MAAMC,IAAI,GAAG,yBAAyB;AACtC,MAAMC,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,KAAK;AAChC,MAAMC,eAAe,GAAI,KAAIF,OAAQ,EAAC;AACtC,MAAMG,qBAAqB,GAAG,QAAQ;AACtC,MAAMC,qBAAqB,GAAG,iDAAiD;AAC/E,MAAMC,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAChD,MAAMC,OAAO,GAAG,eAAe;AAC/B,MAAMC,YAAY,GAAG,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;EAC1B,CAAC,2BAA2B,CAAC,4CAA4C,iDAAiD;EAC1H,CAAC,gBAAgB,CAAC,iCAAiC,0CAA0C;EAC7F,CAAC,wBAAwB,CAAC,yCAAyC,kCAAkC;EACrG,CAAC,gBAAgB,CAAC,iCAAiC,4FAA4F;EAC/I,CAAC,aAAa,CAAC,8BAA8B,iDAAiD;EAC9F,CAAC,6BAA6B,CAAC,8CAA8C;AACjF,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIb,YAAY,CAACU,OAAO,EAAEC,YAAY,EAAEC,qBAAqB,CAAC;AACpF;AACA,SAASE,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAQA,KAAK,YAAYd,aAAa,IAClCc,KAAK,CAACC,IAAI,CAACC,QAAQ,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAAC;EAAEC;AAAU,CAAC,EAAE;EAC7C,OAAQ,GAAEX,qBAAsB,aAAYW,SAAU,gBAAe;AACzE;AACA,SAASC,gCAAgCA,CAACC,QAAQ,EAAE;EAChD,OAAO;IACHC,KAAK,EAAED,QAAQ,CAACC,KAAK;IACrBC,aAAa,EAAE,CAAC,CAAC;IACjBC,SAAS,EAAEC,iCAAiC,CAACJ,QAAQ,CAACG,SAAS,CAAC;IAChEE,YAAY,EAAEC,IAAI,CAACC,GAAG,CAAC;EAC3B,CAAC;AACL;AAAC,SACcC,oBAAoBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,iBAAA,CAAnC,WAAoCC,WAAW,EAAEf,QAAQ,EAAE;IACvD,MAAMgB,YAAY,SAAShB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IAC1C,MAAMC,SAAS,GAAGF,YAAY,CAACtB,KAAK;IACpC,OAAOF,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,gCAAgC;MACzEJ,WAAW;MACXK,UAAU,EAAEF,SAAS,CAACvB,IAAI;MAC1B0B,aAAa,EAAEH,SAAS,CAACI,OAAO;MAChCC,YAAY,EAAEL,SAAS,CAACM;IAC5B,CAAC,CAAC;EACN,CAAC;EAAA,OAAAb,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASY,UAAUA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAC5B,OAAO,IAAIC,OAAO,CAAC;IACf,cAAc,EAAE,kBAAkB;IAClCC,MAAM,EAAE,kBAAkB;IAC1B,gBAAgB,EAAEF;EACtB,CAAC,CAAC;AACN;AACA,SAASG,kBAAkBA,CAACC,SAAS,EAAE;EAAEC;AAAa,CAAC,EAAE;EACrD,MAAMC,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAC;EACrCE,OAAO,CAACC,MAAM,CAAC,eAAe,EAAEC,sBAAsB,CAACH,YAAY,CAAC,CAAC;EACrE,OAAOC,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeG,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAzB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwB,oBAAA;EAAAA,mBAAA,GAAAvB,iBAAA,CAAjC,WAAkCwB,EAAE,EAAE;IAClC,MAAMC,MAAM,SAASD,EAAE,CAAC,CAAC;IACzB,IAAIC,MAAM,CAACf,MAAM,IAAI,GAAG,IAAIe,MAAM,CAACf,MAAM,GAAG,GAAG,EAAE;MAC7C;MACA,OAAOc,EAAE,CAAC,CAAC;IACf;IACA,OAAOC,MAAM;EACjB,CAAC;EAAA,OAAAF,mBAAA,CAAAzB,KAAA,OAAAC,SAAA;AAAA;AACD,SAAST,iCAAiCA,CAACoC,iBAAiB,EAAE;EAC1D;EACA,OAAOC,MAAM,CAACD,iBAAiB,CAACE,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACxD;AACA,SAASR,sBAAsBA,CAACH,YAAY,EAAE;EAC1C,OAAQ,GAAE7C,qBAAsB,IAAG6C,YAAa,EAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeY,yBAAyBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,0BAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAwCxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAiC,2BAAA;EAAAA,0BAAA,GAAAhC,iBAAA,CAxDA,WAAyC;IAAEgB,SAAS;IAAEiB;EAAyB,CAAC,EAAE;IAAEC;EAAI,CAAC,EAAE;IACvF,MAAMC,QAAQ,GAAGpD,wBAAwB,CAACiC,SAAS,CAAC;IACpD,MAAME,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAC;IACrC;IACA,MAAMoB,gBAAgB,GAAGH,wBAAwB,CAACI,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,CAAC,CAAC;MACrE,IAAID,gBAAgB,EAAE;QAClBrB,OAAO,CAACC,MAAM,CAAC,mBAAmB,EAAEoB,gBAAgB,CAAC;MACzD;IACJ;IACA,MAAME,IAAI,GAAG;MACTP,GAAG;MACHQ,WAAW,EAAEtE,qBAAqB;MAClCuE,KAAK,EAAE3B,SAAS,CAAC2B,KAAK;MACtBC,UAAU,EAAEzE;IAChB,CAAC;IACD,MAAM0E,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACd5B,OAAO;MACPuB,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;IAC7B,CAAC;IACD,MAAMvD,QAAQ,SAASmC,kBAAkB,CAAC,MAAM4B,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI3D,QAAQ,CAACgE,EAAE,EAAE;MACb,MAAMC,aAAa,SAASjE,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAC3C,MAAMiD,2BAA2B,GAAG;QAChClB,GAAG,EAAEiB,aAAa,CAACjB,GAAG,IAAIA,GAAG;QAC7BmB,kBAAkB,EAAE,CAAC,CAAC;QACtBpC,YAAY,EAAEkC,aAAa,CAAClC,YAAY;QACxCqC,SAAS,EAAErE,gCAAgC,CAACkE,aAAa,CAACG,SAAS;MACvE,CAAC;MACD,OAAOF,2BAA2B;IACtC,CAAC,MACI;MACD,YAAY1D,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA,OAAA8C,0BAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAmBD,SAASwD,KAAKA,CAACC,EAAE,EAAE;EACf,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC1BC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC;EAC3B,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAACC,KAAK,EAAE;EAClC,MAAMC,GAAG,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAGJ,KAAK,CAAC,CAAC;EAC/C,OAAOC,GAAG,CAAClC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,iBAAiB,GAAG,mBAAmB;AAC7C,MAAMC,WAAW,GAAG,EAAE;AACtB;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,IAAI;IACA;IACA;IACA,MAAMC,YAAY,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IACvC,MAAMC,MAAM,GAAGC,IAAI,CAACD,MAAM,IAAIC,IAAI,CAACC,QAAQ;IAC3CF,MAAM,CAACG,eAAe,CAACL,YAAY,CAAC;IACpC;IACAA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,GAAIA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAW;IAC7D,MAAMnC,GAAG,GAAGyC,MAAM,CAACN,YAAY,CAAC;IAChC,OAAOH,iBAAiB,CAACU,IAAI,CAAC1C,GAAG,CAAC,GAAGA,GAAG,GAAGiC,WAAW;EAC1D,CAAC,CACD,OAAOU,EAAE,EAAE;IACP;IACA,OAAOV,WAAW;EACtB;AACJ;AACA;AACA,SAASQ,MAAMA,CAACN,YAAY,EAAE;EAC1B,MAAMS,SAAS,GAAGlB,qBAAqB,CAACS,YAAY,CAAC;EACrD;EACA;EACA,OAAOS,SAAS,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAAChE,SAAS,EAAE;EACvB,OAAQ,GAAEA,SAAS,CAACiE,OAAQ,IAAGjE,SAAS,CAAC2B,KAAM,EAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACpE,SAAS,EAAEkB,GAAG,EAAE;EAChC,MAAMmD,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;EAC7BsE,sBAAsB,CAACD,GAAG,EAAEnD,GAAG,CAAC;EAChCqD,kBAAkB,CAACF,GAAG,EAAEnD,GAAG,CAAC;AAChC;AACA,SAASsD,WAAWA,CAACxE,SAAS,EAAEyE,QAAQ,EAAE;EACtC;EACA;EACAC,mBAAmB,CAAC,CAAC;EACrB,MAAML,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;EAC7B,IAAI2E,WAAW,GAAGT,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC7C,IAAI,CAACM,WAAW,EAAE;IACdA,WAAW,GAAG,IAAIE,GAAG,CAAC,CAAC;IACvBX,kBAAkB,CAACY,GAAG,CAACT,GAAG,EAAEM,WAAW,CAAC;EAC5C;EACAA,WAAW,CAACI,GAAG,CAACN,QAAQ,CAAC;AAC7B;AACA,SAASO,cAAcA,CAAChF,SAAS,EAAEyE,QAAQ,EAAE;EACzC,MAAMJ,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;EAC7B,MAAM2E,WAAW,GAAGT,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC/C,IAAI,CAACM,WAAW,EAAE;IACd;EACJ;EACAA,WAAW,CAACM,MAAM,CAACR,QAAQ,CAAC;EAC5B,IAAIE,WAAW,CAACO,IAAI,KAAK,CAAC,EAAE;IACxBhB,kBAAkB,CAACe,MAAM,CAACZ,GAAG,CAAC;EAClC;EACA;EACAc,qBAAqB,CAAC,CAAC;AAC3B;AACA,SAASb,sBAAsBA,CAACD,GAAG,EAAEnD,GAAG,EAAE;EACtC,MAAMkE,SAAS,GAAGlB,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC7C,IAAI,CAACe,SAAS,EAAE;IACZ;EACJ;EACA,KAAK,MAAMX,QAAQ,IAAIW,SAAS,EAAE;IAC9BX,QAAQ,CAACvD,GAAG,CAAC;EACjB;AACJ;AACA,SAASqD,kBAAkBA,CAACF,GAAG,EAAEnD,GAAG,EAAE;EAClC,MAAMmE,OAAO,GAAGX,mBAAmB,CAAC,CAAC;EACrC,IAAIW,OAAO,EAAE;IACTA,OAAO,CAACC,WAAW,CAAC;MAAEjB,GAAG;MAAEnD;IAAI,CAAC,CAAC;EACrC;EACAiE,qBAAqB,CAAC,CAAC;AAC3B;AACA,IAAII,gBAAgB,GAAG,IAAI;AAC3B;AACA,SAASb,mBAAmBA,CAAA,EAAG;EAC3B,IAAI,CAACa,gBAAgB,IAAI,kBAAkB,IAAI/B,IAAI,EAAE;IACjD+B,gBAAgB,GAAG,IAAIC,gBAAgB,CAAC,uBAAuB,CAAC;IAChED,gBAAgB,CAACE,SAAS,GAAGC,CAAC,IAAI;MAC9BpB,sBAAsB,CAACoB,CAAC,CAACC,IAAI,CAACtB,GAAG,EAAEqB,CAAC,CAACC,IAAI,CAACzE,GAAG,CAAC;IAClD,CAAC;EACL;EACA,OAAOqE,gBAAgB;AAC3B;AACA,SAASJ,qBAAqBA,CAAA,EAAG;EAC7B,IAAIjB,kBAAkB,CAACgB,IAAI,KAAK,CAAC,IAAIK,gBAAgB,EAAE;IACnDA,gBAAgB,CAACK,KAAK,CAAC,CAAC;IACxBL,gBAAgB,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAG,iCAAiC;AACvD,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,iBAAiB,GAAG,8BAA8B;AACxD,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAGjJ,MAAM,CAAC8I,aAAa,EAAEC,gBAAgB,EAAE;MAChDI,OAAO,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAK;QACzB;QACA;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACFD,EAAE,CAACE,iBAAiB,CAACN,iBAAiB,CAAC;QAC/C;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAOC,SAAS;AACpB;AACA;AAAA,SACelB,GAAGA,CAAAwB,GAAA,EAAAC,GAAA;EAAA,OAAAC,IAAA,CAAA1H,KAAA,OAAAC,SAAA;AAAA;AAalB;AAAA,SAAAyH,KAAA;EAAAA,IAAA,GAAAxH,iBAAA,CAbA,WAAmBgB,SAAS,EAAEyG,KAAK,EAAE;IACjC,MAAMpC,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;IAC7B,MAAMmG,EAAE,SAASF,YAAY,CAAC,CAAC;IAC/B,MAAMS,EAAE,GAAGP,EAAE,CAACQ,WAAW,CAACZ,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMa,WAAW,GAAGF,EAAE,CAACE,WAAW,CAACb,iBAAiB,CAAC;IACrD,MAAMc,QAAQ,SAAUD,WAAW,CAAChC,GAAG,CAACP,GAAG,CAAE;IAC7C,MAAMuC,WAAW,CAACE,GAAG,CAACL,KAAK,EAAEpC,GAAG,CAAC;IACjC,MAAMqC,EAAE,CAACK,IAAI;IACb,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAAC3F,GAAG,KAAKuF,KAAK,CAACvF,GAAG,EAAE;MACzCkD,UAAU,CAACpE,SAAS,EAAEyG,KAAK,CAACvF,GAAG,CAAC;IACpC;IACA,OAAOuF,KAAK;EAChB,CAAC;EAAA,OAAAD,IAAA,CAAA1H,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEciI,MAAMA,CAAAC,GAAA;EAAA,OAAAC,OAAA,CAAApI,KAAA,OAAAC,SAAA;AAAA;AAOrB;AACA;AACA;AACA;AACA;AACA;AALA,SAAAmI,QAAA;EAAAA,OAAA,GAAAlI,iBAAA,CAPA,WAAsBgB,SAAS,EAAE;IAC7B,MAAMqE,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;IAC7B,MAAMmG,EAAE,SAASF,YAAY,CAAC,CAAC;IAC/B,MAAMS,EAAE,GAAGP,EAAE,CAACQ,WAAW,CAACZ,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMW,EAAE,CAACE,WAAW,CAACb,iBAAiB,CAAC,CAACd,MAAM,CAACZ,GAAG,CAAC;IACnD,MAAMqC,EAAE,CAACK,IAAI;EACjB,CAAC;EAAA,OAAAG,OAAA,CAAApI,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcoI,MAAMA,CAAAC,GAAA,EAAAC,IAAA;EAAA,OAAAC,OAAA,CAAAxI,KAAA,OAAAC,SAAA;AAAA;AAoBrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA,SAAAuI,QAAA;EAAAA,OAAA,GAAAtI,iBAAA,CApCA,WAAsBgB,SAAS,EAAEuH,QAAQ,EAAE;IACvC,MAAMlD,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;IAC7B,MAAMmG,EAAE,SAASF,YAAY,CAAC,CAAC;IAC/B,MAAMS,EAAE,GAAGP,EAAE,CAACQ,WAAW,CAACZ,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMyB,KAAK,GAAGd,EAAE,CAACE,WAAW,CAACb,iBAAiB,CAAC;IAC/C,MAAMc,QAAQ,SAAUW,KAAK,CAAC5C,GAAG,CAACP,GAAG,CAAE;IACvC,MAAMoD,QAAQ,GAAGF,QAAQ,CAACV,QAAQ,CAAC;IACnC,IAAIY,QAAQ,KAAKC,SAAS,EAAE;MACxB,MAAMF,KAAK,CAACvC,MAAM,CAACZ,GAAG,CAAC;IAC3B,CAAC,MACI;MACD,MAAMmD,KAAK,CAACV,GAAG,CAACW,QAAQ,EAAEpD,GAAG,CAAC;IAClC;IACA,MAAMqC,EAAE,CAACK,IAAI;IACb,IAAIU,QAAQ,KAAK,CAACZ,QAAQ,IAAIA,QAAQ,CAAC3F,GAAG,KAAKuG,QAAQ,CAACvG,GAAG,CAAC,EAAE;MAC1DkD,UAAU,CAACpE,SAAS,EAAEyH,QAAQ,CAACvG,GAAG,CAAC;IACvC;IACA,OAAOuG,QAAQ;EACnB,CAAC;EAAA,OAAAH,OAAA,CAAAxI,KAAA,OAAAC,SAAA;AAAA;AAAA,SAsBc4I,oBAAoBA,CAAAC,IAAA;EAAA,OAAAC,qBAAA,CAAA/I,KAAA,OAAAC,SAAA;AAAA;AAiBnC;AACA;AACA;AACA;AAHA,SAAA8I,sBAAA;EAAAA,qBAAA,GAAA7I,iBAAA,CAjBA,WAAoC8I,aAAa,EAAE;IAC/C,IAAIC,mBAAmB;IACvB,MAAMC,iBAAiB,SAASb,MAAM,CAACW,aAAa,CAAC9H,SAAS,EAAEiI,QAAQ,IAAI;MACxE,MAAMD,iBAAiB,GAAGE,+BAA+B,CAACD,QAAQ,CAAC;MACnE,MAAME,gBAAgB,GAAGC,8BAA8B,CAACN,aAAa,EAAEE,iBAAiB,CAAC;MACzFD,mBAAmB,GAAGI,gBAAgB,CAACJ,mBAAmB;MAC1D,OAAOI,gBAAgB,CAACH,iBAAiB;IAC7C,CAAC,CAAC;IACF,IAAIA,iBAAiB,CAAC9G,GAAG,KAAKiC,WAAW,EAAE;MACvC;MACA,OAAO;QAAE6E,iBAAiB,QAAQD;MAAoB,CAAC;IAC3D;IACA,OAAO;MACHC,iBAAiB;MACjBD;IACJ,CAAC;EACL,CAAC;EAAA,OAAAF,qBAAA,CAAA/I,KAAA,OAAAC,SAAA;AAAA;AAKD,SAASmJ,+BAA+BA,CAACD,QAAQ,EAAE;EAC/C,MAAMI,KAAK,GAAGJ,QAAQ,IAAI;IACtB/G,GAAG,EAAEkC,WAAW,CAAC,CAAC;IAClBf,kBAAkB,EAAE,CAAC,CAAC;EAC1B,CAAC;;EACD,OAAOiG,oBAAoB,CAACD,KAAK,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,8BAA8BA,CAACN,aAAa,EAAEE,iBAAiB,EAAE;EACtE,IAAIA,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;IAC5E,IAAI,CAACkG,SAAS,CAACC,MAAM,EAAE;MACnB;MACA,MAAMC,4BAA4B,GAAGhG,OAAO,CAACiG,MAAM,CAAChL,aAAa,CAAC2B,MAAM,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;MACpH,OAAO;QACH2I,iBAAiB;QACjBD,mBAAmB,EAAEU;MACzB,CAAC;IACL;IACA;IACA,MAAME,eAAe,GAAG;MACpBzH,GAAG,EAAE8G,iBAAiB,CAAC9G,GAAG;MAC1BmB,kBAAkB,EAAE,CAAC,CAAC;MACtBuG,gBAAgB,EAAEpK,IAAI,CAACC,GAAG,CAAC;IAC/B,CAAC;IACD,MAAMsJ,mBAAmB,GAAGc,oBAAoB,CAACf,aAAa,EAAEa,eAAe,CAAC;IAChF,OAAO;MAAEX,iBAAiB,EAAEW,eAAe;MAAEZ;IAAoB,CAAC;EACtE,CAAC,MACI,IAAIC,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;IACjF,OAAO;MACH2F,iBAAiB;MACjBD,mBAAmB,EAAEe,wBAAwB,CAAChB,aAAa;IAC/D,CAAC;EACL,CAAC,MACI;IACD,OAAO;MAAEE;IAAkB,CAAC;EAChC;AACJ;AACA;AAAA,SACea,oBAAoBA,CAAAE,IAAA,EAAAC,IAAA;EAAA,OAAAC,qBAAA,CAAAnK,KAAA,OAAAC,SAAA;AAAA;AAqBnC;AAAA,SAAAkK,sBAAA;EAAAA,qBAAA,GAAAjK,iBAAA,CArBA,WAAoC8I,aAAa,EAAEE,iBAAiB,EAAE;IAClE,IAAI;MACA,MAAM5F,2BAA2B,SAASvB,yBAAyB,CAACiH,aAAa,EAAEE,iBAAiB,CAAC;MACrG,OAAOlD,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAEoC,2BAA2B,CAAC;IACpE,CAAC,CACD,OAAOsD,CAAC,EAAE;MACN,IAAI/H,aAAa,CAAC+H,CAAC,CAAC,IAAIA,CAAC,CAACwD,UAAU,CAAC5J,UAAU,KAAK,GAAG,EAAE;QACrD;QACA;QACA,MAAM0H,MAAM,CAACc,aAAa,CAAC9H,SAAS,CAAC;MACzC,CAAC,MACI;QACD;QACA,MAAM8E,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAE;UAC/BkB,GAAG,EAAE8G,iBAAiB,CAAC9G,GAAG;UAC1BmB,kBAAkB,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC;MACN;;MACA,MAAMqD,CAAC;IACX;EACJ,CAAC;EAAA,OAAAuD,qBAAA,CAAAnK,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEc+J,wBAAwBA,CAAAK,IAAA;EAAA,OAAAC,yBAAA,CAAAtK,KAAA,OAAAC,SAAA;AAAA;AAuBvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAqK,0BAAA;EAAAA,yBAAA,GAAApK,iBAAA,CAvBA,WAAwC8I,aAAa,EAAE;IACnD;IACA;IACA;IACA,IAAIO,KAAK,SAASgB,yBAAyB,CAACvB,aAAa,CAAC9H,SAAS,CAAC;IACpE,OAAOqI,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MACnE;MACA,MAAME,KAAK,CAAC,GAAG,CAAC;MAChB8F,KAAK,SAASgB,yBAAyB,CAACvB,aAAa,CAAC9H,SAAS,CAAC;IACpE;IACA,IAAIqI,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MAChE;MACA,MAAM;QAAE2F,iBAAiB;QAAED;MAAoB,CAAC,SAASJ,oBAAoB,CAACG,aAAa,CAAC;MAC5F,IAAIC,mBAAmB,EAAE;QACrB,OAAOA,mBAAmB;MAC9B,CAAC,MACI;QACD;QACA,OAAOC,iBAAiB;MAC5B;IACJ;IACA,OAAOK,KAAK;EAChB,CAAC;EAAA,OAAAe,yBAAA,CAAAtK,KAAA,OAAAC,SAAA;AAAA;AASD,SAASsK,yBAAyBA,CAACrJ,SAAS,EAAE;EAC1C,OAAOmH,MAAM,CAACnH,SAAS,EAAEiI,QAAQ,IAAI;IACjC,IAAI,CAACA,QAAQ,EAAE;MACX,MAAMvK,aAAa,CAAC2B,MAAM,CAAC,wBAAwB,CAAC,sCAAsC,CAAC;IAC/F;;IACA,OAAOiJ,oBAAoB,CAACL,QAAQ,CAAC;EACzC,CAAC,CAAC;AACN;AACA,SAASK,oBAAoBA,CAACD,KAAK,EAAE;EACjC,IAAIiB,8BAA8B,CAACjB,KAAK,CAAC,EAAE;IACvC,OAAO;MACHnH,GAAG,EAAEmH,KAAK,CAACnH,GAAG;MACdmB,kBAAkB,EAAE,CAAC,CAAC;IAC1B,CAAC;EACL;;EACA,OAAOgG,KAAK;AAChB;AACA,SAASiB,8BAA8BA,CAACtB,iBAAiB,EAAE;EACvD,OAAQA,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC,mCAC/C2F,iBAAiB,CAACY,gBAAgB,GAAG1L,kBAAkB,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBe8K,wBAAwBA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,yBAAA,CAAA5K,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA2K,0BAAA;EAAAA,yBAAA,GAAA1K,iBAAA,CAAvC,WAAwC;IAAEgB,SAAS;IAAEiB;EAAyB,CAAC,EAAE+G,iBAAiB,EAAE;IAChG,MAAM7G,QAAQ,GAAGwI,4BAA4B,CAAC3J,SAAS,EAAEgI,iBAAiB,CAAC;IAC3E,MAAM9H,OAAO,GAAGH,kBAAkB,CAACC,SAAS,EAAEgI,iBAAiB,CAAC;IAChE;IACA,MAAM5G,gBAAgB,GAAGH,wBAAwB,CAACI,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,CAAC,CAAC;MACrE,IAAID,gBAAgB,EAAE;QAClBrB,OAAO,CAACC,MAAM,CAAC,mBAAmB,EAAEoB,gBAAgB,CAAC;MACzD;IACJ;IACA,MAAME,IAAI,GAAG;MACTmI,YAAY,EAAE;QACVhI,UAAU,EAAEzE,eAAe;QAC3BwE,KAAK,EAAE3B,SAAS,CAAC2B;MACrB;IACJ,CAAC;IACD,MAAME,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACd5B,OAAO;MACPuB,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;IAC7B,CAAC;IACD,MAAMvD,QAAQ,SAASmC,kBAAkB,CAAC,MAAM4B,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI3D,QAAQ,CAACgE,EAAE,EAAE;MACb,MAAMC,aAAa,SAASjE,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAC3C,MAAM0K,kBAAkB,GAAG5L,gCAAgC,CAACkE,aAAa,CAAC;MAC1E,OAAO0H,kBAAkB;IAC7B,CAAC,MACI;MACD,YAAYnL,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA,OAAAwL,yBAAA,CAAA5K,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS4K,4BAA4BA,CAAC3J,SAAS,EAAE;EAAEkB;AAAI,CAAC,EAAE;EACtD,OAAQ,GAAEnD,wBAAwB,CAACiC,SAAS,CAAE,IAAGkB,GAAI,sBAAqB;AAC9E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMe4I,gBAAgBA,CAAAC,IAAA;EAAA,OAAAC,iBAAA,CAAAlL,KAAA,OAAAC,SAAA;AAAA;AA+B/B;AACA;AACA;AACA;AACA;AACA;AALA,SAAAiL,kBAAA;EAAAA,iBAAA,GAAAhL,iBAAA,CA/BA,WAAgC8I,aAAa,EAAEmC,YAAY,GAAG,KAAK,EAAE;IACjE,IAAIC,YAAY;IAChB,MAAM7B,KAAK,SAASlB,MAAM,CAACW,aAAa,CAAC9H,SAAS,EAAEiI,QAAQ,IAAI;MAC5D,IAAI,CAACkC,iBAAiB,CAAClC,QAAQ,CAAC,EAAE;QAC9B,MAAMvK,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;MAC/E;;MACA,MAAM+K,YAAY,GAAGnC,QAAQ,CAAC3F,SAAS;MACvC,IAAI,CAAC2H,YAAY,IAAII,gBAAgB,CAACD,YAAY,CAAC,EAAE;QACjD;QACA,OAAOnC,QAAQ;MACnB,CAAC,MACI,IAAImC,YAAY,CAAChM,aAAa,KAAK,CAAC,CAAC,iCAAiC;QACvE;QACA8L,YAAY,GAAGI,yBAAyB,CAACxC,aAAa,EAAEmC,YAAY,CAAC;QACrE,OAAOhC,QAAQ;MACnB,CAAC,MACI;QACD;QACA,IAAI,CAACM,SAAS,CAACC,MAAM,EAAE;UACnB,MAAM9K,aAAa,CAAC2B,MAAM,CAAC,aAAa,CAAC,2BAA2B,CAAC;QACzE;;QACA,MAAMsJ,eAAe,GAAG4B,mCAAmC,CAACtC,QAAQ,CAAC;QACrEiC,YAAY,GAAGM,wBAAwB,CAAC1C,aAAa,EAAEa,eAAe,CAAC;QACvE,OAAOA,eAAe;MAC1B;IACJ,CAAC,CAAC;IACF,MAAMrG,SAAS,GAAG4H,YAAY,SAClBA,YAAY,GAClB7B,KAAK,CAAC/F,SAAS;IACrB,OAAOA,SAAS;EACpB,CAAC;EAAA,OAAA0H,iBAAA,CAAAlL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcuL,yBAAyBA,CAAAG,IAAA,EAAAC,IAAA;EAAA,OAAAC,0BAAA,CAAA7L,KAAA,OAAAC,SAAA;AAAA;AAmBxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAA4L,2BAAA;EAAAA,0BAAA,GAAA3L,iBAAA,CAnBA,WAAyC8I,aAAa,EAAEmC,YAAY,EAAE;IAClE;IACA;IACA;IACA,IAAI5B,KAAK,SAASuC,sBAAsB,CAAC9C,aAAa,CAAC9H,SAAS,CAAC;IACjE,OAAOqI,KAAK,CAAC/F,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,iCAAiC;MACxE;MACA,MAAMmE,KAAK,CAAC,GAAG,CAAC;MAChB8F,KAAK,SAASuC,sBAAsB,CAAC9C,aAAa,CAAC9H,SAAS,CAAC;IACjE;IACA,MAAMsC,SAAS,GAAG+F,KAAK,CAAC/F,SAAS;IACjC,IAAIA,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,iCAAiC;MAC/D;MACA,OAAO0L,gBAAgB,CAAChC,aAAa,EAAEmC,YAAY,CAAC;IACxD,CAAC,MACI;MACD,OAAO3H,SAAS;IACpB;EACJ,CAAC;EAAA,OAAAqI,0BAAA,CAAA7L,KAAA,OAAAC,SAAA;AAAA;AASD,SAAS6L,sBAAsBA,CAAC5K,SAAS,EAAE;EACvC,OAAOmH,MAAM,CAACnH,SAAS,EAAEiI,QAAQ,IAAI;IACjC,IAAI,CAACkC,iBAAiB,CAAClC,QAAQ,CAAC,EAAE;MAC9B,MAAMvK,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;IAC/E;;IACA,MAAM+K,YAAY,GAAGnC,QAAQ,CAAC3F,SAAS;IACvC,IAAIuI,2BAA2B,CAACT,YAAY,CAAC,EAAE;MAC3C,OAAOU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9C,QAAQ,CAAC,EAAE;QAAE3F,SAAS,EAAE;UAAElE,aAAa,EAAE,CAAC,CAAC;QAAgC;MAAE,CAAC,CAAC;IAC1H;;IACA,OAAO6J,QAAQ;EACnB,CAAC,CAAC;AACN;AAAC,SACcuC,wBAAwBA,CAAAQ,IAAA,EAAAC,IAAA;EAAA,OAAAC,yBAAA,CAAApM,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmM,0BAAA;EAAAA,yBAAA,GAAAlM,iBAAA,CAAvC,WAAwC8I,aAAa,EAAEE,iBAAiB,EAAE;IACtE,IAAI;MACA,MAAM1F,SAAS,SAASiH,wBAAwB,CAACzB,aAAa,EAAEE,iBAAiB,CAAC;MAClF,MAAMmD,wBAAwB,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/C,iBAAiB,CAAC,EAAE;QAAE1F;MAAU,CAAC,CAAC;MACnG,MAAMwC,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAEmL,wBAAwB,CAAC;MAC5D,OAAO7I,SAAS;IACpB,CAAC,CACD,OAAOoD,CAAC,EAAE;MACN,IAAI/H,aAAa,CAAC+H,CAAC,CAAC,KACfA,CAAC,CAACwD,UAAU,CAAC5J,UAAU,KAAK,GAAG,IAAIoG,CAAC,CAACwD,UAAU,CAAC5J,UAAU,KAAK,GAAG,CAAC,EAAE;QACtE;QACA;QACA,MAAM0H,MAAM,CAACc,aAAa,CAAC9H,SAAS,CAAC;MACzC,CAAC,MACI;QACD,MAAMmL,wBAAwB,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/C,iBAAiB,CAAC,EAAE;UAAE1F,SAAS,EAAE;YAAElE,aAAa,EAAE,CAAC,CAAC;UAAgC;QAAE,CAAC,CAAC;QACzJ,MAAM0G,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAEmL,wBAAwB,CAAC;MAChE;MACA,MAAMzF,CAAC;IACX;EACJ,CAAC;EAAA,OAAAwF,yBAAA,CAAApM,KAAA,OAAAC,SAAA;AAAA;AACD,SAASoL,iBAAiBA,CAACnC,iBAAiB,EAAE;EAC1C,OAAQA,iBAAiB,KAAKN,SAAS,IACnCM,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC;AACnD;;AACA,SAASgI,gBAAgBA,CAAC/H,SAAS,EAAE;EACjC,OAAQA,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,iCAClC,CAACgN,kBAAkB,CAAC9I,SAAS,CAAC;AACtC;AACA,SAAS8I,kBAAkBA,CAAC9I,SAAS,EAAE;EACnC,MAAM7D,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;EACtB,OAAQA,GAAG,GAAG6D,SAAS,CAAC/D,YAAY,IAChC+D,SAAS,CAAC/D,YAAY,GAAG+D,SAAS,CAACjE,SAAS,GAAGI,GAAG,GAAGnB,uBAAuB;AACpF;AACA;AACA,SAASiN,mCAAmCA,CAACtC,QAAQ,EAAE;EACnD,MAAMoD,mBAAmB,GAAG;IACxBjN,aAAa,EAAE,CAAC,CAAC;IACjBkN,WAAW,EAAE9M,IAAI,CAACC,GAAG,CAAC;EAC1B,CAAC;EACD,OAAOqM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9C,QAAQ,CAAC,EAAE;IAAE3F,SAAS,EAAE+I;EAAoB,CAAC,CAAC;AACzF;AACA,SAASR,2BAA2BA,CAACvI,SAAS,EAAE;EAC5C,OAAQA,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,mCAClCkE,SAAS,CAACgJ,WAAW,GAAGpO,kBAAkB,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOe8M,KAAKA,CAAAC,IAAA;EAAA,OAAAC,MAAA,CAAA3M,KAAA,OAAAC,SAAA;AAAA;AAcpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAA0M,OAAA;EAAAA,MAAA,GAAAzM,iBAAA,CA9BA,WAAqB8I,aAAa,EAAE;IAChC,MAAM4D,iBAAiB,GAAG5D,aAAa;IACvC,MAAM;MAAEE,iBAAiB;MAAED;IAAoB,CAAC,SAASJ,oBAAoB,CAAC+D,iBAAiB,CAAC;IAChG,IAAI3D,mBAAmB,EAAE;MACrBA,mBAAmB,CAAC4D,KAAK,CAACC,OAAO,CAAChO,KAAK,CAAC;IAC5C,CAAC,MACI;MACD;MACA;MACAkM,gBAAgB,CAAC4B,iBAAiB,CAAC,CAACC,KAAK,CAACC,OAAO,CAAChO,KAAK,CAAC;IAC5D;IACA,OAAOoK,iBAAiB,CAAC9G,GAAG;EAChC,CAAC;EAAA,OAAAuK,MAAA,CAAA3M,KAAA,OAAAC,SAAA;AAAA;AAAA,SA0Bc8M,QAAQA,CAAAC,IAAA;EAAA,OAAAC,SAAA,CAAAjN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAgN,UAAA;EAAAA,SAAA,GAAA/M,iBAAA,CAAvB,WAAwB8I,aAAa,EAAEmC,YAAY,GAAG,KAAK,EAAE;IACzD,MAAMyB,iBAAiB,GAAG5D,aAAa;IACvC,MAAMkE,gCAAgC,CAACN,iBAAiB,CAAC;IACzD;IACA;IACA,MAAMpJ,SAAS,SAASwH,gBAAgB,CAAC4B,iBAAiB,EAAEzB,YAAY,CAAC;IACzE,OAAO3H,SAAS,CAACnE,KAAK;EAC1B,CAAC;EAAA,OAAA4N,SAAA,CAAAjN,KAAA,OAAAC,SAAA;AAAA;AAAA,SACciN,gCAAgCA,CAAAC,IAAA;EAAA,OAAAC,iCAAA,CAAApN,KAAA,OAAAC,SAAA;AAAA;AAQ/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAmN,kCAAA;EAAAA,iCAAA,GAAAlN,iBAAA,CARA,WAAgD8I,aAAa,EAAE;IAC3D,MAAM;MAAEC;IAAoB,CAAC,SAASJ,oBAAoB,CAACG,aAAa,CAAC;IACzE,IAAIC,mBAAmB,EAAE;MACrB;MACA,MAAMA,mBAAmB;IAC7B;EACJ,CAAC;EAAA,OAAAmE,iCAAA,CAAApN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAkBcoN,yBAAyBA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,0BAAA,CAAAxN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAuN,2BAAA;EAAAA,0BAAA,GAAAtN,iBAAA,CAAxC,WAAyCgB,SAAS,EAAEgI,iBAAiB,EAAE;IACnE,MAAM7G,QAAQ,GAAGoL,iBAAiB,CAACvM,SAAS,EAAEgI,iBAAiB,CAAC;IAChE,MAAM9H,OAAO,GAAGH,kBAAkB,CAACC,SAAS,EAAEgI,iBAAiB,CAAC;IAChE,MAAMnG,OAAO,GAAG;MACZC,MAAM,EAAE,QAAQ;MAChB5B;IACJ,CAAC;IACD,MAAMhC,QAAQ,SAASmC,kBAAkB,CAAC,MAAM4B,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI,CAAC3D,QAAQ,CAACgE,EAAE,EAAE;MACd,YAAYxD,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA,OAAAoO,0BAAA,CAAAxN,KAAA,OAAAC,SAAA;AAAA;AACD,SAASwN,iBAAiBA,CAACvM,SAAS,EAAE;EAAEkB;AAAI,CAAC,EAAE;EAC3C,OAAQ,GAAEnD,wBAAwB,CAACiC,SAAS,CAAE,IAAGkB,GAAI,EAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMesL,mBAAmBA,CAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAA5N,KAAA,OAAAC,SAAA;AAAA;AA0BlC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAA2N,qBAAA;EAAAA,oBAAA,GAAA1N,iBAAA,CA1CA,WAAmC8I,aAAa,EAAE;IAC9C,MAAM;MAAE9H;IAAU,CAAC,GAAG8H,aAAa;IACnC,MAAMO,KAAK,SAASlB,MAAM,CAACnH,SAAS,EAAEiI,QAAQ,IAAI;MAC9C,IAAIA,QAAQ,IAAIA,QAAQ,CAAC5F,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;QAC/E;QACA,OAAOqF,SAAS;MACpB;MACA,OAAOO,QAAQ;IACnB,CAAC,CAAC;IACF,IAAII,KAAK,EAAE;MACP,IAAIA,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;QAChE;QACA,MAAM3E,aAAa,CAAC2B,MAAM,CAAC,6BAA6B,CAAC,2CAA2C,CAAC;MACzG,CAAC,MACI,IAAIgJ,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,+BAA+B;QACnE,IAAI,CAACkG,SAAS,CAACC,MAAM,EAAE;UACnB,MAAM9K,aAAa,CAAC2B,MAAM,CAAC,aAAa,CAAC,2BAA2B,CAAC;QACzE,CAAC,MACI;UACD,MAAM8M,yBAAyB,CAACnM,SAAS,EAAEqI,KAAK,CAAC;UACjD,MAAMrB,MAAM,CAAChH,SAAS,CAAC;QAC3B;MACJ;IACJ;EACJ,CAAC;EAAA,OAAA0M,oBAAA,CAAA5N,KAAA,OAAAC,SAAA;AAAA;AA2BD,SAAS4N,UAAUA,CAAC7E,aAAa,EAAErD,QAAQ,EAAE;EACzC,MAAM;IAAEzE;EAAU,CAAC,GAAG8H,aAAa;EACnCtD,WAAW,CAACxE,SAAS,EAAEyE,QAAQ,CAAC;EAChC,OAAO,MAAM;IACTO,cAAc,CAAChF,SAAS,EAAEyE,QAAQ,CAAC;EACvC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmI,gBAAgBA,CAACC,GAAG,GAAGpQ,MAAM,CAAC,CAAC,EAAE;EACtC,MAAMiP,iBAAiB,GAAGlP,YAAY,CAACqQ,GAAG,EAAE,eAAe,CAAC,CAACxL,YAAY,CAAC,CAAC;EAC3E,OAAOqK,iBAAiB;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,gBAAgBA,CAACD,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACE,OAAO,EAAE;IACtB,MAAMC,oBAAoB,CAAC,mBAAmB,CAAC;EACnD;EACA,IAAI,CAACH,GAAG,CAAC7P,IAAI,EAAE;IACX,MAAMgQ,oBAAoB,CAAC,UAAU,CAAC;EAC1C;EACA;EACA,MAAMC,UAAU,GAAG,CACf,WAAW,EACX,QAAQ,EACR,OAAO,CACV;EACD,KAAK,MAAMC,OAAO,IAAID,UAAU,EAAE;IAC9B,IAAI,CAACJ,GAAG,CAACE,OAAO,CAACG,OAAO,CAAC,EAAE;MACvB,MAAMF,oBAAoB,CAACE,OAAO,CAAC;IACvC;EACJ;EACA,OAAO;IACHjJ,OAAO,EAAE4I,GAAG,CAAC7P,IAAI;IACjBgB,SAAS,EAAE6O,GAAG,CAACE,OAAO,CAAC/O,SAAS;IAChC4B,MAAM,EAAEiN,GAAG,CAACE,OAAO,CAACnN,MAAM;IAC1B+B,KAAK,EAAEkL,GAAG,CAACE,OAAO,CAACpL;EACvB,CAAC;AACL;AACA,SAASqL,oBAAoBA,CAACG,SAAS,EAAE;EACrC,OAAOzP,aAAa,CAAC2B,MAAM,CAAC,2BAA2B,CAAC,2CAA2C;IAC/F8N;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,eAAe;AAC1C,MAAMC,2BAA2B,GAAG,wBAAwB;AAC5D,MAAMC,aAAa,GAAIC,SAAS,IAAK;EACjC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAACnM,YAAY,CAAC,CAAC;EACvD;EACA,MAAMrB,SAAS,GAAG8M,gBAAgB,CAACD,GAAG,CAAC;EACvC,MAAM5L,wBAAwB,GAAGzE,YAAY,CAACqQ,GAAG,EAAE,WAAW,CAAC;EAC/D,MAAMnB,iBAAiB,GAAG;IACtBmB,GAAG;IACH7M,SAAS;IACTiB,wBAAwB;IACxBwM,OAAO,EAAEA,CAAA,KAAMhL,OAAO,CAACC,OAAO,CAAC;EACnC,CAAC;EACD,OAAOgJ,iBAAiB;AAC5B,CAAC;AACD,MAAMgC,eAAe,GAAIH,SAAS,IAAK;EACnC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAACnM,YAAY,CAAC,CAAC;EACvD;EACA,MAAMyG,aAAa,GAAGtL,YAAY,CAACqQ,GAAG,EAAEO,kBAAkB,CAAC,CAAC/L,YAAY,CAAC,CAAC;EAC1E,MAAMsM,qBAAqB,GAAG;IAC1BpC,KAAK,EAAEA,CAAA,KAAMA,KAAK,CAACzD,aAAa,CAAC;IACjC+D,QAAQ,EAAG5B,YAAY,IAAK4B,QAAQ,CAAC/D,aAAa,EAAEmC,YAAY;EACpE,CAAC;EACD,OAAO0D,qBAAqB;AAChC,CAAC;AACD,SAASC,qBAAqBA,CAAA,EAAG;EAC7BlR,kBAAkB,CAAC,IAAIE,SAAS,CAACwQ,kBAAkB,EAAEE,aAAa,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACzG5Q,kBAAkB,CAAC,IAAIE,SAAS,CAACyQ,2BAA2B,EAAEK,eAAe,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;AAC1H;;AAEA;AACA;AACA;AACA;AACA;AACAE,qBAAqB,CAAC,CAAC;AACvBjR,eAAe,CAACK,IAAI,EAAEC,OAAO,CAAC;AAC9B;AACAN,eAAe,CAACK,IAAI,EAAEC,OAAO,EAAE,SAAS,CAAC;AAEzC,SAASuP,mBAAmB,EAAEjB,KAAK,EAAEqB,gBAAgB,EAAEf,QAAQ,EAAEc,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeTo } from './subscribeTo';\nimport { Observable } from '../Observable';\nexport function subscribeToResult(outerSubscriber, result, outerValue, outerIndex, innerSubscriber = new InnerSubscriber(outerSubscriber, outerValue, outerIndex)) {\n  if (innerSubscriber.closed) {\n    return undefined;\n  }\n  if (result instanceof Observable) {\n    return result.subscribe(innerSubscriber);\n  }\n  return subscribeTo(result)(innerSubscriber);\n}", "map": {"version": 3, "names": ["InnerSubscriber", "subscribeTo", "Observable", "subscribeToResult", "outerSubscriber", "result", "outerValue", "outerIndex", "innerSubscriber", "closed", "undefined", "subscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/subscribeToResult.js"], "sourcesContent": ["import { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeTo } from './subscribeTo';\nimport { Observable } from '../Observable';\nexport function subscribeToResult(outerSubscriber, result, outerValue, outerIndex, innerSubscriber = new InnerSubscriber(outerSubscriber, outerValue, outerIndex)) {\n    if (innerSubscriber.closed) {\n        return undefined;\n    }\n    if (result instanceof Observable) {\n        return result.subscribe(innerSubscriber);\n    }\n    return subscribeTo(result)(innerSubscriber);\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,iBAAiBA,CAACC,eAAe,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,GAAG,IAAIR,eAAe,CAACI,eAAe,EAAEE,UAAU,EAAEC,UAAU,CAAC,EAAE;EAC/J,IAAIC,eAAe,CAACC,MAAM,EAAE;IACxB,OAAOC,SAAS;EACpB;EACA,IAAIL,MAAM,YAAYH,UAAU,EAAE;IAC9B,OAAOG,MAAM,CAACM,SAAS,CAACH,eAAe,CAAC;EAC5C;EACA,OAAOP,WAAW,CAACI,MAAM,CAAC,CAACG,eAAe,CAAC;AAC/C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
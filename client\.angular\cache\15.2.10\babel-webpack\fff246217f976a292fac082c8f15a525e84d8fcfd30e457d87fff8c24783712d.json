{"ast": null, "code": "import baseExtremum from './_baseExtremum.js';\nimport baseGt from './_baseGt.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * This method is like `_.max` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.maxBy(objects, function(o) { return o.n; });\n * // => { 'n': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.maxBy(objects, 'n');\n * // => { 'n': 2 }\n */\nfunction maxBy(array, iteratee) {\n  return array && array.length ? baseExtremum(array, baseIteratee(iteratee, 2), baseGt) : undefined;\n}\nexport default maxBy;", "map": {"version": 3, "names": ["baseExtremum", "baseGt", "baseIteratee", "maxBy", "array", "iteratee", "length", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/maxBy.js"], "sourcesContent": ["import baseExtremum from './_baseExtremum.js';\nimport baseGt from './_baseGt.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * This method is like `_.max` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.maxBy(objects, function(o) { return o.n; });\n * // => { 'n': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.maxBy(objects, 'n');\n * // => { 'n': 2 }\n */\nfunction maxBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseGt)\n    : undefined;\n}\n\nexport default maxBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC9B,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBN,YAAY,CAACI,KAAK,EAAEF,YAAY,CAACG,QAAQ,EAAE,CAAC,CAAC,EAAEJ,MAAM,CAAC,GACtDM,SAAS;AACf;AAEA,eAAeJ,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
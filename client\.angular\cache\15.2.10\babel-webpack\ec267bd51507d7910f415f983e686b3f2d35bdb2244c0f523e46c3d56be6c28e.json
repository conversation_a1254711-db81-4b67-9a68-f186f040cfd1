{"ast": null, "code": "/***************************************************************************************************\r\n * Load `$localize` onto the global scope - used if i18n tags appear in Angular templates.\r\n */\nimport '@angular/localize/init';\n/**\r\n * This file includes polyfills needed by Angular and is loaded before the app.\r\n * You can add your own extra polyfills to this file.\r\n *\r\n * This file is divided into 2 sections:\r\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\r\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\r\n *      file.\r\n *\r\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\r\n * automatically update themselves. This includes Safari >= 10, Chrome >= 55 (including Opera),\r\n * Edge >= 13 on the desktop, and iOS 10 and Chrome on mobile.\r\n *\r\n * Learn more in https://angular.io/guide/browser-support\r\n */\n/***************************************************************************************************\r\n * BROWSER POLYFILLS\r\n */\n/** IE9, IE10, IE11, and Chrome <55 requires all of the following polyfills.\r\n *  This also includes Android Emulators with older versions of Chrome and Google Search/Googlebot\r\n */\n// import 'core-js/es6/symbol';\n// import 'core-js/es6/object';\n// import 'core-js/es6/function';\n// import 'core-js/es6/parse-int';\n// import 'core-js/es6/parse-float';\n// import 'core-js/es6/number';\n// import 'core-js/es6/math';\n// import 'core-js/es6/string';\n// import 'core-js/es6/date';\n// import 'core-js/es6/array';\n// import 'core-js/es6/regexp';\n// import 'core-js/es6/map';\n// import 'core-js/es6/weak-map';\n// import 'core-js/es6/set';\n/** IE10 and IE11 requires the following for the Reflect API. */\n// import 'core-js/es6/reflect';\n/**\r\n * By default, zone.js will patch all possible macroTask and DomEvents\r\n * user can disable parts of macroTask/DomEvents patch by setting following flags\r\n * because those flags need to be set before `zone.js` being loaded, and webpack\r\n * will put import in the top of bundle, so user need to create a separate file\r\n * in this directory (for example: zone-flags.ts), and put the following flags\r\n * into that file, and then add the following code before importing zone.js.\r\n * import './zone-flags.ts';\r\n *\r\n * The flags allowed in zone-flags.ts are listed here.\r\n *\r\n * The following flags will work for all browsers.\r\n *\r\n * (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame\r\n * (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick\r\n * (window as any).__zone_symbol__BLACK_LISTED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\r\n *\r\n *  in IE/Edge developer tools, the addEventListener will also be wrapped by zone.js\r\n *  with the following flag, it will bypass `zone.js` patch for IE/Edge\r\n *\r\n *  (window as any).__Zone_enable_cross_context_check = true;\r\n *\r\n */\n/***************************************************************************************************\r\n * Zone JS is required by default for Angular itself.\r\n */\nimport 'zone.js'; // Included with Angular CLI.\n/***************************************************************************************************\r\n * APPLICATION IMPORTS\r\n */\n// ng2-dragula\nwindow.global = window;", "map": {"version": 3, "mappings": "AAAA;;;AAGA,OAAO,wBAAwB;AAC/B;;;;;;;;;;;;;;;AAgBA;;;AAIA;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;AAwBA;;;AAGA,OAAO,SAAS,CAAC,CAAC;AAElB;;;AAIA;AAECA,MAAc,CAACC,MAAM,GAAGD,MAAM", "names": ["window", "global"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\polyfills.ts"], "sourcesContent": ["/***************************************************************************************************\r\n * Load `$localize` onto the global scope - used if i18n tags appear in Angular templates.\r\n */\r\nimport '@angular/localize/init';\r\n/**\r\n * This file includes polyfills needed by Angular and is loaded before the app.\r\n * You can add your own extra polyfills to this file.\r\n *\r\n * This file is divided into 2 sections:\r\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\r\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\r\n *      file.\r\n *\r\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\r\n * automatically update themselves. This includes Safari >= 10, Chrome >= 55 (including Opera),\r\n * Edge >= 13 on the desktop, and iOS 10 and Chrome on mobile.\r\n *\r\n * Learn more in https://angular.io/guide/browser-support\r\n */\r\n\r\n/***************************************************************************************************\r\n * BROWSER POLYFILLS\r\n */\r\n\r\n/** IE9, IE10, IE11, and Chrome <55 requires all of the following polyfills.\r\n *  This also includes Android Emulators with older versions of Chrome and Google Search/Googlebot\r\n */\r\n\r\n// import 'core-js/es6/symbol';\r\n// import 'core-js/es6/object';\r\n// import 'core-js/es6/function';\r\n// import 'core-js/es6/parse-int';\r\n// import 'core-js/es6/parse-float';\r\n// import 'core-js/es6/number';\r\n// import 'core-js/es6/math';\r\n// import 'core-js/es6/string';\r\n// import 'core-js/es6/date';\r\n// import 'core-js/es6/array';\r\n// import 'core-js/es6/regexp';\r\n// import 'core-js/es6/map';\r\n// import 'core-js/es6/weak-map';\r\n// import 'core-js/es6/set';\r\n\r\n/** IE10 and IE11 requires the following for the Reflect API. */\r\n// import 'core-js/es6/reflect';\r\n\r\n/**\r\n * By default, zone.js will patch all possible macroTask and DomEvents\r\n * user can disable parts of macroTask/DomEvents patch by setting following flags\r\n * because those flags need to be set before `zone.js` being loaded, and webpack\r\n * will put import in the top of bundle, so user need to create a separate file\r\n * in this directory (for example: zone-flags.ts), and put the following flags\r\n * into that file, and then add the following code before importing zone.js.\r\n * import './zone-flags.ts';\r\n *\r\n * The flags allowed in zone-flags.ts are listed here.\r\n *\r\n * The following flags will work for all browsers.\r\n *\r\n * (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame\r\n * (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick\r\n * (window as any).__zone_symbol__BLACK_LISTED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\r\n *\r\n *  in IE/Edge developer tools, the addEventListener will also be wrapped by zone.js\r\n *  with the following flag, it will bypass `zone.js` patch for IE/Edge\r\n *\r\n *  (window as any).__Zone_enable_cross_context_check = true;\r\n *\r\n */\r\n\r\n/***************************************************************************************************\r\n * Zone JS is required by default for Angular itself.\r\n */\r\nimport 'zone.js'; // Included with Angular CLI.\r\n\r\n/***************************************************************************************************\r\n * APPLICATION IMPORTS\r\n */\r\n\r\n// ng2-dragula\r\n\r\n(window as any).global = window;\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
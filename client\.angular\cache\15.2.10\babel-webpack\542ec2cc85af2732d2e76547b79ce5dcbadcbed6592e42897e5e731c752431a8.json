{"ast": null, "code": "import baseFindKey from './_baseFindKey.js';\nimport baseForOwnRight from './_baseForOwnRight.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * This method is like `_.findKey` except that it iterates over elements of\n * a collection in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {string|undefined} Returns the key of the matched element,\n *  else `undefined`.\n * @example\n *\n * var users = {\n *   'barney':  { 'age': 36, 'active': true },\n *   'fred':    { 'age': 40, 'active': false },\n *   'pebbles': { 'age': 1,  'active': true }\n * };\n *\n * _.findLastKey(users, function(o) { return o.age < 40; });\n * // => returns 'pebbles' assuming `_.findKey` returns 'barney'\n *\n * // The `_.matches` iteratee shorthand.\n * _.findLastKey(users, { 'age': 36, 'active': true });\n * // => 'barney'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findLastKey(users, ['active', false]);\n * // => 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.findLastKey(users, 'active');\n * // => 'pebbles'\n */\nfunction findLastKey(object, predicate) {\n  return baseFindKey(object, baseIteratee(predicate, 3), baseForOwnRight);\n}\nexport default findLastKey;", "map": {"version": 3, "names": ["baseFindKey", "baseForOwnRight", "baseIteratee", "findLastKey", "object", "predicate"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/findLastKey.js"], "sourcesContent": ["import baseFindKey from './_baseFindKey.js';\nimport baseForOwnRight from './_baseForOwnRight.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * This method is like `_.findKey` except that it iterates over elements of\n * a collection in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {string|undefined} Returns the key of the matched element,\n *  else `undefined`.\n * @example\n *\n * var users = {\n *   'barney':  { 'age': 36, 'active': true },\n *   'fred':    { 'age': 40, 'active': false },\n *   'pebbles': { 'age': 1,  'active': true }\n * };\n *\n * _.findLastKey(users, function(o) { return o.age < 40; });\n * // => returns 'pebbles' assuming `_.findKey` returns 'barney'\n *\n * // The `_.matches` iteratee shorthand.\n * _.findLastKey(users, { 'age': 36, 'active': true });\n * // => 'barney'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findLastKey(users, ['active', false]);\n * // => 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.findLastKey(users, 'active');\n * // => 'pebbles'\n */\nfunction findLastKey(object, predicate) {\n  return baseFindKey(object, baseIteratee(predicate, 3), baseForOwnRight);\n}\n\nexport default findLastKey;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACtC,OAAOL,WAAW,CAACI,MAAM,EAAEF,YAAY,CAACG,SAAS,EAAE,CAAC,CAAC,EAAEJ,eAAe,CAAC;AACzE;AAEA,eAAeE,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import Swal from 'sweetalert2';\nimport { AppConfig } from './../../../app-config';\nimport * as _ from 'lodash';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { NavigationEnd } from '@angular/router';\nimport { menu } from './../../../menu/menu';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"app/services/user.service\";\nimport * as i3 from \"@core/services/config.service\";\nimport * as i4 from \"@core/services/media.service\";\nimport * as i5 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i6 from \"@angular/flex-layout\";\nimport * as i7 from \"@ngx-translate/core\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"@angular/router\";\nimport * as i10 from \"app/services/loading.service\";\nimport * as i11 from \"@angular/platform-browser\";\nimport * as i12 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i13 from \"@angular/common\";\nimport * as i14 from \"@angular/flex-layout/extended\";\nimport * as i15 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i16 from \"../../../hostlisteners/back-button.directive\";\nimport * as i17 from \"app/layout/components/navbar/navbar-notification/navbar-notification.component\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nfunction NavbarComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"ul\", 6)(2, \"li\", 7)(3, \"a\", 8)(4, \"span\", 9);\n    i0.ɵɵelement(5, \"img\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r0.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.coreConfig.app.appName);\n  }\n}\nfunction NavbarComponent_div_2_ul_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 17)(1, \"li\", 7)(2, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_2_ul_1_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleSidebar(\"menu\"));\n    });\n    i0.ɵɵelement(3, \"span\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"ficon\");\n    i0.ɵɵproperty(\"data-feather\", \"menu\");\n  }\n}\nfunction NavbarComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, NavbarComponent_div_2_ul_1_Template, 4, 3, \"ul\", 13);\n    i0.ɵɵelementStart(2, \"li\", 14)(3, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_2_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.toggleDarkSkin());\n    });\n    i0.ɵɵelement(4, \"span\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.coreConfig.layout.menu.hidden);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.currentSkin === \"dark\" ? \"icon-sun\" : \"icon-moon\");\n  }\n}\nfunction NavbarComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"button\", 21);\n    i0.ɵɵelement(2, \"i\", 22);\n    i0.ɵɵelementStart(3, \"span\", 23);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 2, \"Back\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, ctx_r2.titleService.getTitle()), \" \");\n  }\n}\nfunction NavbarComponent_ul_4_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ul_4_a_7_Template_a_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const lang_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.setLanguage(lang_r12));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lang_r12 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.languageOptions[lang_r12] == null ? null : ctx_r9.languageOptions[lang_r12].title, \" \");\n  }\n}\nconst _c1 = function () {\n  return [\"/profile\"];\n};\nfunction NavbarComponent_ul_4_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 35)(1, \"a\", 36)(2, \"div\", 37)(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"span\", 40);\n    i0.ɵɵelement(9, \"img\", 41)(10, \"span\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 43)(12, \"a\", 44);\n    i0.ɵɵelement(13, \"span\", 45);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"div\", 46);\n    i0.ɵɵelementStart(17, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ul_4_li_9_Template_a_click_17_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.logout());\n    });\n    i0.ɵɵelement(18, \"span\", 45);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r10.name_settings == null ? null : ctx_r10.name_settings.is_on) == 1 ? (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.first_name) + \" \" + (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.last_name) : (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.last_name) + \" \" + (ctx_r10.currentUser == null ? null : ctx_r10.currentUser.first_name), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 8, ctx_r10.currentUser == null ? null : ctx_r10.currentUser.role == null ? null : ctx_r10.currentUser.role.name));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r10.coreConfig == null ? null : ctx_r10.coreConfig.app == null ? null : ctx_r10.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"data-feather\", \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 10, \"Account\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data-feather\", \"power\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 12, \"Logout\"), \" \");\n  }\n}\nfunction NavbarComponent_ul_4_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 7)(1, \"a\", 47);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ul_4_li_10_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.onClickLogin());\n    });\n    i0.ɵɵelementStart(2, \"span\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"Login\"));\n  }\n}\nfunction NavbarComponent_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 25)(1, \"li\", 26)(2, \"a\", 27);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementStart(4, \"span\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 30);\n    i0.ɵɵtemplate(7, NavbarComponent_ul_4_a_7_Template, 2, 1, \"a\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"app-navbar-notification\");\n    i0.ɵɵtemplate(9, NavbarComponent_ul_4_li_9_Template, 21, 15, \"li\", 32);\n    i0.ɵɵtemplate(10, NavbarComponent_ul_4_li_10_Template, 5, 3, \"li\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.languageOptions[ctx_r3._translateService.currentLang] == null ? null : ctx_r3.languageOptions[ctx_r3._translateService.currentLang].title, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3._translateService.getLangs());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.currentUser == null ? null : ctx_r3.currentUser.role == null ? null : ctx_r3.currentUser.role.id) != 7);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.currentUser == null ? null : ctx_r3.currentUser.role == null ? null : ctx_r3.currentUser.role.id) == 7);\n  }\n}\nexport class NavbarComponent {\n  // Add .navbar-static-style-on-scroll on scroll using HostListener & HostBinding\n  onWindowScroll() {\n    if ((window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop > 100) && this.coreConfig.layout.navbar.type == 'navbar-static-top' && this.coreConfig.layout.type == 'horizontal') {\n      this.windowScrolled = true;\n    } else if (this.windowScrolled && window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop < 10) {\n      this.windowScrolled = false;\n    }\n  }\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {Router} _router\r\n   * @param {AuthenticationService} _authService\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {CoreMediaService} _coreMediaService\r\n   * @param {MediaObserver} _mediaObserver\r\n   * @param {TranslateService} _translateService\r\n   */\n  constructor(_authService, _userService, _coreConfigService, _coreMediaService, _coreSidebarService, _mediaObserver, _translateService, _toastrService, _router, _loadingService, titleService) {\n    this._authService = _authService;\n    this._userService = _userService;\n    this._coreConfigService = _coreConfigService;\n    this._coreMediaService = _coreMediaService;\n    this._coreSidebarService = _coreSidebarService;\n    this._mediaObserver = _mediaObserver;\n    this._translateService = _translateService;\n    this._toastrService = _toastrService;\n    this._router = _router;\n    this._loadingService = _loadingService;\n    this.titleService = titleService;\n    this.isFixed = false;\n    this.windowScrolled = false;\n    this.appConfig = AppConfig;\n    this.languageOptions = [];\n    this.menu = menu;\n    this.menu_urls = [];\n    this.isChildPage = false;\n    this.isHome = true;\n    this.ignoreURLList = ['auth/login', 'auth/register', 'auth/forgot-password', 'auth/reset-password', 'auth/verify-email', 'streaming/broadcast', 'streaming/watch', 'streaming/setup'];\n    this.menu_urls = this.getAllUrl(this.menu);\n    _router.events.subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        this.onRouteChange();\n      }\n    });\n    this._authService.currentUser.subscribe(x => this.currentUser = x);\n    AppConfig.LANGUAGES.forEach(language => {\n      this.languageOptions[language.code] = {\n        title: language.name,\n        flag: language.flag\n      };\n    });\n    // Set the private defaults\n    this._unsubscribeAll = new Subject();\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\n  }\n  // Public Methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Toggle sidebar open\r\n   *\r\n   * @param key\r\n   */\n  toggleSidebar(key) {\n    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();\n  }\n  /**\r\n   * Set the language\r\n   *\r\n   * @param language\r\n   */\n  setLanguage(language) {\n    // Set the selected language for the navbar on change\n    this.selectedLanguage = language;\n    // Use the selected language id for translations\n    this._translateService.use(language);\n    this._coreConfigService.setConfig({\n      app: {\n        appLanguage: language\n      }\n    }, {\n      emitEvent: true\n    });\n    if (this._authService.currentUserValue != null) {\n      this._authService.updateUserLanguage(language).pipe(takeUntil(this._unsubscribeAll)).subscribe(data => {\n        this._toastrService.success(this._translateService.instant('Language changed successfully'));\n        // setTimeout(() => {\n        //   // reload the page\n        //   window.location.reload();\n        // }, 2000);\n      }, error => {\n        Swal.fire({\n          title: 'Error!',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK'),\n          confirmButtonColor: '#ed1c24'\n        });\n      });\n    }\n  }\n  /**\r\n   * Toggle Dark Skin\r\n   */\n  toggleDarkSkin() {\n    // Get the current skin\n    this._coreConfigService.getConfig().pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.currentSkin = config.layout.skin;\n    });\n    // Toggle Dark skin with prevSkin skin\n    this.prevSkin = localStorage.getItem('prevSkin');\n    if (this.currentSkin === 'dark') {\n      this._coreConfigService.setConfig({\n        layout: {\n          skin: this.prevSkin ? this.prevSkin : 'default'\n        }\n      }, {\n        emitEvent: true\n      });\n    } else {\n      localStorage.setItem('prevSkin', this.currentSkin);\n      this._coreConfigService.setConfig({\n        layout: {\n          skin: 'dark'\n        }\n      }, {\n        emitEvent: true\n      });\n    }\n  }\n  resetData() {\n    // are you sure\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: `<p class=\"text-center\">` + this._translateService.instant('This will reset all your data and you will be logged out') + `</p>\n      <p class=\"text-center\">\n      ` + this._translateService.instant('This action cannot be undone') + `</p>\n      `,\n      reverseButtons: true,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      confirmButtonColor: '#ed1c24',\n      cancelButtonColor: '#999999'\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._loadingService.show();\n        this._userService.resetData().subscribe(data => {\n          this._toastrService.success(data.message, 'Success!', {\n            toastClass: 'toast ngx-toastr',\n            closeButton: true\n          });\n          this._authService.logoutNotRequest();\n        });\n      }\n    });\n  }\n  /**\r\n   * Logout method\r\n   */\n  logout() {\n    this._authService.logout();\n    //  this._router.navigate([\"/auth/login\"]);\n  }\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    const userString = localStorage.getItem('EZLEAGUE_USER');\n    if (userString) {\n      this.currentUser = JSON.parse(userString);\n    }\n    console.log('currentUser', this.currentUser);\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.coreConfig = config;\n      this.horizontalMenu = config.layout.type === 'horizontal';\n      this.hiddenMenu = config.layout.menu.hidden === true;\n      this.currentSkin = config.layout.skin;\n      if (this.coreConfig.layout.type === 'vertical') {\n        setTimeout(() => {\n          if (this.coreConfig.layout.navbar.type === 'fixed-top') {\n            this.isFixed = true;\n          }\n        }, 0);\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // Horizontal Layout Only: Add class fixed-top to navbar below large screen\n    if (this.coreConfig?.layout.type === 'horizontal') {\n      // On every media(screen) change\n      this._coreMediaService.onMediaUpdate.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {\n        const isFixedTop = this._mediaObserver.isActive('bs-gt-xl');\n        this.isFixed = !isFixedTop;\n      });\n    }\n    // Set the selected language from default languageOptions\n    this.selectedLanguage = _.find(this.languageOptions, {\n      id: this._translateService.currentLang\n    });\n  }\n  get role_id() {\n    return this.currentUser?.role?.id;\n  }\n  /**\r\n   * On destroy\r\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  onClickLogin() {\n    const user = JSON.parse(localStorage.getItem('EZLEAGUE_USER'));\n    if (user) {\n      this._authService.logoutAsGuest(user);\n    }\n  }\n  // get all url in menu\n  getAllUrl(menu) {\n    let urls = [];\n    menu.forEach(element => {\n      if (element.url) {\n        urls.push(element.url);\n      }\n      if (element.children) {\n        let childUrls = this.getAllUrl(element.children);\n        urls = [...urls, ...childUrls];\n      }\n    });\n    return urls;\n  }\n  // on route change\n  onRouteChange() {\n    let url = this._router.url.slice(1);\n    // strub to first ?\n    let index = url.indexOf('?');\n    if (index > -1) {\n      url = url.substring(0, index);\n    }\n    console.log('router', url);\n    this.isHome = url === 'home';\n    // if (url.includes('no_layout')) {\n    //   this._coreConfigService.config = {\n    //     layout: {\n    //       type: 'none',\n    //     },\n    //   };\n    //   return;\n    // }\n    // if url is not in menu then set isChildPage to true\n    if (!this.menu_urls.includes(url)) {\n      this.isChildPage = true;\n      let check = false;\n      this.ignoreURLList.forEach(element => {\n        if (url.includes(element)) {\n          check = true;\n        }\n      });\n      if (check) {\n        this.isChildPage = false;\n        console.log('ignore url', url);\n      } else {\n        this._coreConfigService.config = {\n          layout: {\n            navbar: {\n              hidden: false,\n              type: 'fixed-top'\n            },\n            menu: {\n              hidden: false\n            },\n            footer: {\n              hidden: true\n            },\n            customizer: false,\n            enableLocalStorage: true\n          },\n          type: 'vertical'\n        };\n      }\n    } else {\n      this.isChildPage = false;\n      this._coreConfigService.config = {\n        layout: {\n          navbar: {\n            hidden: false,\n            type: 'fixed-top'\n          },\n          menu: {\n            hidden: false\n          },\n          footer: {\n            hidden: true\n          },\n          customizer: false,\n          enableLocalStorage: true,\n          type: 'vertical'\n        }\n      };\n    }\n  }\n  static #_ = this.ɵfac = function NavbarComponent_Factory(t) {\n    return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.CoreConfigService), i0.ɵɵdirectiveInject(i4.CoreMediaService), i0.ɵɵdirectiveInject(i5.CoreSidebarService), i0.ɵɵdirectiveInject(i6.MediaObserver), i0.ɵɵdirectiveInject(i7.TranslateService), i0.ɵɵdirectiveInject(i8.ToastrService), i0.ɵɵdirectiveInject(i9.Router), i0.ɵɵdirectiveInject(i10.LoadingService), i0.ɵɵdirectiveInject(i11.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NavbarComponent,\n    selectors: [[\"app-navbar\"]],\n    hostVars: 4,\n    hostBindings: function NavbarComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function NavbarComponent_scroll_HostBindingHandler() {\n          return ctx.onWindowScroll();\n        }, false, i0.ɵɵresolveWindow);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"fixed-top\", ctx.isFixed)(\"navbar-static-style-on-scroll\", ctx.windowScrolled);\n      }\n    },\n    decls: 5,\n    vars: 4,\n    consts: [[\"class\", \"navbar-header d-xl-block d-none\", 4, \"ngIf\"], [1, \"navbar-container\", \"d-flex\", \"content\"], [\"class\", \"bookmark-wrapper d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"nav navbar-nav align-items-center ml-auto\", 4, \"ngIf\"], [1, \"navbar-header\", \"d-xl-block\", \"d-none\"], [1, \"nav\", \"navbar-nav\", \"flex-row\"], [1, \"nav-item\"], [1, \"navbar-brand\", 3, \"routerLink\"], [1, \"brand-logo\"], [\"alt\", \"brand-logo\", \"width\", \"36\", 3, \"src\"], [1, \"brand-text\", \"mb-0\"], [1, \"bookmark-wrapper\", \"d-flex\", \"align-items-center\"], [\"class\", \"nav navbar-nav d-xl-none\", 4, \"ngIf\"], [1, \"nav-item\", \"d-none\"], [\"type\", \"button\", 1, \"nav-link\", \"nav-link-style\", \"btn\", 3, \"click\"], [1, \"ficon\", \"font-medium-5\", \"feather\", 3, \"ngClass\"], [1, \"nav\", \"navbar-nav\", \"d-xl-none\"], [1, \"nav-link\", \"menu-toggle\", 3, \"click\"], [3, \"data-feather\"], [1, \"d-flex\", \"align-items-center\"], [\"backButton\", \"\", 1, \"btn\", \"btn-icon\", \"btn-flat-black\"], [1, \"fa-solid\", \"fa-chevron-left\", \"fa-xl\"], [1, \"d-md-inline\", \"d-none\", 2, \"font-size\", \"1.26rem\"], [1, \"d-md-none\", \"d-inline\", 2, \"font-size\", \"1.26rem\"], [1, \"nav\", \"navbar-nav\", \"align-items-center\", \"ml-auto\"], [\"ngbDropdown\", \"\", 1, \"nav-item\", \"dropdown\", \"dropdown-language\"], [\"id\", \"dropdown-flag\", \"ngbDropdownToggle\", \"\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-translate\"], [1, \"selected-language\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdown-flag\"], [\"ngbDropdownItem\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"ngbDropdown\", \"\", \"class\", \"nav-item dropdown-user\", 4, \"ngIf\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"ngbDropdown\", \"\", 1, \"nav-item\", \"dropdown-user\"], [\"id\", \"dropdown-user\", \"ngbDropdownToggle\", \"\", \"id\", \"navbarUserDropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\", \"dropdown-user-link\"], [1, \"user-nav\", \"d-sm-flex\", \"d-none\"], [1, \"user-name\", \"font-weight-bolder\"], [1, \"user-status\"], [1, \"avatar\"], [\"alt\", \"avatar\", \"height\", \"40\", \"width\", \"40\", 1, \"round\", 3, \"src\"], [1, \"avatar-status-online\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"navbarUserDropdown\", 1, \"dropdown-menu\", \"dropdown-menu-right\"], [\"ngbDropdownItem\", \"\", 3, \"routerLink\"], [1, \"mr-50\", 3, \"data-feather\"], [1, \"dropdown-divider\"], [1, \"nav-link\", 3, \"click\"], [1, \"nav-link-text\", \"text-primary\"]],\n    template: function NavbarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NavbarComponent_div_0_Template, 8, 4, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, NavbarComponent_div_2_Template, 5, 2, \"div\", 2);\n        i0.ɵɵtemplate(3, NavbarComponent_div_3_Template, 9, 6, \"div\", 3);\n        i0.ɵɵtemplate(4, NavbarComponent_ul_4_Template, 11, 4, \"ul\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.horizontalMenu);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isChildPage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isChildPage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isHome && !ctx.isChildPage);\n      }\n    },\n    dependencies: [i9.RouterLink, i12.NgbDropdown, i12.NgbDropdownToggle, i12.NgbDropdownMenu, i12.NgbDropdownItem, i13.NgClass, i13.NgForOf, i13.NgIf, i14.DefaultClassDirective, i15.FeatherIconDirective, i16.BackButtonDirective, i17.NavbarNotificationComponent, i7.TranslatePipe],\n    styles: [\"app-navbar {\\n  top: env(safe-area-inset-top, 20px);\\n}\\n\\n.dropdown-menu-right {\\n  right: 0 !important;\\n  left: auto !important;\\n}\\n\\n.touchspin-cart .touchspin-wrapper {\\n  width: 6.4rem;\\n}\\n.touchspin-cart:focus-within {\\n  box-shadow: none !important;\\n}\\n\\napp-navbar-bookmark {\\n  display: flex;\\n}\\n\\n.navbar-static-style-on-scroll {\\n  background-color: #fff !important;\\n  box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 20px 0px !important;\\n}\\n\\n.dark-layout .navbar-container .search-input .search-list li.auto-suggestion:hover {\\n  background-color: #161d31;\\n}\\n\\n.btn-h:hover {\\n  background: blue;\\n  color: #ffffff;\\n  border-color: blue;\\n  cursor: pointer;\\n}\\n\\n@media (hover: hover) {\\n  .btn-flat-black:hover {\\n    background: rgba(75, 75, 75, 0.12);\\n    cursor: pointer;\\n  }\\n}\\n@media (hover: none) {\\n  .btn-flat-black:active {\\n    background: rgba(75, 75, 75, 0.12);\\n    cursor: pointer;\\n  }\\n}\\n.attachment {\\n  width: 150px;\\n  height: 100px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,OAAOA,IAAI,MAAM,aAAa;AAC9B,SAASC,SAAS,QAAQ,uBAAuB;AAWjD,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAC3B,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAS1C,SAASC,aAAa,QAAgB,iBAAiB;AAGvD,SAASC,IAAI,QAAQ,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;IC3B3CC,EAAA,CAAAC,cAAA,aAAoE;IAM1DD,EAAA,CAAAE,SAAA,cAA2E;IAC7EF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAI,MAAA,GAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IAJvCH,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAoB;IAEnCR,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAS,qBAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,CAAAC,YAAA,EAAAb,EAAA,CAAAc,aAAA,CAAuC;IAElBd,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAe,iBAAA,CAAAL,MAAA,CAAAC,UAAA,CAAAC,GAAA,CAAAI,OAAA,CAA4B;;;;;;IAU5DhB,EAAA,CAAAC,cAAA,aAA6E;IAEzCD,EAAA,CAAAiB,UAAA,mBAAAC,uDAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAc,MAAM,CAAC;IAAA,EAAC;IAC7DxB,EAAA,CAAAE,SAAA,eAAuD;IACzDF,EAAA,CAAAG,YAAA,EAAI;;;IAD4BH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAyB,UAAA,SAAiB;IAAzCzB,EAAA,CAAAM,UAAA,wBAAuB;;;;;;IALrCN,EAAA,CAAAC,cAAA,cAA6E;IAE3ED,EAAA,CAAA0B,UAAA,IAAAC,mCAAA,iBAMK;IAIL3B,EAAA,CAAAC,cAAA,aAA4B;IAC2BD,EAAA,CAAAiB,UAAA,mBAAAW,kDAAA;MAAA5B,EAAA,CAAAmB,aAAA,CAAAU,GAAA;MAAA,MAAAC,MAAA,GAAA9B,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAO,MAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAC7E/B,EAAA,CAAAE,SAAA,eAA+G;IACjHF,EAAA,CAAAG,YAAA,EAAI;;;;IAbgCH,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,UAAA,UAAA0B,MAAA,CAAArB,UAAA,CAAAsB,MAAA,CAAAlC,IAAA,CAAAmC,MAAA,CAAoC;IAYhElC,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAM,UAAA,YAAA0B,MAAA,CAAAG,WAAA,uCAA6D;;;;;IAOzEnC,EAAA,CAAAC,cAAA,cAA2D;IAGvDD,EAAA,CAAAE,SAAA,YAA8C;IAE9CF,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAJLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoC,kBAAA,MAAApC,EAAA,CAAAqC,WAAA,oBACF;IAEErC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoC,kBAAA,MAAApC,EAAA,CAAAqC,WAAA,OAAAC,MAAA,CAAAC,YAAA,CAAAC,QAAA,SACF;;;;;;IAYExC,EAAA,CAAAC,cAAA,YAAiG;IAA5BD,EAAA,CAAAiB,UAAA,mBAAAwB,qDAAA;MAAA,MAAAC,WAAA,GAAA1C,EAAA,CAAAmB,aAAA,CAAAwB,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAuB,OAAA,CAAAC,WAAA,CAAAH,QAAA,CAAiB;IAAA,EAAC;IAC9F5C,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoC,kBAAA,MAAAY,MAAA,CAAAC,eAAA,CAAAL,QAAA,mBAAAI,MAAA,CAAAC,eAAA,CAAAL,QAAA,EAAAM,KAAA,MACF;;;;;;;;;IAUJlD,EAAA,CAAAC,cAAA,aAAkF;IAK1ED,EAAA,CAAAI,MAAA,GAGF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAI,MAAA,GAAyC;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE5EH,EAAA,CAAAC,cAAA,eAAqB;IACnBD,EAAA,CAAAE,SAAA,cAAmG;IAErGF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAoG;IAEhGD,EAAA,CAAAE,SAAA,gBAAmD;IACnDF,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,SAAA,eAAoC;IACpCF,EAAA,CAAAC,cAAA,aAAsC;IAAnBD,EAAA,CAAAiB,UAAA,mBAAAkC,uDAAA;MAAAnD,EAAA,CAAAmB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAA8B,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACnCtD,EAAA,CAAAE,SAAA,gBAAoD;IACpDF,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IApBAH,EAAA,CAAAK,SAAA,GAGF;IAHEL,EAAA,CAAAoC,kBAAA,OAAAmB,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAC,KAAA,UAAAF,OAAA,CAAAG,WAAA,kBAAAH,OAAA,CAAAG,WAAA,CAAAC,UAAA,WAAAJ,OAAA,CAAAG,WAAA,kBAAAH,OAAA,CAAAG,WAAA,CAAAE,SAAA,KAAAL,OAAA,CAAAG,WAAA,kBAAAH,OAAA,CAAAG,WAAA,CAAAE,SAAA,WAAAL,OAAA,CAAAG,WAAA,kBAAAH,OAAA,CAAAG,WAAA,CAAAC,UAAA,OAGF;IAC0B3D,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAqC,WAAA,OAAAkB,OAAA,CAAAG,WAAA,kBAAAH,OAAA,CAAAG,WAAA,CAAAG,IAAA,kBAAAN,OAAA,CAAAG,WAAA,CAAAG,IAAA,CAAAC,IAAA,EAAyC;IAGhD9D,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAS,qBAAA,QAAA8C,OAAA,CAAA5C,UAAA,kBAAA4C,OAAA,CAAA5C,UAAA,CAAAC,GAAA,kBAAA2C,OAAA,CAAA5C,UAAA,CAAAC,GAAA,CAAAC,YAAA,EAAAb,EAAA,CAAAc,aAAA,CAAyC;IAK3Cd,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,KAAAwD,GAAA,EAA2B;IACtC/D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,wBAAuB;IAC7BN,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoC,kBAAA,MAAApC,EAAA,CAAAqC,WAAA,yBACF;IAGQrC,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,UAAA,yBAAwB;IAC9BN,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoC,kBAAA,MAAApC,EAAA,CAAAqC,WAAA,wBACF;;;;;;IAGJrC,EAAA,CAAAC,cAAA,YAAwD;IAClCD,EAAA,CAAAiB,UAAA,mBAAA+C,uDAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAA2C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC1CnE,EAAA,CAAAC,cAAA,eAAyC;IAAAD,EAAA,CAAAI,MAAA,GAAyB;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;IAAhCH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAqC,WAAA,gBAAyB;;;;;IAlDxErC,EAAA,CAAAC,cAAA,aAAqF;IAI/ED,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAC,cAAA,eAAgC;IAACD,EAAA,CAAAI,MAAA,GAA2D;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErGH,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAA0B,UAAA,IAAA0C,iCAAA,gBAEI;IACNpE,EAAA,CAAAG,YAAA,EAAM;IAKRH,EAAA,CAAAE,SAAA,8BAAmD;IAInDF,EAAA,CAAA0B,UAAA,IAAA2C,kCAAA,mBA2BK;IACLrE,EAAA,CAAA0B,UAAA,KAAA4C,mCAAA,iBAIK;IAKPtE,EAAA,CAAAG,YAAA,EAAK;;;;IApDkCH,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAoC,kBAAA,MAAAmC,MAAA,CAAAtB,eAAA,CAAAsB,MAAA,CAAAC,iBAAA,CAAAC,WAAA,mBAAAF,MAAA,CAAAtB,eAAA,CAAAsB,MAAA,CAAAC,iBAAA,CAAAC,WAAA,EAAAvB,KAAA,KAA2D;IAGxElD,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,UAAA,YAAAiE,MAAA,CAAAC,iBAAA,CAAAE,QAAA,GAA+B;IAYP1E,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,UAAA,UAAAiE,MAAA,CAAAb,WAAA,kBAAAa,MAAA,CAAAb,WAAA,CAAAG,IAAA,kBAAAU,MAAA,CAAAb,WAAA,CAAAG,IAAA,CAAAc,EAAA,OAAgC;IA4B1D3E,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,UAAA,UAAAiE,MAAA,CAAAb,WAAA,kBAAAa,MAAA,CAAAb,WAAA,CAAAG,IAAA,kBAAAU,MAAA,CAAAb,WAAA,CAAAG,IAAA,CAAAc,EAAA,OAAgC;;;AD/D1D,OAAM,MAAOC,eAAe;EAM1B;EAEAC,cAAcA,CAAA;IACZ,IACE,CAACC,MAAM,CAACC,WAAW,IACjBC,QAAQ,CAACC,eAAe,CAACC,SAAS,IAClCF,QAAQ,CAACG,IAAI,CAACD,SAAS,GAAG,GAAG,KAC/B,IAAI,CAACvE,UAAU,CAACsB,MAAM,CAACmD,MAAM,CAACC,IAAI,IAAI,mBAAmB,IACzD,IAAI,CAAC1E,UAAU,CAACsB,MAAM,CAACoD,IAAI,IAAI,YAAY,EAC3C;MACA,IAAI,CAACC,cAAc,GAAG,IAAI;KAC3B,MAAM,IACJ,IAAI,CAACA,cAAc,IAAIR,MAAM,CAACC,WAAW,IAC1CC,QAAQ,CAACC,eAAe,CAACC,SAAS,IAClCF,QAAQ,CAACG,IAAI,CAACD,SAAS,GAAG,EAAE,EAC5B;MACA,IAAI,CAACI,cAAc,GAAG,KAAK;;EAE/B;EA8BA;;;;;;;;;;;EAWAC,YACUC,YAAyB,EACzBC,YAAyB,EACzBC,kBAAqC,EACrCC,iBAAmC,EACnCC,mBAAuC,EACvCC,cAA6B,EAC9BrB,iBAAmC,EAClCsB,cAA6B,EAC7BC,OAAe,EACfC,eAA+B,EAChCzD,YAAmB;IAVlB,KAAAiD,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAArB,iBAAiB,GAAjBA,iBAAiB;IAChB,KAAAsB,cAAc,GAAdA,cAAc;IACd,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAzD,YAAY,GAAZA,YAAY;IA1Ed,KAAA0D,OAAO,GAAG,KAAK;IAEf,KAAAX,cAAc,GAAG,KAAK;IA2BtB,KAAAY,SAAS,GAAGxG,SAAS;IAErB,KAAAuD,eAAe,GAAG,EAAE;IAG3B,KAAAlD,IAAI,GAAGA,IAAI;IACX,KAAAoG,SAAS,GAAG,EAAE;IACd,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,MAAM,GAAG,IAAI;IAEb,KAAAC,aAAa,GAAG,CACd,YAAY,EACZ,eAAe,EACf,sBAAsB,EACtB,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,EACrB,iBAAiB,EACjB,iBAAiB,CAClB;IA4BC,IAAI,CAACH,SAAS,GAAG,IAAI,CAACI,SAAS,CAAC,IAAI,CAACxG,IAAI,CAAC;IAC1CgG,OAAO,CAACS,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;MACjC,IAAIA,KAAK,YAAY5G,aAAa,EAAE;QAClC,IAAI,CAAC6G,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF,IAAI,CAACnB,YAAY,CAAC9B,WAAW,CAAC+C,SAAS,CAAEG,CAAC,IAAM,IAAI,CAAClD,WAAW,GAAGkD,CAAE,CAAC;IAEtElH,SAAS,CAACmH,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAI;MACvC,IAAI,CAAC9D,eAAe,CAAC8D,QAAQ,CAACC,IAAI,CAAC,GAAG;QACpC9D,KAAK,EAAE6D,QAAQ,CAACjD,IAAI;QACpBmD,IAAI,EAAEF,QAAQ,CAACE;OAChB;IACH,CAAC,CAAC;IACF;IACA,IAAI,CAACC,eAAe,GAAG,IAAItH,OAAO,EAAE;IACpC,IAAI,CAAC4D,aAAa,GAAG2D,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;EACxE;EAEA;EACA;EAEA;;;;;EAKA9F,aAAaA,CAAC+F,GAAG;IACf,IAAI,CAAC3B,mBAAmB,CAAC4B,kBAAkB,CAACD,GAAG,CAAC,CAACE,UAAU,EAAE;EAC/D;EAEA;;;;;EAKA1E,WAAWA,CAACgE,QAAQ;IAClB;IACA,IAAI,CAACW,gBAAgB,GAAGX,QAAQ;IAEhC;IACA,IAAI,CAACvC,iBAAiB,CAACmD,GAAG,CAACZ,QAAQ,CAAC;IAEpC,IAAI,CAACrB,kBAAkB,CAACkC,SAAS,CAC/B;MAAEhH,GAAG,EAAE;QAAEiH,WAAW,EAAEd;MAAQ;IAAE,CAAE,EAClC;MAAEe,SAAS,EAAE;IAAI,CAAE,CACpB;IAED,IAAI,IAAI,CAACtC,YAAY,CAACuC,gBAAgB,IAAI,IAAI,EAAE;MAC9C,IAAI,CAACvC,YAAY,CACdwC,kBAAkB,CAACjB,QAAQ,CAAC,CAC5BkB,IAAI,CAACpI,SAAS,CAAC,IAAI,CAACqH,eAAe,CAAC,CAAC,CACrCT,SAAS,CACPyB,IAAS,IAAI;QACZ,IAAI,CAACpC,cAAc,CAACqC,OAAO,CACzB,IAAI,CAAC3D,iBAAiB,CAAC4D,OAAO,CAAC,+BAA+B,CAAC,CAChE;QACD;QACA;QACA;QACA;MACF,CAAC,EACAC,KAAU,IAAI;QACb5I,IAAI,CAAC6I,IAAI,CAAC;UACRpF,KAAK,EAAE,QAAQ;UACfqF,IAAI,EAAEF,KAAK,CAACG,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAClE,iBAAiB,CAAC4D,OAAO,CAAC,IAAI,CAAC;UACvDO,kBAAkB,EAAE;SACrB,CAAC;MACJ,CAAC,CACF;;EAEP;EAEA;;;EAGA5G,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC2D,kBAAkB,CACpBkD,SAAS,EAAE,CACXX,IAAI,CAACpI,SAAS,CAAC,IAAI,CAACqH,eAAe,CAAC,CAAC,CACrCT,SAAS,CAAEoC,MAAM,IAAI;MACpB,IAAI,CAAC1G,WAAW,GAAG0G,MAAM,CAAC5G,MAAM,CAAC6G,IAAI;IACvC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACC,QAAQ,GAAG1B,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEhD,IAAI,IAAI,CAACnF,WAAW,KAAK,MAAM,EAAE;MAC/B,IAAI,CAACuD,kBAAkB,CAACkC,SAAS,CAC/B;QAAE3F,MAAM,EAAE;UAAE6G,IAAI,EAAE,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG;QAAS;MAAE,CAAE,EAC/D;QAAEjB,SAAS,EAAE;MAAI,CAAE,CACpB;KACF,MAAM;MACLT,YAAY,CAAC2B,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC7G,WAAW,CAAC;MAClD,IAAI,CAACuD,kBAAkB,CAACkC,SAAS,CAC/B;QAAE3F,MAAM,EAAE;UAAE6G,IAAI,EAAE;QAAM;MAAE,CAAE,EAC5B;QAAEhB,SAAS,EAAE;MAAI,CAAE,CACpB;;EAEL;EAEAmB,SAASA,CAAA;IACP;IACAxJ,IAAI,CAAC6I,IAAI,CAAC;MACRpF,KAAK,EAAE,IAAI,CAACsB,iBAAiB,CAAC4D,OAAO,CAAC,eAAe,CAAC;MACtDc,IAAI,EACF,yBAAyB,GACzB,IAAI,CAAC1E,iBAAiB,CAAC4D,OAAO,CAC5B,0DAA0D,CAC3D,GACD;;OAED,GACC,IAAI,CAAC5D,iBAAiB,CAAC4D,OAAO,CAAC,8BAA8B,CAAC,GAC9D;OACD;MACDe,cAAc,EAAE,IAAI;MACpBV,IAAI,EAAE,SAAS;MACfW,gBAAgB,EAAE,IAAI;MACtBV,iBAAiB,EAAE,IAAI,CAAClE,iBAAiB,CAAC4D,OAAO,CAAC,KAAK,CAAC;MACxDiB,gBAAgB,EAAE,IAAI,CAAC7E,iBAAiB,CAAC4D,OAAO,CAAC,IAAI,CAAC;MACtDO,kBAAkB,EAAE,SAAS;MAC7BW,iBAAiB,EAAE;KACpB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAACzD,eAAe,CAAC0D,IAAI,EAAE;QAC3B,IAAI,CAACjE,YAAY,CAACwD,SAAS,EAAE,CAACxC,SAAS,CAAEyB,IAAS,IAAI;UACpD,IAAI,CAACpC,cAAc,CAACqC,OAAO,CAACD,IAAI,CAACM,OAAO,EAAE,UAAU,EAAE;YACpDmB,UAAU,EAAE,kBAAkB;YAC9BC,WAAW,EAAE;WACd,CAAC;UAEF,IAAI,CAACpE,YAAY,CAACqE,gBAAgB,EAAE;QACtC,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA;;;EAGAvG,MAAMA,CAAA;IACJ,IAAI,CAACkC,YAAY,CAAClC,MAAM,EAAE;IAC1B;EACF;EAEA;EACA;EAEA;;;EAIAwG,QAAQA,CAAA;IACN,MAAMC,UAAU,GAAG1C,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,IAAIyC,UAAU,EAAE;MACd,IAAI,CAACrG,WAAW,GAAGyD,IAAI,CAACC,KAAK,CAAC2C,UAAU,CAAC;;IAE3CC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACvG,WAAW,CAAC;IAE5C,IAAI,CAACgC,kBAAkB,CAACmD,MAAM,CAC3BZ,IAAI,CAACpI,SAAS,CAAC,IAAI,CAACqH,eAAe,CAAC,CAAC,CACrCT,SAAS,CAAEoC,MAAM,IAAI;MACpB,IAAI,CAAClI,UAAU,GAAGkI,MAAM;MACxB,IAAI,CAACqB,cAAc,GAAGrB,MAAM,CAAC5G,MAAM,CAACoD,IAAI,KAAK,YAAY;MACzD,IAAI,CAAC8E,UAAU,GAAGtB,MAAM,CAAC5G,MAAM,CAAClC,IAAI,CAACmC,MAAM,KAAK,IAAI;MACpD,IAAI,CAACC,WAAW,GAAG0G,MAAM,CAAC5G,MAAM,CAAC6G,IAAI;MAErC,IAAI,IAAI,CAACnI,UAAU,CAACsB,MAAM,CAACoD,IAAI,KAAK,UAAU,EAAE;QAC9C+E,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACzJ,UAAU,CAACsB,MAAM,CAACmD,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;YACtD,IAAI,CAACY,OAAO,GAAG,IAAI;;QAEvB,CAAC,EAAE,CAAC,CAAC;;IAET,CAAC,CAAC;EACN;EAGAoE,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAAC1J,UAAU,EAAEsB,MAAM,CAACoD,IAAI,KAAK,YAAY,EAAE;MACjD;MACA,IAAI,CAACM,iBAAiB,CAAC2E,aAAa,CACjCrC,IAAI,CAACpI,SAAS,CAAC,IAAI,CAACqH,eAAe,CAAC,CAAC,CACrCT,SAAS,CAAC,MAAK;QACd,MAAM8D,UAAU,GAAG,IAAI,CAAC1E,cAAc,CAAC2E,QAAQ,CAAC,UAAU,CAAC;QAC3D,IAAI,CAACvE,OAAO,GAAG,CAACsE,UAAU;MAC5B,CAAC,CAAC;;IAGN;IACA,IAAI,CAAC7C,gBAAgB,GAAG/H,CAAC,CAAC8K,IAAI,CAAC,IAAI,CAACxH,eAAe,EAAE;MACnD0B,EAAE,EAAE,IAAI,CAACH,iBAAiB,CAACC;KAC5B,CAAC;EACJ;EAEA,IAAIiG,OAAOA,CAAA;IACT,OAAO,IAAI,CAAChH,WAAW,EAAEG,IAAI,EAAEc,EAAE;EACnC;EAEA;;;EAGAgG,WAAWA,CAAA;IACT;IACA,IAAI,CAACzD,eAAe,CAAC0D,IAAI,EAAE;IAC3B,IAAI,CAAC1D,eAAe,CAAC2D,QAAQ,EAAE;EACjC;EAEA1G,YAAYA,CAAA;IACV,MAAM2G,IAAI,GAAG3D,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;IAE9D,IAAIwD,IAAI,EAAE;MACR,IAAI,CAACtF,YAAY,CAACuF,aAAa,CAACD,IAAI,CAAC;;EAEzC;EACA;EACAvE,SAASA,CAACxG,IAAI;IACZ,IAAIiL,IAAI,GAAG,EAAE;IACbjL,IAAI,CAAC+G,OAAO,CAAEmE,OAAO,IAAI;MACvB,IAAIA,OAAO,CAACC,GAAG,EAAE;QACfF,IAAI,CAACG,IAAI,CAACF,OAAO,CAACC,GAAG,CAAC;;MAExB,IAAID,OAAO,CAACG,QAAQ,EAAE;QACpB,IAAIC,SAAS,GAAG,IAAI,CAAC9E,SAAS,CAAC0E,OAAO,CAACG,QAAQ,CAAC;QAChDJ,IAAI,GAAG,CAAC,GAAGA,IAAI,EAAE,GAAGK,SAAS,CAAC;;IAElC,CAAC,CAAC;IACF,OAAOL,IAAI;EACb;EACA;EACArE,aAAaA,CAAA;IACX,IAAIuE,GAAG,GAAG,IAAI,CAACnF,OAAO,CAACmF,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC;IACnC;IACA,IAAIC,KAAK,GAAGL,GAAG,CAACM,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAID,KAAK,GAAG,CAAC,CAAC,EAAE;MACdL,GAAG,GAAGA,GAAG,CAACO,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC;;IAE/BvB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEiB,GAAG,CAAC;IAE1B,IAAI,CAAC7E,MAAM,GAAI6E,GAAG,KAAK,MAAO;IAE9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC/E,SAAS,CAACuF,QAAQ,CAACR,GAAG,CAAC,EAAE;MACjC,IAAI,CAAC9E,WAAW,GAAG,IAAI;MACvB,IAAIuF,KAAK,GAAG,KAAK;MACjB,IAAI,CAACrF,aAAa,CAACQ,OAAO,CAAEmE,OAAO,IAAI;QACrC,IAAIC,GAAG,CAACQ,QAAQ,CAACT,OAAO,CAAC,EAAE;UACzBU,KAAK,GAAG,IAAI;;MAEhB,CAAC,CAAC;MACF,IAAIA,KAAK,EAAE;QACT,IAAI,CAACvF,WAAW,GAAG,KAAK;QACxB4D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiB,GAAG,CAAC;OAC/B,MAAM;QACL,IAAI,CAACxF,kBAAkB,CAACmD,MAAM,GAAG;UAC/B5G,MAAM,EAAE;YACNmD,MAAM,EAAE;cACNlD,MAAM,EAAE,KAAK;cACbmD,IAAI,EAAE;aACP;YACDtF,IAAI,EAAE;cACJmC,MAAM,EAAE;aACT;YACD0J,MAAM,EAAE;cACN1J,MAAM,EAAE;aACT;YACD2J,UAAU,EAAE,KAAK;YACjBC,kBAAkB,EAAE;WACrB;UACDzG,IAAI,EAAE;SACP;;KAEJ,MAAM;MACL,IAAI,CAACe,WAAW,GAAG,KAAK;MACxB,IAAI,CAACV,kBAAkB,CAACmD,MAAM,GAAG;QAC/B5G,MAAM,EAAE;UACNmD,MAAM,EAAE;YACNlD,MAAM,EAAE,KAAK;YACbmD,IAAI,EAAE;WACP;UACDtF,IAAI,EAAE;YACJmC,MAAM,EAAE;WACT;UACD0J,MAAM,EAAE;YACN1J,MAAM,EAAE;WACT;UACD2J,UAAU,EAAE,KAAK;UACjBC,kBAAkB,EAAE,IAAI;UACxBzG,IAAI,EAAE;;OAET;;EAEL;EAAC,QAAA1F,CAAA;qBAhYUiF,eAAe,EAAA5E,EAAA,CAAA+L,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjM,EAAA,CAAA+L,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnM,EAAA,CAAA+L,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAArM,EAAA,CAAA+L,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvM,EAAA,CAAA+L,iBAAA,CAAAS,EAAA,CAAAC,kBAAA,GAAAzM,EAAA,CAAA+L,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAA3M,EAAA,CAAA+L,iBAAA,CAAAa,EAAA,CAAAC,gBAAA,GAAA7M,EAAA,CAAA+L,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAA/M,EAAA,CAAA+L,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAAjN,EAAA,CAAA+L,iBAAA,CAAAmB,GAAA,CAAAC,cAAA,GAAAnN,EAAA,CAAA+L,iBAAA,CAAAqB,GAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAf1I,eAAe;IAAA2I,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAfC,GAAA,CAAA/I,cAAA,EAAgB;QAAA,UAAA7E,EAAA,CAAA6N,eAAA;;;;;;;;;;;QCpC7B7N,EAAA,CAAA0B,UAAA,IAAAoM,8BAAA,iBAaM;QAEN9N,EAAA,CAAAC,cAAA,aAA6C;QAC3CD,EAAA,CAAA0B,UAAA,IAAAqM,8BAAA,iBAkBM;QAGN/N,EAAA,CAAA0B,UAAA,IAAAsM,8BAAA,iBAYM;QAENhO,EAAA,CAAA0B,UAAA,IAAAuM,6BAAA,iBAyDK;QACPjO,EAAA,CAAAG,YAAA,EAAM;;;QA7GAH,EAAA,CAAAM,UAAA,SAAAsN,GAAA,CAAA1D,cAAA,CAAoB;QAgBiClK,EAAA,CAAAK,SAAA,GAAkB;QAAlBL,EAAA,CAAAM,UAAA,UAAAsN,GAAA,CAAAxH,WAAA,CAAkB;QAqBnCpG,EAAA,CAAAK,SAAA,GAAiB;QAAjBL,EAAA,CAAAM,UAAA,SAAAsN,GAAA,CAAAxH,WAAA,CAAiB;QAcFpG,EAAA,CAAAK,SAAA,GAA4B;QAA5BL,EAAA,CAAAM,UAAA,SAAAsN,GAAA,CAAAvH,MAAA,KAAAuH,GAAA,CAAAxH,WAAA,CAA4B", "names": ["<PERSON><PERSON>", "AppConfig", "_", "Subject", "takeUntil", "NavigationEnd", "menu", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵpropertyInterpolate", "ctx_r0", "coreConfig", "app", "appLogoImage", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "appName", "ɵɵlistener", "NavbarComponent_div_2_ul_1_Template_a_click_2_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "toggleSidebar", "ɵɵclassMap", "ɵɵtemplate", "NavbarComponent_div_2_ul_1_Template", "NavbarComponent_div_2_Template_a_click_3_listener", "_r8", "ctx_r7", "toggleDarkSkin", "ctx_r1", "layout", "hidden", "currentSkin", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r2", "titleService", "getTitle", "NavbarComponent_ul_4_a_7_Template_a_click_0_listener", "restoredCtx", "_r14", "lang_r12", "$implicit", "ctx_r13", "setLanguage", "ctx_r9", "languageOptions", "title", "NavbarComponent_ul_4_li_9_Template_a_click_17_listener", "_r16", "ctx_r15", "logout", "ctx_r10", "name_settings", "is_on", "currentUser", "first_name", "last_name", "role", "name", "_c1", "NavbarComponent_ul_4_li_10_Template_a_click_1_listener", "_r18", "ctx_r17", "onClickLogin", "NavbarComponent_ul_4_a_7_Template", "NavbarComponent_ul_4_li_9_Template", "NavbarComponent_ul_4_li_10_Template", "ctx_r3", "_translateService", "currentLang", "get<PERSON>angs", "id", "NavbarComponent", "onWindowScroll", "window", "pageYOffset", "document", "documentElement", "scrollTop", "body", "navbar", "type", "windowScrolled", "constructor", "_authService", "_userService", "_coreConfigService", "_coreMediaService", "_coreSidebarService", "_mediaObserver", "_toastrService", "_router", "_loadingService", "isFixed", "appConfig", "menu_urls", "isChildPage", "isHome", "ignoreURLList", "getAllUrl", "events", "subscribe", "event", "onRouteChange", "x", "LANGUAGES", "for<PERSON>ach", "language", "code", "flag", "_unsubscribeAll", "JSON", "parse", "localStorage", "getItem", "key", "getSidebarRegistry", "toggle<PERSON><PERSON>", "selectedLanguage", "use", "setConfig", "appLanguage", "emitEvent", "currentUserValue", "updateUserLanguage", "pipe", "data", "success", "instant", "error", "fire", "text", "message", "icon", "confirmButtonText", "confirmButtonColor", "getConfig", "config", "skin", "prev<PERSON><PERSON>", "setItem", "resetData", "html", "reverseButtons", "showCancelButton", "cancelButtonText", "cancelButtonColor", "then", "result", "isConfirmed", "show", "toastClass", "closeButton", "logoutNotRequest", "ngOnInit", "userString", "console", "log", "horizontalMenu", "hiddenMenu", "setTimeout", "ngAfterViewInit", "onMediaUpdate", "isFixedTop", "isActive", "find", "role_id", "ngOnDestroy", "next", "complete", "user", "logoutAsGuest", "urls", "element", "url", "push", "children", "childUrls", "slice", "index", "indexOf", "substring", "includes", "check", "footer", "customizer", "enableLocalStorage", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "CoreConfigService", "i4", "CoreMediaService", "i5", "CoreSidebarService", "i6", "MediaObserver", "i7", "TranslateService", "i8", "ToastrService", "i9", "Router", "i10", "LoadingService", "i11", "Title", "_2", "selectors", "hostVars", "hostBindings", "NavbarComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "NavbarComponent_div_0_Template", "NavbarComponent_div_2_Template", "NavbarComponent_div_3_Template", "NavbarComponent_ul_4_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import { ToastrService } from 'ngx-toastr';\r\nimport Swal from 'sweetalert2';\r\nimport { AppConfig } from './../../../app-config';\r\nimport {\r\n  Component,\r\n  OnDestroy,\r\n  OnInit,\r\n  HostBinding,\r\n  HostListener,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { MediaObserver } from '@angular/flex-layout';\r\n\r\nimport * as _ from 'lodash';\r\nimport { Subject } from 'rxjs';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { CoreMediaService } from '@core/services/media.service';\r\n\r\nimport { User } from 'app/interfaces/user';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { menu } from './../../../menu/menu';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-navbar',\r\n  templateUrl: './navbar.component.html',\r\n  styleUrls: ['./navbar.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class NavbarComponent implements OnInit, OnDestroy {\r\n  @HostBinding('class.fixed-top')\r\n  public isFixed = false;\r\n  @HostBinding('class.navbar-static-style-on-scroll')\r\n  public windowScrolled = false;\r\n  public name_settings : any;\r\n  // Add .navbar-static-style-on-scroll on scroll using HostListener & HostBinding\r\n  @HostListener('window:scroll', [])\r\n  onWindowScroll() {\r\n    if (\r\n      (window.pageYOffset ||\r\n        document.documentElement.scrollTop ||\r\n        document.body.scrollTop > 100) &&\r\n      this.coreConfig.layout.navbar.type == 'navbar-static-top' &&\r\n      this.coreConfig.layout.type == 'horizontal'\r\n    ) {\r\n      this.windowScrolled = true;\r\n    } else if (\r\n      (this.windowScrolled && window.pageYOffset) ||\r\n      document.documentElement.scrollTop ||\r\n      document.body.scrollTop < 10\r\n    ) {\r\n      this.windowScrolled = false;\r\n    }\r\n  }\r\n\r\n  public horizontalMenu: boolean;\r\n  public hiddenMenu: boolean;\r\n  public coreConfig: any;\r\n  public currentSkin: string;\r\n  public prevSkin: string;\r\n  public appConfig = AppConfig;\r\n  public currentUser: User;\r\n  public languageOptions = [];\r\n  public navigation: any;\r\n  public selectedLanguage: any;\r\n  menu = menu;\r\n  menu_urls = [];\r\n  isChildPage = false;\r\n  isHome = true;\r\n\r\n  ignoreURLList = [\r\n    'auth/login',\r\n    'auth/register',\r\n    'auth/forgot-password',\r\n    'auth/reset-password',\r\n    'auth/verify-email',\r\n    'streaming/broadcast',\r\n    'streaming/watch',\r\n    'streaming/setup',\r\n  ];\r\n  // Private\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {Router} _router\r\n   * @param {AuthenticationService} _authService\r\n   * @param {CoreConfigService} _coreConfigService\r\n   * @param {CoreSidebarService} _coreSidebarService\r\n   * @param {CoreMediaService} _coreMediaService\r\n   * @param {MediaObserver} _mediaObserver\r\n   * @param {TranslateService} _translateService\r\n   */\r\n  constructor(\r\n    private _authService: AuthService,\r\n    private _userService: UserService,\r\n    private _coreConfigService: CoreConfigService,\r\n    private _coreMediaService: CoreMediaService,\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _mediaObserver: MediaObserver,\r\n    public _translateService: TranslateService,\r\n    private _toastrService: ToastrService,\r\n    private _router: Router,\r\n    private _loadingService: LoadingService,\r\n    public titleService: Title\r\n  ) {\r\n    this.menu_urls = this.getAllUrl(this.menu);\r\n    _router.events.subscribe((event) => {\r\n      if (event instanceof NavigationEnd) {\r\n        this.onRouteChange();\r\n      }\r\n    });\r\n\r\n    this._authService.currentUser.subscribe((x) => (this.currentUser = x));\r\n\r\n    AppConfig.LANGUAGES.forEach((language) => {\r\n      this.languageOptions[language.code] = {\r\n        title: language.name,\r\n        flag: language.flag,\r\n      };\r\n    });\r\n    // Set the private defaults\r\n    this._unsubscribeAll = new Subject();\r\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n  }\r\n\r\n  // Public Methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Toggle sidebar open\r\n   *\r\n   * @param key\r\n   */\r\n  toggleSidebar(key): void {\r\n    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();\r\n  }\r\n\r\n  /**\r\n   * Set the language\r\n   *\r\n   * @param language\r\n   */\r\n  setLanguage(language): void {\r\n    // Set the selected language for the navbar on change\r\n    this.selectedLanguage = language;\r\n\r\n    // Use the selected language id for translations\r\n    this._translateService.use(language);\r\n\r\n    this._coreConfigService.setConfig(\r\n      { app: { appLanguage: language } },\r\n      { emitEvent: true }\r\n    );\r\n\r\n    if (this._authService.currentUserValue != null) {\r\n      this._authService\r\n        .updateUserLanguage(language)\r\n        .pipe(takeUntil(this._unsubscribeAll))\r\n        .subscribe(\r\n          (data: any) => {\r\n            this._toastrService.success(\r\n              this._translateService.instant('Language changed successfully'),\r\n            );\r\n            // setTimeout(() => {\r\n            //   // reload the page\r\n            //   window.location.reload();\r\n            // }, 2000);\r\n          },\r\n          (error: any) => {\r\n            Swal.fire({\r\n              title: 'Error!',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#ed1c24',\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Toggle Dark Skin\r\n   */\r\n  toggleDarkSkin() {\r\n    // Get the current skin\r\n    this._coreConfigService\r\n      .getConfig()\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe((config) => {\r\n        this.currentSkin = config.layout.skin;\r\n      });\r\n\r\n    // Toggle Dark skin with prevSkin skin\r\n    this.prevSkin = localStorage.getItem('prevSkin');\r\n\r\n    if (this.currentSkin === 'dark') {\r\n      this._coreConfigService.setConfig(\r\n        { layout: { skin: this.prevSkin ? this.prevSkin : 'default' } },\r\n        { emitEvent: true }\r\n      );\r\n    } else {\r\n      localStorage.setItem('prevSkin', this.currentSkin);\r\n      this._coreConfigService.setConfig(\r\n        { layout: { skin: 'dark' } },\r\n        { emitEvent: true }\r\n      );\r\n    }\r\n  }\r\n\r\n  resetData() {\r\n    // are you sure\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        `<p class=\"text-center\">` +\r\n        this._translateService.instant(\r\n          'This will reset all your data and you will be logged out'\r\n        ) +\r\n        `</p>\r\n      <p class=\"text-center\">\r\n      ` +\r\n        this._translateService.instant('This action cannot be undone') +\r\n        `</p>\r\n      `,\r\n      reverseButtons: true,\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      confirmButtonColor: '#ed1c24',\r\n      cancelButtonColor: '#999999',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this._loadingService.show();\r\n        this._userService.resetData().subscribe((data: any) => {\r\n          this._toastrService.success(data.message, 'Success!', {\r\n            toastClass: 'toast ngx-toastr',\r\n            closeButton: true,\r\n          });\r\n\r\n          this._authService.logoutNotRequest();\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Logout method\r\n   */\r\n  logout() {\r\n    this._authService.logout();\r\n    //  this._router.navigate([\"/auth/login\"]);\r\n  }\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n\r\n  ngOnInit(): void {\r\n    const userString = localStorage.getItem('EZLEAGUE_USER');\r\n    if (userString) {\r\n      this.currentUser = JSON.parse(userString);\r\n    }\r\n    console.log('currentUser', this.currentUser);\r\n  \r\n    this._coreConfigService.config\r\n      .pipe(takeUntil(this._unsubscribeAll))\r\n      .subscribe((config) => {\r\n        this.coreConfig = config;\r\n        this.horizontalMenu = config.layout.type === 'horizontal';\r\n        this.hiddenMenu = config.layout.menu.hidden === true;\r\n        this.currentSkin = config.layout.skin;\r\n  \r\n        if (this.coreConfig.layout.type === 'vertical') {\r\n          setTimeout(() => {\r\n            if (this.coreConfig.layout.navbar.type === 'fixed-top') {\r\n              this.isFixed = true;\r\n            }\r\n          }, 0);\r\n        }\r\n      });\r\n  }\r\n  \r\n\r\n  ngAfterViewInit() {\r\n    // Horizontal Layout Only: Add class fixed-top to navbar below large screen\r\n    if (this.coreConfig?.layout.type === 'horizontal') {\r\n      // On every media(screen) change\r\n      this._coreMediaService.onMediaUpdate\r\n        .pipe(takeUntil(this._unsubscribeAll))\r\n        .subscribe(() => {\r\n          const isFixedTop = this._mediaObserver.isActive('bs-gt-xl');\r\n          this.isFixed = !isFixedTop;\r\n        });\r\n    }\r\n\r\n    // Set the selected language from default languageOptions\r\n    this.selectedLanguage = _.find(this.languageOptions, {\r\n      id: this._translateService.currentLang,\r\n    });\r\n  }\r\n\r\n  get role_id() {\r\n    return this.currentUser?.role?.id;\r\n  }\r\n\r\n  /**\r\n   * On destroy\r\n   */\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n\r\n  onClickLogin() {\r\n    const user = JSON.parse(localStorage.getItem('EZLEAGUE_USER'));\r\n\r\n    if (user) {\r\n      this._authService.logoutAsGuest(user);\r\n    }\r\n  }\r\n  // get all url in menu\r\n  getAllUrl(menu) {\r\n    let urls = [];\r\n    menu.forEach((element) => {\r\n      if (element.url) {\r\n        urls.push(element.url);\r\n      }\r\n      if (element.children) {\r\n        let childUrls = this.getAllUrl(element.children);\r\n        urls = [...urls, ...childUrls];\r\n      }\r\n    });\r\n    return urls;\r\n  }\r\n  // on route change\r\n  onRouteChange() {\r\n    let url = this._router.url.slice(1);\r\n    // strub to first ?\r\n    let index = url.indexOf('?');\r\n    if (index > -1) {\r\n      url = url.substring(0, index);\r\n    }\r\n    console.log('router', url);\r\n    \r\n    this.isHome = (url === 'home');\r\n    \r\n    // if (url.includes('no_layout')) {\r\n    //   this._coreConfigService.config = {\r\n    //     layout: {\r\n    //       type: 'none',\r\n    //     },\r\n    //   };\r\n    //   return;\r\n    // }\r\n    // if url is not in menu then set isChildPage to true\r\n    if (!this.menu_urls.includes(url)) {\r\n      this.isChildPage = true;\r\n      let check = false;\r\n      this.ignoreURLList.forEach((element) => {\r\n        if (url.includes(element)) {\r\n          check = true;\r\n        }\r\n      });\r\n      if (check) {\r\n        this.isChildPage = false;\r\n        console.log('ignore url', url);\r\n      } else {\r\n        this._coreConfigService.config = {\r\n          layout: {\r\n            navbar: {\r\n              hidden: false,\r\n              type: 'fixed-top',\r\n            },\r\n            menu: {\r\n              hidden: false,\r\n            },\r\n            footer: {\r\n              hidden: true,\r\n            },\r\n            customizer: false,\r\n            enableLocalStorage: true,\r\n          },\r\n          type: 'vertical',\r\n        };\r\n      }\r\n    } else {\r\n      this.isChildPage = false;\r\n      this._coreConfigService.config = {\r\n        layout: {\r\n          navbar: {\r\n            hidden: false,\r\n            type: 'fixed-top',\r\n          },\r\n          menu: {\r\n            hidden: false,\r\n          },\r\n          footer: {\r\n            hidden: true,\r\n          },\r\n          customizer: false,\r\n          enableLocalStorage: true,\r\n          type: 'vertical',\r\n        },\r\n      };\r\n    }\r\n  }\r\n}\r\n", "<div *ngIf=\"horizontalMenu\" class=\"navbar-header d-xl-block d-none\">\r\n  <!-- Navbar brand -->\r\n  <ul class=\"nav navbar-nav flex-row\">\r\n    <li class=\"nav-item\">\r\n      <a class=\"navbar-brand\" [routerLink]=\"['/']\">\r\n        <span class=\"brand-logo\">\r\n          <img src=\"{{ coreConfig.app.appLogoImage }}\" alt=\"brand-logo\" width=\"36\" />\r\n        </span>\r\n        <h2 class=\"brand-text mb-0\">{{ coreConfig.app.appName }}</h2>\r\n      </a>\r\n    </li>\r\n  </ul>\r\n  <!--/ Navbar brand -->\r\n</div>\r\n\r\n<div class=\"navbar-container d-flex content\">\r\n  <div class=\"bookmark-wrapper d-flex align-items-center\" *ngIf=\"!isChildPage\">\r\n    <!-- Menu Toggler | Menu icon will be hidden in case of layout without menu -->\r\n    <ul class=\"nav navbar-nav d-xl-none\" *ngIf=\"!coreConfig.layout.menu.hidden \">\r\n      <li class=\"nav-item\">\r\n        <a class=\"nav-link menu-toggle\" (click)=\"toggleSidebar('menu')\">\r\n          <span [data-feather]=\"'menu'\" [class]=\"'ficon'\"></span>\r\n        </a>\r\n      </li>\r\n    </ul>\r\n    <!--/ Menu Toggler -->\r\n\r\n    <!-- Toggle skin -->\r\n    <li class=\"nav-item d-none\">\r\n      <a type=\"button\" class=\"nav-link nav-link-style btn\" (click)=\"toggleDarkSkin()\">\r\n        <span [ngClass]=\"currentSkin === 'dark' ? 'icon-sun' : 'icon-moon'\" class=\"ficon font-medium-5 feather\"></span>\r\n      </a>\r\n    </li>\r\n    <!--/ Toggle skin -->\r\n  </div>\r\n\r\n  <!-- buttons left -->\r\n  <div class=\"d-flex align-items-center\" *ngIf=\"isChildPage\">\r\n    <!-- button back -->\r\n    <button backButton class=\"btn btn-icon btn-flat-black\">\r\n      <i class=\"fa-solid fa-chevron-left fa-xl\"></i>\r\n\r\n      <span class=\"d-md-inline d-none\" style=\"font-size: 1.26rem;\">\r\n        {{'Back' | translate}}\r\n      </span>\r\n      <span class=\"d-md-none d-inline\" style=\"font-size: 1.26rem;\">\r\n        {{titleService.getTitle() | translate }}\r\n      </span>\r\n    </button>\r\n  </div>\r\n\r\n  <ul class=\"nav navbar-nav align-items-center ml-auto\" *ngIf=\"isHome && !isChildPage\">\r\n    <!-- Language selection -->\r\n    <li ngbDropdown class=\"nav-item dropdown dropdown-language\">\r\n      <a class=\"nav-link dropdown-toggle\" id=\"dropdown-flag\" ngbDropdownToggle>\r\n        <i class=\"bi bi-translate\"></i>\r\n        <span class=\"selected-language\"> {{ languageOptions[_translateService.currentLang]?.title }}</span>\r\n      </a>\r\n      <div ngbDropdownMenu aria-labelledby=\"dropdown-flag\">\r\n        <a *ngFor=\"let lang of _translateService.getLangs()\" ngbDropdownItem (click)=\"setLanguage(lang)\">\r\n          {{ languageOptions[lang]?.title }}\r\n        </a>\r\n      </div>\r\n    </li>\r\n    <!--/ Language selection -->\r\n\r\n    <!-- Notification -->\r\n    <app-navbar-notification></app-navbar-notification>\r\n    <!--/ Notification -->\r\n\r\n    <!-- User Dropdown -->\r\n    <li ngbDropdown class=\"nav-item dropdown-user\" *ngIf=\"currentUser?.role?.id != 7\">\r\n      <a class=\"nav-link dropdown-toggle dropdown-user-link\" id=\"dropdown-user\" ngbDropdownToggle\r\n         id=\"navbarUserDropdown\" aria-haspopup=\"true\" aria-expanded=\"false\">\r\n        <div class=\"user-nav d-sm-flex d-none\">\r\n          <span class=\"user-name font-weight-bolder\">\r\n            {{ name_settings?.is_on == 1 \r\n                ? currentUser?.first_name + ' ' + currentUser?.last_name \r\n                : currentUser?.last_name + ' ' + currentUser?.first_name }}\r\n          </span>\r\n          <span class=\"user-status\">{{ currentUser?.role?.name | translate }}</span>\r\n        </div>\r\n        <span class=\"avatar\">\r\n          <img class=\"round\" src=\"{{ coreConfig?.app?.appLogoImage }}\" alt=\"avatar\" height=\"40\" width=\"40\" />\r\n          <span class=\"avatar-status-online\"></span>\r\n        </span>\r\n      </a>\r\n      <div ngbDropdownMenu aria-labelledby=\"navbarUserDropdown\" class=\"dropdown-menu dropdown-menu-right\">\r\n        <a ngbDropdownItem [routerLink]=\"['/profile']\">\r\n          <span [data-feather]=\"'user'\" class=\"mr-50\"></span>\r\n          {{ 'Account' | translate }}\r\n        </a>\r\n        <div class=\"dropdown-divider\"></div>\r\n        <a ngbDropdownItem (click)=\"logout()\">\r\n          <span [data-feather]=\"'power'\" class=\"mr-50\"></span>\r\n          {{ 'Logout' | translate }}\r\n        </a>\r\n      </div>\r\n    </li>\r\n    <li class=\"nav-item\" *ngIf=\"currentUser?.role?.id == 7\">\r\n      <a class=\"nav-link\" (click)=\"onClickLogin()\">\r\n        <span class=\"nav-link-text text-primary\">{{ 'Login' | translate }}</span>\r\n      </a>\r\n    </li>\r\n    \r\n    <!--/ User Dropdown -->\r\n    <!-- Not Login -->\r\n  \r\n  </ul>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
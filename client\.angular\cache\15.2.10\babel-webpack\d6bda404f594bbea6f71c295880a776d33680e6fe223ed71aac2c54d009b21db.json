{"ast": null, "code": "import { ViewContainerRef } from '@angular/core';\nimport { FieldWrapper } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"fieldComponent\"];\nfunction DetailsWrapperComponent_ng_container_3_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p\", 4);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r2.description, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DetailsWrapperComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsWrapperComponent_ng_container_3_p_1_Template, 1, 1, \"p\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r2.value === ctx_r1.field.formControl.value && item_r2.description);\n  }\n}\nexport class DetailsWrapperComponent extends FieldWrapper {\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵDetailsWrapperComponent_BaseFactory;\n    return function DetailsWrapperComponent_Factory(t) {\n      return (ɵDetailsWrapperComponent_BaseFactory || (ɵDetailsWrapperComponent_BaseFactory = i0.ɵɵgetInheritedFactory(DetailsWrapperComponent)))(t || DetailsWrapperComponent);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DetailsWrapperComponent,\n    selectors: [[\"formly-wrapper-details\"]],\n    viewQuery: function DetailsWrapperComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fieldComponent = _t.first);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 4,\n    vars: 1,\n    consts: [[1, \"form-group\"], [\"fieldComponent\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"ml-1\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"ml-1\", 3, \"innerHTML\"]],\n    template: function DetailsWrapperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementContainer(1, null, 1);\n        i0.ɵɵtemplate(3, DetailsWrapperComponent_ng_container_3_Template, 2, 1, \"ng-container\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.field.props.options);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAA+BA,gBAAgB,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,kBAAkB;;;;;;IASvCC,EAAA,CAAAC,SAAA,WACI;;;;IAD+ED,EAAA,CAAAE,UAAA,cAAAC,OAAA,CAAAC,WAAA,EAAAJ,EAAA,CAAAK,cAAA,CAA8B;;;;;IADnHL,EAAA,CAAAM,uBAAA,GAAuD;IACrDN,EAAA,CAAAO,UAAA,IAAAC,mDAAA,eACI;IACNR,EAAA,CAAAS,qBAAA,EAAe;;;;;IAFIT,EAAA,CAAAU,SAAA,GAAgE;IAAhEV,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAQ,KAAA,KAAAC,MAAA,CAAAC,KAAA,CAAAC,WAAA,CAAAH,KAAA,IAAAR,OAAA,CAAAC,WAAA,CAAgE;;;AAMzF,OAAM,MAAOW,uBAAwB,SAAQhB,YAAY;EAAA,QAAAiB,CAAA;;;uHAA5CD,uBAAuB,IAAAE,CAAA,IAAvBF,uBAAuB;IAAA;EAAA;EAAA,QAAAG,EAAA;UAAvBH,uBAAuB;IAAAI,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;+BACGxB,gBAAgB;;;;;;;;;;;;;QAVnDE,EAAA,CAAAwB,cAAA,aAAwB;QACtBxB,EAAA,CAAAyB,kBAAA,YAA6C;QAC7CzB,EAAA,CAAAO,UAAA,IAAAmB,+CAAA,0BAGe;QACjB1B,EAAA,CAAA2B,YAAA,EAAM;;;QAJ2B3B,EAAA,CAAAU,SAAA,GAAsB;QAAtBV,EAAA,CAAAE,UAAA,YAAAqB,GAAA,CAAAV,KAAA,CAAAe,KAAA,CAAAC,OAAA,CAAsB", "names": ["ViewContainerRef", "FieldWrapper", "i0", "ɵɵelement", "ɵɵproperty", "item_r2", "description", "ɵɵsanitizeHtml", "ɵɵelementContainerStart", "ɵɵtemplate", "DetailsWrapperComponent_ng_container_3_p_1_Template", "ɵɵelementContainerEnd", "ɵɵadvance", "value", "ctx_r1", "field", "formControl", "DetailsWrapperComponent", "_", "t", "_2", "selectors", "viewQuery", "DetailsWrapperComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainer", "DetailsWrapperComponent_ng_container_3_Template", "ɵɵelementEnd", "props", "options"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-details\\details-wraper.component.ts"], "sourcesContent": ["import { Component, ViewChild, ViewContainerRef } from '@angular/core';\r\nimport { FieldWrapper } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'formly-wrapper-details',\r\n  template: `\r\n    <style></style>\r\n    <div class=\"form-group\">\r\n      <ng-container #fieldComponent></ng-container>\r\n      <ng-container *ngFor=\"let item of field.props.options\">\r\n        <p class=\"ml-1\" *ngIf=\"item.value === field.formControl.value && item.description\" [innerHTML]=\"item.description\">\r\n        </p>\r\n      </ng-container>\r\n    </div>\r\n  `,\r\n})\r\nexport class DetailsWrapperComponent extends FieldWrapper {\r\n  @ViewChild('fieldComponent', { read: ViewContainerRef })\r\n  fieldComponent: ViewContainerRef;\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
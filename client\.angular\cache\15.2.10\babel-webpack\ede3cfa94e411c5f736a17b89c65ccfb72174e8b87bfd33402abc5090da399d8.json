{"ast": null, "code": "import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_CURRY_FLAG = 8;\n\n/**\n * Creates a function that accepts arguments of `func` and either invokes\n * `func` returning its result, if at least `arity` number of arguments have\n * been provided, or returns a function that accepts the remaining `func`\n * arguments, and so on. The arity of `func` may be specified if `func.length`\n * is not sufficient.\n *\n * The `_.curry.placeholder` value, which defaults to `_` in monolithic builds,\n * may be used as a placeholder for provided arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of curried functions.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Function\n * @param {Function} func The function to curry.\n * @param {number} [arity=func.length] The arity of `func`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the new curried function.\n * @example\n *\n * var abc = function(a, b, c) {\n *   return [a, b, c];\n * };\n *\n * var curried = _.curry(abc);\n *\n * curried(1)(2)(3);\n * // => [1, 2, 3]\n *\n * curried(1, 2)(3);\n * // => [1, 2, 3]\n *\n * curried(1, 2, 3);\n * // => [1, 2, 3]\n *\n * // Curried with placeholders.\n * curried(1)(_, 3)(2);\n * // => [1, 2, 3]\n */\nfunction curry(func, arity, guard) {\n  arity = guard ? undefined : arity;\n  var result = createWrap(func, WRAP_CURRY_FLAG, undefined, undefined, undefined, undefined, undefined, arity);\n  result.placeholder = curry.placeholder;\n  return result;\n}\n\n// Assign default placeholders.\ncurry.placeholder = {};\nexport default curry;", "map": {"version": 3, "names": ["createWrap", "WRAP_CURRY_FLAG", "curry", "func", "arity", "guard", "undefined", "result", "placeholder"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/curry.js"], "sourcesContent": ["import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_CURRY_FLAG = 8;\n\n/**\n * Creates a function that accepts arguments of `func` and either invokes\n * `func` returning its result, if at least `arity` number of arguments have\n * been provided, or returns a function that accepts the remaining `func`\n * arguments, and so on. The arity of `func` may be specified if `func.length`\n * is not sufficient.\n *\n * The `_.curry.placeholder` value, which defaults to `_` in monolithic builds,\n * may be used as a placeholder for provided arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of curried functions.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Function\n * @param {Function} func The function to curry.\n * @param {number} [arity=func.length] The arity of `func`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the new curried function.\n * @example\n *\n * var abc = function(a, b, c) {\n *   return [a, b, c];\n * };\n *\n * var curried = _.curry(abc);\n *\n * curried(1)(2)(3);\n * // => [1, 2, 3]\n *\n * curried(1, 2)(3);\n * // => [1, 2, 3]\n *\n * curried(1, 2, 3);\n * // => [1, 2, 3]\n *\n * // Curried with placeholders.\n * curried(1)(_, 3)(2);\n * // => [1, 2, 3]\n */\nfunction curry(func, arity, guard) {\n  arity = guard ? undefined : arity;\n  var result = createWrap(func, WRAP_CURRY_FLAG, undefined, undefined, undefined, undefined, undefined, arity);\n  result.placeholder = curry.placeholder;\n  return result;\n}\n\n// Assign default placeholders.\ncurry.placeholder = {};\n\nexport default curry;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,eAAe,GAAG,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACjCD,KAAK,GAAGC,KAAK,GAAGC,SAAS,GAAGF,KAAK;EACjC,IAAIG,MAAM,GAAGP,UAAU,CAACG,IAAI,EAAEF,eAAe,EAAEK,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEF,KAAK,CAAC;EAC5GG,MAAM,CAACC,WAAW,GAAGN,KAAK,CAACM,WAAW;EACtC,OAAOD,MAAM;AACf;;AAEA;AACAL,KAAK,CAACM,WAAW,GAAG,CAAC,CAAC;AAEtB,eAAeN,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup, FormGroupDirective } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"@ngx-formly/core\";\nfunction EditorSidebarComponent_ngb_alert_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngb-alert\", 17)(1, \"h4\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 19);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"type\", \"warning\")(\"dismissible\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"Warning\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 6, \"You are editing multiple rows! Please be careful, data will be updated for all selected rows.\"), \" \");\n  }\n}\n// get form directive\nexport class EditorSidebarComponent {\n  constructor(_coreSidebarService, _http, _translateService, _loading, router) {\n    this._coreSidebarService = _coreSidebarService;\n    this._http = _http;\n    this._translateService = _translateService;\n    this._loading = _loading;\n    this.router = router;\n    this.onCloseEvent = new EventEmitter();\n    this.onOpenEvent = new EventEmitter();\n    this.triggerClickOverlay = false;\n    this.onClose = new EventEmitter();\n    this.form = new FormGroup({});\n    this.model = {};\n    this.new_model = {};\n    this.onSuccess = new EventEmitter();\n    this.defaultParams = {\n      editor_id: 'editor-sidebar',\n      title: {\n        create: 'Create new record',\n        edit: 'Edit record',\n        remove: 'Delete record'\n      },\n      button: {\n        create: {\n          submit: 'Create',\n          cancel: 'Cancel'\n        },\n        edit: {\n          submit: 'Update',\n          cancel: 'Cancel'\n        },\n        remove: {\n          submit: 'Delete',\n          cancel: 'Cancel'\n        }\n      },\n      url: '',\n      method: 'POST',\n      action: 'create',\n      row: null\n    };\n    this.formChange = new EventEmitter();\n    this.responseData = new EventEmitter();\n    this.rows_selected = [{\n      id: '0'\n    }];\n    this.new_params = {};\n    this.isSubmitted = false;\n  }\n  ngOnInit() {\n    $.fx.off = true;\n    // merge default params with input params\n    this.new_params = {\n      ...this.defaultParams,\n      ...this.params\n    };\n    if (this.fields_subject) {\n      this.fields_subject.subscribe(fields => {\n        this.fields = fields;\n        // sync form with fields, remove controls that not in fields\n        const controls = this.form.controls;\n        for (const key in controls) {\n          if (controls.hasOwnProperty(key)) {\n            if (!fields.find(field => field.key === key)) {\n              this.form.removeControl(key);\n            }\n          }\n        }\n      });\n    }\n    this._coreSidebarService.setOverlayClickEvent(this.new_params.editor_id, this.onCloseEvent);\n    this._coreSidebarService.setOnOpenEvent(this.new_params.editor_id, this.onOpenEvent);\n    this.onCloseEvent.subscribe(() => {\n      if (!this.triggerClickOverlay) {\n        this.triggerClickOverlay = true;\n        // this.reset();\n        this.close();\n      }\n    });\n    this.onOpenEvent.subscribe(() => {\n      this.triggerClickOverlay = false;\n      this.isSubmitted = false;\n      this.new_params = {\n        ...this.defaultParams,\n        ...this.params\n      };\n      switch (this.new_params.action) {\n        case 'create':\n          if (this.new_params.hasOwnProperty('use_data') && this.new_params.use_data) {\n            this.getSelectedRows(this.new_params.action);\n          } else {\n            this.rows_selected = [{\n              id: '0'\n            }];\n          }\n          break;\n        case 'edit':\n          this.getSelectedRows(this.new_params.action);\n          break;\n        case 'remove':\n          this.close();\n          if (this.getSelectedRows(this.new_params.action)) {\n            // Show alert to confirm delete\n            Swal.fire({\n              title: this._translateService.instant('Are you sure to delete?'),\n              text: this._translateService.instant('You won\\'t be able to revert this!'),\n              reverseButtons: true,\n              icon: 'warning',\n              showCancelButton: true,\n              cancelButtonText: this._translateService.instant('Cancel'),\n              confirmButtonText: this._translateService.instant('OK'),\n              buttonsStyling: false,\n              customClass: {\n                confirmButton: 'btn btn-primary ml-1',\n                cancelButton: 'btn btn-outline-primary'\n              }\n            }).then(result => {\n              if (result.value) {\n                this.submit(this.rows_selected[0]);\n              } else {\n                return;\n              }\n            });\n          }\n          break;\n        default:\n          break;\n      }\n      // merge model with input model\n      this.model = {\n        ...this.model,\n        ...this.new_model\n      };\n      // console.log('fields', this.fields);\n      // console.log('form', this.form);\n    });\n    // form change event\n    this.form.valueChanges.subscribe(value => {\n      this.formChange.emit(this.form);\n    });\n  }\n  submit(model) {\n    let controls_value = this.form.value;\n    if (this.onSubmit) {\n      this.onSubmit(model);\n    }\n    const formData = new FormData();\n    formData.append('action', this.new_params.action);\n    // const row_id = this.rows_selected.length > 0 ? this.rows_selected[0].id : 0;\n    for (const key in controls_value) {\n      if (controls_value.hasOwnProperty(key)) {\n        for (let i = 0; i < this.rows_selected.length; i++) {\n          if (this.rows_selected[i].id) {\n            let row_id = this.rows_selected[i].id;\n            const newKey = this.convertKey2Editor(key, row_id);\n            // if value is FileList object\n            if (controls_value[key] instanceof FileList) {\n              // get file from FileList object\n              const file = controls_value[key].item(0);\n              // append file to formData\n              formData.append(newKey, file);\n            } else {\n              formData.append(newKey, this.parseNull(controls_value[key]));\n            }\n          }\n        }\n      }\n    }\n    if (this.paramsToPost) {\n      for (const key in this.paramsToPost) {\n        if (this.paramsToPost[key] instanceof FileList) {\n          const file = this.paramsToPost[key].item(0);\n          formData.append(key, file);\n        } else {\n          formData.append(key, this.paramsToPost[key]);\n        }\n      }\n    }\n    // if form is valid\n    if (this.form.valid || this.new_params.action == 'remove' && model.id) {\n      this._loading.show();\n      // send data to server\n      switch (this.new_params.method) {\n        case 'POST':\n          this._http.post(this.new_params.url, formData).subscribe(res => {\n            if (res.data) {\n              let data = res.data;\n              switch (this.new_params.action) {\n                case 'create':\n                  // add multiple rows\n                  data.forEach(row => {\n                    this.table.dt.row.add(row).draw();\n                  });\n                  break;\n                case 'edit':\n                  // update all rows with new data\n                  data.forEach(row => {\n                    this.table.dt.row('#' + row.id).data(row);\n                  });\n                  this.table.dt.draw();\n                  break;\n                case 'remove':\n                  this.table.dt.row('#' + data[0].id).remove().draw();\n                  break;\n                default:\n                  break;\n              }\n            }\n            // run callback function\n            this.onSuccess.emit(res);\n            return res;\n          }, error => {\n            console.log(error);\n            if (error.hasOwnProperty('fieldErrors')) {\n              error.fieldErrors.forEach(error => {\n                this.form.controls[error.name].setErrors({\n                  serverError: error.status\n                });\n              });\n            }\n            if (error.hasOwnProperty('error')) {\n              Swal.fire({\n                title: this._translateService.instant('Error'),\n                text: error.error,\n                icon: 'error',\n                customClass: {\n                  confirmButton: 'btn btn-primary'\n                }\n              });\n              this.responseData.emit(error.error);\n            } else {\n              this.responseData.emit(error);\n            }\n            switch (this.new_params.action) {\n              case 'create':\n                break;\n              case 'edit':\n                break;\n              case 'remove':\n                this.close();\n                break;\n              default:\n                break;\n            }\n          }, () => {\n            this.isSubmitted = true;\n            this.close();\n          });\n          break;\n        default:\n          break;\n      }\n    }\n  }\n  reset() {\n    // reset form directives\n    // this.close();\n    this.rows_selected = [];\n    this.form.reset({});\n    this.model = {};\n    this.formDirective.resetForm();\n  }\n  convertKey2Editor(key, row_id) {\n    if (this.new_params.action == 'create' && this.new_params.hasOwnProperty('use_data') && this.new_params.use_data) {\n      // set  row_id to 0\n      row_id = '0';\n    }\n    // convert key to array\n    const keyArr = key.split('.');\n    let result = `data[${row_id}]`;\n    keyArr.forEach(key => {\n      result += `[${key}]`;\n    });\n    return result;\n  }\n  close() {\n    this.onClose.emit({\n      action: this.new_params.action,\n      formValue: this.form.value,\n      isSubmitted: this.isSubmitted\n    });\n    this.reset();\n    this._coreSidebarService.getSidebarRegistry(this.new_params.editor_id).toggleOpen(false);\n  }\n  // ondestroy unsubscribe events\n  ngOnDestroy() {\n    this.onCloseEvent.unsubscribe();\n    this.onOpenEvent.unsubscribe();\n  }\n  getSelectedRows(action) {\n    let model = {};\n    this.rows_selected = this.table.dt.rows({\n      selected: true\n    }).data();\n    if (this.rows_selected.length == 0 && !this.new_params.row && !this.new_params.row_id) {\n      this.close();\n      let message = '';\n      switch (action) {\n        case 'edit':\n          message = this._translateService.instant('Please select a row to edit');\n          break;\n        case 'remove':\n          message = this._translateService.instant('Please select a row to remove');\n          break;\n        case 'create':\n          message = this._translateService.instant('Please select a row to use data');\n          break;\n        default:\n          break;\n      }\n      Swal.fire({\n        title: this._translateService.instant('Warning'),\n        text: message,\n        icon: 'warning',\n        confirmButtonText: this._translateService.instant('OK'),\n        customClass: {\n          confirmButton: 'btn btn-primary'\n        }\n      }).then(result => {\n        return false;\n      });\n      return false;\n    }\n    if (this.new_params.row || this.new_params.row_id) {\n      let selector = this.new_params.row ? `#${this.new_params.row.id}` : `#${this.new_params.row_id} `;\n      let row = this.table.dt.row(selector).data();\n      //  if found row\n      if (row) {\n        this.rows_selected = [row];\n        this.fields.forEach(field => {\n          let value = row[field.key];\n          if (field.props && field.props.hasOwnProperty('type') && field.props.type == 'date') {\n            value = moment(value).format('YYYY-MM-DD');\n          }\n          if (!value) value = this.parseNull(field.defaultValue);\n          model[field.key] = value;\n        });\n      }\n    } else {\n      this.fields.forEach(field => {\n        let value = this.rows_selected[0][field.key];\n        if (!value) value = this.parseNull(field.defaultValue);\n        model[field.key] = value;\n        // if field props has key hideOnMultiple and value is true and rows_selected.length > 1 then hide field\n        if (field.props && field.props.hideOnMultiple && this.rows_selected.length > 1) {\n          field.hide = true;\n        } else {\n          if (field.expressions) {\n            // console.log(field.key, eval(field.expressions.hide));\n            if (eval(field.expressions.hide)) {\n              field.hide = true;\n            } else {\n              field.hide = false;\n            }\n          }\n        }\n      });\n    }\n    // merge model with new_model\n    this.model = model;\n    console.log(this.model);\n    return true;\n  }\n  parseNull(value) {\n    // get route is 'clubs'\n    if (this.router.url.includes('clubs') && value == 'TBD') {\n      return value;\n    }\n    if (value == null || value == undefined || value == 'TBD') {\n      return '';\n    } else {\n      return value;\n    }\n  }\n  static #_ = this.ɵfac = function EditorSidebarComponent_Factory(t) {\n    return new (t || EditorSidebarComponent)(i0.ɵɵdirectiveInject(i1.CoreSidebarService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EditorSidebarComponent,\n    selectors: [[\"app-editor-sidebar\"]],\n    viewQuery: function EditorSidebarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(FormGroupDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.formDirective = _t.first);\n      }\n    },\n    inputs: {\n      new_model: \"new_model\",\n      fields: \"fields\",\n      fields_subject: \"fields_subject\",\n      table: \"table\",\n      onSubmit: \"onSubmit\",\n      params: \"params\",\n      paramsToPost: \"paramsToPost\"\n    },\n    outputs: {\n      onClose: \"onClose\",\n      onSuccess: \"onSuccess\",\n      formChange: \"formChange\",\n      responseData: \"responseData\"\n    },\n    decls: 27,\n    vars: 17,\n    consts: [[1, \"slideout-content\"], [1, \"modalsd\", \"modal-slide-in\", \"sdfade\"], [1, \"modal-dialog\", \"dialog-full\"], [1, \"modal-content\", \"pt-0\", 3, \"formGroup\", \"ngSubmit\"], [\"formDirective\", \"ngForm\"], [1, \"modal-header\", \"mb-1\", \"text-primary\", \"text-center\"], [1, \"btn-back\", 3, \"click\"], [1, \"fa\", \"fa-chevron-left\"], [1, \"col\"], [1, \"h4\", \"title-editor\", \"text-capitalize\"], [1, \"modal-body\", \"no-margin\"], [3, \"type\", \"dismissible\", 4, \"ngIf\"], [3, \"form\", \"fields\", \"model\"], [1, \"row\", \"justify-content-end\"], [1, \"col-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [3, \"type\", \"dismissible\"], [1, \"alert-heading\"], [1, \"alert-body\"]],\n    template: function EditorSidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3, 4);\n        i0.ɵɵlistener(\"ngSubmit\", function EditorSidebarComponent_Template_form_ngSubmit_3_listener() {\n          return ctx.submit(ctx.model);\n        });\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵlistener(\"click\", function EditorSidebarComponent_Template_div_click_6_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵelement(7, \"i\", 7);\n        i0.ɵɵelementStart(8, \"span\");\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"b\", 9);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(15, \"div\", 10);\n        i0.ɵɵtemplate(16, EditorSidebarComponent_ngb_alert_16_Template, 7, 8, \"ngb-alert\", 11);\n        i0.ɵɵelement(17, \"formly-form\", 12);\n        i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14)(20, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function EditorSidebarComponent_Template_button_click_20_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 14)(24, \"button\", 16);\n        i0.ɵɵtext(25);\n        i0.ɵɵpipe(26, \"translate\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 9, \"Back\"), \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 11, ctx.new_params.title[ctx.new_params.action]), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.rows_selected.length > 1);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 13, ctx.new_params.button[ctx.new_params.action].cancel), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 15, ctx.new_params.button[ctx.new_params.action].submit), \" \");\n      }\n    },\n    dependencies: [i6.NgIf, i7.ɵNgNoValidate, i7.NgControlStatusGroup, i7.FormGroupDirective, i8.NgbAlert, i9.FormlyForm, i3.TranslatePipe],\n    styles: [\".bg-white {\\n  background-color: #ffffff !important;\\n}\\n.bg-white .card-header,\\n.bg-white .card-footer {\\n  background-color: transparent;\\n}\\n\\n.border-white {\\n  border: 1px solid #ffffff !important;\\n}\\n\\n.border-top-white {\\n  border-top: 1px solid #ffffff;\\n}\\n\\n.border-bottom-white {\\n  border-bottom: 1px solid #ffffff;\\n}\\n\\n.border-left-white {\\n  border-left: 1px solid #ffffff;\\n}\\n\\n.border-right-white {\\n  border-right: 1px solid #ffffff;\\n}\\n\\n.bg-white.badge-glow,\\n.border-white.badge-glow,\\n.badge-white.badge-glow {\\n  box-shadow: 0px 0px 10px #ffffff;\\n}\\n\\n.overlay-white {\\n  background: #ffffff; /* The Fallback */\\n  background: rgba(255, 255, 255, 0.6);\\n}\\n\\ninput:focus ~ .bg-white {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #ffffff !important;\\n}\\n\\n.bg-black {\\n  background-color: #000000 !important;\\n}\\n.bg-black .card-header,\\n.bg-black .card-footer {\\n  background-color: transparent;\\n}\\n\\n.border-black {\\n  border: 1px solid #000000 !important;\\n}\\n\\n.border-top-black {\\n  border-top: 1px solid #000000;\\n}\\n\\n.border-bottom-black {\\n  border-bottom: 1px solid #000000;\\n}\\n\\n.border-left-black {\\n  border-left: 1px solid #000000;\\n}\\n\\n.border-right-black {\\n  border-right: 1px solid #000000;\\n}\\n\\n.bg-black.badge-glow,\\n.border-black.badge-glow,\\n.badge-black.badge-glow {\\n  box-shadow: 0px 0px 10px #000000;\\n}\\n\\n.overlay-black {\\n  background: #000000; /* The Fallback */\\n  background: rgba(0, 0, 0, 0.6);\\n}\\n\\ninput:focus ~ .bg-black {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #000000 !important;\\n}\\n\\n.bg-dark {\\n  background-color: #4b4b4b !important;\\n}\\n.bg-dark .card-header,\\n.bg-dark .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-dark {\\n  background: rgba(75, 75, 75, 0.12) !important;\\n  color: #4b4b4b !important;\\n}\\n.alert-dark .alert-heading {\\n  box-shadow: rgba(75, 75, 75, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-dark .alert-link {\\n  color: #3e3e3e !important;\\n}\\n.alert-dark .close {\\n  color: #4b4b4b !important;\\n}\\n\\n.border-dark {\\n  border: 1px solid #4b4b4b !important;\\n}\\n\\n.border-top-dark {\\n  border-top: 1px solid #4b4b4b;\\n}\\n\\n.border-bottom-dark {\\n  border-bottom: 1px solid #4b4b4b;\\n}\\n\\n.border-left-dark {\\n  border-left: 1px solid #4b4b4b;\\n}\\n\\n.border-right-dark {\\n  border-right: 1px solid #4b4b4b;\\n}\\n\\n.bg-dark.badge-glow,\\n.border-dark.badge-glow,\\n.badge-dark.badge-glow {\\n  box-shadow: 0px 0px 10px #4b4b4b;\\n}\\n\\n.badge.badge-light-dark {\\n  background-color: rgba(75, 75, 75, 0.12);\\n  color: #4b4b4b !important;\\n}\\n\\n.overlay-dark {\\n  background: #4b4b4b; /* The Fallback */\\n  background: rgba(75, 75, 75, 0.6);\\n}\\n\\n.btn-dark {\\n  border-color: #4b4b4b !important;\\n  background-color: #4b4b4b !important;\\n  color: #fff !important;\\n}\\n.btn-dark:focus, .btn-dark:active, .btn-dark.active {\\n  color: #fff;\\n  background-color: #343434 !important;\\n}\\n.btn-dark:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #4b4b4b;\\n}\\n.btn-dark:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-dark {\\n  background-color: transparent;\\n  color: #4b4b4b;\\n}\\n.btn-flat-dark:hover {\\n  color: #4b4b4b;\\n}\\n.btn-flat-dark:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(75, 75, 75, 0.12);\\n}\\n.btn-flat-dark:active, .btn-flat-dark.active, .btn-flat-dark:focus {\\n  background-color: rgba(75, 75, 75, 0.2);\\n  color: #4b4b4b;\\n}\\n.btn-flat-dark.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234b4b4b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-dark {\\n  background-color: #4b4b4b;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-dark:hover:not(.disabled):not(:disabled) {\\n  background-color: #626262;\\n}\\n.btn-relief-dark:active, .btn-relief-dark.active, .btn-relief-dark:focus {\\n  background-color: #343434;\\n}\\n.btn-relief-dark:hover {\\n  color: #fff;\\n}\\n.btn-relief-dark:active, .btn-relief-dark.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-dark {\\n  border: 1px solid #4b4b4b !important;\\n  background-color: transparent;\\n  color: #4b4b4b;\\n}\\n.btn-outline-dark:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(75, 75, 75, 0.04);\\n  color: #4b4b4b;\\n}\\n.btn-outline-dark:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(75, 75, 75, 0.2);\\n  color: #4b4b4b;\\n}\\n.btn-outline-dark.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234b4b4b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-dark.dropdown-toggle {\\n  background-color: rgba(75, 75, 75, 0.2);\\n  color: #4b4b4b;\\n}\\n\\n.btn-outline-dark.waves-effect .waves-ripple,\\n.btn-flat-dark.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(75, 75, 75, 0.2) 0, rgba(75, 75, 75, 0.3) 40%, rgba(75, 75, 75, 0.4) 50%, rgba(75, 75, 75, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-dark {\\n  background-color: #4b4b4b;\\n}\\n\\n.modal.modal-dark .modal-header .modal-title {\\n  color: #4b4b4b;\\n}\\n.modal.modal-dark .modal-header .close {\\n  color: #4b4b4b !important;\\n}\\n\\n.progress-bar-dark {\\n  background-color: rgba(75, 75, 75, 0.12);\\n}\\n.progress-bar-dark .progress-bar {\\n  background-color: #4b4b4b;\\n}\\n\\n.timeline .timeline-point-dark {\\n  border-color: #4b4b4b !important;\\n}\\n.timeline .timeline-point-dark i,\\n.timeline .timeline-point-dark svg {\\n  stroke: #4b4b4b !important;\\n}\\n.timeline .timeline-point-dark.timeline-point-indicator {\\n  background-color: #4b4b4b !important;\\n}\\n.timeline .timeline-point-dark.timeline-point-indicator:before {\\n  background: rgba(75, 75, 75, 0.12) !important;\\n}\\n\\n.divider.divider-dark .divider-text:before, .divider.divider-dark .divider-text:after {\\n  border-color: #4b4b4b !important;\\n}\\n\\ninput:focus ~ .bg-dark {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #4b4b4b !important;\\n}\\n\\n.custom-control-dark .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-dark .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #4b4b4b;\\n  background-color: #4b4b4b;\\n}\\n.custom-control-dark.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-dark.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-dark.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-dark.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-dark.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-dark.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(75, 75, 75, 0.4) !important;\\n}\\n.custom-control-dark .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(75, 75, 75, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-dark .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #4b4b4b !important;\\n}\\n\\n.custom-switch-dark .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #4b4b4b !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #4b4b4b !important;\\n  border-color: #4b4b4b !important;\\n}\\n\\n.text-dark.text-darken-1 {\\n  color: #343434 !important;\\n}\\n\\n.bg-dark.bg-darken-1 {\\n  background-color: #343434 !important;\\n}\\n\\n.border-dark.border-darken-1 {\\n  border: 1px solid #343434 !important;\\n}\\n\\n.border-top-dark.border-top-darken-1 {\\n  border-top: 1px solid #343434 !important;\\n}\\n\\n.border-bottom-dark.border-bottom-darken-1 {\\n  border-bottom: 1px solid #343434 !important;\\n}\\n\\n.border-left-dark.border-left-darken-1 {\\n  border-left: 1px solid #343434 !important;\\n}\\n\\n.border-right-dark.border-right-darken-1 {\\n  border-right: 1px solid #343434 !important;\\n}\\n\\n.overlay-dark.overlay-darken-1 {\\n  background: #343434; /* The Fallback */\\n  background: rgba(52, 52, 52, 0.6);\\n}\\n\\n.text-dark.text-darken-2 {\\n  color: #1e1e1e !important;\\n}\\n\\n.bg-dark.bg-darken-2 {\\n  background-color: #1e1e1e !important;\\n}\\n\\n.border-dark.border-darken-2 {\\n  border: 1px solid #1e1e1e !important;\\n}\\n\\n.border-top-dark.border-top-darken-2 {\\n  border-top: 1px solid #1e1e1e !important;\\n}\\n\\n.border-bottom-dark.border-bottom-darken-2 {\\n  border-bottom: 1px solid #1e1e1e !important;\\n}\\n\\n.border-left-dark.border-left-darken-2 {\\n  border-left: 1px solid #1e1e1e !important;\\n}\\n\\n.border-right-dark.border-right-darken-2 {\\n  border-right: 1px solid #1e1e1e !important;\\n}\\n\\n.overlay-dark.overlay-darken-2 {\\n  background: #1e1e1e; /* The Fallback */\\n  background: rgba(30, 30, 30, 0.6);\\n}\\n\\n.text-dark.text-darken-3 {\\n  color: #626262 !important;\\n}\\n\\n.bg-dark.bg-darken-3 {\\n  background-color: #626262 !important;\\n}\\n\\n.border-dark.border-darken-3 {\\n  border: 1px solid #626262 !important;\\n}\\n\\n.border-top-dark.border-top-darken-3 {\\n  border-top: 1px solid #626262 !important;\\n}\\n\\n.border-bottom-dark.border-bottom-darken-3 {\\n  border-bottom: 1px solid #626262 !important;\\n}\\n\\n.border-left-dark.border-left-darken-3 {\\n  border-left: 1px solid #626262 !important;\\n}\\n\\n.border-right-dark.border-right-darken-3 {\\n  border-right: 1px solid #626262 !important;\\n}\\n\\n.overlay-dark.overlay-darken-3 {\\n  background: #626262; /* The Fallback */\\n  background: rgba(98, 98, 98, 0.6);\\n}\\n\\n.bg-light {\\n  background-color: #f6f6f6 !important;\\n}\\n.bg-light .card-header,\\n.bg-light .card-footer {\\n  background-color: transparent;\\n}\\n\\n.border-light {\\n  border: 1px solid #f6f6f6 !important;\\n}\\n\\n.border-top-light {\\n  border-top: 1px solid #f6f6f6;\\n}\\n\\n.border-bottom-light {\\n  border-bottom: 1px solid #f6f6f6;\\n}\\n\\n.border-left-light {\\n  border-left: 1px solid #f6f6f6;\\n}\\n\\n.border-right-light {\\n  border-right: 1px solid #f6f6f6;\\n}\\n\\n.bg-light.badge-glow,\\n.border-light.badge-glow,\\n.badge-light.badge-glow {\\n  box-shadow: 0px 0px 10px #f6f6f6;\\n}\\n\\n.overlay-light {\\n  background: #f6f6f6; /* The Fallback */\\n  background: rgba(246, 246, 246, 0.6);\\n}\\n\\ninput:focus ~ .bg-light {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #f6f6f6 !important;\\n}\\n\\n.text-primary.text-lighten-5 {\\n  color: #5390ee !important;\\n}\\n\\n.bg-primary.bg-lighten-5 {\\n  background-color: #5390ee !important;\\n}\\n\\n.border-primary.border-lighten-5 {\\n  border: 1px solid #5390ee !important;\\n}\\n\\n.border-top-primary.border-top-lighten-5 {\\n  border-top: 1px solid #5390ee !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-5 {\\n  border-bottom: 1px solid #5390ee !important;\\n}\\n\\n.border-left-primary.border-left-lighten-5 {\\n  border-left: 1px solid #5390ee !important;\\n}\\n\\n.border-right-primary.border-right-lighten-5 {\\n  border-right: 1px solid #5390ee !important;\\n}\\n\\n.overlay-primary.overlay-lighten-5 {\\n  background: #5390ee; /* The Fallback */\\n  background: rgba(83, 144, 238, 0.6);\\n}\\n\\n.text-primary.text-lighten-4 {\\n  color: #3c81ec !important;\\n}\\n\\n.bg-primary.bg-lighten-4 {\\n  background-color: #3c81ec !important;\\n}\\n\\n.border-primary.border-lighten-4 {\\n  border: 1px solid #3c81ec !important;\\n}\\n\\n.border-top-primary.border-top-lighten-4 {\\n  border-top: 1px solid #3c81ec !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-4 {\\n  border-bottom: 1px solid #3c81ec !important;\\n}\\n\\n.border-left-primary.border-left-lighten-4 {\\n  border-left: 1px solid #3c81ec !important;\\n}\\n\\n.border-right-primary.border-right-lighten-4 {\\n  border-right: 1px solid #3c81ec !important;\\n}\\n\\n.overlay-primary.overlay-lighten-4 {\\n  background: #3c81ec; /* The Fallback */\\n  background: rgba(60, 129, 236, 0.6);\\n}\\n\\n.text-primary.text-lighten-3 {\\n  color: #2472ea !important;\\n}\\n\\n.bg-primary.bg-lighten-3 {\\n  background-color: #2472ea !important;\\n}\\n\\n.border-primary.border-lighten-3 {\\n  border: 1px solid #2472ea !important;\\n}\\n\\n.border-top-primary.border-top-lighten-3 {\\n  border-top: 1px solid #2472ea !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-3 {\\n  border-bottom: 1px solid #2472ea !important;\\n}\\n\\n.border-left-primary.border-left-lighten-3 {\\n  border-left: 1px solid #2472ea !important;\\n}\\n\\n.border-right-primary.border-right-lighten-3 {\\n  border-right: 1px solid #2472ea !important;\\n}\\n\\n.overlay-primary.overlay-lighten-3 {\\n  background: #2472ea; /* The Fallback */\\n  background: rgba(36, 114, 234, 0.6);\\n}\\n\\n.text-primary.text-lighten-2 {\\n  color: #1565e0 !important;\\n}\\n\\n.bg-primary.bg-lighten-2 {\\n  background-color: #1565e0 !important;\\n}\\n\\n.border-primary.border-lighten-2 {\\n  border: 1px solid #1565e0 !important;\\n}\\n\\n.border-top-primary.border-top-lighten-2 {\\n  border-top: 1px solid #1565e0 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-2 {\\n  border-bottom: 1px solid #1565e0 !important;\\n}\\n\\n.border-left-primary.border-left-lighten-2 {\\n  border-left: 1px solid #1565e0 !important;\\n}\\n\\n.border-right-primary.border-right-lighten-2 {\\n  border-right: 1px solid #1565e0 !important;\\n}\\n\\n.overlay-primary.overlay-lighten-2 {\\n  background: #1565e0; /* The Fallback */\\n  background: rgba(21, 101, 224, 0.6);\\n}\\n\\n.text-primary.text-lighten-1 {\\n  color: #135bc8 !important;\\n}\\n\\n.bg-primary.bg-lighten-1 {\\n  background-color: #135bc8 !important;\\n}\\n\\n.border-primary.border-lighten-1 {\\n  border: 1px solid #135bc8 !important;\\n}\\n\\n.border-top-primary.border-top-lighten-1 {\\n  border-top: 1px solid #135bc8 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-lighten-1 {\\n  border-bottom: 1px solid #135bc8 !important;\\n}\\n\\n.border-left-primary.border-left-lighten-1 {\\n  border-left: 1px solid #135bc8 !important;\\n}\\n\\n.border-right-primary.border-right-lighten-1 {\\n  border-right: 1px solid #135bc8 !important;\\n}\\n\\n.overlay-primary.overlay-lighten-1 {\\n  background: #135bc8; /* The Fallback */\\n  background: rgba(19, 91, 200, 0.6);\\n}\\n\\n.bg-primary {\\n  background-color: #1150b1 !important;\\n}\\n.bg-primary .card-header,\\n.bg-primary .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-primary {\\n  background: rgba(17, 80, 177, 0.12) !important;\\n  color: #1150b1 !important;\\n}\\n.alert-primary .alert-heading {\\n  box-shadow: rgba(17, 80, 177, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-primary .alert-link {\\n  color: #0f459a !important;\\n}\\n.alert-primary .close {\\n  color: #1150b1 !important;\\n}\\n\\n.bg-light-primary {\\n  background: rgba(17, 80, 177, 0.12) !important;\\n  color: #1150b1 !important;\\n}\\n.bg-light-primary.fc-h-event, .bg-light-primary.fc-v-event {\\n  border-color: rgba(17, 80, 177, 0.1);\\n}\\n.bg-light-primary .fc-list-event-dot,\\n.bg-light-primary .fc-daygrid-event-dot {\\n  border-color: #1150b1 !important;\\n}\\n.bg-light-primary.fc-list-event:hover td {\\n  background: rgba(17, 80, 177, 0.1) !important;\\n}\\n.bg-light-primary.fc-list-event .fc-list-event-title {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-primary {\\n  color: #1150b1 !important;\\n}\\n\\n.border-primary {\\n  border: 1px solid #1150b1 !important;\\n}\\n\\n.border-top-primary {\\n  border-top: 1px solid #1150b1;\\n}\\n\\n.border-bottom-primary {\\n  border-bottom: 1px solid #1150b1;\\n}\\n\\n.border-left-primary {\\n  border-left: 1px solid #1150b1;\\n}\\n\\n.border-right-primary {\\n  border-right: 1px solid #1150b1;\\n}\\n\\n.bg-primary.badge-glow,\\n.border-primary.badge-glow,\\n.badge-primary.badge-glow {\\n  box-shadow: 0px 0px 10px #1150b1;\\n}\\n\\n.badge.badge-light-primary {\\n  background-color: rgba(17, 80, 177, 0.12);\\n  color: #1150b1 !important;\\n}\\n\\n.overlay-primary {\\n  background: #1150b1; /* The Fallback */\\n  background: rgba(17, 80, 177, 0.6);\\n}\\n\\n.btn-primary {\\n  border-color: #1150b1 !important;\\n  background-color: #1150b1 !important;\\n  color: #fff !important;\\n}\\n.btn-primary:focus, .btn-primary:active, .btn-primary.active {\\n  color: #fff;\\n  background-color: #0f459a !important;\\n}\\n.btn-primary:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #1150b1;\\n}\\n.btn-primary:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-primary {\\n  background-color: transparent;\\n  color: #1150b1;\\n}\\n.btn-flat-primary:hover {\\n  color: #1150b1;\\n}\\n.btn-flat-primary:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(17, 80, 177, 0.12);\\n}\\n.btn-flat-primary:active, .btn-flat-primary.active, .btn-flat-primary:focus {\\n  background-color: rgba(17, 80, 177, 0.2);\\n  color: #1150b1;\\n}\\n.btn-flat-primary.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-primary {\\n  background-color: #1150b1;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-primary:hover:not(.disabled):not(:disabled) {\\n  background-color: #135bc8;\\n}\\n.btn-relief-primary:active, .btn-relief-primary.active, .btn-relief-primary:focus {\\n  background-color: #0f459a;\\n}\\n.btn-relief-primary:hover {\\n  color: #fff;\\n}\\n.btn-relief-primary:active, .btn-relief-primary.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-primary {\\n  border: 1px solid #1150b1 !important;\\n  background-color: transparent;\\n  color: #1150b1;\\n}\\n.btn-outline-primary:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(17, 80, 177, 0.04);\\n  color: #1150b1;\\n}\\n.btn-outline-primary:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(17, 80, 177, 0.2);\\n  color: #1150b1;\\n}\\n.btn-outline-primary.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-primary.dropdown-toggle {\\n  background-color: rgba(17, 80, 177, 0.2);\\n  color: #1150b1;\\n}\\n\\n.btn-outline-primary.waves-effect .waves-ripple,\\n.btn-flat-primary.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(17, 80, 177, 0.2) 0, rgba(17, 80, 177, 0.3) 40%, rgba(17, 80, 177, 0.4) 50%, rgba(17, 80, 177, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-primary {\\n  background-color: #1150b1;\\n}\\n\\n.modal.modal-primary .modal-header .modal-title {\\n  color: #1150b1;\\n}\\n.modal.modal-primary .modal-header .close {\\n  color: #1150b1 !important;\\n}\\n\\n.pagination-primary .page-item.active .page-link {\\n  background: #1150b1 !important;\\n  color: #fff;\\n}\\n.pagination-primary .page-item.active .page-link:hover {\\n  color: #fff;\\n}\\n.pagination-primary .page-item .page-link:hover {\\n  color: #1150b1;\\n}\\n.pagination-primary .page-item.prev-item .page-link:hover, .pagination-primary .page-item.next-item .page-link:hover {\\n  background: #1150b1;\\n  color: #fff;\\n}\\n.pagination-primary .page-item.next-item .page-link:active:after, .pagination-primary .page-item.next-item .page-link:hover:after, .pagination-primary .page-item.next .page-link:active:after, .pagination-primary .page-item.next .page-link:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-primary .page-item.prev-item .page-link:active:before, .pagination-primary .page-item.prev-item .page-link:hover:before, .pagination-primary .page-item.prev .page-link:active:before, .pagination-primary .page-item.prev .page-link:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231150b1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-primary .nav-item .nav-link.active {\\n  color: #fff;\\n  background-color: #1150b1 !important;\\n  border-color: #1150b1;\\n  box-shadow: 0 4px 18px -4px rgba(17, 80, 177, 0.65);\\n}\\n\\n.progress-bar-primary {\\n  background-color: rgba(17, 80, 177, 0.12);\\n}\\n.progress-bar-primary .progress-bar {\\n  background-color: #1150b1;\\n}\\n\\n.timeline .timeline-point-primary {\\n  border-color: #1150b1 !important;\\n}\\n.timeline .timeline-point-primary i,\\n.timeline .timeline-point-primary svg {\\n  stroke: #1150b1 !important;\\n}\\n.timeline .timeline-point-primary.timeline-point-indicator {\\n  background-color: #1150b1 !important;\\n}\\n.timeline .timeline-point-primary.timeline-point-indicator:before {\\n  background: rgba(17, 80, 177, 0.12) !important;\\n}\\n\\n.divider.divider-primary .divider-text:before, .divider.divider-primary .divider-text:after {\\n  border-color: #1150b1 !important;\\n}\\n\\ninput:focus ~ .bg-primary {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #1150b1 !important;\\n}\\n\\n.custom-control-primary .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-primary .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #1150b1;\\n  background-color: #1150b1;\\n}\\n.custom-control-primary.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-primary.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-primary.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-primary.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-primary.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-primary.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(17, 80, 177, 0.4) !important;\\n}\\n.custom-control-primary .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(17, 80, 177, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-primary .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #1150b1 !important;\\n}\\n\\n.custom-switch-primary .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #1150b1 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #1150b1 !important;\\n  border-color: #1150b1 !important;\\n}\\n\\n.text-primary.text-darken-1 {\\n  color: #0f459a !important;\\n}\\n\\n.bg-primary.bg-darken-1 {\\n  background-color: #0f459a !important;\\n}\\n\\n.border-primary.border-darken-1 {\\n  border: 1px solid #0f459a !important;\\n}\\n\\n.border-top-primary.border-top-darken-1 {\\n  border-top: 1px solid #0f459a !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-1 {\\n  border-bottom: 1px solid #0f459a !important;\\n}\\n\\n.border-left-primary.border-left-darken-1 {\\n  border-left: 1px solid #0f459a !important;\\n}\\n\\n.border-right-primary.border-right-darken-1 {\\n  border-right: 1px solid #0f459a !important;\\n}\\n\\n.overlay-primary.overlay-darken-1 {\\n  background: #0f459a; /* The Fallback */\\n  background: rgba(15, 69, 154, 0.6);\\n}\\n\\n.text-primary.text-darken-2 {\\n  color: #0d3b82 !important;\\n}\\n\\n.bg-primary.bg-darken-2 {\\n  background-color: #0d3b82 !important;\\n}\\n\\n.border-primary.border-darken-2 {\\n  border: 1px solid #0d3b82 !important;\\n}\\n\\n.border-top-primary.border-top-darken-2 {\\n  border-top: 1px solid #0d3b82 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-2 {\\n  border-bottom: 1px solid #0d3b82 !important;\\n}\\n\\n.border-left-primary.border-left-darken-2 {\\n  border-left: 1px solid #0d3b82 !important;\\n}\\n\\n.border-right-primary.border-right-darken-2 {\\n  border-right: 1px solid #0d3b82 !important;\\n}\\n\\n.overlay-primary.overlay-darken-2 {\\n  background: #0d3b82; /* The Fallback */\\n  background: rgba(13, 59, 130, 0.6);\\n}\\n\\n.text-primary.text-darken-3 {\\n  color: #0a306b !important;\\n}\\n\\n.bg-primary.bg-darken-3 {\\n  background-color: #0a306b !important;\\n}\\n\\n.border-primary.border-darken-3 {\\n  border: 1px solid #0a306b !important;\\n}\\n\\n.border-top-primary.border-top-darken-3 {\\n  border-top: 1px solid #0a306b !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-3 {\\n  border-bottom: 1px solid #0a306b !important;\\n}\\n\\n.border-left-primary.border-left-darken-3 {\\n  border-left: 1px solid #0a306b !important;\\n}\\n\\n.border-right-primary.border-right-darken-3 {\\n  border-right: 1px solid #0a306b !important;\\n}\\n\\n.overlay-primary.overlay-darken-3 {\\n  background: #0a306b; /* The Fallback */\\n  background: rgba(10, 48, 107, 0.6);\\n}\\n\\n.text-primary.text-darken-4 {\\n  color: #082654 !important;\\n}\\n\\n.bg-primary.bg-darken-4 {\\n  background-color: #082654 !important;\\n}\\n\\n.border-primary.border-darken-4 {\\n  border: 1px solid #082654 !important;\\n}\\n\\n.border-top-primary.border-top-darken-4 {\\n  border-top: 1px solid #082654 !important;\\n}\\n\\n.border-bottom-primary.border-bottom-darken-4 {\\n  border-bottom: 1px solid #082654 !important;\\n}\\n\\n.border-left-primary.border-left-darken-4 {\\n  border-left: 1px solid #082654 !important;\\n}\\n\\n.border-right-primary.border-right-darken-4 {\\n  border-right: 1px solid #082654 !important;\\n}\\n\\n.overlay-primary.overlay-darken-4 {\\n  background: #082654; /* The Fallback */\\n  background: rgba(8, 38, 84, 0.6);\\n}\\n\\n.text-primary.text-accent-1 {\\n  color: #bdfdff !important;\\n}\\n\\n.bg-primary.bg-accent-1 {\\n  background-color: #bdfdff !important;\\n}\\n\\n.border-primary.border-accent-1 {\\n  border: 1px solid #bdfdff !important;\\n}\\n\\n.border-top-primary.border-top-accent-1 {\\n  border-top: 1px solid #bdfdff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-1 {\\n  border-bottom: 1px solid #bdfdff !important;\\n}\\n\\n.border-left-primary.border-left-accent-1 {\\n  border-left: 1px solid #bdfdff !important;\\n}\\n\\n.border-right-primary.border-right-accent-1 {\\n  border-right: 1px solid #bdfdff !important;\\n}\\n\\n.overlay-primary.overlay-accent-1 {\\n  background: #bdfdff; /* The Fallback */\\n  background: rgba(189, 253, 255, 0.6);\\n}\\n\\n.text-primary.text-accent-2 {\\n  color: #8afbff !important;\\n}\\n\\n.bg-primary.bg-accent-2 {\\n  background-color: #8afbff !important;\\n}\\n\\n.border-primary.border-accent-2 {\\n  border: 1px solid #8afbff !important;\\n}\\n\\n.border-top-primary.border-top-accent-2 {\\n  border-top: 1px solid #8afbff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-2 {\\n  border-bottom: 1px solid #8afbff !important;\\n}\\n\\n.border-left-primary.border-left-accent-2 {\\n  border-left: 1px solid #8afbff !important;\\n}\\n\\n.border-right-primary.border-right-accent-2 {\\n  border-right: 1px solid #8afbff !important;\\n}\\n\\n.overlay-primary.overlay-accent-2 {\\n  background: #8afbff; /* The Fallback */\\n  background: rgba(138, 251, 255, 0.6);\\n}\\n\\n.text-primary.text-accent-3 {\\n  color: #57faff !important;\\n}\\n\\n.bg-primary.bg-accent-3 {\\n  background-color: #57faff !important;\\n}\\n\\n.border-primary.border-accent-3 {\\n  border: 1px solid #57faff !important;\\n}\\n\\n.border-top-primary.border-top-accent-3 {\\n  border-top: 1px solid #57faff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-3 {\\n  border-bottom: 1px solid #57faff !important;\\n}\\n\\n.border-left-primary.border-left-accent-3 {\\n  border-left: 1px solid #57faff !important;\\n}\\n\\n.border-right-primary.border-right-accent-3 {\\n  border-right: 1px solid #57faff !important;\\n}\\n\\n.overlay-primary.overlay-accent-3 {\\n  background: #57faff; /* The Fallback */\\n  background: rgba(87, 250, 255, 0.6);\\n}\\n\\n.text-primary.text-accent-4 {\\n  color: #3df9ff !important;\\n}\\n\\n.bg-primary.bg-accent-4 {\\n  background-color: #3df9ff !important;\\n}\\n\\n.border-primary.border-accent-4 {\\n  border: 1px solid #3df9ff !important;\\n}\\n\\n.border-top-primary.border-top-accent-4 {\\n  border-top: 1px solid #3df9ff !important;\\n}\\n\\n.border-bottom-primary.border-bottom-accent-4 {\\n  border-bottom: 1px solid #3df9ff !important;\\n}\\n\\n.border-left-primary.border-left-accent-4 {\\n  border-left: 1px solid #3df9ff !important;\\n}\\n\\n.border-right-primary.border-right-accent-4 {\\n  border-right: 1px solid #3df9ff !important;\\n}\\n\\n.overlay-primary.overlay-accent-4 {\\n  background: #3df9ff; /* The Fallback */\\n  background: rgba(61, 249, 255, 0.6);\\n}\\n\\n.text-secondary.text-lighten-5 {\\n  color: #c4c6c8 !important;\\n}\\n\\n.bg-secondary.bg-lighten-5 {\\n  background-color: #c4c6c8 !important;\\n}\\n\\n.border-secondary.border-lighten-5 {\\n  border: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-5 {\\n  border-top: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-5 {\\n  border-bottom: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-5 {\\n  border-left: 1px solid #c4c6c8 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-5 {\\n  border-right: 1px solid #c4c6c8 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-5 {\\n  background: #c4c6c8; /* The Fallback */\\n  background: rgba(196, 198, 200, 0.6);\\n}\\n\\n.text-secondary.text-lighten-4 {\\n  color: #b7b9bc !important;\\n}\\n\\n.bg-secondary.bg-lighten-4 {\\n  background-color: #b7b9bc !important;\\n}\\n\\n.border-secondary.border-lighten-4 {\\n  border: 1px solid #b7b9bc !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-4 {\\n  border-top: 1px solid #b7b9bc !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-4 {\\n  border-bottom: 1px solid #b7b9bc !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-4 {\\n  border-left: 1px solid #b7b9bc !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-4 {\\n  border-right: 1px solid #b7b9bc !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-4 {\\n  background: #b7b9bc; /* The Fallback */\\n  background: rgba(183, 185, 188, 0.6);\\n}\\n\\n.text-secondary.text-lighten-3 {\\n  color: #aaacb0 !important;\\n}\\n\\n.bg-secondary.bg-lighten-3 {\\n  background-color: #aaacb0 !important;\\n}\\n\\n.border-secondary.border-lighten-3 {\\n  border: 1px solid #aaacb0 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-3 {\\n  border-top: 1px solid #aaacb0 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-3 {\\n  border-bottom: 1px solid #aaacb0 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-3 {\\n  border-left: 1px solid #aaacb0 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-3 {\\n  border-right: 1px solid #aaacb0 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-3 {\\n  background: #aaacb0; /* The Fallback */\\n  background: rgba(170, 172, 176, 0.6);\\n}\\n\\n.text-secondary.text-lighten-2 {\\n  color: #9ca0a4 !important;\\n}\\n\\n.bg-secondary.bg-lighten-2 {\\n  background-color: #9ca0a4 !important;\\n}\\n\\n.border-secondary.border-lighten-2 {\\n  border: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-2 {\\n  border-top: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-2 {\\n  border-bottom: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-2 {\\n  border-left: 1px solid #9ca0a4 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-2 {\\n  border-right: 1px solid #9ca0a4 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-2 {\\n  background: #9ca0a4; /* The Fallback */\\n  background: rgba(156, 160, 164, 0.6);\\n}\\n\\n.text-secondary.text-lighten-1 {\\n  color: #8f9397 !important;\\n}\\n\\n.bg-secondary.bg-lighten-1 {\\n  background-color: #8f9397 !important;\\n}\\n\\n.border-secondary.border-lighten-1 {\\n  border: 1px solid #8f9397 !important;\\n}\\n\\n.border-top-secondary.border-top-lighten-1 {\\n  border-top: 1px solid #8f9397 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-lighten-1 {\\n  border-bottom: 1px solid #8f9397 !important;\\n}\\n\\n.border-left-secondary.border-left-lighten-1 {\\n  border-left: 1px solid #8f9397 !important;\\n}\\n\\n.border-right-secondary.border-right-lighten-1 {\\n  border-right: 1px solid #8f9397 !important;\\n}\\n\\n.overlay-secondary.overlay-lighten-1 {\\n  background: #8f9397; /* The Fallback */\\n  background: rgba(143, 147, 151, 0.6);\\n}\\n\\n.bg-secondary {\\n  background-color: #82868b !important;\\n}\\n.bg-secondary .card-header,\\n.bg-secondary .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-secondary {\\n  background: rgba(130, 134, 139, 0.12) !important;\\n  color: #82868b !important;\\n}\\n.alert-secondary .alert-heading {\\n  box-shadow: rgba(130, 134, 139, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-secondary .alert-link {\\n  color: #75797e !important;\\n}\\n.alert-secondary .close {\\n  color: #82868b !important;\\n}\\n\\n.bg-light-secondary {\\n  background: rgba(130, 134, 139, 0.12) !important;\\n  color: #82868b !important;\\n}\\n.bg-light-secondary.fc-h-event, .bg-light-secondary.fc-v-event {\\n  border-color: rgba(130, 134, 139, 0.1);\\n}\\n.bg-light-secondary .fc-list-event-dot,\\n.bg-light-secondary .fc-daygrid-event-dot {\\n  border-color: #82868b !important;\\n}\\n.bg-light-secondary.fc-list-event:hover td {\\n  background: rgba(130, 134, 139, 0.1) !important;\\n}\\n.bg-light-secondary.fc-list-event .fc-list-event-title {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-secondary {\\n  color: #82868b !important;\\n}\\n\\n.border-secondary {\\n  border: 1px solid #82868b !important;\\n}\\n\\n.border-top-secondary {\\n  border-top: 1px solid #82868b;\\n}\\n\\n.border-bottom-secondary {\\n  border-bottom: 1px solid #82868b;\\n}\\n\\n.border-left-secondary {\\n  border-left: 1px solid #82868b;\\n}\\n\\n.border-right-secondary {\\n  border-right: 1px solid #82868b;\\n}\\n\\n.bg-secondary.badge-glow,\\n.border-secondary.badge-glow,\\n.badge-secondary.badge-glow {\\n  box-shadow: 0px 0px 10px #82868b;\\n}\\n\\n.badge.badge-light-secondary {\\n  background-color: rgba(130, 134, 139, 0.12);\\n  color: #82868b !important;\\n}\\n\\n.overlay-secondary {\\n  background: #82868b; /* The Fallback */\\n  background: rgba(130, 134, 139, 0.6);\\n}\\n\\n.btn-secondary {\\n  border-color: #82868b !important;\\n  background-color: #82868b !important;\\n  color: #fff !important;\\n}\\n.btn-secondary:focus, .btn-secondary:active, .btn-secondary.active {\\n  color: #fff;\\n  background-color: #75797e !important;\\n}\\n.btn-secondary:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #82868b;\\n}\\n.btn-secondary:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-secondary {\\n  background-color: transparent;\\n  color: #82868b;\\n}\\n.btn-flat-secondary:hover {\\n  color: #82868b;\\n}\\n.btn-flat-secondary:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(130, 134, 139, 0.12);\\n}\\n.btn-flat-secondary:active, .btn-flat-secondary.active, .btn-flat-secondary:focus {\\n  background-color: rgba(130, 134, 139, 0.2);\\n  color: #82868b;\\n}\\n.btn-flat-secondary.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-secondary {\\n  background-color: #82868b;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-secondary:hover:not(.disabled):not(:disabled) {\\n  background-color: #8f9397;\\n}\\n.btn-relief-secondary:active, .btn-relief-secondary.active, .btn-relief-secondary:focus {\\n  background-color: #75797e;\\n}\\n.btn-relief-secondary:hover {\\n  color: #fff;\\n}\\n.btn-relief-secondary:active, .btn-relief-secondary.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-secondary {\\n  border: 1px solid #82868b !important;\\n  background-color: transparent;\\n  color: #82868b;\\n}\\n.btn-outline-secondary:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(130, 134, 139, 0.04);\\n  color: #82868b;\\n}\\n.btn-outline-secondary:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(130, 134, 139, 0.2);\\n  color: #82868b;\\n}\\n.btn-outline-secondary.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-secondary.dropdown-toggle {\\n  background-color: rgba(130, 134, 139, 0.2);\\n  color: #82868b;\\n}\\n\\n.btn-outline-secondary.waves-effect .waves-ripple,\\n.btn-flat-secondary.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(130, 134, 139, 0.2) 0, rgba(130, 134, 139, 0.3) 40%, rgba(130, 134, 139, 0.4) 50%, rgba(130, 134, 139, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-secondary {\\n  background-color: #82868b;\\n}\\n\\n.modal.modal-secondary .modal-header .modal-title {\\n  color: #82868b;\\n}\\n.modal.modal-secondary .modal-header .close {\\n  color: #82868b !important;\\n}\\n\\n.pagination-secondary .page-item.active .page-link {\\n  background: #82868b !important;\\n  color: #fff;\\n}\\n.pagination-secondary .page-item.active .page-link:hover {\\n  color: #fff;\\n}\\n.pagination-secondary .page-item .page-link:hover {\\n  color: #82868b;\\n}\\n.pagination-secondary .page-item.prev-item .page-link:hover, .pagination-secondary .page-item.next-item .page-link:hover {\\n  background: #82868b;\\n  color: #fff;\\n}\\n.pagination-secondary .page-item.next-item .page-link:active:after, .pagination-secondary .page-item.next-item .page-link:hover:after, .pagination-secondary .page-item.next .page-link:active:after, .pagination-secondary .page-item.next .page-link:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-secondary .page-item.prev-item .page-link:active:before, .pagination-secondary .page-item.prev-item .page-link:hover:before, .pagination-secondary .page-item.prev .page-link:active:before, .pagination-secondary .page-item.prev .page-link:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2382868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-secondary .nav-item .nav-link.active {\\n  color: #fff;\\n  background-color: #82868b !important;\\n  border-color: #82868b;\\n  box-shadow: 0 4px 18px -4px rgba(130, 134, 139, 0.65);\\n}\\n\\n.progress-bar-secondary {\\n  background-color: rgba(130, 134, 139, 0.12);\\n}\\n.progress-bar-secondary .progress-bar {\\n  background-color: #82868b;\\n}\\n\\n.timeline .timeline-point-secondary {\\n  border-color: #82868b !important;\\n}\\n.timeline .timeline-point-secondary i,\\n.timeline .timeline-point-secondary svg {\\n  stroke: #82868b !important;\\n}\\n.timeline .timeline-point-secondary.timeline-point-indicator {\\n  background-color: #82868b !important;\\n}\\n.timeline .timeline-point-secondary.timeline-point-indicator:before {\\n  background: rgba(130, 134, 139, 0.12) !important;\\n}\\n\\n.divider.divider-secondary .divider-text:before, .divider.divider-secondary .divider-text:after {\\n  border-color: #82868b !important;\\n}\\n\\ninput:focus ~ .bg-secondary {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #82868b !important;\\n}\\n\\n.custom-control-secondary .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-secondary .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #82868b;\\n  background-color: #82868b;\\n}\\n.custom-control-secondary.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-secondary.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-secondary.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-secondary.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-secondary.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-secondary.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(130, 134, 139, 0.4) !important;\\n}\\n.custom-control-secondary .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(130, 134, 139, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-secondary .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #82868b !important;\\n}\\n\\n.custom-switch-secondary .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #82868b !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #82868b !important;\\n  border-color: #82868b !important;\\n}\\n\\n.text-secondary.text-darken-1 {\\n  color: #75797e !important;\\n}\\n\\n.bg-secondary.bg-darken-1 {\\n  background-color: #75797e !important;\\n}\\n\\n.border-secondary.border-darken-1 {\\n  border: 1px solid #75797e !important;\\n}\\n\\n.border-top-secondary.border-top-darken-1 {\\n  border-top: 1px solid #75797e !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-1 {\\n  border-bottom: 1px solid #75797e !important;\\n}\\n\\n.border-left-secondary.border-left-darken-1 {\\n  border-left: 1px solid #75797e !important;\\n}\\n\\n.border-right-secondary.border-right-darken-1 {\\n  border-right: 1px solid #75797e !important;\\n}\\n\\n.overlay-secondary.overlay-darken-1 {\\n  background: #75797e; /* The Fallback */\\n  background: rgba(117, 121, 126, 0.6);\\n}\\n\\n.text-secondary.text-darken-2 {\\n  color: #696d71 !important;\\n}\\n\\n.bg-secondary.bg-darken-2 {\\n  background-color: #696d71 !important;\\n}\\n\\n.border-secondary.border-darken-2 {\\n  border: 1px solid #696d71 !important;\\n}\\n\\n.border-top-secondary.border-top-darken-2 {\\n  border-top: 1px solid #696d71 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-2 {\\n  border-bottom: 1px solid #696d71 !important;\\n}\\n\\n.border-left-secondary.border-left-darken-2 {\\n  border-left: 1px solid #696d71 !important;\\n}\\n\\n.border-right-secondary.border-right-darken-2 {\\n  border-right: 1px solid #696d71 !important;\\n}\\n\\n.overlay-secondary.overlay-darken-2 {\\n  background: #696d71; /* The Fallback */\\n  background: rgba(105, 109, 113, 0.6);\\n}\\n\\n.text-secondary.text-darken-3 {\\n  color: #5d6064 !important;\\n}\\n\\n.bg-secondary.bg-darken-3 {\\n  background-color: #5d6064 !important;\\n}\\n\\n.border-secondary.border-darken-3 {\\n  border: 1px solid #5d6064 !important;\\n}\\n\\n.border-top-secondary.border-top-darken-3 {\\n  border-top: 1px solid #5d6064 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-3 {\\n  border-bottom: 1px solid #5d6064 !important;\\n}\\n\\n.border-left-secondary.border-left-darken-3 {\\n  border-left: 1px solid #5d6064 !important;\\n}\\n\\n.border-right-secondary.border-right-darken-3 {\\n  border-right: 1px solid #5d6064 !important;\\n}\\n\\n.overlay-secondary.overlay-darken-3 {\\n  background: #5d6064; /* The Fallback */\\n  background: rgba(93, 96, 100, 0.6);\\n}\\n\\n.text-secondary.text-darken-4 {\\n  color: #505357 !important;\\n}\\n\\n.bg-secondary.bg-darken-4 {\\n  background-color: #505357 !important;\\n}\\n\\n.border-secondary.border-darken-4 {\\n  border: 1px solid #505357 !important;\\n}\\n\\n.border-top-secondary.border-top-darken-4 {\\n  border-top: 1px solid #505357 !important;\\n}\\n\\n.border-bottom-secondary.border-bottom-darken-4 {\\n  border-bottom: 1px solid #505357 !important;\\n}\\n\\n.border-left-secondary.border-left-darken-4 {\\n  border-left: 1px solid #505357 !important;\\n}\\n\\n.border-right-secondary.border-right-darken-4 {\\n  border-right: 1px solid #505357 !important;\\n}\\n\\n.overlay-secondary.overlay-darken-4 {\\n  background: #505357; /* The Fallback */\\n  background: rgba(80, 83, 87, 0.6);\\n}\\n\\n.text-success.text-lighten-5 {\\n  color: #88e7b2 !important;\\n}\\n\\n.bg-success.bg-lighten-5 {\\n  background-color: #88e7b2 !important;\\n}\\n\\n.border-success.border-lighten-5 {\\n  border: 1px solid #88e7b2 !important;\\n}\\n\\n.border-top-success.border-top-lighten-5 {\\n  border-top: 1px solid #88e7b2 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-5 {\\n  border-bottom: 1px solid #88e7b2 !important;\\n}\\n\\n.border-left-success.border-left-lighten-5 {\\n  border-left: 1px solid #88e7b2 !important;\\n}\\n\\n.border-right-success.border-right-lighten-5 {\\n  border-right: 1px solid #88e7b2 !important;\\n}\\n\\n.overlay-success.overlay-lighten-5 {\\n  background: #88e7b2; /* The Fallback */\\n  background: rgba(136, 231, 178, 0.6);\\n}\\n\\n.text-success.text-lighten-4 {\\n  color: #72e3a4 !important;\\n}\\n\\n.bg-success.bg-lighten-4 {\\n  background-color: #72e3a4 !important;\\n}\\n\\n.border-success.border-lighten-4 {\\n  border: 1px solid #72e3a4 !important;\\n}\\n\\n.border-top-success.border-top-lighten-4 {\\n  border-top: 1px solid #72e3a4 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-4 {\\n  border-bottom: 1px solid #72e3a4 !important;\\n}\\n\\n.border-left-success.border-left-lighten-4 {\\n  border-left: 1px solid #72e3a4 !important;\\n}\\n\\n.border-right-success.border-right-lighten-4 {\\n  border-right: 1px solid #72e3a4 !important;\\n}\\n\\n.overlay-success.overlay-lighten-4 {\\n  background: #72e3a4; /* The Fallback */\\n  background: rgba(114, 227, 164, 0.6);\\n}\\n\\n.text-success.text-lighten-3 {\\n  color: #5dde97 !important;\\n}\\n\\n.bg-success.bg-lighten-3 {\\n  background-color: #5dde97 !important;\\n}\\n\\n.border-success.border-lighten-3 {\\n  border: 1px solid #5dde97 !important;\\n}\\n\\n.border-top-success.border-top-lighten-3 {\\n  border-top: 1px solid #5dde97 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-3 {\\n  border-bottom: 1px solid #5dde97 !important;\\n}\\n\\n.border-left-success.border-left-lighten-3 {\\n  border-left: 1px solid #5dde97 !important;\\n}\\n\\n.border-right-success.border-right-lighten-3 {\\n  border-right: 1px solid #5dde97 !important;\\n}\\n\\n.overlay-success.overlay-lighten-3 {\\n  background: #5dde97; /* The Fallback */\\n  background: rgba(93, 222, 151, 0.6);\\n}\\n\\n.text-success.text-lighten-2 {\\n  color: #48da89 !important;\\n}\\n\\n.bg-success.bg-lighten-2 {\\n  background-color: #48da89 !important;\\n}\\n\\n.border-success.border-lighten-2 {\\n  border: 1px solid #48da89 !important;\\n}\\n\\n.border-top-success.border-top-lighten-2 {\\n  border-top: 1px solid #48da89 !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-2 {\\n  border-bottom: 1px solid #48da89 !important;\\n}\\n\\n.border-left-success.border-left-lighten-2 {\\n  border-left: 1px solid #48da89 !important;\\n}\\n\\n.border-right-success.border-right-lighten-2 {\\n  border-right: 1px solid #48da89 !important;\\n}\\n\\n.overlay-success.overlay-lighten-2 {\\n  background: #48da89; /* The Fallback */\\n  background: rgba(72, 218, 137, 0.6);\\n}\\n\\n.text-success.text-lighten-1 {\\n  color: #33d67c !important;\\n}\\n\\n.bg-success.bg-lighten-1 {\\n  background-color: #33d67c !important;\\n}\\n\\n.border-success.border-lighten-1 {\\n  border: 1px solid #33d67c !important;\\n}\\n\\n.border-top-success.border-top-lighten-1 {\\n  border-top: 1px solid #33d67c !important;\\n}\\n\\n.border-bottom-success.border-bottom-lighten-1 {\\n  border-bottom: 1px solid #33d67c !important;\\n}\\n\\n.border-left-success.border-left-lighten-1 {\\n  border-left: 1px solid #33d67c !important;\\n}\\n\\n.border-right-success.border-right-lighten-1 {\\n  border-right: 1px solid #33d67c !important;\\n}\\n\\n.overlay-success.overlay-lighten-1 {\\n  background: #33d67c; /* The Fallback */\\n  background: rgba(51, 214, 124, 0.6);\\n}\\n\\n.bg-success {\\n  background-color: #28c76f !important;\\n}\\n.bg-success .card-header,\\n.bg-success .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-success {\\n  background: rgba(40, 199, 111, 0.12) !important;\\n  color: #28c76f !important;\\n}\\n.alert-success .alert-heading {\\n  box-shadow: rgba(40, 199, 111, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-success .alert-link {\\n  color: #24b263 !important;\\n}\\n.alert-success .close {\\n  color: #28c76f !important;\\n}\\n\\n.bg-light-success {\\n  background: rgba(40, 199, 111, 0.12) !important;\\n  color: #28c76f !important;\\n}\\n.bg-light-success.fc-h-event, .bg-light-success.fc-v-event {\\n  border-color: rgba(40, 199, 111, 0.1);\\n}\\n.bg-light-success .fc-list-event-dot,\\n.bg-light-success .fc-daygrid-event-dot {\\n  border-color: #28c76f !important;\\n}\\n.bg-light-success.fc-list-event:hover td {\\n  background: rgba(40, 199, 111, 0.1) !important;\\n}\\n.bg-light-success.fc-list-event .fc-list-event-title {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-success {\\n  color: #28c76f !important;\\n}\\n\\n.border-success {\\n  border: 1px solid #28c76f !important;\\n}\\n\\n.border-top-success {\\n  border-top: 1px solid #28c76f;\\n}\\n\\n.border-bottom-success {\\n  border-bottom: 1px solid #28c76f;\\n}\\n\\n.border-left-success {\\n  border-left: 1px solid #28c76f;\\n}\\n\\n.border-right-success {\\n  border-right: 1px solid #28c76f;\\n}\\n\\n.bg-success.badge-glow,\\n.border-success.badge-glow,\\n.badge-success.badge-glow {\\n  box-shadow: 0px 0px 10px #28c76f;\\n}\\n\\n.badge.badge-light-success {\\n  background-color: rgba(40, 199, 111, 0.12);\\n  color: #28c76f !important;\\n}\\n\\n.overlay-success {\\n  background: #28c76f; /* The Fallback */\\n  background: rgba(40, 199, 111, 0.6);\\n}\\n\\n.btn-success {\\n  border-color: #28c76f !important;\\n  background-color: #28c76f !important;\\n  color: #fff !important;\\n}\\n.btn-success:focus, .btn-success:active, .btn-success.active {\\n  color: #fff;\\n  background-color: #24b263 !important;\\n}\\n.btn-success:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #28c76f;\\n}\\n.btn-success:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-success {\\n  background-color: transparent;\\n  color: #28c76f;\\n}\\n.btn-flat-success:hover {\\n  color: #28c76f;\\n}\\n.btn-flat-success:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(40, 199, 111, 0.12);\\n}\\n.btn-flat-success:active, .btn-flat-success.active, .btn-flat-success:focus {\\n  background-color: rgba(40, 199, 111, 0.2);\\n  color: #28c76f;\\n}\\n.btn-flat-success.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-success {\\n  background-color: #28c76f;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-success:hover:not(.disabled):not(:disabled) {\\n  background-color: #33d67c;\\n}\\n.btn-relief-success:active, .btn-relief-success.active, .btn-relief-success:focus {\\n  background-color: #24b263;\\n}\\n.btn-relief-success:hover {\\n  color: #fff;\\n}\\n.btn-relief-success:active, .btn-relief-success.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-success {\\n  border: 1px solid #28c76f !important;\\n  background-color: transparent;\\n  color: #28c76f;\\n}\\n.btn-outline-success:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(40, 199, 111, 0.04);\\n  color: #28c76f;\\n}\\n.btn-outline-success:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(40, 199, 111, 0.2);\\n  color: #28c76f;\\n}\\n.btn-outline-success.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-success.dropdown-toggle {\\n  background-color: rgba(40, 199, 111, 0.2);\\n  color: #28c76f;\\n}\\n\\n.btn-outline-success.waves-effect .waves-ripple,\\n.btn-flat-success.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(40, 199, 111, 0.2) 0, rgba(40, 199, 111, 0.3) 40%, rgba(40, 199, 111, 0.4) 50%, rgba(40, 199, 111, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-success {\\n  background-color: #28c76f;\\n}\\n\\n.modal.modal-success .modal-header .modal-title {\\n  color: #28c76f;\\n}\\n.modal.modal-success .modal-header .close {\\n  color: #28c76f !important;\\n}\\n\\n.pagination-success .page-item.active .page-link {\\n  background: #28c76f !important;\\n  color: #fff;\\n}\\n.pagination-success .page-item.active .page-link:hover {\\n  color: #fff;\\n}\\n.pagination-success .page-item .page-link:hover {\\n  color: #28c76f;\\n}\\n.pagination-success .page-item.prev-item .page-link:hover, .pagination-success .page-item.next-item .page-link:hover {\\n  background: #28c76f;\\n  color: #fff;\\n}\\n.pagination-success .page-item.next-item .page-link:active:after, .pagination-success .page-item.next-item .page-link:hover:after, .pagination-success .page-item.next .page-link:active:after, .pagination-success .page-item.next .page-link:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-success .page-item.prev-item .page-link:active:before, .pagination-success .page-item.prev-item .page-link:hover:before, .pagination-success .page-item.prev .page-link:active:before, .pagination-success .page-item.prev .page-link:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2328c76f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-success .nav-item .nav-link.active {\\n  color: #fff;\\n  background-color: #28c76f !important;\\n  border-color: #28c76f;\\n  box-shadow: 0 4px 18px -4px rgba(40, 199, 111, 0.65);\\n}\\n\\n.progress-bar-success {\\n  background-color: rgba(40, 199, 111, 0.12);\\n}\\n.progress-bar-success .progress-bar {\\n  background-color: #28c76f;\\n}\\n\\n.timeline .timeline-point-success {\\n  border-color: #28c76f !important;\\n}\\n.timeline .timeline-point-success i,\\n.timeline .timeline-point-success svg {\\n  stroke: #28c76f !important;\\n}\\n.timeline .timeline-point-success.timeline-point-indicator {\\n  background-color: #28c76f !important;\\n}\\n.timeline .timeline-point-success.timeline-point-indicator:before {\\n  background: rgba(40, 199, 111, 0.12) !important;\\n}\\n\\n.divider.divider-success .divider-text:before, .divider.divider-success .divider-text:after {\\n  border-color: #28c76f !important;\\n}\\n\\ninput:focus ~ .bg-success {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #28c76f !important;\\n}\\n\\n.custom-control-success .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-success .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #28c76f;\\n  background-color: #28c76f;\\n}\\n.custom-control-success.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-success.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-success.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-success.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-success.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-success.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(40, 199, 111, 0.4) !important;\\n}\\n.custom-control-success .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(40, 199, 111, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-success .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #28c76f !important;\\n}\\n\\n.custom-switch-success .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #28c76f !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #28c76f !important;\\n  border-color: #28c76f !important;\\n}\\n\\n.text-success.text-darken-1 {\\n  color: #24b263 !important;\\n}\\n\\n.bg-success.bg-darken-1 {\\n  background-color: #24b263 !important;\\n}\\n\\n.border-success.border-darken-1 {\\n  border: 1px solid #24b263 !important;\\n}\\n\\n.border-top-success.border-top-darken-1 {\\n  border-top: 1px solid #24b263 !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-1 {\\n  border-bottom: 1px solid #24b263 !important;\\n}\\n\\n.border-left-success.border-left-darken-1 {\\n  border-left: 1px solid #24b263 !important;\\n}\\n\\n.border-right-success.border-right-darken-1 {\\n  border-right: 1px solid #24b263 !important;\\n}\\n\\n.overlay-success.overlay-darken-1 {\\n  background: #24b263; /* The Fallback */\\n  background: rgba(36, 178, 99, 0.6);\\n}\\n\\n.text-success.text-darken-2 {\\n  color: #1f9d57 !important;\\n}\\n\\n.bg-success.bg-darken-2 {\\n  background-color: #1f9d57 !important;\\n}\\n\\n.border-success.border-darken-2 {\\n  border: 1px solid #1f9d57 !important;\\n}\\n\\n.border-top-success.border-top-darken-2 {\\n  border-top: 1px solid #1f9d57 !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-2 {\\n  border-bottom: 1px solid #1f9d57 !important;\\n}\\n\\n.border-left-success.border-left-darken-2 {\\n  border-left: 1px solid #1f9d57 !important;\\n}\\n\\n.border-right-success.border-right-darken-2 {\\n  border-right: 1px solid #1f9d57 !important;\\n}\\n\\n.overlay-success.overlay-darken-2 {\\n  background: #1f9d57; /* The Fallback */\\n  background: rgba(31, 157, 87, 0.6);\\n}\\n\\n.text-success.text-darken-3 {\\n  color: #1b874b !important;\\n}\\n\\n.bg-success.bg-darken-3 {\\n  background-color: #1b874b !important;\\n}\\n\\n.border-success.border-darken-3 {\\n  border: 1px solid #1b874b !important;\\n}\\n\\n.border-top-success.border-top-darken-3 {\\n  border-top: 1px solid #1b874b !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-3 {\\n  border-bottom: 1px solid #1b874b !important;\\n}\\n\\n.border-left-success.border-left-darken-3 {\\n  border-left: 1px solid #1b874b !important;\\n}\\n\\n.border-right-success.border-right-darken-3 {\\n  border-right: 1px solid #1b874b !important;\\n}\\n\\n.overlay-success.overlay-darken-3 {\\n  background: #1b874b; /* The Fallback */\\n  background: rgba(27, 135, 75, 0.6);\\n}\\n\\n.text-success.text-darken-4 {\\n  color: #177240 !important;\\n}\\n\\n.bg-success.bg-darken-4 {\\n  background-color: #177240 !important;\\n}\\n\\n.border-success.border-darken-4 {\\n  border: 1px solid #177240 !important;\\n}\\n\\n.border-top-success.border-top-darken-4 {\\n  border-top: 1px solid #177240 !important;\\n}\\n\\n.border-bottom-success.border-bottom-darken-4 {\\n  border-bottom: 1px solid #177240 !important;\\n}\\n\\n.border-left-success.border-left-darken-4 {\\n  border-left: 1px solid #177240 !important;\\n}\\n\\n.border-right-success.border-right-darken-4 {\\n  border-right: 1px solid #177240 !important;\\n}\\n\\n.overlay-success.overlay-darken-4 {\\n  background: #177240; /* The Fallback */\\n  background: rgba(23, 114, 64, 0.6);\\n}\\n\\n.text-success.text-accent-1 {\\n  color: #e1fff1 !important;\\n}\\n\\n.bg-success.bg-accent-1 {\\n  background-color: #e1fff1 !important;\\n}\\n\\n.border-success.border-accent-1 {\\n  border: 1px solid #e1fff1 !important;\\n}\\n\\n.border-top-success.border-top-accent-1 {\\n  border-top: 1px solid #e1fff1 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-1 {\\n  border-bottom: 1px solid #e1fff1 !important;\\n}\\n\\n.border-left-success.border-left-accent-1 {\\n  border-left: 1px solid #e1fff1 !important;\\n}\\n\\n.border-right-success.border-right-accent-1 {\\n  border-right: 1px solid #e1fff1 !important;\\n}\\n\\n.overlay-success.overlay-accent-1 {\\n  background: #e1fff1; /* The Fallback */\\n  background: rgba(225, 255, 241, 0.6);\\n}\\n\\n.text-success.text-accent-2 {\\n  color: #aeffd9 !important;\\n}\\n\\n.bg-success.bg-accent-2 {\\n  background-color: #aeffd9 !important;\\n}\\n\\n.border-success.border-accent-2 {\\n  border: 1px solid #aeffd9 !important;\\n}\\n\\n.border-top-success.border-top-accent-2 {\\n  border-top: 1px solid #aeffd9 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-2 {\\n  border-bottom: 1px solid #aeffd9 !important;\\n}\\n\\n.border-left-success.border-left-accent-2 {\\n  border-left: 1px solid #aeffd9 !important;\\n}\\n\\n.border-right-success.border-right-accent-2 {\\n  border-right: 1px solid #aeffd9 !important;\\n}\\n\\n.overlay-success.overlay-accent-2 {\\n  background: #aeffd9; /* The Fallback */\\n  background: rgba(174, 255, 217, 0.6);\\n}\\n\\n.text-success.text-accent-3 {\\n  color: #7bffc1 !important;\\n}\\n\\n.bg-success.bg-accent-3 {\\n  background-color: #7bffc1 !important;\\n}\\n\\n.border-success.border-accent-3 {\\n  border: 1px solid #7bffc1 !important;\\n}\\n\\n.border-top-success.border-top-accent-3 {\\n  border-top: 1px solid #7bffc1 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-3 {\\n  border-bottom: 1px solid #7bffc1 !important;\\n}\\n\\n.border-left-success.border-left-accent-3 {\\n  border-left: 1px solid #7bffc1 !important;\\n}\\n\\n.border-right-success.border-right-accent-3 {\\n  border-right: 1px solid #7bffc1 !important;\\n}\\n\\n.overlay-success.overlay-accent-3 {\\n  background: #7bffc1; /* The Fallback */\\n  background: rgba(123, 255, 193, 0.6);\\n}\\n\\n.text-success.text-accent-4 {\\n  color: #62ffb5 !important;\\n}\\n\\n.bg-success.bg-accent-4 {\\n  background-color: #62ffb5 !important;\\n}\\n\\n.border-success.border-accent-4 {\\n  border: 1px solid #62ffb5 !important;\\n}\\n\\n.border-top-success.border-top-accent-4 {\\n  border-top: 1px solid #62ffb5 !important;\\n}\\n\\n.border-bottom-success.border-bottom-accent-4 {\\n  border-bottom: 1px solid #62ffb5 !important;\\n}\\n\\n.border-left-success.border-left-accent-4 {\\n  border-left: 1px solid #62ffb5 !important;\\n}\\n\\n.border-right-success.border-right-accent-4 {\\n  border-right: 1px solid #62ffb5 !important;\\n}\\n\\n.overlay-success.overlay-accent-4 {\\n  background: #62ffb5; /* The Fallback */\\n  background: rgba(98, 255, 181, 0.6);\\n}\\n\\n.text-info.text-lighten-5 {\\n  color: #69efff !important;\\n}\\n\\n.bg-info.bg-lighten-5 {\\n  background-color: #69efff !important;\\n}\\n\\n.border-info.border-lighten-5 {\\n  border: 1px solid #69efff !important;\\n}\\n\\n.border-top-info.border-top-lighten-5 {\\n  border-top: 1px solid #69efff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-5 {\\n  border-bottom: 1px solid #69efff !important;\\n}\\n\\n.border-left-info.border-left-lighten-5 {\\n  border-left: 1px solid #69efff !important;\\n}\\n\\n.border-right-info.border-right-lighten-5 {\\n  border-right: 1px solid #69efff !important;\\n}\\n\\n.overlay-info.overlay-lighten-5 {\\n  background: #69efff; /* The Fallback */\\n  background: rgba(105, 239, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-4 {\\n  color: #4fecff !important;\\n}\\n\\n.bg-info.bg-lighten-4 {\\n  background-color: #4fecff !important;\\n}\\n\\n.border-info.border-lighten-4 {\\n  border: 1px solid #4fecff !important;\\n}\\n\\n.border-top-info.border-top-lighten-4 {\\n  border-top: 1px solid #4fecff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-4 {\\n  border-bottom: 1px solid #4fecff !important;\\n}\\n\\n.border-left-info.border-left-lighten-4 {\\n  border-left: 1px solid #4fecff !important;\\n}\\n\\n.border-right-info.border-right-lighten-4 {\\n  border-right: 1px solid #4fecff !important;\\n}\\n\\n.overlay-info.overlay-lighten-4 {\\n  background: #4fecff; /* The Fallback */\\n  background: rgba(79, 236, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-3 {\\n  color: #36e9ff !important;\\n}\\n\\n.bg-info.bg-lighten-3 {\\n  background-color: #36e9ff !important;\\n}\\n\\n.border-info.border-lighten-3 {\\n  border: 1px solid #36e9ff !important;\\n}\\n\\n.border-top-info.border-top-lighten-3 {\\n  border-top: 1px solid #36e9ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-3 {\\n  border-bottom: 1px solid #36e9ff !important;\\n}\\n\\n.border-left-info.border-left-lighten-3 {\\n  border-left: 1px solid #36e9ff !important;\\n}\\n\\n.border-right-info.border-right-lighten-3 {\\n  border-right: 1px solid #36e9ff !important;\\n}\\n\\n.overlay-info.overlay-lighten-3 {\\n  background: #36e9ff; /* The Fallback */\\n  background: rgba(54, 233, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-2 {\\n  color: #1ce7ff !important;\\n}\\n\\n.bg-info.bg-lighten-2 {\\n  background-color: #1ce7ff !important;\\n}\\n\\n.border-info.border-lighten-2 {\\n  border: 1px solid #1ce7ff !important;\\n}\\n\\n.border-top-info.border-top-lighten-2 {\\n  border-top: 1px solid #1ce7ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-2 {\\n  border-bottom: 1px solid #1ce7ff !important;\\n}\\n\\n.border-left-info.border-left-lighten-2 {\\n  border-left: 1px solid #1ce7ff !important;\\n}\\n\\n.border-right-info.border-right-lighten-2 {\\n  border-right: 1px solid #1ce7ff !important;\\n}\\n\\n.overlay-info.overlay-lighten-2 {\\n  background: #1ce7ff; /* The Fallback */\\n  background: rgba(28, 231, 255, 0.6);\\n}\\n\\n.text-info.text-lighten-1 {\\n  color: #03e4ff !important;\\n}\\n\\n.bg-info.bg-lighten-1 {\\n  background-color: #03e4ff !important;\\n}\\n\\n.border-info.border-lighten-1 {\\n  border: 1px solid #03e4ff !important;\\n}\\n\\n.border-top-info.border-top-lighten-1 {\\n  border-top: 1px solid #03e4ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-lighten-1 {\\n  border-bottom: 1px solid #03e4ff !important;\\n}\\n\\n.border-left-info.border-left-lighten-1 {\\n  border-left: 1px solid #03e4ff !important;\\n}\\n\\n.border-right-info.border-right-lighten-1 {\\n  border-right: 1px solid #03e4ff !important;\\n}\\n\\n.overlay-info.overlay-lighten-1 {\\n  background: #03e4ff; /* The Fallback */\\n  background: rgba(3, 228, 255, 0.6);\\n}\\n\\n.bg-info {\\n  background-color: #00cfe8 !important;\\n}\\n.bg-info .card-header,\\n.bg-info .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-info {\\n  background: rgba(0, 207, 232, 0.12) !important;\\n  color: #00cfe8 !important;\\n}\\n.alert-info .alert-heading {\\n  box-shadow: rgba(0, 207, 232, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-info .alert-link {\\n  color: #00b8cf !important;\\n}\\n.alert-info .close {\\n  color: #00cfe8 !important;\\n}\\n\\n.bg-light-info {\\n  background: rgba(0, 207, 232, 0.12) !important;\\n  color: #00cfe8 !important;\\n}\\n.bg-light-info.fc-h-event, .bg-light-info.fc-v-event {\\n  border-color: rgba(0, 207, 232, 0.1);\\n}\\n.bg-light-info .fc-list-event-dot,\\n.bg-light-info .fc-daygrid-event-dot {\\n  border-color: #00cfe8 !important;\\n}\\n.bg-light-info.fc-list-event:hover td {\\n  background: rgba(0, 207, 232, 0.1) !important;\\n}\\n.bg-light-info.fc-list-event .fc-list-event-title {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-info {\\n  color: #00cfe8 !important;\\n}\\n\\n.border-info {\\n  border: 1px solid #00cfe8 !important;\\n}\\n\\n.border-top-info {\\n  border-top: 1px solid #00cfe8;\\n}\\n\\n.border-bottom-info {\\n  border-bottom: 1px solid #00cfe8;\\n}\\n\\n.border-left-info {\\n  border-left: 1px solid #00cfe8;\\n}\\n\\n.border-right-info {\\n  border-right: 1px solid #00cfe8;\\n}\\n\\n.bg-info.badge-glow,\\n.border-info.badge-glow,\\n.badge-info.badge-glow {\\n  box-shadow: 0px 0px 10px #00cfe8;\\n}\\n\\n.badge.badge-light-info {\\n  background-color: rgba(0, 207, 232, 0.12);\\n  color: #00cfe8 !important;\\n}\\n\\n.overlay-info {\\n  background: #00cfe8; /* The Fallback */\\n  background: rgba(0, 207, 232, 0.6);\\n}\\n\\n.btn-info {\\n  border-color: #00cfe8 !important;\\n  background-color: #00cfe8 !important;\\n  color: #fff !important;\\n}\\n.btn-info:focus, .btn-info:active, .btn-info.active {\\n  color: #fff;\\n  background-color: #00b8cf !important;\\n}\\n.btn-info:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #00cfe8;\\n}\\n.btn-info:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-info {\\n  background-color: transparent;\\n  color: #00cfe8;\\n}\\n.btn-flat-info:hover {\\n  color: #00cfe8;\\n}\\n.btn-flat-info:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(0, 207, 232, 0.12);\\n}\\n.btn-flat-info:active, .btn-flat-info.active, .btn-flat-info:focus {\\n  background-color: rgba(0, 207, 232, 0.2);\\n  color: #00cfe8;\\n}\\n.btn-flat-info.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-info {\\n  background-color: #00cfe8;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-info:hover:not(.disabled):not(:disabled) {\\n  background-color: #03e4ff;\\n}\\n.btn-relief-info:active, .btn-relief-info.active, .btn-relief-info:focus {\\n  background-color: #00b8cf;\\n}\\n.btn-relief-info:hover {\\n  color: #fff;\\n}\\n.btn-relief-info:active, .btn-relief-info.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-info {\\n  border: 1px solid #00cfe8 !important;\\n  background-color: transparent;\\n  color: #00cfe8;\\n}\\n.btn-outline-info:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(0, 207, 232, 0.04);\\n  color: #00cfe8;\\n}\\n.btn-outline-info:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(0, 207, 232, 0.2);\\n  color: #00cfe8;\\n}\\n.btn-outline-info.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-info.dropdown-toggle {\\n  background-color: rgba(0, 207, 232, 0.2);\\n  color: #00cfe8;\\n}\\n\\n.btn-outline-info.waves-effect .waves-ripple,\\n.btn-flat-info.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(0, 207, 232, 0.2) 0, rgba(0, 207, 232, 0.3) 40%, rgba(0, 207, 232, 0.4) 50%, rgba(0, 207, 232, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-info {\\n  background-color: #00cfe8;\\n}\\n\\n.modal.modal-info .modal-header .modal-title {\\n  color: #00cfe8;\\n}\\n.modal.modal-info .modal-header .close {\\n  color: #00cfe8 !important;\\n}\\n\\n.pagination-info .page-item.active .page-link {\\n  background: #00cfe8 !important;\\n  color: #fff;\\n}\\n.pagination-info .page-item.active .page-link:hover {\\n  color: #fff;\\n}\\n.pagination-info .page-item .page-link:hover {\\n  color: #00cfe8;\\n}\\n.pagination-info .page-item.prev-item .page-link:hover, .pagination-info .page-item.next-item .page-link:hover {\\n  background: #00cfe8;\\n  color: #fff;\\n}\\n.pagination-info .page-item.next-item .page-link:active:after, .pagination-info .page-item.next-item .page-link:hover:after, .pagination-info .page-item.next .page-link:active:after, .pagination-info .page-item.next .page-link:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-info .page-item.prev-item .page-link:active:before, .pagination-info .page-item.prev-item .page-link:hover:before, .pagination-info .page-item.prev .page-link:active:before, .pagination-info .page-item.prev .page-link:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300cfe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-info .nav-item .nav-link.active {\\n  color: #fff;\\n  background-color: #00cfe8 !important;\\n  border-color: #00cfe8;\\n  box-shadow: 0 4px 18px -4px rgba(0, 207, 232, 0.65);\\n}\\n\\n.progress-bar-info {\\n  background-color: rgba(0, 207, 232, 0.12);\\n}\\n.progress-bar-info .progress-bar {\\n  background-color: #00cfe8;\\n}\\n\\n.timeline .timeline-point-info {\\n  border-color: #00cfe8 !important;\\n}\\n.timeline .timeline-point-info i,\\n.timeline .timeline-point-info svg {\\n  stroke: #00cfe8 !important;\\n}\\n.timeline .timeline-point-info.timeline-point-indicator {\\n  background-color: #00cfe8 !important;\\n}\\n.timeline .timeline-point-info.timeline-point-indicator:before {\\n  background: rgba(0, 207, 232, 0.12) !important;\\n}\\n\\n.divider.divider-info .divider-text:before, .divider.divider-info .divider-text:after {\\n  border-color: #00cfe8 !important;\\n}\\n\\ninput:focus ~ .bg-info {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #00cfe8 !important;\\n}\\n\\n.custom-control-info .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-info .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #00cfe8;\\n  background-color: #00cfe8;\\n}\\n.custom-control-info.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-info.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-info.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-info.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-info.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-info.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(0, 207, 232, 0.4) !important;\\n}\\n.custom-control-info .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(0, 207, 232, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-info .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #00cfe8 !important;\\n}\\n\\n.custom-switch-info .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #00cfe8 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #00cfe8 !important;\\n  border-color: #00cfe8 !important;\\n}\\n\\n.text-info.text-darken-1 {\\n  color: #00b8cf !important;\\n}\\n\\n.bg-info.bg-darken-1 {\\n  background-color: #00b8cf !important;\\n}\\n\\n.border-info.border-darken-1 {\\n  border: 1px solid #00b8cf !important;\\n}\\n\\n.border-top-info.border-top-darken-1 {\\n  border-top: 1px solid #00b8cf !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-1 {\\n  border-bottom: 1px solid #00b8cf !important;\\n}\\n\\n.border-left-info.border-left-darken-1 {\\n  border-left: 1px solid #00b8cf !important;\\n}\\n\\n.border-right-info.border-right-darken-1 {\\n  border-right: 1px solid #00b8cf !important;\\n}\\n\\n.overlay-info.overlay-darken-1 {\\n  background: #00b8cf; /* The Fallback */\\n  background: rgba(0, 184, 207, 0.6);\\n}\\n\\n.text-info.text-darken-2 {\\n  color: #00a1b5 !important;\\n}\\n\\n.bg-info.bg-darken-2 {\\n  background-color: #00a1b5 !important;\\n}\\n\\n.border-info.border-darken-2 {\\n  border: 1px solid #00a1b5 !important;\\n}\\n\\n.border-top-info.border-top-darken-2 {\\n  border-top: 1px solid #00a1b5 !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-2 {\\n  border-bottom: 1px solid #00a1b5 !important;\\n}\\n\\n.border-left-info.border-left-darken-2 {\\n  border-left: 1px solid #00a1b5 !important;\\n}\\n\\n.border-right-info.border-right-darken-2 {\\n  border-right: 1px solid #00a1b5 !important;\\n}\\n\\n.overlay-info.overlay-darken-2 {\\n  background: #00a1b5; /* The Fallback */\\n  background: rgba(0, 161, 181, 0.6);\\n}\\n\\n.text-info.text-darken-3 {\\n  color: #008b9c !important;\\n}\\n\\n.bg-info.bg-darken-3 {\\n  background-color: #008b9c !important;\\n}\\n\\n.border-info.border-darken-3 {\\n  border: 1px solid #008b9c !important;\\n}\\n\\n.border-top-info.border-top-darken-3 {\\n  border-top: 1px solid #008b9c !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-3 {\\n  border-bottom: 1px solid #008b9c !important;\\n}\\n\\n.border-left-info.border-left-darken-3 {\\n  border-left: 1px solid #008b9c !important;\\n}\\n\\n.border-right-info.border-right-darken-3 {\\n  border-right: 1px solid #008b9c !important;\\n}\\n\\n.overlay-info.overlay-darken-3 {\\n  background: #008b9c; /* The Fallback */\\n  background: rgba(0, 139, 156, 0.6);\\n}\\n\\n.text-info.text-darken-4 {\\n  color: #007482 !important;\\n}\\n\\n.bg-info.bg-darken-4 {\\n  background-color: #007482 !important;\\n}\\n\\n.border-info.border-darken-4 {\\n  border: 1px solid #007482 !important;\\n}\\n\\n.border-top-info.border-top-darken-4 {\\n  border-top: 1px solid #007482 !important;\\n}\\n\\n.border-bottom-info.border-bottom-darken-4 {\\n  border-bottom: 1px solid #007482 !important;\\n}\\n\\n.border-left-info.border-left-darken-4 {\\n  border-left: 1px solid #007482 !important;\\n}\\n\\n.border-right-info.border-right-darken-4 {\\n  border-right: 1px solid #007482 !important;\\n}\\n\\n.overlay-info.overlay-darken-4 {\\n  background: #007482; /* The Fallback */\\n  background: rgba(0, 116, 130, 0.6);\\n}\\n\\n.text-info.text-accent-1 {\\n  color: #feffff !important;\\n}\\n\\n.bg-info.bg-accent-1 {\\n  background-color: #feffff !important;\\n}\\n\\n.border-info.border-accent-1 {\\n  border: 1px solid #feffff !important;\\n}\\n\\n.border-top-info.border-top-accent-1 {\\n  border-top: 1px solid #feffff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-1 {\\n  border-bottom: 1px solid #feffff !important;\\n}\\n\\n.border-left-info.border-left-accent-1 {\\n  border-left: 1px solid #feffff !important;\\n}\\n\\n.border-right-info.border-right-accent-1 {\\n  border-right: 1px solid #feffff !important;\\n}\\n\\n.overlay-info.overlay-accent-1 {\\n  background: #feffff; /* The Fallback */\\n  background: rgba(254, 255, 255, 0.6);\\n}\\n\\n.text-info.text-accent-2 {\\n  color: #cbf5ff !important;\\n}\\n\\n.bg-info.bg-accent-2 {\\n  background-color: #cbf5ff !important;\\n}\\n\\n.border-info.border-accent-2 {\\n  border: 1px solid #cbf5ff !important;\\n}\\n\\n.border-top-info.border-top-accent-2 {\\n  border-top: 1px solid #cbf5ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-2 {\\n  border-bottom: 1px solid #cbf5ff !important;\\n}\\n\\n.border-left-info.border-left-accent-2 {\\n  border-left: 1px solid #cbf5ff !important;\\n}\\n\\n.border-right-info.border-right-accent-2 {\\n  border-right: 1px solid #cbf5ff !important;\\n}\\n\\n.overlay-info.overlay-accent-2 {\\n  background: #cbf5ff; /* The Fallback */\\n  background: rgba(203, 245, 255, 0.6);\\n}\\n\\n.text-info.text-accent-3 {\\n  color: #98ecff !important;\\n}\\n\\n.bg-info.bg-accent-3 {\\n  background-color: #98ecff !important;\\n}\\n\\n.border-info.border-accent-3 {\\n  border: 1px solid #98ecff !important;\\n}\\n\\n.border-top-info.border-top-accent-3 {\\n  border-top: 1px solid #98ecff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-3 {\\n  border-bottom: 1px solid #98ecff !important;\\n}\\n\\n.border-left-info.border-left-accent-3 {\\n  border-left: 1px solid #98ecff !important;\\n}\\n\\n.border-right-info.border-right-accent-3 {\\n  border-right: 1px solid #98ecff !important;\\n}\\n\\n.overlay-info.overlay-accent-3 {\\n  background: #98ecff; /* The Fallback */\\n  background: rgba(152, 236, 255, 0.6);\\n}\\n\\n.text-info.text-accent-4 {\\n  color: #7fe7ff !important;\\n}\\n\\n.bg-info.bg-accent-4 {\\n  background-color: #7fe7ff !important;\\n}\\n\\n.border-info.border-accent-4 {\\n  border: 1px solid #7fe7ff !important;\\n}\\n\\n.border-top-info.border-top-accent-4 {\\n  border-top: 1px solid #7fe7ff !important;\\n}\\n\\n.border-bottom-info.border-bottom-accent-4 {\\n  border-bottom: 1px solid #7fe7ff !important;\\n}\\n\\n.border-left-info.border-left-accent-4 {\\n  border-left: 1px solid #7fe7ff !important;\\n}\\n\\n.border-right-info.border-right-accent-4 {\\n  border-right: 1px solid #7fe7ff !important;\\n}\\n\\n.overlay-info.overlay-accent-4 {\\n  background: #7fe7ff; /* The Fallback */\\n  background: rgba(127, 231, 255, 0.6);\\n}\\n\\n.text-warning.text-lighten-5 {\\n  color: #ffe0c3 !important;\\n}\\n\\n.bg-warning.bg-lighten-5 {\\n  background-color: #ffe0c3 !important;\\n}\\n\\n.border-warning.border-lighten-5 {\\n  border: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-5 {\\n  border-top: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-5 {\\n  border-bottom: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-5 {\\n  border-left: 1px solid #ffe0c3 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-5 {\\n  border-right: 1px solid #ffe0c3 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-5 {\\n  background: #ffe0c3; /* The Fallback */\\n  background: rgba(255, 224, 195, 0.6);\\n}\\n\\n.text-warning.text-lighten-4 {\\n  color: #ffd3a9 !important;\\n}\\n\\n.bg-warning.bg-lighten-4 {\\n  background-color: #ffd3a9 !important;\\n}\\n\\n.border-warning.border-lighten-4 {\\n  border: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-4 {\\n  border-top: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-4 {\\n  border-bottom: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-4 {\\n  border-left: 1px solid #ffd3a9 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-4 {\\n  border-right: 1px solid #ffd3a9 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-4 {\\n  background: #ffd3a9; /* The Fallback */\\n  background: rgba(255, 211, 169, 0.6);\\n}\\n\\n.text-warning.text-lighten-3 {\\n  color: #ffc690 !important;\\n}\\n\\n.bg-warning.bg-lighten-3 {\\n  background-color: #ffc690 !important;\\n}\\n\\n.border-warning.border-lighten-3 {\\n  border: 1px solid #ffc690 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-3 {\\n  border-top: 1px solid #ffc690 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-3 {\\n  border-bottom: 1px solid #ffc690 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-3 {\\n  border-left: 1px solid #ffc690 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-3 {\\n  border-right: 1px solid #ffc690 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-3 {\\n  background: #ffc690; /* The Fallback */\\n  background: rgba(255, 198, 144, 0.6);\\n}\\n\\n.text-warning.text-lighten-2 {\\n  color: #ffb976 !important;\\n}\\n\\n.bg-warning.bg-lighten-2 {\\n  background-color: #ffb976 !important;\\n}\\n\\n.border-warning.border-lighten-2 {\\n  border: 1px solid #ffb976 !important;\\n}\\n\\n.border-top-warning.border-top-lighten-2 {\\n  border-top: 1px solid #ffb976 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-2 {\\n  border-bottom: 1px solid #ffb976 !important;\\n}\\n\\n.border-left-warning.border-left-lighten-2 {\\n  border-left: 1px solid #ffb976 !important;\\n}\\n\\n.border-right-warning.border-right-lighten-2 {\\n  border-right: 1px solid #ffb976 !important;\\n}\\n\\n.overlay-warning.overlay-lighten-2 {\\n  background: #ffb976; /* The Fallback */\\n  background: rgba(255, 185, 118, 0.6);\\n}\\n\\n.text-warning.text-lighten-1 {\\n  color: #ffac5d !important;\\n}\\n\\n.bg-warning.bg-lighten-1 {\\n  background-color: #ffac5d !important;\\n}\\n\\n.border-warning.border-lighten-1 {\\n  border: 1px solid #ffac5d !important;\\n}\\n\\n.border-top-warning.border-top-lighten-1 {\\n  border-top: 1px solid #ffac5d !important;\\n}\\n\\n.border-bottom-warning.border-bottom-lighten-1 {\\n  border-bottom: 1px solid #ffac5d !important;\\n}\\n\\n.border-left-warning.border-left-lighten-1 {\\n  border-left: 1px solid #ffac5d !important;\\n}\\n\\n.border-right-warning.border-right-lighten-1 {\\n  border-right: 1px solid #ffac5d !important;\\n}\\n\\n.overlay-warning.overlay-lighten-1 {\\n  background: #ffac5d; /* The Fallback */\\n  background: rgba(255, 172, 93, 0.6);\\n}\\n\\n.bg-warning {\\n  background-color: #ff9f43 !important;\\n}\\n.bg-warning .card-header,\\n.bg-warning .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-warning {\\n  background: rgba(255, 159, 67, 0.12) !important;\\n  color: #ff9f43 !important;\\n}\\n.alert-warning .alert-heading {\\n  box-shadow: rgba(255, 159, 67, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-warning .alert-link {\\n  color: #ff922a !important;\\n}\\n.alert-warning .close {\\n  color: #ff9f43 !important;\\n}\\n\\n.bg-light-warning {\\n  background: rgba(255, 159, 67, 0.12) !important;\\n  color: #ff9f43 !important;\\n}\\n.bg-light-warning.fc-h-event, .bg-light-warning.fc-v-event {\\n  border-color: rgba(255, 159, 67, 0.1);\\n}\\n.bg-light-warning .fc-list-event-dot,\\n.bg-light-warning .fc-daygrid-event-dot {\\n  border-color: #ff9f43 !important;\\n}\\n.bg-light-warning.fc-list-event:hover td {\\n  background: rgba(255, 159, 67, 0.1) !important;\\n}\\n.bg-light-warning.fc-list-event .fc-list-event-title {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-warning {\\n  color: #ff9f43 !important;\\n}\\n\\n.border-warning {\\n  border: 1px solid #ff9f43 !important;\\n}\\n\\n.border-top-warning {\\n  border-top: 1px solid #ff9f43;\\n}\\n\\n.border-bottom-warning {\\n  border-bottom: 1px solid #ff9f43;\\n}\\n\\n.border-left-warning {\\n  border-left: 1px solid #ff9f43;\\n}\\n\\n.border-right-warning {\\n  border-right: 1px solid #ff9f43;\\n}\\n\\n.bg-warning.badge-glow,\\n.border-warning.badge-glow,\\n.badge-warning.badge-glow {\\n  box-shadow: 0px 0px 10px #ff9f43;\\n}\\n\\n.badge.badge-light-warning {\\n  background-color: rgba(255, 159, 67, 0.12);\\n  color: #ff9f43 !important;\\n}\\n\\n.overlay-warning {\\n  background: #ff9f43; /* The Fallback */\\n  background: rgba(255, 159, 67, 0.6);\\n}\\n\\n.btn-warning {\\n  border-color: #ff9f43 !important;\\n  background-color: #ff9f43 !important;\\n  color: #fff !important;\\n}\\n.btn-warning:focus, .btn-warning:active, .btn-warning.active {\\n  color: #fff;\\n  background-color: #ff922a !important;\\n}\\n.btn-warning:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #ff9f43;\\n}\\n.btn-warning:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-warning {\\n  background-color: transparent;\\n  color: #ff9f43;\\n}\\n.btn-flat-warning:hover {\\n  color: #ff9f43;\\n}\\n.btn-flat-warning:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(255, 159, 67, 0.12);\\n}\\n.btn-flat-warning:active, .btn-flat-warning.active, .btn-flat-warning:focus {\\n  background-color: rgba(255, 159, 67, 0.2);\\n  color: #ff9f43;\\n}\\n.btn-flat-warning.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-warning {\\n  background-color: #ff9f43;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-warning:hover:not(.disabled):not(:disabled) {\\n  background-color: #ffac5d;\\n}\\n.btn-relief-warning:active, .btn-relief-warning.active, .btn-relief-warning:focus {\\n  background-color: #ff922a;\\n}\\n.btn-relief-warning:hover {\\n  color: #fff;\\n}\\n.btn-relief-warning:active, .btn-relief-warning.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-warning {\\n  border: 1px solid #ff9f43 !important;\\n  background-color: transparent;\\n  color: #ff9f43;\\n}\\n.btn-outline-warning:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(255, 159, 67, 0.04);\\n  color: #ff9f43;\\n}\\n.btn-outline-warning:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(255, 159, 67, 0.2);\\n  color: #ff9f43;\\n}\\n.btn-outline-warning.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-warning.dropdown-toggle {\\n  background-color: rgba(255, 159, 67, 0.2);\\n  color: #ff9f43;\\n}\\n\\n.btn-outline-warning.waves-effect .waves-ripple,\\n.btn-flat-warning.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(255, 159, 67, 0.2) 0, rgba(255, 159, 67, 0.3) 40%, rgba(255, 159, 67, 0.4) 50%, rgba(255, 159, 67, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-warning {\\n  background-color: #ff9f43;\\n}\\n\\n.modal.modal-warning .modal-header .modal-title {\\n  color: #ff9f43;\\n}\\n.modal.modal-warning .modal-header .close {\\n  color: #ff9f43 !important;\\n}\\n\\n.pagination-warning .page-item.active .page-link {\\n  background: #ff9f43 !important;\\n  color: #fff;\\n}\\n.pagination-warning .page-item.active .page-link:hover {\\n  color: #fff;\\n}\\n.pagination-warning .page-item .page-link:hover {\\n  color: #ff9f43;\\n}\\n.pagination-warning .page-item.prev-item .page-link:hover, .pagination-warning .page-item.next-item .page-link:hover {\\n  background: #ff9f43;\\n  color: #fff;\\n}\\n.pagination-warning .page-item.next-item .page-link:active:after, .pagination-warning .page-item.next-item .page-link:hover:after, .pagination-warning .page-item.next .page-link:active:after, .pagination-warning .page-item.next .page-link:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-warning .page-item.prev-item .page-link:active:before, .pagination-warning .page-item.prev-item .page-link:hover:before, .pagination-warning .page-item.prev .page-link:active:before, .pagination-warning .page-item.prev .page-link:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff9f43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-warning .nav-item .nav-link.active {\\n  color: #fff;\\n  background-color: #ff9f43 !important;\\n  border-color: #ff9f43;\\n  box-shadow: 0 4px 18px -4px rgba(255, 159, 67, 0.65);\\n}\\n\\n.progress-bar-warning {\\n  background-color: rgba(255, 159, 67, 0.12);\\n}\\n.progress-bar-warning .progress-bar {\\n  background-color: #ff9f43;\\n}\\n\\n.timeline .timeline-point-warning {\\n  border-color: #ff9f43 !important;\\n}\\n.timeline .timeline-point-warning i,\\n.timeline .timeline-point-warning svg {\\n  stroke: #ff9f43 !important;\\n}\\n.timeline .timeline-point-warning.timeline-point-indicator {\\n  background-color: #ff9f43 !important;\\n}\\n.timeline .timeline-point-warning.timeline-point-indicator:before {\\n  background: rgba(255, 159, 67, 0.12) !important;\\n}\\n\\n.divider.divider-warning .divider-text:before, .divider.divider-warning .divider-text:after {\\n  border-color: #ff9f43 !important;\\n}\\n\\ninput:focus ~ .bg-warning {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #ff9f43 !important;\\n}\\n\\n.custom-control-warning .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-warning .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #ff9f43;\\n  background-color: #ff9f43;\\n}\\n.custom-control-warning.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-warning.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-warning.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-warning.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-warning.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-warning.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(255, 159, 67, 0.4) !important;\\n}\\n.custom-control-warning .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(255, 159, 67, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-warning .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #ff9f43 !important;\\n}\\n\\n.custom-switch-warning .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #ff9f43 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #ff9f43 !important;\\n  border-color: #ff9f43 !important;\\n}\\n\\n.text-warning.text-darken-1 {\\n  color: #ff922a !important;\\n}\\n\\n.bg-warning.bg-darken-1 {\\n  background-color: #ff922a !important;\\n}\\n\\n.border-warning.border-darken-1 {\\n  border: 1px solid #ff922a !important;\\n}\\n\\n.border-top-warning.border-top-darken-1 {\\n  border-top: 1px solid #ff922a !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-1 {\\n  border-bottom: 1px solid #ff922a !important;\\n}\\n\\n.border-left-warning.border-left-darken-1 {\\n  border-left: 1px solid #ff922a !important;\\n}\\n\\n.border-right-warning.border-right-darken-1 {\\n  border-right: 1px solid #ff922a !important;\\n}\\n\\n.overlay-warning.overlay-darken-1 {\\n  background: #ff922a; /* The Fallback */\\n  background: rgba(255, 146, 42, 0.6);\\n}\\n\\n.text-warning.text-darken-2 {\\n  color: #ff8510 !important;\\n}\\n\\n.bg-warning.bg-darken-2 {\\n  background-color: #ff8510 !important;\\n}\\n\\n.border-warning.border-darken-2 {\\n  border: 1px solid #ff8510 !important;\\n}\\n\\n.border-top-warning.border-top-darken-2 {\\n  border-top: 1px solid #ff8510 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-2 {\\n  border-bottom: 1px solid #ff8510 !important;\\n}\\n\\n.border-left-warning.border-left-darken-2 {\\n  border-left: 1px solid #ff8510 !important;\\n}\\n\\n.border-right-warning.border-right-darken-2 {\\n  border-right: 1px solid #ff8510 !important;\\n}\\n\\n.overlay-warning.overlay-darken-2 {\\n  background: #ff8510; /* The Fallback */\\n  background: rgba(255, 133, 16, 0.6);\\n}\\n\\n.text-warning.text-darken-3 {\\n  color: #f67800 !important;\\n}\\n\\n.bg-warning.bg-darken-3 {\\n  background-color: #f67800 !important;\\n}\\n\\n.border-warning.border-darken-3 {\\n  border: 1px solid #f67800 !important;\\n}\\n\\n.border-top-warning.border-top-darken-3 {\\n  border-top: 1px solid #f67800 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-3 {\\n  border-bottom: 1px solid #f67800 !important;\\n}\\n\\n.border-left-warning.border-left-darken-3 {\\n  border-left: 1px solid #f67800 !important;\\n}\\n\\n.border-right-warning.border-right-darken-3 {\\n  border-right: 1px solid #f67800 !important;\\n}\\n\\n.overlay-warning.overlay-darken-3 {\\n  background: #f67800; /* The Fallback */\\n  background: rgba(246, 120, 0, 0.6);\\n}\\n\\n.text-warning.text-darken-4 {\\n  color: #dc6c00 !important;\\n}\\n\\n.bg-warning.bg-darken-4 {\\n  background-color: #dc6c00 !important;\\n}\\n\\n.border-warning.border-darken-4 {\\n  border: 1px solid #dc6c00 !important;\\n}\\n\\n.border-top-warning.border-top-darken-4 {\\n  border-top: 1px solid #dc6c00 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-darken-4 {\\n  border-bottom: 1px solid #dc6c00 !important;\\n}\\n\\n.border-left-warning.border-left-darken-4 {\\n  border-left: 1px solid #dc6c00 !important;\\n}\\n\\n.border-right-warning.border-right-darken-4 {\\n  border-right: 1px solid #dc6c00 !important;\\n}\\n\\n.overlay-warning.overlay-darken-4 {\\n  background: #dc6c00; /* The Fallback */\\n  background: rgba(220, 108, 0, 0.6);\\n}\\n\\n.text-warning.text-accent-1 {\\n  color: #fff5ef !important;\\n}\\n\\n.bg-warning.bg-accent-1 {\\n  background-color: #fff5ef !important;\\n}\\n\\n.border-warning.border-accent-1 {\\n  border: 1px solid #fff5ef !important;\\n}\\n\\n.border-top-warning.border-top-accent-1 {\\n  border-top: 1px solid #fff5ef !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-1 {\\n  border-bottom: 1px solid #fff5ef !important;\\n}\\n\\n.border-left-warning.border-left-accent-1 {\\n  border-left: 1px solid #fff5ef !important;\\n}\\n\\n.border-right-warning.border-right-accent-1 {\\n  border-right: 1px solid #fff5ef !important;\\n}\\n\\n.overlay-warning.overlay-accent-1 {\\n  background: #fff5ef; /* The Fallback */\\n  background: rgba(255, 245, 239, 0.6);\\n}\\n\\n.text-warning.text-accent-2 {\\n  color: #ffe5d8 !important;\\n}\\n\\n.bg-warning.bg-accent-2 {\\n  background-color: #ffe5d8 !important;\\n}\\n\\n.border-warning.border-accent-2 {\\n  border: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-top-warning.border-top-accent-2 {\\n  border-top: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-2 {\\n  border-bottom: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-left-warning.border-left-accent-2 {\\n  border-left: 1px solid #ffe5d8 !important;\\n}\\n\\n.border-right-warning.border-right-accent-2 {\\n  border-right: 1px solid #ffe5d8 !important;\\n}\\n\\n.overlay-warning.overlay-accent-2 {\\n  background: #ffe5d8; /* The Fallback */\\n  background: rgba(255, 229, 216, 0.6);\\n}\\n\\n.text-warning.text-accent-3 {\\n  color: #fff6f3 !important;\\n}\\n\\n.bg-warning.bg-accent-3 {\\n  background-color: #fff6f3 !important;\\n}\\n\\n.border-warning.border-accent-3 {\\n  border: 1px solid #fff6f3 !important;\\n}\\n\\n.border-top-warning.border-top-accent-3 {\\n  border-top: 1px solid #fff6f3 !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-3 {\\n  border-bottom: 1px solid #fff6f3 !important;\\n}\\n\\n.border-left-warning.border-left-accent-3 {\\n  border-left: 1px solid #fff6f3 !important;\\n}\\n\\n.border-right-warning.border-right-accent-3 {\\n  border-right: 1px solid #fff6f3 !important;\\n}\\n\\n.overlay-warning.overlay-accent-3 {\\n  background: #fff6f3; /* The Fallback */\\n  background: rgba(255, 246, 243, 0.6);\\n}\\n\\n.text-warning.text-accent-4 {\\n  color: #ffe3da !important;\\n}\\n\\n.bg-warning.bg-accent-4 {\\n  background-color: #ffe3da !important;\\n}\\n\\n.border-warning.border-accent-4 {\\n  border: 1px solid #ffe3da !important;\\n}\\n\\n.border-top-warning.border-top-accent-4 {\\n  border-top: 1px solid #ffe3da !important;\\n}\\n\\n.border-bottom-warning.border-bottom-accent-4 {\\n  border-bottom: 1px solid #ffe3da !important;\\n}\\n\\n.border-left-warning.border-left-accent-4 {\\n  border-left: 1px solid #ffe3da !important;\\n}\\n\\n.border-right-warning.border-right-accent-4 {\\n  border-right: 1px solid #ffe3da !important;\\n}\\n\\n.overlay-warning.overlay-accent-4 {\\n  background: #ffe3da; /* The Fallback */\\n  background: rgba(255, 227, 218, 0.6);\\n}\\n\\n.text-danger.text-lighten-5 {\\n  color: #f8c6c6 !important;\\n}\\n\\n.bg-danger.bg-lighten-5 {\\n  background-color: #f8c6c6 !important;\\n}\\n\\n.border-danger.border-lighten-5 {\\n  border: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-top-danger.border-top-lighten-5 {\\n  border-top: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-5 {\\n  border-bottom: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-left-danger.border-left-lighten-5 {\\n  border-left: 1px solid #f8c6c6 !important;\\n}\\n\\n.border-right-danger.border-right-lighten-5 {\\n  border-right: 1px solid #f8c6c6 !important;\\n}\\n\\n.overlay-danger.overlay-lighten-5 {\\n  background: #f8c6c6; /* The Fallback */\\n  background: rgba(248, 198, 198, 0.6);\\n}\\n\\n.text-danger.text-lighten-4 {\\n  color: #f5afaf !important;\\n}\\n\\n.bg-danger.bg-lighten-4 {\\n  background-color: #f5afaf !important;\\n}\\n\\n.border-danger.border-lighten-4 {\\n  border: 1px solid #f5afaf !important;\\n}\\n\\n.border-top-danger.border-top-lighten-4 {\\n  border-top: 1px solid #f5afaf !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-4 {\\n  border-bottom: 1px solid #f5afaf !important;\\n}\\n\\n.border-left-danger.border-left-lighten-4 {\\n  border-left: 1px solid #f5afaf !important;\\n}\\n\\n.border-right-danger.border-right-lighten-4 {\\n  border-right: 1px solid #f5afaf !important;\\n}\\n\\n.overlay-danger.overlay-lighten-4 {\\n  background: #f5afaf; /* The Fallback */\\n  background: rgba(245, 175, 175, 0.6);\\n}\\n\\n.text-danger.text-lighten-3 {\\n  color: #f29899 !important;\\n}\\n\\n.bg-danger.bg-lighten-3 {\\n  background-color: #f29899 !important;\\n}\\n\\n.border-danger.border-lighten-3 {\\n  border: 1px solid #f29899 !important;\\n}\\n\\n.border-top-danger.border-top-lighten-3 {\\n  border-top: 1px solid #f29899 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-3 {\\n  border-bottom: 1px solid #f29899 !important;\\n}\\n\\n.border-left-danger.border-left-lighten-3 {\\n  border-left: 1px solid #f29899 !important;\\n}\\n\\n.border-right-danger.border-right-lighten-3 {\\n  border-right: 1px solid #f29899 !important;\\n}\\n\\n.overlay-danger.overlay-lighten-3 {\\n  background: #f29899; /* The Fallback */\\n  background: rgba(242, 152, 153, 0.6);\\n}\\n\\n.text-danger.text-lighten-2 {\\n  color: #f08182 !important;\\n}\\n\\n.bg-danger.bg-lighten-2 {\\n  background-color: #f08182 !important;\\n}\\n\\n.border-danger.border-lighten-2 {\\n  border: 1px solid #f08182 !important;\\n}\\n\\n.border-top-danger.border-top-lighten-2 {\\n  border-top: 1px solid #f08182 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-2 {\\n  border-bottom: 1px solid #f08182 !important;\\n}\\n\\n.border-left-danger.border-left-lighten-2 {\\n  border-left: 1px solid #f08182 !important;\\n}\\n\\n.border-right-danger.border-right-lighten-2 {\\n  border-right: 1px solid #f08182 !important;\\n}\\n\\n.overlay-danger.overlay-lighten-2 {\\n  background: #f08182; /* The Fallback */\\n  background: rgba(240, 129, 130, 0.6);\\n}\\n\\n.text-danger.text-lighten-1 {\\n  color: #ed6b6c !important;\\n}\\n\\n.bg-danger.bg-lighten-1 {\\n  background-color: #ed6b6c !important;\\n}\\n\\n.border-danger.border-lighten-1 {\\n  border: 1px solid #ed6b6c !important;\\n}\\n\\n.border-top-danger.border-top-lighten-1 {\\n  border-top: 1px solid #ed6b6c !important;\\n}\\n\\n.border-bottom-danger.border-bottom-lighten-1 {\\n  border-bottom: 1px solid #ed6b6c !important;\\n}\\n\\n.border-left-danger.border-left-lighten-1 {\\n  border-left: 1px solid #ed6b6c !important;\\n}\\n\\n.border-right-danger.border-right-lighten-1 {\\n  border-right: 1px solid #ed6b6c !important;\\n}\\n\\n.overlay-danger.overlay-lighten-1 {\\n  background: #ed6b6c; /* The Fallback */\\n  background: rgba(237, 107, 108, 0.6);\\n}\\n\\n.bg-danger {\\n  background-color: #ea5455 !important;\\n}\\n.bg-danger .card-header,\\n.bg-danger .card-footer {\\n  background-color: transparent;\\n}\\n\\n.alert-danger {\\n  background: rgba(234, 84, 85, 0.12) !important;\\n  color: #ea5455 !important;\\n}\\n.alert-danger .alert-heading {\\n  box-shadow: rgba(234, 84, 85, 0.4) 0px 6px 15px -7px;\\n}\\n.alert-danger .alert-link {\\n  color: #e73d3e !important;\\n}\\n.alert-danger .close {\\n  color: #ea5455 !important;\\n}\\n\\n.bg-light-danger {\\n  background: rgba(234, 84, 85, 0.12) !important;\\n  color: #ea5455 !important;\\n}\\n.bg-light-danger.fc-h-event, .bg-light-danger.fc-v-event {\\n  border-color: rgba(234, 84, 85, 0.1);\\n}\\n.bg-light-danger .fc-list-event-dot,\\n.bg-light-danger .fc-daygrid-event-dot {\\n  border-color: #ea5455 !important;\\n}\\n.bg-light-danger.fc-list-event:hover td {\\n  background: rgba(234, 84, 85, 0.1) !important;\\n}\\n.bg-light-danger.fc-list-event .fc-list-event-title {\\n  color: #000;\\n}\\n\\n.avatar.bg-light-danger {\\n  color: #ea5455 !important;\\n}\\n\\n.border-danger {\\n  border: 1px solid #ea5455 !important;\\n}\\n\\n.border-top-danger {\\n  border-top: 1px solid #ea5455;\\n}\\n\\n.border-bottom-danger {\\n  border-bottom: 1px solid #ea5455;\\n}\\n\\n.border-left-danger {\\n  border-left: 1px solid #ea5455;\\n}\\n\\n.border-right-danger {\\n  border-right: 1px solid #ea5455;\\n}\\n\\n.bg-danger.badge-glow,\\n.border-danger.badge-glow,\\n.badge-danger.badge-glow {\\n  box-shadow: 0px 0px 10px #ea5455;\\n}\\n\\n.badge.badge-light-danger {\\n  background-color: rgba(234, 84, 85, 0.12);\\n  color: #ea5455 !important;\\n}\\n\\n.overlay-danger {\\n  background: #ea5455; /* The Fallback */\\n  background: rgba(234, 84, 85, 0.6);\\n}\\n\\n.btn-danger {\\n  border-color: #ea5455 !important;\\n  background-color: #ea5455 !important;\\n  color: #fff !important;\\n}\\n.btn-danger:focus, .btn-danger:active, .btn-danger.active {\\n  color: #fff;\\n  background-color: #e73d3e !important;\\n}\\n.btn-danger:hover:not(.disabled):not(:disabled) {\\n  box-shadow: 0 8px 25px -8px #ea5455;\\n}\\n.btn-danger:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n\\n.btn-flat-danger {\\n  background-color: transparent;\\n  color: #ea5455;\\n}\\n.btn-flat-danger:hover {\\n  color: #ea5455;\\n}\\n.btn-flat-danger:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(234, 84, 85, 0.12);\\n}\\n.btn-flat-danger:active, .btn-flat-danger.active, .btn-flat-danger:focus {\\n  background-color: rgba(234, 84, 85, 0.2);\\n  color: #ea5455;\\n}\\n.btn-flat-danger.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n\\n.btn-relief-danger {\\n  background-color: #ea5455;\\n  box-shadow: inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);\\n  color: #fff;\\n  transition: all 0.2s ease;\\n}\\n.btn-relief-danger:hover:not(.disabled):not(:disabled) {\\n  background-color: #ed6b6c;\\n}\\n.btn-relief-danger:active, .btn-relief-danger.active, .btn-relief-danger:focus {\\n  background-color: #e73d3e;\\n}\\n.btn-relief-danger:hover {\\n  color: #fff;\\n}\\n.btn-relief-danger:active, .btn-relief-danger.active {\\n  outline: none;\\n  box-shadow: none;\\n  transform: translateY(3px);\\n}\\n\\n.btn-outline-danger {\\n  border: 1px solid #ea5455 !important;\\n  background-color: transparent;\\n  color: #ea5455;\\n}\\n.btn-outline-danger:hover:not(.disabled):not(:disabled) {\\n  background-color: rgba(234, 84, 85, 0.04);\\n  color: #ea5455;\\n}\\n.btn-outline-danger:not(:disabled):not(.disabled):active:focus {\\n  box-shadow: none;\\n}\\n.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):focus {\\n  background-color: rgba(234, 84, 85, 0.2);\\n  color: #ea5455;\\n}\\n.btn-outline-danger.dropdown-toggle::after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\\\");\\n}\\n.show > .btn-outline-danger.dropdown-toggle {\\n  background-color: rgba(234, 84, 85, 0.2);\\n  color: #ea5455;\\n}\\n\\n.btn-outline-danger.waves-effect .waves-ripple,\\n.btn-flat-danger.waves-effect .waves-ripple {\\n  background: radial-gradient(rgba(234, 84, 85, 0.2) 0, rgba(234, 84, 85, 0.3) 40%, rgba(234, 84, 85, 0.4) 50%, rgba(234, 84, 85, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\\n}\\n\\n.bullet.bullet-danger {\\n  background-color: #ea5455;\\n}\\n\\n.modal.modal-danger .modal-header .modal-title {\\n  color: #ea5455;\\n}\\n.modal.modal-danger .modal-header .close {\\n  color: #ea5455 !important;\\n}\\n\\n.pagination-danger .page-item.active .page-link {\\n  background: #ea5455 !important;\\n  color: #fff;\\n}\\n.pagination-danger .page-item.active .page-link:hover {\\n  color: #fff;\\n}\\n.pagination-danger .page-item .page-link:hover {\\n  color: #ea5455;\\n}\\n.pagination-danger .page-item.prev-item .page-link:hover, .pagination-danger .page-item.next-item .page-link:hover {\\n  background: #ea5455;\\n  color: #fff;\\n}\\n.pagination-danger .page-item.next-item .page-link:active:after, .pagination-danger .page-item.next-item .page-link:hover:after, .pagination-danger .page-item.next .page-link:active:after, .pagination-danger .page-item.next .page-link:hover:after {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n.pagination-danger .page-item.prev-item .page-link:active:before, .pagination-danger .page-item.prev-item .page-link:hover:before, .pagination-danger .page-item.prev .page-link:active:before, .pagination-danger .page-item.prev .page-link:hover:before {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ea5455' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E\\\") !important;\\n}\\n\\n.nav-pill-danger .nav-item .nav-link.active {\\n  color: #fff;\\n  background-color: #ea5455 !important;\\n  border-color: #ea5455;\\n  box-shadow: 0 4px 18px -4px rgba(234, 84, 85, 0.65);\\n}\\n\\n.progress-bar-danger {\\n  background-color: rgba(234, 84, 85, 0.12);\\n}\\n.progress-bar-danger .progress-bar {\\n  background-color: #ea5455;\\n}\\n\\n.timeline .timeline-point-danger {\\n  border-color: #ea5455 !important;\\n}\\n.timeline .timeline-point-danger i,\\n.timeline .timeline-point-danger svg {\\n  stroke: #ea5455 !important;\\n}\\n.timeline .timeline-point-danger.timeline-point-indicator {\\n  background-color: #ea5455 !important;\\n}\\n.timeline .timeline-point-danger.timeline-point-indicator:before {\\n  background: rgba(234, 84, 85, 0.12) !important;\\n}\\n\\n.divider.divider-danger .divider-text:before, .divider.divider-danger .divider-text:after {\\n  border-color: #ea5455 !important;\\n}\\n\\ninput:focus ~ .bg-danger {\\n  box-shadow: 0 0 0 0.075rem #fff, 0 0 0 0.21rem #ea5455 !important;\\n}\\n\\n.custom-control-danger .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-danger .custom-control-input:active ~ .custom-control-label::before {\\n  border-color: #ea5455;\\n  background-color: #ea5455;\\n}\\n.custom-control-danger.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-danger.custom-checkbox .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-danger.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before, .custom-control-danger.custom-radio .custom-control-input:checked ~ .custom-control-label::before,\\n.custom-control-danger.custom-radio .custom-control-input:active ~ .custom-control-label::before,\\n.custom-control-danger.custom-radio .custom-control-input:focus ~ .custom-control-label::before {\\n  box-shadow: 0 2px 4px 0 rgba(234, 84, 85, 0.4) !important;\\n}\\n.custom-control-danger .custom-control-input:disabled:checked ~ .custom-control-label::before {\\n  background-color: rgba(234, 84, 85, 0.65) !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n.custom-control-danger .custom-control-input:focus ~ .custom-control-label::before {\\n  border-color: #ea5455 !important;\\n}\\n\\n.custom-switch-danger .custom-control-input:checked ~ .custom-control-label::before {\\n  background-color: #ea5455 !important;\\n  color: #fff;\\n  transition: all 0.2s ease-out;\\n}\\n\\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {\\n  background: #ea5455 !important;\\n  border-color: #ea5455 !important;\\n}\\n\\n.text-danger.text-darken-1 {\\n  color: #e73d3e !important;\\n}\\n\\n.bg-danger.bg-darken-1 {\\n  background-color: #e73d3e !important;\\n}\\n\\n.border-danger.border-darken-1 {\\n  border: 1px solid #e73d3e !important;\\n}\\n\\n.border-top-danger.border-top-darken-1 {\\n  border-top: 1px solid #e73d3e !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-1 {\\n  border-bottom: 1px solid #e73d3e !important;\\n}\\n\\n.border-left-danger.border-left-darken-1 {\\n  border-left: 1px solid #e73d3e !important;\\n}\\n\\n.border-right-danger.border-right-darken-1 {\\n  border-right: 1px solid #e73d3e !important;\\n}\\n\\n.overlay-danger.overlay-darken-1 {\\n  background: #e73d3e; /* The Fallback */\\n  background: rgba(231, 61, 62, 0.6);\\n}\\n\\n.text-danger.text-darken-2 {\\n  color: #e42728 !important;\\n}\\n\\n.bg-danger.bg-darken-2 {\\n  background-color: #e42728 !important;\\n}\\n\\n.border-danger.border-darken-2 {\\n  border: 1px solid #e42728 !important;\\n}\\n\\n.border-top-danger.border-top-darken-2 {\\n  border-top: 1px solid #e42728 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-2 {\\n  border-bottom: 1px solid #e42728 !important;\\n}\\n\\n.border-left-danger.border-left-darken-2 {\\n  border-left: 1px solid #e42728 !important;\\n}\\n\\n.border-right-danger.border-right-darken-2 {\\n  border-right: 1px solid #e42728 !important;\\n}\\n\\n.overlay-danger.overlay-darken-2 {\\n  background: #e42728; /* The Fallback */\\n  background: rgba(228, 39, 40, 0.6);\\n}\\n\\n.text-danger.text-darken-3 {\\n  color: #d71a1c !important;\\n}\\n\\n.bg-danger.bg-darken-3 {\\n  background-color: #d71a1c !important;\\n}\\n\\n.border-danger.border-darken-3 {\\n  border: 1px solid #d71a1c !important;\\n}\\n\\n.border-top-danger.border-top-darken-3 {\\n  border-top: 1px solid #d71a1c !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-3 {\\n  border-bottom: 1px solid #d71a1c !important;\\n}\\n\\n.border-left-danger.border-left-darken-3 {\\n  border-left: 1px solid #d71a1c !important;\\n}\\n\\n.border-right-danger.border-right-darken-3 {\\n  border-right: 1px solid #d71a1c !important;\\n}\\n\\n.overlay-danger.overlay-darken-3 {\\n  background: #d71a1c; /* The Fallback */\\n  background: rgba(215, 26, 28, 0.6);\\n}\\n\\n.text-danger.text-darken-4 {\\n  color: #c01819 !important;\\n}\\n\\n.bg-danger.bg-darken-4 {\\n  background-color: #c01819 !important;\\n}\\n\\n.border-danger.border-darken-4 {\\n  border: 1px solid #c01819 !important;\\n}\\n\\n.border-top-danger.border-top-darken-4 {\\n  border-top: 1px solid #c01819 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-darken-4 {\\n  border-bottom: 1px solid #c01819 !important;\\n}\\n\\n.border-left-danger.border-left-darken-4 {\\n  border-left: 1px solid #c01819 !important;\\n}\\n\\n.border-right-danger.border-right-darken-4 {\\n  border-right: 1px solid #c01819 !important;\\n}\\n\\n.overlay-danger.overlay-darken-4 {\\n  background: #c01819; /* The Fallback */\\n  background: rgba(192, 24, 25, 0.6);\\n}\\n\\n.text-danger.text-accent-1 {\\n  color: #ffeef1 !important;\\n}\\n\\n.bg-danger.bg-accent-1 {\\n  background-color: #ffeef1 !important;\\n}\\n\\n.border-danger.border-accent-1 {\\n  border: 1px solid #ffeef1 !important;\\n}\\n\\n.border-top-danger.border-top-accent-1 {\\n  border-top: 1px solid #ffeef1 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-1 {\\n  border-bottom: 1px solid #ffeef1 !important;\\n}\\n\\n.border-left-danger.border-left-accent-1 {\\n  border-left: 1px solid #ffeef1 !important;\\n}\\n\\n.border-right-danger.border-right-accent-1 {\\n  border-right: 1px solid #ffeef1 !important;\\n}\\n\\n.overlay-danger.overlay-accent-1 {\\n  background: #ffeef1; /* The Fallback */\\n  background: rgba(255, 238, 241, 0.6);\\n}\\n\\n.text-danger.text-accent-2 {\\n  color: #ffd6db !important;\\n}\\n\\n.bg-danger.bg-accent-2 {\\n  background-color: #ffd6db !important;\\n}\\n\\n.border-danger.border-accent-2 {\\n  border: 1px solid #ffd6db !important;\\n}\\n\\n.border-top-danger.border-top-accent-2 {\\n  border-top: 1px solid #ffd6db !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-2 {\\n  border-bottom: 1px solid #ffd6db !important;\\n}\\n\\n.border-left-danger.border-left-accent-2 {\\n  border-left: 1px solid #ffd6db !important;\\n}\\n\\n.border-right-danger.border-right-accent-2 {\\n  border-right: 1px solid #ffd6db !important;\\n}\\n\\n.overlay-danger.overlay-accent-2 {\\n  background: #ffd6db; /* The Fallback */\\n  background: rgba(255, 214, 219, 0.6);\\n}\\n\\n.text-danger.text-accent-3 {\\n  color: #ffecee !important;\\n}\\n\\n.bg-danger.bg-accent-3 {\\n  background-color: #ffecee !important;\\n}\\n\\n.border-danger.border-accent-3 {\\n  border: 1px solid #ffecee !important;\\n}\\n\\n.border-top-danger.border-top-accent-3 {\\n  border-top: 1px solid #ffecee !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-3 {\\n  border-bottom: 1px solid #ffecee !important;\\n}\\n\\n.border-left-danger.border-left-accent-3 {\\n  border-left: 1px solid #ffecee !important;\\n}\\n\\n.border-right-danger.border-right-accent-3 {\\n  border-right: 1px solid #ffecee !important;\\n}\\n\\n.overlay-danger.overlay-accent-3 {\\n  background: #ffecee; /* The Fallback */\\n  background: rgba(255, 236, 238, 0.6);\\n}\\n\\n.text-danger.text-accent-4 {\\n  color: #ffd3d7 !important;\\n}\\n\\n.bg-danger.bg-accent-4 {\\n  background-color: #ffd3d7 !important;\\n}\\n\\n.border-danger.border-accent-4 {\\n  border: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-top-danger.border-top-accent-4 {\\n  border-top: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-bottom-danger.border-bottom-accent-4 {\\n  border-bottom: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-left-danger.border-left-accent-4 {\\n  border-left: 1px solid #ffd3d7 !important;\\n}\\n\\n.border-right-danger.border-right-accent-4 {\\n  border-right: 1px solid #ffd3d7 !important;\\n}\\n\\n.overlay-danger.overlay-accent-4 {\\n  background: #ffd3d7; /* The Fallback */\\n  background: rgba(255, 211, 215, 0.6);\\n}\\n\\n.bg-gradient-dark,\\n.btn-gradient-dark {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #4b4b4b, #626262);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.dark-layout .bg-gradient-dark,\\n.dark-layout .btn-gradient-dark {\\n  background-image: linear-gradient(91.47deg, #626262, #4b4b4b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-dark:hover, .bg-gradient-dark:active,\\n.btn-gradient-dark:hover,\\n.btn-gradient-dark:active {\\n  color: #fff;\\n}\\n.bg-gradient-dark:hover:not(.disabled):not(:disabled),\\n.btn-gradient-dark:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-dark:active,\\n.btn-gradient-dark:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-dark:active, .bg-gradient-dark:focus,\\n.btn-gradient-dark:active,\\n.btn-gradient-dark:focus {\\n  background-image: linear-gradient(91.47deg, #626262, #4b4b4b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-primary,\\n.btn-gradient-primary {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #2472ea, #1150b1);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-primary:hover, .bg-gradient-primary:active,\\n.btn-gradient-primary:hover,\\n.btn-gradient-primary:active {\\n  color: #fff;\\n}\\n.bg-gradient-primary:hover:not(.disabled):not(:disabled),\\n.btn-gradient-primary:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-primary:active,\\n.btn-gradient-primary:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-primary:active, .bg-gradient-primary:focus,\\n.btn-gradient-primary:active,\\n.btn-gradient-primary:focus {\\n  background-image: linear-gradient(91.47deg, #0a306b, #1150b1);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-secondary,\\n.btn-gradient-secondary {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #aaacb0, #82868b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-secondary:hover, .bg-gradient-secondary:active,\\n.btn-gradient-secondary:hover,\\n.btn-gradient-secondary:active {\\n  color: #fff;\\n}\\n.bg-gradient-secondary:hover:not(.disabled):not(:disabled),\\n.btn-gradient-secondary:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-secondary:active,\\n.btn-gradient-secondary:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-secondary:active, .bg-gradient-secondary:focus,\\n.btn-gradient-secondary:active,\\n.btn-gradient-secondary:focus {\\n  background-image: linear-gradient(91.47deg, #5d6064, #82868b);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-success,\\n.btn-gradient-success {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #5dde97, #28c76f);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-success:hover, .bg-gradient-success:active,\\n.btn-gradient-success:hover,\\n.btn-gradient-success:active {\\n  color: #fff;\\n}\\n.bg-gradient-success:hover:not(.disabled):not(:disabled),\\n.btn-gradient-success:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-success:active,\\n.btn-gradient-success:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-success:active, .bg-gradient-success:focus,\\n.btn-gradient-success:active,\\n.btn-gradient-success:focus {\\n  background-image: linear-gradient(91.47deg, #1b874b, #28c76f);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-info,\\n.btn-gradient-info {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #36e9ff, #00cfe8);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-info:hover, .bg-gradient-info:active,\\n.btn-gradient-info:hover,\\n.btn-gradient-info:active {\\n  color: #fff;\\n}\\n.bg-gradient-info:hover:not(.disabled):not(:disabled),\\n.btn-gradient-info:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-info:active,\\n.btn-gradient-info:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-info:active, .bg-gradient-info:focus,\\n.btn-gradient-info:active,\\n.btn-gradient-info:focus {\\n  background-image: linear-gradient(91.47deg, #008b9c, #00cfe8);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-warning,\\n.btn-gradient-warning {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #ffc690, #ff9f43);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-warning:hover, .bg-gradient-warning:active,\\n.btn-gradient-warning:hover,\\n.btn-gradient-warning:active {\\n  color: #fff;\\n}\\n.bg-gradient-warning:hover:not(.disabled):not(:disabled),\\n.btn-gradient-warning:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-warning:active,\\n.btn-gradient-warning:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-warning:active, .bg-gradient-warning:focus,\\n.btn-gradient-warning:active,\\n.btn-gradient-warning:focus {\\n  background-image: linear-gradient(91.47deg, #f67800, #ff9f43);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.bg-gradient-danger,\\n.btn-gradient-danger {\\n  color: #fff;\\n  transition: all 0.2s ease;\\n  background-image: linear-gradient(91.47deg, #f29899, #ea5455);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n.bg-gradient-danger:hover, .bg-gradient-danger:active,\\n.btn-gradient-danger:hover,\\n.btn-gradient-danger:active {\\n  color: #fff;\\n}\\n.bg-gradient-danger:hover:not(.disabled):not(:disabled),\\n.btn-gradient-danger:hover:not(.disabled):not(:disabled) {\\n  transform: translateY(-2px);\\n}\\n.bg-gradient-danger:active,\\n.btn-gradient-danger:active {\\n  transform: translateY(0);\\n}\\n.bg-gradient-danger:active, .bg-gradient-danger:focus,\\n.btn-gradient-danger:active,\\n.btn-gradient-danger:focus {\\n  background-image: linear-gradient(91.47deg, #d71a1c, #ea5455);\\n  background-repeat: repeat-x;\\n  background-repeat: repeat;\\n}\\n\\n.modal-header {\\n  align-items: center;\\n  justify-content: left;\\n  position: relative;\\n}\\n.modal-header i {\\n  color: #1150b1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAEEA,YAAY,QAMP,eAAe;AACtB,SAASC,SAAS,EAAEC,kBAAkB,QAAQ,gBAAgB;AAK9D,OAAOC,IAAI,MAAM,aAAa;AAI9B,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;ICKjBC,EAAA,CAAAC,cAAA,oBAIC;IAC2BD,EAAA,CAAAE,MAAA,GAA2B;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,MAAA,GAIF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;IATNH,EAAA,CAAAI,UAAA,mBAAkB;IAGQJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,kBAA2B;IAEnDP,EAAA,CAAAK,SAAA,GAIF;IAJEL,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAO,WAAA,6GAIF;;;ADNZ;AACA,OAAM,MAAOE,sBAAsB;EAGjCC,YACSC,mBAAuC,EACvCC,KAAiB,EACjBC,iBAAmC,EACnCC,QAAwB,EACxBC,MAAc;IAJd,KAAAJ,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAIR,KAAAC,YAAY,GAAG,IAAIrB,YAAY,EAAE;IACjC,KAAAsB,WAAW,GAAG,IAAItB,YAAY,EAAE;IAE/B,KAAAuB,mBAAmB,GAAG,KAAK;IAEzB,KAAAC,OAAO,GAAG,IAAIxB,YAAY,EAAE;IAEtC,KAAAyB,IAAI,GAAG,IAAIxB,SAAS,CAAC,EAAE,CAAC;IACxB,KAAAyB,KAAK,GAAQ,EAAE;IACN,KAAAC,SAAS,GAAQ,EAAE;IAElB,KAAAC,SAAS,GAAsB,IAAI5B,YAAY,EAAE;IAQ3D,KAAA6B,aAAa,GAAwB;MACnCC,SAAS,EAAE,gBAAgB;MAC3BC,KAAK,EAAE;QACLC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAE;OACT;MACDC,MAAM,EAAE;QACNH,MAAM,EAAE;UACNI,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE;SACT;QACDJ,IAAI,EAAE;UACJG,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE;SACT;QACDH,MAAM,EAAE;UACNE,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE;;OAEX;MACDC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE,QAAQ;MAChBC,GAAG,EAAE;KACN;IAED,KAAAC,UAAU,GAAsB,IAAI1C,YAAY,EAAE;IAGlD,KAAA2C,YAAY,GAAsB,IAAI3C,YAAY,EAAE;IAE7C,KAAA4C,aAAa,GAAG,CAAC;MAAEC,EAAE,EAAE;IAAG,CAAE,CAAC;IAC7B,KAAAC,UAAU,GAAQ,EAAE;IAEnB,KAAAC,WAAW,GAAG,KAAK;EAxD3B;EA0DAC,QAAQA,CAAA;IACNC,CAAC,CAACC,EAAE,CAACC,GAAG,GAAG,IAAI;IACf;IACA,IAAI,CAACL,UAAU,GAAG;MAAE,GAAG,IAAI,CAACjB,aAAa;MAAE,GAAG,IAAI,CAACuB;IAAM,CAAE;IAC3D,IAAI,IAAI,CAACC,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACC,SAAS,CAAEC,MAAM,IAAI;QACvC,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB;QACA,MAAMC,QAAQ,GAAG,IAAI,CAAC/B,IAAI,CAAC+B,QAAQ;QACnC,KAAK,MAAMC,GAAG,IAAID,QAAQ,EAAE;UAC1B,IAAIA,QAAQ,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;YAChC,IAAI,CAACF,MAAM,CAACI,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACH,GAAG,KAAKA,GAAG,CAAC,EAAE;cAC9C,IAAI,CAAChC,IAAI,CAACoC,aAAa,CAACJ,GAAG,CAAC;;;;MAIpC,CAAC,CAAC;;IAGJ,IAAI,CAACzC,mBAAmB,CAAC8C,oBAAoB,CAC3C,IAAI,CAAChB,UAAU,CAAChB,SAAS,EACzB,IAAI,CAACT,YAAY,CAClB;IAED,IAAI,CAACL,mBAAmB,CAAC+C,cAAc,CACrC,IAAI,CAACjB,UAAU,CAAChB,SAAS,EACzB,IAAI,CAACR,WAAW,CACjB;IAED,IAAI,CAACD,YAAY,CAACiC,SAAS,CAAC,MAAK;MAC/B,IAAI,CAAC,IAAI,CAAC/B,mBAAmB,EAAE;QAC7B,IAAI,CAACA,mBAAmB,GAAG,IAAI;QAC/B;QACA,IAAI,CAACyC,KAAK,EAAE;;IAEhB,CAAC,CAAC;IAEF,IAAI,CAAC1C,WAAW,CAACgC,SAAS,CAAC,MAAK;MAC9B,IAAI,CAAC/B,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACwB,WAAW,GAAG,KAAK;MAExB,IAAI,CAACD,UAAU,GAAG;QAAE,GAAG,IAAI,CAACjB,aAAa;QAAE,GAAG,IAAI,CAACuB;MAAM,CAAE;MAE3D,QAAQ,IAAI,CAACN,UAAU,CAACN,MAAM;QAC5B,KAAK,QAAQ;UACX,IACE,IAAI,CAACM,UAAU,CAACY,cAAc,CAAC,UAAU,CAAC,IAC1C,IAAI,CAACZ,UAAU,CAACmB,QAAQ,EACxB;YACA,IAAI,CAACC,eAAe,CAAC,IAAI,CAACpB,UAAU,CAACN,MAAM,CAAC;WAC7C,MAAM;YACL,IAAI,CAACI,aAAa,GAAG,CAAC;cAAEC,EAAE,EAAE;YAAG,CAAE,CAAC;;UAEpC;QACF,KAAK,MAAM;UACT,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACpB,UAAU,CAACN,MAAM,CAAC;UAC5C;QACF,KAAK,QAAQ;UACX,IAAI,CAACwB,KAAK,EAAE;UACZ,IAAI,IAAI,CAACE,eAAe,CAAC,IAAI,CAACpB,UAAU,CAACN,MAAM,CAAC,EAAE;YAChD;YACArC,IAAI,CAACgE,IAAI,CAAC;cACRpC,KAAK,EAAE,IAAI,CAACb,iBAAiB,CAACkD,OAAO,CAAC,yBAAyB,CAAC;cAChEC,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACkD,OAAO,CAClC,oCAAoC,CACrC;cACDE,cAAc,EAAE,IAAI;cACpBC,IAAI,EAAE,SAAS;cACfC,gBAAgB,EAAE,IAAI;cACtBC,gBAAgB,EAAE,IAAI,CAACvD,iBAAiB,CAACkD,OAAO,CAAC,QAAQ,CAAC;cAC1DM,iBAAiB,EAAE,IAAI,CAACxD,iBAAiB,CAACkD,OAAO,CAAC,IAAI,CAAC;cACvDO,cAAc,EAAE,KAAK;cACrBC,WAAW,EAAE;gBACXC,aAAa,EAAE,sBAAsB;gBACrCC,YAAY,EAAE;;aAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;cACjB,IAAIA,MAAM,CAACC,KAAK,EAAE;gBAChB,IAAI,CAAC7C,MAAM,CAAC,IAAI,CAACQ,aAAa,CAAC,CAAC,CAAC,CAAC;eACnC,MAAM;gBACL;;YAEJ,CAAC,CAAC;;UAEJ;QACF;UACE;;MAEJ;MACA,IAAI,CAAClB,KAAK,GAAG;QAAE,GAAG,IAAI,CAACA,KAAK;QAAE,GAAG,IAAI,CAACC;MAAS,CAAE;MACjD;MACA;IACF,CAAC,CAAC;IACF;IACA,IAAI,CAACF,IAAI,CAACyD,YAAY,CAAC5B,SAAS,CAAE2B,KAAK,IAAI;MACzC,IAAI,CAACvC,UAAU,CAACyC,IAAI,CAAC,IAAI,CAAC1D,IAAI,CAAC;IACjC,CAAC,CAAC;EACJ;EAEAW,MAAMA,CAACV,KAAK;IAEV,IAAI0D,cAAc,GAAG,IAAI,CAAC3D,IAAI,CAACwD,KAAK;IACpC,IAAI,IAAI,CAACI,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAAC3D,KAAK,CAAC;;IAGtB,MAAM4D,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC1C,UAAU,CAACN,MAAM,CAAC;IACjD;IAEA,KAAK,MAAMiB,GAAG,IAAI2B,cAAc,EAAE;MAChC,IAAIA,cAAc,CAAC1B,cAAc,CAACD,GAAG,CAAC,EAAE;QACtC,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC7C,aAAa,CAAC8C,MAAM,EAAED,CAAC,EAAE,EAAE;UAClD,IAAI,IAAI,CAAC7C,aAAa,CAAC6C,CAAC,CAAC,CAAC5C,EAAE,EAAE;YAC5B,IAAI8C,MAAM,GAAG,IAAI,CAAC/C,aAAa,CAAC6C,CAAC,CAAC,CAAC5C,EAAE;YACrC,MAAM+C,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACpC,GAAG,EAAEkC,MAAM,CAAC;YAClD;YACA,IAAIP,cAAc,CAAC3B,GAAG,CAAC,YAAYqC,QAAQ,EAAE;cAC3C;cACA,MAAMC,IAAI,GAAGX,cAAc,CAAC3B,GAAG,CAAC,CAACuC,IAAI,CAAC,CAAC,CAAC;cACxC;cACAV,QAAQ,CAACE,MAAM,CAACI,MAAM,EAAEG,IAAI,CAAC;aAC9B,MAAM;cACLT,QAAQ,CAACE,MAAM,CAACI,MAAM,EAAE,IAAI,CAACK,SAAS,CAACb,cAAc,CAAC3B,GAAG,CAAC,CAAC,CAAC;;;;;;IAOtE,IAAI,IAAI,CAACyC,YAAY,EAAE;MACrB,KAAK,MAAMzC,GAAG,IAAI,IAAI,CAACyC,YAAY,EAAE;QACnC,IAAI,IAAI,CAACA,YAAY,CAACzC,GAAG,CAAC,YAAYqC,QAAQ,EAAE;UAC9C,MAAMC,IAAI,GAAG,IAAI,CAACG,YAAY,CAACzC,GAAG,CAAC,CAACuC,IAAI,CAAC,CAAC,CAAC;UAC3CV,QAAQ,CAACE,MAAM,CAAC/B,GAAG,EAAEsC,IAAI,CAAC;SAC3B,MAAM;UACLT,QAAQ,CAACE,MAAM,CAAC/B,GAAG,EAAE,IAAI,CAACyC,YAAY,CAACzC,GAAG,CAAC,CAAC;;;;IAKlD;IACA,IAAI,IAAI,CAAChC,IAAI,CAAC0E,KAAK,IAAK,IAAI,CAACrD,UAAU,CAACN,MAAM,IAAI,QAAQ,IAAId,KAAK,CAACmB,EAAG,EAAE;MACvE,IAAI,CAAC1B,QAAQ,CAACiF,IAAI,EAAE;MACpB;MACA,QAAQ,IAAI,CAACtD,UAAU,CAACP,MAAM;QAC5B,KAAK,MAAM;UACT,IAAI,CAACtB,KAAK,CAACoF,IAAI,CAAC,IAAI,CAACvD,UAAU,CAACR,GAAG,EAAEgD,QAAQ,CAAC,CAAChC,SAAS,CACrDgD,GAAQ,IAAI;YACX,IAAIA,GAAG,CAACC,IAAI,EAAE;cACZ,IAAIA,IAAI,GAAGD,GAAG,CAACC,IAAI;cACnB,QAAQ,IAAI,CAACzD,UAAU,CAACN,MAAM;gBAC5B,KAAK,QAAQ;kBACX;kBACA+D,IAAI,CAACC,OAAO,CAAE/D,GAAG,IAAI;oBACnB,IAAI,CAACgE,KAAK,CAACC,EAAE,CAACjE,GAAG,CAACkE,GAAG,CAAClE,GAAG,CAAC,CAACmE,IAAI,EAAE;kBACnC,CAAC,CAAC;kBACF;gBACF,KAAK,MAAM;kBACT;kBACAL,IAAI,CAACC,OAAO,CAAE/D,GAAG,IAAI;oBACnB,IAAI,CAACgE,KAAK,CAACC,EAAE,CAACjE,GAAG,CAAC,GAAG,GAAGA,GAAG,CAACI,EAAE,CAAC,CAAC0D,IAAI,CAAC9D,GAAG,CAAC;kBAC3C,CAAC,CAAC;kBACF,IAAI,CAACgE,KAAK,CAACC,EAAE,CAACE,IAAI,EAAE;kBACpB;gBACF,KAAK,QAAQ;kBACX,IAAI,CAACH,KAAK,CAACC,EAAE,CACVjE,GAAG,CAAC,GAAG,GAAG8D,IAAI,CAAC,CAAC,CAAC,CAAC1D,EAAE,CAAC,CACrBX,MAAM,EAAE,CACR0E,IAAI,EAAE;kBACT;gBACF;kBACE;;;YAGN;YACA,IAAI,CAAChF,SAAS,CAACuD,IAAI,CAACmB,GAAG,CAAC;YACxB,OAAOA,GAAG;UACZ,CAAC,EACAO,KAAK,IAAI;YACRC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;YAClB,IAAIA,KAAK,CAACnD,cAAc,CAAC,aAAa,CAAC,EAAE;cACvCmD,KAAK,CAACG,WAAW,CAACR,OAAO,CAAEK,KAAK,IAAI;gBAClC,IAAI,CAACpF,IAAI,CAAC+B,QAAQ,CAACqD,KAAK,CAACI,IAAI,CAAC,CAACC,SAAS,CAAC;kBACvCC,WAAW,EAAEN,KAAK,CAACO;iBACpB,CAAC;cACJ,CAAC,CAAC;;YAEJ,IAAIP,KAAK,CAACnD,cAAc,CAAC,OAAO,CAAC,EAAE;cACjCvD,IAAI,CAACgE,IAAI,CAAC;gBACRpC,KAAK,EAAE,IAAI,CAACb,iBAAiB,CAACkD,OAAO,CAAC,OAAO,CAAC;gBAC9CC,IAAI,EAAEwC,KAAK,CAACA,KAAK;gBACjBtC,IAAI,EAAE,OAAO;gBACbK,WAAW,EAAE;kBACXC,aAAa,EAAE;;eAElB,CAAC;cACF,IAAI,CAAClC,YAAY,CAACwC,IAAI,CAAC0B,KAAK,CAACA,KAAK,CAAC;aACpC,MAAM;cACL,IAAI,CAAClE,YAAY,CAACwC,IAAI,CAAC0B,KAAK,CAAC;;YAE/B,QAAQ,IAAI,CAAC/D,UAAU,CAACN,MAAM;cAC5B,KAAK,QAAQ;gBACX;cACF,KAAK,MAAM;gBACT;cACF,KAAK,QAAQ;gBACX,IAAI,CAACwB,KAAK,EAAE;gBACZ;cACF;gBACE;;UAEN,CAAC,EACD,MAAK;YACH,IAAI,CAACjB,WAAW,GAAG,IAAI;YACvB,IAAI,CAACiB,KAAK,EAAE;UACd,CAAC,CACF;UACD;QACF;UACE;;;EAGR;EAEAqD,KAAKA,CAAA;IACH;IACA;IAEA,IAAI,CAACzE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACnB,IAAI,CAAC4F,KAAK,CAAC,EAAE,CAAC;IACnB,IAAI,CAAC3F,KAAK,GAAG,EAAE;IACf,IAAI,CAAC4F,aAAa,CAACC,SAAS,EAAE;EAChC;EAEA1B,iBAAiBA,CAACpC,GAAW,EAAEkC,MAAc;IAC3C,IACE,IAAI,CAAC7C,UAAU,CAACN,MAAM,IAAI,QAAQ,IAClC,IAAI,CAACM,UAAU,CAACY,cAAc,CAAC,UAAU,CAAC,IAC1C,IAAI,CAACZ,UAAU,CAACmB,QAAQ,EACxB;MACA;MACA0B,MAAM,GAAG,GAAG;;IAEd;IACA,MAAM6B,MAAM,GAAG/D,GAAG,CAACgE,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAIzC,MAAM,GAAG,QAAQW,MAAM,GAAG;IAC9B6B,MAAM,CAAChB,OAAO,CAAE/C,GAAG,IAAI;MACrBuB,MAAM,IAAI,IAAIvB,GAAG,GAAG;IACtB,CAAC,CAAC;IACF,OAAOuB,MAAM;EACf;EAEAhB,KAAKA,CAAA;IACH,IAAI,CAACxC,OAAO,CAAC2D,IAAI,CAAC;MAChB3C,MAAM,EAAE,IAAI,CAACM,UAAU,CAACN,MAAM;MAC9BkF,SAAS,EAAE,IAAI,CAACjG,IAAI,CAACwD,KAAK;MAC1BlC,WAAW,EAAE,IAAI,CAACA;KACnB,CAAC;IACF,IAAI,CAACsE,KAAK,EAAE;IACZ,IAAI,CAACrG,mBAAmB,CACrB2G,kBAAkB,CAAC,IAAI,CAAC7E,UAAU,CAAChB,SAAS,CAAC,CAC7C8F,UAAU,CAAC,KAAK,CAAC;EACtB;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACxG,YAAY,CAACyG,WAAW,EAAE;IAC/B,IAAI,CAACxG,WAAW,CAACwG,WAAW,EAAE;EAChC;EAEA5D,eAAeA,CAAC1B,MAAM;IACpB,IAAId,KAAK,GAAG,EAAE;IACd,IAAI,CAACkB,aAAa,GAAG,IAAI,CAAC6D,KAAK,CAACC,EAAE,CAACqB,IAAI,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC,CAACzB,IAAI,EAAE;IAElE,IACE,IAAI,CAAC3D,aAAa,CAAC8C,MAAM,IAAI,CAAC,IAC9B,CAAC,IAAI,CAAC5C,UAAU,CAACL,GAAG,IACpB,CAAC,IAAI,CAACK,UAAU,CAAC6C,MAAM,EACvB;MACA,IAAI,CAAC3B,KAAK,EAAE;MACZ,IAAIiE,OAAO,GAAG,EAAE;MAChB,QAAQzF,MAAM;QACZ,KAAK,MAAM;UACTyF,OAAO,GAAG,IAAI,CAAC/G,iBAAiB,CAACkD,OAAO,CACtC,6BAA6B,CAC9B;UACD;QACF,KAAK,QAAQ;UACX6D,OAAO,GAAG,IAAI,CAAC/G,iBAAiB,CAACkD,OAAO,CACtC,+BAA+B,CAChC;UACD;QACF,KAAK,QAAQ;UACX6D,OAAO,GAAG,IAAI,CAAC/G,iBAAiB,CAACkD,OAAO,CACtC,iCAAiC,CAClC;UACD;QACF;UACE;;MAEJjE,IAAI,CAACgE,IAAI,CAAC;QACRpC,KAAK,EAAE,IAAI,CAACb,iBAAiB,CAACkD,OAAO,CAAC,SAAS,CAAC;QAChDC,IAAI,EAAE4D,OAAO;QACb1D,IAAI,EAAE,SAAS;QACfG,iBAAiB,EAAE,IAAI,CAACxD,iBAAiB,CAACkD,OAAO,CAAC,IAAI,CAAC;QACvDQ,WAAW,EAAE;UACXC,aAAa,EAAE;;OAElB,CAAC,CAACE,IAAI,CAAEC,MAAM,IAAI;QACjB,OAAO,KAAK;MACd,CAAC,CAAC;MACF,OAAO,KAAK;;IAGd,IAAI,IAAI,CAAClC,UAAU,CAACL,GAAG,IAAI,IAAI,CAACK,UAAU,CAAC6C,MAAM,EAAE;MACjD,IAAIuC,QAAQ,GAAG,IAAI,CAACpF,UAAU,CAACL,GAAG,GAC9B,IAAI,IAAI,CAACK,UAAU,CAACL,GAAG,CAACI,EAAE,EAAE,GAC5B,IAAI,IAAI,CAACC,UAAU,CAAC6C,MAAM,GAAG;MACjC,IAAIlD,GAAG,GAAG,IAAI,CAACgE,KAAK,CAACC,EAAE,CAACjE,GAAG,CAACyF,QAAQ,CAAC,CAAC3B,IAAI,EAAE;MAE5C;MACA,IAAI9D,GAAG,EAAE;QACP,IAAI,CAACG,aAAa,GAAG,CAACH,GAAG,CAAC;QAC1B,IAAI,CAACc,MAAM,CAACiD,OAAO,CAAE5C,KAAU,IAAI;UACjC,IAAIqB,KAAK,GAAGxC,GAAG,CAACmB,KAAK,CAACH,GAAG,CAAC;UAC1B,IACEG,KAAK,CAACuE,KAAK,IACXvE,KAAK,CAACuE,KAAK,CAACzE,cAAc,CAAC,MAAM,CAAC,IAClCE,KAAK,CAACuE,KAAK,CAACC,IAAI,IAAI,MAAM,EAC1B;YACAnD,KAAK,GAAG7E,MAAM,CAAC6E,KAAK,CAAC,CAACoD,MAAM,CAAC,YAAY,CAAC;;UAE5C,IAAI,CAACpD,KAAK,EAAEA,KAAK,GAAG,IAAI,CAACgB,SAAS,CAACrC,KAAK,CAAC0E,YAAY,CAAC;UACtD5G,KAAK,CAACkC,KAAK,CAACH,GAAG,CAAC,GAAGwB,KAAK;QAC1B,CAAC,CAAC;;KAEL,MAAM;MACL,IAAI,CAAC1B,MAAM,CAACiD,OAAO,CAAE5C,KAAU,IAAI;QACjC,IAAIqB,KAAK,GAAG,IAAI,CAACrC,aAAa,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACH,GAAG,CAAC;QAC5C,IAAI,CAACwB,KAAK,EAAEA,KAAK,GAAG,IAAI,CAACgB,SAAS,CAACrC,KAAK,CAAC0E,YAAY,CAAC;QACtD5G,KAAK,CAACkC,KAAK,CAACH,GAAG,CAAC,GAAGwB,KAAK;QACxB;QACA,IACErB,KAAK,CAACuE,KAAK,IACXvE,KAAK,CAACuE,KAAK,CAACI,cAAc,IAC1B,IAAI,CAAC3F,aAAa,CAAC8C,MAAM,GAAG,CAAC,EAC7B;UACA9B,KAAK,CAAC4E,IAAI,GAAG,IAAI;SAClB,MAAM;UACL,IAAI5E,KAAK,CAAC6E,WAAW,EAAE;YACrB;YACA,IAAIC,IAAI,CAAC9E,KAAK,CAAC6E,WAAW,CAACD,IAAI,CAAC,EAAE;cAChC5E,KAAK,CAAC4E,IAAI,GAAG,IAAI;aAClB,MAAM;cACL5E,KAAK,CAAC4E,IAAI,GAAG,KAAK;;;;MAI1B,CAAC,CAAC;;IAEJ;IACA,IAAI,CAAC9G,KAAK,GAAGA,KAAK;IAClBoF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrF,KAAK,CAAC;IACvB,OAAO,IAAI;EACb;EAEAuE,SAASA,CAAChB,KAAK;IACb;IACA,IAAI,IAAI,CAAC7D,MAAM,CAACkB,GAAG,CAACqG,QAAQ,CAAC,OAAO,CAAC,IAAI1D,KAAK,IAAI,KAAK,EAAE;MACvD,OAAOA,KAAK;;IAGd,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI2D,SAAS,IAAI3D,KAAK,IAAI,KAAK,EAAE;MACzD,OAAO,EAAE;KACV,MAAM;MACL,OAAOA,KAAK;;EAEhB;EAAC,QAAA4D,CAAA;qBA9bU/H,sBAAsB,EAAAT,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA/I,EAAA,CAAAyI,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAjJ,EAAA,CAAAyI,iBAAA,CAAAS,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;UAAtB3I,sBAAsB;IAAA4I,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBACtB3J,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;QC9B/BG,EAAA,CAAAC,cAAA,aAA8B;QAOtBD,EAAA,CAAA0J,UAAA,sBAAAC,yDAAA;UAAA,OAAYF,GAAA,CAAA1H,MAAA,CAAA0H,GAAA,CAAApI,KAAA,CAAa;QAAA,EAAC;QAG1BrB,EAAA,CAAAC,cAAA,aAAwD;QAChCD,EAAA,CAAA0J,UAAA,mBAAAE,qDAAA;UAAA,OAASH,GAAA,CAAA9F,KAAA,EAAO;QAAA,EAAC;QACrC3D,EAAA,CAAA6J,SAAA,WAAkC;QAClC7J,EAAA,CAAAC,cAAA,WAAM;QAACD,EAAA,CAAAE,MAAA,GAAwB;;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAExCH,EAAA,CAAAC,cAAA,cAAiB;QAEbD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAIRH,EAAA,CAAAC,cAAA,eAAkC;QAChCD,EAAA,CAAA8J,UAAA,KAAAC,4CAAA,wBAYY;QACZ/J,EAAA,CAAA6J,SAAA,uBAIe;QACf7J,EAAA,CAAAC,cAAA,eAAqC;QAEeD,EAAA,CAAA0J,UAAA,mBAAAM,yDAAA;UAAA,OAASP,GAAA,CAAA9F,KAAA,EAAO;QAAA,EAAC;QAC/D3D,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAC,cAAA,eAAmB;QAEfD,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA5CfH,EAAA,CAAAK,SAAA,GAAkB;QAAlBL,EAAA,CAAAI,UAAA,cAAAqJ,GAAA,CAAArI,IAAA,CAAkB;QAOPpB,EAAA,CAAAK,SAAA,GAAwB;QAAxBL,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAO,WAAA,oBAAwB;QAI7BP,EAAA,CAAAK,SAAA,GACF;QADEL,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAO,WAAA,SAAAkJ,GAAA,CAAAhH,UAAA,CAAAf,KAAA,CAAA+H,GAAA,CAAAhH,UAAA,CAAAN,MAAA,QACF;QAMCnC,EAAA,CAAAK,SAAA,GAA8B;QAA9BL,EAAA,CAAAI,UAAA,SAAAqJ,GAAA,CAAAlH,aAAA,CAAA8C,MAAA,KAA8B;QAa/BrF,EAAA,CAAAK,SAAA,GAAa;QAAbL,EAAA,CAAAI,UAAA,SAAAqJ,GAAA,CAAArI,IAAA,CAAa,WAAAqI,GAAA,CAAAvG,MAAA,WAAAuG,GAAA,CAAApI,KAAA;QAOTrB,EAAA,CAAAK,SAAA,GACF;QADEL,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAO,WAAA,SAAAkJ,GAAA,CAAAhH,UAAA,CAAAX,MAAA,CAAA2H,GAAA,CAAAhH,UAAA,CAAAN,MAAA,EAAAH,MAAA,OACF;QAIEhC,EAAA,CAAAK,SAAA,GACF;QADEL,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAO,WAAA,SAAAkJ,GAAA,CAAAhH,UAAA,CAAAX,MAAA,CAAA2H,GAAA,CAAAhH,UAAA,CAAAN,MAAA,EAAAJ,MAAA,OACF", "names": ["EventEmitter", "FormGroup", "FormGroupDirective", "<PERSON><PERSON>", "moment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "EditorSidebarComponent", "constructor", "_coreSidebarService", "_http", "_translateService", "_loading", "router", "onCloseEvent", "onOpenEvent", "triggerClickOverlay", "onClose", "form", "model", "new_model", "onSuccess", "defaultParams", "editor_id", "title", "create", "edit", "remove", "button", "submit", "cancel", "url", "method", "action", "row", "formChange", "responseData", "rows_selected", "id", "new_params", "isSubmitted", "ngOnInit", "$", "fx", "off", "params", "fields_subject", "subscribe", "fields", "controls", "key", "hasOwnProperty", "find", "field", "removeControl", "setOverlayClickEvent", "setOnOpenEvent", "close", "use_data", "getSelectedRows", "fire", "instant", "text", "reverseButtons", "icon", "showCancelButton", "cancelButtonText", "confirmButtonText", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "then", "result", "value", "valueChanges", "emit", "controls_value", "onSubmit", "formData", "FormData", "append", "i", "length", "row_id", "new<PERSON>ey", "convertKey2Editor", "FileList", "file", "item", "parseNull", "paramsToPost", "valid", "show", "post", "res", "data", "for<PERSON>ach", "table", "dt", "add", "draw", "error", "console", "log", "fieldErrors", "name", "setErrors", "serverError", "status", "reset", "formDirective", "resetForm", "keyArr", "split", "formValue", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "rows", "selected", "message", "selector", "props", "type", "format", "defaultValue", "hideOnMultiple", "hide", "expressions", "eval", "includes", "undefined", "_", "ɵɵdirectiveInject", "i1", "CoreSidebarService", "i2", "HttpClient", "i3", "TranslateService", "i4", "LoadingService", "i5", "Router", "_2", "selectors", "viewQuery", "EditorSidebarComponent_Query", "rf", "ctx", "ɵɵlistener", "EditorSidebarComponent_Template_form_ngSubmit_3_listener", "EditorSidebarComponent_Template_div_click_6_listener", "ɵɵelement", "ɵɵtemplate", "EditorSidebarComponent_ngb_alert_16_Template", "EditorSidebarComponent_Template_button_click_20_listener"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\editor-sidebar\\editor-sidebar.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\editor-sidebar\\editor-sidebar.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n  ViewChild,\r\n  ViewEncapsulation\r\n} from '@angular/core';\r\nimport { FormGroup, FormGroupDirective } from '@angular/forms';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport Swal from 'sweetalert2';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport moment from 'moment';\r\nimport { Subject } from 'rxjs';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-editor-sidebar',\r\n  templateUrl: './editor-sidebar.component.html',\r\n  styleUrls: ['./editor-sidebar.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\n// get form directive\r\nexport class EditorSidebarComponent implements OnInit {\r\n  @ViewChild(FormGroupDirective) formDirective: FormGroupDirective;\r\n\r\n  constructor(\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _http: HttpClient,\r\n    public _translateService: TranslateService,\r\n    public _loading: LoadingService,\r\n    public router: Router\r\n  ) {\r\n  }\r\n\r\n  public onCloseEvent = new EventEmitter();\r\n  public onOpenEvent = new EventEmitter();\r\n\r\n  private triggerClickOverlay = false;\r\n\r\n  @Output() onClose = new EventEmitter();\r\n\r\n  form = new FormGroup({});\r\n  model: any = {};\r\n  @Input() new_model: any = {};\r\n\r\n  @Output() onSuccess: EventEmitter<any> = new EventEmitter();\r\n  @Input() fields: FormlyFieldConfig[];\r\n  @Input() fields_subject: Subject<FormlyFieldConfig[]>;\r\n  @Input() table: any;\r\n  @Input() onSubmit: Function;\r\n  @Input() params: any;\r\n  @Input() paramsToPost: Object;\r\n\r\n  defaultParams: EditorSidebarParams = {\r\n    editor_id: 'editor-sidebar',\r\n    title: {\r\n      create: 'Create new record',\r\n      edit: 'Edit record',\r\n      remove: 'Delete record'\r\n    },\r\n    button: {\r\n      create: {\r\n        submit: 'Create',\r\n        cancel: 'Cancel'\r\n      },\r\n      edit: {\r\n        submit: 'Update',\r\n        cancel: 'Cancel'\r\n      },\r\n      remove: {\r\n        submit: 'Delete',\r\n        cancel: 'Cancel'\r\n      }\r\n    },\r\n    url: '',\r\n    method: 'POST',\r\n    action: 'create',\r\n    row: null\r\n  };\r\n  @Output()\r\n  formChange: EventEmitter<any> = new EventEmitter();\r\n\r\n  @Output()\r\n  responseData: EventEmitter<any> = new EventEmitter();\r\n\r\n  public rows_selected = [{ id: '0' }];\r\n  public new_params: any = {};\r\n\r\n  private isSubmitted = false;\r\n\r\n  ngOnInit(): void {\r\n    $.fx.off = true;\r\n    // merge default params with input params\r\n    this.new_params = { ...this.defaultParams, ...this.params };\r\n    if (this.fields_subject) {\r\n      this.fields_subject.subscribe((fields) => {\r\n        this.fields = fields;\r\n        // sync form with fields, remove controls that not in fields\r\n        const controls = this.form.controls;\r\n        for (const key in controls) {\r\n          if (controls.hasOwnProperty(key)) {\r\n            if (!fields.find((field) => field.key === key)) {\r\n              this.form.removeControl(key);\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n\r\n    this._coreSidebarService.setOverlayClickEvent(\r\n      this.new_params.editor_id,\r\n      this.onCloseEvent\r\n    );\r\n\r\n    this._coreSidebarService.setOnOpenEvent(\r\n      this.new_params.editor_id,\r\n      this.onOpenEvent\r\n    );\r\n\r\n    this.onCloseEvent.subscribe(() => {\r\n      if (!this.triggerClickOverlay) {\r\n        this.triggerClickOverlay = true;\r\n        // this.reset();\r\n        this.close();\r\n      }\r\n    });\r\n\r\n    this.onOpenEvent.subscribe(() => {\r\n      this.triggerClickOverlay = false;\r\n      this.isSubmitted = false;\r\n\r\n      this.new_params = { ...this.defaultParams, ...this.params };\r\n\r\n      switch (this.new_params.action) {\r\n        case 'create':\r\n          if (\r\n            this.new_params.hasOwnProperty('use_data') &&\r\n            this.new_params.use_data\r\n          ) {\r\n            this.getSelectedRows(this.new_params.action);\r\n          } else {\r\n            this.rows_selected = [{ id: '0' }];\r\n          }\r\n          break;\r\n        case 'edit':\r\n          this.getSelectedRows(this.new_params.action);\r\n          break;\r\n        case 'remove':\r\n          this.close();\r\n          if (this.getSelectedRows(this.new_params.action)) {\r\n            // Show alert to confirm delete\r\n            Swal.fire({\r\n              title: this._translateService.instant('Are you sure to delete?'),\r\n              text: this._translateService.instant(\r\n                'You won\\'t be able to revert this!'\r\n              ),\r\n              reverseButtons: true,\r\n              icon: 'warning',\r\n              showCancelButton: true,\r\n              cancelButtonText: this._translateService.instant('Cancel'),\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              buttonsStyling: false,\r\n              customClass: {\r\n                confirmButton: 'btn btn-primary ml-1',\r\n                cancelButton: 'btn btn-outline-primary'\r\n              }\r\n            }).then((result) => {\r\n              if (result.value) {\r\n                this.submit(this.rows_selected[0]);\r\n              } else {\r\n                return;\r\n              }\r\n            });\r\n          }\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n      // merge model with input model\r\n      this.model = { ...this.model, ...this.new_model };\r\n      // console.log('fields', this.fields);\r\n      // console.log('form', this.form);\r\n    });\r\n    // form change event\r\n    this.form.valueChanges.subscribe((value) => {\r\n      this.formChange.emit(this.form);\r\n    });\r\n  }\r\n\r\n  submit(model) {\r\n\r\n    let controls_value = this.form.value;\r\n    if (this.onSubmit) {\r\n      this.onSubmit(model);\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('action', this.new_params.action);\r\n    // const row_id = this.rows_selected.length > 0 ? this.rows_selected[0].id : 0;\r\n\r\n    for (const key in controls_value) {\r\n      if (controls_value.hasOwnProperty(key)) {\r\n        for (let i = 0; i < this.rows_selected.length; i++) {\r\n          if (this.rows_selected[i].id) {\r\n            let row_id = this.rows_selected[i].id;\r\n            const newKey = this.convertKey2Editor(key, row_id);\r\n            // if value is FileList object\r\n            if (controls_value[key] instanceof FileList) {\r\n              // get file from FileList object\r\n              const file = controls_value[key].item(0);\r\n              // append file to formData\r\n              formData.append(newKey, file);\r\n            } else {\r\n              formData.append(newKey, this.parseNull(controls_value[key]));\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.paramsToPost) {\r\n      for (const key in this.paramsToPost) {\r\n        if (this.paramsToPost[key] instanceof FileList) {\r\n          const file = this.paramsToPost[key].item(0);\r\n          formData.append(key, file);\r\n        } else {\r\n          formData.append(key, this.paramsToPost[key]);\r\n        }\r\n      }\r\n    }\r\n\r\n    // if form is valid\r\n    if (this.form.valid || (this.new_params.action == 'remove' && model.id)) {\r\n      this._loading.show();\r\n      // send data to server\r\n      switch (this.new_params.method) {\r\n        case 'POST':\r\n          this._http.post(this.new_params.url, formData).subscribe(\r\n            (res: any) => {\r\n              if (res.data) {\r\n                let data = res.data;\r\n                switch (this.new_params.action) {\r\n                  case 'create':\r\n                    // add multiple rows\r\n                    data.forEach((row) => {\r\n                      this.table.dt.row.add(row).draw();\r\n                    });\r\n                    break;\r\n                  case 'edit':\r\n                    // update all rows with new data\r\n                    data.forEach((row) => {\r\n                      this.table.dt.row('#' + row.id).data(row);\r\n                    });\r\n                    this.table.dt.draw();\r\n                    break;\r\n                  case 'remove':\r\n                    this.table.dt\r\n                      .row('#' + data[0].id)\r\n                      .remove()\r\n                      .draw();\r\n                    break;\r\n                  default:\r\n                    break;\r\n                }\r\n              }\r\n              // run callback function\r\n              this.onSuccess.emit(res);\r\n              return res;\r\n            },\r\n            (error) => {\r\n              console.log(error);\r\n              if (error.hasOwnProperty('fieldErrors')) {\r\n                error.fieldErrors.forEach((error) => {\r\n                  this.form.controls[error.name].setErrors({\r\n                    serverError: error.status\r\n                  });\r\n                });\r\n              }\r\n              if (error.hasOwnProperty('error')) {\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Error'),\r\n                  text: error.error,\r\n                  icon: 'error',\r\n                  customClass: {\r\n                    confirmButton: 'btn btn-primary'\r\n                  }\r\n                });\r\n                this.responseData.emit(error.error);\r\n              } else {\r\n                this.responseData.emit(error);\r\n              }\r\n              switch (this.new_params.action) {\r\n                case 'create':\r\n                  break;\r\n                case 'edit':\r\n                  break;\r\n                case 'remove':\r\n                  this.close();\r\n                  break;\r\n                default:\r\n                  break;\r\n              }\r\n            },\r\n            () => {\r\n              this.isSubmitted = true;\r\n              this.close();\r\n            }\r\n          );\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  reset() {\r\n    // reset form directives\r\n    // this.close();\r\n\r\n    this.rows_selected = [];\r\n    this.form.reset({});\r\n    this.model = {};\r\n    this.formDirective.resetForm();\r\n  }\r\n\r\n  convertKey2Editor(key: string, row_id: string) {\r\n    if (\r\n      this.new_params.action == 'create' &&\r\n      this.new_params.hasOwnProperty('use_data') &&\r\n      this.new_params.use_data\r\n    ) {\r\n      // set  row_id to 0\r\n      row_id = '0';\r\n    }\r\n    // convert key to array\r\n    const keyArr = key.split('.');\r\n    let result = `data[${row_id}]`;\r\n    keyArr.forEach((key) => {\r\n      result += `[${key}]`;\r\n    });\r\n    return result;\r\n  }\r\n\r\n  close() {\r\n    this.onClose.emit({\r\n      action: this.new_params.action,\r\n      formValue: this.form.value,\r\n      isSubmitted: this.isSubmitted\r\n    });\r\n    this.reset();\r\n    this._coreSidebarService\r\n      .getSidebarRegistry(this.new_params.editor_id)\r\n      .toggleOpen(false);\r\n  }\r\n\r\n  // ondestroy unsubscribe events\r\n  ngOnDestroy() {\r\n    this.onCloseEvent.unsubscribe();\r\n    this.onOpenEvent.unsubscribe();\r\n  }\r\n\r\n  getSelectedRows(action) {\r\n    let model = {};\r\n    this.rows_selected = this.table.dt.rows({ selected: true }).data();\r\n\r\n    if (\r\n      this.rows_selected.length == 0 &&\r\n      !this.new_params.row &&\r\n      !this.new_params.row_id\r\n    ) {\r\n      this.close();\r\n      let message = '';\r\n      switch (action) {\r\n        case 'edit':\r\n          message = this._translateService.instant(\r\n            'Please select a row to edit'\r\n          );\r\n          break;\r\n        case 'remove':\r\n          message = this._translateService.instant(\r\n            'Please select a row to remove'\r\n          );\r\n          break;\r\n        case 'create':\r\n          message = this._translateService.instant(\r\n            'Please select a row to use data'\r\n          );\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n      Swal.fire({\r\n        title: this._translateService.instant('Warning'),\r\n        text: message,\r\n        icon: 'warning',\r\n        confirmButtonText: this._translateService.instant('OK'),\r\n        customClass: {\r\n          confirmButton: 'btn btn-primary'\r\n        }\r\n      }).then((result) => {\r\n        return false;\r\n      });\r\n      return false;\r\n    }\r\n\r\n    if (this.new_params.row || this.new_params.row_id) {\r\n      let selector = this.new_params.row\r\n        ? `#${this.new_params.row.id}`\r\n        : `#${this.new_params.row_id} `;\r\n      let row = this.table.dt.row(selector).data();\r\n\r\n      //  if found row\r\n      if (row) {\r\n        this.rows_selected = [row];\r\n        this.fields.forEach((field: any) => {\r\n          let value = row[field.key];\r\n          if (\r\n            field.props &&\r\n            field.props.hasOwnProperty('type') &&\r\n            field.props.type == 'date'\r\n          ) {\r\n            value = moment(value).format('YYYY-MM-DD');\r\n          }\r\n          if (!value) value = this.parseNull(field.defaultValue);\r\n          model[field.key] = value;\r\n        });\r\n      }\r\n    } else {\r\n      this.fields.forEach((field: any) => {\r\n        let value = this.rows_selected[0][field.key];\r\n        if (!value) value = this.parseNull(field.defaultValue);\r\n        model[field.key] = value;\r\n        // if field props has key hideOnMultiple and value is true and rows_selected.length > 1 then hide field\r\n        if (\r\n          field.props &&\r\n          field.props.hideOnMultiple &&\r\n          this.rows_selected.length > 1\r\n        ) {\r\n          field.hide = true;\r\n        } else {\r\n          if (field.expressions) {\r\n            // console.log(field.key, eval(field.expressions.hide));\r\n            if (eval(field.expressions.hide)) {\r\n              field.hide = true;\r\n            } else {\r\n              field.hide = false;\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    // merge model with new_model\r\n    this.model = model;\r\n    console.log(this.model);\r\n    return true;\r\n  }\r\n\r\n  parseNull(value) {\r\n    // get route is 'clubs'\r\n    if (this.router.url.includes('clubs') && value == 'TBD') {\r\n      return value;\r\n    }\r\n\r\n    if (value == null || value == undefined || value == 'TBD') {\r\n      return '';\r\n    } else {\r\n      return value;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"slideout-content\">\r\n  <!-- Modal to CRUD datatables-->\r\n  <div class=\"modalsd modal-slide-in sdfade\">\r\n    <div class=\"modal-dialog dialog-full\">\r\n      <form\r\n        class=\"modal-content pt-0\"\r\n        [formGroup]=\"form\"\r\n        (ngSubmit)=\"submit(model)\"\r\n        #formDirective=\"ngForm\"\r\n      >\r\n        <div class=\"modal-header mb-1 text-primary text-center\">\r\n          <div class=\"btn-back\" (click)=\"close()\">\r\n            <i class=\"fa fa-chevron-left\"></i>\r\n            <span> {{ 'Back' | translate }}</span>\r\n          </div>\r\n          <div class=\"col\">\r\n            <b class=\"h4 title-editor text-capitalize\">\r\n              {{ new_params.title[new_params.action] | translate }}\r\n            </b>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-body no-margin\">\r\n          <ngb-alert\r\n            *ngIf=\"rows_selected.length > 1\"\r\n            [type]=\"'warning'\"\r\n            [dismissible]=\"false\"\r\n          >\r\n            <h4 class=\"alert-heading\">{{ 'Warning' | translate }}</h4>\r\n            <div class=\"alert-body\">\r\n              {{\r\n                'You are editing multiple rows! Please be careful, data will be updated for all selected rows.'\r\n                  | translate\r\n              }}\r\n            </div>\r\n          </ngb-alert>\r\n          <formly-form\r\n            [form]=\"form\"\r\n            [fields]=\"fields\"\r\n            [model]=\"model\"\r\n          ></formly-form>\r\n          <div class=\"row justify-content-end\">\r\n            <div class=\"col-4\">\r\n              <button type=\"button\" class=\"btn btn-secondary\" (click)=\"close()\">\r\n                {{ new_params.button[new_params.action].cancel | translate }}\r\n              </button>\r\n            </div>\r\n            <div class=\"col-4\">\r\n              <button type=\"submit\" class=\"btn btn-primary\">\r\n                {{ new_params.button[new_params.action].submit | translate }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n  <!-- END Modal to CRUD datatables-->\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AsyncSubject } from '../AsyncSubject';\nimport { multicast } from './multicast';\nexport function publishLast() {\n  return source => multicast(new AsyncSubject())(source);\n}", "map": {"version": 3, "names": ["AsyncSubject", "multicast", "publishLast", "source"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/publishLast.js"], "sourcesContent": ["import { AsyncSubject } from '../AsyncSubject';\nimport { multicast } from './multicast';\nexport function publishLast() {\n    return (source) => multicast(new AsyncSubject())(source);\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC1B,OAAQC,MAAM,IAAKF,SAAS,CAAC,IAAID,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC;AAC5D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
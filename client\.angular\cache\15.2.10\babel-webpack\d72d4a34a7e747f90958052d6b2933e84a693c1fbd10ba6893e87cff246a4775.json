{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, Component, ViewEncapsulation, HostBinding, ContentChildren, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, fromEvent, Observable, combineLatest, timer } from 'rxjs';\nimport { map } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nclass VgStates {}\nVgStates.VG_ENDED = 'ended';\nVgStates.VG_PAUSED = 'paused';\nVgStates.VG_PLAYING = 'playing';\nVgStates.VG_LOADING = 'waiting';\n/** @nocollapse */\nVgStates.ɵfac = function VgStates_Factory(t) {\n  return new (t || VgStates)();\n};\n/** @nocollapse */\nVgStates.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: VgStates,\n  factory: VgStates.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgStates, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass VgApiService {\n  constructor() {\n    this.medias = {}; // TODO: refactor to Set<IPlayable>\n    this.playerReadyEvent = new EventEmitter(true);\n    this.isPlayerReady = false;\n  }\n  onPlayerReady(fsAPI) {\n    this.fsAPI = fsAPI;\n    this.isPlayerReady = true;\n    this.playerReadyEvent.emit(this);\n  }\n  getDefaultMedia() {\n    for (const item in this.medias) {\n      if (this.medias[item]) {\n        return this.medias[item];\n      }\n    }\n  }\n  getMasterMedia() {\n    let master;\n    for (const id in this.medias) {\n      if (this.medias[id].vgMaster === 'true' || this.medias[id].vgMaster === true) {\n        master = this.medias[id];\n        break;\n      }\n    }\n    return master || this.getDefaultMedia();\n  }\n  isMasterDefined() {\n    let result = false;\n    for (const id in this.medias) {\n      if (this.medias[id].vgMaster === 'true' || this.medias[id].vgMaster === true) {\n        result = true;\n        break;\n      }\n    }\n    return result;\n  }\n  getMediaById(id = null) {\n    let media = this.medias[id];\n    if (!id || id === '*') {\n      media = this;\n    }\n    return media;\n  }\n  play() {\n    for (const id in this.medias) {\n      if (this.medias[id]) {\n        this.medias[id].play();\n      }\n    }\n  }\n  pause() {\n    for (const id in this.medias) {\n      if (this.medias[id]) {\n        this.medias[id].pause();\n      }\n    }\n  }\n  get duration() {\n    return this.$$getAllProperties('duration');\n  }\n  set currentTime(seconds) {\n    this.$$setAllProperties('currentTime', seconds);\n  }\n  get currentTime() {\n    return this.$$getAllProperties('currentTime');\n  }\n  set state(state) {\n    this.$$setAllProperties('state', state);\n  }\n  get state() {\n    return this.$$getAllProperties('state');\n  }\n  set volume(volume) {\n    this.$$setAllProperties('volume', volume);\n  }\n  get volume() {\n    return this.$$getAllProperties('volume');\n  }\n  set playbackRate(rate) {\n    this.$$setAllProperties('playbackRate', rate);\n  }\n  get playbackRate() {\n    return this.$$getAllProperties('playbackRate');\n  }\n  get canPlay() {\n    return this.$$getAllProperties('canPlay');\n  }\n  get canPlayThrough() {\n    return this.$$getAllProperties('canPlayThrough');\n  }\n  get isMetadataLoaded() {\n    return this.$$getAllProperties('isMetadataLoaded');\n  }\n  get isWaiting() {\n    return this.$$getAllProperties('isWaiting');\n  }\n  get isCompleted() {\n    return this.$$getAllProperties('isCompleted');\n  }\n  get isLive() {\n    return this.$$getAllProperties('isLive');\n  }\n  get isMaster() {\n    return this.$$getAllProperties('isMaster');\n  }\n  get time() {\n    return this.$$getAllProperties('time');\n  }\n  get buffer() {\n    return this.$$getAllProperties('buffer');\n  }\n  get buffered() {\n    return this.$$getAllProperties('buffered');\n  }\n  get subscriptions() {\n    return this.$$getAllProperties('subscriptions');\n  }\n  get textTracks() {\n    return this.$$getAllProperties('textTracks');\n  }\n  seekTime(value, byPercent = false) {\n    for (const id in this.medias) {\n      if (this.medias[id]) {\n        this.$$seek(this.medias[id], value, byPercent);\n      }\n    }\n  }\n  $$seek(media, value, byPercent = false) {\n    let second;\n    let duration = media.duration;\n    if (byPercent) {\n      if (this.isMasterDefined()) {\n        duration = this.getMasterMedia().duration;\n      }\n      second = value * duration / 100;\n    } else {\n      second = value;\n    }\n    media.currentTime = second;\n  }\n  addTextTrack(type, label, language) {\n    for (const id in this.medias) {\n      if (this.medias[id]) {\n        this.$$addTextTrack(this.medias[id], type, label, language);\n      }\n    }\n  }\n  $$addTextTrack(media, type, label, language) {\n    media.addTextTrack(type, label, language);\n  }\n  $$getAllProperties(property) {\n    const medias = {};\n    let result;\n    for (const id in this.medias) {\n      if (this.medias[id]) {\n        medias[id] = this.medias[id];\n      }\n    }\n    const nMedias = Object.keys(medias).length;\n    switch (nMedias) {\n      case 0:\n        // Return default values until vgMedia is initialized\n        switch (property) {\n          case 'state':\n            result = VgStates.VG_PAUSED;\n            break;\n          case 'playbackRate':\n          case 'volume':\n            result = 1;\n            break;\n          case 'time':\n            result = {\n              current: 0,\n              total: 0,\n              left: 0\n            };\n            break;\n        }\n        break;\n      case 1:\n        // If there's only one media element then return the plain value\n        const firstMediaId = Object.keys(medias)[0];\n        result = medias[firstMediaId][property];\n        break;\n      default:\n        // TODO: return 'master' value\n        const master = this.getMasterMedia();\n        result = medias[master.id][property];\n    }\n    return result;\n  }\n  $$setAllProperties(property, value) {\n    for (const id in this.medias) {\n      if (this.medias[id]) {\n        this.medias[id][property] = value;\n      }\n    }\n  }\n  registerElement(elem) {\n    this.videogularElement = elem;\n  }\n  registerMedia(media) {\n    this.medias[media.id] = media;\n  }\n  unregisterMedia(media) {\n    delete this.medias[media.id];\n  }\n}\n/** @nocollapse */\nVgApiService.ɵfac = function VgApiService_Factory(t) {\n  return new (t || VgApiService)();\n};\n/** @nocollapse */\nVgApiService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: VgApiService,\n  factory: VgApiService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgApiService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass VgControlsHiddenService {\n  constructor() {\n    this.isHiddenSubject = new Subject();\n    this.isHidden = this.isHiddenSubject.asObservable();\n  }\n  state(hidden) {\n    this.isHiddenSubject.next(hidden);\n  }\n}\n/** @nocollapse */\nVgControlsHiddenService.ɵfac = function VgControlsHiddenService_Factory(t) {\n  return new (t || VgControlsHiddenService)();\n};\n/** @nocollapse */\nVgControlsHiddenService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: VgControlsHiddenService,\n  factory: VgControlsHiddenService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgControlsHiddenService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass VgUtilsService {\n  /**\n   * Inspired by Paul Irish\n   * https://gist.github.com/paulirish/211209\n   */\n  static getZIndex() {\n    let zIndex = 1;\n    let elementZIndex;\n    const tags = document.getElementsByTagName('*');\n    for (let i = 0, l = tags.length; i < l; i++) {\n      elementZIndex = parseInt(window.getComputedStyle(tags[i])['z-index'], 10);\n      if (elementZIndex > zIndex) {\n        zIndex = elementZIndex + 1;\n      }\n    }\n    return zIndex;\n  }\n  // Very simple mobile detection, not 100% reliable\n  static isMobileDevice() {\n    // return (\n    //   typeof window.screen.orientation !== 'undefined' ||\n    //   navigator.userAgent.indexOf('IEMobile') !== -1\n    // );\n    // window.orientation is deprecated and we should use window.screen.orientation\n    return typeof window.orientation !== 'undefined' || navigator.userAgent.indexOf('IEMobile') !== -1;\n  }\n  static isiOSDevice() {\n    return (navigator.userAgent.match(/ip(hone|ad|od)/i) || VgUtilsService.isIpadOS()) && !navigator.userAgent.match(/(iemobile)[\\/\\s]?([\\w\\.]*)/i);\n  }\n  static isIpadOS() {\n    return navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /MacIntel/.test(navigator.platform);\n  }\n  static isCordova() {\n    return document.URL.indexOf('http://') === -1 && document.URL.indexOf('https://') === -1;\n  }\n}\n/** @nocollapse */\nVgUtilsService.ɵfac = function VgUtilsService_Factory(t) {\n  return new (t || VgUtilsService)();\n};\n/** @nocollapse */\nVgUtilsService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: VgUtilsService,\n  factory: VgUtilsService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgUtilsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass VgFullscreenApiService {\n  constructor() {\n    this.nativeFullscreen = true;\n    this.isFullscreen = false;\n    this.onChangeFullscreen = new EventEmitter();\n  }\n  init(elem, medias) {\n    this.videogularElement = elem;\n    this.medias = medias;\n    const APIs = {\n      w3: {\n        enabled: 'fullscreenEnabled',\n        element: 'fullscreenElement',\n        request: 'requestFullscreen',\n        exit: 'exitFullscreen',\n        onchange: 'fullscreenchange',\n        onerror: 'fullscreenerror'\n      },\n      newWebkit: {\n        enabled: 'webkitFullscreenEnabled',\n        element: 'webkitFullscreenElement',\n        request: 'webkitRequestFullscreen',\n        exit: 'webkitExitFullscreen',\n        onchange: 'webkitfullscreenchange',\n        onerror: 'webkitfullscreenerror'\n      },\n      oldWebkit: {\n        enabled: 'webkitIsFullScreen',\n        element: 'webkitCurrentFullScreenElement',\n        request: 'webkitRequestFullScreen',\n        exit: 'webkitCancelFullScreen',\n        onchange: 'webkitfullscreenchange',\n        onerror: 'webkitfullscreenerror'\n      },\n      moz: {\n        enabled: 'mozFullScreen',\n        element: 'mozFullScreenElement',\n        request: 'mozRequestFullScreen',\n        exit: 'mozCancelFullScreen',\n        onchange: 'mozfullscreenchange',\n        onerror: 'mozfullscreenerror'\n      },\n      ios: {\n        enabled: 'webkitFullscreenEnabled',\n        element: 'webkitFullscreenElement',\n        request: 'webkitEnterFullscreen',\n        exit: 'webkitExitFullscreen',\n        onchange: 'webkitendfullscreen',\n        onerror: 'webkitfullscreenerror'\n      },\n      ms: {\n        enabled: 'msFullscreenEnabled',\n        element: 'msFullscreenElement',\n        request: 'msRequestFullscreen',\n        exit: 'msExitFullscreen',\n        onchange: 'MSFullscreenChange',\n        onerror: 'MSFullscreenError'\n      }\n    };\n    for (const browser in APIs) {\n      if (APIs[browser].enabled in document) {\n        this.polyfill = APIs[browser];\n        break;\n      }\n    }\n    if (VgUtilsService.isiOSDevice()) {\n      this.polyfill = APIs.ios;\n    }\n    this.isAvailable = this.polyfill != null;\n    if (this.polyfill == null) {\n      return;\n    }\n    let fsElemDispatcher;\n    switch (this.polyfill.onchange) {\n      // Mozilla dispatches the fullscreen change event from document, not the element\n      // See: https://bugzilla.mozilla.org/show_bug.cgi?id=724816#c3\n      case 'mozfullscreenchange':\n        fsElemDispatcher = document;\n        break;\n      // iOS dispatches the fullscreen change event from video element\n      case 'webkitendfullscreen':\n        fsElemDispatcher = this.medias.toArray()[0].elem;\n        break;\n      // HTML5 implementation dispatches the fullscreen change event from the element\n      default:\n        fsElemDispatcher = elem;\n    }\n    this.fsChangeSubscription = fromEvent(fsElemDispatcher, this.polyfill.onchange).subscribe(() => {\n      this.onFullscreenChange();\n    });\n  }\n  onFullscreenChange() {\n    this.isFullscreen = !!document[this.polyfill.element];\n    this.onChangeFullscreen.emit(this.isFullscreen);\n  }\n  toggleFullscreen(element = null) {\n    if (this.isFullscreen) {\n      this.exit();\n    } else {\n      this.request(element);\n    }\n  }\n  request(elem) {\n    if (!elem) {\n      elem = this.videogularElement;\n    }\n    this.isFullscreen = true;\n    this.onChangeFullscreen.emit(true);\n    // Perform native full screen support\n    if (this.isAvailable && this.nativeFullscreen) {\n      // Fullscreen for mobile devices\n      if (VgUtilsService.isMobileDevice()) {\n        // We should make fullscreen the video object if it doesn't have native fullscreen support\n        // Fallback! We can't set vg-player on fullscreen, only video/audio objects\n        if (!this.polyfill.enabled && elem === this.videogularElement || VgUtilsService.isiOSDevice()) {\n          elem = this.medias.toArray()[0].elem;\n        }\n        this.enterElementInFullScreen(elem);\n      } else {\n        this.enterElementInFullScreen(this.videogularElement);\n      }\n    }\n  }\n  enterElementInFullScreen(elem) {\n    elem[this.polyfill.request]();\n  }\n  exit() {\n    this.isFullscreen = false;\n    this.onChangeFullscreen.emit(false);\n    // Exit from native fullscreen\n    if (this.isAvailable && this.nativeFullscreen) {\n      document[this.polyfill.exit]();\n    }\n  }\n}\n/** @nocollapse */\nVgFullscreenApiService.ɵfac = function VgFullscreenApiService_Factory(t) {\n  return new (t || VgFullscreenApiService)();\n};\n/** @nocollapse */\nVgFullscreenApiService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: VgFullscreenApiService,\n  factory: VgFullscreenApiService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgFullscreenApiService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nclass VgEvents {}\nVgEvents.VG_ABORT = 'abort';\nVgEvents.VG_CAN_PLAY = 'canplay';\nVgEvents.VG_CAN_PLAY_THROUGH = 'canplaythrough';\nVgEvents.VG_DURATION_CHANGE = 'durationchange';\nVgEvents.VG_EMPTIED = 'emptied';\nVgEvents.VG_ENCRYPTED = 'encrypted';\nVgEvents.VG_ENDED = 'ended';\nVgEvents.VG_ERROR = 'error';\nVgEvents.VG_LOADED_DATA = 'loadeddata';\nVgEvents.VG_LOADED_METADATA = 'loadedmetadata';\nVgEvents.VG_LOAD_START = 'loadstart';\nVgEvents.VG_PAUSE = 'pause';\nVgEvents.VG_PLAY = 'play';\nVgEvents.VG_PLAYING = 'playing';\nVgEvents.VG_PROGRESS = 'progress';\nVgEvents.VG_RATE_CHANGE = 'ratechange';\nVgEvents.VG_SEEK = 'seek';\nVgEvents.VG_SEEKED = 'seeked';\nVgEvents.VG_SEEKING = 'seeking';\nVgEvents.VG_STALLED = 'stalled';\nVgEvents.VG_SUSPEND = 'suspend';\nVgEvents.VG_TIME_UPDATE = 'timeupdate';\nVgEvents.VG_VOLUME_CHANGE = 'volumechange';\nVgEvents.VG_WAITING = 'waiting';\nVgEvents.VG_LOAD = 'load';\nVgEvents.VG_ENTER = 'enter';\nVgEvents.VG_EXIT = 'exit';\nVgEvents.VG_START_ADS = 'startads';\nVgEvents.VG_END_ADS = 'endads';\n/** @nocollapse */\nVgEvents.ɵfac = function VgEvents_Factory(t) {\n  return new (t || VgEvents)();\n};\n/** @nocollapse */\nVgEvents.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: VgEvents,\n  factory: VgEvents.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgEvents, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass VgCuePointsDirective {\n  constructor(ref) {\n    this.ref = ref;\n    this.onEnterCuePoint = new EventEmitter();\n    this.onUpdateCuePoint = new EventEmitter();\n    this.onExitCuePoint = new EventEmitter();\n    this.onCompleteCuePoint = new EventEmitter();\n    this.subscriptions = [];\n    this.cuesSubscriptions = [];\n    this.totalCues = 0;\n  }\n  ngOnInit() {\n    this.onLoad$ = fromEvent(this.ref.nativeElement, VgEvents.VG_LOAD);\n    this.subscriptions.push(this.onLoad$.subscribe(this.onLoad.bind(this)));\n  }\n  onLoad(event) {\n    const cues = event.target.track.cues;\n    this.ref.nativeElement.cues = cues;\n    this.updateCuePoints(cues);\n  }\n  updateCuePoints(cues) {\n    this.cuesSubscriptions.forEach(s => s.unsubscribe());\n    for (let i = 0, l = cues.length; i < l; i++) {\n      this.onEnter$ = fromEvent(cues[i], VgEvents.VG_ENTER);\n      this.cuesSubscriptions.push(this.onEnter$.subscribe(this.onEnter.bind(this)));\n      this.onExit$ = fromEvent(cues[i], VgEvents.VG_EXIT);\n      this.cuesSubscriptions.push(this.onExit$.subscribe(this.onExit.bind(this)));\n    }\n  }\n  onEnter(event) {\n    this.onEnterCuePoint.emit(event.target);\n  }\n  onExit(event) {\n    this.onExitCuePoint.emit(event.target);\n  }\n  ngDoCheck() {\n    if (this.ref.nativeElement.track && this.ref.nativeElement.track.cues) {\n      const changes = this.totalCues !== this.ref.nativeElement.track.cues.length;\n      if (changes) {\n        this.totalCues = this.ref.nativeElement.track.cues.length;\n        this.ref.nativeElement.cues = this.ref.nativeElement.track.cues;\n        this.updateCuePoints(this.ref.nativeElement.track.cues);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgCuePointsDirective.ɵfac = function VgCuePointsDirective_Factory(t) {\n  return new (t || VgCuePointsDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n/** @nocollapse */\nVgCuePointsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: VgCuePointsDirective,\n  selectors: [[\"\", \"vgCuePoints\", \"\"]],\n  outputs: {\n    onEnterCuePoint: \"onEnterCuePoint\",\n    onUpdateCuePoint: \"onUpdateCuePoint\",\n    onExitCuePoint: \"onExitCuePoint\",\n    onCompleteCuePoint: \"onCompleteCuePoint\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgCuePointsDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[vgCuePoints]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    onEnterCuePoint: [{\n      type: Output\n    }],\n    onUpdateCuePoint: [{\n      type: Output\n    }],\n    onExitCuePoint: [{\n      type: Output\n    }],\n    onCompleteCuePoint: [{\n      type: Output\n    }]\n  });\n})();\nclass VgMediaDirective {\n  constructor(api, ref) {\n    this.api = api;\n    this.ref = ref;\n    this.state = VgStates.VG_PAUSED;\n    this.time = {\n      current: 0,\n      total: 0,\n      left: 0\n    };\n    this.buffer = {\n      end: 0\n    };\n    this.canPlay = false;\n    this.canPlayThrough = false;\n    this.isMetadataLoaded = false;\n    this.isWaiting = false;\n    this.isCompleted = false;\n    this.isLive = false;\n    this.isBufferDetected = false;\n    this.checkInterval = 200;\n    this.currentPlayPos = 0;\n    this.lastPlayPos = 0;\n    this.playAtferSync = false;\n    this.bufferDetected = new Subject();\n  }\n  ngOnInit() {\n    if (this.vgMedia.nodeName) {\n      // It's a native element\n      this.elem = this.vgMedia;\n    } else {\n      // It's an Angular Class\n      this.elem = this.vgMedia.elem;\n    }\n    // Just in case we're creating this vgMedia dynamically register again into API\n    this.api.registerMedia(this);\n    this.subscriptions = {\n      // Native events\n      abort: fromEvent(this.elem, VgEvents.VG_ABORT),\n      canPlay: fromEvent(this.elem, VgEvents.VG_CAN_PLAY),\n      canPlayThrough: fromEvent(this.elem, VgEvents.VG_CAN_PLAY_THROUGH),\n      durationChange: fromEvent(this.elem, VgEvents.VG_DURATION_CHANGE),\n      emptied: fromEvent(this.elem, VgEvents.VG_EMPTIED),\n      encrypted: fromEvent(this.elem, VgEvents.VG_ENCRYPTED),\n      ended: fromEvent(this.elem, VgEvents.VG_ENDED),\n      error: fromEvent(this.elem, VgEvents.VG_ERROR),\n      loadedData: fromEvent(this.elem, VgEvents.VG_LOADED_DATA),\n      loadedMetadata: fromEvent(this.elem, VgEvents.VG_LOADED_METADATA),\n      loadStart: fromEvent(this.elem, VgEvents.VG_LOAD_START),\n      pause: fromEvent(this.elem, VgEvents.VG_PAUSE),\n      play: fromEvent(this.elem, VgEvents.VG_PLAY),\n      playing: fromEvent(this.elem, VgEvents.VG_PLAYING),\n      progress: fromEvent(this.elem, VgEvents.VG_PROGRESS),\n      rateChange: fromEvent(this.elem, VgEvents.VG_RATE_CHANGE),\n      seeked: fromEvent(this.elem, VgEvents.VG_SEEKED),\n      seeking: fromEvent(this.elem, VgEvents.VG_SEEKING),\n      stalled: fromEvent(this.elem, VgEvents.VG_STALLED),\n      suspend: fromEvent(this.elem, VgEvents.VG_SUSPEND),\n      timeUpdate: fromEvent(this.elem, VgEvents.VG_TIME_UPDATE),\n      volumeChange: fromEvent(this.elem, VgEvents.VG_VOLUME_CHANGE),\n      waiting: fromEvent(this.elem, VgEvents.VG_WAITING),\n      // Advertisement only events\n      startAds: fromEvent(window, VgEvents.VG_START_ADS),\n      endAds: fromEvent(window, VgEvents.VG_END_ADS),\n      // See changes on <source> child elements to reload the video file\n      mutation: new Observable(observer => {\n        const domObs = new MutationObserver(mutations => {\n          observer.next(mutations);\n        });\n        domObs.observe(this.elem, {\n          childList: true,\n          attributes: true\n        });\n        return () => {\n          domObs.disconnect();\n        };\n      }),\n      // Custom buffering detection\n      bufferDetected: this.bufferDetected\n    };\n    this.mutationObs = this.subscriptions.mutation.subscribe(this.onMutation.bind(this));\n    this.canPlayObs = this.subscriptions.canPlay.subscribe(this.onCanPlay.bind(this));\n    this.canPlayThroughObs = this.subscriptions.canPlayThrough.subscribe(this.onCanPlayThrough.bind(this));\n    this.loadedMetadataObs = this.subscriptions.loadedMetadata.subscribe(this.onLoadMetadata.bind(this));\n    this.waitingObs = this.subscriptions.waiting.subscribe(this.onWait.bind(this));\n    this.progressObs = this.subscriptions.progress.subscribe(this.onProgress.bind(this));\n    this.endedObs = this.subscriptions.ended.subscribe(this.onComplete.bind(this));\n    this.playingObs = this.subscriptions.playing.subscribe(this.onStartPlaying.bind(this));\n    this.playObs = this.subscriptions.play.subscribe(this.onPlay.bind(this));\n    this.pauseObs = this.subscriptions.pause.subscribe(this.onPause.bind(this));\n    this.timeUpdateObs = this.subscriptions.timeUpdate.subscribe(this.onTimeUpdate.bind(this));\n    this.volumeChangeObs = this.subscriptions.volumeChange.subscribe(this.onVolumeChange.bind(this));\n    this.errorObs = this.subscriptions.error.subscribe(this.onError.bind(this));\n    if (this.vgMaster) {\n      this.api.playerReadyEvent.subscribe(() => {\n        this.prepareSync();\n      });\n    }\n  }\n  prepareSync() {\n    const canPlayAll = [];\n    for (const media in this.api.medias) {\n      if (this.api.medias[media]) {\n        canPlayAll.push(this.api.medias[media].subscriptions.canPlay);\n      }\n    }\n    this.canPlayAllSubscription = combineLatest(canPlayAll).pipe(map((...params) => {\n      const checkReadyState = event => {\n        if (!event?.target) {\n          return false;\n        }\n        return event.target.readyState === 4;\n      };\n      const allReady = params.some(checkReadyState);\n      if (allReady && !this.syncSubscription) {\n        this.startSync();\n        this.syncSubscription.unsubscribe();\n      }\n    })).subscribe();\n  }\n  startSync() {\n    this.syncSubscription = timer(0, 1000).subscribe(() => {\n      for (const media in this.api.medias) {\n        if (this.api.medias[media] !== this) {\n          const diff = this.api.medias[media].currentTime - this.currentTime;\n          if (diff < -0.3 || diff > 0.3) {\n            this.playAtferSync = this.state === VgStates.VG_PLAYING;\n            this.pause();\n            this.api.medias[media].pause();\n            this.api.medias[media].currentTime = this.currentTime;\n          } else {\n            if (this.playAtferSync) {\n              this.play();\n              this.api.medias[media].play();\n              this.playAtferSync = false;\n            }\n          }\n        }\n      }\n    });\n  }\n  onMutation(mutations) {\n    // Detect changes only for source elements or src attribute\n    for (let i = 0, l = mutations.length; i < l; i++) {\n      const mut = mutations[i];\n      if (mut.type === 'attributes' && mut.attributeName === 'src') {\n        // Only load src file if it's not a blob (for DASH / HLS sources)\n        if (mut.target.src && mut.target.src.length > 0 && mut.target.src.indexOf('blob:') < 0) {\n          this.loadMedia();\n          break;\n        }\n      } else if (mut.type === 'childList' && mut.removedNodes.length && mut.removedNodes[0].nodeName.toLowerCase() === 'source') {\n        this.loadMedia();\n        break;\n      }\n    }\n  }\n  loadMedia() {\n    this.vgMedia.pause();\n    this.vgMedia.currentTime = 0;\n    // Start buffering until we can play the media file\n    this.stopBufferCheck();\n    this.isBufferDetected = true;\n    this.bufferDetected.next(this.isBufferDetected);\n    // TODO: This is ugly, we should find something cleaner. For some reason a TimerObservable doesn't works.\n    setTimeout(() => this.vgMedia.load(), 10);\n  }\n  play() {\n    // short-circuit if already playing\n    if (this.playPromise || this.state !== VgStates.VG_PAUSED && this.state !== VgStates.VG_ENDED) {\n      return;\n    }\n    this.playPromise = this.vgMedia.play();\n    // browser has async play promise\n    if (this.playPromise && this.playPromise.then && this.playPromise.catch) {\n      this.playPromise.then(() => {\n        this.playPromise = null;\n      }).catch(() => {\n        this.playPromise = null;\n        // deliberately empty for the sake of eating console noise\n      });\n    }\n\n    return this.playPromise;\n  }\n  pause() {\n    // browser has async play promise\n    if (this.playPromise) {\n      this.playPromise.then(() => {\n        this.vgMedia.pause();\n      });\n    } else {\n      this.vgMedia.pause();\n    }\n  }\n  get id() {\n    // We should return undefined if vgMedia still doesn't exist\n    let result;\n    if (this.vgMedia) {\n      result = this.vgMedia.id;\n    }\n    return result;\n  }\n  get duration() {\n    return this.vgMedia.duration === Infinity ? this.specifiedDuration : this.vgMedia.duration;\n  }\n  set currentTime(seconds) {\n    this.vgMedia.currentTime = seconds;\n    // this.elem.dispatchEvent(new CustomEvent(VgEvents.VG_SEEK));\n  }\n\n  get currentTime() {\n    return this.vgMedia.currentTime;\n  }\n  set volume(volume) {\n    this.vgMedia.volume = volume;\n  }\n  get volume() {\n    return this.vgMedia.volume;\n  }\n  set playbackRate(rate) {\n    this.vgMedia.playbackRate = rate;\n  }\n  get playbackRate() {\n    return this.vgMedia.playbackRate;\n  }\n  get buffered() {\n    return this.vgMedia.buffered;\n  }\n  get textTracks() {\n    return this.vgMedia.textTracks;\n  }\n  // @ts-ignore\n  onCanPlay(event) {\n    this.isBufferDetected = false;\n    this.bufferDetected.next(this.isBufferDetected);\n    this.canPlay = true;\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onCanPlayThrough(event) {\n    this.isBufferDetected = false;\n    this.bufferDetected.next(this.isBufferDetected);\n    this.canPlayThrough = true;\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onLoadMetadata(event) {\n    this.isMetadataLoaded = true;\n    this.time = {\n      current: 0,\n      left: 0,\n      total: this.duration * 1000\n    };\n    this.state = VgStates.VG_PAUSED;\n    // Live streaming check\n    const t = Math.round(this.time.total);\n    this.isLive = t === Infinity;\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onWait(event) {\n    this.isWaiting = true;\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onComplete(event) {\n    this.isCompleted = true;\n    this.state = VgStates.VG_ENDED;\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onStartPlaying(event) {\n    this.state = VgStates.VG_PLAYING;\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onPlay(event) {\n    this.state = VgStates.VG_PLAYING;\n    if (this.vgMaster) {\n      if (!this.syncSubscription || this.syncSubscription.closed) {\n        this.startSync();\n      }\n    }\n    this.startBufferCheck();\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onPause(event) {\n    this.state = VgStates.VG_PAUSED;\n    if (this.vgMaster) {\n      if (!this.playAtferSync) {\n        this.syncSubscription.unsubscribe();\n      }\n    }\n    this.stopBufferCheck();\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onTimeUpdate(event) {\n    const end = this.buffered.length - 1;\n    this.time = {\n      current: this.currentTime * 1000,\n      total: this.time.total,\n      left: (this.duration - this.currentTime) * 1000\n    };\n    if (end >= 0) {\n      this.buffer = {\n        end: this.buffered.end(end) * 1000\n      };\n    }\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onProgress(event) {\n    const end = this.buffered.length - 1;\n    if (end >= 0) {\n      this.buffer = {\n        end: this.buffered.end(end) * 1000\n      };\n    }\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onVolumeChange(event) {\n    // TODO: Save to localstorage the current volume\n    this.ref.detectChanges();\n  }\n  // @ts-ignore\n  onError(event) {\n    // TODO: Handle error messages\n    this.ref.detectChanges();\n  }\n  // http://stackoverflow.com/a/23828241/779529\n  bufferCheck() {\n    const offset = 1 / this.checkInterval;\n    this.currentPlayPos = this.currentTime;\n    if (!this.isBufferDetected && this.currentPlayPos < this.lastPlayPos + offset) {\n      this.isBufferDetected = true;\n    }\n    if (this.isBufferDetected && this.currentPlayPos > this.lastPlayPos + offset) {\n      this.isBufferDetected = false;\n    }\n    // Prevent calls to bufferCheck after ngOnDestroy have been called\n    if (!this.bufferDetected.closed) {\n      this.bufferDetected.next(this.isBufferDetected);\n    }\n    this.lastPlayPos = this.currentPlayPos;\n  }\n  startBufferCheck() {\n    this.checkBufferSubscription = timer(0, this.checkInterval).subscribe(() => {\n      this.bufferCheck();\n    });\n  }\n  stopBufferCheck() {\n    if (this.checkBufferSubscription) {\n      this.checkBufferSubscription.unsubscribe();\n    }\n    this.isBufferDetected = false;\n    this.bufferDetected.next(this.isBufferDetected);\n  }\n  seekTime(value, byPercent = false) {\n    let second;\n    const duration = this.duration;\n    if (byPercent) {\n      second = value * duration / 100;\n    } else {\n      second = value;\n    }\n    this.currentTime = second;\n  }\n  addTextTrack(type, label, language, mode) {\n    const newTrack = this.vgMedia.addTextTrack(type, label, language);\n    if (mode) {\n      newTrack.mode = mode;\n    }\n    return newTrack;\n  }\n  ngOnDestroy() {\n    this.vgMedia.src = '';\n    this.mutationObs?.unsubscribe();\n    this.canPlayObs?.unsubscribe();\n    this.canPlayThroughObs?.unsubscribe();\n    this.loadedMetadataObs?.unsubscribe();\n    this.waitingObs?.unsubscribe();\n    this.progressObs?.unsubscribe();\n    this.endedObs?.unsubscribe();\n    this.playingObs?.unsubscribe();\n    this.playObs?.unsubscribe();\n    this.pauseObs?.unsubscribe();\n    this.timeUpdateObs?.unsubscribe();\n    this.volumeChangeObs?.unsubscribe();\n    this.errorObs?.unsubscribe();\n    this.checkBufferSubscription?.unsubscribe();\n    this.syncSubscription?.unsubscribe();\n    this.bufferDetected?.complete();\n    this.bufferDetected?.unsubscribe();\n    this.api.unregisterMedia(this);\n  }\n}\n/** @nocollapse */\nVgMediaDirective.ɵfac = function VgMediaDirective_Factory(t) {\n  return new (t || VgMediaDirective)(i0.ɵɵdirectiveInject(VgApiService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n/** @nocollapse */\nVgMediaDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: VgMediaDirective,\n  selectors: [[\"\", \"vgMedia\", \"\"]],\n  inputs: {\n    vgMedia: \"vgMedia\",\n    vgMaster: \"vgMaster\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgMediaDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[vgMedia]'\n    }]\n  }], function () {\n    return [{\n      type: VgApiService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    vgMedia: [{\n      type: Input\n    }],\n    vgMaster: [{\n      type: Input\n    }]\n  });\n})();\nclass VgPlayerComponent {\n  constructor(ref, api, fsAPI, controlsHidden) {\n    this.api = api;\n    this.fsAPI = fsAPI;\n    this.controlsHidden = controlsHidden;\n    this.isFullscreen = false;\n    this.isNativeFullscreen = false;\n    this.areControlsHidden = false;\n    this.onPlayerReady = new EventEmitter();\n    this.onMediaReady = new EventEmitter();\n    this.subscriptions = [];\n    this.elem = ref.nativeElement;\n    this.api.registerElement(this.elem);\n  }\n  ngAfterContentInit() {\n    this.medias.toArray().forEach(media => {\n      this.api.registerMedia(media);\n    });\n    this.fsAPI.init(this.elem, this.medias);\n    this.subscriptions.push(this.fsAPI.onChangeFullscreen.subscribe(this.onChangeFullscreen.bind(this)));\n    this.subscriptions.push(this.controlsHidden.isHidden.subscribe(this.onHideControls.bind(this)));\n    this.api.onPlayerReady(this.fsAPI);\n    this.onPlayerReady.emit(this.api);\n  }\n  onChangeFullscreen(fsState) {\n    if (!this.fsAPI.nativeFullscreen) {\n      this.isFullscreen = fsState;\n      this.zIndex = fsState ? VgUtilsService.getZIndex().toString() : 'auto';\n    } else {\n      this.isNativeFullscreen = fsState;\n    }\n  }\n  onHideControls(hidden) {\n    this.areControlsHidden = hidden;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(s => s.unsubscribe());\n  }\n}\n/** @nocollapse */\nVgPlayerComponent.ɵfac = function VgPlayerComponent_Factory(t) {\n  return new (t || VgPlayerComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(VgApiService), i0.ɵɵdirectiveInject(VgFullscreenApiService), i0.ɵɵdirectiveInject(VgControlsHiddenService));\n};\n/** @nocollapse */\nVgPlayerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: VgPlayerComponent,\n  selectors: [[\"vg-player\"]],\n  contentQueries: function VgPlayerComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, VgMediaDirective, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.medias = _t);\n    }\n  },\n  hostVars: 8,\n  hostBindings: function VgPlayerComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"z-index\", ctx.zIndex);\n      i0.ɵɵclassProp(\"fullscreen\", ctx.isFullscreen)(\"native-fullscreen\", ctx.isNativeFullscreen)(\"controls-hidden\", ctx.areControlsHidden);\n    }\n  },\n  outputs: {\n    onPlayerReady: \"onPlayerReady\",\n    onMediaReady: \"onMediaReady\"\n  },\n  features: [i0.ɵɵProvidersFeature([VgApiService, VgFullscreenApiService, VgControlsHiddenService])],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function VgPlayerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\"vg-player{font-family:videogular;position:relative;display:flex;width:100%;height:100%;overflow:hidden;background-color:#000}vg-player.fullscreen{position:fixed;left:0;top:0}vg-player.native-fullscreen.controls-hidden{cursor:none}\\n\"],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgPlayerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vg-player',\n      encapsulation: ViewEncapsulation.None,\n      template: `<ng-content></ng-content>`,\n      providers: [VgApiService, VgFullscreenApiService, VgControlsHiddenService],\n      styles: [\"vg-player{font-family:videogular;position:relative;display:flex;width:100%;height:100%;overflow:hidden;background-color:#000}vg-player.fullscreen{position:fixed;left:0;top:0}vg-player.native-fullscreen.controls-hidden{cursor:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: VgApiService\n    }, {\n      type: VgFullscreenApiService\n    }, {\n      type: VgControlsHiddenService\n    }];\n  }, {\n    isFullscreen: [{\n      type: HostBinding,\n      args: ['class.fullscreen']\n    }],\n    isNativeFullscreen: [{\n      type: HostBinding,\n      args: ['class.native-fullscreen']\n    }],\n    areControlsHidden: [{\n      type: HostBinding,\n      args: ['class.controls-hidden']\n    }],\n    zIndex: [{\n      type: HostBinding,\n      args: ['style.z-index']\n    }],\n    onPlayerReady: [{\n      type: Output\n    }],\n    onMediaReady: [{\n      type: Output\n    }],\n    medias: [{\n      type: ContentChildren,\n      args: [VgMediaDirective]\n    }]\n  });\n})();\nconst services = [VgApiService, VgControlsHiddenService, VgFullscreenApiService, VgUtilsService, VgEvents, VgStates];\nconst directives = [VgCuePointsDirective, VgMediaDirective];\nclass VgCoreModule {}\n/** @nocollapse */\nVgCoreModule.ɵfac = function VgCoreModule_Factory(t) {\n  return new (t || VgCoreModule)();\n};\n/** @nocollapse */\nVgCoreModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: VgCoreModule\n});\n/** @nocollapse */\nVgCoreModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [...services],\n  imports: [CommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(VgCoreModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      providers: [...services],\n      declarations: [...directives, VgPlayerComponent],\n      exports: [...directives, VgPlayerComponent]\n    }]\n  }], null, null);\n})();\nclass VgMediaElement {\n  get audioTracks() {\n    return null;\n  }\n  // @ts-ignore\n  addTextTrack(kind, label, language) {\n    return null;\n  }\n  // @ts-ignore\n  canPlayType(type) {\n    return null;\n  }\n  load() {}\n  msClearEffects() {}\n  msGetAsCastingSource() {\n    return null;\n  }\n  // @ts-ignore\n  msInsertAudioEffect(_activatableClassId, _effectRequired, _config) {}\n  // @ts-ignore\n  msSetMediaProtectionManager(mediaProtectionManager) {}\n  pause() {}\n  play() {\n    return null;\n  }\n  // @ts-ignore\n  setMediaKeys(mediaKeys) {\n    return null;\n  }\n  // @ts-ignore\n  addEventListener(_type, _listener, _useCapture) {}\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgApiService, VgControlsHiddenService, VgCoreModule, VgCuePointsDirective, VgEvents, VgFullscreenApiService, VgMediaDirective, VgMediaElement, VgPlayerComponent, VgStates, VgUtilsService };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "Directive", "Output", "Input", "Component", "ViewEncapsulation", "HostBinding", "ContentChildren", "NgModule", "CommonModule", "Subject", "fromEvent", "Observable", "combineLatest", "timer", "map", "_c0", "VgStates", "VG_ENDED", "VG_PAUSED", "VG_PLAYING", "VG_LOADING", "ɵfac", "VgStates_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "VgApiService", "constructor", "medias", "player<PERSON><PERSON>yEvent", "isPlayerReady", "onPlayerReady", "fsAPI", "emit", "getDefaultMedia", "item", "getMasterMedia", "master", "id", "vgMaster", "isMasterDefined", "result", "getMediaById", "media", "play", "pause", "duration", "$$getAllProperties", "currentTime", "seconds", "$$setAllProperties", "state", "volume", "playbackRate", "rate", "canPlay", "canPlayThrough", "isMetadataLoaded", "isWaiting", "isCompleted", "isLive", "isMaster", "time", "buffer", "buffered", "subscriptions", "textTracks", "seekTime", "value", "byPercent", "$$seek", "second", "addTextTrack", "label", "language", "$$addTextTrack", "property", "nMedias", "Object", "keys", "length", "current", "total", "left", "firstMediaId", "registerElement", "elem", "videogularElement", "registerMedia", "unregisterMedia", "VgApiService_Factory", "VgControlsHiddenService", "isHiddenSubject", "isHidden", "asObservable", "hidden", "next", "VgControlsHiddenService_Factory", "VgUtilsService", "getZIndex", "zIndex", "elementZIndex", "tags", "document", "getElementsByTagName", "i", "l", "parseInt", "window", "getComputedStyle", "isMobileDevice", "orientation", "navigator", "userAgent", "indexOf", "isiOSDevice", "match", "isIpadOS", "maxTouchPoints", "test", "platform", "<PERSON><PERSON><PERSON><PERSON>", "URL", "VgUtilsService_Factory", "VgFullscreenApiService", "nativeFullscreen", "isFullscreen", "onChangeFullscreen", "init", "APIs", "w3", "enabled", "element", "request", "exit", "onchange", "onerror", "newWebkit", "oldWebkit", "moz", "ios", "ms", "browser", "polyfill", "isAvailable", "fs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toArray", "fsChangeSubscription", "subscribe", "onFullscreenChange", "toggleFullscreen", "enterElementInFullScreen", "VgFullscreenApiService_Factory", "VgEvents", "VG_ABORT", "VG_CAN_PLAY", "VG_CAN_PLAY_THROUGH", "VG_DURATION_CHANGE", "VG_EMPTIED", "VG_ENCRYPTED", "VG_ERROR", "VG_LOADED_DATA", "VG_LOADED_METADATA", "VG_LOAD_START", "VG_PAUSE", "VG_PLAY", "VG_PROGRESS", "VG_RATE_CHANGE", "VG_SEEK", "VG_SEEKED", "VG_SEEKING", "VG_STALLED", "VG_SUSPEND", "VG_TIME_UPDATE", "VG_VOLUME_CHANGE", "VG_WAITING", "VG_LOAD", "VG_ENTER", "VG_EXIT", "VG_START_ADS", "VG_END_ADS", "VgEvents_Factory", "VgCuePointsDirective", "ref", "onEnterCuePoint", "onUpdateCuePoint", "onExitCuePoint", "onCompleteCuePoint", "cuesSubscriptions", "totalCues", "ngOnInit", "onLoad$", "nativeElement", "push", "onLoad", "bind", "event", "cues", "target", "track", "updateCuePoints", "for<PERSON>ach", "s", "unsubscribe", "onEnter$", "onEnter", "onExit$", "onExit", "ngDoCheck", "changes", "ngOnDestroy", "VgCuePointsDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "selector", "VgMediaDirective", "api", "end", "isBufferDetected", "checkInterval", "currentPlayPos", "lastPlayPos", "playAtferSync", "bufferDetected", "vgMedia", "nodeName", "abort", "durationChange", "emptied", "encrypted", "ended", "error", "loadedData", "loadedMetadata", "loadStart", "playing", "progress", "rateChange", "seeked", "seeking", "stalled", "suspend", "timeUpdate", "volumeChange", "waiting", "startAds", "endAds", "mutation", "observer", "domObs", "MutationObserver", "mutations", "observe", "childList", "attributes", "disconnect", "mutationObs", "onMutation", "canPlayObs", "onCanPlay", "canPlayThroughObs", "onCanPlayThrough", "loadedMetadataObs", "onLoadMetadata", "waitingObs", "onWait", "progressObs", "onProgress", "endedObs", "onComplete", "playingObs", "onStartPlaying", "playObs", "onPlay", "pauseObs", "onPause", "timeUpdateObs", "onTimeUpdate", "volumeChangeObs", "onVolumeChange", "errorObs", "onError", "prepareSync", "canPlayAll", "canPlayAllSubscription", "pipe", "params", "checkReadyState", "readyState", "allReady", "some", "syncSubscription", "startSync", "diff", "mut", "attributeName", "src", "loadMedia", "removedNodes", "toLowerCase", "stopBufferCheck", "setTimeout", "load", "playPromise", "then", "catch", "Infinity", "specifiedDuration", "detectChanges", "Math", "round", "closed", "startBufferCheck", "bufferCheck", "offset", "checkBufferSubscription", "mode", "newTrack", "complete", "VgMediaDirective_Factory", "ChangeDetectorRef", "inputs", "VgPlayerComponent", "controlsHidden", "isNativeFullscreen", "areControlsHidden", "onMediaReady", "ngAfterContentInit", "onHideControls", "fsState", "toString", "VgPlayerComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "VgPlayerComponent_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "VgPlayerComponent_HostBindings", "ɵɵstyleProp", "ɵɵclassProp", "features", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "template", "VgPlayerComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "None", "providers", "services", "directives", "VgCoreModule", "VgCoreModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "VgMediaElement", "audioTracks", "kind", "canPlayType", "msClearEffects", "msGetAsCastingSource", "msInsertAudioEffect", "_activatableClassId", "_effectRequired", "_config", "msSetMediaProtectionManager", "mediaProtectionManager", "setMediaKeys", "mediaKeys", "addEventListener", "_type", "_listener", "_useCapture"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@videogular/ngx-videogular/fesm2020/videogular-ngx-videogular-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, Component, ViewEncapsulation, HostBinding, ContentChildren, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, fromEvent, Observable, combineLatest, timer } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\nclass VgStates {\n}\nVgStates.VG_ENDED = 'ended';\nVgStates.VG_PAUSED = 'paused';\nVgStates.VG_PLAYING = 'playing';\nVgStates.VG_LOADING = 'waiting';\n/** @nocollapse */ VgStates.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStates, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ VgStates.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStates, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgStates, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass VgApiService {\n    constructor() {\n        this.medias = {}; // TODO: refactor to Set<IPlayable>\n        this.playerReadyEvent = new EventEmitter(true);\n        this.isPlayerReady = false;\n    }\n    onPlayerReady(fsAPI) {\n        this.fsAPI = fsAPI;\n        this.isPlayerReady = true;\n        this.playerReadyEvent.emit(this);\n    }\n    getDefaultMedia() {\n        for (const item in this.medias) {\n            if (this.medias[item]) {\n                return this.medias[item];\n            }\n        }\n    }\n    getMasterMedia() {\n        let master;\n        for (const id in this.medias) {\n            if (this.medias[id].vgMaster === 'true' ||\n                this.medias[id].vgMaster === true) {\n                master = this.medias[id];\n                break;\n            }\n        }\n        return master || this.getDefaultMedia();\n    }\n    isMasterDefined() {\n        let result = false;\n        for (const id in this.medias) {\n            if (this.medias[id].vgMaster === 'true' ||\n                this.medias[id].vgMaster === true) {\n                result = true;\n                break;\n            }\n        }\n        return result;\n    }\n    getMediaById(id = null) {\n        let media = this.medias[id];\n        if (!id || id === '*') {\n            media = this;\n        }\n        return media;\n    }\n    play() {\n        for (const id in this.medias) {\n            if (this.medias[id]) {\n                this.medias[id].play();\n            }\n        }\n    }\n    pause() {\n        for (const id in this.medias) {\n            if (this.medias[id]) {\n                this.medias[id].pause();\n            }\n        }\n    }\n    get duration() {\n        return this.$$getAllProperties('duration');\n    }\n    set currentTime(seconds) {\n        this.$$setAllProperties('currentTime', seconds);\n    }\n    get currentTime() {\n        return this.$$getAllProperties('currentTime');\n    }\n    set state(state) {\n        this.$$setAllProperties('state', state);\n    }\n    get state() {\n        return this.$$getAllProperties('state');\n    }\n    set volume(volume) {\n        this.$$setAllProperties('volume', volume);\n    }\n    get volume() {\n        return this.$$getAllProperties('volume');\n    }\n    set playbackRate(rate) {\n        this.$$setAllProperties('playbackRate', rate);\n    }\n    get playbackRate() {\n        return this.$$getAllProperties('playbackRate');\n    }\n    get canPlay() {\n        return this.$$getAllProperties('canPlay');\n    }\n    get canPlayThrough() {\n        return this.$$getAllProperties('canPlayThrough');\n    }\n    get isMetadataLoaded() {\n        return this.$$getAllProperties('isMetadataLoaded');\n    }\n    get isWaiting() {\n        return this.$$getAllProperties('isWaiting');\n    }\n    get isCompleted() {\n        return this.$$getAllProperties('isCompleted');\n    }\n    get isLive() {\n        return this.$$getAllProperties('isLive');\n    }\n    get isMaster() {\n        return this.$$getAllProperties('isMaster');\n    }\n    get time() {\n        return this.$$getAllProperties('time');\n    }\n    get buffer() {\n        return this.$$getAllProperties('buffer');\n    }\n    get buffered() {\n        return this.$$getAllProperties('buffered');\n    }\n    get subscriptions() {\n        return this.$$getAllProperties('subscriptions');\n    }\n    get textTracks() {\n        return this.$$getAllProperties('textTracks');\n    }\n    seekTime(value, byPercent = false) {\n        for (const id in this.medias) {\n            if (this.medias[id]) {\n                this.$$seek(this.medias[id], value, byPercent);\n            }\n        }\n    }\n    $$seek(media, value, byPercent = false) {\n        let second;\n        let duration = media.duration;\n        if (byPercent) {\n            if (this.isMasterDefined()) {\n                duration = this.getMasterMedia().duration;\n            }\n            second = (value * duration) / 100;\n        }\n        else {\n            second = value;\n        }\n        media.currentTime = second;\n    }\n    addTextTrack(type, label, language) {\n        for (const id in this.medias) {\n            if (this.medias[id]) {\n                this.$$addTextTrack(this.medias[id], type, label, language);\n            }\n        }\n    }\n    $$addTextTrack(media, type, label, language) {\n        media.addTextTrack(type, label, language);\n    }\n    $$getAllProperties(property) {\n        const medias = {};\n        let result;\n        for (const id in this.medias) {\n            if (this.medias[id]) {\n                medias[id] = this.medias[id];\n            }\n        }\n        const nMedias = Object.keys(medias).length;\n        switch (nMedias) {\n            case 0:\n                // Return default values until vgMedia is initialized\n                switch (property) {\n                    case 'state':\n                        result = VgStates.VG_PAUSED;\n                        break;\n                    case 'playbackRate':\n                    case 'volume':\n                        result = 1;\n                        break;\n                    case 'time':\n                        result = { current: 0, total: 0, left: 0 };\n                        break;\n                }\n                break;\n            case 1:\n                // If there's only one media element then return the plain value\n                const firstMediaId = Object.keys(medias)[0];\n                result = medias[firstMediaId][property];\n                break;\n            default:\n                // TODO: return 'master' value\n                const master = this.getMasterMedia();\n                result = medias[master.id][property];\n        }\n        return result;\n    }\n    $$setAllProperties(property, value) {\n        for (const id in this.medias) {\n            if (this.medias[id]) {\n                this.medias[id][property] = value;\n            }\n        }\n    }\n    registerElement(elem) {\n        this.videogularElement = elem;\n    }\n    registerMedia(media) {\n        this.medias[media.id] = media;\n    }\n    unregisterMedia(media) {\n        delete this.medias[media.id];\n    }\n}\n/** @nocollapse */ VgApiService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgApiService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ VgApiService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgApiService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgApiService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass VgControlsHiddenService {\n    constructor() {\n        this.isHiddenSubject = new Subject();\n        this.isHidden = this.isHiddenSubject.asObservable();\n    }\n    state(hidden) {\n        this.isHiddenSubject.next(hidden);\n    }\n}\n/** @nocollapse */ VgControlsHiddenService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsHiddenService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ VgControlsHiddenService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsHiddenService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgControlsHiddenService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass VgUtilsService {\n    /**\n     * Inspired by Paul Irish\n     * https://gist.github.com/paulirish/211209\n     */\n    static getZIndex() {\n        let zIndex = 1;\n        let elementZIndex;\n        const tags = document.getElementsByTagName('*');\n        for (let i = 0, l = tags.length; i < l; i++) {\n            elementZIndex = parseInt(window.getComputedStyle(tags[i])['z-index'], 10);\n            if (elementZIndex > zIndex) {\n                zIndex = elementZIndex + 1;\n            }\n        }\n        return zIndex;\n    }\n    // Very simple mobile detection, not 100% reliable\n    static isMobileDevice() {\n        // return (\n        //   typeof window.screen.orientation !== 'undefined' ||\n        //   navigator.userAgent.indexOf('IEMobile') !== -1\n        // );\n        // window.orientation is deprecated and we should use window.screen.orientation\n        return (typeof window.orientation !== 'undefined' ||\n            navigator.userAgent.indexOf('IEMobile') !== -1);\n    }\n    static isiOSDevice() {\n        return ((navigator.userAgent.match(/ip(hone|ad|od)/i) ||\n            VgUtilsService.isIpadOS()) &&\n            !navigator.userAgent.match(/(iemobile)[\\/\\s]?([\\w\\.]*)/i));\n    }\n    static isIpadOS() {\n        return (navigator.maxTouchPoints &&\n            navigator.maxTouchPoints > 2 &&\n            /MacIntel/.test(navigator.platform));\n    }\n    static isCordova() {\n        return (document.URL.indexOf('http://') === -1 &&\n            document.URL.indexOf('https://') === -1);\n    }\n}\n/** @nocollapse */ VgUtilsService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgUtilsService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ VgUtilsService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgUtilsService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgUtilsService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass VgFullscreenApiService {\n    constructor() {\n        this.nativeFullscreen = true;\n        this.isFullscreen = false;\n        this.onChangeFullscreen = new EventEmitter();\n    }\n    init(elem, medias) {\n        this.videogularElement = elem;\n        this.medias = medias;\n        const APIs = {\n            w3: {\n                enabled: 'fullscreenEnabled',\n                element: 'fullscreenElement',\n                request: 'requestFullscreen',\n                exit: 'exitFullscreen',\n                onchange: 'fullscreenchange',\n                onerror: 'fullscreenerror',\n            },\n            newWebkit: {\n                enabled: 'webkitFullscreenEnabled',\n                element: 'webkitFullscreenElement',\n                request: 'webkitRequestFullscreen',\n                exit: 'webkitExitFullscreen',\n                onchange: 'webkitfullscreenchange',\n                onerror: 'webkitfullscreenerror',\n            },\n            oldWebkit: {\n                enabled: 'webkitIsFullScreen',\n                element: 'webkitCurrentFullScreenElement',\n                request: 'webkitRequestFullScreen',\n                exit: 'webkitCancelFullScreen',\n                onchange: 'webkitfullscreenchange',\n                onerror: 'webkitfullscreenerror',\n            },\n            moz: {\n                enabled: 'mozFullScreen',\n                element: 'mozFullScreenElement',\n                request: 'mozRequestFullScreen',\n                exit: 'mozCancelFullScreen',\n                onchange: 'mozfullscreenchange',\n                onerror: 'mozfullscreenerror',\n            },\n            ios: {\n                enabled: 'webkitFullscreenEnabled',\n                element: 'webkitFullscreenElement',\n                request: 'webkitEnterFullscreen',\n                exit: 'webkitExitFullscreen',\n                onchange: 'webkitendfullscreen',\n                onerror: 'webkitfullscreenerror',\n            },\n            ms: {\n                enabled: 'msFullscreenEnabled',\n                element: 'msFullscreenElement',\n                request: 'msRequestFullscreen',\n                exit: 'msExitFullscreen',\n                onchange: 'MSFullscreenChange',\n                onerror: 'MSFullscreenError',\n            },\n        };\n        for (const browser in APIs) {\n            if (APIs[browser].enabled in document) {\n                this.polyfill = APIs[browser];\n                break;\n            }\n        }\n        if (VgUtilsService.isiOSDevice()) {\n            this.polyfill = APIs.ios;\n        }\n        this.isAvailable = this.polyfill != null;\n        if (this.polyfill == null) {\n            return;\n        }\n        let fsElemDispatcher;\n        switch (this.polyfill.onchange) {\n            // Mozilla dispatches the fullscreen change event from document, not the element\n            // See: https://bugzilla.mozilla.org/show_bug.cgi?id=724816#c3\n            case 'mozfullscreenchange':\n                fsElemDispatcher = document;\n                break;\n            // iOS dispatches the fullscreen change event from video element\n            case 'webkitendfullscreen':\n                fsElemDispatcher = this.medias.toArray()[0].elem;\n                break;\n            // HTML5 implementation dispatches the fullscreen change event from the element\n            default:\n                fsElemDispatcher = elem;\n        }\n        this.fsChangeSubscription = fromEvent(fsElemDispatcher, this.polyfill.onchange).subscribe(() => {\n            this.onFullscreenChange();\n        });\n    }\n    onFullscreenChange() {\n        this.isFullscreen = !!document[this.polyfill.element];\n        this.onChangeFullscreen.emit(this.isFullscreen);\n    }\n    toggleFullscreen(element = null) {\n        if (this.isFullscreen) {\n            this.exit();\n        }\n        else {\n            this.request(element);\n        }\n    }\n    request(elem) {\n        if (!elem) {\n            elem = this.videogularElement;\n        }\n        this.isFullscreen = true;\n        this.onChangeFullscreen.emit(true);\n        // Perform native full screen support\n        if (this.isAvailable && this.nativeFullscreen) {\n            // Fullscreen for mobile devices\n            if (VgUtilsService.isMobileDevice()) {\n                // We should make fullscreen the video object if it doesn't have native fullscreen support\n                // Fallback! We can't set vg-player on fullscreen, only video/audio objects\n                if ((!this.polyfill.enabled && elem === this.videogularElement) ||\n                    VgUtilsService.isiOSDevice()) {\n                    elem = this.medias.toArray()[0].elem;\n                }\n                this.enterElementInFullScreen(elem);\n            }\n            else {\n                this.enterElementInFullScreen(this.videogularElement);\n            }\n        }\n    }\n    enterElementInFullScreen(elem) {\n        elem[this.polyfill.request]();\n    }\n    exit() {\n        this.isFullscreen = false;\n        this.onChangeFullscreen.emit(false);\n        // Exit from native fullscreen\n        if (this.isAvailable && this.nativeFullscreen) {\n            document[this.polyfill.exit]();\n        }\n    }\n}\n/** @nocollapse */ VgFullscreenApiService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgFullscreenApiService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ VgFullscreenApiService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgFullscreenApiService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgFullscreenApiService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass VgEvents {\n}\nVgEvents.VG_ABORT = 'abort';\nVgEvents.VG_CAN_PLAY = 'canplay';\nVgEvents.VG_CAN_PLAY_THROUGH = 'canplaythrough';\nVgEvents.VG_DURATION_CHANGE = 'durationchange';\nVgEvents.VG_EMPTIED = 'emptied';\nVgEvents.VG_ENCRYPTED = 'encrypted';\nVgEvents.VG_ENDED = 'ended';\nVgEvents.VG_ERROR = 'error';\nVgEvents.VG_LOADED_DATA = 'loadeddata';\nVgEvents.VG_LOADED_METADATA = 'loadedmetadata';\nVgEvents.VG_LOAD_START = 'loadstart';\nVgEvents.VG_PAUSE = 'pause';\nVgEvents.VG_PLAY = 'play';\nVgEvents.VG_PLAYING = 'playing';\nVgEvents.VG_PROGRESS = 'progress';\nVgEvents.VG_RATE_CHANGE = 'ratechange';\nVgEvents.VG_SEEK = 'seek';\nVgEvents.VG_SEEKED = 'seeked';\nVgEvents.VG_SEEKING = 'seeking';\nVgEvents.VG_STALLED = 'stalled';\nVgEvents.VG_SUSPEND = 'suspend';\nVgEvents.VG_TIME_UPDATE = 'timeupdate';\nVgEvents.VG_VOLUME_CHANGE = 'volumechange';\nVgEvents.VG_WAITING = 'waiting';\nVgEvents.VG_LOAD = 'load';\nVgEvents.VG_ENTER = 'enter';\nVgEvents.VG_EXIT = 'exit';\nVgEvents.VG_START_ADS = 'startads';\nVgEvents.VG_END_ADS = 'endads';\n/** @nocollapse */ VgEvents.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgEvents, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ VgEvents.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgEvents, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgEvents, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\nclass VgCuePointsDirective {\n    constructor(ref) {\n        this.ref = ref;\n        this.onEnterCuePoint = new EventEmitter();\n        this.onUpdateCuePoint = new EventEmitter();\n        this.onExitCuePoint = new EventEmitter();\n        this.onCompleteCuePoint = new EventEmitter();\n        this.subscriptions = [];\n        this.cuesSubscriptions = [];\n        this.totalCues = 0;\n    }\n    ngOnInit() {\n        this.onLoad$ = fromEvent(this.ref.nativeElement, VgEvents.VG_LOAD);\n        this.subscriptions.push(this.onLoad$.subscribe(this.onLoad.bind(this)));\n    }\n    onLoad(event) {\n        const cues = event.target.track.cues;\n        this.ref.nativeElement.cues = cues;\n        this.updateCuePoints(cues);\n    }\n    updateCuePoints(cues) {\n        this.cuesSubscriptions.forEach((s) => s.unsubscribe());\n        for (let i = 0, l = cues.length; i < l; i++) {\n            this.onEnter$ = fromEvent(cues[i], VgEvents.VG_ENTER);\n            this.cuesSubscriptions.push(this.onEnter$.subscribe(this.onEnter.bind(this)));\n            this.onExit$ = fromEvent(cues[i], VgEvents.VG_EXIT);\n            this.cuesSubscriptions.push(this.onExit$.subscribe(this.onExit.bind(this)));\n        }\n    }\n    onEnter(event) {\n        this.onEnterCuePoint.emit(event.target);\n    }\n    onExit(event) {\n        this.onExitCuePoint.emit(event.target);\n    }\n    ngDoCheck() {\n        if (this.ref.nativeElement.track && this.ref.nativeElement.track.cues) {\n            const changes = this.totalCues !== this.ref.nativeElement.track.cues.length;\n            if (changes) {\n                this.totalCues = this.ref.nativeElement.track.cues.length;\n                this.ref.nativeElement.cues = this.ref.nativeElement.track.cues;\n                this.updateCuePoints(this.ref.nativeElement.track.cues);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgCuePointsDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgCuePointsDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ VgCuePointsDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgCuePointsDirective, selector: \"[vgCuePoints]\", outputs: { onEnterCuePoint: \"onEnterCuePoint\", onUpdateCuePoint: \"onUpdateCuePoint\", onExitCuePoint: \"onExitCuePoint\", onCompleteCuePoint: \"onCompleteCuePoint\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgCuePointsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[vgCuePoints]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { onEnterCuePoint: [{\n                type: Output\n            }], onUpdateCuePoint: [{\n                type: Output\n            }], onExitCuePoint: [{\n                type: Output\n            }], onCompleteCuePoint: [{\n                type: Output\n            }] } });\n\nclass VgMediaDirective {\n    constructor(api, ref) {\n        this.api = api;\n        this.ref = ref;\n        this.state = VgStates.VG_PAUSED;\n        this.time = { current: 0, total: 0, left: 0 };\n        this.buffer = { end: 0 };\n        this.canPlay = false;\n        this.canPlayThrough = false;\n        this.isMetadataLoaded = false;\n        this.isWaiting = false;\n        this.isCompleted = false;\n        this.isLive = false;\n        this.isBufferDetected = false;\n        this.checkInterval = 200;\n        this.currentPlayPos = 0;\n        this.lastPlayPos = 0;\n        this.playAtferSync = false;\n        this.bufferDetected = new Subject();\n    }\n    ngOnInit() {\n        if (this.vgMedia.nodeName) {\n            // It's a native element\n            this.elem = this.vgMedia;\n        }\n        else {\n            // It's an Angular Class\n            this.elem = this.vgMedia.elem;\n        }\n        // Just in case we're creating this vgMedia dynamically register again into API\n        this.api.registerMedia(this);\n        this.subscriptions = {\n            // Native events\n            abort: fromEvent(this.elem, VgEvents.VG_ABORT),\n            canPlay: fromEvent(this.elem, VgEvents.VG_CAN_PLAY),\n            canPlayThrough: fromEvent(this.elem, VgEvents.VG_CAN_PLAY_THROUGH),\n            durationChange: fromEvent(this.elem, VgEvents.VG_DURATION_CHANGE),\n            emptied: fromEvent(this.elem, VgEvents.VG_EMPTIED),\n            encrypted: fromEvent(this.elem, VgEvents.VG_ENCRYPTED),\n            ended: fromEvent(this.elem, VgEvents.VG_ENDED),\n            error: fromEvent(this.elem, VgEvents.VG_ERROR),\n            loadedData: fromEvent(this.elem, VgEvents.VG_LOADED_DATA),\n            loadedMetadata: fromEvent(this.elem, VgEvents.VG_LOADED_METADATA),\n            loadStart: fromEvent(this.elem, VgEvents.VG_LOAD_START),\n            pause: fromEvent(this.elem, VgEvents.VG_PAUSE),\n            play: fromEvent(this.elem, VgEvents.VG_PLAY),\n            playing: fromEvent(this.elem, VgEvents.VG_PLAYING),\n            progress: fromEvent(this.elem, VgEvents.VG_PROGRESS),\n            rateChange: fromEvent(this.elem, VgEvents.VG_RATE_CHANGE),\n            seeked: fromEvent(this.elem, VgEvents.VG_SEEKED),\n            seeking: fromEvent(this.elem, VgEvents.VG_SEEKING),\n            stalled: fromEvent(this.elem, VgEvents.VG_STALLED),\n            suspend: fromEvent(this.elem, VgEvents.VG_SUSPEND),\n            timeUpdate: fromEvent(this.elem, VgEvents.VG_TIME_UPDATE),\n            volumeChange: fromEvent(this.elem, VgEvents.VG_VOLUME_CHANGE),\n            waiting: fromEvent(this.elem, VgEvents.VG_WAITING),\n            // Advertisement only events\n            startAds: fromEvent(window, VgEvents.VG_START_ADS),\n            endAds: fromEvent(window, VgEvents.VG_END_ADS),\n            // See changes on <source> child elements to reload the video file\n            mutation: new Observable((observer) => {\n                const domObs = new MutationObserver((mutations) => {\n                    observer.next(mutations);\n                });\n                domObs.observe(this.elem, { childList: true, attributes: true });\n                return () => {\n                    domObs.disconnect();\n                };\n            }),\n            // Custom buffering detection\n            bufferDetected: this.bufferDetected,\n        };\n        this.mutationObs = this.subscriptions.mutation.subscribe(this.onMutation.bind(this));\n        this.canPlayObs = this.subscriptions.canPlay.subscribe(this.onCanPlay.bind(this));\n        this.canPlayThroughObs = this.subscriptions.canPlayThrough.subscribe(this.onCanPlayThrough.bind(this));\n        this.loadedMetadataObs = this.subscriptions.loadedMetadata.subscribe(this.onLoadMetadata.bind(this));\n        this.waitingObs = this.subscriptions.waiting.subscribe(this.onWait.bind(this));\n        this.progressObs = this.subscriptions.progress.subscribe(this.onProgress.bind(this));\n        this.endedObs = this.subscriptions.ended.subscribe(this.onComplete.bind(this));\n        this.playingObs = this.subscriptions.playing.subscribe(this.onStartPlaying.bind(this));\n        this.playObs = this.subscriptions.play.subscribe(this.onPlay.bind(this));\n        this.pauseObs = this.subscriptions.pause.subscribe(this.onPause.bind(this));\n        this.timeUpdateObs = this.subscriptions.timeUpdate.subscribe(this.onTimeUpdate.bind(this));\n        this.volumeChangeObs = this.subscriptions.volumeChange.subscribe(this.onVolumeChange.bind(this));\n        this.errorObs = this.subscriptions.error.subscribe(this.onError.bind(this));\n        if (this.vgMaster) {\n            this.api.playerReadyEvent.subscribe(() => {\n                this.prepareSync();\n            });\n        }\n    }\n    prepareSync() {\n        const canPlayAll = [];\n        for (const media in this.api.medias) {\n            if (this.api.medias[media]) {\n                canPlayAll.push(this.api.medias[media].subscriptions.canPlay);\n            }\n        }\n        this.canPlayAllSubscription = combineLatest(canPlayAll)\n            .pipe(map((...params) => {\n            const checkReadyState = (event) => {\n                if (!event?.target) {\n                    return false;\n                }\n                return event.target.readyState === 4;\n            };\n            const allReady = params.some(checkReadyState);\n            if (allReady && !this.syncSubscription) {\n                this.startSync();\n                this.syncSubscription.unsubscribe();\n            }\n        }))\n            .subscribe();\n    }\n    startSync() {\n        this.syncSubscription = timer(0, 1000).subscribe(() => {\n            for (const media in this.api.medias) {\n                if (this.api.medias[media] !== this) {\n                    const diff = this.api.medias[media].currentTime - this.currentTime;\n                    if (diff < -0.3 || diff > 0.3) {\n                        this.playAtferSync = this.state === VgStates.VG_PLAYING;\n                        this.pause();\n                        this.api.medias[media].pause();\n                        this.api.medias[media].currentTime = this.currentTime;\n                    }\n                    else {\n                        if (this.playAtferSync) {\n                            this.play();\n                            this.api.medias[media].play();\n                            this.playAtferSync = false;\n                        }\n                    }\n                }\n            }\n        });\n    }\n    onMutation(mutations) {\n        // Detect changes only for source elements or src attribute\n        for (let i = 0, l = mutations.length; i < l; i++) {\n            const mut = mutations[i];\n            if (mut.type === 'attributes' && mut.attributeName === 'src') {\n                // Only load src file if it's not a blob (for DASH / HLS sources)\n                if (mut.target.src &&\n                    mut.target.src.length > 0 &&\n                    mut.target.src.indexOf('blob:') < 0) {\n                    this.loadMedia();\n                    break;\n                }\n            }\n            else if (mut.type === 'childList' &&\n                mut.removedNodes.length &&\n                mut.removedNodes[0].nodeName.toLowerCase() === 'source') {\n                this.loadMedia();\n                break;\n            }\n        }\n    }\n    loadMedia() {\n        this.vgMedia.pause();\n        this.vgMedia.currentTime = 0;\n        // Start buffering until we can play the media file\n        this.stopBufferCheck();\n        this.isBufferDetected = true;\n        this.bufferDetected.next(this.isBufferDetected);\n        // TODO: This is ugly, we should find something cleaner. For some reason a TimerObservable doesn't works.\n        setTimeout(() => this.vgMedia.load(), 10);\n    }\n    play() {\n        // short-circuit if already playing\n        if (this.playPromise ||\n            (this.state !== VgStates.VG_PAUSED && this.state !== VgStates.VG_ENDED)) {\n            return;\n        }\n        this.playPromise = this.vgMedia.play();\n        // browser has async play promise\n        if (this.playPromise && this.playPromise.then && this.playPromise.catch) {\n            this.playPromise\n                .then(() => {\n                this.playPromise = null;\n            })\n                .catch(() => {\n                this.playPromise = null;\n                // deliberately empty for the sake of eating console noise\n            });\n        }\n        return this.playPromise;\n    }\n    pause() {\n        // browser has async play promise\n        if (this.playPromise) {\n            this.playPromise.then(() => {\n                this.vgMedia.pause();\n            });\n        }\n        else {\n            this.vgMedia.pause();\n        }\n    }\n    get id() {\n        // We should return undefined if vgMedia still doesn't exist\n        let result;\n        if (this.vgMedia) {\n            result = this.vgMedia.id;\n        }\n        return result;\n    }\n    get duration() {\n        return this.vgMedia.duration === Infinity\n            ? this.specifiedDuration\n            : this.vgMedia.duration;\n    }\n    set currentTime(seconds) {\n        this.vgMedia.currentTime = seconds;\n        // this.elem.dispatchEvent(new CustomEvent(VgEvents.VG_SEEK));\n    }\n    get currentTime() {\n        return this.vgMedia.currentTime;\n    }\n    set volume(volume) {\n        this.vgMedia.volume = volume;\n    }\n    get volume() {\n        return this.vgMedia.volume;\n    }\n    set playbackRate(rate) {\n        this.vgMedia.playbackRate = rate;\n    }\n    get playbackRate() {\n        return this.vgMedia.playbackRate;\n    }\n    get buffered() {\n        return this.vgMedia.buffered;\n    }\n    get textTracks() {\n        return this.vgMedia.textTracks;\n    }\n    // @ts-ignore\n    onCanPlay(event) {\n        this.isBufferDetected = false;\n        this.bufferDetected.next(this.isBufferDetected);\n        this.canPlay = true;\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onCanPlayThrough(event) {\n        this.isBufferDetected = false;\n        this.bufferDetected.next(this.isBufferDetected);\n        this.canPlayThrough = true;\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onLoadMetadata(event) {\n        this.isMetadataLoaded = true;\n        this.time = {\n            current: 0,\n            left: 0,\n            total: this.duration * 1000,\n        };\n        this.state = VgStates.VG_PAUSED;\n        // Live streaming check\n        const t = Math.round(this.time.total);\n        this.isLive = t === Infinity;\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onWait(event) {\n        this.isWaiting = true;\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onComplete(event) {\n        this.isCompleted = true;\n        this.state = VgStates.VG_ENDED;\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onStartPlaying(event) {\n        this.state = VgStates.VG_PLAYING;\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onPlay(event) {\n        this.state = VgStates.VG_PLAYING;\n        if (this.vgMaster) {\n            if (!this.syncSubscription || this.syncSubscription.closed) {\n                this.startSync();\n            }\n        }\n        this.startBufferCheck();\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onPause(event) {\n        this.state = VgStates.VG_PAUSED;\n        if (this.vgMaster) {\n            if (!this.playAtferSync) {\n                this.syncSubscription.unsubscribe();\n            }\n        }\n        this.stopBufferCheck();\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onTimeUpdate(event) {\n        const end = this.buffered.length - 1;\n        this.time = {\n            current: this.currentTime * 1000,\n            total: this.time.total,\n            left: (this.duration - this.currentTime) * 1000,\n        };\n        if (end >= 0) {\n            this.buffer = { end: this.buffered.end(end) * 1000 };\n        }\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onProgress(event) {\n        const end = this.buffered.length - 1;\n        if (end >= 0) {\n            this.buffer = { end: this.buffered.end(end) * 1000 };\n        }\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onVolumeChange(event) {\n        // TODO: Save to localstorage the current volume\n        this.ref.detectChanges();\n    }\n    // @ts-ignore\n    onError(event) {\n        // TODO: Handle error messages\n        this.ref.detectChanges();\n    }\n    // http://stackoverflow.com/a/23828241/779529\n    bufferCheck() {\n        const offset = 1 / this.checkInterval;\n        this.currentPlayPos = this.currentTime;\n        if (!this.isBufferDetected &&\n            this.currentPlayPos < this.lastPlayPos + offset) {\n            this.isBufferDetected = true;\n        }\n        if (this.isBufferDetected &&\n            this.currentPlayPos > this.lastPlayPos + offset) {\n            this.isBufferDetected = false;\n        }\n        // Prevent calls to bufferCheck after ngOnDestroy have been called\n        if (!this.bufferDetected.closed) {\n            this.bufferDetected.next(this.isBufferDetected);\n        }\n        this.lastPlayPos = this.currentPlayPos;\n    }\n    startBufferCheck() {\n        this.checkBufferSubscription = timer(0, this.checkInterval).subscribe(() => {\n            this.bufferCheck();\n        });\n    }\n    stopBufferCheck() {\n        if (this.checkBufferSubscription) {\n            this.checkBufferSubscription.unsubscribe();\n        }\n        this.isBufferDetected = false;\n        this.bufferDetected.next(this.isBufferDetected);\n    }\n    seekTime(value, byPercent = false) {\n        let second;\n        const duration = this.duration;\n        if (byPercent) {\n            second = (value * duration) / 100;\n        }\n        else {\n            second = value;\n        }\n        this.currentTime = second;\n    }\n    addTextTrack(type, label, language, mode) {\n        const newTrack = this.vgMedia.addTextTrack(type, label, language);\n        if (mode) {\n            newTrack.mode = mode;\n        }\n        return newTrack;\n    }\n    ngOnDestroy() {\n        this.vgMedia.src = '';\n        this.mutationObs?.unsubscribe();\n        this.canPlayObs?.unsubscribe();\n        this.canPlayThroughObs?.unsubscribe();\n        this.loadedMetadataObs?.unsubscribe();\n        this.waitingObs?.unsubscribe();\n        this.progressObs?.unsubscribe();\n        this.endedObs?.unsubscribe();\n        this.playingObs?.unsubscribe();\n        this.playObs?.unsubscribe();\n        this.pauseObs?.unsubscribe();\n        this.timeUpdateObs?.unsubscribe();\n        this.volumeChangeObs?.unsubscribe();\n        this.errorObs?.unsubscribe();\n        this.checkBufferSubscription?.unsubscribe();\n        this.syncSubscription?.unsubscribe();\n        this.bufferDetected?.complete();\n        this.bufferDetected?.unsubscribe();\n        this.api.unregisterMedia(this);\n    }\n}\n/** @nocollapse */ VgMediaDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgMediaDirective, deps: [{ token: VgApiService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n/** @nocollapse */ VgMediaDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgMediaDirective, selector: \"[vgMedia]\", inputs: { vgMedia: \"vgMedia\", vgMaster: \"vgMaster\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgMediaDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[vgMedia]',\n                }]\n        }], ctorParameters: function () { return [{ type: VgApiService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { vgMedia: [{\n                type: Input\n            }], vgMaster: [{\n                type: Input\n            }] } });\n\nclass VgPlayerComponent {\n    constructor(ref, api, fsAPI, controlsHidden) {\n        this.api = api;\n        this.fsAPI = fsAPI;\n        this.controlsHidden = controlsHidden;\n        this.isFullscreen = false;\n        this.isNativeFullscreen = false;\n        this.areControlsHidden = false;\n        this.onPlayerReady = new EventEmitter();\n        this.onMediaReady = new EventEmitter();\n        this.subscriptions = [];\n        this.elem = ref.nativeElement;\n        this.api.registerElement(this.elem);\n    }\n    ngAfterContentInit() {\n        this.medias.toArray().forEach((media) => {\n            this.api.registerMedia(media);\n        });\n        this.fsAPI.init(this.elem, this.medias);\n        this.subscriptions.push(this.fsAPI.onChangeFullscreen.subscribe(this.onChangeFullscreen.bind(this)));\n        this.subscriptions.push(this.controlsHidden.isHidden.subscribe(this.onHideControls.bind(this)));\n        this.api.onPlayerReady(this.fsAPI);\n        this.onPlayerReady.emit(this.api);\n    }\n    onChangeFullscreen(fsState) {\n        if (!this.fsAPI.nativeFullscreen) {\n            this.isFullscreen = fsState;\n            this.zIndex = fsState ? VgUtilsService.getZIndex().toString() : 'auto';\n        }\n        else {\n            this.isNativeFullscreen = fsState;\n        }\n    }\n    onHideControls(hidden) {\n        this.areControlsHidden = hidden;\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach((s) => s.unsubscribe());\n    }\n}\n/** @nocollapse */ VgPlayerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgPlayerComponent, deps: [{ token: i0.ElementRef }, { token: VgApiService }, { token: VgFullscreenApiService }, { token: VgControlsHiddenService }], target: i0.ɵɵFactoryTarget.Component });\n/** @nocollapse */ VgPlayerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: VgPlayerComponent, selector: \"vg-player\", outputs: { onPlayerReady: \"onPlayerReady\", onMediaReady: \"onMediaReady\" }, host: { properties: { \"class.fullscreen\": \"this.isFullscreen\", \"class.native-fullscreen\": \"this.isNativeFullscreen\", \"class.controls-hidden\": \"this.areControlsHidden\", \"style.z-index\": \"this.zIndex\" } }, providers: [VgApiService, VgFullscreenApiService, VgControlsHiddenService], queries: [{ propertyName: \"medias\", predicate: VgMediaDirective }], ngImport: i0, template: `<ng-content></ng-content>`, isInline: true, styles: [\"vg-player{font-family:videogular;position:relative;display:flex;width:100%;height:100%;overflow:hidden;background-color:#000}vg-player.fullscreen{position:fixed;left:0;top:0}vg-player.native-fullscreen.controls-hidden{cursor:none}\\n\"], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgPlayerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'vg-player', encapsulation: ViewEncapsulation.None, template: `<ng-content></ng-content>`, providers: [VgApiService, VgFullscreenApiService, VgControlsHiddenService], styles: [\"vg-player{font-family:videogular;position:relative;display:flex;width:100%;height:100%;overflow:hidden;background-color:#000}vg-player.fullscreen{position:fixed;left:0;top:0}vg-player.native-fullscreen.controls-hidden{cursor:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: VgApiService }, { type: VgFullscreenApiService }, { type: VgControlsHiddenService }]; }, propDecorators: { isFullscreen: [{\n                type: HostBinding,\n                args: ['class.fullscreen']\n            }], isNativeFullscreen: [{\n                type: HostBinding,\n                args: ['class.native-fullscreen']\n            }], areControlsHidden: [{\n                type: HostBinding,\n                args: ['class.controls-hidden']\n            }], zIndex: [{\n                type: HostBinding,\n                args: ['style.z-index']\n            }], onPlayerReady: [{\n                type: Output\n            }], onMediaReady: [{\n                type: Output\n            }], medias: [{\n                type: ContentChildren,\n                args: [VgMediaDirective]\n            }] } });\n\nconst services = [\n    VgApiService,\n    VgControlsHiddenService,\n    VgFullscreenApiService,\n    VgUtilsService,\n    VgEvents,\n    VgStates\n];\nconst directives = [\n    VgCuePointsDirective,\n    VgMediaDirective\n];\nclass VgCoreModule {\n}\n/** @nocollapse */ VgCoreModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgCoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ VgCoreModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: VgCoreModule, declarations: [VgCuePointsDirective,\n        VgMediaDirective, VgPlayerComponent], imports: [CommonModule], exports: [VgCuePointsDirective,\n        VgMediaDirective, VgPlayerComponent] });\n/** @nocollapse */ VgCoreModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgCoreModule, providers: [...services], imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: VgCoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    providers: [...services],\n                    declarations: [...directives, VgPlayerComponent],\n                    exports: [...directives, VgPlayerComponent]\n                }]\n        }] });\n\nclass VgMediaElement {\n    get audioTracks() {\n        return null;\n    }\n    // @ts-ignore\n    addTextTrack(kind, label, language) {\n        return null;\n    }\n    // @ts-ignore\n    canPlayType(type) {\n        return null;\n    }\n    load() { }\n    msClearEffects() { }\n    msGetAsCastingSource() {\n        return null;\n    }\n    // @ts-ignore\n    msInsertAudioEffect(_activatableClassId, _effectRequired, _config) { }\n    // @ts-ignore\n    msSetMediaProtectionManager(mediaProtectionManager) { }\n    pause() { }\n    play() {\n        return null;\n    }\n    // @ts-ignore\n    setMediaKeys(mediaKeys) {\n        return null;\n    }\n    // @ts-ignore\n    addEventListener(_type, _listener, _useCapture) { }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VgApiService, VgControlsHiddenService, VgCoreModule, VgCuePointsDirective, VgEvents, VgFullscreenApiService, VgMediaDirective, VgMediaElement, VgPlayerComponent, VgStates, VgUtilsService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACxJ,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,QAAQ,MAAM;AAC3E,SAASC,GAAG,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAErC,MAAMC,QAAQ,CAAC;AAEfA,QAAQ,CAACC,QAAQ,GAAG,OAAO;AAC3BD,QAAQ,CAACE,SAAS,GAAG,QAAQ;AAC7BF,QAAQ,CAACG,UAAU,GAAG,SAAS;AAC/BH,QAAQ,CAACI,UAAU,GAAG,SAAS;AAC/B;AAAmBJ,QAAQ,CAACK,IAAI,YAAAC,iBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFP,QAAQ;AAAA,CAAoD;AACpL;AAAmBA,QAAQ,CAACQ,KAAK,kBAD6E3B,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EACYV,QAAQ;EAAAW,OAAA,EAARX,QAAQ,CAAAK,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC3J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAF8GhC,EAAE,CAAAiC,iBAAA,CAErBd,QAAQ,EAAc,CAAC;IACtGe,IAAI,EAAEjC,UAAU;IAChBkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,GAAG,IAAIrC,YAAY,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACsC,aAAa,GAAG,KAAK;EAC9B;EACAC,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC;EACpC;EACAC,eAAeA,CAAA,EAAG;IACd,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACP,MAAM,EAAE;MAC5B,IAAI,IAAI,CAACA,MAAM,CAACO,IAAI,CAAC,EAAE;QACnB,OAAO,IAAI,CAACP,MAAM,CAACO,IAAI,CAAC;MAC5B;IACJ;EACJ;EACAC,cAAcA,CAAA,EAAG;IACb,IAAIC,MAAM;IACV,KAAK,MAAMC,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,CAACC,QAAQ,KAAK,MAAM,IACnC,IAAI,CAACX,MAAM,CAACU,EAAE,CAAC,CAACC,QAAQ,KAAK,IAAI,EAAE;QACnCF,MAAM,GAAG,IAAI,CAACT,MAAM,CAACU,EAAE,CAAC;QACxB;MACJ;IACJ;IACA,OAAOD,MAAM,IAAI,IAAI,CAACH,eAAe,CAAC,CAAC;EAC3C;EACAM,eAAeA,CAAA,EAAG;IACd,IAAIC,MAAM,GAAG,KAAK;IAClB,KAAK,MAAMH,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,CAACC,QAAQ,KAAK,MAAM,IACnC,IAAI,CAACX,MAAM,CAACU,EAAE,CAAC,CAACC,QAAQ,KAAK,IAAI,EAAE;QACnCE,MAAM,GAAG,IAAI;QACb;MACJ;IACJ;IACA,OAAOA,MAAM;EACjB;EACAC,YAAYA,CAACJ,EAAE,GAAG,IAAI,EAAE;IACpB,IAAIK,KAAK,GAAG,IAAI,CAACf,MAAM,CAACU,EAAE,CAAC;IAC3B,IAAI,CAACA,EAAE,IAAIA,EAAE,KAAK,GAAG,EAAE;MACnBK,KAAK,GAAG,IAAI;IAChB;IACA,OAAOA,KAAK;EAChB;EACAC,IAAIA,CAAA,EAAG;IACH,KAAK,MAAMN,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,EAAE;QACjB,IAAI,CAACV,MAAM,CAACU,EAAE,CAAC,CAACM,IAAI,CAAC,CAAC;MAC1B;IACJ;EACJ;EACAC,KAAKA,CAAA,EAAG;IACJ,KAAK,MAAMP,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,EAAE;QACjB,IAAI,CAACV,MAAM,CAACU,EAAE,CAAC,CAACO,KAAK,CAAC,CAAC;MAC3B;IACJ;EACJ;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,kBAAkB,CAAC,UAAU,CAAC;EAC9C;EACA,IAAIC,WAAWA,CAACC,OAAO,EAAE;IACrB,IAAI,CAACC,kBAAkB,CAAC,aAAa,EAAED,OAAO,CAAC;EACnD;EACA,IAAID,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,kBAAkB,CAAC,aAAa,CAAC;EACjD;EACA,IAAII,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACD,kBAAkB,CAAC,OAAO,EAAEC,KAAK,CAAC;EAC3C;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACJ,kBAAkB,CAAC,OAAO,CAAC;EAC3C;EACA,IAAIK,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACF,kBAAkB,CAAC,QAAQ,EAAEE,MAAM,CAAC;EAC7C;EACA,IAAIA,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,kBAAkB,CAAC,QAAQ,CAAC;EAC5C;EACA,IAAIM,YAAYA,CAACC,IAAI,EAAE;IACnB,IAAI,CAACJ,kBAAkB,CAAC,cAAc,EAAEI,IAAI,CAAC;EACjD;EACA,IAAID,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACN,kBAAkB,CAAC,cAAc,CAAC;EAClD;EACA,IAAIQ,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACR,kBAAkB,CAAC,SAAS,CAAC;EAC7C;EACA,IAAIS,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACT,kBAAkB,CAAC,gBAAgB,CAAC;EACpD;EACA,IAAIU,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACV,kBAAkB,CAAC,kBAAkB,CAAC;EACtD;EACA,IAAIW,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACX,kBAAkB,CAAC,WAAW,CAAC;EAC/C;EACA,IAAIY,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACZ,kBAAkB,CAAC,aAAa,CAAC;EACjD;EACA,IAAIa,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,kBAAkB,CAAC,QAAQ,CAAC;EAC5C;EACA,IAAIc,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACd,kBAAkB,CAAC,UAAU,CAAC;EAC9C;EACA,IAAIe,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,kBAAkB,CAAC,MAAM,CAAC;EAC1C;EACA,IAAIgB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAChB,kBAAkB,CAAC,QAAQ,CAAC;EAC5C;EACA,IAAIiB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,kBAAkB,CAAC,UAAU,CAAC;EAC9C;EACA,IAAIkB,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAClB,kBAAkB,CAAC,eAAe,CAAC;EACnD;EACA,IAAImB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnB,kBAAkB,CAAC,YAAY,CAAC;EAChD;EACAoB,QAAQA,CAACC,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE;IAC/B,KAAK,MAAM/B,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,EAAE;QACjB,IAAI,CAACgC,MAAM,CAAC,IAAI,CAAC1C,MAAM,CAACU,EAAE,CAAC,EAAE8B,KAAK,EAAEC,SAAS,CAAC;MAClD;IACJ;EACJ;EACAC,MAAMA,CAAC3B,KAAK,EAAEyB,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE;IACpC,IAAIE,MAAM;IACV,IAAIzB,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IAC7B,IAAIuB,SAAS,EAAE;MACX,IAAI,IAAI,CAAC7B,eAAe,CAAC,CAAC,EAAE;QACxBM,QAAQ,GAAG,IAAI,CAACV,cAAc,CAAC,CAAC,CAACU,QAAQ;MAC7C;MACAyB,MAAM,GAAIH,KAAK,GAAGtB,QAAQ,GAAI,GAAG;IACrC,CAAC,MACI;MACDyB,MAAM,GAAGH,KAAK;IAClB;IACAzB,KAAK,CAACK,WAAW,GAAGuB,MAAM;EAC9B;EACAC,YAAYA,CAAChD,IAAI,EAAEiD,KAAK,EAAEC,QAAQ,EAAE;IAChC,KAAK,MAAMpC,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,EAAE;QACjB,IAAI,CAACqC,cAAc,CAAC,IAAI,CAAC/C,MAAM,CAACU,EAAE,CAAC,EAAEd,IAAI,EAAEiD,KAAK,EAAEC,QAAQ,CAAC;MAC/D;IACJ;EACJ;EACAC,cAAcA,CAAChC,KAAK,EAAEnB,IAAI,EAAEiD,KAAK,EAAEC,QAAQ,EAAE;IACzC/B,KAAK,CAAC6B,YAAY,CAAChD,IAAI,EAAEiD,KAAK,EAAEC,QAAQ,CAAC;EAC7C;EACA3B,kBAAkBA,CAAC6B,QAAQ,EAAE;IACzB,MAAMhD,MAAM,GAAG,CAAC,CAAC;IACjB,IAAIa,MAAM;IACV,KAAK,MAAMH,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,EAAE;QACjBV,MAAM,CAACU,EAAE,CAAC,GAAG,IAAI,CAACV,MAAM,CAACU,EAAE,CAAC;MAChC;IACJ;IACA,MAAMuC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACnD,MAAM,CAAC,CAACoD,MAAM;IAC1C,QAAQH,OAAO;MACX,KAAK,CAAC;QACF;QACA,QAAQD,QAAQ;UACZ,KAAK,OAAO;YACRnC,MAAM,GAAGhC,QAAQ,CAACE,SAAS;YAC3B;UACJ,KAAK,cAAc;UACnB,KAAK,QAAQ;YACT8B,MAAM,GAAG,CAAC;YACV;UACJ,KAAK,MAAM;YACPA,MAAM,GAAG;cAAEwC,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAE,CAAC;YAC1C;QACR;QACA;MACJ,KAAK,CAAC;QACF;QACA,MAAMC,YAAY,GAAGN,MAAM,CAACC,IAAI,CAACnD,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3Ca,MAAM,GAAGb,MAAM,CAACwD,YAAY,CAAC,CAACR,QAAQ,CAAC;QACvC;MACJ;QACI;QACA,MAAMvC,MAAM,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;QACpCK,MAAM,GAAGb,MAAM,CAACS,MAAM,CAACC,EAAE,CAAC,CAACsC,QAAQ,CAAC;IAC5C;IACA,OAAOnC,MAAM;EACjB;EACAS,kBAAkBA,CAAC0B,QAAQ,EAAER,KAAK,EAAE;IAChC,KAAK,MAAM9B,EAAE,IAAI,IAAI,CAACV,MAAM,EAAE;MAC1B,IAAI,IAAI,CAACA,MAAM,CAACU,EAAE,CAAC,EAAE;QACjB,IAAI,CAACV,MAAM,CAACU,EAAE,CAAC,CAACsC,QAAQ,CAAC,GAAGR,KAAK;MACrC;IACJ;EACJ;EACAiB,eAAeA,CAACC,IAAI,EAAE;IAClB,IAAI,CAACC,iBAAiB,GAAGD,IAAI;EACjC;EACAE,aAAaA,CAAC7C,KAAK,EAAE;IACjB,IAAI,CAACf,MAAM,CAACe,KAAK,CAACL,EAAE,CAAC,GAAGK,KAAK;EACjC;EACA8C,eAAeA,CAAC9C,KAAK,EAAE;IACnB,OAAO,IAAI,CAACf,MAAM,CAACe,KAAK,CAACL,EAAE,CAAC;EAChC;AACJ;AACA;AAAmBZ,YAAY,CAACZ,IAAI,YAAA4E,qBAAA1E,CAAA;EAAA,YAAAA,CAAA,IAAwFU,YAAY;AAAA,CAAoD;AAC5L;AAAmBA,YAAY,CAACT,KAAK,kBA3NyE3B,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EA2NgBO,YAAY;EAAAN,OAAA,EAAZM,YAAY,CAAAZ,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACnK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5N8GhC,EAAE,CAAAiC,iBAAA,CA4NrBG,YAAY,EAAc,CAAC;IAC1GF,IAAI,EAAEjC,UAAU;IAChBkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMsE,uBAAuB,CAAC;EAC1BhE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiE,eAAe,GAAG,IAAI1F,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC2F,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,CAAC,CAAC;EACvD;EACA3C,KAAKA,CAAC4C,MAAM,EAAE;IACV,IAAI,CAACH,eAAe,CAACI,IAAI,CAACD,MAAM,CAAC;EACrC;AACJ;AACA;AAAmBJ,uBAAuB,CAAC7E,IAAI,YAAAmF,gCAAAjF,CAAA;EAAA,YAAAA,CAAA,IAAwF2E,uBAAuB;AAAA,CAAoD;AAClN;AAAmBA,uBAAuB,CAAC1E,KAAK,kBA7O8D3B,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EA6O2BwE,uBAAuB;EAAAvE,OAAA,EAAvBuE,uBAAuB,CAAA7E,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACzL;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9O8GhC,EAAE,CAAAiC,iBAAA,CA8OrBoE,uBAAuB,EAAc,CAAC;IACrHnE,IAAI,EAAEjC,UAAU;IAChBkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAM6E,cAAc,CAAC;EACjB;AACJ;AACA;AACA;EACI,OAAOC,SAASA,CAAA,EAAG;IACf,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,aAAa;IACjB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,oBAAoB,CAAC,GAAG,CAAC;IAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,IAAI,CAACtB,MAAM,EAAEyB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACzCJ,aAAa,GAAGM,QAAQ,CAACC,MAAM,CAACC,gBAAgB,CAACP,IAAI,CAACG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;MACzE,IAAIJ,aAAa,GAAGD,MAAM,EAAE;QACxBA,MAAM,GAAGC,aAAa,GAAG,CAAC;MAC9B;IACJ;IACA,OAAOD,MAAM;EACjB;EACA;EACA,OAAOU,cAAcA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA;IACA,OAAQ,OAAOF,MAAM,CAACG,WAAW,KAAK,WAAW,IAC7CC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;EACtD;EACA,OAAOC,WAAWA,CAAA,EAAG;IACjB,OAAQ,CAACH,SAAS,CAACC,SAAS,CAACG,KAAK,CAAC,iBAAiB,CAAC,IACjDlB,cAAc,CAACmB,QAAQ,CAAC,CAAC,KACzB,CAACL,SAAS,CAACC,SAAS,CAACG,KAAK,CAAC,6BAA6B,CAAC;EACjE;EACA,OAAOC,QAAQA,CAAA,EAAG;IACd,OAAQL,SAAS,CAACM,cAAc,IAC5BN,SAAS,CAACM,cAAc,GAAG,CAAC,IAC5B,UAAU,CAACC,IAAI,CAACP,SAAS,CAACQ,QAAQ,CAAC;EAC3C;EACA,OAAOC,SAASA,CAAA,EAAG;IACf,OAAQlB,QAAQ,CAACmB,GAAG,CAACR,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAC1CX,QAAQ,CAACmB,GAAG,CAACR,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;EAC/C;AACJ;AACA;AAAmBhB,cAAc,CAACpF,IAAI,YAAA6G,uBAAA3G,CAAA;EAAA,YAAAA,CAAA,IAAwFkF,cAAc;AAAA,CAAoD;AAChM;AAAmBA,cAAc,CAACjF,KAAK,kBAhSuE3B,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EAgSkB+E,cAAc;EAAA9E,OAAA,EAAd8E,cAAc,CAAApF,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACvK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjS8GhC,EAAE,CAAAiC,iBAAA,CAiSrB2E,cAAc,EAAc,CAAC;IAC5G1E,IAAI,EAAEjC,UAAU;IAChBkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMuG,sBAAsB,CAAC;EACzBjG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkG,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,kBAAkB,GAAG,IAAIvI,YAAY,CAAC,CAAC;EAChD;EACAwI,IAAIA,CAAC1C,IAAI,EAAE1D,MAAM,EAAE;IACf,IAAI,CAAC2D,iBAAiB,GAAGD,IAAI;IAC7B,IAAI,CAAC1D,MAAM,GAAGA,MAAM;IACpB,MAAMqG,IAAI,GAAG;MACTC,EAAE,EAAE;QACAC,OAAO,EAAE,mBAAmB;QAC5BC,OAAO,EAAE,mBAAmB;QAC5BC,OAAO,EAAE,mBAAmB;QAC5BC,IAAI,EAAE,gBAAgB;QACtBC,QAAQ,EAAE,kBAAkB;QAC5BC,OAAO,EAAE;MACb,CAAC;MACDC,SAAS,EAAE;QACPN,OAAO,EAAE,yBAAyB;QAClCC,OAAO,EAAE,yBAAyB;QAClCC,OAAO,EAAE,yBAAyB;QAClCC,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE,wBAAwB;QAClCC,OAAO,EAAE;MACb,CAAC;MACDE,SAAS,EAAE;QACPP,OAAO,EAAE,oBAAoB;QAC7BC,OAAO,EAAE,gCAAgC;QACzCC,OAAO,EAAE,yBAAyB;QAClCC,IAAI,EAAE,wBAAwB;QAC9BC,QAAQ,EAAE,wBAAwB;QAClCC,OAAO,EAAE;MACb,CAAC;MACDG,GAAG,EAAE;QACDR,OAAO,EAAE,eAAe;QACxBC,OAAO,EAAE,sBAAsB;QAC/BC,OAAO,EAAE,sBAAsB;QAC/BC,IAAI,EAAE,qBAAqB;QAC3BC,QAAQ,EAAE,qBAAqB;QAC/BC,OAAO,EAAE;MACb,CAAC;MACDI,GAAG,EAAE;QACDT,OAAO,EAAE,yBAAyB;QAClCC,OAAO,EAAE,yBAAyB;QAClCC,OAAO,EAAE,uBAAuB;QAChCC,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE,qBAAqB;QAC/BC,OAAO,EAAE;MACb,CAAC;MACDK,EAAE,EAAE;QACAV,OAAO,EAAE,qBAAqB;QAC9BC,OAAO,EAAE,qBAAqB;QAC9BC,OAAO,EAAE,qBAAqB;QAC9BC,IAAI,EAAE,kBAAkB;QACxBC,QAAQ,EAAE,oBAAoB;QAC9BC,OAAO,EAAE;MACb;IACJ,CAAC;IACD,KAAK,MAAMM,OAAO,IAAIb,IAAI,EAAE;MACxB,IAAIA,IAAI,CAACa,OAAO,CAAC,CAACX,OAAO,IAAI5B,QAAQ,EAAE;QACnC,IAAI,CAACwC,QAAQ,GAAGd,IAAI,CAACa,OAAO,CAAC;QAC7B;MACJ;IACJ;IACA,IAAI5C,cAAc,CAACiB,WAAW,CAAC,CAAC,EAAE;MAC9B,IAAI,CAAC4B,QAAQ,GAAGd,IAAI,CAACW,GAAG;IAC5B;IACA,IAAI,CAACI,WAAW,GAAG,IAAI,CAACD,QAAQ,IAAI,IAAI;IACxC,IAAI,IAAI,CAACA,QAAQ,IAAI,IAAI,EAAE;MACvB;IACJ;IACA,IAAIE,gBAAgB;IACpB,QAAQ,IAAI,CAACF,QAAQ,CAACR,QAAQ;MAC1B;MACA;MACA,KAAK,qBAAqB;QACtBU,gBAAgB,GAAG1C,QAAQ;QAC3B;MACJ;MACA,KAAK,qBAAqB;QACtB0C,gBAAgB,GAAG,IAAI,CAACrH,MAAM,CAACsH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC5D,IAAI;QAChD;MACJ;MACA;QACI2D,gBAAgB,GAAG3D,IAAI;IAC/B;IACA,IAAI,CAAC6D,oBAAoB,GAAGhJ,SAAS,CAAC8I,gBAAgB,EAAE,IAAI,CAACF,QAAQ,CAACR,QAAQ,CAAC,CAACa,SAAS,CAAC,MAAM;MAC5F,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;EACN;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvB,YAAY,GAAG,CAAC,CAACvB,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAACX,OAAO,CAAC;IACrD,IAAI,CAACL,kBAAkB,CAAC9F,IAAI,CAAC,IAAI,CAAC6F,YAAY,CAAC;EACnD;EACAwB,gBAAgBA,CAAClB,OAAO,GAAG,IAAI,EAAE;IAC7B,IAAI,IAAI,CAACN,YAAY,EAAE;MACnB,IAAI,CAACQ,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACD,OAAO,CAACD,OAAO,CAAC;IACzB;EACJ;EACAC,OAAOA,CAAC/C,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,EAAE;MACPA,IAAI,GAAG,IAAI,CAACC,iBAAiB;IACjC;IACA,IAAI,CAACuC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,kBAAkB,CAAC9F,IAAI,CAAC,IAAI,CAAC;IAClC;IACA,IAAI,IAAI,CAAC+G,WAAW,IAAI,IAAI,CAACnB,gBAAgB,EAAE;MAC3C;MACA,IAAI3B,cAAc,CAACY,cAAc,CAAC,CAAC,EAAE;QACjC;QACA;QACA,IAAK,CAAC,IAAI,CAACiC,QAAQ,CAACZ,OAAO,IAAI7C,IAAI,KAAK,IAAI,CAACC,iBAAiB,IAC1DW,cAAc,CAACiB,WAAW,CAAC,CAAC,EAAE;UAC9B7B,IAAI,GAAG,IAAI,CAAC1D,MAAM,CAACsH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC5D,IAAI;QACxC;QACA,IAAI,CAACiE,wBAAwB,CAACjE,IAAI,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAACiE,wBAAwB,CAAC,IAAI,CAAChE,iBAAiB,CAAC;MACzD;IACJ;EACJ;EACAgE,wBAAwBA,CAACjE,IAAI,EAAE;IAC3BA,IAAI,CAAC,IAAI,CAACyD,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC;EACjC;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACR,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,kBAAkB,CAAC9F,IAAI,CAAC,KAAK,CAAC;IACnC;IACA,IAAI,IAAI,CAAC+G,WAAW,IAAI,IAAI,CAACnB,gBAAgB,EAAE;MAC3CtB,QAAQ,CAAC,IAAI,CAACwC,QAAQ,CAACT,IAAI,CAAC,CAAC,CAAC;IAClC;EACJ;AACJ;AACA;AAAmBV,sBAAsB,CAAC9G,IAAI,YAAA0I,+BAAAxI,CAAA;EAAA,YAAAA,CAAA,IAAwF4G,sBAAsB;AAAA,CAAoD;AAChN;AAAmBA,sBAAsB,CAAC3G,KAAK,kBAnb+D3B,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EAmb0ByG,sBAAsB;EAAAxG,OAAA,EAAtBwG,sBAAsB,CAAA9G,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACvL;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApb8GhC,EAAE,CAAAiC,iBAAA,CAobrBqG,sBAAsB,EAAc,CAAC;IACpHpG,IAAI,EAAEjC,UAAU;IAChBkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMoI,QAAQ,CAAC;AAEfA,QAAQ,CAACC,QAAQ,GAAG,OAAO;AAC3BD,QAAQ,CAACE,WAAW,GAAG,SAAS;AAChCF,QAAQ,CAACG,mBAAmB,GAAG,gBAAgB;AAC/CH,QAAQ,CAACI,kBAAkB,GAAG,gBAAgB;AAC9CJ,QAAQ,CAACK,UAAU,GAAG,SAAS;AAC/BL,QAAQ,CAACM,YAAY,GAAG,WAAW;AACnCN,QAAQ,CAAC/I,QAAQ,GAAG,OAAO;AAC3B+I,QAAQ,CAACO,QAAQ,GAAG,OAAO;AAC3BP,QAAQ,CAACQ,cAAc,GAAG,YAAY;AACtCR,QAAQ,CAACS,kBAAkB,GAAG,gBAAgB;AAC9CT,QAAQ,CAACU,aAAa,GAAG,WAAW;AACpCV,QAAQ,CAACW,QAAQ,GAAG,OAAO;AAC3BX,QAAQ,CAACY,OAAO,GAAG,MAAM;AACzBZ,QAAQ,CAAC7I,UAAU,GAAG,SAAS;AAC/B6I,QAAQ,CAACa,WAAW,GAAG,UAAU;AACjCb,QAAQ,CAACc,cAAc,GAAG,YAAY;AACtCd,QAAQ,CAACe,OAAO,GAAG,MAAM;AACzBf,QAAQ,CAACgB,SAAS,GAAG,QAAQ;AAC7BhB,QAAQ,CAACiB,UAAU,GAAG,SAAS;AAC/BjB,QAAQ,CAACkB,UAAU,GAAG,SAAS;AAC/BlB,QAAQ,CAACmB,UAAU,GAAG,SAAS;AAC/BnB,QAAQ,CAACoB,cAAc,GAAG,YAAY;AACtCpB,QAAQ,CAACqB,gBAAgB,GAAG,cAAc;AAC1CrB,QAAQ,CAACsB,UAAU,GAAG,SAAS;AAC/BtB,QAAQ,CAACuB,OAAO,GAAG,MAAM;AACzBvB,QAAQ,CAACwB,QAAQ,GAAG,OAAO;AAC3BxB,QAAQ,CAACyB,OAAO,GAAG,MAAM;AACzBzB,QAAQ,CAAC0B,YAAY,GAAG,UAAU;AAClC1B,QAAQ,CAAC2B,UAAU,GAAG,QAAQ;AAC9B;AAAmB3B,QAAQ,CAAC3I,IAAI,YAAAuK,iBAAArK,CAAA;EAAA,YAAAA,CAAA,IAAwFyI,QAAQ;AAAA,CAAoD;AACpL;AAAmBA,QAAQ,CAACxI,KAAK,kBA3d6E3B,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EA2dYsI,QAAQ;EAAArI,OAAA,EAARqI,QAAQ,CAAA3I,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC3J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5d8GhC,EAAE,CAAAiC,iBAAA,CA4drBkI,QAAQ,EAAc,CAAC;IACtGjI,IAAI,EAAEjC,UAAU;IAChBkC,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMiK,oBAAoB,CAAC;EACvB3J,WAAWA,CAAC4J,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,eAAe,GAAG,IAAIhM,YAAY,CAAC,CAAC;IACzC,IAAI,CAACiM,gBAAgB,GAAG,IAAIjM,YAAY,CAAC,CAAC;IAC1C,IAAI,CAACkM,cAAc,GAAG,IAAIlM,YAAY,CAAC,CAAC;IACxC,IAAI,CAACmM,kBAAkB,GAAG,IAAInM,YAAY,CAAC,CAAC;IAC5C,IAAI,CAACyE,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC2H,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,SAAS,GAAG,CAAC;EACtB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,OAAO,GAAG5L,SAAS,CAAC,IAAI,CAACoL,GAAG,CAACS,aAAa,EAAEvC,QAAQ,CAACuB,OAAO,CAAC;IAClE,IAAI,CAAC/G,aAAa,CAACgI,IAAI,CAAC,IAAI,CAACF,OAAO,CAAC3C,SAAS,CAAC,IAAI,CAAC8C,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAC3E;EACAD,MAAMA,CAACE,KAAK,EAAE;IACV,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAACF,IAAI;IACpC,IAAI,CAACd,GAAG,CAACS,aAAa,CAACK,IAAI,GAAGA,IAAI;IAClC,IAAI,CAACG,eAAe,CAACH,IAAI,CAAC;EAC9B;EACAG,eAAeA,CAACH,IAAI,EAAE;IAClB,IAAI,CAACT,iBAAiB,CAACa,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IACtD,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG2F,IAAI,CAACrH,MAAM,EAAEyB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACzC,IAAI,CAACmG,QAAQ,GAAGzM,SAAS,CAACkM,IAAI,CAAC5F,CAAC,CAAC,EAAEgD,QAAQ,CAACwB,QAAQ,CAAC;MACrD,IAAI,CAACW,iBAAiB,CAACK,IAAI,CAAC,IAAI,CAACW,QAAQ,CAACxD,SAAS,CAAC,IAAI,CAACyD,OAAO,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7E,IAAI,CAACW,OAAO,GAAG3M,SAAS,CAACkM,IAAI,CAAC5F,CAAC,CAAC,EAAEgD,QAAQ,CAACyB,OAAO,CAAC;MACnD,IAAI,CAACU,iBAAiB,CAACK,IAAI,CAAC,IAAI,CAACa,OAAO,CAAC1D,SAAS,CAAC,IAAI,CAAC2D,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/E;EACJ;EACAU,OAAOA,CAACT,KAAK,EAAE;IACX,IAAI,CAACZ,eAAe,CAACvJ,IAAI,CAACmK,KAAK,CAACE,MAAM,CAAC;EAC3C;EACAS,MAAMA,CAACX,KAAK,EAAE;IACV,IAAI,CAACV,cAAc,CAACzJ,IAAI,CAACmK,KAAK,CAACE,MAAM,CAAC;EAC1C;EACAU,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACzB,GAAG,CAACS,aAAa,CAACO,KAAK,IAAI,IAAI,CAAChB,GAAG,CAACS,aAAa,CAACO,KAAK,CAACF,IAAI,EAAE;MACnE,MAAMY,OAAO,GAAG,IAAI,CAACpB,SAAS,KAAK,IAAI,CAACN,GAAG,CAACS,aAAa,CAACO,KAAK,CAACF,IAAI,CAACrH,MAAM;MAC3E,IAAIiI,OAAO,EAAE;QACT,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACN,GAAG,CAACS,aAAa,CAACO,KAAK,CAACF,IAAI,CAACrH,MAAM;QACzD,IAAI,CAACuG,GAAG,CAACS,aAAa,CAACK,IAAI,GAAG,IAAI,CAACd,GAAG,CAACS,aAAa,CAACO,KAAK,CAACF,IAAI;QAC/D,IAAI,CAACG,eAAe,CAAC,IAAI,CAACjB,GAAG,CAACS,aAAa,CAACO,KAAK,CAACF,IAAI,CAAC;MAC3D;IACJ;EACJ;EACAa,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjJ,aAAa,CAACwI,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmBrB,oBAAoB,CAACxK,IAAI,YAAAqM,6BAAAnM,CAAA;EAAA,YAAAA,CAAA,IAAwFsK,oBAAoB,EAphB1ChM,EAAE,CAAA8N,iBAAA,CAohB0D9N,EAAE,CAAC+N,UAAU;AAAA,CAA4C;AACnO;AAAmB/B,oBAAoB,CAACgC,IAAI,kBArhBkEhO,EAAE,CAAAiO,iBAAA;EAAA/L,IAAA,EAqhBQ8J,oBAAoB;EAAAkC,SAAA;EAAAC,OAAA;IAAAjC,eAAA;IAAAC,gBAAA;IAAAC,cAAA;IAAAC,kBAAA;EAAA;AAAA,EAA+M;AAC3V;EAAA,QAAArK,SAAA,oBAAAA,SAAA,KAthB8GhC,EAAE,CAAAiC,iBAAA,CAshBrB+J,oBAAoB,EAAc,CAAC;IAClH9J,IAAI,EAAE/B,SAAS;IACfgC,IAAI,EAAE,CAAC;MACCiM,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElM,IAAI,EAAElC,EAAE,CAAC+N;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE7B,eAAe,EAAE,CAAC;MACnGhK,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAE+L,gBAAgB,EAAE,CAAC;MACnBjK,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEgM,cAAc,EAAE,CAAC;MACjBlK,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEiM,kBAAkB,EAAE,CAAC;MACrBnK,IAAI,EAAE9B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiO,gBAAgB,CAAC;EACnBhM,WAAWA,CAACiM,GAAG,EAAErC,GAAG,EAAE;IAClB,IAAI,CAACqC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACrC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACpI,KAAK,GAAG1C,QAAQ,CAACE,SAAS;IAC/B,IAAI,CAACmD,IAAI,GAAG;MAAEmB,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC;IAC7C,IAAI,CAACpB,MAAM,GAAG;MAAE8J,GAAG,EAAE;IAAE,CAAC;IACxB,IAAI,CAACtK,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACkK,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,aAAa,GAAG,GAAG;IACxB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAIjO,OAAO,CAAC,CAAC;EACvC;EACA4L,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACsC,OAAO,CAACC,QAAQ,EAAE;MACvB;MACA,IAAI,CAAC/I,IAAI,GAAG,IAAI,CAAC8I,OAAO;IAC5B,CAAC,MACI;MACD;MACA,IAAI,CAAC9I,IAAI,GAAG,IAAI,CAAC8I,OAAO,CAAC9I,IAAI;IACjC;IACA;IACA,IAAI,CAACsI,GAAG,CAACpI,aAAa,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACvB,aAAa,GAAG;MACjB;MACAqK,KAAK,EAAEnO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACC,QAAQ,CAAC;MAC9CnG,OAAO,EAAEpD,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACE,WAAW,CAAC;MACnDnG,cAAc,EAAErD,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACG,mBAAmB,CAAC;MAClE2E,cAAc,EAAEpO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACI,kBAAkB,CAAC;MACjE2E,OAAO,EAAErO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACK,UAAU,CAAC;MAClD2E,SAAS,EAAEtO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACM,YAAY,CAAC;MACtD2E,KAAK,EAAEvO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAAC/I,QAAQ,CAAC;MAC9CiO,KAAK,EAAExO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACO,QAAQ,CAAC;MAC9C4E,UAAU,EAAEzO,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACQ,cAAc,CAAC;MACzD4E,cAAc,EAAE1O,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACS,kBAAkB,CAAC;MACjE4E,SAAS,EAAE3O,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACU,aAAa,CAAC;MACvDtH,KAAK,EAAE1C,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACW,QAAQ,CAAC;MAC9CxH,IAAI,EAAEzC,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACY,OAAO,CAAC;MAC5C0E,OAAO,EAAE5O,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAAC7I,UAAU,CAAC;MAClDoO,QAAQ,EAAE7O,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACa,WAAW,CAAC;MACpD2E,UAAU,EAAE9O,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACc,cAAc,CAAC;MACzD2E,MAAM,EAAE/O,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACgB,SAAS,CAAC;MAChD0E,OAAO,EAAEhP,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACiB,UAAU,CAAC;MAClD0E,OAAO,EAAEjP,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACkB,UAAU,CAAC;MAClD0E,OAAO,EAAElP,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACmB,UAAU,CAAC;MAClD0E,UAAU,EAAEnP,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACoB,cAAc,CAAC;MACzD0E,YAAY,EAAEpP,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACqB,gBAAgB,CAAC;MAC7D0E,OAAO,EAAErP,SAAS,CAAC,IAAI,CAACmF,IAAI,EAAEmE,QAAQ,CAACsB,UAAU,CAAC;MAClD;MACA0E,QAAQ,EAAEtP,SAAS,CAACyG,MAAM,EAAE6C,QAAQ,CAAC0B,YAAY,CAAC;MAClDuE,MAAM,EAAEvP,SAAS,CAACyG,MAAM,EAAE6C,QAAQ,CAAC2B,UAAU,CAAC;MAC9C;MACAuE,QAAQ,EAAE,IAAIvP,UAAU,CAAEwP,QAAQ,IAAK;QACnC,MAAMC,MAAM,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAK;UAC/CH,QAAQ,CAAC5J,IAAI,CAAC+J,SAAS,CAAC;QAC5B,CAAC,CAAC;QACFF,MAAM,CAACG,OAAO,CAAC,IAAI,CAAC1K,IAAI,EAAE;UAAE2K,SAAS,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC;QAChE,OAAO,MAAM;UACTL,MAAM,CAACM,UAAU,CAAC,CAAC;QACvB,CAAC;MACL,CAAC,CAAC;MACF;MACAhC,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC;IACD,IAAI,CAACiC,WAAW,GAAG,IAAI,CAACnM,aAAa,CAAC0L,QAAQ,CAACvG,SAAS,CAAC,IAAI,CAACiH,UAAU,CAAClE,IAAI,CAAC,IAAI,CAAC,CAAC;IACpF,IAAI,CAACmE,UAAU,GAAG,IAAI,CAACrM,aAAa,CAACV,OAAO,CAAC6F,SAAS,CAAC,IAAI,CAACmH,SAAS,CAACpE,IAAI,CAAC,IAAI,CAAC,CAAC;IACjF,IAAI,CAACqE,iBAAiB,GAAG,IAAI,CAACvM,aAAa,CAACT,cAAc,CAAC4F,SAAS,CAAC,IAAI,CAACqH,gBAAgB,CAACtE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtG,IAAI,CAACuE,iBAAiB,GAAG,IAAI,CAACzM,aAAa,CAAC4K,cAAc,CAACzF,SAAS,CAAC,IAAI,CAACuH,cAAc,CAACxE,IAAI,CAAC,IAAI,CAAC,CAAC;IACpG,IAAI,CAACyE,UAAU,GAAG,IAAI,CAAC3M,aAAa,CAACuL,OAAO,CAACpG,SAAS,CAAC,IAAI,CAACyH,MAAM,CAAC1E,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9E,IAAI,CAAC2E,WAAW,GAAG,IAAI,CAAC7M,aAAa,CAAC+K,QAAQ,CAAC5F,SAAS,CAAC,IAAI,CAAC2H,UAAU,CAAC5E,IAAI,CAAC,IAAI,CAAC,CAAC;IACpF,IAAI,CAAC6E,QAAQ,GAAG,IAAI,CAAC/M,aAAa,CAACyK,KAAK,CAACtF,SAAS,CAAC,IAAI,CAAC6H,UAAU,CAAC9E,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9E,IAAI,CAAC+E,UAAU,GAAG,IAAI,CAACjN,aAAa,CAAC8K,OAAO,CAAC3F,SAAS,CAAC,IAAI,CAAC+H,cAAc,CAAChF,IAAI,CAAC,IAAI,CAAC,CAAC;IACtF,IAAI,CAACiF,OAAO,GAAG,IAAI,CAACnN,aAAa,CAACrB,IAAI,CAACwG,SAAS,CAAC,IAAI,CAACiI,MAAM,CAAClF,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE,IAAI,CAACmF,QAAQ,GAAG,IAAI,CAACrN,aAAa,CAACpB,KAAK,CAACuG,SAAS,CAAC,IAAI,CAACmI,OAAO,CAACpF,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3E,IAAI,CAACqF,aAAa,GAAG,IAAI,CAACvN,aAAa,CAACqL,UAAU,CAAClG,SAAS,CAAC,IAAI,CAACqI,YAAY,CAACtF,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1F,IAAI,CAACuF,eAAe,GAAG,IAAI,CAACzN,aAAa,CAACsL,YAAY,CAACnG,SAAS,CAAC,IAAI,CAACuI,cAAc,CAACxF,IAAI,CAAC,IAAI,CAAC,CAAC;IAChG,IAAI,CAACyF,QAAQ,GAAG,IAAI,CAAC3N,aAAa,CAAC0K,KAAK,CAACvF,SAAS,CAAC,IAAI,CAACyI,OAAO,CAAC1F,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3E,IAAI,IAAI,CAAC5J,QAAQ,EAAE;MACf,IAAI,CAACqL,GAAG,CAAC/L,gBAAgB,CAACuH,SAAS,CAAC,MAAM;QACtC,IAAI,CAAC0I,WAAW,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;EACJ;EACAA,WAAWA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAG,EAAE;IACrB,KAAK,MAAMpP,KAAK,IAAI,IAAI,CAACiL,GAAG,CAAChM,MAAM,EAAE;MACjC,IAAI,IAAI,CAACgM,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,EAAE;QACxBoP,UAAU,CAAC9F,IAAI,CAAC,IAAI,CAAC2B,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,CAACsB,aAAa,CAACV,OAAO,CAAC;MACjE;IACJ;IACA,IAAI,CAACyO,sBAAsB,GAAG3R,aAAa,CAAC0R,UAAU,CAAC,CAClDE,IAAI,CAAC1R,GAAG,CAAC,CAAC,GAAG2R,MAAM,KAAK;MACzB,MAAMC,eAAe,GAAI/F,KAAK,IAAK;QAC/B,IAAI,CAACA,KAAK,EAAEE,MAAM,EAAE;UAChB,OAAO,KAAK;QAChB;QACA,OAAOF,KAAK,CAACE,MAAM,CAAC8F,UAAU,KAAK,CAAC;MACxC,CAAC;MACD,MAAMC,QAAQ,GAAGH,MAAM,CAACI,IAAI,CAACH,eAAe,CAAC;MAC7C,IAAIE,QAAQ,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;QACpC,IAAI,CAACC,SAAS,CAAC,CAAC;QAChB,IAAI,CAACD,gBAAgB,CAAC5F,WAAW,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC,CAAC,CACEvD,SAAS,CAAC,CAAC;EACpB;EACAoJ,SAASA,CAAA,EAAG;IACR,IAAI,CAACD,gBAAgB,GAAGjS,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC8I,SAAS,CAAC,MAAM;MACnD,KAAK,MAAMzG,KAAK,IAAI,IAAI,CAACiL,GAAG,CAAChM,MAAM,EAAE;QACjC,IAAI,IAAI,CAACgM,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,KAAK,IAAI,EAAE;UACjC,MAAM8P,IAAI,GAAG,IAAI,CAAC7E,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,CAACK,WAAW,GAAG,IAAI,CAACA,WAAW;UAClE,IAAIyP,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,GAAG,GAAG,EAAE;YAC3B,IAAI,CAACvE,aAAa,GAAG,IAAI,CAAC/K,KAAK,KAAK1C,QAAQ,CAACG,UAAU;YACvD,IAAI,CAACiC,KAAK,CAAC,CAAC;YACZ,IAAI,CAAC+K,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,CAACE,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC+K,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,CAACK,WAAW,GAAG,IAAI,CAACA,WAAW;UACzD,CAAC,MACI;YACD,IAAI,IAAI,CAACkL,aAAa,EAAE;cACpB,IAAI,CAACtL,IAAI,CAAC,CAAC;cACX,IAAI,CAACgL,GAAG,CAAChM,MAAM,CAACe,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC;cAC7B,IAAI,CAACsL,aAAa,GAAG,KAAK;YAC9B;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;EACAmC,UAAUA,CAACN,SAAS,EAAE;IAClB;IACA,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGqJ,SAAS,CAAC/K,MAAM,EAAEyB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAMiM,GAAG,GAAG3C,SAAS,CAACtJ,CAAC,CAAC;MACxB,IAAIiM,GAAG,CAAClR,IAAI,KAAK,YAAY,IAAIkR,GAAG,CAACC,aAAa,KAAK,KAAK,EAAE;QAC1D;QACA,IAAID,GAAG,CAACpG,MAAM,CAACsG,GAAG,IACdF,GAAG,CAACpG,MAAM,CAACsG,GAAG,CAAC5N,MAAM,GAAG,CAAC,IACzB0N,GAAG,CAACpG,MAAM,CAACsG,GAAG,CAAC1L,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;UACrC,IAAI,CAAC2L,SAAS,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC,MACI,IAAIH,GAAG,CAAClR,IAAI,KAAK,WAAW,IAC7BkR,GAAG,CAACI,YAAY,CAAC9N,MAAM,IACvB0N,GAAG,CAACI,YAAY,CAAC,CAAC,CAAC,CAACzE,QAAQ,CAAC0E,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;QACzD,IAAI,CAACF,SAAS,CAAC,CAAC;QAChB;MACJ;IACJ;EACJ;EACAA,SAASA,CAAA,EAAG;IACR,IAAI,CAACzE,OAAO,CAACvL,KAAK,CAAC,CAAC;IACpB,IAAI,CAACuL,OAAO,CAACpL,WAAW,GAAG,CAAC;IAC5B;IACA,IAAI,CAACgQ,eAAe,CAAC,CAAC;IACtB,IAAI,CAAClF,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACK,cAAc,CAACnI,IAAI,CAAC,IAAI,CAAC8H,gBAAgB,CAAC;IAC/C;IACAmF,UAAU,CAAC,MAAM,IAAI,CAAC7E,OAAO,CAAC8E,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7C;EACAtQ,IAAIA,CAAA,EAAG;IACH;IACA,IAAI,IAAI,CAACuQ,WAAW,IACf,IAAI,CAAChQ,KAAK,KAAK1C,QAAQ,CAACE,SAAS,IAAI,IAAI,CAACwC,KAAK,KAAK1C,QAAQ,CAACC,QAAS,EAAE;MACzE;IACJ;IACA,IAAI,CAACyS,WAAW,GAAG,IAAI,CAAC/E,OAAO,CAACxL,IAAI,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACuQ,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,IAAI,IAAI,IAAI,CAACD,WAAW,CAACE,KAAK,EAAE;MACrE,IAAI,CAACF,WAAW,CACXC,IAAI,CAAC,MAAM;QACZ,IAAI,CAACD,WAAW,GAAG,IAAI;MAC3B,CAAC,CAAC,CACGE,KAAK,CAAC,MAAM;QACb,IAAI,CAACF,WAAW,GAAG,IAAI;QACvB;MACJ,CAAC,CAAC;IACN;;IACA,OAAO,IAAI,CAACA,WAAW;EAC3B;EACAtQ,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAACsQ,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,MAAM;QACxB,IAAI,CAAChF,OAAO,CAACvL,KAAK,CAAC,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACuL,OAAO,CAACvL,KAAK,CAAC,CAAC;IACxB;EACJ;EACA,IAAIP,EAAEA,CAAA,EAAG;IACL;IACA,IAAIG,MAAM;IACV,IAAI,IAAI,CAAC2L,OAAO,EAAE;MACd3L,MAAM,GAAG,IAAI,CAAC2L,OAAO,CAAC9L,EAAE;IAC5B;IACA,OAAOG,MAAM;EACjB;EACA,IAAIK,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsL,OAAO,CAACtL,QAAQ,KAAKwQ,QAAQ,GACnC,IAAI,CAACC,iBAAiB,GACtB,IAAI,CAACnF,OAAO,CAACtL,QAAQ;EAC/B;EACA,IAAIE,WAAWA,CAACC,OAAO,EAAE;IACrB,IAAI,CAACmL,OAAO,CAACpL,WAAW,GAAGC,OAAO;IAClC;EACJ;;EACA,IAAID,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoL,OAAO,CAACpL,WAAW;EACnC;EACA,IAAII,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACgL,OAAO,CAAChL,MAAM,GAAGA,MAAM;EAChC;EACA,IAAIA,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACgL,OAAO,CAAChL,MAAM;EAC9B;EACA,IAAIC,YAAYA,CAACC,IAAI,EAAE;IACnB,IAAI,CAAC8K,OAAO,CAAC/K,YAAY,GAAGC,IAAI;EACpC;EACA,IAAID,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC+K,OAAO,CAAC/K,YAAY;EACpC;EACA,IAAIW,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoK,OAAO,CAACpK,QAAQ;EAChC;EACA,IAAIE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACkK,OAAO,CAAClK,UAAU;EAClC;EACA;EACAqM,SAASA,CAACnE,KAAK,EAAE;IACb,IAAI,CAAC0B,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACK,cAAc,CAACnI,IAAI,CAAC,IAAI,CAAC8H,gBAAgB,CAAC;IAC/C,IAAI,CAACvK,OAAO,GAAG,IAAI;IACnB,IAAI,CAACgI,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACA/C,gBAAgBA,CAACrE,KAAK,EAAE;IACpB,IAAI,CAAC0B,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACK,cAAc,CAACnI,IAAI,CAAC,IAAI,CAAC8H,gBAAgB,CAAC;IAC/C,IAAI,CAACtK,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC+H,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACA7C,cAAcA,CAACvE,KAAK,EAAE;IAClB,IAAI,CAAC3I,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACK,IAAI,GAAG;MACRmB,OAAO,EAAE,CAAC;MACVE,IAAI,EAAE,CAAC;MACPD,KAAK,EAAE,IAAI,CAACpC,QAAQ,GAAG;IAC3B,CAAC;IACD,IAAI,CAACK,KAAK,GAAG1C,QAAQ,CAACE,SAAS;IAC/B;IACA,MAAMK,CAAC,GAAGyS,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC5P,IAAI,CAACoB,KAAK,CAAC;IACrC,IAAI,CAACtB,MAAM,GAAG5C,CAAC,KAAKsS,QAAQ;IAC5B,IAAI,CAAC/H,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACA3C,MAAMA,CAACzE,KAAK,EAAE;IACV,IAAI,CAAC1I,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC6H,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACAvC,UAAUA,CAAC7E,KAAK,EAAE;IACd,IAAI,CAACzI,WAAW,GAAG,IAAI;IACvB,IAAI,CAACR,KAAK,GAAG1C,QAAQ,CAACC,QAAQ;IAC9B,IAAI,CAAC6K,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACArC,cAAcA,CAAC/E,KAAK,EAAE;IAClB,IAAI,CAACjJ,KAAK,GAAG1C,QAAQ,CAACG,UAAU;IAChC,IAAI,CAAC2K,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACAnC,MAAMA,CAACjF,KAAK,EAAE;IACV,IAAI,CAACjJ,KAAK,GAAG1C,QAAQ,CAACG,UAAU;IAChC,IAAI,IAAI,CAAC2B,QAAQ,EAAE;MACf,IAAI,CAAC,IAAI,CAACgQ,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACoB,MAAM,EAAE;QACxD,IAAI,CAACnB,SAAS,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACoB,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACrI,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACAjC,OAAOA,CAACnF,KAAK,EAAE;IACX,IAAI,CAACjJ,KAAK,GAAG1C,QAAQ,CAACE,SAAS;IAC/B,IAAI,IAAI,CAAC4B,QAAQ,EAAE;MACf,IAAI,CAAC,IAAI,CAAC2L,aAAa,EAAE;QACrB,IAAI,CAACqE,gBAAgB,CAAC5F,WAAW,CAAC,CAAC;MACvC;IACJ;IACA,IAAI,CAACqG,eAAe,CAAC,CAAC;IACtB,IAAI,CAACzH,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACA/B,YAAYA,CAACrF,KAAK,EAAE;IAChB,MAAMyB,GAAG,GAAG,IAAI,CAAC7J,QAAQ,CAACgB,MAAM,GAAG,CAAC;IACpC,IAAI,CAAClB,IAAI,GAAG;MACRmB,OAAO,EAAE,IAAI,CAACjC,WAAW,GAAG,IAAI;MAChCkC,KAAK,EAAE,IAAI,CAACpB,IAAI,CAACoB,KAAK;MACtBC,IAAI,EAAE,CAAC,IAAI,CAACrC,QAAQ,GAAG,IAAI,CAACE,WAAW,IAAI;IAC/C,CAAC;IACD,IAAI6K,GAAG,IAAI,CAAC,EAAE;MACV,IAAI,CAAC9J,MAAM,GAAG;QAAE8J,GAAG,EAAE,IAAI,CAAC7J,QAAQ,CAAC6J,GAAG,CAACA,GAAG,CAAC,GAAG;MAAK,CAAC;IACxD;IACA,IAAI,CAACtC,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACAzC,UAAUA,CAAC3E,KAAK,EAAE;IACd,MAAMyB,GAAG,GAAG,IAAI,CAAC7J,QAAQ,CAACgB,MAAM,GAAG,CAAC;IACpC,IAAI6I,GAAG,IAAI,CAAC,EAAE;MACV,IAAI,CAAC9J,MAAM,GAAG;QAAE8J,GAAG,EAAE,IAAI,CAAC7J,QAAQ,CAAC6J,GAAG,CAACA,GAAG,CAAC,GAAG;MAAK,CAAC;IACxD;IACA,IAAI,CAACtC,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACA7B,cAAcA,CAACvF,KAAK,EAAE;IAClB;IACA,IAAI,CAACb,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACA3B,OAAOA,CAACzF,KAAK,EAAE;IACX;IACA,IAAI,CAACb,GAAG,CAACiI,aAAa,CAAC,CAAC;EAC5B;EACA;EACAK,WAAWA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC/F,aAAa;IACrC,IAAI,CAACC,cAAc,GAAG,IAAI,CAAChL,WAAW;IACtC,IAAI,CAAC,IAAI,CAAC8K,gBAAgB,IACtB,IAAI,CAACE,cAAc,GAAG,IAAI,CAACC,WAAW,GAAG6F,MAAM,EAAE;MACjD,IAAI,CAAChG,gBAAgB,GAAG,IAAI;IAChC;IACA,IAAI,IAAI,CAACA,gBAAgB,IACrB,IAAI,CAACE,cAAc,GAAG,IAAI,CAACC,WAAW,GAAG6F,MAAM,EAAE;MACjD,IAAI,CAAChG,gBAAgB,GAAG,KAAK;IACjC;IACA;IACA,IAAI,CAAC,IAAI,CAACK,cAAc,CAACwF,MAAM,EAAE;MAC7B,IAAI,CAACxF,cAAc,CAACnI,IAAI,CAAC,IAAI,CAAC8H,gBAAgB,CAAC;IACnD;IACA,IAAI,CAACG,WAAW,GAAG,IAAI,CAACD,cAAc;EAC1C;EACA4F,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACG,uBAAuB,GAAGzT,KAAK,CAAC,CAAC,EAAE,IAAI,CAACyN,aAAa,CAAC,CAAC3E,SAAS,CAAC,MAAM;MACxE,IAAI,CAACyK,WAAW,CAAC,CAAC;IACtB,CAAC,CAAC;EACN;EACAb,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACe,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACpH,WAAW,CAAC,CAAC;IAC9C;IACA,IAAI,CAACmB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACK,cAAc,CAACnI,IAAI,CAAC,IAAI,CAAC8H,gBAAgB,CAAC;EACnD;EACA3J,QAAQA,CAACC,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE;IAC/B,IAAIE,MAAM;IACV,MAAMzB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIuB,SAAS,EAAE;MACXE,MAAM,GAAIH,KAAK,GAAGtB,QAAQ,GAAI,GAAG;IACrC,CAAC,MACI;MACDyB,MAAM,GAAGH,KAAK;IAClB;IACA,IAAI,CAACpB,WAAW,GAAGuB,MAAM;EAC7B;EACAC,YAAYA,CAAChD,IAAI,EAAEiD,KAAK,EAAEC,QAAQ,EAAEsP,IAAI,EAAE;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAAC7F,OAAO,CAAC5J,YAAY,CAAChD,IAAI,EAAEiD,KAAK,EAAEC,QAAQ,CAAC;IACjE,IAAIsP,IAAI,EAAE;MACNC,QAAQ,CAACD,IAAI,GAAGA,IAAI;IACxB;IACA,OAAOC,QAAQ;EACnB;EACA/G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkB,OAAO,CAACwE,GAAG,GAAG,EAAE;IACrB,IAAI,CAACxC,WAAW,EAAEzD,WAAW,CAAC,CAAC;IAC/B,IAAI,CAAC2D,UAAU,EAAE3D,WAAW,CAAC,CAAC;IAC9B,IAAI,CAAC6D,iBAAiB,EAAE7D,WAAW,CAAC,CAAC;IACrC,IAAI,CAAC+D,iBAAiB,EAAE/D,WAAW,CAAC,CAAC;IACrC,IAAI,CAACiE,UAAU,EAAEjE,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACmE,WAAW,EAAEnE,WAAW,CAAC,CAAC;IAC/B,IAAI,CAACqE,QAAQ,EAAErE,WAAW,CAAC,CAAC;IAC5B,IAAI,CAACuE,UAAU,EAAEvE,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACyE,OAAO,EAAEzE,WAAW,CAAC,CAAC;IAC3B,IAAI,CAAC2E,QAAQ,EAAE3E,WAAW,CAAC,CAAC;IAC5B,IAAI,CAAC6E,aAAa,EAAE7E,WAAW,CAAC,CAAC;IACjC,IAAI,CAAC+E,eAAe,EAAE/E,WAAW,CAAC,CAAC;IACnC,IAAI,CAACiF,QAAQ,EAAEjF,WAAW,CAAC,CAAC;IAC5B,IAAI,CAACoH,uBAAuB,EAAEpH,WAAW,CAAC,CAAC;IAC3C,IAAI,CAAC4F,gBAAgB,EAAE5F,WAAW,CAAC,CAAC;IACpC,IAAI,CAACwB,cAAc,EAAE+F,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAAC/F,cAAc,EAAExB,WAAW,CAAC,CAAC;IAClC,IAAI,CAACiB,GAAG,CAACnI,eAAe,CAAC,IAAI,CAAC;EAClC;AACJ;AACA;AAAmBkI,gBAAgB,CAAC7M,IAAI,YAAAqT,yBAAAnT,CAAA;EAAA,YAAAA,CAAA,IAAwF2M,gBAAgB,EAx7BlCrO,EAAE,CAAA8N,iBAAA,CAw7BkD1L,YAAY,GAx7BhEpC,EAAE,CAAA8N,iBAAA,CAw7B2E9N,EAAE,CAAC8U,iBAAiB;AAAA,CAA4C;AAC3P;AAAmBzG,gBAAgB,CAACL,IAAI,kBAz7BsEhO,EAAE,CAAAiO,iBAAA;EAAA/L,IAAA,EAy7BImM,gBAAgB;EAAAH,SAAA;EAAA6G,MAAA;IAAAjG,OAAA;IAAA7L,QAAA;EAAA;AAAA,EAA8F;AAClO;EAAA,QAAAjB,SAAA,oBAAAA,SAAA,KA17B8GhC,EAAE,CAAAiC,iBAAA,CA07BrBoM,gBAAgB,EAAc,CAAC;IAC9GnM,IAAI,EAAE/B,SAAS;IACfgC,IAAI,EAAE,CAAC;MACCiM,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElM,IAAI,EAAEE;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAElC,EAAE,CAAC8U;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEhG,OAAO,EAAE,CAAC;MAC1H5M,IAAI,EAAE7B;IACV,CAAC,CAAC;IAAE4C,QAAQ,EAAE,CAAC;MACXf,IAAI,EAAE7B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2U,iBAAiB,CAAC;EACpB3S,WAAWA,CAAC4J,GAAG,EAAEqC,GAAG,EAAE5L,KAAK,EAAEuS,cAAc,EAAE;IACzC,IAAI,CAAC3G,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC5L,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuS,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACzM,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC0M,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC1S,aAAa,GAAG,IAAIvC,YAAY,CAAC,CAAC;IACvC,IAAI,CAACkV,YAAY,GAAG,IAAIlV,YAAY,CAAC,CAAC;IACtC,IAAI,CAACyE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACqB,IAAI,GAAGiG,GAAG,CAACS,aAAa;IAC7B,IAAI,CAAC4B,GAAG,CAACvI,eAAe,CAAC,IAAI,CAACC,IAAI,CAAC;EACvC;EACAqP,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC/S,MAAM,CAACsH,OAAO,CAAC,CAAC,CAACuD,OAAO,CAAE9J,KAAK,IAAK;MACrC,IAAI,CAACiL,GAAG,CAACpI,aAAa,CAAC7C,KAAK,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAACX,KAAK,CAACgG,IAAI,CAAC,IAAI,CAAC1C,IAAI,EAAE,IAAI,CAAC1D,MAAM,CAAC;IACvC,IAAI,CAACqC,aAAa,CAACgI,IAAI,CAAC,IAAI,CAACjK,KAAK,CAAC+F,kBAAkB,CAACqB,SAAS,CAAC,IAAI,CAACrB,kBAAkB,CAACoE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpG,IAAI,CAAClI,aAAa,CAACgI,IAAI,CAAC,IAAI,CAACsI,cAAc,CAAC1O,QAAQ,CAACuD,SAAS,CAAC,IAAI,CAACwL,cAAc,CAACzI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/F,IAAI,CAACyB,GAAG,CAAC7L,aAAa,CAAC,IAAI,CAACC,KAAK,CAAC;IAClC,IAAI,CAACD,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC2L,GAAG,CAAC;EACrC;EACA7F,kBAAkBA,CAAC8M,OAAO,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC7S,KAAK,CAAC6F,gBAAgB,EAAE;MAC9B,IAAI,CAACC,YAAY,GAAG+M,OAAO;MAC3B,IAAI,CAACzO,MAAM,GAAGyO,OAAO,GAAG3O,cAAc,CAACC,SAAS,CAAC,CAAC,CAAC2O,QAAQ,CAAC,CAAC,GAAG,MAAM;IAC1E,CAAC,MACI;MACD,IAAI,CAACN,kBAAkB,GAAGK,OAAO;IACrC;EACJ;EACAD,cAAcA,CAAC7O,MAAM,EAAE;IACnB,IAAI,CAAC0O,iBAAiB,GAAG1O,MAAM;EACnC;EACAmH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjJ,aAAa,CAACwI,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACtD;AACJ;AACA;AAAmB2H,iBAAiB,CAACxT,IAAI,YAAAiU,0BAAA/T,CAAA;EAAA,YAAAA,CAAA,IAAwFsT,iBAAiB,EA7+BpChV,EAAE,CAAA8N,iBAAA,CA6+BoD9N,EAAE,CAAC+N,UAAU,GA7+BnE/N,EAAE,CAAA8N,iBAAA,CA6+B8E1L,YAAY,GA7+B5FpC,EAAE,CAAA8N,iBAAA,CA6+BuGxF,sBAAsB,GA7+B/HtI,EAAE,CAAA8N,iBAAA,CA6+B0IzH,uBAAuB;AAAA,CAA4C;AAC7T;AAAmB2O,iBAAiB,CAACU,IAAI,kBA9+BqE1V,EAAE,CAAA2V,iBAAA;EAAAzT,IAAA,EA8+BK8S,iBAAiB;EAAA9G,SAAA;EAAA0H,cAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MA9+BxB9V,EAAE,CAAAiW,cAAA,CAAAD,QAAA,EA8+Bic3H,gBAAgB;IAAA;IAAA,IAAAyH,EAAA;MAAA,IAAAI,EAAA;MA9+BndlW,EAAE,CAAAmW,cAAA,CAAAD,EAAA,GAAFlW,EAAE,CAAAoW,WAAA,QAAAL,GAAA,CAAAzT,MAAA,GAAA4T,EAAA;IAAA;EAAA;EAAAG,QAAA;EAAAC,YAAA,WAAAC,+BAAAT,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9V,EAAE,CAAAwW,WAAA,YAAAT,GAAA,CAAAjP,MAAA;MAAF9G,EAAE,CAAAyW,WAAA,eAAAV,GAAA,CAAAvN,YAAA,uBAAAuN,GAAA,CAAAb,kBAAA,qBAAAa,GAAA,CAAAZ,iBAAA;IAAA;EAAA;EAAAhH,OAAA;IAAA1L,aAAA;IAAA2S,YAAA;EAAA;EAAAsB,QAAA,GAAF1W,EAAE,CAAA2W,kBAAA,CA8+BiV,CAACvU,YAAY,EAAEkG,sBAAsB,EAAEjC,uBAAuB,CAAC;EAAAuQ,kBAAA,EAAA1V,GAAA;EAAA2V,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,2BAAAlB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA9+BlZ9V,EAAE,CAAAiX,eAAA;MAAFjX,EAAE,CAAAkX,YAAA,EA8+BugB,CAAC;IAAA;EAAA;EAAAC,MAAA;EAAAC,aAAA;AAAA,EAAoT;AAC56B;EAAA,QAAApV,SAAA,oBAAAA,SAAA,KA/+B8GhC,EAAE,CAAAiC,iBAAA,CA++BrB+S,iBAAiB,EAAc,CAAC;IAC/G9S,IAAI,EAAE5B,SAAS;IACf6B,IAAI,EAAE,CAAC;MAAEiM,QAAQ,EAAE,WAAW;MAAEgJ,aAAa,EAAE7W,iBAAiB,CAAC8W,IAAI;MAAEN,QAAQ,EAAG,2BAA0B;MAAEO,SAAS,EAAE,CAAClV,YAAY,EAAEkG,sBAAsB,EAAEjC,uBAAuB,CAAC;MAAE8Q,MAAM,EAAE,CAAC,0OAA0O;IAAE,CAAC;EACpb,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjV,IAAI,EAAElC,EAAE,CAAC+N;IAAW,CAAC,EAAE;MAAE7L,IAAI,EAAEE;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAEoG;IAAuB,CAAC,EAAE;MAAEpG,IAAI,EAAEmE;IAAwB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmC,YAAY,EAAE,CAAC;MAC7LtG,IAAI,EAAE1B,WAAW;MACjB2B,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE+S,kBAAkB,EAAE,CAAC;MACrBhT,IAAI,EAAE1B,WAAW;MACjB2B,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEgT,iBAAiB,EAAE,CAAC;MACpBjT,IAAI,EAAE1B,WAAW;MACjB2B,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC,CAAC;IAAE2E,MAAM,EAAE,CAAC;MACT5E,IAAI,EAAE1B,WAAW;MACjB2B,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEM,aAAa,EAAE,CAAC;MAChBP,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEgV,YAAY,EAAE,CAAC;MACflT,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEkC,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAEzB,eAAe;MACrB0B,IAAI,EAAE,CAACkM,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkJ,QAAQ,GAAG,CACbnV,YAAY,EACZiE,uBAAuB,EACvBiC,sBAAsB,EACtB1B,cAAc,EACduD,QAAQ,EACRhJ,QAAQ,CACX;AACD,MAAMqW,UAAU,GAAG,CACfxL,oBAAoB,EACpBqC,gBAAgB,CACnB;AACD,MAAMoJ,YAAY,CAAC;AAEnB;AAAmBA,YAAY,CAACjW,IAAI,YAAAkW,qBAAAhW,CAAA;EAAA,YAAAA,CAAA,IAAwF+V,YAAY;AAAA,CAAkD;AAC1L;AAAmBA,YAAY,CAACE,IAAI,kBAthC0E3X,EAAE,CAAA4X,gBAAA;EAAA1V,IAAA,EAshCauV;AAAY,EAE1F;AAC/C;AAAmBA,YAAY,CAACI,IAAI,kBAzhC0E7X,EAAE,CAAA8X,gBAAA;EAAAR,SAAA,EAyhCsC,CAAC,GAAGC,QAAQ,CAAC;EAAAQ,OAAA,GAAYpX,YAAY;AAAA,EAAI;AAC/L;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KA1hC8GhC,EAAE,CAAAiC,iBAAA,CA0hCrBwV,YAAY,EAAc,CAAC;IAC1GvV,IAAI,EAAExB,QAAQ;IACdyB,IAAI,EAAE,CAAC;MACC4V,OAAO,EAAE,CAACpX,YAAY,CAAC;MACvB2W,SAAS,EAAE,CAAC,GAAGC,QAAQ,CAAC;MACxBS,YAAY,EAAE,CAAC,GAAGR,UAAU,EAAExC,iBAAiB,CAAC;MAChDiD,OAAO,EAAE,CAAC,GAAGT,UAAU,EAAExC,iBAAiB;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMkD,cAAc,CAAC;EACjB,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI;EACf;EACA;EACAjT,YAAYA,CAACkT,IAAI,EAAEjT,KAAK,EAAEC,QAAQ,EAAE;IAChC,OAAO,IAAI;EACf;EACA;EACAiT,WAAWA,CAACnW,IAAI,EAAE;IACd,OAAO,IAAI;EACf;EACA0R,IAAIA,CAAA,EAAG,CAAE;EACT0E,cAAcA,CAAA,EAAG,CAAE;EACnBC,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI;EACf;EACA;EACAC,mBAAmBA,CAACC,mBAAmB,EAAEC,eAAe,EAAEC,OAAO,EAAE,CAAE;EACrE;EACAC,2BAA2BA,CAACC,sBAAsB,EAAE,CAAE;EACtDtV,KAAKA,CAAA,EAAG,CAAE;EACVD,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI;EACf;EACA;EACAwV,YAAYA,CAACC,SAAS,EAAE;IACpB,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAE,CAAE;AACtD;;AAEA;AACA;AACA;;AAEA,SAAS/W,YAAY,EAAEiE,uBAAuB,EAAEoR,YAAY,EAAEzL,oBAAoB,EAAE7B,QAAQ,EAAE7B,sBAAsB,EAAE+F,gBAAgB,EAAE6J,cAAc,EAAElD,iBAAiB,EAAE7T,QAAQ,EAAEyF,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
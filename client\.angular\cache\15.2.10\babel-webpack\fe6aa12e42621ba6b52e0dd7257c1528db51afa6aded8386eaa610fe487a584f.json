{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { NavbarModule } from 'app/layout/components/navbar/navbar.module';\nimport { ContentModule } from 'app/layout/components/content/content.module';\nimport { MenuModule } from 'app/layout/components/menu/menu.module';\nimport { HorizontalLayoutComponent } from 'app/layout/horizontal/horizontal-layout.component';\nimport { FooterModule } from '../components/footer/footer.module';\nimport * as i0 from \"@angular/core\";\nexport class HorizontalLayoutModule {\n  static #_ = this.ɵfac = function HorizontalLayoutModule_Factory(t) {\n    return new (t || HorizontalLayoutModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HorizontalLayoutModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule, CoreCommonModule, CoreSidebarModule, NavbarModule, ContentModule, MenuModule, FooterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HorizontalLayoutModule, {\n    declarations: [HorizontalLayoutComponent],\n    imports: [RouterModule, CoreCommonModule, CoreSidebarModule, NavbarModule, ContentModule, MenuModule, FooterModule],\n    exports: [HorizontalLayoutComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,SAASC,YAAY,QAAQ,4CAA4C;AACzE,SAASC,aAAa,QAAQ,8CAA8C;AAC5E,SAASC,UAAU,QAAQ,wCAAwC;AAEnE,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,YAAY,QAAQ,oCAAoC;;AAOjE,OAAM,MAAOC,sBAAsB;EAAA,QAAAC,CAAA;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA;UAAtBF;EAAsB;EAAA,QAAAG,EAAA;cAHvBX,YAAY,EAAEE,gBAAgB,EAAED,iBAAiB,EAAEE,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAEE,YAAY;EAAA;;;2EAGvGC,sBAAsB;IAAAI,YAAA,GAJlBN,yBAAyB;IAAAO,OAAA,GAC9Bb,YAAY,EAAEE,gBAAgB,EAAED,iBAAiB,EAAEE,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAEE,YAAY;IAAAO,OAAA,GACxGR,yBAAyB;EAAA;AAAA", "names": ["RouterModule", "CoreSidebarModule", "CoreCommonModule", "NavbarModule", "ContentModule", "MenuModule", "HorizontalLayoutComponent", "FooterModule", "HorizontalLayoutModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\horizontal\\horizontal-layout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\n\r\nimport { NavbarModule } from 'app/layout/components/navbar/navbar.module';\r\nimport { ContentModule } from 'app/layout/components/content/content.module';\r\nimport { MenuModule } from 'app/layout/components/menu/menu.module';\r\n\r\nimport { HorizontalLayoutComponent } from 'app/layout/horizontal/horizontal-layout.component';\r\nimport { FooterModule } from '../components/footer/footer.module';\r\n\r\n@NgModule({\r\n  declarations: [HorizontalLayoutComponent],\r\n  imports: [RouterModule, CoreCommonModule, CoreSidebarModule, NavbarModule, ContentModule, MenuModule, FooterModule],\r\n  exports: [HorizontalLayoutComponent]\r\n})\r\nexport class HorizontalLayoutModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
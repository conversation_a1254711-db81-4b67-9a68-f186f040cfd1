{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, EventEmitter, Component, Output, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nfunction StripeSource_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵtext(2, \"Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction StripeCard_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵtext(2, \"Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction StripeBank_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵtext(2, \"Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst STRIPE_PUBLISHABLE_KEY = new InjectionToken('Stripe Publishable Key');\nconst STRIPE_OPTIONS = new InjectionToken('Stripe Options');\nclass StripeScriptTag {\n  constructor(document, key, options) {\n    this.document = document;\n    this.src = \"https://js.stripe.com/v3/\";\n    this.window = this.document.defaultView;\n    this.load = this.injectIntoHead();\n    if (key) this.setPublishableKey(key, options);\n  }\n  promiseStripe() {\n    return this.load;\n  }\n  promiseInstance() {\n    return this.promiseStripe().then(stripe => {\n      if (!this.StripeInstance) {\n        const err = new Error(\"Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()\");\n        err[\"code\"] = \"STRIPEKEYNOTSET\";\n        throw err;\n        //return Promise.reject( err )\n      }\n\n      return this.StripeInstance;\n    });\n  }\n  setPublishableKey(key, options) {\n    return this.load.then(() => this.StripeInstance = this.Stripe(key, options));\n  }\n  injectIntoHead() {\n    if (this.window && this.window[\"Stripe\"]) {\n      return Promise.resolve(this.Stripe = this.window[\"Stripe\"]);\n    }\n    return new Promise((res, rej) => {\n      const head = this.getTargetTagDropElement();\n      const script = this.document.createElement(\"script\");\n      script.setAttribute(\"src\", this.src);\n      script.setAttribute(\"type\", \"text/javascript\");\n      script.addEventListener(\"load\", () => {\n        this.Stripe = this.grabStripe();\n        res(this.Stripe);\n      });\n      head.appendChild(script);\n    });\n  }\n  grabStripe() {\n    return window[\"Stripe\"];\n  }\n  getTargetTagDropElement() {\n    let elm = this.document.getElementsByTagName(\"head\");\n    if (elm.length) return elm[0];\n    return this.document.getElementsByTagName(\"body\")[0];\n  }\n}\nStripeScriptTag.ɵfac = function StripeScriptTag_Factory(t) {\n  return new (t || StripeScriptTag)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(STRIPE_PUBLISHABLE_KEY), i0.ɵɵinject(STRIPE_OPTIONS));\n};\nStripeScriptTag.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: StripeScriptTag,\n  factory: StripeScriptTag.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StripeScriptTag, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [STRIPE_PUBLISHABLE_KEY]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [STRIPE_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nclass StripeComponent {\n  constructor(StripeScriptTag) {\n    this.StripeScriptTag = StripeScriptTag;\n    this.catcher = new EventEmitter();\n    this.invalidChange = new EventEmitter();\n  }\n  ngOnInit() {\n    this.init();\n  }\n  init() {\n    return this.StripeScriptTag.promiseInstance().then(i => this.stripe = i);\n  }\n}\nStripeComponent.ɵfac = function StripeComponent_Factory(t) {\n  return new (t || StripeComponent)(i0.ɵɵdirectiveInject(StripeScriptTag));\n};\nStripeComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: StripeComponent,\n  selectors: [[\"stripe-component\"]],\n  inputs: {\n    invalid: \"invalid\"\n  },\n  outputs: {\n    catcher: \"catch\",\n    invalidChange: \"invalidChange\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function StripeComponent_Template(rf, ctx) {},\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StripeComponent, [{\n    type: Component,\n    args: [{\n      selector: \"stripe-component\",\n      template: ``\n    }]\n  }], function () {\n    return [{\n      type: StripeScriptTag\n    }];\n  }, {\n    catcher: [{\n      type: Output,\n      args: [\"catch\"]\n    }],\n    invalid: [{\n      type: Input\n    }],\n    invalidChange: [{\n      type: Output\n    }]\n  });\n})();\nclass StripeSource extends StripeComponent {\n  constructor(StripeScriptTag) {\n    super(StripeScriptTag);\n    this.StripeScriptTag = StripeScriptTag;\n    this.sourceChange = new EventEmitter();\n    this.paymentMethodChange = new EventEmitter();\n  }\n  createSource(extraData) {\n    delete this.invalid;\n    this.invalidChange.emit(this.invalid);\n    return this.stripe.createSource(this.elements, extraData).then(result => this.processSourceResult(result));\n  }\n  processSourceResult(result) {\n    if (result.error) {\n      const rError = result.error;\n      if (rError.type === \"validation_error\") {\n        this.invalidChange.emit(this.invalid = rError);\n      } else {\n        this.catcher.emit(rError);\n        throw rError;\n      }\n    }\n    const source = result.source;\n    if (source) {\n      this.sourceChange.emit(this.source = source);\n      return source;\n    }\n  }\n  createPaymentMethod(extraData) {\n    delete this.invalid;\n    this.invalidChange.emit(this.invalid);\n    return this.stripe.createPaymentMethod('card', this.elements, extraData).then(result => this.processPaymentMethodResult(result));\n  }\n  processPaymentMethodResult(result) {\n    if (result.error) {\n      const rError = result.error;\n      if (rError.type === \"validation_error\") {\n        this.invalidChange.emit(this.invalid = rError);\n      } else {\n        this.catcher.emit(rError);\n        throw rError;\n      }\n    }\n    const paymentMethod = result.paymentMethod;\n    if (paymentMethod) {\n      this.paymentMethodChange.emit(this.paymentMethod = paymentMethod);\n      return paymentMethod;\n    }\n  }\n}\nStripeSource.ɵfac = function StripeSource_Factory(t) {\n  return new (t || StripeSource)(i0.ɵɵdirectiveInject(StripeScriptTag));\n};\nStripeSource.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: StripeSource,\n  selectors: [[\"stripe-source\"]],\n  inputs: {\n    source: \"source\",\n    paymentMethod: \"paymentMethod\"\n  },\n  outputs: {\n    sourceChange: \"sourceChange\",\n    paymentMethodChange: \"paymentMethodChange\"\n  },\n  exportAs: [\"StripeSource\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[4, \"ngIf\"], [2, \"color\", \"red\"]],\n  template: function StripeSource_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, StripeSource_ng_container_0_Template, 3, 0, \"ng-container\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.StripeScriptTag.StripeInstance);\n    }\n  },\n  dependencies: [i2.NgIf],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StripeSource, [{\n    type: Component,\n    args: [{\n      selector: \"stripe-source\",\n      template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `,\n      exportAs: \"StripeSource\"\n    }]\n  }], function () {\n    return [{\n      type: StripeScriptTag\n    }];\n  }, {\n    source: [{\n      type: Input\n    }],\n    sourceChange: [{\n      type: Output\n    }],\n    paymentMethod: [{\n      type: Input\n    }],\n    paymentMethodChange: [{\n      type: Output\n    }]\n  });\n})();\nclass StripeCard extends StripeSource {\n  constructor(ElementRef, StripeScriptTag) {\n    super(StripeScriptTag);\n    this.ElementRef = ElementRef;\n    this.StripeScriptTag = StripeScriptTag;\n    this.tokenChange = new EventEmitter();\n    this.cardMounted = new EventEmitter();\n    this.complete = false;\n    this.completeChange = new EventEmitter();\n    this.changed = new EventEmitter();\n    this.drawn = false;\n  }\n  ngOnInit() {\n    super.init().then(() => this.redraw());\n  }\n  ngOnChanges(changes) {\n    if (this.drawn && (changes.options || changes.createOptions)) {\n      this.redraw();\n    }\n  }\n  redraw() {\n    if (this.drawn) {\n      this.elements.unmount();\n      this.elements.destroy();\n    }\n    this.elements = this.stripe.elements(this.createOptions).create('card', this.options);\n    this.elements.mount(this.ElementRef.nativeElement);\n    this.cardMounted.emit(this.elements);\n    this.elements.on('change', result => {\n      this.changed.emit(result);\n      if (result.complete || this.complete && !result.complete) {\n        this.completeChange.emit(this.complete = result.complete);\n      }\n    });\n    this.elements.addEventListener('change', result => {\n      if (result.error) {\n        this.invalidChange.emit(this.invalid = result.error);\n      }\n    });\n    this.drawn = true;\n  }\n  createToken(extraData) {\n    delete this.invalid;\n    this.invalidChange.emit(this.invalid);\n    return this.stripe.createToken(this.elements, extraData).then(result => {\n      if (result.error) {\n        if (result.error.type == \"validation_error\") {\n          this.invalidChange.emit(this.invalid = result.error);\n        } else {\n          this.catcher.emit(result.error);\n          throw result.error;\n        }\n      } else {\n        this.tokenChange.emit(this.token = result.token);\n        return result.token;\n      }\n    });\n  }\n}\nStripeCard.ɵfac = function StripeCard_Factory(t) {\n  return new (t || StripeCard)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(StripeScriptTag));\n};\nStripeCard.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: StripeCard,\n  selectors: [[\"stripe-card\"]],\n  inputs: {\n    createOptions: \"createOptions\",\n    options: \"options\",\n    token: \"token\",\n    complete: \"complete\"\n  },\n  outputs: {\n    tokenChange: \"tokenChange\",\n    cardMounted: \"cardMounted\",\n    completeChange: \"completeChange\",\n    changed: \"changed\"\n  },\n  exportAs: [\"StripeCard\"],\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[4, \"ngIf\"], [2, \"color\", \"red\"]],\n  template: function StripeCard_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, StripeCard_ng_container_0_Template, 3, 0, \"ng-container\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.StripeScriptTag.StripeInstance);\n    }\n  },\n  dependencies: [i2.NgIf],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StripeCard, [{\n    type: Component,\n    args: [{\n      selector: \"stripe-card\",\n      template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `,\n      exportAs: \"StripeCard\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: StripeScriptTag\n    }];\n  }, {\n    createOptions: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    token: [{\n      type: Input\n    }],\n    tokenChange: [{\n      type: Output\n    }],\n    cardMounted: [{\n      type: Output\n    }],\n    complete: [{\n      type: Input\n    }],\n    completeChange: [{\n      type: Output\n    }],\n    changed: [{\n      type: Output\n    }]\n  });\n})();\nclass StripeBank extends StripeComponent {\n  constructor(StripeScriptTag) {\n    super(StripeScriptTag);\n    this.StripeScriptTag = StripeScriptTag;\n    this.tokenChange = new EventEmitter();\n  }\n  createToken(data) {\n    delete this.invalid;\n    this.invalidChange.emit(this.invalid);\n    return this.stripe.createToken('bank_account', data).then(result => {\n      if (result.error) {\n        if (result.error.type == \"validation_error\") {\n          this.invalidChange.emit(this.invalid = result.error);\n        } else {\n          this.catcher.emit(result.error);\n          throw result.error;\n        }\n      } else {\n        this.tokenChange.emit(this.token = result.token);\n        return result.token;\n      }\n    });\n  }\n}\nStripeBank.ɵfac = function StripeBank_Factory(t) {\n  return new (t || StripeBank)(i0.ɵɵdirectiveInject(StripeScriptTag));\n};\nStripeBank.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: StripeBank,\n  selectors: [[\"stripe-bank\"]],\n  inputs: {\n    options: \"options\",\n    token: \"token\"\n  },\n  outputs: {\n    tokenChange: \"tokenChange\"\n  },\n  exportAs: [\"StripeBank\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[4, \"ngIf\"], [2, \"color\", \"red\"]],\n  template: function StripeBank_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, StripeBank_ng_container_0_Template, 3, 0, \"ng-container\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.StripeScriptTag.StripeInstance);\n    }\n  },\n  dependencies: [i2.NgIf],\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StripeBank, [{\n    type: Component,\n    args: [{\n      selector: \"stripe-bank\",\n      template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `,\n      exportAs: \"StripeBank\"\n    }]\n  }], function () {\n    return [{\n      type: StripeScriptTag\n    }];\n  }, {\n    options: [{\n      type: Input\n    }],\n    token: [{\n      type: Input\n    }],\n    tokenChange: [{\n      type: Output\n    }]\n  });\n})();\nconst declarations = [StripeComponent, StripeSource, StripeCard, StripeBank];\nclass StripeModule {\n  static forRoot(publishableKey, options) {\n    return {\n      ngModule: StripeModule,\n      providers: [StripeScriptTag, {\n        provide: STRIPE_PUBLISHABLE_KEY,\n        useValue: publishableKey\n      }, {\n        provide: STRIPE_OPTIONS,\n        useValue: options\n      }]\n    };\n  }\n}\nStripeModule.ɵfac = function StripeModule_Factory(t) {\n  return new (t || StripeModule)();\n};\nStripeModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: StripeModule\n});\nStripeModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StripeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations,\n      // providers: [ StripeScriptTag ],\n      exports: [...declarations]\n    }]\n  }], null, null);\n})();\n/**\n * @deprecated Please import `StripeModule` directly\n */\nconst Module = StripeModule;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Module, STRIPE_OPTIONS, STRIPE_PUBLISHABLE_KEY, StripeBank, StripeCard, StripeComponent, StripeModule, StripeScriptTag, StripeSource };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Injectable", "Inject", "EventEmitter", "Component", "Output", "Input", "NgModule", "i2", "DOCUMENT", "CommonModule", "StripeSource_ng_container_0_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "StripeCard_ng_container_0_Template", "StripeBank_ng_container_0_Template", "STRIPE_PUBLISHABLE_KEY", "STRIPE_OPTIONS", "StripeScriptTag", "constructor", "document", "key", "options", "src", "window", "defaultView", "load", "injectIntoHead", "setPublishableKey", "promiseStripe", "promiseInstance", "then", "stripe", "StripeInstance", "err", "Error", "Stripe", "Promise", "resolve", "res", "rej", "head", "getTargetTagDropElement", "script", "createElement", "setAttribute", "addEventListener", "grabStripe", "append<PERSON><PERSON><PERSON>", "elm", "getElementsByTagName", "length", "ɵfac", "StripeScriptTag_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "undefined", "decorators", "StripeComponent", "catcher", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "init", "i", "StripeComponent_Factory", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "invalid", "outputs", "decls", "vars", "template", "StripeComponent_Template", "encapsulation", "selector", "StripeSource", "sourceChange", "paymentMethodChange", "createSource", "extraData", "emit", "elements", "result", "processSourceResult", "error", "r<PERSON><PERSON><PERSON>", "source", "createPaymentMethod", "processPaymentMethodResult", "paymentMethod", "StripeSource_Factory", "exportAs", "features", "ɵɵInheritDefinitionFeature", "consts", "StripeSource_Template", "ɵɵtemplate", "ɵɵproperty", "dependencies", "NgIf", "StripeCard", "ElementRef", "tokenChange", "cardMounted", "complete", "completeChange", "changed", "drawn", "redraw", "ngOnChanges", "changes", "createOptions", "unmount", "destroy", "create", "mount", "nativeElement", "on", "createToken", "StripeCard_Factory", "ɵɵNgOnChangesFeature", "StripeCard_Template", "StripeBank", "data", "StripeBank_Factory", "StripeBank_Template", "declarations", "StripeModule", "forRoot", "publishableKey", "ngModule", "providers", "provide", "useValue", "StripeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "<PERSON><PERSON><PERSON>"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/stripe-angular/fesm2020/stripe-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, EventEmitter, Component, Output, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\n\nconst STRIPE_PUBLISHABLE_KEY = new InjectionToken('Stripe Publishable Key');\nconst STRIPE_OPTIONS = new InjectionToken('Stripe Options');\n\nclass StripeScriptTag {\n    constructor(document, key, options) {\n        this.document = document;\n        this.src = \"https://js.stripe.com/v3/\";\n        this.window = this.document.defaultView;\n        this.load = this.injectIntoHead();\n        if (key)\n            this.setPublishableKey(key, options);\n    }\n    promiseStripe() {\n        return this.load;\n    }\n    promiseInstance() {\n        return this.promiseStripe()\n            .then(stripe => {\n            if (!this.StripeInstance) {\n                const err = new Error(\"Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()\");\n                err[\"code\"] = \"STRIPEKEYNOTSET\";\n                throw err;\n                //return Promise.reject( err )\n            }\n            return this.StripeInstance;\n        });\n    }\n    setPublishableKey(key, options) {\n        return this.load.then(() => this.StripeInstance = this.Stripe(key, options));\n    }\n    injectIntoHead() {\n        if (this.window && this.window[\"Stripe\"]) {\n            return Promise.resolve(this.Stripe = this.window[\"Stripe\"]);\n        }\n        return new Promise((res, rej) => {\n            const head = this.getTargetTagDropElement();\n            const script = this.document.createElement(\"script\");\n            script.setAttribute(\"src\", this.src);\n            script.setAttribute(\"type\", \"text/javascript\");\n            script.addEventListener(\"load\", () => {\n                this.Stripe = this.grabStripe();\n                res(this.Stripe);\n            });\n            head.appendChild(script);\n        });\n    }\n    grabStripe() {\n        return window[\"Stripe\"];\n    }\n    getTargetTagDropElement() {\n        let elm = this.document.getElementsByTagName(\"head\");\n        if (elm.length)\n            return elm[0];\n        return this.document.getElementsByTagName(\"body\")[0];\n    }\n}\nStripeScriptTag.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeScriptTag, deps: [{ token: DOCUMENT }, { token: STRIPE_PUBLISHABLE_KEY }, { token: STRIPE_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable });\nStripeScriptTag.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeScriptTag, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeScriptTag, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [STRIPE_PUBLISHABLE_KEY]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [STRIPE_OPTIONS]\n                }] }]; } });\n\nclass StripeComponent {\n    constructor(StripeScriptTag) {\n        this.StripeScriptTag = StripeScriptTag;\n        this.catcher = new EventEmitter();\n        this.invalidChange = new EventEmitter();\n    }\n    ngOnInit() {\n        this.init();\n    }\n    init() {\n        return this.StripeScriptTag.promiseInstance()\n            .then(i => this.stripe = i);\n    }\n}\nStripeComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeComponent, deps: [{ token: StripeScriptTag }], target: i0.ɵɵFactoryTarget.Component });\nStripeComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: StripeComponent, selector: \"stripe-component\", inputs: { invalid: \"invalid\" }, outputs: { catcher: \"catch\", invalidChange: \"invalidChange\" }, ngImport: i0, template: ``, isInline: true });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: \"stripe-component\", template: ``\n                }]\n        }], ctorParameters: function () { return [{ type: StripeScriptTag }]; }, propDecorators: { catcher: [{\n                type: Output,\n                args: [\"catch\"]\n            }], invalid: [{\n                type: Input\n            }], invalidChange: [{\n                type: Output\n            }] } });\n\nclass StripeSource extends StripeComponent {\n    constructor(StripeScriptTag) {\n        super(StripeScriptTag);\n        this.StripeScriptTag = StripeScriptTag;\n        this.sourceChange = new EventEmitter();\n        this.paymentMethodChange = new EventEmitter();\n    }\n    createSource(extraData) {\n        delete this.invalid;\n        this.invalidChange.emit(this.invalid);\n        return this.stripe.createSource(this.elements, extraData)\n            .then((result) => this.processSourceResult(result));\n    }\n    processSourceResult(result) {\n        if (result.error) {\n            const rError = result.error;\n            if (rError.type === \"validation_error\") {\n                this.invalidChange.emit(this.invalid = rError);\n            }\n            else {\n                this.catcher.emit(rError);\n                throw rError;\n            }\n        }\n        const source = result.source;\n        if (source) {\n            this.sourceChange.emit(this.source = source);\n            return source;\n        }\n    }\n    createPaymentMethod(extraData) {\n        delete this.invalid;\n        this.invalidChange.emit(this.invalid);\n        return this.stripe.createPaymentMethod('card', this.elements, extraData)\n            .then((result) => this.processPaymentMethodResult(result));\n    }\n    processPaymentMethodResult(result) {\n        if (result.error) {\n            const rError = result.error;\n            if (rError.type === \"validation_error\") {\n                this.invalidChange.emit(this.invalid = rError);\n            }\n            else {\n                this.catcher.emit(rError);\n                throw rError;\n            }\n        }\n        const paymentMethod = result.paymentMethod;\n        if (paymentMethod) {\n            this.paymentMethodChange.emit(this.paymentMethod = paymentMethod);\n            return paymentMethod;\n        }\n    }\n}\nStripeSource.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeSource, deps: [{ token: StripeScriptTag }], target: i0.ɵɵFactoryTarget.Component });\nStripeSource.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: StripeSource, selector: \"stripe-source\", inputs: { source: \"source\", paymentMethod: \"paymentMethod\" }, outputs: { sourceChange: \"sourceChange\", paymentMethodChange: \"paymentMethodChange\" }, exportAs: [\"StripeSource\"], usesInheritance: true, ngImport: i0, template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `, isInline: true, directives: [{ type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeSource, decorators: [{\n            type: Component,\n            args: [{\n                    selector: \"stripe-source\",\n                    template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `,\n                    exportAs: \"StripeSource\"\n                }]\n        }], ctorParameters: function () { return [{ type: StripeScriptTag }]; }, propDecorators: { source: [{\n                type: Input\n            }], sourceChange: [{\n                type: Output\n            }], paymentMethod: [{\n                type: Input\n            }], paymentMethodChange: [{\n                type: Output\n            }] } });\n\nclass StripeCard extends StripeSource {\n    constructor(ElementRef, StripeScriptTag) {\n        super(StripeScriptTag);\n        this.ElementRef = ElementRef;\n        this.StripeScriptTag = StripeScriptTag;\n        this.tokenChange = new EventEmitter();\n        this.cardMounted = new EventEmitter();\n        this.complete = false;\n        this.completeChange = new EventEmitter();\n        this.changed = new EventEmitter();\n        this.drawn = false;\n    }\n    ngOnInit() {\n        super.init().then(() => this.redraw());\n    }\n    ngOnChanges(changes) {\n        if (this.drawn && (changes.options || changes.createOptions)) {\n            this.redraw();\n        }\n    }\n    redraw() {\n        if (this.drawn) {\n            this.elements.unmount();\n            this.elements.destroy();\n        }\n        this.elements = this.stripe.elements(this.createOptions).create('card', this.options);\n        this.elements.mount(this.ElementRef.nativeElement);\n        this.cardMounted.emit(this.elements);\n        this.elements.on('change', (result) => {\n            this.changed.emit(result);\n            if (result.complete || (this.complete && !result.complete)) {\n                this.completeChange.emit(this.complete = result.complete);\n            }\n        });\n        this.elements.addEventListener('change', (result) => {\n            if (result.error) {\n                this.invalidChange.emit(this.invalid = result.error);\n            }\n        });\n        this.drawn = true;\n    }\n    createToken(extraData) {\n        delete this.invalid;\n        this.invalidChange.emit(this.invalid);\n        return this.stripe.createToken(this.elements, extraData)\n            .then((result) => {\n            if (result.error) {\n                if (result.error.type == \"validation_error\") {\n                    this.invalidChange.emit(this.invalid = result.error);\n                }\n                else {\n                    this.catcher.emit(result.error);\n                    throw result.error;\n                }\n            }\n            else {\n                this.tokenChange.emit(this.token = result.token);\n                return result.token;\n            }\n        });\n    }\n}\nStripeCard.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeCard, deps: [{ token: i0.ElementRef }, { token: StripeScriptTag }], target: i0.ɵɵFactoryTarget.Component });\nStripeCard.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: StripeCard, selector: \"stripe-card\", inputs: { createOptions: \"createOptions\", options: \"options\", token: \"token\", complete: \"complete\" }, outputs: { tokenChange: \"tokenChange\", cardMounted: \"cardMounted\", completeChange: \"completeChange\", changed: \"changed\" }, exportAs: [\"StripeCard\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `, isInline: true, directives: [{ type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeCard, decorators: [{\n            type: Component,\n            args: [{\n                    selector: \"stripe-card\",\n                    template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `,\n                    exportAs: \"StripeCard\"\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: StripeScriptTag }]; }, propDecorators: { createOptions: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], token: [{\n                type: Input\n            }], tokenChange: [{\n                type: Output\n            }], cardMounted: [{\n                type: Output\n            }], complete: [{\n                type: Input\n            }], completeChange: [{\n                type: Output\n            }], changed: [{\n                type: Output\n            }] } });\n\nclass StripeBank extends StripeComponent {\n    constructor(StripeScriptTag) {\n        super(StripeScriptTag);\n        this.StripeScriptTag = StripeScriptTag;\n        this.tokenChange = new EventEmitter();\n    }\n    createToken(data) {\n        delete this.invalid;\n        this.invalidChange.emit(this.invalid);\n        return this.stripe.createToken('bank_account', data)\n            .then((result) => {\n            if (result.error) {\n                if (result.error.type == \"validation_error\") {\n                    this.invalidChange.emit(this.invalid = result.error);\n                }\n                else {\n                    this.catcher.emit(result.error);\n                    throw result.error;\n                }\n            }\n            else {\n                this.tokenChange.emit(this.token = result.token);\n                return result.token;\n            }\n        });\n    }\n}\nStripeBank.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeBank, deps: [{ token: StripeScriptTag }], target: i0.ɵɵFactoryTarget.Component });\nStripeBank.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: StripeBank, selector: \"stripe-bank\", inputs: { options: \"options\", token: \"token\" }, outputs: { tokenChange: \"tokenChange\" }, exportAs: [\"StripeBank\"], usesInheritance: true, ngImport: i0, template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `, isInline: true, directives: [{ type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeBank, decorators: [{\n            type: Component,\n            args: [{\n                    selector: \"stripe-bank\",\n                    template: `\n      <ng-container *ngIf=\"!StripeScriptTag.StripeInstance\">\n          <div style=\"color:red;\">Stripe PublishableKey NOT SET. Use method StripeScriptTag.setPublishableKey()</div>\n      </ng-container>\n  `,\n                    exportAs: \"StripeBank\"\n                }]\n        }], ctorParameters: function () { return [{ type: StripeScriptTag }]; }, propDecorators: { options: [{\n                type: Input\n            }], token: [{\n                type: Input\n            }], tokenChange: [{\n                type: Output\n            }] } });\n\nconst declarations = [\n    StripeComponent,\n    StripeSource,\n    StripeCard,\n    StripeBank\n];\nclass StripeModule {\n    static forRoot(publishableKey, options) {\n        return {\n            ngModule: StripeModule,\n            providers: [\n                StripeScriptTag,\n                {\n                    provide: STRIPE_PUBLISHABLE_KEY,\n                    useValue: publishableKey\n                },\n                {\n                    provide: STRIPE_OPTIONS,\n                    useValue: options\n                }\n            ],\n        };\n    }\n}\nStripeModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nStripeModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeModule, declarations: [StripeComponent,\n        StripeSource,\n        StripeCard,\n        StripeBank], imports: [CommonModule], exports: [StripeComponent,\n        StripeSource,\n        StripeCard,\n        StripeBank] });\nStripeModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeModule, imports: [[\n            CommonModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: StripeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule\n                    ],\n                    declarations,\n                    // providers: [ StripeScriptTag ],\n                    exports: [...declarations]\n                }]\n        }] });\n/**\n * @deprecated Please import `StripeModule` directly\n */\nconst Module = StripeModule;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Module, STRIPE_OPTIONS, STRIPE_PUBLISHABLE_KEY, StripeBank, StripeCard, StripeComponent, StripeModule, StripeScriptTag, StripeSource };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACpH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0D0Cb,EAAE,CAAAe,uBAAA,EAsG1C,CAAC;IAtGuCf,EAAE,CAAAgB,cAAA,YAuGpE,CAAC;IAvGiEhB,EAAE,CAAAiB,MAAA,mFAuGS,CAAC;IAvGZjB,EAAE,CAAAkB,YAAA,CAuGe,CAAC;IAvGlBlB,EAAE,CAAAmB,qBAAA,CAwGjF,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxG8Eb,EAAE,CAAAe,uBAAA,EA+L1C,CAAC;IA/LuCf,EAAE,CAAAgB,cAAA,YAgMpE,CAAC;IAhMiEhB,EAAE,CAAAiB,MAAA,mFAgMS,CAAC;IAhMZjB,EAAE,CAAAkB,YAAA,CAgMe,CAAC;IAhMlBlB,EAAE,CAAAmB,qBAAA,CAiMjF,CAAC;EAAA;AAAA;AAAA,SAAAE,mCAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjM8Eb,EAAE,CAAAe,uBAAA,EA6P1C,CAAC;IA7PuCf,EAAE,CAAAgB,cAAA,YA8PpE,CAAC;IA9PiEhB,EAAE,CAAAiB,MAAA,mFA8PS,CAAC;IA9PZjB,EAAE,CAAAkB,YAAA,CA8Pe,CAAC;IA9PlBlB,EAAE,CAAAmB,qBAAA,CA+PjF,CAAC;EAAA;AAAA;AAvTrB,MAAMG,sBAAsB,GAAG,IAAIrB,cAAc,CAAC,wBAAwB,CAAC;AAC3E,MAAMsB,cAAc,GAAG,IAAItB,cAAc,CAAC,gBAAgB,CAAC;AAE3D,MAAMuB,eAAe,CAAC;EAClBC,WAAWA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAChC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACG,GAAG,GAAG,2BAA2B;IACtC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACJ,QAAQ,CAACK,WAAW;IACvC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACjC,IAAIN,GAAG,EACH,IAAI,CAACO,iBAAiB,CAACP,GAAG,EAAEC,OAAO,CAAC;EAC5C;EACAO,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACH,IAAI;EACpB;EACAI,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,aAAa,CAAC,CAAC,CACtBE,IAAI,CAACC,MAAM,IAAI;MAChB,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;QACtB,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,+EAA+E,CAAC;QACtGD,GAAG,CAAC,MAAM,CAAC,GAAG,iBAAiB;QAC/B,MAAMA,GAAG;QACT;MACJ;;MACA,OAAO,IAAI,CAACD,cAAc;IAC9B,CAAC,CAAC;EACN;EACAL,iBAAiBA,CAACP,GAAG,EAAEC,OAAO,EAAE;IAC5B,OAAO,IAAI,CAACI,IAAI,CAACK,IAAI,CAAC,MAAM,IAAI,CAACE,cAAc,GAAG,IAAI,CAACG,MAAM,CAACf,GAAG,EAAEC,OAAO,CAAC,CAAC;EAChF;EACAK,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACH,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,QAAQ,CAAC,EAAE;MACtC,OAAOa,OAAO,CAACC,OAAO,CAAC,IAAI,CAACF,MAAM,GAAG,IAAI,CAACZ,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/D;IACA,OAAO,IAAIa,OAAO,CAAC,CAACE,GAAG,EAAEC,GAAG,KAAK;MAC7B,MAAMC,IAAI,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC3C,MAAMC,MAAM,GAAG,IAAI,CAACvB,QAAQ,CAACwB,aAAa,CAAC,QAAQ,CAAC;MACpDD,MAAM,CAACE,YAAY,CAAC,KAAK,EAAE,IAAI,CAACtB,GAAG,CAAC;MACpCoB,MAAM,CAACE,YAAY,CAAC,MAAM,EAAE,iBAAiB,CAAC;MAC9CF,MAAM,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;QAClC,IAAI,CAACV,MAAM,GAAG,IAAI,CAACW,UAAU,CAAC,CAAC;QAC/BR,GAAG,CAAC,IAAI,CAACH,MAAM,CAAC;MACpB,CAAC,CAAC;MACFK,IAAI,CAACO,WAAW,CAACL,MAAM,CAAC;IAC5B,CAAC,CAAC;EACN;EACAI,UAAUA,CAAA,EAAG;IACT,OAAOvB,MAAM,CAAC,QAAQ,CAAC;EAC3B;EACAkB,uBAAuBA,CAAA,EAAG;IACtB,IAAIO,GAAG,GAAG,IAAI,CAAC7B,QAAQ,CAAC8B,oBAAoB,CAAC,MAAM,CAAC;IACpD,IAAID,GAAG,CAACE,MAAM,EACV,OAAOF,GAAG,CAAC,CAAC,CAAC;IACjB,OAAO,IAAI,CAAC7B,QAAQ,CAAC8B,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACxD;AACJ;AACAhC,eAAe,CAACkC,IAAI,YAAAC,wBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyFpC,eAAe,EAAzBxB,EAAE,CAAA6D,QAAA,CAAyCnD,QAAQ,GAAnDV,EAAE,CAAA6D,QAAA,CAA8DvC,sBAAsB,GAAtFtB,EAAE,CAAA6D,QAAA,CAAiGtC,cAAc;AAAA,CAA6C;AACjQC,eAAe,CAACsC,KAAK,kBAD8E9D,EAAE,CAAA+D,kBAAA;EAAAC,KAAA,EACYxC,eAAe;EAAAyC,OAAA,EAAfzC,eAAe,CAAAkC,IAAA;EAAAQ,UAAA,EAAc;AAAM,EAAG;AACvJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFmGnE,EAAE,CAAAoE,iBAAA,CAET5C,eAAe,EAAc,CAAC;IAC9G6C,IAAI,EAAEnE,UAAU;IAChBoE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAElE,MAAM;QACZmE,IAAI,EAAE,CAAC5D,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE2D,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAElE,MAAM;QACZmE,IAAI,EAAE,CAAChD,sBAAsB;MACjC,CAAC;IAAE,CAAC,EAAE;MAAE+C,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAElE,MAAM;QACZmE,IAAI,EAAE,CAAC/C,cAAc;MACzB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMkD,eAAe,CAAC;EAClBhD,WAAWA,CAACD,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACkD,OAAO,GAAG,IAAItE,YAAY,CAAC,CAAC;IACjC,IAAI,CAACuE,aAAa,GAAG,IAAIvE,YAAY,CAAC,CAAC;EAC3C;EACAwE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,IAAI,CAAC,CAAC;EACf;EACAA,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACrD,eAAe,CAACY,eAAe,CAAC,CAAC,CACxCC,IAAI,CAACyC,CAAC,IAAI,IAAI,CAACxC,MAAM,GAAGwC,CAAC,CAAC;EACnC;AACJ;AACAL,eAAe,CAACf,IAAI,YAAAqB,wBAAAnB,CAAA;EAAA,YAAAA,CAAA,IAAyFa,eAAe,EA9BzBzE,EAAE,CAAAgF,iBAAA,CA8ByCxD,eAAe;AAAA,CAA4C;AACzMiD,eAAe,CAACQ,IAAI,kBA/B+EjF,EAAE,CAAAkF,iBAAA;EAAAb,IAAA,EA+BJI,eAAe;EAAAU,SAAA;EAAAC,MAAA;IAAAC,OAAA;EAAA;EAAAC,OAAA;IAAAZ,OAAA;IAAAC,aAAA;EAAA;EAAAY,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,yBAAA7E,EAAA,EAAAC,GAAA;EAAA6E,aAAA;AAAA,EAA4K;AAC5R;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAhCmGnE,EAAE,CAAAoE,iBAAA,CAgCTK,eAAe,EAAc,CAAC;IAC9GJ,IAAI,EAAEhE,SAAS;IACfiE,IAAI,EAAE,CAAC;MACCsB,QAAQ,EAAE,kBAAkB;MAAEH,QAAQ,EAAG;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAE7C;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkD,OAAO,EAAE,CAAC;MAC7FL,IAAI,EAAE/D,MAAM;MACZgE,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEe,OAAO,EAAE,CAAC;MACVhB,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEoE,aAAa,EAAE,CAAC;MAChBN,IAAI,EAAE/D;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuF,YAAY,SAASpB,eAAe,CAAC;EACvChD,WAAWA,CAACD,eAAe,EAAE;IACzB,KAAK,CAACA,eAAe,CAAC;IACtB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACsE,YAAY,GAAG,IAAI1F,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC2F,mBAAmB,GAAG,IAAI3F,YAAY,CAAC,CAAC;EACjD;EACA4F,YAAYA,CAACC,SAAS,EAAE;IACpB,OAAO,IAAI,CAACZ,OAAO;IACnB,IAAI,CAACV,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC;IACrC,OAAO,IAAI,CAAC/C,MAAM,CAAC0D,YAAY,CAAC,IAAI,CAACG,QAAQ,EAAEF,SAAS,CAAC,CACpD5D,IAAI,CAAE+D,MAAM,IAAK,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAAC,CAAC;EAC3D;EACAC,mBAAmBA,CAACD,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACE,KAAK,EAAE;MACd,MAAMC,MAAM,GAAGH,MAAM,CAACE,KAAK;MAC3B,IAAIC,MAAM,CAAClC,IAAI,KAAK,kBAAkB,EAAE;QACpC,IAAI,CAACM,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,GAAGkB,MAAM,CAAC;MAClD,CAAC,MACI;QACD,IAAI,CAAC7B,OAAO,CAACwB,IAAI,CAACK,MAAM,CAAC;QACzB,MAAMA,MAAM;MAChB;IACJ;IACA,MAAMC,MAAM,GAAGJ,MAAM,CAACI,MAAM;IAC5B,IAAIA,MAAM,EAAE;MACR,IAAI,CAACV,YAAY,CAACI,IAAI,CAAC,IAAI,CAACM,MAAM,GAAGA,MAAM,CAAC;MAC5C,OAAOA,MAAM;IACjB;EACJ;EACAC,mBAAmBA,CAACR,SAAS,EAAE;IAC3B,OAAO,IAAI,CAACZ,OAAO;IACnB,IAAI,CAACV,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC;IACrC,OAAO,IAAI,CAAC/C,MAAM,CAACmE,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACN,QAAQ,EAAEF,SAAS,CAAC,CACnE5D,IAAI,CAAE+D,MAAM,IAAK,IAAI,CAACM,0BAA0B,CAACN,MAAM,CAAC,CAAC;EAClE;EACAM,0BAA0BA,CAACN,MAAM,EAAE;IAC/B,IAAIA,MAAM,CAACE,KAAK,EAAE;MACd,MAAMC,MAAM,GAAGH,MAAM,CAACE,KAAK;MAC3B,IAAIC,MAAM,CAAClC,IAAI,KAAK,kBAAkB,EAAE;QACpC,IAAI,CAACM,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,GAAGkB,MAAM,CAAC;MAClD,CAAC,MACI;QACD,IAAI,CAAC7B,OAAO,CAACwB,IAAI,CAACK,MAAM,CAAC;QACzB,MAAMA,MAAM;MAChB;IACJ;IACA,MAAMI,aAAa,GAAGP,MAAM,CAACO,aAAa;IAC1C,IAAIA,aAAa,EAAE;MACf,IAAI,CAACZ,mBAAmB,CAACG,IAAI,CAAC,IAAI,CAACS,aAAa,GAAGA,aAAa,CAAC;MACjE,OAAOA,aAAa;IACxB;EACJ;AACJ;AACAd,YAAY,CAACnC,IAAI,YAAAkD,qBAAAhD,CAAA;EAAA,YAAAA,CAAA,IAAyFiC,YAAY,EApGnB7F,EAAE,CAAAgF,iBAAA,CAoGmCxD,eAAe;AAAA,CAA4C;AACnMqE,YAAY,CAACZ,IAAI,kBArGkFjF,EAAE,CAAAkF,iBAAA;EAAAb,IAAA,EAqGPwB,YAAY;EAAAV,SAAA;EAAAC,MAAA;IAAAoB,MAAA;IAAAG,aAAA;EAAA;EAAArB,OAAA;IAAAQ,YAAA;IAAAC,mBAAA;EAAA;EAAAc,QAAA;EAAAC,QAAA,GArGP9G,EAAE,CAAA+G,0BAAA;EAAAxB,KAAA;EAAAC,IAAA;EAAAwB,MAAA;EAAAvB,QAAA,WAAAwB,sBAAApG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFb,EAAE,CAAAkH,UAAA,IAAAtG,oCAAA,yBAwGjF,CAAC;IAAA;IAAA,IAAAC,EAAA;MAxG8Eb,EAAE,CAAAmH,UAAA,UAAArG,GAAA,CAAAU,eAAA,CAAAe,cAsG5C,CAAC;IAAA;EAAA;EAAA6E,YAAA,GAGhB3G,EAAE,CAAC4G,IAAI;EAAA1B,aAAA;AAAA,EAAoE;AACrH;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KA1GmGnE,EAAE,CAAAoE,iBAAA,CA0GTyB,YAAY,EAAc,CAAC;IAC3GxB,IAAI,EAAEhE,SAAS;IACfiE,IAAI,EAAE,CAAC;MACCsB,QAAQ,EAAE,eAAe;MACzBH,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,GAAG;MACiBoB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExC,IAAI,EAAE7C;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEgF,MAAM,EAAE,CAAC;MAC5FnC,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEuF,YAAY,EAAE,CAAC;MACfzB,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEqG,aAAa,EAAE,CAAC;MAChBtC,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEwF,mBAAmB,EAAE,CAAC;MACtB1B,IAAI,EAAE/D;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgH,UAAU,SAASzB,YAAY,CAAC;EAClCpE,WAAWA,CAAC8F,UAAU,EAAE/F,eAAe,EAAE;IACrC,KAAK,CAACA,eAAe,CAAC;IACtB,IAAI,CAAC+F,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC/F,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACgG,WAAW,GAAG,IAAIpH,YAAY,CAAC,CAAC;IACrC,IAAI,CAACqH,WAAW,GAAG,IAAIrH,YAAY,CAAC,CAAC;IACrC,IAAI,CAACsH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,cAAc,GAAG,IAAIvH,YAAY,CAAC,CAAC;IACxC,IAAI,CAACwH,OAAO,GAAG,IAAIxH,YAAY,CAAC,CAAC;IACjC,IAAI,CAACyH,KAAK,GAAG,KAAK;EACtB;EACAjD,QAAQA,CAAA,EAAG;IACP,KAAK,CAACC,IAAI,CAAC,CAAC,CAACxC,IAAI,CAAC,MAAM,IAAI,CAACyF,MAAM,CAAC,CAAC,CAAC;EAC1C;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACH,KAAK,KAAKG,OAAO,CAACpG,OAAO,IAAIoG,OAAO,CAACC,aAAa,CAAC,EAAE;MAC1D,IAAI,CAACH,MAAM,CAAC,CAAC;IACjB;EACJ;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACD,KAAK,EAAE;MACZ,IAAI,CAAC1B,QAAQ,CAAC+B,OAAO,CAAC,CAAC;MACvB,IAAI,CAAC/B,QAAQ,CAACgC,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAAChC,QAAQ,GAAG,IAAI,CAAC7D,MAAM,CAAC6D,QAAQ,CAAC,IAAI,CAAC8B,aAAa,CAAC,CAACG,MAAM,CAAC,MAAM,EAAE,IAAI,CAACxG,OAAO,CAAC;IACrF,IAAI,CAACuE,QAAQ,CAACkC,KAAK,CAAC,IAAI,CAACd,UAAU,CAACe,aAAa,CAAC;IAClD,IAAI,CAACb,WAAW,CAACvB,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC;IACpC,IAAI,CAACA,QAAQ,CAACoC,EAAE,CAAC,QAAQ,EAAGnC,MAAM,IAAK;MACnC,IAAI,CAACwB,OAAO,CAAC1B,IAAI,CAACE,MAAM,CAAC;MACzB,IAAIA,MAAM,CAACsB,QAAQ,IAAK,IAAI,CAACA,QAAQ,IAAI,CAACtB,MAAM,CAACsB,QAAS,EAAE;QACxD,IAAI,CAACC,cAAc,CAACzB,IAAI,CAAC,IAAI,CAACwB,QAAQ,GAAGtB,MAAM,CAACsB,QAAQ,CAAC;MAC7D;IACJ,CAAC,CAAC;IACF,IAAI,CAACvB,QAAQ,CAAC/C,gBAAgB,CAAC,QAAQ,EAAGgD,MAAM,IAAK;MACjD,IAAIA,MAAM,CAACE,KAAK,EAAE;QACd,IAAI,CAAC3B,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,GAAGe,MAAM,CAACE,KAAK,CAAC;MACxD;IACJ,CAAC,CAAC;IACF,IAAI,CAACuB,KAAK,GAAG,IAAI;EACrB;EACAW,WAAWA,CAACvC,SAAS,EAAE;IACnB,OAAO,IAAI,CAACZ,OAAO;IACnB,IAAI,CAACV,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC;IACrC,OAAO,IAAI,CAAC/C,MAAM,CAACkG,WAAW,CAAC,IAAI,CAACrC,QAAQ,EAAEF,SAAS,CAAC,CACnD5D,IAAI,CAAE+D,MAAM,IAAK;MAClB,IAAIA,MAAM,CAACE,KAAK,EAAE;QACd,IAAIF,MAAM,CAACE,KAAK,CAACjC,IAAI,IAAI,kBAAkB,EAAE;UACzC,IAAI,CAACM,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,GAAGe,MAAM,CAACE,KAAK,CAAC;QACxD,CAAC,MACI;UACD,IAAI,CAAC5B,OAAO,CAACwB,IAAI,CAACE,MAAM,CAACE,KAAK,CAAC;UAC/B,MAAMF,MAAM,CAACE,KAAK;QACtB;MACJ,CAAC,MACI;QACD,IAAI,CAACkB,WAAW,CAACtB,IAAI,CAAC,IAAI,CAAClC,KAAK,GAAGoC,MAAM,CAACpC,KAAK,CAAC;QAChD,OAAOoC,MAAM,CAACpC,KAAK;MACvB;IACJ,CAAC,CAAC;EACN;AACJ;AACAsD,UAAU,CAAC5D,IAAI,YAAA+E,mBAAA7E,CAAA;EAAA,YAAAA,CAAA,IAAyF0D,UAAU,EA7LftH,EAAE,CAAAgF,iBAAA,CA6L+BhF,EAAE,CAACuH,UAAU,GA7L9CvH,EAAE,CAAAgF,iBAAA,CA6LyDxD,eAAe;AAAA,CAA4C;AACzN8F,UAAU,CAACrC,IAAI,kBA9LoFjF,EAAE,CAAAkF,iBAAA;EAAAb,IAAA,EA8LTiD,UAAU;EAAAnC,SAAA;EAAAC,MAAA;IAAA6C,aAAA;IAAArG,OAAA;IAAAoC,KAAA;IAAA0D,QAAA;EAAA;EAAApC,OAAA;IAAAkC,WAAA;IAAAC,WAAA;IAAAE,cAAA;IAAAC,OAAA;EAAA;EAAAf,QAAA;EAAAC,QAAA,GA9LH9G,EAAE,CAAA+G,0BAAA,EAAF/G,EAAE,CAAA0I,oBAAA;EAAAnD,KAAA;EAAAC,IAAA;EAAAwB,MAAA;EAAAvB,QAAA,WAAAkD,oBAAA9H,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFb,EAAE,CAAAkH,UAAA,IAAA9F,kCAAA,yBAiMjF,CAAC;IAAA;IAAA,IAAAP,EAAA;MAjM8Eb,EAAE,CAAAmH,UAAA,UAAArG,GAAA,CAAAU,eAAA,CAAAe,cA+L5C,CAAC;IAAA;EAAA;EAAA6E,YAAA,GAGhB3G,EAAE,CAAC4G,IAAI;EAAA1B,aAAA;AAAA,EAAoE;AACrH;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAnMmGnE,EAAE,CAAAoE,iBAAA,CAmMTkD,UAAU,EAAc,CAAC;IACzGjD,IAAI,EAAEhE,SAAS;IACfiE,IAAI,EAAE,CAAC;MACCsB,QAAQ,EAAE,aAAa;MACvBH,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,GAAG;MACiBoB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExC,IAAI,EAAErE,EAAE,CAACuH;IAAW,CAAC,EAAE;MAAElD,IAAI,EAAE7C;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyG,aAAa,EAAE,CAAC;MAC5H5D,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEqB,OAAO,EAAE,CAAC;MACVyC,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEyD,KAAK,EAAE,CAAC;MACRK,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEiH,WAAW,EAAE,CAAC;MACdnD,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEmH,WAAW,EAAE,CAAC;MACdpD,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEoH,QAAQ,EAAE,CAAC;MACXrD,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEoH,cAAc,EAAE,CAAC;MACjBtD,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEsH,OAAO,EAAE,CAAC;MACVvD,IAAI,EAAE/D;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsI,UAAU,SAASnE,eAAe,CAAC;EACrChD,WAAWA,CAACD,eAAe,EAAE;IACzB,KAAK,CAACA,eAAe,CAAC;IACtB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACgG,WAAW,GAAG,IAAIpH,YAAY,CAAC,CAAC;EACzC;EACAoI,WAAWA,CAACK,IAAI,EAAE;IACd,OAAO,IAAI,CAACxD,OAAO;IACnB,IAAI,CAACV,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC;IACrC,OAAO,IAAI,CAAC/C,MAAM,CAACkG,WAAW,CAAC,cAAc,EAAEK,IAAI,CAAC,CAC/CxG,IAAI,CAAE+D,MAAM,IAAK;MAClB,IAAIA,MAAM,CAACE,KAAK,EAAE;QACd,IAAIF,MAAM,CAACE,KAAK,CAACjC,IAAI,IAAI,kBAAkB,EAAE;UACzC,IAAI,CAACM,aAAa,CAACuB,IAAI,CAAC,IAAI,CAACb,OAAO,GAAGe,MAAM,CAACE,KAAK,CAAC;QACxD,CAAC,MACI;UACD,IAAI,CAAC5B,OAAO,CAACwB,IAAI,CAACE,MAAM,CAACE,KAAK,CAAC;UAC/B,MAAMF,MAAM,CAACE,KAAK;QACtB;MACJ,CAAC,MACI;QACD,IAAI,CAACkB,WAAW,CAACtB,IAAI,CAAC,IAAI,CAAClC,KAAK,GAAGoC,MAAM,CAACpC,KAAK,CAAC;QAChD,OAAOoC,MAAM,CAACpC,KAAK;MACvB;IACJ,CAAC,CAAC;EACN;AACJ;AACA4E,UAAU,CAAClF,IAAI,YAAAoF,mBAAAlF,CAAA;EAAA,YAAAA,CAAA,IAAyFgF,UAAU,EA3Pf5I,EAAE,CAAAgF,iBAAA,CA2P+BxD,eAAe;AAAA,CAA4C;AAC/LoH,UAAU,CAAC3D,IAAI,kBA5PoFjF,EAAE,CAAAkF,iBAAA;EAAAb,IAAA,EA4PTuE,UAAU;EAAAzD,SAAA;EAAAC,MAAA;IAAAxD,OAAA;IAAAoC,KAAA;EAAA;EAAAsB,OAAA;IAAAkC,WAAA;EAAA;EAAAX,QAAA;EAAAC,QAAA,GA5PH9G,EAAE,CAAA+G,0BAAA;EAAAxB,KAAA;EAAAC,IAAA;EAAAwB,MAAA;EAAAvB,QAAA,WAAAsD,oBAAAlI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFb,EAAE,CAAAkH,UAAA,IAAA7F,kCAAA,yBA+PjF,CAAC;IAAA;IAAA,IAAAR,EAAA;MA/P8Eb,EAAE,CAAAmH,UAAA,UAAArG,GAAA,CAAAU,eAAA,CAAAe,cA6P5C,CAAC;IAAA;EAAA;EAAA6E,YAAA,GAGhB3G,EAAE,CAAC4G,IAAI;EAAA1B,aAAA;AAAA,EAAoE;AACrH;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAjQmGnE,EAAE,CAAAoE,iBAAA,CAiQTwE,UAAU,EAAc,CAAC;IACzGvE,IAAI,EAAEhE,SAAS;IACfiE,IAAI,EAAE,CAAC;MACCsB,QAAQ,EAAE,aAAa;MACvBH,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,GAAG;MACiBoB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExC,IAAI,EAAE7C;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEI,OAAO,EAAE,CAAC;MAC7FyC,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEyD,KAAK,EAAE,CAAC;MACRK,IAAI,EAAE9D;IACV,CAAC,CAAC;IAAEiH,WAAW,EAAE,CAAC;MACdnD,IAAI,EAAE/D;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0I,YAAY,GAAG,CACjBvE,eAAe,EACfoB,YAAY,EACZyB,UAAU,EACVsB,UAAU,CACb;AACD,MAAMK,YAAY,CAAC;EACf,OAAOC,OAAOA,CAACC,cAAc,EAAEvH,OAAO,EAAE;IACpC,OAAO;MACHwH,QAAQ,EAAEH,YAAY;MACtBI,SAAS,EAAE,CACP7H,eAAe,EACf;QACI8H,OAAO,EAAEhI,sBAAsB;QAC/BiI,QAAQ,EAAEJ;MACd,CAAC,EACD;QACIG,OAAO,EAAE/H,cAAc;QACvBgI,QAAQ,EAAE3H;MACd,CAAC;IAET,CAAC;EACL;AACJ;AACAqH,YAAY,CAACvF,IAAI,YAAA8F,qBAAA5F,CAAA;EAAA,YAAAA,CAAA,IAAyFqF,YAAY;AAAA,CAAkD;AACxKA,YAAY,CAACQ,IAAI,kBA7SkFzJ,EAAE,CAAA0J,gBAAA;EAAArF,IAAA,EA6SM4E;AAAY,EAMjG;AACtBA,YAAY,CAACU,IAAI,kBApTkF3J,EAAE,CAAA4J,gBAAA;EAAAC,OAAA,GAoT8B,CACvHlJ,YAAY,CACf;AAAA,EAAI;AACb;EAAA,QAAAwD,SAAA,oBAAAA,SAAA,KAvTmGnE,EAAE,CAAAoE,iBAAA,CAuTT6E,YAAY,EAAc,CAAC;IAC3G5E,IAAI,EAAE7D,QAAQ;IACd8D,IAAI,EAAE,CAAC;MACCuF,OAAO,EAAE,CACLlJ,YAAY,CACf;MACDqI,YAAY;MACZ;MACAc,OAAO,EAAE,CAAC,GAAGd,YAAY;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMe,MAAM,GAAGd,YAAY;;AAE3B;AACA;AACA;;AAEA,SAASc,MAAM,EAAExI,cAAc,EAAED,sBAAsB,EAAEsH,UAAU,EAAEtB,UAAU,EAAE7C,eAAe,EAAEwE,YAAY,EAAEzH,eAAe,EAAEqE,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
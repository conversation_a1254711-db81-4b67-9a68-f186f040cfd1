{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { CoreTouchspinComponent } from '@core/components/core-touchspin/core-touchspin.component';\nimport { CoreCommonModule } from '@core/common.module';\nimport * as i0 from \"@angular/core\";\nexport class CoreTouchspinModule {\n  static #_ = this.ɵfac = function CoreTouchspinModule_Factory(t) {\n    return new (t || CoreTouchspinModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreTouchspinModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, CoreCommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreTouchspinModule, {\n    declarations: [CoreTouchspinComponent],\n    imports: [CommonModule, FormsModule, CoreCommonModule],\n    exports: [CoreTouchspinComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,sBAAsB,QAAQ,0DAA0D;AACjG,SAASC,gBAAgB,QAAQ,qBAAqB;;AAOtD,OAAM,MAAOC,mBAAmB;EAAA,QAAAC,CAAA;qBAAnBD,mBAAmB;EAAA;EAAA,QAAAE,EAAA;UAAnBF;EAAmB;EAAA,QAAAG,EAAA;cAHpBP,YAAY,EAAEC,WAAW,EAAEE,gBAAgB;EAAA;;;2EAG1CC,mBAAmB;IAAAI,YAAA,GAJfN,sBAAsB;IAAAO,OAAA,GAC3BT,YAAY,EAAEC,WAAW,EAAEE,gBAAgB;IAAAO,OAAA,GAC3CR,sBAAsB;EAAA;AAAA", "names": ["CommonModule", "FormsModule", "CoreTouchspinComponent", "CoreCommonModule", "CoreTouchspinModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-touchspin\\core-touchspin.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { CoreTouchspinComponent } from '@core/components/core-touchspin/core-touchspin.component';\r\nimport { CoreCommonModule } from '@core/common.module';\r\n\r\n@NgModule({\r\n  declarations: [CoreTouchspinComponent],\r\n  imports: [CommonModule, FormsModule, CoreCommonModule],\r\n  exports: [CoreTouchspinComponent]\r\n})\r\nexport class CoreTouchspinModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
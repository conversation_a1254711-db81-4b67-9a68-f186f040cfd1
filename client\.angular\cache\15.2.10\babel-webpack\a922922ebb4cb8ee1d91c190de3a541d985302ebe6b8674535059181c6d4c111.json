{"ast": null, "code": "export function isFunction(x) {\n  return typeof x === 'function';\n}", "map": {"version": 3, "names": ["isFunction", "x"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isFunction.js"], "sourcesContent": ["export function isFunction(x) {\n    return typeof x === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAACC,CAAC,EAAE;EAC1B,OAAO,OAAOA,CAAC,KAAK,UAAU;AAClC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport toNumber from './toNumber.js';\n\n/**\n * Clamps `number` within the inclusive `lower` and `upper` bounds.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Number\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n * @example\n *\n * _.clamp(-10, -5, 5);\n * // => -5\n *\n * _.clamp(10, -5, 5);\n * // => 5\n */\nfunction clamp(number, lower, upper) {\n  if (upper === undefined) {\n    upper = lower;\n    lower = undefined;\n  }\n  if (upper !== undefined) {\n    upper = toNumber(upper);\n    upper = upper === upper ? upper : 0;\n  }\n  if (lower !== undefined) {\n    lower = toNumber(lower);\n    lower = lower === lower ? lower : 0;\n  }\n  return baseClamp(toNumber(number), lower, upper);\n}\nexport default clamp;", "map": {"version": 3, "names": ["baseClamp", "toNumber", "clamp", "number", "lower", "upper", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/clamp.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport toNumber from './toNumber.js';\n\n/**\n * Clamps `number` within the inclusive `lower` and `upper` bounds.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Number\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n * @example\n *\n * _.clamp(-10, -5, 5);\n * // => -5\n *\n * _.clamp(10, -5, 5);\n * // => 5\n */\nfunction clamp(number, lower, upper) {\n  if (upper === undefined) {\n    upper = lower;\n    lower = undefined;\n  }\n  if (upper !== undefined) {\n    upper = toNumber(upper);\n    upper = upper === upper ? upper : 0;\n  }\n  if (lower !== undefined) {\n    lower = toNumber(lower);\n    lower = lower === lower ? lower : 0;\n  }\n  return baseClamp(toNumber(number), lower, upper);\n}\n\nexport default clamp;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACvBD,KAAK,GAAGD,KAAK;IACbA,KAAK,GAAGE,SAAS;EACnB;EACA,IAAID,KAAK,KAAKC,SAAS,EAAE;IACvBD,KAAK,GAAGJ,QAAQ,CAACI,KAAK,CAAC;IACvBA,KAAK,GAAGA,KAAK,KAAKA,KAAK,GAAGA,KAAK,GAAG,CAAC;EACrC;EACA,IAAID,KAAK,KAAKE,SAAS,EAAE;IACvBF,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC;IACvBA,KAAK,GAAGA,KAAK,KAAKA,KAAK,GAAGA,KAAK,GAAG,CAAC;EACrC;EACA,OAAOJ,SAAS,CAACC,QAAQ,CAACE,MAAM,CAAC,EAAEC,KAAK,EAAEC,KAAK,CAAC;AAClD;AAEA,eAAeH,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
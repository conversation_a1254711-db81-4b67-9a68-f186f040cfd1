{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FlexLayoutModule } from '@angular/flex-layout';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CoreDirectivesModule } from '@core/directives/directives';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nexport class CoreCommonModule {\n  static #_ = this.ɵfac = function CoreCommonModule_Factory(t) {\n    return new (t || CoreCommonModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreCommonModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FlexLayoutModule, FormsModule, ReactiveFormsModule, CoreDirectivesModule, CorePipesModule, CommonModule, FlexLayoutModule, FormsModule, ReactiveFormsModule, CoreDirectivesModule, CorePipesModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreCommonModule, {\n    imports: [CommonModule, FlexLayoutModule, FormsModule, ReactiveFormsModule, CoreDirectivesModule, CorePipesModule],\n    exports: [CommonModule, FlexLayoutModule, FormsModule, ReactiveFormsModule, CoreDirectivesModule, CorePipesModule]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,eAAe,QAAQ,0BAA0B;;AAM1D,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA;UAAhBF;EAAgB;EAAA,QAAAG,EAAA;cAHjBT,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,EACvGL,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe;EAAA;;;2EAEtGC,gBAAgB;IAAAI,OAAA,GAHjBV,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe;IAAAM,OAAA,GACvGX,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe;EAAA;AAAA", "names": ["CommonModule", "FlexLayoutModule", "FormsModule", "ReactiveFormsModule", "CoreDirectivesModule", "CorePipesModule", "CoreCommonModule", "_", "_2", "_3", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\common.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FlexLayoutModule } from '@angular/flex-layout';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { CoreDirectivesModule } from '@core/directives/directives';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\n\r\n@NgModule({\r\n  imports: [CommonModule, FlexLayoutModule, FormsModule, ReactiveFormsModule, CoreDirectivesModule, CorePipesModule],\r\n  exports: [CommonModule, FlexLayoutModule, FormsModule, ReactiveFormsModule, CoreDirectivesModule, CorePipesModule]\r\n})\r\nexport class CoreCommonModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
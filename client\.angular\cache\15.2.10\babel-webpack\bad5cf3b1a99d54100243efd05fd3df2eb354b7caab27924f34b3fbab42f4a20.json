{"ast": null, "code": "import { isArray } from './util/isArray';\nimport { isObject } from './util/isObject';\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nexport class Subscription {\n  constructor(unsubscribe) {\n    this.closed = false;\n    this._parentOrParents = null;\n    this._subscriptions = null;\n    if (unsubscribe) {\n      this._unsubscribe = unsubscribe;\n    }\n  }\n  unsubscribe() {\n    let errors;\n    if (this.closed) {\n      return;\n    }\n    let {\n      _parentOrParents,\n      _unsubscribe,\n      _subscriptions\n    } = this;\n    this.closed = true;\n    this._parentOrParents = null;\n    this._subscriptions = null;\n    if (_parentOrParents instanceof Subscription) {\n      _parentOrParents.remove(this);\n    } else if (_parentOrParents !== null) {\n      for (let index = 0; index < _parentOrParents.length; ++index) {\n        const parent = _parentOrParents[index];\n        parent.remove(this);\n      }\n    }\n    if (isFunction(_unsubscribe)) {\n      try {\n        _unsubscribe.call(this);\n      } catch (e) {\n        errors = e instanceof UnsubscriptionError ? flattenUnsubscriptionErrors(e.errors) : [e];\n      }\n    }\n    if (isArray(_subscriptions)) {\n      let index = -1;\n      let len = _subscriptions.length;\n      while (++index < len) {\n        const sub = _subscriptions[index];\n        if (isObject(sub)) {\n          try {\n            sub.unsubscribe();\n          } catch (e) {\n            errors = errors || [];\n            if (e instanceof UnsubscriptionError) {\n              errors = errors.concat(flattenUnsubscriptionErrors(e.errors));\n            } else {\n              errors.push(e);\n            }\n          }\n        }\n      }\n    }\n    if (errors) {\n      throw new UnsubscriptionError(errors);\n    }\n  }\n  add(teardown) {\n    let subscription = teardown;\n    if (!teardown) {\n      return Subscription.EMPTY;\n    }\n    switch (typeof teardown) {\n      case 'function':\n        subscription = new Subscription(teardown);\n      case 'object':\n        if (subscription === this || subscription.closed || typeof subscription.unsubscribe !== 'function') {\n          return subscription;\n        } else if (this.closed) {\n          subscription.unsubscribe();\n          return subscription;\n        } else if (!(subscription instanceof Subscription)) {\n          const tmp = subscription;\n          subscription = new Subscription();\n          subscription._subscriptions = [tmp];\n        }\n        break;\n      default:\n        {\n          throw new Error('unrecognized teardown ' + teardown + ' added to Subscription.');\n        }\n    }\n    let {\n      _parentOrParents\n    } = subscription;\n    if (_parentOrParents === null) {\n      subscription._parentOrParents = this;\n    } else if (_parentOrParents instanceof Subscription) {\n      if (_parentOrParents === this) {\n        return subscription;\n      }\n      subscription._parentOrParents = [_parentOrParents, this];\n    } else if (_parentOrParents.indexOf(this) === -1) {\n      _parentOrParents.push(this);\n    } else {\n      return subscription;\n    }\n    const subscriptions = this._subscriptions;\n    if (subscriptions === null) {\n      this._subscriptions = [subscription];\n    } else {\n      subscriptions.push(subscription);\n    }\n    return subscription;\n  }\n  remove(subscription) {\n    const subscriptions = this._subscriptions;\n    if (subscriptions) {\n      const subscriptionIndex = subscriptions.indexOf(subscription);\n      if (subscriptionIndex !== -1) {\n        subscriptions.splice(subscriptionIndex, 1);\n      }\n    }\n  }\n}\nSubscription.EMPTY = function (empty) {\n  empty.closed = true;\n  return empty;\n}(new Subscription());\nfunction flattenUnsubscriptionErrors(errors) {\n  return errors.reduce((errs, err) => errs.concat(err instanceof UnsubscriptionError ? err.errors : err), []);\n}", "map": {"version": 3, "names": ["isArray", "isObject", "isFunction", "UnsubscriptionError", "Subscription", "constructor", "unsubscribe", "closed", "_parentOrParents", "_subscriptions", "_unsubscribe", "errors", "remove", "index", "length", "parent", "call", "e", "flattenUnsubscriptionErrors", "len", "sub", "concat", "push", "add", "teardown", "subscription", "EMPTY", "tmp", "Error", "indexOf", "subscriptions", "subscriptionIndex", "splice", "empty", "reduce", "errs", "err"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/Subscription.js"], "sourcesContent": ["import { isArray } from './util/isArray';\nimport { isObject } from './util/isObject';\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nexport class Subscription {\n    constructor(unsubscribe) {\n        this.closed = false;\n        this._parentOrParents = null;\n        this._subscriptions = null;\n        if (unsubscribe) {\n            this._unsubscribe = unsubscribe;\n        }\n    }\n    unsubscribe() {\n        let errors;\n        if (this.closed) {\n            return;\n        }\n        let { _parentOrParents, _unsubscribe, _subscriptions } = this;\n        this.closed = true;\n        this._parentOrParents = null;\n        this._subscriptions = null;\n        if (_parentOrParents instanceof Subscription) {\n            _parentOrParents.remove(this);\n        }\n        else if (_parentOrParents !== null) {\n            for (let index = 0; index < _parentOrParents.length; ++index) {\n                const parent = _parentOrParents[index];\n                parent.remove(this);\n            }\n        }\n        if (isFunction(_unsubscribe)) {\n            try {\n                _unsubscribe.call(this);\n            }\n            catch (e) {\n                errors = e instanceof UnsubscriptionError ? flattenUnsubscriptionErrors(e.errors) : [e];\n            }\n        }\n        if (isArray(_subscriptions)) {\n            let index = -1;\n            let len = _subscriptions.length;\n            while (++index < len) {\n                const sub = _subscriptions[index];\n                if (isObject(sub)) {\n                    try {\n                        sub.unsubscribe();\n                    }\n                    catch (e) {\n                        errors = errors || [];\n                        if (e instanceof UnsubscriptionError) {\n                            errors = errors.concat(flattenUnsubscriptionErrors(e.errors));\n                        }\n                        else {\n                            errors.push(e);\n                        }\n                    }\n                }\n            }\n        }\n        if (errors) {\n            throw new UnsubscriptionError(errors);\n        }\n    }\n    add(teardown) {\n        let subscription = teardown;\n        if (!teardown) {\n            return Subscription.EMPTY;\n        }\n        switch (typeof teardown) {\n            case 'function':\n                subscription = new Subscription(teardown);\n            case 'object':\n                if (subscription === this || subscription.closed || typeof subscription.unsubscribe !== 'function') {\n                    return subscription;\n                }\n                else if (this.closed) {\n                    subscription.unsubscribe();\n                    return subscription;\n                }\n                else if (!(subscription instanceof Subscription)) {\n                    const tmp = subscription;\n                    subscription = new Subscription();\n                    subscription._subscriptions = [tmp];\n                }\n                break;\n            default: {\n                throw new Error('unrecognized teardown ' + teardown + ' added to Subscription.');\n            }\n        }\n        let { _parentOrParents } = subscription;\n        if (_parentOrParents === null) {\n            subscription._parentOrParents = this;\n        }\n        else if (_parentOrParents instanceof Subscription) {\n            if (_parentOrParents === this) {\n                return subscription;\n            }\n            subscription._parentOrParents = [_parentOrParents, this];\n        }\n        else if (_parentOrParents.indexOf(this) === -1) {\n            _parentOrParents.push(this);\n        }\n        else {\n            return subscription;\n        }\n        const subscriptions = this._subscriptions;\n        if (subscriptions === null) {\n            this._subscriptions = [subscription];\n        }\n        else {\n            subscriptions.push(subscription);\n        }\n        return subscription;\n    }\n    remove(subscription) {\n        const subscriptions = this._subscriptions;\n        if (subscriptions) {\n            const subscriptionIndex = subscriptions.indexOf(subscription);\n            if (subscriptionIndex !== -1) {\n                subscriptions.splice(subscriptionIndex, 1);\n            }\n        }\n    }\n}\nSubscription.EMPTY = (function (empty) {\n    empty.closed = true;\n    return empty;\n}(new Subscription()));\nfunction flattenUnsubscriptionErrors(errors) {\n    return errors.reduce((errs, err) => errs.concat((err instanceof UnsubscriptionError) ? err.errors : err), []);\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAIH,WAAW,EAAE;MACb,IAAI,CAACI,YAAY,GAAGJ,WAAW;IACnC;EACJ;EACAA,WAAWA,CAAA,EAAG;IACV,IAAIK,MAAM;IACV,IAAI,IAAI,CAACJ,MAAM,EAAE;MACb;IACJ;IACA,IAAI;MAAEC,gBAAgB;MAAEE,YAAY;MAAED;IAAe,CAAC,GAAG,IAAI;IAC7D,IAAI,CAACF,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAID,gBAAgB,YAAYJ,YAAY,EAAE;MAC1CI,gBAAgB,CAACI,MAAM,CAAC,IAAI,CAAC;IACjC,CAAC,MACI,IAAIJ,gBAAgB,KAAK,IAAI,EAAE;MAChC,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,gBAAgB,CAACM,MAAM,EAAE,EAAED,KAAK,EAAE;QAC1D,MAAME,MAAM,GAAGP,gBAAgB,CAACK,KAAK,CAAC;QACtCE,MAAM,CAACH,MAAM,CAAC,IAAI,CAAC;MACvB;IACJ;IACA,IAAIV,UAAU,CAACQ,YAAY,CAAC,EAAE;MAC1B,IAAI;QACAA,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC;MAC3B,CAAC,CACD,OAAOC,CAAC,EAAE;QACNN,MAAM,GAAGM,CAAC,YAAYd,mBAAmB,GAAGe,2BAA2B,CAACD,CAAC,CAACN,MAAM,CAAC,GAAG,CAACM,CAAC,CAAC;MAC3F;IACJ;IACA,IAAIjB,OAAO,CAACS,cAAc,CAAC,EAAE;MACzB,IAAII,KAAK,GAAG,CAAC,CAAC;MACd,IAAIM,GAAG,GAAGV,cAAc,CAACK,MAAM;MAC/B,OAAO,EAAED,KAAK,GAAGM,GAAG,EAAE;QAClB,MAAMC,GAAG,GAAGX,cAAc,CAACI,KAAK,CAAC;QACjC,IAAIZ,QAAQ,CAACmB,GAAG,CAAC,EAAE;UACf,IAAI;YACAA,GAAG,CAACd,WAAW,CAAC,CAAC;UACrB,CAAC,CACD,OAAOW,CAAC,EAAE;YACNN,MAAM,GAAGA,MAAM,IAAI,EAAE;YACrB,IAAIM,CAAC,YAAYd,mBAAmB,EAAE;cAClCQ,MAAM,GAAGA,MAAM,CAACU,MAAM,CAACH,2BAA2B,CAACD,CAAC,CAACN,MAAM,CAAC,CAAC;YACjE,CAAC,MACI;cACDA,MAAM,CAACW,IAAI,CAACL,CAAC,CAAC;YAClB;UACJ;QACJ;MACJ;IACJ;IACA,IAAIN,MAAM,EAAE;MACR,MAAM,IAAIR,mBAAmB,CAACQ,MAAM,CAAC;IACzC;EACJ;EACAY,GAAGA,CAACC,QAAQ,EAAE;IACV,IAAIC,YAAY,GAAGD,QAAQ;IAC3B,IAAI,CAACA,QAAQ,EAAE;MACX,OAAOpB,YAAY,CAACsB,KAAK;IAC7B;IACA,QAAQ,OAAOF,QAAQ;MACnB,KAAK,UAAU;QACXC,YAAY,GAAG,IAAIrB,YAAY,CAACoB,QAAQ,CAAC;MAC7C,KAAK,QAAQ;QACT,IAAIC,YAAY,KAAK,IAAI,IAAIA,YAAY,CAAClB,MAAM,IAAI,OAAOkB,YAAY,CAACnB,WAAW,KAAK,UAAU,EAAE;UAChG,OAAOmB,YAAY;QACvB,CAAC,MACI,IAAI,IAAI,CAAClB,MAAM,EAAE;UAClBkB,YAAY,CAACnB,WAAW,CAAC,CAAC;UAC1B,OAAOmB,YAAY;QACvB,CAAC,MACI,IAAI,EAAEA,YAAY,YAAYrB,YAAY,CAAC,EAAE;UAC9C,MAAMuB,GAAG,GAAGF,YAAY;UACxBA,YAAY,GAAG,IAAIrB,YAAY,CAAC,CAAC;UACjCqB,YAAY,CAAChB,cAAc,GAAG,CAACkB,GAAG,CAAC;QACvC;QACA;MACJ;QAAS;UACL,MAAM,IAAIC,KAAK,CAAC,wBAAwB,GAAGJ,QAAQ,GAAG,yBAAyB,CAAC;QACpF;IACJ;IACA,IAAI;MAAEhB;IAAiB,CAAC,GAAGiB,YAAY;IACvC,IAAIjB,gBAAgB,KAAK,IAAI,EAAE;MAC3BiB,YAAY,CAACjB,gBAAgB,GAAG,IAAI;IACxC,CAAC,MACI,IAAIA,gBAAgB,YAAYJ,YAAY,EAAE;MAC/C,IAAII,gBAAgB,KAAK,IAAI,EAAE;QAC3B,OAAOiB,YAAY;MACvB;MACAA,YAAY,CAACjB,gBAAgB,GAAG,CAACA,gBAAgB,EAAE,IAAI,CAAC;IAC5D,CAAC,MACI,IAAIA,gBAAgB,CAACqB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5CrB,gBAAgB,CAACc,IAAI,CAAC,IAAI,CAAC;IAC/B,CAAC,MACI;MACD,OAAOG,YAAY;IACvB;IACA,MAAMK,aAAa,GAAG,IAAI,CAACrB,cAAc;IACzC,IAAIqB,aAAa,KAAK,IAAI,EAAE;MACxB,IAAI,CAACrB,cAAc,GAAG,CAACgB,YAAY,CAAC;IACxC,CAAC,MACI;MACDK,aAAa,CAACR,IAAI,CAACG,YAAY,CAAC;IACpC;IACA,OAAOA,YAAY;EACvB;EACAb,MAAMA,CAACa,YAAY,EAAE;IACjB,MAAMK,aAAa,GAAG,IAAI,CAACrB,cAAc;IACzC,IAAIqB,aAAa,EAAE;MACf,MAAMC,iBAAiB,GAAGD,aAAa,CAACD,OAAO,CAACJ,YAAY,CAAC;MAC7D,IAAIM,iBAAiB,KAAK,CAAC,CAAC,EAAE;QAC1BD,aAAa,CAACE,MAAM,CAACD,iBAAiB,EAAE,CAAC,CAAC;MAC9C;IACJ;EACJ;AACJ;AACA3B,YAAY,CAACsB,KAAK,GAAI,UAAUO,KAAK,EAAE;EACnCA,KAAK,CAAC1B,MAAM,GAAG,IAAI;EACnB,OAAO0B,KAAK;AAChB,CAAC,CAAC,IAAI7B,YAAY,CAAC,CAAC,CAAE;AACtB,SAASc,2BAA2BA,CAACP,MAAM,EAAE;EACzC,OAAOA,MAAM,CAACuB,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAKD,IAAI,CAACd,MAAM,CAAEe,GAAG,YAAYjC,mBAAmB,GAAIiC,GAAG,CAACzB,MAAM,GAAGyB,GAAG,CAAC,EAAE,EAAE,CAAC;AACjH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
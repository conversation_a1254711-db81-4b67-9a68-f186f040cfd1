{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/config.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"app/services/commons.service\";\nimport * as i4 from \"app/services/livekit.service\";\nimport * as i5 from \"app/components/overlays/overlays.service\";\nimport * as i6 from \"../../../components/overlays/screen-overlays/screen-overlays.component\";\nimport * as i7 from \"@angular/common\";\nfunction EgreessComponent_screen_overlays_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"screen-overlays\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"resizeEvent\", ctx_r0.resizeEvent);\n  }\n}\nexport class EgreessComponent {\n  constructor(_coreConfigService, route, commonService, livekitService, overlaysService) {\n    this._coreConfigService = _coreConfigService;\n    this.route = route;\n    this.commonService = commonService;\n    this.livekitService = livekitService;\n    this.overlaysService = overlaysService;\n    this.token = '';\n    this.resizeEvent = new EventEmitter();\n    _coreConfigService.visible_layout = false;\n    this._coreConfigService.setConfig({\n      layout: {\n        navbar: {\n          hidden: true\n        },\n        menu: {\n          hidden: true\n        },\n        footer: {\n          hidden: true\n        },\n        enableLocalStorage: false\n      }\n    });\n    this.token = this.route.snapshot.params['token'];\n  }\n  ngOnInit() {\n    document.body.style.backgroundColor = 'black';\n  }\n  join(token) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('join room', token);\n      yield _this.livekitService.joinRoom(token);\n    })();\n  }\n  ngAfterViewInit() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // set body background color to black\n      yield _this2.join(_this2.token);\n      // await this.livekitService.changeMediaDevice();\n      _this2.setUpscreen();\n      _this2.fullscreen();\n    })();\n  }\n  setUpscreen() {\n    // set background color to black\n    let video_container = document.querySelector('.video-container');\n    video_container.style.backgroundColor = 'black';\n  }\n  fullscreen() {\n    this.commonService.toggleFullScreenCss('video-container');\n    this.resizeEvent.emit();\n  }\n  ngOnDestroy() {}\n  static #_ = this.ɵfac = function EgreessComponent_Factory(t) {\n    return new (t || EgreessComponent)(i0.ɵɵdirectiveInject(i1.CoreConfigService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.CommonsService), i0.ɵɵdirectiveInject(i4.LivekitService), i0.ɵɵdirectiveInject(i5.OverlaysService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EgreessComponent,\n    selectors: [[\"app-egreess\"]],\n    decls: 4,\n    vars: 1,\n    consts: [[1, \"watch-container\"], [\"id\", \"video-container\", 1, \"video-container\"], [3, \"resizeEvent\", 4, \"ngIf\"], [\"id\", \"livekit-subscriber-video\"], [3, \"resizeEvent\"]],\n    template: function EgreessComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, EgreessComponent_screen_overlays_2_Template, 1, 1, \"screen-overlays\", 2);\n        i0.ɵɵelement(3, \"div\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.livekitService.is_connected);\n      }\n    },\n    dependencies: [i6.ScreenOverlaysComponent, i7.NgIf],\n    styles: [\".setup-broadcast-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  top: 0;\\n  left: 50%;\\n  transform: translate(-50%, 0);\\n  width: 100%;\\n}\\n.setup-broadcast-container[_ngcontent-%COMP%]   .video-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  width: calc(45vw - 100px);\\n  height: calc((45vw - 100px) / 1.7777777778);\\n}\\n@media (max-width: 768px) {\\n  .setup-broadcast-container[_ngcontent-%COMP%]   .video-container[_ngcontent-%COMP%] {\\n    width: 50 !important;\\n    height: 28.125vw !important;\\n  }\\n}\\n.setup-broadcast-container[_ngcontent-%COMP%]   .video-container[_ngcontent-%COMP%]   video[_ngcontent-%COMP%] {\\n  min-width: 100%;\\n  min-height: 100%;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n.setup-broadcast-container[_ngcontent-%COMP%]   .video-container[_ngcontent-%COMP%]   iframe[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.setup-broadcast-container[_ngcontent-%COMP%]   .video-container[_ngcontent-%COMP%]   .video-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 10px;\\n  background: rgba(0, 0, 0, 0.1);\\n  transition: all 0.5s;\\n  opacity: 0;\\n}\\n.setup-broadcast-container[_ngcontent-%COMP%]   .video-container[_ngcontent-%COMP%]:hover   .video-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAAA,SAAoBA,YAAY,QAAQ,eAAe;;;;;;;;;;;ICE/CC,EAAA,CAAAC,SAAA,yBAAmG;;;;IAAlFD,EAAA,CAAAE,UAAA,gBAAAC,MAAA,CAAAC,WAAA,CAA2B;;;ADUpD,OAAM,MAAOC,gBAAgB;EAK3BC,YACSC,kBAAqC,EACrCC,KAAqB,EACrBC,aAA6B,EAC7BC,cAA8B,EAC9BC,eAAgC;IAJhC,KAAAJ,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IATxB,KAAAC,KAAK,GAAG,EAAE;IAEV,KAAAR,WAAW,GAAsB,IAAIL,YAAY,EAAE;IASjDQ,kBAAkB,CAACM,cAAc,GAAG,KAAK;IACzC,IAAI,CAACN,kBAAkB,CAACO,SAAS,CAAC;MAChCC,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,MAAM,EAAE;SACT;QACDC,IAAI,EAAE;UACJD,MAAM,EAAE;SACT;QACDE,MAAM,EAAE;UACNF,MAAM,EAAE;SACT;QACDG,kBAAkB,EAAE;;KAEvB,CAAC;IAEF,IAAI,CAACR,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACa,QAAQ,CAACC,MAAM,CAAC,OAAO,CAAC;EAClD;EAEAC,QAAQA,CAAA;IACNC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,eAAe,GAAG,OAAO;EAC/C;EAEMC,IAAIA,CAAChB,KAAa;IAAA,IAAAiB,KAAA;IAAA,OAAAC,iBAAA;MACtBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEpB,KAAK,CAAC;MAC/B,MAAMiB,KAAI,CAACnB,cAAc,CAACuB,QAAQ,CAACrB,KAAK,CAAC;IAAC;EAC5C;EAEMsB,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MACnB;MACA,MAAMK,MAAI,CAACP,IAAI,CAACO,MAAI,CAACvB,KAAK,CAAC;MAC3B;MACAuB,MAAI,CAACC,WAAW,EAAE;MAClBD,MAAI,CAACE,UAAU,EAAE;IAAC;EACpB;EAEAD,WAAWA,CAAA;IACT;IACA,IAAIE,eAAe,GAAGd,QAAQ,CAACe,aAAa,CAC1C,kBAAkB,CACJ;IAChBD,eAAe,CAACZ,KAAK,CAACC,eAAe,GAAG,OAAO;EACjD;EAEAU,UAAUA,CAAA;IACR,IAAI,CAAC5B,aAAa,CAAC+B,mBAAmB,CAAC,iBAAiB,CAAC;IACzD,IAAI,CAACpC,WAAW,CAACqC,IAAI,EAAE;EACzB;EAEAC,WAAWA,CAAA,GAAI;EAAC,QAAAC,CAAA;qBA7DLtC,gBAAgB,EAAAL,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhD,EAAA,CAAA4C,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlD,EAAA,CAAA4C,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAApD,EAAA,CAAA4C,iBAAA,CAAAS,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA;UAAhBlD,gBAAgB;IAAAmD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ7B9D,EAAA,CAAAgE,cAAA,aAA8B;QAEtBhE,EAAA,CAAAiE,UAAA,IAAAC,2CAAA,6BAAmG;QACnGlE,EAAA,CAAAC,SAAA,aACM;QACVD,EAAA,CAAAmE,YAAA,EAAM;;;QAH4CnE,EAAA,CAAAoE,SAAA,GAAiC;QAAjCpE,EAAA,CAAAE,UAAA,SAAA6D,GAAA,CAAArD,cAAA,CAAA2D,YAAA,CAAiC", "names": ["EventEmitter", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "resizeEvent", "EgreessComponent", "constructor", "_coreConfigService", "route", "commonService", "livekitService", "overlaysService", "token", "visible_layout", "setConfig", "layout", "navbar", "hidden", "menu", "footer", "enableLocalStorage", "snapshot", "params", "ngOnInit", "document", "body", "style", "backgroundColor", "join", "_this", "_asyncToGenerator", "console", "log", "joinRoom", "ngAfterViewInit", "_this2", "setUpscreen", "fullscreen", "video_container", "querySelector", "toggleFullScreenCss", "emit", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "CoreConfigService", "i2", "ActivatedRoute", "i3", "CommonsService", "i4", "LivekitService", "i5", "OverlaysService", "_2", "selectors", "decls", "vars", "consts", "template", "EgreessComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "EgreessComponent_screen_overlays_2_Template", "ɵɵelementEnd", "ɵɵadvance", "is_connected"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\egreess\\egreess.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\egreess\\egreess.component.html"], "sourcesContent": ["import { Component, EventEmitter } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { OverlaysService } from 'app/components/overlays/overlays.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LivekitService, RoomMetadata } from 'app/services/livekit.service';\r\n\r\n@Component({\r\n  selector: 'app-egreess',\r\n  templateUrl: './egreess.component.html',\r\n  styleUrls: ['./egreess.component.scss'],\r\n})\r\nexport class EgreessComponent {\r\n  token = '';\r\n  interval: any;\r\n  resizeEvent: EventEmitter<any> = new EventEmitter();\r\n  currentRoomMetadata: RoomMetadata;\r\n  constructor(\r\n    public _coreConfigService: CoreConfigService,\r\n    public route: ActivatedRoute,\r\n    public commonService: CommonsService,\r\n    public livekitService: LivekitService,\r\n    public overlaysService: OverlaysService\r\n  ) {\r\n    _coreConfigService.visible_layout = false;\r\n    this._coreConfigService.setConfig({\r\n      layout: {\r\n        navbar: {\r\n          hidden: true,\r\n        },\r\n        menu: {\r\n          hidden: true,\r\n        },\r\n        footer: {\r\n          hidden: true,\r\n        },\r\n        enableLocalStorage: false,\r\n      },\r\n    });\r\n\r\n    this.token = this.route.snapshot.params['token'];\r\n  }\r\n\r\n  ngOnInit() {\r\n    document.body.style.backgroundColor = 'black';\r\n  }\r\n\r\n  async join(token: string) {\r\n    console.log('join room', token);\r\n    await this.livekitService.joinRoom(token);\r\n  }\r\n\r\n  async ngAfterViewInit() {\r\n    // set body background color to black\r\n    await this.join(this.token);\r\n    // await this.livekitService.changeMediaDevice();\r\n    this.setUpscreen();\r\n    this.fullscreen();\r\n  }\r\n\r\n  setUpscreen() {\r\n    // set background color to black\r\n    let video_container = document.querySelector(\r\n      '.video-container'\r\n    ) as HTMLElement;\r\n    video_container.style.backgroundColor = 'black';\r\n  }\r\n\r\n  fullscreen() {\r\n    this.commonService.toggleFullScreenCss('video-container');\r\n    this.resizeEvent.emit();\r\n  }\r\n\r\n  ngOnDestroy() {}\r\n}\r\n", "<div class=\"watch-container \">\r\n    <div class=\"video-container\" id=\"video-container\">\r\n        <screen-overlays [resizeEvent]=\"resizeEvent\" *ngIf=\"livekitService.is_connected\"></screen-overlays>\r\n        <div id=\"livekit-subscriber-video\">\r\n        </div>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Directive, Input, NgModule } from '@angular/core';\nimport * as i2$1 from '@alyle/ui';\nimport { keyframesUniqueId, mixinStyleUpdater, mixinBg, mixinColor, mixinRaised, mixinOutlined, mixinElevation, mixinShadowColor, StyleRenderer, LyCommonModule } from '@alyle/ui';\nimport { share, map, take } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport * as i3 from '@angular/cdk/platform';\nconst STYLE_PRIORITY$1 = -2;\n/** The following styles will never be updated */\nconst styles = {\n  svg: {\n    width: 'inherit',\n    height: 'inherit',\n    fill: 'currentColor'\n  }\n};\nclass LyIconService {\n  constructor(http, _sanitizer, _document, theme) {\n    this.http = http;\n    this._sanitizer = _sanitizer;\n    this._document = _document;\n    this.theme = theme;\n    this._defaultClass = 'material-icons';\n    this.svgMap = new Map();\n    this._fontClasses = new Map();\n    /**\n     * Styles\n     * @docs-private\n     */\n    this.classes = this.theme.addStyleSheet(styles, STYLE_PRIORITY$1);\n    this.defaultSvgIcon = '<svg viewBox=\"0 0 20 20\"><circle cx=\"10\" cy=\"10\" r=\"10\"></circle></svg>';\n  }\n  get defaultClass() {\n    return this._defaultClass;\n  }\n  get defaultClassPrefix() {\n    return this._defaultClassPrefix;\n  }\n  setSvg(key, url) {\n    if (!this.svgMap.has(key)) {\n      const urlSanitized = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, url);\n      const svgIcon = {\n        obs: this.http.get(`${urlSanitized}.svg`, {\n          responseType: 'text'\n        }).pipe(share(), map(svgText => {\n          if (svgIcon.svg) {\n            return svgIcon.svg;\n          }\n          const svg = this._textToSvg(svgText);\n          this._cacheSvgIcon(svg, key);\n          return svg;\n        }))\n      };\n      this.svgMap.set(key, svgIcon);\n    }\n  }\n  addSvgIconLiteral(key, literal) {\n    if (!this.svgMap.has(key)) {\n      const sanitizedLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n      if (!sanitizedLiteral) {\n        throw new Error(`LyIconService: Failed sanitize '${key}'`);\n      }\n      const svg = this._textToSvg(sanitizedLiteral);\n      this.svgMap.set(key, {\n        svg\n      });\n    }\n  }\n  /** String to SVG */\n  _textToSvg(str) {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str;\n    const svg = div.querySelector('svg');\n    return svg;\n  }\n  _cacheSvgIcon(svg, key) {\n    const svgIconInfo = this.svgMap.get(key);\n    if (!svgIconInfo.svg) {\n      this.svgMap.get(key).svg = svg;\n    }\n  }\n  getSvg(key) {\n    if (!this.svgMap.has(key)) {\n      throw new Error(`LyIconService: Icon ${key} not found`);\n    }\n    return this.svgMap.get(key);\n  }\n  /**\n   * Set default className for `ly-icon`\n   * @param className class name\n   * @param prefix Class prefix,\n   * For example if you use FontAwesome your prefix would be `fa-`,\n   * then in the template it is no longer necessary to use the prefix\n   * Example: `<ly-icon fontIcon=\"alarm\">`\n   */\n  setDefaultClass(className, prefix) {\n    this._defaultClass = className;\n    this._defaultClassPrefix = prefix;\n  }\n  /**\n   * Register new font class alias\n   * demo:\n   * For FontAwesome\n   * registerFontClass({\n   *   key: 'fa',\n   *   class: 'fa'\n   *   prefix: 'fa-'\n   * })\n   */\n  registerFontClass(opt) {\n    this._fontClasses.set(opt.key, opt);\n  }\n  getFontClass(key) {\n    return this._fontClasses.get(key);\n  }\n}\nLyIconService.ɵfac = function LyIconService_Factory(t) {\n  return new (t || LyIconService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i2$1.LyTheme2));\n};\nLyIconService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LyIconService,\n  factory: LyIconService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyIconService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.HttpClient\n    }, {\n      type: i2.DomSanitizer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i2$1.LyTheme2\n    }];\n  }, null);\n})();\nconst STYLE_PRIORITY = -2;\nconst STYLES = theme => {\n  const loading = keyframesUniqueId.next();\n  const {\n    primary,\n    secondary,\n    tertiary\n  } = theme.background;\n  const lum = primary.default.luminance();\n  let one = lum < .5 ? tertiary : secondary;\n  let two = lum < .5 ? secondary : tertiary;\n  one = one.darken(.25 * (lum < .5 ? -1 : 1.1));\n  two = two.darken(.25 * (lum < .5 ? -1 : 1.1));\n  return {\n    $priority: STYLE_PRIORITY,\n    $global: _className => `@keyframes ${loading}{0%{background-position:200% 50%;}100%{background-position:-200% 50%;}}`,\n    root: () => _className => `${_className}{font-size:${theme.icon.fontSize};width:1em;position:relative;height:1em;display:inline-flex;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;}`,\n    loading: _className => `${_className}{background:${`linear-gradient(270deg, ${one}, ${two}, ${two}, ${one})`};background-size:400% 400%;animation:${loading} 8s ease-in-out infinite;}`,\n    defaultIcon: _className => `${_className}{border-radius:50px;}`\n  };\n};\n/** @docs-private */\nclass LyIconBase {\n  constructor(_theme) {\n    this._theme = _theme;\n  }\n}\n/** @docs-private */\nconst LyIconMixinBase = mixinStyleUpdater(mixinBg(mixinColor(mixinRaised(mixinOutlined(mixinElevation(mixinShadowColor(LyIconBase)))))));\nclass LyIcon extends LyIconMixinBase {\n  constructor(iconService, _el, _renderer, theme, sRenderer, _platform) {\n    super(theme);\n    this.iconService = iconService;\n    this._el = _el;\n    this._renderer = _renderer;\n    this.sRenderer = sRenderer;\n    this._platform = _platform;\n    this.classes = this.sRenderer.renderSheet(STYLES, true);\n    this.setAutoContrast();\n  }\n  get icon() {\n    return this._icon;\n  }\n  set icon(val) {\n    this._icon = val;\n    this._addDefaultIcon();\n    if (this._platform.isBrowser) {\n      this._prepareSvgIcon(this.iconService.getSvg(val));\n    }\n  }\n  get fontSet() {\n    return this._fontSet;\n  }\n  set fontSet(key) {\n    this._fontSet = key;\n  }\n  get fontIcon() {\n    return this._fontIcon;\n  }\n  set fontIcon(key) {\n    this._fontIcon = key;\n  }\n  /** @docs-private */\n  get hostElement() {\n    return this._el.nativeElement;\n  }\n  ngOnChanges() {\n    if (this.fontSet || this.fontIcon) {\n      this._updateFontClass();\n    }\n    this.updateStyle(this._el);\n  }\n  _isDefault() {\n    return !(this.icon || this.fontSet);\n  }\n  _prepareSvgIcon(svgIcon) {\n    if (svgIcon.svg) {\n      this._appendChild(svgIcon.svg.cloneNode(true));\n    } else {\n      svgIcon.obs.pipe(take(1)).subscribe(svgElement => {\n        this._appendChild(svgElement.cloneNode(true));\n      });\n    }\n  }\n  _appendChild(svg) {\n    this._cleanIcon();\n    this._iconElement = svg;\n    this._renderer.addClass(svg, this.iconService.classes.svg);\n    this._renderer.appendChild(this._el.nativeElement, svg);\n  }\n  _addDefaultIcon() {\n    this.sRenderer.addClass(this.classes.defaultIcon);\n    this.sRenderer.addClass(this.classes.loading);\n  }\n  // private _appendDefaultSvgIcon() {\n  //   const svgIcon = this.iconService._textToSvg(this.iconService.defaultSvgIcon) as SVGAElement;\n  //   svgIcon.classList.add(this.classes.loading);\n  //   this._appendChild(svgIcon);\n  // }\n  _updateClass() {\n    if (this._isDefault() && this.iconService.defaultClass) {\n      this._renderer.addClass(this._el.nativeElement, this.iconService.defaultClass);\n    }\n  }\n  ngOnInit() {\n    this._updateClass();\n  }\n  ngOnDestroy() {\n    this._cleanIcon();\n  }\n  /**\n   * run only browser\n   * remove current icon\n   */\n  _cleanIcon() {\n    const icon = this._iconElement;\n    this.sRenderer.removeClass(this.classes.defaultIcon);\n    this.sRenderer.removeClass(this.classes.loading);\n    if (icon) {\n      this._renderer.removeChild(this._el.nativeElement, icon);\n      this._iconElement = undefined;\n    }\n  }\n  _updateFontClass() {\n    const currentClass = this._currentClass;\n    const fontSetKey = this.fontSet;\n    const icon = this.fontIcon;\n    const el = this._el.nativeElement;\n    const iconClass = this.iconService.getFontClass(fontSetKey);\n    if (currentClass) {\n      this._renderer.removeClass(el, currentClass);\n    }\n    if (this._previousFontSet) {\n      if (this._previousFontSet.class) {\n        this._renderer.removeClass(el, this._previousFontSet.class);\n      }\n    }\n    if (iconClass) {\n      this._previousFontSet = iconClass;\n    } else {\n      throw new Error(`Icon with key${fontSetKey} not found`);\n    }\n    this._currentClass = `${iconClass.prefix}${icon}`;\n    this._renderer.addClass(el, this._currentClass);\n  }\n}\nLyIcon.ɵfac = function LyIcon_Factory(t) {\n  return new (t || LyIcon)(i0.ɵɵdirectiveInject(LyIconService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2$1.LyTheme2), i0.ɵɵdirectiveInject(i2$1.StyleRenderer), i0.ɵɵdirectiveInject(i3.Platform));\n};\nLyIcon.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: LyIcon,\n  selectors: [[\"ly-icon\"]],\n  inputs: {\n    bg: \"bg\",\n    color: \"color\",\n    raised: \"raised\",\n    outlined: \"outlined\",\n    elevation: \"elevation\",\n    shadowColor: \"shadowColor\",\n    icon: \"icon\",\n    fontSet: \"fontSet\",\n    fontIcon: \"fontIcon\"\n  },\n  exportAs: [\"lyIcon\"],\n  features: [i0.ɵɵProvidersFeature([StyleRenderer]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyIcon, [{\n    type: Directive,\n    args: [{\n      selector: 'ly-icon',\n      inputs: ['bg', 'color', 'raised', 'outlined', 'elevation', 'shadowColor'],\n      exportAs: 'lyIcon',\n      providers: [StyleRenderer]\n    }]\n  }], function () {\n    return [{\n      type: LyIconService\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i2$1.LyTheme2\n    }, {\n      type: i2$1.StyleRenderer\n    }, {\n      type: i3.Platform\n    }];\n  }, {\n    icon: [{\n      type: Input\n    }],\n    fontSet: [{\n      type: Input\n    }],\n    fontIcon: [{\n      type: Input\n    }]\n  });\n})();\nclass LyIconModule {}\nLyIconModule.ɵfac = function LyIconModule_Factory(t) {\n  return new (t || LyIconModule)();\n};\nLyIconModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LyIconModule\n});\nLyIconModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [LyCommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LyIconModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [LyIcon],\n      exports: [LyIcon, LyCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LyIcon, LyIconBase, LyIconMixinBase, LyIconModule, LyIconService, STYLES };", "map": {"version": 3, "names": ["i0", "SecurityContext", "Injectable", "Optional", "Inject", "Directive", "Input", "NgModule", "i2$1", "keyframesUniqueId", "mixinStyleUpdater", "mixinBg", "mixinColor", "mixinRaised", "mixinOutlined", "mixinElevation", "mixinShadowColor", "<PERSON><PERSON><PERSON><PERSON>", "LyCommonModule", "share", "map", "take", "DOCUMENT", "i1", "i2", "i3", "STYLE_PRIORITY$1", "styles", "svg", "width", "height", "fill", "LyIconService", "constructor", "http", "_sanitizer", "_document", "theme", "_defaultClass", "svgMap", "Map", "_fontClasses", "classes", "addStyleSheet", "defaultSvgIcon", "defaultClass", "defaultClassPrefix", "_defaultClassPrefix", "setSvg", "key", "url", "has", "urlSanitized", "sanitize", "RESOURCE_URL", "svgIcon", "obs", "get", "responseType", "pipe", "svgText", "_textToSvg", "_cacheSvgIcon", "set", "addSvgIconLiteral", "literal", "sanitizedLiteral", "HTML", "Error", "str", "div", "createElement", "innerHTML", "querySelector", "svgIconInfo", "getSvg", "setDefaultClass", "className", "prefix", "registerFontClass", "opt", "getFontClass", "ɵfac", "LyIconService_Factory", "t", "ɵɵinject", "HttpClient", "Dom<PERSON><PERSON><PERSON>zer", "LyTheme2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "undefined", "decorators", "STYLE_PRIORITY", "STYLES", "loading", "next", "primary", "secondary", "tertiary", "background", "lum", "default", "luminance", "one", "two", "darken", "$priority", "$global", "_className", "root", "icon", "fontSize", "defaultIcon", "LyIconBase", "_theme", "LyIconMixinBase", "LyIcon", "iconService", "_el", "_renderer", "s<PERSON><PERSON><PERSON>", "_platform", "renderSheet", "setAutoContrast", "_icon", "val", "_addDefaultIcon", "<PERSON><PERSON><PERSON><PERSON>", "_prepareSvgIcon", "fontSet", "_fontSet", "fontIcon", "_fontIcon", "hostElement", "nativeElement", "ngOnChanges", "_updateFontClass", "updateStyle", "_isDefault", "_append<PERSON>hild", "cloneNode", "subscribe", "svgElement", "_cleanIcon", "_iconElement", "addClass", "append<PERSON><PERSON><PERSON>", "_updateClass", "ngOnInit", "ngOnDestroy", "removeClass", "<PERSON><PERSON><PERSON><PERSON>", "currentClass", "_currentClass", "fontSetKey", "el", "iconClass", "_previousFontSet", "class", "LyIcon_Factory", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "Platform", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "bg", "color", "raised", "outlined", "elevation", "shadowColor", "exportAs", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "selector", "providers", "LyIconModule", "LyIconModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@alyle/ui/fesm2020/alyle-ui-icon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Directive, Input, NgModule } from '@angular/core';\nimport * as i2$1 from '@alyle/ui';\nimport { keyframesUniqueId, mixinStyleUpdater, mixinBg, mixinColor, mixinRaised, mixinOutlined, mixinElevation, mixinShadowColor, StyleRenderer, LyCommonModule } from '@alyle/ui';\nimport { share, map, take } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport * as i3 from '@angular/cdk/platform';\n\nconst STYLE_PRIORITY$1 = -2;\n/** The following styles will never be updated */\nconst styles = {\n    svg: {\n        width: 'inherit',\n        height: 'inherit',\n        fill: 'currentColor',\n    }\n};\nclass LyIconService {\n    constructor(http, _sanitizer, _document, theme) {\n        this.http = http;\n        this._sanitizer = _sanitizer;\n        this._document = _document;\n        this.theme = theme;\n        this._defaultClass = 'material-icons';\n        this.svgMap = new Map();\n        this._fontClasses = new Map();\n        /**\n         * Styles\n         * @docs-private\n         */\n        this.classes = this.theme.addStyleSheet(styles, STYLE_PRIORITY$1);\n        this.defaultSvgIcon = '<svg viewBox=\"0 0 20 20\"><circle cx=\"10\" cy=\"10\" r=\"10\"></circle></svg>';\n    }\n    get defaultClass() {\n        return this._defaultClass;\n    }\n    get defaultClassPrefix() {\n        return this._defaultClassPrefix;\n    }\n    setSvg(key, url) {\n        if (!this.svgMap.has(key)) {\n            const urlSanitized = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, url);\n            const svgIcon = {\n                obs: this.http.get(`${urlSanitized}.svg`, { responseType: 'text' })\n                    .pipe(share(), map(svgText => {\n                    if (svgIcon.svg) {\n                        return svgIcon.svg;\n                    }\n                    const svg = this._textToSvg(svgText);\n                    this._cacheSvgIcon(svg, key);\n                    return svg;\n                }))\n            };\n            this.svgMap.set(key, svgIcon);\n        }\n    }\n    addSvgIconLiteral(key, literal) {\n        if (!this.svgMap.has(key)) {\n            const sanitizedLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n            if (!sanitizedLiteral) {\n                throw new Error(`LyIconService: Failed sanitize '${key}'`);\n            }\n            const svg = this._textToSvg(sanitizedLiteral);\n            this.svgMap.set(key, {\n                svg\n            });\n        }\n    }\n    /** String to SVG */\n    _textToSvg(str) {\n        const div = this._document.createElement('DIV');\n        div.innerHTML = str;\n        const svg = div.querySelector('svg');\n        return svg;\n    }\n    _cacheSvgIcon(svg, key) {\n        const svgIconInfo = this.svgMap.get(key);\n        if (!svgIconInfo.svg) {\n            this.svgMap.get(key).svg = svg;\n        }\n    }\n    getSvg(key) {\n        if (!this.svgMap.has(key)) {\n            throw new Error(`LyIconService: Icon ${key} not found`);\n        }\n        return this.svgMap.get(key);\n    }\n    /**\n     * Set default className for `ly-icon`\n     * @param className class name\n     * @param prefix Class prefix,\n     * For example if you use FontAwesome your prefix would be `fa-`,\n     * then in the template it is no longer necessary to use the prefix\n     * Example: `<ly-icon fontIcon=\"alarm\">`\n     */\n    setDefaultClass(className, prefix) {\n        this._defaultClass = className;\n        this._defaultClassPrefix = prefix;\n    }\n    /**\n     * Register new font class alias\n     * demo:\n     * For FontAwesome\n     * registerFontClass({\n     *   key: 'fa',\n     *   class: 'fa'\n     *   prefix: 'fa-'\n     * })\n     */\n    registerFontClass(opt) {\n        this._fontClasses.set(opt.key, opt);\n    }\n    getFontClass(key) {\n        return this._fontClasses.get(key);\n    }\n}\nLyIconService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconService, deps: [{ token: i1.HttpClient }, { token: i2.DomSanitizer }, { token: DOCUMENT, optional: true }, { token: i2$1.LyTheme2 }], target: i0.ɵɵFactoryTarget.Injectable });\nLyIconService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: function () { return [{ type: i1.HttpClient }, { type: i2.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i2$1.LyTheme2 }]; } });\n\nconst STYLE_PRIORITY = -2;\nconst STYLES = (theme) => {\n    const loading = keyframesUniqueId.next();\n    const { primary, secondary, tertiary } = theme.background;\n    const lum = primary.default.luminance();\n    let one = (lum < .5\n        ? tertiary\n        : secondary);\n    let two = (lum < .5\n        ? secondary\n        : tertiary);\n    one = one.darken(.25 * (lum < .5 ? -1 : 1.1));\n    two = two.darken(.25 * (lum < .5 ? -1 : 1.1));\n    return {\n        $priority: STYLE_PRIORITY,\n        $global: (_className) => `@keyframes ${loading}{0%{background-position:200% 50%;}100%{background-position:-200% 50%;}}`,\n        root: () => (_className) => `${_className}{font-size:${theme.icon.fontSize};width:1em;position:relative;height:1em;display:inline-flex;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;}`,\n        loading: (_className) => `${_className}{background:${`linear-gradient(270deg, ${one}, ${two}, ${two}, ${one})`};background-size:400% 400%;animation:${loading} 8s ease-in-out infinite;}`,\n        defaultIcon: (_className) => `${_className}{border-radius:50px;}`\n    };\n};\n/** @docs-private */\nclass LyIconBase {\n    constructor(_theme) {\n        this._theme = _theme;\n    }\n}\n/** @docs-private */\nconst LyIconMixinBase = mixinStyleUpdater(mixinBg(mixinColor(mixinRaised(mixinOutlined(mixinElevation(mixinShadowColor(LyIconBase)))))));\nclass LyIcon extends LyIconMixinBase {\n    constructor(iconService, _el, _renderer, theme, sRenderer, _platform) {\n        super(theme);\n        this.iconService = iconService;\n        this._el = _el;\n        this._renderer = _renderer;\n        this.sRenderer = sRenderer;\n        this._platform = _platform;\n        this.classes = this.sRenderer.renderSheet(STYLES, true);\n        this.setAutoContrast();\n    }\n    get icon() {\n        return this._icon;\n    }\n    set icon(val) {\n        this._icon = val;\n        this._addDefaultIcon();\n        if (this._platform.isBrowser) {\n            this._prepareSvgIcon(this.iconService.getSvg(val));\n        }\n    }\n    get fontSet() {\n        return this._fontSet;\n    }\n    set fontSet(key) {\n        this._fontSet = key;\n    }\n    get fontIcon() {\n        return this._fontIcon;\n    }\n    set fontIcon(key) {\n        this._fontIcon = key;\n    }\n    /** @docs-private */\n    get hostElement() {\n        return this._el.nativeElement;\n    }\n    ngOnChanges() {\n        if (this.fontSet || this.fontIcon) {\n            this._updateFontClass();\n        }\n        this.updateStyle(this._el);\n    }\n    _isDefault() {\n        return !(this.icon || this.fontSet);\n    }\n    _prepareSvgIcon(svgIcon) {\n        if (svgIcon.svg) {\n            this._appendChild(svgIcon.svg.cloneNode(true));\n        }\n        else {\n            svgIcon.obs\n                .pipe(take(1))\n                .subscribe((svgElement) => {\n                this._appendChild(svgElement.cloneNode(true));\n            });\n        }\n    }\n    _appendChild(svg) {\n        this._cleanIcon();\n        this._iconElement = svg;\n        this._renderer.addClass(svg, this.iconService.classes.svg);\n        this._renderer.appendChild(this._el.nativeElement, svg);\n    }\n    _addDefaultIcon() {\n        this.sRenderer.addClass(this.classes.defaultIcon);\n        this.sRenderer.addClass(this.classes.loading);\n    }\n    // private _appendDefaultSvgIcon() {\n    //   const svgIcon = this.iconService._textToSvg(this.iconService.defaultSvgIcon) as SVGAElement;\n    //   svgIcon.classList.add(this.classes.loading);\n    //   this._appendChild(svgIcon);\n    // }\n    _updateClass() {\n        if (this._isDefault() && this.iconService.defaultClass) {\n            this._renderer.addClass(this._el.nativeElement, this.iconService.defaultClass);\n        }\n    }\n    ngOnInit() {\n        this._updateClass();\n    }\n    ngOnDestroy() {\n        this._cleanIcon();\n    }\n    /**\n     * run only browser\n     * remove current icon\n     */\n    _cleanIcon() {\n        const icon = this._iconElement;\n        this.sRenderer.removeClass(this.classes.defaultIcon);\n        this.sRenderer.removeClass(this.classes.loading);\n        if (icon) {\n            this._renderer.removeChild(this._el.nativeElement, icon);\n            this._iconElement = undefined;\n        }\n    }\n    _updateFontClass() {\n        const currentClass = this._currentClass;\n        const fontSetKey = this.fontSet;\n        const icon = this.fontIcon;\n        const el = this._el.nativeElement;\n        const iconClass = this.iconService.getFontClass(fontSetKey);\n        if (currentClass) {\n            this._renderer.removeClass(el, currentClass);\n        }\n        if (this._previousFontSet) {\n            if (this._previousFontSet.class) {\n                this._renderer.removeClass(el, this._previousFontSet.class);\n            }\n        }\n        if (iconClass) {\n            this._previousFontSet = iconClass;\n        }\n        else {\n            throw new Error(`Icon with key${fontSetKey} not found`);\n        }\n        this._currentClass = `${iconClass.prefix}${icon}`;\n        this._renderer.addClass(el, this._currentClass);\n    }\n}\nLyIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIcon, deps: [{ token: LyIconService }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i2$1.LyTheme2 }, { token: i2$1.StyleRenderer }, { token: i3.Platform }], target: i0.ɵɵFactoryTarget.Directive });\nLyIcon.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: LyIcon, selector: \"ly-icon\", inputs: { bg: \"bg\", color: \"color\", raised: \"raised\", outlined: \"outlined\", elevation: \"elevation\", shadowColor: \"shadowColor\", icon: \"icon\", fontSet: \"fontSet\", fontIcon: \"fontIcon\" }, providers: [\n        StyleRenderer\n    ], exportAs: [\"lyIcon\"], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ly-icon',\n                    inputs: [\n                        'bg',\n                        'color',\n                        'raised',\n                        'outlined',\n                        'elevation',\n                        'shadowColor',\n                    ],\n                    exportAs: 'lyIcon',\n                    providers: [\n                        StyleRenderer\n                    ]\n                }]\n        }], ctorParameters: function () { return [{ type: LyIconService }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i2$1.LyTheme2 }, { type: i2$1.StyleRenderer }, { type: i3.Platform }]; }, propDecorators: { icon: [{\n                type: Input\n            }], fontSet: [{\n                type: Input\n            }], fontIcon: [{\n                type: Input\n            }] } });\n\nclass LyIconModule {\n}\nLyIconModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nLyIconModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconModule, declarations: [LyIcon], exports: [LyIcon, LyCommonModule] });\nLyIconModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconModule, imports: [LyCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: LyIconModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [LyIcon],\n                    exports: [LyIcon, LyCommonModule]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LyIcon, LyIconBase, LyIconMixinBase, LyIconModule, LyIconService, STYLES };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACzG,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,SAASC,iBAAiB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,QAAQ,WAAW;AAClL,SAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;AACjD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAE3C,MAAMC,gBAAgB,GAAG,CAAC,CAAC;AAC3B;AACA,MAAMC,MAAM,GAAG;EACXC,GAAG,EAAE;IACDC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE;EACV;AACJ,CAAC;AACD,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;IAC5C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAG,gBAAgB;IACrC,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,YAAY,GAAG,IAAID,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACE,OAAO,GAAG,IAAI,CAACL,KAAK,CAACM,aAAa,CAAChB,MAAM,EAAED,gBAAgB,CAAC;IACjE,IAAI,CAACkB,cAAc,GAAG,yEAAyE;EACnG;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACP,aAAa;EAC7B;EACA,IAAIQ,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,mBAAmB;EACnC;EACAC,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACb,IAAI,CAAC,IAAI,CAACX,MAAM,CAACY,GAAG,CAACF,GAAG,CAAC,EAAE;MACvB,MAAMG,YAAY,GAAG,IAAI,CAACjB,UAAU,CAACkB,QAAQ,CAACpD,eAAe,CAACqD,YAAY,EAAEJ,GAAG,CAAC;MAChF,MAAMK,OAAO,GAAG;QACZC,GAAG,EAAE,IAAI,CAACtB,IAAI,CAACuB,GAAG,CAAE,GAAEL,YAAa,MAAK,EAAE;UAAEM,YAAY,EAAE;QAAO,CAAC,CAAC,CAC9DC,IAAI,CAACxC,KAAK,CAAC,CAAC,EAAEC,GAAG,CAACwC,OAAO,IAAI;UAC9B,IAAIL,OAAO,CAAC3B,GAAG,EAAE;YACb,OAAO2B,OAAO,CAAC3B,GAAG;UACtB;UACA,MAAMA,GAAG,GAAG,IAAI,CAACiC,UAAU,CAACD,OAAO,CAAC;UACpC,IAAI,CAACE,aAAa,CAAClC,GAAG,EAAEqB,GAAG,CAAC;UAC5B,OAAOrB,GAAG;QACd,CAAC,CAAC;MACN,CAAC;MACD,IAAI,CAACW,MAAM,CAACwB,GAAG,CAACd,GAAG,EAAEM,OAAO,CAAC;IACjC;EACJ;EACAS,iBAAiBA,CAACf,GAAG,EAAEgB,OAAO,EAAE;IAC5B,IAAI,CAAC,IAAI,CAAC1B,MAAM,CAACY,GAAG,CAACF,GAAG,CAAC,EAAE;MACvB,MAAMiB,gBAAgB,GAAG,IAAI,CAAC/B,UAAU,CAACkB,QAAQ,CAACpD,eAAe,CAACkE,IAAI,EAAEF,OAAO,CAAC;MAChF,IAAI,CAACC,gBAAgB,EAAE;QACnB,MAAM,IAAIE,KAAK,CAAE,mCAAkCnB,GAAI,GAAE,CAAC;MAC9D;MACA,MAAMrB,GAAG,GAAG,IAAI,CAACiC,UAAU,CAACK,gBAAgB,CAAC;MAC7C,IAAI,CAAC3B,MAAM,CAACwB,GAAG,CAACd,GAAG,EAAE;QACjBrB;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAiC,UAAUA,CAACQ,GAAG,EAAE;IACZ,MAAMC,GAAG,GAAG,IAAI,CAAClC,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC/CD,GAAG,CAACE,SAAS,GAAGH,GAAG;IACnB,MAAMzC,GAAG,GAAG0C,GAAG,CAACG,aAAa,CAAC,KAAK,CAAC;IACpC,OAAO7C,GAAG;EACd;EACAkC,aAAaA,CAAClC,GAAG,EAAEqB,GAAG,EAAE;IACpB,MAAMyB,WAAW,GAAG,IAAI,CAACnC,MAAM,CAACkB,GAAG,CAACR,GAAG,CAAC;IACxC,IAAI,CAACyB,WAAW,CAAC9C,GAAG,EAAE;MAClB,IAAI,CAACW,MAAM,CAACkB,GAAG,CAACR,GAAG,CAAC,CAACrB,GAAG,GAAGA,GAAG;IAClC;EACJ;EACA+C,MAAMA,CAAC1B,GAAG,EAAE;IACR,IAAI,CAAC,IAAI,CAACV,MAAM,CAACY,GAAG,CAACF,GAAG,CAAC,EAAE;MACvB,MAAM,IAAImB,KAAK,CAAE,uBAAsBnB,GAAI,YAAW,CAAC;IAC3D;IACA,OAAO,IAAI,CAACV,MAAM,CAACkB,GAAG,CAACR,GAAG,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI2B,eAAeA,CAACC,SAAS,EAAEC,MAAM,EAAE;IAC/B,IAAI,CAACxC,aAAa,GAAGuC,SAAS;IAC9B,IAAI,CAAC9B,mBAAmB,GAAG+B,MAAM;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACC,GAAG,EAAE;IACnB,IAAI,CAACvC,YAAY,CAACsB,GAAG,CAACiB,GAAG,CAAC/B,GAAG,EAAE+B,GAAG,CAAC;EACvC;EACAC,YAAYA,CAAChC,GAAG,EAAE;IACd,OAAO,IAAI,CAACR,YAAY,CAACgB,GAAG,CAACR,GAAG,CAAC;EACrC;AACJ;AACAjB,aAAa,CAACkD,IAAI,YAAAC,sBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyFpD,aAAa,EAAvBhC,EAAE,CAAAqF,QAAA,CAAuC9D,EAAE,CAAC+D,UAAU,GAAtDtF,EAAE,CAAAqF,QAAA,CAAiE7D,EAAE,CAAC+D,YAAY,GAAlFvF,EAAE,CAAAqF,QAAA,CAA6F/D,QAAQ,MAAvGtB,EAAE,CAAAqF,QAAA,CAAkI7E,IAAI,CAACgF,QAAQ;AAAA,CAA6C;AAC/RxD,aAAa,CAACyD,KAAK,kBAD8EzF,EAAE,CAAA0F,kBAAA;EAAAC,KAAA,EACY3D,aAAa;EAAA4D,OAAA,EAAb5D,aAAa,CAAAkD,IAAA;EAAAW,UAAA,EAAc;AAAM,EAAG;AACnJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFiG9F,EAAE,CAAA+F,iBAAA,CAEP/D,aAAa,EAAc,CAAC;IAC5GgE,IAAI,EAAE9F,UAAU;IAChB+F,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEzE,EAAE,CAAC+D;IAAW,CAAC,EAAE;MAAEU,IAAI,EAAExE,EAAE,CAAC+D;IAAa,CAAC,EAAE;MAAES,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClHH,IAAI,EAAE7F;MACV,CAAC,EAAE;QACC6F,IAAI,EAAE5F,MAAM;QACZ6F,IAAI,EAAE,CAAC3E,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE0E,IAAI,EAAExF,IAAI,CAACgF;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;AAEjD,MAAMY,cAAc,GAAG,CAAC,CAAC;AACzB,MAAMC,MAAM,GAAIhE,KAAK,IAAK;EACtB,MAAMiE,OAAO,GAAG7F,iBAAiB,CAAC8F,IAAI,CAAC,CAAC;EACxC,MAAM;IAAEC,OAAO;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAGrE,KAAK,CAACsE,UAAU;EACzD,MAAMC,GAAG,GAAGJ,OAAO,CAACK,OAAO,CAACC,SAAS,CAAC,CAAC;EACvC,IAAIC,GAAG,GAAIH,GAAG,GAAG,EAAE,GACbF,QAAQ,GACRD,SAAU;EAChB,IAAIO,GAAG,GAAIJ,GAAG,GAAG,EAAE,GACbH,SAAS,GACTC,QAAS;EACfK,GAAG,GAAGA,GAAG,CAACE,MAAM,CAAC,GAAG,IAAIL,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;EAC7CI,GAAG,GAAGA,GAAG,CAACC,MAAM,CAAC,GAAG,IAAIL,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;EAC7C,OAAO;IACHM,SAAS,EAAEd,cAAc;IACzBe,OAAO,EAAGC,UAAU,IAAM,cAAad,OAAQ,yEAAwE;IACvHe,IAAI,EAAEA,CAAA,KAAOD,UAAU,IAAM,GAAEA,UAAW,cAAa/E,KAAK,CAACiF,IAAI,CAACC,QAAS,iJAAgJ;IAC3NjB,OAAO,EAAGc,UAAU,IAAM,GAAEA,UAAW,eAAe,2BAA0BL,GAAI,KAAIC,GAAI,KAAIA,GAAI,KAAID,GAAI,GAAG,wCAAuCT,OAAQ,4BAA2B;IACzLkB,WAAW,EAAGJ,UAAU,IAAM,GAAEA,UAAW;EAC/C,CAAC;AACL,CAAC;AACD;AACA,MAAMK,UAAU,CAAC;EACbxF,WAAWA,CAACyF,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;AACA,MAAMC,eAAe,GAAGjH,iBAAiB,CAACC,OAAO,CAACC,UAAU,CAACC,WAAW,CAACC,aAAa,CAACC,cAAc,CAACC,gBAAgB,CAACyG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxI,MAAMG,MAAM,SAASD,eAAe,CAAC;EACjC1F,WAAWA,CAAC4F,WAAW,EAAEC,GAAG,EAAEC,SAAS,EAAE1F,KAAK,EAAE2F,SAAS,EAAEC,SAAS,EAAE;IAClE,KAAK,CAAC5F,KAAK,CAAC;IACZ,IAAI,CAACwF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACvF,OAAO,GAAG,IAAI,CAACsF,SAAS,CAACE,WAAW,CAAC7B,MAAM,EAAE,IAAI,CAAC;IACvD,IAAI,CAAC8B,eAAe,CAAC,CAAC;EAC1B;EACA,IAAIb,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACc,KAAK;EACrB;EACA,IAAId,IAAIA,CAACe,GAAG,EAAE;IACV,IAAI,CAACD,KAAK,GAAGC,GAAG;IAChB,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,IAAI,CAACL,SAAS,CAACM,SAAS,EAAE;MAC1B,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,WAAW,CAAClD,MAAM,CAAC0D,GAAG,CAAC,CAAC;IACtD;EACJ;EACA,IAAII,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACxF,GAAG,EAAE;IACb,IAAI,CAACyF,QAAQ,GAAGzF,GAAG;EACvB;EACA,IAAI0F,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC1F,GAAG,EAAE;IACd,IAAI,CAAC2F,SAAS,GAAG3F,GAAG;EACxB;EACA;EACA,IAAI4F,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACf,GAAG,CAACgB,aAAa;EACjC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACN,OAAO,IAAI,IAAI,CAACE,QAAQ,EAAE;MAC/B,IAAI,CAACK,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,WAAW,CAAC,IAAI,CAACnB,GAAG,CAAC;EAC9B;EACAoB,UAAUA,CAAA,EAAG;IACT,OAAO,EAAE,IAAI,CAAC5B,IAAI,IAAI,IAAI,CAACmB,OAAO,CAAC;EACvC;EACAD,eAAeA,CAACjF,OAAO,EAAE;IACrB,IAAIA,OAAO,CAAC3B,GAAG,EAAE;MACb,IAAI,CAACuH,YAAY,CAAC5F,OAAO,CAAC3B,GAAG,CAACwH,SAAS,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,MACI;MACD7F,OAAO,CAACC,GAAG,CACNG,IAAI,CAACtC,IAAI,CAAC,CAAC,CAAC,CAAC,CACbgI,SAAS,CAAEC,UAAU,IAAK;QAC3B,IAAI,CAACH,YAAY,CAACG,UAAU,CAACF,SAAS,CAAC,IAAI,CAAC,CAAC;MACjD,CAAC,CAAC;IACN;EACJ;EACAD,YAAYA,CAACvH,GAAG,EAAE;IACd,IAAI,CAAC2H,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG5H,GAAG;IACvB,IAAI,CAACmG,SAAS,CAAC0B,QAAQ,CAAC7H,GAAG,EAAE,IAAI,CAACiG,WAAW,CAACnF,OAAO,CAACd,GAAG,CAAC;IAC1D,IAAI,CAACmG,SAAS,CAAC2B,WAAW,CAAC,IAAI,CAAC5B,GAAG,CAACgB,aAAa,EAAElH,GAAG,CAAC;EAC3D;EACA0G,eAAeA,CAAA,EAAG;IACd,IAAI,CAACN,SAAS,CAACyB,QAAQ,CAAC,IAAI,CAAC/G,OAAO,CAAC8E,WAAW,CAAC;IACjD,IAAI,CAACQ,SAAS,CAACyB,QAAQ,CAAC,IAAI,CAAC/G,OAAO,CAAC4D,OAAO,CAAC;EACjD;EACA;EACA;EACA;EACA;EACA;EACAqD,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACT,UAAU,CAAC,CAAC,IAAI,IAAI,CAACrB,WAAW,CAAChF,YAAY,EAAE;MACpD,IAAI,CAACkF,SAAS,CAAC0B,QAAQ,CAAC,IAAI,CAAC3B,GAAG,CAACgB,aAAa,EAAE,IAAI,CAACjB,WAAW,CAAChF,YAAY,CAAC;IAClF;EACJ;EACA+G,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,YAAY,CAAC,CAAC;EACvB;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIA,UAAUA,CAAA,EAAG;IACT,MAAMjC,IAAI,GAAG,IAAI,CAACkC,YAAY;IAC9B,IAAI,CAACxB,SAAS,CAAC8B,WAAW,CAAC,IAAI,CAACpH,OAAO,CAAC8E,WAAW,CAAC;IACpD,IAAI,CAACQ,SAAS,CAAC8B,WAAW,CAAC,IAAI,CAACpH,OAAO,CAAC4D,OAAO,CAAC;IAChD,IAAIgB,IAAI,EAAE;MACN,IAAI,CAACS,SAAS,CAACgC,WAAW,CAAC,IAAI,CAACjC,GAAG,CAACgB,aAAa,EAAExB,IAAI,CAAC;MACxD,IAAI,CAACkC,YAAY,GAAGtD,SAAS;IACjC;EACJ;EACA8C,gBAAgBA,CAAA,EAAG;IACf,MAAMgB,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,MAAMC,UAAU,GAAG,IAAI,CAACzB,OAAO;IAC/B,MAAMnB,IAAI,GAAG,IAAI,CAACqB,QAAQ;IAC1B,MAAMwB,EAAE,GAAG,IAAI,CAACrC,GAAG,CAACgB,aAAa;IACjC,MAAMsB,SAAS,GAAG,IAAI,CAACvC,WAAW,CAAC5C,YAAY,CAACiF,UAAU,CAAC;IAC3D,IAAIF,YAAY,EAAE;MACd,IAAI,CAACjC,SAAS,CAAC+B,WAAW,CAACK,EAAE,EAAEH,YAAY,CAAC;IAChD;IACA,IAAI,IAAI,CAACK,gBAAgB,EAAE;MACvB,IAAI,IAAI,CAACA,gBAAgB,CAACC,KAAK,EAAE;QAC7B,IAAI,CAACvC,SAAS,CAAC+B,WAAW,CAACK,EAAE,EAAE,IAAI,CAACE,gBAAgB,CAACC,KAAK,CAAC;MAC/D;IACJ;IACA,IAAIF,SAAS,EAAE;MACX,IAAI,CAACC,gBAAgB,GAAGD,SAAS;IACrC,CAAC,MACI;MACD,MAAM,IAAIhG,KAAK,CAAE,gBAAe8F,UAAW,YAAW,CAAC;IAC3D;IACA,IAAI,CAACD,aAAa,GAAI,GAAEG,SAAS,CAACtF,MAAO,GAAEwC,IAAK,EAAC;IACjD,IAAI,CAACS,SAAS,CAAC0B,QAAQ,CAACU,EAAE,EAAE,IAAI,CAACF,aAAa,CAAC;EACnD;AACJ;AACArC,MAAM,CAAC1C,IAAI,YAAAqF,eAAAnF,CAAA;EAAA,YAAAA,CAAA,IAAyFwC,MAAM,EApKT5H,EAAE,CAAAwK,iBAAA,CAoKyBxI,aAAa,GApKxChC,EAAE,CAAAwK,iBAAA,CAoKmDxK,EAAE,CAACyK,UAAU,GApKlEzK,EAAE,CAAAwK,iBAAA,CAoK6ExK,EAAE,CAAC0K,SAAS,GApK3F1K,EAAE,CAAAwK,iBAAA,CAoKsGhK,IAAI,CAACgF,QAAQ,GApKrHxF,EAAE,CAAAwK,iBAAA,CAoKgIhK,IAAI,CAACS,aAAa,GApKpJjB,EAAE,CAAAwK,iBAAA,CAoK+J/I,EAAE,CAACkJ,QAAQ;AAAA,CAA4C;AACzT/C,MAAM,CAACgD,IAAI,kBArKsF5K,EAAE,CAAA6K,iBAAA;EAAA7E,IAAA,EAqKX4B,MAAM;EAAAkD,SAAA;EAAAC,MAAA;IAAAC,EAAA;IAAAC,KAAA;IAAAC,MAAA;IAAAC,QAAA;IAAAC,SAAA;IAAAC,WAAA;IAAA/D,IAAA;IAAAmB,OAAA;IAAAE,QAAA;EAAA;EAAA2C,QAAA;EAAAC,QAAA,GArKGvL,EAAE,CAAAwL,kBAAA,CAqKuN,CAClTvK,aAAa,CAChB,GAvK4FjB,EAAE,CAAAyL,0BAAA,EAAFzL,EAAE,CAAA0L,oBAAA;AAAA,EAuKX;AACxF;EAAA,QAAA5F,SAAA,oBAAAA,SAAA,KAxKiG9F,EAAE,CAAA+F,iBAAA,CAwKP6B,MAAM,EAAc,CAAC;IACrG5B,IAAI,EAAE3F,SAAS;IACf4F,IAAI,EAAE,CAAC;MACC0F,QAAQ,EAAE,SAAS;MACnBZ,MAAM,EAAE,CACJ,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,UAAU,EACV,WAAW,EACX,aAAa,CAChB;MACDO,QAAQ,EAAE,QAAQ;MAClBM,SAAS,EAAE,CACP3K,aAAa;IAErB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+E,IAAI,EAAEhE;IAAc,CAAC,EAAE;MAAEgE,IAAI,EAAEhG,EAAE,CAACyK;IAAW,CAAC,EAAE;MAAEzE,IAAI,EAAEhG,EAAE,CAAC0K;IAAU,CAAC,EAAE;MAAE1E,IAAI,EAAExF,IAAI,CAACgF;IAAS,CAAC,EAAE;MAAEQ,IAAI,EAAExF,IAAI,CAACS;IAAc,CAAC,EAAE;MAAE+E,IAAI,EAAEvE,EAAE,CAACkJ;IAAS,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAErD,IAAI,EAAE,CAAC;MACvNtB,IAAI,EAAE1F;IACV,CAAC,CAAC;IAAEmI,OAAO,EAAE,CAAC;MACVzC,IAAI,EAAE1F;IACV,CAAC,CAAC;IAAEqI,QAAQ,EAAE,CAAC;MACX3C,IAAI,EAAE1F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuL,YAAY,CAAC;AAEnBA,YAAY,CAAC3G,IAAI,YAAA4G,qBAAA1G,CAAA;EAAA,YAAAA,CAAA,IAAyFyG,YAAY;AAAA,CAAkD;AACxKA,YAAY,CAACE,IAAI,kBApMgF/L,EAAE,CAAAgM,gBAAA;EAAAhG,IAAA,EAoMQ6F;AAAY,EAA8D;AACrLA,YAAY,CAACI,IAAI,kBArMgFjM,EAAE,CAAAkM,gBAAA;EAAAC,OAAA,GAqMgCjL,cAAc;AAAA,EAAI;AACrJ;EAAA,QAAA4E,SAAA,oBAAAA,SAAA,KAtMiG9F,EAAE,CAAA+F,iBAAA,CAsMP8F,YAAY,EAAc,CAAC;IAC3G7F,IAAI,EAAEzF,QAAQ;IACd0F,IAAI,EAAE,CAAC;MACCmG,YAAY,EAAE,CAACxE,MAAM,CAAC;MACtByE,OAAO,EAAE,CAACzE,MAAM,EAAE1G,cAAc;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS0G,MAAM,EAAEH,UAAU,EAAEE,eAAe,EAAEkE,YAAY,EAAE7J,aAAa,EAAEqE,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowToggle(openings, closingSelector) {\n  return source => source.lift(new WindowToggleOperator(openings, closingSelector));\n}\nclass WindowToggleOperator {\n  constructor(openings, closingSelector) {\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new WindowToggleSubscriber(subscriber, this.openings, this.closingSelector));\n  }\n}\nclass WindowToggleSubscriber extends OuterSubscriber {\n  constructor(destination, openings, closingSelector) {\n    super(destination);\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n    this.contexts = [];\n    this.add(this.openSubscription = subscribeToResult(this, openings, openings));\n  }\n  _next(value) {\n    const {\n      contexts\n    } = this;\n    if (contexts) {\n      const len = contexts.length;\n      for (let i = 0; i < len; i++) {\n        contexts[i].window.next(value);\n      }\n    }\n  }\n  _error(err) {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.error(err);\n        context.subscription.unsubscribe();\n      }\n    }\n    super._error(err);\n  }\n  _complete() {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.complete();\n        context.subscription.unsubscribe();\n      }\n    }\n    super._complete();\n  }\n  _unsubscribe() {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.unsubscribe();\n        context.subscription.unsubscribe();\n      }\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    if (outerValue === this.openings) {\n      let closingNotifier;\n      try {\n        const {\n          closingSelector\n        } = this;\n        closingNotifier = closingSelector(innerValue);\n      } catch (e) {\n        return this.error(e);\n      }\n      const window = new Subject();\n      const subscription = new Subscription();\n      const context = {\n        window,\n        subscription\n      };\n      this.contexts.push(context);\n      const innerSubscription = subscribeToResult(this, closingNotifier, context);\n      if (innerSubscription.closed) {\n        this.closeWindow(this.contexts.length - 1);\n      } else {\n        innerSubscription.context = context;\n        subscription.add(innerSubscription);\n      }\n      this.destination.next(window);\n    } else {\n      this.closeWindow(this.contexts.indexOf(outerValue));\n    }\n  }\n  notifyError(err) {\n    this.error(err);\n  }\n  notifyComplete(inner) {\n    if (inner !== this.openSubscription) {\n      this.closeWindow(this.contexts.indexOf(inner.context));\n    }\n  }\n  closeWindow(index) {\n    if (index === -1) {\n      return;\n    }\n    const {\n      contexts\n    } = this;\n    const context = contexts[index];\n    const {\n      window,\n      subscription\n    } = context;\n    contexts.splice(index, 1);\n    window.complete();\n    subscription.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "OuterSubscriber", "subscribeToResult", "windowToggle", "openings", "closingSelector", "source", "lift", "WindowToggleOperator", "constructor", "call", "subscriber", "subscribe", "WindowToggleSubscriber", "destination", "contexts", "add", "openSubscription", "_next", "value", "len", "length", "i", "window", "next", "_error", "err", "index", "context", "error", "subscription", "unsubscribe", "_complete", "complete", "_unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "closingNotifier", "e", "push", "innerSubscription", "closed", "closeWindow", "indexOf", "notifyError", "notifyComplete", "inner", "splice"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/windowToggle.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowToggle(openings, closingSelector) {\n    return (source) => source.lift(new WindowToggleOperator(openings, closingSelector));\n}\nclass WindowToggleOperator {\n    constructor(openings, closingSelector) {\n        this.openings = openings;\n        this.closingSelector = closingSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WindowToggleSubscriber(subscriber, this.openings, this.closingSelector));\n    }\n}\nclass WindowToggleSubscriber extends OuterSubscriber {\n    constructor(destination, openings, closingSelector) {\n        super(destination);\n        this.openings = openings;\n        this.closingSelector = closingSelector;\n        this.contexts = [];\n        this.add(this.openSubscription = subscribeToResult(this, openings, openings));\n    }\n    _next(value) {\n        const { contexts } = this;\n        if (contexts) {\n            const len = contexts.length;\n            for (let i = 0; i < len; i++) {\n                contexts[i].window.next(value);\n            }\n        }\n    }\n    _error(err) {\n        const { contexts } = this;\n        this.contexts = null;\n        if (contexts) {\n            const len = contexts.length;\n            let index = -1;\n            while (++index < len) {\n                const context = contexts[index];\n                context.window.error(err);\n                context.subscription.unsubscribe();\n            }\n        }\n        super._error(err);\n    }\n    _complete() {\n        const { contexts } = this;\n        this.contexts = null;\n        if (contexts) {\n            const len = contexts.length;\n            let index = -1;\n            while (++index < len) {\n                const context = contexts[index];\n                context.window.complete();\n                context.subscription.unsubscribe();\n            }\n        }\n        super._complete();\n    }\n    _unsubscribe() {\n        const { contexts } = this;\n        this.contexts = null;\n        if (contexts) {\n            const len = contexts.length;\n            let index = -1;\n            while (++index < len) {\n                const context = contexts[index];\n                context.window.unsubscribe();\n                context.subscription.unsubscribe();\n            }\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        if (outerValue === this.openings) {\n            let closingNotifier;\n            try {\n                const { closingSelector } = this;\n                closingNotifier = closingSelector(innerValue);\n            }\n            catch (e) {\n                return this.error(e);\n            }\n            const window = new Subject();\n            const subscription = new Subscription();\n            const context = { window, subscription };\n            this.contexts.push(context);\n            const innerSubscription = subscribeToResult(this, closingNotifier, context);\n            if (innerSubscription.closed) {\n                this.closeWindow(this.contexts.length - 1);\n            }\n            else {\n                innerSubscription.context = context;\n                subscription.add(innerSubscription);\n            }\n            this.destination.next(window);\n        }\n        else {\n            this.closeWindow(this.contexts.indexOf(outerValue));\n        }\n    }\n    notifyError(err) {\n        this.error(err);\n    }\n    notifyComplete(inner) {\n        if (inner !== this.openSubscription) {\n            this.closeWindow(this.contexts.indexOf(inner.context));\n        }\n    }\n    closeWindow(index) {\n        if (index === -1) {\n            return;\n        }\n        const { contexts } = this;\n        const context = contexts[index];\n        const { window, subscription } = context;\n        contexts.splice(index, 1);\n        window.complete();\n        subscription.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,eAAe,EAAE;EACpD,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,oBAAoB,CAACJ,QAAQ,EAAEC,eAAe,CAAC,CAAC;AACvF;AACA,MAAMG,oBAAoB,CAAC;EACvBC,WAAWA,CAACL,QAAQ,EAAEC,eAAe,EAAE;IACnC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,sBAAsB,CAACF,UAAU,EAAE,IAAI,CAACP,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;EACxG;AACJ;AACA,MAAMQ,sBAAsB,SAASZ,eAAe,CAAC;EACjDQ,WAAWA,CAACK,WAAW,EAAEV,QAAQ,EAAEC,eAAe,EAAE;IAChD,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACU,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,gBAAgB,GAAGf,iBAAiB,CAAC,IAAI,EAAEE,QAAQ,EAAEA,QAAQ,CAAC,CAAC;EACjF;EACAc,KAAKA,CAACC,KAAK,EAAE;IACT,MAAM;MAAEJ;IAAS,CAAC,GAAG,IAAI;IACzB,IAAIA,QAAQ,EAAE;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAAM;MAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC1BP,QAAQ,CAACO,CAAC,CAAC,CAACC,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC;MAClC;IACJ;EACJ;EACAM,MAAMA,CAACC,GAAG,EAAE;IACR,MAAM;MAAEX;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAIA,QAAQ,EAAE;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAAM;MAC3B,IAAIM,KAAK,GAAG,CAAC,CAAC;MACd,OAAO,EAAEA,KAAK,GAAGP,GAAG,EAAE;QAClB,MAAMQ,OAAO,GAAGb,QAAQ,CAACY,KAAK,CAAC;QAC/BC,OAAO,CAACL,MAAM,CAACM,KAAK,CAACH,GAAG,CAAC;QACzBE,OAAO,CAACE,YAAY,CAACC,WAAW,CAAC,CAAC;MACtC;IACJ;IACA,KAAK,CAACN,MAAM,CAACC,GAAG,CAAC;EACrB;EACAM,SAASA,CAAA,EAAG;IACR,MAAM;MAAEjB;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAIA,QAAQ,EAAE;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAAM;MAC3B,IAAIM,KAAK,GAAG,CAAC,CAAC;MACd,OAAO,EAAEA,KAAK,GAAGP,GAAG,EAAE;QAClB,MAAMQ,OAAO,GAAGb,QAAQ,CAACY,KAAK,CAAC;QAC/BC,OAAO,CAACL,MAAM,CAACU,QAAQ,CAAC,CAAC;QACzBL,OAAO,CAACE,YAAY,CAACC,WAAW,CAAC,CAAC;MACtC;IACJ;IACA,KAAK,CAACC,SAAS,CAAC,CAAC;EACrB;EACAE,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEnB;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAIA,QAAQ,EAAE;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAAM;MAC3B,IAAIM,KAAK,GAAG,CAAC,CAAC;MACd,OAAO,EAAEA,KAAK,GAAGP,GAAG,EAAE;QAClB,MAAMQ,OAAO,GAAGb,QAAQ,CAACY,KAAK,CAAC;QAC/BC,OAAO,CAACL,MAAM,CAACQ,WAAW,CAAC,CAAC;QAC5BH,OAAO,CAACE,YAAY,CAACC,WAAW,CAAC,CAAC;MACtC;IACJ;EACJ;EACAI,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAIJ,UAAU,KAAK,IAAI,CAAChC,QAAQ,EAAE;MAC9B,IAAIqC,eAAe;MACnB,IAAI;QACA,MAAM;UAAEpC;QAAgB,CAAC,GAAG,IAAI;QAChCoC,eAAe,GAAGpC,eAAe,CAACgC,UAAU,CAAC;MACjD,CAAC,CACD,OAAOK,CAAC,EAAE;QACN,OAAO,IAAI,CAACb,KAAK,CAACa,CAAC,CAAC;MACxB;MACA,MAAMnB,MAAM,GAAG,IAAIxB,OAAO,CAAC,CAAC;MAC5B,MAAM+B,YAAY,GAAG,IAAI9B,YAAY,CAAC,CAAC;MACvC,MAAM4B,OAAO,GAAG;QAAEL,MAAM;QAAEO;MAAa,CAAC;MACxC,IAAI,CAACf,QAAQ,CAAC4B,IAAI,CAACf,OAAO,CAAC;MAC3B,MAAMgB,iBAAiB,GAAG1C,iBAAiB,CAAC,IAAI,EAAEuC,eAAe,EAAEb,OAAO,CAAC;MAC3E,IAAIgB,iBAAiB,CAACC,MAAM,EAAE;QAC1B,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC/B,QAAQ,CAACM,MAAM,GAAG,CAAC,CAAC;MAC9C,CAAC,MACI;QACDuB,iBAAiB,CAAChB,OAAO,GAAGA,OAAO;QACnCE,YAAY,CAACd,GAAG,CAAC4B,iBAAiB,CAAC;MACvC;MACA,IAAI,CAAC9B,WAAW,CAACU,IAAI,CAACD,MAAM,CAAC;IACjC,CAAC,MACI;MACD,IAAI,CAACuB,WAAW,CAAC,IAAI,CAAC/B,QAAQ,CAACgC,OAAO,CAACX,UAAU,CAAC,CAAC;IACvD;EACJ;EACAY,WAAWA,CAACtB,GAAG,EAAE;IACb,IAAI,CAACG,KAAK,CAACH,GAAG,CAAC;EACnB;EACAuB,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAIA,KAAK,KAAK,IAAI,CAACjC,gBAAgB,EAAE;MACjC,IAAI,CAAC6B,WAAW,CAAC,IAAI,CAAC/B,QAAQ,CAACgC,OAAO,CAACG,KAAK,CAACtB,OAAO,CAAC,CAAC;IAC1D;EACJ;EACAkB,WAAWA,CAACnB,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IACA,MAAM;MAAEZ;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMa,OAAO,GAAGb,QAAQ,CAACY,KAAK,CAAC;IAC/B,MAAM;MAAEJ,MAAM;MAAEO;IAAa,CAAC,GAAGF,OAAO;IACxCb,QAAQ,CAACoC,MAAM,CAACxB,KAAK,EAAE,CAAC,CAAC;IACzBJ,MAAM,CAACU,QAAQ,CAAC,CAAC;IACjBH,YAAY,CAACC,WAAW,CAAC,CAAC;EAC9B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
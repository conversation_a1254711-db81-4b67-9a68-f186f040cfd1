{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"app/services/settings.service\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"app/layout/components/content-header/content-header.component\";\nfunction WeatherPolicyComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h4\", 4);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6);\n    i0.ɵɵtext(6, \">\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.weather_policy.value.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.weather_policy.value.content, i0.ɵɵsanitizeHtml);\n  }\n}\nexport class WeatherPolicyComponent {\n  constructor(_titleService, settingsService, _loadingService) {\n    this._titleService = _titleService;\n    this.settingsService = settingsService;\n    this._loadingService = _loadingService;\n    this._titleService.setTitle('Weather Policy');\n    this.contentHeader = {\n      headerTitle: 'Weather Policy',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Home',\n          isLink: false,\n          link: ''\n        }, {\n          name: 'Weather Policy',\n          isLink: false,\n          link: ''\n        }]\n      }\n    };\n    _loadingService.show();\n    this.settingsService.getSettingNoAuth().toPromise().then(res => {\n      if (res) {\n        this.weather_policy = res.find(element => element.key == AppConfig.SETTINGS_KEYS.WEATHER);\n      }\n    }).finally(() => {\n      _loadingService.dismiss();\n    });\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function WeatherPolicyComponent_Factory(t) {\n    return new (t || WeatherPolicyComponent)(i0.ɵɵdirectiveInject(i1.Title), i0.ɵɵdirectiveInject(i2.SettingsService), i0.ɵɵdirectiveInject(i3.LoadingService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: WeatherPolicyComponent,\n    selectors: [[\"app-weather-policy\"]],\n    decls: 2,\n    vars: 2,\n    consts: [[3, \"contentHeader\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-body\"], [1, \"ck-content\", 3, \"innerHTML\"]],\n    template: function WeatherPolicyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-content-header\", 0);\n        i0.ɵɵtemplate(1, WeatherPolicyComponent_div_1_Template, 7, 2, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.weather_policy);\n      }\n    },\n    dependencies: [i4.NgIf, i5.ContentHeaderComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;;;;ICA1CC,EAAA,CAAAC,cAAA,aAAyC;IAEVD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG9DH,EAAA,CAAAC,cAAA,aAAuB;IACiDD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJpDH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,KAAA,CAAAC,KAAA,CAA8B;IAI5BT,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,cAAAJ,MAAA,CAAAC,cAAA,CAAAC,KAAA,CAAAG,OAAA,EAAAX,EAAA,CAAAY,cAAA,CAA0C;;;ADG3E,OAAM,MAAOC,sBAAsB;EAGjCC,YACSC,aAAoB,EACpBC,eAAgC,EAChCC,eAA+B;IAF/B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAEtB,IAAI,CAACF,aAAa,CAACG,QAAQ,CAAC,gBAAgB,CAAC;IAC7C,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,gBAAgB;MAC7BC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,KAAK;UACbC,IAAI,EAAE;SACP,EACD;UACEF,IAAI,EAAE,gBAAgB;UACtBC,MAAM,EAAE,KAAK;UACbC,IAAI,EAAE;SACP;;KAGN;IACDV,eAAe,CAACW,IAAI,EAAE;IACtB,IAAI,CAACZ,eAAe,CAACa,gBAAgB,EAAE,CAACC,SAAS,EAAE,CAACC,IAAI,CAAEC,GAAG,IAAI;MAC/D,IAAGA,GAAG,EAAC;QACL,IAAI,CAACzB,cAAc,GAAGyB,GAAG,CAACC,IAAI,CAC3BC,OAAO,IAAKA,OAAO,CAACC,GAAG,IAAIpC,SAAS,CAACqC,aAAa,CAACC,OAAO,CAC5D;;IAEL,CAAC,CAAC,CAACC,OAAO,CAAC,MAAK;MACdrB,eAAe,CAACsB,OAAO,EAAE;IAC3B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA,GAAU;EAAC,QAAAC,CAAA;qBAxCR5B,sBAAsB,EAAAb,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,KAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBpC,sBAAsB;IAAAqC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXnCxD,EAAA,CAAA0D,SAAA,4BAAyE;QAEzE1D,EAAA,CAAA2D,UAAA,IAAAC,qCAAA,iBAQM;;;QAVc5D,EAAA,CAAAU,UAAA,kBAAA+C,GAAA,CAAAtC,aAAA,CAA+B;QAEhCnB,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAU,UAAA,SAAA+C,GAAA,CAAAlD,cAAA,CAAoB", "names": ["AppConfig", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "weather_policy", "value", "title", "ɵɵproperty", "content", "ɵɵsanitizeHtml", "WeatherPolicyComponent", "constructor", "_titleService", "settingsService", "_loadingService", "setTitle", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "link", "show", "getSettingNoAuth", "to<PERSON>romise", "then", "res", "find", "element", "key", "SETTINGS_KEYS", "WEATHER", "finally", "dismiss", "ngOnInit", "_", "ɵɵdirectiveInject", "i1", "Title", "i2", "SettingsService", "i3", "LoadingService", "_2", "selectors", "decls", "vars", "consts", "template", "WeatherPolicyComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "WeatherPolicyComponent_div_1_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\weather-policy\\weather-policy.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\weather-policy\\weather-policy.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { SettingsService } from 'app/services/settings.service';\r\n\r\n@Component({\r\n  selector: 'app-weather-policy',\r\n  templateUrl: './weather-policy.component.html',\r\n  styleUrls: ['./weather-policy.component.scss']\r\n})\r\nexport class WeatherPolicyComponent implements OnInit {\r\n  public contentHeader: object;\r\n  weather_policy: any;\r\n  constructor(\r\n    public _titleService: Title,\r\n    public settingsService: SettingsService,\r\n    public _loadingService: LoadingService,\r\n  ) { \r\n    this._titleService.setTitle('Weather Policy');\r\n    this.contentHeader = {\r\n      headerTitle: 'Weather Policy',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Home',\r\n            isLink: false,\r\n            link: '',\r\n          },\r\n          {\r\n            name: 'Weather Policy',\r\n            isLink: false,\r\n            link: '',\r\n          },\r\n        ],\r\n      },\r\n    };\r\n    _loadingService.show();\r\n    this.settingsService.getSettingNoAuth().toPromise().then((res) =>{\r\n      if(res){\r\n        this.weather_policy = res.find(\r\n          (element) => element.key == AppConfig.SETTINGS_KEYS.WEATHER\r\n        )\r\n      }\r\n    }).finally(() => {\r\n      _loadingService.dismiss();\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n}\r\n", "<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n<div class=\"card\" *ngIf=\"weather_policy\">\r\n    <div class=\"card-header\">\r\n        <h4 class=\"card-title\">{{weather_policy.value.title}}</h4>\r\n    </div>   \r\n\r\n    <div class=\"card-body\">\r\n        <div class=\"ck-content \" [innerHTML]=\"weather_policy.value.content\">></div>\r\n    </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
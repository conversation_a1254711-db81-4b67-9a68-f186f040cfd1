{"ast": null, "code": "import { coreConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"app/services/user.service\";\nimport * as i5 from \"app/services/tournament.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/flex-layout/extended\";\nimport * as i10 from \"@angular/forms\";\nfunction SelectRoleModalComponent_label_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 22)(1, \"div\", 23)(2, \"div\", 8)(3, \"div\", 9)(4, \"div\", 24)(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"plan-disabled\": a0\n  };\n};\nexport class SelectRoleModalComponent {\n  constructor(authService, router, activeModal, modalService, userService, tournamentService, loadingService, toast) {\n    this.authService = authService;\n    this.router = router;\n    this.activeModal = activeModal;\n    this.modalService = modalService;\n    this.userService = userService;\n    this.tournamentService = tournamentService;\n    this.loadingService = loadingService;\n    this.toast = toast;\n    this.items = [];\n    this.role = '';\n    this.coreConfig = coreConfig;\n    this.assignedUsers = [];\n    this.errorMessage = '';\n  }\n  close() {\n    this.activeModal.close();\n  }\n  goLive(match_id) {\n    this.router.navigate(['/streaming/broadcast', match_id]);\n  }\n  save() {\n    console.log(\"Selected Role:\", this.role);\n    console.log(\"save\", this.match.id, this.user_id, this.role);\n    this.tournamentService.assignRoleToUser({\n      match_id: this.match.id,\n      user_id: this.user_id.toString(),\n      role: this.role\n    }).subscribe(res => {\n      this.activeModal.close();\n      this.goLive(this.match.id);\n    }, error => {\n      this.errorMessage = error.message;\n    });\n  }\n  isRoleDisabled(role) {\n    if (this.assignedUsers.some(user => user.role === role)) {\n      return true;\n    }\n    if (role === 'camera' && this.assignedUsers.some(user => user.role === 'streamer')) {\n      return true;\n    }\n    if (role === 'streamer' && this.assignedUsers.some(user => user.role === 'control')) {\n      return true;\n    }\n    if (role === 'streamer' && this.assignedUsers.some(user => user.role === 'camera')) {\n      return true;\n    }\n    if (role === 'control' && this.assignedUsers.some(user => user.role === 'streamer')) {\n      return true;\n    }\n    if (role === 'streamer' && this.assignedUsers.some(user => user.role === 'streamer')) {\n      return true;\n    }\n    return false;\n  }\n  getAssignedUsers(match_id) {\n    this.loadingService.show();\n    this.tournamentService.getAssignedUsers(match_id).subscribe(res => {\n      if (res.data) {\n        this.assignedUsers = res.data;\n        console.log(this.assignedUsers, \"--\");\n      }\n    });\n  }\n  ngAfterViewInit() {\n    this.user_id = this.authService.currentUserValue.id;\n    this.getAssignedUsers(this.match.id);\n    console.log('ngAfterViewInit', this.match, this.user_id);\n  }\n  static #_ = this.ɵfac = function SelectRoleModalComponent_Factory(t) {\n    return new (t || SelectRoleModalComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NgbActiveModal), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.TournamentService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SelectRoleModalComponent,\n    selectors: [[\"app-select-role-modal\"]],\n    inputs: {\n      match: \"match\",\n      items: \"items\"\n    },\n    decls: 45,\n    vars: 16,\n    consts: [[1, \"modal-content\"], [1, \"p-2\", \"pt-3\"], [1, \"text-center\", \"mb-0\"], [1, \"modal-body\"], [1, \"plans\"], [\"for\", \"viewer\", 1, \"plan\", 3, \"ngClass\"], [\"type\", \"radio\", \"id\", \"viewer\", \"name\", \"plan\", \"value\", \"stream\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"plan-content\"], [1, \"plan-details\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [2, \"flex\", \"1\"], [1, \"svg-viewer\", 2, \"width\", \"68px\", \"height\", \"68px\"], [\"for\", \"cameraman\", 1, \"plan\", 3, \"ngClass\"], [\"type\", \"radio\", \"name\", \"plan\", \"id\", \"cameraman\", \"value\", \"camera\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"icon-cameraman\", 2, \"width\", \"68px\", \"height\", \"68px\"], [\"for\", \"controller\", 1, \"plan\", 3, \"ngClass\"], [\"type\", \"radio\", \"name\", \"plan\", \"id\", \"controller\", \"value\", \"control\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"svg-score\", 2, \"width\", \"68px\", \"height\", \"68px\"], [\"class\", \"plan\", 4, \"ngIf\"], [1, \"modal-footer\", \"justify-content-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"plan\"], [1, \"plan-content\", 2, \"border\", \"2px\", \"background\", \"var(--light-opacity-color-danger-danger-16, rgba(234, 84, 85, 0.16))\"], [2, \"flex\", \"1\", \"color\", \"red\"]],\n    template: function SelectRoleModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n        i0.ɵɵtext(3, \"Select Role\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"label\", 5)(7, \"input\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function SelectRoleModalComponent_Template_input_ngModelChange_7_listener($event) {\n          return ctx.role = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"span\");\n        i0.ɵɵtext(13, \"Record & Update\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"p\");\n        i0.ɵɵtext(15, \"You can both stream and update events for the match\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(16, \"div\", 11);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(17, \"label\", 12)(18, \"input\", 13);\n        i0.ɵɵlistener(\"ngModelChange\", function SelectRoleModalComponent_Template_input_ngModelChange_18_listener($event) {\n          return ctx.role = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 7)(20, \"div\", 8)(21, \"div\", 9)(22, \"div\", 10)(23, \"span\");\n        i0.ɵɵtext(24, \"Cameraman\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"p\");\n        i0.ɵɵtext(26, \"Use your camera to capture key moments.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(27, \"div\", 14);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(28, \"label\", 15)(29, \"input\", 16);\n        i0.ɵɵlistener(\"ngModelChange\", function SelectRoleModalComponent_Template_input_ngModelChange_29_listener($event) {\n          return ctx.role = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 7)(31, \"div\", 8)(32, \"div\", 9)(33, \"div\", 10)(34, \"span\");\n        i0.ɵɵtext(35, \"Controller\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"p\");\n        i0.ɵɵtext(37, \"You in a crucial role in providing match updates to viewers.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(38, \"div\", 17);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(39, SelectRoleModalComponent_label_39_Template, 7, 1, \"label\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 19)(41, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function SelectRoleModalComponent_Template_button_click_41_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵtext(42, \"Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"button\", 21);\n        i0.ɵɵlistener(\"click\", function SelectRoleModalComponent_Template_button_click_43_listener() {\n          return ctx.save();\n        });\n        i0.ɵɵtext(44, \"Select\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.isRoleDisabled(\"streamer\")));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.role)(\"disabled\", ctx.isRoleDisabled(\"streamer\"));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.isRoleDisabled(\"camera\")));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.role)(\"disabled\", ctx.isRoleDisabled(\"camera\"));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx.isRoleDisabled(\"control\")));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.role)(\"disabled\", ctx.isRoleDisabled(\"control\"));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage !== \"\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i9.DefaultClassDirective, i10.DefaultValueAccessor, i10.RadioControlValueAccessor, i10.NgControlStatus, i10.NgModel],\n    styles: [\".icon-cameraman[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10.934 8.78401C11.2147 9.06553 11.3721 9.44692 11.3717 9.84442C11.3713 10.2419 11.2132 10.623 10.932 10.904C9.2073 12.6205 7.83938 14.6614 6.90709 16.9091C5.9748 19.1567 5.49659 21.5667 5.50001 24C5.4966 26.4612 5.98591 28.8981 6.93909 31.1672C7.89227 33.4363 9.29004 35.4916 11.05 37.212C11.1939 37.3491 11.3092 37.5135 11.389 37.6955C11.4688 37.8776 11.5117 38.0737 11.5151 38.2724C11.5185 38.4712 11.4823 38.6687 11.4087 38.8533C11.3351 39.038 11.2256 39.2062 11.0864 39.3481C10.9473 39.4901 10.7813 39.6029 10.5981 39.6802C10.4149 39.7574 10.2182 39.7975 10.0195 39.798C9.82067 39.7986 9.62376 39.7596 9.44016 39.6834C9.25657 39.6071 9.08996 39.4952 8.95001 39.354C6.90514 37.3543 5.28103 34.9657 4.17331 32.3289C3.06559 29.692 2.49666 26.8601 2.50001 24C2.50001 18.056 4.91402 12.672 8.81202 8.78201C9.09353 8.50137 9.47493 8.34396 9.87243 8.34433C10.2699 8.34471 10.653 8.50285 10.934 8.78401ZM37.232 8.92801C37.5162 8.65001 37.8991 8.49617 38.2966 8.5003C38.6941 8.50442 39.0737 8.66617 39.352 8.95001C43.2976 12.965 45.5059 18.3708 45.5 24C45.5 29.928 43.1 35.3 39.218 39.188C38.9369 39.4697 38.5554 39.6281 38.1574 39.6285C37.7595 39.6289 37.3777 39.4711 37.096 39.19C36.8144 38.9089 36.6559 38.5274 36.6555 38.1294C36.6552 37.7315 36.8129 37.3497 37.094 37.068C38.8107 35.3527 40.1719 33.3153 41.0996 31.0729C42.0272 28.8304 42.5032 26.4268 42.5 24C42.5065 19.1564 40.6071 14.5046 37.212 11.05C36.9338 10.7661 36.7796 10.3834 36.7833 9.98587C36.7871 9.58837 36.9484 9.2066 37.232 8.92801ZM16.616 14.976C16.8878 15.2664 17.0332 15.6529 17.02 16.0505C17.0069 16.4481 16.8364 16.8241 16.546 17.096C14.648 18.872 13.498 21.3 13.498 23.964C13.498 26.66 14.676 29.114 16.614 30.896C16.9068 31.1655 17.0806 31.5402 17.0971 31.9378C17.1136 32.3354 16.9715 32.7232 16.702 33.016C16.4326 33.3088 16.0578 33.4826 15.6602 33.4991C15.2627 33.5156 14.8748 33.3735 14.582 33.104C12.078 30.802 10.498 27.564 10.498 23.964C10.498 20.406 12.04 17.204 14.498 14.904C14.7887 14.6324 15.1754 14.4875 15.5729 14.501C15.9705 14.5145 16.3464 14.6854 16.618 14.976M31.486 15.052C31.7609 14.7646 32.1385 14.5982 32.5361 14.5892C32.9336 14.5802 33.3185 14.7294 33.606 15.004C36 17.294 37.5 20.458 37.5 23.964C37.5 27.514 35.966 30.71 33.52 33.006C33.3772 33.1447 33.2082 33.2536 33.0229 33.3264C32.8376 33.3991 32.6397 33.4342 32.4406 33.4297C32.2416 33.4252 32.0455 33.381 31.8637 33.2999C31.6819 33.2188 31.518 33.1023 31.3817 32.9572C31.2454 32.8121 31.1393 32.6414 31.0696 32.4549C31 32.2684 30.9682 32.0699 30.976 31.871C30.9839 31.672 31.0312 31.4766 31.1154 31.2962C31.1995 31.1158 31.3187 30.9539 31.466 30.82C33.356 29.046 34.5 26.62 34.5 23.964C34.5 21.338 33.382 18.94 31.532 17.172C31.2449 16.8969 31.0788 16.5191 31.0702 16.1215C31.0616 15.724 31.2111 15.3393 31.486 15.052Z' fill='%234B465C'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10.934 8.78401C11.2147 9.06553 11.3721 9.44692 11.3717 9.84442C11.3713 10.2419 11.2132 10.623 10.932 10.904C9.2073 12.6205 7.83938 14.6614 6.90709 16.9091C5.9748 19.1567 5.49659 21.5667 5.50001 24C5.4966 26.4612 5.98591 28.8981 6.93909 31.1672C7.89227 33.4363 9.29004 35.4916 11.05 37.212C11.1939 37.3491 11.3092 37.5135 11.389 37.6955C11.4688 37.8776 11.5117 38.0737 11.5151 38.2724C11.5185 38.4712 11.4823 38.6687 11.4087 38.8533C11.3351 39.038 11.2256 39.2062 11.0864 39.3481C10.9473 39.4901 10.7813 39.6029 10.5981 39.6802C10.4149 39.7574 10.2182 39.7975 10.0195 39.798C9.82067 39.7986 9.62376 39.7596 9.44016 39.6834C9.25657 39.6071 9.08996 39.4952 8.95001 39.354C6.90514 37.3543 5.28103 34.9657 4.17331 32.3289C3.06559 29.692 2.49666 26.8601 2.50001 24C2.50001 18.056 4.91402 12.672 8.81202 8.78201C9.09353 8.50137 9.47493 8.34396 9.87243 8.34433C10.2699 8.34471 10.653 8.50285 10.934 8.78401ZM37.232 8.92801C37.5162 8.65001 37.8991 8.49617 38.2966 8.5003C38.6941 8.50442 39.0737 8.66617 39.352 8.95001C43.2976 12.965 45.5059 18.3708 45.5 24C45.5 29.928 43.1 35.3 39.218 39.188C38.9369 39.4697 38.5554 39.6281 38.1574 39.6285C37.7595 39.6289 37.3777 39.4711 37.096 39.19C36.8144 38.9089 36.6559 38.5274 36.6555 38.1294C36.6552 37.7315 36.8129 37.3497 37.094 37.068C38.8107 35.3527 40.1719 33.3153 41.0996 31.0729C42.0272 28.8304 42.5032 26.4268 42.5 24C42.5065 19.1564 40.6071 14.5046 37.212 11.05C36.9338 10.7661 36.7796 10.3834 36.7833 9.98587C36.7871 9.58837 36.9484 9.2066 37.232 8.92801ZM16.616 14.976C16.8878 15.2664 17.0332 15.6529 17.02 16.0505C17.0069 16.4481 16.8364 16.8241 16.546 17.096C14.648 18.872 13.498 21.3 13.498 23.964C13.498 26.66 14.676 29.114 16.614 30.896C16.9068 31.1655 17.0806 31.5402 17.0971 31.9378C17.1136 32.3354 16.9715 32.7232 16.702 33.016C16.4326 33.3088 16.0578 33.4826 15.6602 33.4991C15.2627 33.5156 14.8748 33.3735 14.582 33.104C12.078 30.802 10.498 27.564 10.498 23.964C10.498 20.406 12.04 17.204 14.498 14.904C14.7887 14.6324 15.1754 14.4875 15.5729 14.501C15.9705 14.5145 16.3464 14.6854 16.618 14.976M31.486 15.052C31.7609 14.7646 32.1385 14.5982 32.5361 14.5892C32.9336 14.5802 33.3185 14.7294 33.606 15.004C36 17.294 37.5 20.458 37.5 23.964C37.5 27.514 35.966 30.71 33.52 33.006C33.3772 33.1447 33.2082 33.2536 33.0229 33.3264C32.8376 33.3991 32.6397 33.4342 32.4406 33.4297C32.2416 33.4252 32.0455 33.381 31.8637 33.2999C31.6819 33.2188 31.518 33.1023 31.3817 32.9572C31.2454 32.8121 31.1393 32.6414 31.0696 32.4549C31 32.2684 30.9682 32.0699 30.976 31.871C30.9839 31.672 31.0312 31.4766 31.1154 31.2962C31.1995 31.1158 31.3187 30.9539 31.466 30.82C33.356 29.046 34.5 26.62 34.5 23.964C34.5 21.338 33.382 18.94 31.532 17.172C31.2449 16.8969 31.0788 16.5191 31.0702 16.1215C31.0616 15.724 31.2111 15.3393 31.486 15.052Z' fill='white' fill-opacity='0.3'/%3E%3Cpath d='M27.312 20.902C29.104 22.218 30 22.876 30 24.002C30 25.126 29.104 25.782 27.312 27.098C26.8445 27.444 26.3656 27.7741 25.876 28.088C25.482 28.338 25.034 28.596 24.57 28.85C22.782 29.83 21.89 30.318 21.088 29.776C20.286 29.234 20.214 28.1 20.068 25.834C20.028 25.194 20 24.564 20 24C20 23.436 20.026 22.808 20.068 22.166C20.212 19.9 20.286 18.766 21.088 18.226C21.888 17.684 22.782 18.172 24.568 19.15C25.034 19.404 25.482 19.662 25.876 19.912C26.328 20.198 26.818 20.54 27.312 20.902Z' fill='%234B465C'/%3E%3Cpath d='M27.312 20.902C29.104 22.218 30 22.876 30 24.002C30 25.126 29.104 25.782 27.312 27.098C26.8445 27.444 26.3656 27.7741 25.876 28.088C25.482 28.338 25.034 28.596 24.57 28.85C22.782 29.83 21.89 30.318 21.088 29.776C20.286 29.234 20.214 28.1 20.068 25.834C20.028 25.194 20 24.564 20 24C20 23.436 20.026 22.808 20.068 22.166C20.212 19.9 20.286 18.766 21.088 18.226C21.888 17.684 22.782 18.172 24.568 19.15C25.034 19.404 25.482 19.662 25.876 19.912C26.328 20.198 26.818 20.54 27.312 20.902Z' fill='white' fill-opacity='0.3'/%3E%3C/svg%3E%0A\\\");\\n  background-repeat: no-repeat no-repeat;\\n  background-position: center center;\\n  background-size: cover;\\n}\\n\\n.svg-score[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='68' height='68' viewBox='0 0 68 68' fill='none'%3E%3Cg clip-path='url(%23clip0_3495_4135)'%3E%3Cmask id='mask0_3495_4135' style='mask-type:luminance' maskUnits='userSpaceOnUse' x='0' y='0' width='68' height='68'%3E%3Cpath d='M0 0.000198364H67.9998V68H0V0.000198364Z' fill='white'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_3495_4135)'%3E%3Cpath d='M27.6482 57.6557H40.3515C41.0851 57.6557 41.6797 58.2503 41.6797 58.9838V65.6758C41.6797 66.4093 41.0851 67.0039 40.3515 67.0039H27.6482C26.9147 67.0039 26.3201 66.4093 26.3201 65.6758V58.9838C26.3201 58.2503 26.9147 57.6557 27.6482 57.6557Z' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M26.8097 42.8076H8.60302C7.50267 42.8076 6.61084 41.9158 6.61084 40.8154V17.4703C6.61084 16.3699 7.50267 15.4781 8.60302 15.4781H26.8097C27.9099 15.4781 28.8019 16.3699 28.8019 17.4703V40.8154C28.8019 41.9158 27.9099 42.8076 26.8097 42.8076Z' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M33.7376 25.5573C33.8072 25.6269 33.9016 25.666 34 25.666C34.0984 25.666 34.1928 25.6269 34.2624 25.5573C34.332 25.4877 34.3711 25.3933 34.3711 25.2949C34.3711 25.1965 34.332 25.1021 34.2624 25.0325C34.1928 24.9629 34.0984 24.9238 34 24.9238C33.9016 24.9238 33.8072 24.9629 33.7376 25.0325C33.668 25.1021 33.6289 25.1965 33.6289 25.2949C33.6289 25.3933 33.668 25.4877 33.7376 25.5573Z' stroke='%23000' stroke-width='1.25'/%3E%3Cpath d='M33.7376 33.2531C33.8072 33.3227 33.9016 33.3618 34 33.3618C34.0984 33.3618 34.1928 33.3227 34.2624 33.2531C34.332 33.1835 34.3711 33.0891 34.3711 32.9907C34.3711 32.8923 34.332 32.7979 34.2624 32.7283C34.1928 32.6587 34.0984 32.6196 34 32.6196C33.9016 32.6196 33.8072 32.6587 33.7376 32.7283C33.668 32.7979 33.6289 32.8923 33.6289 32.9907C33.6289 33.0891 33.668 33.1835 33.7376 33.2531Z' stroke='%23000' stroke-width='1.25'/%3E%3Cpath d='M12.6616 23.6189C13.0793 21.4929 14.9532 19.8889 17.2017 19.8889C19.757 19.8889 21.8286 21.9604 21.8286 24.5158C21.8286 27.0711 19.757 29.1426 17.2017 29.1426' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M17.2016 29.1427C19.7569 29.1427 21.8285 31.2143 21.8285 33.7696C21.8285 36.3249 19.7569 38.3965 17.2016 38.3965C14.8653 38.3965 12.9333 36.6647 12.6195 34.4149C12.5896 34.2011 12.5747 33.9855 12.5747 33.7696' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M47.7964 20.2939H51.6614V38.376' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M39.198 52.0932V57.5229' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M28.802 57.5229V52.056' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M61.389 37.9235V40.8154C61.389 41.9158 60.4971 42.8076 59.3969 42.8076H41.1902C40.09 42.8076 39.198 41.9158 39.198 40.8154V17.4703C39.198 16.3699 40.09 15.4781 41.1902 15.4781H59.3969C60.4971 15.4781 61.389 16.3699 61.389 17.4703V33.5824' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M31.8294 11.9199H21.6887C20.5885 11.9199 19.6965 11.028 19.6965 9.92774V2.98844C19.6965 1.88822 20.5885 0.996258 21.6887 0.996258H46.3114C47.4116 0.996258 48.3036 1.88822 48.3036 2.98844V9.92774C48.3036 11.028 47.4116 11.9199 46.3114 11.9199H36.1707' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M13.2375 6.45801H19.4307' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M48.4362 6.45822H63.6834C65.517 6.45822 67.0037 7.94479 67.0037 9.77853V48.5073C67.0037 50.3411 65.517 51.8276 63.6834 51.8276H4.3164C2.48266 51.8276 0.996094 50.3411 0.996094 48.5073V9.77853C0.996094 7.94479 2.48266 6.45822 4.3164 6.45822H8.89642' stroke='%23000' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/g%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_3495_4135'%3E%3Crect width='68' height='68' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat no-repeat;\\n  background-position: center center;\\n  background-size: cover;\\n}\\n\\n.svg-viewer[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_5151_24269)'%3E%3Cpath d='M44.0025 44.7996V31.9982C44.0025 30.2307 42.5697 28.7979 40.8021 28.7979H35.7367C38.8321 25.7994 40.6417 21.7158 40.7836 17.4086C40.802 17.2014 40.802 17.0037 40.802 16.7965C40.7941 9.73243 36.3761 3.42494 29.74 1.00358C26.0315 -0.334566 21.972 -0.334566 18.2635 1.00358C11.626 3.42359 7.20635 9.73156 7.19843 16.7966C7.19843 17.0038 7.19843 17.203 7.21523 17.3663C7.34535 21.6888 9.15662 25.7901 12.2638 28.7979H7.19843C5.43096 28.7979 3.99805 30.2308 3.99805 31.9983V44.7996C3.99805 46.5671 5.43088 48 7.19843 48H40.8021C42.5697 48 44.0025 46.5672 44.0025 44.7996ZM30.3538 28.7979L31.1603 26.3976H35.7744C35.0489 27.2884 34.2243 28.0937 33.3165 28.7979H30.3538ZM19.335 28.7979L18.3028 25.7271L20.6527 22.3972H27.3479L29.6978 25.7271L28.6657 28.7979H19.335ZM24.0003 11.3736L29.1401 14.9876L27.1854 20.7971H20.8151L18.8605 14.9876L24.0003 11.3736ZM36.9145 24.7975H30.9995L28.6488 21.4659L30.7154 15.3236L34.4902 14.213L39.1603 17.7206C39.0139 20.23 38.2418 22.6629 36.9145 24.7975ZM36.8969 8.77005C38.2132 10.8636 38.99 13.2508 39.1579 15.7181L35.58 13.0305L36.8969 8.77005ZM35.7247 7.14189L34.0118 12.6857L30.2306 13.7987L24.8004 9.98069V6.01136L29.5705 2.659C31.9723 3.60096 34.0916 5.14469 35.7247 7.14189ZM27.6551 2.04932L24.0003 4.61759L20.3454 2.04852C22.7467 1.46153 25.254 1.4618 27.6551 2.04932ZM18.43 2.659L23.2002 6.01136V9.98062L17.77 13.7986L13.9887 12.6857L12.2758 7.14102C13.9093 5.14442 16.0285 3.60107 18.43 2.659ZM12.4206 13.0305L8.8426 15.7181C9.0102 13.2505 9.78707 10.863 11.1037 8.76926L12.4206 13.0305ZM8.84101 17.7198L13.5104 14.213L17.2852 15.3236L19.3518 21.4659L17.0011 24.7975H11.0861C9.76084 22.6616 8.98921 20.229 8.84101 17.7198ZM12.2262 26.3976H16.8403L17.6468 28.7979H14.6865C13.7778 28.0938 12.9524 27.2885 12.2262 26.3976ZM5.59824 44.7996V31.9982C5.59824 31.1145 6.31465 30.3981 7.19843 30.3981H40.8021C41.6859 30.3981 42.4023 31.1145 42.4023 31.9982V44.7996C42.4023 45.6834 41.6859 46.3998 40.8021 46.3998H7.19843C6.31465 46.3998 5.59824 45.6834 5.59824 44.7996Z' fill='%234B465C'/%3E%3Cpath d='M44.0025 44.7996V31.9982C44.0025 30.2307 42.5697 28.7979 40.8021 28.7979H35.7367C38.8321 25.7994 40.6417 21.7158 40.7836 17.4086C40.802 17.2014 40.802 17.0037 40.802 16.7965C40.7941 9.73243 36.3761 3.42494 29.74 1.00358C26.0315 -0.334566 21.972 -0.334566 18.2635 1.00358C11.626 3.42359 7.20635 9.73156 7.19843 16.7966C7.19843 17.0038 7.19843 17.203 7.21523 17.3663C7.34535 21.6888 9.15662 25.7901 12.2638 28.7979H7.19843C5.43096 28.7979 3.99805 30.2308 3.99805 31.9983V44.7996C3.99805 46.5671 5.43088 48 7.19843 48H40.8021C42.5697 48 44.0025 46.5672 44.0025 44.7996ZM30.3538 28.7979L31.1603 26.3976H35.7744C35.0489 27.2884 34.2243 28.0937 33.3165 28.7979H30.3538ZM19.335 28.7979L18.3028 25.7271L20.6527 22.3972H27.3479L29.6978 25.7271L28.6657 28.7979H19.335ZM24.0003 11.3736L29.1401 14.9876L27.1854 20.7971H20.8151L18.8605 14.9876L24.0003 11.3736ZM36.9145 24.7975H30.9995L28.6488 21.4659L30.7154 15.3236L34.4902 14.213L39.1603 17.7206C39.0139 20.23 38.2418 22.6629 36.9145 24.7975ZM36.8969 8.77005C38.2132 10.8636 38.99 13.2508 39.1579 15.7181L35.58 13.0305L36.8969 8.77005ZM35.7247 7.14189L34.0118 12.6857L30.2306 13.7987L24.8004 9.98069V6.01136L29.5705 2.659C31.9723 3.60096 34.0916 5.14469 35.7247 7.14189ZM27.6551 2.04932L24.0003 4.61759L20.3454 2.04852C22.7467 1.46153 25.254 1.4618 27.6551 2.04932ZM18.43 2.659L23.2002 6.01136V9.98062L17.77 13.7986L13.9887 12.6857L12.2758 7.14102C13.9093 5.14442 16.0285 3.60107 18.43 2.659ZM12.4206 13.0305L8.8426 15.7181C9.0102 13.2505 9.78707 10.863 11.1037 8.76926L12.4206 13.0305ZM8.84101 17.7198L13.5104 14.213L17.2852 15.3236L19.3518 21.4659L17.0011 24.7975H11.0861C9.76084 22.6616 8.98921 20.229 8.84101 17.7198ZM12.2262 26.3976H16.8403L17.6468 28.7979H14.6865C13.7778 28.0938 12.9524 27.2885 12.2262 26.3976ZM5.59824 44.7996V31.9982C5.59824 31.1145 6.31465 30.3981 7.19843 30.3981H40.8021C41.6859 30.3981 42.4023 31.1145 42.4023 31.9982V44.7996C42.4023 45.6834 41.6859 46.3998 40.8021 46.3998H7.19843C6.31465 46.3998 5.59824 45.6834 5.59824 44.7996Z' fill='white' fill-opacity='0.3'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M18.3997 33.5984C17.9578 33.5984 17.5996 33.9567 17.5996 34.3985V42.3994C17.5996 42.8413 17.9578 43.1995 18.3997 43.1995C18.8415 43.1995 19.1998 42.8413 19.1998 42.3994V34.3985C19.1998 33.9566 18.8415 33.5984 18.3997 33.5984ZM29.0985 33.6553C29.0009 33.6162 28.8966 33.5968 28.7915 33.598C28.6865 33.5993 28.5827 33.6212 28.4861 33.6626C28.3895 33.704 28.302 33.764 28.2286 33.8392C28.1552 33.9144 28.0974 34.0033 28.0583 34.1009L25.6005 40.2456L23.1434 34.1009C22.979 33.6904 22.5129 33.4909 22.1024 33.6553C21.692 33.8196 21.4924 34.2857 21.6568 34.6962L24.8572 42.697C24.9164 42.8458 25.0189 42.9733 25.1514 43.0631C25.2839 43.153 25.4404 43.201 25.6005 43.201C25.7605 43.201 25.917 43.153 26.0495 43.0631C26.182 42.9733 26.2845 42.8458 26.3437 42.697L29.5441 34.6962C29.5833 34.5986 29.6028 34.4942 29.6016 34.389C29.6004 34.2839 29.5785 34.18 29.5371 34.0833C29.4957 33.9866 29.4356 33.8991 29.3604 33.8256C29.2851 33.7522 29.1961 33.6943 29.0985 33.6553ZM37.6018 35.1986C38.0436 35.1986 38.4019 34.8403 38.4019 34.3985C38.4019 33.9566 38.0436 33.5984 37.6018 33.5984H32.8012C32.3594 33.5984 32.0011 33.9566 32.0011 34.3985V42.3994C32.0011 42.8413 32.3594 43.1995 32.8012 43.1995H37.6017C38.0436 43.1995 38.4018 42.8413 38.4018 42.3994C38.4018 41.9576 38.0436 41.5993 37.6017 41.5993H33.6012V39.199H37.6017C38.0436 39.199 38.4018 38.8408 38.4018 38.3989C38.4018 37.9571 38.0436 37.5988 37.6017 37.5988H33.6012V35.1986H37.6018ZM15.1993 41.5993H11.1988V34.3985C11.1988 33.9567 10.8406 33.5984 10.3987 33.5984C9.95688 33.5984 9.59863 33.9567 9.59863 34.3985V42.3994C9.59863 42.8413 9.95688 43.1995 10.3987 43.1995H15.1992C15.6411 43.1995 15.9993 42.8413 15.9993 42.3994C15.9994 41.9575 15.6411 41.5993 15.1993 41.5993Z' fill='%234B465C'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M18.3997 33.5984C17.9578 33.5984 17.5996 33.9567 17.5996 34.3985V42.3994C17.5996 42.8413 17.9578 43.1995 18.3997 43.1995C18.8415 43.1995 19.1998 42.8413 19.1998 42.3994V34.3985C19.1998 33.9566 18.8415 33.5984 18.3997 33.5984ZM29.0985 33.6553C29.0009 33.6162 28.8966 33.5968 28.7915 33.598C28.6865 33.5993 28.5827 33.6212 28.4861 33.6626C28.3895 33.704 28.302 33.764 28.2286 33.8392C28.1552 33.9144 28.0974 34.0033 28.0583 34.1009L25.6005 40.2456L23.1434 34.1009C22.979 33.6904 22.5129 33.4909 22.1024 33.6553C21.692 33.8196 21.4924 34.2857 21.6568 34.6962L24.8572 42.697C24.9164 42.8458 25.0189 42.9733 25.1514 43.0631C25.2839 43.153 25.4404 43.201 25.6005 43.201C25.7605 43.201 25.917 43.153 26.0495 43.0631C26.182 42.9733 26.2845 42.8458 26.3437 42.697L29.5441 34.6962C29.5833 34.5986 29.6028 34.4942 29.6016 34.389C29.6004 34.2839 29.5785 34.18 29.5371 34.0833C29.4957 33.9866 29.4356 33.8991 29.3604 33.8256C29.2851 33.7522 29.1961 33.6943 29.0985 33.6553ZM37.6018 35.1986C38.0436 35.1986 38.4019 34.8403 38.4019 34.3985C38.4019 33.9566 38.0436 33.5984 37.6018 33.5984H32.8012C32.3594 33.5984 32.0011 33.9566 32.0011 34.3985V42.3994C32.0011 42.8413 32.3594 43.1995 32.8012 43.1995H37.6017C38.0436 43.1995 38.4018 42.8413 38.4018 42.3994C38.4018 41.9576 38.0436 41.5993 37.6017 41.5993H33.6012V39.199H37.6017C38.0436 39.199 38.4018 38.8408 38.4018 38.3989C38.4018 37.9571 38.0436 37.5988 37.6017 37.5988H33.6012V35.1986H37.6018ZM15.1993 41.5993H11.1988V34.3985C11.1988 33.9567 10.8406 33.5984 10.3987 33.5984C9.95688 33.5984 9.59863 33.9567 9.59863 34.3985V42.3994C9.59863 42.8413 9.95688 43.1995 10.3987 43.1995H15.1992C15.6411 43.1995 15.9993 42.8413 15.9993 42.3994C15.9994 41.9575 15.6411 41.5993 15.1993 41.5993Z' fill='white' fill-opacity='0.3'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_5151_24269'%3E%3Crect width='48' height='48' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A\\\");\\n  background-repeat: no-repeat no-repeat;\\n  background-position: center center;\\n  background-size: cover;\\n}\\n\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  width: 97%;\\n  margin: 5px;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .plan-content[_ngcontent-%COMP%] {\\n  border: 2px solid #0d6efd;\\n  background: #eaf1fe;\\n  transition: ease-in 0.3s;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .plan-content[_ngcontent-%COMP%]   .plan-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .plan-content[_ngcontent-%COMP%]   .plan-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .plan-content[_ngcontent-%COMP%]   .icon-cameraman[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10.934 8.78401C11.2147 9.06553 11.3721 9.44692 11.3717 9.84442C11.3713 10.2419 11.2132 10.623 10.932 10.904C9.2073 12.6205 7.83938 14.6614 6.90709 16.9091C5.9748 19.1567 5.49659 21.5667 5.50001 24C5.4966 26.4612 5.98591 28.8981 6.93909 31.1672C7.89227 33.4363 9.29004 35.4916 11.05 37.212C11.1939 37.3491 11.3092 37.5135 11.389 37.6955C11.4688 37.8776 11.5117 38.0737 11.5151 38.2724C11.5185 38.4712 11.4823 38.6687 11.4087 38.8533C11.3351 39.038 11.2256 39.2062 11.0864 39.3481C10.9473 39.4901 10.7813 39.6029 10.5981 39.6802C10.4149 39.7574 10.2182 39.7975 10.0195 39.798C9.82067 39.7986 9.62376 39.7596 9.44016 39.6834C9.25657 39.6071 9.08996 39.4952 8.95001 39.354C6.90514 37.3543 5.28103 34.9657 4.17331 32.3289C3.06559 29.692 2.49666 26.8601 2.50001 24C2.50001 18.056 4.91402 12.672 8.81202 8.78201C9.09353 8.50137 9.47493 8.34396 9.87243 8.34433C10.2699 8.34471 10.653 8.50285 10.934 8.78401ZM37.232 8.92801C37.5162 8.65001 37.8991 8.49617 38.2966 8.5003C38.6941 8.50442 39.0737 8.66617 39.352 8.95001C43.2976 12.965 45.5059 18.3708 45.5 24C45.5 29.928 43.1 35.3 39.218 39.188C38.9369 39.4697 38.5554 39.6281 38.1574 39.6285C37.7595 39.6289 37.3777 39.4711 37.096 39.19C36.8144 38.9089 36.6559 38.5274 36.6555 38.1294C36.6552 37.7315 36.8129 37.3497 37.094 37.068C38.8107 35.3527 40.1719 33.3153 41.0996 31.0729C42.0272 28.8304 42.5032 26.4268 42.5 24C42.5065 19.1564 40.6071 14.5046 37.212 11.05C36.9338 10.7661 36.7796 10.3834 36.7833 9.98587C36.7871 9.58837 36.9484 9.2066 37.232 8.92801ZM16.616 14.976C16.8878 15.2664 17.0332 15.6529 17.02 16.0505C17.0069 16.4481 16.8364 16.8241 16.546 17.096C14.648 18.872 13.498 21.3 13.498 23.964C13.498 26.66 14.676 29.114 16.614 30.896C16.9068 31.1655 17.0806 31.5402 17.0971 31.9378C17.1136 32.3354 16.9715 32.7232 16.702 33.016C16.4326 33.3088 16.0578 33.4826 15.6602 33.4991C15.2627 33.5156 14.8748 33.3735 14.582 33.104C12.078 30.802 10.498 27.564 10.498 23.964C10.498 20.406 12.04 17.204 14.498 14.904C14.7887 14.6324 15.1754 14.4875 15.5729 14.501C15.9705 14.5145 16.3464 14.6854 16.618 14.976M31.486 15.052C31.7609 14.7646 32.1385 14.5982 32.5361 14.5892C32.9336 14.5802 33.3185 14.7294 33.606 15.004C36 17.294 37.5 20.458 37.5 23.964C37.5 27.514 35.966 30.71 33.52 33.006C33.3772 33.1447 33.2082 33.2536 33.0229 33.3264C32.8376 33.3991 32.6397 33.4342 32.4406 33.4297C32.2416 33.4252 32.0455 33.381 31.8637 33.2999C31.6819 33.2188 31.518 33.1023 31.3817 32.9572C31.2454 32.8121 31.1393 32.6414 31.0696 32.4549C31 32.2684 30.9682 32.0699 30.976 31.871C30.9839 31.672 31.0312 31.4767 31.1154 31.2962C31.1995 31.1158 31.3187 30.9539 31.466 30.82C33.356 29.046 34.5 26.62 34.5 23.964C34.5 21.338 33.382 18.94 31.532 17.172C31.2449 16.8969 31.0788 16.5191 31.0702 16.1215C31.0616 15.724 31.2111 15.3393 31.486 15.052Z' fill='%232D67F1'/%3E%3Cpath d='M27.312 20.902C29.104 22.218 30 22.876 30 24.002C30 25.126 29.104 25.782 27.312 27.098C26.8445 27.444 26.3656 27.7741 25.876 28.088C25.482 28.338 25.034 28.596 24.57 28.85C22.782 29.83 21.89 30.318 21.088 29.776C20.286 29.234 20.214 28.1 20.068 25.834C20.028 25.194 20 24.564 20 24C20 23.436 20.026 22.808 20.068 22.166C20.212 19.9 20.286 18.766 21.088 18.226C21.888 17.684 22.782 18.172 24.568 19.15C25.034 19.404 25.482 19.662 25.876 19.912C26.328 20.198 26.818 20.54 27.312 20.902Z' fill='%232D67F1'/%3E%3C/svg%3E%0A\\\");\\n  background-repeat: no-repeat no-repeat;\\n  background-position: center center;\\n  background-size: cover;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .plan-content[_ngcontent-%COMP%]   .svg-score[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='68' height='68' viewBox='0 0 68 68' fill='none'%3E%3Cg clip-path='url(%23clip0_3495_4135)'%3E%3Cmask id='mask0_3495_4135' style='mask-type:luminance' maskUnits='userSpaceOnUse' x='0' y='0' width='68' height='68'%3E%3Cpath d='M0 0.000198364H67.9998V68H0V0.000198364Z' fill='white'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_3495_4135)'%3E%3Cpath d='M27.6482 57.6557H40.3515C41.0851 57.6557 41.6797 58.2503 41.6797 58.9838V65.6758C41.6797 66.4093 41.0851 67.0039 40.3515 67.0039H27.6482C26.9147 67.0039 26.3201 66.4093 26.3201 65.6758V58.9838C26.3201 58.2503 26.9147 57.6557 27.6482 57.6557Z' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M26.8097 42.8076H8.60302C7.50267 42.8076 6.61084 41.9158 6.61084 40.8154V17.4703C6.61084 16.3699 7.50267 15.4781 8.60302 15.4781H26.8097C27.9099 15.4781 28.8019 16.3699 28.8019 17.4703V40.8154C28.8019 41.9158 27.9099 42.8076 26.8097 42.8076Z' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M33.7376 25.5573C33.8072 25.6269 33.9016 25.666 34 25.666C34.0984 25.666 34.1928 25.6269 34.2624 25.5573C34.332 25.4877 34.3711 25.3933 34.3711 25.2949C34.3711 25.1965 34.332 25.1021 34.2624 25.0325C34.1928 24.9629 34.0984 24.9238 34 24.9238C33.9016 24.9238 33.8072 24.9629 33.7376 25.0325C33.668 25.1021 33.6289 25.1965 33.6289 25.2949C33.6289 25.3933 33.668 25.4877 33.7376 25.5573Z' stroke='%232D67F1' stroke-width='1.25'/%3E%3Cpath d='M33.7376 33.2531C33.8072 33.3227 33.9016 33.3618 34 33.3618C34.0984 33.3618 34.1928 33.3227 34.2624 33.2531C34.332 33.1835 34.3711 33.0891 34.3711 32.9907C34.3711 32.8923 34.332 32.7979 34.2624 32.7283C34.1928 32.6587 34.0984 32.6196 34 32.6196C33.9016 32.6196 33.8072 32.6587 33.7376 32.7283C33.668 32.7979 33.6289 32.8923 33.6289 32.9907C33.6289 33.0891 33.668 33.1835 33.7376 33.2531Z' stroke='%232D67F1' stroke-width='1.25'/%3E%3Cpath d='M12.6616 23.6189C13.0793 21.4929 14.9532 19.8889 17.2017 19.8889C19.757 19.8889 21.8286 21.9604 21.8286 24.5158C21.8286 27.0711 19.757 29.1426 17.2017 29.1426' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M17.2016 29.1427C19.7569 29.1427 21.8285 31.2143 21.8285 33.7696C21.8285 36.3249 19.7569 38.3965 17.2016 38.3965C14.8653 38.3965 12.9333 36.6647 12.6195 34.4149C12.5896 34.2011 12.5747 33.9855 12.5747 33.7696' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M47.7964 20.2939H51.6614V38.376' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M39.198 52.0932V57.5229' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M28.802 57.5229V52.056' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M61.389 37.9235V40.8154C61.389 41.9158 60.4971 42.8076 59.3969 42.8076H41.1902C40.09 42.8076 39.198 41.9158 39.198 40.8154V17.4703C39.198 16.3699 40.09 15.4781 41.1902 15.4781H59.3969C60.4971 15.4781 61.389 16.3699 61.389 17.4703V33.5824' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M31.8294 11.9199H21.6887C20.5885 11.9199 19.6965 11.028 19.6965 9.92774V2.98844C19.6965 1.88822 20.5885 0.996258 21.6887 0.996258H46.3114C47.4116 0.996258 48.3036 1.88822 48.3036 2.98844V9.92774C48.3036 11.028 47.4116 11.9199 46.3114 11.9199H36.1707' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M13.2375 6.45801H19.4307' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M48.4362 6.45822H63.6834C65.517 6.45822 67.0037 7.94479 67.0037 9.77853V48.5073C67.0037 50.3411 65.517 51.8276 63.6834 51.8276H4.3164C2.48266 51.8276 0.996094 50.3411 0.996094 48.5073V9.77853C0.996094 7.94479 2.48266 6.45822 4.3164 6.45822H8.89642' stroke='%232D67F1' stroke-width='1.25' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/g%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_3495_4135'%3E%3Crect width='68' height='68' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat no-repeat;\\n  background-position: center center;\\n  background-size: cover;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked    + .plan-content[_ngcontent-%COMP%]   .svg-viewer[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_5204_12611)'%3E%3Cpath d='M44.0025 44.7996V31.9983C44.0025 30.2307 42.5697 28.7979 40.8021 28.7979H35.7367C38.8321 25.7995 40.6417 21.7158 40.7836 17.4086C40.802 17.2014 40.802 17.0038 40.802 16.7966C40.7941 9.73246 36.3761 3.42497 29.74 1.00361C26.0315 -0.334535 21.972 -0.334535 18.2635 1.00361C11.626 3.42362 7.20635 9.73159 7.19843 16.7966C7.19843 17.0038 7.19843 17.2031 7.21523 17.3663C7.34535 21.6889 9.15662 25.7902 12.2638 28.798H7.19843C5.43096 28.798 3.99805 30.2308 3.99805 31.9984V44.7996C3.99805 46.5671 5.43088 48 7.19843 48H40.8021C42.5697 48 44.0025 46.5672 44.0025 44.7996ZM30.3538 28.7979L31.1603 26.3976H35.7744C35.0489 27.2884 34.2243 28.0937 33.3165 28.7979H30.3538ZM19.335 28.7979L18.3028 25.7272L20.6527 22.3972H27.3479L29.6978 25.7272L28.6657 28.7979H19.335ZM24.0003 11.3736L29.1401 14.9876L27.1854 20.7971H20.8151L18.8605 14.9876L24.0003 11.3736ZM36.9145 24.7975H30.9995L28.6488 21.4659L30.7154 15.3236L34.4902 14.2131L39.1603 17.7207C39.0139 20.23 38.2418 22.6629 36.9145 24.7975ZM36.8969 8.77008C38.2132 10.8636 38.99 13.2508 39.1579 15.7181L35.58 13.0305L36.8969 8.77008ZM35.7247 7.14192L34.0118 12.6858L30.2306 13.7987L24.8004 9.98073V6.01139L29.5705 2.65903C31.9723 3.601 34.0916 5.14472 35.7247 7.14192ZM27.6551 2.04935L24.0003 4.61762L20.3454 2.04856C22.7467 1.46156 25.254 1.46183 27.6551 2.04935ZM18.43 2.65903L23.2002 6.01139V9.98065L17.77 13.7986L13.9887 12.6857L12.2758 7.14105C13.9093 5.14445 16.0285 3.6011 18.43 2.65903ZM12.4206 13.0305L8.8426 15.7181C9.0102 13.2505 9.78707 10.863 11.1037 8.76929L12.4206 13.0305ZM8.84101 17.7199L13.5104 14.2131L17.2852 15.3236L19.3518 21.4659L17.0011 24.7975H11.0861C9.76084 22.6617 8.98921 20.2291 8.84101 17.7199ZM12.2262 26.3976H16.8403L17.6468 28.7979H14.6865C13.7778 28.0939 12.9524 27.2886 12.2262 26.3976ZM5.59824 44.7996V31.9983C5.59824 31.1145 6.31465 30.3981 7.19843 30.3981H40.8021C41.6859 30.3981 42.4023 31.1145 42.4023 31.9983V44.7996C42.4023 45.6834 41.6859 46.3998 40.8021 46.3998H7.19843C6.31465 46.3998 5.59824 45.6834 5.59824 44.7996Z' fill='%232D67F1'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M18.3997 33.5985C17.9578 33.5985 17.5996 33.9567 17.5996 34.3986V42.3994C17.5996 42.8413 17.9578 43.1995 18.3997 43.1995C18.8415 43.1995 19.1998 42.8413 19.1998 42.3994V34.3986C19.1998 33.9566 18.8415 33.5985 18.3997 33.5985ZM29.0985 33.6553C29.0009 33.6162 28.8966 33.5968 28.7915 33.5981C28.6865 33.5993 28.5827 33.6213 28.4861 33.6627C28.3895 33.704 28.302 33.764 28.2286 33.8392C28.1552 33.9144 28.0974 34.0034 28.0583 34.1009L25.6005 40.2456L23.1434 34.1009C22.979 33.6905 22.5129 33.4909 22.1024 33.6553C21.692 33.8196 21.4924 34.2857 21.6568 34.6962L24.8572 42.6971C24.9164 42.8458 25.0189 42.9733 25.1514 43.0632C25.2839 43.153 25.4404 43.201 25.6005 43.201C25.7605 43.201 25.917 43.153 26.0495 43.0632C26.182 42.9733 26.2845 42.8458 26.3437 42.6971L29.5441 34.6962C29.5833 34.5986 29.6028 34.4942 29.6016 34.3891C29.6004 34.2839 29.5785 34.18 29.5371 34.0834C29.4957 33.9867 29.4356 33.8991 29.3604 33.8257C29.2851 33.7522 29.1961 33.6943 29.0985 33.6553ZM37.6018 35.1986C38.0437 35.1986 38.4019 34.8403 38.4019 34.3985C38.4019 33.9566 38.0437 33.5984 37.6018 33.5984H32.8012C32.3594 33.5984 32.0011 33.9566 32.0011 34.3985V42.3994C32.0011 42.8413 32.3594 43.1995 32.8012 43.1995H37.6017C38.0436 43.1995 38.4018 42.8413 38.4018 42.3994C38.4018 41.9576 38.0436 41.5993 37.6017 41.5993H33.6012V39.1991H37.6017C38.0436 39.1991 38.4018 38.8408 38.4018 38.399C38.4018 37.9571 38.0436 37.5989 37.6017 37.5989H33.6012V35.1986H37.6018ZM15.1993 41.5993H11.1988V34.3986C11.1988 33.9567 10.8406 33.5985 10.3987 33.5985C9.95688 33.5985 9.59863 33.9567 9.59863 34.3986V42.3994C9.59863 42.8413 9.95688 43.1995 10.3987 43.1995H15.1992C15.6411 43.1995 15.9993 42.8413 15.9993 42.3994C15.9994 41.9575 15.6411 41.5993 15.1993 41.5993Z' fill='%232D67F1'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_5204_12611'%3E%3Crect width='48' height='48' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A\\\");\\n  background-repeat: no-repeat no-repeat;\\n  background-position: center center;\\n  background-size: cover;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   .plan-content[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  box-sizing: border-box;\\n  border: 2px solid #e1e2e7;\\n  border-radius: 10px;\\n  transition: box-shadow 0.4s;\\n  position: relative;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   .plan-content[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  margin-right: 30px;\\n  height: 72px;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   .plan-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0px 3px 5px 0px #e8e8e8;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   .plan-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  display: block;\\n  font-size: 20px;\\n  line-height: 24px;\\n  color: #252f42;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   .plan-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #646a79;\\n  font-size: 14px;\\n  line-height: 18px;\\n}\\n.plans[_ngcontent-%COMP%]   .plan[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  flex-basis: 100%;\\n  color: #252f42;\\n  margin-bottom: 20px;\\n}\\n\\n.plan-disabled[_ngcontent-%COMP%] {\\n  background-color: var(--Secondary-Color-400, linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%), #d2d4d7);\\n  color: white;\\n  opacity: 0.6;\\n  pointer-events: none;\\n  border: 2px solid var(--Secondary-Color-400, linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%), #d2d4d7);\\n  cursor: not-allowed;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;IC+C3BC,EAAA,CAAAC,cAAA,gBAAgD;IAKzBD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAgB;;;;;;;;ADxCvD,OAAM,MAAOC,wBAAwB;EAQnCC,YACSC,WAAwB,EACvBC,MAAc,EACfC,WAA2B,EAC3BC,YAAsB,EACtBC,WAAwB,EACxBC,iBAAoC,EACpCC,cAA8B,EAC9BC,KAAoB;IAPpB,KAAAP,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IAbL,KAAAC,KAAK,GAAQ,EAAE;IACxB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAApB,UAAU,GAAGA,UAAU;IACvB,KAAAqB,aAAa,GAAU,EAAE;IACzB,KAAAb,YAAY,GAAW,EAAE;EAWzB;EAEAc,KAAKA,CAAA;IACH,IAAI,CAACT,WAAW,CAACS,KAAK,EAAE;EAC1B;EAEAC,MAAMA,CAACC,QAAQ;IACb,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,sBAAsB,EAAED,QAAQ,CAAC,CAAC;EAC1D;EAEAE,IAAIA,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACR,IAAI,CAAC;IACxCO,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,KAAK,CAACC,EAAE,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACX,IAAI,CAAC;IAE3D,IAAI,CAACJ,iBAAiB,CAACgB,gBAAgB,CAAC;MACtCR,QAAQ,EAAE,IAAI,CAACK,KAAK,CAACC,EAAE;MACvBC,OAAO,EAAE,IAAI,CAACA,OAAO,CAACE,QAAQ,EAAE;MAChCb,IAAI,EAAE,IAAI,CAACA;KACZ,CAAC,CAACc,SAAS,CAAEC,GAAQ,IAAI;MACxB,IAAI,CAACtB,WAAW,CAACS,KAAK,EAAE;MACxB,IAAI,CAACC,MAAM,CAAC,IAAI,CAACM,KAAK,CAACC,EAAE,CAAC;IAC5B,CAAC,EACAM,KAAU,IAAI;MACb,IAAI,CAAC5B,YAAY,GAAG4B,KAAK,CAACC,OAAO;IACnC,CAAC,CACF;EAAA;EAEDC,cAAcA,CAAClB,IAAY;IACzB,IAAI,IAAI,CAACC,aAAa,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAKA,IAAI,CAAC,EAAE;MACvD,OAAO,IAAI;;IAEb,IAAIA,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACC,aAAa,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAK,UAAU,CAAC,EAAE;MAClF,OAAO,IAAI;;IAEb,IAAIA,IAAI,KAAK,UAAU,IAAI,IAAI,CAACC,aAAa,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAK,SAAS,CAAC,EAAE;MACnF,OAAO,IAAI;;IAEb,IAAIA,IAAI,KAAK,UAAU,IAAI,IAAI,CAACC,aAAa,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAK,QAAQ,CAAC,EAAE;MAClF,OAAO,IAAI;;IAEb,IAAIA,IAAI,KAAK,SAAS,IAAI,IAAI,CAACC,aAAa,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAK,UAAU,CAAC,EAAE;MACnF,OAAO,IAAI;;IAEb,IAAIA,IAAI,KAAK,UAAU,IAAI,IAAI,CAACC,aAAa,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpB,IAAI,KAAK,UAAU,CAAC,EAAE;MACpF,OAAO,IAAI;;IAGb,OAAO,KAAK;EACd;EAEAqB,gBAAgBA,CAACjB,QAAQ;IACvB,IAAI,CAACP,cAAc,CAACyB,IAAI,EAAE;IAC1B,IAAI,CAAC1B,iBAAiB,CACnByB,gBAAgB,CAACjB,QAAQ,CAAC,CAC1BU,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAIA,GAAG,CAACQ,IAAI,EAAE;QACZ,IAAI,CAACtB,aAAa,GAAGc,GAAG,CAACQ,IAAI;QAC7BhB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACP,aAAa,EAAC,IAAI,CAAC;;IAExC,CAAC,CAAC;EACN;EAEAuB,eAAeA,CAAA;IACb,IAAI,CAACb,OAAO,GAAG,IAAI,CAACpB,WAAW,CAACkC,gBAAgB,CAACf,EAAE;IACnD,IAAI,CAACW,gBAAgB,CAAC,IAAI,CAACZ,KAAK,CAACC,EAAE,CAAC;IACpCH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACC,KAAK,EAAC,IAAI,CAACE,OAAO,CAAC;EACzD;EAAC,QAAAe,CAAA;qBApFUrC,wBAAwB,EAAAR,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAlD,EAAA,CAAA8C,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAApD,EAAA,CAAA8C,iBAAA,CAAAK,EAAA,CAAAE,QAAA,GAAArD,EAAA,CAAA8C,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAvD,EAAA,CAAA8C,iBAAA,CAAAU,EAAA,CAAAC,iBAAA,GAAAzD,EAAA,CAAA8C,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA3D,EAAA,CAAA8C,iBAAA,CAAAc,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAAxBtD,wBAAwB;IAAAuD,SAAA;IAAAC,MAAA;MAAApC,KAAA;MAAAV,KAAA;IAAA;IAAA+C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdjCtE,EAAA,CAAAC,cAAA,aAA2B;QAEUD,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEjDH,EAAA,CAAAC,cAAA,aAAwB;QAGgCD,EAAA,CAAAwE,UAAA,2BAAAC,iEAAAC,MAAA;UAAA,OAAAH,GAAA,CAAApD,IAAA,GAAAuD,MAAA;QAAA,EAAkB;QAA9D1E,EAAA,CAAAG,YAAA,EAAwH;QACxHH,EAAA,CAAAC,cAAA,aAA0B;QAIJD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC5BH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,2DAAmD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE9DH,EAAA,CAAA2E,SAAA,eAAgE;QACpE3E,EAAA,CAAAG,YAAA,EAAM;QAIlBH,EAAA,CAAAC,cAAA,iBAA4F;QACzCD,EAAA,CAAAwE,UAAA,2BAAAI,kEAAAF,MAAA;UAAA,OAAAH,GAAA,CAAApD,IAAA,GAAAuD,MAAA;QAAA,EAAkB;QAAjE1E,EAAA,CAAAG,YAAA,EAAyH;QACzHH,EAAA,CAAAC,cAAA,cAA0B;QAIJD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACtBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,+CAAuC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAElDH,EAAA,CAAA2E,SAAA,eAAoE;QACxE3E,EAAA,CAAAG,YAAA,EAAM;QAKlBH,EAAA,CAAAC,cAAA,iBAA8F;QAC1CD,EAAA,CAAAwE,UAAA,2BAAAK,kEAAAH,MAAA;UAAA,OAAAH,GAAA,CAAApD,IAAA,GAAAuD,MAAA;QAAA,EAAkB;QAAlE1E,EAAA,CAAAG,YAAA,EAA2H;QAC3HH,EAAA,CAAAC,cAAA,cAA0B;QAIJD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACvBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,oEAA4D;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEvEH,EAAA,CAAA2E,SAAA,eAA+D;QACnE3E,EAAA,CAAAG,YAAA,EAAM;QAIlBH,EAAA,CAAA8E,UAAA,KAAAC,0CAAA,oBAUQ;QACZ/E,EAAA,CAAAG,YAAA,EAAM;QAEVH,EAAA,CAAAC,cAAA,eAAiD;QACGD,EAAA,CAAAwE,UAAA,mBAAAQ,2DAAA;UAAA,OAAST,GAAA,CAAAlD,KAAA,EAAO;QAAA,EAAC;QAACrB,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjFH,EAAA,CAAAC,cAAA,kBAA+D;QAAjBD,EAAA,CAAAwE,UAAA,mBAAAS,2DAAA;UAAA,OAASV,GAAA,CAAA9C,IAAA,EAAM;QAAA,EAAC;QAACzB,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA1DzCH,EAAA,CAAAI,SAAA,GAAyD;QAAzDJ,EAAA,CAAAkF,UAAA,YAAAlF,EAAA,CAAAmF,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAAlC,cAAA,cAAyD;QAC1CrC,EAAA,CAAAI,SAAA,GAAkB;QAAlBJ,EAAA,CAAAkF,UAAA,YAAAX,GAAA,CAAApD,IAAA,CAAkB,aAAAoD,GAAA,CAAAlC,cAAA;QAa9BrC,EAAA,CAAAI,SAAA,IAAuD;QAAvDJ,EAAA,CAAAkF,UAAA,YAAAlF,EAAA,CAAAmF,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAAlC,cAAA,YAAuD;QACxCrC,EAAA,CAAAI,SAAA,GAAkB;QAAlBJ,EAAA,CAAAkF,UAAA,YAAAX,GAAA,CAAApD,IAAA,CAAkB,aAAAoD,GAAA,CAAAlC,cAAA;QAchCrC,EAAA,CAAAI,SAAA,IAAwD;QAAxDJ,EAAA,CAAAkF,UAAA,YAAAlF,EAAA,CAAAmF,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAAlC,cAAA,aAAwD;QACzCrC,EAAA,CAAAI,SAAA,GAAkB;QAAlBJ,EAAA,CAAAkF,UAAA,YAAAX,GAAA,CAAApD,IAAA,CAAkB,aAAAoD,GAAA,CAAAlC,cAAA;QAajDrC,EAAA,CAAAI,SAAA,IAAyB;QAAzBJ,EAAA,CAAAkF,UAAA,SAAAX,GAAA,CAAAhE,YAAA,QAAyB", "names": ["coreConfig", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "SelectRoleModalComponent", "constructor", "authService", "router", "activeModal", "modalService", "userService", "tournamentService", "loadingService", "toast", "items", "role", "assignedUsers", "close", "goLive", "match_id", "navigate", "save", "console", "log", "match", "id", "user_id", "assignRoleToUser", "toString", "subscribe", "res", "error", "message", "isRoleDisabled", "some", "user", "getAssignedUsers", "show", "data", "ngAfterViewInit", "currentUserValue", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "NgbActiveModal", "NgbModal", "i4", "UserService", "i5", "TournamentService", "i6", "LoadingService", "i7", "ToastrService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "SelectRoleModalComponent_Template", "rf", "ctx", "ɵɵlistener", "SelectRoleModalComponent_Template_input_ngModelChange_7_listener", "$event", "ɵɵelement", "SelectRoleModalComponent_Template_input_ngModelChange_18_listener", "SelectRoleModalComponent_Template_input_ngModelChange_29_listener", "ɵɵtemplate", "SelectRoleModalComponent_label_39_Template", "SelectRoleModalComponent_Template_button_click_41_listener", "SelectRoleModalComponent_Template_button_click_43_listener", "ɵɵproperty", "ɵɵpureFunction1", "_c0"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\select-role-modal\\select-role-modal.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\streaming\\select-role-modal\\select-role-modal.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { coreConfig } from 'app/app-config';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\n\r\n@Component({\r\n  selector: 'app-select-role-modal',\r\n  templateUrl: './select-role-modal.component.html',\r\n  styleUrls: ['./select-role-modal.component.scss']\r\n})\r\nexport class SelectRoleModalComponent {\r\n  user_id: any;\r\n  @Input() match: any;\r\n  @Input() items: any = [];\r\n  role: string = '';\r\n  coreConfig = coreConfig;\r\n  assignedUsers: any[] = [];\r\n  errorMessage: string = '';\r\n  constructor(\r\n    public authService: AuthService,\r\n    private router: Router,\r\n    public activeModal: NgbActiveModal,\r\n    public modalService: NgbModal,\r\n    public userService: UserService,\r\n    public tournamentService: TournamentService,\r\n    public loadingService: LoadingService,\r\n    public toast: ToastrService\r\n  ) {\r\n  }\r\n\r\n  close() {\r\n    this.activeModal.close();\r\n  }\r\n\r\n  goLive(match_id) {\r\n    this.router.navigate(['/streaming/broadcast', match_id]);\r\n  }\r\n\r\n  save() {\r\n    console.log(\"Selected Role:\", this.role); \r\n    console.log(\"save\", this.match.id, this.user_id, this.role);\r\n\r\n    this.tournamentService.assignRoleToUser({\r\n      match_id: this.match.id,\r\n      user_id: this.user_id.toString(),\r\n      role: this.role\r\n    }).subscribe((res: any) => {\r\n      this.activeModal.close();\r\n      this.goLive(this.match.id);\r\n    },\r\n    (error: any) => {\r\n      this.errorMessage = error.message;\r\n    }\r\n  )};\r\n\r\n  isRoleDisabled(role: string): boolean {\r\n    if (this.assignedUsers.some(user => user.role === role)) {\r\n      return true;\r\n    }\r\n    if (role === 'camera' && this.assignedUsers.some(user => user.role === 'streamer')) {\r\n      return true;\r\n    }\r\n    if (role === 'streamer' && this.assignedUsers.some(user => user.role === 'control')) {\r\n      return true;\r\n    }\r\n    if (role === 'streamer' && this.assignedUsers.some(user => user.role === 'camera')) {\r\n      return true;\r\n    }\r\n    if (role === 'control' && this.assignedUsers.some(user => user.role === 'streamer')) {\r\n      return true;\r\n    }\r\n    if (role === 'streamer' && this.assignedUsers.some(user => user.role === 'streamer')) {\r\n      return true;\r\n    }\r\n    \r\n    return false;\r\n  }  \r\n\r\n  getAssignedUsers(match_id) {\r\n    this.loadingService.show();\r\n    this.tournamentService\r\n      .getAssignedUsers(match_id)\r\n      .subscribe((res: any) => {\r\n        if (res.data) {\r\n          this.assignedUsers = res.data;\r\n          console.log(this.assignedUsers,\"--\");\r\n        }\r\n      });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.user_id = this.authService.currentUserValue.id;\r\n    this.getAssignedUsers(this.match.id);\r\n    console.log('ngAfterViewInit', this.match,this.user_id); \r\n  }\r\n}\r\n", "\r\n    <div class=\"modal-content\">\r\n        <div class=\"p-2 pt-3\">\r\n            <h2 class=\"text-center mb-0\">Select Role</h2>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n            <div class=\"plans\">\r\n                <label class=\"plan\" for=\"viewer\" [ngClass]=\"{'plan-disabled': isRoleDisabled('streamer')}\">\r\n                    <input type=\"radio\" id=\"viewer\" name=\"plan\" [(ngModel)]=\"role\" value=\"stream\" [disabled]=\"isRoleDisabled('streamer')\" />\r\n                    <div class=\"plan-content\">\r\n                        <div class=\"plan-details\">\r\n                            <div class=\"d-flex align-items-center justify-content-between\">\r\n                                <div style=\"flex:1\">\r\n                                    <span>Record & Update</span>\r\n                                    <p>You can both stream and update events for the match</p>\r\n                                </div>\r\n                                <div class=\"svg-viewer\" style=\"width: 68px; height: 68px\"></div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </label>\r\n                <label class=\"plan\" for=\"cameraman\" [ngClass]=\"{'plan-disabled': isRoleDisabled('camera')}\">\r\n                    <input type=\"radio\" name=\"plan\" id=\"cameraman\" [(ngModel)]=\"role\" value=\"camera\"  [disabled]=\"isRoleDisabled('camera')\"/>\r\n                    <div class=\"plan-content\">\r\n                        <div class=\"plan-details\">\r\n                            <div class=\"d-flex align-items-center justify-content-between\">\r\n                                <div style=\"flex:1\">\r\n                                    <span>Cameraman</span>\r\n                                    <p>Use your camera to capture key moments.</p>\r\n                                </div>\r\n                                <div class=\"icon-cameraman\" style=\"width: 68px; height: 68px\"></div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </label>\r\n\r\n                <label class=\"plan\" for=\"controller\" [ngClass]=\"{'plan-disabled': isRoleDisabled('control')}\">\r\n                    <input type=\"radio\" name=\"plan\" id=\"controller\" [(ngModel)]=\"role\" value=\"control\" [disabled]=\"isRoleDisabled('control')\"/>\r\n                    <div class=\"plan-content\">\r\n                        <div class=\"plan-details\">\r\n                            <div class=\"d-flex align-items-center justify-content-between\">\r\n                                <div style=\"flex:1\">\r\n                                    <span>Controller</span>\r\n                                    <p>You in a crucial role in providing match updates to viewers.</p>\r\n                                </div>\r\n                                <div class=\"svg-score\" style=\"width: 68px; height: 68px\"></div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </label>\r\n                <label class=\"plan\" *ngIf=\"errorMessage !== ''\">\r\n                    <div class=\"plan-content\" style=\" border: 2px; background: var(--light-opacity-color-danger-danger-16, rgba(234, 84, 85, 0.16));\">\r\n                        <div class=\"plan-details\">\r\n                            <div class=\"d-flex align-items-center justify-content-between\">\r\n                                <div style=\"flex:1; color: red\" >\r\n                                    <i>{{errorMessage}}</i>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </label>\r\n            </div>\r\n        </div>\r\n        <div class=\"modal-footer justify-content-center\">\r\n            <button type=\"button\" class=\"btn btn-secondary\" (click)=\"close()\">Cancel</button>\r\n            <button type=\"button\" class=\"btn btn-primary\" (click)=\"save()\">Select</button>\r\n        </div>\r\n    </div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
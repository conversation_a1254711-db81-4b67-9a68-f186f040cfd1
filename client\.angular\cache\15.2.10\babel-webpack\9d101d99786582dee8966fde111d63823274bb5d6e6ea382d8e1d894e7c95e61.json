{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { _getProvider, getApp, _registerComponent, registerVersion, SDK_VERSION } from '@firebase/app';\nimport { ErrorFactory, FirebaseError, getModularInstance, calculateBackoffMillis, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport { LogLevel, Logger } from '@firebase/logger';\nimport '@firebase/installations';\nconst name = \"@firebase/remote-config\";\nconst version = \"0.4.4\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Shims a minimal AbortSignal.\r\n *\r\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\r\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\r\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\r\n * swapped out if/when we do.\r\n */\nclass RemoteConfigAbortSignal {\n  constructor() {\n    this.listeners = [];\n  }\n  addEventListener(listener) {\n    this.listeners.push(listener);\n  }\n  abort() {\n    this.listeners.forEach(listener => listener());\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst RC_COMPONENT_NAME = 'remote-config';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERROR_DESCRIPTION_MAP = {\n  [\"registration-window\" /* ErrorCode.REGISTRATION_WINDOW */]: 'Undefined window object. This SDK only supports usage in a browser environment.',\n  [\"registration-project-id\" /* ErrorCode.REGISTRATION_PROJECT_ID */]: 'Undefined project identifier. Check Firebase app initialization.',\n  [\"registration-api-key\" /* ErrorCode.REGISTRATION_API_KEY */]: 'Undefined API key. Check Firebase app initialization.',\n  [\"registration-app-id\" /* ErrorCode.REGISTRATION_APP_ID */]: 'Undefined app identifier. Check Firebase app initialization.',\n  [\"storage-open\" /* ErrorCode.STORAGE_OPEN */]: 'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [\"storage-get\" /* ErrorCode.STORAGE_GET */]: 'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [\"storage-set\" /* ErrorCode.STORAGE_SET */]: 'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [\"storage-delete\" /* ErrorCode.STORAGE_DELETE */]: 'Error thrown when deleting from storage. Original error: {$originalErrorMessage}.',\n  [\"fetch-client-network\" /* ErrorCode.FETCH_NETWORK */]: 'Fetch client failed to connect to a network. Check Internet connection.' + ' Original error: {$originalErrorMessage}.',\n  [\"fetch-timeout\" /* ErrorCode.FETCH_TIMEOUT */]: 'The config fetch request timed out. ' + ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.',\n  [\"fetch-throttle\" /* ErrorCode.FETCH_THROTTLE */]: 'The config fetch request timed out while in an exponential backoff state.' + ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.' + ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [\"fetch-client-parse\" /* ErrorCode.FETCH_PARSE */]: 'Fetch client could not parse response.' + ' Original error: {$originalErrorMessage}.',\n  [\"fetch-status\" /* ErrorCode.FETCH_STATUS */]: 'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [\"indexed-db-unavailable\" /* ErrorCode.INDEXED_DB_UNAVAILABLE */]: 'Indexed DB is not supported by current browser'\n};\nconst ERROR_FACTORY = new ErrorFactory('remoteconfig' /* service */, 'Remote Config' /* service name */, ERROR_DESCRIPTION_MAP);\n// Note how this is like typeof/instanceof, but for ErrorCode.\nfunction hasErrorCode(e, errorCode) {\n  return e instanceof FirebaseError && e.code.indexOf(errorCode) !== -1;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DEFAULT_VALUE_FOR_BOOLEAN = false;\nconst DEFAULT_VALUE_FOR_STRING = '';\nconst DEFAULT_VALUE_FOR_NUMBER = 0;\nconst BOOLEAN_TRUTHY_VALUES = ['1', 'true', 't', 'yes', 'y', 'on'];\nclass Value {\n  constructor(_source, _value = DEFAULT_VALUE_FOR_STRING) {\n    this._source = _source;\n    this._value = _value;\n  }\n  asString() {\n    return this._value;\n  }\n  asBoolean() {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_BOOLEAN;\n    }\n    return BOOLEAN_TRUTHY_VALUES.indexOf(this._value.toLowerCase()) >= 0;\n  }\n  asNumber() {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_NUMBER;\n    }\n    let num = Number(this._value);\n    if (isNaN(num)) {\n      num = DEFAULT_VALUE_FOR_NUMBER;\n    }\n    return num;\n  }\n  getSource() {\n    return this._source;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n * @returns A {@link RemoteConfig} instance.\r\n *\r\n * @public\r\n */\nfunction getRemoteConfig(app = getApp()) {\n  app = getModularInstance(app);\n  const rcProvider = _getProvider(app, RC_COMPONENT_NAME);\n  return rcProvider.getImmediate();\n}\n/**\r\n * Makes the last fetched config available to the getters.\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\r\n * If the fetched configs were already activated, the `Promise` will resolve to false.\r\n *\r\n * @public\r\n */\nfunction activate(_x) {\n  return _activate.apply(this, arguments);\n}\n/**\r\n * Ensures the last activated config are available to the getters.\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n *\r\n * @returns A `Promise` that resolves when the last activated config is available to the getters.\r\n * @public\r\n */\nfunction _activate() {\n  _activate = _asyncToGenerator(function* (remoteConfig) {\n    const rc = getModularInstance(remoteConfig);\n    const [lastSuccessfulFetchResponse, activeConfigEtag] = yield Promise.all([rc._storage.getLastSuccessfulFetchResponse(), rc._storage.getActiveConfigEtag()]);\n    if (!lastSuccessfulFetchResponse || !lastSuccessfulFetchResponse.config || !lastSuccessfulFetchResponse.eTag || lastSuccessfulFetchResponse.eTag === activeConfigEtag) {\n      // Either there is no successful fetched config, or is the same as current active\n      // config.\n      return false;\n    }\n    yield Promise.all([rc._storageCache.setActiveConfig(lastSuccessfulFetchResponse.config), rc._storage.setActiveConfigEtag(lastSuccessfulFetchResponse.eTag)]);\n    return true;\n  });\n  return _activate.apply(this, arguments);\n}\nfunction ensureInitialized(remoteConfig) {\n  const rc = getModularInstance(remoteConfig);\n  if (!rc._initializePromise) {\n    rc._initializePromise = rc._storageCache.loadFromStorage().then(() => {\n      rc._isInitializationComplete = true;\n    });\n  }\n  return rc._initializePromise;\n}\n/**\r\n * Fetches and caches configuration from the Remote Config service.\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @public\r\n */\nfunction fetchConfig(_x2) {\n  return _fetchConfig.apply(this, arguments);\n}\n/**\r\n * Gets all config.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @returns All config.\r\n *\r\n * @public\r\n */\nfunction _fetchConfig() {\n  _fetchConfig = _asyncToGenerator(function* (remoteConfig) {\n    const rc = getModularInstance(remoteConfig);\n    // Aborts the request after the given timeout, causing the fetch call to\n    // reject with an `AbortError`.\n    //\n    // <p>Aborting after the request completes is a no-op, so we don't need a\n    // corresponding `clearTimeout`.\n    //\n    // Locating abort logic here because:\n    // * it uses a developer setting (timeout)\n    // * it applies to all retries (like curl's max-time arg)\n    // * it is consistent with the Fetch API's signal input\n    const abortSignal = new RemoteConfigAbortSignal();\n    setTimeout( /*#__PURE__*/_asyncToGenerator(function* () {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      abortSignal.abort();\n    }), rc.settings.fetchTimeoutMillis);\n    // Catches *all* errors thrown by client so status can be set consistently.\n    try {\n      yield rc._client.fetch({\n        cacheMaxAgeMillis: rc.settings.minimumFetchIntervalMillis,\n        signal: abortSignal\n      });\n      yield rc._storageCache.setLastFetchStatus('success');\n    } catch (e) {\n      const lastFetchStatus = hasErrorCode(e, \"fetch-throttle\" /* ErrorCode.FETCH_THROTTLE */) ? 'throttle' : 'failure';\n      yield rc._storageCache.setLastFetchStatus(lastFetchStatus);\n      throw e;\n    }\n  });\n  return _fetchConfig.apply(this, arguments);\n}\nfunction getAll(remoteConfig) {\n  const rc = getModularInstance(remoteConfig);\n  return getAllKeys(rc._storageCache.getActiveConfig(), rc.defaultConfig).reduce((allConfigs, key) => {\n    allConfigs[key] = getValue(remoteConfig, key);\n    return allConfigs;\n  }, {});\n}\n/**\r\n * Gets the value for the given key as a boolean.\r\n *\r\n * Convenience method for calling <code>remoteConfig.getValue(key).asBoolean()</code>.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key as a boolean.\r\n * @public\r\n */\nfunction getBoolean(remoteConfig, key) {\n  return getValue(getModularInstance(remoteConfig), key).asBoolean();\n}\n/**\r\n * Gets the value for the given key as a number.\r\n *\r\n * Convenience method for calling <code>remoteConfig.getValue(key).asNumber()</code>.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key as a number.\r\n *\r\n * @public\r\n */\nfunction getNumber(remoteConfig, key) {\n  return getValue(getModularInstance(remoteConfig), key).asNumber();\n}\n/**\r\n * Gets the value for the given key as a string.\r\n * Convenience method for calling <code>remoteConfig.getValue(key).asString()</code>.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key as a string.\r\n *\r\n * @public\r\n */\nfunction getString(remoteConfig, key) {\n  return getValue(getModularInstance(remoteConfig), key).asString();\n}\n/**\r\n * Gets the {@link Value} for the given key.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key.\r\n *\r\n * @public\r\n */\nfunction getValue(remoteConfig, key) {\n  const rc = getModularInstance(remoteConfig);\n  if (!rc._isInitializationComplete) {\n    rc._logger.debug(`A value was requested for key \"${key}\" before SDK initialization completed.` + ' Await on ensureInitialized if the intent was to get a previously activated value.');\n  }\n  const activeConfig = rc._storageCache.getActiveConfig();\n  if (activeConfig && activeConfig[key] !== undefined) {\n    return new Value('remote', activeConfig[key]);\n  } else if (rc.defaultConfig && rc.defaultConfig[key] !== undefined) {\n    return new Value('default', String(rc.defaultConfig[key]));\n  }\n  rc._logger.debug(`Returning static value for key \"${key}\".` + ' Define a default or remote value if this is unintentional.');\n  return new Value('static');\n}\n/**\r\n * Defines the log level to use.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param logLevel - The log level to set.\r\n *\r\n * @public\r\n */\nfunction setLogLevel(remoteConfig, logLevel) {\n  const rc = getModularInstance(remoteConfig);\n  switch (logLevel) {\n    case 'debug':\n      rc._logger.logLevel = LogLevel.DEBUG;\n      break;\n    case 'silent':\n      rc._logger.logLevel = LogLevel.SILENT;\n      break;\n    default:\n      rc._logger.logLevel = LogLevel.ERROR;\n  }\n}\n/**\r\n * Dedupes and returns an array of all the keys of the received objects.\r\n */\nfunction getAllKeys(obj1 = {}, obj2 = {}) {\n  return Object.keys(Object.assign(Object.assign({}, obj1), obj2));\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Implements the {@link RemoteConfigClient} abstraction with success response caching.\r\n *\r\n * <p>Comparable to the browser's Cache API for responses, but the Cache API requires a Service\r\n * Worker, which requires HTTPS, which would significantly complicate SDK installation. Also, the\r\n * Cache API doesn't support matching entries by time.\r\n */\nclass CachingClient {\n  constructor(client, storage, storageCache, logger) {\n    this.client = client;\n    this.storage = storage;\n    this.storageCache = storageCache;\n    this.logger = logger;\n  }\n  /**\r\n   * Returns true if the age of the cached fetched configs is less than or equal to\r\n   * {@link Settings#minimumFetchIntervalInSeconds}.\r\n   *\r\n   * <p>This is comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the\r\n   * native Fetch API.\r\n   *\r\n   * <p>Visible for testing.\r\n   */\n  isCachedDataFresh(cacheMaxAgeMillis, lastSuccessfulFetchTimestampMillis) {\n    // Cache can only be fresh if it's populated.\n    if (!lastSuccessfulFetchTimestampMillis) {\n      this.logger.debug('Config fetch cache check. Cache unpopulated.');\n      return false;\n    }\n    // Calculates age of cache entry.\n    const cacheAgeMillis = Date.now() - lastSuccessfulFetchTimestampMillis;\n    const isCachedDataFresh = cacheAgeMillis <= cacheMaxAgeMillis;\n    this.logger.debug('Config fetch cache check.' + ` Cache age millis: ${cacheAgeMillis}.` + ` Cache max age millis (minimumFetchIntervalMillis setting): ${cacheMaxAgeMillis}.` + ` Is cache hit: ${isCachedDataFresh}.`);\n    return isCachedDataFresh;\n  }\n  fetch(request) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Reads from persisted storage to avoid cache miss if callers don't wait on initialization.\n      const [lastSuccessfulFetchTimestampMillis, lastSuccessfulFetchResponse] = yield Promise.all([_this.storage.getLastSuccessfulFetchTimestampMillis(), _this.storage.getLastSuccessfulFetchResponse()]);\n      // Exits early on cache hit.\n      if (lastSuccessfulFetchResponse && _this.isCachedDataFresh(request.cacheMaxAgeMillis, lastSuccessfulFetchTimestampMillis)) {\n        return lastSuccessfulFetchResponse;\n      }\n      // Deviates from pure decorator by not honoring a passed ETag since we don't have a public API\n      // that allows the caller to pass an ETag.\n      request.eTag = lastSuccessfulFetchResponse && lastSuccessfulFetchResponse.eTag;\n      // Falls back to service on cache miss.\n      const response = yield _this.client.fetch(request);\n      // Fetch throws for non-success responses, so success is guaranteed here.\n      const storageOperations = [\n      // Uses write-through cache for consistency with synchronous public API.\n      _this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())];\n      if (response.status === 200) {\n        // Caches response only if it has changed, ie non-304 responses.\n        storageOperations.push(_this.storage.setLastSuccessfulFetchResponse(response));\n      }\n      yield Promise.all(storageOperations);\n      return response;\n    })();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Attempts to get the most accurate browser language setting.\r\n *\r\n * <p>Adapted from getUserLanguage in packages/auth/src/utils.js for TypeScript.\r\n *\r\n * <p>Defers default language specification to server logic for consistency.\r\n *\r\n * @param navigatorLanguage Enables tests to override read-only {@link NavigatorLanguage}.\r\n */\nfunction getUserLanguage(navigatorLanguage = navigator) {\n  return (\n    // Most reliable, but only supported in Chrome/Firefox.\n    navigatorLanguage.languages && navigatorLanguage.languages[0] ||\n    // Supported in most browsers, but returns the language of the browser\n    // UI, not the language set in browser settings.\n    navigatorLanguage.language\n    // Polyfill otherwise.\n  );\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Implements the Client abstraction for the Remote Config REST API.\r\n */\nclass RestClient {\n  constructor(firebaseInstallations, sdkVersion, namespace, projectId, apiKey, appId) {\n    this.firebaseInstallations = firebaseInstallations;\n    this.sdkVersion = sdkVersion;\n    this.namespace = namespace;\n    this.projectId = projectId;\n    this.apiKey = apiKey;\n    this.appId = appId;\n  }\n  /**\r\n   * Fetches from the Remote Config REST API.\r\n   *\r\n   * @throws a {@link ErrorCode.FETCH_NETWORK} error if {@link GlobalFetch#fetch} can't\r\n   * connect to the network.\r\n   * @throws a {@link ErrorCode.FETCH_PARSE} error if {@link Response#json} can't parse the\r\n   * fetch response.\r\n   * @throws a {@link ErrorCode.FETCH_STATUS} error if the service returns an HTTP error status.\r\n   */\n  fetch(request) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const [installationId, installationToken] = yield Promise.all([_this2.firebaseInstallations.getId(), _this2.firebaseInstallations.getToken()]);\n      const urlBase = window.FIREBASE_REMOTE_CONFIG_URL_BASE || 'https://firebaseremoteconfig.googleapis.com';\n      const url = `${urlBase}/v1/projects/${_this2.projectId}/namespaces/${_this2.namespace}:fetch?key=${_this2.apiKey}`;\n      const headers = {\n        'Content-Type': 'application/json',\n        'Content-Encoding': 'gzip',\n        // Deviates from pure decorator by not passing max-age header since we don't currently have\n        // service behavior using that header.\n        'If-None-Match': request.eTag || '*'\n      };\n      const requestBody = {\n        /* eslint-disable camelcase */\n        sdk_version: _this2.sdkVersion,\n        app_instance_id: installationId,\n        app_instance_id_token: installationToken,\n        app_id: _this2.appId,\n        language_code: getUserLanguage()\n        /* eslint-enable camelcase */\n      };\n\n      const options = {\n        method: 'POST',\n        headers,\n        body: JSON.stringify(requestBody)\n      };\n      // This logic isn't REST-specific, but shimming abort logic isn't worth another decorator.\n      const fetchPromise = fetch(url, options);\n      const timeoutPromise = new Promise((_resolve, reject) => {\n        // Maps async event listener to Promise API.\n        request.signal.addEventListener(() => {\n          // Emulates https://heycam.github.io/webidl/#aborterror\n          const error = new Error('The operation was aborted.');\n          error.name = 'AbortError';\n          reject(error);\n        });\n      });\n      let response;\n      try {\n        yield Promise.race([fetchPromise, timeoutPromise]);\n        response = yield fetchPromise;\n      } catch (originalError) {\n        let errorCode = \"fetch-client-network\" /* ErrorCode.FETCH_NETWORK */;\n        if ((originalError === null || originalError === void 0 ? void 0 : originalError.name) === 'AbortError') {\n          errorCode = \"fetch-timeout\" /* ErrorCode.FETCH_TIMEOUT */;\n        }\n\n        throw ERROR_FACTORY.create(errorCode, {\n          originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\n        });\n      }\n      let status = response.status;\n      // Normalizes nullable header to optional.\n      const responseEtag = response.headers.get('ETag') || undefined;\n      let config;\n      let state;\n      // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n      // Requesting application/json and checking for a 200 ensures there's JSON data.\n      if (response.status === 200) {\n        let responseBody;\n        try {\n          responseBody = yield response.json();\n        } catch (originalError) {\n          throw ERROR_FACTORY.create(\"fetch-client-parse\" /* ErrorCode.FETCH_PARSE */, {\n            originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\n          });\n        }\n        config = responseBody['entries'];\n        state = responseBody['state'];\n      }\n      // Normalizes based on legacy state.\n      if (state === 'INSTANCE_STATE_UNSPECIFIED') {\n        status = 500;\n      } else if (state === 'NO_CHANGE') {\n        status = 304;\n      } else if (state === 'NO_TEMPLATE' || state === 'EMPTY_CONFIG') {\n        // These cases can be fixed remotely, so normalize to safe value.\n        config = {};\n      }\n      // Normalize to exception-based control flow for non-success cases.\n      // Encapsulates HTTP specifics in this class as much as possible. Status is still the best for\n      // differentiating success states (200 from 304; the state body param is undefined in a\n      // standard 304).\n      if (status !== 304 && status !== 200) {\n        throw ERROR_FACTORY.create(\"fetch-status\" /* ErrorCode.FETCH_STATUS */, {\n          httpStatus: status\n        });\n      }\n      return {\n        status,\n        eTag: responseEtag,\n        config\n      };\n    })();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Supports waiting on a backoff by:\r\n *\r\n * <ul>\r\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\r\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\r\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\r\n *       request appear the same.</li>\r\n * </ul>\r\n *\r\n * <p>Visible for testing.\r\n */\nfunction setAbortableTimeout(signal, throttleEndTimeMillis) {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n    const timeout = setTimeout(resolve, backoffMillis);\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(ERROR_FACTORY.create(\"fetch-throttle\" /* ErrorCode.FETCH_THROTTLE */, {\n        throttleEndTimeMillis\n      }));\n    });\n  });\n}\n/**\r\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\r\n */\nfunction isRetriableError(e) {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n  return httpStatus === 429 || httpStatus === 500 || httpStatus === 503 || httpStatus === 504;\n}\n/**\r\n * Decorates a Client with retry logic.\r\n *\r\n * <p>Comparable to CachingClient, but uses backoff logic instead of cache max age and doesn't cache\r\n * responses (because the SDK has no use for error responses).\r\n */\nclass RetryingClient {\n  constructor(client, storage) {\n    this.client = client;\n    this.storage = storage;\n  }\n  fetch(request) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const throttleMetadata = (yield _this3.storage.getThrottleMetadata()) || {\n        backoffCount: 0,\n        throttleEndTimeMillis: Date.now()\n      };\n      return _this3.attemptFetch(request, throttleMetadata);\n    })();\n  }\n  /**\r\n   * A recursive helper for attempting a fetch request repeatedly.\r\n   *\r\n   * @throws any non-retriable errors.\r\n   */\n  attemptFetch(request, {\n    throttleEndTimeMillis,\n    backoffCount\n  }) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // Starts with a (potentially zero) timeout to support resumption from stored state.\n      // Ensures the throttle end time is honored if the last attempt timed out.\n      // Note the SDK will never make a request if the fetch timeout expires at this point.\n      yield setAbortableTimeout(request.signal, throttleEndTimeMillis);\n      try {\n        const response = yield _this4.client.fetch(request);\n        // Note the SDK only clears throttle state if response is success or non-retriable.\n        yield _this4.storage.deleteThrottleMetadata();\n        return response;\n      } catch (e) {\n        if (!isRetriableError(e)) {\n          throw e;\n        }\n        // Increments backoff state.\n        const throttleMetadata = {\n          throttleEndTimeMillis: Date.now() + calculateBackoffMillis(backoffCount),\n          backoffCount: backoffCount + 1\n        };\n        // Persists state.\n        yield _this4.storage.setThrottleMetadata(throttleMetadata);\n        return _this4.attemptFetch(request, throttleMetadata);\n      }\n    })();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DEFAULT_FETCH_TIMEOUT_MILLIS = 60 * 1000; // One minute\nconst DEFAULT_CACHE_MAX_AGE_MILLIS = 12 * 60 * 60 * 1000; // Twelve hours.\n/**\r\n * Encapsulates business logic mapping network and storage dependencies to the public SDK API.\r\n *\r\n * See {@link https://github.com/FirebasePrivate/firebase-js-sdk/blob/master/packages/firebase/index.d.ts|interface documentation} for method descriptions.\r\n */\nclass RemoteConfig {\n  constructor(\n  // Required by FirebaseServiceFactory interface.\n  app,\n  // JS doesn't support private yet\n  // (https://github.com/tc39/proposal-class-fields#private-fields), so we hint using an\n  // underscore prefix.\n  /**\r\n   * @internal\r\n   */\n  _client,\n  /**\r\n   * @internal\r\n   */\n  _storageCache,\n  /**\r\n   * @internal\r\n   */\n  _storage,\n  /**\r\n   * @internal\r\n   */\n  _logger) {\n    this.app = app;\n    this._client = _client;\n    this._storageCache = _storageCache;\n    this._storage = _storage;\n    this._logger = _logger;\n    /**\r\n     * Tracks completion of initialization promise.\r\n     * @internal\r\n     */\n    this._isInitializationComplete = false;\n    this.settings = {\n      fetchTimeoutMillis: DEFAULT_FETCH_TIMEOUT_MILLIS,\n      minimumFetchIntervalMillis: DEFAULT_CACHE_MAX_AGE_MILLIS\n    };\n    this.defaultConfig = {};\n  }\n  get fetchTimeMillis() {\n    return this._storageCache.getLastSuccessfulFetchTimestampMillis() || -1;\n  }\n  get lastFetchStatus() {\n    return this._storageCache.getLastFetchStatus() || 'no-fetch-yet';\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Converts an error event associated with a {@link IDBRequest} to a {@link FirebaseError}.\r\n */\nfunction toFirebaseError(event, errorCode) {\n  const originalError = event.target.error || undefined;\n  return ERROR_FACTORY.create(errorCode, {\n    originalErrorMessage: originalError && (originalError === null || originalError === void 0 ? void 0 : originalError.message)\n  });\n}\n/**\r\n * A general-purpose store keyed by app + namespace + {@link\r\n * ProjectNamespaceKeyFieldValue}.\r\n *\r\n * <p>The Remote Config SDK can be used with multiple app installations, and each app can interact\r\n * with multiple namespaces, so this store uses app (ID + name) and namespace as common parent keys\r\n * for a set of key-value pairs. See {@link Storage#createCompositeKey}.\r\n *\r\n * <p>Visible for testing.\r\n */\nconst APP_NAMESPACE_STORE = 'app_namespace_store';\nconst DB_NAME = 'firebase_remote_config';\nconst DB_VERSION = 1;\n// Visible for testing.\nfunction openDatabase() {\n  return new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n      request.onerror = event => {\n        reject(toFirebaseError(event, \"storage-open\" /* ErrorCode.STORAGE_OPEN */));\n      };\n\n      request.onsuccess = event => {\n        resolve(event.target.result);\n      };\n      request.onupgradeneeded = event => {\n        const db = event.target.result;\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(APP_NAMESPACE_STORE, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (error) {\n      reject(ERROR_FACTORY.create(\"storage-open\" /* ErrorCode.STORAGE_OPEN */, {\n        originalErrorMessage: error === null || error === void 0 ? void 0 : error.message\n      }));\n    }\n  });\n}\n/**\r\n * Abstracts data persistence.\r\n */\nclass Storage {\n  /**\r\n   * @param appId enables storage segmentation by app (ID + name).\r\n   * @param appName enables storage segmentation by app (ID + name).\r\n   * @param namespace enables storage segmentation by namespace.\r\n   */\n  constructor(appId, appName, namespace, openDbPromise = openDatabase()) {\n    this.appId = appId;\n    this.appName = appName;\n    this.namespace = namespace;\n    this.openDbPromise = openDbPromise;\n  }\n  getLastFetchStatus() {\n    return this.get('last_fetch_status');\n  }\n  setLastFetchStatus(status) {\n    return this.set('last_fetch_status', status);\n  }\n  // This is comparable to a cache entry timestamp. If we need to expire other data, we could\n  // consider adding timestamp to all storage records and an optional max age arg to getters.\n  getLastSuccessfulFetchTimestampMillis() {\n    return this.get('last_successful_fetch_timestamp_millis');\n  }\n  setLastSuccessfulFetchTimestampMillis(timestamp) {\n    return this.set('last_successful_fetch_timestamp_millis', timestamp);\n  }\n  getLastSuccessfulFetchResponse() {\n    return this.get('last_successful_fetch_response');\n  }\n  setLastSuccessfulFetchResponse(response) {\n    return this.set('last_successful_fetch_response', response);\n  }\n  getActiveConfig() {\n    return this.get('active_config');\n  }\n  setActiveConfig(config) {\n    return this.set('active_config', config);\n  }\n  getActiveConfigEtag() {\n    return this.get('active_config_etag');\n  }\n  setActiveConfigEtag(etag) {\n    return this.set('active_config_etag', etag);\n  }\n  getThrottleMetadata() {\n    return this.get('throttle_metadata');\n  }\n  setThrottleMetadata(metadata) {\n    return this.set('throttle_metadata', metadata);\n  }\n  deleteThrottleMetadata() {\n    return this.delete('throttle_metadata');\n  }\n  get(key) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const db = yield _this5.openDbPromise;\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction([APP_NAMESPACE_STORE], 'readonly');\n        const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n        const compositeKey = _this5.createCompositeKey(key);\n        try {\n          const request = objectStore.get(compositeKey);\n          request.onerror = event => {\n            reject(toFirebaseError(event, \"storage-get\" /* ErrorCode.STORAGE_GET */));\n          };\n\n          request.onsuccess = event => {\n            const result = event.target.result;\n            if (result) {\n              resolve(result.value);\n            } else {\n              resolve(undefined);\n            }\n          };\n        } catch (e) {\n          reject(ERROR_FACTORY.create(\"storage-get\" /* ErrorCode.STORAGE_GET */, {\n            originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n          }));\n        }\n      });\n    })();\n  }\n  set(key, value) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const db = yield _this6.openDbPromise;\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n        const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n        const compositeKey = _this6.createCompositeKey(key);\n        try {\n          const request = objectStore.put({\n            compositeKey,\n            value\n          });\n          request.onerror = event => {\n            reject(toFirebaseError(event, \"storage-set\" /* ErrorCode.STORAGE_SET */));\n          };\n\n          request.onsuccess = () => {\n            resolve();\n          };\n        } catch (e) {\n          reject(ERROR_FACTORY.create(\"storage-set\" /* ErrorCode.STORAGE_SET */, {\n            originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n          }));\n        }\n      });\n    })();\n  }\n  delete(key) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const db = yield _this7.openDbPromise;\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n        const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n        const compositeKey = _this7.createCompositeKey(key);\n        try {\n          const request = objectStore.delete(compositeKey);\n          request.onerror = event => {\n            reject(toFirebaseError(event, \"storage-delete\" /* ErrorCode.STORAGE_DELETE */));\n          };\n\n          request.onsuccess = () => {\n            resolve();\n          };\n        } catch (e) {\n          reject(ERROR_FACTORY.create(\"storage-delete\" /* ErrorCode.STORAGE_DELETE */, {\n            originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n          }));\n        }\n      });\n    })();\n  }\n  // Facilitates composite key functionality (which is unsupported in IE).\n  createCompositeKey(key) {\n    return [this.appId, this.appName, this.namespace, key].join();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * A memory cache layer over storage to support the SDK's synchronous read requirements.\r\n */\nclass StorageCache {\n  constructor(storage) {\n    this.storage = storage;\n  }\n  /**\r\n   * Memory-only getters\r\n   */\n  getLastFetchStatus() {\n    return this.lastFetchStatus;\n  }\n  getLastSuccessfulFetchTimestampMillis() {\n    return this.lastSuccessfulFetchTimestampMillis;\n  }\n  getActiveConfig() {\n    return this.activeConfig;\n  }\n  /**\r\n   * Read-ahead getter\r\n   */\n  loadFromStorage() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const lastFetchStatusPromise = _this8.storage.getLastFetchStatus();\n      const lastSuccessfulFetchTimestampMillisPromise = _this8.storage.getLastSuccessfulFetchTimestampMillis();\n      const activeConfigPromise = _this8.storage.getActiveConfig();\n      // Note:\n      // 1. we consistently check for undefined to avoid clobbering defined values\n      //   in memory\n      // 2. we defer awaiting to improve readability, as opposed to destructuring\n      //   a Promise.all result, for example\n      const lastFetchStatus = yield lastFetchStatusPromise;\n      if (lastFetchStatus) {\n        _this8.lastFetchStatus = lastFetchStatus;\n      }\n      const lastSuccessfulFetchTimestampMillis = yield lastSuccessfulFetchTimestampMillisPromise;\n      if (lastSuccessfulFetchTimestampMillis) {\n        _this8.lastSuccessfulFetchTimestampMillis = lastSuccessfulFetchTimestampMillis;\n      }\n      const activeConfig = yield activeConfigPromise;\n      if (activeConfig) {\n        _this8.activeConfig = activeConfig;\n      }\n    })();\n  }\n  /**\r\n   * Write-through setters\r\n   */\n  setLastFetchStatus(status) {\n    this.lastFetchStatus = status;\n    return this.storage.setLastFetchStatus(status);\n  }\n  setLastSuccessfulFetchTimestampMillis(timestampMillis) {\n    this.lastSuccessfulFetchTimestampMillis = timestampMillis;\n    return this.storage.setLastSuccessfulFetchTimestampMillis(timestampMillis);\n  }\n  setActiveConfig(activeConfig) {\n    this.activeConfig = activeConfig;\n    return this.storage.setActiveConfig(activeConfig);\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction registerRemoteConfig() {\n  _registerComponent(new Component(RC_COMPONENT_NAME, remoteConfigFactory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n  function remoteConfigFactory(container, {\n    instanceIdentifier: namespace\n  }) {\n    /* Dependencies */\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    // The following call will always succeed because rc has `import '@firebase/installations'`\n    const installations = container.getProvider('installations-internal').getImmediate();\n    // Guards against the SDK being used in non-browser environments.\n    if (typeof window === 'undefined') {\n      throw ERROR_FACTORY.create(\"registration-window\" /* ErrorCode.REGISTRATION_WINDOW */);\n    }\n    // Guards against the SDK being used when indexedDB is not available.\n    if (!isIndexedDBAvailable()) {\n      throw ERROR_FACTORY.create(\"indexed-db-unavailable\" /* ErrorCode.INDEXED_DB_UNAVAILABLE */);\n    }\n    // Normalizes optional inputs.\n    const {\n      projectId,\n      apiKey,\n      appId\n    } = app.options;\n    if (!projectId) {\n      throw ERROR_FACTORY.create(\"registration-project-id\" /* ErrorCode.REGISTRATION_PROJECT_ID */);\n    }\n\n    if (!apiKey) {\n      throw ERROR_FACTORY.create(\"registration-api-key\" /* ErrorCode.REGISTRATION_API_KEY */);\n    }\n\n    if (!appId) {\n      throw ERROR_FACTORY.create(\"registration-app-id\" /* ErrorCode.REGISTRATION_APP_ID */);\n    }\n\n    namespace = namespace || 'firebase';\n    const storage = new Storage(appId, app.name, namespace);\n    const storageCache = new StorageCache(storage);\n    const logger = new Logger(name);\n    // Sets ERROR as the default log level.\n    // See RemoteConfig#setLogLevel for corresponding normalization to ERROR log level.\n    logger.logLevel = LogLevel.ERROR;\n    const restClient = new RestClient(installations,\n    // Uses the JS SDK version, by which the RC package version can be deduced, if necessary.\n    SDK_VERSION, namespace, projectId, apiKey, appId);\n    const retryingClient = new RetryingClient(restClient, storage);\n    const cachingClient = new CachingClient(retryingClient, storage, storageCache, logger);\n    const remoteConfigInstance = new RemoteConfig(app, cachingClient, storageCache, storage, logger);\n    // Starts warming cache.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    ensureInitialized(remoteConfigInstance);\n    return remoteConfigInstance;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// This API is put in a separate file, so we can stub fetchConfig and activate in tests.\n// It's not possible to stub standalone functions from the same module.\n/**\r\n *\r\n * Performs fetch and activate operations, as a convenience.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n *\r\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\r\n * If the fetched configs were already activated, the `Promise` will resolve to false.\r\n *\r\n * @public\r\n */\nfunction fetchAndActivate(_x3) {\n  return _fetchAndActivate.apply(this, arguments);\n}\n/**\r\n * This method provides two different checks:\r\n *\r\n * 1. Check if IndexedDB exists in the browser environment.\r\n * 2. Check if the current browser context allows IndexedDB `open()` calls.\r\n *\r\n * @returns A `Promise` which resolves to true if a {@link RemoteConfig} instance\r\n * can be initialized in this environment, or false if it cannot.\r\n * @public\r\n */\nfunction _fetchAndActivate() {\n  _fetchAndActivate = _asyncToGenerator(function* (remoteConfig) {\n    remoteConfig = getModularInstance(remoteConfig);\n    yield fetchConfig(remoteConfig);\n    return activate(remoteConfig);\n  });\n  return _fetchAndActivate.apply(this, arguments);\n}\nfunction isSupported() {\n  return _isSupported.apply(this, arguments);\n}\n/**\r\n * Firebase Remote Config\r\n *\r\n * @packageDocumentation\r\n */\n/** register component and version */\nfunction _isSupported() {\n  _isSupported = _asyncToGenerator(function* () {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    }\n    try {\n      const isDBOpenable = yield validateIndexedDBOpenable();\n      return isDBOpenable;\n    } catch (error) {\n      return false;\n    }\n  });\n  return _isSupported.apply(this, arguments);\n}\nregisterRemoteConfig();\nexport { activate, ensureInitialized, fetchAndActivate, fetchConfig, getAll, getBoolean, getNumber, getRemoteConfig, getString, getValue, isSupported, setLogLevel };", "map": {"version": 3, "names": ["_get<PERSON><PERSON><PERSON>", "getApp", "_registerComponent", "registerVersion", "SDK_VERSION", "ErrorFactory", "FirebaseError", "getModularInstance", "calculateBackoffMillis", "isIndexedDBAvailable", "validateIndexedDBOpenable", "Component", "LogLevel", "<PERSON><PERSON>", "name", "version", "RemoteConfigAbortSignal", "constructor", "listeners", "addEventListener", "listener", "push", "abort", "for<PERSON>ach", "RC_COMPONENT_NAME", "ERROR_DESCRIPTION_MAP", "ERROR_FACTORY", "hasErrorCode", "e", "errorCode", "code", "indexOf", "DEFAULT_VALUE_FOR_BOOLEAN", "DEFAULT_VALUE_FOR_STRING", "DEFAULT_VALUE_FOR_NUMBER", "BOOLEAN_TRUTHY_VALUES", "Value", "_source", "_value", "asString", "asBoolean", "toLowerCase", "asNumber", "num", "Number", "isNaN", "getSource", "getRemoteConfig", "app", "rc<PERSON><PERSON><PERSON>", "getImmediate", "activate", "_x", "_activate", "apply", "arguments", "_asyncToGenerator", "remoteConfig", "rc", "lastSuccessfulFetchResponse", "activeConfigEtag", "Promise", "all", "_storage", "getLastSuccessfulFetchResponse", "getActiveConfigEtag", "config", "eTag", "_storageCache", "setActiveConfig", "setActiveConfigEtag", "ensureInitialized", "_initializePromise", "loadFromStorage", "then", "_isInitializationComplete", "fetchConfig", "_x2", "_fetchConfig", "abortSignal", "setTimeout", "settings", "fetchTimeoutMillis", "_client", "fetch", "cacheMaxAgeMillis", "minimumFetchIntervalMillis", "signal", "setLastFetchStatus", "lastFetchStatus", "getAll", "getAllKeys", "getActiveConfig", "defaultConfig", "reduce", "allConfigs", "key", "getValue", "getBoolean", "getNumber", "getString", "_logger", "debug", "activeConfig", "undefined", "String", "setLogLevel", "logLevel", "DEBUG", "SILENT", "ERROR", "obj1", "obj2", "Object", "keys", "assign", "CachingClient", "client", "storage", "storageCache", "logger", "isCachedDataFresh", "lastSuccessfulFetchTimestampMillis", "cacheAgeMillis", "Date", "now", "request", "_this", "getLastSuccessfulFetchTimestampMillis", "response", "storageOperations", "setLastSuccessfulFetchTimestampMillis", "status", "setLastSuccessfulFetchResponse", "getUserLanguage", "navigator<PERSON><PERSON><PERSON>ge", "navigator", "languages", "language", "RestClient", "firebaseInstallations", "sdkVersion", "namespace", "projectId", "<PERSON><PERSON><PERSON><PERSON>", "appId", "_this2", "installationId", "installationToken", "getId", "getToken", "urlBase", "window", "FIREBASE_REMOTE_CONFIG_URL_BASE", "url", "headers", "requestBody", "sdk_version", "app_instance_id", "app_instance_id_token", "app_id", "language_code", "options", "method", "body", "JSON", "stringify", "fetchPromise", "timeoutPromise", "_resolve", "reject", "error", "Error", "race", "originalError", "create", "originalErrorMessage", "message", "responseEtag", "get", "state", "responseBody", "json", "httpStatus", "setAbortableTimeout", "throttleEnd<PERSON>imeMill<PERSON>", "resolve", "backoff<PERSON><PERSON><PERSON>", "Math", "max", "timeout", "clearTimeout", "isRetriableError", "customData", "RetryingClient", "_this3", "throttleMetadata", "getThrottleMetadata", "backoffCount", "attemptFetch", "_this4", "deleteThrottleMetadata", "setThrottleMetadata", "DEFAULT_FETCH_TIMEOUT_MILLIS", "DEFAULT_CACHE_MAX_AGE_MILLIS", "RemoteConfig", "fetchTimeMillis", "getLastFetchStatus", "toFirebaseError", "event", "target", "APP_NAMESPACE_STORE", "DB_NAME", "DB_VERSION", "openDatabase", "indexedDB", "open", "onerror", "onsuccess", "result", "onupgradeneeded", "db", "oldVersion", "createObjectStore", "keyP<PERSON>", "Storage", "appName", "openDbPromise", "set", "timestamp", "etag", "metadata", "delete", "_this5", "transaction", "objectStore", "compositeKey", "createCompositeKey", "value", "_this6", "put", "_this7", "join", "StorageCache", "_this8", "lastFetchStatusPromise", "lastSuccessfulFetchTimestampMillisPromise", "activeConfigPromise", "timestampMillis", "registerRemoteConfig", "remoteConfigFactory", "setMultipleInstances", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "installations", "restClient", "retryingClient", "cachingClient", "remoteConfigInstance", "fetchAndActivate", "_x3", "_fetchAndActivate", "isSupported", "_isSupported", "isDBOpenable"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/node_modules/@firebase/remote-config/dist/esm/index.esm2017.js"], "sourcesContent": ["import { _getProvider, getApp, _registerComponent, registerVersion, SDK_VERSION } from '@firebase/app';\nimport { ErrorFactory, FirebaseError, getModularInstance, calculateBackoffMillis, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport { LogLevel, Logger } from '@firebase/logger';\nimport '@firebase/installations';\n\nconst name = \"@firebase/remote-config\";\nconst version = \"0.4.4\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Shims a minimal AbortSignal.\r\n *\r\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\r\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\r\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\r\n * swapped out if/when we do.\r\n */\r\nclass RemoteConfigAbortSignal {\r\n    constructor() {\r\n        this.listeners = [];\r\n    }\r\n    addEventListener(listener) {\r\n        this.listeners.push(listener);\r\n    }\r\n    abort() {\r\n        this.listeners.forEach(listener => listener());\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst RC_COMPONENT_NAME = 'remote-config';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERROR_DESCRIPTION_MAP = {\r\n    [\"registration-window\" /* ErrorCode.REGISTRATION_WINDOW */]: 'Undefined window object. This SDK only supports usage in a browser environment.',\r\n    [\"registration-project-id\" /* ErrorCode.REGISTRATION_PROJECT_ID */]: 'Undefined project identifier. Check Firebase app initialization.',\r\n    [\"registration-api-key\" /* ErrorCode.REGISTRATION_API_KEY */]: 'Undefined API key. Check Firebase app initialization.',\r\n    [\"registration-app-id\" /* ErrorCode.REGISTRATION_APP_ID */]: 'Undefined app identifier. Check Firebase app initialization.',\r\n    [\"storage-open\" /* ErrorCode.STORAGE_OPEN */]: 'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\r\n    [\"storage-get\" /* ErrorCode.STORAGE_GET */]: 'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\r\n    [\"storage-set\" /* ErrorCode.STORAGE_SET */]: 'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\r\n    [\"storage-delete\" /* ErrorCode.STORAGE_DELETE */]: 'Error thrown when deleting from storage. Original error: {$originalErrorMessage}.',\r\n    [\"fetch-client-network\" /* ErrorCode.FETCH_NETWORK */]: 'Fetch client failed to connect to a network. Check Internet connection.' +\r\n        ' Original error: {$originalErrorMessage}.',\r\n    [\"fetch-timeout\" /* ErrorCode.FETCH_TIMEOUT */]: 'The config fetch request timed out. ' +\r\n        ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.',\r\n    [\"fetch-throttle\" /* ErrorCode.FETCH_THROTTLE */]: 'The config fetch request timed out while in an exponential backoff state.' +\r\n        ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.' +\r\n        ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\r\n    [\"fetch-client-parse\" /* ErrorCode.FETCH_PARSE */]: 'Fetch client could not parse response.' +\r\n        ' Original error: {$originalErrorMessage}.',\r\n    [\"fetch-status\" /* ErrorCode.FETCH_STATUS */]: 'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\r\n    [\"indexed-db-unavailable\" /* ErrorCode.INDEXED_DB_UNAVAILABLE */]: 'Indexed DB is not supported by current browser'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('remoteconfig' /* service */, 'Remote Config' /* service name */, ERROR_DESCRIPTION_MAP);\r\n// Note how this is like typeof/instanceof, but for ErrorCode.\r\nfunction hasErrorCode(e, errorCode) {\r\n    return e instanceof FirebaseError && e.code.indexOf(errorCode) !== -1;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DEFAULT_VALUE_FOR_BOOLEAN = false;\r\nconst DEFAULT_VALUE_FOR_STRING = '';\r\nconst DEFAULT_VALUE_FOR_NUMBER = 0;\r\nconst BOOLEAN_TRUTHY_VALUES = ['1', 'true', 't', 'yes', 'y', 'on'];\r\nclass Value {\r\n    constructor(_source, _value = DEFAULT_VALUE_FOR_STRING) {\r\n        this._source = _source;\r\n        this._value = _value;\r\n    }\r\n    asString() {\r\n        return this._value;\r\n    }\r\n    asBoolean() {\r\n        if (this._source === 'static') {\r\n            return DEFAULT_VALUE_FOR_BOOLEAN;\r\n        }\r\n        return BOOLEAN_TRUTHY_VALUES.indexOf(this._value.toLowerCase()) >= 0;\r\n    }\r\n    asNumber() {\r\n        if (this._source === 'static') {\r\n            return DEFAULT_VALUE_FOR_NUMBER;\r\n        }\r\n        let num = Number(this._value);\r\n        if (isNaN(num)) {\r\n            num = DEFAULT_VALUE_FOR_NUMBER;\r\n        }\r\n        return num;\r\n    }\r\n    getSource() {\r\n        return this._source;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n * @returns A {@link RemoteConfig} instance.\r\n *\r\n * @public\r\n */\r\nfunction getRemoteConfig(app = getApp()) {\r\n    app = getModularInstance(app);\r\n    const rcProvider = _getProvider(app, RC_COMPONENT_NAME);\r\n    return rcProvider.getImmediate();\r\n}\r\n/**\r\n * Makes the last fetched config available to the getters.\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\r\n * If the fetched configs were already activated, the `Promise` will resolve to false.\r\n *\r\n * @public\r\n */\r\nasync function activate(remoteConfig) {\r\n    const rc = getModularInstance(remoteConfig);\r\n    const [lastSuccessfulFetchResponse, activeConfigEtag] = await Promise.all([\r\n        rc._storage.getLastSuccessfulFetchResponse(),\r\n        rc._storage.getActiveConfigEtag()\r\n    ]);\r\n    if (!lastSuccessfulFetchResponse ||\r\n        !lastSuccessfulFetchResponse.config ||\r\n        !lastSuccessfulFetchResponse.eTag ||\r\n        lastSuccessfulFetchResponse.eTag === activeConfigEtag) {\r\n        // Either there is no successful fetched config, or is the same as current active\r\n        // config.\r\n        return false;\r\n    }\r\n    await Promise.all([\r\n        rc._storageCache.setActiveConfig(lastSuccessfulFetchResponse.config),\r\n        rc._storage.setActiveConfigEtag(lastSuccessfulFetchResponse.eTag)\r\n    ]);\r\n    return true;\r\n}\r\n/**\r\n * Ensures the last activated config are available to the getters.\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n *\r\n * @returns A `Promise` that resolves when the last activated config is available to the getters.\r\n * @public\r\n */\r\nfunction ensureInitialized(remoteConfig) {\r\n    const rc = getModularInstance(remoteConfig);\r\n    if (!rc._initializePromise) {\r\n        rc._initializePromise = rc._storageCache.loadFromStorage().then(() => {\r\n            rc._isInitializationComplete = true;\r\n        });\r\n    }\r\n    return rc._initializePromise;\r\n}\r\n/**\r\n * Fetches and caches configuration from the Remote Config service.\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @public\r\n */\r\nasync function fetchConfig(remoteConfig) {\r\n    const rc = getModularInstance(remoteConfig);\r\n    // Aborts the request after the given timeout, causing the fetch call to\r\n    // reject with an `AbortError`.\r\n    //\r\n    // <p>Aborting after the request completes is a no-op, so we don't need a\r\n    // corresponding `clearTimeout`.\r\n    //\r\n    // Locating abort logic here because:\r\n    // * it uses a developer setting (timeout)\r\n    // * it applies to all retries (like curl's max-time arg)\r\n    // * it is consistent with the Fetch API's signal input\r\n    const abortSignal = new RemoteConfigAbortSignal();\r\n    setTimeout(async () => {\r\n        // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\r\n        abortSignal.abort();\r\n    }, rc.settings.fetchTimeoutMillis);\r\n    // Catches *all* errors thrown by client so status can be set consistently.\r\n    try {\r\n        await rc._client.fetch({\r\n            cacheMaxAgeMillis: rc.settings.minimumFetchIntervalMillis,\r\n            signal: abortSignal\r\n        });\r\n        await rc._storageCache.setLastFetchStatus('success');\r\n    }\r\n    catch (e) {\r\n        const lastFetchStatus = hasErrorCode(e, \"fetch-throttle\" /* ErrorCode.FETCH_THROTTLE */)\r\n            ? 'throttle'\r\n            : 'failure';\r\n        await rc._storageCache.setLastFetchStatus(lastFetchStatus);\r\n        throw e;\r\n    }\r\n}\r\n/**\r\n * Gets all config.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @returns All config.\r\n *\r\n * @public\r\n */\r\nfunction getAll(remoteConfig) {\r\n    const rc = getModularInstance(remoteConfig);\r\n    return getAllKeys(rc._storageCache.getActiveConfig(), rc.defaultConfig).reduce((allConfigs, key) => {\r\n        allConfigs[key] = getValue(remoteConfig, key);\r\n        return allConfigs;\r\n    }, {});\r\n}\r\n/**\r\n * Gets the value for the given key as a boolean.\r\n *\r\n * Convenience method for calling <code>remoteConfig.getValue(key).asBoolean()</code>.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key as a boolean.\r\n * @public\r\n */\r\nfunction getBoolean(remoteConfig, key) {\r\n    return getValue(getModularInstance(remoteConfig), key).asBoolean();\r\n}\r\n/**\r\n * Gets the value for the given key as a number.\r\n *\r\n * Convenience method for calling <code>remoteConfig.getValue(key).asNumber()</code>.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key as a number.\r\n *\r\n * @public\r\n */\r\nfunction getNumber(remoteConfig, key) {\r\n    return getValue(getModularInstance(remoteConfig), key).asNumber();\r\n}\r\n/**\r\n * Gets the value for the given key as a string.\r\n * Convenience method for calling <code>remoteConfig.getValue(key).asString()</code>.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key as a string.\r\n *\r\n * @public\r\n */\r\nfunction getString(remoteConfig, key) {\r\n    return getValue(getModularInstance(remoteConfig), key).asString();\r\n}\r\n/**\r\n * Gets the {@link Value} for the given key.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param key - The name of the parameter.\r\n *\r\n * @returns The value for the given key.\r\n *\r\n * @public\r\n */\r\nfunction getValue(remoteConfig, key) {\r\n    const rc = getModularInstance(remoteConfig);\r\n    if (!rc._isInitializationComplete) {\r\n        rc._logger.debug(`A value was requested for key \"${key}\" before SDK initialization completed.` +\r\n            ' Await on ensureInitialized if the intent was to get a previously activated value.');\r\n    }\r\n    const activeConfig = rc._storageCache.getActiveConfig();\r\n    if (activeConfig && activeConfig[key] !== undefined) {\r\n        return new Value('remote', activeConfig[key]);\r\n    }\r\n    else if (rc.defaultConfig && rc.defaultConfig[key] !== undefined) {\r\n        return new Value('default', String(rc.defaultConfig[key]));\r\n    }\r\n    rc._logger.debug(`Returning static value for key \"${key}\".` +\r\n        ' Define a default or remote value if this is unintentional.');\r\n    return new Value('static');\r\n}\r\n/**\r\n * Defines the log level to use.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n * @param logLevel - The log level to set.\r\n *\r\n * @public\r\n */\r\nfunction setLogLevel(remoteConfig, logLevel) {\r\n    const rc = getModularInstance(remoteConfig);\r\n    switch (logLevel) {\r\n        case 'debug':\r\n            rc._logger.logLevel = LogLevel.DEBUG;\r\n            break;\r\n        case 'silent':\r\n            rc._logger.logLevel = LogLevel.SILENT;\r\n            break;\r\n        default:\r\n            rc._logger.logLevel = LogLevel.ERROR;\r\n    }\r\n}\r\n/**\r\n * Dedupes and returns an array of all the keys of the received objects.\r\n */\r\nfunction getAllKeys(obj1 = {}, obj2 = {}) {\r\n    return Object.keys(Object.assign(Object.assign({}, obj1), obj2));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Implements the {@link RemoteConfigClient} abstraction with success response caching.\r\n *\r\n * <p>Comparable to the browser's Cache API for responses, but the Cache API requires a Service\r\n * Worker, which requires HTTPS, which would significantly complicate SDK installation. Also, the\r\n * Cache API doesn't support matching entries by time.\r\n */\r\nclass CachingClient {\r\n    constructor(client, storage, storageCache, logger) {\r\n        this.client = client;\r\n        this.storage = storage;\r\n        this.storageCache = storageCache;\r\n        this.logger = logger;\r\n    }\r\n    /**\r\n     * Returns true if the age of the cached fetched configs is less than or equal to\r\n     * {@link Settings#minimumFetchIntervalInSeconds}.\r\n     *\r\n     * <p>This is comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the\r\n     * native Fetch API.\r\n     *\r\n     * <p>Visible for testing.\r\n     */\r\n    isCachedDataFresh(cacheMaxAgeMillis, lastSuccessfulFetchTimestampMillis) {\r\n        // Cache can only be fresh if it's populated.\r\n        if (!lastSuccessfulFetchTimestampMillis) {\r\n            this.logger.debug('Config fetch cache check. Cache unpopulated.');\r\n            return false;\r\n        }\r\n        // Calculates age of cache entry.\r\n        const cacheAgeMillis = Date.now() - lastSuccessfulFetchTimestampMillis;\r\n        const isCachedDataFresh = cacheAgeMillis <= cacheMaxAgeMillis;\r\n        this.logger.debug('Config fetch cache check.' +\r\n            ` Cache age millis: ${cacheAgeMillis}.` +\r\n            ` Cache max age millis (minimumFetchIntervalMillis setting): ${cacheMaxAgeMillis}.` +\r\n            ` Is cache hit: ${isCachedDataFresh}.`);\r\n        return isCachedDataFresh;\r\n    }\r\n    async fetch(request) {\r\n        // Reads from persisted storage to avoid cache miss if callers don't wait on initialization.\r\n        const [lastSuccessfulFetchTimestampMillis, lastSuccessfulFetchResponse] = await Promise.all([\r\n            this.storage.getLastSuccessfulFetchTimestampMillis(),\r\n            this.storage.getLastSuccessfulFetchResponse()\r\n        ]);\r\n        // Exits early on cache hit.\r\n        if (lastSuccessfulFetchResponse &&\r\n            this.isCachedDataFresh(request.cacheMaxAgeMillis, lastSuccessfulFetchTimestampMillis)) {\r\n            return lastSuccessfulFetchResponse;\r\n        }\r\n        // Deviates from pure decorator by not honoring a passed ETag since we don't have a public API\r\n        // that allows the caller to pass an ETag.\r\n        request.eTag =\r\n            lastSuccessfulFetchResponse && lastSuccessfulFetchResponse.eTag;\r\n        // Falls back to service on cache miss.\r\n        const response = await this.client.fetch(request);\r\n        // Fetch throws for non-success responses, so success is guaranteed here.\r\n        const storageOperations = [\r\n            // Uses write-through cache for consistency with synchronous public API.\r\n            this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())\r\n        ];\r\n        if (response.status === 200) {\r\n            // Caches response only if it has changed, ie non-304 responses.\r\n            storageOperations.push(this.storage.setLastSuccessfulFetchResponse(response));\r\n        }\r\n        await Promise.all(storageOperations);\r\n        return response;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Attempts to get the most accurate browser language setting.\r\n *\r\n * <p>Adapted from getUserLanguage in packages/auth/src/utils.js for TypeScript.\r\n *\r\n * <p>Defers default language specification to server logic for consistency.\r\n *\r\n * @param navigatorLanguage Enables tests to override read-only {@link NavigatorLanguage}.\r\n */\r\nfunction getUserLanguage(navigatorLanguage = navigator) {\r\n    return (\r\n    // Most reliable, but only supported in Chrome/Firefox.\r\n    (navigatorLanguage.languages && navigatorLanguage.languages[0]) ||\r\n        // Supported in most browsers, but returns the language of the browser\r\n        // UI, not the language set in browser settings.\r\n        navigatorLanguage.language\r\n    // Polyfill otherwise.\r\n    );\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Implements the Client abstraction for the Remote Config REST API.\r\n */\r\nclass RestClient {\r\n    constructor(firebaseInstallations, sdkVersion, namespace, projectId, apiKey, appId) {\r\n        this.firebaseInstallations = firebaseInstallations;\r\n        this.sdkVersion = sdkVersion;\r\n        this.namespace = namespace;\r\n        this.projectId = projectId;\r\n        this.apiKey = apiKey;\r\n        this.appId = appId;\r\n    }\r\n    /**\r\n     * Fetches from the Remote Config REST API.\r\n     *\r\n     * @throws a {@link ErrorCode.FETCH_NETWORK} error if {@link GlobalFetch#fetch} can't\r\n     * connect to the network.\r\n     * @throws a {@link ErrorCode.FETCH_PARSE} error if {@link Response#json} can't parse the\r\n     * fetch response.\r\n     * @throws a {@link ErrorCode.FETCH_STATUS} error if the service returns an HTTP error status.\r\n     */\r\n    async fetch(request) {\r\n        const [installationId, installationToken] = await Promise.all([\r\n            this.firebaseInstallations.getId(),\r\n            this.firebaseInstallations.getToken()\r\n        ]);\r\n        const urlBase = window.FIREBASE_REMOTE_CONFIG_URL_BASE ||\r\n            'https://firebaseremoteconfig.googleapis.com';\r\n        const url = `${urlBase}/v1/projects/${this.projectId}/namespaces/${this.namespace}:fetch?key=${this.apiKey}`;\r\n        const headers = {\r\n            'Content-Type': 'application/json',\r\n            'Content-Encoding': 'gzip',\r\n            // Deviates from pure decorator by not passing max-age header since we don't currently have\r\n            // service behavior using that header.\r\n            'If-None-Match': request.eTag || '*'\r\n        };\r\n        const requestBody = {\r\n            /* eslint-disable camelcase */\r\n            sdk_version: this.sdkVersion,\r\n            app_instance_id: installationId,\r\n            app_instance_id_token: installationToken,\r\n            app_id: this.appId,\r\n            language_code: getUserLanguage()\r\n            /* eslint-enable camelcase */\r\n        };\r\n        const options = {\r\n            method: 'POST',\r\n            headers,\r\n            body: JSON.stringify(requestBody)\r\n        };\r\n        // This logic isn't REST-specific, but shimming abort logic isn't worth another decorator.\r\n        const fetchPromise = fetch(url, options);\r\n        const timeoutPromise = new Promise((_resolve, reject) => {\r\n            // Maps async event listener to Promise API.\r\n            request.signal.addEventListener(() => {\r\n                // Emulates https://heycam.github.io/webidl/#aborterror\r\n                const error = new Error('The operation was aborted.');\r\n                error.name = 'AbortError';\r\n                reject(error);\r\n            });\r\n        });\r\n        let response;\r\n        try {\r\n            await Promise.race([fetchPromise, timeoutPromise]);\r\n            response = await fetchPromise;\r\n        }\r\n        catch (originalError) {\r\n            let errorCode = \"fetch-client-network\" /* ErrorCode.FETCH_NETWORK */;\r\n            if ((originalError === null || originalError === void 0 ? void 0 : originalError.name) === 'AbortError') {\r\n                errorCode = \"fetch-timeout\" /* ErrorCode.FETCH_TIMEOUT */;\r\n            }\r\n            throw ERROR_FACTORY.create(errorCode, {\r\n                originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\r\n            });\r\n        }\r\n        let status = response.status;\r\n        // Normalizes nullable header to optional.\r\n        const responseEtag = response.headers.get('ETag') || undefined;\r\n        let config;\r\n        let state;\r\n        // JSON parsing throws SyntaxError if the response body isn't a JSON string.\r\n        // Requesting application/json and checking for a 200 ensures there's JSON data.\r\n        if (response.status === 200) {\r\n            let responseBody;\r\n            try {\r\n                responseBody = await response.json();\r\n            }\r\n            catch (originalError) {\r\n                throw ERROR_FACTORY.create(\"fetch-client-parse\" /* ErrorCode.FETCH_PARSE */, {\r\n                    originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\r\n                });\r\n            }\r\n            config = responseBody['entries'];\r\n            state = responseBody['state'];\r\n        }\r\n        // Normalizes based on legacy state.\r\n        if (state === 'INSTANCE_STATE_UNSPECIFIED') {\r\n            status = 500;\r\n        }\r\n        else if (state === 'NO_CHANGE') {\r\n            status = 304;\r\n        }\r\n        else if (state === 'NO_TEMPLATE' || state === 'EMPTY_CONFIG') {\r\n            // These cases can be fixed remotely, so normalize to safe value.\r\n            config = {};\r\n        }\r\n        // Normalize to exception-based control flow for non-success cases.\r\n        // Encapsulates HTTP specifics in this class as much as possible. Status is still the best for\r\n        // differentiating success states (200 from 304; the state body param is undefined in a\r\n        // standard 304).\r\n        if (status !== 304 && status !== 200) {\r\n            throw ERROR_FACTORY.create(\"fetch-status\" /* ErrorCode.FETCH_STATUS */, {\r\n                httpStatus: status\r\n            });\r\n        }\r\n        return { status, eTag: responseEtag, config };\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Supports waiting on a backoff by:\r\n *\r\n * <ul>\r\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\r\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\r\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\r\n *       request appear the same.</li>\r\n * </ul>\r\n *\r\n * <p>Visible for testing.\r\n */\r\nfunction setAbortableTimeout(signal, throttleEndTimeMillis) {\r\n    return new Promise((resolve, reject) => {\r\n        // Derives backoff from given end time, normalizing negative numbers to zero.\r\n        const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\r\n        const timeout = setTimeout(resolve, backoffMillis);\r\n        // Adds listener, rather than sets onabort, because signal is a shared object.\r\n        signal.addEventListener(() => {\r\n            clearTimeout(timeout);\r\n            // If the request completes before this timeout, the rejection has no effect.\r\n            reject(ERROR_FACTORY.create(\"fetch-throttle\" /* ErrorCode.FETCH_THROTTLE */, {\r\n                throttleEndTimeMillis\r\n            }));\r\n        });\r\n    });\r\n}\r\n/**\r\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\r\n */\r\nfunction isRetriableError(e) {\r\n    if (!(e instanceof FirebaseError) || !e.customData) {\r\n        return false;\r\n    }\r\n    // Uses string index defined by ErrorData, which FirebaseError implements.\r\n    const httpStatus = Number(e.customData['httpStatus']);\r\n    return (httpStatus === 429 ||\r\n        httpStatus === 500 ||\r\n        httpStatus === 503 ||\r\n        httpStatus === 504);\r\n}\r\n/**\r\n * Decorates a Client with retry logic.\r\n *\r\n * <p>Comparable to CachingClient, but uses backoff logic instead of cache max age and doesn't cache\r\n * responses (because the SDK has no use for error responses).\r\n */\r\nclass RetryingClient {\r\n    constructor(client, storage) {\r\n        this.client = client;\r\n        this.storage = storage;\r\n    }\r\n    async fetch(request) {\r\n        const throttleMetadata = (await this.storage.getThrottleMetadata()) || {\r\n            backoffCount: 0,\r\n            throttleEndTimeMillis: Date.now()\r\n        };\r\n        return this.attemptFetch(request, throttleMetadata);\r\n    }\r\n    /**\r\n     * A recursive helper for attempting a fetch request repeatedly.\r\n     *\r\n     * @throws any non-retriable errors.\r\n     */\r\n    async attemptFetch(request, { throttleEndTimeMillis, backoffCount }) {\r\n        // Starts with a (potentially zero) timeout to support resumption from stored state.\r\n        // Ensures the throttle end time is honored if the last attempt timed out.\r\n        // Note the SDK will never make a request if the fetch timeout expires at this point.\r\n        await setAbortableTimeout(request.signal, throttleEndTimeMillis);\r\n        try {\r\n            const response = await this.client.fetch(request);\r\n            // Note the SDK only clears throttle state if response is success or non-retriable.\r\n            await this.storage.deleteThrottleMetadata();\r\n            return response;\r\n        }\r\n        catch (e) {\r\n            if (!isRetriableError(e)) {\r\n                throw e;\r\n            }\r\n            // Increments backoff state.\r\n            const throttleMetadata = {\r\n                throttleEndTimeMillis: Date.now() + calculateBackoffMillis(backoffCount),\r\n                backoffCount: backoffCount + 1\r\n            };\r\n            // Persists state.\r\n            await this.storage.setThrottleMetadata(throttleMetadata);\r\n            return this.attemptFetch(request, throttleMetadata);\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DEFAULT_FETCH_TIMEOUT_MILLIS = 60 * 1000; // One minute\r\nconst DEFAULT_CACHE_MAX_AGE_MILLIS = 12 * 60 * 60 * 1000; // Twelve hours.\r\n/**\r\n * Encapsulates business logic mapping network and storage dependencies to the public SDK API.\r\n *\r\n * See {@link https://github.com/FirebasePrivate/firebase-js-sdk/blob/master/packages/firebase/index.d.ts|interface documentation} for method descriptions.\r\n */\r\nclass RemoteConfig {\r\n    constructor(\r\n    // Required by FirebaseServiceFactory interface.\r\n    app, \r\n    // JS doesn't support private yet\r\n    // (https://github.com/tc39/proposal-class-fields#private-fields), so we hint using an\r\n    // underscore prefix.\r\n    /**\r\n     * @internal\r\n     */\r\n    _client, \r\n    /**\r\n     * @internal\r\n     */\r\n    _storageCache, \r\n    /**\r\n     * @internal\r\n     */\r\n    _storage, \r\n    /**\r\n     * @internal\r\n     */\r\n    _logger) {\r\n        this.app = app;\r\n        this._client = _client;\r\n        this._storageCache = _storageCache;\r\n        this._storage = _storage;\r\n        this._logger = _logger;\r\n        /**\r\n         * Tracks completion of initialization promise.\r\n         * @internal\r\n         */\r\n        this._isInitializationComplete = false;\r\n        this.settings = {\r\n            fetchTimeoutMillis: DEFAULT_FETCH_TIMEOUT_MILLIS,\r\n            minimumFetchIntervalMillis: DEFAULT_CACHE_MAX_AGE_MILLIS\r\n        };\r\n        this.defaultConfig = {};\r\n    }\r\n    get fetchTimeMillis() {\r\n        return this._storageCache.getLastSuccessfulFetchTimestampMillis() || -1;\r\n    }\r\n    get lastFetchStatus() {\r\n        return this._storageCache.getLastFetchStatus() || 'no-fetch-yet';\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Converts an error event associated with a {@link IDBRequest} to a {@link FirebaseError}.\r\n */\r\nfunction toFirebaseError(event, errorCode) {\r\n    const originalError = event.target.error || undefined;\r\n    return ERROR_FACTORY.create(errorCode, {\r\n        originalErrorMessage: originalError && (originalError === null || originalError === void 0 ? void 0 : originalError.message)\r\n    });\r\n}\r\n/**\r\n * A general-purpose store keyed by app + namespace + {@link\r\n * ProjectNamespaceKeyFieldValue}.\r\n *\r\n * <p>The Remote Config SDK can be used with multiple app installations, and each app can interact\r\n * with multiple namespaces, so this store uses app (ID + name) and namespace as common parent keys\r\n * for a set of key-value pairs. See {@link Storage#createCompositeKey}.\r\n *\r\n * <p>Visible for testing.\r\n */\r\nconst APP_NAMESPACE_STORE = 'app_namespace_store';\r\nconst DB_NAME = 'firebase_remote_config';\r\nconst DB_VERSION = 1;\r\n// Visible for testing.\r\nfunction openDatabase() {\r\n    return new Promise((resolve, reject) => {\r\n        try {\r\n            const request = indexedDB.open(DB_NAME, DB_VERSION);\r\n            request.onerror = event => {\r\n                reject(toFirebaseError(event, \"storage-open\" /* ErrorCode.STORAGE_OPEN */));\r\n            };\r\n            request.onsuccess = event => {\r\n                resolve(event.target.result);\r\n            };\r\n            request.onupgradeneeded = event => {\r\n                const db = event.target.result;\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (event.oldVersion) {\r\n                    case 0:\r\n                        db.createObjectStore(APP_NAMESPACE_STORE, {\r\n                            keyPath: 'compositeKey'\r\n                        });\r\n                }\r\n            };\r\n        }\r\n        catch (error) {\r\n            reject(ERROR_FACTORY.create(\"storage-open\" /* ErrorCode.STORAGE_OPEN */, {\r\n                originalErrorMessage: error === null || error === void 0 ? void 0 : error.message\r\n            }));\r\n        }\r\n    });\r\n}\r\n/**\r\n * Abstracts data persistence.\r\n */\r\nclass Storage {\r\n    /**\r\n     * @param appId enables storage segmentation by app (ID + name).\r\n     * @param appName enables storage segmentation by app (ID + name).\r\n     * @param namespace enables storage segmentation by namespace.\r\n     */\r\n    constructor(appId, appName, namespace, openDbPromise = openDatabase()) {\r\n        this.appId = appId;\r\n        this.appName = appName;\r\n        this.namespace = namespace;\r\n        this.openDbPromise = openDbPromise;\r\n    }\r\n    getLastFetchStatus() {\r\n        return this.get('last_fetch_status');\r\n    }\r\n    setLastFetchStatus(status) {\r\n        return this.set('last_fetch_status', status);\r\n    }\r\n    // This is comparable to a cache entry timestamp. If we need to expire other data, we could\r\n    // consider adding timestamp to all storage records and an optional max age arg to getters.\r\n    getLastSuccessfulFetchTimestampMillis() {\r\n        return this.get('last_successful_fetch_timestamp_millis');\r\n    }\r\n    setLastSuccessfulFetchTimestampMillis(timestamp) {\r\n        return this.set('last_successful_fetch_timestamp_millis', timestamp);\r\n    }\r\n    getLastSuccessfulFetchResponse() {\r\n        return this.get('last_successful_fetch_response');\r\n    }\r\n    setLastSuccessfulFetchResponse(response) {\r\n        return this.set('last_successful_fetch_response', response);\r\n    }\r\n    getActiveConfig() {\r\n        return this.get('active_config');\r\n    }\r\n    setActiveConfig(config) {\r\n        return this.set('active_config', config);\r\n    }\r\n    getActiveConfigEtag() {\r\n        return this.get('active_config_etag');\r\n    }\r\n    setActiveConfigEtag(etag) {\r\n        return this.set('active_config_etag', etag);\r\n    }\r\n    getThrottleMetadata() {\r\n        return this.get('throttle_metadata');\r\n    }\r\n    setThrottleMetadata(metadata) {\r\n        return this.set('throttle_metadata', metadata);\r\n    }\r\n    deleteThrottleMetadata() {\r\n        return this.delete('throttle_metadata');\r\n    }\r\n    async get(key) {\r\n        const db = await this.openDbPromise;\r\n        return new Promise((resolve, reject) => {\r\n            const transaction = db.transaction([APP_NAMESPACE_STORE], 'readonly');\r\n            const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\r\n            const compositeKey = this.createCompositeKey(key);\r\n            try {\r\n                const request = objectStore.get(compositeKey);\r\n                request.onerror = event => {\r\n                    reject(toFirebaseError(event, \"storage-get\" /* ErrorCode.STORAGE_GET */));\r\n                };\r\n                request.onsuccess = event => {\r\n                    const result = event.target.result;\r\n                    if (result) {\r\n                        resolve(result.value);\r\n                    }\r\n                    else {\r\n                        resolve(undefined);\r\n                    }\r\n                };\r\n            }\r\n            catch (e) {\r\n                reject(ERROR_FACTORY.create(\"storage-get\" /* ErrorCode.STORAGE_GET */, {\r\n                    originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n                }));\r\n            }\r\n        });\r\n    }\r\n    async set(key, value) {\r\n        const db = await this.openDbPromise;\r\n        return new Promise((resolve, reject) => {\r\n            const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\r\n            const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\r\n            const compositeKey = this.createCompositeKey(key);\r\n            try {\r\n                const request = objectStore.put({\r\n                    compositeKey,\r\n                    value\r\n                });\r\n                request.onerror = (event) => {\r\n                    reject(toFirebaseError(event, \"storage-set\" /* ErrorCode.STORAGE_SET */));\r\n                };\r\n                request.onsuccess = () => {\r\n                    resolve();\r\n                };\r\n            }\r\n            catch (e) {\r\n                reject(ERROR_FACTORY.create(\"storage-set\" /* ErrorCode.STORAGE_SET */, {\r\n                    originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n                }));\r\n            }\r\n        });\r\n    }\r\n    async delete(key) {\r\n        const db = await this.openDbPromise;\r\n        return new Promise((resolve, reject) => {\r\n            const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\r\n            const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\r\n            const compositeKey = this.createCompositeKey(key);\r\n            try {\r\n                const request = objectStore.delete(compositeKey);\r\n                request.onerror = (event) => {\r\n                    reject(toFirebaseError(event, \"storage-delete\" /* ErrorCode.STORAGE_DELETE */));\r\n                };\r\n                request.onsuccess = () => {\r\n                    resolve();\r\n                };\r\n            }\r\n            catch (e) {\r\n                reject(ERROR_FACTORY.create(\"storage-delete\" /* ErrorCode.STORAGE_DELETE */, {\r\n                    originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n                }));\r\n            }\r\n        });\r\n    }\r\n    // Facilitates composite key functionality (which is unsupported in IE).\r\n    createCompositeKey(key) {\r\n        return [this.appId, this.appName, this.namespace, key].join();\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * A memory cache layer over storage to support the SDK's synchronous read requirements.\r\n */\r\nclass StorageCache {\r\n    constructor(storage) {\r\n        this.storage = storage;\r\n    }\r\n    /**\r\n     * Memory-only getters\r\n     */\r\n    getLastFetchStatus() {\r\n        return this.lastFetchStatus;\r\n    }\r\n    getLastSuccessfulFetchTimestampMillis() {\r\n        return this.lastSuccessfulFetchTimestampMillis;\r\n    }\r\n    getActiveConfig() {\r\n        return this.activeConfig;\r\n    }\r\n    /**\r\n     * Read-ahead getter\r\n     */\r\n    async loadFromStorage() {\r\n        const lastFetchStatusPromise = this.storage.getLastFetchStatus();\r\n        const lastSuccessfulFetchTimestampMillisPromise = this.storage.getLastSuccessfulFetchTimestampMillis();\r\n        const activeConfigPromise = this.storage.getActiveConfig();\r\n        // Note:\r\n        // 1. we consistently check for undefined to avoid clobbering defined values\r\n        //   in memory\r\n        // 2. we defer awaiting to improve readability, as opposed to destructuring\r\n        //   a Promise.all result, for example\r\n        const lastFetchStatus = await lastFetchStatusPromise;\r\n        if (lastFetchStatus) {\r\n            this.lastFetchStatus = lastFetchStatus;\r\n        }\r\n        const lastSuccessfulFetchTimestampMillis = await lastSuccessfulFetchTimestampMillisPromise;\r\n        if (lastSuccessfulFetchTimestampMillis) {\r\n            this.lastSuccessfulFetchTimestampMillis =\r\n                lastSuccessfulFetchTimestampMillis;\r\n        }\r\n        const activeConfig = await activeConfigPromise;\r\n        if (activeConfig) {\r\n            this.activeConfig = activeConfig;\r\n        }\r\n    }\r\n    /**\r\n     * Write-through setters\r\n     */\r\n    setLastFetchStatus(status) {\r\n        this.lastFetchStatus = status;\r\n        return this.storage.setLastFetchStatus(status);\r\n    }\r\n    setLastSuccessfulFetchTimestampMillis(timestampMillis) {\r\n        this.lastSuccessfulFetchTimestampMillis = timestampMillis;\r\n        return this.storage.setLastSuccessfulFetchTimestampMillis(timestampMillis);\r\n    }\r\n    setActiveConfig(activeConfig) {\r\n        this.activeConfig = activeConfig;\r\n        return this.storage.setActiveConfig(activeConfig);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction registerRemoteConfig() {\r\n    _registerComponent(new Component(RC_COMPONENT_NAME, remoteConfigFactory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\r\n    registerVersion(name, version);\r\n    // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\n    registerVersion(name, version, 'esm2017');\r\n    function remoteConfigFactory(container, { instanceIdentifier: namespace }) {\r\n        /* Dependencies */\r\n        // getImmediate for FirebaseApp will always succeed\r\n        const app = container.getProvider('app').getImmediate();\r\n        // The following call will always succeed because rc has `import '@firebase/installations'`\r\n        const installations = container\r\n            .getProvider('installations-internal')\r\n            .getImmediate();\r\n        // Guards against the SDK being used in non-browser environments.\r\n        if (typeof window === 'undefined') {\r\n            throw ERROR_FACTORY.create(\"registration-window\" /* ErrorCode.REGISTRATION_WINDOW */);\r\n        }\r\n        // Guards against the SDK being used when indexedDB is not available.\r\n        if (!isIndexedDBAvailable()) {\r\n            throw ERROR_FACTORY.create(\"indexed-db-unavailable\" /* ErrorCode.INDEXED_DB_UNAVAILABLE */);\r\n        }\r\n        // Normalizes optional inputs.\r\n        const { projectId, apiKey, appId } = app.options;\r\n        if (!projectId) {\r\n            throw ERROR_FACTORY.create(\"registration-project-id\" /* ErrorCode.REGISTRATION_PROJECT_ID */);\r\n        }\r\n        if (!apiKey) {\r\n            throw ERROR_FACTORY.create(\"registration-api-key\" /* ErrorCode.REGISTRATION_API_KEY */);\r\n        }\r\n        if (!appId) {\r\n            throw ERROR_FACTORY.create(\"registration-app-id\" /* ErrorCode.REGISTRATION_APP_ID */);\r\n        }\r\n        namespace = namespace || 'firebase';\r\n        const storage = new Storage(appId, app.name, namespace);\r\n        const storageCache = new StorageCache(storage);\r\n        const logger = new Logger(name);\r\n        // Sets ERROR as the default log level.\r\n        // See RemoteConfig#setLogLevel for corresponding normalization to ERROR log level.\r\n        logger.logLevel = LogLevel.ERROR;\r\n        const restClient = new RestClient(installations, \r\n        // Uses the JS SDK version, by which the RC package version can be deduced, if necessary.\r\n        SDK_VERSION, namespace, projectId, apiKey, appId);\r\n        const retryingClient = new RetryingClient(restClient, storage);\r\n        const cachingClient = new CachingClient(retryingClient, storage, storageCache, logger);\r\n        const remoteConfigInstance = new RemoteConfig(app, cachingClient, storageCache, storage, logger);\r\n        // Starts warming cache.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        ensureInitialized(remoteConfigInstance);\r\n        return remoteConfigInstance;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// This API is put in a separate file, so we can stub fetchConfig and activate in tests.\r\n// It's not possible to stub standalone functions from the same module.\r\n/**\r\n *\r\n * Performs fetch and activate operations, as a convenience.\r\n *\r\n * @param remoteConfig - The {@link RemoteConfig} instance.\r\n *\r\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\r\n * If the fetched configs were already activated, the `Promise` will resolve to false.\r\n *\r\n * @public\r\n */\r\nasync function fetchAndActivate(remoteConfig) {\r\n    remoteConfig = getModularInstance(remoteConfig);\r\n    await fetchConfig(remoteConfig);\r\n    return activate(remoteConfig);\r\n}\r\n/**\r\n * This method provides two different checks:\r\n *\r\n * 1. Check if IndexedDB exists in the browser environment.\r\n * 2. Check if the current browser context allows IndexedDB `open()` calls.\r\n *\r\n * @returns A `Promise` which resolves to true if a {@link RemoteConfig} instance\r\n * can be initialized in this environment, or false if it cannot.\r\n * @public\r\n */\r\nasync function isSupported() {\r\n    if (!isIndexedDBAvailable()) {\r\n        return false;\r\n    }\r\n    try {\r\n        const isDBOpenable = await validateIndexedDBOpenable();\r\n        return isDBOpenable;\r\n    }\r\n    catch (error) {\r\n        return false;\r\n    }\r\n}\n\n/**\r\n * Firebase Remote Config\r\n *\r\n * @packageDocumentation\r\n */\r\n/** register component and version */\r\nregisterRemoteConfig();\n\nexport { activate, ensureInitialized, fetchAndActivate, fetchConfig, getAll, getBoolean, getNumber, getRemoteConfig, getString, getValue, isSupported, setLogLevel };\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,WAAW,QAAQ,eAAe;AACtG,SAASC,YAAY,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,yBAAyB,QAAQ,gBAAgB;AACzJ,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,QAAQ,EAAEC,MAAM,QAAQ,kBAAkB;AACnD,OAAO,yBAAyB;AAEhC,MAAMC,IAAI,GAAG,yBAAyB;AACtC,MAAMC,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACAC,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACF,SAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;EACjC;EACAE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACJ,SAAS,CAACK,OAAO,CAACH,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC;EAClD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,iBAAiB,GAAG,eAAe;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;EAC1B,CAAC,qBAAqB,CAAC,sCAAsC,iFAAiF;EAC9I,CAAC,yBAAyB,CAAC,0CAA0C,kEAAkE;EACvI,CAAC,sBAAsB,CAAC,uCAAuC,uDAAuD;EACtH,CAAC,qBAAqB,CAAC,sCAAsC,8DAA8D;EAC3H,CAAC,cAAc,CAAC,+BAA+B,6EAA6E;EAC5H,CAAC,aAAa,CAAC,8BAA8B,kFAAkF;EAC/H,CAAC,aAAa,CAAC,8BAA8B,gFAAgF;EAC7H,CAAC,gBAAgB,CAAC,iCAAiC,mFAAmF;EACtI,CAAC,sBAAsB,CAAC,gCAAgC,yEAAyE,GAC7H,2CAA2C;EAC/C,CAAC,eAAe,CAAC,gCAAgC,sCAAsC,GACnF,4DAA4D;EAChE,CAAC,gBAAgB,CAAC,iCAAiC,2EAA2E,GAC1H,4DAA4D,GAC5D,+FAA+F;EACnG,CAAC,oBAAoB,CAAC,8BAA8B,wCAAwC,GACxF,2CAA2C;EAC/C,CAAC,cAAc,CAAC,+BAA+B,yEAAyE;EACxH,CAAC,wBAAwB,CAAC,yCAAyC;AACvE,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIrB,YAAY,CAAC,cAAc,CAAC,eAAe,eAAe,CAAC,oBAAoBoB,qBAAqB,CAAC;AAC/H;AACA,SAASE,YAAYA,CAACC,CAAC,EAAEC,SAAS,EAAE;EAChC,OAAOD,CAAC,YAAYtB,aAAa,IAAIsB,CAAC,CAACE,IAAI,CAACC,OAAO,CAACF,SAAS,CAAC,KAAK,CAAC,CAAC;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,yBAAyB,GAAG,KAAK;AACvC,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,wBAAwB,GAAG,CAAC;AAClC,MAAMC,qBAAqB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC;AAClE,MAAMC,KAAK,CAAC;EACRnB,WAAWA,CAACoB,OAAO,EAAEC,MAAM,GAAGL,wBAAwB,EAAE;IACpD,IAAI,CAACI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,MAAM;EACtB;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACH,OAAO,KAAK,QAAQ,EAAE;MAC3B,OAAOL,yBAAyB;IACpC;IACA,OAAOG,qBAAqB,CAACJ,OAAO,CAAC,IAAI,CAACO,MAAM,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;EACxE;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,OAAO,KAAK,QAAQ,EAAE;MAC3B,OAAOH,wBAAwB;IACnC;IACA,IAAIS,GAAG,GAAGC,MAAM,CAAC,IAAI,CAACN,MAAM,CAAC;IAC7B,IAAIO,KAAK,CAACF,GAAG,CAAC,EAAE;MACZA,GAAG,GAAGT,wBAAwB;IAClC;IACA,OAAOS,GAAG;EACd;EACAG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACT,OAAO;EACvB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,eAAeA,CAACC,GAAG,GAAG/C,MAAM,CAAC,CAAC,EAAE;EACrC+C,GAAG,GAAGzC,kBAAkB,CAACyC,GAAG,CAAC;EAC7B,MAAMC,UAAU,GAAGjD,YAAY,CAACgD,GAAG,EAAExB,iBAAiB,CAAC;EACvD,OAAOyB,UAAU,CAACC,YAAY,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQeC,QAAQA,CAAAC,EAAA;EAAA,OAAAC,SAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAoBvB;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAAAF,UAAA;EAAAA,SAAA,GAAAG,iBAAA,CApBA,WAAwBC,YAAY,EAAE;IAClC,MAAMC,EAAE,GAAGnD,kBAAkB,CAACkD,YAAY,CAAC;IAC3C,MAAM,CAACE,2BAA2B,EAAEC,gBAAgB,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACtEJ,EAAE,CAACK,QAAQ,CAACC,8BAA8B,CAAC,CAAC,EAC5CN,EAAE,CAACK,QAAQ,CAACE,mBAAmB,CAAC,CAAC,CACpC,CAAC;IACF,IAAI,CAACN,2BAA2B,IAC5B,CAACA,2BAA2B,CAACO,MAAM,IACnC,CAACP,2BAA2B,CAACQ,IAAI,IACjCR,2BAA2B,CAACQ,IAAI,KAAKP,gBAAgB,EAAE;MACvD;MACA;MACA,OAAO,KAAK;IAChB;IACA,MAAMC,OAAO,CAACC,GAAG,CAAC,CACdJ,EAAE,CAACU,aAAa,CAACC,eAAe,CAACV,2BAA2B,CAACO,MAAM,CAAC,EACpER,EAAE,CAACK,QAAQ,CAACO,mBAAmB,CAACX,2BAA2B,CAACQ,IAAI,CAAC,CACpE,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EAAA,OAAAd,SAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAQD,SAASgB,iBAAiBA,CAACd,YAAY,EAAE;EACrC,MAAMC,EAAE,GAAGnD,kBAAkB,CAACkD,YAAY,CAAC;EAC3C,IAAI,CAACC,EAAE,CAACc,kBAAkB,EAAE;IACxBd,EAAE,CAACc,kBAAkB,GAAGd,EAAE,CAACU,aAAa,CAACK,eAAe,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAClEhB,EAAE,CAACiB,yBAAyB,GAAG,IAAI;IACvC,CAAC,CAAC;EACN;EACA,OAAOjB,EAAE,CAACc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeI,WAAWA,CAAAC,GAAA;EAAA,OAAAC,YAAA,CAAAxB,KAAA,OAAAC,SAAA;AAAA;AAiC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAuB,aAAA;EAAAA,YAAA,GAAAtB,iBAAA,CAjCA,WAA2BC,YAAY,EAAE;IACrC,MAAMC,EAAE,GAAGnD,kBAAkB,CAACkD,YAAY,CAAC;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMsB,WAAW,GAAG,IAAI/D,uBAAuB,CAAC,CAAC;IACjDgE,UAAU,eAAAxB,iBAAA,CAAC,aAAY;MACnB;MACAuB,WAAW,CAACzD,KAAK,CAAC,CAAC;IACvB,CAAC,GAAEoC,EAAE,CAACuB,QAAQ,CAACC,kBAAkB,CAAC;IAClC;IACA,IAAI;MACA,MAAMxB,EAAE,CAACyB,OAAO,CAACC,KAAK,CAAC;QACnBC,iBAAiB,EAAE3B,EAAE,CAACuB,QAAQ,CAACK,0BAA0B;QACzDC,MAAM,EAAER;MACZ,CAAC,CAAC;MACF,MAAMrB,EAAE,CAACU,aAAa,CAACoB,kBAAkB,CAAC,SAAS,CAAC;IACxD,CAAC,CACD,OAAO5D,CAAC,EAAE;MACN,MAAM6D,eAAe,GAAG9D,YAAY,CAACC,CAAC,EAAE,gBAAgB,CAAC,8BAA8B,CAAC,GAClF,UAAU,GACV,SAAS;MACf,MAAM8B,EAAE,CAACU,aAAa,CAACoB,kBAAkB,CAACC,eAAe,CAAC;MAC1D,MAAM7D,CAAC;IACX;EACJ,CAAC;EAAA,OAAAkD,YAAA,CAAAxB,KAAA,OAAAC,SAAA;AAAA;AASD,SAASmC,MAAMA,CAACjC,YAAY,EAAE;EAC1B,MAAMC,EAAE,GAAGnD,kBAAkB,CAACkD,YAAY,CAAC;EAC3C,OAAOkC,UAAU,CAACjC,EAAE,CAACU,aAAa,CAACwB,eAAe,CAAC,CAAC,EAAElC,EAAE,CAACmC,aAAa,CAAC,CAACC,MAAM,CAAC,CAACC,UAAU,EAAEC,GAAG,KAAK;IAChGD,UAAU,CAACC,GAAG,CAAC,GAAGC,QAAQ,CAACxC,YAAY,EAAEuC,GAAG,CAAC;IAC7C,OAAOD,UAAU;EACrB,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAACzC,YAAY,EAAEuC,GAAG,EAAE;EACnC,OAAOC,QAAQ,CAAC1F,kBAAkB,CAACkD,YAAY,CAAC,EAAEuC,GAAG,CAAC,CAACxD,SAAS,CAAC,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2D,SAASA,CAAC1C,YAAY,EAAEuC,GAAG,EAAE;EAClC,OAAOC,QAAQ,CAAC1F,kBAAkB,CAACkD,YAAY,CAAC,EAAEuC,GAAG,CAAC,CAACtD,QAAQ,CAAC,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,SAASA,CAAC3C,YAAY,EAAEuC,GAAG,EAAE;EAClC,OAAOC,QAAQ,CAAC1F,kBAAkB,CAACkD,YAAY,CAAC,EAAEuC,GAAG,CAAC,CAACzD,QAAQ,CAAC,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,QAAQA,CAACxC,YAAY,EAAEuC,GAAG,EAAE;EACjC,MAAMtC,EAAE,GAAGnD,kBAAkB,CAACkD,YAAY,CAAC;EAC3C,IAAI,CAACC,EAAE,CAACiB,yBAAyB,EAAE;IAC/BjB,EAAE,CAAC2C,OAAO,CAACC,KAAK,CAAE,kCAAiCN,GAAI,wCAAuC,GAC1F,oFAAoF,CAAC;EAC7F;EACA,MAAMO,YAAY,GAAG7C,EAAE,CAACU,aAAa,CAACwB,eAAe,CAAC,CAAC;EACvD,IAAIW,YAAY,IAAIA,YAAY,CAACP,GAAG,CAAC,KAAKQ,SAAS,EAAE;IACjD,OAAO,IAAIpE,KAAK,CAAC,QAAQ,EAAEmE,YAAY,CAACP,GAAG,CAAC,CAAC;EACjD,CAAC,MACI,IAAItC,EAAE,CAACmC,aAAa,IAAInC,EAAE,CAACmC,aAAa,CAACG,GAAG,CAAC,KAAKQ,SAAS,EAAE;IAC9D,OAAO,IAAIpE,KAAK,CAAC,SAAS,EAAEqE,MAAM,CAAC/C,EAAE,CAACmC,aAAa,CAACG,GAAG,CAAC,CAAC,CAAC;EAC9D;EACAtC,EAAE,CAAC2C,OAAO,CAACC,KAAK,CAAE,mCAAkCN,GAAI,IAAG,GACvD,6DAA6D,CAAC;EAClE,OAAO,IAAI5D,KAAK,CAAC,QAAQ,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsE,WAAWA,CAACjD,YAAY,EAAEkD,QAAQ,EAAE;EACzC,MAAMjD,EAAE,GAAGnD,kBAAkB,CAACkD,YAAY,CAAC;EAC3C,QAAQkD,QAAQ;IACZ,KAAK,OAAO;MACRjD,EAAE,CAAC2C,OAAO,CAACM,QAAQ,GAAG/F,QAAQ,CAACgG,KAAK;MACpC;IACJ,KAAK,QAAQ;MACTlD,EAAE,CAAC2C,OAAO,CAACM,QAAQ,GAAG/F,QAAQ,CAACiG,MAAM;MACrC;IACJ;MACInD,EAAE,CAAC2C,OAAO,CAACM,QAAQ,GAAG/F,QAAQ,CAACkG,KAAK;EAC5C;AACJ;AACA;AACA;AACA;AACA,SAASnB,UAAUA,CAACoB,IAAI,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;EACtC,OAAOC,MAAM,CAACC,IAAI,CAACD,MAAM,CAACE,MAAM,CAACF,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEJ,IAAI,CAAC,EAAEC,IAAI,CAAC,CAAC;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,CAAC;EAChBnG,WAAWA,CAACoG,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAEC,MAAM,EAAE;IAC/C,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACpC,iBAAiB,EAAEqC,kCAAkC,EAAE;IACrE;IACA,IAAI,CAACA,kCAAkC,EAAE;MACrC,IAAI,CAACF,MAAM,CAAClB,KAAK,CAAC,8CAA8C,CAAC;MACjE,OAAO,KAAK;IAChB;IACA;IACA,MAAMqB,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,kCAAkC;IACtE,MAAMD,iBAAiB,GAAGE,cAAc,IAAItC,iBAAiB;IAC7D,IAAI,CAACmC,MAAM,CAAClB,KAAK,CAAC,2BAA2B,GACxC,sBAAqBqB,cAAe,GAAE,GACtC,+DAA8DtC,iBAAkB,GAAE,GAClF,kBAAiBoC,iBAAkB,GAAE,CAAC;IAC3C,OAAOA,iBAAiB;EAC5B;EACMrC,KAAKA,CAAC0C,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAvE,iBAAA;MACjB;MACA,MAAM,CAACkE,kCAAkC,EAAE/D,2BAA2B,CAAC,SAASE,OAAO,CAACC,GAAG,CAAC,CACxFiE,KAAI,CAACT,OAAO,CAACU,qCAAqC,CAAC,CAAC,EACpDD,KAAI,CAACT,OAAO,CAACtD,8BAA8B,CAAC,CAAC,CAChD,CAAC;MACF;MACA,IAAIL,2BAA2B,IAC3BoE,KAAI,CAACN,iBAAiB,CAACK,OAAO,CAACzC,iBAAiB,EAAEqC,kCAAkC,CAAC,EAAE;QACvF,OAAO/D,2BAA2B;MACtC;MACA;MACA;MACAmE,OAAO,CAAC3D,IAAI,GACRR,2BAA2B,IAAIA,2BAA2B,CAACQ,IAAI;MACnE;MACA,MAAM8D,QAAQ,SAASF,KAAI,CAACV,MAAM,CAACjC,KAAK,CAAC0C,OAAO,CAAC;MACjD;MACA,MAAMI,iBAAiB,GAAG;MACtB;MACAH,KAAI,CAACR,YAAY,CAACY,qCAAqC,CAACP,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CACtE;MACD,IAAII,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;QACzB;QACAF,iBAAiB,CAAC7G,IAAI,CAAC0G,KAAI,CAACT,OAAO,CAACe,8BAA8B,CAACJ,QAAQ,CAAC,CAAC;MACjF;MACA,MAAMpE,OAAO,CAACC,GAAG,CAACoE,iBAAiB,CAAC;MACpC,OAAOD,QAAQ;IAAC;EACpB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,eAAeA,CAACC,iBAAiB,GAAGC,SAAS,EAAE;EACpD;IACA;IACCD,iBAAiB,CAACE,SAAS,IAAIF,iBAAiB,CAACE,SAAS,CAAC,CAAC,CAAC;IAC1D;IACA;IACAF,iBAAiB,CAACG;IACtB;EAAA;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb1H,WAAWA,CAAC2H,qBAAqB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAChF,IAAI,CAACL,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU7D,KAAKA,CAAC0C,OAAO,EAAE;IAAA,IAAAoB,MAAA;IAAA,OAAA1F,iBAAA;MACjB,MAAM,CAAC2F,cAAc,EAAEC,iBAAiB,CAAC,SAASvF,OAAO,CAACC,GAAG,CAAC,CAC1DoF,MAAI,CAACN,qBAAqB,CAACS,KAAK,CAAC,CAAC,EAClCH,MAAI,CAACN,qBAAqB,CAACU,QAAQ,CAAC,CAAC,CACxC,CAAC;MACF,MAAMC,OAAO,GAAGC,MAAM,CAACC,+BAA+B,IAClD,6CAA6C;MACjD,MAAMC,GAAG,GAAI,GAAEH,OAAQ,gBAAeL,MAAI,CAACH,SAAU,eAAcG,MAAI,CAACJ,SAAU,cAAaI,MAAI,CAACF,MAAO,EAAC;MAC5G,MAAMW,OAAO,GAAG;QACZ,cAAc,EAAE,kBAAkB;QAClC,kBAAkB,EAAE,MAAM;QAC1B;QACA;QACA,eAAe,EAAE7B,OAAO,CAAC3D,IAAI,IAAI;MACrC,CAAC;MACD,MAAMyF,WAAW,GAAG;QAChB;QACAC,WAAW,EAAEX,MAAI,CAACL,UAAU;QAC5BiB,eAAe,EAAEX,cAAc;QAC/BY,qBAAqB,EAAEX,iBAAiB;QACxCY,MAAM,EAAEd,MAAI,CAACD,KAAK;QAClBgB,aAAa,EAAE3B,eAAe,CAAC;QAC/B;MACJ,CAAC;;MACD,MAAM4B,OAAO,GAAG;QACZC,MAAM,EAAE,MAAM;QACdR,OAAO;QACPS,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACV,WAAW;MACpC,CAAC;MACD;MACA,MAAMW,YAAY,GAAGnF,KAAK,CAACsE,GAAG,EAAEQ,OAAO,CAAC;MACxC,MAAMM,cAAc,GAAG,IAAI3G,OAAO,CAAC,CAAC4G,QAAQ,EAAEC,MAAM,KAAK;QACrD;QACA5C,OAAO,CAACvC,MAAM,CAACpE,gBAAgB,CAAC,MAAM;UAClC;UACA,MAAMwJ,KAAK,GAAG,IAAIC,KAAK,CAAC,4BAA4B,CAAC;UACrDD,KAAK,CAAC7J,IAAI,GAAG,YAAY;UACzB4J,MAAM,CAACC,KAAK,CAAC;QACjB,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI1C,QAAQ;MACZ,IAAI;QACA,MAAMpE,OAAO,CAACgH,IAAI,CAAC,CAACN,YAAY,EAAEC,cAAc,CAAC,CAAC;QAClDvC,QAAQ,SAASsC,YAAY;MACjC,CAAC,CACD,OAAOO,aAAa,EAAE;QAClB,IAAIjJ,SAAS,GAAG,sBAAsB,CAAC;QACvC,IAAI,CAACiJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAChK,IAAI,MAAM,YAAY,EAAE;UACrGe,SAAS,GAAG,eAAe,CAAC;QAChC;;QACA,MAAMH,aAAa,CAACqJ,MAAM,CAAClJ,SAAS,EAAE;UAClCmJ,oBAAoB,EAAEF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG;QACtG,CAAC,CAAC;MACN;MACA,IAAI7C,MAAM,GAAGH,QAAQ,CAACG,MAAM;MAC5B;MACA,MAAM8C,YAAY,GAAGjD,QAAQ,CAAC0B,OAAO,CAACwB,GAAG,CAAC,MAAM,CAAC,IAAI3E,SAAS;MAC9D,IAAItC,MAAM;MACV,IAAIkH,KAAK;MACT;MACA;MACA,IAAInD,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;QACzB,IAAIiD,YAAY;QAChB,IAAI;UACAA,YAAY,SAASpD,QAAQ,CAACqD,IAAI,CAAC,CAAC;QACxC,CAAC,CACD,OAAOR,aAAa,EAAE;UAClB,MAAMpJ,aAAa,CAACqJ,MAAM,CAAC,oBAAoB,CAAC,6BAA6B;YACzEC,oBAAoB,EAAEF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG;UACtG,CAAC,CAAC;QACN;QACA/G,MAAM,GAAGmH,YAAY,CAAC,SAAS,CAAC;QAChCD,KAAK,GAAGC,YAAY,CAAC,OAAO,CAAC;MACjC;MACA;MACA,IAAID,KAAK,KAAK,4BAA4B,EAAE;QACxChD,MAAM,GAAG,GAAG;MAChB,CAAC,MACI,IAAIgD,KAAK,KAAK,WAAW,EAAE;QAC5BhD,MAAM,GAAG,GAAG;MAChB,CAAC,MACI,IAAIgD,KAAK,KAAK,aAAa,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1D;QACAlH,MAAM,GAAG,CAAC,CAAC;MACf;MACA;MACA;MACA;MACA;MACA,IAAIkE,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,EAAE;QAClC,MAAM1G,aAAa,CAACqJ,MAAM,CAAC,cAAc,CAAC,8BAA8B;UACpEQ,UAAU,EAAEnD;QAChB,CAAC,CAAC;MACN;MACA,OAAO;QAAEA,MAAM;QAAEjE,IAAI,EAAE+G,YAAY;QAAEhH;MAAO,CAAC;IAAC;EAClD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsH,mBAAmBA,CAACjG,MAAM,EAAEkG,qBAAqB,EAAE;EACxD,OAAO,IAAI5H,OAAO,CAAC,CAAC6H,OAAO,EAAEhB,MAAM,KAAK;IACpC;IACA,MAAMiB,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACJ,qBAAqB,GAAG7D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMiE,OAAO,GAAG9G,UAAU,CAAC0G,OAAO,EAAEC,aAAa,CAAC;IAClD;IACApG,MAAM,CAACpE,gBAAgB,CAAC,MAAM;MAC1B4K,YAAY,CAACD,OAAO,CAAC;MACrB;MACApB,MAAM,CAAChJ,aAAa,CAACqJ,MAAM,CAAC,gBAAgB,CAAC,gCAAgC;QACzEU;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,SAASO,gBAAgBA,CAACpK,CAAC,EAAE;EACzB,IAAI,EAAEA,CAAC,YAAYtB,aAAa,CAAC,IAAI,CAACsB,CAAC,CAACqK,UAAU,EAAE;IAChD,OAAO,KAAK;EAChB;EACA;EACA,MAAMV,UAAU,GAAG3I,MAAM,CAAChB,CAAC,CAACqK,UAAU,CAAC,YAAY,CAAC,CAAC;EACrD,OAAQV,UAAU,KAAK,GAAG,IACtBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,cAAc,CAAC;EACjBjL,WAAWA,CAACoG,MAAM,EAAEC,OAAO,EAAE;IACzB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACMlC,KAAKA,CAAC0C,OAAO,EAAE;IAAA,IAAAqE,MAAA;IAAA,OAAA3I,iBAAA;MACjB,MAAM4I,gBAAgB,GAAG,OAAOD,MAAI,CAAC7E,OAAO,CAAC+E,mBAAmB,CAAC,CAAC,KAAK;QACnEC,YAAY,EAAE,CAAC;QACfb,qBAAqB,EAAE7D,IAAI,CAACC,GAAG,CAAC;MACpC,CAAC;MACD,OAAOsE,MAAI,CAACI,YAAY,CAACzE,OAAO,EAAEsE,gBAAgB,CAAC;IAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;EACUG,YAAYA,CAACzE,OAAO,EAAE;IAAE2D,qBAAqB;IAAEa;EAAa,CAAC,EAAE;IAAA,IAAAE,MAAA;IAAA,OAAAhJ,iBAAA;MACjE;MACA;MACA;MACA,MAAMgI,mBAAmB,CAAC1D,OAAO,CAACvC,MAAM,EAAEkG,qBAAqB,CAAC;MAChE,IAAI;QACA,MAAMxD,QAAQ,SAASuE,MAAI,CAACnF,MAAM,CAACjC,KAAK,CAAC0C,OAAO,CAAC;QACjD;QACA,MAAM0E,MAAI,CAAClF,OAAO,CAACmF,sBAAsB,CAAC,CAAC;QAC3C,OAAOxE,QAAQ;MACnB,CAAC,CACD,OAAOrG,CAAC,EAAE;QACN,IAAI,CAACoK,gBAAgB,CAACpK,CAAC,CAAC,EAAE;UACtB,MAAMA,CAAC;QACX;QACA;QACA,MAAMwK,gBAAgB,GAAG;UACrBX,qBAAqB,EAAE7D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGrH,sBAAsB,CAAC8L,YAAY,CAAC;UACxEA,YAAY,EAAEA,YAAY,GAAG;QACjC,CAAC;QACD;QACA,MAAME,MAAI,CAAClF,OAAO,CAACoF,mBAAmB,CAACN,gBAAgB,CAAC;QACxD,OAAOI,MAAI,CAACD,YAAY,CAACzE,OAAO,EAAEsE,gBAAgB,CAAC;MACvD;IAAC;EACL;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,4BAA4B,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAChD,MAAMC,4BAA4B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACf5L,WAAWA;EACX;EACA+B,GAAG;EACH;EACA;EACA;EACA;AACJ;AACA;EACImC,OAAO;EACP;AACJ;AACA;EACIf,aAAa;EACb;AACJ;AACA;EACIL,QAAQ;EACR;AACJ;AACA;EACIsC,OAAO,EAAE;IACL,IAAI,CAACrD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACmC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACf,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACL,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACsC,OAAO,GAAGA,OAAO;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAAC1B,yBAAyB,GAAG,KAAK;IACtC,IAAI,CAACM,QAAQ,GAAG;MACZC,kBAAkB,EAAEyH,4BAA4B;MAChDrH,0BAA0B,EAAEsH;IAChC,CAAC;IACD,IAAI,CAAC/G,aAAa,GAAG,CAAC,CAAC;EAC3B;EACA,IAAIiH,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC1I,aAAa,CAAC4D,qCAAqC,CAAC,CAAC,IAAI,CAAC,CAAC;EAC3E;EACA,IAAIvC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrB,aAAa,CAAC2I,kBAAkB,CAAC,CAAC,IAAI,cAAc;EACpE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAEpL,SAAS,EAAE;EACvC,MAAMiJ,aAAa,GAAGmC,KAAK,CAACC,MAAM,CAACvC,KAAK,IAAInE,SAAS;EACrD,OAAO9E,aAAa,CAACqJ,MAAM,CAAClJ,SAAS,EAAE;IACnCmJ,oBAAoB,EAAEF,aAAa,KAAKA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG,OAAO;EAC/H,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,mBAAmB,GAAG,qBAAqB;AACjD,MAAMC,OAAO,GAAG,wBAAwB;AACxC,MAAMC,UAAU,GAAG,CAAC;AACpB;AACA,SAASC,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAIzJ,OAAO,CAAC,CAAC6H,OAAO,EAAEhB,MAAM,KAAK;IACpC,IAAI;MACA,MAAM5C,OAAO,GAAGyF,SAAS,CAACC,IAAI,CAACJ,OAAO,EAAEC,UAAU,CAAC;MACnDvF,OAAO,CAAC2F,OAAO,GAAGR,KAAK,IAAI;QACvBvC,MAAM,CAACsC,eAAe,CAACC,KAAK,EAAE,cAAc,CAAC,4BAA4B,CAAC,CAAC;MAC/E,CAAC;;MACDnF,OAAO,CAAC4F,SAAS,GAAGT,KAAK,IAAI;QACzBvB,OAAO,CAACuB,KAAK,CAACC,MAAM,CAACS,MAAM,CAAC;MAChC,CAAC;MACD7F,OAAO,CAAC8F,eAAe,GAAGX,KAAK,IAAI;QAC/B,MAAMY,EAAE,GAAGZ,KAAK,CAACC,MAAM,CAACS,MAAM;QAC9B;QACA;QACA;QACA;QACA;QACA,QAAQV,KAAK,CAACa,UAAU;UACpB,KAAK,CAAC;YACFD,EAAE,CAACE,iBAAiB,CAACZ,mBAAmB,EAAE;cACtCa,OAAO,EAAE;YACb,CAAC,CAAC;QACV;MACJ,CAAC;IACL,CAAC,CACD,OAAOrD,KAAK,EAAE;MACVD,MAAM,CAAChJ,aAAa,CAACqJ,MAAM,CAAC,cAAc,CAAC,8BAA8B;QACrEC,oBAAoB,EAAEL,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACM;MAC9E,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,MAAMgD,OAAO,CAAC;EACV;AACJ;AACA;AACA;AACA;EACIhN,WAAWA,CAACgI,KAAK,EAAEiF,OAAO,EAAEpF,SAAS,EAAEqF,aAAa,GAAGb,YAAY,CAAC,CAAC,EAAE;IACnE,IAAI,CAACrE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACiF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACpF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACqF,aAAa,GAAGA,aAAa;EACtC;EACApB,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC5B,GAAG,CAAC,mBAAmB,CAAC;EACxC;EACA3F,kBAAkBA,CAAC4C,MAAM,EAAE;IACvB,OAAO,IAAI,CAACgG,GAAG,CAAC,mBAAmB,EAAEhG,MAAM,CAAC;EAChD;EACA;EACA;EACAJ,qCAAqCA,CAAA,EAAG;IACpC,OAAO,IAAI,CAACmD,GAAG,CAAC,wCAAwC,CAAC;EAC7D;EACAhD,qCAAqCA,CAACkG,SAAS,EAAE;IAC7C,OAAO,IAAI,CAACD,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;EACxE;EACArK,8BAA8BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAACmH,GAAG,CAAC,gCAAgC,CAAC;EACrD;EACA9C,8BAA8BA,CAACJ,QAAQ,EAAE;IACrC,OAAO,IAAI,CAACmG,GAAG,CAAC,gCAAgC,EAAEnG,QAAQ,CAAC;EAC/D;EACArC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACuF,GAAG,CAAC,eAAe,CAAC;EACpC;EACA9G,eAAeA,CAACH,MAAM,EAAE;IACpB,OAAO,IAAI,CAACkK,GAAG,CAAC,eAAe,EAAElK,MAAM,CAAC;EAC5C;EACAD,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACkH,GAAG,CAAC,oBAAoB,CAAC;EACzC;EACA7G,mBAAmBA,CAACgK,IAAI,EAAE;IACtB,OAAO,IAAI,CAACF,GAAG,CAAC,oBAAoB,EAAEE,IAAI,CAAC;EAC/C;EACAjC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAClB,GAAG,CAAC,mBAAmB,CAAC;EACxC;EACAuB,mBAAmBA,CAAC6B,QAAQ,EAAE;IAC1B,OAAO,IAAI,CAACH,GAAG,CAAC,mBAAmB,EAAEG,QAAQ,CAAC;EAClD;EACA9B,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC+B,MAAM,CAAC,mBAAmB,CAAC;EAC3C;EACMrD,GAAGA,CAACnF,GAAG,EAAE;IAAA,IAAAyI,MAAA;IAAA,OAAAjL,iBAAA;MACX,MAAMqK,EAAE,SAASY,MAAI,CAACN,aAAa;MACnC,OAAO,IAAItK,OAAO,CAAC,CAAC6H,OAAO,EAAEhB,MAAM,KAAK;QACpC,MAAMgE,WAAW,GAAGb,EAAE,CAACa,WAAW,CAAC,CAACvB,mBAAmB,CAAC,EAAE,UAAU,CAAC;QACrE,MAAMwB,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACxB,mBAAmB,CAAC;QAChE,MAAMyB,YAAY,GAAGH,MAAI,CAACI,kBAAkB,CAAC7I,GAAG,CAAC;QACjD,IAAI;UACA,MAAM8B,OAAO,GAAG6G,WAAW,CAACxD,GAAG,CAACyD,YAAY,CAAC;UAC7C9G,OAAO,CAAC2F,OAAO,GAAGR,KAAK,IAAI;YACvBvC,MAAM,CAACsC,eAAe,CAACC,KAAK,EAAE,aAAa,CAAC,2BAA2B,CAAC,CAAC;UAC7E,CAAC;;UACDnF,OAAO,CAAC4F,SAAS,GAAGT,KAAK,IAAI;YACzB,MAAMU,MAAM,GAAGV,KAAK,CAACC,MAAM,CAACS,MAAM;YAClC,IAAIA,MAAM,EAAE;cACRjC,OAAO,CAACiC,MAAM,CAACmB,KAAK,CAAC;YACzB,CAAC,MACI;cACDpD,OAAO,CAAClF,SAAS,CAAC;YACtB;UACJ,CAAC;QACL,CAAC,CACD,OAAO5E,CAAC,EAAE;UACN8I,MAAM,CAAChJ,aAAa,CAACqJ,MAAM,CAAC,aAAa,CAAC,6BAA6B;YACnEC,oBAAoB,EAAEpJ,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACqJ;UAClE,CAAC,CAAC,CAAC;QACP;MACJ,CAAC,CAAC;IAAC;EACP;EACMmD,GAAGA,CAACpI,GAAG,EAAE8I,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAvL,iBAAA;MAClB,MAAMqK,EAAE,SAASkB,MAAI,CAACZ,aAAa;MACnC,OAAO,IAAItK,OAAO,CAAC,CAAC6H,OAAO,EAAEhB,MAAM,KAAK;QACpC,MAAMgE,WAAW,GAAGb,EAAE,CAACa,WAAW,CAAC,CAACvB,mBAAmB,CAAC,EAAE,WAAW,CAAC;QACtE,MAAMwB,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACxB,mBAAmB,CAAC;QAChE,MAAMyB,YAAY,GAAGG,MAAI,CAACF,kBAAkB,CAAC7I,GAAG,CAAC;QACjD,IAAI;UACA,MAAM8B,OAAO,GAAG6G,WAAW,CAACK,GAAG,CAAC;YAC5BJ,YAAY;YACZE;UACJ,CAAC,CAAC;UACFhH,OAAO,CAAC2F,OAAO,GAAIR,KAAK,IAAK;YACzBvC,MAAM,CAACsC,eAAe,CAACC,KAAK,EAAE,aAAa,CAAC,2BAA2B,CAAC,CAAC;UAC7E,CAAC;;UACDnF,OAAO,CAAC4F,SAAS,GAAG,MAAM;YACtBhC,OAAO,CAAC,CAAC;UACb,CAAC;QACL,CAAC,CACD,OAAO9J,CAAC,EAAE;UACN8I,MAAM,CAAChJ,aAAa,CAACqJ,MAAM,CAAC,aAAa,CAAC,6BAA6B;YACnEC,oBAAoB,EAAEpJ,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACqJ;UAClE,CAAC,CAAC,CAAC;QACP;MACJ,CAAC,CAAC;IAAC;EACP;EACMuD,MAAMA,CAACxI,GAAG,EAAE;IAAA,IAAAiJ,MAAA;IAAA,OAAAzL,iBAAA;MACd,MAAMqK,EAAE,SAASoB,MAAI,CAACd,aAAa;MACnC,OAAO,IAAItK,OAAO,CAAC,CAAC6H,OAAO,EAAEhB,MAAM,KAAK;QACpC,MAAMgE,WAAW,GAAGb,EAAE,CAACa,WAAW,CAAC,CAACvB,mBAAmB,CAAC,EAAE,WAAW,CAAC;QACtE,MAAMwB,WAAW,GAAGD,WAAW,CAACC,WAAW,CAACxB,mBAAmB,CAAC;QAChE,MAAMyB,YAAY,GAAGK,MAAI,CAACJ,kBAAkB,CAAC7I,GAAG,CAAC;QACjD,IAAI;UACA,MAAM8B,OAAO,GAAG6G,WAAW,CAACH,MAAM,CAACI,YAAY,CAAC;UAChD9G,OAAO,CAAC2F,OAAO,GAAIR,KAAK,IAAK;YACzBvC,MAAM,CAACsC,eAAe,CAACC,KAAK,EAAE,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;UACnF,CAAC;;UACDnF,OAAO,CAAC4F,SAAS,GAAG,MAAM;YACtBhC,OAAO,CAAC,CAAC;UACb,CAAC;QACL,CAAC,CACD,OAAO9J,CAAC,EAAE;UACN8I,MAAM,CAAChJ,aAAa,CAACqJ,MAAM,CAAC,gBAAgB,CAAC,gCAAgC;YACzEC,oBAAoB,EAAEpJ,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACqJ;UAClE,CAAC,CAAC,CAAC;QACP;MACJ,CAAC,CAAC;IAAC;EACP;EACA;EACA4D,kBAAkBA,CAAC7I,GAAG,EAAE;IACpB,OAAO,CAAC,IAAI,CAACiD,KAAK,EAAE,IAAI,CAACiF,OAAO,EAAE,IAAI,CAACpF,SAAS,EAAE9C,GAAG,CAAC,CAACkJ,IAAI,CAAC,CAAC;EACjE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACflO,WAAWA,CAACqG,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;EACIyF,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACtH,eAAe;EAC/B;EACAuC,qCAAqCA,CAAA,EAAG;IACpC,OAAO,IAAI,CAACN,kCAAkC;EAClD;EACA9B,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACW,YAAY;EAC5B;EACA;AACJ;AACA;EACU9B,eAAeA,CAAA,EAAG;IAAA,IAAA2K,MAAA;IAAA,OAAA5L,iBAAA;MACpB,MAAM6L,sBAAsB,GAAGD,MAAI,CAAC9H,OAAO,CAACyF,kBAAkB,CAAC,CAAC;MAChE,MAAMuC,yCAAyC,GAAGF,MAAI,CAAC9H,OAAO,CAACU,qCAAqC,CAAC,CAAC;MACtG,MAAMuH,mBAAmB,GAAGH,MAAI,CAAC9H,OAAO,CAAC1B,eAAe,CAAC,CAAC;MAC1D;MACA;MACA;MACA;MACA;MACA,MAAMH,eAAe,SAAS4J,sBAAsB;MACpD,IAAI5J,eAAe,EAAE;QACjB2J,MAAI,CAAC3J,eAAe,GAAGA,eAAe;MAC1C;MACA,MAAMiC,kCAAkC,SAAS4H,yCAAyC;MAC1F,IAAI5H,kCAAkC,EAAE;QACpC0H,MAAI,CAAC1H,kCAAkC,GACnCA,kCAAkC;MAC1C;MACA,MAAMnB,YAAY,SAASgJ,mBAAmB;MAC9C,IAAIhJ,YAAY,EAAE;QACd6I,MAAI,CAAC7I,YAAY,GAAGA,YAAY;MACpC;IAAC;EACL;EACA;AACJ;AACA;EACIf,kBAAkBA,CAAC4C,MAAM,EAAE;IACvB,IAAI,CAAC3C,eAAe,GAAG2C,MAAM;IAC7B,OAAO,IAAI,CAACd,OAAO,CAAC9B,kBAAkB,CAAC4C,MAAM,CAAC;EAClD;EACAD,qCAAqCA,CAACqH,eAAe,EAAE;IACnD,IAAI,CAAC9H,kCAAkC,GAAG8H,eAAe;IACzD,OAAO,IAAI,CAAClI,OAAO,CAACa,qCAAqC,CAACqH,eAAe,CAAC;EAC9E;EACAnL,eAAeA,CAACkC,YAAY,EAAE;IAC1B,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,OAAO,IAAI,CAACe,OAAO,CAACjD,eAAe,CAACkC,YAAY,CAAC;EACrD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkJ,oBAAoBA,CAAA,EAAG;EAC5BvP,kBAAkB,CAAC,IAAIS,SAAS,CAACa,iBAAiB,EAAEkO,mBAAmB,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAACC,oBAAoB,CAAC,IAAI,CAAC,CAAC;EACzIxP,eAAe,CAACW,IAAI,EAAEC,OAAO,CAAC;EAC9B;EACAZ,eAAe,CAACW,IAAI,EAAEC,OAAO,EAAE,SAAS,CAAC;EACzC,SAAS2O,mBAAmBA,CAACE,SAAS,EAAE;IAAEC,kBAAkB,EAAE/G;EAAU,CAAC,EAAE;IACvE;IACA;IACA,MAAM9F,GAAG,GAAG4M,SAAS,CAACE,WAAW,CAAC,KAAK,CAAC,CAAC5M,YAAY,CAAC,CAAC;IACvD;IACA,MAAM6M,aAAa,GAAGH,SAAS,CAC1BE,WAAW,CAAC,wBAAwB,CAAC,CACrC5M,YAAY,CAAC,CAAC;IACnB;IACA,IAAI,OAAOsG,MAAM,KAAK,WAAW,EAAE;MAC/B,MAAM9H,aAAa,CAACqJ,MAAM,CAAC,qBAAqB,CAAC,mCAAmC,CAAC;IACzF;IACA;IACA,IAAI,CAACtK,oBAAoB,CAAC,CAAC,EAAE;MACzB,MAAMiB,aAAa,CAACqJ,MAAM,CAAC,wBAAwB,CAAC,sCAAsC,CAAC;IAC/F;IACA;IACA,MAAM;MAAEhC,SAAS;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGjG,GAAG,CAACkH,OAAO;IAChD,IAAI,CAACnB,SAAS,EAAE;MACZ,MAAMrH,aAAa,CAACqJ,MAAM,CAAC,yBAAyB,CAAC,uCAAuC,CAAC;IACjG;;IACA,IAAI,CAAC/B,MAAM,EAAE;MACT,MAAMtH,aAAa,CAACqJ,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC;IAC3F;;IACA,IAAI,CAAC9B,KAAK,EAAE;MACR,MAAMvH,aAAa,CAACqJ,MAAM,CAAC,qBAAqB,CAAC,mCAAmC,CAAC;IACzF;;IACAjC,SAAS,GAAGA,SAAS,IAAI,UAAU;IACnC,MAAMxB,OAAO,GAAG,IAAI2G,OAAO,CAAChF,KAAK,EAAEjG,GAAG,CAAClC,IAAI,EAAEgI,SAAS,CAAC;IACvD,MAAMvB,YAAY,GAAG,IAAI4H,YAAY,CAAC7H,OAAO,CAAC;IAC9C,MAAME,MAAM,GAAG,IAAI3G,MAAM,CAACC,IAAI,CAAC;IAC/B;IACA;IACA0G,MAAM,CAACb,QAAQ,GAAG/F,QAAQ,CAACkG,KAAK;IAChC,MAAMkJ,UAAU,GAAG,IAAIrH,UAAU,CAACoH,aAAa;IAC/C;IACA3P,WAAW,EAAE0I,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,CAAC;IACjD,MAAMgH,cAAc,GAAG,IAAI/D,cAAc,CAAC8D,UAAU,EAAE1I,OAAO,CAAC;IAC9D,MAAM4I,aAAa,GAAG,IAAI9I,aAAa,CAAC6I,cAAc,EAAE3I,OAAO,EAAEC,YAAY,EAAEC,MAAM,CAAC;IACtF,MAAM2I,oBAAoB,GAAG,IAAItD,YAAY,CAAC7J,GAAG,EAAEkN,aAAa,EAAE3I,YAAY,EAAED,OAAO,EAAEE,MAAM,CAAC;IAChG;IACA;IACAjD,iBAAiB,CAAC4L,oBAAoB,CAAC;IACvC,OAAOA,oBAAoB;EAC/B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAWeC,gBAAgBA,CAAAC,GAAA;EAAA,OAAAC,iBAAA,CAAAhN,KAAA,OAAAC,SAAA;AAAA;AAK/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAA+M,kBAAA;EAAAA,iBAAA,GAAA9M,iBAAA,CALA,WAAgCC,YAAY,EAAE;IAC1CA,YAAY,GAAGlD,kBAAkB,CAACkD,YAAY,CAAC;IAC/C,MAAMmB,WAAW,CAACnB,YAAY,CAAC;IAC/B,OAAON,QAAQ,CAACM,YAAY,CAAC;EACjC,CAAC;EAAA,OAAA6M,iBAAA,CAAAhN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAWcgN,WAAWA,CAAA;EAAA,OAAAC,YAAA,CAAAlN,KAAA,OAAAC,SAAA;AAAA;AAa1B;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAiN,aAAA;EAAAA,YAAA,GAAAhN,iBAAA,CAlBA,aAA6B;IACzB,IAAI,CAAC/C,oBAAoB,CAAC,CAAC,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,IAAI;MACA,MAAMgQ,YAAY,SAAS/P,yBAAyB,CAAC,CAAC;MACtD,OAAO+P,YAAY;IACvB,CAAC,CACD,OAAO9F,KAAK,EAAE;MACV,OAAO,KAAK;IAChB;EACJ,CAAC;EAAA,OAAA6F,YAAA,CAAAlN,KAAA,OAAAC,SAAA;AAAA;AAQDkM,oBAAoB,CAAC,CAAC;AAEtB,SAAStM,QAAQ,EAAEoB,iBAAiB,EAAE6L,gBAAgB,EAAExL,WAAW,EAAEc,MAAM,EAAEQ,UAAU,EAAEC,SAAS,EAAEpD,eAAe,EAAEqD,SAAS,EAAEH,QAAQ,EAAEsK,WAAW,EAAE7J,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
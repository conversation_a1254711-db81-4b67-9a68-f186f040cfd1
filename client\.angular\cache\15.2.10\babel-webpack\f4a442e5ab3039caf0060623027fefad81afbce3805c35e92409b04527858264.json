{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function every(predicate, thisArg) {\n  return source => source.lift(new EveryOperator(predicate, thisArg, source));\n}\nclass EveryOperator {\n  constructor(predicate, thisArg, source) {\n    this.predicate = predicate;\n    this.thisArg = thisArg;\n    this.source = source;\n  }\n  call(observer, source) {\n    return source.subscribe(new EverySubscriber(observer, this.predicate, this.thisArg, this.source));\n  }\n}\nclass EverySubscriber extends Subscriber {\n  constructor(destination, predicate, thisArg, source) {\n    super(destination);\n    this.predicate = predicate;\n    this.thisArg = thisArg;\n    this.source = source;\n    this.index = 0;\n    this.thisArg = thisArg || this;\n  }\n  notifyComplete(everyValueMatch) {\n    this.destination.next(everyValueMatch);\n    this.destination.complete();\n  }\n  _next(value) {\n    let result = false;\n    try {\n      result = this.predicate.call(this.thisArg, value, this.index++, this.source);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n    if (!result) {\n      this.notifyComplete(false);\n    }\n  }\n  _complete() {\n    this.notifyComplete(true);\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "every", "predicate", "thisArg", "source", "lift", "EveryOperator", "constructor", "call", "observer", "subscribe", "EverySubscriber", "destination", "index", "notifyComplete", "everyValueMatch", "next", "complete", "_next", "value", "result", "err", "error", "_complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/every.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function every(predicate, thisArg) {\n    return (source) => source.lift(new EveryOperator(predicate, thisArg, source));\n}\nclass EveryOperator {\n    constructor(predicate, thisArg, source) {\n        this.predicate = predicate;\n        this.thisArg = thisArg;\n        this.source = source;\n    }\n    call(observer, source) {\n        return source.subscribe(new EverySubscriber(observer, this.predicate, this.thisArg, this.source));\n    }\n}\nclass EverySubscriber extends Subscriber {\n    constructor(destination, predicate, thisArg, source) {\n        super(destination);\n        this.predicate = predicate;\n        this.thisArg = thisArg;\n        this.source = source;\n        this.index = 0;\n        this.thisArg = thisArg || this;\n    }\n    notifyComplete(everyValueMatch) {\n        this.destination.next(everyValueMatch);\n        this.destination.complete();\n    }\n    _next(value) {\n        let result = false;\n        try {\n            result = this.predicate.call(this.thisArg, value, this.index++, this.source);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        if (!result) {\n            this.notifyComplete(false);\n        }\n    }\n    _complete() {\n        this.notifyComplete(true);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACtC,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,aAAa,CAACJ,SAAS,EAAEC,OAAO,EAAEC,MAAM,CAAC,CAAC;AACjF;AACA,MAAME,aAAa,CAAC;EAChBC,WAAWA,CAACL,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAI,IAAIA,CAACC,QAAQ,EAAEL,MAAM,EAAE;IACnB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,eAAe,CAACF,QAAQ,EAAE,IAAI,CAACP,SAAS,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC;EACrG;AACJ;AACA,MAAMO,eAAe,SAASX,UAAU,CAAC;EACrCO,WAAWA,CAACK,WAAW,EAAEV,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACjD,KAAK,CAACQ,WAAW,CAAC;IAClB,IAAI,CAACV,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACS,KAAK,GAAG,CAAC;IACd,IAAI,CAACV,OAAO,GAAGA,OAAO,IAAI,IAAI;EAClC;EACAW,cAAcA,CAACC,eAAe,EAAE;IAC5B,IAAI,CAACH,WAAW,CAACI,IAAI,CAACD,eAAe,CAAC;IACtC,IAAI,CAACH,WAAW,CAACK,QAAQ,CAAC,CAAC;EAC/B;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAI;MACAA,MAAM,GAAG,IAAI,CAAClB,SAAS,CAACM,IAAI,CAAC,IAAI,CAACL,OAAO,EAAEgB,KAAK,EAAE,IAAI,CAACN,KAAK,EAAE,EAAE,IAAI,CAACT,MAAM,CAAC;IAChF,CAAC,CACD,OAAOiB,GAAG,EAAE;MACR,IAAI,CAACT,WAAW,CAACU,KAAK,CAACD,GAAG,CAAC;MAC3B;IACJ;IACA,IAAI,CAACD,MAAM,EAAE;MACT,IAAI,CAACN,cAAc,CAAC,KAAK,CAAC;IAC9B;EACJ;EACAS,SAASA,CAAA,EAAG;IACR,IAAI,CAACT,cAAc,CAAC,IAAI,CAAC;EAC7B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, inject, Inject, PLATFORM_ID, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    this.normalizedNames = new Map();\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n    this.lazyUpdate = null;\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const key = name.toLowerCase();\n            const value = line.slice(index + 1).trim();\n            this.maybeSetNormalizedName(name, key);\n            if (this.headers.has(key)) {\n              this.headers.get(key).push(value);\n            } else {\n              this.headers.set(key, [value]);\n            }\n          }\n        });\n      };\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map();\n        Object.entries(headers).forEach(([name, values]) => {\n          let headerValues;\n          if (typeof values === 'string') {\n            headerValues = [values];\n          } else if (typeof values === 'number') {\n            headerValues = [values.toString()];\n          } else {\n            headerValues = values.map(value => value.toString());\n          }\n          if (headerValues.length > 0) {\n            const key = name.toLowerCase();\n            this.headers.set(key, headerValues);\n            this.maybeSetNormalizedName(name, key);\n          }\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n  /**\n   * @internal\n   */\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n  constructor(options = {}) {\n    this.updates = null;\n    this.cloneFrom = null;\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (!!options.fromString) {\n      if (!!options.fromObject) {\n        throw new Error(`Cannot specify both fromString and fromObject.`);\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key);\n      // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    })\n    // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n  constructor() {\n    this.map = new Map();\n  }\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys() {\n    return this.map.keys();\n  }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n    this.body = null;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     */\n    this.reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n    this.withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n    this.responseType = 'json';\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options;\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    }\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n      // Override headers if they're provided.\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n      if (!!options.context) {\n        this.context = options.context;\n      }\n      if (!!options.params) {\n        this.params = options.params;\n      }\n    }\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    if (!this.headers) {\n      this.headers = new HttpHeaders();\n    }\n    // If no context have been passed in, construct a new HttpContext instance.\n    if (!this.context) {\n      this.context = new HttpContext();\n    }\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body) || typeof this.body === 'string') {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return 'text/plain';\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return 'application/json';\n    }\n    // No type could be inferred.\n    return null;\n  }\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = update.withCredentials !== undefined ? update.withCredentials : this.withCredentials;\n    const reportProgress = update.reportProgress !== undefined ? update.reportProgress : this.reportProgress;\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n    // Pass on context if needed\n    const context = update.context ?? this.context;\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    }\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    }\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials\n    });\n  }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   */\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200 /* HttpStatusCode.Ok */, defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.ResponseHeader;\n  }\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.Response;\n    this.body = init.body !== undefined ? init.body : null;\n  }\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    this.name = 'HttpErrorResponse';\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n    this.ok = false;\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n    this.error = init.error || null;\n  }\n}\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/tour-of-heroes/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(first, url, options = {}) {\n    let req;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n      // Sort out parameters.\n      let params = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      }\n      // Construct the request.\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials\n      });\n    }\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req)));\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse));\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new Error('Response is not an ArrayBuffer.');\n              }\n              return res.body;\n            }));\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new Error('Response is not a Blob.');\n              }\n              return res.body;\n            }));\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new Error('Response is not a string.');\n              }\n              return res.body;\n            }));\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n}\nHttpClient.ɵfac = function HttpClient_Factory(t) {\n  return new (t || HttpClient)(i0.ɵɵinject(HttpHandler));\n};\nHttpClient.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpClient,\n  factory: HttpClient.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpHandler\n    }];\n  }, null);\n})();\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n  return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n  return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n    handle: downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)\n  });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n  // clang-format off\n  return (initialRequest, finalHandlerFn) => injector.runInContext(() => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n  // clang-format on\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken('HTTP_INTERCEPTOR_FNS');\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n  let chain = null;\n  return (req, handler) => {\n    if (chain === null) {\n      const interceptors = inject(HTTP_INTERCEPTORS, {\n        optional: true\n      }) ?? [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n    }\n    return chain(req, handler);\n  };\n}\nclass HttpInterceptorHandler extends HttpHandler {\n  constructor(backend, injector) {\n    super();\n    this.backend = backend;\n    this.injector = injector;\n    this.chain = null;\n  }\n  handle(initialRequest) {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(new Set(this.injector.get(HTTP_INTERCEPTOR_FNS)));\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n    }\n    return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n  }\n}\nHttpInterceptorHandler.ɵfac = function HttpInterceptorHandler_Factory(t) {\n  return new (t || HttpInterceptorHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.EnvironmentInjector));\n};\nHttpInterceptorHandler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpInterceptorHandler,\n  factory: HttpInterceptorHandler.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptorHandler, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpBackend\n    }, {\n      type: i0.EnvironmentInjector\n    }];\n  }, null);\n})();\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n    this.resolvedPromise = Promise.resolve();\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n    // Everything else happens inside the Observable boundary.\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n      let body = null;\n      // Whether the response callback has been called.\n      let finished = false;\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        // Remove the <script> tag if it's still on the page.\n        if (node.parentNode) {\n          node.parentNode.removeChild(node);\n        }\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = event => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          }\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(new HttpResponse({\n            body,\n            status: 200 /* HttpStatusCode.Ok */,\n            statusText: 'OK',\n            url\n          }));\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError = error => {\n        cleanup();\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      };\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n      // The request has now been successfully sent.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    if (!foreignDocument) {\n      foreignDocument = this.document.implementation.createHTMLDocument();\n    }\n    foreignDocument.adoptNode(script);\n  }\n}\nJsonpClientBackend.ɵfac = function JsonpClientBackend_Factory(t) {\n  return new (t || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n};\nJsonpClientBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JsonpClientBackend,\n  factory: JsonpClientBackend.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: JsonpCallbackContext\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req);\n  }\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest, next) {\n    return this.injector.runInContext(() => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n}\nJsonpInterceptor.ɵfac = function JsonpInterceptor_Factory(t) {\n  return new (t || JsonpInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n};\nJsonpInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JsonpInterceptor,\n  factory: JsonpInterceptor.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.EnvironmentInjector\n    }];\n  }, null);\n})();\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n    }\n    // Everything happens on Observable subscription.\n    return new Observable(observer => {\n      // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n      const xhr = this.xhrFactory.build();\n      xhr.open(req.method, req.urlWithParams);\n      if (!!req.withCredentials) {\n        xhr.withCredentials = true;\n      }\n      // Add all the requested headers.\n      req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n      // Add an Accept header if one isn't present already.\n      if (!req.headers.has('Accept')) {\n        xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n      }\n      // Auto-detect the Content-Type header if one isn't present already.\n      if (!req.headers.has('Content-Type')) {\n        const detectedType = req.detectContentTypeHeader();\n        // Sometimes Content-Type detection fails.\n        if (detectedType !== null) {\n          xhr.setRequestHeader('Content-Type', detectedType);\n        }\n      }\n      // Set the responseType if one was requested.\n      if (req.responseType) {\n        const responseType = req.responseType.toLowerCase();\n        // JSON responses need to be processed as text. This is because if the server\n        // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n        // xhr.response will be null, and xhr.responseText cannot be accessed to\n        // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n        // is parsed by first requesting text and then applying JSON.parse.\n        xhr.responseType = responseType !== 'json' ? responseType : 'text';\n      }\n      // Serialize the request body if one is present. If not, this will be set to null.\n      const reqBody = req.serializeBody();\n      // If progress events are enabled, response headers will be delivered\n      // in two events - the HttpHeaderResponse event and the full HttpResponse\n      // event. However, since response headers don't change in between these\n      // two events, it doesn't make sense to parse them twice. So headerResponse\n      // caches the data extracted from the response whenever it's first parsed,\n      // to ensure parsing isn't duplicated.\n      let headerResponse = null;\n      // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n      // state, and memoizes it into headerResponse.\n      const partialFromXhr = () => {\n        if (headerResponse !== null) {\n          return headerResponse;\n        }\n        const statusText = xhr.statusText || 'OK';\n        // Parse headers from XMLHttpRequest - this step is lazy.\n        const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n        // Read the response URL from the XMLHttpResponse instance and fall back on the\n        // request URL.\n        const url = getResponseUrl(xhr) || req.url;\n        // Construct the HttpHeaderResponse and memoize it.\n        headerResponse = new HttpHeaderResponse({\n          headers,\n          status: xhr.status,\n          statusText,\n          url\n        });\n        return headerResponse;\n      };\n      // Next, a few closures are defined for the various events which XMLHttpRequest can\n      // emit. This allows them to be unregistered as event listeners later.\n      // First up is the load event, which represents a response being fully available.\n      const onLoad = () => {\n        // Read response state from the memoized partial data.\n        let {\n          headers,\n          status,\n          statusText,\n          url\n        } = partialFromXhr();\n        // The body will be read out if present.\n        let body = null;\n        if (status !== 204 /* HttpStatusCode.NoContent */) {\n          // Use XMLHttpRequest.response if set, responseText otherwise.\n          body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n        }\n        // Normalize another potential bug (this one comes from CORS).\n        if (status === 0) {\n          status = !!body ? 200 /* HttpStatusCode.Ok */ : 0;\n        }\n        // ok determines whether the response will be transmitted on the event or\n        // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n        // but a successful status code can still result in an error if the user\n        // asked for JSON data and the body cannot be parsed as such.\n        let ok = status >= 200 && status < 300;\n        // Check whether the body needs to be parsed as JSON (in many cases the browser\n        // will have done that already).\n        if (req.responseType === 'json' && typeof body === 'string') {\n          // Save the original body, before attempting XSSI prefix stripping.\n          const originalBody = body;\n          body = body.replace(XSSI_PREFIX, '');\n          try {\n            // Attempt the parse. If it fails, a parse error should be delivered to the user.\n            body = body !== '' ? JSON.parse(body) : null;\n          } catch (error) {\n            // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n            // JSON response. Restore the original body (including any XSSI prefix) to deliver\n            // a better error response.\n            body = originalBody;\n            // If this was an error request to begin with, leave it as a string, it probably\n            // just isn't JSON. Otherwise, deliver the parsing error to the user.\n            if (ok) {\n              // Even though the response status was 2xx, this is still an error.\n              ok = false;\n              // The parse error contains the text of the body that failed to parse.\n              body = {\n                error,\n                text: body\n              };\n            }\n          }\n        }\n        if (ok) {\n          // A successful response is delivered on the event stream.\n          observer.next(new HttpResponse({\n            body,\n            headers,\n            status,\n            statusText,\n            url: url || undefined\n          }));\n          // The full body has been received and delivered, no further events\n          // are possible. This request is complete.\n          observer.complete();\n        } else {\n          // An unsuccessful request is delivered on the error channel.\n          observer.error(new HttpErrorResponse({\n            // The error in this case is the response body (error from the server).\n            error: body,\n            headers,\n            status,\n            statusText,\n            url: url || undefined\n          }));\n        }\n      };\n      // The onError callback is called when something goes wrong at the network level.\n      // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n      // transmitted on the error channel.\n      const onError = error => {\n        const {\n          url\n        } = partialFromXhr();\n        const res = new HttpErrorResponse({\n          error,\n          status: xhr.status || 0,\n          statusText: xhr.statusText || 'Unknown Error',\n          url: url || undefined\n        });\n        observer.error(res);\n      };\n      // The sentHeaders flag tracks whether the HttpResponseHeaders event\n      // has been sent on the stream. This is necessary to track if progress\n      // is enabled since the event will be sent on only the first download\n      // progress event.\n      let sentHeaders = false;\n      // The download progress event handler, which is only registered if\n      // progress events are enabled.\n      const onDownProgress = event => {\n        // Send the HttpResponseHeaders event if it hasn't been sent already.\n        if (!sentHeaders) {\n          observer.next(partialFromXhr());\n          sentHeaders = true;\n        }\n        // Start building the download progress event to deliver on the response\n        // event stream.\n        let progressEvent = {\n          type: HttpEventType.DownloadProgress,\n          loaded: event.loaded\n        };\n        // Set the total number of bytes in the event if it's available.\n        if (event.lengthComputable) {\n          progressEvent.total = event.total;\n        }\n        // If the request was for text content and a partial response is\n        // available on XMLHttpRequest, include it in the progress event\n        // to allow for streaming reads.\n        if (req.responseType === 'text' && !!xhr.responseText) {\n          progressEvent.partialText = xhr.responseText;\n        }\n        // Finally, fire the event.\n        observer.next(progressEvent);\n      };\n      // The upload progress event handler, which is only registered if\n      // progress events are enabled.\n      const onUpProgress = event => {\n        // Upload progress events are simpler. Begin building the progress\n        // event.\n        let progress = {\n          type: HttpEventType.UploadProgress,\n          loaded: event.loaded\n        };\n        // If the total number of bytes being uploaded is available, include\n        // it.\n        if (event.lengthComputable) {\n          progress.total = event.total;\n        }\n        // Send the event.\n        observer.next(progress);\n      };\n      // By default, register for load and error events.\n      xhr.addEventListener('load', onLoad);\n      xhr.addEventListener('error', onError);\n      xhr.addEventListener('timeout', onError);\n      xhr.addEventListener('abort', onError);\n      // Progress events are only enabled if requested.\n      if (req.reportProgress) {\n        // Download progress is always enabled if requested.\n        xhr.addEventListener('progress', onDownProgress);\n        // Upload progress depends on whether there is a body to upload.\n        if (reqBody !== null && xhr.upload) {\n          xhr.upload.addEventListener('progress', onUpProgress);\n        }\n      }\n      // Fire the request, and notify the event stream that it was fired.\n      xhr.send(reqBody);\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // This is the return from the Observable function, which is the\n      // request cancellation handler.\n      return () => {\n        // On a cancellation, remove all registered event listeners.\n        xhr.removeEventListener('error', onError);\n        xhr.removeEventListener('abort', onError);\n        xhr.removeEventListener('load', onLoad);\n        xhr.removeEventListener('timeout', onError);\n        if (req.reportProgress) {\n          xhr.removeEventListener('progress', onDownProgress);\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.removeEventListener('progress', onUpProgress);\n          }\n        }\n        // Finally, abort the in-flight request.\n        if (xhr.readyState !== xhr.DONE) {\n          xhr.abort();\n        }\n      };\n    });\n  }\n}\nHttpXhrBackend.ɵfac = function HttpXhrBackend_Factory(t) {\n  return new (t || HttpXhrBackend)(i0.ɵɵinject(i1.XhrFactory));\n};\nHttpXhrBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXhrBackend,\n  factory: HttpXhrBackend.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.XhrFactory\n    }];\n  }, null);\n})();\nconst XSRF_ENABLED = new InjectionToken('XSRF_ENABLED');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n  constructor(doc, platform, cookieName) {\n    this.doc = doc;\n    this.platform = platform;\n    this.cookieName = cookieName;\n    this.lastCookieString = '';\n    this.lastToken = null;\n    /**\n     * @internal for testing\n     */\n    this.parseCount = 0;\n  }\n  getToken() {\n    if (this.platform === 'server') {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n}\nHttpXsrfCookieExtractor.ɵfac = function HttpXsrfCookieExtractor_Factory(t) {\n  return new (t || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n};\nHttpXsrfCookieExtractor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXsrfCookieExtractor,\n  factory: HttpXsrfCookieExtractor.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [XSRF_COOKIE_NAME]\n      }]\n    }];\n  }, null);\n})();\nfunction xsrfInterceptorFn(req, next) {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n    return next(req);\n  }\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({\n      headers: req.headers.set(headerName, token)\n    });\n  }\n  return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  intercept(initialRequest, next) {\n    return this.injector.runInContext(() => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n}\nHttpXsrfInterceptor.ɵfac = function HttpXsrfInterceptor_Factory(t) {\n  return new (t || HttpXsrfInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n};\nHttpXsrfInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXsrfInterceptor,\n  factory: HttpXsrfInterceptor.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.EnvironmentInjector\n    }];\n  }, null);\n})();\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n  HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n  HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n  HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n  HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n */\nfunction provideHttpClient(...features) {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map(f => f.ɵkind));\n    if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) && featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n      throw new Error(ngDevMode ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` : '');\n    }\n  }\n  const providers = [HttpClient, HttpXhrBackend, HttpInterceptorHandler, {\n    provide: HttpHandler,\n    useExisting: HttpInterceptorHandler\n  }, {\n    provide: HttpBackend,\n    useExisting: HttpXhrBackend\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: xsrfInterceptorFn,\n    multi: true\n  }, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n  return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n    return {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: interceptorFn,\n      multi: true\n    };\n  }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken('LEGACY_INTERCEPTOR_FN');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [{\n    provide: LEGACY_INTERCEPTOR_FN,\n    useFactory: legacyInterceptorFnFactory\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useExisting: LEGACY_INTERCEPTOR_FN,\n    multi: true\n  }]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({\n  cookieName,\n  headerName\n}) {\n  const providers = [];\n  if (cookieName !== undefined) {\n    providers.push({\n      provide: XSRF_COOKIE_NAME,\n      useValue: cookieName\n    });\n  }\n  if (headerName !== undefined) {\n    providers.push({\n      provide: XSRF_HEADER_NAME,\n      useValue: headerName\n    });\n  }\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [{\n    provide: XSRF_ENABLED,\n    useValue: false\n  }]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: jsonpInterceptorFn,\n    multi: true\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @developerPreview\n */\nfunction withRequestsMadeViaParent() {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [{\n    provide: HttpBackend,\n    useFactory: () => {\n      const handlerFromParent = inject(HttpHandler, {\n        skipSelf: true,\n        optional: true\n      });\n      if (ngDevMode && handlerFromParent === null) {\n        throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n      }\n      return handlerFromParent;\n    }\n  }]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders\n    };\n  }\n}\nHttpClientXsrfModule.ɵfac = function HttpClientXsrfModule_Factory(t) {\n  return new (t || HttpClientXsrfModule)();\n};\nHttpClientXsrfModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientXsrfModule\n});\nHttpClientXsrfModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [HttpXsrfInterceptor, {\n    provide: HTTP_INTERCEPTORS,\n    useExisting: HttpXsrfInterceptor,\n    multi: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }, withXsrfConfiguration({\n    cookieName: XSRF_DEFAULT_COOKIE_NAME,\n    headerName: XSRF_DEFAULT_HEADER_NAME\n  }).ɵproviders, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\nclass HttpClientModule {}\nHttpClientModule.ɵfac = function HttpClientModule_Factory(t) {\n  return new (t || HttpClientModule)();\n};\nHttpClientModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientModule\n});\nHttpClientModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [provideHttpClient(withInterceptorsFromDi())]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Configures the [dependency injector](guide/glossary#injector) where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n */\nclass HttpClientJsonpModule {}\nHttpClientJsonpModule.ɵfac = function HttpClientJsonpModule_Factory(t) {\n  return new (t || HttpClientJsonpModule)();\n};\nHttpClientJsonpModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientJsonpModule\n});\nHttpClientJsonpModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [withJsonpSupport().ɵproviders]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [withJsonpSupport().ɵproviders]\n    }]\n  }], null, null);\n})();\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\nconst XhrFactory = XhrFactory$1;\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, provideHttpClient, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler };", "map": {"version": 3, "names": ["i1", "DOCUMENT", "ɵparseCookieValue", "XhrFactory", "XhrFactory$1", "i0", "Injectable", "InjectionToken", "inject", "Inject", "PLATFORM_ID", "makeEnvironmentProviders", "NgModule", "of", "Observable", "concatMap", "filter", "map", "HttpHandler", "HttpBackend", "HttpHeaders", "constructor", "headers", "normalizedNames", "Map", "lazyUpdate", "lazyInit", "split", "for<PERSON>ach", "line", "index", "indexOf", "name", "slice", "key", "toLowerCase", "value", "trim", "maybeSetNormalizedName", "has", "get", "push", "set", "ngDevMode", "assertValidHeaders", "Object", "entries", "values", "headerValues", "toString", "length", "init", "keys", "Array", "from", "getAll", "append", "clone", "op", "delete", "lcName", "copyFrom", "update", "applyUpdate", "other", "concat", "base", "undefined", "toDelete", "existing", "fn", "isArray", "Error", "HttpUrlEncodingCodec", "encodeKey", "standardEncoding", "encodeValue", "decodeKey", "decodeURIComponent", "decodeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawParams", "codec", "params", "replace", "param", "eqIdx", "val", "list", "STANDARD_ENCODING_REGEX", "STANDARD_ENCODING_REPLACEMENTS", "v", "encodeURIComponent", "s", "t", "valueToString", "HttpParams", "options", "updates", "cloneFrom", "encoder", "fromString", "fromObject", "res", "appendAll", "_value", "<PERSON><PERSON><PERSON>", "join", "idx", "splice", "HttpContextToken", "defaultValue", "HttpContext", "token", "mightHaveBody", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "Blob", "isFormData", "FormData", "isUrlSearchParams", "URLSearchParams", "HttpRequest", "url", "third", "fourth", "body", "reportProgress", "withCredentials", "responseType", "toUpperCase", "context", "urlWithParams", "qIdx", "sep", "serializeBody", "JSON", "stringify", "detectContentTypeHeader", "type", "setHeaders", "reduce", "setParams", "HttpEventType", "HttpResponseBase", "defaultStatus", "defaultStatusText", "status", "statusText", "ok", "HttpHeaderResponse", "ResponseHeader", "HttpResponse", "Response", "HttpErrorResponse", "message", "error", "addBody", "observe", "HttpClient", "handler", "request", "first", "req", "events$", "pipe", "handle", "res$", "event", "head", "jsonp", "callback<PERSON><PERSON><PERSON>", "patch", "post", "put", "ɵfac", "HttpClient_Factory", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "factory", "ɵsetClassMetadata", "interceptorChainEndFn", "finalHandlerFn", "adaptLegacyInterceptorToChain", "chainTailFn", "interceptor", "initialRequest", "intercept", "downstreamRequest", "chainedInterceptorFn", "interceptorFn", "injector", "runInContext", "HTTP_INTERCEPTORS", "HTTP_INTERCEPTOR_FNS", "legacyInterceptorFnFactory", "chain", "interceptors", "optional", "reduceRight", "HttpInterceptorHandler", "backend", "dedupedInterceptorFns", "Set", "nextSequencedFn", "HttpInterceptorHandler_Factory", "EnvironmentInjector", "nextRequestId", "foreignDocument", "JSONP_ERR_NO_CALLBACK", "JSONP_ERR_WRONG_METHOD", "JSONP_ERR_WRONG_RESPONSE_TYPE", "JSONP_ERR_HEADERS_NOT_SUPPORTED", "JsonpCallbackContext", "jsonpCallbackContext", "window", "JsonpClientBackend", "callbackMap", "document", "resolvedPromise", "Promise", "resolve", "nextCallback", "observer", "callback", "node", "createElement", "src", "finished", "data", "cleanup", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "then", "next", "complete", "onError", "addEventListener", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "removeListeners", "script", "implementation", "createHTMLDocument", "adoptNode", "JsonpClientBackend_Factory", "decorators", "args", "jsonpInterceptorFn", "JsonpInterceptor", "JsonpInterceptor_Factory", "XSSI_PREFIX", "getResponseUrl", "xhr", "responseURL", "test", "getAllResponseHeaders", "getResponseHeader", "HttpXhrBackend", "xhrFactory", "build", "open", "setRequestHeader", "detectedType", "reqBody", "headerResponse", "partialFromXhr", "response", "responseText", "originalBody", "parse", "text", "sentHeaders", "onDownProgress", "progressEvent", "DownloadProgress", "loaded", "lengthComputable", "total", "partialText", "onUpProgress", "progress", "UploadProgress", "upload", "send", "removeEventListener", "readyState", "DONE", "abort", "HttpXhrBackend_Factory", "XSRF_ENABLED", "XSRF_DEFAULT_COOKIE_NAME", "XSRF_COOKIE_NAME", "providedIn", "XSRF_DEFAULT_HEADER_NAME", "XSRF_HEADER_NAME", "HttpXsrfTokenExtractor", "HttpXsrfCookieExtractor", "doc", "platform", "cookieName", "lastCookieString", "lastToken", "parseCount", "getToken", "cookieString", "cookie", "HttpXsrfCookieExtractor_Factory", "xsrfInterceptorFn", "lcUrl", "startsWith", "headerName", "HttpXsrfInterceptor", "HttpXsrfInterceptor_Factory", "HttpFeatureKind", "makeHttpFeature", "kind", "providers", "ɵkind", "ɵproviders", "provideHttpClient", "features", "featureKinds", "f", "NoXsrfProtection", "CustomXsrfConfiguration", "provide", "useExisting", "useValue", "multi", "useClass", "feature", "withInterceptors", "interceptorFns", "Interceptors", "LEGACY_INTERCEPTOR_FN", "withInterceptorsFromDi", "LegacyInterceptors", "useFactory", "withXsrfConfiguration", "withNoXsrfProtection", "withJsonpSupport", "JsonpSupport", "withRequestsMadeViaParent", "RequestsMadeViaParent", "handlerFromParent", "skipSelf", "HttpClientXsrfModule", "disable", "ngModule", "withOptions", "HttpClientXsrfModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "HttpClientModule", "HttpClientModule_Factory", "HttpClientJsonpModule", "HttpClientJsonpModule_Factory", "ɵHttpInterceptingHandler", "ɵHttpInterceptorHandler"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/common/fesm2020/http.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, inject, Inject, PLATFORM_ID, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {\n}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n    /**  Constructs a new HTTP header object with the given values.*/\n    constructor(headers) {\n        /**\n         * Internal map of lowercased header names to the normalized\n         * form of the name (the form seen first).\n         */\n        this.normalizedNames = new Map();\n        /**\n         * Queued updates to be materialized the next initialization.\n         */\n        this.lazyUpdate = null;\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                headers.split('\\n').forEach(line => {\n                    const index = line.indexOf(':');\n                    if (index > 0) {\n                        const name = line.slice(0, index);\n                        const key = name.toLowerCase();\n                        const value = line.slice(index + 1).trim();\n                        this.maybeSetNormalizedName(name, key);\n                        if (this.headers.has(key)) {\n                            this.headers.get(key).push(value);\n                        }\n                        else {\n                            this.headers.set(key, [value]);\n                        }\n                    }\n                });\n            };\n        }\n        else {\n            this.lazyInit = () => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    assertValidHeaders(headers);\n                }\n                this.headers = new Map();\n                Object.entries(headers).forEach(([name, values]) => {\n                    let headerValues;\n                    if (typeof values === 'string') {\n                        headerValues = [values];\n                    }\n                    else if (typeof values === 'number') {\n                        headerValues = [values.toString()];\n                    }\n                    else {\n                        headerValues = values.map((value) => value.toString());\n                    }\n                    if (headerValues.length > 0) {\n                        const key = name.toLowerCase();\n                        this.headers.set(key, headerValues);\n                        this.maybeSetNormalizedName(name, key);\n                    }\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of a given header.\n     *\n     * @param name The header name to check for existence.\n     *\n     * @returns True if the header exists, false otherwise.\n     */\n    has(name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    }\n    /**\n     * Retrieves the first value of a given header.\n     *\n     * @param name The header name.\n     *\n     * @returns The value string if the header exists, null otherwise\n     */\n    get(name) {\n        this.init();\n        const values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    }\n    /**\n     * Retrieves the names of the headers.\n     *\n     * @returns A list of header names.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    }\n    /**\n     * Retrieves a list of values for a given header.\n     *\n     * @param name The header name from which to retrieve values.\n     *\n     * @returns A string of values if the header exists, null otherwise.\n     */\n    getAll(name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    }\n    /**\n     * Appends a new value to the existing set of values for a header\n     * and returns them in a clone of the original instance.\n     *\n     * @param name The header name for which to append the values.\n     * @param value The value to append.\n     *\n     * @returns A clone of the HTTP headers object with the value appended to the given header.\n     */\n    append(name, value) {\n        return this.clone({ name, value, op: 'a' });\n    }\n    /**\n     * Sets or modifies a value for a given header in a clone of the original instance.\n     * If the header already exists, its value is replaced with the given value\n     * in the returned object.\n     *\n     * @param name The header name.\n     * @param value The value or values to set or override for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the newly set header value.\n     */\n    set(name, value) {\n        return this.clone({ name, value, op: 's' });\n    }\n    /**\n     * Deletes values for a given header in a clone of the original instance.\n     *\n     * @param name The header name.\n     * @param value The value or values to delete for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the given value deleted.\n     */\n    delete(name, value) {\n        return this.clone({ name, value, op: 'd' });\n    }\n    maybeSetNormalizedName(name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    }\n    init() {\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach(update => this.applyUpdate(update));\n                this.lazyUpdate = null;\n            }\n        }\n    }\n    copyFrom(other) {\n        other.init();\n        Array.from(other.headers.keys()).forEach(key => {\n            this.headers.set(key, other.headers.get(key));\n            this.normalizedNames.set(key, other.normalizedNames.get(key));\n        });\n    }\n    clone(update) {\n        const clone = new HttpHeaders();\n        clone.lazyInit =\n            (!!this.lazyInit && this.lazyInit instanceof HttpHeaders) ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    }\n    applyUpdate(update) {\n        const key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                let value = update.value;\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push(...value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                const toDelete = update.value;\n                if (!toDelete) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    let existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter(value => toDelete.indexOf(value) === -1);\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    }\n    /**\n     * @internal\n     */\n    forEach(fn) {\n        this.init();\n        Array.from(this.normalizedNames.keys())\n            .forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n    }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n    for (const [key, value] of Object.entries(headers)) {\n        if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n            throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` +\n                `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n        }\n    }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n    /**\n     * Encodes a key name for a URL parameter or query-string.\n     * @param key The key name.\n     * @returns The encoded key name.\n     */\n    encodeKey(key) {\n        return standardEncoding(key);\n    }\n    /**\n     * Encodes the value of a URL parameter or query-string.\n     * @param value The value.\n     * @returns The encoded value.\n     */\n    encodeValue(value) {\n        return standardEncoding(value);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string key.\n     * @param key The encoded key name.\n     * @returns The decoded key name.\n     */\n    decodeKey(key) {\n        return decodeURIComponent(key);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string value.\n     * @param value The encoded value.\n     * @returns The decoded value.\n     */\n    decodeValue(value) {\n        return decodeURIComponent(value);\n    }\n}\nfunction paramParser(rawParams, codec) {\n    const map = new Map();\n    if (rawParams.length > 0) {\n        // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n        // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n        // may start with the `?` char, so we strip it if it's present.\n        const params = rawParams.replace(/^\\?/, '').split('&');\n        params.forEach((param) => {\n            const eqIdx = param.indexOf('=');\n            const [key, val] = eqIdx == -1 ?\n                [codec.decodeKey(param), ''] :\n                [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n            const list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n    '40': '@',\n    '3A': ':',\n    '24': '$',\n    '2C': ',',\n    '3B': ';',\n    '3D': '=',\n    '3F': '?',\n    '2F': '/',\n};\nfunction standardEncoding(v) {\n    return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n    return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n    constructor(options = {}) {\n        this.updates = null;\n        this.cloneFrom = null;\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (!!options.fromString) {\n            if (!!options.fromObject) {\n                throw new Error(`Cannot specify both fromString and fromObject.`);\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach(key => {\n                const value = options.fromObject[key];\n                // convert the values to strings\n                const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n                this.map.set(key, values);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Reports whether the body includes one or more values for a given parameter.\n     * @param param The parameter name.\n     * @returns True if the parameter has one or more values,\n     * false if it has no value or is not present.\n     */\n    has(param) {\n        this.init();\n        return this.map.has(param);\n    }\n    /**\n     * Retrieves the first value for a parameter.\n     * @param param The parameter name.\n     * @returns The first value of the given parameter,\n     * or `null` if the parameter is not present.\n     */\n    get(param) {\n        this.init();\n        const res = this.map.get(param);\n        return !!res ? res[0] : null;\n    }\n    /**\n     * Retrieves all values for a  parameter.\n     * @param param The parameter name.\n     * @returns All values in a string array,\n     * or `null` if the parameter not present.\n     */\n    getAll(param) {\n        this.init();\n        return this.map.get(param) || null;\n    }\n    /**\n     * Retrieves all the parameters for this body.\n     * @returns The parameter names in a string array.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.map.keys());\n    }\n    /**\n     * Appends a new value to existing values for a parameter.\n     * @param param The parameter name.\n     * @param value The new value to add.\n     * @return A new body with the appended value.\n     */\n    append(param, value) {\n        return this.clone({ param, value, op: 'a' });\n    }\n    /**\n     * Constructs a new body with appended values for the given parameter name.\n     * @param params parameters and values\n     * @return A new body with the new value.\n     */\n    appendAll(params) {\n        const updates = [];\n        Object.keys(params).forEach(param => {\n            const value = params[param];\n            if (Array.isArray(value)) {\n                value.forEach(_value => {\n                    updates.push({ param, value: _value, op: 'a' });\n                });\n            }\n            else {\n                updates.push({ param, value: value, op: 'a' });\n            }\n        });\n        return this.clone(updates);\n    }\n    /**\n     * Replaces the value for a parameter.\n     * @param param The parameter name.\n     * @param value The new value.\n     * @return A new body with the new value.\n     */\n    set(param, value) {\n        return this.clone({ param, value, op: 's' });\n    }\n    /**\n     * Removes a given value or all values from a parameter.\n     * @param param The parameter name.\n     * @param value The value to remove, if provided.\n     * @return A new body with the given value removed, or with all values\n     * removed if no value is specified.\n     */\n    delete(param, value) {\n        return this.clone({ param, value, op: 'd' });\n    }\n    /**\n     * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    toString() {\n        this.init();\n        return this.keys()\n            .map(key => {\n            const eKey = this.encoder.encodeKey(key);\n            // `a: ['1']` produces `'a=1'`\n            // `b: []` produces `''`\n            // `c: ['1', '2']` produces `'c=1&c=2'`\n            return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value))\n                .join('&');\n        })\n            // filter out empty values because `b: []` produces `''`\n            // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n            .filter(param => param !== '')\n            .join('&');\n    }\n    clone(update) {\n        const clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat(update);\n        return clone;\n    }\n    init() {\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n            this.updates.forEach(update => {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n                        base.push(valueToString(update.value));\n                        this.map.set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            let base = this.map.get(update.param) || [];\n                            const idx = base.indexOf(valueToString(update.value));\n                            if (idx !== -1) {\n                                base.splice(idx, 1);\n                            }\n                            if (base.length > 0) {\n                                this.map.set(update.param, base);\n                            }\n                            else {\n                                this.map.delete(update.param);\n                            }\n                        }\n                        else {\n                            this.map.delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = this.updates = null;\n        }\n    }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n    constructor() {\n        this.map = new Map();\n    }\n    /**\n     * Store a value in the context. If a value is already present it will be overwritten.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     * @param value The value to store.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    set(token, value) {\n        this.map.set(token, value);\n        return this;\n    }\n    /**\n     * Retrieve the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns The stored value or default if one is defined.\n     */\n    get(token) {\n        if (!this.map.has(token)) {\n            this.map.set(token, token.defaultValue());\n        }\n        return this.map.get(token);\n    }\n    /**\n     * Delete the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    delete(token) {\n        this.map.delete(token);\n        return this;\n    }\n    /**\n     * Checks for existence of a given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns True if the token exists, false otherwise.\n     */\n    has(token) {\n        return this.map.has(token);\n    }\n    /**\n     * @returns a list of tokens currently stored in the context.\n     */\n    keys() {\n        return this.map.keys();\n    }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n    return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n    constructor(method, url, third, fourth) {\n        this.url = url;\n        /**\n         * The request body, or `null` if one isn't set.\n         *\n         * Bodies are not enforced to be immutable, as they can include a reference to any\n         * user-defined data type. However, interceptors should take care to preserve\n         * idempotence by treating them as such.\n         */\n        this.body = null;\n        /**\n         * Whether this request should be made in a way that exposes progress events.\n         *\n         * Progress events are expensive (change detection runs on each event) and so\n         * they should only be requested if the consumer intends to monitor them.\n         */\n        this.reportProgress = false;\n        /**\n         * Whether this request should be sent with outgoing credentials (cookies).\n         */\n        this.withCredentials = false;\n        /**\n         * The expected response type of the server.\n         *\n         * This is used to parse the response appropriately before returning it to\n         * the requestee.\n         */\n        this.responseType = 'json';\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        let options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = (third !== undefined) ? third : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = third;\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.context) {\n                this.context = options.context;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        if (!this.headers) {\n            this.headers = new HttpHeaders();\n        }\n        // If no context have been passed in, construct a new HttpContext instance.\n        if (!this.context) {\n            this.context = new HttpContext();\n        }\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            const params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                const qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                const sep = qIdx === -1 ? '?' : (qIdx < url.length - 1 ? '&' : '');\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    serializeBody() {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) ||\n            isUrlSearchParams(this.body) || typeof this.body === 'string') {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' || typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return this.body.toString();\n    }\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    detectContentTypeHeader() {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return 'text/plain';\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, boolean and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' || typeof this.body === 'number' ||\n            typeof this.body === 'boolean') {\n            return 'application/json';\n        }\n        // No type could be inferred.\n        return null;\n    }\n    clone(update = {}) {\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        const method = update.method || this.method;\n        const url = update.url || this.url;\n        const responseType = update.responseType || this.responseType;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        const body = (update.body !== undefined) ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        const withCredentials = (update.withCredentials !== undefined) ? update.withCredentials : this.withCredentials;\n        const reportProgress = (update.reportProgress !== undefined) ? update.reportProgress : this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        let headers = update.headers || this.headers;\n        let params = update.params || this.params;\n        // Pass on context if needed\n        const context = update.context ?? this.context;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers =\n                Object.keys(update.setHeaders)\n                    .reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams)\n                .reduce((params, param) => params.set(param, update.setParams[param]), params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params,\n            headers,\n            context,\n            reportProgress,\n            responseType,\n            withCredentials,\n        });\n    }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n    /**\n     * The request was sent out over the wire.\n     */\n    HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n    /**\n     * An upload progress event was received.\n     */\n    HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n    /**\n     * The response status code and headers were received.\n     */\n    HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n    /**\n     * A download progress event was received.\n     */\n    HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n    /**\n     * The full response including the body was received.\n     */\n    HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n    /**\n     * A custom event from an interceptor or a backend.\n     */\n    HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    constructor(init, defaultStatus = 200 /* HttpStatusCode.Ok */, defaultStatusText = 'OK') {\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.ResponseHeader;\n    }\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    clone(update = {}) {\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.Response;\n        this.body = init.body !== undefined ? init.body : null;\n    }\n    clone(update = {}) {\n        return new HttpResponse({\n            body: (update.body !== undefined) ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: (update.status !== undefined) ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n    constructor(init) {\n        // Initialize with a default status of 0 / Unknown Error.\n        super(init, 0, 'Unknown Error');\n        this.name = 'HttpErrorResponse';\n        /**\n         * Errors are never okay, even when the status code is in the 2xx success range.\n         */\n        this.ok = false;\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (this.status >= 200 && this.status < 300) {\n            this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n        }\n        else {\n            this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n        }\n        this.error = init.error || null;\n    }\n}\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n    return {\n        body,\n        headers: options.headers,\n        context: options.context,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n    };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/tour-of-heroes/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n    request(first, url, options = {}) {\n        let req;\n        // First, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = first;\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming `GET` unless a method is\n            // provided.\n            // Figure out the headers.\n            let headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            let params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, url, (options.body !== undefined ? options.body : null), {\n                headers,\n                context: options.context,\n                params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        const events$ = of(req).pipe(concatMap((req) => this.handler.handle(req)));\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        const res$ = events$.pipe(filter((event) => event instanceof HttpResponse));\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new Error('Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        }));\n                    case 'blob':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new Error('Response is not a Blob.');\n                            }\n                            return res.body;\n                        }));\n                    case 'text':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new Error('Response is not a string.');\n                            }\n                            return res.body;\n                        }));\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return res$.pipe(map((res) => res.body));\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n        }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n    delete(url, options = {}) {\n        return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    get(url, options = {}) {\n        return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n    head(url, options = {}) {\n        return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n    jsonp(url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n    options(url, options = {}) {\n        return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    patch(url, body, options = {}) {\n        return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n    post(url, body, options = {}) {\n        return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n    put(url, body, options = {}) {\n        return this.request('PUT', url, addBody(options, body));\n    }\n}\nHttpClient.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClient, deps: [{ token: HttpHandler }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpClient.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClient });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClient, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpHandler }]; } });\n\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n    return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n    return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n        handle: (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn),\n    });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n    // clang-format off\n    return (initialRequest, finalHandlerFn) => injector.runInContext(() => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n    // clang-format on\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken('HTTP_INTERCEPTOR_FNS');\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n    let chain = null;\n    return (req, handler) => {\n        if (chain === null) {\n            const interceptors = inject(HTTP_INTERCEPTORS, { optional: true }) ?? [];\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n        }\n        return chain(req, handler);\n    };\n}\nclass HttpInterceptorHandler extends HttpHandler {\n    constructor(backend, injector) {\n        super();\n        this.backend = backend;\n        this.injector = injector;\n        this.chain = null;\n    }\n    handle(initialRequest) {\n        if (this.chain === null) {\n            const dedupedInterceptorFns = Array.from(new Set(this.injector.get(HTTP_INTERCEPTOR_FNS)));\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n        }\n        return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n    }\n}\nHttpInterceptorHandler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpInterceptorHandler, deps: [{ token: HttpBackend }, { token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpInterceptorHandler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpInterceptorHandler });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpInterceptorHandler, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpBackend }, { type: i0.EnvironmentInjector }]; } });\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n    constructor(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n        /**\n         * A resolved promise that can be used to schedule microtasks in the event handlers.\n         */\n        this.resolvedPromise = Promise.resolve();\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n    nextCallback() {\n        return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n    handle(req) {\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Check the request headers. JSONP doesn't support headers and\n        // cannot set any that were supplied.\n        if (req.headers.keys().length > 0) {\n            throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable((observer) => {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            const callback = this.nextCallback();\n            const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n            // Construct the <script> tag and point it at the URL.\n            const node = this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            let body = null;\n            // Whether the response callback has been called.\n            let finished = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            this.callbackMap[callback] = (data) => {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete this.callbackMap[callback];\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            const cleanup = () => {\n                // Remove the <script> tag if it's still on the page.\n                if (node.parentNode) {\n                    node.parentNode.removeChild(node);\n                }\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            const onLoad = (event) => {\n                // We wrap it in an extra Promise, to ensure the microtask\n                // is scheduled after the loaded endpoint has executed any potential microtask itself,\n                // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n                this.resolvedPromise.then(() => {\n                    // Cleanup the page.\n                    cleanup();\n                    // Check whether the response callback has run.\n                    if (!finished) {\n                        // It hasn't, something went wrong with the request. Return an error via\n                        // the Observable error path. All JSONP errors have status 0.\n                        observer.error(new HttpErrorResponse({\n                            url,\n                            status: 0,\n                            statusText: 'JSONP Error',\n                            error: new Error(JSONP_ERR_NO_CALLBACK),\n                        }));\n                        return;\n                    }\n                    // Success. body either contains the response body or null if none was\n                    // returned.\n                    observer.next(new HttpResponse({\n                        body,\n                        status: 200 /* HttpStatusCode.Ok */,\n                        statusText: 'OK',\n                        url,\n                    }));\n                    // Complete the stream, the response is over.\n                    observer.complete();\n                });\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            const onError = (error) => {\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error,\n                    status: 0,\n                    statusText: 'JSONP Error',\n                    url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return () => {\n                if (!finished) {\n                    this.removeListeners(node);\n                }\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    }\n    removeListeners(script) {\n        // Issue #34818\n        // Changing <script>'s ownerDocument will prevent it from execution.\n        // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n        if (!foreignDocument) {\n            foreignDocument = this.document.implementation.createHTMLDocument();\n        }\n        foreignDocument.adoptNode(script);\n    }\n}\nJsonpClientBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: JsonpClientBackend, deps: [{ token: JsonpCallbackContext }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nJsonpClientBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: JsonpClientBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: JsonpClientBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: JsonpCallbackContext }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n    if (req.method === 'JSONP') {\n        return inject(JsonpClientBackend).handle(req);\n    }\n    // Fall through for normal HTTP requests.\n    return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n    constructor(injector) {\n        this.injector = injector;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param initialRequest The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n    intercept(initialRequest, next) {\n        return this.injector.runInContext(() => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n    }\n}\nJsonpInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: JsonpInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\nJsonpInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: JsonpInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: JsonpInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.EnvironmentInjector }]; } });\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader('X-Request-URL');\n    }\n    return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n    constructor(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n    handle(req) {\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the HttpClientJsonpModule\n        if (req.method === 'JSONP') {\n            throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n        }\n        // Everything happens on Observable subscription.\n        return new Observable((observer) => {\n            // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n            const xhr = this.xhrFactory.build();\n            xhr.open(req.method, req.urlWithParams);\n            if (!!req.withCredentials) {\n                xhr.withCredentials = true;\n            }\n            // Add all the requested headers.\n            req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n            // Add an Accept header if one isn't present already.\n            if (!req.headers.has('Accept')) {\n                xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n            }\n            // Auto-detect the Content-Type header if one isn't present already.\n            if (!req.headers.has('Content-Type')) {\n                const detectedType = req.detectContentTypeHeader();\n                // Sometimes Content-Type detection fails.\n                if (detectedType !== null) {\n                    xhr.setRequestHeader('Content-Type', detectedType);\n                }\n            }\n            // Set the responseType if one was requested.\n            if (req.responseType) {\n                const responseType = req.responseType.toLowerCase();\n                // JSON responses need to be processed as text. This is because if the server\n                // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                // xhr.response will be null, and xhr.responseText cannot be accessed to\n                // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                // is parsed by first requesting text and then applying JSON.parse.\n                xhr.responseType = ((responseType !== 'json') ? responseType : 'text');\n            }\n            // Serialize the request body if one is present. If not, this will be set to null.\n            const reqBody = req.serializeBody();\n            // If progress events are enabled, response headers will be delivered\n            // in two events - the HttpHeaderResponse event and the full HttpResponse\n            // event. However, since response headers don't change in between these\n            // two events, it doesn't make sense to parse them twice. So headerResponse\n            // caches the data extracted from the response whenever it's first parsed,\n            // to ensure parsing isn't duplicated.\n            let headerResponse = null;\n            // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n            // state, and memoizes it into headerResponse.\n            const partialFromXhr = () => {\n                if (headerResponse !== null) {\n                    return headerResponse;\n                }\n                const statusText = xhr.statusText || 'OK';\n                // Parse headers from XMLHttpRequest - this step is lazy.\n                const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                // Read the response URL from the XMLHttpResponse instance and fall back on the\n                // request URL.\n                const url = getResponseUrl(xhr) || req.url;\n                // Construct the HttpHeaderResponse and memoize it.\n                headerResponse = new HttpHeaderResponse({ headers, status: xhr.status, statusText, url });\n                return headerResponse;\n            };\n            // Next, a few closures are defined for the various events which XMLHttpRequest can\n            // emit. This allows them to be unregistered as event listeners later.\n            // First up is the load event, which represents a response being fully available.\n            const onLoad = () => {\n                // Read response state from the memoized partial data.\n                let { headers, status, statusText, url } = partialFromXhr();\n                // The body will be read out if present.\n                let body = null;\n                if (status !== 204 /* HttpStatusCode.NoContent */) {\n                    // Use XMLHttpRequest.response if set, responseText otherwise.\n                    body = (typeof xhr.response === 'undefined') ? xhr.responseText : xhr.response;\n                }\n                // Normalize another potential bug (this one comes from CORS).\n                if (status === 0) {\n                    status = !!body ? 200 /* HttpStatusCode.Ok */ : 0;\n                }\n                // ok determines whether the response will be transmitted on the event or\n                // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                // but a successful status code can still result in an error if the user\n                // asked for JSON data and the body cannot be parsed as such.\n                let ok = status >= 200 && status < 300;\n                // Check whether the body needs to be parsed as JSON (in many cases the browser\n                // will have done that already).\n                if (req.responseType === 'json' && typeof body === 'string') {\n                    // Save the original body, before attempting XSSI prefix stripping.\n                    const originalBody = body;\n                    body = body.replace(XSSI_PREFIX, '');\n                    try {\n                        // Attempt the parse. If it fails, a parse error should be delivered to the user.\n                        body = body !== '' ? JSON.parse(body) : null;\n                    }\n                    catch (error) {\n                        // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n                        // JSON response. Restore the original body (including any XSSI prefix) to deliver\n                        // a better error response.\n                        body = originalBody;\n                        // If this was an error request to begin with, leave it as a string, it probably\n                        // just isn't JSON. Otherwise, deliver the parsing error to the user.\n                        if (ok) {\n                            // Even though the response status was 2xx, this is still an error.\n                            ok = false;\n                            // The parse error contains the text of the body that failed to parse.\n                            body = { error, text: body };\n                        }\n                    }\n                }\n                if (ok) {\n                    // A successful response is delivered on the event stream.\n                    observer.next(new HttpResponse({\n                        body,\n                        headers,\n                        status,\n                        statusText,\n                        url: url || undefined,\n                    }));\n                    // The full body has been received and delivered, no further events\n                    // are possible. This request is complete.\n                    observer.complete();\n                }\n                else {\n                    // An unsuccessful request is delivered on the error channel.\n                    observer.error(new HttpErrorResponse({\n                        // The error in this case is the response body (error from the server).\n                        error: body,\n                        headers,\n                        status,\n                        statusText,\n                        url: url || undefined,\n                    }));\n                }\n            };\n            // The onError callback is called when something goes wrong at the network level.\n            // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n            // transmitted on the error channel.\n            const onError = (error) => {\n                const { url } = partialFromXhr();\n                const res = new HttpErrorResponse({\n                    error,\n                    status: xhr.status || 0,\n                    statusText: xhr.statusText || 'Unknown Error',\n                    url: url || undefined,\n                });\n                observer.error(res);\n            };\n            // The sentHeaders flag tracks whether the HttpResponseHeaders event\n            // has been sent on the stream. This is necessary to track if progress\n            // is enabled since the event will be sent on only the first download\n            // progress event.\n            let sentHeaders = false;\n            // The download progress event handler, which is only registered if\n            // progress events are enabled.\n            const onDownProgress = (event) => {\n                // Send the HttpResponseHeaders event if it hasn't been sent already.\n                if (!sentHeaders) {\n                    observer.next(partialFromXhr());\n                    sentHeaders = true;\n                }\n                // Start building the download progress event to deliver on the response\n                // event stream.\n                let progressEvent = {\n                    type: HttpEventType.DownloadProgress,\n                    loaded: event.loaded,\n                };\n                // Set the total number of bytes in the event if it's available.\n                if (event.lengthComputable) {\n                    progressEvent.total = event.total;\n                }\n                // If the request was for text content and a partial response is\n                // available on XMLHttpRequest, include it in the progress event\n                // to allow for streaming reads.\n                if (req.responseType === 'text' && !!xhr.responseText) {\n                    progressEvent.partialText = xhr.responseText;\n                }\n                // Finally, fire the event.\n                observer.next(progressEvent);\n            };\n            // The upload progress event handler, which is only registered if\n            // progress events are enabled.\n            const onUpProgress = (event) => {\n                // Upload progress events are simpler. Begin building the progress\n                // event.\n                let progress = {\n                    type: HttpEventType.UploadProgress,\n                    loaded: event.loaded,\n                };\n                // If the total number of bytes being uploaded is available, include\n                // it.\n                if (event.lengthComputable) {\n                    progress.total = event.total;\n                }\n                // Send the event.\n                observer.next(progress);\n            };\n            // By default, register for load and error events.\n            xhr.addEventListener('load', onLoad);\n            xhr.addEventListener('error', onError);\n            xhr.addEventListener('timeout', onError);\n            xhr.addEventListener('abort', onError);\n            // Progress events are only enabled if requested.\n            if (req.reportProgress) {\n                // Download progress is always enabled if requested.\n                xhr.addEventListener('progress', onDownProgress);\n                // Upload progress depends on whether there is a body to upload.\n                if (reqBody !== null && xhr.upload) {\n                    xhr.upload.addEventListener('progress', onUpProgress);\n                }\n            }\n            // Fire the request, and notify the event stream that it was fired.\n            xhr.send(reqBody);\n            observer.next({ type: HttpEventType.Sent });\n            // This is the return from the Observable function, which is the\n            // request cancellation handler.\n            return () => {\n                // On a cancellation, remove all registered event listeners.\n                xhr.removeEventListener('error', onError);\n                xhr.removeEventListener('abort', onError);\n                xhr.removeEventListener('load', onLoad);\n                xhr.removeEventListener('timeout', onError);\n                if (req.reportProgress) {\n                    xhr.removeEventListener('progress', onDownProgress);\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.removeEventListener('progress', onUpProgress);\n                    }\n                }\n                // Finally, abort the in-flight request.\n                if (xhr.readyState !== xhr.DONE) {\n                    xhr.abort();\n                }\n            };\n        });\n    }\n}\nHttpXhrBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXhrBackend, deps: [{ token: i1.XhrFactory }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXhrBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXhrBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXhrBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1.XhrFactory }]; } });\n\nconst XSRF_ENABLED = new InjectionToken('XSRF_ENABLED');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_COOKIE_NAME,\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_HEADER_NAME,\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n    constructor(doc, platform, cookieName) {\n        this.doc = doc;\n        this.platform = platform;\n        this.cookieName = cookieName;\n        this.lastCookieString = '';\n        this.lastToken = null;\n        /**\n         * @internal for testing\n         */\n        this.parseCount = 0;\n    }\n    getToken() {\n        if (this.platform === 'server') {\n            return null;\n        }\n        const cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    }\n}\nHttpXsrfCookieExtractor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXsrfCookieExtractor, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: XSRF_COOKIE_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXsrfCookieExtractor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXsrfCookieExtractor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXsrfCookieExtractor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_COOKIE_NAME]\n                }] }]; } });\nfunction xsrfInterceptorFn(req, next) {\n    const lcUrl = req.url.toLowerCase();\n    // Skip both non-mutating requests and absolute URLs.\n    // Non-mutating requests don't require a token, and absolute URLs require special handling\n    // anyway as the cookie set\n    // on our origin is not the same as the token expected by another origin.\n    if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' ||\n        lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n        return next(req);\n    }\n    const token = inject(HttpXsrfTokenExtractor).getToken();\n    const headerName = inject(XSRF_HEADER_NAME);\n    // Be careful not to overwrite an existing header of the same name.\n    if (token != null && !req.headers.has(headerName)) {\n        req = req.clone({ headers: req.headers.set(headerName, token) });\n    }\n    return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n    constructor(injector) {\n        this.injector = injector;\n    }\n    intercept(initialRequest, next) {\n        return this.injector.runInContext(() => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n    }\n}\nHttpXsrfInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXsrfInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXsrfInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXsrfInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpXsrfInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.EnvironmentInjector }]; } });\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n    HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n    HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n    HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n    HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n    return {\n        ɵkind: kind,\n        ɵproviders: providers,\n    };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n */\nfunction provideHttpClient(...features) {\n    if (ngDevMode) {\n        const featureKinds = new Set(features.map(f => f.ɵkind));\n        if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) &&\n            featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n            throw new Error(ngDevMode ?\n                `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` :\n                '');\n        }\n    }\n    const providers = [\n        HttpClient,\n        HttpXhrBackend,\n        HttpInterceptorHandler,\n        { provide: HttpHandler, useExisting: HttpInterceptorHandler },\n        { provide: HttpBackend, useExisting: HttpXhrBackend },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: xsrfInterceptorFn,\n            multi: true,\n        },\n        { provide: XSRF_ENABLED, useValue: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n    ];\n    for (const feature of features) {\n        providers.push(...feature.ɵproviders);\n    }\n    return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n    return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n        return {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: interceptorFn,\n            multi: true,\n        };\n    }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken('LEGACY_INTERCEPTOR_FN');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n    // Note: the legacy interceptor function is provided here via an intermediate token\n    // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n    // included multiple times, all of the multi-provider entries will have the same instance of the\n    // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n    // interceptors will not run multiple times.\n    return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [\n        {\n            provide: LEGACY_INTERCEPTOR_FN,\n            useFactory: legacyInterceptorFnFactory,\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useExisting: LEGACY_INTERCEPTOR_FN,\n            multi: true,\n        }\n    ]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({ cookieName, headerName }) {\n    const providers = [];\n    if (cookieName !== undefined) {\n        providers.push({ provide: XSRF_COOKIE_NAME, useValue: cookieName });\n    }\n    if (headerName !== undefined) {\n        providers.push({ provide: XSRF_HEADER_NAME, useValue: headerName });\n    }\n    return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n    return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [\n        {\n            provide: XSRF_ENABLED,\n            useValue: false,\n        },\n    ]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n    return makeHttpFeature(HttpFeatureKind.JsonpSupport, [\n        JsonpClientBackend,\n        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n        { provide: HTTP_INTERCEPTOR_FNS, useValue: jsonpInterceptorFn, multi: true },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @developerPreview\n */\nfunction withRequestsMadeViaParent() {\n    return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                const handlerFromParent = inject(HttpHandler, { skipSelf: true, optional: true });\n                if (ngDevMode && handlerFromParent === null) {\n                    throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n                }\n                return handlerFromParent;\n            },\n        },\n    ]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\nclass HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                withNoXsrfProtection().ɵproviders,\n            ],\n        };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n    static withOptions(options = {}) {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: withXsrfConfiguration(options).ɵproviders,\n        };\n    }\n}\nHttpClientXsrfModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientXsrfModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientXsrfModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientXsrfModule });\nHttpClientXsrfModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientXsrfModule, providers: [\n        HttpXsrfInterceptor,\n        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n        withXsrfConfiguration({\n            cookieName: XSRF_DEFAULT_COOKIE_NAME,\n            headerName: XSRF_DEFAULT_HEADER_NAME,\n        }).ɵproviders,\n        { provide: XSRF_ENABLED, useValue: true },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientXsrfModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        withXsrfConfiguration({\n                            cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                            headerName: XSRF_DEFAULT_HEADER_NAME,\n                        }).ɵproviders,\n                        { provide: XSRF_ENABLED, useValue: true },\n                    ],\n                }]\n        }] });\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\nclass HttpClientModule {\n}\nHttpClientModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientModule });\nHttpClientModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientModule, providers: [\n        provideHttpClient(withInterceptorsFromDi()),\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    /**\n                     * Configures the [dependency injector](guide/glossary#injector) where it is imported\n                     * with supporting services for HTTP communications.\n                     */\n                    providers: [\n                        provideHttpClient(withInterceptorsFromDi()),\n                    ],\n                }]\n        }] });\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n */\nclass HttpClientJsonpModule {\n}\nHttpClientJsonpModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientJsonpModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientJsonpModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientJsonpModule });\nHttpClientJsonpModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientJsonpModule, providers: [\n        withJsonpSupport().ɵproviders,\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HttpClientJsonpModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        withJsonpSupport().ɵproviders,\n                    ],\n                }]\n        }] });\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\nconst XhrFactory = XhrFactory$1;\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, provideHttpClient, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,UAAU,IAAIC,YAAY,QAAQ,iBAAiB;AACzF,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,eAAe;AAC3H,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,SAAS,EAAEC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;;AAGlB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChC;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACH,OAAO,EAAE;MACV,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC5B,CAAC,MACI,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAClC,IAAI,CAACI,QAAQ,GAAG,MAAM;QAClB,IAAI,CAACJ,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBF,OAAO,CAACK,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;UAChC,MAAMC,KAAK,GAAGD,IAAI,CAACE,OAAO,CAAC,GAAG,CAAC;UAC/B,IAAID,KAAK,GAAG,CAAC,EAAE;YACX,MAAME,IAAI,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;YACjC,MAAMI,GAAG,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;YAC9B,MAAMC,KAAK,GAAGP,IAAI,CAACI,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;YAC1C,IAAI,CAACC,sBAAsB,CAACN,IAAI,EAAEE,GAAG,CAAC;YACtC,IAAI,IAAI,CAACZ,OAAO,CAACiB,GAAG,CAACL,GAAG,CAAC,EAAE;cACvB,IAAI,CAACZ,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,CAACO,IAAI,CAACL,KAAK,CAAC;YACrC,CAAC,MACI;cACD,IAAI,CAACd,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAE,CAACE,KAAK,CAAC,CAAC;YAClC;UACJ;QACJ,CAAC,CAAC;MACN,CAAC;IACL,CAAC,MACI;MACD,IAAI,CAACV,QAAQ,GAAG,MAAM;QAClB,IAAI,OAAOiB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/CC,kBAAkB,CAACtB,OAAO,CAAC;QAC/B;QACA,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBqB,MAAM,CAACC,OAAO,CAACxB,OAAO,CAAC,CAACM,OAAO,CAAC,CAAC,CAACI,IAAI,EAAEe,MAAM,CAAC,KAAK;UAChD,IAAIC,YAAY;UAChB,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;YAC5BC,YAAY,GAAG,CAACD,MAAM,CAAC;UAC3B,CAAC,MACI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;YACjCC,YAAY,GAAG,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC;UACtC,CAAC,MACI;YACDD,YAAY,GAAGD,MAAM,CAAC9B,GAAG,CAAEmB,KAAK,IAAKA,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;UAC1D;UACA,IAAID,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;YACzB,MAAMhB,GAAG,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;YAC9B,IAAI,CAACb,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAEc,YAAY,CAAC;YACnC,IAAI,CAACV,sBAAsB,CAACN,IAAI,EAAEE,GAAG,CAAC;UAC1C;QACJ,CAAC,CAAC;MACN,CAAC;IACL;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,GAAGA,CAACP,IAAI,EAAE;IACN,IAAI,CAACmB,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC7B,OAAO,CAACiB,GAAG,CAACP,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,GAAGA,CAACR,IAAI,EAAE;IACN,IAAI,CAACmB,IAAI,CAAC,CAAC;IACX,MAAMJ,MAAM,GAAG,IAAI,CAACzB,OAAO,CAACkB,GAAG,CAACR,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;IACnD,OAAOY,MAAM,IAAIA,MAAM,CAACG,MAAM,GAAG,CAAC,GAAGH,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;EACzD;EACA;AACJ;AACA;AACA;AACA;EACIK,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,IAAI,CAAC,CAAC;IACX,OAAOE,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/B,eAAe,CAACwB,MAAM,CAAC,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,MAAMA,CAACvB,IAAI,EAAE;IACT,IAAI,CAACmB,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC7B,OAAO,CAACkB,GAAG,CAACR,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqB,MAAMA,CAACxB,IAAI,EAAEI,KAAK,EAAE;IAChB,OAAO,IAAI,CAACqB,KAAK,CAAC;MAAEzB,IAAI;MAAEI,KAAK;MAAEsB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhB,GAAGA,CAACV,IAAI,EAAEI,KAAK,EAAE;IACb,OAAO,IAAI,CAACqB,KAAK,CAAC;MAAEzB,IAAI;MAAEI,KAAK;MAAEsB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAC3B,IAAI,EAAEI,KAAK,EAAE;IAChB,OAAO,IAAI,CAACqB,KAAK,CAAC;MAAEzB,IAAI;MAAEI,KAAK;MAAEsB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACApB,sBAAsBA,CAACN,IAAI,EAAE4B,MAAM,EAAE;IACjC,IAAI,CAAC,IAAI,CAACrC,eAAe,CAACgB,GAAG,CAACqB,MAAM,CAAC,EAAE;MACnC,IAAI,CAACrC,eAAe,CAACmB,GAAG,CAACkB,MAAM,EAAE5B,IAAI,CAAC;IAC1C;EACJ;EACAmB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,CAAC,IAAI,CAACzB,QAAQ,EAAE;MACjB,IAAI,IAAI,CAACA,QAAQ,YAAYN,WAAW,EAAE;QACtC,IAAI,CAACyC,QAAQ,CAAC,IAAI,CAACnC,QAAQ,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC,CAAC,IAAI,CAACD,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACG,OAAO,CAACkC,MAAM,IAAI,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC,CAAC;QAC3D,IAAI,CAACrC,UAAU,GAAG,IAAI;MAC1B;IACJ;EACJ;EACAoC,QAAQA,CAACG,KAAK,EAAE;IACZA,KAAK,CAACb,IAAI,CAAC,CAAC;IACZE,KAAK,CAACC,IAAI,CAACU,KAAK,CAAC1C,OAAO,CAAC8B,IAAI,CAAC,CAAC,CAAC,CAACxB,OAAO,CAACM,GAAG,IAAI;MAC5C,IAAI,CAACZ,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAE8B,KAAK,CAAC1C,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACX,eAAe,CAACmB,GAAG,CAACR,GAAG,EAAE8B,KAAK,CAACzC,eAAe,CAACiB,GAAG,CAACN,GAAG,CAAC,CAAC;IACjE,CAAC,CAAC;EACN;EACAuB,KAAKA,CAACK,MAAM,EAAE;IACV,MAAML,KAAK,GAAG,IAAIrC,WAAW,CAAC,CAAC;IAC/BqC,KAAK,CAAC/B,QAAQ,GACT,CAAC,CAAC,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ,YAAYN,WAAW,GAAI,IAAI,CAACM,QAAQ,GAAG,IAAI;IACpF+B,KAAK,CAAChC,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU,IAAI,EAAE,EAAEwC,MAAM,CAAC,CAACH,MAAM,CAAC,CAAC;IAC3D,OAAOL,KAAK;EAChB;EACAM,WAAWA,CAACD,MAAM,EAAE;IAChB,MAAM5B,GAAG,GAAG4B,MAAM,CAAC9B,IAAI,CAACG,WAAW,CAAC,CAAC;IACrC,QAAQ2B,MAAM,CAACJ,EAAE;MACb,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,IAAItB,KAAK,GAAG0B,MAAM,CAAC1B,KAAK;QACxB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC;QACnB;QACA,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;UACpB;QACJ;QACA,IAAI,CAACZ,sBAAsB,CAACwB,MAAM,CAAC9B,IAAI,EAAEE,GAAG,CAAC;QAC7C,MAAMgC,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAE,KAAK,GAAG,GAAG,IAAI,CAACpC,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,GAAGiC,SAAS,KAAK,EAAE;QAC1ED,IAAI,CAACzB,IAAI,CAAC,GAAGL,KAAK,CAAC;QACnB,IAAI,CAACd,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAEgC,IAAI,CAAC;QAC3B;MACJ,KAAK,GAAG;QACJ,MAAME,QAAQ,GAAGN,MAAM,CAAC1B,KAAK;QAC7B,IAAI,CAACgC,QAAQ,EAAE;UACX,IAAI,CAAC9C,OAAO,CAACqC,MAAM,CAACzB,GAAG,CAAC;UACxB,IAAI,CAACX,eAAe,CAACoC,MAAM,CAACzB,GAAG,CAAC;QACpC,CAAC,MACI;UACD,IAAImC,QAAQ,GAAG,IAAI,CAAC/C,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC;UACpC,IAAI,CAACmC,QAAQ,EAAE;YACX;UACJ;UACAA,QAAQ,GAAGA,QAAQ,CAACrD,MAAM,CAACoB,KAAK,IAAIgC,QAAQ,CAACrC,OAAO,CAACK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;UACnE,IAAIiC,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC5B,OAAO,CAACqC,MAAM,CAACzB,GAAG,CAAC;YACxB,IAAI,CAACX,eAAe,CAACoC,MAAM,CAACzB,GAAG,CAAC;UACpC,CAAC,MACI;YACD,IAAI,CAACZ,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAEmC,QAAQ,CAAC;UACnC;QACJ;QACA;IACR;EACJ;EACA;AACJ;AACA;EACIzC,OAAOA,CAAC0C,EAAE,EAAE;IACR,IAAI,CAACnB,IAAI,CAAC,CAAC;IACXE,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/B,eAAe,CAAC6B,IAAI,CAAC,CAAC,CAAC,CAClCxB,OAAO,CAACM,GAAG,IAAIoC,EAAE,CAAC,IAAI,CAAC/C,eAAe,CAACiB,GAAG,CAACN,GAAG,CAAC,EAAE,IAAI,CAACZ,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC;EACjF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,kBAAkBA,CAACtB,OAAO,EAAE;EACjC,KAAK,MAAM,CAACY,GAAG,EAAEE,KAAK,CAAC,IAAIS,MAAM,CAACC,OAAO,CAACxB,OAAO,CAAC,EAAE;IAChD,IAAI,EAAE,OAAOc,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC,IAAI,CAACiB,KAAK,CAACkB,OAAO,CAACnC,KAAK,CAAC,EAAE;MACpF,MAAM,IAAIoC,KAAK,CAAE,6BAA4BtC,GAAI,sBAAqB,GACjE,+DAA8DE,KAAM,KAAI,CAAC;IAClF;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqC,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACxC,GAAG,EAAE;IACX,OAAOyC,gBAAgB,CAACzC,GAAG,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI0C,WAAWA,CAACxC,KAAK,EAAE;IACf,OAAOuC,gBAAgB,CAACvC,KAAK,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIyC,SAASA,CAAC3C,GAAG,EAAE;IACX,OAAO4C,kBAAkB,CAAC5C,GAAG,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI6C,WAAWA,CAAC3C,KAAK,EAAE;IACf,OAAO0C,kBAAkB,CAAC1C,KAAK,CAAC;EACpC;AACJ;AACA,SAAS4C,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACnC,MAAMjE,GAAG,GAAG,IAAIO,GAAG,CAAC,CAAC;EACrB,IAAIyD,SAAS,CAAC/B,MAAM,GAAG,CAAC,EAAE;IACtB;IACA;IACA;IACA,MAAMiC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACzD,KAAK,CAAC,GAAG,CAAC;IACtDwD,MAAM,CAACvD,OAAO,CAAEyD,KAAK,IAAK;MACtB,MAAMC,KAAK,GAAGD,KAAK,CAACtD,OAAO,CAAC,GAAG,CAAC;MAChC,MAAM,CAACG,GAAG,EAAEqD,GAAG,CAAC,GAAGD,KAAK,IAAI,CAAC,CAAC,GAC1B,CAACJ,KAAK,CAACL,SAAS,CAACQ,KAAK,CAAC,EAAE,EAAE,CAAC,GAC5B,CAACH,KAAK,CAACL,SAAS,CAACQ,KAAK,CAACpD,KAAK,CAAC,CAAC,EAAEqD,KAAK,CAAC,CAAC,EAAEJ,KAAK,CAACH,WAAW,CAACM,KAAK,CAACpD,KAAK,CAACqD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;MACvF,MAAME,IAAI,GAAGvE,GAAG,CAACuB,GAAG,CAACN,GAAG,CAAC,IAAI,EAAE;MAC/BsD,IAAI,CAAC/C,IAAI,CAAC8C,GAAG,CAAC;MACdtE,GAAG,CAACyB,GAAG,CAACR,GAAG,EAAEsD,IAAI,CAAC;IACtB,CAAC,CAAC;EACN;EACA,OAAOvE,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMwE,uBAAuB,GAAG,iBAAiB;AACjD,MAAMC,8BAA8B,GAAG;EACnC,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE;AACV,CAAC;AACD,SAASf,gBAAgBA,CAACgB,CAAC,EAAE;EACzB,OAAOC,kBAAkB,CAACD,CAAC,CAAC,CAACP,OAAO,CAACK,uBAAuB,EAAE,CAACI,CAAC,EAAEC,CAAC,KAAKJ,8BAA8B,CAACI,CAAC,CAAC,IAAID,CAAC,CAAC;AACnH;AACA,SAASE,aAAaA,CAAC3D,KAAK,EAAE;EAC1B,OAAQ,GAAEA,KAAM,EAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4D,UAAU,CAAC;EACb3E,WAAWA,CAAC4E,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,IAAI3B,oBAAoB,CAAC,CAAC;IAC5D,IAAI,CAAC,CAACwB,OAAO,CAACI,UAAU,EAAE;MACtB,IAAI,CAAC,CAACJ,OAAO,CAACK,UAAU,EAAE;QACtB,MAAM,IAAI9B,KAAK,CAAE,gDAA+C,CAAC;MACrE;MACA,IAAI,CAACvD,GAAG,GAAG+D,WAAW,CAACiB,OAAO,CAACI,UAAU,EAAE,IAAI,CAACD,OAAO,CAAC;IAC5D,CAAC,MACI,IAAI,CAAC,CAACH,OAAO,CAACK,UAAU,EAAE;MAC3B,IAAI,CAACrF,GAAG,GAAG,IAAIO,GAAG,CAAC,CAAC;MACpBqB,MAAM,CAACO,IAAI,CAAC6C,OAAO,CAACK,UAAU,CAAC,CAAC1E,OAAO,CAACM,GAAG,IAAI;QAC3C,MAAME,KAAK,GAAG6D,OAAO,CAACK,UAAU,CAACpE,GAAG,CAAC;QACrC;QACA,MAAMa,MAAM,GAAGM,KAAK,CAACkB,OAAO,CAACnC,KAAK,CAAC,GAAGA,KAAK,CAACnB,GAAG,CAAC8E,aAAa,CAAC,GAAG,CAACA,aAAa,CAAC3D,KAAK,CAAC,CAAC;QACvF,IAAI,CAACnB,GAAG,CAACyB,GAAG,CAACR,GAAG,EAAEa,MAAM,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC9B,GAAG,GAAG,IAAI;IACnB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsB,GAAGA,CAAC8C,KAAK,EAAE;IACP,IAAI,CAAClC,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAClC,GAAG,CAACsB,GAAG,CAAC8C,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI7C,GAAGA,CAAC6C,KAAK,EAAE;IACP,IAAI,CAAClC,IAAI,CAAC,CAAC;IACX,MAAMoD,GAAG,GAAG,IAAI,CAACtF,GAAG,CAACuB,GAAG,CAAC6C,KAAK,CAAC;IAC/B,OAAO,CAAC,CAACkB,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIhD,MAAMA,CAAC8B,KAAK,EAAE;IACV,IAAI,CAAClC,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAClC,GAAG,CAACuB,GAAG,CAAC6C,KAAK,CAAC,IAAI,IAAI;EACtC;EACA;AACJ;AACA;AACA;EACIjC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,IAAI,CAAC,CAAC;IACX,OAAOE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACrC,GAAG,CAACmC,IAAI,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,MAAMA,CAAC6B,KAAK,EAAEjD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACqB,KAAK,CAAC;MAAE4B,KAAK;MAAEjD,KAAK;MAAEsB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACI8C,SAASA,CAACrB,MAAM,EAAE;IACd,MAAMe,OAAO,GAAG,EAAE;IAClBrD,MAAM,CAACO,IAAI,CAAC+B,MAAM,CAAC,CAACvD,OAAO,CAACyD,KAAK,IAAI;MACjC,MAAMjD,KAAK,GAAG+C,MAAM,CAACE,KAAK,CAAC;MAC3B,IAAIhC,KAAK,CAACkB,OAAO,CAACnC,KAAK,CAAC,EAAE;QACtBA,KAAK,CAACR,OAAO,CAAC6E,MAAM,IAAI;UACpBP,OAAO,CAACzD,IAAI,CAAC;YAAE4C,KAAK;YAAEjD,KAAK,EAAEqE,MAAM;YAAE/C,EAAE,EAAE;UAAI,CAAC,CAAC;QACnD,CAAC,CAAC;MACN,CAAC,MACI;QACDwC,OAAO,CAACzD,IAAI,CAAC;UAAE4C,KAAK;UAAEjD,KAAK,EAAEA,KAAK;UAAEsB,EAAE,EAAE;QAAI,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACD,KAAK,CAACyC,OAAO,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIxD,GAAGA,CAAC2C,KAAK,EAAEjD,KAAK,EAAE;IACd,OAAO,IAAI,CAACqB,KAAK,CAAC;MAAE4B,KAAK;MAAEjD,KAAK;MAAEsB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAC0B,KAAK,EAAEjD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACqB,KAAK,CAAC;MAAE4B,KAAK;MAAEjD,KAAK;MAAEsB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIT,QAAQA,CAAA,EAAG;IACP,IAAI,CAACE,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACC,IAAI,CAAC,CAAC,CACbnC,GAAG,CAACiB,GAAG,IAAI;MACZ,MAAMwE,IAAI,GAAG,IAAI,CAACN,OAAO,CAAC1B,SAAS,CAACxC,GAAG,CAAC;MACxC;MACA;MACA;MACA,OAAO,IAAI,CAACjB,GAAG,CAACuB,GAAG,CAACN,GAAG,CAAC,CAACjB,GAAG,CAACmB,KAAK,IAAIsE,IAAI,GAAG,GAAG,GAAG,IAAI,CAACN,OAAO,CAACxB,WAAW,CAACxC,KAAK,CAAC,CAAC,CAC9EuE,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IACG;IACA;IAAA,CACC3F,MAAM,CAACqE,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC,CAC7BsB,IAAI,CAAC,GAAG,CAAC;EAClB;EACAlD,KAAKA,CAACK,MAAM,EAAE;IACV,MAAML,KAAK,GAAG,IAAIuC,UAAU,CAAC;MAAEI,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IACvD3C,KAAK,CAAC0C,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI;IACxC1C,KAAK,CAACyC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,IAAI,EAAE,EAAEjC,MAAM,CAACH,MAAM,CAAC;IACnD,OAAOL,KAAK;EAChB;EACAN,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAClC,GAAG,KAAK,IAAI,EAAE;MACnB,IAAI,CAACA,GAAG,GAAG,IAAIO,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAAC2E,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACA,SAAS,CAAChD,IAAI,CAAC,CAAC;MACrB,IAAI,CAACgD,SAAS,CAAC/C,IAAI,CAAC,CAAC,CAACxB,OAAO,CAACM,GAAG,IAAI,IAAI,CAACjB,GAAG,CAACyB,GAAG,CAACR,GAAG,EAAE,IAAI,CAACiE,SAAS,CAAClF,GAAG,CAACuB,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC;MACpF,IAAI,CAACgE,OAAO,CAACtE,OAAO,CAACkC,MAAM,IAAI;QAC3B,QAAQA,MAAM,CAACJ,EAAE;UACb,KAAK,GAAG;UACR,KAAK,GAAG;YACJ,MAAMQ,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAE,KAAK,GAAG,GAAG,IAAI,CAACzC,GAAG,CAACuB,GAAG,CAACsB,MAAM,CAACuB,KAAK,CAAC,GAAGlB,SAAS,KAAK,EAAE;YAC/ED,IAAI,CAACzB,IAAI,CAACsD,aAAa,CAACjC,MAAM,CAAC1B,KAAK,CAAC,CAAC;YACtC,IAAI,CAACnB,GAAG,CAACyB,GAAG,CAACoB,MAAM,CAACuB,KAAK,EAAEnB,IAAI,CAAC;YAChC;UACJ,KAAK,GAAG;YACJ,IAAIJ,MAAM,CAAC1B,KAAK,KAAK+B,SAAS,EAAE;cAC5B,IAAID,IAAI,GAAG,IAAI,CAACjD,GAAG,CAACuB,GAAG,CAACsB,MAAM,CAACuB,KAAK,CAAC,IAAI,EAAE;cAC3C,MAAMuB,GAAG,GAAG1C,IAAI,CAACnC,OAAO,CAACgE,aAAa,CAACjC,MAAM,CAAC1B,KAAK,CAAC,CAAC;cACrD,IAAIwE,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ1C,IAAI,CAAC2C,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;cACvB;cACA,IAAI1C,IAAI,CAAChB,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,CAACjC,GAAG,CAACyB,GAAG,CAACoB,MAAM,CAACuB,KAAK,EAAEnB,IAAI,CAAC;cACpC,CAAC,MACI;gBACD,IAAI,CAACjD,GAAG,CAAC0C,MAAM,CAACG,MAAM,CAACuB,KAAK,CAAC;cACjC;YACJ,CAAC,MACI;cACD,IAAI,CAACpE,GAAG,CAAC0C,MAAM,CAACG,MAAM,CAACuB,KAAK,CAAC;cAC7B;YACJ;QACR;MACJ,CAAC,CAAC;MACF,IAAI,CAACc,SAAS,GAAG,IAAI,CAACD,OAAO,GAAG,IAAI;IACxC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,CAAC;EACnBzF,WAAWA,CAAC0F,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd3F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,GAAG,GAAG,IAAIO,GAAG,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,GAAGA,CAACuE,KAAK,EAAE7E,KAAK,EAAE;IACd,IAAI,CAACnB,GAAG,CAACyB,GAAG,CAACuE,KAAK,EAAE7E,KAAK,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,GAAGA,CAACyE,KAAK,EAAE;IACP,IAAI,CAAC,IAAI,CAAChG,GAAG,CAACsB,GAAG,CAAC0E,KAAK,CAAC,EAAE;MACtB,IAAI,CAAChG,GAAG,CAACyB,GAAG,CAACuE,KAAK,EAAEA,KAAK,CAACF,YAAY,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO,IAAI,CAAC9F,GAAG,CAACuB,GAAG,CAACyE,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACItD,MAAMA,CAACsD,KAAK,EAAE;IACV,IAAI,CAAChG,GAAG,CAAC0C,MAAM,CAACsD,KAAK,CAAC;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1E,GAAGA,CAAC0E,KAAK,EAAE;IACP,OAAO,IAAI,CAAChG,GAAG,CAACsB,GAAG,CAAC0E,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;EACI7D,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACnC,GAAG,CAACmC,IAAI,CAAC,CAAC;EAC1B;AACJ;;AAEA;AACA;AACA;AACA,SAAS8D,aAAaA,CAACC,MAAM,EAAE;EAC3B,QAAQA,MAAM;IACV,KAAK,QAAQ;IACb,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,SAAS;IACd,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAO,IAAI;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAChF,KAAK,EAAE;EAC1B,OAAO,OAAOiF,WAAW,KAAK,WAAW,IAAIjF,KAAK,YAAYiF,WAAW;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAAClF,KAAK,EAAE;EACnB,OAAO,OAAOmF,IAAI,KAAK,WAAW,IAAInF,KAAK,YAAYmF,IAAI;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACpF,KAAK,EAAE;EACvB,OAAO,OAAOqF,QAAQ,KAAK,WAAW,IAAIrF,KAAK,YAAYqF,QAAQ;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACtF,KAAK,EAAE;EAC9B,OAAO,OAAOuF,eAAe,KAAK,WAAW,IAAIvF,KAAK,YAAYuF,eAAe;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdvG,WAAWA,CAAC8F,MAAM,EAAEU,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACG,IAAI,GAAG,IAAI;IAChB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,MAAM;IAC1B,IAAI,CAAChB,MAAM,GAAGA,MAAM,CAACiB,WAAW,CAAC,CAAC;IAClC;IACA;IACA,IAAInC,OAAO;IACX;IACA;IACA,IAAIiB,aAAa,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,CAACY,MAAM,EAAE;MACxC;MACA,IAAI,CAACC,IAAI,GAAIF,KAAK,KAAK3D,SAAS,GAAI2D,KAAK,GAAG,IAAI;MAChD7B,OAAO,GAAG8B,MAAM;IACpB,CAAC,MACI;MACD;MACA9B,OAAO,GAAG6B,KAAK;IACnB;IACA;IACA,IAAI7B,OAAO,EAAE;MACT;MACA,IAAI,CAACgC,cAAc,GAAG,CAAC,CAAChC,OAAO,CAACgC,cAAc;MAC9C,IAAI,CAACC,eAAe,GAAG,CAAC,CAACjC,OAAO,CAACiC,eAAe;MAChD;MACA,IAAI,CAAC,CAACjC,OAAO,CAACkC,YAAY,EAAE;QACxB,IAAI,CAACA,YAAY,GAAGlC,OAAO,CAACkC,YAAY;MAC5C;MACA;MACA,IAAI,CAAC,CAAClC,OAAO,CAAC3E,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAG2E,OAAO,CAAC3E,OAAO;MAClC;MACA,IAAI,CAAC,CAAC2E,OAAO,CAACoC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGpC,OAAO,CAACoC,OAAO;MAClC;MACA,IAAI,CAAC,CAACpC,OAAO,CAACd,MAAM,EAAE;QAClB,IAAI,CAACA,MAAM,GAAGc,OAAO,CAACd,MAAM;MAChC;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAAC7D,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIF,WAAW,CAAC,CAAC;IACpC;IACA;IACA,IAAI,CAAC,IAAI,CAACiH,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIrB,WAAW,CAAC,CAAC;IACpC;IACA;IACA,IAAI,CAAC,IAAI,CAAC7B,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAIa,UAAU,CAAC,CAAC;MAC9B,IAAI,CAACsC,aAAa,GAAGT,GAAG;IAC5B,CAAC,MACI;MACD;MACA,MAAM1C,MAAM,GAAG,IAAI,CAACA,MAAM,CAAClC,QAAQ,CAAC,CAAC;MACrC,IAAIkC,MAAM,CAACjC,MAAM,KAAK,CAAC,EAAE;QACrB;QACA,IAAI,CAACoF,aAAa,GAAGT,GAAG;MAC5B,CAAC,MACI;QACD;QACA,MAAMU,IAAI,GAAGV,GAAG,CAAC9F,OAAO,CAAC,GAAG,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMyG,GAAG,GAAGD,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,GAAIA,IAAI,GAAGV,GAAG,CAAC3E,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG;QAClE,IAAI,CAACoF,aAAa,GAAGT,GAAG,GAAGW,GAAG,GAAGrD,MAAM;MAC3C;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIsD,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACT,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAIZ,aAAa,CAAC,IAAI,CAACY,IAAI,CAAC,IAAIV,MAAM,CAAC,IAAI,CAACU,IAAI,CAAC,IAAIR,UAAU,CAAC,IAAI,CAACQ,IAAI,CAAC,IACtEN,iBAAiB,CAAC,IAAI,CAACM,IAAI,CAAC,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;MAC/D,OAAO,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI,IAAI,CAACA,IAAI,YAAYhC,UAAU,EAAE;MACjC,OAAO,IAAI,CAACgC,IAAI,CAAC/E,QAAQ,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,OAAO,IAAI,CAAC+E,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,IAC/D3E,KAAK,CAACkB,OAAO,CAAC,IAAI,CAACyD,IAAI,CAAC,EAAE;MAC1B,OAAOU,IAAI,CAACC,SAAS,CAAC,IAAI,CAACX,IAAI,CAAC;IACpC;IACA;IACA,OAAO,IAAI,CAACA,IAAI,CAAC/E,QAAQ,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2F,uBAAuBA,CAAA,EAAG;IACtB;IACA,IAAI,IAAI,CAACZ,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA,IAAIR,UAAU,CAAC,IAAI,CAACQ,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAIV,MAAM,CAAC,IAAI,CAACU,IAAI,CAAC,EAAE;MACnB,OAAO,IAAI,CAACA,IAAI,CAACa,IAAI,IAAI,IAAI;IACjC;IACA;IACA,IAAIzB,aAAa,CAAC,IAAI,CAACY,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;MAC/B,OAAO,YAAY;IACvB;IACA;IACA,IAAI,IAAI,CAACA,IAAI,YAAYhC,UAAU,EAAE;MACjC,OAAO,iDAAiD;IAC5D;IACA;IACA,IAAI,OAAO,IAAI,CAACgC,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC9D,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,EAAE;MAChC,OAAO,kBAAkB;IAC7B;IACA;IACA,OAAO,IAAI;EACf;EACAvE,KAAKA,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,MAAMqD,MAAM,GAAGrD,MAAM,CAACqD,MAAM,IAAI,IAAI,CAACA,MAAM;IAC3C,MAAMU,GAAG,GAAG/D,MAAM,CAAC+D,GAAG,IAAI,IAAI,CAACA,GAAG;IAClC,MAAMM,YAAY,GAAGrE,MAAM,CAACqE,YAAY,IAAI,IAAI,CAACA,YAAY;IAC7D;IACA;IACA;IACA;IACA,MAAMH,IAAI,GAAIlE,MAAM,CAACkE,IAAI,KAAK7D,SAAS,GAAIL,MAAM,CAACkE,IAAI,GAAG,IAAI,CAACA,IAAI;IAClE;IACA;IACA,MAAME,eAAe,GAAIpE,MAAM,CAACoE,eAAe,KAAK/D,SAAS,GAAIL,MAAM,CAACoE,eAAe,GAAG,IAAI,CAACA,eAAe;IAC9G,MAAMD,cAAc,GAAInE,MAAM,CAACmE,cAAc,KAAK9D,SAAS,GAAIL,MAAM,CAACmE,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1G;IACA;IACA,IAAI3G,OAAO,GAAGwC,MAAM,CAACxC,OAAO,IAAI,IAAI,CAACA,OAAO;IAC5C,IAAI6D,MAAM,GAAGrB,MAAM,CAACqB,MAAM,IAAI,IAAI,CAACA,MAAM;IACzC;IACA,MAAMkD,OAAO,GAAGvE,MAAM,CAACuE,OAAO,IAAI,IAAI,CAACA,OAAO;IAC9C;IACA,IAAIvE,MAAM,CAACgF,UAAU,KAAK3E,SAAS,EAAE;MACjC;MACA7C,OAAO,GACHuB,MAAM,CAACO,IAAI,CAACU,MAAM,CAACgF,UAAU,CAAC,CACzBC,MAAM,CAAC,CAACzH,OAAO,EAAEU,IAAI,KAAKV,OAAO,CAACoB,GAAG,CAACV,IAAI,EAAE8B,MAAM,CAACgF,UAAU,CAAC9G,IAAI,CAAC,CAAC,EAAEV,OAAO,CAAC;IAC3F;IACA;IACA,IAAIwC,MAAM,CAACkF,SAAS,EAAE;MAClB;MACA7D,MAAM,GAAGtC,MAAM,CAACO,IAAI,CAACU,MAAM,CAACkF,SAAS,CAAC,CACjCD,MAAM,CAAC,CAAC5D,MAAM,EAAEE,KAAK,KAAKF,MAAM,CAACzC,GAAG,CAAC2C,KAAK,EAAEvB,MAAM,CAACkF,SAAS,CAAC3D,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;IACtF;IACA;IACA,OAAO,IAAIyC,WAAW,CAACT,MAAM,EAAEU,GAAG,EAAEG,IAAI,EAAE;MACtC7C,MAAM;MACN7D,OAAO;MACP+G,OAAO;MACPJ,cAAc;MACdE,YAAY;MACZD;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIe,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjD;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACzE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzD;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACrD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;AACA;EACI7H,WAAWA,CAAC8B,IAAI,EAAEgG,aAAa,GAAG,GAAG,CAAC,yBAAyBC,iBAAiB,GAAG,IAAI,EAAE;IACrF;IACA;IACA,IAAI,CAAC9H,OAAO,GAAG6B,IAAI,CAAC7B,OAAO,IAAI,IAAIF,WAAW,CAAC,CAAC;IAChD,IAAI,CAACiI,MAAM,GAAGlG,IAAI,CAACkG,MAAM,KAAKlF,SAAS,GAAGhB,IAAI,CAACkG,MAAM,GAAGF,aAAa;IACrE,IAAI,CAACG,UAAU,GAAGnG,IAAI,CAACmG,UAAU,IAAIF,iBAAiB;IACtD,IAAI,CAACvB,GAAG,GAAG1E,IAAI,CAAC0E,GAAG,IAAI,IAAI;IAC3B;IACA,IAAI,CAAC0B,EAAE,GAAG,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG;EACrD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,kBAAkB,SAASN,gBAAgB,CAAC;EAC9C;AACJ;AACA;EACI7H,WAAWA,CAAC8B,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAAC0F,IAAI,GAAGI,aAAa,CAACQ,cAAc;EAC5C;EACA;AACJ;AACA;AACA;EACIhG,KAAKA,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,OAAO,IAAI0F,kBAAkB,CAAC;MAC1BlI,OAAO,EAAEwC,MAAM,CAACxC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvC+H,MAAM,EAAEvF,MAAM,CAACuF,MAAM,KAAKlF,SAAS,GAAGL,MAAM,CAACuF,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAExF,MAAM,CAACwF,UAAU,IAAI,IAAI,CAACA,UAAU;MAChDzB,GAAG,EAAE/D,MAAM,CAAC+D,GAAG,IAAI,IAAI,CAACA,GAAG,IAAI1D;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuF,YAAY,SAASR,gBAAgB,CAAC;EACxC;AACJ;AACA;EACI7H,WAAWA,CAAC8B,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAAC0F,IAAI,GAAGI,aAAa,CAACU,QAAQ;IAClC,IAAI,CAAC3B,IAAI,GAAG7E,IAAI,CAAC6E,IAAI,KAAK7D,SAAS,GAAGhB,IAAI,CAAC6E,IAAI,GAAG,IAAI;EAC1D;EACAvE,KAAKA,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;IACf,OAAO,IAAI4F,YAAY,CAAC;MACpB1B,IAAI,EAAGlE,MAAM,CAACkE,IAAI,KAAK7D,SAAS,GAAIL,MAAM,CAACkE,IAAI,GAAG,IAAI,CAACA,IAAI;MAC3D1G,OAAO,EAAEwC,MAAM,CAACxC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvC+H,MAAM,EAAGvF,MAAM,CAACuF,MAAM,KAAKlF,SAAS,GAAIL,MAAM,CAACuF,MAAM,GAAG,IAAI,CAACA,MAAM;MACnEC,UAAU,EAAExF,MAAM,CAACwF,UAAU,IAAI,IAAI,CAACA,UAAU;MAChDzB,GAAG,EAAE/D,MAAM,CAAC+D,GAAG,IAAI,IAAI,CAACA,GAAG,IAAI1D;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyF,iBAAiB,SAASV,gBAAgB,CAAC;EAC7C7H,WAAWA,CAAC8B,IAAI,EAAE;IACd;IACA,KAAK,CAACA,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC;IAC/B,IAAI,CAACnB,IAAI,GAAG,mBAAmB;IAC/B;AACR;AACA;IACQ,IAAI,CAACuH,EAAE,GAAG,KAAK;IACf;IACA;IACA;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG,EAAE;MACzC,IAAI,CAACQ,OAAO,GAAI,mCAAkC1G,IAAI,CAAC0E,GAAG,IAAI,eAAgB,EAAC;IACnF,CAAC,MACI;MACD,IAAI,CAACgC,OAAO,GAAI,6BAA4B1G,IAAI,CAAC0E,GAAG,IAAI,eAAgB,KAAI1E,IAAI,CAACkG,MAAO,IAAGlG,IAAI,CAACmG,UAAW,EAAC;IAChH;IACA,IAAI,CAACQ,KAAK,GAAG3G,IAAI,CAAC2G,KAAK,IAAI,IAAI;EACnC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAAC9D,OAAO,EAAE+B,IAAI,EAAE;EAC5B,OAAO;IACHA,IAAI;IACJ1G,OAAO,EAAE2E,OAAO,CAAC3E,OAAO;IACxB+G,OAAO,EAAEpC,OAAO,CAACoC,OAAO;IACxB2B,OAAO,EAAE/D,OAAO,CAAC+D,OAAO;IACxB7E,MAAM,EAAEc,OAAO,CAACd,MAAM;IACtB8C,cAAc,EAAEhC,OAAO,CAACgC,cAAc;IACtCE,YAAY,EAAElC,OAAO,CAACkC,YAAY;IAClCD,eAAe,EAAEjC,OAAO,CAACiC;EAC7B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+B,UAAU,CAAC;EACb5I,WAAWA,CAAC6I,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,KAAK,EAAEvC,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAIoE,GAAG;IACP;IACA,IAAID,KAAK,YAAYxC,WAAW,EAAE;MAC9B;MACA;MACAyC,GAAG,GAAGD,KAAK;IACf,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,IAAI9I,OAAO,GAAG6C,SAAS;MACvB,IAAI8B,OAAO,CAAC3E,OAAO,YAAYF,WAAW,EAAE;QACxCE,OAAO,GAAG2E,OAAO,CAAC3E,OAAO;MAC7B,CAAC,MACI;QACDA,OAAO,GAAG,IAAIF,WAAW,CAAC6E,OAAO,CAAC3E,OAAO,CAAC;MAC9C;MACA;MACA,IAAI6D,MAAM,GAAGhB,SAAS;MACtB,IAAI,CAAC,CAAC8B,OAAO,CAACd,MAAM,EAAE;QAClB,IAAIc,OAAO,CAACd,MAAM,YAAYa,UAAU,EAAE;UACtCb,MAAM,GAAGc,OAAO,CAACd,MAAM;QAC3B,CAAC,MACI;UACDA,MAAM,GAAG,IAAIa,UAAU,CAAC;YAAEM,UAAU,EAAEL,OAAO,CAACd;UAAO,CAAC,CAAC;QAC3D;MACJ;MACA;MACAkF,GAAG,GAAG,IAAIzC,WAAW,CAACwC,KAAK,EAAEvC,GAAG,EAAG5B,OAAO,CAAC+B,IAAI,KAAK7D,SAAS,GAAG8B,OAAO,CAAC+B,IAAI,GAAG,IAAI,EAAG;QAClF1G,OAAO;QACP+G,OAAO,EAAEpC,OAAO,CAACoC,OAAO;QACxBlD,MAAM;QACN8C,cAAc,EAAEhC,OAAO,CAACgC,cAAc;QACtC;QACAE,YAAY,EAAElC,OAAO,CAACkC,YAAY,IAAI,MAAM;QAC5CD,eAAe,EAAEjC,OAAO,CAACiC;MAC7B,CAAC,CAAC;IACN;IACA;IACA;IACA;IACA;IACA,MAAMoC,OAAO,GAAGzJ,EAAE,CAACwJ,GAAG,CAAC,CAACE,IAAI,CAACxJ,SAAS,CAAEsJ,GAAG,IAAK,IAAI,CAACH,OAAO,CAACM,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC;IAC1E;IACA;IACA;IACA,IAAID,KAAK,YAAYxC,WAAW,IAAI3B,OAAO,CAAC+D,OAAO,KAAK,QAAQ,EAAE;MAC9D,OAAOM,OAAO;IAClB;IACA;IACA;IACA;IACA,MAAMG,IAAI,GAAGH,OAAO,CAACC,IAAI,CAACvJ,MAAM,CAAE0J,KAAK,IAAKA,KAAK,YAAYhB,YAAY,CAAC,CAAC;IAC3E;IACA,QAAQzD,OAAO,CAAC+D,OAAO,IAAI,MAAM;MAC7B,KAAK,MAAM;QACP;QACA;QACA;QACA;QACA;QACA,QAAQK,GAAG,CAAClC,YAAY;UACpB,KAAK,aAAa;YACd,OAAOsC,IAAI,CAACF,IAAI,CAACtJ,GAAG,CAAEsF,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACyB,IAAI,KAAK,IAAI,IAAI,EAAEzB,GAAG,CAACyB,IAAI,YAAYX,WAAW,CAAC,EAAE;gBACzD,MAAM,IAAI7C,KAAK,CAAC,iCAAiC,CAAC;cACtD;cACA,OAAO+B,GAAG,CAACyB,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAOyC,IAAI,CAACF,IAAI,CAACtJ,GAAG,CAAEsF,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACyB,IAAI,KAAK,IAAI,IAAI,EAAEzB,GAAG,CAACyB,IAAI,YAAYT,IAAI,CAAC,EAAE;gBAClD,MAAM,IAAI/C,KAAK,CAAC,yBAAyB,CAAC;cAC9C;cACA,OAAO+B,GAAG,CAACyB,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAOyC,IAAI,CAACF,IAAI,CAACtJ,GAAG,CAAEsF,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACyB,IAAI,KAAK,IAAI,IAAI,OAAOzB,GAAG,CAACyB,IAAI,KAAK,QAAQ,EAAE;gBACnD,MAAM,IAAIxD,KAAK,CAAC,2BAA2B,CAAC;cAChD;cACA,OAAO+B,GAAG,CAACyB,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;UACX;YACI;YACA,OAAOyC,IAAI,CAACF,IAAI,CAACtJ,GAAG,CAAEsF,GAAG,IAAKA,GAAG,CAACyB,IAAI,CAAC,CAAC;QAChD;MACJ,KAAK,UAAU;QACX;QACA,OAAOyC,IAAI;MACf;QACI;QACA,MAAM,IAAIjG,KAAK,CAAE,uCAAsCyB,OAAO,CAAC+D,OAAQ,GAAE,CAAC;IAClF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrG,MAAMA,CAACkE,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI,CAACkE,OAAO,CAAC,QAAQ,EAAEtC,GAAG,EAAE5B,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;EACIzD,GAAGA,CAACqF,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO,IAAI,CAACkE,OAAO,CAAC,KAAK,EAAEtC,GAAG,EAAE5B,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0E,IAAIA,CAAC9C,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACpB,OAAO,IAAI,CAACkE,OAAO,CAAC,MAAM,EAAEtC,GAAG,EAAE5B,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2E,KAAKA,CAAC/C,GAAG,EAAEgD,aAAa,EAAE;IACtB,OAAO,IAAI,CAACV,OAAO,CAAC,OAAO,EAAEtC,GAAG,EAAE;MAC9B1C,MAAM,EAAE,IAAIa,UAAU,CAAC,CAAC,CAACxC,MAAM,CAACqH,aAAa,EAAE,gBAAgB,CAAC;MAChEb,OAAO,EAAE,MAAM;MACf7B,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlC,OAAOA,CAAC4B,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACkE,OAAO,CAAC,SAAS,EAAEtC,GAAG,EAAE5B,OAAO,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACI6E,KAAKA,CAACjD,GAAG,EAAEG,IAAI,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACkE,OAAO,CAAC,OAAO,EAAEtC,GAAG,EAAEkC,OAAO,CAAC9D,OAAO,EAAE+B,IAAI,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+C,IAAIA,CAAClD,GAAG,EAAEG,IAAI,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAI,CAACkE,OAAO,CAAC,MAAM,EAAEtC,GAAG,EAAEkC,OAAO,CAAC9D,OAAO,EAAE+B,IAAI,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgD,GAAGA,CAACnD,GAAG,EAAEG,IAAI,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO,IAAI,CAACkE,OAAO,CAAC,KAAK,EAAEtC,GAAG,EAAEkC,OAAO,CAAC9D,OAAO,EAAE+B,IAAI,CAAC,CAAC;EAC3D;AACJ;AACAiC,UAAU,CAACgB,IAAI,YAAAC,mBAAApF,CAAA;EAAA,YAAAA,CAAA,IAAyFmE,UAAU,EAApB5J,EAAE,CAAA8K,QAAA,CAAoCjK,WAAW;AAAA,CAA6C;AAC5L+I,UAAU,CAACmB,KAAK,kBAD8E/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EACYgD,UAAU;EAAAqB,OAAA,EAAVrB,UAAU,CAAAgB;AAAA,EAAG;AACzH;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAF8FtC,EAAE,CAAAkL,iBAAA,CAEJtB,UAAU,EAAc,CAAC;IACzGpB,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAE3H;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;AAE3E,SAASsK,qBAAqBA,CAACnB,GAAG,EAAEoB,cAAc,EAAE;EAChD,OAAOA,cAAc,CAACpB,GAAG,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASqB,6BAA6BA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC7D,OAAO,CAACC,cAAc,EAAEJ,cAAc,KAAKG,WAAW,CAACE,SAAS,CAACD,cAAc,EAAE;IAC7ErB,MAAM,EAAGuB,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc;EAChF,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAACL,WAAW,EAAEM,aAAa,EAAEC,QAAQ,EAAE;EAChE;EACA,OAAO,CAACL,cAAc,EAAEJ,cAAc,KAAKS,QAAQ,CAACC,YAAY,CAAC,MAAMF,aAAa,CAACJ,cAAc,EAAEE,iBAAiB,IAAIJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc,CAAC,CAAC,CAAC;EAC1K;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,iBAAiB,GAAG,IAAI7L,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA;AACA;AACA,MAAM8L,oBAAoB,GAAG,IAAI9L,cAAc,CAAC,sBAAsB,CAAC;AACvE;AACA;AACA;AACA;AACA,SAAS+L,0BAA0BA,CAAA,EAAG;EAClC,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,CAAClC,GAAG,EAAEH,OAAO,KAAK;IACrB,IAAIqC,KAAK,KAAK,IAAI,EAAE;MAChB,MAAMC,YAAY,GAAGhM,MAAM,CAAC4L,iBAAiB,EAAE;QAAEK,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI,EAAE;MACxE;MACA;MACA;MACA;MACAF,KAAK,GAAGC,YAAY,CAACE,WAAW,CAAChB,6BAA6B,EAAEF,qBAAqB,CAAC;IAC1F;IACA,OAAOe,KAAK,CAAClC,GAAG,EAAEH,OAAO,CAAC;EAC9B,CAAC;AACL;AACA,MAAMyC,sBAAsB,SAASzL,WAAW,CAAC;EAC7CG,WAAWA,CAACuL,OAAO,EAAEV,QAAQ,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACU,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACK,KAAK,GAAG,IAAI;EACrB;EACA/B,MAAMA,CAACqB,cAAc,EAAE;IACnB,IAAI,IAAI,CAACU,KAAK,KAAK,IAAI,EAAE;MACrB,MAAMM,qBAAqB,GAAGxJ,KAAK,CAACC,IAAI,CAAC,IAAIwJ,GAAG,CAAC,IAAI,CAACZ,QAAQ,CAAC1J,GAAG,CAAC6J,oBAAoB,CAAC,CAAC,CAAC;MAC1F;MACA;MACA;MACA;MACA,IAAI,CAACE,KAAK,GAAGM,qBAAqB,CAACH,WAAW,CAAC,CAACK,eAAe,EAAEd,aAAa,KAAKD,oBAAoB,CAACe,eAAe,EAAEd,aAAa,EAAE,IAAI,CAACC,QAAQ,CAAC,EAAEV,qBAAqB,CAAC;IAClL;IACA,OAAO,IAAI,CAACe,KAAK,CAACV,cAAc,EAAEE,iBAAiB,IAAI,IAAI,CAACa,OAAO,CAACpC,MAAM,CAACuB,iBAAiB,CAAC,CAAC;EAClG;AACJ;AACAY,sBAAsB,CAAC1B,IAAI,YAAA+B,+BAAAlH,CAAA;EAAA,YAAAA,CAAA,IAAyF6G,sBAAsB,EA3E5CtM,EAAE,CAAA8K,QAAA,CA2E4DhK,WAAW,GA3EzEd,EAAE,CAAA8K,QAAA,CA2EoF9K,EAAE,CAAC4M,mBAAmB;AAAA,CAA6C;AACvPN,sBAAsB,CAACvB,KAAK,kBA5EkE/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EA4EwB0F,sBAAsB;EAAArB,OAAA,EAAtBqB,sBAAsB,CAAA1B;AAAA,EAAG;AACjJ;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KA7E8FtC,EAAE,CAAAkL,iBAAA,CA6EJoB,sBAAsB,EAAc,CAAC;IACrH9D,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAE1H;IAAY,CAAC,EAAE;MAAE0H,IAAI,EAAExI,EAAE,CAAC4M;IAAoB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE7G;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB;AACA;AACA,MAAMC,qBAAqB,GAAG,gDAAgD;AAC9E;AACA;AACA,MAAMC,sBAAsB,GAAG,+CAA+C;AAC9E,MAAMC,6BAA6B,GAAG,6CAA6C;AACnF;AACA;AACA,MAAMC,+BAA+B,GAAG,wCAAwC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOA,MAAM;EACjB;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBtM,WAAWA,CAACuM,WAAW,EAAEC,QAAQ,EAAE;IAC/B,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,OAAQ,qBAAoBf,aAAa,EAAG,EAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1C,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAAClD,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAI3C,KAAK,CAAC6I,sBAAsB,CAAC;IAC3C,CAAC,MACI,IAAIhD,GAAG,CAAClC,YAAY,KAAK,MAAM,EAAE;MAClC,MAAM,IAAI3D,KAAK,CAAC8I,6BAA6B,CAAC;IAClD;IACA;IACA;IACA,IAAIjD,GAAG,CAAC/I,OAAO,CAAC8B,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAIsB,KAAK,CAAC+I,+BAA+B,CAAC;IACpD;IACA;IACA,OAAO,IAAIzM,UAAU,CAAEoN,QAAQ,IAAK;MAChC;MACA;MACA;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC;MACpC,MAAMpG,GAAG,GAAGwC,GAAG,CAAC/B,aAAa,CAAClD,OAAO,CAAC,sBAAsB,EAAG,IAAG+I,QAAS,IAAG,CAAC;MAC/E;MACA,MAAMC,IAAI,GAAG,IAAI,CAACP,QAAQ,CAACQ,aAAa,CAAC,QAAQ,CAAC;MAClDD,IAAI,CAACE,GAAG,GAAGzG,GAAG;MACd;MACA;MACA;MACA,IAAIG,IAAI,GAAG,IAAI;MACf;MACA,IAAIuG,QAAQ,GAAG,KAAK;MACpB;MACA;MACA;MACA,IAAI,CAACX,WAAW,CAACO,QAAQ,CAAC,GAAIK,IAAI,IAAK;QACnC;QACA,OAAO,IAAI,CAACZ,WAAW,CAACO,QAAQ,CAAC;QACjC;QACAnG,IAAI,GAAGwG,IAAI;QACXD,QAAQ,GAAG,IAAI;MACnB,CAAC;MACD;MACA;MACA;MACA,MAAME,OAAO,GAAGA,CAAA,KAAM;QAClB;QACA,IAAIL,IAAI,CAACM,UAAU,EAAE;UACjBN,IAAI,CAACM,UAAU,CAACC,WAAW,CAACP,IAAI,CAAC;QACrC;QACA;QACA;QACA,OAAO,IAAI,CAACR,WAAW,CAACO,QAAQ,CAAC;MACrC,CAAC;MACD;MACA;MACA;MACA;MACA,MAAMS,MAAM,GAAIlE,KAAK,IAAK;QACtB;QACA;QACA;QACA,IAAI,CAACoD,eAAe,CAACe,IAAI,CAAC,MAAM;UAC5B;UACAJ,OAAO,CAAC,CAAC;UACT;UACA,IAAI,CAACF,QAAQ,EAAE;YACX;YACA;YACAL,QAAQ,CAACpE,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjC/B,GAAG;cACHwB,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,aAAa;cACzBQ,KAAK,EAAE,IAAItF,KAAK,CAAC4I,qBAAqB;YAC1C,CAAC,CAAC,CAAC;YACH;UACJ;UACA;UACA;UACAc,QAAQ,CAACY,IAAI,CAAC,IAAIpF,YAAY,CAAC;YAC3B1B,IAAI;YACJqB,MAAM,EAAE,GAAG,CAAC;YACZC,UAAU,EAAE,IAAI;YAChBzB;UACJ,CAAC,CAAC,CAAC;UACH;UACAqG,QAAQ,CAACa,QAAQ,CAAC,CAAC;QACvB,CAAC,CAAC;MACN,CAAC;MACD;MACA;MACA;MACA,MAAMC,OAAO,GAAIlF,KAAK,IAAK;QACvB2E,OAAO,CAAC,CAAC;QACT;QACAP,QAAQ,CAACpE,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLT,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,aAAa;UACzBzB;QACJ,CAAC,CAAC,CAAC;MACP,CAAC;MACD;MACA;MACAuG,IAAI,CAACa,gBAAgB,CAAC,MAAM,EAAEL,MAAM,CAAC;MACrCR,IAAI,CAACa,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;MACvC,IAAI,CAACnB,QAAQ,CAAC7F,IAAI,CAACkH,WAAW,CAACd,IAAI,CAAC;MACpC;MACAF,QAAQ,CAACY,IAAI,CAAC;QAAEjG,IAAI,EAAEI,aAAa,CAACkG;MAAK,CAAC,CAAC;MAC3C;MACA,OAAO,MAAM;QACT,IAAI,CAACZ,QAAQ,EAAE;UACX,IAAI,CAACa,eAAe,CAAChB,IAAI,CAAC;QAC9B;QACA;QACAK,OAAO,CAAC,CAAC;MACb,CAAC;IACL,CAAC,CAAC;EACN;EACAW,eAAeA,CAACC,MAAM,EAAE;IACpB;IACA;IACA;IACA,IAAI,CAAClC,eAAe,EAAE;MAClBA,eAAe,GAAG,IAAI,CAACU,QAAQ,CAACyB,cAAc,CAACC,kBAAkB,CAAC,CAAC;IACvE;IACApC,eAAe,CAACqC,SAAS,CAACH,MAAM,CAAC;EACrC;AACJ;AACA1B,kBAAkB,CAAC1C,IAAI,YAAAwE,2BAAA3J,CAAA;EAAA,YAAAA,CAAA,IAAyF6H,kBAAkB,EAxRpCtN,EAAE,CAAA8K,QAAA,CAwRoDqC,oBAAoB,GAxR1EnN,EAAE,CAAA8K,QAAA,CAwRqFlL,QAAQ;AAAA,CAA6C;AAC1O0N,kBAAkB,CAACvC,KAAK,kBAzRsE/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EAyRoB0G,kBAAkB;EAAArC,OAAA,EAAlBqC,kBAAkB,CAAA1C;AAAA,EAAG;AACzI;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KA1R8FtC,EAAE,CAAAkL,iBAAA,CA0RJoC,kBAAkB,EAAc,CAAC;IACjH9E,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAE2E;IAAqB,CAAC,EAAE;MAAE3E,IAAI,EAAE1E,SAAS;MAAEuL,UAAU,EAAE,CAAC;QAC9F7G,IAAI,EAAEpI,MAAM;QACZkP,IAAI,EAAE,CAAC1P,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,SAAS2P,kBAAkBA,CAACvF,GAAG,EAAEyE,IAAI,EAAE;EACnC,IAAIzE,GAAG,CAAClD,MAAM,KAAK,OAAO,EAAE;IACxB,OAAO3G,MAAM,CAACmN,kBAAkB,CAAC,CAACnD,MAAM,CAACH,GAAG,CAAC;EACjD;EACA;EACA,OAAOyE,IAAI,CAACzE,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwF,gBAAgB,CAAC;EACnBxO,WAAWA,CAAC6K,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,SAASA,CAACD,cAAc,EAAEiD,IAAI,EAAE;IAC5B,OAAO,IAAI,CAAC5C,QAAQ,CAACC,YAAY,CAAC,MAAMyD,kBAAkB,CAAC/D,cAAc,EAAEE,iBAAiB,IAAI+C,IAAI,CAACtE,MAAM,CAACuB,iBAAiB,CAAC,CAAC,CAAC;EACpI;AACJ;AACA8D,gBAAgB,CAAC5E,IAAI,YAAA6E,yBAAAhK,CAAA;EAAA,YAAAA,CAAA,IAAyF+J,gBAAgB,EAjUhCxP,EAAE,CAAA8K,QAAA,CAiUgD9K,EAAE,CAAC4M,mBAAmB;AAAA,CAA6C;AACnN4C,gBAAgB,CAACzE,KAAK,kBAlUwE/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EAkUkB4I,gBAAgB;EAAAvE,OAAA,EAAhBuE,gBAAgB,CAAA5E;AAAA,EAAG;AACrI;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAnU8FtC,EAAE,CAAAkL,iBAAA,CAmUJsE,gBAAgB,EAAc,CAAC;IAC/GhH,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAExI,EAAE,CAAC4M;IAAoB,CAAC,CAAC;EAAE,CAAC;AAAA;AAEtF,MAAM8C,WAAW,GAAG,cAAc;AAClC;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,GAAG,EAAE;EACzB,IAAI,aAAa,IAAIA,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE;IACzC,OAAOD,GAAG,CAACC,WAAW;EAC1B;EACA,IAAI,kBAAkB,CAACC,IAAI,CAACF,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACtD,OAAOH,GAAG,CAACI,iBAAiB,CAAC,eAAe,CAAC;EACjD;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBjP,WAAWA,CAACkP,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI/F,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAAClD,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAI3C,KAAK,CAAE,+EAA8E,CAAC;IACpG;IACA;IACA,OAAO,IAAI1D,UAAU,CAAEoN,QAAQ,IAAK;MAChC;MACA,MAAM+B,GAAG,GAAG,IAAI,CAACM,UAAU,CAACC,KAAK,CAAC,CAAC;MACnCP,GAAG,CAACQ,IAAI,CAACpG,GAAG,CAAClD,MAAM,EAAEkD,GAAG,CAAC/B,aAAa,CAAC;MACvC,IAAI,CAAC,CAAC+B,GAAG,CAACnC,eAAe,EAAE;QACvB+H,GAAG,CAAC/H,eAAe,GAAG,IAAI;MAC9B;MACA;MACAmC,GAAG,CAAC/I,OAAO,CAACM,OAAO,CAAC,CAACI,IAAI,EAAEe,MAAM,KAAKkN,GAAG,CAACS,gBAAgB,CAAC1O,IAAI,EAAEe,MAAM,CAAC4D,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MACnF;MACA,IAAI,CAAC0D,GAAG,CAAC/I,OAAO,CAACiB,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC5B0N,GAAG,CAACS,gBAAgB,CAAC,QAAQ,EAAE,mCAAmC,CAAC;MACvE;MACA;MACA,IAAI,CAACrG,GAAG,CAAC/I,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC,EAAE;QAClC,MAAMoO,YAAY,GAAGtG,GAAG,CAACzB,uBAAuB,CAAC,CAAC;QAClD;QACA,IAAI+H,YAAY,KAAK,IAAI,EAAE;UACvBV,GAAG,CAACS,gBAAgB,CAAC,cAAc,EAAEC,YAAY,CAAC;QACtD;MACJ;MACA;MACA,IAAItG,GAAG,CAAClC,YAAY,EAAE;QAClB,MAAMA,YAAY,GAAGkC,GAAG,CAAClC,YAAY,CAAChG,WAAW,CAAC,CAAC;QACnD;QACA;QACA;QACA;QACA;QACA8N,GAAG,CAAC9H,YAAY,GAAKA,YAAY,KAAK,MAAM,GAAIA,YAAY,GAAG,MAAO;MAC1E;MACA;MACA,MAAMyI,OAAO,GAAGvG,GAAG,CAAC5B,aAAa,CAAC,CAAC;MACnC;MACA;MACA;MACA;MACA;MACA;MACA,IAAIoI,cAAc,GAAG,IAAI;MACzB;MACA;MACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;QACzB,IAAID,cAAc,KAAK,IAAI,EAAE;UACzB,OAAOA,cAAc;QACzB;QACA,MAAMvH,UAAU,GAAG2G,GAAG,CAAC3G,UAAU,IAAI,IAAI;QACzC;QACA,MAAMhI,OAAO,GAAG,IAAIF,WAAW,CAAC6O,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC;QAC5D;QACA;QACA,MAAMvI,GAAG,GAAGmI,cAAc,CAACC,GAAG,CAAC,IAAI5F,GAAG,CAACxC,GAAG;QAC1C;QACAgJ,cAAc,GAAG,IAAIrH,kBAAkB,CAAC;UAAElI,OAAO;UAAE+H,MAAM,EAAE4G,GAAG,CAAC5G,MAAM;UAAEC,UAAU;UAAEzB;QAAI,CAAC,CAAC;QACzF,OAAOgJ,cAAc;MACzB,CAAC;MACD;MACA;MACA;MACA,MAAMjC,MAAM,GAAGA,CAAA,KAAM;QACjB;QACA,IAAI;UAAEtN,OAAO;UAAE+H,MAAM;UAAEC,UAAU;UAAEzB;QAAI,CAAC,GAAGiJ,cAAc,CAAC,CAAC;QAC3D;QACA,IAAI9I,IAAI,GAAG,IAAI;QACf,IAAIqB,MAAM,KAAK,GAAG,CAAC,gCAAgC;UAC/C;UACArB,IAAI,GAAI,OAAOiI,GAAG,CAACc,QAAQ,KAAK,WAAW,GAAId,GAAG,CAACe,YAAY,GAAGf,GAAG,CAACc,QAAQ;QAClF;QACA;QACA,IAAI1H,MAAM,KAAK,CAAC,EAAE;UACdA,MAAM,GAAG,CAAC,CAACrB,IAAI,GAAG,GAAG,CAAC,0BAA0B,CAAC;QACrD;QACA;QACA;QACA;QACA;QACA,IAAIuB,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;QACtC;QACA;QACA,IAAIgB,GAAG,CAAClC,YAAY,KAAK,MAAM,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;UACzD;UACA,MAAMiJ,YAAY,GAAGjJ,IAAI;UACzBA,IAAI,GAAGA,IAAI,CAAC5C,OAAO,CAAC2K,WAAW,EAAE,EAAE,CAAC;UACpC,IAAI;YACA;YACA/H,IAAI,GAAGA,IAAI,KAAK,EAAE,GAAGU,IAAI,CAACwI,KAAK,CAAClJ,IAAI,CAAC,GAAG,IAAI;UAChD,CAAC,CACD,OAAO8B,KAAK,EAAE;YACV;YACA;YACA;YACA9B,IAAI,GAAGiJ,YAAY;YACnB;YACA;YACA,IAAI1H,EAAE,EAAE;cACJ;cACAA,EAAE,GAAG,KAAK;cACV;cACAvB,IAAI,GAAG;gBAAE8B,KAAK;gBAAEqH,IAAI,EAAEnJ;cAAK,CAAC;YAChC;UACJ;QACJ;QACA,IAAIuB,EAAE,EAAE;UACJ;UACA2E,QAAQ,CAACY,IAAI,CAAC,IAAIpF,YAAY,CAAC;YAC3B1B,IAAI;YACJ1G,OAAO;YACP+H,MAAM;YACNC,UAAU;YACVzB,GAAG,EAAEA,GAAG,IAAI1D;UAChB,CAAC,CAAC,CAAC;UACH;UACA;UACA+J,QAAQ,CAACa,QAAQ,CAAC,CAAC;QACvB,CAAC,MACI;UACD;UACAb,QAAQ,CAACpE,KAAK,CAAC,IAAIF,iBAAiB,CAAC;YACjC;YACAE,KAAK,EAAE9B,IAAI;YACX1G,OAAO;YACP+H,MAAM;YACNC,UAAU;YACVzB,GAAG,EAAEA,GAAG,IAAI1D;UAChB,CAAC,CAAC,CAAC;QACP;MACJ,CAAC;MACD;MACA;MACA;MACA,MAAM6K,OAAO,GAAIlF,KAAK,IAAK;QACvB,MAAM;UAAEjC;QAAI,CAAC,GAAGiJ,cAAc,CAAC,CAAC;QAChC,MAAMvK,GAAG,GAAG,IAAIqD,iBAAiB,CAAC;UAC9BE,KAAK;UACLT,MAAM,EAAE4G,GAAG,CAAC5G,MAAM,IAAI,CAAC;UACvBC,UAAU,EAAE2G,GAAG,CAAC3G,UAAU,IAAI,eAAe;UAC7CzB,GAAG,EAAEA,GAAG,IAAI1D;QAChB,CAAC,CAAC;QACF+J,QAAQ,CAACpE,KAAK,CAACvD,GAAG,CAAC;MACvB,CAAC;MACD;MACA;MACA;MACA;MACA,IAAI6K,WAAW,GAAG,KAAK;MACvB;MACA;MACA,MAAMC,cAAc,GAAI3G,KAAK,IAAK;QAC9B;QACA,IAAI,CAAC0G,WAAW,EAAE;UACdlD,QAAQ,CAACY,IAAI,CAACgC,cAAc,CAAC,CAAC,CAAC;UAC/BM,WAAW,GAAG,IAAI;QACtB;QACA;QACA;QACA,IAAIE,aAAa,GAAG;UAChBzI,IAAI,EAAEI,aAAa,CAACsI,gBAAgB;UACpCC,MAAM,EAAE9G,KAAK,CAAC8G;QAClB,CAAC;QACD;QACA,IAAI9G,KAAK,CAAC+G,gBAAgB,EAAE;UACxBH,aAAa,CAACI,KAAK,GAAGhH,KAAK,CAACgH,KAAK;QACrC;QACA;QACA;QACA;QACA,IAAIrH,GAAG,CAAClC,YAAY,KAAK,MAAM,IAAI,CAAC,CAAC8H,GAAG,CAACe,YAAY,EAAE;UACnDM,aAAa,CAACK,WAAW,GAAG1B,GAAG,CAACe,YAAY;QAChD;QACA;QACA9C,QAAQ,CAACY,IAAI,CAACwC,aAAa,CAAC;MAChC,CAAC;MACD;MACA;MACA,MAAMM,YAAY,GAAIlH,KAAK,IAAK;QAC5B;QACA;QACA,IAAImH,QAAQ,GAAG;UACXhJ,IAAI,EAAEI,aAAa,CAAC6I,cAAc;UAClCN,MAAM,EAAE9G,KAAK,CAAC8G;QAClB,CAAC;QACD;QACA;QACA,IAAI9G,KAAK,CAAC+G,gBAAgB,EAAE;UACxBI,QAAQ,CAACH,KAAK,GAAGhH,KAAK,CAACgH,KAAK;QAChC;QACA;QACAxD,QAAQ,CAACY,IAAI,CAAC+C,QAAQ,CAAC;MAC3B,CAAC;MACD;MACA5B,GAAG,CAAChB,gBAAgB,CAAC,MAAM,EAAEL,MAAM,CAAC;MACpCqB,GAAG,CAAChB,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;MACtCiB,GAAG,CAAChB,gBAAgB,CAAC,SAAS,EAAED,OAAO,CAAC;MACxCiB,GAAG,CAAChB,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;MACtC;MACA,IAAI3E,GAAG,CAACpC,cAAc,EAAE;QACpB;QACAgI,GAAG,CAAChB,gBAAgB,CAAC,UAAU,EAAEoC,cAAc,CAAC;QAChD;QACA,IAAIT,OAAO,KAAK,IAAI,IAAIX,GAAG,CAAC8B,MAAM,EAAE;UAChC9B,GAAG,CAAC8B,MAAM,CAAC9C,gBAAgB,CAAC,UAAU,EAAE2C,YAAY,CAAC;QACzD;MACJ;MACA;MACA3B,GAAG,CAAC+B,IAAI,CAACpB,OAAO,CAAC;MACjB1C,QAAQ,CAACY,IAAI,CAAC;QAAEjG,IAAI,EAAEI,aAAa,CAACkG;MAAK,CAAC,CAAC;MAC3C;MACA;MACA,OAAO,MAAM;QACT;QACAc,GAAG,CAACgC,mBAAmB,CAAC,OAAO,EAAEjD,OAAO,CAAC;QACzCiB,GAAG,CAACgC,mBAAmB,CAAC,OAAO,EAAEjD,OAAO,CAAC;QACzCiB,GAAG,CAACgC,mBAAmB,CAAC,MAAM,EAAErD,MAAM,CAAC;QACvCqB,GAAG,CAACgC,mBAAmB,CAAC,SAAS,EAAEjD,OAAO,CAAC;QAC3C,IAAI3E,GAAG,CAACpC,cAAc,EAAE;UACpBgI,GAAG,CAACgC,mBAAmB,CAAC,UAAU,EAAEZ,cAAc,CAAC;UACnD,IAAIT,OAAO,KAAK,IAAI,IAAIX,GAAG,CAAC8B,MAAM,EAAE;YAChC9B,GAAG,CAAC8B,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAEL,YAAY,CAAC;UAC5D;QACJ;QACA;QACA,IAAI3B,GAAG,CAACiC,UAAU,KAAKjC,GAAG,CAACkC,IAAI,EAAE;UAC7BlC,GAAG,CAACmC,KAAK,CAAC,CAAC;QACf;MACJ,CAAC;IACL,CAAC,CAAC;EACN;AACJ;AACA9B,cAAc,CAACrF,IAAI,YAAAoH,uBAAAvM,CAAA;EAAA,YAAAA,CAAA,IAAyFwK,cAAc,EAhlB5BjQ,EAAE,CAAA8K,QAAA,CAglB4CnL,EAAE,CAACG,UAAU;AAAA,CAA6C;AACtMmQ,cAAc,CAAClF,KAAK,kBAjlB0E/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EAilBgBqJ,cAAc;EAAAhF,OAAA,EAAdgF,cAAc,CAAArF;AAAA,EAAG;AACjI;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAllB8FtC,EAAE,CAAAkL,iBAAA,CAklBJ+E,cAAc,EAAc,CAAC;IAC7GzH,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAE7I,EAAE,CAACG;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAE7E,MAAMmS,YAAY,GAAG,IAAI/R,cAAc,CAAC,cAAc,CAAC;AACvD,MAAMgS,wBAAwB,GAAG,YAAY;AAC7C,MAAMC,gBAAgB,GAAG,IAAIjS,cAAc,CAAC,kBAAkB,EAAE;EAC5DkS,UAAU,EAAE,MAAM;EAClBnH,OAAO,EAAEA,CAAA,KAAMiH;AACnB,CAAC,CAAC;AACF,MAAMG,wBAAwB,GAAG,cAAc;AAC/C,MAAMC,gBAAgB,GAAG,IAAIpS,cAAc,CAAC,kBAAkB,EAAE;EAC5DkS,UAAU,EAAE,MAAM;EAClBnH,OAAO,EAAEA,CAAA,KAAMoH;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAME,sBAAsB,CAAC;AAE7B;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BxR,WAAWA,CAACyR,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IACnC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CAAC;EACvB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,QAAQ,KAAK,QAAQ,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,MAAMM,YAAY,GAAG,IAAI,CAACP,GAAG,CAACQ,MAAM,IAAI,EAAE;IAC1C,IAAID,YAAY,KAAK,IAAI,CAACJ,gBAAgB,EAAE;MACxC,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACD,SAAS,GAAGhT,iBAAiB,CAACmT,YAAY,EAAE,IAAI,CAACL,UAAU,CAAC;MACjE,IAAI,CAACC,gBAAgB,GAAGI,YAAY;IACxC;IACA,OAAO,IAAI,CAACH,SAAS;EACzB;AACJ;AACAL,uBAAuB,CAAC5H,IAAI,YAAAsI,gCAAAzN,CAAA;EAAA,YAAAA,CAAA,IAAyF+M,uBAAuB,EApoB9CxS,EAAE,CAAA8K,QAAA,CAooB8DlL,QAAQ,GApoBxEI,EAAE,CAAA8K,QAAA,CAooBmFzK,WAAW,GApoBhGL,EAAE,CAAA8K,QAAA,CAooB2GqH,gBAAgB;AAAA,CAA6C;AACxQK,uBAAuB,CAACzH,KAAK,kBAroBiE/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EAqoByB4L,uBAAuB;EAAAvH,OAAA,EAAvBuH,uBAAuB,CAAA5H;AAAA,EAAG;AACnJ;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAtoB8FtC,EAAE,CAAAkL,iBAAA,CAsoBJsH,uBAAuB,EAAc,CAAC;IACtHhK,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAE1E,SAAS;MAAEuL,UAAU,EAAE,CAAC;QAC9D7G,IAAI,EAAEpI,MAAM;QACZkP,IAAI,EAAE,CAAC1P,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE4I,IAAI,EAAE1E,SAAS;MAAEuL,UAAU,EAAE,CAAC;QAClC7G,IAAI,EAAEpI,MAAM;QACZkP,IAAI,EAAE,CAACjP,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEmI,IAAI,EAAE1E,SAAS;MAAEuL,UAAU,EAAE,CAAC;QAClC7G,IAAI,EAAEpI,MAAM;QACZkP,IAAI,EAAE,CAAC6C,gBAAgB;MAC3B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,SAASgB,iBAAiBA,CAACnJ,GAAG,EAAEyE,IAAI,EAAE;EAClC,MAAM2E,KAAK,GAAGpJ,GAAG,CAACxC,GAAG,CAAC1F,WAAW,CAAC,CAAC;EACnC;EACA;EACA;EACA;EACA,IAAI,CAAC3B,MAAM,CAAC8R,YAAY,CAAC,IAAIjI,GAAG,CAAClD,MAAM,KAAK,KAAK,IAAIkD,GAAG,CAAClD,MAAM,KAAK,MAAM,IACtEsM,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,KAAK,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC7D,OAAO5E,IAAI,CAACzE,GAAG,CAAC;EACpB;EACA,MAAMpD,KAAK,GAAGzG,MAAM,CAACoS,sBAAsB,CAAC,CAACQ,QAAQ,CAAC,CAAC;EACvD,MAAMO,UAAU,GAAGnT,MAAM,CAACmS,gBAAgB,CAAC;EAC3C;EACA,IAAI1L,KAAK,IAAI,IAAI,IAAI,CAACoD,GAAG,CAAC/I,OAAO,CAACiB,GAAG,CAACoR,UAAU,CAAC,EAAE;IAC/CtJ,GAAG,GAAGA,GAAG,CAAC5G,KAAK,CAAC;MAAEnC,OAAO,EAAE+I,GAAG,CAAC/I,OAAO,CAACoB,GAAG,CAACiR,UAAU,EAAE1M,KAAK;IAAE,CAAC,CAAC;EACpE;EACA,OAAO6H,IAAI,CAACzE,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMuJ,mBAAmB,CAAC;EACtBvS,WAAWA,CAAC6K,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAJ,SAASA,CAACD,cAAc,EAAEiD,IAAI,EAAE;IAC5B,OAAO,IAAI,CAAC5C,QAAQ,CAACC,YAAY,CAAC,MAAMqH,iBAAiB,CAAC3H,cAAc,EAAEE,iBAAiB,IAAI+C,IAAI,CAACtE,MAAM,CAACuB,iBAAiB,CAAC,CAAC,CAAC;EACnI;AACJ;AACA6H,mBAAmB,CAAC3I,IAAI,YAAA4I,4BAAA/N,CAAA;EAAA,YAAAA,CAAA,IAAyF8N,mBAAmB,EA/qBtCvT,EAAE,CAAA8K,QAAA,CA+qBsD9K,EAAE,CAAC4M,mBAAmB;AAAA,CAA6C;AACzN2G,mBAAmB,CAACxI,KAAK,kBAhrBqE/K,EAAE,CAAAgL,kBAAA;EAAApE,KAAA,EAgrBqB2M,mBAAmB;EAAAtI,OAAA,EAAnBsI,mBAAmB,CAAA3I;AAAA,EAAG;AAC3I;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAjrB8FtC,EAAE,CAAAkL,iBAAA,CAirBJqI,mBAAmB,EAAc,CAAC;IAClH/K,IAAI,EAAEvI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuI,IAAI,EAAExI,EAAE,CAAC4M;IAAoB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEtF;AACA;AACA;AACA;AACA;AACA,IAAI6G,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EACjFA,eAAe,CAACA,eAAe,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB;EAC3FA,eAAe,CAACA,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EAC7EA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB;AAC3F,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,SAASC,eAAeA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACtC,OAAO;IACHC,KAAK,EAAEF,IAAI;IACXG,UAAU,EAAEF;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAAC,GAAGC,QAAQ,EAAE;EACpC,IAAI1R,SAAS,EAAE;IACX,MAAM2R,YAAY,GAAG,IAAIxH,GAAG,CAACuH,QAAQ,CAACpT,GAAG,CAACsT,CAAC,IAAIA,CAAC,CAACL,KAAK,CAAC,CAAC;IACxD,IAAII,YAAY,CAAC/R,GAAG,CAACuR,eAAe,CAACU,gBAAgB,CAAC,IAClDF,YAAY,CAAC/R,GAAG,CAACuR,eAAe,CAACW,uBAAuB,CAAC,EAAE;MAC3D,MAAM,IAAIjQ,KAAK,CAAC7B,SAAS,GACpB,uJAAsJ,GACvJ,EAAE,CAAC;IACX;EACJ;EACA,MAAMsR,SAAS,GAAG,CACdhK,UAAU,EACVqG,cAAc,EACd3D,sBAAsB,EACtB;IAAE+H,OAAO,EAAExT,WAAW;IAAEyT,WAAW,EAAEhI;EAAuB,CAAC,EAC7D;IAAE+H,OAAO,EAAEvT,WAAW;IAAEwT,WAAW,EAAErE;EAAe,CAAC,EACrD;IACIoE,OAAO,EAAErI,oBAAoB;IAC7BuI,QAAQ,EAAEpB,iBAAiB;IAC3BqB,KAAK,EAAE;EACX,CAAC,EACD;IAAEH,OAAO,EAAEpC,YAAY;IAAEsC,QAAQ,EAAE;EAAK,CAAC,EACzC;IAAEF,OAAO,EAAE9B,sBAAsB;IAAEkC,QAAQ,EAAEjC;EAAwB,CAAC,CACzE;EACD,KAAK,MAAMkC,OAAO,IAAIV,QAAQ,EAAE;IAC5BJ,SAAS,CAACxR,IAAI,CAAC,GAAGsS,OAAO,CAACZ,UAAU,CAAC;EACzC;EACA,OAAOxT,wBAAwB,CAACsT,SAAS,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,gBAAgBA,CAACC,cAAc,EAAE;EACtC,OAAOlB,eAAe,CAACD,eAAe,CAACoB,YAAY,EAAED,cAAc,CAAChU,GAAG,CAACgL,aAAa,IAAI;IACrF,OAAO;MACHyI,OAAO,EAAErI,oBAAoB;MAC7BuI,QAAQ,EAAE3I,aAAa;MACvB4I,KAAK,EAAE;IACX,CAAC;EACL,CAAC,CAAC,CAAC;AACP;AACA,MAAMM,qBAAqB,GAAG,IAAI5U,cAAc,CAAC,uBAAuB,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6U,sBAAsBA,CAAA,EAAG;EAC9B;EACA;EACA;EACA;EACA;EACA,OAAOrB,eAAe,CAACD,eAAe,CAACuB,kBAAkB,EAAE,CACvD;IACIX,OAAO,EAAES,qBAAqB;IAC9BG,UAAU,EAAEhJ;EAChB,CAAC,EACD;IACIoI,OAAO,EAAErI,oBAAoB;IAC7BsI,WAAW,EAAEQ,qBAAqB;IAClCN,KAAK,EAAE;EACX,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,qBAAqBA,CAAC;EAAEvC,UAAU;EAAEW;AAAW,CAAC,EAAE;EACvD,MAAMM,SAAS,GAAG,EAAE;EACpB,IAAIjB,UAAU,KAAK7O,SAAS,EAAE;IAC1B8P,SAAS,CAACxR,IAAI,CAAC;MAAEiS,OAAO,EAAElC,gBAAgB;MAAEoC,QAAQ,EAAE5B;IAAW,CAAC,CAAC;EACvE;EACA,IAAIW,UAAU,KAAKxP,SAAS,EAAE;IAC1B8P,SAAS,CAACxR,IAAI,CAAC;MAAEiS,OAAO,EAAE/B,gBAAgB;MAAEiC,QAAQ,EAAEjB;IAAW,CAAC,CAAC;EACvE;EACA,OAAOI,eAAe,CAACD,eAAe,CAACW,uBAAuB,EAAER,SAAS,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,oBAAoBA,CAAA,EAAG;EAC5B,OAAOzB,eAAe,CAACD,eAAe,CAACU,gBAAgB,EAAE,CACrD;IACIE,OAAO,EAAEpC,YAAY;IACrBsC,QAAQ,EAAE;EACd,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,gBAAgBA,CAAA,EAAG;EACxB,OAAO1B,eAAe,CAACD,eAAe,CAAC4B,YAAY,EAAE,CACjD/H,kBAAkB,EAClB;IAAE+G,OAAO,EAAElH,oBAAoB;IAAE8H,UAAU,EAAE7H;EAAqB,CAAC,EACnE;IAAEiH,OAAO,EAAErI,oBAAoB;IAAEuI,QAAQ,EAAEhF,kBAAkB;IAAEiF,KAAK,EAAE;EAAK,CAAC,CAC/E,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,yBAAyBA,CAAA,EAAG;EACjC,OAAO5B,eAAe,CAACD,eAAe,CAAC8B,qBAAqB,EAAE,CAC1D;IACIlB,OAAO,EAAEvT,WAAW;IACpBmU,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMO,iBAAiB,GAAGrV,MAAM,CAACU,WAAW,EAAE;QAAE4U,QAAQ,EAAE,IAAI;QAAErJ,QAAQ,EAAE;MAAK,CAAC,CAAC;MACjF,IAAI9J,SAAS,IAAIkT,iBAAiB,KAAK,IAAI,EAAE;QACzC,MAAM,IAAIrR,KAAK,CAAC,kGAAkG,CAAC;MACvH;MACA,OAAOqR,iBAAiB;IAC5B;EACJ,CAAC,CACJ,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,oBAAoB,CAAC;EACvB;AACJ;AACA;EACI,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,oBAAoB;MAC9B9B,SAAS,EAAE,CACPuB,oBAAoB,CAAC,CAAC,CAACrB,UAAU;IAEzC,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO+B,WAAWA,CAACjQ,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,OAAO;MACHgQ,QAAQ,EAAEF,oBAAoB;MAC9B9B,SAAS,EAAEsB,qBAAqB,CAACtP,OAAO,CAAC,CAACkO;IAC9C,CAAC;EACL;AACJ;AACA4B,oBAAoB,CAAC9K,IAAI,YAAAkL,6BAAArQ,CAAA;EAAA,YAAAA,CAAA,IAAyFiQ,oBAAoB;AAAA,CAAkD;AACxLA,oBAAoB,CAACK,IAAI,kBA35BqE/V,EAAE,CAAAgW,gBAAA;EAAAxN,IAAA,EA25BmBkN;AAAoB,EAAG;AAC1IA,oBAAoB,CAACO,IAAI,kBA55BqEjW,EAAE,CAAAkW,gBAAA;EAAAtC,SAAA,EA45BoD,CAC5IL,mBAAmB,EACnB;IAAEc,OAAO,EAAEtI,iBAAiB;IAAEuI,WAAW,EAAEf,mBAAmB;IAAEiB,KAAK,EAAE;EAAK,CAAC,EAC7E;IAAEH,OAAO,EAAE9B,sBAAsB;IAAEkC,QAAQ,EAAEjC;EAAwB,CAAC,EACtE0C,qBAAqB,CAAC;IAClBvC,UAAU,EAAET,wBAAwB;IACpCoB,UAAU,EAAEjB;EAChB,CAAC,CAAC,CAACyB,UAAU,EACb;IAAEO,OAAO,EAAEpC,YAAY;IAAEsC,QAAQ,EAAE;EAAK,CAAC;AAC5C,EAAG;AACR;EAAA,QAAAjS,SAAA,oBAAAA,SAAA,KAt6B8FtC,EAAE,CAAAkL,iBAAA,CAs6BJwK,oBAAoB,EAAc,CAAC;IACnHlN,IAAI,EAAEjI,QAAQ;IACd+O,IAAI,EAAE,CAAC;MACCsE,SAAS,EAAE,CACPL,mBAAmB,EACnB;QAAEc,OAAO,EAAEtI,iBAAiB;QAAEuI,WAAW,EAAEf,mBAAmB;QAAEiB,KAAK,EAAE;MAAK,CAAC,EAC7E;QAAEH,OAAO,EAAE9B,sBAAsB;QAAEkC,QAAQ,EAAEjC;MAAwB,CAAC,EACtE0C,qBAAqB,CAAC;QAClBvC,UAAU,EAAET,wBAAwB;QACpCoB,UAAU,EAAEjB;MAChB,CAAC,CAAC,CAACyB,UAAU,EACb;QAAEO,OAAO,EAAEpC,YAAY;QAAEsC,QAAQ,EAAE;MAAK,CAAC;IAEjD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,gBAAgB,CAAC;AAEvBA,gBAAgB,CAACvL,IAAI,YAAAwL,yBAAA3Q,CAAA;EAAA,YAAAA,CAAA,IAAyF0Q,gBAAgB;AAAA,CAAkD;AAChLA,gBAAgB,CAACJ,IAAI,kBAj8ByE/V,EAAE,CAAAgW,gBAAA;EAAAxN,IAAA,EAi8Be2N;AAAgB,EAAG;AAClIA,gBAAgB,CAACF,IAAI,kBAl8ByEjW,EAAE,CAAAkW,gBAAA;EAAAtC,SAAA,EAk8B4C,CACpIG,iBAAiB,CAACgB,sBAAsB,CAAC,CAAC,CAAC;AAC9C,EAAG;AACR;EAAA,QAAAzS,SAAA,oBAAAA,SAAA,KAr8B8FtC,EAAE,CAAAkL,iBAAA,CAq8BJiL,gBAAgB,EAAc,CAAC;IAC/G3N,IAAI,EAAEjI,QAAQ;IACd+O,IAAI,EAAE,CAAC;MACC;AACpB;AACA;AACA;MACoBsE,SAAS,EAAE,CACPG,iBAAiB,CAACgB,sBAAsB,CAAC,CAAC,CAAC;IAEnD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,qBAAqB,CAAC;AAE5BA,qBAAqB,CAACzL,IAAI,YAAA0L,8BAAA7Q,CAAA;EAAA,YAAAA,CAAA,IAAyF4Q,qBAAqB;AAAA,CAAkD;AAC1LA,qBAAqB,CAACN,IAAI,kBA59BoE/V,EAAE,CAAAgW,gBAAA;EAAAxN,IAAA,EA49BoB6N;AAAqB,EAAG;AAC5IA,qBAAqB,CAACJ,IAAI,kBA79BoEjW,EAAE,CAAAkW,gBAAA;EAAAtC,SAAA,EA69BsD,CAC9IwB,gBAAgB,CAAC,CAAC,CAACtB,UAAU;AAChC,EAAG;AACR;EAAA,QAAAxR,SAAA,oBAAAA,SAAA,KAh+B8FtC,EAAE,CAAAkL,iBAAA,CAg+BJmL,qBAAqB,EAAc,CAAC;IACpH7N,IAAI,EAAEjI,QAAQ;IACd+O,IAAI,EAAE,CAAC;MACCsE,SAAS,EAAE,CACPwB,gBAAgB,CAAC,CAAC,CAACtB,UAAU;IAErC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhU,UAAU,GAAGC,YAAY;;AAE/B;;AAEA;AACA;AACA;;AAEA,SAASgM,iBAAiB,EAAEjL,WAAW,EAAE8I,UAAU,EAAEyM,qBAAqB,EAAEF,gBAAgB,EAAET,oBAAoB,EAAE/O,WAAW,EAAEF,gBAAgB,EAAE8C,iBAAiB,EAAEX,aAAa,EAAE6K,eAAe,EAAE5S,WAAW,EAAEsI,kBAAkB,EAAEpI,WAAW,EAAE4E,UAAU,EAAE4B,WAAW,EAAE8B,YAAY,EAAER,gBAAgB,EAAEzE,oBAAoB,EAAE6L,cAAc,EAAEsC,sBAAsB,EAAEjF,kBAAkB,EAAEkC,gBAAgB,EAAE1P,UAAU,EAAEiU,iBAAiB,EAAEY,gBAAgB,EAAEI,sBAAsB,EAAEK,gBAAgB,EAAED,oBAAoB,EAAEG,yBAAyB,EAAEJ,qBAAqB,EAAE5I,sBAAsB,IAAIiK,wBAAwB,EAAEjK,sBAAsB,IAAIkK,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
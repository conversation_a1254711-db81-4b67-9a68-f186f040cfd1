{"ast": null, "code": "import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var enabledFeatures = Object.keys(features);\n  if (!features || enabledFeatures.length === 0) {\n    return;\n  }\n  var logString = \"Server started:\"; // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n\n  for (var i = 0; i < enabledFeatures.length; i++) {\n    var key = enabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  } // replace last comma with a period\n\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\nexport { log, logEnabledFeatures, setLogLevel };", "map": {"version": 3, "names": ["logger", "name", "defaultLevel", "setLogLevel", "level", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "<PERSON><PERSON><PERSON><PERSON>", "logEnabledFeatures", "features", "enabledFeatures", "Object", "keys", "length", "logString", "i", "key", "concat", "slice", "info"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/webpack-dev-server/client/utils/log.js"], "sourcesContent": ["import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\n\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\n\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var enabledFeatures = Object.keys(features);\n\n  if (!features || enabledFeatures.length === 0) {\n    return;\n  }\n\n  var logString = \"Server started:\"; // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n\n  for (var i = 0; i < enabledFeatures.length; i++) {\n    var key = enabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  } // replace last comma with a period\n\n\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\n\nexport { log, logEnabledFeatures, setLogLevel };"], "mappings": "AAAA,OAAOA,MAAM,MAAM,4BAA4B;AAC/C,IAAIC,IAAI,GAAG,oBAAoB,CAAC,CAAC;AACjC;;AAEA,IAAIC,YAAY,GAAG,MAAM,CAAC,CAAC;;AAE3B;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1BJ,MAAM,CAACK,sBAAsB,CAAC;IAC5BD,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AAEAD,WAAW,CAACD,YAAY,CAAC;AACzB,IAAII,GAAG,GAAGN,MAAM,CAACO,SAAS,CAACN,IAAI,CAAC;AAEhC,IAAIO,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;EAC7D,IAAIC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC;EAE3C,IAAI,CAACA,QAAQ,IAAIC,eAAe,CAACG,MAAM,KAAK,CAAC,EAAE;IAC7C;EACF;EAEA,IAAIC,SAAS,GAAG,iBAAiB,CAAC,CAAC;;EAEnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,eAAe,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC/C,IAAIC,GAAG,GAAGN,eAAe,CAACK,CAAC,CAAC;IAC5BD,SAAS,IAAI,GAAG,CAACG,MAAM,CAACD,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,CAACR,QAAQ,CAACO,GAAG,CAAC,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,CAAC;EACvF,CAAC,CAAC;;EAGFF,SAAS,GAAGA,SAAS,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACD,MAAM,CAAC,GAAG,CAAC;EAC9CX,GAAG,CAACa,IAAI,CAACL,SAAS,CAAC;AACrB,CAAC;AAED,SAASR,GAAG,EAAEE,kBAAkB,EAAEL,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"app/services/team-player.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ngx-translate/core\";\nconst _c0 = [\"table\"];\nfunction ModalAssignPlayersComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ModalAssignPlayersComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onAssign());\n    });\n    i0.ɵɵtext(1, \"Assign\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModalAssignPlayersComponent_tbody_34_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 21)(8, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ModalAssignPlayersComponent_tbody_34_tr_1_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const player_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.lookUp(player_r7));\n    });\n    i0.ɵɵelement(9, \"i\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const player_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(player_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(player_r7.club.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(player_r7.group.name);\n  }\n}\nfunction ModalAssignPlayersComponent_tbody_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\", 19);\n    i0.ɵɵtemplate(1, ModalAssignPlayersComponent_tbody_34_tr_1_Template, 10, 3, \"tr\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredPlayers);\n  }\n}\nfunction ModalAssignPlayersComponent_tbody_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\")(1, \"tr\")(2, \"td\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"No players found\"), \" \");\n  }\n}\nexport class ModalAssignPlayersComponent {\n  get anyPlayerNotAccepted() {\n    if (this.filteredPlayers) {\n      return this.players.some(p => p.isAccepted);\n    }\n  }\n  constructor(_modalService, _teamPlayerService) {\n    this._modalService = _modalService;\n    this._teamPlayerService = _teamPlayerService;\n    this.searchQuery = '';\n    this.players = [];\n    this.filteredPlayers = [];\n  }\n  ngOnInit() {\n    if (this.seasonId) {\n      this._teamPlayerService.getUnassignPlayersBySeasonId(parseInt(this.seasonId)).subscribe(data => {\n        this.players = data;\n        console.log(\"players\", this.players);\n        this.filteredPlayers = this.players;\n      });\n    }\n  }\n  filterPlayers() {\n    if (this.filteredPlayers) {\n      function findInValues(arr, value) {\n        value = value.toLowerCase(); // Convert search query to lowercase\n        return arr.filter(o => Object.entries(o).some(entry => String(entry[1]).toLowerCase().normalize(\"NFC\").includes(value) // Normalize and search\n        ));\n      }\n\n      this.filteredPlayers = findInValues(this.players, this.searchQuery);\n    } else {\n      this.filteredPlayers = [];\n    }\n  }\n  toggleAction(player, isAccepted) {\n    if (isAccepted) {\n      // Perform accept action\n      console.log('Accepted:', player.name);\n      player.isAccepted = true;\n    } else {\n      console.log('Denied:', player.name);\n      player.isAccepted = false;\n    }\n  }\n  lookUp(player) {\n    console.log(\"navigateTo\", player);\n    this._modalService.close(player);\n  }\n  onAssign() {\n    // submit all players isAccepted\n    const acceptedPlayers = this.players.filter(p => p.isAccepted);\n    if (acceptedPlayers.length > 0) {\n      console.log(acceptedPlayers);\n      this._modalService.close(acceptedPlayers);\n    } else {\n      alert('Please select at least one player');\n    }\n  }\n  onClose() {\n    this._modalService.close();\n  }\n  static #_ = this.ɵfac = function ModalAssignPlayersComponent_Factory(t) {\n    return new (t || ModalAssignPlayersComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.TeamPlayerService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalAssignPlayersComponent,\n    selectors: [[\"app-modal-assign-players\"]],\n    viewQuery: function ModalAssignPlayersComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n      }\n    },\n    inputs: {\n      seasonId: \"seasonId\"\n    },\n    decls: 36,\n    vars: 19,\n    consts: [[1, \"modal-header\"], [1, \"text-center\", \"w-100\"], [1, \"text-capitalize\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [\"type\", \"button\", \"class\", \"btn btn-primary float-right\", 3, \"click\", 4, \"ngIf\"], [1, \"input-group\", \"mb-3\"], [1, \"input-group-prepend\"], [\"id\", \"basic-addon1\", 1, \"input-group-text\"], [1, \"fa-light\", \"fa-magnifying-glass\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", \"aria-describedby\", \"basic-addon1\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-responsive\"], [1, \"table\", \"border\", \"row-border\", \"hover\"], [\"table\", \"\"], [1, \"text-capitalize\", \"text-center\"], [\"class\", \"text-center\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"float-right\", 3, \"click\"], [1, \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"colspan\", \"4\", 1, \"text-center\"]],\n    template: function ModalAssignPlayersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"div\", 1)(3, \"h2\")(4, \"span\", 2);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ModalAssignPlayersComponent_Template_button_click_7_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(8, \"span\", 4);\n        i0.ɵɵtext(9, \"\\u00D7 \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 5);\n        i0.ɵɵtemplate(11, ModalAssignPlayersComponent_button_11_Template, 2, 0, \"button\", 6);\n        i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8)(14, \"span\", 9);\n        i0.ɵɵelement(15, \"i\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"input\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function ModalAssignPlayersComponent_Template_input_ngModelChange_16_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"ngModelChange\", function ModalAssignPlayersComponent_Template_input_ngModelChange_16_listener() {\n          return ctx.filterPlayers();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 12)(18, \"table\", 13, 14)(20, \"thead\")(21, \"tr\")(22, \"th\", 15);\n        i0.ɵɵtext(23);\n        i0.ɵɵpipe(24, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"th\", 15);\n        i0.ɵɵtext(26);\n        i0.ɵɵpipe(27, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"th\", 15);\n        i0.ɵɵtext(29);\n        i0.ɵɵpipe(30, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"th\", 15);\n        i0.ɵɵtext(32);\n        i0.ɵɵpipe(33, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(34, ModalAssignPlayersComponent_tbody_34_Template, 2, 1, \"tbody\", 16);\n        i0.ɵɵtemplate(35, ModalAssignPlayersComponent_tbody_35_Template, 5, 3, \"tbody\", 17);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 9, \"Quick Assign\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.anyPlayerNotAccepted);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 11, \"Players\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 13, \"Club\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(30, 15, \"Group\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 17, \"Navigate\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.filteredPlayers.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.filteredPlayers.length == 0);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;IAsBQA,EAAA,CAAAC,cAAA,iBACyB;IAArBD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAACT,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAuB9BX,EAAA,CAAAC,cAAA,SAA4C;IACtCD,EAAA,CAAAU,MAAA,GAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC/BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChCX,EAAA,CAAAC,cAAA,aAA6D;IACbD,EAAA,CAAAE,UAAA,mBAAAU,2EAAA;MAAA,MAAAC,WAAA,GAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,MAAA,CAAAC,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IACpEf,EAAA,CAAAmB,SAAA,YAA4C;IAC9CnB,EAAA,CAAAW,YAAA,EAAS;;;;IANPX,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAAN,SAAA,CAAAO,IAAA,CAAiB;IACjBtB,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAqB,iBAAA,CAAAN,SAAA,CAAAQ,IAAA,CAAAD,IAAA,CAAsB;IACtBtB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAAN,SAAA,CAAAS,KAAA,CAAAF,IAAA,CAAuB;;;;;IAJ/BtB,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAAyB,UAAA,IAAAC,kDAAA,kBASK;IACP1B,EAAA,CAAAW,YAAA,EAAQ;;;;IAViBX,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,YAAAC,MAAA,CAAAC,eAAA,CAAkB;;;;;IAY3C7B,EAAA,CAAAC,cAAA,YAA2C;IAGrCD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;IADHX,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA+B,WAAA,gCACF;;;ACpDpB,OAAM,MAAOC,2BAA2B;EAOtC,IAAIC,oBAAoBA,CAAA;IACtB,IAAI,IAAI,CAACJ,eAAe,EAAE;MACxB,OAAO,IAAI,CAACK,OAAO,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC;;EAE/C;EAKAC,YACSC,aAA6B,EAC5BC,kBAAqC;IADtC,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAjB5B,KAAAC,WAAW,GAAW,EAAE;IAGxB,KAAAP,OAAO,GAAmB,EAAE;IAS5B,KAAAL,eAAe,GAAU,EAAE;EAQ3B;EAEAa,QAAQA,CAAA;IAEN,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjB,IAAI,CAACH,kBAAkB,CAACI,4BAA4B,CAACC,QAAQ,CAAC,IAAI,CAACF,QAAQ,CAAC,CAAC,CAACG,SAAS,CAAEC,IAAoB,IAAI;QAC/G,IAAI,CAACb,OAAO,GAAGa,IAAI;QACnBC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACf,OAAO,CAAC;QACpC,IAAI,CAACL,eAAe,GAAG,IAAI,CAACK,OAAO;MAErC,CAAC,CAAC;;EAEN;EAEAgB,aAAaA,CAAA;IACX,IAAI,IAAI,CAACrB,eAAe,EAAE;MACxB,SAASsB,YAAYA,CAACC,GAAG,EAAEC,KAAK;QAC9BA,KAAK,GAAGA,KAAK,CAACC,WAAW,EAAE,CAAC,CAAC;QAC7B,OAAOF,GAAG,CAACG,MAAM,CAACC,CAAC,IACjBC,MAAM,CAACC,OAAO,CAACF,CAAC,CAAC,CAACrB,IAAI,CAACwB,KAAK,IAC1BC,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,WAAW,EAAE,CAACO,SAAS,CAAC,KAAK,CAAC,CAACC,QAAQ,CAACT,KAAK,CAAC,CAAC;SACjE,CACF;MACH;;MAEA,IAAI,CAACxB,eAAe,GAAGsB,YAAY,CAAC,IAAI,CAACjB,OAAO,EAAE,IAAI,CAACO,WAAW,CAAC;KACpE,MAAM;MACL,IAAI,CAACZ,eAAe,GAAG,EAAE;;EAE7B;EAIAkC,YAAYA,CAACC,MAAW,EAAE3B,UAAmB;IAC3C,IAAIA,UAAU,EAAE;MACd;MACAW,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEe,MAAM,CAAC1C,IAAI,CAAC;MACrC0C,MAAM,CAAC3B,UAAU,GAAG,IAAI;KACzB,MAAM;MACLW,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEe,MAAM,CAAC1C,IAAI,CAAC;MACnC0C,MAAM,CAAC3B,UAAU,GAAG,KAAK;;EAE7B;EAEAnB,MAAMA,CAAC8C,MAAoB;IAEzBhB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEe,MAAM,CAAC;IAEjC,IAAI,CAACzB,aAAa,CAAC0B,KAAK,CAACD,MAAM,CAAC;EAElC;EAGAvD,QAAQA,CAAA;IACN;IACA,MAAMyD,eAAe,GAAG,IAAI,CAAChC,OAAO,CAACqB,MAAM,CAACnB,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC;IAE9D,IAAI6B,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9BnB,OAAO,CAACC,GAAG,CAACiB,eAAe,CAAC;MAC5B,IAAI,CAAC3B,aAAa,CAAC0B,KAAK,CAACC,eAAe,CAAC;KAC1C,MAAM;MACLE,KAAK,CAAC,mCAAmC,CAAC;;EAG9C;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC9B,aAAa,CAAC0B,KAAK,EAAE;EAC5B;EAAC,QAAAK,CAAA;qBAzFUtC,2BAA2B,EAAAhC,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzE,EAAA,CAAAuE,iBAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAA3B5C,2BAA2B;IAAA6C,SAAA;IAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;QDVxChF,EAAA,CAAAC,cAAA,UAAK;QAQyCD,EAAA,CAAAU,MAAA,GAA8B;;QAAAV,EAAA,CAAAW,YAAA,EAAO;QAK3EX,EAAA,CAAAC,cAAA,gBAAmG;QAApBD,EAAA,CAAAE,UAAA,mBAAAgF,6DAAA;UAAA,OAASD,GAAA,CAAAZ,OAAA,EAAS;QAAA,EAAC;QAC9FrE,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAU,MAAA,cAEzB;QAAAV,EAAA,CAAAW,YAAA,EAAO;QAKfX,EAAA,CAAAC,cAAA,cAAkD;QAC9CD,EAAA,CAAAyB,UAAA,KAAA0D,8CAAA,oBACwC;QAExCnF,EAAA,CAAAC,cAAA,cAA8B;QAEyBD,EAAA,CAAAmB,SAAA,aAA4C;QAAAnB,EAAA,CAAAW,YAAA,EAAO;QAEtGX,EAAA,CAAAC,cAAA,iBAC8D;QAA5DD,EAAA,CAAAE,UAAA,2BAAAkF,qEAAAC,MAAA;UAAA,OAAAJ,GAAA,CAAAxC,WAAA,GAAA4C,MAAA;QAAA,EAAyB,2BAAAD,qEAAA;UAAA,OAAkBH,GAAA,CAAA/B,aAAA,EAAe;QAAA,EAAjC;QAD3BlD,EAAA,CAAAW,YAAA,EAC8D;QAIlEX,EAAA,CAAAC,cAAA,eAA8B;QAKsBD,EAAA,CAAAU,MAAA,IAA2B;;QAAAV,EAAA,CAAAW,YAAA,EAAK;QACxEX,EAAA,CAAAC,cAAA,cAAwC;QAAAD,EAAA,CAAAU,MAAA,IAAwB;;QAAAV,EAAA,CAAAW,YAAA,EAAK;QACrEX,EAAA,CAAAC,cAAA,cAAwC;QAAAD,EAAA,CAAAU,MAAA,IAAyB;;QAAAV,EAAA,CAAAW,YAAA,EAAK;QACtEX,EAAA,CAAAC,cAAA,cAAwC;QAAAD,EAAA,CAAAU,MAAA,IAA4B;;QAAAV,EAAA,CAAAW,YAAA,EAAK;QAG7EX,EAAA,CAAAyB,UAAA,KAAA6D,6CAAA,oBAWQ;QAERtF,EAAA,CAAAyB,UAAA,KAAA8D,6CAAA,oBAMQ;QACVvF,EAAA,CAAAW,YAAA,EAAQ;;;QAzDwBX,EAAA,CAAAoB,SAAA,GAA8B;QAA9BpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAA+B,WAAA,uBAA8B;QAcT/B,EAAA,CAAAoB,SAAA,GAA0B;QAA1BpB,EAAA,CAAA2B,UAAA,SAAAsD,GAAA,CAAAhD,oBAAA,CAA0B;QAQ/EjC,EAAA,CAAAoB,SAAA,GAAyB;QAAzBpB,EAAA,CAAA2B,UAAA,YAAAsD,GAAA,CAAAxC,WAAA,CAAyB;QASqBzC,EAAA,CAAAoB,SAAA,GAA2B;QAA3BpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAA+B,WAAA,oBAA2B;QAC3B/B,EAAA,CAAAoB,SAAA,GAAwB;QAAxBpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAA+B,WAAA,iBAAwB;QACxB/B,EAAA,CAAAoB,SAAA,GAAyB;QAAzBpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAA+B,WAAA,kBAAyB;QACzB/B,EAAA,CAAAoB,SAAA,GAA4B;QAA5BpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAA+B,WAAA,qBAA4B;QAG5C/B,EAAA,CAAAoB,SAAA,GAAgC;QAAhCpB,EAAA,CAAA2B,UAAA,SAAAsD,GAAA,CAAApD,eAAA,CAAAsC,MAAA,KAAgC;QAapDnE,EAAA,CAAAoB,SAAA,GAAiC;QAAjCpB,EAAA,CAAA2B,UAAA,SAAAsD,GAAA,CAAApD,eAAA,CAAAsC,MAAA,MAAiC", "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "ModalAssignPlayersComponent_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "onAssign", "ɵɵtext", "ɵɵelementEnd", "ModalAssignPlayersComponent_tbody_34_tr_1_Template_button_click_8_listener", "restoredCtx", "_r9", "player_r7", "$implicit", "ctx_r8", "lookUp", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "name", "club", "group", "ɵɵtemplate", "ModalAssignPlayersComponent_tbody_34_tr_1_Template", "ɵɵproperty", "ctx_r2", "filteredPlayers", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ModalAssignPlayersComponent", "anyPlayerNotAccepted", "players", "some", "p", "isAccepted", "constructor", "_modalService", "_teamPlayerService", "searchQuery", "ngOnInit", "seasonId", "getUnassignPlayersBySeasonId", "parseInt", "subscribe", "data", "console", "log", "filterPlayers", "findInV<PERSON>ues", "arr", "value", "toLowerCase", "filter", "o", "Object", "entries", "entry", "String", "normalize", "includes", "toggleAction", "player", "close", "acceptedPlayers", "length", "alert", "onClose", "_", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "TeamPlayerService", "_2", "selectors", "viewQuery", "ModalAssignPlayersComponent_Query", "rf", "ctx", "ModalAssignPlayersComponent_Template_button_click_7_listener", "ModalAssignPlayersComponent_button_11_Template", "ModalAssignPlayersComponent_Template_input_ngModelChange_16_listener", "$event", "ModalAssignPlayersComponent_tbody_34_Template", "ModalAssignPlayersComponent_tbody_35_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-management\\modal-assign-players\\modal-assign-players.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-management\\modal-assign-players\\modal-assign-players.component.ts"], "sourcesContent": ["<div>\r\n\r\n    <div class=\"modal-header\">\r\n        <!-- text center title and subtitle -->\r\n        <!-- title is 'Team List 'H2 -->\r\n        <!-- subtitle is 'Assign player to team' h5-->\r\n        <div class=\"text-center w-100\">\r\n            <h2>\r\n                <span class=\"text-capitalize\">{{'Quick Assign' | translate}}</span>\r\n            </h2>\r\n\r\n        </div>\r\n\r\n        <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\" (click)=\"onClose()\">\r\n            <span aria-hidden=\"true\">&times;\r\n\r\n            </span>\r\n        </button>\r\n\r\n    </div>\r\n\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n        <button type=\"button\" class=\"btn btn-primary float-right\" *ngIf=\"anyPlayerNotAccepted\"\r\n            (click)=\"onAssign()\">Assign</button>\r\n        <!-- search field -->\r\n        <div class=\"input-group mb-3\">\r\n            <div class=\"input-group-prepend\">\r\n              <span class=\"input-group-text\" id=\"basic-addon1\"><i class=\"fa-light fa-magnifying-glass\"></i></span>\r\n            </div>\r\n            <input type=\"text\" class=\"form-control\" placeholder=\"Search\" aria-label=\"Search\" aria-describedby=\"basic-addon1\"\r\n              [(ngModel)]=\"searchQuery\" (ngModelChange)=\"filterPlayers()\">\r\n          </div>\r\n          \r\n        \r\n        <div class=\"table-responsive\">\r\n\r\n            <table #table class=\"table border row-border hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th class=\"text-capitalize text-center\">{{ 'Players' | translate }}</th>\r\n                    <th class=\"text-capitalize text-center\">{{ 'Club' | translate }}</th>\r\n                    <th class=\"text-capitalize text-center\">{{ 'Group' | translate }}</th>\r\n                    <th class=\"text-capitalize text-center\">{{ 'Navigate' | translate }}</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody class=\"text-center\" *ngIf=\"filteredPlayers.length > 0\">\r\n                  <tr *ngFor=\"let player of filteredPlayers\" >\r\n                    <td>{{ player.name }}</td>\r\n                    <td>{{ player.club.name }}</td>\r\n                    <td>{{ player.group.name }}</td>\r\n                    <td class=\"d-flex justify-content-center align-items-center\">\r\n                      <button type=\"button\" class=\"btn btn-primary\" (click)=\"lookUp(player)\"> \r\n                        <i class=\"fa-light fa-magnifying-glass\"></i>\r\n                      </button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n\r\n                <tbody *ngIf=\"filteredPlayers.length == 0\">\r\n                  <tr>\r\n                    <td colspan=\"4\" class=\"text-center\">\r\n                      {{ 'No players found' | translate }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n              \r\n        </div>\r\n\r\n    </div>\r\n\r\n\r\n\r\n</div>", "import { Component, Input, ViewChild } from '@angular/core';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ITeamPlayers } from 'app/interfaces/players';\r\nimport { TeamPlayerService } from 'app/services/team-player.service';\r\n\r\n@Component({\r\n  selector: 'app-modal-assign-players',\r\n  templateUrl: './modal-assign-players.component.html',\r\n  styleUrls: ['./modal-assign-players.component.scss']\r\n})\r\nexport class ModalAssignPlayersComponent {\r\n  searchQuery: string = '';\r\n  @ViewChild('table') table: any;\r\n\r\n  players: ITeamPlayers[] = [];\r\n  @Input() seasonId: any;\r\n\r\n  get anyPlayerNotAccepted(): boolean {\r\n    if (this.filteredPlayers) {\r\n      return this.players.some(p => p.isAccepted);\r\n    }\r\n  }\r\n\r\n  filteredPlayers: any[] = [];\r\n\r\n\r\n  constructor(\r\n    public _modalService: NgbActiveModal,\r\n    private _teamPlayerService: TeamPlayerService\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit(\r\n  ) {\r\n    if (this.seasonId) {\r\n      this._teamPlayerService.getUnassignPlayersBySeasonId(parseInt(this.seasonId)).subscribe((data: ITeamPlayers[]) => {\r\n        this.players = data;\r\n        console.log(\"players\", this.players);\r\n        this.filteredPlayers = this.players;\r\n\r\n      })\r\n    }\r\n  }\r\n\r\n  filterPlayers() {\r\n    if (this.filteredPlayers) {\r\n      function findInValues(arr, value) {\r\n        value = value.toLowerCase(); // Convert search query to lowercase\r\n        return arr.filter(o =>\r\n          Object.entries(o).some(entry =>\r\n            String(entry[1]).toLowerCase().normalize(\"NFC\").includes(value) // Normalize and search\r\n          )\r\n        );\r\n      }\r\n\r\n      this.filteredPlayers = findInValues(this.players, this.searchQuery);\r\n    } else {\r\n      this.filteredPlayers = [];\r\n    }\r\n  }\r\n\r\n\r\n\r\n  toggleAction(player: any, isAccepted: boolean) {\r\n    if (isAccepted) {\r\n      // Perform accept action\r\n      console.log('Accepted:', player.name);\r\n      player.isAccepted = true;\r\n    } else {\r\n      console.log('Denied:', player.name);\r\n      player.isAccepted = false;\r\n    }\r\n  }\r\n\r\n  lookUp(player: ITeamPlayers) {\r\n\r\n    console.log(\"navigateTo\", player);\r\n\r\n    this._modalService.close(player)\r\n\r\n  }\r\n\r\n\r\n  onAssign() {\r\n    // submit all players isAccepted\r\n    const acceptedPlayers = this.players.filter(p => p.isAccepted);\r\n\r\n    if (acceptedPlayers.length > 0) {\r\n      console.log(acceptedPlayers);\r\n      this._modalService.close(acceptedPlayers);\r\n    } else {\r\n      alert('Please select at least one player');\r\n    }\r\n\r\n  }\r\n\r\n  onClose() {\r\n    this._modalService.close();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
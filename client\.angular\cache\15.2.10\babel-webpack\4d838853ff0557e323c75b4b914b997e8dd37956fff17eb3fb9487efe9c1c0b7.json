{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/flex-layout/extended\";\nimport * as i5 from \"@core/directives/core-feather-icons/core-feather-icons\";\nconst _c0 = [\"core-menu-horizontal-item\", \"\"];\nfunction CoreMenuHorizontalItemComponent_ng_container_0_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c1 = function (a0) {\n  return [a0];\n};\nfunction CoreMenuHorizontalItemComponent_ng_container_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 4);\n    i0.ɵɵtemplate(1, CoreMenuHorizontalItemComponent_ng_container_0_a_1_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.classes)(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.item.url))(\"target\", ctx_r1.item.openInNewTab ? \"_blank\" : \"_self\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3);\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵtemplate(1, CoreMenuHorizontalItemComponent_ng_container_0_a_2_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(4);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.item.classes)(\"href\", ctx_r2.item.url, i0.ɵɵsanitizeUrl)(\"target\", ctx_r2.item.openInNewTab ? \"_blank\" : \"_self\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3);\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.item.icon);\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data-feather\", ctx_r9.item.icon);\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_i_1_Template, 1, 1, \"i\", 8);\n    i0.ɵɵtemplate(2, CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_span_2_Template, 1, 1, \"span\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.item.icon.startsWith(\"fa-\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.item.icon.startsWith(\"fa-\"));\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n    i0.ɵɵelementStart(1, \"span\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.item.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"translate\", ctx_r4.item.translate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.item.title);\n  }\n}\nfunction CoreMenuHorizontalItemComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuHorizontalItemComponent_ng_container_0_a_1_Template, 2, 6, \"a\", 1);\n    i0.ɵɵtemplate(2, CoreMenuHorizontalItemComponent_ng_container_0_a_2_Template, 2, 4, \"a\", 2);\n    i0.ɵɵtemplate(3, CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_Template, 3, 3, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.url && !ctx_r0.item.externalUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.url && ctx_r0.item.externalUrl);\n  }\n}\nexport class CoreMenuHorizontalItemComponent {\n  static #_ = this.ɵfac = function CoreMenuHorizontalItemComponent_Factory(t) {\n    return new (t || CoreMenuHorizontalItemComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CoreMenuHorizontalItemComponent,\n    selectors: [[\"\", \"core-menu-horizontal-item\", \"\"]],\n    inputs: {\n      item: \"item\"\n    },\n    attrs: _c0,\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [\"class\", \"dropdown-item d-flex align-items-center\", 3, \"ngClass\", \"routerLink\", \"target\", 4, \"ngIf\"], [\"class\", \"dropdown-item d-flex align-items-center\", 3, \"ngClass\", \"href\", \"target\", 4, \"ngIf\"], [\"itemContent\", \"\"], [1, \"dropdown-item\", \"d-flex\", \"align-items-center\", 3, \"ngClass\", \"routerLink\", \"target\"], [4, \"ngTemplateOutlet\"], [1, \"dropdown-item\", \"d-flex\", \"align-items-center\", 3, \"ngClass\", \"href\", \"target\"], [3, \"translate\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"data-feather\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"data-feather\"]],\n    template: function CoreMenuHorizontalItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CoreMenuHorizontalItemComponent_ng_container_0_Template, 5, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.item.hidden);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i2.RouterLink, i3.TranslateDirective, i4.DefaultClassDirective, i5.FeatherIconDirective],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;IASIA,EAAA,CAAAC,kBAAA,GAA6D;;;;;;;;IAP/DD,EAAA,CAAAE,cAAA,WAMC;IACCF,EAAA,CAAAG,UAAA,IAAAC,0EAAA,0BAA6D;I<PERSON>/DJ,EAAA,CAAAK,YAAA,EAAI;;;;;;IANFL,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAwB,eAAAT,EAAA,CAAAU,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAC,IAAA,CAAAI,GAAA,aAAAL,MAAA,CAAAC,IAAA,CAAAK,YAAA;IAKTb,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAM,UAAA,qBAAAS,GAAA,CAA6B;;;;;IAW5Cf,EAAA,CAAAC,kBAAA,GAA6D;;;;;IAP/DD,EAAA,CAAAE,cAAA,WAMC;IACCF,EAAA,CAAAG,UAAA,IAAAa,0EAAA,0BAA6D;IAC/DhB,EAAA,CAAAK,YAAA,EAAI;;;;;;IANFL,EAAA,CAAAM,UAAA,YAAAW,MAAA,CAAAT,IAAA,CAAAC,OAAA,CAAwB,SAAAQ,MAAA,CAAAT,IAAA,CAAAI,GAAA,EAAAZ,EAAA,CAAAkB,aAAA,YAAAD,MAAA,CAAAT,IAAA,CAAAK,YAAA;IAKTb,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAM,UAAA,qBAAAS,GAAA,CAA6B;;;;;IAK1Cf,EAAA,CAAAmB,SAAA,YAAiE;;;;IAA9DnB,EAAA,CAAAM,UAAA,YAAAc,MAAA,CAAAZ,IAAA,CAAAa,IAAA,CAAqB;;;;;IACxBrB,EAAA,CAAAmB,SAAA,eAA6E;;;;IAAvEnB,EAAA,CAAAM,UAAA,iBAAAgB,MAAA,CAAAd,IAAA,CAAAa,IAAA,CAA0B;;;;;IAFlCrB,EAAA,CAAAuB,uBAAA,GAAgC;IAC9BvB,EAAA,CAAAG,UAAA,IAAAqB,wFAAA,eAAiE;IACjExB,EAAA,CAAAG,UAAA,IAAAsB,2FAAA,kBAA6E;IAC/EzB,EAAA,CAAA0B,qBAAA,EAAe;;;;IAFa1B,EAAA,CAAAc,SAAA,GAAiC;IAAjCd,EAAA,CAAAM,UAAA,SAAAqB,MAAA,CAAAnB,IAAA,CAAAa,IAAA,CAAAO,UAAA,QAAiC;IACzB5B,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAM,UAAA,UAAAqB,MAAA,CAAAnB,IAAA,CAAAa,IAAA,CAAAO,UAAA,QAAkC;;;;;IAFtE5B,EAAA,CAAAG,UAAA,IAAA0B,oFAAA,0BAGe;IACf7B,EAAA,CAAAE,cAAA,cAAmC;IAAAF,EAAA,CAAA8B,MAAA,GAAgB;IAAA9B,EAAA,CAAAK,YAAA,EAAO;;;;IAJ3CL,EAAA,CAAAM,UAAA,SAAAyB,MAAA,CAAAvB,IAAA,CAAAa,IAAA,CAAe;IAIxBrB,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAAM,UAAA,cAAAyB,MAAA,CAAAvB,IAAA,CAAAwB,SAAA,CAA4B;IAAChC,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAAiC,iBAAA,CAAAF,MAAA,CAAAvB,IAAA,CAAA0B,KAAA,CAAgB;;;;;IA5BvDlC,EAAA,CAAAuB,uBAAA,GAAmC;IAEjCvB,EAAA,CAAAG,UAAA,IAAAgC,2DAAA,eAQI;IAGJnC,EAAA,CAAAG,UAAA,IAAAiC,2DAAA,eAQI;IAEJpC,EAAA,CAAAG,UAAA,IAAAkC,qEAAA,gCAAArC,EAAA,CAAAsC,sBAAA,CAMc;IAChBtC,EAAA,CAAA0B,qBAAA,EAAe;;;;IAzBV1B,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAM,UAAA,SAAAiC,MAAA,CAAA/B,IAAA,CAAAI,GAAA,KAAA2B,MAAA,CAAA/B,IAAA,CAAAgC,WAAA,CAAmC;IAWnCxC,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAM,UAAA,SAAAiC,MAAA,CAAA/B,IAAA,CAAAI,GAAA,IAAA2B,MAAA,CAAA/B,IAAA,CAAAgC,WAAA,CAAkC;;;ACVvC,OAAM,MAAOC,+BAA+B;EAAA,QAAAC,CAAA;qBAA/BD,+BAA+B;EAAA;EAAA,QAAAE,EAAA;UAA/BF,+BAA+B;IAAAG,SAAA;IAAAC,MAAA;MAAArC,IAAA;IAAA;IAAAsC,KAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDN5CrD,EAAA,CAAAG,UAAA,IAAAoD,uDAAA,0BA8Be;;;QA9BAvD,EAAA,CAAAM,UAAA,UAAAgD,GAAA,CAAA9C,IAAA,CAAAgD,MAAA,CAAkB", "names": ["i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵtemplate", "CoreMenuHorizontalItemComponent_ng_container_0_a_1_ng_container_1_Template", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "item", "classes", "ɵɵpureFunction1", "_c1", "url", "openInNewTab", "ɵɵadvance", "_r3", "CoreMenuHorizontalItemComponent_ng_container_0_a_2_ng_container_1_Template", "ctx_r2", "ɵɵsanitizeUrl", "ɵɵelement", "ctx_r8", "icon", "ctx_r9", "ɵɵelementContainerStart", "CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_i_1_Template", "CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_span_2_Template", "ɵɵelementContainerEnd", "ctx_r7", "startsWith", "CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_ng_container_0_Template", "ɵɵtext", "ctx_r4", "translate", "ɵɵtextInterpolate", "title", "CoreMenuHorizontalItemComponent_ng_container_0_a_1_Template", "CoreMenuHorizontalItemComponent_ng_container_0_a_2_Template", "CoreMenuHorizontalItemComponent_ng_container_0_ng_template_3_Template", "ɵɵtemplateRefExtractor", "ctx_r0", "externalUrl", "CoreMenuHorizontalItemComponent", "_", "_2", "selectors", "inputs", "attrs", "_c0", "decls", "vars", "consts", "template", "CoreMenuHorizontalItemComponent_Template", "rf", "ctx", "CoreMenuHorizontalItemComponent_ng_container_0_Template", "hidden"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\horizontal\\item\\item.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\horizontal\\item\\item.component.ts"], "sourcesContent": ["<ng-container *ngIf=\"!item.hidden\">\r\n  <!-- item.url -->\r\n  <a\r\n    class=\"dropdown-item d-flex align-items-center\"\r\n    [ngClass]=\"item.classes\"\r\n    *ngIf=\"item.url && !item.externalUrl\"\r\n    [routerLink]=\"[item.url]\"\r\n    [target]=\"item.openInNewTab ? '_blank' : '_self'\"\r\n  >\r\n    <ng-container *ngTemplateOutlet=\"itemContent\"></ng-container>\r\n  </a>\r\n\r\n  <!-- item.externalUrl -->\r\n  <a\r\n    class=\"dropdown-item d-flex align-items-center\"\r\n    [ngClass]=\"item.classes\"\r\n    *ngIf=\"item.url && item.externalUrl\"\r\n    [href]=\"item.url\"\r\n    [target]=\"item.openInNewTab ? '_blank' : '_self'\"\r\n  >\r\n    <ng-container *ngTemplateOutlet=\"itemContent\"></ng-container>\r\n  </a>\r\n\r\n  <ng-template #itemContent>\r\n    <ng-container *ngIf=\"item.icon\">\r\n      <i [ngClass]=\"item.icon\" *ngIf=\"item.icon.startsWith('fa-')\"></i>\r\n      <span [data-feather]=\"item.icon\" *ngIf=\"!item.icon.startsWith('fa-')\"></span>\r\n    </ng-container>\r\n    <span [translate]=\"item.translate\">{{ item.title }}</span>\r\n  </ng-template>\r\n</ng-container>\r\n", "import { Component, HostBinding, Input } from '@angular/core';\r\n\r\n@Component({\r\n  selector: '[core-menu-horizontal-item]',\r\n  templateUrl: './item.component.html'\r\n})\r\nexport class CoreMenuHorizontalItemComponent {\r\n  @Input()\r\n  item: any;\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
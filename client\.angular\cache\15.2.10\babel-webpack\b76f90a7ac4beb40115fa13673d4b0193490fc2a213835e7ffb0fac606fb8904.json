{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function debounceTime(dueTime, scheduler = async) {\n  return source => source.lift(new DebounceTimeOperator(dueTime, scheduler));\n}\nclass DebounceTimeOperator {\n  constructor(dueTime, scheduler) {\n    this.dueTime = dueTime;\n    this.scheduler = scheduler;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new DebounceTimeSubscriber(subscriber, this.dueTime, this.scheduler));\n  }\n}\nclass DebounceTimeSubscriber extends Subscriber {\n  constructor(destination, dueTime, scheduler) {\n    super(destination);\n    this.dueTime = dueTime;\n    this.scheduler = scheduler;\n    this.debouncedSubscription = null;\n    this.lastValue = null;\n    this.hasValue = false;\n  }\n  _next(value) {\n    this.clearDebounce();\n    this.lastValue = value;\n    this.hasValue = true;\n    this.add(this.debouncedSubscription = this.scheduler.schedule(dispatchNext, this.dueTime, this));\n  }\n  _complete() {\n    this.debouncedNext();\n    this.destination.complete();\n  }\n  debouncedNext() {\n    this.clearDebounce();\n    if (this.hasValue) {\n      const {\n        lastValue\n      } = this;\n      this.lastValue = null;\n      this.hasValue = false;\n      this.destination.next(lastValue);\n    }\n  }\n  clearDebounce() {\n    const debouncedSubscription = this.debouncedSubscription;\n    if (debouncedSubscription !== null) {\n      this.remove(debouncedSubscription);\n      debouncedSubscription.unsubscribe();\n      this.debouncedSubscription = null;\n    }\n  }\n}\nfunction dispatchNext(subscriber) {\n  subscriber.debouncedNext();\n}", "map": {"version": 3, "names": ["Subscriber", "async", "debounceTime", "dueTime", "scheduler", "source", "lift", "DebounceTimeOperator", "constructor", "call", "subscriber", "subscribe", "DebounceTimeSubscriber", "destination", "debouncedSubscription", "lastValue", "hasValue", "_next", "value", "clearDebounce", "add", "schedule", "dispatchNext", "_complete", "debouncedNext", "complete", "next", "remove", "unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/debounceTime.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function debounceTime(dueTime, scheduler = async) {\n    return (source) => source.lift(new DebounceTimeOperator(dueTime, scheduler));\n}\nclass DebounceTimeOperator {\n    constructor(dueTime, scheduler) {\n        this.dueTime = dueTime;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DebounceTimeSubscriber(subscriber, this.dueTime, this.scheduler));\n    }\n}\nclass DebounceTimeSubscriber extends Subscriber {\n    constructor(destination, dueTime, scheduler) {\n        super(destination);\n        this.dueTime = dueTime;\n        this.scheduler = scheduler;\n        this.debouncedSubscription = null;\n        this.lastValue = null;\n        this.hasValue = false;\n    }\n    _next(value) {\n        this.clearDebounce();\n        this.lastValue = value;\n        this.hasValue = true;\n        this.add(this.debouncedSubscription = this.scheduler.schedule(dispatchNext, this.dueTime, this));\n    }\n    _complete() {\n        this.debouncedNext();\n        this.destination.complete();\n    }\n    debouncedNext() {\n        this.clearDebounce();\n        if (this.hasValue) {\n            const { lastValue } = this;\n            this.lastValue = null;\n            this.hasValue = false;\n            this.destination.next(lastValue);\n        }\n    }\n    clearDebounce() {\n        const debouncedSubscription = this.debouncedSubscription;\n        if (debouncedSubscription !== null) {\n            this.remove(debouncedSubscription);\n            debouncedSubscription.unsubscribe();\n            this.debouncedSubscription = null;\n        }\n    }\n}\nfunction dispatchNext(subscriber) {\n    subscriber.debouncedNext();\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,GAAGH,KAAK,EAAE;EACrD,OAAQI,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,oBAAoB,CAACJ,OAAO,EAAEC,SAAS,CAAC,CAAC;AAChF;AACA,MAAMG,oBAAoB,CAAC;EACvBC,WAAWA,CAACL,OAAO,EAAEC,SAAS,EAAE;IAC5B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,sBAAsB,CAACF,UAAU,EAAE,IAAI,CAACP,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,CAAC;EACjG;AACJ;AACA,MAAMQ,sBAAsB,SAASZ,UAAU,CAAC;EAC5CQ,WAAWA,CAACK,WAAW,EAAEV,OAAO,EAAEC,SAAS,EAAE;IACzC,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACV,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACU,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACJ,SAAS,GAAGG,KAAK;IACtB,IAAI,CAACF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACI,GAAG,CAAC,IAAI,CAACN,qBAAqB,GAAG,IAAI,CAACV,SAAS,CAACiB,QAAQ,CAACC,YAAY,EAAE,IAAI,CAACnB,OAAO,EAAE,IAAI,CAAC,CAAC;EACpG;EACAoB,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACX,WAAW,CAACY,QAAQ,CAAC,CAAC;EAC/B;EACAD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACL,aAAa,CAAC,CAAC;IACpB,IAAI,IAAI,CAACH,QAAQ,EAAE;MACf,MAAM;QAAED;MAAU,CAAC,GAAG,IAAI;MAC1B,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACH,WAAW,CAACa,IAAI,CAACX,SAAS,CAAC;IACpC;EACJ;EACAI,aAAaA,CAAA,EAAG;IACZ,MAAML,qBAAqB,GAAG,IAAI,CAACA,qBAAqB;IACxD,IAAIA,qBAAqB,KAAK,IAAI,EAAE;MAChC,IAAI,CAACa,MAAM,CAACb,qBAAqB,CAAC;MAClCA,qBAAqB,CAACc,WAAW,CAAC,CAAC;MACnC,IAAI,CAACd,qBAAqB,GAAG,IAAI;IACrC;EACJ;AACJ;AACA,SAASQ,YAAYA,CAACZ,UAAU,EAAE;EAC9BA,UAAU,CAACc,aAAa,CAAC,CAAC;AAC9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export function isObject(x) {\n  return x !== null && typeof x === 'object';\n}", "map": {"version": 3, "names": ["isObject", "x"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isObject.js"], "sourcesContent": ["export function isObject(x) {\n    return x !== null && typeof x === 'object';\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ;AAC9C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport const defaultThrottleConfig = {\n  leading: true,\n  trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n  return source => source.lift(new ThrottleOperator(durationSelector, config.leading, config.trailing));\n}\nclass ThrottleOperator {\n  constructor(durationSelector, leading, trailing) {\n    this.durationSelector = durationSelector;\n    this.leading = leading;\n    this.trailing = trailing;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing));\n  }\n}\nclass ThrottleSubscriber extends OuterSubscriber {\n  constructor(destination, durationSelector, _leading, _trailing) {\n    super(destination);\n    this.destination = destination;\n    this.durationSelector = durationSelector;\n    this._leading = _leading;\n    this._trailing = _trailing;\n    this._hasValue = false;\n  }\n  _next(value) {\n    this._hasValue = true;\n    this._sendValue = value;\n    if (!this._throttled) {\n      if (this._leading) {\n        this.send();\n      } else {\n        this.throttle(value);\n      }\n    }\n  }\n  send() {\n    const {\n      _hasValue,\n      _sendValue\n    } = this;\n    if (_hasValue) {\n      this.destination.next(_sendValue);\n      this.throttle(_sendValue);\n    }\n    this._hasValue = false;\n    this._sendValue = null;\n  }\n  throttle(value) {\n    const duration = this.tryDurationSelector(value);\n    if (!!duration) {\n      this.add(this._throttled = subscribeToResult(this, duration));\n    }\n  }\n  tryDurationSelector(value) {\n    try {\n      return this.durationSelector(value);\n    } catch (err) {\n      this.destination.error(err);\n      return null;\n    }\n  }\n  throttlingDone() {\n    const {\n      _throttled,\n      _trailing\n    } = this;\n    if (_throttled) {\n      _throttled.unsubscribe();\n    }\n    this._throttled = null;\n    if (_trailing) {\n      this.send();\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.throttlingDone();\n  }\n  notifyComplete() {\n    this.throttlingDone();\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "subscribeToResult", "defaultThrottleConfig", "leading", "trailing", "throttle", "durationSelector", "config", "source", "lift", "ThrottleOperator", "constructor", "call", "subscriber", "subscribe", "ThrottleSubscriber", "destination", "_leading", "_trailing", "_hasValue", "_next", "value", "_sendValue", "_throttled", "send", "next", "duration", "tryDurationSelector", "add", "err", "error", "throttlingDone", "unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "notifyComplete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/throttle.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport const defaultThrottleConfig = {\n    leading: true,\n    trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n    return (source) => source.lift(new ThrottleOperator(durationSelector, config.leading, config.trailing));\n}\nclass ThrottleOperator {\n    constructor(durationSelector, leading, trailing) {\n        this.durationSelector = durationSelector;\n        this.leading = leading;\n        this.trailing = trailing;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing));\n    }\n}\nclass ThrottleSubscriber extends OuterSubscriber {\n    constructor(destination, durationSelector, _leading, _trailing) {\n        super(destination);\n        this.destination = destination;\n        this.durationSelector = durationSelector;\n        this._leading = _leading;\n        this._trailing = _trailing;\n        this._hasValue = false;\n    }\n    _next(value) {\n        this._hasValue = true;\n        this._sendValue = value;\n        if (!this._throttled) {\n            if (this._leading) {\n                this.send();\n            }\n            else {\n                this.throttle(value);\n            }\n        }\n    }\n    send() {\n        const { _hasValue, _sendValue } = this;\n        if (_hasValue) {\n            this.destination.next(_sendValue);\n            this.throttle(_sendValue);\n        }\n        this._hasValue = false;\n        this._sendValue = null;\n    }\n    throttle(value) {\n        const duration = this.tryDurationSelector(value);\n        if (!!duration) {\n            this.add(this._throttled = subscribeToResult(this, duration));\n        }\n    }\n    tryDurationSelector(value) {\n        try {\n            return this.durationSelector(value);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return null;\n        }\n    }\n    throttlingDone() {\n        const { _throttled, _trailing } = this;\n        if (_throttled) {\n            _throttled.unsubscribe();\n        }\n        this._throttled = null;\n        if (_trailing) {\n            this.send();\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.throttlingDone();\n    }\n    notifyComplete() {\n        this.throttlingDone();\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE;AACd,CAAC;AACD,OAAO,SAASC,QAAQA,CAACC,gBAAgB,EAAEC,MAAM,GAAGL,qBAAqB,EAAE;EACvE,OAAQM,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,gBAAgB,CAACJ,gBAAgB,EAAEC,MAAM,CAACJ,OAAO,EAAEI,MAAM,CAACH,QAAQ,CAAC,CAAC;AAC3G;AACA,MAAMM,gBAAgB,CAAC;EACnBC,WAAWA,CAACL,gBAAgB,EAAEH,OAAO,EAAEC,QAAQ,EAAE;IAC7C,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAQ,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,kBAAkB,CAACF,UAAU,EAAE,IAAI,CAACP,gBAAgB,EAAE,IAAI,CAACH,OAAO,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;EACnH;AACJ;AACA,MAAMW,kBAAkB,SAASf,eAAe,CAAC;EAC7CW,WAAWA,CAACK,WAAW,EAAEV,gBAAgB,EAAEW,QAAQ,EAAEC,SAAS,EAAE;IAC5D,KAAK,CAACF,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACV,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACW,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACG,UAAU,GAAGD,KAAK;IACvB,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE;MAClB,IAAI,IAAI,CAACN,QAAQ,EAAE;QACf,IAAI,CAACO,IAAI,CAAC,CAAC;MACf,CAAC,MACI;QACD,IAAI,CAACnB,QAAQ,CAACgB,KAAK,CAAC;MACxB;IACJ;EACJ;EACAG,IAAIA,CAAA,EAAG;IACH,MAAM;MAAEL,SAAS;MAAEG;IAAW,CAAC,GAAG,IAAI;IACtC,IAAIH,SAAS,EAAE;MACX,IAAI,CAACH,WAAW,CAACS,IAAI,CAACH,UAAU,CAAC;MACjC,IAAI,CAACjB,QAAQ,CAACiB,UAAU,CAAC;IAC7B;IACA,IAAI,CAACH,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,UAAU,GAAG,IAAI;EAC1B;EACAjB,QAAQA,CAACgB,KAAK,EAAE;IACZ,MAAMK,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAACN,KAAK,CAAC;IAChD,IAAI,CAAC,CAACK,QAAQ,EAAE;MACZ,IAAI,CAACE,GAAG,CAAC,IAAI,CAACL,UAAU,GAAGtB,iBAAiB,CAAC,IAAI,EAAEyB,QAAQ,CAAC,CAAC;IACjE;EACJ;EACAC,mBAAmBA,CAACN,KAAK,EAAE;IACvB,IAAI;MACA,OAAO,IAAI,CAACf,gBAAgB,CAACe,KAAK,CAAC;IACvC,CAAC,CACD,OAAOQ,GAAG,EAAE;MACR,IAAI,CAACb,WAAW,CAACc,KAAK,CAACD,GAAG,CAAC;MAC3B,OAAO,IAAI;IACf;EACJ;EACAE,cAAcA,CAAA,EAAG;IACb,MAAM;MAAER,UAAU;MAAEL;IAAU,CAAC,GAAG,IAAI;IACtC,IAAIK,UAAU,EAAE;MACZA,UAAU,CAACS,WAAW,CAAC,CAAC;IAC5B;IACA,IAAI,CAACT,UAAU,GAAG,IAAI;IACtB,IAAIL,SAAS,EAAE;MACX,IAAI,CAACM,IAAI,CAAC,CAAC;IACf;EACJ;EACAS,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACP,cAAc,CAAC,CAAC;EACzB;EACAQ,cAAcA,CAAA,EAAG;IACb,IAAI,CAACR,cAAc,CAAC,CAAC;EACzB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
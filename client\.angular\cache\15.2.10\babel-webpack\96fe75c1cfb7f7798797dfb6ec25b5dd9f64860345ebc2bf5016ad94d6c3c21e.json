{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { BreadcrumbComponent } from 'app/layout/components/content-header/breadcrumb/breadcrumb.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class BreadcrumbModule {\n  static #_ = this.ɵfac = function BreadcrumbModule_Factory(t) {\n    return new (t || BreadcrumbModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BreadcrumbModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild([]), TranslateModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BreadcrumbModule, {\n    declarations: [BreadcrumbComponent],\n    imports: [CommonModule, i1.RouterModule, TranslateModule],\n    exports: [BreadcrumbComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,sEAAsE;AAC1G,SAASC,eAAe,QAAQ,qBAAqB;;;AAOrD,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA;UAAhBF;EAAgB;EAAA,QAAAG,EAAA;cAHjBP,YAAY,EAAEC,YAAY,CAACO,QAAQ,CAAC,EAAE,CAAC,EAACL,eAAe;EAAA;;;2EAGtDC,gBAAgB;IAAAK,YAAA,GAJZP,mBAAmB;IAAAQ,OAAA,GACxBV,YAAY,EAAAW,EAAA,CAAAV,YAAA,EAA4BE,eAAe;IAAAS,OAAA,GACvDV,mBAAmB;EAAA;AAAA", "names": ["CommonModule", "RouterModule", "BreadcrumbComponent", "TranslateModule", "BreadcrumbModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content-header\\breadcrumb\\breadcrumb.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { BreadcrumbComponent } from 'app/layout/components/content-header/breadcrumb/breadcrumb.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\n\r\n@NgModule({\r\n  declarations: [BreadcrumbComponent],\r\n  imports: [CommonModule, RouterModule.forChild([]),TranslateModule],\r\n  exports: [BreadcrumbComponent]\r\n})\r\nexport class BreadcrumbModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
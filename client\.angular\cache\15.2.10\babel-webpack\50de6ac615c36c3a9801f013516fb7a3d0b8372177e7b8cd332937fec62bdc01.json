{"ast": null, "code": "import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\n\n/**\n * The base implementation of `_.update`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to update.\n * @param {Function} updater The function to produce the updated value.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseUpdate(object, path, updater, customizer) {\n  return baseSet(object, path, updater(baseGet(object, path)), customizer);\n}\nexport default baseUpdate;", "map": {"version": 3, "names": ["baseGet", "baseSet", "baseUpdate", "object", "path", "updater", "customizer"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_baseUpdate.js"], "sourcesContent": ["import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\n\n/**\n * The base implementation of `_.update`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to update.\n * @param {Function} updater The function to produce the updated value.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseUpdate(object, path, updater, customizer) {\n  return baseSet(object, path, updater(baseGet(object, path)), customizer);\n}\n\nexport default baseUpdate;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAE;EACrD,OAAOL,OAAO,CAACE,MAAM,EAAEC,IAAI,EAAEC,OAAO,CAACL,OAAO,CAACG,MAAM,EAAEC,IAAI,CAAC,CAAC,EAAEE,UAAU,CAAC;AAC1E;AAEA,eAAeJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result;\n}\nexport default toKey;", "map": {"version": 3, "names": ["isSymbol", "INFINITY", "to<PERSON><PERSON>", "value", "result"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/_toKey.js"], "sourcesContent": ["import isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\nexport default to<PERSON>ey;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,IAAI,OAAOA,KAAK,IAAI,QAAQ,IAAIH,QAAQ,CAACG,KAAK,CAAC,EAAE;IAC/C,OAAOA,KAAK;EACd;EACA,IAAIC,MAAM,GAAID,KAAK,GAAG,EAAG;EACzB,OAAQC,MAAM,IAAI,GAAG,IAAK,CAAC,GAAGD,KAAK,IAAK,CAACF,QAAQ,GAAI,IAAI,GAAGG,MAAM;AACpE;AAEA,eAAeF,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
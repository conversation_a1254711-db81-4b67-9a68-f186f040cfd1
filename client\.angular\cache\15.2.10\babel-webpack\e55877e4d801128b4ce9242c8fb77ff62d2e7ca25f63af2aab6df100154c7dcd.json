{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i2 from \"@ngx-translate/core\";\nexport class UpdateModalComponent {\n  constructor() {\n    this.currentVersion = '';\n    this.availableVersion = '';\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function UpdateModalComponent_Factory(t) {\n    return new (t || UpdateModalComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UpdateModalComponent,\n    selectors: [[\"app-update-modal\"]],\n    decls: 15,\n    vars: 12,\n    consts: [[1, \"modal-header\"], [1, \"modal-title\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"]],\n    template: function UpdateModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 2)(5, \"h5\");\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\");\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 3)(12, \"button\", 4);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"Update App\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 6, \"Your version is outdated\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 8, \"Please update to the latest version\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 10, \"OK\"), \" \");\n      }\n    },\n    dependencies: [i1.RippleEffectDirective, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;AAOA,OAAM,MAAOA,oBAAoB;EAG/BC,YAAA;IAFA,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,gBAAgB,GAAW,EAAE;EACd;EAEfC,QAAQA,CAAA,GAAU;EAAC,QAAAC,CAAA;qBALRL,oBAAoB;EAAA;EAAA,QAAAM,EAAA;UAApBN,oBAAoB;IAAAO,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNjCE,EAAA,CAAAC,cAAA,aAA0B;QACED,EAAA,CAAAE,MAAA,GAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAG3DH,EAAA,CAAAC,cAAA,aAAwB;QAChBD,EAAA,CAAAE,MAAA,GAAwC;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjDH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,GAAoD;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAG/DH,EAAA,CAAAC,cAAA,cAA0B;QAElBD,EAAA,CAAAE,MAAA,IACJ;;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAXeH,EAAA,CAAAI,SAAA,GAA0B;QAA1BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,qBAA0B;QAI9CN,EAAA,CAAAI,SAAA,GAAwC;QAAxCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,mCAAwC;QACzCN,EAAA,CAAAI,SAAA,GAAoD;QAApDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,+CAAoD;QAKnDN,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAM,WAAA,oBACJ", "names": ["UpdateModalComponent", "constructor", "currentVersion", "availableVersion", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "UpdateModalComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\update-modal\\update-modal.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\update-modal\\update-modal.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-update-modal',\r\n  templateUrl: './update-modal.component.html',\r\n  styleUrls: ['./update-modal.component.scss'],\r\n})\r\nexport class UpdateModalComponent implements OnInit {\r\n  currentVersion: string = '';\r\n  availableVersion: string = '';\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {}\r\n \r\n}\r\n", "<!-- <div class=\"modal\"> -->\r\n<div class=\"modal-header\">\r\n    <h4 class=\"modal-title\">{{'Update App'|translate}}</h4>\r\n</div>\r\n\r\n<div class=\"modal-body\">\r\n    <h5>{{'Your version is outdated'|translate}}</h5>\r\n    <p>{{'Please update to the latest version'|translate }}</p>\r\n\r\n</div>\r\n<div class=\"modal-footer\">\r\n    <button type=\"button\" class=\"btn btn-primary\" rippleEffect>\r\n        {{'OK'|translate}}\r\n    </button>\r\n</div>\r\n<!-- </div> -->"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ_global, ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n// Attach $localize to the global context, as a side-effect of this module.\nɵ_global.$localize = ɵ$localize;", "map": {"version": 3, "names": ["ɵ_global", "ɵ$localize", "$localize"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/localize/fesm2020/init.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ_global, ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n// Attach $localize to the global context, as a side-effect of this module.\nɵ_global.$localize = ɵ$localize;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,UAAU,QAAQ,mBAAmB;AACxD,SAASA,UAAU,IAAIC,SAAS,QAAQ,mBAAmB;;AAE3D;AACAF,QAAQ,CAACE,SAAS,GAAGD,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
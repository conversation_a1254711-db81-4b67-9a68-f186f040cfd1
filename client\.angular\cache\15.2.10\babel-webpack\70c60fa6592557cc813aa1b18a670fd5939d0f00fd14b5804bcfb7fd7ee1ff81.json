{"ast": null, "code": "import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { map } from '../operators/map';\nimport { canReportError } from '../util/canReportError';\nimport { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return (...args) => bindNodeCallback(callbackFunc, scheduler)(...args).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n  }\n  return function (...args) {\n    const params = {\n      subject: undefined,\n      args,\n      callbackFunc,\n      scheduler,\n      context: this\n    };\n    return new Observable(subscriber => {\n      const {\n        context\n      } = params;\n      let {\n        subject\n      } = params;\n      if (!scheduler) {\n        if (!subject) {\n          subject = params.subject = new AsyncSubject();\n          const handler = (...innerArgs) => {\n            const err = innerArgs.shift();\n            if (err) {\n              subject.error(err);\n              return;\n            }\n            subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n            subject.complete();\n          };\n          try {\n            callbackFunc.apply(context, [...args, handler]);\n          } catch (err) {\n            if (canReportError(subject)) {\n              subject.error(err);\n            } else {\n              console.warn(err);\n            }\n          }\n        }\n        return subject.subscribe(subscriber);\n      } else {\n        return scheduler.schedule(dispatch, 0, {\n          params,\n          subscriber,\n          context\n        });\n      }\n    });\n  };\n}\nfunction dispatch(state) {\n  const {\n    params,\n    subscriber,\n    context\n  } = state;\n  const {\n    callbackFunc,\n    args,\n    scheduler\n  } = params;\n  let subject = params.subject;\n  if (!subject) {\n    subject = params.subject = new AsyncSubject();\n    const handler = (...innerArgs) => {\n      const err = innerArgs.shift();\n      if (err) {\n        this.add(scheduler.schedule(dispatchError, 0, {\n          err,\n          subject\n        }));\n      } else {\n        const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n        this.add(scheduler.schedule(dispatchNext, 0, {\n          value,\n          subject\n        }));\n      }\n    };\n    try {\n      callbackFunc.apply(context, [...args, handler]);\n    } catch (err) {\n      this.add(scheduler.schedule(dispatchError, 0, {\n        err,\n        subject\n      }));\n    }\n  }\n  this.add(subject.subscribe(subscriber));\n}\nfunction dispatchNext(arg) {\n  const {\n    value,\n    subject\n  } = arg;\n  subject.next(value);\n  subject.complete();\n}\nfunction dispatchError(arg) {\n  const {\n    err,\n    subject\n  } = arg;\n  subject.error(err);\n}", "map": {"version": 3, "names": ["Observable", "AsyncSubject", "map", "canReportError", "isScheduler", "isArray", "bindNodeCallback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "args", "pipe", "params", "subject", "undefined", "context", "subscriber", "handler", "innerArgs", "err", "shift", "error", "next", "length", "complete", "apply", "console", "warn", "subscribe", "schedule", "dispatch", "state", "add", "dispatchError", "value", "dispatchNext", "arg"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/bindNodeCallback.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { map } from '../operators/map';\nimport { canReportError } from '../util/canReportError';\nimport { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return (...args) => bindNodeCallback(callbackFunc, scheduler)(...args).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n        }\n    }\n    return function (...args) {\n        const params = {\n            subject: undefined,\n            args,\n            callbackFunc,\n            scheduler,\n            context: this,\n        };\n        return new Observable(subscriber => {\n            const { context } = params;\n            let { subject } = params;\n            if (!scheduler) {\n                if (!subject) {\n                    subject = params.subject = new AsyncSubject();\n                    const handler = (...innerArgs) => {\n                        const err = innerArgs.shift();\n                        if (err) {\n                            subject.error(err);\n                            return;\n                        }\n                        subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n                        subject.complete();\n                    };\n                    try {\n                        callbackFunc.apply(context, [...args, handler]);\n                    }\n                    catch (err) {\n                        if (canReportError(subject)) {\n                            subject.error(err);\n                        }\n                        else {\n                            console.warn(err);\n                        }\n                    }\n                }\n                return subject.subscribe(subscriber);\n            }\n            else {\n                return scheduler.schedule(dispatch, 0, { params, subscriber, context });\n            }\n        });\n    };\n}\nfunction dispatch(state) {\n    const { params, subscriber, context } = state;\n    const { callbackFunc, args, scheduler } = params;\n    let subject = params.subject;\n    if (!subject) {\n        subject = params.subject = new AsyncSubject();\n        const handler = (...innerArgs) => {\n            const err = innerArgs.shift();\n            if (err) {\n                this.add(scheduler.schedule(dispatchError, 0, { err, subject }));\n            }\n            else {\n                const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n                this.add(scheduler.schedule(dispatchNext, 0, { value, subject }));\n            }\n        };\n        try {\n            callbackFunc.apply(context, [...args, handler]);\n        }\n        catch (err) {\n            this.add(scheduler.schedule(dispatchError, 0, { err, subject }));\n        }\n    }\n    this.add(subject.subscribe(subscriber));\n}\nfunction dispatchNext(arg) {\n    const { value, subject } = arg;\n    subject.next(value);\n    subject.complete();\n}\nfunction dispatchError(arg) {\n    const { err, subject } = arg;\n    subject.error(err);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAO,SAASC,gBAAgBA,CAACC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACtE,IAAID,cAAc,EAAE;IAChB,IAAIJ,WAAW,CAACI,cAAc,CAAC,EAAE;MAC7BC,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI;MACD,OAAO,CAAC,GAAGE,IAAI,KAAKJ,gBAAgB,CAACC,YAAY,EAAEE,SAAS,CAAC,CAAC,GAAGC,IAAI,CAAC,CAACC,IAAI,CAACT,GAAG,CAACQ,IAAI,IAAIL,OAAO,CAACK,IAAI,CAAC,GAAGF,cAAc,CAAC,GAAGE,IAAI,CAAC,GAAGF,cAAc,CAACE,IAAI,CAAC,CAAC,CAAC;IAC5J;EACJ;EACA,OAAO,UAAU,GAAGA,IAAI,EAAE;IACtB,MAAME,MAAM,GAAG;MACXC,OAAO,EAAEC,SAAS;MAClBJ,IAAI;MACJH,YAAY;MACZE,SAAS;MACTM,OAAO,EAAE;IACb,CAAC;IACD,OAAO,IAAIf,UAAU,CAACgB,UAAU,IAAI;MAChC,MAAM;QAAED;MAAQ,CAAC,GAAGH,MAAM;MAC1B,IAAI;QAAEC;MAAQ,CAAC,GAAGD,MAAM;MACxB,IAAI,CAACH,SAAS,EAAE;QACZ,IAAI,CAACI,OAAO,EAAE;UACVA,OAAO,GAAGD,MAAM,CAACC,OAAO,GAAG,IAAIZ,YAAY,CAAC,CAAC;UAC7C,MAAMgB,OAAO,GAAGA,CAAC,GAAGC,SAAS,KAAK;YAC9B,MAAMC,GAAG,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC;YAC7B,IAAID,GAAG,EAAE;cACLN,OAAO,CAACQ,KAAK,CAACF,GAAG,CAAC;cAClB;YACJ;YACAN,OAAO,CAACS,IAAI,CAACJ,SAAS,CAACK,MAAM,IAAI,CAAC,GAAGL,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC;YAC9DL,OAAO,CAACW,QAAQ,CAAC,CAAC;UACtB,CAAC;UACD,IAAI;YACAjB,YAAY,CAACkB,KAAK,CAACV,OAAO,EAAE,CAAC,GAAGL,IAAI,EAAEO,OAAO,CAAC,CAAC;UACnD,CAAC,CACD,OAAOE,GAAG,EAAE;YACR,IAAIhB,cAAc,CAACU,OAAO,CAAC,EAAE;cACzBA,OAAO,CAACQ,KAAK,CAACF,GAAG,CAAC;YACtB,CAAC,MACI;cACDO,OAAO,CAACC,IAAI,CAACR,GAAG,CAAC;YACrB;UACJ;QACJ;QACA,OAAON,OAAO,CAACe,SAAS,CAACZ,UAAU,CAAC;MACxC,CAAC,MACI;QACD,OAAOP,SAAS,CAACoB,QAAQ,CAACC,QAAQ,EAAE,CAAC,EAAE;UAAElB,MAAM;UAAEI,UAAU;UAAED;QAAQ,CAAC,CAAC;MAC3E;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASe,QAAQA,CAACC,KAAK,EAAE;EACrB,MAAM;IAAEnB,MAAM;IAAEI,UAAU;IAAED;EAAQ,CAAC,GAAGgB,KAAK;EAC7C,MAAM;IAAExB,YAAY;IAAEG,IAAI;IAAED;EAAU,CAAC,GAAGG,MAAM;EAChD,IAAIC,OAAO,GAAGD,MAAM,CAACC,OAAO;EAC5B,IAAI,CAACA,OAAO,EAAE;IACVA,OAAO,GAAGD,MAAM,CAACC,OAAO,GAAG,IAAIZ,YAAY,CAAC,CAAC;IAC7C,MAAMgB,OAAO,GAAGA,CAAC,GAAGC,SAAS,KAAK;MAC9B,MAAMC,GAAG,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC;MAC7B,IAAID,GAAG,EAAE;QACL,IAAI,CAACa,GAAG,CAACvB,SAAS,CAACoB,QAAQ,CAACI,aAAa,EAAE,CAAC,EAAE;UAAEd,GAAG;UAAEN;QAAQ,CAAC,CAAC,CAAC;MACpE,CAAC,MACI;QACD,MAAMqB,KAAK,GAAGhB,SAAS,CAACK,MAAM,IAAI,CAAC,GAAGL,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;QAC9D,IAAI,CAACc,GAAG,CAACvB,SAAS,CAACoB,QAAQ,CAACM,YAAY,EAAE,CAAC,EAAE;UAAED,KAAK;UAAErB;QAAQ,CAAC,CAAC,CAAC;MACrE;IACJ,CAAC;IACD,IAAI;MACAN,YAAY,CAACkB,KAAK,CAACV,OAAO,EAAE,CAAC,GAAGL,IAAI,EAAEO,OAAO,CAAC,CAAC;IACnD,CAAC,CACD,OAAOE,GAAG,EAAE;MACR,IAAI,CAACa,GAAG,CAACvB,SAAS,CAACoB,QAAQ,CAACI,aAAa,EAAE,CAAC,EAAE;QAAEd,GAAG;QAAEN;MAAQ,CAAC,CAAC,CAAC;IACpE;EACJ;EACA,IAAI,CAACmB,GAAG,CAACnB,OAAO,CAACe,SAAS,CAACZ,UAAU,CAAC,CAAC;AAC3C;AACA,SAASmB,YAAYA,CAACC,GAAG,EAAE;EACvB,MAAM;IAAEF,KAAK;IAAErB;EAAQ,CAAC,GAAGuB,GAAG;EAC9BvB,OAAO,CAACS,IAAI,CAACY,KAAK,CAAC;EACnBrB,OAAO,CAACW,QAAQ,CAAC,CAAC;AACtB;AACA,SAASS,aAAaA,CAACG,GAAG,EAAE;EACxB,MAAM;IAAEjB,GAAG;IAAEN;EAAQ,CAAC,GAAGuB,GAAG;EAC5BvB,OAAO,CAACQ,KAAK,CAACF,GAAG,CAAC;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
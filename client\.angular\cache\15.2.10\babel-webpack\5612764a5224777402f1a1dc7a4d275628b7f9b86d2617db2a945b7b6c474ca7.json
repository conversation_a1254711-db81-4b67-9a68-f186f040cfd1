{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin } from '@capacitor/core';\nexport class ScreenOrientationWeb extends WebPlugin {\n  constructor() {\n    super();\n    if (typeof screen !== 'undefined' && typeof screen.orientation !== 'undefined') {\n      screen.orientation.addEventListener('change', () => {\n        const type = screen.orientation.type;\n        this.notifyListeners('screenOrientationChange', {\n          type\n        });\n      });\n    }\n  }\n  orientation() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (typeof screen === 'undefined' || !screen.orientation) {\n        throw _this.unavailable('ScreenOrientation API not available in this browser');\n      }\n      return {\n        type: screen.orientation.type\n      };\n    })();\n  }\n  lock(options) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (typeof screen === 'undefined' || !screen.orientation || !screen.orientation.lock) {\n        throw _this2.unavailable('ScreenOrientation API not available in this browser');\n      }\n      try {\n        yield screen.orientation.lock(options.orientation);\n      } catch (_a) {\n        throw _this2.unavailable('ScreenOrientation API not available in this browser');\n      }\n    })();\n  }\n  unlock() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (typeof screen === 'undefined' || !screen.orientation || !screen.orientation.unlock) {\n        throw _this3.unavailable('ScreenOrientation API not available in this browser');\n      }\n      try {\n        screen.orientation.unlock();\n      } catch (_a) {\n        throw _this3.unavailable('ScreenOrientation API not available in this browser');\n      }\n    })();\n  }\n}", "map": {"version": 3, "names": ["WebPlugin", "ScreenOrientationWeb", "constructor", "screen", "orientation", "addEventListener", "type", "notifyListeners", "_this", "_asyncToGenerator", "unavailable", "lock", "options", "_this2", "_a", "unlock", "_this3"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/screen-orientation/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class ScreenOrientationWeb extends WebPlugin {\n    constructor() {\n        super();\n        if (typeof screen !== 'undefined' &&\n            typeof screen.orientation !== 'undefined') {\n            screen.orientation.addEventListener('change', () => {\n                const type = screen.orientation.type;\n                this.notifyListeners('screenOrientationChange', { type });\n            });\n        }\n    }\n    async orientation() {\n        if (typeof screen === 'undefined' || !screen.orientation) {\n            throw this.unavailable('ScreenOrientation API not available in this browser');\n        }\n        return { type: screen.orientation.type };\n    }\n    async lock(options) {\n        if (typeof screen === 'undefined' ||\n            !screen.orientation ||\n            !screen.orientation.lock) {\n            throw this.unavailable('ScreenOrientation API not available in this browser');\n        }\n        try {\n            await screen.orientation.lock(options.orientation);\n        }\n        catch (_a) {\n            throw this.unavailable('ScreenOrientation API not available in this browser');\n        }\n    }\n    async unlock() {\n        if (typeof screen === 'undefined' ||\n            !screen.orientation ||\n            !screen.orientation.unlock) {\n            throw this.unavailable('ScreenOrientation API not available in this browser');\n        }\n        try {\n            screen.orientation.unlock();\n        }\n        catch (_a) {\n            throw this.unavailable('ScreenOrientation API not available in this browser');\n        }\n    }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,oBAAoB,SAASD,SAAS,CAAC;EAChDE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,OAAOC,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW,EAAE;MAC3CD,MAAM,CAACC,WAAW,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QAChD,MAAMC,IAAI,GAAGH,MAAM,CAACC,WAAW,CAACE,IAAI;QACpC,IAAI,CAACC,eAAe,CAAC,yBAAyB,EAAE;UAAED;QAAK,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN;EACJ;EACMF,WAAWA,CAAA,EAAG;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,OAAON,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;QACtD,MAAMI,KAAI,CAACE,WAAW,CAAC,qDAAqD,CAAC;MACjF;MACA,OAAO;QAAEJ,IAAI,EAAEH,MAAM,CAACC,WAAW,CAACE;MAAK,CAAC;IAAC;EAC7C;EACMK,IAAIA,CAACC,OAAO,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAJ,iBAAA;MAChB,IAAI,OAAON,MAAM,KAAK,WAAW,IAC7B,CAACA,MAAM,CAACC,WAAW,IACnB,CAACD,MAAM,CAACC,WAAW,CAACO,IAAI,EAAE;QAC1B,MAAME,MAAI,CAACH,WAAW,CAAC,qDAAqD,CAAC;MACjF;MACA,IAAI;QACA,MAAMP,MAAM,CAACC,WAAW,CAACO,IAAI,CAACC,OAAO,CAACR,WAAW,CAAC;MACtD,CAAC,CACD,OAAOU,EAAE,EAAE;QACP,MAAMD,MAAI,CAACH,WAAW,CAAC,qDAAqD,CAAC;MACjF;IAAC;EACL;EACMK,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MACX,IAAI,OAAON,MAAM,KAAK,WAAW,IAC7B,CAACA,MAAM,CAACC,WAAW,IACnB,CAACD,MAAM,CAACC,WAAW,CAACW,MAAM,EAAE;QAC5B,MAAMC,MAAI,CAACN,WAAW,CAAC,qDAAqD,CAAC;MACjF;MACA,IAAI;QACAP,MAAM,CAACC,WAAW,CAACW,MAAM,CAAC,CAAC;MAC/B,CAAC,CACD,OAAOD,EAAE,EAAE;QACP,MAAME,MAAI,CAACN,WAAW,CAAC,qDAAqD,CAAC;MACjF;IAAC;EACL;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AdsVideoComponent {\n  static #_ = this.ɵfac = function AdsVideoComponent_Factory(t) {\n    return new (t || AdsVideoComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AdsVideoComponent,\n    selectors: [[\"app-ads-video\"]],\n    decls: 2,\n    vars: 0,\n    template: function AdsVideoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"ads-video works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAOA,OAAM,MAAOA,iBAAiB;EAAA,QAAAC,CAAA;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA;UAAjBF,iBAAiB;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP9BE,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,uBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI", "names": ["AdsVideoComponent", "_", "_2", "selectors", "decls", "vars", "template", "AdsVideoComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\ads-video\\ads-video.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\overlays\\ads-video\\ads-video.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-ads-video',\r\n  templateUrl: './ads-video.component.html',\r\n  styleUrls: ['./ads-video.component.scss']\r\n})\r\nexport class AdsVideoComponent {\r\n\r\n}\r\n", "<p>ads-video works!</p>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function window(windowBoundaries) {\n  return function windowOperatorFunction(source) {\n    return source.lift(new WindowOperator(windowBoundaries));\n  };\n}\nclass WindowOperator {\n  constructor(windowBoundaries) {\n    this.windowBoundaries = windowBoundaries;\n  }\n  call(subscriber, source) {\n    const windowSubscriber = new WindowSubscriber(subscriber);\n    const sourceSubscription = source.subscribe(windowSubscriber);\n    if (!sourceSubscription.closed) {\n      windowSubscriber.add(subscribeToResult(windowSubscriber, this.windowBoundaries));\n    }\n    return sourceSubscription;\n  }\n}\nclass WindowSubscriber extends OuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.window = new Subject();\n    destination.next(this.window);\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.openWindow();\n  }\n  notifyError(error, innerSub) {\n    this._error(error);\n  }\n  notifyComplete(innerSub) {\n    this._complete();\n  }\n  _next(value) {\n    this.window.next(value);\n  }\n  _error(err) {\n    this.window.error(err);\n    this.destination.error(err);\n  }\n  _complete() {\n    this.window.complete();\n    this.destination.complete();\n  }\n  _unsubscribe() {\n    this.window = null;\n  }\n  openWindow() {\n    const prevWindow = this.window;\n    if (prevWindow) {\n      prevWindow.complete();\n    }\n    const destination = this.destination;\n    const newWindow = this.window = new Subject();\n    destination.next(newWindow);\n  }\n}", "map": {"version": 3, "names": ["Subject", "OuterSubscriber", "subscribeToResult", "window", "windowBoundaries", "windowOperatorFunction", "source", "lift", "WindowOperator", "constructor", "call", "subscriber", "windowSubscriber", "WindowSubscriber", "sourceSubscription", "subscribe", "closed", "add", "destination", "next", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "openWindow", "notifyError", "error", "_error", "notifyComplete", "_complete", "_next", "value", "err", "complete", "_unsubscribe", "prevWindow", "newWindow"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/window.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function window(windowBoundaries) {\n    return function windowOperatorFunction(source) {\n        return source.lift(new WindowOperator(windowBoundaries));\n    };\n}\nclass WindowOperator {\n    constructor(windowBoundaries) {\n        this.windowBoundaries = windowBoundaries;\n    }\n    call(subscriber, source) {\n        const windowSubscriber = new WindowSubscriber(subscriber);\n        const sourceSubscription = source.subscribe(windowSubscriber);\n        if (!sourceSubscription.closed) {\n            windowSubscriber.add(subscribeToResult(windowSubscriber, this.windowBoundaries));\n        }\n        return sourceSubscription;\n    }\n}\nclass WindowSubscriber extends OuterSubscriber {\n    constructor(destination) {\n        super(destination);\n        this.window = new Subject();\n        destination.next(this.window);\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.openWindow();\n    }\n    notifyError(error, innerSub) {\n        this._error(error);\n    }\n    notifyComplete(innerSub) {\n        this._complete();\n    }\n    _next(value) {\n        this.window.next(value);\n    }\n    _error(err) {\n        this.window.error(err);\n        this.destination.error(err);\n    }\n    _complete() {\n        this.window.complete();\n        this.destination.complete();\n    }\n    _unsubscribe() {\n        this.window = null;\n    }\n    openWindow() {\n        const prevWindow = this.window;\n        if (prevWindow) {\n            prevWindow.complete();\n        }\n        const destination = this.destination;\n        const newWindow = this.window = new Subject();\n        destination.next(newWindow);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,MAAMA,CAACC,gBAAgB,EAAE;EACrC,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAE;IAC3C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,cAAc,CAACJ,gBAAgB,CAAC,CAAC;EAC5D,CAAC;AACL;AACA,MAAMI,cAAc,CAAC;EACjBC,WAAWA,CAACL,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAM,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,MAAMM,gBAAgB,GAAG,IAAIC,gBAAgB,CAACF,UAAU,CAAC;IACzD,MAAMG,kBAAkB,GAAGR,MAAM,CAACS,SAAS,CAACH,gBAAgB,CAAC;IAC7D,IAAI,CAACE,kBAAkB,CAACE,MAAM,EAAE;MAC5BJ,gBAAgB,CAACK,GAAG,CAACf,iBAAiB,CAACU,gBAAgB,EAAE,IAAI,CAACR,gBAAgB,CAAC,CAAC;IACpF;IACA,OAAOU,kBAAkB;EAC7B;AACJ;AACA,MAAMD,gBAAgB,SAASZ,eAAe,CAAC;EAC3CQ,WAAWA,CAACS,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;IAClB,IAAI,CAACf,MAAM,GAAG,IAAIH,OAAO,CAAC,CAAC;IAC3BkB,WAAW,CAACC,IAAI,CAAC,IAAI,CAAChB,MAAM,CAAC;EACjC;EACAiB,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAACC,KAAK,EAAEH,QAAQ,EAAE;IACzB,IAAI,CAACI,MAAM,CAACD,KAAK,CAAC;EACtB;EACAE,cAAcA,CAACL,QAAQ,EAAE;IACrB,IAAI,CAACM,SAAS,CAAC,CAAC;EACpB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAAC9B,MAAM,CAACgB,IAAI,CAACc,KAAK,CAAC;EAC3B;EACAJ,MAAMA,CAACK,GAAG,EAAE;IACR,IAAI,CAAC/B,MAAM,CAACyB,KAAK,CAACM,GAAG,CAAC;IACtB,IAAI,CAAChB,WAAW,CAACU,KAAK,CAACM,GAAG,CAAC;EAC/B;EACAH,SAASA,CAAA,EAAG;IACR,IAAI,CAAC5B,MAAM,CAACgC,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAAC,CAAC;EAC/B;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACjC,MAAM,GAAG,IAAI;EACtB;EACAuB,UAAUA,CAAA,EAAG;IACT,MAAMW,UAAU,GAAG,IAAI,CAAClC,MAAM;IAC9B,IAAIkC,UAAU,EAAE;MACZA,UAAU,CAACF,QAAQ,CAAC,CAAC;IACzB;IACA,MAAMjB,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMoB,SAAS,GAAG,IAAI,CAACnC,MAAM,GAAG,IAAIH,OAAO,CAAC,CAAC;IAC7CkB,WAAW,CAACC,IAAI,CAACmB,SAAS,CAAC;EAC/B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
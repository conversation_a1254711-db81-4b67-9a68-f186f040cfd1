{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function sequenceEqual(compareTo, comparator) {\n  return source => source.lift(new SequenceEqualOperator(compareTo, comparator));\n}\nexport class SequenceEqualOperator {\n  constructor(compareTo, comparator) {\n    this.compareTo = compareTo;\n    this.comparator = comparator;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new SequenceEqualSubscriber(subscriber, this.compareTo, this.comparator));\n  }\n}\nexport class SequenceEqualSubscriber extends Subscriber {\n  constructor(destination, compareTo, comparator) {\n    super(destination);\n    this.compareTo = compareTo;\n    this.comparator = comparator;\n    this._a = [];\n    this._b = [];\n    this._oneComplete = false;\n    this.destination.add(compareTo.subscribe(new SequenceEqualCompareToSubscriber(destination, this)));\n  }\n  _next(value) {\n    if (this._oneComplete && this._b.length === 0) {\n      this.emit(false);\n    } else {\n      this._a.push(value);\n      this.checkValues();\n    }\n  }\n  _complete() {\n    if (this._oneComplete) {\n      this.emit(this._a.length === 0 && this._b.length === 0);\n    } else {\n      this._oneComplete = true;\n    }\n    this.unsubscribe();\n  }\n  checkValues() {\n    const {\n      _a,\n      _b,\n      comparator\n    } = this;\n    while (_a.length > 0 && _b.length > 0) {\n      let a = _a.shift();\n      let b = _b.shift();\n      let areEqual = false;\n      try {\n        areEqual = comparator ? comparator(a, b) : a === b;\n      } catch (e) {\n        this.destination.error(e);\n      }\n      if (!areEqual) {\n        this.emit(false);\n      }\n    }\n  }\n  emit(value) {\n    const {\n      destination\n    } = this;\n    destination.next(value);\n    destination.complete();\n  }\n  nextB(value) {\n    if (this._oneComplete && this._a.length === 0) {\n      this.emit(false);\n    } else {\n      this._b.push(value);\n      this.checkValues();\n    }\n  }\n  completeB() {\n    if (this._oneComplete) {\n      this.emit(this._a.length === 0 && this._b.length === 0);\n    } else {\n      this._oneComplete = true;\n    }\n  }\n}\nclass SequenceEqualCompareToSubscriber extends Subscriber {\n  constructor(destination, parent) {\n    super(destination);\n    this.parent = parent;\n  }\n  _next(value) {\n    this.parent.nextB(value);\n  }\n  _error(err) {\n    this.parent.error(err);\n    this.unsubscribe();\n  }\n  _complete() {\n    this.parent.completeB();\n    this.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "sequenceEqual", "compareTo", "comparator", "source", "lift", "SequenceEqualOperator", "constructor", "call", "subscriber", "subscribe", "SequenceEqualSubscriber", "destination", "_a", "_b", "_oneComplete", "add", "SequenceEqualCompareToSubscriber", "_next", "value", "length", "emit", "push", "checkValues", "_complete", "unsubscribe", "a", "shift", "b", "areEqual", "e", "error", "next", "complete", "nextB", "completeB", "parent", "_error", "err"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/sequenceEqual.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function sequenceEqual(compareTo, comparator) {\n    return (source) => source.lift(new SequenceEqualOperator(compareTo, comparator));\n}\nexport class SequenceEqualOperator {\n    constructor(compareTo, comparator) {\n        this.compareTo = compareTo;\n        this.comparator = comparator;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SequenceEqualSubscriber(subscriber, this.compareTo, this.comparator));\n    }\n}\nexport class SequenceEqualSubscriber extends Subscriber {\n    constructor(destination, compareTo, comparator) {\n        super(destination);\n        this.compareTo = compareTo;\n        this.comparator = comparator;\n        this._a = [];\n        this._b = [];\n        this._oneComplete = false;\n        this.destination.add(compareTo.subscribe(new SequenceEqualCompareToSubscriber(destination, this)));\n    }\n    _next(value) {\n        if (this._oneComplete && this._b.length === 0) {\n            this.emit(false);\n        }\n        else {\n            this._a.push(value);\n            this.checkValues();\n        }\n    }\n    _complete() {\n        if (this._oneComplete) {\n            this.emit(this._a.length === 0 && this._b.length === 0);\n        }\n        else {\n            this._oneComplete = true;\n        }\n        this.unsubscribe();\n    }\n    checkValues() {\n        const { _a, _b, comparator } = this;\n        while (_a.length > 0 && _b.length > 0) {\n            let a = _a.shift();\n            let b = _b.shift();\n            let areEqual = false;\n            try {\n                areEqual = comparator ? comparator(a, b) : a === b;\n            }\n            catch (e) {\n                this.destination.error(e);\n            }\n            if (!areEqual) {\n                this.emit(false);\n            }\n        }\n    }\n    emit(value) {\n        const { destination } = this;\n        destination.next(value);\n        destination.complete();\n    }\n    nextB(value) {\n        if (this._oneComplete && this._a.length === 0) {\n            this.emit(false);\n        }\n        else {\n            this._b.push(value);\n            this.checkValues();\n        }\n    }\n    completeB() {\n        if (this._oneComplete) {\n            this.emit(this._a.length === 0 && this._b.length === 0);\n        }\n        else {\n            this._oneComplete = true;\n        }\n    }\n}\nclass SequenceEqualCompareToSubscriber extends Subscriber {\n    constructor(destination, parent) {\n        super(destination);\n        this.parent = parent;\n    }\n    _next(value) {\n        this.parent.nextB(value);\n    }\n    _error(err) {\n        this.parent.error(err);\n        this.unsubscribe();\n    }\n    _complete() {\n        this.parent.completeB();\n        this.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACjD,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,qBAAqB,CAACJ,SAAS,EAAEC,UAAU,CAAC,CAAC;AACpF;AACA,OAAO,MAAMG,qBAAqB,CAAC;EAC/BC,WAAWA,CAACL,SAAS,EAAEC,UAAU,EAAE;IAC/B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,uBAAuB,CAACF,UAAU,EAAE,IAAI,CAACP,SAAS,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;EACrG;AACJ;AACA,OAAO,MAAMQ,uBAAuB,SAASX,UAAU,CAAC;EACpDO,WAAWA,CAACK,WAAW,EAAEV,SAAS,EAAEC,UAAU,EAAE;IAC5C,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACV,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACU,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACH,WAAW,CAACI,GAAG,CAACd,SAAS,CAACQ,SAAS,CAAC,IAAIO,gCAAgC,CAACL,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;EACtG;EACAM,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,IAAI,CAACJ,YAAY,IAAI,IAAI,CAACD,EAAE,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3C,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC,MACI;MACD,IAAI,CAACR,EAAE,CAACS,IAAI,CAACH,KAAK,CAAC;MACnB,IAAI,CAACI,WAAW,CAAC,CAAC;IACtB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,YAAY,EAAE;MACnB,IAAI,CAACM,IAAI,CAAC,IAAI,CAACR,EAAE,CAACO,MAAM,KAAK,CAAC,IAAI,IAAI,CAACN,EAAE,CAACM,MAAM,KAAK,CAAC,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAACL,YAAY,GAAG,IAAI;IAC5B;IACA,IAAI,CAACU,WAAW,CAAC,CAAC;EACtB;EACAF,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEV,EAAE;MAAEC,EAAE;MAAEX;IAAW,CAAC,GAAG,IAAI;IACnC,OAAOU,EAAE,CAACO,MAAM,GAAG,CAAC,IAAIN,EAAE,CAACM,MAAM,GAAG,CAAC,EAAE;MACnC,IAAIM,CAAC,GAAGb,EAAE,CAACc,KAAK,CAAC,CAAC;MAClB,IAAIC,CAAC,GAAGd,EAAE,CAACa,KAAK,CAAC,CAAC;MAClB,IAAIE,QAAQ,GAAG,KAAK;MACpB,IAAI;QACAA,QAAQ,GAAG1B,UAAU,GAAGA,UAAU,CAACuB,CAAC,EAAEE,CAAC,CAAC,GAAGF,CAAC,KAAKE,CAAC;MACtD,CAAC,CACD,OAAOE,CAAC,EAAE;QACN,IAAI,CAAClB,WAAW,CAACmB,KAAK,CAACD,CAAC,CAAC;MAC7B;MACA,IAAI,CAACD,QAAQ,EAAE;QACX,IAAI,CAACR,IAAI,CAAC,KAAK,CAAC;MACpB;IACJ;EACJ;EACAA,IAAIA,CAACF,KAAK,EAAE;IACR,MAAM;MAAEP;IAAY,CAAC,GAAG,IAAI;IAC5BA,WAAW,CAACoB,IAAI,CAACb,KAAK,CAAC;IACvBP,WAAW,CAACqB,QAAQ,CAAC,CAAC;EAC1B;EACAC,KAAKA,CAACf,KAAK,EAAE;IACT,IAAI,IAAI,CAACJ,YAAY,IAAI,IAAI,CAACF,EAAE,CAACO,MAAM,KAAK,CAAC,EAAE;MAC3C,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC,MACI;MACD,IAAI,CAACP,EAAE,CAACQ,IAAI,CAACH,KAAK,CAAC;MACnB,IAAI,CAACI,WAAW,CAAC,CAAC;IACtB;EACJ;EACAY,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACpB,YAAY,EAAE;MACnB,IAAI,CAACM,IAAI,CAAC,IAAI,CAACR,EAAE,CAACO,MAAM,KAAK,CAAC,IAAI,IAAI,CAACN,EAAE,CAACM,MAAM,KAAK,CAAC,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAACL,YAAY,GAAG,IAAI;IAC5B;EACJ;AACJ;AACA,MAAME,gCAAgC,SAASjB,UAAU,CAAC;EACtDO,WAAWA,CAACK,WAAW,EAAEwB,MAAM,EAAE;IAC7B,KAAK,CAACxB,WAAW,CAAC;IAClB,IAAI,CAACwB,MAAM,GAAGA,MAAM;EACxB;EACAlB,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACiB,MAAM,CAACF,KAAK,CAACf,KAAK,CAAC;EAC5B;EACAkB,MAAMA,CAACC,GAAG,EAAE;IACR,IAAI,CAACF,MAAM,CAACL,KAAK,CAACO,GAAG,CAAC;IACtB,IAAI,CAACb,WAAW,CAAC,CAAC;EACtB;EACAD,SAASA,CAAA,EAAG;IACR,IAAI,CAACY,MAAM,CAACD,SAAS,CAAC,CAAC;IACvB,IAAI,CAACV,WAAW,CAAC,CAAC;EACtB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TranslateModuleComponent } from './translate-module.component';\nimport * as i0 from \"@angular/core\";\nexport class TranslateModuleModule {\n  static #_ = this.ɵfac = function TranslateModuleModule_Factory(t) {\n    return new (t || TranslateModuleModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TranslateModuleModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TranslateModuleModule, {\n    declarations: [TranslateModuleComponent],\n    imports: [CommonModule],\n    exports: [TranslateModuleComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,8BAA8B;;AAWvE,OAAM,MAAOC,qBAAqB;EAAA,QAAAC,CAAA;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA;UAArBF;EAAqB;EAAA,QAAAG,EAAA;cAJ9BL,YAAY;EAAA;;;2EAIHE,qBAAqB;IAAAI,YAAA,GANjBL,wBAAwB;IAAAM,OAAA,GAErCP,YAAY;IAAAQ,OAAA,GAEJP,wBAAwB;EAAA;AAAA", "names": ["CommonModule", "TranslateModuleComponent", "TranslateModuleModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\translate-module\\translate-module.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { TranslateModuleComponent } from './translate-module.component';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [TranslateModuleComponent],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [TranslateModuleComponent]\r\n})\r\nexport class TranslateModuleModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
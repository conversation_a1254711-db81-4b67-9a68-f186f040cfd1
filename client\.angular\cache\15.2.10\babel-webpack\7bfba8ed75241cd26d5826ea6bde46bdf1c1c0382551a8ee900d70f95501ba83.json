{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/flex-layout\";\nexport class CoreMediaService {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {MediaObserver} _mediaObserver\r\n   */\n  constructor(_mediaObserver) {\n    this._mediaObserver = _mediaObserver;\n    this.onMediaUpdate = new BehaviorSubject('');\n    // Set the defaults\n    this.currentMediaQuery = '';\n    // Initialize\n    this._init();\n  }\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Initialize\r\n   *\r\n   * @private\r\n   */\n  _init() {\n    this._mediaObserver.media$.pipe(debounceTime(500), distinctUntilChanged()).subscribe(change => {\n      // console.log('subscription: ', change);\n      if (this.currentMediaQuery !== change.mqAlias) {\n        this.currentMediaQuery = change.mqAlias;\n        this.onMediaUpdate.next(change.mqAlias);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function CoreMediaService_Factory(t) {\n    return new (t || CoreMediaService)(i0.ɵɵinject(i1.MediaObserver));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CoreMediaService,\n    factory: CoreMediaService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,eAAe,QAAQ,MAAM;AACtC,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;;;AAKnE,OAAM,MAAOC,gBAAgB;EAI3B;;;;;EAKAC,YAAoBC,cAA6B;IAA7B,KAAAA,cAAc,GAAdA,cAAc;IAPlC,KAAAC,aAAa,GAA4B,IAAIN,eAAe,CAAS,EAAE,CAAC;IAQtE;IACA,IAAI,CAACO,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAACC,KAAK,EAAE;EACd;EAEA;EACA;EAEA;;;;;EAKQA,KAAKA,CAAA;IACX,IAAI,CAACH,cAAc,CAACI,MAAM,CAACC,IAAI,CAACT,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAACS,SAAS,CAAEC,MAAmB,IAAI;MAC3G;MACA,IAAI,IAAI,CAACL,iBAAiB,KAAKK,MAAM,CAACC,OAAO,EAAE;QAC7C,IAAI,CAACN,iBAAiB,GAAGK,MAAM,CAACC,OAAO;QACvC,IAAI,CAACP,aAAa,CAACQ,IAAI,CAACF,MAAM,CAACC,OAAO,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAAC,QAAAE,CAAA;qBAjCUZ,gBAAgB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;WAAhBjB,gBAAgB;IAAAkB,OAAA,EAAhBlB,gBAAgB,CAAAmB,IAAA;IAAAC,UAAA,EAFf;EAAM", "names": ["BehaviorSubject", "debounceTime", "distinctUntilChanged", "CoreMediaService", "constructor", "_mediaObserver", "onMediaUpdate", "currentMediaQuery", "_init", "media$", "pipe", "subscribe", "change", "mq<PERSON><PERSON><PERSON>", "next", "_", "i0", "ɵɵinject", "i1", "MediaObserver", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\services\\media.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { MediaChange, MediaObserver } from '@angular/flex-layout';\r\n\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CoreMediaService {\r\n  currentMediaQuery: string;\r\n  onMediaUpdate: BehaviorSubject<string> = new BehaviorSubject<string>('');\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {MediaObserver} _mediaObserver\r\n   */\r\n  constructor(private _mediaObserver: MediaObserver) {\r\n    // Set the defaults\r\n    this.currentMediaQuery = '';\r\n\r\n    // Initialize\r\n    this._init();\r\n  }\r\n\r\n  // @ Private methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Initialize\r\n   *\r\n   * @private\r\n   */\r\n  private _init(): void {\r\n    this._mediaObserver.media$.pipe(debounceTime(500), distinctUntilChanged()).subscribe((change: MediaChange) => {\r\n      // console.log('subscription: ', change);\r\n      if (this.currentMediaQuery !== change.mqAlias) {\r\n        this.currentMediaQuery = change.mqAlias;\r\n        this.onMediaUpdate.next(change.mqAlias);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
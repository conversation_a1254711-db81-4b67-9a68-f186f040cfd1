{"ast": null, "code": "import { Observable } from '../Observable';\nexport function range(start = 0, count, scheduler) {\n  return new Observable(subscriber => {\n    if (count === undefined) {\n      count = start;\n      start = 0;\n    }\n    let index = 0;\n    let current = start;\n    if (scheduler) {\n      return scheduler.schedule(dispatch, 0, {\n        index,\n        count,\n        start,\n        subscriber\n      });\n    } else {\n      do {\n        if (index++ >= count) {\n          subscriber.complete();\n          break;\n        }\n        subscriber.next(current++);\n        if (subscriber.closed) {\n          break;\n        }\n      } while (true);\n    }\n    return undefined;\n  });\n}\nexport function dispatch(state) {\n  const {\n    start,\n    index,\n    count,\n    subscriber\n  } = state;\n  if (index >= count) {\n    subscriber.complete();\n    return;\n  }\n  subscriber.next(start);\n  if (subscriber.closed) {\n    return;\n  }\n  state.index = index + 1;\n  state.start = start + 1;\n  this.schedule(state);\n}", "map": {"version": 3, "names": ["Observable", "range", "start", "count", "scheduler", "subscriber", "undefined", "index", "current", "schedule", "dispatch", "complete", "next", "closed", "state"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/range.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function range(start = 0, count, scheduler) {\n    return new Observable(subscriber => {\n        if (count === undefined) {\n            count = start;\n            start = 0;\n        }\n        let index = 0;\n        let current = start;\n        if (scheduler) {\n            return scheduler.schedule(dispatch, 0, {\n                index, count, start, subscriber\n            });\n        }\n        else {\n            do {\n                if (index++ >= count) {\n                    subscriber.complete();\n                    break;\n                }\n                subscriber.next(current++);\n                if (subscriber.closed) {\n                    break;\n                }\n            } while (true);\n        }\n        return undefined;\n    });\n}\nexport function dispatch(state) {\n    const { start, index, count, subscriber } = state;\n    if (index >= count) {\n        subscriber.complete();\n        return;\n    }\n    subscriber.next(start);\n    if (subscriber.closed) {\n        return;\n    }\n    state.index = index + 1;\n    state.start = start + 1;\n    this.schedule(state);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,KAAKA,CAACC,KAAK,GAAG,CAAC,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC/C,OAAO,IAAIJ,UAAU,CAACK,UAAU,IAAI;IAChC,IAAIF,KAAK,KAAKG,SAAS,EAAE;MACrBH,KAAK,GAAGD,KAAK;MACbA,KAAK,GAAG,CAAC;IACb;IACA,IAAIK,KAAK,GAAG,CAAC;IACb,IAAIC,OAAO,GAAGN,KAAK;IACnB,IAAIE,SAAS,EAAE;MACX,OAAOA,SAAS,CAACK,QAAQ,CAACC,QAAQ,EAAE,CAAC,EAAE;QACnCH,KAAK;QAAEJ,KAAK;QAAED,KAAK;QAAEG;MACzB,CAAC,CAAC;IACN,CAAC,MACI;MACD,GAAG;QACC,IAAIE,KAAK,EAAE,IAAIJ,KAAK,EAAE;UAClBE,UAAU,CAACM,QAAQ,CAAC,CAAC;UACrB;QACJ;QACAN,UAAU,CAACO,IAAI,CAACJ,OAAO,EAAE,CAAC;QAC1B,IAAIH,UAAU,CAACQ,MAAM,EAAE;UACnB;QACJ;MACJ,CAAC,QAAQ,IAAI;IACjB;IACA,OAAOP,SAAS;EACpB,CAAC,CAAC;AACN;AACA,OAAO,SAASI,QAAQA,CAACI,KAAK,EAAE;EAC5B,MAAM;IAAEZ,KAAK;IAAEK,KAAK;IAAEJ,KAAK;IAAEE;EAAW,CAAC,GAAGS,KAAK;EACjD,IAAIP,KAAK,IAAIJ,KAAK,EAAE;IAChBE,UAAU,CAACM,QAAQ,CAAC,CAAC;IACrB;EACJ;EACAN,UAAU,CAACO,IAAI,CAACV,KAAK,CAAC;EACtB,IAAIG,UAAU,CAACQ,MAAM,EAAE;IACnB;EACJ;EACAC,KAAK,CAACP,KAAK,GAAGA,KAAK,GAAG,CAAC;EACvBO,KAAK,CAACZ,KAAK,GAAGA,KAAK,GAAG,CAAC;EACvB,IAAI,CAACO,QAAQ,CAACK,KAAK,CAAC;AACxB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
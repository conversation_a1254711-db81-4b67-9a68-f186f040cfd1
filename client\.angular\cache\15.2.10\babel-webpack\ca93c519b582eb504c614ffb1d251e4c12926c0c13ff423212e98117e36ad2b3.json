{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroupDirective } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i2 from \"app/services/sponsor.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ngx-formly/core\";\nimport * as i6 from \"@ngx-translate/core\";\nfunction ManageSponsorsEditorComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Update Sponsor\"), \" \");\n  }\n}\nfunction ManageSponsorsEditorComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Add New Sponsor\"), \" \");\n  }\n}\nexport class ManageSponsorsEditorComponent {\n  constructor(_coreSidebarService, _sponsorService) {\n    this._coreSidebarService = _coreSidebarService;\n    this._sponsorService = _sponsorService;\n    this.action = 'create';\n    this.sideBarID = 'manage-sponsors';\n    this.sideBarName = 'manage-sponsors';\n    this.emitSponsor = new EventEmitter();\n    this.onCloseEvent = new EventEmitter();\n    this.onOpenEvent = new EventEmitter();\n    this.isSubmitted = false;\n    this.triggerClickOverlay = false;\n    this.onClose = new EventEmitter();\n  }\n  ngOnChanges() {\n    if (this.action == 'create') {\n      this.model = {};\n    }\n  }\n  ngOnInit() {\n    this.triggerClickOverlay = false;\n    this._coreSidebarService.setOverlayClickEvent(this.sideBarID, this.onCloseEvent);\n    this._coreSidebarService.setOnOpenEvent(this.sideBarID, this.onOpenEvent);\n    this.onCloseEvent.subscribe(() => {\n      if (!this.triggerClickOverlay) {\n        this.triggerClickOverlay = true;\n        this.close();\n      }\n    });\n    this.onOpenEvent.subscribe(() => {\n      this.triggerClickOverlay = false;\n      this.isSubmitted = false;\n    });\n  }\n  reset() {\n    // reset form directives\n    this.form.reset();\n    this.model = {};\n    this.formDirective.resetForm();\n    // this.emitSponsor.emit(this.model);\n  }\n\n  close() {\n    this.onClose.emit({\n      isSubmitted: this.isSubmitted,\n      formValue: this.form.value,\n      action: this.action\n    });\n    this.reset();\n    this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen(false);\n    // this.emitSponsor.emit(this.model);\n  }\n  // ondestroy unsubscribe events\n  ngOnDestroy() {\n    this.onCloseEvent.unsubscribe();\n    this.onOpenEvent.unsubscribe();\n  }\n  onSubmit() {\n    // validate form\n    if (this.form.valid) {\n      switch (this.action) {\n        case 'edit':\n          this._sponsorService.updateSponsor(this.model).subscribe(res => {\n            this.isSubmitted = true;\n            this.close();\n            // this.emitSponsor.emit(this.model);\n          });\n\n          break;\n        case 'create':\n          this._sponsorService.createSponsor(this.model).subscribe(res => {\n            this.isSubmitted = true;\n            this.close();\n            // this.emitSponsor.emit(res);\n          });\n\n          break;\n        default:\n          Swal.fire({\n            icon: 'error',\n            title: 'Oops...',\n            text: `Action ${this.action} not found!`\n          });\n          break;\n      }\n    } else {\n      this.form.markAllAsTouched();\n    }\n  }\n  static #_ = this.ɵfac = function ManageSponsorsEditorComponent_Factory(t) {\n    return new (t || ManageSponsorsEditorComponent)(i0.ɵɵdirectiveInject(i1.CoreSidebarService), i0.ɵɵdirectiveInject(i2.SponsorService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ManageSponsorsEditorComponent,\n    selectors: [[\"app-manage-sponsors-editor\"]],\n    viewQuery: function ManageSponsorsEditorComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(FormGroupDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.formDirective = _t.first);\n      }\n    },\n    inputs: {\n      form: \"form\",\n      model: \"model\",\n      options: \"options\",\n      fields: \"fields\",\n      action: \"action\",\n      sideBarID: \"sideBarID\",\n      sideBarName: \"sideBarName\"\n    },\n    outputs: {\n      emitSponsor: \"emitSponsor\",\n      onClose: \"onClose\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 21,\n    vars: 13,\n    consts: [[1, \"slideout-content\"], [\"id\", \"modals-slide-in\", 1, \"modalsd\", \"modal-slide-in\", \"sdfade\", \"new-user-modal\"], [1, \"modal-dialog\", \"dialog-full\"], [1, \"modal-content\", \"pt-0\"], [1, \"modal-header\", \"mb-1\", \"text-primary\", \"text-center\"], [1, \"btn-back\", 3, \"click\"], [1, \"fa\", \"fa-chevron-left\"], [1, \"col\"], [1, \"mr-3\", \"h4\", \"title-editor\", \"text-capitalize\"], [4, \"ngIf\"], [1, \"modal-body\", \"no-margin\"], [3, \"formGroup\", \"ngSubmit\"], [\"formDirective\", \"ngForm\"], [3, \"model\", \"fields\", \"options\", \"form\"], [\"type\", \"submit\", \"data-testid\", \"btnSubmit\", 1, \"btn\", \"btn-primary\", \"btn-block\", \"round-10\"]],\n    template: function ManageSponsorsEditorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function ManageSponsorsEditorComponent_Template_div_click_5_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵelementStart(7, \"span\");\n        i0.ɵɵtext(8);\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"b\", 8);\n        i0.ɵɵtemplate(12, ManageSponsorsEditorComponent_span_12_Template, 3, 3, \"span\", 9);\n        i0.ɵɵtemplate(13, ManageSponsorsEditorComponent_span_13_Template, 3, 3, \"span\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(14, \"div\", 10)(15, \"form\", 11, 12);\n        i0.ɵɵlistener(\"ngSubmit\", function ManageSponsorsEditorComponent_Template_form_ngSubmit_15_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelement(17, \"formly-form\", 13);\n        i0.ɵɵelementStart(18, \"button\", 14);\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 9, \"Back\"), \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.action == \"edit\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.action == \"create\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"model\", ctx.model)(\"fields\", ctx.fields)(\"options\", ctx.options)(\"form\", ctx.form);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 11, \"Submit\"), \" \");\n      }\n    },\n    dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, i5.FormlyForm, i6.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAEEA,YAAY,QAIP,eAAe;AACtB,SAAoBC,kBAAkB,QAAQ,gBAAgB;AAK9D,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;ICDFC,EAAA,CAAAC,cAAA,WAA6B;IACzBD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IADHH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,8BACJ;;;;;IACAN,EAAA,CAAAC,cAAA,WAA+B;IAC3BD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IADHH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,+BACJ;;;ADG5B,OAAM,MAAOC,6BAA6B;EAqBxCC,YACUC,mBAAuC,EACvCC,eAA+B;IAD/B,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IAhBhB,KAAAC,MAAM,GAAG,QAAQ;IAEjB,KAAAC,SAAS,GAAW,iBAAiB;IACrC,KAAAC,WAAW,GAAW,iBAAiB;IACtC,KAAAC,WAAW,GAA0B,IAAIjB,YAAY,EAAE;IAE1D,KAAAkB,YAAY,GAAG,IAAIlB,YAAY,EAAE;IACjC,KAAAmB,WAAW,GAAG,IAAInB,YAAY,EAAE;IAE/B,KAAAoB,WAAW,GAAY,KAAK;IAC5B,KAAAC,mBAAmB,GAAY,KAAK;IAElC,KAAAC,OAAO,GAAsB,IAAItB,YAAY,EAAE;EAKtD;EAEHuB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACT,MAAM,IAAI,QAAQ,EAAE;MAC3B,IAAI,CAACU,KAAK,GAAG,EAAE;;EAEnB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACJ,mBAAmB,GAAG,KAAK;IAEhC,IAAI,CAACT,mBAAmB,CAACc,oBAAoB,CAC3C,IAAI,CAACX,SAAS,EACd,IAAI,CAACG,YAAY,CAClB;IAED,IAAI,CAACN,mBAAmB,CAACe,cAAc,CAAC,IAAI,CAACZ,SAAS,EAAE,IAAI,CAACI,WAAW,CAAC;IAEzE,IAAI,CAACD,YAAY,CAACU,SAAS,CAAC,MAAK;MAC/B,IAAI,CAAC,IAAI,CAACP,mBAAmB,EAAE;QAC7B,IAAI,CAACA,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAACQ,KAAK,EAAE;;IAEhB,CAAC,CAAC;IAEF,IAAI,CAACV,WAAW,CAACS,SAAS,CAAC,MAAK;MAC9B,IAAI,CAACP,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACD,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;EACJ;EAEAU,KAAKA,CAAA;IACH;IAEA,IAAI,CAACC,IAAI,CAACD,KAAK,EAAE;IACjB,IAAI,CAACN,KAAK,GAAG,EAAE;IACf,IAAI,CAACQ,aAAa,CAACC,SAAS,EAAE;IAC9B;EACF;;EAEAJ,KAAKA,CAAA;IACH,IAAI,CAACP,OAAO,CAACY,IAAI,CAAC;MAChBd,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7Be,SAAS,EAAE,IAAI,CAACJ,IAAI,CAACK,KAAK;MAC1BtB,MAAM,EAAE,IAAI,CAACA;KACd,CAAC;IAEF,IAAI,CAACgB,KAAK,EAAE;IAEZ,IAAI,CAAClB,mBAAmB,CACrByB,kBAAkB,CAAC,IAAI,CAACtB,SAAS,CAAC,CAClCuB,UAAU,CAAC,KAAK,CAAC;IACpB;EACF;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACrB,YAAY,CAACsB,WAAW,EAAE;IAC/B,IAAI,CAACrB,WAAW,CAACqB,WAAW,EAAE;EAChC;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACV,IAAI,CAACW,KAAK,EAAE;MACnB,QAAQ,IAAI,CAAC5B,MAAM;QACjB,KAAK,MAAM;UACT,IAAI,CAACD,eAAe,CAAC8B,aAAa,CAAC,IAAI,CAACnB,KAAK,CAAC,CAACI,SAAS,CAAEgB,GAAG,IAAI;YAC/D,IAAI,CAACxB,WAAW,GAAG,IAAI;YACvB,IAAI,CAACS,KAAK,EAAE;YACZ;UACF,CAAC,CAAC;;UACF;QACF,KAAK,QAAQ;UACX,IAAI,CAAChB,eAAe,CAACgC,aAAa,CAAC,IAAI,CAACrB,KAAK,CAAC,CAACI,SAAS,CAAEgB,GAAG,IAAI;YAC/D,IAAI,CAACxB,WAAW,GAAG,IAAI;YACvB,IAAI,CAACS,KAAK,EAAE;YACZ;UACF,CAAC,CAAC;;UACF;QACF;UACE3B,IAAI,CAAC4C,IAAI,CAAC;YACRC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE,UAAU,IAAI,CAACnC,MAAM;WAC5B,CAAC;UACF;;KAEL,MAAM;MACL,IAAI,CAACiB,IAAI,CAACmB,gBAAgB,EAAE;;EAEhC;EAAC,QAAAC,CAAA;qBAlHUzC,6BAA6B,EAAAP,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAnD,EAAA,CAAAiD,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAA7B/C,6BAA6B;IAAAgD,SAAA;IAAAC,SAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAM7B5D,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;QCzB/BE,EAAA,CAAAC,cAAA,aAA8B;QAKYD,EAAA,CAAA4D,UAAA,mBAAAC,4DAAA;UAAA,OAASF,GAAA,CAAAjC,KAAA,EAAO;QAAA,EAAC;QACnC1B,EAAA,CAAA8D,SAAA,WAAkC;QAClC9D,EAAA,CAAAC,cAAA,WAAM;QAACD,EAAA,CAAAE,MAAA,GAAwB;;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAE1CH,EAAA,CAAAC,cAAA,cAAiB;QAETD,EAAA,CAAA+D,UAAA,KAAAC,8CAAA,kBAEO;QACPhE,EAAA,CAAA+D,UAAA,KAAAE,8CAAA,kBAEO;QACXjE,EAAA,CAAAG,YAAA,EAAI;QAIZH,EAAA,CAAAC,cAAA,eAAkC;QACLD,EAAA,CAAA4D,UAAA,sBAAAM,iEAAA;UAAA,OAAYP,GAAA,CAAArB,QAAA,EAAU;QAAA,EAAC;QAC5CtC,EAAA,CAAA8D,SAAA,uBAA+F;QAC/F9D,EAAA,CAAAC,cAAA,kBAAyF;QACrFD,EAAA,CAAAE,MAAA,IACJ;;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAnBFH,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,mBAAwB;QAIpBN,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAmE,UAAA,SAAAR,GAAA,CAAAhD,MAAA,WAAoB;QAGpBX,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAmE,UAAA,SAAAR,GAAA,CAAAhD,MAAA,aAAsB;QAQ/BX,EAAA,CAAAI,SAAA,GAAkB;QAAlBJ,EAAA,CAAAmE,UAAA,cAAAR,GAAA,CAAA/B,IAAA,CAAkB;QACP5B,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAmE,UAAA,UAAAR,GAAA,CAAAtC,KAAA,CAAe,WAAAsC,GAAA,CAAAS,MAAA,aAAAT,GAAA,CAAAU,OAAA,UAAAV,GAAA,CAAA/B,IAAA;QAExB5B,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,wBACJ", "names": ["EventEmitter", "FormGroupDirective", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ManageSponsorsEditorComponent", "constructor", "_coreSidebarService", "_sponsorService", "action", "sideBarID", "sideBarName", "emitSponsor", "onCloseEvent", "onOpenEvent", "isSubmitted", "triggerClickOverlay", "onClose", "ngOnChanges", "model", "ngOnInit", "setOverlayClickEvent", "setOnOpenEvent", "subscribe", "close", "reset", "form", "formDirective", "resetForm", "emit", "formValue", "value", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "onSubmit", "valid", "updateSponsor", "res", "createSponsor", "fire", "icon", "title", "text", "mark<PERSON>llAsTouched", "_", "ɵɵdirectiveInject", "i1", "CoreSidebarService", "i2", "SponsorService", "_2", "selectors", "viewQuery", "ManageSponsorsEditorComponent_Query", "rf", "ctx", "ɵɵlistener", "ManageSponsorsEditorComponent_Template_div_click_5_listener", "ɵɵelement", "ɵɵtemplate", "ManageSponsorsEditorComponent_span_12_Template", "ManageSponsorsEditorComponent_span_13_Template", "ManageSponsorsEditorComponent_Template_form_ngSubmit_15_listener", "ɵɵproperty", "fields", "options"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\manage-sponsors\\manage-sponsors-editor\\manage-sponsors-editor.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\manage-sponsors\\manage-sponsors-editor\\manage-sponsors-editor.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  Output,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { FormGroup, FormGroupDirective } from '@angular/forms';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { Sponsor } from 'app/interfaces/sponsor';\r\nimport { SponsorService } from 'app/services/sponsor.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-manage-sponsors-editor',\r\n  templateUrl: './manage-sponsors-editor.component.html',\r\n  styleUrls: ['./manage-sponsors-editor.component.scss'],\r\n})\r\nexport class ManageSponsorsEditorComponent {\r\n  @Input() form: FormGroup;\r\n  @Input() model: any;\r\n  @Input() options: FormlyFormOptions;\r\n  @Input() fields: FormlyFieldConfig[];\r\n\r\n  @ViewChild(FormGroupDirective) formDirective: FormGroupDirective;\r\n  @Input() action = 'create';\r\n\r\n  @Input() sideBarID: string = 'manage-sponsors';\r\n  @Input() sideBarName: string = 'manage-sponsors';\r\n  @Output() emitSponsor: EventEmitter<Sponsor> = new EventEmitter();\r\n\r\n  public onCloseEvent = new EventEmitter();\r\n  public onOpenEvent = new EventEmitter();\r\n\r\n  private isSubmitted: boolean = false;\r\n  private triggerClickOverlay: boolean = false;\r\n\r\n  @Output() onClose: EventEmitter<any> = new EventEmitter();\r\n\r\n  constructor(\r\n    private _coreSidebarService: CoreSidebarService,\r\n    private _sponsorService: SponsorService\r\n  ) {}\r\n\r\n  ngOnChanges() {\r\n    if (this.action == 'create') {\r\n      this.model = {};\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.triggerClickOverlay = false;\r\n\r\n    this._coreSidebarService.setOverlayClickEvent(\r\n      this.sideBarID,\r\n      this.onCloseEvent\r\n    );\r\n\r\n    this._coreSidebarService.setOnOpenEvent(this.sideBarID, this.onOpenEvent);\r\n\r\n    this.onCloseEvent.subscribe(() => {\r\n      if (!this.triggerClickOverlay) {\r\n        this.triggerClickOverlay = true;\r\n        this.close();\r\n      }\r\n    });\r\n\r\n    this.onOpenEvent.subscribe(() => {\r\n      this.triggerClickOverlay = false;\r\n      this.isSubmitted = false;\r\n    });\r\n  }\r\n\r\n  reset() {\r\n    // reset form directives\r\n\r\n    this.form.reset();\r\n    this.model = {};\r\n    this.formDirective.resetForm();\r\n    // this.emitSponsor.emit(this.model);\r\n  }\r\n\r\n  close() {\r\n    this.onClose.emit({\r\n      isSubmitted: this.isSubmitted,\r\n      formValue: this.form.value,\r\n      action: this.action,\r\n    });\r\n\r\n    this.reset();\r\n\r\n    this._coreSidebarService\r\n      .getSidebarRegistry(this.sideBarID)\r\n      .toggleOpen(false);\r\n    // this.emitSponsor.emit(this.model);\r\n  }\r\n\r\n  // ondestroy unsubscribe events\r\n  ngOnDestroy() {\r\n    this.onCloseEvent.unsubscribe();\r\n    this.onOpenEvent.unsubscribe();\r\n  }\r\n\r\n  onSubmit() {\r\n    // validate form\r\n    if (this.form.valid) {\r\n      switch (this.action) {\r\n        case 'edit':\r\n          this._sponsorService.updateSponsor(this.model).subscribe((res) => {\r\n            this.isSubmitted = true;\r\n            this.close();\r\n            // this.emitSponsor.emit(this.model);\r\n          });\r\n          break;\r\n        case 'create':\r\n          this._sponsorService.createSponsor(this.model).subscribe((res) => {\r\n            this.isSubmitted = true;\r\n            this.close();\r\n            // this.emitSponsor.emit(res);\r\n          });\r\n          break;\r\n        default:\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Oops...',\r\n            text: `Action ${this.action} not found!`,\r\n          });\r\n          break;\r\n      }\r\n    } else {\r\n      this.form.markAllAsTouched();\r\n    }\r\n  }\r\n}\r\n", "<div class=\"slideout-content\">\r\n    <div class=\"modalsd modal-slide-in sdfade new-user-modal\" id=\"modals-slide-in\">\r\n        <div class=\"modal-dialog dialog-full\">\r\n            <div class=\"modal-content pt-0\">\r\n                <div class=\"modal-header mb-1 text-primary text-center\">\r\n                    <div class=\"btn-back\" (click)=\"close()\">\r\n                        <i class=\"fa fa-chevron-left\"></i>\r\n                        <span> {{ 'Back' | translate }}</span>\r\n                    </div>\r\n                    <div class=\"col\">\r\n                        <b class=\"mr-3 h4 title-editor text-capitalize\">\r\n                            <span *ngIf=\"action=='edit'\">\r\n                                {{ 'Update Sponsor' | translate }}\r\n                            </span>\r\n                            <span *ngIf=\"action=='create'\">\r\n                                {{ 'Add New Sponsor' | translate }}\r\n                            </span>\r\n                        </b>\r\n\r\n                    </div>\r\n                </div>\r\n                <div class=\"modal-body no-margin\">\r\n                    <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" #formDirective=\"ngForm\">\r\n                        <formly-form [model]=\"model\" [fields]=\"fields\" [options]=\"options\" [form]=\"form\"></formly-form>\r\n                        <button type=\"submit\" class=\"btn btn-primary btn-block round-10\" data-testid=\"btnSubmit\">\r\n                            {{ 'Submit' | translate }}\r\n                        </button>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
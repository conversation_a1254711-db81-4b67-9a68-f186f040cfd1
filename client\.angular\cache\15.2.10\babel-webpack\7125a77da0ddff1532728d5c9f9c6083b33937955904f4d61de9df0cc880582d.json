{"ast": null, "code": "const EPS = 1e-7;\nconst MAX_ITER = 20;\nconst {\n  pow,\n  min,\n  max\n} = Math;\nclass Color {\n  constructor(...args) {\n    if (args.length < 3 && args.length > 0) {\n      this._color = bigIntToRgb(args[0], args[1]);\n    } else if (args.length > 2) {\n      this._color = args;\n      // Set default alpha\n      if (args.length === 3) {\n        this._color[3] = 1;\n      }\n    } else {\n      this._color = [];\n    }\n  }\n  rgba() {\n    return this._color.slice(0);\n  }\n  alpha(value) {\n    if (value === void 0) {\n      return this._color[3];\n    }\n    // Clone\n    const _color = this.rgba();\n    // Set alpha\n    _color[3] = value;\n    return new Color(..._color);\n  }\n  luminance(lum) {\n    if (lum === void 0) {\n      return rgbToLuminance(...this._color);\n    }\n    if (lum === 0) {\n      // return pure black\n      return new Color(0, 0, 0, this._color[3]);\n    }\n    if (lum === 1) {\n      // return pure white\n      return new Color(255, 255, 255, this._color[3]);\n    }\n    const relativeLuminance = this.luminance();\n    let max_iter = MAX_ITER;\n    const test = (low, high) => {\n      const mid = new Color(...interpolateRgb(low.rgba(), high.rgba(), 0.5));\n      const lm = mid.luminance();\n      if (Math.abs(lum - lm) < EPS || !max_iter--) {\n        return mid;\n      }\n      return lm > lum ? test(low, mid) : test(mid, high);\n    };\n    const rgb = (relativeLuminance > lum ? test(new Color(0, 0, 0), this) : test(this, new Color(255, 255, 255))).rgba();\n    rgb.pop();\n    rgb.push(this._color[3]);\n    return new Color(...rgb);\n  }\n  /**\n   * Changes the saturation of a color by manipulating the Lch chromaticity.\n   * @param amount default: 1\n   */\n  saturate(amount = 1) {\n    const lab = rgbToLab(this._color);\n    const lch = labToLch(lab);\n    lch[1] += 18 * amount;\n    if (lch[1] < 0) {\n      lch[1] = 0;\n    }\n    const labFromLch = lchToLab(lch);\n    const xyzFromLab = labToXyz(labFromLch);\n    const rgb = xyzToRgb(xyzFromLab);\n    // Set alpha\n    rgb.push(this._color[3]);\n    return new Color(...rgb);\n  }\n  /**\n   * Similar to saturate, but the opposite direction.\n   * @param amount default: 1\n   */\n  desaturate(amount = 1) {\n    return this.saturate(-amount);\n  }\n  /**\n   * @param amount default: 1\n   */\n  darken(amount = 1) {\n    const lab = rgbToLab(this._color);\n    lab[0] -= 18 * amount;\n    const xyzFromLab = labToXyz(lab);\n    const rgb = xyzToRgb(xyzFromLab);\n    // Set alpha\n    rgb.push(this._color[3]);\n    return new Color(...rgb);\n  }\n  /**\n   * The opposite of darken\n   * @param amount default 1\n   */\n  brighten(amount = 1) {\n    return this.darken(-amount);\n  }\n  css() {\n    if (!this._color.length) {\n      return 'undefined - invalid color';\n    }\n    return rgbToCss(this.rgba());\n  }\n  toString() {\n    return this.css();\n  }\n}\n// /**\n//  * Convert number to CSS\n//  * 0x00bcd4 > #00bcd4\n//  * @param int Int\n//  */\n// function bigIntToCss(int: number) {\n//   const hex = int.toString(16);\n//   return '#000000'.substring(0, 7 - hex.length) + hex;\n// }\nfunction rgbToCss(rgb) {\n  const alpha = rgb.pop();\n  if (alpha === 1) {\n    return `rgb(${rgb.map(Math.round).join()})`;\n  }\n  return `rgba(${rgb.map(Math.round).join()},${alpha})`;\n}\nfunction bigIntToRgb(bigInt, alpha = 1) {\n  // if (bigInt < 0x1000) {\n  //   bigInt = parseInt(bigInt.toString(16).split('').map(char => {\n  //     return char + char;\n  //   }).join(''), 16);\n  // }\n  // tslint:disable-next-line: no-bitwise\n  const red = bigInt >> 16 & 0xff;\n  // tslint:disable-next-line: no-bitwise\n  const green = bigInt >> 8 & 0xff;\n  // tslint:disable-next-line: no-bitwise\n  const blue = bigInt & 0xff;\n  return [red, green, blue, alpha];\n}\n// function rgbToBigInt(r: number, g: number, b: number) {\n//   // tslint:disable-next-line: no-bitwise\n//   return (r << 16) + (g << 8) + b;\n// }\nfunction rgbToXyz(rgb) {\n  let r = rgb[0] / 255;\n  let g = rgb[1] / 255;\n  let b = rgb[2] / 255;\n  // Assume sRGB\n  r = r > 0.04045 ? ((r + 0.055) / 1.055) ** 2.4 : r / 12.92;\n  g = g > 0.04045 ? ((g + 0.055) / 1.055) ** 2.4 : g / 12.92;\n  b = b > 0.04045 ? ((b + 0.055) / 1.055) ** 2.4 : b / 12.92;\n  const x = r * 0.4124 + g * 0.3576 + b * 0.1805;\n  const y = r * 0.2126 + g * 0.7152 + b * 0.0722;\n  const z = r * 0.0193 + g * 0.1192 + b * 0.9505;\n  return [x * 100, y * 100, z * 100];\n}\nfunction rgbToLab(rgb) {\n  const xyz = rgbToXyz(rgb);\n  let x = xyz[0];\n  let y = xyz[1];\n  let z = xyz[2];\n  x /= 95.047;\n  y /= 100;\n  z /= 108.883;\n  x = x > 0.008856 ? x ** (1 / 3) : 7.787 * x + 16 / 116;\n  y = y > 0.008856 ? y ** (1 / 3) : 7.787 * y + 16 / 116;\n  z = z > 0.008856 ? z ** (1 / 3) : 7.787 * z + 16 / 116;\n  const l = 116 * y - 16;\n  const a = 500 * (x - y);\n  const b = 200 * (y - z);\n  return [l, a, b];\n}\nfunction labToLch(lab) {\n  const l = lab[0];\n  const a = lab[1];\n  const b = lab[2];\n  let h;\n  const hr = Math.atan2(b, a);\n  h = hr * 360 / 2 / Math.PI;\n  if (h < 0) {\n    h += 360;\n  }\n  const c = Math.sqrt(a * a + b * b);\n  return [l, c, h];\n}\nfunction lchToLab(lch) {\n  const l = lch[0];\n  const c = lch[1];\n  const h = lch[2];\n  const hr = h / 360 * 2 * Math.PI;\n  const a = c * Math.cos(hr);\n  const b = c * Math.sin(hr);\n  return [l, a, b];\n}\nfunction labToXyz(lab) {\n  const l = lab[0];\n  const a = lab[1];\n  const b = lab[2];\n  let x;\n  let y;\n  let z;\n  y = (l + 16) / 116;\n  x = a / 500 + y;\n  z = y - b / 200;\n  const y2 = y ** 3;\n  const x2 = x ** 3;\n  const z2 = z ** 3;\n  y = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n  x = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n  z = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n  x *= 95.047;\n  y *= 100;\n  z *= 108.883;\n  return [x, y, z];\n}\nfunction xyzToRgb(xyz) {\n  const x = xyz[0] / 100;\n  const y = xyz[1] / 100;\n  const z = xyz[2] / 100;\n  let r;\n  let g;\n  let b;\n  r = x * 3.2406 + y * -1.5372 + z * -0.4986;\n  g = x * -0.9689 + y * 1.8758 + z * 0.0415;\n  b = x * 0.0557 + y * -0.2040 + z * 1.0570;\n  // Assume sRGB\n  r = r > 0.0031308 ? 1.055 * r ** (1.0 / 2.4) - 0.055 : r * 12.92;\n  g = g > 0.0031308 ? 1.055 * g ** (1.0 / 2.4) - 0.055 : g * 12.92;\n  b = b > 0.0031308 ? 1.055 * b ** (1.0 / 2.4) - 0.055 : b * 12.92;\n  r = min(max(0, r), 1);\n  g = min(max(0, g), 1);\n  b = min(max(0, b), 1);\n  return [r * 255, g * 255, b * 255];\n}\nfunction rgbToLuminance(r, g, b) {\n  // Relative luminance\n  // See http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n  r = luminance_channel(r);\n  g = luminance_channel(g);\n  b = luminance_channel(b);\n  return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n}\nfunction luminance_channel(x) {\n  x /= 255;\n  return x <= 0.03928 ? x / 12.92 : pow((x + 0.055) / 1.055, 2.4);\n}\nfunction interpolateRgb(rgb1, rgb2, f = 0.5) {\n  return [rgb1[0] + f * (rgb2[0] - rgb1[0]), rgb1[1] + f * (rgb2[1] - rgb1[1]), rgb1[2] + f * (rgb2[2] - rgb1[2])];\n}\nfunction hexColorToInt(_color) {\n  if (_color.startsWith('#')) {\n    return parseInt(_color.slice(1), 16);\n  }\n  throw new Error(`Expected to start with '#' the given value is: ${_color}`);\n}\nfunction color(...args) {\n  return new Color(...args);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Color, color, hexColorToInt };", "map": {"version": 3, "names": ["EPS", "MAX_ITER", "pow", "min", "max", "Math", "Color", "constructor", "args", "length", "_color", "bigIntToRgb", "rgba", "slice", "alpha", "value", "luminance", "lum", "rgbToLuminance", "relativeLuminance", "max_iter", "test", "low", "high", "mid", "interpolateRgb", "lm", "abs", "rgb", "pop", "push", "saturate", "amount", "lab", "rgbToLab", "lch", "labToLch", "labFromLch", "lchToLab", "xyzFromLab", "labToXyz", "xyzToRgb", "desaturate", "darken", "brighten", "css", "rgbToCss", "toString", "map", "round", "join", "bigInt", "red", "green", "blue", "rgbToXyz", "r", "g", "b", "x", "y", "z", "xyz", "l", "a", "h", "hr", "atan2", "PI", "c", "sqrt", "cos", "sin", "y2", "x2", "z2", "luminance_channel", "rgb1", "rgb2", "f", "hexColorToInt", "startsWith", "parseInt", "Error", "color"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@alyle/ui/fesm2020/alyle-ui-color.mjs"], "sourcesContent": ["const EPS = 1e-7;\nconst MAX_ITER = 20;\nconst { pow, min, max } = Math;\nclass Color {\n    constructor(...args) {\n        if (args.length < 3 && args.length > 0) {\n            this._color = bigIntToRgb(args[0], args[1]);\n        }\n        else if (args.length > 2) {\n            this._color = args;\n            // Set default alpha\n            if (args.length === 3) {\n                this._color[3] = 1;\n            }\n        }\n        else {\n            this._color = [];\n        }\n    }\n    rgba() {\n        return this._color.slice(0);\n    }\n    alpha(value) {\n        if (value === void 0) {\n            return this._color[3];\n        }\n        // Clone\n        const _color = this.rgba();\n        // Set alpha\n        _color[3] = value;\n        return new Color(..._color);\n    }\n    luminance(lum) {\n        if (lum === void 0) {\n            return rgbToLuminance(...this._color);\n        }\n        if (lum === 0) {\n            // return pure black\n            return new Color(0, 0, 0, this._color[3]);\n        }\n        if (lum === 1) {\n            // return pure white\n            return new Color(255, 255, 255, this._color[3]);\n        }\n        const relativeLuminance = this.luminance();\n        let max_iter = MAX_ITER;\n        const test = (low, high) => {\n            const mid = new Color(...interpolateRgb(low.rgba(), high.rgba(), 0.5));\n            const lm = mid.luminance();\n            if (Math.abs(lum - lm) < EPS || !max_iter--) {\n                return mid;\n            }\n            return lm > lum ? test(low, mid) : test(mid, high);\n        };\n        const rgb = (relativeLuminance > lum\n            ? test(new Color(0, 0, 0), this)\n            : test(this, new Color(255, 255, 255))).rgba();\n        rgb.pop();\n        rgb.push(this._color[3]);\n        return new Color(...rgb);\n    }\n    /**\n     * Changes the saturation of a color by manipulating the Lch chromaticity.\n     * @param amount default: 1\n     */\n    saturate(amount = 1) {\n        const lab = rgbToLab(this._color);\n        const lch = labToLch(lab);\n        lch[1] += 18 * amount;\n        if (lch[1] < 0) {\n            lch[1] = 0;\n        }\n        const labFromLch = lchToLab(lch);\n        const xyzFromLab = labToXyz(labFromLch);\n        const rgb = xyzToRgb(xyzFromLab);\n        // Set alpha\n        rgb.push(this._color[3]);\n        return new Color(...rgb);\n    }\n    /**\n     * Similar to saturate, but the opposite direction.\n     * @param amount default: 1\n     */\n    desaturate(amount = 1) {\n        return this.saturate(-amount);\n    }\n    /**\n     * @param amount default: 1\n     */\n    darken(amount = 1) {\n        const lab = rgbToLab(this._color);\n        lab[0] -= 18 * amount;\n        const xyzFromLab = labToXyz(lab);\n        const rgb = xyzToRgb(xyzFromLab);\n        // Set alpha\n        rgb.push(this._color[3]);\n        return new Color(...rgb);\n    }\n    /**\n     * The opposite of darken\n     * @param amount default 1\n     */\n    brighten(amount = 1) {\n        return this.darken(-amount);\n    }\n    css() {\n        if (!this._color.length) {\n            return 'undefined - invalid color';\n        }\n        return rgbToCss(this.rgba());\n    }\n    toString() {\n        return this.css();\n    }\n}\n// /**\n//  * Convert number to CSS\n//  * 0x00bcd4 > #00bcd4\n//  * @param int Int\n//  */\n// function bigIntToCss(int: number) {\n//   const hex = int.toString(16);\n//   return '#000000'.substring(0, 7 - hex.length) + hex;\n// }\nfunction rgbToCss(rgb) {\n    const alpha = rgb.pop();\n    if (alpha === 1) {\n        return `rgb(${rgb.map(Math.round).join()})`;\n    }\n    return `rgba(${rgb.map(Math.round).join()},${alpha})`;\n}\nfunction bigIntToRgb(bigInt, alpha = 1) {\n    // if (bigInt < 0x1000) {\n    //   bigInt = parseInt(bigInt.toString(16).split('').map(char => {\n    //     return char + char;\n    //   }).join(''), 16);\n    // }\n    // tslint:disable-next-line: no-bitwise\n    const red = (bigInt >> 16) & 0xff;\n    // tslint:disable-next-line: no-bitwise\n    const green = (bigInt >> 8) & 0xff;\n    // tslint:disable-next-line: no-bitwise\n    const blue = bigInt & 0xff;\n    return [red, green, blue, alpha];\n}\n// function rgbToBigInt(r: number, g: number, b: number) {\n//   // tslint:disable-next-line: no-bitwise\n//   return (r << 16) + (g << 8) + b;\n// }\nfunction rgbToXyz(rgb) {\n    let r = rgb[0] / 255;\n    let g = rgb[1] / 255;\n    let b = rgb[2] / 255;\n    // Assume sRGB\n    r = r > 0.04045 ? (((r + 0.055) / 1.055) ** 2.4) : (r / 12.92);\n    g = g > 0.04045 ? (((g + 0.055) / 1.055) ** 2.4) : (g / 12.92);\n    b = b > 0.04045 ? (((b + 0.055) / 1.055) ** 2.4) : (b / 12.92);\n    const x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n    const y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n    const z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n    return [x * 100, y * 100, z * 100];\n}\nfunction rgbToLab(rgb) {\n    const xyz = rgbToXyz(rgb);\n    let x = xyz[0];\n    let y = xyz[1];\n    let z = xyz[2];\n    x /= 95.047;\n    y /= 100;\n    z /= 108.883;\n    x = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n    y = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n    z = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n    const l = (116 * y) - 16;\n    const a = 500 * (x - y);\n    const b = 200 * (y - z);\n    return [l, a, b];\n}\nfunction labToLch(lab) {\n    const l = lab[0];\n    const a = lab[1];\n    const b = lab[2];\n    let h;\n    const hr = Math.atan2(b, a);\n    h = hr * 360 / 2 / Math.PI;\n    if (h < 0) {\n        h += 360;\n    }\n    const c = Math.sqrt(a * a + b * b);\n    return [l, c, h];\n}\nfunction lchToLab(lch) {\n    const l = lch[0];\n    const c = lch[1];\n    const h = lch[2];\n    const hr = h / 360 * 2 * Math.PI;\n    const a = c * Math.cos(hr);\n    const b = c * Math.sin(hr);\n    return [l, a, b];\n}\nfunction labToXyz(lab) {\n    const l = lab[0];\n    const a = lab[1];\n    const b = lab[2];\n    let x;\n    let y;\n    let z;\n    y = (l + 16) / 116;\n    x = a / 500 + y;\n    z = y - b / 200;\n    const y2 = y ** 3;\n    const x2 = x ** 3;\n    const z2 = z ** 3;\n    y = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n    x = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n    z = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n    x *= 95.047;\n    y *= 100;\n    z *= 108.883;\n    return [x, y, z];\n}\nfunction xyzToRgb(xyz) {\n    const x = xyz[0] / 100;\n    const y = xyz[1] / 100;\n    const z = xyz[2] / 100;\n    let r;\n    let g;\n    let b;\n    r = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n    g = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n    b = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n    // Assume sRGB\n    r = r > 0.0031308\n        ? ((1.055 * (r ** (1.0 / 2.4))) - 0.055)\n        : r * 12.92;\n    g = g > 0.0031308\n        ? ((1.055 * (g ** (1.0 / 2.4))) - 0.055)\n        : g * 12.92;\n    b = b > 0.0031308\n        ? ((1.055 * (b ** (1.0 / 2.4))) - 0.055)\n        : b * 12.92;\n    r = min(max(0, r), 1);\n    g = min(max(0, g), 1);\n    b = min(max(0, b), 1);\n    return [r * 255, g * 255, b * 255];\n}\nfunction rgbToLuminance(r, g, b) {\n    // Relative luminance\n    // See http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    r = luminance_channel(r);\n    g = luminance_channel(g);\n    b = luminance_channel(b);\n    return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n}\nfunction luminance_channel(x) {\n    x /= 255;\n    return x <= 0.03928 ? x / 12.92 : pow((x + 0.055) / 1.055, 2.4);\n}\nfunction interpolateRgb(rgb1, rgb2, f = 0.5) {\n    return [\n        rgb1[0] + f * (rgb2[0] - rgb1[0]),\n        rgb1[1] + f * (rgb2[1] - rgb1[1]),\n        rgb1[2] + f * (rgb2[2] - rgb1[2]),\n    ];\n}\nfunction hexColorToInt(_color) {\n    if (_color.startsWith('#')) {\n        return parseInt(_color.slice(1), 16);\n    }\n    throw new Error(`Expected to start with '#' the given value is: ${_color}`);\n}\nfunction color(...args) {\n    return new Color(...args);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Color, color, hexColorToInt };\n"], "mappings": "AAAA,MAAMA,GAAG,GAAG,IAAI;AAChB,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAM;EAAEC,GAAG;EAAEC,GAAG;EAAEC;AAAI,CAAC,GAAGC,IAAI;AAC9B,MAAMC,KAAK,CAAC;EACRC,WAAWA,CAAC,GAAGC,IAAI,EAAE;IACjB,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,IAAID,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MACpC,IAAI,CAACC,MAAM,GAAGC,WAAW,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,MACI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB,IAAI,CAACC,MAAM,GAAGF,IAAI;MAClB;MACA,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;QACnB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;MACtB;IACJ,CAAC,MACI;MACD,IAAI,CAACA,MAAM,GAAG,EAAE;IACpB;EACJ;EACAE,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;EAC/B;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAClB,OAAO,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC;IACzB;IACA;IACA,MAAMA,MAAM,GAAG,IAAI,CAACE,IAAI,CAAC,CAAC;IAC1B;IACAF,MAAM,CAAC,CAAC,CAAC,GAAGK,KAAK;IACjB,OAAO,IAAIT,KAAK,CAAC,GAAGI,MAAM,CAAC;EAC/B;EACAM,SAASA,CAACC,GAAG,EAAE;IACX,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAChB,OAAOC,cAAc,CAAC,GAAG,IAAI,CAACR,MAAM,CAAC;IACzC;IACA,IAAIO,GAAG,KAAK,CAAC,EAAE;MACX;MACA,OAAO,IAAIX,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,IAAIO,GAAG,KAAK,CAAC,EAAE;MACX;MACA,OAAO,IAAIX,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;IACnD;IACA,MAAMS,iBAAiB,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC;IAC1C,IAAII,QAAQ,GAAGnB,QAAQ;IACvB,MAAMoB,IAAI,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAK;MACxB,MAAMC,GAAG,GAAG,IAAIlB,KAAK,CAAC,GAAGmB,cAAc,CAACH,GAAG,CAACV,IAAI,CAAC,CAAC,EAAEW,IAAI,CAACX,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACtE,MAAMc,EAAE,GAAGF,GAAG,CAACR,SAAS,CAAC,CAAC;MAC1B,IAAIX,IAAI,CAACsB,GAAG,CAACV,GAAG,GAAGS,EAAE,CAAC,GAAG1B,GAAG,IAAI,CAACoB,QAAQ,EAAE,EAAE;QACzC,OAAOI,GAAG;MACd;MACA,OAAOE,EAAE,GAAGT,GAAG,GAAGI,IAAI,CAACC,GAAG,EAAEE,GAAG,CAAC,GAAGH,IAAI,CAACG,GAAG,EAAED,IAAI,CAAC;IACtD,CAAC;IACD,MAAMK,GAAG,GAAG,CAACT,iBAAiB,GAAGF,GAAG,GAC9BI,IAAI,CAAC,IAAIf,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAC9Be,IAAI,CAAC,IAAI,EAAE,IAAIf,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAEM,IAAI,CAAC,CAAC;IAClDgB,GAAG,CAACC,GAAG,CAAC,CAAC;IACTD,GAAG,CAACE,IAAI,CAAC,IAAI,CAACpB,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,OAAO,IAAIJ,KAAK,CAAC,GAAGsB,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIG,QAAQA,CAACC,MAAM,GAAG,CAAC,EAAE;IACjB,MAAMC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACxB,MAAM,CAAC;IACjC,MAAMyB,GAAG,GAAGC,QAAQ,CAACH,GAAG,CAAC;IACzBE,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGH,MAAM;IACrB,IAAIG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACZA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACd;IACA,MAAME,UAAU,GAAGC,QAAQ,CAACH,GAAG,CAAC;IAChC,MAAMI,UAAU,GAAGC,QAAQ,CAACH,UAAU,CAAC;IACvC,MAAMT,GAAG,GAAGa,QAAQ,CAACF,UAAU,CAAC;IAChC;IACAX,GAAG,CAACE,IAAI,CAAC,IAAI,CAACpB,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,OAAO,IAAIJ,KAAK,CAAC,GAAGsB,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIc,UAAUA,CAACV,MAAM,GAAG,CAAC,EAAE;IACnB,OAAO,IAAI,CAACD,QAAQ,CAAC,CAACC,MAAM,CAAC;EACjC;EACA;AACJ;AACA;EACIW,MAAMA,CAACX,MAAM,GAAG,CAAC,EAAE;IACf,MAAMC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACxB,MAAM,CAAC;IACjCuB,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGD,MAAM;IACrB,MAAMO,UAAU,GAAGC,QAAQ,CAACP,GAAG,CAAC;IAChC,MAAML,GAAG,GAAGa,QAAQ,CAACF,UAAU,CAAC;IAChC;IACAX,GAAG,CAACE,IAAI,CAAC,IAAI,CAACpB,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,OAAO,IAAIJ,KAAK,CAAC,GAAGsB,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIgB,QAAQA,CAACZ,MAAM,GAAG,CAAC,EAAE;IACjB,OAAO,IAAI,CAACW,MAAM,CAAC,CAACX,MAAM,CAAC;EAC/B;EACAa,GAAGA,CAAA,EAAG;IACF,IAAI,CAAC,IAAI,CAACnC,MAAM,CAACD,MAAM,EAAE;MACrB,OAAO,2BAA2B;IACtC;IACA,OAAOqC,QAAQ,CAAC,IAAI,CAAClC,IAAI,CAAC,CAAC,CAAC;EAChC;EACAmC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,GAAG,CAAC,CAAC;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAClB,GAAG,EAAE;EACnB,MAAMd,KAAK,GAAGc,GAAG,CAACC,GAAG,CAAC,CAAC;EACvB,IAAIf,KAAK,KAAK,CAAC,EAAE;IACb,OAAQ,OAAMc,GAAG,CAACoB,GAAG,CAAC3C,IAAI,CAAC4C,KAAK,CAAC,CAACC,IAAI,CAAC,CAAE,GAAE;EAC/C;EACA,OAAQ,QAAOtB,GAAG,CAACoB,GAAG,CAAC3C,IAAI,CAAC4C,KAAK,CAAC,CAACC,IAAI,CAAC,CAAE,IAAGpC,KAAM,GAAE;AACzD;AACA,SAASH,WAAWA,CAACwC,MAAM,EAAErC,KAAK,GAAG,CAAC,EAAE;EACpC;EACA;EACA;EACA;EACA;EACA;EACA,MAAMsC,GAAG,GAAID,MAAM,IAAI,EAAE,GAAI,IAAI;EACjC;EACA,MAAME,KAAK,GAAIF,MAAM,IAAI,CAAC,GAAI,IAAI;EAClC;EACA,MAAMG,IAAI,GAAGH,MAAM,GAAG,IAAI;EAC1B,OAAO,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAExC,KAAK,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA,SAASyC,QAAQA,CAAC3B,GAAG,EAAE;EACnB,IAAI4B,CAAC,GAAG5B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI6B,CAAC,GAAG7B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB,IAAI8B,CAAC,GAAG9B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACpB;EACA4B,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAI,CAAC,CAACA,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,GAAKA,CAAC,GAAG,KAAM;EAC9DC,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAI,CAAC,CAACA,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,GAAKA,CAAC,GAAG,KAAM;EAC9DC,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAI,CAAC,CAACA,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,GAAKA,CAAC,GAAG,KAAM;EAC9D,MAAMC,CAAC,GAAIH,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EACpD,MAAME,CAAC,GAAIJ,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EACpD,MAAMG,CAAC,GAAIL,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EACpD,OAAO,CAACC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AACtC;AACA,SAAS3B,QAAQA,CAACN,GAAG,EAAE;EACnB,MAAMkC,GAAG,GAAGP,QAAQ,CAAC3B,GAAG,CAAC;EACzB,IAAI+B,CAAC,GAAGG,GAAG,CAAC,CAAC,CAAC;EACd,IAAIF,CAAC,GAAGE,GAAG,CAAC,CAAC,CAAC;EACd,IAAID,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC;EACdH,CAAC,IAAI,MAAM;EACXC,CAAC,IAAI,GAAG;EACRC,CAAC,IAAI,OAAO;EACZF,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAIA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAK,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAC5DC,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAIA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAK,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAC5DC,CAAC,GAAGA,CAAC,GAAG,QAAQ,GAAIA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAK,KAAK,GAAGA,CAAC,GAAK,EAAE,GAAG,GAAI;EAC5D,MAAME,CAAC,GAAI,GAAG,GAAGH,CAAC,GAAI,EAAE;EACxB,MAAMI,CAAC,GAAG,GAAG,IAAIL,CAAC,GAAGC,CAAC,CAAC;EACvB,MAAMF,CAAC,GAAG,GAAG,IAAIE,CAAC,GAAGC,CAAC,CAAC;EACvB,OAAO,CAACE,CAAC,EAAEC,CAAC,EAAEN,CAAC,CAAC;AACpB;AACA,SAAStB,QAAQA,CAACH,GAAG,EAAE;EACnB,MAAM8B,CAAC,GAAG9B,GAAG,CAAC,CAAC,CAAC;EAChB,MAAM+B,CAAC,GAAG/B,GAAG,CAAC,CAAC,CAAC;EAChB,MAAMyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC;EAChB,IAAIgC,CAAC;EACL,MAAMC,EAAE,GAAG7D,IAAI,CAAC8D,KAAK,CAACT,CAAC,EAAEM,CAAC,CAAC;EAC3BC,CAAC,GAAGC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG7D,IAAI,CAAC+D,EAAE;EAC1B,IAAIH,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,IAAI,GAAG;EACZ;EACA,MAAMI,CAAC,GAAGhE,IAAI,CAACiE,IAAI,CAACN,CAAC,GAAGA,CAAC,GAAGN,CAAC,GAAGA,CAAC,CAAC;EAClC,OAAO,CAACK,CAAC,EAAEM,CAAC,EAAEJ,CAAC,CAAC;AACpB;AACA,SAAS3B,QAAQA,CAACH,GAAG,EAAE;EACnB,MAAM4B,CAAC,GAAG5B,GAAG,CAAC,CAAC,CAAC;EAChB,MAAMkC,CAAC,GAAGlC,GAAG,CAAC,CAAC,CAAC;EAChB,MAAM8B,CAAC,GAAG9B,GAAG,CAAC,CAAC,CAAC;EAChB,MAAM+B,EAAE,GAAGD,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG5D,IAAI,CAAC+D,EAAE;EAChC,MAAMJ,CAAC,GAAGK,CAAC,GAAGhE,IAAI,CAACkE,GAAG,CAACL,EAAE,CAAC;EAC1B,MAAMR,CAAC,GAAGW,CAAC,GAAGhE,IAAI,CAACmE,GAAG,CAACN,EAAE,CAAC;EAC1B,OAAO,CAACH,CAAC,EAAEC,CAAC,EAAEN,CAAC,CAAC;AACpB;AACA,SAASlB,QAAQA,CAACP,GAAG,EAAE;EACnB,MAAM8B,CAAC,GAAG9B,GAAG,CAAC,CAAC,CAAC;EAChB,MAAM+B,CAAC,GAAG/B,GAAG,CAAC,CAAC,CAAC;EAChB,MAAMyB,CAAC,GAAGzB,GAAG,CAAC,CAAC,CAAC;EAChB,IAAI0B,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EACLD,CAAC,GAAG,CAACG,CAAC,GAAG,EAAE,IAAI,GAAG;EAClBJ,CAAC,GAAGK,CAAC,GAAG,GAAG,GAAGJ,CAAC;EACfC,CAAC,GAAGD,CAAC,GAAGF,CAAC,GAAG,GAAG;EACf,MAAMe,EAAE,GAAGb,CAAC,IAAI,CAAC;EACjB,MAAMc,EAAE,GAAGf,CAAC,IAAI,CAAC;EACjB,MAAMgB,EAAE,GAAGd,CAAC,IAAI,CAAC;EACjBD,CAAC,GAAGa,EAAE,GAAG,QAAQ,GAAGA,EAAE,GAAG,CAACb,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK;EAC/CD,CAAC,GAAGe,EAAE,GAAG,QAAQ,GAAGA,EAAE,GAAG,CAACf,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK;EAC/CE,CAAC,GAAGc,EAAE,GAAG,QAAQ,GAAGA,EAAE,GAAG,CAACd,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,KAAK;EAC/CF,CAAC,IAAI,MAAM;EACXC,CAAC,IAAI,GAAG;EACRC,CAAC,IAAI,OAAO;EACZ,OAAO,CAACF,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACpB;AACA,SAASpB,QAAQA,CAACqB,GAAG,EAAE;EACnB,MAAMH,CAAC,GAAGG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,MAAMF,CAAC,GAAGE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,MAAMD,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACtB,IAAIN,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EACLF,CAAC,GAAIG,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,CAAC,MAAO,GAAIC,CAAC,GAAG,CAAC,MAAO;EAChDJ,CAAC,GAAIE,CAAC,GAAG,CAAC,MAAM,GAAKC,CAAC,GAAG,MAAO,GAAIC,CAAC,GAAG,MAAO;EAC/CH,CAAC,GAAIC,CAAC,GAAG,MAAM,GAAKC,CAAC,GAAG,CAAC,MAAO,GAAIC,CAAC,GAAG,MAAO;EAC/C;EACAL,CAAC,GAAGA,CAAC,GAAG,SAAS,GACT,KAAK,GAAIA,CAAC,KAAK,GAAG,GAAG,GAAG,CAAE,GAAI,KAAK,GACrCA,CAAC,GAAG,KAAK;EACfC,CAAC,GAAGA,CAAC,GAAG,SAAS,GACT,KAAK,GAAIA,CAAC,KAAK,GAAG,GAAG,GAAG,CAAE,GAAI,KAAK,GACrCA,CAAC,GAAG,KAAK;EACfC,CAAC,GAAGA,CAAC,GAAG,SAAS,GACT,KAAK,GAAIA,CAAC,KAAK,GAAG,GAAG,GAAG,CAAE,GAAI,KAAK,GACrCA,CAAC,GAAG,KAAK;EACfF,CAAC,GAAGrD,GAAG,CAACC,GAAG,CAAC,CAAC,EAAEoD,CAAC,CAAC,EAAE,CAAC,CAAC;EACrBC,CAAC,GAAGtD,GAAG,CAACC,GAAG,CAAC,CAAC,EAAEqD,CAAC,CAAC,EAAE,CAAC,CAAC;EACrBC,CAAC,GAAGvD,GAAG,CAACC,GAAG,CAAC,CAAC,EAAEsD,CAAC,CAAC,EAAE,CAAC,CAAC;EACrB,OAAO,CAACF,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,EAAEC,CAAC,GAAG,GAAG,CAAC;AACtC;AACA,SAASxC,cAAcA,CAACsC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC7B;EACA;EACAF,CAAC,GAAGoB,iBAAiB,CAACpB,CAAC,CAAC;EACxBC,CAAC,GAAGmB,iBAAiB,CAACnB,CAAC,CAAC;EACxBC,CAAC,GAAGkB,iBAAiB,CAAClB,CAAC,CAAC;EACxB,OAAO,MAAM,GAAGF,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC;AAC/C;AACA,SAASkB,iBAAiBA,CAACjB,CAAC,EAAE;EAC1BA,CAAC,IAAI,GAAG;EACR,OAAOA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGzD,GAAG,CAAC,CAACyD,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;AACnE;AACA,SAASlC,cAAcA,CAACoD,IAAI,EAAEC,IAAI,EAAEC,CAAC,GAAG,GAAG,EAAE;EACzC,OAAO,CACHF,IAAI,CAAC,CAAC,CAAC,GAAGE,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,CAAC,EACjCA,IAAI,CAAC,CAAC,CAAC,GAAGE,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,CAAC,EACjCA,IAAI,CAAC,CAAC,CAAC,GAAGE,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,CAAC,CACpC;AACL;AACA,SAASG,aAAaA,CAACtE,MAAM,EAAE;EAC3B,IAAIA,MAAM,CAACuE,UAAU,CAAC,GAAG,CAAC,EAAE;IACxB,OAAOC,QAAQ,CAACxE,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC;EACA,MAAM,IAAIsE,KAAK,CAAE,kDAAiDzE,MAAO,EAAC,CAAC;AAC/E;AACA,SAAS0E,KAAKA,CAAC,GAAG5E,IAAI,EAAE;EACpB,OAAO,IAAIF,KAAK,CAAC,GAAGE,IAAI,CAAC;AAC7B;;AAEA;AACA;AACA;;AAEA,SAASF,KAAK,EAAE8E,KAAK,EAAEJ,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport moment from 'moment';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i8 from \"app/layout/components/content-header/content-header.component\";\nimport * as i9 from \"angular-datatables\";\nimport * as i10 from \"../../components/editor-sidebar/editor-sidebar.component\";\nexport class EmailBlacklistComponent {\n  constructor(_http, _coreSidebarService, _translateService, _commonsService, renderer, _modalService, _titleService) {\n    this._http = _http;\n    this._coreSidebarService = _coreSidebarService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this.renderer = renderer;\n    this._modalService = _modalService;\n    this._titleService = _titleService;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.table_name = 'email-blacklist';\n    this.fields = [{\n      key: 'email',\n      type: 'input',\n      props: {\n        required: true,\n        label: this._translateService.instant('Email Address'),\n        placeholder: this._translateService.instant('Enter email address')\n      }\n    }, {\n      key: 'reason',\n      type: 'input',\n      props: {\n        required: true,\n        label: this._translateService.instant('Reason'),\n        placeholder: this._translateService.instant('Reason')\n      }\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: 'Add email blacklist',\n        edit: 'Edit email blacklist',\n        remove: 'Delete email blacklist'\n      },\n      url: `${environment.apiUrl}/email/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this._titleService.setTitle('Email Blacklist');\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: 'Email Black List',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Email Black List',\n          isLink: false\n        }]\n      }\n    };\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        this._http.post(`${environment.apiUrl}/email/blacklist/all`, dataTablesParameters).subscribe(resp => {\n          console.log('resp', resp);\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }, {\n        responsivePriority: 1,\n        targets: -2\n      }, {\n        responsivePriority: 2,\n        targets: -3\n      }],\n      orderBy: [[1, 'asc']],\n      displayLength: 10,\n      columns: [{\n        title: this._translateService.instant('Name'),\n        data: 'user_name',\n        className: 'font-weight-bolder p-1'\n      }, {\n        title: this._translateService.instant('Email'),\n        data: 'email',\n        className: 'p-1'\n      }, {\n        title: this._translateService.instant('Status'),\n        data: 'status',\n        className: 'p-1',\n        render: (data, type, row) => {\n          return this._commonsService.getBadgeClassBlacklist(data);\n        }\n      }, {\n        title: this._translateService.instant('Reason'),\n        data: 'reason',\n        className: 'p-1'\n      }, {\n        data: 'created_at',\n        title: this._translateService.instant('Created At'),\n        className: 'center',\n        render: (data, type, row) => {\n          const content = moment(data).format('YYYY-MM-DD');\n          return `<span style=\"white-space: nowrap; display: inline-block; min-width: 120px\">${content}</span>`;\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"feather icon-plus\"></i> ' + this._translateService.instant('Add email to block'),\n          action: () => this.editor('create')\n        }, {\n          text: '<i class=\"feather icon-edit\"></i> ' + this._translateService.instant('Edit'),\n          action: () => this.editor('edit'),\n          extend: 'selected'\n        }, {\n          text: '<i class=\"feather icon-trash\"></i> ' + this._translateService.instant('Delete'),\n          action: () => this.editor('remove'),\n          extend: 'selected'\n        }]\n      }\n    };\n  }\n  editor(action, row) {\n    console.log('editor', action, row);\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n  }\n  static #_ = this.ɵfac = function EmailBlacklistComponent_Factory(t) {\n    return new (t || EmailBlacklistComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CoreSidebarService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.NgbModal), i0.ɵɵdirectiveInject(i6.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmailBlacklistComponent,\n    selectors: [[\"app-email-blacklist\"]],\n    viewQuery: function EmailBlacklistComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 6,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"card\"], [1, \"card-header\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"]],\n    template: function EmailBlacklistComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"div\", 4)(5, \"table\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"core-sidebar\", 6);\n        i0.ɵɵelement(7, \"app-editor-sidebar\", 7);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    dependencies: [i7.CoreSidebarComponent, i8.ContentHeaderComponent, i9.DataTableDirective, i10.EditorSidebarComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SAASA,kBAAkB,QAAQ,oBAAoB;AAIvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,OAAO,QAAQ,MAAM;;;;;;;;;;;;AAQ9B,OAAM,MAAOC,uBAAuB;EAuClCC,YACUC,KAAiB,EAClBC,mBAAuC,EACvCC,iBAAmC,EACnCC,eAA+B,EAC/BC,QAAmB,EACnBC,aAAuB,EACvBC,aAAoB;IANnB,KAAAN,KAAK,GAALA,KAAK;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IA5CtB,KAAAC,SAAS,GAAQb,kBAAkB;IAGnC,KAAAc,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAIZ,OAAO,EAAe;IACrD,KAAAa,UAAU,GAAG,iBAAiB;IAC9B,KAAAC,MAAM,GAAU,CACpB;MACCC,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACd,iBAAiB,CAACe,OAAO,CAAC,eAAe,CAAC;QACtDC,WAAW,EAAE,IAAI,CAAChB,iBAAiB,CAACe,OAAO,CAAC,qBAAqB;;KAEpE,EACD;MACEL,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACd,iBAAiB,CAACe,OAAO,CAAC,QAAQ,CAAC;QAC/CC,WAAW,EAAE,IAAI,CAAChB,iBAAiB,CAACe,OAAO,CAAC,QAAQ;;KAEvD,CACF;IACM,KAAAE,MAAM,GAAwB;MACjCC,SAAS,EAAE,IAAI,CAACV,UAAU;MAC1BW,KAAK,EAAE;QACLC,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,sBAAsB;QAC5BC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAG9B,WAAW,CAAC+B,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACX;IAUC,IAAI,CAACtB,aAAa,CAACuB,QAAQ,CAAC,iBAAiB,CAAC;EAChD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,kBAAkB;MAC/BC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVrB,IAAI,EAAE,EAAE;QACRsB,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,kBAAkB;UACxBC,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAC7B,SAAS,GAAG;MACf8B,GAAG,EAAE,IAAI,CAACnC,eAAe,CAACoC,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAAC7C,KAAK,CACP8C,IAAI,CAAM,GAAGnD,WAAW,CAAC+B,MAAM,sBAAsB,EAAEkB,oBAAoB,CAAC,CAC5EG,SAAS,CAAEC,IAAS,IAAI;UACvBC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEF,IAAI,CAAC;UACzBH,QAAQ,CAAC;YACPM,YAAY,EAAEH,IAAI,CAACG,YAAY;YAC/BC,eAAe,EAAEJ,IAAI,CAACI,eAAe;YACrCC,IAAI,EAAEL,IAAI,CAACK;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAACrD,eAAe,CAACoC,iBAAiB,CAACkB,IAAI;MACrDC,UAAU,EAAE,IAAI,CAACvD,eAAe,CAACoC,iBAAiB,CAACmB,UAAU;MAC7DC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CACvC;MACDC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MACrBC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,CACP;QACE3C,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACe,OAAO,CAAC,MAAM,CAAC;QAC7CoC,IAAI,EAAE,WAAW;QACjBY,SAAS,EAAE;OACZ,EACD;QACE5C,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACe,OAAO,CAAC,OAAO,CAAC;QAC9CoC,IAAI,EAAE,OAAO;QACbY,SAAS,EAAE;OACZ,EACD;QACE5C,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACe,OAAO,CAAC,QAAQ,CAAC;QAC/CoC,IAAI,EAAE,QAAQ;QACdY,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAEA,CAACb,IAAI,EAAExC,IAAI,EAAEsD,GAAG,KAAI;UAC1B,OAAO,IAAI,CAAChE,eAAe,CAACiE,sBAAsB,CAACf,IAAI,CAAC;QAC1D;OACD,EACD;QACEhC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACe,OAAO,CAAC,QAAQ,CAAC;QAC/CoC,IAAI,EAAE,QAAQ;QACdY,SAAS,EAAE;OACZ,EACD;QACAZ,IAAI,EAAE,YAAY;QAClBhC,KAAK,EAAE,IAAI,CAACnB,iBAAiB,CAACe,OAAO,CAAC,YAAY,CAAC;QACnDgD,SAAS,EAAE,QAAQ;QACnBC,MAAM,EAAEA,CAACb,IAAI,EAAExC,IAAI,EAAEsD,GAAG,KAAI;UACxB,MAAME,OAAO,GAAGzE,MAAM,CAACyD,IAAI,CAAC,CAACiB,MAAM,CAAC,YAAY,CAAC;UACjD,OAAO,8EAA8ED,OAAO,SAAS;QACvG;OACD,CACF;MACDE,OAAO,EAAE;QACPjC,GAAG,EAAE,IAAI,CAACnC,eAAe,CAACoC,iBAAiB,CAACgC,OAAO,CAACjC,GAAG;QACvDiC,OAAO,EAAE,CACP;UACEC,IAAI,EACF,oCAAoC,GACpC,IAAI,CAACtE,iBAAiB,CAACe,OAAO,CAAC,oBAAoB,CAAC;UACtDW,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6C,MAAM,CAAC,QAAQ;SACnC,EACD;UACED,IAAI,EACF,oCAAoC,GACpC,IAAI,CAACtE,iBAAiB,CAACe,OAAO,CAAC,MAAM,CAAC;UACxCW,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6C,MAAM,CAAC,MAAM,CAAC;UACjCC,MAAM,EAAE;SACT,EACD;UACEF,IAAI,EACF,qCAAqC,GACrC,IAAI,CAACtE,iBAAiB,CAACe,OAAO,CAAC,QAAQ,CAAC;UAC1CW,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6C,MAAM,CAAC,QAAQ,CAAC;UACnCC,MAAM,EAAE;SACT;;KAGN;EACH;EAEAD,MAAMA,CAAC7C,MAAM,EAAEuC,GAAI;IACjBlB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEtB,MAAM,EAAEuC,GAAG,CAAC;IAClC,IAAI,CAAChD,MAAM,CAACS,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACT,MAAM,CAACgD,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAAClE,mBAAmB,CAAC0E,kBAAkB,CAAC,IAAI,CAACjE,UAAU,CAAC,CAACkE,UAAU,EAAE;EAC3E;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACrE,SAAS,CAACsE,IAAI,CAAC,IAAI,CAACvE,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAwE,WAAWA,CAAA;IACT,IAAI,CAACvE,SAAS,CAACwE,WAAW,EAAE;EAC9B;EAAC,QAAAC,CAAA;qBA5KUpF,uBAAuB,EAAAqF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAU,EAAA,CAAAC,QAAA,GAAAZ,EAAA,CAAAC,iBAAA,CAAAY,EAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAvBpG,uBAAuB;IAAAqG,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBACvB5G,kBAAkB;;;;;;;;;;;;QCtB/ByF,EAAA,CAAAqB,cAAA,aAA+C;QAG3CrB,EAAA,CAAAsB,SAAA,4BAAyE;QACzEtB,EAAA,CAAAqB,cAAA,aAAkB;QAChBrB,EAAA,CAAAsB,SAAA,aAEM;QAMRtB,EAAA,CAAAuB,YAAA,EAAM;QAIVvB,EAAA,CAAAqB,cAAA,sBAAqH;QACnHrB,EAAA,CAAAsB,SAAA,4BACqB;QACvBtB,EAAA,CAAAuB,YAAA,EAAe;;;QAjBSvB,EAAA,CAAAwB,SAAA,GAA+B;QAA/BxB,EAAA,CAAAyB,UAAA,kBAAAL,GAAA,CAAAxE,aAAA,CAA+B;QAO/CoD,EAAA,CAAAwB,SAAA,GAAuB;QAAvBxB,EAAA,CAAAyB,UAAA,cAAAL,GAAA,CAAA/F,SAAA,CAAuB;QAOoC2E,EAAA,CAAAwB,SAAA,GAAmB;QAAnBxB,EAAA,CAAAyB,UAAA,SAAAL,GAAA,CAAA7F,UAAA,CAAmB;QAChEyE,EAAA,CAAAwB,SAAA,GAAmB;QAAnBxB,EAAA,CAAAyB,UAAA,UAAAL,GAAA,CAAAhG,SAAA,CAAmB,WAAAgG,GAAA,CAAA5F,MAAA,YAAA4F,GAAA,CAAApF,MAAA", "names": ["DataTableDirective", "environment", "moment", "Subject", "EmailBlacklistComponent", "constructor", "_http", "_coreSidebarService", "_translateService", "_commonsService", "renderer", "_modalService", "_titleService", "dtElement", "dtOptions", "dtTrigger", "table_name", "fields", "key", "type", "props", "required", "label", "instant", "placeholder", "params", "editor_id", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "setTitle", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "dom", "dataTableDefaults", "select", "serverSide", "rowId", "ajax", "dataTablesParameters", "callback", "post", "subscribe", "resp", "console", "log", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "lengthMenu", "columnDefs", "responsivePriority", "targets", "orderBy", "displayLength", "columns", "className", "render", "row", "getBadgeClassBlacklist", "content", "format", "buttons", "text", "editor", "extend", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngAfterViewInit", "setTimeout", "next", "ngOnDestroy", "unsubscribe", "_", "i0", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CoreSidebarService", "i3", "TranslateService", "i4", "CommonsService", "Renderer2", "i5", "NgbModal", "i6", "Title", "_2", "selectors", "viewQuery", "EmailBlacklistComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\email-blacklist\\email-blacklist.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\email-blacklist\\email-blacklist.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { environment } from 'environments/environment';\r\nimport moment from 'moment';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-email-blacklist',\r\n  templateUrl: './email-blacklist.component.html',\r\n  styleUrls: ['./email-blacklist.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class EmailBlacklistComponent {\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  public form: FormGroup;\r\n  public contentHeader: object;\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  public table_name = 'email-blacklist';\r\n  public fields: any[] = [\r\n     {\r\n      key: 'email',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Email Address'),\r\n        placeholder: this._translateService.instant('Enter email address'),\r\n      },\r\n    },\r\n    {\r\n      key: 'reason',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Reason'),\r\n        placeholder: this._translateService.instant('Reason'),\r\n      },\r\n    },\r\n  ];\r\n  public params: EditorSidebarParams = {\r\n      editor_id: this.table_name,\r\n      title: {\r\n        create: 'Add email blacklist',\r\n        edit: 'Edit email blacklist',\r\n        remove: 'Delete email blacklist',\r\n      },\r\n      url: `${environment.apiUrl}/email/editor`,\r\n      method: 'POST',\r\n      action: 'create',\r\n  };\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public renderer: Renderer2,\r\n    public _modalService: NgbModal,\r\n    public _titleService: Title,\r\n  ) {\r\n    this._titleService.setTitle('Email Blacklist');\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: 'Email Black List',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Email Black List',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .post<any>(`${environment.apiUrl}/email/blacklist/all`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            console.log('resp', resp);\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: -1 },\r\n        { responsivePriority: 1, targets: -2 },\r\n        { responsivePriority: 2, targets: -3 },\r\n      ],\r\n      orderBy: [[1, 'asc']],\r\n      displayLength: 10,\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('Name'),\r\n          data: 'user_name',\r\n          className: 'font-weight-bolder p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Email'),\r\n          data: 'email',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n          title: this._translateService.instant('Status'),\r\n          data: 'status',\r\n          className: 'p-1',\r\n          render: (data, type, row) => {\r\n            return this._commonsService.getBadgeClassBlacklist(data);\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Reason'),\r\n          data: 'reason',\r\n          className: 'p-1',\r\n        },\r\n        {\r\n        data: 'created_at',\r\n        title: this._translateService.instant('Created At'),\r\n        className: 'center',\r\n        render: (data, type, row) => {\r\n            const content = moment(data).format('YYYY-MM-DD');\r\n            return `<span style=\"white-space: nowrap; display: inline-block; min-width: 120px\">${content}</span>`;\r\n          },\r\n        },\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-plus\"></i> ' +\r\n              this._translateService.instant('Add email to block'),\r\n            action: () => this.editor('create'),\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-edit\"></i> ' +\r\n              this._translateService.instant('Edit'),\r\n            action: () => this.editor('edit'),\r\n            extend: 'selected',\r\n          },\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-trash\"></i> ' +\r\n              this._translateService.instant('Delete'),\r\n            action: () => this.editor('remove'),\r\n            extend: 'selected',\r\n          },\r\n        ],\r\n      },\r\n    }\r\n  }\r\n\r\n  editor(action, row?) {\r\n    console.log('editor', action, row);\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\r\n      this.dtTrigger.next(this.dtOptions);\r\n    }, 500);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <!-- <h4 class=\"card-title\">Table Basic</h4> -->\r\n      </div>\r\n      <table\r\n        datatable\r\n        [dtOptions]=\"dtOptions\"\r\n        class=\"table border row-border hover\"\r\n      ></table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n  <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n  </app-editor-sidebar>\r\n</core-sidebar>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
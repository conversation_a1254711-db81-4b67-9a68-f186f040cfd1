{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { enableProdMode } from '@angular/core';\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nimport { hmrBootstrap } from './hmr';\nimport 'hammerjs';\nif (environment.production) {\n  enableProdMode();\n}\nconst bootstrap = () => __NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule);\nif (environment.hmr) {\n  if (module['hot']) {\n    hmrBootstrap(module, bootstrap);\n  } else {\n    console.error('HMR is not enabled for webpack-dev-server!');\n    console.log('Are you using the --hmr flag for ng serve?');\n  }\n} else {\n  bootstrap().catch(err => console.log(err));\n}", "map": {"version": 3, "mappings": ";AAAA,SAASA,cAAc,QAAQ,eAAe;AAG9C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,4BAA4B;AAExD,SAASC,YAAY,QAAQ,OAAO;AACpC,OAAO,UAAU;AACjB,IAAID,WAAW,CAACE,UAAU,EAAE;EAC1BJ,cAAc,EAAE;;AAElB,MAAMK,SAAS,GAAGA,CAAA,KAAMC,mBAAA,CAAAC,eAAA,EAAwB,CAACC,eAAe,CAACP,SAAS,CAAC;AAE3E,IAAIC,WAAW,CAACO,GAAG,EAAE;EACnB,IAAIC,MAAM,CAAC,KAAK,CAAC,EAAE;IACjBP,YAAY,CAACO,MAAM,EAAEL,SAAS,CAAC;GAChC,MAAM;IACLM,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;IAC3DD,OAAO,CAACE,GAAG,CAAC,4CAA4C,CAAC;;CAE5D,MAAM;EACLR,SAAS,EAAE,CAACS,KAAK,CAAEC,GAAG,IAAKJ,OAAO,CAACE,GAAG,CAACE,GAAG,CAAC,CAAC", "names": ["enableProdMode", "AppModule", "environment", "hmrBootstrap", "production", "bootstrap", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "hmr", "module", "console", "error", "log", "catch", "err"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\main.ts"], "sourcesContent": ["import { enableProdMode } from '@angular/core';\r\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\nimport { AppModule } from './app/app.module';\r\nimport { environment } from './environments/environment';\r\n\r\nimport { hmrBootstrap } from './hmr';\r\nimport 'hammerjs';\r\nif (environment.production) {\r\n  enableProdMode();\r\n}\r\nconst bootstrap = () => platformBrowserDynamic().bootstrapModule(AppModule);\r\n\r\nif (environment.hmr) {\r\n  if (module['hot']) {\r\n    hmrBootstrap(module, bootstrap);\r\n  } else {\r\n    console.error('HMR is not enabled for webpack-dev-server!');\r\n    console.log('Are you using the --hmr flag for ng serve?');\r\n  }\r\n} else {\r\n  bootstrap().catch((err) => console.log(err));\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
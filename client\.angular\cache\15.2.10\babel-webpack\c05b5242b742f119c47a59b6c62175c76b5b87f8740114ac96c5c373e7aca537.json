{"ast": null, "code": "import { FieldType } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@ngx-formly/core\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction FormlyFieldTabsVertical_li_3_ng_template_3_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r6.form.valid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"Submit\"));\n  }\n}\nfunction FormlyFieldTabsVertical_li_3_ng_template_3_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function FormlyFieldTabsVertical_li_3_ng_template_3_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const i_r3 = i0.ɵɵnextContext(2).index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(ctx_r9.nextTab(_r0, i_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = i0.ɵɵnextContext(2).index;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r7.isValid(ctx_r7.field.fieldGroup[i_r3]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"Next\"));\n  }\n}\nfunction FormlyFieldTabsVertical_li_3_ng_template_3_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function FormlyFieldTabsVertical_li_3_ng_template_3_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const i_r3 = i0.ɵɵnextContext(2).index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(ctx_r13.prevTab(_r0, i_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"Previous\"));\n  }\n}\nfunction FormlyFieldTabsVertical_li_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"formly-field\", 8);\n    i0.ɵɵelementStart(1, \"div\", 9);\n    i0.ɵɵtemplate(2, FormlyFieldTabsVertical_li_3_ng_template_3_button_2_Template, 3, 4, \"button\", 10);\n    i0.ɵɵtemplate(3, FormlyFieldTabsVertical_li_3_ng_template_3_button_3_Template, 3, 4, \"button\", 11);\n    i0.ɵɵtemplate(4, FormlyFieldTabsVertical_li_3_ng_template_3_button_4_Template, 3, 3, \"button\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    const tab_r2 = ctx_r16.$implicit;\n    const last_r4 = ctx_r16.last;\n    const i_r3 = ctx_r16.index;\n    i0.ɵɵproperty(\"field\", tab_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", last_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r3 !== 0);\n  }\n}\nfunction FormlyFieldTabsVertical_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 5);\n    i0.ɵɵelement(1, \"a\", 6);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, FormlyFieldTabsVertical_li_3_ng_template_3_Template, 5, 4, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngbNavItem\", i_r3)(\"disabled\", i_r3 !== 0 && !ctx_r1.isValid(ctx_r1.field.fieldGroup[i_r3 - 1]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(2, 3, tab_r2.props.label), i0.ɵɵsanitizeHtml);\n  }\n}\nexport class FormlyFieldTabsVertical extends FieldType {\n  constructor() {\n    super(...arguments);\n    this.activeID = 0;\n  }\n  ngOnInit() {\n    if (this.field.props.hasOwnProperty('onNext')) {\n      this.onNext = this.field.props.onNext;\n    }\n    if (this.field.props.hasOwnProperty('onPrev')) {\n      this.onPrev = this.field.props.onPrev;\n    }\n  }\n  isValid(field) {\n    if (field.key) {\n      return field.formControl.valid;\n    }\n    return field.fieldGroup ? field.fieldGroup.every(f => this.isValid(f)) : true;\n  }\n  nextTab(ngbNav, current_index) {\n    console.log('Next current_index', current_index);\n    if (current_index >= 0) {\n      this.form.markAsPristine();\n      this.activeID = current_index + 1;\n      ngbNav.select(this.activeID);\n      if (this.onNext) this.onNext(this.activeID, this.model);\n    }\n  }\n  prevTab(ngbNav, current_index) {\n    if (current_index > 0) {\n      this.activeID = current_index - 1;\n      ngbNav.select(this.activeID);\n      if (this.onPrev) this.onPrev(this.activeID, this.model);\n    }\n  }\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵFormlyFieldTabsVertical_BaseFactory;\n    return function FormlyFieldTabsVertical_Factory(t) {\n      return (ɵFormlyFieldTabsVertical_BaseFactory || (ɵFormlyFieldTabsVertical_BaseFactory = i0.ɵɵgetInheritedFactory(FormlyFieldTabsVertical)))(t || FormlyFieldTabsVertical);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormlyFieldTabsVertical,\n    selectors: [[\"formly-field-tabs\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 5,\n    vars: 2,\n    consts: [[1, \"nav-vertical\"], [\"ngbNav\", \"\", 1, \"nav\", \"nav-tabs\", \"nav-left\", \"flex-column\"], [\"navVertical\", \"ngbNav\"], [3, \"ngbNavItem\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngbNavOutlet\"], [3, \"ngbNavItem\", \"disabled\"], [\"ngbNavLink\", \"\", 3, \"innerHTML\"], [\"ngbNavContent\", \"\"], [3, \"field\"], [1, \"d-flex\", \"justify-content-between\", \"flex-row-reverse\"], [\"class\", \"btn btn-primary\", \"type\", \"submit\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", \"type\", \"button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function FormlyFieldTabsVertical_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"ul\", 1, 2);\n        i0.ɵɵtemplate(3, FormlyFieldTabsVertical_li_3_Template, 4, 5, \"li\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"div\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(2);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.field.fieldGroup);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.NgbNavContent, i2.NgbNav, i2.NgbNavItem, i2.NgbNavLink, i2.NgbNavOutlet, i3.FormlyField, i4.TranslatePipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAA2B,kBAAkB;;;;;;;;ICO3CC,EAAA,CAAAC,cAAA,iBACkB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADJH,EAAA,CAAAI,UAAA,cAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAwB;IACnDP,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,iBAAsB;;;;;;IACxCV,EAAA,CAAAC,cAAA,iBAC+C;IADaD,EAAA,CAAAW,UAAA,mBAAAC,qFAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAf,EAAA,CAAAgB,aAAA,IAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,MAAAG,GAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAH,MAAA,CAAAI,OAAA,CAAAH,GAAA,EAAAJ,IAAA,CAAsB;IAAA,EAAC;IAC7Cf,EAAA,CAAAE,MAAA,GAAoB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAAxEH,EAAA,CAAAI,UAAA,cAAAmB,MAAA,CAAAC,OAAA,CAAAD,MAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAX,IAAA,GAA0C;IAACf,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,eAAoB;;;;;;IACnEV,EAAA,CAAAC,cAAA,iBACqC;IAAjCD,EAAA,CAAAW,UAAA,mBAAAgB,qFAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAe,IAAA;MAAA,MAAAb,IAAA,GAAAf,EAAA,CAAAgB,aAAA,IAAAC,KAAA;MAAA,MAAAY,OAAA,GAAA7B,EAAA,CAAAgB,aAAA;MAAA,MAAAG,GAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAQ,OAAA,CAAAC,OAAA,CAAAX,GAAA,EAAAJ,IAAA,CAAsB;IAAA,EAAC;IAACf,EAAA,CAAAE,MAAA,GAAwB;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;IAAjCH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,mBAAwB;;;;;IAPjEV,EAAA,CAAA+B,SAAA,sBAA2C;IAC3C/B,EAAA,CAAAC,cAAA,aAA6D;IACzDD,EAAA,CAAAgC,UAAA,IAAAC,4DAAA,qBACiD;IACjDjC,EAAA,CAAAgC,UAAA,IAAAE,4DAAA,qBAC4E;IAC5ElC,EAAA,CAAAgC,UAAA,IAAAG,4DAAA,qBACsE;IAC1EnC,EAAA,CAAAG,YAAA,EAAM;;;;;;;IARQH,EAAA,CAAAI,UAAA,UAAAgC,MAAA,CAAa;IAEdpC,EAAA,CAAAQ,SAAA,GAAU;IAAVR,EAAA,CAAAI,UAAA,SAAAiC,OAAA,CAAU;IAEVrC,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAI,UAAA,UAAAiC,OAAA,CAAW;IAEXrC,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAI,UAAA,SAAAW,IAAA,OAAa;;;;;IAVlCf,EAAA,CAAAC,cAAA,YAC8D;IAC1DD,EAAA,CAAA+B,SAAA,WAA0D;;IAC1D/B,EAAA,CAAAgC,UAAA,IAAAM,mDAAA,yBAUc;IAClBtC,EAAA,CAAAG,YAAA,EAAK;;;;;;IAdDH,EAAA,CAAAI,UAAA,eAAAW,IAAA,CAAgB,aAAAA,IAAA,WAAAwB,MAAA,CAAAf,OAAA,CAAAe,MAAA,CAAAd,KAAA,CAAAC,UAAA,CAAAX,IAAA;IAEFf,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAI,UAAA,cAAAJ,EAAA,CAAAU,WAAA,OAAA0B,MAAA,CAAAI,KAAA,CAAAC,KAAA,GAAAzC,EAAA,CAAA0C,cAAA,CAAuC;;;ADEjE,OAAM,MAAOC,uBAAwB,SAAQ5C,SAAS;EAJtD6C,YAAA;;IAKE,KAAAC,QAAQ,GAAG,CAAC;;EAGZC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrB,KAAK,CAACe,KAAK,CAACO,cAAc,CAAC,QAAQ,CAAC,EAAE;MAC7C,IAAI,CAACC,MAAM,GAAG,IAAI,CAACvB,KAAK,CAACe,KAAK,CAACQ,MAAM;;IAGvC,IAAI,IAAI,CAACvB,KAAK,CAACe,KAAK,CAACO,cAAc,CAAC,QAAQ,CAAC,EAAE;MAC7C,IAAI,CAACE,MAAM,GAAG,IAAI,CAACxB,KAAK,CAACe,KAAK,CAACS,MAAM;;EAEzC;EACAzB,OAAOA,CAACC,KAAwB;IAC9B,IAAIA,KAAK,CAACyB,GAAG,EAAE;MACb,OAAOzB,KAAK,CAAC0B,WAAW,CAAC5C,KAAK;;IAEhC,OAAOkB,KAAK,CAACC,UAAU,GACnBD,KAAK,CAACC,UAAU,CAAC0B,KAAK,CAAEC,CAAC,IAAK,IAAI,CAAC7B,OAAO,CAAC6B,CAAC,CAAC,CAAC,GAC9C,IAAI;EACV;EAEA/B,OAAOA,CAACgC,MAAW,EAAEC,aAAqB;IACxCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,aAAa,CAAC;IAChD,IAAIA,aAAa,IAAI,CAAC,EAAE;MACtB,IAAI,CAACjD,IAAI,CAACoD,cAAc,EAAE;MAC1B,IAAI,CAACb,QAAQ,GAAGU,aAAa,GAAG,CAAC;MACjCD,MAAM,CAACK,MAAM,CAAC,IAAI,CAACd,QAAQ,CAAC;MAC5B,IAAI,IAAI,CAACG,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,IAAI,CAACH,QAAQ,EAAE,IAAI,CAACe,KAAK,CAAC;;EAE3D;EAEA9B,OAAOA,CAACwB,MAAW,EAAEC,aAAqB;IACxC,IAAIA,aAAa,GAAG,CAAC,EAAE;MACrB,IAAI,CAACV,QAAQ,GAAGU,aAAa,GAAG,CAAC;MACjCD,MAAM,CAACK,MAAM,CAAC,IAAI,CAACd,QAAQ,CAAC;MAC5B,IAAI,IAAI,CAACI,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAACe,KAAK,CAAC;;EAE3D;EAAC,QAAAC,CAAA;;;uHAtCUlB,uBAAuB,IAAAmB,CAAA,IAAvBnB,uBAAuB;IAAA;EAAA;EAAA,QAAAoB,EAAA;UAAvBpB,uBAAuB;IAAAqB,SAAA;IAAAC,QAAA,GAAAjE,EAAA,CAAAkE,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNpCxE,EAAA,CAAAC,cAAA,aAA0B;QAElBD,EAAA,CAAAgC,UAAA,IAAA0C,qCAAA,gBAcK;QACT1E,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAA+B,SAAA,aAAwC;QAC5C/B,EAAA,CAAAG,YAAA,EAAM;;;;QAjBuCH,EAAA,CAAAQ,SAAA,GAAqB;QAArBR,EAAA,CAAAI,UAAA,YAAAqE,GAAA,CAAAhD,KAAA,CAAAC,UAAA,CAAqB;QAgBzD1B,EAAA,CAAAQ,SAAA,GAA4B;QAA5BR,EAAA,CAAAI,UAAA,iBAAAe,GAAA,CAA4B", "names": ["FieldType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r6", "form", "valid", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵlistener", "FormlyFieldTabsVertical_li_3_ng_template_3_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "i_r3", "ɵɵnextContext", "index", "ctx_r9", "_r0", "ɵɵreference", "ɵɵresetView", "nextTab", "ctx_r7", "<PERSON><PERSON><PERSON><PERSON>", "field", "fieldGroup", "FormlyFieldTabsVertical_li_3_ng_template_3_button_4_Template_button_click_0_listener", "_r14", "ctx_r13", "prevTab", "ɵɵelement", "ɵɵtemplate", "FormlyFieldTabsVertical_li_3_ng_template_3_button_2_Template", "FormlyFieldTabsVertical_li_3_ng_template_3_button_3_Template", "FormlyFieldTabsVertical_li_3_ng_template_3_button_4_Template", "tab_r2", "last_r4", "FormlyFieldTabsVertical_li_3_ng_template_3_Template", "ctx_r1", "props", "label", "ɵɵsanitizeHtml", "FormlyFieldTabsVertical", "constructor", "activeID", "ngOnInit", "hasOwnProperty", "onNext", "onPrev", "key", "formControl", "every", "f", "ngbNav", "current_index", "console", "log", "mark<PERSON><PERSON>ristine", "select", "model", "_", "t", "_2", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "FormlyFieldTabsVertical_Template", "rf", "ctx", "FormlyFieldTabsVertical_li_3_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\tabs-vertical-type\\tabs-vertical-type.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\tabs-vertical-type\\tabs-vertical-type.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input } from '@angular/core';\r\nimport { FieldType, FormlyFieldConfig } from '@ngx-formly/core';\r\n@Component({\r\n  selector: 'formly-field-tabs',\r\n  templateUrl: './tabs-vertical-type.component.html',\r\n})\r\nexport class FormlyFieldTabsVertical extends FieldType {\r\n  activeID = 0;\r\n  onNext: any;\r\n  onPrev: any;\r\n  ngOnInit() {\r\n    if (this.field.props.hasOwnProperty('onNext')) {\r\n      this.onNext = this.field.props.onNext;\r\n    }\r\n\r\n    if (this.field.props.hasOwnProperty('onPrev')) {\r\n      this.onPrev = this.field.props.onPrev;\r\n    }\r\n  }\r\n  isValid(field: FormlyFieldConfig): boolean {\r\n    if (field.key) {\r\n      return field.formControl.valid;\r\n    }\r\n    return field.fieldGroup\r\n      ? field.fieldGroup.every((f) => this.isValid(f))\r\n      : true;\r\n  }\r\n\r\n  nextTab(ngbNav: any, current_index: number) {\r\n    console.log('Next current_index', current_index);\r\n    if (current_index >= 0) {\r\n      this.form.markAsPristine();\r\n      this.activeID = current_index + 1;\r\n      ngbNav.select(this.activeID);\r\n      if (this.onNext) this.onNext(this.activeID, this.model);\r\n    }\r\n  }\r\n\r\n  prevTab(ngbNav: any, current_index: number) {\r\n    if (current_index > 0) {\r\n      this.activeID = current_index - 1;\r\n      ngbNav.select(this.activeID);\r\n      if (this.onPrev) this.onPrev(this.activeID, this.model);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"nav-vertical\">\r\n    <ul ngbNav #navVertical=\"ngbNav\" class=\"nav nav-tabs nav-left flex-column\">\r\n        <li [ngbNavItem]=\"i\" *ngFor=\"let tab of field.fieldGroup; let i = index; let last = last\"\r\n            [disabled]=\"i !== 0 && !isValid(field.fieldGroup[i - 1])\">\r\n            <a ngbNavLink [innerHTML]=\"tab.props.label|translate\"></a>\r\n            <ng-template ngbNavContent>\r\n                <formly-field [field]=\"tab\"></formly-field>\r\n                <div class=\"d-flex justify-content-between flex-row-reverse\">\r\n                    <button *ngIf=\"last\" class=\"btn btn-primary\" [disabled]=\"!form.valid\"\r\n                        type=\"submit\">{{\"Submit\"|translate}}</button>\r\n                    <button *ngIf=\"!last\" class=\"btn btn-primary\" type=\"button\" (click)=\"nextTab(navVertical,i)\"\r\n                        [disabled]=\"!isValid(field.fieldGroup[i])\">{{'Next'|translate}}</button>\r\n                    <button *ngIf=\"i !== 0\" class=\"btn btn-primary\" type=\"button\"\r\n                        (click)=\"prevTab(navVertical,i)\">{{'Previous'|translate}}</button>\r\n                </div>\r\n            </ng-template>\r\n        </li>\r\n    </ul>\r\n    <div [ngbNavOutlet]=\"navVertical\"></div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
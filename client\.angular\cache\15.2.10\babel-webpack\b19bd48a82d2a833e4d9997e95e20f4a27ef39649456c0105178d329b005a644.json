{"ast": null, "code": "import { AppConfig } from './../app-config';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"@ngx-translate/core\";\nexport class JwtInterceptor {\n  /**\r\n   *\r\n   * @param {AuthenticationService} _authService\r\n   */\n  constructor(_authService, _translateService) {\n    this._authService = _authService;\n    this._translateService = _translateService;\n  }\n  /**\r\n   * Add auth header with jwt if user is logged in and request is to api url\r\n   * @param request\r\n   * @param next\r\n   */\n  intercept(request, next) {\n    const currentUser = this._authService.currentUserValue;\n    const isLoggedIn = currentUser && currentUser.token;\n    const isApiUrl = request.url.startsWith(environment.apiUrl);\n    const currentLang = this._translateService.currentLang ? this._translateService.currentLang : 'en';\n    if (isApiUrl) {\n      request = request.clone({\n        setHeaders: {\n          'X-localization': currentLang,\n          'X-project-id': AppConfig.PROJECT_ID,\n          'X-time-zone': this.timezoneName()\n        }\n      });\n      if (isLoggedIn && isApiUrl) {\n        request = request.clone({\n          setHeaders: {\n            Authorization: `Bearer ${currentUser.token}`\n          }\n        });\n      }\n    }\n    return next.handle(request);\n  }\n  timezoneName() {\n    let timezoneName = Intl.DateTimeFormat('en', {\n      timeZoneName: 'short',\n      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone\n    }).formatToParts().find(part => part.type === 'timeZoneName').value;\n    if (!timezoneName) {\n      timezoneName = '+00:00';\n    }\n    // convert format 'GMT+2' or 'GMT+5:30' or 'GMT-11:30' to 'sign+hh:mm'\n    timezoneName = timezoneName.replace('GMT', '');\n    let sign = timezoneName[0] === '-' ? '-' : '+';\n    // get hours and minutes\n    let [hours, minutes] = timezoneName.replace(sign, '').split(':').map(str => parseInt(str, 10));\n    // build string '+hh:mm' or '-hh:mm'\n    timezoneName = `${sign}${hours < 10 ? '0' : ''}${hours ? hours : '00'}:${minutes < 10 ? '0' : ''}${minutes ? minutes : '00'}`;\n    return timezoneName;\n  }\n  static #_ = this.ɵfac = function JwtInterceptor_Factory(t) {\n    return new (t || JwtInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.TranslateService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: JwtInterceptor,\n    factory: JwtInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,iBAAiB;AAU3C,SAASC,WAAW,QAAQ,0BAA0B;;;;AAItD,OAAM,MAAOC,cAAc;EACzB;;;;EAIAC,YACUC,YAAyB,EACzBC,iBAAmC;IADnC,KAAAD,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;EACxB;EAEH;;;;;EAKAC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAEjB,MAAMC,WAAW,GAAG,IAAI,CAACL,YAAY,CAACM,gBAAgB;IACtD,MAAMC,UAAU,GAAGF,WAAW,IAAIA,WAAW,CAACG,KAAK;IACnD,MAAMC,QAAQ,GAAGN,OAAO,CAACO,GAAG,CAACC,UAAU,CAACd,WAAW,CAACe,MAAM,CAAC;IAC3D,MAAMC,WAAW,GAAG,IAAI,CAACZ,iBAAiB,CAACY,WAAW,GAClD,IAAI,CAACZ,iBAAiB,CAACY,WAAW,GAClC,IAAI;IAER,IAAIJ,QAAQ,EAAE;MACZN,OAAO,GAAGA,OAAO,CAACW,KAAK,CAAC;QACtBC,UAAU,EAAE;UACV,gBAAgB,EAAEF,WAAW;UAC7B,cAAc,EAAEjB,SAAS,CAACoB,UAAU;UACpC,aAAa,EAAE,IAAI,CAACC,YAAY;;OAEnC,CAAC;MAEF,IAAIV,UAAU,IAAIE,QAAQ,EAAE;QAC1BN,OAAO,GAAGA,OAAO,CAACW,KAAK,CAAC;UACtBC,UAAU,EAAE;YACVG,aAAa,EAAE,UAAUb,WAAW,CAACG,KAAK;;SAE7C,CAAC;;;IAIN,OAAOJ,IAAI,CAACe,MAAM,CAAChB,OAAO,CAAC;EAC7B;EAEAc,YAAYA,CAAA;IACV,IAAIA,YAAY,GAAGG,IAAI,CAACC,cAAc,CAAC,IAAI,EAAE;MAC3CC,YAAY,EAAE,OAAO;MACrBC,QAAQ,EAAEH,IAAI,CAACC,cAAc,EAAE,CAACG,eAAe,EAAE,CAACD;KACnD,CAAC,CACCE,aAAa,EAAE,CACfC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,cAAc,CAAC,CAACC,KAAK;IAErD,IAAI,CAACZ,YAAY,EAAE;MACjBA,YAAY,GAAG,QAAQ;;IAEzB;IACAA,YAAY,GAAGA,YAAY,CAACa,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,IAAIC,IAAI,GAAGd,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;IAC9C;IACA,IAAI,CAACe,KAAK,EAAEC,OAAO,CAAC,GAAGhB,YAAY,CAChCa,OAAO,CAACC,IAAI,EAAE,EAAE,CAAC,CACjBG,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,GAAG,IAAKC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC;IAElC;IACAnB,YAAY,GAAG,GAAGc,IAAI,GAAGC,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAGA,KAAK,GAAGA,KAAK,GAAG,IAAI,IACnEC,OAAO,GAAG,EAAE,GAAG,GAAG,GAAG,EACvB,GAAGA,OAAO,GAAGA,OAAO,GAAG,IAAI,EAAE;IAC7B,OAAOhB,YAAY;EACrB;EAAC,QAAAqB,CAAA;qBAxEUxC,cAAc,EAAAyC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;WAAd/C,cAAc;IAAAgD,OAAA,EAAdhD,cAAc,CAAAiD;EAAA", "names": ["AppConfig", "environment", "JwtInterceptor", "constructor", "_authService", "_translateService", "intercept", "request", "next", "currentUser", "currentUserValue", "isLoggedIn", "token", "isApiUrl", "url", "startsWith", "apiUrl", "currentLang", "clone", "setHeaders", "PROJECT_ID", "timezoneName", "Authorization", "handle", "Intl", "DateTimeFormat", "timeZoneName", "timeZone", "resolvedOptions", "formatToParts", "find", "part", "type", "value", "replace", "sign", "hours", "minutes", "split", "map", "str", "parseInt", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "TranslateService", "_2", "factory", "ɵfac"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\helpers\\jwt.interceptor.ts"], "sourcesContent": ["import { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from './../app-config';\r\nimport { Injectable } from '@angular/core';\r\nimport {\r\n  HttpRequest,\r\n  HttpHandler,\r\n  HttpEvent,\r\n  HttpInterceptor,\r\n} from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\n\r\nimport { environment } from 'environments/environment';\r\nimport { AuthService } from 'app/services/auth.service';\r\n\r\n@Injectable()\r\nexport class JwtInterceptor implements HttpInterceptor {\r\n  /**\r\n   *\r\n   * @param {AuthenticationService} _authService\r\n   */\r\n  constructor(\r\n    private _authService: AuthService,\r\n    private _translateService: TranslateService\r\n  ) {}\r\n\r\n  /**\r\n   * Add auth header with jwt if user is logged in and request is to api url\r\n   * @param request\r\n   * @param next\r\n   */\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    const currentUser = this._authService.currentUserValue;\r\n    const isLoggedIn = currentUser && currentUser.token;\r\n    const isApiUrl = request.url.startsWith(environment.apiUrl);\r\n    const currentLang = this._translateService.currentLang\r\n      ? this._translateService.currentLang\r\n      : 'en';\r\n\r\n    if (isApiUrl) {\r\n      request = request.clone({\r\n        setHeaders: {\r\n          'X-localization': currentLang,\r\n          'X-project-id': AppConfig.PROJECT_ID,\r\n          'X-time-zone': this.timezoneName(),\r\n        },\r\n      });\r\n\r\n      if (isLoggedIn && isApiUrl) {\r\n        request = request.clone({\r\n          setHeaders: {\r\n            Authorization: `Bearer ${currentUser.token}`,\r\n          },\r\n        });\r\n      }\r\n    }\r\n\r\n    return next.handle(request);\r\n  }\r\n\r\n  timezoneName() {\r\n    let timezoneName = Intl.DateTimeFormat('en', {\r\n      timeZoneName: 'short',\r\n      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,\r\n    })\r\n      .formatToParts()\r\n      .find((part) => part.type === 'timeZoneName').value;\r\n\r\n    if (!timezoneName) {\r\n      timezoneName = '+00:00';\r\n    }\r\n    // convert format 'GMT+2' or 'GMT+5:30' or 'GMT-11:30' to 'sign+hh:mm'\r\n    timezoneName = timezoneName.replace('GMT', '');\r\n    let sign = timezoneName[0] === '-' ? '-' : '+';\r\n    // get hours and minutes\r\n    let [hours, minutes] = timezoneName\r\n      .replace(sign, '')\r\n      .split(':')\r\n      .map((str) => parseInt(str, 10));\r\n\r\n    // build string '+hh:mm' or '-hh:mm'\r\n    timezoneName = `${sign}${hours < 10 ? '0' : ''}${hours ? hours : '00'}:${\r\n      minutes < 10 ? '0' : ''\r\n    }${minutes ? minutes : '00'}`;\r\n    return timezoneName;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
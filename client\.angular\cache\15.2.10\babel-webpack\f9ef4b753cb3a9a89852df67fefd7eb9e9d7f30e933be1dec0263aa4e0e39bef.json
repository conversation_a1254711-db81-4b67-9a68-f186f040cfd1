{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction BreadcrumbComponent_li_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"routerLink\", link_r1.link);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, link_r1.name));\n  }\n}\nfunction BreadcrumbComponent_li_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, link_r1.name));\n  }\n}\nfunction BreadcrumbComponent_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 3);\n    i0.ɵɵtemplate(1, BreadcrumbComponent_li_2_a_1_Template, 3, 4, \"a\", 4);\n    i0.ɵɵtemplate(2, BreadcrumbComponent_li_2_span_2_Template, 3, 3, \"span\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", link_r1.isLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !link_r1.isLink);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"justify-content-center\": a0,\n    \"justify-content-end\": a1\n  };\n};\nconst _c1 = function (a0, a1, a2, a3, a4, a5) {\n  return {\n    \"breadcrumb-slash\": a0,\n    \"breadcrumb-dots\": a1,\n    \"breadcrumb-dashes\": a2,\n    \"breadcrumb-pipes\": a3,\n    \"breadcrumb-chevron\": a4,\n    \"mr-1\": a5\n  };\n};\nexport class BreadcrumbComponent {\n  constructor() {}\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    // concatenate default properties with passed properties\n    this.breadcrumb = this.breadcrumb;\n  }\n  static #_ = this.ɵfac = function BreadcrumbComponent_Factory(t) {\n    return new (t || BreadcrumbComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BreadcrumbComponent,\n    selectors: [[\"app-breadcrumb\"]],\n    inputs: {\n      breadcrumb: \"breadcrumb\"\n    },\n    decls: 3,\n    vars: 13,\n    consts: [[1, \"breadcrumb-wrapper\", \"d-flex\", \"align-items-center\", 3, \"ngClass\"], [1, \"breadcrumb\", 3, \"ngClass\"], [\"class\", \"breadcrumb-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"breadcrumb-item\"], [3, \"routerLink\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"routerLink\"]],\n    template: function BreadcrumbComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"ol\", 1);\n        i0.ɵɵtemplate(2, BreadcrumbComponent_li_2_Template, 3, 2, \"li\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, ctx.breadcrumb.alignment == \"center\", ctx.breadcrumb.alignment == \"right\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction6(6, _c1, ctx.breadcrumb.type == \"slash\", ctx.breadcrumb.type == \"dots\", ctx.breadcrumb.type == \"dashes\", ctx.breadcrumb.type == \"pipes\", ctx.breadcrumb.type == \"chevron\", ctx.breadcrumb.alignment == \"right\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.breadcrumb.links);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.RouterLink, i3.TranslatePipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;IAoBMA,EAAA,CAAAC,cAAA,WAAoD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA3DH,EAAA,CAAAI,qBAAA,eAAAC,OAAA,CAAAC,IAAA,CAA4B;IAACN,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAS,WAAA,OAAAJ,OAAA,CAAAK,IAAA,EAA0B;;;;;IAC9EV,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjCH,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAS,WAAA,OAAAJ,OAAA,CAAAK,IAAA,EAA0B;;;;;IAFvDV,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAW,UAAA,IAAAC,qCAAA,eAAkF;IAClFZ,EAAA,CAAAW,UAAA,IAAAE,wCAAA,kBAA4D;IAC9Db,EAAA,CAAAG,YAAA,EAAK;;;;IAFCH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAc,UAAA,SAAAT,OAAA,CAAAU,MAAA,CAAiB;IACdf,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAc,UAAA,UAAAT,OAAA,CAAAU,MAAA,CAAkB;;;;;;;;;;;;;;;;;;;ACJ/B,OAAM,MAAOC,mBAAmB;EAI9BC,YAAA,GAAe;EAEf;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU;EACnC;EAAC,QAAAC,CAAA;qBAfUJ,mBAAmB;EAAA;EAAA,QAAAK,EAAA;UAAnBL,mBAAmB;IAAAM,SAAA;IAAAC,MAAA;MAAAJ,UAAA;IAAA;IAAAK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDhBhC7B,EAAA,CAAAC,cAAA,aAMC;QAYGD,EAAA,CAAAW,UAAA,IAAAoB,iCAAA,gBAGK;QACP/B,EAAA,CAAAG,YAAA,EAAK;;;QApBLH,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAAH,GAAA,CAAAX,UAAA,CAAAe,SAAA,cAAAJ,GAAA,CAAAX,UAAA,CAAAe,SAAA,aAGE;QAIAlC,EAAA,CAAAO,SAAA,GAOE;QAPFP,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAmC,eAAA,IAAAC,GAAA,EAAAN,GAAA,CAAAX,UAAA,CAAAkB,IAAA,aAAAP,GAAA,CAAAX,UAAA,CAAAkB,IAAA,YAAAP,GAAA,CAAAX,UAAA,CAAAkB,IAAA,cAAAP,GAAA,CAAAX,UAAA,CAAAkB,IAAA,aAAAP,GAAA,CAAAX,UAAA,CAAAkB,IAAA,eAAAP,GAAA,CAAAX,UAAA,CAAAe,SAAA,aAOE;QAE2ClC,EAAA,CAAAO,SAAA,GAAmB;QAAnBP,EAAA,CAAAc,UAAA,YAAAgB,GAAA,CAAAX,UAAA,CAAAmB,KAAA,CAAmB", "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵpropertyInterpolate", "link_r1", "link", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "name", "ɵɵtemplate", "BreadcrumbComponent_li_2_a_1_Template", "BreadcrumbComponent_li_2_span_2_Template", "ɵɵproperty", "isLink", "BreadcrumbComponent", "constructor", "ngOnInit", "breadcrumb", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "BreadcrumbComponent_Template", "rf", "ctx", "BreadcrumbComponent_li_2_Template", "ɵɵpureFunction2", "_c0", "alignment", "ɵɵpureFunction6", "_c1", "type", "links"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content-header\\breadcrumb\\breadcrumb.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content-header\\breadcrumb\\breadcrumb.component.ts"], "sourcesContent": ["<!-- app-breadcrumb start -->\r\n<div\r\n  class=\"breadcrumb-wrapper d-flex align-items-center\"\r\n  [ngClass]=\"{\r\n    'justify-content-center': breadcrumb.alignment == 'center',\r\n    'justify-content-end': breadcrumb.alignment == 'right'\r\n  }\"\r\n>\r\n  <ol\r\n    class=\"breadcrumb\"\r\n    [ngClass]=\"{\r\n      'breadcrumb-slash': breadcrumb.type == 'slash',\r\n      'breadcrumb-dots': breadcrumb.type == 'dots',\r\n      'breadcrumb-dashes': breadcrumb.type == 'dashes',\r\n      'breadcrumb-pipes': breadcrumb.type == 'pipes',\r\n      'breadcrumb-chevron': breadcrumb.type == 'chevron',\r\n      'mr-1': breadcrumb.alignment == 'right'\r\n    }\"\r\n  >\r\n    <li class=\"breadcrumb-item\" *ngFor=\"let link of breadcrumb.links\">\r\n      <a *ngIf=\"link.isLink\" routerLink=\"{{ link.link }}\">{{ link.name | translate}}</a>\r\n      <span *ngIf=\"!link.isLink\">{{ link.name | translate}}</span>\r\n    </li>\r\n  </ol>\r\n</div>\r\n<!-- app-breadcrumb end -->\r\n", "import { Component, OnInit, Input } from '@angular/core';\r\n\r\n// Breadcrumb component interface\r\nexport interface Breadcrumb {\r\n  type?: string;\r\n  alignment?: string;\r\n  links?: Array<{\r\n    name: string;\r\n    isLink: boolean;\r\n    link?: string;\r\n  }>;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-breadcrumb',\r\n  templateUrl: './breadcrumb.component.html'\r\n})\r\nexport class BreadcrumbComponent implements OnInit {\r\n  // input variable\r\n  @Input() breadcrumb: Breadcrumb;\r\n\r\n  constructor() {}\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit() {\r\n    // concatenate default properties with passed properties\r\n    this.breadcrumb = this.breadcrumb;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
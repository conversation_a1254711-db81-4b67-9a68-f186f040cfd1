{"ast": null, "code": "import { scan } from './scan';\nimport { takeLast } from './takeLast';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { pipe } from '../util/pipe';\nexport function reduce(accumulator, seed) {\n  if (arguments.length >= 2) {\n    return function reduceOperatorFunctionWithSeed(source) {\n      return pipe(scan(accumulator, seed), takeLast(1), defaultIfEmpty(seed))(source);\n    };\n  }\n  return function reduceOperatorFunction(source) {\n    return pipe(scan((acc, value, index) => accumulator(acc, value, index + 1)), takeLast(1))(source);\n  };\n}", "map": {"version": 3, "names": ["scan", "takeLast", "defaultIfEmpty", "pipe", "reduce", "accumulator", "seed", "arguments", "length", "reduceOperatorFunctionWithSeed", "source", "reduceOperatorFunction", "acc", "value", "index"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/reduce.js"], "sourcesContent": ["import { scan } from './scan';\nimport { takeLast } from './takeLast';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { pipe } from '../util/pipe';\nexport function reduce(accumulator, seed) {\n    if (arguments.length >= 2) {\n        return function reduceOperatorFunctionWithSeed(source) {\n            return pipe(scan(accumulator, seed), takeLast(1), defaultIfEmpty(seed))(source);\n        };\n    }\n    return function reduceOperatorFunction(source) {\n        return pipe(scan((acc, value, index) => accumulator(acc, value, index + 1)), takeLast(1))(source);\n    };\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,SAASC,MAAMA,CAACC,WAAW,EAAEC,IAAI,EAAE;EACtC,IAAIC,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE;IACvB,OAAO,SAASC,8BAA8BA,CAACC,MAAM,EAAE;MACnD,OAAOP,IAAI,CAACH,IAAI,CAACK,WAAW,EAAEC,IAAI,CAAC,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEC,cAAc,CAACI,IAAI,CAAC,CAAC,CAACI,MAAM,CAAC;IACnF,CAAC;EACL;EACA,OAAO,SAASC,sBAAsBA,CAACD,MAAM,EAAE;IAC3C,OAAOP,IAAI,CAACH,IAAI,CAAC,CAACY,GAAG,EAAEC,KAAK,EAAEC,KAAK,KAAKT,WAAW,CAACO,GAAG,EAAEC,KAAK,EAAEC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACS,MAAM,CAAC;EACrG,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
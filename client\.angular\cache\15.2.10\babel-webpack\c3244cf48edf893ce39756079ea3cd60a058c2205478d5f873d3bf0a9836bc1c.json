{"ast": null, "code": "export const observable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();", "map": {"version": 3, "names": ["observable", "Symbol"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/symbol/observable.js"], "sourcesContent": ["export const observable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG,CAAC,MAAM,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACD,UAAU,IAAI,cAAc,EAAE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
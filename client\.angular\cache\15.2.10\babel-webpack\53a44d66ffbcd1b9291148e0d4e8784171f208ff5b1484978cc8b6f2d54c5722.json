{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Dutch (Belgium) [nl-be]\n//! author : <PERSON><PERSON> : https://github.com/jorisroling\n//! author : <PERSON> : https://github.com/middagj\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsShortWithDots = 'jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n    monthsShortWithoutDots = 'jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n    monthsParse = [/^jan/i, /^feb/i, /^maart|mrt.?$/i, /^apr/i, /^mei$/i, /^jun[i.]?$/i, /^jul[i.]?$/i, /^aug/i, /^sep/i, /^okt/i, /^nov/i, /^dec/i],\n    monthsRegex = /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\\.?|feb\\.?|mrt\\.?|apr\\.?|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i;\n  var nlBe = moment.defineLocale('nl-be', {\n    months: 'januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december'.split('_'),\n    monthsShort: function (m, format) {\n      if (!m) {\n        return monthsShortWithDots;\n      } else if (/-MMM-/.test(format)) {\n        return monthsShortWithoutDots[m.month()];\n      } else {\n        return monthsShortWithDots[m.month()];\n      }\n    },\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,\n    monthsShortStrictRegex: /^(jan\\.?|feb\\.?|mrt\\.?|apr\\.?|mei|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag'.split('_'),\n    weekdaysShort: 'zo._ma._di._wo._do._vr._za.'.split('_'),\n    weekdaysMin: 'zo_ma_di_wo_do_vr_za'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[vandaag om] LT',\n      nextDay: '[morgen om] LT',\n      nextWeek: 'dddd [om] LT',\n      lastDay: '[gisteren om] LT',\n      lastWeek: '[afgelopen] dddd [om] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'over %s',\n      past: '%s geleden',\n      s: 'een paar seconden',\n      ss: '%d seconden',\n      m: 'één minuut',\n      mm: '%d minuten',\n      h: 'één uur',\n      hh: '%d uur',\n      d: 'één dag',\n      dd: '%d dagen',\n      M: 'één maand',\n      MM: '%d maanden',\n      y: 'één jaar',\n      yy: '%d jaar'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n    ordinal: function (number) {\n      return number + (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return nlBe;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "monthsShortWithDots", "split", "monthsShortWithoutDots", "<PERSON><PERSON><PERSON>e", "monthsRegex", "nlBe", "defineLocale", "months", "monthsShort", "m", "format", "test", "month", "monthsShortRegex", "monthsStrictRegex", "monthsShortStrictRegex", "longMonthsParse", "shortMonthsParse", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/moment/locale/nl-be.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Dutch (Belgium) [nl-be]\n//! author : <PERSON><PERSON> : https://github.com/jorisroling\n//! author : <PERSON> : https://github.com/middagj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var monthsShortWithDots =\n            'jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n        monthsShortWithoutDots =\n            'jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n        monthsParse = [\n            /^jan/i,\n            /^feb/i,\n            /^maart|mrt.?$/i,\n            /^apr/i,\n            /^mei$/i,\n            /^jun[i.]?$/i,\n            /^jul[i.]?$/i,\n            /^aug/i,\n            /^sep/i,\n            /^okt/i,\n            /^nov/i,\n            /^dec/i,\n        ],\n        monthsRegex =\n            /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\\.?|feb\\.?|mrt\\.?|apr\\.?|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i;\n\n    var nlBe = moment.defineLocale('nl-be', {\n        months: 'januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december'.split(\n            '_'\n        ),\n        monthsShort: function (m, format) {\n            if (!m) {\n                return monthsShortWithDots;\n            } else if (/-MMM-/.test(format)) {\n                return monthsShortWithoutDots[m.month()];\n            } else {\n                return monthsShortWithDots[m.month()];\n            }\n        },\n\n        monthsRegex: monthsRegex,\n        monthsShortRegex: monthsRegex,\n        monthsStrictRegex:\n            /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,\n        monthsShortStrictRegex:\n            /^(jan\\.?|feb\\.?|mrt\\.?|apr\\.?|mei|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: monthsParse,\n\n        weekdays:\n            'zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag'.split('_'),\n        weekdaysShort: 'zo._ma._di._wo._do._vr._za.'.split('_'),\n        weekdaysMin: 'zo_ma_di_wo_do_vr_za'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[vandaag om] LT',\n            nextDay: '[morgen om] LT',\n            nextWeek: 'dddd [om] LT',\n            lastDay: '[gisteren om] LT',\n            lastWeek: '[afgelopen] dddd [om] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'over %s',\n            past: '%s geleden',\n            s: 'een paar seconden',\n            ss: '%d seconden',\n            m: 'één minuut',\n            mm: '%d minuten',\n            h: 'één uur',\n            hh: '%d uur',\n            d: 'één dag',\n            dd: '%d dagen',\n            M: 'één maand',\n            MM: '%d maanden',\n            y: 'één jaar',\n            yy: '%d jaar',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n        ordinal: function (number) {\n            return (\n                number +\n                (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de')\n            );\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return nlBe;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,mBAAmB,GACf,4DAA4D,CAACC,KAAK,CAAC,GAAG,CAAC;IAC3EC,sBAAsB,GAClB,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IAChEE,WAAW,GAAG,CACV,OAAO,EACP,OAAO,EACP,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACR,aAAa,EACb,aAAa,EACb,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACV;IACDC,WAAW,GACP,oKAAoK;EAE5K,IAAIC,IAAI,GAAGN,MAAM,CAACO,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,yFAAyF,CAACN,KAAK,CACnG,GACJ,CAAC;IACDO,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAEC,MAAM,EAAE;MAC9B,IAAI,CAACD,CAAC,EAAE;QACJ,OAAOT,mBAAmB;MAC9B,CAAC,MAAM,IAAI,OAAO,CAACW,IAAI,CAACD,MAAM,CAAC,EAAE;QAC7B,OAAOR,sBAAsB,CAACO,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;MAC5C,CAAC,MAAM;QACH,OAAOZ,mBAAmB,CAACS,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;MACzC;IACJ,CAAC;IAEDR,WAAW,EAAEA,WAAW;IACxBS,gBAAgB,EAAET,WAAW;IAC7BU,iBAAiB,EACb,2FAA2F;IAC/FC,sBAAsB,EAClB,kFAAkF;IAEtFZ,WAAW,EAAEA,WAAW;IACxBa,eAAe,EAAEb,WAAW;IAC5Bc,gBAAgB,EAAEd,WAAW;IAE7Be,QAAQ,EACJ,4DAA4D,CAACjB,KAAK,CAAC,GAAG,CAAC;IAC3EkB,aAAa,EAAE,6BAA6B,CAAClB,KAAK,CAAC,GAAG,CAAC;IACvDmB,WAAW,EAAE,sBAAsB,CAACnB,KAAK,CAAC,GAAG,CAAC;IAC9CoB,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,0BAA0B;MACpCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,YAAY;MAClBC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,aAAa;MACjB/B,CAAC,EAAE,YAAY;MACfgC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,iBAAiB;IACzCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OACIA,MAAM,IACLA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,IAAI,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;IAErE,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOlD,IAAI;AAEf,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
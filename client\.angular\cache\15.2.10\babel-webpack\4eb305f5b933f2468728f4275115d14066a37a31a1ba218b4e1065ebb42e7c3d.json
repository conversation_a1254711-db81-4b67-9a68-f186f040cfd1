{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/config.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"app/layout/components/footer/scroll-to-top/scroll-top.component\";\nfunction FooterComponent_app_scroll_top_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-scroll-top\");\n  }\n}\nexport class FooterComponent {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {CoreConfigService} _coreConfigService\r\n   */\n  constructor(_coreConfigService) {\n    this._coreConfigService = _coreConfigService;\n    this.year = new Date().getFullYear();\n    // Set the private defaults\n    this._unsubscribeAll = new Subject();\n  }\n  // Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    // Subscribe to config changes\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      this.coreConfig = config;\n    });\n  }\n  /**\r\n   * On destroy\r\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next();\n    this._unsubscribeAll.complete();\n  }\n  static #_ = this.ɵfac = function FooterComponent_Factory(t) {\n    return new (t || FooterComponent)(i0.ɵɵdirectiveInject(i1.CoreConfigService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FooterComponent,\n    selectors: [[\"footer\"]],\n    decls: 7,\n    vars: 1,\n    consts: [[1, \"clearfix\", \"mb-0\"], [1, \"text-center\", \"d-block\", \"mt-25\"], [\"src\", \"assets/images/ico/icon-72x72.png\", \"alt\", \"logo\", \"height\", \"14\"], [\"href\", \"https://www.ezactive.com/\", \"target\", \"_blank\", 1, \"ml-25\"], [4, \"ngIf\"]],\n    template: function FooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\", 0)(1, \"span\", 1);\n        i0.ɵɵtext(2, \" Powered by \");\n        i0.ɵɵelement(3, \"img\", 2);\n        i0.ɵɵelementStart(4, \"a\", 3);\n        i0.ɵɵtext(5, \"EZ ACTIVE\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(6, FooterComponent_app_scroll_top_6_Template, 1, 0, \"app-scroll-top\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.coreConfig.layout.scrollTop);\n      }\n    },\n    dependencies: [i2.NgIf, i3.ScrollTopComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;ICY1CC,EAAA,CAAAC,SAAA,qBAAqE;;;ADJrE,OAAM,MAAOC,eAAe;EAO1B;;;;;EAKAC,YAAmBC,kBAAqC;IAArC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAV9B,KAAAC,IAAI,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAW5C;IACA,IAAI,CAACC,eAAe,GAAG,IAAIV,OAAO,EAAE;EACtC;EAEA;EACA;EAEA;;;EAGAW,QAAQA,CAAA;IACN;IACA,IAAI,CAACL,kBAAkB,CAACM,MAAM,CAACC,IAAI,CAACZ,SAAS,CAAC,IAAI,CAACS,eAAe,CAAC,CAAC,CAACI,SAAS,CAACF,MAAM,IAAG;MACtF,IAAI,CAACG,UAAU,GAAGH,MAAM;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAI,WAAWA,CAAA;IACT;IACA,IAAI,CAACN,eAAe,CAACO,IAAI,EAAE;IAC3B,IAAI,CAACP,eAAe,CAACQ,QAAQ,EAAE;EACjC;EAAC,QAAAC,CAAA;qBArCUf,eAAe,EAAAF,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAAfnB,eAAe;IAAAoB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV5B5B,EAAA,CAAA8B,cAAA,WAAyB;QAErB9B,EAAA,CAAA+B,MAAA,mBACA;QAAA/B,EAAA,CAAAC,SAAA,aAAqE;QACrED,EAAA,CAAA8B,cAAA,WAAkE;QAAA9B,EAAA,CAAA+B,MAAA,gBAAS;QAAA/B,EAAA,CAAAgC,YAAA,EAAI;QAUnFhC,EAAA,CAAAiC,UAAA,IAAAC,yCAAA,4BAAqE;;;QAApDlC,EAAA,CAAAmC,SAAA,GAAiC;QAAjCnC,EAAA,CAAAoC,UAAA,SAAAP,GAAA,CAAAhB,UAAA,CAAAwB,MAAA,CAAAC,SAAA,CAAiC", "names": ["Subject", "takeUntil", "i0", "ɵɵelement", "FooterComponent", "constructor", "_coreConfigService", "year", "Date", "getFullYear", "_unsubscribeAll", "ngOnInit", "config", "pipe", "subscribe", "coreConfig", "ngOnDestroy", "next", "complete", "_", "ɵɵdirectiveInject", "i1", "CoreConfigService", "_2", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "FooterComponent_app_scroll_top_6_Template", "ɵɵadvance", "ɵɵproperty", "layout", "scrollTop"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\footer\\footer.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\footer\\footer.component.html"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Component } from '@angular/core';\r\n\r\nimport { Subject } from 'rxjs';\r\nimport { takeUntil } from 'rxjs/operators';\r\n\r\nimport { CoreConfigService } from '@core/services/config.service';\r\n\r\n@Component({\r\n  selector: 'footer',\r\n  templateUrl: './footer.component.html'\r\n})\r\nexport class FooterComponent implements OnInit, OnDestroy {\r\n  public coreConfig: any;\r\n  public year: number = new Date().getFullYear();\r\n\r\n  // Private\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {CoreConfigService} _coreConfigService\r\n   */\r\n  constructor(public _coreConfigService: CoreConfigService) {\r\n    // Set the private defaults\r\n    this._unsubscribeAll = new Subject();\r\n  }\r\n\r\n  // Lifecycle hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit(): void {\r\n    // Subscribe to config changes\r\n    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\r\n      this.coreConfig = config;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * On destroy\r\n   */\r\n  ngOnDestroy(): void {\r\n    // Unsubscribe from all subscriptions\r\n    this._unsubscribeAll.next();\r\n    this._unsubscribeAll.complete();\r\n  }\r\n}\r\n", "<!-- Footer -->\r\n<p class=\"clearfix mb-0\">\r\n  <span class=\"text-center d-block mt-25\">\r\n    Powered by\r\n    <img src=\"assets/images/ico/icon-72x72.png\" alt=\"logo\" height=\"14\" />\r\n    <a class=\"ml-25\" href=\"https://www.ezactive.com/\" target=\"_blank\">EZ ACTIVE</a>\r\n    <!-- <span class=\"d-none d-sm-inline-block\">, All rights Reserved</span> -->\r\n  </span>\r\n  <!-- <span class=\"float-md-right d-none d-md-block\">\r\n    Hand-crafted & Made by <span [data-feather]=\"'heart'\" [class]=\"'pink'\"></span>\r\n  </span> -->\r\n</p>\r\n<!--/ Footer -->\r\n\r\n<!-- Move to top Button-->\r\n<app-scroll-top *ngIf=\"coreConfig.layout.scrollTop\"></app-scroll-top>\r\n\r\n<!-- Buynow Button-->\r\n<!-- <div class=\"buy-now\">\r\n  <a href=\"https://1.envato.market/vuexy_admin\" target=\"_blank\" class=\"btn btn-danger\" *ngIf=\"coreConfig.layout.buyNow\"\r\n    >Buy Now</a\r\n  >\r\n</div> -->"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
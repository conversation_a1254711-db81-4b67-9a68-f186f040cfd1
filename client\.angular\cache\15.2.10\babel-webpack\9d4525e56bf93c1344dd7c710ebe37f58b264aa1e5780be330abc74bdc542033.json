{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/user.service\";\nimport * as i2 from \"app/services/auth.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@ngx-formly/core\";\nfunction EditProfileComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"a\", 8);\n    i0.ɵɵlistener(\"click\", function EditProfileComponent_div_4_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openModal());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"Your email has not been verified.\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, \"Resend request verification email\"));\n  }\n}\nfunction EditProfileComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 9)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 1)(6, \"p\");\n    i0.ɵɵtext(7, \" Delete your account and all your data. This action is irreversible. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function EditProfileComponent_div_8_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.deleteAccount());\n    });\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"Delete Your Account\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(11, 4, \"Delete Account\"), \" \");\n  }\n}\nexport class EditProfileComponent {\n  constructor(_userService, _authService, _trans, _loadingService) {\n    this._userService = _userService;\n    this._authService = _authService;\n    this._trans = _trans;\n    this._loadingService = _loadingService;\n    this.form = new FormGroup({});\n    this.model = {};\n    this.options = {};\n  }\n  submit() {\n    if (!this.form.valid) return;\n    let loading = this._loadingService.show();\n    console.log(this.user.email, \"/\", this.model.email);\n    if (this.user.email.trim() != this.model.email.trim()) {\n      this._authService.sendVerificationEmail(this.user.email, this.model.email).subscribe(res => {\n        this._authService.getProfile().subscribe(res => {\n          Swal.fire({\n            title: this._trans.instant('Information'),\n            icon: 'info',\n            text: this._trans.instant('The confirmation email has been sent to you. Please check your email!')\n          });\n        });\n      }, err => {\n        console.log(err);\n        Swal.fire({\n          title: this._trans.instant('Error'),\n          icon: 'error',\n          text: this._trans.instant(err.message)\n        });\n      });\n    } else {\n      this._userService.update(this.model).toPromise().then(res => {\n        let message = res.message;\n        // console.log(res);\n        this._authService.getProfile().subscribe(res => {\n          Swal.fire({\n            title: this._trans.instant('Success'),\n            icon: 'success',\n            text: message\n          });\n        });\n      }, err => {\n        // console.log(err);\n        if (err.hasOwnProperty('errors')) {\n          let errors = err.errors;\n          for (let key in errors) {\n            if (errors.hasOwnProperty(key)) {\n              this.form.get(key).setErrors({\n                serverError: errors[key]\n              });\n            }\n          }\n        }\n      });\n    }\n  }\n  ngOnInit() {\n    this.model = {\n      first_name: this.user.first_name,\n      last_name: this.user.last_name,\n      email: this.user.email.trim()\n    };\n    if (this.user.verify_email) {\n      this.model.email = this.user.verify_email;\n    }\n    setTimeout(() => {\n      this.fields = [{\n        fieldGroupClassName: 'row',\n        fieldGroup: [{\n          className: 'col-6',\n          type: 'input',\n          key: 'first_name',\n          props: {\n            label: this._trans.instant('Surname'),\n            required: true\n          }\n        }, {\n          className: 'col-6',\n          type: 'input',\n          key: 'last_name',\n          props: {\n            label: this._trans.instant('Other names'),\n            required: true\n          }\n        }]\n      },\n      // {\n      //   className: 'section-label',\n      //   template: '<hr /><div><strong>Address:</strong></div>',\n      // },\n      {\n        fieldGroupClassName: 'row',\n        fieldGroup: [{\n          className: 'col col-md-6',\n          type: 'input',\n          key: 'email',\n          props: {\n            label: this._trans.instant('Email'),\n            required: true,\n            blur: event => {\n              this.model.email = event.target.value.trim();\n            }\n          }\n        }]\n      }];\n    }, 500);\n  }\n  deleteAccount() {\n    Swal.fire({\n      title: this._trans.instant('Are you sure?'),\n      text: this._trans.instant('Deleting your account will remove all of your information from our database. This cannot be undone.'),\n      input: 'text',\n      inputPlaceholder: 'Enter DELETE to confirm',\n      icon: 'warning',\n      reverseButtons: true,\n      showCancelButton: true,\n      confirmButtonText: 'Delete',\n      customClass: {\n        confirmButton: 'btn btn-danger',\n        cancelButton: 'btn btn-outline-danger'\n      },\n      onBeforeOpen: function (ele) {\n        $(ele).find('button.swal2-confirm.swal2-styled').toggleClass('swal2-confirm');\n        $(ele).find('button.swal2-cancel.swal2-styled').toggleClass('swal2-cancel');\n      },\n      inputValidator: value => {\n        if (value !== 'DELETE') {\n          return 'You need to type DELETE to confirm';\n        }\n      }\n    }).then(result => {\n      if (result.value) {\n        if (result.value === 'DELETE') {\n          console.log('delete account');\n          this._userService.deleteUser(this.user.id).subscribe(res => {\n            this._authService.logout();\n          });\n        }\n      }\n    });\n  }\n  openModal() {\n    console.log(this.user, \"/\", this.model.email);\n    if (this.user.verify_email) {\n      this._authService.resendVerificationEmail(this.user.id).subscribe(res => {\n        Swal.fire({\n          title: this._trans.instant('Success'),\n          html: ` <div class=\"text-center\">\n                <img src=\"assets/images/icons//Successful Illustration (1).png\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n                <h3>The authentication link has been sent to you!</h3>\n                <p class=\"text-center\" style=\"margin-bottom: 0px;\">\n                  The authentication link has been sent to \n                </p>\n                <b>${this.user.verify_email}</b>\n              </div>`,\n          // icon: 'error',\n          confirmButtonText: 'Ok',\n          confirmButtonColor: '#7367f0'\n        });\n      }, err => {\n        console.log(err);\n        Swal.fire({\n          title: this._trans.instant('Error'),\n          icon: 'error',\n          text: this._trans.instant(err.message)\n        });\n      });\n    }\n  }\n  isTestAccount() {\n    return this.user.email === '<EMAIL>';\n  }\n  static #_ = this.ɵfac = function EditProfileComponent_Factory(t) {\n    return new (t || EditProfileComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.LoadingService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EditProfileComponent,\n    selectors: [[\"edit-profile\"]],\n    inputs: {\n      user: \"user\"\n    },\n    decls: 9,\n    vars: 11,\n    consts: [[1, \"card\"], [1, \"card-body\"], [3, \"formGroup\", \"ngSubmit\"], [3, \"model\", \"fields\", \"options\", \"form\"], [\"class\", \"pb-1\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"submit-button\", 3, \"disabled\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"pb-1\"], [\"href\", \"javascript:void(0);\", 3, \"click\"], [1, \"card-header\"], [1, \"btn\", \"btn-danger\", 3, \"click\"]],\n    template: function EditProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function EditProfileComponent_Template_form_ngSubmit_2_listener() {\n          return ctx.submit();\n        });\n        i0.ɵɵelement(3, \"formly-form\", 3);\n        i0.ɵɵtemplate(4, EditProfileComponent_div_4_Template, 6, 6, \"div\", 4);\n        i0.ɵɵelementStart(5, \"button\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(8, EditProfileComponent_div_8_Template, 12, 6, \"div\", 6);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"model\", ctx.model)(\"fields\", ctx.fields)(\"options\", ctx.options)(\"form\", ctx.form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.user == null ? null : ctx.user.verify_email) != null);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", !ctx.form.valid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 9, \"Save\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isTestAccount());\n      }\n    },\n    dependencies: [i5.NgIf, i6.ɵNgNoValidate, i6.NgControlStatusGroup, i6.FormGroupDirective, i7.FormlyForm, i3.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;AAM1C,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;ICHlBC,EAAA,CAAAC,cAAA,aAA2D;IACvDD,EAAA,CAAAE,MAAA,GACA;;IAAAF,EAAA,CAAAC,cAAA,WAAoD;IAAtBD,EAAA,CAAAG,UAAA,mBAAAC,uDAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACV,EAAA,CAAAE,MAAA,GAAmD;;IAAAF,EAAA,CAAAW,YAAA,EAAI;;;IAD3GX,EAAA,CAAAY,SAAA,GACA;IADAZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,iDACA;IAAoDd,EAAA,CAAAY,SAAA,GAAmD;IAAnDZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,4CAAmD;;;;;;IASvHd,EAAA,CAAAC,cAAA,aAA0C;IAE9BD,EAAA,CAAAE,MAAA,GAAqC;;IAAAF,EAAA,CAAAW,YAAA,EAAK;IAElDX,EAAA,CAAAC,cAAA,aAAuB;IAEfD,EAAA,CAAAE,MAAA,4EACJ;IAAAF,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAAC,cAAA,iBAAyD;IAA1BD,EAAA,CAAAG,UAAA,mBAAAa,4DAAA;MAAAhB,EAAA,CAAAK,aAAA,CAAAY,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAS,MAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IACpDnB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAgC;;IAAAF,EAAA,CAAAW,YAAA,EAAS;;;IAPjDX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAc,WAAA,8BAAqC;IAO7Bd,EAAA,CAAAY,SAAA,GAAgC;IAAhCZ,EAAA,CAAAa,kBAAA,KAAAb,EAAA,CAAAc,WAAA,+BAAgC;;;ADVpD,OAAM,MAAOM,oBAAoB;EAC/BC,YACSC,YAAyB,EACzBC,YAAyB,EACzBC,MAAwB,EACxBC,eAA+B;IAH/B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAGxB,KAAAC,IAAI,GAAG,IAAI5B,SAAS,CAAC,EAAE,CAAC;IACxB,KAAA6B,KAAK,GAAQ,EAAE;IACf,KAAAC,OAAO,GAAsB,EAAE;EAJ5B;EAQHC,MAAMA,CAAA;IACJ,IAAI,CAAC,IAAI,CAACH,IAAI,CAACI,KAAK,EAAE;IACtB,IAAIC,OAAO,GAAG,IAAI,CAACN,eAAe,CAACO,IAAI,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,IAAI,CAACC,KAAK,EAAC,GAAG,EAAC,IAAI,CAACT,KAAK,CAACS,KAAK,CAAC;IACjD,IAAI,IAAI,CAACD,IAAI,CAACC,KAAK,CAACC,IAAI,EAAE,IAAI,IAAI,CAACV,KAAK,CAACS,KAAK,CAACC,IAAI,EAAE,EAAE;MACrD,IAAI,CAACd,YAAY,CAACe,qBAAqB,CAAC,IAAI,CAACH,IAAI,CAACC,KAAK,EAAE,IAAI,CAACT,KAAK,CAACS,KAAK,CAAC,CAACG,SAAS,CACjFC,GAAG,IAAI;QACN,IAAI,CAACjB,YAAY,CAACkB,UAAU,EAAE,CAACF,SAAS,CAAEC,GAAG,IAAI;UAC/CzC,IAAI,CAAC2C,IAAI,CAAC;YACRC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACoB,OAAO,CAAC,aAAa,CAAC;YACzCC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,IAAI,CAACtB,MAAM,CAACoB,OAAO,CAAC,uEAAuE;WAClG,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,EACAG,GAAG,IAAI;QACNd,OAAO,CAACC,GAAG,CAACa,GAAG,CAAC;QAChBhD,IAAI,CAAC2C,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACoB,OAAO,CAAC,OAAO,CAAC;UACnCC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,IAAI,CAACtB,MAAM,CAACoB,OAAO,CAACG,GAAG,CAACC,OAAO;SACtC,CAAC;MACJ,CAAC,CACF;KACF,MACK;MACJ,IAAI,CAAC1B,YAAY,CAChB2B,MAAM,CAAC,IAAI,CAACtB,KAAK,CAAC,CAClBuB,SAAS,EAAE,CACXC,IAAI,CACFX,GAAG,IAAI;QACN,IAAIQ,OAAO,GAAGR,GAAG,CAACQ,OAAO;QACzB;QACA,IAAI,CAACzB,YAAY,CAACkB,UAAU,EAAE,CAACF,SAAS,CAAEC,GAAG,IAAI;UAC/CzC,IAAI,CAAC2C,IAAI,CAAC;YACRC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACoB,OAAO,CAAC,SAAS,CAAC;YACrCC,IAAI,EAAE,SAAS;YACfC,IAAI,EAAEE;WACP,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,EACAD,GAAG,IAAI;QACN;QACA,IAAIA,GAAG,CAACK,cAAc,CAAC,QAAQ,CAAC,EAAE;UAChC,IAAIC,MAAM,GAAGN,GAAG,CAACM,MAAM;UACvB,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;YACtB,IAAIA,MAAM,CAACD,cAAc,CAACE,GAAG,CAAC,EAAE;cAC9B,IAAI,CAAC5B,IAAI,CAAC6B,GAAG,CAACD,GAAG,CAAC,CAACE,SAAS,CAAC;gBAAEC,WAAW,EAAEJ,MAAM,CAACC,GAAG;cAAC,CAAE,CAAC;;;;MAIlE,CAAC,CACF;;EAEL;EACAI,QAAQA,CAAA;IACN,IAAI,CAAC/B,KAAK,GAAG;MACXgC,UAAU,EAAE,IAAI,CAACxB,IAAI,CAACwB,UAAU;MAChCC,SAAS,EAAE,IAAI,CAACzB,IAAI,CAACyB,SAAS;MAC9BxB,KAAK,EAAE,IAAI,CAACD,IAAI,CAACC,KAAK,CAACC,IAAI;KAC5B;IACD,IAAG,IAAI,CAACF,IAAI,CAAC0B,YAAY,EAAC;MACxB,IAAI,CAAClC,KAAK,CAACS,KAAK,GAAG,IAAI,CAACD,IAAI,CAAC0B,YAAY;;IAE3CC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,MAAM,GAAG,CACZ;QACEC,mBAAmB,EAAE,KAAK;QAC1BC,UAAU,EAAE,CACV;UACEC,SAAS,EAAE,OAAO;UAClBC,IAAI,EAAE,OAAO;UACbb,GAAG,EAAE,YAAY;UACjBc,KAAK,EAAE;YACLC,KAAK,EAAE,IAAI,CAAC7C,MAAM,CAACoB,OAAO,CAAC,SAAS,CAAC;YACrC0B,QAAQ,EAAE;;SAEb,EACD;UACEJ,SAAS,EAAE,OAAO;UAClBC,IAAI,EAAE,OAAO;UACbb,GAAG,EAAE,WAAW;UAChBc,KAAK,EAAE;YACLC,KAAK,EAAE,IAAI,CAAC7C,MAAM,CAACoB,OAAO,CAAC,aAAa,CAAC;YACzC0B,QAAQ,EAAE;;SAEb;OAEJ;MACD;MACA;MACA;MACA;MACA;QACEN,mBAAmB,EAAE,KAAK;QAC1BC,UAAU,EAAE,CACV;UACEC,SAAS,EAAE,cAAc;UACzBC,IAAI,EAAE,OAAO;UACbb,GAAG,EAAE,OAAO;UACZc,KAAK,EAAE;YACLC,KAAK,EAAE,IAAI,CAAC7C,MAAM,CAACoB,OAAO,CAAC,OAAO,CAAC;YACnC0B,QAAQ,EAAE,IAAI;YACdC,IAAI,EAAGC,KAAU,IAAI;cACnB,IAAI,CAAC7C,KAAK,CAACS,KAAK,GAAGoC,KAAK,CAACC,MAAM,CAACC,KAAK,CAACrC,IAAI,EAAE;YAC9C;;SAEH;OAEJ,CACF;IACH,CAAC,EAAE,GAAG,CAAC;EACT;EAEAlB,aAAaA,CAAA;IACXpB,IAAI,CAAC2C,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACoB,OAAO,CAAC,eAAe,CAAC;MAC3CE,IAAI,EAAE,IAAI,CAACtB,MAAM,CAACoB,OAAO,CACvB,qGAAqG,CACtG;MACD+B,KAAK,EAAE,MAAM;MACbC,gBAAgB,EAAE,yBAAyB;MAC3C/B,IAAI,EAAE,SAAS;MACfgC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,QAAQ;MAC3BC,WAAW,EAAE;QACXC,aAAa,EAAE,gBAAgB;QAC/BC,YAAY,EAAE;OACf;MACDC,YAAY,EAAE,SAAAA,CAAUC,GAAG;QACzBC,CAAC,CAACD,GAAG,CAAC,CACHE,IAAI,CAAC,mCAAmC,CAAC,CACzCC,WAAW,CAAC,eAAe,CAAC;QAE/BF,CAAC,CAACD,GAAG,CAAC,CACHE,IAAI,CAAC,kCAAkC,CAAC,CACxCC,WAAW,CAAC,cAAc,CAAC;MAChC,CAAC;MACDC,cAAc,EAAGd,KAAK,IAAI;QACxB,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtB,OAAO,oCAAoC;;MAE/C;KACD,CAAC,CAACvB,IAAI,CAAEsC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACf,KAAK,EAAE;QAChB,IAAIe,MAAM,CAACf,KAAK,KAAK,QAAQ,EAAE;UAC7BzC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,IAAI,CAACZ,YAAY,CAACoE,UAAU,CAAC,IAAI,CAACvD,IAAI,CAACwD,EAAE,CAAC,CAACpD,SAAS,CAAEC,GAAG,IAAI;YAC3D,IAAI,CAACjB,YAAY,CAACqE,MAAM,EAAE;UAC5B,CAAC,CAAC;;;IAGR,CAAC,CAAC;EACJ;EAEAlF,SAASA,CAAA;IACPuB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,IAAI,EAAC,GAAG,EAAC,IAAI,CAACR,KAAK,CAACS,KAAK,CAAC;IAC3C,IAAG,IAAI,CAACD,IAAI,CAAC0B,YAAY,EAAC;MACxB,IAAI,CAACtC,YAAY,CAACsE,uBAAuB,CAAC,IAAI,CAAC1D,IAAI,CAACwD,EAAE,CAAC,CAACpD,SAAS,CAC9DC,GAAG,IAAI;QACNzC,IAAI,CAAC2C,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACoB,OAAO,CAAC,SAAS,CAAC;UACrCkD,IAAI,EAAE;;;;;;qBAMG,IAAI,CAAC3D,IAAI,CAAC0B,YAAY;qBACtB;UACT;UACAkB,iBAAiB,EAAE,IAAI;UACvBgB,kBAAkB,EAAE;SACrB,CAAC;MACJ,CAAC,EACAhD,GAAG,IAAI;QACNd,OAAO,CAACC,GAAG,CAACa,GAAG,CAAC;QAChBhD,IAAI,CAAC2C,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACoB,OAAO,CAAC,OAAO,CAAC;UACnCC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,IAAI,CAACtB,MAAM,CAACoB,OAAO,CAACG,GAAG,CAACC,OAAO;SACtC,CAAC;MACJ,CAAC,CACF;;EAEL;EACAgD,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC7D,IAAI,CAACC,KAAK,KAAK,mBAAmB;EAChD;EAAC,QAAA6D,CAAA;qBA3MU7E,oBAAoB,EAAApB,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApG,EAAA,CAAAkG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtG,EAAA,CAAAkG,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAxG,EAAA,CAAAkG,iBAAA,CAAAO,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAApBvF,oBAAoB;IAAAwF,SAAA;IAAAC,MAAA;MAAA1E,IAAA;IAAA;IAAA2E,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdjCnH,EAAA,CAAAC,cAAA,aAAkB;QAEeD,EAAA,CAAAG,UAAA,sBAAAkH,uDAAA;UAAA,OAAYD,GAAA,CAAAvF,MAAA,EAAQ;QAAA,EAAC;QAC1C7B,EAAA,CAAAsH,SAAA,qBAA+F;QAC/FtH,EAAA,CAAAuH,UAAA,IAAAC,mCAAA,iBAGM;QACNxH,EAAA,CAAAC,cAAA,gBAAqF;QACjFD,EAAA,CAAAE,MAAA,GACJ;;QAAAF,EAAA,CAAAW,YAAA,EAAS;QAKrBX,EAAA,CAAAuH,UAAA,IAAAE,mCAAA,kBAYM;;;QAzBQzH,EAAA,CAAAY,SAAA,GAAkB;QAAlBZ,EAAA,CAAA0H,UAAA,cAAAN,GAAA,CAAA1F,IAAA,CAAkB;QACP1B,EAAA,CAAAY,SAAA,GAAe;QAAfZ,EAAA,CAAA0H,UAAA,UAAAN,GAAA,CAAAzF,KAAA,CAAe,WAAAyF,GAAA,CAAArD,MAAA,aAAAqD,GAAA,CAAAxF,OAAA,UAAAwF,GAAA,CAAA1F,IAAA;QACT1B,EAAA,CAAAY,SAAA,GAAqC;QAArCZ,EAAA,CAAA0H,UAAA,UAAAN,GAAA,CAAAjF,IAAA,kBAAAiF,GAAA,CAAAjF,IAAA,CAAA0B,YAAA,UAAqC;QAII7D,EAAA,CAAAY,SAAA,GAAwB;QAAxBZ,EAAA,CAAA0H,UAAA,cAAAN,GAAA,CAAA1F,IAAA,CAAAI,KAAA,CAAwB;QAChF9B,EAAA,CAAAY,SAAA,GACJ;QADIZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,oBACJ;QAKOd,EAAA,CAAAY,SAAA,GAAqB;QAArBZ,EAAA,CAAA0H,UAAA,SAAAN,GAAA,CAAApB,aAAA,GAAqB", "names": ["FormGroup", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "EditProfileComponent_div_4_Template_a_click_3_listener", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openModal", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate", "EditProfileComponent_div_8_Template_button_click_8_listener", "_r5", "ctx_r4", "deleteAccount", "EditProfileComponent", "constructor", "_userService", "_authService", "_trans", "_loadingService", "form", "model", "options", "submit", "valid", "loading", "show", "console", "log", "user", "email", "trim", "sendVerificationEmail", "subscribe", "res", "getProfile", "fire", "title", "instant", "icon", "text", "err", "message", "update", "to<PERSON>romise", "then", "hasOwnProperty", "errors", "key", "get", "setErrors", "serverError", "ngOnInit", "first_name", "last_name", "verify_email", "setTimeout", "fields", "fieldGroupClassName", "fieldGroup", "className", "type", "props", "label", "required", "blur", "event", "target", "value", "input", "inputPlaceholder", "reverseButtons", "showCancelButton", "confirmButtonText", "customClass", "confirmButton", "cancelButton", "onBeforeOpen", "ele", "$", "find", "toggleClass", "inputValidator", "result", "deleteUser", "id", "logout", "resendVerificationEmail", "html", "confirmButtonColor", "isTestAccount", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "AuthService", "i3", "TranslateService", "i4", "LoadingService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "EditProfileComponent_Template", "rf", "ctx", "EditProfileComponent_Template_form_ngSubmit_2_listener", "ɵɵelement", "ɵɵtemplate", "EditProfileComponent_div_4_Template", "EditProfileComponent_div_8_Template", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\edit-profile\\edit-profile.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\edit-profile\\edit-profile.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'edit-profile',\r\n  templateUrl: './edit-profile.component.html',\r\n  styleUrls: ['./edit-profile.component.scss'],\r\n})\r\nexport class EditProfileComponent implements OnInit {\r\n  constructor(\r\n    public _userService: UserService,\r\n    public _authService: AuthService,\r\n    public _trans: TranslateService,\r\n    public _loadingService: LoadingService,\r\n  ) {}\r\n  @Input() user: any;\r\n  form = new FormGroup({});\r\n  model: any = {};\r\n  options: FormlyFormOptions = {};\r\n\r\n  fields: FormlyFieldConfig[];\r\n\r\n  submit() {\r\n    if (!this.form.valid) return;\r\n    let loading = this._loadingService.show();\r\n    console.log(this.user.email,\"/\",this.model.email);\r\n    if (this.user.email.trim() != this.model.email.trim()) {\r\n      this._authService.sendVerificationEmail(this.user.email, this.model.email).subscribe(\r\n        (res) => {\r\n          this._authService.getProfile().subscribe((res) => {\r\n            Swal.fire({\r\n              title: this._trans.instant('Information'),\r\n              icon: 'info',\r\n              text: this._trans.instant('The confirmation email has been sent to you. Please check your email!'),\r\n            });\r\n          })\r\n        },\r\n        (err) => {\r\n          console.log(err);\r\n          Swal.fire({\r\n            title: this._trans.instant('Error'),\r\n            icon: 'error',\r\n            text: this._trans.instant(err.message),\r\n          });\r\n        }\r\n      );\r\n    }\r\n     else {\r\n      this._userService\r\n      .update(this.model)\r\n      .toPromise()\r\n      .then(\r\n        (res) => {\r\n          let message = res.message;\r\n          // console.log(res);\r\n          this._authService.getProfile().subscribe((res) => {\r\n            Swal.fire({\r\n              title: this._trans.instant('Success'),\r\n              icon: 'success',\r\n              text: message,\r\n            });\r\n          });\r\n        },\r\n        (err) => {\r\n          // console.log(err);\r\n          if (err.hasOwnProperty('errors')) {\r\n            let errors = err.errors;\r\n            for (let key in errors) {\r\n              if (errors.hasOwnProperty(key)) {\r\n                this.form.get(key).setErrors({ serverError: errors[key] });\r\n              }\r\n            }\r\n          }\r\n        }\r\n      );\r\n    }\r\n  }\r\n  ngOnInit(): void {\r\n    this.model = {\r\n      first_name: this.user.first_name,\r\n      last_name: this.user.last_name,\r\n      email: this.user.email.trim(),\r\n    };\r\n    if(this.user.verify_email){\r\n      this.model.email = this.user.verify_email\r\n    }\r\n    setTimeout(() => {\r\n      this.fields = [\r\n        {\r\n          fieldGroupClassName: 'row',\r\n          fieldGroup: [\r\n            {\r\n              className: 'col-6',\r\n              type: 'input',\r\n              key: 'first_name',\r\n              props: {\r\n                label: this._trans.instant('Surname'),\r\n                required: true,\r\n              },\r\n            },\r\n            {\r\n              className: 'col-6',\r\n              type: 'input',\r\n              key: 'last_name',\r\n              props: {\r\n                label: this._trans.instant('Other names'),\r\n                required: true,\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        // {\r\n        //   className: 'section-label',\r\n        //   template: '<hr /><div><strong>Address:</strong></div>',\r\n        // },\r\n        {\r\n          fieldGroupClassName: 'row',\r\n          fieldGroup: [\r\n            {\r\n              className: 'col col-md-6',\r\n              type: 'input',\r\n              key: 'email',\r\n              props: {\r\n                label: this._trans.instant('Email'),\r\n                required: true,\r\n                blur: (event: any) => {\r\n                  this.model.email = event.target.value.trim();\r\n                },\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      ];\r\n    }, 500);\r\n  }\r\n  \r\n  deleteAccount() {\r\n    Swal.fire({\r\n      title: this._trans.instant('Are you sure?'),\r\n      text: this._trans.instant(\r\n        'Deleting your account will remove all of your information from our database. This cannot be undone.'\r\n      ),\r\n      input: 'text',\r\n      inputPlaceholder: 'Enter DELETE to confirm',\r\n      icon: 'warning',\r\n      reverseButtons: true,\r\n      showCancelButton: true,\r\n      confirmButtonText: 'Delete',\r\n      customClass: {\r\n        confirmButton: 'btn btn-danger',\r\n        cancelButton: 'btn btn-outline-danger',\r\n      },\r\n      onBeforeOpen: function (ele) {\r\n        $(ele)\r\n          .find('button.swal2-confirm.swal2-styled')\r\n          .toggleClass('swal2-confirm');\r\n\r\n        $(ele)\r\n          .find('button.swal2-cancel.swal2-styled')\r\n          .toggleClass('swal2-cancel');\r\n      },\r\n      inputValidator: (value) => {\r\n        if (value !== 'DELETE') {\r\n          return 'You need to type DELETE to confirm';\r\n        }\r\n      },\r\n    }).then((result) => {\r\n      if (result.value) {\r\n        if (result.value === 'DELETE') {\r\n          console.log('delete account');\r\n          this._userService.deleteUser(this.user.id).subscribe((res) => {\r\n            this._authService.logout();\r\n          });\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  openModal(){\r\n    console.log(this.user,\"/\",this.model.email);\r\n    if(this.user.verify_email){\r\n      this._authService.resendVerificationEmail(this.user.id).subscribe(\r\n        (res) => {\r\n          Swal.fire({\r\n            title: this._trans.instant('Success'),\r\n            html: ` <div class=\"text-center\">\r\n                <img src=\"assets/images/icons//Successful Illustration (1).png\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n                <h3>The authentication link has been sent to you!</h3>\r\n                <p class=\"text-center\" style=\"margin-bottom: 0px;\">\r\n                  The authentication link has been sent to \r\n                </p>\r\n                <b>${this.user.verify_email}</b>\r\n              </div>`,\r\n            // icon: 'error',\r\n            confirmButtonText: 'Ok',\r\n            confirmButtonColor: '#7367f0',\r\n          });\r\n        },\r\n        (err) => {\r\n          console.log(err);\r\n          Swal.fire({\r\n            title: this._trans.instant('Error'),\r\n            icon: 'error',\r\n            text: this._trans.instant(err.message),\r\n          });\r\n        }\r\n      )\r\n    }\r\n  }\r\n  isTestAccount() {\r\n    return this.user.email === '<EMAIL>';\r\n  }\r\n}\r\n", "<div class=\"card\">\r\n    <div class=\"card-body\">\r\n        <form [formGroup]=\"form\" (ngSubmit)=\"submit()\">\r\n            <formly-form [model]=\"model\" [fields]=\"fields\" [options]=\"options\" [form]=\"form\"></formly-form>\r\n            <div class=\"pb-1\" *ngIf=\"this.user?.verify_email != null\" >\r\n                {{'Your email has not been verified.' | translate}} \r\n                <a href=\"javascript:void(0);\" (click)=\"openModal()\">{{'Resend request verification email' | translate}}</a>\r\n            </div>\r\n            <button type=\"submit\" class=\"btn btn-primary submit-button\" [disabled]=\"!form.valid\">\r\n                {{'Save' | translate}}\r\n            </button>\r\n        </form>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"card\" *ngIf=\"isTestAccount()\">\r\n    <div class=\"card-header\">\r\n        <h4>{{'Delete Your Account'| translate }}</h4>\r\n    </div>\r\n    <div class=\"card-body\">\r\n        <p>\r\n            Delete your account and all your data. This action is irreversible.\r\n        </p>\r\n        <button class=\"btn btn-danger\" (click)=\"deleteAccount()\">\r\n            <strong>{{'Delete Account'| translate}} </strong>\r\n        </button>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
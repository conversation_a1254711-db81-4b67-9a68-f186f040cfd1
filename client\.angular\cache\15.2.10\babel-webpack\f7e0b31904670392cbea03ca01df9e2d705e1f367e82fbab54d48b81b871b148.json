{"ast": null, "code": "import { map } from './map';\nexport function pluck(...properties) {\n  const length = properties.length;\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n  return source => map(plucker(properties, length))(source);\n}\nfunction plucker(props, length) {\n  const mapper = x => {\n    let currentProp = x;\n    for (let i = 0; i < length; i++) {\n      const p = currentProp != null ? currentProp[props[i]] : undefined;\n      if (p !== void 0) {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n    return currentProp;\n  };\n  return mapper;\n}", "map": {"version": 3, "names": ["map", "pluck", "properties", "length", "Error", "source", "plucker", "props", "mapper", "x", "currentProp", "i", "p", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/pluck.js"], "sourcesContent": ["import { map } from './map';\nexport function pluck(...properties) {\n    const length = properties.length;\n    if (length === 0) {\n        throw new Error('list of properties cannot be empty.');\n    }\n    return (source) => map(plucker(properties, length))(source);\n}\nfunction plucker(props, length) {\n    const mapper = (x) => {\n        let currentProp = x;\n        for (let i = 0; i < length; i++) {\n            const p = currentProp != null ? currentProp[props[i]] : undefined;\n            if (p !== void 0) {\n                currentProp = p;\n            }\n            else {\n                return undefined;\n            }\n        }\n        return currentProp;\n    };\n    return mapper;\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,KAAKA,CAAC,GAAGC,UAAU,EAAE;EACjC,MAAMC,MAAM,GAAGD,UAAU,CAACC,MAAM;EAChC,IAAIA,MAAM,KAAK,CAAC,EAAE;IACd,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EAC1D;EACA,OAAQC,MAAM,IAAKL,GAAG,CAACM,OAAO,CAACJ,UAAU,EAAEC,MAAM,CAAC,CAAC,CAACE,MAAM,CAAC;AAC/D;AACA,SAASC,OAAOA,CAACC,KAAK,EAAEJ,MAAM,EAAE;EAC5B,MAAMK,MAAM,GAAIC,CAAC,IAAK;IAClB,IAAIC,WAAW,GAAGD,CAAC;IACnB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,EAAEQ,CAAC,EAAE,EAAE;MAC7B,MAAMC,CAAC,GAAGF,WAAW,IAAI,IAAI,GAAGA,WAAW,CAACH,KAAK,CAACI,CAAC,CAAC,CAAC,GAAGE,SAAS;MACjE,IAAID,CAAC,KAAK,KAAK,CAAC,EAAE;QACdF,WAAW,GAAGE,CAAC;MACnB,CAAC,MACI;QACD,OAAOC,SAAS;MACpB;IACJ;IACA,OAAOH,WAAW;EACtB,CAAC;EACD,OAAOF,MAAM;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
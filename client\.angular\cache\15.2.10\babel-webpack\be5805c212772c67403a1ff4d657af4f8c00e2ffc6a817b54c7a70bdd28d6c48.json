{"ast": null, "code": "export class TranslateExtension {\n  constructor(translate) {\n    this.translate = translate;\n  }\n  prePopulate(field) {\n    const props = field.props || {};\n    if (!props.translate || props._translated) {\n      return;\n    }\n    props._translated = true;\n    field.expressions = {\n      ...(field.expressions || {}),\n      'props.label': props.label ? this.translate.stream(props.label) : props.label\n    };\n    // translate placeholder\n    if (props.placeholder) {\n      field.props.placeholder = this.translate.instant(props.placeholder);\n    }\n    // translate options\n    if (props.options) {\n      props.options.forEach(option => {\n        option.label = this.translate.instant(option.label);\n      });\n    }\n  }\n}\nexport function registerTranslateExtension(translate) {\n  return {\n    validationMessages: [{\n      name: 'required',\n      message() {\n        return translate.stream('FORM.VALIDATION.REQUIRED');\n      }\n    }],\n    extensions: [{\n      name: 'translate',\n      extension: new TranslateExtension(translate)\n    }]\n  };\n}\n/**  Copyright 2021 Formly. All Rights Reserved.\r\n    Use of this source code is governed by an MIT-style license that\r\n    can be found in the LICENSE file at https://github.com/ngx-formly/ngx-formly/blob/main/LICENSE */", "map": {"version": 3, "mappings": "AAGA,OAAM,MAAOA,kBAAkB;EAC7BC,YAAoBC,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;EAAqB;EAClDC,WAAWA,CAACC,KAAwB;IAClC,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACA,KAAK,CAACH,SAAS,IAAIG,KAAK,CAACC,WAAW,EAAE;MACzC;;IAGFD,KAAK,CAACC,WAAW,GAAG,IAAI;IACxBF,KAAK,CAACG,WAAW,GAAG;MAClB,IAAIH,KAAK,CAACG,WAAW,IAAI,EAAE,CAAC;MAC5B,aAAa,EAAGF,KAAK,CAACG,KAAK,GAAE,IAAI,CAACN,SAAS,CAACO,MAAM,CAACJ,KAAK,CAACG,KAAK,CAAC,GAACH,KAAK,CAACG;KACvE;IACD;IACA,IAAIH,KAAK,CAACK,WAAW,EAAE;MACrBN,KAAK,CAACC,KAAK,CAACK,WAAW,GAAG,IAAI,CAACR,SAAS,CAACS,OAAO,CAACN,KAAK,CAACK,WAAW,CAAC;;IAErE;IACA,IAAIL,KAAK,CAACO,OAAO,EAAE;MACjBP,KAAK,CAACO,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;QACpCA,MAAM,CAACN,KAAK,GAAG,IAAI,CAACN,SAAS,CAACS,OAAO,CAACG,MAAM,CAACN,KAAK,CAAC;MACrD,CAAC,CAAC;;EAEN;;AAGF,OAAM,SAAUO,0BAA0BA,CAACb,SAA2B;EACpE,OAAO;IACLc,kBAAkB,EAAE,CAClB;MACEC,IAAI,EAAE,UAAU;MAChBC,OAAOA,CAAA;QACL,OAAOhB,SAAS,CAACO,MAAM,CAAC,0BAA0B,CAAC;MACrD;KACD,CACF;IACDU,UAAU,EAAE,CACV;MACEF,IAAI,EAAE,WAAW;MACjBG,SAAS,EAAE,IAAIpB,kBAAkB,CAACE,SAAS;KAC5C;GAEJ;AACH;AAEA", "names": ["TranslateExtension", "constructor", "translate", "prePopulate", "field", "props", "_translated", "expressions", "label", "stream", "placeholder", "instant", "options", "for<PERSON>ach", "option", "registerTranslateExtension", "validationMessages", "name", "message", "extensions", "extension"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\translate.extension.ts"], "sourcesContent": ["import { FormlyExtension, FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\nexport class TranslateExtension implements FormlyExtension {\r\n  constructor(private translate: TranslateService) {}\r\n  prePopulate(field: FormlyFieldConfig) {\r\n    const props = field.props || {};\r\n    if (!props.translate || props._translated) {\r\n      return;\r\n    }\r\n\r\n    props._translated = true;\r\n    field.expressions = {\r\n      ...(field.expressions || {}),\r\n      'props.label': (props.label? this.translate.stream(props.label):props.label ),\r\n    };\r\n    // translate placeholder\r\n    if (props.placeholder) {\r\n      field.props.placeholder = this.translate.instant(props.placeholder);\r\n    }\r\n    // translate options\r\n    if (props.options) {\r\n      props.options.forEach((option: any) => {\r\n        option.label = this.translate.instant(option.label);\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\nexport function registerTranslateExtension(translate: TranslateService) {\r\n  return {\r\n    validationMessages: [\r\n      {\r\n        name: 'required',\r\n        message() {\r\n          return translate.stream('FORM.VALIDATION.REQUIRED');\r\n        },\r\n      },\r\n    ],\r\n    extensions: [\r\n      {\r\n        name: 'translate',\r\n        extension: new TranslateExtension(translate),\r\n      },\r\n    ],\r\n  };\r\n}\r\n\r\n/**  Copyright 2021 Formly. All Rights Reserved.\r\n    Use of this source code is governed by an MIT-style license that\r\n    can be found in the LICENSE file at https://github.com/ngx-formly/ngx-formly/blob/main/LICENSE */\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
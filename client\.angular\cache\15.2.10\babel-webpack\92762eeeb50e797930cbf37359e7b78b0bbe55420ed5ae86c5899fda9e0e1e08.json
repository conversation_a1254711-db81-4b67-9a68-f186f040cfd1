{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Type, TemplateRef, ComponentRef, ChangeDetectorRef, InjectionToken, Injectable, Optional, Directive, Input, ViewContainerRef, Component, ViewChild, EventEmitter, ChangeDetectionStrategy, Output, ContentChildren, Inject, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { AbstractControl, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';\nimport { isObservable, merge, of, Observable, Subject } from 'rxjs';\nimport { distinctUntilChanged, startWith, debounceTime, filter, switchMap, take, tap, map } from 'rxjs/operators';\nimport * as i2$1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nconst _c0 = [\"container\"];\nfunction FormlyField_ng_template_0_Template(rf, ctx) {}\nfunction FormlyGroup_formly_field_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"formly-field\", 1);\n  }\n  if (rf & 2) {\n    const f_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"field\", f_r1);\n  }\n}\nconst _c1 = [\"*\"];\nconst _c2 = [\"fieldComponent\"];\nfunction disableTreeValidityCall(form, callback) {\n  const _updateTreeValidity = form._updateTreeValidity.bind(form);\n  form._updateTreeValidity = () => {};\n  callback();\n  form._updateTreeValidity = _updateTreeValidity;\n}\nfunction getFieldId(formId, field, index) {\n  if (field.id) {\n    return field.id;\n  }\n  let type = field.type;\n  if (!type && field.template) {\n    type = 'template';\n  }\n  if (type instanceof Type) {\n    type = type.prototype.constructor.name;\n  }\n  return [formId, type, field.key, index].join('_');\n}\nfunction hasKey(field) {\n  return !isNil(field.key) && field.key !== '';\n}\nfunction getKeyPath(field) {\n  if (!hasKey(field)) {\n    return [];\n  }\n  /* We store the keyPath in the field for performance reasons. This function will be called frequently. */\n  if (field._keyPath?.key !== field.key) {\n    let path = [];\n    if (typeof field.key === 'string') {\n      const key = field.key.indexOf('[') === -1 ? field.key : field.key.replace(/\\[(\\w+)\\]/g, '.$1');\n      path = key.indexOf('.') !== -1 ? key.split('.') : [key];\n    } else if (Array.isArray(field.key)) {\n      path = field.key.slice(0);\n    } else {\n      path = [`${field.key}`];\n    }\n    defineHiddenProp(field, '_keyPath', {\n      key: field.key,\n      path\n    });\n  }\n  return field._keyPath.path.slice(0);\n}\nconst FORMLY_VALIDATORS = ['required', 'pattern', 'minLength', 'maxLength', 'min', 'max'];\nfunction assignFieldValue(field, value) {\n  let paths = getKeyPath(field);\n  if (paths.length === 0) {\n    return;\n  }\n  let root = field;\n  while (root.parent) {\n    root = root.parent;\n    paths = [...getKeyPath(root), ...paths];\n  }\n  if (value === undefined && field.resetOnHide) {\n    const k = paths.pop();\n    const m = paths.reduce((model, path) => model[path] || {}, root.model);\n    delete m[k];\n    return;\n  }\n  assignModelValue(root.model, paths, value);\n}\nfunction assignModelValue(model, paths, value) {\n  for (let i = 0; i < paths.length - 1; i++) {\n    const path = paths[i];\n    if (!model[path] || !isObject(model[path])) {\n      model[path] = /^\\d+$/.test(paths[i + 1]) ? [] : {};\n    }\n    model = model[path];\n  }\n  model[paths[paths.length - 1]] = clone(value);\n}\nfunction getFieldValue(field) {\n  let model = field.parent ? field.parent.model : field.model;\n  for (const path of getKeyPath(field)) {\n    if (!model) {\n      return model;\n    }\n    model = model[path];\n  }\n  return model;\n}\nfunction reverseDeepMerge(dest, ...args) {\n  args.forEach(src => {\n    for (const srcArg in src) {\n      if (isNil(dest[srcArg]) || isBlankString(dest[srcArg])) {\n        dest[srcArg] = clone(src[srcArg]);\n      } else if (objAndSameType(dest[srcArg], src[srcArg])) {\n        reverseDeepMerge(dest[srcArg], src[srcArg]);\n      }\n    }\n  });\n  return dest;\n}\n// check a value is null or undefined\nfunction isNil(value) {\n  return value == null;\n}\nfunction isUndefined(value) {\n  return value === undefined;\n}\nfunction isBlankString(value) {\n  return value === '';\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nfunction objAndSameType(obj1, obj2) {\n  return isObject(obj1) && isObject(obj2) && Object.getPrototypeOf(obj1) === Object.getPrototypeOf(obj2) && !(Array.isArray(obj1) || Array.isArray(obj2));\n}\nfunction isObject(x) {\n  return x != null && typeof x === 'object';\n}\nfunction isPromise(obj) {\n  return !!obj && typeof obj.then === 'function';\n}\nfunction clone(value) {\n  if (!isObject(value) || isObservable(value) || value instanceof TemplateRef || /* instanceof SafeHtmlImpl */value.changingThisBreaksApplicationSecurity || ['RegExp', 'FileList', 'File', 'Blob'].indexOf(value.constructor.name) !== -1) {\n    return value;\n  }\n  if (value instanceof Set) {\n    return new Set(value);\n  }\n  if (value instanceof Map) {\n    return new Map(value);\n  }\n  // https://github.com/moment/moment/blob/master/moment.js#L252\n  if (value._isAMomentObject && isFunction(value.clone)) {\n    return value.clone();\n  }\n  if (value instanceof AbstractControl) {\n    return null;\n  }\n  if (value instanceof Date) {\n    return new Date(value.getTime());\n  }\n  if (Array.isArray(value)) {\n    return value.slice(0).map(v => clone(v));\n  }\n  // best way to clone a js object maybe\n  // https://stackoverflow.com/questions/41474986/how-to-clone-a-javascript-es6-class-instance\n  const proto = Object.getPrototypeOf(value);\n  let c = Object.create(proto);\n  c = Object.setPrototypeOf(c, proto);\n  // need to make a deep copy so we dont use Object.assign\n  // also Object.assign wont copy property descriptor exactly\n  return Object.keys(value).reduce((newVal, prop) => {\n    const propDesc = Object.getOwnPropertyDescriptor(value, prop);\n    if (propDesc.get) {\n      Object.defineProperty(newVal, prop, propDesc);\n    } else {\n      newVal[prop] = clone(value[prop]);\n    }\n    return newVal;\n  }, c);\n}\nfunction defineHiddenProp(field, prop, defaultValue) {\n  Object.defineProperty(field, prop, {\n    enumerable: false,\n    writable: true,\n    configurable: true\n  });\n  field[prop] = defaultValue;\n}\nfunction observeDeep(source, paths, setFn) {\n  let observers = [];\n  const unsubscribe = () => {\n    observers.forEach(observer => observer());\n    observers = [];\n  };\n  const observer = observe(source, paths, ({\n    firstChange,\n    currentValue\n  }) => {\n    !firstChange && setFn();\n    unsubscribe();\n    if (isObject(currentValue) && currentValue.constructor.name === 'Object') {\n      Object.keys(currentValue).forEach(prop => {\n        observers.push(observeDeep(source, [...paths, prop], setFn));\n      });\n    }\n  });\n  return () => {\n    observer.unsubscribe();\n    unsubscribe();\n  };\n}\nfunction observe(o, paths, setFn) {\n  if (!o._observers) {\n    defineHiddenProp(o, '_observers', {});\n  }\n  let target = o;\n  for (let i = 0; i < paths.length - 1; i++) {\n    if (!target[paths[i]] || !isObject(target[paths[i]])) {\n      target[paths[i]] = /^\\d+$/.test(paths[i + 1]) ? [] : {};\n    }\n    target = target[paths[i]];\n  }\n  const key = paths[paths.length - 1];\n  const prop = paths.join('.');\n  if (!o._observers[prop]) {\n    o._observers[prop] = {\n      value: target[key],\n      onChange: []\n    };\n  }\n  const state = o._observers[prop];\n  if (target[key] !== state.value) {\n    state.value = target[key];\n  }\n  if (state.onChange.indexOf(setFn) === -1) {\n    state.onChange.push(setFn);\n    setFn({\n      currentValue: state.value,\n      firstChange: true\n    });\n    if (state.onChange.length >= 1 && isObject(target)) {\n      const {\n        enumerable\n      } = Object.getOwnPropertyDescriptor(target, key) || {\n        enumerable: true\n      };\n      Object.defineProperty(target, key, {\n        enumerable,\n        configurable: true,\n        get: () => state.value,\n        set: currentValue => {\n          if (currentValue !== state.value) {\n            const previousValue = state.value;\n            state.value = currentValue;\n            state.onChange.forEach(changeFn => changeFn({\n              previousValue,\n              currentValue,\n              firstChange: false\n            }));\n          }\n        }\n      });\n    }\n  }\n  return {\n    setValue(value) {\n      state.value = value;\n    },\n    unsubscribe() {\n      state.onChange = state.onChange.filter(changeFn => changeFn !== setFn);\n      if (state.onChange.length === 0) {\n        delete o._observers[prop];\n      }\n    }\n  };\n}\nfunction getField(f, key) {\n  key = Array.isArray(key) ? key.join('.') : key;\n  if (!f.fieldGroup) {\n    return undefined;\n  }\n  for (let i = 0, len = f.fieldGroup.length; i < len; i++) {\n    const c = f.fieldGroup[i];\n    const k = Array.isArray(c.key) ? c.key.join('.') : c.key;\n    if (k === key) {\n      return c;\n    }\n    if (c.fieldGroup && (isNil(k) || key.indexOf(`${k}.`) === 0)) {\n      const field = getField(c, isNil(k) ? key : key.slice(k.length + 1));\n      if (field) {\n        return field;\n      }\n    }\n  }\n  return undefined;\n}\nfunction markFieldForCheck(field) {\n  field._componentRefs?.forEach(ref => {\n    // NOTE: we cannot use ref.changeDetectorRef, see https://github.com/ngx-formly/ngx-formly/issues/2191\n    if (ref instanceof ComponentRef) {\n      const changeDetectorRef = ref.injector.get(ChangeDetectorRef);\n      changeDetectorRef.markForCheck();\n    } else {\n      ref.markForCheck();\n    }\n  });\n}\n\n/**\n * An InjectionToken for registering additional formly config options (types, wrappers ...).\n */\nconst FORMLY_CONFIG = new InjectionToken('FORMLY_CONFIG');\n/**\n * Maintains list of formly config options. This can be used to register new field type.\n */\nclass FormlyConfig {\n  constructor() {\n    this.types = {};\n    this.validators = {};\n    this.wrappers = {};\n    this.messages = {};\n    this.extras = {\n      checkExpressionOn: 'modelChange',\n      lazyRender: true,\n      resetFieldOnHide: true,\n      renderFormlyFieldElement: true,\n      showError(field) {\n        return field.formControl?.invalid && (field.formControl?.touched || field.options.parentForm?.submitted || !!field.field.validation?.show);\n      }\n    };\n    this.extensions = {};\n    this.presets = {};\n    this.extensionsByPriority = {};\n  }\n  addConfig(config) {\n    if (config.types) {\n      config.types.forEach(type => this.setType(type));\n    }\n    if (config.validators) {\n      config.validators.forEach(validator => this.setValidator(validator));\n    }\n    if (config.wrappers) {\n      config.wrappers.forEach(wrapper => this.setWrapper(wrapper));\n    }\n    if (config.validationMessages) {\n      config.validationMessages.forEach(validation => this.addValidatorMessage(validation.name, validation.message));\n    }\n    if (config.extensions) {\n      this.setSortedExtensions(config.extensions);\n    }\n    if (config.extras) {\n      this.extras = {\n        ...this.extras,\n        ...config.extras\n      };\n    }\n    if (config.presets) {\n      this.presets = {\n        ...this.presets,\n        ...config.presets.reduce((acc, curr) => ({\n          ...acc,\n          [curr.name]: curr.config\n        }), {})\n      };\n    }\n  }\n  /**\n   * Allows you to specify a custom type which you can use in your field configuration.\n   * You can pass an object of options, or an array of objects of options.\n   */\n  setType(options) {\n    if (Array.isArray(options)) {\n      options.forEach(option => this.setType(option));\n    } else {\n      if (!this.types[options.name]) {\n        this.types[options.name] = {\n          name: options.name\n        };\n      }\n      ['component', 'extends', 'defaultOptions', 'wrappers'].forEach(prop => {\n        if (options.hasOwnProperty(prop)) {\n          this.types[options.name][prop] = options[prop];\n        }\n      });\n    }\n  }\n  getType(name, throwIfNotFound = false) {\n    if (name instanceof Type) {\n      return {\n        component: name,\n        name: name.prototype.constructor.name\n      };\n    }\n    if (!this.types[name]) {\n      if (throwIfNotFound) {\n        throw new Error(`[Formly Error] The type \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n      }\n      return null;\n    }\n    this.mergeExtendedType(name);\n    return this.types[name];\n  }\n  /** @ignore */\n  getMergedField(field = {}) {\n    const type = this.getType(field.type);\n    if (!type) {\n      return;\n    }\n    if (type.defaultOptions) {\n      reverseDeepMerge(field, type.defaultOptions);\n    }\n    const extendDefaults = type.extends && this.getType(type.extends).defaultOptions;\n    if (extendDefaults) {\n      reverseDeepMerge(field, extendDefaults);\n    }\n    if (field?.optionsTypes) {\n      field.optionsTypes.forEach(option => {\n        const defaultOptions = this.getType(option).defaultOptions;\n        if (defaultOptions) {\n          reverseDeepMerge(field, defaultOptions);\n        }\n      });\n    }\n    const componentRef = this.resolveFieldTypeRef(field);\n    if (componentRef?.instance?.defaultOptions) {\n      reverseDeepMerge(field, componentRef.instance.defaultOptions);\n    }\n    if (!field.wrappers && type.wrappers) {\n      field.wrappers = [...type.wrappers];\n    }\n  }\n  /** @ignore @internal */\n  resolveFieldTypeRef(field = {}) {\n    const type = this.getType(field.type);\n    if (!type) {\n      return null;\n    }\n    if (!type.component || type._componentRef) {\n      return type._componentRef;\n    }\n    const {\n      _viewContainerRef,\n      _injector\n    } = field.options;\n    if (!_viewContainerRef || !_injector) {\n      return null;\n    }\n    const componentRef = _viewContainerRef.createComponent(type.component, {\n      injector: _injector\n    });\n    defineHiddenProp(type, '_componentRef', componentRef);\n    try {\n      componentRef.destroy();\n    } catch (e) {\n      console.error(`An error occurred while destroying the Formly component type \"${field.type}\"`, e);\n    }\n    return type._componentRef;\n  }\n  setWrapper(options) {\n    this.wrappers[options.name] = options;\n    if (options.types) {\n      options.types.forEach(type => {\n        this.setTypeWrapper(type, options.name);\n      });\n    }\n  }\n  getWrapper(name) {\n    if (name instanceof Type) {\n      return {\n        component: name,\n        name: name.prototype.constructor.name\n      };\n    }\n    if (!this.wrappers[name]) {\n      throw new Error(`[Formly Error] The wrapper \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n    }\n    return this.wrappers[name];\n  }\n  /** @ignore */\n  setTypeWrapper(type, name) {\n    if (!this.types[type]) {\n      this.types[type] = {};\n    }\n    if (!this.types[type].wrappers) {\n      this.types[type].wrappers = [];\n    }\n    if (this.types[type].wrappers.indexOf(name) === -1) {\n      this.types[type].wrappers.push(name);\n    }\n  }\n  setValidator(options) {\n    this.validators[options.name] = options;\n  }\n  getValidator(name) {\n    if (!this.validators[name]) {\n      throw new Error(`[Formly Error] The validator \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n    }\n    return this.validators[name];\n  }\n  addValidatorMessage(name, message) {\n    this.messages[name] = message;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const deprecated = {\n        minlength: 'minLength',\n        maxlength: 'maxLength'\n      };\n      if (deprecated[name]) {\n        console.warn(`Formly deprecation: passing validation messages key '${name}' is deprecated since v6.0, use '${deprecated[name]}' instead.`);\n        this.messages[deprecated[name]] = message;\n      }\n    }\n  }\n  getValidatorMessage(name) {\n    return this.messages[name];\n  }\n  setSortedExtensions(extensionOptions) {\n    // insert new extensions, grouped by priority\n    extensionOptions.forEach(extensionOption => {\n      const priority = extensionOption.priority ?? 1;\n      this.extensionsByPriority[priority] = {\n        ...this.extensionsByPriority[priority],\n        [extensionOption.name]: extensionOption.extension\n      };\n    });\n    // flatten extensions object with sorted keys\n    this.extensions = Object.keys(this.extensionsByPriority).map(Number).sort((a, b) => a - b).reduce((acc, prio) => ({\n      ...acc,\n      ...this.extensionsByPriority[prio]\n    }), {});\n  }\n  mergeExtendedType(name) {\n    if (!this.types[name].extends) {\n      return;\n    }\n    const extendedType = this.getType(this.types[name].extends);\n    if (!this.types[name].component) {\n      this.types[name].component = extendedType.component;\n    }\n    if (!this.types[name].wrappers) {\n      this.types[name].wrappers = extendedType.wrappers;\n    }\n  }\n}\nFormlyConfig.ɵfac = function FormlyConfig_Factory(t) {\n  return new (t || FormlyConfig)();\n};\nFormlyConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FormlyConfig,\n  factory: FormlyConfig.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass FormlyFormBuilder {\n  constructor(config, injector, viewContainerRef, parentForm) {\n    this.config = config;\n    this.injector = injector;\n    this.viewContainerRef = viewContainerRef;\n    this.parentForm = parentForm;\n  }\n  buildForm(form, fieldGroup = [], model, options) {\n    this.build({\n      fieldGroup,\n      model,\n      form,\n      options\n    });\n  }\n  build(field) {\n    if (!this.config.extensions.core) {\n      throw new Error('NgxFormly: missing `forRoot()` call. use `forRoot()` when registering the `FormlyModule`.');\n    }\n    if (!field.parent) {\n      this._setOptions(field);\n      disableTreeValidityCall(field.form, () => {\n        this._build(field);\n        const options = field.options;\n        options.checkExpressions?.(field, true);\n        options.detectChanges?.(field);\n      });\n    } else {\n      this._build(field);\n    }\n  }\n  _build(field) {\n    if (!field) {\n      return;\n    }\n    const extensions = Object.values(this.config.extensions);\n    extensions.forEach(extension => extension.prePopulate?.(field));\n    extensions.forEach(extension => extension.onPopulate?.(field));\n    field.fieldGroup?.forEach(f => this._build(f));\n    extensions.forEach(extension => extension.postPopulate?.(field));\n  }\n  _setOptions(field) {\n    field.form = field.form || new FormGroup({});\n    field.model = field.model || {};\n    field.options = field.options || {};\n    const options = field.options;\n    if (!options._viewContainerRef) {\n      defineHiddenProp(options, '_viewContainerRef', this.viewContainerRef);\n    }\n    if (!options._injector) {\n      defineHiddenProp(options, '_injector', this.injector);\n    }\n    if (!options.build) {\n      options._buildForm = () => {\n        console.warn(`Formly: 'options._buildForm' is deprecated since v6.0, use 'options.build' instead.`);\n        this.build(field);\n      };\n      options.build = (f = field) => {\n        this.build(f);\n        return f;\n      };\n    }\n    if (!options.parentForm && this.parentForm) {\n      defineHiddenProp(options, 'parentForm', this.parentForm);\n      observe(options, ['parentForm', 'submitted'], ({\n        firstChange\n      }) => {\n        if (!firstChange) {\n          options.checkExpressions(field);\n          options.detectChanges(field);\n        }\n      });\n    }\n  }\n}\nFormlyFormBuilder.ɵfac = function FormlyFormBuilder_Factory(t) {\n  return new (t || FormlyFormBuilder)(i0.ɵɵinject(FormlyConfig), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.ViewContainerRef, 8), i0.ɵɵinject(i2.FormGroupDirective, 8));\n};\nFormlyFormBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FormlyFormBuilder,\n  factory: FormlyFormBuilder.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyFormBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: FormlyConfig\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.ViewContainerRef,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nfunction unregisterControl(field, emitEvent = false) {\n  const control = field.formControl;\n  const fieldIndex = control._fields ? control._fields.indexOf(field) : -1;\n  if (fieldIndex !== -1) {\n    control._fields.splice(fieldIndex, 1);\n  }\n  const form = control.parent;\n  if (!form) {\n    return;\n  }\n  const opts = {\n    emitEvent\n  };\n  if (form instanceof FormArray) {\n    const key = form.controls.findIndex(c => c === control);\n    if (key !== -1) {\n      form.removeAt(key, opts);\n    }\n  } else if (form instanceof FormGroup) {\n    const paths = getKeyPath(field);\n    const key = paths[paths.length - 1];\n    if (form.get([key]) === control) {\n      form.removeControl(key, opts);\n    }\n  }\n  control.setParent(null);\n}\nfunction findControl(field) {\n  if (field.formControl) {\n    return field.formControl;\n  }\n  if (field.shareFormControl === false) {\n    return null;\n  }\n  return field.form?.get(getKeyPath(field));\n}\nfunction registerControl(field, control, emitEvent = false) {\n  control = control || field.formControl;\n  if (!control._fields) {\n    defineHiddenProp(control, '_fields', []);\n  }\n  if (control._fields.indexOf(field) === -1) {\n    control._fields.push(field);\n  }\n  if (!field.formControl && control) {\n    defineHiddenProp(field, 'formControl', control);\n    control.setValidators(null);\n    control.setAsyncValidators(null);\n    field.props.disabled = !!field.props.disabled;\n    const disabledObserver = observe(field, ['props', 'disabled'], ({\n      firstChange,\n      currentValue\n    }) => {\n      if (!firstChange) {\n        currentValue ? field.formControl.disable() : field.formControl.enable();\n      }\n    });\n    if (control instanceof FormControl) {\n      control.registerOnDisabledChange(disabledObserver.setValue);\n    }\n  }\n  if (!field.form || !hasKey(field)) {\n    return;\n  }\n  let form = field.form;\n  const paths = getKeyPath(field);\n  const value = getFieldValue(field);\n  if (!(isNil(control.value) && isNil(value)) && control.value !== value && control instanceof FormControl) {\n    control.patchValue(value);\n  }\n  for (let i = 0; i < paths.length - 1; i++) {\n    const path = paths[i];\n    if (!form.get([path])) {\n      form.setControl(path, new FormGroup({}), {\n        emitEvent\n      });\n    }\n    form = form.get([path]);\n  }\n  const key = paths[paths.length - 1];\n  if (!field._hide && form.get([key]) !== control) {\n    form.setControl(key, control, {\n      emitEvent\n    });\n  }\n}\nfunction updateValidity(c, onlySelf = false) {\n  const status = c.status;\n  const value = c.value;\n  c.updateValueAndValidity({\n    emitEvent: false,\n    onlySelf\n  });\n  if (status !== c.status) {\n    c.statusChanges.emit(c.status);\n  }\n  if (value !== c.value) {\n    c.valueChanges.emit(c.value);\n  }\n}\nfunction clearControl(form) {\n  delete form?._fields;\n  form.setValidators(null);\n  form.setAsyncValidators(null);\n  if (form instanceof FormGroup || form instanceof FormArray) {\n    Object.values(form.controls).forEach(c => clearControl(c));\n  }\n}\nclass FormlyTemplate {\n  constructor(ref) {\n    this.ref = ref;\n  }\n  ngOnChanges() {\n    this.name = this.name || 'formly-group';\n  }\n}\nFormlyTemplate.ɵfac = function FormlyTemplate_Factory(t) {\n  return new (t || FormlyTemplate)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\nFormlyTemplate.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: FormlyTemplate,\n  selectors: [[\"\", \"formlyTemplate\", \"\"]],\n  inputs: {\n    name: [\"formlyTemplate\", \"name\"]\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyTemplate, [{\n    type: Directive,\n    args: [{\n      selector: '[formlyTemplate]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, {\n    name: [{\n      type: Input,\n      args: ['formlyTemplate']\n    }]\n  });\n})();\n// workarround for https://github.com/angular/angular/issues/43227#issuecomment-904173738\nclass FormlyFieldTemplates {}\nFormlyFieldTemplates.ɵfac = function FormlyFieldTemplates_Factory(t) {\n  return new (t || FormlyFieldTemplates)();\n};\nFormlyFieldTemplates.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FormlyFieldTemplates,\n  factory: FormlyFieldTemplates.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyFieldTemplates, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The `<formly-field>` component is used to render the UI widget (layout + type) of a given `field`.\n */\nclass FormlyField {\n  constructor(config, renderer, _elementRef, hostContainerRef, form) {\n    this.config = config;\n    this.renderer = renderer;\n    this._elementRef = _elementRef;\n    this.hostContainerRef = hostContainerRef;\n    this.form = form;\n    this.hostObservers = [];\n    this.componentRefs = [];\n    this.hooksObservers = [];\n    this.detectFieldBuild = false;\n    this.valueChangesUnsubscribe = () => {};\n  }\n  get containerRef() {\n    return this.config.extras.renderFormlyFieldElement ? this.viewContainerRef : this.hostContainerRef;\n  }\n  get elementRef() {\n    if (this.config.extras.renderFormlyFieldElement) {\n      return this._elementRef;\n    }\n    if (this.componentRefs?.[0] instanceof ComponentRef) {\n      return this.componentRefs[0].location;\n    }\n    return null;\n  }\n  ngAfterContentInit() {\n    this.triggerHook('afterContentInit');\n  }\n  ngAfterViewInit() {\n    this.triggerHook('afterViewInit');\n  }\n  ngDoCheck() {\n    if (this.detectFieldBuild && this.field && this.field.options) {\n      this.render();\n    }\n  }\n  ngOnInit() {\n    this.triggerHook('onInit');\n  }\n  ngOnChanges(changes) {\n    this.triggerHook('onChanges', changes);\n  }\n  ngOnDestroy() {\n    this.resetRefs(this.field);\n    this.hostObservers.forEach(hostObserver => hostObserver.unsubscribe());\n    this.hooksObservers.forEach(unsubscribe => unsubscribe());\n    this.valueChangesUnsubscribe();\n    this.triggerHook('onDestroy');\n  }\n  renderField(containerRef, f, wrappers = []) {\n    if (this.containerRef === containerRef) {\n      this.resetRefs(this.field);\n      this.containerRef.clear();\n      wrappers = this.field?.wrappers;\n    }\n    if (wrappers?.length > 0) {\n      const [wrapper, ...wps] = wrappers;\n      const {\n        component\n      } = this.config.getWrapper(wrapper);\n      const ref = containerRef.createComponent(component);\n      this.attachComponentRef(ref, f);\n      observe(ref.instance, ['fieldComponent'], ({\n        currentValue,\n        previousValue,\n        firstChange\n      }) => {\n        if (currentValue) {\n          if (previousValue && previousValue._lContainer === currentValue._lContainer) {\n            return;\n          }\n          const viewRef = previousValue ? previousValue.detach() : null;\n          if (viewRef && !viewRef.destroyed) {\n            currentValue.insert(viewRef);\n          } else {\n            this.renderField(currentValue, f, wps);\n          }\n          !firstChange && ref.changeDetectorRef.detectChanges();\n        }\n      });\n    } else if (f?.type) {\n      const inlineType = this.form?.templates?.find(ref => ref.name === f.type);\n      let ref;\n      if (inlineType) {\n        ref = containerRef.createEmbeddedView(inlineType.ref, {\n          $implicit: f\n        });\n      } else {\n        const {\n          component\n        } = this.config.getType(f.type, true);\n        ref = containerRef.createComponent(component);\n      }\n      this.attachComponentRef(ref, f);\n    }\n  }\n  triggerHook(name, changes) {\n    if (name === 'onInit' || name === 'onChanges' && changes.field && !changes.field.firstChange) {\n      this.valueChangesUnsubscribe = this.fieldChanges(this.field);\n    }\n    if (this.field?.hooks?.[name]) {\n      if (!changes || changes.field) {\n        const r = this.field.hooks[name](this.field);\n        if (isObservable(r) && ['onInit', 'afterContentInit', 'afterViewInit'].indexOf(name) !== -1) {\n          const sub = r.subscribe();\n          this.hooksObservers.push(() => sub.unsubscribe());\n        }\n      }\n    }\n    if (name === 'onChanges' && changes.field) {\n      this.resetRefs(changes.field.previousValue);\n      this.render();\n    }\n  }\n  attachComponentRef(ref, field) {\n    this.componentRefs.push(ref);\n    field._componentRefs.push(ref);\n    if (ref instanceof ComponentRef) {\n      Object.assign(ref.instance, {\n        field\n      });\n    }\n  }\n  render() {\n    if (!this.field) {\n      return;\n    }\n    // require Formly build\n    if (!this.field.options) {\n      this.detectFieldBuild = true;\n      return;\n    }\n    this.detectFieldBuild = false;\n    this.hostObservers.forEach(hostObserver => hostObserver.unsubscribe());\n    this.hostObservers = [observe(this.field, ['hide'], ({\n      firstChange,\n      currentValue\n    }) => {\n      const containerRef = this.containerRef;\n      if (this.config.extras.lazyRender === false) {\n        firstChange && this.renderField(containerRef, this.field);\n        if (!firstChange || firstChange && currentValue) {\n          this.elementRef && this.renderer.setStyle(this.elementRef.nativeElement, 'display', currentValue ? 'none' : '');\n        }\n      } else {\n        if (currentValue) {\n          containerRef.clear();\n          if (this.field.className) {\n            this.renderer.removeAttribute(this.elementRef.nativeElement, 'class');\n          }\n        } else {\n          this.renderField(containerRef, this.field);\n          if (this.field.className) {\n            this.renderer.setAttribute(this.elementRef.nativeElement, 'class', this.field.className);\n          }\n        }\n      }\n      !firstChange && this.field.options.detectChanges(this.field);\n    }), observe(this.field, ['className'], ({\n      firstChange,\n      currentValue\n    }) => {\n      if ((!firstChange || firstChange && currentValue) && (!this.config.extras.lazyRender || this.field.hide !== true)) {\n        this.elementRef && this.renderer.setAttribute(this.elementRef.nativeElement, 'class', currentValue);\n      }\n    }), ...['touched', 'pristine', 'status'].map(prop => observe(this.field, ['formControl', prop], ({\n      firstChange\n    }) => !firstChange && markFieldForCheck(this.field)))];\n  }\n  resetRefs(field) {\n    if (field) {\n      if (field._componentRefs) {\n        field._componentRefs = field._componentRefs.filter(ref => this.componentRefs.indexOf(ref) === -1);\n      } else {\n        defineHiddenProp(this.field, '_componentRefs', []);\n      }\n    }\n    this.componentRefs = [];\n  }\n  fieldChanges(field) {\n    this.valueChangesUnsubscribe();\n    if (!field) {\n      return () => {};\n    }\n    const subscribes = [observeDeep(field, ['props'], () => field.options.detectChanges(field))];\n    if (field.options) {\n      subscribes.push(observeDeep(field.options, ['formState'], () => field.options.detectChanges(field)));\n    }\n    for (const key of Object.keys(field._expressions || {})) {\n      const expressionObserver = observe(field, ['_expressions', key], ({\n        currentValue,\n        previousValue\n      }) => {\n        if (previousValue?.subscription) {\n          previousValue.subscription.unsubscribe();\n          previousValue.subscription = null;\n        }\n        if (isObservable(currentValue.value$)) {\n          currentValue.subscription = currentValue.value$.subscribe();\n        }\n      });\n      subscribes.push(() => {\n        if (field._expressions[key]?.subscription) {\n          field._expressions[key].subscription.unsubscribe();\n        }\n        expressionObserver.unsubscribe();\n      });\n    }\n    for (const path of [['template'], ['fieldGroupClassName'], ['validation', 'show']]) {\n      const fieldObserver = observe(field, path, ({\n        firstChange\n      }) => !firstChange && field.options.detectChanges(field));\n      subscribes.push(() => fieldObserver.unsubscribe());\n    }\n    if (field.formControl && !field.fieldGroup) {\n      const control = field.formControl;\n      let valueChanges = control.valueChanges.pipe(distinctUntilChanged((x, y) => {\n        if (x !== y || Array.isArray(x) || isObject(x)) {\n          return false;\n        }\n        return true;\n      }));\n      if (control.value !== getFieldValue(field)) {\n        valueChanges = valueChanges.pipe(startWith(control.value));\n      }\n      const {\n        updateOn,\n        debounce\n      } = field.modelOptions;\n      if ((!updateOn || updateOn === 'change') && debounce?.default > 0) {\n        valueChanges = control.valueChanges.pipe(debounceTime(debounce.default));\n      }\n      const sub = valueChanges.subscribe(value => {\n        // workaround for https://github.com/angular/angular/issues/13792\n        if (control._fields?.length > 1 && control instanceof FormControl) {\n          control.patchValue(value, {\n            emitEvent: false,\n            onlySelf: true\n          });\n        }\n        field.parsers?.forEach(parserFn => value = parserFn(value));\n        if (value !== field.formControl.value) {\n          field.formControl.setValue(value);\n          return;\n        }\n        if (hasKey(field)) {\n          assignFieldValue(field, value);\n        }\n        field.options.fieldChanges.next({\n          value,\n          field,\n          type: 'valueChanges'\n        });\n      });\n      subscribes.push(() => sub.unsubscribe());\n    }\n    return () => subscribes.forEach(subscribe => subscribe());\n  }\n}\nFormlyField.ɵfac = function FormlyField_Factory(t) {\n  return new (t || FormlyField)(i0.ɵɵdirectiveInject(FormlyConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(FormlyFieldTemplates, 8));\n};\nFormlyField.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FormlyField,\n  selectors: [[\"formly-field\"]],\n  viewQuery: function FormlyField_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7, ViewContainerRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.viewContainerRef = _t.first);\n    }\n  },\n  inputs: {\n    field: \"field\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 2,\n  vars: 0,\n  consts: [[\"container\", \"\"]],\n  template: function FormlyField_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, FormlyField_ng_template_0_Template, 0, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    }\n  },\n  styles: [\"[_nghost-%COMP%]:empty{display:none}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyField, [{\n    type: Component,\n    args: [{\n      selector: 'formly-field',\n      template: '<ng-template #container></ng-template>',\n      styles: [\":host:empty{display:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: FormlyConfig\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: FormlyFieldTemplates,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    field: [{\n      type: Input\n    }],\n    viewContainerRef: [{\n      type: ViewChild,\n      args: ['container', {\n        read: ViewContainerRef,\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * The `<form-form>` component is the main container of the form,\n * which takes care of managing the form state\n * and delegates the rendering of each field to `<formly-field>` component.\n */\nclass FormlyForm {\n  constructor(builder, config, ngZone, fieldTemplates) {\n    this.builder = builder;\n    this.config = config;\n    this.ngZone = ngZone;\n    this.fieldTemplates = fieldTemplates;\n    /** Event that is emitted when the model value is changed */\n    this.modelChange = new EventEmitter();\n    this.field = {\n      type: 'formly-group'\n    };\n    this._modelChangeValue = {};\n    this.valueChangesUnsubscribe = () => {};\n  }\n  /** The form instance which allow to track model value and validation status. */\n  set form(form) {\n    this.field.form = form;\n  }\n  get form() {\n    return this.field.form;\n  }\n  /** The model to be represented by the form. */\n  set model(model) {\n    if (this.config.extras.immutable && this._modelChangeValue === model) {\n      return;\n    }\n    this.setField({\n      model\n    });\n  }\n  get model() {\n    return this.field.model;\n  }\n  /** The field configurations for building the form. */\n  set fields(fieldGroup) {\n    this.setField({\n      fieldGroup\n    });\n  }\n  get fields() {\n    return this.field.fieldGroup;\n  }\n  /** Options for the form. */\n  set options(options) {\n    this.setField({\n      options\n    });\n  }\n  get options() {\n    return this.field.options;\n  }\n  set templates(templates) {\n    this.fieldTemplates.templates = templates;\n  }\n  ngDoCheck() {\n    if (this.config.extras.checkExpressionOn === 'changeDetectionCheck') {\n      this.checkExpressionChange();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.fields && this.form) {\n      clearControl(this.form);\n    }\n    if (changes.fields || changes.form || changes.model && this._modelChangeValue !== changes.model.currentValue) {\n      this.valueChangesUnsubscribe();\n      this.builder.build(this.field);\n      this.valueChangesUnsubscribe = this.valueChanges();\n    }\n  }\n  ngOnDestroy() {\n    this.valueChangesUnsubscribe();\n  }\n  checkExpressionChange() {\n    this.field.options.checkExpressions?.(this.field);\n  }\n  valueChanges() {\n    this.valueChangesUnsubscribe();\n    const sub = this.field.options.fieldChanges.pipe(filter(({\n      field,\n      type\n    }) => hasKey(field) && type === 'valueChanges'), switchMap(() => this.ngZone.onStable.asObservable().pipe(take(1)))).subscribe(() => this.ngZone.runGuarded(() => {\n      // runGuarded is used to keep in sync the expression changes\n      // https://github.com/ngx-formly/ngx-formly/issues/2095\n      this.checkExpressionChange();\n      this.modelChange.emit(this._modelChangeValue = clone(this.model));\n    }));\n    return () => sub.unsubscribe();\n  }\n  setField(field) {\n    if (this.config.extras.immutable) {\n      this.field = {\n        ...this.field,\n        ...clone(field)\n      };\n    } else {\n      Object.keys(field).forEach(p => this.field[p] = field[p]);\n    }\n  }\n}\nFormlyForm.ɵfac = function FormlyForm_Factory(t) {\n  return new (t || FormlyForm)(i0.ɵɵdirectiveInject(FormlyFormBuilder), i0.ɵɵdirectiveInject(FormlyConfig), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(FormlyFieldTemplates));\n};\nFormlyForm.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FormlyForm,\n  selectors: [[\"formly-form\"]],\n  contentQueries: function FormlyForm_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, FormlyTemplate, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  inputs: {\n    form: \"form\",\n    model: \"model\",\n    fields: \"fields\",\n    options: \"options\"\n  },\n  outputs: {\n    modelChange: \"modelChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([FormlyFormBuilder, FormlyFieldTemplates]), i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"field\"]],\n  template: function FormlyForm_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"formly-field\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"field\", ctx.field);\n    }\n  },\n  dependencies: [FormlyField],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyForm, [{\n    type: Component,\n    args: [{\n      selector: 'formly-form',\n      template: '<formly-field [field]=\"field\"></formly-field>',\n      providers: [FormlyFormBuilder, FormlyFieldTemplates],\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: FormlyFormBuilder\n    }, {\n      type: FormlyConfig\n    }, {\n      type: i0.NgZone\n    }, {\n      type: FormlyFieldTemplates\n    }];\n  }, {\n    form: [{\n      type: Input\n    }],\n    model: [{\n      type: Input\n    }],\n    fields: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    modelChange: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [FormlyTemplate]\n    }]\n  });\n})();\n\n/**\n * Allow to link the `field` HTML attributes (`id`, `name` ...) and Event attributes (`focus`, `blur` ...) to an element in the DOM.\n */\nclass FormlyAttributes {\n  constructor(renderer, elementRef, _document) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.uiAttributesCache = {};\n    /**\n     * HostBinding doesn't register listeners conditionally which may produce some perf issues.\n     *\n     * Formly issue: https://github.com/ngx-formly/ngx-formly/issues/1991\n     */\n    this.uiEvents = {\n      listeners: [],\n      events: ['click', 'keyup', 'keydown', 'keypress', 'focus', 'blur', 'change'],\n      callback: (eventName, $event) => {\n        switch (eventName) {\n          case 'focus':\n            return this.onFocus($event);\n          case 'blur':\n            return this.onBlur($event);\n          case 'change':\n            return this.onChange($event);\n          default:\n            return this.props[eventName](this.field, $event);\n        }\n      }\n    };\n    this.document = _document;\n  }\n  get props() {\n    return this.field.props || {};\n  }\n  get fieldAttrElements() {\n    return this.field?.['_elementRefs'] || [];\n  }\n  ngOnChanges(changes) {\n    if (changes.field) {\n      this.field.name && this.setAttribute('name', this.field.name);\n      this.uiEvents.listeners.forEach(listener => listener());\n      this.uiEvents.events.forEach(eventName => {\n        if (this.props?.[eventName] || ['focus', 'blur', 'change'].indexOf(eventName) !== -1) {\n          this.uiEvents.listeners.push(this.renderer.listen(this.elementRef.nativeElement, eventName, e => this.uiEvents.callback(eventName, e)));\n        }\n      });\n      if (this.props?.attributes) {\n        observe(this.field, ['props', 'attributes'], ({\n          currentValue,\n          previousValue\n        }) => {\n          if (previousValue) {\n            Object.keys(previousValue).forEach(attr => this.removeAttribute(attr));\n          }\n          if (currentValue) {\n            Object.keys(currentValue).forEach(attr => {\n              if (currentValue[attr] != null) {\n                this.setAttribute(attr, currentValue[attr]);\n              }\n            });\n          }\n        });\n      }\n      this.detachElementRef(changes.field.previousValue);\n      this.attachElementRef(changes.field.currentValue);\n      if (this.fieldAttrElements.length === 1) {\n        !this.id && this.field.id && this.setAttribute('id', this.field.id);\n        this.focusObserver = observe(this.field, ['focus'], ({\n          currentValue\n        }) => {\n          this.toggleFocus(currentValue);\n        });\n      }\n    }\n    if (changes.id) {\n      this.setAttribute('id', this.id);\n    }\n  }\n  /**\n   * We need to re-evaluate all the attributes on every change detection cycle, because\n   * by using a HostBinding we run into certain edge cases. This means that whatever logic\n   * is in here has to be super lean or we risk seriously damaging or destroying the performance.\n   *\n   * Formly issue: https://github.com/ngx-formly/ngx-formly/issues/1317\n   * Material issue: https://github.com/angular/components/issues/14024\n   */\n  ngDoCheck() {\n    if (!this.uiAttributes) {\n      const element = this.elementRef.nativeElement;\n      this.uiAttributes = [...FORMLY_VALIDATORS, 'tabindex', 'placeholder', 'readonly', 'disabled', 'step'].filter(attr => !element.hasAttribute || !element.hasAttribute(attr));\n    }\n    this.uiAttributes.forEach(attr => {\n      const value = this.props[attr];\n      if (this.uiAttributesCache[attr] !== value && (!this.props.attributes || !this.props.attributes.hasOwnProperty(attr.toLowerCase()))) {\n        this.uiAttributesCache[attr] = value;\n        if (value || value === 0) {\n          this.setAttribute(attr, value === true ? attr : `${value}`);\n        } else {\n          this.removeAttribute(attr);\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.uiEvents.listeners.forEach(listener => listener());\n    this.detachElementRef(this.field);\n    this.focusObserver?.unsubscribe();\n  }\n  toggleFocus(value) {\n    const element = this.fieldAttrElements ? this.fieldAttrElements[0] : null;\n    if (!element || !element.nativeElement.focus) {\n      return;\n    }\n    const isFocused = !!this.document.activeElement && this.fieldAttrElements.some(({\n      nativeElement\n    }) => this.document.activeElement === nativeElement || nativeElement.contains(this.document.activeElement));\n    if (value && !isFocused) {\n      Promise.resolve().then(() => element.nativeElement.focus());\n    } else if (!value && isFocused) {\n      Promise.resolve().then(() => element.nativeElement.blur());\n    }\n  }\n  onFocus($event) {\n    this.focusObserver?.setValue(true);\n    this.props.focus?.(this.field, $event);\n  }\n  onBlur($event) {\n    this.focusObserver?.setValue(false);\n    this.props.blur?.(this.field, $event);\n  }\n  // handle custom `change` event, for regular ones rely on DOM listener\n  onHostChange($event) {\n    if ($event instanceof Event) {\n      return;\n    }\n    this.onChange($event);\n  }\n  onChange($event) {\n    this.props.change?.(this.field, $event);\n    this.field.formControl?.markAsDirty();\n  }\n  attachElementRef(f) {\n    if (!f) {\n      return;\n    }\n    if (f['_elementRefs']?.indexOf(this.elementRef) === -1) {\n      f['_elementRefs'].push(this.elementRef);\n    } else {\n      defineHiddenProp(f, '_elementRefs', [this.elementRef]);\n    }\n  }\n  detachElementRef(f) {\n    const index = f?.['_elementRefs'] ? this.fieldAttrElements.indexOf(this.elementRef) : -1;\n    if (index !== -1) {\n      f['_elementRefs'].splice(index, 1);\n    }\n  }\n  setAttribute(attr, value) {\n    this.renderer.setAttribute(this.elementRef.nativeElement, attr, value);\n  }\n  removeAttribute(attr) {\n    this.renderer.removeAttribute(this.elementRef.nativeElement, attr);\n  }\n}\nFormlyAttributes.ɵfac = function FormlyAttributes_Factory(t) {\n  return new (t || FormlyAttributes)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nFormlyAttributes.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: FormlyAttributes,\n  selectors: [[\"\", \"formlyAttributes\", \"\"]],\n  hostBindings: function FormlyAttributes_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"change\", function FormlyAttributes_change_HostBindingHandler($event) {\n        return ctx.onHostChange($event);\n      });\n    }\n  },\n  inputs: {\n    field: [\"formlyAttributes\", \"field\"],\n    id: \"id\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyAttributes, [{\n    type: Directive,\n    args: [{\n      selector: '[formlyAttributes]',\n      host: {\n        '(change)': 'onHostChange($event)'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    field: [{\n      type: Input,\n      args: ['formlyAttributes']\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass FieldType {\n  get model() {\n    return this.field.model;\n  }\n  get form() {\n    return this.field.form;\n  }\n  get options() {\n    return this.field.options;\n  }\n  get key() {\n    return this.field.key;\n  }\n  get formControl() {\n    return this.field.formControl;\n  }\n  get props() {\n    return this.field.props || {};\n  }\n  /** @deprecated Use `props` instead. */\n  get to() {\n    return this.props;\n  }\n  get showError() {\n    return this.options.showError(this);\n  }\n  get id() {\n    return this.field.id;\n  }\n  get formState() {\n    return this.options.formState || {};\n  }\n}\nFieldType.ɵfac = function FieldType_Factory(t) {\n  return new (t || FieldType)();\n};\nFieldType.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: FieldType,\n  inputs: {\n    field: \"field\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldType, [{\n    type: Directive\n  }], null, {\n    field: [{\n      type: Input\n    }]\n  });\n})();\n\n/** @ignore */\nclass FormlyGroup extends FieldType {}\nFormlyGroup.ɵfac = /* @__PURE__ */function () {\n  let ɵFormlyGroup_BaseFactory;\n  return function FormlyGroup_Factory(t) {\n    return (ɵFormlyGroup_BaseFactory || (ɵFormlyGroup_BaseFactory = i0.ɵɵgetInheritedFactory(FormlyGroup)))(t || FormlyGroup);\n  };\n}();\nFormlyGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FormlyGroup,\n  selectors: [[\"formly-group\"]],\n  hostVars: 2,\n  hostBindings: function FormlyGroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.field.fieldGroupClassName || \"\");\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 2,\n  vars: 1,\n  consts: [[3, \"field\", 4, \"ngFor\", \"ngForOf\"], [3, \"field\"]],\n  template: function FormlyGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, FormlyGroup_formly_field_0_Template, 1, 1, \"formly-field\", 0);\n      i0.ɵɵprojection(1);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngForOf\", ctx.field.fieldGroup);\n    }\n  },\n  dependencies: [FormlyField, i2$1.NgForOf],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyGroup, [{\n    type: Component,\n    args: [{\n      selector: 'formly-group',\n      template: `\n    <formly-field *ngFor=\"let f of field.fieldGroup\" [field]=\"f\"></formly-field>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class]': 'field.fieldGroupClassName || \"\"'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\n\n/**\n * The `<formly-validation-message>` component renders the error message of a given `field`.\n */\nclass FormlyValidationMessage {\n  constructor(config) {\n    this.config = config;\n  }\n  ngOnChanges() {\n    const EXPR_VALIDATORS = FORMLY_VALIDATORS.map(v => `templateOptions.${v}`);\n    this.errorMessage$ = merge(this.field.formControl.statusChanges, !this.field.options ? of(null) : this.field.options.fieldChanges.pipe(filter(({\n      field,\n      type,\n      property\n    }) => {\n      return field === this.field && type === 'expressionChanges' && (property.indexOf('validation') !== -1 || EXPR_VALIDATORS.indexOf(property) !== -1);\n    }))).pipe(startWith(null), switchMap(() => isObservable(this.errorMessage) ? this.errorMessage : of(this.errorMessage)));\n  }\n  get errorMessage() {\n    const fieldForm = this.field.formControl;\n    for (const error in fieldForm.errors) {\n      if (fieldForm.errors.hasOwnProperty(error)) {\n        let message = this.config.getValidatorMessage(error);\n        if (isObject(fieldForm.errors[error])) {\n          if (fieldForm.errors[error].errorPath) {\n            return undefined;\n          }\n          if (fieldForm.errors[error].message) {\n            message = fieldForm.errors[error].message;\n          }\n        }\n        if (this.field.validation?.messages?.[error]) {\n          message = this.field.validation.messages[error];\n        }\n        if (this.field.validators?.[error]?.message) {\n          message = this.field.validators[error].message;\n        }\n        if (this.field.asyncValidators?.[error]?.message) {\n          message = this.field.asyncValidators[error].message;\n        }\n        if (typeof message === 'function') {\n          return message(fieldForm.errors[error], this.field);\n        }\n        return message;\n      }\n    }\n    return undefined;\n  }\n}\nFormlyValidationMessage.ɵfac = function FormlyValidationMessage_Factory(t) {\n  return new (t || FormlyValidationMessage)(i0.ɵɵdirectiveInject(FormlyConfig));\n};\nFormlyValidationMessage.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FormlyValidationMessage,\n  selectors: [[\"formly-validation-message\"]],\n  inputs: {\n    field: \"field\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 2,\n  vars: 3,\n  template: function FormlyValidationMessage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtext(0);\n      i0.ɵɵpipe(1, \"async\");\n    }\n    if (rf & 2) {\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(1, 1, ctx.errorMessage$));\n    }\n  },\n  dependencies: [i2$1.AsyncPipe],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyValidationMessage, [{\n    type: Component,\n    args: [{\n      selector: 'formly-validation-message',\n      template: '{{ errorMessage$ | async }}',\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: FormlyConfig\n    }];\n  }, {\n    field: [{\n      type: Input\n    }]\n  });\n})();\nclass FieldArrayType extends FieldType {\n  onPopulate(field) {\n    if (!field.formControl && hasKey(field)) {\n      const control = findControl(field);\n      registerControl(field, control ? control : new FormArray([], {\n        updateOn: field.modelOptions.updateOn\n      }));\n    }\n    field.fieldGroup = field.fieldGroup || [];\n    const length = Array.isArray(field.model) ? field.model.length : 0;\n    if (field.fieldGroup.length > length) {\n      for (let i = field.fieldGroup.length - 1; i >= length; --i) {\n        unregisterControl(field.fieldGroup[i], true);\n        field.fieldGroup.splice(i, 1);\n      }\n    }\n    for (let i = field.fieldGroup.length; i < length; i++) {\n      const f = {\n        ...clone(typeof field.fieldArray === 'function' ? field.fieldArray(field) : field.fieldArray),\n        key: `${i}`\n      };\n      field.fieldGroup.push(f);\n    }\n  }\n  add(i, initialModel, {\n    markAsDirty\n  } = {\n    markAsDirty: true\n  }) {\n    i = i == null ? this.field.fieldGroup.length : i;\n    if (!this.model) {\n      assignFieldValue(this.field, []);\n    }\n    this.model.splice(i, 0, initialModel ? clone(initialModel) : undefined);\n    this._build();\n    markAsDirty && this.formControl.markAsDirty();\n  }\n  remove(i, {\n    markAsDirty\n  } = {\n    markAsDirty: true\n  }) {\n    this.model.splice(i, 1);\n    const field = this.field.fieldGroup[i];\n    this.field.fieldGroup.splice(i, 1);\n    this.field.fieldGroup.forEach((f, key) => f.key = `${key}`);\n    unregisterControl(field, true);\n    this._build();\n    markAsDirty && this.formControl.markAsDirty();\n  }\n  _build() {\n    const fields = this.field.formControl._fields ?? [this.field];\n    fields.forEach(f => this.options.build(f));\n    this.options.fieldChanges.next({\n      field: this.field,\n      value: getFieldValue(this.field),\n      type: 'valueChanges'\n    });\n  }\n}\nFieldArrayType.ɵfac = /* @__PURE__ */function () {\n  let ɵFieldArrayType_BaseFactory;\n  return function FieldArrayType_Factory(t) {\n    return (ɵFieldArrayType_BaseFactory || (ɵFieldArrayType_BaseFactory = i0.ɵɵgetInheritedFactory(FieldArrayType)))(t || FieldArrayType);\n  };\n}();\nFieldArrayType.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: FieldArrayType,\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldArrayType, [{\n    type: Directive\n  }], null, null);\n})();\nclass FieldWrapper extends FieldType {\n  set _staticContent(content) {\n    this.fieldComponent = content;\n  }\n}\nFieldWrapper.ɵfac = /* @__PURE__ */function () {\n  let ɵFieldWrapper_BaseFactory;\n  return function FieldWrapper_Factory(t) {\n    return (ɵFieldWrapper_BaseFactory || (ɵFieldWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(FieldWrapper)))(t || FieldWrapper);\n  };\n}();\nFieldWrapper.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: FieldWrapper,\n  viewQuery: function FieldWrapper_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c2, 5, ViewContainerRef);\n      i0.ɵɵviewQuery(_c2, 7, ViewContainerRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fieldComponent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._staticContent = _t.first);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldWrapper, [{\n    type: Directive\n  }], null, {\n    fieldComponent: [{\n      type: ViewChild,\n      args: ['fieldComponent', {\n        read: ViewContainerRef\n      }]\n    }],\n    _staticContent: [{\n      type: ViewChild,\n      args: ['fieldComponent', {\n        read: ViewContainerRef,\n        static: true\n      }]\n    }]\n  });\n})();\n\n/** @ignore */\nclass FormlyTemplateType extends FieldType {\n  constructor(sanitizer) {\n    super();\n    this.sanitizer = sanitizer;\n    this.innerHtml = {};\n  }\n  get template() {\n    if (this.field && this.field.template !== this.innerHtml.template) {\n      this.innerHtml = {\n        template: this.field.template,\n        content: this.props.safeHtml ? this.sanitizer.bypassSecurityTrustHtml(this.field.template) : this.field.template\n      };\n    }\n    return this.innerHtml.content;\n  }\n}\nFormlyTemplateType.ɵfac = function FormlyTemplateType_Factory(t) {\n  return new (t || FormlyTemplateType)(i0.ɵɵdirectiveInject(i1.DomSanitizer));\n};\nFormlyTemplateType.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FormlyTemplateType,\n  selectors: [[\"formly-template\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"innerHtml\"]],\n  template: function FormlyTemplateType_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"innerHtml\", ctx.template, i0.ɵɵsanitizeHtml);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyTemplateType, [{\n    type: Component,\n    args: [{\n      selector: 'formly-template',\n      template: `<div [innerHtml]=\"template\"></div>`,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: i1.DomSanitizer\n    }];\n  }, null);\n})();\nfunction evalStringExpression(expression, argNames) {\n  try {\n    return Function(...argNames, `return ${expression};`);\n  } catch (error) {\n    console.error(error);\n  }\n}\nfunction evalExpression(expression, thisArg, argVal) {\n  if (typeof expression === 'function') {\n    return expression.apply(thisArg, argVal);\n  } else {\n    return expression ? true : false;\n  }\n}\nclass FieldExpressionExtension {\n  onPopulate(field) {\n    if (field._expressions) {\n      return;\n    }\n    // cache built expression\n    defineHiddenProp(field, '_expressions', {});\n    observe(field, ['hide'], ({\n      currentValue,\n      firstChange\n    }) => {\n      defineHiddenProp(field, '_hide', !!currentValue);\n      if (!firstChange || firstChange && currentValue === true) {\n        field.props.hidden = currentValue;\n        field.options._hiddenFieldsForCheck.push(field);\n      }\n    });\n    if (field.hideExpression) {\n      observe(field, ['hideExpression'], ({\n        currentValue: expr\n      }) => {\n        field._expressions.hide = this.parseExpressions(field, 'hide', typeof expr === 'boolean' ? () => expr : expr);\n      });\n    }\n    const evalExpr = (key, expr) => {\n      if (typeof expr === 'string' || isFunction(expr)) {\n        field._expressions[key] = this.parseExpressions(field, key, expr);\n      } else if (expr instanceof Observable) {\n        field._expressions[key] = {\n          value$: expr.pipe(tap(v => {\n            this.evalExpr(field, key, v);\n            field.options.detectChanges(field);\n          }))\n        };\n      }\n    };\n    field.expressions = field.expressions || {};\n    for (const key of Object.keys(field.expressions)) {\n      observe(field, ['expressions', key], ({\n        currentValue: expr\n      }) => {\n        evalExpr(key, isFunction(expr) ? (...args) => expr(field, args[3]) : expr);\n      });\n    }\n    field.expressionProperties = field.expressionProperties || {};\n    for (const key of Object.keys(field.expressionProperties)) {\n      observe(field, ['expressionProperties', key], ({\n        currentValue\n      }) => evalExpr(key, currentValue));\n    }\n  }\n  postPopulate(field) {\n    if (field.parent) {\n      return;\n    }\n    if (!field.options.checkExpressions) {\n      let checkLocked = false;\n      field.options.checkExpressions = (f, ignoreCache) => {\n        if (checkLocked) {\n          return;\n        }\n        checkLocked = true;\n        const fieldChanged = this.checkExpressions(f, ignoreCache);\n        const options = field.options;\n        options._hiddenFieldsForCheck.sort(f => f.hide ? -1 : 1).forEach(f => this.changeHideState(f, f.hide, !ignoreCache));\n        options._hiddenFieldsForCheck = [];\n        if (fieldChanged) {\n          this.checkExpressions(field);\n          if (field.options && field.options.detectChanges) {\n            field.options.detectChanges(field);\n          }\n        }\n        checkLocked = false;\n      };\n      field.options._checkField = (f, ignoreCache) => {\n        console.warn(`Formly: 'options._checkField' is deprecated since v6.0, use 'options.checkExpressions' instead.`);\n        field.options.checkExpressions(f, ignoreCache);\n      };\n    }\n  }\n  parseExpressions(field, path, expr) {\n    let parentExpression;\n    if (field.parent && ['hide', 'props.disabled'].includes(path)) {\n      const rootValue = f => {\n        return path === 'hide' ? f.hide : f.props.disabled;\n      };\n      parentExpression = () => {\n        let root = field.parent;\n        while (root.parent && !rootValue(root)) {\n          root = root.parent;\n        }\n        return rootValue(root);\n      };\n    }\n    expr = expr || (() => false);\n    if (typeof expr === 'string') {\n      expr = evalStringExpression(expr, ['model', 'formState', 'field']);\n    }\n    let currentValue;\n    return {\n      callback: ignoreCache => {\n        try {\n          const exprValue = evalExpression(parentExpression ? (...args) => parentExpression(field) || expr(...args) : expr, {\n            field\n          }, [field.model, field.options.formState, field, ignoreCache]);\n          if (ignoreCache || currentValue !== exprValue && (!isObject(exprValue) || isObservable(exprValue) || JSON.stringify(exprValue) !== JSON.stringify(currentValue))) {\n            currentValue = exprValue;\n            this.evalExpr(field, path, exprValue);\n            return true;\n          }\n          return false;\n        } catch (error) {\n          error.message = `[Formly Error] [Expression \"${path}\"] ${error.message}`;\n          throw error;\n        }\n      }\n    };\n  }\n  checkExpressions(field, ignoreCache = false) {\n    if (!field) {\n      return false;\n    }\n    let fieldChanged = false;\n    if (field._expressions) {\n      for (const key of Object.keys(field._expressions)) {\n        field._expressions[key].callback?.(ignoreCache) && (fieldChanged = true);\n      }\n    }\n    field.fieldGroup?.forEach(f => this.checkExpressions(f, ignoreCache) && (fieldChanged = true));\n    return fieldChanged;\n  }\n  changeDisabledState(field, value) {\n    if (field.fieldGroup) {\n      field.fieldGroup.filter(f => !f._expressions.hasOwnProperty('props.disabled')).forEach(f => this.changeDisabledState(f, value));\n    }\n    if (hasKey(field) && field.props.disabled !== value) {\n      field.props.disabled = value;\n    }\n  }\n  changeHideState(field, hide, resetOnHide) {\n    if (field.fieldGroup) {\n      field.fieldGroup.filter(f => !f._expressions.hide).forEach(f => this.changeHideState(f, hide, resetOnHide));\n    }\n    if (field.formControl && hasKey(field)) {\n      defineHiddenProp(field, '_hide', !!(hide || field.hide));\n      const c = field.formControl;\n      if (c._fields?.length > 1) {\n        updateValidity(c);\n      }\n      if (hide === true && (!c._fields || c._fields.every(f => !!f._hide))) {\n        unregisterControl(field, true);\n        if (resetOnHide && field.resetOnHide) {\n          assignFieldValue(field, undefined);\n          field.formControl.reset({\n            value: undefined,\n            disabled: field.formControl.disabled\n          });\n          field.options.fieldChanges.next({\n            value: undefined,\n            field,\n            type: 'valueChanges'\n          });\n          if (field.fieldGroup && field.formControl instanceof FormArray) {\n            field.fieldGroup.length = 0;\n          }\n        }\n      } else if (hide === false) {\n        if (field.resetOnHide && !isUndefined(field.defaultValue) && isUndefined(getFieldValue(field))) {\n          assignFieldValue(field, field.defaultValue);\n        }\n        registerControl(field, undefined, true);\n        if (field.resetOnHide && field.fieldArray && field.fieldGroup?.length !== field.model?.length) {\n          field.options.build(field);\n        }\n      }\n    }\n    if (field.options.fieldChanges) {\n      field.options.fieldChanges.next({\n        field,\n        type: 'hidden',\n        value: hide\n      });\n    }\n  }\n  evalExpr(field, prop, value) {\n    if (prop.indexOf('model.') === 0) {\n      const key = prop.replace(/^model\\./, ''),\n        parent = field.fieldGroup ? field : field.parent;\n      let control = field?.key === key ? field.formControl : field.form.get(key);\n      if (!control && field.get(key)) {\n        control = field.get(key).formControl;\n      }\n      assignFieldValue({\n        key,\n        parent,\n        model: field.model\n      }, value);\n      if (control && !(isNil(control.value) && isNil(value)) && control.value !== value) {\n        control.patchValue(value);\n      }\n    } else {\n      try {\n        let target = field;\n        const paths = this._evalExpressionPath(field, prop);\n        const lastIndex = paths.length - 1;\n        for (let i = 0; i < lastIndex; i++) {\n          target = target[paths[i]];\n        }\n        target[paths[lastIndex]] = value;\n      } catch (error) {\n        error.message = `[Formly Error] [Expression \"${prop}\"] ${error.message}`;\n        throw error;\n      }\n      if (['templateOptions.disabled', 'props.disabled'].includes(prop) && hasKey(field)) {\n        this.changeDisabledState(field, value);\n      }\n    }\n    this.emitExpressionChanges(field, prop, value);\n  }\n  emitExpressionChanges(field, property, value) {\n    if (!field.options.fieldChanges) {\n      return;\n    }\n    field.options.fieldChanges.next({\n      field,\n      type: 'expressionChanges',\n      property,\n      value\n    });\n  }\n  _evalExpressionPath(field, prop) {\n    if (field._expressions[prop] && field._expressions[prop].paths) {\n      return field._expressions[prop].paths;\n    }\n    let paths = [];\n    if (prop.indexOf('[') === -1) {\n      paths = prop.split('.');\n    } else {\n      prop.split(/[[\\]]{1,2}/) // https://stackoverflow.com/a/20198206\n      .filter(p => p).forEach(path => {\n        const arrayPath = path.match(/['|\"](.*?)['|\"]/);\n        if (arrayPath) {\n          paths.push(arrayPath[1]);\n        } else {\n          paths.push(...path.split('.').filter(p => p));\n        }\n      });\n    }\n    if (field._expressions[prop]) {\n      field._expressions[prop].paths = paths;\n    }\n    return paths;\n  }\n}\nclass FieldValidationExtension {\n  constructor(config) {\n    this.config = config;\n  }\n  onPopulate(field) {\n    this.initFieldValidation(field, 'validators');\n    this.initFieldValidation(field, 'asyncValidators');\n  }\n  initFieldValidation(field, type) {\n    const validators = [];\n    if (type === 'validators' && !(field.hasOwnProperty('fieldGroup') && !hasKey(field))) {\n      validators.push(this.getPredefinedFieldValidation(field));\n    }\n    if (field[type]) {\n      for (const validatorName of Object.keys(field[type])) {\n        validatorName === 'validation' ? validators.push(...field[type].validation.map(v => this.wrapNgValidatorFn(field, v))) : validators.push(this.wrapNgValidatorFn(field, field[type][validatorName], validatorName));\n      }\n    }\n    defineHiddenProp(field, '_' + type, validators);\n  }\n  getPredefinedFieldValidation(field) {\n    let VALIDATORS = [];\n    FORMLY_VALIDATORS.forEach(opt => observe(field, ['props', opt], ({\n      currentValue,\n      firstChange\n    }) => {\n      VALIDATORS = VALIDATORS.filter(o => o !== opt);\n      if (currentValue != null && currentValue !== false) {\n        VALIDATORS.push(opt);\n      }\n      if (!firstChange && field.formControl) {\n        updateValidity(field.formControl);\n      }\n    }));\n    return control => {\n      if (VALIDATORS.length === 0) {\n        return null;\n      }\n      return Validators.compose(VALIDATORS.map(opt => () => {\n        const value = field.props[opt];\n        switch (opt) {\n          case 'required':\n            return Validators.required(control);\n          case 'pattern':\n            return Validators.pattern(value)(control);\n          case 'minLength':\n            const minLengthResult = Validators.minLength(value)(control);\n            const minLengthKey = this.config.getValidatorMessage('minlength') || field.validation?.messages?.minlength ? 'minlength' : 'minLength';\n            return minLengthResult ? {\n              [minLengthKey]: minLengthResult.minlength\n            } : null;\n          case 'maxLength':\n            const maxLengthResult = Validators.maxLength(value)(control);\n            const maxLengthKey = this.config.getValidatorMessage('maxlength') || field.validation?.messages?.maxlength ? 'maxlength' : 'maxLength';\n            return maxLengthResult ? {\n              [maxLengthKey]: maxLengthResult.maxlength\n            } : null;\n          case 'min':\n            return Validators.min(value)(control);\n          case 'max':\n            return Validators.max(value)(control);\n          default:\n            return null;\n        }\n      }))(control);\n    };\n  }\n  wrapNgValidatorFn(field, validator, validatorName) {\n    let validatorOption;\n    if (typeof validator === 'string') {\n      validatorOption = clone(this.config.getValidator(validator));\n    }\n    if (typeof validator === 'object' && validator.name) {\n      validatorOption = clone(this.config.getValidator(validator.name));\n      if (validator.options) {\n        validatorOption.options = validator.options;\n      }\n    }\n    if (typeof validator === 'object' && validator.expression) {\n      const {\n        expression,\n        ...options\n      } = validator;\n      validatorOption = {\n        name: validatorName,\n        validation: expression,\n        options: Object.keys(options).length > 0 ? options : null\n      };\n    }\n    if (typeof validator === 'function') {\n      validatorOption = {\n        name: validatorName,\n        validation: validator\n      };\n    }\n    return control => {\n      const errors = validatorOption.validation(control, field, validatorOption.options);\n      if (isPromise(errors)) {\n        return errors.then(v => this.handleAsyncResult(field, validatorName ? !!v : v, validatorOption));\n      }\n      if (isObservable(errors)) {\n        return errors.pipe(map(v => this.handleAsyncResult(field, validatorName ? !!v : v, validatorOption)));\n      }\n      return this.handleResult(field, validatorName ? !!errors : errors, validatorOption);\n    };\n  }\n  handleAsyncResult(field, errors, options) {\n    // workaround for https://github.com/angular/angular/issues/13200\n    field.options.detectChanges(field);\n    return this.handleResult(field, errors, options);\n  }\n  handleResult(field, errors, {\n    name,\n    options\n  }) {\n    if (typeof errors === 'boolean') {\n      errors = errors ? null : {\n        [name]: options ? options : true\n      };\n    }\n    const ctrl = field.formControl;\n    ctrl?._childrenErrors?.[name]?.();\n    if (isObject(errors)) {\n      Object.keys(errors).forEach(name => {\n        const errorPath = errors[name].errorPath ? errors[name].errorPath : options?.errorPath;\n        const childCtrl = errorPath ? field.formControl.get(errorPath) : null;\n        if (childCtrl) {\n          const {\n            errorPath: _errorPath,\n            ...opts\n          } = errors[name];\n          childCtrl.setErrors({\n            ...(childCtrl.errors || {}),\n            [name]: opts\n          });\n          !ctrl._childrenErrors && defineHiddenProp(ctrl, '_childrenErrors', {});\n          ctrl._childrenErrors[name] = () => {\n            const {\n              [name]: _toDelete,\n              ...childErrors\n            } = childCtrl.errors || {};\n            childCtrl.setErrors(Object.keys(childErrors).length === 0 ? null : childErrors);\n          };\n        }\n      });\n    }\n    return errors;\n  }\n}\nclass FieldFormExtension {\n  prePopulate(field) {\n    if (!this.root) {\n      this.root = field;\n    }\n    if (field.parent) {\n      Object.defineProperty(field, 'form', {\n        get: () => field.parent.formControl,\n        configurable: true\n      });\n    }\n  }\n  onPopulate(field) {\n    if (field.hasOwnProperty('fieldGroup') && !hasKey(field)) {\n      defineHiddenProp(field, 'formControl', field.form);\n    } else {\n      this.addFormControl(field);\n    }\n  }\n  postPopulate(field) {\n    if (this.root !== field) {\n      return;\n    }\n    this.root = null;\n    const markForCheck = this.setValidators(field);\n    if (markForCheck && field.parent) {\n      let parent = field.parent;\n      while (parent) {\n        if (hasKey(parent) || !parent.parent) {\n          updateValidity(parent.formControl, true);\n        }\n        parent = parent.parent;\n      }\n    }\n  }\n  addFormControl(field) {\n    let control = findControl(field);\n    if (field.fieldArray) {\n      return;\n    }\n    if (!control) {\n      const controlOptions = {\n        updateOn: field.modelOptions.updateOn\n      };\n      if (field.fieldGroup) {\n        control = new FormGroup({}, controlOptions);\n      } else {\n        const value = hasKey(field) ? getFieldValue(field) : field.defaultValue;\n        control = new FormControl({\n          value,\n          disabled: !!field.props.disabled\n        }, {\n          ...controlOptions,\n          initialValueIsDefault: true\n        });\n      }\n    }\n    registerControl(field, control);\n  }\n  setValidators(field, disabled = false) {\n    if (disabled === false && hasKey(field) && field.props?.disabled) {\n      disabled = true;\n    }\n    let markForCheck = false;\n    field.fieldGroup?.forEach(f => f && this.setValidators(f, disabled) && (markForCheck = true));\n    if (hasKey(field) || !field.parent || !hasKey(field) && !field.fieldGroup) {\n      const {\n        formControl: c\n      } = field;\n      if (c) {\n        if (hasKey(field) && c instanceof FormControl) {\n          if (disabled && c.enabled) {\n            c.disable({\n              emitEvent: false,\n              onlySelf: true\n            });\n            markForCheck = true;\n          }\n          if (!disabled && c.disabled) {\n            c.enable({\n              emitEvent: false,\n              onlySelf: true\n            });\n            markForCheck = true;\n          }\n        }\n        if (null === c.validator || null === c.asyncValidator) {\n          c.setValidators(() => {\n            const v = Validators.compose(this.mergeValidators(field, '_validators'));\n            return v ? v(c) : null;\n          });\n          c.setAsyncValidators(() => {\n            const v = Validators.composeAsync(this.mergeValidators(field, '_asyncValidators'));\n            return v ? v(c) : of(null);\n          });\n          markForCheck = true;\n        }\n        if (markForCheck) {\n          updateValidity(c, true);\n          // update validity of `FormGroup` instance created by field with nested key.\n          let parent = c.parent;\n          for (let i = 1; i < getKeyPath(field).length; i++) {\n            if (parent) {\n              updateValidity(parent, true);\n              parent = parent.parent;\n            }\n          }\n        }\n      }\n    }\n    return markForCheck;\n  }\n  mergeValidators(field, type) {\n    const validators = [];\n    const c = field.formControl;\n    if (c?._fields?.length > 1) {\n      c._fields.filter(f => !f._hide).forEach(f => validators.push(...f[type]));\n    } else if (field[type]) {\n      validators.push(...field[type]);\n    }\n    if (field.fieldGroup) {\n      field.fieldGroup.filter(f => f?.fieldGroup && !hasKey(f)).forEach(f => validators.push(...this.mergeValidators(f, type)));\n    }\n    return validators;\n  }\n}\nclass CoreExtension {\n  constructor(config) {\n    this.config = config;\n    this.formId = 0;\n  }\n  prePopulate(field) {\n    const root = field.parent;\n    this.initRootOptions(field);\n    this.initFieldProps(field);\n    if (root) {\n      Object.defineProperty(field, 'options', {\n        get: () => root.options,\n        configurable: true\n      });\n      Object.defineProperty(field, 'model', {\n        get: () => hasKey(field) && field.fieldGroup ? getFieldValue(field) : root.model,\n        configurable: true\n      });\n    }\n    Object.defineProperty(field, 'get', {\n      value: key => getField(field, key),\n      configurable: true\n    });\n    this.getFieldComponentInstance(field).prePopulate?.(field);\n  }\n  onPopulate(field) {\n    this.initFieldOptions(field);\n    this.getFieldComponentInstance(field).onPopulate?.(field);\n    if (field.fieldGroup) {\n      field.fieldGroup.forEach((f, index) => {\n        if (f) {\n          Object.defineProperty(f, 'parent', {\n            get: () => field,\n            configurable: true\n          });\n          Object.defineProperty(f, 'index', {\n            get: () => index,\n            configurable: true\n          });\n        }\n        this.formId++;\n      });\n    }\n  }\n  postPopulate(field) {\n    this.getFieldComponentInstance(field).postPopulate?.(field);\n  }\n  initFieldProps(field) {\n    field.props ?? (field.props = field.templateOptions);\n    Object.defineProperty(field, 'templateOptions', {\n      get: () => field.props,\n      set: props => field.props = props,\n      configurable: true\n    });\n  }\n  initRootOptions(field) {\n    if (field.parent) {\n      return;\n    }\n    const options = field.options;\n    field.options.formState = field.options.formState || {};\n    if (!options.showError) {\n      options.showError = this.config.extras.showError;\n    }\n    if (!options.fieldChanges) {\n      defineHiddenProp(options, 'fieldChanges', new Subject());\n    }\n    if (!options._hiddenFieldsForCheck) {\n      options._hiddenFieldsForCheck = [];\n    }\n    options._markForCheck = f => {\n      console.warn(`Formly: 'options._markForCheck' is deprecated since v6.0, use 'options.detectChanges' instead.`);\n      options.detectChanges(f);\n    };\n    options.detectChanges = f => {\n      if (f._componentRefs) {\n        f.options.checkExpressions(f);\n        markFieldForCheck(f);\n      }\n      f.fieldGroup?.forEach(f => f && options.detectChanges(f));\n    };\n    options.resetModel = model => {\n      model = clone(model ?? options._initialModel);\n      if (field.model) {\n        Object.keys(field.model).forEach(k => delete field.model[k]);\n        Object.assign(field.model, model || {});\n      }\n      options.build(field);\n      field.form.reset(field.model);\n      if (options.parentForm && options.parentForm.control === field.formControl) {\n        options.parentForm.submitted = false;\n      }\n    };\n    options.updateInitialValue = model => options._initialModel = clone(model ?? field.model);\n    field.options.updateInitialValue();\n  }\n  initFieldOptions(field) {\n    reverseDeepMerge(field, {\n      id: getFieldId(`formly_${this.formId}`, field, field.index),\n      hooks: {},\n      modelOptions: {},\n      validation: {\n        messages: {}\n      },\n      props: !field.type || !hasKey(field) ? {} : {\n        label: '',\n        placeholder: '',\n        disabled: false\n      }\n    });\n    if (this.config.extras.resetFieldOnHide && field.resetOnHide !== false) {\n      field.resetOnHide = true;\n    }\n    if (field.type !== 'formly-template' && (field.template || field.expressions?.template || field.expressionProperties?.template)) {\n      field.type = 'formly-template';\n    }\n    if (!field.type && field.fieldGroup) {\n      field.type = 'formly-group';\n    }\n    if (field.type) {\n      this.config.getMergedField(field);\n    }\n    if (hasKey(field) && !isUndefined(field.defaultValue) && isUndefined(getFieldValue(field))) {\n      const isHidden = f => f.hide || f.expressions?.hide || f.hideExpression;\n      let setDefaultValue = !field.resetOnHide || !isHidden(field);\n      if (!isHidden(field) && field.resetOnHide) {\n        let parent = field.parent;\n        while (parent && !isHidden(parent)) {\n          parent = parent.parent;\n        }\n        setDefaultValue = !parent || !isHidden(parent);\n      }\n      if (setDefaultValue) {\n        assignFieldValue(field, field.defaultValue);\n      }\n    }\n    field.wrappers = field.wrappers || [];\n  }\n  getFieldComponentInstance(field) {\n    const componentRefInstance = () => {\n      let componentRef = this.config.resolveFieldTypeRef(field);\n      const fieldComponentRef = field._componentRefs?.slice(-1)[0];\n      if (fieldComponentRef instanceof ComponentRef && fieldComponentRef?.componentType === componentRef?.componentType) {\n        componentRef = fieldComponentRef;\n      }\n      return componentRef?.instance;\n    };\n    if (!field._proxyInstance) {\n      defineHiddenProp(field, '_proxyInstance', new Proxy({}, {\n        get: (_, prop) => componentRefInstance()?.[prop],\n        set: (_, prop, value) => componentRefInstance()[prop] = value\n      }));\n    }\n    return field._proxyInstance;\n  }\n}\nfunction defaultFormlyConfig(config) {\n  return {\n    types: [{\n      name: 'formly-group',\n      component: FormlyGroup\n    }, {\n      name: 'formly-template',\n      component: FormlyTemplateType\n    }],\n    extensions: [{\n      name: 'core',\n      extension: new CoreExtension(config),\n      priority: -250\n    }, {\n      name: 'field-validation',\n      extension: new FieldValidationExtension(config),\n      priority: -200\n    }, {\n      name: 'field-form',\n      extension: new FieldFormExtension(),\n      priority: -150\n    }, {\n      name: 'field-expression',\n      extension: new FieldExpressionExtension(),\n      priority: -100\n    }]\n  };\n}\nclass FormlyModule {\n  constructor(configService, configs = []) {\n    if (!configs) {\n      return;\n    }\n    configs.forEach(config => configService.addConfig(config));\n  }\n  static forRoot(config = {}) {\n    return {\n      ngModule: FormlyModule,\n      providers: [{\n        provide: FORMLY_CONFIG,\n        multi: true,\n        useFactory: defaultFormlyConfig,\n        deps: [FormlyConfig]\n      }, {\n        provide: FORMLY_CONFIG,\n        useValue: config,\n        multi: true\n      }, FormlyConfig, FormlyFormBuilder]\n    };\n  }\n  static forChild(config = {}) {\n    return {\n      ngModule: FormlyModule,\n      providers: [{\n        provide: FORMLY_CONFIG,\n        multi: true,\n        useFactory: defaultFormlyConfig,\n        deps: [FormlyConfig]\n      }, {\n        provide: FORMLY_CONFIG,\n        useValue: config,\n        multi: true\n      }, FormlyFormBuilder]\n    };\n  }\n}\nFormlyModule.ɵfac = function FormlyModule_Factory(t) {\n  return new (t || FormlyModule)(i0.ɵɵinject(FormlyConfig), i0.ɵɵinject(FORMLY_CONFIG, 8));\n};\nFormlyModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: FormlyModule\n});\nFormlyModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormlyModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [FormlyTemplate, FormlyForm, FormlyField, FormlyAttributes, FormlyGroup, FormlyValidationMessage, FormlyTemplateType],\n      exports: [FormlyTemplate, FormlyForm, FormlyField, FormlyAttributes, FormlyGroup, FormlyValidationMessage],\n      imports: [CommonModule]\n    }]\n  }], function () {\n    return [{\n      type: FormlyConfig\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FORMLY_CONFIG]\n      }]\n    }];\n  }, null);\n})();\n\n/*\n * Public API Surface of core\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FORMLY_CONFIG, FieldArrayType, FieldType, FieldWrapper, FormlyConfig, FormlyField, FormlyForm, FormlyFormBuilder, FormlyModule, FormlyAttributes as ɵFormlyAttributes, FormlyGroup as ɵFormlyGroup, FormlyTemplate as ɵFormlyTemplate, FormlyValidationMessage as ɵFormlyValidationMessage, clone as ɵclone, defineHiddenProp as ɵdefineHiddenProp, getFieldValue as ɵgetFieldValue, hasKey as ɵhasKey, observe as ɵobserve, reverseDeepMerge as ɵreverseDeepMerge };", "map": {"version": 3, "names": ["i0", "Type", "TemplateRef", "ComponentRef", "ChangeDetectorRef", "InjectionToken", "Injectable", "Optional", "Directive", "Input", "ViewContainerRef", "Component", "ViewChild", "EventEmitter", "ChangeDetectionStrategy", "Output", "ContentChildren", "Inject", "NgModule", "i2", "AbstractControl", "FormGroup", "FormArray", "FormControl", "Validators", "isObservable", "merge", "of", "Observable", "Subject", "distinctUntilChanged", "startWith", "debounceTime", "filter", "switchMap", "take", "tap", "map", "i2$1", "DOCUMENT", "CommonModule", "i1", "_c0", "FormlyField_ng_template_0_Template", "rf", "ctx", "FormlyGroup_formly_field_0_Template", "ɵɵelement", "f_r1", "$implicit", "ɵɵproperty", "_c1", "_c2", "disableTreeValidityCall", "form", "callback", "_updateTreeValidity", "bind", "getFieldId", "formId", "field", "index", "id", "type", "template", "prototype", "constructor", "name", "key", "join", "<PERSON><PERSON><PERSON>", "isNil", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_keyPath", "path", "indexOf", "replace", "split", "Array", "isArray", "slice", "defineHiddenProp", "FORMLY_VALIDATORS", "assignFieldValue", "value", "paths", "length", "root", "parent", "undefined", "resetOnHide", "k", "pop", "m", "reduce", "model", "assignModelValue", "i", "isObject", "test", "clone", "getFieldValue", "reverseDeepMerge", "dest", "args", "for<PERSON>ach", "src", "srcArg", "isBlankString", "objAndSameType", "isUndefined", "isFunction", "obj1", "obj2", "Object", "getPrototypeOf", "x", "isPromise", "obj", "then", "changingThisBreaksApplicationSecurity", "Set", "Map", "_isAMomentObject", "Date", "getTime", "v", "proto", "c", "create", "setPrototypeOf", "keys", "newVal", "prop", "propDesc", "getOwnPropertyDescriptor", "get", "defineProperty", "defaultValue", "enumerable", "writable", "configurable", "observe<PERSON><PERSON>", "source", "setFn", "observers", "unsubscribe", "observer", "observe", "firstChange", "currentValue", "push", "o", "_observers", "target", "onChange", "state", "set", "previousValue", "changeFn", "setValue", "getField", "f", "fieldGroup", "len", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_componentRefs", "ref", "changeDetectorRef", "injector", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FORMLY_CONFIG", "FormlyConfig", "types", "validators", "wrappers", "messages", "extras", "checkExpressionOn", "lazy<PERSON>ender", "resetFieldOnHide", "renderFormlyFieldElement", "showError", "formControl", "invalid", "touched", "options", "parentForm", "submitted", "validation", "show", "extensions", "presets", "extensionsByPriority", "addConfig", "config", "setType", "validator", "setValidator", "wrapper", "setWrapper", "validationMessages", "addValidatorMessage", "message", "setSortedExtensions", "acc", "curr", "option", "hasOwnProperty", "getType", "throwIfNotFound", "component", "Error", "mergeExtendedType", "getMergedField", "defaultOptions", "extendDefaults", "extends", "optionsTypes", "componentRef", "resolveFieldTypeRef", "instance", "_componentRef", "_viewContainerRef", "_injector", "createComponent", "destroy", "e", "console", "error", "setTypeWrapper", "getWrapper", "getValidator", "ngDevMode", "deprecated", "minlength", "maxlength", "warn", "getValidatorMessage", "extensionOptions", "extensionOption", "priority", "extension", "Number", "sort", "a", "b", "prio", "extendedType", "ɵfac", "FormlyConfig_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "FormlyFormBuilder", "viewContainerRef", "buildForm", "build", "core", "_setOptions", "_build", "checkExpressions", "detectChanges", "values", "prePopulate", "onPopulate", "postPopulate", "_buildForm", "FormlyFormBuilder_Factory", "ɵɵinject", "Injector", "FormGroupDirective", "decorators", "unregisterControl", "emitEvent", "control", "fieldIndex", "_fields", "splice", "opts", "controls", "findIndex", "removeAt", "removeControl", "setParent", "findControl", "shareFormControl", "registerControl", "setValidators", "setAsyncValidators", "props", "disabled", "disabledObserver", "disable", "enable", "registerOnDisabledChange", "patchValue", "setControl", "_hide", "updateValidity", "onlySelf", "status", "updateValueAndValidity", "statusChanges", "emit", "valueChanges", "clearControl", "FormlyTemplate", "ngOnChanges", "FormlyTemplate_Factory", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "selector", "FormlyFieldTemplates", "FormlyFieldTemplates_Factory", "FormlyField", "renderer", "_elementRef", "hostContainerRef", "hostObservers", "componentRefs", "hooksObservers", "detectFieldBuild", "valueChangesUnsubscribe", "containerRef", "elementRef", "location", "ngAfterContentInit", "trigger<PERSON>ook", "ngAfterViewInit", "ngDoCheck", "render", "ngOnInit", "changes", "ngOnDestroy", "resetRefs", "hostObserver", "renderField", "clear", "wps", "attachComponentRef", "_l<PERSON><PERSON><PERSON>", "viewRef", "detach", "destroyed", "insert", "inlineType", "templates", "find", "createEmbeddedView", "fieldChanges", "hooks", "r", "sub", "subscribe", "assign", "setStyle", "nativeElement", "className", "removeAttribute", "setAttribute", "hide", "subscribes", "_expressions", "expressionObserver", "subscription", "value$", "fieldObserver", "pipe", "y", "updateOn", "debounce", "modelOptions", "default", "parsers", "parserFn", "next", "FormlyField_Factory", "Renderer2", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "FormlyField_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "decls", "vars", "consts", "FormlyField_Template", "ɵɵtemplate", "ɵɵtemplateRefExtractor", "styles", "read", "static", "FormlyForm", "builder", "ngZone", "fieldTemplates", "modelChange", "_modelChangeValue", "immutable", "set<PERSON><PERSON>", "fields", "checkExpressionChange", "onStable", "asObservable", "runGuarded", "p", "FormlyForm_Factory", "NgZone", "contentQueries", "FormlyForm_ContentQueries", "dirIndex", "ɵɵcontentQuery", "outputs", "ɵɵProvidersFeature", "FormlyForm_Template", "dependencies", "encapsulation", "changeDetection", "providers", "OnPush", "FormlyAttributes", "_document", "uiAttributesCache", "uiEvents", "listeners", "events", "eventName", "$event", "onFocus", "onBlur", "document", "fieldAttrElements", "listener", "listen", "attributes", "attr", "detachElementRef", "attachElementRef", "focusObserver", "toggleF<PERSON><PERSON>", "uiAttributes", "element", "hasAttribute", "toLowerCase", "focus", "isFocused", "activeElement", "some", "contains", "Promise", "resolve", "blur", "onHostChange", "Event", "change", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FormlyAttributes_Factory", "hostBindings", "FormlyAttributes_HostBindings", "ɵɵlistener", "FormlyAttributes_change_HostBindingHandler", "host", "FieldType", "to", "formState", "FieldType_Factory", "FormlyGroup", "ɵFormlyGroup_BaseFactory", "FormlyGroup_Factory", "ɵɵgetInheritedFactory", "hostVars", "FormlyGroup_HostBindings", "ɵɵclassMap", "fieldGroupClassName", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "FormlyGroup_Template", "ɵɵprojectionDef", "ɵɵprojection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FormlyValidationMessage", "EXPR_VALIDATORS", "errorMessage$", "property", "errorMessage", "fieldForm", "errors", "errorPath", "asyncValidators", "FormlyValidationMessage_Factory", "FormlyValidationMessage_Template", "ɵɵtext", "ɵɵpipe", "ɵɵtextInterpolate", "ɵɵpipeBind1", "AsyncPipe", "FieldArrayType", "fieldArray", "add", "initialModel", "remove", "ɵFieldArrayType_BaseFactory", "FieldArrayType_Factory", "FieldWrapper", "_staticContent", "content", "fieldComponent", "ɵFieldWrapper_BaseFactory", "FieldWrapper_Factory", "FieldWrapper_Query", "FormlyTemplateType", "sanitizer", "innerHtml", "safeHtml", "bypassSecurityTrustHtml", "FormlyTemplateType_Factory", "Dom<PERSON><PERSON><PERSON>zer", "FormlyTemplateType_Template", "ɵɵsanitizeHtml", "evalStringExpression", "expression", "argNames", "Function", "evalExpression", "thisArg", "argVal", "apply", "FieldExpressionExtension", "hidden", "_hiddenFields<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideExpression", "expr", "parseExpressions", "evalExpr", "expressions", "expressionProperties", "checkLocked", "ignoreCache", "fieldChanged", "changeHideState", "_checkField", "parentExpression", "includes", "rootValue", "expr<PERSON><PERSON>ue", "JSON", "stringify", "changeDisabledState", "every", "reset", "_evalExpressionPath", "lastIndex", "emitExpressionChanges", "arrayPath", "match", "FieldValidationExtension", "initFieldValidation", "getPredefinedFieldValidation", "validatorName", "wrapNgValidatorFn", "VALIDATORS", "opt", "compose", "required", "pattern", "minLengthResult", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON>engthResult", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "min", "max", "validatorOption", "handleAsyncResult", "handleResult", "ctrl", "_childrenErrors", "childCtrl", "_errorPath", "setErrors", "_toDelete", "childErrors", "FieldFormExtension", "addFormControl", "controlOptions", "initialValueIsDefault", "enabled", "asyncValidator", "mergeValidators", "composeAsync", "CoreExtension", "initRootOptions", "initFieldProps", "getFieldComponentInstance", "initFieldOptions", "templateOptions", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetModel", "_initialModel", "updateInitialValue", "label", "placeholder", "isHidden", "setDefaultValue", "componentRefInstance", "fieldComponentRef", "componentType", "_proxyInstance", "Proxy", "_", "defaultFormlyConfig", "FormlyModule", "configService", "configs", "forRoot", "ngModule", "provide", "multi", "useFactory", "deps", "useValue", "<PERSON><PERSON><PERSON><PERSON>", "FormlyModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "ɵFormlyAttributes", "ɵFormlyGroup", "ɵFormlyTemplate", "ɵFormlyValidationMessage", "ɵclone", "ɵdefineHiddenProp", "ɵgetFieldValue", "ɵhasKey", "ɵobserve", "ɵreverseDeepMerge"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@ngx-formly/core/fesm2020/ngx-formly-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Type, TemplateRef, ComponentRef, ChangeDetectorRef, InjectionToken, Injectable, Optional, Directive, Input, ViewContainerRef, Component, ViewChild, EventEmitter, ChangeDetectionStrategy, Output, ContentChildren, Inject, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { AbstractControl, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';\nimport { isObservable, merge, of, Observable, Subject } from 'rxjs';\nimport { distinctUntilChanged, startWith, debounceTime, filter, switchMap, take, tap, map } from 'rxjs/operators';\nimport * as i2$1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\n\nfunction disableTreeValidityCall(form, callback) {\n    const _updateTreeValidity = form._updateTreeValidity.bind(form);\n    form._updateTreeValidity = () => { };\n    callback();\n    form._updateTreeValidity = _updateTreeValidity;\n}\nfunction getFieldId(formId, field, index) {\n    if (field.id) {\n        return field.id;\n    }\n    let type = field.type;\n    if (!type && field.template) {\n        type = 'template';\n    }\n    if (type instanceof Type) {\n        type = type.prototype.constructor.name;\n    }\n    return [formId, type, field.key, index].join('_');\n}\nfunction hasKey(field) {\n    return !isNil(field.key) && field.key !== '';\n}\nfunction getKeyPath(field) {\n    if (!hasKey(field)) {\n        return [];\n    }\n    /* We store the keyPath in the field for performance reasons. This function will be called frequently. */\n    if (field._keyPath?.key !== field.key) {\n        let path = [];\n        if (typeof field.key === 'string') {\n            const key = field.key.indexOf('[') === -1 ? field.key : field.key.replace(/\\[(\\w+)\\]/g, '.$1');\n            path = key.indexOf('.') !== -1 ? key.split('.') : [key];\n        }\n        else if (Array.isArray(field.key)) {\n            path = field.key.slice(0);\n        }\n        else {\n            path = [`${field.key}`];\n        }\n        defineHiddenProp(field, '_keyPath', { key: field.key, path });\n    }\n    return field._keyPath.path.slice(0);\n}\nconst FORMLY_VALIDATORS = ['required', 'pattern', 'minLength', 'maxLength', 'min', 'max'];\nfunction assignFieldValue(field, value) {\n    let paths = getKeyPath(field);\n    if (paths.length === 0) {\n        return;\n    }\n    let root = field;\n    while (root.parent) {\n        root = root.parent;\n        paths = [...getKeyPath(root), ...paths];\n    }\n    if (value === undefined && field.resetOnHide) {\n        const k = paths.pop();\n        const m = paths.reduce((model, path) => model[path] || {}, root.model);\n        delete m[k];\n        return;\n    }\n    assignModelValue(root.model, paths, value);\n}\nfunction assignModelValue(model, paths, value) {\n    for (let i = 0; i < paths.length - 1; i++) {\n        const path = paths[i];\n        if (!model[path] || !isObject(model[path])) {\n            model[path] = /^\\d+$/.test(paths[i + 1]) ? [] : {};\n        }\n        model = model[path];\n    }\n    model[paths[paths.length - 1]] = clone(value);\n}\nfunction getFieldValue(field) {\n    let model = field.parent ? field.parent.model : field.model;\n    for (const path of getKeyPath(field)) {\n        if (!model) {\n            return model;\n        }\n        model = model[path];\n    }\n    return model;\n}\nfunction reverseDeepMerge(dest, ...args) {\n    args.forEach((src) => {\n        for (const srcArg in src) {\n            if (isNil(dest[srcArg]) || isBlankString(dest[srcArg])) {\n                dest[srcArg] = clone(src[srcArg]);\n            }\n            else if (objAndSameType(dest[srcArg], src[srcArg])) {\n                reverseDeepMerge(dest[srcArg], src[srcArg]);\n            }\n        }\n    });\n    return dest;\n}\n// check a value is null or undefined\nfunction isNil(value) {\n    return value == null;\n}\nfunction isUndefined(value) {\n    return value === undefined;\n}\nfunction isBlankString(value) {\n    return value === '';\n}\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\nfunction objAndSameType(obj1, obj2) {\n    return (isObject(obj1) &&\n        isObject(obj2) &&\n        Object.getPrototypeOf(obj1) === Object.getPrototypeOf(obj2) &&\n        !(Array.isArray(obj1) || Array.isArray(obj2)));\n}\nfunction isObject(x) {\n    return x != null && typeof x === 'object';\n}\nfunction isPromise(obj) {\n    return !!obj && typeof obj.then === 'function';\n}\nfunction clone(value) {\n    if (!isObject(value) ||\n        isObservable(value) ||\n        value instanceof TemplateRef ||\n        /* instanceof SafeHtmlImpl */ value.changingThisBreaksApplicationSecurity ||\n        ['RegExp', 'FileList', 'File', 'Blob'].indexOf(value.constructor.name) !== -1) {\n        return value;\n    }\n    if (value instanceof Set) {\n        return new Set(value);\n    }\n    if (value instanceof Map) {\n        return new Map(value);\n    }\n    // https://github.com/moment/moment/blob/master/moment.js#L252\n    if (value._isAMomentObject && isFunction(value.clone)) {\n        return value.clone();\n    }\n    if (value instanceof AbstractControl) {\n        return null;\n    }\n    if (value instanceof Date) {\n        return new Date(value.getTime());\n    }\n    if (Array.isArray(value)) {\n        return value.slice(0).map((v) => clone(v));\n    }\n    // best way to clone a js object maybe\n    // https://stackoverflow.com/questions/41474986/how-to-clone-a-javascript-es6-class-instance\n    const proto = Object.getPrototypeOf(value);\n    let c = Object.create(proto);\n    c = Object.setPrototypeOf(c, proto);\n    // need to make a deep copy so we dont use Object.assign\n    // also Object.assign wont copy property descriptor exactly\n    return Object.keys(value).reduce((newVal, prop) => {\n        const propDesc = Object.getOwnPropertyDescriptor(value, prop);\n        if (propDesc.get) {\n            Object.defineProperty(newVal, prop, propDesc);\n        }\n        else {\n            newVal[prop] = clone(value[prop]);\n        }\n        return newVal;\n    }, c);\n}\nfunction defineHiddenProp(field, prop, defaultValue) {\n    Object.defineProperty(field, prop, { enumerable: false, writable: true, configurable: true });\n    field[prop] = defaultValue;\n}\nfunction observeDeep(source, paths, setFn) {\n    let observers = [];\n    const unsubscribe = () => {\n        observers.forEach((observer) => observer());\n        observers = [];\n    };\n    const observer = observe(source, paths, ({ firstChange, currentValue }) => {\n        !firstChange && setFn();\n        unsubscribe();\n        if (isObject(currentValue) && currentValue.constructor.name === 'Object') {\n            Object.keys(currentValue).forEach((prop) => {\n                observers.push(observeDeep(source, [...paths, prop], setFn));\n            });\n        }\n    });\n    return () => {\n        observer.unsubscribe();\n        unsubscribe();\n    };\n}\nfunction observe(o, paths, setFn) {\n    if (!o._observers) {\n        defineHiddenProp(o, '_observers', {});\n    }\n    let target = o;\n    for (let i = 0; i < paths.length - 1; i++) {\n        if (!target[paths[i]] || !isObject(target[paths[i]])) {\n            target[paths[i]] = /^\\d+$/.test(paths[i + 1]) ? [] : {};\n        }\n        target = target[paths[i]];\n    }\n    const key = paths[paths.length - 1];\n    const prop = paths.join('.');\n    if (!o._observers[prop]) {\n        o._observers[prop] = { value: target[key], onChange: [] };\n    }\n    const state = o._observers[prop];\n    if (target[key] !== state.value) {\n        state.value = target[key];\n    }\n    if (state.onChange.indexOf(setFn) === -1) {\n        state.onChange.push(setFn);\n        setFn({ currentValue: state.value, firstChange: true });\n        if (state.onChange.length >= 1 && isObject(target)) {\n            const { enumerable } = Object.getOwnPropertyDescriptor(target, key) || { enumerable: true };\n            Object.defineProperty(target, key, {\n                enumerable,\n                configurable: true,\n                get: () => state.value,\n                set: (currentValue) => {\n                    if (currentValue !== state.value) {\n                        const previousValue = state.value;\n                        state.value = currentValue;\n                        state.onChange.forEach((changeFn) => changeFn({ previousValue, currentValue, firstChange: false }));\n                    }\n                },\n            });\n        }\n    }\n    return {\n        setValue(value) {\n            state.value = value;\n        },\n        unsubscribe() {\n            state.onChange = state.onChange.filter((changeFn) => changeFn !== setFn);\n            if (state.onChange.length === 0) {\n                delete o._observers[prop];\n            }\n        },\n    };\n}\nfunction getField(f, key) {\n    key = (Array.isArray(key) ? key.join('.') : key);\n    if (!f.fieldGroup) {\n        return undefined;\n    }\n    for (let i = 0, len = f.fieldGroup.length; i < len; i++) {\n        const c = f.fieldGroup[i];\n        const k = (Array.isArray(c.key) ? c.key.join('.') : c.key);\n        if (k === key) {\n            return c;\n        }\n        if (c.fieldGroup && (isNil(k) || key.indexOf(`${k}.`) === 0)) {\n            const field = getField(c, isNil(k) ? key : key.slice(k.length + 1));\n            if (field) {\n                return field;\n            }\n        }\n    }\n    return undefined;\n}\nfunction markFieldForCheck(field) {\n    field._componentRefs?.forEach((ref) => {\n        // NOTE: we cannot use ref.changeDetectorRef, see https://github.com/ngx-formly/ngx-formly/issues/2191\n        if (ref instanceof ComponentRef) {\n            const changeDetectorRef = ref.injector.get(ChangeDetectorRef);\n            changeDetectorRef.markForCheck();\n        }\n        else {\n            ref.markForCheck();\n        }\n    });\n}\n\n/**\n * An InjectionToken for registering additional formly config options (types, wrappers ...).\n */\nconst FORMLY_CONFIG = new InjectionToken('FORMLY_CONFIG');\n/**\n * Maintains list of formly config options. This can be used to register new field type.\n */\nclass FormlyConfig {\n    constructor() {\n        this.types = {};\n        this.validators = {};\n        this.wrappers = {};\n        this.messages = {};\n        this.extras = {\n            checkExpressionOn: 'modelChange',\n            lazyRender: true,\n            resetFieldOnHide: true,\n            renderFormlyFieldElement: true,\n            showError(field) {\n                return (field.formControl?.invalid &&\n                    (field.formControl?.touched || field.options.parentForm?.submitted || !!field.field.validation?.show));\n            },\n        };\n        this.extensions = {};\n        this.presets = {};\n        this.extensionsByPriority = {};\n    }\n    addConfig(config) {\n        if (config.types) {\n            config.types.forEach((type) => this.setType(type));\n        }\n        if (config.validators) {\n            config.validators.forEach((validator) => this.setValidator(validator));\n        }\n        if (config.wrappers) {\n            config.wrappers.forEach((wrapper) => this.setWrapper(wrapper));\n        }\n        if (config.validationMessages) {\n            config.validationMessages.forEach((validation) => this.addValidatorMessage(validation.name, validation.message));\n        }\n        if (config.extensions) {\n            this.setSortedExtensions(config.extensions);\n        }\n        if (config.extras) {\n            this.extras = { ...this.extras, ...config.extras };\n        }\n        if (config.presets) {\n            this.presets = {\n                ...this.presets,\n                ...config.presets.reduce((acc, curr) => ({ ...acc, [curr.name]: curr.config }), {}),\n            };\n        }\n    }\n    /**\n     * Allows you to specify a custom type which you can use in your field configuration.\n     * You can pass an object of options, or an array of objects of options.\n     */\n    setType(options) {\n        if (Array.isArray(options)) {\n            options.forEach((option) => this.setType(option));\n        }\n        else {\n            if (!this.types[options.name]) {\n                this.types[options.name] = { name: options.name };\n            }\n            ['component', 'extends', 'defaultOptions', 'wrappers'].forEach((prop) => {\n                if (options.hasOwnProperty(prop)) {\n                    this.types[options.name][prop] = options[prop];\n                }\n            });\n        }\n    }\n    getType(name, throwIfNotFound = false) {\n        if (name instanceof Type) {\n            return { component: name, name: name.prototype.constructor.name };\n        }\n        if (!this.types[name]) {\n            if (throwIfNotFound) {\n                throw new Error(`[Formly Error] The type \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n            }\n            return null;\n        }\n        this.mergeExtendedType(name);\n        return this.types[name];\n    }\n    /** @ignore */\n    getMergedField(field = {}) {\n        const type = this.getType(field.type);\n        if (!type) {\n            return;\n        }\n        if (type.defaultOptions) {\n            reverseDeepMerge(field, type.defaultOptions);\n        }\n        const extendDefaults = type.extends && this.getType(type.extends).defaultOptions;\n        if (extendDefaults) {\n            reverseDeepMerge(field, extendDefaults);\n        }\n        if (field?.optionsTypes) {\n            field.optionsTypes.forEach((option) => {\n                const defaultOptions = this.getType(option).defaultOptions;\n                if (defaultOptions) {\n                    reverseDeepMerge(field, defaultOptions);\n                }\n            });\n        }\n        const componentRef = this.resolveFieldTypeRef(field);\n        if (componentRef?.instance?.defaultOptions) {\n            reverseDeepMerge(field, componentRef.instance.defaultOptions);\n        }\n        if (!field.wrappers && type.wrappers) {\n            field.wrappers = [...type.wrappers];\n        }\n    }\n    /** @ignore @internal */\n    resolveFieldTypeRef(field = {}) {\n        const type = this.getType(field.type);\n        if (!type) {\n            return null;\n        }\n        if (!type.component || type._componentRef) {\n            return type._componentRef;\n        }\n        const { _viewContainerRef, _injector } = field.options;\n        if (!_viewContainerRef || !_injector) {\n            return null;\n        }\n        const componentRef = _viewContainerRef.createComponent(type.component, { injector: _injector });\n        defineHiddenProp(type, '_componentRef', componentRef);\n        try {\n            componentRef.destroy();\n        }\n        catch (e) {\n            console.error(`An error occurred while destroying the Formly component type \"${field.type}\"`, e);\n        }\n        return type._componentRef;\n    }\n    setWrapper(options) {\n        this.wrappers[options.name] = options;\n        if (options.types) {\n            options.types.forEach((type) => {\n                this.setTypeWrapper(type, options.name);\n            });\n        }\n    }\n    getWrapper(name) {\n        if (name instanceof Type) {\n            return { component: name, name: name.prototype.constructor.name };\n        }\n        if (!this.wrappers[name]) {\n            throw new Error(`[Formly Error] The wrapper \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n        }\n        return this.wrappers[name];\n    }\n    /** @ignore */\n    setTypeWrapper(type, name) {\n        if (!this.types[type]) {\n            this.types[type] = {};\n        }\n        if (!this.types[type].wrappers) {\n            this.types[type].wrappers = [];\n        }\n        if (this.types[type].wrappers.indexOf(name) === -1) {\n            this.types[type].wrappers.push(name);\n        }\n    }\n    setValidator(options) {\n        this.validators[options.name] = options;\n    }\n    getValidator(name) {\n        if (!this.validators[name]) {\n            throw new Error(`[Formly Error] The validator \"${name}\" could not be found. Please make sure that is registered through the FormlyModule declaration.`);\n        }\n        return this.validators[name];\n    }\n    addValidatorMessage(name, message) {\n        this.messages[name] = message;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const deprecated = { minlength: 'minLength', maxlength: 'maxLength' };\n            if (deprecated[name]) {\n                console.warn(`Formly deprecation: passing validation messages key '${name}' is deprecated since v6.0, use '${deprecated[name]}' instead.`);\n                this.messages[deprecated[name]] = message;\n            }\n        }\n    }\n    getValidatorMessage(name) {\n        return this.messages[name];\n    }\n    setSortedExtensions(extensionOptions) {\n        // insert new extensions, grouped by priority\n        extensionOptions.forEach((extensionOption) => {\n            const priority = extensionOption.priority ?? 1;\n            this.extensionsByPriority[priority] = {\n                ...this.extensionsByPriority[priority],\n                [extensionOption.name]: extensionOption.extension,\n            };\n        });\n        // flatten extensions object with sorted keys\n        this.extensions = Object.keys(this.extensionsByPriority)\n            .map(Number)\n            .sort((a, b) => a - b)\n            .reduce((acc, prio) => ({\n            ...acc,\n            ...this.extensionsByPriority[prio],\n        }), {});\n    }\n    mergeExtendedType(name) {\n        if (!this.types[name].extends) {\n            return;\n        }\n        const extendedType = this.getType(this.types[name].extends);\n        if (!this.types[name].component) {\n            this.types[name].component = extendedType.component;\n        }\n        if (!this.types[name].wrappers) {\n            this.types[name].wrappers = extendedType.wrappers;\n        }\n    }\n}\nFormlyConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nFormlyConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyConfig, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyConfig, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass FormlyFormBuilder {\n    constructor(config, injector, viewContainerRef, parentForm) {\n        this.config = config;\n        this.injector = injector;\n        this.viewContainerRef = viewContainerRef;\n        this.parentForm = parentForm;\n    }\n    buildForm(form, fieldGroup = [], model, options) {\n        this.build({ fieldGroup, model, form, options });\n    }\n    build(field) {\n        if (!this.config.extensions.core) {\n            throw new Error('NgxFormly: missing `forRoot()` call. use `forRoot()` when registering the `FormlyModule`.');\n        }\n        if (!field.parent) {\n            this._setOptions(field);\n            disableTreeValidityCall(field.form, () => {\n                this._build(field);\n                const options = field.options;\n                options.checkExpressions?.(field, true);\n                options.detectChanges?.(field);\n            });\n        }\n        else {\n            this._build(field);\n        }\n    }\n    _build(field) {\n        if (!field) {\n            return;\n        }\n        const extensions = Object.values(this.config.extensions);\n        extensions.forEach((extension) => extension.prePopulate?.(field));\n        extensions.forEach((extension) => extension.onPopulate?.(field));\n        field.fieldGroup?.forEach((f) => this._build(f));\n        extensions.forEach((extension) => extension.postPopulate?.(field));\n    }\n    _setOptions(field) {\n        field.form = field.form || new FormGroup({});\n        field.model = field.model || {};\n        field.options = field.options || {};\n        const options = field.options;\n        if (!options._viewContainerRef) {\n            defineHiddenProp(options, '_viewContainerRef', this.viewContainerRef);\n        }\n        if (!options._injector) {\n            defineHiddenProp(options, '_injector', this.injector);\n        }\n        if (!options.build) {\n            options._buildForm = () => {\n                console.warn(`Formly: 'options._buildForm' is deprecated since v6.0, use 'options.build' instead.`);\n                this.build(field);\n            };\n            options.build = (f = field) => {\n                this.build(f);\n                return f;\n            };\n        }\n        if (!options.parentForm && this.parentForm) {\n            defineHiddenProp(options, 'parentForm', this.parentForm);\n            observe(options, ['parentForm', 'submitted'], ({ firstChange }) => {\n                if (!firstChange) {\n                    options.checkExpressions(field);\n                    options.detectChanges(field);\n                }\n            });\n        }\n    }\n}\nFormlyFormBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyFormBuilder, deps: [{ token: FormlyConfig }, { token: i0.Injector }, { token: i0.ViewContainerRef, optional: true }, { token: i2.FormGroupDirective, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nFormlyFormBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyFormBuilder, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyFormBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: FormlyConfig }, { type: i0.Injector }, { type: i0.ViewContainerRef, decorators: [{\n                    type: Optional\n                }] }, { type: i2.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }]; } });\n\nfunction unregisterControl(field, emitEvent = false) {\n    const control = field.formControl;\n    const fieldIndex = control._fields ? control._fields.indexOf(field) : -1;\n    if (fieldIndex !== -1) {\n        control._fields.splice(fieldIndex, 1);\n    }\n    const form = control.parent;\n    if (!form) {\n        return;\n    }\n    const opts = { emitEvent };\n    if (form instanceof FormArray) {\n        const key = form.controls.findIndex((c) => c === control);\n        if (key !== -1) {\n            form.removeAt(key, opts);\n        }\n    }\n    else if (form instanceof FormGroup) {\n        const paths = getKeyPath(field);\n        const key = paths[paths.length - 1];\n        if (form.get([key]) === control) {\n            form.removeControl(key, opts);\n        }\n    }\n    control.setParent(null);\n}\nfunction findControl(field) {\n    if (field.formControl) {\n        return field.formControl;\n    }\n    if (field.shareFormControl === false) {\n        return null;\n    }\n    return field.form?.get(getKeyPath(field));\n}\nfunction registerControl(field, control, emitEvent = false) {\n    control = control || field.formControl;\n    if (!control._fields) {\n        defineHiddenProp(control, '_fields', []);\n    }\n    if (control._fields.indexOf(field) === -1) {\n        control._fields.push(field);\n    }\n    if (!field.formControl && control) {\n        defineHiddenProp(field, 'formControl', control);\n        control.setValidators(null);\n        control.setAsyncValidators(null);\n        field.props.disabled = !!field.props.disabled;\n        const disabledObserver = observe(field, ['props', 'disabled'], ({ firstChange, currentValue }) => {\n            if (!firstChange) {\n                currentValue ? field.formControl.disable() : field.formControl.enable();\n            }\n        });\n        if (control instanceof FormControl) {\n            control.registerOnDisabledChange(disabledObserver.setValue);\n        }\n    }\n    if (!field.form || !hasKey(field)) {\n        return;\n    }\n    let form = field.form;\n    const paths = getKeyPath(field);\n    const value = getFieldValue(field);\n    if (!(isNil(control.value) && isNil(value)) && control.value !== value && control instanceof FormControl) {\n        control.patchValue(value);\n    }\n    for (let i = 0; i < paths.length - 1; i++) {\n        const path = paths[i];\n        if (!form.get([path])) {\n            form.setControl(path, new FormGroup({}), { emitEvent });\n        }\n        form = form.get([path]);\n    }\n    const key = paths[paths.length - 1];\n    if (!field._hide && form.get([key]) !== control) {\n        form.setControl(key, control, { emitEvent });\n    }\n}\nfunction updateValidity(c, onlySelf = false) {\n    const status = c.status;\n    const value = c.value;\n    c.updateValueAndValidity({ emitEvent: false, onlySelf });\n    if (status !== c.status) {\n        c.statusChanges.emit(c.status);\n    }\n    if (value !== c.value) {\n        c.valueChanges.emit(c.value);\n    }\n}\nfunction clearControl(form) {\n    delete form?._fields;\n    form.setValidators(null);\n    form.setAsyncValidators(null);\n    if (form instanceof FormGroup || form instanceof FormArray) {\n        Object.values(form.controls).forEach((c) => clearControl(c));\n    }\n}\n\nclass FormlyTemplate {\n    constructor(ref) {\n        this.ref = ref;\n    }\n    ngOnChanges() {\n        this.name = this.name || 'formly-group';\n    }\n}\nFormlyTemplate.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyTemplate, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nFormlyTemplate.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyTemplate, selector: \"[formlyTemplate]\", inputs: { name: [\"formlyTemplate\", \"name\"] }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyTemplate, decorators: [{\n            type: Directive,\n            args: [{ selector: '[formlyTemplate]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { name: [{\n                type: Input,\n                args: ['formlyTemplate']\n            }] } });\n// workarround for https://github.com/angular/angular/issues/43227#issuecomment-904173738\nclass FormlyFieldTemplates {\n}\nFormlyFieldTemplates.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyFieldTemplates, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nFormlyFieldTemplates.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyFieldTemplates });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyFieldTemplates, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The `<formly-field>` component is used to render the UI widget (layout + type) of a given `field`.\n */\nclass FormlyField {\n    constructor(config, renderer, _elementRef, hostContainerRef, form) {\n        this.config = config;\n        this.renderer = renderer;\n        this._elementRef = _elementRef;\n        this.hostContainerRef = hostContainerRef;\n        this.form = form;\n        this.hostObservers = [];\n        this.componentRefs = [];\n        this.hooksObservers = [];\n        this.detectFieldBuild = false;\n        this.valueChangesUnsubscribe = () => { };\n    }\n    get containerRef() {\n        return this.config.extras.renderFormlyFieldElement ? this.viewContainerRef : this.hostContainerRef;\n    }\n    get elementRef() {\n        if (this.config.extras.renderFormlyFieldElement) {\n            return this._elementRef;\n        }\n        if (this.componentRefs?.[0] instanceof ComponentRef) {\n            return this.componentRefs[0].location;\n        }\n        return null;\n    }\n    ngAfterContentInit() {\n        this.triggerHook('afterContentInit');\n    }\n    ngAfterViewInit() {\n        this.triggerHook('afterViewInit');\n    }\n    ngDoCheck() {\n        if (this.detectFieldBuild && this.field && this.field.options) {\n            this.render();\n        }\n    }\n    ngOnInit() {\n        this.triggerHook('onInit');\n    }\n    ngOnChanges(changes) {\n        this.triggerHook('onChanges', changes);\n    }\n    ngOnDestroy() {\n        this.resetRefs(this.field);\n        this.hostObservers.forEach((hostObserver) => hostObserver.unsubscribe());\n        this.hooksObservers.forEach((unsubscribe) => unsubscribe());\n        this.valueChangesUnsubscribe();\n        this.triggerHook('onDestroy');\n    }\n    renderField(containerRef, f, wrappers = []) {\n        if (this.containerRef === containerRef) {\n            this.resetRefs(this.field);\n            this.containerRef.clear();\n            wrappers = this.field?.wrappers;\n        }\n        if (wrappers?.length > 0) {\n            const [wrapper, ...wps] = wrappers;\n            const { component } = this.config.getWrapper(wrapper);\n            const ref = containerRef.createComponent(component);\n            this.attachComponentRef(ref, f);\n            observe(ref.instance, ['fieldComponent'], ({ currentValue, previousValue, firstChange }) => {\n                if (currentValue) {\n                    if (previousValue && previousValue._lContainer === currentValue._lContainer) {\n                        return;\n                    }\n                    const viewRef = previousValue ? previousValue.detach() : null;\n                    if (viewRef && !viewRef.destroyed) {\n                        currentValue.insert(viewRef);\n                    }\n                    else {\n                        this.renderField(currentValue, f, wps);\n                    }\n                    !firstChange && ref.changeDetectorRef.detectChanges();\n                }\n            });\n        }\n        else if (f?.type) {\n            const inlineType = this.form?.templates?.find((ref) => ref.name === f.type);\n            let ref;\n            if (inlineType) {\n                ref = containerRef.createEmbeddedView(inlineType.ref, { $implicit: f });\n            }\n            else {\n                const { component } = this.config.getType(f.type, true);\n                ref = containerRef.createComponent(component);\n            }\n            this.attachComponentRef(ref, f);\n        }\n    }\n    triggerHook(name, changes) {\n        if (name === 'onInit' || (name === 'onChanges' && changes.field && !changes.field.firstChange)) {\n            this.valueChangesUnsubscribe = this.fieldChanges(this.field);\n        }\n        if (this.field?.hooks?.[name]) {\n            if (!changes || changes.field) {\n                const r = this.field.hooks[name](this.field);\n                if (isObservable(r) && ['onInit', 'afterContentInit', 'afterViewInit'].indexOf(name) !== -1) {\n                    const sub = r.subscribe();\n                    this.hooksObservers.push(() => sub.unsubscribe());\n                }\n            }\n        }\n        if (name === 'onChanges' && changes.field) {\n            this.resetRefs(changes.field.previousValue);\n            this.render();\n        }\n    }\n    attachComponentRef(ref, field) {\n        this.componentRefs.push(ref);\n        field._componentRefs.push(ref);\n        if (ref instanceof ComponentRef) {\n            Object.assign(ref.instance, { field });\n        }\n    }\n    render() {\n        if (!this.field) {\n            return;\n        }\n        // require Formly build\n        if (!this.field.options) {\n            this.detectFieldBuild = true;\n            return;\n        }\n        this.detectFieldBuild = false;\n        this.hostObservers.forEach((hostObserver) => hostObserver.unsubscribe());\n        this.hostObservers = [\n            observe(this.field, ['hide'], ({ firstChange, currentValue }) => {\n                const containerRef = this.containerRef;\n                if (this.config.extras.lazyRender === false) {\n                    firstChange && this.renderField(containerRef, this.field);\n                    if (!firstChange || (firstChange && currentValue)) {\n                        this.elementRef &&\n                            this.renderer.setStyle(this.elementRef.nativeElement, 'display', currentValue ? 'none' : '');\n                    }\n                }\n                else {\n                    if (currentValue) {\n                        containerRef.clear();\n                        if (this.field.className) {\n                            this.renderer.removeAttribute(this.elementRef.nativeElement, 'class');\n                        }\n                    }\n                    else {\n                        this.renderField(containerRef, this.field);\n                        if (this.field.className) {\n                            this.renderer.setAttribute(this.elementRef.nativeElement, 'class', this.field.className);\n                        }\n                    }\n                }\n                !firstChange && this.field.options.detectChanges(this.field);\n            }),\n            observe(this.field, ['className'], ({ firstChange, currentValue }) => {\n                if ((!firstChange || (firstChange && currentValue)) &&\n                    (!this.config.extras.lazyRender || this.field.hide !== true)) {\n                    this.elementRef && this.renderer.setAttribute(this.elementRef.nativeElement, 'class', currentValue);\n                }\n            }),\n            ...['touched', 'pristine', 'status'].map((prop) => observe(this.field, ['formControl', prop], ({ firstChange }) => !firstChange && markFieldForCheck(this.field))),\n        ];\n    }\n    resetRefs(field) {\n        if (field) {\n            if (field._componentRefs) {\n                field._componentRefs = field._componentRefs.filter((ref) => this.componentRefs.indexOf(ref) === -1);\n            }\n            else {\n                defineHiddenProp(this.field, '_componentRefs', []);\n            }\n        }\n        this.componentRefs = [];\n    }\n    fieldChanges(field) {\n        this.valueChangesUnsubscribe();\n        if (!field) {\n            return () => { };\n        }\n        const subscribes = [observeDeep(field, ['props'], () => field.options.detectChanges(field))];\n        if (field.options) {\n            subscribes.push(observeDeep(field.options, ['formState'], () => field.options.detectChanges(field)));\n        }\n        for (const key of Object.keys(field._expressions || {})) {\n            const expressionObserver = observe(field, ['_expressions', key], ({ currentValue, previousValue }) => {\n                if (previousValue?.subscription) {\n                    previousValue.subscription.unsubscribe();\n                    previousValue.subscription = null;\n                }\n                if (isObservable(currentValue.value$)) {\n                    currentValue.subscription = currentValue.value$.subscribe();\n                }\n            });\n            subscribes.push(() => {\n                if (field._expressions[key]?.subscription) {\n                    field._expressions[key].subscription.unsubscribe();\n                }\n                expressionObserver.unsubscribe();\n            });\n        }\n        for (const path of [['template'], ['fieldGroupClassName'], ['validation', 'show']]) {\n            const fieldObserver = observe(field, path, ({ firstChange }) => !firstChange && field.options.detectChanges(field));\n            subscribes.push(() => fieldObserver.unsubscribe());\n        }\n        if (field.formControl && !field.fieldGroup) {\n            const control = field.formControl;\n            let valueChanges = control.valueChanges.pipe(distinctUntilChanged((x, y) => {\n                if (x !== y || Array.isArray(x) || isObject(x)) {\n                    return false;\n                }\n                return true;\n            }));\n            if (control.value !== getFieldValue(field)) {\n                valueChanges = valueChanges.pipe(startWith(control.value));\n            }\n            const { updateOn, debounce } = field.modelOptions;\n            if ((!updateOn || updateOn === 'change') && debounce?.default > 0) {\n                valueChanges = control.valueChanges.pipe(debounceTime(debounce.default));\n            }\n            const sub = valueChanges.subscribe((value) => {\n                // workaround for https://github.com/angular/angular/issues/13792\n                if (control._fields?.length > 1 && control instanceof FormControl) {\n                    control.patchValue(value, { emitEvent: false, onlySelf: true });\n                }\n                field.parsers?.forEach((parserFn) => (value = parserFn(value)));\n                if (value !== field.formControl.value) {\n                    field.formControl.setValue(value);\n                    return;\n                }\n                if (hasKey(field)) {\n                    assignFieldValue(field, value);\n                }\n                field.options.fieldChanges.next({ value, field, type: 'valueChanges' });\n            });\n            subscribes.push(() => sub.unsubscribe());\n        }\n        return () => subscribes.forEach((subscribe) => subscribe());\n    }\n}\nFormlyField.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyField, deps: [{ token: FormlyConfig }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: FormlyFieldTemplates, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nFormlyField.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyField, selector: \"formly-field\", inputs: { field: \"field\" }, viewQueries: [{ propertyName: \"viewContainerRef\", first: true, predicate: [\"container\"], descendants: true, read: ViewContainerRef, static: true }], usesOnChanges: true, ngImport: i0, template: '<ng-template #container></ng-template>', isInline: true, styles: [\":host:empty{display:none}\\n\"] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyField, decorators: [{\n            type: Component,\n            args: [{ selector: 'formly-field', template: '<ng-template #container></ng-template>', styles: [\":host:empty{display:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: FormlyConfig }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: FormlyFieldTemplates, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { field: [{\n                type: Input\n            }], viewContainerRef: [{\n                type: ViewChild,\n                args: ['container', { read: ViewContainerRef, static: true }]\n            }] } });\n\n/**\n * The `<form-form>` component is the main container of the form,\n * which takes care of managing the form state\n * and delegates the rendering of each field to `<formly-field>` component.\n */\nclass FormlyForm {\n    constructor(builder, config, ngZone, fieldTemplates) {\n        this.builder = builder;\n        this.config = config;\n        this.ngZone = ngZone;\n        this.fieldTemplates = fieldTemplates;\n        /** Event that is emitted when the model value is changed */\n        this.modelChange = new EventEmitter();\n        this.field = { type: 'formly-group' };\n        this._modelChangeValue = {};\n        this.valueChangesUnsubscribe = () => { };\n    }\n    /** The form instance which allow to track model value and validation status. */\n    set form(form) {\n        this.field.form = form;\n    }\n    get form() {\n        return this.field.form;\n    }\n    /** The model to be represented by the form. */\n    set model(model) {\n        if (this.config.extras.immutable && this._modelChangeValue === model) {\n            return;\n        }\n        this.setField({ model });\n    }\n    get model() {\n        return this.field.model;\n    }\n    /** The field configurations for building the form. */\n    set fields(fieldGroup) {\n        this.setField({ fieldGroup });\n    }\n    get fields() {\n        return this.field.fieldGroup;\n    }\n    /** Options for the form. */\n    set options(options) {\n        this.setField({ options });\n    }\n    get options() {\n        return this.field.options;\n    }\n    set templates(templates) {\n        this.fieldTemplates.templates = templates;\n    }\n    ngDoCheck() {\n        if (this.config.extras.checkExpressionOn === 'changeDetectionCheck') {\n            this.checkExpressionChange();\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes.fields && this.form) {\n            clearControl(this.form);\n        }\n        if (changes.fields || changes.form || (changes.model && this._modelChangeValue !== changes.model.currentValue)) {\n            this.valueChangesUnsubscribe();\n            this.builder.build(this.field);\n            this.valueChangesUnsubscribe = this.valueChanges();\n        }\n    }\n    ngOnDestroy() {\n        this.valueChangesUnsubscribe();\n    }\n    checkExpressionChange() {\n        this.field.options.checkExpressions?.(this.field);\n    }\n    valueChanges() {\n        this.valueChangesUnsubscribe();\n        const sub = this.field.options.fieldChanges\n            .pipe(filter(({ field, type }) => hasKey(field) && type === 'valueChanges'), switchMap(() => this.ngZone.onStable.asObservable().pipe(take(1))))\n            .subscribe(() => this.ngZone.runGuarded(() => {\n            // runGuarded is used to keep in sync the expression changes\n            // https://github.com/ngx-formly/ngx-formly/issues/2095\n            this.checkExpressionChange();\n            this.modelChange.emit((this._modelChangeValue = clone(this.model)));\n        }));\n        return () => sub.unsubscribe();\n    }\n    setField(field) {\n        if (this.config.extras.immutable) {\n            this.field = { ...this.field, ...clone(field) };\n        }\n        else {\n            Object.keys(field).forEach((p) => (this.field[p] = field[p]));\n        }\n    }\n}\nFormlyForm.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyForm, deps: [{ token: FormlyFormBuilder }, { token: FormlyConfig }, { token: i0.NgZone }, { token: FormlyFieldTemplates }], target: i0.ɵɵFactoryTarget.Component });\nFormlyForm.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyForm, selector: \"formly-form\", inputs: { form: \"form\", model: \"model\", fields: \"fields\", options: \"options\" }, outputs: { modelChange: \"modelChange\" }, providers: [FormlyFormBuilder, FormlyFieldTemplates], queries: [{ propertyName: \"templates\", predicate: FormlyTemplate }], usesOnChanges: true, ngImport: i0, template: '<formly-field [field]=\"field\"></formly-field>', isInline: true, components: [{ type: FormlyField, selector: \"formly-field\", inputs: [\"field\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyForm, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'formly-form',\n                    template: '<formly-field [field]=\"field\"></formly-field>',\n                    providers: [FormlyFormBuilder, FormlyFieldTemplates],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: function () { return [{ type: FormlyFormBuilder }, { type: FormlyConfig }, { type: i0.NgZone }, { type: FormlyFieldTemplates }]; }, propDecorators: { form: [{\n                type: Input\n            }], model: [{\n                type: Input\n            }], fields: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], modelChange: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [FormlyTemplate]\n            }] } });\n\n/**\n * Allow to link the `field` HTML attributes (`id`, `name` ...) and Event attributes (`focus`, `blur` ...) to an element in the DOM.\n */\nclass FormlyAttributes {\n    constructor(renderer, elementRef, _document) {\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.uiAttributesCache = {};\n        /**\n         * HostBinding doesn't register listeners conditionally which may produce some perf issues.\n         *\n         * Formly issue: https://github.com/ngx-formly/ngx-formly/issues/1991\n         */\n        this.uiEvents = {\n            listeners: [],\n            events: ['click', 'keyup', 'keydown', 'keypress', 'focus', 'blur', 'change'],\n            callback: (eventName, $event) => {\n                switch (eventName) {\n                    case 'focus':\n                        return this.onFocus($event);\n                    case 'blur':\n                        return this.onBlur($event);\n                    case 'change':\n                        return this.onChange($event);\n                    default:\n                        return this.props[eventName](this.field, $event);\n                }\n            },\n        };\n        this.document = _document;\n    }\n    get props() {\n        return this.field.props || {};\n    }\n    get fieldAttrElements() {\n        return this.field?.['_elementRefs'] || [];\n    }\n    ngOnChanges(changes) {\n        if (changes.field) {\n            this.field.name && this.setAttribute('name', this.field.name);\n            this.uiEvents.listeners.forEach((listener) => listener());\n            this.uiEvents.events.forEach((eventName) => {\n                if (this.props?.[eventName] || ['focus', 'blur', 'change'].indexOf(eventName) !== -1) {\n                    this.uiEvents.listeners.push(this.renderer.listen(this.elementRef.nativeElement, eventName, (e) => this.uiEvents.callback(eventName, e)));\n                }\n            });\n            if (this.props?.attributes) {\n                observe(this.field, ['props', 'attributes'], ({ currentValue, previousValue }) => {\n                    if (previousValue) {\n                        Object.keys(previousValue).forEach((attr) => this.removeAttribute(attr));\n                    }\n                    if (currentValue) {\n                        Object.keys(currentValue).forEach((attr) => {\n                            if (currentValue[attr] != null) {\n                                this.setAttribute(attr, currentValue[attr]);\n                            }\n                        });\n                    }\n                });\n            }\n            this.detachElementRef(changes.field.previousValue);\n            this.attachElementRef(changes.field.currentValue);\n            if (this.fieldAttrElements.length === 1) {\n                !this.id && this.field.id && this.setAttribute('id', this.field.id);\n                this.focusObserver = observe(this.field, ['focus'], ({ currentValue }) => {\n                    this.toggleFocus(currentValue);\n                });\n            }\n        }\n        if (changes.id) {\n            this.setAttribute('id', this.id);\n        }\n    }\n    /**\n     * We need to re-evaluate all the attributes on every change detection cycle, because\n     * by using a HostBinding we run into certain edge cases. This means that whatever logic\n     * is in here has to be super lean or we risk seriously damaging or destroying the performance.\n     *\n     * Formly issue: https://github.com/ngx-formly/ngx-formly/issues/1317\n     * Material issue: https://github.com/angular/components/issues/14024\n     */\n    ngDoCheck() {\n        if (!this.uiAttributes) {\n            const element = this.elementRef.nativeElement;\n            this.uiAttributes = [...FORMLY_VALIDATORS, 'tabindex', 'placeholder', 'readonly', 'disabled', 'step'].filter((attr) => !element.hasAttribute || !element.hasAttribute(attr));\n        }\n        this.uiAttributes.forEach((attr) => {\n            const value = this.props[attr];\n            if (this.uiAttributesCache[attr] !== value &&\n                (!this.props.attributes || !this.props.attributes.hasOwnProperty(attr.toLowerCase()))) {\n                this.uiAttributesCache[attr] = value;\n                if (value || value === 0) {\n                    this.setAttribute(attr, value === true ? attr : `${value}`);\n                }\n                else {\n                    this.removeAttribute(attr);\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.uiEvents.listeners.forEach((listener) => listener());\n        this.detachElementRef(this.field);\n        this.focusObserver?.unsubscribe();\n    }\n    toggleFocus(value) {\n        const element = this.fieldAttrElements ? this.fieldAttrElements[0] : null;\n        if (!element || !element.nativeElement.focus) {\n            return;\n        }\n        const isFocused = !!this.document.activeElement &&\n            this.fieldAttrElements.some(({ nativeElement }) => this.document.activeElement === nativeElement || nativeElement.contains(this.document.activeElement));\n        if (value && !isFocused) {\n            Promise.resolve().then(() => element.nativeElement.focus());\n        }\n        else if (!value && isFocused) {\n            Promise.resolve().then(() => element.nativeElement.blur());\n        }\n    }\n    onFocus($event) {\n        this.focusObserver?.setValue(true);\n        this.props.focus?.(this.field, $event);\n    }\n    onBlur($event) {\n        this.focusObserver?.setValue(false);\n        this.props.blur?.(this.field, $event);\n    }\n    // handle custom `change` event, for regular ones rely on DOM listener\n    onHostChange($event) {\n        if ($event instanceof Event) {\n            return;\n        }\n        this.onChange($event);\n    }\n    onChange($event) {\n        this.props.change?.(this.field, $event);\n        this.field.formControl?.markAsDirty();\n    }\n    attachElementRef(f) {\n        if (!f) {\n            return;\n        }\n        if (f['_elementRefs']?.indexOf(this.elementRef) === -1) {\n            f['_elementRefs'].push(this.elementRef);\n        }\n        else {\n            defineHiddenProp(f, '_elementRefs', [this.elementRef]);\n        }\n    }\n    detachElementRef(f) {\n        const index = f?.['_elementRefs'] ? this.fieldAttrElements.indexOf(this.elementRef) : -1;\n        if (index !== -1) {\n            f['_elementRefs'].splice(index, 1);\n        }\n    }\n    setAttribute(attr, value) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, attr, value);\n    }\n    removeAttribute(attr) {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, attr);\n    }\n}\nFormlyAttributes.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyAttributes, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nFormlyAttributes.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyAttributes, selector: \"[formlyAttributes]\", inputs: { field: [\"formlyAttributes\", \"field\"], id: \"id\" }, host: { listeners: { \"change\": \"onHostChange($event)\" } }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyAttributes, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[formlyAttributes]',\n                    host: {\n                        '(change)': 'onHostChange($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { field: [{\n                type: Input,\n                args: ['formlyAttributes']\n            }], id: [{\n                type: Input\n            }] } });\n\nclass FieldType {\n    get model() {\n        return this.field.model;\n    }\n    get form() {\n        return this.field.form;\n    }\n    get options() {\n        return this.field.options;\n    }\n    get key() {\n        return this.field.key;\n    }\n    get formControl() {\n        return this.field.formControl;\n    }\n    get props() {\n        return (this.field.props || {});\n    }\n    /** @deprecated Use `props` instead. */\n    get to() {\n        return this.props;\n    }\n    get showError() {\n        return this.options.showError(this);\n    }\n    get id() {\n        return this.field.id;\n    }\n    get formState() {\n        return this.options.formState || {};\n    }\n}\nFieldType.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FieldType, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nFieldType.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FieldType, inputs: { field: \"field\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FieldType, decorators: [{\n            type: Directive\n        }], propDecorators: { field: [{\n                type: Input\n            }] } });\n\n/** @ignore */\nclass FormlyGroup extends FieldType {\n}\nFormlyGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyGroup, deps: null, target: i0.ɵɵFactoryTarget.Component });\nFormlyGroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyGroup, selector: \"formly-group\", host: { properties: { \"class\": \"field.fieldGroupClassName || \\\"\\\"\" } }, usesInheritance: true, ngImport: i0, template: `\n    <formly-field *ngFor=\"let f of field.fieldGroup\" [field]=\"f\"></formly-field>\n    <ng-content></ng-content>\n  `, isInline: true, components: [{ type: FormlyField, selector: \"formly-field\", inputs: [\"field\"] }], directives: [{ type: i2$1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyGroup, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'formly-group',\n                    template: `\n    <formly-field *ngFor=\"let f of field.fieldGroup\" [field]=\"f\"></formly-field>\n    <ng-content></ng-content>\n  `,\n                    host: {\n                        '[class]': 'field.fieldGroupClassName || \"\"',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }] });\n\n/**\n * The `<formly-validation-message>` component renders the error message of a given `field`.\n */\nclass FormlyValidationMessage {\n    constructor(config) {\n        this.config = config;\n    }\n    ngOnChanges() {\n        const EXPR_VALIDATORS = FORMLY_VALIDATORS.map((v) => `templateOptions.${v}`);\n        this.errorMessage$ = merge(this.field.formControl.statusChanges, !this.field.options\n            ? of(null)\n            : this.field.options.fieldChanges.pipe(filter(({ field, type, property }) => {\n                return (field === this.field &&\n                    type === 'expressionChanges' &&\n                    (property.indexOf('validation') !== -1 || EXPR_VALIDATORS.indexOf(property) !== -1));\n            }))).pipe(startWith(null), switchMap(() => (isObservable(this.errorMessage) ? this.errorMessage : of(this.errorMessage))));\n    }\n    get errorMessage() {\n        const fieldForm = this.field.formControl;\n        for (const error in fieldForm.errors) {\n            if (fieldForm.errors.hasOwnProperty(error)) {\n                let message = this.config.getValidatorMessage(error);\n                if (isObject(fieldForm.errors[error])) {\n                    if (fieldForm.errors[error].errorPath) {\n                        return undefined;\n                    }\n                    if (fieldForm.errors[error].message) {\n                        message = fieldForm.errors[error].message;\n                    }\n                }\n                if (this.field.validation?.messages?.[error]) {\n                    message = this.field.validation.messages[error];\n                }\n                if (this.field.validators?.[error]?.message) {\n                    message = this.field.validators[error].message;\n                }\n                if (this.field.asyncValidators?.[error]?.message) {\n                    message = this.field.asyncValidators[error].message;\n                }\n                if (typeof message === 'function') {\n                    return message(fieldForm.errors[error], this.field);\n                }\n                return message;\n            }\n        }\n        return undefined;\n    }\n}\nFormlyValidationMessage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyValidationMessage, deps: [{ token: FormlyConfig }], target: i0.ɵɵFactoryTarget.Component });\nFormlyValidationMessage.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyValidationMessage, selector: \"formly-validation-message\", inputs: { field: \"field\" }, usesOnChanges: true, ngImport: i0, template: '{{ errorMessage$ | async }}', isInline: true, pipes: { \"async\": i2$1.AsyncPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyValidationMessage, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'formly-validation-message',\n                    template: '{{ errorMessage$ | async }}',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: function () { return [{ type: FormlyConfig }]; }, propDecorators: { field: [{\n                type: Input\n            }] } });\n\nclass FieldArrayType extends FieldType {\n    onPopulate(field) {\n        if (!field.formControl && hasKey(field)) {\n            const control = findControl(field);\n            registerControl(field, control ? control : new FormArray([], { updateOn: field.modelOptions.updateOn }));\n        }\n        field.fieldGroup = field.fieldGroup || [];\n        const length = Array.isArray(field.model) ? field.model.length : 0;\n        if (field.fieldGroup.length > length) {\n            for (let i = field.fieldGroup.length - 1; i >= length; --i) {\n                unregisterControl(field.fieldGroup[i], true);\n                field.fieldGroup.splice(i, 1);\n            }\n        }\n        for (let i = field.fieldGroup.length; i < length; i++) {\n            const f = {\n                ...clone(typeof field.fieldArray === 'function' ? field.fieldArray(field) : field.fieldArray),\n                key: `${i}`,\n            };\n            field.fieldGroup.push(f);\n        }\n    }\n    add(i, initialModel, { markAsDirty } = { markAsDirty: true }) {\n        i = i == null ? this.field.fieldGroup.length : i;\n        if (!this.model) {\n            assignFieldValue(this.field, []);\n        }\n        this.model.splice(i, 0, initialModel ? clone(initialModel) : undefined);\n        this._build();\n        markAsDirty && this.formControl.markAsDirty();\n    }\n    remove(i, { markAsDirty } = { markAsDirty: true }) {\n        this.model.splice(i, 1);\n        const field = this.field.fieldGroup[i];\n        this.field.fieldGroup.splice(i, 1);\n        this.field.fieldGroup.forEach((f, key) => (f.key = `${key}`));\n        unregisterControl(field, true);\n        this._build();\n        markAsDirty && this.formControl.markAsDirty();\n    }\n    _build() {\n        const fields = this.field.formControl._fields ?? [this.field];\n        fields.forEach((f) => this.options.build(f));\n        this.options.fieldChanges.next({\n            field: this.field,\n            value: getFieldValue(this.field),\n            type: 'valueChanges',\n        });\n    }\n}\nFieldArrayType.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FieldArrayType, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nFieldArrayType.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FieldArrayType, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FieldArrayType, decorators: [{\n            type: Directive\n        }] });\n\nclass FieldWrapper extends FieldType {\n    set _staticContent(content) {\n        this.fieldComponent = content;\n    }\n}\nFieldWrapper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FieldWrapper, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nFieldWrapper.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FieldWrapper, viewQueries: [{ propertyName: \"fieldComponent\", first: true, predicate: [\"fieldComponent\"], descendants: true, read: ViewContainerRef }, { propertyName: \"_staticContent\", first: true, predicate: [\"fieldComponent\"], descendants: true, read: ViewContainerRef, static: true }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FieldWrapper, decorators: [{\n            type: Directive\n        }], propDecorators: { fieldComponent: [{\n                type: ViewChild,\n                args: ['fieldComponent', { read: ViewContainerRef }]\n            }], _staticContent: [{\n                type: ViewChild,\n                args: ['fieldComponent', { read: ViewContainerRef, static: true }]\n            }] } });\n\n/** @ignore */\nclass FormlyTemplateType extends FieldType {\n    constructor(sanitizer) {\n        super();\n        this.sanitizer = sanitizer;\n        this.innerHtml = {};\n    }\n    get template() {\n        if (this.field && this.field.template !== this.innerHtml.template) {\n            this.innerHtml = {\n                template: this.field.template,\n                content: this.props.safeHtml\n                    ? this.sanitizer.bypassSecurityTrustHtml(this.field.template)\n                    : this.field.template,\n            };\n        }\n        return this.innerHtml.content;\n    }\n}\nFormlyTemplateType.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyTemplateType, deps: [{ token: i1.DomSanitizer }], target: i0.ɵɵFactoryTarget.Component });\nFormlyTemplateType.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FormlyTemplateType, selector: \"formly-template\", usesInheritance: true, ngImport: i0, template: `<div [innerHtml]=\"template\"></div>`, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyTemplateType, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'formly-template',\n                    template: `<div [innerHtml]=\"template\"></div>`,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], ctorParameters: function () { return [{ type: i1.DomSanitizer }]; } });\n\nfunction evalStringExpression(expression, argNames) {\n    try {\n        return Function(...argNames, `return ${expression};`);\n    }\n    catch (error) {\n        console.error(error);\n    }\n}\nfunction evalExpression(expression, thisArg, argVal) {\n    if (typeof expression === 'function') {\n        return expression.apply(thisArg, argVal);\n    }\n    else {\n        return expression ? true : false;\n    }\n}\n\nclass FieldExpressionExtension {\n    onPopulate(field) {\n        if (field._expressions) {\n            return;\n        }\n        // cache built expression\n        defineHiddenProp(field, '_expressions', {});\n        observe(field, ['hide'], ({ currentValue, firstChange }) => {\n            defineHiddenProp(field, '_hide', !!currentValue);\n            if (!firstChange || (firstChange && currentValue === true)) {\n                field.props.hidden = currentValue;\n                field.options._hiddenFieldsForCheck.push(field);\n            }\n        });\n        if (field.hideExpression) {\n            observe(field, ['hideExpression'], ({ currentValue: expr }) => {\n                field._expressions.hide = this.parseExpressions(field, 'hide', typeof expr === 'boolean' ? () => expr : expr);\n            });\n        }\n        const evalExpr = (key, expr) => {\n            if (typeof expr === 'string' || isFunction(expr)) {\n                field._expressions[key] = this.parseExpressions(field, key, expr);\n            }\n            else if (expr instanceof Observable) {\n                field._expressions[key] = {\n                    value$: expr.pipe(tap((v) => {\n                        this.evalExpr(field, key, v);\n                        field.options.detectChanges(field);\n                    })),\n                };\n            }\n        };\n        field.expressions = field.expressions || {};\n        for (const key of Object.keys(field.expressions)) {\n            observe(field, ['expressions', key], ({ currentValue: expr }) => {\n                evalExpr(key, isFunction(expr) ? (...args) => expr(field, args[3]) : expr);\n            });\n        }\n        field.expressionProperties = field.expressionProperties || {};\n        for (const key of Object.keys(field.expressionProperties)) {\n            observe(field, ['expressionProperties', key], ({ currentValue }) => evalExpr(key, currentValue));\n        }\n    }\n    postPopulate(field) {\n        if (field.parent) {\n            return;\n        }\n        if (!field.options.checkExpressions) {\n            let checkLocked = false;\n            field.options.checkExpressions = (f, ignoreCache) => {\n                if (checkLocked) {\n                    return;\n                }\n                checkLocked = true;\n                const fieldChanged = this.checkExpressions(f, ignoreCache);\n                const options = field.options;\n                options._hiddenFieldsForCheck\n                    .sort((f) => (f.hide ? -1 : 1))\n                    .forEach((f) => this.changeHideState(f, f.hide, !ignoreCache));\n                options._hiddenFieldsForCheck = [];\n                if (fieldChanged) {\n                    this.checkExpressions(field);\n                    if (field.options && field.options.detectChanges) {\n                        field.options.detectChanges(field);\n                    }\n                }\n                checkLocked = false;\n            };\n            field.options._checkField = (f, ignoreCache) => {\n                console.warn(`Formly: 'options._checkField' is deprecated since v6.0, use 'options.checkExpressions' instead.`);\n                field.options.checkExpressions(f, ignoreCache);\n            };\n        }\n    }\n    parseExpressions(field, path, expr) {\n        let parentExpression;\n        if (field.parent && ['hide', 'props.disabled'].includes(path)) {\n            const rootValue = (f) => {\n                return path === 'hide' ? f.hide : f.props.disabled;\n            };\n            parentExpression = () => {\n                let root = field.parent;\n                while (root.parent && !rootValue(root)) {\n                    root = root.parent;\n                }\n                return rootValue(root);\n            };\n        }\n        expr = expr || (() => false);\n        if (typeof expr === 'string') {\n            expr = evalStringExpression(expr, ['model', 'formState', 'field']);\n        }\n        let currentValue;\n        return {\n            callback: (ignoreCache) => {\n                try {\n                    const exprValue = evalExpression(parentExpression ? (...args) => parentExpression(field) || expr(...args) : expr, { field }, [field.model, field.options.formState, field, ignoreCache]);\n                    if (ignoreCache ||\n                        (currentValue !== exprValue &&\n                            (!isObject(exprValue) ||\n                                isObservable(exprValue) ||\n                                JSON.stringify(exprValue) !== JSON.stringify(currentValue)))) {\n                        currentValue = exprValue;\n                        this.evalExpr(field, path, exprValue);\n                        return true;\n                    }\n                    return false;\n                }\n                catch (error) {\n                    error.message = `[Formly Error] [Expression \"${path}\"] ${error.message}`;\n                    throw error;\n                }\n            },\n        };\n    }\n    checkExpressions(field, ignoreCache = false) {\n        if (!field) {\n            return false;\n        }\n        let fieldChanged = false;\n        if (field._expressions) {\n            for (const key of Object.keys(field._expressions)) {\n                field._expressions[key].callback?.(ignoreCache) && (fieldChanged = true);\n            }\n        }\n        field.fieldGroup?.forEach((f) => this.checkExpressions(f, ignoreCache) && (fieldChanged = true));\n        return fieldChanged;\n    }\n    changeDisabledState(field, value) {\n        if (field.fieldGroup) {\n            field.fieldGroup\n                .filter((f) => !f._expressions.hasOwnProperty('props.disabled'))\n                .forEach((f) => this.changeDisabledState(f, value));\n        }\n        if (hasKey(field) && field.props.disabled !== value) {\n            field.props.disabled = value;\n        }\n    }\n    changeHideState(field, hide, resetOnHide) {\n        if (field.fieldGroup) {\n            field.fieldGroup\n                .filter((f) => !f._expressions.hide)\n                .forEach((f) => this.changeHideState(f, hide, resetOnHide));\n        }\n        if (field.formControl && hasKey(field)) {\n            defineHiddenProp(field, '_hide', !!(hide || field.hide));\n            const c = field.formControl;\n            if (c._fields?.length > 1) {\n                updateValidity(c);\n            }\n            if (hide === true && (!c._fields || c._fields.every((f) => !!f._hide))) {\n                unregisterControl(field, true);\n                if (resetOnHide && field.resetOnHide) {\n                    assignFieldValue(field, undefined);\n                    field.formControl.reset({ value: undefined, disabled: field.formControl.disabled });\n                    field.options.fieldChanges.next({ value: undefined, field, type: 'valueChanges' });\n                    if (field.fieldGroup && field.formControl instanceof FormArray) {\n                        field.fieldGroup.length = 0;\n                    }\n                }\n            }\n            else if (hide === false) {\n                if (field.resetOnHide && !isUndefined(field.defaultValue) && isUndefined(getFieldValue(field))) {\n                    assignFieldValue(field, field.defaultValue);\n                }\n                registerControl(field, undefined, true);\n                if (field.resetOnHide && field.fieldArray && field.fieldGroup?.length !== field.model?.length) {\n                    field.options.build(field);\n                }\n            }\n        }\n        if (field.options.fieldChanges) {\n            field.options.fieldChanges.next({ field, type: 'hidden', value: hide });\n        }\n    }\n    evalExpr(field, prop, value) {\n        if (prop.indexOf('model.') === 0) {\n            const key = prop.replace(/^model\\./, ''), parent = field.fieldGroup ? field : field.parent;\n            let control = field?.key === key ? field.formControl : field.form.get(key);\n            if (!control && field.get(key)) {\n                control = field.get(key).formControl;\n            }\n            assignFieldValue({ key, parent, model: field.model }, value);\n            if (control && !(isNil(control.value) && isNil(value)) && control.value !== value) {\n                control.patchValue(value);\n            }\n        }\n        else {\n            try {\n                let target = field;\n                const paths = this._evalExpressionPath(field, prop);\n                const lastIndex = paths.length - 1;\n                for (let i = 0; i < lastIndex; i++) {\n                    target = target[paths[i]];\n                }\n                target[paths[lastIndex]] = value;\n            }\n            catch (error) {\n                error.message = `[Formly Error] [Expression \"${prop}\"] ${error.message}`;\n                throw error;\n            }\n            if (['templateOptions.disabled', 'props.disabled'].includes(prop) && hasKey(field)) {\n                this.changeDisabledState(field, value);\n            }\n        }\n        this.emitExpressionChanges(field, prop, value);\n    }\n    emitExpressionChanges(field, property, value) {\n        if (!field.options.fieldChanges) {\n            return;\n        }\n        field.options.fieldChanges.next({\n            field,\n            type: 'expressionChanges',\n            property,\n            value,\n        });\n    }\n    _evalExpressionPath(field, prop) {\n        if (field._expressions[prop] && field._expressions[prop].paths) {\n            return field._expressions[prop].paths;\n        }\n        let paths = [];\n        if (prop.indexOf('[') === -1) {\n            paths = prop.split('.');\n        }\n        else {\n            prop\n                .split(/[[\\]]{1,2}/) // https://stackoverflow.com/a/20198206\n                .filter((p) => p)\n                .forEach((path) => {\n                const arrayPath = path.match(/['|\"](.*?)['|\"]/);\n                if (arrayPath) {\n                    paths.push(arrayPath[1]);\n                }\n                else {\n                    paths.push(...path.split('.').filter((p) => p));\n                }\n            });\n        }\n        if (field._expressions[prop]) {\n            field._expressions[prop].paths = paths;\n        }\n        return paths;\n    }\n}\n\nclass FieldValidationExtension {\n    constructor(config) {\n        this.config = config;\n    }\n    onPopulate(field) {\n        this.initFieldValidation(field, 'validators');\n        this.initFieldValidation(field, 'asyncValidators');\n    }\n    initFieldValidation(field, type) {\n        const validators = [];\n        if (type === 'validators' && !(field.hasOwnProperty('fieldGroup') && !hasKey(field))) {\n            validators.push(this.getPredefinedFieldValidation(field));\n        }\n        if (field[type]) {\n            for (const validatorName of Object.keys(field[type])) {\n                validatorName === 'validation'\n                    ? validators.push(...field[type].validation.map((v) => this.wrapNgValidatorFn(field, v)))\n                    : validators.push(this.wrapNgValidatorFn(field, field[type][validatorName], validatorName));\n            }\n        }\n        defineHiddenProp(field, '_' + type, validators);\n    }\n    getPredefinedFieldValidation(field) {\n        let VALIDATORS = [];\n        FORMLY_VALIDATORS.forEach((opt) => observe(field, ['props', opt], ({ currentValue, firstChange }) => {\n            VALIDATORS = VALIDATORS.filter((o) => o !== opt);\n            if (currentValue != null && currentValue !== false) {\n                VALIDATORS.push(opt);\n            }\n            if (!firstChange && field.formControl) {\n                updateValidity(field.formControl);\n            }\n        }));\n        return (control) => {\n            if (VALIDATORS.length === 0) {\n                return null;\n            }\n            return Validators.compose(VALIDATORS.map((opt) => () => {\n                const value = field.props[opt];\n                switch (opt) {\n                    case 'required':\n                        return Validators.required(control);\n                    case 'pattern':\n                        return Validators.pattern(value)(control);\n                    case 'minLength':\n                        const minLengthResult = Validators.minLength(value)(control);\n                        const minLengthKey = this.config.getValidatorMessage('minlength') || field.validation?.messages?.minlength\n                            ? 'minlength'\n                            : 'minLength';\n                        return minLengthResult ? { [minLengthKey]: minLengthResult.minlength } : null;\n                    case 'maxLength':\n                        const maxLengthResult = Validators.maxLength(value)(control);\n                        const maxLengthKey = this.config.getValidatorMessage('maxlength') || field.validation?.messages?.maxlength\n                            ? 'maxlength'\n                            : 'maxLength';\n                        return maxLengthResult ? { [maxLengthKey]: maxLengthResult.maxlength } : null;\n                    case 'min':\n                        return Validators.min(value)(control);\n                    case 'max':\n                        return Validators.max(value)(control);\n                    default:\n                        return null;\n                }\n            }))(control);\n        };\n    }\n    wrapNgValidatorFn(field, validator, validatorName) {\n        let validatorOption;\n        if (typeof validator === 'string') {\n            validatorOption = clone(this.config.getValidator(validator));\n        }\n        if (typeof validator === 'object' && validator.name) {\n            validatorOption = clone(this.config.getValidator(validator.name));\n            if (validator.options) {\n                validatorOption.options = validator.options;\n            }\n        }\n        if (typeof validator === 'object' && validator.expression) {\n            const { expression, ...options } = validator;\n            validatorOption = {\n                name: validatorName,\n                validation: expression,\n                options: Object.keys(options).length > 0 ? options : null,\n            };\n        }\n        if (typeof validator === 'function') {\n            validatorOption = {\n                name: validatorName,\n                validation: validator,\n            };\n        }\n        return (control) => {\n            const errors = validatorOption.validation(control, field, validatorOption.options);\n            if (isPromise(errors)) {\n                return errors.then((v) => this.handleAsyncResult(field, validatorName ? !!v : v, validatorOption));\n            }\n            if (isObservable(errors)) {\n                return errors.pipe(map((v) => this.handleAsyncResult(field, validatorName ? !!v : v, validatorOption)));\n            }\n            return this.handleResult(field, validatorName ? !!errors : errors, validatorOption);\n        };\n    }\n    handleAsyncResult(field, errors, options) {\n        // workaround for https://github.com/angular/angular/issues/13200\n        field.options.detectChanges(field);\n        return this.handleResult(field, errors, options);\n    }\n    handleResult(field, errors, { name, options }) {\n        if (typeof errors === 'boolean') {\n            errors = errors ? null : { [name]: options ? options : true };\n        }\n        const ctrl = field.formControl;\n        ctrl?._childrenErrors?.[name]?.();\n        if (isObject(errors)) {\n            Object.keys(errors).forEach((name) => {\n                const errorPath = errors[name].errorPath ? errors[name].errorPath : options?.errorPath;\n                const childCtrl = errorPath ? field.formControl.get(errorPath) : null;\n                if (childCtrl) {\n                    const { errorPath: _errorPath, ...opts } = errors[name];\n                    childCtrl.setErrors({ ...(childCtrl.errors || {}), [name]: opts });\n                    !ctrl._childrenErrors && defineHiddenProp(ctrl, '_childrenErrors', {});\n                    ctrl._childrenErrors[name] = () => {\n                        const { [name]: _toDelete, ...childErrors } = childCtrl.errors || {};\n                        childCtrl.setErrors(Object.keys(childErrors).length === 0 ? null : childErrors);\n                    };\n                }\n            });\n        }\n        return errors;\n    }\n}\n\nclass FieldFormExtension {\n    prePopulate(field) {\n        if (!this.root) {\n            this.root = field;\n        }\n        if (field.parent) {\n            Object.defineProperty(field, 'form', {\n                get: () => field.parent.formControl,\n                configurable: true,\n            });\n        }\n    }\n    onPopulate(field) {\n        if (field.hasOwnProperty('fieldGroup') && !hasKey(field)) {\n            defineHiddenProp(field, 'formControl', field.form);\n        }\n        else {\n            this.addFormControl(field);\n        }\n    }\n    postPopulate(field) {\n        if (this.root !== field) {\n            return;\n        }\n        this.root = null;\n        const markForCheck = this.setValidators(field);\n        if (markForCheck && field.parent) {\n            let parent = field.parent;\n            while (parent) {\n                if (hasKey(parent) || !parent.parent) {\n                    updateValidity(parent.formControl, true);\n                }\n                parent = parent.parent;\n            }\n        }\n    }\n    addFormControl(field) {\n        let control = findControl(field);\n        if (field.fieldArray) {\n            return;\n        }\n        if (!control) {\n            const controlOptions = { updateOn: field.modelOptions.updateOn };\n            if (field.fieldGroup) {\n                control = new FormGroup({}, controlOptions);\n            }\n            else {\n                const value = hasKey(field) ? getFieldValue(field) : field.defaultValue;\n                control = new FormControl({ value, disabled: !!field.props.disabled }, { ...controlOptions, initialValueIsDefault: true });\n            }\n        }\n        registerControl(field, control);\n    }\n    setValidators(field, disabled = false) {\n        if (disabled === false && hasKey(field) && field.props?.disabled) {\n            disabled = true;\n        }\n        let markForCheck = false;\n        field.fieldGroup?.forEach((f) => f && this.setValidators(f, disabled) && (markForCheck = true));\n        if (hasKey(field) || !field.parent || (!hasKey(field) && !field.fieldGroup)) {\n            const { formControl: c } = field;\n            if (c) {\n                if (hasKey(field) && c instanceof FormControl) {\n                    if (disabled && c.enabled) {\n                        c.disable({ emitEvent: false, onlySelf: true });\n                        markForCheck = true;\n                    }\n                    if (!disabled && c.disabled) {\n                        c.enable({ emitEvent: false, onlySelf: true });\n                        markForCheck = true;\n                    }\n                }\n                if (null === c.validator || null === c.asyncValidator) {\n                    c.setValidators(() => {\n                        const v = Validators.compose(this.mergeValidators(field, '_validators'));\n                        return v ? v(c) : null;\n                    });\n                    c.setAsyncValidators(() => {\n                        const v = Validators.composeAsync(this.mergeValidators(field, '_asyncValidators'));\n                        return v ? v(c) : of(null);\n                    });\n                    markForCheck = true;\n                }\n                if (markForCheck) {\n                    updateValidity(c, true);\n                    // update validity of `FormGroup` instance created by field with nested key.\n                    let parent = c.parent;\n                    for (let i = 1; i < getKeyPath(field).length; i++) {\n                        if (parent) {\n                            updateValidity(parent, true);\n                            parent = parent.parent;\n                        }\n                    }\n                }\n            }\n        }\n        return markForCheck;\n    }\n    mergeValidators(field, type) {\n        const validators = [];\n        const c = field.formControl;\n        if (c?._fields?.length > 1) {\n            c._fields\n                .filter((f) => !f._hide)\n                .forEach((f) => validators.push(...f[type]));\n        }\n        else if (field[type]) {\n            validators.push(...field[type]);\n        }\n        if (field.fieldGroup) {\n            field.fieldGroup\n                .filter((f) => f?.fieldGroup && !hasKey(f))\n                .forEach((f) => validators.push(...this.mergeValidators(f, type)));\n        }\n        return validators;\n    }\n}\n\nclass CoreExtension {\n    constructor(config) {\n        this.config = config;\n        this.formId = 0;\n    }\n    prePopulate(field) {\n        const root = field.parent;\n        this.initRootOptions(field);\n        this.initFieldProps(field);\n        if (root) {\n            Object.defineProperty(field, 'options', { get: () => root.options, configurable: true });\n            Object.defineProperty(field, 'model', {\n                get: () => (hasKey(field) && field.fieldGroup ? getFieldValue(field) : root.model),\n                configurable: true,\n            });\n        }\n        Object.defineProperty(field, 'get', {\n            value: (key) => getField(field, key),\n            configurable: true,\n        });\n        this.getFieldComponentInstance(field).prePopulate?.(field);\n    }\n    onPopulate(field) {\n        this.initFieldOptions(field);\n        this.getFieldComponentInstance(field).onPopulate?.(field);\n        if (field.fieldGroup) {\n            field.fieldGroup.forEach((f, index) => {\n                if (f) {\n                    Object.defineProperty(f, 'parent', { get: () => field, configurable: true });\n                    Object.defineProperty(f, 'index', { get: () => index, configurable: true });\n                }\n                this.formId++;\n            });\n        }\n    }\n    postPopulate(field) {\n        this.getFieldComponentInstance(field).postPopulate?.(field);\n    }\n    initFieldProps(field) {\n        field.props ?? (field.props = field.templateOptions);\n        Object.defineProperty(field, 'templateOptions', {\n            get: () => field.props,\n            set: (props) => (field.props = props),\n            configurable: true,\n        });\n    }\n    initRootOptions(field) {\n        if (field.parent) {\n            return;\n        }\n        const options = field.options;\n        field.options.formState = field.options.formState || {};\n        if (!options.showError) {\n            options.showError = this.config.extras.showError;\n        }\n        if (!options.fieldChanges) {\n            defineHiddenProp(options, 'fieldChanges', new Subject());\n        }\n        if (!options._hiddenFieldsForCheck) {\n            options._hiddenFieldsForCheck = [];\n        }\n        options._markForCheck = (f) => {\n            console.warn(`Formly: 'options._markForCheck' is deprecated since v6.0, use 'options.detectChanges' instead.`);\n            options.detectChanges(f);\n        };\n        options.detectChanges = (f) => {\n            if (f._componentRefs) {\n                f.options.checkExpressions(f);\n                markFieldForCheck(f);\n            }\n            f.fieldGroup?.forEach((f) => f && options.detectChanges(f));\n        };\n        options.resetModel = (model) => {\n            model = clone(model ?? options._initialModel);\n            if (field.model) {\n                Object.keys(field.model).forEach((k) => delete field.model[k]);\n                Object.assign(field.model, model || {});\n            }\n            options.build(field);\n            field.form.reset(field.model);\n            if (options.parentForm && options.parentForm.control === field.formControl) {\n                options.parentForm.submitted = false;\n            }\n        };\n        options.updateInitialValue = (model) => (options._initialModel = clone(model ?? field.model));\n        field.options.updateInitialValue();\n    }\n    initFieldOptions(field) {\n        reverseDeepMerge(field, {\n            id: getFieldId(`formly_${this.formId}`, field, field.index),\n            hooks: {},\n            modelOptions: {},\n            validation: { messages: {} },\n            props: !field.type || !hasKey(field)\n                ? {}\n                : {\n                    label: '',\n                    placeholder: '',\n                    disabled: false,\n                },\n        });\n        if (this.config.extras.resetFieldOnHide && field.resetOnHide !== false) {\n            field.resetOnHide = true;\n        }\n        if (field.type !== 'formly-template' &&\n            (field.template || field.expressions?.template || field.expressionProperties?.template)) {\n            field.type = 'formly-template';\n        }\n        if (!field.type && field.fieldGroup) {\n            field.type = 'formly-group';\n        }\n        if (field.type) {\n            this.config.getMergedField(field);\n        }\n        if (hasKey(field) && !isUndefined(field.defaultValue) && isUndefined(getFieldValue(field))) {\n            const isHidden = (f) => f.hide || f.expressions?.hide || f.hideExpression;\n            let setDefaultValue = !field.resetOnHide || !isHidden(field);\n            if (!isHidden(field) && field.resetOnHide) {\n                let parent = field.parent;\n                while (parent && !isHidden(parent)) {\n                    parent = parent.parent;\n                }\n                setDefaultValue = !parent || !isHidden(parent);\n            }\n            if (setDefaultValue) {\n                assignFieldValue(field, field.defaultValue);\n            }\n        }\n        field.wrappers = field.wrappers || [];\n    }\n    getFieldComponentInstance(field) {\n        const componentRefInstance = () => {\n            let componentRef = this.config.resolveFieldTypeRef(field);\n            const fieldComponentRef = field._componentRefs?.slice(-1)[0];\n            if (fieldComponentRef instanceof ComponentRef &&\n                fieldComponentRef?.componentType === componentRef?.componentType) {\n                componentRef = fieldComponentRef;\n            }\n            return componentRef?.instance;\n        };\n        if (!field._proxyInstance) {\n            defineHiddenProp(field, '_proxyInstance', new Proxy({}, {\n                get: (_, prop) => componentRefInstance()?.[prop],\n                set: (_, prop, value) => (componentRefInstance()[prop] = value),\n            }));\n        }\n        return field._proxyInstance;\n    }\n}\n\nfunction defaultFormlyConfig(config) {\n    return {\n        types: [\n            { name: 'formly-group', component: FormlyGroup },\n            { name: 'formly-template', component: FormlyTemplateType },\n        ],\n        extensions: [\n            { name: 'core', extension: new CoreExtension(config), priority: -250 },\n            { name: 'field-validation', extension: new FieldValidationExtension(config), priority: -200 },\n            { name: 'field-form', extension: new FieldFormExtension(), priority: -150 },\n            { name: 'field-expression', extension: new FieldExpressionExtension(), priority: -100 },\n        ],\n    };\n}\nclass FormlyModule {\n    constructor(configService, configs = []) {\n        if (!configs) {\n            return;\n        }\n        configs.forEach((config) => configService.addConfig(config));\n    }\n    static forRoot(config = {}) {\n        return {\n            ngModule: FormlyModule,\n            providers: [\n                { provide: FORMLY_CONFIG, multi: true, useFactory: defaultFormlyConfig, deps: [FormlyConfig] },\n                { provide: FORMLY_CONFIG, useValue: config, multi: true },\n                FormlyConfig,\n                FormlyFormBuilder,\n            ],\n        };\n    }\n    static forChild(config = {}) {\n        return {\n            ngModule: FormlyModule,\n            providers: [\n                { provide: FORMLY_CONFIG, multi: true, useFactory: defaultFormlyConfig, deps: [FormlyConfig] },\n                { provide: FORMLY_CONFIG, useValue: config, multi: true },\n                FormlyFormBuilder,\n            ],\n        };\n    }\n}\nFormlyModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyModule, deps: [{ token: FormlyConfig }, { token: FORMLY_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.NgModule });\nFormlyModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyModule, declarations: [FormlyTemplate,\n        FormlyForm,\n        FormlyField,\n        FormlyAttributes,\n        FormlyGroup,\n        FormlyValidationMessage,\n        FormlyTemplateType], imports: [CommonModule], exports: [FormlyTemplate, FormlyForm, FormlyField, FormlyAttributes, FormlyGroup, FormlyValidationMessage] });\nFormlyModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FormlyModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        FormlyTemplate,\n                        FormlyForm,\n                        FormlyField,\n                        FormlyAttributes,\n                        FormlyGroup,\n                        FormlyValidationMessage,\n                        FormlyTemplateType,\n                    ],\n                    exports: [FormlyTemplate, FormlyForm, FormlyField, FormlyAttributes, FormlyGroup, FormlyValidationMessage],\n                    imports: [CommonModule],\n                }]\n        }], ctorParameters: function () { return [{ type: FormlyConfig }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FORMLY_CONFIG]\n                }] }]; } });\n\n/*\n * Public API Surface of core\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FORMLY_CONFIG, FieldArrayType, FieldType, FieldWrapper, FormlyConfig, FormlyField, FormlyForm, FormlyFormBuilder, FormlyModule, FormlyAttributes as ɵFormlyAttributes, FormlyGroup as ɵFormlyGroup, FormlyTemplate as ɵFormlyTemplate, FormlyValidationMessage as ɵFormlyValidationMessage, clone as ɵclone, defineHiddenProp as ɵdefineHiddenProp, getFieldValue as ɵgetFieldValue, hasKey as ɵhasKey, observe as ɵobserve, reverseDeepMerge as ɵreverseDeepMerge };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,EAAEC,WAAW,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACpQ,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,eAAe,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAC/F,SAASC,YAAY,EAAEC,KAAK,EAAEC,EAAE,EAAEC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AACnE,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AACjH,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAAC,MAAAC,GAAA;AAAA,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;AAAA,SAAAC,oCAAAF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA8egD5C,EAAE,CAAA+C,SAAA,qBA0yBnB,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAI,IAAA,GAAAH,GAAA,CAAAI,SAAA;IA1yBgBjD,EAAE,CAAAkD,UAAA,UAAAF,IA0yBnC,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA;AAAA,MAAAC,GAAA;AAtxChE,SAASC,uBAAuBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC7C,MAAMC,mBAAmB,GAAGF,IAAI,CAACE,mBAAmB,CAACC,IAAI,CAACH,IAAI,CAAC;EAC/DA,IAAI,CAACE,mBAAmB,GAAG,MAAM,CAAE,CAAC;EACpCD,QAAQ,CAAC,CAAC;EACVD,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB;AAClD;AACA,SAASE,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACtC,IAAID,KAAK,CAACE,EAAE,EAAE;IACV,OAAOF,KAAK,CAACE,EAAE;EACnB;EACA,IAAIC,IAAI,GAAGH,KAAK,CAACG,IAAI;EACrB,IAAI,CAACA,IAAI,IAAIH,KAAK,CAACI,QAAQ,EAAE;IACzBD,IAAI,GAAG,UAAU;EACrB;EACA,IAAIA,IAAI,YAAY9D,IAAI,EAAE;IACtB8D,IAAI,GAAGA,IAAI,CAACE,SAAS,CAACC,WAAW,CAACC,IAAI;EAC1C;EACA,OAAO,CAACR,MAAM,EAAEI,IAAI,EAAEH,KAAK,CAACQ,GAAG,EAAEP,KAAK,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;AACrD;AACA,SAASC,MAAMA,CAACV,KAAK,EAAE;EACnB,OAAO,CAACW,KAAK,CAACX,KAAK,CAACQ,GAAG,CAAC,IAAIR,KAAK,CAACQ,GAAG,KAAK,EAAE;AAChD;AACA,SAASI,UAAUA,CAACZ,KAAK,EAAE;EACvB,IAAI,CAACU,MAAM,CAACV,KAAK,CAAC,EAAE;IAChB,OAAO,EAAE;EACb;EACA;EACA,IAAIA,KAAK,CAACa,QAAQ,EAAEL,GAAG,KAAKR,KAAK,CAACQ,GAAG,EAAE;IACnC,IAAIM,IAAI,GAAG,EAAE;IACb,IAAI,OAAOd,KAAK,CAACQ,GAAG,KAAK,QAAQ,EAAE;MAC/B,MAAMA,GAAG,GAAGR,KAAK,CAACQ,GAAG,CAACO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGf,KAAK,CAACQ,GAAG,GAAGR,KAAK,CAACQ,GAAG,CAACQ,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;MAC9FF,IAAI,GAAGN,GAAG,CAACO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGP,GAAG,CAACS,KAAK,CAAC,GAAG,CAAC,GAAG,CAACT,GAAG,CAAC;IAC3D,CAAC,MACI,IAAIU,KAAK,CAACC,OAAO,CAACnB,KAAK,CAACQ,GAAG,CAAC,EAAE;MAC/BM,IAAI,GAAGd,KAAK,CAACQ,GAAG,CAACY,KAAK,CAAC,CAAC,CAAC;IAC7B,CAAC,MACI;MACDN,IAAI,GAAG,CAAE,GAAEd,KAAK,CAACQ,GAAI,EAAC,CAAC;IAC3B;IACAa,gBAAgB,CAACrB,KAAK,EAAE,UAAU,EAAE;MAAEQ,GAAG,EAAER,KAAK,CAACQ,GAAG;MAAEM;IAAK,CAAC,CAAC;EACjE;EACA,OAAOd,KAAK,CAACa,QAAQ,CAACC,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC;AACvC;AACA,MAAME,iBAAiB,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC;AACzF,SAASC,gBAAgBA,CAACvB,KAAK,EAAEwB,KAAK,EAAE;EACpC,IAAIC,KAAK,GAAGb,UAAU,CAACZ,KAAK,CAAC;EAC7B,IAAIyB,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB;EACJ;EACA,IAAIC,IAAI,GAAG3B,KAAK;EAChB,OAAO2B,IAAI,CAACC,MAAM,EAAE;IAChBD,IAAI,GAAGA,IAAI,CAACC,MAAM;IAClBH,KAAK,GAAG,CAAC,GAAGb,UAAU,CAACe,IAAI,CAAC,EAAE,GAAGF,KAAK,CAAC;EAC3C;EACA,IAAID,KAAK,KAAKK,SAAS,IAAI7B,KAAK,CAAC8B,WAAW,EAAE;IAC1C,MAAMC,CAAC,GAAGN,KAAK,CAACO,GAAG,CAAC,CAAC;IACrB,MAAMC,CAAC,GAAGR,KAAK,CAACS,MAAM,CAAC,CAACC,KAAK,EAAErB,IAAI,KAAKqB,KAAK,CAACrB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEa,IAAI,CAACQ,KAAK,CAAC;IACtE,OAAOF,CAAC,CAACF,CAAC,CAAC;IACX;EACJ;EACAK,gBAAgB,CAACT,IAAI,CAACQ,KAAK,EAAEV,KAAK,EAAED,KAAK,CAAC;AAC9C;AACA,SAASY,gBAAgBA,CAACD,KAAK,EAAEV,KAAK,EAAED,KAAK,EAAE;EAC3C,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEW,CAAC,EAAE,EAAE;IACvC,MAAMvB,IAAI,GAAGW,KAAK,CAACY,CAAC,CAAC;IACrB,IAAI,CAACF,KAAK,CAACrB,IAAI,CAAC,IAAI,CAACwB,QAAQ,CAACH,KAAK,CAACrB,IAAI,CAAC,CAAC,EAAE;MACxCqB,KAAK,CAACrB,IAAI,CAAC,GAAG,OAAO,CAACyB,IAAI,CAACd,KAAK,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACtD;IACAF,KAAK,GAAGA,KAAK,CAACrB,IAAI,CAAC;EACvB;EACAqB,KAAK,CAACV,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGc,KAAK,CAAChB,KAAK,CAAC;AACjD;AACA,SAASiB,aAAaA,CAACzC,KAAK,EAAE;EAC1B,IAAImC,KAAK,GAAGnC,KAAK,CAAC4B,MAAM,GAAG5B,KAAK,CAAC4B,MAAM,CAACO,KAAK,GAAGnC,KAAK,CAACmC,KAAK;EAC3D,KAAK,MAAMrB,IAAI,IAAIF,UAAU,CAACZ,KAAK,CAAC,EAAE;IAClC,IAAI,CAACmC,KAAK,EAAE;MACR,OAAOA,KAAK;IAChB;IACAA,KAAK,GAAGA,KAAK,CAACrB,IAAI,CAAC;EACvB;EACA,OAAOqB,KAAK;AAChB;AACA,SAASO,gBAAgBA,CAACC,IAAI,EAAE,GAAGC,IAAI,EAAE;EACrCA,IAAI,CAACC,OAAO,CAAEC,GAAG,IAAK;IAClB,KAAK,MAAMC,MAAM,IAAID,GAAG,EAAE;MACtB,IAAInC,KAAK,CAACgC,IAAI,CAACI,MAAM,CAAC,CAAC,IAAIC,aAAa,CAACL,IAAI,CAACI,MAAM,CAAC,CAAC,EAAE;QACpDJ,IAAI,CAACI,MAAM,CAAC,GAAGP,KAAK,CAACM,GAAG,CAACC,MAAM,CAAC,CAAC;MACrC,CAAC,MACI,IAAIE,cAAc,CAACN,IAAI,CAACI,MAAM,CAAC,EAAED,GAAG,CAACC,MAAM,CAAC,CAAC,EAAE;QAChDL,gBAAgB,CAACC,IAAI,CAACI,MAAM,CAAC,EAAED,GAAG,CAACC,MAAM,CAAC,CAAC;MAC/C;IACJ;EACJ,CAAC,CAAC;EACF,OAAOJ,IAAI;AACf;AACA;AACA,SAAShC,KAAKA,CAACa,KAAK,EAAE;EAClB,OAAOA,KAAK,IAAI,IAAI;AACxB;AACA,SAAS0B,WAAWA,CAAC1B,KAAK,EAAE;EACxB,OAAOA,KAAK,KAAKK,SAAS;AAC9B;AACA,SAASmB,aAAaA,CAACxB,KAAK,EAAE;EAC1B,OAAOA,KAAK,KAAK,EAAE;AACvB;AACA,SAAS2B,UAAUA,CAAC3B,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACA,SAASyB,cAAcA,CAACG,IAAI,EAAEC,IAAI,EAAE;EAChC,OAAQf,QAAQ,CAACc,IAAI,CAAC,IAClBd,QAAQ,CAACe,IAAI,CAAC,IACdC,MAAM,CAACC,cAAc,CAACH,IAAI,CAAC,KAAKE,MAAM,CAACC,cAAc,CAACF,IAAI,CAAC,IAC3D,EAAEnC,KAAK,CAACC,OAAO,CAACiC,IAAI,CAAC,IAAIlC,KAAK,CAACC,OAAO,CAACkC,IAAI,CAAC,CAAC;AACrD;AACA,SAASf,QAAQA,CAACkB,CAAC,EAAE;EACjB,OAAOA,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ;AAC7C;AACA,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,OAAO,CAAC,CAACA,GAAG,IAAI,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU;AAClD;AACA,SAASnB,KAAKA,CAAChB,KAAK,EAAE;EAClB,IAAI,CAACc,QAAQ,CAACd,KAAK,CAAC,IAChB3D,YAAY,CAAC2D,KAAK,CAAC,IACnBA,KAAK,YAAYlF,WAAW,IAC5B,6BAA8BkF,KAAK,CAACoC,qCAAqC,IACzE,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC7C,OAAO,CAACS,KAAK,CAAClB,WAAW,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IAC/E,OAAOiB,KAAK;EAChB;EACA,IAAIA,KAAK,YAAYqC,GAAG,EAAE;IACtB,OAAO,IAAIA,GAAG,CAACrC,KAAK,CAAC;EACzB;EACA,IAAIA,KAAK,YAAYsC,GAAG,EAAE;IACtB,OAAO,IAAIA,GAAG,CAACtC,KAAK,CAAC;EACzB;EACA;EACA,IAAIA,KAAK,CAACuC,gBAAgB,IAAIZ,UAAU,CAAC3B,KAAK,CAACgB,KAAK,CAAC,EAAE;IACnD,OAAOhB,KAAK,CAACgB,KAAK,CAAC,CAAC;EACxB;EACA,IAAIhB,KAAK,YAAYhE,eAAe,EAAE;IAClC,OAAO,IAAI;EACf;EACA,IAAIgE,KAAK,YAAYwC,IAAI,EAAE;IACvB,OAAO,IAAIA,IAAI,CAACxC,KAAK,CAACyC,OAAO,CAAC,CAAC,CAAC;EACpC;EACA,IAAI/C,KAAK,CAACC,OAAO,CAACK,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC3C,GAAG,CAAEyF,CAAC,IAAK1B,KAAK,CAAC0B,CAAC,CAAC,CAAC;EAC9C;EACA;EACA;EACA,MAAMC,KAAK,GAAGb,MAAM,CAACC,cAAc,CAAC/B,KAAK,CAAC;EAC1C,IAAI4C,CAAC,GAAGd,MAAM,CAACe,MAAM,CAACF,KAAK,CAAC;EAC5BC,CAAC,GAAGd,MAAM,CAACgB,cAAc,CAACF,CAAC,EAAED,KAAK,CAAC;EACnC;EACA;EACA,OAAOb,MAAM,CAACiB,IAAI,CAAC/C,KAAK,CAAC,CAACU,MAAM,CAAC,CAACsC,MAAM,EAAEC,IAAI,KAAK;IAC/C,MAAMC,QAAQ,GAAGpB,MAAM,CAACqB,wBAAwB,CAACnD,KAAK,EAAEiD,IAAI,CAAC;IAC7D,IAAIC,QAAQ,CAACE,GAAG,EAAE;MACdtB,MAAM,CAACuB,cAAc,CAACL,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;IACjD,CAAC,MACI;MACDF,MAAM,CAACC,IAAI,CAAC,GAAGjC,KAAK,CAAChB,KAAK,CAACiD,IAAI,CAAC,CAAC;IACrC;IACA,OAAOD,MAAM;EACjB,CAAC,EAAEJ,CAAC,CAAC;AACT;AACA,SAAS/C,gBAAgBA,CAACrB,KAAK,EAAEyE,IAAI,EAAEK,YAAY,EAAE;EACjDxB,MAAM,CAACuB,cAAc,CAAC7E,KAAK,EAAEyE,IAAI,EAAE;IAAEM,UAAU,EAAE,KAAK;IAAEC,QAAQ,EAAE,IAAI;IAAEC,YAAY,EAAE;EAAK,CAAC,CAAC;EAC7FjF,KAAK,CAACyE,IAAI,CAAC,GAAGK,YAAY;AAC9B;AACA,SAASI,WAAWA,CAACC,MAAM,EAAE1D,KAAK,EAAE2D,KAAK,EAAE;EACvC,IAAIC,SAAS,GAAG,EAAE;EAClB,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtBD,SAAS,CAACxC,OAAO,CAAE0C,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IAC3CF,SAAS,GAAG,EAAE;EAClB,CAAC;EACD,MAAME,QAAQ,GAAGC,OAAO,CAACL,MAAM,EAAE1D,KAAK,EAAE,CAAC;IAAEgE,WAAW;IAAEC;EAAa,CAAC,KAAK;IACvE,CAACD,WAAW,IAAIL,KAAK,CAAC,CAAC;IACvBE,WAAW,CAAC,CAAC;IACb,IAAIhD,QAAQ,CAACoD,YAAY,CAAC,IAAIA,YAAY,CAACpF,WAAW,CAACC,IAAI,KAAK,QAAQ,EAAE;MACtE+C,MAAM,CAACiB,IAAI,CAACmB,YAAY,CAAC,CAAC7C,OAAO,CAAE4B,IAAI,IAAK;QACxCY,SAAS,CAACM,IAAI,CAACT,WAAW,CAACC,MAAM,EAAE,CAAC,GAAG1D,KAAK,EAAEgD,IAAI,CAAC,EAAEW,KAAK,CAAC,CAAC;MAChE,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF,OAAO,MAAM;IACTG,QAAQ,CAACD,WAAW,CAAC,CAAC;IACtBA,WAAW,CAAC,CAAC;EACjB,CAAC;AACL;AACA,SAASE,OAAOA,CAACI,CAAC,EAAEnE,KAAK,EAAE2D,KAAK,EAAE;EAC9B,IAAI,CAACQ,CAAC,CAACC,UAAU,EAAE;IACfxE,gBAAgB,CAACuE,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;EACzC;EACA,IAAIE,MAAM,GAAGF,CAAC;EACd,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEW,CAAC,EAAE,EAAE;IACvC,IAAI,CAACyD,MAAM,CAACrE,KAAK,CAACY,CAAC,CAAC,CAAC,IAAI,CAACC,QAAQ,CAACwD,MAAM,CAACrE,KAAK,CAACY,CAAC,CAAC,CAAC,CAAC,EAAE;MAClDyD,MAAM,CAACrE,KAAK,CAACY,CAAC,CAAC,CAAC,GAAG,OAAO,CAACE,IAAI,CAACd,KAAK,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC3D;IACAyD,MAAM,GAAGA,MAAM,CAACrE,KAAK,CAACY,CAAC,CAAC,CAAC;EAC7B;EACA,MAAM7B,GAAG,GAAGiB,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EACnC,MAAM+C,IAAI,GAAGhD,KAAK,CAAChB,IAAI,CAAC,GAAG,CAAC;EAC5B,IAAI,CAACmF,CAAC,CAACC,UAAU,CAACpB,IAAI,CAAC,EAAE;IACrBmB,CAAC,CAACC,UAAU,CAACpB,IAAI,CAAC,GAAG;MAAEjD,KAAK,EAAEsE,MAAM,CAACtF,GAAG,CAAC;MAAEuF,QAAQ,EAAE;IAAG,CAAC;EAC7D;EACA,MAAMC,KAAK,GAAGJ,CAAC,CAACC,UAAU,CAACpB,IAAI,CAAC;EAChC,IAAIqB,MAAM,CAACtF,GAAG,CAAC,KAAKwF,KAAK,CAACxE,KAAK,EAAE;IAC7BwE,KAAK,CAACxE,KAAK,GAAGsE,MAAM,CAACtF,GAAG,CAAC;EAC7B;EACA,IAAIwF,KAAK,CAACD,QAAQ,CAAChF,OAAO,CAACqE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACtCY,KAAK,CAACD,QAAQ,CAACJ,IAAI,CAACP,KAAK,CAAC;IAC1BA,KAAK,CAAC;MAAEM,YAAY,EAAEM,KAAK,CAACxE,KAAK;MAAEiE,WAAW,EAAE;IAAK,CAAC,CAAC;IACvD,IAAIO,KAAK,CAACD,QAAQ,CAACrE,MAAM,IAAI,CAAC,IAAIY,QAAQ,CAACwD,MAAM,CAAC,EAAE;MAChD,MAAM;QAAEf;MAAW,CAAC,GAAGzB,MAAM,CAACqB,wBAAwB,CAACmB,MAAM,EAAEtF,GAAG,CAAC,IAAI;QAAEuE,UAAU,EAAE;MAAK,CAAC;MAC3FzB,MAAM,CAACuB,cAAc,CAACiB,MAAM,EAAEtF,GAAG,EAAE;QAC/BuE,UAAU;QACVE,YAAY,EAAE,IAAI;QAClBL,GAAG,EAAEA,CAAA,KAAMoB,KAAK,CAACxE,KAAK;QACtByE,GAAG,EAAGP,YAAY,IAAK;UACnB,IAAIA,YAAY,KAAKM,KAAK,CAACxE,KAAK,EAAE;YAC9B,MAAM0E,aAAa,GAAGF,KAAK,CAACxE,KAAK;YACjCwE,KAAK,CAACxE,KAAK,GAAGkE,YAAY;YAC1BM,KAAK,CAACD,QAAQ,CAAClD,OAAO,CAAEsD,QAAQ,IAAKA,QAAQ,CAAC;cAAED,aAAa;cAAER,YAAY;cAAED,WAAW,EAAE;YAAM,CAAC,CAAC,CAAC;UACvG;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA,OAAO;IACHW,QAAQA,CAAC5E,KAAK,EAAE;MACZwE,KAAK,CAACxE,KAAK,GAAGA,KAAK;IACvB,CAAC;IACD8D,WAAWA,CAAA,EAAG;MACVU,KAAK,CAACD,QAAQ,GAAGC,KAAK,CAACD,QAAQ,CAAC1H,MAAM,CAAE8H,QAAQ,IAAKA,QAAQ,KAAKf,KAAK,CAAC;MACxE,IAAIY,KAAK,CAACD,QAAQ,CAACrE,MAAM,KAAK,CAAC,EAAE;QAC7B,OAAOkE,CAAC,CAACC,UAAU,CAACpB,IAAI,CAAC;MAC7B;IACJ;EACJ,CAAC;AACL;AACA,SAAS4B,QAAQA,CAACC,CAAC,EAAE9F,GAAG,EAAE;EACtBA,GAAG,GAAIU,KAAK,CAACC,OAAO,CAACX,GAAG,CAAC,GAAGA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGD,GAAI;EAChD,IAAI,CAAC8F,CAAC,CAACC,UAAU,EAAE;IACf,OAAO1E,SAAS;EACpB;EACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEmE,GAAG,GAAGF,CAAC,CAACC,UAAU,CAAC7E,MAAM,EAAEW,CAAC,GAAGmE,GAAG,EAAEnE,CAAC,EAAE,EAAE;IACrD,MAAM+B,CAAC,GAAGkC,CAAC,CAACC,UAAU,CAAClE,CAAC,CAAC;IACzB,MAAMN,CAAC,GAAIb,KAAK,CAACC,OAAO,CAACiD,CAAC,CAAC5D,GAAG,CAAC,GAAG4D,CAAC,CAAC5D,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG2D,CAAC,CAAC5D,GAAI;IAC1D,IAAIuB,CAAC,KAAKvB,GAAG,EAAE;MACX,OAAO4D,CAAC;IACZ;IACA,IAAIA,CAAC,CAACmC,UAAU,KAAK5F,KAAK,CAACoB,CAAC,CAAC,IAAIvB,GAAG,CAACO,OAAO,CAAE,GAAEgB,CAAE,GAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MAC1D,MAAM/B,KAAK,GAAGqG,QAAQ,CAACjC,CAAC,EAAEzD,KAAK,CAACoB,CAAC,CAAC,GAAGvB,GAAG,GAAGA,GAAG,CAACY,KAAK,CAACW,CAAC,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC;MACnE,IAAI1B,KAAK,EAAE;QACP,OAAOA,KAAK;MAChB;IACJ;EACJ;EACA,OAAO6B,SAAS;AACpB;AACA,SAAS4E,iBAAiBA,CAACzG,KAAK,EAAE;EAC9BA,KAAK,CAAC0G,cAAc,EAAE7D,OAAO,CAAE8D,GAAG,IAAK;IACnC;IACA,IAAIA,GAAG,YAAYpK,YAAY,EAAE;MAC7B,MAAMqK,iBAAiB,GAAGD,GAAG,CAACE,QAAQ,CAACjC,GAAG,CAACpI,iBAAiB,CAAC;MAC7DoK,iBAAiB,CAACE,YAAY,CAAC,CAAC;IACpC,CAAC,MACI;MACDH,GAAG,CAACG,YAAY,CAAC,CAAC;IACtB;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAItK,cAAc,CAAC,eAAe,CAAC;AACzD;AACA;AACA;AACA,MAAMuK,YAAY,CAAC;EACf1G,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2G,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,MAAM,GAAG;MACVC,iBAAiB,EAAE,aAAa;MAChCC,UAAU,EAAE,IAAI;MAChBC,gBAAgB,EAAE,IAAI;MACtBC,wBAAwB,EAAE,IAAI;MAC9BC,SAASA,CAAC1H,KAAK,EAAE;QACb,OAAQA,KAAK,CAAC2H,WAAW,EAAEC,OAAO,KAC7B5H,KAAK,CAAC2H,WAAW,EAAEE,OAAO,IAAI7H,KAAK,CAAC8H,OAAO,CAACC,UAAU,EAAEC,SAAS,IAAI,CAAC,CAAChI,KAAK,CAACA,KAAK,CAACiI,UAAU,EAAEC,IAAI,CAAC;MAC7G;IACJ,CAAC;IACD,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;EAClC;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,IAAIA,MAAM,CAACtB,KAAK,EAAE;MACdsB,MAAM,CAACtB,KAAK,CAACpE,OAAO,CAAE1C,IAAI,IAAK,IAAI,CAACqI,OAAO,CAACrI,IAAI,CAAC,CAAC;IACtD;IACA,IAAIoI,MAAM,CAACrB,UAAU,EAAE;MACnBqB,MAAM,CAACrB,UAAU,CAACrE,OAAO,CAAE4F,SAAS,IAAK,IAAI,CAACC,YAAY,CAACD,SAAS,CAAC,CAAC;IAC1E;IACA,IAAIF,MAAM,CAACpB,QAAQ,EAAE;MACjBoB,MAAM,CAACpB,QAAQ,CAACtE,OAAO,CAAE8F,OAAO,IAAK,IAAI,CAACC,UAAU,CAACD,OAAO,CAAC,CAAC;IAClE;IACA,IAAIJ,MAAM,CAACM,kBAAkB,EAAE;MAC3BN,MAAM,CAACM,kBAAkB,CAAChG,OAAO,CAAEoF,UAAU,IAAK,IAAI,CAACa,mBAAmB,CAACb,UAAU,CAAC1H,IAAI,EAAE0H,UAAU,CAACc,OAAO,CAAC,CAAC;IACpH;IACA,IAAIR,MAAM,CAACJ,UAAU,EAAE;MACnB,IAAI,CAACa,mBAAmB,CAACT,MAAM,CAACJ,UAAU,CAAC;IAC/C;IACA,IAAII,MAAM,CAAClB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,GAAG;QAAE,GAAG,IAAI,CAACA,MAAM;QAAE,GAAGkB,MAAM,CAAClB;MAAO,CAAC;IACtD;IACA,IAAIkB,MAAM,CAACH,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAG;QACX,GAAG,IAAI,CAACA,OAAO;QACf,GAAGG,MAAM,CAACH,OAAO,CAAClG,MAAM,CAAC,CAAC+G,GAAG,EAAEC,IAAI,MAAM;UAAE,GAAGD,GAAG;UAAE,CAACC,IAAI,CAAC3I,IAAI,GAAG2I,IAAI,CAACX;QAAO,CAAC,CAAC,EAAE,CAAC,CAAC;MACtF,CAAC;IACL;EACJ;EACA;AACJ;AACA;AACA;EACIC,OAAOA,CAACV,OAAO,EAAE;IACb,IAAI5G,KAAK,CAACC,OAAO,CAAC2G,OAAO,CAAC,EAAE;MACxBA,OAAO,CAACjF,OAAO,CAAEsG,MAAM,IAAK,IAAI,CAACX,OAAO,CAACW,MAAM,CAAC,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAAC,IAAI,CAAClC,KAAK,CAACa,OAAO,CAACvH,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC0G,KAAK,CAACa,OAAO,CAACvH,IAAI,CAAC,GAAG;UAAEA,IAAI,EAAEuH,OAAO,CAACvH;QAAK,CAAC;MACrD;MACA,CAAC,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAACsC,OAAO,CAAE4B,IAAI,IAAK;QACrE,IAAIqD,OAAO,CAACsB,cAAc,CAAC3E,IAAI,CAAC,EAAE;UAC9B,IAAI,CAACwC,KAAK,CAACa,OAAO,CAACvH,IAAI,CAAC,CAACkE,IAAI,CAAC,GAAGqD,OAAO,CAACrD,IAAI,CAAC;QAClD;MACJ,CAAC,CAAC;IACN;EACJ;EACA4E,OAAOA,CAAC9I,IAAI,EAAE+I,eAAe,GAAG,KAAK,EAAE;IACnC,IAAI/I,IAAI,YAAYlE,IAAI,EAAE;MACtB,OAAO;QAAEkN,SAAS,EAAEhJ,IAAI;QAAEA,IAAI,EAAEA,IAAI,CAACF,SAAS,CAACC,WAAW,CAACC;MAAK,CAAC;IACrE;IACA,IAAI,CAAC,IAAI,CAAC0G,KAAK,CAAC1G,IAAI,CAAC,EAAE;MACnB,IAAI+I,eAAe,EAAE;QACjB,MAAM,IAAIE,KAAK,CAAE,4BAA2BjJ,IAAK,iGAAgG,CAAC;MACtJ;MACA,OAAO,IAAI;IACf;IACA,IAAI,CAACkJ,iBAAiB,CAAClJ,IAAI,CAAC;IAC5B,OAAO,IAAI,CAAC0G,KAAK,CAAC1G,IAAI,CAAC;EAC3B;EACA;EACAmJ,cAAcA,CAAC1J,KAAK,GAAG,CAAC,CAAC,EAAE;IACvB,MAAMG,IAAI,GAAG,IAAI,CAACkJ,OAAO,CAACrJ,KAAK,CAACG,IAAI,CAAC;IACrC,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACwJ,cAAc,EAAE;MACrBjH,gBAAgB,CAAC1C,KAAK,EAAEG,IAAI,CAACwJ,cAAc,CAAC;IAChD;IACA,MAAMC,cAAc,GAAGzJ,IAAI,CAAC0J,OAAO,IAAI,IAAI,CAACR,OAAO,CAAClJ,IAAI,CAAC0J,OAAO,CAAC,CAACF,cAAc;IAChF,IAAIC,cAAc,EAAE;MAChBlH,gBAAgB,CAAC1C,KAAK,EAAE4J,cAAc,CAAC;IAC3C;IACA,IAAI5J,KAAK,EAAE8J,YAAY,EAAE;MACrB9J,KAAK,CAAC8J,YAAY,CAACjH,OAAO,CAAEsG,MAAM,IAAK;QACnC,MAAMQ,cAAc,GAAG,IAAI,CAACN,OAAO,CAACF,MAAM,CAAC,CAACQ,cAAc;QAC1D,IAAIA,cAAc,EAAE;UAChBjH,gBAAgB,CAAC1C,KAAK,EAAE2J,cAAc,CAAC;QAC3C;MACJ,CAAC,CAAC;IACN;IACA,MAAMI,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAChK,KAAK,CAAC;IACpD,IAAI+J,YAAY,EAAEE,QAAQ,EAAEN,cAAc,EAAE;MACxCjH,gBAAgB,CAAC1C,KAAK,EAAE+J,YAAY,CAACE,QAAQ,CAACN,cAAc,CAAC;IACjE;IACA,IAAI,CAAC3J,KAAK,CAACmH,QAAQ,IAAIhH,IAAI,CAACgH,QAAQ,EAAE;MAClCnH,KAAK,CAACmH,QAAQ,GAAG,CAAC,GAAGhH,IAAI,CAACgH,QAAQ,CAAC;IACvC;EACJ;EACA;EACA6C,mBAAmBA,CAAChK,KAAK,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMG,IAAI,GAAG,IAAI,CAACkJ,OAAO,CAACrJ,KAAK,CAACG,IAAI,CAAC;IACrC,IAAI,CAACA,IAAI,EAAE;MACP,OAAO,IAAI;IACf;IACA,IAAI,CAACA,IAAI,CAACoJ,SAAS,IAAIpJ,IAAI,CAAC+J,aAAa,EAAE;MACvC,OAAO/J,IAAI,CAAC+J,aAAa;IAC7B;IACA,MAAM;MAAEC,iBAAiB;MAAEC;IAAU,CAAC,GAAGpK,KAAK,CAAC8H,OAAO;IACtD,IAAI,CAACqC,iBAAiB,IAAI,CAACC,SAAS,EAAE;MAClC,OAAO,IAAI;IACf;IACA,MAAML,YAAY,GAAGI,iBAAiB,CAACE,eAAe,CAAClK,IAAI,CAACoJ,SAAS,EAAE;MAAE1C,QAAQ,EAAEuD;IAAU,CAAC,CAAC;IAC/F/I,gBAAgB,CAAClB,IAAI,EAAE,eAAe,EAAE4J,YAAY,CAAC;IACrD,IAAI;MACAA,YAAY,CAACO,OAAO,CAAC,CAAC;IAC1B,CAAC,CACD,OAAOC,CAAC,EAAE;MACNC,OAAO,CAACC,KAAK,CAAE,iEAAgEzK,KAAK,CAACG,IAAK,GAAE,EAAEoK,CAAC,CAAC;IACpG;IACA,OAAOpK,IAAI,CAAC+J,aAAa;EAC7B;EACAtB,UAAUA,CAACd,OAAO,EAAE;IAChB,IAAI,CAACX,QAAQ,CAACW,OAAO,CAACvH,IAAI,CAAC,GAAGuH,OAAO;IACrC,IAAIA,OAAO,CAACb,KAAK,EAAE;MACfa,OAAO,CAACb,KAAK,CAACpE,OAAO,CAAE1C,IAAI,IAAK;QAC5B,IAAI,CAACuK,cAAc,CAACvK,IAAI,EAAE2H,OAAO,CAACvH,IAAI,CAAC;MAC3C,CAAC,CAAC;IACN;EACJ;EACAoK,UAAUA,CAACpK,IAAI,EAAE;IACb,IAAIA,IAAI,YAAYlE,IAAI,EAAE;MACtB,OAAO;QAAEkN,SAAS,EAAEhJ,IAAI;QAAEA,IAAI,EAAEA,IAAI,CAACF,SAAS,CAACC,WAAW,CAACC;MAAK,CAAC;IACrE;IACA,IAAI,CAAC,IAAI,CAAC4G,QAAQ,CAAC5G,IAAI,CAAC,EAAE;MACtB,MAAM,IAAIiJ,KAAK,CAAE,+BAA8BjJ,IAAK,iGAAgG,CAAC;IACzJ;IACA,OAAO,IAAI,CAAC4G,QAAQ,CAAC5G,IAAI,CAAC;EAC9B;EACA;EACAmK,cAAcA,CAACvK,IAAI,EAAEI,IAAI,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC0G,KAAK,CAAC9G,IAAI,CAAC,EAAE;MACnB,IAAI,CAAC8G,KAAK,CAAC9G,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB;IACA,IAAI,CAAC,IAAI,CAAC8G,KAAK,CAAC9G,IAAI,CAAC,CAACgH,QAAQ,EAAE;MAC5B,IAAI,CAACF,KAAK,CAAC9G,IAAI,CAAC,CAACgH,QAAQ,GAAG,EAAE;IAClC;IACA,IAAI,IAAI,CAACF,KAAK,CAAC9G,IAAI,CAAC,CAACgH,QAAQ,CAACpG,OAAO,CAACR,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MAChD,IAAI,CAAC0G,KAAK,CAAC9G,IAAI,CAAC,CAACgH,QAAQ,CAACxB,IAAI,CAACpF,IAAI,CAAC;IACxC;EACJ;EACAmI,YAAYA,CAACZ,OAAO,EAAE;IAClB,IAAI,CAACZ,UAAU,CAACY,OAAO,CAACvH,IAAI,CAAC,GAAGuH,OAAO;EAC3C;EACA8C,YAAYA,CAACrK,IAAI,EAAE;IACf,IAAI,CAAC,IAAI,CAAC2G,UAAU,CAAC3G,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIiJ,KAAK,CAAE,iCAAgCjJ,IAAK,iGAAgG,CAAC;IAC3J;IACA,OAAO,IAAI,CAAC2G,UAAU,CAAC3G,IAAI,CAAC;EAChC;EACAuI,mBAAmBA,CAACvI,IAAI,EAAEwI,OAAO,EAAE;IAC/B,IAAI,CAAC3B,QAAQ,CAAC7G,IAAI,CAAC,GAAGwI,OAAO;IAC7B,IAAI,OAAO8B,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMC,UAAU,GAAG;QAAEC,SAAS,EAAE,WAAW;QAAEC,SAAS,EAAE;MAAY,CAAC;MACrE,IAAIF,UAAU,CAACvK,IAAI,CAAC,EAAE;QAClBiK,OAAO,CAACS,IAAI,CAAE,wDAAuD1K,IAAK,oCAAmCuK,UAAU,CAACvK,IAAI,CAAE,YAAW,CAAC;QAC1I,IAAI,CAAC6G,QAAQ,CAAC0D,UAAU,CAACvK,IAAI,CAAC,CAAC,GAAGwI,OAAO;MAC7C;IACJ;EACJ;EACAmC,mBAAmBA,CAAC3K,IAAI,EAAE;IACtB,OAAO,IAAI,CAAC6G,QAAQ,CAAC7G,IAAI,CAAC;EAC9B;EACAyI,mBAAmBA,CAACmC,gBAAgB,EAAE;IAClC;IACAA,gBAAgB,CAACtI,OAAO,CAAEuI,eAAe,IAAK;MAC1C,MAAMC,QAAQ,GAAGD,eAAe,CAACC,QAAQ,IAAI,CAAC;MAC9C,IAAI,CAAChD,oBAAoB,CAACgD,QAAQ,CAAC,GAAG;QAClC,GAAG,IAAI,CAAChD,oBAAoB,CAACgD,QAAQ,CAAC;QACtC,CAACD,eAAe,CAAC7K,IAAI,GAAG6K,eAAe,CAACE;MAC5C,CAAC;IACL,CAAC,CAAC;IACF;IACA,IAAI,CAACnD,UAAU,GAAG7E,MAAM,CAACiB,IAAI,CAAC,IAAI,CAAC8D,oBAAoB,CAAC,CACnD5J,GAAG,CAAC8M,MAAM,CAAC,CACXC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACrBxJ,MAAM,CAAC,CAAC+G,GAAG,EAAE0C,IAAI,MAAM;MACxB,GAAG1C,GAAG;MACN,GAAG,IAAI,CAACZ,oBAAoB,CAACsD,IAAI;IACrC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX;EACAlC,iBAAiBA,CAAClJ,IAAI,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC0G,KAAK,CAAC1G,IAAI,CAAC,CAACsJ,OAAO,EAAE;MAC3B;IACJ;IACA,MAAM+B,YAAY,GAAG,IAAI,CAACvC,OAAO,CAAC,IAAI,CAACpC,KAAK,CAAC1G,IAAI,CAAC,CAACsJ,OAAO,CAAC;IAC3D,IAAI,CAAC,IAAI,CAAC5C,KAAK,CAAC1G,IAAI,CAAC,CAACgJ,SAAS,EAAE;MAC7B,IAAI,CAACtC,KAAK,CAAC1G,IAAI,CAAC,CAACgJ,SAAS,GAAGqC,YAAY,CAACrC,SAAS;IACvD;IACA,IAAI,CAAC,IAAI,CAACtC,KAAK,CAAC1G,IAAI,CAAC,CAAC4G,QAAQ,EAAE;MAC5B,IAAI,CAACF,KAAK,CAAC1G,IAAI,CAAC,CAAC4G,QAAQ,GAAGyE,YAAY,CAACzE,QAAQ;IACrD;EACJ;AACJ;AACAH,YAAY,CAAC6E,IAAI,YAAAC,qBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyF/E,YAAY;AAAA,CAAoD;AAC1KA,YAAY,CAACgF,KAAK,kBAD8E5P,EAAE,CAAA6P,kBAAA;EAAAC,KAAA,EACYlF,YAAY;EAAAmF,OAAA,EAAZnF,YAAY,CAAA6E,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACjJ;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KAFgGzO,EAAE,CAAAiQ,iBAAA,CAENrF,YAAY,EAAc,CAAC;IAC3G7G,IAAI,EAAEzD,UAAU;IAChBkG,IAAI,EAAE,CAAC;MAAEwJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,MAAME,iBAAiB,CAAC;EACpBhM,WAAWA,CAACiI,MAAM,EAAE1B,QAAQ,EAAE0F,gBAAgB,EAAExE,UAAU,EAAE;IACxD,IAAI,CAACQ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC0F,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACxE,UAAU,GAAGA,UAAU;EAChC;EACAyE,SAASA,CAAC9M,IAAI,EAAE6G,UAAU,GAAG,EAAE,EAAEpE,KAAK,EAAE2F,OAAO,EAAE;IAC7C,IAAI,CAAC2E,KAAK,CAAC;MAAElG,UAAU;MAAEpE,KAAK;MAAEzC,IAAI;MAAEoI;IAAQ,CAAC,CAAC;EACpD;EACA2E,KAAKA,CAACzM,KAAK,EAAE;IACT,IAAI,CAAC,IAAI,CAACuI,MAAM,CAACJ,UAAU,CAACuE,IAAI,EAAE;MAC9B,MAAM,IAAIlD,KAAK,CAAC,2FAA2F,CAAC;IAChH;IACA,IAAI,CAACxJ,KAAK,CAAC4B,MAAM,EAAE;MACf,IAAI,CAAC+K,WAAW,CAAC3M,KAAK,CAAC;MACvBP,uBAAuB,CAACO,KAAK,CAACN,IAAI,EAAE,MAAM;QACtC,IAAI,CAACkN,MAAM,CAAC5M,KAAK,CAAC;QAClB,MAAM8H,OAAO,GAAG9H,KAAK,CAAC8H,OAAO;QAC7BA,OAAO,CAAC+E,gBAAgB,GAAG7M,KAAK,EAAE,IAAI,CAAC;QACvC8H,OAAO,CAACgF,aAAa,GAAG9M,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC4M,MAAM,CAAC5M,KAAK,CAAC;IACtB;EACJ;EACA4M,MAAMA,CAAC5M,KAAK,EAAE;IACV,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,MAAMmI,UAAU,GAAG7E,MAAM,CAACyJ,MAAM,CAAC,IAAI,CAACxE,MAAM,CAACJ,UAAU,CAAC;IACxDA,UAAU,CAACtF,OAAO,CAAEyI,SAAS,IAAKA,SAAS,CAAC0B,WAAW,GAAGhN,KAAK,CAAC,CAAC;IACjEmI,UAAU,CAACtF,OAAO,CAAEyI,SAAS,IAAKA,SAAS,CAAC2B,UAAU,GAAGjN,KAAK,CAAC,CAAC;IAChEA,KAAK,CAACuG,UAAU,EAAE1D,OAAO,CAAEyD,CAAC,IAAK,IAAI,CAACsG,MAAM,CAACtG,CAAC,CAAC,CAAC;IAChD6B,UAAU,CAACtF,OAAO,CAAEyI,SAAS,IAAKA,SAAS,CAAC4B,YAAY,GAAGlN,KAAK,CAAC,CAAC;EACtE;EACA2M,WAAWA,CAAC3M,KAAK,EAAE;IACfA,KAAK,CAACN,IAAI,GAAGM,KAAK,CAACN,IAAI,IAAI,IAAIjC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5CuC,KAAK,CAACmC,KAAK,GAAGnC,KAAK,CAACmC,KAAK,IAAI,CAAC,CAAC;IAC/BnC,KAAK,CAAC8H,OAAO,GAAG9H,KAAK,CAAC8H,OAAO,IAAI,CAAC,CAAC;IACnC,MAAMA,OAAO,GAAG9H,KAAK,CAAC8H,OAAO;IAC7B,IAAI,CAACA,OAAO,CAACqC,iBAAiB,EAAE;MAC5B9I,gBAAgB,CAACyG,OAAO,EAAE,mBAAmB,EAAE,IAAI,CAACyE,gBAAgB,CAAC;IACzE;IACA,IAAI,CAACzE,OAAO,CAACsC,SAAS,EAAE;MACpB/I,gBAAgB,CAACyG,OAAO,EAAE,WAAW,EAAE,IAAI,CAACjB,QAAQ,CAAC;IACzD;IACA,IAAI,CAACiB,OAAO,CAAC2E,KAAK,EAAE;MAChB3E,OAAO,CAACqF,UAAU,GAAG,MAAM;QACvB3C,OAAO,CAACS,IAAI,CAAE,qFAAoF,CAAC;QACnG,IAAI,CAACwB,KAAK,CAACzM,KAAK,CAAC;MACrB,CAAC;MACD8H,OAAO,CAAC2E,KAAK,GAAG,CAACnG,CAAC,GAAGtG,KAAK,KAAK;QAC3B,IAAI,CAACyM,KAAK,CAACnG,CAAC,CAAC;QACb,OAAOA,CAAC;MACZ,CAAC;IACL;IACA,IAAI,CAACwB,OAAO,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,EAAE;MACxC1G,gBAAgB,CAACyG,OAAO,EAAE,YAAY,EAAE,IAAI,CAACC,UAAU,CAAC;MACxDvC,OAAO,CAACsC,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,CAAC;QAAErC;MAAY,CAAC,KAAK;QAC/D,IAAI,CAACA,WAAW,EAAE;UACdqC,OAAO,CAAC+E,gBAAgB,CAAC7M,KAAK,CAAC;UAC/B8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC;QAChC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACAsM,iBAAiB,CAACT,IAAI,YAAAuB,0BAAArB,CAAA;EAAA,YAAAA,CAAA,IAAyFO,iBAAiB,EA5EhClQ,EAAE,CAAAiR,QAAA,CA4EgDrG,YAAY,GA5E9D5K,EAAE,CAAAiR,QAAA,CA4EyEjR,EAAE,CAACkR,QAAQ,GA5EtFlR,EAAE,CAAAiR,QAAA,CA4EiGjR,EAAE,CAACU,gBAAgB,MA5EtHV,EAAE,CAAAiR,QAAA,CA4EiJ9P,EAAE,CAACgQ,kBAAkB;AAAA,CAA6D;AACrUjB,iBAAiB,CAACN,KAAK,kBA7EyE5P,EAAE,CAAA6P,kBAAA;EAAAC,KAAA,EA6EiBI,iBAAiB;EAAAH,OAAA,EAAjBG,iBAAiB,CAAAT,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC3J;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KA9EgGzO,EAAE,CAAAiQ,iBAAA,CA8ENC,iBAAiB,EAAc,CAAC;IAChHnM,IAAI,EAAEzD,UAAU;IAChBkG,IAAI,EAAE,CAAC;MAAEwJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjM,IAAI,EAAE6G;IAAa,CAAC,EAAE;MAAE7G,IAAI,EAAE/D,EAAE,CAACkR;IAAS,CAAC,EAAE;MAAEnN,IAAI,EAAE/D,EAAE,CAACU,gBAAgB;MAAE0Q,UAAU,EAAE,CAAC;QACvHrN,IAAI,EAAExD;MACV,CAAC;IAAE,CAAC,EAAE;MAAEwD,IAAI,EAAE5C,EAAE,CAACgQ,kBAAkB;MAAEC,UAAU,EAAE,CAAC;QAC9CrN,IAAI,EAAExD;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,SAAS8Q,iBAAiBA,CAACzN,KAAK,EAAE0N,SAAS,GAAG,KAAK,EAAE;EACjD,MAAMC,OAAO,GAAG3N,KAAK,CAAC2H,WAAW;EACjC,MAAMiG,UAAU,GAAGD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACE,OAAO,CAAC9M,OAAO,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC;EACxE,IAAI4N,UAAU,KAAK,CAAC,CAAC,EAAE;IACnBD,OAAO,CAACE,OAAO,CAACC,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC;EACzC;EACA,MAAMlO,IAAI,GAAGiO,OAAO,CAAC/L,MAAM;EAC3B,IAAI,CAAClC,IAAI,EAAE;IACP;EACJ;EACA,MAAMqO,IAAI,GAAG;IAAEL;EAAU,CAAC;EAC1B,IAAIhO,IAAI,YAAYhC,SAAS,EAAE;IAC3B,MAAM8C,GAAG,GAAGd,IAAI,CAACsO,QAAQ,CAACC,SAAS,CAAE7J,CAAC,IAAKA,CAAC,KAAKuJ,OAAO,CAAC;IACzD,IAAInN,GAAG,KAAK,CAAC,CAAC,EAAE;MACZd,IAAI,CAACwO,QAAQ,CAAC1N,GAAG,EAAEuN,IAAI,CAAC;IAC5B;EACJ,CAAC,MACI,IAAIrO,IAAI,YAAYjC,SAAS,EAAE;IAChC,MAAMgE,KAAK,GAAGb,UAAU,CAACZ,KAAK,CAAC;IAC/B,MAAMQ,GAAG,GAAGiB,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;IACnC,IAAIhC,IAAI,CAACkF,GAAG,CAAC,CAACpE,GAAG,CAAC,CAAC,KAAKmN,OAAO,EAAE;MAC7BjO,IAAI,CAACyO,aAAa,CAAC3N,GAAG,EAAEuN,IAAI,CAAC;IACjC;EACJ;EACAJ,OAAO,CAACS,SAAS,CAAC,IAAI,CAAC;AAC3B;AACA,SAASC,WAAWA,CAACrO,KAAK,EAAE;EACxB,IAAIA,KAAK,CAAC2H,WAAW,EAAE;IACnB,OAAO3H,KAAK,CAAC2H,WAAW;EAC5B;EACA,IAAI3H,KAAK,CAACsO,gBAAgB,KAAK,KAAK,EAAE;IAClC,OAAO,IAAI;EACf;EACA,OAAOtO,KAAK,CAACN,IAAI,EAAEkF,GAAG,CAAChE,UAAU,CAACZ,KAAK,CAAC,CAAC;AAC7C;AACA,SAASuO,eAAeA,CAACvO,KAAK,EAAE2N,OAAO,EAAED,SAAS,GAAG,KAAK,EAAE;EACxDC,OAAO,GAAGA,OAAO,IAAI3N,KAAK,CAAC2H,WAAW;EACtC,IAAI,CAACgG,OAAO,CAACE,OAAO,EAAE;IAClBxM,gBAAgB,CAACsM,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;EAC5C;EACA,IAAIA,OAAO,CAACE,OAAO,CAAC9M,OAAO,CAACf,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACvC2N,OAAO,CAACE,OAAO,CAAClI,IAAI,CAAC3F,KAAK,CAAC;EAC/B;EACA,IAAI,CAACA,KAAK,CAAC2H,WAAW,IAAIgG,OAAO,EAAE;IAC/BtM,gBAAgB,CAACrB,KAAK,EAAE,aAAa,EAAE2N,OAAO,CAAC;IAC/CA,OAAO,CAACa,aAAa,CAAC,IAAI,CAAC;IAC3Bb,OAAO,CAACc,kBAAkB,CAAC,IAAI,CAAC;IAChCzO,KAAK,CAAC0O,KAAK,CAACC,QAAQ,GAAG,CAAC,CAAC3O,KAAK,CAAC0O,KAAK,CAACC,QAAQ;IAC7C,MAAMC,gBAAgB,GAAGpJ,OAAO,CAACxF,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;MAAEyF,WAAW;MAAEC;IAAa,CAAC,KAAK;MAC9F,IAAI,CAACD,WAAW,EAAE;QACdC,YAAY,GAAG1F,KAAK,CAAC2H,WAAW,CAACkH,OAAO,CAAC,CAAC,GAAG7O,KAAK,CAAC2H,WAAW,CAACmH,MAAM,CAAC,CAAC;MAC3E;IACJ,CAAC,CAAC;IACF,IAAInB,OAAO,YAAYhQ,WAAW,EAAE;MAChCgQ,OAAO,CAACoB,wBAAwB,CAACH,gBAAgB,CAACxI,QAAQ,CAAC;IAC/D;EACJ;EACA,IAAI,CAACpG,KAAK,CAACN,IAAI,IAAI,CAACgB,MAAM,CAACV,KAAK,CAAC,EAAE;IAC/B;EACJ;EACA,IAAIN,IAAI,GAAGM,KAAK,CAACN,IAAI;EACrB,MAAM+B,KAAK,GAAGb,UAAU,CAACZ,KAAK,CAAC;EAC/B,MAAMwB,KAAK,GAAGiB,aAAa,CAACzC,KAAK,CAAC;EAClC,IAAI,EAAEW,KAAK,CAACgN,OAAO,CAACnM,KAAK,CAAC,IAAIb,KAAK,CAACa,KAAK,CAAC,CAAC,IAAImM,OAAO,CAACnM,KAAK,KAAKA,KAAK,IAAImM,OAAO,YAAYhQ,WAAW,EAAE;IACtGgQ,OAAO,CAACqB,UAAU,CAACxN,KAAK,CAAC;EAC7B;EACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEW,CAAC,EAAE,EAAE;IACvC,MAAMvB,IAAI,GAAGW,KAAK,CAACY,CAAC,CAAC;IACrB,IAAI,CAAC3C,IAAI,CAACkF,GAAG,CAAC,CAAC9D,IAAI,CAAC,CAAC,EAAE;MACnBpB,IAAI,CAACuP,UAAU,CAACnO,IAAI,EAAE,IAAIrD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAAEiQ;MAAU,CAAC,CAAC;IAC3D;IACAhO,IAAI,GAAGA,IAAI,CAACkF,GAAG,CAAC,CAAC9D,IAAI,CAAC,CAAC;EAC3B;EACA,MAAMN,GAAG,GAAGiB,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EACnC,IAAI,CAAC1B,KAAK,CAACkP,KAAK,IAAIxP,IAAI,CAACkF,GAAG,CAAC,CAACpE,GAAG,CAAC,CAAC,KAAKmN,OAAO,EAAE;IAC7CjO,IAAI,CAACuP,UAAU,CAACzO,GAAG,EAAEmN,OAAO,EAAE;MAAED;IAAU,CAAC,CAAC;EAChD;AACJ;AACA,SAASyB,cAAcA,CAAC/K,CAAC,EAAEgL,QAAQ,GAAG,KAAK,EAAE;EACzC,MAAMC,MAAM,GAAGjL,CAAC,CAACiL,MAAM;EACvB,MAAM7N,KAAK,GAAG4C,CAAC,CAAC5C,KAAK;EACrB4C,CAAC,CAACkL,sBAAsB,CAAC;IAAE5B,SAAS,EAAE,KAAK;IAAE0B;EAAS,CAAC,CAAC;EACxD,IAAIC,MAAM,KAAKjL,CAAC,CAACiL,MAAM,EAAE;IACrBjL,CAAC,CAACmL,aAAa,CAACC,IAAI,CAACpL,CAAC,CAACiL,MAAM,CAAC;EAClC;EACA,IAAI7N,KAAK,KAAK4C,CAAC,CAAC5C,KAAK,EAAE;IACnB4C,CAAC,CAACqL,YAAY,CAACD,IAAI,CAACpL,CAAC,CAAC5C,KAAK,CAAC;EAChC;AACJ;AACA,SAASkO,YAAYA,CAAChQ,IAAI,EAAE;EACxB,OAAOA,IAAI,EAAEmO,OAAO;EACpBnO,IAAI,CAAC8O,aAAa,CAAC,IAAI,CAAC;EACxB9O,IAAI,CAAC+O,kBAAkB,CAAC,IAAI,CAAC;EAC7B,IAAI/O,IAAI,YAAYjC,SAAS,IAAIiC,IAAI,YAAYhC,SAAS,EAAE;IACxD4F,MAAM,CAACyJ,MAAM,CAACrN,IAAI,CAACsO,QAAQ,CAAC,CAACnL,OAAO,CAAEuB,CAAC,IAAKsL,YAAY,CAACtL,CAAC,CAAC,CAAC;EAChE;AACJ;AAEA,MAAMuL,cAAc,CAAC;EACjBrP,WAAWA,CAACqG,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;EAClB;EACAiJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrP,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,cAAc;EAC3C;AACJ;AACAoP,cAAc,CAAC9D,IAAI,YAAAgE,uBAAA9D,CAAA;EAAA,YAAAA,CAAA,IAAyF4D,cAAc,EAjM1BvT,EAAE,CAAA0T,iBAAA,CAiM0C1T,EAAE,CAACE,WAAW;AAAA,CAA4C;AACtMqT,cAAc,CAACI,IAAI,kBAlM6E3T,EAAE,CAAA4T,iBAAA;EAAA7P,IAAA,EAkMFwP,cAAc;EAAAM,SAAA;EAAAC,MAAA;IAAA3P,IAAA;EAAA;EAAA4P,QAAA,GAlMd/T,EAAE,CAAAgU,oBAAA;AAAA,EAkM8H;AAChO;EAAA,QAAAvF,SAAA,oBAAAA,SAAA,KAnMgGzO,EAAE,CAAAiQ,iBAAA,CAmMNsD,cAAc,EAAc,CAAC;IAC7GxP,IAAI,EAAEvD,SAAS;IACfgG,IAAI,EAAE,CAAC;MAAEyN,QAAQ,EAAE;IAAmB,CAAC;EAC3C,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElQ,IAAI,EAAE/D,EAAE,CAACE;IAAY,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEiE,IAAI,EAAE,CAAC;MACzFJ,IAAI,EAAEtD,KAAK;MACX+F,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM0N,oBAAoB,CAAC;AAE3BA,oBAAoB,CAACzE,IAAI,YAAA0E,6BAAAxE,CAAA;EAAA,YAAAA,CAAA,IAAyFuE,oBAAoB;AAAA,CAAoD;AAC1LA,oBAAoB,CAACtE,KAAK,kBA9MsE5P,EAAE,CAAA6P,kBAAA;EAAAC,KAAA,EA8MoBoE,oBAAoB;EAAAnE,OAAA,EAApBmE,oBAAoB,CAAAzE;AAAA,EAAG;AAC7I;EAAA,QAAAhB,SAAA,oBAAAA,SAAA,KA/MgGzO,EAAE,CAAAiQ,iBAAA,CA+MNiE,oBAAoB,EAAc,CAAC;IACnHnQ,IAAI,EAAEzD;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAM8T,WAAW,CAAC;EACdlQ,WAAWA,CAACiI,MAAM,EAAEkI,QAAQ,EAAEC,WAAW,EAAEC,gBAAgB,EAAEjR,IAAI,EAAE;IAC/D,IAAI,CAAC6I,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACjR,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACkR,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,uBAAuB,GAAG,MAAM,CAAE,CAAC;EAC5C;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC1I,MAAM,CAAClB,MAAM,CAACI,wBAAwB,GAAG,IAAI,CAAC8E,gBAAgB,GAAG,IAAI,CAACoE,gBAAgB;EACtG;EACA,IAAIO,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC3I,MAAM,CAAClB,MAAM,CAACI,wBAAwB,EAAE;MAC7C,OAAO,IAAI,CAACiJ,WAAW;IAC3B;IACA,IAAI,IAAI,CAACG,aAAa,GAAG,CAAC,CAAC,YAAYtU,YAAY,EAAE;MACjD,OAAO,IAAI,CAACsU,aAAa,CAAC,CAAC,CAAC,CAACM,QAAQ;IACzC;IACA,OAAO,IAAI;EACf;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,WAAW,CAAC,kBAAkB,CAAC;EACxC;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,WAAW,CAAC,eAAe,CAAC;EACrC;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAAC/Q,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC8H,OAAO,EAAE;MAC3D,IAAI,CAAC0J,MAAM,CAAC,CAAC;IACjB;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACJ,WAAW,CAAC,QAAQ,CAAC;EAC9B;EACAzB,WAAWA,CAAC8B,OAAO,EAAE;IACjB,IAAI,CAACL,WAAW,CAAC,WAAW,EAAEK,OAAO,CAAC;EAC1C;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC5R,KAAK,CAAC;IAC1B,IAAI,CAAC4Q,aAAa,CAAC/N,OAAO,CAAEgP,YAAY,IAAKA,YAAY,CAACvM,WAAW,CAAC,CAAC,CAAC;IACxE,IAAI,CAACwL,cAAc,CAACjO,OAAO,CAAEyC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC;IAC3D,IAAI,CAAC0L,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACK,WAAW,CAAC,WAAW,CAAC;EACjC;EACAS,WAAWA,CAACb,YAAY,EAAE3K,CAAC,EAAEa,QAAQ,GAAG,EAAE,EAAE;IACxC,IAAI,IAAI,CAAC8J,YAAY,KAAKA,YAAY,EAAE;MACpC,IAAI,CAACW,SAAS,CAAC,IAAI,CAAC5R,KAAK,CAAC;MAC1B,IAAI,CAACiR,YAAY,CAACc,KAAK,CAAC,CAAC;MACzB5K,QAAQ,GAAG,IAAI,CAACnH,KAAK,EAAEmH,QAAQ;IACnC;IACA,IAAIA,QAAQ,EAAEzF,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM,CAACiH,OAAO,EAAE,GAAGqJ,GAAG,CAAC,GAAG7K,QAAQ;MAClC,MAAM;QAAEoC;MAAU,CAAC,GAAG,IAAI,CAAChB,MAAM,CAACoC,UAAU,CAAChC,OAAO,CAAC;MACrD,MAAMhC,GAAG,GAAGsK,YAAY,CAAC5G,eAAe,CAACd,SAAS,CAAC;MACnD,IAAI,CAAC0I,kBAAkB,CAACtL,GAAG,EAAEL,CAAC,CAAC;MAC/Bd,OAAO,CAACmB,GAAG,CAACsD,QAAQ,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAAEvE,YAAY;QAAEQ,aAAa;QAAET;MAAY,CAAC,KAAK;QACxF,IAAIC,YAAY,EAAE;UACd,IAAIQ,aAAa,IAAIA,aAAa,CAACgM,WAAW,KAAKxM,YAAY,CAACwM,WAAW,EAAE;YACzE;UACJ;UACA,MAAMC,OAAO,GAAGjM,aAAa,GAAGA,aAAa,CAACkM,MAAM,CAAC,CAAC,GAAG,IAAI;UAC7D,IAAID,OAAO,IAAI,CAACA,OAAO,CAACE,SAAS,EAAE;YAC/B3M,YAAY,CAAC4M,MAAM,CAACH,OAAO,CAAC;UAChC,CAAC,MACI;YACD,IAAI,CAACL,WAAW,CAACpM,YAAY,EAAEY,CAAC,EAAE0L,GAAG,CAAC;UAC1C;UACA,CAACvM,WAAW,IAAIkB,GAAG,CAACC,iBAAiB,CAACkG,aAAa,CAAC,CAAC;QACzD;MACJ,CAAC,CAAC;IACN,CAAC,MACI,IAAIxG,CAAC,EAAEnG,IAAI,EAAE;MACd,MAAMoS,UAAU,GAAG,IAAI,CAAC7S,IAAI,EAAE8S,SAAS,EAAEC,IAAI,CAAE9L,GAAG,IAAKA,GAAG,CAACpG,IAAI,KAAK+F,CAAC,CAACnG,IAAI,CAAC;MAC3E,IAAIwG,GAAG;MACP,IAAI4L,UAAU,EAAE;QACZ5L,GAAG,GAAGsK,YAAY,CAACyB,kBAAkB,CAACH,UAAU,CAAC5L,GAAG,EAAE;UAAEtH,SAAS,EAAEiH;QAAE,CAAC,CAAC;MAC3E,CAAC,MACI;QACD,MAAM;UAAEiD;QAAU,CAAC,GAAG,IAAI,CAAChB,MAAM,CAACc,OAAO,CAAC/C,CAAC,CAACnG,IAAI,EAAE,IAAI,CAAC;QACvDwG,GAAG,GAAGsK,YAAY,CAAC5G,eAAe,CAACd,SAAS,CAAC;MACjD;MACA,IAAI,CAAC0I,kBAAkB,CAACtL,GAAG,EAAEL,CAAC,CAAC;IACnC;EACJ;EACA+K,WAAWA,CAAC9Q,IAAI,EAAEmR,OAAO,EAAE;IACvB,IAAInR,IAAI,KAAK,QAAQ,IAAKA,IAAI,KAAK,WAAW,IAAImR,OAAO,CAAC1R,KAAK,IAAI,CAAC0R,OAAO,CAAC1R,KAAK,CAACyF,WAAY,EAAE;MAC5F,IAAI,CAACuL,uBAAuB,GAAG,IAAI,CAAC2B,YAAY,CAAC,IAAI,CAAC3S,KAAK,CAAC;IAChE;IACA,IAAI,IAAI,CAACA,KAAK,EAAE4S,KAAK,GAAGrS,IAAI,CAAC,EAAE;MAC3B,IAAI,CAACmR,OAAO,IAAIA,OAAO,CAAC1R,KAAK,EAAE;QAC3B,MAAM6S,CAAC,GAAG,IAAI,CAAC7S,KAAK,CAAC4S,KAAK,CAACrS,IAAI,CAAC,CAAC,IAAI,CAACP,KAAK,CAAC;QAC5C,IAAInC,YAAY,CAACgV,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,EAAE,eAAe,CAAC,CAAC9R,OAAO,CAACR,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;UACzF,MAAMuS,GAAG,GAAGD,CAAC,CAACE,SAAS,CAAC,CAAC;UACzB,IAAI,CAACjC,cAAc,CAACnL,IAAI,CAAC,MAAMmN,GAAG,CAACxN,WAAW,CAAC,CAAC,CAAC;QACrD;MACJ;IACJ;IACA,IAAI/E,IAAI,KAAK,WAAW,IAAImR,OAAO,CAAC1R,KAAK,EAAE;MACvC,IAAI,CAAC4R,SAAS,CAACF,OAAO,CAAC1R,KAAK,CAACkG,aAAa,CAAC;MAC3C,IAAI,CAACsL,MAAM,CAAC,CAAC;IACjB;EACJ;EACAS,kBAAkBA,CAACtL,GAAG,EAAE3G,KAAK,EAAE;IAC3B,IAAI,CAAC6Q,aAAa,CAAClL,IAAI,CAACgB,GAAG,CAAC;IAC5B3G,KAAK,CAAC0G,cAAc,CAACf,IAAI,CAACgB,GAAG,CAAC;IAC9B,IAAIA,GAAG,YAAYpK,YAAY,EAAE;MAC7B+G,MAAM,CAAC0P,MAAM,CAACrM,GAAG,CAACsD,QAAQ,EAAE;QAAEjK;MAAM,CAAC,CAAC;IAC1C;EACJ;EACAwR,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACxR,KAAK,EAAE;MACb;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAACA,KAAK,CAAC8H,OAAO,EAAE;MACrB,IAAI,CAACiJ,gBAAgB,GAAG,IAAI;MAC5B;IACJ;IACA,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACH,aAAa,CAAC/N,OAAO,CAAEgP,YAAY,IAAKA,YAAY,CAACvM,WAAW,CAAC,CAAC,CAAC;IACxE,IAAI,CAACsL,aAAa,GAAG,CACjBpL,OAAO,CAAC,IAAI,CAACxF,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;MAAEyF,WAAW;MAAEC;IAAa,CAAC,KAAK;MAC7D,MAAMuL,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,IAAI,IAAI,CAAC1I,MAAM,CAAClB,MAAM,CAACE,UAAU,KAAK,KAAK,EAAE;QACzC9B,WAAW,IAAI,IAAI,CAACqM,WAAW,CAACb,YAAY,EAAE,IAAI,CAACjR,KAAK,CAAC;QACzD,IAAI,CAACyF,WAAW,IAAKA,WAAW,IAAIC,YAAa,EAAE;UAC/C,IAAI,CAACwL,UAAU,IACX,IAAI,CAACT,QAAQ,CAACwC,QAAQ,CAAC,IAAI,CAAC/B,UAAU,CAACgC,aAAa,EAAE,SAAS,EAAExN,YAAY,GAAG,MAAM,GAAG,EAAE,CAAC;QACpG;MACJ,CAAC,MACI;QACD,IAAIA,YAAY,EAAE;UACduL,YAAY,CAACc,KAAK,CAAC,CAAC;UACpB,IAAI,IAAI,CAAC/R,KAAK,CAACmT,SAAS,EAAE;YACtB,IAAI,CAAC1C,QAAQ,CAAC2C,eAAe,CAAC,IAAI,CAAClC,UAAU,CAACgC,aAAa,EAAE,OAAO,CAAC;UACzE;QACJ,CAAC,MACI;UACD,IAAI,CAACpB,WAAW,CAACb,YAAY,EAAE,IAAI,CAACjR,KAAK,CAAC;UAC1C,IAAI,IAAI,CAACA,KAAK,CAACmT,SAAS,EAAE;YACtB,IAAI,CAAC1C,QAAQ,CAAC4C,YAAY,CAAC,IAAI,CAACnC,UAAU,CAACgC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAClT,KAAK,CAACmT,SAAS,CAAC;UAC5F;QACJ;MACJ;MACA,CAAC1N,WAAW,IAAI,IAAI,CAACzF,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC,IAAI,CAAC9M,KAAK,CAAC;IAChE,CAAC,CAAC,EACFwF,OAAO,CAAC,IAAI,CAACxF,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;MAAEyF,WAAW;MAAEC;IAAa,CAAC,KAAK;MAClE,IAAI,CAAC,CAACD,WAAW,IAAKA,WAAW,IAAIC,YAAa,MAC7C,CAAC,IAAI,CAAC6C,MAAM,CAAClB,MAAM,CAACE,UAAU,IAAI,IAAI,CAACvH,KAAK,CAACsT,IAAI,KAAK,IAAI,CAAC,EAAE;QAC9D,IAAI,CAACpC,UAAU,IAAI,IAAI,CAACT,QAAQ,CAAC4C,YAAY,CAAC,IAAI,CAACnC,UAAU,CAACgC,aAAa,EAAE,OAAO,EAAExN,YAAY,CAAC;MACvG;IACJ,CAAC,CAAC,EACF,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACjH,GAAG,CAAEgG,IAAI,IAAKe,OAAO,CAAC,IAAI,CAACxF,KAAK,EAAE,CAAC,aAAa,EAAEyE,IAAI,CAAC,EAAE,CAAC;MAAEgB;IAAY,CAAC,KAAK,CAACA,WAAW,IAAIgB,iBAAiB,CAAC,IAAI,CAACzG,KAAK,CAAC,CAAC,CAAC,CACrK;EACL;EACA4R,SAASA,CAAC5R,KAAK,EAAE;IACb,IAAIA,KAAK,EAAE;MACP,IAAIA,KAAK,CAAC0G,cAAc,EAAE;QACtB1G,KAAK,CAAC0G,cAAc,GAAG1G,KAAK,CAAC0G,cAAc,CAACrI,MAAM,CAAEsI,GAAG,IAAK,IAAI,CAACkK,aAAa,CAAC9P,OAAO,CAAC4F,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;MACvG,CAAC,MACI;QACDtF,gBAAgB,CAAC,IAAI,CAACrB,KAAK,EAAE,gBAAgB,EAAE,EAAE,CAAC;MACtD;IACJ;IACA,IAAI,CAAC6Q,aAAa,GAAG,EAAE;EAC3B;EACA8B,YAAYA,CAAC3S,KAAK,EAAE;IAChB,IAAI,CAACgR,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAChR,KAAK,EAAE;MACR,OAAO,MAAM,CAAE,CAAC;IACpB;IACA,MAAMuT,UAAU,GAAG,CAACrO,WAAW,CAAClF,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,MAAMA,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC,CAAC,CAAC;IAC5F,IAAIA,KAAK,CAAC8H,OAAO,EAAE;MACfyL,UAAU,CAAC5N,IAAI,CAACT,WAAW,CAAClF,KAAK,CAAC8H,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM9H,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC,CAAC,CAAC;IACxG;IACA,KAAK,MAAMQ,GAAG,IAAI8C,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAACwT,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE;MACrD,MAAMC,kBAAkB,GAAGjO,OAAO,CAACxF,KAAK,EAAE,CAAC,cAAc,EAAEQ,GAAG,CAAC,EAAE,CAAC;QAAEkF,YAAY;QAAEQ;MAAc,CAAC,KAAK;QAClG,IAAIA,aAAa,EAAEwN,YAAY,EAAE;UAC7BxN,aAAa,CAACwN,YAAY,CAACpO,WAAW,CAAC,CAAC;UACxCY,aAAa,CAACwN,YAAY,GAAG,IAAI;QACrC;QACA,IAAI7V,YAAY,CAAC6H,YAAY,CAACiO,MAAM,CAAC,EAAE;UACnCjO,YAAY,CAACgO,YAAY,GAAGhO,YAAY,CAACiO,MAAM,CAACZ,SAAS,CAAC,CAAC;QAC/D;MACJ,CAAC,CAAC;MACFQ,UAAU,CAAC5N,IAAI,CAAC,MAAM;QAClB,IAAI3F,KAAK,CAACwT,YAAY,CAAChT,GAAG,CAAC,EAAEkT,YAAY,EAAE;UACvC1T,KAAK,CAACwT,YAAY,CAAChT,GAAG,CAAC,CAACkT,YAAY,CAACpO,WAAW,CAAC,CAAC;QACtD;QACAmO,kBAAkB,CAACnO,WAAW,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;IACA,KAAK,MAAMxE,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,qBAAqB,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;MAChF,MAAM8S,aAAa,GAAGpO,OAAO,CAACxF,KAAK,EAAEc,IAAI,EAAE,CAAC;QAAE2E;MAAY,CAAC,KAAK,CAACA,WAAW,IAAIzF,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC,CAAC;MACnHuT,UAAU,CAAC5N,IAAI,CAAC,MAAMiO,aAAa,CAACtO,WAAW,CAAC,CAAC,CAAC;IACtD;IACA,IAAItF,KAAK,CAAC2H,WAAW,IAAI,CAAC3H,KAAK,CAACuG,UAAU,EAAE;MACxC,MAAMoH,OAAO,GAAG3N,KAAK,CAAC2H,WAAW;MACjC,IAAI8H,YAAY,GAAG9B,OAAO,CAAC8B,YAAY,CAACoE,IAAI,CAAC3V,oBAAoB,CAAC,CAACsF,CAAC,EAAEsQ,CAAC,KAAK;QACxE,IAAItQ,CAAC,KAAKsQ,CAAC,IAAI5S,KAAK,CAACC,OAAO,CAACqC,CAAC,CAAC,IAAIlB,QAAQ,CAACkB,CAAC,CAAC,EAAE;UAC5C,OAAO,KAAK;QAChB;QACA,OAAO,IAAI;MACf,CAAC,CAAC,CAAC;MACH,IAAImK,OAAO,CAACnM,KAAK,KAAKiB,aAAa,CAACzC,KAAK,CAAC,EAAE;QACxCyP,YAAY,GAAGA,YAAY,CAACoE,IAAI,CAAC1V,SAAS,CAACwP,OAAO,CAACnM,KAAK,CAAC,CAAC;MAC9D;MACA,MAAM;QAAEuS,QAAQ;QAAEC;MAAS,CAAC,GAAGhU,KAAK,CAACiU,YAAY;MACjD,IAAI,CAAC,CAACF,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,KAAKC,QAAQ,EAAEE,OAAO,GAAG,CAAC,EAAE;QAC/DzE,YAAY,GAAG9B,OAAO,CAAC8B,YAAY,CAACoE,IAAI,CAACzV,YAAY,CAAC4V,QAAQ,CAACE,OAAO,CAAC,CAAC;MAC5E;MACA,MAAMpB,GAAG,GAAGrD,YAAY,CAACsD,SAAS,CAAEvR,KAAK,IAAK;QAC1C;QACA,IAAImM,OAAO,CAACE,OAAO,EAAEnM,MAAM,GAAG,CAAC,IAAIiM,OAAO,YAAYhQ,WAAW,EAAE;UAC/DgQ,OAAO,CAACqB,UAAU,CAACxN,KAAK,EAAE;YAAEkM,SAAS,EAAE,KAAK;YAAE0B,QAAQ,EAAE;UAAK,CAAC,CAAC;QACnE;QACApP,KAAK,CAACmU,OAAO,EAAEtR,OAAO,CAAEuR,QAAQ,IAAM5S,KAAK,GAAG4S,QAAQ,CAAC5S,KAAK,CAAE,CAAC;QAC/D,IAAIA,KAAK,KAAKxB,KAAK,CAAC2H,WAAW,CAACnG,KAAK,EAAE;UACnCxB,KAAK,CAAC2H,WAAW,CAACvB,QAAQ,CAAC5E,KAAK,CAAC;UACjC;QACJ;QACA,IAAId,MAAM,CAACV,KAAK,CAAC,EAAE;UACfuB,gBAAgB,CAACvB,KAAK,EAAEwB,KAAK,CAAC;QAClC;QACAxB,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,CAAC0B,IAAI,CAAC;UAAE7S,KAAK;UAAExB,KAAK;UAAEG,IAAI,EAAE;QAAe,CAAC,CAAC;MAC3E,CAAC,CAAC;MACFoT,UAAU,CAAC5N,IAAI,CAAC,MAAMmN,GAAG,CAACxN,WAAW,CAAC,CAAC,CAAC;IAC5C;IACA,OAAO,MAAMiO,UAAU,CAAC1Q,OAAO,CAAEkQ,SAAS,IAAKA,SAAS,CAAC,CAAC,CAAC;EAC/D;AACJ;AACAvC,WAAW,CAAC3E,IAAI,YAAAyI,oBAAAvI,CAAA;EAAA,YAAAA,CAAA,IAAyFyE,WAAW,EAlcpBpU,EAAE,CAAA0T,iBAAA,CAkcoC9I,YAAY,GAlclD5K,EAAE,CAAA0T,iBAAA,CAkc6D1T,EAAE,CAACmY,SAAS,GAlc3EnY,EAAE,CAAA0T,iBAAA,CAkcsF1T,EAAE,CAACoY,UAAU,GAlcrGpY,EAAE,CAAA0T,iBAAA,CAkcgH1T,EAAE,CAACU,gBAAgB,GAlcrIV,EAAE,CAAA0T,iBAAA,CAkcgJQ,oBAAoB;AAAA,CAA4D;AAClUE,WAAW,CAACiE,IAAI,kBAncgFrY,EAAE,CAAAsY,iBAAA;EAAAvU,IAAA,EAmcLqQ,WAAW;EAAAP,SAAA;EAAA0E,SAAA,WAAAC,kBAAA5V,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAncR5C,EAAE,CAAAyY,WAAA,CAAA/V,GAAA,KAmcgLhC,gBAAgB;IAAA;IAAA,IAAAkC,EAAA;MAAA,IAAA8V,EAAA;MAnclM1Y,EAAE,CAAA2Y,cAAA,CAAAD,EAAA,GAAF1Y,EAAE,CAAA4Y,WAAA,QAAA/V,GAAA,CAAAsN,gBAAA,GAAAuI,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA/E,MAAA;IAAAlQ,KAAA;EAAA;EAAAmQ,QAAA,GAAF/T,EAAE,CAAAgU,oBAAA;EAAA8E,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAhV,QAAA,WAAAiV,qBAAArW,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF5C,EAAE,CAAAkZ,UAAA,IAAAvW,kCAAA,gCAAF3C,EAAE,CAAAmZ,sBAmcsS,CAAC;IAAA;EAAA;EAAAC,MAAA;AAAA,EAA6D;AACtc;EAAA,QAAA3K,SAAA,oBAAAA,SAAA,KApcgGzO,EAAE,CAAAiQ,iBAAA,CAocNmE,WAAW,EAAc,CAAC;IAC1GrQ,IAAI,EAAEpD,SAAS;IACf6F,IAAI,EAAE,CAAC;MAAEyN,QAAQ,EAAE,cAAc;MAAEjQ,QAAQ,EAAE,wCAAwC;MAAEoV,MAAM,EAAE,CAAC,6BAA6B;IAAE,CAAC;EACpI,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErV,IAAI,EAAE6G;IAAa,CAAC,EAAE;MAAE7G,IAAI,EAAE/D,EAAE,CAACmY;IAAU,CAAC,EAAE;MAAEpU,IAAI,EAAE/D,EAAE,CAACoY;IAAW,CAAC,EAAE;MAAErU,IAAI,EAAE/D,EAAE,CAACU;IAAiB,CAAC,EAAE;MAAEqD,IAAI,EAAEmQ,oBAAoB;MAAE9C,UAAU,EAAE,CAAC;QACjLrN,IAAI,EAAExD;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqD,KAAK,EAAE,CAAC;MACpCG,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE0P,gBAAgB,EAAE,CAAC;MACnBpM,IAAI,EAAEnD,SAAS;MACf4F,IAAI,EAAE,CAAC,WAAW,EAAE;QAAE6S,IAAI,EAAE3Y,gBAAgB;QAAE4Y,MAAM,EAAE;MAAK,CAAC;IAChE,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbrV,WAAWA,CAACsV,OAAO,EAAErN,MAAM,EAAEsN,MAAM,EAAEC,cAAc,EAAE;IACjD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACrN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACsN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,WAAW,GAAG,IAAI9Y,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC+C,KAAK,GAAG;MAAEG,IAAI,EAAE;IAAe,CAAC;IACrC,IAAI,CAAC6V,iBAAiB,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAChF,uBAAuB,GAAG,MAAM,CAAE,CAAC;EAC5C;EACA;EACA,IAAItR,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACM,KAAK,CAACN,IAAI,GAAGA,IAAI;EAC1B;EACA,IAAIA,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACM,KAAK,CAACN,IAAI;EAC1B;EACA;EACA,IAAIyC,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACoG,MAAM,CAAClB,MAAM,CAAC4O,SAAS,IAAI,IAAI,CAACD,iBAAiB,KAAK7T,KAAK,EAAE;MAClE;IACJ;IACA,IAAI,CAAC+T,QAAQ,CAAC;MAAE/T;IAAM,CAAC,CAAC;EAC5B;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnC,KAAK,CAACmC,KAAK;EAC3B;EACA;EACA,IAAIgU,MAAMA,CAAC5P,UAAU,EAAE;IACnB,IAAI,CAAC2P,QAAQ,CAAC;MAAE3P;IAAW,CAAC,CAAC;EACjC;EACA,IAAI4P,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACnW,KAAK,CAACuG,UAAU;EAChC;EACA;EACA,IAAIuB,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACoO,QAAQ,CAAC;MAAEpO;IAAQ,CAAC,CAAC;EAC9B;EACA,IAAIA,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9H,KAAK,CAAC8H,OAAO;EAC7B;EACA,IAAI0K,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACsD,cAAc,CAACtD,SAAS,GAAGA,SAAS;EAC7C;EACAjB,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAChJ,MAAM,CAAClB,MAAM,CAACC,iBAAiB,KAAK,sBAAsB,EAAE;MACjE,IAAI,CAAC8O,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAxG,WAAWA,CAAC8B,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACyE,MAAM,IAAI,IAAI,CAACzW,IAAI,EAAE;MAC7BgQ,YAAY,CAAC,IAAI,CAAChQ,IAAI,CAAC;IAC3B;IACA,IAAIgS,OAAO,CAACyE,MAAM,IAAIzE,OAAO,CAAChS,IAAI,IAAKgS,OAAO,CAACvP,KAAK,IAAI,IAAI,CAAC6T,iBAAiB,KAAKtE,OAAO,CAACvP,KAAK,CAACuD,YAAa,EAAE;MAC5G,IAAI,CAACsL,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAC4E,OAAO,CAACnJ,KAAK,CAAC,IAAI,CAACzM,KAAK,CAAC;MAC9B,IAAI,CAACgR,uBAAuB,GAAG,IAAI,CAACvB,YAAY,CAAC,CAAC;IACtD;EACJ;EACAkC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACX,uBAAuB,CAAC,CAAC;EAClC;EACAoF,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACpW,KAAK,CAAC8H,OAAO,CAAC+E,gBAAgB,GAAG,IAAI,CAAC7M,KAAK,CAAC;EACrD;EACAyP,YAAYA,CAAA,EAAG;IACX,IAAI,CAACuB,uBAAuB,CAAC,CAAC;IAC9B,MAAM8B,GAAG,GAAG,IAAI,CAAC9S,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,CACtCkB,IAAI,CAACxV,MAAM,CAAC,CAAC;MAAE2B,KAAK;MAAEG;IAAK,CAAC,KAAKO,MAAM,CAACV,KAAK,CAAC,IAAIG,IAAI,KAAK,cAAc,CAAC,EAAE7B,SAAS,CAAC,MAAM,IAAI,CAACuX,MAAM,CAACQ,QAAQ,CAACC,YAAY,CAAC,CAAC,CAACzC,IAAI,CAACtV,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/IwU,SAAS,CAAC,MAAM,IAAI,CAAC8C,MAAM,CAACU,UAAU,CAAC,MAAM;MAC9C;MACA;MACA,IAAI,CAACH,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACL,WAAW,CAACvG,IAAI,CAAE,IAAI,CAACwG,iBAAiB,GAAGxT,KAAK,CAAC,IAAI,CAACL,KAAK,CAAE,CAAC;IACvE,CAAC,CAAC,CAAC;IACH,OAAO,MAAM2Q,GAAG,CAACxN,WAAW,CAAC,CAAC;EAClC;EACA4Q,QAAQA,CAAClW,KAAK,EAAE;IACZ,IAAI,IAAI,CAACuI,MAAM,CAAClB,MAAM,CAAC4O,SAAS,EAAE;MAC9B,IAAI,CAACjW,KAAK,GAAG;QAAE,GAAG,IAAI,CAACA,KAAK;QAAE,GAAGwC,KAAK,CAACxC,KAAK;MAAE,CAAC;IACnD,CAAC,MACI;MACDsD,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAAC,CAAC6C,OAAO,CAAE2T,CAAC,IAAM,IAAI,CAACxW,KAAK,CAACwW,CAAC,CAAC,GAAGxW,KAAK,CAACwW,CAAC,CAAE,CAAC;IACjE;EACJ;AACJ;AACAb,UAAU,CAAC9J,IAAI,YAAA4K,mBAAA1K,CAAA;EAAA,YAAAA,CAAA,IAAyF4J,UAAU,EA7iBlBvZ,EAAE,CAAA0T,iBAAA,CA6iBkCxD,iBAAiB,GA7iBrDlQ,EAAE,CAAA0T,iBAAA,CA6iBgE9I,YAAY,GA7iB9E5K,EAAE,CAAA0T,iBAAA,CA6iByF1T,EAAE,CAACsa,MAAM,GA7iBpGta,EAAE,CAAA0T,iBAAA,CA6iB+GQ,oBAAoB;AAAA,CAA4C;AACjRqF,UAAU,CAAClB,IAAI,kBA9iBiFrY,EAAE,CAAAsY,iBAAA;EAAAvU,IAAA,EA8iBNwV,UAAU;EAAA1F,SAAA;EAAA0G,cAAA,WAAAC,0BAAA5X,EAAA,EAAAC,GAAA,EAAA4X,QAAA;IAAA,IAAA7X,EAAA;MA9iBN5C,EAAE,CAAA0a,cAAA,CAAAD,QAAA,EA8iBgQlH,cAAc;IAAA;IAAA,IAAA3Q,EAAA;MAAA,IAAA8V,EAAA;MA9iBhR1Y,EAAE,CAAA2Y,cAAA,CAAAD,EAAA,GAAF1Y,EAAE,CAAA4Y,WAAA,QAAA/V,GAAA,CAAAuT,SAAA,GAAAsC,EAAA;IAAA;EAAA;EAAA5E,MAAA;IAAAxQ,IAAA;IAAAyC,KAAA;IAAAgU,MAAA;IAAArO,OAAA;EAAA;EAAAiP,OAAA;IAAAhB,WAAA;EAAA;EAAA5F,QAAA,GAAF/T,EAAE,CAAA4a,kBAAA,CA8iBmK,CAAC1K,iBAAiB,EAAEgE,oBAAoB,CAAC,GA9iB9MlU,EAAE,CAAAgU,oBAAA;EAAA8E,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAhV,QAAA,WAAA6W,oBAAAjY,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF5C,EAAE,CAAA+C,SAAA,qBA8iB6W,CAAC;IAAA;IAAA,IAAAH,EAAA;MA9iBhX5C,EAAE,CAAAkD,UAAA,UAAAL,GAAA,CAAAe,KA8iB6V,CAAC;IAAA;EAAA;EAAAkX,YAAA,GAAwD1G,WAAW;EAAA2G,aAAA;EAAAC,eAAA;AAAA,EAAuG;AAC1mB;EAAA,QAAAvM,SAAA,oBAAAA,SAAA,KA/iBgGzO,EAAE,CAAAiQ,iBAAA,CA+iBNsJ,UAAU,EAAc,CAAC;IACzGxV,IAAI,EAAEpD,SAAS;IACf6F,IAAI,EAAE,CAAC;MACCyN,QAAQ,EAAE,aAAa;MACvBjQ,QAAQ,EAAE,+CAA+C;MACzDiX,SAAS,EAAE,CAAC/K,iBAAiB,EAAEgE,oBAAoB,CAAC;MACpD8G,eAAe,EAAEla,uBAAuB,CAACoa;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnX,IAAI,EAAEmM;IAAkB,CAAC,EAAE;MAAEnM,IAAI,EAAE6G;IAAa,CAAC,EAAE;MAAE7G,IAAI,EAAE/D,EAAE,CAACsa;IAAO,CAAC,EAAE;MAAEvW,IAAI,EAAEmQ;IAAqB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE5Q,IAAI,EAAE,CAAC;MACzKS,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEsF,KAAK,EAAE,CAAC;MACRhC,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEsZ,MAAM,EAAE,CAAC;MACThW,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEiL,OAAO,EAAE,CAAC;MACV3H,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEkZ,WAAW,EAAE,CAAC;MACd5V,IAAI,EAAEhD;IACV,CAAC,CAAC;IAAEqV,SAAS,EAAE,CAAC;MACZrS,IAAI,EAAE/C,eAAe;MACrBwF,IAAI,EAAE,CAAC+M,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM4H,gBAAgB,CAAC;EACnBjX,WAAWA,CAACmQ,QAAQ,EAAES,UAAU,EAAEsG,SAAS,EAAE;IACzC,IAAI,CAAC/G,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACS,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACuG,iBAAiB,GAAG,CAAC,CAAC;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG;MACZC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;MAC5EjY,QAAQ,EAAEA,CAACkY,SAAS,EAAEC,MAAM,KAAK;QAC7B,QAAQD,SAAS;UACb,KAAK,OAAO;YACR,OAAO,IAAI,CAACE,OAAO,CAACD,MAAM,CAAC;UAC/B,KAAK,MAAM;YACP,OAAO,IAAI,CAACE,MAAM,CAACF,MAAM,CAAC;UAC9B,KAAK,QAAQ;YACT,OAAO,IAAI,CAAC/R,QAAQ,CAAC+R,MAAM,CAAC;UAChC;YACI,OAAO,IAAI,CAACpJ,KAAK,CAACmJ,SAAS,CAAC,CAAC,IAAI,CAAC7X,KAAK,EAAE8X,MAAM,CAAC;QACxD;MACJ;IACJ,CAAC;IACD,IAAI,CAACG,QAAQ,GAAGT,SAAS;EAC7B;EACA,IAAI9I,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC1O,KAAK,CAAC0O,KAAK,IAAI,CAAC,CAAC;EACjC;EACA,IAAIwJ,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAClY,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE;EAC7C;EACA4P,WAAWA,CAAC8B,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC1R,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,CAACO,IAAI,IAAI,IAAI,CAAC8S,YAAY,CAAC,MAAM,EAAE,IAAI,CAACrT,KAAK,CAACO,IAAI,CAAC;MAC7D,IAAI,CAACmX,QAAQ,CAACC,SAAS,CAAC9U,OAAO,CAAEsV,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;MACzD,IAAI,CAACT,QAAQ,CAACE,MAAM,CAAC/U,OAAO,CAAEgV,SAAS,IAAK;QACxC,IAAI,IAAI,CAACnJ,KAAK,GAAGmJ,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC9W,OAAO,CAAC8W,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;UAClF,IAAI,CAACH,QAAQ,CAACC,SAAS,CAAChS,IAAI,CAAC,IAAI,CAAC8K,QAAQ,CAAC2H,MAAM,CAAC,IAAI,CAAClH,UAAU,CAACgC,aAAa,EAAE2E,SAAS,EAAGtN,CAAC,IAAK,IAAI,CAACmN,QAAQ,CAAC/X,QAAQ,CAACkY,SAAS,EAAEtN,CAAC,CAAC,CAAC,CAAC;QAC7I;MACJ,CAAC,CAAC;MACF,IAAI,IAAI,CAACmE,KAAK,EAAE2J,UAAU,EAAE;QACxB7S,OAAO,CAAC,IAAI,CAACxF,KAAK,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC;UAAE0F,YAAY;UAAEQ;QAAc,CAAC,KAAK;UAC9E,IAAIA,aAAa,EAAE;YACf5C,MAAM,CAACiB,IAAI,CAAC2B,aAAa,CAAC,CAACrD,OAAO,CAAEyV,IAAI,IAAK,IAAI,CAAClF,eAAe,CAACkF,IAAI,CAAC,CAAC;UAC5E;UACA,IAAI5S,YAAY,EAAE;YACdpC,MAAM,CAACiB,IAAI,CAACmB,YAAY,CAAC,CAAC7C,OAAO,CAAEyV,IAAI,IAAK;cACxC,IAAI5S,YAAY,CAAC4S,IAAI,CAAC,IAAI,IAAI,EAAE;gBAC5B,IAAI,CAACjF,YAAY,CAACiF,IAAI,EAAE5S,YAAY,CAAC4S,IAAI,CAAC,CAAC;cAC/C;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAACC,gBAAgB,CAAC7G,OAAO,CAAC1R,KAAK,CAACkG,aAAa,CAAC;MAClD,IAAI,CAACsS,gBAAgB,CAAC9G,OAAO,CAAC1R,KAAK,CAAC0F,YAAY,CAAC;MACjD,IAAI,IAAI,CAACwS,iBAAiB,CAACxW,MAAM,KAAK,CAAC,EAAE;QACrC,CAAC,IAAI,CAACxB,EAAE,IAAI,IAAI,CAACF,KAAK,CAACE,EAAE,IAAI,IAAI,CAACmT,YAAY,CAAC,IAAI,EAAE,IAAI,CAACrT,KAAK,CAACE,EAAE,CAAC;QACnE,IAAI,CAACuY,aAAa,GAAGjT,OAAO,CAAC,IAAI,CAACxF,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;UAAE0F;QAAa,CAAC,KAAK;UACtE,IAAI,CAACgT,WAAW,CAAChT,YAAY,CAAC;QAClC,CAAC,CAAC;MACN;IACJ;IACA,IAAIgM,OAAO,CAACxR,EAAE,EAAE;MACZ,IAAI,CAACmT,YAAY,CAAC,IAAI,EAAE,IAAI,CAACnT,EAAE,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIqR,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACoH,YAAY,EAAE;MACpB,MAAMC,OAAO,GAAG,IAAI,CAAC1H,UAAU,CAACgC,aAAa;MAC7C,IAAI,CAACyF,YAAY,GAAG,CAAC,GAAGrX,iBAAiB,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAACjD,MAAM,CAAEia,IAAI,IAAK,CAACM,OAAO,CAACC,YAAY,IAAI,CAACD,OAAO,CAACC,YAAY,CAACP,IAAI,CAAC,CAAC;IAChL;IACA,IAAI,CAACK,YAAY,CAAC9V,OAAO,CAAEyV,IAAI,IAAK;MAChC,MAAM9W,KAAK,GAAG,IAAI,CAACkN,KAAK,CAAC4J,IAAI,CAAC;MAC9B,IAAI,IAAI,CAACb,iBAAiB,CAACa,IAAI,CAAC,KAAK9W,KAAK,KACrC,CAAC,IAAI,CAACkN,KAAK,CAAC2J,UAAU,IAAI,CAAC,IAAI,CAAC3J,KAAK,CAAC2J,UAAU,CAACjP,cAAc,CAACkP,IAAI,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvF,IAAI,CAACrB,iBAAiB,CAACa,IAAI,CAAC,GAAG9W,KAAK;QACpC,IAAIA,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;UACtB,IAAI,CAAC6R,YAAY,CAACiF,IAAI,EAAE9W,KAAK,KAAK,IAAI,GAAG8W,IAAI,GAAI,GAAE9W,KAAM,EAAC,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAAC4R,eAAe,CAACkF,IAAI,CAAC;QAC9B;MACJ;IACJ,CAAC,CAAC;EACN;EACA3G,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+F,QAAQ,CAACC,SAAS,CAAC9U,OAAO,CAAEsV,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACzD,IAAI,CAACI,gBAAgB,CAAC,IAAI,CAACvY,KAAK,CAAC;IACjC,IAAI,CAACyY,aAAa,EAAEnT,WAAW,CAAC,CAAC;EACrC;EACAoT,WAAWA,CAAClX,KAAK,EAAE;IACf,MAAMoX,OAAO,GAAG,IAAI,CAACV,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;IACzE,IAAI,CAACU,OAAO,IAAI,CAACA,OAAO,CAAC1F,aAAa,CAAC6F,KAAK,EAAE;MAC1C;IACJ;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC,IAAI,CAACf,QAAQ,CAACgB,aAAa,IAC3C,IAAI,CAACf,iBAAiB,CAACgB,IAAI,CAAC,CAAC;MAAEhG;IAAc,CAAC,KAAK,IAAI,CAAC+E,QAAQ,CAACgB,aAAa,KAAK/F,aAAa,IAAIA,aAAa,CAACiG,QAAQ,CAAC,IAAI,CAAClB,QAAQ,CAACgB,aAAa,CAAC,CAAC;IAC5J,IAAIzX,KAAK,IAAI,CAACwX,SAAS,EAAE;MACrBI,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC1V,IAAI,CAAC,MAAMiV,OAAO,CAAC1F,aAAa,CAAC6F,KAAK,CAAC,CAAC,CAAC;IAC/D,CAAC,MACI,IAAI,CAACvX,KAAK,IAAIwX,SAAS,EAAE;MAC1BI,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC1V,IAAI,CAAC,MAAMiV,OAAO,CAAC1F,aAAa,CAACoG,IAAI,CAAC,CAAC,CAAC;IAC9D;EACJ;EACAvB,OAAOA,CAACD,MAAM,EAAE;IACZ,IAAI,CAACW,aAAa,EAAErS,QAAQ,CAAC,IAAI,CAAC;IAClC,IAAI,CAACsI,KAAK,CAACqK,KAAK,GAAG,IAAI,CAAC/Y,KAAK,EAAE8X,MAAM,CAAC;EAC1C;EACAE,MAAMA,CAACF,MAAM,EAAE;IACX,IAAI,CAACW,aAAa,EAAErS,QAAQ,CAAC,KAAK,CAAC;IACnC,IAAI,CAACsI,KAAK,CAAC4K,IAAI,GAAG,IAAI,CAACtZ,KAAK,EAAE8X,MAAM,CAAC;EACzC;EACA;EACAyB,YAAYA,CAACzB,MAAM,EAAE;IACjB,IAAIA,MAAM,YAAY0B,KAAK,EAAE;MACzB;IACJ;IACA,IAAI,CAACzT,QAAQ,CAAC+R,MAAM,CAAC;EACzB;EACA/R,QAAQA,CAAC+R,MAAM,EAAE;IACb,IAAI,CAACpJ,KAAK,CAAC+K,MAAM,GAAG,IAAI,CAACzZ,KAAK,EAAE8X,MAAM,CAAC;IACvC,IAAI,CAAC9X,KAAK,CAAC2H,WAAW,EAAE+R,WAAW,CAAC,CAAC;EACzC;EACAlB,gBAAgBA,CAAClS,CAAC,EAAE;IAChB,IAAI,CAACA,CAAC,EAAE;MACJ;IACJ;IACA,IAAIA,CAAC,CAAC,cAAc,CAAC,EAAEvF,OAAO,CAAC,IAAI,CAACmQ,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACpD5K,CAAC,CAAC,cAAc,CAAC,CAACX,IAAI,CAAC,IAAI,CAACuL,UAAU,CAAC;IAC3C,CAAC,MACI;MACD7P,gBAAgB,CAACiF,CAAC,EAAE,cAAc,EAAE,CAAC,IAAI,CAAC4K,UAAU,CAAC,CAAC;IAC1D;EACJ;EACAqH,gBAAgBA,CAACjS,CAAC,EAAE;IAChB,MAAMrG,KAAK,GAAGqG,CAAC,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC4R,iBAAiB,CAACnX,OAAO,CAAC,IAAI,CAACmQ,UAAU,CAAC,GAAG,CAAC,CAAC;IACxF,IAAIjR,KAAK,KAAK,CAAC,CAAC,EAAE;MACdqG,CAAC,CAAC,cAAc,CAAC,CAACwH,MAAM,CAAC7N,KAAK,EAAE,CAAC,CAAC;IACtC;EACJ;EACAoT,YAAYA,CAACiF,IAAI,EAAE9W,KAAK,EAAE;IACtB,IAAI,CAACiP,QAAQ,CAAC4C,YAAY,CAAC,IAAI,CAACnC,UAAU,CAACgC,aAAa,EAAEoF,IAAI,EAAE9W,KAAK,CAAC;EAC1E;EACA4R,eAAeA,CAACkF,IAAI,EAAE;IAClB,IAAI,CAAC7H,QAAQ,CAAC2C,eAAe,CAAC,IAAI,CAAClC,UAAU,CAACgC,aAAa,EAAEoF,IAAI,CAAC;EACtE;AACJ;AACAf,gBAAgB,CAAC1L,IAAI,YAAA8N,yBAAA5N,CAAA;EAAA,YAAAA,CAAA,IAAyFwL,gBAAgB,EAxuB9Bnb,EAAE,CAAA0T,iBAAA,CAwuB8C1T,EAAE,CAACmY,SAAS,GAxuB5DnY,EAAE,CAAA0T,iBAAA,CAwuBuE1T,EAAE,CAACoY,UAAU,GAxuBtFpY,EAAE,CAAA0T,iBAAA,CAwuBiGnR,QAAQ;AAAA,CAA4C;AACvP4Y,gBAAgB,CAACxH,IAAI,kBAzuB2E3T,EAAE,CAAA4T,iBAAA;EAAA7P,IAAA,EAyuBAoX,gBAAgB;EAAAtH,SAAA;EAAA2J,YAAA,WAAAC,8BAAA7a,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAzuBlB5C,EAAE,CAAA0d,UAAA,oBAAAC,2CAAAjC,MAAA;QAAA,OAyuBA7Y,GAAA,CAAAsa,YAAA,CAAAzB,MAAmB,CAAC;MAAA;IAAA;EAAA;EAAA5H,MAAA;IAAAlQ,KAAA;IAAAE,EAAA;EAAA;EAAAiQ,QAAA,GAzuBtB/T,EAAE,CAAAgU,oBAAA;AAAA,EAyuB6M;AAC/S;EAAA,QAAAvF,SAAA,oBAAAA,SAAA,KA1uBgGzO,EAAE,CAAAiQ,iBAAA,CA0uBNkL,gBAAgB,EAAc,CAAC;IAC/GpX,IAAI,EAAEvD,SAAS;IACfgG,IAAI,EAAE,CAAC;MACCyN,QAAQ,EAAE,oBAAoB;MAC9B2J,IAAI,EAAE;QACF,UAAU,EAAE;MAChB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7Z,IAAI,EAAE/D,EAAE,CAACmY;IAAU,CAAC,EAAE;MAAEpU,IAAI,EAAE/D,EAAE,CAACoY;IAAW,CAAC,EAAE;MAAErU,IAAI,EAAE0B,SAAS;MAAE2L,UAAU,EAAE,CAAC;QAC/GrN,IAAI,EAAE9C,MAAM;QACZuF,IAAI,EAAE,CAACjE,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqB,KAAK,EAAE,CAAC;MACpCG,IAAI,EAAEtD,KAAK;MACX+F,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE1C,EAAE,EAAE,CAAC;MACLC,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMod,SAAS,CAAC;EACZ,IAAI9X,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnC,KAAK,CAACmC,KAAK;EAC3B;EACA,IAAIzC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACM,KAAK,CAACN,IAAI;EAC1B;EACA,IAAIoI,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9H,KAAK,CAAC8H,OAAO;EAC7B;EACA,IAAItH,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACR,KAAK,CAACQ,GAAG;EACzB;EACA,IAAImH,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3H,KAAK,CAAC2H,WAAW;EACjC;EACA,IAAI+G,KAAKA,CAAA,EAAG;IACR,OAAQ,IAAI,CAAC1O,KAAK,CAAC0O,KAAK,IAAI,CAAC,CAAC;EAClC;EACA;EACA,IAAIwL,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACxL,KAAK;EACrB;EACA,IAAIhH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACI,OAAO,CAACJ,SAAS,CAAC,IAAI,CAAC;EACvC;EACA,IAAIxH,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACF,KAAK,CAACE,EAAE;EACxB;EACA,IAAIia,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrS,OAAO,CAACqS,SAAS,IAAI,CAAC,CAAC;EACvC;AACJ;AACAF,SAAS,CAACpO,IAAI,YAAAuO,kBAAArO,CAAA;EAAA,YAAAA,CAAA,IAAyFkO,SAAS;AAAA,CAAmD;AACnKA,SAAS,CAAClK,IAAI,kBA9xBkF3T,EAAE,CAAA4T,iBAAA;EAAA7P,IAAA,EA8xBP8Z,SAAS;EAAA/J,MAAA;IAAAlQ,KAAA;EAAA;AAAA,EAA6C;AACjJ;EAAA,QAAA6K,SAAA,oBAAAA,SAAA,KA/xBgGzO,EAAE,CAAAiQ,iBAAA,CA+xBN4N,SAAS,EAAc,CAAC;IACxG9Z,IAAI,EAAEvD;EACV,CAAC,CAAC,QAAkB;IAAEoD,KAAK,EAAE,CAAC;MACtBG,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMwd,WAAW,SAASJ,SAAS,CAAC;AAEpCI,WAAW,CAACxO,IAAI;EAAA,IAAAyO,wBAAA;EAAA,gBAAAC,oBAAAxO,CAAA;IAAA,QAAAuO,wBAAA,KAAAA,wBAAA,GAxyBgFle,EAAE,CAAAoe,qBAAA,CAwyBOH,WAAW,IAAAtO,CAAA,IAAXsO,WAAW;EAAA;AAAA,GAAqD;AACzKA,WAAW,CAAC5F,IAAI,kBAzyBgFrY,EAAE,CAAAsY,iBAAA;EAAAvU,IAAA,EAyyBLka,WAAW;EAAApK,SAAA;EAAAwK,QAAA;EAAAb,YAAA,WAAAc,yBAAA1b,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAzyBR5C,EAAE,CAAAue,UAAA,CAAA1b,GAAA,CAAAe,KAAA,CAAA4a,mBAAA;IAAA;EAAA;EAAAzK,QAAA,GAAF/T,EAAE,CAAAye,0BAAA;EAAAC,kBAAA,EAAAvb,GAAA;EAAA2V,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAhV,QAAA,WAAA2a,qBAAA/b,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF5C,EAAE,CAAA4e,eAAA;MAAF5e,EAAE,CAAAkZ,UAAA,IAAApW,mCAAA,yBA0yBnB,CAAC;MA1yBgB9C,EAAE,CAAA6e,YAAA,EA2yBtE,CAAC;IAAA;IAAA,IAAAjc,EAAA;MA3yBmE5C,EAAE,CAAAkD,UAAA,YAAAL,GAAA,CAAAe,KAAA,CAAAuG,UA0yBhD,CAAC;IAAA;EAAA;EAAA2Q,YAAA,GAET1G,WAAW,EAAuE9R,IAAI,CAACwc,OAAO;EAAA/D,aAAA;EAAAC,eAAA;AAAA,EAA8I;AACtR;EAAA,QAAAvM,SAAA,oBAAAA,SAAA,KA7yBgGzO,EAAE,CAAAiQ,iBAAA,CA6yBNgO,WAAW,EAAc,CAAC;IAC1Gla,IAAI,EAAEpD,SAAS;IACf6F,IAAI,EAAE,CAAC;MACCyN,QAAQ,EAAE,cAAc;MACxBjQ,QAAQ,EAAG;AAC/B;AACA;AACA,GAAG;MACiB4Z,IAAI,EAAE;QACF,SAAS,EAAE;MACf,CAAC;MACD5C,eAAe,EAAEla,uBAAuB,CAACoa;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAM6D,uBAAuB,CAAC;EAC1B7a,WAAWA,CAACiI,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACAqH,WAAWA,CAAA,EAAG;IACV,MAAMwL,eAAe,GAAG9Z,iBAAiB,CAAC7C,GAAG,CAAEyF,CAAC,IAAM,mBAAkBA,CAAE,EAAC,CAAC;IAC5E,IAAI,CAACmX,aAAa,GAAGvd,KAAK,CAAC,IAAI,CAACkC,KAAK,CAAC2H,WAAW,CAAC4H,aAAa,EAAE,CAAC,IAAI,CAACvP,KAAK,CAAC8H,OAAO,GAC9E/J,EAAE,CAAC,IAAI,CAAC,GACR,IAAI,CAACiC,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,CAACkB,IAAI,CAACxV,MAAM,CAAC,CAAC;MAAE2B,KAAK;MAAEG,IAAI;MAAEmb;IAAS,CAAC,KAAK;MACzE,OAAQtb,KAAK,KAAK,IAAI,CAACA,KAAK,IACxBG,IAAI,KAAK,mBAAmB,KAC3Bmb,QAAQ,CAACva,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAIqa,eAAe,CAACra,OAAO,CAACua,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC1V,SAAS,CAAC,IAAI,CAAC,EAAEG,SAAS,CAAC,MAAOT,YAAY,CAAC,IAAI,CAAC0d,YAAY,CAAC,GAAG,IAAI,CAACA,YAAY,GAAGxd,EAAE,CAAC,IAAI,CAACwd,YAAY,CAAE,CAAC,CAAC;EAClI;EACA,IAAIA,YAAYA,CAAA,EAAG;IACf,MAAMC,SAAS,GAAG,IAAI,CAACxb,KAAK,CAAC2H,WAAW;IACxC,KAAK,MAAM8C,KAAK,IAAI+Q,SAAS,CAACC,MAAM,EAAE;MAClC,IAAID,SAAS,CAACC,MAAM,CAACrS,cAAc,CAACqB,KAAK,CAAC,EAAE;QACxC,IAAI1B,OAAO,GAAG,IAAI,CAACR,MAAM,CAAC2C,mBAAmB,CAACT,KAAK,CAAC;QACpD,IAAInI,QAAQ,CAACkZ,SAAS,CAACC,MAAM,CAAChR,KAAK,CAAC,CAAC,EAAE;UACnC,IAAI+Q,SAAS,CAACC,MAAM,CAAChR,KAAK,CAAC,CAACiR,SAAS,EAAE;YACnC,OAAO7Z,SAAS;UACpB;UACA,IAAI2Z,SAAS,CAACC,MAAM,CAAChR,KAAK,CAAC,CAAC1B,OAAO,EAAE;YACjCA,OAAO,GAAGyS,SAAS,CAACC,MAAM,CAAChR,KAAK,CAAC,CAAC1B,OAAO;UAC7C;QACJ;QACA,IAAI,IAAI,CAAC/I,KAAK,CAACiI,UAAU,EAAEb,QAAQ,GAAGqD,KAAK,CAAC,EAAE;UAC1C1B,OAAO,GAAG,IAAI,CAAC/I,KAAK,CAACiI,UAAU,CAACb,QAAQ,CAACqD,KAAK,CAAC;QACnD;QACA,IAAI,IAAI,CAACzK,KAAK,CAACkH,UAAU,GAAGuD,KAAK,CAAC,EAAE1B,OAAO,EAAE;UACzCA,OAAO,GAAG,IAAI,CAAC/I,KAAK,CAACkH,UAAU,CAACuD,KAAK,CAAC,CAAC1B,OAAO;QAClD;QACA,IAAI,IAAI,CAAC/I,KAAK,CAAC2b,eAAe,GAAGlR,KAAK,CAAC,EAAE1B,OAAO,EAAE;UAC9CA,OAAO,GAAG,IAAI,CAAC/I,KAAK,CAAC2b,eAAe,CAAClR,KAAK,CAAC,CAAC1B,OAAO;QACvD;QACA,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC/B,OAAOA,OAAO,CAACyS,SAAS,CAACC,MAAM,CAAChR,KAAK,CAAC,EAAE,IAAI,CAACzK,KAAK,CAAC;QACvD;QACA,OAAO+I,OAAO;MAClB;IACJ;IACA,OAAOlH,SAAS;EACpB;AACJ;AACAsZ,uBAAuB,CAACtP,IAAI,YAAA+P,gCAAA7P,CAAA;EAAA,YAAAA,CAAA,IAAyFoP,uBAAuB,EA52B5C/e,EAAE,CAAA0T,iBAAA,CA42B4D9I,YAAY;AAAA,CAA4C;AACtNmU,uBAAuB,CAAC1G,IAAI,kBA72BoErY,EAAE,CAAAsY,iBAAA;EAAAvU,IAAA,EA62BOgb,uBAAuB;EAAAlL,SAAA;EAAAC,MAAA;IAAAlQ,KAAA;EAAA;EAAAmQ,QAAA,GA72BhC/T,EAAE,CAAAgU,oBAAA;EAAA8E,KAAA;EAAAC,IAAA;EAAA/U,QAAA,WAAAyb,iCAAA7c,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF5C,EAAE,CAAA0f,MAAA,EA62B2K,CAAC;MA72B9K1f,EAAE,CAAA2f,MAAA;IAAA;IAAA,IAAA/c,EAAA;MAAF5C,EAAE,CAAA4f,iBAAA,CAAF5f,EAAE,CAAA6f,WAAA,OAAAhd,GAAA,CAAAoc,aAAA,CA62B2K,CAAC;IAAA;EAAA;EAAAnE,YAAA,GAAqCxY,IAAI,CAACwd,SAAS;EAAA/E,aAAA;EAAAC,eAAA;AAAA,EAAyD;AAC1X;EAAA,QAAAvM,SAAA,oBAAAA,SAAA,KA92BgGzO,EAAE,CAAAiQ,iBAAA,CA82BN8O,uBAAuB,EAAc,CAAC;IACtHhb,IAAI,EAAEpD,SAAS;IACf6F,IAAI,EAAE,CAAC;MACCyN,QAAQ,EAAE,2BAA2B;MACrCjQ,QAAQ,EAAE,6BAA6B;MACvCgX,eAAe,EAAEla,uBAAuB,CAACoa;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnX,IAAI,EAAE6G;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEhH,KAAK,EAAE,CAAC;MACxFG,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsf,cAAc,SAASlC,SAAS,CAAC;EACnChN,UAAUA,CAACjN,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,CAAC2H,WAAW,IAAIjH,MAAM,CAACV,KAAK,CAAC,EAAE;MACrC,MAAM2N,OAAO,GAAGU,WAAW,CAACrO,KAAK,CAAC;MAClCuO,eAAe,CAACvO,KAAK,EAAE2N,OAAO,GAAGA,OAAO,GAAG,IAAIjQ,SAAS,CAAC,EAAE,EAAE;QAAEqW,QAAQ,EAAE/T,KAAK,CAACiU,YAAY,CAACF;MAAS,CAAC,CAAC,CAAC;IAC5G;IACA/T,KAAK,CAACuG,UAAU,GAAGvG,KAAK,CAACuG,UAAU,IAAI,EAAE;IACzC,MAAM7E,MAAM,GAAGR,KAAK,CAACC,OAAO,CAACnB,KAAK,CAACmC,KAAK,CAAC,GAAGnC,KAAK,CAACmC,KAAK,CAACT,MAAM,GAAG,CAAC;IAClE,IAAI1B,KAAK,CAACuG,UAAU,CAAC7E,MAAM,GAAGA,MAAM,EAAE;MAClC,KAAK,IAAIW,CAAC,GAAGrC,KAAK,CAACuG,UAAU,CAAC7E,MAAM,GAAG,CAAC,EAAEW,CAAC,IAAIX,MAAM,EAAE,EAAEW,CAAC,EAAE;QACxDoL,iBAAiB,CAACzN,KAAK,CAACuG,UAAU,CAAClE,CAAC,CAAC,EAAE,IAAI,CAAC;QAC5CrC,KAAK,CAACuG,UAAU,CAACuH,MAAM,CAACzL,CAAC,EAAE,CAAC,CAAC;MACjC;IACJ;IACA,KAAK,IAAIA,CAAC,GAAGrC,KAAK,CAACuG,UAAU,CAAC7E,MAAM,EAAEW,CAAC,GAAGX,MAAM,EAAEW,CAAC,EAAE,EAAE;MACnD,MAAMiE,CAAC,GAAG;QACN,GAAG9D,KAAK,CAAC,OAAOxC,KAAK,CAACoc,UAAU,KAAK,UAAU,GAAGpc,KAAK,CAACoc,UAAU,CAACpc,KAAK,CAAC,GAAGA,KAAK,CAACoc,UAAU,CAAC;QAC7F5b,GAAG,EAAG,GAAE6B,CAAE;MACd,CAAC;MACDrC,KAAK,CAACuG,UAAU,CAACZ,IAAI,CAACW,CAAC,CAAC;IAC5B;EACJ;EACA+V,GAAGA,CAACha,CAAC,EAAEia,YAAY,EAAE;IAAE5C;EAAY,CAAC,GAAG;IAAEA,WAAW,EAAE;EAAK,CAAC,EAAE;IAC1DrX,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACrC,KAAK,CAACuG,UAAU,CAAC7E,MAAM,GAAGW,CAAC;IAChD,IAAI,CAAC,IAAI,CAACF,KAAK,EAAE;MACbZ,gBAAgB,CAAC,IAAI,CAACvB,KAAK,EAAE,EAAE,CAAC;IACpC;IACA,IAAI,CAACmC,KAAK,CAAC2L,MAAM,CAACzL,CAAC,EAAE,CAAC,EAAEia,YAAY,GAAG9Z,KAAK,CAAC8Z,YAAY,CAAC,GAAGza,SAAS,CAAC;IACvE,IAAI,CAAC+K,MAAM,CAAC,CAAC;IACb8M,WAAW,IAAI,IAAI,CAAC/R,WAAW,CAAC+R,WAAW,CAAC,CAAC;EACjD;EACA6C,MAAMA,CAACla,CAAC,EAAE;IAAEqX;EAAY,CAAC,GAAG;IAAEA,WAAW,EAAE;EAAK,CAAC,EAAE;IAC/C,IAAI,CAACvX,KAAK,CAAC2L,MAAM,CAACzL,CAAC,EAAE,CAAC,CAAC;IACvB,MAAMrC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACuG,UAAU,CAAClE,CAAC,CAAC;IACtC,IAAI,CAACrC,KAAK,CAACuG,UAAU,CAACuH,MAAM,CAACzL,CAAC,EAAE,CAAC,CAAC;IAClC,IAAI,CAACrC,KAAK,CAACuG,UAAU,CAAC1D,OAAO,CAAC,CAACyD,CAAC,EAAE9F,GAAG,KAAM8F,CAAC,CAAC9F,GAAG,GAAI,GAAEA,GAAI,EAAE,CAAC;IAC7DiN,iBAAiB,CAACzN,KAAK,EAAE,IAAI,CAAC;IAC9B,IAAI,CAAC4M,MAAM,CAAC,CAAC;IACb8M,WAAW,IAAI,IAAI,CAAC/R,WAAW,CAAC+R,WAAW,CAAC,CAAC;EACjD;EACA9M,MAAMA,CAAA,EAAG;IACL,MAAMuJ,MAAM,GAAG,IAAI,CAACnW,KAAK,CAAC2H,WAAW,CAACkG,OAAO,IAAI,CAAC,IAAI,CAAC7N,KAAK,CAAC;IAC7DmW,MAAM,CAACtT,OAAO,CAAEyD,CAAC,IAAK,IAAI,CAACwB,OAAO,CAAC2E,KAAK,CAACnG,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACwB,OAAO,CAAC6K,YAAY,CAAC0B,IAAI,CAAC;MAC3BrU,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBwB,KAAK,EAAEiB,aAAa,CAAC,IAAI,CAACzC,KAAK,CAAC;MAChCG,IAAI,EAAE;IACV,CAAC,CAAC;EACN;AACJ;AACAgc,cAAc,CAACtQ,IAAI;EAAA,IAAA2Q,2BAAA;EAAA,gBAAAC,uBAAA1Q,CAAA;IAAA,QAAAyQ,2BAAA,KAAAA,2BAAA,GA36B6EpgB,EAAE,CAAAoe,qBAAA,CA26BU2B,cAAc,IAAApQ,CAAA,IAAdoQ,cAAc;EAAA;AAAA,GAAqD;AAC/KA,cAAc,CAACpM,IAAI,kBA56B6E3T,EAAE,CAAA4T,iBAAA;EAAA7P,IAAA,EA46BFgc,cAAc;EAAAhM,QAAA,GA56Bd/T,EAAE,CAAAye,0BAAA;AAAA,EA46BoD;AACtJ;EAAA,QAAAhQ,SAAA,oBAAAA,SAAA,KA76BgGzO,EAAE,CAAAiQ,iBAAA,CA66BN8P,cAAc,EAAc,CAAC;IAC7Ghc,IAAI,EAAEvD;EACV,CAAC,CAAC;AAAA;AAEV,MAAM8f,YAAY,SAASzC,SAAS,CAAC;EACjC,IAAI0C,cAAcA,CAACC,OAAO,EAAE;IACxB,IAAI,CAACC,cAAc,GAAGD,OAAO;EACjC;AACJ;AACAF,YAAY,CAAC7Q,IAAI;EAAA,IAAAiR,yBAAA;EAAA,gBAAAC,qBAAAhR,CAAA;IAAA,QAAA+Q,yBAAA,KAAAA,yBAAA,GAt7B+E1gB,EAAE,CAAAoe,qBAAA,CAs7BQkC,YAAY,IAAA3Q,CAAA,IAAZ2Q,YAAY;EAAA;AAAA,GAAqD;AAC3KA,YAAY,CAAC3M,IAAI,kBAv7B+E3T,EAAE,CAAA4T,iBAAA;EAAA7P,IAAA,EAu7BJuc,YAAY;EAAA/H,SAAA,WAAAqI,mBAAAhe,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAv7BV5C,EAAE,CAAAyY,WAAA,CAAArV,GAAA,KAu7B+H1C,gBAAgB;MAv7BjJV,EAAE,CAAAyY,WAAA,CAAArV,GAAA,KAu7B0P1C,gBAAgB;IAAA;IAAA,IAAAkC,EAAA;MAAA,IAAA8V,EAAA;MAv7B5Q1Y,EAAE,CAAA2Y,cAAA,CAAAD,EAAA,GAAF1Y,EAAE,CAAA4Y,WAAA,QAAA/V,GAAA,CAAA4d,cAAA,GAAA/H,EAAA,CAAAG,KAAA;MAAF7Y,EAAE,CAAA2Y,cAAA,CAAAD,EAAA,GAAF1Y,EAAE,CAAA4Y,WAAA,QAAA/V,GAAA,CAAA0d,cAAA,GAAA7H,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA9E,QAAA,GAAF/T,EAAE,CAAAye,0BAAA;AAAA,EAu7BmU;AACra;EAAA,QAAAhQ,SAAA,oBAAAA,SAAA,KAx7BgGzO,EAAE,CAAAiQ,iBAAA,CAw7BNqQ,YAAY,EAAc,CAAC;IAC3Gvc,IAAI,EAAEvD;EACV,CAAC,CAAC,QAAkB;IAAEigB,cAAc,EAAE,CAAC;MAC/B1c,IAAI,EAAEnD,SAAS;MACf4F,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAE6S,IAAI,EAAE3Y;MAAiB,CAAC;IACvD,CAAC,CAAC;IAAE6f,cAAc,EAAE,CAAC;MACjBxc,IAAI,EAAEnD,SAAS;MACf4F,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAE6S,IAAI,EAAE3Y,gBAAgB;QAAE4Y,MAAM,EAAE;MAAK,CAAC;IACrE,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMuH,kBAAkB,SAAShD,SAAS,CAAC;EACvC3Z,WAAWA,CAAC4c,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;EACA,IAAI/c,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAACJ,KAAK,IAAI,IAAI,CAACA,KAAK,CAACI,QAAQ,KAAK,IAAI,CAAC+c,SAAS,CAAC/c,QAAQ,EAAE;MAC/D,IAAI,CAAC+c,SAAS,GAAG;QACb/c,QAAQ,EAAE,IAAI,CAACJ,KAAK,CAACI,QAAQ;QAC7Bwc,OAAO,EAAE,IAAI,CAAClO,KAAK,CAAC0O,QAAQ,GACtB,IAAI,CAACF,SAAS,CAACG,uBAAuB,CAAC,IAAI,CAACrd,KAAK,CAACI,QAAQ,CAAC,GAC3D,IAAI,CAACJ,KAAK,CAACI;MACrB,CAAC;IACL;IACA,OAAO,IAAI,CAAC+c,SAAS,CAACP,OAAO;EACjC;AACJ;AACAK,kBAAkB,CAACpR,IAAI,YAAAyR,2BAAAvR,CAAA;EAAA,YAAAA,CAAA,IAAyFkR,kBAAkB,EAr9BlC7gB,EAAE,CAAA0T,iBAAA,CAq9BkDjR,EAAE,CAAC0e,YAAY;AAAA,CAA4C;AAC/MN,kBAAkB,CAACxI,IAAI,kBAt9ByErY,EAAE,CAAAsY,iBAAA;EAAAvU,IAAA,EAs9BE8c,kBAAkB;EAAAhN,SAAA;EAAAE,QAAA,GAt9BtB/T,EAAE,CAAAye,0BAAA;EAAA3F,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAhV,QAAA,WAAAod,4BAAAxe,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF5C,EAAE,CAAA+C,SAAA,YAs9BoI,CAAC;IAAA;IAAA,IAAAH,EAAA;MAt9BvI5C,EAAE,CAAAkD,UAAA,cAAAL,GAAA,CAAAmB,QAAA,EAAFhE,EAAE,CAAAqhB,cAs9B6H,CAAC;IAAA;EAAA;EAAAtG,aAAA;EAAAC,eAAA;AAAA,EAA+E;AAC/S;EAAA,QAAAvM,SAAA,oBAAAA,SAAA,KAv9BgGzO,EAAE,CAAAiQ,iBAAA,CAu9BN4Q,kBAAkB,EAAc,CAAC;IACjH9c,IAAI,EAAEpD,SAAS;IACf6F,IAAI,EAAE,CAAC;MACCyN,QAAQ,EAAE,iBAAiB;MAC3BjQ,QAAQ,EAAG,oCAAmC;MAC9CgX,eAAe,EAAEla,uBAAuB,CAACoa;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnX,IAAI,EAAEtB,EAAE,CAAC0e;IAAa,CAAC,CAAC;EAAE,CAAC;AAAA;AAE/E,SAASG,oBAAoBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAChD,IAAI;IACA,OAAOC,QAAQ,CAAC,GAAGD,QAAQ,EAAG,UAASD,UAAW,GAAE,CAAC;EACzD,CAAC,CACD,OAAOlT,KAAK,EAAE;IACVD,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,SAASqT,cAAcA,CAACH,UAAU,EAAEI,OAAO,EAAEC,MAAM,EAAE;EACjD,IAAI,OAAOL,UAAU,KAAK,UAAU,EAAE;IAClC,OAAOA,UAAU,CAACM,KAAK,CAACF,OAAO,EAAEC,MAAM,CAAC;EAC5C,CAAC,MACI;IACD,OAAOL,UAAU,GAAG,IAAI,GAAG,KAAK;EACpC;AACJ;AAEA,MAAMO,wBAAwB,CAAC;EAC3BjR,UAAUA,CAACjN,KAAK,EAAE;IACd,IAAIA,KAAK,CAACwT,YAAY,EAAE;MACpB;IACJ;IACA;IACAnS,gBAAgB,CAACrB,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;IAC3CwF,OAAO,CAACxF,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;MAAE0F,YAAY;MAAED;IAAY,CAAC,KAAK;MACxDpE,gBAAgB,CAACrB,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC0F,YAAY,CAAC;MAChD,IAAI,CAACD,WAAW,IAAKA,WAAW,IAAIC,YAAY,KAAK,IAAK,EAAE;QACxD1F,KAAK,CAAC0O,KAAK,CAACyP,MAAM,GAAGzY,YAAY;QACjC1F,KAAK,CAAC8H,OAAO,CAACsW,qBAAqB,CAACzY,IAAI,CAAC3F,KAAK,CAAC;MACnD;IACJ,CAAC,CAAC;IACF,IAAIA,KAAK,CAACqe,cAAc,EAAE;MACtB7Y,OAAO,CAACxF,KAAK,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAAE0F,YAAY,EAAE4Y;MAAK,CAAC,KAAK;QAC3Dte,KAAK,CAACwT,YAAY,CAACF,IAAI,GAAG,IAAI,CAACiL,gBAAgB,CAACve,KAAK,EAAE,MAAM,EAAE,OAAOse,IAAI,KAAK,SAAS,GAAG,MAAMA,IAAI,GAAGA,IAAI,CAAC;MACjH,CAAC,CAAC;IACN;IACA,MAAME,QAAQ,GAAGA,CAAChe,GAAG,EAAE8d,IAAI,KAAK;MAC5B,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAInb,UAAU,CAACmb,IAAI,CAAC,EAAE;QAC9Cte,KAAK,CAACwT,YAAY,CAAChT,GAAG,CAAC,GAAG,IAAI,CAAC+d,gBAAgB,CAACve,KAAK,EAAEQ,GAAG,EAAE8d,IAAI,CAAC;MACrE,CAAC,MACI,IAAIA,IAAI,YAAYtgB,UAAU,EAAE;QACjCgC,KAAK,CAACwT,YAAY,CAAChT,GAAG,CAAC,GAAG;UACtBmT,MAAM,EAAE2K,IAAI,CAACzK,IAAI,CAACrV,GAAG,CAAE0F,CAAC,IAAK;YACzB,IAAI,CAACsa,QAAQ,CAACxe,KAAK,EAAEQ,GAAG,EAAE0D,CAAC,CAAC;YAC5BlE,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC;UACtC,CAAC,CAAC;QACN,CAAC;MACL;IACJ,CAAC;IACDA,KAAK,CAACye,WAAW,GAAGze,KAAK,CAACye,WAAW,IAAI,CAAC,CAAC;IAC3C,KAAK,MAAMje,GAAG,IAAI8C,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAACye,WAAW,CAAC,EAAE;MAC9CjZ,OAAO,CAACxF,KAAK,EAAE,CAAC,aAAa,EAAEQ,GAAG,CAAC,EAAE,CAAC;QAAEkF,YAAY,EAAE4Y;MAAK,CAAC,KAAK;QAC7DE,QAAQ,CAAChe,GAAG,EAAE2C,UAAU,CAACmb,IAAI,CAAC,GAAG,CAAC,GAAG1b,IAAI,KAAK0b,IAAI,CAACte,KAAK,EAAE4C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG0b,IAAI,CAAC;MAC9E,CAAC,CAAC;IACN;IACAte,KAAK,CAAC0e,oBAAoB,GAAG1e,KAAK,CAAC0e,oBAAoB,IAAI,CAAC,CAAC;IAC7D,KAAK,MAAMle,GAAG,IAAI8C,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAAC0e,oBAAoB,CAAC,EAAE;MACvDlZ,OAAO,CAACxF,KAAK,EAAE,CAAC,sBAAsB,EAAEQ,GAAG,CAAC,EAAE,CAAC;QAAEkF;MAAa,CAAC,KAAK8Y,QAAQ,CAAChe,GAAG,EAAEkF,YAAY,CAAC,CAAC;IACpG;EACJ;EACAwH,YAAYA,CAAClN,KAAK,EAAE;IAChB,IAAIA,KAAK,CAAC4B,MAAM,EAAE;MACd;IACJ;IACA,IAAI,CAAC5B,KAAK,CAAC8H,OAAO,CAAC+E,gBAAgB,EAAE;MACjC,IAAI8R,WAAW,GAAG,KAAK;MACvB3e,KAAK,CAAC8H,OAAO,CAAC+E,gBAAgB,GAAG,CAACvG,CAAC,EAAEsY,WAAW,KAAK;QACjD,IAAID,WAAW,EAAE;UACb;QACJ;QACAA,WAAW,GAAG,IAAI;QAClB,MAAME,YAAY,GAAG,IAAI,CAAChS,gBAAgB,CAACvG,CAAC,EAAEsY,WAAW,CAAC;QAC1D,MAAM9W,OAAO,GAAG9H,KAAK,CAAC8H,OAAO;QAC7BA,OAAO,CAACsW,qBAAqB,CACxB5S,IAAI,CAAElF,CAAC,IAAMA,CAAC,CAACgN,IAAI,GAAG,CAAC,CAAC,GAAG,CAAE,CAAC,CAC9BzQ,OAAO,CAAEyD,CAAC,IAAK,IAAI,CAACwY,eAAe,CAACxY,CAAC,EAAEA,CAAC,CAACgN,IAAI,EAAE,CAACsL,WAAW,CAAC,CAAC;QAClE9W,OAAO,CAACsW,qBAAqB,GAAG,EAAE;QAClC,IAAIS,YAAY,EAAE;UACd,IAAI,CAAChS,gBAAgB,CAAC7M,KAAK,CAAC;UAC5B,IAAIA,KAAK,CAAC8H,OAAO,IAAI9H,KAAK,CAAC8H,OAAO,CAACgF,aAAa,EAAE;YAC9C9M,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC;UACtC;QACJ;QACA2e,WAAW,GAAG,KAAK;MACvB,CAAC;MACD3e,KAAK,CAAC8H,OAAO,CAACiX,WAAW,GAAG,CAACzY,CAAC,EAAEsY,WAAW,KAAK;QAC5CpU,OAAO,CAACS,IAAI,CAAE,iGAAgG,CAAC;QAC/GjL,KAAK,CAAC8H,OAAO,CAAC+E,gBAAgB,CAACvG,CAAC,EAAEsY,WAAW,CAAC;MAClD,CAAC;IACL;EACJ;EACAL,gBAAgBA,CAACve,KAAK,EAAEc,IAAI,EAAEwd,IAAI,EAAE;IAChC,IAAIU,gBAAgB;IACpB,IAAIhf,KAAK,CAAC4B,MAAM,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAACqd,QAAQ,CAACne,IAAI,CAAC,EAAE;MAC3D,MAAMoe,SAAS,GAAI5Y,CAAC,IAAK;QACrB,OAAOxF,IAAI,KAAK,MAAM,GAAGwF,CAAC,CAACgN,IAAI,GAAGhN,CAAC,CAACoI,KAAK,CAACC,QAAQ;MACtD,CAAC;MACDqQ,gBAAgB,GAAGA,CAAA,KAAM;QACrB,IAAIrd,IAAI,GAAG3B,KAAK,CAAC4B,MAAM;QACvB,OAAOD,IAAI,CAACC,MAAM,IAAI,CAACsd,SAAS,CAACvd,IAAI,CAAC,EAAE;UACpCA,IAAI,GAAGA,IAAI,CAACC,MAAM;QACtB;QACA,OAAOsd,SAAS,CAACvd,IAAI,CAAC;MAC1B,CAAC;IACL;IACA2c,IAAI,GAAGA,IAAI,KAAK,MAAM,KAAK,CAAC;IAC5B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1BA,IAAI,GAAGZ,oBAAoB,CAACY,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IACtE;IACA,IAAI5Y,YAAY;IAChB,OAAO;MACH/F,QAAQ,EAAGif,WAAW,IAAK;QACvB,IAAI;UACA,MAAMO,SAAS,GAAGrB,cAAc,CAACkB,gBAAgB,GAAG,CAAC,GAAGpc,IAAI,KAAKoc,gBAAgB,CAAChf,KAAK,CAAC,IAAIse,IAAI,CAAC,GAAG1b,IAAI,CAAC,GAAG0b,IAAI,EAAE;YAAEte;UAAM,CAAC,EAAE,CAACA,KAAK,CAACmC,KAAK,EAAEnC,KAAK,CAAC8H,OAAO,CAACqS,SAAS,EAAEna,KAAK,EAAE4e,WAAW,CAAC,CAAC;UACxL,IAAIA,WAAW,IACVlZ,YAAY,KAAKyZ,SAAS,KACtB,CAAC7c,QAAQ,CAAC6c,SAAS,CAAC,IACjBthB,YAAY,CAACshB,SAAS,CAAC,IACvBC,IAAI,CAACC,SAAS,CAACF,SAAS,CAAC,KAAKC,IAAI,CAACC,SAAS,CAAC3Z,YAAY,CAAC,CAAE,EAAE;YACtEA,YAAY,GAAGyZ,SAAS;YACxB,IAAI,CAACX,QAAQ,CAACxe,KAAK,EAAEc,IAAI,EAAEqe,SAAS,CAAC;YACrC,OAAO,IAAI;UACf;UACA,OAAO,KAAK;QAChB,CAAC,CACD,OAAO1U,KAAK,EAAE;UACVA,KAAK,CAAC1B,OAAO,GAAI,+BAA8BjI,IAAK,MAAK2J,KAAK,CAAC1B,OAAQ,EAAC;UACxE,MAAM0B,KAAK;QACf;MACJ;IACJ,CAAC;EACL;EACAoC,gBAAgBA,CAAC7M,KAAK,EAAE4e,WAAW,GAAG,KAAK,EAAE;IACzC,IAAI,CAAC5e,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAI6e,YAAY,GAAG,KAAK;IACxB,IAAI7e,KAAK,CAACwT,YAAY,EAAE;MACpB,KAAK,MAAMhT,GAAG,IAAI8C,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAACwT,YAAY,CAAC,EAAE;QAC/CxT,KAAK,CAACwT,YAAY,CAAChT,GAAG,CAAC,CAACb,QAAQ,GAAGif,WAAW,CAAC,KAAKC,YAAY,GAAG,IAAI,CAAC;MAC5E;IACJ;IACA7e,KAAK,CAACuG,UAAU,EAAE1D,OAAO,CAAEyD,CAAC,IAAK,IAAI,CAACuG,gBAAgB,CAACvG,CAAC,EAAEsY,WAAW,CAAC,KAAKC,YAAY,GAAG,IAAI,CAAC,CAAC;IAChG,OAAOA,YAAY;EACvB;EACAS,mBAAmBA,CAACtf,KAAK,EAAEwB,KAAK,EAAE;IAC9B,IAAIxB,KAAK,CAACuG,UAAU,EAAE;MAClBvG,KAAK,CAACuG,UAAU,CACXlI,MAAM,CAAEiI,CAAC,IAAK,CAACA,CAAC,CAACkN,YAAY,CAACpK,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAC/DvG,OAAO,CAAEyD,CAAC,IAAK,IAAI,CAACgZ,mBAAmB,CAAChZ,CAAC,EAAE9E,KAAK,CAAC,CAAC;IAC3D;IACA,IAAId,MAAM,CAACV,KAAK,CAAC,IAAIA,KAAK,CAAC0O,KAAK,CAACC,QAAQ,KAAKnN,KAAK,EAAE;MACjDxB,KAAK,CAAC0O,KAAK,CAACC,QAAQ,GAAGnN,KAAK;IAChC;EACJ;EACAsd,eAAeA,CAAC9e,KAAK,EAAEsT,IAAI,EAAExR,WAAW,EAAE;IACtC,IAAI9B,KAAK,CAACuG,UAAU,EAAE;MAClBvG,KAAK,CAACuG,UAAU,CACXlI,MAAM,CAAEiI,CAAC,IAAK,CAACA,CAAC,CAACkN,YAAY,CAACF,IAAI,CAAC,CACnCzQ,OAAO,CAAEyD,CAAC,IAAK,IAAI,CAACwY,eAAe,CAACxY,CAAC,EAAEgN,IAAI,EAAExR,WAAW,CAAC,CAAC;IACnE;IACA,IAAI9B,KAAK,CAAC2H,WAAW,IAAIjH,MAAM,CAACV,KAAK,CAAC,EAAE;MACpCqB,gBAAgB,CAACrB,KAAK,EAAE,OAAO,EAAE,CAAC,EAAEsT,IAAI,IAAItT,KAAK,CAACsT,IAAI,CAAC,CAAC;MACxD,MAAMlP,CAAC,GAAGpE,KAAK,CAAC2H,WAAW;MAC3B,IAAIvD,CAAC,CAACyJ,OAAO,EAAEnM,MAAM,GAAG,CAAC,EAAE;QACvByN,cAAc,CAAC/K,CAAC,CAAC;MACrB;MACA,IAAIkP,IAAI,KAAK,IAAI,KAAK,CAAClP,CAAC,CAACyJ,OAAO,IAAIzJ,CAAC,CAACyJ,OAAO,CAAC0R,KAAK,CAAEjZ,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC4I,KAAK,CAAC,CAAC,EAAE;QACpEzB,iBAAiB,CAACzN,KAAK,EAAE,IAAI,CAAC;QAC9B,IAAI8B,WAAW,IAAI9B,KAAK,CAAC8B,WAAW,EAAE;UAClCP,gBAAgB,CAACvB,KAAK,EAAE6B,SAAS,CAAC;UAClC7B,KAAK,CAAC2H,WAAW,CAAC6X,KAAK,CAAC;YAAEhe,KAAK,EAAEK,SAAS;YAAE8M,QAAQ,EAAE3O,KAAK,CAAC2H,WAAW,CAACgH;UAAS,CAAC,CAAC;UACnF3O,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,CAAC0B,IAAI,CAAC;YAAE7S,KAAK,EAAEK,SAAS;YAAE7B,KAAK;YAAEG,IAAI,EAAE;UAAe,CAAC,CAAC;UAClF,IAAIH,KAAK,CAACuG,UAAU,IAAIvG,KAAK,CAAC2H,WAAW,YAAYjK,SAAS,EAAE;YAC5DsC,KAAK,CAACuG,UAAU,CAAC7E,MAAM,GAAG,CAAC;UAC/B;QACJ;MACJ,CAAC,MACI,IAAI4R,IAAI,KAAK,KAAK,EAAE;QACrB,IAAItT,KAAK,CAAC8B,WAAW,IAAI,CAACoB,WAAW,CAAClD,KAAK,CAAC8E,YAAY,CAAC,IAAI5B,WAAW,CAACT,aAAa,CAACzC,KAAK,CAAC,CAAC,EAAE;UAC5FuB,gBAAgB,CAACvB,KAAK,EAAEA,KAAK,CAAC8E,YAAY,CAAC;QAC/C;QACAyJ,eAAe,CAACvO,KAAK,EAAE6B,SAAS,EAAE,IAAI,CAAC;QACvC,IAAI7B,KAAK,CAAC8B,WAAW,IAAI9B,KAAK,CAACoc,UAAU,IAAIpc,KAAK,CAACuG,UAAU,EAAE7E,MAAM,KAAK1B,KAAK,CAACmC,KAAK,EAAET,MAAM,EAAE;UAC3F1B,KAAK,CAAC8H,OAAO,CAAC2E,KAAK,CAACzM,KAAK,CAAC;QAC9B;MACJ;IACJ;IACA,IAAIA,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,EAAE;MAC5B3S,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,CAAC0B,IAAI,CAAC;QAAErU,KAAK;QAAEG,IAAI,EAAE,QAAQ;QAAEqB,KAAK,EAAE8R;MAAK,CAAC,CAAC;IAC3E;EACJ;EACAkL,QAAQA,CAACxe,KAAK,EAAEyE,IAAI,EAAEjD,KAAK,EAAE;IACzB,IAAIiD,IAAI,CAAC1D,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC9B,MAAMP,GAAG,GAAGiE,IAAI,CAACzD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QAAEY,MAAM,GAAG5B,KAAK,CAACuG,UAAU,GAAGvG,KAAK,GAAGA,KAAK,CAAC4B,MAAM;MAC1F,IAAI+L,OAAO,GAAG3N,KAAK,EAAEQ,GAAG,KAAKA,GAAG,GAAGR,KAAK,CAAC2H,WAAW,GAAG3H,KAAK,CAACN,IAAI,CAACkF,GAAG,CAACpE,GAAG,CAAC;MAC1E,IAAI,CAACmN,OAAO,IAAI3N,KAAK,CAAC4E,GAAG,CAACpE,GAAG,CAAC,EAAE;QAC5BmN,OAAO,GAAG3N,KAAK,CAAC4E,GAAG,CAACpE,GAAG,CAAC,CAACmH,WAAW;MACxC;MACApG,gBAAgB,CAAC;QAAEf,GAAG;QAAEoB,MAAM;QAAEO,KAAK,EAAEnC,KAAK,CAACmC;MAAM,CAAC,EAAEX,KAAK,CAAC;MAC5D,IAAImM,OAAO,IAAI,EAAEhN,KAAK,CAACgN,OAAO,CAACnM,KAAK,CAAC,IAAIb,KAAK,CAACa,KAAK,CAAC,CAAC,IAAImM,OAAO,CAACnM,KAAK,KAAKA,KAAK,EAAE;QAC/EmM,OAAO,CAACqB,UAAU,CAACxN,KAAK,CAAC;MAC7B;IACJ,CAAC,MACI;MACD,IAAI;QACA,IAAIsE,MAAM,GAAG9F,KAAK;QAClB,MAAMyB,KAAK,GAAG,IAAI,CAACge,mBAAmB,CAACzf,KAAK,EAAEyE,IAAI,CAAC;QACnD,MAAMib,SAAS,GAAGje,KAAK,CAACC,MAAM,GAAG,CAAC;QAClC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqd,SAAS,EAAErd,CAAC,EAAE,EAAE;UAChCyD,MAAM,GAAGA,MAAM,CAACrE,KAAK,CAACY,CAAC,CAAC,CAAC;QAC7B;QACAyD,MAAM,CAACrE,KAAK,CAACie,SAAS,CAAC,CAAC,GAAGle,KAAK;MACpC,CAAC,CACD,OAAOiJ,KAAK,EAAE;QACVA,KAAK,CAAC1B,OAAO,GAAI,+BAA8BtE,IAAK,MAAKgG,KAAK,CAAC1B,OAAQ,EAAC;QACxE,MAAM0B,KAAK;MACf;MACA,IAAI,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAACwU,QAAQ,CAACxa,IAAI,CAAC,IAAI/D,MAAM,CAACV,KAAK,CAAC,EAAE;QAChF,IAAI,CAACsf,mBAAmB,CAACtf,KAAK,EAAEwB,KAAK,CAAC;MAC1C;IACJ;IACA,IAAI,CAACme,qBAAqB,CAAC3f,KAAK,EAAEyE,IAAI,EAAEjD,KAAK,CAAC;EAClD;EACAme,qBAAqBA,CAAC3f,KAAK,EAAEsb,QAAQ,EAAE9Z,KAAK,EAAE;IAC1C,IAAI,CAACxB,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,EAAE;MAC7B;IACJ;IACA3S,KAAK,CAAC8H,OAAO,CAAC6K,YAAY,CAAC0B,IAAI,CAAC;MAC5BrU,KAAK;MACLG,IAAI,EAAE,mBAAmB;MACzBmb,QAAQ;MACR9Z;IACJ,CAAC,CAAC;EACN;EACAie,mBAAmBA,CAACzf,KAAK,EAAEyE,IAAI,EAAE;IAC7B,IAAIzE,KAAK,CAACwT,YAAY,CAAC/O,IAAI,CAAC,IAAIzE,KAAK,CAACwT,YAAY,CAAC/O,IAAI,CAAC,CAAChD,KAAK,EAAE;MAC5D,OAAOzB,KAAK,CAACwT,YAAY,CAAC/O,IAAI,CAAC,CAAChD,KAAK;IACzC;IACA,IAAIA,KAAK,GAAG,EAAE;IACd,IAAIgD,IAAI,CAAC1D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC1BU,KAAK,GAAGgD,IAAI,CAACxD,KAAK,CAAC,GAAG,CAAC;IAC3B,CAAC,MACI;MACDwD,IAAI,CACCxD,KAAK,CAAC,YAAY,CAAC,CAAC;MAAA,CACpB5C,MAAM,CAAEmY,CAAC,IAAKA,CAAC,CAAC,CAChB3T,OAAO,CAAE/B,IAAI,IAAK;QACnB,MAAM8e,SAAS,GAAG9e,IAAI,CAAC+e,KAAK,CAAC,iBAAiB,CAAC;QAC/C,IAAID,SAAS,EAAE;UACXne,KAAK,CAACkE,IAAI,CAACia,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,MACI;UACDne,KAAK,CAACkE,IAAI,CAAC,GAAG7E,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC5C,MAAM,CAAEmY,CAAC,IAAKA,CAAC,CAAC,CAAC;QACnD;MACJ,CAAC,CAAC;IACN;IACA,IAAIxW,KAAK,CAACwT,YAAY,CAAC/O,IAAI,CAAC,EAAE;MAC1BzE,KAAK,CAACwT,YAAY,CAAC/O,IAAI,CAAC,CAAChD,KAAK,GAAGA,KAAK;IAC1C;IACA,OAAOA,KAAK;EAChB;AACJ;AAEA,MAAMqe,wBAAwB,CAAC;EAC3Bxf,WAAWA,CAACiI,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA0E,UAAUA,CAACjN,KAAK,EAAE;IACd,IAAI,CAAC+f,mBAAmB,CAAC/f,KAAK,EAAE,YAAY,CAAC;IAC7C,IAAI,CAAC+f,mBAAmB,CAAC/f,KAAK,EAAE,iBAAiB,CAAC;EACtD;EACA+f,mBAAmBA,CAAC/f,KAAK,EAAEG,IAAI,EAAE;IAC7B,MAAM+G,UAAU,GAAG,EAAE;IACrB,IAAI/G,IAAI,KAAK,YAAY,IAAI,EAAEH,KAAK,CAACoJ,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC1I,MAAM,CAACV,KAAK,CAAC,CAAC,EAAE;MAClFkH,UAAU,CAACvB,IAAI,CAAC,IAAI,CAACqa,4BAA4B,CAAChgB,KAAK,CAAC,CAAC;IAC7D;IACA,IAAIA,KAAK,CAACG,IAAI,CAAC,EAAE;MACb,KAAK,MAAM8f,aAAa,IAAI3c,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE;QAClD8f,aAAa,KAAK,YAAY,GACxB/Y,UAAU,CAACvB,IAAI,CAAC,GAAG3F,KAAK,CAACG,IAAI,CAAC,CAAC8H,UAAU,CAACxJ,GAAG,CAAEyF,CAAC,IAAK,IAAI,CAACgc,iBAAiB,CAAClgB,KAAK,EAAEkE,CAAC,CAAC,CAAC,CAAC,GACvFgD,UAAU,CAACvB,IAAI,CAAC,IAAI,CAACua,iBAAiB,CAAClgB,KAAK,EAAEA,KAAK,CAACG,IAAI,CAAC,CAAC8f,aAAa,CAAC,EAAEA,aAAa,CAAC,CAAC;MACnG;IACJ;IACA5e,gBAAgB,CAACrB,KAAK,EAAE,GAAG,GAAGG,IAAI,EAAE+G,UAAU,CAAC;EACnD;EACA8Y,4BAA4BA,CAAChgB,KAAK,EAAE;IAChC,IAAImgB,UAAU,GAAG,EAAE;IACnB7e,iBAAiB,CAACuB,OAAO,CAAEud,GAAG,IAAK5a,OAAO,CAACxF,KAAK,EAAE,CAAC,OAAO,EAAEogB,GAAG,CAAC,EAAE,CAAC;MAAE1a,YAAY;MAAED;IAAY,CAAC,KAAK;MACjG0a,UAAU,GAAGA,UAAU,CAAC9hB,MAAM,CAAEuH,CAAC,IAAKA,CAAC,KAAKwa,GAAG,CAAC;MAChD,IAAI1a,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,KAAK,EAAE;QAChDya,UAAU,CAACxa,IAAI,CAACya,GAAG,CAAC;MACxB;MACA,IAAI,CAAC3a,WAAW,IAAIzF,KAAK,CAAC2H,WAAW,EAAE;QACnCwH,cAAc,CAACnP,KAAK,CAAC2H,WAAW,CAAC;MACrC;IACJ,CAAC,CAAC,CAAC;IACH,OAAQgG,OAAO,IAAK;MAChB,IAAIwS,UAAU,CAACze,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,IAAI;MACf;MACA,OAAO9D,UAAU,CAACyiB,OAAO,CAACF,UAAU,CAAC1hB,GAAG,CAAE2hB,GAAG,IAAK,MAAM;QACpD,MAAM5e,KAAK,GAAGxB,KAAK,CAAC0O,KAAK,CAAC0R,GAAG,CAAC;QAC9B,QAAQA,GAAG;UACP,KAAK,UAAU;YACX,OAAOxiB,UAAU,CAAC0iB,QAAQ,CAAC3S,OAAO,CAAC;UACvC,KAAK,SAAS;YACV,OAAO/P,UAAU,CAAC2iB,OAAO,CAAC/e,KAAK,CAAC,CAACmM,OAAO,CAAC;UAC7C,KAAK,WAAW;YACZ,MAAM6S,eAAe,GAAG5iB,UAAU,CAAC6iB,SAAS,CAACjf,KAAK,CAAC,CAACmM,OAAO,CAAC;YAC5D,MAAM+S,YAAY,GAAG,IAAI,CAACnY,MAAM,CAAC2C,mBAAmB,CAAC,WAAW,CAAC,IAAIlL,KAAK,CAACiI,UAAU,EAAEb,QAAQ,EAAE2D,SAAS,GACpG,WAAW,GACX,WAAW;YACjB,OAAOyV,eAAe,GAAG;cAAE,CAACE,YAAY,GAAGF,eAAe,CAACzV;YAAU,CAAC,GAAG,IAAI;UACjF,KAAK,WAAW;YACZ,MAAM4V,eAAe,GAAG/iB,UAAU,CAACgjB,SAAS,CAACpf,KAAK,CAAC,CAACmM,OAAO,CAAC;YAC5D,MAAMkT,YAAY,GAAG,IAAI,CAACtY,MAAM,CAAC2C,mBAAmB,CAAC,WAAW,CAAC,IAAIlL,KAAK,CAACiI,UAAU,EAAEb,QAAQ,EAAE4D,SAAS,GACpG,WAAW,GACX,WAAW;YACjB,OAAO2V,eAAe,GAAG;cAAE,CAACE,YAAY,GAAGF,eAAe,CAAC3V;YAAU,CAAC,GAAG,IAAI;UACjF,KAAK,KAAK;YACN,OAAOpN,UAAU,CAACkjB,GAAG,CAACtf,KAAK,CAAC,CAACmM,OAAO,CAAC;UACzC,KAAK,KAAK;YACN,OAAO/P,UAAU,CAACmjB,GAAG,CAACvf,KAAK,CAAC,CAACmM,OAAO,CAAC;UACzC;YACI,OAAO,IAAI;QACnB;MACJ,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC;IAChB,CAAC;EACL;EACAuS,iBAAiBA,CAAClgB,KAAK,EAAEyI,SAAS,EAAEwX,aAAa,EAAE;IAC/C,IAAIe,eAAe;IACnB,IAAI,OAAOvY,SAAS,KAAK,QAAQ,EAAE;MAC/BuY,eAAe,GAAGxe,KAAK,CAAC,IAAI,CAAC+F,MAAM,CAACqC,YAAY,CAACnC,SAAS,CAAC,CAAC;IAChE;IACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAAClI,IAAI,EAAE;MACjDygB,eAAe,GAAGxe,KAAK,CAAC,IAAI,CAAC+F,MAAM,CAACqC,YAAY,CAACnC,SAAS,CAAClI,IAAI,CAAC,CAAC;MACjE,IAAIkI,SAAS,CAACX,OAAO,EAAE;QACnBkZ,eAAe,CAAClZ,OAAO,GAAGW,SAAS,CAACX,OAAO;MAC/C;IACJ;IACA,IAAI,OAAOW,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACkV,UAAU,EAAE;MACvD,MAAM;QAAEA,UAAU;QAAE,GAAG7V;MAAQ,CAAC,GAAGW,SAAS;MAC5CuY,eAAe,GAAG;QACdzgB,IAAI,EAAE0f,aAAa;QACnBhY,UAAU,EAAE0V,UAAU;QACtB7V,OAAO,EAAExE,MAAM,CAACiB,IAAI,CAACuD,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,GAAGoG,OAAO,GAAG;MACzD,CAAC;IACL;IACA,IAAI,OAAOW,SAAS,KAAK,UAAU,EAAE;MACjCuY,eAAe,GAAG;QACdzgB,IAAI,EAAE0f,aAAa;QACnBhY,UAAU,EAAEQ;MAChB,CAAC;IACL;IACA,OAAQkF,OAAO,IAAK;MAChB,MAAM8N,MAAM,GAAGuF,eAAe,CAAC/Y,UAAU,CAAC0F,OAAO,EAAE3N,KAAK,EAAEghB,eAAe,CAAClZ,OAAO,CAAC;MAClF,IAAIrE,SAAS,CAACgY,MAAM,CAAC,EAAE;QACnB,OAAOA,MAAM,CAAC9X,IAAI,CAAEO,CAAC,IAAK,IAAI,CAAC+c,iBAAiB,CAACjhB,KAAK,EAAEigB,aAAa,GAAG,CAAC,CAAC/b,CAAC,GAAGA,CAAC,EAAE8c,eAAe,CAAC,CAAC;MACtG;MACA,IAAInjB,YAAY,CAAC4d,MAAM,CAAC,EAAE;QACtB,OAAOA,MAAM,CAAC5H,IAAI,CAACpV,GAAG,CAAEyF,CAAC,IAAK,IAAI,CAAC+c,iBAAiB,CAACjhB,KAAK,EAAEigB,aAAa,GAAG,CAAC,CAAC/b,CAAC,GAAGA,CAAC,EAAE8c,eAAe,CAAC,CAAC,CAAC;MAC3G;MACA,OAAO,IAAI,CAACE,YAAY,CAAClhB,KAAK,EAAEigB,aAAa,GAAG,CAAC,CAACxE,MAAM,GAAGA,MAAM,EAAEuF,eAAe,CAAC;IACvF,CAAC;EACL;EACAC,iBAAiBA,CAACjhB,KAAK,EAAEyb,MAAM,EAAE3T,OAAO,EAAE;IACtC;IACA9H,KAAK,CAAC8H,OAAO,CAACgF,aAAa,CAAC9M,KAAK,CAAC;IAClC,OAAO,IAAI,CAACkhB,YAAY,CAAClhB,KAAK,EAAEyb,MAAM,EAAE3T,OAAO,CAAC;EACpD;EACAoZ,YAAYA,CAAClhB,KAAK,EAAEyb,MAAM,EAAE;IAAElb,IAAI;IAAEuH;EAAQ,CAAC,EAAE;IAC3C,IAAI,OAAO2T,MAAM,KAAK,SAAS,EAAE;MAC7BA,MAAM,GAAGA,MAAM,GAAG,IAAI,GAAG;QAAE,CAAClb,IAAI,GAAGuH,OAAO,GAAGA,OAAO,GAAG;MAAK,CAAC;IACjE;IACA,MAAMqZ,IAAI,GAAGnhB,KAAK,CAAC2H,WAAW;IAC9BwZ,IAAI,EAAEC,eAAe,GAAG7gB,IAAI,CAAC,GAAG,CAAC;IACjC,IAAI+B,QAAQ,CAACmZ,MAAM,CAAC,EAAE;MAClBnY,MAAM,CAACiB,IAAI,CAACkX,MAAM,CAAC,CAAC5Y,OAAO,CAAEtC,IAAI,IAAK;QAClC,MAAMmb,SAAS,GAAGD,MAAM,CAAClb,IAAI,CAAC,CAACmb,SAAS,GAAGD,MAAM,CAAClb,IAAI,CAAC,CAACmb,SAAS,GAAG5T,OAAO,EAAE4T,SAAS;QACtF,MAAM2F,SAAS,GAAG3F,SAAS,GAAG1b,KAAK,CAAC2H,WAAW,CAAC/C,GAAG,CAAC8W,SAAS,CAAC,GAAG,IAAI;QACrE,IAAI2F,SAAS,EAAE;UACX,MAAM;YAAE3F,SAAS,EAAE4F,UAAU;YAAE,GAAGvT;UAAK,CAAC,GAAG0N,MAAM,CAAClb,IAAI,CAAC;UACvD8gB,SAAS,CAACE,SAAS,CAAC;YAAE,IAAIF,SAAS,CAAC5F,MAAM,IAAI,CAAC,CAAC,CAAC;YAAE,CAAClb,IAAI,GAAGwN;UAAK,CAAC,CAAC;UAClE,CAACoT,IAAI,CAACC,eAAe,IAAI/f,gBAAgB,CAAC8f,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;UACtEA,IAAI,CAACC,eAAe,CAAC7gB,IAAI,CAAC,GAAG,MAAM;YAC/B,MAAM;cAAE,CAACA,IAAI,GAAGihB,SAAS;cAAE,GAAGC;YAAY,CAAC,GAAGJ,SAAS,CAAC5F,MAAM,IAAI,CAAC,CAAC;YACpE4F,SAAS,CAACE,SAAS,CAACje,MAAM,CAACiB,IAAI,CAACkd,WAAW,CAAC,CAAC/f,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG+f,WAAW,CAAC;UACnF,CAAC;QACL;MACJ,CAAC,CAAC;IACN;IACA,OAAOhG,MAAM;EACjB;AACJ;AAEA,MAAMiG,kBAAkB,CAAC;EACrB1U,WAAWA,CAAChN,KAAK,EAAE;IACf,IAAI,CAAC,IAAI,CAAC2B,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG3B,KAAK;IACrB;IACA,IAAIA,KAAK,CAAC4B,MAAM,EAAE;MACd0B,MAAM,CAACuB,cAAc,CAAC7E,KAAK,EAAE,MAAM,EAAE;QACjC4E,GAAG,EAAEA,CAAA,KAAM5E,KAAK,CAAC4B,MAAM,CAAC+F,WAAW;QACnC1C,YAAY,EAAE;MAClB,CAAC,CAAC;IACN;EACJ;EACAgI,UAAUA,CAACjN,KAAK,EAAE;IACd,IAAIA,KAAK,CAACoJ,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC1I,MAAM,CAACV,KAAK,CAAC,EAAE;MACtDqB,gBAAgB,CAACrB,KAAK,EAAE,aAAa,EAAEA,KAAK,CAACN,IAAI,CAAC;IACtD,CAAC,MACI;MACD,IAAI,CAACiiB,cAAc,CAAC3hB,KAAK,CAAC;IAC9B;EACJ;EACAkN,YAAYA,CAAClN,KAAK,EAAE;IAChB,IAAI,IAAI,CAAC2B,IAAI,KAAK3B,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAAC2B,IAAI,GAAG,IAAI;IAChB,MAAMmF,YAAY,GAAG,IAAI,CAAC0H,aAAa,CAACxO,KAAK,CAAC;IAC9C,IAAI8G,YAAY,IAAI9G,KAAK,CAAC4B,MAAM,EAAE;MAC9B,IAAIA,MAAM,GAAG5B,KAAK,CAAC4B,MAAM;MACzB,OAAOA,MAAM,EAAE;QACX,IAAIlB,MAAM,CAACkB,MAAM,CAAC,IAAI,CAACA,MAAM,CAACA,MAAM,EAAE;UAClCuN,cAAc,CAACvN,MAAM,CAAC+F,WAAW,EAAE,IAAI,CAAC;QAC5C;QACA/F,MAAM,GAAGA,MAAM,CAACA,MAAM;MAC1B;IACJ;EACJ;EACA+f,cAAcA,CAAC3hB,KAAK,EAAE;IAClB,IAAI2N,OAAO,GAAGU,WAAW,CAACrO,KAAK,CAAC;IAChC,IAAIA,KAAK,CAACoc,UAAU,EAAE;MAClB;IACJ;IACA,IAAI,CAACzO,OAAO,EAAE;MACV,MAAMiU,cAAc,GAAG;QAAE7N,QAAQ,EAAE/T,KAAK,CAACiU,YAAY,CAACF;MAAS,CAAC;MAChE,IAAI/T,KAAK,CAACuG,UAAU,EAAE;QAClBoH,OAAO,GAAG,IAAIlQ,SAAS,CAAC,CAAC,CAAC,EAAEmkB,cAAc,CAAC;MAC/C,CAAC,MACI;QACD,MAAMpgB,KAAK,GAAGd,MAAM,CAACV,KAAK,CAAC,GAAGyC,aAAa,CAACzC,KAAK,CAAC,GAAGA,KAAK,CAAC8E,YAAY;QACvE6I,OAAO,GAAG,IAAIhQ,WAAW,CAAC;UAAE6D,KAAK;UAAEmN,QAAQ,EAAE,CAAC,CAAC3O,KAAK,CAAC0O,KAAK,CAACC;QAAS,CAAC,EAAE;UAAE,GAAGiT,cAAc;UAAEC,qBAAqB,EAAE;QAAK,CAAC,CAAC;MAC9H;IACJ;IACAtT,eAAe,CAACvO,KAAK,EAAE2N,OAAO,CAAC;EACnC;EACAa,aAAaA,CAACxO,KAAK,EAAE2O,QAAQ,GAAG,KAAK,EAAE;IACnC,IAAIA,QAAQ,KAAK,KAAK,IAAIjO,MAAM,CAACV,KAAK,CAAC,IAAIA,KAAK,CAAC0O,KAAK,EAAEC,QAAQ,EAAE;MAC9DA,QAAQ,GAAG,IAAI;IACnB;IACA,IAAI7H,YAAY,GAAG,KAAK;IACxB9G,KAAK,CAACuG,UAAU,EAAE1D,OAAO,CAAEyD,CAAC,IAAKA,CAAC,IAAI,IAAI,CAACkI,aAAa,CAAClI,CAAC,EAAEqI,QAAQ,CAAC,KAAK7H,YAAY,GAAG,IAAI,CAAC,CAAC;IAC/F,IAAIpG,MAAM,CAACV,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC4B,MAAM,IAAK,CAAClB,MAAM,CAACV,KAAK,CAAC,IAAI,CAACA,KAAK,CAACuG,UAAW,EAAE;MACzE,MAAM;QAAEoB,WAAW,EAAEvD;MAAE,CAAC,GAAGpE,KAAK;MAChC,IAAIoE,CAAC,EAAE;QACH,IAAI1D,MAAM,CAACV,KAAK,CAAC,IAAIoE,CAAC,YAAYzG,WAAW,EAAE;UAC3C,IAAIgR,QAAQ,IAAIvK,CAAC,CAAC0d,OAAO,EAAE;YACvB1d,CAAC,CAACyK,OAAO,CAAC;cAAEnB,SAAS,EAAE,KAAK;cAAE0B,QAAQ,EAAE;YAAK,CAAC,CAAC;YAC/CtI,YAAY,GAAG,IAAI;UACvB;UACA,IAAI,CAAC6H,QAAQ,IAAIvK,CAAC,CAACuK,QAAQ,EAAE;YACzBvK,CAAC,CAAC0K,MAAM,CAAC;cAAEpB,SAAS,EAAE,KAAK;cAAE0B,QAAQ,EAAE;YAAK,CAAC,CAAC;YAC9CtI,YAAY,GAAG,IAAI;UACvB;QACJ;QACA,IAAI,IAAI,KAAK1C,CAAC,CAACqE,SAAS,IAAI,IAAI,KAAKrE,CAAC,CAAC2d,cAAc,EAAE;UACnD3d,CAAC,CAACoK,aAAa,CAAC,MAAM;YAClB,MAAMtK,CAAC,GAAGtG,UAAU,CAACyiB,OAAO,CAAC,IAAI,CAAC2B,eAAe,CAAChiB,KAAK,EAAE,aAAa,CAAC,CAAC;YACxE,OAAOkE,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC,GAAG,IAAI;UAC1B,CAAC,CAAC;UACFA,CAAC,CAACqK,kBAAkB,CAAC,MAAM;YACvB,MAAMvK,CAAC,GAAGtG,UAAU,CAACqkB,YAAY,CAAC,IAAI,CAACD,eAAe,CAAChiB,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAClF,OAAOkE,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC,GAAGrG,EAAE,CAAC,IAAI,CAAC;UAC9B,CAAC,CAAC;UACF+I,YAAY,GAAG,IAAI;QACvB;QACA,IAAIA,YAAY,EAAE;UACdqI,cAAc,CAAC/K,CAAC,EAAE,IAAI,CAAC;UACvB;UACA,IAAIxC,MAAM,GAAGwC,CAAC,CAACxC,MAAM;UACrB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,UAAU,CAACZ,KAAK,CAAC,CAAC0B,MAAM,EAAEW,CAAC,EAAE,EAAE;YAC/C,IAAIT,MAAM,EAAE;cACRuN,cAAc,CAACvN,MAAM,EAAE,IAAI,CAAC;cAC5BA,MAAM,GAAGA,MAAM,CAACA,MAAM;YAC1B;UACJ;QACJ;MACJ;IACJ;IACA,OAAOkF,YAAY;EACvB;EACAkb,eAAeA,CAAChiB,KAAK,EAAEG,IAAI,EAAE;IACzB,MAAM+G,UAAU,GAAG,EAAE;IACrB,MAAM9C,CAAC,GAAGpE,KAAK,CAAC2H,WAAW;IAC3B,IAAIvD,CAAC,EAAEyJ,OAAO,EAAEnM,MAAM,GAAG,CAAC,EAAE;MACxB0C,CAAC,CAACyJ,OAAO,CACJxP,MAAM,CAAEiI,CAAC,IAAK,CAACA,CAAC,CAAC4I,KAAK,CAAC,CACvBrM,OAAO,CAAEyD,CAAC,IAAKY,UAAU,CAACvB,IAAI,CAAC,GAAGW,CAAC,CAACnG,IAAI,CAAC,CAAC,CAAC;IACpD,CAAC,MACI,IAAIH,KAAK,CAACG,IAAI,CAAC,EAAE;MAClB+G,UAAU,CAACvB,IAAI,CAAC,GAAG3F,KAAK,CAACG,IAAI,CAAC,CAAC;IACnC;IACA,IAAIH,KAAK,CAACuG,UAAU,EAAE;MAClBvG,KAAK,CAACuG,UAAU,CACXlI,MAAM,CAAEiI,CAAC,IAAKA,CAAC,EAAEC,UAAU,IAAI,CAAC7F,MAAM,CAAC4F,CAAC,CAAC,CAAC,CAC1CzD,OAAO,CAAEyD,CAAC,IAAKY,UAAU,CAACvB,IAAI,CAAC,GAAG,IAAI,CAACqc,eAAe,CAAC1b,CAAC,EAAEnG,IAAI,CAAC,CAAC,CAAC;IAC1E;IACA,OAAO+G,UAAU;EACrB;AACJ;AAEA,MAAMgb,aAAa,CAAC;EAChB5hB,WAAWA,CAACiI,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACxI,MAAM,GAAG,CAAC;EACnB;EACAiN,WAAWA,CAAChN,KAAK,EAAE;IACf,MAAM2B,IAAI,GAAG3B,KAAK,CAAC4B,MAAM;IACzB,IAAI,CAACugB,eAAe,CAACniB,KAAK,CAAC;IAC3B,IAAI,CAACoiB,cAAc,CAACpiB,KAAK,CAAC;IAC1B,IAAI2B,IAAI,EAAE;MACN2B,MAAM,CAACuB,cAAc,CAAC7E,KAAK,EAAE,SAAS,EAAE;QAAE4E,GAAG,EAAEA,CAAA,KAAMjD,IAAI,CAACmG,OAAO;QAAE7C,YAAY,EAAE;MAAK,CAAC,CAAC;MACxF3B,MAAM,CAACuB,cAAc,CAAC7E,KAAK,EAAE,OAAO,EAAE;QAClC4E,GAAG,EAAEA,CAAA,KAAOlE,MAAM,CAACV,KAAK,CAAC,IAAIA,KAAK,CAACuG,UAAU,GAAG9D,aAAa,CAACzC,KAAK,CAAC,GAAG2B,IAAI,CAACQ,KAAM;QAClF8C,YAAY,EAAE;MAClB,CAAC,CAAC;IACN;IACA3B,MAAM,CAACuB,cAAc,CAAC7E,KAAK,EAAE,KAAK,EAAE;MAChCwB,KAAK,EAAGhB,GAAG,IAAK6F,QAAQ,CAACrG,KAAK,EAAEQ,GAAG,CAAC;MACpCyE,YAAY,EAAE;IAClB,CAAC,CAAC;IACF,IAAI,CAACod,yBAAyB,CAACriB,KAAK,CAAC,CAACgN,WAAW,GAAGhN,KAAK,CAAC;EAC9D;EACAiN,UAAUA,CAACjN,KAAK,EAAE;IACd,IAAI,CAACsiB,gBAAgB,CAACtiB,KAAK,CAAC;IAC5B,IAAI,CAACqiB,yBAAyB,CAACriB,KAAK,CAAC,CAACiN,UAAU,GAAGjN,KAAK,CAAC;IACzD,IAAIA,KAAK,CAACuG,UAAU,EAAE;MAClBvG,KAAK,CAACuG,UAAU,CAAC1D,OAAO,CAAC,CAACyD,CAAC,EAAErG,KAAK,KAAK;QACnC,IAAIqG,CAAC,EAAE;UACHhD,MAAM,CAACuB,cAAc,CAACyB,CAAC,EAAE,QAAQ,EAAE;YAAE1B,GAAG,EAAEA,CAAA,KAAM5E,KAAK;YAAEiF,YAAY,EAAE;UAAK,CAAC,CAAC;UAC5E3B,MAAM,CAACuB,cAAc,CAACyB,CAAC,EAAE,OAAO,EAAE;YAAE1B,GAAG,EAAEA,CAAA,KAAM3E,KAAK;YAAEgF,YAAY,EAAE;UAAK,CAAC,CAAC;QAC/E;QACA,IAAI,CAAClF,MAAM,EAAE;MACjB,CAAC,CAAC;IACN;EACJ;EACAmN,YAAYA,CAAClN,KAAK,EAAE;IAChB,IAAI,CAACqiB,yBAAyB,CAACriB,KAAK,CAAC,CAACkN,YAAY,GAAGlN,KAAK,CAAC;EAC/D;EACAoiB,cAAcA,CAACpiB,KAAK,EAAE;IAClBA,KAAK,CAAC0O,KAAK,KAAK1O,KAAK,CAAC0O,KAAK,GAAG1O,KAAK,CAACuiB,eAAe,CAAC;IACpDjf,MAAM,CAACuB,cAAc,CAAC7E,KAAK,EAAE,iBAAiB,EAAE;MAC5C4E,GAAG,EAAEA,CAAA,KAAM5E,KAAK,CAAC0O,KAAK;MACtBzI,GAAG,EAAGyI,KAAK,IAAM1O,KAAK,CAAC0O,KAAK,GAAGA,KAAM;MACrCzJ,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAkd,eAAeA,CAACniB,KAAK,EAAE;IACnB,IAAIA,KAAK,CAAC4B,MAAM,EAAE;MACd;IACJ;IACA,MAAMkG,OAAO,GAAG9H,KAAK,CAAC8H,OAAO;IAC7B9H,KAAK,CAAC8H,OAAO,CAACqS,SAAS,GAAGna,KAAK,CAAC8H,OAAO,CAACqS,SAAS,IAAI,CAAC,CAAC;IACvD,IAAI,CAACrS,OAAO,CAACJ,SAAS,EAAE;MACpBI,OAAO,CAACJ,SAAS,GAAG,IAAI,CAACa,MAAM,CAAClB,MAAM,CAACK,SAAS;IACpD;IACA,IAAI,CAACI,OAAO,CAAC6K,YAAY,EAAE;MACvBtR,gBAAgB,CAACyG,OAAO,EAAE,cAAc,EAAE,IAAI7J,OAAO,CAAC,CAAC,CAAC;IAC5D;IACA,IAAI,CAAC6J,OAAO,CAACsW,qBAAqB,EAAE;MAChCtW,OAAO,CAACsW,qBAAqB,GAAG,EAAE;IACtC;IACAtW,OAAO,CAAC0a,aAAa,GAAIlc,CAAC,IAAK;MAC3BkE,OAAO,CAACS,IAAI,CAAE,gGAA+F,CAAC;MAC9GnD,OAAO,CAACgF,aAAa,CAACxG,CAAC,CAAC;IAC5B,CAAC;IACDwB,OAAO,CAACgF,aAAa,GAAIxG,CAAC,IAAK;MAC3B,IAAIA,CAAC,CAACI,cAAc,EAAE;QAClBJ,CAAC,CAACwB,OAAO,CAAC+E,gBAAgB,CAACvG,CAAC,CAAC;QAC7BG,iBAAiB,CAACH,CAAC,CAAC;MACxB;MACAA,CAAC,CAACC,UAAU,EAAE1D,OAAO,CAAEyD,CAAC,IAAKA,CAAC,IAAIwB,OAAO,CAACgF,aAAa,CAACxG,CAAC,CAAC,CAAC;IAC/D,CAAC;IACDwB,OAAO,CAAC2a,UAAU,GAAItgB,KAAK,IAAK;MAC5BA,KAAK,GAAGK,KAAK,CAACL,KAAK,IAAI2F,OAAO,CAAC4a,aAAa,CAAC;MAC7C,IAAI1iB,KAAK,CAACmC,KAAK,EAAE;QACbmB,MAAM,CAACiB,IAAI,CAACvE,KAAK,CAACmC,KAAK,CAAC,CAACU,OAAO,CAAEd,CAAC,IAAK,OAAO/B,KAAK,CAACmC,KAAK,CAACJ,CAAC,CAAC,CAAC;QAC9DuB,MAAM,CAAC0P,MAAM,CAAChT,KAAK,CAACmC,KAAK,EAAEA,KAAK,IAAI,CAAC,CAAC,CAAC;MAC3C;MACA2F,OAAO,CAAC2E,KAAK,CAACzM,KAAK,CAAC;MACpBA,KAAK,CAACN,IAAI,CAAC8f,KAAK,CAACxf,KAAK,CAACmC,KAAK,CAAC;MAC7B,IAAI2F,OAAO,CAACC,UAAU,IAAID,OAAO,CAACC,UAAU,CAAC4F,OAAO,KAAK3N,KAAK,CAAC2H,WAAW,EAAE;QACxEG,OAAO,CAACC,UAAU,CAACC,SAAS,GAAG,KAAK;MACxC;IACJ,CAAC;IACDF,OAAO,CAAC6a,kBAAkB,GAAIxgB,KAAK,IAAM2F,OAAO,CAAC4a,aAAa,GAAGlgB,KAAK,CAACL,KAAK,IAAInC,KAAK,CAACmC,KAAK,CAAE;IAC7FnC,KAAK,CAAC8H,OAAO,CAAC6a,kBAAkB,CAAC,CAAC;EACtC;EACAL,gBAAgBA,CAACtiB,KAAK,EAAE;IACpB0C,gBAAgB,CAAC1C,KAAK,EAAE;MACpBE,EAAE,EAAEJ,UAAU,CAAE,UAAS,IAAI,CAACC,MAAO,EAAC,EAAEC,KAAK,EAAEA,KAAK,CAACC,KAAK,CAAC;MAC3D2S,KAAK,EAAE,CAAC,CAAC;MACTqB,YAAY,EAAE,CAAC,CAAC;MAChBhM,UAAU,EAAE;QAAEb,QAAQ,EAAE,CAAC;MAAE,CAAC;MAC5BsH,KAAK,EAAE,CAAC1O,KAAK,CAACG,IAAI,IAAI,CAACO,MAAM,CAACV,KAAK,CAAC,GAC9B,CAAC,CAAC,GACF;QACE4iB,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACflU,QAAQ,EAAE;MACd;IACR,CAAC,CAAC;IACF,IAAI,IAAI,CAACpG,MAAM,CAAClB,MAAM,CAACG,gBAAgB,IAAIxH,KAAK,CAAC8B,WAAW,KAAK,KAAK,EAAE;MACpE9B,KAAK,CAAC8B,WAAW,GAAG,IAAI;IAC5B;IACA,IAAI9B,KAAK,CAACG,IAAI,KAAK,iBAAiB,KAC/BH,KAAK,CAACI,QAAQ,IAAIJ,KAAK,CAACye,WAAW,EAAEre,QAAQ,IAAIJ,KAAK,CAAC0e,oBAAoB,EAAEte,QAAQ,CAAC,EAAE;MACzFJ,KAAK,CAACG,IAAI,GAAG,iBAAiB;IAClC;IACA,IAAI,CAACH,KAAK,CAACG,IAAI,IAAIH,KAAK,CAACuG,UAAU,EAAE;MACjCvG,KAAK,CAACG,IAAI,GAAG,cAAc;IAC/B;IACA,IAAIH,KAAK,CAACG,IAAI,EAAE;MACZ,IAAI,CAACoI,MAAM,CAACmB,cAAc,CAAC1J,KAAK,CAAC;IACrC;IACA,IAAIU,MAAM,CAACV,KAAK,CAAC,IAAI,CAACkD,WAAW,CAAClD,KAAK,CAAC8E,YAAY,CAAC,IAAI5B,WAAW,CAACT,aAAa,CAACzC,KAAK,CAAC,CAAC,EAAE;MACxF,MAAM8iB,QAAQ,GAAIxc,CAAC,IAAKA,CAAC,CAACgN,IAAI,IAAIhN,CAAC,CAACmY,WAAW,EAAEnL,IAAI,IAAIhN,CAAC,CAAC+X,cAAc;MACzE,IAAI0E,eAAe,GAAG,CAAC/iB,KAAK,CAAC8B,WAAW,IAAI,CAACghB,QAAQ,CAAC9iB,KAAK,CAAC;MAC5D,IAAI,CAAC8iB,QAAQ,CAAC9iB,KAAK,CAAC,IAAIA,KAAK,CAAC8B,WAAW,EAAE;QACvC,IAAIF,MAAM,GAAG5B,KAAK,CAAC4B,MAAM;QACzB,OAAOA,MAAM,IAAI,CAACkhB,QAAQ,CAAClhB,MAAM,CAAC,EAAE;UAChCA,MAAM,GAAGA,MAAM,CAACA,MAAM;QAC1B;QACAmhB,eAAe,GAAG,CAACnhB,MAAM,IAAI,CAACkhB,QAAQ,CAAClhB,MAAM,CAAC;MAClD;MACA,IAAImhB,eAAe,EAAE;QACjBxhB,gBAAgB,CAACvB,KAAK,EAAEA,KAAK,CAAC8E,YAAY,CAAC;MAC/C;IACJ;IACA9E,KAAK,CAACmH,QAAQ,GAAGnH,KAAK,CAACmH,QAAQ,IAAI,EAAE;EACzC;EACAkb,yBAAyBA,CAACriB,KAAK,EAAE;IAC7B,MAAMgjB,oBAAoB,GAAGA,CAAA,KAAM;MAC/B,IAAIjZ,YAAY,GAAG,IAAI,CAACxB,MAAM,CAACyB,mBAAmB,CAAChK,KAAK,CAAC;MACzD,MAAMijB,iBAAiB,GAAGjjB,KAAK,CAAC0G,cAAc,EAAEtF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,IAAI6hB,iBAAiB,YAAY1mB,YAAY,IACzC0mB,iBAAiB,EAAEC,aAAa,KAAKnZ,YAAY,EAAEmZ,aAAa,EAAE;QAClEnZ,YAAY,GAAGkZ,iBAAiB;MACpC;MACA,OAAOlZ,YAAY,EAAEE,QAAQ;IACjC,CAAC;IACD,IAAI,CAACjK,KAAK,CAACmjB,cAAc,EAAE;MACvB9hB,gBAAgB,CAACrB,KAAK,EAAE,gBAAgB,EAAE,IAAIojB,KAAK,CAAC,CAAC,CAAC,EAAE;QACpDxe,GAAG,EAAEA,CAACye,CAAC,EAAE5e,IAAI,KAAKue,oBAAoB,CAAC,CAAC,GAAGve,IAAI,CAAC;QAChDwB,GAAG,EAAEA,CAACod,CAAC,EAAE5e,IAAI,EAAEjD,KAAK,KAAMwhB,oBAAoB,CAAC,CAAC,CAACve,IAAI,CAAC,GAAGjD;MAC7D,CAAC,CAAC,CAAC;IACP;IACA,OAAOxB,KAAK,CAACmjB,cAAc;EAC/B;AACJ;AAEA,SAASG,mBAAmBA,CAAC/a,MAAM,EAAE;EACjC,OAAO;IACHtB,KAAK,EAAE,CACH;MAAE1G,IAAI,EAAE,cAAc;MAAEgJ,SAAS,EAAE8Q;IAAY,CAAC,EAChD;MAAE9Z,IAAI,EAAE,iBAAiB;MAAEgJ,SAAS,EAAE0T;IAAmB,CAAC,CAC7D;IACD9U,UAAU,EAAE,CACR;MAAE5H,IAAI,EAAE,MAAM;MAAE+K,SAAS,EAAE,IAAI4W,aAAa,CAAC3Z,MAAM,CAAC;MAAE8C,QAAQ,EAAE,CAAC;IAAI,CAAC,EACtE;MAAE9K,IAAI,EAAE,kBAAkB;MAAE+K,SAAS,EAAE,IAAIwU,wBAAwB,CAACvX,MAAM,CAAC;MAAE8C,QAAQ,EAAE,CAAC;IAAI,CAAC,EAC7F;MAAE9K,IAAI,EAAE,YAAY;MAAE+K,SAAS,EAAE,IAAIoW,kBAAkB,CAAC,CAAC;MAAErW,QAAQ,EAAE,CAAC;IAAI,CAAC,EAC3E;MAAE9K,IAAI,EAAE,kBAAkB;MAAE+K,SAAS,EAAE,IAAI4S,wBAAwB,CAAC,CAAC;MAAE7S,QAAQ,EAAE,CAAC;IAAI,CAAC;EAE/F,CAAC;AACL;AACA,MAAMkY,YAAY,CAAC;EACfjjB,WAAWA,CAACkjB,aAAa,EAAEC,OAAO,GAAG,EAAE,EAAE;IACrC,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACAA,OAAO,CAAC5gB,OAAO,CAAE0F,MAAM,IAAKib,aAAa,CAAClb,SAAS,CAACC,MAAM,CAAC,CAAC;EAChE;EACA,OAAOmb,OAAOA,CAACnb,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHob,QAAQ,EAAEJ,YAAY;MACtBlM,SAAS,EAAE,CACP;QAAEuM,OAAO,EAAE7c,aAAa;QAAE8c,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAER,mBAAmB;QAAES,IAAI,EAAE,CAAC/c,YAAY;MAAE,CAAC,EAC9F;QAAE4c,OAAO,EAAE7c,aAAa;QAAEid,QAAQ,EAAEzb,MAAM;QAAEsb,KAAK,EAAE;MAAK,CAAC,EACzD7c,YAAY,EACZsF,iBAAiB;IAEzB,CAAC;EACL;EACA,OAAO2X,QAAQA,CAAC1b,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO;MACHob,QAAQ,EAAEJ,YAAY;MACtBlM,SAAS,EAAE,CACP;QAAEuM,OAAO,EAAE7c,aAAa;QAAE8c,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAER,mBAAmB;QAAES,IAAI,EAAE,CAAC/c,YAAY;MAAE,CAAC,EAC9F;QAAE4c,OAAO,EAAE7c,aAAa;QAAEid,QAAQ,EAAEzb,MAAM;QAAEsb,KAAK,EAAE;MAAK,CAAC,EACzDvX,iBAAiB;IAEzB,CAAC;EACL;AACJ;AACAiX,YAAY,CAAC1X,IAAI,YAAAqY,qBAAAnY,CAAA;EAAA,YAAAA,CAAA,IAAyFwX,YAAY,EAnqDtBnnB,EAAE,CAAAiR,QAAA,CAmqDsCrG,YAAY,GAnqDpD5K,EAAE,CAAAiR,QAAA,CAmqD+DtG,aAAa;AAAA,CAA2D;AACzOwc,YAAY,CAACY,IAAI,kBApqD+E/nB,EAAE,CAAAgoB,gBAAA;EAAAjkB,IAAA,EAoqDSojB;AAAY,EAM4C;AACnKA,YAAY,CAACc,IAAI,kBA3qD+EjoB,EAAE,CAAAkoB,gBAAA;EAAAC,OAAA,GA2qDiC,CAAC3lB,YAAY,CAAC;AAAA,EAAI;AACrJ;EAAA,QAAAiM,SAAA,oBAAAA,SAAA,KA5qDgGzO,EAAE,CAAAiQ,iBAAA,CA4qDNkX,YAAY,EAAc,CAAC;IAC3GpjB,IAAI,EAAE7C,QAAQ;IACdsF,IAAI,EAAE,CAAC;MACC4hB,YAAY,EAAE,CACV7U,cAAc,EACdgG,UAAU,EACVnF,WAAW,EACX+G,gBAAgB,EAChB8C,WAAW,EACXc,uBAAuB,EACvB8B,kBAAkB,CACrB;MACDwH,OAAO,EAAE,CAAC9U,cAAc,EAAEgG,UAAU,EAAEnF,WAAW,EAAE+G,gBAAgB,EAAE8C,WAAW,EAAEc,uBAAuB,CAAC;MAC1GoJ,OAAO,EAAE,CAAC3lB,YAAY;IAC1B,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuB,IAAI,EAAE6G;IAAa,CAAC,EAAE;MAAE7G,IAAI,EAAE0B,SAAS;MAAE2L,UAAU,EAAE,CAAC;QACtFrN,IAAI,EAAExD;MACV,CAAC,EAAE;QACCwD,IAAI,EAAE9C,MAAM;QACZuF,IAAI,EAAE,CAACmE,aAAa;MACxB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,aAAa,EAAEoV,cAAc,EAAElC,SAAS,EAAEyC,YAAY,EAAE1V,YAAY,EAAEwJ,WAAW,EAAEmF,UAAU,EAAErJ,iBAAiB,EAAEiX,YAAY,EAAEhM,gBAAgB,IAAImN,iBAAiB,EAAErK,WAAW,IAAIsK,YAAY,EAAEhV,cAAc,IAAIiV,eAAe,EAAEzJ,uBAAuB,IAAI0J,wBAAwB,EAAEriB,KAAK,IAAIsiB,MAAM,EAAEzjB,gBAAgB,IAAI0jB,iBAAiB,EAAEtiB,aAAa,IAAIuiB,cAAc,EAAEtkB,MAAM,IAAIukB,OAAO,EAAEzf,OAAO,IAAI0f,QAAQ,EAAExiB,gBAAgB,IAAIyiB,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
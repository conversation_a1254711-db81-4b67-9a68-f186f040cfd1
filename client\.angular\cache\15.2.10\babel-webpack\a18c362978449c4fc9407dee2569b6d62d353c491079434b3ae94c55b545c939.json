{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i4 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i5 from \"app/layout/components/content-header/breadcrumb/breadcrumb.component\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"@ngx-translate/core\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nfunction ContentHeaderComponent_ng_container_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 8)(3, \"div\", 9)(4, \"button\", 10);\n    i0.ɵɵelement(5, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 12)(7, \"a\", 13);\n    i0.ɵɵelement(8, \"span\", 11);\n    i0.ɵɵtext(9, \" Todo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 13);\n    i0.ɵɵelement(11, \"span\", 11);\n    i0.ɵɵtext(12, \" Chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"a\", 13);\n    i0.ɵɵelement(14, \"span\", 11);\n    i0.ɵɵtext(15, \" Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"a\", 13);\n    i0.ɵɵelement(17, \"span\", 11);\n    i0.ɵɵtext(18, \" Calendar\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data-feather\", \"grid\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"mr-50\");\n    i0.ɵɵproperty(\"data-feather\", \"check-square\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"mr-50\");\n    i0.ɵɵproperty(\"data-feather\", \"message-square\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(19, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"mr-50\");\n    i0.ɵɵproperty(\"data-feather\", \"mail\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"mr-50\");\n    i0.ɵɵproperty(\"data-feather\", \"calendar\");\n  }\n}\nfunction ContentHeaderComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"app-breadcrumb\", 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, ContentHeaderComponent_ng_container_0_ng_container_9_Template, 19, 21, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 3, ctx_r0.contentHeader.headerTitle), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"breadcrumb\", ctx_r0.contentHeader.breadcrumb);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.contentHeader.actionButton);\n  }\n}\nexport class ContentHeaderComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function ContentHeaderComponent_Factory(t) {\n    return new (t || ContentHeaderComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ContentHeaderComponent,\n    selectors: [[\"app-content-header\"]],\n    inputs: {\n      contentHeader: \"contentHeader\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [1, \"content-header\", \"row\", \"d-md-block\", \"d-none\"], [1, \"content-header-left\", \"col-md-9\", \"col-12\", \"mb-2\"], [1, \"row\", \"breadcrumbs-top\"], [1, \"col-12\", \"d-flex\"], [1, \"content-header-title\", \"float-left\", \"mb-0\"], [3, \"breadcrumb\"], [1, \"content-header-right\", \"text-md-right\", \"col-md-3\", \"col-12\", \"d-md-block\", \"d-none\"], [1, \"form-group\", \"breadcrum-right\"], [\"ngbDropdown\", \"\"], [\"ngbDropdownToggle\", \"\", \"id\", \"dropdownSettings\", \"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn-icon\", \"btn\", \"btn-primary\", \"btn-round\", \"btn-sm\"], [3, \"data-feather\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownSettings\"], [\"ngbDropdownItem\", \"\", 3, \"routerLink\"]],\n    template: function ContentHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ContentHeaderComponent_ng_container_0_Template, 10, 5, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.contentHeader);\n      }\n    },\n    dependencies: [i1.NgIf, i2.RouterLink, i3.RippleEffectDirective, i4.FeatherIconDirective, i5.BreadcrumbComponent, i6.NgbDropdown, i6.NgbDropdownToggle, i6.NgbDropdownMenu, i6.NgbDropdownItem, i7.TranslatePipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;IAcIA,EAAA,CAAAC,uBAAA,GAAiD;IAC/CD,EAAA,CAAAE,cAAA,aAAkF;IAU1EF,EAAA,CAAAG,SAAA,eAAqC;IACvCH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAAwD;IAEnDF,EAAA,CAAAG,SAAA,eAA+D;IAACH,EAAA,CAAAK,MAAA,YAAI;IAAAL,EAAA,CAAAI,YAAA,EACtE;IACDJ,EAAA,CAAAE,cAAA,aACG;IAAAF,EAAA,CAAAG,SAAA,gBAAiE;IAACH,EAAA,CAAAK,MAAA,aAAI;IAAAL,EAAA,CAAAI,YAAA,EACxE;IACDJ,EAAA,CAAAE,cAAA,aAAwC;IAAAF,EAAA,CAAAG,SAAA,gBAAuD;IAACH,EAAA,CAAAK,MAAA,cAAK;IAAAL,EAAA,CAAAI,YAAA,EAAI;IACzGJ,EAAA,CAAAE,cAAA,aACG;IAAAF,EAAA,CAAAG,SAAA,gBAA2D;IAACH,EAAA,CAAAK,MAAA,iBAAQ;IAAAL,EAAA,CAAAI,YAAA,EACtE;IAKXJ,EAAA,CAAAM,qBAAA,EAAe;;;IAjBCN,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAQ,UAAA,wBAAuB;IAGVR,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAoB;IACEV,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAW,UAAA,SAAiB;IAAjDX,EAAA,CAAAQ,UAAA,gCAA+B;IAErBR,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAoB;IACIV,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAW,UAAA,SAAiB;IAAnDX,EAAA,CAAAQ,UAAA,kCAAiC;IAEvBR,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAoB;IAA+BV,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAW,UAAA,SAAiB;IAAzCX,EAAA,CAAAQ,UAAA,wBAAuB;IAClDR,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAoB;IACFV,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAW,UAAA,SAAiB;IAA7CX,EAAA,CAAAQ,UAAA,4BAA2B;;;;;IAnClDR,EAAA,CAAAC,uBAAA,GAAoC;IAClCD,EAAA,CAAAE,cAAA,aAAkD;IAKxCF,EAAA,CAAAK,MAAA,GACF;;IAAAL,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAG,SAAA,wBAAyE;IAC3EH,EAAA,CAAAI,YAAA,EAAM;IAGVJ,EAAA,CAAAY,UAAA,IAAAC,6DAAA,4BA4Be;IACjBb,EAAA,CAAAI,YAAA,EAAM;IACRJ,EAAA,CAAAM,qBAAA,EAAe;;;;IArCHN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAe,WAAA,OAAAC,MAAA,CAAAC,aAAA,CAAAC,WAAA,OACF;IAEgBlB,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,UAAA,eAAAQ,MAAA,CAAAC,aAAA,CAAAE,UAAA,CAAuC;IAI9CnB,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAQ,MAAA,CAAAC,aAAA,CAAAG,YAAA,CAAgC;;;ACMnD,OAAM,MAAOC,sBAAsB;EAIjCC,YAAA,GAAe;EAEfC,QAAQA,CAAA,GAAI;EAAC,QAAAC,CAAA;qBANFH,sBAAsB;EAAA;EAAA,QAAAI,EAAA;UAAtBJ,sBAAsB;IAAAK,SAAA;IAAAC,MAAA;MAAAV,aAAA;IAAA;IAAAW,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDnBnCjC,EAAA,CAAAY,UAAA,IAAAuB,8CAAA,2BA2Ce;;;QA3CAnC,EAAA,CAAAQ,UAAA,SAAA0B,GAAA,CAAAjB,aAAA,CAAmB", "names": ["i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵclassMap", "ɵɵtemplate", "ContentHeaderComponent_ng_container_0_ng_container_9_Template", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r0", "contentHeader", "headerTitle", "breadcrumb", "actionButton", "ContentHeaderComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ContentHeaderComponent_Template", "rf", "ctx", "ContentHeaderComponent_ng_container_0_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content-header\\content-header.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\content-header\\content-header.component.ts"], "sourcesContent": ["<!-- app-content-header start -->\r\n<ng-container *ngIf=\"contentHeader\">\r\n  <div class=\"content-header row d-md-block d-none\">\r\n    <div class=\"content-header-left col-md-9 col-12 mb-2\">\r\n      <div class=\"row breadcrumbs-top\">\r\n        <div class=\"col-12 d-flex\">\r\n          <h2 class=\"content-header-title float-left mb-0\">\r\n            {{ contentHeader.headerTitle | translate}}\r\n          </h2>\r\n          <!-- app-breadcrumb component -->\r\n          <app-breadcrumb [breadcrumb]=\"contentHeader.breadcrumb\"></app-breadcrumb>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <ng-container *ngIf=\"contentHeader.actionButton\">\r\n      <div class=\"content-header-right text-md-right col-md-3 col-12 d-md-block d-none\">\r\n        <div class=\"form-group breadcrum-right\">\r\n          <div ngbDropdown>\r\n            <button\r\n              ngbDropdownToggle\r\n              id=\"dropdownSettings\"\r\n              class=\"btn-icon btn btn-primary btn-round btn-sm\"\r\n              type=\"button\"\r\n              rippleEffect\r\n            >\r\n              <span [data-feather]=\"'grid'\"></span>\r\n            </button>\r\n            <div ngbDropdownMenu aria-labelledby=\"dropdownSettings\">\r\n              <a ngbDropdownItem [routerLink]=\"['/']\"\r\n                ><span [data-feather]=\"'check-square'\" [class]=\"'mr-50'\"></span> Todo</a\r\n              >\r\n              <a ngbDropdownItem [routerLink]=\"['/']\"\r\n                ><span [data-feather]=\"'message-square'\" [class]=\"'mr-50'\"></span> Chat</a\r\n              >\r\n              <a ngbDropdownItem [routerLink]=\"['/']\"><span [data-feather]=\"'mail'\" [class]=\"'mr-50'\"></span> Email</a>\r\n              <a ngbDropdownItem [routerLink]=\"['/']\"\r\n                ><span [data-feather]=\"'calendar'\" [class]=\"'mr-50'\"></span> Calendar</a\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n</ng-container>\r\n<!-- app-content-header end -->\r\n", "import { Component, OnInit, Input } from '@angular/core';\r\n\r\n// ContentHeader component interface\r\nexport interface ContentHeader {\r\n  headerTitle: string;\r\n  actionButton: boolean;\r\n  breadcrumb?: {\r\n    type?: string;\r\n    links?: Array<{\r\n      name?: string;\r\n      isLink?: boolean;\r\n      link?: string;\r\n    }>;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-content-header',\r\n  templateUrl: './content-header.component.html'\r\n})\r\nexport class ContentHeaderComponent implements OnInit {\r\n  // input variable\r\n  @Input() contentHeader: ContentHeader;\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {}\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export var Style;\n(function (Style) {\n  /**\n   * Light text for dark backgrounds.\n   *\n   * @since 1.0.0\n   */\n  Style[\"Dark\"] = \"DARK\";\n  /**\n   * Dark text for light backgrounds.\n   *\n   * @since 1.0.0\n   */\n  Style[\"Light\"] = \"LIGHT\";\n  /**\n   * The style is based on the device appearance.\n   * If the device is using Dark mode, the statusbar text will be light.\n   * If the device is using Light mode, the statusbar text will be dark.\n   * On Android the default will be the one the app was launched with.\n   *\n   * @since 1.0.0\n   */\n  Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nexport var Animation;\n(function (Animation) {\n  /**\n   * No animation during show/hide.\n   *\n   * @since 1.0.0\n   */\n  Animation[\"None\"] = \"NONE\";\n  /**\n   * Slide animation during show/hide.\n   * It doesn't work on iOS 15+.\n   *\n   * @deprecated Use Animation.Fade or Animation.None instead.\n   *\n   * @since 1.0.0\n   */\n  Animation[\"Slide\"] = \"SLIDE\";\n  /**\n   * Fade animation during show/hide.\n   *\n   * @since 1.0.0\n   */\n  Animation[\"Fade\"] = \"FADE\";\n})(Animation || (Animation = {}));\n/**\n * @deprecated Use `Animation`.\n * @since 1.0.0\n */\nexport const StatusBarAnimation = Animation;\n/**\n * @deprecated Use `Style`.\n * @since 1.0.0\n */\nexport const StatusBarStyle = Style;", "map": {"version": 3, "names": ["Style", "Animation", "StatusBarAnimation", "StatusBarStyle"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/status-bar/dist/esm/definitions.js"], "sourcesContent": ["export var Style;\n(function (Style) {\n    /**\n     * Light text for dark backgrounds.\n     *\n     * @since 1.0.0\n     */\n    Style[\"Dark\"] = \"DARK\";\n    /**\n     * Dark text for light backgrounds.\n     *\n     * @since 1.0.0\n     */\n    Style[\"Light\"] = \"LIGHT\";\n    /**\n     * The style is based on the device appearance.\n     * If the device is using Dark mode, the statusbar text will be light.\n     * If the device is using Light mode, the statusbar text will be dark.\n     * On Android the default will be the one the app was launched with.\n     *\n     * @since 1.0.0\n     */\n    Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nexport var Animation;\n(function (Animation) {\n    /**\n     * No animation during show/hide.\n     *\n     * @since 1.0.0\n     */\n    Animation[\"None\"] = \"NONE\";\n    /**\n     * Slide animation during show/hide.\n     * It doesn't work on iOS 15+.\n     *\n     * @deprecated Use Animation.Fade or Animation.None instead.\n     *\n     * @since 1.0.0\n     */\n    Animation[\"Slide\"] = \"SLIDE\";\n    /**\n     * Fade animation during show/hide.\n     *\n     * @since 1.0.0\n     */\n    Animation[\"Fade\"] = \"FADE\";\n})(Animation || (Animation = {}));\n/**\n * @deprecated Use `Animation`.\n * @since 1.0.0\n */\nexport const StatusBarAnimation = Animation;\n/**\n * @deprecated Use `Style`.\n * @since 1.0.0\n */\nexport const StatusBarStyle = Style;\n"], "mappings": "AAAA,OAAO,IAAIA,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE;EACd;AACJ;AACA;AACA;AACA;EACIA,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM;EACtB;AACJ;AACA;AACA;AACA;EACIA,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;EACxB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS;AAChC,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,OAAO,IAAIC,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClB;AACJ;AACA;AACA;AACA;EACIA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO;EAC5B;AACJ;AACA;AACA;AACA;EACIA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;AAC9B,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAGD,SAAS;AAC3C;AACA;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGH,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function refCount() {\n  return function refCountOperatorFunction(source) {\n    return source.lift(new RefCountOperator(source));\n  };\n}\nclass RefCountOperator {\n  constructor(connectable) {\n    this.connectable = connectable;\n  }\n  call(subscriber, source) {\n    const {\n      connectable\n    } = this;\n    connectable._refCount++;\n    const refCounter = new RefCountSubscriber(subscriber, connectable);\n    const subscription = source.subscribe(refCounter);\n    if (!refCounter.closed) {\n      refCounter.connection = connectable.connect();\n    }\n    return subscription;\n  }\n}\nclass RefCountSubscriber extends Subscriber {\n  constructor(destination, connectable) {\n    super(destination);\n    this.connectable = connectable;\n  }\n  _unsubscribe() {\n    const {\n      connectable\n    } = this;\n    if (!connectable) {\n      this.connection = null;\n      return;\n    }\n    this.connectable = null;\n    const refCount = connectable._refCount;\n    if (refCount <= 0) {\n      this.connection = null;\n      return;\n    }\n    connectable._refCount = refCount - 1;\n    if (refCount > 1) {\n      this.connection = null;\n      return;\n    }\n    const {\n      connection\n    } = this;\n    const sharedConnection = connectable._connection;\n    this.connection = null;\n    if (sharedConnection && (!connection || sharedConnection === connection)) {\n      sharedConnection.unsubscribe();\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "refCount", "refCountOperatorFunction", "source", "lift", "RefCountOperator", "constructor", "connectable", "call", "subscriber", "_refCount", "refCounter", "RefCountSubscriber", "subscription", "subscribe", "closed", "connection", "connect", "destination", "_unsubscribe", "sharedConnection", "_connection", "unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/refCount.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function refCount() {\n    return function refCountOperatorFunction(source) {\n        return source.lift(new RefCountOperator(source));\n    };\n}\nclass RefCountOperator {\n    constructor(connectable) {\n        this.connectable = connectable;\n    }\n    call(subscriber, source) {\n        const { connectable } = this;\n        connectable._refCount++;\n        const refCounter = new RefCountSubscriber(subscriber, connectable);\n        const subscription = source.subscribe(refCounter);\n        if (!refCounter.closed) {\n            refCounter.connection = connectable.connect();\n        }\n        return subscription;\n    }\n}\nclass RefCountSubscriber extends Subscriber {\n    constructor(destination, connectable) {\n        super(destination);\n        this.connectable = connectable;\n    }\n    _unsubscribe() {\n        const { connectable } = this;\n        if (!connectable) {\n            this.connection = null;\n            return;\n        }\n        this.connectable = null;\n        const refCount = connectable._refCount;\n        if (refCount <= 0) {\n            this.connection = null;\n            return;\n        }\n        connectable._refCount = refCount - 1;\n        if (refCount > 1) {\n            this.connection = null;\n            return;\n        }\n        const { connection } = this;\n        const sharedConnection = connectable._connection;\n        this.connection = null;\n        if (sharedConnection && (!connection || sharedConnection === connection)) {\n            sharedConnection.unsubscribe();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,QAAQA,CAAA,EAAG;EACvB,OAAO,SAASC,wBAAwBA,CAACC,MAAM,EAAE;IAC7C,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,gBAAgB,CAACF,MAAM,CAAC,CAAC;EACpD,CAAC;AACL;AACA,MAAME,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAC,IAAIA,CAACC,UAAU,EAAEN,MAAM,EAAE;IACrB,MAAM;MAAEI;IAAY,CAAC,GAAG,IAAI;IAC5BA,WAAW,CAACG,SAAS,EAAE;IACvB,MAAMC,UAAU,GAAG,IAAIC,kBAAkB,CAACH,UAAU,EAAEF,WAAW,CAAC;IAClE,MAAMM,YAAY,GAAGV,MAAM,CAACW,SAAS,CAACH,UAAU,CAAC;IACjD,IAAI,CAACA,UAAU,CAACI,MAAM,EAAE;MACpBJ,UAAU,CAACK,UAAU,GAAGT,WAAW,CAACU,OAAO,CAAC,CAAC;IACjD;IACA,OAAOJ,YAAY;EACvB;AACJ;AACA,MAAMD,kBAAkB,SAASZ,UAAU,CAAC;EACxCM,WAAWA,CAACY,WAAW,EAAEX,WAAW,EAAE;IAClC,KAAK,CAACW,WAAW,CAAC;IAClB,IAAI,CAACX,WAAW,GAAGA,WAAW;EAClC;EACAY,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEZ;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACA,WAAW,EAAE;MACd,IAAI,CAACS,UAAU,GAAG,IAAI;MACtB;IACJ;IACA,IAAI,CAACT,WAAW,GAAG,IAAI;IACvB,MAAMN,QAAQ,GAAGM,WAAW,CAACG,SAAS;IACtC,IAAIT,QAAQ,IAAI,CAAC,EAAE;MACf,IAAI,CAACe,UAAU,GAAG,IAAI;MACtB;IACJ;IACAT,WAAW,CAACG,SAAS,GAAGT,QAAQ,GAAG,CAAC;IACpC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACd,IAAI,CAACe,UAAU,GAAG,IAAI;MACtB;IACJ;IACA,MAAM;MAAEA;IAAW,CAAC,GAAG,IAAI;IAC3B,MAAMI,gBAAgB,GAAGb,WAAW,CAACc,WAAW;IAChD,IAAI,CAACL,UAAU,GAAG,IAAI;IACtB,IAAII,gBAAgB,KAAK,CAACJ,UAAU,IAAII,gBAAgB,KAAKJ,UAAU,CAAC,EAAE;MACtEI,gBAAgB,CAACE,WAAW,CAAC,CAAC;IAClC;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
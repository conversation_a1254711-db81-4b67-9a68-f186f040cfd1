{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function bufferWhen(closingSelector) {\n  return function (source) {\n    return source.lift(new <PERSON>uffer<PERSON>henOperator(closingSelector));\n  };\n}\nclass BufferWhenOperator {\n  constructor(closingSelector) {\n    this.closingSelector = closingSelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));\n  }\n}\nclass BufferWhenSubscriber extends OuterSubscriber {\n  constructor(destination, closingSelector) {\n    super(destination);\n    this.closingSelector = closingSelector;\n    this.subscribing = false;\n    this.openBuffer();\n  }\n  _next(value) {\n    this.buffer.push(value);\n  }\n  _complete() {\n    const buffer = this.buffer;\n    if (buffer) {\n      this.destination.next(buffer);\n    }\n    super._complete();\n  }\n  _unsubscribe() {\n    this.buffer = null;\n    this.subscribing = false;\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.openBuffer();\n  }\n  notifyComplete() {\n    if (this.subscribing) {\n      this.complete();\n    } else {\n      this.openBuffer();\n    }\n  }\n  openBuffer() {\n    let {\n      closingSubscription\n    } = this;\n    if (closingSubscription) {\n      this.remove(closingSubscription);\n      closingSubscription.unsubscribe();\n    }\n    const buffer = this.buffer;\n    if (this.buffer) {\n      this.destination.next(buffer);\n    }\n    this.buffer = [];\n    let closingNotifier;\n    try {\n      const {\n        closingSelector\n      } = this;\n      closingNotifier = closingSelector();\n    } catch (err) {\n      return this.error(err);\n    }\n    closingSubscription = new Subscription();\n    this.closingSubscription = closingSubscription;\n    this.add(closingSubscription);\n    this.subscribing = true;\n    closingSubscription.add(subscribeToResult(this, closingNotifier));\n    this.subscribing = false;\n  }\n}", "map": {"version": 3, "names": ["Subscription", "OuterSubscriber", "subscribeToResult", "bufferWhen", "closingSelector", "source", "lift", "BufferWhenOperator", "constructor", "call", "subscriber", "subscribe", "BufferWhenSubscriber", "destination", "subscribing", "openBuffer", "_next", "value", "buffer", "push", "_complete", "next", "_unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "notifyComplete", "complete", "closingSubscription", "remove", "unsubscribe", "closingNotifier", "err", "error", "add"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/bufferWhen.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function bufferWhen(closingSelector) {\n    return function (source) {\n        return source.lift(new <PERSON>uffer<PERSON>henOperator(closingSelector));\n    };\n}\nclass BufferWhenOperator {\n    constructor(closingSelector) {\n        this.closingSelector = closingSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));\n    }\n}\nclass BufferWhenSubscriber extends OuterSubscriber {\n    constructor(destination, closingSelector) {\n        super(destination);\n        this.closingSelector = closingSelector;\n        this.subscribing = false;\n        this.openBuffer();\n    }\n    _next(value) {\n        this.buffer.push(value);\n    }\n    _complete() {\n        const buffer = this.buffer;\n        if (buffer) {\n            this.destination.next(buffer);\n        }\n        super._complete();\n    }\n    _unsubscribe() {\n        this.buffer = null;\n        this.subscribing = false;\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.openBuffer();\n    }\n    notifyComplete() {\n        if (this.subscribing) {\n            this.complete();\n        }\n        else {\n            this.openBuffer();\n        }\n    }\n    openBuffer() {\n        let { closingSubscription } = this;\n        if (closingSubscription) {\n            this.remove(closingSubscription);\n            closingSubscription.unsubscribe();\n        }\n        const buffer = this.buffer;\n        if (this.buffer) {\n            this.destination.next(buffer);\n        }\n        this.buffer = [];\n        let closingNotifier;\n        try {\n            const { closingSelector } = this;\n            closingNotifier = closingSelector();\n        }\n        catch (err) {\n            return this.error(err);\n        }\n        closingSubscription = new Subscription();\n        this.closingSubscription = closingSubscription;\n        this.add(closingSubscription);\n        this.subscribing = true;\n        closingSubscription.add(subscribeToResult(this, closingNotifier));\n        this.subscribing = false;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAE;EACxC,OAAO,UAAUC,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACC,IAAI,CAAC,IAAIC,kBAAkB,CAACH,eAAe,CAAC,CAAC;EAC/D,CAAC;AACL;AACA,MAAMG,kBAAkB,CAAC;EACrBC,WAAWA,CAACJ,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,oBAAoB,CAACF,UAAU,EAAE,IAAI,CAACN,eAAe,CAAC,CAAC;EACvF;AACJ;AACA,MAAMQ,oBAAoB,SAASX,eAAe,CAAC;EAC/CO,WAAWA,CAACK,WAAW,EAAET,eAAe,EAAE;IACtC,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACT,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACU,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,CAACC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC;EAC3B;EACAG,SAASA,CAAA,EAAG;IACR,MAAMF,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACR,IAAI,CAACL,WAAW,CAACQ,IAAI,CAACH,MAAM,CAAC;IACjC;IACA,KAAK,CAACE,SAAS,CAAC,CAAC;EACrB;EACAE,YAAYA,CAAA,EAAG;IACX,IAAI,CAACJ,MAAM,GAAG,IAAI;IAClB,IAAI,CAACJ,WAAW,GAAG,KAAK;EAC5B;EACAS,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACb,UAAU,CAAC,CAAC;EACrB;EACAc,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACf,WAAW,EAAE;MAClB,IAAI,CAACgB,QAAQ,CAAC,CAAC;IACnB,CAAC,MACI;MACD,IAAI,CAACf,UAAU,CAAC,CAAC;IACrB;EACJ;EACAA,UAAUA,CAAA,EAAG;IACT,IAAI;MAAEgB;IAAoB,CAAC,GAAG,IAAI;IAClC,IAAIA,mBAAmB,EAAE;MACrB,IAAI,CAACC,MAAM,CAACD,mBAAmB,CAAC;MAChCA,mBAAmB,CAACE,WAAW,CAAC,CAAC;IACrC;IACA,MAAMf,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,CAACL,WAAW,CAACQ,IAAI,CAACH,MAAM,CAAC;IACjC;IACA,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAIgB,eAAe;IACnB,IAAI;MACA,MAAM;QAAE9B;MAAgB,CAAC,GAAG,IAAI;MAChC8B,eAAe,GAAG9B,eAAe,CAAC,CAAC;IACvC,CAAC,CACD,OAAO+B,GAAG,EAAE;MACR,OAAO,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;IAC1B;IACAJ,mBAAmB,GAAG,IAAI/B,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC+B,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACM,GAAG,CAACN,mBAAmB,CAAC;IAC7B,IAAI,CAACjB,WAAW,GAAG,IAAI;IACvBiB,mBAAmB,CAACM,GAAG,CAACnC,iBAAiB,CAAC,IAAI,EAAEgC,eAAe,CAAC,CAAC;IACjE,IAAI,CAACpB,WAAW,GAAG,KAAK;EAC5B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
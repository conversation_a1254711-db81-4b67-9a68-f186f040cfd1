{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nexport class Animation<PERSON>rameAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler.scheduled || (scheduler.scheduled = requestAnimationFrame(() => scheduler.flush(null)));\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0 || delay === null && this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n    if (scheduler.actions.length === 0) {\n      cancelAnimationFrame(id);\n      scheduler.scheduled = undefined;\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["AsyncAction", "AnimationFrameAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "scheduled", "requestAnimationFrame", "flush", "recycleAsyncId", "length", "cancelAnimationFrame", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduler/AnimationFrameAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nexport class Animation<PERSON>rameAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler.scheduled || (scheduler.scheduled = requestAnimationFrame(() => scheduler.flush(null)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        if ((delay !== null && delay > 0) || (delay === null && this.delay > 0)) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        if (scheduler.actions.length === 0) {\n            cancelAnimationFrame(id);\n            scheduler.scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,OAAO,MAAMC,oBAAoB,SAASD,WAAW,CAAC;EAClDE,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAACD,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,cAAcA,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK,CAACF,cAAc,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACAJ,SAAS,CAACK,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5B,OAAON,SAAS,CAACO,SAAS,KAAKP,SAAS,CAACO,SAAS,GAAGC,qBAAqB,CAAC,MAAMR,SAAS,CAACS,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5G;EACAC,cAAcA,CAACV,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAMA,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAAE,EAAE;MACrE,OAAO,KAAK,CAACM,cAAc,CAACV,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACA,IAAIJ,SAAS,CAACK,OAAO,CAACM,MAAM,KAAK,CAAC,EAAE;MAChCC,oBAAoB,CAACT,EAAE,CAAC;MACxBH,SAAS,CAACO,SAAS,GAAGM,SAAS;IACnC;IACA,OAAOA,SAAS;EACpB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
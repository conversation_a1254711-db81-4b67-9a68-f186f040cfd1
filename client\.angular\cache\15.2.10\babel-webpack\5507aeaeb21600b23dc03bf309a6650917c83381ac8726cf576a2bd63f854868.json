{"ast": null, "code": "import { trigger, transition, style, query, group, animateChild, animate } from '@angular/animations';\n// Animation: FadeInLeft\nexport const fadeInLeft = trigger('fadeInLeft', [transition('* => *', [query(':enter, :leave', style({\n  position: 'absolute',\n  left: 0,\n  width: '100%',\n  paddingRight: '2rem',\n  paddingLeft: '2rem',\n  opacity: 0\n}), {\n  optional: true\n}), query(':enter', style({\n  transform: 'translateX(-100%)',\n  opacity: 0\n}), {\n  optional: true\n}), group([query(':leave', [style({\n  transform: 'translateX(0%)'\n}), animate('600ms ease', style({\n  opacity: 1,\n  transform: 'translateX(100%)'\n}))], {\n  optional: true\n}), query(':enter', [animate('800ms ease', style({\n  opacity: 1,\n  transform: 'translateX(0%)'\n})), animateChild()], {\n  optional: true\n})])])]);\n// Animation: ZoomIn\nexport const zoomIn = trigger('zoomIn', [transition('* <=> *', [query(':enter, :leave', [style({\n  position: 'absolute',\n  left: 0,\n  width: '100%',\n  paddingRight: '2rem',\n  paddingLeft: '2rem',\n  opacity: 0,\n  transform: 'scale(0.5) translateY(-20%)'\n})], {\n  optional: true\n}), query(':enter', [animate('400ms ease', style({\n  opacity: 1,\n  paddingRight: '2rem',\n  paddingLeft: '2rem',\n  transform: 'scale(1) translateY(0)'\n}))], {\n  optional: true\n})])]);\n// Animation: FadeIn\nexport const fadeIn = trigger('fadeIn', [transition('* <=> *', [query(':enter, :leave', [style({\n  position: 'absolute',\n  left: 0,\n  width: '100%',\n  paddingRight: '2rem',\n  paddingLeft: '2rem',\n  opacity: 0\n})], {\n  optional: true\n}), query(':enter', [animate('500ms ease', style({\n  opacity: 1,\n  paddingRight: '2rem',\n  paddingLeft: '2rem'\n}))], {\n  optional: true\n})])]);", "map": {"version": 3, "mappings": "AAAA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,YAAY,EAAEC,OAAO,QAAmB,qBAAqB;AAEhH;AACA,OAAO,MAAMC,UAAU,GAAGP,OAAO,CAAC,YAAY,EAAE,CAC9CC,UAAU,CAAC,QAAQ,EAAE,CACnBE,KAAK,CACH,gBAAgB,EAChBD,KAAK,CAAC;EAAEM,QAAQ,EAAE,UAAU;EAAEC,IAAI,EAAE,CAAC;EAAEC,KAAK,EAAE,MAAM;EAAEC,YAAY,EAAE,MAAM;EAAEC,WAAW,EAAE,MAAM;EAAEC,OAAO,EAAE;AAAC,CAAE,CAAC,EAC9G;EACEC,QAAQ,EAAE;CACX,CACF,EACDX,KAAK,CAAC,QAAQ,EAAED,KAAK,CAAC;EAAEa,SAAS,EAAE,mBAAmB;EAAEF,OAAO,EAAE;AAAC,CAAE,CAAC,EAAE;EAAEC,QAAQ,EAAE;AAAI,CAAE,CAAC,EAE1FV,KAAK,CAAC,CACJD,KAAK,CACH,QAAQ,EACR,CACED,KAAK,CAAC;EAAEa,SAAS,EAAE;AAAgB,CAAE,CAAC,EACtCT,OAAO,CAAC,YAAY,EAAEJ,KAAK,CAAC;EAAEW,OAAO,EAAE,CAAC;EAAEE,SAAS,EAAE;AAAkB,CAAE,CAAC,CAAC,CAC5E,EACD;EAAED,QAAQ,EAAE;AAAI,CAAE,CACnB,EACDX,KAAK,CAAC,QAAQ,EAAE,CAACG,OAAO,CAAC,YAAY,EAAEJ,KAAK,CAAC;EAAEW,OAAO,EAAE,CAAC;EAAEE,SAAS,EAAE;AAAgB,CAAE,CAAC,CAAC,EAAEV,YAAY,EAAE,CAAC,EAAE;EAC3GS,QAAQ,EAAE;CACX,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AAEF;AACA,OAAO,MAAME,MAAM,GAAGhB,OAAO,CAAC,QAAQ,EAAE,CACtCC,UAAU,CAAC,SAAS,EAAE,CACpBE,KAAK,CACH,gBAAgB,EAChB,CACED,KAAK,CAAC;EACJM,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,YAAY,EAAE,MAAM;EACpBC,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE,CAAC;EACVE,SAAS,EAAE;CACZ,CAAC,CACH,EACD;EAAED,QAAQ,EAAE;AAAI,CAAE,CACnB,EACDX,KAAK,CACH,QAAQ,EACR,CACEG,OAAO,CACL,YAAY,EACZJ,KAAK,CAAC;EAAEW,OAAO,EAAE,CAAC;EAAEF,YAAY,EAAE,MAAM;EAAEC,WAAW,EAAE,MAAM;EAAEG,SAAS,EAAE;AAAwB,CAAE,CAAC,CACtG,CACF,EACD;EACED,QAAQ,EAAE;CACX,CACF,CACF,CAAC,CACH,CAAC;AAEF;AACA,OAAO,MAAMG,MAAM,GAAGjB,OAAO,CAAC,QAAQ,EAAE,CACtCC,UAAU,CAAC,SAAS,EAAE,CACpBE,KAAK,CACH,gBAAgB,EAChB,CACED,KAAK,CAAC;EACJM,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,YAAY,EAAE,MAAM;EACpBC,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE;CACV,CAAC,CACH,EACD;EAAEC,QAAQ,EAAE;AAAI,CAAE,CACnB,EACDX,KAAK,CAAC,QAAQ,EAAE,CAACG,OAAO,CAAC,YAAY,EAAEJ,KAAK,CAAC;EAAEW,OAAO,EAAE,CAAC;EAAEF,YAAY,EAAE,MAAM;EAAEC,WAAW,EAAE;AAAM,CAAE,CAAC,CAAC,CAAC,EAAE;EACzGE,QAAQ,EAAE;CACX,CAAC,CACH,CAAC,CACH,CAAC", "names": ["trigger", "transition", "style", "query", "group", "animate<PERSON><PERSON><PERSON>", "animate", "fadeInLeft", "position", "left", "width", "paddingRight", "paddingLeft", "opacity", "optional", "transform", "zoomIn", "fadeIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\animations\\core.animation.ts"], "sourcesContent": ["import { trigger, transition, style, query, group, animateChild, animate, keyframes } from '@angular/animations';\r\n\r\n// Animation: FadeInLeft\r\nexport const fadeInLeft = trigger('fadeInLeft', [\r\n  transition('* => *', [\r\n    query(\r\n      ':enter, :leave',\r\n      style({ position: 'absolute', left: 0, width: '100%', paddingRight: '2rem', paddingLeft: '2rem', opacity: 0 }),\r\n      {\r\n        optional: true\r\n      }\r\n    ),\r\n    query(':enter', style({ transform: 'translateX(-100%)', opacity: 0 }), { optional: true }),\r\n\r\n    group([\r\n      query(\r\n        ':leave',\r\n        [\r\n          style({ transform: 'translateX(0%)' }),\r\n          animate('600ms ease', style({ opacity: 1, transform: 'translateX(100%)' }))\r\n        ],\r\n        { optional: true }\r\n      ),\r\n      query(':enter', [animate('800ms ease', style({ opacity: 1, transform: 'translateX(0%)' })), animateChild()], {\r\n        optional: true\r\n      })\r\n    ])\r\n  ])\r\n]);\r\n\r\n// Animation: ZoomIn\r\nexport const zoomIn = trigger('zoomIn', [\r\n  transition('* <=> *', [\r\n    query(\r\n      ':enter, :leave',\r\n      [\r\n        style({\r\n          position: 'absolute',\r\n          left: 0,\r\n          width: '100%',\r\n          paddingRight: '2rem',\r\n          paddingLeft: '2rem',\r\n          opacity: 0,\r\n          transform: 'scale(0.5) translateY(-20%)'\r\n        })\r\n      ],\r\n      { optional: true }\r\n    ),\r\n    query(\r\n      ':enter',\r\n      [\r\n        animate(\r\n          '400ms ease',\r\n          style({ opacity: 1, paddingRight: '2rem', paddingLeft: '2rem', transform: 'scale(1) translateY(0)' })\r\n        )\r\n      ],\r\n      {\r\n        optional: true\r\n      }\r\n    )\r\n  ])\r\n]);\r\n\r\n// Animation: FadeIn\r\nexport const fadeIn = trigger('fadeIn', [\r\n  transition('* <=> *', [\r\n    query(\r\n      ':enter, :leave',\r\n      [\r\n        style({\r\n          position: 'absolute',\r\n          left: 0,\r\n          width: '100%',\r\n          paddingRight: '2rem',\r\n          paddingLeft: '2rem',\r\n          opacity: 0\r\n        })\r\n      ],\r\n      { optional: true }\r\n    ),\r\n    query(':enter', [animate('500ms ease', style({ opacity: 1, paddingRight: '2rem', paddingLeft: '2rem' }))], {\r\n      optional: true\r\n    })\r\n  ])\r\n]);\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
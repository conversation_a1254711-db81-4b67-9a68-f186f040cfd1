{"ast": null, "code": "/**\r\n * Default App Config\r\n *\r\n * ? TIP:\r\n *\r\n * Change app config based on your preferences.\r\n * You can also change them on each component basis. i.e `app/main/pages/authentication/auth-login-v1/auth-login-v1.component.ts`\r\n *\r\n * ! IMPORTANT: If the enableLocalStorage option is true then make sure you clear the browser local storage(https://developers.google.com/web/tools/chrome-devtools/storage/localstorage#delete).\r\n *  ! Otherwise, it will not take the below config changes and use stored config from local storage.\r\n *\r\n */\n// prettier-ignore\nexport const coreConfig = {\n  app: {\n    appName: 'EZ League',\n    appTitle: 'EZ League - A Sports Management System',\n    appLogoImage: 'assets/images/logo/logo.png',\n    appLanguage: 'en' // App Default Language (en, fr, de, pt etc..)\n  },\n\n  layout: {\n    skin: 'default',\n    type: 'vertical',\n    animation: 'fadeIn',\n    menu: {\n      hidden: false,\n      collapsed: false // Boolean: true, false\n    },\n\n    // ? For horizontal menu, navbar type will work for navMenu type\n    navbar: {\n      hidden: false,\n      type: 'floating-nav',\n      background: 'navbar-light',\n      customBackgroundColor: true,\n      backgroundColor: '' // BS color i.e bg-primary, bg-success\n    },\n\n    footer: {\n      hidden: false,\n      type: 'footer-static',\n      background: 'footer-light',\n      customBackgroundColor: false,\n      backgroundColor: '' // BS color i.e bg-primary, bg-success\n    },\n\n    enableLocalStorage: true,\n    customizer: false,\n    scrollTop: true,\n    buyNow: false // Boolean: true, false (Set false in real project, For demo purpose only)\n  }\n};\n\nexport class AppConfig {\n  constructor() {}\n  static #_ = this.APP_NAME = 'EZ League';\n  static #_2 = this.LANGUAGES = [{\n    code: 'en',\n    name: 'English',\n    shortname: 'ENG',\n    flag: 'us'\n  }, {\n    code: 'zh_HK',\n    name: '繁體中文',\n    shortname: '中文',\n    flag: 'hk'\n  }\n  // { code: 'vi', name: 'Tiếng Việt', shortname: 'VN', flag: 'vn' },\n  ];\n  static #_3 = this.PROJECT_ID = 'EZLEAGUE';\n  static #_4 = this.Fake_Player_Photo = 'assets/images/example_uploads/avatar.jpg';\n  static #_5 = this.Fake_Player_Name = 'John Doe';\n  static #_6 = this.Fake_Player_Link = 'https://www.google.com/';\n  static #_7 = this.Fake_Player_Description = `Lorem ipsum dolor sit amet consectetur adipisicing elit. Tempora dolor debitis quibusdam repellendus delectus sed quo perspiciatis omnis atque magnam. Impedit omnis dicta tempore in ut quisquam dolores minima amet?\n    Modi doloremque unde aperiam, quasi libero fuga alias, ratione tempore ducimus aut omnis quo id nihil expedita? Repellendus consequatur aspernatur facere similique veritatis quod, modi dignissimos, commodi omnis aperiam esse?\n    Ipsum minus perspiciatis nobis repellendus magni voluptates aliquid dicta quae perferendis. Modi odit, ex, doloremque suscipit repellendus est non inventore autem natus perferendis nulla et repellat recusandae animi numquam laboriosam!`;\n  static #_8 = this.Fake_ID_Photo = 'assets/images/example_uploads/hkid.jpg';\n  static #_9 = this.GROUP_TYPE = {\n    Mixed: 'Mixed',\n    Boys: 'Boys',\n    Girls: 'Girls'\n  };\n  static #_10 = this.GENDER = {\n    Male: 'Male',\n    Female: 'Female'\n  };\n  static #_11 = this.VALIDATE_STATUS = {\n    Pending: 'Pending',\n    AwaitingUpdate: 'Awaiting Update',\n    Validated: 'Validated',\n    Updated: 'Updated'\n  };\n  static #_12 = this.REGISTER_TYPE = {\n    new: 'New',\n    awaiting_update: 'Awaiting Update',\n    reregister: 'Re-Register',\n    edit: 'Edit'\n  };\n  static #_13 = this.APPROVE_STATUS = {\n    Registered: 'Registered',\n    Approved: 'Approved',\n    Rejected: 'Rejected',\n    Cancelled: 'Cancelled',\n    PaymentError: 'Payment Error',\n    Pending: 'Pending'\n  };\n  static #_14 = this.PERMISSIONS = {\n    registration: 11,\n    manage_registrations: 12,\n    team_management: 21,\n    assign_players: 22,\n    assign_coaches: 23,\n    manage_teamsheets: 24,\n    manage_leagues: 31,\n    update_score: 32,\n    league_reports: 33,\n    fixtures_results: 34,\n    manage_events: 41,\n    manage_groups: 42,\n    manage_clubs: 43,\n    manage_locations: 44,\n    manage_users: 45,\n    send_messages: 46,\n    manage_payments: 47,\n    registrations_report: 48,\n    manage_settings: 49,\n    manage_request_update_players: 50,\n    photo_gallery: 51,\n    manage_streaming: 52,\n    streaming: 53,\n    manage_account: 54,\n    check_update: 55,\n    manage_children: 56,\n    sponsors: 57,\n    update_photo: 58,\n    show_video_stream: 59,\n    email_templates: 60\n  };\n  static #_15 = this.TOURNAMENT_TYPES = {\n    league: 'League',\n    knockout: 'Knockout',\n    league_knockout: 'League + Knockout',\n    groups: 'Groups',\n    groups_knockouts: 'Groups + Knockout(s)'\n  };\n  static #_16 = this.KNOCKOUT_TYPES = {\n    type1: '1v1, 2v2, 3v3, 4v4',\n    type2: '1v2, 2v1, 3v4, 4v3',\n    type3: '1v2, 2v1',\n    type4: 'Custom'\n  };\n  static #_17 = this.USER_ROLES = {\n    admin: 2,\n    parent: 5,\n    player: 6\n  };\n  static #_18 = this.RANKING_CRITERIA = {\n    total: 'Total',\n    direct_matches: 'Direct matches',\n    head_to_head: 'Head to Head'\n  };\n  static #_19 = this.KNOCKOUT_MODES = {\n    single: {\n      value: 1,\n      label: 'Single elimination'\n    },\n    double: {\n      value: 2,\n      label: 'Double elimination'\n    }\n  };\n  static #_20 = this.MATCH_DETAIL_TYPES = {\n    goal: 'Goal',\n    yellow_card: 'Yellow Card',\n    red_card: 'Red Card',\n    substitution: 'Substitution',\n    penalty: 'Penalty'\n  };\n  static #_21 = this.CANCEL_MATCH_TYPES = ['Postponed', 'Cancelled', 'Abandoned', 'Rescheduled']; // Send Message Type\n  static #_22 = this.SEND_MESSAGE_TYPES = {\n    email: 'Email',\n    push_noti: 'Push Notification',\n    email_push_noti: 'Email & Push Notification'\n  };\n  static #_23 = this.SETTINGS_KEYS = {\n    SMTP: 'smtp_account',\n    REQUIRED_VERSIONS: 'r_version',\n    INIT_JSON: 'init_json',\n    NOTIFICATION: 'notification',\n    POLICY_NOTIFICATION: 'policy_notification',\n    LIVEKIT: 'livekit',\n    PAYMENT: 'payment_settings',\n    SCORDBOARD: 'scoreboard',\n    ABOUT: 'about',\n    WEATHER: 'weather policy',\n    CONDUCT: 'code_of_conduct',\n    NAME_SETTINGS: 'name_settings'\n  }; // PAYMENT DETAILS TYPE\n  static #_24 = this.PAYMENT_DETAIL_TYPES = {\n    registration: 'registration',\n    other: 'other'\n  }; // PAYMENT_STATUS\n  static #_25 = this.PAYMENT_STATUS = {\n    open: 'open',\n    sent: 'sent',\n    paid: 'paid',\n    failed: 'failed',\n    cancelled: 'cancelled',\n    succeeded: 'succeeded',\n    refunded: 'refunded',\n    partially_refunded: 'partially refunded',\n    marked_as_refunded: 'marked as refunded',\n    marked_as_paid: 'marked as paid',\n    pending: 'pending',\n    draft: 'draft'\n  };\n  static #_26 = this.PAYMENT_STATUS_PAID = [this.PAYMENT_STATUS.paid, this.PAYMENT_STATUS.succeeded];\n  static #_27 = this.PAYMENT_STATUS_SENT = [this.PAYMENT_STATUS.open, this.PAYMENT_STATUS.sent];\n  static #_28 = this.PAYMENT_STATUS_REFUND = [this.PAYMENT_STATUS.refunded, this.PAYMENT_STATUS.partially_refunded, this.PAYMENT_STATUS.marked_as_refunded];\n  static #_29 = this.PAYMENT_STATUS_REFUNDED = [this.PAYMENT_STATUS.refunded, this.PAYMENT_STATUS.marked_as_refunded];\n  static #_30 = this.PAYMENT_NOTE_TYPES = {\n    REFUND: 'REFUND',\n    CANCEL: 'CANCEL'\n  };\n  static #_31 = this.PLAYER_UPDATE_STATUS = {\n    pending: 'Pending',\n    rejected: 'Rejected',\n    approved: 'Approved'\n  };\n  static #_32 = this.BLACK_LIST_STATUS = {\n    block: 'Block',\n    unsubscribed: 'Unsubscribed'\n  };\n  static #_33 = this.MAX_UPLOAD_SIZE = 2; // 5MB\n}", "map": {"version": 3, "mappings": "AAEA;;;;;;;;;;;;AAaA;AACA,OAAO,MAAMA,UAAU,GAAe;EACpCC,GAAG,EAAE;IACHC,OAAO,EAAO,WAAW;IACzBC,QAAQ,EAAM,wCAAwC;IACtDC,YAAY,EAAE,6BAA6B;IAC3CC,WAAW,EAAG,IAAI,CAAE;GACrB;;EACDC,MAAM,EAAE;IACNC,IAAI,EAAI,SAAS;IACjBC,IAAI,EAAI,UAAU;IAClBC,SAAS,EAAG,QAAQ;IACpBC,IAAI,EAAG;MACLC,MAAM,EAAiB,KAAK;MAC5BC,SAAS,EAAc,KAAK,CAAY;KACzC;;IACD;IACAC,MAAM,EAAE;MACNF,MAAM,EAAiB,KAAK;MAC5BH,IAAI,EAAmB,cAAc;MACrCM,UAAU,EAAa,cAAc;MACrCC,qBAAqB,EAAE,IAAI;MAC3BC,eAAe,EAAQ,EAAE,CAAe;KACzC;;IACDC,MAAM,EAAE;MACNN,MAAM,EAAiB,KAAK;MAC5BH,IAAI,EAAmB,eAAe;MACtCM,UAAU,EAAa,cAAc;MACrCC,qBAAqB,EAAE,KAAK;MAC5BC,eAAe,EAAQ,EAAE,CAAe;KACzC;;IACDE,kBAAkB,EAAE,IAAI;IACxBC,UAAU,EAAI,KAAK;IACnBC,SAAS,EAAK,IAAI;IAClBC,MAAM,EAAQ,KAAK,CAAwB;;CAE9C;;AAED,OAAM,MAAOC,SAAS;EACpBC,YAAA,GAAe;EAAC,QAAAC,CAAA,GACF,KAAAC,QAAQ,GAAG,WAAW;EAAA,QAAAC,EAAA,GACtB,KAAAC,SAAS,GAAG,CACxB;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAI,CAAE,EAC7D;IAAEH,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAI;EAC1D;EAAA,CACD;EAAA,QAAAC,EAAA,GAEa,KAAAC,UAAU,GAAW,UAAU;EAAA,QAAAC,EAAA,GAC/B,KAAAC,iBAAiB,GAAG,0CAA0C;EAAA,QAAAC,EAAA,GAC9D,KAAAC,gBAAgB,GAAG,UAAU;EAAA,QAAAC,EAAA,GAC7B,KAAAC,gBAAgB,GAAG,yBAAyB;EAAA,QAAAC,EAAA,GAC5C,KAAAC,uBAAuB,GAAG;;gPAEsM;EAAA,QAAAC,EAAA,GAChO,KAAAC,aAAa,GAAG,wCAAwC;EAAA,QAAAC,EAAA,GAExD,KAAAC,UAAU,GAAG;IACzBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;GACR;EAAA,QAAAC,GAAA,GAEa,KAAAC,MAAM,GAAG;IACrBC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE;GACT;EAAA,QAAAC,GAAA,GAEa,KAAAC,eAAe,GAAG;IAC9BC,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE;GACV;EAAA,QAAAC,GAAA,GAEa,KAAAC,aAAa,GAAG;IAC5BC,GAAG,EAAE,KAAK;IACVC,eAAe,EAAE,iBAAiB;IAClCC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE;GACP;EAAA,QAAAC,GAAA,GAEa,KAAAC,cAAc,GAAG;IAC7BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,eAAe;IAC7BhB,OAAO,EAAE;GACV;EAAA,QAAAiB,GAAA,GAEa,KAAAC,WAAW,GAAG;IAC1BC,YAAY,EAAE,EAAE;IAChBC,oBAAoB,EAAE,EAAE;IACxBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,oBAAoB,EAAE,EAAE;IACxBC,eAAe,EAAE,EAAE;IACnBC,6BAA6B,EAAE,EAAE;IACjCC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE;GAClB;EAAA,QAAAC,GAAA,GAEa,KAAAC,gBAAgB,GAAG;IAC/BC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,mBAAmB;IACpCC,MAAM,EAAE,QAAQ;IAChBC,gBAAgB,EAAE;GACnB;EAAA,QAAAC,GAAA,GAEa,KAAAC,cAAc,GAAG;IAC7BC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;GACR;EAAA,QAAAC,GAAA,GAEa,KAAAC,UAAU,GAAG;IACzBC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;GACT;EAAA,QAAAC,GAAA,GACa,KAAAC,gBAAgB,GAAG;IAC/BC,KAAK,EAAE,OAAO;IACdC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE;GACf;EAAA,QAAAC,GAAA,GACa,KAAAC,cAAc,GAAG;IAC7BC,MAAM,EAAE;MACNC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;KACR;IACDC,MAAM,EAAE;MACNF,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;;GAEV;EAAA,QAAAE,GAAA,GAEa,KAAAC,kBAAkB,GAAG;IACjCC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE;GACV;EAAA,QAAAC,GAAA,GAEa,KAAAC,kBAAkB,GAAG,CACjC,WAAW,EACX,WAAW,EACX,WAAW,EACX,aAAa,CACd,EAED;EAAA,QAAAC,GAAA,GACc,KAAAC,kBAAkB,GAAG;IACjCC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,mBAAmB;IAC9BC,eAAe,EAAE;GAClB;EAAA,QAAAC,GAAA,GAEa,KAAAC,aAAa,GAAG;IAC5BC,IAAI,EAAE,cAAc;IACpBC,iBAAiB,EAAE,WAAW;IAC9BC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,cAAc;IAC5BC,mBAAmB,EAAE,qBAAqB;IAC1CC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,YAAY;IACxBC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,gBAAgB;IACzBC,OAAO,EAAE,iBAAiB;IAC1BC,aAAa,EAAE;GAChB,EAED;EAAA,QAAAC,GAAA,GACc,KAAAC,oBAAoB,GAAG;IACnCxF,YAAY,EAAE,cAAc;IAC5ByF,KAAK,EAAE;GACR,EAED;EAAA,QAAAC,GAAA,GACc,KAAAC,cAAc,GAAG;IAC7BC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,kBAAkB,EAAE,oBAAoB;IACxCC,kBAAkB,EAAE,oBAAoB;IACxCC,cAAc,EAAE,gBAAgB;IAChCC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;GACR;EAAA,QAAAC,GAAA,GAEa,KAAAC,mBAAmB,GAAG,CAClC,IAAI,CAACd,cAAc,CAACG,IAAI,EACxB,IAAI,CAACH,cAAc,CAACM,SAAS,CAC9B;EAAA,QAAAS,GAAA,GACa,KAAAC,mBAAmB,GAAG,CAClC,IAAI,CAAChB,cAAc,CAACC,IAAI,EACxB,IAAI,CAACD,cAAc,CAACE,IAAI,CACzB;EAAA,QAAAe,GAAA,GACa,KAAAC,qBAAqB,GAAG,CACpC,IAAI,CAAClB,cAAc,CAACO,QAAQ,EAC5B,IAAI,CAACP,cAAc,CAACQ,kBAAkB,EACtC,IAAI,CAACR,cAAc,CAACS,kBAAkB,CACvC;EAAA,QAAAU,GAAA,GACa,KAAAC,uBAAuB,GAAG,CACtC,IAAI,CAACpB,cAAc,CAACO,QAAQ,EAC5B,IAAI,CAACP,cAAc,CAACS,kBAAkB,CACvC;EAAA,QAAAY,GAAA,GAEa,KAAAC,kBAAkB,GAAG;IACjCC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE;GACT;EAAA,QAAAC,GAAA,GAEa,KAAAC,oBAAoB,GAAG;IACnCf,OAAO,EAAE,SAAS;IAClBgB,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE;GACX;EAAA,QAAAC,GAAA,GACa,KAAAC,iBAAiB,GAAG;IAChCC,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE;GACf;EAAA,QAAAC,GAAA,GAEa,KAAAC,eAAe,GAAG,CAAC,EAAE", "names": ["coreConfig", "app", "appName", "appTitle", "appLogoImage", "appLanguage", "layout", "skin", "type", "animation", "menu", "hidden", "collapsed", "navbar", "background", "customBackgroundColor", "backgroundColor", "footer", "enableLocalStorage", "customizer", "scrollTop", "buyNow", "AppConfig", "constructor", "_", "APP_NAME", "_2", "LANGUAGES", "code", "name", "shortname", "flag", "_3", "PROJECT_ID", "_4", "Fake_Player_Photo", "_5", "Fake_Player_Name", "_6", "Fake_Player_Link", "_7", "Fake_Player_Description", "_8", "Fake_ID_Photo", "_9", "GROUP_TYPE", "Mixed", "Boys", "Girls", "_10", "GENDER", "Male", "Female", "_11", "VALIDATE_STATUS", "Pending", "AwaitingUpdate", "Validated", "Updated", "_12", "REGISTER_TYPE", "new", "awaiting_update", "reregister", "edit", "_13", "APPROVE_STATUS", "Registered", "Approved", "Rejected", "Cancelled", "PaymentError", "_14", "PERMISSIONS", "registration", "manage_registrations", "team_management", "assign_players", "assign_coaches", "manage_teamsheets", "manage_leagues", "update_score", "league_reports", "fixtures_results", "manage_events", "manage_groups", "manage_clubs", "manage_locations", "manage_users", "send_messages", "manage_payments", "registrations_report", "manage_settings", "manage_request_update_players", "photo_gallery", "manage_streaming", "streaming", "manage_account", "check_update", "manage_children", "sponsors", "update_photo", "show_video_stream", "email_templates", "_15", "TOURNAMENT_TYPES", "league", "knockout", "league_knockout", "groups", "groups_knockouts", "_16", "KNOCKOUT_TYPES", "type1", "type2", "type3", "type4", "_17", "USER_ROLES", "admin", "parent", "player", "_18", "RANKING_CRITERIA", "total", "direct_matches", "head_to_head", "_19", "KNOCKOUT_MODES", "single", "value", "label", "double", "_20", "MATCH_DETAIL_TYPES", "goal", "yellow_card", "red_card", "substitution", "penalty", "_21", "CANCEL_MATCH_TYPES", "_22", "SEND_MESSAGE_TYPES", "email", "push_noti", "email_push_noti", "_23", "SETTINGS_KEYS", "SMTP", "REQUIRED_VERSIONS", "INIT_JSON", "NOTIFICATION", "POLICY_NOTIFICATION", "LIVEKIT", "PAYMENT", "SCORDBOARD", "ABOUT", "WEATHER", "CONDUCT", "NAME_SETTINGS", "_24", "PAYMENT_DETAIL_TYPES", "other", "_25", "PAYMENT_STATUS", "open", "sent", "paid", "failed", "cancelled", "succeeded", "refunded", "partially_refunded", "marked_as_refunded", "marked_as_paid", "pending", "draft", "_26", "PAYMENT_STATUS_PAID", "_27", "PAYMENT_STATUS_SENT", "_28", "PAYMENT_STATUS_REFUND", "_29", "PAYMENT_STATUS_REFUNDED", "_30", "PAYMENT_NOTE_TYPES", "REFUND", "CANCEL", "_31", "PLAYER_UPDATE_STATUS", "rejected", "approved", "_32", "BLACK_LIST_STATUS", "block", "unsubscribed", "_33", "MAX_UPLOAD_SIZE"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\app-config.ts"], "sourcesContent": ["import { CoreConfig } from '@core/types';\r\n\r\n/**\r\n * Default App Config\r\n *\r\n * ? TIP:\r\n *\r\n * Change app config based on your preferences.\r\n * You can also change them on each component basis. i.e `app/main/pages/authentication/auth-login-v1/auth-login-v1.component.ts`\r\n *\r\n * ! IMPORTANT: If the enableLocalStorage option is true then make sure you clear the browser local storage(https://developers.google.com/web/tools/chrome-devtools/storage/localstorage#delete).\r\n *  ! Otherwise, it will not take the below config changes and use stored config from local storage.\r\n *\r\n */\r\n\r\n// prettier-ignore\r\nexport const coreConfig: CoreConfig = {\r\n  app: {\r\n    appName     : 'EZ League',// App Name\r\n    appTitle    : 'EZ League - A Sports Management System', // App Title\r\n    appLogoImage: 'assets/images/logo/logo.png',// App Logo\r\n    appLanguage : 'en', // App Default Language (en, fr, de, pt etc..)\r\n  },\r\n  layout: {\r\n    skin  : 'default',                        // default, dark, bordered, semi-dark\r\n    type  : 'vertical',                       // vertical, horizontal\r\n    animation : 'fadeIn',                     // fadeInLeft, zoomIn , fadeIn, none\r\n    menu : {\r\n      hidden               : false,           // Boolean: true, false\r\n      collapsed            : false,           // Boolean: true, false\r\n    },\r\n    // ? For horizontal menu, navbar type will work for navMenu type\r\n    navbar: {\r\n      hidden               : false,           // Boolean: true, false\r\n      type                 : 'floating-nav',  // navbar-static-top, fixed-top, floating-nav, d-none\r\n      background           : 'navbar-light',  // navbar-light. navbar-dark\r\n      customBackgroundColor: true,            // Boolean: true, false\r\n      backgroundColor      : ''               // BS color i.e bg-primary, bg-success\r\n    },\r\n    footer: {\r\n      hidden               : false,           // Boolean: true, false\r\n      type                 : 'footer-static', // footer-static, footer-sticky, d-none\r\n      background           : 'footer-light',  // footer-light. footer-dark\r\n      customBackgroundColor: false,           // Boolean: true, false\r\n      backgroundColor      : ''               // BS color i.e bg-primary, bg-success\r\n    },\r\n    enableLocalStorage: true,\r\n    customizer  : false,                       // Boolean: true, false (Enable theme customizer)\r\n    scrollTop   : true,                       // Boolean: true, false (Enable scroll to top button)\r\n    buyNow      : false                        // Boolean: true, false (Set false in real project, For demo purpose only)\r\n  }\r\n}\r\n\r\nexport class AppConfig {\r\n  constructor() {}\r\n  public static APP_NAME = 'EZ League';\r\n  public static LANGUAGES = [\r\n    { code: 'en', name: 'English', shortname: 'ENG', flag: 'us' },\r\n    { code: 'zh_HK', name: '繁體中文', shortname: '中文', flag: 'hk' },\r\n    // { code: 'vi', name: 'Tiếng Việt', shortname: 'VN', flag: 'vn' },\r\n  ];\r\n\r\n  public static PROJECT_ID: string = 'EZLEAGUE';\r\n  public static Fake_Player_Photo = 'assets/images/example_uploads/avatar.jpg';\r\n  public static Fake_Player_Name = 'John Doe';\r\n  public static Fake_Player_Link = 'https://www.google.com/';\r\n  public static Fake_Player_Description = `Lorem ipsum dolor sit amet consectetur adipisicing elit. Tempora dolor debitis quibusdam repellendus delectus sed quo perspiciatis omnis atque magnam. Impedit omnis dicta tempore in ut quisquam dolores minima amet?\r\n    Modi doloremque unde aperiam, quasi libero fuga alias, ratione tempore ducimus aut omnis quo id nihil expedita? Repellendus consequatur aspernatur facere similique veritatis quod, modi dignissimos, commodi omnis aperiam esse?\r\n    Ipsum minus perspiciatis nobis repellendus magni voluptates aliquid dicta quae perferendis. Modi odit, ex, doloremque suscipit repellendus est non inventore autem natus perferendis nulla et repellat recusandae animi numquam laboriosam!`;\r\n  public static Fake_ID_Photo = 'assets/images/example_uploads/hkid.jpg';\r\n\r\n  public static GROUP_TYPE = {\r\n    Mixed: 'Mixed',\r\n    Boys: 'Boys',\r\n    Girls: 'Girls',\r\n  };\r\n\r\n  public static GENDER = {\r\n    Male: 'Male',\r\n    Female: 'Female',\r\n  };\r\n\r\n  public static VALIDATE_STATUS = {\r\n    Pending: 'Pending',\r\n    AwaitingUpdate: 'Awaiting Update',\r\n    Validated: 'Validated',\r\n    Updated: 'Updated',\r\n  };\r\n\r\n  public static REGISTER_TYPE = {\r\n    new: 'New',\r\n    awaiting_update: 'Awaiting Update',\r\n    reregister: 'Re-Register',\r\n    edit: 'Edit',\r\n  };\r\n\r\n  public static APPROVE_STATUS = {\r\n    Registered: 'Registered',\r\n    Approved: 'Approved',\r\n    Rejected: 'Rejected',\r\n    Cancelled: 'Cancelled',\r\n    PaymentError: 'Payment Error',\r\n    Pending: 'Pending',\r\n  };\r\n\r\n  public static PERMISSIONS = {\r\n    registration: 11,\r\n    manage_registrations: 12,\r\n    team_management: 21,\r\n    assign_players: 22,\r\n    assign_coaches: 23,\r\n    manage_teamsheets: 24,\r\n    manage_leagues: 31,\r\n    update_score: 32,\r\n    league_reports: 33,\r\n    fixtures_results: 34,\r\n    manage_events: 41,\r\n    manage_groups: 42,\r\n    manage_clubs: 43,\r\n    manage_locations: 44,\r\n    manage_users: 45,\r\n    send_messages: 46,\r\n    manage_payments: 47,\r\n    registrations_report: 48,\r\n    manage_settings: 49,\r\n    manage_request_update_players: 50,\r\n    photo_gallery: 51,\r\n    manage_streaming: 52,\r\n    streaming: 53,\r\n    manage_account: 54,\r\n    check_update: 55,\r\n    manage_children: 56,\r\n    sponsors: 57,\r\n    update_photo: 58,\r\n    show_video_stream: 59,\r\n    email_templates: 60\r\n  };\r\n\r\n  public static TOURNAMENT_TYPES = {\r\n    league: 'League',\r\n    knockout: 'Knockout',\r\n    league_knockout: 'League + Knockout',\r\n    groups: 'Groups',\r\n    groups_knockouts: 'Groups + Knockout(s)',\r\n  };\r\n\r\n  public static KNOCKOUT_TYPES = {\r\n    type1: '1v1, 2v2, 3v3, 4v4',\r\n    type2: '1v2, 2v1, 3v4, 4v3',\r\n    type3: '1v2, 2v1',\r\n    type4: 'Custom',\r\n  };\r\n\r\n  public static USER_ROLES = {\r\n    admin: 2,\r\n    parent: 5,\r\n    player: 6,\r\n  };\r\n  public static RANKING_CRITERIA = {\r\n    total: 'Total',\r\n    direct_matches: 'Direct matches',\r\n    head_to_head: 'Head to Head',\r\n  };\r\n  public static KNOCKOUT_MODES = {\r\n    single: {\r\n      value: 1,\r\n      label: 'Single elimination',\r\n    },\r\n    double: {\r\n      value: 2,\r\n      label: 'Double elimination',\r\n    },\r\n  };\r\n\r\n  public static MATCH_DETAIL_TYPES = {\r\n    goal: 'Goal',\r\n    yellow_card: 'Yellow Card',\r\n    red_card: 'Red Card',\r\n    substitution: 'Substitution',\r\n    penalty: 'Penalty',\r\n  };\r\n\r\n  public static CANCEL_MATCH_TYPES = [\r\n    'Postponed',\r\n    'Cancelled',\r\n    'Abandoned',\r\n    'Rescheduled',\r\n  ];\r\n\r\n  // Send Message Type\r\n  public static SEND_MESSAGE_TYPES = {\r\n    email: 'Email',\r\n    push_noti: 'Push Notification',\r\n    email_push_noti: 'Email & Push Notification',\r\n  };\r\n\r\n  public static SETTINGS_KEYS = {\r\n    SMTP: 'smtp_account',\r\n    REQUIRED_VERSIONS: 'r_version',\r\n    INIT_JSON: 'init_json',\r\n    NOTIFICATION: 'notification',\r\n    POLICY_NOTIFICATION: 'policy_notification',\r\n    LIVEKIT: 'livekit',\r\n    PAYMENT: 'payment_settings',\r\n    SCORDBOARD: 'scoreboard',\r\n    ABOUT: 'about',\r\n    WEATHER: 'weather policy',\r\n    CONDUCT: 'code_of_conduct',\r\n    NAME_SETTINGS: 'name_settings',\r\n  };\r\n\r\n  // PAYMENT DETAILS TYPE\r\n  public static PAYMENT_DETAIL_TYPES = {\r\n    registration: 'registration',\r\n    other: 'other',\r\n  };\r\n\r\n  // PAYMENT_STATUS\r\n  public static PAYMENT_STATUS = {\r\n    open: 'open',\r\n    sent: 'sent',\r\n    paid: 'paid',\r\n    failed: 'failed',\r\n    cancelled: 'cancelled',\r\n    succeeded: 'succeeded',\r\n    refunded: 'refunded',\r\n    partially_refunded: 'partially refunded',\r\n    marked_as_refunded: 'marked as refunded',\r\n    marked_as_paid: 'marked as paid',\r\n    pending: 'pending',\r\n    draft: 'draft',\r\n  };\r\n\r\n  public static PAYMENT_STATUS_PAID = [\r\n    this.PAYMENT_STATUS.paid,\r\n    this.PAYMENT_STATUS.succeeded,\r\n  ];\r\n  public static PAYMENT_STATUS_SENT = [\r\n    this.PAYMENT_STATUS.open,\r\n    this.PAYMENT_STATUS.sent,\r\n  ];\r\n  public static PAYMENT_STATUS_REFUND = [\r\n    this.PAYMENT_STATUS.refunded,\r\n    this.PAYMENT_STATUS.partially_refunded,\r\n    this.PAYMENT_STATUS.marked_as_refunded,\r\n  ];\r\n  public static PAYMENT_STATUS_REFUNDED = [\r\n    this.PAYMENT_STATUS.refunded,\r\n    this.PAYMENT_STATUS.marked_as_refunded,\r\n  ];\r\n\r\n  public static PAYMENT_NOTE_TYPES = {\r\n    REFUND: 'REFUND',\r\n    CANCEL: 'CANCEL',\r\n  };\r\n\r\n  public static PLAYER_UPDATE_STATUS = {\r\n    pending: 'Pending',\r\n    rejected: 'Rejected',\r\n    approved: 'Approved',\r\n  };\r\n  public static BLACK_LIST_STATUS = {\r\n    block: 'Block',\r\n    unsubscribed: 'Unsubscribed',\r\n  }\r\n\r\n  public static MAX_UPLOAD_SIZE = 2; // 5MB\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
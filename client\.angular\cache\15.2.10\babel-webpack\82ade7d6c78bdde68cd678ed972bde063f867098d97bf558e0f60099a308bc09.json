{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n  constructor(SchedulerAction = VirtualAction, maxFrames = Number.POSITIVE_INFINITY) {\n    super(SchedulerAction, () => this.frame);\n    this.maxFrames = maxFrames;\n    this.frame = 0;\n    this.index = -1;\n  }\n  flush() {\n    const {\n      actions,\n      maxFrames\n    } = this;\n    let error, action;\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n  constructor(scheduler, work, index = scheduler.index += 1) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.index = index;\n    this.active = true;\n    this.index = scheduler.index = index;\n  }\n  schedule(state, delay = 0) {\n    if (!this.id) {\n      return super.schedule(state, delay);\n    }\n    this.active = false;\n    const action = new VirtualAction(this.scheduler, this.work);\n    this.add(action);\n    return action.schedule(state, delay);\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    this.delay = scheduler.frame + delay;\n    const {\n      actions\n    } = scheduler;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return true;\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    return undefined;\n  }\n  _execute(state, delay) {\n    if (this.active === true) {\n      return super._execute(state, delay);\n    }\n  }\n  static sortActions(a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  }\n}", "map": {"version": 3, "names": ["AsyncAction", "AsyncScheduler", "VirtualTimeScheduler", "constructor", "SchedulerAction", "VirtualAction", "maxFrames", "Number", "POSITIVE_INFINITY", "frame", "index", "flush", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "scheduler", "work", "active", "schedule", "id", "add", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "b"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduler/VirtualTimeScheduler.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n    constructor(SchedulerAction = VirtualAction, maxFrames = Number.POSITIVE_INFINITY) {\n        super(SchedulerAction, () => this.frame);\n        this.maxFrames = maxFrames;\n        this.frame = 0;\n        this.index = -1;\n    }\n    flush() {\n        const { actions, maxFrames } = this;\n        let error, action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if (error = action.execute(action.state, action.delay)) {\n                break;\n            }\n        }\n        if (error) {\n            while (action = actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n    constructor(scheduler, work, index = scheduler.index += 1) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.index = index;\n        this.active = true;\n        this.index = scheduler.index = index;\n    }\n    schedule(state, delay = 0) {\n        if (!this.id) {\n            return super.schedule(state, delay);\n        }\n        this.active = false;\n        const action = new VirtualAction(this.scheduler, this.work);\n        this.add(action);\n        return action.schedule(state, delay);\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        this.delay = scheduler.frame + delay;\n        const { actions } = scheduler;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return true;\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        return undefined;\n    }\n    _execute(state, delay) {\n        if (this.active === true) {\n            return super._execute(state, delay);\n        }\n    }\n    static sortActions(a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,oBAAoB,SAASD,cAAc,CAAC;EACrDE,WAAWA,CAACC,eAAe,GAAGC,aAAa,EAAEC,SAAS,GAAGC,MAAM,CAACC,iBAAiB,EAAE;IAC/E,KAAK,CAACJ,eAAe,EAAE,MAAM,IAAI,CAACK,KAAK,CAAC;IACxC,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEC,OAAO;MAAEN;IAAU,CAAC,GAAG,IAAI;IACnC,IAAIO,KAAK,EAAEC,MAAM;IACjB,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAC,CAAC,KAAKE,MAAM,CAACC,KAAK,IAAIT,SAAS,EAAE;MACvDM,OAAO,CAACI,KAAK,CAAC,CAAC;MACf,IAAI,CAACP,KAAK,GAAGK,MAAM,CAACC,KAAK;MACzB,IAAIF,KAAK,GAAGC,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACC,KAAK,CAAC,EAAE;QACpD;MACJ;IACJ;IACA,IAAIF,KAAK,EAAE;MACP,OAAOC,MAAM,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE;QAC7BF,MAAM,CAACK,WAAW,CAAC,CAAC;MACxB;MACA,MAAMN,KAAK;IACf;EACJ;AACJ;AACAX,oBAAoB,CAACkB,eAAe,GAAG,EAAE;AACzC,OAAO,MAAMf,aAAa,SAASL,WAAW,CAAC;EAC3CG,WAAWA,CAACkB,SAAS,EAAEC,IAAI,EAAEZ,KAAK,GAAGW,SAAS,CAACX,KAAK,IAAI,CAAC,EAAE;IACvD,KAAK,CAACW,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACZ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACa,MAAM,GAAG,IAAI;IAClB,IAAI,CAACb,KAAK,GAAGW,SAAS,CAACX,KAAK,GAAGA,KAAK;EACxC;EACAc,QAAQA,CAACN,KAAK,EAAEH,KAAK,GAAG,CAAC,EAAE;IACvB,IAAI,CAAC,IAAI,CAACU,EAAE,EAAE;MACV,OAAO,KAAK,CAACD,QAAQ,CAACN,KAAK,EAAEH,KAAK,CAAC;IACvC;IACA,IAAI,CAACQ,MAAM,GAAG,KAAK;IACnB,MAAMT,MAAM,GAAG,IAAIT,aAAa,CAAC,IAAI,CAACgB,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC;IAC3D,IAAI,CAACI,GAAG,CAACZ,MAAM,CAAC;IAChB,OAAOA,MAAM,CAACU,QAAQ,CAACN,KAAK,EAAEH,KAAK,CAAC;EACxC;EACAY,cAAcA,CAACN,SAAS,EAAEI,EAAE,EAAEV,KAAK,GAAG,CAAC,EAAE;IACrC,IAAI,CAACA,KAAK,GAAGM,SAAS,CAACZ,KAAK,GAAGM,KAAK;IACpC,MAAM;MAAEH;IAAQ,CAAC,GAAGS,SAAS;IAC7BT,OAAO,CAACgB,IAAI,CAAC,IAAI,CAAC;IAClBhB,OAAO,CAACiB,IAAI,CAACxB,aAAa,CAACyB,WAAW,CAAC;IACvC,OAAO,IAAI;EACf;EACAC,cAAcA,CAACV,SAAS,EAAEI,EAAE,EAAEV,KAAK,GAAG,CAAC,EAAE;IACrC,OAAOiB,SAAS;EACpB;EACAC,QAAQA,CAACf,KAAK,EAAEH,KAAK,EAAE;IACnB,IAAI,IAAI,CAACQ,MAAM,KAAK,IAAI,EAAE;MACtB,OAAO,KAAK,CAACU,QAAQ,CAACf,KAAK,EAAEH,KAAK,CAAC;IACvC;EACJ;EACA,OAAOe,WAAWA,CAACI,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAID,CAAC,CAACnB,KAAK,KAAKoB,CAAC,CAACpB,KAAK,EAAE;MACrB,IAAImB,CAAC,CAACxB,KAAK,KAAKyB,CAAC,CAACzB,KAAK,EAAE;QACrB,OAAO,CAAC;MACZ,CAAC,MACI,IAAIwB,CAAC,CAACxB,KAAK,GAAGyB,CAAC,CAACzB,KAAK,EAAE;QACxB,OAAO,CAAC;MACZ,CAAC,MACI;QACD,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,MACI,IAAIwB,CAAC,CAACnB,KAAK,GAAGoB,CAAC,CAACpB,KAAK,EAAE;MACxB,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
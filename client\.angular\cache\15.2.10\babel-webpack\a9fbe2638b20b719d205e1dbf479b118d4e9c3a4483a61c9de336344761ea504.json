{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport { animate, style } from '@angular/animations';\nimport { NavigationEnd } from '@angular/router';\nimport { filter, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/animations\";\nexport class CoreLoadingScreenService {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param _document\r\n   * @param {Router} _router\r\n   * @param {AnimationBuilder} _animationBuilder\r\n   */\n  constructor(_document, _router, _animationBuilder) {\n    this._document = _document;\n    this._router = _router;\n    this._animationBuilder = _animationBuilder;\n    // Initialize\n    this._init();\n  }\n  // Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Initialize\r\n   *\r\n   * @private\r\n   */\n  _init() {\n    // Get the loading screen element\n    this.loadingScreenEl = this._document.body.querySelector('#loading-bg');\n    // If loading screen element\n    if (this.loadingScreenEl) {\n      // Hide it on the first NavigationEnd event\n      this._router.events.pipe(filter(event => event instanceof NavigationEnd), take(1)).subscribe(() => {\n        setTimeout(() => {\n          this.hide();\n        });\n      });\n    }\n  }\n  // Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Show the loading screen\r\n   */\n  show() {\n    this.animationPlayer = this._animationBuilder.build([style({\n      opacity: '0',\n      zIndex: '99999'\n    }), animate('250ms ease', style({\n      opacity: '1'\n    }))]).create(this.loadingScreenEl);\n    setTimeout(() => {\n      console.log('loading screen');\n      this.loadingScreenEl.hidden = false;\n      this.animationPlayer.play();\n    }, 0);\n  }\n  /**\r\n   * Hide the loading screen\r\n   */\n  hide() {\n    this.animationPlayer = this._animationBuilder.build([style({\n      opacity: '1'\n    }), animate('250ms ease', style({\n      opacity: '0',\n      zIndex: '-10'\n    }))]).create(this.loadingScreenEl);\n    setTimeout(() => {\n      this.loadingScreenEl.hidden = true;\n      this.animationPlayer.play();\n    }, 0);\n  }\n  static #_ = this.ɵfac = function CoreLoadingScreenService_Factory(t) {\n    return new (t || CoreLoadingScreenService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AnimationBuilder));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CoreLoadingScreenService,\n    factory: CoreLoadingScreenService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,QAAQ,QAAQ,iBAAiB;AAE1C,SACEC,OAAO,EAGPC,KAAK,QACA,qBAAqB;AAC5B,SAASC,aAAa,QAAgB,iBAAiB;AAEvD,SAASC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;;;;AAK7C,OAAM,MAAOC,wBAAwB;EAInC;;;;;;;EAOAC,YAC4BC,SAAc,EAChCC,OAAe,EACfC,iBAAmC;IAFjB,KAAAF,SAAS,GAATA,SAAS;IAC3B,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAEzB;IACA,IAAI,CAACC,KAAK,EAAE;EACd;EAEA;EACA;EAEA;;;;;EAKQA,KAAKA,CAAA;IACX;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACJ,SAAS,CAACK,IAAI,CAACC,aAAa,CAAC,aAAa,CAAgB;IAEtF;IACA,IAAI,IAAI,CAACF,eAAe,EAAE;MACxB;MACA,IAAI,CAACH,OAAO,CAACM,MAAM,CAChBC,IAAI,CACHZ,MAAM,CAAEa,KAAK,IAAKA,KAAK,YAAYd,aAAa,CAAC,EACjDE,IAAI,CAAC,CAAC,CAAC,CACR,CACAa,SAAS,CAAC,MAAK;QACdC,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,IAAI,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,CAAC;;EAER;EAEA;EACA;EAEA;;;EAGAC,IAAIA,CAAA;IACF,IAAI,CAACC,eAAe,GAAG,IAAI,CAACZ,iBAAiB,CAC1Ca,KAAK,CAAC,CACLrB,KAAK,CAAC;MACJsB,OAAO,EAAE,GAAG;MACZC,MAAM,EAAE;KACT,CAAC,EACFxB,OAAO,CAAC,YAAY,EAAEC,KAAK,CAAC;MAAEsB,OAAO,EAAE;IAAG,CAAE,CAAC,CAAC,CAC/C,CAAC,CACDE,MAAM,CAAC,IAAI,CAACd,eAAe,CAAC;IAE/BO,UAAU,CAAC,MAAK;MACdQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,IAAI,CAAChB,eAAe,CAACiB,MAAM,GAAG,KAAK;MACnC,IAAI,CAACP,eAAe,CAACQ,IAAI,EAAE;IAC7B,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;EAGAV,IAAIA,CAAA;IACF,IAAI,CAACE,eAAe,GAAG,IAAI,CAACZ,iBAAiB,CAC1Ca,KAAK,CAAC,CACLrB,KAAK,CAAC;MAAEsB,OAAO,EAAE;IAAG,CAAE,CAAC,EACvBvB,OAAO,CACL,YAAY,EACZC,KAAK,CAAC;MACJsB,OAAO,EAAE,GAAG;MACZC,MAAM,EAAE;KACT,CAAC,CACH,CACF,CAAC,CACDC,MAAM,CAAC,IAAI,CAACd,eAAe,CAAC;IAE/BO,UAAU,CAAC,MAAK;MACd,IAAI,CAACP,eAAe,CAACiB,MAAM,GAAG,IAAI;MAClC,IAAI,CAACP,eAAe,CAACQ,IAAI,EAAE;IAC7B,CAAC,EAAE,CAAC,CAAC;EACP;EAAC,QAAAC,CAAA;qBA7FUzB,wBAAwB,EAAA0B,EAAA,CAAAC,QAAA,CAYzBjC,QAAQ,GAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;WAZPhC,wBAAwB;IAAAiC,OAAA,EAAxBjC,wBAAwB,CAAAkC,IAAA;IAAAC,UAAA,EAFvB;EAAM", "names": ["DOCUMENT", "animate", "style", "NavigationEnd", "filter", "take", "CoreLoadingScreenService", "constructor", "_document", "_router", "_animationBuilder", "_init", "loadingScreenEl", "body", "querySelector", "events", "pipe", "event", "subscribe", "setTimeout", "hide", "show", "animationPlayer", "build", "opacity", "zIndex", "create", "console", "log", "hidden", "play", "_", "i0", "ɵɵinject", "i1", "Router", "i2", "AnimationBuilder", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\services\\loading-screen.service.ts"], "sourcesContent": ["import { Inject, Injectable } from '@angular/core';\r\nimport { DOCUMENT } from '@angular/common';\r\n\r\nimport {\r\n  animate,\r\n  AnimationBuilder,\r\n  AnimationPlayer,\r\n  style,\r\n} from '@angular/animations';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\n\r\nimport { filter, take } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CoreLoadingScreenService {\r\n  loadingScreenEl: HTMLElement;\r\n  animationPlayer: AnimationPlayer;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param _document\r\n   * @param {Router} _router\r\n   * @param {AnimationBuilder} _animationBuilder\r\n   */\r\n  constructor(\r\n    @Inject(DOCUMENT) private _document: any,\r\n    private _router: Router,\r\n    private _animationBuilder: AnimationBuilder\r\n  ) {\r\n    // Initialize\r\n    this._init();\r\n  }\r\n\r\n  // Private methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Initialize\r\n   *\r\n   * @private\r\n   */\r\n  private _init(): void {\r\n    // Get the loading screen element\r\n    this.loadingScreenEl = this._document.body.querySelector('#loading-bg') as HTMLElement;\r\n\r\n    // If loading screen element\r\n    if (this.loadingScreenEl) {\r\n      // Hide it on the first NavigationEnd event\r\n      this._router.events\r\n        .pipe(\r\n          filter((event) => event instanceof NavigationEnd),\r\n          take(1)\r\n        )\r\n        .subscribe(() => {\r\n          setTimeout(() => {\r\n            this.hide();\r\n          });\r\n        });\r\n    }\r\n  }\r\n\r\n  // Public methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Show the loading screen\r\n   */\r\n  show(): void {\r\n    this.animationPlayer = this._animationBuilder\r\n      .build([\r\n        style({\r\n          opacity: '0',\r\n          zIndex: '99999',\r\n        }),\r\n        animate('250ms ease', style({ opacity: '1' })),\r\n      ])\r\n      .create(this.loadingScreenEl);\r\n\r\n    setTimeout(() => {\r\n      console.log('loading screen');\r\n      this.loadingScreenEl.hidden = false;\r\n      this.animationPlayer.play();\r\n    }, 0);\r\n  }\r\n\r\n  /**\r\n   * Hide the loading screen\r\n   */\r\n  hide(): void {\r\n    this.animationPlayer = this._animationBuilder\r\n      .build([\r\n        style({ opacity: '1' }),\r\n        animate(\r\n          '250ms ease',\r\n          style({\r\n            opacity: '0',\r\n            zIndex: '-10',\r\n          })\r\n        ),\r\n      ])\r\n      .create(this.loadingScreenEl);\r\n\r\n    setTimeout(() => {\r\n      this.loadingScreenEl.hidden = true;\r\n      this.animationPlayer.play();\r\n    }, 0);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SponsorComponent } from './sponsor.component';\nimport { RouterModule } from '@angular/router';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { Ckeditor5TypeComponent } from 'app/components/ckeditor5-type/ckeditor5-type.component';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { ImageCropperTypeComponent } from 'app/components/image-cropper-type/image-cropper-type.component';\nimport { SponsorEditorComponent } from './sponsor-editor/sponsor-editor.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreSidebarModule } from '@core/components';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\nconst routes = [{\n  path: '',\n  component: SponsorComponent\n}];\nexport class SponsorModule {\n  static #_ = this.ɵfac = function SponsorModule_Factory(t) {\n    return new (t || SponsorModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SponsorModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), ContentHeaderModule, CKEditorModule, FormsModule, ReactiveFormsModule, CoreSidebarModule, DragDropModule, TranslateModule, BtnDropdownActionModule, FormlyModule.forRoot({\n      validationMessages: [{\n        name: 'required',\n        message: 'This field is required'\n      }, {\n        name: 'minLength',\n        message: 'Minimum length should be {0}'\n      }, {\n        name: 'maxLength',\n        message: 'Maximum length should be {0}'\n      }, {\n        name: 'pattern',\n        message: 'This field is invalid'\n      }, {\n        name: 'server',\n        message: serverValidationMessage\n      }],\n      types: [{\n        name: 'ckeditor5',\n        component: Ckeditor5TypeComponent\n      }, {\n        name: 'image-cropper',\n        component: ImageCropperTypeComponent,\n        wrappers: ['form-field']\n      }]\n    }), FormlyBootstrapModule, FormlyModule, FormlyBootstrapModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SponsorModule, {\n    declarations: [SponsorComponent, SponsorEditorComponent],\n    imports: [CommonModule, i1.RouterModule, ContentHeaderModule, CKEditorModule, FormsModule, ReactiveFormsModule, CoreSidebarModule, DragDropModule, TranslateModule, BtnDropdownActionModule, i2.FormlyModule, FormlyBootstrapModule],\n    exports: [FormlyModule, FormlyBootstrapModule]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAAoBC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAE5E,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,uBAAuB,QAAQ,qDAAqD;AAE7F,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,yBAAyB,QAAQ,gEAAgE;AAC1G,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,cAAc,QAAQ,wBAAwB;;;;AAEvD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAElB;CACZ,CACF;AA0CD,OAAM,MAAOmB,aAAa;EAAA,QAAAC,CAAA;qBAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA;UAAbF;EAAa;EAAA,QAAAG,EAAA;cAlCtBvB,YAAY,EACZE,YAAY,CAACsB,QAAQ,CAACP,MAAM,CAAC,EAC7Bd,mBAAmB,EACnBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBQ,iBAAiB,EACjBE,cAAc,EACdH,eAAe,EACfE,uBAAuB,EACvBR,YAAY,CAACkB,OAAO,CAAC;MACnBC,kBAAkB,EAAE,CAClB;QAAEC,IAAI,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAwB,CAAE,EACvD;QAAED,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAA8B,CAAE,EAC9D;QAAED,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAA8B,CAAE,EAC9D;QAAED,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAuB,CAAE,EACrD;QAAED,IAAI,EAAE,QAAQ;QAAEC,OAAO,EAAEpB;MAAuB,CAAE,CACrD;MACDqB,KAAK,EAAE,CACL;QAAEF,IAAI,EAAE,WAAW;QAAER,SAAS,EAAEV;MAAsB,CAAE,EACxD;QACEkB,IAAI,EAAE,eAAe;QACrBR,SAAS,EAAER,yBAAyB;QACpCmB,QAAQ,EAAE,CAAC,YAAY;OACxB;KAEJ,CAAC,EACFpB,qBAAqB,EAGrBH,YAAY,EACZG,qBAAqB;EAAA;;;2EAGZU,aAAa;IAAAW,YAAA,GAtCtB9B,gBAAgB,EAChBW,sBAAsB;IAAAoB,OAAA,GAGtBhC,YAAY,EAAAiC,EAAA,CAAA/B,YAAA,EAEZC,mBAAmB,EACnBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBQ,iBAAiB,EACjBE,cAAc,EACdH,eAAe,EACfE,uBAAuB,EAAAmB,EAAA,CAAA3B,YAAA,EAkBvBG,qBAAqB;IAAAyB,OAAA,GAGrB5B,YAAY,EACZG,qBAAqB;EAAA;AAAA", "names": ["CommonModule", "SponsorComponent", "RouterModule", "ContentHeaderModule", "CKEditorModule", "FormsModule", "ReactiveFormsModule", "FormlyModule", "serverValidationMessage", "Ckeditor5TypeComponent", "FormlyBootstrapModule", "ImageCropperTypeComponent", "SponsorEditorComponent", "TranslateModule", "CoreSidebarModule", "BtnDropdownActionModule", "DragDropModule", "routes", "path", "component", "SponsorModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "validationMessages", "name", "message", "types", "wrappers", "declarations", "imports", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\sponsor\\sponsor.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SponsorComponent } from './sponsor.component';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { CKEditorModule } from '@ckeditor/ckeditor5-angular';\r\nimport { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { FormlyModule } from '@ngx-formly/core';\r\nimport { serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { FormlyHorizontalWrapper } from 'app/components/editor-sidebar/horizontal-wrapper';\r\nimport { Ckeditor5TypeComponent } from 'app/components/ckeditor5-type/ckeditor5-type.component';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { ImageCropperTypeComponent } from 'app/components/image-cropper-type/image-cropper-type.component';\r\nimport { SponsorEditorComponent } from './sponsor-editor/sponsor-editor.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SponsorComponent\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SponsorComponent,\r\n    SponsorEditorComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    CKEditorModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CoreSidebarModule,\r\n    DragDropModule,\r\n    TranslateModule,\r\n    BtnDropdownActionModule,\r\n    FormlyModule.forRoot({\r\n      validationMessages: [\r\n        { name: 'required', message: 'This field is required' },\r\n        { name: 'minLength', message: 'Minimum length should be {0}' },\r\n        { name: 'maxLength', message: 'Maximum length should be {0}' },\r\n        { name: 'pattern', message: 'This field is invalid' },\r\n        { name: 'server', message: serverValidationMessage },\r\n      ],\r\n      types: [\r\n        { name: 'ckeditor5', component: Ckeditor5TypeComponent },\r\n        {\r\n          name: 'image-cropper',\r\n          component: ImageCropperTypeComponent,\r\n          wrappers: ['form-field'],\r\n        },\r\n      ],\r\n    }),\r\n    FormlyBootstrapModule\r\n  ],\r\n  exports: [\r\n    FormlyModule,\r\n    FormlyBootstrapModule,\r\n  ]\r\n})\r\nexport class SponsorModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const Utils = require('./utils');\nconst G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;\nconst G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;\nconst G15_BCH = Utils.getBCHDigit(G15);\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits(errorCorrectionLevel, mask) {\n  const data = errorCorrectionLevel.bit << 3 | mask;\n  let d = data << 10;\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= G15 << Utils.getBCHDigit(d) - G15_BCH;\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return (data << 10 | d) ^ G15_MASK;\n};", "map": {"version": 3, "names": ["Utils", "require", "G15", "G15_MASK", "G15_BCH", "getBCHDigit", "exports", "getEncodedBits", "errorCorrectionLevel", "mask", "data", "bit", "d"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/core/format-info.js"], "sourcesContent": ["const Utils = require('./utils')\n\nconst G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0)\nconst G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1)\nconst G15_BCH = Utils.getBCHDigit(G15)\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits (errorCorrectionLevel, mask) {\n  const data = ((errorCorrectionLevel.bit << 3) | mask)\n  let d = data << 10\n\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= (G15 << (Utils.getBCHDigit(d) - G15_BCH))\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return ((data << 10) | d) ^ G15_MASK\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAEhC,MAAMC,GAAG,GAAI,CAAC,IAAI,EAAE,GAAK,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE;AACvF,MAAMC,QAAQ,GAAI,CAAC,IAAI,EAAE,GAAK,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE;AACxE,MAAMC,OAAO,GAAGJ,KAAK,CAACK,WAAW,CAACH,GAAG,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAI,OAAO,CAACC,cAAc,GAAG,SAASA,cAAcA,CAAEC,oBAAoB,EAAEC,IAAI,EAAE;EAC5E,MAAMC,IAAI,GAAKF,oBAAoB,CAACG,GAAG,IAAI,CAAC,GAAIF,IAAK;EACrD,IAAIG,CAAC,GAAGF,IAAI,IAAI,EAAE;EAElB,OAAOV,KAAK,CAACK,WAAW,CAACO,CAAC,CAAC,GAAGR,OAAO,IAAI,CAAC,EAAE;IAC1CQ,CAAC,IAAKV,GAAG,IAAKF,KAAK,CAACK,WAAW,CAACO,CAAC,CAAC,GAAGR,OAAS;EAChD;;EAEA;EACA;EACA;EACA,OAAO,CAAEM,IAAI,IAAI,EAAE,GAAIE,CAAC,IAAIT,QAAQ;AACtC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
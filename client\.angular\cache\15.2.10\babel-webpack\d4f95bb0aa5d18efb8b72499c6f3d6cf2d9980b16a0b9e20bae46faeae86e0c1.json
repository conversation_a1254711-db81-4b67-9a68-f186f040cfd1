{"ast": null, "code": "//! moment.js locale configuration\n//! locale : German (Switzerland) [de-ch]\n//! author : sschueller : https://github.com/sschueller\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      m: ['eine Minute', 'einer Minute'],\n      h: ['eine Stunde', 'einer Stunde'],\n      d: ['ein Tag', 'einem Tag'],\n      dd: [number + ' Tage', number + ' Tagen'],\n      w: ['eine Woche', 'einer Woche'],\n      M: ['ein <PERSON>t', 'einem Monat'],\n      MM: [number + ' <PERSON><PERSON>', number + ' <PERSON><PERSON>'],\n      y: ['ein Jahr', 'einem Jahr'],\n      yy: [number + ' Jahre', number + ' Jahren']\n    };\n    return withoutSuffix ? format[key][0] : format[key][1];\n  }\n  var deCh = moment.defineLocale('de-ch', {\n    months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split('_'),\n    monthsShort: 'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split('_'),\n    weekdaysShort: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n    weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY HH:mm',\n      LLLL: 'dddd, D. MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[heute um] LT [Uhr]',\n      sameElse: 'L',\n      nextDay: '[morgen um] LT [Uhr]',\n      nextWeek: 'dddd [um] LT [Uhr]',\n      lastDay: '[gestern um] LT [Uhr]',\n      lastWeek: '[letzten] dddd [um] LT [Uhr]'\n    },\n    relativeTime: {\n      future: 'in %s',\n      past: 'vor %s',\n      s: 'ein paar Sekunden',\n      ss: '%d Sekunden',\n      m: processRelativeTime,\n      mm: '%d Minuten',\n      h: processRelativeTime,\n      hh: '%d Stunden',\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      w: processRelativeTime,\n      ww: '%d Wochen',\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n\n  return deCh;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "processRelativeTime", "number", "withoutSuffix", "key", "isFuture", "format", "m", "h", "d", "dd", "w", "M", "MM", "y", "yy", "deCh", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "same<PERSON><PERSON><PERSON>", "nextDay", "nextWeek", "lastDay", "lastWeek", "relativeTime", "future", "past", "s", "ss", "mm", "hh", "ww", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/moment/locale/de-ch.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : German (Switzerland) [de-ch]\n//! author : sschueller : https://github.com/sschueller\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            m: ['eine Minute', 'einer Minute'],\n            h: ['eine Stunde', 'einer Stunde'],\n            d: ['ein Tag', 'einem Tag'],\n            dd: [number + ' Tage', number + ' Tagen'],\n            w: ['eine Woche', 'einer Woche'],\n            M: ['ein <PERSON>t', 'einem Monat'],\n            MM: [number + ' <PERSON><PERSON>', number + ' <PERSON><PERSON>'],\n            y: ['ein Jahr', 'einem Jahr'],\n            yy: [number + ' Jahre', number + ' Jahren'],\n        };\n        return withoutSuffix ? format[key][0] : format[key][1];\n    }\n\n    var deCh = moment.defineLocale('de-ch', {\n        months: 'Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember'.split(\n            '_'\n        ),\n        monthsShort:\n            'Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.'.split('_'),\n        monthsParseExact: true,\n        weekdays:\n            'Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag'.split(\n                '_'\n            ),\n        weekdaysShort: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysMin: 'So_Mo_Di_Mi_Do_Fr_Sa'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd, D. MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[heute um] LT [Uhr]',\n            sameElse: 'L',\n            nextDay: '[morgen um] LT [Uhr]',\n            nextWeek: 'dddd [um] LT [Uhr]',\n            lastDay: '[gestern um] LT [Uhr]',\n            lastWeek: '[letzten] dddd [um] LT [Uhr]',\n        },\n        relativeTime: {\n            future: 'in %s',\n            past: 'vor %s',\n            s: 'ein paar Sekunden',\n            ss: '%d Sekunden',\n            m: processRelativeTime,\n            mm: '%d Minuten',\n            h: processRelativeTime,\n            hh: '%d Stunden',\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            w: processRelativeTime,\n            ww: '%d Wochen',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return deCh;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,MAAM,GAAG;MACTC,CAAC,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC;MAClCC,CAAC,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC;MAClCC,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;MAC3BC,EAAE,EAAE,CAACR,MAAM,GAAG,OAAO,EAAEA,MAAM,GAAG,QAAQ,CAAC;MACzCS,CAAC,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;MAChCC,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;MAC/BC,EAAE,EAAE,CAACX,MAAM,GAAG,SAAS,EAAEA,MAAM,GAAG,UAAU,CAAC;MAC7CY,CAAC,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;MAC7BC,EAAE,EAAE,CAACb,MAAM,GAAG,QAAQ,EAAEA,MAAM,GAAG,SAAS;IAC9C,CAAC;IACD,OAAOC,aAAa,GAAGG,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1D;EAEA,IAAIY,IAAI,GAAGhB,MAAM,CAACiB,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;IACDC,WAAW,EACP,4DAA4D,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,6DAA6D,CAACH,KAAK,CAC/D,GACJ,CAAC;IACLI,aAAa,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAChDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,sBAAsB;MAC/BC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE,uBAAuB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,aAAa;MACjBrC,CAAC,EAAEN,mBAAmB;MACtB4C,EAAE,EAAE,YAAY;MAChBrC,CAAC,EAAEP,mBAAmB;MACtB6C,EAAE,EAAE,YAAY;MAChBrC,CAAC,EAAER,mBAAmB;MACtBS,EAAE,EAAET,mBAAmB;MACvBU,CAAC,EAAEV,mBAAmB;MACtB8C,EAAE,EAAE,WAAW;MACfnC,CAAC,EAAEX,mBAAmB;MACtBY,EAAE,EAAEZ,mBAAmB;MACvBa,CAAC,EAAEb,mBAAmB;MACtBc,EAAE,EAAEd;IACR,CAAC;IACD+C,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;;EAEF,OAAOpC,IAAI;AAEf,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
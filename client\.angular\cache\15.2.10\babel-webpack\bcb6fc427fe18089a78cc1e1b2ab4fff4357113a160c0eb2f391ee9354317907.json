{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, NgZone, ApplicationRef, PLATFORM_ID, APP_INITIALIZER, Injector, NgModule } from '@angular/core';\nimport { defer, throwError, fromEvent, of, concat, Subject, NEVER, merge } from 'rxjs';\nimport { map, filter, switchMap, publish, take, tap, delay } from 'rxjs/operators';\nconst ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\nfunction errorObservable(message) {\n  return defer(() => throwError(new Error(message)));\n}\n/**\n * @publicApi\n */\nclass NgswCommChannel {\n  constructor(serviceWorker) {\n    this.serviceWorker = serviceWorker;\n    if (!serviceWorker) {\n      this.worker = this.events = this.registration = errorObservable(ERR_SW_NOT_SUPPORTED);\n    } else {\n      const controllerChangeEvents = fromEvent(serviceWorker, 'controllerchange');\n      const controllerChanges = controllerChangeEvents.pipe(map(() => serviceWorker.controller));\n      const currentController = defer(() => of(serviceWorker.controller));\n      const controllerWithChanges = concat(currentController, controllerChanges);\n      this.worker = controllerWithChanges.pipe(filter(c => !!c));\n      this.registration = this.worker.pipe(switchMap(() => serviceWorker.getRegistration()));\n      const rawEvents = fromEvent(serviceWorker, 'message');\n      const rawEventPayload = rawEvents.pipe(map(event => event.data));\n      const eventsUnconnected = rawEventPayload.pipe(filter(event => event && event.type));\n      const events = eventsUnconnected.pipe(publish());\n      events.connect();\n      this.events = events;\n    }\n  }\n  postMessage(action, payload) {\n    return this.worker.pipe(take(1), tap(sw => {\n      sw.postMessage({\n        action,\n        ...payload\n      });\n    })).toPromise().then(() => undefined);\n  }\n  postMessageWithOperation(type, payload, operationNonce) {\n    const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n    const postMessage = this.postMessage(type, payload);\n    return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n  }\n  generateNonce() {\n    return Math.round(Math.random() * 10000000);\n  }\n  eventsOfType(type) {\n    let filterFn;\n    if (typeof type === 'string') {\n      filterFn = event => event.type === type;\n    } else {\n      filterFn = event => type.includes(event.type);\n    }\n    return this.events.pipe(filter(filterFn));\n  }\n  nextEventOfType(type) {\n    return this.eventsOfType(type).pipe(take(1));\n  }\n  waitForOperationCompleted(nonce) {\n    return this.eventsOfType('OPERATION_COMPLETED').pipe(filter(event => event.nonce === nonce), take(1), map(event => {\n      if (event.result !== undefined) {\n        return event.result;\n      }\n      throw new Error(event.error);\n    })).toPromise();\n  }\n  get isEnabled() {\n    return !!this.serviceWorker;\n  }\n}\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](guide/service-worker-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\nclass SwPush {\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled() {\n    return this.sw.isEnabled;\n  }\n  constructor(sw) {\n    this.sw = sw;\n    this.pushManager = null;\n    this.subscriptionChanges = new Subject();\n    if (!sw.isEnabled) {\n      this.messages = NEVER;\n      this.notificationClicks = NEVER;\n      this.subscription = NEVER;\n      return;\n    }\n    this.messages = this.sw.eventsOfType('PUSH').pipe(map(message => message.data));\n    this.notificationClicks = this.sw.eventsOfType('NOTIFICATION_CLICK').pipe(map(message => message.data));\n    this.pushManager = this.sw.registration.pipe(map(registration => registration.pushManager));\n    const workerDrivenSubscriptions = this.pushManager.pipe(switchMap(pm => pm.getSubscription()));\n    this.subscription = merge(workerDrivenSubscriptions, this.subscriptionChanges);\n  }\n  /**\n   * Subscribes to Web Push Notifications,\n   * after requesting and receiving user permission.\n   *\n   * @param options An object containing the `serverPublicKey` string.\n   * @returns A Promise that resolves to the new subscription object.\n   */\n  requestSubscription(options) {\n    if (!this.sw.isEnabled || this.pushManager === null) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const pushOptions = {\n      userVisibleOnly: true\n    };\n    let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n    let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n    for (let i = 0; i < key.length; i++) {\n      applicationServerKey[i] = key.charCodeAt(i);\n    }\n    pushOptions.applicationServerKey = applicationServerKey;\n    return this.pushManager.pipe(switchMap(pm => pm.subscribe(pushOptions)), take(1)).toPromise().then(sub => {\n      this.subscriptionChanges.next(sub);\n      return sub;\n    });\n  }\n  /**\n   * Unsubscribes from Service Worker push notifications.\n   *\n   * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n   *          active subscription or the unsubscribe operation fails.\n   */\n  unsubscribe() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const doUnsubscribe = sub => {\n      if (sub === null) {\n        throw new Error('Not subscribed to push notifications.');\n      }\n      return sub.unsubscribe().then(success => {\n        if (!success) {\n          throw new Error('Unsubscribe failed!');\n        }\n        this.subscriptionChanges.next(null);\n      });\n    };\n    return this.subscription.pipe(take(1), switchMap(doUnsubscribe)).toPromise();\n  }\n  decodeBase64(input) {\n    return atob(input);\n  }\n}\nSwPush.ɵfac = function SwPush_Factory(t) {\n  return new (t || SwPush)(i0.ɵɵinject(NgswCommChannel));\n};\nSwPush.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SwPush,\n  factory: SwPush.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SwPush, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: NgswCommChannel\n    }];\n  }, null);\n})();\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nclass SwUpdate {\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled() {\n    return this.sw.isEnabled;\n  }\n  constructor(sw) {\n    this.sw = sw;\n    if (!sw.isEnabled) {\n      this.versionUpdates = NEVER;\n      this.available = NEVER;\n      this.activated = NEVER;\n      this.unrecoverable = NEVER;\n      return;\n    }\n    this.versionUpdates = this.sw.eventsOfType(['VERSION_DETECTED', 'VERSION_INSTALLATION_FAILED', 'VERSION_READY', 'NO_NEW_VERSION_DETECTED']);\n    this.available = this.versionUpdates.pipe(filter(evt => evt.type === 'VERSION_READY'), map(evt => ({\n      type: 'UPDATE_AVAILABLE',\n      current: evt.currentVersion,\n      available: evt.latestVersion\n    })));\n    this.activated = this.sw.eventsOfType('UPDATE_ACTIVATED');\n    this.unrecoverable = this.sw.eventsOfType('UNRECOVERABLE_STATE');\n  }\n  /**\n   * Checks for an update and waits until the new version is downloaded from the server and ready\n   * for activation.\n   *\n   * @returns a promise that\n   * - resolves to `true` if a new version was found and is ready to be activated.\n   * - resolves to `false` if no new version was found\n   * - rejects if any error occurs\n   */\n  checkForUpdate() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', {\n      nonce\n    }, nonce);\n  }\n  /**\n   * Updates the current client (i.e. browser tab) to the latest version that is ready for\n   * activation.\n   *\n   * In most cases, you should not use this method and instead should update a client by reloading\n   * the page.\n   *\n   * <div class=\"alert is-important\">\n   *\n   * Updating a client without reloading can easily result in a broken application due to a version\n   * mismatch between the [application shell](guide/glossary#app-shell) and other page resources,\n   * such as [lazy-loaded chunks](guide/glossary#lazy-loading), whose filenames may change between\n   * versions.\n   *\n   * Only use this method, if you are certain it is safe for your specific use case.\n   *\n   * </div>\n   *\n   * @returns a promise that\n   *  - resolves to `true` if an update was activated successfully\n   *  - resolves to `false` if no update was available (for example, the client was already on the\n   *    latest version).\n   *  - rejects if any error occurs\n   */\n  activateUpdate() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', {\n      nonce\n    }, nonce);\n  }\n}\nSwUpdate.ɵfac = function SwUpdate_Factory(t) {\n  return new (t || SwUpdate)(i0.ɵɵinject(NgswCommChannel));\n};\nSwUpdate.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SwUpdate,\n  factory: SwUpdate.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SwUpdate, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: NgswCommChannel\n    }];\n  }, null);\n})();\n\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nclass SwRegistrationOptions {}\nconst SCRIPT = new InjectionToken('NGSW_REGISTER_SCRIPT');\nfunction ngswAppInitializer(injector, script, options, platformId) {\n  return () => {\n    if (!(isPlatformBrowser(platformId) && 'serviceWorker' in navigator && options.enabled !== false)) {\n      return;\n    }\n    // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n    // becomes active. This allows the SW to initialize itself even if there is no application\n    // traffic.\n    navigator.serviceWorker.addEventListener('controllerchange', () => {\n      if (navigator.serviceWorker.controller !== null) {\n        navigator.serviceWorker.controller.postMessage({\n          action: 'INITIALIZE'\n        });\n      }\n    });\n    let readyToRegister$;\n    if (typeof options.registrationStrategy === 'function') {\n      readyToRegister$ = options.registrationStrategy();\n    } else {\n      const [strategy, ...args] = (options.registrationStrategy || 'registerWhenStable:30000').split(':');\n      switch (strategy) {\n        case 'registerImmediately':\n          readyToRegister$ = of(null);\n          break;\n        case 'registerWithDelay':\n          readyToRegister$ = delayWithTimeout(+args[0] || 0);\n          break;\n        case 'registerWhenStable':\n          readyToRegister$ = !args[0] ? whenStable(injector) : merge(whenStable(injector), delayWithTimeout(+args[0]));\n          break;\n        default:\n          // Unknown strategy.\n          throw new Error(`Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`);\n      }\n    }\n    // Don't return anything to avoid blocking the application until the SW is registered.\n    // Also, run outside the Angular zone to avoid preventing the app from stabilizing (especially\n    // given that some registration strategies wait for the app to stabilize).\n    // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n    const ngZone = injector.get(NgZone);\n    ngZone.runOutsideAngular(() => readyToRegister$.pipe(take(1)).subscribe(() => navigator.serviceWorker.register(script, {\n      scope: options.scope\n    }).catch(err => console.error('Service worker registration failed with:', err))));\n  };\n}\nfunction delayWithTimeout(timeout) {\n  return of(null).pipe(delay(timeout));\n}\nfunction whenStable(injector) {\n  const appRef = injector.get(ApplicationRef);\n  return appRef.isStable.pipe(filter(stable => stable));\n}\nfunction ngswCommChannelFactory(opts, platformId) {\n  return new NgswCommChannel(isPlatformBrowser(platformId) && opts.enabled !== false ? navigator.serviceWorker : undefined);\n}\n/**\n * @publicApi\n */\nclass ServiceWorkerModule {\n  /**\n   * Register the given Angular Service Worker script.\n   *\n   * If `enabled` is set to `false` in the given options, the module will behave as if service\n   * workers are not supported by the browser, and the service worker will not be registered.\n   */\n  static register(script, opts = {}) {\n    return {\n      ngModule: ServiceWorkerModule,\n      providers: [{\n        provide: SCRIPT,\n        useValue: script\n      }, {\n        provide: SwRegistrationOptions,\n        useValue: opts\n      }, {\n        provide: NgswCommChannel,\n        useFactory: ngswCommChannelFactory,\n        deps: [SwRegistrationOptions, PLATFORM_ID]\n      }, {\n        provide: APP_INITIALIZER,\n        useFactory: ngswAppInitializer,\n        deps: [Injector, SCRIPT, SwRegistrationOptions, PLATFORM_ID],\n        multi: true\n      }]\n    };\n  }\n}\nServiceWorkerModule.ɵfac = function ServiceWorkerModule_Factory(t) {\n  return new (t || ServiceWorkerModule)();\n};\nServiceWorkerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ServiceWorkerModule\n});\nServiceWorkerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [SwPush, SwUpdate]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ServiceWorkerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [SwPush, SwUpdate]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ServiceWorkerModule, SwPush, SwRegistrationOptions, SwUpdate };", "map": {"version": 3, "names": ["isPlatformBrowser", "i0", "Injectable", "InjectionToken", "NgZone", "ApplicationRef", "PLATFORM_ID", "APP_INITIALIZER", "Injector", "NgModule", "defer", "throwError", "fromEvent", "of", "concat", "Subject", "NEVER", "merge", "map", "filter", "switchMap", "publish", "take", "tap", "delay", "ERR_SW_NOT_SUPPORTED", "errorObservable", "message", "Error", "NgswCommChannel", "constructor", "serviceWorker", "worker", "events", "registration", "controllerChangeEvents", "controllerChanges", "pipe", "controller", "currentController", "controllerWithChanges", "c", "getRegistration", "rawEvents", "rawEventPayload", "event", "data", "eventsUnconnected", "type", "connect", "postMessage", "action", "payload", "sw", "to<PERSON>romise", "then", "undefined", "postMessageWithOperation", "operationNonce", "waitForOperationCompleted", "Promise", "all", "result", "generateNonce", "Math", "round", "random", "eventsOfType", "filterFn", "includes", "nextEventOfType", "nonce", "error", "isEnabled", "SwPush", "pushManager", "subscriptionChanges", "messages", "notificationClicks", "subscription", "workerDrivenSubscriptions", "pm", "getSubscription", "requestSubscription", "options", "reject", "pushOptions", "userVisibleOnly", "key", "decodeBase64", "serverPublicKey", "replace", "applicationServerKey", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "charCodeAt", "subscribe", "sub", "next", "unsubscribe", "doUnsubscribe", "success", "input", "atob", "ɵfac", "SwPush_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "SwUpdate", "versionUpdates", "available", "activated", "unrecoverable", "evt", "current", "currentVersion", "latestVersion", "checkForUpdate", "activateUpdate", "SwUpdate_Factory", "SwRegistrationOptions", "SCRIPT", "ngswAppInitializer", "injector", "script", "platformId", "navigator", "enabled", "addEventListener", "readyToRegister$", "registrationStrategy", "strategy", "args", "split", "delayWithTimeout", "whenStable", "ngZone", "get", "runOutsideAngular", "register", "scope", "catch", "err", "console", "timeout", "appRef", "isStable", "stable", "ngswCommChannelFactory", "opts", "ServiceWorkerModule", "ngModule", "providers", "provide", "useValue", "useFactory", "deps", "multi", "ServiceWorkerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/service-worker/fesm2020/service-worker.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, NgZone, ApplicationRef, PLATFORM_ID, APP_INITIALIZER, Injector, NgModule } from '@angular/core';\nimport { defer, throwError, fromEvent, of, concat, Subject, NEVER, merge } from 'rxjs';\nimport { map, filter, switchMap, publish, take, tap, delay } from 'rxjs/operators';\n\nconst ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\nfunction errorObservable(message) {\n    return defer(() => throwError(new Error(message)));\n}\n/**\n * @publicApi\n */\nclass NgswCommChannel {\n    constructor(serviceWorker) {\n        this.serviceWorker = serviceWorker;\n        if (!serviceWorker) {\n            this.worker = this.events = this.registration = errorObservable(ERR_SW_NOT_SUPPORTED);\n        }\n        else {\n            const controllerChangeEvents = fromEvent(serviceWorker, 'controllerchange');\n            const controllerChanges = controllerChangeEvents.pipe(map(() => serviceWorker.controller));\n            const currentController = defer(() => of(serviceWorker.controller));\n            const controllerWithChanges = concat(currentController, controllerChanges);\n            this.worker = controllerWithChanges.pipe(filter((c) => !!c));\n            this.registration = (this.worker.pipe(switchMap(() => serviceWorker.getRegistration())));\n            const rawEvents = fromEvent(serviceWorker, 'message');\n            const rawEventPayload = rawEvents.pipe(map(event => event.data));\n            const eventsUnconnected = rawEventPayload.pipe(filter(event => event && event.type));\n            const events = eventsUnconnected.pipe(publish());\n            events.connect();\n            this.events = events;\n        }\n    }\n    postMessage(action, payload) {\n        return this.worker\n            .pipe(take(1), tap((sw) => {\n            sw.postMessage({\n                action,\n                ...payload,\n            });\n        }))\n            .toPromise()\n            .then(() => undefined);\n    }\n    postMessageWithOperation(type, payload, operationNonce) {\n        const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n        const postMessage = this.postMessage(type, payload);\n        return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n    }\n    generateNonce() {\n        return Math.round(Math.random() * 10000000);\n    }\n    eventsOfType(type) {\n        let filterFn;\n        if (typeof type === 'string') {\n            filterFn = (event) => event.type === type;\n        }\n        else {\n            filterFn = (event) => type.includes(event.type);\n        }\n        return this.events.pipe(filter(filterFn));\n    }\n    nextEventOfType(type) {\n        return this.eventsOfType(type).pipe(take(1));\n    }\n    waitForOperationCompleted(nonce) {\n        return this.eventsOfType('OPERATION_COMPLETED')\n            .pipe(filter(event => event.nonce === nonce), take(1), map(event => {\n            if (event.result !== undefined) {\n                return event.result;\n            }\n            throw new Error(event.error);\n        }))\n            .toPromise();\n    }\n    get isEnabled() {\n        return !!this.serviceWorker;\n    }\n}\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](guide/service-worker-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\nclass SwPush {\n    /**\n     * True if the Service Worker is enabled (supported by the browser and enabled via\n     * `ServiceWorkerModule`).\n     */\n    get isEnabled() {\n        return this.sw.isEnabled;\n    }\n    constructor(sw) {\n        this.sw = sw;\n        this.pushManager = null;\n        this.subscriptionChanges = new Subject();\n        if (!sw.isEnabled) {\n            this.messages = NEVER;\n            this.notificationClicks = NEVER;\n            this.subscription = NEVER;\n            return;\n        }\n        this.messages = this.sw.eventsOfType('PUSH').pipe(map(message => message.data));\n        this.notificationClicks =\n            this.sw.eventsOfType('NOTIFICATION_CLICK').pipe(map((message) => message.data));\n        this.pushManager = this.sw.registration.pipe(map(registration => registration.pushManager));\n        const workerDrivenSubscriptions = this.pushManager.pipe(switchMap(pm => pm.getSubscription()));\n        this.subscription = merge(workerDrivenSubscriptions, this.subscriptionChanges);\n    }\n    /**\n     * Subscribes to Web Push Notifications,\n     * after requesting and receiving user permission.\n     *\n     * @param options An object containing the `serverPublicKey` string.\n     * @returns A Promise that resolves to the new subscription object.\n     */\n    requestSubscription(options) {\n        if (!this.sw.isEnabled || this.pushManager === null) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const pushOptions = { userVisibleOnly: true };\n        let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n        let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n        for (let i = 0; i < key.length; i++) {\n            applicationServerKey[i] = key.charCodeAt(i);\n        }\n        pushOptions.applicationServerKey = applicationServerKey;\n        return this.pushManager.pipe(switchMap(pm => pm.subscribe(pushOptions)), take(1))\n            .toPromise()\n            .then(sub => {\n            this.subscriptionChanges.next(sub);\n            return sub;\n        });\n    }\n    /**\n     * Unsubscribes from Service Worker push notifications.\n     *\n     * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n     *          active subscription or the unsubscribe operation fails.\n     */\n    unsubscribe() {\n        if (!this.sw.isEnabled) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const doUnsubscribe = (sub) => {\n            if (sub === null) {\n                throw new Error('Not subscribed to push notifications.');\n            }\n            return sub.unsubscribe().then(success => {\n                if (!success) {\n                    throw new Error('Unsubscribe failed!');\n                }\n                this.subscriptionChanges.next(null);\n            });\n        };\n        return this.subscription.pipe(take(1), switchMap(doUnsubscribe)).toPromise();\n    }\n    decodeBase64(input) {\n        return atob(input);\n    }\n}\nSwPush.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SwPush, deps: [{ token: NgswCommChannel }], target: i0.ɵɵFactoryTarget.Injectable });\nSwPush.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SwPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SwPush, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: NgswCommChannel }]; } });\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nclass SwUpdate {\n    /**\n     * True if the Service Worker is enabled (supported by the browser and enabled via\n     * `ServiceWorkerModule`).\n     */\n    get isEnabled() {\n        return this.sw.isEnabled;\n    }\n    constructor(sw) {\n        this.sw = sw;\n        if (!sw.isEnabled) {\n            this.versionUpdates = NEVER;\n            this.available = NEVER;\n            this.activated = NEVER;\n            this.unrecoverable = NEVER;\n            return;\n        }\n        this.versionUpdates = this.sw.eventsOfType([\n            'VERSION_DETECTED',\n            'VERSION_INSTALLATION_FAILED',\n            'VERSION_READY',\n            'NO_NEW_VERSION_DETECTED',\n        ]);\n        this.available = this.versionUpdates.pipe(filter((evt) => evt.type === 'VERSION_READY'), map(evt => ({\n            type: 'UPDATE_AVAILABLE',\n            current: evt.currentVersion,\n            available: evt.latestVersion,\n        })));\n        this.activated = this.sw.eventsOfType('UPDATE_ACTIVATED');\n        this.unrecoverable = this.sw.eventsOfType('UNRECOVERABLE_STATE');\n    }\n    /**\n     * Checks for an update and waits until the new version is downloaded from the server and ready\n     * for activation.\n     *\n     * @returns a promise that\n     * - resolves to `true` if a new version was found and is ready to be activated.\n     * - resolves to `false` if no new version was found\n     * - rejects if any error occurs\n     */\n    checkForUpdate() {\n        if (!this.sw.isEnabled) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const nonce = this.sw.generateNonce();\n        return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', { nonce }, nonce);\n    }\n    /**\n     * Updates the current client (i.e. browser tab) to the latest version that is ready for\n     * activation.\n     *\n     * In most cases, you should not use this method and instead should update a client by reloading\n     * the page.\n     *\n     * <div class=\"alert is-important\">\n     *\n     * Updating a client without reloading can easily result in a broken application due to a version\n     * mismatch between the [application shell](guide/glossary#app-shell) and other page resources,\n     * such as [lazy-loaded chunks](guide/glossary#lazy-loading), whose filenames may change between\n     * versions.\n     *\n     * Only use this method, if you are certain it is safe for your specific use case.\n     *\n     * </div>\n     *\n     * @returns a promise that\n     *  - resolves to `true` if an update was activated successfully\n     *  - resolves to `false` if no update was available (for example, the client was already on the\n     *    latest version).\n     *  - rejects if any error occurs\n     */\n    activateUpdate() {\n        if (!this.sw.isEnabled) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const nonce = this.sw.generateNonce();\n        return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', { nonce }, nonce);\n    }\n}\nSwUpdate.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SwUpdate, deps: [{ token: NgswCommChannel }], target: i0.ɵɵFactoryTarget.Injectable });\nSwUpdate.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SwUpdate });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SwUpdate, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: NgswCommChannel }]; } });\n\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nclass SwRegistrationOptions {\n}\nconst SCRIPT = new InjectionToken('NGSW_REGISTER_SCRIPT');\nfunction ngswAppInitializer(injector, script, options, platformId) {\n    return () => {\n        if (!(isPlatformBrowser(platformId) && ('serviceWorker' in navigator) &&\n            options.enabled !== false)) {\n            return;\n        }\n        // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n        // becomes active. This allows the SW to initialize itself even if there is no application\n        // traffic.\n        navigator.serviceWorker.addEventListener('controllerchange', () => {\n            if (navigator.serviceWorker.controller !== null) {\n                navigator.serviceWorker.controller.postMessage({ action: 'INITIALIZE' });\n            }\n        });\n        let readyToRegister$;\n        if (typeof options.registrationStrategy === 'function') {\n            readyToRegister$ = options.registrationStrategy();\n        }\n        else {\n            const [strategy, ...args] = (options.registrationStrategy || 'registerWhenStable:30000').split(':');\n            switch (strategy) {\n                case 'registerImmediately':\n                    readyToRegister$ = of(null);\n                    break;\n                case 'registerWithDelay':\n                    readyToRegister$ = delayWithTimeout(+args[0] || 0);\n                    break;\n                case 'registerWhenStable':\n                    readyToRegister$ = !args[0] ? whenStable(injector) :\n                        merge(whenStable(injector), delayWithTimeout(+args[0]));\n                    break;\n                default:\n                    // Unknown strategy.\n                    throw new Error(`Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`);\n            }\n        }\n        // Don't return anything to avoid blocking the application until the SW is registered.\n        // Also, run outside the Angular zone to avoid preventing the app from stabilizing (especially\n        // given that some registration strategies wait for the app to stabilize).\n        // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n        const ngZone = injector.get(NgZone);\n        ngZone.runOutsideAngular(() => readyToRegister$.pipe(take(1)).subscribe(() => navigator.serviceWorker.register(script, { scope: options.scope })\n            .catch(err => console.error('Service worker registration failed with:', err))));\n    };\n}\nfunction delayWithTimeout(timeout) {\n    return of(null).pipe(delay(timeout));\n}\nfunction whenStable(injector) {\n    const appRef = injector.get(ApplicationRef);\n    return appRef.isStable.pipe(filter(stable => stable));\n}\nfunction ngswCommChannelFactory(opts, platformId) {\n    return new NgswCommChannel(isPlatformBrowser(platformId) && opts.enabled !== false ? navigator.serviceWorker :\n        undefined);\n}\n/**\n * @publicApi\n */\nclass ServiceWorkerModule {\n    /**\n     * Register the given Angular Service Worker script.\n     *\n     * If `enabled` is set to `false` in the given options, the module will behave as if service\n     * workers are not supported by the browser, and the service worker will not be registered.\n     */\n    static register(script, opts = {}) {\n        return {\n            ngModule: ServiceWorkerModule,\n            providers: [\n                { provide: SCRIPT, useValue: script },\n                { provide: SwRegistrationOptions, useValue: opts },\n                {\n                    provide: NgswCommChannel,\n                    useFactory: ngswCommChannelFactory,\n                    deps: [SwRegistrationOptions, PLATFORM_ID]\n                },\n                {\n                    provide: APP_INITIALIZER,\n                    useFactory: ngswAppInitializer,\n                    deps: [Injector, SCRIPT, SwRegistrationOptions, PLATFORM_ID],\n                    multi: true,\n                },\n            ],\n        };\n    }\n}\nServiceWorkerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: ServiceWorkerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nServiceWorkerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: ServiceWorkerModule });\nServiceWorkerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: ServiceWorkerModule, providers: [SwPush, SwUpdate] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: ServiceWorkerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [SwPush, SwUpdate],\n                }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ServiceWorkerModule, SwPush, SwRegistrationOptions, SwUpdate };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,iBAAiB,QAAQ,iBAAiB;AACnD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,cAAc,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACpI,SAASC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,EAAE,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACtF,SAASC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,gBAAgB;AAElF,MAAMC,oBAAoB,GAAG,+DAA+D;AAC5F,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC9B,OAAOjB,KAAK,CAAC,MAAMC,UAAU,CAAC,IAAIiB,KAAK,CAACD,OAAO,CAAC,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClBC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACA,aAAa,EAAE;MAChB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,YAAY,GAAGR,eAAe,CAACD,oBAAoB,CAAC;IACzF,CAAC,MACI;MACD,MAAMU,sBAAsB,GAAGvB,SAAS,CAACmB,aAAa,EAAE,kBAAkB,CAAC;MAC3E,MAAMK,iBAAiB,GAAGD,sBAAsB,CAACE,IAAI,CAACnB,GAAG,CAAC,MAAMa,aAAa,CAACO,UAAU,CAAC,CAAC;MAC1F,MAAMC,iBAAiB,GAAG7B,KAAK,CAAC,MAAMG,EAAE,CAACkB,aAAa,CAACO,UAAU,CAAC,CAAC;MACnE,MAAME,qBAAqB,GAAG1B,MAAM,CAACyB,iBAAiB,EAAEH,iBAAiB,CAAC;MAC1E,IAAI,CAACJ,MAAM,GAAGQ,qBAAqB,CAACH,IAAI,CAAClB,MAAM,CAAEsB,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAAC;MAC5D,IAAI,CAACP,YAAY,GAAI,IAAI,CAACF,MAAM,CAACK,IAAI,CAACjB,SAAS,CAAC,MAAMW,aAAa,CAACW,eAAe,CAAC,CAAC,CAAC,CAAE;MACxF,MAAMC,SAAS,GAAG/B,SAAS,CAACmB,aAAa,EAAE,SAAS,CAAC;MACrD,MAAMa,eAAe,GAAGD,SAAS,CAACN,IAAI,CAACnB,GAAG,CAAC2B,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC;MAChE,MAAMC,iBAAiB,GAAGH,eAAe,CAACP,IAAI,CAAClB,MAAM,CAAC0B,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACG,IAAI,CAAC,CAAC;MACpF,MAAMf,MAAM,GAAGc,iBAAiB,CAACV,IAAI,CAAChB,OAAO,CAAC,CAAC,CAAC;MAChDY,MAAM,CAACgB,OAAO,CAAC,CAAC;MAChB,IAAI,CAAChB,MAAM,GAAGA,MAAM;IACxB;EACJ;EACAiB,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACzB,OAAO,IAAI,CAACpB,MAAM,CACbK,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAE8B,EAAE,IAAK;MAC3BA,EAAE,CAACH,WAAW,CAAC;QACXC,MAAM;QACN,GAAGC;MACP,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CACEE,SAAS,CAAC,CAAC,CACXC,IAAI,CAAC,MAAMC,SAAS,CAAC;EAC9B;EACAC,wBAAwBA,CAACT,IAAI,EAAEI,OAAO,EAAEM,cAAc,EAAE;IACpD,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACD,cAAc,CAAC;IAChF,MAAMR,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,EAAEI,OAAO,CAAC;IACnD,OAAOQ,OAAO,CAACC,GAAG,CAAC,CAACX,WAAW,EAAES,yBAAyB,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC,GAAGO,MAAM,CAAC,KAAKA,MAAM,CAAC;EAC7F;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC;EAC/C;EACAC,YAAYA,CAACnB,IAAI,EAAE;IACf,IAAIoB,QAAQ;IACZ,IAAI,OAAOpB,IAAI,KAAK,QAAQ,EAAE;MAC1BoB,QAAQ,GAAIvB,KAAK,IAAKA,KAAK,CAACG,IAAI,KAAKA,IAAI;IAC7C,CAAC,MACI;MACDoB,QAAQ,GAAIvB,KAAK,IAAKG,IAAI,CAACqB,QAAQ,CAACxB,KAAK,CAACG,IAAI,CAAC;IACnD;IACA,OAAO,IAAI,CAACf,MAAM,CAACI,IAAI,CAAClB,MAAM,CAACiD,QAAQ,CAAC,CAAC;EAC7C;EACAE,eAAeA,CAACtB,IAAI,EAAE;IAClB,OAAO,IAAI,CAACmB,YAAY,CAACnB,IAAI,CAAC,CAACX,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAAC;EAChD;EACAqC,yBAAyBA,CAACY,KAAK,EAAE;IAC7B,OAAO,IAAI,CAACJ,YAAY,CAAC,qBAAqB,CAAC,CAC1C9B,IAAI,CAAClB,MAAM,CAAC0B,KAAK,IAAIA,KAAK,CAAC0B,KAAK,KAAKA,KAAK,CAAC,EAAEjD,IAAI,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAAC2B,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACiB,MAAM,KAAKN,SAAS,EAAE;QAC5B,OAAOX,KAAK,CAACiB,MAAM;MACvB;MACA,MAAM,IAAIlC,KAAK,CAACiB,KAAK,CAAC2B,KAAK,CAAC;IAChC,CAAC,CAAC,CAAC,CACElB,SAAS,CAAC,CAAC;EACpB;EACA,IAAImB,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAAC1C,aAAa;EAC/B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2C,MAAM,CAAC;EACT;AACJ;AACA;AACA;EACI,IAAID,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpB,EAAE,CAACoB,SAAS;EAC5B;EACA3C,WAAWA,CAACuB,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACsB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,mBAAmB,GAAG,IAAI7D,OAAO,CAAC,CAAC;IACxC,IAAI,CAACsC,EAAE,CAACoB,SAAS,EAAE;MACf,IAAI,CAACI,QAAQ,GAAG7D,KAAK;MACrB,IAAI,CAAC8D,kBAAkB,GAAG9D,KAAK;MAC/B,IAAI,CAAC+D,YAAY,GAAG/D,KAAK;MACzB;IACJ;IACA,IAAI,CAAC6D,QAAQ,GAAG,IAAI,CAACxB,EAAE,CAACc,YAAY,CAAC,MAAM,CAAC,CAAC9B,IAAI,CAACnB,GAAG,CAACS,OAAO,IAAIA,OAAO,CAACmB,IAAI,CAAC,CAAC;IAC/E,IAAI,CAACgC,kBAAkB,GACnB,IAAI,CAACzB,EAAE,CAACc,YAAY,CAAC,oBAAoB,CAAC,CAAC9B,IAAI,CAACnB,GAAG,CAAES,OAAO,IAAKA,OAAO,CAACmB,IAAI,CAAC,CAAC;IACnF,IAAI,CAAC6B,WAAW,GAAG,IAAI,CAACtB,EAAE,CAACnB,YAAY,CAACG,IAAI,CAACnB,GAAG,CAACgB,YAAY,IAAIA,YAAY,CAACyC,WAAW,CAAC,CAAC;IAC3F,MAAMK,yBAAyB,GAAG,IAAI,CAACL,WAAW,CAACtC,IAAI,CAACjB,SAAS,CAAC6D,EAAE,IAAIA,EAAE,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACH,YAAY,GAAG9D,KAAK,CAAC+D,yBAAyB,EAAE,IAAI,CAACJ,mBAAmB,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIO,mBAAmBA,CAACC,OAAO,EAAE;IACzB,IAAI,CAAC,IAAI,CAAC/B,EAAE,CAACoB,SAAS,IAAI,IAAI,CAACE,WAAW,KAAK,IAAI,EAAE;MACjD,OAAOf,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM6D,WAAW,GAAG;MAAEC,eAAe,EAAE;IAAK,CAAC;IAC7C,IAAIC,GAAG,GAAG,IAAI,CAACC,YAAY,CAACL,OAAO,CAACM,eAAe,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1F,IAAIC,oBAAoB,GAAG,IAAIC,UAAU,CAAC,IAAIC,WAAW,CAACN,GAAG,CAACO,MAAM,CAAC,CAAC;IACtE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACO,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjCJ,oBAAoB,CAACI,CAAC,CAAC,GAAGR,GAAG,CAACS,UAAU,CAACD,CAAC,CAAC;IAC/C;IACAV,WAAW,CAACM,oBAAoB,GAAGA,oBAAoB;IACvD,OAAO,IAAI,CAACjB,WAAW,CAACtC,IAAI,CAACjB,SAAS,CAAC6D,EAAE,IAAIA,EAAE,CAACiB,SAAS,CAACZ,WAAW,CAAC,CAAC,EAAEhE,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5EgC,SAAS,CAAC,CAAC,CACXC,IAAI,CAAC4C,GAAG,IAAI;MACb,IAAI,CAACvB,mBAAmB,CAACwB,IAAI,CAACD,GAAG,CAAC;MAClC,OAAOA,GAAG;IACd,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAChD,EAAE,CAACoB,SAAS,EAAE;MACpB,OAAOb,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM6E,aAAa,GAAIH,GAAG,IAAK;MAC3B,IAAIA,GAAG,KAAK,IAAI,EAAE;QACd,MAAM,IAAIvE,KAAK,CAAC,uCAAuC,CAAC;MAC5D;MACA,OAAOuE,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC9C,IAAI,CAACgD,OAAO,IAAI;QACrC,IAAI,CAACA,OAAO,EAAE;UACV,MAAM,IAAI3E,KAAK,CAAC,qBAAqB,CAAC;QAC1C;QACA,IAAI,CAACgD,mBAAmB,CAACwB,IAAI,CAAC,IAAI,CAAC;MACvC,CAAC,CAAC;IACN,CAAC;IACD,OAAO,IAAI,CAACrB,YAAY,CAAC1C,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,EAAEF,SAAS,CAACkF,aAAa,CAAC,CAAC,CAAChD,SAAS,CAAC,CAAC;EAChF;EACAmC,YAAYA,CAACe,KAAK,EAAE;IAChB,OAAOC,IAAI,CAACD,KAAK,CAAC;EACtB;AACJ;AACA9B,MAAM,CAACgC,IAAI,YAAAC,eAAAC,CAAA;EAAA,YAAAA,CAAA,IAAyFlC,MAAM,EAAhBzE,EAAE,CAAA4G,QAAA,CAAgChF,eAAe;AAAA,CAA6C;AACxL6C,MAAM,CAACoC,KAAK,kBAD8E7G,EAAE,CAAA8G,kBAAA;EAAAC,KAAA,EACYtC,MAAM;EAAAuC,OAAA,EAANvC,MAAM,CAAAgC;AAAA,EAAG;AACjH;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAF0FjH,EAAE,CAAAkH,iBAAA,CAEAzC,MAAM,EAAc,CAAC;IACrG1B,IAAI,EAAE9C;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE8C,IAAI,EAAEnB;IAAgB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuF,QAAQ,CAAC;EACX;AACJ;AACA;AACA;EACI,IAAI3C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpB,EAAE,CAACoB,SAAS;EAC5B;EACA3C,WAAWA,CAACuB,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACA,EAAE,CAACoB,SAAS,EAAE;MACf,IAAI,CAAC4C,cAAc,GAAGrG,KAAK;MAC3B,IAAI,CAACsG,SAAS,GAAGtG,KAAK;MACtB,IAAI,CAACuG,SAAS,GAAGvG,KAAK;MACtB,IAAI,CAACwG,aAAa,GAAGxG,KAAK;MAC1B;IACJ;IACA,IAAI,CAACqG,cAAc,GAAG,IAAI,CAAChE,EAAE,CAACc,YAAY,CAAC,CACvC,kBAAkB,EAClB,6BAA6B,EAC7B,eAAe,EACf,yBAAyB,CAC5B,CAAC;IACF,IAAI,CAACmD,SAAS,GAAG,IAAI,CAACD,cAAc,CAAChF,IAAI,CAAClB,MAAM,CAAEsG,GAAG,IAAKA,GAAG,CAACzE,IAAI,KAAK,eAAe,CAAC,EAAE9B,GAAG,CAACuG,GAAG,KAAK;MACjGzE,IAAI,EAAE,kBAAkB;MACxB0E,OAAO,EAAED,GAAG,CAACE,cAAc;MAC3BL,SAAS,EAAEG,GAAG,CAACG;IACnB,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACL,SAAS,GAAG,IAAI,CAAClE,EAAE,CAACc,YAAY,CAAC,kBAAkB,CAAC;IACzD,IAAI,CAACqD,aAAa,GAAG,IAAI,CAACnE,EAAE,CAACc,YAAY,CAAC,qBAAqB,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0D,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACxE,EAAE,CAACoB,SAAS,EAAE;MACpB,OAAOb,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM8C,KAAK,GAAG,IAAI,CAAClB,EAAE,CAACU,aAAa,CAAC,CAAC;IACrC,OAAO,IAAI,CAACV,EAAE,CAACI,wBAAwB,CAAC,mBAAmB,EAAE;MAAEc;IAAM,CAAC,EAAEA,KAAK,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuD,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACzE,EAAE,CAACoB,SAAS,EAAE;MACpB,OAAOb,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM8C,KAAK,GAAG,IAAI,CAAClB,EAAE,CAACU,aAAa,CAAC,CAAC;IACrC,OAAO,IAAI,CAACV,EAAE,CAACI,wBAAwB,CAAC,iBAAiB,EAAE;MAAEc;IAAM,CAAC,EAAEA,KAAK,CAAC;EAChF;AACJ;AACA6C,QAAQ,CAACV,IAAI,YAAAqB,iBAAAnB,CAAA;EAAA,YAAAA,CAAA,IAAyFQ,QAAQ,EA7FpBnH,EAAE,CAAA4G,QAAA,CA6FoChF,eAAe;AAAA,CAA6C;AAC5LuF,QAAQ,CAACN,KAAK,kBA9F4E7G,EAAE,CAAA8G,kBAAA;EAAAC,KAAA,EA8FcI,QAAQ;EAAAH,OAAA,EAARG,QAAQ,CAAAV;AAAA,EAAG;AACrH;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA/F0FjH,EAAE,CAAAkH,iBAAA,CA+FAC,QAAQ,EAAc,CAAC;IACvGpE,IAAI,EAAE9C;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE8C,IAAI,EAAEnB;IAAgB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmG,qBAAqB,CAAC;AAE5B,MAAMC,MAAM,GAAG,IAAI9H,cAAc,CAAC,sBAAsB,CAAC;AACzD,SAAS+H,kBAAkBA,CAACC,QAAQ,EAAEC,MAAM,EAAEhD,OAAO,EAAEiD,UAAU,EAAE;EAC/D,OAAO,MAAM;IACT,IAAI,EAAErI,iBAAiB,CAACqI,UAAU,CAAC,IAAK,eAAe,IAAIC,SAAU,IACjElD,OAAO,CAACmD,OAAO,KAAK,KAAK,CAAC,EAAE;MAC5B;IACJ;IACA;IACA;IACA;IACAD,SAAS,CAACvG,aAAa,CAACyG,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAC/D,IAAIF,SAAS,CAACvG,aAAa,CAACO,UAAU,KAAK,IAAI,EAAE;QAC7CgG,SAAS,CAACvG,aAAa,CAACO,UAAU,CAACY,WAAW,CAAC;UAAEC,MAAM,EAAE;QAAa,CAAC,CAAC;MAC5E;IACJ,CAAC,CAAC;IACF,IAAIsF,gBAAgB;IACpB,IAAI,OAAOrD,OAAO,CAACsD,oBAAoB,KAAK,UAAU,EAAE;MACpDD,gBAAgB,GAAGrD,OAAO,CAACsD,oBAAoB,CAAC,CAAC;IACrD,CAAC,MACI;MACD,MAAM,CAACC,QAAQ,EAAE,GAAGC,IAAI,CAAC,GAAG,CAACxD,OAAO,CAACsD,oBAAoB,IAAI,0BAA0B,EAAEG,KAAK,CAAC,GAAG,CAAC;MACnG,QAAQF,QAAQ;QACZ,KAAK,qBAAqB;UACtBF,gBAAgB,GAAG5H,EAAE,CAAC,IAAI,CAAC;UAC3B;QACJ,KAAK,mBAAmB;UACpB4H,gBAAgB,GAAGK,gBAAgB,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAClD;QACJ,KAAK,oBAAoB;UACrBH,gBAAgB,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACZ,QAAQ,CAAC,GAC9ClH,KAAK,CAAC8H,UAAU,CAACZ,QAAQ,CAAC,EAAEW,gBAAgB,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D;QACJ;UACI;UACA,MAAM,IAAIhH,KAAK,CAAE,gDAA+CwD,OAAO,CAACsD,oBAAqB,EAAC,CAAC;MACvG;IACJ;IACA;IACA;IACA;IACA;IACA,MAAMM,MAAM,GAAGb,QAAQ,CAACc,GAAG,CAAC7I,MAAM,CAAC;IACnC4I,MAAM,CAACE,iBAAiB,CAAC,MAAMT,gBAAgB,CAACpG,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAAC,MAAMoC,SAAS,CAACvG,aAAa,CAACoH,QAAQ,CAACf,MAAM,EAAE;MAAEgB,KAAK,EAAEhE,OAAO,CAACgE;IAAM,CAAC,CAAC,CAC3IC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAAC/E,KAAK,CAAC,0CAA0C,EAAE8E,GAAG,CAAC,CAAC,CAAC,CAAC;EACvF,CAAC;AACL;AACA,SAASR,gBAAgBA,CAACU,OAAO,EAAE;EAC/B,OAAO3I,EAAE,CAAC,IAAI,CAAC,CAACwB,IAAI,CAACb,KAAK,CAACgI,OAAO,CAAC,CAAC;AACxC;AACA,SAAST,UAAUA,CAACZ,QAAQ,EAAE;EAC1B,MAAMsB,MAAM,GAAGtB,QAAQ,CAACc,GAAG,CAAC5I,cAAc,CAAC;EAC3C,OAAOoJ,MAAM,CAACC,QAAQ,CAACrH,IAAI,CAAClB,MAAM,CAACwI,MAAM,IAAIA,MAAM,CAAC,CAAC;AACzD;AACA,SAASC,sBAAsBA,CAACC,IAAI,EAAExB,UAAU,EAAE;EAC9C,OAAO,IAAIxG,eAAe,CAAC7B,iBAAiB,CAACqI,UAAU,CAAC,IAAIwB,IAAI,CAACtB,OAAO,KAAK,KAAK,GAAGD,SAAS,CAACvG,aAAa,GACxGyB,SAAS,CAAC;AAClB;AACA;AACA;AACA;AACA,MAAMsG,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOX,QAAQA,CAACf,MAAM,EAAEyB,IAAI,GAAG,CAAC,CAAC,EAAE;IAC/B,OAAO;MACHE,QAAQ,EAAED,mBAAmB;MAC7BE,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEhC,MAAM;QAAEiC,QAAQ,EAAE9B;MAAO,CAAC,EACrC;QAAE6B,OAAO,EAAEjC,qBAAqB;QAAEkC,QAAQ,EAAEL;MAAK,CAAC,EAClD;QACII,OAAO,EAAEpI,eAAe;QACxBsI,UAAU,EAAEP,sBAAsB;QAClCQ,IAAI,EAAE,CAACpC,qBAAqB,EAAE1H,WAAW;MAC7C,CAAC,EACD;QACI2J,OAAO,EAAE1J,eAAe;QACxB4J,UAAU,EAAEjC,kBAAkB;QAC9BkC,IAAI,EAAE,CAAC5J,QAAQ,EAAEyH,MAAM,EAAED,qBAAqB,EAAE1H,WAAW,CAAC;QAC5D+J,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACL;AACJ;AACAP,mBAAmB,CAACpD,IAAI,YAAA4D,4BAAA1D,CAAA;EAAA,YAAAA,CAAA,IAAyFkD,mBAAmB;AAAA,CAAkD;AACtLA,mBAAmB,CAACS,IAAI,kBA1MkEtK,EAAE,CAAAuK,gBAAA;EAAAxH,IAAA,EA0MsB8G;AAAmB,EAAG;AACxIA,mBAAmB,CAACW,IAAI,kBA3MkExK,EAAE,CAAAyK,gBAAA;EAAAV,SAAA,EA2MsD,CAACtF,MAAM,EAAE0C,QAAQ;AAAC,EAAG;AACvK;EAAA,QAAAF,SAAA,oBAAAA,SAAA,KA5M0FjH,EAAE,CAAAkH,iBAAA,CA4MA2C,mBAAmB,EAAc,CAAC;IAClH9G,IAAI,EAAEvC,QAAQ;IACdmI,IAAI,EAAE,CAAC;MACCoB,SAAS,EAAE,CAACtF,MAAM,EAAE0C,QAAQ;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS0C,mBAAmB,EAAEpF,MAAM,EAAEsD,qBAAqB,EAAEZ,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
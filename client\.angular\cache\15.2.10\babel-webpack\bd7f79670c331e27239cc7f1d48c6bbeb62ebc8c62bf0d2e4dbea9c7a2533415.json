{"ast": null, "code": "let _enable_super_gross_mode_that_will_cause_bad_things = false;\nexport const config = {\n  Promise: undefined,\n  set useDeprecatedSynchronousErrorHandling(value) {\n    if (value) {\n      const error = new Error();\n      console.warn('DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \\n' + error.stack);\n    } else if (_enable_super_gross_mode_that_will_cause_bad_things) {\n      console.log('RxJS: Back to a better error behavior. Thank you. <3');\n    }\n    _enable_super_gross_mode_that_will_cause_bad_things = value;\n  },\n  get useDeprecatedSynchronousErrorHandling() {\n    return _enable_super_gross_mode_that_will_cause_bad_things;\n  }\n};", "map": {"version": 3, "names": ["_enable_super_gross_mode_that_will_cause_bad_things", "config", "Promise", "undefined", "useDeprecatedSynchronousErrorHandling", "value", "error", "Error", "console", "warn", "stack", "log"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/config.js"], "sourcesContent": ["let _enable_super_gross_mode_that_will_cause_bad_things = false;\nexport const config = {\n    Promise: undefined,\n    set useDeprecatedSynchronousErrorHandling(value) {\n        if (value) {\n            const error = new Error();\n            console.warn('DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \\n' + error.stack);\n        }\n        else if (_enable_super_gross_mode_that_will_cause_bad_things) {\n            console.log('RxJS: Back to a better error behavior. Thank you. <3');\n        }\n        _enable_super_gross_mode_that_will_cause_bad_things = value;\n    },\n    get useDeprecatedSynchronousErrorHandling() {\n        return _enable_super_gross_mode_that_will_cause_bad_things;\n    },\n};\n"], "mappings": "AAAA,IAAIA,mDAAmD,GAAG,KAAK;AAC/D,OAAO,MAAMC,MAAM,GAAG;EAClBC,OAAO,EAAEC,SAAS;EAClB,IAAIC,qCAAqCA,CAACC,KAAK,EAAE;IAC7C,IAAIA,KAAK,EAAE;MACP,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;MACzBC,OAAO,CAACC,IAAI,CAAC,+FAA+F,GAAGH,KAAK,CAACI,KAAK,CAAC;IAC/H,CAAC,MACI,IAAIV,mDAAmD,EAAE;MAC1DQ,OAAO,CAACG,GAAG,CAAC,sDAAsD,CAAC;IACvE;IACAX,mDAAmD,GAAGK,KAAK;EAC/D,CAAC;EACD,IAAID,qCAAqCA,CAAA,EAAG;IACxC,OAAOJ,mDAAmD;EAC9D;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
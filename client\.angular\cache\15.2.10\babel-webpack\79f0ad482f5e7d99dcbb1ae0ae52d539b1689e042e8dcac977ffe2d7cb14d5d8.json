{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\nimport toInteger from './toInteger.js';\n\n/**\n * This method is like `_.flatMap` except that it recursively flattens the\n * mapped results up to `depth` times.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {number} [depth=1] The maximum recursion depth.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [[[n, n]]];\n * }\n *\n * _.flatMapDepth([1, 2], duplicate, 2);\n * // => [[1, 1], [2, 2]]\n */\nfunction flatMapDepth(collection, iteratee, depth) {\n  depth = depth === undefined ? 1 : toInteger(depth);\n  return baseFlatten(map(collection, iteratee), depth);\n}\nexport default flatMapDepth;", "map": {"version": 3, "names": ["baseFlatten", "map", "toInteger", "flatMapDepth", "collection", "iteratee", "depth", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/flatMapDepth.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\nimport toInteger from './toInteger.js';\n\n/**\n * This method is like `_.flatMap` except that it recursively flattens the\n * mapped results up to `depth` times.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {number} [depth=1] The maximum recursion depth.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [[[n, n]]];\n * }\n *\n * _.flatMapDepth([1, 2], duplicate, 2);\n * // => [[1, 1], [2, 2]]\n */\nfunction flatMapDepth(collection, iteratee, depth) {\n  depth = depth === undefined ? 1 : toInteger(depth);\n  return baseFlatten(map(collection, iteratee), depth);\n}\n\nexport default flatMapDepth;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,GAAG,MAAM,UAAU;AAC1B,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EACjDA,KAAK,GAAGA,KAAK,KAAKC,SAAS,GAAG,CAAC,GAAGL,SAAS,CAACI,KAAK,CAAC;EAClD,OAAON,WAAW,CAACC,GAAG,CAACG,UAAU,EAAEC,QAAQ,CAAC,EAAEC,KAAK,CAAC;AACtD;AAEA,eAAeH,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
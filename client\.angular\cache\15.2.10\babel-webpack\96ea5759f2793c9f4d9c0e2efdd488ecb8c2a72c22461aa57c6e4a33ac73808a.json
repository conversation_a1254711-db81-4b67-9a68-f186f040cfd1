{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { fromArray } from './fromArray';\nimport { scheduleArray } from '../scheduled/scheduleArray';\nexport function of(...args) {\n  let scheduler = args[args.length - 1];\n  if (isScheduler(scheduler)) {\n    args.pop();\n    return scheduleArray(args, scheduler);\n  } else {\n    return fromArray(args);\n  }\n}", "map": {"version": 3, "names": ["isScheduler", "fromArray", "scheduleArray", "of", "args", "scheduler", "length", "pop"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/of.js"], "sourcesContent": ["import { isScheduler } from '../util/isScheduler';\nimport { fromArray } from './fromArray';\nimport { scheduleArray } from '../scheduled/scheduleArray';\nexport function of(...args) {\n    let scheduler = args[args.length - 1];\n    if (isScheduler(scheduler)) {\n        args.pop();\n        return scheduleArray(args, scheduler);\n    }\n    else {\n        return fromArray(args);\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,SAASC,EAAEA,CAAC,GAAGC,IAAI,EAAE;EACxB,IAAIC,SAAS,GAAGD,IAAI,CAACA,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC;EACrC,IAAIN,WAAW,CAACK,SAAS,CAAC,EAAE;IACxBD,IAAI,CAACG,GAAG,CAAC,CAAC;IACV,OAAOL,aAAa,CAACE,IAAI,EAAEC,SAAS,CAAC;EACzC,CAAC,MACI;IACD,OAAOJ,SAAS,CAACG,IAAI,CAAC;EAC1B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
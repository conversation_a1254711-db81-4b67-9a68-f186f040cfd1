{"ast": null, "code": "import { reduce } from './reduce';\nexport function min(comparer) {\n  const min = typeof comparer === 'function' ? (x, y) => comparer(x, y) < 0 ? x : y : (x, y) => x < y ? x : y;\n  return reduce(min);\n}", "map": {"version": 3, "names": ["reduce", "min", "comparer", "x", "y"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/min.js"], "sourcesContent": ["import { reduce } from './reduce';\nexport function min(comparer) {\n    const min = (typeof comparer === 'function')\n        ? (x, y) => comparer(x, y) < 0 ? x : y\n        : (x, y) => x < y ? x : y;\n    return reduce(min);\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,OAAO,SAASC,GAAGA,CAACC,QAAQ,EAAE;EAC1B,MAAMD,GAAG,GAAI,OAAOC,QAAQ,KAAK,UAAU,GACrC,CAACC,CAAC,EAAEC,CAAC,KAAKF,QAAQ,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GACpC,CAACD,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAC7B,OAAOJ,MAAM,CAACC,GAAG,CAAC;AACtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
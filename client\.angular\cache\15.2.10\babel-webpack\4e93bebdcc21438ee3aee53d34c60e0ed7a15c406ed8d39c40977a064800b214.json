{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { TimeoutError } from '../util/TimeoutError';\nimport { timeoutWith } from './timeoutWith';\nimport { throwError } from '../observable/throwError';\nexport function timeout(due, scheduler = async) {\n  return timeoutWith(due, throwError(new TimeoutError()), scheduler);\n}", "map": {"version": 3, "names": ["async", "TimeoutError", "timeoutWith", "throwError", "timeout", "due", "scheduler"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/timeout.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { TimeoutError } from '../util/TimeoutError';\nimport { timeoutWith } from './timeoutWith';\nimport { throwError } from '../observable/throwError';\nexport function timeout(due, scheduler = async) {\n    return timeoutWith(due, throwError(new TimeoutError()), scheduler);\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAEC,SAAS,GAAGN,KAAK,EAAE;EAC5C,OAAOE,WAAW,CAACG,GAAG,EAAEF,UAAU,CAAC,IAAIF,YAAY,CAAC,CAAC,CAAC,EAAEK,SAAS,CAAC;AACtE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SelectClubModuleComponent } from './select-club-module.component';\nimport { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport * as i0 from \"@angular/core\";\nexport class SelectClubModuleModule {\n  static #_ = this.ɵfac = function SelectClubModuleModule_Factory(t) {\n    return new (t || SelectClubModuleModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SelectClubModuleModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, TranslateModule, NgSelectModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SelectClubModuleModule, {\n    declarations: [SelectClubModuleComponent],\n    imports: [CommonModule, FormsModule, TranslateModule, NgSelectModule],\n    exports: [SelectClubModuleComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,QAAQ,gCAAgC;AAE1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,sBAAsB;;AAOrD,OAAM,MAAOC,sBAAsB;EAAA,QAAAC,CAAA;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA;UAAtBF;EAAsB;EAAA,QAAAG,EAAA;cAHvBL,YAAY,EAAEH,WAAW,EAAEC,eAAe,EAAEG,cAAc;EAAA;;;2EAGzDC,sBAAsB;IAAAI,YAAA,GAJlBP,yBAAyB;IAAAQ,OAAA,GAC9BP,YAAY,EAAEH,WAAW,EAAEC,eAAe,EAAEG,cAAc;IAAAO,OAAA,GAC1DT,yBAAyB;EAAA;AAAA", "names": ["FormsModule", "TranslateModule", "SelectClubModuleComponent", "CommonModule", "NgSelectModule", "SelectClubModuleModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\select-club-module\\select-club-module.module.ts"], "sourcesContent": ["import { FormsModule } from '@angular/forms';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { SelectClubModuleComponent } from './select-club-module.component';\r\nimport { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\n\r\n@NgModule({\r\n  declarations: [SelectClubModuleComponent],\r\n  imports: [CommonModule, FormsModule, TranslateModule, NgSelectModule],\r\n  exports: [SelectClubModuleComponent],\r\n})\r\nexport class SelectClubModuleModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
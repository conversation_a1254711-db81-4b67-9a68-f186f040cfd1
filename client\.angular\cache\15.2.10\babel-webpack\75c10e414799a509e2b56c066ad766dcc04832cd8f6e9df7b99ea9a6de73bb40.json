{"ast": null, "code": "import { TranslateModuleModule } from 'app/components/translate-module/translate-module.module';\nimport { RouterModule } from '@angular/router';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { PerfectScrollbarModule, PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';\nimport { CoreCommonModule } from '@core/common.module';\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\nimport { NavbarComponent } from 'app/layout/components/navbar/navbar.component';\nimport { NavbarBookmarkComponent } from 'app/layout/components/navbar/navbar-bookmark/navbar-bookmark.component';\nimport { NavbarSearchComponent } from 'app/layout/components/navbar/navbar-search/navbar-search.component';\nimport { NavbarNotificationComponent } from 'app/layout/components/navbar/navbar-notification/navbar-notification.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\nimport * as i0 from \"@angular/core\";\nconst DEFAULT_PERFECT_SCROLLBAR_CONFIG = {\n  suppressScrollX: true,\n  wheelPropagation: false\n};\nexport class NavbarModule {\n  static #_ = this.ɵfac = function NavbarModule_Factory(t) {\n    return new (t || NavbarModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: NavbarModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: PERFECT_SCROLLBAR_CONFIG,\n      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG\n    }],\n    imports: [RouterModule, NgbModule, CoreCommonModule, PerfectScrollbarModule, CoreTouchspinModule, TranslateModule, TranslateModuleModule, HostListenersModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NavbarModule, {\n    declarations: [NavbarComponent, NavbarSearchComponent, NavbarBookmarkComponent, NavbarNotificationComponent],\n    imports: [RouterModule, NgbModule, CoreCommonModule, PerfectScrollbarModule, CoreTouchspinModule, TranslateModule, TranslateModuleModule, HostListenersModule],\n    exports: [NavbarComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,qBAAqB,QAAQ,yDAAyD;AAE/F,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAEEC,sBAAsB,EACtBC,wBAAwB,QACnB,uBAAuB;AAE9B,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,mBAAmB,QAAQ,uDAAuD;AAE3F,SAASC,eAAe,QAAQ,+CAA+C;AAC/E,SAASC,uBAAuB,QAAQ,wEAAwE;AAChH,SAASC,qBAAqB,QAAQ,oEAAoE;AAE1G,SAASC,2BAA2B,QAAQ,gFAAgF;AAC5H,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yCAAyC;;AAC7E,MAAMC,gCAAgC,GAAoC;EACxEC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE;CACnB;AA2BD,OAAM,MAAOC,YAAY;EAAA,QAAAC,CAAA;qBAAZD,YAAY;EAAA;EAAA,QAAAE,EAAA;UAAZF;EAAY;EAAA,QAAAG,EAAA;eARZ,CACT;MACEC,OAAO,EAAEhB,wBAAwB;MACjCiB,QAAQ,EAAER;KACX,CACF;IAAAS,OAAA,GAdCrB,YAAY,EACZC,SAAS,EACTG,gBAAgB,EAChBF,sBAAsB,EACtBG,mBAAmB,EACnBK,eAAe,EACfX,qBAAqB,EACrBY,mBAAmB;EAAA;;;2EAUVI,YAAY;IAAAO,YAAA,GAvBrBhB,eAAe,EACfE,qBAAqB,EACrBD,uBAAuB,EACvBE,2BAA2B;IAAAY,OAAA,GAG3BrB,YAAY,EACZC,SAAS,EACTG,gBAAgB,EAChBF,sBAAsB,EACtBG,mBAAmB,EACnBK,eAAe,EACfX,qBAAqB,EACrBY,mBAAmB;IAAAY,OAAA,GAQXjB,eAAe;EAAA;AAAA", "names": ["TranslateModuleModule", "RouterModule", "NgbModule", "PerfectScrollbarModule", "PERFECT_SCROLLBAR_CONFIG", "CoreCommonModule", "CoreTouchspinModule", "NavbarComponent", "NavbarBookmarkComponent", "NavbarSearchComponent", "NavbarNotificationComponent", "TranslateModule", "HostListenersModule", "DEFAULT_PERFECT_SCROLLBAR_CONFIG", "suppressScrollX", "wheelPropagation", "NavbarModule", "_", "_2", "_3", "provide", "useValue", "imports", "declarations", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar.module.ts"], "sourcesContent": ["import { TranslateModuleModule } from 'app/components/translate-module/translate-module.module';\r\nimport { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport {\r\n  PerfectScrollbarConfigInterface,\r\n  PerfectScrollbarModule,\r\n  PERFECT_SCROLLBAR_CONFIG,\r\n} from 'ngx-perfect-scrollbar';\r\n\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\r\n\r\nimport { NavbarComponent } from 'app/layout/components/navbar/navbar.component';\r\nimport { NavbarBookmarkComponent } from 'app/layout/components/navbar/navbar-bookmark/navbar-bookmark.component';\r\nimport { NavbarSearchComponent } from 'app/layout/components/navbar/navbar-search/navbar-search.component';\r\n\r\nimport { NavbarNotificationComponent } from 'app/layout/components/navbar/navbar-notification/navbar-notification.component';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\r\nconst DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {\r\n  suppressScrollX: true,\r\n  wheelPropagation: false,\r\n};\r\n\r\n@NgModule({\r\n  declarations: [\r\n    NavbarComponent,\r\n    NavbarSearchComponent,\r\n    NavbarBookmarkComponent,\r\n    NavbarNotificationComponent,\r\n  ],\r\n  imports: [\r\n    RouterModule,\r\n    NgbModule,\r\n    CoreCommonModule,\r\n    PerfectScrollbarModule,\r\n    CoreTouchspinModule,\r\n    TranslateModule,\r\n    TranslateModuleModule,\r\n    HostListenersModule,\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: PERFECT_SCROLLBAR_CONFIG,\r\n      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG,\r\n    },\r\n  ],\r\n  exports: [NavbarComponent],\r\n})\r\nexport class NavbarModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n};\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n};\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid(mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7;\n};\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from(value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined;\n};\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1(data) {\n  const size = data.size;\n  let points = 0;\n  let sameCountCol = 0;\n  let sameCountRow = 0;\n  let lastCol = null;\n  let lastRow = null;\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0;\n    lastCol = lastRow = null;\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col);\n      if (module === lastCol) {\n        sameCountCol++;\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);\n        lastCol = module;\n        sameCountCol = 1;\n      }\n      module = data.get(col, row);\n      if (module === lastRow) {\n        sameCountRow++;\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);\n        lastRow = module;\n        sameCountRow = 1;\n      }\n    }\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);\n  }\n  return points;\n};\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2(data) {\n  const size = data.size;\n  let points = 0;\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) + data.get(row, col + 1) + data.get(row + 1, col) + data.get(row + 1, col + 1);\n      if (last === 4 || last === 0) points++;\n    }\n  }\n  return points * PenaltyScores.N2;\n};\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3(data) {\n  const size = data.size;\n  let points = 0;\n  let bitsCol = 0;\n  let bitsRow = 0;\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0;\n    for (let col = 0; col < size; col++) {\n      bitsCol = bitsCol << 1 & 0x7FF | data.get(row, col);\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++;\n      bitsRow = bitsRow << 1 & 0x7FF | data.get(col, row);\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++;\n    }\n  }\n  return points * PenaltyScores.N3;\n};\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4(data) {\n  let darkCount = 0;\n  const modulesCount = data.data.length;\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i];\n  const k = Math.abs(Math.ceil(darkCount * 100 / modulesCount / 5) - 10);\n  return k * PenaltyScores.N4;\n};\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt(maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000:\n      return (i + j) % 2 === 0;\n    case exports.Patterns.PATTERN001:\n      return i % 2 === 0;\n    case exports.Patterns.PATTERN010:\n      return j % 3 === 0;\n    case exports.Patterns.PATTERN011:\n      return (i + j) % 3 === 0;\n    case exports.Patterns.PATTERN100:\n      return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0;\n    case exports.Patterns.PATTERN101:\n      return i * j % 2 + i * j % 3 === 0;\n    case exports.Patterns.PATTERN110:\n      return (i * j % 2 + i * j % 3) % 2 === 0;\n    case exports.Patterns.PATTERN111:\n      return (i * j % 3 + (i + j) % 2) % 2 === 0;\n    default:\n      throw new Error('bad maskPattern:' + maskPattern);\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask(pattern, data) {\n  const size = data.size;\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue;\n      data.xor(row, col, getMaskAt(pattern, row, col));\n    }\n  }\n};\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask(data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length;\n  let bestPattern = 0;\n  let lowerPenalty = Infinity;\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p);\n    exports.applyMask(p, data);\n\n    // Calculate penalty\n    const penalty = exports.getPenaltyN1(data) + exports.getPenaltyN2(data) + exports.getPenaltyN3(data) + exports.getPenaltyN4(data);\n\n    // Undo previously applied mask\n    exports.applyMask(p, data);\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty;\n      bestPattern = p;\n    }\n  }\n  return bestPattern;\n};", "map": {"version": 3, "names": ["exports", "Patterns", "PATTERN000", "PATTERN001", "PATTERN010", "PATTERN011", "PATTERN100", "PATTERN101", "PATTERN110", "PATTERN111", "PenaltyScores", "N1", "N2", "N3", "N4", "<PERSON><PERSON><PERSON><PERSON>", "mask", "isNaN", "from", "value", "parseInt", "undefined", "getPenaltyN1", "data", "size", "points", "sameCountCol", "sameCountRow", "lastCol", "lastRow", "row", "col", "module", "get", "getPenaltyN2", "last", "getPenaltyN3", "bitsCol", "bitsRow", "getPenaltyN4", "darkCount", "modulesCount", "length", "i", "k", "Math", "abs", "ceil", "getMaskAt", "maskPattern", "j", "floor", "Error", "applyMask", "pattern", "isReserved", "xor", "getBestMask", "setupFormatFunc", "numPatterns", "Object", "keys", "bestPattern", "lowerPenalty", "Infinity", "p", "penalty"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/core/mask-pattern.js"], "sourcesContent": ["/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n}\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n}\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid (mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7\n}\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from (value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined\n}\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1 (data) {\n  const size = data.size\n  let points = 0\n  let sameCountCol = 0\n  let sameCountRow = 0\n  let lastCol = null\n  let lastRow = null\n\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0\n    lastCol = lastRow = null\n\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col)\n      if (module === lastCol) {\n        sameCountCol++\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n        lastCol = module\n        sameCountCol = 1\n      }\n\n      module = data.get(col, row)\n      if (module === lastRow) {\n        sameCountRow++\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n        lastRow = module\n        sameCountRow = 1\n      }\n    }\n\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n  }\n\n  return points\n}\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2 (data) {\n  const size = data.size\n  let points = 0\n\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) +\n        data.get(row, col + 1) +\n        data.get(row + 1, col) +\n        data.get(row + 1, col + 1)\n\n      if (last === 4 || last === 0) points++\n    }\n  }\n\n  return points * PenaltyScores.N2\n}\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3 (data) {\n  const size = data.size\n  let points = 0\n  let bitsCol = 0\n  let bitsRow = 0\n\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0\n    for (let col = 0; col < size; col++) {\n      bitsCol = ((bitsCol << 1) & 0x7FF) | data.get(row, col)\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++\n\n      bitsRow = ((bitsRow << 1) & 0x7FF) | data.get(col, row)\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++\n    }\n  }\n\n  return points * PenaltyScores.N3\n}\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4 (data) {\n  let darkCount = 0\n  const modulesCount = data.data.length\n\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i]\n\n  const k = Math.abs(Math.ceil((darkCount * 100 / modulesCount) / 5) - 10)\n\n  return k * PenaltyScores.N4\n}\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt (maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000: return (i + j) % 2 === 0\n    case exports.Patterns.PATTERN001: return i % 2 === 0\n    case exports.Patterns.PATTERN010: return j % 3 === 0\n    case exports.Patterns.PATTERN011: return (i + j) % 3 === 0\n    case exports.Patterns.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0\n    case exports.Patterns.PATTERN101: return (i * j) % 2 + (i * j) % 3 === 0\n    case exports.Patterns.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 === 0\n    case exports.Patterns.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 === 0\n\n    default: throw new Error('bad maskPattern:' + maskPattern)\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask (pattern, data) {\n  const size = data.size\n\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue\n      data.xor(row, col, getMaskAt(pattern, row, col))\n    }\n  }\n}\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask (data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length\n  let bestPattern = 0\n  let lowerPenalty = Infinity\n\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p)\n    exports.applyMask(p, data)\n\n    // Calculate penalty\n    const penalty =\n      exports.getPenaltyN1(data) +\n      exports.getPenaltyN2(data) +\n      exports.getPenaltyN3(data) +\n      exports.getPenaltyN4(data)\n\n    // Undo previously applied mask\n    exports.applyMask(p, data)\n\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty\n      bestPattern = p\n    }\n  }\n\n  return bestPattern\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACAA,OAAO,CAACC,QAAQ,GAAG;EACjBC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG;EACpBC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAd,OAAO,CAACe,OAAO,GAAG,SAASA,OAAOA,CAAEC,IAAI,EAAE;EACxC,OAAOA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC;AAC9E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAhB,OAAO,CAACkB,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAE;EACnC,OAAOnB,OAAO,CAACe,OAAO,CAACI,KAAK,CAAC,GAAGC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,GAAGE,SAAS;AACjE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACArB,OAAO,CAACsB,YAAY,GAAG,SAASA,YAAYA,CAAEC,IAAI,EAAE;EAClD,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EACtB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,OAAO,GAAG,IAAI;EAElB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,IAAI,EAAEM,GAAG,EAAE,EAAE;IACnCJ,YAAY,GAAGC,YAAY,GAAG,CAAC;IAC/BC,OAAO,GAAGC,OAAO,GAAG,IAAI;IAExB,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,IAAI,EAAEO,GAAG,EAAE,EAAE;MACnC,IAAIC,MAAM,GAAGT,IAAI,CAACU,GAAG,CAACH,GAAG,EAAEC,GAAG,CAAC;MAC/B,IAAIC,MAAM,KAAKJ,OAAO,EAAE;QACtBF,YAAY,EAAE;MAChB,CAAC,MAAM;QACL,IAAIA,YAAY,IAAI,CAAC,EAAED,MAAM,IAAIf,aAAa,CAACC,EAAE,IAAIe,YAAY,GAAG,CAAC,CAAC;QACtEE,OAAO,GAAGI,MAAM;QAChBN,YAAY,GAAG,CAAC;MAClB;MAEAM,MAAM,GAAGT,IAAI,CAACU,GAAG,CAACF,GAAG,EAAED,GAAG,CAAC;MAC3B,IAAIE,MAAM,KAAKH,OAAO,EAAE;QACtBF,YAAY,EAAE;MAChB,CAAC,MAAM;QACL,IAAIA,YAAY,IAAI,CAAC,EAAEF,MAAM,IAAIf,aAAa,CAACC,EAAE,IAAIgB,YAAY,GAAG,CAAC,CAAC;QACtEE,OAAO,GAAGG,MAAM;QAChBL,YAAY,GAAG,CAAC;MAClB;IACF;IAEA,IAAID,YAAY,IAAI,CAAC,EAAED,MAAM,IAAIf,aAAa,CAACC,EAAE,IAAIe,YAAY,GAAG,CAAC,CAAC;IACtE,IAAIC,YAAY,IAAI,CAAC,EAAEF,MAAM,IAAIf,aAAa,CAACC,EAAE,IAAIgB,YAAY,GAAG,CAAC,CAAC;EACxE;EAEA,OAAOF,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAzB,OAAO,CAACkC,YAAY,GAAG,SAASA,YAAYA,CAAEX,IAAI,EAAE;EAClD,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EACtB,IAAIC,MAAM,GAAG,CAAC;EAEd,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,IAAI,GAAG,CAAC,EAAEM,GAAG,EAAE,EAAE;IACvC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,IAAI,GAAG,CAAC,EAAEO,GAAG,EAAE,EAAE;MACvC,MAAMI,IAAI,GAAGZ,IAAI,CAACU,GAAG,CAACH,GAAG,EAAEC,GAAG,CAAC,GAC7BR,IAAI,CAACU,GAAG,CAACH,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,GACtBR,IAAI,CAACU,GAAG,CAACH,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,GACtBR,IAAI,CAACU,GAAG,CAACH,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,CAAC;MAE5B,IAAII,IAAI,KAAK,CAAC,IAAIA,IAAI,KAAK,CAAC,EAAEV,MAAM,EAAE;IACxC;EACF;EAEA,OAAOA,MAAM,GAAGf,aAAa,CAACE,EAAE;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAZ,OAAO,CAACoC,YAAY,GAAG,SAASA,YAAYA,CAAEb,IAAI,EAAE;EAClD,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EACtB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIY,OAAO,GAAG,CAAC;EACf,IAAIC,OAAO,GAAG,CAAC;EAEf,KAAK,IAAIR,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,IAAI,EAAEM,GAAG,EAAE,EAAE;IACnCO,OAAO,GAAGC,OAAO,GAAG,CAAC;IACrB,KAAK,IAAIP,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,IAAI,EAAEO,GAAG,EAAE,EAAE;MACnCM,OAAO,GAAKA,OAAO,IAAI,CAAC,GAAI,KAAK,GAAId,IAAI,CAACU,GAAG,CAACH,GAAG,EAAEC,GAAG,CAAC;MACvD,IAAIA,GAAG,IAAI,EAAE,KAAKM,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAEZ,MAAM,EAAE;MAEnEa,OAAO,GAAKA,OAAO,IAAI,CAAC,GAAI,KAAK,GAAIf,IAAI,CAACU,GAAG,CAACF,GAAG,EAAED,GAAG,CAAC;MACvD,IAAIC,GAAG,IAAI,EAAE,KAAKO,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAEb,MAAM,EAAE;IACrE;EACF;EAEA,OAAOA,MAAM,GAAGf,aAAa,CAACG,EAAE;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAb,OAAO,CAACuC,YAAY,GAAG,SAASA,YAAYA,CAAEhB,IAAI,EAAE;EAClD,IAAIiB,SAAS,GAAG,CAAC;EACjB,MAAMC,YAAY,GAAGlB,IAAI,CAACA,IAAI,CAACmB,MAAM;EAErC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAEH,SAAS,IAAIjB,IAAI,CAACA,IAAI,CAACoB,CAAC,CAAC;EAEhE,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAEP,SAAS,GAAG,GAAG,GAAGC,YAAY,GAAI,CAAC,CAAC,GAAG,EAAE,CAAC;EAExE,OAAOG,CAAC,GAAGlC,aAAa,CAACI,EAAE;AAC7B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,SAASA,CAAEC,WAAW,EAAEN,CAAC,EAAEO,CAAC,EAAE;EACrC,QAAQD,WAAW;IACjB,KAAKjD,OAAO,CAACC,QAAQ,CAACC,UAAU;MAAE,OAAO,CAACyC,CAAC,GAAGO,CAAC,IAAI,CAAC,KAAK,CAAC;IAC1D,KAAKlD,OAAO,CAACC,QAAQ,CAACE,UAAU;MAAE,OAAOwC,CAAC,GAAG,CAAC,KAAK,CAAC;IACpD,KAAK3C,OAAO,CAACC,QAAQ,CAACG,UAAU;MAAE,OAAO8C,CAAC,GAAG,CAAC,KAAK,CAAC;IACpD,KAAKlD,OAAO,CAACC,QAAQ,CAACI,UAAU;MAAE,OAAO,CAACsC,CAAC,GAAGO,CAAC,IAAI,CAAC,KAAK,CAAC;IAC1D,KAAKlD,OAAO,CAACC,QAAQ,CAACK,UAAU;MAAE,OAAO,CAACuC,IAAI,CAACM,KAAK,CAACR,CAAC,GAAG,CAAC,CAAC,GAAGE,IAAI,CAACM,KAAK,CAACD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IAC1F,KAAKlD,OAAO,CAACC,QAAQ,CAACM,UAAU;MAAE,OAAQoC,CAAC,GAAGO,CAAC,GAAI,CAAC,GAAIP,CAAC,GAAGO,CAAC,GAAI,CAAC,KAAK,CAAC;IACxE,KAAKlD,OAAO,CAACC,QAAQ,CAACO,UAAU;MAAE,OAAO,CAAEmC,CAAC,GAAGO,CAAC,GAAI,CAAC,GAAIP,CAAC,GAAGO,CAAC,GAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IAC9E,KAAKlD,OAAO,CAACC,QAAQ,CAACQ,UAAU;MAAE,OAAO,CAAEkC,CAAC,GAAGO,CAAC,GAAI,CAAC,GAAG,CAACP,CAAC,GAAGO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IAE9E;MAAS,MAAM,IAAIE,KAAK,CAAC,kBAAkB,GAAGH,WAAW,CAAC;EAC5D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAjD,OAAO,CAACqD,SAAS,GAAG,SAASA,SAASA,CAAEC,OAAO,EAAE/B,IAAI,EAAE;EACrD,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EAEtB,KAAK,IAAIO,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,IAAI,EAAEO,GAAG,EAAE,EAAE;IACnC,KAAK,IAAID,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,IAAI,EAAEM,GAAG,EAAE,EAAE;MACnC,IAAIP,IAAI,CAACgC,UAAU,CAACzB,GAAG,EAAEC,GAAG,CAAC,EAAE;MAC/BR,IAAI,CAACiC,GAAG,CAAC1B,GAAG,EAAEC,GAAG,EAAEiB,SAAS,CAACM,OAAO,EAAExB,GAAG,EAAEC,GAAG,CAAC,CAAC;IAClD;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA/B,OAAO,CAACyD,WAAW,GAAG,SAASA,WAAWA,CAAElC,IAAI,EAAEmC,eAAe,EAAE;EACjE,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC7D,OAAO,CAACC,QAAQ,CAAC,CAACyC,MAAM;EACxD,IAAIoB,WAAW,GAAG,CAAC;EACnB,IAAIC,YAAY,GAAGC,QAAQ;EAE3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,EAAEM,CAAC,EAAE,EAAE;IACpCP,eAAe,CAACO,CAAC,CAAC;IAClBjE,OAAO,CAACqD,SAAS,CAACY,CAAC,EAAE1C,IAAI,CAAC;;IAE1B;IACA,MAAM2C,OAAO,GACXlE,OAAO,CAACsB,YAAY,CAACC,IAAI,CAAC,GAC1BvB,OAAO,CAACkC,YAAY,CAACX,IAAI,CAAC,GAC1BvB,OAAO,CAACoC,YAAY,CAACb,IAAI,CAAC,GAC1BvB,OAAO,CAACuC,YAAY,CAAChB,IAAI,CAAC;;IAE5B;IACAvB,OAAO,CAACqD,SAAS,CAACY,CAAC,EAAE1C,IAAI,CAAC;IAE1B,IAAI2C,OAAO,GAAGH,YAAY,EAAE;MAC1BA,YAAY,GAAGG,OAAO;MACtBJ,WAAW,GAAGG,CAAC;IACjB;EACF;EAEA,OAAOH,WAAW;AACpB,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
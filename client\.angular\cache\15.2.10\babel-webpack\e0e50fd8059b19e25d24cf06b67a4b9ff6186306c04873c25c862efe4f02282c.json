{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function pairwise() {\n  return source => source.lift(new PairwiseOperator());\n}\nclass PairwiseOperator {\n  call(subscriber, source) {\n    return source.subscribe(new PairwiseSubscriber(subscriber));\n  }\n}\nclass PairwiseSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n    this.hasPrev = false;\n  }\n  _next(value) {\n    let pair;\n    if (this.hasPrev) {\n      pair = [this.prev, value];\n    } else {\n      this.hasPrev = true;\n    }\n    this.prev = value;\n    if (pair) {\n      this.destination.next(pair);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "pairwise", "source", "lift", "PairwiseOperator", "call", "subscriber", "subscribe", "PairwiseSubscriber", "constructor", "destination", "has<PERSON>rev", "_next", "value", "pair", "prev", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/pairwise.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function pairwise() {\n    return (source) => source.lift(new PairwiseOperator());\n}\nclass PairwiseOperator {\n    call(subscriber, source) {\n        return source.subscribe(new PairwiseSubscriber(subscriber));\n    }\n}\nclass PairwiseSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n        this.hasPrev = false;\n    }\n    _next(value) {\n        let pair;\n        if (this.hasPrev) {\n            pair = [this.prev, value];\n        }\n        else {\n            this.hasPrev = true;\n        }\n        this.prev = value;\n        if (pair) {\n            this.destination.next(pair);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,QAAQA,CAAA,EAAG;EACvB,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,gBAAgB,CAAC,CAAC,CAAC;AAC1D;AACA,MAAMA,gBAAgB,CAAC;EACnBC,IAAIA,CAACC,UAAU,EAAEJ,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACK,SAAS,CAAC,IAAIC,kBAAkB,CAACF,UAAU,CAAC,CAAC;EAC/D;AACJ;AACA,MAAME,kBAAkB,SAASR,UAAU,CAAC;EACxCS,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIC,IAAI;IACR,IAAI,IAAI,CAACH,OAAO,EAAE;MACdG,IAAI,GAAG,CAAC,IAAI,CAACC,IAAI,EAAEF,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACF,OAAO,GAAG,IAAI;IACvB;IACA,IAAI,CAACI,IAAI,GAAGF,KAAK;IACjB,IAAIC,IAAI,EAAE;MACN,IAAI,CAACJ,WAAW,CAACM,IAAI,CAACF,IAAI,CAAC;IAC/B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
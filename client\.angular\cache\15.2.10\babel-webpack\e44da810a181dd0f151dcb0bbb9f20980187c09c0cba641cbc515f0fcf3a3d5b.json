{"ast": null, "code": "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, ElementRef, Injectable, NgModule, NgZone, Inject, Input, ɵɵdefineInjectable, ɵɵinject } from '@angular/core';\nimport { BaseDirective2, StyleBuilder, StyleUtils, MediaMarshaller, CoreModule, LAYOUT_CONFIG, validateBasis } from '@angular/flex-layout/core';\nimport { Directionality, BidiModule } from '@angular/cdk/bidi';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * @fileoverview added by tsickle\n * Generated from: utils/layout-validator.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @type {?}\n */\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/flex-layout/core';\nimport * as ɵngcc2 from '@angular/cdk/bidi';\nconst INLINE = 'inline';\n/** @type {?} */\nconst LAYOUT_VALUES = ['row', 'column', 'row-reverse', 'column-reverse'];\n/**\n * Validate the direction|'direction wrap' value and then update the host's inline flexbox styles\n * @param {?} value\n * @return {?}\n */\nfunction buildLayoutCSS(value) {\n  let [direction, wrap, isInline] = validateValue(value);\n  return buildCSS(direction, wrap, isInline);\n}\n/**\n * Validate the value to be one of the acceptable value options\n * Use default fallback of 'row'\n * @param {?} value\n * @return {?}\n */\nfunction validateValue(value) {\n  value = value ? value.toLowerCase() : '';\n  let [direction, wrap, inline] = value.split(' ');\n  // First value must be the `flex-direction`\n  if (!LAYOUT_VALUES.find(\n  /**\n  * @param {?} x\n  * @return {?}\n  */\n  x => x === direction)) {\n    direction = LAYOUT_VALUES[0];\n  }\n  if (wrap === INLINE) {\n    wrap = inline !== INLINE ? inline : '';\n    inline = INLINE;\n  }\n  return [direction, validateWrapValue(wrap), !!inline];\n}\n/**\n * Determine if the validated, flex-direction value specifies\n * a horizontal/row flow.\n * @param {?} value\n * @return {?}\n */\nfunction isFlowHorizontal(value) {\n  let [flow] = validateValue(value);\n  return flow.indexOf('row') > -1;\n}\n/**\n * Convert layout-wrap='<value>' to expected flex-wrap style\n * @param {?} value\n * @return {?}\n */\nfunction validateWrapValue(value) {\n  if (!!value) {\n    switch (value.toLowerCase()) {\n      case 'reverse':\n      case 'wrap-reverse':\n      case 'reverse-wrap':\n        value = 'wrap-reverse';\n        break;\n      case 'no':\n      case 'none':\n      case 'nowrap':\n        value = 'nowrap';\n        break;\n      // All other values fallback to 'wrap'\n      default:\n        value = 'wrap';\n        break;\n    }\n  }\n  return value;\n}\n/**\n * Build the CSS that should be assigned to the element instance\n * BUG:\n *   1) min-height on a column flex container won’t apply to its flex item children in IE 10-11.\n *      Use height instead if possible; height : <xxx>vh;\n *\n *  This way any padding or border specified on the child elements are\n *  laid out and drawn inside that element's specified width and height.\n * @param {?} direction\n * @param {?=} wrap\n * @param {?=} inline\n * @return {?}\n */\nfunction buildCSS(direction, wrap = null, inline = false) {\n  return {\n    'display': inline ? 'inline-flex' : 'flex',\n    'box-sizing': 'border-box',\n    'flex-direction': direction,\n    'flex-wrap': !!wrap ? wrap : null\n  };\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/layout/layout.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass LayoutStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} input\n   * @return {?}\n   */\n  buildStyles(input) {\n    return buildLayoutCSS(input);\n  }\n}\nLayoutStyleBuilder.ɵfac = /*@__PURE__*/function () {\n  let ɵLayoutStyleBuilder_BaseFactory;\n  return function LayoutStyleBuilder_Factory(t) {\n    return (ɵLayoutStyleBuilder_BaseFactory || (ɵLayoutStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(LayoutStyleBuilder)))(t || LayoutStyleBuilder);\n  };\n}();\n/** @nocollapse */\nLayoutStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function LayoutStyleBuilder_Factory() {\n    return new LayoutStyleBuilder();\n  },\n  token: LayoutStyleBuilder,\n  providedIn: \"root\"\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst inputs = ['fxLayout', 'fxLayout.xs', 'fxLayout.sm', 'fxLayout.md', 'fxLayout.lg', 'fxLayout.xl', 'fxLayout.lt-sm', 'fxLayout.lt-md', 'fxLayout.lt-lg', 'fxLayout.lt-xl', 'fxLayout.gt-xs', 'fxLayout.gt-sm', 'fxLayout.gt-md', 'fxLayout.gt-lg'];\n/** @type {?} */\nconst selector = `\n  [fxLayout], [fxLayout.xs], [fxLayout.sm], [fxLayout.md],\n  [fxLayout.lg], [fxLayout.xl], [fxLayout.lt-sm], [fxLayout.lt-md],\n  [fxLayout.lt-lg], [fxLayout.lt-xl], [fxLayout.gt-xs], [fxLayout.gt-sm],\n  [fxLayout.gt-md], [fxLayout.gt-lg]\n`;\n/**\n * 'layout' flexbox styling directive\n * Defines the positioning flow direction for the child elements: row or column\n * Optional values: column or row (default)\n * @see https://css-tricks.com/almanac/properties/f/flex-direction/\n *\n */\nclass LayoutDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} styleUtils\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, styleUtils, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.DIRECTIVE_KEY = 'layout';\n    this.styleCache = layoutCache;\n    this.init();\n  }\n}\nLayoutDirective.ɵfac = function LayoutDirective_Factory(t) {\n  return new (t || LayoutDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LayoutStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nLayoutDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: LayoutDirective,\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nLayoutDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: StyleUtils\n}, {\n  type: LayoutStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: LayoutStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, null);\n})();\nclass DefaultLayoutDirective extends LayoutDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs;\n  }\n}\nDefaultLayoutDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultLayoutDirective_BaseFactory;\n  return function DefaultLayoutDirective_Factory(t) {\n    return (ɵDefaultLayoutDirective_BaseFactory || (ɵDefaultLayoutDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultLayoutDirective)))(t || DefaultLayoutDirective);\n  };\n}();\nDefaultLayoutDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultLayoutDirective,\n  selectors: [[\"\", \"fxLayout\", \"\"], [\"\", \"fxLayout.xs\", \"\"], [\"\", \"fxLayout.sm\", \"\"], [\"\", \"fxLayout.md\", \"\"], [\"\", \"fxLayout.lg\", \"\"], [\"\", \"fxLayout.xl\", \"\"], [\"\", \"fxLayout.lt-sm\", \"\"], [\"\", \"fxLayout.lt-md\", \"\"], [\"\", \"fxLayout.lt-lg\", \"\"], [\"\", \"fxLayout.lt-xl\", \"\"], [\"\", \"fxLayout.gt-xs\", \"\"], [\"\", \"fxLayout.gt-sm\", \"\"], [\"\", \"fxLayout.gt-md\", \"\"], [\"\", \"fxLayout.gt-lg\", \"\"]],\n  inputs: {\n    fxLayout: \"fxLayout\",\n    \"fxLayout.xs\": \"fxLayout.xs\",\n    \"fxLayout.sm\": \"fxLayout.sm\",\n    \"fxLayout.md\": \"fxLayout.md\",\n    \"fxLayout.lg\": \"fxLayout.lg\",\n    \"fxLayout.xl\": \"fxLayout.xl\",\n    \"fxLayout.lt-sm\": \"fxLayout.lt-sm\",\n    \"fxLayout.lt-md\": \"fxLayout.lt-md\",\n    \"fxLayout.lt-lg\": \"fxLayout.lt-lg\",\n    \"fxLayout.lt-xl\": \"fxLayout.lt-xl\",\n    \"fxLayout.gt-xs\": \"fxLayout.gt-xs\",\n    \"fxLayout.gt-sm\": \"fxLayout.gt-sm\",\n    \"fxLayout.gt-md\": \"fxLayout.gt-md\",\n    \"fxLayout.gt-lg\": \"fxLayout.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultLayoutDirective, [{\n    type: Directive,\n    args: [{\n      selector,\n      inputs\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst layoutCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/layout-gap/layout-gap.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst CLEAR_MARGIN_CSS = {\n  'margin-left': null,\n  'margin-right': null,\n  'margin-top': null,\n  'margin-bottom': null\n};\nclass LayoutGapStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} _styler\n   */\n  constructor(_styler) {\n    super();\n    this._styler = _styler;\n  }\n  /**\n   * @param {?} gapValue\n   * @param {?} parent\n   * @return {?}\n   */\n  buildStyles(gapValue, parent) {\n    if (gapValue.endsWith(GRID_SPECIFIER)) {\n      gapValue = gapValue.slice(0, gapValue.indexOf(GRID_SPECIFIER));\n      // Add the margin to the host element\n      return buildGridMargin(gapValue, parent.directionality);\n    } else {\n      return {};\n    }\n  }\n  /**\n   * @param {?} gapValue\n   * @param {?} _styles\n   * @param {?} parent\n   * @return {?}\n   */\n  sideEffect(gapValue, _styles, parent) {\n    /** @type {?} */\n    const items = parent.items;\n    if (gapValue.endsWith(GRID_SPECIFIER)) {\n      gapValue = gapValue.slice(0, gapValue.indexOf(GRID_SPECIFIER));\n      // For each `element` children, set the padding\n      /** @type {?} */\n      const paddingStyles = buildGridPadding(gapValue, parent.directionality);\n      this._styler.applyStyleToElements(paddingStyles, parent.items);\n    } else {\n      /** @type {?} */\n      const lastItem = /** @type {?} */items.pop();\n      // For each `element` children EXCEPT the last,\n      // set the margin right/bottom styles...\n      /** @type {?} */\n      const gapCss = buildGapCSS(gapValue, parent);\n      this._styler.applyStyleToElements(gapCss, items);\n      // Clear all gaps for all visible elements\n      this._styler.applyStyleToElements(CLEAR_MARGIN_CSS, [lastItem]);\n    }\n  }\n}\nLayoutGapStyleBuilder.ɵfac = function LayoutGapStyleBuilder_Factory(t) {\n  return new (t || LayoutGapStyleBuilder)(ɵngcc0.ɵɵinject(ɵngcc1.StyleUtils));\n};\n/** @nocollapse */\nLayoutGapStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function LayoutGapStyleBuilder_Factory() {\n    return new LayoutGapStyleBuilder(ɵɵinject(StyleUtils));\n  },\n  token: LayoutGapStyleBuilder,\n  providedIn: \"root\"\n});\n/** @nocollapse */\nLayoutGapStyleBuilder.ctorParameters = () => [{\n  type: StyleUtils\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutGapStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: ɵngcc1.StyleUtils\n    }];\n  }, null);\n})();\n/** @type {?} */\nconst inputs$1 = ['fxLayoutGap', 'fxLayoutGap.xs', 'fxLayoutGap.sm', 'fxLayoutGap.md', 'fxLayoutGap.lg', 'fxLayoutGap.xl', 'fxLayoutGap.lt-sm', 'fxLayoutGap.lt-md', 'fxLayoutGap.lt-lg', 'fxLayoutGap.lt-xl', 'fxLayoutGap.gt-xs', 'fxLayoutGap.gt-sm', 'fxLayoutGap.gt-md', 'fxLayoutGap.gt-lg'];\n/** @type {?} */\nconst selector$1 = `\n  [fxLayoutGap], [fxLayoutGap.xs], [fxLayoutGap.sm], [fxLayoutGap.md],\n  [fxLayoutGap.lg], [fxLayoutGap.xl], [fxLayoutGap.lt-sm], [fxLayoutGap.lt-md],\n  [fxLayoutGap.lt-lg], [fxLayoutGap.lt-xl], [fxLayoutGap.gt-xs], [fxLayoutGap.gt-sm],\n  [fxLayoutGap.gt-md], [fxLayoutGap.gt-lg]\n`;\n/**\n * 'layout-padding' styling directive\n *  Defines padding of child elements in a layout container\n */\nclass LayoutGapDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} zone\n   * @param {?} directionality\n   * @param {?} styleUtils\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, zone, directionality, styleUtils, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.zone = zone;\n    this.directionality = directionality;\n    this.styleUtils = styleUtils;\n    this.layout = 'row'; // default flex-direction\n    // default flex-direction\n    this.DIRECTIVE_KEY = 'layout-gap';\n    this.observerSubject = new Subject();\n    /** @type {?} */\n    const extraTriggers = [this.directionality.change, this.observerSubject.asObservable()];\n    this.init(extraTriggers);\n    this.marshal.trackValue(this.nativeElement, 'layout').pipe(takeUntil(this.destroySubject)).subscribe(this.onLayoutChange.bind(this));\n  }\n  /**\n   * Special accessor to query for all child 'element' nodes regardless of type, class, etc\n   * @protected\n   * @return {?}\n   */\n  get childrenNodes() {\n    /** @type {?} */\n    const obj = this.nativeElement.children;\n    /** @type {?} */\n    const buffer = [];\n    // iterate backwards ensuring that length is an UInt32\n    for (let i = obj.length; i--;) {\n      buffer[i] = obj[i];\n    }\n    return buffer;\n  }\n  // *********************************************\n  // Lifecycle Methods\n  // *********************************************\n  /**\n   * @return {?}\n   */\n  ngAfterContentInit() {\n    this.buildChildObservable();\n    this.triggerUpdate();\n  }\n  /**\n   * @return {?}\n   */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this.observer) {\n      this.observer.disconnect();\n    }\n  }\n  // *********************************************\n  // Protected methods\n  // *********************************************\n  /**\n   * Cache the parent container 'flex-direction' and update the 'margin' styles\n   * @protected\n   * @param {?} matcher\n   * @return {?}\n   */\n  onLayoutChange(matcher) {\n    /** @type {?} */\n    const layout = matcher.value;\n    // Make sure to filter out 'wrap' option\n    /** @type {?} */\n    const direction = layout.split(' ');\n    this.layout = direction[0];\n    if (!LAYOUT_VALUES.find(\n    /**\n    * @param {?} x\n    * @return {?}\n    */\n    x => x === this.layout)) {\n      this.layout = 'row';\n    }\n    this.triggerUpdate();\n  }\n  /**\n   *\n   * @protected\n   * @param {?} value\n   * @return {?}\n   */\n  updateWithValue(value) {\n    // Gather all non-hidden Element nodes\n    /** @type {?} */\n    const items = this.childrenNodes.filter(\n    /**\n    * @param {?} el\n    * @return {?}\n    */\n    el => el.nodeType === 1 && this.willDisplay(el)).sort(\n    /**\n    * @param {?} a\n    * @param {?} b\n    * @return {?}\n    */\n    (a, b) => {\n      /** @type {?} */\n      const orderA = +this.styler.lookupStyle(a, 'order');\n      /** @type {?} */\n      const orderB = +this.styler.lookupStyle(b, 'order');\n      if (isNaN(orderA) || isNaN(orderB) || orderA === orderB) {\n        return 0;\n      } else {\n        return orderA > orderB ? 1 : -1;\n      }\n    });\n    if (items.length > 0) {\n      /** @type {?} */\n      const directionality = this.directionality.value;\n      /** @type {?} */\n      const layout = this.layout;\n      if (layout === 'row' && directionality === 'rtl') {\n        this.styleCache = layoutGapCacheRowRtl;\n      } else if (layout === 'row' && directionality !== 'rtl') {\n        this.styleCache = layoutGapCacheRowLtr;\n      } else if (layout === 'column' && directionality === 'rtl') {\n        this.styleCache = layoutGapCacheColumnRtl;\n      } else if (layout === 'column' && directionality !== 'rtl') {\n        this.styleCache = layoutGapCacheColumnLtr;\n      }\n      this.addStyles(value, {\n        directionality,\n        items,\n        layout\n      });\n    }\n  }\n  /**\n   * We need to override clearStyles because in most cases mru isn't populated\n   * @protected\n   * @return {?}\n   */\n  clearStyles() {\n    /** @type {?} */\n    const gridMode = Object.keys(this.mru).length > 0;\n    /** @type {?} */\n    const childrenStyle = gridMode ? 'padding' : getMarginType(this.directionality.value, this.layout);\n    // If there are styles on the parent remove them\n    if (gridMode) {\n      super.clearStyles();\n    }\n    // Then remove the children styles too\n    this.styleUtils.applyStyleToElements({\n      [childrenStyle]: ''\n    }, this.childrenNodes);\n  }\n  /**\n   * Determine if an element will show or hide based on current activation\n   * @protected\n   * @param {?} source\n   * @return {?}\n   */\n  willDisplay(source) {\n    /** @type {?} */\n    const value = this.marshal.getValue(source, 'show-hide');\n    return value === true || value === undefined && this.styleUtils.lookupStyle(source, 'display') !== 'none';\n  }\n  /**\n   * @protected\n   * @return {?}\n   */\n  buildChildObservable() {\n    this.zone.runOutsideAngular(\n    /**\n    * @return {?}\n    */\n    () => {\n      if (typeof MutationObserver !== 'undefined') {\n        this.observer = new MutationObserver(\n        /**\n        * @param {?} mutations\n        * @return {?}\n        */\n        mutations => {\n          /** @type {?} */\n          const validatedChanges =\n          /**\n          * @param {?} it\n          * @return {?}\n          */\n          it => {\n            return it.addedNodes && it.addedNodes.length > 0 || it.removedNodes && it.removedNodes.length > 0;\n          };\n          // update gap styles only for child 'added' or 'removed' events\n          if (mutations.some(validatedChanges)) {\n            this.observerSubject.next();\n          }\n        });\n        this.observer.observe(this.nativeElement, {\n          childList: true\n        });\n      }\n    });\n  }\n}\nLayoutGapDirective.ɵfac = function LayoutGapDirective_Factory(t) {\n  return new (t || LayoutGapDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone), ɵngcc0.ɵɵdirectiveInject(ɵngcc2.Directionality), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LayoutGapStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nLayoutGapDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: LayoutGapDirective,\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nLayoutGapDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: NgZone\n}, {\n  type: Directionality\n}, {\n  type: StyleUtils\n}, {\n  type: LayoutGapStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutGapDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc0.NgZone\n    }, {\n      type: ɵngcc2.Directionality\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: LayoutGapStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, null);\n})();\nclass DefaultLayoutGapDirective extends LayoutGapDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$1;\n  }\n}\nDefaultLayoutGapDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultLayoutGapDirective_BaseFactory;\n  return function DefaultLayoutGapDirective_Factory(t) {\n    return (ɵDefaultLayoutGapDirective_BaseFactory || (ɵDefaultLayoutGapDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultLayoutGapDirective)))(t || DefaultLayoutGapDirective);\n  };\n}();\nDefaultLayoutGapDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultLayoutGapDirective,\n  selectors: [[\"\", \"fxLayoutGap\", \"\"], [\"\", \"fxLayoutGap.xs\", \"\"], [\"\", \"fxLayoutGap.sm\", \"\"], [\"\", \"fxLayoutGap.md\", \"\"], [\"\", \"fxLayoutGap.lg\", \"\"], [\"\", \"fxLayoutGap.xl\", \"\"], [\"\", \"fxLayoutGap.lt-sm\", \"\"], [\"\", \"fxLayoutGap.lt-md\", \"\"], [\"\", \"fxLayoutGap.lt-lg\", \"\"], [\"\", \"fxLayoutGap.lt-xl\", \"\"], [\"\", \"fxLayoutGap.gt-xs\", \"\"], [\"\", \"fxLayoutGap.gt-sm\", \"\"], [\"\", \"fxLayoutGap.gt-md\", \"\"], [\"\", \"fxLayoutGap.gt-lg\", \"\"]],\n  inputs: {\n    fxLayoutGap: \"fxLayoutGap\",\n    \"fxLayoutGap.xs\": \"fxLayoutGap.xs\",\n    \"fxLayoutGap.sm\": \"fxLayoutGap.sm\",\n    \"fxLayoutGap.md\": \"fxLayoutGap.md\",\n    \"fxLayoutGap.lg\": \"fxLayoutGap.lg\",\n    \"fxLayoutGap.xl\": \"fxLayoutGap.xl\",\n    \"fxLayoutGap.lt-sm\": \"fxLayoutGap.lt-sm\",\n    \"fxLayoutGap.lt-md\": \"fxLayoutGap.lt-md\",\n    \"fxLayoutGap.lt-lg\": \"fxLayoutGap.lt-lg\",\n    \"fxLayoutGap.lt-xl\": \"fxLayoutGap.lt-xl\",\n    \"fxLayoutGap.gt-xs\": \"fxLayoutGap.gt-xs\",\n    \"fxLayoutGap.gt-sm\": \"fxLayoutGap.gt-sm\",\n    \"fxLayoutGap.gt-md\": \"fxLayoutGap.gt-md\",\n    \"fxLayoutGap.gt-lg\": \"fxLayoutGap.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultLayoutGapDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$1,\n      inputs: inputs$1\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst layoutGapCacheRowRtl = new Map();\n/** @type {?} */\nconst layoutGapCacheColumnRtl = new Map();\n/** @type {?} */\nconst layoutGapCacheRowLtr = new Map();\n/** @type {?} */\nconst layoutGapCacheColumnLtr = new Map();\n/** @type {?} */\nconst GRID_SPECIFIER = ' grid';\n/**\n * @param {?} value\n * @param {?} directionality\n * @return {?}\n */\nfunction buildGridPadding(value, directionality) {\n  const [between, below] = value.split(' ');\n  /** @type {?} */\n  const bottom = below || between;\n  /** @type {?} */\n  let paddingRight = '0px';\n  /** @type {?} */\n  let paddingBottom = bottom;\n  /** @type {?} */\n  let paddingLeft = '0px';\n  if (directionality === 'rtl') {\n    paddingLeft = between;\n  } else {\n    paddingRight = between;\n  }\n  return {\n    'padding': `0px ${paddingRight} ${paddingBottom} ${paddingLeft}`\n  };\n}\n/**\n * @param {?} value\n * @param {?} directionality\n * @return {?}\n */\nfunction buildGridMargin(value, directionality) {\n  const [between, below] = value.split(' ');\n  /** @type {?} */\n  const bottom = below || between;\n  /** @type {?} */\n  const minus =\n  /**\n  * @param {?} str\n  * @return {?}\n  */\n  str => `-${str}`;\n  /** @type {?} */\n  let marginRight = '0px';\n  /** @type {?} */\n  let marginBottom = minus(bottom);\n  /** @type {?} */\n  let marginLeft = '0px';\n  if (directionality === 'rtl') {\n    marginLeft = minus(between);\n  } else {\n    marginRight = minus(between);\n  }\n  return {\n    'margin': `0px ${marginRight} ${marginBottom} ${marginLeft}`\n  };\n}\n/**\n * @param {?} directionality\n * @param {?} layout\n * @return {?}\n */\nfunction getMarginType(directionality, layout) {\n  switch (layout) {\n    case 'column':\n      return 'margin-bottom';\n    case 'column-reverse':\n      return 'margin-top';\n    case 'row':\n      return directionality === 'rtl' ? 'margin-left' : 'margin-right';\n    case 'row-reverse':\n      return directionality === 'rtl' ? 'margin-right' : 'margin-left';\n    default:\n      return directionality === 'rtl' ? 'margin-left' : 'margin-right';\n  }\n}\n/**\n * @param {?} gapValue\n * @param {?} parent\n * @return {?}\n */\nfunction buildGapCSS(gapValue, parent) {\n  /** @type {?} */\n  const key = getMarginType(parent.directionality, parent.layout);\n  /** @type {?} */\n  const margins = Object.assign({}, CLEAR_MARGIN_CSS);\n  margins[key] = gapValue;\n  return margins;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: utils/object-extend.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * Extends an object with the *enumerable* and *own* properties of one or more source objects,\n * similar to Object.assign.\n *\n * @param {?} dest The object which will have properties copied to it.\n * @param {...?} sources The source objects from which properties will be copied.\n * @return {?}\n */\nfunction extendObject(dest, ...sources) {\n  if (dest == null) {\n    throw TypeError('Cannot convert undefined or null to object');\n  }\n  for (let source of sources) {\n    if (source != null) {\n      for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n          dest[key] = source[key];\n        }\n      }\n    }\n  }\n  return dest;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex/flex.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} layoutConfig\n   */\n  constructor(layoutConfig) {\n    super();\n    this.layoutConfig = layoutConfig;\n  }\n  /**\n   * @param {?} input\n   * @param {?} parent\n   * @return {?}\n   */\n  buildStyles(input, parent) {\n    let [grow, shrink, ...basisParts] = input.split(' ');\n    /** @type {?} */\n    let basis = basisParts.join(' ');\n    // The flex-direction of this element's flex container. Defaults to 'row'.\n    /** @type {?} */\n    const direction = parent.direction.indexOf('column') > -1 ? 'column' : 'row';\n    /** @type {?} */\n    const max = isFlowHorizontal(direction) ? 'max-width' : 'max-height';\n    /** @type {?} */\n    const min = isFlowHorizontal(direction) ? 'min-width' : 'min-height';\n    /** @type {?} */\n    const hasCalc = String(basis).indexOf('calc') > -1;\n    /** @type {?} */\n    const usingCalc = hasCalc || basis === 'auto';\n    /** @type {?} */\n    const isPercent = String(basis).indexOf('%') > -1 && !hasCalc;\n    /** @type {?} */\n    const hasUnits = String(basis).indexOf('px') > -1 || String(basis).indexOf('rem') > -1 || String(basis).indexOf('em') > -1 || String(basis).indexOf('vw') > -1 || String(basis).indexOf('vh') > -1;\n    /** @type {?} */\n    let isValue = hasCalc || hasUnits;\n    grow = grow == '0' ? 0 : grow;\n    shrink = shrink == '0' ? 0 : shrink;\n    // make box inflexible when shrink and grow are both zero\n    // should not set a min when the grow is zero\n    // should not set a max when the shrink is zero\n    /** @type {?} */\n    const isFixed = !grow && !shrink;\n    /** @type {?} */\n    let css = {};\n    // flex-basis allows you to specify the initial/starting main-axis size of the element,\n    // before anything else is computed. It can either be a percentage or an absolute value.\n    // It is, however, not the breaking point for flex-grow/shrink properties\n    //\n    // flex-grow can be seen as this:\n    //   0: Do not stretch. Either size to element's content width, or obey 'flex-basis'.\n    //   1: (Default value). Stretch; will be the same size to all other flex items on\n    //       the same row since they have a default value of 1.\n    //   ≥2 (integer n): Stretch. Will be n times the size of other elements\n    //      with 'flex-grow: 1' on the same row.\n    // Use `null` to clear existing styles.\n    /** @type {?} */\n    const clearStyles = {\n      'max-width': null,\n      'max-height': null,\n      'min-width': null,\n      'min-height': null\n    };\n    switch (basis || '') {\n      case '':\n        /** @type {?} */\n        const useColumnBasisZero = this.layoutConfig.useColumnBasisZero !== false;\n        basis = direction === 'row' ? '0%' : useColumnBasisZero ? '0.000000001px' : 'auto';\n        break;\n      case 'initial': // default\n      case 'nogrow':\n        grow = 0;\n        basis = 'auto';\n        break;\n      case 'grow':\n        basis = '100%';\n        break;\n      case 'noshrink':\n        shrink = 0;\n        basis = 'auto';\n        break;\n      case 'auto':\n        break;\n      case 'none':\n        grow = 0;\n        shrink = 0;\n        basis = 'auto';\n        break;\n      default:\n        // Defaults to percentage sizing unless `px` is explicitly set\n        if (!isValue && !isPercent && !isNaN( /** @type {?} */basis)) {\n          basis = basis + '%';\n        }\n        // Fix for issue 280\n        if (basis === '0%') {\n          isValue = true;\n        }\n        if (basis === '0px') {\n          basis = '0%';\n        }\n        // fix issue #5345\n        if (hasCalc) {\n          css = extendObject(clearStyles, {\n            'flex-grow': grow,\n            'flex-shrink': shrink,\n            'flex-basis': isValue ? basis : '100%'\n          });\n        } else {\n          css = extendObject(clearStyles, {\n            'flex': `${grow} ${shrink} ${isValue ? basis : '100%'}`\n          });\n        }\n        break;\n    }\n    if (!(css['flex'] || css['flex-grow'])) {\n      if (hasCalc) {\n        css = extendObject(clearStyles, {\n          'flex-grow': grow,\n          'flex-shrink': shrink,\n          'flex-basis': basis\n        });\n      } else {\n        css = extendObject(clearStyles, {\n          'flex': `${grow} ${shrink} ${basis}`\n        });\n      }\n    }\n    // Fix for issues 277, 534, and 728\n    if (basis !== '0%' && basis !== '0px' && basis !== '0.000000001px' && basis !== 'auto') {\n      css[min] = isFixed || isValue && grow ? basis : null;\n      css[max] = isFixed || !usingCalc && shrink ? basis : null;\n    }\n    // Fix for issue 528\n    if (!css[min] && !css[max]) {\n      if (hasCalc) {\n        css = extendObject(clearStyles, {\n          'flex-grow': grow,\n          'flex-shrink': shrink,\n          'flex-basis': basis\n        });\n      } else {\n        css = extendObject(clearStyles, {\n          'flex': `${grow} ${shrink} ${basis}`\n        });\n      }\n    } else {\n      // Fix for issue 660\n      if (parent.hasWrap) {\n        css[hasCalc ? 'flex-basis' : 'flex'] = css[max] ? hasCalc ? css[max] : `${grow} ${shrink} ${css[max]}` : hasCalc ? css[min] : `${grow} ${shrink} ${css[min]}`;\n      }\n    }\n    return (/** @type {?} */extendObject(css, {\n        'box-sizing': 'border-box'\n      })\n    );\n  }\n}\nFlexStyleBuilder.ɵfac = function FlexStyleBuilder_Factory(t) {\n  return new (t || FlexStyleBuilder)(ɵngcc0.ɵɵinject(LAYOUT_CONFIG));\n};\n/** @nocollapse */\nFlexStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function FlexStyleBuilder_Factory() {\n    return new FlexStyleBuilder(ɵɵinject(LAYOUT_CONFIG));\n  },\n  token: FlexStyleBuilder,\n  providedIn: \"root\"\n});\n/** @nocollapse */\nFlexStyleBuilder.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [LAYOUT_CONFIG]\n  }]\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LAYOUT_CONFIG]\n      }]\n    }];\n  }, null);\n})();\n/** @type {?} */\nconst inputs$2 = ['fxFlex', 'fxFlex.xs', 'fxFlex.sm', 'fxFlex.md', 'fxFlex.lg', 'fxFlex.xl', 'fxFlex.lt-sm', 'fxFlex.lt-md', 'fxFlex.lt-lg', 'fxFlex.lt-xl', 'fxFlex.gt-xs', 'fxFlex.gt-sm', 'fxFlex.gt-md', 'fxFlex.gt-lg'];\n/** @type {?} */\nconst selector$2 = `\n  [fxFlex], [fxFlex.xs], [fxFlex.sm], [fxFlex.md],\n  [fxFlex.lg], [fxFlex.xl], [fxFlex.lt-sm], [fxFlex.lt-md],\n  [fxFlex.lt-lg], [fxFlex.lt-xl], [fxFlex.gt-xs], [fxFlex.gt-sm],\n  [fxFlex.gt-md], [fxFlex.gt-lg]\n`;\n/**\n * Directive to control the size of a flex item using flex-basis, flex-grow, and flex-shrink.\n * Corresponds to the css `flex` shorthand property.\n *\n * @see https://css-tricks.com/snippets/css/a-guide-to-flexbox/\n */\nclass FlexDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} styleUtils\n   * @param {?} layoutConfig\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, styleUtils, layoutConfig, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.layoutConfig = layoutConfig;\n    this.marshal = marshal;\n    this.DIRECTIVE_KEY = 'flex';\n    this.direction = undefined;\n    this.wrap = undefined;\n    this.flexGrow = '1';\n    this.flexShrink = '1';\n    this.init();\n  }\n  /**\n   * @return {?}\n   */\n  get shrink() {\n    return this.flexShrink;\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n  set shrink(value) {\n    this.flexShrink = value || '1';\n    this.triggerReflow();\n  }\n  /**\n   * @return {?}\n   */\n  get grow() {\n    return this.flexGrow;\n  }\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n  set grow(value) {\n    this.flexGrow = value || '1';\n    this.triggerReflow();\n  }\n  /**\n   * @return {?}\n   */\n  ngOnInit() {\n    if (this.parentElement) {\n      this.marshal.trackValue(this.parentElement, 'layout').pipe(takeUntil(this.destroySubject)).subscribe(this.onLayoutChange.bind(this));\n      this.marshal.trackValue(this.nativeElement, 'layout-align').pipe(takeUntil(this.destroySubject)).subscribe(this.triggerReflow.bind(this));\n    }\n  }\n  /**\n   * Caches the parent container's 'flex-direction' and updates the element's style.\n   * Used as a handler for layout change events from the parent flex container.\n   * @protected\n   * @param {?} matcher\n   * @return {?}\n   */\n  onLayoutChange(matcher) {\n    /** @type {?} */\n    const layout = matcher.value;\n    /** @type {?} */\n    const layoutParts = layout.split(' ');\n    this.direction = layoutParts[0];\n    this.wrap = layoutParts[1] !== undefined && layoutParts[1] === 'wrap';\n    this.triggerUpdate();\n  }\n  /**\n   * Input to this is exclusively the basis input value\n   * @protected\n   * @param {?} value\n   * @return {?}\n   */\n  updateWithValue(value) {\n    /** @type {?} */\n    const addFlexToParent = this.layoutConfig.addFlexToParent !== false;\n    if (this.direction === undefined) {\n      this.direction = this.getFlexFlowDirection( /** @type {?} */this.parentElement, addFlexToParent);\n    }\n    if (this.wrap === undefined) {\n      this.wrap = this.hasWrap( /** @type {?} */this.parentElement);\n    }\n    /** @type {?} */\n    const direction = this.direction;\n    /** @type {?} */\n    const isHorizontal = direction.startsWith('row');\n    /** @type {?} */\n    const hasWrap = this.wrap;\n    if (isHorizontal && hasWrap) {\n      this.styleCache = flexRowWrapCache;\n    } else if (isHorizontal && !hasWrap) {\n      this.styleCache = flexRowCache;\n    } else if (!isHorizontal && hasWrap) {\n      this.styleCache = flexColumnWrapCache;\n    } else if (!isHorizontal && !hasWrap) {\n      this.styleCache = flexColumnCache;\n    }\n    /** @type {?} */\n    const basis = String(value).replace(';', '');\n    /** @type {?} */\n    const parts = validateBasis(basis, this.flexGrow, this.flexShrink);\n    this.addStyles(parts.join(' '), {\n      direction,\n      hasWrap\n    });\n  }\n  /**\n   * Trigger a style reflow, usually based on a shrink/grow input event\n   * @protected\n   * @return {?}\n   */\n  triggerReflow() {\n    /** @type {?} */\n    const activatedValue = this.activatedValue;\n    if (activatedValue !== undefined) {\n      /** @type {?} */\n      const parts = validateBasis(activatedValue + '', this.flexGrow, this.flexShrink);\n      this.marshal.updateElement(this.nativeElement, this.DIRECTIVE_KEY, parts.join(' '));\n    }\n  }\n}\nFlexDirective.ɵfac = function FlexDirective_Factory(t) {\n  return new (t || FlexDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LAYOUT_CONFIG), ɵngcc0.ɵɵdirectiveInject(FlexStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nFlexDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: FlexDirective,\n  inputs: {\n    shrink: [\"fxShrink\", \"shrink\"],\n    grow: [\"fxGrow\", \"grow\"]\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nFlexDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: StyleUtils\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [LAYOUT_CONFIG]\n  }]\n}, {\n  type: FlexStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\nFlexDirective.propDecorators = {\n  shrink: [{\n    type: Input,\n    args: ['fxShrink']\n  }],\n  grow: [{\n    type: Input,\n    args: ['fxGrow']\n  }]\n};\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LAYOUT_CONFIG]\n      }]\n    }, {\n      type: FlexStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, {\n    shrink: [{\n      type: Input,\n      args: ['fxShrink']\n    }],\n    grow: [{\n      type: Input,\n      args: ['fxGrow']\n    }]\n  });\n})();\nclass DefaultFlexDirective extends FlexDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$2;\n  }\n}\nDefaultFlexDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultFlexDirective_BaseFactory;\n  return function DefaultFlexDirective_Factory(t) {\n    return (ɵDefaultFlexDirective_BaseFactory || (ɵDefaultFlexDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexDirective)))(t || DefaultFlexDirective);\n  };\n}();\nDefaultFlexDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultFlexDirective,\n  selectors: [[\"\", \"fxFlex\", \"\"], [\"\", \"fxFlex.xs\", \"\"], [\"\", \"fxFlex.sm\", \"\"], [\"\", \"fxFlex.md\", \"\"], [\"\", \"fxFlex.lg\", \"\"], [\"\", \"fxFlex.xl\", \"\"], [\"\", \"fxFlex.lt-sm\", \"\"], [\"\", \"fxFlex.lt-md\", \"\"], [\"\", \"fxFlex.lt-lg\", \"\"], [\"\", \"fxFlex.lt-xl\", \"\"], [\"\", \"fxFlex.gt-xs\", \"\"], [\"\", \"fxFlex.gt-sm\", \"\"], [\"\", \"fxFlex.gt-md\", \"\"], [\"\", \"fxFlex.gt-lg\", \"\"]],\n  inputs: {\n    fxFlex: \"fxFlex\",\n    \"fxFlex.xs\": \"fxFlex.xs\",\n    \"fxFlex.sm\": \"fxFlex.sm\",\n    \"fxFlex.md\": \"fxFlex.md\",\n    \"fxFlex.lg\": \"fxFlex.lg\",\n    \"fxFlex.xl\": \"fxFlex.xl\",\n    \"fxFlex.lt-sm\": \"fxFlex.lt-sm\",\n    \"fxFlex.lt-md\": \"fxFlex.lt-md\",\n    \"fxFlex.lt-lg\": \"fxFlex.lt-lg\",\n    \"fxFlex.lt-xl\": \"fxFlex.lt-xl\",\n    \"fxFlex.gt-xs\": \"fxFlex.gt-xs\",\n    \"fxFlex.gt-sm\": \"fxFlex.gt-sm\",\n    \"fxFlex.gt-md\": \"fxFlex.gt-md\",\n    \"fxFlex.gt-lg\": \"fxFlex.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexDirective, [{\n    type: Directive,\n    args: [{\n      inputs: inputs$2,\n      selector: selector$2\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst flexRowCache = new Map();\n/** @type {?} */\nconst flexColumnCache = new Map();\n/** @type {?} */\nconst flexRowWrapCache = new Map();\n/** @type {?} */\nconst flexColumnWrapCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-order/flex-order.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexOrderStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} value\n   * @return {?}\n   */\n  buildStyles(value) {\n    return {\n      order: value && parseInt(value, 10) || ''\n    };\n  }\n}\nFlexOrderStyleBuilder.ɵfac = /*@__PURE__*/function () {\n  let ɵFlexOrderStyleBuilder_BaseFactory;\n  return function FlexOrderStyleBuilder_Factory(t) {\n    return (ɵFlexOrderStyleBuilder_BaseFactory || (ɵFlexOrderStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexOrderStyleBuilder)))(t || FlexOrderStyleBuilder);\n  };\n}();\n/** @nocollapse */\nFlexOrderStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function FlexOrderStyleBuilder_Factory() {\n    return new FlexOrderStyleBuilder();\n  },\n  token: FlexOrderStyleBuilder,\n  providedIn: \"root\"\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOrderStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst inputs$3 = ['fxFlexOrder', 'fxFlexOrder.xs', 'fxFlexOrder.sm', 'fxFlexOrder.md', 'fxFlexOrder.lg', 'fxFlexOrder.xl', 'fxFlexOrder.lt-sm', 'fxFlexOrder.lt-md', 'fxFlexOrder.lt-lg', 'fxFlexOrder.lt-xl', 'fxFlexOrder.gt-xs', 'fxFlexOrder.gt-sm', 'fxFlexOrder.gt-md', 'fxFlexOrder.gt-lg'];\n/** @type {?} */\nconst selector$3 = `\n  [fxFlexOrder], [fxFlexOrder.xs], [fxFlexOrder.sm], [fxFlexOrder.md],\n  [fxFlexOrder.lg], [fxFlexOrder.xl], [fxFlexOrder.lt-sm], [fxFlexOrder.lt-md],\n  [fxFlexOrder.lt-lg], [fxFlexOrder.lt-xl], [fxFlexOrder.gt-xs], [fxFlexOrder.gt-sm],\n  [fxFlexOrder.gt-md], [fxFlexOrder.gt-lg]\n`;\n/**\n * 'flex-order' flexbox styling directive\n * Configures the positional ordering of the element in a sorted layout container\n * @see https://css-tricks.com/almanac/properties/o/order/\n */\nclass FlexOrderDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} styleUtils\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, styleUtils, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.DIRECTIVE_KEY = 'flex-order';\n    this.styleCache = flexOrderCache;\n    this.init();\n  }\n}\nFlexOrderDirective.ɵfac = function FlexOrderDirective_Factory(t) {\n  return new (t || FlexOrderDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(FlexOrderStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nFlexOrderDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: FlexOrderDirective,\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nFlexOrderDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: StyleUtils\n}, {\n  type: FlexOrderStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOrderDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: FlexOrderStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, null);\n})();\n/** @type {?} */\nconst flexOrderCache = new Map();\nclass DefaultFlexOrderDirective extends FlexOrderDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$3;\n  }\n}\nDefaultFlexOrderDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultFlexOrderDirective_BaseFactory;\n  return function DefaultFlexOrderDirective_Factory(t) {\n    return (ɵDefaultFlexOrderDirective_BaseFactory || (ɵDefaultFlexOrderDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexOrderDirective)))(t || DefaultFlexOrderDirective);\n  };\n}();\nDefaultFlexOrderDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultFlexOrderDirective,\n  selectors: [[\"\", \"fxFlexOrder\", \"\"], [\"\", \"fxFlexOrder.xs\", \"\"], [\"\", \"fxFlexOrder.sm\", \"\"], [\"\", \"fxFlexOrder.md\", \"\"], [\"\", \"fxFlexOrder.lg\", \"\"], [\"\", \"fxFlexOrder.xl\", \"\"], [\"\", \"fxFlexOrder.lt-sm\", \"\"], [\"\", \"fxFlexOrder.lt-md\", \"\"], [\"\", \"fxFlexOrder.lt-lg\", \"\"], [\"\", \"fxFlexOrder.lt-xl\", \"\"], [\"\", \"fxFlexOrder.gt-xs\", \"\"], [\"\", \"fxFlexOrder.gt-sm\", \"\"], [\"\", \"fxFlexOrder.gt-md\", \"\"], [\"\", \"fxFlexOrder.gt-lg\", \"\"]],\n  inputs: {\n    fxFlexOrder: \"fxFlexOrder\",\n    \"fxFlexOrder.xs\": \"fxFlexOrder.xs\",\n    \"fxFlexOrder.sm\": \"fxFlexOrder.sm\",\n    \"fxFlexOrder.md\": \"fxFlexOrder.md\",\n    \"fxFlexOrder.lg\": \"fxFlexOrder.lg\",\n    \"fxFlexOrder.xl\": \"fxFlexOrder.xl\",\n    \"fxFlexOrder.lt-sm\": \"fxFlexOrder.lt-sm\",\n    \"fxFlexOrder.lt-md\": \"fxFlexOrder.lt-md\",\n    \"fxFlexOrder.lt-lg\": \"fxFlexOrder.lt-lg\",\n    \"fxFlexOrder.lt-xl\": \"fxFlexOrder.lt-xl\",\n    \"fxFlexOrder.gt-xs\": \"fxFlexOrder.gt-xs\",\n    \"fxFlexOrder.gt-sm\": \"fxFlexOrder.gt-sm\",\n    \"fxFlexOrder.gt-md\": \"fxFlexOrder.gt-md\",\n    \"fxFlexOrder.gt-lg\": \"fxFlexOrder.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexOrderDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$3,\n      inputs: inputs$3\n    }]\n  }], null, null);\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-offset/flex-offset.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexOffsetStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} offset\n   * @param {?} parent\n   * @return {?}\n   */\n  buildStyles(offset, parent) {\n    if (offset === '') {\n      offset = '0';\n    }\n    /** @type {?} */\n    const isPercent = String(offset).indexOf('%') > -1;\n    /** @type {?} */\n    const isPx = String(offset).indexOf('px') > -1;\n    if (!isPx && !isPercent && !isNaN(+offset)) {\n      offset = offset + '%';\n    }\n    /** @type {?} */\n    const horizontalLayoutKey = parent.isRtl ? 'margin-right' : 'margin-left';\n    /** @type {?} */\n    const styles = isFlowHorizontal(parent.layout) ? {\n      [horizontalLayoutKey]: `${offset}`\n    } : {\n      'margin-top': `${offset}`\n    };\n    return styles;\n  }\n}\nFlexOffsetStyleBuilder.ɵfac = /*@__PURE__*/function () {\n  let ɵFlexOffsetStyleBuilder_BaseFactory;\n  return function FlexOffsetStyleBuilder_Factory(t) {\n    return (ɵFlexOffsetStyleBuilder_BaseFactory || (ɵFlexOffsetStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexOffsetStyleBuilder)))(t || FlexOffsetStyleBuilder);\n  };\n}();\n/** @nocollapse */\nFlexOffsetStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function FlexOffsetStyleBuilder_Factory() {\n    return new FlexOffsetStyleBuilder();\n  },\n  token: FlexOffsetStyleBuilder,\n  providedIn: \"root\"\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOffsetStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst inputs$4 = ['fxFlexOffset', 'fxFlexOffset.xs', 'fxFlexOffset.sm', 'fxFlexOffset.md', 'fxFlexOffset.lg', 'fxFlexOffset.xl', 'fxFlexOffset.lt-sm', 'fxFlexOffset.lt-md', 'fxFlexOffset.lt-lg', 'fxFlexOffset.lt-xl', 'fxFlexOffset.gt-xs', 'fxFlexOffset.gt-sm', 'fxFlexOffset.gt-md', 'fxFlexOffset.gt-lg'];\n/** @type {?} */\nconst selector$4 = `\n  [fxFlexOffset], [fxFlexOffset.xs], [fxFlexOffset.sm], [fxFlexOffset.md],\n  [fxFlexOffset.lg], [fxFlexOffset.xl], [fxFlexOffset.lt-sm], [fxFlexOffset.lt-md],\n  [fxFlexOffset.lt-lg], [fxFlexOffset.lt-xl], [fxFlexOffset.gt-xs], [fxFlexOffset.gt-sm],\n  [fxFlexOffset.gt-md], [fxFlexOffset.gt-lg]\n`;\n/**\n * 'flex-offset' flexbox styling directive\n * Configures the 'margin-left' of the element in a layout container\n */\nclass FlexOffsetDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} directionality\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   * @param {?} styler\n   */\n  constructor(elRef, directionality, styleBuilder, marshal, styler) {\n    super(elRef, styleBuilder, styler, marshal);\n    this.directionality = directionality;\n    this.DIRECTIVE_KEY = 'flex-offset';\n    this.init([this.directionality.change]);\n    // Parent DOM `layout-gap` with affect the nested child with `flex-offset`\n    if (this.parentElement) {\n      this.marshal.trackValue(this.parentElement, 'layout-gap').pipe(takeUntil(this.destroySubject)).subscribe(this.triggerUpdate.bind(this));\n    }\n  }\n  // *********************************************\n  // Protected methods\n  // *********************************************\n  /**\n   * Using the current fxFlexOffset value, update the inline CSS\n   * NOTE: this will assign `margin-left` if the parent flex-direction == 'row',\n   *       otherwise `margin-top` is used for the offset.\n   * @protected\n   * @param {?=} value\n   * @return {?}\n   */\n  updateWithValue(value = '') {\n    // The flex-direction of this element's flex container. Defaults to 'row'.\n    /** @type {?} */\n    const layout = this.getFlexFlowDirection( /** @type {?} */this.parentElement, true);\n    /** @type {?} */\n    const isRtl = this.directionality.value === 'rtl';\n    if (layout === 'row' && isRtl) {\n      this.styleCache = flexOffsetCacheRowRtl;\n    } else if (layout === 'row' && !isRtl) {\n      this.styleCache = flexOffsetCacheRowLtr;\n    } else if (layout === 'column' && isRtl) {\n      this.styleCache = flexOffsetCacheColumnRtl;\n    } else if (layout === 'column' && !isRtl) {\n      this.styleCache = flexOffsetCacheColumnLtr;\n    }\n    this.addStyles(value + '', {\n      layout,\n      isRtl\n    });\n  }\n}\nFlexOffsetDirective.ɵfac = function FlexOffsetDirective_Factory(t) {\n  return new (t || FlexOffsetDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc2.Directionality), ɵngcc0.ɵɵdirectiveInject(FlexOffsetStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils));\n};\nFlexOffsetDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: FlexOffsetDirective,\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nFlexOffsetDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: Directionality\n}, {\n  type: FlexOffsetStyleBuilder\n}, {\n  type: MediaMarshaller\n}, {\n  type: StyleUtils\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOffsetDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc2.Directionality\n    }, {\n      type: FlexOffsetStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }, {\n      type: ɵngcc1.StyleUtils\n    }];\n  }, null);\n})();\nclass DefaultFlexOffsetDirective extends FlexOffsetDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$4;\n  }\n}\nDefaultFlexOffsetDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultFlexOffsetDirective_BaseFactory;\n  return function DefaultFlexOffsetDirective_Factory(t) {\n    return (ɵDefaultFlexOffsetDirective_BaseFactory || (ɵDefaultFlexOffsetDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexOffsetDirective)))(t || DefaultFlexOffsetDirective);\n  };\n}();\nDefaultFlexOffsetDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultFlexOffsetDirective,\n  selectors: [[\"\", \"fxFlexOffset\", \"\"], [\"\", \"fxFlexOffset.xs\", \"\"], [\"\", \"fxFlexOffset.sm\", \"\"], [\"\", \"fxFlexOffset.md\", \"\"], [\"\", \"fxFlexOffset.lg\", \"\"], [\"\", \"fxFlexOffset.xl\", \"\"], [\"\", \"fxFlexOffset.lt-sm\", \"\"], [\"\", \"fxFlexOffset.lt-md\", \"\"], [\"\", \"fxFlexOffset.lt-lg\", \"\"], [\"\", \"fxFlexOffset.lt-xl\", \"\"], [\"\", \"fxFlexOffset.gt-xs\", \"\"], [\"\", \"fxFlexOffset.gt-sm\", \"\"], [\"\", \"fxFlexOffset.gt-md\", \"\"], [\"\", \"fxFlexOffset.gt-lg\", \"\"]],\n  inputs: {\n    fxFlexOffset: \"fxFlexOffset\",\n    \"fxFlexOffset.xs\": \"fxFlexOffset.xs\",\n    \"fxFlexOffset.sm\": \"fxFlexOffset.sm\",\n    \"fxFlexOffset.md\": \"fxFlexOffset.md\",\n    \"fxFlexOffset.lg\": \"fxFlexOffset.lg\",\n    \"fxFlexOffset.xl\": \"fxFlexOffset.xl\",\n    \"fxFlexOffset.lt-sm\": \"fxFlexOffset.lt-sm\",\n    \"fxFlexOffset.lt-md\": \"fxFlexOffset.lt-md\",\n    \"fxFlexOffset.lt-lg\": \"fxFlexOffset.lt-lg\",\n    \"fxFlexOffset.lt-xl\": \"fxFlexOffset.lt-xl\",\n    \"fxFlexOffset.gt-xs\": \"fxFlexOffset.gt-xs\",\n    \"fxFlexOffset.gt-sm\": \"fxFlexOffset.gt-sm\",\n    \"fxFlexOffset.gt-md\": \"fxFlexOffset.gt-md\",\n    \"fxFlexOffset.gt-lg\": \"fxFlexOffset.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexOffsetDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$4,\n      inputs: inputs$4\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst flexOffsetCacheRowRtl = new Map();\n/** @type {?} */\nconst flexOffsetCacheColumnRtl = new Map();\n/** @type {?} */\nconst flexOffsetCacheRowLtr = new Map();\n/** @type {?} */\nconst flexOffsetCacheColumnLtr = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-align/flex-align.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexAlignStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} input\n   * @return {?}\n   */\n  buildStyles(input) {\n    input = input || 'stretch';\n    /** @type {?} */\n    const styles = {};\n    // Cross-axis\n    switch (input) {\n      case 'start':\n        styles['align-self'] = 'flex-start';\n        break;\n      case 'end':\n        styles['align-self'] = 'flex-end';\n        break;\n      default:\n        styles['align-self'] = input;\n        break;\n    }\n    return styles;\n  }\n}\nFlexAlignStyleBuilder.ɵfac = /*@__PURE__*/function () {\n  let ɵFlexAlignStyleBuilder_BaseFactory;\n  return function FlexAlignStyleBuilder_Factory(t) {\n    return (ɵFlexAlignStyleBuilder_BaseFactory || (ɵFlexAlignStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexAlignStyleBuilder)))(t || FlexAlignStyleBuilder);\n  };\n}();\n/** @nocollapse */\nFlexAlignStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function FlexAlignStyleBuilder_Factory() {\n    return new FlexAlignStyleBuilder();\n  },\n  token: FlexAlignStyleBuilder,\n  providedIn: \"root\"\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexAlignStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst inputs$5 = ['fxFlexAlign', 'fxFlexAlign.xs', 'fxFlexAlign.sm', 'fxFlexAlign.md', 'fxFlexAlign.lg', 'fxFlexAlign.xl', 'fxFlexAlign.lt-sm', 'fxFlexAlign.lt-md', 'fxFlexAlign.lt-lg', 'fxFlexAlign.lt-xl', 'fxFlexAlign.gt-xs', 'fxFlexAlign.gt-sm', 'fxFlexAlign.gt-md', 'fxFlexAlign.gt-lg'];\n/** @type {?} */\nconst selector$5 = `\n  [fxFlexAlign], [fxFlexAlign.xs], [fxFlexAlign.sm], [fxFlexAlign.md],\n  [fxFlexAlign.lg], [fxFlexAlign.xl], [fxFlexAlign.lt-sm], [fxFlexAlign.lt-md],\n  [fxFlexAlign.lt-lg], [fxFlexAlign.lt-xl], [fxFlexAlign.gt-xs], [fxFlexAlign.gt-sm],\n  [fxFlexAlign.gt-md], [fxFlexAlign.gt-lg]\n`;\n/**\n * 'flex-align' flexbox styling directive\n * Allows element-specific overrides for cross-axis alignments in a layout container\n * @see https://css-tricks.com/almanac/properties/a/align-self/\n */\nclass FlexAlignDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} styleUtils\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, styleUtils, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.DIRECTIVE_KEY = 'flex-align';\n    this.styleCache = flexAlignCache;\n    this.init();\n  }\n}\nFlexAlignDirective.ɵfac = function FlexAlignDirective_Factory(t) {\n  return new (t || FlexAlignDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(FlexAlignStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nFlexAlignDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: FlexAlignDirective,\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nFlexAlignDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: StyleUtils\n}, {\n  type: FlexAlignStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexAlignDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: FlexAlignStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, null);\n})();\n/** @type {?} */\nconst flexAlignCache = new Map();\nclass DefaultFlexAlignDirective extends FlexAlignDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$5;\n  }\n}\nDefaultFlexAlignDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultFlexAlignDirective_BaseFactory;\n  return function DefaultFlexAlignDirective_Factory(t) {\n    return (ɵDefaultFlexAlignDirective_BaseFactory || (ɵDefaultFlexAlignDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexAlignDirective)))(t || DefaultFlexAlignDirective);\n  };\n}();\nDefaultFlexAlignDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultFlexAlignDirective,\n  selectors: [[\"\", \"fxFlexAlign\", \"\"], [\"\", \"fxFlexAlign.xs\", \"\"], [\"\", \"fxFlexAlign.sm\", \"\"], [\"\", \"fxFlexAlign.md\", \"\"], [\"\", \"fxFlexAlign.lg\", \"\"], [\"\", \"fxFlexAlign.xl\", \"\"], [\"\", \"fxFlexAlign.lt-sm\", \"\"], [\"\", \"fxFlexAlign.lt-md\", \"\"], [\"\", \"fxFlexAlign.lt-lg\", \"\"], [\"\", \"fxFlexAlign.lt-xl\", \"\"], [\"\", \"fxFlexAlign.gt-xs\", \"\"], [\"\", \"fxFlexAlign.gt-sm\", \"\"], [\"\", \"fxFlexAlign.gt-md\", \"\"], [\"\", \"fxFlexAlign.gt-lg\", \"\"]],\n  inputs: {\n    fxFlexAlign: \"fxFlexAlign\",\n    \"fxFlexAlign.xs\": \"fxFlexAlign.xs\",\n    \"fxFlexAlign.sm\": \"fxFlexAlign.sm\",\n    \"fxFlexAlign.md\": \"fxFlexAlign.md\",\n    \"fxFlexAlign.lg\": \"fxFlexAlign.lg\",\n    \"fxFlexAlign.xl\": \"fxFlexAlign.xl\",\n    \"fxFlexAlign.lt-sm\": \"fxFlexAlign.lt-sm\",\n    \"fxFlexAlign.lt-md\": \"fxFlexAlign.lt-md\",\n    \"fxFlexAlign.lt-lg\": \"fxFlexAlign.lt-lg\",\n    \"fxFlexAlign.lt-xl\": \"fxFlexAlign.lt-xl\",\n    \"fxFlexAlign.gt-xs\": \"fxFlexAlign.gt-xs\",\n    \"fxFlexAlign.gt-sm\": \"fxFlexAlign.gt-sm\",\n    \"fxFlexAlign.gt-md\": \"fxFlexAlign.gt-md\",\n    \"fxFlexAlign.gt-lg\": \"fxFlexAlign.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexAlignDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$5,\n      inputs: inputs$5\n    }]\n  }], null, null);\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-fill/flex-fill.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst FLEX_FILL_CSS = {\n  'margin': 0,\n  'width': '100%',\n  'height': '100%',\n  'min-width': '100%',\n  'min-height': '100%'\n};\nclass FlexFillStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} _input\n   * @return {?}\n   */\n  buildStyles(_input) {\n    return FLEX_FILL_CSS;\n  }\n}\nFlexFillStyleBuilder.ɵfac = /*@__PURE__*/function () {\n  let ɵFlexFillStyleBuilder_BaseFactory;\n  return function FlexFillStyleBuilder_Factory(t) {\n    return (ɵFlexFillStyleBuilder_BaseFactory || (ɵFlexFillStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexFillStyleBuilder)))(t || FlexFillStyleBuilder);\n  };\n}();\n/** @nocollapse */\nFlexFillStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function FlexFillStyleBuilder_Factory() {\n    return new FlexFillStyleBuilder();\n  },\n  token: FlexFillStyleBuilder,\n  providedIn: \"root\"\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexFillStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * 'fxFill' flexbox styling directive\n *  Maximizes width and height of element in a layout container\n *\n *  NOTE: fxFill is NOT responsive API!!\n */\nclass FlexFillDirective extends BaseDirective2 {\n  /**\n   * @param {?} elRef\n   * @param {?} styleUtils\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, styleUtils, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.styleCache = flexFillCache;\n    this.addStyles('');\n  }\n}\nFlexFillDirective.ɵfac = function FlexFillDirective_Factory(t) {\n  return new (t || FlexFillDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(FlexFillStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nFlexFillDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: FlexFillDirective,\n  selectors: [[\"\", \"fxFill\", \"\"], [\"\", \"fxFlexFill\", \"\"]],\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nFlexFillDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: StyleUtils\n}, {\n  type: FlexFillStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexFillDirective, [{\n    type: Directive,\n    args: [{\n      selector: `[fxFill], [fxFlexFill]`\n    }]\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: FlexFillStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, null);\n})();\n/** @type {?} */\nconst flexFillCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/layout-align/layout-align.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass LayoutAlignStyleBuilder extends StyleBuilder {\n  /**\n   * @param {?} align\n   * @param {?} parent\n   * @return {?}\n   */\n  buildStyles(align, parent) {\n    /** @type {?} */\n    const css = {};\n    const [mainAxis, crossAxis] = align.split(' ');\n    // Main axis\n    switch (mainAxis) {\n      case 'center':\n        css['justify-content'] = 'center';\n        break;\n      case 'space-around':\n        css['justify-content'] = 'space-around';\n        break;\n      case 'space-between':\n        css['justify-content'] = 'space-between';\n        break;\n      case 'space-evenly':\n        css['justify-content'] = 'space-evenly';\n        break;\n      case 'end':\n      case 'flex-end':\n        css['justify-content'] = 'flex-end';\n        break;\n      case 'start':\n      case 'flex-start':\n      default:\n        css['justify-content'] = 'flex-start'; // default main axis\n        break;\n    }\n    // Cross-axis\n    switch (crossAxis) {\n      case 'start':\n      case 'flex-start':\n        css['align-items'] = css['align-content'] = 'flex-start';\n        break;\n      case 'center':\n        css['align-items'] = css['align-content'] = 'center';\n        break;\n      case 'end':\n      case 'flex-end':\n        css['align-items'] = css['align-content'] = 'flex-end';\n        break;\n      case 'space-between':\n        css['align-content'] = 'space-between';\n        css['align-items'] = 'stretch';\n        break;\n      case 'space-around':\n        css['align-content'] = 'space-around';\n        css['align-items'] = 'stretch';\n        break;\n      case 'baseline':\n        css['align-content'] = 'stretch';\n        css['align-items'] = 'baseline';\n        break;\n      case 'stretch':\n      default:\n        // 'stretch'\n        css['align-items'] = css['align-content'] = 'stretch'; // default cross axis\n        break;\n    }\n    return (/** @type {?} */extendObject(css, {\n        'display': parent.inline ? 'inline-flex' : 'flex',\n        'flex-direction': parent.layout,\n        'box-sizing': 'border-box',\n        'max-width': crossAxis === 'stretch' ? !isFlowHorizontal(parent.layout) ? '100%' : null : null,\n        'max-height': crossAxis === 'stretch' ? isFlowHorizontal(parent.layout) ? '100%' : null : null\n      })\n    );\n  }\n}\nLayoutAlignStyleBuilder.ɵfac = /*@__PURE__*/function () {\n  let ɵLayoutAlignStyleBuilder_BaseFactory;\n  return function LayoutAlignStyleBuilder_Factory(t) {\n    return (ɵLayoutAlignStyleBuilder_BaseFactory || (ɵLayoutAlignStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(LayoutAlignStyleBuilder)))(t || LayoutAlignStyleBuilder);\n  };\n}();\n/** @nocollapse */\nLayoutAlignStyleBuilder.ɵprov = ɵɵdefineInjectable({\n  factory: function LayoutAlignStyleBuilder_Factory() {\n    return new LayoutAlignStyleBuilder();\n  },\n  token: LayoutAlignStyleBuilder,\n  providedIn: \"root\"\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutAlignStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst inputs$6 = ['fxLayoutAlign', 'fxLayoutAlign.xs', 'fxLayoutAlign.sm', 'fxLayoutAlign.md', 'fxLayoutAlign.lg', 'fxLayoutAlign.xl', 'fxLayoutAlign.lt-sm', 'fxLayoutAlign.lt-md', 'fxLayoutAlign.lt-lg', 'fxLayoutAlign.lt-xl', 'fxLayoutAlign.gt-xs', 'fxLayoutAlign.gt-sm', 'fxLayoutAlign.gt-md', 'fxLayoutAlign.gt-lg'];\n/** @type {?} */\nconst selector$6 = `\n  [fxLayoutAlign], [fxLayoutAlign.xs], [fxLayoutAlign.sm], [fxLayoutAlign.md],\n  [fxLayoutAlign.lg], [fxLayoutAlign.xl], [fxLayoutAlign.lt-sm], [fxLayoutAlign.lt-md],\n  [fxLayoutAlign.lt-lg], [fxLayoutAlign.lt-xl], [fxLayoutAlign.gt-xs], [fxLayoutAlign.gt-sm],\n  [fxLayoutAlign.gt-md], [fxLayoutAlign.gt-lg]\n`;\n/**\n * 'layout-align' flexbox styling directive\n *  Defines positioning of child elements along main and cross axis in a layout container\n *  Optional values: {main-axis} values or {main-axis cross-axis} value pairs\n *\n * @see https://css-tricks.com/almanac/properties/j/justify-content/\n * @see https://css-tricks.com/almanac/properties/a/align-items/\n * @see https://css-tricks.com/almanac/properties/a/align-content/\n */\nclass LayoutAlignDirective extends BaseDirective2 {\n  // default inline value\n  /**\n   * @param {?} elRef\n   * @param {?} styleUtils\n   * @param {?} styleBuilder\n   * @param {?} marshal\n   */\n  constructor(elRef, styleUtils, styleBuilder, marshal) {\n    super(elRef, styleBuilder, styleUtils, marshal);\n    this.DIRECTIVE_KEY = 'layout-align';\n    this.layout = 'row'; // default flex-direction\n    // default flex-direction\n    this.inline = false; // default inline value\n    this.init();\n    this.marshal.trackValue(this.nativeElement, 'layout').pipe(takeUntil(this.destroySubject)).subscribe(this.onLayoutChange.bind(this));\n  }\n  // *********************************************\n  // Protected methods\n  // *********************************************\n  /**\n   *\n   * @protected\n   * @param {?} value\n   * @return {?}\n   */\n  updateWithValue(value) {\n    /** @type {?} */\n    const layout = this.layout || 'row';\n    /** @type {?} */\n    const inline = this.inline;\n    if (layout === 'row' && inline) {\n      this.styleCache = layoutAlignHorizontalInlineCache;\n    } else if (layout === 'row' && !inline) {\n      this.styleCache = layoutAlignHorizontalCache;\n    } else if (layout === 'row-reverse' && inline) {\n      this.styleCache = layoutAlignHorizontalRevInlineCache;\n    } else if (layout === 'row-reverse' && !inline) {\n      this.styleCache = layoutAlignHorizontalRevCache;\n    } else if (layout === 'column' && inline) {\n      this.styleCache = layoutAlignVerticalInlineCache;\n    } else if (layout === 'column' && !inline) {\n      this.styleCache = layoutAlignVerticalCache;\n    } else if (layout === 'column-reverse' && inline) {\n      this.styleCache = layoutAlignVerticalRevInlineCache;\n    } else if (layout === 'column-reverse' && !inline) {\n      this.styleCache = layoutAlignVerticalRevCache;\n    }\n    this.addStyles(value, {\n      layout,\n      inline\n    });\n  }\n  /**\n   * Cache the parent container 'flex-direction' and update the 'flex' styles\n   * @protected\n   * @param {?} matcher\n   * @return {?}\n   */\n  onLayoutChange(matcher) {\n    /** @type {?} */\n    const layoutKeys = matcher.value.split(' ');\n    this.layout = layoutKeys[0];\n    this.inline = matcher.value.includes('inline');\n    if (!LAYOUT_VALUES.find(\n    /**\n    * @param {?} x\n    * @return {?}\n    */\n    x => x === this.layout)) {\n      this.layout = 'row';\n    }\n    this.triggerUpdate();\n  }\n}\nLayoutAlignDirective.ɵfac = function LayoutAlignDirective_Factory(t) {\n  return new (t || LayoutAlignDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LayoutAlignStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller));\n};\nLayoutAlignDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: LayoutAlignDirective,\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n/** @nocollapse */\nLayoutAlignDirective.ctorParameters = () => [{\n  type: ElementRef\n}, {\n  type: StyleUtils\n}, {\n  type: LayoutAlignStyleBuilder\n}, {\n  type: MediaMarshaller\n}];\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutAlignDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }, {\n      type: ɵngcc1.StyleUtils\n    }, {\n      type: LayoutAlignStyleBuilder\n    }, {\n      type: ɵngcc1.MediaMarshaller\n    }];\n  }, null);\n})();\nclass DefaultLayoutAlignDirective extends LayoutAlignDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$6;\n  }\n}\nDefaultLayoutAlignDirective.ɵfac = /*@__PURE__*/function () {\n  let ɵDefaultLayoutAlignDirective_BaseFactory;\n  return function DefaultLayoutAlignDirective_Factory(t) {\n    return (ɵDefaultLayoutAlignDirective_BaseFactory || (ɵDefaultLayoutAlignDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultLayoutAlignDirective)))(t || DefaultLayoutAlignDirective);\n  };\n}();\nDefaultLayoutAlignDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: DefaultLayoutAlignDirective,\n  selectors: [[\"\", \"fxLayoutAlign\", \"\"], [\"\", \"fxLayoutAlign.xs\", \"\"], [\"\", \"fxLayoutAlign.sm\", \"\"], [\"\", \"fxLayoutAlign.md\", \"\"], [\"\", \"fxLayoutAlign.lg\", \"\"], [\"\", \"fxLayoutAlign.xl\", \"\"], [\"\", \"fxLayoutAlign.lt-sm\", \"\"], [\"\", \"fxLayoutAlign.lt-md\", \"\"], [\"\", \"fxLayoutAlign.lt-lg\", \"\"], [\"\", \"fxLayoutAlign.lt-xl\", \"\"], [\"\", \"fxLayoutAlign.gt-xs\", \"\"], [\"\", \"fxLayoutAlign.gt-sm\", \"\"], [\"\", \"fxLayoutAlign.gt-md\", \"\"], [\"\", \"fxLayoutAlign.gt-lg\", \"\"]],\n  inputs: {\n    fxLayoutAlign: \"fxLayoutAlign\",\n    \"fxLayoutAlign.xs\": \"fxLayoutAlign.xs\",\n    \"fxLayoutAlign.sm\": \"fxLayoutAlign.sm\",\n    \"fxLayoutAlign.md\": \"fxLayoutAlign.md\",\n    \"fxLayoutAlign.lg\": \"fxLayoutAlign.lg\",\n    \"fxLayoutAlign.xl\": \"fxLayoutAlign.xl\",\n    \"fxLayoutAlign.lt-sm\": \"fxLayoutAlign.lt-sm\",\n    \"fxLayoutAlign.lt-md\": \"fxLayoutAlign.lt-md\",\n    \"fxLayoutAlign.lt-lg\": \"fxLayoutAlign.lt-lg\",\n    \"fxLayoutAlign.lt-xl\": \"fxLayoutAlign.lt-xl\",\n    \"fxLayoutAlign.gt-xs\": \"fxLayoutAlign.gt-xs\",\n    \"fxLayoutAlign.gt-sm\": \"fxLayoutAlign.gt-sm\",\n    \"fxLayoutAlign.gt-md\": \"fxLayoutAlign.gt-md\",\n    \"fxLayoutAlign.gt-lg\": \"fxLayoutAlign.gt-lg\"\n  },\n  features: [ɵngcc0.ɵɵInheritDefinitionFeature]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultLayoutAlignDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$6,\n      inputs: inputs$6\n    }]\n  }], null, null);\n})();\n/** @type {?} */\nconst layoutAlignHorizontalCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalCache = new Map();\n/** @type {?} */\nconst layoutAlignHorizontalRevCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalRevCache = new Map();\n/** @type {?} */\nconst layoutAlignHorizontalInlineCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalInlineCache = new Map();\n/** @type {?} */\nconst layoutAlignHorizontalRevInlineCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalRevInlineCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/module.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst ALL_DIRECTIVES = [DefaultLayoutDirective, DefaultLayoutGapDirective, DefaultLayoutAlignDirective, DefaultFlexOrderDirective, DefaultFlexOffsetDirective, FlexFillDirective, DefaultFlexAlignDirective, DefaultFlexDirective];\n/**\n * *****************************************************************\n * Define module for the Flex API\n * *****************************************************************\n */\nclass FlexModule {}\nFlexModule.ɵfac = function FlexModule_Factory(t) {\n  return new (t || FlexModule)();\n};\nFlexModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: FlexModule\n});\nFlexModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n  imports: [CoreModule, BidiModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule, BidiModule],\n      declarations: [...ALL_DIRECTIVES],\n      exports: [...ALL_DIRECTIVES]\n    }]\n  }], null, null);\n})();\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(FlexModule, {\n    declarations: function () {\n      return [DefaultLayoutDirective, DefaultLayoutGapDirective, DefaultLayoutAlignDirective, DefaultFlexOrderDirective, DefaultFlexOffsetDirective, FlexFillDirective, DefaultFlexAlignDirective, DefaultFlexDirective];\n    },\n    imports: function () {\n      return [CoreModule, BidiModule];\n    },\n    exports: function () {\n      return [DefaultLayoutDirective, DefaultLayoutGapDirective, DefaultLayoutAlignDirective, DefaultFlexOrderDirective, DefaultFlexOffsetDirective, FlexFillDirective, DefaultFlexAlignDirective, DefaultFlexDirective];\n    }\n  });\n})();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/public-api.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/index.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nexport { FlexModule, FlexStyleBuilder, FlexDirective, DefaultFlexDirective, FlexAlignStyleBuilder, FlexAlignDirective, DefaultFlexAlignDirective, FlexFillStyleBuilder, FlexFillDirective, FlexOffsetStyleBuilder, FlexOffsetDirective, DefaultFlexOffsetDirective, FlexOrderStyleBuilder, FlexOrderDirective, DefaultFlexOrderDirective, LayoutStyleBuilder, LayoutDirective, DefaultLayoutDirective, LayoutAlignStyleBuilder, LayoutAlignDirective, DefaultLayoutAlignDirective, LayoutGapStyleBuilder, LayoutGapDirective, DefaultLayoutGapDirective };", "map": {"version": 3, "names": ["Directive", "ElementRef", "Injectable", "NgModule", "NgZone", "Inject", "Input", "ɵɵdefineInjectable", "ɵɵinject", "BaseDirective2", "StyleBuilder", "StyleUtils", "MediaMarshaller", "CoreModule", "LAYOUT_CONFIG", "validateBasis", "Directionality", "BidiModule", "Subject", "takeUntil", "ɵngcc0", "ɵngcc1", "ɵngcc2", "INLINE", "LAYOUT_VALUES", "buildLayoutCSS", "value", "direction", "wrap", "isInline", "validate<PERSON><PERSON>ue", "buildCSS", "toLowerCase", "inline", "split", "find", "x", "validateWrapV<PERSON>ue", "isFlowHorizontal", "flow", "indexOf", "LayoutStyleBuilder", "buildStyles", "input", "ɵfac", "ɵLayoutStyleBuilder_BaseFactory", "LayoutStyleBuilder_Factory", "t", "ɵɵgetInheritedFactory", "ɵprov", "factory", "token", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "inputs", "selector", "LayoutDirective", "constructor", "elRef", "styleUtils", "styleBuilder", "marshal", "DIRECTIVE_KEY", "styleCache", "layoutCache", "init", "LayoutDirective_Factory", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "features", "ɵɵInheritDefinitionFeature", "ctorParameters", "DefaultLayoutDirective", "arguments", "ɵDefaultLayoutDirective_BaseFactory", "DefaultLayoutDirective_Factory", "selectors", "fxLayout", "Map", "CLEAR_MARGIN_CSS", "LayoutGapStyleBuilder", "_styler", "gapValue", "parent", "endsWith", "GRID_SPECIFIER", "slice", "buildGridMargin", "directionality", "sideEffect", "_styles", "items", "paddingStyles", "buildGridPadding", "applyStyleToElements", "lastItem", "pop", "gapCss", "buildGapCSS", "LayoutGapStyleBuilder_Factory", "inputs$1", "selector$1", "LayoutGapDirective", "zone", "layout", "observerSubject", "extraTriggers", "change", "asObservable", "trackValue", "nativeElement", "pipe", "destroySubject", "subscribe", "onLayoutChange", "bind", "childrenNodes", "obj", "children", "buffer", "i", "length", "ngAfterContentInit", "buildChildObservable", "triggerUpdate", "ngOnDestroy", "observer", "disconnect", "matcher", "updateWithValue", "filter", "el", "nodeType", "willDisplay", "sort", "a", "b", "orderA", "styler", "lookupStyle", "orderB", "isNaN", "layoutGapCacheRowRtl", "layoutGapCacheRowLtr", "layoutGapCacheColumnRtl", "layoutGapCacheColumnLtr", "addStyles", "clearStyles", "gridMode", "Object", "keys", "mru", "childrenStyle", "getMarginType", "source", "getValue", "undefined", "runOutsideAngular", "MutationObserver", "mutations", "validatedChanges", "it", "addedNodes", "removedNodes", "some", "next", "observe", "childList", "LayoutGapDirective_Factory", "DefaultLayoutGapDirective", "ɵDefaultLayoutGapDirective_BaseFactory", "DefaultLayoutGapDirective_Factory", "fxLayoutGap", "between", "below", "bottom", "paddingRight", "paddingBottom", "paddingLeft", "minus", "str", "marginRight", "marginBottom", "marginLeft", "key", "margins", "assign", "extendObject", "dest", "sources", "TypeError", "hasOwnProperty", "FlexStyleBuilder", "layoutConfig", "grow", "shrink", "basisParts", "basis", "join", "max", "min", "hasCalc", "String", "usingCalc", "isPercent", "hasUnits", "isValue", "isFixed", "css", "useColumnBasisZero", "hasWrap", "FlexStyleBuilder_Factory", "decorators", "inputs$2", "selector$2", "FlexDirective", "flexGrow", "flexShrink", "triggerReflow", "ngOnInit", "parentElement", "layoutParts", "addFlexToParent", "getFlexFlowDirection", "isHorizontal", "startsWith", "flexRowWrapCache", "flexRowCache", "flexColumnWrapCache", "flexColumnCache", "replace", "parts", "activatedValue", "updateElement", "FlexDirective_Factory", "propDecorators", "DefaultFlexDirective", "ɵDefaultFlexDirective_BaseFactory", "DefaultFlexDirective_Factory", "fxFlex", "FlexOrderStyleBuilder", "order", "parseInt", "ɵFlexOrderStyleBuilder_BaseFactory", "FlexOrderStyleBuilder_Factory", "inputs$3", "selector$3", "FlexOrderDirective", "flexOrderCache", "FlexOrderDirective_Factory", "DefaultFlexOrderDirective", "ɵDefaultFlexOrderDirective_BaseFactory", "DefaultFlexOrderDirective_Factory", "fxFlexOrder", "FlexOffsetStyleBuilder", "offset", "isPx", "horizontalLayoutKey", "isRtl", "styles", "ɵFlexOffsetStyleBuilder_BaseFactory", "FlexOffsetStyleBuilder_Factory", "inputs$4", "selector$4", "FlexOffsetDirective", "flexOffsetCacheRowRtl", "flexOffsetCacheRowLtr", "flexOffsetCacheColumnRtl", "flexOffsetCacheColumnLtr", "FlexOffsetDirective_Factory", "DefaultFlexOffsetDirective", "ɵDefaultFlexOffsetDirective_BaseFactory", "DefaultFlexOffsetDirective_Factory", "fxFlexOffset", "FlexAlignStyleBuilder", "ɵFlexAlignStyleBuilder_BaseFactory", "FlexAlignStyleBuilder_Factory", "inputs$5", "selector$5", "FlexAlignDirective", "flexAlignCache", "FlexAlignDirective_Factory", "DefaultFlexAlignDirective", "ɵDefaultFlexAlignDirective_BaseFactory", "DefaultFlexAlignDirective_Factory", "fxFlexAlign", "FLEX_FILL_CSS", "FlexFillStyleBuilder", "_input", "ɵFlexFillStyleBuilder_BaseFactory", "FlexFillStyleBuilder_Factory", "FlexFillDirective", "flexFillCache", "FlexFillDirective_Factory", "LayoutAlignStyleBuilder", "align", "mainAxis", "crossAxis", "ɵLayoutAlignStyleBuilder_BaseFactory", "LayoutAlignStyleBuilder_Factory", "inputs$6", "selector$6", "LayoutAlignDirective", "layoutAlignHorizontalInlineCache", "layoutAlignHorizontalCache", "layoutAlignHorizontalRevInlineCache", "layoutAlignHorizontalRevCache", "layoutAlignVerticalInlineCache", "layoutAlignVerticalCache", "layoutAlignVerticalRevInlineCache", "layoutAlignVerticalRevCache", "<PERSON><PERSON><PERSON><PERSON>", "includes", "LayoutAlignDirective_Factory", "DefaultLayoutAlignDirective", "ɵDefaultLayoutAlignDirective_BaseFactory", "DefaultLayoutAlignDirective_Factory", "fxLayoutAlign", "ALL_DIRECTIVES", "FlexModule", "FlexModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "ngJitMode", "ɵɵsetNgModuleScope"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/flex-layout/__ivy_ngcc__/esm2015/flex.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport { Directive, ElementRef, Injectable, NgModule, NgZone, Inject, Input, ɵɵdefineInjectable, ɵɵinject } from '@angular/core';\nimport { BaseDirective2, StyleBuilder, StyleUtils, MediaMarshaller, CoreModule, LAYOUT_CONFIG, validateBasis } from '@angular/flex-layout/core';\nimport { Directionality, BidiModule } from '@angular/cdk/bidi';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * @fileoverview added by tsickle\n * Generated from: utils/layout-validator.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n * @type {?}\n */\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/flex-layout/core';\nimport * as ɵngcc2 from '@angular/cdk/bidi';\nconst INLINE = 'inline';\n/** @type {?} */\nconst LAYOUT_VALUES = ['row', 'column', 'row-reverse', 'column-reverse'];\n/**\n * Validate the direction|'direction wrap' value and then update the host's inline flexbox styles\n * @param {?} value\n * @return {?}\n */\nfunction buildLayoutCSS(value) {\n    let [direction, wrap, isInline] = validateValue(value);\n    return buildCSS(direction, wrap, isInline);\n}\n/**\n * Validate the value to be one of the acceptable value options\n * Use default fallback of 'row'\n * @param {?} value\n * @return {?}\n */\nfunction validateValue(value) {\n    value = value ? value.toLowerCase() : '';\n    let [direction, wrap, inline] = value.split(' ');\n    // First value must be the `flex-direction`\n    if (!LAYOUT_VALUES.find((/**\n     * @param {?} x\n     * @return {?}\n     */\n    x => x === direction))) {\n        direction = LAYOUT_VALUES[0];\n    }\n    if (wrap === INLINE) {\n        wrap = (inline !== INLINE) ? inline : '';\n        inline = INLINE;\n    }\n    return [direction, validateWrapValue(wrap), !!inline];\n}\n/**\n * Determine if the validated, flex-direction value specifies\n * a horizontal/row flow.\n * @param {?} value\n * @return {?}\n */\nfunction isFlowHorizontal(value) {\n    let [flow,] = validateValue(value);\n    return flow.indexOf('row') > -1;\n}\n/**\n * Convert layout-wrap='<value>' to expected flex-wrap style\n * @param {?} value\n * @return {?}\n */\nfunction validateWrapValue(value) {\n    if (!!value) {\n        switch (value.toLowerCase()) {\n            case 'reverse':\n            case 'wrap-reverse':\n            case 'reverse-wrap':\n                value = 'wrap-reverse';\n                break;\n            case 'no':\n            case 'none':\n            case 'nowrap':\n                value = 'nowrap';\n                break;\n            // All other values fallback to 'wrap'\n            default:\n                value = 'wrap';\n                break;\n        }\n    }\n    return value;\n}\n/**\n * Build the CSS that should be assigned to the element instance\n * BUG:\n *   1) min-height on a column flex container won’t apply to its flex item children in IE 10-11.\n *      Use height instead if possible; height : <xxx>vh;\n *\n *  This way any padding or border specified on the child elements are\n *  laid out and drawn inside that element's specified width and height.\n * @param {?} direction\n * @param {?=} wrap\n * @param {?=} inline\n * @return {?}\n */\nfunction buildCSS(direction, wrap = null, inline = false) {\n    return {\n        'display': inline ? 'inline-flex' : 'flex',\n        'box-sizing': 'border-box',\n        'flex-direction': direction,\n        'flex-wrap': !!wrap ? wrap : null\n    };\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/layout/layout.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass LayoutStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} input\n     * @return {?}\n     */\n    buildStyles(input) {\n        return buildLayoutCSS(input);\n    }\n}\nLayoutStyleBuilder.ɵfac = /*@__PURE__*/ function () { let ɵLayoutStyleBuilder_BaseFactory; return function LayoutStyleBuilder_Factory(t) { return (ɵLayoutStyleBuilder_BaseFactory || (ɵLayoutStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(LayoutStyleBuilder)))(t || LayoutStyleBuilder); }; }();\n/** @nocollapse */ LayoutStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function LayoutStyleBuilder_Factory() { return new LayoutStyleBuilder(); }, token: LayoutStyleBuilder, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], null, null); })();\n/** @type {?} */\nconst inputs = [\n    'fxLayout', 'fxLayout.xs', 'fxLayout.sm', 'fxLayout.md',\n    'fxLayout.lg', 'fxLayout.xl', 'fxLayout.lt-sm', 'fxLayout.lt-md',\n    'fxLayout.lt-lg', 'fxLayout.lt-xl', 'fxLayout.gt-xs', 'fxLayout.gt-sm',\n    'fxLayout.gt-md', 'fxLayout.gt-lg'\n];\n/** @type {?} */\nconst selector = `\n  [fxLayout], [fxLayout.xs], [fxLayout.sm], [fxLayout.md],\n  [fxLayout.lg], [fxLayout.xl], [fxLayout.lt-sm], [fxLayout.lt-md],\n  [fxLayout.lt-lg], [fxLayout.lt-xl], [fxLayout.gt-xs], [fxLayout.gt-sm],\n  [fxLayout.gt-md], [fxLayout.gt-lg]\n`;\n/**\n * 'layout' flexbox styling directive\n * Defines the positioning flow direction for the child elements: row or column\n * Optional values: column or row (default)\n * @see https://css-tricks.com/almanac/properties/f/flex-direction/\n *\n */\nclass LayoutDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} styleUtils\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.DIRECTIVE_KEY = 'layout';\n        this.styleCache = layoutCache;\n        this.init();\n    }\n}\nLayoutDirective.ɵfac = function LayoutDirective_Factory(t) { return new (t || LayoutDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LayoutStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nLayoutDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: LayoutDirective, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nLayoutDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: StyleUtils },\n    { type: LayoutStyleBuilder },\n    { type: MediaMarshaller }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.StyleUtils }, { type: LayoutStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, null); })();\nclass DefaultLayoutDirective extends LayoutDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs;\n    }\n}\nDefaultLayoutDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultLayoutDirective_BaseFactory; return function DefaultLayoutDirective_Factory(t) { return (ɵDefaultLayoutDirective_BaseFactory || (ɵDefaultLayoutDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultLayoutDirective)))(t || DefaultLayoutDirective); }; }();\nDefaultLayoutDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultLayoutDirective, selectors: [[\"\", \"fxLayout\", \"\"], [\"\", \"fxLayout.xs\", \"\"], [\"\", \"fxLayout.sm\", \"\"], [\"\", \"fxLayout.md\", \"\"], [\"\", \"fxLayout.lg\", \"\"], [\"\", \"fxLayout.xl\", \"\"], [\"\", \"fxLayout.lt-sm\", \"\"], [\"\", \"fxLayout.lt-md\", \"\"], [\"\", \"fxLayout.lt-lg\", \"\"], [\"\", \"fxLayout.lt-xl\", \"\"], [\"\", \"fxLayout.gt-xs\", \"\"], [\"\", \"fxLayout.gt-sm\", \"\"], [\"\", \"fxLayout.gt-md\", \"\"], [\"\", \"fxLayout.gt-lg\", \"\"]], inputs: { fxLayout: \"fxLayout\", \"fxLayout.xs\": \"fxLayout.xs\", \"fxLayout.sm\": \"fxLayout.sm\", \"fxLayout.md\": \"fxLayout.md\", \"fxLayout.lg\": \"fxLayout.lg\", \"fxLayout.xl\": \"fxLayout.xl\", \"fxLayout.lt-sm\": \"fxLayout.lt-sm\", \"fxLayout.lt-md\": \"fxLayout.lt-md\", \"fxLayout.lt-lg\": \"fxLayout.lt-lg\", \"fxLayout.lt-xl\": \"fxLayout.lt-xl\", \"fxLayout.gt-xs\": \"fxLayout.gt-xs\", \"fxLayout.gt-sm\": \"fxLayout.gt-sm\", \"fxLayout.gt-md\": \"fxLayout.gt-md\", \"fxLayout.gt-lg\": \"fxLayout.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultLayoutDirective, [{\n        type: Directive,\n        args: [{ selector, inputs }]\n    }], null, null); })();\n/** @type {?} */\nconst layoutCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/layout-gap/layout-gap.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst CLEAR_MARGIN_CSS = {\n    'margin-left': null,\n    'margin-right': null,\n    'margin-top': null,\n    'margin-bottom': null\n};\nclass LayoutGapStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} _styler\n     */\n    constructor(_styler) {\n        super();\n        this._styler = _styler;\n    }\n    /**\n     * @param {?} gapValue\n     * @param {?} parent\n     * @return {?}\n     */\n    buildStyles(gapValue, parent) {\n        if (gapValue.endsWith(GRID_SPECIFIER)) {\n            gapValue = gapValue.slice(0, gapValue.indexOf(GRID_SPECIFIER));\n            // Add the margin to the host element\n            return buildGridMargin(gapValue, parent.directionality);\n        }\n        else {\n            return {};\n        }\n    }\n    /**\n     * @param {?} gapValue\n     * @param {?} _styles\n     * @param {?} parent\n     * @return {?}\n     */\n    sideEffect(gapValue, _styles, parent) {\n        /** @type {?} */\n        const items = parent.items;\n        if (gapValue.endsWith(GRID_SPECIFIER)) {\n            gapValue = gapValue.slice(0, gapValue.indexOf(GRID_SPECIFIER));\n            // For each `element` children, set the padding\n            /** @type {?} */\n            const paddingStyles = buildGridPadding(gapValue, parent.directionality);\n            this._styler.applyStyleToElements(paddingStyles, parent.items);\n        }\n        else {\n            /** @type {?} */\n            const lastItem = (/** @type {?} */ (items.pop()));\n            // For each `element` children EXCEPT the last,\n            // set the margin right/bottom styles...\n            /** @type {?} */\n            const gapCss = buildGapCSS(gapValue, parent);\n            this._styler.applyStyleToElements(gapCss, items);\n            // Clear all gaps for all visible elements\n            this._styler.applyStyleToElements(CLEAR_MARGIN_CSS, [lastItem]);\n        }\n    }\n}\nLayoutGapStyleBuilder.ɵfac = function LayoutGapStyleBuilder_Factory(t) { return new (t || LayoutGapStyleBuilder)(ɵngcc0.ɵɵinject(ɵngcc1.StyleUtils)); };\n/** @nocollapse */ LayoutGapStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function LayoutGapStyleBuilder_Factory() { return new LayoutGapStyleBuilder(ɵɵinject(StyleUtils)); }, token: LayoutGapStyleBuilder, providedIn: \"root\" });\n/** @nocollapse */\nLayoutGapStyleBuilder.ctorParameters = () => [\n    { type: StyleUtils }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutGapStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return [{ type: ɵngcc1.StyleUtils }]; }, null); })();\n/** @type {?} */\nconst inputs$1 = [\n    'fxLayoutGap', 'fxLayoutGap.xs', 'fxLayoutGap.sm', 'fxLayoutGap.md',\n    'fxLayoutGap.lg', 'fxLayoutGap.xl', 'fxLayoutGap.lt-sm', 'fxLayoutGap.lt-md',\n    'fxLayoutGap.lt-lg', 'fxLayoutGap.lt-xl', 'fxLayoutGap.gt-xs', 'fxLayoutGap.gt-sm',\n    'fxLayoutGap.gt-md', 'fxLayoutGap.gt-lg'\n];\n/** @type {?} */\nconst selector$1 = `\n  [fxLayoutGap], [fxLayoutGap.xs], [fxLayoutGap.sm], [fxLayoutGap.md],\n  [fxLayoutGap.lg], [fxLayoutGap.xl], [fxLayoutGap.lt-sm], [fxLayoutGap.lt-md],\n  [fxLayoutGap.lt-lg], [fxLayoutGap.lt-xl], [fxLayoutGap.gt-xs], [fxLayoutGap.gt-sm],\n  [fxLayoutGap.gt-md], [fxLayoutGap.gt-lg]\n`;\n/**\n * 'layout-padding' styling directive\n *  Defines padding of child elements in a layout container\n */\nclass LayoutGapDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} zone\n     * @param {?} directionality\n     * @param {?} styleUtils\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, zone, directionality, styleUtils, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.zone = zone;\n        this.directionality = directionality;\n        this.styleUtils = styleUtils;\n        this.layout = 'row'; // default flex-direction\n        // default flex-direction\n        this.DIRECTIVE_KEY = 'layout-gap';\n        this.observerSubject = new Subject();\n        /** @type {?} */\n        const extraTriggers = [this.directionality.change, this.observerSubject.asObservable()];\n        this.init(extraTriggers);\n        this.marshal\n            .trackValue(this.nativeElement, 'layout')\n            .pipe(takeUntil(this.destroySubject))\n            .subscribe(this.onLayoutChange.bind(this));\n    }\n    /**\n     * Special accessor to query for all child 'element' nodes regardless of type, class, etc\n     * @protected\n     * @return {?}\n     */\n    get childrenNodes() {\n        /** @type {?} */\n        const obj = this.nativeElement.children;\n        /** @type {?} */\n        const buffer = [];\n        // iterate backwards ensuring that length is an UInt32\n        for (let i = obj.length; i--;) {\n            buffer[i] = obj[i];\n        }\n        return buffer;\n    }\n    // *********************************************\n    // Lifecycle Methods\n    // *********************************************\n    /**\n     * @return {?}\n     */\n    ngAfterContentInit() {\n        this.buildChildObservable();\n        this.triggerUpdate();\n    }\n    /**\n     * @return {?}\n     */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    }\n    // *********************************************\n    // Protected methods\n    // *********************************************\n    /**\n     * Cache the parent container 'flex-direction' and update the 'margin' styles\n     * @protected\n     * @param {?} matcher\n     * @return {?}\n     */\n    onLayoutChange(matcher) {\n        /** @type {?} */\n        const layout = matcher.value;\n        // Make sure to filter out 'wrap' option\n        /** @type {?} */\n        const direction = layout.split(' ');\n        this.layout = direction[0];\n        if (!LAYOUT_VALUES.find((/**\n         * @param {?} x\n         * @return {?}\n         */\n        x => x === this.layout))) {\n            this.layout = 'row';\n        }\n        this.triggerUpdate();\n    }\n    /**\n     *\n     * @protected\n     * @param {?} value\n     * @return {?}\n     */\n    updateWithValue(value) {\n        // Gather all non-hidden Element nodes\n        /** @type {?} */\n        const items = this.childrenNodes\n            .filter((/**\n         * @param {?} el\n         * @return {?}\n         */\n        el => el.nodeType === 1 && this.willDisplay(el)))\n            .sort((/**\n         * @param {?} a\n         * @param {?} b\n         * @return {?}\n         */\n        (a, b) => {\n            /** @type {?} */\n            const orderA = +this.styler.lookupStyle(a, 'order');\n            /** @type {?} */\n            const orderB = +this.styler.lookupStyle(b, 'order');\n            if (isNaN(orderA) || isNaN(orderB) || orderA === orderB) {\n                return 0;\n            }\n            else {\n                return orderA > orderB ? 1 : -1;\n            }\n        }));\n        if (items.length > 0) {\n            /** @type {?} */\n            const directionality = this.directionality.value;\n            /** @type {?} */\n            const layout = this.layout;\n            if (layout === 'row' && directionality === 'rtl') {\n                this.styleCache = layoutGapCacheRowRtl;\n            }\n            else if (layout === 'row' && directionality !== 'rtl') {\n                this.styleCache = layoutGapCacheRowLtr;\n            }\n            else if (layout === 'column' && directionality === 'rtl') {\n                this.styleCache = layoutGapCacheColumnRtl;\n            }\n            else if (layout === 'column' && directionality !== 'rtl') {\n                this.styleCache = layoutGapCacheColumnLtr;\n            }\n            this.addStyles(value, { directionality, items, layout });\n        }\n    }\n    /**\n     * We need to override clearStyles because in most cases mru isn't populated\n     * @protected\n     * @return {?}\n     */\n    clearStyles() {\n        /** @type {?} */\n        const gridMode = Object.keys(this.mru).length > 0;\n        /** @type {?} */\n        const childrenStyle = gridMode ? 'padding' :\n            getMarginType(this.directionality.value, this.layout);\n        // If there are styles on the parent remove them\n        if (gridMode) {\n            super.clearStyles();\n        }\n        // Then remove the children styles too\n        this.styleUtils.applyStyleToElements({ [childrenStyle]: '' }, this.childrenNodes);\n    }\n    /**\n     * Determine if an element will show or hide based on current activation\n     * @protected\n     * @param {?} source\n     * @return {?}\n     */\n    willDisplay(source) {\n        /** @type {?} */\n        const value = this.marshal.getValue(source, 'show-hide');\n        return value === true ||\n            (value === undefined && this.styleUtils.lookupStyle(source, 'display') !== 'none');\n    }\n    /**\n     * @protected\n     * @return {?}\n     */\n    buildChildObservable() {\n        this.zone.runOutsideAngular((/**\n         * @return {?}\n         */\n        () => {\n            if (typeof MutationObserver !== 'undefined') {\n                this.observer = new MutationObserver((/**\n                 * @param {?} mutations\n                 * @return {?}\n                 */\n                (mutations) => {\n                    /** @type {?} */\n                    const validatedChanges = (/**\n                     * @param {?} it\n                     * @return {?}\n                     */\n                    (it) => {\n                        return (it.addedNodes && it.addedNodes.length > 0) ||\n                            (it.removedNodes && it.removedNodes.length > 0);\n                    });\n                    // update gap styles only for child 'added' or 'removed' events\n                    if (mutations.some(validatedChanges)) {\n                        this.observerSubject.next();\n                    }\n                }));\n                this.observer.observe(this.nativeElement, { childList: true });\n            }\n        }));\n    }\n}\nLayoutGapDirective.ɵfac = function LayoutGapDirective_Factory(t) { return new (t || LayoutGapDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone), ɵngcc0.ɵɵdirectiveInject(ɵngcc2.Directionality), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LayoutGapStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nLayoutGapDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: LayoutGapDirective, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nLayoutGapDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: NgZone },\n    { type: Directionality },\n    { type: StyleUtils },\n    { type: LayoutGapStyleBuilder },\n    { type: MediaMarshaller }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutGapDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc0.NgZone }, { type: ɵngcc2.Directionality }, { type: ɵngcc1.StyleUtils }, { type: LayoutGapStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, null); })();\nclass DefaultLayoutGapDirective extends LayoutGapDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$1;\n    }\n}\nDefaultLayoutGapDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultLayoutGapDirective_BaseFactory; return function DefaultLayoutGapDirective_Factory(t) { return (ɵDefaultLayoutGapDirective_BaseFactory || (ɵDefaultLayoutGapDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultLayoutGapDirective)))(t || DefaultLayoutGapDirective); }; }();\nDefaultLayoutGapDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultLayoutGapDirective, selectors: [[\"\", \"fxLayoutGap\", \"\"], [\"\", \"fxLayoutGap.xs\", \"\"], [\"\", \"fxLayoutGap.sm\", \"\"], [\"\", \"fxLayoutGap.md\", \"\"], [\"\", \"fxLayoutGap.lg\", \"\"], [\"\", \"fxLayoutGap.xl\", \"\"], [\"\", \"fxLayoutGap.lt-sm\", \"\"], [\"\", \"fxLayoutGap.lt-md\", \"\"], [\"\", \"fxLayoutGap.lt-lg\", \"\"], [\"\", \"fxLayoutGap.lt-xl\", \"\"], [\"\", \"fxLayoutGap.gt-xs\", \"\"], [\"\", \"fxLayoutGap.gt-sm\", \"\"], [\"\", \"fxLayoutGap.gt-md\", \"\"], [\"\", \"fxLayoutGap.gt-lg\", \"\"]], inputs: { fxLayoutGap: \"fxLayoutGap\", \"fxLayoutGap.xs\": \"fxLayoutGap.xs\", \"fxLayoutGap.sm\": \"fxLayoutGap.sm\", \"fxLayoutGap.md\": \"fxLayoutGap.md\", \"fxLayoutGap.lg\": \"fxLayoutGap.lg\", \"fxLayoutGap.xl\": \"fxLayoutGap.xl\", \"fxLayoutGap.lt-sm\": \"fxLayoutGap.lt-sm\", \"fxLayoutGap.lt-md\": \"fxLayoutGap.lt-md\", \"fxLayoutGap.lt-lg\": \"fxLayoutGap.lt-lg\", \"fxLayoutGap.lt-xl\": \"fxLayoutGap.lt-xl\", \"fxLayoutGap.gt-xs\": \"fxLayoutGap.gt-xs\", \"fxLayoutGap.gt-sm\": \"fxLayoutGap.gt-sm\", \"fxLayoutGap.gt-md\": \"fxLayoutGap.gt-md\", \"fxLayoutGap.gt-lg\": \"fxLayoutGap.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultLayoutGapDirective, [{\n        type: Directive,\n        args: [{ selector: selector$1, inputs: inputs$1 }]\n    }], null, null); })();\n/** @type {?} */\nconst layoutGapCacheRowRtl = new Map();\n/** @type {?} */\nconst layoutGapCacheColumnRtl = new Map();\n/** @type {?} */\nconst layoutGapCacheRowLtr = new Map();\n/** @type {?} */\nconst layoutGapCacheColumnLtr = new Map();\n/** @type {?} */\nconst GRID_SPECIFIER = ' grid';\n/**\n * @param {?} value\n * @param {?} directionality\n * @return {?}\n */\nfunction buildGridPadding(value, directionality) {\n    const [between, below] = value.split(' ');\n    /** @type {?} */\n    const bottom = below || between;\n    /** @type {?} */\n    let paddingRight = '0px';\n    /** @type {?} */\n    let paddingBottom = bottom;\n    /** @type {?} */\n    let paddingLeft = '0px';\n    if (directionality === 'rtl') {\n        paddingLeft = between;\n    }\n    else {\n        paddingRight = between;\n    }\n    return { 'padding': `0px ${paddingRight} ${paddingBottom} ${paddingLeft}` };\n}\n/**\n * @param {?} value\n * @param {?} directionality\n * @return {?}\n */\nfunction buildGridMargin(value, directionality) {\n    const [between, below] = value.split(' ');\n    /** @type {?} */\n    const bottom = below || between;\n    /** @type {?} */\n    const minus = (/**\n     * @param {?} str\n     * @return {?}\n     */\n    (str) => `-${str}`);\n    /** @type {?} */\n    let marginRight = '0px';\n    /** @type {?} */\n    let marginBottom = minus(bottom);\n    /** @type {?} */\n    let marginLeft = '0px';\n    if (directionality === 'rtl') {\n        marginLeft = minus(between);\n    }\n    else {\n        marginRight = minus(between);\n    }\n    return { 'margin': `0px ${marginRight} ${marginBottom} ${marginLeft}` };\n}\n/**\n * @param {?} directionality\n * @param {?} layout\n * @return {?}\n */\nfunction getMarginType(directionality, layout) {\n    switch (layout) {\n        case 'column':\n            return 'margin-bottom';\n        case 'column-reverse':\n            return 'margin-top';\n        case 'row':\n            return directionality === 'rtl' ? 'margin-left' : 'margin-right';\n        case 'row-reverse':\n            return directionality === 'rtl' ? 'margin-right' : 'margin-left';\n        default:\n            return directionality === 'rtl' ? 'margin-left' : 'margin-right';\n    }\n}\n/**\n * @param {?} gapValue\n * @param {?} parent\n * @return {?}\n */\nfunction buildGapCSS(gapValue, parent) {\n    /** @type {?} */\n    const key = getMarginType(parent.directionality, parent.layout);\n    /** @type {?} */\n    const margins = Object.assign({}, CLEAR_MARGIN_CSS);\n    margins[key] = gapValue;\n    return margins;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: utils/object-extend.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * Extends an object with the *enumerable* and *own* properties of one or more source objects,\n * similar to Object.assign.\n *\n * @param {?} dest The object which will have properties copied to it.\n * @param {...?} sources The source objects from which properties will be copied.\n * @return {?}\n */\nfunction extendObject(dest, ...sources) {\n    if (dest == null) {\n        throw TypeError('Cannot convert undefined or null to object');\n    }\n    for (let source of sources) {\n        if (source != null) {\n            for (let key in source) {\n                if (source.hasOwnProperty(key)) {\n                    dest[key] = source[key];\n                }\n            }\n        }\n    }\n    return dest;\n}\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex/flex.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} layoutConfig\n     */\n    constructor(layoutConfig) {\n        super();\n        this.layoutConfig = layoutConfig;\n    }\n    /**\n     * @param {?} input\n     * @param {?} parent\n     * @return {?}\n     */\n    buildStyles(input, parent) {\n        let [grow, shrink, ...basisParts] = input.split(' ');\n        /** @type {?} */\n        let basis = basisParts.join(' ');\n        // The flex-direction of this element's flex container. Defaults to 'row'.\n        /** @type {?} */\n        const direction = (parent.direction.indexOf('column') > -1) ? 'column' : 'row';\n        /** @type {?} */\n        const max = isFlowHorizontal(direction) ? 'max-width' : 'max-height';\n        /** @type {?} */\n        const min = isFlowHorizontal(direction) ? 'min-width' : 'min-height';\n        /** @type {?} */\n        const hasCalc = String(basis).indexOf('calc') > -1;\n        /** @type {?} */\n        const usingCalc = hasCalc || (basis === 'auto');\n        /** @type {?} */\n        const isPercent = String(basis).indexOf('%') > -1 && !hasCalc;\n        /** @type {?} */\n        const hasUnits = String(basis).indexOf('px') > -1 || String(basis).indexOf('rem') > -1 ||\n            String(basis).indexOf('em') > -1 || String(basis).indexOf('vw') > -1 ||\n            String(basis).indexOf('vh') > -1;\n        /** @type {?} */\n        let isValue = (hasCalc || hasUnits);\n        grow = (grow == '0') ? 0 : grow;\n        shrink = (shrink == '0') ? 0 : shrink;\n        // make box inflexible when shrink and grow are both zero\n        // should not set a min when the grow is zero\n        // should not set a max when the shrink is zero\n        /** @type {?} */\n        const isFixed = !grow && !shrink;\n        /** @type {?} */\n        let css = {};\n        // flex-basis allows you to specify the initial/starting main-axis size of the element,\n        // before anything else is computed. It can either be a percentage or an absolute value.\n        // It is, however, not the breaking point for flex-grow/shrink properties\n        //\n        // flex-grow can be seen as this:\n        //   0: Do not stretch. Either size to element's content width, or obey 'flex-basis'.\n        //   1: (Default value). Stretch; will be the same size to all other flex items on\n        //       the same row since they have a default value of 1.\n        //   ≥2 (integer n): Stretch. Will be n times the size of other elements\n        //      with 'flex-grow: 1' on the same row.\n        // Use `null` to clear existing styles.\n        /** @type {?} */\n        const clearStyles = {\n            'max-width': null,\n            'max-height': null,\n            'min-width': null,\n            'min-height': null\n        };\n        switch (basis || '') {\n            case '':\n                /** @type {?} */\n                const useColumnBasisZero = this.layoutConfig.useColumnBasisZero !== false;\n                basis = direction === 'row' ? '0%' : (useColumnBasisZero ? '0.000000001px' : 'auto');\n                break;\n            case 'initial': // default\n            case 'nogrow':\n                grow = 0;\n                basis = 'auto';\n                break;\n            case 'grow':\n                basis = '100%';\n                break;\n            case 'noshrink':\n                shrink = 0;\n                basis = 'auto';\n                break;\n            case 'auto':\n                break;\n            case 'none':\n                grow = 0;\n                shrink = 0;\n                basis = 'auto';\n                break;\n            default:\n                // Defaults to percentage sizing unless `px` is explicitly set\n                if (!isValue && !isPercent && !isNaN((/** @type {?} */ (basis)))) {\n                    basis = basis + '%';\n                }\n                // Fix for issue 280\n                if (basis === '0%') {\n                    isValue = true;\n                }\n                if (basis === '0px') {\n                    basis = '0%';\n                }\n                // fix issue #5345\n                if (hasCalc) {\n                    css = extendObject(clearStyles, {\n                        'flex-grow': grow,\n                        'flex-shrink': shrink,\n                        'flex-basis': isValue ? basis : '100%'\n                    });\n                }\n                else {\n                    css = extendObject(clearStyles, {\n                        'flex': `${grow} ${shrink} ${isValue ? basis : '100%'}`\n                    });\n                }\n                break;\n        }\n        if (!(css['flex'] || css['flex-grow'])) {\n            if (hasCalc) {\n                css = extendObject(clearStyles, {\n                    'flex-grow': grow,\n                    'flex-shrink': shrink,\n                    'flex-basis': basis\n                });\n            }\n            else {\n                css = extendObject(clearStyles, {\n                    'flex': `${grow} ${shrink} ${basis}`\n                });\n            }\n        }\n        // Fix for issues 277, 534, and 728\n        if (basis !== '0%' && basis !== '0px' && basis !== '0.000000001px' && basis !== 'auto') {\n            css[min] = isFixed || (isValue && grow) ? basis : null;\n            css[max] = isFixed || (!usingCalc && shrink) ? basis : null;\n        }\n        // Fix for issue 528\n        if (!css[min] && !css[max]) {\n            if (hasCalc) {\n                css = extendObject(clearStyles, {\n                    'flex-grow': grow,\n                    'flex-shrink': shrink,\n                    'flex-basis': basis\n                });\n            }\n            else {\n                css = extendObject(clearStyles, {\n                    'flex': `${grow} ${shrink} ${basis}`\n                });\n            }\n        }\n        else {\n            // Fix for issue 660\n            if (parent.hasWrap) {\n                css[hasCalc ? 'flex-basis' : 'flex'] = css[max] ?\n                    (hasCalc ? css[max] : `${grow} ${shrink} ${css[max]}`) :\n                    (hasCalc ? css[min] : `${grow} ${shrink} ${css[min]}`);\n            }\n        }\n        return (/** @type {?} */ (extendObject(css, { 'box-sizing': 'border-box' })));\n    }\n}\nFlexStyleBuilder.ɵfac = function FlexStyleBuilder_Factory(t) { return new (t || FlexStyleBuilder)(ɵngcc0.ɵɵinject(LAYOUT_CONFIG)); };\n/** @nocollapse */ FlexStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function FlexStyleBuilder_Factory() { return new FlexStyleBuilder(ɵɵinject(LAYOUT_CONFIG)); }, token: FlexStyleBuilder, providedIn: \"root\" });\n/** @nocollapse */\nFlexStyleBuilder.ctorParameters = () => [\n    { type: undefined, decorators: [{ type: Inject, args: [LAYOUT_CONFIG,] }] }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return [{ type: undefined, decorators: [{\n                type: Inject,\n                args: [LAYOUT_CONFIG]\n            }] }]; }, null); })();\n/** @type {?} */\nconst inputs$2 = [\n    'fxFlex', 'fxFlex.xs', 'fxFlex.sm', 'fxFlex.md',\n    'fxFlex.lg', 'fxFlex.xl', 'fxFlex.lt-sm', 'fxFlex.lt-md',\n    'fxFlex.lt-lg', 'fxFlex.lt-xl', 'fxFlex.gt-xs', 'fxFlex.gt-sm',\n    'fxFlex.gt-md', 'fxFlex.gt-lg'\n];\n/** @type {?} */\nconst selector$2 = `\n  [fxFlex], [fxFlex.xs], [fxFlex.sm], [fxFlex.md],\n  [fxFlex.lg], [fxFlex.xl], [fxFlex.lt-sm], [fxFlex.lt-md],\n  [fxFlex.lt-lg], [fxFlex.lt-xl], [fxFlex.gt-xs], [fxFlex.gt-sm],\n  [fxFlex.gt-md], [fxFlex.gt-lg]\n`;\n/**\n * Directive to control the size of a flex item using flex-basis, flex-grow, and flex-shrink.\n * Corresponds to the css `flex` shorthand property.\n *\n * @see https://css-tricks.com/snippets/css/a-guide-to-flexbox/\n */\nclass FlexDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} styleUtils\n     * @param {?} layoutConfig\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, styleUtils, layoutConfig, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.layoutConfig = layoutConfig;\n        this.marshal = marshal;\n        this.DIRECTIVE_KEY = 'flex';\n        this.direction = undefined;\n        this.wrap = undefined;\n        this.flexGrow = '1';\n        this.flexShrink = '1';\n        this.init();\n    }\n    /**\n     * @return {?}\n     */\n    get shrink() { return this.flexShrink; }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    set shrink(value) {\n        this.flexShrink = value || '1';\n        this.triggerReflow();\n    }\n    /**\n     * @return {?}\n     */\n    get grow() { return this.flexGrow; }\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    set grow(value) {\n        this.flexGrow = value || '1';\n        this.triggerReflow();\n    }\n    /**\n     * @return {?}\n     */\n    ngOnInit() {\n        if (this.parentElement) {\n            this.marshal.trackValue(this.parentElement, 'layout')\n                .pipe(takeUntil(this.destroySubject))\n                .subscribe(this.onLayoutChange.bind(this));\n            this.marshal.trackValue(this.nativeElement, 'layout-align')\n                .pipe(takeUntil(this.destroySubject))\n                .subscribe(this.triggerReflow.bind(this));\n        }\n    }\n    /**\n     * Caches the parent container's 'flex-direction' and updates the element's style.\n     * Used as a handler for layout change events from the parent flex container.\n     * @protected\n     * @param {?} matcher\n     * @return {?}\n     */\n    onLayoutChange(matcher) {\n        /** @type {?} */\n        const layout = matcher.value;\n        /** @type {?} */\n        const layoutParts = layout.split(' ');\n        this.direction = layoutParts[0];\n        this.wrap = layoutParts[1] !== undefined && layoutParts[1] === 'wrap';\n        this.triggerUpdate();\n    }\n    /**\n     * Input to this is exclusively the basis input value\n     * @protected\n     * @param {?} value\n     * @return {?}\n     */\n    updateWithValue(value) {\n        /** @type {?} */\n        const addFlexToParent = this.layoutConfig.addFlexToParent !== false;\n        if (this.direction === undefined) {\n            this.direction = this.getFlexFlowDirection((/** @type {?} */ (this.parentElement)), addFlexToParent);\n        }\n        if (this.wrap === undefined) {\n            this.wrap = this.hasWrap((/** @type {?} */ (this.parentElement)));\n        }\n        /** @type {?} */\n        const direction = this.direction;\n        /** @type {?} */\n        const isHorizontal = direction.startsWith('row');\n        /** @type {?} */\n        const hasWrap = this.wrap;\n        if (isHorizontal && hasWrap) {\n            this.styleCache = flexRowWrapCache;\n        }\n        else if (isHorizontal && !hasWrap) {\n            this.styleCache = flexRowCache;\n        }\n        else if (!isHorizontal && hasWrap) {\n            this.styleCache = flexColumnWrapCache;\n        }\n        else if (!isHorizontal && !hasWrap) {\n            this.styleCache = flexColumnCache;\n        }\n        /** @type {?} */\n        const basis = String(value).replace(';', '');\n        /** @type {?} */\n        const parts = validateBasis(basis, this.flexGrow, this.flexShrink);\n        this.addStyles(parts.join(' '), { direction, hasWrap });\n    }\n    /**\n     * Trigger a style reflow, usually based on a shrink/grow input event\n     * @protected\n     * @return {?}\n     */\n    triggerReflow() {\n        /** @type {?} */\n        const activatedValue = this.activatedValue;\n        if (activatedValue !== undefined) {\n            /** @type {?} */\n            const parts = validateBasis(activatedValue + '', this.flexGrow, this.flexShrink);\n            this.marshal.updateElement(this.nativeElement, this.DIRECTIVE_KEY, parts.join(' '));\n        }\n    }\n}\nFlexDirective.ɵfac = function FlexDirective_Factory(t) { return new (t || FlexDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LAYOUT_CONFIG), ɵngcc0.ɵɵdirectiveInject(FlexStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nFlexDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: FlexDirective, inputs: { shrink: [\"fxShrink\", \"shrink\"], grow: [\"fxGrow\", \"grow\"] }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nFlexDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: StyleUtils },\n    { type: undefined, decorators: [{ type: Inject, args: [LAYOUT_CONFIG,] }] },\n    { type: FlexStyleBuilder },\n    { type: MediaMarshaller }\n];\nFlexDirective.propDecorators = {\n    shrink: [{ type: Input, args: ['fxShrink',] }],\n    grow: [{ type: Input, args: ['fxGrow',] }]\n};\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.StyleUtils }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [LAYOUT_CONFIG]\n            }] }, { type: FlexStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, { shrink: [{\n            type: Input,\n            args: ['fxShrink']\n        }], grow: [{\n            type: Input,\n            args: ['fxGrow']\n        }] }); })();\nclass DefaultFlexDirective extends FlexDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$2;\n    }\n}\nDefaultFlexDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultFlexDirective_BaseFactory; return function DefaultFlexDirective_Factory(t) { return (ɵDefaultFlexDirective_BaseFactory || (ɵDefaultFlexDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexDirective)))(t || DefaultFlexDirective); }; }();\nDefaultFlexDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultFlexDirective, selectors: [[\"\", \"fxFlex\", \"\"], [\"\", \"fxFlex.xs\", \"\"], [\"\", \"fxFlex.sm\", \"\"], [\"\", \"fxFlex.md\", \"\"], [\"\", \"fxFlex.lg\", \"\"], [\"\", \"fxFlex.xl\", \"\"], [\"\", \"fxFlex.lt-sm\", \"\"], [\"\", \"fxFlex.lt-md\", \"\"], [\"\", \"fxFlex.lt-lg\", \"\"], [\"\", \"fxFlex.lt-xl\", \"\"], [\"\", \"fxFlex.gt-xs\", \"\"], [\"\", \"fxFlex.gt-sm\", \"\"], [\"\", \"fxFlex.gt-md\", \"\"], [\"\", \"fxFlex.gt-lg\", \"\"]], inputs: { fxFlex: \"fxFlex\", \"fxFlex.xs\": \"fxFlex.xs\", \"fxFlex.sm\": \"fxFlex.sm\", \"fxFlex.md\": \"fxFlex.md\", \"fxFlex.lg\": \"fxFlex.lg\", \"fxFlex.xl\": \"fxFlex.xl\", \"fxFlex.lt-sm\": \"fxFlex.lt-sm\", \"fxFlex.lt-md\": \"fxFlex.lt-md\", \"fxFlex.lt-lg\": \"fxFlex.lt-lg\", \"fxFlex.lt-xl\": \"fxFlex.lt-xl\", \"fxFlex.gt-xs\": \"fxFlex.gt-xs\", \"fxFlex.gt-sm\": \"fxFlex.gt-sm\", \"fxFlex.gt-md\": \"fxFlex.gt-md\", \"fxFlex.gt-lg\": \"fxFlex.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexDirective, [{\n        type: Directive,\n        args: [{ inputs: inputs$2, selector: selector$2 }]\n    }], null, null); })();\n/** @type {?} */\nconst flexRowCache = new Map();\n/** @type {?} */\nconst flexColumnCache = new Map();\n/** @type {?} */\nconst flexRowWrapCache = new Map();\n/** @type {?} */\nconst flexColumnWrapCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-order/flex-order.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexOrderStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} value\n     * @return {?}\n     */\n    buildStyles(value) {\n        return { order: (value && parseInt(value, 10)) || '' };\n    }\n}\nFlexOrderStyleBuilder.ɵfac = /*@__PURE__*/ function () { let ɵFlexOrderStyleBuilder_BaseFactory; return function FlexOrderStyleBuilder_Factory(t) { return (ɵFlexOrderStyleBuilder_BaseFactory || (ɵFlexOrderStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexOrderStyleBuilder)))(t || FlexOrderStyleBuilder); }; }();\n/** @nocollapse */ FlexOrderStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function FlexOrderStyleBuilder_Factory() { return new FlexOrderStyleBuilder(); }, token: FlexOrderStyleBuilder, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOrderStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], null, null); })();\n/** @type {?} */\nconst inputs$3 = [\n    'fxFlexOrder', 'fxFlexOrder.xs', 'fxFlexOrder.sm', 'fxFlexOrder.md',\n    'fxFlexOrder.lg', 'fxFlexOrder.xl', 'fxFlexOrder.lt-sm', 'fxFlexOrder.lt-md',\n    'fxFlexOrder.lt-lg', 'fxFlexOrder.lt-xl', 'fxFlexOrder.gt-xs', 'fxFlexOrder.gt-sm',\n    'fxFlexOrder.gt-md', 'fxFlexOrder.gt-lg'\n];\n/** @type {?} */\nconst selector$3 = `\n  [fxFlexOrder], [fxFlexOrder.xs], [fxFlexOrder.sm], [fxFlexOrder.md],\n  [fxFlexOrder.lg], [fxFlexOrder.xl], [fxFlexOrder.lt-sm], [fxFlexOrder.lt-md],\n  [fxFlexOrder.lt-lg], [fxFlexOrder.lt-xl], [fxFlexOrder.gt-xs], [fxFlexOrder.gt-sm],\n  [fxFlexOrder.gt-md], [fxFlexOrder.gt-lg]\n`;\n/**\n * 'flex-order' flexbox styling directive\n * Configures the positional ordering of the element in a sorted layout container\n * @see https://css-tricks.com/almanac/properties/o/order/\n */\nclass FlexOrderDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} styleUtils\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.DIRECTIVE_KEY = 'flex-order';\n        this.styleCache = flexOrderCache;\n        this.init();\n    }\n}\nFlexOrderDirective.ɵfac = function FlexOrderDirective_Factory(t) { return new (t || FlexOrderDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(FlexOrderStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nFlexOrderDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: FlexOrderDirective, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nFlexOrderDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: StyleUtils },\n    { type: FlexOrderStyleBuilder },\n    { type: MediaMarshaller }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOrderDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.StyleUtils }, { type: FlexOrderStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, null); })();\n/** @type {?} */\nconst flexOrderCache = new Map();\nclass DefaultFlexOrderDirective extends FlexOrderDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$3;\n    }\n}\nDefaultFlexOrderDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultFlexOrderDirective_BaseFactory; return function DefaultFlexOrderDirective_Factory(t) { return (ɵDefaultFlexOrderDirective_BaseFactory || (ɵDefaultFlexOrderDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexOrderDirective)))(t || DefaultFlexOrderDirective); }; }();\nDefaultFlexOrderDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultFlexOrderDirective, selectors: [[\"\", \"fxFlexOrder\", \"\"], [\"\", \"fxFlexOrder.xs\", \"\"], [\"\", \"fxFlexOrder.sm\", \"\"], [\"\", \"fxFlexOrder.md\", \"\"], [\"\", \"fxFlexOrder.lg\", \"\"], [\"\", \"fxFlexOrder.xl\", \"\"], [\"\", \"fxFlexOrder.lt-sm\", \"\"], [\"\", \"fxFlexOrder.lt-md\", \"\"], [\"\", \"fxFlexOrder.lt-lg\", \"\"], [\"\", \"fxFlexOrder.lt-xl\", \"\"], [\"\", \"fxFlexOrder.gt-xs\", \"\"], [\"\", \"fxFlexOrder.gt-sm\", \"\"], [\"\", \"fxFlexOrder.gt-md\", \"\"], [\"\", \"fxFlexOrder.gt-lg\", \"\"]], inputs: { fxFlexOrder: \"fxFlexOrder\", \"fxFlexOrder.xs\": \"fxFlexOrder.xs\", \"fxFlexOrder.sm\": \"fxFlexOrder.sm\", \"fxFlexOrder.md\": \"fxFlexOrder.md\", \"fxFlexOrder.lg\": \"fxFlexOrder.lg\", \"fxFlexOrder.xl\": \"fxFlexOrder.xl\", \"fxFlexOrder.lt-sm\": \"fxFlexOrder.lt-sm\", \"fxFlexOrder.lt-md\": \"fxFlexOrder.lt-md\", \"fxFlexOrder.lt-lg\": \"fxFlexOrder.lt-lg\", \"fxFlexOrder.lt-xl\": \"fxFlexOrder.lt-xl\", \"fxFlexOrder.gt-xs\": \"fxFlexOrder.gt-xs\", \"fxFlexOrder.gt-sm\": \"fxFlexOrder.gt-sm\", \"fxFlexOrder.gt-md\": \"fxFlexOrder.gt-md\", \"fxFlexOrder.gt-lg\": \"fxFlexOrder.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexOrderDirective, [{\n        type: Directive,\n        args: [{ selector: selector$3, inputs: inputs$3 }]\n    }], null, null); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-offset/flex-offset.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexOffsetStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} offset\n     * @param {?} parent\n     * @return {?}\n     */\n    buildStyles(offset, parent) {\n        if (offset === '') {\n            offset = '0';\n        }\n        /** @type {?} */\n        const isPercent = String(offset).indexOf('%') > -1;\n        /** @type {?} */\n        const isPx = String(offset).indexOf('px') > -1;\n        if (!isPx && !isPercent && !isNaN(+offset)) {\n            offset = offset + '%';\n        }\n        /** @type {?} */\n        const horizontalLayoutKey = parent.isRtl ? 'margin-right' : 'margin-left';\n        /** @type {?} */\n        const styles = isFlowHorizontal(parent.layout) ?\n            { [horizontalLayoutKey]: `${offset}` } : { 'margin-top': `${offset}` };\n        return styles;\n    }\n}\nFlexOffsetStyleBuilder.ɵfac = /*@__PURE__*/ function () { let ɵFlexOffsetStyleBuilder_BaseFactory; return function FlexOffsetStyleBuilder_Factory(t) { return (ɵFlexOffsetStyleBuilder_BaseFactory || (ɵFlexOffsetStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexOffsetStyleBuilder)))(t || FlexOffsetStyleBuilder); }; }();\n/** @nocollapse */ FlexOffsetStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function FlexOffsetStyleBuilder_Factory() { return new FlexOffsetStyleBuilder(); }, token: FlexOffsetStyleBuilder, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOffsetStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], null, null); })();\n/** @type {?} */\nconst inputs$4 = [\n    'fxFlexOffset', 'fxFlexOffset.xs', 'fxFlexOffset.sm', 'fxFlexOffset.md',\n    'fxFlexOffset.lg', 'fxFlexOffset.xl', 'fxFlexOffset.lt-sm', 'fxFlexOffset.lt-md',\n    'fxFlexOffset.lt-lg', 'fxFlexOffset.lt-xl', 'fxFlexOffset.gt-xs', 'fxFlexOffset.gt-sm',\n    'fxFlexOffset.gt-md', 'fxFlexOffset.gt-lg'\n];\n/** @type {?} */\nconst selector$4 = `\n  [fxFlexOffset], [fxFlexOffset.xs], [fxFlexOffset.sm], [fxFlexOffset.md],\n  [fxFlexOffset.lg], [fxFlexOffset.xl], [fxFlexOffset.lt-sm], [fxFlexOffset.lt-md],\n  [fxFlexOffset.lt-lg], [fxFlexOffset.lt-xl], [fxFlexOffset.gt-xs], [fxFlexOffset.gt-sm],\n  [fxFlexOffset.gt-md], [fxFlexOffset.gt-lg]\n`;\n/**\n * 'flex-offset' flexbox styling directive\n * Configures the 'margin-left' of the element in a layout container\n */\nclass FlexOffsetDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} directionality\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     * @param {?} styler\n     */\n    constructor(elRef, directionality, styleBuilder, marshal, styler) {\n        super(elRef, styleBuilder, styler, marshal);\n        this.directionality = directionality;\n        this.DIRECTIVE_KEY = 'flex-offset';\n        this.init([this.directionality.change]);\n        // Parent DOM `layout-gap` with affect the nested child with `flex-offset`\n        if (this.parentElement) {\n            this.marshal\n                .trackValue(this.parentElement, 'layout-gap')\n                .pipe(takeUntil(this.destroySubject))\n                .subscribe(this.triggerUpdate.bind(this));\n        }\n    }\n    // *********************************************\n    // Protected methods\n    // *********************************************\n    /**\n     * Using the current fxFlexOffset value, update the inline CSS\n     * NOTE: this will assign `margin-left` if the parent flex-direction == 'row',\n     *       otherwise `margin-top` is used for the offset.\n     * @protected\n     * @param {?=} value\n     * @return {?}\n     */\n    updateWithValue(value = '') {\n        // The flex-direction of this element's flex container. Defaults to 'row'.\n        /** @type {?} */\n        const layout = this.getFlexFlowDirection((/** @type {?} */ (this.parentElement)), true);\n        /** @type {?} */\n        const isRtl = this.directionality.value === 'rtl';\n        if (layout === 'row' && isRtl) {\n            this.styleCache = flexOffsetCacheRowRtl;\n        }\n        else if (layout === 'row' && !isRtl) {\n            this.styleCache = flexOffsetCacheRowLtr;\n        }\n        else if (layout === 'column' && isRtl) {\n            this.styleCache = flexOffsetCacheColumnRtl;\n        }\n        else if (layout === 'column' && !isRtl) {\n            this.styleCache = flexOffsetCacheColumnLtr;\n        }\n        this.addStyles(value + '', { layout, isRtl });\n    }\n}\nFlexOffsetDirective.ɵfac = function FlexOffsetDirective_Factory(t) { return new (t || FlexOffsetDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc2.Directionality), ɵngcc0.ɵɵdirectiveInject(FlexOffsetStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils)); };\nFlexOffsetDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: FlexOffsetDirective, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nFlexOffsetDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: Directionality },\n    { type: FlexOffsetStyleBuilder },\n    { type: MediaMarshaller },\n    { type: StyleUtils }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexOffsetDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc2.Directionality }, { type: FlexOffsetStyleBuilder }, { type: ɵngcc1.MediaMarshaller }, { type: ɵngcc1.StyleUtils }]; }, null); })();\nclass DefaultFlexOffsetDirective extends FlexOffsetDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$4;\n    }\n}\nDefaultFlexOffsetDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultFlexOffsetDirective_BaseFactory; return function DefaultFlexOffsetDirective_Factory(t) { return (ɵDefaultFlexOffsetDirective_BaseFactory || (ɵDefaultFlexOffsetDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexOffsetDirective)))(t || DefaultFlexOffsetDirective); }; }();\nDefaultFlexOffsetDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultFlexOffsetDirective, selectors: [[\"\", \"fxFlexOffset\", \"\"], [\"\", \"fxFlexOffset.xs\", \"\"], [\"\", \"fxFlexOffset.sm\", \"\"], [\"\", \"fxFlexOffset.md\", \"\"], [\"\", \"fxFlexOffset.lg\", \"\"], [\"\", \"fxFlexOffset.xl\", \"\"], [\"\", \"fxFlexOffset.lt-sm\", \"\"], [\"\", \"fxFlexOffset.lt-md\", \"\"], [\"\", \"fxFlexOffset.lt-lg\", \"\"], [\"\", \"fxFlexOffset.lt-xl\", \"\"], [\"\", \"fxFlexOffset.gt-xs\", \"\"], [\"\", \"fxFlexOffset.gt-sm\", \"\"], [\"\", \"fxFlexOffset.gt-md\", \"\"], [\"\", \"fxFlexOffset.gt-lg\", \"\"]], inputs: { fxFlexOffset: \"fxFlexOffset\", \"fxFlexOffset.xs\": \"fxFlexOffset.xs\", \"fxFlexOffset.sm\": \"fxFlexOffset.sm\", \"fxFlexOffset.md\": \"fxFlexOffset.md\", \"fxFlexOffset.lg\": \"fxFlexOffset.lg\", \"fxFlexOffset.xl\": \"fxFlexOffset.xl\", \"fxFlexOffset.lt-sm\": \"fxFlexOffset.lt-sm\", \"fxFlexOffset.lt-md\": \"fxFlexOffset.lt-md\", \"fxFlexOffset.lt-lg\": \"fxFlexOffset.lt-lg\", \"fxFlexOffset.lt-xl\": \"fxFlexOffset.lt-xl\", \"fxFlexOffset.gt-xs\": \"fxFlexOffset.gt-xs\", \"fxFlexOffset.gt-sm\": \"fxFlexOffset.gt-sm\", \"fxFlexOffset.gt-md\": \"fxFlexOffset.gt-md\", \"fxFlexOffset.gt-lg\": \"fxFlexOffset.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexOffsetDirective, [{\n        type: Directive,\n        args: [{ selector: selector$4, inputs: inputs$4 }]\n    }], null, null); })();\n/** @type {?} */\nconst flexOffsetCacheRowRtl = new Map();\n/** @type {?} */\nconst flexOffsetCacheColumnRtl = new Map();\n/** @type {?} */\nconst flexOffsetCacheRowLtr = new Map();\n/** @type {?} */\nconst flexOffsetCacheColumnLtr = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-align/flex-align.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass FlexAlignStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} input\n     * @return {?}\n     */\n    buildStyles(input) {\n        input = input || 'stretch';\n        /** @type {?} */\n        const styles = {};\n        // Cross-axis\n        switch (input) {\n            case 'start':\n                styles['align-self'] = 'flex-start';\n                break;\n            case 'end':\n                styles['align-self'] = 'flex-end';\n                break;\n            default:\n                styles['align-self'] = input;\n                break;\n        }\n        return styles;\n    }\n}\nFlexAlignStyleBuilder.ɵfac = /*@__PURE__*/ function () { let ɵFlexAlignStyleBuilder_BaseFactory; return function FlexAlignStyleBuilder_Factory(t) { return (ɵFlexAlignStyleBuilder_BaseFactory || (ɵFlexAlignStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexAlignStyleBuilder)))(t || FlexAlignStyleBuilder); }; }();\n/** @nocollapse */ FlexAlignStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function FlexAlignStyleBuilder_Factory() { return new FlexAlignStyleBuilder(); }, token: FlexAlignStyleBuilder, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexAlignStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], null, null); })();\n/** @type {?} */\nconst inputs$5 = [\n    'fxFlexAlign', 'fxFlexAlign.xs', 'fxFlexAlign.sm', 'fxFlexAlign.md',\n    'fxFlexAlign.lg', 'fxFlexAlign.xl', 'fxFlexAlign.lt-sm', 'fxFlexAlign.lt-md',\n    'fxFlexAlign.lt-lg', 'fxFlexAlign.lt-xl', 'fxFlexAlign.gt-xs', 'fxFlexAlign.gt-sm',\n    'fxFlexAlign.gt-md', 'fxFlexAlign.gt-lg'\n];\n/** @type {?} */\nconst selector$5 = `\n  [fxFlexAlign], [fxFlexAlign.xs], [fxFlexAlign.sm], [fxFlexAlign.md],\n  [fxFlexAlign.lg], [fxFlexAlign.xl], [fxFlexAlign.lt-sm], [fxFlexAlign.lt-md],\n  [fxFlexAlign.lt-lg], [fxFlexAlign.lt-xl], [fxFlexAlign.gt-xs], [fxFlexAlign.gt-sm],\n  [fxFlexAlign.gt-md], [fxFlexAlign.gt-lg]\n`;\n/**\n * 'flex-align' flexbox styling directive\n * Allows element-specific overrides for cross-axis alignments in a layout container\n * @see https://css-tricks.com/almanac/properties/a/align-self/\n */\nclass FlexAlignDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} styleUtils\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.DIRECTIVE_KEY = 'flex-align';\n        this.styleCache = flexAlignCache;\n        this.init();\n    }\n}\nFlexAlignDirective.ɵfac = function FlexAlignDirective_Factory(t) { return new (t || FlexAlignDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(FlexAlignStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nFlexAlignDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: FlexAlignDirective, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nFlexAlignDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: StyleUtils },\n    { type: FlexAlignStyleBuilder },\n    { type: MediaMarshaller }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexAlignDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.StyleUtils }, { type: FlexAlignStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, null); })();\n/** @type {?} */\nconst flexAlignCache = new Map();\nclass DefaultFlexAlignDirective extends FlexAlignDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$5;\n    }\n}\nDefaultFlexAlignDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultFlexAlignDirective_BaseFactory; return function DefaultFlexAlignDirective_Factory(t) { return (ɵDefaultFlexAlignDirective_BaseFactory || (ɵDefaultFlexAlignDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultFlexAlignDirective)))(t || DefaultFlexAlignDirective); }; }();\nDefaultFlexAlignDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultFlexAlignDirective, selectors: [[\"\", \"fxFlexAlign\", \"\"], [\"\", \"fxFlexAlign.xs\", \"\"], [\"\", \"fxFlexAlign.sm\", \"\"], [\"\", \"fxFlexAlign.md\", \"\"], [\"\", \"fxFlexAlign.lg\", \"\"], [\"\", \"fxFlexAlign.xl\", \"\"], [\"\", \"fxFlexAlign.lt-sm\", \"\"], [\"\", \"fxFlexAlign.lt-md\", \"\"], [\"\", \"fxFlexAlign.lt-lg\", \"\"], [\"\", \"fxFlexAlign.lt-xl\", \"\"], [\"\", \"fxFlexAlign.gt-xs\", \"\"], [\"\", \"fxFlexAlign.gt-sm\", \"\"], [\"\", \"fxFlexAlign.gt-md\", \"\"], [\"\", \"fxFlexAlign.gt-lg\", \"\"]], inputs: { fxFlexAlign: \"fxFlexAlign\", \"fxFlexAlign.xs\": \"fxFlexAlign.xs\", \"fxFlexAlign.sm\": \"fxFlexAlign.sm\", \"fxFlexAlign.md\": \"fxFlexAlign.md\", \"fxFlexAlign.lg\": \"fxFlexAlign.lg\", \"fxFlexAlign.xl\": \"fxFlexAlign.xl\", \"fxFlexAlign.lt-sm\": \"fxFlexAlign.lt-sm\", \"fxFlexAlign.lt-md\": \"fxFlexAlign.lt-md\", \"fxFlexAlign.lt-lg\": \"fxFlexAlign.lt-lg\", \"fxFlexAlign.lt-xl\": \"fxFlexAlign.lt-xl\", \"fxFlexAlign.gt-xs\": \"fxFlexAlign.gt-xs\", \"fxFlexAlign.gt-sm\": \"fxFlexAlign.gt-sm\", \"fxFlexAlign.gt-md\": \"fxFlexAlign.gt-md\", \"fxFlexAlign.gt-lg\": \"fxFlexAlign.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultFlexAlignDirective, [{\n        type: Directive,\n        args: [{ selector: selector$5, inputs: inputs$5 }]\n    }], null, null); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/flex-fill/flex-fill.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst FLEX_FILL_CSS = {\n    'margin': 0,\n    'width': '100%',\n    'height': '100%',\n    'min-width': '100%',\n    'min-height': '100%'\n};\nclass FlexFillStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} _input\n     * @return {?}\n     */\n    buildStyles(_input) {\n        return FLEX_FILL_CSS;\n    }\n}\nFlexFillStyleBuilder.ɵfac = /*@__PURE__*/ function () { let ɵFlexFillStyleBuilder_BaseFactory; return function FlexFillStyleBuilder_Factory(t) { return (ɵFlexFillStyleBuilder_BaseFactory || (ɵFlexFillStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(FlexFillStyleBuilder)))(t || FlexFillStyleBuilder); }; }();\n/** @nocollapse */ FlexFillStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function FlexFillStyleBuilder_Factory() { return new FlexFillStyleBuilder(); }, token: FlexFillStyleBuilder, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexFillStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], null, null); })();\n/**\n * 'fxFill' flexbox styling directive\n *  Maximizes width and height of element in a layout container\n *\n *  NOTE: fxFill is NOT responsive API!!\n */\nclass FlexFillDirective extends BaseDirective2 {\n    /**\n     * @param {?} elRef\n     * @param {?} styleUtils\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.styleCache = flexFillCache;\n        this.addStyles('');\n    }\n}\nFlexFillDirective.ɵfac = function FlexFillDirective_Factory(t) { return new (t || FlexFillDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(FlexFillStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nFlexFillDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: FlexFillDirective, selectors: [[\"\", \"fxFill\", \"\"], [\"\", \"fxFlexFill\", \"\"]], features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nFlexFillDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: StyleUtils },\n    { type: FlexFillStyleBuilder },\n    { type: MediaMarshaller }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexFillDirective, [{\n        type: Directive,\n        args: [{ selector: `[fxFill], [fxFlexFill]` }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.StyleUtils }, { type: FlexFillStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, null); })();\n/** @type {?} */\nconst flexFillCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/layout-align/layout-align.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\nclass LayoutAlignStyleBuilder extends StyleBuilder {\n    /**\n     * @param {?} align\n     * @param {?} parent\n     * @return {?}\n     */\n    buildStyles(align, parent) {\n        /** @type {?} */\n        const css = {};\n        const [mainAxis, crossAxis] = align.split(' ');\n        // Main axis\n        switch (mainAxis) {\n            case 'center':\n                css['justify-content'] = 'center';\n                break;\n            case 'space-around':\n                css['justify-content'] = 'space-around';\n                break;\n            case 'space-between':\n                css['justify-content'] = 'space-between';\n                break;\n            case 'space-evenly':\n                css['justify-content'] = 'space-evenly';\n                break;\n            case 'end':\n            case 'flex-end':\n                css['justify-content'] = 'flex-end';\n                break;\n            case 'start':\n            case 'flex-start':\n            default:\n                css['justify-content'] = 'flex-start'; // default main axis\n                break;\n        }\n        // Cross-axis\n        switch (crossAxis) {\n            case 'start':\n            case 'flex-start':\n                css['align-items'] = css['align-content'] = 'flex-start';\n                break;\n            case 'center':\n                css['align-items'] = css['align-content'] = 'center';\n                break;\n            case 'end':\n            case 'flex-end':\n                css['align-items'] = css['align-content'] = 'flex-end';\n                break;\n            case 'space-between':\n                css['align-content'] = 'space-between';\n                css['align-items'] = 'stretch';\n                break;\n            case 'space-around':\n                css['align-content'] = 'space-around';\n                css['align-items'] = 'stretch';\n                break;\n            case 'baseline':\n                css['align-content'] = 'stretch';\n                css['align-items'] = 'baseline';\n                break;\n            case 'stretch':\n            default: // 'stretch'\n                css['align-items'] = css['align-content'] = 'stretch'; // default cross axis\n                break;\n        }\n        return (/** @type {?} */ (extendObject(css, {\n            'display': parent.inline ? 'inline-flex' : 'flex',\n            'flex-direction': parent.layout,\n            'box-sizing': 'border-box',\n            'max-width': crossAxis === 'stretch' ?\n                !isFlowHorizontal(parent.layout) ? '100%' : null : null,\n            'max-height': crossAxis === 'stretch' ?\n                isFlowHorizontal(parent.layout) ? '100%' : null : null,\n        })));\n    }\n}\nLayoutAlignStyleBuilder.ɵfac = /*@__PURE__*/ function () { let ɵLayoutAlignStyleBuilder_BaseFactory; return function LayoutAlignStyleBuilder_Factory(t) { return (ɵLayoutAlignStyleBuilder_BaseFactory || (ɵLayoutAlignStyleBuilder_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(LayoutAlignStyleBuilder)))(t || LayoutAlignStyleBuilder); }; }();\n/** @nocollapse */ LayoutAlignStyleBuilder.ɵprov = ɵɵdefineInjectable({ factory: function LayoutAlignStyleBuilder_Factory() { return new LayoutAlignStyleBuilder(); }, token: LayoutAlignStyleBuilder, providedIn: \"root\" });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutAlignStyleBuilder, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], null, null); })();\n/** @type {?} */\nconst inputs$6 = [\n    'fxLayoutAlign', 'fxLayoutAlign.xs', 'fxLayoutAlign.sm', 'fxLayoutAlign.md',\n    'fxLayoutAlign.lg', 'fxLayoutAlign.xl', 'fxLayoutAlign.lt-sm', 'fxLayoutAlign.lt-md',\n    'fxLayoutAlign.lt-lg', 'fxLayoutAlign.lt-xl', 'fxLayoutAlign.gt-xs', 'fxLayoutAlign.gt-sm',\n    'fxLayoutAlign.gt-md', 'fxLayoutAlign.gt-lg'\n];\n/** @type {?} */\nconst selector$6 = `\n  [fxLayoutAlign], [fxLayoutAlign.xs], [fxLayoutAlign.sm], [fxLayoutAlign.md],\n  [fxLayoutAlign.lg], [fxLayoutAlign.xl], [fxLayoutAlign.lt-sm], [fxLayoutAlign.lt-md],\n  [fxLayoutAlign.lt-lg], [fxLayoutAlign.lt-xl], [fxLayoutAlign.gt-xs], [fxLayoutAlign.gt-sm],\n  [fxLayoutAlign.gt-md], [fxLayoutAlign.gt-lg]\n`;\n/**\n * 'layout-align' flexbox styling directive\n *  Defines positioning of child elements along main and cross axis in a layout container\n *  Optional values: {main-axis} values or {main-axis cross-axis} value pairs\n *\n * @see https://css-tricks.com/almanac/properties/j/justify-content/\n * @see https://css-tricks.com/almanac/properties/a/align-items/\n * @see https://css-tricks.com/almanac/properties/a/align-content/\n */\nclass LayoutAlignDirective extends BaseDirective2 {\n    // default inline value\n    /**\n     * @param {?} elRef\n     * @param {?} styleUtils\n     * @param {?} styleBuilder\n     * @param {?} marshal\n     */\n    constructor(elRef, styleUtils, styleBuilder, marshal) {\n        super(elRef, styleBuilder, styleUtils, marshal);\n        this.DIRECTIVE_KEY = 'layout-align';\n        this.layout = 'row'; // default flex-direction\n        // default flex-direction\n        this.inline = false; // default inline value\n        this.init();\n        this.marshal.trackValue(this.nativeElement, 'layout')\n            .pipe(takeUntil(this.destroySubject))\n            .subscribe(this.onLayoutChange.bind(this));\n    }\n    // *********************************************\n    // Protected methods\n    // *********************************************\n    /**\n     *\n     * @protected\n     * @param {?} value\n     * @return {?}\n     */\n    updateWithValue(value) {\n        /** @type {?} */\n        const layout = this.layout || 'row';\n        /** @type {?} */\n        const inline = this.inline;\n        if (layout === 'row' && inline) {\n            this.styleCache = layoutAlignHorizontalInlineCache;\n        }\n        else if (layout === 'row' && !inline) {\n            this.styleCache = layoutAlignHorizontalCache;\n        }\n        else if (layout === 'row-reverse' && inline) {\n            this.styleCache = layoutAlignHorizontalRevInlineCache;\n        }\n        else if (layout === 'row-reverse' && !inline) {\n            this.styleCache = layoutAlignHorizontalRevCache;\n        }\n        else if (layout === 'column' && inline) {\n            this.styleCache = layoutAlignVerticalInlineCache;\n        }\n        else if (layout === 'column' && !inline) {\n            this.styleCache = layoutAlignVerticalCache;\n        }\n        else if (layout === 'column-reverse' && inline) {\n            this.styleCache = layoutAlignVerticalRevInlineCache;\n        }\n        else if (layout === 'column-reverse' && !inline) {\n            this.styleCache = layoutAlignVerticalRevCache;\n        }\n        this.addStyles(value, { layout, inline });\n    }\n    /**\n     * Cache the parent container 'flex-direction' and update the 'flex' styles\n     * @protected\n     * @param {?} matcher\n     * @return {?}\n     */\n    onLayoutChange(matcher) {\n        /** @type {?} */\n        const layoutKeys = matcher.value.split(' ');\n        this.layout = layoutKeys[0];\n        this.inline = matcher.value.includes('inline');\n        if (!LAYOUT_VALUES.find((/**\n         * @param {?} x\n         * @return {?}\n         */\n        x => x === this.layout))) {\n            this.layout = 'row';\n        }\n        this.triggerUpdate();\n    }\n}\nLayoutAlignDirective.ɵfac = function LayoutAlignDirective_Factory(t) { return new (t || LayoutAlignDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.StyleUtils), ɵngcc0.ɵɵdirectiveInject(LayoutAlignStyleBuilder), ɵngcc0.ɵɵdirectiveInject(ɵngcc1.MediaMarshaller)); };\nLayoutAlignDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: LayoutAlignDirective, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n/** @nocollapse */\nLayoutAlignDirective.ctorParameters = () => [\n    { type: ElementRef },\n    { type: StyleUtils },\n    { type: LayoutAlignStyleBuilder },\n    { type: MediaMarshaller }\n];\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(LayoutAlignDirective, [{\n        type: Directive\n    }], function () { return [{ type: ɵngcc0.ElementRef }, { type: ɵngcc1.StyleUtils }, { type: LayoutAlignStyleBuilder }, { type: ɵngcc1.MediaMarshaller }]; }, null); })();\nclass DefaultLayoutAlignDirective extends LayoutAlignDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$6;\n    }\n}\nDefaultLayoutAlignDirective.ɵfac = /*@__PURE__*/ function () { let ɵDefaultLayoutAlignDirective_BaseFactory; return function DefaultLayoutAlignDirective_Factory(t) { return (ɵDefaultLayoutAlignDirective_BaseFactory || (ɵDefaultLayoutAlignDirective_BaseFactory = ɵngcc0.ɵɵgetInheritedFactory(DefaultLayoutAlignDirective)))(t || DefaultLayoutAlignDirective); }; }();\nDefaultLayoutAlignDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: DefaultLayoutAlignDirective, selectors: [[\"\", \"fxLayoutAlign\", \"\"], [\"\", \"fxLayoutAlign.xs\", \"\"], [\"\", \"fxLayoutAlign.sm\", \"\"], [\"\", \"fxLayoutAlign.md\", \"\"], [\"\", \"fxLayoutAlign.lg\", \"\"], [\"\", \"fxLayoutAlign.xl\", \"\"], [\"\", \"fxLayoutAlign.lt-sm\", \"\"], [\"\", \"fxLayoutAlign.lt-md\", \"\"], [\"\", \"fxLayoutAlign.lt-lg\", \"\"], [\"\", \"fxLayoutAlign.lt-xl\", \"\"], [\"\", \"fxLayoutAlign.gt-xs\", \"\"], [\"\", \"fxLayoutAlign.gt-sm\", \"\"], [\"\", \"fxLayoutAlign.gt-md\", \"\"], [\"\", \"fxLayoutAlign.gt-lg\", \"\"]], inputs: { fxLayoutAlign: \"fxLayoutAlign\", \"fxLayoutAlign.xs\": \"fxLayoutAlign.xs\", \"fxLayoutAlign.sm\": \"fxLayoutAlign.sm\", \"fxLayoutAlign.md\": \"fxLayoutAlign.md\", \"fxLayoutAlign.lg\": \"fxLayoutAlign.lg\", \"fxLayoutAlign.xl\": \"fxLayoutAlign.xl\", \"fxLayoutAlign.lt-sm\": \"fxLayoutAlign.lt-sm\", \"fxLayoutAlign.lt-md\": \"fxLayoutAlign.lt-md\", \"fxLayoutAlign.lt-lg\": \"fxLayoutAlign.lt-lg\", \"fxLayoutAlign.lt-xl\": \"fxLayoutAlign.lt-xl\", \"fxLayoutAlign.gt-xs\": \"fxLayoutAlign.gt-xs\", \"fxLayoutAlign.gt-sm\": \"fxLayoutAlign.gt-sm\", \"fxLayoutAlign.gt-md\": \"fxLayoutAlign.gt-md\", \"fxLayoutAlign.gt-lg\": \"fxLayoutAlign.gt-lg\" }, features: [ɵngcc0.ɵɵInheritDefinitionFeature] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(DefaultLayoutAlignDirective, [{\n        type: Directive,\n        args: [{ selector: selector$6, inputs: inputs$6 }]\n    }], null, null); })();\n/** @type {?} */\nconst layoutAlignHorizontalCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalCache = new Map();\n/** @type {?} */\nconst layoutAlignHorizontalRevCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalRevCache = new Map();\n/** @type {?} */\nconst layoutAlignHorizontalInlineCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalInlineCache = new Map();\n/** @type {?} */\nconst layoutAlignHorizontalRevInlineCache = new Map();\n/** @type {?} */\nconst layoutAlignVerticalRevInlineCache = new Map();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/module.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n/** @type {?} */\nconst ALL_DIRECTIVES = [\n    DefaultLayoutDirective,\n    DefaultLayoutGapDirective,\n    DefaultLayoutAlignDirective,\n    DefaultFlexOrderDirective,\n    DefaultFlexOffsetDirective,\n    FlexFillDirective,\n    DefaultFlexAlignDirective,\n    DefaultFlexDirective,\n];\n/**\n * *****************************************************************\n * Define module for the Flex API\n * *****************************************************************\n */\nclass FlexModule {\n}\nFlexModule.ɵfac = function FlexModule_Factory(t) { return new (t || FlexModule)(); };\nFlexModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: FlexModule });\nFlexModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [CoreModule, BidiModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(FlexModule, [{\n        type: NgModule,\n        args: [{\n                imports: [CoreModule, BidiModule],\n                declarations: [...ALL_DIRECTIVES],\n                exports: [...ALL_DIRECTIVES]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(FlexModule, { declarations: function () { return [DefaultLayoutDirective, DefaultLayoutGapDirective, DefaultLayoutAlignDirective, DefaultFlexOrderDirective, DefaultFlexOffsetDirective, FlexFillDirective, DefaultFlexAlignDirective, DefaultFlexDirective]; }, imports: function () { return [CoreModule, BidiModule]; }, exports: function () { return [DefaultLayoutDirective, DefaultLayoutGapDirective, DefaultLayoutAlignDirective, DefaultFlexOrderDirective, DefaultFlexOffsetDirective, FlexFillDirective, DefaultFlexAlignDirective, DefaultFlexDirective]; } }); })();\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/public-api.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\n/**\n * @fileoverview added by tsickle\n * Generated from: flex/index.ts\n * @suppress {checkTypes,constantProperty,extraRequire,missingOverride,missingRequire,missingReturn,unusedPrivateMembers,uselessCode} checked by tsc\n */\n\nexport { FlexModule, FlexStyleBuilder, FlexDirective, DefaultFlexDirective, FlexAlignStyleBuilder, FlexAlignDirective, DefaultFlexAlignDirective, FlexFillStyleBuilder, FlexFillDirective, FlexOffsetStyleBuilder, FlexOffsetDirective, DefaultFlexOffsetDirective, FlexOrderStyleBuilder, FlexOrderDirective, DefaultFlexOrderDirective, LayoutStyleBuilder, LayoutDirective, DefaultLayoutDirective, LayoutAlignStyleBuilder, LayoutAlignDirective, DefaultLayoutAlignDirective, LayoutGapStyleBuilder, LayoutGapDirective, DefaultLayoutGapDirective };\n\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,QAAQ,QAAQ,eAAe;AAChI,SAASC,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAEC,eAAe,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,QAAQ,2BAA2B;AAC/I,SAASC,cAAc,EAAEC,UAAU,QAAQ,mBAAmB;AAC9D,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,MAAM,MAAM,eAAe;AACvC,OAAO,KAAKC,MAAM,MAAM,2BAA2B;AACnD,OAAO,KAAKC,MAAM,MAAM,mBAAmB;AAC3C,MAAMC,MAAM,GAAG,QAAQ;AACvB;AACA,MAAMC,aAAa,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC3B,IAAI,CAACC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,CAAC,GAAGC,aAAa,CAACJ,KAAK,CAAC;EACtD,OAAOK,QAAQ,CAACJ,SAAS,EAAEC,IAAI,EAAEC,QAAQ,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACJ,KAAK,EAAE;EAC1BA,KAAK,GAAGA,KAAK,GAAGA,KAAK,CAACM,WAAW,CAAC,CAAC,GAAG,EAAE;EACxC,IAAI,CAACL,SAAS,EAAEC,IAAI,EAAEK,MAAM,CAAC,GAAGP,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC;EAChD;EACA,IAAI,CAACV,aAAa,CAACW,IAAI;EAAE;AAC7B;AACA;AACA;EACIC,CAAC,IAAIA,CAAC,KAAKT,SAAU,CAAC,EAAE;IACpBA,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC;EAChC;EACA,IAAII,IAAI,KAAKL,MAAM,EAAE;IACjBK,IAAI,GAAIK,MAAM,KAAKV,MAAM,GAAIU,MAAM,GAAG,EAAE;IACxCA,MAAM,GAAGV,MAAM;EACnB;EACA,OAAO,CAACI,SAAS,EAAEU,iBAAiB,CAACT,IAAI,CAAC,EAAE,CAAC,CAACK,MAAM,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,gBAAgBA,CAACZ,KAAK,EAAE;EAC7B,IAAI,CAACa,IAAI,CAAE,GAAGT,aAAa,CAACJ,KAAK,CAAC;EAClC,OAAOa,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,iBAAiBA,CAACX,KAAK,EAAE;EAC9B,IAAI,CAAC,CAACA,KAAK,EAAE;IACT,QAAQA,KAAK,CAACM,WAAW,CAAC,CAAC;MACvB,KAAK,SAAS;MACd,KAAK,cAAc;MACnB,KAAK,cAAc;QACfN,KAAK,GAAG,cAAc;QACtB;MACJ,KAAK,IAAI;MACT,KAAK,MAAM;MACX,KAAK,QAAQ;QACTA,KAAK,GAAG,QAAQ;QAChB;MACJ;MACA;QACIA,KAAK,GAAG,MAAM;QACd;IACR;EACJ;EACA,OAAOA,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACJ,SAAS,EAAEC,IAAI,GAAG,IAAI,EAAEK,MAAM,GAAG,KAAK,EAAE;EACtD,OAAO;IACH,SAAS,EAAEA,MAAM,GAAG,aAAa,GAAG,MAAM;IAC1C,YAAY,EAAE,YAAY;IAC1B,gBAAgB,EAAEN,SAAS;IAC3B,WAAW,EAAE,CAAC,CAACC,IAAI,GAAGA,IAAI,GAAG;EACjC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMa,kBAAkB,SAAS/B,YAAY,CAAC;EAC1C;AACJ;AACA;AACA;EACIgC,WAAWA,CAACC,KAAK,EAAE;IACf,OAAOlB,cAAc,CAACkB,KAAK,CAAC;EAChC;AACJ;AACAF,kBAAkB,CAACG,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIC,+BAA+B;EAAE,OAAO,SAASC,0BAA0BA,CAACC,CAAC,EAAE;IAAE,OAAO,CAACF,+BAA+B,KAAKA,+BAA+B,GAAGzB,MAAM,CAAC4B,qBAAqB,CAACP,kBAAkB,CAAC,CAAC,EAAEM,CAAC,IAAIN,kBAAkB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC5S;AAAmBA,kBAAkB,CAACQ,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASJ,0BAA0BA,CAAA,EAAG;IAAE,OAAO,IAAIL,kBAAkB,CAAC,CAAC;EAAE,CAAC;EAAEU,KAAK,EAAEV,kBAAkB;EAAEW,UAAU,EAAE;AAAO,CAAC,CAAC;AACxM,CAAC,YAAY;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACb,kBAAkB,EAAE,CAAC;IACxGc,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMK,MAAM,GAAG,CACX,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EACvD,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,EAChE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EACtE,gBAAgB,EAAE,gBAAgB,CACrC;AACD;AACA,MAAMC,QAAQ,GAAI;AAClB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASlD,cAAc,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAE;IAClD,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAG,QAAQ;IAC7B,IAAI,CAACC,UAAU,GAAGC,WAAW;IAC7B,IAAI,CAACC,IAAI,CAAC,CAAC;EACf;AACJ;AACAT,eAAe,CAACf,IAAI,GAAG,SAASyB,uBAAuBA,CAACtB,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIY,eAAe,EAAEvC,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAAC7B,kBAAkB,CAAC,EAAErB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AAC3R+C,eAAe,CAACY,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEI,eAAe;EAAEc,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACvI;AACAf,eAAe,CAACgB,cAAc,GAAG,MAAM,CACnC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAEd;AAAmB,CAAC,EAC5B;EAAEc,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD,CAAC,YAAY;EAAE,CAAC,OAAOyC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACK,eAAe,EAAE,CAAC;IACrGJ,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAEd;IAAmB,CAAC,EAAE;MAAEc,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACvK,MAAMgE,sBAAsB,SAASjB,eAAe,CAAC;EACjDC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAGA,MAAM;EACxB;AACJ;AACAmB,sBAAsB,CAAChC,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIkC,mCAAmC;EAAE,OAAO,SAASC,8BAA8BA,CAAChC,CAAC,EAAE;IAAE,OAAO,CAAC+B,mCAAmC,KAAKA,mCAAmC,GAAG1D,MAAM,CAAC4B,qBAAqB,CAAC4B,sBAAsB,CAAC,CAAC,EAAE7B,CAAC,IAAI6B,sBAAsB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACxUA,sBAAsB,CAACL,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEqB,sBAAsB;EAAEI,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAEwB,QAAQ,EAAE,UAAU;IAAE,aAAa,EAAE,aAAa;IAAE,aAAa,EAAE,aAAa;IAAE,aAAa,EAAE,aAAa;IAAE,aAAa,EAAE,aAAa;IAAE,aAAa,EAAE,aAAa;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE;EAAiB,CAAC;EAAER,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AAC7+B,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACsB,sBAAsB,EAAE,CAAC;IAC5GrB,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ;MAAED;IAAO,CAAC;EAC/B,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMU,WAAW,GAAG,IAAIe,GAAG,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG;EACrB,aAAa,EAAE,IAAI;EACnB,cAAc,EAAE,IAAI;EACpB,YAAY,EAAE,IAAI;EAClB,eAAe,EAAE;AACrB,CAAC;AACD,MAAMC,qBAAqB,SAAS1E,YAAY,CAAC;EAC7C;AACJ;AACA;EACIkD,WAAWA,CAACyB,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI3C,WAAWA,CAAC4C,QAAQ,EAAEC,MAAM,EAAE;IAC1B,IAAID,QAAQ,CAACE,QAAQ,CAACC,cAAc,CAAC,EAAE;MACnCH,QAAQ,GAAGA,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAEJ,QAAQ,CAAC9C,OAAO,CAACiD,cAAc,CAAC,CAAC;MAC9D;MACA,OAAOE,eAAe,CAACL,QAAQ,EAAEC,MAAM,CAACK,cAAc,CAAC;IAC3D,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACP,QAAQ,EAAEQ,OAAO,EAAEP,MAAM,EAAE;IAClC;IACA,MAAMQ,KAAK,GAAGR,MAAM,CAACQ,KAAK;IAC1B,IAAIT,QAAQ,CAACE,QAAQ,CAACC,cAAc,CAAC,EAAE;MACnCH,QAAQ,GAAGA,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAEJ,QAAQ,CAAC9C,OAAO,CAACiD,cAAc,CAAC,CAAC;MAC9D;MACA;MACA,MAAMO,aAAa,GAAGC,gBAAgB,CAACX,QAAQ,EAAEC,MAAM,CAACK,cAAc,CAAC;MACvE,IAAI,CAACP,OAAO,CAACa,oBAAoB,CAACF,aAAa,EAAET,MAAM,CAACQ,KAAK,CAAC;IAClE,CAAC,MACI;MACD;MACA,MAAMI,QAAQ,GAAI,gBAAkBJ,KAAK,CAACK,GAAG,CAAC,CAAG;MACjD;MACA;MACA;MACA,MAAMC,MAAM,GAAGC,WAAW,CAAChB,QAAQ,EAAEC,MAAM,CAAC;MAC5C,IAAI,CAACF,OAAO,CAACa,oBAAoB,CAACG,MAAM,EAAEN,KAAK,CAAC;MAChD;MACA,IAAI,CAACV,OAAO,CAACa,oBAAoB,CAACf,gBAAgB,EAAE,CAACgB,QAAQ,CAAC,CAAC;IACnE;EACJ;AACJ;AACAf,qBAAqB,CAACxC,IAAI,GAAG,SAAS2D,6BAA6BA,CAACxD,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIqC,qBAAqB,EAAEhE,MAAM,CAACZ,QAAQ,CAACa,MAAM,CAACV,UAAU,CAAC,CAAC;AAAE,CAAC;AACvJ;AAAmByE,qBAAqB,CAACnC,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASqD,6BAA6BA,CAAA,EAAG;IAAE,OAAO,IAAInB,qBAAqB,CAAC5E,QAAQ,CAACG,UAAU,CAAC,CAAC;EAAE,CAAC;EAAEwC,KAAK,EAAEiC,qBAAqB;EAAEhC,UAAU,EAAE;AAAO,CAAC,CAAC;AACxO;AACAgC,qBAAqB,CAACT,cAAc,GAAG,MAAM,CACzC;EAAEpB,IAAI,EAAE5C;AAAW,CAAC,CACvB;AACD,CAAC,YAAY;EAAE,CAAC,OAAO0C,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC8B,qBAAqB,EAAE,CAAC;IAC3G7B,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAC1E;AACA,MAAM6F,QAAQ,GAAG,CACb,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EACnE,gBAAgB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAC5E,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAClF,mBAAmB,EAAE,mBAAmB,CAC3C;AACD;AACA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASjG,cAAc,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAE8C,IAAI,EAAEf,cAAc,EAAE9B,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAE;IACxE,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAAC2C,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACf,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC9B,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC8C,MAAM,GAAG,KAAK,CAAC,CAAC;IACrB;IACA,IAAI,CAAC3C,aAAa,GAAG,YAAY;IACjC,IAAI,CAAC4C,eAAe,GAAG,IAAI3F,OAAO,CAAC,CAAC;IACpC;IACA,MAAM4F,aAAa,GAAG,CAAC,IAAI,CAAClB,cAAc,CAACmB,MAAM,EAAE,IAAI,CAACF,eAAe,CAACG,YAAY,CAAC,CAAC,CAAC;IACvF,IAAI,CAAC5C,IAAI,CAAC0C,aAAa,CAAC;IACxB,IAAI,CAAC9C,OAAO,CACPiD,UAAU,CAAC,IAAI,CAACC,aAAa,EAAE,QAAQ,CAAC,CACxCC,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACiG,cAAc,CAAC,CAAC,CACpCC,SAAS,CAAC,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,aAAaA,CAAA,EAAG;IAChB;IACA,MAAMC,GAAG,GAAG,IAAI,CAACP,aAAa,CAACQ,QAAQ;IACvC;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB;IACA,KAAK,IAAIC,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,GAAG;MAC3BD,MAAM,CAACC,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC;IACtB;IACA,OAAOD,MAAM;EACjB;EACA;EACA;EACA;EACA;AACJ;AACA;EACIG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACC,UAAU,CAAC,CAAC;IAC9B;EACJ;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;EACIb,cAAcA,CAACc,OAAO,EAAE;IACpB;IACA,MAAMxB,MAAM,GAAGwB,OAAO,CAAC1G,KAAK;IAC5B;IACA;IACA,MAAMC,SAAS,GAAGiF,MAAM,CAAC1E,KAAK,CAAC,GAAG,CAAC;IACnC,IAAI,CAAC0E,MAAM,GAAGjF,SAAS,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACH,aAAa,CAACW,IAAI;IAAE;AACjC;AACA;AACA;IACQC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAACwE,MAAO,CAAC,EAAE;MACtB,IAAI,CAACA,MAAM,GAAG,KAAK;IACvB;IACA,IAAI,CAACoB,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,eAAeA,CAAC3G,KAAK,EAAE;IACnB;IACA;IACA,MAAMqE,KAAK,GAAG,IAAI,CAACyB,aAAa,CAC3Bc,MAAM;IAAE;AACrB;AACA;AACA;IACQC,EAAE,IAAIA,EAAE,CAACC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACC,WAAW,CAACF,EAAE,CAAE,CAAC,CAC5CG,IAAI;IAAE;AACnB;AACA;AACA;AACA;IACQ,CAACC,CAAC,EAAEC,CAAC,KAAK;MACN;MACA,MAAMC,MAAM,GAAG,CAAC,IAAI,CAACC,MAAM,CAACC,WAAW,CAACJ,CAAC,EAAE,OAAO,CAAC;MACnD;MACA,MAAMK,MAAM,GAAG,CAAC,IAAI,CAACF,MAAM,CAACC,WAAW,CAACH,CAAC,EAAE,OAAO,CAAC;MACnD,IAAIK,KAAK,CAACJ,MAAM,CAAC,IAAII,KAAK,CAACD,MAAM,CAAC,IAAIH,MAAM,KAAKG,MAAM,EAAE;QACrD,OAAO,CAAC;MACZ,CAAC,MACI;QACD,OAAOH,MAAM,GAAGG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACnC;IACJ,CAAE,CAAC;IACH,IAAIjD,KAAK,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAClB;MACA,MAAMjC,cAAc,GAAG,IAAI,CAACA,cAAc,CAAClE,KAAK;MAChD;MACA,MAAMkF,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,IAAIA,MAAM,KAAK,KAAK,IAAIhB,cAAc,KAAK,KAAK,EAAE;QAC9C,IAAI,CAAC1B,UAAU,GAAGgF,oBAAoB;MAC1C,CAAC,MACI,IAAItC,MAAM,KAAK,KAAK,IAAIhB,cAAc,KAAK,KAAK,EAAE;QACnD,IAAI,CAAC1B,UAAU,GAAGiF,oBAAoB;MAC1C,CAAC,MACI,IAAIvC,MAAM,KAAK,QAAQ,IAAIhB,cAAc,KAAK,KAAK,EAAE;QACtD,IAAI,CAAC1B,UAAU,GAAGkF,uBAAuB;MAC7C,CAAC,MACI,IAAIxC,MAAM,KAAK,QAAQ,IAAIhB,cAAc,KAAK,KAAK,EAAE;QACtD,IAAI,CAAC1B,UAAU,GAAGmF,uBAAuB;MAC7C;MACA,IAAI,CAACC,SAAS,CAAC5H,KAAK,EAAE;QAAEkE,cAAc;QAAEG,KAAK;QAAEa;MAAO,CAAC,CAAC;IAC5D;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2C,WAAWA,CAAA,EAAG;IACV;IACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,GAAG,CAAC,CAAC9B,MAAM,GAAG,CAAC;IACjD;IACA,MAAM+B,aAAa,GAAGJ,QAAQ,GAAG,SAAS,GACtCK,aAAa,CAAC,IAAI,CAACjE,cAAc,CAAClE,KAAK,EAAE,IAAI,CAACkF,MAAM,CAAC;IACzD;IACA,IAAI4C,QAAQ,EAAE;MACV,KAAK,CAACD,WAAW,CAAC,CAAC;IACvB;IACA;IACA,IAAI,CAACzF,UAAU,CAACoC,oBAAoB,CAAC;MAAE,CAAC0D,aAAa,GAAG;IAAG,CAAC,EAAE,IAAI,CAACpC,aAAa,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,WAAWA,CAACqB,MAAM,EAAE;IAChB;IACA,MAAMpI,KAAK,GAAG,IAAI,CAACsC,OAAO,CAAC+F,QAAQ,CAACD,MAAM,EAAE,WAAW,CAAC;IACxD,OAAOpI,KAAK,KAAK,IAAI,IAChBA,KAAK,KAAKsI,SAAS,IAAI,IAAI,CAAClG,UAAU,CAACiF,WAAW,CAACe,MAAM,EAAE,SAAS,CAAC,KAAK,MAAO;EAC1F;EACA;AACJ;AACA;AACA;EACI/B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACpB,IAAI,CAACsD,iBAAiB;IAAE;AACrC;AACA;IACQ,MAAM;MACF,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;QACzC,IAAI,CAAChC,QAAQ,GAAG,IAAIgC,gBAAgB;QAAE;AACtD;AACA;AACA;QACiBC,SAAS,IAAK;UACX;UACA,MAAMC,gBAAgB;UAAI;AAC9C;AACA;AACA;UACqBC,EAAE,IAAK;YACJ,OAAQA,EAAE,CAACC,UAAU,IAAID,EAAE,CAACC,UAAU,CAACzC,MAAM,GAAG,CAAC,IAC5CwC,EAAE,CAACE,YAAY,IAAIF,EAAE,CAACE,YAAY,CAAC1C,MAAM,GAAG,CAAE;UACvD,CAAE;UACF;UACA,IAAIsC,SAAS,CAACK,IAAI,CAACJ,gBAAgB,CAAC,EAAE;YAClC,IAAI,CAACvD,eAAe,CAAC4D,IAAI,CAAC,CAAC;UAC/B;QACJ,CAAE,CAAC;QACH,IAAI,CAACvC,QAAQ,CAACwC,OAAO,CAAC,IAAI,CAACxD,aAAa,EAAE;UAAEyD,SAAS,EAAE;QAAK,CAAC,CAAC;MAClE;IACJ,CAAE,CAAC;EACP;AACJ;AACAjE,kBAAkB,CAAC9D,IAAI,GAAG,SAASgI,0BAA0BA,CAAC7H,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAI2D,kBAAkB,EAAEtF,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAAChB,MAAM,CAAC,EAAEgB,MAAM,CAACkD,iBAAiB,CAAChD,MAAM,CAACN,cAAc,CAAC,EAAEI,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAACc,qBAAqB,CAAC,EAAEhE,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AACjY8F,kBAAkB,CAACnC,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEmD,kBAAkB;EAAEjC,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AAC7I;AACAgC,kBAAkB,CAAC/B,cAAc,GAAG,MAAM,CACtC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAEnD;AAAO,CAAC,EAChB;EAAEmD,IAAI,EAAEvC;AAAe,CAAC,EACxB;EAAEuC,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAE6B;AAAsB,CAAC,EAC/B;EAAE7B,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD,CAAC,YAAY;EAAE,CAAC,OAAOyC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACoD,kBAAkB,EAAE,CAAC;IACxGnD,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAEnC,MAAM,CAAChB;IAAO,CAAC,EAAE;MAAEmD,IAAI,EAAEjC,MAAM,CAACN;IAAe,CAAC,EAAE;MAAEuC,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAE6B;IAAsB,CAAC,EAAE;MAAE7B,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACpO,MAAMiK,yBAAyB,SAASnE,kBAAkB,CAAC;EACvD9C,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAG+C,QAAQ;EAC1B;AACJ;AACAqE,yBAAyB,CAACjI,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIkI,sCAAsC;EAAE,OAAO,SAASC,iCAAiCA,CAAChI,CAAC,EAAE;IAAE,OAAO,CAAC+H,sCAAsC,KAAKA,sCAAsC,GAAG1J,MAAM,CAAC4B,qBAAqB,CAAC6H,yBAAyB,CAAC,CAAC,EAAE9H,CAAC,IAAI8H,yBAAyB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC7VA,yBAAyB,CAACtG,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEsH,yBAAyB;EAAE7F,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAEuH,WAAW,EAAE,aAAa;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE;EAAoB,CAAC;EAAEvG,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACjnC,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACuH,yBAAyB,EAAE,CAAC;IAC/GtH,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAE+C,UAAU;MAAEhD,MAAM,EAAE+C;IAAS,CAAC;EACrD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAM0C,oBAAoB,GAAG,IAAIhE,GAAG,CAAC,CAAC;AACtC;AACA,MAAMkE,uBAAuB,GAAG,IAAIlE,GAAG,CAAC,CAAC;AACzC;AACA,MAAMiE,oBAAoB,GAAG,IAAIjE,GAAG,CAAC,CAAC;AACtC;AACA,MAAMmE,uBAAuB,GAAG,IAAInE,GAAG,CAAC,CAAC;AACzC;AACA,MAAMO,cAAc,GAAG,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASQ,gBAAgBA,CAACvE,KAAK,EAAEkE,cAAc,EAAE;EAC7C,MAAM,CAACqF,OAAO,EAAEC,KAAK,CAAC,GAAGxJ,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC;EACzC;EACA,MAAMiJ,MAAM,GAAGD,KAAK,IAAID,OAAO;EAC/B;EACA,IAAIG,YAAY,GAAG,KAAK;EACxB;EACA,IAAIC,aAAa,GAAGF,MAAM;EAC1B;EACA,IAAIG,WAAW,GAAG,KAAK;EACvB,IAAI1F,cAAc,KAAK,KAAK,EAAE;IAC1B0F,WAAW,GAAGL,OAAO;EACzB,CAAC,MACI;IACDG,YAAY,GAAGH,OAAO;EAC1B;EACA,OAAO;IAAE,SAAS,EAAG,OAAMG,YAAa,IAAGC,aAAc,IAAGC,WAAY;EAAE,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS3F,eAAeA,CAACjE,KAAK,EAAEkE,cAAc,EAAE;EAC5C,MAAM,CAACqF,OAAO,EAAEC,KAAK,CAAC,GAAGxJ,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC;EACzC;EACA,MAAMiJ,MAAM,GAAGD,KAAK,IAAID,OAAO;EAC/B;EACA,MAAMM,KAAK;EAAI;AACnB;AACA;AACA;EACKC,GAAG,IAAM,IAAGA,GAAI,EAAE;EACnB;EACA,IAAIC,WAAW,GAAG,KAAK;EACvB;EACA,IAAIC,YAAY,GAAGH,KAAK,CAACJ,MAAM,CAAC;EAChC;EACA,IAAIQ,UAAU,GAAG,KAAK;EACtB,IAAI/F,cAAc,KAAK,KAAK,EAAE;IAC1B+F,UAAU,GAAGJ,KAAK,CAACN,OAAO,CAAC;EAC/B,CAAC,MACI;IACDQ,WAAW,GAAGF,KAAK,CAACN,OAAO,CAAC;EAChC;EACA,OAAO;IAAE,QAAQ,EAAG,OAAMQ,WAAY,IAAGC,YAAa,IAAGC,UAAW;EAAE,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9B,aAAaA,CAACjE,cAAc,EAAEgB,MAAM,EAAE;EAC3C,QAAQA,MAAM;IACV,KAAK,QAAQ;MACT,OAAO,eAAe;IAC1B,KAAK,gBAAgB;MACjB,OAAO,YAAY;IACvB,KAAK,KAAK;MACN,OAAOhB,cAAc,KAAK,KAAK,GAAG,aAAa,GAAG,cAAc;IACpE,KAAK,aAAa;MACd,OAAOA,cAAc,KAAK,KAAK,GAAG,cAAc,GAAG,aAAa;IACpE;MACI,OAAOA,cAAc,KAAK,KAAK,GAAG,aAAa,GAAG,cAAc;EACxE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,WAAWA,CAAChB,QAAQ,EAAEC,MAAM,EAAE;EACnC;EACA,MAAMqG,GAAG,GAAG/B,aAAa,CAACtE,MAAM,CAACK,cAAc,EAAEL,MAAM,CAACqB,MAAM,CAAC;EAC/D;EACA,MAAMiF,OAAO,GAAGpC,MAAM,CAACqC,MAAM,CAAC,CAAC,CAAC,EAAE3G,gBAAgB,CAAC;EACnD0G,OAAO,CAACD,GAAG,CAAC,GAAGtG,QAAQ;EACvB,OAAOuG,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,IAAI,EAAE,GAAGC,OAAO,EAAE;EACpC,IAAID,IAAI,IAAI,IAAI,EAAE;IACd,MAAME,SAAS,CAAC,4CAA4C,CAAC;EACjE;EACA,KAAK,IAAIpC,MAAM,IAAImC,OAAO,EAAE;IACxB,IAAInC,MAAM,IAAI,IAAI,EAAE;MAChB,KAAK,IAAI8B,GAAG,IAAI9B,MAAM,EAAE;QACpB,IAAIA,MAAM,CAACqC,cAAc,CAACP,GAAG,CAAC,EAAE;UAC5BI,IAAI,CAACJ,GAAG,CAAC,GAAG9B,MAAM,CAAC8B,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;EACA,OAAOI,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,gBAAgB,SAAS1L,YAAY,CAAC;EACxC;AACJ;AACA;EACIkD,WAAWA,CAACyI,YAAY,EAAE;IACtB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACA;AACJ;AACA;AACA;AACA;EACI3J,WAAWA,CAACC,KAAK,EAAE4C,MAAM,EAAE;IACvB,IAAI,CAAC+G,IAAI,EAAEC,MAAM,EAAE,GAAGC,UAAU,CAAC,GAAG7J,KAAK,CAACT,KAAK,CAAC,GAAG,CAAC;IACpD;IACA,IAAIuK,KAAK,GAAGD,UAAU,CAACE,IAAI,CAAC,GAAG,CAAC;IAChC;IACA;IACA,MAAM/K,SAAS,GAAI4D,MAAM,CAAC5D,SAAS,CAACa,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAI,QAAQ,GAAG,KAAK;IAC9E;IACA,MAAMmK,GAAG,GAAGrK,gBAAgB,CAACX,SAAS,CAAC,GAAG,WAAW,GAAG,YAAY;IACpE;IACA,MAAMiL,GAAG,GAAGtK,gBAAgB,CAACX,SAAS,CAAC,GAAG,WAAW,GAAG,YAAY;IACpE;IACA,MAAMkL,OAAO,GAAGC,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAClD;IACA,MAAMuK,SAAS,GAAGF,OAAO,IAAKJ,KAAK,KAAK,MAAO;IAC/C;IACA,MAAMO,SAAS,GAAGF,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAACqK,OAAO;IAC7D;IACA,MAAMI,QAAQ,GAAGH,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIsK,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAClFsK,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIsK,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IACpEsK,MAAM,CAACL,KAAK,CAAC,CAACjK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpC;IACA,IAAI0K,OAAO,GAAIL,OAAO,IAAII,QAAS;IACnCX,IAAI,GAAIA,IAAI,IAAI,GAAG,GAAI,CAAC,GAAGA,IAAI;IAC/BC,MAAM,GAAIA,MAAM,IAAI,GAAG,GAAI,CAAC,GAAGA,MAAM;IACrC;IACA;IACA;IACA;IACA,MAAMY,OAAO,GAAG,CAACb,IAAI,IAAI,CAACC,MAAM;IAChC;IACA,IAAIa,GAAG,GAAG,CAAC,CAAC;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM7D,WAAW,GAAG;MAChB,WAAW,EAAE,IAAI;MACjB,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,IAAI;MACjB,YAAY,EAAE;IAClB,CAAC;IACD,QAAQkD,KAAK,IAAI,EAAE;MACf,KAAK,EAAE;QACH;QACA,MAAMY,kBAAkB,GAAG,IAAI,CAAChB,YAAY,CAACgB,kBAAkB,KAAK,KAAK;QACzEZ,KAAK,GAAG9K,SAAS,KAAK,KAAK,GAAG,IAAI,GAAI0L,kBAAkB,GAAG,eAAe,GAAG,MAAO;QACpF;MACJ,KAAK,SAAS,CAAC,CAAC;MAChB,KAAK,QAAQ;QACTf,IAAI,GAAG,CAAC;QACRG,KAAK,GAAG,MAAM;QACd;MACJ,KAAK,MAAM;QACPA,KAAK,GAAG,MAAM;QACd;MACJ,KAAK,UAAU;QACXF,MAAM,GAAG,CAAC;QACVE,KAAK,GAAG,MAAM;QACd;MACJ,KAAK,MAAM;QACP;MACJ,KAAK,MAAM;QACPH,IAAI,GAAG,CAAC;QACRC,MAAM,GAAG,CAAC;QACVE,KAAK,GAAG,MAAM;QACd;MACJ;QACI;QACA,IAAI,CAACS,OAAO,IAAI,CAACF,SAAS,IAAI,CAAC/D,KAAK,EAAE,gBAAkBwD,KAAO,CAAC,EAAE;UAC9DA,KAAK,GAAGA,KAAK,GAAG,GAAG;QACvB;QACA;QACA,IAAIA,KAAK,KAAK,IAAI,EAAE;UAChBS,OAAO,GAAG,IAAI;QAClB;QACA,IAAIT,KAAK,KAAK,KAAK,EAAE;UACjBA,KAAK,GAAG,IAAI;QAChB;QACA;QACA,IAAII,OAAO,EAAE;UACTO,GAAG,GAAGrB,YAAY,CAACxC,WAAW,EAAE;YAC5B,WAAW,EAAE+C,IAAI;YACjB,aAAa,EAAEC,MAAM;YACrB,YAAY,EAAEW,OAAO,GAAGT,KAAK,GAAG;UACpC,CAAC,CAAC;QACN,CAAC,MACI;UACDW,GAAG,GAAGrB,YAAY,CAACxC,WAAW,EAAE;YAC5B,MAAM,EAAG,GAAE+C,IAAK,IAAGC,MAAO,IAAGW,OAAO,GAAGT,KAAK,GAAG,MAAO;UAC1D,CAAC,CAAC;QACN;QACA;IACR;IACA,IAAI,EAAEW,GAAG,CAAC,MAAM,CAAC,IAAIA,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE;MACpC,IAAIP,OAAO,EAAE;QACTO,GAAG,GAAGrB,YAAY,CAACxC,WAAW,EAAE;UAC5B,WAAW,EAAE+C,IAAI;UACjB,aAAa,EAAEC,MAAM;UACrB,YAAY,EAAEE;QAClB,CAAC,CAAC;MACN,CAAC,MACI;QACDW,GAAG,GAAGrB,YAAY,CAACxC,WAAW,EAAE;UAC5B,MAAM,EAAG,GAAE+C,IAAK,IAAGC,MAAO,IAAGE,KAAM;QACvC,CAAC,CAAC;MACN;IACJ;IACA;IACA,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,eAAe,IAAIA,KAAK,KAAK,MAAM,EAAE;MACpFW,GAAG,CAACR,GAAG,CAAC,GAAGO,OAAO,IAAKD,OAAO,IAAIZ,IAAK,GAAGG,KAAK,GAAG,IAAI;MACtDW,GAAG,CAACT,GAAG,CAAC,GAAGQ,OAAO,IAAK,CAACJ,SAAS,IAAIR,MAAO,GAAGE,KAAK,GAAG,IAAI;IAC/D;IACA;IACA,IAAI,CAACW,GAAG,CAACR,GAAG,CAAC,IAAI,CAACQ,GAAG,CAACT,GAAG,CAAC,EAAE;MACxB,IAAIE,OAAO,EAAE;QACTO,GAAG,GAAGrB,YAAY,CAACxC,WAAW,EAAE;UAC5B,WAAW,EAAE+C,IAAI;UACjB,aAAa,EAAEC,MAAM;UACrB,YAAY,EAAEE;QAClB,CAAC,CAAC;MACN,CAAC,MACI;QACDW,GAAG,GAAGrB,YAAY,CAACxC,WAAW,EAAE;UAC5B,MAAM,EAAG,GAAE+C,IAAK,IAAGC,MAAO,IAAGE,KAAM;QACvC,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACD;MACA,IAAIlH,MAAM,CAAC+H,OAAO,EAAE;QAChBF,GAAG,CAACP,OAAO,GAAG,YAAY,GAAG,MAAM,CAAC,GAAGO,GAAG,CAACT,GAAG,CAAC,GAC1CE,OAAO,GAAGO,GAAG,CAACT,GAAG,CAAC,GAAI,GAAEL,IAAK,IAAGC,MAAO,IAAGa,GAAG,CAACT,GAAG,CAAE,EAAC,GACpDE,OAAO,GAAGO,GAAG,CAACR,GAAG,CAAC,GAAI,GAAEN,IAAK,IAAGC,MAAO,IAAGa,GAAG,CAACR,GAAG,CAAE,EAAE;MAC9D;IACJ;IACA,OAAQ,iBAAkBb,YAAY,CAACqB,GAAG,EAAE;QAAE,YAAY,EAAE;MAAa,CAAC;IAAC;EAC/E;AACJ;AACAhB,gBAAgB,CAACxJ,IAAI,GAAG,SAAS2K,wBAAwBA,CAACxK,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIqJ,gBAAgB,EAAEhL,MAAM,CAACZ,QAAQ,CAACM,aAAa,CAAC,CAAC;AAAE,CAAC;AACpI;AAAmBsL,gBAAgB,CAACnJ,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASqK,wBAAwBA,CAAA,EAAG;IAAE,OAAO,IAAInB,gBAAgB,CAAC5L,QAAQ,CAACM,aAAa,CAAC,CAAC;EAAE,CAAC;EAAEqC,KAAK,EAAEiJ,gBAAgB;EAAEhJ,UAAU,EAAE;AAAO,CAAC,CAAC;AACvN;AACAgJ,gBAAgB,CAACzH,cAAc,GAAG,MAAM,CACpC;EAAEpB,IAAI,EAAEyG,SAAS;EAAEwD,UAAU,EAAE,CAAC;IAAEjK,IAAI,EAAElD,MAAM;IAAEmD,IAAI,EAAE,CAAC1C,aAAa;EAAG,CAAC;AAAE,CAAC,CAC9E;AACD,CAAC,YAAY;EAAE,CAAC,OAAOuC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC8I,gBAAgB,EAAE,CAAC;IACtG7I,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEyG,SAAS;MAAEwD,UAAU,EAAE,CAAC;QAC9CjK,IAAI,EAAElD,MAAM;QACZmD,IAAI,EAAE,CAAC1C,aAAa;MACxB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACjC;AACA,MAAM2M,QAAQ,GAAG,CACb,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAC/C,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EACxD,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAC9D,cAAc,EAAE,cAAc,CACjC;AACD;AACA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,SAASlN,cAAc,CAAC;EACvC;AACJ;AACA;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEuI,YAAY,EAAEtI,YAAY,EAAEC,OAAO,EAAE;IAChE,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAACqI,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACrI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,aAAa,GAAG,MAAM;IAC3B,IAAI,CAACtC,SAAS,GAAGqI,SAAS;IAC1B,IAAI,CAACpI,IAAI,GAAGoI,SAAS;IACrB,IAAI,CAAC4D,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB,IAAI,CAACzJ,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;EACI,IAAImI,MAAMA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACsB,UAAU;EAAE;EACvC;AACJ;AACA;AACA;EACI,IAAItB,MAAMA,CAAC7K,KAAK,EAAE;IACd,IAAI,CAACmM,UAAU,GAAGnM,KAAK,IAAI,GAAG;IAC9B,IAAI,CAACoM,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;EACI,IAAIxB,IAAIA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACsB,QAAQ;EAAE;EACnC;AACJ;AACA;AACA;EACI,IAAItB,IAAIA,CAAC5K,KAAK,EAAE;IACZ,IAAI,CAACkM,QAAQ,GAAGlM,KAAK,IAAI,GAAG;IAC5B,IAAI,CAACoM,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,aAAa,EAAE;MACpB,IAAI,CAAChK,OAAO,CAACiD,UAAU,CAAC,IAAI,CAAC+G,aAAa,EAAE,QAAQ,CAAC,CAChD7G,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACiG,cAAc,CAAC,CAAC,CACpCC,SAAS,CAAC,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9C,IAAI,CAACvD,OAAO,CAACiD,UAAU,CAAC,IAAI,CAACC,aAAa,EAAE,cAAc,CAAC,CACtDC,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACiG,cAAc,CAAC,CAAC,CACpCC,SAAS,CAAC,IAAI,CAACyG,aAAa,CAACvG,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,cAAcA,CAACc,OAAO,EAAE;IACpB;IACA,MAAMxB,MAAM,GAAGwB,OAAO,CAAC1G,KAAK;IAC5B;IACA,MAAMuM,WAAW,GAAGrH,MAAM,CAAC1E,KAAK,CAAC,GAAG,CAAC;IACrC,IAAI,CAACP,SAAS,GAAGsM,WAAW,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACrM,IAAI,GAAGqM,WAAW,CAAC,CAAC,CAAC,KAAKjE,SAAS,IAAIiE,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM;IACrE,IAAI,CAACjG,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,eAAeA,CAAC3G,KAAK,EAAE;IACnB;IACA,MAAMwM,eAAe,GAAG,IAAI,CAAC7B,YAAY,CAAC6B,eAAe,KAAK,KAAK;IACnE,IAAI,IAAI,CAACvM,SAAS,KAAKqI,SAAS,EAAE;MAC9B,IAAI,CAACrI,SAAS,GAAG,IAAI,CAACwM,oBAAoB,EAAE,gBAAkB,IAAI,CAACH,aAAa,EAAIE,eAAe,CAAC;IACxG;IACA,IAAI,IAAI,CAACtM,IAAI,KAAKoI,SAAS,EAAE;MACzB,IAAI,CAACpI,IAAI,GAAG,IAAI,CAAC0L,OAAO,EAAE,gBAAkB,IAAI,CAACU,aAAe,CAAC;IACrE;IACA;IACA,MAAMrM,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA,MAAMyM,YAAY,GAAGzM,SAAS,CAAC0M,UAAU,CAAC,KAAK,CAAC;IAChD;IACA,MAAMf,OAAO,GAAG,IAAI,CAAC1L,IAAI;IACzB,IAAIwM,YAAY,IAAId,OAAO,EAAE;MACzB,IAAI,CAACpJ,UAAU,GAAGoK,gBAAgB;IACtC,CAAC,MACI,IAAIF,YAAY,IAAI,CAACd,OAAO,EAAE;MAC/B,IAAI,CAACpJ,UAAU,GAAGqK,YAAY;IAClC,CAAC,MACI,IAAI,CAACH,YAAY,IAAId,OAAO,EAAE;MAC/B,IAAI,CAACpJ,UAAU,GAAGsK,mBAAmB;IACzC,CAAC,MACI,IAAI,CAACJ,YAAY,IAAI,CAACd,OAAO,EAAE;MAChC,IAAI,CAACpJ,UAAU,GAAGuK,eAAe;IACrC;IACA;IACA,MAAMhC,KAAK,GAAGK,MAAM,CAACpL,KAAK,CAAC,CAACgN,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC5C;IACA,MAAMC,KAAK,GAAG5N,aAAa,CAAC0L,KAAK,EAAE,IAAI,CAACmB,QAAQ,EAAE,IAAI,CAACC,UAAU,CAAC;IAClE,IAAI,CAACvE,SAAS,CAACqF,KAAK,CAACjC,IAAI,CAAC,GAAG,CAAC,EAAE;MAAE/K,SAAS;MAAE2L;IAAQ,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;EACIQ,aAAaA,CAAA,EAAG;IACZ;IACA,MAAMc,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAIA,cAAc,KAAK5E,SAAS,EAAE;MAC9B;MACA,MAAM2E,KAAK,GAAG5N,aAAa,CAAC6N,cAAc,GAAG,EAAE,EAAE,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACC,UAAU,CAAC;MAChF,IAAI,CAAC7J,OAAO,CAAC6K,aAAa,CAAC,IAAI,CAAC3H,aAAa,EAAE,IAAI,CAACjD,aAAa,EAAE0K,KAAK,CAACjC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvF;EACJ;AACJ;AACAiB,aAAa,CAAC/K,IAAI,GAAG,SAASkM,qBAAqBA,CAAC/L,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAI4K,aAAa,EAAEvM,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAACxD,aAAa,CAAC,EAAEM,MAAM,CAACkD,iBAAiB,CAAC8H,gBAAgB,CAAC,EAAEhL,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AAC5T+M,aAAa,CAACpJ,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEoK,aAAa;EAAElK,MAAM,EAAE;IAAE8I,MAAM,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;IAAED,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM;EAAE,CAAC;EAAE7H,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACzM;AACAiJ,aAAa,CAAChJ,cAAc,GAAG,MAAM,CACjC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAEyG,SAAS;EAAEwD,UAAU,EAAE,CAAC;IAAEjK,IAAI,EAAElD,MAAM;IAAEmD,IAAI,EAAE,CAAC1C,aAAa;EAAG,CAAC;AAAE,CAAC,EAC3E;EAAEyC,IAAI,EAAE6I;AAAiB,CAAC,EAC1B;EAAE7I,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD+M,aAAa,CAACoB,cAAc,GAAG;EAC3BxC,MAAM,EAAE,CAAC;IAAEhJ,IAAI,EAAEjD,KAAK;IAAEkD,IAAI,EAAE,CAAC,UAAU;EAAG,CAAC,CAAC;EAC9C8I,IAAI,EAAE,CAAC;IAAE/I,IAAI,EAAEjD,KAAK;IAAEkD,IAAI,EAAE,CAAC,QAAQ;EAAG,CAAC;AAC7C,CAAC;AACD,CAAC,YAAY;EAAE,CAAC,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACqK,aAAa,EAAE,CAAC;IACnGpK,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAEyG,SAAS;MAAEwD,UAAU,EAAE,CAAC;QACxGjK,IAAI,EAAElD,MAAM;QACZmD,IAAI,EAAE,CAAC1C,aAAa;MACxB,CAAC;IAAE,CAAC,EAAE;MAAEyC,IAAI,EAAE6I;IAAiB,CAAC,EAAE;MAAE7I,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE;IAAE2L,MAAM,EAAE,CAAC;MACnFhJ,IAAI,EAAEjD,KAAK;MACXkD,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAE8I,IAAI,EAAE,CAAC;MACP/I,IAAI,EAAEjD,KAAK;MACXkD,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAE,CAAC,EAAE,CAAC;AACnB,MAAMwL,oBAAoB,SAASrB,aAAa,CAAC;EAC7C/J,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAGgK,QAAQ;EAC1B;AACJ;AACAuB,oBAAoB,CAACpM,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIqM,iCAAiC;EAAE,OAAO,SAASC,4BAA4BA,CAACnM,CAAC,EAAE;IAAE,OAAO,CAACkM,iCAAiC,KAAKA,iCAAiC,GAAG7N,MAAM,CAAC4B,qBAAqB,CAACgM,oBAAoB,CAAC,CAAC,EAAEjM,CAAC,IAAIiM,oBAAoB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC1TA,oBAAoB,CAACzK,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEyL,oBAAoB;EAAEhK,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAE0L,MAAM,EAAE,QAAQ;IAAE,WAAW,EAAE,WAAW;IAAE,WAAW,EAAE,WAAW;IAAE,WAAW,EAAE,WAAW;IAAE,WAAW,EAAE,WAAW;IAAE,WAAW,EAAE,WAAW;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE,cAAc;IAAE,cAAc,EAAE;EAAe,CAAC;EAAE1K,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACr5B,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC0L,oBAAoB,EAAE,CAAC;IAC1GzL,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAEgK,QAAQ;MAAE/J,QAAQ,EAAEgK;IAAW,CAAC;EACrD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMa,YAAY,GAAG,IAAIrJ,GAAG,CAAC,CAAC;AAC9B;AACA,MAAMuJ,eAAe,GAAG,IAAIvJ,GAAG,CAAC,CAAC;AACjC;AACA,MAAMoJ,gBAAgB,GAAG,IAAIpJ,GAAG,CAAC,CAAC;AAClC;AACA,MAAMsJ,mBAAmB,GAAG,IAAItJ,GAAG,CAAC,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA,MAAMkK,qBAAqB,SAAS1O,YAAY,CAAC;EAC7C;AACJ;AACA;AACA;EACIgC,WAAWA,CAAChB,KAAK,EAAE;IACf,OAAO;MAAE2N,KAAK,EAAG3N,KAAK,IAAI4N,QAAQ,CAAC5N,KAAK,EAAE,EAAE,CAAC,IAAK;IAAG,CAAC;EAC1D;AACJ;AACA0N,qBAAqB,CAACxM,IAAI,GAAG,aAAc,YAAY;EAAE,IAAI2M,kCAAkC;EAAE,OAAO,SAASC,6BAA6BA,CAACzM,CAAC,EAAE;IAAE,OAAO,CAACwM,kCAAkC,KAAKA,kCAAkC,GAAGnO,MAAM,CAAC4B,qBAAqB,CAACoM,qBAAqB,CAAC,CAAC,EAAErM,CAAC,IAAIqM,qBAAqB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACjU;AAAmBA,qBAAqB,CAACnM,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASsM,6BAA6BA,CAAA,EAAG;IAAE,OAAO,IAAIJ,qBAAqB,CAAC,CAAC;EAAE,CAAC;EAAEjM,KAAK,EAAEiM,qBAAqB;EAAEhM,UAAU,EAAE;AAAO,CAAC,CAAC;AACpN,CAAC,YAAY;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC8L,qBAAqB,EAAE,CAAC;IAC3G7L,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMqM,QAAQ,GAAG,CACb,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EACnE,gBAAgB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAC5E,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAClF,mBAAmB,EAAE,mBAAmB,CAC3C;AACD;AACA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASlP,cAAc,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAE;IAClD,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAG,YAAY;IACjC,IAAI,CAACC,UAAU,GAAG0L,cAAc;IAChC,IAAI,CAACxL,IAAI,CAAC,CAAC;EACf;AACJ;AACAuL,kBAAkB,CAAC/M,IAAI,GAAG,SAASiN,0BAA0BA,CAAC9M,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAI4M,kBAAkB,EAAEvO,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAAC8K,qBAAqB,CAAC,EAAEhO,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AACvS+O,kBAAkB,CAACpL,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEoM,kBAAkB;EAAElL,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AAC7I;AACAiL,kBAAkB,CAAChL,cAAc,GAAG,MAAM,CACtC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAE6L;AAAsB,CAAC,EAC/B;EAAE7L,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD,CAAC,YAAY;EAAE,CAAC,OAAOyC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACqM,kBAAkB,EAAE,CAAC;IACxGpM,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAE6L;IAAsB,CAAC,EAAE;MAAE7L,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAC1K;AACA,MAAMgP,cAAc,GAAG,IAAI1K,GAAG,CAAC,CAAC;AAChC,MAAM4K,yBAAyB,SAASH,kBAAkB,CAAC;EACvD/L,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAGgM,QAAQ;EAC1B;AACJ;AACAK,yBAAyB,CAAClN,IAAI,GAAG,aAAc,YAAY;EAAE,IAAImN,sCAAsC;EAAE,OAAO,SAASC,iCAAiCA,CAACjN,CAAC,EAAE;IAAE,OAAO,CAACgN,sCAAsC,KAAKA,sCAAsC,GAAG3O,MAAM,CAAC4B,qBAAqB,CAAC8M,yBAAyB,CAAC,CAAC,EAAE/M,CAAC,IAAI+M,yBAAyB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC7VA,yBAAyB,CAACvL,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEuM,yBAAyB;EAAE9K,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAEwM,WAAW,EAAE,aAAa;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE;EAAoB,CAAC;EAAExL,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACjnC,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACwM,yBAAyB,EAAE,CAAC;IAC/GvM,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAEgM,UAAU;MAAEjM,MAAM,EAAEgM;IAAS,CAAC;EACrD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA,MAAMS,sBAAsB,SAASxP,YAAY,CAAC;EAC9C;AACJ;AACA;AACA;AACA;EACIgC,WAAWA,CAACyN,MAAM,EAAE5K,MAAM,EAAE;IACxB,IAAI4K,MAAM,KAAK,EAAE,EAAE;MACfA,MAAM,GAAG,GAAG;IAChB;IACA;IACA,MAAMnD,SAAS,GAAGF,MAAM,CAACqD,MAAM,CAAC,CAAC3N,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClD;IACA,MAAM4N,IAAI,GAAGtD,MAAM,CAACqD,MAAM,CAAC,CAAC3N,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9C,IAAI,CAAC4N,IAAI,IAAI,CAACpD,SAAS,IAAI,CAAC/D,KAAK,CAAC,CAACkH,MAAM,CAAC,EAAE;MACxCA,MAAM,GAAGA,MAAM,GAAG,GAAG;IACzB;IACA;IACA,MAAME,mBAAmB,GAAG9K,MAAM,CAAC+K,KAAK,GAAG,cAAc,GAAG,aAAa;IACzE;IACA,MAAMC,MAAM,GAAGjO,gBAAgB,CAACiD,MAAM,CAACqB,MAAM,CAAC,GAC1C;MAAE,CAACyJ,mBAAmB,GAAI,GAAEF,MAAO;IAAE,CAAC,GAAG;MAAE,YAAY,EAAG,GAAEA,MAAO;IAAE,CAAC;IAC1E,OAAOI,MAAM;EACjB;AACJ;AACAL,sBAAsB,CAACtN,IAAI,GAAG,aAAc,YAAY;EAAE,IAAI4N,mCAAmC;EAAE,OAAO,SAASC,8BAA8BA,CAAC1N,CAAC,EAAE;IAAE,OAAO,CAACyN,mCAAmC,KAAKA,mCAAmC,GAAGpP,MAAM,CAAC4B,qBAAqB,CAACkN,sBAAsB,CAAC,CAAC,EAAEnN,CAAC,IAAImN,sBAAsB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACxU;AAAmBA,sBAAsB,CAACjN,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASuN,8BAA8BA,CAAA,EAAG;IAAE,OAAO,IAAIP,sBAAsB,CAAC,CAAC;EAAE,CAAC;EAAE/M,KAAK,EAAE+M,sBAAsB;EAAE9M,UAAU,EAAE;AAAO,CAAC,CAAC;AACxN,CAAC,YAAY;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC4M,sBAAsB,EAAE,CAAC;IAC5G3M,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMsN,QAAQ,GAAG,CACb,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EACvE,iBAAiB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,oBAAoB,EAChF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,CAC7C;AACD;AACA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASnQ,cAAc,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAE+B,cAAc,EAAE7B,YAAY,EAAEC,OAAO,EAAE8E,MAAM,EAAE;IAC9D,KAAK,CAACjF,KAAK,EAAEE,YAAY,EAAE+E,MAAM,EAAE9E,OAAO,CAAC;IAC3C,IAAI,CAAC4B,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC3B,aAAa,GAAG,aAAa;IAClC,IAAI,CAACG,IAAI,CAAC,CAAC,IAAI,CAACwB,cAAc,CAACmB,MAAM,CAAC,CAAC;IACvC;IACA,IAAI,IAAI,CAACiH,aAAa,EAAE;MACpB,IAAI,CAAChK,OAAO,CACPiD,UAAU,CAAC,IAAI,CAAC+G,aAAa,EAAE,YAAY,CAAC,CAC5C7G,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACiG,cAAc,CAAC,CAAC,CACpCC,SAAS,CAAC,IAAI,CAACW,aAAa,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD;EACJ;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,eAAeA,CAAC3G,KAAK,GAAG,EAAE,EAAE;IACxB;IACA;IACA,MAAMkF,MAAM,GAAG,IAAI,CAACuH,oBAAoB,EAAE,gBAAkB,IAAI,CAACH,aAAa,EAAI,IAAI,CAAC;IACvF;IACA,MAAMsC,KAAK,GAAG,IAAI,CAAC1K,cAAc,CAAClE,KAAK,KAAK,KAAK;IACjD,IAAIkF,MAAM,KAAK,KAAK,IAAI0J,KAAK,EAAE;MAC3B,IAAI,CAACpM,UAAU,GAAG2M,qBAAqB;IAC3C,CAAC,MACI,IAAIjK,MAAM,KAAK,KAAK,IAAI,CAAC0J,KAAK,EAAE;MACjC,IAAI,CAACpM,UAAU,GAAG4M,qBAAqB;IAC3C,CAAC,MACI,IAAIlK,MAAM,KAAK,QAAQ,IAAI0J,KAAK,EAAE;MACnC,IAAI,CAACpM,UAAU,GAAG6M,wBAAwB;IAC9C,CAAC,MACI,IAAInK,MAAM,KAAK,QAAQ,IAAI,CAAC0J,KAAK,EAAE;MACpC,IAAI,CAACpM,UAAU,GAAG8M,wBAAwB;IAC9C;IACA,IAAI,CAAC1H,SAAS,CAAC5H,KAAK,GAAG,EAAE,EAAE;MAAEkF,MAAM;MAAE0J;IAAM,CAAC,CAAC;EACjD;AACJ;AACAM,mBAAmB,CAAChO,IAAI,GAAG,SAASqO,2BAA2BA,CAAClO,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAI6N,mBAAmB,EAAExP,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAAChD,MAAM,CAACN,cAAc,CAAC,EAAEI,MAAM,CAACkD,iBAAiB,CAAC4L,sBAAsB,CAAC,EAAE9O,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,EAAEQ,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,CAAC;AAAE,CAAC;AAC5ViQ,mBAAmB,CAACrM,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEqN,mBAAmB;EAAEnM,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AAC/I;AACAkM,mBAAmB,CAACjM,cAAc,GAAG,MAAM,CACvC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAEvC;AAAe,CAAC,EACxB;EAAEuC,IAAI,EAAE2M;AAAuB,CAAC,EAChC;EAAE3M,IAAI,EAAE3C;AAAgB,CAAC,EACzB;EAAE2C,IAAI,EAAE5C;AAAW,CAAC,CACvB;AACD,CAAC,YAAY;EAAE,CAAC,OAAO0C,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACsN,mBAAmB,EAAE,CAAC;IACzGrN,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAEjC,MAAM,CAACN;IAAe,CAAC,EAAE;MAAEuC,IAAI,EAAE2M;IAAuB,CAAC,EAAE;MAAE3M,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,EAAE;MAAE2C,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAC5M,MAAMuQ,0BAA0B,SAASN,mBAAmB,CAAC;EACzDhN,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAGiN,QAAQ;EAC1B;AACJ;AACAQ,0BAA0B,CAACtO,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIuO,uCAAuC;EAAE,OAAO,SAASC,kCAAkCA,CAACrO,CAAC,EAAE;IAAE,OAAO,CAACoO,uCAAuC,KAAKA,uCAAuC,GAAG/P,MAAM,CAAC4B,qBAAqB,CAACkO,0BAA0B,CAAC,CAAC,EAAEnO,CAAC,IAAImO,0BAA0B,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACpWA,0BAA0B,CAAC3M,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAE2N,0BAA0B;EAAElM,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAE4N,YAAY,EAAE,cAAc;IAAE,iBAAiB,EAAE,iBAAiB;IAAE,iBAAiB,EAAE,iBAAiB;IAAE,iBAAiB,EAAE,iBAAiB;IAAE,iBAAiB,EAAE,iBAAiB;IAAE,iBAAiB,EAAE,iBAAiB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE,oBAAoB;IAAE,oBAAoB,EAAE;EAAqB,CAAC;EAAE5M,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AAC7pC,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC4N,0BAA0B,EAAE,CAAC;IAChH3N,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAEiN,UAAU;MAAElN,MAAM,EAAEiN;IAAS,CAAC;EACrD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMG,qBAAqB,GAAG,IAAI3L,GAAG,CAAC,CAAC;AACvC;AACA,MAAM6L,wBAAwB,GAAG,IAAI7L,GAAG,CAAC,CAAC;AAC1C;AACA,MAAM4L,qBAAqB,GAAG,IAAI5L,GAAG,CAAC,CAAC;AACvC;AACA,MAAM8L,wBAAwB,GAAG,IAAI9L,GAAG,CAAC,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA,MAAMoM,qBAAqB,SAAS5Q,YAAY,CAAC;EAC7C;AACJ;AACA;AACA;EACIgC,WAAWA,CAACC,KAAK,EAAE;IACfA,KAAK,GAAGA,KAAK,IAAI,SAAS;IAC1B;IACA,MAAM4N,MAAM,GAAG,CAAC,CAAC;IACjB;IACA,QAAQ5N,KAAK;MACT,KAAK,OAAO;QACR4N,MAAM,CAAC,YAAY,CAAC,GAAG,YAAY;QACnC;MACJ,KAAK,KAAK;QACNA,MAAM,CAAC,YAAY,CAAC,GAAG,UAAU;QACjC;MACJ;QACIA,MAAM,CAAC,YAAY,CAAC,GAAG5N,KAAK;QAC5B;IACR;IACA,OAAO4N,MAAM;EACjB;AACJ;AACAe,qBAAqB,CAAC1O,IAAI,GAAG,aAAc,YAAY;EAAE,IAAI2O,kCAAkC;EAAE,OAAO,SAASC,6BAA6BA,CAACzO,CAAC,EAAE;IAAE,OAAO,CAACwO,kCAAkC,KAAKA,kCAAkC,GAAGnQ,MAAM,CAAC4B,qBAAqB,CAACsO,qBAAqB,CAAC,CAAC,EAAEvO,CAAC,IAAIuO,qBAAqB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AACjU;AAAmBA,qBAAqB,CAACrO,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASsO,6BAA6BA,CAAA,EAAG;IAAE,OAAO,IAAIF,qBAAqB,CAAC,CAAC;EAAE,CAAC;EAAEnO,KAAK,EAAEmO,qBAAqB;EAAElO,UAAU,EAAE;AAAO,CAAC,CAAC;AACpN,CAAC,YAAY;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACgO,qBAAqB,EAAE,CAAC;IAC3G/N,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMqO,QAAQ,GAAG,CACb,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EACnE,gBAAgB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAC5E,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAClF,mBAAmB,EAAE,mBAAmB,CAC3C;AACD;AACA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASlR,cAAc,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAE;IAClD,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAG,YAAY;IACjC,IAAI,CAACC,UAAU,GAAG0N,cAAc;IAChC,IAAI,CAACxN,IAAI,CAAC,CAAC;EACf;AACJ;AACAuN,kBAAkB,CAAC/O,IAAI,GAAG,SAASiP,0BAA0BA,CAAC9O,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAI4O,kBAAkB,EAAEvQ,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAACgN,qBAAqB,CAAC,EAAElQ,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AACvS+Q,kBAAkB,CAACpN,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEoO,kBAAkB;EAAElN,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AAC7I;AACAiN,kBAAkB,CAAChN,cAAc,GAAG,MAAM,CACtC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAE+N;AAAsB,CAAC,EAC/B;EAAE/N,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD,CAAC,YAAY;EAAE,CAAC,OAAOyC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACqO,kBAAkB,EAAE,CAAC;IACxGpO,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAE+N;IAAsB,CAAC,EAAE;MAAE/N,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAC1K;AACA,MAAMgR,cAAc,GAAG,IAAI1M,GAAG,CAAC,CAAC;AAChC,MAAM4M,yBAAyB,SAASH,kBAAkB,CAAC;EACvD/N,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAGgO,QAAQ;EAC1B;AACJ;AACAK,yBAAyB,CAAClP,IAAI,GAAG,aAAc,YAAY;EAAE,IAAImP,sCAAsC;EAAE,OAAO,SAASC,iCAAiCA,CAACjP,CAAC,EAAE;IAAE,OAAO,CAACgP,sCAAsC,KAAKA,sCAAsC,GAAG3Q,MAAM,CAAC4B,qBAAqB,CAAC8O,yBAAyB,CAAC,CAAC,EAAE/O,CAAC,IAAI+O,yBAAyB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC7VA,yBAAyB,CAACvN,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEuO,yBAAyB;EAAE9M,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAEwO,WAAW,EAAE,aAAa;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,gBAAgB,EAAE,gBAAgB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE,mBAAmB;IAAE,mBAAmB,EAAE;EAAoB,CAAC;EAAExN,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACjnC,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACwO,yBAAyB,EAAE,CAAC;IAC/GvO,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAEgO,UAAU;MAAEjO,MAAM,EAAEgO;IAAS,CAAC;EACrD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,aAAa,GAAG;EAClB,QAAQ,EAAE,CAAC;EACX,OAAO,EAAE,MAAM;EACf,QAAQ,EAAE,MAAM;EAChB,WAAW,EAAE,MAAM;EACnB,YAAY,EAAE;AAClB,CAAC;AACD,MAAMC,oBAAoB,SAASzR,YAAY,CAAC;EAC5C;AACJ;AACA;AACA;EACIgC,WAAWA,CAAC0P,MAAM,EAAE;IAChB,OAAOF,aAAa;EACxB;AACJ;AACAC,oBAAoB,CAACvP,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIyP,iCAAiC;EAAE,OAAO,SAASC,4BAA4BA,CAACvP,CAAC,EAAE;IAAE,OAAO,CAACsP,iCAAiC,KAAKA,iCAAiC,GAAGjR,MAAM,CAAC4B,qBAAqB,CAACmP,oBAAoB,CAAC,CAAC,EAAEpP,CAAC,IAAIoP,oBAAoB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC1T;AAAmBA,oBAAoB,CAAClP,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAASoP,4BAA4BA,CAAA,EAAG;IAAE,OAAO,IAAIH,oBAAoB,CAAC,CAAC;EAAE,CAAC;EAAEhP,KAAK,EAAEgP,oBAAoB;EAAE/O,UAAU,EAAE;AAAO,CAAC,CAAC;AAChN,CAAC,YAAY;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC6O,oBAAoB,EAAE,CAAC;IAC1G5O,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmP,iBAAiB,SAAS9R,cAAc,CAAC;EAC3C;AACJ;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAE;IAClD,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAACE,UAAU,GAAGsO,aAAa;IAC/B,IAAI,CAAClJ,SAAS,CAAC,EAAE,CAAC;EACtB;AACJ;AACAiJ,iBAAiB,CAAC3P,IAAI,GAAG,SAAS6P,yBAAyBA,CAAC1P,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIwP,iBAAiB,EAAEnR,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAAC6N,oBAAoB,CAAC,EAAE/Q,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AACnS2R,iBAAiB,CAAChO,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEgP,iBAAiB;EAAEvN,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;EAAEP,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACpM;AACA6N,iBAAiB,CAAC5N,cAAc,GAAG,MAAM,CACrC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAE4O;AAAqB,CAAC,EAC9B;EAAE5O,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD,CAAC,YAAY;EAAE,CAAC,OAAOyC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACiP,iBAAiB,EAAE,CAAC;IACvGhP,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAG;IAAwB,CAAC;EACjD,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAE4O;IAAqB,CAAC,EAAE;MAAE5O,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzK;AACA,MAAM4R,aAAa,GAAG,IAAItN,GAAG,CAAC,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA,MAAMwN,uBAAuB,SAAShS,YAAY,CAAC;EAC/C;AACJ;AACA;AACA;AACA;EACIgC,WAAWA,CAACiQ,KAAK,EAAEpN,MAAM,EAAE;IACvB;IACA,MAAM6H,GAAG,GAAG,CAAC,CAAC;IACd,MAAM,CAACwF,QAAQ,EAAEC,SAAS,CAAC,GAAGF,KAAK,CAACzQ,KAAK,CAAC,GAAG,CAAC;IAC9C;IACA,QAAQ0Q,QAAQ;MACZ,KAAK,QAAQ;QACTxF,GAAG,CAAC,iBAAiB,CAAC,GAAG,QAAQ;QACjC;MACJ,KAAK,cAAc;QACfA,GAAG,CAAC,iBAAiB,CAAC,GAAG,cAAc;QACvC;MACJ,KAAK,eAAe;QAChBA,GAAG,CAAC,iBAAiB,CAAC,GAAG,eAAe;QACxC;MACJ,KAAK,cAAc;QACfA,GAAG,CAAC,iBAAiB,CAAC,GAAG,cAAc;QACvC;MACJ,KAAK,KAAK;MACV,KAAK,UAAU;QACXA,GAAG,CAAC,iBAAiB,CAAC,GAAG,UAAU;QACnC;MACJ,KAAK,OAAO;MACZ,KAAK,YAAY;MACjB;QACIA,GAAG,CAAC,iBAAiB,CAAC,GAAG,YAAY,CAAC,CAAC;QACvC;IACR;IACA;IACA,QAAQyF,SAAS;MACb,KAAK,OAAO;MACZ,KAAK,YAAY;QACbzF,GAAG,CAAC,aAAa,CAAC,GAAGA,GAAG,CAAC,eAAe,CAAC,GAAG,YAAY;QACxD;MACJ,KAAK,QAAQ;QACTA,GAAG,CAAC,aAAa,CAAC,GAAGA,GAAG,CAAC,eAAe,CAAC,GAAG,QAAQ;QACpD;MACJ,KAAK,KAAK;MACV,KAAK,UAAU;QACXA,GAAG,CAAC,aAAa,CAAC,GAAGA,GAAG,CAAC,eAAe,CAAC,GAAG,UAAU;QACtD;MACJ,KAAK,eAAe;QAChBA,GAAG,CAAC,eAAe,CAAC,GAAG,eAAe;QACtCA,GAAG,CAAC,aAAa,CAAC,GAAG,SAAS;QAC9B;MACJ,KAAK,cAAc;QACfA,GAAG,CAAC,eAAe,CAAC,GAAG,cAAc;QACrCA,GAAG,CAAC,aAAa,CAAC,GAAG,SAAS;QAC9B;MACJ,KAAK,UAAU;QACXA,GAAG,CAAC,eAAe,CAAC,GAAG,SAAS;QAChCA,GAAG,CAAC,aAAa,CAAC,GAAG,UAAU;QAC/B;MACJ,KAAK,SAAS;MACd;QAAS;QACLA,GAAG,CAAC,aAAa,CAAC,GAAGA,GAAG,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC,CAAC;QACvD;IACR;IACA,OAAQ,iBAAkBrB,YAAY,CAACqB,GAAG,EAAE;QACxC,SAAS,EAAE7H,MAAM,CAACtD,MAAM,GAAG,aAAa,GAAG,MAAM;QACjD,gBAAgB,EAAEsD,MAAM,CAACqB,MAAM;QAC/B,YAAY,EAAE,YAAY;QAC1B,WAAW,EAAEiM,SAAS,KAAK,SAAS,GAChC,CAACvQ,gBAAgB,CAACiD,MAAM,CAACqB,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI;QAC3D,YAAY,EAAEiM,SAAS,KAAK,SAAS,GACjCvQ,gBAAgB,CAACiD,MAAM,CAACqB,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG;MAC1D,CAAC;IAAC;EACN;AACJ;AACA8L,uBAAuB,CAAC9P,IAAI,GAAG,aAAc,YAAY;EAAE,IAAIkQ,oCAAoC;EAAE,OAAO,SAASC,+BAA+BA,CAAChQ,CAAC,EAAE;IAAE,OAAO,CAAC+P,oCAAoC,KAAKA,oCAAoC,GAAG1R,MAAM,CAAC4B,qBAAqB,CAAC0P,uBAAuB,CAAC,CAAC,EAAE3P,CAAC,IAAI2P,uBAAuB,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC/U;AAAmBA,uBAAuB,CAACzP,KAAK,GAAG1C,kBAAkB,CAAC;EAAE2C,OAAO,EAAE,SAAS6P,+BAA+BA,CAAA,EAAG;IAAE,OAAO,IAAIL,uBAAuB,CAAC,CAAC;EAAE,CAAC;EAAEvP,KAAK,EAAEuP,uBAAuB;EAAEtP,UAAU,EAAE;AAAO,CAAC,CAAC;AAC5N,CAAC,YAAY;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACoP,uBAAuB,EAAE,CAAC;IAC7GnP,IAAI,EAAErD,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAM4P,QAAQ,GAAG,CACb,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,EAC3E,kBAAkB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,qBAAqB,EACpF,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,EAC1F,qBAAqB,EAAE,qBAAqB,CAC/C;AACD;AACA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAASzS,cAAc,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;EACImD,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAE;IAClD,KAAK,CAACH,KAAK,EAAEE,YAAY,EAAED,UAAU,EAAEE,OAAO,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAG,cAAc;IACnC,IAAI,CAAC2C,MAAM,GAAG,KAAK,CAAC,CAAC;IACrB;IACA,IAAI,CAAC3E,MAAM,GAAG,KAAK,CAAC,CAAC;IACrB,IAAI,CAACmC,IAAI,CAAC,CAAC;IACX,IAAI,CAACJ,OAAO,CAACiD,UAAU,CAAC,IAAI,CAACC,aAAa,EAAE,QAAQ,CAAC,CAChDC,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACiG,cAAc,CAAC,CAAC,CACpCC,SAAS,CAAC,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClD;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;EACIc,eAAeA,CAAC3G,KAAK,EAAE;IACnB;IACA,MAAMkF,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,KAAK;IACnC;IACA,MAAM3E,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI2E,MAAM,KAAK,KAAK,IAAI3E,MAAM,EAAE;MAC5B,IAAI,CAACiC,UAAU,GAAGiP,gCAAgC;IACtD,CAAC,MACI,IAAIvM,MAAM,KAAK,KAAK,IAAI,CAAC3E,MAAM,EAAE;MAClC,IAAI,CAACiC,UAAU,GAAGkP,0BAA0B;IAChD,CAAC,MACI,IAAIxM,MAAM,KAAK,aAAa,IAAI3E,MAAM,EAAE;MACzC,IAAI,CAACiC,UAAU,GAAGmP,mCAAmC;IACzD,CAAC,MACI,IAAIzM,MAAM,KAAK,aAAa,IAAI,CAAC3E,MAAM,EAAE;MAC1C,IAAI,CAACiC,UAAU,GAAGoP,6BAA6B;IACnD,CAAC,MACI,IAAI1M,MAAM,KAAK,QAAQ,IAAI3E,MAAM,EAAE;MACpC,IAAI,CAACiC,UAAU,GAAGqP,8BAA8B;IACpD,CAAC,MACI,IAAI3M,MAAM,KAAK,QAAQ,IAAI,CAAC3E,MAAM,EAAE;MACrC,IAAI,CAACiC,UAAU,GAAGsP,wBAAwB;IAC9C,CAAC,MACI,IAAI5M,MAAM,KAAK,gBAAgB,IAAI3E,MAAM,EAAE;MAC5C,IAAI,CAACiC,UAAU,GAAGuP,iCAAiC;IACvD,CAAC,MACI,IAAI7M,MAAM,KAAK,gBAAgB,IAAI,CAAC3E,MAAM,EAAE;MAC7C,IAAI,CAACiC,UAAU,GAAGwP,2BAA2B;IACjD;IACA,IAAI,CAACpK,SAAS,CAAC5H,KAAK,EAAE;MAAEkF,MAAM;MAAE3E;IAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqF,cAAcA,CAACc,OAAO,EAAE;IACpB;IACA,MAAMuL,UAAU,GAAGvL,OAAO,CAAC1G,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC;IAC3C,IAAI,CAAC0E,MAAM,GAAG+M,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAI,CAAC1R,MAAM,GAAGmG,OAAO,CAAC1G,KAAK,CAACkS,QAAQ,CAAC,QAAQ,CAAC;IAC9C,IAAI,CAACpS,aAAa,CAACW,IAAI;IAAE;AACjC;AACA;AACA;IACQC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAACwE,MAAO,CAAC,EAAE;MACtB,IAAI,CAACA,MAAM,GAAG,KAAK;IACvB;IACA,IAAI,CAACoB,aAAa,CAAC,CAAC;EACxB;AACJ;AACAkL,oBAAoB,CAACtQ,IAAI,GAAG,SAASiR,4BAA4BA,CAAC9Q,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAImQ,oBAAoB,EAAE9R,MAAM,CAACkD,iBAAiB,CAAClD,MAAM,CAACnB,UAAU,CAAC,EAAEmB,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACV,UAAU,CAAC,EAAES,MAAM,CAACkD,iBAAiB,CAACoO,uBAAuB,CAAC,EAAEtR,MAAM,CAACkD,iBAAiB,CAACjD,MAAM,CAACT,eAAe,CAAC,CAAC;AAAE,CAAC;AAC/SsS,oBAAoB,CAAC3O,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAE2P,oBAAoB;EAAEzO,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACjJ;AACAwO,oBAAoB,CAACvO,cAAc,GAAG,MAAM,CACxC;EAAEpB,IAAI,EAAEtD;AAAW,CAAC,EACpB;EAAEsD,IAAI,EAAE5C;AAAW,CAAC,EACpB;EAAE4C,IAAI,EAAEmP;AAAwB,CAAC,EACjC;EAAEnP,IAAI,EAAE3C;AAAgB,CAAC,CAC5B;AACD,CAAC,YAAY;EAAE,CAAC,OAAOyC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC4P,oBAAoB,EAAE,CAAC;IAC1G3P,IAAI,EAAEvD;EACV,CAAC,CAAC,EAAE,YAAY;IAAE,OAAO,CAAC;MAAEuD,IAAI,EAAEnC,MAAM,CAACnB;IAAW,CAAC,EAAE;MAAEsD,IAAI,EAAElC,MAAM,CAACV;IAAW,CAAC,EAAE;MAAE4C,IAAI,EAAEmP;IAAwB,CAAC,EAAE;MAAEnP,IAAI,EAAElC,MAAM,CAACT;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAC5K,MAAMkT,2BAA2B,SAASZ,oBAAoB,CAAC;EAC3DtP,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGiB,SAAS,CAAC;IACnB,IAAI,CAACpB,MAAM,GAAGuP,QAAQ;EAC1B;AACJ;AACAc,2BAA2B,CAAClR,IAAI,GAAG,aAAc,YAAY;EAAE,IAAImR,wCAAwC;EAAE,OAAO,SAASC,mCAAmCA,CAACjR,CAAC,EAAE;IAAE,OAAO,CAACgR,wCAAwC,KAAKA,wCAAwC,GAAG3S,MAAM,CAAC4B,qBAAqB,CAAC8Q,2BAA2B,CAAC,CAAC,EAAE/Q,CAAC,IAAI+Q,2BAA2B,CAAC;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAC3WA,2BAA2B,CAACvP,IAAI,GAAG,aAAcnD,MAAM,CAACoD,iBAAiB,CAAC;EAAEjB,IAAI,EAAEuQ,2BAA2B;EAAE9O,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,CAAC,CAAC;EAAEvB,MAAM,EAAE;IAAEwQ,aAAa,EAAE,eAAe;IAAE,kBAAkB,EAAE,kBAAkB;IAAE,kBAAkB,EAAE,kBAAkB;IAAE,kBAAkB,EAAE,kBAAkB;IAAE,kBAAkB,EAAE,kBAAkB;IAAE,kBAAkB,EAAE,kBAAkB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE,qBAAqB;IAAE,qBAAqB,EAAE;EAAsB,CAAC;EAAExP,QAAQ,EAAE,CAACrD,MAAM,CAACsD,0BAA0B;AAAE,CAAC,CAAC;AACzsC,CAAC,YAAY;EAAE,CAAC,OAAOrB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAACwQ,2BAA2B,EAAE,CAAC;IACjHvQ,IAAI,EAAEvD,SAAS;IACfwD,IAAI,EAAE,CAAC;MAAEE,QAAQ,EAAEuP,UAAU;MAAExP,MAAM,EAAEuP;IAAS,CAAC;EACrD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB;AACA,MAAMI,0BAA0B,GAAG,IAAIlO,GAAG,CAAC,CAAC;AAC5C;AACA,MAAMsO,wBAAwB,GAAG,IAAItO,GAAG,CAAC,CAAC;AAC1C;AACA,MAAMoO,6BAA6B,GAAG,IAAIpO,GAAG,CAAC,CAAC;AAC/C;AACA,MAAMwO,2BAA2B,GAAG,IAAIxO,GAAG,CAAC,CAAC;AAC7C;AACA,MAAMiO,gCAAgC,GAAG,IAAIjO,GAAG,CAAC,CAAC;AAClD;AACA,MAAMqO,8BAA8B,GAAG,IAAIrO,GAAG,CAAC,CAAC;AAChD;AACA,MAAMmO,mCAAmC,GAAG,IAAInO,GAAG,CAAC,CAAC;AACrD;AACA,MAAMuO,iCAAiC,GAAG,IAAIvO,GAAG,CAAC,CAAC;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgP,cAAc,GAAG,CACnBtP,sBAAsB,EACtBiG,yBAAyB,EACzBiJ,2BAA2B,EAC3BhE,yBAAyB,EACzBoB,0BAA0B,EAC1BqB,iBAAiB,EACjBT,yBAAyB,EACzB9C,oBAAoB,CACvB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMmF,UAAU,CAAC;AAEjBA,UAAU,CAACvR,IAAI,GAAG,SAASwR,kBAAkBA,CAACrR,CAAC,EAAE;EAAE,OAAO,KAAKA,CAAC,IAAIoR,UAAU,EAAE,CAAC;AAAE,CAAC;AACpFA,UAAU,CAACE,IAAI,GAAG,aAAcjT,MAAM,CAACkT,gBAAgB,CAAC;EAAE/Q,IAAI,EAAE4Q;AAAW,CAAC,CAAC;AAC7EA,UAAU,CAACI,IAAI,GAAG,aAAcnT,MAAM,CAACoT,gBAAgB,CAAC;EAAEC,OAAO,EAAE,CAAC5T,UAAU,EAAEI,UAAU;AAAE,CAAC,CAAC;AAC9F,CAAC,YAAY;EAAE,CAAC,OAAOoC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKjC,MAAM,CAACkC,iBAAiB,CAAC6Q,UAAU,EAAE,CAAC;IAChG5Q,IAAI,EAAEpD,QAAQ;IACdqD,IAAI,EAAE,CAAC;MACCiR,OAAO,EAAE,CAAC5T,UAAU,EAAEI,UAAU,CAAC;MACjCyT,YAAY,EAAE,CAAC,GAAGR,cAAc,CAAC;MACjCS,OAAO,EAAE,CAAC,GAAGT,cAAc;IAC/B,CAAC;EACT,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AACzB,CAAC,YAAY;EAAE,CAAC,OAAOU,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKxT,MAAM,CAACyT,kBAAkB,CAACV,UAAU,EAAE;IAAEO,YAAY,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAC9P,sBAAsB,EAAEiG,yBAAyB,EAAEiJ,2BAA2B,EAAEhE,yBAAyB,EAAEoB,0BAA0B,EAAEqB,iBAAiB,EAAET,yBAAyB,EAAE9C,oBAAoB,CAAC;IAAE,CAAC;IAAEyF,OAAO,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAC5T,UAAU,EAAEI,UAAU,CAAC;IAAE,CAAC;IAAE0T,OAAO,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAC/P,sBAAsB,EAAEiG,yBAAyB,EAAEiJ,2BAA2B,EAAEhE,yBAAyB,EAAEoB,0BAA0B,EAAEqB,iBAAiB,EAAET,yBAAyB,EAAE9C,oBAAoB,CAAC;IAAE;EAAE,CAAC,CAAC;AAAE,CAAC,EAAE,CAAC;;AAE7oB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAASmF,UAAU,EAAE/H,gBAAgB,EAAEuB,aAAa,EAAEqB,oBAAoB,EAAEsC,qBAAqB,EAAEK,kBAAkB,EAAEG,yBAAyB,EAAEK,oBAAoB,EAAEI,iBAAiB,EAAErC,sBAAsB,EAAEU,mBAAmB,EAAEM,0BAA0B,EAAE9B,qBAAqB,EAAEO,kBAAkB,EAAEG,yBAAyB,EAAErN,kBAAkB,EAAEkB,eAAe,EAAEiB,sBAAsB,EAAE8N,uBAAuB,EAAEQ,oBAAoB,EAAEY,2BAA2B,EAAE1O,qBAAqB,EAAEsB,kBAAkB,EAAEmE,yBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nexport function skipLast(count) {\n  return source => source.lift(new SkipLastOperator(count));\n}\nclass SkipLastOperator {\n  constructor(_skipCount) {\n    this._skipCount = _skipCount;\n    if (this._skipCount < 0) {\n      throw new ArgumentOutOfRangeError();\n    }\n  }\n  call(subscriber, source) {\n    if (this._skipCount === 0) {\n      return source.subscribe(new Subscriber(subscriber));\n    } else {\n      return source.subscribe(new SkipLastSubscriber(subscriber, this._skipCount));\n    }\n  }\n}\nclass SkipLastSubscriber extends Subscriber {\n  constructor(destination, _skipCount) {\n    super(destination);\n    this._skipCount = _skipCount;\n    this._count = 0;\n    this._ring = new Array(_skipCount);\n  }\n  _next(value) {\n    const skipCount = this._skipCount;\n    const count = this._count++;\n    if (count < skipCount) {\n      this._ring[count] = value;\n    } else {\n      const currentIndex = count % skipCount;\n      const ring = this._ring;\n      const oldValue = ring[currentIndex];\n      ring[currentIndex] = value;\n      this.destination.next(oldValue);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "ArgumentOutOfRangeError", "skipLast", "count", "source", "lift", "SkipLastOperator", "constructor", "_skipCount", "call", "subscriber", "subscribe", "SkipLastSubscriber", "destination", "_count", "_ring", "Array", "_next", "value", "skip<PERSON><PERSON>nt", "currentIndex", "ring", "oldValue", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/skipLast.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nexport function skipLast(count) {\n    return (source) => source.lift(new SkipLastOperator(count));\n}\nclass SkipLastOperator {\n    constructor(_skipCount) {\n        this._skipCount = _skipCount;\n        if (this._skipCount < 0) {\n            throw new ArgumentOutOfRangeError;\n        }\n    }\n    call(subscriber, source) {\n        if (this._skipCount === 0) {\n            return source.subscribe(new Subscriber(subscriber));\n        }\n        else {\n            return source.subscribe(new SkipLastSubscriber(subscriber, this._skipCount));\n        }\n    }\n}\nclass SkipLastSubscriber extends Subscriber {\n    constructor(destination, _skipCount) {\n        super(destination);\n        this._skipCount = _skipCount;\n        this._count = 0;\n        this._ring = new Array(_skipCount);\n    }\n    _next(value) {\n        const skipCount = this._skipCount;\n        const count = this._count++;\n        if (count < skipCount) {\n            this._ring[count] = value;\n        }\n        else {\n            const currentIndex = count % skipCount;\n            const ring = this._ring;\n            const oldValue = ring[currentIndex];\n            ring[currentIndex] = value;\n            this.destination.next(oldValue);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC5B,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,gBAAgB,CAACH,KAAK,CAAC,CAAC;AAC/D;AACA,MAAMG,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAACA,UAAU,GAAG,CAAC,EAAE;MACrB,MAAM,IAAIP,uBAAuB,CAAD,CAAC;IACrC;EACJ;EACAQ,IAAIA,CAACC,UAAU,EAAEN,MAAM,EAAE;IACrB,IAAI,IAAI,CAACI,UAAU,KAAK,CAAC,EAAE;MACvB,OAAOJ,MAAM,CAACO,SAAS,CAAC,IAAIX,UAAU,CAACU,UAAU,CAAC,CAAC;IACvD,CAAC,MACI;MACD,OAAON,MAAM,CAACO,SAAS,CAAC,IAAIC,kBAAkB,CAACF,UAAU,EAAE,IAAI,CAACF,UAAU,CAAC,CAAC;IAChF;EACJ;AACJ;AACA,MAAMI,kBAAkB,SAASZ,UAAU,CAAC;EACxCO,WAAWA,CAACM,WAAW,EAAEL,UAAU,EAAE;IACjC,KAAK,CAACK,WAAW,CAAC;IAClB,IAAI,CAACL,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACM,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,IAAIC,KAAK,CAACR,UAAU,CAAC;EACtC;EACAS,KAAKA,CAACC,KAAK,EAAE;IACT,MAAMC,SAAS,GAAG,IAAI,CAACX,UAAU;IACjC,MAAML,KAAK,GAAG,IAAI,CAACW,MAAM,EAAE;IAC3B,IAAIX,KAAK,GAAGgB,SAAS,EAAE;MACnB,IAAI,CAACJ,KAAK,CAACZ,KAAK,CAAC,GAAGe,KAAK;IAC7B,CAAC,MACI;MACD,MAAME,YAAY,GAAGjB,KAAK,GAAGgB,SAAS;MACtC,MAAME,IAAI,GAAG,IAAI,CAACN,KAAK;MACvB,MAAMO,QAAQ,GAAGD,IAAI,CAACD,YAAY,CAAC;MACnCC,IAAI,CAACD,YAAY,CAAC,GAAGF,KAAK;MAC1B,IAAI,CAACL,WAAW,CAACU,IAAI,CAACD,QAAQ,CAAC;IACnC;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
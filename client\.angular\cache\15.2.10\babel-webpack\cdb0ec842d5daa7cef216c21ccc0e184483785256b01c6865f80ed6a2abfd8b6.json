{"ast": null, "code": "export const MODULE_CONFIG = {\n  'OnInit': 'hmrOnInit',\n  'OnStatus': 'hmrOnStatus',\n  'OnCheck': 'hmrOnCheck',\n  'OnDecline': 'hmrOnDecline',\n  'OnDestroy': 'hmr<PERSON>n<PERSON><PERSON><PERSON>',\n  'AfterDestroy': 'hmrA<PERSON><PERSON><PERSON>roy'\n};\nexport function hmrModule(MODULE_REF, MODULE, CONFIG = MODULE_CONFIG) {\n  if (MODULE['hot']) {\n    MODULE['hot']['accept']();\n    if (MODULE_REF.instance[MODULE_CONFIG['OnInit']]) {\n      if (MODULE['hot']['data']) {\n        MODULE_REF.instance[MODULE_CONFIG['OnInit']](MODULE['hot']['data']);\n      }\n    }\n    if (MODULE_REF.instance[MODULE_CONFIG['OnStatus']]) {\n      MODULE['hot']['apply'](function hmrOnStatus(status) {\n        MODULE_REF.instance[MODULE_CONFIG['OnStatus']](status);\n      });\n    }\n    if (MODULE_REF.instance[MODULE_CONFIG['OnCheck']]) {\n      MODULE['hot']['check'](function hmrOnCheck(err, outdatedModules) {\n        MODULE_REF.instance[MODULE_CONFIG['OnCheck']](err, outdatedModules);\n      });\n    }\n    if (MODULE_REF.instance[MODULE_CONFIG['OnDecline']]) {\n      MODULE['hot']['decline'](function hmrOnDecline(dependencies) {\n        MODULE_REF.instance[MODULE_CONFIG['OnDecline']](dependencies);\n      });\n    }\n    MODULE['hot']['dispose'](function hmrOnDestroy(store) {\n      if (MODULE_REF.instance[MODULE_CONFIG['OnDestroy']]) {\n        MODULE_REF.instance[MODULE_CONFIG['OnDestroy']](store);\n      }\n      MODULE_REF.destroy();\n      if (MODULE_REF.instance[MODULE_CONFIG['AfterDestroy']]) {\n        MODULE_REF.instance[MODULE_CONFIG['AfterDestroy']](store);\n      }\n    });\n  }\n  return MODULE_REF;\n}", "map": {"version": 3, "names": ["MODULE_CONFIG", "hmrModule", "MODULE_REF", "MODULE", "CONFIG", "instance", "hmrOnStatus", "status", "hmrOnCheck", "err", "outdatedModules", "hmrOnDecline", "dependencies", "hmrOnDestroy", "store", "destroy"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angularclass/hmr/dist/hmr.js"], "sourcesContent": ["export const MODULE_CONFIG = {\n    'OnInit': 'hmrOnInit',\n    'OnStatus': 'hmrOnStatus',\n    'OnCheck': 'hmrOnCheck',\n    'OnDecline': 'hmrOnDecline',\n    'OnDestroy': 'hmr<PERSON>n<PERSON><PERSON><PERSON>',\n    'AfterDestroy': 'hmrA<PERSON><PERSON><PERSON>roy'\n};\nexport function hmrModule(MODULE_REF, MODULE, CONFIG = MODULE_CONFIG) {\n    if (MODULE['hot']) {\n        MODULE['hot']['accept']();\n        if (MODULE_REF.instance[MODULE_CONFIG['OnInit']]) {\n            if (MODULE['hot']['data']) {\n                MODULE_REF.instance[MODULE_CONFIG['OnInit']](MODULE['hot']['data']);\n            }\n        }\n        if (MODULE_REF.instance[MODULE_CONFIG['OnStatus']]) {\n            MODULE['hot']['apply'](function hmrOnStatus(status) {\n                MODULE_REF.instance[MODULE_CONFIG['OnStatus']](status);\n            });\n        }\n        if (MODULE_REF.instance[MODULE_CONFIG['OnCheck']]) {\n            MODULE['hot']['check'](function hmrOnCheck(err, outdatedModules) {\n                MODULE_REF.instance[MODULE_CONFIG['OnCheck']](err, outdatedModules);\n            });\n        }\n        if (MODULE_REF.instance[MODULE_CONFIG['OnDecline']]) {\n            MODULE['hot']['decline'](function hmrOnDecline(dependencies) {\n                MODULE_REF.instance[MODULE_CONFIG['OnDecline']](dependencies);\n            });\n        }\n        MODULE['hot']['dispose'](function hmrOnDestroy(store) {\n            if (MODULE_REF.instance[MODULE_CONFIG['OnDestroy']]) {\n                MODULE_REF.instance[MODULE_CONFIG['OnDestroy']](store);\n            }\n            MODULE_REF.destroy();\n            if (MODULE_REF.instance[MODULE_CONFIG['AfterDestroy']]) {\n                MODULE_REF.instance[MODULE_CONFIG['AfterDestroy']](store);\n            }\n        });\n    }\n    return MODULE_REF;\n}\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAG;EACzB,QAAQ,EAAE,WAAW;EACrB,UAAU,EAAE,aAAa;EACzB,SAAS,EAAE,YAAY;EACvB,WAAW,EAAE,cAAc;EAC3B,WAAW,EAAE,cAAc;EAC3B,cAAc,EAAE;AACpB,CAAC;AACD,OAAO,SAASC,SAASA,CAACC,UAAU,EAAEC,MAAM,EAAEC,MAAM,GAAGJ,aAAa,EAAE;EAClE,IAAIG,MAAM,CAAC,KAAK,CAAC,EAAE;IACfA,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzB,IAAID,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE;MAC9C,IAAIG,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;QACvBD,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,QAAQ,CAAC,CAAC,CAACG,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;MACvE;IACJ;IACA,IAAID,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,UAAU,CAAC,CAAC,EAAE;MAChDG,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,SAASG,WAAWA,CAACC,MAAM,EAAE;QAChDL,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,UAAU,CAAC,CAAC,CAACO,MAAM,CAAC;MAC1D,CAAC,CAAC;IACN;IACA,IAAIL,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE;MAC/CG,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,SAASK,UAAUA,CAACC,GAAG,EAAEC,eAAe,EAAE;QAC7DR,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,SAAS,CAAC,CAAC,CAACS,GAAG,EAAEC,eAAe,CAAC;MACvE,CAAC,CAAC;IACN;IACA,IAAIR,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,WAAW,CAAC,CAAC,EAAE;MACjDG,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,SAASQ,YAAYA,CAACC,YAAY,EAAE;QACzDV,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,WAAW,CAAC,CAAC,CAACY,YAAY,CAAC;MACjE,CAAC,CAAC;IACN;IACAT,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,SAASU,YAAYA,CAACC,KAAK,EAAE;MAClD,IAAIZ,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,WAAW,CAAC,CAAC,EAAE;QACjDE,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,WAAW,CAAC,CAAC,CAACc,KAAK,CAAC;MAC1D;MACAZ,UAAU,CAACa,OAAO,CAAC,CAAC;MACpB,IAAIb,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,cAAc,CAAC,CAAC,EAAE;QACpDE,UAAU,CAACG,QAAQ,CAACL,aAAa,CAAC,cAAc,CAAC,CAAC,CAACc,KAAK,CAAC;MAC7D;IACJ,CAAC,CAAC;EACN;EACA,OAAOZ,UAAU;AACrB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
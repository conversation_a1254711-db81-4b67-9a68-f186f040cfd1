{"ast": null, "code": "import { observable as Symbol_observable } from '../symbol/observable';\nexport function isInteropObservable(input) {\n  return input && typeof input[Symbol_observable] === 'function';\n}", "map": {"version": 3, "names": ["observable", "Symbol_observable", "isInteropObservable", "input"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isInteropObservable.js"], "sourcesContent": ["import { observable as Symbol_observable } from '../symbol/observable';\nexport function isInteropObservable(input) {\n    return input && typeof input[Symbol_observable] === 'function';\n}\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,iBAAiB,QAAQ,sBAAsB;AACtE,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACvC,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACF,iBAAiB,CAAC,KAAK,UAAU;AAClE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
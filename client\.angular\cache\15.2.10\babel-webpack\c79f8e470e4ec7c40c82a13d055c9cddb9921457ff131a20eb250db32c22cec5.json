{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/registration.service\";\nimport * as i2 from \"@angular/router\";\nexport class SelectPlayerGuard {\n  constructor(_registrationService, _router) {\n    this._registrationService = _registrationService;\n    this._router = _router;\n  }\n  canActivate(route, state) {\n    if (!this._registrationService.selectedSeason) {\n      this._router.navigate(['/registration/select-event']);\n      return false;\n    }\n    return true;\n  }\n  static #_ = this.ɵfac = function SelectPlayerGuard_Factory(t) {\n    return new (t || SelectPlayerGuard)(i0.ɵɵinject(i1.RegistrationService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SelectPlayerGuard,\n    factory: SelectPlayerGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";;;AASA,OAAM,MAAOA,iBAAiB;EAC5BC,YAAoBC,oBAAyC,EAASC,OAAe;IAAjE,KAAAD,oBAAoB,GAApBA,oBAAoB;IAA8B,KAAAC,OAAO,GAAPA,OAAO;EAAY;EACzFC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAC1B,IAAI,CAAC,IAAI,CAACJ,oBAAoB,CAACK,cAAc,EAAE;MAC7C,IAAI,CAACJ,OAAO,CAACK,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;MACrD,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAAC,QAAAC,CAAA;qBAVUT,iBAAiB,EAAAU,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;WAAjBhB,iBAAiB;IAAAiB,OAAA,EAAjBjB,iBAAiB,CAAAkB,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["SelectPlayerGuard", "constructor", "_registrationService", "_router", "canActivate", "route", "state", "selectedS<PERSON>on", "navigate", "_", "i0", "ɵɵinject", "i1", "RegistrationService", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\guards\\select-player.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot, UrlTree } from '@angular/router';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { Observable } from 'rxjs';\r\nimport { Router } from '@angular/router';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SelectPlayerGuard implements CanActivate { \r\n  constructor(private _registrationService: RegistrationService,private _router: Router) { }\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {\r\n    if (!this._registrationService.selectedSeason) {\r\n      this._router.navigate(['/registration/select-event']);\r\n      return false;\r\n    }      \r\n    return true;\r\n  }\r\n  \r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
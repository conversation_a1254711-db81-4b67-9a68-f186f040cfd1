{"ast": null, "code": "/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, inject, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener, false);\n    // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n    return () => {\n      el.removeEventListener(evt, listener, false);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const dom = ɵgetDOM();\n      const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n      for (let i = 0; i < styles.length; i++) {\n        dom.remove(styles[i]);\n      }\n    });\n  };\n}\nconst SERVER_TRANSITION_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: appInitializerFactory,\n  deps: [TRANSITION_ID, DOCUMENT, Injector],\n  multi: true\n}];\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n      return testability;\n    };\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = (callback /** TODO #9100 */) => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n      const decrement = function (didWork_ /** TODO #9100 */) {\n        didWork = didWork || didWork_;\n        count--;\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n      testabilities.forEach(function (testability /** TODO #9100 */) {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n}\nBrowserXhr.ɵfac = function BrowserXhr_Factory(t) {\n  return new (t || BrowserXhr)();\n};\nBrowserXhr.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserXhr,\n  factory: BrowserXhr.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Registers a global handler for an event in a target view.\n   *\n   * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns A callback function that can be used to remove the handler.\n   * @deprecated No longer being used in Ivy code. To be removed in version 14.\n   */\n  addGlobalEventListener(target, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    const plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    for (let i = 0; i < plugins.length; i++) {\n      const plugin = plugins[i];\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n      }\n    }\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\n}\nEventManager.ɵfac = function EventManager_Factory(t) {\n  return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n};\nEventManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: EventManager,\n  factory: EventManager.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [EVENT_MANAGER_PLUGINS]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  addGlobalEventListener(element, eventName, handler) {\n    const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n    return this.addEventListener(target, eventName, handler);\n  }\n}\nclass SharedStylesHost {\n  constructor() {\n    this.usageCount = new Map();\n  }\n  addStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, 1);\n      if (usageCount === 1) {\n        this.onStyleAdded(style);\n      }\n    }\n  }\n  removeStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, -1);\n      if (usageCount === 0) {\n        this.onStyleRemoved(style);\n      }\n    }\n  }\n  onStyleRemoved(style) {}\n  onStyleAdded(style) {}\n  getAllStyles() {\n    return this.usageCount.keys();\n  }\n  changeUsageCount(style, delta) {\n    const map = this.usageCount;\n    let usage = map.get(style) ?? 0;\n    usage += delta;\n    if (usage > 0) {\n      map.set(style, usage);\n    } else {\n      map.delete(style);\n    }\n    return usage;\n  }\n  ngOnDestroy() {\n    for (const style of this.getAllStyles()) {\n      this.onStyleRemoved(style);\n    }\n    this.usageCount.clear();\n  }\n}\nSharedStylesHost.ɵfac = function SharedStylesHost_Factory(t) {\n  return new (t || SharedStylesHost)();\n};\nSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SharedStylesHost,\n  factory: SharedStylesHost.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], null, null);\n})();\nclass DomSharedStylesHost extends SharedStylesHost {\n  constructor(doc) {\n    super();\n    this.doc = doc;\n    // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n    this.styleRef = new Map();\n    this.hostNodes = new Set();\n    this.resetHostNodes();\n  }\n  onStyleAdded(style) {\n    for (const host of this.hostNodes) {\n      this.addStyleToHost(host, style);\n    }\n  }\n  onStyleRemoved(style) {\n    const styleRef = this.styleRef;\n    const styleElements = styleRef.get(style);\n    styleElements?.forEach(e => e.remove());\n    styleRef.delete(style);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this.styleRef.clear();\n    this.resetHostNodes();\n  }\n  addHost(hostNode) {\n    this.hostNodes.add(hostNode);\n    for (const style of this.getAllStyles()) {\n      this.addStyleToHost(hostNode, style);\n    }\n  }\n  removeHost(hostNode) {\n    this.hostNodes.delete(hostNode);\n  }\n  addStyleToHost(host, style) {\n    const styleEl = this.doc.createElement('style');\n    styleEl.textContent = style;\n    host.appendChild(styleEl);\n    const styleElRef = this.styleRef.get(style);\n    if (styleElRef) {\n      styleElRef.push(styleEl);\n    } else {\n      this.styleRef.set(style, [styleEl]);\n    }\n  }\n  resetHostNodes() {\n    const hostNodes = this.hostNodes;\n    hostNodes.clear();\n    // Re-add the head element back since this is the default host.\n    hostNodes.add(this.doc.head);\n  }\n}\nDomSharedStylesHost.ɵfac = function DomSharedStylesHost_Factory(t) {\n  return new (t || DomSharedStylesHost)(i0.ɵɵinject(DOCUMENT));\n};\nDomSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSharedStylesHost,\n  factory: DomSharedStylesHost.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSharedStylesHost, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = false;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `false`. This will be changed in the next major version.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken('RemoveStylesOnCompDestory', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles) {\n  // Cannot use `Infinity` as depth as `infinity` is not a number literal in TypeScript.\n  // See: https://github.com/microsoft/TypeScript/issues/32277\n  return styles.flat(100).map(s => s.replace(COMPONENT_REGEX, compId));\n}\nfunction decoratePreventDefault(eventHandler) {\n  // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n  // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n  // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n  // the listener (see below).\n  return event => {\n    // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n    // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n    // can inspect the listener toString contents for the existence of this special token. Because\n    // the token is a string literal, it is ensured to not be modified by compiled code.\n    if (event === '__ngUnwrap__') {\n      return eventHandler;\n    }\n    const allowDefaultBehavior = eventHandler(event);\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n    return undefined;\n  };\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestory) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n    this.rendererByCompId = new Map();\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestory = this.removeStylesOnCompDestory;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestory);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestory);\n          break;\n      }\n      renderer.onDestroy = () => rendererByCompId.delete(type.id);\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  begin() {}\n  end() {}\n}\nDomRendererFactory2.ɵfac = function DomRendererFactory2_Factory(t) {\n  return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(DomSharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY));\n};\nDomRendererFactory2.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomRendererFactory2,\n  factory: DomRendererFactory2.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: EventManager\n    }, {\n      type: DomSharedStylesHost\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n      }]\n    }];\n  }, null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager) {\n    this.eventManager = eventManager;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return document.createElement(name);\n  }\n  createComment(value) {\n    return document.createComment(value);\n  }\n  createText(value) {\n    return document.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n    }\n    return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId = component.id) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n    this.rendererUsageCount = 0;\n    this.styles = flattenStyles(compId, component.styles);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles);\n    this.rendererUsageCount++;\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestory) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles);\n    this.rendererUsageCount--;\n    if (this.rendererUsageCount === 0) {\n      this.onDestroy?.();\n    }\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestory) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n}\nDomEventsPlugin.ɵfac = function DomEventsPlugin_Factory(t) {\n  return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\nDomEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomEventsPlugin,\n  factory: DomEventsPlugin.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n      default:\n        return keyName;\n    }\n  }\n}\nKeyEventsPlugin.ɵfac = function KeyEventsPlugin_Factory(t) {\n  return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\nKeyEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: KeyEventsPlugin,\n  factory: KeyEventsPlugin.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: DomRendererFactory2,\n  useClass: DomRendererFactory2,\n  deps: [EventManager, DomSharedStylesHost, APP_ID, REMOVE_STYLES_ON_COMPONENT_DESTROY]\n}, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: SharedStylesHost,\n  useExisting: DomSharedStylesHost\n}, {\n  provide: DomSharedStylesHost,\n  useClass: DomSharedStylesHost,\n  deps: [DOCUMENT]\n}, {\n  provide: EventManager,\n  useClass: EventManager,\n  deps: [EVENT_MANAGER_PLUGINS, NgZone]\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, NG_DEV_MODE ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if (NG_DEV_MODE && providersAlreadyPresent) {\n      throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   */\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }, {\n        provide: TRANSITION_ID,\n        useExisting: APP_ID\n      }, SERVER_TRANSITION_PROVIDERS]\n    };\n  }\n}\nBrowserModule.ɵfac = function BrowserModule_Factory(t) {\n  return new (t || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n};\nBrowserModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserModule\n});\nBrowserModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  imports: [CommonModule, ApplicationModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [BROWSER_MODULE_PROVIDERS_MARKER]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n}\nMeta.ɵfac = function Meta_Factory(t) {\n  return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n};\nMeta.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Meta,\n  factory: function Meta_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new t();\n    } else {\n      r = createMeta();\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createMeta,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n}\nTitle.ɵfac = function Title_Factory(t) {\n  return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n};\nTitle.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Title,\n  factory: function Title_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new t();\n    } else {\n      r = createTitle();\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createTitle,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\nconst win = typeof window !== 'undefined' && window || {};\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    const isProfilerAvailable = win.console.profile != null;\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n    const start = performanceNow();\n    let numTicks = 0;\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performanceNow();\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\nfunction escapeHtml(text) {\n  const escapedText = {\n    '&': '&a;',\n    '\"': '&q;',\n    '\\'': '&s;',\n    '<': '&l;',\n    '>': '&g;'\n  };\n  return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n  const unescapedText = {\n    '&a;': '&',\n    '&q;': '\"',\n    '&s;': '\\'',\n    '&l;': '<',\n    '&g;': '>'\n  };\n  return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n  return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n  constructor() {\n    this.store = {};\n    this.onSerializeCallbacks = {};\n    this.store = retrieveTransferredState(inject(DOCUMENT), inject(APP_ID));\n  }\n  /**\n   * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n   */\n  get(key, defaultValue) {\n    return this.store[key] !== undefined ? this.store[key] : defaultValue;\n  }\n  /**\n   * Set the value corresponding to a key.\n   */\n  set(key, value) {\n    this.store[key] = value;\n  }\n  /**\n   * Remove a key from the store.\n   */\n  remove(key) {\n    delete this.store[key];\n  }\n  /**\n   * Test whether a key exists in the store.\n   */\n  hasKey(key) {\n    return this.store.hasOwnProperty(key);\n  }\n  /**\n   * Indicates whether the state is empty.\n   */\n  get isEmpty() {\n    return Object.keys(this.store).length === 0;\n  }\n  /**\n   * Register a callback to provide the value for a key when `toJson` is called.\n   */\n  onSerialize(key, callback) {\n    this.onSerializeCallbacks[key] = callback;\n  }\n  /**\n   * Serialize the current state of the store to JSON.\n   */\n  toJson() {\n    // Call the onSerialize callbacks and put those values into the store.\n    for (const key in this.onSerializeCallbacks) {\n      if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n        try {\n          this.store[key] = this.onSerializeCallbacks[key]();\n        } catch (e) {\n          console.warn('Exception in onSerialize callback: ', e);\n        }\n      }\n    }\n    return JSON.stringify(this.store);\n  }\n}\nTransferState.ɵfac = function TransferState_Factory(t) {\n  return new (t || TransferState)();\n};\nTransferState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TransferState,\n  factory: TransferState.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransferState, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction retrieveTransferredState(doc, appId) {\n  // Locate the script tag with the JSON data transferred from the server.\n  // The id of the script tag is set to the Angular appId + 'state'.\n  const script = doc.getElementById(appId + '-state');\n  let initialState = {};\n  if (script && script.textContent) {\n    try {\n      // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n      initialState = JSON.parse(unescapeHtml(script.textContent));\n    } catch (e) {\n      console.warn('Exception while restoring TransferState for app ' + appId, e);\n    }\n  }\n  return initialState;\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n * @deprecated no longer needed, you can inject the `TransferState` in an app without providing\n *     this module.\n */\nclass BrowserTransferStateModule {}\nBrowserTransferStateModule.ɵfac = function BrowserTransferStateModule_Factory(t) {\n  return new (t || BrowserTransferStateModule)();\n};\nBrowserTransferStateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserTransferStateModule\n});\nBrowserTransferStateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTransferStateModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n}\nHammerGestureConfig.ɵfac = function HammerGestureConfig_Factory(t) {\n  return new (t || HammerGestureConfig)();\n};\nHammerGestureConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGestureConfig,\n  factory: HammerGestureConfig.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n}\nHammerGesturesPlugin.ɵfac = function HammerGesturesPlugin_Factory(t) {\n  return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n};\nHammerGesturesPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGesturesPlugin,\n  factory: HammerGesturesPlugin.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: HammerGestureConfig,\n      decorators: [{\n        type: Inject,\n        args: [HAMMER_GESTURE_CONFIG]\n      }]\n    }, {\n      type: i0.ɵConsole\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [HAMMER_LOADER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {}\nHammerModule.ɵfac = function HammerModule_Factory(t) {\n  return new (t || HammerModule)();\n};\nHammerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HammerModule\n});\nHammerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: HammerGesturesPlugin,\n    multi: true,\n    deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n  }, {\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: HammerGestureConfig,\n    deps: []\n  }]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {}\nDomSanitizer.ɵfac = function DomSanitizer_Factory(t) {\n  return new (t || DomSanitizer)();\n};\nDomSanitizer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizer,\n  factory: function DomSanitizer_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new (t || DomSanitizer)();\n    } else {\n      r = i0.ɵɵinject(DomSanitizerImpl);\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new Error('unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new Error(`unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n}\nDomSanitizerImpl.ɵfac = function DomSanitizerImpl_Factory(t) {\n  return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n};\nDomSanitizerImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizerImpl,\n  factory: function DomSanitizerImpl_Factory(t) {\n    let r = null;\n    if (t) {\n      r = new t();\n    } else {\n      r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n    }\n    return r;\n  },\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: domSanitizerImplFactory,\n      deps: [Injector]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('15.2.10');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };", "map": {"version": 3, "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "InjectionToken", "ApplicationInitStatus", "APP_INITIALIZER", "Injector", "ɵglobal", "Injectable", "Inject", "ViewEncapsulation", "APP_ID", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_ID", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "Optional", "SkipSelf", "ɵɵinject", "ApplicationRef", "inject", "ɵConsole", "forwardRef", "ɵXSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "Version", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "urlParsingNode", "url", "setAttribute", "pathName", "pathname", "char<PERSON>t", "TRANSITION_ID", "appInitializerFactory", "transitionId", "injector", "get", "donePromise", "then", "dom", "styles", "querySelectorAll", "i", "length", "SERVER_TRANSITION_PROVIDERS", "provide", "useFactory", "deps", "multi", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "Error", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "didWork", "decrement", "didWork_", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "BrowserXhr_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "plugin", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "_findPluginFor", "addGlobalEventListener", "getZone", "supports", "set", "EventManager_Factory", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "SharedStylesHost", "usageCount", "addStyles", "style", "changeUsageCount", "onStyleAdded", "removeStyles", "onStyleRemoved", "getAllStyles", "keys", "delta", "map", "usage", "delete", "ngOnDestroy", "clear", "SharedStylesHost_Factory", "DomSharedStylesHost", "styleRef", "hostNodes", "Set", "resetHostNodes", "addStyleToHost", "styleElements", "e", "addHost", "hostNode", "add", "removeHost", "styleEl", "textContent", "append<PERSON><PERSON><PERSON>", "styleElRef", "head", "DomSharedStylesHost_Factory", "NAMESPACE_URIS", "COMPONENT_REGEX", "NG_DEV_MODE$1", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "flattenStyles", "compId", "flat", "s", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "event", "allowDefaultBehavior", "preventDefault", "returnValue", "DomRendererFactory2", "eventManager", "sharedStylesHost", "appId", "removeStylesOnCompDestory", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "id", "encapsulation", "Emulated", "ShadowDom", "ShadowDom<PERSON><PERSON><PERSON>", "onDestroy", "begin", "end", "DomRendererFactory2_Factory", "data", "Object", "create", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttribute", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "hostEl", "component", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "rendererUsageCount", "contentAttr", "hostAttr", "DomEventsPlugin", "DomEventsPlugin_Factory", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "runGuarded", "keyName", "KeyEventsPlugin_Factory", "NG_DEV_MODE", "bootstrapApplication", "rootComponent", "options", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "useValue", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "BrowserModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "createMeta", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "Meta_Factory", "r", "httpEquiv", "createTitle", "Title", "getTitle", "title", "setTitle", "newTitle", "Title_Factory", "exportNgVar", "COMPILED", "ng", "win", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "console", "profile", "start", "performanceNow", "tick", "profileEnd", "log", "toFixed", "performance", "now", "Date", "getTime", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "escapeHtml", "text", "escapedText", "unescapeHtml", "unescapedText", "makeStateKey", "TransferState", "store", "onSerializeCallbacks", "retrieveTransferredState", "defaultValue", "<PERSON><PERSON><PERSON>", "hasOwnProperty", "isEmpty", "onSerialize", "to<PERSON><PERSON>", "warn", "JSON", "stringify", "TransferState_Factory", "script", "getElementById", "initialState", "parse", "BrowserTransferStateModule", "BrowserTransferStateModule_Factory", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGestureConfig_Factory", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "isCustomEvent", "cancelRegistration", "deregister", "catch", "eventObj", "on", "off", "HammerGesturesPlugin_Factory", "HammerModule", "HammerModule_Factory", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizer_Factory", "DomSanitizerImpl", "domSanitizerImplFactory", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "DomSanitizerImpl_Factory", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵDomSharedStylesHost", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵNAMESPACE_URIS", "ɵSharedStylesHost", "ɵTRANSITION_ID", "ɵescapeHtml", "ɵflattenStyles", "ɵinitDomAdapter", "ɵshimContentAttribute", "ɵshimHostAttribute"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/platform-browser/fesm2020/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v15.2.10\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, inject, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener, false);\n        // Needed to follow Dart's subscription semantic, until fix of\n        // https://code.google.com/p/dart/issues/detail?id=17406\n        return () => {\n            el.removeEventListener(evt, listener, false);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n    urlParsingNode = urlParsingNode || document.createElement('a');\n    urlParsingNode.setAttribute('href', url);\n    const pathName = urlParsingNode.pathname;\n    return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n    return () => {\n        // Wait for all application initializers to be completed before removing the styles set by\n        // the server.\n        injector.get(ApplicationInitStatus).donePromise.then(() => {\n            const dom = ɵgetDOM();\n            const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n            for (let i = 0; i < styles.length; i++) {\n                dom.remove(styles[i]);\n            }\n        });\n    };\n}\nconst SERVER_TRANSITION_PROVIDERS = [\n    {\n        provide: APP_INITIALIZER,\n        useFactory: appInitializerFactory,\n        deps: [TRANSITION_ID, DOCUMENT, Injector],\n        multi: true\n    },\n];\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new Error('Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback /** TODO #9100 */) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            let didWork = false;\n            const decrement = function (didWork_ /** TODO #9100 */) {\n                didWork = didWork || didWork_;\n                count--;\n                if (count == 0) {\n                    callback(didWork);\n                }\n            };\n            testabilities.forEach(function (testability /** TODO #9100 */) {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n}\nBrowserXhr.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserXhr.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserXhr });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Registers a global handler for an event in a target view.\n     *\n     * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns A callback function that can be used to remove the handler.\n     * @deprecated No longer being used in Ivy code. To be removed in version 14.\n     */\n    addGlobalEventListener(target, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addGlobalEventListener(target, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        const plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        for (let i = 0; i < plugins.length; i++) {\n            const plugin = plugins[i];\n            if (plugin.supports(eventName)) {\n                this._eventNameToPlugin.set(eventName, plugin);\n                return plugin;\n            }\n        }\n        throw new Error(`No event manager plugin found for event ${eventName}`);\n    }\n}\nEventManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nEventManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: EventManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }]; } });\nclass EventManagerPlugin {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    addGlobalEventListener(element, eventName, handler) {\n        const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n        if (!target) {\n            throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n        }\n        return this.addEventListener(target, eventName, handler);\n    }\n}\n\nclass SharedStylesHost {\n    constructor() {\n        this.usageCount = new Map();\n    }\n    addStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, 1);\n            if (usageCount === 1) {\n                this.onStyleAdded(style);\n            }\n        }\n    }\n    removeStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, -1);\n            if (usageCount === 0) {\n                this.onStyleRemoved(style);\n            }\n        }\n    }\n    onStyleRemoved(style) { }\n    onStyleAdded(style) { }\n    getAllStyles() {\n        return this.usageCount.keys();\n    }\n    changeUsageCount(style, delta) {\n        const map = this.usageCount;\n        let usage = map.get(style) ?? 0;\n        usage += delta;\n        if (usage > 0) {\n            map.set(style, usage);\n        }\n        else {\n            map.delete(style);\n        }\n        return usage;\n    }\n    ngOnDestroy() {\n        for (const style of this.getAllStyles()) {\n            this.onStyleRemoved(style);\n        }\n        this.usageCount.clear();\n    }\n}\nSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SharedStylesHost, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }] });\nclass DomSharedStylesHost extends SharedStylesHost {\n    constructor(doc) {\n        super();\n        this.doc = doc;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this.styleRef = new Map();\n        this.hostNodes = new Set();\n        this.resetHostNodes();\n    }\n    onStyleAdded(style) {\n        for (const host of this.hostNodes) {\n            this.addStyleToHost(host, style);\n        }\n    }\n    onStyleRemoved(style) {\n        const styleRef = this.styleRef;\n        const styleElements = styleRef.get(style);\n        styleElements?.forEach(e => e.remove());\n        styleRef.delete(style);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this.styleRef.clear();\n        this.resetHostNodes();\n    }\n    addHost(hostNode) {\n        this.hostNodes.add(hostNode);\n        for (const style of this.getAllStyles()) {\n            this.addStyleToHost(hostNode, style);\n        }\n    }\n    removeHost(hostNode) {\n        this.hostNodes.delete(hostNode);\n    }\n    addStyleToHost(host, style) {\n        const styleEl = this.doc.createElement('style');\n        styleEl.textContent = style;\n        host.appendChild(styleEl);\n        const styleElRef = this.styleRef.get(style);\n        if (styleElRef) {\n            styleElRef.push(styleEl);\n        }\n        else {\n            this.styleRef.set(style, [styleEl]);\n        }\n    }\n    resetHostNodes() {\n        const hostNodes = this.hostNodes;\n        hostNodes.clear();\n        // Re-add the head element back since this is the default host.\n        hostNodes.add(this.doc.head);\n    }\n}\nDomSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSharedStylesHost, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/MathML/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = false;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `false`. This will be changed in the next major version.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken('RemoveStylesOnCompDestory', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles) {\n    // Cannot use `Infinity` as depth as `infinity` is not a number literal in TypeScript.\n    // See: https://github.com/microsoft/TypeScript/issues/32277\n    return styles.flat(100).map(s => s.replace(COMPONENT_REGEX, compId));\n}\nfunction decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n    // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n    // the listener (see below).\n    return (event) => {\n        // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n        // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n        // can inspect the listener toString contents for the existence of this special token. Because\n        // the token is a string literal, it is ensured to not be modified by compiled code.\n        if (event === '__ngUnwrap__') {\n            return eventHandler;\n        }\n        const allowDefaultBehavior = eventHandler(event);\n        if (allowDefaultBehavior === false) {\n            // TODO(tbosch): move preventDefault into event plugins...\n            event.preventDefault();\n            event.returnValue = false;\n        }\n        return undefined;\n    };\n}\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestory) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n        this.rendererByCompId = new Map();\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestory = this.removeStylesOnCompDestory;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestory);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestory);\n                    break;\n            }\n            renderer.onDestroy = () => rendererByCompId.delete(type.id);\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    begin() { }\n    end() { }\n}\nDomRendererFactory2.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: DomSharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }], target: i0.ɵɵFactoryTarget.Injectable });\nDomRendererFactory2.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomRendererFactory2 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: EventManager }, { type: DomSharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }]; } });\nclass DefaultDomRenderer2 {\n    constructor(eventManager) {\n        this.eventManager = eventManager;\n        this.data = Object.create(null);\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return document.createElement(name);\n    }\n    createComment(value) {\n        return document.createComment(value);\n    }\n    createText(value) {\n        return document.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n        }\n        return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = flattenStyles(component.id, component.styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId = component.id) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestory = removeStylesOnCompDestory;\n        this.rendererUsageCount = 0;\n        this.styles = flattenStyles(compId, component.styles);\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles);\n        this.rendererUsageCount++;\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestory) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles);\n        this.rendererUsageCount--;\n        if (this.rendererUsageCount === 0) {\n            this.onDestroy?.();\n        }\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestory) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestory, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n}\nDomEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        // TODO: switch to a Map if the mapping grows too much\n        switch (keyName) {\n            case 'esc':\n                return 'escape';\n            default:\n                return keyName;\n        }\n    }\n}\nKeyEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nKeyEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: KeyEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [\n            ...BROWSER_MODULE_PROVIDERS,\n            ...(options?.providers ?? []),\n        ],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    },\n    {\n        provide: Testability,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    }\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] }, {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] }, {\n        provide: DomRendererFactory2,\n        useClass: DomRendererFactory2,\n        deps: [EventManager, DomSharedStylesHost, APP_ID, REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    },\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: SharedStylesHost, useExisting: DomSharedStylesHost },\n    { provide: DomSharedStylesHost, useClass: DomSharedStylesHost, deps: [DOCUMENT] },\n    { provide: EventManager, useClass: EventManager, deps: [EVENT_MANAGER_PLUGINS, NgZone] },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    NG_DEV_MODE ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true } : []\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if (NG_DEV_MODE && providersAlreadyPresent) {\n            throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n                { provide: TRANSITION_ID, useExisting: APP_ID },\n                SERVER_TRANSITION_PROVIDERS,\n            ],\n        };\n    }\n}\nBrowserModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\nBrowserModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, providers: [\n        ...BROWSER_MODULE_PROVIDERS,\n        ...TESTABILITY_PROVIDERS\n    ], imports: [CommonModule, ApplicationModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        ...BROWSER_MODULE_PROVIDERS,\n                        ...TESTABILITY_PROVIDERS\n                    ],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                }] }]; } });\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n    return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n}\nMeta.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nMeta.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Meta, providedIn: 'root', useFactory: createMeta, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createMeta, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n    return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n}\nTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nTitle.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Title, providedIn: 'root', useFactory: createTitle, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createTitle, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\nconst win = typeof window !== 'undefined' && window || {};\n\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        const isProfilerAvailable = win.console.profile != null;\n        if (record && isProfilerAvailable) {\n            win.console.profile(profileName);\n        }\n        const start = performanceNow();\n        let numTicks = 0;\n        while (numTicks < 5 || (performanceNow() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performanceNow();\n        if (record && isProfilerAvailable) {\n            win.console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        win.console.log(`ran ${numTicks} change detection cycles`);\n        win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\nfunction performanceNow() {\n    return win.performance && win.performance.now ? win.performance.now() :\n        new Date().getTime();\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\nfunction escapeHtml(text) {\n    const escapedText = {\n        '&': '&a;',\n        '\"': '&q;',\n        '\\'': '&s;',\n        '<': '&l;',\n        '>': '&g;',\n    };\n    return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n    const unescapedText = {\n        '&a;': '&',\n        '&q;': '\"',\n        '&s;': '\\'',\n        '&l;': '<',\n        '&g;': '>',\n    };\n    return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n    return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n    constructor() {\n        this.store = {};\n        this.onSerializeCallbacks = {};\n        this.store = retrieveTransferredState(inject(DOCUMENT), inject(APP_ID));\n    }\n    /**\n     * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n     */\n    get(key, defaultValue) {\n        return this.store[key] !== undefined ? this.store[key] : defaultValue;\n    }\n    /**\n     * Set the value corresponding to a key.\n     */\n    set(key, value) {\n        this.store[key] = value;\n    }\n    /**\n     * Remove a key from the store.\n     */\n    remove(key) {\n        delete this.store[key];\n    }\n    /**\n     * Test whether a key exists in the store.\n     */\n    hasKey(key) {\n        return this.store.hasOwnProperty(key);\n    }\n    /**\n     * Indicates whether the state is empty.\n     */\n    get isEmpty() {\n        return Object.keys(this.store).length === 0;\n    }\n    /**\n     * Register a callback to provide the value for a key when `toJson` is called.\n     */\n    onSerialize(key, callback) {\n        this.onSerializeCallbacks[key] = callback;\n    }\n    /**\n     * Serialize the current state of the store to JSON.\n     */\n    toJson() {\n        // Call the onSerialize callbacks and put those values into the store.\n        for (const key in this.onSerializeCallbacks) {\n            if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n                try {\n                    this.store[key] = this.onSerializeCallbacks[key]();\n                }\n                catch (e) {\n                    console.warn('Exception in onSerialize callback: ', e);\n                }\n            }\n        }\n        return JSON.stringify(this.store);\n    }\n}\nTransferState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: TransferState, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTransferState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: TransferState, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: TransferState, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return []; } });\nfunction retrieveTransferredState(doc, appId) {\n    // Locate the script tag with the JSON data transferred from the server.\n    // The id of the script tag is set to the Angular appId + 'state'.\n    const script = doc.getElementById(appId + '-state');\n    let initialState = {};\n    if (script && script.textContent) {\n        try {\n            // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n            initialState = JSON.parse(unescapeHtml(script.textContent));\n        }\n        catch (e) {\n            console.warn('Exception while restoring TransferState for app ' + appId, e);\n        }\n    }\n    return initialState;\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n * @deprecated no longer needed, you can inject the `TransferState` in an app without providing\n *     this module.\n */\nclass BrowserTransferStateModule {\n}\nBrowserTransferStateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserTransferStateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule });\nBrowserTransferStateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: BrowserTransferStateModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n}\nHammerGestureConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGestureConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGestureConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n}\nHammerGesturesPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGesturesPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGesturesPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }]; } });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n}\nHammerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHammerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule });\nHammerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule, providers: [\n        {\n            provide: EVENT_MANAGER_PLUGINS,\n            useClass: HammerGesturesPlugin,\n            multi: true,\n            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n        },\n        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n}\nDomSanitizer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(function () { return DomSanitizerImpl; }) });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nfunction domSanitizerImplFactory(injector) {\n    return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error(`unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n            default:\n                throw new Error(`Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n}\nDomSanitizerImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizerImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [{ token: Injector }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.10\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [Injector] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('15.2.10');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AACvJ,SAASJ,OAAO,QAAQ,iBAAiB;AACzC,OAAO,KAAKK,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,OAAO,QAAQ,eAAe;;AAEryB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,SAAStD,WAAW,CAAC;EAC/CuD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASJ,wBAAwB,CAAC;EACrD,OAAOK,WAAWA,CAAA,EAAG;IACjB1D,kBAAkB,CAAC,IAAIyD,iBAAiB,CAAC,CAAC,CAAC;EAC/C;EACAE,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC3BF,EAAE,CAACG,gBAAgB,CAACF,GAAG,EAAEC,QAAQ,EAAE,KAAK,CAAC;IACzC;IACA;IACA,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAmB,CAACH,GAAG,EAAEC,QAAQ,EAAE,KAAK,CAAC;IAChD,CAAC;EACL;EACAG,aAAaA,CAACL,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACK,aAAa,CAACJ,GAAG,CAAC;EACzB;EACAK,MAAMA,CAACC,IAAI,EAAE;IACT,IAAIA,IAAI,CAACC,UAAU,EAAE;MACjBD,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;IACrC;EACJ;EACAG,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkBA,CAAA,EAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAaA,CAACX,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAYA,CAACf,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYgB,gBAAgB;EAC3C;EACA;EACAC,oBAAoBA,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,CAAC,CAAC;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgBA,CAAA,EAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOjG,iBAAiB,CAAC0E,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkBA,CAAA,EAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;EAC3D,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA;AACA,IAAIC,cAAc;AAClB,SAASX,YAAYA,CAACY,GAAG,EAAE;EACvBD,cAAc,GAAGA,cAAc,IAAI3B,QAAQ,CAACL,aAAa,CAAC,GAAG,CAAC;EAC9DgC,cAAc,CAACE,YAAY,CAAC,MAAM,EAAED,GAAG,CAAC;EACxC,MAAME,QAAQ,GAAGH,cAAc,CAACI,QAAQ;EACxC,OAAOD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGF,QAAQ,GAAI,IAAGA,QAAS,EAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA,MAAMG,aAAa,GAAG,IAAIpG,cAAc,CAAC,eAAe,CAAC;AACzD,SAASqG,qBAAqBA,CAACC,YAAY,EAAEnC,QAAQ,EAAEoC,QAAQ,EAAE;EAC7D,OAAO,MAAM;IACT;IACA;IACAA,QAAQ,CAACC,GAAG,CAACvG,qBAAqB,CAAC,CAACwG,WAAW,CAACC,IAAI,CAAC,MAAM;MACvD,MAAMC,GAAG,GAAGjH,OAAO,CAAC,CAAC;MACrB,MAAMkH,MAAM,GAAGzC,QAAQ,CAAC0C,gBAAgB,CAAE,wBAAuBP,YAAa,IAAG,CAAC;MAClF,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACpCH,GAAG,CAACjD,MAAM,CAACkD,MAAM,CAACE,CAAC,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AACA,MAAME,2BAA2B,GAAG,CAChC;EACIC,OAAO,EAAE/G,eAAe;EACxBgH,UAAU,EAAEb,qBAAqB;EACjCc,IAAI,EAAE,CAACf,aAAa,EAAEzG,QAAQ,EAAEQ,QAAQ,CAAC;EACzCiH,KAAK,EAAE;AACX,CAAC,CACJ;AAED,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAE;IAClBnH,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAACoH,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;MAC9D;MACA,OAAOF,WAAW;IACtB,CAAC;IACDtH,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAMmH,QAAQ,CAACM,mBAAmB,CAAC,CAAC;IAC5EzH,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAMmH,QAAQ,CAACO,kBAAkB,CAAC,CAAC;IAC1E,MAAMC,aAAa,GAAGA,CAACC,QAAQ,CAAC,sBAAsB;MAClD,MAAMC,aAAa,GAAG7H,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;MAC7D,IAAI8H,KAAK,GAAGD,aAAa,CAAClB,MAAM;MAChC,IAAIoB,OAAO,GAAG,KAAK;MACnB,MAAMC,SAAS,GAAG,SAAAA,CAAUC,QAAQ,CAAC,mBAAmB;QACpDF,OAAO,GAAGA,OAAO,IAAIE,QAAQ;QAC7BH,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAACG,OAAO,CAAC;QACrB;MACJ,CAAC;MACDF,aAAa,CAACK,OAAO,CAAC,UAAUZ,WAAW,CAAC,mBAAmB;QAC3DA,WAAW,CAACa,UAAU,CAACH,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAChI,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAACoI,IAAI,CAACT,aAAa,CAAC;EACvD;EACAJ,qBAAqBA,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMiB,CAAC,GAAGlB,QAAQ,CAACmB,cAAc,CAAClB,IAAI,CAAC;IACvC,IAAIiB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAAChB,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAI/H,OAAO,CAAC,CAAC,CAACgF,YAAY,CAAC8C,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAAChB,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACoB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,cAAc,CAAC,CAAC;EAC/B;AACJ;AACAF,UAAU,CAACG,IAAI,YAAAC,mBAAAR,CAAA;EAAA,YAAAA,CAAA,IAAyFI,UAAU;AAAA,CAAoD;AACtKA,UAAU,CAACK,KAAK,kBAD8EnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EACYP,UAAU;EAAAQ,OAAA,EAAVR,UAAU,CAAAG;AAAA,EAAG;AACzH;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAF8FvJ,EAAE,CAAAwJ,iBAAA,CAEJV,UAAU,EAAc,CAAC;IACzGW,IAAI,EAAEnJ;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMoJ,qBAAqB,GAAG,IAAIzJ,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0J,YAAY,CAAC;EACf;AACJ;AACA;EACI5G,WAAWA,CAAC6G,OAAO,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnCH,OAAO,CAACrB,OAAO,CAAEyB,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACO,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5G,gBAAgBA,CAAC6G,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACxG,gBAAgB,CAAC6G,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,sBAAsBA,CAAC3F,MAAM,EAAEwF,SAAS,EAAEC,OAAO,EAAE;IAC/C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACS,sBAAsB,CAAC3F,MAAM,EAAEwF,SAAS,EAAEC,OAAO,CAAC;EACpE;EACA;AACJ;AACA;EACIG,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACb,KAAK;EACrB;EACA;EACAW,cAAcA,CAACF,SAAS,EAAE;IACtB,MAAMN,MAAM,GAAG,IAAI,CAACF,kBAAkB,CAACrD,GAAG,CAAC6D,SAAS,CAAC;IACrD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACM,QAAQ;IAC7B,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,OAAO,CAAC5C,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAMiD,MAAM,GAAGJ,OAAO,CAAC7C,CAAC,CAAC;MACzB,IAAIiD,MAAM,CAACW,QAAQ,CAACL,SAAS,CAAC,EAAE;QAC5B,IAAI,CAACR,kBAAkB,CAACc,GAAG,CAACN,SAAS,EAAEN,MAAM,CAAC;QAC9C,OAAOA,MAAM;MACjB;IACJ;IACA,MAAM,IAAInC,KAAK,CAAE,2CAA0CyC,SAAU,EAAC,CAAC;EAC3E;AACJ;AACAX,YAAY,CAACV,IAAI,YAAA4B,qBAAAnC,CAAA;EAAA,YAAAA,CAAA,IAAyFiB,YAAY,EAhFxB3J,EAAE,CAAA6B,QAAA,CAgFwC6H,qBAAqB,GAhF/D1J,EAAE,CAAA6B,QAAA,CAgF0E7B,EAAE,CAACqB,MAAM;AAAA,CAA6C;AAChOsI,YAAY,CAACR,KAAK,kBAjF4EnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAiFcM,YAAY;EAAAL,OAAA,EAAZK,YAAY,CAAAV;AAAA,EAAG;AAC7H;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAlF8FvJ,EAAE,CAAAwJ,iBAAA,CAkFJG,YAAY,EAAc,CAAC;IAC3GF,IAAI,EAAEnJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACtB,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAED,IAAI,EAAEzJ,EAAE,CAACqB;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7C,MAAM4J,kBAAkB,CAAC;EACrBlI,WAAWA,CAACmI,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAT,sBAAsBA,CAACJ,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAChD,MAAMzF,MAAM,GAAGnF,OAAO,CAAC,CAAC,CAACkF,oBAAoB,CAAC,IAAI,CAACqG,IAAI,EAAEb,OAAO,CAAC;IACjE,IAAI,CAACvF,MAAM,EAAE;MACT,MAAM,IAAI+C,KAAK,CAAE,4BAA2B/C,MAAO,cAAawF,SAAU,EAAC,CAAC;IAChF;IACA,OAAO,IAAI,CAAC9G,gBAAgB,CAACsB,MAAM,EAAEwF,SAAS,EAAEC,OAAO,CAAC;EAC5D;AACJ;AAEA,MAAMY,gBAAgB,CAAC;EACnBpI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqI,UAAU,GAAG,IAAIrB,GAAG,CAAC,CAAC;EAC/B;EACAsB,SAASA,CAACxE,MAAM,EAAE;IACd,KAAK,MAAMyE,KAAK,IAAIzE,MAAM,EAAE;MACxB,MAAMuE,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACD,KAAK,EAAE,CAAC,CAAC;MAClD,IAAIF,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACI,YAAY,CAACF,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAG,YAAYA,CAAC5E,MAAM,EAAE;IACjB,KAAK,MAAMyE,KAAK,IAAIzE,MAAM,EAAE;MACxB,MAAMuE,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACD,KAAK,EAAE,CAAC,CAAC,CAAC;MACnD,IAAIF,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACM,cAAc,CAACJ,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAI,cAAcA,CAACJ,KAAK,EAAE,CAAE;EACxBE,YAAYA,CAACF,KAAK,EAAE,CAAE;EACtBK,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACP,UAAU,CAACQ,IAAI,CAAC,CAAC;EACjC;EACAL,gBAAgBA,CAACD,KAAK,EAAEO,KAAK,EAAE;IAC3B,MAAMC,GAAG,GAAG,IAAI,CAACV,UAAU;IAC3B,IAAIW,KAAK,GAAGD,GAAG,CAACrF,GAAG,CAAC6E,KAAK,CAAC,IAAI,CAAC;IAC/BS,KAAK,IAAIF,KAAK;IACd,IAAIE,KAAK,GAAG,CAAC,EAAE;MACXD,GAAG,CAAClB,GAAG,CAACU,KAAK,EAAES,KAAK,CAAC;IACzB,CAAC,MACI;MACDD,GAAG,CAACE,MAAM,CAACV,KAAK,CAAC;IACrB;IACA,OAAOS,KAAK;EAChB;EACAE,WAAWA,CAAA,EAAG;IACV,KAAK,MAAMX,KAAK,IAAI,IAAI,CAACK,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACD,cAAc,CAACJ,KAAK,CAAC;IAC9B;IACA,IAAI,CAACF,UAAU,CAACc,KAAK,CAAC,CAAC;EAC3B;AACJ;AACAf,gBAAgB,CAAClC,IAAI,YAAAkD,yBAAAzD,CAAA;EAAA,YAAAA,CAAA,IAAyFyC,gBAAgB;AAAA,CAAoD;AAClLA,gBAAgB,CAAChC,KAAK,kBAlJwEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAkJkB8B,gBAAgB;EAAA7B,OAAA,EAAhB6B,gBAAgB,CAAAlC;AAAA,EAAG;AACrI;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAnJ8FvJ,EAAE,CAAAwJ,iBAAA,CAmJJ2B,gBAAgB,EAAc,CAAC;IAC/G1B,IAAI,EAAEnJ;EACV,CAAC,CAAC;AAAA;AACV,MAAM8L,mBAAmB,SAASjB,gBAAgB,CAAC;EAC/CpI,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd;IACA,IAAI,CAACoI,QAAQ,GAAG,IAAItC,GAAG,CAAC,CAAC;IACzB,IAAI,CAACuC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAhB,YAAYA,CAACF,KAAK,EAAE;IAChB,KAAK,MAAM1C,IAAI,IAAI,IAAI,CAAC0D,SAAS,EAAE;MAC/B,IAAI,CAACG,cAAc,CAAC7D,IAAI,EAAE0C,KAAK,CAAC;IACpC;EACJ;EACAI,cAAcA,CAACJ,KAAK,EAAE;IAClB,MAAMe,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMK,aAAa,GAAGL,QAAQ,CAAC5F,GAAG,CAAC6E,KAAK,CAAC;IACzCoB,aAAa,EAAEnE,OAAO,CAACoE,CAAC,IAAIA,CAAC,CAAChJ,MAAM,CAAC,CAAC,CAAC;IACvC0I,QAAQ,CAACL,MAAM,CAACV,KAAK,CAAC;EAC1B;EACAW,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACI,QAAQ,CAACH,KAAK,CAAC,CAAC;IACrB,IAAI,CAACM,cAAc,CAAC,CAAC;EACzB;EACAI,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAACP,SAAS,CAACQ,GAAG,CAACD,QAAQ,CAAC;IAC5B,KAAK,MAAMvB,KAAK,IAAI,IAAI,CAACK,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACc,cAAc,CAACI,QAAQ,EAAEvB,KAAK,CAAC;IACxC;EACJ;EACAyB,UAAUA,CAACF,QAAQ,EAAE;IACjB,IAAI,CAACP,SAAS,CAACN,MAAM,CAACa,QAAQ,CAAC;EACnC;EACAJ,cAAcA,CAAC7D,IAAI,EAAE0C,KAAK,EAAE;IACxB,MAAM0B,OAAO,GAAG,IAAI,CAAC/I,GAAG,CAACF,aAAa,CAAC,OAAO,CAAC;IAC/CiJ,OAAO,CAACC,WAAW,GAAG3B,KAAK;IAC3B1C,IAAI,CAACsE,WAAW,CAACF,OAAO,CAAC;IACzB,MAAMG,UAAU,GAAG,IAAI,CAACd,QAAQ,CAAC5F,GAAG,CAAC6E,KAAK,CAAC;IAC3C,IAAI6B,UAAU,EAAE;MACZA,UAAU,CAAC1E,IAAI,CAACuE,OAAO,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACX,QAAQ,CAACzB,GAAG,CAACU,KAAK,EAAE,CAAC0B,OAAO,CAAC,CAAC;IACvC;EACJ;EACAR,cAAcA,CAAA,EAAG;IACb,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;IAChCA,SAAS,CAACJ,KAAK,CAAC,CAAC;IACjB;IACAI,SAAS,CAACQ,GAAG,CAAC,IAAI,CAAC7I,GAAG,CAACmJ,IAAI,CAAC;EAChC;AACJ;AACAhB,mBAAmB,CAACnD,IAAI,YAAAoE,4BAAA3E,CAAA;EAAA,YAAAA,CAAA,IAAyF0D,mBAAmB,EA3MtCpM,EAAE,CAAA6B,QAAA,CA2MsDjC,QAAQ;AAAA,CAA6C;AAC3MwM,mBAAmB,CAACjD,KAAK,kBA5MqEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EA4MqB+C,mBAAmB;EAAA9C,OAAA,EAAnB8C,mBAAmB,CAAAnD;AAAA,EAAG;AAC3I;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KA7M8FvJ,EAAE,CAAAwJ,iBAAA,CA6MJ4C,mBAAmB,EAAc,CAAC;IAClH3C,IAAI,EAAEnJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM0N,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,aAAa,GAAG,OAAOjE,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;AACrE,MAAMkE,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAI,WAAUD,kBAAmB,EAAC;AACjD,MAAME,YAAY,GAAI,cAAaF,kBAAmB,EAAC;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,KAAK;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAI5N,cAAc,CAAC,2BAA2B,EAAE;EACvF6N,UAAU,EAAE,MAAM;EAClBxE,OAAO,EAAEA,CAAA,KAAMsE;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACV,eAAe,EAAES,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACV,eAAe,EAAES,gBAAgB,CAAC;AAC/D;AACA,SAASG,aAAaA,CAACC,MAAM,EAAEvH,MAAM,EAAE;EACnC;EACA;EACA,OAAOA,MAAM,CAACwH,IAAI,CAAC,GAAG,CAAC,CAACvC,GAAG,CAACwC,CAAC,IAAIA,CAAC,CAACL,OAAO,CAACV,eAAe,EAAEa,MAAM,CAAC,CAAC;AACxE;AACA,SAASG,sBAAsBA,CAACC,YAAY,EAAE;EAC1C;EACA;EACA;EACA;EACA,OAAQC,KAAK,IAAK;IACd;IACA;IACA;IACA;IACA,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC1B,OAAOD,YAAY;IACvB;IACA,MAAME,oBAAoB,GAAGF,YAAY,CAACC,KAAK,CAAC;IAChD,IAAIC,oBAAoB,KAAK,KAAK,EAAE;MAChC;MACAD,KAAK,CAACE,cAAc,CAAC,CAAC;MACtBF,KAAK,CAACG,WAAW,GAAG,KAAK;IAC7B;IACA,OAAO9D,SAAS;EACpB,CAAC;AACL;AACA,MAAM+D,mBAAmB,CAAC;EACtB9L,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,yBAAyB,EAAE;IAC1E,IAAI,CAACH,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,gBAAgB,GAAG,IAAInF,GAAG,CAAC,CAAC;IACjC,IAAI,CAACoF,eAAe,GAAG,IAAIC,mBAAmB,CAACN,YAAY,CAAC;EAChE;EACAO,cAAcA,CAAChF,OAAO,EAAEZ,IAAI,EAAE;IAC1B,IAAI,CAACY,OAAO,IAAI,CAACZ,IAAI,EAAE;MACnB,OAAO,IAAI,CAAC0F,eAAe;IAC/B;IACA,MAAMG,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAClF,OAAO,EAAEZ,IAAI,CAAC;IACxD;IACA;IACA,IAAI6F,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAACpF,OAAO,CAAC;IACjC,CAAC,MACI,IAAIiF,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAClF,OAAO,EAAEZ,IAAI,EAAE;IAC/B,MAAMyF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAII,QAAQ,GAAGJ,gBAAgB,CAACzI,GAAG,CAACgD,IAAI,CAACmG,EAAE,CAAC;IAC5C,IAAI,CAACN,QAAQ,EAAE;MACX,MAAMR,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAME,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,QAAQxF,IAAI,CAACoG,aAAa;QACtB,KAAKrP,iBAAiB,CAACsP,QAAQ;UAC3BR,QAAQ,GAAG,IAAIE,iCAAiC,CAACV,YAAY,EAAEC,gBAAgB,EAAEtF,IAAI,EAAE,IAAI,CAACuF,KAAK,EAAEC,yBAAyB,CAAC;UAC7H;QACJ,KAAKzO,iBAAiB,CAACuP,SAAS;UAC5B,OAAO,IAAIC,iBAAiB,CAAClB,YAAY,EAAEC,gBAAgB,EAAE1E,OAAO,EAAEZ,IAAI,CAAC;QAC/E;UACI6F,QAAQ,GAAG,IAAII,4BAA4B,CAACZ,YAAY,EAAEC,gBAAgB,EAAEtF,IAAI,EAAEwF,yBAAyB,CAAC;UAC5G;MACR;MACAK,QAAQ,CAACW,SAAS,GAAG,MAAMf,gBAAgB,CAAClD,MAAM,CAACvC,IAAI,CAACmG,EAAE,CAAC;MAC3DV,gBAAgB,CAACtE,GAAG,CAACnB,IAAI,CAACmG,EAAE,EAAEN,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACArD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiD,gBAAgB,CAAChD,KAAK,CAAC,CAAC;EACjC;EACAgE,KAAKA,CAAA,EAAG,CAAE;EACVC,GAAGA,CAAA,EAAG,CAAE;AACZ;AACAtB,mBAAmB,CAAC5F,IAAI,YAAAmH,4BAAA1H,CAAA;EAAA,YAAAA,CAAA,IAAyFmG,mBAAmB,EArUtC7O,EAAE,CAAA6B,QAAA,CAqUsD8H,YAAY,GArUpE3J,EAAE,CAAA6B,QAAA,CAqU+EuK,mBAAmB,GArUpGpM,EAAE,CAAA6B,QAAA,CAqU+GpB,MAAM,GArUvHT,EAAE,CAAA6B,QAAA,CAqUkIgM,kCAAkC;AAAA,CAA6C;AACjTgB,mBAAmB,CAAC1F,KAAK,kBAtUqEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAsUqBwF,mBAAmB;EAAAvF,OAAA,EAAnBuF,mBAAmB,CAAA5F;AAAA,EAAG;AAC3I;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAvU8FvJ,EAAE,CAAAwJ,iBAAA,CAuUJqF,mBAAmB,EAAc,CAAC;IAClHpF,IAAI,EAAEnJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmJ,IAAI,EAAEE;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAE2C;IAAoB,CAAC,EAAE;MAAE3C,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrHtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACvK,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAAC6C,kCAAkC;MAC7C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMuB,mBAAmB,CAAC;EACtBrM,WAAWA,CAAC+L,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACuB,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,OAAOA,CAAA,EAAG,CAAE;EACZ1M,aAAaA,CAAC4B,IAAI,EAAE+K,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOtM,QAAQ,CAACuM,eAAe,CAACrD,cAAc,CAACoD,SAAS,CAAC,IAAIA,SAAS,EAAE/K,IAAI,CAAC;IACjF;IACA,OAAOvB,QAAQ,CAACL,aAAa,CAAC4B,IAAI,CAAC;EACvC;EACAiL,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAOzM,QAAQ,CAACwM,aAAa,CAACC,KAAK,CAAC;EACxC;EACAC,UAAUA,CAACD,KAAK,EAAE;IACd,OAAOzM,QAAQ,CAAC2M,cAAc,CAACF,KAAK,CAAC;EACzC;EACA3D,WAAWA,CAAC8D,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAAChE,WAAW,CAAC+D,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAxN,WAAWA,CAACkN,MAAM,EAAEO,QAAQ,EAAE;IAC1B,IAAIP,MAAM,EAAE;MACRA,MAAM,CAAClN,WAAW,CAACyN,QAAQ,CAAC;IAChC;EACJ;EACAC,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAIrO,EAAE,GAAG,OAAOoO,cAAc,KAAK,QAAQ,GAAGrN,QAAQ,CAACyB,aAAa,CAAC4L,cAAc,CAAC,GAChFA,cAAc;IAClB,IAAI,CAACpO,EAAE,EAAE;MACL,MAAM,IAAIwE,KAAK,CAAE,iBAAgB4J,cAAe,8BAA6B,CAAC;IAClF;IACA,IAAI,CAACC,eAAe,EAAE;MAClBrO,EAAE,CAAC4J,WAAW,GAAG,EAAE;IACvB;IACA,OAAO5J,EAAE;EACb;EACAQ,UAAUA,CAACD,IAAI,EAAE;IACb,OAAOA,IAAI,CAACC,UAAU;EAC1B;EACA8N,WAAWA,CAAC/N,IAAI,EAAE;IACd,OAAOA,IAAI,CAAC+N,WAAW;EAC3B;EACA1L,YAAYA,CAAC5C,EAAE,EAAEsC,IAAI,EAAEkL,KAAK,EAAEH,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACX/K,IAAI,GAAG+K,SAAS,GAAG,GAAG,GAAG/K,IAAI;MAC7B,MAAMiM,YAAY,GAAGtE,cAAc,CAACoD,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACdvO,EAAE,CAACwO,cAAc,CAACD,YAAY,EAAEjM,IAAI,EAAEkL,KAAK,CAAC;MAChD,CAAC,MACI;QACDxN,EAAE,CAAC4C,YAAY,CAACN,IAAI,EAAEkL,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDxN,EAAE,CAAC4C,YAAY,CAACN,IAAI,EAAEkL,KAAK,CAAC;IAChC;EACJ;EACAiB,eAAeA,CAACzO,EAAE,EAAEsC,IAAI,EAAE+K,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMkB,YAAY,GAAGtE,cAAc,CAACoD,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACdvO,EAAE,CAAC0O,iBAAiB,CAACH,YAAY,EAAEjM,IAAI,CAAC;MAC5C,CAAC,MACI;QACDtC,EAAE,CAACyO,eAAe,CAAE,GAAEpB,SAAU,IAAG/K,IAAK,EAAC,CAAC;MAC9C;IACJ,CAAC,MACI;MACDtC,EAAE,CAACyO,eAAe,CAACnM,IAAI,CAAC;IAC5B;EACJ;EACAqM,QAAQA,CAAC3O,EAAE,EAAEsC,IAAI,EAAE;IACftC,EAAE,CAAC4O,SAAS,CAACnF,GAAG,CAACnH,IAAI,CAAC;EAC1B;EACAuM,WAAWA,CAAC7O,EAAE,EAAEsC,IAAI,EAAE;IAClBtC,EAAE,CAAC4O,SAAS,CAACtO,MAAM,CAACgC,IAAI,CAAC;EAC7B;EACAwM,QAAQA,CAAC9O,EAAE,EAAEiI,KAAK,EAAEuF,KAAK,EAAEuB,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAI1R,mBAAmB,CAAC2R,QAAQ,GAAG3R,mBAAmB,CAAC4R,SAAS,CAAC,EAAE;MACxEjP,EAAE,CAACiI,KAAK,CAACiH,WAAW,CAACjH,KAAK,EAAEuF,KAAK,EAAEuB,KAAK,GAAG1R,mBAAmB,CAAC4R,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDjP,EAAE,CAACiI,KAAK,CAACA,KAAK,CAAC,GAAGuF,KAAK;IAC3B;EACJ;EACA2B,WAAWA,CAACnP,EAAE,EAAEiI,KAAK,EAAE8G,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAG1R,mBAAmB,CAAC2R,QAAQ,EAAE;MACtC;MACAhP,EAAE,CAACiI,KAAK,CAACmH,cAAc,CAACnH,KAAK,CAAC;IAClC,CAAC,MACI;MACDjI,EAAE,CAACiI,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAiH,WAAWA,CAAClP,EAAE,EAAEsC,IAAI,EAAEkL,KAAK,EAAE;IACzBrD,aAAa,IAAIkF,oBAAoB,CAAC/M,IAAI,EAAE,UAAU,CAAC;IACvDtC,EAAE,CAACsC,IAAI,CAAC,GAAGkL,KAAK;EACpB;EACA8B,QAAQA,CAAC/O,IAAI,EAAEiN,KAAK,EAAE;IAClBjN,IAAI,CAACgP,SAAS,GAAG/B,KAAK;EAC1B;EACAgC,MAAMA,CAAC/N,MAAM,EAAE2J,KAAK,EAAExG,QAAQ,EAAE;IAC5BuF,aAAa,IAAIkF,oBAAoB,CAACjE,KAAK,EAAE,UAAU,CAAC;IACxD,IAAI,OAAO3J,MAAM,KAAK,QAAQ,EAAE;MAC5B,OAAO,IAAI,CAACgK,YAAY,CAACrE,sBAAsB,CAAC3F,MAAM,EAAE2J,KAAK,EAAEF,sBAAsB,CAACtG,QAAQ,CAAC,CAAC;IACpG;IACA,OAAO,IAAI,CAAC6G,YAAY,CAACtL,gBAAgB,CAACsB,MAAM,EAAE2J,KAAK,EAAEF,sBAAsB,CAACtG,QAAQ,CAAC,CAAC;EAC9F;AACJ;AACA,MAAM6K,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAASL,oBAAoBA,CAAC/M,IAAI,EAAEqN,QAAQ,EAAE;EAC1C,IAAIrN,IAAI,CAACoN,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAIjL,KAAK,CAAE,wBAAuBmL,QAAS,IAAGrN,IAAK;AACjE;AACA,qEAAqEA,IAAK,gIAA+H,CAAC;EACtM;AACJ;AACA,SAASwL,cAAcA,CAACvN,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACI,OAAO,KAAK,UAAU,IAAIJ,IAAI,CAACwN,OAAO,KAAKtG,SAAS;AACpE;AACA,MAAMkF,iBAAiB,SAASZ,mBAAmB,CAAC;EAChDrM,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAEkE,MAAM,EAAEC,SAAS,EAAE;IAC3D,KAAK,CAACpE,YAAY,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACkE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,UAAU,GAAGF,MAAM,CAACG,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAACtE,gBAAgB,CAACnC,OAAO,CAAC,IAAI,CAACuG,UAAU,CAAC;IAC9C,MAAMtM,MAAM,GAAGsH,aAAa,CAAC+E,SAAS,CAACtD,EAAE,EAAEsD,SAAS,CAACrM,MAAM,CAAC;IAC5D,KAAK,MAAMyE,KAAK,IAAIzE,MAAM,EAAE;MACxB,MAAMmG,OAAO,GAAG5I,QAAQ,CAACL,aAAa,CAAC,OAAO,CAAC;MAC/CiJ,OAAO,CAACC,WAAW,GAAG3B,KAAK;MAC3B,IAAI,CAAC6H,UAAU,CAACjG,WAAW,CAACF,OAAO,CAAC;IACxC;EACJ;EACAsG,gBAAgBA,CAAC1P,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAACqP,MAAM,GAAG,IAAI,CAACE,UAAU,GAAGvP,IAAI;EACxD;EACAsJ,WAAWA,CAAC8D,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAAC/D,WAAW,CAAC,IAAI,CAACoG,gBAAgB,CAACtC,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACiC,gBAAgB,CAACtC,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAxN,WAAWA,CAACkN,MAAM,EAAEO,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACzN,WAAW,CAAC,IAAI,CAACwP,gBAAgB,CAACtC,MAAM,CAAC,EAAEO,QAAQ,CAAC;EACrE;EACA1N,UAAUA,CAACD,IAAI,EAAE;IACb,OAAO,IAAI,CAAC0P,gBAAgB,CAAC,KAAK,CAACzP,UAAU,CAAC,IAAI,CAACyP,gBAAgB,CAAC1P,IAAI,CAAC,CAAC,CAAC;EAC/E;EACA6M,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC1B,gBAAgB,CAAChC,UAAU,CAAC,IAAI,CAACoG,UAAU,CAAC;EACrD;AACJ;AACA,MAAMzD,4BAA4B,SAASN,mBAAmB,CAAC;EAC3DrM,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAEmE,SAAS,EAAEjE,yBAAyB,EAAEb,MAAM,GAAG8E,SAAS,CAACtD,EAAE,EAAE;IACrG,KAAK,CAACd,YAAY,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACE,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACsE,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAAC1M,MAAM,GAAGsH,aAAa,CAACC,MAAM,EAAE8E,SAAS,CAACrM,MAAM,CAAC;EACzD;EACA8I,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,gBAAgB,CAAC1D,SAAS,CAAC,IAAI,CAACxE,MAAM,CAAC;IAC5C,IAAI,CAAC0M,kBAAkB,EAAE;EAC7B;EACA9C,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACxB,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACF,gBAAgB,CAACtD,YAAY,CAAC,IAAI,CAAC5E,MAAM,CAAC;IAC/C,IAAI,CAAC0M,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAACA,kBAAkB,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACtD,SAAS,GAAG,CAAC;IACtB;EACJ;AACJ;AACA,MAAMT,iCAAiC,SAASE,4BAA4B,CAAC;EACzE3M,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAEmE,SAAS,EAAElE,KAAK,EAAEC,yBAAyB,EAAE;IACrF,MAAMb,MAAM,GAAGY,KAAK,GAAG,GAAG,GAAGkE,SAAS,CAACtD,EAAE;IACzC,KAAK,CAACd,YAAY,EAAEC,gBAAgB,EAAEmE,SAAS,EAAEjE,yBAAyB,EAAEb,MAAM,CAAC;IACnF,IAAI,CAACoF,WAAW,GAAGzF,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACqF,QAAQ,GAAGvF,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACAqB,WAAWA,CAACpF,OAAO,EAAE;IACjB,IAAI,CAACsF,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC1J,YAAY,CAACoE,OAAO,EAAE,IAAI,CAACoJ,QAAQ,EAAE,EAAE,CAAC;EACjD;EACA1P,aAAaA,CAACiN,MAAM,EAAErL,IAAI,EAAE;IACxB,MAAMtC,EAAE,GAAG,KAAK,CAACU,aAAa,CAACiN,MAAM,EAAErL,IAAI,CAAC;IAC5C,KAAK,CAACM,YAAY,CAAC5C,EAAE,EAAE,IAAI,CAACmQ,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAOnQ,EAAE;EACb;AACJ;AAEA,MAAMqQ,eAAe,SAASzI,kBAAkB,CAAC;EAC7ClI,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACA0G,QAAQA,CAACL,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACA9G,gBAAgBA,CAAC6G,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1CF,OAAO,CAAC7G,gBAAgB,CAAC8G,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IACnD,OAAO,MAAM,IAAI,CAAC9G,mBAAmB,CAAC4G,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACtE;EACA9G,mBAAmBA,CAACqB,MAAM,EAAEwF,SAAS,EAAErC,QAAQ,EAAE;IAC7C,OAAOnD,MAAM,CAACrB,mBAAmB,CAAC6G,SAAS,EAAErC,QAAQ,CAAC;EAC1D;AACJ;AACAyL,eAAe,CAACzK,IAAI,YAAA0K,wBAAAjL,CAAA;EAAA,YAAAA,CAAA,IAAyFgL,eAAe,EArjB9B1T,EAAE,CAAA6B,QAAA,CAqjB8CjC,QAAQ;AAAA,CAA6C;AACnM8T,eAAe,CAACvK,KAAK,kBAtjByEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAsjBiBqK,eAAe;EAAApK,OAAA,EAAfoK,eAAe,CAAAzK;AAAA,EAAG;AACnI;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAvjB8FvJ,EAAE,CAAAwJ,iBAAA,CAujBJkK,eAAe,EAAc,CAAC;IAC9GjK,IAAI,EAAEnJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,MAAMgU,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGrF,KAAK,IAAKA,KAAK,CAACsF,MAAM;EAC9B,SAAS,EAAGtF,KAAK,IAAKA,KAAK,CAACuF,OAAO;EACnC,MAAM,EAAGvF,KAAK,IAAKA,KAAK,CAACwF,OAAO;EAChC,OAAO,EAAGxF,KAAK,IAAKA,KAAK,CAACyF;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASlJ,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACIlI,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACI0G,QAAQA,CAACL,SAAS,EAAE;IAChB,OAAO6J,eAAe,CAACC,cAAc,CAAC9J,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI9G,gBAAgBA,CAAC6G,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAM8J,WAAW,GAAGF,eAAe,CAACC,cAAc,CAAC9J,SAAS,CAAC;IAC7D,MAAMgK,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAE9J,OAAO,EAAE,IAAI,CAACN,OAAO,CAACS,OAAO,CAAC,CAAC,CAAC;IAC7G,OAAO,IAAI,CAACT,OAAO,CAACS,OAAO,CAAC,CAAC,CAAC8J,iBAAiB,CAAC,MAAM;MAClD,OAAO7U,OAAO,CAAC,CAAC,CAACyD,WAAW,CAACiH,OAAO,EAAEgK,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,CAAC;IACtF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOF,cAAcA,CAAC9J,SAAS,EAAE;IAC7B,MAAMmK,KAAK,GAAGnK,SAAS,CAACoK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC;IAClC,IAAKJ,KAAK,CAACzN,MAAM,KAAK,CAAC,IAAK,EAAE4N,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACnF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAa,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACArB,aAAa,CAACrL,OAAO,CAAC8M,YAAY,IAAI;MAClC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAACzN,MAAM,IAAI,CAAC,IAAI8N,GAAG,CAAC9N,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAMuO,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqBA,CAAC/G,KAAK,EAAEgH,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAACpF,KAAK,CAACqG,GAAG,CAAC,IAAIrG,KAAK,CAACqG,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAGjH,KAAK,CAACkH,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,CAAC,CAAC;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;;IACA9B,aAAa,CAACrL,OAAO,CAAC8M,YAAY,IAAI;MAClC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAY,CAAC;QACzD,IAAIO,cAAc,CAACnH,KAAK,CAAC,EAAE;UACvBqG,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOlB,aAAaA,CAACU,OAAO,EAAE1K,OAAO,EAAEsL,IAAI,EAAE;IACzC,OAAQpH,KAAK,IAAK;MACd,IAAI0F,eAAe,CAACqB,qBAAqB,CAAC/G,KAAK,EAAEwG,OAAO,CAAC,EAAE;QACvDY,IAAI,CAACC,UAAU,CAAC,MAAMvL,OAAO,CAACkE,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOsG,aAAaA,CAACgB,OAAO,EAAE;IAC1B;IACA,QAAQA,OAAO;MACX,KAAK,KAAK;QACN,OAAO,QAAQ;MACnB;QACI,OAAOA,OAAO;IACtB;EACJ;AACJ;AACA5B,eAAe,CAAClL,IAAI,YAAA+M,wBAAAtN,CAAA;EAAA,YAAAA,CAAA,IAAyFyL,eAAe,EAxuB9BnU,EAAE,CAAA6B,QAAA,CAwuB8CjC,QAAQ;AAAA,CAA6C;AACnMuU,eAAe,CAAChL,KAAK,kBAzuByEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAyuBiB8K,eAAe;EAAA7K,OAAA,EAAf6K,eAAe,CAAAlL;AAAA,EAAG;AACnI;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KA1uB8FvJ,EAAE,CAAAwJ,iBAAA,CA0uBJ2K,eAAe,EAAc,CAAC;IAC9G1K,IAAI,EAAEnJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMqW,WAAW,GAAG,OAAO1M,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2M,oBAAoBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClD,OAAOzV,0BAA0B,CAAC;IAAEwV,aAAa;IAAE,GAAGE,qBAAqB,CAACD,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACF,OAAO,EAAE;EAChC,OAAOzV,0BAA0B,CAAC0V,qBAAqB,CAACD,OAAO,CAAC,CAAC;AACrE;AACA,SAASC,qBAAqBA,CAACD,OAAO,EAAE;EACpC,OAAO;IACHG,YAAY,EAAE,CACV,GAAGC,wBAAwB,EAC3B,IAAIJ,OAAO,EAAEK,SAAS,IAAI,EAAE,CAAC,CAChC;IACDC,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB5T,iBAAiB,CAACC,WAAW,CAAC,CAAC;AACnC;AACA,SAAS4T,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAInW,YAAY,CAAC,CAAC;AAC7B;AACA,SAASoW,SAASA,CAAA,EAAG;EACjB;EACAnW,YAAY,CAACuD,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAMuS,mCAAmC,GAAG,CACxC;EAAEzP,OAAO,EAAEpG,WAAW;EAAEmW,QAAQ,EAAEpX;AAAqB,CAAC,EACxD;EAAEqH,OAAO,EAAEnG,oBAAoB;EAAEkW,QAAQ,EAAEH,cAAc;EAAEzP,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEH,OAAO,EAAEtH,QAAQ;EAAEuH,UAAU,EAAE6P,SAAS;EAAE5P,IAAI,EAAE;AAAG,CAAC,CACzD;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8P,eAAe,GAAGlW,qBAAqB,CAACC,YAAY,EAAE,SAAS,EAAE0V,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,+BAA+B,GAAG,IAAIlX,cAAc,CAACgW,WAAW,GAAG,gCAAgC,GAAG,EAAE,CAAC;AAC/G,MAAMY,qBAAqB,GAAG,CAC1B;EACI3P,OAAO,EAAEhG,mBAAmB;EAC5BkW,QAAQ,EAAE9P,qBAAqB;EAC/BF,IAAI,EAAE;AACV,CAAC,EACD;EACIF,OAAO,EAAE/F,YAAY;EACrBiW,QAAQ,EAAEhW,WAAW;EACrBgG,IAAI,EAAE,CAAC/F,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,EACD;EACIgG,OAAO,EAAE9F,WAAW;EACpBgW,QAAQ,EAAEhW,WAAW;EACrBgG,IAAI,EAAE,CAAC/F,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAMsV,wBAAwB,GAAG,CAC7B;EAAEtP,OAAO,EAAE3F,eAAe;EAAE0V,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAE/P,OAAO,EAAEtG,YAAY;EAAEuG,UAAU,EAAE4P,YAAY;EAAE3P,IAAI,EAAE;AAAG,CAAC,EAAE;EAC3DF,OAAO,EAAEwC,qBAAqB;EAC9B0N,QAAQ,EAAE1D,eAAe;EACzBrM,KAAK,EAAE,IAAI;EACXD,IAAI,EAAE,CAACxH,QAAQ,EAAEyB,MAAM,EAAEP,WAAW;AACxC,CAAC,EACD;EAAEoG,OAAO,EAAEwC,qBAAqB;EAAE0N,QAAQ,EAAEjD,eAAe;EAAE9M,KAAK,EAAE,IAAI;EAAED,IAAI,EAAE,CAACxH,QAAQ;AAAE,CAAC,EAAE;EAC1FsH,OAAO,EAAE2H,mBAAmB;EAC5BuI,QAAQ,EAAEvI,mBAAmB;EAC7BzH,IAAI,EAAE,CAACuC,YAAY,EAAEyC,mBAAmB,EAAE3L,MAAM,EAAEoN,kCAAkC;AACxF,CAAC,EACD;EAAE3G,OAAO,EAAE1F,gBAAgB;EAAE6V,WAAW,EAAExI;AAAoB,CAAC,EAC/D;EAAE3H,OAAO,EAAEiE,gBAAgB;EAAEkM,WAAW,EAAEjL;AAAoB,CAAC,EAC/D;EAAElF,OAAO,EAAEkF,mBAAmB;EAAEgL,QAAQ,EAAEhL,mBAAmB;EAAEhF,IAAI,EAAE,CAACxH,QAAQ;AAAE,CAAC,EACjF;EAAEsH,OAAO,EAAEyC,YAAY;EAAEyN,QAAQ,EAAEzN,YAAY;EAAEvC,IAAI,EAAE,CAACsC,qBAAqB,EAAErI,MAAM;AAAE,CAAC,EACxF;EAAE6F,OAAO,EAAEpH,UAAU;EAAEsX,QAAQ,EAAEtO,UAAU;EAAE1B,IAAI,EAAE;AAAG,CAAC,EACvD6O,WAAW,GAAG;EAAE/O,OAAO,EAAEiQ,+BAA+B;EAAEF,QAAQ,EAAE;AAAK,CAAC,GAAG,EAAE,CAClF;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,aAAa,CAAC;EAChBvU,WAAWA,CAACwU,uBAAuB,EAAE;IACjC,IAAItB,WAAW,IAAIsB,uBAAuB,EAAE;MACxC,MAAM,IAAI1P,KAAK,CAAE,oFAAmF,GAC/F,mFAAkF,CAAC;IAC5F;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO2P,oBAAoBA,CAACC,MAAM,EAAE;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aAAa;MACvBb,SAAS,EAAE,CACP;QAAEvP,OAAO,EAAEzG,MAAM;QAAEwW,QAAQ,EAAEQ,MAAM,CAACzI;MAAM,CAAC,EAC3C;QAAE9H,OAAO,EAAEb,aAAa;QAAEgR,WAAW,EAAE5W;MAAO,CAAC,EAC/CwG,2BAA2B;IAEnC,CAAC;EACL;AACJ;AACAqQ,aAAa,CAACrO,IAAI,YAAA0O,sBAAAjP,CAAA;EAAA,YAAAA,CAAA,IAAyF4O,aAAa,EA97B1BtX,EAAE,CAAA6B,QAAA,CA87B0CsV,+BAA+B;AAAA,CAA2E;AACpPG,aAAa,CAACM,IAAI,kBA/7B4E5X,EAAE,CAAA6X,gBAAA;EAAApO,IAAA,EA+7BY6N;AAAa,EAA+C;AACxKA,aAAa,CAACQ,IAAI,kBAh8B4E9X,EAAE,CAAA+X,gBAAA;EAAAtB,SAAA,EAg8BsC,CAC9H,GAAGD,wBAAwB,EAC3B,GAAGK,qBAAqB,CAC3B;EAAAmB,OAAA,GAAYjY,YAAY,EAAE0B,iBAAiB;AAAA,EAAI;AACpD;EAAA,QAAA8H,SAAA,oBAAAA,SAAA,KAp8B8FvJ,EAAE,CAAAwJ,iBAAA,CAo8BJ8N,aAAa,EAAc,CAAC;IAC5G7N,IAAI,EAAE/H,QAAQ;IACdsJ,IAAI,EAAE,CAAC;MACCyL,SAAS,EAAE,CACP,GAAGD,wBAAwB,EAC3B,GAAGK,qBAAqB,CAC3B;MACDoB,OAAO,EAAE,CAAClY,YAAY,EAAE0B,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgI,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE9H;MACV,CAAC,EAAE;QACC8H,IAAI,EAAE7H;MACV,CAAC,EAAE;QACC6H,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACmM,+BAA+B;MAC1C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,SAASe,UAAUA,CAAA,EAAG;EAClB,OAAO,IAAIC,IAAI,CAACtW,QAAQ,CAACjC,QAAQ,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuY,IAAI,CAAC;EACPpV,WAAWA,CAACmI,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACkN,IAAI,GAAGzY,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0Y,MAAMA,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACpD,MAAM,EAAE+C,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACL/C,MAAM,CAAC9M,IAAI,CAAC,IAAI,CAAC+P,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAOhD,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqD,MAAMA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAAC3N,IAAI,CAACrF,aAAa,CAAE,QAAOgT,YAAa,GAAE,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACD,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAME,IAAI,CAAC,eAAe,IAAI,CAAC7N,IAAI,CAACpE,gBAAgB,CAAE,QAAO+R,YAAa,GAAE,CAAC;IAC7E,OAAOE,IAAI,GAAG,EAAE,CAAC5O,KAAK,CAAC6O,IAAI,CAACD,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,SAASA,CAACX,GAAG,EAAEY,QAAQ,EAAE;IACrB,IAAI,CAACZ,GAAG,EACJ,OAAO,IAAI;IACfY,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACb,GAAG,CAAC;IAC/C,MAAMc,IAAI,GAAG,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACf,GAAG,EAAEc,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACZ,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIgB,SAASA,CAACT,YAAY,EAAE;IACpB,IAAI,CAACU,gBAAgB,CAAC,IAAI,CAACX,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIU,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAAChB,IAAI,CAACzU,MAAM,CAACyV,IAAI,CAAC;IAC1B;EACJ;EACAZ,mBAAmBA,CAACY,IAAI,EAAEb,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMW,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAM3R,IAAI,GAAG,IAAI,CAACqR,OAAO,CAACI,QAAQ,CAAC,CAACM,MAAM,CAAC/R,IAAI,IAAI,IAAI,CAACgS,mBAAmB,CAACL,IAAI,EAAE3R,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,IAAIA,IAAI,KAAKqD,SAAS,EAClB,OAAOrD,IAAI;IACnB;IACA,MAAM4C,OAAO,GAAG,IAAI,CAAC+N,IAAI,CAACrU,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACsV,yBAAyB,CAACD,IAAI,EAAE/O,OAAO,CAAC;IAC7C,MAAM+C,IAAI,GAAG,IAAI,CAAClC,IAAI,CAACwO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDtM,IAAI,CAACF,WAAW,CAAC7C,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACAgP,yBAAyBA,CAACf,GAAG,EAAEjV,EAAE,EAAE;IAC/BiN,MAAM,CAAC1E,IAAI,CAAC0M,GAAG,CAAC,CAAC/P,OAAO,CAAEoR,IAAI,IAAKtW,EAAE,CAAC4C,YAAY,CAAC,IAAI,CAAC2T,cAAc,CAACD,IAAI,CAAC,EAAErB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;IACzF,OAAOtW,EAAE;EACb;EACA8V,cAAcA,CAACb,GAAG,EAAE;IAChB,MAAMuB,IAAI,GAAGvB,GAAG,CAAC3S,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAQ,GAAEkU,IAAK,KAAIvB,GAAG,CAACuB,IAAI,CAAE,GAAE;EACnC;EACAJ,mBAAmBA,CAACnB,GAAG,EAAE7Q,IAAI,EAAE;IAC3B,OAAO6I,MAAM,CAAC1E,IAAI,CAAC0M,GAAG,CAAC,CAACwB,KAAK,CAAEhF,GAAG,IAAKrN,IAAI,CAAC3B,YAAY,CAAC,IAAI,CAAC8T,cAAc,CAAC9E,GAAG,CAAC,CAAC,KAAKwD,GAAG,CAACxD,GAAG,CAAC,CAAC;EACpG;EACA8E,cAAcA,CAACD,IAAI,EAAE;IACjB,OAAOI,aAAa,CAACJ,IAAI,CAAC,IAAIA,IAAI;EACtC;AACJ;AACAxB,IAAI,CAAClP,IAAI,YAAA+Q,aAAAtR,CAAA;EAAA,YAAAA,CAAA,IAAyFyP,IAAI,EAlnCRnY,EAAE,CAAA6B,QAAA,CAknCwBjC,QAAQ;AAAA,CAA6C;AAC7KuY,IAAI,CAAChP,KAAK,kBAnnCoFnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAmnCM8O,IAAI;EAAA7O,OAAA,WAAA0Q,aAAAtR,CAAA;IAAA,IAAAuR,CAAA;IAAA,IAAAvR,CAAA;MAAAuR,CAAA,OAAAvR,CAAA;IAAA;MAAAuR,CAAA,GAAkC/B,UAAU;IAAA;IAAA,OAAA+B,CAAA;EAAA;EAAAnM,UAAA,EAA9B;AAAM,EAAqC;AACnK;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KApnC8FvJ,EAAE,CAAAwJ,iBAAA,CAonCJ2O,IAAI,EAAc,CAAC;IACnG1O,IAAI,EAAEnJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE8C,UAAU,EAAE,MAAM;MAAE3G,UAAU,EAAE+Q,UAAU;MAAE9Q,IAAI,EAAE;IAAG,CAAC;EACnE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEqC,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAMma,aAAa,GAAG;EAClBG,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,OAAO,IAAIC,KAAK,CAACvY,QAAQ,CAACjC,QAAQ,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwa,KAAK,CAAC;EACRrX,WAAWA,CAACmI,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACImP,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnP,IAAI,CAACoP,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAACtP,IAAI,CAACoP,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;AACJ;AACAJ,KAAK,CAACnR,IAAI,YAAAwR,cAAA/R,CAAA;EAAA,YAAAA,CAAA,IAAyF0R,KAAK,EApqCVpa,EAAE,CAAA6B,QAAA,CAoqC0BjC,QAAQ;AAAA,CAA6C;AAC/Kwa,KAAK,CAACjR,KAAK,kBArqCmFnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAqqCO+Q,KAAK;EAAA9Q,OAAA,WAAAmR,cAAA/R,CAAA;IAAA,IAAAuR,CAAA;IAAA,IAAAvR,CAAA;MAAAuR,CAAA,OAAAvR,CAAA;IAAA;MAAAuR,CAAA,GAAkCE,WAAW;IAAA;IAAA,OAAAF,CAAA;EAAA;EAAAnM,UAAA,EAA/B;AAAM,EAAsC;AACtK;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KAtqC8FvJ,EAAE,CAAAwJ,iBAAA,CAsqCJ4Q,KAAK,EAAc,CAAC;IACpG3Q,IAAI,EAAEnJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE8C,UAAU,EAAE,MAAM;MAAE3G,UAAU,EAAEgT,WAAW;MAAE/S,IAAI,EAAE;IAAG,CAAC;EACpE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEqC,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8a,WAAWA,CAAC/U,IAAI,EAAEkL,KAAK,EAAE;EAC9B,IAAI,OAAO8J,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAGva,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9Cua,EAAE,CAACjV,IAAI,CAAC,GAAGkL,KAAK;EACpB;AACJ;AAEA,MAAMgK,GAAG,GAAG,OAAO9V,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,CAAC,CAAC;AAEzD,MAAM+V,yBAAyB,CAAC;EAC5B/X,WAAWA,CAACgY,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBlY,WAAWA,CAACmY,GAAG,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,GAAG,CAAC1U,QAAQ,CAACC,GAAG,CAAC3E,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsZ,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAME,WAAW,GAAG,kBAAkB;IACtC;IACA,MAAMC,mBAAmB,GAAGX,GAAG,CAACY,OAAO,CAACC,OAAO,IAAI,IAAI;IACvD,IAAIJ,MAAM,IAAIE,mBAAmB,EAAE;MAC/BX,GAAG,CAACY,OAAO,CAACC,OAAO,CAACH,WAAW,CAAC;IACpC;IACA,MAAMI,KAAK,GAAGC,cAAc,CAAC,CAAC;IAC9B,IAAIZ,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAKY,cAAc,CAAC,CAAC,GAAGD,KAAK,GAAI,GAAG,EAAE;MACrD,IAAI,CAACR,MAAM,CAACU,IAAI,CAAC,CAAC;MAClBb,QAAQ,EAAE;IACd;IACA,MAAM7K,GAAG,GAAGyL,cAAc,CAAC,CAAC;IAC5B,IAAIN,MAAM,IAAIE,mBAAmB,EAAE;MAC/BX,GAAG,CAACY,OAAO,CAACK,UAAU,CAACP,WAAW,CAAC;IACvC;IACA,MAAMR,SAAS,GAAG,CAAC5K,GAAG,GAAGwL,KAAK,IAAIX,QAAQ;IAC1CH,GAAG,CAACY,OAAO,CAACM,GAAG,CAAE,OAAMf,QAAS,0BAAyB,CAAC;IAC1DH,GAAG,CAACY,OAAO,CAACM,GAAG,CAAE,GAAEhB,SAAS,CAACiB,OAAO,CAAC,CAAC,CAAE,eAAc,CAAC;IACvD,OAAO,IAAIlB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AACA,SAASY,cAAcA,CAAA,EAAG;EACtB,OAAOf,GAAG,CAACoB,WAAW,IAAIpB,GAAG,CAACoB,WAAW,CAACC,GAAG,GAAGrB,GAAG,CAACoB,WAAW,CAACC,GAAG,CAAC,CAAC,GACjE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;AAC5B;AAEA,MAAMC,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACpB,GAAG,EAAE;EAC3BR,WAAW,CAAC2B,oBAAoB,EAAE,IAAIpB,eAAe,CAACC,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,iBAAiBA,CAAA,EAAG;EACzB7B,WAAW,CAAC2B,oBAAoB,EAAE,IAAI,CAAC;AAC3C;AAEA,SAASG,UAAUA,CAACC,IAAI,EAAE;EACtB,MAAMC,WAAW,GAAG;IAChB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,KAAK;IACX,GAAG,EAAE,KAAK;IACV,GAAG,EAAE;EACT,CAAC;EACD,OAAOD,IAAI,CAACxO,OAAO,CAAC,UAAU,EAAEK,CAAC,IAAIoO,WAAW,CAACpO,CAAC,CAAC,CAAC;AACxD;AACA,SAASqO,YAAYA,CAACF,IAAI,EAAE;EACxB,MAAMG,aAAa,GAAG;IAClB,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,GAAG;IACV,KAAK,EAAE;EACX,CAAC;EACD,OAAOH,IAAI,CAACxO,OAAO,CAAC,UAAU,EAAEK,CAAC,IAAIsO,aAAa,CAACtO,CAAC,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuO,YAAYA,CAAC/H,GAAG,EAAE;EACvB,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgI,aAAa,CAAC;EAChB/Z,WAAWA,CAAA,EAAG;IACV,IAAI,CAACga,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACD,KAAK,GAAGE,wBAAwB,CAAClb,MAAM,CAACnC,QAAQ,CAAC,EAAEmC,MAAM,CAACtB,MAAM,CAAC,CAAC;EAC3E;EACA;AACJ;AACA;EACIgG,GAAGA,CAACqO,GAAG,EAAEoI,YAAY,EAAE;IACnB,OAAO,IAAI,CAACH,KAAK,CAACjI,GAAG,CAAC,KAAKhK,SAAS,GAAG,IAAI,CAACiS,KAAK,CAACjI,GAAG,CAAC,GAAGoI,YAAY;EACzE;EACA;AACJ;AACA;EACItS,GAAGA,CAACkK,GAAG,EAAEjE,KAAK,EAAE;IACZ,IAAI,CAACkM,KAAK,CAACjI,GAAG,CAAC,GAAGjE,KAAK;EAC3B;EACA;AACJ;AACA;EACIlN,MAAMA,CAACmR,GAAG,EAAE;IACR,OAAO,IAAI,CAACiI,KAAK,CAACjI,GAAG,CAAC;EAC1B;EACA;AACJ;AACA;EACIqI,MAAMA,CAACrI,GAAG,EAAE;IACR,OAAO,IAAI,CAACiI,KAAK,CAACK,cAAc,CAACtI,GAAG,CAAC;EACzC;EACA;AACJ;AACA;EACI,IAAIuI,OAAOA,CAAA,EAAG;IACV,OAAO/M,MAAM,CAAC1E,IAAI,CAAC,IAAI,CAACmR,KAAK,CAAC,CAAC/V,MAAM,KAAK,CAAC;EAC/C;EACA;AACJ;AACA;EACIsW,WAAWA,CAACxI,GAAG,EAAE7M,QAAQ,EAAE;IACvB,IAAI,CAAC+U,oBAAoB,CAAClI,GAAG,CAAC,GAAG7M,QAAQ;EAC7C;EACA;AACJ;AACA;EACIsV,MAAMA,CAAA,EAAG;IACL;IACA,KAAK,MAAMzI,GAAG,IAAI,IAAI,CAACkI,oBAAoB,EAAE;MACzC,IAAI,IAAI,CAACA,oBAAoB,CAACI,cAAc,CAACtI,GAAG,CAAC,EAAE;QAC/C,IAAI;UACA,IAAI,CAACiI,KAAK,CAACjI,GAAG,CAAC,GAAG,IAAI,CAACkI,oBAAoB,CAAClI,GAAG,CAAC,CAAC,CAAC;QACtD,CAAC,CACD,OAAOnI,CAAC,EAAE;UACN8O,OAAO,CAAC+B,IAAI,CAAC,qCAAqC,EAAE7Q,CAAC,CAAC;QAC1D;MACJ;IACJ;IACA,OAAO8Q,IAAI,CAACC,SAAS,CAAC,IAAI,CAACX,KAAK,CAAC;EACrC;AACJ;AACAD,aAAa,CAAC7T,IAAI,YAAA0U,sBAAAjV,CAAA;EAAA,YAAAA,CAAA,IAAyFoU,aAAa;AAAA,CAAoD;AAC5KA,aAAa,CAAC3T,KAAK,kBA14C2EnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EA04CeyT,aAAa;EAAAxT,OAAA,EAAbwT,aAAa,CAAA7T,IAAA;EAAA6E,UAAA,EAAc;AAAM,EAAG;AACnJ;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KA34C8FvJ,EAAE,CAAAwJ,iBAAA,CA24CJsT,aAAa,EAAc,CAAC;IAC5GrT,IAAI,EAAEnJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE8C,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASmP,wBAAwBA,CAAChZ,GAAG,EAAE+K,KAAK,EAAE;EAC1C;EACA;EACA,MAAM4O,MAAM,GAAG3Z,GAAG,CAAC4Z,cAAc,CAAC7O,KAAK,GAAG,QAAQ,CAAC;EACnD,IAAI8O,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIF,MAAM,IAAIA,MAAM,CAAC3Q,WAAW,EAAE;IAC9B,IAAI;MACA;MACA6Q,YAAY,GAAGL,IAAI,CAACM,KAAK,CAACpB,YAAY,CAACiB,MAAM,CAAC3Q,WAAW,CAAC,CAAC;IAC/D,CAAC,CACD,OAAON,CAAC,EAAE;MACN8O,OAAO,CAAC+B,IAAI,CAAC,kDAAkD,GAAGxO,KAAK,EAAErC,CAAC,CAAC;IAC/E;EACJ;EACA,OAAOmR,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,CAAC;AAEjCA,0BAA0B,CAAC/U,IAAI,YAAAgV,mCAAAvV,CAAA;EAAA,YAAAA,CAAA,IAAyFsV,0BAA0B;AAAA,CAAkD;AACpMA,0BAA0B,CAACpG,IAAI,kBA16C+D5X,EAAE,CAAA6X,gBAAA;EAAApO,IAAA,EA06CyBuU;AAA0B,EAAG;AACtJA,0BAA0B,CAAClG,IAAI,kBA36C+D9X,EAAE,CAAA+X,gBAAA,IA26CsD;AACtJ;EAAA,QAAAxO,SAAA,oBAAAA,SAAA,KA56C8FvJ,EAAE,CAAAwJ,iBAAA,CA46CJwU,0BAA0B,EAAc,CAAC;IACzHvU,IAAI,EAAE/H,QAAQ;IACdsJ,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMkT,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAClF,QAAQ,EAAE;IACjB,OAAQmF,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACrCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAEpF,QAAQ,CAAC,GACpD,KAAK;IACb,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOsF,SAASA,CAAC/U,IAAI,EAAE;IACnB,OAAQgV,SAAS,IAAKA,SAAS,CAACC,cAAc,CAACvJ,OAAO,CAAC1L,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAAS8U,cAAcA,CAACI,CAAC,EAAEzF,QAAQ,EAAE;EACjC,IAAIvZ,OAAO,CAAC,CAAC,CAAC4E,aAAa,CAACoa,CAAC,CAAC,EAAE;IAC5B,OAAOA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACC,OAAO,CAAC1F,QAAQ,CAAC,IACnCyF,CAAC,CAACE,iBAAiB,IAAIF,CAAC,CAACE,iBAAiB,CAAC3F,QAAQ,CAAC,IACpDyF,CAAC,CAACG,qBAAqB,IAAIH,CAAC,CAACG,qBAAqB,CAAC5F,QAAQ,CAAC;EACpE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,MAAM6F,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAI/e,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,MAAMgf,aAAa,GAAG,IAAIhf,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,MAAMif,mBAAmB,CAAC;EACtBnc,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACoc,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAChV,OAAO,EAAE;IACjB,MAAMiV,EAAE,GAAG,IAAIC,MAAM,CAAClV,OAAO,EAAE,IAAI,CAAC+L,OAAO,CAAC;IAC5CkJ,EAAE,CAAC7Y,GAAG,CAAC,OAAO,CAAC,CAACmE,GAAG,CAAC;MAAE4U,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCF,EAAE,CAAC7Y,GAAG,CAAC,QAAQ,CAAC,CAACmE,GAAG,CAAC;MAAE4U,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAMlV,SAAS,IAAI,IAAI,CAAC8U,SAAS,EAAE;MACpCE,EAAE,CAAC7Y,GAAG,CAAC6D,SAAS,CAAC,CAACM,GAAG,CAAC,IAAI,CAACwU,SAAS,CAAC9U,SAAS,CAAC,CAAC;IACpD;IACA,OAAOgV,EAAE;EACb;AACJ;AACAJ,mBAAmB,CAACjW,IAAI,YAAAwW,4BAAA/W,CAAA;EAAA,YAAAA,CAAA,IAAyFwW,mBAAmB;AAAA,CAAoD;AACxLA,mBAAmB,CAAC/V,KAAK,kBA7kDqEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EA6kDqB6V,mBAAmB;EAAA5V,OAAA,EAAnB4V,mBAAmB,CAAAjW;AAAA,EAAG;AAC3I;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KA9kD8FvJ,EAAE,CAAAwJ,iBAAA,CA8kDJ0V,mBAAmB,EAAc,CAAC;IAClHzV,IAAI,EAAEnJ;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMof,oBAAoB,SAASzU,kBAAkB,CAAC;EAClDlI,WAAWA,CAACkB,GAAG,EAAE0b,OAAO,EAAElE,OAAO,EAAEmE,MAAM,EAAE;IACvC,KAAK,CAAC3b,GAAG,CAAC;IACV,IAAI,CAAC0b,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAClE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC9B;EACAlV,QAAQA,CAACL,SAAS,EAAE;IAChB,IAAI,CAACyU,WAAW,CAAC3B,cAAc,CAAC9S,SAAS,CAACoK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACoL,aAAa,CAACxV,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAACvF,MAAM,CAACwa,MAAM,IAAI,CAAC,IAAI,CAACK,MAAM,EAAE;MAChC,IAAI,OAAOrW,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACkS,OAAO,CAAC+B,IAAI,CAAE,QAAOlT,SAAU,mDAAkD,GACjF,iDAAgD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA9G,gBAAgBA,CAAC6G,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMsL,IAAI,GAAG,IAAI,CAAC5L,OAAO,CAACS,OAAO,CAAC,CAAC;IACnCJ,SAAS,GAAGA,SAAS,CAACoK,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAAC3P,MAAM,CAACwa,MAAM,IAAI,IAAI,CAACK,MAAM,EAAE;MAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIhK,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACoL,MAAM,CAAC,CAAC,CAAC;MACxF;MACA;MACA;MACA,IAAIG,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAGA,CAAA,KAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACDlK,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACqL,cAAc,CAC3ClZ,IAAI,CAAC,MAAM;QACZ;QACA,IAAI,CAAC5B,MAAM,CAACwa,MAAM,EAAE;UAChB,IAAI,OAAOhW,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,IAAI,CAACkS,OAAO,CAAC+B,IAAI,CAAE,mEAAkE,CAAC;UAC1F;UACAwC,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAACxc,gBAAgB,CAAC6G,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CACG0V,KAAK,CAAC,MAAM;QACb,IAAI,OAAO1W,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAACkS,OAAO,CAAC+B,IAAI,CAAE,QAAOlT,SAAU,6CAA4C,GAC3E,0BAAyB,CAAC;QACnC;QACA0V,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,CAAC,CAAC;MAChB,CAAC;IACL;IACA,OAAOnK,IAAI,CAACrB,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAM8K,EAAE,GAAG,IAAI,CAACK,OAAO,CAACN,WAAW,CAAChV,OAAO,CAAC;MAC5C,MAAMpC,QAAQ,GAAG,SAAAA,CAAUiY,QAAQ,EAAE;QACjCrK,IAAI,CAACC,UAAU,CAAC,YAAY;UACxBvL,OAAO,CAAC2V,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACDZ,EAAE,CAACa,EAAE,CAAC7V,SAAS,EAAErC,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACTqX,EAAE,CAACc,GAAG,CAAC9V,SAAS,EAAErC,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAOqX,EAAE,CAAC7O,OAAO,KAAK,UAAU,EAAE;UAClC6O,EAAE,CAAC7O,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAqP,aAAaA,CAACxV,SAAS,EAAE;IACrB,OAAO,IAAI,CAACqV,OAAO,CAACR,MAAM,CAAChK,OAAO,CAAC7K,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;AACJ;AACAoV,oBAAoB,CAACzW,IAAI,YAAAoX,6BAAA3X,CAAA;EAAA,YAAAA,CAAA,IAAyFgX,oBAAoB,EA9qDxC1f,EAAE,CAAA6B,QAAA,CA8qDwDjC,QAAQ,GA9qDlEI,EAAE,CAAA6B,QAAA,CA8qD6Emd,qBAAqB,GA9qDpGhf,EAAE,CAAA6B,QAAA,CA8qD+G7B,EAAE,CAACgC,QAAQ,GA9qD5HhC,EAAE,CAAA6B,QAAA,CA8qDuIod,aAAa;AAAA,CAA6D;AACjTS,oBAAoB,CAACvW,KAAK,kBA/qDoEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EA+qDsBqW,oBAAoB;EAAApW,OAAA,EAApBoW,oBAAoB,CAAAzW;AAAA,EAAG;AAC7I;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAhrD8FvJ,EAAE,CAAAwJ,iBAAA,CAgrDJkW,oBAAoB,EAAc,CAAC;IACnHjW,IAAI,EAAEnJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE6J,IAAI,EAAEyV,mBAAmB;MAAEnU,UAAU,EAAE,CAAC;QAC5CtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACgU,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEvV,IAAI,EAAEzJ,EAAE,CAACgC;IAAS,CAAC,EAAE;MAAEyH,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzDtB,IAAI,EAAE9H;MACV,CAAC,EAAE;QACC8H,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACiU,aAAa;MACxB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,YAAY,CAAC;AAEnBA,YAAY,CAACrX,IAAI,YAAAsX,qBAAA7X,CAAA;EAAA,YAAAA,CAAA,IAAyF4X,YAAY;AAAA,CAAkD;AACxKA,YAAY,CAAC1I,IAAI,kBA5sD6E5X,EAAE,CAAA6X,gBAAA;EAAApO,IAAA,EA4sDW6W;AAAY,EAAG;AAC1HA,YAAY,CAACxI,IAAI,kBA7sD6E9X,EAAE,CAAA+X,gBAAA;EAAAtB,SAAA,EA6sDoC,CAC5H;IACIvP,OAAO,EAAEwC,qBAAqB;IAC9B0N,QAAQ,EAAEsI,oBAAoB;IAC9BrY,KAAK,EAAE,IAAI;IACXD,IAAI,EAAE,CAACxH,QAAQ,EAAEof,qBAAqB,EAAEhd,QAAQ,EAAE,CAAC,IAAIL,QAAQ,CAAC,CAAC,EAAEsd,aAAa,CAAC;EACrF,CAAC,EACD;IAAE/X,OAAO,EAAE8X,qBAAqB;IAAE5H,QAAQ,EAAE8H,mBAAmB;IAAE9X,IAAI,EAAE;EAAG,CAAC;AAC9E,EAAG;AACR;EAAA,QAAAmC,SAAA,oBAAAA,SAAA,KAttD8FvJ,EAAE,CAAAwJ,iBAAA,CAstDJ8W,YAAY,EAAc,CAAC;IAC3G7W,IAAI,EAAE/H,QAAQ;IACdsJ,IAAI,EAAE,CAAC;MACCyL,SAAS,EAAE,CACP;QACIvP,OAAO,EAAEwC,qBAAqB;QAC9B0N,QAAQ,EAAEsI,oBAAoB;QAC9BrY,KAAK,EAAE,IAAI;QACXD,IAAI,EAAE,CAACxH,QAAQ,EAAEof,qBAAqB,EAAEhd,QAAQ,EAAE,CAAC,IAAIL,QAAQ,CAAC,CAAC,EAAEsd,aAAa,CAAC;MACrF,CAAC,EACD;QAAE/X,OAAO,EAAE8X,qBAAqB;QAAE5H,QAAQ,EAAE8H,mBAAmB;QAAE9X,IAAI,EAAE;MAAG,CAAC;IAEnF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoZ,YAAY,CAAC;AAEnBA,YAAY,CAACvX,IAAI,YAAAwX,qBAAA/X,CAAA;EAAA,YAAAA,CAAA,IAAyF8X,YAAY;AAAA,CAAoD;AAC1KA,YAAY,CAACrX,KAAK,kBAvwD4EnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAuwDcmX,YAAY;EAAAlX,OAAA,WAAAmX,qBAAA/X,CAAA;IAAA,IAAAuR,CAAA;IAAA,IAAAvR,CAAA;MAAAuR,CAAA,QAAAvR,CAAA,IAAZ8X,YAAY;IAAA;MAAAvG,CAAA,GAvwD5Bja,EAAE,CAAA6B,QAAA,CAuwDgG6e,gBAAgB;IAAA;IAAA,OAAAzG,CAAA;EAAA;EAAAnM,UAAA,EAAxE;AAAM,EAAyE;AACvN;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KAxwD8FvJ,EAAE,CAAAwJ,iBAAA,CAwwDJgX,YAAY,EAAc,CAAC;IAC3G/W,IAAI,EAAEnJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE8C,UAAU,EAAE,MAAM;MAAEuJ,WAAW,EAAEpV,UAAU,CAAC,MAAMye,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,SAASC,uBAAuBA,CAACna,QAAQ,EAAE;EACvC,OAAO,IAAIka,gBAAgB,CAACla,QAAQ,CAACC,GAAG,CAAC7G,QAAQ,CAAC,CAAC;AACvD;AACA,MAAM8gB,gBAAgB,SAASF,YAAY,CAAC;EACxCzd,WAAWA,CAACmI,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA0V,QAAQA,CAACC,GAAG,EAAEhQ,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQgQ,GAAG;MACP,KAAK1e,eAAe,CAAC2e,IAAI;QACrB,OAAOjQ,KAAK;MAChB,KAAK1O,eAAe,CAAC4e,IAAI;QACrB,IAAI3e,gCAAgC,CAACyO,KAAK,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE;UACvE,OAAOxO,gBAAgB,CAACwO,KAAK,CAAC;QAClC;QACA,OAAOtO,cAAc,CAAC,IAAI,CAAC2I,IAAI,EAAE8V,MAAM,CAACnQ,KAAK,CAAC,CAAC,CAACoQ,QAAQ,CAAC,CAAC;MAC9D,KAAK9e,eAAe,CAAC+e,KAAK;QACtB,IAAI9e,gCAAgC,CAACyO,KAAK,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAAE;UACzE,OAAOxO,gBAAgB,CAACwO,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAK1O,eAAe,CAACgf,MAAM;QACvB,IAAI/e,gCAAgC,CAACyO,KAAK,EAAE,QAAQ,CAAC,uBAAuB,CAAC,EAAE;UAC3E,OAAOxO,gBAAgB,CAACwO,KAAK,CAAC;QAClC;QACA,MAAM,IAAIhJ,KAAK,CAAC,uCAAuC,CAAC;MAC5D,KAAK1F,eAAe,CAACif,GAAG;QACpB,IAAIhf,gCAAgC,CAACyO,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrE,OAAOxO,gBAAgB,CAACwO,KAAK,CAAC;QAClC;QACA,OAAOvO,aAAa,CAAC0e,MAAM,CAACnQ,KAAK,CAAC,CAAC;MACvC,KAAK1O,eAAe,CAACkf,YAAY;QAC7B,IAAIjf,gCAAgC,CAACyO,KAAK,EAAE,aAAa,CAAC,4BAA4B,CAAC,EAAE;UACrF,OAAOxO,gBAAgB,CAACwO,KAAK,CAAC;QAClC;QACA,MAAM,IAAIhJ,KAAK,CAAE,oDAAmD3F,iBAAkB,GAAE,CAAC;MAC7F;QACI,MAAM,IAAI2F,KAAK,CAAE,8BAA6BgZ,GAAI,SAAQ3e,iBAAkB,GAAE,CAAC;IACvF;EACJ;EACAof,uBAAuBA,CAACzQ,KAAK,EAAE;IAC3B,OAAOrO,4BAA4B,CAACqO,KAAK,CAAC;EAC9C;EACA0Q,wBAAwBA,CAAC1Q,KAAK,EAAE;IAC5B,OAAOpO,6BAA6B,CAACoO,KAAK,CAAC;EAC/C;EACA2Q,yBAAyBA,CAAC3Q,KAAK,EAAE;IAC7B,OAAOnO,8BAA8B,CAACmO,KAAK,CAAC;EAChD;EACA4Q,sBAAsBA,CAAC5Q,KAAK,EAAE;IAC1B,OAAOlO,2BAA2B,CAACkO,KAAK,CAAC;EAC7C;EACA6Q,8BAA8BA,CAAC7Q,KAAK,EAAE;IAClC,OAAOjO,mCAAmC,CAACiO,KAAK,CAAC;EACrD;AACJ;AACA6P,gBAAgB,CAACzX,IAAI,YAAA0Y,yBAAAjZ,CAAA;EAAA,YAAAA,CAAA,IAAyFgY,gBAAgB,EAv0DhC1gB,EAAE,CAAA6B,QAAA,CAu0DgDjC,QAAQ;AAAA,CAA6C;AACrM8gB,gBAAgB,CAACvX,KAAK,kBAx0DwEnJ,EAAE,CAAAoJ,kBAAA;EAAAC,KAAA,EAw0DkBqX,gBAAgB;EAAApX,OAAA,WAAAqY,yBAAAjZ,CAAA;IAAA,IAAAuR,CAAA;IAAA,IAAAvR,CAAA;MAAAuR,CAAA,OAAAvR,CAAA;IAAA;MAAAuR,CAAA,GAAkC0G,uBAAuB,CAx0D7F3gB,EAAE,CAAA6B,QAAA,CAw0D6GzB,QAAQ;IAAA;IAAA,OAAA6Z,CAAA;EAAA;EAAAnM,UAAA,EAArE;AAAM,EAAqE;AAC3N;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KAz0D8FvJ,EAAE,CAAAwJ,iBAAA,CAy0DJkX,gBAAgB,EAAc,CAAC;IAC/GjX,IAAI,EAAEnJ,UAAU;IAChB0K,IAAI,EAAE,CAAC;MAAE8C,UAAU,EAAE,MAAM;MAAE3G,UAAU,EAAEwZ,uBAAuB;MAAEvZ,IAAI,EAAE,CAAChH,QAAQ;IAAE,CAAC;EACxF,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEqJ,IAAI,EAAEqB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAElJ,MAAM;QACZyK,IAAI,EAAE,CAACpL,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgiB,OAAO,GAAG,IAAI/e,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASyU,aAAa,EAAE0G,0BAA0B,EAAEE,EAAE,EAAEsC,YAAY,EAAE9W,qBAAqB,EAAEC,YAAY,EAAEqV,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEoB,YAAY,EAAEnI,IAAI,EAAEtK,kCAAkC,EAAEuM,KAAK,EAAE0C,aAAa,EAAE8E,OAAO,EAAE1L,oBAAoB,EAAEI,iBAAiB,EAAEiG,iBAAiB,EAAED,gBAAgB,EAAEO,YAAY,EAAE3F,eAAe,EAAEN,+BAA+B,EAAE1T,iBAAiB,IAAI2e,kBAAkB,EAAEva,qBAAqB,IAAIwa,sBAAsB,EAAEpO,eAAe,IAAIqO,gBAAgB,EAAElT,mBAAmB,IAAImT,oBAAoB,EAAEtB,gBAAgB,IAAIuB,iBAAiB,EAAE7V,mBAAmB,IAAI8V,oBAAoB,EAAExC,oBAAoB,IAAIyC,qBAAqB,EAAExL,mCAAmC,IAAIyL,oCAAoC,EAAEjO,eAAe,IAAIkO,gBAAgB,EAAE/U,cAAc,IAAIgV,eAAe,EAAEnX,gBAAgB,IAAIoX,iBAAiB,EAAElc,aAAa,IAAImc,cAAc,EAAEhG,UAAU,IAAIiG,WAAW,EAAEtU,aAAa,IAAIuU,cAAc,EAAE5L,cAAc,IAAI6L,eAAe,EAAE5U,oBAAoB,IAAI6U,qBAAqB,EAAE1U,iBAAiB,IAAI2U,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
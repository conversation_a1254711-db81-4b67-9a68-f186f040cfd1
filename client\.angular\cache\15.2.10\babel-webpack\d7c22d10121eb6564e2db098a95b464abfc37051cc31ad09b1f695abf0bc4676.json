{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngx-formly/core\";\nimport * as i4 from \"@ngx-translate/core\";\nexport class ModalChangeClubComponent {\n  ngOnInit() {\n    console.log(this.clubs);\n    console.log(this.current_club);\n    this.fields = [{\n      key: 'club_id',\n      type: 'select',\n      templateOptions: {\n        label: 'Club',\n        options: this.clubs,\n        required: true,\n        valueProp: 'id',\n        labelProp: 'name'\n      }\n    }];\n    this.model = {\n      club_id: this.current_club.id\n    };\n  }\n  constructor(activeModal) {\n    this.activeModal = activeModal;\n    this.form = new FormGroup({});\n    this.model = {};\n    this.options = {};\n    this.fields = [];\n  }\n  dismissModal() {\n    this.activeModal.dismiss('Close click');\n  }\n  submit() {\n    if (this.form.valid) {\n      // Perform your submit logic here\n      this.activeModal.close(this.model);\n    }\n  }\n  static #_ = this.ɵfac = function ModalChangeClubComponent_Factory(t) {\n    return new (t || ModalChangeClubComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalChangeClubComponent,\n    selectors: [[\"app-modal-change-club\"]],\n    inputs: {\n      clubs: \"clubs\",\n      current_club: \"current_club\"\n    },\n    decls: 17,\n    vars: 14,\n    consts: [[1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [3, \"formGroup\", \"ngSubmit\"], [3, \"form\", \"fields\", \"model\", \"options\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function ModalChangeClubComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalChangeClubComponent_Template_button_click_4_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 4)(8, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalChangeClubComponent_Template_form_ngSubmit_8_listener() {\n          return ctx.submit();\n        });\n        i0.ɵɵelement(9, \"formly-form\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function ModalChangeClubComponent_Template_button_click_11_listener() {\n          return ctx.dismissModal();\n        });\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ModalChangeClubComponent_Template_button_click_14_listener() {\n          return ctx.submit();\n        });\n        i0.ɵɵtext(15);\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 8, \"Change Club\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model)(\"options\", ctx.options);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, \"Close\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 12, \"Submit\"));\n      }\n    },\n    dependencies: [i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i3.FormlyForm, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;AAS1C,OAAM,MAAOC,wBAAwB;EAanCC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;IACvBF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACE,YAAY,CAAC;IAE9B,IAAI,CAACC,MAAM,GAAG,CACZ;MACEC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,QAAQ;MACdC,eAAe,EAAE;QACfC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,IAAI,CAACP,KAAK;QACnBQ,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE;;KAEd,CACF;IAED,IAAI,CAACC,KAAK,GAAG;MACXC,OAAO,EAAE,IAAI,CAACX,YAAY,CAACY;KAC5B;EAGH;EACAC,YAAmBC,WAA2B;IAA3B,KAAAA,WAAW,GAAXA,WAAW;IAlC9B,KAAAC,IAAI,GAAG,IAAIrB,SAAS,CAAC,EAAE,CAAC;IAExB,KAAAgB,KAAK,GAAG,EAAE;IACV,KAAAJ,OAAO,GAAsB,EAAE;IAG/B,KAAAL,MAAM,GAAwB,EAE7B;EA0BgD;EAGjDe,YAAYA,CAAA;IACV,IAAI,CAACF,WAAW,CAACG,OAAO,CAAC,aAAa,CAAC;EACzC;EAEAC,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACH,IAAI,CAACI,KAAK,EAAE;MACnB;MACA,IAAI,CAACL,WAAW,CAACM,KAAK,CAAC,IAAI,CAACV,KAAK,CAAC;;EAEtC;EAAC,QAAAW,CAAA;qBAjDU1B,wBAAwB,EAAA2B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;UAAxB/B,wBAAwB;IAAAgC,SAAA;IAAAC,MAAA;MAAA7B,KAAA;MAAAC,YAAA;IAAA;IAAA6B,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVrCZ,EAAA,CAAAc,cAAA,aAA0B;QACEd,EAAA,CAAAe,MAAA,GAA6B;;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAC1DhB,EAAA,CAAAc,cAAA,gBAAgF;QAAzBd,EAAA,CAAAiB,UAAA,mBAAAC,0DAAA;UAAA,OAASL,GAAA,CAAAnB,YAAA,EAAc;QAAA,EAAC;QAC7EM,EAAA,CAAAc,cAAA,cAAyB;QAAAd,EAAA,CAAAe,MAAA,aAAO;QAAAf,EAAA,CAAAgB,YAAA,EAAO;QAG3ChB,EAAA,CAAAc,cAAA,aAAwB;QACGd,EAAA,CAAAiB,UAAA,sBAAAE,2DAAA;UAAA,OAAYN,GAAA,CAAAjB,MAAA,EAAQ;QAAA,EAAC;QAC5CI,EAAA,CAAAoB,SAAA,qBAA+F;QACjGpB,EAAA,CAAAgB,YAAA,EAAO;QAEThB,EAAA,CAAAc,cAAA,cAA0B;QACwBd,EAAA,CAAAiB,UAAA,mBAAAI,2DAAA;UAAA,OAASR,GAAA,CAAAnB,YAAA,EAAc;QAAA,EAAC;QAACM,EAAA,CAAAe,MAAA,IAAuB;;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QACzGhB,EAAA,CAAAc,cAAA,iBAAiE;QAAnBd,EAAA,CAAAiB,UAAA,mBAAAK,2DAAA;UAAA,OAAST,GAAA,CAAAjB,MAAA,EAAQ;QAAA,EAAC;QAACI,EAAA,CAAAe,MAAA,IAAwB;;QAAAf,EAAA,CAAAgB,YAAA,EAAS;;;QAZ1EhB,EAAA,CAAAuB,SAAA,GAA6B;QAA7BvB,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAAyB,WAAA,sBAA6B;QAM/CzB,EAAA,CAAAuB,SAAA,GAAkB;QAAlBvB,EAAA,CAAA0B,UAAA,cAAAb,GAAA,CAAApB,IAAA,CAAkB;QACTO,EAAA,CAAAuB,SAAA,GAAa;QAAbvB,EAAA,CAAA0B,UAAA,SAAAb,GAAA,CAAApB,IAAA,CAAa,WAAAoB,GAAA,CAAAlC,MAAA,WAAAkC,GAAA,CAAAzB,KAAA,aAAAyB,GAAA,CAAA7B,OAAA;QAI6CgB,EAAA,CAAAuB,SAAA,GAAuB;QAAvBvB,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAAyB,WAAA,kBAAuB;QAC/BzB,EAAA,CAAAuB,SAAA,GAAwB;QAAxBvB,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAAyB,WAAA,mBAAwB", "names": ["FormGroup", "ModalChangeClubComponent", "ngOnInit", "console", "log", "clubs", "current_club", "fields", "key", "type", "templateOptions", "label", "options", "required", "valueProp", "labelProp", "model", "club_id", "id", "constructor", "activeModal", "form", "dismissModal", "dismiss", "submit", "valid", "close", "_", "i0", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ModalChangeClubComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModalChangeClubComponent_Template_button_click_4_listener", "ModalChangeClubComponent_Template_form_ngSubmit_8_listener", "ɵɵelement", "ModalChangeClubComponent_Template_button_click_11_listener", "ModalChangeClubComponent_Template_button_click_14_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\modal-change-club\\modal-change-club.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\admin-registration\\modal-change-club\\modal-change-club.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'app-modal-change-club',\r\n  templateUrl: './modal-change-club.component.html',\r\n  styleUrls: ['./modal-change-club.component.scss']\r\n})\r\nexport class ModalChangeClubComponent {\r\n  @Input() clubs: any;\r\n  @Input() current_club: any;\r\n  form = new FormGroup({});\r\n  modalRef: any;\r\n  model = {};\r\n  options: FormlyFormOptions = {};\r\n  \r\n\r\n  fields: FormlyFieldConfig[] = [\r\n  \r\n  ];\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.clubs);\r\n    console.log(this.current_club);\r\n\r\n    this.fields = [\r\n      {\r\n        key: 'club_id',\r\n        type: 'select',\r\n        templateOptions: {\r\n          label: 'Club',\r\n          options: this.clubs,\r\n          required: true,\r\n          valueProp: 'id',\r\n          labelProp: 'name',\r\n        },\r\n      },\r\n    ];\r\n\r\n    this.model = {\r\n      club_id: this.current_club.id,\r\n    };\r\n\r\n   \r\n  }\r\n  constructor(public activeModal: NgbActiveModal) {}\r\n\r\n\r\n  dismissModal() {\r\n    this.activeModal.dismiss('Close click');\r\n  }\r\n\r\n  submit() {\r\n    if (this.form.valid) {\r\n      // Perform your submit logic here\r\n      this.activeModal.close(this.model);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"modal-header\">\r\n    <h4 class=\"modal-title\">{{'Change Club' | translate}}</h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"dismissModal()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <div class=\"modal-body\">\r\n    <form [formGroup]=\"form\" (ngSubmit)=\"submit()\">\r\n      <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\" [options]=\"options\"></formly-form>\r\n    </form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"button\" class=\"btn btn-secondary\" (click)=\"dismissModal()\">{{'Close' | translate}}</button>\r\n    <button type=\"button\" class=\"btn btn-primary\" (click)=\"submit()\">{{'Submit' | translate}}</button>\r\n  </div>\r\n  "]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
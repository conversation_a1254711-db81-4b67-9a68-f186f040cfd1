{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { isArray } from '../util/isArray';\nimport { EMPTY } from './empty';\nexport function onErrorResumeNext(...sources) {\n  if (sources.length === 0) {\n    return EMPTY;\n  }\n  const [first, ...remainder] = sources;\n  if (sources.length === 1 && isArray(first)) {\n    return onErrorResumeNext(...first);\n  }\n  return new Observable(subscriber => {\n    const subNext = () => subscriber.add(onErrorResumeNext(...remainder).subscribe(subscriber));\n    return from(first).subscribe({\n      next(value) {\n        subscriber.next(value);\n      },\n      error: subNext,\n      complete: subNext\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "from", "isArray", "EMPTY", "onErrorResumeNext", "sources", "length", "first", "remainder", "subscriber", "subNext", "add", "subscribe", "next", "value", "error", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/onErrorResumeNext.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { from } from './from';\nimport { isArray } from '../util/isArray';\nimport { EMPTY } from './empty';\nexport function onErrorResumeNext(...sources) {\n    if (sources.length === 0) {\n        return EMPTY;\n    }\n    const [first, ...remainder] = sources;\n    if (sources.length === 1 && isArray(first)) {\n        return onErrorResumeNext(...first);\n    }\n    return new Observable(subscriber => {\n        const subNext = () => subscriber.add(onErrorResumeNext(...remainder).subscribe(subscriber));\n        return from(first).subscribe({\n            next(value) { subscriber.next(value); },\n            error: subNext,\n            complete: subNext,\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,iBAAiBA,CAAC,GAAGC,OAAO,EAAE;EAC1C,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOH,KAAK;EAChB;EACA,MAAM,CAACI,KAAK,EAAE,GAAGC,SAAS,CAAC,GAAGH,OAAO;EACrC,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACK,KAAK,CAAC,EAAE;IACxC,OAAOH,iBAAiB,CAAC,GAAGG,KAAK,CAAC;EACtC;EACA,OAAO,IAAIP,UAAU,CAACS,UAAU,IAAI;IAChC,MAAMC,OAAO,GAAGA,CAAA,KAAMD,UAAU,CAACE,GAAG,CAACP,iBAAiB,CAAC,GAAGI,SAAS,CAAC,CAACI,SAAS,CAACH,UAAU,CAAC,CAAC;IAC3F,OAAOR,IAAI,CAACM,KAAK,CAAC,CAACK,SAAS,CAAC;MACzBC,IAAIA,CAACC,KAAK,EAAE;QAAEL,UAAU,CAACI,IAAI,CAACC,KAAK,CAAC;MAAE,CAAC;MACvCC,KAAK,EAAEL,OAAO;MACdM,QAAQ,EAAEN;IACd,CAAC,CAAC;EACN,CAAC,CAAC;AACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
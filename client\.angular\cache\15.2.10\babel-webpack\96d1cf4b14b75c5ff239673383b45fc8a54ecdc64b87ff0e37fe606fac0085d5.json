{"ast": null, "code": "import { registerPlugin } from '@capacitor/core';\nconst PushNotifications = registerPlugin('PushNotifications', {});\nexport * from './definitions';\nexport { PushNotifications };", "map": {"version": 3, "names": ["registerPlugin", "PushNotifications"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/push-notifications/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst PushNotifications = registerPlugin('PushNotifications', {});\nexport * from './definitions';\nexport { PushNotifications };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iBAAiB;AAChD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;AACjE,cAAc,eAAe;AAC7B,SAASC,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var getSrc = item => typeof item === 'string' ? item : item.src;\nvar getArgs = ({\n  amount = 3,\n  format = 'array',\n  group = 20,\n  sample = 10\n} = {}) => ({\n  amount,\n  format,\n  group,\n  sample\n});\nvar format = (input, args) => {\n  var list = input.map(val => {\n    var rgb = Array.isArray(val) ? val : val.split(',').map(Number);\n    return args.format === 'hex' ? rgbToHex(rgb) : rgb;\n  });\n  return args.amount === 1 || list.length === 1 ? list[0] : list;\n};\nvar group = (number, grouping) => {\n  var grouped = Math.round(number / grouping) * grouping;\n  return Math.min(grouped, 255);\n};\nvar rgbToHex = rgb => '#' + rgb.map(val => {\n  var hex = val.toString(16);\n  return hex.length === 1 ? '0' + hex : hex;\n}).join('');\nvar getImageData = src => new Promise((resolve, reject) => {\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var img = new Image();\n  img.onload = () => {\n    canvas.height = img.height;\n    canvas.width = img.width;\n    context.drawImage(img, 0, 0);\n    var data = context.getImageData(0, 0, img.width, img.height).data;\n    resolve(data);\n  };\n  img.onerror = () => reject(Error('Image loading failed.'));\n  img.crossOrigin = '';\n  img.src = src;\n});\nvar getAverage = (data, args) => {\n  var gap = 4 * args.sample;\n  var amount = data.length / gap;\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  for (var i = 0; i < data.length; i += gap) {\n    rgb.r += data[i];\n    rgb.g += data[i + 1];\n    rgb.b += data[i + 2];\n  }\n  return format([[Math.round(rgb.r / amount), Math.round(rgb.g / amount), Math.round(rgb.b / amount)]], args);\n};\nvar getProminent = (data, args) => {\n  var gap = 4 * args.sample;\n  var colors = {};\n  for (var i = 0; i < data.length; i += gap) {\n    var rgb = [group(data[i], args.group), group(data[i + 1], args.group), group(data[i + 2], args.group)].join();\n    colors[rgb] = colors[rgb] ? colors[rgb] + 1 : 1;\n  }\n  return format(Object.entries(colors).sort(([_keyA, valA], [_keyB, valB]) => valA > valB ? -1 : 1).slice(0, args.amount).map(([rgb]) => rgb), args);\n};\nvar process = (handler, item, args) => new Promise((resolve, reject) => getImageData(getSrc(item)).then(data => resolve(handler(data, getArgs(args)))).catch(error => reject(error)));\nvar average = (item, args) => process(getAverage, item, args);\nvar prominent = (item, args) => process(getProminent, item, args);\nexport { average, prominent };", "map": {"version": 3, "names": ["getSrc", "item", "src", "getArgs", "amount", "format", "group", "sample", "input", "args", "list", "map", "val", "rgb", "Array", "isArray", "split", "Number", "rgbToHex", "length", "number", "grouping", "grouped", "Math", "round", "min", "hex", "toString", "join", "getImageData", "Promise", "resolve", "reject", "canvas", "document", "createElement", "context", "getContext", "img", "Image", "onload", "height", "width", "drawImage", "data", "onerror", "Error", "crossOrigin", "getAverage", "gap", "r", "g", "b", "i", "getProminent", "colors", "Object", "entries", "sort", "_keyA", "valA", "_keyB", "valB", "slice", "process", "handler", "then", "catch", "error", "average", "prominent"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/color.js/dist/color.esm.js"], "sourcesContent": ["var getSrc = item => typeof item === 'string' ? item : item.src;\n\nvar getArgs = ({\n  amount = 3,\n  format = 'array',\n  group = 20,\n  sample = 10\n} = {}) => ({\n  amount,\n  format,\n  group,\n  sample\n});\n\nvar format = (input, args) => {\n  var list = input.map(val => {\n    var rgb = Array.isArray(val) ? val : val.split(',').map(Number);\n    return args.format === 'hex' ? rgbToHex(rgb) : rgb;\n  });\n  return args.amount === 1 || list.length === 1 ? list[0] : list;\n};\n\nvar group = (number, grouping) => {\n  var grouped = Math.round(number / grouping) * grouping;\n  return Math.min(grouped, 255);\n};\n\nvar rgbToHex = rgb => '#' + rgb.map(val => {\n  var hex = val.toString(16);\n  return hex.length === 1 ? '0' + hex : hex;\n}).join('');\n\nvar getImageData = src => new Promise((resolve, reject) => {\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var img = new Image();\n\n  img.onload = () => {\n    canvas.height = img.height;\n    canvas.width = img.width;\n    context.drawImage(img, 0, 0);\n    var data = context.getImageData(0, 0, img.width, img.height).data;\n    resolve(data);\n  };\n\n  img.onerror = () => reject(Error('Image loading failed.'));\n\n  img.crossOrigin = '';\n  img.src = src;\n});\n\nvar getAverage = (data, args) => {\n  var gap = 4 * args.sample;\n  var amount = data.length / gap;\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n\n  for (var i = 0; i < data.length; i += gap) {\n    rgb.r += data[i];\n    rgb.g += data[i + 1];\n    rgb.b += data[i + 2];\n  }\n\n  return format([[Math.round(rgb.r / amount), Math.round(rgb.g / amount), Math.round(rgb.b / amount)]], args);\n};\n\nvar getProminent = (data, args) => {\n  var gap = 4 * args.sample;\n  var colors = {};\n\n  for (var i = 0; i < data.length; i += gap) {\n    var rgb = [group(data[i], args.group), group(data[i + 1], args.group), group(data[i + 2], args.group)].join();\n    colors[rgb] = colors[rgb] ? colors[rgb] + 1 : 1;\n  }\n\n  return format(Object.entries(colors).sort(([_keyA, valA], [_keyB, valB]) => valA > valB ? -1 : 1).slice(0, args.amount).map(([rgb]) => rgb), args);\n};\n\nvar process = (handler, item, args) => new Promise((resolve, reject) => getImageData(getSrc(item)).then(data => resolve(handler(data, getArgs(args)))).catch(error => reject(error)));\n\nvar average = (item, args) => process(getAverage, item, args);\n\nvar prominent = (item, args) => process(getProminent, item, args);\n\nexport { average, prominent };\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACC,GAAG;AAE/D,IAAIC,OAAO,GAAGA,CAAC;EACbC,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,OAAO;EAChBC,KAAK,GAAG,EAAE;EACVC,MAAM,GAAG;AACX,CAAC,GAAG,CAAC,CAAC,MAAM;EACVH,MAAM;EACNC,MAAM;EACNC,KAAK;EACLC;AACF,CAAC,CAAC;AAEF,IAAIF,MAAM,GAAGA,CAACG,KAAK,EAAEC,IAAI,KAAK;EAC5B,IAAIC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAACC,GAAG,IAAI;IAC1B,IAAIC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC,CAACL,GAAG,CAACM,MAAM,CAAC;IAC/D,OAAOR,IAAI,CAACJ,MAAM,KAAK,KAAK,GAAGa,QAAQ,CAACL,GAAG,CAAC,GAAGA,GAAG;EACpD,CAAC,CAAC;EACF,OAAOJ,IAAI,CAACL,MAAM,KAAK,CAAC,IAAIM,IAAI,CAACS,MAAM,KAAK,CAAC,GAAGT,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;AAChE,CAAC;AAED,IAAIJ,KAAK,GAAGA,CAACc,MAAM,EAAEC,QAAQ,KAAK;EAChC,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAGC,QAAQ,CAAC,GAAGA,QAAQ;EACtD,OAAOE,IAAI,CAACE,GAAG,CAACH,OAAO,EAAE,GAAG,CAAC;AAC/B,CAAC;AAED,IAAIJ,QAAQ,GAAGL,GAAG,IAAI,GAAG,GAAGA,GAAG,CAACF,GAAG,CAACC,GAAG,IAAI;EACzC,IAAIc,GAAG,GAAGd,GAAG,CAACe,QAAQ,CAAC,EAAE,CAAC;EAC1B,OAAOD,GAAG,CAACP,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGO,GAAG,GAAGA,GAAG;AAC3C,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AAEX,IAAIC,YAAY,GAAG3B,GAAG,IAAI,IAAI4B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;EACzD,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC7C,IAAIC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACrC,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;EAErBD,GAAG,CAACE,MAAM,GAAG,MAAM;IACjBP,MAAM,CAACQ,MAAM,GAAGH,GAAG,CAACG,MAAM;IAC1BR,MAAM,CAACS,KAAK,GAAGJ,GAAG,CAACI,KAAK;IACxBN,OAAO,CAACO,SAAS,CAACL,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B,IAAIM,IAAI,GAAGR,OAAO,CAACP,YAAY,CAAC,CAAC,EAAE,CAAC,EAAES,GAAG,CAACI,KAAK,EAAEJ,GAAG,CAACG,MAAM,CAAC,CAACG,IAAI;IACjEb,OAAO,CAACa,IAAI,CAAC;EACf,CAAC;EAEDN,GAAG,CAACO,OAAO,GAAG,MAAMb,MAAM,CAACc,KAAK,CAAC,uBAAuB,CAAC,CAAC;EAE1DR,GAAG,CAACS,WAAW,GAAG,EAAE;EACpBT,GAAG,CAACpC,GAAG,GAAGA,GAAG;AACf,CAAC,CAAC;AAEF,IAAI8C,UAAU,GAAGA,CAACJ,IAAI,EAAEnC,IAAI,KAAK;EAC/B,IAAIwC,GAAG,GAAG,CAAC,GAAGxC,IAAI,CAACF,MAAM;EACzB,IAAIH,MAAM,GAAGwC,IAAI,CAACzB,MAAM,GAAG8B,GAAG;EAC9B,IAAIpC,GAAG,GAAG;IACRqC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAACzB,MAAM,EAAEkC,CAAC,IAAIJ,GAAG,EAAE;IACzCpC,GAAG,CAACqC,CAAC,IAAIN,IAAI,CAACS,CAAC,CAAC;IAChBxC,GAAG,CAACsC,CAAC,IAAIP,IAAI,CAACS,CAAC,GAAG,CAAC,CAAC;IACpBxC,GAAG,CAACuC,CAAC,IAAIR,IAAI,CAACS,CAAC,GAAG,CAAC,CAAC;EACtB;EAEA,OAAOhD,MAAM,CAAC,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACX,GAAG,CAACqC,CAAC,GAAG9C,MAAM,CAAC,EAAEmB,IAAI,CAACC,KAAK,CAACX,GAAG,CAACsC,CAAC,GAAG/C,MAAM,CAAC,EAAEmB,IAAI,CAACC,KAAK,CAACX,GAAG,CAACuC,CAAC,GAAGhD,MAAM,CAAC,CAAC,CAAC,EAAEK,IAAI,CAAC;AAC7G,CAAC;AAED,IAAI6C,YAAY,GAAGA,CAACV,IAAI,EAAEnC,IAAI,KAAK;EACjC,IAAIwC,GAAG,GAAG,CAAC,GAAGxC,IAAI,CAACF,MAAM;EACzB,IAAIgD,MAAM,GAAG,CAAC,CAAC;EAEf,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAACzB,MAAM,EAAEkC,CAAC,IAAIJ,GAAG,EAAE;IACzC,IAAIpC,GAAG,GAAG,CAACP,KAAK,CAACsC,IAAI,CAACS,CAAC,CAAC,EAAE5C,IAAI,CAACH,KAAK,CAAC,EAAEA,KAAK,CAACsC,IAAI,CAACS,CAAC,GAAG,CAAC,CAAC,EAAE5C,IAAI,CAACH,KAAK,CAAC,EAAEA,KAAK,CAACsC,IAAI,CAACS,CAAC,GAAG,CAAC,CAAC,EAAE5C,IAAI,CAACH,KAAK,CAAC,CAAC,CAACsB,IAAI,CAAC,CAAC;IAC7G2B,MAAM,CAAC1C,GAAG,CAAC,GAAG0C,MAAM,CAAC1C,GAAG,CAAC,GAAG0C,MAAM,CAAC1C,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACjD;EAEA,OAAOR,MAAM,CAACmD,MAAM,CAACC,OAAO,CAACF,MAAM,CAAC,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,EAAEC,IAAI,CAAC,EAAE,CAACC,KAAK,EAAEC,IAAI,CAAC,KAAKF,IAAI,GAAGE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAEtD,IAAI,CAACL,MAAM,CAAC,CAACO,GAAG,CAAC,CAAC,CAACE,GAAG,CAAC,KAAKA,GAAG,CAAC,EAAEJ,IAAI,CAAC;AACpJ,CAAC;AAED,IAAIuD,OAAO,GAAGA,CAACC,OAAO,EAAEhE,IAAI,EAAEQ,IAAI,KAAK,IAAIqB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAKH,YAAY,CAAC7B,MAAM,CAACC,IAAI,CAAC,CAAC,CAACiE,IAAI,CAACtB,IAAI,IAAIb,OAAO,CAACkC,OAAO,CAACrB,IAAI,EAAEzC,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0D,KAAK,CAACC,KAAK,IAAIpC,MAAM,CAACoC,KAAK,CAAC,CAAC,CAAC;AAErL,IAAIC,OAAO,GAAGA,CAACpE,IAAI,EAAEQ,IAAI,KAAKuD,OAAO,CAAChB,UAAU,EAAE/C,IAAI,EAAEQ,IAAI,CAAC;AAE7D,IAAI6D,SAAS,GAAGA,CAACrE,IAAI,EAAEQ,IAAI,KAAKuD,OAAO,CAACV,YAAY,EAAErD,IAAI,EAAEQ,IAAI,CAAC;AAEjE,SAAS4D,OAAO,EAAEC,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin } from '@capacitor/core';\nexport class ShareWeb extends WebPlugin {\n  canShare() {\n    return _asyncToGenerator(function* () {\n      if (typeof navigator === 'undefined' || !navigator.share) {\n        return {\n          value: false\n        };\n      } else {\n        return {\n          value: true\n        };\n      }\n    })();\n  }\n  share(options) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (typeof navigator === 'undefined' || !navigator.share) {\n        throw _this.unavailable('Share API not available in this browser');\n      }\n      yield navigator.share({\n        title: options.title,\n        text: options.text,\n        url: options.url\n      });\n      return {};\n    })();\n  }\n}", "map": {"version": 3, "names": ["WebPlugin", "ShareWeb", "canShare", "_asyncToGenerator", "navigator", "share", "value", "options", "_this", "unavailable", "title", "text", "url"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/share/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class ShareWeb extends WebPlugin {\n    async canShare() {\n        if (typeof navigator === 'undefined' || !navigator.share) {\n            return { value: false };\n        }\n        else {\n            return { value: true };\n        }\n    }\n    async share(options) {\n        if (typeof navigator === 'undefined' || !navigator.share) {\n            throw this.unavailable('Share API not available in this browser');\n        }\n        await navigator.share({\n            title: options.title,\n            text: options.text,\n            url: options.url,\n        });\n        return {};\n    }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,QAAQ,SAASD,SAAS,CAAC;EAC9BE,QAAQA,CAAA,EAAG;IAAA,OAAAC,iBAAA;MACb,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACC,KAAK,EAAE;QACtD,OAAO;UAAEC,KAAK,EAAE;QAAM,CAAC;MAC3B,CAAC,MACI;QACD,OAAO;UAAEA,KAAK,EAAE;QAAK,CAAC;MAC1B;IAAC;EACL;EACMD,KAAKA,CAACE,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAL,iBAAA;MACjB,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACC,KAAK,EAAE;QACtD,MAAMG,KAAI,CAACC,WAAW,CAAC,yCAAyC,CAAC;MACrE;MACA,MAAML,SAAS,CAACC,KAAK,CAAC;QAClBK,KAAK,EAAEH,OAAO,CAACG,KAAK;QACpBC,IAAI,EAAEJ,OAAO,CAACI,IAAI;QAClBC,GAAG,EAAEL,OAAO,CAACK;MACjB,CAAC,CAAC;MACF,OAAO,CAAC,CAAC;IAAC;EACd;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
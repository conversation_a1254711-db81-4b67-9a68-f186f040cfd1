{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/commons.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"app/services/auth.service\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"app/services/team.service\";\nimport * as i9 from \"app/services/loading.service\";\nimport * as i10 from \"app/services/user.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"angular-datatables\";\nimport * as i13 from \"@ngx-formly/core\";\nfunction AssignNewCoachComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function AssignNewCoachComponent_div_11_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ctx_r2.model));\n    });\n    i0.ɵɵelement(2, \"formly-form\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.form);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"form\", ctx_r0.form)(\"fields\", ctx_r0.fields)(\"model\", ctx_r0.model);\n  }\n}\nfunction AssignNewCoachComponent_div_12_table_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 12)(1, \"thead\")(2, \"tr\")(3, \"th\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 13);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 13);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"dtOptions\", ctx_r4.dtOptions1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 5, \"Name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 7, \"Email\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 9, \"Phone\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 11, \"Action\"));\n  }\n}\nfunction AssignNewCoachComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7)(2, \"div\", 8)(3, \"h5\")(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 8)(8, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function AssignNewCoachComponent_div_12_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onClickAddNewCoach());\n    });\n    i0.ɵɵelement(9, \"i\", 10);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, AssignNewCoachComponent_div_12_table_12_Template, 15, 13, \"table\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 3, \"Suggestions\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 5, \"Add new coach\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.dataLoaded);\n  }\n}\nexport class AssignNewCoachComponent {\n  constructor(modalService, _translateService, _commonsService, _formBuilder, _http, renderer, _authService, _toastrService, _teamService, loadingService, _userService) {\n    this.modalService = modalService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._formBuilder = _formBuilder;\n    this._http = _http;\n    this.renderer = renderer;\n    this._authService = _authService;\n    this._toastrService = _toastrService;\n    this._teamService = _teamService;\n    this.loadingService = loadingService;\n    this._userService = _userService;\n    this.onCheckEmail = new EventEmitter();\n    this.isCheckEmail = false;\n    this.dtOptions1 = {};\n    this.dtTrigger = {};\n    this.dtElement = DataTableDirective;\n    this.submitted = false;\n    this.dataLoaded = false;\n    this.loading = false;\n    // variable to show/hide form add new coach\n    this.showForm = false;\n    this.form = new FormGroup({});\n    this.teamCoaches = [];\n    this.model = {};\n    this.fields = [{\n      type: 'tabs',\n      props: {\n        onNext: (index, model) => {\n          console.log('next', index, model);\n          this.getUserByEmail(model.email);\n        },\n        onChangeTab: (index, model) => {\n          console.log('change', index, model);\n          if (index === 1) {\n            this.getUserByEmail(model.email);\n          }\n        }\n      },\n      fieldGroup: [{\n        props: {\n          label: 'Validate Email'\n        },\n        fieldGroup: [{\n          type: 'input',\n          key: 'email',\n          props: {\n            label: 'Email',\n            type: 'email',\n            placeholder: 'Enter email',\n            translate: true,\n            // change: (field, $event) => {\n            //   console.log('change');\n            //   this.getUserByEmail($event.target.value);\n            // },\n            required: true,\n            pattern: /^[a-zA-Z0-9_\\.\\+\\-]+@[a-zA-Z0-9\\-]+\\.[a-zA-Z0-9\\-\\.]+$/\n          }\n        }]\n      }, {\n        props: {\n          label: 'Coach information'\n        },\n        fieldGroup: [{\n          type: 'input',\n          key: 'phone',\n          props: {\n            label: 'Phone',\n            placeholder: 'Enter phone',\n            translate: true,\n            disabled: false,\n            type: 'number'\n          },\n          defaultValue: ''\n        }, {\n          type: 'input',\n          key: 'first_name',\n          props: {\n            label: 'Surname',\n            placeholder: 'Enter surname',\n            translate: true,\n            disabled: false,\n            pattern: /[\\S]/,\n            required: true\n          }\n        }, {\n          type: 'input',\n          key: 'last_name',\n          props: {\n            label: 'Other Name',\n            placeholder: 'Enter other name',\n            translate: true,\n            disabled: false,\n            pattern: /[\\S]/,\n            required: true\n          }\n        }]\n      }]\n    }];\n    this._unsubscribeAll = new Subject();\n  }\n  ngOnInit() {\n    this.getAllCoaches();\n  }\n  getAllCoaches() {\n    let params = this.params;\n    console.log('params', params);\n    let current_season_coach_url = `${environment.apiUrl}/clubs/${params.club_id}/coaches`;\n    let team_coach_url = `${environment.apiUrl}/teams/coaches`;\n    let team_coaches_byId_url = `${environment.apiUrl}/teams/${this.params.team_id}/coaches`;\n    this._http.post(`${team_coaches_byId_url}`, {\n      team_id: params.team_id\n    }).subscribe(teamCoaches => {\n      this.dtOptions1 = this.buildDtOptions1(team_coach_url, params, teamCoaches);\n      this.dataLoaded = true;\n    });\n  }\n  get f() {\n    return this.form.controls;\n  }\n  buildDtOptions1(url, params, teamCoaches) {\n    return {\n      dom: this._commonsService.dataTableDefaults.dom,\n      ajax: (dataTablesParameters, callback) => {\n        if (params) {\n          dataTablesParameters['team_id'] = parseInt(params.team_id);\n        }\n        this._http.post(`${url}`, dataTablesParameters).subscribe(resp => {\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      // fake data\n      responsive: true,\n      scrollX: false,\n      language: this._commonsService.dataTableDefaults.lang,\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\n      displayLength: 10,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: 0\n      }, {\n        responsivePriority: 1,\n        targets: -1\n      }, {\n        responsivePriority: 2,\n        targets: 1\n      }],\n      columns: [{\n        data: null,\n        className: 'font-weight-bolder',\n        render: (data, type, row) => {\n          const name_settings = JSON.parse(localStorage.getItem('name_settings'));\n          if (row.first_name && row.last_name) {\n            if (name_settings && name_settings.is_on == 1) {\n              return row.first_name + ' ' + row.last_name;\n            } else {\n              return row.last_name + ' ' + row.first_name;\n            }\n          } else {\n            return '';\n          }\n        }\n      }, {\n        data: 'email'\n      }, {\n        data: 'phone'\n      }, {\n        data: null,\n        render: (data, type, row) => {\n          const isAssigned = teamCoaches.data.some(coach => {\n            return coach.user_id === row.user_id;\n          });\n          const disabledAttr = isAssigned ? 'disabled' : '';\n          const buttonText = isAssigned ? 'Assigned' : 'Assign';\n          return `<button class=\"btn btn-primary btn-sm\" ${disabledAttr}\n                    data-row-value='${JSON.stringify(row)}'\n                    action=\"assign\">${this._translateService.instant(buttonText)}</button>`;\n        }\n      }],\n      buttons: []\n    };\n  }\n  ngAfterViewInit() {\n    this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('data-row-value')) {\n        let row = event.target.getAttribute('data-row-value');\n        row = JSON.parse(row);\n        this.editor(event.target.getAttribute('action'), row);\n      }\n    });\n  }\n  editor(action, row) {\n    switch (action) {\n      case 'assign':\n        Swal.fire({\n          title: this._translateService.instant('Are you sure?'),\n          html: `\n          <div class=\"text-center\">\n            <img src=\"assets/images/alerts/Frame1.svg\" width=\"200px\" height=\"149px\">\n            <p class=\"text-center\" style=\"margin-top: 1rem\">` + this._translateService.instant('You want to assign this coach to this team?') + `\n            </p>\n          </div>`,\n          reverseButtons: true,\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Yes'),\n          cancelButtonText: '<span class=\"text-primary\">' + this._translateService.instant('Cancel') + '</span>',\n          buttonsStyling: false,\n          customClass: {\n            confirmButton: 'btn btn-primary mr-1',\n            cancelButton: 'btn btn-outline-primary mr-1'\n          }\n        }).then(result => {\n          if (result.isConfirmed) {\n            let team_id = this.params.team_id;\n            let user_id = row.user_id;\n            let assigned = '1';\n            let params = new FormData();\n            params.append('action', 'create');\n            params.append('data[0][team_id]', team_id);\n            params.append('data[0][user_id]', user_id);\n            params.append('data[0][assigned]', assigned);\n            this._teamService.editorTableTeamCoaches(params).subscribe(resp => {\n              if (resp) {\n                this._toastrService.success(this._translateService.instant('Coaches assigned successfully'));\n                // disable button\n                let button = document.querySelector(`button[action=\"assign\"][data-row-value='${JSON.stringify(row)}']`);\n                button.setAttribute('disabled', 'disabled');\n                button.classList.add('disabled');\n                // change text\n                button.innerHTML = this._translateService.instant('Assigned');\n              }\n            });\n          }\n        });\n        break;\n      default:\n        break;\n    }\n  }\n  onClickAddNewCoach() {\n    this.form.reset();\n    this.model = {};\n    this.submitted = false;\n    this.showForm = true;\n  }\n  onClickCancel() {\n    this.showForm = false;\n  }\n  getUserByEmail(email) {\n    if (this.form.controls['email'].invalid) return;\n    if (!email.trim()) return;\n    this.isCheckEmail = true;\n    this.loadingService.show();\n    this._userService.getByEmail(email).subscribe(resp => {\n      if (resp) {\n        if (resp.data) {\n          let data = resp.data;\n          this.disableFieldByData(data);\n          console.log('resp', resp);\n        } else {\n          this.disableFieldByData();\n        }\n      }\n      this.isCheckEmail = false;\n      this.onCheckEmail.emit(resp);\n    }, error => {\n      this.disableFieldByData();\n      console.log('error', error);\n      this.isCheckEmail = false;\n      this.onCheckEmail.emit(error);\n    });\n  }\n  disableFieldByData(data = null) {\n    if (data == null) {\n      for (let key in this.form.controls) {\n        if (key != 'email') {\n          this.form.controls[key].enable();\n          this.form.controls[key].setValue('');\n          // reset dirty and touched\n          this.form.controls[key].markAsUntouched();\n          this.form.controls[key].markAsPristine();\n        }\n      }\n    } else {\n      this.form.patchValue(data);\n      // disable field has value\n      for (let key in data) {\n        if (this.form.controls.hasOwnProperty(key) && key != 'email' && data[key] != '' && data[key] != null && data[key] != undefined) {\n          this.form.controls[key]?.disable();\n        } else {\n          this.form.controls[key]?.enable();\n        }\n      }\n    }\n  }\n  onSubmit(model) {\n    if (this.isCheckEmail) {\n      let uncsub = this.onCheckEmail.subscribe(resp => {\n        setTimeout(() => {\n          this.onSubmit(model);\n        }, 1000);\n        uncsub.unsubscribe();\n      });\n      return;\n    }\n    // stop here if form is invalid\n    if (this.form.invalid) {\n      return;\n    } else {\n      // show loading spinner\n      this.loading = true;\n      // create new coach and assign to team\n      this._teamService.assignNewCoachToTeam(this.params.team_id, model).pipe(takeUntil(this._unsubscribeAll)).subscribe(res => {\n        this.loading = false;\n        // show success message\n        Swal.fire({\n          title: 'Success',\n          text: res.message,\n          // icon: \"success\",\n          imageUrl: 'assets/images/alerts/signup.png',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            container: 'signup-successfull',\n            confirmButton: 'btn btn-primary'\n          }\n        }).then(result => {\n          if (result.isConfirmed) {\n            this.onClose();\n            // hide form\n            this.showForm = false;\n          }\n        });\n      }, error => {\n        // hide loading spinner\n        this.loading = false;\n        // add error to form control\n        let errors = error.errors;\n        for (let key in errors) {\n          if (errors.hasOwnProperty(key)) {\n            this.form.controls[key].setErrors({\n              serverError: errors[key][0]\n            });\n          }\n        }\n        // check length of array\n        if (errors.length > 0) {\n          // show error message\n          this._toastrService.error(error.message, 'Error', {\n            closeButton: true,\n            tapToDismiss: false\n          });\n        }\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: 'Ok'\n        });\n      });\n    }\n  }\n  onClose() {\n    this.modalService.dismissAll();\n  }\n  static #_ = this.ɵfac = function AssignNewCoachComponent_Factory(t) {\n    return new (t || AssignNewCoachComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.CommonsService), i0.ɵɵdirectiveInject(i4.UntypedFormBuilder), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i6.AuthService), i0.ɵɵdirectiveInject(i7.ToastrService), i0.ɵɵdirectiveInject(i8.TeamService), i0.ɵɵdirectiveInject(i9.LoadingService), i0.ɵɵdirectiveInject(i10.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AssignNewCoachComponent,\n    selectors: [[\"app-assign-new-coach\"]],\n    viewQuery: function AssignNewCoachComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 13,\n    vars: 5,\n    consts: [[1, \"modal-header\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [3, \"form\", \"fields\", \"model\"], [1, \"row\"], [1, \"col-12\", \"col-sm-6\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"float-right\", \"mb-1\", 3, \"click\"], [1, \"fa\", \"fa-plus\"], [\"datatable\", \"\", \"class\", \"table border row-border hover\", 3, \"dtOptions\", 4, \"ngIf\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\"], [1, \"text-capitalize\"]],\n    template: function AssignNewCoachComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"div\")(3, \"h4\")(4, \"span\");\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"button\", 1);\n        i0.ɵɵlistener(\"click\", function AssignNewCoachComponent_Template_button_click_7_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(8, \"span\", 2);\n        i0.ɵɵtext(9, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 3);\n        i0.ɵɵtemplate(11, AssignNewCoachComponent_div_11_Template, 3, 4, \"div\", 4);\n        i0.ɵɵtemplate(12, AssignNewCoachComponent_div_12_Template, 13, 7, \"div\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 3, \"Add new coach\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.showForm);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showForm);\n      }\n    },\n    dependencies: [i11.NgIf, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, i12.DataTableDirective, i13.FormlyForm, i2.TranslatePipe],\n    styles: [\".swal2-cancel[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  border-color: #f44336 !important;\\n  color: #fff;\\n}\\n\\n.colname[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvdGVhbXMvdGVhbS1hc3NpZ25tZW50L2Fzc2lnbi1jb2FjaGVzL2Fzc2lnbi1uZXctY29hY2gvYXNzaWduLW5ldy1jb2FjaC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNJLHlCQUFBO0VBQ0EsZ0NBQUE7RUFDQSxXQUFBO0FBQUo7O0FBR0E7RUFDSSxVQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLnN3YWwyLWNhbmNlbHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNDQzMzY7XHJcbiAgICBib3JkZXItY29sb3I6ICNmNDQzMzYgIWltcG9ydGFudDtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG59XHJcblxyXG4uY29sbmFtZXtcclxuICAgIHdpZHRoOiAyMCU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAEEA,YAAY,QAIP,eAAe;AACtB,SAEEC,SAAS,QAIJ,gBAAgB;AAIvB,SAASC,kBAAkB,QAAQ,oBAAoB;AAMvD,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;;ICdtBC,EAAA,CAAAC,cAAA,UAAsB;IAEOD,EAAA,CAAAE,UAAA,sBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,QAAA,CAAAH,MAAA,CAAAI,KAAA,CAAe;IAAA,EAAC;IACjDV,EAAA,CAAAW,SAAA,qBAA2E;IAC/EX,EAAA,CAAAY,YAAA,EAAO;;;;IAFDZ,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,UAAA,cAAAC,MAAA,CAAAC,IAAA,CAAkB;IACPhB,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa,WAAAD,MAAA,CAAAE,MAAA,WAAAF,MAAA,CAAAL,KAAA;;;;;IAkB9BV,EAAA,CAAAC,cAAA,gBAAmG;IAG3DD,EAAA,CAAAkB,MAAA,GAAsB;;IAAAlB,EAAA,CAAAY,YAAA,EAAK;IACvDZ,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAkB,MAAA,GAAuB;;IAAAlB,EAAA,CAAAY,YAAA,EAAK;IACxDZ,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAkB,MAAA,IAAuB;;IAAAlB,EAAA,CAAAY,YAAA,EAAK;IACxDZ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAkB,MAAA,IAAwB;;IAAAlB,EAAA,CAAAY,YAAA,EAAK;;;;IANpDZ,EAAA,CAAAc,UAAA,cAAAK,MAAA,CAAAC,UAAA,CAAwB;IAGDpB,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,eAAsB;IACtBtB,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,gBAAuB;IACvBtB,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,iBAAuB;IACvBtB,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,mBAAwB;;;;;;IApBpEtB,EAAA,CAAAC,cAAA,UAAuB;IAIDD,EAAA,CAAAkB,MAAA,GAA8B;;IAAAlB,EAAA,CAAAY,YAAA,EAAO;IAGnDZ,EAAA,CAAAC,cAAA,aAA6B;IACuCD,EAAA,CAAAE,UAAA,mBAAAqB,gEAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAC,MAAA,GAAAzB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiB,MAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAC1F1B,EAAA,CAAAW,SAAA,YAA0B;IAC1BX,EAAA,CAAAkB,MAAA,IACJ;;IAAAlB,EAAA,CAAAY,YAAA,EAAS;IAGjBZ,EAAA,CAAA2B,UAAA,KAAAC,gDAAA,sBASQ;IACZ5B,EAAA,CAAAY,YAAA,EAAM;;;;IApBgBZ,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,sBAA8B;IAMpCtB,EAAA,CAAAa,SAAA,GACJ;IADIb,EAAA,CAAA6B,kBAAA,MAAA7B,EAAA,CAAAsB,WAAA,8BACJ;IAGyEtB,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAc,UAAA,SAAAgB,MAAA,CAAAC,UAAA,CAAgB;;;ADA7G,OAAM,MAAOC,uBAAuB;EAqGlCC,YACSC,YAAsB,EACtBC,iBAAmC,EACnCC,eAA+B,EAC9BC,YAAgC,EACjCC,KAAiB,EACjBC,QAAmB,EAClBC,YAAyB,EACzBC,cAA6B,EAC9BC,YAAyB,EACzBC,cAA8B,EAC9BC,YAAyB;IAVzB,KAAAV,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACd,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACP,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IA/GrB,KAAAC,YAAY,GAAsB,IAAIpD,YAAY,EAAO;IACzD,KAAAqD,YAAY,GAAG,KAAK;IACpB,KAAA1B,UAAU,GAAQ,EAAE;IACpB,KAAA2B,SAAS,GAAQ,EAAE;IAGnB,KAAAC,SAAS,GAAQrD,kBAAkB;IAC5B,KAAAsD,SAAS,GAAG,KAAK;IACxB,KAAAlB,UAAU,GAAY,KAAK;IACpB,KAAAmB,OAAO,GAAG,KAAK;IACtB;IACO,KAAAC,QAAQ,GAAG,KAAK;IAEvB,KAAAnC,IAAI,GAAG,IAAItB,SAAS,CAAC,EAAE,CAAC;IACxB,KAAA0D,WAAW,GAAU,EAAE;IACvB,KAAA1C,KAAK,GAAQ,EAAE;IACf,KAAAO,MAAM,GAAwB,CAC5B;MACEoC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;QACLC,MAAM,EAAEA,CAACC,KAAK,EAAE9C,KAAK,KAAI;UACvB+C,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEF,KAAK,EAAE9C,KAAK,CAAC;UACjC,IAAI,CAACiD,cAAc,CAACjD,KAAK,CAACkD,KAAK,CAAC;QAClC,CAAC;QACDC,WAAW,EAAEA,CAACL,KAAK,EAAE9C,KAAK,KAAI;UAC5B+C,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,KAAK,EAAE9C,KAAK,CAAC;UAEnC,IAAI8C,KAAK,KAAK,CAAC,EAAE;YACf,IAAI,CAACG,cAAc,CAACjD,KAAK,CAACkD,KAAK,CAAC;;QAEpC;OACD;MACDE,UAAU,EAAE,CACV;QACER,KAAK,EAAE;UAAES,KAAK,EAAE;QAAgB,CAAE;QAClCD,UAAU,EAAE,CACV;UACET,IAAI,EAAE,OAAO;UACbW,GAAG,EAAE,OAAO;UACZV,KAAK,EAAE;YACLS,KAAK,EAAE,OAAO;YACdV,IAAI,EAAE,OAAO;YACbY,WAAW,EAAE,aAAa;YAC1BC,SAAS,EAAE,IAAI;YACf;YACA;YACA;YACA;YACAC,QAAQ,EAAE,IAAI;YACdC,OAAO,EACL;;SAEL;OAEJ,EACD;QACEd,KAAK,EAAE;UAAES,KAAK,EAAE;QAAmB,CAAE;QACrCD,UAAU,EAAE,CACV;UACET,IAAI,EAAE,OAAO;UACbW,GAAG,EAAE,OAAO;UACZV,KAAK,EAAE;YACLS,KAAK,EAAE,OAAO;YACdE,WAAW,EAAE,aAAa;YAC1BC,SAAS,EAAE,IAAI;YACfG,QAAQ,EAAE,KAAK;YACfhB,IAAI,EAAE;WACP;UACDiB,YAAY,EAAE;SACf,EACD;UACEjB,IAAI,EAAE,OAAO;UACbW,GAAG,EAAE,YAAY;UACjBV,KAAK,EAAE;YACLS,KAAK,EAAE,SAAS;YAChBE,WAAW,EAAE,eAAe;YAC5BC,SAAS,EAAE,IAAI;YACfG,QAAQ,EAAE,KAAK;YACfD,OAAO,EAAE,MAAM;YACfD,QAAQ,EAAE;;SAEb,EACD;UACEd,IAAI,EAAE,OAAO;UACbW,GAAG,EAAE,WAAW;UAChBV,KAAK,EAAE;YACLS,KAAK,EAAE,YAAY;YACnBE,WAAW,EAAE,kBAAkB;YAC/BC,SAAS,EAAE,IAAI;YACfG,QAAQ,EAAE,KAAK;YACfD,OAAO,EAAE,MAAM;YACfD,QAAQ,EAAE;;SAEb;OAEJ;KAEJ,CACF;IAeC,IAAI,CAACI,eAAe,GAAG,IAAI1E,OAAO,EAAE;EACtC;EAEA2E,QAAQA,CAAA;IACP,IAAI,CAACC,aAAa,EAAE;EACrB;EAEAA,aAAaA,CAAA;IACX,IAAIC,MAAM,GAAG,IAAI,CAACA,MAAM;IACxBjB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEgB,MAAM,CAAC;IAE7B,IAAIC,wBAAwB,GAAG,GAAG/E,WAAW,CAACgF,MAAM,UAAUF,MAAM,CAACG,OAAO,UAAU;IACtF,IAAIC,cAAc,GAAG,GAAGlF,WAAW,CAACgF,MAAM,gBAAgB;IAC1D,IAAIG,qBAAqB,GAAG,GAAGnF,WAAW,CAACgF,MAAM,UAAU,IAAI,CAACF,MAAM,CAACM,OAAO,UAAU;IAExF,IAAI,CAAC1C,KAAK,CACP2C,IAAI,CAAM,GAAGF,qBAAqB,EAAE,EAAE;MAAEC,OAAO,EAAEN,MAAM,CAACM;IAAO,CAAE,CAAC,CAClEE,SAAS,CAAE9B,WAAgB,IAAI;MAC9B,IAAI,CAAChC,UAAU,GAAG,IAAI,CAAC+D,eAAe,CACpCL,cAAc,EACdJ,MAAM,EACNtB,WAAW,CACZ;MACD,IAAI,CAACrB,UAAU,GAAG,IAAI;IACxB,CAAC,CAAC;EACN;EACA,IAAIqD,CAACA,CAAA;IACH,OAAO,IAAI,CAACpE,IAAI,CAACqE,QAAQ;EAC3B;EAEAF,eAAeA,CAACG,GAAG,EAAEZ,MAAW,EAAEtB,WAAgB;IAChD,OAAO;MACLmC,GAAG,EAAE,IAAI,CAACnD,eAAe,CAACoD,iBAAiB,CAACD,GAAG;MAC/CE,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAIjB,MAAM,EAAE;UACVgB,oBAAoB,CAAC,SAAS,CAAC,GAAGE,QAAQ,CAAClB,MAAM,CAACM,OAAO,CAAC;;QAE5D,IAAI,CAAC1C,KAAK,CACP2C,IAAI,CAAM,GAAGK,GAAG,EAAE,EAAEI,oBAAoB,CAAC,CACzCR,SAAS,CAAEW,IAAS,IAAI;UACvBF,QAAQ,CAAC;YACP;YACAG,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACX;MACAC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI,CAACjE,eAAe,CAACoD,iBAAiB,CAACc,IAAI;MACrDC,UAAU,EAAE,IAAI,CAACnE,eAAe,CAACoD,iBAAiB,CAACe,UAAU;MAC7DC,aAAa,EAAE,EAAE;MACjBC,UAAU,EAAE,CACV;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,EACrC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,EACtC;QAAED,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAC,CAAE,CACtC;MACDC,OAAO,EAAE,CACP;QACEZ,IAAI,EAAE,IAAI;QACVa,SAAS,EAAE,oBAAoB;QAC/BC,MAAM,EAAEA,CAACd,IAAI,EAAE3C,IAAI,EAAE0D,GAAG,KAAI;UAC1B,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;UACvE,IAAIL,GAAG,CAACM,UAAU,IAAIN,GAAG,CAACO,SAAS,EAAE;YACnC,IAAIN,aAAa,IAAIA,aAAa,CAACO,KAAK,IAAI,CAAC,EAAE;cAC7C,OAAOR,GAAG,CAACM,UAAU,GAAG,GAAG,GAAGN,GAAG,CAACO,SAAS;aAC5C,MAAM;cACL,OAAOP,GAAG,CAACO,SAAS,GAAG,GAAG,GAAGP,GAAG,CAACM,UAAU;;WAE9C,MAAM;YACL,OAAO,EAAE;;QAEb;OACD,EACD;QACErB,IAAI,EAAE;OACP,EACD;QACEA,IAAI,EAAE;OACP,EACD;QACEA,IAAI,EAAE,IAAI;QACVc,MAAM,EAAEA,CAACd,IAAI,EAAE3C,IAAI,EAAE0D,GAAG,KAAI;UAC1B,MAAMS,UAAU,GAAGpE,WAAW,CAAC4C,IAAI,CAACyB,IAAI,CAAEC,KAAK,IAAI;YACjD,OAAOA,KAAK,CAACC,OAAO,KAAKZ,GAAG,CAACY,OAAO;UACtC,CAAC,CAAC;UACF,MAAMC,YAAY,GAAGJ,UAAU,GAAG,UAAU,GAAG,EAAE;UACjD,MAAMK,UAAU,GAAGL,UAAU,GAAG,UAAU,GAAG,QAAQ;UACrD,OAAO,0CAA0CI,YAAY;sCACnCX,IAAI,CAACa,SAAS,CAACf,GAAG,CAAC;sCACnB,IAAI,CAAC5E,iBAAiB,CAAC4F,OAAO,CAC9CF,UAAU,CACX,WAAW;QACtB;OACD,CACF;MACDG,OAAO,EAAE;KACV;EACH;EAEAC,eAAeA,CAAA;IACb,IAAI,CAAC1F,QAAQ,CAAC2F,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGC,KAAK,IAAI;MAClD,IAAIA,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,gBAAgB,CAAC,EAAE;QAC/C,IAAItB,GAAG,GAAGoB,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,gBAAgB,CAAC;QACrDvB,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;QAErB,IAAI,CAACwB,MAAM,CAACJ,KAAK,CAACC,MAAM,CAACE,YAAY,CAAC,QAAQ,CAAC,EAAEvB,GAAG,CAAC;;IAEzD,CAAC,CAAC;EACJ;EAEAwB,MAAMA,CAACC,MAAM,EAAEzB,GAAG;IAChB,QAAQyB,MAAM;MACZ,KAAK,QAAQ;QACXzI,IAAI,CAAC0I,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACvG,iBAAiB,CAAC4F,OAAO,CAAC,eAAe,CAAC;UACtDY,IAAI,EACF;;;6DAGiD,GACjD,IAAI,CAACxG,iBAAiB,CAAC4F,OAAO,CAC5B,6CAA6C,CAC9C,GACD;;iBAEK;UACPa,cAAc,EAAE,IAAI;UACpBC,gBAAgB,EAAE,IAAI;UACtBC,iBAAiB,EAAE,IAAI,CAAC3G,iBAAiB,CAAC4F,OAAO,CAAC,KAAK,CAAC;UACxDgB,gBAAgB,EACd,6BAA6B,GAC7B,IAAI,CAAC5G,iBAAiB,CAAC4F,OAAO,CAAC,QAAQ,CAAC,GACxC,SAAS;UACXiB,cAAc,EAAE,KAAK;UACrBC,WAAW,EAAE;YACXC,aAAa,EAAE,sBAAsB;YACrCC,YAAY,EAAE;;SAEjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;YACtB,IAAItE,OAAO,GAAG,IAAI,CAACN,MAAM,CAACM,OAAO;YACjC,IAAI2C,OAAO,GAAGZ,GAAG,CAACY,OAAO;YACzB,IAAI4B,QAAQ,GAAG,GAAG;YAClB,IAAI7E,MAAM,GAAa,IAAI8E,QAAQ,EAAE;YACrC9E,MAAM,CAAC+E,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;YACjC/E,MAAM,CAAC+E,MAAM,CAAC,kBAAkB,EAAEzE,OAAO,CAAC;YAC1CN,MAAM,CAAC+E,MAAM,CAAC,kBAAkB,EAAE9B,OAAO,CAAC;YAC1CjD,MAAM,CAAC+E,MAAM,CAAC,mBAAmB,EAAEF,QAAQ,CAAC;YAE5C,IAAI,CAAC7G,YAAY,CACdgH,sBAAsB,CAAChF,MAAM,CAAC,CAC9BQ,SAAS,CAAEW,IAAS,IAAI;cACvB,IAAIA,IAAI,EAAE;gBACR,IAAI,CAACpD,cAAc,CAACkH,OAAO,CACzB,IAAI,CAACxH,iBAAiB,CAAC4F,OAAO,CAC5B,+BAA+B,CAChC,CACF;gBAED;gBACA,IAAI6B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CACjC,2CAA2C7C,IAAI,CAACa,SAAS,CACvDf,GAAG,CACJ,IAAI,CACN;gBACD6C,MAAM,CAACG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;gBAC3CH,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;gBAChC;gBACAL,MAAM,CAACM,SAAS,GAAG,IAAI,CAAC/H,iBAAiB,CAAC4F,OAAO,CAAC,UAAU,CAAC;;YAEjE,CAAC,CAAC;;QAER,CAAC,CAAC;QACF;MACF;QACE;;EAEN;EAEArG,kBAAkBA,CAAA;IAChB,IAAI,CAACV,IAAI,CAACmJ,KAAK,EAAE;IACjB,IAAI,CAACzJ,KAAK,GAAG,EAAE;IACf,IAAI,CAACuC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACE,QAAQ,GAAG,IAAI;EACtB;EAEAiH,aAAaA,CAAA;IACX,IAAI,CAACjH,QAAQ,GAAG,KAAK;EACvB;EAEAQ,cAAcA,CAACC,KAAK;IAClB,IAAI,IAAI,CAAC5C,IAAI,CAACqE,QAAQ,CAAC,OAAO,CAAC,CAACgF,OAAO,EAAE;IACzC,IAAI,CAACzG,KAAK,CAAC0G,IAAI,EAAE,EAAE;IACnB,IAAI,CAACxH,YAAY,GAAG,IAAI;IACxB,IAAI,CAACH,cAAc,CAAC4H,IAAI,EAAE;IAC1B,IAAI,CAAC3H,YAAY,CAAC4H,UAAU,CAAC5G,KAAK,CAAC,CAACsB,SAAS,CAC1CW,IAAS,IAAI;MACZ,IAAIA,IAAI,EAAE;QACR,IAAIA,IAAI,CAACG,IAAI,EAAE;UACb,IAAIA,IAAI,GAAGH,IAAI,CAACG,IAAI;UACpB,IAAI,CAACyE,kBAAkB,CAACzE,IAAI,CAAC;UAC7BvC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEmC,IAAI,CAAC;SAC1B,MAAM;UACL,IAAI,CAAC4E,kBAAkB,EAAE;;;MAG7B,IAAI,CAAC3H,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,YAAY,CAAC6H,IAAI,CAAC7E,IAAI,CAAC;IAC9B,CAAC,EACA8E,KAAK,IAAI;MACR,IAAI,CAACF,kBAAkB,EAAE;MACzBhH,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEiH,KAAK,CAAC;MAC3B,IAAI,CAAC7H,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,YAAY,CAAC6H,IAAI,CAACC,KAAK,CAAC;IAC/B,CAAC,CACF;EACH;EAEAF,kBAAkBA,CAACzE,IAAA,GAAY,IAAI;IACjC,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,KAAK,IAAIhC,GAAG,IAAI,IAAI,CAAChD,IAAI,CAACqE,QAAQ,EAAE;QAClC,IAAIrB,GAAG,IAAI,OAAO,EAAE;UAClB,IAAI,CAAChD,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,CAAC4G,MAAM,EAAE;UAChC,IAAI,CAAC5J,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,CAAC6G,QAAQ,CAAC,EAAE,CAAC;UACpC;UACA,IAAI,CAAC7J,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,CAAC8G,eAAe,EAAE;UACzC,IAAI,CAAC9J,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,CAAC+G,cAAc,EAAE;;;KAG7C,MAAM;MACL,IAAI,CAAC/J,IAAI,CAACgK,UAAU,CAAChF,IAAI,CAAC;MAC1B;MACA,KAAK,IAAIhC,GAAG,IAAIgC,IAAI,EAAE;QACpB,IACE,IAAI,CAAChF,IAAI,CAACqE,QAAQ,CAAC4F,cAAc,CAACjH,GAAG,CAAC,IACtCA,GAAG,IAAI,OAAO,IACdgC,IAAI,CAAChC,GAAG,CAAC,IAAI,EAAE,IACfgC,IAAI,CAAChC,GAAG,CAAC,IAAI,IAAI,IACjBgC,IAAI,CAAChC,GAAG,CAAC,IAAIkH,SAAS,EACtB;UACA,IAAI,CAAClK,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,EAAEmH,OAAO,EAAE;SACnC,MAAM;UACL,IAAI,CAACnK,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,EAAE4G,MAAM,EAAE;;;;EAIzC;EAEAnK,QAAQA,CAACC,KAAK;IAEZ,IAAI,IAAI,CAACoC,YAAY,EAAE;MACrB,IAAIsI,MAAM,GAAG,IAAI,CAACvI,YAAY,CAACqC,SAAS,CAAEW,IAAI,IAAI;QAChDwF,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5K,QAAQ,CAACC,KAAK,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC;QACR0K,MAAM,CAACE,WAAW,EAAE;MACtB,CAAC,CAAC;MACF;;IAEF;IACA,IAAI,IAAI,CAACtK,IAAI,CAACqJ,OAAO,EAAE;MACrB;KACD,MAAM;MACL;MACA,IAAI,CAACnH,OAAO,GAAG,IAAI;MAEnB;MACA,IAAI,CAACR,YAAY,CACd6I,oBAAoB,CAAC,IAAI,CAAC7G,MAAM,CAACM,OAAO,EAAEtE,KAAK,CAAC,CAChD8K,IAAI,CAAC1L,SAAS,CAAC,IAAI,CAACyE,eAAe,CAAC,CAAC,CACrCW,SAAS,CACPuG,GAAQ,IAAI;QACX,IAAI,CAACvI,OAAO,GAAG,KAAK;QACpB;QACAnD,IAAI,CAAC0I,IAAI,CAAC;UACRC,KAAK,EAAE,SAAS;UAChBgD,IAAI,EAAED,GAAG,CAACE,OAAO;UACjB;UACAC,QAAQ,EAAE,iCAAiC;UAC3C9C,iBAAiB,EAAE,IAAI,CAAC3G,iBAAiB,CAAC4F,OAAO,CAAC,IAAI,CAAC;UACvDkB,WAAW,EAAE;YACX4C,SAAS,EAAE,oBAAoB;YAC/B3C,aAAa,EAAE;;SAElB,CAAC,CAACE,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;YACtB,IAAI,CAACwC,OAAO,EAAE;YACd;YACA,IAAI,CAAC3I,QAAQ,GAAG,KAAK;;QAEzB,CAAC,CAAC;MACJ,CAAC,EACAwH,KAAK,IAAI;QACR;QACA,IAAI,CAACzH,OAAO,GAAG,KAAK;QACpB;QACA,IAAI6I,MAAM,GAAGpB,KAAK,CAACoB,MAAM;QACzB,KAAK,IAAI/H,GAAG,IAAI+H,MAAM,EAAE;UACtB,IAAIA,MAAM,CAACd,cAAc,CAACjH,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAChD,IAAI,CAACqE,QAAQ,CAACrB,GAAG,CAAC,CAACgI,SAAS,CAAC;cAChCC,WAAW,EAAEF,MAAM,CAAC/H,GAAG,CAAC,CAAC,CAAC;aAC3B,CAAC;;;QAGN;QACA,IAAI+H,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;UACrB;UACA,IAAI,CAACzJ,cAAc,CAACkI,KAAK,CAACA,KAAK,CAACgB,OAAO,EAAE,OAAO,EAAE;YAChDQ,WAAW,EAAE,IAAI;YACjBC,YAAY,EAAE;WACf,CAAC;;QAEJrM,IAAI,CAAC0I,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdgD,IAAI,EAAEf,KAAK,CAACgB,OAAO;UACnBU,IAAI,EAAE,OAAO;UACbvD,iBAAiB,EAAE;SACpB,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAgD,OAAOA,CAAA;IACL,IAAI,CAAC5J,YAAY,CAACoK,UAAU,EAAE;EAChC;EAAC,QAAAC,CAAA;qBA7bUvK,uBAAuB,EAAAhC,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAA1M,EAAA,CAAAwM,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA5M,EAAA,CAAAwM,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA9M,EAAA,CAAAwM,iBAAA,CAAAO,EAAA,CAAAC,kBAAA,GAAAhN,EAAA,CAAAwM,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAAlN,EAAA,CAAAwM,iBAAA,CAAAxM,EAAA,CAAAmN,SAAA,GAAAnN,EAAA,CAAAwM,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAArN,EAAA,CAAAwM,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAAvN,EAAA,CAAAwM,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAAzN,EAAA,CAAAwM,iBAAA,CAAAkB,EAAA,CAAAC,cAAA,GAAA3N,EAAA,CAAAwM,iBAAA,CAAAoB,GAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA;UAAvB9L,uBAAuB;IAAA+L,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAMvBvO,kBAAkB;;;;;;;;;;;;QCzC/BK,EAAA,CAAAC,cAAA,UAAK;QAIiBD,EAAA,CAAAkB,MAAA,GAA8B;;QAAAlB,EAAA,CAAAY,YAAA,EAAO;QAInDZ,EAAA,CAAAC,cAAA,gBAAmG;QAApBD,EAAA,CAAAE,UAAA,mBAAAkO,yDAAA;UAAA,OAASD,GAAA,CAAArC,OAAA,EAAS;QAAA,EAAC;QAC9F9L,EAAA,CAAAC,cAAA,cAAyB;QAAAD,EAAA,CAAAkB,MAAA,aAAO;QAAAlB,EAAA,CAAAY,YAAA,EAAO;QAG/CZ,EAAA,CAAAC,cAAA,cAAkD;QAE9CD,EAAA,CAAA2B,UAAA,KAAA0M,uCAAA,iBAKM;QAENrO,EAAA,CAAA2B,UAAA,KAAA2M,uCAAA,kBAwBM;QACVtO,EAAA,CAAAY,YAAA,EAAM;;;QA1CYZ,EAAA,CAAAa,SAAA,GAA8B;QAA9Bb,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,wBAA8B;QAUtCtB,EAAA,CAAAa,SAAA,GAAc;QAAdb,EAAA,CAAAc,UAAA,SAAAqN,GAAA,CAAAhL,QAAA,CAAc;QAOdnD,EAAA,CAAAa,SAAA,GAAe;QAAfb,EAAA,CAAAc,UAAA,UAAAqN,GAAA,CAAAhL,QAAA,CAAe", "names": ["EventEmitter", "FormGroup", "DataTableDirective", "environment", "Subject", "takeUntil", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "AssignNewCoachComponent_div_11_Template_form_ngSubmit_1_listener", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "model", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "form", "fields", "ɵɵtext", "ctx_r4", "dtOptions1", "ɵɵtextInterpolate", "ɵɵpipeBind1", "AssignNewCoachComponent_div_12_Template_button_click_8_listener", "_r6", "ctx_r5", "onClickAddNewCoach", "ɵɵtemplate", "AssignNewCoachComponent_div_12_table_12_Template", "ɵɵtextInterpolate1", "ctx_r1", "dataLoaded", "AssignNewCoachComponent", "constructor", "modalService", "_translateService", "_commonsService", "_formBuilder", "_http", "renderer", "_authService", "_toastrService", "_teamService", "loadingService", "_userService", "onCheckEmail", "isCheckEmail", "dtTrigger", "dtElement", "submitted", "loading", "showForm", "teamCoaches", "type", "props", "onNext", "index", "console", "log", "getUserByEmail", "email", "onChangeTab", "fieldGroup", "label", "key", "placeholder", "translate", "required", "pattern", "disabled", "defaultValue", "_unsubscribeAll", "ngOnInit", "getAllCoaches", "params", "current_season_coach_url", "apiUrl", "club_id", "team_coach_url", "team_coaches_byId_url", "team_id", "post", "subscribe", "buildDtOptions1", "f", "controls", "url", "dom", "dataTableDefaults", "ajax", "dataTablesParameters", "callback", "parseInt", "resp", "recordsTotal", "recordsFiltered", "data", "select", "rowId", "responsive", "scrollX", "language", "lang", "lengthMenu", "displayLength", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "name_settings", "JSON", "parse", "localStorage", "getItem", "first_name", "last_name", "is_on", "isAssigned", "some", "coach", "user_id", "disabledAttr", "buttonText", "stringify", "instant", "buttons", "ngAfterViewInit", "listen", "event", "target", "hasAttribute", "getAttribute", "editor", "action", "fire", "title", "html", "reverseButtons", "showCancelButton", "confirmButtonText", "cancelButtonText", "buttonsStyling", "customClass", "confirmButton", "cancelButton", "then", "result", "isConfirmed", "assigned", "FormData", "append", "editorTableTeamCoaches", "success", "button", "document", "querySelector", "setAttribute", "classList", "add", "innerHTML", "reset", "onClickCancel", "invalid", "trim", "show", "getByEmail", "disableFieldByData", "emit", "error", "enable", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mark<PERSON><PERSON>ristine", "patchValue", "hasOwnProperty", "undefined", "disable", "uncsub", "setTimeout", "unsubscribe", "assignNewCoachToTeam", "pipe", "res", "text", "message", "imageUrl", "container", "onClose", "errors", "setErrors", "serverError", "length", "closeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "icon", "dismissAll", "_", "ɵɵdirectiveInject", "i1", "NgbModal", "i2", "TranslateService", "i3", "CommonsService", "i4", "UntypedFormBuilder", "i5", "HttpClient", "Renderer2", "i6", "AuthService", "i7", "ToastrService", "i8", "TeamService", "i9", "LoadingService", "i10", "UserService", "_2", "selectors", "viewQuery", "AssignNewCoachComponent_Query", "rf", "ctx", "AssignNewCoachComponent_Template_button_click_7_listener", "AssignNewCoachComponent_div_11_Template", "AssignNewCoachComponent_div_12_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-coaches\\assign-new-coach\\assign-new-coach.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\teams\\team-assignment\\assign-coaches\\assign-new-coach\\assign-new-coach.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  EventEmitter,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport {\r\n  FormControl,\r\n  FormGroup,\r\n  UntypedFormBuilder,\r\n  UntypedFormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { environment } from 'environments/environment';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { Subject } from 'rxjs';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-assign-new-coach',\r\n  templateUrl: './assign-new-coach.component.html',\r\n  styleUrls: ['./assign-new-coach.component.scss'],\r\n})\r\nexport class AssignNewCoachComponent implements OnInit {\r\n  onCheckEmail: EventEmitter<any> = new EventEmitter<any>();\r\n  isCheckEmail = false;\r\n  dtOptions1: any = {};\r\n  dtTrigger: any = {};\r\n  public params: any;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  public submitted = false;\r\n  dataLoaded: boolean = false;\r\n  public loading = false;\r\n  // variable to show/hide form add new coach\r\n  public showForm = false;\r\n  private _unsubscribeAll: Subject<any>;\r\n  form = new FormGroup({});\r\n  teamCoaches: any[] = [];\r\n  model: any = {};\r\n  fields: FormlyFieldConfig[] = [\r\n    {\r\n      type: 'tabs',\r\n      props: {\r\n        onNext: (index, model) => {\r\n          console.log('next', index, model);\r\n          this.getUserByEmail(model.email);\r\n        },\r\n        onChangeTab: (index, model) => {\r\n          console.log('change', index, model);\r\n\r\n          if (index === 1) {\r\n            this.getUserByEmail(model.email);\r\n          }\r\n        },\r\n      },\r\n      fieldGroup: [\r\n        {\r\n          props: { label: 'Validate Email' },\r\n          fieldGroup: [\r\n            {\r\n              type: 'input',\r\n              key: 'email',\r\n              props: {\r\n                label: 'Email',\r\n                type: 'email',\r\n                placeholder: 'Enter email',\r\n                translate: true,\r\n                // change: (field, $event) => {\r\n                //   console.log('change');\r\n                //   this.getUserByEmail($event.target.value);\r\n                // },\r\n                required: true,\r\n                pattern:\r\n                  /^[a-zA-Z0-9_\\.\\+\\-]+@[a-zA-Z0-9\\-]+\\.[a-zA-Z0-9\\-\\.]+$/,\r\n              },\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          props: { label: 'Coach information' },\r\n          fieldGroup: [\r\n            {\r\n              type: 'input',\r\n              key: 'phone',\r\n              props: {\r\n                label: 'Phone',\r\n                placeholder: 'Enter phone',\r\n                translate: true,\r\n                disabled: false,\r\n                type: 'number',\r\n              },\r\n              defaultValue: '',\r\n            },\r\n            {\r\n              type: 'input',\r\n              key: 'first_name',\r\n              props: {\r\n                label: 'Surname',\r\n                placeholder: 'Enter surname',\r\n                translate: true,\r\n                disabled: false,\r\n                pattern: /[\\S]/,\r\n                required: true,\r\n              },\r\n            },\r\n            {\r\n              type: 'input',\r\n              key: 'last_name',\r\n              props: {\r\n                label: 'Other Name',\r\n                placeholder: 'Enter other name',\r\n                translate: true,\r\n                disabled: false,\r\n                pattern: /[\\S]/,\r\n                required: true,\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  constructor(\r\n    public modalService: NgbModal,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    private _formBuilder: UntypedFormBuilder,\r\n    public _http: HttpClient,\r\n    public renderer: Renderer2,\r\n    private _authService: AuthService,\r\n    private _toastrService: ToastrService,\r\n    public _teamService: TeamService,\r\n    public loadingService: LoadingService,\r\n    public _userService: UserService,\r\n  ) {\r\n    this._unsubscribeAll = new Subject();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n   this.getAllCoaches()\r\n  }\r\n\r\n  getAllCoaches() {\r\n    let params = this.params;\r\n    console.log('params', params);\r\n\r\n    let current_season_coach_url = `${environment.apiUrl}/clubs/${params.club_id}/coaches`;\r\n    let team_coach_url = `${environment.apiUrl}/teams/coaches`;\r\n    let team_coaches_byId_url = `${environment.apiUrl}/teams/${this.params.team_id}/coaches`;\r\n\r\n    this._http\r\n      .post<any>(`${team_coaches_byId_url}`, { team_id: params.team_id })\r\n      .subscribe((teamCoaches: any) => {\r\n        this.dtOptions1 = this.buildDtOptions1(\r\n          team_coach_url,\r\n          params,\r\n          teamCoaches,\r\n        );\r\n        this.dataLoaded = true;\r\n      });\r\n  }\r\n  get f() {\r\n    return this.form.controls;\r\n  }\r\n\r\n  buildDtOptions1(url, params: any, teamCoaches: any) {\r\n    return {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        if (params) {\r\n          dataTablesParameters['team_id'] = parseInt(params.team_id);\r\n        }\r\n        this._http\r\n          .post<any>(`${url}`, dataTablesParameters)\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data,\r\n            });\r\n          });\r\n      },\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      // fake data\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,\r\n      displayLength: 10,\r\n      columnDefs: [\r\n        { responsivePriority: 1, targets: 0 },\r\n        { responsivePriority: 1, targets: -1 },\r\n        { responsivePriority: 2, targets: 1 },\r\n      ],\r\n      columns: [\r\n        {\r\n          data: null,\r\n          className: 'font-weight-bolder',\r\n          render: (data, type, row) => {\r\n            const name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n            if (row.first_name && row.last_name) {\r\n              if (name_settings && name_settings.is_on == 1) {\r\n                return row.first_name + ' ' + row.last_name;\r\n              } else {\r\n                return row.last_name + ' ' + row.first_name;\r\n              }\r\n            } else {\r\n              return '';\r\n            }\r\n          },\r\n        },\r\n        {\r\n          data: 'email',\r\n        },\r\n        {\r\n          data: 'phone',\r\n        },\r\n        {\r\n          data: null,\r\n          render: (data, type, row) => {\r\n            const isAssigned = teamCoaches.data.some((coach) => {\r\n              return coach.user_id === row.user_id;\r\n            });\r\n            const disabledAttr = isAssigned ? 'disabled' : '';\r\n            const buttonText = isAssigned ? 'Assigned' : 'Assign';\r\n            return `<button class=\"btn btn-primary btn-sm\" ${disabledAttr}\r\n                    data-row-value='${JSON.stringify(row)}'\r\n                    action=\"assign\">${this._translateService.instant(\r\n                      buttonText,\r\n                    )}</button>`;\r\n          },\r\n        },\r\n      ],\r\n      buttons: [],\r\n    };\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('data-row-value')) {\r\n        let row = event.target.getAttribute('data-row-value');\r\n        row = JSON.parse(row);\r\n\r\n        this.editor(event.target.getAttribute('action'), row);\r\n      }\r\n    });\r\n  }\r\n\r\n  editor(action, row) {\r\n    switch (action) {\r\n      case 'assign':\r\n        Swal.fire({\r\n          title: this._translateService.instant('Are you sure?'),\r\n          html:\r\n            `\r\n          <div class=\"text-center\">\r\n            <img src=\"assets/images/alerts/Frame1.svg\" width=\"200px\" height=\"149px\">\r\n            <p class=\"text-center\" style=\"margin-top: 1rem\">` +\r\n            this._translateService.instant(\r\n              'You want to assign this coach to this team?',\r\n            ) +\r\n            `\r\n            </p>\r\n          </div>`,\r\n          reverseButtons: true,\r\n          showCancelButton: true,\r\n          confirmButtonText: this._translateService.instant('Yes'),\r\n          cancelButtonText:\r\n            '<span class=\"text-primary\">' +\r\n            this._translateService.instant('Cancel') +\r\n            '</span>',\r\n          buttonsStyling: false,\r\n          customClass: {\r\n            confirmButton: 'btn btn-primary mr-1',\r\n            cancelButton: 'btn btn-outline-primary mr-1',\r\n          },\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            let team_id = this.params.team_id;\r\n            let user_id = row.user_id;\r\n            let assigned = '1';\r\n            let params: FormData = new FormData();\r\n            params.append('action', 'create');\r\n            params.append('data[0][team_id]', team_id);\r\n            params.append('data[0][user_id]', user_id);\r\n            params.append('data[0][assigned]', assigned);\r\n\r\n            this._teamService\r\n              .editorTableTeamCoaches(params)\r\n              .subscribe((resp: any) => {\r\n                if (resp) {\r\n                  this._toastrService.success(\r\n                    this._translateService.instant(\r\n                      'Coaches assigned successfully',\r\n                    ),\r\n                  );\r\n\r\n                  // disable button\r\n                  let button = document.querySelector(\r\n                    `button[action=\"assign\"][data-row-value='${JSON.stringify(\r\n                      row,\r\n                    )}']`,\r\n                  );\r\n                  button.setAttribute('disabled', 'disabled');\r\n                  button.classList.add('disabled');\r\n                  // change text\r\n                  button.innerHTML = this._translateService.instant('Assigned');\r\n                }\r\n              });\r\n          }\r\n        });\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  onClickAddNewCoach() {\r\n    this.form.reset();\r\n    this.model = {};\r\n    this.submitted = false;\r\n    this.showForm = true;\r\n  }\r\n\r\n  onClickCancel() {\r\n    this.showForm = false;\r\n  }\r\n\r\n  getUserByEmail(email) {\r\n    if (this.form.controls['email'].invalid) return;\r\n    if (!email.trim()) return;\r\n    this.isCheckEmail = true;\r\n    this.loadingService.show();\r\n    this._userService.getByEmail(email).subscribe(\r\n      (resp: any) => {\r\n        if (resp) {\r\n          if (resp.data) {\r\n            let data = resp.data;\r\n            this.disableFieldByData(data);\r\n            console.log('resp', resp);\r\n          } else {\r\n            this.disableFieldByData();\r\n          }\r\n        }\r\n        this.isCheckEmail = false;\r\n        this.onCheckEmail.emit(resp);\r\n      },\r\n      (error) => {\r\n        this.disableFieldByData();\r\n        console.log('error', error);\r\n        this.isCheckEmail = false;\r\n        this.onCheckEmail.emit(error);\r\n      },\r\n    );\r\n  }\r\n\r\n  disableFieldByData(data: any = null) {\r\n    if (data == null) {\r\n      for (let key in this.form.controls) {\r\n        if (key != 'email') {\r\n          this.form.controls[key].enable();\r\n          this.form.controls[key].setValue('');\r\n          // reset dirty and touched\r\n          this.form.controls[key].markAsUntouched();\r\n          this.form.controls[key].markAsPristine();\r\n        }\r\n      }\r\n    } else {\r\n      this.form.patchValue(data);\r\n      // disable field has value\r\n      for (let key in data) {\r\n        if (\r\n          this.form.controls.hasOwnProperty(key) &&\r\n          key != 'email' &&\r\n          data[key] != '' &&\r\n          data[key] != null &&\r\n          data[key] != undefined\r\n        ) {\r\n          this.form.controls[key]?.disable();\r\n        } else {\r\n          this.form.controls[key]?.enable();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  onSubmit(model) {\r\n\r\n    if (this.isCheckEmail) {\r\n      let uncsub = this.onCheckEmail.subscribe((resp) => {\r\n        setTimeout(() => {\r\n          this.onSubmit(model);\r\n        }, 1000);\r\n        uncsub.unsubscribe();\r\n      });\r\n      return;\r\n    }\r\n    // stop here if form is invalid\r\n    if (this.form.invalid) {\r\n      return;\r\n    } else {\r\n      // show loading spinner\r\n      this.loading = true;\r\n\r\n      // create new coach and assign to team\r\n      this._teamService\r\n        .assignNewCoachToTeam(this.params.team_id, model)\r\n        .pipe(takeUntil(this._unsubscribeAll))\r\n        .subscribe(\r\n          (res: any) => {\r\n            this.loading = false;\r\n            // show success message\r\n            Swal.fire({\r\n              title: 'Success',\r\n              text: res.message,\r\n              // icon: \"success\",\r\n              imageUrl: 'assets/images/alerts/signup.png',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              customClass: {\r\n                container: 'signup-successfull',\r\n                confirmButton: 'btn btn-primary',\r\n              },\r\n            }).then((result) => {\r\n              if (result.isConfirmed) {\r\n                this.onClose();\r\n                // hide form\r\n                this.showForm = false;\r\n              }\r\n            });\r\n          },\r\n          (error) => {\r\n            // hide loading spinner\r\n            this.loading = false;\r\n            // add error to form control\r\n            let errors = error.errors;\r\n            for (let key in errors) {\r\n              if (errors.hasOwnProperty(key)) {\r\n                this.form.controls[key].setErrors({\r\n                  serverError: errors[key][0],\r\n                });\r\n              }\r\n            }\r\n            // check length of array\r\n            if (errors.length > 0) {\r\n              // show error message\r\n              this._toastrService.error(error.message, 'Error', {\r\n                closeButton: true,\r\n                tapToDismiss: false,\r\n              });\r\n            }\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: 'Ok',\r\n            });\r\n          },\r\n        );\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    this.modalService.dismissAll();\r\n  }\r\n}\r\n", "<div>\r\n    <div class=\"modal-header\">\r\n        <div>\r\n            <h4>\r\n                <span>{{'Add new coach'| translate}}</span>\r\n            </h4>\r\n        </div>\r\n\r\n        <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\" (click)=\"onClose()\">\r\n            <span aria-hidden=\"true\">&times;</span>\r\n        </button>\r\n    </div>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n        <!-- Add new coach -->\r\n        <div *ngIf=\"showForm\">\r\n            <!-- Create a form with email, phone, first name and last name -->\r\n            <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit(model)\">\r\n                <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n            </form>\r\n        </div>\r\n        <!-- Suggestions -->\r\n        <div *ngIf=\"!showForm\">\r\n            <div class=\"row\">\r\n                <div class=\"col-12 col-sm-6\">\r\n                    <h5>\r\n                        <span>{{ 'Suggestions' | translate}}</span>\r\n                    </h5>\r\n                </div>\r\n                <div class=\"col-12 col-sm-6\">\r\n                    <button class=\"btn btn-sm btn-outline-primary float-right mb-1\" (click)=\"onClickAddNewCoach()\">\r\n                        <i class=\"fa fa-plus\"></i>\r\n                        {{ 'Add new coach' | translate}}\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <table datatable [dtOptions]=\"dtOptions1\" class=\"table border row-border hover\" *ngIf=\"dataLoaded\"> \r\n                <thead>\r\n                    <tr>\r\n                        <th class=\"text-capitalize\">{{'Name' | translate}}</th>\r\n                        <th class=\"text-capitalize\">{{'Email' | translate}}</th>\r\n                        <th class=\"text-capitalize\">{{'Phone' | translate}}</th>\r\n                        <th class=\"text-capitalize\">{{'Action' | translate}}</th>\r\n                    </tr>\r\n                </thead>\r\n            </table>\r\n        </div>\r\n    </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
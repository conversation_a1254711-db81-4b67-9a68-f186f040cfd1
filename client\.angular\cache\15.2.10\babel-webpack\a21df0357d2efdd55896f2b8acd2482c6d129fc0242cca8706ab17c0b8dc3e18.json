{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/layout/components/navbar/navbar-search/search.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/flex-layout/extended\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i8 from \"ngx-perfect-scrollbar\";\nimport * as i9 from \"@core/pipes/filter.pipe\";\nconst _c0 = [\"openBookmark\"];\nfunction NavbarBookmarkComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 3)(2, \"a\", 13);\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const page_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", page_r5.title);\n    i0.ɵɵproperty(\"routerLink\", page_r5.link);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"ficon\");\n    i0.ɵɵproperty(\"data-feather\", page_r5.icon);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    current_item: a0\n  };\n};\nfunction NavbarBookmarkComponent_ng_container_12_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 15)(1, \"a\", 16);\n    i0.ɵɵlistener(\"click\", function NavbarBookmarkComponent_ng_container_12_li_1_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      $event.preventDefault();\n      ctx_r9.closeBookmark();\n      return i0.ɵɵresetView(ctx_r9.removeOverlay());\n    });\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NavbarBookmarkComponent_ng_container_12_li_1_Template_button_click_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const page_r7 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r11.toggleBookmark(page_r7.id));\n    });\n    i0.ɵɵelement(7, \"i\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const page_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, i_r8 === ctx_r6.activeIndex));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", page_r7.link);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"feather icon-\", page_r7.icon, \" mr-75\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r7.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"text-warning\", page_r7.isBookmarked);\n  }\n}\nfunction NavbarBookmarkComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarBookmarkComponent_ng_container_12_li_1_Template, 8, 10, \"li\", 14);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵpipe(3, \"filter\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, i0.ɵɵpipeBind3(3, 5, ctx_r2.pages, ctx_r2.bookmarkText, \"title\"), 0, 6));\n  }\n}\nfunction NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 15)(1, \"a\", 16);\n    i0.ɵɵlistener(\"click\", function NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      $event.stopPropagation();\n      $event.preventDefault();\n      ctx_r16.closeBookmark();\n      return i0.ɵɵresetView(ctx_r16.removeOverlay());\n    });\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const page_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r18.toggleBookmark(page_r13.id));\n    });\n    i0.ɵɵelement(7, \"i\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    const i_r14 = ctx_r20.index;\n    const page_r13 = ctx_r20.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, i_r14 === ctx_r15.activeIndex));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", page_r13.link);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"feather icon-\", page_r13.icon, \" mr-75\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r13.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"text-warning\", page_r13.isBookmarked);\n  }\n}\nfunction NavbarBookmarkComponent_ng_template_13_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template, 8, 10, \"li\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const page_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", page_r13.isBookmarked);\n  }\n}\nfunction NavbarBookmarkComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavbarBookmarkComponent_ng_template_13_ng_container_0_Template, 2, 1, \"ng-container\", 1);\n    i0.ɵɵpipe(1, \"filter\");\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(1, 1, ctx_r4.pages, ctx_r4.bookmarkText, \"title\"));\n  }\n}\nexport class NavbarBookmarkComponent {\n  fn() {\n    this.removeOverlay();\n    this.openBookmarkRef = false;\n    this.bookmarkText = '';\n  }\n  clickout(event) {\n    // Close Bookmark if Clicked on Overlay\n    if (event.target.className === 'content-overlay') {\n      this.removeOverlay();\n      this.openBookmarkRef = false;\n      this.bookmarkText = '';\n    }\n    // Close Bookmark if clicked Outside of Container\n    if (!(event.target.nodeName === 'INPUT') && this.openBookmarkRef === true) {\n      this.removeOverlay();\n      this.openBookmarkRef = false;\n      this.bookmarkText = '';\n    }\n  }\n  /**\r\n   *\r\n   * @param document\r\n   * @param _searchService\r\n   */\n  constructor(document, _searchService) {\n    this.document = document;\n    this._searchService = _searchService;\n    // Public\n    this.bookmarkText = '';\n    this.openBookmarkRef = false;\n    this.activeIndex = 0;\n    this.pages = [];\n  }\n  // Public Methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * Add Bookmark\r\n   *\r\n   * @param id\r\n   */\n  addBookmark(id) {\n    const index = this.pages.findIndex(object => {\n      return object.id === id;\n    });\n    this.pages[index].isBookmarked = true;\n    this.bookmarkedItems.push(this.pages[index]);\n  }\n  /**\r\n   * Remove Bookmark\r\n   *\r\n   * @param id\r\n   */\n  removeBookmark(id) {\n    const index = this.bookmarkedItems.findIndex(object => {\n      return object.id === id;\n    });\n    this.bookmarkedItems[index].isBookmarked = false;\n    this.bookmarkedItems.splice(index, 1);\n  }\n  /**\r\n   * Open Bookmark\r\n   */\n  openBookmark() {\n    this.openBookmarkRef = true;\n    this._searchService.onIsBookmarkOpenChange.next(this.openBookmarkRef);\n  }\n  /**\r\n   * Close Bookmark\r\n   */\n  closeBookmark() {\n    this.openBookmarkRef = false;\n    this._searchService.onIsBookmarkOpenChange.next(this.openBookmarkRef);\n  }\n  /**\r\n   * Remove Overlay\r\n   */\n  removeOverlay() {\n    this.document.querySelector('.app-content').classList.remove('show-overlay');\n  }\n  /**\r\n   * Next Active Match\r\n   */\n  nextActiveMatch() {\n    this.activeIndex = this.activeIndex < this.bookmarkSearchLimit - 1 ? ++this.activeIndex : this.activeIndex;\n  }\n  /**\r\n   * Previous Active Match\r\n   */\n  prevActiveMatch() {\n    this.activeIndex = this.activeIndex > 0 ? --this.activeIndex : 0;\n  }\n  /**\r\n   * Auto Suggestion\r\n   *\r\n   * @param event\r\n   */\n  autoSuggestion(event) {\n    if (38 === event.keyCode) {\n      return this.prevActiveMatch();\n    }\n    if (40 === event.keyCode) {\n      return this.nextActiveMatch();\n    }\n  }\n  /**\r\n   * Toggle Bookmark\r\n   *\r\n   * @param id\r\n   */\n  toggleBookmark(id) {\n    const index = this.pages.findIndex(object => {\n      return object.id === id;\n    });\n    if (this.pages[index].isBookmarked === true) {\n      this.removeBookmark(id);\n    } else {\n      this.addBookmark(id);\n    }\n  }\n  /**\r\n   * Toggle Bookmark Popup\r\n   */\n  toggleBookmarkPopup() {\n    setTimeout(() => {\n      if (this.openBookmarkRef === false) {\n        this.openBookmark();\n      } else {\n        this.closeBookmark();\n      }\n      setTimeout(() => {\n        this._bookmarkElement.nativeElement.focus();\n      }, 0);\n    }, 0);\n  }\n  /**\r\n   * Update Bookmark\r\n   *\r\n   * @param event\r\n   */\n  bookmarkUpdate(event) {\n    const val = event.target.value.toLowerCase();\n    if (val !== '') {\n      this.document.querySelector('.app-content').classList.add('show-overlay');\n    } else {\n      this.document.querySelector('.app-content').classList.remove('show-overlay');\n    }\n    this.autoSuggestion(event);\n  }\n  // Lifecycle Hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\r\n   * On init\r\n   */\n  ngOnInit() {\n    this._searchService.onApiDataChange.subscribe(res => {\n      this.apiData = res;\n      this.pages = this.apiData[0].data;\n      this.bookmarkedItems = this.pages.filter(page => page.isBookmarked === true);\n      this.bookmarkSearchLimit = this.apiData[0].bookmarkLimit;\n    });\n    this._searchService.onIsBookmarkOpenChange.subscribe(res => {\n      this.openBookmarkRef = res;\n    });\n  }\n  static #_ = this.ɵfac = function NavbarBookmarkComponent_Factory(t) {\n    return new (t || NavbarBookmarkComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.SearchService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NavbarBookmarkComponent,\n    selectors: [[\"app-navbar-bookmark\"]],\n    viewQuery: function NavbarBookmarkComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._bookmarkElement = _t.first);\n      }\n    },\n    hostBindings: function NavbarBookmarkComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.escape\", function NavbarBookmarkComponent_keydown_escape_HostBindingHandler() {\n          return ctx.fn();\n        })(\"click\", function NavbarBookmarkComponent_click_HostBindingHandler($event) {\n          return ctx.clickout($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 15,\n    vars: 10,\n    consts: [[1, \"nav\", \"navbar-nav\", \"bookmark-icons\"], [4, \"ngFor\", \"ngForOf\"], [1, \"nav\", \"navbar-nav\"], [1, \"nav-item\", \"d-none\", \"d-lg-block\"], [1, \"nav-link\", \"bookmark-star\", 3, \"click\"], [3, \"data-feather\"], [1, \"bookmark-input\", \"search-input\"], [1, \"bookmark-input-icon\"], [\"type\", \"text\", \"placeholder\", \"Bookmark\", \"tabindex\", \"0\", \"data-search\", \"search\", 1, \"form-control\", \"input\", 3, \"ngModel\", \"keyup\", \"ngModelChange\"], [\"openBookmark\", \"\"], [1, \"search-list\", \"search-list-bookmark\", \"show\", 3, \"perfectScrollbar\"], [4, \"ngIf\", \"ngIfElse\"], [\"defaultBookmarks\", \"\"], [\"placement\", \"bottom\", 1, \"nav-link\", 3, \"routerLink\", \"ngbTooltip\"], [\"class\", \"auto-suggestion\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"auto-suggestion\", 3, \"ngClass\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"w-100\", 3, \"routerLink\", \"click\"], [1, \"d-flex\", \"justify-content-start\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"p-0\", 3, \"click\"], [1, \"feather\", \"icon-star\"], [\"class\", \"auto-suggestion\", 3, \"ngClass\", 4, \"ngIf\"]],\n    template: function NavbarBookmarkComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ul\", 0);\n        i0.ɵɵtemplate(1, NavbarBookmarkComponent_ng_container_1_Template, 4, 5, \"ng-container\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"ul\", 2)(3, \"li\", 3)(4, \"a\", 4);\n        i0.ɵɵlistener(\"click\", function NavbarBookmarkComponent_Template_a_click_4_listener() {\n          return ctx.toggleBookmarkPopup();\n        });\n        i0.ɵɵelement(5, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n        i0.ɵɵelement(8, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"input\", 8, 9);\n        i0.ɵɵlistener(\"keyup\", function NavbarBookmarkComponent_Template_input_keyup_9_listener($event) {\n          return ctx.bookmarkUpdate($event);\n        })(\"ngModelChange\", function NavbarBookmarkComponent_Template_input_ngModelChange_9_listener($event) {\n          return ctx.bookmarkText = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"ul\", 10);\n        i0.ɵɵtemplate(12, NavbarBookmarkComponent_ng_container_12_Template, 4, 9, \"ng-container\", 11);\n        i0.ɵɵtemplate(13, NavbarBookmarkComponent_ng_template_13_Template, 2, 5, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        const _r3 = i0.ɵɵreference(14);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.bookmarkedItems);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassMap(\"ficon text-warning\");\n        i0.ɵɵproperty(\"data-feather\", \"star\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"show\", ctx.openBookmarkRef);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data-feather\", \"search\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.bookmarkText);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.bookmarkText !== \"\")(\"ngIfElse\", _r3);\n      }\n    },\n    dependencies: [i2.RouterLink, i3.NgbTooltip, i4.NgClass, i4.NgForOf, i4.NgIf, i5.DefaultClassDirective, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.FeatherIconDirective, i8.PerfectScrollbarDirective, i4.SlicePipe, i9.FilterPipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;;;;;;;;;;;;;;ICExCC,EAAA,CAAAC,uBAAA,GAAmD;IACjDD,EAAA,CAAAE,cAAA,YAAuC;IAEnCF,EAAA,CAAAG,SAAA,cAA0D;IAC5DH,EAAA,CAAAI,YAAA,EAAI;IAERJ,EAAA,CAAAK,qBAAA,EAAe;;;;IAJqDL,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAO,qBAAA,eAAAC,OAAA,CAAAC,KAAA,CAA6B;IAAzET,EAAA,CAAAU,UAAA,eAAAF,OAAA,CAAAG,IAAA,CAAwB;IACTX,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAY,UAAA,SAAiB;IAA5CZ,EAAA,CAAAU,UAAA,iBAAAF,OAAA,CAAAK,IAAA,CAA0B;;;;;;;;;;;IAgC9Bb,EAAA,CAAAE,cAAA,aAIC;IAIGF,EAAA,CAAAc,UAAA,mBAAAC,yEAAAC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAASJ,MAAA,CAAAK,eAAA,EAAwB;MAAEL,MAAA,CAAAM,cAAA,EAAuB;MAAEH,MAAA,CAAAI,aAAA,EAAe;MAAA,OAAEvB,EAAA,CAAAwB,WAAA,CAAAL,MAAA,CAAAM,aAAA,EAAe;IAAA,EAAC;IAC5FzB,EAAA,CAAAE,cAAA,cAA6D;IAC5DF,EAAA,CAAAG,SAAA,QAAkD;IAClDH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAA0B,MAAA,GAAgB;IAAA1B,EAAA,CAAAI,YAAA,EAAO;IAE/BJ,EAAA,CAAAE,cAAA,iBAIC;IADCF,EAAA,CAAAc,UAAA,mBAAAa,8EAAAX,MAAA;MAAA,MAAAY,WAAA,GAAA5B,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAD,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAoB,aAAA;MAASJ,MAAA,CAAAK,eAAA,EAAwB;MAAEL,MAAA,CAAAM,cAAA,EAAuB;MAAA,OAAEtB,EAAA,CAAAwB,WAAA,CAAAO,OAAA,CAAAC,cAAA,CAAAH,OAAA,CAAAI,EAAA,CAAuB;IAAA,EAAC;IAEpFjC,EAAA,CAAAG,SAAA,YAA0E;IAC5EH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAhBXJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAAC,IAAA,KAAAC,MAAA,CAAAC,WAAA,EAA+C;IAI7CtC,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAU,UAAA,eAAAmB,OAAA,CAAAlB,IAAA,CAAwB;IAGnBX,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAuC,sBAAA,kBAAAV,OAAA,CAAAhB,IAAA,WAA0C;IACvCb,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAwC,iBAAA,CAAAX,OAAA,CAAApB,KAAA,CAAgB;IAOOT,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAyC,WAAA,iBAAAZ,OAAA,CAAAa,YAAA,CAAwC;;;;;IAnB7E1C,EAAA,CAAAC,uBAAA,GAAiE;IAC/DD,EAAA,CAAA2C,UAAA,IAAAC,qDAAA,kBAqBK;;;IACP5C,EAAA,CAAAK,qBAAA,EAAe;;;;IApBML,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA6C,WAAA,OAAA7C,EAAA,CAAA6C,WAAA,OAAAC,MAAA,CAAAC,KAAA,EAAAD,MAAA,CAAAE,YAAA,kBAAsD;;;;;;IAuBvEhD,EAAA,CAAAE,cAAA,aAAsG;IAIlGF,EAAA,CAAAc,UAAA,mBAAAmC,uFAAAjC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAoB,aAAA;MAASJ,MAAA,CAAAK,eAAA,EAAwB;MAAEL,MAAA,CAAAM,cAAA,EAAuB;MAAE6B,OAAA,CAAA5B,aAAA,EAAe;MAAA,OAAEvB,EAAA,CAAAwB,WAAA,CAAA2B,OAAA,CAAA1B,aAAA,EAAe;IAAA,EAAC;IAC5FzB,EAAA,CAAAE,cAAA,cAA6D;IAC5DF,EAAA,CAAAG,SAAA,QAAkD;IAClDH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAA0B,MAAA,GAAgB;IAAA1B,EAAA,CAAAI,YAAA,EAAO;IAE/BJ,EAAA,CAAAE,cAAA,iBAIC;IADCF,EAAA,CAAAc,UAAA,mBAAAsC,4FAAApC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAiC,IAAA;MAAA,MAAAG,QAAA,GAAArD,EAAA,CAAAoB,aAAA,GAAAU,SAAA;MAAA,MAAAwB,OAAA,GAAAtD,EAAA,CAAAoB,aAAA;MAASJ,MAAA,CAAAK,eAAA,EAAwB;MAAEL,MAAA,CAAAM,cAAA,EAAuB;MAAA,OAAEtB,EAAA,CAAAwB,WAAA,CAAA8B,OAAA,CAAAtB,cAAA,CAAAqB,QAAA,CAAApB,EAAA,CAAuB;IAAA,EAAC;IAEpFjC,EAAA,CAAAG,SAAA,YAA0E;IAC5EH,EAAA,CAAAI,YAAA,EAAS;;;;;;;IAfyCJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAAoB,KAAA,KAAAC,OAAA,CAAAlB,WAAA,EAA+C;IAGjGtC,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAU,UAAA,eAAA2C,QAAA,CAAA1C,IAAA,CAAwB;IAGnBX,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAuC,sBAAA,kBAAAc,QAAA,CAAAxC,IAAA,WAA0C;IACvCb,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAwC,iBAAA,CAAAa,QAAA,CAAA5C,KAAA,CAAgB;IAOOT,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAyC,WAAA,iBAAAY,QAAA,CAAAX,YAAA,CAAwC;;;;;IAf7E1C,EAAA,CAAAC,uBAAA,GAAuF;IACrFD,EAAA,CAAA2C,UAAA,IAAAc,mEAAA,kBAiBK;IACPzD,EAAA,CAAAK,qBAAA,EAAe;;;;IAlBgBL,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAU,UAAA,SAAA2C,QAAA,CAAAX,YAAA,CAAuB;;;;;IADtD1C,EAAA,CAAA2C,UAAA,IAAAe,8DAAA,0BAmBe;;;;;IAnBgB1D,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA6C,WAAA,OAAAc,MAAA,CAAAZ,KAAA,EAAAY,MAAA,CAAAX,YAAA,WAAyC;;;ADpDlF,OAAM,MAAOY,uBAAuB;EAYFC,EAAEA,CAAA;IAChC,IAAI,CAACpC,aAAa,EAAE;IACpB,IAAI,CAACqC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACd,YAAY,GAAG,EAAE;EACxB;EAC4Ce,QAAQA,CAACC,KAAK;IACxD;IACA,IAAIA,KAAK,CAACC,MAAM,CAACC,SAAS,KAAK,iBAAiB,EAAE;MAChD,IAAI,CAACzC,aAAa,EAAE;MACpB,IAAI,CAACqC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACd,YAAY,GAAG,EAAE;;IAGxB;IACA,IAAI,EAAEgB,KAAK,CAACC,MAAM,CAACE,QAAQ,KAAK,OAAO,CAAC,IAAI,IAAI,CAACL,eAAe,KAAK,IAAI,EAAE;MACzE,IAAI,CAACrC,aAAa,EAAE;MACpB,IAAI,CAACqC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACd,YAAY,GAAG,EAAE;;EAE1B;EAEA;;;;;EAKAoB,YAAsCC,QAAQ,EAASC,cAA6B;IAA9C,KAAAD,QAAQ,GAARA,QAAQ;IAAS,KAAAC,cAAc,GAAdA,cAAc;IArCrE;IACO,KAAAtB,YAAY,GAAG,EAAE;IACjB,KAAAc,eAAe,GAAG,KAAK;IACvB,KAAAxB,WAAW,GAAG,CAAC;IAEf,KAAAS,KAAK,GAAG,EAAE;EAgCsE;EAEvF;EACA;EAEA;;;;;EAKAwB,WAAWA,CAACtC,EAAE;IACZ,MAAMuC,KAAK,GAAG,IAAI,CAACzB,KAAK,CAAC0B,SAAS,CAACC,MAAM,IAAG;MAC1C,OAAOA,MAAM,CAACzC,EAAE,KAAKA,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAACc,KAAK,CAACyB,KAAK,CAAC,CAAC9B,YAAY,GAAG,IAAI;IAErC,IAAI,CAACiC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC7B,KAAK,CAACyB,KAAK,CAAC,CAAC;EAC9C;EAEA;;;;;EAKAK,cAAcA,CAAC5C,EAAE;IACf,MAAMuC,KAAK,GAAG,IAAI,CAACG,eAAe,CAACF,SAAS,CAACC,MAAM,IAAG;MACpD,OAAOA,MAAM,CAACzC,EAAE,KAAKA,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAAC0C,eAAe,CAACH,KAAK,CAAC,CAAC9B,YAAY,GAAG,KAAK;IAChD,IAAI,CAACiC,eAAe,CAACG,MAAM,CAACN,KAAK,EAAE,CAAC,CAAC;EACvC;EAEA;;;EAGAO,YAAYA,CAAA;IACV,IAAI,CAACjB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACQ,cAAc,CAACU,sBAAsB,CAACC,IAAI,CAAC,IAAI,CAACnB,eAAe,CAAC;EACvE;EAEA;;;EAGAvC,aAAaA,CAAA;IACX,IAAI,CAACuC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACQ,cAAc,CAACU,sBAAsB,CAACC,IAAI,CAAC,IAAI,CAACnB,eAAe,CAAC;EACvE;EAEA;;;EAGArC,aAAaA,CAAA;IACX,IAAI,CAAC4C,QAAQ,CAACa,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;EAC9E;EAEA;;;EAGAC,eAAeA,CAAA;IACb,IAAI,CAAC/C,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAI,CAACgD,mBAAmB,GAAG,CAAC,GAAG,EAAE,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACA,WAAW;EAC5G;EAEA;;;EAGAiD,eAAeA,CAAA;IACb,IAAI,CAACjD,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC,GAAG,EAAE,IAAI,CAACA,WAAW,GAAG,CAAC;EAClE;EAEA;;;;;EAKAkD,cAAcA,CAACxB,KAAK;IAClB,IAAI,EAAE,KAAKA,KAAK,CAACyB,OAAO,EAAE;MACxB,OAAO,IAAI,CAACF,eAAe,EAAE;;IAE/B,IAAI,EAAE,KAAKvB,KAAK,CAACyB,OAAO,EAAE;MACxB,OAAO,IAAI,CAACJ,eAAe,EAAE;;EAEjC;EAEA;;;;;EAKArD,cAAcA,CAACC,EAAE;IACf,MAAMuC,KAAK,GAAG,IAAI,CAACzB,KAAK,CAAC0B,SAAS,CAACC,MAAM,IAAG;MAC1C,OAAOA,MAAM,CAACzC,EAAE,KAAKA,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,IAAI,CAACc,KAAK,CAACyB,KAAK,CAAC,CAAC9B,YAAY,KAAK,IAAI,EAAE;MAC3C,IAAI,CAACmC,cAAc,CAAC5C,EAAE,CAAC;KACxB,MAAM;MACL,IAAI,CAACsC,WAAW,CAACtC,EAAE,CAAC;;EAExB;EAEA;;;EAGAyD,mBAAmBA,CAAA;IACjBC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC7B,eAAe,KAAK,KAAK,EAAE;QAClC,IAAI,CAACiB,YAAY,EAAE;OACpB,MAAM;QACL,IAAI,CAACxD,aAAa,EAAE;;MAEtBoE,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,gBAAgB,CAACC,aAAa,CAACC,KAAK,EAAE;MAC7C,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;;;EAKAC,cAAcA,CAAC/B,KAAK;IAClB,MAAMgC,GAAG,GAAGhC,KAAK,CAACC,MAAM,CAACgC,KAAK,CAACC,WAAW,EAAE;IAC5C,IAAIF,GAAG,KAAK,EAAE,EAAE;MACd,IAAI,CAAC3B,QAAQ,CAACa,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACgB,GAAG,CAAC,cAAc,CAAC;KAC1E,MAAM;MACL,IAAI,CAAC9B,QAAQ,CAACa,aAAa,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;;IAE9E,IAAI,CAACI,cAAc,CAACxB,KAAK,CAAC;EAC5B;EAEA;EACA;EAEA;;;EAGAoC,QAAQA,CAAA;IACN,IAAI,CAAC9B,cAAc,CAAC+B,eAAe,CAACC,SAAS,CAACC,GAAG,IAAG;MAClD,IAAI,CAACC,OAAO,GAAGD,GAAG;MAClB,IAAI,CAACxD,KAAK,GAAG,IAAI,CAACyD,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;MACjC,IAAI,CAAC9B,eAAe,GAAG,IAAI,CAAC5B,KAAK,CAAC2D,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjE,YAAY,KAAK,IAAI,CAAC;MAC5E,IAAI,CAAC4C,mBAAmB,GAAG,IAAI,CAACkB,OAAO,CAAC,CAAC,CAAC,CAACI,aAAa;IAC1D,CAAC,CAAC;IACF,IAAI,CAACtC,cAAc,CAACU,sBAAsB,CAACsB,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACzC,eAAe,GAAGyC,GAAG;IAC5B,CAAC,CAAC;EACJ;EAAC,QAAAM,CAAA;qBAxLUjD,uBAAuB,EAAA5D,EAAA,CAAA8G,iBAAA,CAsCd/G,QAAQ,GAAAC,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA;UAtCjBrD,uBAAuB;IAAAsD,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;iBAAvBC,GAAA,CAAAzD,EAAA,EAAI;QAAA,qBAAA0D,iDAAAvG,MAAA;UAAA,OAAJsG,GAAA,CAAAvD,QAAA,CAAA/C,MAAA,CAAgB;QAAA,UAAAhB,EAAA,CAAAwH,iBAAA;;;;;;;;QCR7BxH,EAAA,CAAAE,cAAA,YAA0C;QACxCF,EAAA,CAAA2C,UAAA,IAAA8E,+CAAA,0BAMe;QACjBzH,EAAA,CAAAI,YAAA,EAAK;QAGLJ,EAAA,CAAAE,cAAA,YAA2B;QAGWF,EAAA,CAAAc,UAAA,mBAAA4G,oDAAA;UAAA,OAASJ,GAAA,CAAA5B,mBAAA,EAAqB;QAAA,EAAC;QAC9D1F,EAAA,CAAAG,SAAA,cACF;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAILJ,EAAA,CAAAE,cAAA,aAAwE;QACrCF,EAAA,CAAAG,SAAA,cAAuC;QAAAH,EAAA,CAAAI,YAAA,EAAM;QAC9EJ,EAAA,CAAAE,cAAA,kBASE;QAFAF,EAAA,CAAAc,UAAA,mBAAA6G,wDAAA3G,MAAA;UAAA,OAASsG,GAAA,CAAAvB,cAAA,CAAA/E,MAAA,CAAsB;QAAA,EAAC,2BAAA4G,gEAAA5G,MAAA;UAAA,OAAAsG,GAAA,CAAAtE,YAAA,GAAAhC,MAAA;QAAA;QAPlChB,EAAA,CAAAI,YAAA,EASE;QAGFJ,EAAA,CAAAE,cAAA,cAAqE;QACnEF,EAAA,CAAA2C,UAAA,KAAAkF,gDAAA,2BAuBe;QACf7H,EAAA,CAAA2C,UAAA,KAAAmF,+CAAA,iCAAA9H,EAAA,CAAA+H,sBAAA,CAqBc;QAChB/H,EAAA,CAAAI,YAAA,EAAK;;;;QAhFsBJ,EAAA,CAAAM,SAAA,GAAkB;QAAlBN,EAAA,CAAAU,UAAA,YAAA4G,GAAA,CAAA3C,eAAA,CAAkB;QAcd3E,EAAA,CAAAM,SAAA,GAA8B;QAA9BN,EAAA,CAAAY,UAAA,sBAA8B;QAAtDZ,EAAA,CAAAU,UAAA,wBAAuB;QAKSV,EAAA,CAAAM,SAAA,GAA8B;QAA9BN,EAAA,CAAAyC,WAAA,SAAA6E,GAAA,CAAAxD,eAAA,CAA8B;QAC9B9D,EAAA,CAAAM,SAAA,GAAyB;QAAzBN,EAAA,CAAAU,UAAA,0BAAyB;QAS9DV,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAAU,UAAA,YAAA4G,GAAA,CAAAtE,YAAA,CAA0B;QAKXhD,EAAA,CAAAM,SAAA,GAA2B;QAA3BN,EAAA,CAAAU,UAAA,SAAA4G,GAAA,CAAAtE,YAAA,QAA2B,aAAAgF,GAAA", "names": ["DOCUMENT", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "page_r5", "title", "ɵɵproperty", "link", "ɵɵclassMap", "icon", "ɵɵlistener", "NavbarBookmarkComponent_ng_container_12_li_1_Template_a_click_1_listener", "$event", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "stopPropagation", "preventDefault", "closeBookmark", "ɵɵresetView", "removeOverlay", "ɵɵtext", "NavbarBookmarkComponent_ng_container_12_li_1_Template_button_click_6_listener", "restoredCtx", "page_r7", "$implicit", "ctx_r11", "toggleBookmark", "id", "ɵɵpureFunction1", "_c1", "i_r8", "ctx_r6", "activeIndex", "ɵɵclassMapInterpolate1", "ɵɵtextInterpolate", "ɵɵclassProp", "isBookmarked", "ɵɵtemplate", "NavbarBookmarkComponent_ng_container_12_li_1_Template", "ɵɵpipeBind3", "ctx_r2", "pages", "bookmarkText", "NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template_a_click_1_listener", "_r17", "ctx_r16", "NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template_button_click_6_listener", "page_r13", "ctx_r18", "i_r14", "ctx_r15", "NavbarBookmarkComponent_ng_template_13_ng_container_0_li_1_Template", "NavbarBookmarkComponent_ng_template_13_ng_container_0_Template", "ctx_r4", "NavbarBookmarkComponent", "fn", "openBookmarkRef", "clickout", "event", "target", "className", "nodeName", "constructor", "document", "_searchService", "addBookmark", "index", "findIndex", "object", "bookmarkedItems", "push", "removeBookmark", "splice", "openBookmark", "onIsBookmarkOpenChange", "next", "querySelector", "classList", "remove", "nextActiveMatch", "bookmarkSearchLimit", "prevActiveMatch", "autoSuggestion", "keyCode", "toggleBookmarkPopup", "setTimeout", "_bookmarkElement", "nativeElement", "focus", "bookmarkUpdate", "val", "value", "toLowerCase", "add", "ngOnInit", "onApiDataChange", "subscribe", "res", "apiData", "data", "filter", "page", "bookmarkLimit", "_", "ɵɵdirectiveInject", "i1", "SearchService", "_2", "selectors", "viewQuery", "NavbarBookmarkComponent_Query", "rf", "ctx", "NavbarBookmarkComponent_click_HostBindingHandler", "ɵɵresolveDocument", "NavbarBookmarkComponent_ng_container_1_Template", "NavbarBookmarkComponent_Template_a_click_4_listener", "NavbarBookmarkComponent_Template_input_keyup_9_listener", "NavbarBookmarkComponent_Template_input_ngModelChange_9_listener", "NavbarBookmarkComponent_ng_container_12_Template", "NavbarBookmarkComponent_ng_template_13_Template", "ɵɵtemplateRefExtractor", "_r3"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar-bookmark\\navbar-bookmark.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\navbar\\navbar-bookmark\\navbar-bookmark.component.html"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\r\nimport { Component, ElementRef, HostListener, Inject, OnInit, ViewChild } from '@angular/core';\r\n\r\nimport { SearchService } from 'app/layout/components/navbar/navbar-search/search.service';\r\n\r\n@Component({\r\n  selector: 'app-navbar-bookmark',\r\n  templateUrl: './navbar-bookmark.component.html'\r\n})\r\nexport class NavbarBookmarkComponent implements OnInit {\r\n  // Public\r\n  public bookmarkText = '';\r\n  public openBookmarkRef = false;\r\n  public activeIndex = 0;\r\n  public apiData;\r\n  public pages = [];\r\n  public bookmarkSearchLimit;\r\n  public bookmarkedItems;\r\n\r\n  // Decorator\r\n  @ViewChild('openBookmark') private _bookmarkElement: ElementRef;\r\n  @HostListener('keydown.escape') fn() {\r\n    this.removeOverlay();\r\n    this.openBookmarkRef = false;\r\n    this.bookmarkText = '';\r\n  }\r\n  @HostListener('document:click', ['$event']) clickout(event) {\r\n    // Close Bookmark if Clicked on Overlay\r\n    if (event.target.className === 'content-overlay') {\r\n      this.removeOverlay();\r\n      this.openBookmarkRef = false;\r\n      this.bookmarkText = '';\r\n    }\r\n\r\n    // Close Bookmark if clicked Outside of Container\r\n    if (!(event.target.nodeName === 'INPUT') && this.openBookmarkRef === true) {\r\n      this.removeOverlay();\r\n      this.openBookmarkRef = false;\r\n      this.bookmarkText = '';\r\n    }\r\n  }\r\n\r\n  /**\r\n   *\r\n   * @param document\r\n   * @param _searchService\r\n   */\r\n  constructor(@Inject(DOCUMENT) private document, public _searchService: SearchService) {}\r\n\r\n  // Public Methods\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Add Bookmark\r\n   *\r\n   * @param id\r\n   */\r\n  addBookmark(id) {\r\n    const index = this.pages.findIndex(object => {\r\n      return object.id === id;\r\n    });\r\n    this.pages[index].isBookmarked = true;\r\n\r\n    this.bookmarkedItems.push(this.pages[index]);\r\n  }\r\n\r\n  /**\r\n   * Remove Bookmark\r\n   *\r\n   * @param id\r\n   */\r\n  removeBookmark(id) {\r\n    const index = this.bookmarkedItems.findIndex(object => {\r\n      return object.id === id;\r\n    });\r\n    this.bookmarkedItems[index].isBookmarked = false;\r\n    this.bookmarkedItems.splice(index, 1);\r\n  }\r\n\r\n  /**\r\n   * Open Bookmark\r\n   */\r\n  openBookmark() {\r\n    this.openBookmarkRef = true;\r\n    this._searchService.onIsBookmarkOpenChange.next(this.openBookmarkRef);\r\n  }\r\n\r\n  /**\r\n   * Close Bookmark\r\n   */\r\n  closeBookmark() {\r\n    this.openBookmarkRef = false;\r\n    this._searchService.onIsBookmarkOpenChange.next(this.openBookmarkRef);\r\n  }\r\n\r\n  /**\r\n   * Remove Overlay\r\n   */\r\n  removeOverlay() {\r\n    this.document.querySelector('.app-content').classList.remove('show-overlay');\r\n  }\r\n\r\n  /**\r\n   * Next Active Match\r\n   */\r\n  nextActiveMatch() {\r\n    this.activeIndex = this.activeIndex < this.bookmarkSearchLimit - 1 ? ++this.activeIndex : this.activeIndex;\r\n  }\r\n\r\n  /**\r\n   * Previous Active Match\r\n   */\r\n  prevActiveMatch() {\r\n    this.activeIndex = this.activeIndex > 0 ? --this.activeIndex : 0;\r\n  }\r\n\r\n  /**\r\n   * Auto Suggestion\r\n   *\r\n   * @param event\r\n   */\r\n  autoSuggestion(event) {\r\n    if (38 === event.keyCode) {\r\n      return this.prevActiveMatch();\r\n    }\r\n    if (40 === event.keyCode) {\r\n      return this.nextActiveMatch();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Toggle Bookmark\r\n   *\r\n   * @param id\r\n   */\r\n  toggleBookmark(id) {\r\n    const index = this.pages.findIndex(object => {\r\n      return object.id === id;\r\n    });\r\n    if (this.pages[index].isBookmarked === true) {\r\n      this.removeBookmark(id);\r\n    } else {\r\n      this.addBookmark(id);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Toggle Bookmark Popup\r\n   */\r\n  toggleBookmarkPopup() {\r\n    setTimeout(() => {\r\n      if (this.openBookmarkRef === false) {\r\n        this.openBookmark();\r\n      } else {\r\n        this.closeBookmark();\r\n      }\r\n      setTimeout(() => {\r\n        this._bookmarkElement.nativeElement.focus();\r\n      }, 0);\r\n    }, 0);\r\n  }\r\n\r\n  /**\r\n   * Update Bookmark\r\n   *\r\n   * @param event\r\n   */\r\n  bookmarkUpdate(event) {\r\n    const val = event.target.value.toLowerCase();\r\n    if (val !== '') {\r\n      this.document.querySelector('.app-content').classList.add('show-overlay');\r\n    } else {\r\n      this.document.querySelector('.app-content').classList.remove('show-overlay');\r\n    }\r\n    this.autoSuggestion(event);\r\n  }\r\n\r\n  // Lifecycle Hooks\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  /**\r\n   * On init\r\n   */\r\n  ngOnInit(): void {\r\n    this._searchService.onApiDataChange.subscribe(res => {\r\n      this.apiData = res;\r\n      this.pages = this.apiData[0].data;\r\n      this.bookmarkedItems = this.pages.filter(page => page.isBookmarked === true);\r\n      this.bookmarkSearchLimit = this.apiData[0].bookmarkLimit;\r\n    });\r\n    this._searchService.onIsBookmarkOpenChange.subscribe(res => {\r\n      this.openBookmarkRef = res;\r\n    });\r\n  }\r\n}\r\n", "<!-- Bookmarked Icons -->\r\n<ul class=\"nav navbar-nav bookmark-icons\">\r\n  <ng-container *ngFor=\"let page of bookmarkedItems\">\r\n    <li class=\"nav-item d-none d-lg-block\">\r\n      <a class=\"nav-link\" [routerLink]=\"page.link\" placement=\"bottom\" ngbTooltip=\"{{ page.title }}\">\r\n        <span [data-feather]=\"page.icon\" [class]=\"'ficon'\"></span>\r\n      </a>\r\n    </li>\r\n  </ng-container>\r\n</ul>\r\n<!-- Bookmarked Icons -->\r\n\r\n<ul class=\"nav navbar-nav\">\r\n  <li class=\"nav-item d-none d-lg-block\">\r\n    <!-- Bookmark Icon -->\r\n    <a class=\"nav-link bookmark-star\" (click)=\"toggleBookmarkPopup()\"\r\n      ><span [data-feather]=\"'star'\" [class]=\"'ficon text-warning'\"></span\r\n    ></a>\r\n    <!--/ Bookmark Icon -->\r\n\r\n    <!-- Bookmark Pop-up -->\r\n    <div class=\"bookmark-input search-input\" [class.show]=\"openBookmarkRef\">\r\n      <div class=\"bookmark-input-icon\"><span [data-feather]=\"'search'\"></span></div>\r\n      <input\r\n        class=\"form-control input\"\r\n        type=\"text\"\r\n        placeholder=\"Bookmark\"\r\n        #openBookmark\r\n        tabindex=\"0\"\r\n        data-search=\"search\"\r\n        (keyup)=\"bookmarkUpdate($event)\"\r\n        [(ngModel)]=\"bookmarkText\"\r\n      />\r\n\r\n      <!-- Auto Suggestion List -->\r\n      <ul class=\"search-list search-list-bookmark show\" [perfectScrollbar]>\r\n        <ng-container *ngIf=\"bookmarkText !== ''; else defaultBookmarks\">\r\n          <li\r\n            class=\"auto-suggestion\"\r\n            *ngFor=\"let page of pages | filter: bookmarkText:'title' | slice: 0:6; let i = index\"\r\n            [ngClass]=\"{ current_item: i === activeIndex }\"\r\n          >\r\n            <a\r\n              class=\"d-flex align-items-center justify-content-between w-100\"\r\n              [routerLink]=\"page.link\"\r\n              (click)=\"$event.stopPropagation(); $event.preventDefault(); closeBookmark(); removeOverlay()\"\r\n              ><div class=\"d-flex justify-content-start align-items-center\">\r\n                <i class=\"feather icon-{{ page.icon }} mr-75\"></i>\r\n                <span>{{ page.title }}</span>\r\n              </div>\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn p-0\"\r\n                (click)=\"$event.stopPropagation(); $event.preventDefault(); toggleBookmark(page.id)\"\r\n              >\r\n                <i class=\"feather icon-star\" [class.text-warning]=\"page.isBookmarked\"></i>\r\n              </button>\r\n            </a>\r\n          </li>\r\n        </ng-container>\r\n        <ng-template #defaultBookmarks>\r\n          <ng-container *ngFor=\"let page of pages | filter: bookmarkText:'title'; let i = index\">\r\n            <li class=\"auto-suggestion\" *ngIf=\"page.isBookmarked\" [ngClass]=\"{ current_item: i === activeIndex }\">\r\n              <a\r\n                class=\"d-flex align-items-center justify-content-between w-100\"\r\n                [routerLink]=\"page.link\"\r\n                (click)=\"$event.stopPropagation(); $event.preventDefault(); closeBookmark(); removeOverlay()\"\r\n                ><div class=\"d-flex justify-content-start align-items-center\">\r\n                  <i class=\"feather icon-{{ page.icon }} mr-75\"></i>\r\n                  <span>{{ page.title }}</span>\r\n                </div>\r\n                <button\r\n                  type=\"button\"\r\n                  class=\"btn p-0\"\r\n                  (click)=\"$event.stopPropagation(); $event.preventDefault(); toggleBookmark(page.id)\"\r\n                >\r\n                  <i class=\"feather icon-star\" [class.text-warning]=\"page.isBookmarked\"></i>\r\n                </button>\r\n              </a>\r\n            </li>\r\n          </ng-container>\r\n        </ng-template>\r\n      </ul>\r\n      <!--/ Auto Suggestion List -->\r\n    </div>\r\n    <!--/ Bookmark Pop-up -->\r\n  </li>\r\n</ul>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
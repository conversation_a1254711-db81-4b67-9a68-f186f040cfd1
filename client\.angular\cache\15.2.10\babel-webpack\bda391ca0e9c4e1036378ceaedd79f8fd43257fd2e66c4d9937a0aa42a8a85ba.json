{"ast": null, "code": "import { Observable } from './Observable';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { SubjectSubscription } from './SubjectSubscription';\nimport { rxSubscriber as rxSubscriberSymbol } from '../internal/symbol/rxSubscriber';\nexport class SubjectSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n    this.destination = destination;\n  }\n}\nexport class Subject extends Observable {\n  constructor() {\n    super();\n    this.observers = [];\n    this.closed = false;\n    this.isStopped = false;\n    this.hasError = false;\n    this.thrownError = null;\n  }\n  [rxSubscriberSymbol]() {\n    return new SubjectSubscriber(this);\n  }\n  lift(operator) {\n    const subject = new AnonymousSubject(this, this);\n    subject.operator = operator;\n    return subject;\n  }\n  next(value) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n    if (!this.isStopped) {\n      const {\n        observers\n      } = this;\n      const len = observers.length;\n      const copy = observers.slice();\n      for (let i = 0; i < len; i++) {\n        copy[i].next(value);\n      }\n    }\n  }\n  error(err) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n    this.hasError = true;\n    this.thrownError = err;\n    this.isStopped = true;\n    const {\n      observers\n    } = this;\n    const len = observers.length;\n    const copy = observers.slice();\n    for (let i = 0; i < len; i++) {\n      copy[i].error(err);\n    }\n    this.observers.length = 0;\n  }\n  complete() {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n    this.isStopped = true;\n    const {\n      observers\n    } = this;\n    const len = observers.length;\n    const copy = observers.slice();\n    for (let i = 0; i < len; i++) {\n      copy[i].complete();\n    }\n    this.observers.length = 0;\n  }\n  unsubscribe() {\n    this.isStopped = true;\n    this.closed = true;\n    this.observers = null;\n  }\n  _trySubscribe(subscriber) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    } else {\n      return super._trySubscribe(subscriber);\n    }\n  }\n  _subscribe(subscriber) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    } else if (this.hasError) {\n      subscriber.error(this.thrownError);\n      return Subscription.EMPTY;\n    } else if (this.isStopped) {\n      subscriber.complete();\n      return Subscription.EMPTY;\n    } else {\n      this.observers.push(subscriber);\n      return new SubjectSubscription(this, subscriber);\n    }\n  }\n  asObservable() {\n    const observable = new Observable();\n    observable.source = this;\n    return observable;\n  }\n}\nSubject.create = (destination, source) => {\n  return new AnonymousSubject(destination, source);\n};\nexport class AnonymousSubject extends Subject {\n  constructor(destination, source) {\n    super();\n    this.destination = destination;\n    this.source = source;\n  }\n  next(value) {\n    const {\n      destination\n    } = this;\n    if (destination && destination.next) {\n      destination.next(value);\n    }\n  }\n  error(err) {\n    const {\n      destination\n    } = this;\n    if (destination && destination.error) {\n      this.destination.error(err);\n    }\n  }\n  complete() {\n    const {\n      destination\n    } = this;\n    if (destination && destination.complete) {\n      this.destination.complete();\n    }\n  }\n  _subscribe(subscriber) {\n    const {\n      source\n    } = this;\n    if (source) {\n      return this.source.subscribe(subscriber);\n    } else {\n      return Subscription.EMPTY;\n    }\n  }\n}", "map": {"version": 3, "names": ["Observable", "Subscriber", "Subscription", "ObjectUnsubscribedError", "SubjectSubscription", "rxSubscriber", "rxSubscriberSymbol", "SubjectSubscriber", "constructor", "destination", "Subject", "observers", "closed", "isStopped", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "lift", "operator", "subject", "AnonymousSubject", "next", "value", "len", "length", "copy", "slice", "i", "error", "err", "complete", "unsubscribe", "_trySubscribe", "subscriber", "_subscribe", "EMPTY", "push", "asObservable", "observable", "source", "create", "subscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/Subject.js"], "sourcesContent": ["import { Observable } from './Observable';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { SubjectSubscription } from './SubjectSubscription';\nimport { rxSubscriber as rxSubscriberSymbol } from '../internal/symbol/rxSubscriber';\nexport class SubjectSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n        this.destination = destination;\n    }\n}\nexport class Subject extends Observable {\n    constructor() {\n        super();\n        this.observers = [];\n        this.closed = false;\n        this.isStopped = false;\n        this.hasError = false;\n        this.thrownError = null;\n    }\n    [rxSubscriberSymbol]() {\n        return new SubjectSubscriber(this);\n    }\n    lift(operator) {\n        const subject = new AnonymousSubject(this, this);\n        subject.operator = operator;\n        return subject;\n    }\n    next(value) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        if (!this.isStopped) {\n            const { observers } = this;\n            const len = observers.length;\n            const copy = observers.slice();\n            for (let i = 0; i < len; i++) {\n                copy[i].next(value);\n            }\n        }\n    }\n    error(err) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        this.hasError = true;\n        this.thrownError = err;\n        this.isStopped = true;\n        const { observers } = this;\n        const len = observers.length;\n        const copy = observers.slice();\n        for (let i = 0; i < len; i++) {\n            copy[i].error(err);\n        }\n        this.observers.length = 0;\n    }\n    complete() {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        this.isStopped = true;\n        const { observers } = this;\n        const len = observers.length;\n        const copy = observers.slice();\n        for (let i = 0; i < len; i++) {\n            copy[i].complete();\n        }\n        this.observers.length = 0;\n    }\n    unsubscribe() {\n        this.isStopped = true;\n        this.closed = true;\n        this.observers = null;\n    }\n    _trySubscribe(subscriber) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        else {\n            return super._trySubscribe(subscriber);\n        }\n    }\n    _subscribe(subscriber) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        else if (this.hasError) {\n            subscriber.error(this.thrownError);\n            return Subscription.EMPTY;\n        }\n        else if (this.isStopped) {\n            subscriber.complete();\n            return Subscription.EMPTY;\n        }\n        else {\n            this.observers.push(subscriber);\n            return new SubjectSubscription(this, subscriber);\n        }\n    }\n    asObservable() {\n        const observable = new Observable();\n        observable.source = this;\n        return observable;\n    }\n}\nSubject.create = (destination, source) => {\n    return new AnonymousSubject(destination, source);\n};\nexport class AnonymousSubject extends Subject {\n    constructor(destination, source) {\n        super();\n        this.destination = destination;\n        this.source = source;\n    }\n    next(value) {\n        const { destination } = this;\n        if (destination && destination.next) {\n            destination.next(value);\n        }\n    }\n    error(err) {\n        const { destination } = this;\n        if (destination && destination.error) {\n            this.destination.error(err);\n        }\n    }\n    complete() {\n        const { destination } = this;\n        if (destination && destination.complete) {\n            this.destination.complete();\n        }\n    }\n    _subscribe(subscriber) {\n        const { source } = this;\n        if (source) {\n            return this.source.subscribe(subscriber);\n        }\n        else {\n            return Subscription.EMPTY;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,IAAIC,kBAAkB,QAAQ,iCAAiC;AACpF,OAAO,MAAMC,iBAAiB,SAASN,UAAU,CAAC;EAC9CO,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA,OAAO,MAAMC,OAAO,SAASV,UAAU,CAAC;EACpCQ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACG,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACA,CAACT,kBAAkB,IAAI;IACnB,OAAO,IAAIC,iBAAiB,CAAC,IAAI,CAAC;EACtC;EACAS,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,OAAO,GAAG,IAAIC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IAChDD,OAAO,CAACD,QAAQ,GAAGA,QAAQ;IAC3B,OAAOC,OAAO;EAClB;EACAE,IAAIA,CAACC,KAAK,EAAE;IACR,IAAI,IAAI,CAACT,MAAM,EAAE;MACb,MAAM,IAAIT,uBAAuB,CAAC,CAAC;IACvC;IACA,IAAI,CAAC,IAAI,CAACU,SAAS,EAAE;MACjB,MAAM;QAAEF;MAAU,CAAC,GAAG,IAAI;MAC1B,MAAMW,GAAG,GAAGX,SAAS,CAACY,MAAM;MAC5B,MAAMC,IAAI,GAAGb,SAAS,CAACc,KAAK,CAAC,CAAC;MAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAE;QAC1BF,IAAI,CAACE,CAAC,CAAC,CAACN,IAAI,CAACC,KAAK,CAAC;MACvB;IACJ;EACJ;EACAM,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,IAAI,CAAChB,MAAM,EAAE;MACb,MAAM,IAAIT,uBAAuB,CAAC,CAAC;IACvC;IACA,IAAI,CAACW,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,WAAW,GAAGa,GAAG;IACtB,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,MAAM;MAAEF;IAAU,CAAC,GAAG,IAAI;IAC1B,MAAMW,GAAG,GAAGX,SAAS,CAACY,MAAM;IAC5B,MAAMC,IAAI,GAAGb,SAAS,CAACc,KAAK,CAAC,CAAC;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAE;MAC1BF,IAAI,CAACE,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,CAAC;IACtB;IACA,IAAI,CAACjB,SAAS,CAACY,MAAM,GAAG,CAAC;EAC7B;EACAM,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACjB,MAAM,EAAE;MACb,MAAM,IAAIT,uBAAuB,CAAC,CAAC;IACvC;IACA,IAAI,CAACU,SAAS,GAAG,IAAI;IACrB,MAAM;MAAEF;IAAU,CAAC,GAAG,IAAI;IAC1B,MAAMW,GAAG,GAAGX,SAAS,CAACY,MAAM;IAC5B,MAAMC,IAAI,GAAGb,SAAS,CAACc,KAAK,CAAC,CAAC;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAE;MAC1BF,IAAI,CAACE,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC;IACtB;IACA,IAAI,CAAClB,SAAS,CAACY,MAAM,GAAG,CAAC;EAC7B;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACD,MAAM,GAAG,IAAI;IAClB,IAAI,CAACD,SAAS,GAAG,IAAI;EACzB;EACAoB,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,IAAI,CAACpB,MAAM,EAAE;MACb,MAAM,IAAIT,uBAAuB,CAAC,CAAC;IACvC,CAAC,MACI;MACD,OAAO,KAAK,CAAC4B,aAAa,CAACC,UAAU,CAAC;IAC1C;EACJ;EACAC,UAAUA,CAACD,UAAU,EAAE;IACnB,IAAI,IAAI,CAACpB,MAAM,EAAE;MACb,MAAM,IAAIT,uBAAuB,CAAC,CAAC;IACvC,CAAC,MACI,IAAI,IAAI,CAACW,QAAQ,EAAE;MACpBkB,UAAU,CAACL,KAAK,CAAC,IAAI,CAACZ,WAAW,CAAC;MAClC,OAAOb,YAAY,CAACgC,KAAK;IAC7B,CAAC,MACI,IAAI,IAAI,CAACrB,SAAS,EAAE;MACrBmB,UAAU,CAACH,QAAQ,CAAC,CAAC;MACrB,OAAO3B,YAAY,CAACgC,KAAK;IAC7B,CAAC,MACI;MACD,IAAI,CAACvB,SAAS,CAACwB,IAAI,CAACH,UAAU,CAAC;MAC/B,OAAO,IAAI5B,mBAAmB,CAAC,IAAI,EAAE4B,UAAU,CAAC;IACpD;EACJ;EACAI,YAAYA,CAAA,EAAG;IACX,MAAMC,UAAU,GAAG,IAAIrC,UAAU,CAAC,CAAC;IACnCqC,UAAU,CAACC,MAAM,GAAG,IAAI;IACxB,OAAOD,UAAU;EACrB;AACJ;AACA3B,OAAO,CAAC6B,MAAM,GAAG,CAAC9B,WAAW,EAAE6B,MAAM,KAAK;EACtC,OAAO,IAAInB,gBAAgB,CAACV,WAAW,EAAE6B,MAAM,CAAC;AACpD,CAAC;AACD,OAAO,MAAMnB,gBAAgB,SAAST,OAAO,CAAC;EAC1CF,WAAWA,CAACC,WAAW,EAAE6B,MAAM,EAAE;IAC7B,KAAK,CAAC,CAAC;IACP,IAAI,CAAC7B,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC6B,MAAM,GAAGA,MAAM;EACxB;EACAlB,IAAIA,CAACC,KAAK,EAAE;IACR,MAAM;MAAEZ;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAIA,WAAW,IAAIA,WAAW,CAACW,IAAI,EAAE;MACjCX,WAAW,CAACW,IAAI,CAACC,KAAK,CAAC;IAC3B;EACJ;EACAM,KAAKA,CAACC,GAAG,EAAE;IACP,MAAM;MAAEnB;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAIA,WAAW,IAAIA,WAAW,CAACkB,KAAK,EAAE;MAClC,IAAI,CAAClB,WAAW,CAACkB,KAAK,CAACC,GAAG,CAAC;IAC/B;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEpB;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAIA,WAAW,IAAIA,WAAW,CAACoB,QAAQ,EAAE;MACrC,IAAI,CAACpB,WAAW,CAACoB,QAAQ,CAAC,CAAC;IAC/B;EACJ;EACAI,UAAUA,CAACD,UAAU,EAAE;IACnB,MAAM;MAAEM;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,EAAE;MACR,OAAO,IAAI,CAACA,MAAM,CAACE,SAAS,CAACR,UAAU,CAAC;IAC5C,CAAC,MACI;MACD,OAAO9B,YAAY,CAACgC,KAAK;IAC7B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
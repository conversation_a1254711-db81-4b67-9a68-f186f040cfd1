{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isScheduler } from '../util/isScheduler';\nimport { mergeAll } from '../operators/mergeAll';\nimport { fromArray } from './fromArray';\nexport function merge(...observables) {\n  let concurrent = Number.POSITIVE_INFINITY;\n  let scheduler = null;\n  let last = observables[observables.length - 1];\n  if (isScheduler(last)) {\n    scheduler = observables.pop();\n    if (observables.length > 1 && typeof observables[observables.length - 1] === 'number') {\n      concurrent = observables.pop();\n    }\n  } else if (typeof last === 'number') {\n    concurrent = observables.pop();\n  }\n  if (scheduler === null && observables.length === 1 && observables[0] instanceof Observable) {\n    return observables[0];\n  }\n  return mergeAll(concurrent)(fromArray(observables, scheduler));\n}", "map": {"version": 3, "names": ["Observable", "isScheduler", "mergeAll", "fromArray", "merge", "observables", "concurrent", "Number", "POSITIVE_INFINITY", "scheduler", "last", "length", "pop"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/merge.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isScheduler } from '../util/isScheduler';\nimport { mergeAll } from '../operators/mergeAll';\nimport { fromArray } from './fromArray';\nexport function merge(...observables) {\n    let concurrent = Number.POSITIVE_INFINITY;\n    let scheduler = null;\n    let last = observables[observables.length - 1];\n    if (isScheduler(last)) {\n        scheduler = observables.pop();\n        if (observables.length > 1 && typeof observables[observables.length - 1] === 'number') {\n            concurrent = observables.pop();\n        }\n    }\n    else if (typeof last === 'number') {\n        concurrent = observables.pop();\n    }\n    if (scheduler === null && observables.length === 1 && observables[0] instanceof Observable) {\n        return observables[0];\n    }\n    return mergeAll(concurrent)(fromArray(observables, scheduler));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,KAAKA,CAAC,GAAGC,WAAW,EAAE;EAClC,IAAIC,UAAU,GAAGC,MAAM,CAACC,iBAAiB;EACzC,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,IAAI,GAAGL,WAAW,CAACA,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC;EAC9C,IAAIV,WAAW,CAACS,IAAI,CAAC,EAAE;IACnBD,SAAS,GAAGJ,WAAW,CAACO,GAAG,CAAC,CAAC;IAC7B,IAAIP,WAAW,CAACM,MAAM,GAAG,CAAC,IAAI,OAAON,WAAW,CAACA,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnFL,UAAU,GAAGD,WAAW,CAACO,GAAG,CAAC,CAAC;IAClC;EACJ,CAAC,MACI,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC/BJ,UAAU,GAAGD,WAAW,CAACO,GAAG,CAAC,CAAC;EAClC;EACA,IAAIH,SAAS,KAAK,IAAI,IAAIJ,WAAW,CAACM,MAAM,KAAK,CAAC,IAAIN,WAAW,CAAC,CAAC,CAAC,YAAYL,UAAU,EAAE;IACxF,OAAOK,WAAW,CAAC,CAAC,CAAC;EACzB;EACA,OAAOH,QAAQ,CAACI,UAAU,CAAC,CAACH,SAAS,CAACE,WAAW,EAAEI,SAAS,CAAC,CAAC;AAClE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
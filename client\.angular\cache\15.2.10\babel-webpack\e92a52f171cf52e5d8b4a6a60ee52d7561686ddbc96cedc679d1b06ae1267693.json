{"ast": null, "code": "export function isDate(value) {\n  return value instanceof Date && !isNaN(+value);\n}", "map": {"version": 3, "names": ["isDate", "value", "Date", "isNaN"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/isDate.js"], "sourcesContent": ["export function isDate(value) {\n    return value instanceof Date && !isNaN(+value);\n}\n"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAACC,KAAK,EAAE;EAC1B,OAAOA,KAAK,YAAYC,IAAI,IAAI,CAACC,KAAK,CAAC,CAACF,KAAK,CAAC;AAClD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
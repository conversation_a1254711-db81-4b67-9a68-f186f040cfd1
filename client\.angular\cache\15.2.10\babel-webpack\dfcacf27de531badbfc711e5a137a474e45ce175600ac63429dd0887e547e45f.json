{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function withLatestFrom(...args) {\n  return source => {\n    let project;\n    if (typeof args[args.length - 1] === 'function') {\n      project = args.pop();\n    }\n    const observables = args;\n    return source.lift(new WithLatestFromOperator(observables, project));\n  };\n}\nclass WithLatestFromOperator {\n  constructor(observables, project) {\n    this.observables = observables;\n    this.project = project;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new WithLatestFromSubscriber(subscriber, this.observables, this.project));\n  }\n}\nclass WithLatestFromSubscriber extends OuterSubscriber {\n  constructor(destination, observables, project) {\n    super(destination);\n    this.observables = observables;\n    this.project = project;\n    this.toRespond = [];\n    const len = observables.length;\n    this.values = new Array(len);\n    for (let i = 0; i < len; i++) {\n      this.toRespond.push(i);\n    }\n    for (let i = 0; i < len; i++) {\n      let observable = observables[i];\n      this.add(subscribeToResult(this, observable, observable, i));\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.values[outerIndex] = innerValue;\n    const toRespond = this.toRespond;\n    if (toRespond.length > 0) {\n      const found = toRespond.indexOf(outerIndex);\n      if (found !== -1) {\n        toRespond.splice(found, 1);\n      }\n    }\n  }\n  notifyComplete() {}\n  _next(value) {\n    if (this.toRespond.length === 0) {\n      const args = [value, ...this.values];\n      if (this.project) {\n        this._tryProject(args);\n      } else {\n        this.destination.next(args);\n      }\n    }\n  }\n  _tryProject(args) {\n    let result;\n    try {\n      result = this.project.apply(this, args);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n    this.destination.next(result);\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "subscribeToResult", "withLatestFrom", "args", "source", "project", "length", "pop", "observables", "lift", "WithLatestFromOperator", "constructor", "call", "subscriber", "subscribe", "WithLatestFromSubscriber", "destination", "toRespond", "len", "values", "Array", "i", "push", "observable", "add", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "found", "indexOf", "splice", "notifyComplete", "_next", "value", "_tryProject", "next", "result", "apply", "err", "error"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/withLatestFrom.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function withLatestFrom(...args) {\n    return (source) => {\n        let project;\n        if (typeof args[args.length - 1] === 'function') {\n            project = args.pop();\n        }\n        const observables = args;\n        return source.lift(new WithLatestFromOperator(observables, project));\n    };\n}\nclass WithLatestFromOperator {\n    constructor(observables, project) {\n        this.observables = observables;\n        this.project = project;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WithLatestFromSubscriber(subscriber, this.observables, this.project));\n    }\n}\nclass WithLatestFromSubscriber extends OuterSubscriber {\n    constructor(destination, observables, project) {\n        super(destination);\n        this.observables = observables;\n        this.project = project;\n        this.toRespond = [];\n        const len = observables.length;\n        this.values = new Array(len);\n        for (let i = 0; i < len; i++) {\n            this.toRespond.push(i);\n        }\n        for (let i = 0; i < len; i++) {\n            let observable = observables[i];\n            this.add(subscribeToResult(this, observable, observable, i));\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.values[outerIndex] = innerValue;\n        const toRespond = this.toRespond;\n        if (toRespond.length > 0) {\n            const found = toRespond.indexOf(outerIndex);\n            if (found !== -1) {\n                toRespond.splice(found, 1);\n            }\n        }\n    }\n    notifyComplete() {\n    }\n    _next(value) {\n        if (this.toRespond.length === 0) {\n            const args = [value, ...this.values];\n            if (this.project) {\n                this._tryProject(args);\n            }\n            else {\n                this.destination.next(args);\n            }\n        }\n    }\n    _tryProject(args) {\n        let result;\n        try {\n            result = this.project.apply(this, args);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.next(result);\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,cAAcA,CAAC,GAAGC,IAAI,EAAE;EACpC,OAAQC,MAAM,IAAK;IACf,IAAIC,OAAO;IACX,IAAI,OAAOF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;MAC7CD,OAAO,GAAGF,IAAI,CAACI,GAAG,CAAC,CAAC;IACxB;IACA,MAAMC,WAAW,GAAGL,IAAI;IACxB,OAAOC,MAAM,CAACK,IAAI,CAAC,IAAIC,sBAAsB,CAACF,WAAW,EAAEH,OAAO,CAAC,CAAC;EACxE,CAAC;AACL;AACA,MAAMK,sBAAsB,CAAC;EACzBC,WAAWA,CAACH,WAAW,EAAEH,OAAO,EAAE;IAC9B,IAAI,CAACG,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACH,OAAO,GAAGA,OAAO;EAC1B;EACAO,IAAIA,CAACC,UAAU,EAAET,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACU,SAAS,CAAC,IAAIC,wBAAwB,CAACF,UAAU,EAAE,IAAI,CAACL,WAAW,EAAE,IAAI,CAACH,OAAO,CAAC,CAAC;EACrG;AACJ;AACA,MAAMU,wBAAwB,SAASf,eAAe,CAAC;EACnDW,WAAWA,CAACK,WAAW,EAAER,WAAW,EAAEH,OAAO,EAAE;IAC3C,KAAK,CAACW,WAAW,CAAC;IAClB,IAAI,CAACR,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACY,SAAS,GAAG,EAAE;IACnB,MAAMC,GAAG,GAAGV,WAAW,CAACF,MAAM;IAC9B,IAAI,CAACa,MAAM,GAAG,IAAIC,KAAK,CAACF,GAAG,CAAC;IAC5B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACJ,SAAS,CAACK,IAAI,CAACD,CAAC,CAAC;IAC1B;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAIE,UAAU,GAAGf,WAAW,CAACa,CAAC,CAAC;MAC/B,IAAI,CAACG,GAAG,CAACvB,iBAAiB,CAAC,IAAI,EAAEsB,UAAU,EAAEA,UAAU,EAAEF,CAAC,CAAC,CAAC;IAChE;EACJ;EACAI,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACX,MAAM,CAACS,UAAU,CAAC,GAAGD,UAAU;IACpC,MAAMV,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,CAACX,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMyB,KAAK,GAAGd,SAAS,CAACe,OAAO,CAACJ,UAAU,CAAC;MAC3C,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;QACdd,SAAS,CAACgB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAG,cAAcA,CAAA,EAAG,CACjB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,IAAI,CAACnB,SAAS,CAACX,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAMH,IAAI,GAAG,CAACiC,KAAK,EAAE,GAAG,IAAI,CAACjB,MAAM,CAAC;MACpC,IAAI,IAAI,CAACd,OAAO,EAAE;QACd,IAAI,CAACgC,WAAW,CAAClC,IAAI,CAAC;MAC1B,CAAC,MACI;QACD,IAAI,CAACa,WAAW,CAACsB,IAAI,CAACnC,IAAI,CAAC;MAC/B;IACJ;EACJ;EACAkC,WAAWA,CAAClC,IAAI,EAAE;IACd,IAAIoC,MAAM;IACV,IAAI;MACAA,MAAM,GAAG,IAAI,CAAClC,OAAO,CAACmC,KAAK,CAAC,IAAI,EAAErC,IAAI,CAAC;IAC3C,CAAC,CACD,OAAOsC,GAAG,EAAE;MACR,IAAI,CAACzB,WAAW,CAAC0B,KAAK,CAACD,GAAG,CAAC;MAC3B;IACJ;IACA,IAAI,CAACzB,WAAW,CAACsB,IAAI,CAACC,MAAM,CAAC;EACjC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
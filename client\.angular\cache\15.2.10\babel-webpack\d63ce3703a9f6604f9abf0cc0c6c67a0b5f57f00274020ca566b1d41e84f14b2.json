{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormGroup } from '@angular/forms';\nimport { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport { Directory, Encoding, Filesystem } from '@capacitor/filesystem';\nimport { Share } from '@capacitor/share';\nimport { Capacitor } from '@capacitor/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/season.service\";\nimport * as i6 from \"app/services/commons.service\";\nimport * as i7 from \"app/services/registration.service\";\nimport * as i8 from \"@angular/platform-browser\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"app/services/club.service\";\nimport * as i11 from \"app/services/tournament.service\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i15 from \"app/layout/components/content-header/content-header.component\";\nimport * as i16 from \"angular-datatables\";\nimport * as i17 from \"@ng-select/ng-select\";\nimport * as i18 from \"@ngx-formly/core\";\nconst _c0 = [\"rowActionBtn\"];\nconst _c1 = [\"modalForm\"];\nfunction TournamentSelectionComponent_ng_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", season_r4.name, \" \");\n  }\n}\nfunction TournamentSelectionComponent_ng_container_8_div_4_ng_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r8.name, \" \");\n  }\n}\nfunction TournamentSelectionComponent_ng_container_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20)(3, \"div\", 21)(4, \"ng-select\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function TournamentSelectionComponent_ng_container_8_div_4_Template_ng_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.group_id = $event);\n    })(\"change\", function TournamentSelectionComponent_ng_container_8_div_4_Template_ng_select_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onSelectedGroupChange($event));\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementStart(6, \"ng-option\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, TournamentSelectionComponent_ng_container_8_div_4_ng_option_9_Template, 2, 2, \"ng-option\", 24);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 6, \"Select Group\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.group_id)(\"clearable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 8, \"All\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.currentGroups);\n  }\n}\nfunction TournamentSelectionComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TournamentSelectionComponent_ng_container_8_Template_button_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const variable_r5 = restoredCtx.ngIf;\n      return i0.ɵɵresetView(variable_r5.isShowFilter = !variable_r5.isShowFilter);\n    });\n    i0.ɵɵelement(3, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, TournamentSelectionComponent_ng_container_8_div_4_Template, 10, 10, \"div\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const variable_r5 = ctx.ngIf;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", variable_r5.isShowFilter);\n  }\n}\nfunction TournamentSelectionComponent_ng_template_13_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.errorMessage, \" \");\n  }\n}\nfunction TournamentSelectionComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"h4\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function TournamentSelectionComponent_ng_template_13_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const modal_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r14.close(\"Cross click\"));\n    });\n    i0.ɵɵelementStart(5, \"span\", 29);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"form\", 31);\n    i0.ɵɵlistener(\"ngSubmit\", function TournamentSelectionComponent_ng_template_13_Template_form_ngSubmit_10_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onSubmit());\n    });\n    i0.ɵɵelementStart(11, \"div\", 32);\n    i0.ɵɵelement(12, \"formly-form\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 34);\n    i0.ɵɵtemplate(14, TournamentSelectionComponent_ng_template_13_div_14_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 36)(16, \"button\", 37);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 8, \"Add fixtures to calendar\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 10, \"Select Teams or Tournaments to receive fixtures and other relevant messages to your calendars.\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.form);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"form\", ctx_r3.form)(\"fields\", ctx_r3.fields)(\"model\", ctx_r3.model);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.errorMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 12, \"Submit\"), \" \");\n  }\n}\nconst _c2 = function () {\n  return {\n    isShowFilter: true\n  };\n};\nexport class TournamentSelectionComponent {\n  constructor(_router, _http, _loading, renderer, _trans, _seasonService, _commonsService, _registrationService, _titleService, _modalService, _clubService, _tournamentService) {\n    this._router = _router;\n    this._http = _http;\n    this._loading = _loading;\n    this.renderer = renderer;\n    this._trans = _trans;\n    this._seasonService = _seasonService;\n    this._commonsService = _commonsService;\n    this._registrationService = _registrationService;\n    this._titleService = _titleService;\n    this._modalService = _modalService;\n    this._clubService = _clubService;\n    this._tournamentService = _tournamentService;\n    this.dtElement = DataTableDirective;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.tableID = 'select-tournament-table';\n    this.isInitTable = false;\n    this.contentHeader = {};\n    this.tournaments = [];\n    this.group_id = '';\n    this.model = {};\n    this.form = new FormGroup({});\n    this.errorMessage = '';\n    this.fields = [{\n      key: 'tournament_ids',\n      type: 'ng-select',\n      props: {\n        label: this._trans.instant('Select Tournament'),\n        multiple: true,\n        options: []\n      }\n    }, {\n      key: 'club_ids',\n      type: 'ng-select',\n      props: {\n        label: this._trans.instant('Select Club'),\n        multiple: true,\n        options: []\n      }\n    }];\n    this._titleService.setTitle(`${this._trans.instant('Fixtures')} & ${this._trans.instant('Results')}`);\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Competitions'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Competitions'),\n          isLink: false\n        }]\n      }\n    };\n  }\n  ngOnInit() {\n    this.getCurrentSeason();\n    this.getClubActive();\n  }\n  buildTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom_table_card,\n      ajax: (dataTablesParameters, callback) => {\n        this._http.get(`${environment.apiUrl}/seasons/${this.season_id}/tournaments?is_released=true`, dataTablesParameters).subscribe(resp => {\n          this._commonsService.customSearchInput(this.tableID);\n          this.currentGroups = resp.options.groups;\n          let tournamentSelection = this.tournamentSelection;\n          if (!tournamentSelection?.selectedGroup) {\n            let data = {\n              selectedGroup: ''\n            };\n            this.tournamentSelection = data;\n          }\n          // check if selectedSeason is not in currentSeasons then set selectedSeason = currentSeasons[0]\n          else if (!this.currentGroups.find(group => {\n            return group.id == tournamentSelection.selectedGroup;\n          })) {\n            let data = {\n              selectedGroup: ''\n            };\n            this.tournamentSelection = data;\n          }\n          this.group_id = this.tournamentSelection.selectedGroup;\n          this.onSelectedGroupChange(this.group_id);\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      stateSave: false,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      responsive: true,\n      scrollX: false,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        ...{\n          search: '',\n          searchPlaceholder: 'Search',\n          attr: {\n            class: 'w-100'\n          }\n        }\n      },\n      createdRow: function (row, data, dataIndex, cells) {\n        $(row).addClass('col-12 col-md-6 col-lg-4 p-0');\n      },\n      columnDefs: [\n        // { responsivePriority: 1, targets: -1 },\n        // { responsivePriority: 2, targets: 2 },\n      ],\n      order: [[2, 'asc']],\n      rowGroup: {\n        dataSrc: 'group.name'\n      },\n      columns: [{\n        data: 'group_id',\n        visible: false\n      }, {\n        data: 'name',\n        className: 'h3 fw-bold d-flex py-2'\n      },\n      // {\n      //   data: 'group.name',\n      //   className: 'd-flex',\n      //   type: 'any-number',\n      // },\n      {\n        data: 'id',\n        className: 'd-block pb-2',\n        translate: true,\n        render: (data, type, row, meta) => {\n          return `\n            <div class=\"pt-50 pb-50\">\n              <button class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-primary btn-block\" tournament-id=\"${data}\">\n                ${this._trans.instant('Select Tournament')}\n              </button>\n            </div>\n            `;\n        }\n      }],\n      lengthMenu: [[25, 50, 100, -1], [25, 50, 100, 'All']],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom_table_card,\n        buttons: [{\n          text: '<i class=\"bi bi-calendar-event\"></i> ' + this._trans.instant('Add fixtures to calendar'),\n          action: () => this.modalOpenForm()\n        }]\n      }\n    };\n  }\n  onSelectedGroupChange($event) {\n    this.tournamentSelection = {\n      selectedGroup: this.group_id\n    };\n    this.dtElement.dtInstance.then(dtInstance => {\n      // filter by group_id\n      let str_filter = this.group_id ? `^\\\\b${this.group_id}\\\\b$` : '';\n      dtInstance.column(0).search(str_filter, true, false).draw();\n    });\n  }\n  getCurrentSeason() {\n    this._loading.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.currentSeasons = data;\n      // // if not exist selectedSeason tableManageLeague then set default, else get from tableManageLeague\n      let tournamentSelection = this.tournamentSelection;\n      if (!tournamentSelection?.selectedSeason) {\n        let data = {\n          selectedSeason: this.currentSeasons[0].id\n        };\n        this.tournamentSelection = data;\n      }\n      // check if selectedSeason is not in currentSeasons then set selectedSeason = currentSeasons[0]\n      else if (!this.currentSeasons.find(season => {\n        return season.id == tournamentSelection.selectedSeason;\n      })) {\n        let data = {\n          selectedSeason: this.currentSeasons[0].id\n        };\n        this.tournamentSelection = data;\n      }\n      this.season_id = this.tournamentSelection.selectedSeason;\n      this.getTournamentsActive(this.season_id);\n      if (!this.isInitTable) {\n        this.isInitTable = true;\n        this.buildTable();\n        this.dtTrigger.next(this.dtOptions);\n      } else {\n        if (this.dtElement.dtInstance) {\n          this.dtElement.dtInstance.then(dtInstance => {\n            dtInstance.ajax.reload();\n          });\n        }\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: this._trans.instant(error.message),\n        icon: 'error',\n        confirmButtonText: this._trans.instant('OK')\n      });\n    });\n  }\n  onSelectedSeasonChange(event) {\n    this._loading.show();\n    this.dtElement.dtInstance.then(dtInstance => {\n      this.tournamentSelection = {\n        selectedSeason: this.season_id\n      };\n      dtInstance.ajax.reload();\n    });\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament-id')) {\n        let tournament_id = event.target.getAttribute('tournament-id');\n        // go to fixtures-results/:tournament_id\n        this._router.navigate(['/leagues/fixtures-results', tournament_id]);\n      }\n    });\n  }\n  modalOpenForm() {\n    this.modalRef = this._modalService.open(this.modalForm, {\n      size: 'md',\n      centered: true,\n      backdrop: 'static'\n    });\n    // when close modal reset form\n    this.modalRef.closed.subscribe(res => {\n      this.resetForm();\n    });\n    this.modalRef.dismissed.subscribe(res => {\n      this.resetForm();\n    });\n  }\n  resetForm() {\n    this.form.reset();\n    this.errorMessage = '';\n  }\n  getTournamentsActive(season_id) {\n    this._http.get(`${environment.apiUrl}/seasons/${season_id}/tournaments?is_released=true`).subscribe(data => {\n      console.log(data);\n      let tournaments = data.data.map(tournament => {\n        return {\n          label: tournament.name,\n          value: tournament.id\n        };\n      });\n      // find index of field key tournament_ids\n      let index = this.fields.findIndex(field => field.key == 'tournament_ids');\n      // set options for field tournament_ids\n      this.fields[index].props.options = tournaments;\n    });\n  }\n  getClubActive() {\n    this._clubService.getAllClubsIsActive().subscribe(data => {\n      let clubs = data.map(club => {\n        return {\n          label: club.name,\n          value: club.id\n        };\n      });\n      // find index of field key club_ids\n      let index = this.fields.findIndex(field => field.key == 'club_ids');\n      // set options for field club_ids\n      this.fields[index].props.options = clubs;\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    this.errorMessage = '';\n    if (this.form.invalid) {\n      return;\n    }\n    this._tournamentService.generateIcs(this.model).subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (res) {\n        if (res) {\n          const reader = new FileReader();\n          reader.onload = /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator(function* (event) {\n              const icsContent = event.target.result;\n              if (Capacitor.isNativePlatform()) {\n                try {\n                  // Write to file in a temporary or documents directory\n                  const writeFileResult = yield Filesystem.writeFile({\n                    path: 'fixtures.ics',\n                    data: icsContent,\n                    directory: Directory.Documents,\n                    encoding: Encoding.UTF8\n                  });\n                  // Use Share plugin to share the file with available apps\n                  yield Share.share({\n                    title: 'Share Calendar File',\n                    url: writeFileResult.uri,\n                    dialogTitle: 'Share Calendar Event'\n                  });\n                  alert('File downloaded successfully!');\n                } catch (e) {\n                  console.error('Error saving or sharing file:', e);\n                  _this.errorMessage = 'Error saving or sharing file.';\n                }\n              } else {\n                const blob = new Blob([icsContent], {\n                  type: 'text/calendar'\n                });\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = 'fixtures.ics';\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                window.URL.revokeObjectURL(url);\n              }\n            });\n            return function (_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }();\n          reader.readAsText(res);\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }(), error => {\n      if (error.status == 400) {\n        this.errorMessage = this._trans.instant('Please fill in all fields.');\n      } else {\n        this.errorMessage = this._trans.instant('No matching fixtures found.');\n      }\n      console.log(error);\n    });\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  ngOnDestroy() {\n    // Do not forget to unsubscribe the event\n    if (this.unlistener) {\n      this.unlistener();\n    }\n    this.dtTrigger.unsubscribe();\n  }\n  get tournamentSelection() {\n    let tournamentSelection = localStorage.getItem('tournament_selection');\n    return tournamentSelection ? JSON.parse(tournamentSelection) : null;\n  }\n  set tournamentSelection(data) {\n    let str_data = JSON.stringify(data);\n    // merge data if exist\n    let exist_data = this.tournamentSelection;\n    if (exist_data) {\n      str_data = JSON.stringify({\n        ...exist_data,\n        ...data\n      });\n    }\n    localStorage.setItem('tournament_selection', str_data);\n  }\n  static #_ = this.ɵfac = function TournamentSelectionComponent_Factory(t) {\n    return new (t || TournamentSelectionComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.SeasonService), i0.ɵɵdirectiveInject(i6.CommonsService), i0.ɵɵdirectiveInject(i7.RegistrationService), i0.ɵɵdirectiveInject(i8.Title), i0.ɵɵdirectiveInject(i9.NgbModal), i0.ɵɵdirectiveInject(i10.ClubService), i0.ɵɵdirectiveInject(i11.TournamentService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TournamentSelectionComponent,\n    selectors: [[\"app-tournament-selection\"]],\n    viewQuery: function TournamentSelectionComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowActionBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalForm = _t.first);\n      }\n    },\n    decls: 15,\n    vars: 13,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\", \"m-0\"], [1, \"col\", \"col-md-6\", \"col-lg-3\", \"mb-1\", \"pl-25\", \"pr-50\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"id\", \"fixtures-results\"], [\"datatable\", \"\", 1, \"card_table\", 3, \"dtOptions\", \"dtTrigger\", \"id\"], [1, \"row\", \"ml-0\", \"mr-0\"], [1, \"login-form\"], [\"modalForm\", \"\"], [3, \"value\"], [1, \"col-auto\", \"col-md\", \"pl-50\", \"pr-0\", \"d-flex\", \"justify-content-end\", \"align-items-start\"], [\"type\", \"button\", 1, \"btn\", \"btn-flat\", \"pl-25\", \"pr-25\", 3, \"click\"], [1, \"fa-light\", \"fa-filter-list\", \"fa-xl\", \"mr-25\"], [\"class\", \"col-12 col-md-auto p-25 mb-1\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-auto\", \"p-25\", \"mb-1\"], [1, \"row\", \"mr-0\"], [1, \"col\", \"pr-0\"], [2, \"min-width\", \"130px\"], [3, \"placeholder\", \"ngModel\", \"clearable\", \"ngModelChange\", \"change\"], [\"name\", \"all\", 3, \"value\"], [\"name\", \"group.id\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"name\", \"group.id\", 3, \"value\"], [1, \"modal-header\"], [\"id\", \"myModalLabel1\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\", 2, \"padding-bottom\", \"0px\"], [3, \"form\", \"fields\", \"model\"], [1, \"modal-body\", 2, \"padding-top\", \"0px\", \"padding-bottom\", \"0px\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"], [1, \"text-danger\"]],\n    template: function TournamentSelectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"ng-select\", 5);\n        i0.ɵɵlistener(\"ngModelChange\", function TournamentSelectionComponent_Template_ng_select_ngModelChange_5_listener($event) {\n          return ctx.season_id = $event;\n        })(\"change\", function TournamentSelectionComponent_Template_ng_select_change_5_listener($event) {\n          return ctx.onSelectedSeasonChange($event);\n        });\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵtemplate(7, TournamentSelectionComponent_ng_option_7_Template, 2, 2, \"ng-option\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, TournamentSelectionComponent_ng_container_8_Template, 5, 1, \"ng-container\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"section\", 8)(10, \"table\", 9);\n        i0.ɵɵelement(11, \"tbody\", 10);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(12, \"div\", 11);\n        i0.ɵɵtemplate(13, TournamentSelectionComponent_ng_template_13_Template, 19, 14, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(6, 10, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx.season_id);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.currentSeasons);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction0(12, _c2));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger)(\"id\", ctx.tableID);\n      }\n    },\n    dependencies: [i12.NgForOf, i12.NgIf, i13.ɵNgNoValidate, i13.NgControlStatus, i13.NgControlStatusGroup, i13.NgModel, i13.FormGroupDirective, i14.RippleEffectDirective, i15.ContentHeaderComponent, i16.DataTableDirective, i17.NgSelectComponent, i17.ɵr, i18.FormlyForm, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAEA,SAASA,SAAS,QAAQ,gBAAgB;AAM1C,SAASC,kBAAkB,QAAQ,oBAAoB;AAQvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,uBAAuB;AACvE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;ICZvBC,EAAA,CAAAC,cAAA,oBAAqE;IAAAD,EAAA,CAAAE,MAAA,GACrE;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADqCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAmB;IAACN,EAAA,CAAAO,SAAA,GACrE;IADqEP,EAAA,CAAAQ,kBAAA,KAAAH,SAAA,CAAAI,IAAA,MACrE;;;;;IAqBgBT,EAAA,CAAAC,cAAA,oBAAkF;IAC9ED,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAAJ,EAAA,CAAkB;IAC7DN,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAE,QAAA,CAAAD,IAAA,MACJ;;;;;;IAZpBT,EAAA,CAAAC,cAAA,cAAwE;IAKED,EAAA,CAAAW,UAAA,2BAAAC,8FAAAC,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAAF,MAAA,CAAAG,QAAA,GAAAN,MAAA;IAAA,EAAsB,oBAAAO,uFAAAP,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAArB,EAAA,CAAAiB,aAAA;MAAA,OAC9CjB,EAAA,CAAAkB,WAAA,CAAAG,OAAA,CAAAC,qBAAA,CAAAT,MAAA,CAA6B;IAAA,EADiB;;IAE5Eb,EAAA,CAAAC,cAAA,oBAAmC;IAC/BD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAuB,UAAA,IAAAC,sEAAA,wBAEY;IAChBxB,EAAA,CAAAG,YAAA,EAAY;;;;IARDH,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAyB,qBAAA,gBAAAzB,EAAA,CAAA0B,WAAA,uBAA8C;IAAC1B,EAAA,CAAAI,UAAA,YAAAuB,MAAA,CAAAR,QAAA,CAAsB;IAEjEnB,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,aAAY;IACnBJ,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA0B,WAAA,mBACJ;IAC6B1B,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAuB,MAAA,CAAAC,aAAA,CAAgB;;;;;;IAlBrE5B,EAAA,CAAA6B,uBAAA,GAAwD;IACpD7B,EAAA,CAAAC,cAAA,cAAqF;IAE7ED,EAAA,CAAAW,UAAA,mBAAAmB,6EAAA;MAAA,MAAAC,WAAA,GAAA/B,EAAA,CAAAc,aAAA,CAAAkB,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,IAAA;MAAA,OAAWlC,EAAA,CAAAkB,WAAA,CAAAe,WAAA,CAAAE,YAAA,IAAAF,WAAA,CAAAE,YAAA,CAA8C;IAAA;IACzDnC,EAAA,CAAAoC,SAAA,YAAmD;IACvDpC,EAAA,CAAAG,YAAA,EAAS;IAGbH,EAAA,CAAAuB,UAAA,IAAAc,0DAAA,oBAiBM;IACVrC,EAAA,CAAAsC,qBAAA,EAAe;;;;IAlBgCtC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAA6B,WAAA,CAAAE,YAAA,CAA2B;;;;;IA8CtEnC,EAAA,CAAAC,cAAA,cAA+C;IAC3CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAA+B,OAAA,CAAAC,YAAA,MACJ;;;;;;IAhBRxC,EAAA,CAAAC,cAAA,cAA0B;IACqBD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1FH,EAAA,CAAAC,cAAA,iBAA4F;IAAxDD,EAAA,CAAAW,UAAA,mBAAA8B,6EAAA;MAAA,MAAAV,WAAA,GAAA/B,EAAA,CAAAc,aAAA,CAAA4B,IAAA;MAAA,MAAAC,SAAA,GAAAZ,WAAA,CAAAa,SAAA;MAAA,OAAS5C,EAAA,CAAAkB,WAAA,CAAAyB,SAAA,CAAAE,KAAA,CAAY,aAAa,CAAC;IAAA,EAAC;IACpE7C,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,aAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAiD;IAAxBD,EAAA,CAAAW,UAAA,sBAAAmC,+EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAA4B,IAAA;MAAA,MAAAK,OAAA,GAAA/C,EAAA,CAAAiB,aAAA;MAAA,OAAYjB,EAAA,CAAAkB,WAAA,CAAA6B,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC5ChD,EAAA,CAAAC,cAAA,eAA8E;IAC1ED,EAAA,CAAAoC,SAAA,uBAA2E;IAC/EpC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAsE;IAClED,EAAA,CAAAuB,UAAA,KAAA0B,2DAAA,kBAEM;IACVjD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IAElBD,EAAA,CAAAE,MAAA,IACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IApB8BH,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAkD,iBAAA,CAAAlD,EAAA,CAAA0B,WAAA,mCAA0C;IAMrF1B,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA0B,WAAA,+GACJ;IACM1B,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,cAAA+C,MAAA,CAAAC,IAAA,CAAkB;IAEHpD,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAI,UAAA,SAAA+C,MAAA,CAAAC,IAAA,CAAa,WAAAD,MAAA,CAAAE,MAAA,WAAAF,MAAA,CAAAG,KAAA;IAGpBtD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAA+C,MAAA,CAAAX,YAAA,CAAkB;IAMpBxC,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA0B,WAAA,wBACJ;;;;;;;;AD9ChB,OAAM,MAAO6B,4BAA4B;EAqBvCC,YACSC,OAAe,EACfC,KAAiB,EACjBC,QAAwB,EACxBC,QAAmB,EACnBC,MAAwB,EACxBC,cAA6B,EAC7BC,eAA+B,EAC/BC,oBAAyC,EACzCC,aAAoB,EACpBC,aAAuB,EACvBC,YAAyB,EACzBC,kBAAqC;IAXrC,KAAAX,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA9B3B,KAAAC,SAAS,GAAQ9E,kBAAkB;IAEnC,KAAA+E,SAAS,GAAyB,IAAI7E,OAAO,EAAe;IAC5D,KAAA8E,SAAS,GAAQ,EAAE;IACnB,KAAAC,OAAO,GAAW,yBAAyB;IAC3C,KAAAC,WAAW,GAAY,KAAK;IAE5B,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,WAAW,GAAG,EAAE;IAEhB,KAAAxD,QAAQ,GAAG,EAAE;IAGb,KAAAmC,KAAK,GAAQ,EAAE;IACf,KAAAF,IAAI,GAAG,IAAI9D,SAAS,CAAC,EAAE,CAAC;IACxB,KAAAkD,YAAY,GAAW,EAAE;IA+OzB,KAAAa,MAAM,GAAwB,CAC5B;MACEuB,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,mBAAmB,CAAC;QAC/CC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;;KAEZ,EACD;MACEN,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,aAAa,CAAC;QACzCC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;;KAEZ,CACF;IAjPC,IAAI,CAACjB,aAAa,CAACkB,QAAQ,CACzB,GAAG,IAAI,CAACtB,MAAM,CAACmB,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,CAACnB,MAAM,CAACmB,OAAO,CAAC,SAAS,CAAC,EAAE,CACzE;IACD,IAAI,CAACN,aAAa,GAAG;MACnBU,WAAW,EAAE,IAAI,CAACvB,MAAM,CAACmB,OAAO,CAAC,cAAc,CAAC;MAChDK,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVT,IAAI,EAAE,EAAE;QACRU,KAAK,EAAE,CACL;UACE9E,IAAI,EAAE,IAAI,CAACoD,MAAM,CAACmB,OAAO,CAAC,cAAc,CAAC;UACzCQ,MAAM,EAAE;SACT;;KAGN;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACrB,SAAS,GAAG;MACfsB,GAAG,EAAE,IAAI,CAAC9B,eAAe,CAAC+B,iBAAiB,CAACC,cAAc;MAC1DC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAI,CAACxC,KAAK,CACPyC,GAAG,CACF,GAAG3G,WAAW,CAAC4G,MAAM,YAAY,IAAI,CAACC,SAAS,+BAA+B,EAC9EJ,oBAAoB,CACrB,CACAK,SAAS,CAAEC,IAAS,IAAI;UACvB,IAAI,CAACxC,eAAe,CAACyC,iBAAiB,CAAC,IAAI,CAAChC,OAAO,CAAC;UACpD,IAAI,CAAC5C,aAAa,GAAG2E,IAAI,CAACrB,OAAO,CAACuB,MAAM;UAExC,IAAIC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB;UAClD,IAAI,CAACA,mBAAmB,EAAEC,aAAa,EAAE;YACvC,IAAIC,IAAI,GAAG;cACTD,aAAa,EAAE;aAChB;YACD,IAAI,CAACD,mBAAmB,GAAGE,IAAI;;UAEjC;UAAA,KACK,IACH,CAAC,IAAI,CAAChF,aAAa,CAACiF,IAAI,CAAEC,KAAK,IAAI;YACjC,OAAOA,KAAK,CAACxG,EAAE,IAAIoG,mBAAmB,CAACC,aAAa;UACtD,CAAC,CAAC,EACF;YACA,IAAIC,IAAI,GAAG;cACTD,aAAa,EAAE;aAChB;YACD,IAAI,CAACD,mBAAmB,GAAGE,IAAI;;UAGjC,IAAI,CAACzF,QAAQ,GAAG,IAAI,CAACuF,mBAAmB,CAACC,aAAa;UACtD,IAAI,CAACrF,qBAAqB,CAAC,IAAI,CAACH,QAAQ,CAAC;UACzC+E,QAAQ,CAAC;YACP;YACAa,YAAY,EAAER,IAAI,CAACQ,YAAY;YAC/BC,eAAe,EAAET,IAAI,CAACS,eAAe;YACrCJ,IAAI,EAAEL,IAAI,CAACK;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDK,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;QACR,GAAG,IAAI,CAACvD,eAAe,CAAC+B,iBAAiB,CAACyB,IAAI;QAC9C,GAAG;UACDC,MAAM,EAAE,EAAE;UACVC,iBAAiB,EAAE,QAAQ;UAC3BC,IAAI,EAAE;YACJC,KAAK,EAAE;;;OAGZ;MACDC,UAAU,EAAE,SAAAA,CAASC,GAAG,EAAEjB,IAAI,EAAEkB,SAAS,EAAEC,KAAK;QAC9CC,CAAC,CAACH,GAAG,CAAC,CAACI,QAAQ,CAAC,8BAA8B,CAAC;MACjD,CAAC;MACDC,UAAU,EAAE;QACV;QACA;MAAA,CACD;MACDC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MACnBC,QAAQ,EAAE;QACRC,OAAO,EAAE;OACV;MACDC,OAAO,EAAE,CACP;QACE1B,IAAI,EAAE,UAAU;QAChB2B,OAAO,EAAE;OACV,EACD;QACE3B,IAAI,EAAE,MAAM;QACZ4B,SAAS,EAAE;OACZ;MACD;MACA;MACA;MACA;MACA;MACA;QACE5B,IAAI,EAAE,IAAI;QACV4B,SAAS,EAAE,cAAc;QACzBC,SAAS,EAAE,IAAI;QACfC,MAAM,EAAEA,CAAC9B,IAAI,EAAE/B,IAAI,EAAEgD,GAAG,EAAEc,IAAI,KAAI;UAChC,OAAO;;mHAEgG/B,IAAI;kBACrG,IAAI,CAAC/C,MAAM,CAACmB,OAAO,CAAC,mBAAmB,CAAC;;;aAG7C;QACH;OACD,CACF;MACD4D,UAAU,EAAE,CACV,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EACjB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CACrB;MACDC,OAAO,EAAE;QACPhD,GAAG,EAAE,IAAI,CAAC9B,eAAe,CAAC+B,iBAAiB,CAAC+C,OAAO,CAAC9C,cAAc;QAClE8C,OAAO,EAAE,CACP;UACEC,IAAI,EACF,uCAAuC,GACvC,IAAI,CAACjF,MAAM,CAACmB,OAAO,CAAC,0BAA0B,CAAC;UACjD+D,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,aAAa;SACjC;;KAGN;EACH;EAEA1H,qBAAqBA,CAACT,MAAM;IAC1B,IAAI,CAAC6F,mBAAmB,GAAG;MACzBC,aAAa,EAAE,IAAI,CAACxF;KACrB;IACD,IAAI,CAACkD,SAAS,CAAC4E,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5D;MACA,IAAIE,UAAU,GAAG,IAAI,CAAChI,QAAQ,GAAG,OAAO,IAAI,CAACA,QAAQ,MAAM,GAAG,EAAE;MAChE8H,UAAU,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC5B,MAAM,CAAC2B,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAACE,IAAI,EAAE;IAC7D,CAAC,CAAC;EACJ;EAEA3D,gBAAgBA,CAAA;IACd,IAAI,CAAC/B,QAAQ,CAAC2F,IAAI,EAAE;IACpB,IAAI,CAACtF,oBAAoB,CAACuF,kBAAkB,EAAE,CAACjD,SAAS,CACrDM,IAAI,IAAI;MACP,IAAI,CAAC4C,cAAc,GAAG5C,IAAI;MAE1B;MACA,IAAIF,mBAAmB,GAAG,IAAI,CAACA,mBAAmB;MAClD,IAAI,CAACA,mBAAmB,EAAE+C,cAAc,EAAE;QACxC,IAAI7C,IAAI,GAAG;UACT6C,cAAc,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC,CAAClJ;SACxC;QACD,IAAI,CAACoG,mBAAmB,GAAGE,IAAI;;MAEjC;MAAA,KACK,IACH,CAAC,IAAI,CAAC4C,cAAc,CAAC3C,IAAI,CAAE6C,MAAM,IAAI;QACnC,OAAOA,MAAM,CAACpJ,EAAE,IAAIoG,mBAAmB,CAAC+C,cAAc;MACxD,CAAC,CAAC,EACF;QACA,IAAI7C,IAAI,GAAG;UACT6C,cAAc,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC,CAAClJ;SACxC;QACD,IAAI,CAACoG,mBAAmB,GAAGE,IAAI;;MAGjC,IAAI,CAACP,SAAS,GAAG,IAAI,CAACK,mBAAmB,CAAC+C,cAAc;MACxD,IAAI,CAACE,oBAAoB,CAAC,IAAI,CAACtD,SAAS,CAAC;MAEzC,IAAI,CAAC,IAAI,CAAC5B,WAAW,EAAE;QACrB,IAAI,CAACA,WAAW,GAAG,IAAI;QACvB,IAAI,CAACmB,UAAU,EAAE;QACjB,IAAI,CAACtB,SAAS,CAACsF,IAAI,CAAC,IAAI,CAACrF,SAAS,CAAC;OACpC,MAAM;QACL,IAAI,IAAI,CAACF,SAAS,CAAC4E,UAAU,EAAE;UAC7B,IAAI,CAAC5E,SAAS,CAAC4E,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;YAC5DA,UAAU,CAACjD,IAAI,CAAC6D,MAAM,EAAE;UAC1B,CAAC,CAAC;;;IAGR,CAAC,EACAC,KAAK,IAAI;MACRpK,IAAI,CAACqK,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdlB,IAAI,EAAE,IAAI,CAACjF,MAAM,CAACmB,OAAO,CAAC8E,KAAK,CAACG,OAAO,CAAC;QACxCC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACtG,MAAM,CAACmB,OAAO,CAAC,IAAI;OAC5C,CAAC;IACJ,CAAC,CACF;EACH;EAEAoF,sBAAsBA,CAACC,KAAK;IAC1B,IAAI,CAAC1G,QAAQ,CAAC2F,IAAI,EAAE;IACpB,IAAI,CAACjF,SAAS,CAAC4E,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5D,IAAI,CAACvC,mBAAmB,GAAG;QACzB+C,cAAc,EAAE,IAAI,CAACpD;OACtB;MACD4C,UAAU,CAACjD,IAAI,CAAC6D,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAS,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC3G,QAAQ,CAAC4G,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGH,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACI,MAAM,CAACC,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAGN,KAAK,CAACI,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;QAC9D;QACA,IAAI,CAACnH,OAAO,CAACoH,QAAQ,CAAC,CAAC,2BAA2B,EAAEF,aAAa,CAAC,CAAC;;IAEvE,CAAC,CAAC;EACJ;EAuBA3B,aAAaA,CAAA;IACX,IAAI,CAAC8B,QAAQ,GAAG,IAAI,CAAC5G,aAAa,CAAC6G,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACtDC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,CAAC;IACF;IACA,IAAI,CAACL,QAAQ,CAACM,MAAM,CAAC9E,SAAS,CAAE+E,GAAG,IAAI;MACrC,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,CAAC;IAEF,IAAI,CAACR,QAAQ,CAACS,SAAS,CAACjF,SAAS,CAAE+E,GAAG,IAAI;MACxC,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,CAAC;EACJ;EAEAA,SAASA,CAAA;IACP,IAAI,CAAClI,IAAI,CAACoI,KAAK,EAAE;IACjB,IAAI,CAAChJ,YAAY,GAAG,EAAE;EACxB;EAEAmH,oBAAoBA,CAACtD,SAAS;IAC5B,IAAI,CAAC3C,KAAK,CACPyC,GAAG,CACF,GAAG3G,WAAW,CAAC4G,MAAM,YAAYC,SAAS,+BAA+B,CAC1E,CACAC,SAAS,CAAEM,IAAI,IAAI;MAClB6E,OAAO,CAACC,GAAG,CAAC9E,IAAI,CAAC;MACjB,IAAIjC,WAAW,GAAGiC,IAAI,CAACA,IAAI,CAAC+E,GAAG,CAAEC,UAAU,IAAI;QAC7C,OAAO;UAAE7G,KAAK,EAAE6G,UAAU,CAACnL,IAAI;UAAEoL,KAAK,EAAED,UAAU,CAACtL;QAAE,CAAE;MACzD,CAAC,CAAC;MACF;MACA,IAAIwL,KAAK,GAAG,IAAI,CAACzI,MAAM,CAAC0I,SAAS,CAC9BC,KAAK,IAAKA,KAAK,CAACpH,GAAG,IAAI,gBAAgB,CACzC;MACD;MACA,IAAI,CAACvB,MAAM,CAACyI,KAAK,CAAC,CAAChH,KAAK,CAACI,OAAO,GAAGP,WAAW;IAChD,CAAC,CAAC;EACN;EAEAgB,aAAaA,CAAA;IACX,IAAI,CAACxB,YAAY,CAAC8H,mBAAmB,EAAE,CAAC3F,SAAS,CAAEM,IAAI,IAAI;MACzD,IAAIsF,KAAK,GAAGtF,IAAI,CAAC+E,GAAG,CAAEQ,IAAI,IAAI;QAC5B,OAAO;UAAEpH,KAAK,EAAEoH,IAAI,CAAC1L,IAAI;UAAEoL,KAAK,EAAEM,IAAI,CAAC7L;QAAE,CAAE;MAC7C,CAAC,CAAC;MACF;MACA,IAAIwL,KAAK,GAAG,IAAI,CAACzI,MAAM,CAAC0I,SAAS,CAAEC,KAAK,IAAKA,KAAK,CAACpH,GAAG,IAAI,UAAU,CAAC;MACrE;MACA,IAAI,CAACvB,MAAM,CAACyI,KAAK,CAAC,CAAChH,KAAK,CAACI,OAAO,GAAGgH,KAAK;IAC1C,CAAC,CAAC;EACJ;EAEAlJ,QAAQA,CAAA;IAAA,IAAAoJ,KAAA;IACN,IAAI,CAAC5J,YAAY,GAAG,EAAE;IACtB,IAAI,IAAI,CAACY,IAAI,CAACiJ,OAAO,EAAE;MACrB;;IAEF,IAAI,CAACjI,kBAAkB,CAACkI,WAAW,CAAC,IAAI,CAAChJ,KAAK,CAAC,CAACgD,SAAS;MAAA,IAAAiG,IAAA,GAAAC,iBAAA,CAAC,WAAOnB,GAAS,EAAI;QAC1E,IAAIA,GAAG,EAAE;UACP,MAAMoB,MAAM,GAAG,IAAIC,UAAU,EAAE;UAC/BD,MAAM,CAACE,MAAM;YAAA,IAAAC,KAAA,GAAAJ,iBAAA,CAAG,WAAOnC,KAAU,EAAI;cACnC,MAAMwC,UAAU,GAAGxC,KAAK,CAACI,MAAM,CAACqC,MAAM;cAEtC,IAAI/M,SAAS,CAACgN,gBAAgB,EAAE,EAAE;gBAChC,IAAI;kBACF;kBACA,MAAMC,eAAe,SAASnN,UAAU,CAACoN,SAAS,CAAC;oBACjDC,IAAI,EAAE,cAAc;oBACpBtG,IAAI,EAAEiG,UAAU;oBAChBM,SAAS,EAAExN,SAAS,CAACyN,SAAS;oBAC9BC,QAAQ,EAAEzN,QAAQ,CAAC0N;mBACpB,CAAC;kBAEF;kBACA,MAAMxN,KAAK,CAACyN,KAAK,CAAC;oBAChBvD,KAAK,EAAE,qBAAqB;oBAC5BwD,GAAG,EAAER,eAAe,CAACS,GAAG;oBACxBC,WAAW,EAAE;mBACd,CAAC;kBACFC,KAAK,CAAC,+BAA+B,CAAC;iBACvC,CAAC,OAAOC,CAAC,EAAE;kBACVnC,OAAO,CAAC3B,KAAK,CAAC,+BAA+B,EAAE8D,CAAC,CAAC;kBACjDxB,KAAI,CAAC5J,YAAY,GAAG,+BAA+B;;eAEtD,MAAM;gBACL,MAAMqL,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACjB,UAAU,CAAC,EAAE;kBAAEhI,IAAI,EAAE;gBAAe,CAAE,CAAC;gBAC9D,MAAM2I,GAAG,GAAGO,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;gBAC5C,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;gBACrCF,CAAC,CAACG,IAAI,GAAGb,GAAG;gBACZU,CAAC,CAACI,QAAQ,GAAG,cAAc;gBAC3BH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;gBAC5BA,CAAC,CAACO,KAAK,EAAE;gBACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;gBAC5BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACnB,GAAG,CAAC;;YAEnC,CAAC;YAAA,iBAAAoB,GAAA;cAAA,OAAAhC,KAAA,CAAAiC,KAAA,OAAAC,SAAA;YAAA;UAAA;UACDrC,MAAM,CAACsC,UAAU,CAAC1D,GAAG,CAAC;;MAE1B,CAAC;MAAA,iBAAA2D,EAAA;QAAA,OAAAzC,IAAA,CAAAsC,KAAA,OAAAC,SAAA;MAAA;IAAA,KACAhF,KAAK,IAAI;MACR,IAAIA,KAAK,CAACmF,MAAM,IAAI,GAAG,EAAE;QACvB,IAAI,CAACzM,YAAY,GAAG,IAAI,CAACqB,MAAM,CAACmB,OAAO,CAAC,4BAA4B,CAAC;OACtE,MAAM;QACL,IAAI,CAACxC,YAAY,GAAG,IAAI,CAACqB,MAAM,CAACmB,OAAO,CAAC,6BAA6B,CAAC;;MAExEyG,OAAO,CAACC,GAAG,CAAC5B,KAAK,CAAC;IACpB,CAAC,CAAC;EACN;EAGAoF,cAAcA,CAAC7E,KAAU;IACvB;EAAA;EAGF8E,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC5E,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,EAAE;;IAEnB,IAAI,CAACjG,SAAS,CAAC8K,WAAW,EAAE;EAC9B;EAEA,IAAI1I,mBAAmBA,CAAA;IACrB,IAAIA,mBAAmB,GAAG2I,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IACtE,OAAO5I,mBAAmB,GAAG6I,IAAI,CAACC,KAAK,CAAC9I,mBAAmB,CAAC,GAAG,IAAI;EACrE;EAEA,IAAIA,mBAAmBA,CAACE,IAAI;IAC1B,IAAI6I,QAAQ,GAAGF,IAAI,CAACG,SAAS,CAAC9I,IAAI,CAAC;IACnC;IACA,IAAI+I,UAAU,GAAG,IAAI,CAACjJ,mBAAmB;IACzC,IAAIiJ,UAAU,EAAE;MACdF,QAAQ,GAAGF,IAAI,CAACG,SAAS,CAAC;QAAE,GAAGC,UAAU;QAAE,GAAG/I;MAAI,CAAE,CAAC;;IAGvDyI,YAAY,CAACO,OAAO,CAAC,sBAAsB,EAAEH,QAAQ,CAAC;EACxD;EAAC,QAAAI,CAAA;qBA9ZUtM,4BAA4B,EAAAvD,EAAA,CAAA8P,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhQ,EAAA,CAAA8P,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAlQ,EAAA,CAAA8P,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAApQ,EAAA,CAAA8P,iBAAA,CAAA9P,EAAA,CAAAqQ,SAAA,GAAArQ,EAAA,CAAA8P,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAvQ,EAAA,CAAA8P,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAzQ,EAAA,CAAA8P,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA3Q,EAAA,CAAA8P,iBAAA,CAAAc,EAAA,CAAAC,mBAAA,GAAA7Q,EAAA,CAAA8P,iBAAA,CAAAgB,EAAA,CAAAC,KAAA,GAAA/Q,EAAA,CAAA8P,iBAAA,CAAAkB,EAAA,CAAAC,QAAA,GAAAjR,EAAA,CAAA8P,iBAAA,CAAAoB,GAAA,CAAAC,WAAA,GAAAnR,EAAA,CAAA8P,iBAAA,CAAAsB,GAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA;UAA5B/N,4BAA4B;IAAAgO,SAAA;IAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBAE5BnS,kBAAkB;;;;;;;;;;;;;;;QC9B/BS,EAAA,CAAAC,cAAA,aAA+C;QAEvCD,EAAA,CAAAoC,SAAA,4BAAyE;QACzEpC,EAAA,CAAAC,cAAA,aAAqB;QAKTD,EAAA,CAAAW,UAAA,2BAAAiR,yEAAA/Q,MAAA;UAAA,OAAA8Q,GAAA,CAAAtL,SAAA,GAAAxF,MAAA;QAAA,EAAuB,oBAAAgR,kEAAAhR,MAAA;UAAA,OAAW8Q,GAAA,CAAAvH,sBAAA,CAAAvJ,MAAA,CAA8B;QAAA,EAAzC;;QACvBb,EAAA,CAAAuB,UAAA,IAAAuQ,iDAAA,uBACY;QAChB9R,EAAA,CAAAG,YAAA,EAAY;QAEhBH,EAAA,CAAAuB,UAAA,IAAAwQ,oDAAA,0BA0Be;QACnB/R,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA+B;QAEvBD,EAAA,CAAAoC,SAAA,iBACQ;QACZpC,EAAA,CAAAG,YAAA,EAAQ;QAKpBH,EAAA,CAAAC,cAAA,eAAwB;QAEpBD,EAAA,CAAAuB,UAAA,KAAAyQ,oDAAA,mCAAAhS,EAAA,CAAAiS,sBAAA,CAyBc;QAElBjS,EAAA,CAAAG,YAAA,EAAM;;;QA7EsBH,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAI,UAAA,kBAAAuR,GAAA,CAAAjN,aAAA,CAA+B;QAKS1E,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAyB,qBAAA,gBAAAzB,EAAA,CAAA0B,WAAA,yBAA+C;QAAxF1B,EAAA,CAAAI,UAAA,qBAAoB,gCAAAuR,GAAA,CAAAtL,SAAA;QAEGrG,EAAA,CAAAO,SAAA,GAAiB;QAAjBP,EAAA,CAAAI,UAAA,YAAAuR,GAAA,CAAAnI,cAAA,CAAiB;QAIxCxJ,EAAA,CAAAO,SAAA,GAA4B;QAA5BP,EAAA,CAAAI,UAAA,SAAAJ,EAAA,CAAAkS,eAAA,KAAAC,GAAA,EAA4B;QA6B1BnS,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAI,UAAA,cAAAuR,GAAA,CAAApN,SAAA,CAAuB,cAAAoN,GAAA,CAAArN,SAAA,QAAAqN,GAAA,CAAAnN,OAAA", "names": ["FormGroup", "DataTableDirective", "environment", "Subject", "<PERSON><PERSON>", "Directory", "Encoding", "Filesystem", "Share", "Capacitor", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r4", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "group_r8", "ɵɵlistener", "TournamentSelectionComponent_ng_container_8_div_4_Template_ng_select_ngModelChange_4_listener", "$event", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "group_id", "TournamentSelectionComponent_ng_container_8_div_4_Template_ng_select_change_4_listener", "ctx_r11", "onSelectedGroupChange", "ɵɵtemplate", "TournamentSelectionComponent_ng_container_8_div_4_ng_option_9_Template", "ɵɵpropertyInterpolate", "ɵɵpipeBind1", "ctx_r6", "currentGroups", "ɵɵelementContainerStart", "TournamentSelectionComponent_ng_container_8_Template_button_click_2_listener", "restoredCtx", "_r13", "variable_r5", "ngIf", "isShowFilter", "ɵɵelement", "TournamentSelectionComponent_ng_container_8_div_4_Template", "ɵɵelementContainerEnd", "ctx_r15", "errorMessage", "TournamentSelectionComponent_ng_template_13_Template_button_click_4_listener", "_r17", "modal_r14", "$implicit", "close", "TournamentSelectionComponent_ng_template_13_Template_form_ngSubmit_10_listener", "ctx_r18", "onSubmit", "TournamentSelectionComponent_ng_template_13_div_14_Template", "ɵɵtextInterpolate", "ctx_r3", "form", "fields", "model", "TournamentSelectionComponent", "constructor", "_router", "_http", "_loading", "renderer", "_trans", "_seasonService", "_commonsService", "_registrationService", "_titleService", "_modalService", "_clubService", "_tournamentService", "dtElement", "dtTrigger", "dtOptions", "tableID", "isInitTable", "contentHeader", "tournaments", "key", "type", "props", "label", "instant", "multiple", "options", "setTitle", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "ngOnInit", "getCurrentSeason", "getClubActive", "buildTable", "dom", "dataTableDefaults", "dom_table_card", "ajax", "dataTablesParameters", "callback", "get", "apiUrl", "season_id", "subscribe", "resp", "customSearchInput", "groups", "tournamentSelection", "selectedGroup", "data", "find", "group", "recordsTotal", "recordsFiltered", "stateSave", "select", "rowId", "responsive", "scrollX", "language", "lang", "search", "searchPlaceholder", "attr", "class", "createdRow", "row", "dataIndex", "cells", "$", "addClass", "columnDefs", "order", "rowGroup", "dataSrc", "columns", "visible", "className", "translate", "render", "meta", "lengthMenu", "buttons", "text", "action", "modalOpenForm", "dtInstance", "then", "str_filter", "column", "draw", "show", "getAllSeasonActive", "currentSeasons", "selectedS<PERSON>on", "season", "getTournamentsActive", "next", "reload", "error", "fire", "title", "message", "icon", "confirmButtonText", "onSelectedSeasonChange", "event", "ngAfterViewInit", "unlistener", "listen", "target", "hasAttribute", "tournament_id", "getAttribute", "navigate", "modalRef", "open", "modalForm", "size", "centered", "backdrop", "closed", "res", "resetForm", "dismissed", "reset", "console", "log", "map", "tournament", "value", "index", "findIndex", "field", "getAllClubsIsActive", "clubs", "club", "_this", "invalid", "generateIcs", "_ref", "_asyncToGenerator", "reader", "FileReader", "onload", "_ref2", "ics<PERSON><PERSON>nt", "result", "isNativePlatform", "writeFileResult", "writeFile", "path", "directory", "Documents", "encoding", "UTF8", "share", "url", "uri", "dialogTitle", "alert", "e", "blob", "Blob", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_x2", "apply", "arguments", "readAsText", "_x", "status", "onCaptureEvent", "ngOnDestroy", "unsubscribe", "localStorage", "getItem", "JSON", "parse", "str_data", "stringify", "exist_data", "setItem", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "LoadingService", "Renderer2", "i4", "TranslateService", "i5", "SeasonService", "i6", "CommonsService", "i7", "RegistrationService", "i8", "Title", "i9", "NgbModal", "i10", "ClubService", "i11", "TournamentService", "_2", "selectors", "viewQuery", "TournamentSelectionComponent_Query", "rf", "ctx", "TournamentSelectionComponent_Template_ng_select_ngModelChange_5_listener", "TournamentSelectionComponent_Template_ng_select_change_5_listener", "TournamentSelectionComponent_ng_option_7_Template", "TournamentSelectionComponent_ng_container_8_Template", "TournamentSelectionComponent_ng_template_13_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction0", "_c2"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\tournament-selection\\tournament-selection.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\tournament-selection\\tournament-selection.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit, Renderer2, TemplateRef, ViewChild } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { Router } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport { Directory, Encoding, Filesystem } from '@capacitor/filesystem';\r\nimport { Share } from '@capacitor/share';\r\nimport { Capacitor } from '@capacitor/core';\r\n\r\n@Component({\r\n  selector: 'app-tournament-selection',\r\n  templateUrl: './tournament-selection.component.html',\r\n  styleUrls: ['./tournament-selection.component.scss']\r\n})\r\nexport class TournamentSelectionComponent implements OnInit {\r\n  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @ViewChild('modalForm') modalForm: any;\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  dtOptions: any = {};\r\n  tableID: string = 'select-tournament-table';\r\n  isInitTable: boolean = false;\r\n  modalRef: any;\r\n  contentHeader = {};\r\n  tournaments = [];\r\n  season_id: any;\r\n  group_id = '';\r\n  currentSeasons;\r\n  currentGroups;\r\n  model: any = {};\r\n  form = new FormGroup({});\r\n  errorMessage: string = '';\r\n  unlistener: () => void;\r\n\r\n  constructor(\r\n    public _router: Router,\r\n    public _http: HttpClient,\r\n    public _loading: LoadingService,\r\n    public renderer: Renderer2,\r\n    public _trans: TranslateService,\r\n    public _seasonService: SeasonService,\r\n    public _commonsService: CommonsService,\r\n    public _registrationService: RegistrationService,\r\n    public _titleService: Title,\r\n    public _modalService: NgbModal,\r\n    public _clubService: ClubService,\r\n    public _tournamentService: TournamentService\r\n  ) {\r\n    this._titleService.setTitle(\r\n      `${this._trans.instant('Fixtures')} & ${this._trans.instant('Results')}`\r\n    );\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Competitions'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Competitions'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getCurrentSeason();\r\n    this.getClubActive();\r\n  }\r\n\r\n  buildTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom_table_card,\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        this._http\r\n          .get<any>(\r\n            `${environment.apiUrl}/seasons/${this.season_id}/tournaments?is_released=true`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this._commonsService.customSearchInput(this.tableID);\r\n            this.currentGroups = resp.options.groups;\r\n\r\n            let tournamentSelection = this.tournamentSelection;\r\n            if (!tournamentSelection?.selectedGroup) {\r\n              let data = {\r\n                selectedGroup: ''\r\n              };\r\n              this.tournamentSelection = data;\r\n            }\r\n            // check if selectedSeason is not in currentSeasons then set selectedSeason = currentSeasons[0]\r\n            else if (\r\n              !this.currentGroups.find((group) => {\r\n                return group.id == tournamentSelection.selectedGroup;\r\n              })\r\n            ) {\r\n              let data = {\r\n                selectedGroup: ''\r\n              };\r\n              this.tournamentSelection = data;\r\n            }\r\n\r\n            this.group_id = this.tournamentSelection.selectedGroup;\r\n            this.onSelectedGroupChange(this.group_id);\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      stateSave: false,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: {\r\n        ...this._commonsService.dataTableDefaults.lang,\r\n        ...{\r\n          search: '',\r\n          searchPlaceholder: 'Search',\r\n          attr: {\r\n            class: 'w-100'\r\n          }\r\n        }\r\n      },\r\n      createdRow: function(row, data, dataIndex, cells) {\r\n        $(row).addClass('col-12 col-md-6 col-lg-4 p-0');\r\n      },\r\n      columnDefs: [\r\n        // { responsivePriority: 1, targets: -1 },\r\n        // { responsivePriority: 2, targets: 2 },\r\n      ],\r\n      order: [[2, 'asc']],\r\n      rowGroup: {\r\n        dataSrc: 'group.name'\r\n      },\r\n      columns: [\r\n        {\r\n          data: 'group_id',\r\n          visible: false\r\n        },\r\n        {\r\n          data: 'name',\r\n          className: 'h3 fw-bold d-flex py-2'\r\n        },\r\n        // {\r\n        //   data: 'group.name',\r\n        //   className: 'd-flex',\r\n        //   type: 'any-number',\r\n        // },\r\n        {\r\n          data: 'id',\r\n          className: 'd-block pb-2',\r\n          translate: true,\r\n          render: (data, type, row, meta) => {\r\n            return `\r\n            <div class=\"pt-50 pb-50\">\r\n              <button class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-primary btn-block\" tournament-id=\"${data}\">\r\n                ${this._trans.instant('Select Tournament')}\r\n              </button>\r\n            </div>\r\n            `;\r\n          }\r\n        }\r\n      ],\r\n      lengthMenu: [\r\n        [25, 50, 100, -1],\r\n        [25, 50, 100, 'All']\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom_table_card,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"bi bi-calendar-event\"></i> ' +\r\n              this._trans.instant('Add fixtures to calendar'),\r\n            action: () => this.modalOpenForm()\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  onSelectedGroupChange($event) {\r\n    this.tournamentSelection = {\r\n      selectedGroup: this.group_id\r\n    };\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      // filter by group_id\r\n      let str_filter = this.group_id ? `^\\\\b${this.group_id}\\\\b$` : '';\r\n      dtInstance.column(0).search(str_filter, true, false).draw();\r\n    });\r\n  }\r\n\r\n  getCurrentSeason() {\r\n    this._loading.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.currentSeasons = data;\r\n\r\n        // // if not exist selectedSeason tableManageLeague then set default, else get from tableManageLeague\r\n        let tournamentSelection = this.tournamentSelection;\r\n        if (!tournamentSelection?.selectedSeason) {\r\n          let data = {\r\n            selectedSeason: this.currentSeasons[0].id\r\n          };\r\n          this.tournamentSelection = data;\r\n        }\r\n        // check if selectedSeason is not in currentSeasons then set selectedSeason = currentSeasons[0]\r\n        else if (\r\n          !this.currentSeasons.find((season) => {\r\n            return season.id == tournamentSelection.selectedSeason;\r\n          })\r\n        ) {\r\n          let data = {\r\n            selectedSeason: this.currentSeasons[0].id\r\n          };\r\n          this.tournamentSelection = data;\r\n        }\r\n\r\n        this.season_id = this.tournamentSelection.selectedSeason;\r\n        this.getTournamentsActive(this.season_id);\r\n\r\n        if (!this.isInitTable) {\r\n          this.isInitTable = true;\r\n          this.buildTable();\r\n          this.dtTrigger.next(this.dtOptions);\r\n        } else {\r\n          if (this.dtElement.dtInstance) {\r\n            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n              dtInstance.ajax.reload();\r\n            });\r\n          }\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: this._trans.instant(error.message),\r\n          icon: 'error',\r\n          confirmButtonText: this._trans.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectedSeasonChange(event) {\r\n    this._loading.show();\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      this.tournamentSelection = {\r\n        selectedSeason: this.season_id\r\n      };\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament-id')) {\r\n        let tournament_id = event.target.getAttribute('tournament-id');\r\n        // go to fixtures-results/:tournament_id\r\n        this._router.navigate(['/leagues/fixtures-results', tournament_id]);\r\n      }\r\n    });\r\n  }\r\n\r\n  fields: FormlyFieldConfig[] = [\r\n    {\r\n      key: 'tournament_ids',\r\n      type: 'ng-select',\r\n      props: {\r\n        label: this._trans.instant('Select Tournament'),\r\n        multiple: true,\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'club_ids',\r\n      type: 'ng-select',\r\n      props: {\r\n        label: this._trans.instant('Select Club'),\r\n        multiple: true,\r\n        options: []\r\n      }\r\n    }\r\n  ];\r\n\r\n  modalOpenForm() {\r\n    this.modalRef = this._modalService.open(this.modalForm, {\r\n      size: 'md',\r\n      centered: true,\r\n      backdrop: 'static'\r\n    });\r\n    // when close modal reset form\r\n    this.modalRef.closed.subscribe((res) => {\r\n      this.resetForm();\r\n    });\r\n\r\n    this.modalRef.dismissed.subscribe((res) => {\r\n      this.resetForm();\r\n    });\r\n  }\r\n\r\n  resetForm() {\r\n    this.form.reset();\r\n    this.errorMessage = '';\r\n  }\r\n\r\n  getTournamentsActive(season_id) {\r\n    this._http\r\n      .get<any>(\r\n        `${environment.apiUrl}/seasons/${season_id}/tournaments?is_released=true`\r\n      )\r\n      .subscribe((data) => {\r\n        console.log(data);\r\n        let tournaments = data.data.map((tournament) => {\r\n          return { label: tournament.name, value: tournament.id };\r\n        });\r\n        // find index of field key tournament_ids\r\n        let index = this.fields.findIndex(\r\n          (field) => field.key == 'tournament_ids'\r\n        );\r\n        // set options for field tournament_ids\r\n        this.fields[index].props.options = tournaments;\r\n      });\r\n  }\r\n\r\n  getClubActive() {\r\n    this._clubService.getAllClubsIsActive().subscribe((data) => {\r\n      let clubs = data.map((club) => {\r\n        return { label: club.name, value: club.id };\r\n      });\r\n      // find index of field key club_ids\r\n      let index = this.fields.findIndex((field) => field.key == 'club_ids');\r\n      // set options for field club_ids\r\n      this.fields[index].props.options = clubs;\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    this.errorMessage = '';\r\n    if (this.form.invalid) {\r\n      return;\r\n    }\r\n    this._tournamentService.generateIcs(this.model).subscribe(async (res: Blob) => {\r\n        if (res) {\r\n          const reader = new FileReader();\r\n          reader.onload = async (event: any) => {\r\n            const icsContent = event.target.result;\r\n\r\n            if (Capacitor.isNativePlatform()) {\r\n              try {\r\n                // Write to file in a temporary or documents directory\r\n                const writeFileResult = await Filesystem.writeFile({\r\n                  path: 'fixtures.ics',\r\n                  data: icsContent,\r\n                  directory: Directory.Documents, // You can choose another directory if needed\r\n                  encoding: Encoding.UTF8\r\n                });\r\n\r\n                // Use Share plugin to share the file with available apps\r\n                await Share.share({\r\n                  title: 'Share Calendar File',\r\n                  url: writeFileResult.uri, // Use the file URI from the writeFileResult\r\n                  dialogTitle: 'Share Calendar Event'\r\n                });\r\n                alert('File downloaded successfully!');\r\n              } catch (e) {\r\n                console.error('Error saving or sharing file:', e);\r\n                this.errorMessage = 'Error saving or sharing file.';\r\n              }\r\n            } else {\r\n              const blob = new Blob([icsContent], { type: 'text/calendar' });\r\n              const url = window.URL.createObjectURL(blob);\r\n              const a = document.createElement('a');\r\n              a.href = url;\r\n              a.download = 'fixtures.ics';\r\n              document.body.appendChild(a);\r\n              a.click();\r\n              document.body.removeChild(a);\r\n              window.URL.revokeObjectURL(url);\r\n            }\r\n          };\r\n          reader.readAsText(res);\r\n        }\r\n      },\r\n      (error) => {\r\n        if (error.status == 400) {\r\n          this.errorMessage = this._trans.instant('Please fill in all fields.');\r\n        } else {\r\n          this.errorMessage = this._trans.instant('No matching fixtures found.');\r\n        }\r\n        console.log(error);\r\n      });\r\n  }\r\n\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Do not forget to unsubscribe the event\r\n    if (this.unlistener) {\r\n      this.unlistener();\r\n    }\r\n    this.dtTrigger.unsubscribe();\r\n  }\r\n\r\n  get tournamentSelection() {\r\n    let tournamentSelection = localStorage.getItem('tournament_selection');\r\n    return tournamentSelection ? JSON.parse(tournamentSelection) : null;\r\n  }\r\n\r\n  set tournamentSelection(data) {\r\n    let str_data = JSON.stringify(data);\r\n    // merge data if exist\r\n    let exist_data = this.tournamentSelection;\r\n    if (exist_data) {\r\n      str_data = JSON.stringify({ ...exist_data, ...data });\r\n    }\r\n\r\n    localStorage.setItem('tournament_selection', str_data);\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n    <div class=\"content-body\">\r\n        <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n        <div class=\"row m-0\">\r\n            <!-- ng select season -->\r\n            <div class=\"col col-md-6 col-lg-3 mb-1 pl-25 pr-50\">\r\n                <!-- <label class=\"form-label\" for=\"season\">{{ 'Season' | translate }}</label> -->\r\n                <ng-select [searchable]=\"false\" [clearable]=\"false\" placeholder=\"{{ 'Select Season' | translate }}\"\r\n                    [(ngModel)]=\"season_id\" (change)=\"onSelectedSeasonChange($event)\">\r\n                    <ng-option *ngFor=\"let season of currentSeasons\" [value]=\"season.id\">{{ season.name }}\r\n                    </ng-option>\r\n                </ng-select>\r\n            </div>\r\n            <ng-container *ngIf=\"{ isShowFilter:true } as variable\">\r\n                <div class=\"col-auto col-md pl-50 pr-0 d-flex justify-content-end align-items-start\">\r\n                    <button type=\"button\" class=\"btn btn-flat pl-25 pr-25\"\r\n                        (click)=\" variable.isShowFilter = !variable.isShowFilter\">\r\n                        <i class=\"fa-light fa-filter-list fa-xl mr-25\"></i>\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"col-12 col-md-auto p-25 mb-1\" *ngIf=\"variable.isShowFilter\">\r\n                    <div class=\"row mr-0\">\r\n                        <div class=\"col pr-0\">\r\n                            <div style=\"min-width: 130px;\">\r\n\r\n                                <ng-select placeholder=\"{{ 'Select Group' | translate }}\" [(ngModel)]=\"group_id\"\r\n                                    [clearable]=\"false\" (change)=\"onSelectedGroupChange($event)\">\r\n                                    <ng-option [value]=\"''\" name=\"all\">\r\n                                        {{ 'All' | translate }}\r\n                                    </ng-option>\r\n                                    <ng-option *ngFor=\"let group of currentGroups\" [value]=\"group.id\" name=\"group.id\">\r\n                                        {{ group.name }}\r\n                                    </ng-option>\r\n                                </ng-select>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n        </div>\r\n        <section id=\"fixtures-results\">\r\n            <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"card_table\" [id]=\"tableID\">\r\n                <tbody class=\"row ml-0 mr-0\">\r\n                </tbody>\r\n            </table>\r\n        </section>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"login-form\">\r\n    <!-- Modal -->\r\n    <ng-template #modalForm let-modal>\r\n        <div class=\"modal-header\">\r\n            <h4 class=\"modal-title\" id=\"myModalLabel1\">{{'Add fixtures to calendar' | translate}}</h4>\r\n            <button type=\"button\" class=\"close\" (click)=\"modal.close('Cross click')\" aria-label=\"Close\">\r\n                <span aria-hidden=\"true\">&times;</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n            {{'Select Teams or Tournaments to receive fixtures and other relevant messages to your calendars.' | translate}}\r\n        </div>\r\n        <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus style=\"padding-bottom: 0px\">\r\n                <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n            </div>\r\n            <div class=\"modal-body\" style=\"padding-top: 0px; padding-bottom: 0px\">\r\n                <div *ngIf=\"errorMessage\" class=\"text-danger\" >\r\n                    {{ errorMessage }}\r\n                </div>\r\n            </div>\r\n            <div class=\"modal-footer\">\r\n                <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n                    {{\"Submit\" | translate}}\r\n                </button>\r\n            </div>\r\n        </form>\r\n    </ng-template>\r\n    <!-- / Modal -->\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import copyObject from './_copyObject.js';\nimport createAssigner from './_createAssigner.js';\nimport keys from './keys.js';\n\n/**\n * This method is like `_.assign` except that it accepts `customizer`\n * which is invoked to produce the assigned values. If `customizer` returns\n * `undefined`, assignment is handled by the method instead. The `customizer`\n * is invoked with five arguments: (objValue, srcValue, key, object, source).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} sources The source objects.\n * @param {Function} [customizer] The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @see _.assignInWith\n * @example\n *\n * function customizer(objValue, srcValue) {\n *   return _.isUndefined(objValue) ? srcValue : objValue;\n * }\n *\n * var defaults = _.partialRight(_.assignWith, customizer);\n *\n * defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar assignWith = createAssigner(function (object, source, srcIndex, customizer) {\n  copyObject(source, keys(source), object, customizer);\n});\nexport default assignWith;", "map": {"version": 3, "names": ["copyObject", "createAssigner", "keys", "assignWith", "object", "source", "srcIndex", "customizer"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lodash-es/assignWith.js"], "sourcesContent": ["import copyObject from './_copyObject.js';\nimport createAssigner from './_createAssigner.js';\nimport keys from './keys.js';\n\n/**\n * This method is like `_.assign` except that it accepts `customizer`\n * which is invoked to produce the assigned values. If `customizer` returns\n * `undefined`, assignment is handled by the method instead. The `customizer`\n * is invoked with five arguments: (objValue, srcValue, key, object, source).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} sources The source objects.\n * @param {Function} [customizer] The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @see _.assignInWith\n * @example\n *\n * function customizer(objValue, srcValue) {\n *   return _.isUndefined(objValue) ? srcValue : objValue;\n * }\n *\n * var defaults = _.partialRight(_.assignWith, customizer);\n *\n * defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar assignWith = createAssigner(function(object, source, srcIndex, customizer) {\n  copyObject(source, keys(source), object, customizer);\n});\n\nexport default assignWith;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAGF,cAAc,CAAC,UAASG,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7EP,UAAU,CAACK,MAAM,EAAEH,IAAI,CAACG,MAAM,CAAC,EAAED,MAAM,EAAEG,UAAU,CAAC;AACtD,CAAC,CAAC;AAEF,eAAeJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"app/layout/components/menu/vertical-menu/vertical-menu.component\";\nimport * as i3 from \"app/layout/components/menu/horizontal-menu/horizontal-menu.component\";\nfunction MenuComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"vertical-menu\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"horizontal-menu\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport class MenuComponent {\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {ElementRef} _elementRef\r\n   * @param {Renderer2} _renderer\r\n   */\n  constructor(_elementRef, _renderer) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    // Set the default menu\n    this._menuType = 'vertical-menu';\n  }\n  // Accessors\n  // -----------------------------------------------------------------------------------------------------\n  //Get the menu type\n  get menuType() {\n    return this._menuType;\n  }\n  set menuType(value) {\n    // Remove the old class name from native element\n    this._renderer.removeClass(this._elementRef.nativeElement, this.menuType);\n    // Store the menuType value\n    this._menuType = value;\n    // Add the new class name from native element\n    this._renderer.addClass(this._elementRef.nativeElement, value);\n  }\n  static #_ = this.ɵfac = function MenuComponent_Factory(t) {\n    return new (t || MenuComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MenuComponent,\n    selectors: [[\"app-menu\"]],\n    inputs: {\n      menuType: \"menuType\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[4, \"ngIf\"]],\n    template: function MenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MenuComponent_ng_container_0_Template, 2, 0, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, MenuComponent_ng_container_1_Template, 2, 0, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.menuType === \"vertical-menu\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.menuType === \"horizontal-menu\");\n      }\n    },\n    dependencies: [i1.NgIf, i2.VerticalMenuComponent, i3.HorizontalMenuComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;IACAA,EAAA,CAAAC,uBAAA,GAAmD;IACjDD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,qBAAA,EAAe;;;;;IAIfH,EAAA,CAAAC,uBAAA,GAAqD;IACnDD,EAAA,CAAAE,SAAA,sBAAmC;IACrCF,EAAA,CAAAG,qBAAA,EAAe;;;ACDf,OAAM,MAAOC,aAAa;EAGxB;;;;;;EAMAC,YAAoBC,WAAuB,EAAUC,SAAoB;IAArD,KAAAD,WAAW,GAAXA,WAAW;IAAsB,KAAAC,SAAS,GAATA,SAAS;IAC5D;IACA,IAAI,CAACC,SAAS,GAAG,eAAe;EAClC;EAEA;EACA;EAEA;EACA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACD,SAAS;EACvB;EAEA,IAEIC,QAAQA,CAACC,KAAa;IACxB;IACA,IAAI,CAACH,SAAS,CAACI,WAAW,CAAC,IAAI,CAACL,WAAW,CAACM,aAAa,EAAE,IAAI,CAACH,QAAQ,CAAC;IAEzE;IACA,IAAI,CAACD,SAAS,GAAGE,KAAK;IAEtB;IACA,IAAI,CAACH,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACP,WAAW,CAACM,aAAa,EAAEF,KAAK,CAAC;EAChE;EAAC,QAAAI,CAAA;qBAjCUV,aAAa,EAAAJ,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,UAAA,GAAAhB,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAiB,SAAA;EAAA;EAAA,QAAAC,EAAA;UAAbd,aAAa;IAAAe,SAAA;IAAAC,MAAA;MAAAX,QAAA;IAAA;IAAAY,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDP1B1B,EAAA,CAAA4B,UAAA,IAAAC,qCAAA,0BAEe;QAIf7B,EAAA,CAAA4B,UAAA,IAAAE,qCAAA,0BAEe;;;QARA9B,EAAA,CAAA+B,UAAA,SAAAJ,GAAA,CAAAlB,QAAA,qBAAkC;QAMlCT,EAAA,CAAAgC,SAAA,GAAoC;QAApChC,EAAA,CAAA+B,UAAA,SAAAJ,GAAA,CAAAlB,QAAA,uBAAoC", "names": ["i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "MenuComponent", "constructor", "_elementRef", "_renderer", "_menuType", "menuType", "value", "removeClass", "nativeElement", "addClass", "_", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "MenuComponent_Template", "rf", "ctx", "ɵɵtemplate", "MenuComponent_ng_container_0_Template", "MenuComponent_ng_container_1_Template", "ɵɵproperty", "ɵɵadvance"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\menu\\menu.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\layout\\components\\menu\\menu.component.ts"], "sourcesContent": ["<!-- Vertical Menu -->\r\n<ng-container *ngIf=\"menuType === 'vertical-menu'\">\r\n  <vertical-menu></vertical-menu>\r\n</ng-container>\r\n<!--/ Vertical Menu -->\r\n\r\n<!-- Horizontal Menu -->\r\n<ng-container *ngIf=\"menuType === 'horizontal-menu'\">\r\n  <horizontal-menu></horizontal-menu>\r\n</ng-container>\r\n<!--/ Horizontal Menu -->\r\n", "import { Component, ElementRef, Input, Renderer2, ViewEncapsulation } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-menu',\r\n  templateUrl: './menu.component.html',\r\n  styleUrls: ['./menu.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class MenuComponent {\r\n  private _menuType: string;\r\n\r\n  /**\r\n   * Constructor\r\n   *\r\n   * @param {ElementRef} _elementRef\r\n   * @param {Renderer2} _renderer\r\n   */\r\n  constructor(private _elementRef: ElementRef, private _renderer: Renderer2) {\r\n    // Set the default menu\r\n    this._menuType = 'vertical-menu';\r\n  }\r\n\r\n  // Accessors\r\n  // -----------------------------------------------------------------------------------------------------\r\n\r\n  //Get the menu type\r\n  get menuType(): string {\r\n    return this._menuType;\r\n  }\r\n\r\n  @Input()\r\n  //Set the menu type to the native element\r\n  set menuType(value: string) {\r\n    // Remove the old class name from native element\r\n    this._renderer.removeClass(this._elementRef.nativeElement, this.menuType);\r\n\r\n    // Store the menuType value\r\n    this._menuType = value;\r\n\r\n    // Add the new class name from native element\r\n    this._renderer.addClass(this._elementRef.nativeElement, value);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
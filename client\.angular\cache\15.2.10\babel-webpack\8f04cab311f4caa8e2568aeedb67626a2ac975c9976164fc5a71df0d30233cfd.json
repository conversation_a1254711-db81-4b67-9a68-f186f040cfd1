{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/layout/components/navbar/navbar-notification/notifications.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"app/layout/components/content-header/content-header.component\";\nimport * as i7 from \"@core/pipes/filter.pipe\";\nimport * as i8 from \"@core/pipes/stripHtml.pipe\";\nimport * as i9 from \"../../../../@core/pipes/localized-date.pipe\";\nfunction AllNotificationsComponent_div_32_ng_container_4_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(2, 3, \"From\"), \": \", (ctx_r7.name_settings == null ? null : ctx_r7.name_settings.is_on) == 1 ? (item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by == null ? null : item_r2.message.send_by.first_name) + \" \" + (item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by == null ? null : item_r2.message.send_by.last_name) : (item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by == null ? null : item_r2.message.send_by.last_name) + \" \" + (item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by == null ? null : item_r2.message.send_by.first_name), \" (\", item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by == null ? null : item_r2.message.send_by.email, \") \");\n  }\n}\nfunction AllNotificationsComponent_div_32_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AllNotificationsComponent_div_32_ng_container_4_small_1_Template, 3, 5, \"small\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r2.message && !ctx_r3.isString(item_r2.message));\n  }\n}\nfunction AllNotificationsComponent_div_32_ng_container_5_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"From\"), \": System \");\n  }\n}\nfunction AllNotificationsComponent_div_32_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AllNotificationsComponent_div_32_ng_container_5_small_1_Template, 3, 3, \"small\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r2.message && !ctx_r4.isString(item_r2.message));\n  }\n}\nfunction AllNotificationsComponent_div_32_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, item_r2.message), \" \");\n  }\n}\nfunction AllNotificationsComponent_div_32_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"h6\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 27)(6, \"div\", 28)(7, \"a\", 29);\n    i0.ɵɵelement(8, \"i\", 30);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const attachment_r14 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r14.filename.length > 50 ? i0.ɵɵpipeBind3(4, 3, attachment_r14.filename, 0, 50) + \"...\" : attachment_r14.filename, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"href\", attachment_r14.url, i0.ɵɵsanitizeUrl)(\"download\", attachment_r14.filename);\n  }\n}\nfunction AllNotificationsComponent_div_32_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, AllNotificationsComponent_div_32_div_12_div_1_Template, 9, 7, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", item_r2.message == null ? null : item_r2.message.attachments);\n  }\n}\nfunction AllNotificationsComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h4\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AllNotificationsComponent_div_32_ng_container_4_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵtemplate(5, AllNotificationsComponent_div_32_ng_container_5_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelement(6, \"p\", 17);\n    i0.ɵɵpipe(7, \"striphtml\");\n    i0.ɵɵtemplate(8, AllNotificationsComponent_div_32_p_8_Template, 3, 3, \"p\", 18);\n    i0.ɵɵelementStart(9, \"small\", 19);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localizedDate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, AllNotificationsComponent_div_32_div_12_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.message == null ? null : item_r2.message.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(item_r2 == null ? null : item_r2.message == null ? null : item_r2.message.send_by));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", item_r2.message ? item_r2.message == null ? null : item_r2.message.content : i0.ɵɵpipeBind1(7, 7, \"\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r2.message && ctx_r1.isString(item_r2.message));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 9, item_r2.message == null ? null : item_r2.message.created_at, \"yyyy-MM-dd hh:mm\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r2.message == null ? null : item_r2.message.attachments);\n  }\n}\nexport class AllNotificationsComponent {\n  constructor(_notficationsService, _translateService, _titleService) {\n    this._notficationsService = _notficationsService;\n    this._translateService = _translateService;\n    this._titleService = _titleService;\n    this.notificationsData = [];\n    this.filter = null;\n    this._titleService.setTitle('Notifications');\n    this.getAllNotifications();\n    this.contentHeader = {\n      headerTitle: _translateService.instant('All Notifications'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: []\n      }\n    };\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\n  }\n  getAllNotifications() {\n    this._notficationsService.getAllNotifications().subscribe(res => {\n      this.notificationsData = res.messages;\n      console.log(this.notificationsData);\n    });\n  }\n  ngOnInit() {}\n  tabClick(type) {\n    switch (type) {\n      case 'all':\n        this.filter = null;\n        break;\n      case 'unread':\n        this.filter = '0';\n        break;\n    }\n    console.log(this.filter);\n  }\n  isString(val) {\n    return typeof val === 'string';\n  }\n  static #_ = this.ɵfac = function AllNotificationsComponent_Factory(t) {\n    return new (t || AllNotificationsComponent)(i0.ɵɵdirectiveInject(i1.NotificationsService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AllNotificationsComponent,\n    selectors: [[\"app-all-notifications\"]],\n    decls: 34,\n    vars: 27,\n    consts: [[3, \"contentHeader\"], [1, \"card\", \"mb-25\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"navWithIcons\", \"ngbNav\"], [\"ngbNavItem\", \"\"], [\"ngbNavLink\", \"\", 3, \"click\"], [1, \"fa-regular\", \"fa-envelopes-bulk\"], [1, \"fa-solid\", \"fa-bell-slash\"], [1, \"card\", \"my-1\", 2, \"background-color\", \"#dcdcdc\", \"color\", \"#6f6f6f\"], [1, \"card-header\", 2, \"padding-bottom\", \"10px\"], [2, \"font-size\", \"16px\", \"font-weight\", \"700\"], [1, \"card-body\"], [\"class\", \"card text-left\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\", \"text-left\"], [1, \"card-body\", \"p-1\"], [1, \"card-title\", \"mb-0\"], [4, \"ngIf\"], [1, \"card-text\", \"mt-1\", 3, \"innerHTML\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"text-muted\"], [\"class\", \"modal-footer justify-content-start p-50\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"modal-footer\", \"justify-content-start\", \"p-50\"], [\"class\", \"card attachment\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\", \"attachment\"], [1, \"card-body\", \"border\", \"border-secondary\", \"rounded\", \"p-50\"], [1, \"row\"], [1, \"col\"], [\"target\", \"_blank\", 1, \"btn\", \"btn-flat-secondary\", \"p-50\", \"float-right\", 3, \"href\", \"download\"], [1, \"fa-duotone\", \"fa-download\"]],\n    template: function AllNotificationsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-content-header\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"ul\", 2, 3)(4, \"li\", 4)(5, \"a\", 5);\n        i0.ɵɵlistener(\"click\", function AllNotificationsComponent_Template_a_click_5_listener() {\n          return ctx.tabClick(\"all\");\n        });\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"li\", 4)(10, \"a\", 5);\n        i0.ɵɵlistener(\"click\", function AllNotificationsComponent_Template_a_click_10_listener() {\n          return ctx.tabClick(\"unread\");\n        });\n        i0.ɵɵelement(11, \"i\", 7);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"div\", 10);\n        i0.ɵɵtext(17);\n        i0.ɵɵpipe(18, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 11)(20, \"div\")(21, \"b\");\n        i0.ɵɵtext(22);\n        i0.ɵɵpipe(23, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(24);\n        i0.ɵɵpipe(25, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\")(27, \"b\");\n        i0.ɵɵtext(28);\n        i0.ɵɵpipe(29, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(30);\n        i0.ɵɵpipe(31, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(32, AllNotificationsComponent_div_32_Template, 13, 12, \"div\", 12);\n        i0.ɵɵpipe(33, \"filter\");\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 9, \"All\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, \"Unread\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 13, \"To receive notifications, you need to grant notification permissions to get updates\"), \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(23, 15, \"Website\"), \":\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 17, \"Open your browser settings and allow notifications\"), \". \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(29, 19, \"Mobile\"), \":\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 21, \"Go to your device settings and enable notifications for the app\"), \". \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(33, 23, ctx.notificationsData, ctx.filter, \"read\"));\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.NgbNav, i5.NgbNavItem, i5.NgbNavLink, i6.ContentHeaderComponent, i4.SlicePipe, i7.FilterPipe, i8.StripHtmlPipe, i9.LocalizedDatePipe, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;IA6CMA,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAE,MAAA,GAQF;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IARNH,EAAA,CAAAI,SAAA,GAQF;IAREJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAM,WAAA,uBAAAC,MAAA,CAAAC,aAAA,kBAAAD,MAAA,CAAAC,aAAA,CAAAC,KAAA,UAAAC,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,kBAAAF,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,UAAA,WAAAH,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,kBAAAF,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAE,SAAA,KAAAJ,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,kBAAAF,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAE,SAAA,WAAAJ,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,kBAAAF,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAC,UAAA,SAAAH,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,kBAAAF,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAG,KAAA,OAQF;;;;;IAVFf,EAAA,CAAAgB,uBAAA,GAA6C;IAC3ChB,EAAA,CAAAiB,UAAA,IAAAC,gEAAA,oBASQ;IACVlB,EAAA,CAAAmB,qBAAA,EAAe;;;;;IAVcnB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAoB,UAAA,SAAAV,OAAA,CAAAC,OAAA,KAAAU,MAAA,CAAAC,QAAA,CAAAZ,OAAA,CAAAC,OAAA,EAA6C;;;;;IAYxEX,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;IADNH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAM,WAAA,4BACF;;;;;IAHFN,EAAA,CAAAgB,uBAAA,GAA8C;IAC5ChB,EAAA,CAAAiB,UAAA,IAAAO,gEAAA,oBAEQ;IACVxB,EAAA,CAAAmB,qBAAA,EAAe;;;;;IAHcnB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAoB,UAAA,SAAAV,OAAA,CAAAC,OAAA,KAAAc,MAAA,CAAAH,QAAA,CAAAZ,OAAA,CAAAC,OAAA,EAA6C;;;;;IAQ1EX,EAAA,CAAAC,cAAA,YAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAM,WAAA,OAAAI,OAAA,CAAAC,OAAA,OACF;;;;;IASAX,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,MAAA,GAKF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiB;IAWXD,EAAA,CAAA0B,SAAA,YAAsC;IACxC1B,EAAA,CAAAG,YAAA,EAAI;;;;IAlBNH,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAuB,kBAAA,MAAAI,cAAA,CAAAC,QAAA,CAAAC,MAAA,QAAA7B,EAAA,CAAA8B,WAAA,OAAAH,cAAA,CAAAC,QAAA,mBAAAD,cAAA,CAAAC,QAAA,MAKF;IASM5B,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAoB,UAAA,SAAAO,cAAA,CAAAI,GAAA,EAAA/B,EAAA,CAAAgC,aAAA,CAAuB,aAAAL,cAAA,CAAAC,QAAA;;;;;IAxBnC5B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiB,UAAA,IAAAgB,sDAAA,kBA4BM;IACRjC,EAAA,CAAAG,YAAA,EAAM;;;;IA3BqBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAoB,UAAA,YAAAV,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAuB,WAAA,CAA4B;;;;;IAxCzDlC,EAAA,CAAAC,cAAA,cAGC;IAE+BD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAiB,UAAA,IAAAkB,wDAAA,2BAWe;IACfnC,EAAA,CAAAiB,UAAA,IAAAmB,wDAAA,2BAIe;IACfpC,EAAA,CAAA0B,SAAA,YAGK;;IACL1B,EAAA,CAAAiB,UAAA,IAAAoB,6CAAA,gBAEI;IACJrC,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAE,MAAA,IAAmE;;IAAAF,EAAA,CAAAG,YAAA,EACpE;IAEHH,EAAA,CAAAiB,UAAA,KAAAqB,gDAAA,kBAiCM;IACRtC,EAAA,CAAAG,YAAA,EAAM;;;;;IA/D0BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAuC,iBAAA,CAAA7B,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAA6B,KAAA,CAAyB;IACtCxC,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAoB,UAAA,SAAAV,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,CAA4B;IAY5BZ,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAoB,UAAA,WAAAV,OAAA,kBAAAA,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAC,OAAA,EAA6B;IAO1CZ,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAoB,UAAA,cAAAV,OAAA,CAAAC,OAAA,GAAAD,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAA8B,OAAA,GAAAzC,EAAA,CAAAM,WAAA,YAAAN,EAAA,CAAA0C,cAAA,CAAqE;IAE/C1C,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAoB,UAAA,SAAAV,OAAA,CAAAC,OAAA,IAAAgC,MAAA,CAAArB,QAAA,CAAAZ,OAAA,CAAAC,OAAA,EAA4C;IAIlEX,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAA4C,WAAA,QAAAlC,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAkC,UAAA,0BAAmE;IAKpE7C,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAoB,UAAA,SAAAV,OAAA,CAAAC,OAAA,kBAAAD,OAAA,CAAAC,OAAA,CAAAuB,WAAA,CAA+B;;;AChEpC,OAAM,MAAOY,yBAAyB;EAKpCC,YACSC,oBAA0C,EAC1CC,iBAAmC,EACnCC,aAAoB;IAFpB,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IANtB,KAAAC,iBAAiB,GAAQ,EAAE;IAC3B,KAAAC,MAAM,GAAQ,IAAI;IAOhB,IAAI,CAACF,aAAa,CAACG,QAAQ,CAAC,eAAe,CAAC;IAC5C,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAEP,iBAAiB,CAACQ,OAAO,CAAC,mBAAmB,CAAC;MAC3DC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;;KAEV;IACD,IAAI,CAACrD,aAAa,GAAGsD,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;EACxE;EAEAX,mBAAmBA,CAAA;IACjB,IAAI,CAACN,oBAAoB,CAACM,mBAAmB,EAAE,CAACY,SAAS,CAAEC,GAAQ,IAAI;MACrE,IAAI,CAAChB,iBAAiB,GAAGgB,GAAG,CAACC,QAAQ;MACrCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnB,iBAAiB,CAAC;IACrC,CAAC,CAAC;EACJ;EAEAoB,QAAQA,CAAA,GAAU;EAClBC,QAAQA,CAACZ,IAAI;IACX,QAAQA,IAAI;MACV,KAAK,KAAK;QACR,IAAI,CAACR,MAAM,GAAG,IAAI;QAClB;MACF,KAAK,QAAQ;QACX,IAAI,CAACA,MAAM,GAAG,GAAG;QACjB;;IAEJiB,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClB,MAAM,CAAC;EAC1B;EAEA9B,QAAQA,CAACmD,GAAG;IACV,OAAO,OAAOA,GAAG,KAAK,QAAQ;EAChC;EAAC,QAAAC,CAAA;qBA7CU5B,yBAAyB,EAAA9C,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA7E,EAAA,CAAA2E,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA/E,EAAA,CAAA2E,iBAAA,CAAAK,EAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAzBpC,yBAAyB;IAAAqC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDVtCzF,EAAA,CAAA0B,SAAA,4BAAyE;QACzE1B,EAAA,CAAAC,cAAA,aAAwB;QAGJD,EAAA,CAAA2F,UAAA,mBAAAC,sDAAA;UAAA,OAASF,GAAA,CAAAlB,QAAA,CAAS,KAAK,CAAC;QAAA,EAAC;QACtCxE,EAAA,CAAA0B,SAAA,WAA4C;QAAA1B,EAAA,CAAAE,MAAA,GAAuB;;QAAAF,EAAA,CAAAG,YAAA,EACnE;QAEHH,EAAA,CAAAC,cAAA,YAAe;QACCD,EAAA,CAAA2F,UAAA,mBAAAE,uDAAA;UAAA,OAASH,GAAA,CAAAlB,QAAA,CAAS,QAAQ,CAAC;QAAA,EAAC;QACzCxE,EAAA,CAAA0B,SAAA,YAAsC;QAAA1B,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAChE;QAIPH,EAAA,CAAAC,cAAA,cAAyE;QAGnED,EAAA,CAAAE,MAAA,IAIF;;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,eAAuB;QAEhBD,EAAA,CAAAE,MAAA,IAA4B;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACnCH,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,WAAK;QACAD,EAAA,CAAAE,MAAA,IAA2B;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAClCH,EAAA,CAAAE,MAAA,IAIF;;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAGVH,EAAA,CAAAiB,UAAA,KAAA6E,yCAAA,oBAoEM;;;;QA1Gc9F,EAAA,CAAAoB,UAAA,kBAAAsE,GAAA,CAAAnC,aAAA,CAA+B;QAKAvD,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAM,WAAA,cAAuB;QAK7BN,EAAA,CAAAI,SAAA,GAA0B;QAA1BJ,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAM,WAAA,mBAA0B;QAQjEN,EAAA,CAAAI,SAAA,GAIF;QAJEJ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAM,WAAA,qGAIF;QAIKN,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAuB,kBAAA,KAAAvB,EAAA,CAAAM,WAAA,yBAA4B;QAC/BN,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAM,WAAA,qEACF;QAEKN,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAuB,kBAAA,KAAAvB,EAAA,CAAAM,WAAA,wBAA2B;QAC9BN,EAAA,CAAAI,SAAA,GAIF;QAJEJ,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAM,WAAA,kFAIF;QAKeN,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAA8B,WAAA,SAAA4D,GAAA,CAAAvC,iBAAA,EAAAuC,GAAA,CAAAtC,MAAA,UAA+C", "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate3", "ɵɵpipeBind1", "ctx_r7", "name_settings", "is_on", "item_r2", "message", "send_by", "first_name", "last_name", "email", "ɵɵelementContainerStart", "ɵɵtemplate", "AllNotificationsComponent_div_32_ng_container_4_small_1_Template", "ɵɵelementContainerEnd", "ɵɵproperty", "ctx_r3", "isString", "ɵɵtextInterpolate1", "AllNotificationsComponent_div_32_ng_container_5_small_1_Template", "ctx_r4", "ɵɵelement", "attachment_r14", "filename", "length", "ɵɵpipeBind3", "url", "ɵɵsanitizeUrl", "AllNotificationsComponent_div_32_div_12_div_1_Template", "attachments", "AllNotificationsComponent_div_32_ng_container_4_Template", "AllNotificationsComponent_div_32_ng_container_5_Template", "AllNotificationsComponent_div_32_p_8_Template", "AllNotificationsComponent_div_32_div_12_Template", "ɵɵtextInterpolate", "title", "content", "ɵɵsanitizeHtml", "ctx_r1", "ɵɵpipeBind2", "created_at", "AllNotificationsComponent", "constructor", "_notficationsService", "_translateService", "_titleService", "notificationsData", "filter", "setTitle", "getAllNotifications", "contentHeader", "headerTitle", "instant", "actionButton", "breadcrumb", "type", "links", "JSON", "parse", "localStorage", "getItem", "subscribe", "res", "messages", "console", "log", "ngOnInit", "tabClick", "val", "_", "ɵɵdirectiveInject", "i1", "NotificationsService", "i2", "TranslateService", "i3", "Title", "_2", "selectors", "decls", "vars", "consts", "template", "AllNotificationsComponent_Template", "rf", "ctx", "ɵɵlistener", "AllNotificationsComponent_Template_a_click_5_listener", "AllNotificationsComponent_Template_a_click_10_listener", "AllNotificationsComponent_div_32_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\messages\\all-notifications\\all-notifications.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\messages\\all-notifications\\all-notifications.component.ts"], "sourcesContent": ["<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n<div class=\"card mb-25\">\r\n  <ul ngbNav #navWithIcons=\"ngbNav\" class=\"nav-tabs m-0\">\r\n    <li ngbNavItem>\r\n      <a ngbNavLink (click)=\"tabClick('all')\"\r\n      ><i class=\"fa-regular fa-envelopes-bulk\"></i>{{ 'All' | translate }}</a\r\n      >\r\n    </li>\r\n    <li ngbNavItem>\r\n      <a ngbNavLink (click)=\"tabClick('unread')\"\r\n      ><i class=\"fa-solid fa-bell-slash\"></i>{{ 'Unread' | translate }}</a\r\n      >\r\n    </li>\r\n  </ul>\r\n</div>\r\n<div class=\"card my-1\" style=\"background-color: #dcdcdc; color: #6f6f6f\">\r\n  <div class=\"card-header\" style=\"padding-bottom: 10px\">\r\n    <div style=\"font-size: 16px; font-weight: 700\">\r\n      {{\r\n        'To receive notifications, you need to grant notification permissions to get updates'\r\n          | translate\r\n      }}\r\n    </div>\r\n  </div>\r\n  <div class=\"card-body\">\r\n    <div>\r\n      <b>{{ 'Website' | translate }}:</b>\r\n      {{ 'Open your browser settings and allow notifications' | translate }}.\r\n    </div>\r\n    <div>\r\n      <b>{{ 'Mobile' | translate }}:</b>\r\n      {{\r\n        'Go to your device settings and enable notifications for the app'\r\n          | translate\r\n      }}.\r\n    </div>\r\n  </div>\r\n</div>\r\n<div\r\n  class=\"card text-left\"\r\n  *ngFor=\"let item of notificationsData | filter : filter : 'read'\"\r\n>\r\n  <div class=\"card-body p-1\">\r\n    <h4 class=\"card-title mb-0\">{{ item.message?.title }}</h4>\r\n    <ng-container *ngIf=\"item?.message?.send_by\">\r\n      <small class=\"text-muted\" *ngIf=\"item.message && !isString(item.message)\">\r\n        {{ 'From' | translate }}: {{\r\n          name_settings?.is_on == 1\r\n            ? item?.message?.send_by?.first_name + ' ' + item?.message?.send_by?.last_name\r\n            : item?.message?.send_by?.last_name + ' ' + item?.message?.send_by?.first_name\r\n        }}\r\n        ({{\r\n          item?.message?.send_by?.email\r\n        }})\r\n      </small>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"!item?.message?.send_by\">\r\n      <small class=\"text-muted\" *ngIf=\"item.message && !isString(item.message)\">\r\n        {{ 'From' | translate }}: System\r\n      </small>\r\n    </ng-container>\r\n    <p\r\n      class=\"card-text mt-1\"\r\n      [innerHTML]=\"item.message ? item.message?.content : ('' | striphtml)\"\r\n    ></p>\r\n    <p class=\"text-center\" *ngIf=\"item.message && isString(item.message)\">\r\n      {{ item.message | translate }}\r\n    </p>\r\n    <small class=\"text-muted\">\r\n      {{ item.message?.created_at | localizedDate : 'yyyy-MM-dd hh:mm' }}</small\r\n    >\r\n  </div>\r\n  <div\r\n    class=\"modal-footer justify-content-start p-50\"\r\n    *ngIf=\"item.message?.attachments\"\r\n  >\r\n    <div\r\n      class=\"card attachment\"\r\n      *ngFor=\"let attachment of item.message?.attachments\"\r\n    >\r\n      <div class=\"card-body border border-secondary rounded p-50\">\r\n        <h6>\r\n          {{\r\n            attachment.filename.length > 50\r\n              ? (attachment.filename | slice : 0 : 50) + '...'\r\n              : attachment.filename\r\n          }}\r\n        </h6>\r\n        <div class=\"row\">\r\n          <!-- <div class=\"col\">\r\n                        <p class=\"text-muted\">{{attachment.size/ 1024 / 1024 | number: '.2'}}MB</p>\r\n                    </div> -->\r\n          <div class=\"col\">\r\n            <a\r\n              target=\"_blank\"\r\n              class=\"btn btn-flat-secondary p-50 float-right\"\r\n              [href]=\"attachment.url\"\r\n              [download]=\"attachment.filename\"\r\n            >\r\n              <i class=\"fa-duotone fa-download\"></i>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { NotificationsService } from 'app/layout/components/navbar/navbar-notification/notifications.service';\r\n\r\n@Component({\r\n  selector: 'app-all-notifications',\r\n  templateUrl: './all-notifications.component.html',\r\n  styleUrls: ['./all-notifications.component.scss'],\r\n})\r\nexport class AllNotificationsComponent implements OnInit {\r\n  public contentHeader: object;\r\n  notificationsData: any = [];\r\n  filter: any = null;\r\n  public name_settings : any;\r\n  constructor(\r\n    public _notficationsService: NotificationsService,\r\n    public _translateService: TranslateService,\r\n    public _titleService: Title\r\n  ) {\r\n    this._titleService.setTitle('Notifications');\r\n    this.getAllNotifications();\r\n    this.contentHeader = {\r\n      headerTitle: _translateService.instant('All Notifications'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [],\r\n      },\r\n    };\r\n    this.name_settings = JSON.parse(localStorage.getItem('name_settings'));\r\n  }\r\n\r\n  getAllNotifications() {\r\n    this._notficationsService.getAllNotifications().subscribe((res: any) => {\r\n      this.notificationsData = res.messages;\r\n      console.log(this.notificationsData);\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n  tabClick(type) {\r\n    switch (type) {\r\n      case 'all':\r\n        this.filter = null;\r\n        break;\r\n      case 'unread':\r\n        this.filter = '0';\r\n        break;\r\n    }\r\n    console.log(this.filter);\r\n  }\r\n\r\n  isString(val) {\r\n    return typeof val === 'string';\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
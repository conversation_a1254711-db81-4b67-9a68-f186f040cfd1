{"ast": null, "code": "import { FeatherIconDirective } from '@core/directives/core-feather-icons/core-feather-icons';\nimport { RippleEffectDirective } from '@core/directives/core-ripple-effect/core-ripple-effect.directive';\nimport * as i0 from \"@angular/core\";\nexport class CoreDirectivesModule {\n  static #_ = this.ɵfac = function CoreDirectivesModule_Factory(t) {\n    return new (t || CoreDirectivesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreDirectivesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({});\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreDirectivesModule, {\n    declarations: [RippleEffectDirective, FeatherIconDirective],\n    exports: [RippleEffectDirective, FeatherIconDirective]\n  });\n})();", "map": {"version": 3, "mappings": "AAEA,SAASA,oBAAoB,QAAQ,wDAAwD;AAC7F,SAASC,qBAAqB,QAAQ,kEAAkE;;AAMxG,OAAM,MAAOC,oBAAoB;EAAA,QAAAC,CAAA;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA;UAApBF;EAAoB;EAAA,QAAAG,EAAA;;;2EAApBH,oBAAoB;IAAAI,YAAA,GAHhBL,qBAAqB,EAAED,oBAAoB;IAAAO,OAAA,GAChDN,qBAAqB,EAAED,oBAAoB;EAAA;AAAA", "names": ["FeatherIconDirective", "RippleEffectDirective", "CoreDirectivesModule", "_", "_2", "_3", "declarations", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\directives\\directives.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { FeatherIconDirective } from '@core/directives/core-feather-icons/core-feather-icons';\r\nimport { RippleEffectDirective } from '@core/directives/core-ripple-effect/core-ripple-effect.directive';\r\n\r\n@NgModule({\r\n  declarations: [RippleEffectDirective, FeatherIconDirective],\r\n  exports: [RippleEffectDirective, FeatherIconDirective]\r\n})\r\nexport class CoreDirectivesModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
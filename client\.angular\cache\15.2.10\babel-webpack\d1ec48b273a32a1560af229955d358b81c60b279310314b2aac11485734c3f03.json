{"ast": null, "code": "import { Observable } from '../Observable';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { scheduled } from '../scheduled/scheduled';\nexport function from(input, scheduler) {\n  if (!scheduler) {\n    if (input instanceof Observable) {\n      return input;\n    }\n    return new Observable(subscribeTo(input));\n  } else {\n    return scheduled(input, scheduler);\n  }\n}", "map": {"version": 3, "names": ["Observable", "subscribeTo", "scheduled", "from", "input", "scheduler"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/observable/from.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { scheduled } from '../scheduled/scheduled';\nexport function from(input, scheduler) {\n    if (!scheduler) {\n        if (input instanceof Observable) {\n            return input;\n        }\n        return new Observable(subscribeTo(input));\n    }\n    else {\n        return scheduled(input, scheduler);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnC,IAAI,CAACA,SAAS,EAAE;IACZ,IAAID,KAAK,YAAYJ,UAAU,EAAE;MAC7B,OAAOI,KAAK;IAChB;IACA,OAAO,IAAIJ,UAAU,CAACC,WAAW,CAACG,KAAK,CAAC,CAAC;EAC7C,CAAC,MACI;IACD,OAAOF,SAAS,CAACE,KAAK,EAAEC,SAAS,CAAC;EACtC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
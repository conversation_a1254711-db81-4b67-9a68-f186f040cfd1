{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function scheduleObservable(input, scheduler) {\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    sub.add(scheduler.schedule(() => {\n      const observable = input[Symbol_observable]();\n      sub.add(observable.subscribe({\n        next(value) {\n          sub.add(scheduler.schedule(() => subscriber.next(value)));\n        },\n        error(err) {\n          sub.add(scheduler.schedule(() => subscriber.error(err)));\n        },\n        complete() {\n          sub.add(scheduler.schedule(() => subscriber.complete()));\n        }\n      }));\n    }));\n    return sub;\n  });\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "observable", "Symbol_observable", "scheduleObservable", "input", "scheduler", "subscriber", "sub", "add", "schedule", "subscribe", "next", "value", "error", "err", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduled/scheduleObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function scheduleObservable(input, scheduler) {\n    return new Observable(subscriber => {\n        const sub = new Subscription();\n        sub.add(scheduler.schedule(() => {\n            const observable = input[Symbol_observable]();\n            sub.add(observable.subscribe({\n                next(value) { sub.add(scheduler.schedule(() => subscriber.next(value))); },\n                error(err) { sub.add(scheduler.schedule(() => subscriber.error(err))); },\n                complete() { sub.add(scheduler.schedule(() => subscriber.complete())); },\n            }));\n        }));\n        return sub;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,sBAAsB;AACtE,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACjD,OAAO,IAAIN,UAAU,CAACO,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAG,IAAIP,YAAY,CAAC,CAAC;IAC9BO,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAM;MAC7B,MAAMR,UAAU,GAAGG,KAAK,CAACF,iBAAiB,CAAC,CAAC,CAAC;MAC7CK,GAAG,CAACC,GAAG,CAACP,UAAU,CAACS,SAAS,CAAC;QACzBC,IAAIA,CAACC,KAAK,EAAE;UAAEL,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAMH,UAAU,CAACK,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;QAAE,CAAC;QAC1EC,KAAKA,CAACC,GAAG,EAAE;UAAEP,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAMH,UAAU,CAACO,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC;QACxEC,QAAQA,CAAA,EAAG;UAAER,GAAG,CAACC,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,MAAMH,UAAU,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC;QAAE;MAC3E,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,OAAOR,GAAG;EACd,CAAC,CAAC;AACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
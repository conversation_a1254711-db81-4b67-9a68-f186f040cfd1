{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function find(predicate, thisArg) {\n  if (typeof predicate !== 'function') {\n    throw new TypeError('predicate is not a function');\n  }\n  return source => source.lift(new FindValueOperator(predicate, source, false, thisArg));\n}\nexport class FindValueOperator {\n  constructor(predicate, source, yieldIndex, thisArg) {\n    this.predicate = predicate;\n    this.source = source;\n    this.yieldIndex = yieldIndex;\n    this.thisArg = thisArg;\n  }\n  call(observer, source) {\n    return source.subscribe(new FindValueSubscriber(observer, this.predicate, this.source, this.yieldIndex, this.thisArg));\n  }\n}\nexport class FindValueSubscriber extends Subscriber {\n  constructor(destination, predicate, source, yieldIndex, thisArg) {\n    super(destination);\n    this.predicate = predicate;\n    this.source = source;\n    this.yieldIndex = yieldIndex;\n    this.thisArg = thisArg;\n    this.index = 0;\n  }\n  notifyComplete(value) {\n    const destination = this.destination;\n    destination.next(value);\n    destination.complete();\n    this.unsubscribe();\n  }\n  _next(value) {\n    const {\n      predicate,\n      thisArg\n    } = this;\n    const index = this.index++;\n    try {\n      const result = predicate.call(thisArg || this, value, index, this.source);\n      if (result) {\n        this.notifyComplete(this.yieldIndex ? index : value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n  _complete() {\n    this.notifyComplete(this.yieldIndex ? -1 : undefined);\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "find", "predicate", "thisArg", "TypeError", "source", "lift", "FindValueOperator", "constructor", "yieldIndex", "call", "observer", "subscribe", "FindValueSubscriber", "destination", "index", "notifyComplete", "value", "next", "complete", "unsubscribe", "_next", "result", "err", "error", "_complete", "undefined"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/find.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function find(predicate, thisArg) {\n    if (typeof predicate !== 'function') {\n        throw new TypeError('predicate is not a function');\n    }\n    return (source) => source.lift(new FindValueOperator(predicate, source, false, thisArg));\n}\nexport class FindValueOperator {\n    constructor(predicate, source, yieldIndex, thisArg) {\n        this.predicate = predicate;\n        this.source = source;\n        this.yieldIndex = yieldIndex;\n        this.thisArg = thisArg;\n    }\n    call(observer, source) {\n        return source.subscribe(new FindValueSubscriber(observer, this.predicate, this.source, this.yieldIndex, this.thisArg));\n    }\n}\nexport class FindValueSubscriber extends Subscriber {\n    constructor(destination, predicate, source, yieldIndex, thisArg) {\n        super(destination);\n        this.predicate = predicate;\n        this.source = source;\n        this.yieldIndex = yieldIndex;\n        this.thisArg = thisArg;\n        this.index = 0;\n    }\n    notifyComplete(value) {\n        const destination = this.destination;\n        destination.next(value);\n        destination.complete();\n        this.unsubscribe();\n    }\n    _next(value) {\n        const { predicate, thisArg } = this;\n        const index = this.index++;\n        try {\n            const result = predicate.call(thisArg || this, value, index, this.source);\n            if (result) {\n                this.notifyComplete(this.yieldIndex ? index : value);\n            }\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n    _complete() {\n        this.notifyComplete(this.yieldIndex ? -1 : undefined);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,IAAIA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACrC,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;IACjC,MAAM,IAAIE,SAAS,CAAC,6BAA6B,CAAC;EACtD;EACA,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,iBAAiB,CAACL,SAAS,EAAEG,MAAM,EAAE,KAAK,EAAEF,OAAO,CAAC,CAAC;AAC5F;AACA,OAAO,MAAMI,iBAAiB,CAAC;EAC3BC,WAAWA,CAACN,SAAS,EAAEG,MAAM,EAAEI,UAAU,EAAEN,OAAO,EAAE;IAChD,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACN,OAAO,GAAGA,OAAO;EAC1B;EACAO,IAAIA,CAACC,QAAQ,EAAEN,MAAM,EAAE;IACnB,OAAOA,MAAM,CAACO,SAAS,CAAC,IAAIC,mBAAmB,CAACF,QAAQ,EAAE,IAAI,CAACT,SAAS,EAAE,IAAI,CAACG,MAAM,EAAE,IAAI,CAACI,UAAU,EAAE,IAAI,CAACN,OAAO,CAAC,CAAC;EAC1H;AACJ;AACA,OAAO,MAAMU,mBAAmB,SAASb,UAAU,CAAC;EAChDQ,WAAWA,CAACM,WAAW,EAAEZ,SAAS,EAAEG,MAAM,EAAEI,UAAU,EAAEN,OAAO,EAAE;IAC7D,KAAK,CAACW,WAAW,CAAC;IAClB,IAAI,CAACZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACY,KAAK,GAAG,CAAC;EAClB;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,MAAMH,WAAW,GAAG,IAAI,CAACA,WAAW;IACpCA,WAAW,CAACI,IAAI,CAACD,KAAK,CAAC;IACvBH,WAAW,CAACK,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACAC,KAAKA,CAACJ,KAAK,EAAE;IACT,MAAM;MAAEf,SAAS;MAAEC;IAAQ,CAAC,GAAG,IAAI;IACnC,MAAMY,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;IAC1B,IAAI;MACA,MAAMO,MAAM,GAAGpB,SAAS,CAACQ,IAAI,CAACP,OAAO,IAAI,IAAI,EAAEc,KAAK,EAAEF,KAAK,EAAE,IAAI,CAACV,MAAM,CAAC;MACzE,IAAIiB,MAAM,EAAE;QACR,IAAI,CAACN,cAAc,CAAC,IAAI,CAACP,UAAU,GAAGM,KAAK,GAAGE,KAAK,CAAC;MACxD;IACJ,CAAC,CACD,OAAOM,GAAG,EAAE;MACR,IAAI,CAACT,WAAW,CAACU,KAAK,CAACD,GAAG,CAAC;IAC/B;EACJ;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACT,cAAc,CAAC,IAAI,CAACP,UAAU,GAAG,CAAC,CAAC,GAAGiB,SAAS,CAAC;EACzD;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
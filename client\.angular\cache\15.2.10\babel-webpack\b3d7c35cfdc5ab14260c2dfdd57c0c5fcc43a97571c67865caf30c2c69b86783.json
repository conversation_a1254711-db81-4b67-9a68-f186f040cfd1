{"ast": null, "code": "import { environment } from './../../../../environments/environment';\nimport { EVENT_ACTIVE, EVENT_ARCHIVED } from './events-constants';\nimport { DataTableDirective } from 'angular-datatables';\nimport { Subject } from 'rxjs';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/settings.service\";\nimport * as i5 from \"../../../services/commons.service\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"../../../components/editor-sidebar/editor-sidebar.component\";\nimport * as i9 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i10 from \"angular-datatables\";\nimport * as i11 from \"app/layout/components/content-header/content-header.component\";\nimport * as i12 from \"../../../components/btn-dropdown-action/btn-dropdown-action.component\";\nconst _c0 = [\"dropdownAction\"];\nfunction EventsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-btn-dropdown-action\", 9);\n    i0.ɵɵlistener(\"emitter\", function EventsComponent_ng_template_8_Template_app_btn_dropdown_action_emitter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const emitter_r3 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r3($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.adtData;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"actions\", ctx_r1.rowActions)(\"data\", data_r2);\n  }\n}\nexport class EventsComponent {\n  constructor(_http, _coreSidebarService, _translateService, _settingsService, _commonsService, _titleService, render, router) {\n    this._http = _http;\n    this._coreSidebarService = _coreSidebarService;\n    this._translateService = _translateService;\n    this._settingsService = _settingsService;\n    this._commonsService = _commonsService;\n    this._titleService = _titleService;\n    this.render = render;\n    this.router = router;\n    this.dtElement = DataTableDirective;\n    this.table_name = 'seasons-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: 'Create new event',\n        edit: 'Edit event',\n        remove: 'Delete event'\n      },\n      url: `${environment.apiUrl}/seasons/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'name',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Event name'),\n        placeholder: this._translateService.instant('Enter name of event'),\n        required: true\n      }\n    }, {\n      key: 'fee',\n      type: 'input',\n      props: {\n        type: 'number',\n        label: this._translateService.instant('Event fee'),\n        placeholder: this._translateService.instant('Enter fee of event'),\n        required: true,\n        min: 0,\n        max: 9999999999\n      },\n      defaultValue: 0,\n      hideExpression: model => {\n        console.log(model, this._settingsService.initSettingsValue);\n        return !this._settingsService.initSettingsValue.is_payment_required;\n      },\n      validators: {\n        fee: {\n          expression: control => {\n            if (this._settingsService.initSettingsValue.is_payment_required) {\n              return control.value > 0 ? true : false;\n            } else {\n              return true;\n            }\n          },\n          message: this._translateService.instant('Event fee must be greater than 0')\n        }\n      }\n    }, {\n      key: 'start_date',\n      type: 'input',\n      props: {\n        type: 'date',\n        label: this._translateService.instant('Start date'),\n        placeholder: this._translateService.instant('Enter start date of event'),\n        required: true,\n        max: '9999-12-31',\n        min: moment().format('YYYY-MM-DD')\n      }\n    }, {\n      key: 'end_date',\n      type: 'input',\n      props: {\n        type: 'date',\n        label: this._translateService.instant('End date'),\n        placeholder: this._translateService.instant('Enter end date of event'),\n        required: true,\n        max: '9999-12-31',\n        min: moment().format('YYYY-MM-DD')\n      }\n    }, {\n      key: 'start_register_date',\n      type: 'input',\n      props: {\n        type: 'date',\n        label: this._translateService.instant('Start register date'),\n        placeholder: this._translateService.instant('Enter start register date of event'),\n        required: true,\n        max: '9999-12-31',\n        min: moment().format('YYYY-MM-DD')\n      }\n    }, {\n      key: 'status',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Status'),\n        required: true,\n        closeOnSelect: true,\n        options: [{\n          label: this._translateService.instant('Active'),\n          value: EVENT_ACTIVE\n        }, {\n          label: this._translateService.instant('Archived'),\n          value: EVENT_ARCHIVED\n        }]\n      }\n    }, {\n      key: 'description',\n      type: 'textarea',\n      props: {\n        label: this._translateService.instant('Description'),\n        placeholder: this._translateService.instant('Enter description'),\n        required: false\n      }\n    }];\n    this.rowActions = [{\n      label: 'Edit',\n      onClick: row => {\n        this.editor('edit', row);\n      },\n      icon: 'fa-regular fa-pen-to-square'\n    }, {\n      type: 'collection',\n      buttons: [{\n        label: this._translateService.instant('Groups'),\n        onClick: row => {\n          this.router.navigate(['tables', 'events', row.id, 'groups']);\n        },\n        icon: 'fa-duotone fa-people-group'\n      }, {\n        label: 'Delete',\n        onClick: row => {\n          this.editor('remove', row);\n        },\n        icon: 'fa-regular fa-trash'\n      }]\n    }];\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    _titleService.setTitle('Table Events');\n    console.log(_settingsService.initSettingsValue);\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: 'Events',\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Settings',\n          isLink: false\n        }, {\n          name: 'Events',\n          isLink: false\n        }]\n      }\n    };\n    let defColumns = [{\n      data: 'name',\n      title: this._translateService.instant('Event name'),\n      className: 'font-weight-bolder'\n    },\n    // { data: \"type\", className: \"center\" },\n    {\n      data: 'start_date',\n      title: this._translateService.instant('Start date'),\n      className: 'center',\n      render: (data, type, row) => {\n        return moment(data).format('YYYY-MM-DD');\n      }\n    }, {\n      data: 'end_date',\n      title: this._translateService.instant('End date'),\n      className: 'center',\n      render: (data, type, row) => {\n        return moment(data).format('YYYY-MM-DD');\n      }\n    }, {\n      data: 'start_register_date',\n      title: this._translateService.instant('Start register date'),\n      className: 'center',\n      render: (data, type, row) => {\n        return moment(data).format('YYYY-MM-DD');\n      }\n    }, {\n      data: 'status',\n      title: this._translateService.instant('Status'),\n      className: 'text-center',\n      render: (data, type, row) => {\n        if (data == EVENT_ACTIVE) {\n          return `<span class=\"badge badge-light-success\">` + this._translateService.instant('Active') + `</span>`;\n        } else {\n          return `<span class=\"badge badge-light-danger\">` + this._translateService.instant('Archived') + `</span>`;\n        }\n      }\n    }];\n    // insert { data: 'fee', className: 'center' } to index 4 if is_payment_required is true\n    if (this._settingsService.initSettingsValue.is_payment_required) {\n      defColumns.splice(4, 0, {\n        data: 'fee',\n        title: this._translateService.instant('Fee'),\n        className: 'center',\n        render: (data, type, row) => {\n          return data;\n        }\n      });\n    }\n    setTimeout(() => {\n      // add columns  {\n      //   width: '100px',\n      //   data: null,\n      //   title: this._translateService.instant('Actions'),\n      //   defaultContent: '',\n      //   ngTemplateRef: {\n      //     ref: this.dropdownAction,\n      //     context: {\n      //       captureEvents: this.onCaptureEvent.bind(this),\n      //     },\n      //   },\n      // },\n      defColumns.push({\n        width: '100px',\n        data: null,\n        title: this._translateService.instant('Action'),\n        className: 'text-center',\n        defaultContent: '',\n        ngTemplateRef: {\n          ref: this.dropdownAction,\n          context: {\n            captureEvents: this.onCaptureEvent.bind(this)\n          }\n        }\n      });\n      this.dtOptions = {\n        dom: this._commonsService.dataTableDefaults.dom,\n        select: 'single',\n        // serverSide: true,\n        rowId: 'id',\n        ajax: (dataTablesParameters, callback) => {\n          this._http.post(`${environment.apiUrl}/seasons/all`, dataTablesParameters).subscribe(resp => {\n            callback({\n              recordsTotal: resp.recordsTotal,\n              recordsFiltered: resp.recordsFiltered,\n              data: resp.data\n            });\n          });\n        },\n        responsive: true,\n        scrollX: false,\n        language: this._commonsService.dataTableDefaults.lang,\n        columnDefs: [{\n          targets: 0,\n          responsivePriority: 1\n        }, {\n          targets: -1,\n          responsivePriority: 2\n        }, {\n          targets: -2,\n          responsivePriority: 3\n        }],\n        columns: defColumns,\n        buttons: {\n          dom: this._commonsService.dataTableDefaults.buttons.dom,\n          buttons: [{\n            text: '<i class=\"feather icon-plus\"></i> ' + this._translateService.instant('Add'),\n            action: () => this.editor('create')\n          }, {\n            text: '<i class=\"feather icon-edit\"></i> ' + this._translateService.instant('Edit'),\n            action: () => this.editor('edit'),\n            extend: 'selected'\n          }, {\n            text: '<i class=\"feather icon-trash\"></i> ' + this._translateService.instant('Delete'),\n            action: () => this.editor('remove'),\n            extend: 'selected'\n          }]\n        }\n      };\n    });\n  }\n  editor(action, row) {\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    // this.unlistener();\n  }\n  static #_ = this.ɵfac = function EventsComponent_Factory(t) {\n    return new (t || EventsComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CoreSidebarService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.SettingsService), i0.ɵɵdirectiveInject(i5.CommonsService), i0.ɵɵdirectiveInject(i6.Title), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i7.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EventsComponent,\n    selectors: [[\"app-events\"]],\n    viewQuery: function EventsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownAction = _t.first);\n      }\n    },\n    decls: 10,\n    vars: 7,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"card\"], [1, \"card-header\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"], [\"dropdownAction\", \"\"], [\"btnActionStyle\", \"font-size:1.25em; \", 1, \"row\", \"align-items-center\", \"justify-content-center\", \"gap-2\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function EventsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"div\", 4)(5, \"table\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"core-sidebar\", 6);\n        i0.ɵɵelement(7, \"app-editor-sidebar\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, EventsComponent_ng_template_8_Template, 1, 2, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    dependencies: [i8.EditorSidebarComponent, i9.CoreSidebarComponent, i10.DataTableDirective, i11.ContentHeaderComponent, i12.BtnDropdownActionComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,WAAW,QAAQ,wCAAwC;AAEpE,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AAYjE,SAASC,kBAAkB,QAAQ,oBAAoB;AAMvD,SAASC,OAAO,QAAQ,MAAM;AAE9B,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;ICFzBC,EAAA,CAAAC,cAAA,iCAA0L;IAA5HD,EAAA,CAAAE,UAAA,qBAAAC,kFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAF,UAAA,CAAAJ,MAAA,CAAe;IAAA,EAAC;IAAiGJ,EAAA,CAAAW,YAAA,EAA0B;;;;;IAA3LX,EAAA,CAAAY,UAAA,YAAAC,MAAA,CAAAC,UAAA,CAAsB,SAAAC,OAAA;;;ADWjD,OAAM,MAAOC,eAAe;EAkK1BC,YACUC,KAAiB,EAClBC,mBAAuC,EACvCC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,aAAoB,EACpBC,MAAiB,EACjBC,MAAc;IAPb,KAAAP,KAAK,GAALA,KAAK;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IAvKf,KAAAC,SAAS,GAAQ7B,kBAAkB;IAI5B,KAAA8B,UAAU,GAAG,eAAe;IAC5B,KAAAC,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACF,UAAU;MAC1BG,KAAK,EAAE;QACLC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAGxC,WAAW,CAACyC,MAAM,iBAAiB;MAC3CC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAIM,KAAAC,MAAM,GAAU,CACrB;MACEC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,YAAY,CAAC;QACnDC,WAAW,EAAE,IAAI,CAACxB,iBAAiB,CAACuB,OAAO,CAAC,qBAAqB,CAAC;QAClEE,QAAQ,EAAE;;KAEb,EACD;MACEN,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,QAAQ;QACdE,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,WAAW,CAAC;QAClDC,WAAW,EAAE,IAAI,CAACxB,iBAAiB,CAACuB,OAAO,CAAC,oBAAoB,CAAC;QACjEE,QAAQ,EAAE,IAAI;QACdC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE;OACN;MACDC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAGC,KAAK,IAAI;QACxBC,OAAO,CAACC,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC7B,gBAAgB,CAACgC,iBAAiB,CAAC;QAC3D,OAAO,CAAC,IAAI,CAAChC,gBAAgB,CAACgC,iBAAiB,CAACC,mBAAmB;MACrE,CAAC;MACDC,UAAU,EAAC;QACTC,GAAG,EAAE;UACHC,UAAU,EAAGC,OAAO,IAAI;YACtB,IAAI,IAAI,CAACrC,gBAAgB,CAACgC,iBAAiB,CAACC,mBAAmB,EAAE;cAC/D,OAAOI,OAAO,CAACC,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK;aACxC,MAAM;cACL,OAAO,IAAI;;UAEf,CAAC;UACDC,OAAO,EAAE,IAAI,CAACxC,iBAAiB,CAACuB,OAAO,CAAC,kCAAkC;;;KAG/E,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,MAAM;QACZE,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,YAAY,CAAC;QACnDC,WAAW,EAAE,IAAI,CAACxB,iBAAiB,CAACuB,OAAO,CACzC,2BAA2B,CAC5B;QACDE,QAAQ,EAAE,IAAI;QACdE,GAAG,EAAE,YAAY;QACjBD,GAAG,EAAE/C,MAAM,EAAE,CAAC8D,MAAM,CAAC,YAAY;;KAEpC,EACD;MACEtB,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,MAAM;QACZE,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,UAAU,CAAC;QACjDC,WAAW,EAAE,IAAI,CAACxB,iBAAiB,CAACuB,OAAO,CAAC,yBAAyB,CAAC;QACtEE,QAAQ,EAAE,IAAI;QACdE,GAAG,EAAE,YAAY;QACjBD,GAAG,EAAE/C,MAAM,EAAE,CAAC8D,MAAM,CAAC,YAAY;;KAEpC,EACD;MACEtB,GAAG,EAAE,qBAAqB;MAC1BC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE,MAAM;QACZE,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,qBAAqB,CAAC;QAC5DC,WAAW,EAAE,IAAI,CAACxB,iBAAiB,CAACuB,OAAO,CACzC,oCAAoC,CACrC;QACDE,QAAQ,EAAE,IAAI;QACdE,GAAG,EAAE,YAAY;QACjBD,GAAG,EAAE/C,MAAM,EAAE,CAAC8D,MAAM,CAAC,YAAY;;KAEpC,EACD;MACEtB,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC;QAC/CE,QAAQ,EAAE,IAAI;QACdiB,aAAa,EAAE,IAAI;QACnBC,OAAO,EAAE,CACP;UACErB,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC;UAC/CgB,KAAK,EAAEhE;SACR,EACD;UACE+C,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,UAAU,CAAC;UACjDgB,KAAK,EAAE/D;SACR;;KAGN,EACD;MACE2C,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,aAAa,CAAC;QACpDC,WAAW,EAAE,IAAI,CAACxB,iBAAiB,CAACuB,OAAO,CAAC,mBAAmB,CAAC;QAChEE,QAAQ,EAAE;;KAEb,CACF;IAEM,KAAA/B,UAAU,GAAmB,CAClC;MACE4B,KAAK,EAAE,MAAM;MACbsB,OAAO,EAAGC,GAAQ,IAAI;QACpB,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,GAAG,CAAC;MAC1B,CAAC;MACDE,IAAI,EAAE;KACP,EACD;MACE3B,IAAI,EAAE,YAAY;MAClB4B,OAAO,EAAE,CACP;QACE1B,KAAK,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC;QAC/CqB,OAAO,EAAGC,GAAQ,IAAI;UACpB,IAAI,CAACxC,MAAM,CAAC4C,QAAQ,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAEJ,GAAG,CAACK,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC9D,CAAC;QACDH,IAAI,EAAE;OACP,EACD;QACEzB,KAAK,EAAE,QAAQ;QACfsB,OAAO,EAAGC,GAAQ,IAAI;UACpB,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAED,GAAG,CAAC;QAC5B,CAAC;QACDE,IAAI,EAAE;OACP;KAEJ,CACF;IAED,KAAAI,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAyB,IAAI1E,OAAO,EAAe;IAW1DyB,aAAa,CAACkD,QAAQ,CAAC,cAAc,CAAC;IACtCtB,OAAO,CAACC,GAAG,CAAC/B,gBAAgB,CAACgC,iBAAiB,CAAC;EACjD;EAEAqB,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,QAAQ;MACrBC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVtC,IAAI,EAAE,EAAE;QACRuC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,UAAU;UAChBC,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE;SACT;;KAGN;IAED,IAAIC,UAAU,GAAQ,CACpB;MACEC,IAAI,EAAE,MAAM;MACZrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,YAAY,CAAC;MACnDyC,SAAS,EAAE;KACZ;IACD;IACA;MACED,IAAI,EAAE,YAAY;MAClBrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,YAAY,CAAC;MACnDyC,SAAS,EAAE,QAAQ;MACnB5D,MAAM,EAAEA,CAAC2D,IAAI,EAAE3C,IAAI,EAAEyB,GAAG,KAAI;QAC1B,OAAOlE,MAAM,CAACoF,IAAI,CAAC,CAACtB,MAAM,CAAC,YAAY,CAAC;MAC1C;KACD,EACD;MACEsB,IAAI,EAAE,UAAU;MAChBrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,UAAU,CAAC;MACjDyC,SAAS,EAAE,QAAQ;MACnB5D,MAAM,EAAEA,CAAC2D,IAAI,EAAE3C,IAAI,EAAEyB,GAAG,KAAI;QAC1B,OAAOlE,MAAM,CAACoF,IAAI,CAAC,CAACtB,MAAM,CAAC,YAAY,CAAC;MAC1C;KACD,EACD;MACEsB,IAAI,EAAE,qBAAqB;MAC3BrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,qBAAqB,CAAC;MAC5DyC,SAAS,EAAE,QAAQ;MACnB5D,MAAM,EAAEA,CAAC2D,IAAI,EAAE3C,IAAI,EAAEyB,GAAG,KAAI;QAC1B,OAAOlE,MAAM,CAACoF,IAAI,CAAC,CAACtB,MAAM,CAAC,YAAY,CAAC;MAC1C;KACD,EAED;MACEsB,IAAI,EAAE,QAAQ;MACdrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC;MAC/CyC,SAAS,EAAE,aAAa;MACxB5D,MAAM,EAAEA,CAAC2D,IAAI,EAAE3C,IAAI,EAAEyB,GAAG,KAAI;QAC1B,IAAIkB,IAAI,IAAIxF,YAAY,EAAE;UACxB,OACE,0CAA0C,GAC1C,IAAI,CAACyB,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC,GACxC,SAAS;SAEZ,MAAM;UACL,OACE,yCAAyC,GACzC,IAAI,CAACvB,iBAAiB,CAACuB,OAAO,CAAC,UAAU,CAAC,GAC1C,SAAS;;MAGf;KACD,CACF;IAED;IACA,IAAI,IAAI,CAACtB,gBAAgB,CAACgC,iBAAiB,CAACC,mBAAmB,EAAE;MAC/D4B,UAAU,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACtBF,IAAI,EAAE,KAAK;QACXrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,KAAK,CAAC;QAC5CyC,SAAS,EAAE,QAAQ;QACnB5D,MAAM,EAAEA,CAAC2D,IAAI,EAAE3C,IAAI,EAAEyB,GAAG,KAAI;UAC1B,OAAOkB,IAAI;QACb;OACD,CAAC;;IAEJG,UAAU,CAAC,MAAK;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ,UAAU,CAACK,IAAI,CAAC;QACdC,KAAK,EAAE,OAAO;QACdL,IAAI,EAAE,IAAI;QACVrD,KAAK,EAAE,IAAI,CAACV,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC;QAC/CyC,SAAS,EAAE,aAAa;QACxBK,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;UACbC,GAAG,EAAE,IAAI,CAACC,cAAc;UACxBC,OAAO,EAAE;YACPpF,aAAa,EAAE,IAAI,CAACqF,cAAc,CAACC,IAAI,CAAC,IAAI;;;OAGjD,CAAC;MACF,IAAI,CAACxB,SAAS,GAAG;QACfyB,GAAG,EAAE,IAAI,CAAC1E,eAAe,CAAC2E,iBAAiB,CAACD,GAAG;QAC/CE,MAAM,EAAE,QAAQ;QAChB;QACAC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;UAC5C,IAAI,CAACpF,KAAK,CACPqF,IAAI,CACH,GAAG7G,WAAW,CAACyC,MAAM,cAAc,EACnCkE,oBAAoB,CACrB,CACAG,SAAS,CAAEC,IAAS,IAAI;YACvBH,QAAQ,CAAC;cACPI,YAAY,EAAED,IAAI,CAACC,YAAY;cAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;cACrCxB,IAAI,EAAEsB,IAAI,CAACtB;aACZ,CAAC;UACJ,CAAC,CAAC;QACN,CAAC;QACDyB,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,IAAI,CAACxF,eAAe,CAAC2E,iBAAiB,CAACc,IAAI;QACrDC,UAAU,EAAE,CACV;UAAEC,OAAO,EAAE,CAAC;UAAEC,kBAAkB,EAAE;QAAC,CAAE,EACrC;UAAED,OAAO,EAAE,CAAC,CAAC;UAAEC,kBAAkB,EAAE;QAAC,CAAE,EACtC;UAAED,OAAO,EAAE,CAAC,CAAC;UAAEC,kBAAkB,EAAE;QAAC,CAAE,CACvC;QACDC,OAAO,EAAEjC,UAAU;QACnBd,OAAO,EAAE;UACP4B,GAAG,EAAE,IAAI,CAAC1E,eAAe,CAAC2E,iBAAiB,CAAC7B,OAAO,CAAC4B,GAAG;UACvD5B,OAAO,EAAE,CACP;YACEgD,IAAI,EACF,oCAAoC,GACpC,IAAI,CAAChG,iBAAiB,CAACuB,OAAO,CAAC,KAAK,CAAC;YACvCN,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6B,MAAM,CAAC,QAAQ;WACnC,EACD;YACEkD,IAAI,EACF,oCAAoC,GACpC,IAAI,CAAChG,iBAAiB,CAACuB,OAAO,CAAC,MAAM,CAAC;YACxCN,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6B,MAAM,CAAC,MAAM,CAAC;YACjCmD,MAAM,EAAE;WACT,EACD;YACED,IAAI,EACF,qCAAqC,GACrC,IAAI,CAAChG,iBAAiB,CAACuB,OAAO,CAAC,QAAQ,CAAC;YAC1CN,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6B,MAAM,CAAC,QAAQ,CAAC;YACnCmD,MAAM,EAAE;WACT;;OAGN;IACH,CAAC,CAAC;EACJ;EAEAnD,MAAMA,CAAC7B,MAAM,EAAE4B,GAAI;IACjB,IAAI,CAACrC,MAAM,CAACS,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACT,MAAM,CAACqC,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAAC9C,mBAAmB,CAACmG,kBAAkB,CAAC,IAAI,CAAC3F,UAAU,CAAC,CAAC4F,UAAU,EAAE;EAC3E;EAEAC,eAAeA,CAAA;IACblC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACd,SAAS,CAACiD,IAAI,CAAC,IAAI,CAAClD,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAuB,cAAcA,CAAC4B,KAAU;IACvB;EAAA;EAGFC,WAAWA,CAAA;IACT,IAAI,CAACnD,SAAS,CAACoD,WAAW,EAAE;IAC5B;EACF;EAAC,QAAAC,CAAA;qBA3WU7G,eAAe,EAAAhB,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAlI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAApI,EAAA,CAAA8H,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAtI,EAAA,CAAA8H,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAxI,EAAA,CAAA8H,iBAAA,CAAAW,EAAA,CAAAC,KAAA,GAAA1I,EAAA,CAAA8H,iBAAA,CAAA9H,EAAA,CAAA2I,SAAA,GAAA3I,EAAA,CAAA8H,iBAAA,CAAAc,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;UAAf9H,eAAe;IAAA+H,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAEfrJ,kBAAkB;;;;;;;;;;;;;;QCjC/BG,EAAA,CAAAC,cAAA,aAA+C;QAG3CD,EAAA,CAAAoJ,SAAA,4BAAyE;QACzEpJ,EAAA,CAAAC,cAAA,aAAkB;QAChBD,EAAA,CAAAoJ,SAAA,aAEM;QAGRpJ,EAAA,CAAAW,YAAA,EAAM;QAKVX,EAAA,CAAAC,cAAA,sBAAqH;QACnHD,EAAA,CAAAoJ,SAAA,4BACqB;QACvBpJ,EAAA,CAAAW,YAAA,EAAe;QACfX,EAAA,CAAAqJ,UAAA,IAAAC,sCAAA,gCAAAtJ,EAAA,CAAAuJ,sBAAA,CAEc;;;QAlBUvJ,EAAA,CAAAwJ,SAAA,GAA+B;QAA/BxJ,EAAA,CAAAY,UAAA,kBAAAuI,GAAA,CAAAxE,aAAA,CAA+B;QAKhC3E,EAAA,CAAAwJ,SAAA,GAAuB;QAAvBxJ,EAAA,CAAAY,UAAA,cAAAuI,GAAA,CAAA5E,SAAA,CAAuB,cAAA4E,GAAA,CAAA3E,SAAA;QAOqBxE,EAAA,CAAAwJ,SAAA,GAAmB;QAAnBxJ,EAAA,CAAAY,UAAA,SAAAuI,GAAA,CAAAxH,UAAA,CAAmB;QAChE3B,EAAA,CAAAwJ,SAAA,GAAmB;QAAnBxJ,EAAA,CAAAY,UAAA,UAAAuI,GAAA,CAAAzH,SAAA,CAAmB,WAAAyH,GAAA,CAAA7G,MAAA,YAAA6G,GAAA,CAAAvH,MAAA", "names": ["environment", "EVENT_ACTIVE", "EVENT_ARCHIVED", "DataTableDirective", "Subject", "moment", "i0", "ɵɵelementStart", "ɵɵlistener", "EventsComponent_ng_template_8_Template_app_btn_dropdown_action_emitter_0_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r5", "emitter_r3", "captureEvents", "ɵɵresetView", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "rowActions", "data_r2", "EventsComponent", "constructor", "_http", "_coreSidebarService", "_translateService", "_settingsService", "_commonsService", "_titleService", "render", "router", "dtElement", "table_name", "params", "editor_id", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "label", "instant", "placeholder", "required", "min", "max", "defaultValue", "hideExpression", "model", "console", "log", "initSettingsValue", "is_payment_required", "validators", "fee", "expression", "control", "value", "message", "format", "closeOnSelect", "options", "onClick", "row", "editor", "icon", "buttons", "navigate", "id", "dtOptions", "dtTrigger", "setTitle", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "defColumns", "data", "className", "splice", "setTimeout", "push", "width", "defaultContent", "ngTemplateRef", "ref", "dropdownAction", "context", "onCaptureEvent", "bind", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "language", "lang", "columnDefs", "targets", "responsivePriority", "columns", "text", "extend", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngAfterViewInit", "next", "event", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CoreSidebarService", "i3", "TranslateService", "i4", "SettingsService", "i5", "CommonsService", "i6", "Title", "Renderer2", "i7", "Router", "_2", "selectors", "viewQuery", "EventsComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "EventsComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ɵɵadvance"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\events\\events.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\tables\\events\\events.component.html"], "sourcesContent": ["import { environment } from './../../../../environments/environment';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { EVENT_ACTIVE, EVENT_ARCHIVED } from './events-constants';\r\nimport {\r\n  AfterViewInit,\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  TemplateRef,\r\n  ViewChild,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CommonsService } from '../../../services/commons.service';\r\nimport { Router } from '@angular/router';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { Subject } from 'rxjs';\r\nimport { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { SettingsService } from 'app/services/settings.service';\r\n@Component({\r\n  selector: 'app-events',\r\n  templateUrl: './events.component.html',\r\n  styleUrls: ['./events.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class EventsComponent implements OnInit, AfterViewInit {\r\n  private unlistener: () => void;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @ViewChild('dropdownAction') dropdownAction: TemplateRef<any>;\r\n  // public\r\n  public contentHeader: object;\r\n  public table_name = 'seasons-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: 'Create new event',\r\n      edit: 'Edit event',\r\n      remove: 'Delete event',\r\n    },\r\n    url: `${environment.apiUrl}/seasons/editor`,\r\n    method: 'POST',\r\n    action: 'create',\r\n  };\r\n\r\n \r\n\r\n  public fields: any[] = [\r\n    {\r\n      key: 'name',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Event name'),\r\n        placeholder: this._translateService.instant('Enter name of event'),\r\n        required: true,\r\n      },\r\n    },\r\n    {\r\n      key: 'fee',\r\n      type: 'input',\r\n      props: {\r\n        type: 'number',\r\n        label: this._translateService.instant('Event fee'),\r\n        placeholder: this._translateService.instant('Enter fee of event'),\r\n        required: true,\r\n        min: 0,\r\n        max: 9999999999,\r\n      },\r\n      defaultValue: 0,\r\n      hideExpression: (model) => {\r\n        console.log(model, this._settingsService.initSettingsValue);\r\n        return !this._settingsService.initSettingsValue.is_payment_required;\r\n      },\r\n      validators:{\r\n        fee: {\r\n          expression: (control) => {\r\n            if (this._settingsService.initSettingsValue.is_payment_required) {\r\n              return control.value > 0 ? true : false;\r\n            } else {\r\n              return true;\r\n            }\r\n          },\r\n          message: this._translateService.instant('Event fee must be greater than 0'),\r\n        }\r\n      }\r\n    },    \r\n    {\r\n      key: 'start_date',\r\n      type: 'input',\r\n      props: {\r\n        type: 'date',\r\n        label: this._translateService.instant('Start date'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter start date of event'\r\n        ),\r\n        required: true,\r\n        max: '9999-12-31',\r\n        min: moment().format('YYYY-MM-DD'),\r\n      },\r\n    },\r\n    {\r\n      key: 'end_date',\r\n      type: 'input',\r\n      props: {\r\n        type: 'date',\r\n        label: this._translateService.instant('End date'),\r\n        placeholder: this._translateService.instant('Enter end date of event'),\r\n        required: true,\r\n        max: '9999-12-31',\r\n        min: moment().format('YYYY-MM-DD'),\r\n      },\r\n    },\r\n    {\r\n      key: 'start_register_date',\r\n      type: 'input',\r\n      props: {\r\n        type: 'date',\r\n        label: this._translateService.instant('Start register date'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter start register date of event'\r\n        ),\r\n        required: true,\r\n        max: '9999-12-31',\r\n        min: moment().format('YYYY-MM-DD'),\r\n      },\r\n    },\r\n    {\r\n      key: 'status',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Status'),\r\n        required: true,\r\n        closeOnSelect: true,\r\n        options: [\r\n          {\r\n            label: this._translateService.instant('Active'),\r\n            value: EVENT_ACTIVE,\r\n          },\r\n          {\r\n            label: this._translateService.instant('Archived'),\r\n            value: EVENT_ARCHIVED,\r\n          },\r\n        ],\r\n      },\r\n    },\r\n    {\r\n      key: 'description',\r\n      type: 'textarea',\r\n      props: {\r\n        label: this._translateService.instant('Description'),\r\n        placeholder: this._translateService.instant('Enter description'),\r\n        required: false,\r\n      },\r\n    },\r\n  ];\r\n\r\n  public rowActions: EZBtnActions[] = [\r\n    {\r\n      label: 'Edit',\r\n      onClick: (row: any) => {\r\n        this.editor('edit', row);\r\n      },\r\n      icon: 'fa-regular fa-pen-to-square',\r\n    },\r\n    {\r\n      type: 'collection',\r\n      buttons: [\r\n        {\r\n          label: this._translateService.instant('Groups'),\r\n          onClick: (row: any) => {\r\n            this.router.navigate(['tables', 'events', row.id, 'groups']);\r\n          },\r\n          icon: 'fa-duotone fa-people-group',\r\n        },\r\n        {\r\n          label: 'Delete',\r\n          onClick: (row: any) => {\r\n            this.editor('remove', row);\r\n          },\r\n          icon: 'fa-regular fa-trash',\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  constructor(\r\n    private _http: HttpClient,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _translateService: TranslateService,\r\n    public _settingsService: SettingsService,\r\n    public _commonsService: CommonsService,\r\n    public _titleService: Title,\r\n    public render: Renderer2,\r\n    public router: Router\r\n  ) {\r\n    _titleService.setTitle('Table Events');\r\n    console.log(_settingsService.initSettingsValue);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: 'Events',\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: 'Settings',\r\n            isLink: false,\r\n          },\r\n          {\r\n            name: 'Events',\r\n            isLink: false,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    let defColumns: any = [\r\n      {\r\n        data: 'name',\r\n        title: this._translateService.instant('Event name'),\r\n        className: 'font-weight-bolder',\r\n      },\r\n      // { data: \"type\", className: \"center\" },\r\n      {\r\n        data: 'start_date',\r\n        title: this._translateService.instant('Start date'),\r\n        className: 'center',\r\n        render: (data, type, row) => {\r\n          return moment(data).format('YYYY-MM-DD');\r\n        },\r\n      },\r\n      {\r\n        data: 'end_date',\r\n        title: this._translateService.instant('End date'),\r\n        className: 'center',\r\n        render: (data, type, row) => {\r\n          return moment(data).format('YYYY-MM-DD');\r\n        },\r\n      },\r\n      {\r\n        data: 'start_register_date',\r\n        title: this._translateService.instant('Start register date'),\r\n        className: 'center',\r\n        render: (data, type, row) => {\r\n          return moment(data).format('YYYY-MM-DD');\r\n        },\r\n      },\r\n\r\n      {\r\n        data: 'status',\r\n        title: this._translateService.instant('Status'),\r\n        className: 'text-center',\r\n        render: (data, type, row) => {\r\n          if (data == EVENT_ACTIVE) {\r\n            return (\r\n              `<span class=\"badge badge-light-success\">` +\r\n              this._translateService.instant('Active') +\r\n              `</span>`\r\n            );\r\n          } else {\r\n            return (\r\n              `<span class=\"badge badge-light-danger\">` +\r\n              this._translateService.instant('Archived') +\r\n              `</span>`\r\n            );\r\n          }\r\n        },\r\n      },\r\n    ];\r\n\r\n    // insert { data: 'fee', className: 'center' } to index 4 if is_payment_required is true\r\n    if (this._settingsService.initSettingsValue.is_payment_required) {\r\n      defColumns.splice(4, 0, {\r\n        data: 'fee',\r\n        title: this._translateService.instant('Fee'),\r\n        className: 'center',\r\n        render: (data, type, row) => {\r\n          return data;\r\n        },\r\n      });\r\n    }\r\n    setTimeout(() => {\r\n      // add columns  {\r\n      //   width: '100px',\r\n      //   data: null,\r\n      //   title: this._translateService.instant('Actions'),\r\n      //   defaultContent: '',\r\n      //   ngTemplateRef: {\r\n      //     ref: this.dropdownAction,\r\n      //     context: {\r\n      //       captureEvents: this.onCaptureEvent.bind(this),\r\n      //     },\r\n      //   },\r\n      // },\r\n      defColumns.push({\r\n        width: '100px',\r\n        data: null,\r\n        title: this._translateService.instant('Action'),\r\n        className: 'text-center',\r\n        defaultContent: '',\r\n        ngTemplateRef: {\r\n          ref: this.dropdownAction,\r\n          context: {\r\n            captureEvents: this.onCaptureEvent.bind(this),\r\n          },\r\n        },\r\n      });\r\n      this.dtOptions = {\r\n        dom: this._commonsService.dataTableDefaults.dom,\r\n        select: 'single',\r\n        // serverSide: true,\r\n        rowId: 'id',\r\n        ajax: (dataTablesParameters: any, callback) => {\r\n          this._http\r\n            .post<any>(\r\n              `${environment.apiUrl}/seasons/all`,\r\n              dataTablesParameters\r\n            )\r\n            .subscribe((resp: any) => {\r\n              callback({\r\n                recordsTotal: resp.recordsTotal,\r\n                recordsFiltered: resp.recordsFiltered,\r\n                data: resp.data,\r\n              });\r\n            });\r\n        },\r\n        responsive: true,\r\n        scrollX: false,\r\n        language: this._commonsService.dataTableDefaults.lang,\r\n        columnDefs: [\r\n          { targets: 0, responsivePriority: 1 },\r\n          { targets: -1, responsivePriority: 2 },\r\n          { targets: -2, responsivePriority: 3 },\r\n        ],\r\n        columns: defColumns,\r\n        buttons: {\r\n          dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n          buttons: [\r\n            {\r\n              text:\r\n                '<i class=\"feather icon-plus\"></i> ' +\r\n                this._translateService.instant('Add'),\r\n              action: () => this.editor('create'),\r\n            },\r\n            {\r\n              text:\r\n                '<i class=\"feather icon-edit\"></i> ' +\r\n                this._translateService.instant('Edit'),\r\n              action: () => this.editor('edit'),\r\n              extend: 'selected',\r\n            },\r\n            {\r\n              text:\r\n                '<i class=\"feather icon-trash\"></i> ' +\r\n                this._translateService.instant('Delete'),\r\n              action: () => this.editor('remove'),\r\n              extend: 'selected',\r\n            },\r\n          ],\r\n        },\r\n      };\r\n    });\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n      // race condition fails unit tests if dtOptions isn't sent with dtTrigger\r\n      this.dtTrigger.next(this.dtOptions);\r\n    }, 500);\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    // this.unlistener();\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <!-- <h4 class=\"card-title\">Table Basic</h4> -->\r\n      </div>\r\n      <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table row-border hover\">\r\n      </table>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Modal -->\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n  <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n<ng-template #dropdownAction let-data=\"adtData\" let-emitter=\"captureEvents\">\r\n  <app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\" btnActionStyle=\"font-size:1.25em; \" class=\"row align-items-center justify-content-center gap-2\"></app-btn-dropdown-action>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n  flush(action) {\n    this.active = true;\n    this.scheduled = undefined;\n    const {\n      actions\n    } = this;\n    let error;\n    let index = -1;\n    let count = actions.length;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (++index < count && (action = actions.shift()));\n    this.active = false;\n    if (error) {\n      while (++index < count && (action = actions.shift())) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["AsyncScheduler", "AsapScheduler", "flush", "action", "active", "scheduled", "undefined", "actions", "error", "index", "count", "length", "shift", "execute", "state", "delay", "unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/scheduler/AsapScheduler.js"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n    flush(action) {\n        this.active = true;\n        this.scheduled = undefined;\n        const { actions } = this;\n        let error;\n        let index = -1;\n        let count = actions.length;\n        action = action || actions.shift();\n        do {\n            if (error = action.execute(action.state, action.delay)) {\n                break;\n            }\n        } while (++index < count && (action = actions.shift()));\n        this.active = false;\n        if (error) {\n            while (++index < count && (action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,aAAa,SAASD,cAAc,CAAC;EAC9CE,KAAKA,CAACC,MAAM,EAAE;IACV,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIC,KAAK;IACT,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,KAAK,GAAGH,OAAO,CAACI,MAAM;IAC1BR,MAAM,GAAGA,MAAM,IAAII,OAAO,CAACK,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAIJ,KAAK,GAAGL,MAAM,CAACU,OAAO,CAACV,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,KAAK,CAAC,EAAE;QACpD;MACJ;IACJ,CAAC,QAAQ,EAAEN,KAAK,GAAGC,KAAK,KAAKP,MAAM,GAAGI,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC;IACtD,IAAI,CAACR,MAAM,GAAG,KAAK;IACnB,IAAII,KAAK,EAAE;MACP,OAAO,EAAEC,KAAK,GAAGC,KAAK,KAAKP,MAAM,GAAGI,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC,EAAE;QAClDT,MAAM,CAACa,WAAW,CAAC,CAAC;MACxB;MACA,MAAMR,KAAK;IACf;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
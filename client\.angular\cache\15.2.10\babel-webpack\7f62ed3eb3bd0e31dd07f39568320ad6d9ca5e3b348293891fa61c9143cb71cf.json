{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/flex-layout/extended\";\nimport * as i5 from \"@core/directives/core-feather-icons/core-feather-icons\";\nconst _c0 = [\"core-menu-vertical-item\", \"\"];\nfunction CoreMenuVerticalItemComponent_ng_container_0_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c1 = function (a0) {\n  return [a0];\n};\nfunction CoreMenuVerticalItemComponent_ng_container_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 4);\n    i0.ɵɵtemplate(1, CoreMenuVerticalItemComponent_ng_container_0_a_1_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.classes)(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.item.url))(\"target\", ctx_r1.item.openInNewTab ? \"_blank\" : \"_self\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3);\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵtemplate(1, CoreMenuVerticalItemComponent_ng_container_0_a_2_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(4);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.item.classes)(\"href\", ctx_r2.item.url, i0.ɵɵsanitizeUrl)(\"target\", ctx_r2.item.openInNewTab ? \"_blank\" : \"_self\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3);\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.item.icon);\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data-feather\", ctx_r10.item.icon);\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_i_1_Template, 1, 1, \"i\", 9);\n    i0.ɵɵtemplate(2, CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_span_2_Template, 1, 1, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.item.icon.startsWith(\"fa-\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.item.icon.startsWith(\"fa-\"));\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"translate\", ctx_r8.item.badge.translate)(\"ngClass\", ctx_r8.item.badge.classes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.item.badge.title, \" \");\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n    i0.ɵɵelementStart(1, \"span\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_span_3_Template, 2, 3, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.item.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"translate\", ctx_r4.item.translate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.item.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.item.badge);\n  }\n}\nfunction CoreMenuVerticalItemComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CoreMenuVerticalItemComponent_ng_container_0_a_1_Template, 2, 6, \"a\", 1);\n    i0.ɵɵtemplate(2, CoreMenuVerticalItemComponent_ng_container_0_a_2_Template, 2, 4, \"a\", 2);\n    i0.ɵɵtemplate(3, CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_Template, 4, 4, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.url && !ctx_r0.item.externalUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.url && ctx_r0.item.externalUrl);\n  }\n}\nexport class CoreMenuVerticalItemComponent {\n  static #_ = this.ɵfac = function CoreMenuVerticalItemComponent_Factory(t) {\n    return new (t || CoreMenuVerticalItemComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CoreMenuVerticalItemComponent,\n    selectors: [[\"\", \"core-menu-vertical-item\", \"\"]],\n    inputs: {\n      item: \"item\"\n    },\n    attrs: _c0,\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [\"class\", \"d-flex align-items-center\", 3, \"ngClass\", \"routerLink\", \"target\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center\", 3, \"ngClass\", \"href\", \"target\", 4, \"ngIf\"], [\"itemContent\", \"\"], [1, \"d-flex\", \"align-items-center\", 3, \"ngClass\", \"routerLink\", \"target\"], [4, \"ngTemplateOutlet\"], [1, \"d-flex\", \"align-items-center\", 3, \"ngClass\", \"href\", \"target\"], [1, \"menu-title\", \"text-truncate\", 3, \"translate\"], [\"class\", \"badge ml-auto mr-1\", 3, \"translate\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"data-feather\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"data-feather\"], [1, \"badge\", \"ml-auto\", \"mr-1\", 3, \"translate\", \"ngClass\"]],\n    template: function CoreMenuVerticalItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CoreMenuVerticalItemComponent_ng_container_0_Template, 5, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.item.hidden);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i2.RouterLink, i3.TranslateDirective, i4.DefaultClassDirective, i5.FeatherIconDirective],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;IAIIA,EAAA,CAAAC,kBAAA,GAA6D;;;;;;;;IAF/DD,EAAA,CAAAE,cAAA,WAC8E;IAC5EF,EAAA,CAAAG,UAAA,IAAAC,wEAAA,0BAA6D;I<PERSON>/DJ,EAAA,CAAAK,YAAA,EAAI;;;;;;IAHiCL,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAwB,eAAAT,EAAA,CAAAU,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAC,IAAA,CAAAI,GAAA,aAAAL,MAAA,CAAAC,IAAA,CAAAK,YAAA;IAE5Cb,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAM,UAAA,qBAAAS,GAAA,CAA6B;;;;;IAM5Cf,EAAA,CAAAC,kBAAA,GAA6D;;;;;IAF/DD,EAAA,CAAAE,cAAA,WACoD;IAClDF,EAAA,CAAAG,UAAA,IAAAa,wEAAA,0BAA6D;IAC/DhB,EAAA,CAAAK,YAAA,EAAI;;;;;;IAHiCL,EAAA,CAAAM,UAAA,YAAAW,MAAA,CAAAT,IAAA,CAAAC,OAAA,CAAwB,SAAAQ,MAAA,CAAAT,IAAA,CAAAI,GAAA,EAAAZ,EAAA,CAAAkB,aAAA,YAAAD,MAAA,CAAAT,IAAA,CAAAK,YAAA;IAE5Cb,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAM,UAAA,qBAAAS,GAAA,CAA6B;;;;;IAK1Cf,EAAA,CAAAmB,SAAA,YAAiE;;;;IAA9DnB,EAAA,CAAAM,UAAA,YAAAc,MAAA,CAAAZ,IAAA,CAAAa,IAAA,CAAqB;;;;;IACxBrB,EAAA,CAAAmB,SAAA,eAA6E;;;;IAAvEnB,EAAA,CAAAM,UAAA,iBAAAgB,OAAA,CAAAd,IAAA,CAAAa,IAAA,CAA0B;;;;;IAFlCrB,EAAA,CAAAuB,uBAAA,GAAgC;IAC9BvB,EAAA,CAAAG,UAAA,IAAAqB,sFAAA,eAAiE;IACjExB,EAAA,CAAAG,UAAA,IAAAsB,yFAAA,mBAA6E;IAC/EzB,EAAA,CAAA0B,qBAAA,EAAe;;;;IAFa1B,EAAA,CAAAc,SAAA,GAAiC;IAAjCd,EAAA,CAAAM,UAAA,SAAAqB,MAAA,CAAAnB,IAAA,CAAAa,IAAA,CAAAO,UAAA,QAAiC;IACzB5B,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAM,UAAA,UAAAqB,MAAA,CAAAnB,IAAA,CAAAa,IAAA,CAAAO,UAAA,QAAkC;;;;;IAGtE5B,EAAA,CAAAE,cAAA,eACiC;IAC/BF,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAK,YAAA,EAAO;;;;IAH6CL,EAAA,CAAAM,UAAA,cAAAwB,MAAA,CAAAtB,IAAA,CAAAuB,KAAA,CAAAC,SAAA,CAAkC,YAAAF,MAAA,CAAAtB,IAAA,CAAAuB,KAAA,CAAAtB,OAAA;IAEpFT,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiC,kBAAA,MAAAH,MAAA,CAAAtB,IAAA,CAAAuB,KAAA,CAAAG,KAAA,MACF;;;;;IARAlC,EAAA,CAAAG,UAAA,IAAAgC,kFAAA,0BAGe;IACfnC,EAAA,CAAAE,cAAA,cAAoE;IAAAF,EAAA,CAAA6B,MAAA,GAAgB;IAAA7B,EAAA,CAAAK,YAAA,EAAO;IAC3FL,EAAA,CAAAG,UAAA,IAAAiC,0EAAA,kBAGO;;;;IARQpC,EAAA,CAAAM,UAAA,SAAA+B,MAAA,CAAA7B,IAAA,CAAAa,IAAA,CAAe;IAISrB,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAAM,UAAA,cAAA+B,MAAA,CAAA7B,IAAA,CAAAwB,SAAA,CAA4B;IAAChC,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAAsC,iBAAA,CAAAD,MAAA,CAAA7B,IAAA,CAAA0B,KAAA,CAAgB;IAClDlC,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAAM,UAAA,SAAA+B,MAAA,CAAA7B,IAAA,CAAAuB,KAAA,CAAgB;;;;;IAnBtD/B,EAAA,CAAAuB,uBAAA,GAAmC;IAEjCvB,EAAA,CAAAG,UAAA,IAAAoC,yDAAA,eAGI;IAGJvC,EAAA,CAAAG,UAAA,IAAAqC,yDAAA,eAGI;IAEJxC,EAAA,CAAAG,UAAA,IAAAsC,mEAAA,gCAAAzC,EAAA,CAAA0C,sBAAA,CAUc;IAChB1C,EAAA,CAAA0B,qBAAA,EAAe;;;;IAtBkD1B,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAM,UAAA,SAAAqC,MAAA,CAAAnC,IAAA,CAAAI,GAAA,KAAA+B,MAAA,CAAAnC,IAAA,CAAAoC,WAAA,CAAmC;IAMnC5C,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAM,UAAA,SAAAqC,MAAA,CAAAnC,IAAA,CAAAI,GAAA,IAAA+B,MAAA,CAAAnC,IAAA,CAAAoC,WAAA,CAAkC;;;ACAnG,OAAM,MAAOC,6BAA6B;EAAA,QAAAC,CAAA;qBAA7BD,6BAA6B;EAAA;EAAA,QAAAE,EAAA;UAA7BF,6BAA6B;IAAAG,SAAA;IAAAC,MAAA;MAAAzC,IAAA;IAAA;IAAA0C,KAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDR1CzD,EAAA,CAAAG,UAAA,IAAAwD,qDAAA,0BAwBe;;;QAxBA3D,EAAA,CAAAM,UAAA,UAAAoD,GAAA,CAAAlD,IAAA,CAAAoD,MAAA,CAAkB", "names": ["i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵtemplate", "CoreMenuVerticalItemComponent_ng_container_0_a_1_ng_container_1_Template", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "item", "classes", "ɵɵpureFunction1", "_c1", "url", "openInNewTab", "ɵɵadvance", "_r3", "CoreMenuVerticalItemComponent_ng_container_0_a_2_ng_container_1_Template", "ctx_r2", "ɵɵsanitizeUrl", "ɵɵelement", "ctx_r9", "icon", "ctx_r10", "ɵɵelementContainerStart", "CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_i_1_Template", "CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_span_2_Template", "ɵɵelementContainerEnd", "ctx_r7", "startsWith", "ɵɵtext", "ctx_r8", "badge", "translate", "ɵɵtextInterpolate1", "title", "CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_ng_container_0_Template", "CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_span_3_Template", "ctx_r4", "ɵɵtextInterpolate", "CoreMenuVerticalItemComponent_ng_container_0_a_1_Template", "CoreMenuVerticalItemComponent_ng_container_0_a_2_Template", "CoreMenuVerticalItemComponent_ng_container_0_ng_template_3_Template", "ɵɵtemplateRefExtractor", "ctx_r0", "externalUrl", "CoreMenuVerticalItemComponent", "_", "_2", "selectors", "inputs", "attrs", "_c0", "decls", "vars", "consts", "template", "CoreMenuVerticalItemComponent_Template", "rf", "ctx", "CoreMenuVerticalItemComponent_ng_container_0_Template", "hidden"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\vertical\\item\\item.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\@core\\components\\core-menu\\vertical\\item\\item.component.ts"], "sourcesContent": ["<ng-container *ngIf=\"!item.hidden\">\r\n  <!-- item with router url -->\r\n  <a class=\"d-flex align-items-center\" [ngClass]=\"item.classes\" *ngIf=\"item.url && !item.externalUrl\"\r\n    [routerLink]=\"[item.url]\" [target]=\"item.openInNewTab ? '_blank' : '_self'\">\r\n    <ng-container *ngTemplateOutlet=\"itemContent\"></ng-container>\r\n  </a>\r\n\r\n  <!-- item with external url -->\r\n  <a class=\"d-flex align-items-center\" [ngClass]=\"item.classes\" *ngIf=\"item.url && item.externalUrl\" [href]=\"item.url\"\r\n    [target]=\"item.openInNewTab ? '_blank' : '_self'\">\r\n    <ng-container *ngTemplateOutlet=\"itemContent\"></ng-container>\r\n  </a>\r\n\r\n  <ng-template #itemContent>\r\n    <ng-container *ngIf=\"item.icon\">\r\n      <i [ngClass]=\"item.icon\" *ngIf=\"item.icon.startsWith('fa-')\"></i>\r\n      <span [data-feather]=\"item.icon\" *ngIf=\"!item.icon.startsWith('fa-')\"></span>\r\n    </ng-container>\r\n    <span class=\"menu-title text-truncate\" [translate]=\"item.translate\">{{ item.title }}</span>\r\n    <span class=\"badge ml-auto mr-1\" *ngIf=\"item.badge\" [translate]=\"item.badge.translate\"\r\n      [ngClass]=\"item.badge.classes\">\r\n      {{ item.badge.title }}\r\n    </span>\r\n  </ng-template>\r\n</ng-container>", "import { Component, Input } from '@angular/core';\r\n\r\nimport { CoreMenuItem } from '@core/types';\r\n\r\n@Component({\r\n  selector: '[core-menu-vertical-item]',\r\n  templateUrl: './item.component.html'\r\n})\r\nexport class CoreMenuVerticalItemComponent {\r\n  @Input()\r\n  item: CoreMenuItem;\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { onSnapshot, refEqual, getCountFromServer } from 'firebase/firestore';\nimport { Observable, from, pipe } from 'rxjs';\nimport { map, scan, distinctUntilChanged, filter, startWith, pairwise } from 'rxjs/operators';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar DEFAULT_OPTIONS = {\n  includeMetadataChanges: false\n};\nfunction fromRef(ref, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  /* eslint-enable @typescript-eslint/no-explicit-any */\n  return new Observable(function (subscriber) {\n    var unsubscribe = onSnapshot(ref, options, {\n      next: subscriber.next.bind(subscriber),\n      error: subscriber.error.bind(subscriber),\n      complete: subscriber.complete.bind(subscriber)\n    });\n    return {\n      unsubscribe: unsubscribe\n    };\n  });\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction doc(ref) {\n  return fromRef(ref, {\n    includeMetadataChanges: true\n  });\n}\n/**\n * Returns a stream of a document, mapped to its data payload and optionally the document ID\n * @param query\n * @param options\n */\nfunction docData(ref, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return doc(ref).pipe(map(function (snap) {\n    return snapToData(snap, options);\n  }));\n}\nfunction snapToData(snapshot, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var data = snapshot.data(options);\n  // match the behavior of the JS SDK when the snapshot doesn't exist\n  // it's possible with data converters too that the user didn't return an object\n  if (!snapshot.exists() || typeof data !== 'object' || data === null || !options.idField) {\n    return data;\n  }\n  return __assign(__assign({}, data), (_a = {}, _a[options.idField] = snapshot.id, _a));\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar ALL_EVENTS = ['added', 'modified', 'removed'];\n/**\n * Create an operator that determines if a the stream of document changes\n * are specified by the event filter. If the document change type is not\n * in specified events array, it will not be emitted.\n */\nvar filterEvents = function (events) {\n  return filter(function (changes) {\n    var hasChange = false;\n    for (var i = 0; i < changes.length; i++) {\n      var change = changes[i];\n      if (events && events.indexOf(change.type) >= 0) {\n        hasChange = true;\n        break;\n      }\n    }\n    return hasChange;\n  });\n};\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\nfunction sliceAndSplice(original, start, deleteCount) {\n  var args = [];\n  for (var _i = 3; _i < arguments.length; _i++) {\n    args[_i - 3] = arguments[_i];\n  }\n  var returnArray = original.slice();\n  returnArray.splice.apply(returnArray, __spreadArray([start, deleteCount], args, false));\n  return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * @param combined\n * @param change\n */\nfunction processIndividualChange(combined, change) {\n  switch (change.type) {\n    case 'added':\n      if (combined[change.newIndex] && refEqual(combined[change.newIndex].doc.ref, change.doc.ref)) ;else {\n        return sliceAndSplice(combined, change.newIndex, 0, change);\n      }\n      break;\n    case 'modified':\n      if (combined[change.oldIndex] == null || refEqual(combined[change.oldIndex].doc.ref, change.doc.ref)) {\n        // When an item changes position we first remove it\n        // and then add it's new position\n        if (change.oldIndex !== change.newIndex) {\n          var copiedArray = combined.slice();\n          copiedArray.splice(change.oldIndex, 1);\n          copiedArray.splice(change.newIndex, 0, change);\n          return copiedArray;\n        } else {\n          return sliceAndSplice(combined, change.newIndex, 1, change);\n        }\n      }\n      break;\n    case 'removed':\n      if (combined[change.oldIndex] && refEqual(combined[change.oldIndex].doc.ref, change.doc.ref)) {\n        return sliceAndSplice(combined, change.oldIndex, 1);\n      }\n      break;\n  }\n  return combined;\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n * @param current\n * @param changes\n * @param events\n */\nfunction processDocumentChanges(current, changes, events) {\n  if (events === void 0) {\n    events = ALL_EVENTS;\n  }\n  changes.forEach(function (change) {\n    // skip unwanted change types\n    if (events.indexOf(change.type) > -1) {\n      current = processIndividualChange(current, change);\n    }\n  });\n  return current;\n}\n/**\n * Create an operator that allows you to compare the current emission with\n * the prior, even on first emission (where prior is undefined).\n */\nvar windowwise = function () {\n  return pipe(startWith(undefined), pairwise());\n};\n/**\n * Given two snapshots does their metadata match?\n * @param a\n * @param b\n */\nvar metaDataEquals = function (a, b) {\n  return JSON.stringify(a.metadata) === JSON.stringify(b.metadata);\n};\n/**\n * Create an operator that filters out empty changes. We provide the\n * ability to filter on events, which means all changes can be filtered out.\n * This creates an empty array and would be incorrect to emit.\n */\nvar filterEmptyUnlessFirst = function () {\n  return pipe(windowwise(), filter(function (_a) {\n    var prior = _a[0],\n      current = _a[1];\n    return current.length > 0 || prior === undefined;\n  }), map(function (_a) {\n    var current = _a[1];\n    return current;\n  }));\n};\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n * @param query\n */\nfunction collectionChanges(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return fromRef(query, {\n    includeMetadataChanges: true\n  }).pipe(windowwise(), map(function (_a) {\n    var priorSnapshot = _a[0],\n      currentSnapshot = _a[1];\n    var docChanges = currentSnapshot.docChanges();\n    if (priorSnapshot && !metaDataEquals(priorSnapshot, currentSnapshot)) {\n      // the metadata has changed, docChanges() doesn't return metadata events, so let's\n      // do it ourselves by scanning over all the docs and seeing if the metadata has changed\n      // since either this docChanges() emission or the prior snapshot\n      currentSnapshot.docs.forEach(function (currentDocSnapshot, currentIndex) {\n        var currentDocChange = docChanges.find(function (c) {\n          return refEqual(c.doc.ref, currentDocSnapshot.ref);\n        });\n        if (currentDocChange) {\n          // if the doc is in the current changes and the metadata hasn't changed this doc\n          if (metaDataEquals(currentDocChange.doc, currentDocSnapshot)) {\n            return;\n          }\n        } else {\n          // if there is a prior doc and the metadata hasn't changed skip this doc\n          var priorDocSnapshot = priorSnapshot === null || priorSnapshot === void 0 ? void 0 : priorSnapshot.docs.find(function (d) {\n            return refEqual(d.ref, currentDocSnapshot.ref);\n          });\n          if (priorDocSnapshot && metaDataEquals(priorDocSnapshot, currentDocSnapshot)) {\n            return;\n          }\n        }\n        docChanges.push({\n          oldIndex: currentIndex,\n          newIndex: currentIndex,\n          type: 'modified',\n          doc: currentDocSnapshot\n        });\n      });\n    }\n    return docChanges;\n  }), filterEvents(options.events || ALL_EVENTS), filterEmptyUnlessFirst());\n}\n/**\n * Return a stream of document snapshots on a query. These results are in sort order.\n * @param query\n */\nfunction collection(query) {\n  return fromRef(query, {\n    includeMetadataChanges: true\n  }).pipe(map(function (changes) {\n    return changes.docs;\n  }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n * @param query\n */\nfunction sortedChanges(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return collectionChanges(query, options).pipe(scan(function (current, changes) {\n    return processDocumentChanges(current, changes, options.events);\n  }, []), distinctUntilChanged());\n}\n/**\n * Create a stream of changes as they occur it time. This method is similar\n * to docChanges() but it collects each event in an array over time.\n */\nfunction auditTrail(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return collectionChanges(query, options).pipe(scan(function (current, action) {\n    return __spreadArray(__spreadArray([], current, true), action, true);\n  }, []));\n}\n/**\n * Returns a stream of documents mapped to their data payload, and optionally the document ID\n * @param query\n * @param options\n */\nfunction collectionData(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return collection(query).pipe(map(function (arr) {\n    return arr.map(function (snap) {\n      return snapToData(snap, options);\n    });\n  }));\n}\nfunction collectionCountSnap(query) {\n  return from(getCountFromServer(query));\n}\nfunction collectionCount(query) {\n  return collectionCountSnap(query).pipe(map(function (snap) {\n    return snap.data().count;\n  }));\n}\nexport { auditTrail, collection, collectionChanges, collectionCount, collectionCountSnap, collectionData, doc, docData, fromRef, snapToData, sortedChanges };", "map": {"version": 3, "names": ["onSnapshot", "refEqual", "getCountFromServer", "Observable", "from", "pipe", "map", "scan", "distinctUntilChanged", "filter", "startWith", "pairwise", "__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__spread<PERSON><PERSON>y", "to", "pack", "l", "ar", "Array", "slice", "concat", "SuppressedError", "error", "suppressed", "message", "e", "Error", "name", "DEFAULT_OPTIONS", "includeMetadataChanges", "fromRef", "ref", "options", "subscriber", "unsubscribe", "next", "bind", "complete", "doc", "docData", "snap", "snapToData", "snapshot", "_a", "data", "exists", "idField", "id", "ALL_EVENTS", "filterEvents", "events", "changes", "hasChange", "change", "indexOf", "type", "sliceAndSplice", "original", "start", "deleteCount", "args", "_i", "returnArray", "splice", "processIndividualChange", "combined", "newIndex", "oldIndex", "copiedArray", "processDocumentChanges", "current", "for<PERSON>ach", "windowwise", "undefined", "metaDataEquals", "a", "b", "JSON", "stringify", "metadata", "filterEmptyUnlessFirst", "prior", "collectionChanges", "query", "priorSnapshot", "currentSnapshot", "do<PERSON><PERSON><PERSON><PERSON>", "docs", "currentDocSnapshot", "currentIndex", "currentDocChange", "find", "c", "priorDocSnapshot", "d", "push", "collection", "sortedChanges", "auditTrail", "action", "collectionData", "arr", "collectionCountSnap", "collectionCount", "count"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxfire/firestore/index.esm.js"], "sourcesContent": ["import { onSnapshot, refEqual, getCountFromServer } from 'firebase/firestore';\nimport { Observable, from, pipe } from 'rxjs';\nimport { map, scan, distinctUntilChanged, filter, startWith, pairwise } from 'rxjs/operators';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar DEFAULT_OPTIONS = { includeMetadataChanges: false };\nfunction fromRef(ref, options) {\n    if (options === void 0) { options = DEFAULT_OPTIONS; }\n    /* eslint-enable @typescript-eslint/no-explicit-any */\n    return new Observable(function (subscriber) {\n        var unsubscribe = onSnapshot(ref, options, {\n            next: subscriber.next.bind(subscriber),\n            error: subscriber.error.bind(subscriber),\n            complete: subscriber.complete.bind(subscriber),\n        });\n        return { unsubscribe: unsubscribe };\n    });\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction doc(ref) {\n    return fromRef(ref, { includeMetadataChanges: true });\n}\n/**\n * Returns a stream of a document, mapped to its data payload and optionally the document ID\n * @param query\n * @param options\n */\nfunction docData(ref, options) {\n    if (options === void 0) { options = {}; }\n    return doc(ref).pipe(map(function (snap) { return snapToData(snap, options); }));\n}\nfunction snapToData(snapshot, options) {\n    var _a;\n    if (options === void 0) { options = {}; }\n    var data = snapshot.data(options);\n    // match the behavior of the JS SDK when the snapshot doesn't exist\n    // it's possible with data converters too that the user didn't return an object\n    if (!snapshot.exists() || typeof data !== 'object' || data === null || !options.idField) {\n        return data;\n    }\n    return __assign(__assign({}, data), (_a = {}, _a[options.idField] = snapshot.id, _a));\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar ALL_EVENTS = ['added', 'modified', 'removed'];\n/**\n * Create an operator that determines if a the stream of document changes\n * are specified by the event filter. If the document change type is not\n * in specified events array, it will not be emitted.\n */\nvar filterEvents = function (events) {\n    return filter(function (changes) {\n        var hasChange = false;\n        for (var i = 0; i < changes.length; i++) {\n            var change = changes[i];\n            if (events && events.indexOf(change.type) >= 0) {\n                hasChange = true;\n                break;\n            }\n        }\n        return hasChange;\n    });\n};\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\nfunction sliceAndSplice(original, start, deleteCount) {\n    var args = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        args[_i - 3] = arguments[_i];\n    }\n    var returnArray = original.slice();\n    returnArray.splice.apply(returnArray, __spreadArray([start, deleteCount], args, false));\n    return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * @param combined\n * @param change\n */\nfunction processIndividualChange(combined, change) {\n    switch (change.type) {\n        case 'added':\n            if (combined[change.newIndex] &&\n                refEqual(combined[change.newIndex].doc.ref, change.doc.ref)) ;\n            else {\n                return sliceAndSplice(combined, change.newIndex, 0, change);\n            }\n            break;\n        case 'modified':\n            if (combined[change.oldIndex] == null ||\n                refEqual(combined[change.oldIndex].doc.ref, change.doc.ref)) {\n                // When an item changes position we first remove it\n                // and then add it's new position\n                if (change.oldIndex !== change.newIndex) {\n                    var copiedArray = combined.slice();\n                    copiedArray.splice(change.oldIndex, 1);\n                    copiedArray.splice(change.newIndex, 0, change);\n                    return copiedArray;\n                }\n                else {\n                    return sliceAndSplice(combined, change.newIndex, 1, change);\n                }\n            }\n            break;\n        case 'removed':\n            if (combined[change.oldIndex] &&\n                refEqual(combined[change.oldIndex].doc.ref, change.doc.ref)) {\n                return sliceAndSplice(combined, change.oldIndex, 1);\n            }\n            break;\n    }\n    return combined;\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n * @param current\n * @param changes\n * @param events\n */\nfunction processDocumentChanges(current, changes, events) {\n    if (events === void 0) { events = ALL_EVENTS; }\n    changes.forEach(function (change) {\n        // skip unwanted change types\n        if (events.indexOf(change.type) > -1) {\n            current = processIndividualChange(current, change);\n        }\n    });\n    return current;\n}\n/**\n * Create an operator that allows you to compare the current emission with\n * the prior, even on first emission (where prior is undefined).\n */\nvar windowwise = function () {\n    return pipe(startWith(undefined), pairwise());\n};\n/**\n * Given two snapshots does their metadata match?\n * @param a\n * @param b\n */\nvar metaDataEquals = function (a, b) { return JSON.stringify(a.metadata) === JSON.stringify(b.metadata); };\n/**\n * Create an operator that filters out empty changes. We provide the\n * ability to filter on events, which means all changes can be filtered out.\n * This creates an empty array and would be incorrect to emit.\n */\nvar filterEmptyUnlessFirst = function () {\n    return pipe(windowwise(), filter(function (_a) {\n        var prior = _a[0], current = _a[1];\n        return current.length > 0 || prior === undefined;\n    }), map(function (_a) {\n        var current = _a[1];\n        return current;\n    }));\n};\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n * @param query\n */\nfunction collectionChanges(query, options) {\n    if (options === void 0) { options = {}; }\n    return fromRef(query, { includeMetadataChanges: true }).pipe(windowwise(), map(function (_a) {\n        var priorSnapshot = _a[0], currentSnapshot = _a[1];\n        var docChanges = currentSnapshot.docChanges();\n        if (priorSnapshot && !metaDataEquals(priorSnapshot, currentSnapshot)) {\n            // the metadata has changed, docChanges() doesn't return metadata events, so let's\n            // do it ourselves by scanning over all the docs and seeing if the metadata has changed\n            // since either this docChanges() emission or the prior snapshot\n            currentSnapshot.docs.forEach(function (currentDocSnapshot, currentIndex) {\n                var currentDocChange = docChanges.find(function (c) {\n                    return refEqual(c.doc.ref, currentDocSnapshot.ref);\n                });\n                if (currentDocChange) {\n                    // if the doc is in the current changes and the metadata hasn't changed this doc\n                    if (metaDataEquals(currentDocChange.doc, currentDocSnapshot)) {\n                        return;\n                    }\n                }\n                else {\n                    // if there is a prior doc and the metadata hasn't changed skip this doc\n                    var priorDocSnapshot = priorSnapshot === null || priorSnapshot === void 0 ? void 0 : priorSnapshot.docs.find(function (d) {\n                        return refEqual(d.ref, currentDocSnapshot.ref);\n                    });\n                    if (priorDocSnapshot &&\n                        metaDataEquals(priorDocSnapshot, currentDocSnapshot)) {\n                        return;\n                    }\n                }\n                docChanges.push({\n                    oldIndex: currentIndex,\n                    newIndex: currentIndex,\n                    type: 'modified',\n                    doc: currentDocSnapshot,\n                });\n            });\n        }\n        return docChanges;\n    }), filterEvents(options.events || ALL_EVENTS), filterEmptyUnlessFirst());\n}\n/**\n * Return a stream of document snapshots on a query. These results are in sort order.\n * @param query\n */\nfunction collection(query) {\n    return fromRef(query, { includeMetadataChanges: true }).pipe(map(function (changes) { return changes.docs; }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n * @param query\n */\nfunction sortedChanges(query, options) {\n    if (options === void 0) { options = {}; }\n    return collectionChanges(query, options).pipe(scan(function (current, changes) {\n        return processDocumentChanges(current, changes, options.events);\n    }, []), distinctUntilChanged());\n}\n/**\n * Create a stream of changes as they occur it time. This method is similar\n * to docChanges() but it collects each event in an array over time.\n */\nfunction auditTrail(query, options) {\n    if (options === void 0) { options = {}; }\n    return collectionChanges(query, options).pipe(scan(function (current, action) { return __spreadArray(__spreadArray([], current, true), action, true); }, []));\n}\n/**\n * Returns a stream of documents mapped to their data payload, and optionally the document ID\n * @param query\n * @param options\n */\nfunction collectionData(query, options) {\n    if (options === void 0) { options = {}; }\n    return collection(query).pipe(map(function (arr) {\n        return arr.map(function (snap) { return snapToData(snap, options); });\n    }));\n}\nfunction collectionCountSnap(query) {\n    return from(getCountFromServer(query));\n}\nfunction collectionCount(query) {\n    return collectionCountSnap(query).pipe(map(function (snap) { return snap.data().count; }));\n}\n\nexport { auditTrail, collection, collectionChanges, collectionCount, collectionCountSnap, collectionData, doc, docData, fromRef, snapToData, sortedChanges };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,kBAAkB,QAAQ,oBAAoB;AAC7E,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,QAAQ,MAAM;AAC7C,SAASC,GAAG,EAAEC,IAAI,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;;AAE7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAW;EACtBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASF,QAAQA,CAACG,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAChF;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AAED,SAASO,aAAaA,CAACC,EAAE,EAAEvB,IAAI,EAAEwB,IAAI,EAAE;EACnC,IAAIA,IAAI,IAAIT,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAGzB,IAAI,CAACgB,MAAM,EAAEU,EAAE,EAAEb,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EAAE;IACjF,IAAIa,EAAE,IAAI,EAAEb,CAAC,IAAIb,IAAI,CAAC,EAAE;MACpB,IAAI,CAAC0B,EAAE,EAAEA,EAAE,GAAGC,KAAK,CAACT,SAAS,CAACU,KAAK,CAACR,IAAI,CAACpB,IAAI,EAAE,CAAC,EAAEa,CAAC,CAAC;MACpDa,EAAE,CAACb,CAAC,CAAC,GAAGb,IAAI,CAACa,CAAC,CAAC;IACnB;EACJ;EACA,OAAOU,EAAE,CAACM,MAAM,CAACH,EAAE,IAAIC,KAAK,CAACT,SAAS,CAACU,KAAK,CAACR,IAAI,CAACpB,IAAI,CAAC,CAAC;AAC5D;AAEA,OAAO8B,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAC5F,IAAIC,CAAC,GAAG,IAAIC,KAAK,CAACF,OAAO,CAAC;EAC1B,OAAOC,CAAC,CAACE,IAAI,GAAG,iBAAiB,EAAEF,CAAC,CAACH,KAAK,GAAGA,KAAK,EAAEG,CAAC,CAACF,UAAU,GAAGA,UAAU,EAAEE,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,eAAe,GAAG;EAAEC,sBAAsB,EAAE;AAAM,CAAC;AACvD,SAASC,OAAOA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAC3B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAGJ,eAAe;EAAE;EACrD;EACA,OAAO,IAAItC,UAAU,CAAC,UAAU2C,UAAU,EAAE;IACxC,IAAIC,WAAW,GAAG/C,UAAU,CAAC4C,GAAG,EAAEC,OAAO,EAAE;MACvCG,IAAI,EAAEF,UAAU,CAACE,IAAI,CAACC,IAAI,CAACH,UAAU,CAAC;MACtCX,KAAK,EAAEW,UAAU,CAACX,KAAK,CAACc,IAAI,CAACH,UAAU,CAAC;MACxCI,QAAQ,EAAEJ,UAAU,CAACI,QAAQ,CAACD,IAAI,CAACH,UAAU;IACjD,CAAC,CAAC;IACF,OAAO;MAAEC,WAAW,EAAEA;IAAY,CAAC;EACvC,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,GAAGA,CAACP,GAAG,EAAE;EACd,OAAOD,OAAO,CAACC,GAAG,EAAE;IAAEF,sBAAsB,EAAE;EAAK,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,OAAOA,CAACR,GAAG,EAAEC,OAAO,EAAE;EAC3B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOM,GAAG,CAACP,GAAG,CAAC,CAACvC,IAAI,CAACC,GAAG,CAAC,UAAU+C,IAAI,EAAE;IAAE,OAAOC,UAAU,CAACD,IAAI,EAAER,OAAO,CAAC;EAAE,CAAC,CAAC,CAAC;AACpF;AACA,SAASS,UAAUA,CAACC,QAAQ,EAAEV,OAAO,EAAE;EACnC,IAAIW,EAAE;EACN,IAAIX,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,IAAIY,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACZ,OAAO,CAAC;EACjC;EACA;EACA,IAAI,CAACU,QAAQ,CAACG,MAAM,CAAC,CAAC,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,CAACZ,OAAO,CAACc,OAAO,EAAE;IACrF,OAAOF,IAAI;EACf;EACA,OAAO7C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6C,IAAI,CAAC,GAAGD,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACX,OAAO,CAACc,OAAO,CAAC,GAAGJ,QAAQ,CAACK,EAAE,EAAEJ,EAAE,CAAC,CAAC;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,UAAU,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,SAAAA,CAAUC,MAAM,EAAE;EACjC,OAAOtD,MAAM,CAAC,UAAUuD,OAAO,EAAE;IAC7B,IAAIC,SAAS,GAAG,KAAK;IACrB,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,OAAO,CAAC5C,MAAM,EAAEH,CAAC,EAAE,EAAE;MACrC,IAAIiD,MAAM,GAAGF,OAAO,CAAC/C,CAAC,CAAC;MACvB,IAAI8C,MAAM,IAAIA,MAAM,CAACI,OAAO,CAACD,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE;QAC5CH,SAAS,GAAG,IAAI;QAChB;MACJ;IACJ;IACA,OAAOA,SAAS;EACpB,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,SAASI,cAAcA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAClD,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGvD,SAAS,CAACC,MAAM,EAAEsD,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGvD,SAAS,CAACuD,EAAE,CAAC;EAChC;EACA,IAAIC,WAAW,GAAGL,QAAQ,CAACtC,KAAK,CAAC,CAAC;EAClC2C,WAAW,CAACC,MAAM,CAACnD,KAAK,CAACkD,WAAW,EAAEjD,aAAa,CAAC,CAAC6C,KAAK,EAAEC,WAAW,CAAC,EAAEC,IAAI,EAAE,KAAK,CAAC,CAAC;EACvF,OAAOE,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAACC,QAAQ,EAAEZ,MAAM,EAAE;EAC/C,QAAQA,MAAM,CAACE,IAAI;IACf,KAAK,OAAO;MACR,IAAIU,QAAQ,CAACZ,MAAM,CAACa,QAAQ,CAAC,IACzB9E,QAAQ,CAAC6E,QAAQ,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC5B,GAAG,CAACP,GAAG,EAAEsB,MAAM,CAACf,GAAG,CAACP,GAAG,CAAC,EAAE,CAAC,KAC7D;QACD,OAAOyB,cAAc,CAACS,QAAQ,EAAEZ,MAAM,CAACa,QAAQ,EAAE,CAAC,EAAEb,MAAM,CAAC;MAC/D;MACA;IACJ,KAAK,UAAU;MACX,IAAIY,QAAQ,CAACZ,MAAM,CAACc,QAAQ,CAAC,IAAI,IAAI,IACjC/E,QAAQ,CAAC6E,QAAQ,CAACZ,MAAM,CAACc,QAAQ,CAAC,CAAC7B,GAAG,CAACP,GAAG,EAAEsB,MAAM,CAACf,GAAG,CAACP,GAAG,CAAC,EAAE;QAC7D;QACA;QACA,IAAIsB,MAAM,CAACc,QAAQ,KAAKd,MAAM,CAACa,QAAQ,EAAE;UACrC,IAAIE,WAAW,GAAGH,QAAQ,CAAC9C,KAAK,CAAC,CAAC;UAClCiD,WAAW,CAACL,MAAM,CAACV,MAAM,CAACc,QAAQ,EAAE,CAAC,CAAC;UACtCC,WAAW,CAACL,MAAM,CAACV,MAAM,CAACa,QAAQ,EAAE,CAAC,EAAEb,MAAM,CAAC;UAC9C,OAAOe,WAAW;QACtB,CAAC,MACI;UACD,OAAOZ,cAAc,CAACS,QAAQ,EAAEZ,MAAM,CAACa,QAAQ,EAAE,CAAC,EAAEb,MAAM,CAAC;QAC/D;MACJ;MACA;IACJ,KAAK,SAAS;MACV,IAAIY,QAAQ,CAACZ,MAAM,CAACc,QAAQ,CAAC,IACzB/E,QAAQ,CAAC6E,QAAQ,CAACZ,MAAM,CAACc,QAAQ,CAAC,CAAC7B,GAAG,CAACP,GAAG,EAAEsB,MAAM,CAACf,GAAG,CAACP,GAAG,CAAC,EAAE;QAC7D,OAAOyB,cAAc,CAACS,QAAQ,EAAEZ,MAAM,CAACc,QAAQ,EAAE,CAAC,CAAC;MACvD;MACA;EACR;EACA,OAAOF,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,sBAAsBA,CAACC,OAAO,EAAEnB,OAAO,EAAED,MAAM,EAAE;EACtD,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IAAEA,MAAM,GAAGF,UAAU;EAAE;EAC9CG,OAAO,CAACoB,OAAO,CAAC,UAAUlB,MAAM,EAAE;IAC9B;IACA,IAAIH,MAAM,CAACI,OAAO,CAACD,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAClCe,OAAO,GAAGN,uBAAuB,CAACM,OAAO,EAAEjB,MAAM,CAAC;IACtD;EACJ,CAAC,CAAC;EACF,OAAOiB,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA,IAAIE,UAAU,GAAG,SAAAA,CAAA,EAAY;EACzB,OAAOhF,IAAI,CAACK,SAAS,CAAC4E,SAAS,CAAC,EAAE3E,QAAQ,CAAC,CAAC,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,IAAI4E,cAAc,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOC,IAAI,CAACC,SAAS,CAACH,CAAC,CAACI,QAAQ,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACF,CAAC,CAACG,QAAQ,CAAC;AAAE,CAAC;AAC1G;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,SAAAA,CAAA,EAAY;EACrC,OAAOxF,IAAI,CAACgF,UAAU,CAAC,CAAC,EAAE5E,MAAM,CAAC,UAAU+C,EAAE,EAAE;IAC3C,IAAIsC,KAAK,GAAGtC,EAAE,CAAC,CAAC,CAAC;MAAE2B,OAAO,GAAG3B,EAAE,CAAC,CAAC,CAAC;IAClC,OAAO2B,OAAO,CAAC/D,MAAM,GAAG,CAAC,IAAI0E,KAAK,KAAKR,SAAS;EACpD,CAAC,CAAC,EAAEhF,GAAG,CAAC,UAAUkD,EAAE,EAAE;IAClB,IAAI2B,OAAO,GAAG3B,EAAE,CAAC,CAAC,CAAC;IACnB,OAAO2B,OAAO;EAClB,CAAC,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASY,iBAAiBA,CAACC,KAAK,EAAEnD,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOF,OAAO,CAACqD,KAAK,EAAE;IAAEtD,sBAAsB,EAAE;EAAK,CAAC,CAAC,CAACrC,IAAI,CAACgF,UAAU,CAAC,CAAC,EAAE/E,GAAG,CAAC,UAAUkD,EAAE,EAAE;IACzF,IAAIyC,aAAa,GAAGzC,EAAE,CAAC,CAAC,CAAC;MAAE0C,eAAe,GAAG1C,EAAE,CAAC,CAAC,CAAC;IAClD,IAAI2C,UAAU,GAAGD,eAAe,CAACC,UAAU,CAAC,CAAC;IAC7C,IAAIF,aAAa,IAAI,CAACV,cAAc,CAACU,aAAa,EAAEC,eAAe,CAAC,EAAE;MAClE;MACA;MACA;MACAA,eAAe,CAACE,IAAI,CAAChB,OAAO,CAAC,UAAUiB,kBAAkB,EAAEC,YAAY,EAAE;QACrE,IAAIC,gBAAgB,GAAGJ,UAAU,CAACK,IAAI,CAAC,UAAUC,CAAC,EAAE;UAChD,OAAOxG,QAAQ,CAACwG,CAAC,CAACtD,GAAG,CAACP,GAAG,EAAEyD,kBAAkB,CAACzD,GAAG,CAAC;QACtD,CAAC,CAAC;QACF,IAAI2D,gBAAgB,EAAE;UAClB;UACA,IAAIhB,cAAc,CAACgB,gBAAgB,CAACpD,GAAG,EAAEkD,kBAAkB,CAAC,EAAE;YAC1D;UACJ;QACJ,CAAC,MACI;UACD;UACA,IAAIK,gBAAgB,GAAGT,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG,IAAI,CAACI,IAAI,CAAC,UAAUG,CAAC,EAAE;YACtH,OAAO1G,QAAQ,CAAC0G,CAAC,CAAC/D,GAAG,EAAEyD,kBAAkB,CAACzD,GAAG,CAAC;UAClD,CAAC,CAAC;UACF,IAAI8D,gBAAgB,IAChBnB,cAAc,CAACmB,gBAAgB,EAAEL,kBAAkB,CAAC,EAAE;YACtD;UACJ;QACJ;QACAF,UAAU,CAACS,IAAI,CAAC;UACZ5B,QAAQ,EAAEsB,YAAY;UACtBvB,QAAQ,EAAEuB,YAAY;UACtBlC,IAAI,EAAE,UAAU;UAChBjB,GAAG,EAAEkD;QACT,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,OAAOF,UAAU;EACrB,CAAC,CAAC,EAAErC,YAAY,CAACjB,OAAO,CAACkB,MAAM,IAAIF,UAAU,CAAC,EAAEgC,sBAAsB,CAAC,CAAC,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA,SAASgB,UAAUA,CAACb,KAAK,EAAE;EACvB,OAAOrD,OAAO,CAACqD,KAAK,EAAE;IAAEtD,sBAAsB,EAAE;EAAK,CAAC,CAAC,CAACrC,IAAI,CAACC,GAAG,CAAC,UAAU0D,OAAO,EAAE;IAAE,OAAOA,OAAO,CAACoC,IAAI;EAAE,CAAC,CAAC,CAAC;AAClH;AACA;AACA;AACA;AACA;AACA,SAASU,aAAaA,CAACd,KAAK,EAAEnD,OAAO,EAAE;EACnC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOkD,iBAAiB,CAACC,KAAK,EAAEnD,OAAO,CAAC,CAACxC,IAAI,CAACE,IAAI,CAAC,UAAU4E,OAAO,EAAEnB,OAAO,EAAE;IAC3E,OAAOkB,sBAAsB,CAACC,OAAO,EAAEnB,OAAO,EAAEnB,OAAO,CAACkB,MAAM,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC,EAAEvD,oBAAoB,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,SAASuG,UAAUA,CAACf,KAAK,EAAEnD,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOkD,iBAAiB,CAACC,KAAK,EAAEnD,OAAO,CAAC,CAACxC,IAAI,CAACE,IAAI,CAAC,UAAU4E,OAAO,EAAE6B,MAAM,EAAE;IAAE,OAAOtF,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEyD,OAAO,EAAE,IAAI,CAAC,EAAE6B,MAAM,EAAE,IAAI,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AACjK;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACjB,KAAK,EAAEnD,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOgE,UAAU,CAACb,KAAK,CAAC,CAAC3F,IAAI,CAACC,GAAG,CAAC,UAAU4G,GAAG,EAAE;IAC7C,OAAOA,GAAG,CAAC5G,GAAG,CAAC,UAAU+C,IAAI,EAAE;MAAE,OAAOC,UAAU,CAACD,IAAI,EAAER,OAAO,CAAC;IAAE,CAAC,CAAC;EACzE,CAAC,CAAC,CAAC;AACP;AACA,SAASsE,mBAAmBA,CAACnB,KAAK,EAAE;EAChC,OAAO5F,IAAI,CAACF,kBAAkB,CAAC8F,KAAK,CAAC,CAAC;AAC1C;AACA,SAASoB,eAAeA,CAACpB,KAAK,EAAE;EAC5B,OAAOmB,mBAAmB,CAACnB,KAAK,CAAC,CAAC3F,IAAI,CAACC,GAAG,CAAC,UAAU+C,IAAI,EAAE;IAAE,OAAOA,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC4D,KAAK;EAAE,CAAC,CAAC,CAAC;AAC9F;AAEA,SAASN,UAAU,EAAEF,UAAU,EAAEd,iBAAiB,EAAEqB,eAAe,EAAED,mBAAmB,EAAEF,cAAc,EAAE9D,GAAG,EAAEC,OAAO,EAAET,OAAO,EAAEW,UAAU,EAAEwD,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, Injectable } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { isSupported as isSupported$2 } from 'firebase/remote-config';\nimport { isSupported as isSupported$1 } from 'firebase/messaging';\nimport { isSupported } from 'firebase/analytics';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { tap, observeOn, subscribeOn } from 'rxjs/operators';\nconst VERSION = new Version('7.5.0');\nconst isAnalyticsSupportedValueSymbol = '__angularfire_symbol__analyticsIsSupportedValue';\nconst isAnalyticsSupportedPromiseSymbol = '__angularfire_symbol__analyticsIsSupported';\nconst isRemoteConfigSupportedValueSymbol = '__angularfire_symbol__remoteConfigIsSupportedValue';\nconst isRemoteConfigSupportedPromiseSymbol = '__angularfire_symbol__remoteConfigIsSupported';\nconst isMessagingSupportedValueSymbol = '__angularfire_symbol__messagingIsSupportedValue';\nconst isMessagingSupportedPromiseSymbol = '__angularfire_symbol__messagingIsSupported';\nglobalThis[isAnalyticsSupportedPromiseSymbol] || (globalThis[isAnalyticsSupportedPromiseSymbol] = isSupported().then(it => globalThis[isAnalyticsSupportedValueSymbol] = it).catch(() => globalThis[isAnalyticsSupportedValueSymbol] = false));\nglobalThis[isMessagingSupportedPromiseSymbol] || (globalThis[isMessagingSupportedPromiseSymbol] = isSupported$1().then(it => globalThis[isMessagingSupportedValueSymbol] = it).catch(() => globalThis[isMessagingSupportedValueSymbol] = false));\nglobalThis[isRemoteConfigSupportedPromiseSymbol] || (globalThis[isRemoteConfigSupportedPromiseSymbol] = isSupported$2().then(it => globalThis[isRemoteConfigSupportedValueSymbol] = it).catch(() => globalThis[isRemoteConfigSupportedValueSymbol] = false));\nconst isSupportedError = module => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\nconst ɵisMessagingSupportedFactory = {\n  async: () => globalThis[isMessagingSupportedPromiseSymbol],\n  sync: () => {\n    const ret = globalThis[isMessagingSupportedValueSymbol];\n    if (ret === undefined) {\n      throw new Error(isSupportedError('Messaging'));\n    }\n    return ret;\n  }\n};\nconst ɵisRemoteConfigSupportedFactory = {\n  async: () => globalThis[isRemoteConfigSupportedPromiseSymbol],\n  sync: () => {\n    const ret = globalThis[isRemoteConfigSupportedValueSymbol];\n    if (ret === undefined) {\n      throw new Error(isSupportedError('RemoteConfig'));\n    }\n    return ret;\n  }\n};\nconst ɵisAnalyticsSupportedFactory = {\n  async: () => globalThis[isAnalyticsSupportedPromiseSymbol],\n  sync: () => {\n    const ret = globalThis[isAnalyticsSupportedValueSymbol];\n    if (ret === undefined) {\n      throw new Error(isSupportedError('Analytics'));\n    }\n    return ret;\n  }\n};\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n  if (provided) {\n    // Was provide* only called once? If so grab that\n    if (provided.length === 1) {\n      return provided[0];\n    }\n    const providedUsingDefaultApp = provided.filter(it => it.app === defaultApp);\n    // Was provide* only called once, using the default app? If so use that\n    if (providedUsingDefaultApp.length === 1) {\n      return providedUsingDefaultApp[0];\n    }\n  }\n  // Grab the default instance from the defaultApp\n  const defaultAppWithContainer = defaultApp;\n  const provider = defaultAppWithContainer.container.getProvider(identifier);\n  return provider.getImmediate({\n    optional: true\n  });\n}\nconst ɵgetAllInstancesOf = (identifier, app) => {\n  const apps = app ? [app] : getApps();\n  const instances = [];\n  apps.forEach(app => {\n    const provider = app.container.getProvider(identifier);\n    provider.instances.forEach(instance => {\n      if (!instances.includes(instance)) {\n        instances.push(instance);\n      }\n    });\n  });\n  return instances;\n};\nfunction noop() {}\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\n// tslint:disable-next-line:class-name\nclass ɵZoneScheduler {\n  constructor(zone, delegate = queueScheduler) {\n    this.zone = zone;\n    this.delegate = delegate;\n  }\n  now() {\n    return this.delegate.now();\n  }\n  schedule(work, delay, state) {\n    const targetZone = this.zone;\n    // Wrap the specified work function to make sure that if nested scheduling takes place the\n    // work is executed in the correct zone\n    const workInZone = function (state) {\n      targetZone.runGuarded(() => {\n        work.apply(this, [state]);\n      });\n    };\n    // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n    // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n    // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n    return this.delegate.schedule(workInZone, delay, state);\n  }\n}\nclass BlockUntilFirstOperator {\n  constructor(zone) {\n    this.zone = zone;\n    this.task = null;\n  }\n  call(subscriber, source) {\n    const unscheduleTask = this.unscheduleTask.bind(this);\n    this.task = this.zone.run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n    return source.pipe(tap({\n      next: unscheduleTask,\n      complete: unscheduleTask,\n      error: unscheduleTask\n    })).subscribe(subscriber).add(unscheduleTask);\n  }\n  unscheduleTask() {\n    // maybe this is a race condition, invoke in a timeout\n    // hold for 10ms while I try to figure out what is going on\n    setTimeout(() => {\n      if (this.task != null && this.task.state === 'scheduled') {\n        this.task.invoke();\n        this.task = null;\n      }\n    }, 10);\n  }\n}\n// tslint:disable-next-line:class-name\nclass ɵAngularFireSchedulers {\n  constructor(ngZone) {\n    this.ngZone = ngZone;\n    this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(Zone.current));\n    this.insideAngular = ngZone.run(() => new ɵZoneScheduler(Zone.current, asyncScheduler));\n    globalThis.ɵAngularFireScheduler || (globalThis.ɵAngularFireScheduler = this);\n  }\n}\nɵAngularFireSchedulers.ɵfac = function ɵAngularFireSchedulers_Factory(t) {\n  return new (t || ɵAngularFireSchedulers)(i0.ɵɵinject(i0.NgZone));\n};\nɵAngularFireSchedulers.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ɵAngularFireSchedulers,\n  factory: ɵAngularFireSchedulers.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵAngularFireSchedulers, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nfunction getSchedulers() {\n  const schedulers = globalThis.ɵAngularFireScheduler;\n  if (!schedulers) {\n    throw new Error(`Either AngularFireModule has not been provided in your AppModule (this can be done manually or implictly using\nprovideFirebaseApp) or you're calling an AngularFire method outside of an NgModule (which is not supported).`);\n  }\n  return schedulers;\n}\nfunction runOutsideAngular(fn) {\n  return getSchedulers().ngZone.runOutsideAngular(() => fn());\n}\nfunction run(fn) {\n  return getSchedulers().ngZone.run(() => fn());\n}\nfunction observeOutsideAngular(obs$) {\n  return obs$.pipe(observeOn(getSchedulers().outsideAngular));\n}\nfunction observeInsideAngular(obs$) {\n  return obs$.pipe(observeOn(getSchedulers().insideAngular));\n}\nfunction keepUnstableUntilFirst(obs$) {\n  const scheduler = getSchedulers();\n  return ɵkeepUnstableUntilFirstFactory(getSchedulers())(obs$);\n}\n/**\n * Operator to block the zone until the first value has been emitted or the observable\n * has completed/errored. This is used to make sure that universal waits until the first\n * value from firebase but doesn't block the zone forever since the firebase subscription\n * is still alive.\n */\nfunction ɵkeepUnstableUntilFirstFactory(schedulers) {\n  return function keepUnstableUntilFirst(obs$) {\n    obs$ = obs$.lift(new BlockUntilFirstOperator(schedulers.ngZone));\n    return obs$.pipe(\n    // Run the subscribe body outside of Angular (e.g. calling Firebase SDK to add a listener to a change event)\n    subscribeOn(schedulers.outsideAngular),\n    // Run operators inside the angular zone (e.g. side effects via tap())\n    observeOn(schedulers.insideAngular)\n    // INVESTIGATE https://github.com/angular/angularfire/pull/2315\n    // share()\n    );\n  };\n}\n\nconst zoneWrapFn = (it, macrotask) => {\n  const _this = this;\n  // function() is needed for the arguments object\n  // tslint:disable-next-line:only-arrow-functions\n  return function () {\n    const _arguments = arguments;\n    if (macrotask) {\n      setTimeout(() => {\n        if (macrotask.state === 'scheduled') {\n          macrotask.invoke();\n        }\n      }, 10);\n    }\n    return run(() => it.apply(_this, _arguments));\n  };\n};\nconst ɵzoneWrap = (it, blockUntilFirst) => {\n  // function() is needed for the arguments object\n  // tslint:disable-next-line:only-arrow-functions\n  return function () {\n    let macrotask;\n    const _arguments = arguments;\n    // if this is a callback function, e.g, onSnapshot, we should create a microtask and invoke it\n    // only once one of the callback functions is tripped.\n    for (let i = 0; i < arguments.length; i++) {\n      if (typeof _arguments[i] === 'function') {\n        if (blockUntilFirst) {\n          macrotask || (macrotask = run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop)));\n        }\n        // TODO create a microtask to track callback functions\n        _arguments[i] = zoneWrapFn(_arguments[i], macrotask);\n      }\n    }\n    const ret = runOutsideAngular(() => it.apply(this, _arguments));\n    if (!blockUntilFirst) {\n      if (ret instanceof Observable) {\n        const schedulers = getSchedulers();\n        return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      } else {\n        return run(() => ret);\n      }\n    }\n    if (ret instanceof Observable) {\n      return ret.pipe(keepUnstableUntilFirst);\n    } else if (ret instanceof Promise) {\n      return run(() => new Promise((resolve, reject) => ret.then(it => run(() => resolve(it)), reason => run(() => reject(reason)))));\n    } else if (typeof ret === 'function' && macrotask) {\n      // Handle unsubscribe\n      // function() is needed for the arguments object\n      // tslint:disable-next-line:only-arrow-functions\n      return function () {\n        setTimeout(() => {\n          if (macrotask && macrotask.state === 'scheduled') {\n            macrotask.invoke();\n          }\n        }, 10);\n        return ret.apply(this, arguments);\n      };\n    } else {\n      // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n      return run(() => ret);\n    }\n  };\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VERSION, keepUnstableUntilFirst, observeInsideAngular, observeOutsideAngular, ɵAngularFireSchedulers, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisAnalyticsSupportedFactory, ɵisMessagingSupportedFactory, ɵisRemoteConfigSupportedFactory, ɵkeepUnstableUntilFirstFactory, ɵzoneWrap };", "map": {"version": 3, "names": ["i0", "Version", "Injectable", "getApps", "isSupported", "isSupported$2", "isSupported$1", "queueScheduler", "asyncScheduler", "Observable", "tap", "observeOn", "subscribeOn", "VERSION", "isAnalyticsSupportedValueSymbol", "isAnalyticsSupportedPromiseSymbol", "isRemoteConfigSupportedValueSymbol", "isRemoteConfigSupportedPromiseSymbol", "isMessagingSupportedValueSymbol", "isMessagingSupportedPromiseSymbol", "globalThis", "then", "it", "catch", "isSupportedError", "module", "ɵisMessagingSupportedFactory", "async", "sync", "ret", "undefined", "Error", "ɵisRemoteConfigSupportedFactory", "ɵisAnalyticsSupportedFactory", "ɵgetDefaultInstanceOf", "identifier", "provided", "defaultApp", "length", "providedUsingDefaultApp", "filter", "app", "defaultAppWithContainer", "provider", "container", "get<PERSON><PERSON><PERSON>", "getImmediate", "optional", "ɵgetAllInstancesOf", "apps", "instances", "for<PERSON>ach", "instance", "includes", "push", "noop", "ɵZoneScheduler", "constructor", "zone", "delegate", "now", "schedule", "work", "delay", "state", "targetZone", "workInZone", "runGuarded", "apply", "BlockUntilFirstOperator", "task", "call", "subscriber", "source", "unscheduleTask", "bind", "run", "Zone", "current", "scheduleMacroTask", "pipe", "next", "complete", "error", "subscribe", "add", "setTimeout", "invoke", "ɵAngularFireSchedulers", "ngZone", "outsideAngular", "runOutsideAngular", "insideAngular", "ɵAngularFireScheduler", "ɵfac", "ɵAngularFireSchedulers_Factory", "t", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "getSchedulers", "schedulers", "fn", "observeOutsideAngular", "obs$", "observeInsideAngular", "keepUnstableUntilFirst", "scheduler", "ɵkeepUnstableUntilFirstFactory", "lift", "zoneWrapFn", "macrotask", "_this", "_arguments", "arguments", "ɵzoneWrap", "blockUntilFirst", "i", "Promise", "resolve", "reject", "reason"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/fesm2015/angular-fire.js"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, Injectable } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { isSupported as isSupported$2 } from 'firebase/remote-config';\nimport { isSupported as isSupported$1 } from 'firebase/messaging';\nimport { isSupported } from 'firebase/analytics';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { tap, observeOn, subscribeOn } from 'rxjs/operators';\n\nconst VERSION = new Version('7.5.0');\nconst isAnalyticsSupportedValueSymbol = '__angularfire_symbol__analyticsIsSupportedValue';\nconst isAnalyticsSupportedPromiseSymbol = '__angularfire_symbol__analyticsIsSupported';\nconst isRemoteConfigSupportedValueSymbol = '__angularfire_symbol__remoteConfigIsSupportedValue';\nconst isRemoteConfigSupportedPromiseSymbol = '__angularfire_symbol__remoteConfigIsSupported';\nconst isMessagingSupportedValueSymbol = '__angularfire_symbol__messagingIsSupportedValue';\nconst isMessagingSupportedPromiseSymbol = '__angularfire_symbol__messagingIsSupported';\nglobalThis[isAnalyticsSupportedPromiseSymbol] || (globalThis[isAnalyticsSupportedPromiseSymbol] = isSupported().then(it => globalThis[isAnalyticsSupportedValueSymbol] = it).catch(() => globalThis[isAnalyticsSupportedValueSymbol] = false));\nglobalThis[isMessagingSupportedPromiseSymbol] || (globalThis[isMessagingSupportedPromiseSymbol] = isSupported$1().then(it => globalThis[isMessagingSupportedValueSymbol] = it).catch(() => globalThis[isMessagingSupportedValueSymbol] = false));\nglobalThis[isRemoteConfigSupportedPromiseSymbol] || (globalThis[isRemoteConfigSupportedPromiseSymbol] = isSupported$2().then(it => globalThis[isRemoteConfigSupportedValueSymbol] = it).catch(() => globalThis[isRemoteConfigSupportedValueSymbol] = false));\nconst isSupportedError = (module) => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\nconst ɵisMessagingSupportedFactory = {\n    async: () => globalThis[isMessagingSupportedPromiseSymbol],\n    sync: () => {\n        const ret = globalThis[isMessagingSupportedValueSymbol];\n        if (ret === undefined) {\n            throw new Error(isSupportedError('Messaging'));\n        }\n        return ret;\n    }\n};\nconst ɵisRemoteConfigSupportedFactory = {\n    async: () => globalThis[isRemoteConfigSupportedPromiseSymbol],\n    sync: () => {\n        const ret = globalThis[isRemoteConfigSupportedValueSymbol];\n        if (ret === undefined) {\n            throw new Error(isSupportedError('RemoteConfig'));\n        }\n        return ret;\n    }\n};\nconst ɵisAnalyticsSupportedFactory = {\n    async: () => globalThis[isAnalyticsSupportedPromiseSymbol],\n    sync: () => {\n        const ret = globalThis[isAnalyticsSupportedValueSymbol];\n        if (ret === undefined) {\n            throw new Error(isSupportedError('Analytics'));\n        }\n        return ret;\n    }\n};\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n    if (provided) {\n        // Was provide* only called once? If so grab that\n        if (provided.length === 1) {\n            return provided[0];\n        }\n        const providedUsingDefaultApp = provided.filter((it) => it.app === defaultApp);\n        // Was provide* only called once, using the default app? If so use that\n        if (providedUsingDefaultApp.length === 1) {\n            return providedUsingDefaultApp[0];\n        }\n    }\n    // Grab the default instance from the defaultApp\n    const defaultAppWithContainer = defaultApp;\n    const provider = defaultAppWithContainer.container.getProvider(identifier);\n    return provider.getImmediate({ optional: true });\n}\nconst ɵgetAllInstancesOf = (identifier, app) => {\n    const apps = app ? [app] : getApps();\n    const instances = [];\n    apps.forEach((app) => {\n        const provider = app.container.getProvider(identifier);\n        provider.instances.forEach((instance) => {\n            if (!instances.includes(instance)) {\n                instances.push(instance);\n            }\n        });\n    });\n    return instances;\n};\n\nfunction noop() {\n}\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\n// tslint:disable-next-line:class-name\nclass ɵZoneScheduler {\n    constructor(zone, delegate = queueScheduler) {\n        this.zone = zone;\n        this.delegate = delegate;\n    }\n    now() {\n        return this.delegate.now();\n    }\n    schedule(work, delay, state) {\n        const targetZone = this.zone;\n        // Wrap the specified work function to make sure that if nested scheduling takes place the\n        // work is executed in the correct zone\n        const workInZone = function (state) {\n            targetZone.runGuarded(() => {\n                work.apply(this, [state]);\n            });\n        };\n        // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n        // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n        // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n        return this.delegate.schedule(workInZone, delay, state);\n    }\n}\nclass BlockUntilFirstOperator {\n    constructor(zone) {\n        this.zone = zone;\n        this.task = null;\n    }\n    call(subscriber, source) {\n        const unscheduleTask = this.unscheduleTask.bind(this);\n        this.task = this.zone.run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n        return source.pipe(tap({ next: unscheduleTask, complete: unscheduleTask, error: unscheduleTask })).subscribe(subscriber).add(unscheduleTask);\n    }\n    unscheduleTask() {\n        // maybe this is a race condition, invoke in a timeout\n        // hold for 10ms while I try to figure out what is going on\n        setTimeout(() => {\n            if (this.task != null && this.task.state === 'scheduled') {\n                this.task.invoke();\n                this.task = null;\n            }\n        }, 10);\n    }\n}\n// tslint:disable-next-line:class-name\nclass ɵAngularFireSchedulers {\n    constructor(ngZone) {\n        this.ngZone = ngZone;\n        this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(Zone.current));\n        this.insideAngular = ngZone.run(() => new ɵZoneScheduler(Zone.current, asyncScheduler));\n        globalThis.ɵAngularFireScheduler || (globalThis.ɵAngularFireScheduler = this);\n    }\n}\nɵAngularFireSchedulers.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: ɵAngularFireSchedulers, deps: [{ token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nɵAngularFireSchedulers.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: ɵAngularFireSchedulers, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: ɵAngularFireSchedulers, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }]; } });\nfunction getSchedulers() {\n    const schedulers = globalThis.ɵAngularFireScheduler;\n    if (!schedulers) {\n        throw new Error(`Either AngularFireModule has not been provided in your AppModule (this can be done manually or implictly using\nprovideFirebaseApp) or you're calling an AngularFire method outside of an NgModule (which is not supported).`);\n    }\n    return schedulers;\n}\nfunction runOutsideAngular(fn) {\n    return getSchedulers().ngZone.runOutsideAngular(() => fn());\n}\nfunction run(fn) {\n    return getSchedulers().ngZone.run(() => fn());\n}\nfunction observeOutsideAngular(obs$) {\n    return obs$.pipe(observeOn(getSchedulers().outsideAngular));\n}\nfunction observeInsideAngular(obs$) {\n    return obs$.pipe(observeOn(getSchedulers().insideAngular));\n}\nfunction keepUnstableUntilFirst(obs$) {\n    const scheduler = getSchedulers();\n    return ɵkeepUnstableUntilFirstFactory(getSchedulers())(obs$);\n}\n/**\n * Operator to block the zone until the first value has been emitted or the observable\n * has completed/errored. This is used to make sure that universal waits until the first\n * value from firebase but doesn't block the zone forever since the firebase subscription\n * is still alive.\n */\nfunction ɵkeepUnstableUntilFirstFactory(schedulers) {\n    return function keepUnstableUntilFirst(obs$) {\n        obs$ = obs$.lift(new BlockUntilFirstOperator(schedulers.ngZone));\n        return obs$.pipe(\n        // Run the subscribe body outside of Angular (e.g. calling Firebase SDK to add a listener to a change event)\n        subscribeOn(schedulers.outsideAngular), \n        // Run operators inside the angular zone (e.g. side effects via tap())\n        observeOn(schedulers.insideAngular)\n        // INVESTIGATE https://github.com/angular/angularfire/pull/2315\n        // share()\n        );\n    };\n}\nconst zoneWrapFn = (it, macrotask) => {\n    const _this = this;\n    // function() is needed for the arguments object\n    // tslint:disable-next-line:only-arrow-functions\n    return function () {\n        const _arguments = arguments;\n        if (macrotask) {\n            setTimeout(() => {\n                if (macrotask.state === 'scheduled') {\n                    macrotask.invoke();\n                }\n            }, 10);\n        }\n        return run(() => it.apply(_this, _arguments));\n    };\n};\nconst ɵzoneWrap = (it, blockUntilFirst) => {\n    // function() is needed for the arguments object\n    // tslint:disable-next-line:only-arrow-functions\n    return function () {\n        let macrotask;\n        const _arguments = arguments;\n        // if this is a callback function, e.g, onSnapshot, we should create a microtask and invoke it\n        // only once one of the callback functions is tripped.\n        for (let i = 0; i < arguments.length; i++) {\n            if (typeof _arguments[i] === 'function') {\n                if (blockUntilFirst) {\n                    macrotask || (macrotask = run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop)));\n                }\n                // TODO create a microtask to track callback functions\n                _arguments[i] = zoneWrapFn(_arguments[i], macrotask);\n            }\n        }\n        const ret = runOutsideAngular(() => it.apply(this, _arguments));\n        if (!blockUntilFirst) {\n            if (ret instanceof Observable) {\n                const schedulers = getSchedulers();\n                return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n            }\n            else {\n                return run(() => ret);\n            }\n        }\n        if (ret instanceof Observable) {\n            return ret.pipe(keepUnstableUntilFirst);\n        }\n        else if (ret instanceof Promise) {\n            return run(() => new Promise((resolve, reject) => ret.then(it => run(() => resolve(it)), reason => run(() => reject(reason)))));\n        }\n        else if (typeof ret === 'function' && macrotask) {\n            // Handle unsubscribe\n            // function() is needed for the arguments object\n            // tslint:disable-next-line:only-arrow-functions\n            return function () {\n                setTimeout(() => {\n                    if (macrotask && macrotask.state === 'scheduled') {\n                        macrotask.invoke();\n                    }\n                }, 10);\n                return ret.apply(this, arguments);\n            };\n        }\n        else {\n            // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n            return run(() => ret);\n        }\n    };\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VERSION, keepUnstableUntilFirst, observeInsideAngular, observeOutsideAngular, ɵAngularFireSchedulers, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisAnalyticsSupportedFactory, ɵisMessagingSupportedFactory, ɵisRemoteConfigSupportedFactory, ɵkeepUnstableUntilFirstFactory, ɵzoneWrap };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,WAAW,IAAIC,aAAa,QAAQ,wBAAwB;AACrE,SAASD,WAAW,IAAIE,aAAa,QAAQ,oBAAoB;AACjE,SAASF,WAAW,QAAQ,oBAAoB;AAChD,SAASG,cAAc,EAAEC,cAAc,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,GAAG,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAE5D,MAAMC,OAAO,GAAG,IAAIZ,OAAO,CAAC,OAAO,CAAC;AACpC,MAAMa,+BAA+B,GAAG,iDAAiD;AACzF,MAAMC,iCAAiC,GAAG,4CAA4C;AACtF,MAAMC,kCAAkC,GAAG,oDAAoD;AAC/F,MAAMC,oCAAoC,GAAG,+CAA+C;AAC5F,MAAMC,+BAA+B,GAAG,iDAAiD;AACzF,MAAMC,iCAAiC,GAAG,4CAA4C;AACtFC,UAAU,CAACL,iCAAiC,CAAC,KAAKK,UAAU,CAACL,iCAAiC,CAAC,GAAGX,WAAW,CAAC,CAAC,CAACiB,IAAI,CAACC,EAAE,IAAIF,UAAU,CAACN,+BAA+B,CAAC,GAAGQ,EAAE,CAAC,CAACC,KAAK,CAAC,MAAMH,UAAU,CAACN,+BAA+B,CAAC,GAAG,KAAK,CAAC,CAAC;AAC9OM,UAAU,CAACD,iCAAiC,CAAC,KAAKC,UAAU,CAACD,iCAAiC,CAAC,GAAGb,aAAa,CAAC,CAAC,CAACe,IAAI,CAACC,EAAE,IAAIF,UAAU,CAACF,+BAA+B,CAAC,GAAGI,EAAE,CAAC,CAACC,KAAK,CAAC,MAAMH,UAAU,CAACF,+BAA+B,CAAC,GAAG,KAAK,CAAC,CAAC;AAChPE,UAAU,CAACH,oCAAoC,CAAC,KAAKG,UAAU,CAACH,oCAAoC,CAAC,GAAGZ,aAAa,CAAC,CAAC,CAACgB,IAAI,CAACC,EAAE,IAAIF,UAAU,CAACJ,kCAAkC,CAAC,GAAGM,EAAE,CAAC,CAACC,KAAK,CAAC,MAAMH,UAAU,CAACJ,kCAAkC,CAAC,GAAG,KAAK,CAAC,CAAC;AAC5P,MAAMQ,gBAAgB,GAAIC,MAAM,IAAM;AACtC,iCAAiCA,MAAO,0CAAyCA,MAAO;AACxF,gGAAgGA,MAAO,MAAK;AAC5G,MAAMC,4BAA4B,GAAG;EACjCC,KAAK,EAAEA,CAAA,KAAMP,UAAU,CAACD,iCAAiC,CAAC;EAC1DS,IAAI,EAAEA,CAAA,KAAM;IACR,MAAMC,GAAG,GAAGT,UAAU,CAACF,+BAA+B,CAAC;IACvD,IAAIW,GAAG,KAAKC,SAAS,EAAE;MACnB,MAAM,IAAIC,KAAK,CAACP,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAClD;IACA,OAAOK,GAAG;EACd;AACJ,CAAC;AACD,MAAMG,+BAA+B,GAAG;EACpCL,KAAK,EAAEA,CAAA,KAAMP,UAAU,CAACH,oCAAoC,CAAC;EAC7DW,IAAI,EAAEA,CAAA,KAAM;IACR,MAAMC,GAAG,GAAGT,UAAU,CAACJ,kCAAkC,CAAC;IAC1D,IAAIa,GAAG,KAAKC,SAAS,EAAE;MACnB,MAAM,IAAIC,KAAK,CAACP,gBAAgB,CAAC,cAAc,CAAC,CAAC;IACrD;IACA,OAAOK,GAAG;EACd;AACJ,CAAC;AACD,MAAMI,4BAA4B,GAAG;EACjCN,KAAK,EAAEA,CAAA,KAAMP,UAAU,CAACL,iCAAiC,CAAC;EAC1Da,IAAI,EAAEA,CAAA,KAAM;IACR,MAAMC,GAAG,GAAGT,UAAU,CAACN,+BAA+B,CAAC;IACvD,IAAIe,GAAG,KAAKC,SAAS,EAAE;MACnB,MAAM,IAAIC,KAAK,CAACP,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAClD;IACA,OAAOK,GAAG;EACd;AACJ,CAAC;AACD,SAASK,qBAAqBA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7D,IAAID,QAAQ,EAAE;IACV;IACA,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOF,QAAQ,CAAC,CAAC,CAAC;IACtB;IACA,MAAMG,uBAAuB,GAAGH,QAAQ,CAACI,MAAM,CAAElB,EAAE,IAAKA,EAAE,CAACmB,GAAG,KAAKJ,UAAU,CAAC;IAC9E;IACA,IAAIE,uBAAuB,CAACD,MAAM,KAAK,CAAC,EAAE;MACtC,OAAOC,uBAAuB,CAAC,CAAC,CAAC;IACrC;EACJ;EACA;EACA,MAAMG,uBAAuB,GAAGL,UAAU;EAC1C,MAAMM,QAAQ,GAAGD,uBAAuB,CAACE,SAAS,CAACC,WAAW,CAACV,UAAU,CAAC;EAC1E,OAAOQ,QAAQ,CAACG,YAAY,CAAC;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;AACpD;AACA,MAAMC,kBAAkB,GAAGA,CAACb,UAAU,EAAEM,GAAG,KAAK;EAC5C,MAAMQ,IAAI,GAAGR,GAAG,GAAG,CAACA,GAAG,CAAC,GAAGtC,OAAO,CAAC,CAAC;EACpC,MAAM+C,SAAS,GAAG,EAAE;EACpBD,IAAI,CAACE,OAAO,CAAEV,GAAG,IAAK;IAClB,MAAME,QAAQ,GAAGF,GAAG,CAACG,SAAS,CAACC,WAAW,CAACV,UAAU,CAAC;IACtDQ,QAAQ,CAACO,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAK;MACrC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAACD,QAAQ,CAAC,EAAE;QAC/BF,SAAS,CAACI,IAAI,CAACF,QAAQ,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOF,SAAS;AACpB,CAAC;AAED,SAASK,IAAIA,CAAA,EAAG,CAChB;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,GAAGpD,cAAc,EAAE;IACzC,IAAI,CAACmD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACD,QAAQ,CAACC,GAAG,CAAC,CAAC;EAC9B;EACAC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACzB,MAAMC,UAAU,GAAG,IAAI,CAACP,IAAI;IAC5B;IACA;IACA,MAAMQ,UAAU,GAAG,SAAAA,CAAUF,KAAK,EAAE;MAChCC,UAAU,CAACE,UAAU,CAAC,MAAM;QACxBL,IAAI,CAACM,KAAK,CAAC,IAAI,EAAE,CAACJ,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC;IACD;IACA;IACA;IACA,OAAO,IAAI,CAACL,QAAQ,CAACE,QAAQ,CAACK,UAAU,EAAEH,KAAK,EAAEC,KAAK,CAAC;EAC3D;AACJ;AACA,MAAMK,uBAAuB,CAAC;EAC1BZ,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACY,IAAI,GAAG,IAAI;EACpB;EACAC,IAAIA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACrB,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACrD,IAAI,CAACL,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAC,MAAMC,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,mBAAmB,EAAExB,IAAI,EAAE,CAAC,CAAC,EAAEA,IAAI,EAAEA,IAAI,CAAC,CAAC;IAC1G,OAAOkB,MAAM,CAACO,IAAI,CAACtE,GAAG,CAAC;MAAEuE,IAAI,EAAEP,cAAc;MAAEQ,QAAQ,EAAER,cAAc;MAAES,KAAK,EAAET;IAAe,CAAC,CAAC,CAAC,CAACU,SAAS,CAACZ,UAAU,CAAC,CAACa,GAAG,CAACX,cAAc,CAAC;EAChJ;EACAA,cAAcA,CAAA,EAAG;IACb;IACA;IACAY,UAAU,CAAC,MAAM;MACb,IAAI,IAAI,CAAChB,IAAI,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,CAACN,KAAK,KAAK,WAAW,EAAE;QACtD,IAAI,CAACM,IAAI,CAACiB,MAAM,CAAC,CAAC;QAClB,IAAI,CAACjB,IAAI,GAAG,IAAI;MACpB;IACJ,CAAC,EAAE,EAAE,CAAC;EACV;AACJ;AACA;AACA,MAAMkB,sBAAsB,CAAC;EACzB/B,WAAWA,CAACgC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGD,MAAM,CAACE,iBAAiB,CAAC,MAAM,IAAInC,cAAc,CAACqB,IAAI,CAACC,OAAO,CAAC,CAAC;IACtF,IAAI,CAACc,aAAa,GAAGH,MAAM,CAACb,GAAG,CAAC,MAAM,IAAIpB,cAAc,CAACqB,IAAI,CAACC,OAAO,EAAEtE,cAAc,CAAC,CAAC;IACvFY,UAAU,CAACyE,qBAAqB,KAAKzE,UAAU,CAACyE,qBAAqB,GAAG,IAAI,CAAC;EACjF;AACJ;AACAL,sBAAsB,CAACM,IAAI,YAAAC,+BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFR,sBAAsB,EAAhCxF,EAAE,CAAAiG,QAAA,CAAgDjG,EAAE,CAACkG,MAAM;AAAA,CAA6C;AACjNV,sBAAsB,CAACW,KAAK,kBAD6EnG,EAAE,CAAAoG,kBAAA;EAAAC,KAAA,EACYb,sBAAsB;EAAAc,OAAA,EAAtBd,sBAAsB,CAAAM,IAAA;EAAAS,UAAA,EAAc;AAAM,EAAG;AACpK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFyGxG,EAAE,CAAAyG,iBAAA,CAEhBjB,sBAAsB,EAAc,CAAC;IACpHkB,IAAI,EAAExG,UAAU;IAChByG,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE1G,EAAE,CAACkG;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AACzE,SAASU,aAAaA,CAAA,EAAG;EACrB,MAAMC,UAAU,GAAGzF,UAAU,CAACyE,qBAAqB;EACnD,IAAI,CAACgB,UAAU,EAAE;IACb,MAAM,IAAI9E,KAAK,CAAE;AACzB,6GAA6G,CAAC;EAC1G;EACA,OAAO8E,UAAU;AACrB;AACA,SAASlB,iBAAiBA,CAACmB,EAAE,EAAE;EAC3B,OAAOF,aAAa,CAAC,CAAC,CAACnB,MAAM,CAACE,iBAAiB,CAAC,MAAMmB,EAAE,CAAC,CAAC,CAAC;AAC/D;AACA,SAASlC,GAAGA,CAACkC,EAAE,EAAE;EACb,OAAOF,aAAa,CAAC,CAAC,CAACnB,MAAM,CAACb,GAAG,CAAC,MAAMkC,EAAE,CAAC,CAAC,CAAC;AACjD;AACA,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAAChC,IAAI,CAACrE,SAAS,CAACiG,aAAa,CAAC,CAAC,CAAClB,cAAc,CAAC,CAAC;AAC/D;AACA,SAASuB,oBAAoBA,CAACD,IAAI,EAAE;EAChC,OAAOA,IAAI,CAAChC,IAAI,CAACrE,SAAS,CAACiG,aAAa,CAAC,CAAC,CAAChB,aAAa,CAAC,CAAC;AAC9D;AACA,SAASsB,sBAAsBA,CAACF,IAAI,EAAE;EAClC,MAAMG,SAAS,GAAGP,aAAa,CAAC,CAAC;EACjC,OAAOQ,8BAA8B,CAACR,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,8BAA8BA,CAACP,UAAU,EAAE;EAChD,OAAO,SAASK,sBAAsBA,CAACF,IAAI,EAAE;IACzCA,IAAI,GAAGA,IAAI,CAACK,IAAI,CAAC,IAAIhD,uBAAuB,CAACwC,UAAU,CAACpB,MAAM,CAAC,CAAC;IAChE,OAAOuB,IAAI,CAAChC,IAAI;IAChB;IACApE,WAAW,CAACiG,UAAU,CAACnB,cAAc,CAAC;IACtC;IACA/E,SAAS,CAACkG,UAAU,CAACjB,aAAa;IAClC;IACA;IACA,CAAC;EACL,CAAC;AACL;;AACA,MAAM0B,UAAU,GAAGA,CAAChG,EAAE,EAAEiG,SAAS,KAAK;EAClC,MAAMC,KAAK,GAAG,IAAI;EAClB;EACA;EACA,OAAO,YAAY;IACf,MAAMC,UAAU,GAAGC,SAAS;IAC5B,IAAIH,SAAS,EAAE;MACXjC,UAAU,CAAC,MAAM;QACb,IAAIiC,SAAS,CAACvD,KAAK,KAAK,WAAW,EAAE;UACjCuD,SAAS,CAAChC,MAAM,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,EAAE,CAAC;IACV;IACA,OAAOX,GAAG,CAAC,MAAMtD,EAAE,CAAC8C,KAAK,CAACoD,KAAK,EAAEC,UAAU,CAAC,CAAC;EACjD,CAAC;AACL,CAAC;AACD,MAAME,SAAS,GAAGA,CAACrG,EAAE,EAAEsG,eAAe,KAAK;EACvC;EACA;EACA,OAAO,YAAY;IACf,IAAIL,SAAS;IACb,MAAME,UAAU,GAAGC,SAAS;IAC5B;IACA;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACpF,MAAM,EAAEuF,CAAC,EAAE,EAAE;MACvC,IAAI,OAAOJ,UAAU,CAACI,CAAC,CAAC,KAAK,UAAU,EAAE;QACrC,IAAID,eAAe,EAAE;UACjBL,SAAS,KAAKA,SAAS,GAAG3C,GAAG,CAAC,MAAMC,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,mBAAmB,EAAExB,IAAI,EAAE,CAAC,CAAC,EAAEA,IAAI,EAAEA,IAAI,CAAC,CAAC,CAAC;QACnH;QACA;QACAkE,UAAU,CAACI,CAAC,CAAC,GAAGP,UAAU,CAACG,UAAU,CAACI,CAAC,CAAC,EAAEN,SAAS,CAAC;MACxD;IACJ;IACA,MAAM1F,GAAG,GAAG8D,iBAAiB,CAAC,MAAMrE,EAAE,CAAC8C,KAAK,CAAC,IAAI,EAAEqD,UAAU,CAAC,CAAC;IAC/D,IAAI,CAACG,eAAe,EAAE;MAClB,IAAI/F,GAAG,YAAYpB,UAAU,EAAE;QAC3B,MAAMoG,UAAU,GAAGD,aAAa,CAAC,CAAC;QAClC,OAAO/E,GAAG,CAACmD,IAAI,CAACpE,WAAW,CAACiG,UAAU,CAACnB,cAAc,CAAC,EAAE/E,SAAS,CAACkG,UAAU,CAACjB,aAAa,CAAC,CAAC;MAChG,CAAC,MACI;QACD,OAAOhB,GAAG,CAAC,MAAM/C,GAAG,CAAC;MACzB;IACJ;IACA,IAAIA,GAAG,YAAYpB,UAAU,EAAE;MAC3B,OAAOoB,GAAG,CAACmD,IAAI,CAACkC,sBAAsB,CAAC;IAC3C,CAAC,MACI,IAAIrF,GAAG,YAAYiG,OAAO,EAAE;MAC7B,OAAOlD,GAAG,CAAC,MAAM,IAAIkD,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAKnG,GAAG,CAACR,IAAI,CAACC,EAAE,IAAIsD,GAAG,CAAC,MAAMmD,OAAO,CAACzG,EAAE,CAAC,CAAC,EAAE2G,MAAM,IAAIrD,GAAG,CAAC,MAAMoD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnI,CAAC,MACI,IAAI,OAAOpG,GAAG,KAAK,UAAU,IAAI0F,SAAS,EAAE;MAC7C;MACA;MACA;MACA,OAAO,YAAY;QACfjC,UAAU,CAAC,MAAM;UACb,IAAIiC,SAAS,IAAIA,SAAS,CAACvD,KAAK,KAAK,WAAW,EAAE;YAC9CuD,SAAS,CAAChC,MAAM,CAAC,CAAC;UACtB;QACJ,CAAC,EAAE,EAAE,CAAC;QACN,OAAO1D,GAAG,CAACuC,KAAK,CAAC,IAAI,EAAEsD,SAAS,CAAC;MACrC,CAAC;IACL,CAAC,MACI;MACD;MACA,OAAO9C,GAAG,CAAC,MAAM/C,GAAG,CAAC;IACzB;EACJ,CAAC;AACL,CAAC;;AAED;AACA;AACA;;AAEA,SAAShB,OAAO,EAAEqG,sBAAsB,EAAED,oBAAoB,EAAEF,qBAAqB,EAAEvB,sBAAsB,EAAEhC,cAAc,EAAER,kBAAkB,EAAEd,qBAAqB,EAAED,4BAA4B,EAAEP,4BAA4B,EAAEM,+BAA+B,EAAEoF,8BAA8B,EAAEO,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SeasonRefereeService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getAllSeasonReferee(seasonId) {\n    return this._http.get(`${environment.apiUrl}/seasons/${seasonId}/referees`);\n  }\n  createNewReferee(data) {\n    return this._http.post(`${environment.apiUrl}/season-referees`, data);\n  }\n  deleteRefereeFromSeason(seasonId, refereeId) {\n    return this._http.delete(`${environment.apiUrl}/seasons/${seasonId}/referees/${refereeId}`);\n  }\n  static #_ = this.ɵfac = function SeasonRefereeService_Factory(t) {\n    return new (t || SeasonRefereeService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SeasonRefereeService,\n    factory: SeasonRefereeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;;;AAMtD,OAAM,MAAOC,oBAAoB;EAC/BC,YAAoBC,KAAiB;IAAjB,KAAAA,KAAK,GAALA,KAAK;EACzB;EAEAC,mBAAmBA,CAACC,QAAyB;IAC3C,OAAO,IAAI,CAACF,KAAK,CACdG,GAAG,CACF,GAAGN,WAAW,CAACO,MAAM,YAAYF,QAAQ,WAAW,CACrD;EACL;EAEAG,gBAAgBA,CAACC,IAAI;IACnB,OAAO,IAAI,CAACN,KAAK,CAACO,IAAI,CAAC,GAAGV,WAAW,CAACO,MAAM,kBAAkB,EAAEE,IAAI,CAAC;EACvE;EACAE,uBAAuBA,CAACN,QAAyB,EAAEO,SAA0B;IAC3E,OAAO,IAAI,CAACT,KAAK,CAACU,MAAM,CAAC,GAAGb,WAAW,CAACO,MAAM,YAAYF,QAAQ,aAAaO,SAAS,EAAE,CAAC;EAC7F;EAAC,QAAAE,CAAA;qBAhBUb,oBAAoB,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA;WAApBlB,oBAAoB;IAAAmB,OAAA,EAApBnB,oBAAoB,CAAAoB,IAAA;IAAAC,UAAA,EAFnB;EAAM", "names": ["environment", "SeasonRefereeService", "constructor", "_http", "getAllSeasonReferee", "seasonId", "get", "apiUrl", "createNewReferee", "data", "post", "deleteRefereeFromSeason", "refereeId", "delete", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\season-referee.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from 'environments/environment';\r\nimport { map } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SeasonRefereeService {\r\n  constructor(private _http: HttpClient) {\r\n  }\r\n\r\n  getAllSeasonReferee(seasonId: string | number) {\r\n    return this._http\r\n      .get<any>(\r\n        `${environment.apiUrl}/seasons/${seasonId}/referees`\r\n      );\r\n  }\r\n\r\n  createNewReferee(data) {\r\n    return this._http.post(`${environment.apiUrl}/season-referees`, data);\r\n  }\r\n  deleteRefereeFromSeason(seasonId: string | number, refereeId: string | number) {\r\n    return this._http.delete(`${environment.apiUrl}/seasons/${seasonId}/referees/${refereeId}`);\r\n  }\r\n\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
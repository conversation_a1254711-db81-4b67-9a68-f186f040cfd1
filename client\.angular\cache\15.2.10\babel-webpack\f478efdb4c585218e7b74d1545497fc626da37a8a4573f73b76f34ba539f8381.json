{"ast": null, "code": "exports.L = {\n  bit: 1\n};\nexports.M = {\n  bit: 0\n};\nexports.Q = {\n  bit: 3\n};\nexports.H = {\n  bit: 2\n};\nfunction fromString(string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string');\n  }\n  const lcStr = string.toLowerCase();\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L;\n    case 'm':\n    case 'medium':\n      return exports.M;\n    case 'q':\n    case 'quartile':\n      return exports.Q;\n    case 'h':\n    case 'high':\n      return exports.H;\n    default:\n      throw new Error('Unknown EC Level: ' + string);\n  }\n}\nexports.isValid = function isValid(level) {\n  return level && typeof level.bit !== 'undefined' && level.bit >= 0 && level.bit < 4;\n};\nexports.from = function from(value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value;\n  }\n  try {\n    return fromString(value);\n  } catch (e) {\n    return defaultValue;\n  }\n};", "map": {"version": 3, "names": ["exports", "L", "bit", "M", "Q", "H", "fromString", "string", "Error", "lcStr", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "level", "from", "value", "defaultValue", "e"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/core/error-correction-level.js"], "sourcesContent": ["exports.L = { bit: 1 }\nexports.M = { bit: 0 }\nexports.Q = { bit: 3 }\nexports.H = { bit: 2 }\n\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L\n\n    case 'm':\n    case 'medium':\n      return exports.M\n\n    case 'q':\n    case 'quartile':\n      return exports.Q\n\n    case 'h':\n    case 'high':\n      return exports.H\n\n    default:\n      throw new Error('Unknown EC Level: ' + string)\n  }\n}\n\nexports.isValid = function isValid (level) {\n  return level && typeof level.bit !== 'undefined' &&\n    level.bit >= 0 && level.bit < 4\n}\n\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n"], "mappings": "AAAAA,OAAO,CAACC,CAAC,GAAG;EAAEC,GAAG,EAAE;AAAE,CAAC;AACtBF,OAAO,CAACG,CAAC,GAAG;EAAED,GAAG,EAAE;AAAE,CAAC;AACtBF,OAAO,CAACI,CAAC,GAAG;EAAEF,GAAG,EAAE;AAAE,CAAC;AACtBF,OAAO,CAACK,CAAC,GAAG;EAAEH,GAAG,EAAE;AAAE,CAAC;AAEtB,SAASI,UAAUA,CAAEC,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;EAC1C;EAEA,MAAMC,KAAK,GAAGF,MAAM,CAACG,WAAW,CAAC,CAAC;EAElC,QAAQD,KAAK;IACX,KAAK,GAAG;IACR,KAAK,KAAK;MACR,OAAOT,OAAO,CAACC,CAAC;IAElB,KAAK,GAAG;IACR,KAAK,QAAQ;MACX,OAAOD,OAAO,CAACG,CAAC;IAElB,KAAK,GAAG;IACR,KAAK,UAAU;MACb,OAAOH,OAAO,CAACI,CAAC;IAElB,KAAK,GAAG;IACR,KAAK,MAAM;MACT,OAAOJ,OAAO,CAACK,CAAC;IAElB;MACE,MAAM,IAAIG,KAAK,CAAC,oBAAoB,GAAGD,MAAM,CAAC;EAClD;AACF;AAEAP,OAAO,CAACW,OAAO,GAAG,SAASA,OAAOA,CAAEC,KAAK,EAAE;EACzC,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACV,GAAG,KAAK,WAAW,IAC9CU,KAAK,CAACV,GAAG,IAAI,CAAC,IAAIU,KAAK,CAACV,GAAG,GAAG,CAAC;AACnC,CAAC;AAEDF,OAAO,CAACa,IAAI,GAAG,SAASA,IAAIA,CAAEC,KAAK,EAAEC,YAAY,EAAE;EACjD,IAAIf,OAAO,CAACW,OAAO,CAACG,KAAK,CAAC,EAAE;IAC1B,OAAOA,KAAK;EACd;EAEA,IAAI;IACF,OAAOR,UAAU,CAACQ,KAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAOD,YAAY;EACrB;AACF,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
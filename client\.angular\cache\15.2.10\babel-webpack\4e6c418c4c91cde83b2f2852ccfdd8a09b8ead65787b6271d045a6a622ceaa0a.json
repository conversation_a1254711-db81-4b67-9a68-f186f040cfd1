{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/tournament.service\";\nimport * as i2 from \"@angular/router\";\nexport class MatchDetailsGuard {\n  constructor(_tournamentService, router) {\n    this._tournamentService = _tournamentService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    this.match_id = route.paramMap.get('match_id');\n    console.log('match_id', this.match_id);\n    this._tournamentService.checkMatchExist(this.match_id).toPromise().then(res => {\n      console.log('res: ', res);\n      // if !res or {}\n      if (res == false) {\n        this.router.navigate(['/home']);\n        return false;\n      }\n      return true;\n    }, err => {\n      this.router.navigate(['/home']);\n      return false;\n    });\n    return true;\n  }\n  static #_ = this.ɵfac = function MatchDetailsGuard_Factory(t) {\n    return new (t || MatchDetailsGuard)(i0.ɵɵinject(i1.TournamentService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: MatchDetailsGuard,\n    factory: MatchDetailsGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";;;AAeA,OAAM,MAAOA,iBAAiB;EAE5BC,YACSC,kBAAqC,EACrCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;EACZ;EACHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAM1B,IAAI,CAACC,QAAQ,GAAGF,KAAK,CAACG,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC;IAC9CC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACJ,QAAQ,CAAC;IACtC,IAAI,CAACL,kBAAkB,CACpBU,eAAe,CAAC,IAAI,CAACL,QAAQ,CAAC,CAC9BM,SAAS,EAAE,CACXC,IAAI,CACFC,GAAG,IAAI;MACNL,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEI,GAAG,CAAC;MACzB;MACA,IAAIA,GAAG,IAAI,KAAK,EAAE;QAChB,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,KAAK;;MAEd,OAAO,IAAI;IACb,CAAC,EACAC,GAAG,IAAI;MACN,IAAI,CAACd,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B,OAAO,KAAK;IACd,CAAC,CACF;IACH,OAAO,IAAI;EACb;EAAC,QAAAE,CAAA;qBAnCUlB,iBAAiB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA;WAAjBzB,iBAAiB;IAAA0B,OAAA,EAAjB1B,iBAAiB,CAAA2B,IAAA;IAAAC,UAAA,EAFhB;EAAM", "names": ["MatchDetailsGuard", "constructor", "_tournamentService", "router", "canActivate", "route", "state", "match_id", "paramMap", "get", "console", "log", "checkMatchExist", "to<PERSON>romise", "then", "res", "navigate", "err", "_", "i0", "ɵɵinject", "i1", "TournamentService", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\guards\\match-details.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  ActivatedRoute,\r\n  ActivatedRouteSnapshot,\r\n  CanActivate,\r\n  Router,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class MatchDetailsGuard implements CanActivate {\r\n  match_id: any;\r\n  constructor(\r\n    public _tournamentService: TournamentService,\r\n    public router: Router\r\n  ) {}\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ):\r\n    | Observable<boolean | UrlTree>\r\n    | Promise<boolean | UrlTree>\r\n    | boolean\r\n    | UrlTree {\r\n    this.match_id = route.paramMap.get('match_id');\r\n    console.log('match_id', this.match_id);\r\n    this._tournamentService\r\n      .checkMatchExist(this.match_id)\r\n      .toPromise()\r\n      .then(\r\n        (res) => {\r\n          console.log('res: ', res);\r\n          // if !res or {}\r\n          if (res == false) {\r\n            this.router.navigate(['/home']);\r\n            return false;\r\n          }\r\n          return true;\r\n        },\r\n        (err) => {\r\n          this.router.navigate(['/home']);\r\n          return false;\r\n        }\r\n      );\r\n    return true;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
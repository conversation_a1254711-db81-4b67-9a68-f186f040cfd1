{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TutorialTourService {\n  constructor() {}\n  getTutorialTourStatus(id) {\n    let status = localStorage.getItem('tutorial_tour_' + id);\n    console.log(status);\n    if (status === 'true') {\n      return status === 'true';\n    }\n    return false;\n  }\n  setTutorialTourStatus(id, status) {\n    return localStorage.setItem('tutorial_tour_' + id, status.toString());\n  }\n  static #_ = this.ɵfac = function TutorialTourService_Factory(t) {\n    return new (t || TutorialTourService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TutorialTourService,\n    factory: TutorialTourService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,mBAAmB;EAC9BC,YAAA,GAAe;EAERC,qBAAqBA,CAACC,EAAU;IACrC,IAAIC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,GAAGH,EAAE,CAAC;IACxDI,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC;IAEnB,IAAIA,MAAM,KAAK,MAAM,EAAE;MACrB,OAAOA,MAAM,KAAK,MAAM;;IAE1B,OAAO,KAAK;EACd;EAEOK,qBAAqBA,CAACN,EAAU,EAAEC,MAAe;IACtD,OAAOC,YAAY,CAACK,OAAO,CAAC,gBAAgB,GAAGP,EAAE,EAAEC,MAAM,CAACO,QAAQ,EAAE,CAAC;EACvE;EAAC,QAAAC,CAAA;qBAfUZ,mBAAmB;EAAA;EAAA,QAAAa,EAAA;WAAnBb,mBAAmB;IAAAc,OAAA,EAAnBd,mBAAmB,CAAAe,IAAA;IAAAC,UAAA,EAFlB;EAAM", "names": ["TutorialTourService", "constructor", "getTutorialTourStatus", "id", "status", "localStorage", "getItem", "console", "log", "setTutorialTourStatus", "setItem", "toString", "_", "_2", "factory", "ɵfac", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\services\\tutorial-tour.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TutorialTourService {\r\n  constructor() {}\r\n\r\n  public getTutorialTourStatus(id: string) {\r\n    let status = localStorage.getItem('tutorial_tour_' + id);\r\n    console.log(status);\r\n\r\n    if (status === 'true') {\r\n      return status === 'true';\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public setTutorialTourStatus(id: string, status: boolean) {\r\n    return localStorage.setItem('tutorial_tour_' + id, status.toString());\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
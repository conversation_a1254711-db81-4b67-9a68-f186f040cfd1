{"ast": null, "code": "import { from } from '../observable/from';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function onErrorResumeNext(...nextSources) {\n  if (nextSources.length === 1 && isArray(nextSources[0])) {\n    nextSources = nextSources[0];\n  }\n  return source => source.lift(new OnErrorResumeNextOperator(nextSources));\n}\nexport function onErrorResumeNextStatic(...nextSources) {\n  let source = null;\n  if (nextSources.length === 1 && isArray(nextSources[0])) {\n    nextSources = nextSources[0];\n  }\n  source = nextSources.shift();\n  return from(source, null).lift(new OnErrorResumeNextOperator(nextSources));\n}\nclass OnErrorResumeNextOperator {\n  constructor(nextSources) {\n    this.nextSources = nextSources;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new OnErrorResumeNextSubscriber(subscriber, this.nextSources));\n  }\n}\nclass OnErrorResumeNextSubscriber extends OuterSubscriber {\n  constructor(destination, nextSources) {\n    super(destination);\n    this.destination = destination;\n    this.nextSources = nextSources;\n  }\n  notifyError(error, innerSub) {\n    this.subscribeToNextSource();\n  }\n  notifyComplete(innerSub) {\n    this.subscribeToNextSource();\n  }\n  _error(err) {\n    this.subscribeToNextSource();\n    this.unsubscribe();\n  }\n  _complete() {\n    this.subscribeToNextSource();\n    this.unsubscribe();\n  }\n  subscribeToNextSource() {\n    const next = this.nextSources.shift();\n    if (!!next) {\n      const innerSubscriber = new InnerSubscriber(this, undefined, undefined);\n      const destination = this.destination;\n      destination.add(innerSubscriber);\n      const innerSubscription = subscribeToResult(this, next, undefined, undefined, innerSubscriber);\n      if (innerSubscription !== innerSubscriber) {\n        destination.add(innerSubscription);\n      }\n    } else {\n      this.destination.complete();\n    }\n  }\n}", "map": {"version": 3, "names": ["from", "isArray", "OuterSubscriber", "InnerSubscriber", "subscribeToResult", "onErrorResumeNext", "nextSources", "length", "source", "lift", "OnErrorResumeNextOperator", "onErrorResumeNextStatic", "shift", "constructor", "call", "subscriber", "subscribe", "OnErrorResumeNextSubscriber", "destination", "notifyError", "error", "innerSub", "subscribeToNextSource", "notifyComplete", "_error", "err", "unsubscribe", "_complete", "next", "innerSubscriber", "undefined", "add", "innerSubscription", "complete"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/onErrorResumeNext.js"], "sourcesContent": ["import { from } from '../observable/from';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function onErrorResumeNext(...nextSources) {\n    if (nextSources.length === 1 && isArray(nextSources[0])) {\n        nextSources = nextSources[0];\n    }\n    return (source) => source.lift(new OnErrorResumeNextOperator(nextSources));\n}\nexport function onErrorResumeNextStatic(...nextSources) {\n    let source = null;\n    if (nextSources.length === 1 && isArray(nextSources[0])) {\n        nextSources = nextSources[0];\n    }\n    source = nextSources.shift();\n    return from(source, null).lift(new OnErrorResumeNextOperator(nextSources));\n}\nclass OnErrorResumeNextOperator {\n    constructor(nextSources) {\n        this.nextSources = nextSources;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new OnErrorResumeNextSubscriber(subscriber, this.nextSources));\n    }\n}\nclass OnErrorResumeNextSubscriber extends OuterSubscriber {\n    constructor(destination, nextSources) {\n        super(destination);\n        this.destination = destination;\n        this.nextSources = nextSources;\n    }\n    notifyError(error, innerSub) {\n        this.subscribeToNextSource();\n    }\n    notifyComplete(innerSub) {\n        this.subscribeToNextSource();\n    }\n    _error(err) {\n        this.subscribeToNextSource();\n        this.unsubscribe();\n    }\n    _complete() {\n        this.subscribeToNextSource();\n        this.unsubscribe();\n    }\n    subscribeToNextSource() {\n        const next = this.nextSources.shift();\n        if (!!next) {\n            const innerSubscriber = new InnerSubscriber(this, undefined, undefined);\n            const destination = this.destination;\n            destination.add(innerSubscriber);\n            const innerSubscription = subscribeToResult(this, next, undefined, undefined, innerSubscriber);\n            if (innerSubscription !== innerSubscriber) {\n                destination.add(innerSubscription);\n            }\n        }\n        else {\n            this.destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,iBAAiBA,CAAC,GAAGC,WAAW,EAAE;EAC9C,IAAIA,WAAW,CAACC,MAAM,KAAK,CAAC,IAAIN,OAAO,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACrDA,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC;EAChC;EACA,OAAQE,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,yBAAyB,CAACJ,WAAW,CAAC,CAAC;AAC9E;AACA,OAAO,SAASK,uBAAuBA,CAAC,GAAGL,WAAW,EAAE;EACpD,IAAIE,MAAM,GAAG,IAAI;EACjB,IAAIF,WAAW,CAACC,MAAM,KAAK,CAAC,IAAIN,OAAO,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACrDA,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC;EAChC;EACAE,MAAM,GAAGF,WAAW,CAACM,KAAK,CAAC,CAAC;EAC5B,OAAOZ,IAAI,CAACQ,MAAM,EAAE,IAAI,CAAC,CAACC,IAAI,CAAC,IAAIC,yBAAyB,CAACJ,WAAW,CAAC,CAAC;AAC9E;AACA,MAAMI,yBAAyB,CAAC;EAC5BG,WAAWA,CAACP,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAQ,IAAIA,CAACC,UAAU,EAAEP,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACQ,SAAS,CAAC,IAAIC,2BAA2B,CAACF,UAAU,EAAE,IAAI,CAACT,WAAW,CAAC,CAAC;EAC1F;AACJ;AACA,MAAMW,2BAA2B,SAASf,eAAe,CAAC;EACtDW,WAAWA,CAACK,WAAW,EAAEZ,WAAW,EAAE;IAClC,KAAK,CAACY,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACZ,WAAW,GAAGA,WAAW;EAClC;EACAa,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACzB,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,cAAcA,CAACF,QAAQ,EAAE;IACrB,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAE,MAAMA,CAACC,GAAG,EAAE;IACR,IAAI,CAACH,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACI,WAAW,CAAC,CAAC;EACtB;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACL,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACI,WAAW,CAAC,CAAC;EACtB;EACAJ,qBAAqBA,CAAA,EAAG;IACpB,MAAMM,IAAI,GAAG,IAAI,CAACtB,WAAW,CAACM,KAAK,CAAC,CAAC;IACrC,IAAI,CAAC,CAACgB,IAAI,EAAE;MACR,MAAMC,eAAe,GAAG,IAAI1B,eAAe,CAAC,IAAI,EAAE2B,SAAS,EAAEA,SAAS,CAAC;MACvE,MAAMZ,WAAW,GAAG,IAAI,CAACA,WAAW;MACpCA,WAAW,CAACa,GAAG,CAACF,eAAe,CAAC;MAChC,MAAMG,iBAAiB,GAAG5B,iBAAiB,CAAC,IAAI,EAAEwB,IAAI,EAAEE,SAAS,EAAEA,SAAS,EAAED,eAAe,CAAC;MAC9F,IAAIG,iBAAiB,KAAKH,eAAe,EAAE;QACvCX,WAAW,CAACa,GAAG,CAACC,iBAAiB,CAAC;MACtC;IACJ,CAAC,MACI;MACD,IAAI,CAACd,WAAW,CAACe,QAAQ,CAAC,CAAC;IAC/B;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
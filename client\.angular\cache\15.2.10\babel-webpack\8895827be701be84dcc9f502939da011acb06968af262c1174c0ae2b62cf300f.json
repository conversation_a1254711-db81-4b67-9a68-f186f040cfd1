{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BadgeLiveComponent } from './badge-live';\nimport * as i0 from \"@angular/core\";\nexport class BadgeLiveModule {\n  static #_ = this.ɵfac = function BadgeLiveModule_Factory(t) {\n    return new (t || BadgeLiveModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BadgeLiveModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BadgeLiveModule, {\n    declarations: [BadgeLiveComponent],\n    imports: [CommonModule],\n    exports: [BadgeLiveComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,cAAc;;AAOjD,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA;UAAfF;EAAe;EAAA,QAAAG,EAAA;cAHhBL,YAAY;EAAA;;;2EAGXE,eAAe;IAAAI,YAAA,GAJXL,kBAAkB;IAAAM,OAAA,GACvBP,YAAY;IAAAQ,OAAA,GACZP,kBAAkB;EAAA;AAAA", "names": ["CommonModule", "BadgeLiveComponent", "BadgeLiveModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\widget\\badge-live\\badge-live.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { BadgeLiveComponent } from './badge-live';\r\n\r\n@NgModule({\r\n  declarations: [BadgeLiveComponent],\r\n  imports: [CommonModule],\r\n  exports: [BadgeLiveComponent],\r\n})\r\nexport class BadgeLiveModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
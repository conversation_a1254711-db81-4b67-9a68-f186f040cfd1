{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"app/services/loading.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"angularx-qrcode\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction ModalTwofaComponent_div_0_qrcode_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"qrcode\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"qrdata\", ctx_r1.qrCodeUrl)(\"width\", 256)(\"errorCorrectionLevel\", \"M\");\n  }\n}\nfunction ModalTwofaComponent_div_0_ngb_alert_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngb-alert\", 15)(1, \"div\", 16)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"dismissible\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 3, \"Secret key\"), \": \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.secret, \"\");\n  }\n}\nfunction ModalTwofaComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 1)(2, \"h4\", 2);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function ModalTwofaComponent_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.close());\n    });\n    i0.ɵɵelementStart(6, \"span\", 4);\n    i0.ɵɵtext(7, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"form\")(9, \"div\", 5)(10, \"p\", 6);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 7);\n    i0.ɵɵtemplate(14, ModalTwofaComponent_div_0_qrcode_14_Template, 1, 3, \"qrcode\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ModalTwofaComponent_div_0_ngb_alert_15_Template, 6, 5, \"ngb-alert\", 9);\n    i0.ɵɵelementStart(16, \"div\", 10)(17, \"input\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function ModalTwofaComponent_div_0_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.code = $event);\n    });\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 12)(20, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ModalTwofaComponent_div_0_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.enable2fa());\n    });\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 7, \"Two Factor Authentication\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 9, \"To be able to authorize transactions you need to scan this QR Code with your Google Authentication App or enter the secret key below\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.qrCodeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.secret);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(18, 11, \"Enter authentication code\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.code);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 13, \"Enable\"), \" \");\n  }\n}\nexport class ModalTwofaComponent {\n  constructor(_authService, _loadingService, _modalService, _trans) {\n    this._authService = _authService;\n    this._loadingService = _loadingService;\n    this._modalService = _modalService;\n    this._trans = _trans;\n    this.is2faEnabled = false;\n    _authService.currentUser.subscribe(user => {\n      this.current_user = user;\n      this.is2faEnabled = user.two_factor_auth;\n    });\n    _loadingService.show();\n    this._authService.generate2faSecret().subscribe(data => {\n      this.secret = data.secret;\n      this.qrCodeUrl = data.url;\n    });\n  }\n  ngOnInit() {}\n  enable2fa() {\n    let data = {\n      code: this.code,\n      auth_code: this.secret,\n      is_enabled: true\n    };\n    this._loadingService.show();\n    this._authService.enable2fa(data).subscribe(res => {\n      let message = res.message;\n      this._authService.getProfile().subscribe(user => {\n        Swal.fire({\n          title: this._trans.instant('Success'),\n          text: message,\n          icon: 'success'\n        });\n        this._modalService.dismiss();\n      }, err => {\n        Swal.fire({\n          title: this._trans.instant('Error'),\n          text: err.message,\n          icon: 'error'\n        });\n      });\n    }, err => {\n      Swal.fire({\n        title: this._trans.instant('Error'),\n        text: err.message,\n        icon: 'error'\n      });\n    });\n  }\n  close() {\n    this._modalService.dismiss();\n  }\n  static #_ = this.ɵfac = function ModalTwofaComponent_Factory(t) {\n    return new (t || ModalTwofaComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.LoadingService), i0.ɵɵdirectiveInject(i3.NgbActiveModal), i0.ɵɵdirectiveInject(i4.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalTwofaComponent,\n    selectors: [[\"app-modal-twofa\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [1, \"modal-header\", \"bg-white\"], [\"id\", \"label_follows\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [1, \"text-justify\"], [1, \"row\", \"d-flex\", \"justify-content-center\"], [3, \"qrdata\", \"width\", \"errorCorrectionLevel\", 4, \"ngIf\"], [\"type\", \"warning\", 3, \"dismissible\", 4, \"ngIf\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"code\", \"name\", \"code\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [3, \"qrdata\", \"width\", \"errorCorrectionLevel\"], [\"type\", \"warning\", 3, \"dismissible\"], [1, \"alert-body\"]],\n    template: function ModalTwofaComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ModalTwofaComponent_div_0_Template, 23, 15, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.qrCodeUrl);\n      }\n    },\n    dependencies: [i5.QRCodeComponent, i6.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.NgModel, i7.NgForm, i3.NgbAlert, i4.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAOA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;ICStBC,EAAA,CAAAC,SAAA,iBAAmG;;;;IAAzED,EAAA,CAAAE,UAAA,WAAAC,MAAA,CAAAC,SAAA,CAAoB;;;;;IAEhDJ,EAAA,CAAAK,cAAA,oBAA+D;IAC7BL,EAAA,CAAAM,MAAA,GAA8B;;IAAAN,EAAA,CAAAO,YAAA,EAAS;IAACP,EAAA,CAAAM,MAAA,GAAY;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADlEP,EAAA,CAAAE,UAAA,sBAAqB;IACbF,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAS,kBAAA,KAAAT,EAAA,CAAAU,WAAA,2BAA8B;IAAUV,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAS,kBAAA,MAAAE,MAAA,CAAAC,MAAA,KAAY;;;;;;IAnB5FZ,EAAA,CAAAK,cAAA,UAAuB;IAGjBL,EAAA,CAAAM,MAAA,GACF;;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACLP,EAAA,CAAAK,cAAA,gBAAyE;IAAlBL,EAAA,CAAAa,UAAA,mBAAAC,2DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IACtEpB,EAAA,CAAAK,cAAA,cAAyB;IAAAL,EAAA,CAAAM,MAAA,aAAO;IAAAN,EAAA,CAAAO,YAAA,EAAO;IAI3CP,EAAA,CAAAK,cAAA,WAAM;IAGAL,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAO,YAAA,EAAI;IACJP,EAAA,CAAAK,cAAA,cAA+C;IAC7CL,EAAA,CAAAqB,UAAA,KAAAC,4CAAA,oBAAmG;IACrGtB,EAAA,CAAAO,YAAA,EAAM;IACNP,EAAA,CAAAqB,UAAA,KAAAE,+CAAA,uBAEY;IAEZvB,EAAA,CAAAK,cAAA,eAAwB;IACwCL,EAAA,CAAAa,UAAA,2BAAAW,mEAAAC,MAAA;MAAAzB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAU,MAAA,GAAA1B,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAO,MAAA,CAAAC,IAAA,GAAAF,MAAA;IAAA,EAAkB;;IAAhFzB,EAAA,CAAAO,YAAA,EAC0E;IAI9EP,EAAA,CAAAK,cAAA,eAA0B;IACsBL,EAAA,CAAAa,UAAA,mBAAAe,4DAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAa,MAAA,GAAA7B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAU,MAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACjE9B,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;IA5BTP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAU,WAAA,yCACF;IASIV,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAU,WAAA,qJACF;IAEWV,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAE,UAAA,SAAA6B,MAAA,CAAA3B,SAAA,CAAe;IAEuBJ,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAE,UAAA,SAAA6B,MAAA,CAAAnB,MAAA,CAAY;IAMpDZ,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAgC,qBAAA,gBAAAhC,EAAA,CAAAU,WAAA,sCAAyD;IADFV,EAAA,CAAAE,UAAA,YAAA6B,MAAA,CAAAJ,IAAA,CAAkB;IAOhF3B,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAU,WAAA,wBACF;;;ADjBN,OAAM,MAAOuB,mBAAmB;EAC9BC,YACSC,YAAyB,EACzBC,eAA+B,EAC/BC,aAA6B,EAC7BC,MAAwB;IAHxB,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAgBf,KAAAC,YAAY,GAAG,KAAK;IAdlBJ,YAAY,CAACK,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC1C,IAAI,CAACC,YAAY,GAAGD,IAAI;MACxB,IAAI,CAACH,YAAY,GAAGG,IAAI,CAACE,eAAe;IAC1C,CAAC,CAAC;IACFR,eAAe,CAACS,IAAI,EAAE;IACtB,IAAI,CAACV,YAAY,CAACW,iBAAiB,EAAE,CAACL,SAAS,CAAEM,IAAI,IAAI;MACvD,IAAI,CAACnC,MAAM,GAAGmC,IAAI,CAACnC,MAAM;MACzB,IAAI,CAACR,SAAS,GAAG2C,IAAI,CAACC,GAAG;IAC3B,CAAC,CAAC;EACJ;EAMAC,QAAQA,CAAA,GAAU;EAElBnB,SAASA,CAAA;IACP,IAAIiB,IAAI,GAAG;MACTpB,IAAI,EAAE,IAAI,CAACA,IAAI;MACfuB,SAAS,EAAE,IAAI,CAACtC,MAAM;MACtBuC,UAAU,EAAE;KACb;IACD,IAAI,CAACf,eAAe,CAACS,IAAI,EAAE;IAC3B,IAAI,CAACV,YAAY,CAACL,SAAS,CAACiB,IAAI,CAAC,CAACN,SAAS,CACxCW,GAAG,IAAI;MACN,IAAIC,OAAO,GAAGD,GAAG,CAACC,OAAO;MACzB,IAAI,CAAClB,YAAY,CAACmB,UAAU,EAAE,CAACb,SAAS,CACrCC,IAAI,IAAI;QACP3C,IAAI,CAACwD,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,SAAS,CAAC;UACrCC,IAAI,EAAEL,OAAO;UACbM,IAAI,EAAE;SACP,CAAC;QACF,IAAI,CAACtB,aAAa,CAACuB,OAAO,EAAE;MAC9B,CAAC,EACAC,GAAG,IAAI;QACN9D,IAAI,CAACwD,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,OAAO,CAAC;UACnCC,IAAI,EAAEG,GAAG,CAACR,OAAO;UACjBM,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CACF;IACH,CAAC,EACAE,GAAG,IAAI;MACN9D,IAAI,CAACwD,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,OAAO,CAAC;QACnCC,IAAI,EAAEG,GAAG,CAACR,OAAO;QACjBM,IAAI,EAAE;OACP,CAAC;IACJ,CAAC,CACF;EACH;EACAvC,KAAKA,CAAA;IACH,IAAI,CAACiB,aAAa,CAACuB,OAAO,EAAE;EAC9B;EAAC,QAAAE,CAAA;qBA/DU7B,mBAAmB,EAAAjC,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjE,EAAA,CAAA+D,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnE,EAAA,CAAA+D,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAArE,EAAA,CAAA+D,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA;UAAnBvC,mBAAmB;IAAAwC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdhC/E,EAAA,CAAAqB,UAAA,IAAA4D,kCAAA,mBAkCM;;;QAlCAjF,EAAA,CAAAE,UAAA,SAAA8E,GAAA,CAAA5E,SAAA,CAAe", "names": ["<PERSON><PERSON>", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "qrCodeUrl", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r2", "secret", "ɵɵlistener", "ModalTwofaComponent_div_0_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "close", "ɵɵtemplate", "ModalTwofaComponent_div_0_qrcode_14_Template", "ModalTwofaComponent_div_0_ngb_alert_15_Template", "ModalTwofaComponent_div_0_Template_input_ngModelChange_17_listener", "$event", "ctx_r5", "code", "ModalTwofaComponent_div_0_Template_button_click_20_listener", "ctx_r6", "enable2fa", "ctx_r0", "ɵɵpropertyInterpolate", "ModalTwofaComponent", "constructor", "_authService", "_loadingService", "_modalService", "_trans", "is2faEnabled", "currentUser", "subscribe", "user", "current_user", "two_factor_auth", "show", "generate2faSecret", "data", "url", "ngOnInit", "auth_code", "is_enabled", "res", "message", "getProfile", "fire", "title", "instant", "text", "icon", "dismiss", "err", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "LoadingService", "i3", "NgbActiveModal", "i4", "TranslateService", "_2", "selectors", "decls", "vars", "consts", "template", "ModalTwofaComponent_Template", "rf", "ctx", "ModalTwofaComponent_div_0_Template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\profile-sercurity\\modal-twofa\\modal-twofa.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\profile\\profile-sercurity\\modal-twofa\\modal-twofa.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { User } from 'app/interfaces/user';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-modal-twofa',\r\n  templateUrl: './modal-twofa.component.html',\r\n  styleUrls: ['./modal-twofa.component.scss'],\r\n})\r\nexport class ModalTwofaComponent implements OnInit {\r\n  constructor(\r\n    public _authService: AuthService,\r\n    public _loadingService: LoadingService,\r\n    public _modalService: NgbActiveModal,\r\n    public _trans: TranslateService\r\n  ) {\r\n    _authService.currentUser.subscribe((user) => {\r\n      this.current_user = user;\r\n      this.is2faEnabled = user.two_factor_auth;\r\n    });\r\n    _loadingService.show();\r\n    this._authService.generate2faSecret().subscribe((data) => {\r\n      this.secret = data.secret;\r\n      this.qrCodeUrl = data.url;\r\n    });\r\n  }\r\n  current_user: User;\r\n  qrCodeUrl: string;\r\n  secret: string;\r\n  code: string;\r\n  is2faEnabled = false;\r\n  ngOnInit(): void {}\r\n\r\n  enable2fa() {\r\n    let data = {\r\n      code: this.code,\r\n      auth_code: this.secret,\r\n      is_enabled: true,\r\n    };\r\n    this._loadingService.show();\r\n    this._authService.enable2fa(data).subscribe(\r\n      (res) => {\r\n        let message = res.message;\r\n        this._authService.getProfile().subscribe(\r\n          (user) => {\r\n            Swal.fire({\r\n              title: this._trans.instant('Success'),\r\n              text: message,\r\n              icon: 'success',\r\n            });\r\n            this._modalService.dismiss();\r\n          },\r\n          (err) => {\r\n            Swal.fire({\r\n              title: this._trans.instant('Error'),\r\n              text: err.message,\r\n              icon: 'error',\r\n            });\r\n          }\r\n        );\r\n      },\r\n      (err) => {\r\n        Swal.fire({\r\n          title: this._trans.instant('Error'),\r\n          text: err.message,\r\n          icon: 'error',\r\n        });\r\n      }\r\n    );\r\n  }\r\n  close() {\r\n    this._modalService.dismiss();\r\n  }\r\n}\r\n", "<div *ngIf=\"qrCodeUrl\">\r\n  <div class=\"modal-header bg-white\">\r\n    <h4 class=\"modal-title\" id=\"label_follows\">\r\n      {{ 'Two Factor Authentication' | translate }}\r\n    </h4>\r\n    <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"close()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n\r\n  <form>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n      <p class=\"text-justify\">\r\n        {{ 'To be able to authorize transactions you need to scan this QR Code with your Google Authentication App or enter the secret key below' | translate }}\r\n      </p>\r\n      <div class=\"row d-flex justify-content-center\">\r\n        <qrcode *ngIf=\"qrCodeUrl\" [qrdata]=\"qrCodeUrl\" [width]=\"256\" [errorCorrectionLevel]=\"'M'\"></qrcode>\r\n      </div>\r\n      <ngb-alert type=\"warning\" [dismissible]=\"false\" *ngIf=\"secret\">\r\n        <div class=\"alert-body\"><strong>{{ 'Secret key'|translate }}: </strong> {{ secret }}</div>\r\n      </ngb-alert>\r\n\r\n      <div class=\"form-group\">\r\n        <input type=\"text\" class=\"form-control\" id=\"code\" name=\"code\" [(ngModel)]=\"code\"\r\n               placeholder=\"{{'Enter authentication code' | translate}}\" required>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"modal-footer\">\r\n      <button type=\"submit\" class=\"btn btn-primary\" (click)=\"enable2fa()\">\r\n        {{ 'Enable' | translate }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
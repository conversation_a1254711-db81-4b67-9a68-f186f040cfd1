{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Component, Input, NgModule } from '@angular/core';\nimport lightGallery from 'lightgallery';\nconst _c0 = [\"*\"];\nclass LightgalleryService {\n  constructor() {}\n}\nLightgalleryService.ɵfac = function LightgalleryService_Factory(t) {\n  return new (t || LightgalleryService)();\n};\nLightgalleryService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LightgalleryService,\n  factory: LightgalleryService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LightgalleryService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nconst LgMethods = {\n  onAfterAppendSlide: 'lgAfterAppendSlide',\n  onInit: 'lgInit',\n  onHasVideo: 'lgHasVideo',\n  onContainerResize: 'lgContainerResize',\n  onUpdateSlides: 'lgUpdateSlides',\n  onAfterAppendSubHtml: 'lgAfterAppendSubHtml',\n  onBeforeOpen: 'lgBeforeOpen',\n  onAfterOpen: 'lgAfterOpen',\n  onSlideItemLoad: 'lgSlideItemLoad',\n  onBeforeSlide: 'lgBeforeSlide',\n  onAfterSlide: 'lgAfterSlide',\n  onPosterClick: 'lgPosterClick',\n  onDragStart: 'lgDragStart',\n  onDragMove: 'lgDragMove',\n  onDragEnd: 'lgDragEnd',\n  onBeforeNextSlide: 'lgBeforeNextSlide',\n  onBeforePrevSlide: 'lgBeforePrevSlide',\n  onBeforeClose: 'lgBeforeClose',\n  onAfterClose: 'lgAfterClose',\n  onRotateLeft: 'lgRotateLeft',\n  onRotateRight: 'lgRotateRight',\n  onFlipHorizontal: 'lgFlipHorizontal',\n  onFlipVertical: 'lgFlipVertical'\n};\nclass LightgalleryComponent {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n    this.lgInitialized = false;\n    this._elementRef = _elementRef;\n  }\n  ngAfterViewChecked() {\n    if (!this.lgInitialized) {\n      this.registerEvents();\n      this.LG = lightGallery(this._elementRef.nativeElement, this.settings);\n      this.lgInitialized = true;\n    }\n  }\n  ngOnDestroy() {\n    this.LG.destroy();\n    this.lgInitialized = false;\n  }\n  registerEvents() {\n    if (this.onAfterAppendSlide) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onAfterAppendSlide, event => {\n        this.onAfterAppendSlide && this.onAfterAppendSlide(event.detail);\n      });\n    }\n    if (this.onInit) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onInit, event => {\n        this.onInit && this.onInit(event.detail);\n      });\n    }\n    if (this.onHasVideo) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onHasVideo, event => {\n        this.onHasVideo && this.onHasVideo(event.detail);\n      });\n    }\n    if (this.onContainerResize) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onContainerResize, event => {\n        this.onContainerResize && this.onContainerResize(event.detail);\n      });\n    }\n    if (this.onAfterAppendSubHtml) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onAfterAppendSubHtml, event => {\n        this.onAfterAppendSubHtml && this.onAfterAppendSubHtml(event.detail);\n      });\n    }\n    if (this.onBeforeOpen) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeOpen, event => {\n        this.onBeforeOpen && this.onBeforeOpen(event.detail);\n      });\n    }\n    if (this.onAfterOpen) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onAfterOpen, event => {\n        this.onAfterOpen && this.onAfterOpen(event.detail);\n      });\n    }\n    if (this.onSlideItemLoad) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onSlideItemLoad, event => {\n        this.onSlideItemLoad && this.onSlideItemLoad(event.detail);\n      });\n    }\n    if (this.onBeforeSlide) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeSlide, event => {\n        this.onBeforeSlide && this.onBeforeSlide(event.detail);\n      });\n    }\n    if (this.onAfterSlide) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onAfterSlide, event => {\n        this.onAfterSlide && this.onAfterSlide(event.detail);\n      });\n    }\n    if (this.onPosterClick) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onPosterClick, event => {\n        this.onPosterClick && this.onPosterClick(event.detail);\n      });\n    }\n    if (this.onDragStart) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onDragStart, event => {\n        this.onDragStart && this.onDragStart(event.detail);\n      });\n    }\n    if (this.onDragMove) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onDragMove, event => {\n        this.onDragMove && this.onDragMove(event.detail);\n      });\n    }\n    if (this.onDragEnd) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onDragEnd, event => {\n        this.onDragEnd && this.onDragEnd(event.detail);\n      });\n    }\n    if (this.onBeforeNextSlide) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeNextSlide, event => {\n        this.onBeforeNextSlide && this.onBeforeNextSlide(event.detail);\n      });\n    }\n    if (this.onBeforePrevSlide) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onBeforePrevSlide, event => {\n        this.onBeforePrevSlide && this.onBeforePrevSlide(event.detail);\n      });\n    }\n    if (this.onBeforeClose) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeClose, event => {\n        this.onBeforeClose && this.onBeforeClose(event.detail);\n      });\n    }\n    if (this.onAfterClose) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onAfterClose, event => {\n        this.onAfterClose && this.onAfterClose(event.detail);\n      });\n    }\n    if (this.onRotateLeft) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onRotateLeft, event => {\n        this.onRotateLeft && this.onRotateLeft(event.detail);\n      });\n    }\n    if (this.onRotateRight) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onRotateRight, event => {\n        this.onRotateRight && this.onRotateRight(event.detail);\n      });\n    }\n    if (this.onFlipHorizontal) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onFlipHorizontal, event => {\n        this.onFlipHorizontal && this.onFlipHorizontal(event.detail);\n      });\n    }\n    if (this.onFlipVertical) {\n      this._elementRef.nativeElement.addEventListener(LgMethods.onFlipVertical, event => {\n        this.onFlipVertical && this.onFlipVertical(event.detail);\n      });\n    }\n  }\n}\nLightgalleryComponent.ɵfac = function LightgalleryComponent_Factory(t) {\n  return new (t || LightgalleryComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nLightgalleryComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: LightgalleryComponent,\n  selectors: [[\"lightgallery\"]],\n  inputs: {\n    settings: \"settings\",\n    onAfterAppendSlide: \"onAfterAppendSlide\",\n    onInit: \"onInit\",\n    onHasVideo: \"onHasVideo\",\n    onContainerResize: \"onContainerResize\",\n    onAfterAppendSubHtml: \"onAfterAppendSubHtml\",\n    onBeforeOpen: \"onBeforeOpen\",\n    onAfterOpen: \"onAfterOpen\",\n    onSlideItemLoad: \"onSlideItemLoad\",\n    onBeforeSlide: \"onBeforeSlide\",\n    onAfterSlide: \"onAfterSlide\",\n    onPosterClick: \"onPosterClick\",\n    onDragStart: \"onDragStart\",\n    onDragMove: \"onDragMove\",\n    onDragEnd: \"onDragEnd\",\n    onBeforeNextSlide: \"onBeforeNextSlide\",\n    onBeforePrevSlide: \"onBeforePrevSlide\",\n    onBeforeClose: \"onBeforeClose\",\n    onAfterClose: \"onAfterClose\",\n    onRotateLeft: \"onRotateLeft\",\n    onRotateRight: \"onRotateRight\",\n    onFlipHorizontal: \"onFlipHorizontal\",\n    onFlipVertical: \"onFlipVertical\"\n  },\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function LightgalleryComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LightgalleryComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lightgallery',\n      template: '<ng-content></ng-content>'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    settings: [{\n      type: Input\n    }],\n    onAfterAppendSlide: [{\n      type: Input\n    }],\n    onInit: [{\n      type: Input\n    }],\n    onHasVideo: [{\n      type: Input\n    }],\n    onContainerResize: [{\n      type: Input\n    }],\n    onAfterAppendSubHtml: [{\n      type: Input\n    }],\n    onBeforeOpen: [{\n      type: Input\n    }],\n    onAfterOpen: [{\n      type: Input\n    }],\n    onSlideItemLoad: [{\n      type: Input\n    }],\n    onBeforeSlide: [{\n      type: Input\n    }],\n    onAfterSlide: [{\n      type: Input\n    }],\n    onPosterClick: [{\n      type: Input\n    }],\n    onDragStart: [{\n      type: Input\n    }],\n    onDragMove: [{\n      type: Input\n    }],\n    onDragEnd: [{\n      type: Input\n    }],\n    onBeforeNextSlide: [{\n      type: Input\n    }],\n    onBeforePrevSlide: [{\n      type: Input\n    }],\n    onBeforeClose: [{\n      type: Input\n    }],\n    onAfterClose: [{\n      type: Input\n    }],\n    onRotateLeft: [{\n      type: Input\n    }],\n    onRotateRight: [{\n      type: Input\n    }],\n    onFlipHorizontal: [{\n      type: Input\n    }],\n    onFlipVertical: [{\n      type: Input\n    }]\n  });\n})();\nclass LightgalleryModule {}\nLightgalleryModule.ɵfac = function LightgalleryModule_Factory(t) {\n  return new (t || LightgalleryModule)();\n};\nLightgalleryModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LightgalleryModule\n});\nLightgalleryModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LightgalleryModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [LightgalleryComponent],\n      imports: [],\n      exports: [LightgalleryComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of lightgallery-angular\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LightgalleryComponent, LightgalleryModule, LightgalleryService };", "map": {"version": 3, "names": ["i0", "Injectable", "Component", "Input", "NgModule", "lightGallery", "_c0", "LightgalleryService", "constructor", "ɵfac", "LightgalleryService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "LgMethods", "onAfterAppendSlide", "onInit", "onHasVideo", "onContainerResize", "onUpdateSlides", "onAfterAppendSubHtml", "onBeforeOpen", "onAfterOpen", "onSlideItemLoad", "onBeforeSlide", "onAfterSlide", "onPosterClick", "onDragStart", "onDragMove", "onDragEnd", "onBeforeNextSlide", "onBeforePrevSlide", "onBeforeClose", "onAfterClose", "onRotateLeft", "onRotateRight", "onFlipHorizontal", "onFlipVertical", "LightgalleryComponent", "_elementRef", "lgInitialized", "ngAfterViewChecked", "registerEvents", "LG", "nativeElement", "settings", "ngOnDestroy", "destroy", "addEventListener", "event", "detail", "LightgalleryComponent_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "ngContentSelectors", "decls", "vars", "template", "LightgalleryComponent_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "selector", "LightgalleryModule", "LightgalleryModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "imports", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/lightgallery/angular/fesm2020/lightgallery-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Component, Input, NgModule } from '@angular/core';\nimport lightGallery from 'lightgallery';\n\nclass LightgalleryService {\n    constructor() { }\n}\nLightgalleryService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nLightgalleryService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return []; } });\n\nconst LgMethods = {\n    onAfterAppendSlide: 'lgAfterAppendSlide',\n    onInit: 'lgInit',\n    onHasVideo: 'lgHasVideo',\n    onContainerResize: 'lgContainerResize',\n    onUpdateSlides: 'lgUpdateSlides',\n    onAfterAppendSubHtml: 'lgAfterAppendSubHtml',\n    onBeforeOpen: 'lgBeforeOpen',\n    onAfterOpen: 'lgAfterOpen',\n    onSlideItemLoad: 'lgSlideItemLoad',\n    onBeforeSlide: 'lgBeforeSlide',\n    onAfterSlide: 'lgAfterSlide',\n    onPosterClick: 'lgPosterClick',\n    onDragStart: 'lgDragStart',\n    onDragMove: 'lgDragMove',\n    onDragEnd: 'lgDragEnd',\n    onBeforeNextSlide: 'lgBeforeNextSlide',\n    onBeforePrevSlide: 'lgBeforePrevSlide',\n    onBeforeClose: 'lgBeforeClose',\n    onAfterClose: 'lgAfterClose',\n    onRotateLeft: 'lgRotateLeft',\n    onRotateRight: 'lgRotateRight',\n    onFlipHorizontal: 'lgFlipHorizontal',\n    onFlipVertical: 'lgFlipVertical',\n};\nclass LightgalleryComponent {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n        this.lgInitialized = false;\n        this._elementRef = _elementRef;\n    }\n    ngAfterViewChecked() {\n        if (!this.lgInitialized) {\n            this.registerEvents();\n            this.LG = lightGallery(this._elementRef.nativeElement, this.settings);\n            this.lgInitialized = true;\n        }\n    }\n    ngOnDestroy() {\n        this.LG.destroy();\n        this.lgInitialized = false;\n    }\n    registerEvents() {\n        if (this.onAfterAppendSlide) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onAfterAppendSlide, ((event) => {\n                this.onAfterAppendSlide &&\n                    this.onAfterAppendSlide(event.detail);\n            }));\n        }\n        if (this.onInit) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onInit, ((event) => {\n                this.onInit && this.onInit(event.detail);\n            }));\n        }\n        if (this.onHasVideo) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onHasVideo, ((event) => {\n                this.onHasVideo && this.onHasVideo(event.detail);\n            }));\n        }\n        if (this.onContainerResize) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onContainerResize, ((event) => {\n                this.onContainerResize &&\n                    this.onContainerResize(event.detail);\n            }));\n        }\n        if (this.onAfterAppendSubHtml) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onAfterAppendSubHtml, ((event) => {\n                this.onAfterAppendSubHtml &&\n                    this.onAfterAppendSubHtml(event.detail);\n            }));\n        }\n        if (this.onBeforeOpen) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeOpen, ((event) => {\n                this.onBeforeOpen && this.onBeforeOpen(event.detail);\n            }));\n        }\n        if (this.onAfterOpen) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onAfterOpen, ((event) => {\n                this.onAfterOpen && this.onAfterOpen(event.detail);\n            }));\n        }\n        if (this.onSlideItemLoad) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onSlideItemLoad, ((event) => {\n                this.onSlideItemLoad && this.onSlideItemLoad(event.detail);\n            }));\n        }\n        if (this.onBeforeSlide) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeSlide, ((event) => {\n                this.onBeforeSlide && this.onBeforeSlide(event.detail);\n            }));\n        }\n        if (this.onAfterSlide) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onAfterSlide, ((event) => {\n                this.onAfterSlide && this.onAfterSlide(event.detail);\n            }));\n        }\n        if (this.onPosterClick) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onPosterClick, ((event) => {\n                this.onPosterClick && this.onPosterClick(event.detail);\n            }));\n        }\n        if (this.onDragStart) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onDragStart, ((event) => {\n                this.onDragStart && this.onDragStart(event.detail);\n            }));\n        }\n        if (this.onDragMove) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onDragMove, ((event) => {\n                this.onDragMove && this.onDragMove(event.detail);\n            }));\n        }\n        if (this.onDragEnd) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onDragEnd, ((event) => {\n                this.onDragEnd && this.onDragEnd(event.detail);\n            }));\n        }\n        if (this.onBeforeNextSlide) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeNextSlide, ((event) => {\n                this.onBeforeNextSlide &&\n                    this.onBeforeNextSlide(event.detail);\n            }));\n        }\n        if (this.onBeforePrevSlide) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onBeforePrevSlide, ((event) => {\n                this.onBeforePrevSlide &&\n                    this.onBeforePrevSlide(event.detail);\n            }));\n        }\n        if (this.onBeforeClose) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onBeforeClose, ((event) => {\n                this.onBeforeClose && this.onBeforeClose(event.detail);\n            }));\n        }\n        if (this.onAfterClose) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onAfterClose, ((event) => {\n                this.onAfterClose && this.onAfterClose(event.detail);\n            }));\n        }\n        if (this.onRotateLeft) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onRotateLeft, ((event) => {\n                this.onRotateLeft && this.onRotateLeft(event.detail);\n            }));\n        }\n        if (this.onRotateRight) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onRotateRight, ((event) => {\n                this.onRotateRight && this.onRotateRight(event.detail);\n            }));\n        }\n        if (this.onFlipHorizontal) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onFlipHorizontal, ((event) => {\n                this.onFlipHorizontal &&\n                    this.onFlipHorizontal(event.detail);\n            }));\n        }\n        if (this.onFlipVertical) {\n            this._elementRef.nativeElement.addEventListener(LgMethods.onFlipVertical, ((event) => {\n                this.onFlipVertical && this.onFlipVertical(event.detail);\n            }));\n        }\n    }\n}\nLightgalleryComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryComponent, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nLightgalleryComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.2\", type: LightgalleryComponent, selector: \"lightgallery\", inputs: { settings: \"settings\", onAfterAppendSlide: \"onAfterAppendSlide\", onInit: \"onInit\", onHasVideo: \"onHasVideo\", onContainerResize: \"onContainerResize\", onAfterAppendSubHtml: \"onAfterAppendSubHtml\", onBeforeOpen: \"onBeforeOpen\", onAfterOpen: \"onAfterOpen\", onSlideItemLoad: \"onSlideItemLoad\", onBeforeSlide: \"onBeforeSlide\", onAfterSlide: \"onAfterSlide\", onPosterClick: \"onPosterClick\", onDragStart: \"onDragStart\", onDragMove: \"onDragMove\", onDragEnd: \"onDragEnd\", onBeforeNextSlide: \"onBeforeNextSlide\", onBeforePrevSlide: \"onBeforePrevSlide\", onBeforeClose: \"onBeforeClose\", onAfterClose: \"onAfterClose\", onRotateLeft: \"onRotateLeft\", onRotateRight: \"onRotateRight\", onFlipHorizontal: \"onFlipHorizontal\", onFlipVertical: \"onFlipVertical\" }, ngImport: i0, template: '<ng-content></ng-content>', isInline: true });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'lightgallery', template: '<ng-content></ng-content>' }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { settings: [{\n                type: Input\n            }], onAfterAppendSlide: [{\n                type: Input\n            }], onInit: [{\n                type: Input\n            }], onHasVideo: [{\n                type: Input\n            }], onContainerResize: [{\n                type: Input\n            }], onAfterAppendSubHtml: [{\n                type: Input\n            }], onBeforeOpen: [{\n                type: Input\n            }], onAfterOpen: [{\n                type: Input\n            }], onSlideItemLoad: [{\n                type: Input\n            }], onBeforeSlide: [{\n                type: Input\n            }], onAfterSlide: [{\n                type: Input\n            }], onPosterClick: [{\n                type: Input\n            }], onDragStart: [{\n                type: Input\n            }], onDragMove: [{\n                type: Input\n            }], onDragEnd: [{\n                type: Input\n            }], onBeforeNextSlide: [{\n                type: Input\n            }], onBeforePrevSlide: [{\n                type: Input\n            }], onBeforeClose: [{\n                type: Input\n            }], onAfterClose: [{\n                type: Input\n            }], onRotateLeft: [{\n                type: Input\n            }], onRotateRight: [{\n                type: Input\n            }], onFlipHorizontal: [{\n                type: Input\n            }], onFlipVertical: [{\n                type: Input\n            }] } });\n\nclass LightgalleryModule {\n}\nLightgalleryModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nLightgalleryModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryModule, declarations: [LightgalleryComponent], exports: [LightgalleryComponent] });\nLightgalleryModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: LightgalleryModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [LightgalleryComponent],\n                    imports: [],\n                    exports: [LightgalleryComponent],\n                }]\n        }] });\n\n/*\n * Public API Surface of lightgallery-angular\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LightgalleryComponent, LightgalleryModule, LightgalleryService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtE,OAAOC,YAAY,MAAM,cAAc;AAAC,MAAAC,GAAA;AAExC,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAAA,EAAG,CAAE;AACpB;AACAD,mBAAmB,CAACE,IAAI,YAAAC,4BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFJ,mBAAmB;AAAA,CAAoD;AACvLA,mBAAmB,CAACK,KAAK,kBAD6EZ,EAAE,CAAAa,kBAAA;EAAAC,KAAA,EACYP,mBAAmB;EAAAQ,OAAA,EAAnBR,mBAAmB,CAAAE,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AAC9J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFsGjB,EAAE,CAAAkB,iBAAA,CAEbX,mBAAmB,EAAc,CAAC;IACjHY,IAAI,EAAElB,UAAU;IAChBmB,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMK,SAAS,GAAG;EACdC,kBAAkB,EAAE,oBAAoB;EACxCC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,iBAAiB,EAAE,mBAAmB;EACtCC,cAAc,EAAE,gBAAgB;EAChCC,oBAAoB,EAAE,sBAAsB;EAC5CC,YAAY,EAAE,cAAc;EAC5BC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,WAAW;EACtBC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,cAAc;EAC5BC,YAAY,EAAE,cAAc;EAC5BC,aAAa,EAAE,eAAe;EAC9BC,gBAAgB,EAAE,kBAAkB;EACpCC,cAAc,EAAE;AACpB,CAAC;AACD,MAAMC,qBAAqB,CAAC;EACxBrC,WAAWA,CAACsC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,WAAW,GAAGA,WAAW;EAClC;EACAE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;MACrB,IAAI,CAACE,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,EAAE,GAAG7C,YAAY,CAAC,IAAI,CAACyC,WAAW,CAACK,aAAa,EAAE,IAAI,CAACC,QAAQ,CAAC;MACrE,IAAI,CAACL,aAAa,GAAG,IAAI;IAC7B;EACJ;EACAM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,EAAE,CAACI,OAAO,CAAC,CAAC;IACjB,IAAI,CAACP,aAAa,GAAG,KAAK;EAC9B;EACAE,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC3B,kBAAkB,EAAE;MACzB,IAAI,CAACwB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACC,kBAAkB,EAAIkC,KAAK,IAAK;QACtF,IAAI,CAAClC,kBAAkB,IACnB,IAAI,CAACA,kBAAkB,CAACkC,KAAK,CAACC,MAAM,CAAC;MAC7C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAClC,MAAM,EAAE;MACb,IAAI,CAACuB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACE,MAAM,EAAIiC,KAAK,IAAK;QAC1E,IAAI,CAACjC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACiC,KAAK,CAACC,MAAM,CAAC;MAC5C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACjC,UAAU,EAAE;MACjB,IAAI,CAACsB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACG,UAAU,EAAIgC,KAAK,IAAK;QAC9E,IAAI,CAAChC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgC,KAAK,CAACC,MAAM,CAAC;MACpD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAChC,iBAAiB,EAAE;MACxB,IAAI,CAACqB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACI,iBAAiB,EAAI+B,KAAK,IAAK;QACrF,IAAI,CAAC/B,iBAAiB,IAClB,IAAI,CAACA,iBAAiB,CAAC+B,KAAK,CAACC,MAAM,CAAC;MAC5C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAC9B,oBAAoB,EAAE;MAC3B,IAAI,CAACmB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACM,oBAAoB,EAAI6B,KAAK,IAAK;QACxF,IAAI,CAAC7B,oBAAoB,IACrB,IAAI,CAACA,oBAAoB,CAAC6B,KAAK,CAACC,MAAM,CAAC;MAC/C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAC7B,YAAY,EAAE;MACnB,IAAI,CAACkB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACO,YAAY,EAAI4B,KAAK,IAAK;QAChF,IAAI,CAAC5B,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC4B,KAAK,CAACC,MAAM,CAAC;MACxD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAC5B,WAAW,EAAE;MAClB,IAAI,CAACiB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACQ,WAAW,EAAI2B,KAAK,IAAK;QAC/E,IAAI,CAAC3B,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC2B,KAAK,CAACC,MAAM,CAAC;MACtD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAC3B,eAAe,EAAE;MACtB,IAAI,CAACgB,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACS,eAAe,EAAI0B,KAAK,IAAK;QACnF,IAAI,CAAC1B,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC0B,KAAK,CAACC,MAAM,CAAC;MAC9D,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAC1B,aAAa,EAAE;MACpB,IAAI,CAACe,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACU,aAAa,EAAIyB,KAAK,IAAK;QACjF,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyB,KAAK,CAACC,MAAM,CAAC;MAC1D,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACzB,YAAY,EAAE;MACnB,IAAI,CAACc,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACW,YAAY,EAAIwB,KAAK,IAAK;QAChF,IAAI,CAACxB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACwB,KAAK,CAACC,MAAM,CAAC;MACxD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACxB,aAAa,EAAE;MACpB,IAAI,CAACa,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACY,aAAa,EAAIuB,KAAK,IAAK;QACjF,IAAI,CAACvB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACuB,KAAK,CAACC,MAAM,CAAC;MAC1D,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACvB,WAAW,EAAE;MAClB,IAAI,CAACY,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACa,WAAW,EAAIsB,KAAK,IAAK;QAC/E,IAAI,CAACtB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACsB,KAAK,CAACC,MAAM,CAAC;MACtD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACtB,UAAU,EAAE;MACjB,IAAI,CAACW,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACc,UAAU,EAAIqB,KAAK,IAAK;QAC9E,IAAI,CAACrB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACqB,KAAK,CAACC,MAAM,CAAC;MACpD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACrB,SAAS,EAAE;MAChB,IAAI,CAACU,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACe,SAAS,EAAIoB,KAAK,IAAK;QAC7E,IAAI,CAACpB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACoB,KAAK,CAACC,MAAM,CAAC;MAClD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACpB,iBAAiB,EAAE;MACxB,IAAI,CAACS,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACgB,iBAAiB,EAAImB,KAAK,IAAK;QACrF,IAAI,CAACnB,iBAAiB,IAClB,IAAI,CAACA,iBAAiB,CAACmB,KAAK,CAACC,MAAM,CAAC;MAC5C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACnB,iBAAiB,EAAE;MACxB,IAAI,CAACQ,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACiB,iBAAiB,EAAIkB,KAAK,IAAK;QACrF,IAAI,CAAClB,iBAAiB,IAClB,IAAI,CAACA,iBAAiB,CAACkB,KAAK,CAACC,MAAM,CAAC;MAC5C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAClB,aAAa,EAAE;MACpB,IAAI,CAACO,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACkB,aAAa,EAAIiB,KAAK,IAAK;QACjF,IAAI,CAACjB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiB,KAAK,CAACC,MAAM,CAAC;MAC1D,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACjB,YAAY,EAAE;MACnB,IAAI,CAACM,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACmB,YAAY,EAAIgB,KAAK,IAAK;QAChF,IAAI,CAAChB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACgB,KAAK,CAACC,MAAM,CAAC;MACxD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAAChB,YAAY,EAAE;MACnB,IAAI,CAACK,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACoB,YAAY,EAAIe,KAAK,IAAK;QAChF,IAAI,CAACf,YAAY,IAAI,IAAI,CAACA,YAAY,CAACe,KAAK,CAACC,MAAM,CAAC;MACxD,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACf,aAAa,EAAE;MACpB,IAAI,CAACI,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACqB,aAAa,EAAIc,KAAK,IAAK;QACjF,IAAI,CAACd,aAAa,IAAI,IAAI,CAACA,aAAa,CAACc,KAAK,CAACC,MAAM,CAAC;MAC1D,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACd,gBAAgB,EAAE;MACvB,IAAI,CAACG,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACsB,gBAAgB,EAAIa,KAAK,IAAK;QACpF,IAAI,CAACb,gBAAgB,IACjB,IAAI,CAACA,gBAAgB,CAACa,KAAK,CAACC,MAAM,CAAC;MAC3C,CAAE,CAAC;IACP;IACA,IAAI,IAAI,CAACb,cAAc,EAAE;MACrB,IAAI,CAACE,WAAW,CAACK,aAAa,CAACI,gBAAgB,CAAClC,SAAS,CAACuB,cAAc,EAAIY,KAAK,IAAK;QAClF,IAAI,CAACZ,cAAc,IAAI,IAAI,CAACA,cAAc,CAACY,KAAK,CAACC,MAAM,CAAC;MAC5D,CAAE,CAAC;IACP;EACJ;AACJ;AACAZ,qBAAqB,CAACpC,IAAI,YAAAiD,8BAAA/C,CAAA;EAAA,YAAAA,CAAA,IAAwFkC,qBAAqB,EA1KjC7C,EAAE,CAAA2D,iBAAA,CA0KiD3D,EAAE,CAAC4D,UAAU;AAAA,CAA4C;AAClNf,qBAAqB,CAACgB,IAAI,kBA3K4E7D,EAAE,CAAA8D,iBAAA;EAAA3C,IAAA,EA2KF0B,qBAAqB;EAAAkB,SAAA;EAAAC,MAAA;IAAAZ,QAAA;IAAA9B,kBAAA;IAAAC,MAAA;IAAAC,UAAA;IAAAC,iBAAA;IAAAE,oBAAA;IAAAC,YAAA;IAAAC,WAAA;IAAAC,eAAA;IAAAC,aAAA;IAAAC,YAAA;IAAAC,aAAA;IAAAC,WAAA;IAAAC,UAAA;IAAAC,SAAA;IAAAC,iBAAA;IAAAC,iBAAA;IAAAC,aAAA;IAAAC,YAAA;IAAAC,YAAA;IAAAC,aAAA;IAAAC,gBAAA;IAAAC,cAAA;EAAA;EAAAqB,kBAAA,EAAA3D,GAAA;EAAA4D,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3KrBtE,EAAE,CAAAwE,eAAA;MAAFxE,EAAE,CAAAyE,YAAA,EA2K40B,CAAC;IAAA;EAAA;EAAAC,aAAA;AAAA,EAAoB;AACz8B;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KA5KsGjB,EAAE,CAAAkB,iBAAA,CA4Kb2B,qBAAqB,EAAc,CAAC;IACnH1B,IAAI,EAAEjB,SAAS;IACfkB,IAAI,EAAE,CAAC;MAAEuD,QAAQ,EAAE,cAAc;MAAEP,QAAQ,EAAE;IAA4B,CAAC;EAC9E,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjD,IAAI,EAAEnB,EAAE,CAAC4D;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAER,QAAQ,EAAE,CAAC;MAC5FjC,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEmB,kBAAkB,EAAE,CAAC;MACrBH,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEoB,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEqB,UAAU,EAAE,CAAC;MACbL,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEsB,iBAAiB,EAAE,CAAC;MACpBN,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEwB,oBAAoB,EAAE,CAAC;MACvBR,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEyB,YAAY,EAAE,CAAC;MACfT,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAE0B,WAAW,EAAE,CAAC;MACdV,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAE2B,eAAe,EAAE,CAAC;MAClBX,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAE4B,aAAa,EAAE,CAAC;MAChBZ,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAE6B,YAAY,EAAE,CAAC;MACfb,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAE8B,aAAa,EAAE,CAAC;MAChBd,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAE+B,WAAW,EAAE,CAAC;MACdf,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEgC,UAAU,EAAE,CAAC;MACbhB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEiC,SAAS,EAAE,CAAC;MACZjB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEkC,iBAAiB,EAAE,CAAC;MACpBlB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEmC,iBAAiB,EAAE,CAAC;MACpBnB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEoC,aAAa,EAAE,CAAC;MAChBpB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEqC,YAAY,EAAE,CAAC;MACfrB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEsC,YAAY,EAAE,CAAC;MACftB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEuC,aAAa,EAAE,CAAC;MAChBvB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEwC,gBAAgB,EAAE,CAAC;MACnBxB,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEyC,cAAc,EAAE,CAAC;MACjBzB,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyE,kBAAkB,CAAC;AAEzBA,kBAAkB,CAACnE,IAAI,YAAAoE,2BAAAlE,CAAA;EAAA,YAAAA,CAAA,IAAwFiE,kBAAkB;AAAA,CAAkD;AACnLA,kBAAkB,CAACE,IAAI,kBAlO+E9E,EAAE,CAAA+E,gBAAA;EAAA5D,IAAA,EAkOQyD;AAAkB,EAA4E;AAC9MA,kBAAkB,CAACI,IAAI,kBAnO+EhF,EAAE,CAAAiF,gBAAA,IAmO6B;AACrI;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KApOsGjB,EAAE,CAAAkB,iBAAA,CAoOb0D,kBAAkB,EAAc,CAAC;IAChHzD,IAAI,EAAEf,QAAQ;IACdgB,IAAI,EAAE,CAAC;MACC8D,YAAY,EAAE,CAACrC,qBAAqB,CAAC;MACrCsC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,CAACvC,qBAAqB;IACnC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,qBAAqB,EAAE+B,kBAAkB,EAAErE,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { FieldType } from '@ngx-formly/core';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"app/services/loading.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../cropper-dialog/cropper-dialog.component\";\nimport * as i6 from \"@ngx-translate/core\";\nfunction ImageCropperTypeComponent_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 7);\n    i0.ɵɵlistener(\"click\", function ImageCropperTypeComponent_small_4_Template_small_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClick($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"note_\", ctx_r0.field.key, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.field.props.note ? ctx_r0.field.props.note : \"\");\n  }\n}\nfunction ImageCropperTypeComponent_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 8);\n    i0.ɵɵlistener(\"click\", function ImageCropperTypeComponent_small_6_Template_small_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onClick($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"image_test\", ctx_r1.field.key, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"Click here to show image for testing\"), \" \");\n  }\n}\nfunction ImageCropperTypeComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 1);\n    i0.ɵɵelement(2, \"img\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"border\", ctx_r2.isInvalid ? \"1px solid red\" : \"\");\n    i0.ɵɵproperty(\"src\", ctx_r2.image_src, i0.ɵɵsanitizeUrl);\n  }\n}\nexport class ImageCropperTypeComponent extends FieldType {\n  constructor(_http, _loadingService, ngForm) {\n    super();\n    this._http = _http;\n    this._loadingService = _loadingService;\n    this.ngForm = ngForm;\n    this.submitted = false;\n    this.uploaded = false;\n    this.image_src = '';\n    this.newImageURL = null;\n    this.environment = environment;\n    this.configPhoto = {\n      width: 250,\n      height: 250,\n      resizableArea: false,\n      output: {\n        width: 250,\n        height: 250\n      }\n    };\n    ngForm.ngSubmit.subscribe(event => {\n      this.submitted = true;\n    });\n  }\n  onCropedPhoto(event) {\n    let type = event.type;\n    this.image_src = event.preview;\n    let file = type == 'file' ? event.data : this.base64ToFile(event.data);\n    this.submitFile(file);\n  }\n  onClick($event) {\n    // if environment is local, then set the value to assets/images/logo/ezactive_1024x1024.png\n    if (environment.production === false) {\n      this.uploaded = true;\n      if (this.to.hasOwnProperty('test_image')) {\n        this.formControl.setValue(this.to.test_image);\n      } else {\n        this.formControl.setValue('assets/images/ico/icon-72x72.png');\n      }\n      // close browser file dialog\n      $event.target.value = null;\n    }\n  }\n  submitFile(file) {\n    const formData = new FormData();\n    formData.append('upload', file);\n    formData.append('uploadField', this.field.key);\n    formData.append('dir', this.field.props.dir);\n    formData.append('action', 'upload');\n    this._loadingService.show();\n    this.field.props.upload_url = this.field.props.upload_url ? this.field.props.upload_url : `${environment.apiUrl}/s3`; // fix after\n    if (this.field.props.hasOwnProperty('upload_url')) {\n      this._http.post(this.field.props.upload_url, formData).subscribe(res => {\n        this.uploaded = true;\n        if (res.hasOwnProperty('fieldErrors')) {\n          res.fieldErrors.forEach(fieldError => {\n            if (fieldError.name === this.field.key) {\n              this.formControl.setErrors({\n                serverError: fieldError.status\n              });\n            }\n          });\n        } else if (res.hasOwnProperty('files')) {\n          // let files = res.files.files;\n          // let key_file = Object.keys(files)[0];\n          // let url = files[key_file].url;\n          //this.formControl.setValue(url);\n          this.newImageURL = res.files.url;\n          this.image_src = this.newImageURL;\n          let imagePath = res.files.filename;\n          this.formControl.setValue(imagePath);\n        }\n      });\n    }\n  }\n  // convert base64 to file\n  base64ToFile(dataurl, filename) {\n    let timestamp = new Date().getTime();\n    filename = filename || `image_${timestamp}`;\n    var arr = dataurl.split(','),\n      mime = arr[0].match(/:(.*?);/)[1],\n      bstr = atob(arr[1]),\n      n = bstr.length,\n      u8arr = new Uint8Array(n);\n    while (n--) {\n      u8arr[n] = bstr.charCodeAt(n);\n    }\n    filename = filename + '.' + mime.split('/')[1];\n    return new File([u8arr], filename, {\n      type: mime\n    });\n  }\n  // check field is invalid\n  get isInvalid() {\n    return this.formControl.invalid && (this.formControl.dirty || this.formControl.touched || this.submitted);\n  }\n  ngOnInit() {\n    this.field.props['useCropperDialog'] = this.field.props.hasOwnProperty('useCropperDialog') ? this.field.props.useCropperDialog : true;\n    this.image_src = this.formControl.value;\n    this.formControl.valueChanges.subscribe(value => {\n      if (!value) {\n        this.uploaded = false;\n      }\n      if (this.newImageURL) {\n        this.image_src = this.newImageURL;\n        this.newImageURL = null;\n      } else {\n        this.image_src = value;\n      }\n      // this.image_src = value;\n    });\n\n    if (this.field.props.hasOwnProperty('config')) {\n      // merge config\n      this.configPhoto = {\n        ...this.configPhoto,\n        ...this.field.props.config\n      };\n    }\n  }\n  ngOnDestroy() {\n    return this.image_src = null;\n  }\n  static #_ = this.ɵfac = function ImageCropperTypeComponent_Factory(t) {\n    return new (t || ImageCropperTypeComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.LoadingService), i0.ɵɵdirectiveInject(i3.FormGroupDirective));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ImageCropperTypeComponent,\n    selectors: [[\"app-image-cropper-type\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 8,\n    vars: 8,\n    consts: [[1, \"row\"], [1, \"col-auto\"], [3, \"cropper_config\", \"disabled\", \"maxFileSize\", \"accept\", \"useCropperDialog\", \"croppedImage\"], [\"class\", \"text-muted\", 3, \"id\", \"click\", 4, \"ngIf\"], [1, \"m-0\"], [3, \"id\", \"click\", 4, \"ngIf\"], [\"class\", \"row justify-content-center mt-1\", 4, \"ngIf\"], [1, \"text-muted\", 3, \"id\", \"click\"], [3, \"id\", \"click\"], [1, \"row\", \"justify-content-center\", \"mt-1\"], [\"alt\", \"\", 1, \"img-fluid\", 2, \"max-width\", \"250px\", 3, \"src\"]],\n    template: function ImageCropperTypeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"div\", 1)(3, \"aui-cropper-with-dialog\", 2);\n        i0.ɵɵlistener(\"croppedImage\", function ImageCropperTypeComponent_Template_aui_cropper_with_dialog_croppedImage_3_listener($event) {\n          return ctx.onCropedPhoto($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(4, ImageCropperTypeComponent_small_4_Template, 2, 2, \"small\", 3);\n        i0.ɵɵelementStart(5, \"p\", 4);\n        i0.ɵɵtemplate(6, ImageCropperTypeComponent_small_6_Template, 3, 4, \"small\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, ImageCropperTypeComponent_div_7_Template, 3, 3, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"cropper_config\", ctx.configPhoto)(\"disabled\", ctx.to.disabled)(\"maxFileSize\", ctx.field.props.maxFileSize ? ctx.field.props.maxFileSize : 5000)(\"accept\", ctx.field.props.accept ? ctx.field.props.accept : \"image/*\")(\"useCropperDialog\", ctx.field.props.useCropperDialog);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.field.props.note);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.environment.production);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.image_src);\n      }\n    },\n    dependencies: [i4.NgIf, i5.CropperDialogComponent, i6.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,SAAS,QAAyB,kBAAkB;AAE7D,SAASC,WAAW,QAAQ,0BAA0B;;;;;;;;;;;ICGpDC,EAAA,CAAAC,cAAA,eACqB;IADmCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,OAAA,CAAAN,MAAA,CAAe;IAAA,EAAC;IAC5DJ,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAQ;;;;IADrCZ,EAAA,CAAAa,sBAAA,gBAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,KAAuB;IAClChB,EAAA,CAAAiB,SAAA,GAAwC;IAAxCjB,EAAA,CAAAkB,iBAAA,CAAAJ,MAAA,CAAAC,KAAA,CAAAI,KAAA,CAAAC,IAAA,GAAAN,MAAA,CAAAC,KAAA,CAAAI,KAAA,CAAAC,IAAA,MAAwC;;;;;;IAE3DpB,EAAA,CAAAC,cAAA,eAA8F;IAA1DD,EAAA,CAAAE,UAAA,mBAAAmB,kEAAAjB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAc,MAAA,CAAAb,OAAA,CAAAN,MAAA,CAAe;IAAA,EAAC;IAC3DJ,EAAA,CAAAW,MAAA,GACF;;IAAAX,EAAA,CAAAY,YAAA,EAAQ;;;;IAFDZ,EAAA,CAAAa,sBAAA,qBAAAW,MAAA,CAAAT,KAAA,CAAAC,GAAA,KAA4B;IACjChB,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,MAAAzB,EAAA,CAAA0B,WAAA,oDACF;;;;;IAEF1B,EAAA,CAAAC,cAAA,aAA+D;IAE3DD,EAAA,CAAA2B,SAAA,cACgD;IAClD3B,EAAA,CAAAY,YAAA,EAAM;;;;IADFZ,EAAA,CAAAiB,SAAA,GAA6C;IAA7CjB,EAAA,CAAA4B,WAAA,WAAAC,MAAA,CAAAC,SAAA,wBAA6C;IAD1C9B,EAAA,CAAA+B,UAAA,QAAAF,MAAA,CAAAG,SAAA,EAAAhC,EAAA,CAAAiC,aAAA,CAAiB;;;ADL5B,OAAM,MAAOC,yBACX,SAAQpC,SAA0B;EAGlCqC,YACSC,KAAiB,EACjBC,eAA+B,EAC9BC,MAA0B;IAElC,KAAK,EAAE;IAJA,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACd,KAAAC,MAAM,GAANA,MAAM;IAQhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,QAAQ,GAAG,KAAK;IAET,KAAAR,SAAS,GAAG,EAAE;IAEb,KAAAS,WAAW,GAAkB,IAAI;IAEzC,KAAA1C,WAAW,GAAGA,WAAW;IA2EzB,KAAA2C,WAAW,GAAqB;MAC9BC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE;QACNH,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE;;KAEX;IA/FCN,MAAM,CAACS,QAAQ,CAACC,SAAS,CAAEC,KAAK,IAAI;MAClC,IAAI,CAACV,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EAUAW,aAAaA,CAACD,KAAK;IACjB,IAAIE,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAI,CAACnB,SAAS,GAAGiB,KAAK,CAACG,OAAO;IAC9B,IAAIC,IAAI,GAAGF,IAAI,IAAI,MAAM,GAAGF,KAAK,CAACK,IAAI,GAAG,IAAI,CAACC,YAAY,CAACN,KAAK,CAACK,IAAI,CAAC;IACtE,IAAI,CAACE,UAAU,CAACH,IAAI,CAAC;EACvB;EAEA3C,OAAOA,CAACN,MAAM;IACZ;IACA,IAAIL,WAAW,CAAC0D,UAAU,KAAK,KAAK,EAAE;MACpC,IAAI,CAACjB,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACkB,EAAE,CAACC,cAAc,CAAC,YAAY,CAAC,EAAE;QACxC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,IAAI,CAACH,EAAE,CAACI,UAAU,CAAC;OAC9C,MAAM;QACL,IAAI,CAACF,WAAW,CAACC,QAAQ,CAAC,kCAAkC,CAAC;;MAE/D;MACAzD,MAAM,CAAC2D,MAAM,CAACC,KAAK,GAAG,IAAI;;EAE9B;EAEAR,UAAUA,CAACH,IAAI;IACb,MAAMY,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEd,IAAI,CAAC;IAC/BY,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACpD,KAAK,CAACC,GAAa,CAAC;IACxDiD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACI,KAAK,CAACiD,GAAa,CAAC;IACtDH,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACnC,IAAI,CAAC9B,eAAe,CAACgC,IAAI,EAAE;IAC3B,IAAI,CAACtD,KAAK,CAACI,KAAK,CAACmD,UAAU,GAAG,IAAI,CAACvD,KAAK,CAACI,KAAK,CAACmD,UAAU,GACrD,IAAI,CAACvD,KAAK,CAACI,KAAK,CAACmD,UAAU,GAC3B,GAAGvE,WAAW,CAACwE,MAAM,KAAK,CAAC,CAAC;IAChC,IAAI,IAAI,CAACxD,KAAK,CAACI,KAAK,CAACwC,cAAc,CAAC,YAAY,CAAC,EAAE;MACjD,IAAI,CAACvB,KAAK,CACPoC,IAAI,CAAC,IAAI,CAACzD,KAAK,CAACI,KAAK,CAACmD,UAAU,EAAEL,QAAQ,CAAC,CAC3CjB,SAAS,CAAEyB,GAAQ,IAAI;QACtB,IAAI,CAACjC,QAAQ,GAAG,IAAI;QACpB,IAAIiC,GAAG,CAACd,cAAc,CAAC,aAAa,CAAC,EAAE;UACrCc,GAAG,CAACC,WAAW,CAACC,OAAO,CAAEC,UAAe,IAAI;YAC1C,IAAIA,UAAU,CAACC,IAAI,KAAK,IAAI,CAAC9D,KAAK,CAACC,GAAG,EAAE;cACtC,IAAI,CAAC4C,WAAW,CAACkB,SAAS,CAAC;gBAAEC,WAAW,EAAEH,UAAU,CAACI;cAAM,CAAE,CAAC;;UAElE,CAAC,CAAC;SACH,MAAM,IAAIP,GAAG,CAACd,cAAc,CAAC,OAAO,CAAC,EAAE;UACtC;UACA;UACA;UACA;UAEA,IAAI,CAAClB,WAAW,GAAGgC,GAAG,CAACQ,KAAK,CAACC,GAAG;UAChC,IAAI,CAAClD,SAAS,GAAG,IAAI,CAACS,WAAW;UAEjC,IAAI0C,SAAS,GAAGV,GAAG,CAACQ,KAAK,CAACG,QAAQ;UAClC,IAAI,CAACxB,WAAW,CAACC,QAAQ,CAACsB,SAAS,CAAC;;MAExC,CAAC,CAAC;;EAER;EAEA;EACA5B,YAAYA,CAAC8B,OAAO,EAAED,QAAS;IAC7B,IAAIE,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;IACpCJ,QAAQ,GAAGA,QAAQ,IAAI,SAASE,SAAS,EAAE;IAC3C,IAAIG,GAAG,GAAGJ,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC;MAC1BC,IAAI,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;MACjCC,IAAI,GAAGC,IAAI,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC;MACnBM,CAAC,GAAGF,IAAI,CAACG,MAAM;MACfC,KAAK,GAAG,IAAIC,UAAU,CAACH,CAAC,CAAC;IAC3B,OAAOA,CAAC,EAAE,EAAE;MACVE,KAAK,CAACF,CAAC,CAAC,GAAGF,IAAI,CAACM,UAAU,CAACJ,CAAC,CAAC;;IAE/BX,QAAQ,GAAGA,QAAQ,GAAG,GAAG,GAAGO,IAAI,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,IAAIU,IAAI,CAAC,CAACH,KAAK,CAAC,EAAEb,QAAQ,EAAE;MAAEjC,IAAI,EAAEwC;IAAI,CAAE,CAAC;EACpD;EAYA;EACA,IAAI7D,SAASA,CAAA;IACX,OACE,IAAI,CAAC8B,WAAW,CAACyC,OAAO,KACvB,IAAI,CAACzC,WAAW,CAAC0C,KAAK,IAAI,IAAI,CAAC1C,WAAW,CAAC2C,OAAO,IAAI,IAAI,CAAChE,SAAS,CAAC;EAE1E;EAEAiE,QAAQA,CAAA;IACN,IAAI,CAACzF,KAAK,CAACI,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACJ,KAAK,CAACI,KAAK,CAACwC,cAAc,CACpE,kBAAkB,CACnB,GACG,IAAI,CAAC5C,KAAK,CAACI,KAAK,CAACsF,gBAAgB,GACjC,IAAI;IAER,IAAI,CAACzE,SAAS,GAAG,IAAI,CAAC4B,WAAW,CAACI,KAAK;IAEvC,IAAI,CAACJ,WAAW,CAAC8C,YAAY,CAAC1D,SAAS,CAAEgB,KAAK,IAAI;MAChD,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACxB,QAAQ,GAAG,KAAK;;MAGvB,IAAI,IAAI,CAACC,WAAW,EAAE;QACpB,IAAI,CAACT,SAAS,GAAG,IAAI,CAACS,WAAW;QACjC,IAAI,CAACA,WAAW,GAAG,IAAI;OACxB,MAAM;QACL,IAAI,CAACT,SAAS,GAAGgC,KAAK;;MAExB;IACF,CAAC,CAAC;;IACF,IAAI,IAAI,CAACjD,KAAK,CAACI,KAAK,CAACwC,cAAc,CAAC,QAAQ,CAAC,EAAE;MAC7C;MACA,IAAI,CAACjB,WAAW,GAAG;QAAE,GAAG,IAAI,CAACA,WAAW;QAAE,GAAG,IAAI,CAAC3B,KAAK,CAACI,KAAK,CAACwF;MAAM,CAAE;;EAE1E;EAEAC,WAAWA,CAAA;IACT,OAAQ,IAAI,CAAC5E,SAAS,GAAG,IAAI;EAC/B;EAAC,QAAA6E,CAAA;qBAjJU3E,yBAAyB,EAAAlC,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAAK,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA;UAAzBnF,yBAAyB;IAAAoF,SAAA;IAAAC,QAAA,GAAAvH,EAAA,CAAAwH,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbtC9H,EAAA,CAAAC,cAAA,UAAK;QAGyDD,EAAA,CAAAE,UAAA,0BAAA8H,mFAAA5H,MAAA;UAAA,OAAgB2H,GAAA,CAAA7E,aAAA,CAAA9C,MAAA,CAAqB;QAAA,EAAC;QAGrCJ,EAAA,CAAAY,YAAA,EAA0B;QAGvFZ,EAAA,CAAAiI,UAAA,IAAAC,0CAAA,mBACqE;QACrElI,EAAA,CAAAC,cAAA,WAAe;QACbD,EAAA,CAAAiI,UAAA,IAAAE,0CAAA,mBAEQ;QACVnI,EAAA,CAAAY,YAAA,EAAI;QACJZ,EAAA,CAAAiI,UAAA,IAAAG,wCAAA,iBAKM;QACRpI,EAAA,CAAAY,YAAA,EAAM;;;QAnByBZ,EAAA,CAAAiB,SAAA,GAA8B;QAA9BjB,EAAA,CAAA+B,UAAA,mBAAAgG,GAAA,CAAArF,WAAA,CAA8B,aAAAqF,GAAA,CAAArE,EAAA,CAAA2E,QAAA,iBAAAN,GAAA,CAAAhH,KAAA,CAAAI,KAAA,CAAAmH,WAAA,GAAAP,GAAA,CAAAhH,KAAA,CAAAI,KAAA,CAAAmH,WAAA,mBAAAP,GAAA,CAAAhH,KAAA,CAAAI,KAAA,CAAAoH,MAAA,GAAAR,GAAA,CAAAhH,KAAA,CAAAI,KAAA,CAAAoH,MAAA,kCAAAR,GAAA,CAAAhH,KAAA,CAAAI,KAAA,CAAAsF,gBAAA;QAMnDzG,EAAA,CAAAiB,SAAA,GAAsB;QAAtBjB,EAAA,CAAA+B,UAAA,SAAAgG,GAAA,CAAAhH,KAAA,CAAAI,KAAA,CAAAC,IAAA,CAAsB;QAGmCpB,EAAA,CAAAiB,SAAA,GAA6B;QAA7BjB,EAAA,CAAA+B,UAAA,UAAAgG,GAAA,CAAAhI,WAAA,CAAA0D,UAAA,CAA6B;QAIhDzD,EAAA,CAAAiB,SAAA,GAAe;QAAfjB,EAAA,CAAA+B,UAAA,SAAAgG,GAAA,CAAA/F,SAAA,CAAe", "names": ["FieldType", "environment", "i0", "ɵɵelementStart", "ɵɵlistener", "ImageCropperTypeComponent_small_4_Template_small_click_0_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onClick", "ɵɵtext", "ɵɵelementEnd", "ɵɵpropertyInterpolate1", "ctx_r0", "field", "key", "ɵɵadvance", "ɵɵtextInterpolate", "props", "note", "ImageCropperTypeComponent_small_6_Template_small_click_0_listener", "_r6", "ctx_r5", "ctx_r1", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵelement", "ɵɵstyleProp", "ctx_r2", "isInvalid", "ɵɵproperty", "image_src", "ɵɵsanitizeUrl", "ImageCropperTypeComponent", "constructor", "_http", "_loadingService", "ngForm", "submitted", "uploaded", "newImageURL", "config<PERSON><PERSON><PERSON>", "width", "height", "resizableArea", "output", "ngSubmit", "subscribe", "event", "onCropedPhoto", "type", "preview", "file", "data", "base64ToFile", "submitFile", "production", "to", "hasOwnProperty", "formControl", "setValue", "test_image", "target", "value", "formData", "FormData", "append", "dir", "show", "upload_url", "apiUrl", "post", "res", "fieldErrors", "for<PERSON>ach", "fieldError", "name", "setErrors", "serverError", "status", "files", "url", "imagePath", "filename", "dataurl", "timestamp", "Date", "getTime", "arr", "split", "mime", "match", "bstr", "atob", "n", "length", "u8arr", "Uint8Array", "charCodeAt", "File", "invalid", "dirty", "touched", "ngOnInit", "useCropperDialog", "valueChanges", "config", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "LoadingService", "i3", "FormGroupDirective", "_2", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ImageCropperTypeComponent_Template", "rf", "ctx", "ImageCropperTypeComponent_Template_aui_cropper_with_dialog_croppedImage_3_listener", "ɵɵtemplate", "ImageCropperTypeComponent_small_4_Template", "ImageCropperTypeComponent_small_6_Template", "ImageCropperTypeComponent_div_7_Template", "disabled", "maxFileSize", "accept"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\image-cropper-type\\image-cropper-type.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\image-cropper-type\\image-cropper-type.component.html"], "sourcesContent": ["import { ImgCropperConfig } from '@alyle/ui/image-cropper';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { FormGroupDirective } from '@angular/forms';\r\nimport { FieldType, FieldTypeConfig } from '@ngx-formly/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-image-cropper-type',\r\n  templateUrl: './image-cropper-type.component.html',\r\n  styleUrls: ['./image-cropper-type.component.scss'],\r\n})\r\nexport class ImageCropperTypeComponent\r\n  extends FieldType<FieldTypeConfig>\r\n  implements OnInit\r\n{\r\n  constructor(\r\n    public _http: HttpClient,\r\n    public _loadingService: LoadingService,\r\n    private ngForm: FormGroupDirective\r\n  ) {\r\n    super();\r\n    ngForm.ngSubmit.subscribe((event) => {\r\n      this.submitted = true;\r\n    });\r\n  }\r\n\r\n  submitted = false;\r\n  uploaded = false;\r\n\r\n  public image_src = '';\r\n\r\n  private newImageURL: string | null = null;\r\n\r\n  environment = environment;\r\n  onCropedPhoto(event) {\r\n    let type = event.type;\r\n    this.image_src = event.preview;\r\n    let file = type == 'file' ? event.data : this.base64ToFile(event.data);\r\n    this.submitFile(file);\r\n  }\r\n\r\n  onClick($event) {\r\n    // if environment is local, then set the value to assets/images/logo/ezactive_1024x1024.png\r\n    if (environment.production === false) {\r\n      this.uploaded = true;\r\n      if (this.to.hasOwnProperty('test_image')) {\r\n        this.formControl.setValue(this.to.test_image);\r\n      } else {\r\n        this.formControl.setValue('assets/images/ico/icon-72x72.png');\r\n      }\r\n      // close browser file dialog\r\n      $event.target.value = null;\r\n    }\r\n  }\r\n\r\n  submitFile(file) {\r\n    const formData = new FormData();\r\n    formData.append('upload', file);\r\n    formData.append('uploadField', this.field.key as string);\r\n    formData.append('dir', this.field.props.dir as string);\r\n    formData.append('action', 'upload');\r\n    this._loadingService.show();\r\n    this.field.props.upload_url = this.field.props.upload_url\r\n      ? this.field.props.upload_url\r\n      : `${environment.apiUrl}/s3`; // fix after\r\n    if (this.field.props.hasOwnProperty('upload_url')) {\r\n      this._http\r\n        .post(this.field.props.upload_url, formData)\r\n        .subscribe((res: any) => {\r\n          this.uploaded = true;\r\n          if (res.hasOwnProperty('fieldErrors')) {\r\n            res.fieldErrors.forEach((fieldError: any) => {\r\n              if (fieldError.name === this.field.key) {\r\n                this.formControl.setErrors({ serverError: fieldError.status });\r\n              }\r\n            });\r\n          } else if (res.hasOwnProperty('files')) {\r\n            // let files = res.files.files;\r\n            // let key_file = Object.keys(files)[0];\r\n            // let url = files[key_file].url;\r\n            //this.formControl.setValue(url);\r\n\r\n            this.newImageURL = res.files.url;\r\n            this.image_src = this.newImageURL;\r\n\r\n            let imagePath = res.files.filename;\r\n            this.formControl.setValue(imagePath);\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  // convert base64 to file\r\n  base64ToFile(dataurl, filename?) {\r\n    let timestamp = new Date().getTime();\r\n    filename = filename || `image_${timestamp}`;\r\n    var arr = dataurl.split(','),\r\n      mime = arr[0].match(/:(.*?);/)[1],\r\n      bstr = atob(arr[1]),\r\n      n = bstr.length,\r\n      u8arr = new Uint8Array(n);\r\n    while (n--) {\r\n      u8arr[n] = bstr.charCodeAt(n);\r\n    }\r\n    filename = filename + '.' + mime.split('/')[1];\r\n    return new File([u8arr], filename, { type: mime });\r\n  }\r\n\r\n  configPhoto: ImgCropperConfig = {\r\n    width: 250,\r\n    height: 250,\r\n    resizableArea: false,\r\n    output: {\r\n      width: 250,\r\n      height: 250,\r\n    },\r\n  };\r\n\r\n  // check field is invalid\r\n  get isInvalid() {\r\n    return (\r\n      this.formControl.invalid &&\r\n      (this.formControl.dirty || this.formControl.touched || this.submitted)\r\n    );\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.field.props['useCropperDialog'] = this.field.props.hasOwnProperty(\r\n      'useCropperDialog'\r\n    )\r\n      ? this.field.props.useCropperDialog\r\n      : true;\r\n\r\n    this.image_src = this.formControl.value;\r\n\r\n    this.formControl.valueChanges.subscribe((value) => {\r\n      if (!value) {\r\n        this.uploaded = false;\r\n      }\r\n\r\n      if (this.newImageURL) {\r\n        this.image_src = this.newImageURL;\r\n        this.newImageURL = null;\r\n      } else {\r\n        this.image_src = value;\r\n      }\r\n      // this.image_src = value;\r\n    });\r\n    if (this.field.props.hasOwnProperty('config')) {\r\n      // merge config\r\n      this.configPhoto = { ...this.configPhoto, ...this.field.props.config };\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    return (this.image_src = null);\r\n  }\r\n}\r\n", "<div>\r\n  <div class=\"row\">\r\n    <div class=\"col-auto\">\r\n      <aui-cropper-with-dialog [cropper_config]=\"configPhoto\" (croppedImage)=\"onCropedPhoto($event)\"\r\n        [disabled]=\"to.disabled\" [maxFileSize]=\"this.field.props.maxFileSize?this.field.props.maxFileSize: 5000\"\r\n        [accept]=\"this.field.props.accept?this.field.props.accept:'image/*' \"\r\n        [useCropperDialog]=\"this.field.props.useCropperDialog\"></aui-cropper-with-dialog>\r\n    </div>\r\n  </div>\r\n  <small *ngIf=\"field.props.note\" id=\"note_{{field.key}}\" (click)=\"onClick($event)\"\r\n    class=\"text-muted\">{{field.props.note?field.props.note:''}}</small>\r\n  <p class=\"m-0\">\r\n    <small id=\"image_test{{field.key}}\" (click)=\"onClick($event)\" *ngIf=\"!environment.production\">\r\n      {{'Click here to show image for testing' | translate}}\r\n    </small>\r\n  </p>\r\n  <div class=\"row justify-content-center mt-1\" *ngIf=\"image_src\">\r\n    <div class=\"col-auto\">\r\n      <img [src]=\"image_src\" style=\"max-width: 250px;\" class=\"img-fluid\" alt=\"\"\r\n        [style.border]=\"isInvalid?'1px solid red':''\">\r\n    </div>\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LeagueComponent } from './league/league.component';\nimport { LeagueReportsComponent } from './league-reports/league-reports.component';\nimport { RouterModule } from '@angular/router';\nimport { NgbAccordionModule, NgbCollapseModule, NgbDropdownModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CoreSidebarModule } from '@core/components';\nimport { CoreCommonModule } from '@core/common.module';\nimport { DataTablesModule } from 'angular-datatables';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\nimport { StagesComponent } from './stages/stages.component';\nimport { StageDetailsComponent } from './stages/stage-details/stage-details.component';\nimport { FormlyModule } from '@ngx-formly/core';\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\nimport { NumberTypeComponent } from 'app/components/number-type/number-type.component';\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\nimport { DetailsWrapperComponent } from './stages/stage-details/details-wraper.component';\nimport { StageTablesComponent } from './stages/stage-tables/stage-tables.component';\nimport { StageTeamsComponent } from './stages/stage-teams/stage-teams.component';\nimport { ModalAddGroupTeamComponent } from './stages/stage-teams/modal-add-group-team/modal-add-group-team.component';\nimport { StageMatchesComponent } from './stages/stage-matches/stage-matches.component';\nimport { LeagueMatchesComponent } from './league-matches/league-matches.component';\nimport { ScrollableTabsModule } from 'app/components/scrollable-tabs/scrollable-tabs.module';\nimport { UpadateMatchesComponent } from './league-matches/update-matches/update-matches.component';\nimport { ModalUpdateScoreComponent } from './modal-update-score/modal-update-score.component';\nimport { UpdateMatchDetailsComponent } from './modal-update-match-details/update-match-details.component';\nimport { FixturesResultsComponent } from './fixtures-results/fixtures-results.component';\nimport { FixturesComponent } from './fixtures-results/fixtures/fixtures.component';\nimport { MatchCardModule } from 'app/components/match-card/match-card.module';\nimport { MatchesDetailsComponent } from './fixtures-results/matches-details/matches-details.component';\nimport { MatchDetailsGuard } from 'app/guards/match-details.guard';\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\nimport { AppConfig } from 'app/app-config';\nimport { RowMatchModule } from 'app/components/row-match/row-match.module';\nimport { TournamentSelectionComponent } from './fixtures-results/tournament-selection/tournament-selection.component';\nimport { TeamFixturesComponent } from './fixtures-results/team-fixtures/team-fixtures.component';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport { ModalManageRefereesComponent } from './league/modal-manage-referees/modal-manage-referees.component';\nimport { ModalAssignRefereesComponent } from './stages/stage-matches/modal-assign-referees/modal-assign-referees.component';\nimport { ModalAddNumberTeamComponent } from './stages/stage-teams/modal-add-number-team/modal-add-number-team.component';\nimport { AutoScheduleComponent } from './auto-schedule/auto-schedule.component';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { ModalSetupScheduleComponent } from './auto-schedule/modal-setup-schedule/modal-setup-schedule.component';\nimport { ModalUpdateConfigComponent } from './auto-schedule/modal-update-config/modal-update-config.component';\nimport { ModalCrudBreakComponent } from './auto-schedule/modal-crud-break/modal-crud-break.component';\nimport { ModalUpdateMatchComponent } from './auto-schedule/modal-update-match/modal-update-match.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-formly/core\";\nconst routes = [{\n  path: 'manage',\n  component: LeagueComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_leagues\n  }\n}, {\n  path: 'manage/:tournament_id/auto-schedule',\n  component: AutoScheduleComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_leagues\n  }\n}, {\n  path: 'manage/:tournament_id/stages/:stage_id',\n  component: StagesComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.manage_leagues\n  }\n}, {\n  path: 'reports',\n  component: LeagueReportsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.league_reports\n  }\n}, {\n  path: 'matches',\n  component: LeagueMatchesComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.update_score\n  }\n}, {\n  path: 'matches/:match_id/update-details',\n  component: UpadateMatchesComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.update_score\n  }\n}, {\n  path: 'matches/:match_id/details',\n  component: MatchesDetailsComponent,\n  canActivate: [MatchDetailsGuard]\n}, {\n  path: 'fixtures-results/:tournament_id',\n  component: FixturesResultsComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.fixtures_results\n  }\n}, {\n  path: 'fixtures-results/team/:team_id',\n  component: TeamFixturesComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.fixtures_results\n  }\n}, {\n  path: 'select-tournament',\n  component: TournamentSelectionComponent,\n  canActivate: [PermissionsGuard],\n  data: {\n    permissions: AppConfig.PERMISSIONS.fixtures_results\n  }\n}];\nexport class LeagueTournamentModule {\n  static #_ = this.ɵfac = function LeagueTournamentModule_Factory(t) {\n    return new (t || LeagueTournamentModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LeagueTournamentModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [HostListenersModule, CoreCommonModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, RouterModule.forChild(routes), ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, CorePipesModule, DataTablesModule, NgSelectModule, BtnDropdownActionModule, CoreTouchspinModule, ScrollableTabsModule, NgbCollapseModule, FormlyBootstrapModule, MatchCardModule, RowMatchModule, EditorSidebarModule, DragDropModule, FormlyModule.forRoot({\n      validationMessages: [{\n        name: 'serverError',\n        message: serverValidationMessage\n      }],\n      wrappers: [{\n        name: 'details',\n        component: DetailsWrapperComponent\n      }]\n    })]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LeagueTournamentModule, {\n    declarations: [LeagueComponent, StagesComponent, LeagueReportsComponent, StageDetailsComponent, NumberTypeComponent, DetailsWrapperComponent, StageTablesComponent, StageTeamsComponent, ModalAddGroupTeamComponent, StageMatchesComponent, LeagueMatchesComponent, UpadateMatchesComponent, MatchesDetailsComponent, ModalUpdateScoreComponent, UpdateMatchDetailsComponent, FixturesResultsComponent, FixturesComponent, TournamentSelectionComponent, TeamFixturesComponent, ModalManageRefereesComponent, ModalAssignRefereesComponent, ModalAddNumberTeamComponent, AutoScheduleComponent, ModalSetupScheduleComponent, ModalUpdateConfigComponent, ModalCrudBreakComponent, ModalUpdateMatchComponent],\n    imports: [HostListenersModule, CoreCommonModule, NgbAccordionModule, NgbModule, NgbDropdownModule, CommonModule, i1.RouterModule, ContentHeaderModule, FormsModule, ReactiveFormsModule, ErrorMessageModule, TranslateModule, CoreSidebarModule, CoreCommonModule, CorePipesModule, DataTablesModule, NgSelectModule, BtnDropdownActionModule, CoreTouchspinModule, ScrollableTabsModule, NgbCollapseModule, FormlyBootstrapModule, MatchCardModule, RowMatchModule, EditorSidebarModule, DragDropModule, i2.FormlyModule],\n    exports: [LeagueComponent, StagesComponent, LeagueReportsComponent, NumberTypeComponent, ModalUpdateScoreComponent, UpdateMatchDetailsComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,SAAS,QAAQ,4BAA4B;AAChH,SAASC,mBAAmB,QAAQ,4DAA4D;AAChG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,mBAAmB,EAAEC,uBAAuB,QAAQ,qDAAqD;AAClH,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,uBAAuB,QAAQ,iDAAiD;AACzF,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,0BAA0B,QAAQ,0EAA0E;AACrH,SAASC,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,2BAA2B,QAAQ,6DAA6D;AACzG,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,iBAAiB,QAAQ,gDAAgD;AAClF,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,uBAAuB,QAAQ,8DAA8D;AACtG,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,4BAA4B,QAAQ,wEAAwE;AACrH,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,4BAA4B,QAAQ,gEAAgE;AAC7G,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,2BAA2B,QAAQ,4EAA4E;AACxH,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,0BAA0B,QAAQ,mEAAmE;AAC9G,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,yBAAyB,QAAQ,iEAAiE;;;;AAG3G,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE1D,eAAe;EAC1B2D,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACC;EAAc;CAC1D,EACD;EACEN,IAAI,EAAE,qCAAqC;EAC3CC,SAAS,EAAER,qBAAqB;EAChCS,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACC;EAAc;CAC1D,EACD;EACEN,IAAI,EAAE,wCAAwC;EAC9CC,SAAS,EAAEvC,eAAe;EAC1BwC,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACC;EAAc;CAC1D,EACD;EACEN,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEzD,sBAAsB;EACjC0D,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACE;EAAc;CAC1D,EACD;EACEP,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE5B,sBAAsB;EACjC6B,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACG;EAAY;CACxD,EACD;EACER,IAAI,EAAE,kCAAkC;EACxCC,SAAS,EAAE1B,uBAAuB;EAClC2B,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACG;EAAY;CACxD,EACD;EACER,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEpB,uBAAuB;EAClCqB,WAAW,EAAE,CAACpB,iBAAiB;CAChC,EACD;EACEkB,IAAI,EAAE,iCAAiC;EACvCC,SAAS,EAAEvB,wBAAwB;EACnCwB,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACI;EAAgB;CAC5D,EACD;EACET,IAAI,EAAE,gCAAgC;EACtCC,SAAS,EAAEb,qBAAqB;EAChCc,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACI;EAAgB;CAC5D,EACD;EACET,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEd,4BAA4B;EACvCe,WAAW,EAAE,CAAClB,gBAAgB,CAAC;EAC/BmB,IAAI,EAAE;IAAEC,WAAW,EAAEnB,SAAS,CAACoB,WAAW,CAACI;EAAgB;CAC5D,CACF;AA2ED,OAAM,MAAOC,sBAAsB;EAAA,QAAAC,CAAA;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA;UAAtBF;EAAsB;EAAA,QAAAG,EAAA;cA1C/B9B,mBAAmB,EACnB3B,gBAAgB,EAChBV,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBN,YAAY,EACZG,YAAY,CAACqE,QAAQ,CAACf,MAAM,CAAC,EAC7BjD,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBiC,eAAe,EACfhC,gBAAgB,EAChBC,cAAc,EACdG,uBAAuB,EACvBM,mBAAmB,EACnBO,oBAAoB,EACpB3B,iBAAiB,EACjBkB,qBAAqB,EACrBe,eAAe,EACfM,cAAc,EACd3B,mBAAmB,EACnBmC,cAAc,EACd9B,YAAY,CAACmD,OAAO,CAAC;MACnBC,kBAAkB,EAAE,CAClB;QAAEC,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE1D;MAAuB,CAAE,CAC1D;MACD2D,QAAQ,EAAE,CAAC;QAAEF,IAAI,EAAE,SAAS;QAAEhB,SAAS,EAAEjC;MAAuB,CAAE;KACnE,CAAC;EAAA;;;2EAWO0C,sBAAsB;IAAAU,YAAA,GAvE/B7E,eAAe,EACfmB,eAAe,EACflB,sBAAsB,EACtBmB,qBAAqB,EACrBG,mBAAmB,EACnBE,uBAAuB,EACvBC,oBAAoB,EACpBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,qBAAqB,EACrBC,sBAAsB,EACtBE,uBAAuB,EACvBM,uBAAuB,EACvBL,yBAAyB,EACzBC,2BAA2B,EAC3BC,wBAAwB,EACxBC,iBAAiB,EACjBQ,4BAA4B,EAC5BC,qBAAqB,EACrBE,4BAA4B,EAC5BC,4BAA4B,EAC5BC,2BAA2B,EAC3BC,qBAAqB,EACrBE,2BAA2B,EAC3BC,0BAA0B,EAC1BC,uBAAuB,EACvBC,yBAAyB;IAAAuB,OAAA,GAGzBtC,mBAAmB,EACnB3B,gBAAgB,EAChBV,kBAAkB,EAClBG,SAAS,EACTD,iBAAiB,EACjBN,YAAY,EAAAgF,EAAA,CAAA7E,YAAA,EAEZK,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,gBAAgB,EAChBiC,eAAe,EACfhC,gBAAgB,EAChBC,cAAc,EACdG,uBAAuB,EACvBM,mBAAmB,EACnBO,oBAAoB,EACpB3B,iBAAiB,EACjBkB,qBAAqB,EACrBe,eAAe,EACfM,cAAc,EACd3B,mBAAmB,EACnBmC,cAAc,EAAA6B,EAAA,CAAA3D,YAAA;IAAA4D,OAAA,GASdjF,eAAe,EACfmB,eAAe,EACflB,sBAAsB,EACtBsB,mBAAmB,EACnBU,yBAAyB,EACzBC,2BAA2B;EAAA;AAAA", "names": ["CommonModule", "LeagueComponent", "LeagueReportsComponent", "RouterModule", "NgbAccordionModule", "NgbCollapseModule", "NgbDropdownModule", "NgbModule", "ContentHeaderModule", "FormsModule", "ReactiveFormsModule", "ErrorMessageModule", "TranslateModule", "CoreSidebarModule", "CoreCommonModule", "DataTablesModule", "NgSelectModule", "EditorSidebarModule", "serverValidationMessage", "BtnDropdownActionModule", "StagesComponent", "StageDetailsComponent", "FormlyModule", "FormlyBootstrapModule", "NumberTypeComponent", "CoreTouchspinModule", "DetailsWrapperComponent", "StageTablesComponent", "StageTeamsComponent", "ModalAddGroupTeamComponent", "StageMatchesComponent", "LeagueMatchesComponent", "ScrollableTabsModule", "UpadateMatchesComponent", "ModalUpdateScoreComponent", "UpdateMatchDetailsComponent", "FixturesResultsComponent", "FixturesComponent", "MatchCardModule", "MatchesDetailsComponent", "MatchDetailsGuard", "HostListenersModule", "PermissionsGuard", "AppConfig", "RowMatchModule", "TournamentSelectionComponent", "TeamFixturesComponent", "CorePipesModule", "ModalManageRefereesComponent", "ModalAssignRefereesComponent", "ModalAddNumberTeamComponent", "AutoScheduleComponent", "DragDropModule", "ModalSetupScheduleComponent", "ModalUpdateConfigComponent", "ModalCrudBreakComponent", "ModalUpdateMatchComponent", "routes", "path", "component", "canActivate", "data", "permissions", "PERMISSIONS", "manage_leagues", "league_reports", "update_score", "fixtures_results", "LeagueTournamentModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "forRoot", "validationMessages", "name", "message", "wrappers", "declarations", "imports", "i1", "i2", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-tournament.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { LeagueComponent } from './league/league.component';\r\nimport { LeagueReportsComponent } from './league-reports/league-reports.component';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { NgbAccordionModule, NgbCollapseModule, NgbDropdownModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { CoreSidebarModule } from '@core/components';\r\nimport { CoreCommonModule } from '@core/common.module';\r\nimport { DataTablesModule } from 'angular-datatables';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';\r\nimport { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';\r\nimport { StagesComponent } from './stages/stages.component';\r\nimport { StageDetailsComponent } from './stages/stage-details/stage-details.component';\r\nimport { FormlyModule } from '@ngx-formly/core';\r\nimport { FormlyBootstrapModule } from '@ngx-formly/bootstrap';\r\nimport { NumberTypeComponent } from 'app/components/number-type/number-type.component';\r\nimport { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';\r\nimport { DetailsWrapperComponent } from './stages/stage-details/details-wraper.component';\r\nimport { StageTablesComponent } from './stages/stage-tables/stage-tables.component';\r\nimport { StageTeamsComponent } from './stages/stage-teams/stage-teams.component';\r\nimport { ModalAddGroupTeamComponent } from './stages/stage-teams/modal-add-group-team/modal-add-group-team.component';\r\nimport { StageMatchesComponent } from './stages/stage-matches/stage-matches.component';\r\nimport { LeagueMatchesComponent } from './league-matches/league-matches.component';\r\nimport { ScrollableTabsModule } from 'app/components/scrollable-tabs/scrollable-tabs.module';\r\nimport { UpadateMatchesComponent } from './league-matches/update-matches/update-matches.component';\r\nimport { ModalUpdateScoreComponent } from './modal-update-score/modal-update-score.component';\r\nimport { UpdateMatchDetailsComponent } from './modal-update-match-details/update-match-details.component';\r\nimport { FixturesResultsComponent } from './fixtures-results/fixtures-results.component';\r\nimport { FixturesComponent } from './fixtures-results/fixtures/fixtures.component';\r\nimport { MatchCardModule } from 'app/components/match-card/match-card.module';\r\nimport { MatchesDetailsComponent } from './fixtures-results/matches-details/matches-details.component';\r\nimport { MatchDetailsGuard } from 'app/guards/match-details.guard';\r\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\r\nimport { PermissionsGuard } from 'app/guards/permissions.guard';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { RowMatchModule } from 'app/components/row-match/row-match.module';\r\nimport { TournamentSelectionComponent } from './fixtures-results/tournament-selection/tournament-selection.component';\r\nimport { TeamFixturesComponent } from './fixtures-results/team-fixtures/team-fixtures.component';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\nimport { ModalManageRefereesComponent } from './league/modal-manage-referees/modal-manage-referees.component';\r\nimport { ModalAssignRefereesComponent } from './stages/stage-matches/modal-assign-referees/modal-assign-referees.component';\r\nimport { ModalAddNumberTeamComponent } from './stages/stage-teams/modal-add-number-team/modal-add-number-team.component';\r\nimport { AutoScheduleComponent } from './auto-schedule/auto-schedule.component';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport { ModalSetupScheduleComponent } from './auto-schedule/modal-setup-schedule/modal-setup-schedule.component';\r\nimport { ModalUpdateConfigComponent } from './auto-schedule/modal-update-config/modal-update-config.component';\r\nimport { ModalCrudBreakComponent } from './auto-schedule/modal-crud-break/modal-crud-break.component';\r\nimport { ModalUpdateMatchComponent } from './auto-schedule/modal-update-match/modal-update-match.component';\r\n\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'manage',\r\n    component: LeagueComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_leagues }\r\n  },\r\n  {\r\n    path: 'manage/:tournament_id/auto-schedule',\r\n    component: AutoScheduleComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_leagues }\r\n  },\r\n  {\r\n    path: 'manage/:tournament_id/stages/:stage_id',\r\n    component: StagesComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.manage_leagues }\r\n  },\r\n  {\r\n    path: 'reports',\r\n    component: LeagueReportsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.league_reports }\r\n  },\r\n  {\r\n    path: 'matches',\r\n    component: LeagueMatchesComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.update_score }\r\n  },\r\n  {\r\n    path: 'matches/:match_id/update-details',\r\n    component: UpadateMatchesComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.update_score }\r\n  },\r\n  {\r\n    path: 'matches/:match_id/details',\r\n    component: MatchesDetailsComponent,\r\n    canActivate: [MatchDetailsGuard]\r\n  },\r\n  {\r\n    path: 'fixtures-results/:tournament_id',\r\n    component: FixturesResultsComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.fixtures_results }\r\n  },\r\n  {\r\n    path: 'fixtures-results/team/:team_id',\r\n    component: TeamFixturesComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.fixtures_results }\r\n  },\r\n  {\r\n    path: 'select-tournament',\r\n    component: TournamentSelectionComponent,\r\n    canActivate: [PermissionsGuard],\r\n    data: { permissions: AppConfig.PERMISSIONS.fixtures_results }\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  declarations: [\r\n    LeagueComponent,\r\n    StagesComponent,\r\n    LeagueReportsComponent,\r\n    StageDetailsComponent,\r\n    NumberTypeComponent,\r\n    DetailsWrapperComponent,\r\n    StageTablesComponent,\r\n    StageTeamsComponent,\r\n    ModalAddGroupTeamComponent,\r\n    StageMatchesComponent,\r\n    LeagueMatchesComponent,\r\n    UpadateMatchesComponent,\r\n    MatchesDetailsComponent,\r\n    ModalUpdateScoreComponent,\r\n    UpdateMatchDetailsComponent,\r\n    FixturesResultsComponent,\r\n    FixturesComponent,\r\n    TournamentSelectionComponent,\r\n    TeamFixturesComponent,\r\n    ModalManageRefereesComponent,\r\n    ModalAssignRefereesComponent,\r\n    ModalAddNumberTeamComponent,\r\n    AutoScheduleComponent,\r\n    ModalSetupScheduleComponent,\r\n    ModalUpdateConfigComponent,\r\n    ModalCrudBreakComponent,\r\n    ModalUpdateMatchComponent\r\n  ],\r\n  imports: [\r\n    HostListenersModule,\r\n    CoreCommonModule,\r\n    NgbAccordionModule,\r\n    NgbModule,\r\n    NgbDropdownModule,\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    ContentHeaderModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ErrorMessageModule,\r\n    TranslateModule,\r\n    CoreSidebarModule,\r\n    CoreCommonModule,\r\n    CorePipesModule,\r\n    DataTablesModule,\r\n    NgSelectModule,\r\n    BtnDropdownActionModule,\r\n    CoreTouchspinModule,\r\n    ScrollableTabsModule,\r\n    NgbCollapseModule,\r\n    FormlyBootstrapModule,\r\n    MatchCardModule,\r\n    RowMatchModule,\r\n    EditorSidebarModule,\r\n    DragDropModule,\r\n    FormlyModule.forRoot({\r\n      validationMessages: [\r\n        { name: 'serverError', message: serverValidationMessage }\r\n      ],\r\n      wrappers: [{ name: 'details', component: DetailsWrapperComponent }]\r\n    })\r\n  ],\r\n  exports: [\r\n    LeagueComponent,\r\n    StagesComponent,\r\n    LeagueReportsComponent,\r\n    NumberTypeComponent,\r\n    ModalUpdateScoreComponent,\r\n    UpdateMatchDetailsComponent\r\n  ]\r\n})\r\nexport class LeagueTournamentModule {\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
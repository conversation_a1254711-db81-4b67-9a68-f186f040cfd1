{"ast": null, "code": "/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\nimport areConnectedThroughProperties from './utils/areconnectedthroughproperties';\nimport Watchdog from './watchdog';\nimport { throttle, cloneDeepWith, isElement } from 'lodash-es';\n/**\n * A watchdog for CKEditor 5 editors.\n *\n * See the {@glink features/watchdog Watchdog feature guide} to learn the rationale behind it and\n * how to use it.\n */\nexport default class EditorWatchdog extends Watchdog {\n  /**\n   * @param Editor The editor class.\n   * @param watchdogConfig The watchdog plugin configuration.\n   */\n  constructor(Editor, watchdogConfig = {}) {\n    super(watchdogConfig);\n    /**\n     * The current editor instance.\n     */\n    this._editor = null;\n    // this._editorClass = Editor;\n    this._throttledSave = throttle(this._save.bind(this), typeof watchdogConfig.saveInterval === 'number' ? watchdogConfig.saveInterval : 5000);\n    // Set default creator and destructor functions:\n    if (Editor) {\n      this._creator = (elementOrData, config) => Editor.create(elementOrData, config);\n    }\n    this._destructor = editor => editor.destroy();\n  }\n  /**\n   * The current editor instance.\n   */\n  get editor() {\n    return this._editor;\n  }\n  /**\n   * @internal\n   */\n  get _item() {\n    return this._editor;\n  }\n  /**\n   * Sets the function that is responsible for the editor creation.\n   * It expects a function that should return a promise.\n   *\n   * ```ts\n   * watchdog.setCreator( ( element, config ) => ClassicEditor.create( element, config ) );\n   * ```\n   */\n  setCreator(creator) {\n    this._creator = creator;\n  }\n  /**\n   * Sets the function that is responsible for the editor destruction.\n   * Overrides the default destruction function, which destroys only the editor instance.\n   * It expects a function that should return a promise or `undefined`.\n   *\n   * ```ts\n   * watchdog.setDestructor( editor => {\n   * \t// Do something before the editor is destroyed.\n   *\n   * \treturn editor\n   * \t\t.destroy()\n   * \t\t.then( () => {\n   * \t\t\t// Do something after the editor is destroyed.\n   * \t\t} );\n   * } );\n   * ```\n   */\n  setDestructor(destructor) {\n    this._destructor = destructor;\n  }\n  /**\n   * Restarts the editor instance. This method is called whenever an editor error occurs. It fires the `restart` event and changes\n   * the state to `initializing`.\n   *\n   * @fires restart\n   */\n  _restart() {\n    return Promise.resolve().then(() => {\n      this.state = 'initializing';\n      this._fire('stateChange');\n      return this._destroy();\n    }).catch(err => {\n      console.error('An error happened during the editor destroying.', err);\n    }).then(() => {\n      if (typeof this._elementOrData === 'string') {\n        return this.create(this._data, this._config, this._config.context);\n      } else {\n        const updatedConfig = Object.assign({}, this._config, {\n          initialData: this._data\n        });\n        return this.create(this._elementOrData, updatedConfig, updatedConfig.context);\n      }\n    }).then(() => {\n      this._fire('restart');\n    });\n  }\n  /**\n   * Creates the editor instance and keeps it running, using the defined creator and destructor.\n   *\n   * @param elementOrData The editor source element or the editor data.\n   * @param config The editor configuration.\n   * @param context A context for the editor.\n   */\n  create(elementOrData = this._elementOrData, config = this._config, context) {\n    return Promise.resolve().then(() => {\n      super._startErrorHandling();\n      this._elementOrData = elementOrData;\n      // Clone configuration because it might be shared within multiple watchdog instances. Otherwise,\n      // when an error occurs in one of these editors, the watchdog will restart all of them.\n      this._config = this._cloneEditorConfiguration(config) || {};\n      this._config.context = context;\n      return this._creator(elementOrData, this._config);\n    }).then(editor => {\n      this._editor = editor;\n      editor.model.document.on('change:data', this._throttledSave);\n      this._lastDocumentVersion = editor.model.document.version;\n      this._data = this._getData();\n      this.state = 'ready';\n      this._fire('stateChange');\n    });\n  }\n  /**\n   * Destroys the watchdog and the current editor instance. It fires the callback\n   * registered in {@link #setDestructor `setDestructor()`} and uses it to destroy the editor instance.\n   * It also sets the state to `destroyed`.\n   */\n  destroy() {\n    return Promise.resolve().then(() => {\n      this.state = 'destroyed';\n      this._fire('stateChange');\n      super.destroy();\n      return this._destroy();\n    });\n  }\n  _destroy() {\n    return Promise.resolve().then(() => {\n      this._stopErrorHandling();\n      // Save data if there is a remaining editor data change.\n      this._throttledSave.flush();\n      const editor = this._editor;\n      this._editor = null;\n      // Remove the `change:data` listener before destroying the editor.\n      // Incorrectly written plugins may trigger firing `change:data` events during the editor destruction phase\n      // causing the watchdog to call `editor.getData()` when some parts of editor are already destroyed.\n      editor.model.document.off('change:data', this._throttledSave);\n      return this._destructor(editor);\n    });\n  }\n  /**\n   * Saves the editor data, so it can be restored after the crash even if the data cannot be fetched at\n   * the moment of the crash.\n   */\n  _save() {\n    const version = this._editor.model.document.version;\n    try {\n      this._data = this._getData();\n      this._lastDocumentVersion = version;\n    } catch (err) {\n      console.error(err, 'An error happened during restoring editor data. ' + 'Editor will be restored from the previously saved data.');\n    }\n  }\n  /**\n   * @internal\n   */\n  _setExcludedProperties(props) {\n    this._excludedProps = props;\n  }\n  /**\n   * Returns the editor data.\n   */\n  _getData() {\n    const data = {};\n    for (const rootName of this._editor.model.document.getRootNames()) {\n      data[rootName] = this._editor.data.get({\n        rootName\n      });\n    }\n    return data;\n  }\n  /**\n   * Traverses the error context and the current editor to find out whether these structures are connected\n   * to each other via properties.\n   *\n   * @internal\n   */\n  _isErrorComingFromThisItem(error) {\n    return areConnectedThroughProperties(this._editor, error.context, this._excludedProps);\n  }\n  /**\n   * Clones the editor configuration.\n   */\n  _cloneEditorConfiguration(config) {\n    return cloneDeepWith(config, (value, key) => {\n      // Leave DOM references.\n      if (isElement(value)) {\n        return value;\n      }\n      if (key === 'context') {\n        return value;\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["areConnectedThroughProperties", "Watchdog", "throttle", "cloneDeepWith", "isElement", "EditorWatchdog", "constructor", "Editor", "watchdogConfig", "_editor", "_throttledSave", "_save", "bind", "saveInterval", "_creator", "elementOrData", "config", "create", "_destructor", "editor", "destroy", "_item", "setCreator", "creator", "setDestructor", "destructor", "_restart", "Promise", "resolve", "then", "state", "_fire", "_destroy", "catch", "err", "console", "error", "_elementOrData", "_data", "_config", "context", "updatedConfig", "Object", "assign", "initialData", "_startErrorHandling", "_cloneEditorConfiguration", "model", "document", "on", "_lastDocumentVersion", "version", "_getData", "_stopErro<PERSON><PERSON><PERSON><PERSON>", "flush", "off", "_setExcludedProperties", "props", "_excludedProps", "data", "rootName", "getRootNames", "get", "_isErrorComingFromThisItem", "value", "key"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@ckeditor/ckeditor5-watchdog/src/editorwatchdog.js"], "sourcesContent": ["/**\n * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\nimport areConnectedThroughProperties from './utils/areconnectedthroughproperties';\nimport Watchdog from './watchdog';\nimport { throttle, cloneDeepWith, isElement } from 'lodash-es';\n/**\n * A watchdog for CKEditor 5 editors.\n *\n * See the {@glink features/watchdog Watchdog feature guide} to learn the rationale behind it and\n * how to use it.\n */\nexport default class EditorWatchdog extends Watchdog {\n    /**\n     * @param Editor The editor class.\n     * @param watchdogConfig The watchdog plugin configuration.\n     */\n    constructor(Editor, watchdogConfig = {}) {\n        super(watchdogConfig);\n        /**\n         * The current editor instance.\n         */\n        this._editor = null;\n        // this._editorClass = Editor;\n        this._throttledSave = throttle(this._save.bind(this), typeof watchdogConfig.saveInterval === 'number' ? watchdogConfig.saveInterval : 5000);\n        // Set default creator and destructor functions:\n        if (Editor) {\n            this._creator = ((elementOrData, config) => Editor.create(elementOrData, config));\n        }\n        this._destructor = editor => editor.destroy();\n    }\n    /**\n     * The current editor instance.\n     */\n    get editor() {\n        return this._editor;\n    }\n    /**\n     * @internal\n     */\n    get _item() {\n        return this._editor;\n    }\n    /**\n     * Sets the function that is responsible for the editor creation.\n     * It expects a function that should return a promise.\n     *\n     * ```ts\n     * watchdog.setCreator( ( element, config ) => ClassicEditor.create( element, config ) );\n     * ```\n     */\n    setCreator(creator) {\n        this._creator = creator;\n    }\n    /**\n     * Sets the function that is responsible for the editor destruction.\n     * Overrides the default destruction function, which destroys only the editor instance.\n     * It expects a function that should return a promise or `undefined`.\n     *\n     * ```ts\n     * watchdog.setDestructor( editor => {\n     * \t// Do something before the editor is destroyed.\n     *\n     * \treturn editor\n     * \t\t.destroy()\n     * \t\t.then( () => {\n     * \t\t\t// Do something after the editor is destroyed.\n     * \t\t} );\n     * } );\n     * ```\n     */\n    setDestructor(destructor) {\n        this._destructor = destructor;\n    }\n    /**\n     * Restarts the editor instance. This method is called whenever an editor error occurs. It fires the `restart` event and changes\n     * the state to `initializing`.\n     *\n     * @fires restart\n     */\n    _restart() {\n        return Promise.resolve()\n            .then(() => {\n            this.state = 'initializing';\n            this._fire('stateChange');\n            return this._destroy();\n        })\n            .catch(err => {\n            console.error('An error happened during the editor destroying.', err);\n        })\n            .then(() => {\n            if (typeof this._elementOrData === 'string') {\n                return this.create(this._data, this._config, this._config.context);\n            }\n            else {\n                const updatedConfig = Object.assign({}, this._config, {\n                    initialData: this._data\n                });\n                return this.create(this._elementOrData, updatedConfig, updatedConfig.context);\n            }\n        })\n            .then(() => {\n            this._fire('restart');\n        });\n    }\n    /**\n     * Creates the editor instance and keeps it running, using the defined creator and destructor.\n     *\n     * @param elementOrData The editor source element or the editor data.\n     * @param config The editor configuration.\n     * @param context A context for the editor.\n     */\n    create(elementOrData = this._elementOrData, config = this._config, context) {\n        return Promise.resolve()\n            .then(() => {\n            super._startErrorHandling();\n            this._elementOrData = elementOrData;\n            // Clone configuration because it might be shared within multiple watchdog instances. Otherwise,\n            // when an error occurs in one of these editors, the watchdog will restart all of them.\n            this._config = this._cloneEditorConfiguration(config) || {};\n            this._config.context = context;\n            return this._creator(elementOrData, this._config);\n        })\n            .then(editor => {\n            this._editor = editor;\n            editor.model.document.on('change:data', this._throttledSave);\n            this._lastDocumentVersion = editor.model.document.version;\n            this._data = this._getData();\n            this.state = 'ready';\n            this._fire('stateChange');\n        });\n    }\n    /**\n     * Destroys the watchdog and the current editor instance. It fires the callback\n     * registered in {@link #setDestructor `setDestructor()`} and uses it to destroy the editor instance.\n     * It also sets the state to `destroyed`.\n     */\n    destroy() {\n        return Promise.resolve()\n            .then(() => {\n            this.state = 'destroyed';\n            this._fire('stateChange');\n            super.destroy();\n            return this._destroy();\n        });\n    }\n    _destroy() {\n        return Promise.resolve()\n            .then(() => {\n            this._stopErrorHandling();\n            // Save data if there is a remaining editor data change.\n            this._throttledSave.flush();\n            const editor = this._editor;\n            this._editor = null;\n            // Remove the `change:data` listener before destroying the editor.\n            // Incorrectly written plugins may trigger firing `change:data` events during the editor destruction phase\n            // causing the watchdog to call `editor.getData()` when some parts of editor are already destroyed.\n            editor.model.document.off('change:data', this._throttledSave);\n            return this._destructor(editor);\n        });\n    }\n    /**\n     * Saves the editor data, so it can be restored after the crash even if the data cannot be fetched at\n     * the moment of the crash.\n     */\n    _save() {\n        const version = this._editor.model.document.version;\n        try {\n            this._data = this._getData();\n            this._lastDocumentVersion = version;\n        }\n        catch (err) {\n            console.error(err, 'An error happened during restoring editor data. ' +\n                'Editor will be restored from the previously saved data.');\n        }\n    }\n    /**\n     * @internal\n     */\n    _setExcludedProperties(props) {\n        this._excludedProps = props;\n    }\n    /**\n     * Returns the editor data.\n     */\n    _getData() {\n        const data = {};\n        for (const rootName of this._editor.model.document.getRootNames()) {\n            data[rootName] = this._editor.data.get({ rootName });\n        }\n        return data;\n    }\n    /**\n     * Traverses the error context and the current editor to find out whether these structures are connected\n     * to each other via properties.\n     *\n     * @internal\n     */\n    _isErrorComingFromThisItem(error) {\n        return areConnectedThroughProperties(this._editor, error.context, this._excludedProps);\n    }\n    /**\n     * Clones the editor configuration.\n     */\n    _cloneEditorConfiguration(config) {\n        return cloneDeepWith(config, (value, key) => {\n            // Leave DOM references.\n            if (isElement(value)) {\n                return value;\n            }\n            if (key === 'context') {\n                return value;\n            }\n        });\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,6BAA6B,MAAM,uCAAuC;AACjF,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,QAAQ,EAAEC,aAAa,EAAEC,SAAS,QAAQ,WAAW;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,cAAc,SAASJ,QAAQ,CAAC;EACjD;AACJ;AACA;AACA;EACIK,WAAWA,CAACC,MAAM,EAAEC,cAAc,GAAG,CAAC,CAAC,EAAE;IACrC,KAAK,CAACA,cAAc,CAAC;IACrB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACC,cAAc,GAAGR,QAAQ,CAAC,IAAI,CAACS,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAOJ,cAAc,CAACK,YAAY,KAAK,QAAQ,GAAGL,cAAc,CAACK,YAAY,GAAG,IAAI,CAAC;IAC3I;IACA,IAAIN,MAAM,EAAE;MACR,IAAI,CAACO,QAAQ,GAAI,CAACC,aAAa,EAAEC,MAAM,KAAKT,MAAM,CAACU,MAAM,CAACF,aAAa,EAAEC,MAAM,CAAE;IACrF;IACA,IAAI,CAACE,WAAW,GAAGC,MAAM,IAAIA,MAAM,CAACC,OAAO,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACI,IAAID,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACV,OAAO;EACvB;EACA;AACJ;AACA;EACI,IAAIY,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACZ,OAAO;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAI,CAACT,QAAQ,GAAGS,OAAO;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACP,WAAW,GAAGO,UAAU;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC,CACnBC,IAAI,CAAC,MAAM;MACZ,IAAI,CAACC,KAAK,GAAG,cAAc;MAC3B,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC;MACzB,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC1B,CAAC,CAAC,CACGC,KAAK,CAACC,GAAG,IAAI;MACdC,OAAO,CAACC,KAAK,CAAC,iDAAiD,EAAEF,GAAG,CAAC;IACzE,CAAC,CAAC,CACGL,IAAI,CAAC,MAAM;MACZ,IAAI,OAAO,IAAI,CAACQ,cAAc,KAAK,QAAQ,EAAE;QACzC,OAAO,IAAI,CAACpB,MAAM,CAAC,IAAI,CAACqB,KAAK,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC;MACtE,CAAC,MACI;QACD,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACJ,OAAO,EAAE;UAClDK,WAAW,EAAE,IAAI,CAACN;QACtB,CAAC,CAAC;QACF,OAAO,IAAI,CAACrB,MAAM,CAAC,IAAI,CAACoB,cAAc,EAAEI,aAAa,EAAEA,aAAa,CAACD,OAAO,CAAC;MACjF;IACJ,CAAC,CAAC,CACGX,IAAI,CAAC,MAAM;MACZ,IAAI,CAACE,KAAK,CAAC,SAAS,CAAC;IACzB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACId,MAAMA,CAACF,aAAa,GAAG,IAAI,CAACsB,cAAc,EAAErB,MAAM,GAAG,IAAI,CAACuB,OAAO,EAAEC,OAAO,EAAE;IACxE,OAAOb,OAAO,CAACC,OAAO,CAAC,CAAC,CACnBC,IAAI,CAAC,MAAM;MACZ,KAAK,CAACgB,mBAAmB,CAAC,CAAC;MAC3B,IAAI,CAACR,cAAc,GAAGtB,aAAa;MACnC;MACA;MACA,IAAI,CAACwB,OAAO,GAAG,IAAI,CAACO,yBAAyB,CAAC9B,MAAM,CAAC,IAAI,CAAC,CAAC;MAC3D,IAAI,CAACuB,OAAO,CAACC,OAAO,GAAGA,OAAO;MAC9B,OAAO,IAAI,CAAC1B,QAAQ,CAACC,aAAa,EAAE,IAAI,CAACwB,OAAO,CAAC;IACrD,CAAC,CAAC,CACGV,IAAI,CAACV,MAAM,IAAI;MAChB,IAAI,CAACV,OAAO,GAAGU,MAAM;MACrBA,MAAM,CAAC4B,KAAK,CAACC,QAAQ,CAACC,EAAE,CAAC,aAAa,EAAE,IAAI,CAACvC,cAAc,CAAC;MAC5D,IAAI,CAACwC,oBAAoB,GAAG/B,MAAM,CAAC4B,KAAK,CAACC,QAAQ,CAACG,OAAO;MACzD,IAAI,CAACb,KAAK,GAAG,IAAI,CAACc,QAAQ,CAAC,CAAC;MAC5B,IAAI,CAACtB,KAAK,GAAG,OAAO;MACpB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC;IAC7B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIX,OAAOA,CAAA,EAAG;IACN,OAAOO,OAAO,CAACC,OAAO,CAAC,CAAC,CACnBC,IAAI,CAAC,MAAM;MACZ,IAAI,CAACC,KAAK,GAAG,WAAW;MACxB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC;MACzB,KAAK,CAACX,OAAO,CAAC,CAAC;MACf,OAAO,IAAI,CAACY,QAAQ,CAAC,CAAC;IAC1B,CAAC,CAAC;EACN;EACAA,QAAQA,CAAA,EAAG;IACP,OAAOL,OAAO,CAACC,OAAO,CAAC,CAAC,CACnBC,IAAI,CAAC,MAAM;MACZ,IAAI,CAACwB,kBAAkB,CAAC,CAAC;MACzB;MACA,IAAI,CAAC3C,cAAc,CAAC4C,KAAK,CAAC,CAAC;MAC3B,MAAMnC,MAAM,GAAG,IAAI,CAACV,OAAO;MAC3B,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB;MACA;MACA;MACAU,MAAM,CAAC4B,KAAK,CAACC,QAAQ,CAACO,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC7C,cAAc,CAAC;MAC7D,OAAO,IAAI,CAACQ,WAAW,CAACC,MAAM,CAAC;IACnC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIR,KAAKA,CAAA,EAAG;IACJ,MAAMwC,OAAO,GAAG,IAAI,CAAC1C,OAAO,CAACsC,KAAK,CAACC,QAAQ,CAACG,OAAO;IACnD,IAAI;MACA,IAAI,CAACb,KAAK,GAAG,IAAI,CAACc,QAAQ,CAAC,CAAC;MAC5B,IAAI,CAACF,oBAAoB,GAAGC,OAAO;IACvC,CAAC,CACD,OAAOjB,GAAG,EAAE;MACRC,OAAO,CAACC,KAAK,CAACF,GAAG,EAAE,kDAAkD,GACjE,yDAAyD,CAAC;IAClE;EACJ;EACA;AACJ;AACA;EACIsB,sBAAsBA,CAACC,KAAK,EAAE;IAC1B,IAAI,CAACC,cAAc,GAAGD,KAAK;EAC/B;EACA;AACJ;AACA;EACIL,QAAQA,CAAA,EAAG;IACP,MAAMO,IAAI,GAAG,CAAC,CAAC;IACf,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACnD,OAAO,CAACsC,KAAK,CAACC,QAAQ,CAACa,YAAY,CAAC,CAAC,EAAE;MAC/DF,IAAI,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACnD,OAAO,CAACkD,IAAI,CAACG,GAAG,CAAC;QAAEF;MAAS,CAAC,CAAC;IACxD;IACA,OAAOD,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,0BAA0BA,CAAC3B,KAAK,EAAE;IAC9B,OAAOpC,6BAA6B,CAAC,IAAI,CAACS,OAAO,EAAE2B,KAAK,CAACI,OAAO,EAAE,IAAI,CAACkB,cAAc,CAAC;EAC1F;EACA;AACJ;AACA;EACIZ,yBAAyBA,CAAC9B,MAAM,EAAE;IAC9B,OAAOb,aAAa,CAACa,MAAM,EAAE,CAACgD,KAAK,EAAEC,GAAG,KAAK;MACzC;MACA,IAAI7D,SAAS,CAAC4D,KAAK,CAAC,EAAE;QAClB,OAAOA,KAAK;MAChB;MACA,IAAIC,GAAG,KAAK,SAAS,EAAE;QACnB,OAAOD,KAAK;MAChB;IACJ,CAAC,CAAC;EACN;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
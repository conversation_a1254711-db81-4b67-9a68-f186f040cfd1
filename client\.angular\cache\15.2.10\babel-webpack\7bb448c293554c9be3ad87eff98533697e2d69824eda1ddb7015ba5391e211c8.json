{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function distinctUntilChanged(compare, keySelector) {\n  return source => source.lift(new DistinctUntilChangedOperator(compare, keySelector));\n}\nclass DistinctUntilChangedOperator {\n  constructor(compare, keySelector) {\n    this.compare = compare;\n    this.keySelector = keySelector;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new DistinctUntilChangedSubscriber(subscriber, this.compare, this.keySelector));\n  }\n}\nclass DistinctUntilChangedSubscriber extends Subscriber {\n  constructor(destination, compare, keySelector) {\n    super(destination);\n    this.keySelector = keySelector;\n    this.hasKey = false;\n    if (typeof compare === 'function') {\n      this.compare = compare;\n    }\n  }\n  compare(x, y) {\n    return x === y;\n  }\n  _next(value) {\n    let key;\n    try {\n      const {\n        keySelector\n      } = this;\n      key = keySelector ? keySelector(value) : value;\n    } catch (err) {\n      return this.destination.error(err);\n    }\n    let result = false;\n    if (this.hasKey) {\n      try {\n        const {\n          compare\n        } = this;\n        result = compare(this.key, key);\n      } catch (err) {\n        return this.destination.error(err);\n      }\n    } else {\n      this.hasKey = true;\n    }\n    if (!result) {\n      this.key = key;\n      this.destination.next(value);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "distinctUntilChanged", "compare", "keySelector", "source", "lift", "DistinctUntilChangedOperator", "constructor", "call", "subscriber", "subscribe", "DistinctUntilChangedSubscriber", "destination", "<PERSON><PERSON><PERSON>", "x", "y", "_next", "value", "key", "err", "error", "result", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/distinctUntilChanged.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function distinctUntilChanged(compare, keySelector) {\n    return (source) => source.lift(new DistinctUntilChangedOperator(compare, keySelector));\n}\nclass DistinctUntilChangedOperator {\n    constructor(compare, keySelector) {\n        this.compare = compare;\n        this.keySelector = keySelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DistinctUntilChangedSubscriber(subscriber, this.compare, this.keySelector));\n    }\n}\nclass DistinctUntilChangedSubscriber extends Subscriber {\n    constructor(destination, compare, keySelector) {\n        super(destination);\n        this.keySelector = keySelector;\n        this.hasKey = false;\n        if (typeof compare === 'function') {\n            this.compare = compare;\n        }\n    }\n    compare(x, y) {\n        return x === y;\n    }\n    _next(value) {\n        let key;\n        try {\n            const { keySelector } = this;\n            key = keySelector ? keySelector(value) : value;\n        }\n        catch (err) {\n            return this.destination.error(err);\n        }\n        let result = false;\n        if (this.hasKey) {\n            try {\n                const { compare } = this;\n                result = compare(this.key, key);\n            }\n            catch (err) {\n                return this.destination.error(err);\n            }\n        }\n        else {\n            this.has<PERSON>ey = true;\n        }\n        if (!result) {\n            this.key = key;\n            this.destination.next(value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,oBAAoBA,CAACC,OAAO,EAAEC,WAAW,EAAE;EACvD,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,4BAA4B,CAACJ,OAAO,EAAEC,WAAW,CAAC,CAAC;AAC1F;AACA,MAAMG,4BAA4B,CAAC;EAC/BC,WAAWA,CAACL,OAAO,EAAEC,WAAW,EAAE;IAC9B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,8BAA8B,CAACF,UAAU,EAAE,IAAI,CAACP,OAAO,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;EAC3G;AACJ;AACA,MAAMQ,8BAA8B,SAASX,UAAU,CAAC;EACpDO,WAAWA,CAACK,WAAW,EAAEV,OAAO,EAAEC,WAAW,EAAE;IAC3C,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACT,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACU,MAAM,GAAG,KAAK;IACnB,IAAI,OAAOX,OAAO,KAAK,UAAU,EAAE;MAC/B,IAAI,CAACA,OAAO,GAAGA,OAAO;IAC1B;EACJ;EACAA,OAAOA,CAACY,CAAC,EAAEC,CAAC,EAAE;IACV,OAAOD,CAAC,KAAKC,CAAC;EAClB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACT,IAAIC,GAAG;IACP,IAAI;MACA,MAAM;QAAEf;MAAY,CAAC,GAAG,IAAI;MAC5Be,GAAG,GAAGf,WAAW,GAAGA,WAAW,CAACc,KAAK,CAAC,GAAGA,KAAK;IAClD,CAAC,CACD,OAAOE,GAAG,EAAE;MACR,OAAO,IAAI,CAACP,WAAW,CAACQ,KAAK,CAACD,GAAG,CAAC;IACtC;IACA,IAAIE,MAAM,GAAG,KAAK;IAClB,IAAI,IAAI,CAACR,MAAM,EAAE;MACb,IAAI;QACA,MAAM;UAAEX;QAAQ,CAAC,GAAG,IAAI;QACxBmB,MAAM,GAAGnB,OAAO,CAAC,IAAI,CAACgB,GAAG,EAAEA,GAAG,CAAC;MACnC,CAAC,CACD,OAAOC,GAAG,EAAE;QACR,OAAO,IAAI,CAACP,WAAW,CAACQ,KAAK,CAACD,GAAG,CAAC;MACtC;IACJ,CAAC,MACI;MACD,IAAI,CAACN,MAAM,GAAG,IAAI;IACtB;IACA,IAAI,CAACQ,MAAM,EAAE;MACT,IAAI,CAACH,GAAG,GAAGA,GAAG;MACd,IAAI,CAACN,WAAW,CAACU,IAAI,CAACL,KAAK,CAAC;IAChC;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
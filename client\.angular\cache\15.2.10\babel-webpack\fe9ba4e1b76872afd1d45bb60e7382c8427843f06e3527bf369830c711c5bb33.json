{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport Swal from 'sweetalert2';\nimport { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/auth.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/loading.service\";\nexport class ErrorInterceptor {\n  /**\r\n   * @param {Router} _router\r\n   * @param {AuthenticationService} _authService\r\n   */\n  constructor(_authService, _translateService, _loadingService) {\n    this._authService = _authService;\n    this._translateService = _translateService;\n    this._loadingService = _loadingService;\n  }\n  intercept(request, next) {\n    const isApiUrl = request.url.startsWith(environment.apiUrl);\n    return next.handle(request).pipe(map(event => {\n      if (event instanceof HttpResponse) {\n        this._loadingService.dismiss();\n        this._loadingService.dismissButtonLoading();\n      }\n      return event;\n    }), catchError(err => {\n      this._loadingService.dismiss();\n      this._loadingService.dismissButtonLoading();\n      if (err.status == 0) {\n        // ? Can also logout and reload if needed\n        // this._authService.logout();\n        err.error = {\n          message: 'Connection to server failed. Please try again later.'\n        };\n        Swal.fire({\n          title: 'Error',\n          text: 'Connection error. Please try again later.',\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      }\n      if (err.status == 500) {\n        Swal.fire({\n          title: 'Error',\n          text: 'Internal server error. Please try again later.',\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      }\n      if ([401, 403].indexOf(err.status) !== -1 && isApiUrl) {\n        // auto logout if 401 Unauthorized or 403 Forbidden response returned from api\n        this._authService.logoutNotRequest();\n        // ? Can also logout and reload if needed\n        // this._authService.logout();\n      }\n      // throwError\n      const error = err.error || err.statusText;\n      error.status = err?.status;\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function ErrorInterceptor_Factory(t) {\n    return new (t || ErrorInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.TranslateService), i0.ɵɵinject(i3.LoadingService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorInterceptor,\n    factory: ErrorInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAKEA,YAAY,QACP,sBAAsB;AAC7B,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAa,gBAAgB;AAGrD,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,WAAW,QAAQ,0BAA0B;;;;;AAItD,OAAM,MAAOC,gBAAgB;EAC3B;;;;EAIAC,YACUC,YAAyB,EACzBC,iBAAmC,EACpCC,eAA+B;IAF9B,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAClB,KAAAC,eAAe,GAAfA,eAAe;EACrB;EAEHC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAEjB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,GAAG,CAACC,UAAU,CAACX,WAAW,CAACY,MAAM,CAAC;IAC3D,OAAOJ,IAAI,CAACK,MAAM,CAACN,OAAO,CAAC,CAACO,IAAI,CAC9BhB,GAAG,CAAEiB,KAAqB,IAAI;MAC5B,IAAIA,KAAK,YAAYpB,YAAY,EAAE;QACjC,IAAI,CAACU,eAAe,CAACW,OAAO,EAAE;QAC9B,IAAI,CAACX,eAAe,CAACY,oBAAoB,EAAE;;MAE7C,OAAOF,KAAK;IACd,CAAC,CAAC,EACFlB,UAAU,CAAEqB,GAAG,IAAI;MACjB,IAAI,CAACb,eAAe,CAACW,OAAO,EAAE;MAC9B,IAAI,CAACX,eAAe,CAACY,oBAAoB,EAAE;MAC3C,IAAIC,GAAG,CAACC,MAAM,IAAI,CAAC,EAAE;QACnB;QACA;QACAD,GAAG,CAACE,KAAK,GAAG;UACVC,OAAO,EAAE;SACV;QACDtB,IAAI,CAACuB,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE,2CAA2C;UACjDC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,IAAI;SACvD,CAAC;;MAGJ,IAAIT,GAAG,CAACC,MAAM,IAAI,GAAG,EAAE;QACrBpB,IAAI,CAACuB,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE,gDAAgD;UACtDC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACtB,iBAAiB,CAACuB,OAAO,CAAC,IAAI;SACvD,CAAC;;MAGJ,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,OAAO,CAACV,GAAG,CAACC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAIV,QAAQ,EAAE;QACrD;QACA,IAAI,CAACN,YAAY,CAAC0B,gBAAgB,EAAE;QAEpC;QACA;;MAEF;MACA,MAAMT,KAAK,GAAGF,GAAG,CAACE,KAAK,IAAIF,GAAG,CAACY,UAAU;MACzCV,KAAK,CAACD,MAAM,GAAGD,GAAG,EAAEC,MAAM;MAE1B,OAAOvB,UAAU,CAACwB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAAC,QAAAW,CAAA;qBAhEU9B,gBAAgB,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA;WAAhBvC,gBAAgB;IAAAwC,OAAA,EAAhBxC,gBAAgB,CAAAyC;EAAA", "names": ["HttpResponse", "throwError", "catchError", "map", "<PERSON><PERSON>", "environment", "ErrorInterceptor", "constructor", "_authService", "_translateService", "_loadingService", "intercept", "request", "next", "isApiUrl", "url", "startsWith", "apiUrl", "handle", "pipe", "event", "dismiss", "dismissButtonLoading", "err", "status", "error", "message", "fire", "title", "text", "icon", "confirmButtonText", "instant", "indexOf", "logoutNotRequest", "statusText", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "TranslateService", "i3", "LoadingService", "_2", "factory", "ɵfac"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\helpers\\error.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport {\r\n  HttpRequest,\r\n  HttpHandler,\r\n  HttpEvent,\r\n  HttpInterceptor,\r\n  HttpResponse,\r\n} from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, map, tap } from 'rxjs/operators';\r\n\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\n@Injectable()\r\nexport class ErrorInterceptor implements HttpInterceptor {\r\n  /**\r\n   * @param {Router} _router\r\n   * @param {AuthenticationService} _authService\r\n   */\r\n  constructor(\r\n    private _authService: AuthService,\r\n    private _translateService: TranslateService,\r\n    public _loadingService: LoadingService\r\n  ) {}\r\n\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: <PERSON>ttpHand<PERSON>\r\n  ): Observable<HttpEvent<any>> {\r\n    const isApiUrl = request.url.startsWith(environment.apiUrl);\r\n    return next.handle(request).pipe(\r\n      map((event: HttpEvent<any>) => {\r\n        if (event instanceof HttpResponse) {\r\n          this._loadingService.dismiss();\r\n          this._loadingService.dismissButtonLoading();\r\n        }\r\n        return event;\r\n      }),\r\n      catchError((err) => {\r\n        this._loadingService.dismiss();\r\n        this._loadingService.dismissButtonLoading();\r\n        if (err.status == 0) {\r\n          // ? Can also logout and reload if needed\r\n          // this._authService.logout();\r\n          err.error = {\r\n            message: 'Connection to server failed. Please try again later.',\r\n          };\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: 'Connection error. Please try again later.',\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n          });\r\n        }\r\n\r\n        if (err.status == 500) {\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: 'Internal server error. Please try again later.',\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n          });\r\n        }\r\n\r\n        if ([401, 403].indexOf(err.status) !== -1 && isApiUrl) {\r\n          // auto logout if 401 Unauthorized or 403 Forbidden response returned from api\r\n          this._authService.logoutNotRequest();\r\n\r\n          // ? Can also logout and reload if needed\r\n          // this._authService.logout();\r\n        }\r\n        // throwError\r\n        const error = err.error || err.statusText;\r\n        error.status = err?.status;\r\n\r\n        return throwError(error);\r\n      })\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
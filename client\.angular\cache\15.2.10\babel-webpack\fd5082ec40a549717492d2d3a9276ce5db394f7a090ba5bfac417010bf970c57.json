{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function canReportError(observer) {\n  while (observer) {\n    const {\n      closed,\n      destination,\n      isStopped\n    } = observer;\n    if (closed || isStopped) {\n      return false;\n    } else if (destination && destination instanceof Subscriber) {\n      observer = destination;\n    } else {\n      observer = null;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["Subscriber", "canReportError", "observer", "closed", "destination", "isStopped"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/util/canReportError.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function canReportError(observer) {\n    while (observer) {\n        const { closed, destination, isStopped } = observer;\n        if (closed || isStopped) {\n            return false;\n        }\n        else if (destination && destination instanceof Subscriber) {\n            observer = destination;\n        }\n        else {\n            observer = null;\n        }\n    }\n    return true;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAE;EACrC,OAAOA,QAAQ,EAAE;IACb,MAAM;MAAEC,MAAM;MAAEC,WAAW;MAAEC;IAAU,CAAC,GAAGH,QAAQ;IACnD,IAAIC,MAAM,IAAIE,SAAS,EAAE;MACrB,OAAO,KAAK;IAChB,CAAC,MACI,IAAID,WAAW,IAAIA,WAAW,YAAYJ,UAAU,EAAE;MACvDE,QAAQ,GAAGE,WAAW;IAC1B,CAAC,MACI;MACDF,QAAQ,GAAG,IAAI;IACnB;EACJ;EACA,OAAO,IAAI;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
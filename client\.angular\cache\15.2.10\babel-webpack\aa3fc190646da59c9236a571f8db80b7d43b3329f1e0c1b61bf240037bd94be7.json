{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RowMatchComponent } from './row-match.component';\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\nimport * as i0 from \"@angular/core\";\nexport class RowMatchModule {\n  static #_ = this.ɵfac = function RowMatchModule_Factory(t) {\n    return new (t || RowMatchModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: RowMatchModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, HostListenersModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RowMatchModule, {\n    declarations: [RowMatchComponent],\n    imports: [CommonModule, HostListenersModule],\n    exports: [RowMatchComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,mBAAmB,QAAQ,yCAAyC;;AAO7E,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA;UAAdF;EAAc;EAAA,QAAAG,EAAA;cAHfN,YAAY,EAAEE,mBAAmB;EAAA;;;2EAGhCC,cAAc;IAAAI,YAAA,GAJVN,iBAAiB;IAAAO,OAAA,GACtBR,YAAY,EAAEE,mBAAmB;IAAAO,OAAA,GACjCR,iBAAiB;EAAA;AAAA", "names": ["CommonModule", "RowMatchComponent", "HostListenersModule", "RowMatchModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\components\\row-match\\row-match.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RowMatchComponent } from './row-match.component';\r\nimport { HostListenersModule } from 'app/hostlisteners/host-listeners.module';\r\n\r\n@NgModule({\r\n  declarations: [RowMatchComponent],\r\n  imports: [CommonModule, HostListenersModule],\r\n  exports: [RowMatchComponent],\r\n})\r\nexport class RowMatchModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
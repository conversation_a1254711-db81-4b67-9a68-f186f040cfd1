{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function distinct(keySelector, flushes) {\n  return source => source.lift(new DistinctOperator(keySelector, flushes));\n}\nclass DistinctOperator {\n  constructor(keySelector, flushes) {\n    this.keySelector = keySelector;\n    this.flushes = flushes;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new DistinctSubscriber(subscriber, this.keySelector, this.flushes));\n  }\n}\nexport class DistinctSubscriber extends OuterSubscriber {\n  constructor(destination, keySelector, flushes) {\n    super(destination);\n    this.keySelector = keySelector;\n    this.values = new Set();\n    if (flushes) {\n      this.add(subscribeToResult(this, flushes));\n    }\n  }\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.values.clear();\n  }\n  notifyError(error, innerSub) {\n    this._error(error);\n  }\n  _next(value) {\n    if (this.keySelector) {\n      this._useKeySelector(value);\n    } else {\n      this._finalizeNext(value, value);\n    }\n  }\n  _useKeySelector(value) {\n    let key;\n    const {\n      destination\n    } = this;\n    try {\n      key = this.keySelector(value);\n    } catch (err) {\n      destination.error(err);\n      return;\n    }\n    this._finalizeNext(key, value);\n  }\n  _finalizeNext(key, value) {\n    const {\n      values\n    } = this;\n    if (!values.has(key)) {\n      values.add(key);\n      this.destination.next(value);\n    }\n  }\n}", "map": {"version": 3, "names": ["OuterSubscriber", "subscribeToResult", "distinct", "keySelector", "flushes", "source", "lift", "DistinctOperator", "constructor", "call", "subscriber", "subscribe", "DistinctSubscriber", "destination", "values", "Set", "add", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "clear", "notifyError", "error", "_error", "_next", "value", "_useKeySelector", "_finalizeNext", "key", "err", "has", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/distinct.js"], "sourcesContent": ["import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function distinct(keySelector, flushes) {\n    return (source) => source.lift(new DistinctOperator(keySelector, flushes));\n}\nclass DistinctOperator {\n    constructor(keySelector, flushes) {\n        this.keySelector = keySelector;\n        this.flushes = flushes;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DistinctSubscriber(subscriber, this.keySelector, this.flushes));\n    }\n}\nexport class DistinctSubscriber extends OuterSubscriber {\n    constructor(destination, keySelector, flushes) {\n        super(destination);\n        this.keySelector = keySelector;\n        this.values = new Set();\n        if (flushes) {\n            this.add(subscribeToResult(this, flushes));\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.values.clear();\n    }\n    notifyError(error, innerSub) {\n        this._error(error);\n    }\n    _next(value) {\n        if (this.keySelector) {\n            this._useKeySelector(value);\n        }\n        else {\n            this._finalizeNext(value, value);\n        }\n    }\n    _useKeySelector(value) {\n        let key;\n        const { destination } = this;\n        try {\n            key = this.keySelector(value);\n        }\n        catch (err) {\n            destination.error(err);\n            return;\n        }\n        this._finalizeNext(key, value);\n    }\n    _finalizeNext(key, value) {\n        const { values } = this;\n        if (!values.has(key)) {\n            values.add(key);\n            this.destination.next(value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,OAAO,SAASC,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC3C,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,gBAAgB,CAACJ,WAAW,EAAEC,OAAO,CAAC,CAAC;AAC9E;AACA,MAAMG,gBAAgB,CAAC;EACnBC,WAAWA,CAACL,WAAW,EAAEC,OAAO,EAAE;IAC9B,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,kBAAkB,CAACF,UAAU,EAAE,IAAI,CAACP,WAAW,EAAE,IAAI,CAACC,OAAO,CAAC,CAAC;EAC/F;AACJ;AACA,OAAO,MAAMQ,kBAAkB,SAASZ,eAAe,CAAC;EACpDQ,WAAWA,CAACK,WAAW,EAAEV,WAAW,EAAEC,OAAO,EAAE;IAC3C,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACV,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACW,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAIX,OAAO,EAAE;MACT,IAAI,CAACY,GAAG,CAACf,iBAAiB,CAAC,IAAI,EAAEG,OAAO,CAAC,CAAC;IAC9C;EACJ;EACAa,UAAUA,CAACC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IACjE,IAAI,CAACR,MAAM,CAACS,KAAK,CAAC,CAAC;EACvB;EACAC,WAAWA,CAACC,KAAK,EAAEH,QAAQ,EAAE;IACzB,IAAI,CAACI,MAAM,CAACD,KAAK,CAAC;EACtB;EACAE,KAAKA,CAACC,KAAK,EAAE;IACT,IAAI,IAAI,CAACzB,WAAW,EAAE;MAClB,IAAI,CAAC0B,eAAe,CAACD,KAAK,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACE,aAAa,CAACF,KAAK,EAAEA,KAAK,CAAC;IACpC;EACJ;EACAC,eAAeA,CAACD,KAAK,EAAE;IACnB,IAAIG,GAAG;IACP,MAAM;MAAElB;IAAY,CAAC,GAAG,IAAI;IAC5B,IAAI;MACAkB,GAAG,GAAG,IAAI,CAAC5B,WAAW,CAACyB,KAAK,CAAC;IACjC,CAAC,CACD,OAAOI,GAAG,EAAE;MACRnB,WAAW,CAACY,KAAK,CAACO,GAAG,CAAC;MACtB;IACJ;IACA,IAAI,CAACF,aAAa,CAACC,GAAG,EAAEH,KAAK,CAAC;EAClC;EACAE,aAAaA,CAACC,GAAG,EAAEH,KAAK,EAAE;IACtB,MAAM;MAAEd;IAAO,CAAC,GAAG,IAAI;IACvB,IAAI,CAACA,MAAM,CAACmB,GAAG,CAACF,GAAG,CAAC,EAAE;MAClBjB,MAAM,CAACE,GAAG,CAACe,GAAG,CAAC;MACf,IAAI,CAAClB,WAAW,CAACqB,IAAI,CAACN,KAAK,CAAC;IAChC;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
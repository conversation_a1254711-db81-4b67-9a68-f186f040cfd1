{"ast": null, "code": "/*! Hammer.JS - v2.0.7 - 2016-04-22\n * http://hammerjs.github.io/\n *\n * Copyright (c) 2016 <PERSON><PERSON>;\n * Licensed under the MIT license */\n(function (window, document, exportName, undefined) {\n  'use strict';\n\n  var VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\n  var TEST_ELEMENT = document.createElement('div');\n  var TYPE_FUNCTION = 'function';\n  var round = Math.round;\n  var abs = Math.abs;\n  var now = Date.now;\n\n  /**\n   * set a timeout with a given scope\n   * @param {Function} fn\n   * @param {Number} timeout\n   * @param {Object} context\n   * @returns {number}\n   */\n  function setTimeoutContext(fn, timeout, context) {\n    return setTimeout(bindFn(fn, context), timeout);\n  }\n\n  /**\n   * if the argument is an array, we want to execute the fn on each entry\n   * if it aint an array we don't want to do a thing.\n   * this is used by all the methods that accept a single and array argument.\n   * @param {*|Array} arg\n   * @param {String} fn\n   * @param {Object} [context]\n   * @returns {Boolean}\n   */\n  function invokeArrayArg(arg, fn, context) {\n    if (Array.isArray(arg)) {\n      each(arg, context[fn], context);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * walk objects and arrays\n   * @param {Object} obj\n   * @param {Function} iterator\n   * @param {Object} context\n   */\n  function each(obj, iterator, context) {\n    var i;\n    if (!obj) {\n      return;\n    }\n    if (obj.forEach) {\n      obj.forEach(iterator, context);\n    } else if (obj.length !== undefined) {\n      i = 0;\n      while (i < obj.length) {\n        iterator.call(context, obj[i], i, obj);\n        i++;\n      }\n    } else {\n      for (i in obj) {\n        obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n      }\n    }\n  }\n\n  /**\n   * wrap a method with a deprecation warning and stack trace\n   * @param {Function} method\n   * @param {String} name\n   * @param {String} message\n   * @returns {Function} A new function wrapping the supplied method.\n   */\n  function deprecate(method, name, message) {\n    var deprecationMessage = 'DEPRECATED METHOD: ' + name + '\\n' + message + ' AT \\n';\n    return function () {\n      var e = new Error('get-stack-trace');\n      var stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '').replace(/^\\s+at\\s+/gm, '').replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n      var log = window.console && (window.console.warn || window.console.log);\n      if (log) {\n        log.call(window.console, deprecationMessage, stack);\n      }\n      return method.apply(this, arguments);\n    };\n  }\n\n  /**\n   * extend object.\n   * means that properties in dest will be overwritten by the ones in src.\n   * @param {Object} target\n   * @param {...Object} objects_to_assign\n   * @returns {Object} target\n   */\n  var assign;\n  if (typeof Object.assign !== 'function') {\n    assign = function assign(target) {\n      if (target === undefined || target === null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n      }\n      var output = Object(target);\n      for (var index = 1; index < arguments.length; index++) {\n        var source = arguments[index];\n        if (source !== undefined && source !== null) {\n          for (var nextKey in source) {\n            if (source.hasOwnProperty(nextKey)) {\n              output[nextKey] = source[nextKey];\n            }\n          }\n        }\n      }\n      return output;\n    };\n  } else {\n    assign = Object.assign;\n  }\n\n  /**\n   * extend object.\n   * means that properties in dest will be overwritten by the ones in src.\n   * @param {Object} dest\n   * @param {Object} src\n   * @param {Boolean} [merge=false]\n   * @returns {Object} dest\n   */\n  var extend = deprecate(function extend(dest, src, merge) {\n    var keys = Object.keys(src);\n    var i = 0;\n    while (i < keys.length) {\n      if (!merge || merge && dest[keys[i]] === undefined) {\n        dest[keys[i]] = src[keys[i]];\n      }\n      i++;\n    }\n    return dest;\n  }, 'extend', 'Use `assign`.');\n\n  /**\n   * merge the values from src in the dest.\n   * means that properties that exist in dest will not be overwritten by src\n   * @param {Object} dest\n   * @param {Object} src\n   * @returns {Object} dest\n   */\n  var merge = deprecate(function merge(dest, src) {\n    return extend(dest, src, true);\n  }, 'merge', 'Use `assign`.');\n\n  /**\n   * simple class inheritance\n   * @param {Function} child\n   * @param {Function} base\n   * @param {Object} [properties]\n   */\n  function inherit(child, base, properties) {\n    var baseP = base.prototype,\n      childP;\n    childP = child.prototype = Object.create(baseP);\n    childP.constructor = child;\n    childP._super = baseP;\n    if (properties) {\n      assign(childP, properties);\n    }\n  }\n\n  /**\n   * simple function bind\n   * @param {Function} fn\n   * @param {Object} context\n   * @returns {Function}\n   */\n  function bindFn(fn, context) {\n    return function boundFn() {\n      return fn.apply(context, arguments);\n    };\n  }\n\n  /**\n   * let a boolean value also be a function that must return a boolean\n   * this first item in args will be used as the context\n   * @param {Boolean|Function} val\n   * @param {Array} [args]\n   * @returns {Boolean}\n   */\n  function boolOrFn(val, args) {\n    if (typeof val == TYPE_FUNCTION) {\n      return val.apply(args ? args[0] || undefined : undefined, args);\n    }\n    return val;\n  }\n\n  /**\n   * use the val2 when val1 is undefined\n   * @param {*} val1\n   * @param {*} val2\n   * @returns {*}\n   */\n  function ifUndefined(val1, val2) {\n    return val1 === undefined ? val2 : val1;\n  }\n\n  /**\n   * addEventListener with multiple events at once\n   * @param {EventTarget} target\n   * @param {String} types\n   * @param {Function} handler\n   */\n  function addEventListeners(target, types, handler) {\n    each(splitStr(types), function (type) {\n      target.addEventListener(type, handler, false);\n    });\n  }\n\n  /**\n   * removeEventListener with multiple events at once\n   * @param {EventTarget} target\n   * @param {String} types\n   * @param {Function} handler\n   */\n  function removeEventListeners(target, types, handler) {\n    each(splitStr(types), function (type) {\n      target.removeEventListener(type, handler, false);\n    });\n  }\n\n  /**\n   * find if a node is in the given parent\n   * @method hasParent\n   * @param {HTMLElement} node\n   * @param {HTMLElement} parent\n   * @return {Boolean} found\n   */\n  function hasParent(node, parent) {\n    while (node) {\n      if (node == parent) {\n        return true;\n      }\n      node = node.parentNode;\n    }\n    return false;\n  }\n\n  /**\n   * small indexOf wrapper\n   * @param {String} str\n   * @param {String} find\n   * @returns {Boolean} found\n   */\n  function inStr(str, find) {\n    return str.indexOf(find) > -1;\n  }\n\n  /**\n   * split string on whitespace\n   * @param {String} str\n   * @returns {Array} words\n   */\n  function splitStr(str) {\n    return str.trim().split(/\\s+/g);\n  }\n\n  /**\n   * find if a array contains the object using indexOf or a simple polyFill\n   * @param {Array} src\n   * @param {String} find\n   * @param {String} [findByKey]\n   * @return {Boolean|Number} false when not found, or the index\n   */\n  function inArray(src, find, findByKey) {\n    if (src.indexOf && !findByKey) {\n      return src.indexOf(find);\n    } else {\n      var i = 0;\n      while (i < src.length) {\n        if (findByKey && src[i][findByKey] == find || !findByKey && src[i] === find) {\n          return i;\n        }\n        i++;\n      }\n      return -1;\n    }\n  }\n\n  /**\n   * convert array-like objects to real arrays\n   * @param {Object} obj\n   * @returns {Array}\n   */\n  function toArray(obj) {\n    return Array.prototype.slice.call(obj, 0);\n  }\n\n  /**\n   * unique array with objects based on a key (like 'id') or just by the array's value\n   * @param {Array} src [{id:1},{id:2},{id:1}]\n   * @param {String} [key]\n   * @param {Boolean} [sort=False]\n   * @returns {Array} [{id:1},{id:2}]\n   */\n  function uniqueArray(src, key, sort) {\n    var results = [];\n    var values = [];\n    var i = 0;\n    while (i < src.length) {\n      var val = key ? src[i][key] : src[i];\n      if (inArray(values, val) < 0) {\n        results.push(src[i]);\n      }\n      values[i] = val;\n      i++;\n    }\n    if (sort) {\n      if (!key) {\n        results = results.sort();\n      } else {\n        results = results.sort(function sortUniqueArray(a, b) {\n          return a[key] > b[key];\n        });\n      }\n    }\n    return results;\n  }\n\n  /**\n   * get the prefixed property\n   * @param {Object} obj\n   * @param {String} property\n   * @returns {String|Undefined} prefixed\n   */\n  function prefixed(obj, property) {\n    var prefix, prop;\n    var camelProp = property[0].toUpperCase() + property.slice(1);\n    var i = 0;\n    while (i < VENDOR_PREFIXES.length) {\n      prefix = VENDOR_PREFIXES[i];\n      prop = prefix ? prefix + camelProp : property;\n      if (prop in obj) {\n        return prop;\n      }\n      i++;\n    }\n    return undefined;\n  }\n\n  /**\n   * get a unique id\n   * @returns {number} uniqueId\n   */\n  var _uniqueId = 1;\n  function uniqueId() {\n    return _uniqueId++;\n  }\n\n  /**\n   * get the window object of an element\n   * @param {HTMLElement} element\n   * @returns {DocumentView|Window}\n   */\n  function getWindowForElement(element) {\n    var doc = element.ownerDocument || element;\n    return doc.defaultView || doc.parentWindow || window;\n  }\n  var MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n  var SUPPORT_TOUCH = ('ontouchstart' in window);\n  var SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\n  var SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n  var INPUT_TYPE_TOUCH = 'touch';\n  var INPUT_TYPE_PEN = 'pen';\n  var INPUT_TYPE_MOUSE = 'mouse';\n  var INPUT_TYPE_KINECT = 'kinect';\n  var COMPUTE_INTERVAL = 25;\n  var INPUT_START = 1;\n  var INPUT_MOVE = 2;\n  var INPUT_END = 4;\n  var INPUT_CANCEL = 8;\n  var DIRECTION_NONE = 1;\n  var DIRECTION_LEFT = 2;\n  var DIRECTION_RIGHT = 4;\n  var DIRECTION_UP = 8;\n  var DIRECTION_DOWN = 16;\n  var DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\n  var DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\n  var DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n  var PROPS_XY = ['x', 'y'];\n  var PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\n  /**\n   * create new input type manager\n   * @param {Manager} manager\n   * @param {Function} callback\n   * @returns {Input}\n   * @constructor\n   */\n  function Input(manager, callback) {\n    var self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function (ev) {\n      if (boolOrFn(manager.options.enable, [manager])) {\n        self.handler(ev);\n      }\n    };\n    this.init();\n  }\n  Input.prototype = {\n    /**\n     * should handle the inputEvent data and trigger the callback\n     * @virtual\n     */\n    handler: function () {},\n    /**\n     * bind the events\n     */\n    init: function () {\n      this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n      this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n      this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    },\n    /**\n     * unbind the events\n     */\n    destroy: function () {\n      this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n      this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n      this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    }\n  };\n\n  /**\n   * create new input type manager\n   * called by the Manager constructor\n   * @param {Hammer} manager\n   * @returns {Input}\n   */\n  function createInputInstance(manager) {\n    var Type;\n    var inputClass = manager.options.inputClass;\n    if (inputClass) {\n      Type = inputClass;\n    } else if (SUPPORT_POINTER_EVENTS) {\n      Type = PointerEventInput;\n    } else if (SUPPORT_ONLY_TOUCH) {\n      Type = TouchInput;\n    } else if (!SUPPORT_TOUCH) {\n      Type = MouseInput;\n    } else {\n      Type = TouchMouseInput;\n    }\n    return new Type(manager, inputHandler);\n  }\n\n  /**\n   * handle input events\n   * @param {Manager} manager\n   * @param {String} eventType\n   * @param {Object} input\n   */\n  function inputHandler(manager, eventType, input) {\n    var pointersLen = input.pointers.length;\n    var changedPointersLen = input.changedPointers.length;\n    var isFirst = eventType & INPUT_START && pointersLen - changedPointersLen === 0;\n    var isFinal = eventType & (INPUT_END | INPUT_CANCEL) && pointersLen - changedPointersLen === 0;\n    input.isFirst = !!isFirst;\n    input.isFinal = !!isFinal;\n    if (isFirst) {\n      manager.session = {};\n    }\n\n    // source event is the normalized value of the domEvents\n    // like 'touchstart, mouseup, pointerdown'\n    input.eventType = eventType;\n\n    // compute scale, rotation etc\n    computeInputData(manager, input);\n\n    // emit secret event\n    manager.emit('hammer.input', input);\n    manager.recognize(input);\n    manager.session.prevInput = input;\n  }\n\n  /**\n   * extend the data with some usable properties like scale, rotate, velocity etc\n   * @param {Object} manager\n   * @param {Object} input\n   */\n  function computeInputData(manager, input) {\n    var session = manager.session;\n    var pointers = input.pointers;\n    var pointersLength = pointers.length;\n\n    // store the first input to calculate the distance and direction\n    if (!session.firstInput) {\n      session.firstInput = simpleCloneInputData(input);\n    }\n\n    // to compute scale and rotation we need to store the multiple touches\n    if (pointersLength > 1 && !session.firstMultiple) {\n      session.firstMultiple = simpleCloneInputData(input);\n    } else if (pointersLength === 1) {\n      session.firstMultiple = false;\n    }\n    var firstInput = session.firstInput;\n    var firstMultiple = session.firstMultiple;\n    var offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n    var center = input.center = getCenter(pointers);\n    input.timeStamp = now();\n    input.deltaTime = input.timeStamp - firstInput.timeStamp;\n    input.angle = getAngle(offsetCenter, center);\n    input.distance = getDistance(offsetCenter, center);\n    computeDeltaXY(session, input);\n    input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n    var overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n    input.overallVelocityX = overallVelocity.x;\n    input.overallVelocityY = overallVelocity.y;\n    input.overallVelocity = abs(overallVelocity.x) > abs(overallVelocity.y) ? overallVelocity.x : overallVelocity.y;\n    input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n    input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n    input.maxPointers = !session.prevInput ? input.pointers.length : input.pointers.length > session.prevInput.maxPointers ? input.pointers.length : session.prevInput.maxPointers;\n    computeIntervalInputData(session, input);\n\n    // find the correct target\n    var target = manager.element;\n    if (hasParent(input.srcEvent.target, target)) {\n      target = input.srcEvent.target;\n    }\n    input.target = target;\n  }\n  function computeDeltaXY(session, input) {\n    var center = input.center;\n    var offset = session.offsetDelta || {};\n    var prevDelta = session.prevDelta || {};\n    var prevInput = session.prevInput || {};\n    if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n      prevDelta = session.prevDelta = {\n        x: prevInput.deltaX || 0,\n        y: prevInput.deltaY || 0\n      };\n      offset = session.offsetDelta = {\n        x: center.x,\n        y: center.y\n      };\n    }\n    input.deltaX = prevDelta.x + (center.x - offset.x);\n    input.deltaY = prevDelta.y + (center.y - offset.y);\n  }\n\n  /**\n   * velocity is calculated every x ms\n   * @param {Object} session\n   * @param {Object} input\n   */\n  function computeIntervalInputData(session, input) {\n    var last = session.lastInterval || input,\n      deltaTime = input.timeStamp - last.timeStamp,\n      velocity,\n      velocityX,\n      velocityY,\n      direction;\n    if (input.eventType != INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n      var deltaX = input.deltaX - last.deltaX;\n      var deltaY = input.deltaY - last.deltaY;\n      var v = getVelocity(deltaTime, deltaX, deltaY);\n      velocityX = v.x;\n      velocityY = v.y;\n      velocity = abs(v.x) > abs(v.y) ? v.x : v.y;\n      direction = getDirection(deltaX, deltaY);\n      session.lastInterval = input;\n    } else {\n      // use latest velocity info if it doesn't overtake a minimum period\n      velocity = last.velocity;\n      velocityX = last.velocityX;\n      velocityY = last.velocityY;\n      direction = last.direction;\n    }\n    input.velocity = velocity;\n    input.velocityX = velocityX;\n    input.velocityY = velocityY;\n    input.direction = direction;\n  }\n\n  /**\n   * create a simple clone from the input used for storage of firstInput and firstMultiple\n   * @param {Object} input\n   * @returns {Object} clonedInputData\n   */\n  function simpleCloneInputData(input) {\n    // make a simple copy of the pointers because we will get a reference if we don't\n    // we only need clientXY for the calculations\n    var pointers = [];\n    var i = 0;\n    while (i < input.pointers.length) {\n      pointers[i] = {\n        clientX: round(input.pointers[i].clientX),\n        clientY: round(input.pointers[i].clientY)\n      };\n      i++;\n    }\n    return {\n      timeStamp: now(),\n      pointers: pointers,\n      center: getCenter(pointers),\n      deltaX: input.deltaX,\n      deltaY: input.deltaY\n    };\n  }\n\n  /**\n   * get the center of all the pointers\n   * @param {Array} pointers\n   * @return {Object} center contains `x` and `y` properties\n   */\n  function getCenter(pointers) {\n    var pointersLength = pointers.length;\n\n    // no need to loop when only one touch\n    if (pointersLength === 1) {\n      return {\n        x: round(pointers[0].clientX),\n        y: round(pointers[0].clientY)\n      };\n    }\n    var x = 0,\n      y = 0,\n      i = 0;\n    while (i < pointersLength) {\n      x += pointers[i].clientX;\n      y += pointers[i].clientY;\n      i++;\n    }\n    return {\n      x: round(x / pointersLength),\n      y: round(y / pointersLength)\n    };\n  }\n\n  /**\n   * calculate the velocity between two points. unit is in px per ms.\n   * @param {Number} deltaTime\n   * @param {Number} x\n   * @param {Number} y\n   * @return {Object} velocity `x` and `y`\n   */\n  function getVelocity(deltaTime, x, y) {\n    return {\n      x: x / deltaTime || 0,\n      y: y / deltaTime || 0\n    };\n  }\n\n  /**\n   * get the direction between two points\n   * @param {Number} x\n   * @param {Number} y\n   * @return {Number} direction\n   */\n  function getDirection(x, y) {\n    if (x === y) {\n      return DIRECTION_NONE;\n    }\n    if (abs(x) >= abs(y)) {\n      return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n  }\n\n  /**\n   * calculate the absolute distance between two points\n   * @param {Object} p1 {x, y}\n   * @param {Object} p2 {x, y}\n   * @param {Array} [props] containing x and y keys\n   * @return {Number} distance\n   */\n  function getDistance(p1, p2, props) {\n    if (!props) {\n      props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n      y = p2[props[1]] - p1[props[1]];\n    return Math.sqrt(x * x + y * y);\n  }\n\n  /**\n   * calculate the angle between two coordinates\n   * @param {Object} p1\n   * @param {Object} p2\n   * @param {Array} [props] containing x and y keys\n   * @return {Number} angle\n   */\n  function getAngle(p1, p2, props) {\n    if (!props) {\n      props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n      y = p2[props[1]] - p1[props[1]];\n    return Math.atan2(y, x) * 180 / Math.PI;\n  }\n\n  /**\n   * calculate the rotation degrees between two pointersets\n   * @param {Array} start array of pointers\n   * @param {Array} end array of pointers\n   * @return {Number} rotation\n   */\n  function getRotation(start, end) {\n    return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n  }\n\n  /**\n   * calculate the scale factor between two pointersets\n   * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n   * @param {Array} start array of pointers\n   * @param {Array} end array of pointers\n   * @return {Number} scale\n   */\n  function getScale(start, end) {\n    return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n  }\n  var MOUSE_INPUT_MAP = {\n    mousedown: INPUT_START,\n    mousemove: INPUT_MOVE,\n    mouseup: INPUT_END\n  };\n  var MOUSE_ELEMENT_EVENTS = 'mousedown';\n  var MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n  /**\n   * Mouse events input\n   * @constructor\n   * @extends Input\n   */\n  function MouseInput() {\n    this.evEl = MOUSE_ELEMENT_EVENTS;\n    this.evWin = MOUSE_WINDOW_EVENTS;\n    this.pressed = false; // mousedown state\n\n    Input.apply(this, arguments);\n  }\n  inherit(MouseInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function MEhandler(ev) {\n      var eventType = MOUSE_INPUT_MAP[ev.type];\n\n      // on start we want to have the left mouse button down\n      if (eventType & INPUT_START && ev.button === 0) {\n        this.pressed = true;\n      }\n      if (eventType & INPUT_MOVE && ev.which !== 1) {\n        eventType = INPUT_END;\n      }\n\n      // mouse must be down\n      if (!this.pressed) {\n        return;\n      }\n      if (eventType & INPUT_END) {\n        this.pressed = false;\n      }\n      this.callback(this.manager, eventType, {\n        pointers: [ev],\n        changedPointers: [ev],\n        pointerType: INPUT_TYPE_MOUSE,\n        srcEvent: ev\n      });\n    }\n  });\n  var POINTER_INPUT_MAP = {\n    pointerdown: INPUT_START,\n    pointermove: INPUT_MOVE,\n    pointerup: INPUT_END,\n    pointercancel: INPUT_CANCEL,\n    pointerout: INPUT_CANCEL\n  };\n\n  // in IE10 the pointer types is defined as an enum\n  var IE10_POINTER_TYPE_ENUM = {\n    2: INPUT_TYPE_TOUCH,\n    3: INPUT_TYPE_PEN,\n    4: INPUT_TYPE_MOUSE,\n    5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n  };\n\n  var POINTER_ELEMENT_EVENTS = 'pointerdown';\n  var POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n  // IE10 has prefixed support, and case-sensitive\n  if (window.MSPointerEvent && !window.PointerEvent) {\n    POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n    POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n  }\n\n  /**\n   * Pointer events input\n   * @constructor\n   * @extends Input\n   */\n  function PointerEventInput() {\n    this.evEl = POINTER_ELEMENT_EVENTS;\n    this.evWin = POINTER_WINDOW_EVENTS;\n    Input.apply(this, arguments);\n    this.store = this.manager.session.pointerEvents = [];\n  }\n  inherit(PointerEventInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function PEhandler(ev) {\n      var store = this.store;\n      var removePointer = false;\n      var eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n      var eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n      var pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n      var isTouch = pointerType == INPUT_TYPE_TOUCH;\n\n      // get index of the event in the store\n      var storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n      // start and mouse must be down\n      if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n        if (storeIndex < 0) {\n          store.push(ev);\n          storeIndex = store.length - 1;\n        }\n      } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n        removePointer = true;\n      }\n\n      // it not found, so the pointer hasn't been down (so it's probably a hover)\n      if (storeIndex < 0) {\n        return;\n      }\n\n      // update the event in the store\n      store[storeIndex] = ev;\n      this.callback(this.manager, eventType, {\n        pointers: store,\n        changedPointers: [ev],\n        pointerType: pointerType,\n        srcEvent: ev\n      });\n      if (removePointer) {\n        // remove from the store\n        store.splice(storeIndex, 1);\n      }\n    }\n  });\n  var SINGLE_TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n  };\n  var SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\n  var SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n  /**\n   * Touch events input\n   * @constructor\n   * @extends Input\n   */\n  function SingleTouchInput() {\n    this.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    this.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n    this.started = false;\n    Input.apply(this, arguments);\n  }\n  inherit(SingleTouchInput, Input, {\n    handler: function TEhandler(ev) {\n      var type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n      // should we handle the touch events?\n      if (type === INPUT_START) {\n        this.started = true;\n      }\n      if (!this.started) {\n        return;\n      }\n      var touches = normalizeSingleTouches.call(this, ev, type);\n\n      // when done, reset the started state\n      if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n        this.started = false;\n      }\n      this.callback(this.manager, type, {\n        pointers: touches[0],\n        changedPointers: touches[1],\n        pointerType: INPUT_TYPE_TOUCH,\n        srcEvent: ev\n      });\n    }\n  });\n\n  /**\n   * @this {TouchInput}\n   * @param {Object} ev\n   * @param {Number} type flag\n   * @returns {undefined|Array} [all, changed]\n   */\n  function normalizeSingleTouches(ev, type) {\n    var all = toArray(ev.touches);\n    var changed = toArray(ev.changedTouches);\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n      all = uniqueArray(all.concat(changed), 'identifier', true);\n    }\n    return [all, changed];\n  }\n  var TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n  };\n  var TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n  /**\n   * Multi-user touch events input\n   * @constructor\n   * @extends Input\n   */\n  function TouchInput() {\n    this.evTarget = TOUCH_TARGET_EVENTS;\n    this.targetIds = {};\n    Input.apply(this, arguments);\n  }\n  inherit(TouchInput, Input, {\n    handler: function MTEhandler(ev) {\n      var type = TOUCH_INPUT_MAP[ev.type];\n      var touches = getTouches.call(this, ev, type);\n      if (!touches) {\n        return;\n      }\n      this.callback(this.manager, type, {\n        pointers: touches[0],\n        changedPointers: touches[1],\n        pointerType: INPUT_TYPE_TOUCH,\n        srcEvent: ev\n      });\n    }\n  });\n\n  /**\n   * @this {TouchInput}\n   * @param {Object} ev\n   * @param {Number} type flag\n   * @returns {undefined|Array} [all, changed]\n   */\n  function getTouches(ev, type) {\n    var allTouches = toArray(ev.touches);\n    var targetIds = this.targetIds;\n\n    // when there is only one touch, the process can be simplified\n    if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n      targetIds[allTouches[0].identifier] = true;\n      return [allTouches, allTouches];\n    }\n    var i,\n      targetTouches,\n      changedTouches = toArray(ev.changedTouches),\n      changedTargetTouches = [],\n      target = this.target;\n\n    // get target touches from touches\n    targetTouches = allTouches.filter(function (touch) {\n      return hasParent(touch.target, target);\n    });\n\n    // collect touches\n    if (type === INPUT_START) {\n      i = 0;\n      while (i < targetTouches.length) {\n        targetIds[targetTouches[i].identifier] = true;\n        i++;\n      }\n    }\n\n    // filter changed touches to only contain touches that exist in the collected target ids\n    i = 0;\n    while (i < changedTouches.length) {\n      if (targetIds[changedTouches[i].identifier]) {\n        changedTargetTouches.push(changedTouches[i]);\n      }\n\n      // cleanup removed touches\n      if (type & (INPUT_END | INPUT_CANCEL)) {\n        delete targetIds[changedTouches[i].identifier];\n      }\n      i++;\n    }\n    if (!changedTargetTouches.length) {\n      return;\n    }\n    return [\n    // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n    uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true), changedTargetTouches];\n  }\n\n  /**\n   * Combined touch and mouse input\n   *\n   * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n   * This because touch devices also emit mouse events while doing a touch.\n   *\n   * @constructor\n   * @extends Input\n   */\n\n  var DEDUP_TIMEOUT = 2500;\n  var DEDUP_DISTANCE = 25;\n  function TouchMouseInput() {\n    Input.apply(this, arguments);\n    var handler = bindFn(this.handler, this);\n    this.touch = new TouchInput(this.manager, handler);\n    this.mouse = new MouseInput(this.manager, handler);\n    this.primaryTouch = null;\n    this.lastTouches = [];\n  }\n  inherit(TouchMouseInput, Input, {\n    /**\n     * handle mouse and touch events\n     * @param {Hammer} manager\n     * @param {String} inputEvent\n     * @param {Object} inputData\n     */\n    handler: function TMEhandler(manager, inputEvent, inputData) {\n      var isTouch = inputData.pointerType == INPUT_TYPE_TOUCH,\n        isMouse = inputData.pointerType == INPUT_TYPE_MOUSE;\n      if (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n        return;\n      }\n\n      // when we're in a touch event, record touches to  de-dupe synthetic mouse event\n      if (isTouch) {\n        recordTouches.call(this, inputEvent, inputData);\n      } else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n        return;\n      }\n      this.callback(manager, inputEvent, inputData);\n    },\n    /**\n     * remove the event listeners\n     */\n    destroy: function destroy() {\n      this.touch.destroy();\n      this.mouse.destroy();\n    }\n  });\n  function recordTouches(eventType, eventData) {\n    if (eventType & INPUT_START) {\n      this.primaryTouch = eventData.changedPointers[0].identifier;\n      setLastTouch.call(this, eventData);\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n      setLastTouch.call(this, eventData);\n    }\n  }\n  function setLastTouch(eventData) {\n    var touch = eventData.changedPointers[0];\n    if (touch.identifier === this.primaryTouch) {\n      var lastTouch = {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n      this.lastTouches.push(lastTouch);\n      var lts = this.lastTouches;\n      var removeLastTouch = function () {\n        var i = lts.indexOf(lastTouch);\n        if (i > -1) {\n          lts.splice(i, 1);\n        }\n      };\n      setTimeout(removeLastTouch, DEDUP_TIMEOUT);\n    }\n  }\n  function isSyntheticEvent(eventData) {\n    var x = eventData.srcEvent.clientX,\n      y = eventData.srcEvent.clientY;\n    for (var i = 0; i < this.lastTouches.length; i++) {\n      var t = this.lastTouches[i];\n      var dx = Math.abs(x - t.x),\n        dy = Math.abs(y - t.y);\n      if (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n        return true;\n      }\n    }\n    return false;\n  }\n  var PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\n  var NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\n  // magical touchAction value\n  var TOUCH_ACTION_COMPUTE = 'compute';\n  var TOUCH_ACTION_AUTO = 'auto';\n  var TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\n  var TOUCH_ACTION_NONE = 'none';\n  var TOUCH_ACTION_PAN_X = 'pan-x';\n  var TOUCH_ACTION_PAN_Y = 'pan-y';\n  var TOUCH_ACTION_MAP = getTouchActionProps();\n\n  /**\n   * Touch Action\n   * sets the touchAction property or uses the js alternative\n   * @param {Manager} manager\n   * @param {String} value\n   * @constructor\n   */\n  function TouchAction(manager, value) {\n    this.manager = manager;\n    this.set(value);\n  }\n  TouchAction.prototype = {\n    /**\n     * set the touchAction value on the element or enable the polyfill\n     * @param {String} value\n     */\n    set: function (value) {\n      // find out the touch-action by the event handlers\n      if (value == TOUCH_ACTION_COMPUTE) {\n        value = this.compute();\n      }\n      if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n        this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n      }\n      this.actions = value.toLowerCase().trim();\n    },\n    /**\n     * just re-set the touchAction value\n     */\n    update: function () {\n      this.set(this.manager.options.touchAction);\n    },\n    /**\n     * compute the value for the touchAction property based on the recognizer's settings\n     * @returns {String} value\n     */\n    compute: function () {\n      var actions = [];\n      each(this.manager.recognizers, function (recognizer) {\n        if (boolOrFn(recognizer.options.enable, [recognizer])) {\n          actions = actions.concat(recognizer.getTouchAction());\n        }\n      });\n      return cleanTouchActions(actions.join(' '));\n    },\n    /**\n     * this method is called on each input cycle and provides the preventing of the browser behavior\n     * @param {Object} input\n     */\n    preventDefaults: function (input) {\n      var srcEvent = input.srcEvent;\n      var direction = input.offsetDirection;\n\n      // if the touch action did prevented once this session\n      if (this.manager.session.prevented) {\n        srcEvent.preventDefault();\n        return;\n      }\n      var actions = this.actions;\n      var hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n      var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n      var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n      if (hasNone) {\n        //do not prevent defaults if this is a tap gesture\n\n        var isTapPointer = input.pointers.length === 1;\n        var isTapMovement = input.distance < 2;\n        var isTapTouchTime = input.deltaTime < 250;\n        if (isTapPointer && isTapMovement && isTapTouchTime) {\n          return;\n        }\n      }\n      if (hasPanX && hasPanY) {\n        // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n        return;\n      }\n      if (hasNone || hasPanY && direction & DIRECTION_HORIZONTAL || hasPanX && direction & DIRECTION_VERTICAL) {\n        return this.preventSrc(srcEvent);\n      }\n    },\n    /**\n     * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n     * @param {Object} srcEvent\n     */\n    preventSrc: function (srcEvent) {\n      this.manager.session.prevented = true;\n      srcEvent.preventDefault();\n    }\n  };\n\n  /**\n   * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n   * @param {String} actions\n   * @returns {*}\n   */\n  function cleanTouchActions(actions) {\n    // none\n    if (inStr(actions, TOUCH_ACTION_NONE)) {\n      return TOUCH_ACTION_NONE;\n    }\n    var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n    var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n    // if both pan-x and pan-y are set (different recognizers\n    // for different directions, e.g. horizontal pan but vertical swipe?)\n    // we need none (as otherwise with pan-x pan-y combined none of these\n    // recognizers will work, since the browser would handle all panning\n    if (hasPanX && hasPanY) {\n      return TOUCH_ACTION_NONE;\n    }\n\n    // pan-x OR pan-y\n    if (hasPanX || hasPanY) {\n      return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n    }\n\n    // manipulation\n    if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n      return TOUCH_ACTION_MANIPULATION;\n    }\n    return TOUCH_ACTION_AUTO;\n  }\n  function getTouchActionProps() {\n    if (!NATIVE_TOUCH_ACTION) {\n      return false;\n    }\n    var touchMap = {};\n    var cssSupports = window.CSS && window.CSS.supports;\n    ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach(function (val) {\n      // If css.supports is not supported but there is native touch-action assume it supports\n      // all values. This is the case for IE 10 and 11.\n      touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n    });\n    return touchMap;\n  }\n\n  /**\n   * Recognizer flow explained; *\n   * All recognizers have the initial state of POSSIBLE when a input session starts.\n   * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n   * Example session for mouse-input: mousedown -> mousemove -> mouseup\n   *\n   * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n   * which determines with state it should be.\n   *\n   * If the recognizer has the state FAILED, CANCELLED or RECOGNIZED (equals ENDED), it is reset to\n   * POSSIBLE to give it another change on the next cycle.\n   *\n   *               Possible\n   *                  |\n   *            +-----+---------------+\n   *            |                     |\n   *      +-----+-----+               |\n   *      |           |               |\n   *   Failed      Cancelled          |\n   *                          +-------+------+\n   *                          |              |\n   *                      Recognized       Began\n   *                                         |\n   *                                      Changed\n   *                                         |\n   *                                  Ended/Recognized\n   */\n  var STATE_POSSIBLE = 1;\n  var STATE_BEGAN = 2;\n  var STATE_CHANGED = 4;\n  var STATE_ENDED = 8;\n  var STATE_RECOGNIZED = STATE_ENDED;\n  var STATE_CANCELLED = 16;\n  var STATE_FAILED = 32;\n\n  /**\n   * Recognizer\n   * Every recognizer needs to extend from this class.\n   * @constructor\n   * @param {Object} options\n   */\n  function Recognizer(options) {\n    this.options = assign({}, this.defaults, options || {});\n    this.id = uniqueId();\n    this.manager = null;\n\n    // default is enable true\n    this.options.enable = ifUndefined(this.options.enable, true);\n    this.state = STATE_POSSIBLE;\n    this.simultaneous = {};\n    this.requireFail = [];\n  }\n  Recognizer.prototype = {\n    /**\n     * @virtual\n     * @type {Object}\n     */\n    defaults: {},\n    /**\n     * set options\n     * @param {Object} options\n     * @return {Recognizer}\n     */\n    set: function (options) {\n      assign(this.options, options);\n\n      // also update the touchAction, in case something changed about the directions/enabled state\n      this.manager && this.manager.touchAction.update();\n      return this;\n    },\n    /**\n     * recognize simultaneous with an other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    recognizeWith: function (otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n        return this;\n      }\n      var simultaneous = this.simultaneous;\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      if (!simultaneous[otherRecognizer.id]) {\n        simultaneous[otherRecognizer.id] = otherRecognizer;\n        otherRecognizer.recognizeWith(this);\n      }\n      return this;\n    },\n    /**\n     * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRecognizeWith: function (otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n        return this;\n      }\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      delete this.simultaneous[otherRecognizer.id];\n      return this;\n    },\n    /**\n     * recognizer can only run when an other is failing\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    requireFailure: function (otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n        return this;\n      }\n      var requireFail = this.requireFail;\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      if (inArray(requireFail, otherRecognizer) === -1) {\n        requireFail.push(otherRecognizer);\n        otherRecognizer.requireFailure(this);\n      }\n      return this;\n    },\n    /**\n     * drop the requireFailure link. it does not remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRequireFailure: function (otherRecognizer) {\n      if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n        return this;\n      }\n      otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n      var index = inArray(this.requireFail, otherRecognizer);\n      if (index > -1) {\n        this.requireFail.splice(index, 1);\n      }\n      return this;\n    },\n    /**\n     * has require failures boolean\n     * @returns {boolean}\n     */\n    hasRequireFailures: function () {\n      return this.requireFail.length > 0;\n    },\n    /**\n     * if the recognizer can recognize simultaneous with an other recognizer\n     * @param {Recognizer} otherRecognizer\n     * @returns {Boolean}\n     */\n    canRecognizeWith: function (otherRecognizer) {\n      return !!this.simultaneous[otherRecognizer.id];\n    },\n    /**\n     * You should use `tryEmit` instead of `emit` directly to check\n     * that all the needed recognizers has failed before emitting.\n     * @param {Object} input\n     */\n    emit: function (input) {\n      var self = this;\n      var state = this.state;\n      function emit(event) {\n        self.manager.emit(event, input);\n      }\n\n      // 'panstart' and 'panmove'\n      if (state < STATE_ENDED) {\n        emit(self.options.event + stateStr(state));\n      }\n      emit(self.options.event); // simple 'eventName' events\n\n      if (input.additionalEvent) {\n        // additional event(panleft, panright, pinchin, pinchout...)\n        emit(input.additionalEvent);\n      }\n\n      // panend and pancancel\n      if (state >= STATE_ENDED) {\n        emit(self.options.event + stateStr(state));\n      }\n    },\n    /**\n     * Check that all the require failure recognizers has failed,\n     * if true, it emits a gesture event,\n     * otherwise, setup the state to FAILED.\n     * @param {Object} input\n     */\n    tryEmit: function (input) {\n      if (this.canEmit()) {\n        return this.emit(input);\n      }\n      // it's failing anyway\n      this.state = STATE_FAILED;\n    },\n    /**\n     * can we emit?\n     * @returns {boolean}\n     */\n    canEmit: function () {\n      var i = 0;\n      while (i < this.requireFail.length) {\n        if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n          return false;\n        }\n        i++;\n      }\n      return true;\n    },\n    /**\n     * update the recognizer\n     * @param {Object} inputData\n     */\n    recognize: function (inputData) {\n      // make a new copy of the inputData\n      // so we can change the inputData without messing up the other recognizers\n      var inputDataClone = assign({}, inputData);\n\n      // is is enabled and allow recognizing?\n      if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n        this.reset();\n        this.state = STATE_FAILED;\n        return;\n      }\n\n      // reset when we've reached the end\n      if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n        this.state = STATE_POSSIBLE;\n      }\n      this.state = this.process(inputDataClone);\n\n      // the recognizer has recognized a gesture\n      // so trigger an event\n      if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n        this.tryEmit(inputDataClone);\n      }\n    },\n    /**\n     * return the state of the recognizer\n     * the actual recognizing happens in this method\n     * @virtual\n     * @param {Object} inputData\n     * @returns {Const} STATE\n     */\n    process: function (inputData) {},\n    // jshint ignore:line\n\n    /**\n     * return the preferred touch-action\n     * @virtual\n     * @returns {Array}\n     */\n    getTouchAction: function () {},\n    /**\n     * called when the gesture isn't allowed to recognize\n     * like when another is being recognized or it is disabled\n     * @virtual\n     */\n    reset: function () {}\n  };\n\n  /**\n   * get a usable string, used as event postfix\n   * @param {Const} state\n   * @returns {String} state\n   */\n  function stateStr(state) {\n    if (state & STATE_CANCELLED) {\n      return 'cancel';\n    } else if (state & STATE_ENDED) {\n      return 'end';\n    } else if (state & STATE_CHANGED) {\n      return 'move';\n    } else if (state & STATE_BEGAN) {\n      return 'start';\n    }\n    return '';\n  }\n\n  /**\n   * direction cons to string\n   * @param {Const} direction\n   * @returns {String}\n   */\n  function directionStr(direction) {\n    if (direction == DIRECTION_DOWN) {\n      return 'down';\n    } else if (direction == DIRECTION_UP) {\n      return 'up';\n    } else if (direction == DIRECTION_LEFT) {\n      return 'left';\n    } else if (direction == DIRECTION_RIGHT) {\n      return 'right';\n    }\n    return '';\n  }\n\n  /**\n   * get a recognizer by name if it is bound to a manager\n   * @param {Recognizer|String} otherRecognizer\n   * @param {Recognizer} recognizer\n   * @returns {Recognizer}\n   */\n  function getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n    var manager = recognizer.manager;\n    if (manager) {\n      return manager.get(otherRecognizer);\n    }\n    return otherRecognizer;\n  }\n\n  /**\n   * This recognizer is just used as a base for the simple attribute recognizers.\n   * @constructor\n   * @extends Recognizer\n   */\n  function AttrRecognizer() {\n    Recognizer.apply(this, arguments);\n  }\n  inherit(AttrRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof AttrRecognizer\n     */\n    defaults: {\n      /**\n       * @type {Number}\n       * @default 1\n       */\n      pointers: 1\n    },\n    /**\n     * Used to check if it the recognizer receives valid input, like input.distance > 10.\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {Boolean} recognized\n     */\n    attrTest: function (input) {\n      var optionPointers = this.options.pointers;\n      return optionPointers === 0 || input.pointers.length === optionPointers;\n    },\n    /**\n     * Process the input and return the state for the recognizer\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {*} State\n     */\n    process: function (input) {\n      var state = this.state;\n      var eventType = input.eventType;\n      var isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n      var isValid = this.attrTest(input);\n\n      // on cancel input and we've recognized before, return STATE_CANCELLED\n      if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n        return state | STATE_CANCELLED;\n      } else if (isRecognized || isValid) {\n        if (eventType & INPUT_END) {\n          return state | STATE_ENDED;\n        } else if (!(state & STATE_BEGAN)) {\n          return STATE_BEGAN;\n        }\n        return state | STATE_CHANGED;\n      }\n      return STATE_FAILED;\n    }\n  });\n\n  /**\n   * Pan\n   * Recognized when the pointer is down and moved in the allowed direction.\n   * @constructor\n   * @extends AttrRecognizer\n   */\n  function PanRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n    this.pX = null;\n    this.pY = null;\n  }\n  inherit(PanRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PanRecognizer\n     */\n    defaults: {\n      event: 'pan',\n      threshold: 10,\n      pointers: 1,\n      direction: DIRECTION_ALL\n    },\n    getTouchAction: function () {\n      var direction = this.options.direction;\n      var actions = [];\n      if (direction & DIRECTION_HORIZONTAL) {\n        actions.push(TOUCH_ACTION_PAN_Y);\n      }\n      if (direction & DIRECTION_VERTICAL) {\n        actions.push(TOUCH_ACTION_PAN_X);\n      }\n      return actions;\n    },\n    directionTest: function (input) {\n      var options = this.options;\n      var hasMoved = true;\n      var distance = input.distance;\n      var direction = input.direction;\n      var x = input.deltaX;\n      var y = input.deltaY;\n\n      // lock to axis?\n      if (!(direction & options.direction)) {\n        if (options.direction & DIRECTION_HORIZONTAL) {\n          direction = x === 0 ? DIRECTION_NONE : x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n          hasMoved = x != this.pX;\n          distance = Math.abs(input.deltaX);\n        } else {\n          direction = y === 0 ? DIRECTION_NONE : y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n          hasMoved = y != this.pY;\n          distance = Math.abs(input.deltaY);\n        }\n      }\n      input.direction = direction;\n      return hasMoved && distance > options.threshold && direction & options.direction;\n    },\n    attrTest: function (input) {\n      return AttrRecognizer.prototype.attrTest.call(this, input) && (this.state & STATE_BEGAN || !(this.state & STATE_BEGAN) && this.directionTest(input));\n    },\n    emit: function (input) {\n      this.pX = input.deltaX;\n      this.pY = input.deltaY;\n      var direction = directionStr(input.direction);\n      if (direction) {\n        input.additionalEvent = this.options.event + direction;\n      }\n      this._super.emit.call(this, input);\n    }\n  });\n\n  /**\n   * Pinch\n   * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n   * @constructor\n   * @extends AttrRecognizer\n   */\n  function PinchRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n  }\n  inherit(PinchRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n      event: 'pinch',\n      threshold: 0,\n      pointers: 2\n    },\n    getTouchAction: function () {\n      return [TOUCH_ACTION_NONE];\n    },\n    attrTest: function (input) {\n      return this._super.attrTest.call(this, input) && (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n    },\n    emit: function (input) {\n      if (input.scale !== 1) {\n        var inOut = input.scale < 1 ? 'in' : 'out';\n        input.additionalEvent = this.options.event + inOut;\n      }\n      this._super.emit.call(this, input);\n    }\n  });\n\n  /**\n   * Press\n   * Recognized when the pointer is down for x ms without any movement.\n   * @constructor\n   * @extends Recognizer\n   */\n  function PressRecognizer() {\n    Recognizer.apply(this, arguments);\n    this._timer = null;\n    this._input = null;\n  }\n  inherit(PressRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PressRecognizer\n     */\n    defaults: {\n      event: 'press',\n      pointers: 1,\n      time: 251,\n      // minimal time of the pointer to be pressed\n      threshold: 9 // a minimal movement is ok, but keep it low\n    },\n\n    getTouchAction: function () {\n      return [TOUCH_ACTION_AUTO];\n    },\n    process: function (input) {\n      var options = this.options;\n      var validPointers = input.pointers.length === options.pointers;\n      var validMovement = input.distance < options.threshold;\n      var validTime = input.deltaTime > options.time;\n      this._input = input;\n\n      // we only allow little movement\n      // and we've reached an end event, so a tap is possible\n      if (!validMovement || !validPointers || input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime) {\n        this.reset();\n      } else if (input.eventType & INPUT_START) {\n        this.reset();\n        this._timer = setTimeoutContext(function () {\n          this.state = STATE_RECOGNIZED;\n          this.tryEmit();\n        }, options.time, this);\n      } else if (input.eventType & INPUT_END) {\n        return STATE_RECOGNIZED;\n      }\n      return STATE_FAILED;\n    },\n    reset: function () {\n      clearTimeout(this._timer);\n    },\n    emit: function (input) {\n      if (this.state !== STATE_RECOGNIZED) {\n        return;\n      }\n      if (input && input.eventType & INPUT_END) {\n        this.manager.emit(this.options.event + 'up', input);\n      } else {\n        this._input.timeStamp = now();\n        this.manager.emit(this.options.event, this._input);\n      }\n    }\n  });\n\n  /**\n   * Rotate\n   * Recognized when two or more pointer are moving in a circular motion.\n   * @constructor\n   * @extends AttrRecognizer\n   */\n  function RotateRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n  }\n  inherit(RotateRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof RotateRecognizer\n     */\n    defaults: {\n      event: 'rotate',\n      threshold: 0,\n      pointers: 2\n    },\n    getTouchAction: function () {\n      return [TOUCH_ACTION_NONE];\n    },\n    attrTest: function (input) {\n      return this._super.attrTest.call(this, input) && (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n    }\n  });\n\n  /**\n   * Swipe\n   * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n   * @constructor\n   * @extends AttrRecognizer\n   */\n  function SwipeRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n  }\n  inherit(SwipeRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof SwipeRecognizer\n     */\n    defaults: {\n      event: 'swipe',\n      threshold: 10,\n      velocity: 0.3,\n      direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n      pointers: 1\n    },\n    getTouchAction: function () {\n      return PanRecognizer.prototype.getTouchAction.call(this);\n    },\n    attrTest: function (input) {\n      var direction = this.options.direction;\n      var velocity;\n      if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n        velocity = input.overallVelocity;\n      } else if (direction & DIRECTION_HORIZONTAL) {\n        velocity = input.overallVelocityX;\n      } else if (direction & DIRECTION_VERTICAL) {\n        velocity = input.overallVelocityY;\n      }\n      return this._super.attrTest.call(this, input) && direction & input.offsetDirection && input.distance > this.options.threshold && input.maxPointers == this.options.pointers && abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n    },\n    emit: function (input) {\n      var direction = directionStr(input.offsetDirection);\n      if (direction) {\n        this.manager.emit(this.options.event + direction, input);\n      }\n      this.manager.emit(this.options.event, input);\n    }\n  });\n\n  /**\n   * A tap is ecognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n   * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n   * a single tap.\n   *\n   * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n   * multi-taps being recognized.\n   * @constructor\n   * @extends Recognizer\n   */\n  function TapRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n  }\n  inherit(TapRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n      event: 'tap',\n      pointers: 1,\n      taps: 1,\n      interval: 300,\n      // max time between the multi-tap taps\n      time: 250,\n      // max time of the pointer to be down (like finger on the screen)\n      threshold: 9,\n      // a minimal movement is ok, but keep it low\n      posThreshold: 10 // a multi-tap can be a bit off the initial position\n    },\n\n    getTouchAction: function () {\n      return [TOUCH_ACTION_MANIPULATION];\n    },\n    process: function (input) {\n      var options = this.options;\n      var validPointers = input.pointers.length === options.pointers;\n      var validMovement = input.distance < options.threshold;\n      var validTouchTime = input.deltaTime < options.time;\n      this.reset();\n      if (input.eventType & INPUT_START && this.count === 0) {\n        return this.failTimeout();\n      }\n\n      // we only allow little movement\n      // and we've reached an end event, so a tap is possible\n      if (validMovement && validTouchTime && validPointers) {\n        if (input.eventType != INPUT_END) {\n          return this.failTimeout();\n        }\n        var validInterval = this.pTime ? input.timeStamp - this.pTime < options.interval : true;\n        var validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n        this.pTime = input.timeStamp;\n        this.pCenter = input.center;\n        if (!validMultiTap || !validInterval) {\n          this.count = 1;\n        } else {\n          this.count += 1;\n        }\n        this._input = input;\n\n        // if tap count matches we have recognized it,\n        // else it has began recognizing...\n        var tapCount = this.count % options.taps;\n        if (tapCount === 0) {\n          // no failing requirements, immediately trigger the tap event\n          // or wait as long as the multitap interval to trigger\n          if (!this.hasRequireFailures()) {\n            return STATE_RECOGNIZED;\n          } else {\n            this._timer = setTimeoutContext(function () {\n              this.state = STATE_RECOGNIZED;\n              this.tryEmit();\n            }, options.interval, this);\n            return STATE_BEGAN;\n          }\n        }\n      }\n      return STATE_FAILED;\n    },\n    failTimeout: function () {\n      this._timer = setTimeoutContext(function () {\n        this.state = STATE_FAILED;\n      }, this.options.interval, this);\n      return STATE_FAILED;\n    },\n    reset: function () {\n      clearTimeout(this._timer);\n    },\n    emit: function () {\n      if (this.state == STATE_RECOGNIZED) {\n        this._input.tapCount = this.count;\n        this.manager.emit(this.options.event, this._input);\n      }\n    }\n  });\n\n  /**\n   * Simple way to create a manager with a default set of recognizers.\n   * @param {HTMLElement} element\n   * @param {Object} [options]\n   * @constructor\n   */\n  function Hammer(element, options) {\n    options = options || {};\n    options.recognizers = ifUndefined(options.recognizers, Hammer.defaults.preset);\n    return new Manager(element, options);\n  }\n\n  /**\n   * @const {string}\n   */\n  Hammer.VERSION = '2.0.7';\n\n  /**\n   * default settings\n   * @namespace\n   */\n  Hammer.defaults = {\n    /**\n     * set if DOM events are being triggered.\n     * But this is slower and unused by simple implementations, so disabled by default.\n     * @type {Boolean}\n     * @default false\n     */\n    domEvents: false,\n    /**\n     * The value for the touchAction property/fallback.\n     * When set to `compute` it will magically set the correct value based on the added recognizers.\n     * @type {String}\n     * @default compute\n     */\n    touchAction: TOUCH_ACTION_COMPUTE,\n    /**\n     * @type {Boolean}\n     * @default true\n     */\n    enable: true,\n    /**\n     * EXPERIMENTAL FEATURE -- can be removed/changed\n     * Change the parent input target element.\n     * If Null, then it is being set the to main element.\n     * @type {Null|EventTarget}\n     * @default null\n     */\n    inputTarget: null,\n    /**\n     * force an input class\n     * @type {Null|Function}\n     * @default null\n     */\n    inputClass: null,\n    /**\n     * Default recognizer setup when calling `Hammer()`\n     * When creating a new Manager these will be skipped.\n     * @type {Array}\n     */\n    preset: [\n    // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n    [RotateRecognizer, {\n      enable: false\n    }], [PinchRecognizer, {\n      enable: false\n    }, ['rotate']], [SwipeRecognizer, {\n      direction: DIRECTION_HORIZONTAL\n    }], [PanRecognizer, {\n      direction: DIRECTION_HORIZONTAL\n    }, ['swipe']], [TapRecognizer], [TapRecognizer, {\n      event: 'doubletap',\n      taps: 2\n    }, ['tap']], [PressRecognizer]],\n    /**\n     * Some CSS properties can be used to improve the working of Hammer.\n     * Add them to this method and they will be set when creating a new Manager.\n     * @namespace\n     */\n    cssProps: {\n      /**\n       * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n       * @type {String}\n       * @default 'none'\n       */\n      userSelect: 'none',\n      /**\n       * Disable the Windows Phone grippers when pressing an element.\n       * @type {String}\n       * @default 'none'\n       */\n      touchSelect: 'none',\n      /**\n       * Disables the default callout shown when you touch and hold a touch target.\n       * On iOS, when you touch and hold a touch target such as a link, Safari displays\n       * a callout containing information about the link. This property allows you to disable that callout.\n       * @type {String}\n       * @default 'none'\n       */\n      touchCallout: 'none',\n      /**\n       * Specifies whether zooming is enabled. Used by IE10>\n       * @type {String}\n       * @default 'none'\n       */\n      contentZooming: 'none',\n      /**\n       * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n       * @type {String}\n       * @default 'none'\n       */\n      userDrag: 'none',\n      /**\n       * Overrides the highlight color shown when the user taps a link or a JavaScript\n       * clickable element in iOS. This property obeys the alpha value, if specified.\n       * @type {String}\n       * @default 'rgba(0,0,0,0)'\n       */\n      tapHighlightColor: 'rgba(0,0,0,0)'\n    }\n  };\n  var STOP = 1;\n  var FORCED_STOP = 2;\n\n  /**\n   * Manager\n   * @param {HTMLElement} element\n   * @param {Object} [options]\n   * @constructor\n   */\n  function Manager(element, options) {\n    this.options = assign({}, Hammer.defaults, options || {});\n    this.options.inputTarget = this.options.inputTarget || element;\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n    toggleCssProps(this, true);\n    each(this.options.recognizers, function (item) {\n      var recognizer = this.add(new item[0](item[1]));\n      item[2] && recognizer.recognizeWith(item[2]);\n      item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n  }\n  Manager.prototype = {\n    /**\n     * set options\n     * @param {Object} options\n     * @returns {Manager}\n     */\n    set: function (options) {\n      assign(this.options, options);\n\n      // Options that need a little more setup\n      if (options.touchAction) {\n        this.touchAction.update();\n      }\n      if (options.inputTarget) {\n        // Clean up existing event listeners and reinitialize\n        this.input.destroy();\n        this.input.target = options.inputTarget;\n        this.input.init();\n      }\n      return this;\n    },\n    /**\n     * stop recognizing for this session.\n     * This session will be discarded, when a new [input]start event is fired.\n     * When forced, the recognizer cycle is stopped immediately.\n     * @param {Boolean} [force]\n     */\n    stop: function (force) {\n      this.session.stopped = force ? FORCED_STOP : STOP;\n    },\n    /**\n     * run the recognizers!\n     * called by the inputHandler function on every movement of the pointers (touches)\n     * it walks through all the recognizers and tries to detect the gesture that is being made\n     * @param {Object} inputData\n     */\n    recognize: function (inputData) {\n      var session = this.session;\n      if (session.stopped) {\n        return;\n      }\n\n      // run the touch-action polyfill\n      this.touchAction.preventDefaults(inputData);\n      var recognizer;\n      var recognizers = this.recognizers;\n\n      // this holds the recognizer that is being recognized.\n      // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n      // if no recognizer is detecting a thing, it is set to `null`\n      var curRecognizer = session.curRecognizer;\n\n      // reset when the last recognizer is recognized\n      // or when we're in a new session\n      if (!curRecognizer || curRecognizer && curRecognizer.state & STATE_RECOGNIZED) {\n        curRecognizer = session.curRecognizer = null;\n      }\n      var i = 0;\n      while (i < recognizers.length) {\n        recognizer = recognizers[i];\n\n        // find out if we are allowed try to recognize the input for this one.\n        // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n        // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n        //      that is being recognized.\n        // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n        //      this can be setup with the `recognizeWith()` method on the recognizer.\n        if (session.stopped !== FORCED_STOP && (\n        // 1\n        !curRecognizer || recognizer == curRecognizer ||\n        // 2\n        recognizer.canRecognizeWith(curRecognizer))) {\n          // 3\n          recognizer.recognize(inputData);\n        } else {\n          recognizer.reset();\n        }\n\n        // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n        // current active recognizer. but only if we don't already have an active recognizer\n        if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n          curRecognizer = session.curRecognizer = recognizer;\n        }\n        i++;\n      }\n    },\n    /**\n     * get a recognizer by its event name.\n     * @param {Recognizer|String} recognizer\n     * @returns {Recognizer|Null}\n     */\n    get: function (recognizer) {\n      if (recognizer instanceof Recognizer) {\n        return recognizer;\n      }\n      var recognizers = this.recognizers;\n      for (var i = 0; i < recognizers.length; i++) {\n        if (recognizers[i].options.event == recognizer) {\n          return recognizers[i];\n        }\n      }\n      return null;\n    },\n    /**\n     * add a recognizer to the manager\n     * existing recognizers with the same event name will be removed\n     * @param {Recognizer} recognizer\n     * @returns {Recognizer|Manager}\n     */\n    add: function (recognizer) {\n      if (invokeArrayArg(recognizer, 'add', this)) {\n        return this;\n      }\n\n      // remove existing\n      var existing = this.get(recognizer.options.event);\n      if (existing) {\n        this.remove(existing);\n      }\n      this.recognizers.push(recognizer);\n      recognizer.manager = this;\n      this.touchAction.update();\n      return recognizer;\n    },\n    /**\n     * remove a recognizer by name or instance\n     * @param {Recognizer|String} recognizer\n     * @returns {Manager}\n     */\n    remove: function (recognizer) {\n      if (invokeArrayArg(recognizer, 'remove', this)) {\n        return this;\n      }\n      recognizer = this.get(recognizer);\n\n      // let's make sure this recognizer exists\n      if (recognizer) {\n        var recognizers = this.recognizers;\n        var index = inArray(recognizers, recognizer);\n        if (index !== -1) {\n          recognizers.splice(index, 1);\n          this.touchAction.update();\n        }\n      }\n      return this;\n    },\n    /**\n     * bind event\n     * @param {String} events\n     * @param {Function} handler\n     * @returns {EventEmitter} this\n     */\n    on: function (events, handler) {\n      if (events === undefined) {\n        return;\n      }\n      if (handler === undefined) {\n        return;\n      }\n      var handlers = this.handlers;\n      each(splitStr(events), function (event) {\n        handlers[event] = handlers[event] || [];\n        handlers[event].push(handler);\n      });\n      return this;\n    },\n    /**\n     * unbind event, leave emit blank to remove all handlers\n     * @param {String} events\n     * @param {Function} [handler]\n     * @returns {EventEmitter} this\n     */\n    off: function (events, handler) {\n      if (events === undefined) {\n        return;\n      }\n      var handlers = this.handlers;\n      each(splitStr(events), function (event) {\n        if (!handler) {\n          delete handlers[event];\n        } else {\n          handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n        }\n      });\n      return this;\n    },\n    /**\n     * emit event to the listeners\n     * @param {String} event\n     * @param {Object} data\n     */\n    emit: function (event, data) {\n      // we also want to trigger dom events\n      if (this.options.domEvents) {\n        triggerDomEvent(event, data);\n      }\n\n      // no handlers, so skip it all\n      var handlers = this.handlers[event] && this.handlers[event].slice();\n      if (!handlers || !handlers.length) {\n        return;\n      }\n      data.type = event;\n      data.preventDefault = function () {\n        data.srcEvent.preventDefault();\n      };\n      var i = 0;\n      while (i < handlers.length) {\n        handlers[i](data);\n        i++;\n      }\n    },\n    /**\n     * destroy the manager and unbinds all events\n     * it doesn't unbind dom events, that is the user own responsibility\n     */\n    destroy: function () {\n      this.element && toggleCssProps(this, false);\n      this.handlers = {};\n      this.session = {};\n      this.input.destroy();\n      this.element = null;\n    }\n  };\n\n  /**\n   * add/remove the css properties as defined in manager.options.cssProps\n   * @param {Manager} manager\n   * @param {Boolean} add\n   */\n  function toggleCssProps(manager, add) {\n    var element = manager.element;\n    if (!element.style) {\n      return;\n    }\n    var prop;\n    each(manager.options.cssProps, function (value, name) {\n      prop = prefixed(element.style, name);\n      if (add) {\n        manager.oldCssProps[prop] = element.style[prop];\n        element.style[prop] = value;\n      } else {\n        element.style[prop] = manager.oldCssProps[prop] || '';\n      }\n    });\n    if (!add) {\n      manager.oldCssProps = {};\n    }\n  }\n\n  /**\n   * trigger dom event\n   * @param {String} event\n   * @param {Object} data\n   */\n  function triggerDomEvent(event, data) {\n    var gestureEvent = document.createEvent('Event');\n    gestureEvent.initEvent(event, true, true);\n    gestureEvent.gesture = data;\n    data.target.dispatchEvent(gestureEvent);\n  }\n  assign(Hammer, {\n    INPUT_START: INPUT_START,\n    INPUT_MOVE: INPUT_MOVE,\n    INPUT_END: INPUT_END,\n    INPUT_CANCEL: INPUT_CANCEL,\n    STATE_POSSIBLE: STATE_POSSIBLE,\n    STATE_BEGAN: STATE_BEGAN,\n    STATE_CHANGED: STATE_CHANGED,\n    STATE_ENDED: STATE_ENDED,\n    STATE_RECOGNIZED: STATE_RECOGNIZED,\n    STATE_CANCELLED: STATE_CANCELLED,\n    STATE_FAILED: STATE_FAILED,\n    DIRECTION_NONE: DIRECTION_NONE,\n    DIRECTION_LEFT: DIRECTION_LEFT,\n    DIRECTION_RIGHT: DIRECTION_RIGHT,\n    DIRECTION_UP: DIRECTION_UP,\n    DIRECTION_DOWN: DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL: DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL: DIRECTION_VERTICAL,\n    DIRECTION_ALL: DIRECTION_ALL,\n    Manager: Manager,\n    Input: Input,\n    TouchAction: TouchAction,\n    TouchInput: TouchInput,\n    MouseInput: MouseInput,\n    PointerEventInput: PointerEventInput,\n    TouchMouseInput: TouchMouseInput,\n    SingleTouchInput: SingleTouchInput,\n    Recognizer: Recognizer,\n    AttrRecognizer: AttrRecognizer,\n    Tap: TapRecognizer,\n    Pan: PanRecognizer,\n    Swipe: SwipeRecognizer,\n    Pinch: PinchRecognizer,\n    Rotate: RotateRecognizer,\n    Press: PressRecognizer,\n    on: addEventListeners,\n    off: removeEventListeners,\n    each: each,\n    merge: merge,\n    extend: extend,\n    assign: assign,\n    inherit: inherit,\n    bindFn: bindFn,\n    prefixed: prefixed\n  });\n\n  // this prevents errors when Hammer is loaded in the presence of an AMD\n  //  style loader but by script tag, not by the loader.\n  var freeGlobal = typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}; // jshint ignore:line\n  freeGlobal.Hammer = Hammer;\n  if (typeof define === 'function' && define.amd) {\n    define(function () {\n      return Hammer;\n    });\n  } else if (typeof module != 'undefined' && module.exports) {\n    module.exports = Hammer;\n  } else {\n    window[exportName] = Hammer;\n  }\n})(window, document, 'Hammer');", "map": {"version": 3, "names": ["window", "document", "exportName", "undefined", "VENDOR_PREFIXES", "TEST_ELEMENT", "createElement", "TYPE_FUNCTION", "round", "Math", "abs", "now", "Date", "setTimeoutContext", "fn", "timeout", "context", "setTimeout", "bindFn", "invokeArrayArg", "arg", "Array", "isArray", "each", "obj", "iterator", "i", "for<PERSON>ach", "length", "call", "hasOwnProperty", "deprecate", "method", "name", "message", "deprecationMessage", "e", "Error", "stack", "replace", "log", "console", "warn", "apply", "arguments", "assign", "Object", "target", "TypeError", "output", "index", "source", "<PERSON><PERSON><PERSON>", "extend", "dest", "src", "merge", "keys", "inherit", "child", "base", "properties", "baseP", "prototype", "childP", "create", "constructor", "_super", "boundFn", "boolOrFn", "val", "args", "ifUndefined", "val1", "val2", "addEventListeners", "types", "handler", "splitStr", "type", "addEventListener", "removeEventListeners", "removeEventListener", "hasParent", "node", "parent", "parentNode", "inStr", "str", "find", "indexOf", "trim", "split", "inArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toArray", "slice", "uniqueArray", "key", "sort", "results", "values", "push", "sortUniqueArray", "a", "b", "prefixed", "property", "prefix", "prop", "camelProp", "toUpperCase", "_uniqueId", "uniqueId", "getWindowForElement", "element", "doc", "ownerDocument", "defaultView", "parentWindow", "MOBILE_REGEX", "SUPPORT_TOUCH", "SUPPORT_POINTER_EVENTS", "SUPPORT_ONLY_TOUCH", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_PEN", "INPUT_TYPE_MOUSE", "INPUT_TYPE_KINECT", "COMPUTE_INTERVAL", "INPUT_START", "INPUT_MOVE", "INPUT_END", "INPUT_CANCEL", "DIRECTION_NONE", "DIRECTION_LEFT", "DIRECTION_RIGHT", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_ALL", "PROPS_XY", "PROPS_CLIENT_XY", "Input", "manager", "callback", "self", "options", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "enable", "init", "evEl", "ev<PERSON><PERSON><PERSON>", "evWin", "destroy", "createInputInstance", "Type", "inputClass", "PointerEventInput", "TouchInput", "MouseInput", "TouchMouseInput", "inputHandler", "eventType", "input", "pointersLen", "pointers", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "isFinal", "session", "computeInputData", "emit", "recognize", "prevInput", "pointers<PERSON><PERSON><PERSON>", "firstInput", "simpleCloneInputData", "firstMultiple", "offsetCenter", "center", "getCenter", "timeStamp", "deltaTime", "angle", "getAngle", "distance", "getDistance", "computeDeltaXY", "offsetDirection", "getDirection", "deltaX", "deltaY", "overallVelocity", "getVelocity", "overallVelocityX", "x", "overallVelocityY", "y", "scale", "getScale", "rotation", "getRotation", "maxPointers", "computeIntervalInputData", "srcEvent", "offset", "offsetDelta", "prevDel<PERSON>", "last", "lastInterval", "velocity", "velocityX", "velocityY", "direction", "v", "clientX", "clientY", "p1", "p2", "props", "sqrt", "atan2", "PI", "start", "end", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "MOUSE_ELEMENT_EVENTS", "MOUSE_WINDOW_EVENTS", "pressed", "MEhandler", "button", "which", "pointerType", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "MSPointerEvent", "PointerEvent", "store", "pointerEvents", "PEhandler", "removePointer", "eventTypeNormalized", "toLowerCase", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "splice", "SINGLE_TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "SINGLE_TOUCH_TARGET_EVENTS", "SINGLE_TOUCH_WINDOW_EVENTS", "SingleTouchInput", "started", "TE<PERSON>ler", "touches", "normalizeSingleTouches", "all", "changed", "changedTouches", "concat", "TOUCH_INPUT_MAP", "TOUCH_TARGET_EVENTS", "targetIds", "MTEhandler", "getTouches", "allTouches", "identifier", "targetTouches", "changedTargetTouches", "filter", "touch", "DEDUP_TIMEOUT", "DEDUP_DISTANCE", "mouse", "primaryTouch", "lastTouches", "TMEhandler", "inputEvent", "inputData", "isMouse", "sourceCapabilities", "firesTouchEvents", "recordTouches", "isSyntheticEvent", "eventData", "setLastTouch", "lastTouch", "lts", "removeLastTouch", "t", "dx", "dy", "PREFIXED_TOUCH_ACTION", "style", "NATIVE_TOUCH_ACTION", "TOUCH_ACTION_COMPUTE", "TOUCH_ACTION_AUTO", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_NONE", "TOUCH_ACTION_PAN_X", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MAP", "getTouchActionProps", "TouchAction", "value", "set", "compute", "actions", "update", "touchAction", "recognizers", "recognizer", "getTouchAction", "cleanTouchActions", "join", "preventDefaults", "prevented", "preventDefault", "hasNone", "hasPanY", "hasPanX", "isTapPointer", "isTapMovement", "isTapTouchTime", "preventSrc", "touchMap", "cssSupports", "CSS", "supports", "STATE_POSSIBLE", "STATE_BEGAN", "STATE_CHANGED", "STATE_ENDED", "STATE_RECOGNIZED", "STATE_CANCELLED", "STATE_FAILED", "Recognizer", "defaults", "id", "state", "simultaneous", "requireFail", "recognizeWith", "otherRecognizer", "getRecognizerByNameIfManager", "dropRecognizeWith", "requireFailure", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "event", "stateStr", "additionalEvent", "tryEmit", "canEmit", "inputDataClone", "reset", "process", "directionStr", "get", "AttrRecognizer", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "PanRecognizer", "pX", "pY", "threshold", "directionTest", "hasMoved", "PinchRecognizer", "inOut", "PressRecognizer", "_timer", "_input", "time", "validPointers", "validMovement", "validTime", "clearTimeout", "RotateRecognizer", "SwipeRecognizer", "TapRecognizer", "pTime", "pCenter", "count", "taps", "interval", "pos<PERSON><PERSON><PERSON><PERSON>", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "tapCount", "Hammer", "preset", "Manager", "VERSION", "domEvents", "cssProps", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "STOP", "FORCED_STOP", "handlers", "oldCssProps", "toggleCssProps", "item", "add", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "on", "events", "off", "data", "triggerDomEvent", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press", "freeGlobal", "define", "amd", "module", "exports"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/hammerjs/hammer.js"], "sourcesContent": ["/*! Hammer.JS - v2.0.7 - 2016-04-22\n * http://hammerjs.github.io/\n *\n * Copyright (c) 2016 <PERSON><PERSON>;\n * Licensed under the MIT license */\n(function(window, document, exportName, undefined) {\n  'use strict';\n\nvar VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\nvar TEST_ELEMENT = document.createElement('div');\n\nvar TYPE_FUNCTION = 'function';\n\nvar round = Math.round;\nvar abs = Math.abs;\nvar now = Date.now;\n\n/**\n * set a timeout with a given scope\n * @param {Function} fn\n * @param {Number} timeout\n * @param {Object} context\n * @returns {number}\n */\nfunction setTimeoutContext(fn, timeout, context) {\n    return setTimeout(bindFn(fn, context), timeout);\n}\n\n/**\n * if the argument is an array, we want to execute the fn on each entry\n * if it aint an array we don't want to do a thing.\n * this is used by all the methods that accept a single and array argument.\n * @param {*|Array} arg\n * @param {String} fn\n * @param {Object} [context]\n * @returns {Boolean}\n */\nfunction invokeArrayArg(arg, fn, context) {\n    if (Array.isArray(arg)) {\n        each(arg, context[fn], context);\n        return true;\n    }\n    return false;\n}\n\n/**\n * walk objects and arrays\n * @param {Object} obj\n * @param {Function} iterator\n * @param {Object} context\n */\nfunction each(obj, iterator, context) {\n    var i;\n\n    if (!obj) {\n        return;\n    }\n\n    if (obj.forEach) {\n        obj.forEach(iterator, context);\n    } else if (obj.length !== undefined) {\n        i = 0;\n        while (i < obj.length) {\n            iterator.call(context, obj[i], i, obj);\n            i++;\n        }\n    } else {\n        for (i in obj) {\n            obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n        }\n    }\n}\n\n/**\n * wrap a method with a deprecation warning and stack trace\n * @param {Function} method\n * @param {String} name\n * @param {String} message\n * @returns {Function} A new function wrapping the supplied method.\n */\nfunction deprecate(method, name, message) {\n    var deprecationMessage = 'DEPRECATED METHOD: ' + name + '\\n' + message + ' AT \\n';\n    return function() {\n        var e = new Error('get-stack-trace');\n        var stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '')\n            .replace(/^\\s+at\\s+/gm, '')\n            .replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n\n        var log = window.console && (window.console.warn || window.console.log);\n        if (log) {\n            log.call(window.console, deprecationMessage, stack);\n        }\n        return method.apply(this, arguments);\n    };\n}\n\n/**\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} target\n * @param {...Object} objects_to_assign\n * @returns {Object} target\n */\nvar assign;\nif (typeof Object.assign !== 'function') {\n    assign = function assign(target) {\n        if (target === undefined || target === null) {\n            throw new TypeError('Cannot convert undefined or null to object');\n        }\n\n        var output = Object(target);\n        for (var index = 1; index < arguments.length; index++) {\n            var source = arguments[index];\n            if (source !== undefined && source !== null) {\n                for (var nextKey in source) {\n                    if (source.hasOwnProperty(nextKey)) {\n                        output[nextKey] = source[nextKey];\n                    }\n                }\n            }\n        }\n        return output;\n    };\n} else {\n    assign = Object.assign;\n}\n\n/**\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} dest\n * @param {Object} src\n * @param {Boolean} [merge=false]\n * @returns {Object} dest\n */\nvar extend = deprecate(function extend(dest, src, merge) {\n    var keys = Object.keys(src);\n    var i = 0;\n    while (i < keys.length) {\n        if (!merge || (merge && dest[keys[i]] === undefined)) {\n            dest[keys[i]] = src[keys[i]];\n        }\n        i++;\n    }\n    return dest;\n}, 'extend', 'Use `assign`.');\n\n/**\n * merge the values from src in the dest.\n * means that properties that exist in dest will not be overwritten by src\n * @param {Object} dest\n * @param {Object} src\n * @returns {Object} dest\n */\nvar merge = deprecate(function merge(dest, src) {\n    return extend(dest, src, true);\n}, 'merge', 'Use `assign`.');\n\n/**\n * simple class inheritance\n * @param {Function} child\n * @param {Function} base\n * @param {Object} [properties]\n */\nfunction inherit(child, base, properties) {\n    var baseP = base.prototype,\n        childP;\n\n    childP = child.prototype = Object.create(baseP);\n    childP.constructor = child;\n    childP._super = baseP;\n\n    if (properties) {\n        assign(childP, properties);\n    }\n}\n\n/**\n * simple function bind\n * @param {Function} fn\n * @param {Object} context\n * @returns {Function}\n */\nfunction bindFn(fn, context) {\n    return function boundFn() {\n        return fn.apply(context, arguments);\n    };\n}\n\n/**\n * let a boolean value also be a function that must return a boolean\n * this first item in args will be used as the context\n * @param {Boolean|Function} val\n * @param {Array} [args]\n * @returns {Boolean}\n */\nfunction boolOrFn(val, args) {\n    if (typeof val == TYPE_FUNCTION) {\n        return val.apply(args ? args[0] || undefined : undefined, args);\n    }\n    return val;\n}\n\n/**\n * use the val2 when val1 is undefined\n * @param {*} val1\n * @param {*} val2\n * @returns {*}\n */\nfunction ifUndefined(val1, val2) {\n    return (val1 === undefined) ? val2 : val1;\n}\n\n/**\n * addEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nfunction addEventListeners(target, types, handler) {\n    each(splitStr(types), function(type) {\n        target.addEventListener(type, handler, false);\n    });\n}\n\n/**\n * removeEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nfunction removeEventListeners(target, types, handler) {\n    each(splitStr(types), function(type) {\n        target.removeEventListener(type, handler, false);\n    });\n}\n\n/**\n * find if a node is in the given parent\n * @method hasParent\n * @param {HTMLElement} node\n * @param {HTMLElement} parent\n * @return {Boolean} found\n */\nfunction hasParent(node, parent) {\n    while (node) {\n        if (node == parent) {\n            return true;\n        }\n        node = node.parentNode;\n    }\n    return false;\n}\n\n/**\n * small indexOf wrapper\n * @param {String} str\n * @param {String} find\n * @returns {Boolean} found\n */\nfunction inStr(str, find) {\n    return str.indexOf(find) > -1;\n}\n\n/**\n * split string on whitespace\n * @param {String} str\n * @returns {Array} words\n */\nfunction splitStr(str) {\n    return str.trim().split(/\\s+/g);\n}\n\n/**\n * find if a array contains the object using indexOf or a simple polyFill\n * @param {Array} src\n * @param {String} find\n * @param {String} [findByKey]\n * @return {Boolean|Number} false when not found, or the index\n */\nfunction inArray(src, find, findByKey) {\n    if (src.indexOf && !findByKey) {\n        return src.indexOf(find);\n    } else {\n        var i = 0;\n        while (i < src.length) {\n            if ((findByKey && src[i][findByKey] == find) || (!findByKey && src[i] === find)) {\n                return i;\n            }\n            i++;\n        }\n        return -1;\n    }\n}\n\n/**\n * convert array-like objects to real arrays\n * @param {Object} obj\n * @returns {Array}\n */\nfunction toArray(obj) {\n    return Array.prototype.slice.call(obj, 0);\n}\n\n/**\n * unique array with objects based on a key (like 'id') or just by the array's value\n * @param {Array} src [{id:1},{id:2},{id:1}]\n * @param {String} [key]\n * @param {Boolean} [sort=False]\n * @returns {Array} [{id:1},{id:2}]\n */\nfunction uniqueArray(src, key, sort) {\n    var results = [];\n    var values = [];\n    var i = 0;\n\n    while (i < src.length) {\n        var val = key ? src[i][key] : src[i];\n        if (inArray(values, val) < 0) {\n            results.push(src[i]);\n        }\n        values[i] = val;\n        i++;\n    }\n\n    if (sort) {\n        if (!key) {\n            results = results.sort();\n        } else {\n            results = results.sort(function sortUniqueArray(a, b) {\n                return a[key] > b[key];\n            });\n        }\n    }\n\n    return results;\n}\n\n/**\n * get the prefixed property\n * @param {Object} obj\n * @param {String} property\n * @returns {String|Undefined} prefixed\n */\nfunction prefixed(obj, property) {\n    var prefix, prop;\n    var camelProp = property[0].toUpperCase() + property.slice(1);\n\n    var i = 0;\n    while (i < VENDOR_PREFIXES.length) {\n        prefix = VENDOR_PREFIXES[i];\n        prop = (prefix) ? prefix + camelProp : property;\n\n        if (prop in obj) {\n            return prop;\n        }\n        i++;\n    }\n    return undefined;\n}\n\n/**\n * get a unique id\n * @returns {number} uniqueId\n */\nvar _uniqueId = 1;\nfunction uniqueId() {\n    return _uniqueId++;\n}\n\n/**\n * get the window object of an element\n * @param {HTMLElement} element\n * @returns {DocumentView|Window}\n */\nfunction getWindowForElement(element) {\n    var doc = element.ownerDocument || element;\n    return (doc.defaultView || doc.parentWindow || window);\n}\n\nvar MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n\nvar SUPPORT_TOUCH = ('ontouchstart' in window);\nvar SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\nvar SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n\nvar INPUT_TYPE_TOUCH = 'touch';\nvar INPUT_TYPE_PEN = 'pen';\nvar INPUT_TYPE_MOUSE = 'mouse';\nvar INPUT_TYPE_KINECT = 'kinect';\n\nvar COMPUTE_INTERVAL = 25;\n\nvar INPUT_START = 1;\nvar INPUT_MOVE = 2;\nvar INPUT_END = 4;\nvar INPUT_CANCEL = 8;\n\nvar DIRECTION_NONE = 1;\nvar DIRECTION_LEFT = 2;\nvar DIRECTION_RIGHT = 4;\nvar DIRECTION_UP = 8;\nvar DIRECTION_DOWN = 16;\n\nvar DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\nvar DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\nvar DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n\nvar PROPS_XY = ['x', 'y'];\nvar PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\n/**\n * create new input type manager\n * @param {Manager} manager\n * @param {Function} callback\n * @returns {Input}\n * @constructor\n */\nfunction Input(manager, callback) {\n    var self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function(ev) {\n        if (boolOrFn(manager.options.enable, [manager])) {\n            self.handler(ev);\n        }\n    };\n\n    this.init();\n\n}\n\nInput.prototype = {\n    /**\n     * should handle the inputEvent data and trigger the callback\n     * @virtual\n     */\n    handler: function() { },\n\n    /**\n     * bind the events\n     */\n    init: function() {\n        this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n        this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n        this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    },\n\n    /**\n     * unbind the events\n     */\n    destroy: function() {\n        this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n        this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n        this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    }\n};\n\n/**\n * create new input type manager\n * called by the Manager constructor\n * @param {Hammer} manager\n * @returns {Input}\n */\nfunction createInputInstance(manager) {\n    var Type;\n    var inputClass = manager.options.inputClass;\n\n    if (inputClass) {\n        Type = inputClass;\n    } else if (SUPPORT_POINTER_EVENTS) {\n        Type = PointerEventInput;\n    } else if (SUPPORT_ONLY_TOUCH) {\n        Type = TouchInput;\n    } else if (!SUPPORT_TOUCH) {\n        Type = MouseInput;\n    } else {\n        Type = TouchMouseInput;\n    }\n    return new (Type)(manager, inputHandler);\n}\n\n/**\n * handle input events\n * @param {Manager} manager\n * @param {String} eventType\n * @param {Object} input\n */\nfunction inputHandler(manager, eventType, input) {\n    var pointersLen = input.pointers.length;\n    var changedPointersLen = input.changedPointers.length;\n    var isFirst = (eventType & INPUT_START && (pointersLen - changedPointersLen === 0));\n    var isFinal = (eventType & (INPUT_END | INPUT_CANCEL) && (pointersLen - changedPointersLen === 0));\n\n    input.isFirst = !!isFirst;\n    input.isFinal = !!isFinal;\n\n    if (isFirst) {\n        manager.session = {};\n    }\n\n    // source event is the normalized value of the domEvents\n    // like 'touchstart, mouseup, pointerdown'\n    input.eventType = eventType;\n\n    // compute scale, rotation etc\n    computeInputData(manager, input);\n\n    // emit secret event\n    manager.emit('hammer.input', input);\n\n    manager.recognize(input);\n    manager.session.prevInput = input;\n}\n\n/**\n * extend the data with some usable properties like scale, rotate, velocity etc\n * @param {Object} manager\n * @param {Object} input\n */\nfunction computeInputData(manager, input) {\n    var session = manager.session;\n    var pointers = input.pointers;\n    var pointersLength = pointers.length;\n\n    // store the first input to calculate the distance and direction\n    if (!session.firstInput) {\n        session.firstInput = simpleCloneInputData(input);\n    }\n\n    // to compute scale and rotation we need to store the multiple touches\n    if (pointersLength > 1 && !session.firstMultiple) {\n        session.firstMultiple = simpleCloneInputData(input);\n    } else if (pointersLength === 1) {\n        session.firstMultiple = false;\n    }\n\n    var firstInput = session.firstInput;\n    var firstMultiple = session.firstMultiple;\n    var offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n\n    var center = input.center = getCenter(pointers);\n    input.timeStamp = now();\n    input.deltaTime = input.timeStamp - firstInput.timeStamp;\n\n    input.angle = getAngle(offsetCenter, center);\n    input.distance = getDistance(offsetCenter, center);\n\n    computeDeltaXY(session, input);\n    input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n\n    var overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n    input.overallVelocityX = overallVelocity.x;\n    input.overallVelocityY = overallVelocity.y;\n    input.overallVelocity = (abs(overallVelocity.x) > abs(overallVelocity.y)) ? overallVelocity.x : overallVelocity.y;\n\n    input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n    input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n\n    input.maxPointers = !session.prevInput ? input.pointers.length : ((input.pointers.length >\n        session.prevInput.maxPointers) ? input.pointers.length : session.prevInput.maxPointers);\n\n    computeIntervalInputData(session, input);\n\n    // find the correct target\n    var target = manager.element;\n    if (hasParent(input.srcEvent.target, target)) {\n        target = input.srcEvent.target;\n    }\n    input.target = target;\n}\n\nfunction computeDeltaXY(session, input) {\n    var center = input.center;\n    var offset = session.offsetDelta || {};\n    var prevDelta = session.prevDelta || {};\n    var prevInput = session.prevInput || {};\n\n    if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n        prevDelta = session.prevDelta = {\n            x: prevInput.deltaX || 0,\n            y: prevInput.deltaY || 0\n        };\n\n        offset = session.offsetDelta = {\n            x: center.x,\n            y: center.y\n        };\n    }\n\n    input.deltaX = prevDelta.x + (center.x - offset.x);\n    input.deltaY = prevDelta.y + (center.y - offset.y);\n}\n\n/**\n * velocity is calculated every x ms\n * @param {Object} session\n * @param {Object} input\n */\nfunction computeIntervalInputData(session, input) {\n    var last = session.lastInterval || input,\n        deltaTime = input.timeStamp - last.timeStamp,\n        velocity, velocityX, velocityY, direction;\n\n    if (input.eventType != INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n        var deltaX = input.deltaX - last.deltaX;\n        var deltaY = input.deltaY - last.deltaY;\n\n        var v = getVelocity(deltaTime, deltaX, deltaY);\n        velocityX = v.x;\n        velocityY = v.y;\n        velocity = (abs(v.x) > abs(v.y)) ? v.x : v.y;\n        direction = getDirection(deltaX, deltaY);\n\n        session.lastInterval = input;\n    } else {\n        // use latest velocity info if it doesn't overtake a minimum period\n        velocity = last.velocity;\n        velocityX = last.velocityX;\n        velocityY = last.velocityY;\n        direction = last.direction;\n    }\n\n    input.velocity = velocity;\n    input.velocityX = velocityX;\n    input.velocityY = velocityY;\n    input.direction = direction;\n}\n\n/**\n * create a simple clone from the input used for storage of firstInput and firstMultiple\n * @param {Object} input\n * @returns {Object} clonedInputData\n */\nfunction simpleCloneInputData(input) {\n    // make a simple copy of the pointers because we will get a reference if we don't\n    // we only need clientXY for the calculations\n    var pointers = [];\n    var i = 0;\n    while (i < input.pointers.length) {\n        pointers[i] = {\n            clientX: round(input.pointers[i].clientX),\n            clientY: round(input.pointers[i].clientY)\n        };\n        i++;\n    }\n\n    return {\n        timeStamp: now(),\n        pointers: pointers,\n        center: getCenter(pointers),\n        deltaX: input.deltaX,\n        deltaY: input.deltaY\n    };\n}\n\n/**\n * get the center of all the pointers\n * @param {Array} pointers\n * @return {Object} center contains `x` and `y` properties\n */\nfunction getCenter(pointers) {\n    var pointersLength = pointers.length;\n\n    // no need to loop when only one touch\n    if (pointersLength === 1) {\n        return {\n            x: round(pointers[0].clientX),\n            y: round(pointers[0].clientY)\n        };\n    }\n\n    var x = 0, y = 0, i = 0;\n    while (i < pointersLength) {\n        x += pointers[i].clientX;\n        y += pointers[i].clientY;\n        i++;\n    }\n\n    return {\n        x: round(x / pointersLength),\n        y: round(y / pointersLength)\n    };\n}\n\n/**\n * calculate the velocity between two points. unit is in px per ms.\n * @param {Number} deltaTime\n * @param {Number} x\n * @param {Number} y\n * @return {Object} velocity `x` and `y`\n */\nfunction getVelocity(deltaTime, x, y) {\n    return {\n        x: x / deltaTime || 0,\n        y: y / deltaTime || 0\n    };\n}\n\n/**\n * get the direction between two points\n * @param {Number} x\n * @param {Number} y\n * @return {Number} direction\n */\nfunction getDirection(x, y) {\n    if (x === y) {\n        return DIRECTION_NONE;\n    }\n\n    if (abs(x) >= abs(y)) {\n        return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n}\n\n/**\n * calculate the absolute distance between two points\n * @param {Object} p1 {x, y}\n * @param {Object} p2 {x, y}\n * @param {Array} [props] containing x and y keys\n * @return {Number} distance\n */\nfunction getDistance(p1, p2, props) {\n    if (!props) {\n        props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n        y = p2[props[1]] - p1[props[1]];\n\n    return Math.sqrt((x * x) + (y * y));\n}\n\n/**\n * calculate the angle between two coordinates\n * @param {Object} p1\n * @param {Object} p2\n * @param {Array} [props] containing x and y keys\n * @return {Number} angle\n */\nfunction getAngle(p1, p2, props) {\n    if (!props) {\n        props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n        y = p2[props[1]] - p1[props[1]];\n    return Math.atan2(y, x) * 180 / Math.PI;\n}\n\n/**\n * calculate the rotation degrees between two pointersets\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} rotation\n */\nfunction getRotation(start, end) {\n    return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n}\n\n/**\n * calculate the scale factor between two pointersets\n * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} scale\n */\nfunction getScale(start, end) {\n    return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n}\n\nvar MOUSE_INPUT_MAP = {\n    mousedown: INPUT_START,\n    mousemove: INPUT_MOVE,\n    mouseup: INPUT_END\n};\n\nvar MOUSE_ELEMENT_EVENTS = 'mousedown';\nvar MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n/**\n * Mouse events input\n * @constructor\n * @extends Input\n */\nfunction MouseInput() {\n    this.evEl = MOUSE_ELEMENT_EVENTS;\n    this.evWin = MOUSE_WINDOW_EVENTS;\n\n    this.pressed = false; // mousedown state\n\n    Input.apply(this, arguments);\n}\n\ninherit(MouseInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function MEhandler(ev) {\n        var eventType = MOUSE_INPUT_MAP[ev.type];\n\n        // on start we want to have the left mouse button down\n        if (eventType & INPUT_START && ev.button === 0) {\n            this.pressed = true;\n        }\n\n        if (eventType & INPUT_MOVE && ev.which !== 1) {\n            eventType = INPUT_END;\n        }\n\n        // mouse must be down\n        if (!this.pressed) {\n            return;\n        }\n\n        if (eventType & INPUT_END) {\n            this.pressed = false;\n        }\n\n        this.callback(this.manager, eventType, {\n            pointers: [ev],\n            changedPointers: [ev],\n            pointerType: INPUT_TYPE_MOUSE,\n            srcEvent: ev\n        });\n    }\n});\n\nvar POINTER_INPUT_MAP = {\n    pointerdown: INPUT_START,\n    pointermove: INPUT_MOVE,\n    pointerup: INPUT_END,\n    pointercancel: INPUT_CANCEL,\n    pointerout: INPUT_CANCEL\n};\n\n// in IE10 the pointer types is defined as an enum\nvar IE10_POINTER_TYPE_ENUM = {\n    2: INPUT_TYPE_TOUCH,\n    3: INPUT_TYPE_PEN,\n    4: INPUT_TYPE_MOUSE,\n    5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n};\n\nvar POINTER_ELEMENT_EVENTS = 'pointerdown';\nvar POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n// IE10 has prefixed support, and case-sensitive\nif (window.MSPointerEvent && !window.PointerEvent) {\n    POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n    POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n}\n\n/**\n * Pointer events input\n * @constructor\n * @extends Input\n */\nfunction PointerEventInput() {\n    this.evEl = POINTER_ELEMENT_EVENTS;\n    this.evWin = POINTER_WINDOW_EVENTS;\n\n    Input.apply(this, arguments);\n\n    this.store = (this.manager.session.pointerEvents = []);\n}\n\ninherit(PointerEventInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function PEhandler(ev) {\n        var store = this.store;\n        var removePointer = false;\n\n        var eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n        var eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n        var pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n\n        var isTouch = (pointerType == INPUT_TYPE_TOUCH);\n\n        // get index of the event in the store\n        var storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n        // start and mouse must be down\n        if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n            if (storeIndex < 0) {\n                store.push(ev);\n                storeIndex = store.length - 1;\n            }\n        } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n            removePointer = true;\n        }\n\n        // it not found, so the pointer hasn't been down (so it's probably a hover)\n        if (storeIndex < 0) {\n            return;\n        }\n\n        // update the event in the store\n        store[storeIndex] = ev;\n\n        this.callback(this.manager, eventType, {\n            pointers: store,\n            changedPointers: [ev],\n            pointerType: pointerType,\n            srcEvent: ev\n        });\n\n        if (removePointer) {\n            // remove from the store\n            store.splice(storeIndex, 1);\n        }\n    }\n});\n\nvar SINGLE_TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n};\n\nvar SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\nvar SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * Touch events input\n * @constructor\n * @extends Input\n */\nfunction SingleTouchInput() {\n    this.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    this.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n    this.started = false;\n\n    Input.apply(this, arguments);\n}\n\ninherit(SingleTouchInput, Input, {\n    handler: function TEhandler(ev) {\n        var type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n        // should we handle the touch events?\n        if (type === INPUT_START) {\n            this.started = true;\n        }\n\n        if (!this.started) {\n            return;\n        }\n\n        var touches = normalizeSingleTouches.call(this, ev, type);\n\n        // when done, reset the started state\n        if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n            this.started = false;\n        }\n\n        this.callback(this.manager, type, {\n            pointers: touches[0],\n            changedPointers: touches[1],\n            pointerType: INPUT_TYPE_TOUCH,\n            srcEvent: ev\n        });\n    }\n});\n\n/**\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction normalizeSingleTouches(ev, type) {\n    var all = toArray(ev.touches);\n    var changed = toArray(ev.changedTouches);\n\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n        all = uniqueArray(all.concat(changed), 'identifier', true);\n    }\n\n    return [all, changed];\n}\n\nvar TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n};\n\nvar TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * Multi-user touch events input\n * @constructor\n * @extends Input\n */\nfunction TouchInput() {\n    this.evTarget = TOUCH_TARGET_EVENTS;\n    this.targetIds = {};\n\n    Input.apply(this, arguments);\n}\n\ninherit(TouchInput, Input, {\n    handler: function MTEhandler(ev) {\n        var type = TOUCH_INPUT_MAP[ev.type];\n        var touches = getTouches.call(this, ev, type);\n        if (!touches) {\n            return;\n        }\n\n        this.callback(this.manager, type, {\n            pointers: touches[0],\n            changedPointers: touches[1],\n            pointerType: INPUT_TYPE_TOUCH,\n            srcEvent: ev\n        });\n    }\n});\n\n/**\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction getTouches(ev, type) {\n    var allTouches = toArray(ev.touches);\n    var targetIds = this.targetIds;\n\n    // when there is only one touch, the process can be simplified\n    if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n        targetIds[allTouches[0].identifier] = true;\n        return [allTouches, allTouches];\n    }\n\n    var i,\n        targetTouches,\n        changedTouches = toArray(ev.changedTouches),\n        changedTargetTouches = [],\n        target = this.target;\n\n    // get target touches from touches\n    targetTouches = allTouches.filter(function(touch) {\n        return hasParent(touch.target, target);\n    });\n\n    // collect touches\n    if (type === INPUT_START) {\n        i = 0;\n        while (i < targetTouches.length) {\n            targetIds[targetTouches[i].identifier] = true;\n            i++;\n        }\n    }\n\n    // filter changed touches to only contain touches that exist in the collected target ids\n    i = 0;\n    while (i < changedTouches.length) {\n        if (targetIds[changedTouches[i].identifier]) {\n            changedTargetTouches.push(changedTouches[i]);\n        }\n\n        // cleanup removed touches\n        if (type & (INPUT_END | INPUT_CANCEL)) {\n            delete targetIds[changedTouches[i].identifier];\n        }\n        i++;\n    }\n\n    if (!changedTargetTouches.length) {\n        return;\n    }\n\n    return [\n        // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n        uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true),\n        changedTargetTouches\n    ];\n}\n\n/**\n * Combined touch and mouse input\n *\n * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n * This because touch devices also emit mouse events while doing a touch.\n *\n * @constructor\n * @extends Input\n */\n\nvar DEDUP_TIMEOUT = 2500;\nvar DEDUP_DISTANCE = 25;\n\nfunction TouchMouseInput() {\n    Input.apply(this, arguments);\n\n    var handler = bindFn(this.handler, this);\n    this.touch = new TouchInput(this.manager, handler);\n    this.mouse = new MouseInput(this.manager, handler);\n\n    this.primaryTouch = null;\n    this.lastTouches = [];\n}\n\ninherit(TouchMouseInput, Input, {\n    /**\n     * handle mouse and touch events\n     * @param {Hammer} manager\n     * @param {String} inputEvent\n     * @param {Object} inputData\n     */\n    handler: function TMEhandler(manager, inputEvent, inputData) {\n        var isTouch = (inputData.pointerType == INPUT_TYPE_TOUCH),\n            isMouse = (inputData.pointerType == INPUT_TYPE_MOUSE);\n\n        if (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n            return;\n        }\n\n        // when we're in a touch event, record touches to  de-dupe synthetic mouse event\n        if (isTouch) {\n            recordTouches.call(this, inputEvent, inputData);\n        } else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n            return;\n        }\n\n        this.callback(manager, inputEvent, inputData);\n    },\n\n    /**\n     * remove the event listeners\n     */\n    destroy: function destroy() {\n        this.touch.destroy();\n        this.mouse.destroy();\n    }\n});\n\nfunction recordTouches(eventType, eventData) {\n    if (eventType & INPUT_START) {\n        this.primaryTouch = eventData.changedPointers[0].identifier;\n        setLastTouch.call(this, eventData);\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n        setLastTouch.call(this, eventData);\n    }\n}\n\nfunction setLastTouch(eventData) {\n    var touch = eventData.changedPointers[0];\n\n    if (touch.identifier === this.primaryTouch) {\n        var lastTouch = {x: touch.clientX, y: touch.clientY};\n        this.lastTouches.push(lastTouch);\n        var lts = this.lastTouches;\n        var removeLastTouch = function() {\n            var i = lts.indexOf(lastTouch);\n            if (i > -1) {\n                lts.splice(i, 1);\n            }\n        };\n        setTimeout(removeLastTouch, DEDUP_TIMEOUT);\n    }\n}\n\nfunction isSyntheticEvent(eventData) {\n    var x = eventData.srcEvent.clientX, y = eventData.srcEvent.clientY;\n    for (var i = 0; i < this.lastTouches.length; i++) {\n        var t = this.lastTouches[i];\n        var dx = Math.abs(x - t.x), dy = Math.abs(y - t.y);\n        if (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n            return true;\n        }\n    }\n    return false;\n}\n\nvar PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\nvar NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\n// magical touchAction value\nvar TOUCH_ACTION_COMPUTE = 'compute';\nvar TOUCH_ACTION_AUTO = 'auto';\nvar TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\nvar TOUCH_ACTION_NONE = 'none';\nvar TOUCH_ACTION_PAN_X = 'pan-x';\nvar TOUCH_ACTION_PAN_Y = 'pan-y';\nvar TOUCH_ACTION_MAP = getTouchActionProps();\n\n/**\n * Touch Action\n * sets the touchAction property or uses the js alternative\n * @param {Manager} manager\n * @param {String} value\n * @constructor\n */\nfunction TouchAction(manager, value) {\n    this.manager = manager;\n    this.set(value);\n}\n\nTouchAction.prototype = {\n    /**\n     * set the touchAction value on the element or enable the polyfill\n     * @param {String} value\n     */\n    set: function(value) {\n        // find out the touch-action by the event handlers\n        if (value == TOUCH_ACTION_COMPUTE) {\n            value = this.compute();\n        }\n\n        if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n            this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n        }\n        this.actions = value.toLowerCase().trim();\n    },\n\n    /**\n     * just re-set the touchAction value\n     */\n    update: function() {\n        this.set(this.manager.options.touchAction);\n    },\n\n    /**\n     * compute the value for the touchAction property based on the recognizer's settings\n     * @returns {String} value\n     */\n    compute: function() {\n        var actions = [];\n        each(this.manager.recognizers, function(recognizer) {\n            if (boolOrFn(recognizer.options.enable, [recognizer])) {\n                actions = actions.concat(recognizer.getTouchAction());\n            }\n        });\n        return cleanTouchActions(actions.join(' '));\n    },\n\n    /**\n     * this method is called on each input cycle and provides the preventing of the browser behavior\n     * @param {Object} input\n     */\n    preventDefaults: function(input) {\n        var srcEvent = input.srcEvent;\n        var direction = input.offsetDirection;\n\n        // if the touch action did prevented once this session\n        if (this.manager.session.prevented) {\n            srcEvent.preventDefault();\n            return;\n        }\n\n        var actions = this.actions;\n        var hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n        var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n        var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n\n        if (hasNone) {\n            //do not prevent defaults if this is a tap gesture\n\n            var isTapPointer = input.pointers.length === 1;\n            var isTapMovement = input.distance < 2;\n            var isTapTouchTime = input.deltaTime < 250;\n\n            if (isTapPointer && isTapMovement && isTapTouchTime) {\n                return;\n            }\n        }\n\n        if (hasPanX && hasPanY) {\n            // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n            return;\n        }\n\n        if (hasNone ||\n            (hasPanY && direction & DIRECTION_HORIZONTAL) ||\n            (hasPanX && direction & DIRECTION_VERTICAL)) {\n            return this.preventSrc(srcEvent);\n        }\n    },\n\n    /**\n     * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n     * @param {Object} srcEvent\n     */\n    preventSrc: function(srcEvent) {\n        this.manager.session.prevented = true;\n        srcEvent.preventDefault();\n    }\n};\n\n/**\n * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n * @param {String} actions\n * @returns {*}\n */\nfunction cleanTouchActions(actions) {\n    // none\n    if (inStr(actions, TOUCH_ACTION_NONE)) {\n        return TOUCH_ACTION_NONE;\n    }\n\n    var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n    var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n    // if both pan-x and pan-y are set (different recognizers\n    // for different directions, e.g. horizontal pan but vertical swipe?)\n    // we need none (as otherwise with pan-x pan-y combined none of these\n    // recognizers will work, since the browser would handle all panning\n    if (hasPanX && hasPanY) {\n        return TOUCH_ACTION_NONE;\n    }\n\n    // pan-x OR pan-y\n    if (hasPanX || hasPanY) {\n        return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n    }\n\n    // manipulation\n    if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n        return TOUCH_ACTION_MANIPULATION;\n    }\n\n    return TOUCH_ACTION_AUTO;\n}\n\nfunction getTouchActionProps() {\n    if (!NATIVE_TOUCH_ACTION) {\n        return false;\n    }\n    var touchMap = {};\n    var cssSupports = window.CSS && window.CSS.supports;\n    ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach(function(val) {\n\n        // If css.supports is not supported but there is native touch-action assume it supports\n        // all values. This is the case for IE 10 and 11.\n        touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n    });\n    return touchMap;\n}\n\n/**\n * Recognizer flow explained; *\n * All recognizers have the initial state of POSSIBLE when a input session starts.\n * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n * Example session for mouse-input: mousedown -> mousemove -> mouseup\n *\n * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n * which determines with state it should be.\n *\n * If the recognizer has the state FAILED, CANCELLED or RECOGNIZED (equals ENDED), it is reset to\n * POSSIBLE to give it another change on the next cycle.\n *\n *               Possible\n *                  |\n *            +-----+---------------+\n *            |                     |\n *      +-----+-----+               |\n *      |           |               |\n *   Failed      Cancelled          |\n *                          +-------+------+\n *                          |              |\n *                      Recognized       Began\n *                                         |\n *                                      Changed\n *                                         |\n *                                  Ended/Recognized\n */\nvar STATE_POSSIBLE = 1;\nvar STATE_BEGAN = 2;\nvar STATE_CHANGED = 4;\nvar STATE_ENDED = 8;\nvar STATE_RECOGNIZED = STATE_ENDED;\nvar STATE_CANCELLED = 16;\nvar STATE_FAILED = 32;\n\n/**\n * Recognizer\n * Every recognizer needs to extend from this class.\n * @constructor\n * @param {Object} options\n */\nfunction Recognizer(options) {\n    this.options = assign({}, this.defaults, options || {});\n\n    this.id = uniqueId();\n\n    this.manager = null;\n\n    // default is enable true\n    this.options.enable = ifUndefined(this.options.enable, true);\n\n    this.state = STATE_POSSIBLE;\n\n    this.simultaneous = {};\n    this.requireFail = [];\n}\n\nRecognizer.prototype = {\n    /**\n     * @virtual\n     * @type {Object}\n     */\n    defaults: {},\n\n    /**\n     * set options\n     * @param {Object} options\n     * @return {Recognizer}\n     */\n    set: function(options) {\n        assign(this.options, options);\n\n        // also update the touchAction, in case something changed about the directions/enabled state\n        this.manager && this.manager.touchAction.update();\n        return this;\n    },\n\n    /**\n     * recognize simultaneous with an other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    recognizeWith: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n            return this;\n        }\n\n        var simultaneous = this.simultaneous;\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        if (!simultaneous[otherRecognizer.id]) {\n            simultaneous[otherRecognizer.id] = otherRecognizer;\n            otherRecognizer.recognizeWith(this);\n        }\n        return this;\n    },\n\n    /**\n     * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRecognizeWith: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n            return this;\n        }\n\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        delete this.simultaneous[otherRecognizer.id];\n        return this;\n    },\n\n    /**\n     * recognizer can only run when an other is failing\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    requireFailure: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n            return this;\n        }\n\n        var requireFail = this.requireFail;\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        if (inArray(requireFail, otherRecognizer) === -1) {\n            requireFail.push(otherRecognizer);\n            otherRecognizer.requireFailure(this);\n        }\n        return this;\n    },\n\n    /**\n     * drop the requireFailure link. it does not remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRequireFailure: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n            return this;\n        }\n\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        var index = inArray(this.requireFail, otherRecognizer);\n        if (index > -1) {\n            this.requireFail.splice(index, 1);\n        }\n        return this;\n    },\n\n    /**\n     * has require failures boolean\n     * @returns {boolean}\n     */\n    hasRequireFailures: function() {\n        return this.requireFail.length > 0;\n    },\n\n    /**\n     * if the recognizer can recognize simultaneous with an other recognizer\n     * @param {Recognizer} otherRecognizer\n     * @returns {Boolean}\n     */\n    canRecognizeWith: function(otherRecognizer) {\n        return !!this.simultaneous[otherRecognizer.id];\n    },\n\n    /**\n     * You should use `tryEmit` instead of `emit` directly to check\n     * that all the needed recognizers has failed before emitting.\n     * @param {Object} input\n     */\n    emit: function(input) {\n        var self = this;\n        var state = this.state;\n\n        function emit(event) {\n            self.manager.emit(event, input);\n        }\n\n        // 'panstart' and 'panmove'\n        if (state < STATE_ENDED) {\n            emit(self.options.event + stateStr(state));\n        }\n\n        emit(self.options.event); // simple 'eventName' events\n\n        if (input.additionalEvent) { // additional event(panleft, panright, pinchin, pinchout...)\n            emit(input.additionalEvent);\n        }\n\n        // panend and pancancel\n        if (state >= STATE_ENDED) {\n            emit(self.options.event + stateStr(state));\n        }\n    },\n\n    /**\n     * Check that all the require failure recognizers has failed,\n     * if true, it emits a gesture event,\n     * otherwise, setup the state to FAILED.\n     * @param {Object} input\n     */\n    tryEmit: function(input) {\n        if (this.canEmit()) {\n            return this.emit(input);\n        }\n        // it's failing anyway\n        this.state = STATE_FAILED;\n    },\n\n    /**\n     * can we emit?\n     * @returns {boolean}\n     */\n    canEmit: function() {\n        var i = 0;\n        while (i < this.requireFail.length) {\n            if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n                return false;\n            }\n            i++;\n        }\n        return true;\n    },\n\n    /**\n     * update the recognizer\n     * @param {Object} inputData\n     */\n    recognize: function(inputData) {\n        // make a new copy of the inputData\n        // so we can change the inputData without messing up the other recognizers\n        var inputDataClone = assign({}, inputData);\n\n        // is is enabled and allow recognizing?\n        if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n            this.reset();\n            this.state = STATE_FAILED;\n            return;\n        }\n\n        // reset when we've reached the end\n        if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n            this.state = STATE_POSSIBLE;\n        }\n\n        this.state = this.process(inputDataClone);\n\n        // the recognizer has recognized a gesture\n        // so trigger an event\n        if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n            this.tryEmit(inputDataClone);\n        }\n    },\n\n    /**\n     * return the state of the recognizer\n     * the actual recognizing happens in this method\n     * @virtual\n     * @param {Object} inputData\n     * @returns {Const} STATE\n     */\n    process: function(inputData) { }, // jshint ignore:line\n\n    /**\n     * return the preferred touch-action\n     * @virtual\n     * @returns {Array}\n     */\n    getTouchAction: function() { },\n\n    /**\n     * called when the gesture isn't allowed to recognize\n     * like when another is being recognized or it is disabled\n     * @virtual\n     */\n    reset: function() { }\n};\n\n/**\n * get a usable string, used as event postfix\n * @param {Const} state\n * @returns {String} state\n */\nfunction stateStr(state) {\n    if (state & STATE_CANCELLED) {\n        return 'cancel';\n    } else if (state & STATE_ENDED) {\n        return 'end';\n    } else if (state & STATE_CHANGED) {\n        return 'move';\n    } else if (state & STATE_BEGAN) {\n        return 'start';\n    }\n    return '';\n}\n\n/**\n * direction cons to string\n * @param {Const} direction\n * @returns {String}\n */\nfunction directionStr(direction) {\n    if (direction == DIRECTION_DOWN) {\n        return 'down';\n    } else if (direction == DIRECTION_UP) {\n        return 'up';\n    } else if (direction == DIRECTION_LEFT) {\n        return 'left';\n    } else if (direction == DIRECTION_RIGHT) {\n        return 'right';\n    }\n    return '';\n}\n\n/**\n * get a recognizer by name if it is bound to a manager\n * @param {Recognizer|String} otherRecognizer\n * @param {Recognizer} recognizer\n * @returns {Recognizer}\n */\nfunction getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n    var manager = recognizer.manager;\n    if (manager) {\n        return manager.get(otherRecognizer);\n    }\n    return otherRecognizer;\n}\n\n/**\n * This recognizer is just used as a base for the simple attribute recognizers.\n * @constructor\n * @extends Recognizer\n */\nfunction AttrRecognizer() {\n    Recognizer.apply(this, arguments);\n}\n\ninherit(AttrRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof AttrRecognizer\n     */\n    defaults: {\n        /**\n         * @type {Number}\n         * @default 1\n         */\n        pointers: 1\n    },\n\n    /**\n     * Used to check if it the recognizer receives valid input, like input.distance > 10.\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {Boolean} recognized\n     */\n    attrTest: function(input) {\n        var optionPointers = this.options.pointers;\n        return optionPointers === 0 || input.pointers.length === optionPointers;\n    },\n\n    /**\n     * Process the input and return the state for the recognizer\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {*} State\n     */\n    process: function(input) {\n        var state = this.state;\n        var eventType = input.eventType;\n\n        var isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n        var isValid = this.attrTest(input);\n\n        // on cancel input and we've recognized before, return STATE_CANCELLED\n        if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n            return state | STATE_CANCELLED;\n        } else if (isRecognized || isValid) {\n            if (eventType & INPUT_END) {\n                return state | STATE_ENDED;\n            } else if (!(state & STATE_BEGAN)) {\n                return STATE_BEGAN;\n            }\n            return state | STATE_CHANGED;\n        }\n        return STATE_FAILED;\n    }\n});\n\n/**\n * Pan\n * Recognized when the pointer is down and moved in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction PanRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n\n    this.pX = null;\n    this.pY = null;\n}\n\ninherit(PanRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PanRecognizer\n     */\n    defaults: {\n        event: 'pan',\n        threshold: 10,\n        pointers: 1,\n        direction: DIRECTION_ALL\n    },\n\n    getTouchAction: function() {\n        var direction = this.options.direction;\n        var actions = [];\n        if (direction & DIRECTION_HORIZONTAL) {\n            actions.push(TOUCH_ACTION_PAN_Y);\n        }\n        if (direction & DIRECTION_VERTICAL) {\n            actions.push(TOUCH_ACTION_PAN_X);\n        }\n        return actions;\n    },\n\n    directionTest: function(input) {\n        var options = this.options;\n        var hasMoved = true;\n        var distance = input.distance;\n        var direction = input.direction;\n        var x = input.deltaX;\n        var y = input.deltaY;\n\n        // lock to axis?\n        if (!(direction & options.direction)) {\n            if (options.direction & DIRECTION_HORIZONTAL) {\n                direction = (x === 0) ? DIRECTION_NONE : (x < 0) ? DIRECTION_LEFT : DIRECTION_RIGHT;\n                hasMoved = x != this.pX;\n                distance = Math.abs(input.deltaX);\n            } else {\n                direction = (y === 0) ? DIRECTION_NONE : (y < 0) ? DIRECTION_UP : DIRECTION_DOWN;\n                hasMoved = y != this.pY;\n                distance = Math.abs(input.deltaY);\n            }\n        }\n        input.direction = direction;\n        return hasMoved && distance > options.threshold && direction & options.direction;\n    },\n\n    attrTest: function(input) {\n        return AttrRecognizer.prototype.attrTest.call(this, input) &&\n            (this.state & STATE_BEGAN || (!(this.state & STATE_BEGAN) && this.directionTest(input)));\n    },\n\n    emit: function(input) {\n\n        this.pX = input.deltaX;\n        this.pY = input.deltaY;\n\n        var direction = directionStr(input.direction);\n\n        if (direction) {\n            input.additionalEvent = this.options.event + direction;\n        }\n        this._super.emit.call(this, input);\n    }\n});\n\n/**\n * Pinch\n * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n * @constructor\n * @extends AttrRecognizer\n */\nfunction PinchRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(PinchRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n        event: 'pinch',\n        threshold: 0,\n        pointers: 2\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_NONE];\n    },\n\n    attrTest: function(input) {\n        return this._super.attrTest.call(this, input) &&\n            (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n    },\n\n    emit: function(input) {\n        if (input.scale !== 1) {\n            var inOut = input.scale < 1 ? 'in' : 'out';\n            input.additionalEvent = this.options.event + inOut;\n        }\n        this._super.emit.call(this, input);\n    }\n});\n\n/**\n * Press\n * Recognized when the pointer is down for x ms without any movement.\n * @constructor\n * @extends Recognizer\n */\nfunction PressRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    this._timer = null;\n    this._input = null;\n}\n\ninherit(PressRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PressRecognizer\n     */\n    defaults: {\n        event: 'press',\n        pointers: 1,\n        time: 251, // minimal time of the pointer to be pressed\n        threshold: 9 // a minimal movement is ok, but keep it low\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_AUTO];\n    },\n\n    process: function(input) {\n        var options = this.options;\n        var validPointers = input.pointers.length === options.pointers;\n        var validMovement = input.distance < options.threshold;\n        var validTime = input.deltaTime > options.time;\n\n        this._input = input;\n\n        // we only allow little movement\n        // and we've reached an end event, so a tap is possible\n        if (!validMovement || !validPointers || (input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime)) {\n            this.reset();\n        } else if (input.eventType & INPUT_START) {\n            this.reset();\n            this._timer = setTimeoutContext(function() {\n                this.state = STATE_RECOGNIZED;\n                this.tryEmit();\n            }, options.time, this);\n        } else if (input.eventType & INPUT_END) {\n            return STATE_RECOGNIZED;\n        }\n        return STATE_FAILED;\n    },\n\n    reset: function() {\n        clearTimeout(this._timer);\n    },\n\n    emit: function(input) {\n        if (this.state !== STATE_RECOGNIZED) {\n            return;\n        }\n\n        if (input && (input.eventType & INPUT_END)) {\n            this.manager.emit(this.options.event + 'up', input);\n        } else {\n            this._input.timeStamp = now();\n            this.manager.emit(this.options.event, this._input);\n        }\n    }\n});\n\n/**\n * Rotate\n * Recognized when two or more pointer are moving in a circular motion.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction RotateRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(RotateRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof RotateRecognizer\n     */\n    defaults: {\n        event: 'rotate',\n        threshold: 0,\n        pointers: 2\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_NONE];\n    },\n\n    attrTest: function(input) {\n        return this._super.attrTest.call(this, input) &&\n            (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n    }\n});\n\n/**\n * Swipe\n * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction SwipeRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(SwipeRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof SwipeRecognizer\n     */\n    defaults: {\n        event: 'swipe',\n        threshold: 10,\n        velocity: 0.3,\n        direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n        pointers: 1\n    },\n\n    getTouchAction: function() {\n        return PanRecognizer.prototype.getTouchAction.call(this);\n    },\n\n    attrTest: function(input) {\n        var direction = this.options.direction;\n        var velocity;\n\n        if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n            velocity = input.overallVelocity;\n        } else if (direction & DIRECTION_HORIZONTAL) {\n            velocity = input.overallVelocityX;\n        } else if (direction & DIRECTION_VERTICAL) {\n            velocity = input.overallVelocityY;\n        }\n\n        return this._super.attrTest.call(this, input) &&\n            direction & input.offsetDirection &&\n            input.distance > this.options.threshold &&\n            input.maxPointers == this.options.pointers &&\n            abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n    },\n\n    emit: function(input) {\n        var direction = directionStr(input.offsetDirection);\n        if (direction) {\n            this.manager.emit(this.options.event + direction, input);\n        }\n\n        this.manager.emit(this.options.event, input);\n    }\n});\n\n/**\n * A tap is ecognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n * a single tap.\n *\n * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n * multi-taps being recognized.\n * @constructor\n * @extends Recognizer\n */\nfunction TapRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n}\n\ninherit(TapRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n        event: 'tap',\n        pointers: 1,\n        taps: 1,\n        interval: 300, // max time between the multi-tap taps\n        time: 250, // max time of the pointer to be down (like finger on the screen)\n        threshold: 9, // a minimal movement is ok, but keep it low\n        posThreshold: 10 // a multi-tap can be a bit off the initial position\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_MANIPULATION];\n    },\n\n    process: function(input) {\n        var options = this.options;\n\n        var validPointers = input.pointers.length === options.pointers;\n        var validMovement = input.distance < options.threshold;\n        var validTouchTime = input.deltaTime < options.time;\n\n        this.reset();\n\n        if ((input.eventType & INPUT_START) && (this.count === 0)) {\n            return this.failTimeout();\n        }\n\n        // we only allow little movement\n        // and we've reached an end event, so a tap is possible\n        if (validMovement && validTouchTime && validPointers) {\n            if (input.eventType != INPUT_END) {\n                return this.failTimeout();\n            }\n\n            var validInterval = this.pTime ? (input.timeStamp - this.pTime < options.interval) : true;\n            var validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n\n            this.pTime = input.timeStamp;\n            this.pCenter = input.center;\n\n            if (!validMultiTap || !validInterval) {\n                this.count = 1;\n            } else {\n                this.count += 1;\n            }\n\n            this._input = input;\n\n            // if tap count matches we have recognized it,\n            // else it has began recognizing...\n            var tapCount = this.count % options.taps;\n            if (tapCount === 0) {\n                // no failing requirements, immediately trigger the tap event\n                // or wait as long as the multitap interval to trigger\n                if (!this.hasRequireFailures()) {\n                    return STATE_RECOGNIZED;\n                } else {\n                    this._timer = setTimeoutContext(function() {\n                        this.state = STATE_RECOGNIZED;\n                        this.tryEmit();\n                    }, options.interval, this);\n                    return STATE_BEGAN;\n                }\n            }\n        }\n        return STATE_FAILED;\n    },\n\n    failTimeout: function() {\n        this._timer = setTimeoutContext(function() {\n            this.state = STATE_FAILED;\n        }, this.options.interval, this);\n        return STATE_FAILED;\n    },\n\n    reset: function() {\n        clearTimeout(this._timer);\n    },\n\n    emit: function() {\n        if (this.state == STATE_RECOGNIZED) {\n            this._input.tapCount = this.count;\n            this.manager.emit(this.options.event, this._input);\n        }\n    }\n});\n\n/**\n * Simple way to create a manager with a default set of recognizers.\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nfunction Hammer(element, options) {\n    options = options || {};\n    options.recognizers = ifUndefined(options.recognizers, Hammer.defaults.preset);\n    return new Manager(element, options);\n}\n\n/**\n * @const {string}\n */\nHammer.VERSION = '2.0.7';\n\n/**\n * default settings\n * @namespace\n */\nHammer.defaults = {\n    /**\n     * set if DOM events are being triggered.\n     * But this is slower and unused by simple implementations, so disabled by default.\n     * @type {Boolean}\n     * @default false\n     */\n    domEvents: false,\n\n    /**\n     * The value for the touchAction property/fallback.\n     * When set to `compute` it will magically set the correct value based on the added recognizers.\n     * @type {String}\n     * @default compute\n     */\n    touchAction: TOUCH_ACTION_COMPUTE,\n\n    /**\n     * @type {Boolean}\n     * @default true\n     */\n    enable: true,\n\n    /**\n     * EXPERIMENTAL FEATURE -- can be removed/changed\n     * Change the parent input target element.\n     * If Null, then it is being set the to main element.\n     * @type {Null|EventTarget}\n     * @default null\n     */\n    inputTarget: null,\n\n    /**\n     * force an input class\n     * @type {Null|Function}\n     * @default null\n     */\n    inputClass: null,\n\n    /**\n     * Default recognizer setup when calling `Hammer()`\n     * When creating a new Manager these will be skipped.\n     * @type {Array}\n     */\n    preset: [\n        // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n        [RotateRecognizer, {enable: false}],\n        [PinchRecognizer, {enable: false}, ['rotate']],\n        [SwipeRecognizer, {direction: DIRECTION_HORIZONTAL}],\n        [PanRecognizer, {direction: DIRECTION_HORIZONTAL}, ['swipe']],\n        [TapRecognizer],\n        [TapRecognizer, {event: 'doubletap', taps: 2}, ['tap']],\n        [PressRecognizer]\n    ],\n\n    /**\n     * Some CSS properties can be used to improve the working of Hammer.\n     * Add them to this method and they will be set when creating a new Manager.\n     * @namespace\n     */\n    cssProps: {\n        /**\n         * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n         * @type {String}\n         * @default 'none'\n         */\n        userSelect: 'none',\n\n        /**\n         * Disable the Windows Phone grippers when pressing an element.\n         * @type {String}\n         * @default 'none'\n         */\n        touchSelect: 'none',\n\n        /**\n         * Disables the default callout shown when you touch and hold a touch target.\n         * On iOS, when you touch and hold a touch target such as a link, Safari displays\n         * a callout containing information about the link. This property allows you to disable that callout.\n         * @type {String}\n         * @default 'none'\n         */\n        touchCallout: 'none',\n\n        /**\n         * Specifies whether zooming is enabled. Used by IE10>\n         * @type {String}\n         * @default 'none'\n         */\n        contentZooming: 'none',\n\n        /**\n         * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n         * @type {String}\n         * @default 'none'\n         */\n        userDrag: 'none',\n\n        /**\n         * Overrides the highlight color shown when the user taps a link or a JavaScript\n         * clickable element in iOS. This property obeys the alpha value, if specified.\n         * @type {String}\n         * @default 'rgba(0,0,0,0)'\n         */\n        tapHighlightColor: 'rgba(0,0,0,0)'\n    }\n};\n\nvar STOP = 1;\nvar FORCED_STOP = 2;\n\n/**\n * Manager\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nfunction Manager(element, options) {\n    this.options = assign({}, Hammer.defaults, options || {});\n\n    this.options.inputTarget = this.options.inputTarget || element;\n\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n\n    toggleCssProps(this, true);\n\n    each(this.options.recognizers, function(item) {\n        var recognizer = this.add(new (item[0])(item[1]));\n        item[2] && recognizer.recognizeWith(item[2]);\n        item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n}\n\nManager.prototype = {\n    /**\n     * set options\n     * @param {Object} options\n     * @returns {Manager}\n     */\n    set: function(options) {\n        assign(this.options, options);\n\n        // Options that need a little more setup\n        if (options.touchAction) {\n            this.touchAction.update();\n        }\n        if (options.inputTarget) {\n            // Clean up existing event listeners and reinitialize\n            this.input.destroy();\n            this.input.target = options.inputTarget;\n            this.input.init();\n        }\n        return this;\n    },\n\n    /**\n     * stop recognizing for this session.\n     * This session will be discarded, when a new [input]start event is fired.\n     * When forced, the recognizer cycle is stopped immediately.\n     * @param {Boolean} [force]\n     */\n    stop: function(force) {\n        this.session.stopped = force ? FORCED_STOP : STOP;\n    },\n\n    /**\n     * run the recognizers!\n     * called by the inputHandler function on every movement of the pointers (touches)\n     * it walks through all the recognizers and tries to detect the gesture that is being made\n     * @param {Object} inputData\n     */\n    recognize: function(inputData) {\n        var session = this.session;\n        if (session.stopped) {\n            return;\n        }\n\n        // run the touch-action polyfill\n        this.touchAction.preventDefaults(inputData);\n\n        var recognizer;\n        var recognizers = this.recognizers;\n\n        // this holds the recognizer that is being recognized.\n        // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n        // if no recognizer is detecting a thing, it is set to `null`\n        var curRecognizer = session.curRecognizer;\n\n        // reset when the last recognizer is recognized\n        // or when we're in a new session\n        if (!curRecognizer || (curRecognizer && curRecognizer.state & STATE_RECOGNIZED)) {\n            curRecognizer = session.curRecognizer = null;\n        }\n\n        var i = 0;\n        while (i < recognizers.length) {\n            recognizer = recognizers[i];\n\n            // find out if we are allowed try to recognize the input for this one.\n            // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n            // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n            //      that is being recognized.\n            // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n            //      this can be setup with the `recognizeWith()` method on the recognizer.\n            if (session.stopped !== FORCED_STOP && ( // 1\n                    !curRecognizer || recognizer == curRecognizer || // 2\n                    recognizer.canRecognizeWith(curRecognizer))) { // 3\n                recognizer.recognize(inputData);\n            } else {\n                recognizer.reset();\n            }\n\n            // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n            // current active recognizer. but only if we don't already have an active recognizer\n            if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n                curRecognizer = session.curRecognizer = recognizer;\n            }\n            i++;\n        }\n    },\n\n    /**\n     * get a recognizer by its event name.\n     * @param {Recognizer|String} recognizer\n     * @returns {Recognizer|Null}\n     */\n    get: function(recognizer) {\n        if (recognizer instanceof Recognizer) {\n            return recognizer;\n        }\n\n        var recognizers = this.recognizers;\n        for (var i = 0; i < recognizers.length; i++) {\n            if (recognizers[i].options.event == recognizer) {\n                return recognizers[i];\n            }\n        }\n        return null;\n    },\n\n    /**\n     * add a recognizer to the manager\n     * existing recognizers with the same event name will be removed\n     * @param {Recognizer} recognizer\n     * @returns {Recognizer|Manager}\n     */\n    add: function(recognizer) {\n        if (invokeArrayArg(recognizer, 'add', this)) {\n            return this;\n        }\n\n        // remove existing\n        var existing = this.get(recognizer.options.event);\n        if (existing) {\n            this.remove(existing);\n        }\n\n        this.recognizers.push(recognizer);\n        recognizer.manager = this;\n\n        this.touchAction.update();\n        return recognizer;\n    },\n\n    /**\n     * remove a recognizer by name or instance\n     * @param {Recognizer|String} recognizer\n     * @returns {Manager}\n     */\n    remove: function(recognizer) {\n        if (invokeArrayArg(recognizer, 'remove', this)) {\n            return this;\n        }\n\n        recognizer = this.get(recognizer);\n\n        // let's make sure this recognizer exists\n        if (recognizer) {\n            var recognizers = this.recognizers;\n            var index = inArray(recognizers, recognizer);\n\n            if (index !== -1) {\n                recognizers.splice(index, 1);\n                this.touchAction.update();\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * bind event\n     * @param {String} events\n     * @param {Function} handler\n     * @returns {EventEmitter} this\n     */\n    on: function(events, handler) {\n        if (events === undefined) {\n            return;\n        }\n        if (handler === undefined) {\n            return;\n        }\n\n        var handlers = this.handlers;\n        each(splitStr(events), function(event) {\n            handlers[event] = handlers[event] || [];\n            handlers[event].push(handler);\n        });\n        return this;\n    },\n\n    /**\n     * unbind event, leave emit blank to remove all handlers\n     * @param {String} events\n     * @param {Function} [handler]\n     * @returns {EventEmitter} this\n     */\n    off: function(events, handler) {\n        if (events === undefined) {\n            return;\n        }\n\n        var handlers = this.handlers;\n        each(splitStr(events), function(event) {\n            if (!handler) {\n                delete handlers[event];\n            } else {\n                handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n            }\n        });\n        return this;\n    },\n\n    /**\n     * emit event to the listeners\n     * @param {String} event\n     * @param {Object} data\n     */\n    emit: function(event, data) {\n        // we also want to trigger dom events\n        if (this.options.domEvents) {\n            triggerDomEvent(event, data);\n        }\n\n        // no handlers, so skip it all\n        var handlers = this.handlers[event] && this.handlers[event].slice();\n        if (!handlers || !handlers.length) {\n            return;\n        }\n\n        data.type = event;\n        data.preventDefault = function() {\n            data.srcEvent.preventDefault();\n        };\n\n        var i = 0;\n        while (i < handlers.length) {\n            handlers[i](data);\n            i++;\n        }\n    },\n\n    /**\n     * destroy the manager and unbinds all events\n     * it doesn't unbind dom events, that is the user own responsibility\n     */\n    destroy: function() {\n        this.element && toggleCssProps(this, false);\n\n        this.handlers = {};\n        this.session = {};\n        this.input.destroy();\n        this.element = null;\n    }\n};\n\n/**\n * add/remove the css properties as defined in manager.options.cssProps\n * @param {Manager} manager\n * @param {Boolean} add\n */\nfunction toggleCssProps(manager, add) {\n    var element = manager.element;\n    if (!element.style) {\n        return;\n    }\n    var prop;\n    each(manager.options.cssProps, function(value, name) {\n        prop = prefixed(element.style, name);\n        if (add) {\n            manager.oldCssProps[prop] = element.style[prop];\n            element.style[prop] = value;\n        } else {\n            element.style[prop] = manager.oldCssProps[prop] || '';\n        }\n    });\n    if (!add) {\n        manager.oldCssProps = {};\n    }\n}\n\n/**\n * trigger dom event\n * @param {String} event\n * @param {Object} data\n */\nfunction triggerDomEvent(event, data) {\n    var gestureEvent = document.createEvent('Event');\n    gestureEvent.initEvent(event, true, true);\n    gestureEvent.gesture = data;\n    data.target.dispatchEvent(gestureEvent);\n}\n\nassign(Hammer, {\n    INPUT_START: INPUT_START,\n    INPUT_MOVE: INPUT_MOVE,\n    INPUT_END: INPUT_END,\n    INPUT_CANCEL: INPUT_CANCEL,\n\n    STATE_POSSIBLE: STATE_POSSIBLE,\n    STATE_BEGAN: STATE_BEGAN,\n    STATE_CHANGED: STATE_CHANGED,\n    STATE_ENDED: STATE_ENDED,\n    STATE_RECOGNIZED: STATE_RECOGNIZED,\n    STATE_CANCELLED: STATE_CANCELLED,\n    STATE_FAILED: STATE_FAILED,\n\n    DIRECTION_NONE: DIRECTION_NONE,\n    DIRECTION_LEFT: DIRECTION_LEFT,\n    DIRECTION_RIGHT: DIRECTION_RIGHT,\n    DIRECTION_UP: DIRECTION_UP,\n    DIRECTION_DOWN: DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL: DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL: DIRECTION_VERTICAL,\n    DIRECTION_ALL: DIRECTION_ALL,\n\n    Manager: Manager,\n    Input: Input,\n    TouchAction: TouchAction,\n\n    TouchInput: TouchInput,\n    MouseInput: MouseInput,\n    PointerEventInput: PointerEventInput,\n    TouchMouseInput: TouchMouseInput,\n    SingleTouchInput: SingleTouchInput,\n\n    Recognizer: Recognizer,\n    AttrRecognizer: AttrRecognizer,\n    Tap: TapRecognizer,\n    Pan: PanRecognizer,\n    Swipe: SwipeRecognizer,\n    Pinch: PinchRecognizer,\n    Rotate: RotateRecognizer,\n    Press: PressRecognizer,\n\n    on: addEventListeners,\n    off: removeEventListeners,\n    each: each,\n    merge: merge,\n    extend: extend,\n    assign: assign,\n    inherit: inherit,\n    bindFn: bindFn,\n    prefixed: prefixed\n});\n\n// this prevents errors when Hammer is loaded in the presence of an AMD\n//  style loader but by script tag, not by the loader.\nvar freeGlobal = (typeof window !== 'undefined' ? window : (typeof self !== 'undefined' ? self : {})); // jshint ignore:line\nfreeGlobal.Hammer = Hammer;\n\nif (typeof define === 'function' && define.amd) {\n    define(function() {\n        return Hammer;\n    });\n} else if (typeof module != 'undefined' && module.exports) {\n    module.exports = Hammer;\n} else {\n    window[exportName] = Hammer;\n}\n\n})(window, document, 'Hammer');\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASA,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACjD,YAAY;;EAEd,IAAIC,eAAe,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC5D,IAAIC,YAAY,GAAGJ,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAEhD,IAAIC,aAAa,GAAG,UAAU;EAE9B,IAAIC,KAAK,GAAGC,IAAI,CAACD,KAAK;EACtB,IAAIE,GAAG,GAAGD,IAAI,CAACC,GAAG;EAClB,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG;;EAElB;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASE,iBAAiBA,CAACC,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC7C,OAAOC,UAAU,CAACC,MAAM,CAACJ,EAAE,EAAEE,OAAO,CAAC,EAAED,OAAO,CAAC;EACnD;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASI,cAAcA,CAACC,GAAG,EAAEN,EAAE,EAAEE,OAAO,EAAE;IACtC,IAAIK,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;MACpBG,IAAI,CAACH,GAAG,EAAEJ,OAAO,CAACF,EAAE,CAAC,EAAEE,OAAO,CAAC;MAC/B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASO,IAAIA,CAACC,GAAG,EAAEC,QAAQ,EAAET,OAAO,EAAE;IAClC,IAAIU,CAAC;IAEL,IAAI,CAACF,GAAG,EAAE;MACN;IACJ;IAEA,IAAIA,GAAG,CAACG,OAAO,EAAE;MACbH,GAAG,CAACG,OAAO,CAACF,QAAQ,EAAET,OAAO,CAAC;IAClC,CAAC,MAAM,IAAIQ,GAAG,CAACI,MAAM,KAAKzB,SAAS,EAAE;MACjCuB,CAAC,GAAG,CAAC;MACL,OAAOA,CAAC,GAAGF,GAAG,CAACI,MAAM,EAAE;QACnBH,QAAQ,CAACI,IAAI,CAACb,OAAO,EAAEQ,GAAG,CAACE,CAAC,CAAC,EAAEA,CAAC,EAAEF,GAAG,CAAC;QACtCE,CAAC,EAAE;MACP;IACJ,CAAC,MAAM;MACH,KAAKA,CAAC,IAAIF,GAAG,EAAE;QACXA,GAAG,CAACM,cAAc,CAACJ,CAAC,CAAC,IAAID,QAAQ,CAACI,IAAI,CAACb,OAAO,EAAEQ,GAAG,CAACE,CAAC,CAAC,EAAEA,CAAC,EAAEF,GAAG,CAAC;MACnE;IACJ;EACJ;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASO,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACtC,IAAIC,kBAAkB,GAAG,qBAAqB,GAAGF,IAAI,GAAG,IAAI,GAAGC,OAAO,GAAG,QAAQ;IACjF,OAAO,YAAW;MACd,IAAIE,CAAC,GAAG,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC,IAAIC,KAAK,GAAGF,CAAC,IAAIA,CAAC,CAACE,KAAK,GAAGF,CAAC,CAACE,KAAK,CAACC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAC5DA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAC1BA,OAAO,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,GAAG,qBAAqB;MAEpF,IAAIC,GAAG,GAAGxC,MAAM,CAACyC,OAAO,KAAKzC,MAAM,CAACyC,OAAO,CAACC,IAAI,IAAI1C,MAAM,CAACyC,OAAO,CAACD,GAAG,CAAC;MACvE,IAAIA,GAAG,EAAE;QACLA,GAAG,CAACX,IAAI,CAAC7B,MAAM,CAACyC,OAAO,EAAEN,kBAAkB,EAAEG,KAAK,CAAC;MACvD;MACA,OAAON,MAAM,CAACW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACxC,CAAC;EACL;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIC,MAAM;EACV,IAAI,OAAOC,MAAM,CAACD,MAAM,KAAK,UAAU,EAAE;IACrCA,MAAM,GAAG,SAASA,MAAMA,CAACE,MAAM,EAAE;MAC7B,IAAIA,MAAM,KAAK5C,SAAS,IAAI4C,MAAM,KAAK,IAAI,EAAE;QACzC,MAAM,IAAIC,SAAS,CAAC,4CAA4C,CAAC;MACrE;MAEA,IAAIC,MAAM,GAAGH,MAAM,CAACC,MAAM,CAAC;MAC3B,KAAK,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,SAAS,CAAChB,MAAM,EAAEsB,KAAK,EAAE,EAAE;QACnD,IAAIC,MAAM,GAAGP,SAAS,CAACM,KAAK,CAAC;QAC7B,IAAIC,MAAM,KAAKhD,SAAS,IAAIgD,MAAM,KAAK,IAAI,EAAE;UACzC,KAAK,IAAIC,OAAO,IAAID,MAAM,EAAE;YACxB,IAAIA,MAAM,CAACrB,cAAc,CAACsB,OAAO,CAAC,EAAE;cAChCH,MAAM,CAACG,OAAO,CAAC,GAAGD,MAAM,CAACC,OAAO,CAAC;YACrC;UACJ;QACJ;MACJ;MACA,OAAOH,MAAM;IACjB,CAAC;EACL,CAAC,MAAM;IACHJ,MAAM,GAAGC,MAAM,CAACD,MAAM;EAC1B;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIQ,MAAM,GAAGtB,SAAS,CAAC,SAASsB,MAAMA,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAE;IACrD,IAAIC,IAAI,GAAGX,MAAM,CAACW,IAAI,CAACF,GAAG,CAAC;IAC3B,IAAI7B,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG+B,IAAI,CAAC7B,MAAM,EAAE;MACpB,IAAI,CAAC4B,KAAK,IAAKA,KAAK,IAAIF,IAAI,CAACG,IAAI,CAAC/B,CAAC,CAAC,CAAC,KAAKvB,SAAU,EAAE;QAClDmD,IAAI,CAACG,IAAI,CAAC/B,CAAC,CAAC,CAAC,GAAG6B,GAAG,CAACE,IAAI,CAAC/B,CAAC,CAAC,CAAC;MAChC;MACAA,CAAC,EAAE;IACP;IACA,OAAO4B,IAAI;EACf,CAAC,EAAE,QAAQ,EAAE,eAAe,CAAC;;EAE7B;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIE,KAAK,GAAGzB,SAAS,CAAC,SAASyB,KAAKA,CAACF,IAAI,EAAEC,GAAG,EAAE;IAC5C,OAAOF,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAE,IAAI,CAAC;EAClC,CAAC,EAAE,OAAO,EAAE,eAAe,CAAC;;EAE5B;AACA;AACA;AACA;AACA;AACA;EACA,SAASG,OAAOA,CAACC,KAAK,EAAEC,IAAI,EAAEC,UAAU,EAAE;IACtC,IAAIC,KAAK,GAAGF,IAAI,CAACG,SAAS;MACtBC,MAAM;IAEVA,MAAM,GAAGL,KAAK,CAACI,SAAS,GAAGjB,MAAM,CAACmB,MAAM,CAACH,KAAK,CAAC;IAC/CE,MAAM,CAACE,WAAW,GAAGP,KAAK;IAC1BK,MAAM,CAACG,MAAM,GAAGL,KAAK;IAErB,IAAID,UAAU,EAAE;MACZhB,MAAM,CAACmB,MAAM,EAAEH,UAAU,CAAC;IAC9B;EACJ;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAAS3C,MAAMA,CAACJ,EAAE,EAAEE,OAAO,EAAE;IACzB,OAAO,SAASoD,OAAOA,CAAA,EAAG;MACtB,OAAOtD,EAAE,CAAC6B,KAAK,CAAC3B,OAAO,EAAE4B,SAAS,CAAC;IACvC,CAAC;EACL;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASyB,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACzB,IAAI,OAAOD,GAAG,IAAI/D,aAAa,EAAE;MAC7B,OAAO+D,GAAG,CAAC3B,KAAK,CAAC4B,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAIpE,SAAS,GAAGA,SAAS,EAAEoE,IAAI,CAAC;IACnE;IACA,OAAOD,GAAG;EACd;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASE,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAC7B,OAAQD,IAAI,KAAKtE,SAAS,GAAIuE,IAAI,GAAGD,IAAI;EAC7C;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASE,iBAAiBA,CAAC5B,MAAM,EAAE6B,KAAK,EAAEC,OAAO,EAAE;IAC/CtD,IAAI,CAACuD,QAAQ,CAACF,KAAK,CAAC,EAAE,UAASG,IAAI,EAAE;MACjChC,MAAM,CAACiC,gBAAgB,CAACD,IAAI,EAAEF,OAAO,EAAE,KAAK,CAAC;IACjD,CAAC,CAAC;EACN;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASI,oBAAoBA,CAAClC,MAAM,EAAE6B,KAAK,EAAEC,OAAO,EAAE;IAClDtD,IAAI,CAACuD,QAAQ,CAACF,KAAK,CAAC,EAAE,UAASG,IAAI,EAAE;MACjChC,MAAM,CAACmC,mBAAmB,CAACH,IAAI,EAAEF,OAAO,EAAE,KAAK,CAAC;IACpD,CAAC,CAAC;EACN;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASM,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC7B,OAAOD,IAAI,EAAE;MACT,IAAIA,IAAI,IAAIC,MAAM,EAAE;QAChB,OAAO,IAAI;MACf;MACAD,IAAI,GAAGA,IAAI,CAACE,UAAU;IAC1B;IACA,OAAO,KAAK;EAChB;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASC,KAAKA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACtB,OAAOD,GAAG,CAACE,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;EACjC;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASX,QAAQA,CAACU,GAAG,EAAE;IACnB,OAAOA,GAAG,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC;EACnC;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASC,OAAOA,CAACtC,GAAG,EAAEkC,IAAI,EAAEK,SAAS,EAAE;IACnC,IAAIvC,GAAG,CAACmC,OAAO,IAAI,CAACI,SAAS,EAAE;MAC3B,OAAOvC,GAAG,CAACmC,OAAO,CAACD,IAAI,CAAC;IAC5B,CAAC,MAAM;MACH,IAAI/D,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAG6B,GAAG,CAAC3B,MAAM,EAAE;QACnB,IAAKkE,SAAS,IAAIvC,GAAG,CAAC7B,CAAC,CAAC,CAACoE,SAAS,CAAC,IAAIL,IAAI,IAAM,CAACK,SAAS,IAAIvC,GAAG,CAAC7B,CAAC,CAAC,KAAK+D,IAAK,EAAE;UAC7E,OAAO/D,CAAC;QACZ;QACAA,CAAC,EAAE;MACP;MACA,OAAO,CAAC,CAAC;IACb;EACJ;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASqE,OAAOA,CAACvE,GAAG,EAAE;IAClB,OAAOH,KAAK,CAAC0C,SAAS,CAACiC,KAAK,CAACnE,IAAI,CAACL,GAAG,EAAE,CAAC,CAAC;EAC7C;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASyE,WAAWA,CAAC1C,GAAG,EAAE2C,GAAG,EAAEC,IAAI,EAAE;IACjC,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAI3E,CAAC,GAAG,CAAC;IAET,OAAOA,CAAC,GAAG6B,GAAG,CAAC3B,MAAM,EAAE;MACnB,IAAI0C,GAAG,GAAG4B,GAAG,GAAG3C,GAAG,CAAC7B,CAAC,CAAC,CAACwE,GAAG,CAAC,GAAG3C,GAAG,CAAC7B,CAAC,CAAC;MACpC,IAAImE,OAAO,CAACQ,MAAM,EAAE/B,GAAG,CAAC,GAAG,CAAC,EAAE;QAC1B8B,OAAO,CAACE,IAAI,CAAC/C,GAAG,CAAC7B,CAAC,CAAC,CAAC;MACxB;MACA2E,MAAM,CAAC3E,CAAC,CAAC,GAAG4C,GAAG;MACf5C,CAAC,EAAE;IACP;IAEA,IAAIyE,IAAI,EAAE;MACN,IAAI,CAACD,GAAG,EAAE;QACNE,OAAO,GAAGA,OAAO,CAACD,IAAI,CAAC,CAAC;MAC5B,CAAC,MAAM;QACHC,OAAO,GAAGA,OAAO,CAACD,IAAI,CAAC,SAASI,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;UAClD,OAAOD,CAAC,CAACN,GAAG,CAAC,GAAGO,CAAC,CAACP,GAAG,CAAC;QAC1B,CAAC,CAAC;MACN;IACJ;IAEA,OAAOE,OAAO;EAClB;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASM,QAAQA,CAAClF,GAAG,EAAEmF,QAAQ,EAAE;IAC7B,IAAIC,MAAM,EAAEC,IAAI;IAChB,IAAIC,SAAS,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,GAAGJ,QAAQ,CAACX,KAAK,CAAC,CAAC,CAAC;IAE7D,IAAItE,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGtB,eAAe,CAACwB,MAAM,EAAE;MAC/BgF,MAAM,GAAGxG,eAAe,CAACsB,CAAC,CAAC;MAC3BmF,IAAI,GAAID,MAAM,GAAIA,MAAM,GAAGE,SAAS,GAAGH,QAAQ;MAE/C,IAAIE,IAAI,IAAIrF,GAAG,EAAE;QACb,OAAOqF,IAAI;MACf;MACAnF,CAAC,EAAE;IACP;IACA,OAAOvB,SAAS;EACpB;;EAEA;AACA;AACA;AACA;EACA,IAAI6G,SAAS,GAAG,CAAC;EACjB,SAASC,QAAQA,CAAA,EAAG;IAChB,OAAOD,SAAS,EAAE;EACtB;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASE,mBAAmBA,CAACC,OAAO,EAAE;IAClC,IAAIC,GAAG,GAAGD,OAAO,CAACE,aAAa,IAAIF,OAAO;IAC1C,OAAQC,GAAG,CAACE,WAAW,IAAIF,GAAG,CAACG,YAAY,IAAIvH,MAAM;EACzD;EAEA,IAAIwH,YAAY,GAAG,uCAAuC;EAE1D,IAAIC,aAAa,IAAI,cAAc,IAAIzH,MAAM,CAAC;EAC9C,IAAI0H,sBAAsB,GAAGhB,QAAQ,CAAC1G,MAAM,EAAE,cAAc,CAAC,KAAKG,SAAS;EAC3E,IAAIwH,kBAAkB,GAAGF,aAAa,IAAID,YAAY,CAACI,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EAEhF,IAAIC,gBAAgB,GAAG,OAAO;EAC9B,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,gBAAgB,GAAG,OAAO;EAC9B,IAAIC,iBAAiB,GAAG,QAAQ;EAEhC,IAAIC,gBAAgB,GAAG,EAAE;EAEzB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,YAAY,GAAG,CAAC;EAEpB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAIC,oBAAoB,GAAGJ,cAAc,GAAGC,eAAe;EAC3D,IAAII,kBAAkB,GAAGH,YAAY,GAAGC,cAAc;EACtD,IAAIG,aAAa,GAAGF,oBAAoB,GAAGC,kBAAkB;EAE7D,IAAIE,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EACzB,IAAIC,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;;EAE5C;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASC,KAAKA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC9B,IAAIC,IAAI,GAAG,IAAI;IACf,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACjC,OAAO,GAAGgC,OAAO,CAAChC,OAAO;IAC9B,IAAI,CAACpE,MAAM,GAAGoG,OAAO,CAACG,OAAO,CAACC,WAAW;;IAEzC;IACA;IACA,IAAI,CAACC,UAAU,GAAG,UAASC,EAAE,EAAE;MAC3B,IAAIpF,QAAQ,CAAC8E,OAAO,CAACG,OAAO,CAACI,MAAM,EAAE,CAACP,OAAO,CAAC,CAAC,EAAE;QAC7CE,IAAI,CAACxE,OAAO,CAAC4E,EAAE,CAAC;MACpB;IACJ,CAAC;IAED,IAAI,CAACE,IAAI,CAAC,CAAC;EAEf;EAEAT,KAAK,CAACnF,SAAS,GAAG;IACd;AACJ;AACA;AACA;IACIc,OAAO,EAAE,SAAAA,CAAA,EAAW,CAAE,CAAC;IAEvB;AACJ;AACA;IACI8E,IAAI,EAAE,SAAAA,CAAA,EAAW;MACb,IAAI,CAACC,IAAI,IAAIjF,iBAAiB,CAAC,IAAI,CAACwC,OAAO,EAAE,IAAI,CAACyC,IAAI,EAAE,IAAI,CAACJ,UAAU,CAAC;MACxE,IAAI,CAACK,QAAQ,IAAIlF,iBAAiB,CAAC,IAAI,CAAC5B,MAAM,EAAE,IAAI,CAAC8G,QAAQ,EAAE,IAAI,CAACL,UAAU,CAAC;MAC/E,IAAI,CAACM,KAAK,IAAInF,iBAAiB,CAACuC,mBAAmB,CAAC,IAAI,CAACC,OAAO,CAAC,EAAE,IAAI,CAAC2C,KAAK,EAAE,IAAI,CAACN,UAAU,CAAC;IACnG,CAAC;IAED;AACJ;AACA;IACIO,OAAO,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,CAACH,IAAI,IAAI3E,oBAAoB,CAAC,IAAI,CAACkC,OAAO,EAAE,IAAI,CAACyC,IAAI,EAAE,IAAI,CAACJ,UAAU,CAAC;MAC3E,IAAI,CAACK,QAAQ,IAAI5E,oBAAoB,CAAC,IAAI,CAAClC,MAAM,EAAE,IAAI,CAAC8G,QAAQ,EAAE,IAAI,CAACL,UAAU,CAAC;MAClF,IAAI,CAACM,KAAK,IAAI7E,oBAAoB,CAACiC,mBAAmB,CAAC,IAAI,CAACC,OAAO,CAAC,EAAE,IAAI,CAAC2C,KAAK,EAAE,IAAI,CAACN,UAAU,CAAC;IACtG;EACJ,CAAC;;EAED;AACA;AACA;AACA;AACA;AACA;EACA,SAASQ,mBAAmBA,CAACb,OAAO,EAAE;IAClC,IAAIc,IAAI;IACR,IAAIC,UAAU,GAAGf,OAAO,CAACG,OAAO,CAACY,UAAU;IAE3C,IAAIA,UAAU,EAAE;MACZD,IAAI,GAAGC,UAAU;IACrB,CAAC,MAAM,IAAIxC,sBAAsB,EAAE;MAC/BuC,IAAI,GAAGE,iBAAiB;IAC5B,CAAC,MAAM,IAAIxC,kBAAkB,EAAE;MAC3BsC,IAAI,GAAGG,UAAU;IACrB,CAAC,MAAM,IAAI,CAAC3C,aAAa,EAAE;MACvBwC,IAAI,GAAGI,UAAU;IACrB,CAAC,MAAM;MACHJ,IAAI,GAAGK,eAAe;IAC1B;IACA,OAAO,IAAKL,IAAI,CAAEd,OAAO,EAAEoB,YAAY,CAAC;EAC5C;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASA,YAAYA,CAACpB,OAAO,EAAEqB,SAAS,EAAEC,KAAK,EAAE;IAC7C,IAAIC,WAAW,GAAGD,KAAK,CAACE,QAAQ,CAAC/I,MAAM;IACvC,IAAIgJ,kBAAkB,GAAGH,KAAK,CAACI,eAAe,CAACjJ,MAAM;IACrD,IAAIkJ,OAAO,GAAIN,SAAS,GAAGpC,WAAW,IAAKsC,WAAW,GAAGE,kBAAkB,KAAK,CAAG;IACnF,IAAIG,OAAO,GAAIP,SAAS,IAAIlC,SAAS,GAAGC,YAAY,CAAC,IAAKmC,WAAW,GAAGE,kBAAkB,KAAK,CAAG;IAElGH,KAAK,CAACK,OAAO,GAAG,CAAC,CAACA,OAAO;IACzBL,KAAK,CAACM,OAAO,GAAG,CAAC,CAACA,OAAO;IAEzB,IAAID,OAAO,EAAE;MACT3B,OAAO,CAAC6B,OAAO,GAAG,CAAC,CAAC;IACxB;;IAEA;IACA;IACAP,KAAK,CAACD,SAAS,GAAGA,SAAS;;IAE3B;IACAS,gBAAgB,CAAC9B,OAAO,EAAEsB,KAAK,CAAC;;IAEhC;IACAtB,OAAO,CAAC+B,IAAI,CAAC,cAAc,EAAET,KAAK,CAAC;IAEnCtB,OAAO,CAACgC,SAAS,CAACV,KAAK,CAAC;IACxBtB,OAAO,CAAC6B,OAAO,CAACI,SAAS,GAAGX,KAAK;EACrC;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASQ,gBAAgBA,CAAC9B,OAAO,EAAEsB,KAAK,EAAE;IACtC,IAAIO,OAAO,GAAG7B,OAAO,CAAC6B,OAAO;IAC7B,IAAIL,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC7B,IAAIU,cAAc,GAAGV,QAAQ,CAAC/I,MAAM;;IAEpC;IACA,IAAI,CAACoJ,OAAO,CAACM,UAAU,EAAE;MACrBN,OAAO,CAACM,UAAU,GAAGC,oBAAoB,CAACd,KAAK,CAAC;IACpD;;IAEA;IACA,IAAIY,cAAc,GAAG,CAAC,IAAI,CAACL,OAAO,CAACQ,aAAa,EAAE;MAC9CR,OAAO,CAACQ,aAAa,GAAGD,oBAAoB,CAACd,KAAK,CAAC;IACvD,CAAC,MAAM,IAAIY,cAAc,KAAK,CAAC,EAAE;MAC7BL,OAAO,CAACQ,aAAa,GAAG,KAAK;IACjC;IAEA,IAAIF,UAAU,GAAGN,OAAO,CAACM,UAAU;IACnC,IAAIE,aAAa,GAAGR,OAAO,CAACQ,aAAa;IACzC,IAAIC,YAAY,GAAGD,aAAa,GAAGA,aAAa,CAACE,MAAM,GAAGJ,UAAU,CAACI,MAAM;IAE3E,IAAIA,MAAM,GAAGjB,KAAK,CAACiB,MAAM,GAAGC,SAAS,CAAChB,QAAQ,CAAC;IAC/CF,KAAK,CAACmB,SAAS,GAAGjL,GAAG,CAAC,CAAC;IACvB8J,KAAK,CAACoB,SAAS,GAAGpB,KAAK,CAACmB,SAAS,GAAGN,UAAU,CAACM,SAAS;IAExDnB,KAAK,CAACqB,KAAK,GAAGC,QAAQ,CAACN,YAAY,EAAEC,MAAM,CAAC;IAC5CjB,KAAK,CAACuB,QAAQ,GAAGC,WAAW,CAACR,YAAY,EAAEC,MAAM,CAAC;IAElDQ,cAAc,CAAClB,OAAO,EAAEP,KAAK,CAAC;IAC9BA,KAAK,CAAC0B,eAAe,GAAGC,YAAY,CAAC3B,KAAK,CAAC4B,MAAM,EAAE5B,KAAK,CAAC6B,MAAM,CAAC;IAEhE,IAAIC,eAAe,GAAGC,WAAW,CAAC/B,KAAK,CAACoB,SAAS,EAAEpB,KAAK,CAAC4B,MAAM,EAAE5B,KAAK,CAAC6B,MAAM,CAAC;IAC9E7B,KAAK,CAACgC,gBAAgB,GAAGF,eAAe,CAACG,CAAC;IAC1CjC,KAAK,CAACkC,gBAAgB,GAAGJ,eAAe,CAACK,CAAC;IAC1CnC,KAAK,CAAC8B,eAAe,GAAI7L,GAAG,CAAC6L,eAAe,CAACG,CAAC,CAAC,GAAGhM,GAAG,CAAC6L,eAAe,CAACK,CAAC,CAAC,GAAIL,eAAe,CAACG,CAAC,GAAGH,eAAe,CAACK,CAAC;IAEjHnC,KAAK,CAACoC,KAAK,GAAGrB,aAAa,GAAGsB,QAAQ,CAACtB,aAAa,CAACb,QAAQ,EAAEA,QAAQ,CAAC,GAAG,CAAC;IAC5EF,KAAK,CAACsC,QAAQ,GAAGvB,aAAa,GAAGwB,WAAW,CAACxB,aAAa,CAACb,QAAQ,EAAEA,QAAQ,CAAC,GAAG,CAAC;IAElFF,KAAK,CAACwC,WAAW,GAAG,CAACjC,OAAO,CAACI,SAAS,GAAGX,KAAK,CAACE,QAAQ,CAAC/I,MAAM,GAAK6I,KAAK,CAACE,QAAQ,CAAC/I,MAAM,GACpFoJ,OAAO,CAACI,SAAS,CAAC6B,WAAW,GAAIxC,KAAK,CAACE,QAAQ,CAAC/I,MAAM,GAAGoJ,OAAO,CAACI,SAAS,CAAC6B,WAAY;IAE3FC,wBAAwB,CAAClC,OAAO,EAAEP,KAAK,CAAC;;IAExC;IACA,IAAI1H,MAAM,GAAGoG,OAAO,CAAChC,OAAO;IAC5B,IAAIhC,SAAS,CAACsF,KAAK,CAAC0C,QAAQ,CAACpK,MAAM,EAAEA,MAAM,CAAC,EAAE;MAC1CA,MAAM,GAAG0H,KAAK,CAAC0C,QAAQ,CAACpK,MAAM;IAClC;IACA0H,KAAK,CAAC1H,MAAM,GAAGA,MAAM;EACzB;EAEA,SAASmJ,cAAcA,CAAClB,OAAO,EAAEP,KAAK,EAAE;IACpC,IAAIiB,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACzB,IAAI0B,MAAM,GAAGpC,OAAO,CAACqC,WAAW,IAAI,CAAC,CAAC;IACtC,IAAIC,SAAS,GAAGtC,OAAO,CAACsC,SAAS,IAAI,CAAC,CAAC;IACvC,IAAIlC,SAAS,GAAGJ,OAAO,CAACI,SAAS,IAAI,CAAC,CAAC;IAEvC,IAAIX,KAAK,CAACD,SAAS,KAAKpC,WAAW,IAAIgD,SAAS,CAACZ,SAAS,KAAKlC,SAAS,EAAE;MACtEgF,SAAS,GAAGtC,OAAO,CAACsC,SAAS,GAAG;QAC5BZ,CAAC,EAAEtB,SAAS,CAACiB,MAAM,IAAI,CAAC;QACxBO,CAAC,EAAExB,SAAS,CAACkB,MAAM,IAAI;MAC3B,CAAC;MAEDc,MAAM,GAAGpC,OAAO,CAACqC,WAAW,GAAG;QAC3BX,CAAC,EAAEhB,MAAM,CAACgB,CAAC;QACXE,CAAC,EAAElB,MAAM,CAACkB;MACd,CAAC;IACL;IAEAnC,KAAK,CAAC4B,MAAM,GAAGiB,SAAS,CAACZ,CAAC,IAAIhB,MAAM,CAACgB,CAAC,GAAGU,MAAM,CAACV,CAAC,CAAC;IAClDjC,KAAK,CAAC6B,MAAM,GAAGgB,SAAS,CAACV,CAAC,IAAIlB,MAAM,CAACkB,CAAC,GAAGQ,MAAM,CAACR,CAAC,CAAC;EACtD;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASM,wBAAwBA,CAAClC,OAAO,EAAEP,KAAK,EAAE;IAC9C,IAAI8C,IAAI,GAAGvC,OAAO,CAACwC,YAAY,IAAI/C,KAAK;MACpCoB,SAAS,GAAGpB,KAAK,CAACmB,SAAS,GAAG2B,IAAI,CAAC3B,SAAS;MAC5C6B,QAAQ;MAAEC,SAAS;MAAEC,SAAS;MAAEC,SAAS;IAE7C,IAAInD,KAAK,CAACD,SAAS,IAAIjC,YAAY,KAAKsD,SAAS,GAAG1D,gBAAgB,IAAIoF,IAAI,CAACE,QAAQ,KAAKtN,SAAS,CAAC,EAAE;MAClG,IAAIkM,MAAM,GAAG5B,KAAK,CAAC4B,MAAM,GAAGkB,IAAI,CAAClB,MAAM;MACvC,IAAIC,MAAM,GAAG7B,KAAK,CAAC6B,MAAM,GAAGiB,IAAI,CAACjB,MAAM;MAEvC,IAAIuB,CAAC,GAAGrB,WAAW,CAACX,SAAS,EAAEQ,MAAM,EAAEC,MAAM,CAAC;MAC9CoB,SAAS,GAAGG,CAAC,CAACnB,CAAC;MACfiB,SAAS,GAAGE,CAAC,CAACjB,CAAC;MACfa,QAAQ,GAAI/M,GAAG,CAACmN,CAAC,CAACnB,CAAC,CAAC,GAAGhM,GAAG,CAACmN,CAAC,CAACjB,CAAC,CAAC,GAAIiB,CAAC,CAACnB,CAAC,GAAGmB,CAAC,CAACjB,CAAC;MAC5CgB,SAAS,GAAGxB,YAAY,CAACC,MAAM,EAAEC,MAAM,CAAC;MAExCtB,OAAO,CAACwC,YAAY,GAAG/C,KAAK;IAChC,CAAC,MAAM;MACH;MACAgD,QAAQ,GAAGF,IAAI,CAACE,QAAQ;MACxBC,SAAS,GAAGH,IAAI,CAACG,SAAS;MAC1BC,SAAS,GAAGJ,IAAI,CAACI,SAAS;MAC1BC,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC9B;IAEAnD,KAAK,CAACgD,QAAQ,GAAGA,QAAQ;IACzBhD,KAAK,CAACiD,SAAS,GAAGA,SAAS;IAC3BjD,KAAK,CAACkD,SAAS,GAAGA,SAAS;IAC3BlD,KAAK,CAACmD,SAAS,GAAGA,SAAS;EAC/B;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASrC,oBAAoBA,CAACd,KAAK,EAAE;IACjC;IACA;IACA,IAAIE,QAAQ,GAAG,EAAE;IACjB,IAAIjJ,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG+I,KAAK,CAACE,QAAQ,CAAC/I,MAAM,EAAE;MAC9B+I,QAAQ,CAACjJ,CAAC,CAAC,GAAG;QACVoM,OAAO,EAAEtN,KAAK,CAACiK,KAAK,CAACE,QAAQ,CAACjJ,CAAC,CAAC,CAACoM,OAAO,CAAC;QACzCC,OAAO,EAAEvN,KAAK,CAACiK,KAAK,CAACE,QAAQ,CAACjJ,CAAC,CAAC,CAACqM,OAAO;MAC5C,CAAC;MACDrM,CAAC,EAAE;IACP;IAEA,OAAO;MACHkK,SAAS,EAAEjL,GAAG,CAAC,CAAC;MAChBgK,QAAQ,EAAEA,QAAQ;MAClBe,MAAM,EAAEC,SAAS,CAAChB,QAAQ,CAAC;MAC3B0B,MAAM,EAAE5B,KAAK,CAAC4B,MAAM;MACpBC,MAAM,EAAE7B,KAAK,CAAC6B;IAClB,CAAC;EACL;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASX,SAASA,CAAChB,QAAQ,EAAE;IACzB,IAAIU,cAAc,GAAGV,QAAQ,CAAC/I,MAAM;;IAEpC;IACA,IAAIyJ,cAAc,KAAK,CAAC,EAAE;MACtB,OAAO;QACHqB,CAAC,EAAElM,KAAK,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAACmD,OAAO,CAAC;QAC7BlB,CAAC,EAAEpM,KAAK,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAACoD,OAAO;MAChC,CAAC;IACL;IAEA,IAAIrB,CAAC,GAAG,CAAC;MAAEE,CAAC,GAAG,CAAC;MAAElL,CAAC,GAAG,CAAC;IACvB,OAAOA,CAAC,GAAG2J,cAAc,EAAE;MACvBqB,CAAC,IAAI/B,QAAQ,CAACjJ,CAAC,CAAC,CAACoM,OAAO;MACxBlB,CAAC,IAAIjC,QAAQ,CAACjJ,CAAC,CAAC,CAACqM,OAAO;MACxBrM,CAAC,EAAE;IACP;IAEA,OAAO;MACHgL,CAAC,EAAElM,KAAK,CAACkM,CAAC,GAAGrB,cAAc,CAAC;MAC5BuB,CAAC,EAAEpM,KAAK,CAACoM,CAAC,GAAGvB,cAAc;IAC/B,CAAC;EACL;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASmB,WAAWA,CAACX,SAAS,EAAEa,CAAC,EAAEE,CAAC,EAAE;IAClC,OAAO;MACHF,CAAC,EAAEA,CAAC,GAAGb,SAAS,IAAI,CAAC;MACrBe,CAAC,EAAEA,CAAC,GAAGf,SAAS,IAAI;IACxB,CAAC;EACL;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASO,YAAYA,CAACM,CAAC,EAAEE,CAAC,EAAE;IACxB,IAAIF,CAAC,KAAKE,CAAC,EAAE;MACT,OAAOpE,cAAc;IACzB;IAEA,IAAI9H,GAAG,CAACgM,CAAC,CAAC,IAAIhM,GAAG,CAACkM,CAAC,CAAC,EAAE;MAClB,OAAOF,CAAC,GAAG,CAAC,GAAGjE,cAAc,GAAGC,eAAe;IACnD;IACA,OAAOkE,CAAC,GAAG,CAAC,GAAGjE,YAAY,GAAGC,cAAc;EAChD;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASqD,WAAWA,CAAC+B,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;IAChC,IAAI,CAACA,KAAK,EAAE;MACRA,KAAK,GAAGlF,QAAQ;IACpB;IACA,IAAI0D,CAAC,GAAGuB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/BtB,CAAC,GAAGqB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;IAEnC,OAAOzN,IAAI,CAAC0N,IAAI,CAAEzB,CAAC,GAAGA,CAAC,GAAKE,CAAC,GAAGA,CAAE,CAAC;EACvC;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASb,QAAQA,CAACiC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;IAC7B,IAAI,CAACA,KAAK,EAAE;MACRA,KAAK,GAAGlF,QAAQ;IACpB;IACA,IAAI0D,CAAC,GAAGuB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/BtB,CAAC,GAAGqB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,OAAOzN,IAAI,CAAC2N,KAAK,CAACxB,CAAC,EAAEF,CAAC,CAAC,GAAG,GAAG,GAAGjM,IAAI,CAAC4N,EAAE;EAC3C;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASrB,WAAWA,CAACsB,KAAK,EAAEC,GAAG,EAAE;IAC7B,OAAOxC,QAAQ,CAACwC,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEtF,eAAe,CAAC,GAAG8C,QAAQ,CAACuC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAErF,eAAe,CAAC;EACpG;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAAS6D,QAAQA,CAACwB,KAAK,EAAEC,GAAG,EAAE;IAC1B,OAAOtC,WAAW,CAACsC,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEtF,eAAe,CAAC,GAAGgD,WAAW,CAACqC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAErF,eAAe,CAAC;EAC1G;EAEA,IAAIuF,eAAe,GAAG;IAClBC,SAAS,EAAErG,WAAW;IACtBsG,SAAS,EAAErG,UAAU;IACrBsG,OAAO,EAAErG;EACb,CAAC;EAED,IAAIsG,oBAAoB,GAAG,WAAW;EACtC,IAAIC,mBAAmB,GAAG,mBAAmB;;EAE7C;AACA;AACA;AACA;AACA;EACA,SAASxE,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACT,IAAI,GAAGgF,oBAAoB;IAChC,IAAI,CAAC9E,KAAK,GAAG+E,mBAAmB;IAEhC,IAAI,CAACC,OAAO,GAAG,KAAK,CAAC,CAAC;;IAEtB5F,KAAK,CAACvG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEAc,OAAO,CAAC2G,UAAU,EAAEnB,KAAK,EAAE;IACvB;AACJ;AACA;AACA;IACIrE,OAAO,EAAE,SAASkK,SAASA,CAACtF,EAAE,EAAE;MAC5B,IAAIe,SAAS,GAAGgE,eAAe,CAAC/E,EAAE,CAAC1E,IAAI,CAAC;;MAExC;MACA,IAAIyF,SAAS,GAAGpC,WAAW,IAAIqB,EAAE,CAACuF,MAAM,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACF,OAAO,GAAG,IAAI;MACvB;MAEA,IAAItE,SAAS,GAAGnC,UAAU,IAAIoB,EAAE,CAACwF,KAAK,KAAK,CAAC,EAAE;QAC1CzE,SAAS,GAAGlC,SAAS;MACzB;;MAEA;MACA,IAAI,CAAC,IAAI,CAACwG,OAAO,EAAE;QACf;MACJ;MAEA,IAAItE,SAAS,GAAGlC,SAAS,EAAE;QACvB,IAAI,CAACwG,OAAO,GAAG,KAAK;MACxB;MAEA,IAAI,CAAC1F,QAAQ,CAAC,IAAI,CAACD,OAAO,EAAEqB,SAAS,EAAE;QACnCG,QAAQ,EAAE,CAAClB,EAAE,CAAC;QACdoB,eAAe,EAAE,CAACpB,EAAE,CAAC;QACrByF,WAAW,EAAEjH,gBAAgB;QAC7BkF,QAAQ,EAAE1D;MACd,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EAEF,IAAI0F,iBAAiB,GAAG;IACpBC,WAAW,EAAEhH,WAAW;IACxBiH,WAAW,EAAEhH,UAAU;IACvBiH,SAAS,EAAEhH,SAAS;IACpBiH,aAAa,EAAEhH,YAAY;IAC3BiH,UAAU,EAAEjH;EAChB,CAAC;;EAED;EACA,IAAIkH,sBAAsB,GAAG;IACzB,CAAC,EAAE1H,gBAAgB;IACnB,CAAC,EAAEC,cAAc;IACjB,CAAC,EAAEC,gBAAgB;IACnB,CAAC,EAAEC,iBAAiB,CAAC;EACzB,CAAC;;EAED,IAAIwH,sBAAsB,GAAG,aAAa;EAC1C,IAAIC,qBAAqB,GAAG,qCAAqC;;EAEjE;EACA,IAAI3P,MAAM,CAAC4P,cAAc,IAAI,CAAC5P,MAAM,CAAC6P,YAAY,EAAE;IAC/CH,sBAAsB,GAAG,eAAe;IACxCC,qBAAqB,GAAG,2CAA2C;EACvE;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASxF,iBAAiBA,CAAA,EAAG;IACzB,IAAI,CAACP,IAAI,GAAG8F,sBAAsB;IAClC,IAAI,CAAC5F,KAAK,GAAG6F,qBAAqB;IAElCzG,KAAK,CAACvG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAE5B,IAAI,CAACkN,KAAK,GAAI,IAAI,CAAC3G,OAAO,CAAC6B,OAAO,CAAC+E,aAAa,GAAG,EAAG;EAC1D;EAEArM,OAAO,CAACyG,iBAAiB,EAAEjB,KAAK,EAAE;IAC9B;AACJ;AACA;AACA;IACIrE,OAAO,EAAE,SAASmL,SAASA,CAACvG,EAAE,EAAE;MAC5B,IAAIqG,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAIG,aAAa,GAAG,KAAK;MAEzB,IAAIC,mBAAmB,GAAGzG,EAAE,CAAC1E,IAAI,CAACoL,WAAW,CAAC,CAAC,CAAC5N,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;MACjE,IAAIiI,SAAS,GAAG2E,iBAAiB,CAACe,mBAAmB,CAAC;MACtD,IAAIhB,WAAW,GAAGO,sBAAsB,CAAChG,EAAE,CAACyF,WAAW,CAAC,IAAIzF,EAAE,CAACyF,WAAW;MAE1E,IAAIkB,OAAO,GAAIlB,WAAW,IAAInH,gBAAiB;;MAE/C;MACA,IAAIsI,UAAU,GAAGxK,OAAO,CAACiK,KAAK,EAAErG,EAAE,CAAC6G,SAAS,EAAE,WAAW,CAAC;;MAE1D;MACA,IAAI9F,SAAS,GAAGpC,WAAW,KAAKqB,EAAE,CAACuF,MAAM,KAAK,CAAC,IAAIoB,OAAO,CAAC,EAAE;QACzD,IAAIC,UAAU,GAAG,CAAC,EAAE;UAChBP,KAAK,CAACxJ,IAAI,CAACmD,EAAE,CAAC;UACd4G,UAAU,GAAGP,KAAK,CAAClO,MAAM,GAAG,CAAC;QACjC;MACJ,CAAC,MAAM,IAAI4I,SAAS,IAAIlC,SAAS,GAAGC,YAAY,CAAC,EAAE;QAC/C0H,aAAa,GAAG,IAAI;MACxB;;MAEA;MACA,IAAII,UAAU,GAAG,CAAC,EAAE;QAChB;MACJ;;MAEA;MACAP,KAAK,CAACO,UAAU,CAAC,GAAG5G,EAAE;MAEtB,IAAI,CAACL,QAAQ,CAAC,IAAI,CAACD,OAAO,EAAEqB,SAAS,EAAE;QACnCG,QAAQ,EAAEmF,KAAK;QACfjF,eAAe,EAAE,CAACpB,EAAE,CAAC;QACrByF,WAAW,EAAEA,WAAW;QACxB/B,QAAQ,EAAE1D;MACd,CAAC,CAAC;MAEF,IAAIwG,aAAa,EAAE;QACf;QACAH,KAAK,CAACS,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC;MAC/B;IACJ;EACJ,CAAC,CAAC;EAEF,IAAIG,sBAAsB,GAAG;IACzBC,UAAU,EAAErI,WAAW;IACvBsI,SAAS,EAAErI,UAAU;IACrBsI,QAAQ,EAAErI,SAAS;IACnBsI,WAAW,EAAErI;EACjB,CAAC;EAED,IAAIsI,0BAA0B,GAAG,YAAY;EAC7C,IAAIC,0BAA0B,GAAG,2CAA2C;;EAE5E;AACA;AACA;AACA;AACA;EACA,SAASC,gBAAgBA,CAAA,EAAG;IACxB,IAAI,CAAClH,QAAQ,GAAGgH,0BAA0B;IAC1C,IAAI,CAAC/G,KAAK,GAAGgH,0BAA0B;IACvC,IAAI,CAACE,OAAO,GAAG,KAAK;IAEpB9H,KAAK,CAACvG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEAc,OAAO,CAACqN,gBAAgB,EAAE7H,KAAK,EAAE;IAC7BrE,OAAO,EAAE,SAASoM,SAASA,CAACxH,EAAE,EAAE;MAC5B,IAAI1E,IAAI,GAAGyL,sBAAsB,CAAC/G,EAAE,CAAC1E,IAAI,CAAC;;MAE1C;MACA,IAAIA,IAAI,KAAKqD,WAAW,EAAE;QACtB,IAAI,CAAC4I,OAAO,GAAG,IAAI;MACvB;MAEA,IAAI,CAAC,IAAI,CAACA,OAAO,EAAE;QACf;MACJ;MAEA,IAAIE,OAAO,GAAGC,sBAAsB,CAACtP,IAAI,CAAC,IAAI,EAAE4H,EAAE,EAAE1E,IAAI,CAAC;;MAEzD;MACA,IAAIA,IAAI,IAAIuD,SAAS,GAAGC,YAAY,CAAC,IAAI2I,OAAO,CAAC,CAAC,CAAC,CAACtP,MAAM,GAAGsP,OAAO,CAAC,CAAC,CAAC,CAACtP,MAAM,KAAK,CAAC,EAAE;QAClF,IAAI,CAACoP,OAAO,GAAG,KAAK;MACxB;MAEA,IAAI,CAAC5H,QAAQ,CAAC,IAAI,CAACD,OAAO,EAAEpE,IAAI,EAAE;QAC9B4F,QAAQ,EAAEuG,OAAO,CAAC,CAAC,CAAC;QACpBrG,eAAe,EAAEqG,OAAO,CAAC,CAAC,CAAC;QAC3BhC,WAAW,EAAEnH,gBAAgB;QAC7BoF,QAAQ,EAAE1D;MACd,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAAS0H,sBAAsBA,CAAC1H,EAAE,EAAE1E,IAAI,EAAE;IACtC,IAAIqM,GAAG,GAAGrL,OAAO,CAAC0D,EAAE,CAACyH,OAAO,CAAC;IAC7B,IAAIG,OAAO,GAAGtL,OAAO,CAAC0D,EAAE,CAAC6H,cAAc,CAAC;IAExC,IAAIvM,IAAI,IAAIuD,SAAS,GAAGC,YAAY,CAAC,EAAE;MACnC6I,GAAG,GAAGnL,WAAW,CAACmL,GAAG,CAACG,MAAM,CAACF,OAAO,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC;IAC9D;IAEA,OAAO,CAACD,GAAG,EAAEC,OAAO,CAAC;EACzB;EAEA,IAAIG,eAAe,GAAG;IAClBf,UAAU,EAAErI,WAAW;IACvBsI,SAAS,EAAErI,UAAU;IACrBsI,QAAQ,EAAErI,SAAS;IACnBsI,WAAW,EAAErI;EACjB,CAAC;EAED,IAAIkJ,mBAAmB,GAAG,2CAA2C;;EAErE;AACA;AACA;AACA;AACA;EACA,SAASrH,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACP,QAAQ,GAAG4H,mBAAmB;IACnC,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IAEnBxI,KAAK,CAACvG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAChC;EAEAc,OAAO,CAAC0G,UAAU,EAAElB,KAAK,EAAE;IACvBrE,OAAO,EAAE,SAAS8M,UAAUA,CAAClI,EAAE,EAAE;MAC7B,IAAI1E,IAAI,GAAGyM,eAAe,CAAC/H,EAAE,CAAC1E,IAAI,CAAC;MACnC,IAAImM,OAAO,GAAGU,UAAU,CAAC/P,IAAI,CAAC,IAAI,EAAE4H,EAAE,EAAE1E,IAAI,CAAC;MAC7C,IAAI,CAACmM,OAAO,EAAE;QACV;MACJ;MAEA,IAAI,CAAC9H,QAAQ,CAAC,IAAI,CAACD,OAAO,EAAEpE,IAAI,EAAE;QAC9B4F,QAAQ,EAAEuG,OAAO,CAAC,CAAC,CAAC;QACpBrG,eAAe,EAAEqG,OAAO,CAAC,CAAC,CAAC;QAC3BhC,WAAW,EAAEnH,gBAAgB;QAC7BoF,QAAQ,EAAE1D;MACd,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAASmI,UAAUA,CAACnI,EAAE,EAAE1E,IAAI,EAAE;IAC1B,IAAI8M,UAAU,GAAG9L,OAAO,CAAC0D,EAAE,CAACyH,OAAO,CAAC;IACpC,IAAIQ,SAAS,GAAG,IAAI,CAACA,SAAS;;IAE9B;IACA,IAAI3M,IAAI,IAAIqD,WAAW,GAAGC,UAAU,CAAC,IAAIwJ,UAAU,CAACjQ,MAAM,KAAK,CAAC,EAAE;MAC9D8P,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,IAAI;MAC1C,OAAO,CAACD,UAAU,EAAEA,UAAU,CAAC;IACnC;IAEA,IAAInQ,CAAC;MACDqQ,aAAa;MACbT,cAAc,GAAGvL,OAAO,CAAC0D,EAAE,CAAC6H,cAAc,CAAC;MAC3CU,oBAAoB,GAAG,EAAE;MACzBjP,MAAM,GAAG,IAAI,CAACA,MAAM;;IAExB;IACAgP,aAAa,GAAGF,UAAU,CAACI,MAAM,CAAC,UAASC,KAAK,EAAE;MAC9C,OAAO/M,SAAS,CAAC+M,KAAK,CAACnP,MAAM,EAAEA,MAAM,CAAC;IAC1C,CAAC,CAAC;;IAEF;IACA,IAAIgC,IAAI,KAAKqD,WAAW,EAAE;MACtB1G,CAAC,GAAG,CAAC;MACL,OAAOA,CAAC,GAAGqQ,aAAa,CAACnQ,MAAM,EAAE;QAC7B8P,SAAS,CAACK,aAAa,CAACrQ,CAAC,CAAC,CAACoQ,UAAU,CAAC,GAAG,IAAI;QAC7CpQ,CAAC,EAAE;MACP;IACJ;;IAEA;IACAA,CAAC,GAAG,CAAC;IACL,OAAOA,CAAC,GAAG4P,cAAc,CAAC1P,MAAM,EAAE;MAC9B,IAAI8P,SAAS,CAACJ,cAAc,CAAC5P,CAAC,CAAC,CAACoQ,UAAU,CAAC,EAAE;QACzCE,oBAAoB,CAAC1L,IAAI,CAACgL,cAAc,CAAC5P,CAAC,CAAC,CAAC;MAChD;;MAEA;MACA,IAAIqD,IAAI,IAAIuD,SAAS,GAAGC,YAAY,CAAC,EAAE;QACnC,OAAOmJ,SAAS,CAACJ,cAAc,CAAC5P,CAAC,CAAC,CAACoQ,UAAU,CAAC;MAClD;MACApQ,CAAC,EAAE;IACP;IAEA,IAAI,CAACsQ,oBAAoB,CAACpQ,MAAM,EAAE;MAC9B;IACJ;IAEA,OAAO;IACH;IACAqE,WAAW,CAAC8L,aAAa,CAACR,MAAM,CAACS,oBAAoB,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,EAC3EA,oBAAoB,CACvB;EACL;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEA,IAAIG,aAAa,GAAG,IAAI;EACxB,IAAIC,cAAc,GAAG,EAAE;EAEvB,SAAS9H,eAAeA,CAAA,EAAG;IACvBpB,KAAK,CAACvG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAE5B,IAAIiC,OAAO,GAAG3D,MAAM,CAAC,IAAI,CAAC2D,OAAO,EAAE,IAAI,CAAC;IACxC,IAAI,CAACqN,KAAK,GAAG,IAAI9H,UAAU,CAAC,IAAI,CAACjB,OAAO,EAAEtE,OAAO,CAAC;IAClD,IAAI,CAACwN,KAAK,GAAG,IAAIhI,UAAU,CAAC,IAAI,CAAClB,OAAO,EAAEtE,OAAO,CAAC;IAElD,IAAI,CAACyN,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,WAAW,GAAG,EAAE;EACzB;EAEA7O,OAAO,CAAC4G,eAAe,EAAEpB,KAAK,EAAE;IAC5B;AACJ;AACA;AACA;AACA;AACA;IACIrE,OAAO,EAAE,SAAS2N,UAAUA,CAACrJ,OAAO,EAAEsJ,UAAU,EAAEC,SAAS,EAAE;MACzD,IAAItC,OAAO,GAAIsC,SAAS,CAACxD,WAAW,IAAInH,gBAAiB;QACrD4K,OAAO,GAAID,SAAS,CAACxD,WAAW,IAAIjH,gBAAiB;MAEzD,IAAI0K,OAAO,IAAID,SAAS,CAACE,kBAAkB,IAAIF,SAAS,CAACE,kBAAkB,CAACC,gBAAgB,EAAE;QAC1F;MACJ;;MAEA;MACA,IAAIzC,OAAO,EAAE;QACT0C,aAAa,CAACjR,IAAI,CAAC,IAAI,EAAE4Q,UAAU,EAAEC,SAAS,CAAC;MACnD,CAAC,MAAM,IAAIC,OAAO,IAAII,gBAAgB,CAAClR,IAAI,CAAC,IAAI,EAAE6Q,SAAS,CAAC,EAAE;QAC1D;MACJ;MAEA,IAAI,CAACtJ,QAAQ,CAACD,OAAO,EAAEsJ,UAAU,EAAEC,SAAS,CAAC;IACjD,CAAC;IAED;AACJ;AACA;IACI3I,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MACxB,IAAI,CAACmI,KAAK,CAACnI,OAAO,CAAC,CAAC;MACpB,IAAI,CAACsI,KAAK,CAACtI,OAAO,CAAC,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,SAAS+I,aAAaA,CAACtI,SAAS,EAAEwI,SAAS,EAAE;IACzC,IAAIxI,SAAS,GAAGpC,WAAW,EAAE;MACzB,IAAI,CAACkK,YAAY,GAAGU,SAAS,CAACnI,eAAe,CAAC,CAAC,CAAC,CAACiH,UAAU;MAC3DmB,YAAY,CAACpR,IAAI,CAAC,IAAI,EAAEmR,SAAS,CAAC;IACtC,CAAC,MAAM,IAAIxI,SAAS,IAAIlC,SAAS,GAAGC,YAAY,CAAC,EAAE;MAC/C0K,YAAY,CAACpR,IAAI,CAAC,IAAI,EAAEmR,SAAS,CAAC;IACtC;EACJ;EAEA,SAASC,YAAYA,CAACD,SAAS,EAAE;IAC7B,IAAId,KAAK,GAAGc,SAAS,CAACnI,eAAe,CAAC,CAAC,CAAC;IAExC,IAAIqH,KAAK,CAACJ,UAAU,KAAK,IAAI,CAACQ,YAAY,EAAE;MACxC,IAAIY,SAAS,GAAG;QAACxG,CAAC,EAAEwF,KAAK,CAACpE,OAAO;QAAElB,CAAC,EAAEsF,KAAK,CAACnE;MAAO,CAAC;MACpD,IAAI,CAACwE,WAAW,CAACjM,IAAI,CAAC4M,SAAS,CAAC;MAChC,IAAIC,GAAG,GAAG,IAAI,CAACZ,WAAW;MAC1B,IAAIa,eAAe,GAAG,SAAAA,CAAA,EAAW;QAC7B,IAAI1R,CAAC,GAAGyR,GAAG,CAACzN,OAAO,CAACwN,SAAS,CAAC;QAC9B,IAAIxR,CAAC,GAAG,CAAC,CAAC,EAAE;UACRyR,GAAG,CAAC5C,MAAM,CAAC7O,CAAC,EAAE,CAAC,CAAC;QACpB;MACJ,CAAC;MACDT,UAAU,CAACmS,eAAe,EAAEjB,aAAa,CAAC;IAC9C;EACJ;EAEA,SAASY,gBAAgBA,CAACC,SAAS,EAAE;IACjC,IAAItG,CAAC,GAAGsG,SAAS,CAAC7F,QAAQ,CAACW,OAAO;MAAElB,CAAC,GAAGoG,SAAS,CAAC7F,QAAQ,CAACY,OAAO;IAClE,KAAK,IAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6Q,WAAW,CAAC3Q,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC9C,IAAI2R,CAAC,GAAG,IAAI,CAACd,WAAW,CAAC7Q,CAAC,CAAC;MAC3B,IAAI4R,EAAE,GAAG7S,IAAI,CAACC,GAAG,CAACgM,CAAC,GAAG2G,CAAC,CAAC3G,CAAC,CAAC;QAAE6G,EAAE,GAAG9S,IAAI,CAACC,GAAG,CAACkM,CAAC,GAAGyG,CAAC,CAACzG,CAAC,CAAC;MAClD,IAAI0G,EAAE,IAAIlB,cAAc,IAAImB,EAAE,IAAInB,cAAc,EAAE;QAC9C,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EAEA,IAAIoB,qBAAqB,GAAG9M,QAAQ,CAACrG,YAAY,CAACoT,KAAK,EAAE,aAAa,CAAC;EACvE,IAAIC,mBAAmB,GAAGF,qBAAqB,KAAKrT,SAAS;;EAE7D;EACA,IAAIwT,oBAAoB,GAAG,SAAS;EACpC,IAAIC,iBAAiB,GAAG,MAAM;EAC9B,IAAIC,yBAAyB,GAAG,cAAc,CAAC,CAAC;EAChD,IAAIC,iBAAiB,GAAG,MAAM;EAC9B,IAAIC,kBAAkB,GAAG,OAAO;EAChC,IAAIC,kBAAkB,GAAG,OAAO;EAChC,IAAIC,gBAAgB,GAAGC,mBAAmB,CAAC,CAAC;;EAE5C;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASC,WAAWA,CAAChL,OAAO,EAAEiL,KAAK,EAAE;IACjC,IAAI,CAACjL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkL,GAAG,CAACD,KAAK,CAAC;EACnB;EAEAD,WAAW,CAACpQ,SAAS,GAAG;IACpB;AACJ;AACA;AACA;IACIsQ,GAAG,EAAE,SAAAA,CAASD,KAAK,EAAE;MACjB;MACA,IAAIA,KAAK,IAAIT,oBAAoB,EAAE;QAC/BS,KAAK,GAAG,IAAI,CAACE,OAAO,CAAC,CAAC;MAC1B;MAEA,IAAIZ,mBAAmB,IAAI,IAAI,CAACvK,OAAO,CAAChC,OAAO,CAACsM,KAAK,IAAIQ,gBAAgB,CAACG,KAAK,CAAC,EAAE;QAC9E,IAAI,CAACjL,OAAO,CAAChC,OAAO,CAACsM,KAAK,CAACD,qBAAqB,CAAC,GAAGY,KAAK;MAC7D;MACA,IAAI,CAACG,OAAO,GAAGH,KAAK,CAACjE,WAAW,CAAC,CAAC,CAACxK,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;AACJ;AACA;IACI6O,MAAM,EAAE,SAAAA,CAAA,EAAW;MACf,IAAI,CAACH,GAAG,CAAC,IAAI,CAAClL,OAAO,CAACG,OAAO,CAACmL,WAAW,CAAC;IAC9C,CAAC;IAED;AACJ;AACA;AACA;IACIH,OAAO,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAIC,OAAO,GAAG,EAAE;MAChBhT,IAAI,CAAC,IAAI,CAAC4H,OAAO,CAACuL,WAAW,EAAE,UAASC,UAAU,EAAE;QAChD,IAAItQ,QAAQ,CAACsQ,UAAU,CAACrL,OAAO,CAACI,MAAM,EAAE,CAACiL,UAAU,CAAC,CAAC,EAAE;UACnDJ,OAAO,GAAGA,OAAO,CAAChD,MAAM,CAACoD,UAAU,CAACC,cAAc,CAAC,CAAC,CAAC;QACzD;MACJ,CAAC,CAAC;MACF,OAAOC,iBAAiB,CAACN,OAAO,CAACO,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;AACJ;AACA;AACA;IACIC,eAAe,EAAE,SAAAA,CAAStK,KAAK,EAAE;MAC7B,IAAI0C,QAAQ,GAAG1C,KAAK,CAAC0C,QAAQ;MAC7B,IAAIS,SAAS,GAAGnD,KAAK,CAAC0B,eAAe;;MAErC;MACA,IAAI,IAAI,CAAChD,OAAO,CAAC6B,OAAO,CAACgK,SAAS,EAAE;QAChC7H,QAAQ,CAAC8H,cAAc,CAAC,CAAC;QACzB;MACJ;MAEA,IAAIV,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIW,OAAO,GAAG3P,KAAK,CAACgP,OAAO,EAAET,iBAAiB,CAAC,IAAI,CAACG,gBAAgB,CAACH,iBAAiB,CAAC;MACvF,IAAIqB,OAAO,GAAG5P,KAAK,CAACgP,OAAO,EAAEP,kBAAkB,CAAC,IAAI,CAACC,gBAAgB,CAACD,kBAAkB,CAAC;MACzF,IAAIoB,OAAO,GAAG7P,KAAK,CAACgP,OAAO,EAAER,kBAAkB,CAAC,IAAI,CAACE,gBAAgB,CAACF,kBAAkB,CAAC;MAEzF,IAAImB,OAAO,EAAE;QACT;;QAEA,IAAIG,YAAY,GAAG5K,KAAK,CAACE,QAAQ,CAAC/I,MAAM,KAAK,CAAC;QAC9C,IAAI0T,aAAa,GAAG7K,KAAK,CAACuB,QAAQ,GAAG,CAAC;QACtC,IAAIuJ,cAAc,GAAG9K,KAAK,CAACoB,SAAS,GAAG,GAAG;QAE1C,IAAIwJ,YAAY,IAAIC,aAAa,IAAIC,cAAc,EAAE;UACjD;QACJ;MACJ;MAEA,IAAIH,OAAO,IAAID,OAAO,EAAE;QACpB;QACA;MACJ;MAEA,IAAID,OAAO,IACNC,OAAO,IAAIvH,SAAS,GAAG/E,oBAAqB,IAC5CuM,OAAO,IAAIxH,SAAS,GAAG9E,kBAAmB,EAAE;QAC7C,OAAO,IAAI,CAAC0M,UAAU,CAACrI,QAAQ,CAAC;MACpC;IACJ,CAAC;IAED;AACJ;AACA;AACA;IACIqI,UAAU,EAAE,SAAAA,CAASrI,QAAQ,EAAE;MAC3B,IAAI,CAAChE,OAAO,CAAC6B,OAAO,CAACgK,SAAS,GAAG,IAAI;MACrC7H,QAAQ,CAAC8H,cAAc,CAAC,CAAC;IAC7B;EACJ,CAAC;;EAED;AACA;AACA;AACA;AACA;EACA,SAASJ,iBAAiBA,CAACN,OAAO,EAAE;IAChC;IACA,IAAIhP,KAAK,CAACgP,OAAO,EAAET,iBAAiB,CAAC,EAAE;MACnC,OAAOA,iBAAiB;IAC5B;IAEA,IAAIsB,OAAO,GAAG7P,KAAK,CAACgP,OAAO,EAAER,kBAAkB,CAAC;IAChD,IAAIoB,OAAO,GAAG5P,KAAK,CAACgP,OAAO,EAAEP,kBAAkB,CAAC;;IAEhD;IACA;IACA;IACA;IACA,IAAIoB,OAAO,IAAID,OAAO,EAAE;MACpB,OAAOrB,iBAAiB;IAC5B;;IAEA;IACA,IAAIsB,OAAO,IAAID,OAAO,EAAE;MACpB,OAAOC,OAAO,GAAGrB,kBAAkB,GAAGC,kBAAkB;IAC5D;;IAEA;IACA,IAAIzO,KAAK,CAACgP,OAAO,EAAEV,yBAAyB,CAAC,EAAE;MAC3C,OAAOA,yBAAyB;IACpC;IAEA,OAAOD,iBAAiB;EAC5B;EAEA,SAASM,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACR,mBAAmB,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI+B,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,WAAW,GAAG1V,MAAM,CAAC2V,GAAG,IAAI3V,MAAM,CAAC2V,GAAG,CAACC,QAAQ;IACnD,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,CAAC,CAACjU,OAAO,CAAC,UAAS2C,GAAG,EAAE;MAEpF;MACA;MACAmR,QAAQ,CAACnR,GAAG,CAAC,GAAGoR,WAAW,GAAG1V,MAAM,CAAC2V,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAEtR,GAAG,CAAC,GAAG,IAAI;IACjF,CAAC,CAAC;IACF,OAAOmR,QAAQ;EACnB;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAII,cAAc,GAAG,CAAC;EACtB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,gBAAgB,GAAGD,WAAW;EAClC,IAAIE,eAAe,GAAG,EAAE;EACxB,IAAIC,YAAY,GAAG,EAAE;;EAErB;AACA;AACA;AACA;AACA;AACA;EACA,SAASC,UAAUA,CAAC9M,OAAO,EAAE;IACzB,IAAI,CAACA,OAAO,GAAGzG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACwT,QAAQ,EAAE/M,OAAO,IAAI,CAAC,CAAC,CAAC;IAEvD,IAAI,CAACgN,EAAE,GAAGrP,QAAQ,CAAC,CAAC;IAEpB,IAAI,CAACkC,OAAO,GAAG,IAAI;;IAEnB;IACA,IAAI,CAACG,OAAO,CAACI,MAAM,GAAGlF,WAAW,CAAC,IAAI,CAAC8E,OAAO,CAACI,MAAM,EAAE,IAAI,CAAC;IAE5D,IAAI,CAAC6M,KAAK,GAAGV,cAAc;IAE3B,IAAI,CAACW,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE;EACzB;EAEAL,UAAU,CAACrS,SAAS,GAAG;IACnB;AACJ;AACA;AACA;IACIsS,QAAQ,EAAE,CAAC,CAAC;IAEZ;AACJ;AACA;AACA;AACA;IACIhC,GAAG,EAAE,SAAAA,CAAS/K,OAAO,EAAE;MACnBzG,MAAM,CAAC,IAAI,CAACyG,OAAO,EAAEA,OAAO,CAAC;;MAE7B;MACA,IAAI,CAACH,OAAO,IAAI,IAAI,CAACA,OAAO,CAACsL,WAAW,CAACD,MAAM,CAAC,CAAC;MACjD,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIkC,aAAa,EAAE,SAAAA,CAASC,eAAe,EAAE;MACrC,IAAIxV,cAAc,CAACwV,eAAe,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE;QACxD,OAAO,IAAI;MACf;MAEA,IAAIH,YAAY,GAAG,IAAI,CAACA,YAAY;MACpCG,eAAe,GAAGC,4BAA4B,CAACD,eAAe,EAAE,IAAI,CAAC;MACrE,IAAI,CAACH,YAAY,CAACG,eAAe,CAACL,EAAE,CAAC,EAAE;QACnCE,YAAY,CAACG,eAAe,CAACL,EAAE,CAAC,GAAGK,eAAe;QAClDA,eAAe,CAACD,aAAa,CAAC,IAAI,CAAC;MACvC;MACA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIG,iBAAiB,EAAE,SAAAA,CAASF,eAAe,EAAE;MACzC,IAAIxV,cAAc,CAACwV,eAAe,EAAE,mBAAmB,EAAE,IAAI,CAAC,EAAE;QAC5D,OAAO,IAAI;MACf;MAEAA,eAAe,GAAGC,4BAA4B,CAACD,eAAe,EAAE,IAAI,CAAC;MACrE,OAAO,IAAI,CAACH,YAAY,CAACG,eAAe,CAACL,EAAE,CAAC;MAC5C,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIQ,cAAc,EAAE,SAAAA,CAASH,eAAe,EAAE;MACtC,IAAIxV,cAAc,CAACwV,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,EAAE;QACzD,OAAO,IAAI;MACf;MAEA,IAAIF,WAAW,GAAG,IAAI,CAACA,WAAW;MAClCE,eAAe,GAAGC,4BAA4B,CAACD,eAAe,EAAE,IAAI,CAAC;MACrE,IAAI9Q,OAAO,CAAC4Q,WAAW,EAAEE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9CF,WAAW,CAACnQ,IAAI,CAACqQ,eAAe,CAAC;QACjCA,eAAe,CAACG,cAAc,CAAC,IAAI,CAAC;MACxC;MACA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIC,kBAAkB,EAAE,SAAAA,CAASJ,eAAe,EAAE;MAC1C,IAAIxV,cAAc,CAACwV,eAAe,EAAE,oBAAoB,EAAE,IAAI,CAAC,EAAE;QAC7D,OAAO,IAAI;MACf;MAEAA,eAAe,GAAGC,4BAA4B,CAACD,eAAe,EAAE,IAAI,CAAC;MACrE,IAAIzT,KAAK,GAAG2C,OAAO,CAAC,IAAI,CAAC4Q,WAAW,EAAEE,eAAe,CAAC;MACtD,IAAIzT,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACuT,WAAW,CAAClG,MAAM,CAACrN,KAAK,EAAE,CAAC,CAAC;MACrC;MACA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;IACI8T,kBAAkB,EAAE,SAAAA,CAAA,EAAW;MAC3B,OAAO,IAAI,CAACP,WAAW,CAAC7U,MAAM,GAAG,CAAC;IACtC,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIqV,gBAAgB,EAAE,SAAAA,CAASN,eAAe,EAAE;MACxC,OAAO,CAAC,CAAC,IAAI,CAACH,YAAY,CAACG,eAAe,CAACL,EAAE,CAAC;IAClD,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIpL,IAAI,EAAE,SAAAA,CAAST,KAAK,EAAE;MAClB,IAAIpB,IAAI,GAAG,IAAI;MACf,IAAIkN,KAAK,GAAG,IAAI,CAACA,KAAK;MAEtB,SAASrL,IAAIA,CAACgM,KAAK,EAAE;QACjB7N,IAAI,CAACF,OAAO,CAAC+B,IAAI,CAACgM,KAAK,EAAEzM,KAAK,CAAC;MACnC;;MAEA;MACA,IAAI8L,KAAK,GAAGP,WAAW,EAAE;QACrB9K,IAAI,CAAC7B,IAAI,CAACC,OAAO,CAAC4N,KAAK,GAAGC,QAAQ,CAACZ,KAAK,CAAC,CAAC;MAC9C;MAEArL,IAAI,CAAC7B,IAAI,CAACC,OAAO,CAAC4N,KAAK,CAAC,CAAC,CAAC;;MAE1B,IAAIzM,KAAK,CAAC2M,eAAe,EAAE;QAAE;QACzBlM,IAAI,CAACT,KAAK,CAAC2M,eAAe,CAAC;MAC/B;;MAEA;MACA,IAAIb,KAAK,IAAIP,WAAW,EAAE;QACtB9K,IAAI,CAAC7B,IAAI,CAACC,OAAO,CAAC4N,KAAK,GAAGC,QAAQ,CAACZ,KAAK,CAAC,CAAC;MAC9C;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIc,OAAO,EAAE,SAAAA,CAAS5M,KAAK,EAAE;MACrB,IAAI,IAAI,CAAC6M,OAAO,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI,CAACpM,IAAI,CAACT,KAAK,CAAC;MAC3B;MACA;MACA,IAAI,CAAC8L,KAAK,GAAGJ,YAAY;IAC7B,CAAC;IAED;AACJ;AACA;AACA;IACImB,OAAO,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI5V,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAG,IAAI,CAAC+U,WAAW,CAAC7U,MAAM,EAAE;QAChC,IAAI,EAAE,IAAI,CAAC6U,WAAW,CAAC/U,CAAC,CAAC,CAAC6U,KAAK,IAAIJ,YAAY,GAAGN,cAAc,CAAC,CAAC,EAAE;UAChE,OAAO,KAAK;QAChB;QACAnU,CAAC,EAAE;MACP;MACA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;IACIyJ,SAAS,EAAE,SAAAA,CAASuH,SAAS,EAAE;MAC3B;MACA;MACA,IAAI6E,cAAc,GAAG1U,MAAM,CAAC,CAAC,CAAC,EAAE6P,SAAS,CAAC;;MAE1C;MACA,IAAI,CAACrO,QAAQ,CAAC,IAAI,CAACiF,OAAO,CAACI,MAAM,EAAE,CAAC,IAAI,EAAE6N,cAAc,CAAC,CAAC,EAAE;QACxD,IAAI,CAACC,KAAK,CAAC,CAAC;QACZ,IAAI,CAACjB,KAAK,GAAGJ,YAAY;QACzB;MACJ;;MAEA;MACA,IAAI,IAAI,CAACI,KAAK,IAAIN,gBAAgB,GAAGC,eAAe,GAAGC,YAAY,CAAC,EAAE;QAClE,IAAI,CAACI,KAAK,GAAGV,cAAc;MAC/B;MAEA,IAAI,CAACU,KAAK,GAAG,IAAI,CAACkB,OAAO,CAACF,cAAc,CAAC;;MAEzC;MACA;MACA,IAAI,IAAI,CAAChB,KAAK,IAAIT,WAAW,GAAGC,aAAa,GAAGC,WAAW,GAAGE,eAAe,CAAC,EAAE;QAC5E,IAAI,CAACmB,OAAO,CAACE,cAAc,CAAC;MAChC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIE,OAAO,EAAE,SAAAA,CAAS/E,SAAS,EAAE,CAAE,CAAC;IAAE;;IAElC;AACJ;AACA;AACA;AACA;IACIkC,cAAc,EAAE,SAAAA,CAAA,EAAW,CAAE,CAAC;IAE9B;AACJ;AACA;AACA;AACA;IACI4C,KAAK,EAAE,SAAAA,CAAA,EAAW,CAAE;EACxB,CAAC;;EAED;AACA;AACA;AACA;AACA;EACA,SAASL,QAAQA,CAACZ,KAAK,EAAE;IACrB,IAAIA,KAAK,GAAGL,eAAe,EAAE;MACzB,OAAO,QAAQ;IACnB,CAAC,MAAM,IAAIK,KAAK,GAAGP,WAAW,EAAE;MAC5B,OAAO,KAAK;IAChB,CAAC,MAAM,IAAIO,KAAK,GAAGR,aAAa,EAAE;MAC9B,OAAO,MAAM;IACjB,CAAC,MAAM,IAAIQ,KAAK,GAAGT,WAAW,EAAE;MAC5B,OAAO,OAAO;IAClB;IACA,OAAO,EAAE;EACb;;EAEA;AACA;AACA;AACA;AACA;EACA,SAAS4B,YAAYA,CAAC9J,SAAS,EAAE;IAC7B,IAAIA,SAAS,IAAIhF,cAAc,EAAE;MAC7B,OAAO,MAAM;IACjB,CAAC,MAAM,IAAIgF,SAAS,IAAIjF,YAAY,EAAE;MAClC,OAAO,IAAI;IACf,CAAC,MAAM,IAAIiF,SAAS,IAAInF,cAAc,EAAE;MACpC,OAAO,MAAM;IACjB,CAAC,MAAM,IAAImF,SAAS,IAAIlF,eAAe,EAAE;MACrC,OAAO,OAAO;IAClB;IACA,OAAO,EAAE;EACb;;EAEA;AACA;AACA;AACA;AACA;AACA;EACA,SAASkO,4BAA4BA,CAACD,eAAe,EAAEhC,UAAU,EAAE;IAC/D,IAAIxL,OAAO,GAAGwL,UAAU,CAACxL,OAAO;IAChC,IAAIA,OAAO,EAAE;MACT,OAAOA,OAAO,CAACwO,GAAG,CAAChB,eAAe,CAAC;IACvC;IACA,OAAOA,eAAe;EAC1B;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASiB,cAAcA,CAAA,EAAG;IACtBxB,UAAU,CAACzT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACrC;EAEAc,OAAO,CAACkU,cAAc,EAAExB,UAAU,EAAE;IAChC;AACJ;AACA;AACA;IACIC,QAAQ,EAAE;MACN;AACR;AACA;AACA;MACQ1L,QAAQ,EAAE;IACd,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIkN,QAAQ,EAAE,SAAAA,CAASpN,KAAK,EAAE;MACtB,IAAIqN,cAAc,GAAG,IAAI,CAACxO,OAAO,CAACqB,QAAQ;MAC1C,OAAOmN,cAAc,KAAK,CAAC,IAAIrN,KAAK,CAACE,QAAQ,CAAC/I,MAAM,KAAKkW,cAAc;IAC3E,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIL,OAAO,EAAE,SAAAA,CAAShN,KAAK,EAAE;MACrB,IAAI8L,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI/L,SAAS,GAAGC,KAAK,CAACD,SAAS;MAE/B,IAAIuN,YAAY,GAAGxB,KAAK,IAAIT,WAAW,GAAGC,aAAa,CAAC;MACxD,IAAIiC,OAAO,GAAG,IAAI,CAACH,QAAQ,CAACpN,KAAK,CAAC;;MAElC;MACA,IAAIsN,YAAY,KAAKvN,SAAS,GAAGjC,YAAY,IAAI,CAACyP,OAAO,CAAC,EAAE;QACxD,OAAOzB,KAAK,GAAGL,eAAe;MAClC,CAAC,MAAM,IAAI6B,YAAY,IAAIC,OAAO,EAAE;QAChC,IAAIxN,SAAS,GAAGlC,SAAS,EAAE;UACvB,OAAOiO,KAAK,GAAGP,WAAW;QAC9B,CAAC,MAAM,IAAI,EAAEO,KAAK,GAAGT,WAAW,CAAC,EAAE;UAC/B,OAAOA,WAAW;QACtB;QACA,OAAOS,KAAK,GAAGR,aAAa;MAChC;MACA,OAAOI,YAAY;IACvB;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAAS8B,aAAaA,CAAA,EAAG;IACrBL,cAAc,CAACjV,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAErC,IAAI,CAACsV,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,EAAE,GAAG,IAAI;EAClB;EAEAzU,OAAO,CAACuU,aAAa,EAAEL,cAAc,EAAE;IACnC;AACJ;AACA;AACA;IACIvB,QAAQ,EAAE;MACNa,KAAK,EAAE,KAAK;MACZkB,SAAS,EAAE,EAAE;MACbzN,QAAQ,EAAE,CAAC;MACXiD,SAAS,EAAE7E;IACf,CAAC;IAED6L,cAAc,EAAE,SAAAA,CAAA,EAAW;MACvB,IAAIhH,SAAS,GAAG,IAAI,CAACtE,OAAO,CAACsE,SAAS;MACtC,IAAI2G,OAAO,GAAG,EAAE;MAChB,IAAI3G,SAAS,GAAG/E,oBAAoB,EAAE;QAClC0L,OAAO,CAACjO,IAAI,CAAC0N,kBAAkB,CAAC;MACpC;MACA,IAAIpG,SAAS,GAAG9E,kBAAkB,EAAE;QAChCyL,OAAO,CAACjO,IAAI,CAACyN,kBAAkB,CAAC;MACpC;MACA,OAAOQ,OAAO;IAClB,CAAC;IAED8D,aAAa,EAAE,SAAAA,CAAS5N,KAAK,EAAE;MAC3B,IAAInB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIgP,QAAQ,GAAG,IAAI;MACnB,IAAItM,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ;MAC7B,IAAI4B,SAAS,GAAGnD,KAAK,CAACmD,SAAS;MAC/B,IAAIlB,CAAC,GAAGjC,KAAK,CAAC4B,MAAM;MACpB,IAAIO,CAAC,GAAGnC,KAAK,CAAC6B,MAAM;;MAEpB;MACA,IAAI,EAAEsB,SAAS,GAAGtE,OAAO,CAACsE,SAAS,CAAC,EAAE;QAClC,IAAItE,OAAO,CAACsE,SAAS,GAAG/E,oBAAoB,EAAE;UAC1C+E,SAAS,GAAIlB,CAAC,KAAK,CAAC,GAAIlE,cAAc,GAAIkE,CAAC,GAAG,CAAC,GAAIjE,cAAc,GAAGC,eAAe;UACnF4P,QAAQ,GAAG5L,CAAC,IAAI,IAAI,CAACwL,EAAE;UACvBlM,QAAQ,GAAGvL,IAAI,CAACC,GAAG,CAAC+J,KAAK,CAAC4B,MAAM,CAAC;QACrC,CAAC,MAAM;UACHuB,SAAS,GAAIhB,CAAC,KAAK,CAAC,GAAIpE,cAAc,GAAIoE,CAAC,GAAG,CAAC,GAAIjE,YAAY,GAAGC,cAAc;UAChF0P,QAAQ,GAAG1L,CAAC,IAAI,IAAI,CAACuL,EAAE;UACvBnM,QAAQ,GAAGvL,IAAI,CAACC,GAAG,CAAC+J,KAAK,CAAC6B,MAAM,CAAC;QACrC;MACJ;MACA7B,KAAK,CAACmD,SAAS,GAAGA,SAAS;MAC3B,OAAO0K,QAAQ,IAAItM,QAAQ,GAAG1C,OAAO,CAAC8O,SAAS,IAAIxK,SAAS,GAAGtE,OAAO,CAACsE,SAAS;IACpF,CAAC;IAEDiK,QAAQ,EAAE,SAAAA,CAASpN,KAAK,EAAE;MACtB,OAAOmN,cAAc,CAAC7T,SAAS,CAAC8T,QAAQ,CAAChW,IAAI,CAAC,IAAI,EAAE4I,KAAK,CAAC,KACrD,IAAI,CAAC8L,KAAK,GAAGT,WAAW,IAAK,EAAE,IAAI,CAACS,KAAK,GAAGT,WAAW,CAAC,IAAI,IAAI,CAACuC,aAAa,CAAC5N,KAAK,CAAE,CAAC;IAChG,CAAC;IAEDS,IAAI,EAAE,SAAAA,CAAST,KAAK,EAAE;MAElB,IAAI,CAACyN,EAAE,GAAGzN,KAAK,CAAC4B,MAAM;MACtB,IAAI,CAAC8L,EAAE,GAAG1N,KAAK,CAAC6B,MAAM;MAEtB,IAAIsB,SAAS,GAAG8J,YAAY,CAACjN,KAAK,CAACmD,SAAS,CAAC;MAE7C,IAAIA,SAAS,EAAE;QACXnD,KAAK,CAAC2M,eAAe,GAAG,IAAI,CAAC9N,OAAO,CAAC4N,KAAK,GAAGtJ,SAAS;MAC1D;MACA,IAAI,CAACzJ,MAAM,CAAC+G,IAAI,CAACrJ,IAAI,CAAC,IAAI,EAAE4I,KAAK,CAAC;IACtC;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAAS8N,eAAeA,CAAA,EAAG;IACvBX,cAAc,CAACjV,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACzC;EAEAc,OAAO,CAAC6U,eAAe,EAAEX,cAAc,EAAE;IACrC;AACJ;AACA;AACA;IACIvB,QAAQ,EAAE;MACNa,KAAK,EAAE,OAAO;MACdkB,SAAS,EAAE,CAAC;MACZzN,QAAQ,EAAE;IACd,CAAC;IAEDiK,cAAc,EAAE,SAAAA,CAAA,EAAW;MACvB,OAAO,CAACd,iBAAiB,CAAC;IAC9B,CAAC;IAED+D,QAAQ,EAAE,SAAAA,CAASpN,KAAK,EAAE;MACtB,OAAO,IAAI,CAACtG,MAAM,CAAC0T,QAAQ,CAAChW,IAAI,CAAC,IAAI,EAAE4I,KAAK,CAAC,KACxChK,IAAI,CAACC,GAAG,CAAC+J,KAAK,CAACoC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAACvD,OAAO,CAAC8O,SAAS,IAAI,IAAI,CAAC7B,KAAK,GAAGT,WAAW,CAAC;IACxF,CAAC;IAED5K,IAAI,EAAE,SAAAA,CAAST,KAAK,EAAE;MAClB,IAAIA,KAAK,CAACoC,KAAK,KAAK,CAAC,EAAE;QACnB,IAAI2L,KAAK,GAAG/N,KAAK,CAACoC,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK;QAC1CpC,KAAK,CAAC2M,eAAe,GAAG,IAAI,CAAC9N,OAAO,CAAC4N,KAAK,GAAGsB,KAAK;MACtD;MACA,IAAI,CAACrU,MAAM,CAAC+G,IAAI,CAACrJ,IAAI,CAAC,IAAI,EAAE4I,KAAK,CAAC;IACtC;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAASgO,eAAeA,CAAA,EAAG;IACvBrC,UAAU,CAACzT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAEjC,IAAI,CAAC8V,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,IAAI;EACtB;EAEAjV,OAAO,CAAC+U,eAAe,EAAErC,UAAU,EAAE;IACjC;AACJ;AACA;AACA;IACIC,QAAQ,EAAE;MACNa,KAAK,EAAE,OAAO;MACdvM,QAAQ,EAAE,CAAC;MACXiO,IAAI,EAAE,GAAG;MAAE;MACXR,SAAS,EAAE,CAAC,CAAC;IACjB,CAAC;;IAEDxD,cAAc,EAAE,SAAAA,CAAA,EAAW;MACvB,OAAO,CAAChB,iBAAiB,CAAC;IAC9B,CAAC;IAED6D,OAAO,EAAE,SAAAA,CAAShN,KAAK,EAAE;MACrB,IAAInB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIuP,aAAa,GAAGpO,KAAK,CAACE,QAAQ,CAAC/I,MAAM,KAAK0H,OAAO,CAACqB,QAAQ;MAC9D,IAAImO,aAAa,GAAGrO,KAAK,CAACuB,QAAQ,GAAG1C,OAAO,CAAC8O,SAAS;MACtD,IAAIW,SAAS,GAAGtO,KAAK,CAACoB,SAAS,GAAGvC,OAAO,CAACsP,IAAI;MAE9C,IAAI,CAACD,MAAM,GAAGlO,KAAK;;MAEnB;MACA;MACA,IAAI,CAACqO,aAAa,IAAI,CAACD,aAAa,IAAKpO,KAAK,CAACD,SAAS,IAAIlC,SAAS,GAAGC,YAAY,CAAC,IAAI,CAACwQ,SAAU,EAAE;QAClG,IAAI,CAACvB,KAAK,CAAC,CAAC;MAChB,CAAC,MAAM,IAAI/M,KAAK,CAACD,SAAS,GAAGpC,WAAW,EAAE;QACtC,IAAI,CAACoP,KAAK,CAAC,CAAC;QACZ,IAAI,CAACkB,MAAM,GAAG7X,iBAAiB,CAAC,YAAW;UACvC,IAAI,CAAC0V,KAAK,GAAGN,gBAAgB;UAC7B,IAAI,CAACoB,OAAO,CAAC,CAAC;QAClB,CAAC,EAAE/N,OAAO,CAACsP,IAAI,EAAE,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAInO,KAAK,CAACD,SAAS,GAAGlC,SAAS,EAAE;QACpC,OAAO2N,gBAAgB;MAC3B;MACA,OAAOE,YAAY;IACvB,CAAC;IAEDqB,KAAK,EAAE,SAAAA,CAAA,EAAW;MACdwB,YAAY,CAAC,IAAI,CAACN,MAAM,CAAC;IAC7B,CAAC;IAEDxN,IAAI,EAAE,SAAAA,CAAST,KAAK,EAAE;MAClB,IAAI,IAAI,CAAC8L,KAAK,KAAKN,gBAAgB,EAAE;QACjC;MACJ;MAEA,IAAIxL,KAAK,IAAKA,KAAK,CAACD,SAAS,GAAGlC,SAAU,EAAE;QACxC,IAAI,CAACa,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC4N,KAAK,GAAG,IAAI,EAAEzM,KAAK,CAAC;MACvD,CAAC,MAAM;QACH,IAAI,CAACkO,MAAM,CAAC/M,SAAS,GAAGjL,GAAG,CAAC,CAAC;QAC7B,IAAI,CAACwI,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC4N,KAAK,EAAE,IAAI,CAACyB,MAAM,CAAC;MACtD;IACJ;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAASM,gBAAgBA,CAAA,EAAG;IACxBrB,cAAc,CAACjV,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACzC;EAEAc,OAAO,CAACuV,gBAAgB,EAAErB,cAAc,EAAE;IACtC;AACJ;AACA;AACA;IACIvB,QAAQ,EAAE;MACNa,KAAK,EAAE,QAAQ;MACfkB,SAAS,EAAE,CAAC;MACZzN,QAAQ,EAAE;IACd,CAAC;IAEDiK,cAAc,EAAE,SAAAA,CAAA,EAAW;MACvB,OAAO,CAACd,iBAAiB,CAAC;IAC9B,CAAC;IAED+D,QAAQ,EAAE,SAAAA,CAASpN,KAAK,EAAE;MACtB,OAAO,IAAI,CAACtG,MAAM,CAAC0T,QAAQ,CAAChW,IAAI,CAAC,IAAI,EAAE4I,KAAK,CAAC,KACxChK,IAAI,CAACC,GAAG,CAAC+J,KAAK,CAACsC,QAAQ,CAAC,GAAG,IAAI,CAACzD,OAAO,CAAC8O,SAAS,IAAI,IAAI,CAAC7B,KAAK,GAAGT,WAAW,CAAC;IACvF;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAASoD,eAAeA,CAAA,EAAG;IACvBtB,cAAc,CAACjV,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACzC;EAEAc,OAAO,CAACwV,eAAe,EAAEtB,cAAc,EAAE;IACrC;AACJ;AACA;AACA;IACIvB,QAAQ,EAAE;MACNa,KAAK,EAAE,OAAO;MACdkB,SAAS,EAAE,EAAE;MACb3K,QAAQ,EAAE,GAAG;MACbG,SAAS,EAAE/E,oBAAoB,GAAGC,kBAAkB;MACpD6B,QAAQ,EAAE;IACd,CAAC;IAEDiK,cAAc,EAAE,SAAAA,CAAA,EAAW;MACvB,OAAOqD,aAAa,CAAClU,SAAS,CAAC6Q,cAAc,CAAC/S,IAAI,CAAC,IAAI,CAAC;IAC5D,CAAC;IAEDgW,QAAQ,EAAE,SAAAA,CAASpN,KAAK,EAAE;MACtB,IAAImD,SAAS,GAAG,IAAI,CAACtE,OAAO,CAACsE,SAAS;MACtC,IAAIH,QAAQ;MAEZ,IAAIG,SAAS,IAAI/E,oBAAoB,GAAGC,kBAAkB,CAAC,EAAE;QACzD2E,QAAQ,GAAGhD,KAAK,CAAC8B,eAAe;MACpC,CAAC,MAAM,IAAIqB,SAAS,GAAG/E,oBAAoB,EAAE;QACzC4E,QAAQ,GAAGhD,KAAK,CAACgC,gBAAgB;MACrC,CAAC,MAAM,IAAImB,SAAS,GAAG9E,kBAAkB,EAAE;QACvC2E,QAAQ,GAAGhD,KAAK,CAACkC,gBAAgB;MACrC;MAEA,OAAO,IAAI,CAACxI,MAAM,CAAC0T,QAAQ,CAAChW,IAAI,CAAC,IAAI,EAAE4I,KAAK,CAAC,IACzCmD,SAAS,GAAGnD,KAAK,CAAC0B,eAAe,IACjC1B,KAAK,CAACuB,QAAQ,GAAG,IAAI,CAAC1C,OAAO,CAAC8O,SAAS,IACvC3N,KAAK,CAACwC,WAAW,IAAI,IAAI,CAAC3D,OAAO,CAACqB,QAAQ,IAC1CjK,GAAG,CAAC+M,QAAQ,CAAC,GAAG,IAAI,CAACnE,OAAO,CAACmE,QAAQ,IAAIhD,KAAK,CAACD,SAAS,GAAGlC,SAAS;IAC5E,CAAC;IAED4C,IAAI,EAAE,SAAAA,CAAST,KAAK,EAAE;MAClB,IAAImD,SAAS,GAAG8J,YAAY,CAACjN,KAAK,CAAC0B,eAAe,CAAC;MACnD,IAAIyB,SAAS,EAAE;QACX,IAAI,CAACzE,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC4N,KAAK,GAAGtJ,SAAS,EAAEnD,KAAK,CAAC;MAC5D;MAEA,IAAI,CAACtB,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC4N,KAAK,EAAEzM,KAAK,CAAC;IAChD;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAAS0O,aAAaA,CAAA,EAAG;IACrB/C,UAAU,CAACzT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;;IAEjC;IACA;IACA,IAAI,CAACwW,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IAEpB,IAAI,CAACX,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACW,KAAK,GAAG,CAAC;EAClB;EAEA5V,OAAO,CAACyV,aAAa,EAAE/C,UAAU,EAAE;IAC/B;AACJ;AACA;AACA;IACIC,QAAQ,EAAE;MACNa,KAAK,EAAE,KAAK;MACZvM,QAAQ,EAAE,CAAC;MACX4O,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,GAAG;MAAE;MACfZ,IAAI,EAAE,GAAG;MAAE;MACXR,SAAS,EAAE,CAAC;MAAE;MACdqB,YAAY,EAAE,EAAE,CAAC;IACrB,CAAC;;IAED7E,cAAc,EAAE,SAAAA,CAAA,EAAW;MACvB,OAAO,CAACf,yBAAyB,CAAC;IACtC,CAAC;IAED4D,OAAO,EAAE,SAAAA,CAAShN,KAAK,EAAE;MACrB,IAAInB,OAAO,GAAG,IAAI,CAACA,OAAO;MAE1B,IAAIuP,aAAa,GAAGpO,KAAK,CAACE,QAAQ,CAAC/I,MAAM,KAAK0H,OAAO,CAACqB,QAAQ;MAC9D,IAAImO,aAAa,GAAGrO,KAAK,CAACuB,QAAQ,GAAG1C,OAAO,CAAC8O,SAAS;MACtD,IAAIsB,cAAc,GAAGjP,KAAK,CAACoB,SAAS,GAAGvC,OAAO,CAACsP,IAAI;MAEnD,IAAI,CAACpB,KAAK,CAAC,CAAC;MAEZ,IAAK/M,KAAK,CAACD,SAAS,GAAGpC,WAAW,IAAM,IAAI,CAACkR,KAAK,KAAK,CAAE,EAAE;QACvD,OAAO,IAAI,CAACK,WAAW,CAAC,CAAC;MAC7B;;MAEA;MACA;MACA,IAAIb,aAAa,IAAIY,cAAc,IAAIb,aAAa,EAAE;QAClD,IAAIpO,KAAK,CAACD,SAAS,IAAIlC,SAAS,EAAE;UAC9B,OAAO,IAAI,CAACqR,WAAW,CAAC,CAAC;QAC7B;QAEA,IAAIC,aAAa,GAAG,IAAI,CAACR,KAAK,GAAI3O,KAAK,CAACmB,SAAS,GAAG,IAAI,CAACwN,KAAK,GAAG9P,OAAO,CAACkQ,QAAQ,GAAI,IAAI;QACzF,IAAIK,aAAa,GAAG,CAAC,IAAI,CAACR,OAAO,IAAIpN,WAAW,CAAC,IAAI,CAACoN,OAAO,EAAE5O,KAAK,CAACiB,MAAM,CAAC,GAAGpC,OAAO,CAACmQ,YAAY;QAEnG,IAAI,CAACL,KAAK,GAAG3O,KAAK,CAACmB,SAAS;QAC5B,IAAI,CAACyN,OAAO,GAAG5O,KAAK,CAACiB,MAAM;QAE3B,IAAI,CAACmO,aAAa,IAAI,CAACD,aAAa,EAAE;UAClC,IAAI,CAACN,KAAK,GAAG,CAAC;QAClB,CAAC,MAAM;UACH,IAAI,CAACA,KAAK,IAAI,CAAC;QACnB;QAEA,IAAI,CAACX,MAAM,GAAGlO,KAAK;;QAEnB;QACA;QACA,IAAIqP,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAGhQ,OAAO,CAACiQ,IAAI;QACxC,IAAIO,QAAQ,KAAK,CAAC,EAAE;UAChB;UACA;UACA,IAAI,CAAC,IAAI,CAAC9C,kBAAkB,CAAC,CAAC,EAAE;YAC5B,OAAOf,gBAAgB;UAC3B,CAAC,MAAM;YACH,IAAI,CAACyC,MAAM,GAAG7X,iBAAiB,CAAC,YAAW;cACvC,IAAI,CAAC0V,KAAK,GAAGN,gBAAgB;cAC7B,IAAI,CAACoB,OAAO,CAAC,CAAC;YAClB,CAAC,EAAE/N,OAAO,CAACkQ,QAAQ,EAAE,IAAI,CAAC;YAC1B,OAAO1D,WAAW;UACtB;QACJ;MACJ;MACA,OAAOK,YAAY;IACvB,CAAC;IAEDwD,WAAW,EAAE,SAAAA,CAAA,EAAW;MACpB,IAAI,CAACjB,MAAM,GAAG7X,iBAAiB,CAAC,YAAW;QACvC,IAAI,CAAC0V,KAAK,GAAGJ,YAAY;MAC7B,CAAC,EAAE,IAAI,CAAC7M,OAAO,CAACkQ,QAAQ,EAAE,IAAI,CAAC;MAC/B,OAAOrD,YAAY;IACvB,CAAC;IAEDqB,KAAK,EAAE,SAAAA,CAAA,EAAW;MACdwB,YAAY,CAAC,IAAI,CAACN,MAAM,CAAC;IAC7B,CAAC;IAEDxN,IAAI,EAAE,SAAAA,CAAA,EAAW;MACb,IAAI,IAAI,CAACqL,KAAK,IAAIN,gBAAgB,EAAE;QAChC,IAAI,CAAC0C,MAAM,CAACmB,QAAQ,GAAG,IAAI,CAACR,KAAK;QACjC,IAAI,CAACnQ,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC4N,KAAK,EAAE,IAAI,CAACyB,MAAM,CAAC;MACtD;IACJ;EACJ,CAAC,CAAC;;EAEF;AACA;AACA;AACA;AACA;AACA;EACA,SAASoB,MAAMA,CAAC5S,OAAO,EAAEmC,OAAO,EAAE;IAC9BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvBA,OAAO,CAACoL,WAAW,GAAGlQ,WAAW,CAAC8E,OAAO,CAACoL,WAAW,EAAEqF,MAAM,CAAC1D,QAAQ,CAAC2D,MAAM,CAAC;IAC9E,OAAO,IAAIC,OAAO,CAAC9S,OAAO,EAAEmC,OAAO,CAAC;EACxC;;EAEA;AACA;AACA;EACAyQ,MAAM,CAACG,OAAO,GAAG,OAAO;;EAExB;AACA;AACA;AACA;EACAH,MAAM,CAAC1D,QAAQ,GAAG;IACd;AACJ;AACA;AACA;AACA;AACA;IACI8D,SAAS,EAAE,KAAK;IAEhB;AACJ;AACA;AACA;AACA;AACA;IACI1F,WAAW,EAAEd,oBAAoB;IAEjC;AACJ;AACA;AACA;IACIjK,MAAM,EAAE,IAAI;IAEZ;AACJ;AACA;AACA;AACA;AACA;AACA;IACIH,WAAW,EAAE,IAAI;IAEjB;AACJ;AACA;AACA;AACA;IACIW,UAAU,EAAE,IAAI;IAEhB;AACJ;AACA;AACA;AACA;IACI8P,MAAM,EAAE;IACJ;IACA,CAACf,gBAAgB,EAAE;MAACvP,MAAM,EAAE;IAAK,CAAC,CAAC,EACnC,CAAC6O,eAAe,EAAE;MAAC7O,MAAM,EAAE;IAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAC9C,CAACwP,eAAe,EAAE;MAACtL,SAAS,EAAE/E;IAAoB,CAAC,CAAC,EACpD,CAACoP,aAAa,EAAE;MAACrK,SAAS,EAAE/E;IAAoB,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EAC7D,CAACsQ,aAAa,CAAC,EACf,CAACA,aAAa,EAAE;MAACjC,KAAK,EAAE,WAAW;MAAEqC,IAAI,EAAE;IAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EACvD,CAACd,eAAe,CAAC,CACpB;IAED;AACJ;AACA;AACA;AACA;IACI2B,QAAQ,EAAE;MACN;AACR;AACA;AACA;AACA;MACQC,UAAU,EAAE,MAAM;MAElB;AACR;AACA;AACA;AACA;MACQC,WAAW,EAAE,MAAM;MAEnB;AACR;AACA;AACA;AACA;AACA;AACA;MACQC,YAAY,EAAE,MAAM;MAEpB;AACR;AACA;AACA;AACA;MACQC,cAAc,EAAE,MAAM;MAEtB;AACR;AACA;AACA;AACA;MACQC,QAAQ,EAAE,MAAM;MAEhB;AACR;AACA;AACA;AACA;AACA;MACQC,iBAAiB,EAAE;IACvB;EACJ,CAAC;EAED,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,WAAW,GAAG,CAAC;;EAEnB;AACA;AACA;AACA;AACA;AACA;EACA,SAASX,OAAOA,CAAC9S,OAAO,EAAEmC,OAAO,EAAE;IAC/B,IAAI,CAACA,OAAO,GAAGzG,MAAM,CAAC,CAAC,CAAC,EAAEkX,MAAM,CAAC1D,QAAQ,EAAE/M,OAAO,IAAI,CAAC,CAAC,CAAC;IAEzD,IAAI,CAACA,OAAO,CAACC,WAAW,GAAG,IAAI,CAACD,OAAO,CAACC,WAAW,IAAIpC,OAAO;IAE9D,IAAI,CAAC0T,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAAC7P,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC0J,WAAW,GAAG,EAAE;IACrB,IAAI,CAACoG,WAAW,GAAG,CAAC,CAAC;IAErB,IAAI,CAAC3T,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsD,KAAK,GAAGT,mBAAmB,CAAC,IAAI,CAAC;IACtC,IAAI,CAACyK,WAAW,GAAG,IAAIN,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC7K,OAAO,CAACmL,WAAW,CAAC;IAElEsG,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAE1BxZ,IAAI,CAAC,IAAI,CAAC+H,OAAO,CAACoL,WAAW,EAAE,UAASsG,IAAI,EAAE;MAC1C,IAAIrG,UAAU,GAAG,IAAI,CAACsG,GAAG,CAAC,IAAKD,IAAI,CAAC,CAAC,CAAC,CAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACjDA,IAAI,CAAC,CAAC,CAAC,IAAIrG,UAAU,CAAC+B,aAAa,CAACsE,IAAI,CAAC,CAAC,CAAC,CAAC;MAC5CA,IAAI,CAAC,CAAC,CAAC,IAAIrG,UAAU,CAACmC,cAAc,CAACkE,IAAI,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,EAAE,IAAI,CAAC;EACZ;EAEAf,OAAO,CAAClW,SAAS,GAAG;IAChB;AACJ;AACA;AACA;AACA;IACIsQ,GAAG,EAAE,SAAAA,CAAS/K,OAAO,EAAE;MACnBzG,MAAM,CAAC,IAAI,CAACyG,OAAO,EAAEA,OAAO,CAAC;;MAE7B;MACA,IAAIA,OAAO,CAACmL,WAAW,EAAE;QACrB,IAAI,CAACA,WAAW,CAACD,MAAM,CAAC,CAAC;MAC7B;MACA,IAAIlL,OAAO,CAACC,WAAW,EAAE;QACrB;QACA,IAAI,CAACkB,KAAK,CAACV,OAAO,CAAC,CAAC;QACpB,IAAI,CAACU,KAAK,CAAC1H,MAAM,GAAGuG,OAAO,CAACC,WAAW;QACvC,IAAI,CAACkB,KAAK,CAACd,IAAI,CAAC,CAAC;MACrB;MACA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIuR,IAAI,EAAE,SAAAA,CAASC,KAAK,EAAE;MAClB,IAAI,CAACnQ,OAAO,CAACoQ,OAAO,GAAGD,KAAK,GAAGP,WAAW,GAAGD,IAAI;IACrD,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIxP,SAAS,EAAE,SAAAA,CAASuH,SAAS,EAAE;MAC3B,IAAI1H,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIA,OAAO,CAACoQ,OAAO,EAAE;QACjB;MACJ;;MAEA;MACA,IAAI,CAAC3G,WAAW,CAACM,eAAe,CAACrC,SAAS,CAAC;MAE3C,IAAIiC,UAAU;MACd,IAAID,WAAW,GAAG,IAAI,CAACA,WAAW;;MAElC;MACA;MACA;MACA,IAAI2G,aAAa,GAAGrQ,OAAO,CAACqQ,aAAa;;MAEzC;MACA;MACA,IAAI,CAACA,aAAa,IAAKA,aAAa,IAAIA,aAAa,CAAC9E,KAAK,GAAGN,gBAAiB,EAAE;QAC7EoF,aAAa,GAAGrQ,OAAO,CAACqQ,aAAa,GAAG,IAAI;MAChD;MAEA,IAAI3Z,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAGgT,WAAW,CAAC9S,MAAM,EAAE;QAC3B+S,UAAU,GAAGD,WAAW,CAAChT,CAAC,CAAC;;QAE3B;QACA;QACA;QACA;QACA;QACA;QACA,IAAIsJ,OAAO,CAACoQ,OAAO,KAAKR,WAAW;QAAM;QACjC,CAACS,aAAa,IAAI1G,UAAU,IAAI0G,aAAa;QAAI;QACjD1G,UAAU,CAACsC,gBAAgB,CAACoE,aAAa,CAAC,CAAC,EAAE;UAAE;UACnD1G,UAAU,CAACxJ,SAAS,CAACuH,SAAS,CAAC;QACnC,CAAC,MAAM;UACHiC,UAAU,CAAC6C,KAAK,CAAC,CAAC;QACtB;;QAEA;QACA;QACA,IAAI,CAAC6D,aAAa,IAAI1G,UAAU,CAAC4B,KAAK,IAAIT,WAAW,GAAGC,aAAa,GAAGC,WAAW,CAAC,EAAE;UAClFqF,aAAa,GAAGrQ,OAAO,CAACqQ,aAAa,GAAG1G,UAAU;QACtD;QACAjT,CAAC,EAAE;MACP;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIiW,GAAG,EAAE,SAAAA,CAAShD,UAAU,EAAE;MACtB,IAAIA,UAAU,YAAYyB,UAAU,EAAE;QAClC,OAAOzB,UAAU;MACrB;MAEA,IAAID,WAAW,GAAG,IAAI,CAACA,WAAW;MAClC,KAAK,IAAIhT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgT,WAAW,CAAC9S,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzC,IAAIgT,WAAW,CAAChT,CAAC,CAAC,CAAC4H,OAAO,CAAC4N,KAAK,IAAIvC,UAAU,EAAE;UAC5C,OAAOD,WAAW,CAAChT,CAAC,CAAC;QACzB;MACJ;MACA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIuZ,GAAG,EAAE,SAAAA,CAAStG,UAAU,EAAE;MACtB,IAAIxT,cAAc,CAACwT,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;QACzC,OAAO,IAAI;MACf;;MAEA;MACA,IAAI2G,QAAQ,GAAG,IAAI,CAAC3D,GAAG,CAAChD,UAAU,CAACrL,OAAO,CAAC4N,KAAK,CAAC;MACjD,IAAIoE,QAAQ,EAAE;QACV,IAAI,CAACC,MAAM,CAACD,QAAQ,CAAC;MACzB;MAEA,IAAI,CAAC5G,WAAW,CAACpO,IAAI,CAACqO,UAAU,CAAC;MACjCA,UAAU,CAACxL,OAAO,GAAG,IAAI;MAEzB,IAAI,CAACsL,WAAW,CAACD,MAAM,CAAC,CAAC;MACzB,OAAOG,UAAU;IACrB,CAAC;IAED;AACJ;AACA;AACA;AACA;IACI4G,MAAM,EAAE,SAAAA,CAAS5G,UAAU,EAAE;MACzB,IAAIxT,cAAc,CAACwT,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE;QAC5C,OAAO,IAAI;MACf;MAEAA,UAAU,GAAG,IAAI,CAACgD,GAAG,CAAChD,UAAU,CAAC;;MAEjC;MACA,IAAIA,UAAU,EAAE;QACZ,IAAID,WAAW,GAAG,IAAI,CAACA,WAAW;QAClC,IAAIxR,KAAK,GAAG2C,OAAO,CAAC6O,WAAW,EAAEC,UAAU,CAAC;QAE5C,IAAIzR,KAAK,KAAK,CAAC,CAAC,EAAE;UACdwR,WAAW,CAACnE,MAAM,CAACrN,KAAK,EAAE,CAAC,CAAC;UAC5B,IAAI,CAACuR,WAAW,CAACD,MAAM,CAAC,CAAC;QAC7B;MACJ;MAEA,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIgH,EAAE,EAAE,SAAAA,CAASC,MAAM,EAAE5W,OAAO,EAAE;MAC1B,IAAI4W,MAAM,KAAKtb,SAAS,EAAE;QACtB;MACJ;MACA,IAAI0E,OAAO,KAAK1E,SAAS,EAAE;QACvB;MACJ;MAEA,IAAI0a,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5BtZ,IAAI,CAACuD,QAAQ,CAAC2W,MAAM,CAAC,EAAE,UAASvE,KAAK,EAAE;QACnC2D,QAAQ,CAAC3D,KAAK,CAAC,GAAG2D,QAAQ,CAAC3D,KAAK,CAAC,IAAI,EAAE;QACvC2D,QAAQ,CAAC3D,KAAK,CAAC,CAAC5Q,IAAI,CAACzB,OAAO,CAAC;MACjC,CAAC,CAAC;MACF,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACI6W,GAAG,EAAE,SAAAA,CAASD,MAAM,EAAE5W,OAAO,EAAE;MAC3B,IAAI4W,MAAM,KAAKtb,SAAS,EAAE;QACtB;MACJ;MAEA,IAAI0a,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5BtZ,IAAI,CAACuD,QAAQ,CAAC2W,MAAM,CAAC,EAAE,UAASvE,KAAK,EAAE;QACnC,IAAI,CAACrS,OAAO,EAAE;UACV,OAAOgW,QAAQ,CAAC3D,KAAK,CAAC;QAC1B,CAAC,MAAM;UACH2D,QAAQ,CAAC3D,KAAK,CAAC,IAAI2D,QAAQ,CAAC3D,KAAK,CAAC,CAAC3G,MAAM,CAAC1K,OAAO,CAACgV,QAAQ,CAAC3D,KAAK,CAAC,EAAErS,OAAO,CAAC,EAAE,CAAC,CAAC;QACnF;MACJ,CAAC,CAAC;MACF,OAAO,IAAI;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIqG,IAAI,EAAE,SAAAA,CAASgM,KAAK,EAAEyE,IAAI,EAAE;MACxB;MACA,IAAI,IAAI,CAACrS,OAAO,CAAC6Q,SAAS,EAAE;QACxByB,eAAe,CAAC1E,KAAK,EAAEyE,IAAI,CAAC;MAChC;;MAEA;MACA,IAAId,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC3D,KAAK,CAAC,IAAI,IAAI,CAAC2D,QAAQ,CAAC3D,KAAK,CAAC,CAAClR,KAAK,CAAC,CAAC;MACnE,IAAI,CAAC6U,QAAQ,IAAI,CAACA,QAAQ,CAACjZ,MAAM,EAAE;QAC/B;MACJ;MAEA+Z,IAAI,CAAC5W,IAAI,GAAGmS,KAAK;MACjByE,IAAI,CAAC1G,cAAc,GAAG,YAAW;QAC7B0G,IAAI,CAACxO,QAAQ,CAAC8H,cAAc,CAAC,CAAC;MAClC,CAAC;MAED,IAAIvT,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAGmZ,QAAQ,CAACjZ,MAAM,EAAE;QACxBiZ,QAAQ,CAACnZ,CAAC,CAAC,CAACia,IAAI,CAAC;QACjBja,CAAC,EAAE;MACP;IACJ,CAAC;IAED;AACJ;AACA;AACA;IACIqI,OAAO,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,CAAC5C,OAAO,IAAI4T,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;MAE3C,IAAI,CAACF,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAAC7P,OAAO,GAAG,CAAC,CAAC;MACjB,IAAI,CAACP,KAAK,CAACV,OAAO,CAAC,CAAC;MACpB,IAAI,CAAC5C,OAAO,GAAG,IAAI;IACvB;EACJ,CAAC;;EAED;AACA;AACA;AACA;AACA;EACA,SAAS4T,cAAcA,CAAC5R,OAAO,EAAE8R,GAAG,EAAE;IAClC,IAAI9T,OAAO,GAAGgC,OAAO,CAAChC,OAAO;IAC7B,IAAI,CAACA,OAAO,CAACsM,KAAK,EAAE;MAChB;IACJ;IACA,IAAI5M,IAAI;IACRtF,IAAI,CAAC4H,OAAO,CAACG,OAAO,CAAC8Q,QAAQ,EAAE,UAAShG,KAAK,EAAEnS,IAAI,EAAE;MACjD4E,IAAI,GAAGH,QAAQ,CAACS,OAAO,CAACsM,KAAK,EAAExR,IAAI,CAAC;MACpC,IAAIgZ,GAAG,EAAE;QACL9R,OAAO,CAAC2R,WAAW,CAACjU,IAAI,CAAC,GAAGM,OAAO,CAACsM,KAAK,CAAC5M,IAAI,CAAC;QAC/CM,OAAO,CAACsM,KAAK,CAAC5M,IAAI,CAAC,GAAGuN,KAAK;MAC/B,CAAC,MAAM;QACHjN,OAAO,CAACsM,KAAK,CAAC5M,IAAI,CAAC,GAAGsC,OAAO,CAAC2R,WAAW,CAACjU,IAAI,CAAC,IAAI,EAAE;MACzD;IACJ,CAAC,CAAC;IACF,IAAI,CAACoU,GAAG,EAAE;MACN9R,OAAO,CAAC2R,WAAW,GAAG,CAAC,CAAC;IAC5B;EACJ;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASc,eAAeA,CAAC1E,KAAK,EAAEyE,IAAI,EAAE;IAClC,IAAIE,YAAY,GAAG5b,QAAQ,CAAC6b,WAAW,CAAC,OAAO,CAAC;IAChDD,YAAY,CAACE,SAAS,CAAC7E,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC2E,YAAY,CAACG,OAAO,GAAGL,IAAI;IAC3BA,IAAI,CAAC5Y,MAAM,CAACkZ,aAAa,CAACJ,YAAY,CAAC;EAC3C;EAEAhZ,MAAM,CAACkX,MAAM,EAAE;IACX3R,WAAW,EAAEA,WAAW;IACxBC,UAAU,EAAEA,UAAU;IACtBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAE1BsN,cAAc,EAAEA,cAAc;IAC9BC,WAAW,EAAEA,WAAW;IACxBC,aAAa,EAAEA,aAAa;IAC5BC,WAAW,EAAEA,WAAW;IACxBC,gBAAgB,EAAEA,gBAAgB;IAClCC,eAAe,EAAEA,eAAe;IAChCC,YAAY,EAAEA,YAAY;IAE1B3N,cAAc,EAAEA,cAAc;IAC9BC,cAAc,EAAEA,cAAc;IAC9BC,eAAe,EAAEA,eAAe;IAChCC,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA,cAAc;IAC9BC,oBAAoB,EAAEA,oBAAoB;IAC1CC,kBAAkB,EAAEA,kBAAkB;IACtCC,aAAa,EAAEA,aAAa;IAE5BkR,OAAO,EAAEA,OAAO;IAChB/Q,KAAK,EAAEA,KAAK;IACZiL,WAAW,EAAEA,WAAW;IAExB/J,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA,UAAU;IACtBF,iBAAiB,EAAEA,iBAAiB;IACpCG,eAAe,EAAEA,eAAe;IAChCyG,gBAAgB,EAAEA,gBAAgB;IAElCqF,UAAU,EAAEA,UAAU;IACtBwB,cAAc,EAAEA,cAAc;IAC9BsE,GAAG,EAAE/C,aAAa;IAClBgD,GAAG,EAAElE,aAAa;IAClBmE,KAAK,EAAElD,eAAe;IACtBmD,KAAK,EAAE9D,eAAe;IACtB+D,MAAM,EAAErD,gBAAgB;IACxBsD,KAAK,EAAE9D,eAAe;IAEtB+C,EAAE,EAAE7W,iBAAiB;IACrB+W,GAAG,EAAEzW,oBAAoB;IACzB1D,IAAI,EAAEA,IAAI;IACViC,KAAK,EAAEA,KAAK;IACZH,MAAM,EAAEA,MAAM;IACdR,MAAM,EAAEA,MAAM;IACda,OAAO,EAAEA,OAAO;IAChBxC,MAAM,EAAEA,MAAM;IACdwF,QAAQ,EAAEA;EACd,CAAC,CAAC;;EAEF;EACA;EACA,IAAI8V,UAAU,GAAI,OAAOxc,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAI,OAAOqJ,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,CAAC,CAAG,CAAC,CAAC;EACvGmT,UAAU,CAACzC,MAAM,GAAGA,MAAM;EAE1B,IAAI,OAAO0C,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5CD,MAAM,CAAC,YAAW;MACd,OAAO1C,MAAM;IACjB,CAAC,CAAC;EACN,CAAC,MAAM,IAAI,OAAO4C,MAAM,IAAI,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACvDD,MAAM,CAACC,OAAO,GAAG7C,MAAM;EAC3B,CAAC,MAAM;IACH/Z,MAAM,CAACE,UAAU,CAAC,GAAG6Z,MAAM;EAC/B;AAEA,CAAC,EAAE/Z,MAAM,EAAEC,QAAQ,EAAE,QAAQ,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import { FormGroup } from '@angular/forms';\nimport { DataTableDirective } from 'angular-datatables';\nimport { AppConfig } from 'app/app-config';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport { REGEX } from '../../../helpers/regex';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/loading.service\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i7 from \"app/services/sponsor.service\";\nimport * as i8 from \"app/services/s3.service\";\nimport * as i9 from \"app/layout/components/content-header/content-header.component\";\nimport * as i10 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i11 from \"angular-datatables\";\nimport * as i12 from \"./manage-sponsors-editor/manage-sponsors-editor.component\";\nexport class ManageSponsorsComponent {\n  constructor(_loadingService, _titleService, _trans, _commonsService, _http, _coreSidebarService, _sponsorService, _s3Service) {\n    this._loadingService = _loadingService;\n    this._titleService = _titleService;\n    this._trans = _trans;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._coreSidebarService = _coreSidebarService;\n    this._sponsorService = _sponsorService;\n    this._s3Service = _s3Service;\n    this.appLogo = AppConfig.Fake_Player_Photo;\n    this.dtElement = DataTableDirective;\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.sideBarID = 'manage-sponsors';\n    this.sideBarName = 'manage-sponsors';\n    this.action = 'create';\n    this.sponsors = [];\n    this.form = new FormGroup({});\n    this.isUpdating = false;\n    this.model = {\n      logo: AppConfig.Fake_Player_Photo,\n      name: AppConfig.Fake_Player_Name,\n      url: AppConfig.Fake_Player_Link,\n      description: AppConfig.Fake_Player_Description\n    };\n    this.options = {};\n    this.fields = [{\n      fieldGroupClassName: 'row',\n      fieldGroup: [{\n        className: 'col-12',\n        type: 'image-cropper',\n        key: 'logo',\n        props: {\n          translate: true,\n          label: this._trans.instant('Sponsor logo'),\n          required: true,\n          upload_url: `${environment.apiUrl}/s3`,\n          dir: 'sponsors',\n          accept: 'image/png,image/jpg,image/jpeg',\n          maxFileSize: 12000,\n          useCropperDialog: true,\n          config: {\n            width: 300,\n            height: 300,\n            responsiveArea: true,\n            resizableArea: true,\n            keepAspectRatio: false,\n            extraZoomOut: true,\n            autoCrop: false,\n            fill: '#FFFFFF'\n          },\n          test_image: 'assets/images/logo/logo.png'\n        },\n        validation: {\n          messages: {\n            required: (error, field) => {\n              return this._trans.instant('This field is required', {\n                field: this._trans.instant(field.templateOptions.label)\n              });\n            }\n          }\n        }\n      }, {\n        className: 'col-12',\n        type: 'input',\n        key: 'name',\n        templateOptions: {\n          required: true,\n          label: this._trans.instant('Sponsor name'),\n          maxLength: 50,\n          placeholder: this._trans.instant('Type sponsor name'),\n          pattern: /[\\S]/\n        },\n        validation: {\n          messages: {\n            required: (error, field) => {\n              return this._trans.instant('This field is required', {\n                field: this._trans.instant(field.templateOptions.label)\n              });\n            }\n          }\n        }\n      }, {\n        className: 'col-12',\n        type: 'input',\n        key: 'url',\n        templateOptions: {\n          label: this._trans.instant('Sponsor URL'),\n          maxLength: 250,\n          placeholder: this._trans.instant('Type sponsor URL'),\n          pattern: /^(https?:\\/\\/)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/\n        },\n        validation: {\n          show: true,\n          messages: {\n            pattern: this._trans.instant('Invalid URL, example: https://example.com')\n          }\n        }\n      }, {\n        className: 'col-12',\n        type: 'textarea',\n        key: 'description',\n        templateOptions: {\n          label: this._trans.instant('Description'),\n          placeholder: this._trans.instant('Type description'),\n          maxLength: 250,\n          rows: 3\n        }\n      }]\n    }];\n    this._titleService.setTitle('Manage Sponsors');\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Manage Sponsors'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: 'Settings',\n          isLink: false\n        }, {\n          name: 'Manage Sponsors',\n          isLink: false\n        }]\n      }\n    };\n  }\n  ngOnInit() {\n    console.log('Sponsors');\n    this.initTable();\n  }\n  initTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      ajax: (dataTablesParameters, callback) => {\n        this._http.post(`${environment.apiUrl}/sponsors/all`, dataTablesParameters).subscribe(resp => {\n          this.sponsors = resp.data;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      select: 'single',\n      rowId: 'id',\n      // Disable responsive to enable proper horizontal scrolling\n      responsive: false,\n      scrollX: true,\n      scrollCollapse: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [{\n        targets: 0,\n        className: 'text-center'\n      }, {\n        targets: 1,\n        className: 'text-center'\n      }, {\n        targets: 2\n      }, {\n        targets: 3\n      }, {\n        targets: 4\n      }],\n      columns: [{\n        sortable: false,\n        data: 'order',\n        className: 'reorder text-center',\n        title: 'Order',\n        width: '60px',\n        render: function (data, type, row) {\n          const img = document.createElement('img');\n          img.src = 'assets/images/icons/Group 1.png';\n          img.alt = 'Order Icon';\n          img.style.width = '10px';\n          img.style.height = 'auto';\n          return img.outerHTML;\n        }\n      }, {\n        data: 'logo',\n        title: 'SPONSOR LOGO',\n        className: 'text-center',\n        width: '80px',\n        render: function (data, type, row) {\n          //create image\n          let img = document.createElement('img');\n          img.src = data;\n          img.id = `img-${row.id}`;\n          img.style.width = '50px';\n          img.style.height = 'auto';\n          img.style.objectFit = 'cover';\n          img.style.backgroundColor = '#fff';\n          img.style.objectFit = 'cover';\n          if (data == null) {\n            img.src = 'assets/images/logo/ezactive_1024x1024.png';\n          }\n          // check get image error\n          img.onerror = function () {\n            img.src = 'assets/images/logo/ezactive_1024x1024.png';\n            // set src by row id\n            $(`#img-${row.id}`).attr('src', img.src);\n          };\n          return type === 'print' ? 'print' : img.outerHTML;\n        }\n      }, {\n        data: 'name',\n        title: 'SPONSOR NAME',\n        width: '200px',\n        render: function (data, type, row) {\n          if (type === 'display' && data !== null) {\n            // Create a span with proper styling for long names\n            return `<span class=\"sponsor-name\" title=\"${data}\">${data}</span>`;\n          }\n          return data;\n        }\n      }, {\n        data: 'url',\n        title: 'SPONSOR URL',\n        width: '250px',\n        render: function (data, type, row) {\n          if (type === 'display' && data !== null && data !== '') {\n            // Create a clickable link with proper styling for long URLs\n            const displayUrl = data.length > 40 ? data.substring(0, 37) + '...' : data;\n            return `<a href=\"${data}\" target=\"_blank\" class=\"sponsor-url\" title=\"${data}\">${displayUrl}</a>`;\n          }\n          return data || '';\n        }\n      }, {\n        data: 'description',\n        title: 'DESCRIPTION',\n        width: '300px',\n        render: function (data, type, row) {\n          if (type === 'display' && data !== null) {\n            return data.length > 120 ? data.substring(0, 120) + '...' : data;\n          }\n          return data;\n        }\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: '<i class=\"feather icon-plus\"></i> ' + this._trans.instant('Add New Sponsor'),\n          action: () => this.toggleSidebar()\n        }, {\n          text: '<i class=\"feather icon-edit\"></i> ' + this._trans.instant('Edit'),\n          action: (e, dt, node, config) => {\n            const selectedRowData = dt.row({\n              selected: true\n            }).data();\n            if (selectedRowData) {\n              this.editor('edit', selectedRowData);\n            }\n          },\n          extend: 'selected'\n        }, {\n          text: '<i class=\"feather icon-trash\"></i> ' + this._trans.instant('Delete'),\n          action: (e, dt, node, config) => {\n            const selectedRowData = dt.row({\n              selected: true\n            }).data();\n            if (selectedRowData) {\n              this.editor('remove', selectedRowData);\n            }\n          },\n          extend: 'selected'\n        }]\n      },\n      rowReorder: {\n        dataSrc: 'order'\n      }\n    };\n    $('#ManageSponsors').on('row-reorder.dt', (e, diff, edit) => {\n      if (!this.isUpdating) {\n        this.isUpdating = true;\n        $('#ManageSponsors').off('row-reorder.dt');\n        $('#ManageSponsors').css('pointer-events', 'none');\n        diff.forEach(row => {\n          const sponsor = this.sponsors.find(s => s.id === +row.node.id);\n          if (sponsor) {\n            sponsor.order = row.newData;\n          }\n        });\n        this.sponsors.sort((a, b) => a.order - b.order);\n        this._sponsorService.updateSponsorOrder(this.sponsors).subscribe(data => {\n          console.log('Update success', data);\n          $('#ManageSponsors').DataTable().ajax.reload(() => {\n            $('#ManageSponsors').on('row-reorder.dt', this.rowReorderHandler.bind(this));\n            $('#ManageSponsors').css('pointer-events', 'auto');\n            this.isUpdating = false;\n          });\n        }, error => {\n          console.error('Update error', error);\n          $('#ManageSponsors').on('row-reorder.dt', this.rowReorderHandler.bind(this));\n          $('#ManageSponsors').css('pointer-events', 'auto');\n          this.isUpdating = false;\n        });\n      }\n    });\n  }\n  rowReorderHandler(e, diff, edit) {\n    if (!this.isUpdating) {\n      this.isUpdating = true;\n      $('#ManageSponsors').off('row-reorder.dt');\n      $('#ManageSponsors').css('pointer-events', 'none');\n      diff.forEach(row => {\n        const sponsor = this.sponsors.find(s => s.id === +row.node.id);\n        if (sponsor) {\n          sponsor.order = row.newData;\n        }\n      });\n      this.sponsors.sort((a, b) => a.order - b.order);\n      this._sponsorService.updateSponsorOrder(this.sponsors).subscribe(data => {\n        $('#ManageSponsors').DataTable().ajax.reload(() => {\n          $('#ManageSponsors').on('row-reorder.dt', this.rowReorderHandler.bind(this));\n          $('#ManageSponsors').css('pointer-events', 'auto');\n          this.isUpdating = false;\n        });\n      }, error => {\n        $('#ManageSponsors').on('row-reorder.dt', this.rowReorderHandler.bind(this));\n        $('#ManageSponsors').css('pointer-events', 'auto');\n        this.isUpdating = false;\n      });\n    }\n  }\n  editor(action, row) {\n    if (action == 'edit') {\n      console.log(action, row);\n      this.action = action;\n      this.model = row;\n      this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen();\n    } else if (action == 'remove') {\n      Swal.fire({\n        title: this._trans.instant('Are you sure?'),\n        text: this._trans.instant(\"You won't be able to revert this!\"),\n        icon: 'warning',\n        showCancelButton: true,\n        confirmButtonText: this._trans.instant('Yes'),\n        cancelButtonText: this._trans.instant('No')\n      }).then(result => {\n        if (result.isConfirmed) {\n          this._sponsorService.deleteSponsor(row.id).subscribe(data => {\n            this.refreshTable();\n            Swal.fire({\n              title: this._trans.instant('Deleted!'),\n              text: this._trans.instant('Deleted successfully'),\n              icon: 'success',\n              showCancelButton: false,\n              confirmButtonText: this._trans.instant('OK')\n            });\n          });\n        }\n      });\n    }\n  }\n  toggleSidebar() {\n    this.action = 'create';\n    this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen();\n  }\n  refreshTable() {\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload(() => {\n        // Re-setup horizontal scrolling after reload\n        setTimeout(() => {\n          this.setupHorizontalScrolling();\n        }, 100);\n      }, false);\n    });\n  }\n  getAllSponsors() {\n    this._loadingService.show();\n    return this._sponsorService.getAllSponsors().toPromise().then(res => {\n      this.sponsors = res.data;\n    });\n  }\n  reloadDataTable() {\n    const table = $('#ManageSponsors').DataTable();\n    table.ajax.reload(() => {\n      // Re-setup horizontal scrolling after reload\n      setTimeout(() => {\n        this.setupHorizontalScrolling();\n      }, 100);\n    });\n  }\n  onSponsorChanged(sponsor) {\n    console.log('Sponsor data has been changed', sponsor);\n    this.reloadDataTable();\n  }\n  ngAfterViewInit() {\n    this.dtTrigger.next();\n    // Ensure proper horizontal scrolling setup after table initialization\n    setTimeout(() => {\n      this.dtElement.dtInstance.then(dtInstance => {\n        // Adjust columns and redraw\n        dtInstance.columns.adjust().draw();\n        // Force horizontal scrolling setup\n        this.setupHorizontalScrolling();\n      });\n    }, 200);\n  }\n  setupHorizontalScrolling() {\n    const tableWrapper = document.getElementById('ManageSponsors_wrapper');\n    const scrollBody = tableWrapper?.querySelector('.dataTables_scrollBody');\n    const table = document.getElementById('ManageSponsors');\n    if (scrollBody && table) {\n      // Ensure minimum width is applied\n      table.style.minWidth = '890px';\n      table.style.width = 'auto';\n      // Add scroll event listener for visual feedback\n      scrollBody.addEventListener('scroll', () => {\n        const scrollLeft = scrollBody.scrollLeft;\n        const maxScroll = scrollBody.scrollWidth - scrollBody.clientWidth;\n        // Add/remove classes based on scroll position for visual indicators\n        if (scrollLeft > 0) {\n          scrollBody.classList.add('scrolled-left');\n        } else {\n          scrollBody.classList.remove('scrolled-left');\n        }\n        if (scrollLeft < maxScroll) {\n          scrollBody.classList.add('scrolled-right');\n        } else {\n          scrollBody.classList.remove('scrolled-right');\n        }\n      });\n      // Trigger initial scroll event to set up indicators\n      scrollBody.dispatchEvent(new Event('scroll'));\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n  }\n  onClose(event) {\n    console.log(event);\n    if (!event.isSubmitted && !REGEX.URL.test(event.formValue.logo) && event.formValue.logo) {\n      console.log('Remove image');\n      this._s3Service.removeImage(event.formValue.logo).subscribe();\n    } else {\n      this.reloadDataTable();\n    }\n    // if (event.isSubmitted) {\n    //   this.reloadDataTable();\n    // }\n  }\n  static #_ = this.ɵfac = function ManageSponsorsComponent_Factory(t) {\n    return new (t || ManageSponsorsComponent)(i0.ɵɵdirectiveInject(i1.LoadingService), i0.ɵɵdirectiveInject(i2.Title), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.CoreSidebarService), i0.ɵɵdirectiveInject(i7.SponsorService), i0.ɵɵdirectiveInject(i8.S3Service));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ManageSponsorsComponent,\n    selectors: [[\"app-manage-sponsors\"]],\n    viewQuery: function ManageSponsorsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 10,\n    vars: 12,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"card\"], [1, \"mt-1\", \"table-responsive-container\", \"sponsors-table-container\"], [\"id\", \"ManageSponsors\", \"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", \"table-white-space\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\", \"id\"], [\"coreSidebar\", \"\"], [3, \"form\", \"fields\", \"options\", \"model\", \"action\", \"sideBarID\", \"sideBarName\", \"emitSponsor\", \"onClose\"]],\n    template: function ManageSponsorsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"table\", 5);\n        i0.ɵɵelement(6, \"thead\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(7, \"core-sidebar\", 6, 7)(9, \"app-manage-sponsors-editor\", 8);\n        i0.ɵɵlistener(\"emitSponsor\", function ManageSponsorsComponent_Template_app_manage_sponsors_editor_emitSponsor_9_listener($event) {\n          return ctx.onSponsorChanged($event);\n        })(\"onClose\", function ManageSponsorsComponent_Template_app_manage_sponsors_editor_onClose_9_listener($event) {\n          return ctx.onClose($event);\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"name\", ctx.sideBarName)(\"id\", ctx.sideBarID);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"options\", ctx.options)(\"model\", ctx.model)(\"action\", ctx.action)(\"sideBarID\", ctx.sideBarID)(\"sideBarName\", ctx.sideBarName);\n      }\n    },\n    dependencies: [i9.ContentHeaderComponent, i10.CoreSidebarComponent, i11.DataTableDirective, i12.ManageSponsorsEditorComponent],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.p-10px[_ngcontent-%COMP%] {\\n  padding: 10px !important;\\n}\\n\\n.p-15px[_ngcontent-%COMP%] {\\n  padding: 15px !important;\\n}\\n\\n.pr-20px[_ngcontent-%COMP%] {\\n  padding-right: 20px !important;\\n}\\n\\n.p-20px[_ngcontent-%COMP%] {\\n  padding: 20px !important;\\n}\\n\\n.table-responsive-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.table-responsive-container.sponsors-table-container[_ngcontent-%COMP%] {\\n  overflow: visible;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\ntable.dataTable.dtr-column[_ngcontent-%COMP%]    > tbody[_ngcontent-%COMP%]    > tr.parent[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child.dtr-control:before {\\n  content: \\\"\\\\f077\\\";\\n  font-family: \\\"Font Awesome 5 Pro\\\";\\n  border: none;\\n  color: #6f6b7d;\\n  background: none;\\n  box-shadow: none;\\n}\\n\\ntable.dataTable.dtr-column[_ngcontent-%COMP%]    > tbody[_ngcontent-%COMP%]    > tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child.dtr-control:before {\\n  content: \\\"\\\\f078\\\";\\n  font-family: \\\"Font Awesome 5 Pro\\\";\\n  border: none;\\n  color: #6f6b7d;\\n  background: none;\\n  box-shadow: none;\\n}\\n\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollHead[_ngcontent-%COMP%]   .dataTables_scrollHeadInner[_ngcontent-%COMP%], #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]   .dataTables_scrollHeadInner[_ngcontent-%COMP%] {\\n  min-width: 890px !important;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollHead[_ngcontent-%COMP%]   .dataTables_scrollHeadInner[_ngcontent-%COMP%]   table[_ngcontent-%COMP%], #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]   .dataTables_scrollHeadInner[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  min-width: 890px !important;\\n  width: auto !important;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollHead[_ngcontent-%COMP%]   table[_ngcontent-%COMP%], #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  min-width: 890px !important;\\n  width: auto !important;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%] {\\n  overflow-x: auto !important;\\n  overflow-y: visible !important;\\n  -webkit-overflow-scrolling: touch;\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%), linear-gradient(90deg, rgb(255, 255, 255) 0%, rgba(255, 255, 255, 0) 100%), linear-gradient(90deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%), linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);\\n  background-position: left center, right center, left center, right center;\\n  background-repeat: no-repeat;\\n  background-size: 20px 100%, 20px 100%, 10px 100%, 10px 100%;\\n  background-attachment: local, local, scroll, scroll;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 12px;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n  border: 1px solid #e9ecef;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(45deg, #6c757d, #495057);\\n  border-radius: 6px;\\n  border: 1px solid #adb5bd;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(45deg, #495057, #343a40);\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar-corner {\\n  background: #f8f9fa;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody.scrolled-left[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 20px;\\n  background: linear-gradient(90deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);\\n  pointer-events: none;\\n  z-index: 2;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody.scrolled-right[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 20px;\\n  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);\\n  pointer-events: none;\\n  z-index: 2;\\n}\\n#ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollHead[_ngcontent-%COMP%] {\\n  overflow: hidden !important;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n\\n.sponsor-name[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  white-space: nowrap;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n\\n.sponsor-url[_ngcontent-%COMP%] {\\n  color: #7367f0;\\n  text-decoration: none;\\n  display: inline-block;\\n  white-space: nowrap;\\n}\\n.sponsor-url[_ngcontent-%COMP%]:hover {\\n  color: #5e50ee;\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 768px) {\\n  #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%] {\\n    position: relative;\\n  }\\n  #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    height: 16px;\\n  }\\n  #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n    background: linear-gradient(45deg, #007bff, #0056b3);\\n  }\\n  #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n    background: linear-gradient(45deg, #0056b3, #004085);\\n  }\\n  #ManageSponsors_wrapper[_ngcontent-%COMP%]   .dataTables_scrollBody[_ngcontent-%COMP%]::after {\\n    content: \\\"\\u2190 Scroll to see more \\u2192\\\";\\n    position: absolute;\\n    bottom: -25px;\\n    left: 50%;\\n    transform: translateX(-50%);\\n    font-size: 12px;\\n    color: #6c757d;\\n    background: rgba(255, 255, 255, 0.9);\\n    padding: 2px 8px;\\n    border-radius: 4px;\\n    white-space: nowrap;\\n    pointer-events: none;\\n    z-index: 10;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAAQA,SAAS,QAAO,gBAAgB;AAKxC,SAAQC,kBAAkB,QAAO,oBAAoB;AACrD,SAAQC,SAAS,QAAO,gBAAgB;AAMxC,SAAQC,WAAW,QAAO,0BAA0B;AACpD,SAAQC,OAAO,QAAO,MAAM;AAC5B,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAAQC,KAAK,QAAO,wBAAwB;;;;;;;;;;;;;;AAO5C,OAAM,MAAOC,uBAAuB;EAmHhCC,YACWC,eAA+B,EAC/BC,aAAoB,EACpBC,MAAwB,EACxBC,eAA+B,EAC9BC,KAAiB,EACjBC,mBAAuC,EACvCC,eAA+B,EAC/BC,UAAqB;IAPtB,KAAAP,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,UAAU,GAAVA,UAAU;IA1HtB,KAAAC,OAAO,GAAGf,SAAS,CAACgB,iBAAiB;IAGrC,KAAAC,SAAS,GAAQlB,kBAAkB;IACnC,KAAAmB,SAAS,GAAQ,EAAE;IACnB,KAAAC,SAAS,GAAiB,IAAIjB,OAAO,EAAE;IACvC,KAAAkB,SAAS,GAAW,iBAAiB;IACrC,KAAAC,WAAW,GAAW,iBAAiB;IACvC,KAAAC,MAAM,GAAG,QAAQ;IACjB,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,IAAI,GAAG,IAAI1B,SAAS,CAAC,EAAE,CAAC;IACxB,KAAA2B,UAAU,GAAY,KAAK;IAC3B,KAAAC,KAAK,GAAY;MACbC,IAAI,EAAE3B,SAAS,CAACgB,iBAAiB;MACjCY,IAAI,EAAE5B,SAAS,CAAC6B,gBAAgB;MAChCC,GAAG,EAAE9B,SAAS,CAAC+B,gBAAgB;MAC/BC,WAAW,EAAEhC,SAAS,CAACiC;KAC1B;IACD,KAAAC,OAAO,GAAsB,EAAE;IAC/B,KAAAC,MAAM,GAAwB,CAC1B;MACIC,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CACR;QACIC,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,eAAe;QACrBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE;UACHC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE,IAAI,CAAClC,MAAM,CAACmC,OAAO,CAAC,cAAc,CAAC;UAC1CC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,GAAG7C,WAAW,CAAC8C,MAAM,KAAK;UACtCC,GAAG,EAAE,UAAU;UACfC,MAAM,EAAE,gCAAgC;UACxCC,WAAW,EAAE,KAAK;UAClBC,gBAAgB,EAAE,IAAI;UACtBC,MAAM,EAAE;YACJC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXC,cAAc,EAAE,IAAI;YACpBC,aAAa,EAAE,IAAI;YACnBC,eAAe,EAAE,KAAK;YACtBC,YAAY,EAAE,IAAI;YAClBC,QAAQ,EAAE,KAAK;YACfC,IAAI,EAAE;WACT;UACDC,UAAU,EAAE;SACf;QACDC,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNlB,QAAQ,EAAEA,CAACmB,KAAK,EAAEC,KAAwB,KAAI;cAC1C,OAAO,IAAI,CAACxD,MAAM,CAACmC,OAAO,CAAC,wBAAwB,EAAE;gBACjDqB,KAAK,EAAE,IAAI,CAACxD,MAAM,CAACmC,OAAO,CAACqB,KAAK,CAACC,eAAe,CAACvB,KAAK;eACzD,CAAC;YACN;;;OAGX,EACD;QACIL,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,OAAO;QACbC,GAAG,EAAE,MAAM;QACX0B,eAAe,EAAE;UACbrB,QAAQ,EAAE,IAAI;UACdF,KAAK,EAAE,IAAI,CAAClC,MAAM,CAACmC,OAAO,CAAC,cAAc,CAAC;UAC1CuB,SAAS,EAAE,EAAE;UACbC,WAAW,EAAE,IAAI,CAAC3D,MAAM,CAACmC,OAAO,CAAC,mBAAmB,CAAC;UACrDyB,OAAO,EAAE;SACZ;QACDP,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNlB,QAAQ,EAAEA,CAACmB,KAAK,EAAEC,KAAwB,KAAI;cAC1C,OAAO,IAAI,CAACxD,MAAM,CAACmC,OAAO,CAAC,wBAAwB,EAAE;gBACjDqB,KAAK,EAAE,IAAI,CAACxD,MAAM,CAACmC,OAAO,CAACqB,KAAK,CAACC,eAAe,CAACvB,KAAK;eACzD,CAAC;YACN;;;OAGX,EACD;QACIL,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,OAAO;QACbC,GAAG,EAAE,KAAK;QACV0B,eAAe,EAAE;UACbvB,KAAK,EAAE,IAAI,CAAClC,MAAM,CAACmC,OAAO,CAAC,aAAa,CAAC;UACzCuB,SAAS,EAAE,GAAG;UACdC,WAAW,EAAE,IAAI,CAAC3D,MAAM,CAACmC,OAAO,CAAC,kBAAkB,CAAC;UACpDyB,OAAO,EACH;SACP;QACDP,UAAU,EAAE;UACRQ,IAAI,EAAE,IAAI;UACVP,QAAQ,EAAE;YACNM,OAAO,EAAE,IAAI,CAAC5D,MAAM,CAACmC,OAAO,CACxB,2CAA2C;;;OAI1D,EACD;QACIN,SAAS,EAAE,QAAQ;QACnBC,IAAI,EAAE,UAAU;QAChBC,GAAG,EAAE,aAAa;QAClB0B,eAAe,EAAE;UACbvB,KAAK,EAAE,IAAI,CAAClC,MAAM,CAACmC,OAAO,CAAC,aAAa,CAAC;UACzCwB,WAAW,EAAE,IAAI,CAAC3D,MAAM,CAACmC,OAAO,CAAC,kBAAkB,CAAC;UACpDuB,SAAS,EAAE,GAAG;UACdI,IAAI,EAAE;;OAEb;KAER,CACJ;IAYG,IAAI,CAAC/D,aAAa,CAACgE,QAAQ,CAAC,iBAAiB,CAAC;IAC9C,IAAI,CAACC,aAAa,GAAG;MACjBC,WAAW,EAAE,IAAI,CAACjE,MAAM,CAACmC,OAAO,CAAC,iBAAiB,CAAC;MACnD+B,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACRrC,IAAI,EAAE,EAAE;QACRsC,KAAK,EAAE,CACH;UACIjD,IAAI,EAAE,UAAU;UAChBkD,MAAM,EAAE;SACX,EACD;UACIlD,IAAI,EAAE,iBAAiB;UACvBkD,MAAM,EAAE;SACX;;KAGZ;EACL;EAEAC,QAAQA,CAAA;IACJC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACvB,IAAI,CAACC,SAAS,EAAE;EACpB;EAEAA,SAASA,CAAA;IACL,IAAI,CAAChE,SAAS,GAAG;MACbiE,GAAG,EAAE,IAAI,CAACzE,eAAe,CAAC0E,iBAAiB,CAACD,GAAG;MAC/CE,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC1C,IAAI,CAAC5E,KAAK,CACL6E,IAAI,CAAM,GAAGvF,WAAW,CAAC8C,MAAM,eAAe,EAAEuC,oBAAoB,CAAC,CACrEG,SAAS,CAAEC,IAAS,IAAI;UACrB,IAAI,CAACnE,QAAQ,GAAGmE,IAAI,CAACC,IAAI;UACzBJ,QAAQ,CAAC;YACLK,YAAY,EAAEF,IAAI,CAACE,YAAY;YAC/BC,eAAe,EAAEH,IAAI,CAACG,eAAe;YACrCF,IAAI,EAAED,IAAI,CAACC;WACd,CAAC;QACN,CAAC,CAAC;MACV,CAAC;MACDG,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACX;MACAC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,IAAI,CAACzF,eAAe,CAAC0E,iBAAiB,CAACgB,IAAI;MACrDC,UAAU,EAAE,CACR;QAACC,OAAO,EAAE,CAAC;QAAEhE,SAAS,EAAE;MAAa,CAAC,EACtC;QAACgE,OAAO,EAAE,CAAC;QAAEhE,SAAS,EAAE;MAAa,CAAC,EACtC;QAACgE,OAAO,EAAE;MAAC,CAAC,EACZ;QAACA,OAAO,EAAE;MAAC,CAAC,EACZ;QAACA,OAAO,EAAE;MAAC,CAAC,CACf;MACDC,OAAO,EAAE,CACL;QACIC,QAAQ,EAAE,KAAK;QACfb,IAAI,EAAE,OAAO;QACbrD,SAAS,EAAE,qBAAqB;QAChCmE,KAAK,EAAE,OAAO;QACdpD,KAAK,EAAE,MAAM;QACbqD,MAAM,EAAE,SAAAA,CAAUf,IAAS,EAAEpD,IAAS,EAAEoE,GAAQ;UAC5C,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACzCF,GAAG,CAACG,GAAG,GAAG,iCAAiC;UAC3CH,GAAG,CAACI,GAAG,GAAG,YAAY;UACtBJ,GAAG,CAACK,KAAK,CAAC5D,KAAK,GAAG,MAAM;UACxBuD,GAAG,CAACK,KAAK,CAAC3D,MAAM,GAAG,MAAM;UAEzB,OAAOsD,GAAG,CAACM,SAAS;QACxB;OACH,EACD;QACIvB,IAAI,EAAE,MAAM;QACZc,KAAK,EAAE,cAAc;QACrBnE,SAAS,EAAE,aAAa;QACxBe,KAAK,EAAE,MAAM;QACbqD,MAAM,EAAE,SAAAA,CAAUf,IAAS,EAAEpD,IAAS,EAAEoE,GAAQ;UAC5C;UACA,IAAIC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACvCF,GAAG,CAACG,GAAG,GAAGpB,IAAI;UACdiB,GAAG,CAACO,EAAE,GAAG,OAAOR,GAAG,CAACQ,EAAE,EAAE;UACxBP,GAAG,CAACK,KAAK,CAAC5D,KAAK,GAAG,MAAM;UACxBuD,GAAG,CAACK,KAAK,CAAC3D,MAAM,GAAG,MAAM;UACzBsD,GAAG,CAACK,KAAK,CAACG,SAAS,GAAG,OAAO;UAC7BR,GAAG,CAACK,KAAK,CAACI,eAAe,GAAG,MAAM;UAClCT,GAAG,CAACK,KAAK,CAACG,SAAS,GAAG,OAAO;UAC7B,IAAIzB,IAAI,IAAI,IAAI,EAAE;YACdiB,GAAG,CAACG,GAAG,GAAG,2CAA2C;;UAEzD;UACAH,GAAG,CAACU,OAAO,GAAG;YACVV,GAAG,CAACG,GAAG,GAAG,2CAA2C;YACrD;YACAQ,CAAC,CAAC,QAAQZ,GAAG,CAACQ,EAAE,EAAE,CAAC,CAACK,IAAI,CAAC,KAAK,EAAEZ,GAAG,CAACG,GAAG,CAAC;UAC5C,CAAC;UACD,OAAOxE,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGqE,GAAG,CAACM,SAAS;QACrD;OACH,EACD;QACIvB,IAAI,EAAE,MAAM;QACZc,KAAK,EAAE,cAAc;QACrBpD,KAAK,EAAE,OAAO;QACdqD,MAAM,EAAE,SAAAA,CAAUf,IAAS,EAAEpD,IAAS,EAAEoE,GAAQ;UAC5C,IAAIpE,IAAI,KAAK,SAAS,IAAIoD,IAAI,KAAK,IAAI,EAAE;YACrC;YACA,OAAO,qCAAqCA,IAAI,KAAKA,IAAI,SAAS;;UAEtE,OAAOA,IAAI;QACf;OACH,EACD;QACIA,IAAI,EAAE,KAAK;QACXc,KAAK,EAAE,aAAa;QACpBpD,KAAK,EAAE,OAAO;QACdqD,MAAM,EAAE,SAAAA,CAAUf,IAAS,EAAEpD,IAAS,EAAEoE,GAAQ;UAC5C,IAAIpE,IAAI,KAAK,SAAS,IAAIoD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;YACpD;YACA,MAAM8B,UAAU,GAAG9B,IAAI,CAAC+B,MAAM,GAAG,EAAE,GAAG/B,IAAI,CAACgC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGhC,IAAI;YAC1E,OAAO,YAAYA,IAAI,gDAAgDA,IAAI,KAAK8B,UAAU,MAAM;;UAEpG,OAAO9B,IAAI,IAAI,EAAE;QACrB;OACH,EACD;QACIA,IAAI,EAAE,aAAa;QACnBc,KAAK,EAAE,aAAa;QACpBpD,KAAK,EAAE,OAAO;QACdqD,MAAM,EAAE,SAAAA,CAAUf,IAAS,EAAEpD,IAAS,EAAEoE,GAAQ;UAC5C,IAAIpE,IAAI,KAAK,SAAS,IAAIoD,IAAI,KAAK,IAAI,EAAE;YACrC,OAAOA,IAAI,CAAC+B,MAAM,GAAG,GAAG,GAAG/B,IAAI,CAACgC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAGhC,IAAI;;UAEpE,OAAOA,IAAI;QACf;OACH,CACJ;MACDiC,OAAO,EAAE;QACLzC,GAAG,EAAE,IAAI,CAACzE,eAAe,CAAC0E,iBAAiB,CAACwC,OAAO,CAACzC,GAAG;QACvDyC,OAAO,EAAE,CACL;UACIC,IAAI,EACA,oCAAoC,GACpC,IAAI,CAACpH,MAAM,CAACmC,OAAO,CAAC,iBAAiB,CAAC;UAC1CtB,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACwG,aAAa;SACnC,EACD;UACID,IAAI,EACA,oCAAoC,GACpC,IAAI,CAACpH,MAAM,CAACmC,OAAO,CAAC,MAAM,CAAC;UAC/BtB,MAAM,EAAEA,CAACyG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAE7E,MAAM,KAAI;YAC5B,MAAM8E,eAAe,GAAGF,EAAE,CAACrB,GAAG,CAAC;cAACwB,QAAQ,EAAE;YAAI,CAAC,CAAC,CAACxC,IAAI,EAAE;YACvD,IAAIuC,eAAe,EAAE;cACjB,IAAI,CAACE,MAAM,CAAC,MAAM,EAAEF,eAAe,CAAC;;UAE5C,CAAC;UACDG,MAAM,EAAE;SACX,EACD;UACIR,IAAI,EACA,qCAAqC,GACrC,IAAI,CAACpH,MAAM,CAACmC,OAAO,CAAC,QAAQ,CAAC;UACjCtB,MAAM,EAAEA,CAACyG,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAE7E,MAAM,KAAI;YAC5B,MAAM8E,eAAe,GAAGF,EAAE,CAACrB,GAAG,CAAC;cAACwB,QAAQ,EAAE;YAAI,CAAC,CAAC,CAACxC,IAAI,EAAE;YACvD,IAAIuC,eAAe,EAAE;cACjB,IAAI,CAACE,MAAM,CAAC,QAAQ,EAAEF,eAAe,CAAC;;UAE9C,CAAC;UACDG,MAAM,EAAE;SACX;OAER;MACDC,UAAU,EAAE;QACRC,OAAO,EAAE;;KAEhB;IAEDhB,CAAC,CAAC,iBAAiB,CAAC,CAACiB,EAAE,CAAC,gBAAgB,EAAE,CAACT,CAAC,EAAEU,IAAI,EAAEC,IAAI,KAAI;MACxD,IAAI,CAAC,IAAI,CAACjH,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI;QAEtB8F,CAAC,CAAC,iBAAiB,CAAC,CAACoB,GAAG,CAAC,gBAAgB,CAAC;QAC1CpB,CAAC,CAAC,iBAAiB,CAAC,CAACqB,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC;QAElDH,IAAI,CAACI,OAAO,CAAElC,GAAQ,IAAI;UACtB,MAAMmC,OAAO,GAAG,IAAI,CAACvH,QAAQ,CAACwH,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC7B,EAAE,KAAK,CAACR,GAAG,CAACsB,IAAI,CAACd,EAAE,CAAC;UAChE,IAAI2B,OAAO,EAAE;YACTA,OAAO,CAACG,KAAK,GAAGtC,GAAG,CAACuC,OAAO;;QAEnC,CAAC,CAAC;QACF,IAAI,CAAC3H,QAAQ,CAAC4H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACH,KAAK,GAAGI,CAAC,CAACJ,KAAK,CAAC;QAE/C,IAAI,CAACpI,eAAe,CAACyI,kBAAkB,CAAC,IAAI,CAAC/H,QAAQ,CAAC,CAACkE,SAAS,CAC3DE,IAAI,IAAI;UACLX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,IAAI,CAAC;UACnC4B,CAAC,CAAC,iBAAiB,CAAC,CACfgC,SAAS,EAAE,CACXlE,IAAI,CAACmE,MAAM,CAAC,MAAK;YAClBjC,CAAC,CAAC,iBAAiB,CAAC,CAACiB,EAAE,CACnB,gBAAgB,EAChB,IAAI,CAACiB,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CACpC;YACDnC,CAAC,CAAC,iBAAiB,CAAC,CAACqB,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC;YAClD,IAAI,CAACnH,UAAU,GAAG,KAAK;UAC3B,CAAC,CAAC;QACN,CAAC,EACAuC,KAAK,IAAI;UACNgB,OAAO,CAAChB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpCuD,CAAC,CAAC,iBAAiB,CAAC,CAACiB,EAAE,CACnB,gBAAgB,EAChB,IAAI,CAACiB,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CACpC;UACDnC,CAAC,CAAC,iBAAiB,CAAC,CAACqB,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC;UAClD,IAAI,CAACnH,UAAU,GAAG,KAAK;QAC3B,CAAC,CACJ;;IAET,CAAC,CAAC;EACN;EAEAgI,iBAAiBA,CAAC1B,CAAC,EAAEU,IAAI,EAAEC,IAAI;IAC3B,IAAI,CAAC,IAAI,CAACjH,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB8F,CAAC,CAAC,iBAAiB,CAAC,CAACoB,GAAG,CAAC,gBAAgB,CAAC;MAC1CpB,CAAC,CAAC,iBAAiB,CAAC,CAACqB,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC;MAElDH,IAAI,CAACI,OAAO,CAAElC,GAAQ,IAAI;QACtB,MAAMmC,OAAO,GAAG,IAAI,CAACvH,QAAQ,CAACwH,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC7B,EAAE,KAAK,CAACR,GAAG,CAACsB,IAAI,CAACd,EAAE,CAAC;QAChE,IAAI2B,OAAO,EAAE;UACTA,OAAO,CAACG,KAAK,GAAGtC,GAAG,CAACuC,OAAO;;MAEnC,CAAC,CAAC;MAEF,IAAI,CAAC3H,QAAQ,CAAC4H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACH,KAAK,GAAGI,CAAC,CAACJ,KAAK,CAAC;MAC/C,IAAI,CAACpI,eAAe,CAACyI,kBAAkB,CAAC,IAAI,CAAC/H,QAAQ,CAAC,CAACkE,SAAS,CAC3DE,IAAI,IAAI;QACL4B,CAAC,CAAC,iBAAiB,CAAC,CACfgC,SAAS,EAAE,CACXlE,IAAI,CAACmE,MAAM,CAAC,MAAK;UAClBjC,CAAC,CAAC,iBAAiB,CAAC,CAACiB,EAAE,CACnB,gBAAgB,EAChB,IAAI,CAACiB,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CACpC;UACDnC,CAAC,CAAC,iBAAiB,CAAC,CAACqB,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC;UAClD,IAAI,CAACnH,UAAU,GAAG,KAAK;QAC3B,CAAC,CAAC;MACN,CAAC,EACAuC,KAAK,IAAI;QACNuD,CAAC,CAAC,iBAAiB,CAAC,CAACiB,EAAE,CACnB,gBAAgB,EAChB,IAAI,CAACiB,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CACpC;QACDnC,CAAC,CAAC,iBAAiB,CAAC,CAACqB,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC;QAClD,IAAI,CAACnH,UAAU,GAAG,KAAK;MAC3B,CAAC,CACJ;;EAET;EAEA2G,MAAMA,CAAC9G,MAAM,EAAEqF,GAAI;IACf,IAAIrF,MAAM,IAAI,MAAM,EAAE;MAClB0D,OAAO,CAACC,GAAG,CAAC3D,MAAM,EAAEqF,GAAG,CAAC;MACxB,IAAI,CAACrF,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACI,KAAK,GAAGiF,GAAG;MAChB,IAAI,CAAC/F,mBAAmB,CAAC+I,kBAAkB,CAAC,IAAI,CAACvI,SAAS,CAAC,CAACwI,UAAU,EAAE;KAC3E,MAAM,IAAItI,MAAM,IAAI,QAAQ,EAAE;MAC3BnB,IAAI,CAAC0J,IAAI,CAAC;QACNpD,KAAK,EAAE,IAAI,CAAChG,MAAM,CAACmC,OAAO,CAAC,eAAe,CAAC;QAC3CiF,IAAI,EAAE,IAAI,CAACpH,MAAM,CAACmC,OAAO,CAAC,mCAAmC,CAAC;QAC9DkH,IAAI,EAAE,SAAS;QACfC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI,CAACvJ,MAAM,CAACmC,OAAO,CAAC,KAAK,CAAC;QAC7CqH,gBAAgB,EAAE,IAAI,CAACxJ,MAAM,CAACmC,OAAO,CAAC,IAAI;OAC7C,CAAC,CAACsH,IAAI,CAAEC,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,WAAW,EAAE;UACpB,IAAI,CAACvJ,eAAe,CAACwJ,aAAa,CAAC1D,GAAG,CAACQ,EAAE,CAAC,CAAC1B,SAAS,CAAEE,IAAI,IAAI;YAC1D,IAAI,CAAC2E,YAAY,EAAE;YACnBnK,IAAI,CAAC0J,IAAI,CAAC;cACNpD,KAAK,EAAE,IAAI,CAAChG,MAAM,CAACmC,OAAO,CAAC,UAAU,CAAC;cACtCiF,IAAI,EAAE,IAAI,CAACpH,MAAM,CAACmC,OAAO,CAAC,sBAAsB,CAAC;cACjDkH,IAAI,EAAE,SAAS;cACfC,gBAAgB,EAAE,KAAK;cACvBC,iBAAiB,EAAE,IAAI,CAACvJ,MAAM,CAACmC,OAAO,CAAC,IAAI;aAC9C,CAAC;UACN,CAAC,CAAC;;MAEV,CAAC,CAAC;;EAEV;EAEAkF,aAAaA,CAAA;IACT,IAAI,CAACxG,MAAM,GAAG,QAAQ;IACtB,IAAI,CAACV,mBAAmB,CAAC+I,kBAAkB,CAAC,IAAI,CAACvI,SAAS,CAAC,CAACwI,UAAU,EAAE;EAC5E;EAEAU,YAAYA,CAAA;IACR,IAAI,CAACrJ,SAAS,CAACsJ,UAAU,CAACL,IAAI,CAAEK,UAA0B,IAAI;MAC1DA,UAAU,CAAClF,IAAI,CAACmE,MAAM,CAAC,MAAK;QACxB;QACAgB,UAAU,CAAC,MAAK;UACZ,IAAI,CAACC,wBAAwB,EAAE;QACnC,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,EAAE,KAAK,CAAC;IACb,CAAC,CAAC;EACN;EAEAC,cAAcA,CAAA;IACV,IAAI,CAACnK,eAAe,CAAC+D,IAAI,EAAE;IAC3B,OAAO,IAAI,CAACzD,eAAe,CACtB6J,cAAc,EAAE,CAChBC,SAAS,EAAE,CACXT,IAAI,CAAEU,GAAG,IAAI;MACV,IAAI,CAACrJ,QAAQ,GAAGqJ,GAAG,CAACjF,IAAI;IAC5B,CAAC,CAAC;EACV;EAEAkF,eAAeA,CAAA;IACX,MAAMC,KAAK,GAAGvD,CAAC,CAAC,iBAAiB,CAAC,CAACgC,SAAS,EAAE;IAC9CuB,KAAK,CAACzF,IAAI,CAACmE,MAAM,CAAC,MAAK;MACnB;MACAgB,UAAU,CAAC,MAAK;QACZ,IAAI,CAACC,wBAAwB,EAAE;MACnC,CAAC,EAAE,GAAG,CAAC;IACX,CAAC,CAAC;EACN;EAEAM,gBAAgBA,CAACjC,OAAgB;IAC7B9D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE6D,OAAO,CAAC;IACrD,IAAI,CAAC+B,eAAe,EAAE;EAC1B;EAEAG,eAAeA,CAAA;IACX,IAAI,CAAC7J,SAAS,CAAC8J,IAAI,EAAE;IAErB;IACAT,UAAU,CAAC,MAAK;MACZ,IAAI,CAACvJ,SAAS,CAACsJ,UAAU,CAACL,IAAI,CAAEK,UAA0B,IAAI;QAC1D;QACAA,UAAU,CAAChE,OAAO,CAAC2E,MAAM,EAAE,CAACC,IAAI,EAAE;QAElC;QACA,IAAI,CAACV,wBAAwB,EAAE;MACnC,CAAC,CAAC;IACN,CAAC,EAAE,GAAG,CAAC;EACX;EAEQA,wBAAwBA,CAAA;IAC5B,MAAMW,YAAY,GAAGvE,QAAQ,CAACwE,cAAc,CAAC,wBAAwB,CAAC;IACtE,MAAMC,UAAU,GAAGF,YAAY,EAAEG,aAAa,CAAC,wBAAwB,CAAgB;IACvF,MAAMT,KAAK,GAAGjE,QAAQ,CAACwE,cAAc,CAAC,gBAAgB,CAAgB;IAEtE,IAAIC,UAAU,IAAIR,KAAK,EAAE;MACrB;MACAA,KAAK,CAAC7D,KAAK,CAACuE,QAAQ,GAAG,OAAO;MAC9BV,KAAK,CAAC7D,KAAK,CAAC5D,KAAK,GAAG,MAAM;MAE1B;MACAiI,UAAU,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;QACvC,MAAMC,UAAU,GAAGJ,UAAU,CAACI,UAAU;QACxC,MAAMC,SAAS,GAAGL,UAAU,CAACM,WAAW,GAAGN,UAAU,CAACO,WAAW;QAEjE;QACA,IAAIH,UAAU,GAAG,CAAC,EAAE;UAChBJ,UAAU,CAACQ,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;SAC5C,MAAM;UACHT,UAAU,CAACQ,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;;QAGhD,IAAIN,UAAU,GAAGC,SAAS,EAAE;UACxBL,UAAU,CAACQ,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;SAC7C,MAAM;UACHT,UAAU,CAACQ,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;;MAErD,CAAC,CAAC;MAEF;MACAV,UAAU,CAACW,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;;EAErD;EAEAC,WAAWA,CAAA;IACP,IAAI,CAAChL,SAAS,CAACiL,WAAW,EAAE;EAChC;EAEAC,OAAOA,CAACC,KAAK;IACTtH,OAAO,CAACC,GAAG,CAACqH,KAAK,CAAC;IAClB,IACI,CAACA,KAAK,CAACC,WAAW,IAClB,CAACnM,KAAK,CAACoM,GAAG,CAACC,IAAI,CAACH,KAAK,CAACI,SAAS,CAAC/K,IAAI,CAAC,IACrC2K,KAAK,CAACI,SAAS,CAAC/K,IAAI,EACtB;MACEqD,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,IAAI,CAACnE,UAAU,CAAC6L,WAAW,CAACL,KAAK,CAACI,SAAS,CAAC/K,IAAI,CAAC,CAAC8D,SAAS,EAAE;KAChE,MAAM;MACH,IAAI,CAACoF,eAAe,EAAE;;IAG1B;IACA;IACA;EACJ;EAAC,QAAA+B,CAAA;qBA3gBQvM,uBAAuB,EAAAwM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,KAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,kBAAA,GAAAb,EAAA,CAAAC,iBAAA,CAAAa,EAAA,CAAAC,cAAA,GAAAf,EAAA,CAAAC,iBAAA,CAAAe,EAAA,CAAAC,SAAA;EAAA;EAAA,QAAAC,EAAA;UAAvB1N,uBAAuB;IAAA2N,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAGrBpO,kBAAkB;;;;;;;;;;;;QC3BjC8M,EAAA,CAAAwB,cAAA,aAA+C;QAG3CxB,EAAA,CAAAyB,SAAA,4BAAyE;QACzEzB,EAAA,CAAAwB,cAAA,aAAkB;QASZxB,EAAA,CAAAyB,SAAA,YAAe;QACjBzB,EAAA,CAAA0B,YAAA,EAAQ;QAKhB1B,EAAA,CAAAwB,cAAA,yBAMC;QASGxB,EAAA,CAAA2B,UAAA,yBAAAC,mFAAAC,MAAA;UAAA,OAAeN,GAAA,CAAArD,gBAAA,CAAA2D,MAAA,CAAwB;QAAA,EAAC,qBAAAC,+EAAAD,MAAA;UAAA,OAC7BN,GAAA,CAAA/B,OAAA,CAAAqC,MAAA,CAAe;QAAA,EADc;QAG1C7B,EAAA,CAAA0B,YAAA,EAA6B;;;QAlCP1B,EAAA,CAAA+B,SAAA,GAA+B;QAA/B/B,EAAA,CAAAgC,UAAA,kBAAAT,GAAA,CAAA3J,aAAA,CAA+B;QAM7CoI,EAAA,CAAA+B,SAAA,GAAuB;QAAvB/B,EAAA,CAAAgC,UAAA,cAAAT,GAAA,CAAAlN,SAAA,CAAuB,cAAAkN,GAAA,CAAAjN,SAAA;QAY/B0L,EAAA,CAAA+B,SAAA,GAAoB;QAApB/B,EAAA,CAAAgC,UAAA,SAAAT,GAAA,CAAA/M,WAAA,CAAoB,OAAA+M,GAAA,CAAAhN,SAAA;QAMlByL,EAAA,CAAA+B,SAAA,GAAa;QAAb/B,EAAA,CAAAgC,UAAA,SAAAT,GAAA,CAAA5M,IAAA,CAAa,WAAA4M,GAAA,CAAAjM,MAAA,aAAAiM,GAAA,CAAAlM,OAAA,WAAAkM,GAAA,CAAA1M,KAAA,YAAA0M,GAAA,CAAA9M,MAAA,eAAA8M,GAAA,CAAAhN,SAAA,iBAAAgN,GAAA,CAAA/M,WAAA", "names": ["FormGroup", "DataTableDirective", "AppConfig", "environment", "Subject", "<PERSON><PERSON>", "REGEX", "ManageSponsorsComponent", "constructor", "_loadingService", "_titleService", "_trans", "_commonsService", "_http", "_coreSidebarService", "_sponsorService", "_s3Service", "appLogo", "Fake_Player_Photo", "dtElement", "dtOptions", "dtTrigger", "sideBarID", "sideBarName", "action", "sponsors", "form", "isUpdating", "model", "logo", "name", "Fake_Player_Name", "url", "Fake_Player_Link", "description", "Fake_Player_Description", "options", "fields", "fieldGroupClassName", "fieldGroup", "className", "type", "key", "props", "translate", "label", "instant", "required", "upload_url", "apiUrl", "dir", "accept", "maxFileSize", "useCropperDialog", "config", "width", "height", "responsiveArea", "resizableArea", "keepAspectRatio", "extraZoomOut", "autoCrop", "fill", "test_image", "validation", "messages", "error", "field", "templateOptions", "max<PERSON><PERSON><PERSON>", "placeholder", "pattern", "show", "rows", "setTitle", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "ngOnInit", "console", "log", "initTable", "dom", "dataTableDefaults", "ajax", "dataTablesParameters", "callback", "post", "subscribe", "resp", "data", "recordsTotal", "recordsFiltered", "select", "rowId", "responsive", "scrollX", "scrollCollapse", "language", "lang", "columnDefs", "targets", "columns", "sortable", "title", "render", "row", "img", "document", "createElement", "src", "alt", "style", "outerHTML", "id", "objectFit", "backgroundColor", "onerror", "$", "attr", "displayUrl", "length", "substring", "buttons", "text", "toggleSidebar", "e", "dt", "node", "selectedRowData", "selected", "editor", "extend", "<PERSON><PERSON><PERSON><PERSON>", "dataSrc", "on", "diff", "edit", "off", "css", "for<PERSON>ach", "sponsor", "find", "s", "order", "newData", "sort", "a", "b", "updateSponsorOrder", "DataTable", "reload", "rowReorderHandler", "bind", "getSidebarRegistry", "toggle<PERSON><PERSON>", "fire", "icon", "showCancelButton", "confirmButtonText", "cancelButtonText", "then", "result", "isConfirmed", "deleteSponsor", "refreshTable", "dtInstance", "setTimeout", "setupHorizontalScrolling", "getAllSponsors", "to<PERSON>romise", "res", "reloadDataTable", "table", "onSponsor<PERSON><PERSON>ed", "ngAfterViewInit", "next", "adjust", "draw", "tableWrapper", "getElementById", "scrollBody", "querySelector", "min<PERSON><PERSON><PERSON>", "addEventListener", "scrollLeft", "maxScroll", "scrollWidth", "clientWidth", "classList", "add", "remove", "dispatchEvent", "Event", "ngOnDestroy", "unsubscribe", "onClose", "event", "isSubmitted", "URL", "test", "formValue", "removeImage", "_", "i0", "ɵɵdirectiveInject", "i1", "LoadingService", "i2", "Title", "i3", "TranslateService", "i4", "CommonsService", "i5", "HttpClient", "i6", "CoreSidebarService", "i7", "SponsorService", "i8", "S3Service", "_2", "selectors", "viewQuery", "ManageSponsorsComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "ManageSponsorsComponent_Template_app_manage_sponsors_editor_emitSponsor_9_listener", "$event", "ManageSponsorsComponent_Template_app_manage_sponsors_editor_onClose_9_listener", "ɵɵadvance", "ɵɵproperty"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\manage-sponsors\\manage-sponsors.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\settings\\manage-sponsors\\manage-sponsors.component.html"], "sourcesContent": ["import {HttpClient} from '@angular/common/http';\r\nimport {AfterViewInit, Component, OnInit, ViewChild} from '@angular/core';\r\nimport {FormGroup} from '@angular/forms';\r\nimport {Title} from '@angular/platform-browser';\r\nimport {CoreSidebarService} from '@core/components/core-sidebar/core-sidebar.service';\r\nimport {FormlyFieldConfig, FormlyFormOptions} from '@ngx-formly/core';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {DataTableDirective} from 'angular-datatables';\r\nimport {AppConfig} from 'app/app-config';\r\nimport {Sponsor} from 'app/interfaces/sponsor';\r\nimport {CommonsService} from 'app/services/commons.service';\r\nimport {LoadingService} from 'app/services/loading.service';\r\nimport {S3Service} from 'app/services/s3.service';\r\nimport {SponsorService} from 'app/services/sponsor.service';\r\nimport {environment} from 'environments/environment';\r\nimport {Subject} from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport {REGEX} from '../../../helpers/regex';\r\n\r\n@Component({\r\n    selector: 'app-manage-sponsors',\r\n    templateUrl: './manage-sponsors.component.html',\r\n    styleUrls: ['./manage-sponsors.component.scss'],\r\n})\r\nexport class ManageSponsorsComponent implements OnInit, AfterViewInit {\r\n    appLogo = AppConfig.Fake_Player_Photo;\r\n    public contentHeader: object;\r\n    @ViewChild(DataTableDirective, {static: false})\r\n    dtElement: any = DataTableDirective;\r\n    dtOptions: any = {};\r\n    dtTrigger: Subject<any> = new Subject();\r\n    sideBarID: string = 'manage-sponsors';\r\n    sideBarName: string = 'manage-sponsors';\r\n    action = 'create';\r\n    sponsors: Sponsor[] = [];\r\n    form = new FormGroup({});\r\n    isUpdating: boolean = false;\r\n    model: Sponsor = {\r\n        logo: AppConfig.Fake_Player_Photo,\r\n        name: AppConfig.Fake_Player_Name,\r\n        url: AppConfig.Fake_Player_Link,\r\n        description: AppConfig.Fake_Player_Description,\r\n    };\r\n    options: FormlyFormOptions = {};\r\n    fields: FormlyFieldConfig[] = [\r\n        {\r\n            fieldGroupClassName: 'row',\r\n            fieldGroup: [\r\n                {\r\n                    className: 'col-12',\r\n                    type: 'image-cropper',\r\n                    key: 'logo',\r\n                    props: {\r\n                        translate: true,\r\n                        label: this._trans.instant('Sponsor logo'),\r\n                        required: true,\r\n                        upload_url: `${environment.apiUrl}/s3`,\r\n                        dir: 'sponsors',\r\n                        accept: 'image/png,image/jpg,image/jpeg',\r\n                        maxFileSize: 12000,\r\n                        useCropperDialog: true,\r\n                        config: {\r\n                            width: 300,\r\n                            height: 300,\r\n                            responsiveArea: true,\r\n                            resizableArea: true,\r\n                            keepAspectRatio: false,\r\n                            extraZoomOut: true,\r\n                            autoCrop: false,\r\n                            fill: '#FFFFFF',\r\n                        },\r\n                        test_image: 'assets/images/logo/logo.png',\r\n                    },\r\n                    validation: {\r\n                        messages: {\r\n                            required: (error, field: FormlyFieldConfig) => {\r\n                                return this._trans.instant('This field is required', {\r\n                                    field: this._trans.instant(field.templateOptions.label),\r\n                                });\r\n                            },\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    className: 'col-12',\r\n                    type: 'input',\r\n                    key: 'name',\r\n                    templateOptions: {\r\n                        required: true,\r\n                        label: this._trans.instant('Sponsor name'),\r\n                        maxLength: 50,\r\n                        placeholder: this._trans.instant('Type sponsor name'),\r\n                        pattern: /[\\S]/,\r\n                    },\r\n                    validation: {\r\n                        messages: {\r\n                            required: (error, field: FormlyFieldConfig) => {\r\n                                return this._trans.instant('This field is required', {\r\n                                    field: this._trans.instant(field.templateOptions.label),\r\n                                });\r\n                            },\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    className: 'col-12',\r\n                    type: 'input',\r\n                    key: 'url',\r\n                    templateOptions: {\r\n                        label: this._trans.instant('Sponsor URL'),\r\n                        maxLength: 250,\r\n                        placeholder: this._trans.instant('Type sponsor URL'),\r\n                        pattern:\r\n                            /^(https?:\\/\\/)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/,\r\n                    },\r\n                    validation: {\r\n                        show: true,\r\n                        messages: {\r\n                            pattern: this._trans.instant(\r\n                                'Invalid URL, example: https://example.com'\r\n                            ),\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    className: 'col-12',\r\n                    type: 'textarea',\r\n                    key: 'description',\r\n                    templateOptions: {\r\n                        label: this._trans.instant('Description'),\r\n                        placeholder: this._trans.instant('Type description'),\r\n                        maxLength: 250,\r\n                        rows: 3,\r\n                    },\r\n                },\r\n            ],\r\n        },\r\n    ];\r\n\r\n    constructor(\r\n        public _loadingService: LoadingService,\r\n        public _titleService: Title,\r\n        public _trans: TranslateService,\r\n        public _commonsService: CommonsService,\r\n        private _http: HttpClient,\r\n        private _coreSidebarService: CoreSidebarService,\r\n        private _sponsorService: SponsorService,\r\n        private _s3Service: S3Service\r\n    ) {\r\n        this._titleService.setTitle('Manage Sponsors');\r\n        this.contentHeader = {\r\n            headerTitle: this._trans.instant('Manage Sponsors'),\r\n            actionButton: false,\r\n            breadcrumb: {\r\n                type: '',\r\n                links: [\r\n                    {\r\n                        name: 'Settings',\r\n                        isLink: false,\r\n                    },\r\n                    {\r\n                        name: 'Manage Sponsors',\r\n                        isLink: false,\r\n                    },\r\n                ],\r\n            },\r\n        };\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        console.log('Sponsors');\r\n        this.initTable();\r\n    }\r\n\r\n    initTable() {\r\n        this.dtOptions = {\r\n            dom: this._commonsService.dataTableDefaults.dom,\r\n            ajax: (dataTablesParameters: any, callback) => {\r\n                this._http\r\n                    .post<any>(`${environment.apiUrl}/sponsors/all`, dataTablesParameters)\r\n                    .subscribe((resp: any) => {\r\n                        this.sponsors = resp.data;\r\n                        callback({\r\n                            recordsTotal: resp.recordsTotal,\r\n                            recordsFiltered: resp.recordsFiltered,\r\n                            data: resp.data,\r\n                        });\r\n                    });\r\n            },\r\n            select: 'single',\r\n            rowId: 'id',\r\n            // Disable responsive to enable proper horizontal scrolling\r\n            responsive: false,\r\n            scrollX: true,\r\n            scrollCollapse: true,\r\n            language: this._commonsService.dataTableDefaults.lang,\r\n            columnDefs: [\r\n                {targets: 0, className: 'text-center'},\r\n                {targets: 1, className: 'text-center'},\r\n                {targets: 2},\r\n                {targets: 3},\r\n                {targets: 4},\r\n            ],\r\n            columns: [\r\n                {\r\n                    sortable: false,\r\n                    data: 'order',\r\n                    className: 'reorder text-center',\r\n                    title: 'Order',\r\n                    width: '60px',\r\n                    render: function (data: any, type: any, row: any) {\r\n                        const img = document.createElement('img');\r\n                        img.src = 'assets/images/icons/Group 1.png';\r\n                        img.alt = 'Order Icon';\r\n                        img.style.width = '10px';\r\n                        img.style.height = 'auto';\r\n\r\n                        return img.outerHTML;\r\n                    },\r\n                },\r\n                {\r\n                    data: 'logo',\r\n                    title: 'SPONSOR LOGO',\r\n                    className: 'text-center',\r\n                    width: '80px',\r\n                    render: function (data: any, type: any, row: any) {\r\n                        //create image\r\n                        let img = document.createElement('img');\r\n                        img.src = data;\r\n                        img.id = `img-${row.id}`;\r\n                        img.style.width = '50px';\r\n                        img.style.height = 'auto';\r\n                        img.style.objectFit = 'cover';\r\n                        img.style.backgroundColor = '#fff';\r\n                        img.style.objectFit = 'cover';\r\n                        if (data == null) {\r\n                            img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n                        }\r\n                        // check get image error\r\n                        img.onerror = function () {\r\n                            img.src = 'assets/images/logo/ezactive_1024x1024.png';\r\n                            // set src by row id\r\n                            $(`#img-${row.id}`).attr('src', img.src);\r\n                        };\r\n                        return type === 'print' ? 'print' : img.outerHTML;\r\n                    },\r\n                },\r\n                {\r\n                    data: 'name',\r\n                    title: 'SPONSOR NAME',\r\n                    width: '200px',\r\n                    render: function (data: any, type: any, row: any) {\r\n                        if (type === 'display' && data !== null) {\r\n                            // Create a span with proper styling for long names\r\n                            return `<span class=\"sponsor-name\" title=\"${data}\">${data}</span>`;\r\n                        }\r\n                        return data;\r\n                    },\r\n                },\r\n                {\r\n                    data: 'url',\r\n                    title: 'SPONSOR URL',\r\n                    width: '250px',\r\n                    render: function (data: any, type: any, row: any) {\r\n                        if (type === 'display' && data !== null && data !== '') {\r\n                            // Create a clickable link with proper styling for long URLs\r\n                            const displayUrl = data.length > 40 ? data.substring(0, 37) + '...' : data;\r\n                            return `<a href=\"${data}\" target=\"_blank\" class=\"sponsor-url\" title=\"${data}\">${displayUrl}</a>`;\r\n                        }\r\n                        return data || '';\r\n                    },\r\n                },\r\n                {\r\n                    data: 'description',\r\n                    title: 'DESCRIPTION',\r\n                    width: '300px',\r\n                    render: function (data: any, type: any, row: any) {\r\n                        if (type === 'display' && data !== null) {\r\n                            return data.length > 120 ? data.substring(0, 120) + '...' : data;\r\n                        }\r\n                        return data;\r\n                    },\r\n                },\r\n            ],\r\n            buttons: {\r\n                dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n                buttons: [\r\n                    {\r\n                        text:\r\n                            '<i class=\"feather icon-plus\"></i> ' +\r\n                            this._trans.instant('Add New Sponsor'),\r\n                        action: () => this.toggleSidebar(),\r\n                    },\r\n                    {\r\n                        text:\r\n                            '<i class=\"feather icon-edit\"></i> ' +\r\n                            this._trans.instant('Edit'),\r\n                        action: (e, dt, node, config) => {\r\n                            const selectedRowData = dt.row({selected: true}).data();\r\n                            if (selectedRowData) {\r\n                                this.editor('edit', selectedRowData);\r\n                            }\r\n                        },\r\n                        extend: 'selected',\r\n                    },\r\n                    {\r\n                        text:\r\n                            '<i class=\"feather icon-trash\"></i> ' +\r\n                            this._trans.instant('Delete'),\r\n                        action: (e, dt, node, config) => {\r\n                            const selectedRowData = dt.row({selected: true}).data();\r\n                            if (selectedRowData) {\r\n                                this.editor('remove', selectedRowData);\r\n                            }\r\n                        },\r\n                        extend: 'selected',\r\n                    },\r\n                ],\r\n            },\r\n            rowReorder: {\r\n                dataSrc: 'order',\r\n            },\r\n        };\r\n\r\n        $('#ManageSponsors').on('row-reorder.dt', (e, diff, edit) => {\r\n            if (!this.isUpdating) {\r\n                this.isUpdating = true;\r\n\r\n                $('#ManageSponsors').off('row-reorder.dt');\r\n                $('#ManageSponsors').css('pointer-events', 'none');\r\n\r\n                diff.forEach((row: any) => {\r\n                    const sponsor = this.sponsors.find((s) => s.id === +row.node.id);\r\n                    if (sponsor) {\r\n                        sponsor.order = row.newData;\r\n                    }\r\n                });\r\n                this.sponsors.sort((a, b) => a.order - b.order);\r\n\r\n                this._sponsorService.updateSponsorOrder(this.sponsors).subscribe(\r\n                    (data) => {\r\n                        console.log('Update success', data);\r\n                        $('#ManageSponsors')\r\n                            .DataTable()\r\n                            .ajax.reload(() => {\r\n                            $('#ManageSponsors').on(\r\n                                'row-reorder.dt',\r\n                                this.rowReorderHandler.bind(this)\r\n                            );\r\n                            $('#ManageSponsors').css('pointer-events', 'auto');\r\n                            this.isUpdating = false;\r\n                        });\r\n                    },\r\n                    (error) => {\r\n                        console.error('Update error', error);\r\n                        $('#ManageSponsors').on(\r\n                            'row-reorder.dt',\r\n                            this.rowReorderHandler.bind(this)\r\n                        );\r\n                        $('#ManageSponsors').css('pointer-events', 'auto');\r\n                        this.isUpdating = false;\r\n                    }\r\n                );\r\n            }\r\n        });\r\n    }\r\n\r\n    rowReorderHandler(e, diff, edit) {\r\n        if (!this.isUpdating) {\r\n            this.isUpdating = true;\r\n            $('#ManageSponsors').off('row-reorder.dt');\r\n            $('#ManageSponsors').css('pointer-events', 'none');\r\n\r\n            diff.forEach((row: any) => {\r\n                const sponsor = this.sponsors.find((s) => s.id === +row.node.id);\r\n                if (sponsor) {\r\n                    sponsor.order = row.newData;\r\n                }\r\n            });\r\n\r\n            this.sponsors.sort((a, b) => a.order - b.order);\r\n            this._sponsorService.updateSponsorOrder(this.sponsors).subscribe(\r\n                (data) => {\r\n                    $('#ManageSponsors')\r\n                        .DataTable()\r\n                        .ajax.reload(() => {\r\n                        $('#ManageSponsors').on(\r\n                            'row-reorder.dt',\r\n                            this.rowReorderHandler.bind(this)\r\n                        );\r\n                        $('#ManageSponsors').css('pointer-events', 'auto');\r\n                        this.isUpdating = false;\r\n                    });\r\n                },\r\n                (error) => {\r\n                    $('#ManageSponsors').on(\r\n                        'row-reorder.dt',\r\n                        this.rowReorderHandler.bind(this)\r\n                    );\r\n                    $('#ManageSponsors').css('pointer-events', 'auto');\r\n                    this.isUpdating = false;\r\n                }\r\n            );\r\n        }\r\n    }\r\n\r\n    editor(action, row?) {\r\n        if (action == 'edit') {\r\n            console.log(action, row);\r\n            this.action = action;\r\n            this.model = row;\r\n            this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen();\r\n        } else if (action == 'remove') {\r\n            Swal.fire({\r\n                title: this._trans.instant('Are you sure?'),\r\n                text: this._trans.instant(\"You won't be able to revert this!\"),\r\n                icon: 'warning',\r\n                showCancelButton: true,\r\n                confirmButtonText: this._trans.instant('Yes'),\r\n                cancelButtonText: this._trans.instant('No'),\r\n            }).then((result) => {\r\n                if (result.isConfirmed) {\r\n                    this._sponsorService.deleteSponsor(row.id).subscribe((data) => {\r\n                        this.refreshTable();\r\n                        Swal.fire({\r\n                            title: this._trans.instant('Deleted!'),\r\n                            text: this._trans.instant('Deleted successfully'),\r\n                            icon: 'success',\r\n                            showCancelButton: false,\r\n                            confirmButtonText: this._trans.instant('OK'),\r\n                        });\r\n                    });\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    toggleSidebar(): void {\r\n        this.action = 'create';\r\n        this._coreSidebarService.getSidebarRegistry(this.sideBarID).toggleOpen();\r\n    }\r\n\r\n    refreshTable() {\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload(() => {\r\n                // Re-setup horizontal scrolling after reload\r\n                setTimeout(() => {\r\n                    this.setupHorizontalScrolling();\r\n                }, 100);\r\n            }, false);\r\n        });\r\n    }\r\n\r\n    getAllSponsors() {\r\n        this._loadingService.show();\r\n        return this._sponsorService\r\n            .getAllSponsors()\r\n            .toPromise()\r\n            .then((res) => {\r\n                this.sponsors = res.data;\r\n            });\r\n    }\r\n\r\n    reloadDataTable() {\r\n        const table = $('#ManageSponsors').DataTable();\r\n        table.ajax.reload(() => {\r\n            // Re-setup horizontal scrolling after reload\r\n            setTimeout(() => {\r\n                this.setupHorizontalScrolling();\r\n            }, 100);\r\n        });\r\n    }\r\n\r\n    onSponsorChanged(sponsor: Sponsor) {\r\n        console.log('Sponsor data has been changed', sponsor);\r\n        this.reloadDataTable();\r\n    }\r\n\r\n    ngAfterViewInit(): void {\r\n        this.dtTrigger.next();\r\n\r\n        // Ensure proper horizontal scrolling setup after table initialization\r\n        setTimeout(() => {\r\n            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n                // Adjust columns and redraw\r\n                dtInstance.columns.adjust().draw();\r\n\r\n                // Force horizontal scrolling setup\r\n                this.setupHorizontalScrolling();\r\n            });\r\n        }, 200);\r\n    }\r\n\r\n    private setupHorizontalScrolling(): void {\r\n        const tableWrapper = document.getElementById('ManageSponsors_wrapper');\r\n        const scrollBody = tableWrapper?.querySelector('.dataTables_scrollBody') as HTMLElement;\r\n        const table = document.getElementById('ManageSponsors') as HTMLElement;\r\n\r\n        if (scrollBody && table) {\r\n            // Ensure minimum width is applied\r\n            table.style.minWidth = '890px';\r\n            table.style.width = 'auto';\r\n\r\n            // Add scroll event listener for visual feedback\r\n            scrollBody.addEventListener('scroll', () => {\r\n                const scrollLeft = scrollBody.scrollLeft;\r\n                const maxScroll = scrollBody.scrollWidth - scrollBody.clientWidth;\r\n\r\n                // Add/remove classes based on scroll position for visual indicators\r\n                if (scrollLeft > 0) {\r\n                    scrollBody.classList.add('scrolled-left');\r\n                } else {\r\n                    scrollBody.classList.remove('scrolled-left');\r\n                }\r\n\r\n                if (scrollLeft < maxScroll) {\r\n                    scrollBody.classList.add('scrolled-right');\r\n                } else {\r\n                    scrollBody.classList.remove('scrolled-right');\r\n                }\r\n            });\r\n\r\n            // Trigger initial scroll event to set up indicators\r\n            scrollBody.dispatchEvent(new Event('scroll'));\r\n        }\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        this.dtTrigger.unsubscribe();\r\n    }\r\n\r\n    onClose(event) {\r\n        console.log(event);\r\n        if (\r\n            !event.isSubmitted &&\r\n            !REGEX.URL.test(event.formValue.logo) &&\r\n            event.formValue.logo\r\n        ) {\r\n            console.log('Remove image');\r\n            this._s3Service.removeImage(event.formValue.logo).subscribe();\r\n        } else {\r\n            this.reloadDataTable();\r\n        }\r\n\r\n        // if (event.isSubmitted) {\r\n        //   this.reloadDataTable();\r\n        // }\r\n    }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div class=\"card\">\r\n      <div class=\"mt-1 table-responsive-container sponsors-table-container\">\r\n        <table\r\n          id=\"ManageSponsors\"\r\n          datatable\r\n          [dtOptions]=\"dtOptions\"\r\n          [dtTrigger]=\"dtTrigger\"\r\n          class=\"table row-border hover table-white-space\"\r\n        >\r\n          <thead></thead>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n<core-sidebar\r\n  class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n  [name]=\"sideBarName\"\r\n  [id]=\"sideBarID\"\r\n  overlayClass=\"modal-backdrop\"\r\n  #coreSidebar\r\n>\r\n  <app-manage-sponsors-editor\r\n    [form]=\"form\"\r\n    [fields]=\"fields\"\r\n    [options]=\"options\"\r\n    [model]=\"model\"\r\n    [action]=\"action\"\r\n    [sideBarID]=\"sideBarID\"\r\n    [sideBarName]=\"sideBarName\"\r\n    (emitSponsor)=\"onSponsorChanged($event)\"\r\n    (onClose)=\"onClose($event)\"\r\n  >\r\n  </app-manage-sponsors-editor>\r\n</core-sidebar>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin, buildRequestInit } from '@capacitor/core';\nimport { Encoding } from './definitions';\nfunction resolve(path) {\n  const posix = path.split('/').filter(item => item !== '.');\n  const newPosix = [];\n  posix.forEach(item => {\n    if (item === '..' && newPosix.length > 0 && newPosix[newPosix.length - 1] !== '..') {\n      newPosix.pop();\n    } else {\n      newPosix.push(item);\n    }\n  });\n  return newPosix.join('/');\n}\nfunction isPathParent(parent, children) {\n  parent = resolve(parent);\n  children = resolve(children);\n  const pathsA = parent.split('/');\n  const pathsB = children.split('/');\n  return parent !== children && pathsA.every((value, index) => value === pathsB[index]);\n}\nexport class FilesystemWeb extends WebPlugin {\n  constructor() {\n    var _this;\n    super(...arguments);\n    _this = this;\n    this.DB_VERSION = 1;\n    this.DB_NAME = 'Disc';\n    this._writeCmds = ['add', 'put', 'delete'];\n    /**\n     * Function that performs a http request to a server and downloads the file to the specified destination\n     *\n     * @param options the options for the download operation\n     * @returns a promise that resolves with the download file result\n     */\n    this.downloadFile = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (options) {\n        var _a, _b;\n        const requestInit = buildRequestInit(options, options.webFetchExtra);\n        const response = yield fetch(options.url, requestInit);\n        let blob;\n        if (!options.progress) blob = yield response.blob();else if (!(response === null || response === void 0 ? void 0 : response.body)) blob = new Blob();else {\n          const reader = response.body.getReader();\n          let bytes = 0;\n          const chunks = [];\n          const contentType = response.headers.get('content-type');\n          const contentLength = parseInt(response.headers.get('content-length') || '0', 10);\n          while (true) {\n            const {\n              done,\n              value\n            } = yield reader.read();\n            if (done) break;\n            chunks.push(value);\n            bytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n            const status = {\n              url: options.url,\n              bytes,\n              contentLength\n            };\n            _this.notifyListeners('progress', status);\n          }\n          const allChunks = new Uint8Array(bytes);\n          let position = 0;\n          for (const chunk of chunks) {\n            if (typeof chunk === 'undefined') continue;\n            allChunks.set(chunk, position);\n            position += chunk.length;\n          }\n          blob = new Blob([allChunks.buffer], {\n            type: contentType || undefined\n          });\n        }\n        const result = yield _this.writeFile({\n          path: options.path,\n          directory: (_a = options.directory) !== null && _a !== void 0 ? _a : undefined,\n          recursive: (_b = options.recursive) !== null && _b !== void 0 ? _b : false,\n          data: blob\n        });\n        return {\n          path: result.uri,\n          blob\n        };\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n  }\n  initDb() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2._db !== undefined) {\n        return _this2._db;\n      }\n      if (!('indexedDB' in window)) {\n        throw _this2.unavailable(\"This browser doesn't support IndexedDB\");\n      }\n      return new Promise((resolve, reject) => {\n        const request = indexedDB.open(_this2.DB_NAME, _this2.DB_VERSION);\n        request.onupgradeneeded = FilesystemWeb.doUpgrade;\n        request.onsuccess = () => {\n          _this2._db = request.result;\n          resolve(request.result);\n        };\n        request.onerror = () => reject(request.error);\n        request.onblocked = () => {\n          console.warn('db blocked');\n        };\n      });\n    })();\n  }\n  static doUpgrade(event) {\n    const eventTarget = event.target;\n    const db = eventTarget.result;\n    switch (event.oldVersion) {\n      case 0:\n      case 1:\n      default:\n        {\n          if (db.objectStoreNames.contains('FileStorage')) {\n            db.deleteObjectStore('FileStorage');\n          }\n          const store = db.createObjectStore('FileStorage', {\n            keyPath: 'path'\n          });\n          store.createIndex('by_folder', 'folder');\n        }\n    }\n  }\n  dbRequest(cmd, args) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const readFlag = _this3._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n      return _this3.initDb().then(conn => {\n        return new Promise((resolve, reject) => {\n          const tx = conn.transaction(['FileStorage'], readFlag);\n          const store = tx.objectStore('FileStorage');\n          const req = store[cmd](...args);\n          req.onsuccess = () => resolve(req.result);\n          req.onerror = () => reject(req.error);\n        });\n      });\n    })();\n  }\n  dbIndexRequest(indexName, cmd, args) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const readFlag = _this4._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n      return _this4.initDb().then(conn => {\n        return new Promise((resolve, reject) => {\n          const tx = conn.transaction(['FileStorage'], readFlag);\n          const store = tx.objectStore('FileStorage');\n          const index = store.index(indexName);\n          const req = index[cmd](...args);\n          req.onsuccess = () => resolve(req.result);\n          req.onerror = () => reject(req.error);\n        });\n      });\n    })();\n  }\n  getPath(directory, uriPath) {\n    const cleanedUriPath = uriPath !== undefined ? uriPath.replace(/^[/]+|[/]+$/g, '') : '';\n    let fsPath = '';\n    if (directory !== undefined) fsPath += '/' + directory;\n    if (uriPath !== '') fsPath += '/' + cleanedUriPath;\n    return fsPath;\n  }\n  clear() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const conn = yield _this5.initDb();\n      const tx = conn.transaction(['FileStorage'], 'readwrite');\n      const store = tx.objectStore('FileStorage');\n      store.clear();\n    })();\n  }\n  /**\n   * Read a file from disk\n   * @param options options for the file read\n   * @return a promise that resolves with the read file data result\n   */\n  readFile(options) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this6.getPath(options.directory, options.path);\n      // const encoding = options.encoding;\n      const entry = yield _this6.dbRequest('get', [path]);\n      if (entry === undefined) throw Error('File does not exist.');\n      return {\n        data: entry.content ? entry.content : ''\n      };\n    })();\n  }\n  /**\n   * Write a file to disk in the specified location on device\n   * @param options options for the file write\n   * @return a promise that resolves with the file write result\n   */\n  writeFile(options) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this7.getPath(options.directory, options.path);\n      let data = options.data;\n      const encoding = options.encoding;\n      const doRecursive = options.recursive;\n      const occupiedEntry = yield _this7.dbRequest('get', [path]);\n      if (occupiedEntry && occupiedEntry.type === 'directory') throw Error('The supplied path is a directory.');\n      const parentPath = path.substr(0, path.lastIndexOf('/'));\n      const parentEntry = yield _this7.dbRequest('get', [parentPath]);\n      if (parentEntry === undefined) {\n        const subDirIndex = parentPath.indexOf('/', 1);\n        if (subDirIndex !== -1) {\n          const parentArgPath = parentPath.substr(subDirIndex);\n          yield _this7.mkdir({\n            path: parentArgPath,\n            directory: options.directory,\n            recursive: doRecursive\n          });\n        }\n      }\n      if (!encoding && !(data instanceof Blob)) {\n        data = data.indexOf(',') >= 0 ? data.split(',')[1] : data;\n        if (!_this7.isBase64String(data)) throw Error('The supplied data is not valid base64 content.');\n      }\n      const now = Date.now();\n      const pathObj = {\n        path: path,\n        folder: parentPath,\n        type: 'file',\n        size: data instanceof Blob ? data.size : data.length,\n        ctime: now,\n        mtime: now,\n        content: data\n      };\n      yield _this7.dbRequest('put', [pathObj]);\n      return {\n        uri: pathObj.path\n      };\n    })();\n  }\n  /**\n   * Append to a file on disk in the specified location on device\n   * @param options options for the file append\n   * @return a promise that resolves with the file write result\n   */\n  appendFile(options) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this8.getPath(options.directory, options.path);\n      let data = options.data;\n      const encoding = options.encoding;\n      const parentPath = path.substr(0, path.lastIndexOf('/'));\n      const now = Date.now();\n      let ctime = now;\n      const occupiedEntry = yield _this8.dbRequest('get', [path]);\n      if (occupiedEntry && occupiedEntry.type === 'directory') throw Error('The supplied path is a directory.');\n      const parentEntry = yield _this8.dbRequest('get', [parentPath]);\n      if (parentEntry === undefined) {\n        const subDirIndex = parentPath.indexOf('/', 1);\n        if (subDirIndex !== -1) {\n          const parentArgPath = parentPath.substr(subDirIndex);\n          yield _this8.mkdir({\n            path: parentArgPath,\n            directory: options.directory,\n            recursive: true\n          });\n        }\n      }\n      if (!encoding && !_this8.isBase64String(data)) throw Error('The supplied data is not valid base64 content.');\n      if (occupiedEntry !== undefined) {\n        if (occupiedEntry.content instanceof Blob) {\n          throw Error('The occupied entry contains a Blob object which cannot be appended to.');\n        }\n        if (occupiedEntry.content !== undefined && !encoding) {\n          data = btoa(atob(occupiedEntry.content) + atob(data));\n        } else {\n          data = occupiedEntry.content + data;\n        }\n        ctime = occupiedEntry.ctime;\n      }\n      const pathObj = {\n        path: path,\n        folder: parentPath,\n        type: 'file',\n        size: data.length,\n        ctime: ctime,\n        mtime: now,\n        content: data\n      };\n      yield _this8.dbRequest('put', [pathObj]);\n    })();\n  }\n  /**\n   * Delete a file from disk\n   * @param options options for the file delete\n   * @return a promise that resolves with the deleted file data result\n   */\n  deleteFile(options) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this9.getPath(options.directory, options.path);\n      const entry = yield _this9.dbRequest('get', [path]);\n      if (entry === undefined) throw Error('File does not exist.');\n      const entries = yield _this9.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n      if (entries.length !== 0) throw Error('Folder is not empty.');\n      yield _this9.dbRequest('delete', [path]);\n    })();\n  }\n  /**\n   * Create a directory.\n   * @param options options for the mkdir\n   * @return a promise that resolves with the mkdir result\n   */\n  mkdir(options) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this10.getPath(options.directory, options.path);\n      const doRecursive = options.recursive;\n      const parentPath = path.substr(0, path.lastIndexOf('/'));\n      const depth = (path.match(/\\//g) || []).length;\n      const parentEntry = yield _this10.dbRequest('get', [parentPath]);\n      const occupiedEntry = yield _this10.dbRequest('get', [path]);\n      if (depth === 1) throw Error('Cannot create Root directory');\n      if (occupiedEntry !== undefined) throw Error('Current directory does already exist.');\n      if (!doRecursive && depth !== 2 && parentEntry === undefined) throw Error('Parent directory must exist');\n      if (doRecursive && depth !== 2 && parentEntry === undefined) {\n        const parentArgPath = parentPath.substr(parentPath.indexOf('/', 1));\n        yield _this10.mkdir({\n          path: parentArgPath,\n          directory: options.directory,\n          recursive: doRecursive\n        });\n      }\n      const now = Date.now();\n      const pathObj = {\n        path: path,\n        folder: parentPath,\n        type: 'directory',\n        size: 0,\n        ctime: now,\n        mtime: now\n      };\n      yield _this10.dbRequest('put', [pathObj]);\n    })();\n  }\n  /**\n   * Remove a directory\n   * @param options the options for the directory remove\n   */\n  rmdir(options) {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        path,\n        directory,\n        recursive\n      } = options;\n      const fullPath = _this11.getPath(directory, path);\n      const entry = yield _this11.dbRequest('get', [fullPath]);\n      if (entry === undefined) throw Error('Folder does not exist.');\n      if (entry.type !== 'directory') throw Error('Requested path is not a directory');\n      const readDirResult = yield _this11.readdir({\n        path,\n        directory\n      });\n      if (readDirResult.files.length !== 0 && !recursive) throw Error('Folder is not empty');\n      for (const entry of readDirResult.files) {\n        const entryPath = `${path}/${entry.name}`;\n        const entryObj = yield _this11.stat({\n          path: entryPath,\n          directory\n        });\n        if (entryObj.type === 'file') {\n          yield _this11.deleteFile({\n            path: entryPath,\n            directory\n          });\n        } else {\n          yield _this11.rmdir({\n            path: entryPath,\n            directory,\n            recursive\n          });\n        }\n      }\n      yield _this11.dbRequest('delete', [fullPath]);\n    })();\n  }\n  /**\n   * Return a list of files from the directory (not recursive)\n   * @param options the options for the readdir operation\n   * @return a promise that resolves with the readdir directory listing result\n   */\n  readdir(options) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this12.getPath(options.directory, options.path);\n      const entry = yield _this12.dbRequest('get', [path]);\n      if (options.path !== '' && entry === undefined) throw Error('Folder does not exist.');\n      const entries = yield _this12.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n      const files = yield Promise.all(entries.map( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (e) {\n          let subEntry = yield _this12.dbRequest('get', [e]);\n          if (subEntry === undefined) {\n            subEntry = yield _this12.dbRequest('get', [e + '/']);\n          }\n          return {\n            name: e.substring(path.length + 1),\n            type: subEntry.type,\n            size: subEntry.size,\n            ctime: subEntry.ctime,\n            mtime: subEntry.mtime,\n            uri: subEntry.path\n          };\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()));\n      return {\n        files: files\n      };\n    })();\n  }\n  /**\n   * Return full File URI for a path and directory\n   * @param options the options for the stat operation\n   * @return a promise that resolves with the file stat result\n   */\n  getUri(options) {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this13.getPath(options.directory, options.path);\n      let entry = yield _this13.dbRequest('get', [path]);\n      if (entry === undefined) {\n        entry = yield _this13.dbRequest('get', [path + '/']);\n      }\n      return {\n        uri: (entry === null || entry === void 0 ? void 0 : entry.path) || path\n      };\n    })();\n  }\n  /**\n   * Return data about a file\n   * @param options the options for the stat operation\n   * @return a promise that resolves with the file stat result\n   */\n  stat(options) {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      const path = _this14.getPath(options.directory, options.path);\n      let entry = yield _this14.dbRequest('get', [path]);\n      if (entry === undefined) {\n        entry = yield _this14.dbRequest('get', [path + '/']);\n      }\n      if (entry === undefined) throw Error('Entry does not exist.');\n      return {\n        type: entry.type,\n        size: entry.size,\n        ctime: entry.ctime,\n        mtime: entry.mtime,\n        uri: entry.path\n      };\n    })();\n  }\n  /**\n   * Rename a file or directory\n   * @param options the options for the rename operation\n   * @return a promise that resolves with the rename result\n   */\n  rename(options) {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      yield _this15._copy(options, true);\n      return;\n    })();\n  }\n  /**\n   * Copy a file or directory\n   * @param options the options for the copy operation\n   * @return a promise that resolves with the copy result\n   */\n  copy(options) {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      return _this16._copy(options, false);\n    })();\n  }\n  requestPermissions() {\n    return _asyncToGenerator(function* () {\n      return {\n        publicStorage: 'granted'\n      };\n    })();\n  }\n  checkPermissions() {\n    return _asyncToGenerator(function* () {\n      return {\n        publicStorage: 'granted'\n      };\n    })();\n  }\n  /**\n   * Function that can perform a copy or a rename\n   * @param options the options for the rename operation\n   * @param doRename whether to perform a rename or copy operation\n   * @return a promise that resolves with the result\n   */\n  _copy(options, doRename = false) {\n    var _this17 = this;\n    return _asyncToGenerator(function* () {\n      let {\n        toDirectory\n      } = options;\n      const {\n        to,\n        from,\n        directory: fromDirectory\n      } = options;\n      if (!to || !from) {\n        throw Error('Both to and from must be provided');\n      }\n      // If no \"to\" directory is provided, use the \"from\" directory\n      if (!toDirectory) {\n        toDirectory = fromDirectory;\n      }\n      const fromPath = _this17.getPath(fromDirectory, from);\n      const toPath = _this17.getPath(toDirectory, to);\n      // Test that the \"to\" and \"from\" locations are different\n      if (fromPath === toPath) {\n        return {\n          uri: toPath\n        };\n      }\n      if (isPathParent(fromPath, toPath)) {\n        throw Error('To path cannot contain the from path');\n      }\n      // Check the state of the \"to\" location\n      let toObj;\n      try {\n        toObj = yield _this17.stat({\n          path: to,\n          directory: toDirectory\n        });\n      } catch (e) {\n        // To location does not exist, ensure the directory containing \"to\" location exists and is a directory\n        const toPathComponents = to.split('/');\n        toPathComponents.pop();\n        const toPath = toPathComponents.join('/');\n        // Check the containing directory of the \"to\" location exists\n        if (toPathComponents.length > 0) {\n          const toParentDirectory = yield _this17.stat({\n            path: toPath,\n            directory: toDirectory\n          });\n          if (toParentDirectory.type !== 'directory') {\n            throw new Error('Parent directory of the to path is a file');\n          }\n        }\n      }\n      // Cannot overwrite a directory\n      if (toObj && toObj.type === 'directory') {\n        throw new Error('Cannot overwrite a directory with a file');\n      }\n      // Ensure the \"from\" object exists\n      const fromObj = yield _this17.stat({\n        path: from,\n        directory: fromDirectory\n      });\n      // Set the mtime/ctime of the supplied path\n      const updateTime = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (path, ctime, mtime) {\n          const fullPath = _this17.getPath(toDirectory, path);\n          const entry = yield _this17.dbRequest('get', [fullPath]);\n          entry.ctime = ctime;\n          entry.mtime = mtime;\n          yield _this17.dbRequest('put', [entry]);\n        });\n        return function updateTime(_x3, _x4, _x5) {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      const ctime = fromObj.ctime ? fromObj.ctime : Date.now();\n      switch (fromObj.type) {\n        // The \"from\" object is a file\n        case 'file':\n          {\n            // Read the file\n            const file = yield _this17.readFile({\n              path: from,\n              directory: fromDirectory\n            });\n            // Optionally remove the file\n            if (doRename) {\n              yield _this17.deleteFile({\n                path: from,\n                directory: fromDirectory\n              });\n            }\n            let encoding;\n            if (!(file.data instanceof Blob) && !_this17.isBase64String(file.data)) {\n              encoding = Encoding.UTF8;\n            }\n            // Write the file to the new location\n            const writeResult = yield _this17.writeFile({\n              path: to,\n              directory: toDirectory,\n              data: file.data,\n              encoding: encoding\n            });\n            // Copy the mtime/ctime of a renamed file\n            if (doRename) {\n              yield updateTime(to, ctime, fromObj.mtime);\n            }\n            // Resolve promise\n            return writeResult;\n          }\n        case 'directory':\n          {\n            if (toObj) {\n              throw Error('Cannot move a directory over an existing object');\n            }\n            try {\n              // Create the to directory\n              yield _this17.mkdir({\n                path: to,\n                directory: toDirectory,\n                recursive: false\n              });\n              // Copy the mtime/ctime of a renamed directory\n              if (doRename) {\n                yield updateTime(to, ctime, fromObj.mtime);\n              }\n            } catch (e) {\n              // ignore\n            }\n            // Iterate over the contents of the from location\n            const contents = (yield _this17.readdir({\n              path: from,\n              directory: fromDirectory\n            })).files;\n            for (const filename of contents) {\n              // Move item from the from directory to the to directory\n              yield _this17._copy({\n                from: `${from}/${filename.name}`,\n                to: `${to}/${filename.name}`,\n                directory: fromDirectory,\n                toDirectory\n              }, doRename);\n            }\n            // Optionally remove the original from directory\n            if (doRename) {\n              yield _this17.rmdir({\n                path: from,\n                directory: fromDirectory\n              });\n            }\n          }\n      }\n      return {\n        uri: toPath\n      };\n    })();\n  }\n  isBase64String(str) {\n    try {\n      return btoa(atob(str)) == str;\n    } catch (err) {\n      return false;\n    }\n  }\n}\nFilesystemWeb._debug = true;", "map": {"version": 3, "names": ["WebPlugin", "buildRequestInit", "Encoding", "resolve", "path", "posix", "split", "filter", "item", "newPosix", "for<PERSON>ach", "length", "pop", "push", "join", "isPath<PERSON>arent", "parent", "children", "pathsA", "pathsB", "every", "value", "index", "FilesystemWeb", "constructor", "_this", "arguments", "this", "DB_VERSION", "DB_NAME", "_writeCmds", "downloadFile", "_ref", "_asyncToGenerator", "options", "_a", "_b", "requestInit", "webFetchExtra", "response", "fetch", "url", "blob", "progress", "body", "Blob", "reader", "<PERSON><PERSON><PERSON><PERSON>", "bytes", "chunks", "contentType", "headers", "get", "contentLength", "parseInt", "done", "read", "status", "notifyListeners", "allChunks", "Uint8Array", "position", "chunk", "set", "buffer", "type", "undefined", "result", "writeFile", "directory", "recursive", "data", "uri", "_x", "apply", "initDb", "_this2", "_db", "window", "unavailable", "Promise", "reject", "request", "indexedDB", "open", "onupgradeneeded", "doUpgrade", "onsuccess", "onerror", "error", "onblocked", "console", "warn", "event", "eventTarget", "target", "db", "oldVersion", "objectStoreNames", "contains", "deleteObjectStore", "store", "createObjectStore", "keyP<PERSON>", "createIndex", "dbRequest", "cmd", "args", "_this3", "readFlag", "indexOf", "then", "conn", "tx", "transaction", "objectStore", "req", "dbIndexRequest", "indexName", "_this4", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "fsPath", "clear", "_this5", "readFile", "_this6", "entry", "Error", "content", "_this7", "encoding", "doRecursive", "occupiedEntry", "parentPath", "substr", "lastIndexOf", "parentEntry", "subDirIndex", "parentArgPath", "mkdir", "isBase64String", "now", "Date", "pathObj", "folder", "size", "ctime", "mtime", "appendFile", "_this8", "btoa", "atob", "deleteFile", "_this9", "entries", "IDBKeyRange", "only", "_this10", "depth", "match", "rmdir", "_this11", "fullPath", "readDirResult", "readdir", "files", "entryPath", "name", "entryObj", "stat", "_this12", "all", "map", "_ref2", "e", "subEntry", "substring", "_x2", "get<PERSON><PERSON>", "_this13", "_this14", "rename", "_this15", "_copy", "copy", "_this16", "requestPermissions", "publicStorage", "checkPermissions", "<PERSON><PERSON><PERSON><PERSON>", "_this17", "toDirectory", "to", "from", "fromDirectory", "fromPath", "to<PERSON><PERSON>", "to<PERSON><PERSON><PERSON>", "toPathComponents", "toParentDirectory", "fromObj", "updateTime", "_ref3", "_x3", "_x4", "_x5", "file", "UTF8", "writeResult", "contents", "filename", "str", "err", "_debug"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@capacitor/filesystem/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin, buildRequestInit } from '@capacitor/core';\nimport { Encoding } from './definitions';\nfunction resolve(path) {\n    const posix = path.split('/').filter(item => item !== '.');\n    const newPosix = [];\n    posix.forEach(item => {\n        if (item === '..' &&\n            newPosix.length > 0 &&\n            newPosix[newPosix.length - 1] !== '..') {\n            newPosix.pop();\n        }\n        else {\n            newPosix.push(item);\n        }\n    });\n    return newPosix.join('/');\n}\nfunction isPathParent(parent, children) {\n    parent = resolve(parent);\n    children = resolve(children);\n    const pathsA = parent.split('/');\n    const pathsB = children.split('/');\n    return (parent !== children &&\n        pathsA.every((value, index) => value === pathsB[index]));\n}\nexport class FilesystemWeb extends WebPlugin {\n    constructor() {\n        super(...arguments);\n        this.DB_VERSION = 1;\n        this.DB_NAME = 'Disc';\n        this._writeCmds = ['add', 'put', 'delete'];\n        /**\n         * Function that performs a http request to a server and downloads the file to the specified destination\n         *\n         * @param options the options for the download operation\n         * @returns a promise that resolves with the download file result\n         */\n        this.downloadFile = async (options) => {\n            var _a, _b;\n            const requestInit = buildRequestInit(options, options.webFetchExtra);\n            const response = await fetch(options.url, requestInit);\n            let blob;\n            if (!options.progress)\n                blob = await response.blob();\n            else if (!(response === null || response === void 0 ? void 0 : response.body))\n                blob = new Blob();\n            else {\n                const reader = response.body.getReader();\n                let bytes = 0;\n                const chunks = [];\n                const contentType = response.headers.get('content-type');\n                const contentLength = parseInt(response.headers.get('content-length') || '0', 10);\n                while (true) {\n                    const { done, value } = await reader.read();\n                    if (done)\n                        break;\n                    chunks.push(value);\n                    bytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                    const status = {\n                        url: options.url,\n                        bytes,\n                        contentLength,\n                    };\n                    this.notifyListeners('progress', status);\n                }\n                const allChunks = new Uint8Array(bytes);\n                let position = 0;\n                for (const chunk of chunks) {\n                    if (typeof chunk === 'undefined')\n                        continue;\n                    allChunks.set(chunk, position);\n                    position += chunk.length;\n                }\n                blob = new Blob([allChunks.buffer], { type: contentType || undefined });\n            }\n            const result = await this.writeFile({\n                path: options.path,\n                directory: (_a = options.directory) !== null && _a !== void 0 ? _a : undefined,\n                recursive: (_b = options.recursive) !== null && _b !== void 0 ? _b : false,\n                data: blob,\n            });\n            return { path: result.uri, blob };\n        };\n    }\n    async initDb() {\n        if (this._db !== undefined) {\n            return this._db;\n        }\n        if (!('indexedDB' in window)) {\n            throw this.unavailable(\"This browser doesn't support IndexedDB\");\n        }\n        return new Promise((resolve, reject) => {\n            const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);\n            request.onupgradeneeded = FilesystemWeb.doUpgrade;\n            request.onsuccess = () => {\n                this._db = request.result;\n                resolve(request.result);\n            };\n            request.onerror = () => reject(request.error);\n            request.onblocked = () => {\n                console.warn('db blocked');\n            };\n        });\n    }\n    static doUpgrade(event) {\n        const eventTarget = event.target;\n        const db = eventTarget.result;\n        switch (event.oldVersion) {\n            case 0:\n            case 1:\n            default: {\n                if (db.objectStoreNames.contains('FileStorage')) {\n                    db.deleteObjectStore('FileStorage');\n                }\n                const store = db.createObjectStore('FileStorage', { keyPath: 'path' });\n                store.createIndex('by_folder', 'folder');\n            }\n        }\n    }\n    async dbRequest(cmd, args) {\n        const readFlag = this._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n        return this.initDb().then((conn) => {\n            return new Promise((resolve, reject) => {\n                const tx = conn.transaction(['FileStorage'], readFlag);\n                const store = tx.objectStore('FileStorage');\n                const req = store[cmd](...args);\n                req.onsuccess = () => resolve(req.result);\n                req.onerror = () => reject(req.error);\n            });\n        });\n    }\n    async dbIndexRequest(indexName, cmd, args) {\n        const readFlag = this._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n        return this.initDb().then((conn) => {\n            return new Promise((resolve, reject) => {\n                const tx = conn.transaction(['FileStorage'], readFlag);\n                const store = tx.objectStore('FileStorage');\n                const index = store.index(indexName);\n                const req = index[cmd](...args);\n                req.onsuccess = () => resolve(req.result);\n                req.onerror = () => reject(req.error);\n            });\n        });\n    }\n    getPath(directory, uriPath) {\n        const cleanedUriPath = uriPath !== undefined ? uriPath.replace(/^[/]+|[/]+$/g, '') : '';\n        let fsPath = '';\n        if (directory !== undefined)\n            fsPath += '/' + directory;\n        if (uriPath !== '')\n            fsPath += '/' + cleanedUriPath;\n        return fsPath;\n    }\n    async clear() {\n        const conn = await this.initDb();\n        const tx = conn.transaction(['FileStorage'], 'readwrite');\n        const store = tx.objectStore('FileStorage');\n        store.clear();\n    }\n    /**\n     * Read a file from disk\n     * @param options options for the file read\n     * @return a promise that resolves with the read file data result\n     */\n    async readFile(options) {\n        const path = this.getPath(options.directory, options.path);\n        // const encoding = options.encoding;\n        const entry = (await this.dbRequest('get', [path]));\n        if (entry === undefined)\n            throw Error('File does not exist.');\n        return { data: entry.content ? entry.content : '' };\n    }\n    /**\n     * Write a file to disk in the specified location on device\n     * @param options options for the file write\n     * @return a promise that resolves with the file write result\n     */\n    async writeFile(options) {\n        const path = this.getPath(options.directory, options.path);\n        let data = options.data;\n        const encoding = options.encoding;\n        const doRecursive = options.recursive;\n        const occupiedEntry = (await this.dbRequest('get', [path]));\n        if (occupiedEntry && occupiedEntry.type === 'directory')\n            throw Error('The supplied path is a directory.');\n        const parentPath = path.substr(0, path.lastIndexOf('/'));\n        const parentEntry = (await this.dbRequest('get', [parentPath]));\n        if (parentEntry === undefined) {\n            const subDirIndex = parentPath.indexOf('/', 1);\n            if (subDirIndex !== -1) {\n                const parentArgPath = parentPath.substr(subDirIndex);\n                await this.mkdir({\n                    path: parentArgPath,\n                    directory: options.directory,\n                    recursive: doRecursive,\n                });\n            }\n        }\n        if (!encoding && !(data instanceof Blob)) {\n            data = data.indexOf(',') >= 0 ? data.split(',')[1] : data;\n            if (!this.isBase64String(data))\n                throw Error('The supplied data is not valid base64 content.');\n        }\n        const now = Date.now();\n        const pathObj = {\n            path: path,\n            folder: parentPath,\n            type: 'file',\n            size: data instanceof Blob ? data.size : data.length,\n            ctime: now,\n            mtime: now,\n            content: data,\n        };\n        await this.dbRequest('put', [pathObj]);\n        return {\n            uri: pathObj.path,\n        };\n    }\n    /**\n     * Append to a file on disk in the specified location on device\n     * @param options options for the file append\n     * @return a promise that resolves with the file write result\n     */\n    async appendFile(options) {\n        const path = this.getPath(options.directory, options.path);\n        let data = options.data;\n        const encoding = options.encoding;\n        const parentPath = path.substr(0, path.lastIndexOf('/'));\n        const now = Date.now();\n        let ctime = now;\n        const occupiedEntry = (await this.dbRequest('get', [path]));\n        if (occupiedEntry && occupiedEntry.type === 'directory')\n            throw Error('The supplied path is a directory.');\n        const parentEntry = (await this.dbRequest('get', [parentPath]));\n        if (parentEntry === undefined) {\n            const subDirIndex = parentPath.indexOf('/', 1);\n            if (subDirIndex !== -1) {\n                const parentArgPath = parentPath.substr(subDirIndex);\n                await this.mkdir({\n                    path: parentArgPath,\n                    directory: options.directory,\n                    recursive: true,\n                });\n            }\n        }\n        if (!encoding && !this.isBase64String(data))\n            throw Error('The supplied data is not valid base64 content.');\n        if (occupiedEntry !== undefined) {\n            if (occupiedEntry.content instanceof Blob) {\n                throw Error('The occupied entry contains a Blob object which cannot be appended to.');\n            }\n            if (occupiedEntry.content !== undefined && !encoding) {\n                data = btoa(atob(occupiedEntry.content) + atob(data));\n            }\n            else {\n                data = occupiedEntry.content + data;\n            }\n            ctime = occupiedEntry.ctime;\n        }\n        const pathObj = {\n            path: path,\n            folder: parentPath,\n            type: 'file',\n            size: data.length,\n            ctime: ctime,\n            mtime: now,\n            content: data,\n        };\n        await this.dbRequest('put', [pathObj]);\n    }\n    /**\n     * Delete a file from disk\n     * @param options options for the file delete\n     * @return a promise that resolves with the deleted file data result\n     */\n    async deleteFile(options) {\n        const path = this.getPath(options.directory, options.path);\n        const entry = (await this.dbRequest('get', [path]));\n        if (entry === undefined)\n            throw Error('File does not exist.');\n        const entries = await this.dbIndexRequest('by_folder', 'getAllKeys', [\n            IDBKeyRange.only(path),\n        ]);\n        if (entries.length !== 0)\n            throw Error('Folder is not empty.');\n        await this.dbRequest('delete', [path]);\n    }\n    /**\n     * Create a directory.\n     * @param options options for the mkdir\n     * @return a promise that resolves with the mkdir result\n     */\n    async mkdir(options) {\n        const path = this.getPath(options.directory, options.path);\n        const doRecursive = options.recursive;\n        const parentPath = path.substr(0, path.lastIndexOf('/'));\n        const depth = (path.match(/\\//g) || []).length;\n        const parentEntry = (await this.dbRequest('get', [parentPath]));\n        const occupiedEntry = (await this.dbRequest('get', [path]));\n        if (depth === 1)\n            throw Error('Cannot create Root directory');\n        if (occupiedEntry !== undefined)\n            throw Error('Current directory does already exist.');\n        if (!doRecursive && depth !== 2 && parentEntry === undefined)\n            throw Error('Parent directory must exist');\n        if (doRecursive && depth !== 2 && parentEntry === undefined) {\n            const parentArgPath = parentPath.substr(parentPath.indexOf('/', 1));\n            await this.mkdir({\n                path: parentArgPath,\n                directory: options.directory,\n                recursive: doRecursive,\n            });\n        }\n        const now = Date.now();\n        const pathObj = {\n            path: path,\n            folder: parentPath,\n            type: 'directory',\n            size: 0,\n            ctime: now,\n            mtime: now,\n        };\n        await this.dbRequest('put', [pathObj]);\n    }\n    /**\n     * Remove a directory\n     * @param options the options for the directory remove\n     */\n    async rmdir(options) {\n        const { path, directory, recursive } = options;\n        const fullPath = this.getPath(directory, path);\n        const entry = (await this.dbRequest('get', [fullPath]));\n        if (entry === undefined)\n            throw Error('Folder does not exist.');\n        if (entry.type !== 'directory')\n            throw Error('Requested path is not a directory');\n        const readDirResult = await this.readdir({ path, directory });\n        if (readDirResult.files.length !== 0 && !recursive)\n            throw Error('Folder is not empty');\n        for (const entry of readDirResult.files) {\n            const entryPath = `${path}/${entry.name}`;\n            const entryObj = await this.stat({ path: entryPath, directory });\n            if (entryObj.type === 'file') {\n                await this.deleteFile({ path: entryPath, directory });\n            }\n            else {\n                await this.rmdir({ path: entryPath, directory, recursive });\n            }\n        }\n        await this.dbRequest('delete', [fullPath]);\n    }\n    /**\n     * Return a list of files from the directory (not recursive)\n     * @param options the options for the readdir operation\n     * @return a promise that resolves with the readdir directory listing result\n     */\n    async readdir(options) {\n        const path = this.getPath(options.directory, options.path);\n        const entry = (await this.dbRequest('get', [path]));\n        if (options.path !== '' && entry === undefined)\n            throw Error('Folder does not exist.');\n        const entries = await this.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n        const files = await Promise.all(entries.map(async (e) => {\n            let subEntry = (await this.dbRequest('get', [e]));\n            if (subEntry === undefined) {\n                subEntry = (await this.dbRequest('get', [e + '/']));\n            }\n            return {\n                name: e.substring(path.length + 1),\n                type: subEntry.type,\n                size: subEntry.size,\n                ctime: subEntry.ctime,\n                mtime: subEntry.mtime,\n                uri: subEntry.path,\n            };\n        }));\n        return { files: files };\n    }\n    /**\n     * Return full File URI for a path and directory\n     * @param options the options for the stat operation\n     * @return a promise that resolves with the file stat result\n     */\n    async getUri(options) {\n        const path = this.getPath(options.directory, options.path);\n        let entry = (await this.dbRequest('get', [path]));\n        if (entry === undefined) {\n            entry = (await this.dbRequest('get', [path + '/']));\n        }\n        return {\n            uri: (entry === null || entry === void 0 ? void 0 : entry.path) || path,\n        };\n    }\n    /**\n     * Return data about a file\n     * @param options the options for the stat operation\n     * @return a promise that resolves with the file stat result\n     */\n    async stat(options) {\n        const path = this.getPath(options.directory, options.path);\n        let entry = (await this.dbRequest('get', [path]));\n        if (entry === undefined) {\n            entry = (await this.dbRequest('get', [path + '/']));\n        }\n        if (entry === undefined)\n            throw Error('Entry does not exist.');\n        return {\n            type: entry.type,\n            size: entry.size,\n            ctime: entry.ctime,\n            mtime: entry.mtime,\n            uri: entry.path,\n        };\n    }\n    /**\n     * Rename a file or directory\n     * @param options the options for the rename operation\n     * @return a promise that resolves with the rename result\n     */\n    async rename(options) {\n        await this._copy(options, true);\n        return;\n    }\n    /**\n     * Copy a file or directory\n     * @param options the options for the copy operation\n     * @return a promise that resolves with the copy result\n     */\n    async copy(options) {\n        return this._copy(options, false);\n    }\n    async requestPermissions() {\n        return { publicStorage: 'granted' };\n    }\n    async checkPermissions() {\n        return { publicStorage: 'granted' };\n    }\n    /**\n     * Function that can perform a copy or a rename\n     * @param options the options for the rename operation\n     * @param doRename whether to perform a rename or copy operation\n     * @return a promise that resolves with the result\n     */\n    async _copy(options, doRename = false) {\n        let { toDirectory } = options;\n        const { to, from, directory: fromDirectory } = options;\n        if (!to || !from) {\n            throw Error('Both to and from must be provided');\n        }\n        // If no \"to\" directory is provided, use the \"from\" directory\n        if (!toDirectory) {\n            toDirectory = fromDirectory;\n        }\n        const fromPath = this.getPath(fromDirectory, from);\n        const toPath = this.getPath(toDirectory, to);\n        // Test that the \"to\" and \"from\" locations are different\n        if (fromPath === toPath) {\n            return {\n                uri: toPath,\n            };\n        }\n        if (isPathParent(fromPath, toPath)) {\n            throw Error('To path cannot contain the from path');\n        }\n        // Check the state of the \"to\" location\n        let toObj;\n        try {\n            toObj = await this.stat({\n                path: to,\n                directory: toDirectory,\n            });\n        }\n        catch (e) {\n            // To location does not exist, ensure the directory containing \"to\" location exists and is a directory\n            const toPathComponents = to.split('/');\n            toPathComponents.pop();\n            const toPath = toPathComponents.join('/');\n            // Check the containing directory of the \"to\" location exists\n            if (toPathComponents.length > 0) {\n                const toParentDirectory = await this.stat({\n                    path: toPath,\n                    directory: toDirectory,\n                });\n                if (toParentDirectory.type !== 'directory') {\n                    throw new Error('Parent directory of the to path is a file');\n                }\n            }\n        }\n        // Cannot overwrite a directory\n        if (toObj && toObj.type === 'directory') {\n            throw new Error('Cannot overwrite a directory with a file');\n        }\n        // Ensure the \"from\" object exists\n        const fromObj = await this.stat({\n            path: from,\n            directory: fromDirectory,\n        });\n        // Set the mtime/ctime of the supplied path\n        const updateTime = async (path, ctime, mtime) => {\n            const fullPath = this.getPath(toDirectory, path);\n            const entry = (await this.dbRequest('get', [fullPath]));\n            entry.ctime = ctime;\n            entry.mtime = mtime;\n            await this.dbRequest('put', [entry]);\n        };\n        const ctime = fromObj.ctime ? fromObj.ctime : Date.now();\n        switch (fromObj.type) {\n            // The \"from\" object is a file\n            case 'file': {\n                // Read the file\n                const file = await this.readFile({\n                    path: from,\n                    directory: fromDirectory,\n                });\n                // Optionally remove the file\n                if (doRename) {\n                    await this.deleteFile({\n                        path: from,\n                        directory: fromDirectory,\n                    });\n                }\n                let encoding;\n                if (!(file.data instanceof Blob) && !this.isBase64String(file.data)) {\n                    encoding = Encoding.UTF8;\n                }\n                // Write the file to the new location\n                const writeResult = await this.writeFile({\n                    path: to,\n                    directory: toDirectory,\n                    data: file.data,\n                    encoding: encoding,\n                });\n                // Copy the mtime/ctime of a renamed file\n                if (doRename) {\n                    await updateTime(to, ctime, fromObj.mtime);\n                }\n                // Resolve promise\n                return writeResult;\n            }\n            case 'directory': {\n                if (toObj) {\n                    throw Error('Cannot move a directory over an existing object');\n                }\n                try {\n                    // Create the to directory\n                    await this.mkdir({\n                        path: to,\n                        directory: toDirectory,\n                        recursive: false,\n                    });\n                    // Copy the mtime/ctime of a renamed directory\n                    if (doRename) {\n                        await updateTime(to, ctime, fromObj.mtime);\n                    }\n                }\n                catch (e) {\n                    // ignore\n                }\n                // Iterate over the contents of the from location\n                const contents = (await this.readdir({\n                    path: from,\n                    directory: fromDirectory,\n                })).files;\n                for (const filename of contents) {\n                    // Move item from the from directory to the to directory\n                    await this._copy({\n                        from: `${from}/${filename.name}`,\n                        to: `${to}/${filename.name}`,\n                        directory: fromDirectory,\n                        toDirectory,\n                    }, doRename);\n                }\n                // Optionally remove the original from directory\n                if (doRename) {\n                    await this.rmdir({\n                        path: from,\n                        directory: fromDirectory,\n                    });\n                }\n            }\n        }\n        return {\n            uri: toPath,\n        };\n    }\n    isBase64String(str) {\n        try {\n            return btoa(atob(str)) == str;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n}\nFilesystemWeb._debug = true;\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC7D,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,OAAOA,CAACC,IAAI,EAAE;EACnB,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAK,GAAG,CAAC;EAC1D,MAAMC,QAAQ,GAAG,EAAE;EACnBJ,KAAK,CAACK,OAAO,CAACF,IAAI,IAAI;IAClB,IAAIA,IAAI,KAAK,IAAI,IACbC,QAAQ,CAACE,MAAM,GAAG,CAAC,IACnBF,QAAQ,CAACA,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;MACxCF,QAAQ,CAACG,GAAG,CAAC,CAAC;IAClB,CAAC,MACI;MACDH,QAAQ,CAACI,IAAI,CAACL,IAAI,CAAC;IACvB;EACJ,CAAC,CAAC;EACF,OAAOC,QAAQ,CAACK,IAAI,CAAC,GAAG,CAAC;AAC7B;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACpCD,MAAM,GAAGb,OAAO,CAACa,MAAM,CAAC;EACxBC,QAAQ,GAAGd,OAAO,CAACc,QAAQ,CAAC;EAC5B,MAAMC,MAAM,GAAGF,MAAM,CAACV,KAAK,CAAC,GAAG,CAAC;EAChC,MAAMa,MAAM,GAAGF,QAAQ,CAACX,KAAK,CAAC,GAAG,CAAC;EAClC,OAAQU,MAAM,KAAKC,QAAQ,IACvBC,MAAM,CAACE,KAAK,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAK,KAAKF,MAAM,CAACG,KAAK,CAAC,CAAC;AAC/D;AACA,OAAO,MAAMC,aAAa,SAASvB,SAAS,CAAC;EACzCwB,WAAWA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IAAAD,KAAA,GAAAE,IAAA;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,MAAM;IACrB,IAAI,CAACC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,OAAO,EAAK;QACnC,IAAIC,EAAE,EAAEC,EAAE;QACV,MAAMC,WAAW,GAAGpC,gBAAgB,CAACiC,OAAO,EAAEA,OAAO,CAACI,aAAa,CAAC;QACpE,MAAMC,QAAQ,SAASC,KAAK,CAACN,OAAO,CAACO,GAAG,EAAEJ,WAAW,CAAC;QACtD,IAAIK,IAAI;QACR,IAAI,CAACR,OAAO,CAACS,QAAQ,EACjBD,IAAI,SAASH,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,KAC5B,IAAI,EAAEH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACK,IAAI,CAAC,EACzEF,IAAI,GAAG,IAAIG,IAAI,CAAC,CAAC,CAAC,KACjB;UACD,MAAMC,MAAM,GAAGP,QAAQ,CAACK,IAAI,CAACG,SAAS,CAAC,CAAC;UACxC,IAAIC,KAAK,GAAG,CAAC;UACb,MAAMC,MAAM,GAAG,EAAE;UACjB,MAAMC,WAAW,GAAGX,QAAQ,CAACY,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UACxD,MAAMC,aAAa,GAAGC,QAAQ,CAACf,QAAQ,CAACY,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;UACjF,OAAO,IAAI,EAAE;YACT,MAAM;cAAEG,IAAI;cAAElC;YAAM,CAAC,SAASyB,MAAM,CAACU,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EACJ;YACJN,MAAM,CAACpC,IAAI,CAACQ,KAAK,CAAC;YAClB2B,KAAK,IAAI,CAAC3B,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACV,MAAM,KAAK,CAAC;YAC1E,MAAM8C,MAAM,GAAG;cACXhB,GAAG,EAAEP,OAAO,CAACO,GAAG;cAChBO,KAAK;cACLK;YACJ,CAAC;YACD5B,KAAI,CAACiC,eAAe,CAAC,UAAU,EAAED,MAAM,CAAC;UAC5C;UACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACZ,KAAK,CAAC;UACvC,IAAIa,QAAQ,GAAG,CAAC;UAChB,KAAK,MAAMC,KAAK,IAAIb,MAAM,EAAE;YACxB,IAAI,OAAOa,KAAK,KAAK,WAAW,EAC5B;YACJH,SAAS,CAACI,GAAG,CAACD,KAAK,EAAED,QAAQ,CAAC;YAC9BA,QAAQ,IAAIC,KAAK,CAACnD,MAAM;UAC5B;UACA+B,IAAI,GAAG,IAAIG,IAAI,CAAC,CAACc,SAAS,CAACK,MAAM,CAAC,EAAE;YAAEC,IAAI,EAAEf,WAAW,IAAIgB;UAAU,CAAC,CAAC;QAC3E;QACA,MAAMC,MAAM,SAAS1C,KAAI,CAAC2C,SAAS,CAAC;UAChChE,IAAI,EAAE8B,OAAO,CAAC9B,IAAI;UAClBiE,SAAS,EAAE,CAAClC,EAAE,GAAGD,OAAO,CAACmC,SAAS,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG+B,SAAS;UAC9EI,SAAS,EAAE,CAAClC,EAAE,GAAGF,OAAO,CAACoC,SAAS,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;UAC1EmC,IAAI,EAAE7B;QACV,CAAC,CAAC;QACF,OAAO;UAAEtC,IAAI,EAAE+D,MAAM,CAACK,GAAG;UAAE9B;QAAK,CAAC;MACrC,CAAC;MAAA,iBAAA+B,EAAA;QAAA,OAAAzC,IAAA,CAAA0C,KAAA,OAAAhD,SAAA;MAAA;IAAA;EACL;EACMiD,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3C,iBAAA;MACX,IAAI2C,MAAI,CAACC,GAAG,KAAKX,SAAS,EAAE;QACxB,OAAOU,MAAI,CAACC,GAAG;MACnB;MACA,IAAI,EAAE,WAAW,IAAIC,MAAM,CAAC,EAAE;QAC1B,MAAMF,MAAI,CAACG,WAAW,CAAC,wCAAwC,CAAC;MACpE;MACA,OAAO,IAAIC,OAAO,CAAC,CAAC7E,OAAO,EAAE8E,MAAM,KAAK;QACpC,MAAMC,OAAO,GAAGC,SAAS,CAACC,IAAI,CAACR,MAAI,CAAC/C,OAAO,EAAE+C,MAAI,CAAChD,UAAU,CAAC;QAC7DsD,OAAO,CAACG,eAAe,GAAG9D,aAAa,CAAC+D,SAAS;QACjDJ,OAAO,CAACK,SAAS,GAAG,MAAM;UACtBX,MAAI,CAACC,GAAG,GAAGK,OAAO,CAACf,MAAM;UACzBhE,OAAO,CAAC+E,OAAO,CAACf,MAAM,CAAC;QAC3B,CAAC;QACDe,OAAO,CAACM,OAAO,GAAG,MAAMP,MAAM,CAACC,OAAO,CAACO,KAAK,CAAC;QAC7CP,OAAO,CAACQ,SAAS,GAAG,MAAM;UACtBC,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;QAC9B,CAAC;MACL,CAAC,CAAC;IAAC;EACP;EACA,OAAON,SAASA,CAACO,KAAK,EAAE;IACpB,MAAMC,WAAW,GAAGD,KAAK,CAACE,MAAM;IAChC,MAAMC,EAAE,GAAGF,WAAW,CAAC3B,MAAM;IAC7B,QAAQ0B,KAAK,CAACI,UAAU;MACpB,KAAK,CAAC;MACN,KAAK,CAAC;MACN;QAAS;UACL,IAAID,EAAE,CAACE,gBAAgB,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC7CH,EAAE,CAACI,iBAAiB,CAAC,aAAa,CAAC;UACvC;UACA,MAAMC,KAAK,GAAGL,EAAE,CAACM,iBAAiB,CAAC,aAAa,EAAE;YAAEC,OAAO,EAAE;UAAO,CAAC,CAAC;UACtEF,KAAK,CAACG,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC;QAC5C;IACJ;EACJ;EACMC,SAASA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3E,iBAAA;MACvB,MAAM4E,QAAQ,GAAGD,MAAI,CAAC9E,UAAU,CAACgF,OAAO,CAACJ,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,UAAU;MAC/E,OAAOE,MAAI,CAACjC,MAAM,CAAC,CAAC,CAACoC,IAAI,CAAEC,IAAI,IAAK;QAChC,OAAO,IAAIhC,OAAO,CAAC,CAAC7E,OAAO,EAAE8E,MAAM,KAAK;UACpC,MAAMgC,EAAE,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,aAAa,CAAC,EAAEL,QAAQ,CAAC;UACtD,MAAMR,KAAK,GAAGY,EAAE,CAACE,WAAW,CAAC,aAAa,CAAC;UAC3C,MAAMC,GAAG,GAAGf,KAAK,CAACK,GAAG,CAAC,CAAC,GAAGC,IAAI,CAAC;UAC/BS,GAAG,CAAC7B,SAAS,GAAG,MAAMpF,OAAO,CAACiH,GAAG,CAACjD,MAAM,CAAC;UACzCiD,GAAG,CAAC5B,OAAO,GAAG,MAAMP,MAAM,CAACmC,GAAG,CAAC3B,KAAK,CAAC;QACzC,CAAC,CAAC;MACN,CAAC,CAAC;IAAC;EACP;EACM4B,cAAcA,CAACC,SAAS,EAAEZ,GAAG,EAAEC,IAAI,EAAE;IAAA,IAAAY,MAAA;IAAA,OAAAtF,iBAAA;MACvC,MAAM4E,QAAQ,GAAGU,MAAI,CAACzF,UAAU,CAACgF,OAAO,CAACJ,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,UAAU;MAC/E,OAAOa,MAAI,CAAC5C,MAAM,CAAC,CAAC,CAACoC,IAAI,CAAEC,IAAI,IAAK;QAChC,OAAO,IAAIhC,OAAO,CAAC,CAAC7E,OAAO,EAAE8E,MAAM,KAAK;UACpC,MAAMgC,EAAE,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,aAAa,CAAC,EAAEL,QAAQ,CAAC;UACtD,MAAMR,KAAK,GAAGY,EAAE,CAACE,WAAW,CAAC,aAAa,CAAC;UAC3C,MAAM7F,KAAK,GAAG+E,KAAK,CAAC/E,KAAK,CAACgG,SAAS,CAAC;UACpC,MAAMF,GAAG,GAAG9F,KAAK,CAACoF,GAAG,CAAC,CAAC,GAAGC,IAAI,CAAC;UAC/BS,GAAG,CAAC7B,SAAS,GAAG,MAAMpF,OAAO,CAACiH,GAAG,CAACjD,MAAM,CAAC;UACzCiD,GAAG,CAAC5B,OAAO,GAAG,MAAMP,MAAM,CAACmC,GAAG,CAAC3B,KAAK,CAAC;QACzC,CAAC,CAAC;MACN,CAAC,CAAC;IAAC;EACP;EACA+B,OAAOA,CAACnD,SAAS,EAAEoD,OAAO,EAAE;IACxB,MAAMC,cAAc,GAAGD,OAAO,KAAKvD,SAAS,GAAGuD,OAAO,CAACE,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,GAAG,EAAE;IACvF,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIvD,SAAS,KAAKH,SAAS,EACvB0D,MAAM,IAAI,GAAG,GAAGvD,SAAS;IAC7B,IAAIoD,OAAO,KAAK,EAAE,EACdG,MAAM,IAAI,GAAG,GAAGF,cAAc;IAClC,OAAOE,MAAM;EACjB;EACMC,KAAKA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7F,iBAAA;MACV,MAAM+E,IAAI,SAASc,MAAI,CAACnD,MAAM,CAAC,CAAC;MAChC,MAAMsC,EAAE,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC;MACzD,MAAMb,KAAK,GAAGY,EAAE,CAACE,WAAW,CAAC,aAAa,CAAC;MAC3Cd,KAAK,CAACwB,KAAK,CAAC,CAAC;IAAC;EAClB;EACA;AACJ;AACA;AACA;AACA;EACUE,QAAQA,CAAC7F,OAAO,EAAE;IAAA,IAAA8F,MAAA;IAAA,OAAA/F,iBAAA;MACpB,MAAM7B,IAAI,GAAG4H,MAAI,CAACR,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D;MACA,MAAM6H,KAAK,SAAUD,MAAI,CAACvB,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MACnD,IAAI6H,KAAK,KAAK/D,SAAS,EACnB,MAAMgE,KAAK,CAAC,sBAAsB,CAAC;MACvC,OAAO;QAAE3D,IAAI,EAAE0D,KAAK,CAACE,OAAO,GAAGF,KAAK,CAACE,OAAO,GAAG;MAAG,CAAC;IAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;EACU/D,SAASA,CAAClC,OAAO,EAAE;IAAA,IAAAkG,MAAA;IAAA,OAAAnG,iBAAA;MACrB,MAAM7B,IAAI,GAAGgI,MAAI,CAACZ,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,IAAImE,IAAI,GAAGrC,OAAO,CAACqC,IAAI;MACvB,MAAM8D,QAAQ,GAAGnG,OAAO,CAACmG,QAAQ;MACjC,MAAMC,WAAW,GAAGpG,OAAO,CAACoC,SAAS;MACrC,MAAMiE,aAAa,SAAUH,MAAI,CAAC3B,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MAC3D,IAAImI,aAAa,IAAIA,aAAa,CAACtE,IAAI,KAAK,WAAW,EACnD,MAAMiE,KAAK,CAAC,mCAAmC,CAAC;MACpD,MAAMM,UAAU,GAAGpI,IAAI,CAACqI,MAAM,CAAC,CAAC,EAAErI,IAAI,CAACsI,WAAW,CAAC,GAAG,CAAC,CAAC;MACxD,MAAMC,WAAW,SAAUP,MAAI,CAAC3B,SAAS,CAAC,KAAK,EAAE,CAAC+B,UAAU,CAAC,CAAE;MAC/D,IAAIG,WAAW,KAAKzE,SAAS,EAAE;QAC3B,MAAM0E,WAAW,GAAGJ,UAAU,CAAC1B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9C,IAAI8B,WAAW,KAAK,CAAC,CAAC,EAAE;UACpB,MAAMC,aAAa,GAAGL,UAAU,CAACC,MAAM,CAACG,WAAW,CAAC;UACpD,MAAMR,MAAI,CAACU,KAAK,CAAC;YACb1I,IAAI,EAAEyI,aAAa;YACnBxE,SAAS,EAAEnC,OAAO,CAACmC,SAAS;YAC5BC,SAAS,EAAEgE;UACf,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACD,QAAQ,IAAI,EAAE9D,IAAI,YAAY1B,IAAI,CAAC,EAAE;QACtC0B,IAAI,GAAGA,IAAI,CAACuC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGvC,IAAI,CAACjE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGiE,IAAI;QACzD,IAAI,CAAC6D,MAAI,CAACW,cAAc,CAACxE,IAAI,CAAC,EAC1B,MAAM2D,KAAK,CAAC,gDAAgD,CAAC;MACrE;MACA,MAAMc,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAG;QACZ9I,IAAI,EAAEA,IAAI;QACV+I,MAAM,EAAEX,UAAU;QAClBvE,IAAI,EAAE,MAAM;QACZmF,IAAI,EAAE7E,IAAI,YAAY1B,IAAI,GAAG0B,IAAI,CAAC6E,IAAI,GAAG7E,IAAI,CAAC5D,MAAM;QACpD0I,KAAK,EAAEL,GAAG;QACVM,KAAK,EAAEN,GAAG;QACVb,OAAO,EAAE5D;MACb,CAAC;MACD,MAAM6D,MAAI,CAAC3B,SAAS,CAAC,KAAK,EAAE,CAACyC,OAAO,CAAC,CAAC;MACtC,OAAO;QACH1E,GAAG,EAAE0E,OAAO,CAAC9I;MACjB,CAAC;IAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACUmJ,UAAUA,CAACrH,OAAO,EAAE;IAAA,IAAAsH,MAAA;IAAA,OAAAvH,iBAAA;MACtB,MAAM7B,IAAI,GAAGoJ,MAAI,CAAChC,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,IAAImE,IAAI,GAAGrC,OAAO,CAACqC,IAAI;MACvB,MAAM8D,QAAQ,GAAGnG,OAAO,CAACmG,QAAQ;MACjC,MAAMG,UAAU,GAAGpI,IAAI,CAACqI,MAAM,CAAC,CAAC,EAAErI,IAAI,CAACsI,WAAW,CAAC,GAAG,CAAC,CAAC;MACxD,MAAMM,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAIK,KAAK,GAAGL,GAAG;MACf,MAAMT,aAAa,SAAUiB,MAAI,CAAC/C,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MAC3D,IAAImI,aAAa,IAAIA,aAAa,CAACtE,IAAI,KAAK,WAAW,EACnD,MAAMiE,KAAK,CAAC,mCAAmC,CAAC;MACpD,MAAMS,WAAW,SAAUa,MAAI,CAAC/C,SAAS,CAAC,KAAK,EAAE,CAAC+B,UAAU,CAAC,CAAE;MAC/D,IAAIG,WAAW,KAAKzE,SAAS,EAAE;QAC3B,MAAM0E,WAAW,GAAGJ,UAAU,CAAC1B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9C,IAAI8B,WAAW,KAAK,CAAC,CAAC,EAAE;UACpB,MAAMC,aAAa,GAAGL,UAAU,CAACC,MAAM,CAACG,WAAW,CAAC;UACpD,MAAMY,MAAI,CAACV,KAAK,CAAC;YACb1I,IAAI,EAAEyI,aAAa;YACnBxE,SAAS,EAAEnC,OAAO,CAACmC,SAAS;YAC5BC,SAAS,EAAE;UACf,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC+D,QAAQ,IAAI,CAACmB,MAAI,CAACT,cAAc,CAACxE,IAAI,CAAC,EACvC,MAAM2D,KAAK,CAAC,gDAAgD,CAAC;MACjE,IAAIK,aAAa,KAAKrE,SAAS,EAAE;QAC7B,IAAIqE,aAAa,CAACJ,OAAO,YAAYtF,IAAI,EAAE;UACvC,MAAMqF,KAAK,CAAC,wEAAwE,CAAC;QACzF;QACA,IAAIK,aAAa,CAACJ,OAAO,KAAKjE,SAAS,IAAI,CAACmE,QAAQ,EAAE;UAClD9D,IAAI,GAAGkF,IAAI,CAACC,IAAI,CAACnB,aAAa,CAACJ,OAAO,CAAC,GAAGuB,IAAI,CAACnF,IAAI,CAAC,CAAC;QACzD,CAAC,MACI;UACDA,IAAI,GAAGgE,aAAa,CAACJ,OAAO,GAAG5D,IAAI;QACvC;QACA8E,KAAK,GAAGd,aAAa,CAACc,KAAK;MAC/B;MACA,MAAMH,OAAO,GAAG;QACZ9I,IAAI,EAAEA,IAAI;QACV+I,MAAM,EAAEX,UAAU;QAClBvE,IAAI,EAAE,MAAM;QACZmF,IAAI,EAAE7E,IAAI,CAAC5D,MAAM;QACjB0I,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEN,GAAG;QACVb,OAAO,EAAE5D;MACb,CAAC;MACD,MAAMiF,MAAI,CAAC/C,SAAS,CAAC,KAAK,EAAE,CAACyC,OAAO,CAAC,CAAC;IAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACUS,UAAUA,CAACzH,OAAO,EAAE;IAAA,IAAA0H,MAAA;IAAA,OAAA3H,iBAAA;MACtB,MAAM7B,IAAI,GAAGwJ,MAAI,CAACpC,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,MAAM6H,KAAK,SAAU2B,MAAI,CAACnD,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MACnD,IAAI6H,KAAK,KAAK/D,SAAS,EACnB,MAAMgE,KAAK,CAAC,sBAAsB,CAAC;MACvC,MAAM2B,OAAO,SAASD,MAAI,CAACvC,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CACjEyC,WAAW,CAACC,IAAI,CAAC3J,IAAI,CAAC,CACzB,CAAC;MACF,IAAIyJ,OAAO,CAAClJ,MAAM,KAAK,CAAC,EACpB,MAAMuH,KAAK,CAAC,sBAAsB,CAAC;MACvC,MAAM0B,MAAI,CAACnD,SAAS,CAAC,QAAQ,EAAE,CAACrG,IAAI,CAAC,CAAC;IAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACU0I,KAAKA,CAAC5G,OAAO,EAAE;IAAA,IAAA8H,OAAA;IAAA,OAAA/H,iBAAA;MACjB,MAAM7B,IAAI,GAAG4J,OAAI,CAACxC,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,MAAMkI,WAAW,GAAGpG,OAAO,CAACoC,SAAS;MACrC,MAAMkE,UAAU,GAAGpI,IAAI,CAACqI,MAAM,CAAC,CAAC,EAAErI,IAAI,CAACsI,WAAW,CAAC,GAAG,CAAC,CAAC;MACxD,MAAMuB,KAAK,GAAG,CAAC7J,IAAI,CAAC8J,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAEvJ,MAAM;MAC9C,MAAMgI,WAAW,SAAUqB,OAAI,CAACvD,SAAS,CAAC,KAAK,EAAE,CAAC+B,UAAU,CAAC,CAAE;MAC/D,MAAMD,aAAa,SAAUyB,OAAI,CAACvD,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MAC3D,IAAI6J,KAAK,KAAK,CAAC,EACX,MAAM/B,KAAK,CAAC,8BAA8B,CAAC;MAC/C,IAAIK,aAAa,KAAKrE,SAAS,EAC3B,MAAMgE,KAAK,CAAC,uCAAuC,CAAC;MACxD,IAAI,CAACI,WAAW,IAAI2B,KAAK,KAAK,CAAC,IAAItB,WAAW,KAAKzE,SAAS,EACxD,MAAMgE,KAAK,CAAC,6BAA6B,CAAC;MAC9C,IAAII,WAAW,IAAI2B,KAAK,KAAK,CAAC,IAAItB,WAAW,KAAKzE,SAAS,EAAE;QACzD,MAAM2E,aAAa,GAAGL,UAAU,CAACC,MAAM,CAACD,UAAU,CAAC1B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACnE,MAAMkD,OAAI,CAAClB,KAAK,CAAC;UACb1I,IAAI,EAAEyI,aAAa;UACnBxE,SAAS,EAAEnC,OAAO,CAACmC,SAAS;UAC5BC,SAAS,EAAEgE;QACf,CAAC,CAAC;MACN;MACA,MAAMU,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAME,OAAO,GAAG;QACZ9I,IAAI,EAAEA,IAAI;QACV+I,MAAM,EAAEX,UAAU;QAClBvE,IAAI,EAAE,WAAW;QACjBmF,IAAI,EAAE,CAAC;QACPC,KAAK,EAAEL,GAAG;QACVM,KAAK,EAAEN;MACX,CAAC;MACD,MAAMgB,OAAI,CAACvD,SAAS,CAAC,KAAK,EAAE,CAACyC,OAAO,CAAC,CAAC;IAAC;EAC3C;EACA;AACJ;AACA;AACA;EACUiB,KAAKA,CAACjI,OAAO,EAAE;IAAA,IAAAkI,OAAA;IAAA,OAAAnI,iBAAA;MACjB,MAAM;QAAE7B,IAAI;QAAEiE,SAAS;QAAEC;MAAU,CAAC,GAAGpC,OAAO;MAC9C,MAAMmI,QAAQ,GAAGD,OAAI,CAAC5C,OAAO,CAACnD,SAAS,EAAEjE,IAAI,CAAC;MAC9C,MAAM6H,KAAK,SAAUmC,OAAI,CAAC3D,SAAS,CAAC,KAAK,EAAE,CAAC4D,QAAQ,CAAC,CAAE;MACvD,IAAIpC,KAAK,KAAK/D,SAAS,EACnB,MAAMgE,KAAK,CAAC,wBAAwB,CAAC;MACzC,IAAID,KAAK,CAAChE,IAAI,KAAK,WAAW,EAC1B,MAAMiE,KAAK,CAAC,mCAAmC,CAAC;MACpD,MAAMoC,aAAa,SAASF,OAAI,CAACG,OAAO,CAAC;QAAEnK,IAAI;QAAEiE;MAAU,CAAC,CAAC;MAC7D,IAAIiG,aAAa,CAACE,KAAK,CAAC7J,MAAM,KAAK,CAAC,IAAI,CAAC2D,SAAS,EAC9C,MAAM4D,KAAK,CAAC,qBAAqB,CAAC;MACtC,KAAK,MAAMD,KAAK,IAAIqC,aAAa,CAACE,KAAK,EAAE;QACrC,MAAMC,SAAS,GAAI,GAAErK,IAAK,IAAG6H,KAAK,CAACyC,IAAK,EAAC;QACzC,MAAMC,QAAQ,SAASP,OAAI,CAACQ,IAAI,CAAC;UAAExK,IAAI,EAAEqK,SAAS;UAAEpG;QAAU,CAAC,CAAC;QAChE,IAAIsG,QAAQ,CAAC1G,IAAI,KAAK,MAAM,EAAE;UAC1B,MAAMmG,OAAI,CAACT,UAAU,CAAC;YAAEvJ,IAAI,EAAEqK,SAAS;YAAEpG;UAAU,CAAC,CAAC;QACzD,CAAC,MACI;UACD,MAAM+F,OAAI,CAACD,KAAK,CAAC;YAAE/J,IAAI,EAAEqK,SAAS;YAAEpG,SAAS;YAAEC;UAAU,CAAC,CAAC;QAC/D;MACJ;MACA,MAAM8F,OAAI,CAAC3D,SAAS,CAAC,QAAQ,EAAE,CAAC4D,QAAQ,CAAC,CAAC;IAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;EACUE,OAAOA,CAACrI,OAAO,EAAE;IAAA,IAAA2I,OAAA;IAAA,OAAA5I,iBAAA;MACnB,MAAM7B,IAAI,GAAGyK,OAAI,CAACrD,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,MAAM6H,KAAK,SAAU4C,OAAI,CAACpE,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MACnD,IAAI8B,OAAO,CAAC9B,IAAI,KAAK,EAAE,IAAI6H,KAAK,KAAK/D,SAAS,EAC1C,MAAMgE,KAAK,CAAC,wBAAwB,CAAC;MACzC,MAAM2B,OAAO,SAASgB,OAAI,CAACxD,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAACyC,WAAW,CAACC,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC;MAC9F,MAAMoK,KAAK,SAASxF,OAAO,CAAC8F,GAAG,CAACjB,OAAO,CAACkB,GAAG;QAAA,IAAAC,KAAA,GAAA/I,iBAAA,CAAC,WAAOgJ,CAAC,EAAK;UACrD,IAAIC,QAAQ,SAAUL,OAAI,CAACpE,SAAS,CAAC,KAAK,EAAE,CAACwE,CAAC,CAAC,CAAE;UACjD,IAAIC,QAAQ,KAAKhH,SAAS,EAAE;YACxBgH,QAAQ,SAAUL,OAAI,CAACpE,SAAS,CAAC,KAAK,EAAE,CAACwE,CAAC,GAAG,GAAG,CAAC,CAAE;UACvD;UACA,OAAO;YACHP,IAAI,EAAEO,CAAC,CAACE,SAAS,CAAC/K,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC;YAClCsD,IAAI,EAAEiH,QAAQ,CAACjH,IAAI;YACnBmF,IAAI,EAAE8B,QAAQ,CAAC9B,IAAI;YACnBC,KAAK,EAAE6B,QAAQ,CAAC7B,KAAK;YACrBC,KAAK,EAAE4B,QAAQ,CAAC5B,KAAK;YACrB9E,GAAG,EAAE0G,QAAQ,CAAC9K;UAClB,CAAC;QACL,CAAC;QAAA,iBAAAgL,GAAA;UAAA,OAAAJ,KAAA,CAAAtG,KAAA,OAAAhD,SAAA;QAAA;MAAA,IAAC,CAAC;MACH,OAAO;QAAE8I,KAAK,EAAEA;MAAM,CAAC;IAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACUa,MAAMA,CAACnJ,OAAO,EAAE;IAAA,IAAAoJ,OAAA;IAAA,OAAArJ,iBAAA;MAClB,MAAM7B,IAAI,GAAGkL,OAAI,CAAC9D,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,IAAI6H,KAAK,SAAUqD,OAAI,CAAC7E,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MACjD,IAAI6H,KAAK,KAAK/D,SAAS,EAAE;QACrB+D,KAAK,SAAUqD,OAAI,CAAC7E,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,GAAG,GAAG,CAAC,CAAE;MACvD;MACA,OAAO;QACHoE,GAAG,EAAE,CAACyD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC7H,IAAI,KAAKA;MACvE,CAAC;IAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACUwK,IAAIA,CAAC1I,OAAO,EAAE;IAAA,IAAAqJ,OAAA;IAAA,OAAAtJ,iBAAA;MAChB,MAAM7B,IAAI,GAAGmL,OAAI,CAAC/D,OAAO,CAACtF,OAAO,CAACmC,SAAS,EAAEnC,OAAO,CAAC9B,IAAI,CAAC;MAC1D,IAAI6H,KAAK,SAAUsD,OAAI,CAAC9E,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,CAAC,CAAE;MACjD,IAAI6H,KAAK,KAAK/D,SAAS,EAAE;QACrB+D,KAAK,SAAUsD,OAAI,CAAC9E,SAAS,CAAC,KAAK,EAAE,CAACrG,IAAI,GAAG,GAAG,CAAC,CAAE;MACvD;MACA,IAAI6H,KAAK,KAAK/D,SAAS,EACnB,MAAMgE,KAAK,CAAC,uBAAuB,CAAC;MACxC,OAAO;QACHjE,IAAI,EAAEgE,KAAK,CAAChE,IAAI;QAChBmF,IAAI,EAAEnB,KAAK,CAACmB,IAAI;QAChBC,KAAK,EAAEpB,KAAK,CAACoB,KAAK;QAClBC,KAAK,EAAErB,KAAK,CAACqB,KAAK;QAClB9E,GAAG,EAAEyD,KAAK,CAAC7H;MACf,CAAC;IAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACUoL,MAAMA,CAACtJ,OAAO,EAAE;IAAA,IAAAuJ,OAAA;IAAA,OAAAxJ,iBAAA;MAClB,MAAMwJ,OAAI,CAACC,KAAK,CAACxJ,OAAO,EAAE,IAAI,CAAC;MAC/B;IAAO;EACX;EACA;AACJ;AACA;AACA;AACA;EACUyJ,IAAIA,CAACzJ,OAAO,EAAE;IAAA,IAAA0J,OAAA;IAAA,OAAA3J,iBAAA;MAChB,OAAO2J,OAAI,CAACF,KAAK,CAACxJ,OAAO,EAAE,KAAK,CAAC;IAAC;EACtC;EACM2J,kBAAkBA,CAAA,EAAG;IAAA,OAAA5J,iBAAA;MACvB,OAAO;QAAE6J,aAAa,EAAE;MAAU,CAAC;IAAC;EACxC;EACMC,gBAAgBA,CAAA,EAAG;IAAA,OAAA9J,iBAAA;MACrB,OAAO;QAAE6J,aAAa,EAAE;MAAU,CAAC;IAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACUJ,KAAKA,CAACxJ,OAAO,EAAE8J,QAAQ,GAAG,KAAK,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAhK,iBAAA;MACnC,IAAI;QAAEiK;MAAY,CAAC,GAAGhK,OAAO;MAC7B,MAAM;QAAEiK,EAAE;QAAEC,IAAI;QAAE/H,SAAS,EAAEgI;MAAc,CAAC,GAAGnK,OAAO;MACtD,IAAI,CAACiK,EAAE,IAAI,CAACC,IAAI,EAAE;QACd,MAAMlE,KAAK,CAAC,mCAAmC,CAAC;MACpD;MACA;MACA,IAAI,CAACgE,WAAW,EAAE;QACdA,WAAW,GAAGG,aAAa;MAC/B;MACA,MAAMC,QAAQ,GAAGL,OAAI,CAACzE,OAAO,CAAC6E,aAAa,EAAED,IAAI,CAAC;MAClD,MAAMG,MAAM,GAAGN,OAAI,CAACzE,OAAO,CAAC0E,WAAW,EAAEC,EAAE,CAAC;MAC5C;MACA,IAAIG,QAAQ,KAAKC,MAAM,EAAE;QACrB,OAAO;UACH/H,GAAG,EAAE+H;QACT,CAAC;MACL;MACA,IAAIxL,YAAY,CAACuL,QAAQ,EAAEC,MAAM,CAAC,EAAE;QAChC,MAAMrE,KAAK,CAAC,sCAAsC,CAAC;MACvD;MACA;MACA,IAAIsE,KAAK;MACT,IAAI;QACAA,KAAK,SAASP,OAAI,CAACrB,IAAI,CAAC;UACpBxK,IAAI,EAAE+L,EAAE;UACR9H,SAAS,EAAE6H;QACf,CAAC,CAAC;MACN,CAAC,CACD,OAAOjB,CAAC,EAAE;QACN;QACA,MAAMwB,gBAAgB,GAAGN,EAAE,CAAC7L,KAAK,CAAC,GAAG,CAAC;QACtCmM,gBAAgB,CAAC7L,GAAG,CAAC,CAAC;QACtB,MAAM2L,MAAM,GAAGE,gBAAgB,CAAC3L,IAAI,CAAC,GAAG,CAAC;QACzC;QACA,IAAI2L,gBAAgB,CAAC9L,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM+L,iBAAiB,SAAST,OAAI,CAACrB,IAAI,CAAC;YACtCxK,IAAI,EAAEmM,MAAM;YACZlI,SAAS,EAAE6H;UACf,CAAC,CAAC;UACF,IAAIQ,iBAAiB,CAACzI,IAAI,KAAK,WAAW,EAAE;YACxC,MAAM,IAAIiE,KAAK,CAAC,2CAA2C,CAAC;UAChE;QACJ;MACJ;MACA;MACA,IAAIsE,KAAK,IAAIA,KAAK,CAACvI,IAAI,KAAK,WAAW,EAAE;QACrC,MAAM,IAAIiE,KAAK,CAAC,0CAA0C,CAAC;MAC/D;MACA;MACA,MAAMyE,OAAO,SAASV,OAAI,CAACrB,IAAI,CAAC;QAC5BxK,IAAI,EAAEgM,IAAI;QACV/H,SAAS,EAAEgI;MACf,CAAC,CAAC;MACF;MACA,MAAMO,UAAU;QAAA,IAAAC,KAAA,GAAA5K,iBAAA,CAAG,WAAO7B,IAAI,EAAEiJ,KAAK,EAAEC,KAAK,EAAK;UAC7C,MAAMe,QAAQ,GAAG4B,OAAI,CAACzE,OAAO,CAAC0E,WAAW,EAAE9L,IAAI,CAAC;UAChD,MAAM6H,KAAK,SAAUgE,OAAI,CAACxF,SAAS,CAAC,KAAK,EAAE,CAAC4D,QAAQ,CAAC,CAAE;UACvDpC,KAAK,CAACoB,KAAK,GAAGA,KAAK;UACnBpB,KAAK,CAACqB,KAAK,GAAGA,KAAK;UACnB,MAAM2C,OAAI,CAACxF,SAAS,CAAC,KAAK,EAAE,CAACwB,KAAK,CAAC,CAAC;QACxC,CAAC;QAAA,gBANK2E,UAAUA,CAAAE,GAAA,EAAAC,GAAA,EAAAC,GAAA;UAAA,OAAAH,KAAA,CAAAnI,KAAA,OAAAhD,SAAA;QAAA;MAAA,GAMf;MACD,MAAM2H,KAAK,GAAGsD,OAAO,CAACtD,KAAK,GAAGsD,OAAO,CAACtD,KAAK,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;MACxD,QAAQ2D,OAAO,CAAC1I,IAAI;QAChB;QACA,KAAK,MAAM;UAAE;YACT;YACA,MAAMgJ,IAAI,SAAShB,OAAI,CAAClE,QAAQ,CAAC;cAC7B3H,IAAI,EAAEgM,IAAI;cACV/H,SAAS,EAAEgI;YACf,CAAC,CAAC;YACF;YACA,IAAIL,QAAQ,EAAE;cACV,MAAMC,OAAI,CAACtC,UAAU,CAAC;gBAClBvJ,IAAI,EAAEgM,IAAI;gBACV/H,SAAS,EAAEgI;cACf,CAAC,CAAC;YACN;YACA,IAAIhE,QAAQ;YACZ,IAAI,EAAE4E,IAAI,CAAC1I,IAAI,YAAY1B,IAAI,CAAC,IAAI,CAACoJ,OAAI,CAAClD,cAAc,CAACkE,IAAI,CAAC1I,IAAI,CAAC,EAAE;cACjE8D,QAAQ,GAAGnI,QAAQ,CAACgN,IAAI;YAC5B;YACA;YACA,MAAMC,WAAW,SAASlB,OAAI,CAAC7H,SAAS,CAAC;cACrChE,IAAI,EAAE+L,EAAE;cACR9H,SAAS,EAAE6H,WAAW;cACtB3H,IAAI,EAAE0I,IAAI,CAAC1I,IAAI;cACf8D,QAAQ,EAAEA;YACd,CAAC,CAAC;YACF;YACA,IAAI2D,QAAQ,EAAE;cACV,MAAMY,UAAU,CAACT,EAAE,EAAE9C,KAAK,EAAEsD,OAAO,CAACrD,KAAK,CAAC;YAC9C;YACA;YACA,OAAO6D,WAAW;UACtB;QACA,KAAK,WAAW;UAAE;YACd,IAAIX,KAAK,EAAE;cACP,MAAMtE,KAAK,CAAC,iDAAiD,CAAC;YAClE;YACA,IAAI;cACA;cACA,MAAM+D,OAAI,CAACnD,KAAK,CAAC;gBACb1I,IAAI,EAAE+L,EAAE;gBACR9H,SAAS,EAAE6H,WAAW;gBACtB5H,SAAS,EAAE;cACf,CAAC,CAAC;cACF;cACA,IAAI0H,QAAQ,EAAE;gBACV,MAAMY,UAAU,CAACT,EAAE,EAAE9C,KAAK,EAAEsD,OAAO,CAACrD,KAAK,CAAC;cAC9C;YACJ,CAAC,CACD,OAAO2B,CAAC,EAAE;cACN;YAAA;YAEJ;YACA,MAAMmC,QAAQ,GAAG,OAAOnB,OAAI,CAAC1B,OAAO,CAAC;cACjCnK,IAAI,EAAEgM,IAAI;cACV/H,SAAS,EAAEgI;YACf,CAAC,CAAC,EAAE7B,KAAK;YACT,KAAK,MAAM6C,QAAQ,IAAID,QAAQ,EAAE;cAC7B;cACA,MAAMnB,OAAI,CAACP,KAAK,CAAC;gBACbU,IAAI,EAAG,GAAEA,IAAK,IAAGiB,QAAQ,CAAC3C,IAAK,EAAC;gBAChCyB,EAAE,EAAG,GAAEA,EAAG,IAAGkB,QAAQ,CAAC3C,IAAK,EAAC;gBAC5BrG,SAAS,EAAEgI,aAAa;gBACxBH;cACJ,CAAC,EAAEF,QAAQ,CAAC;YAChB;YACA;YACA,IAAIA,QAAQ,EAAE;cACV,MAAMC,OAAI,CAAC9B,KAAK,CAAC;gBACb/J,IAAI,EAAEgM,IAAI;gBACV/H,SAAS,EAAEgI;cACf,CAAC,CAAC;YACN;UACJ;MACJ;MACA,OAAO;QACH7H,GAAG,EAAE+H;MACT,CAAC;IAAC;EACN;EACAxD,cAAcA,CAACuE,GAAG,EAAE;IAChB,IAAI;MACA,OAAO7D,IAAI,CAACC,IAAI,CAAC4D,GAAG,CAAC,CAAC,IAAIA,GAAG;IACjC,CAAC,CACD,OAAOC,GAAG,EAAE;MACR,OAAO,KAAK;IAChB;EACJ;AACJ;AACAhM,aAAa,CAACiM,MAAM,GAAG,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
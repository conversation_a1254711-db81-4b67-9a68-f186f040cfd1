{"ast": null, "code": "import { connectableObservableDescriptor } from '../observable/ConnectableObservable';\nexport function multicast(subjectOrSubjectFactory, selector) {\n  return function multicastOperatorFunction(source) {\n    let subjectFactory;\n    if (typeof subjectOrSubjectFactory === 'function') {\n      subjectFactory = subjectOrSubjectFactory;\n    } else {\n      subjectFactory = function subjectFactory() {\n        return subjectOrSubjectFactory;\n      };\n    }\n    if (typeof selector === 'function') {\n      return source.lift(new MulticastOperator(subjectFactory, selector));\n    }\n    const connectable = Object.create(source, connectableObservableDescriptor);\n    connectable.source = source;\n    connectable.subjectFactory = subjectFactory;\n    return connectable;\n  };\n}\nexport class MulticastOperator {\n  constructor(subjectFactory, selector) {\n    this.subjectFactory = subjectFactory;\n    this.selector = selector;\n  }\n  call(subscriber, source) {\n    const {\n      selector\n    } = this;\n    const subject = this.subjectFactory();\n    const subscription = selector(subject).subscribe(subscriber);\n    subscription.add(source.subscribe(subject));\n    return subscription;\n  }\n}", "map": {"version": 3, "names": ["connectableObservableDescriptor", "multicast", "subjectOrSubjectFactory", "selector", "multicastOperatorFunction", "source", "subjectFactory", "lift", "MulticastOperator", "connectable", "Object", "create", "constructor", "call", "subscriber", "subject", "subscription", "subscribe", "add"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/multicast.js"], "sourcesContent": ["import { connectableObservableDescriptor } from '../observable/ConnectableObservable';\nexport function multicast(subjectOrSubjectFactory, selector) {\n    return function multicastOperatorFunction(source) {\n        let subjectFactory;\n        if (typeof subjectOrSubjectFactory === 'function') {\n            subjectFactory = subjectOrSubjectFactory;\n        }\n        else {\n            subjectFactory = function subjectFactory() {\n                return subjectOrSubjectFactory;\n            };\n        }\n        if (typeof selector === 'function') {\n            return source.lift(new MulticastOperator(subjectFactory, selector));\n        }\n        const connectable = Object.create(source, connectableObservableDescriptor);\n        connectable.source = source;\n        connectable.subjectFactory = subjectFactory;\n        return connectable;\n    };\n}\nexport class MulticastOperator {\n    constructor(subjectFactory, selector) {\n        this.subjectFactory = subjectFactory;\n        this.selector = selector;\n    }\n    call(subscriber, source) {\n        const { selector } = this;\n        const subject = this.subjectFactory();\n        const subscription = selector(subject).subscribe(subscriber);\n        subscription.add(source.subscribe(subject));\n        return subscription;\n    }\n}\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,qCAAqC;AACrF,OAAO,SAASC,SAASA,CAACC,uBAAuB,EAAEC,QAAQ,EAAE;EACzD,OAAO,SAASC,yBAAyBA,CAACC,MAAM,EAAE;IAC9C,IAAIC,cAAc;IAClB,IAAI,OAAOJ,uBAAuB,KAAK,UAAU,EAAE;MAC/CI,cAAc,GAAGJ,uBAAuB;IAC5C,CAAC,MACI;MACDI,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QACvC,OAAOJ,uBAAuB;MAClC,CAAC;IACL;IACA,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAE;MAChC,OAAOE,MAAM,CAACE,IAAI,CAAC,IAAIC,iBAAiB,CAACF,cAAc,EAAEH,QAAQ,CAAC,CAAC;IACvE;IACA,MAAMM,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACN,MAAM,EAAEL,+BAA+B,CAAC;IAC1ES,WAAW,CAACJ,MAAM,GAAGA,MAAM;IAC3BI,WAAW,CAACH,cAAc,GAAGA,cAAc;IAC3C,OAAOG,WAAW;EACtB,CAAC;AACL;AACA,OAAO,MAAMD,iBAAiB,CAAC;EAC3BI,WAAWA,CAACN,cAAc,EAAEH,QAAQ,EAAE;IAClC,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;EAC5B;EACAU,IAAIA,CAACC,UAAU,EAAET,MAAM,EAAE;IACrB,MAAM;MAAEF;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMY,OAAO,GAAG,IAAI,CAACT,cAAc,CAAC,CAAC;IACrC,MAAMU,YAAY,GAAGb,QAAQ,CAACY,OAAO,CAAC,CAACE,SAAS,CAACH,UAAU,CAAC;IAC5DE,YAAY,CAACE,GAAG,CAACb,MAAM,CAACY,SAAS,CAACF,OAAO,CAAC,CAAC;IAC3C,OAAOC,YAAY;EACvB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
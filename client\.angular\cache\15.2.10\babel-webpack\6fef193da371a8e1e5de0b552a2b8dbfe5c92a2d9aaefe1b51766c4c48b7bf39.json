{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, scheduler) {\n  if (selectorOrScheduler && typeof selectorOrScheduler !== 'function') {\n    scheduler = selectorOrScheduler;\n  }\n  const selector = typeof selectorOrScheduler === 'function' ? selectorOrScheduler : undefined;\n  const subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n  return source => multicast(() => subject, selector)(source);\n}", "map": {"version": 3, "names": ["ReplaySubject", "multicast", "publishReplay", "bufferSize", "windowTime", "selectorOrScheduler", "scheduler", "selector", "undefined", "subject", "source"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/publishReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, scheduler) {\n    if (selectorOrScheduler && typeof selectorOrScheduler !== 'function') {\n        scheduler = selectorOrScheduler;\n    }\n    const selector = typeof selectorOrScheduler === 'function' ? selectorOrScheduler : undefined;\n    const subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n    return (source) => multicast(() => subject, selector)(source);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,aAAaA,CAACC,UAAU,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,SAAS,EAAE;EAClF,IAAID,mBAAmB,IAAI,OAAOA,mBAAmB,KAAK,UAAU,EAAE;IAClEC,SAAS,GAAGD,mBAAmB;EACnC;EACA,MAAME,QAAQ,GAAG,OAAOF,mBAAmB,KAAK,UAAU,GAAGA,mBAAmB,GAAGG,SAAS;EAC5F,MAAMC,OAAO,GAAG,IAAIT,aAAa,CAACG,UAAU,EAAEC,UAAU,EAAEE,SAAS,CAAC;EACpE,OAAQI,MAAM,IAAKT,SAAS,CAAC,MAAMQ,OAAO,EAAEF,QAAQ,CAAC,CAACG,MAAM,CAAC;AACjE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
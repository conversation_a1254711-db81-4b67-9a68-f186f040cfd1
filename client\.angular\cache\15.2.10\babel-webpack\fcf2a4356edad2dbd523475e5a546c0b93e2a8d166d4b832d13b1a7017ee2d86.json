{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function mapTo(value) {\n  return source => source.lift(new MapToOperator(value));\n}\nclass MapToOperator {\n  constructor(value) {\n    this.value = value;\n  }\n  call(subscriber, source) {\n    return source.subscribe(new MapToSubscriber(subscriber, this.value));\n  }\n}\nclass MapToSubscriber extends Subscriber {\n  constructor(destination, value) {\n    super(destination);\n    this.value = value;\n  }\n  _next(x) {\n    this.destination.next(this.value);\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "mapTo", "value", "source", "lift", "MapToOperator", "constructor", "call", "subscriber", "subscribe", "MapToSubscriber", "destination", "_next", "x", "next"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/mapTo.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function mapTo(value) {\n    return (source) => source.lift(new MapToOperator(value));\n}\nclass MapToOperator {\n    constructor(value) {\n        this.value = value;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new MapToSubscriber(subscriber, this.value));\n    }\n}\nclass MapToSubscriber extends Subscriber {\n    constructor(destination, value) {\n        super(destination);\n        this.value = value;\n    }\n    _next(x) {\n        this.destination.next(this.value);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,KAAKA,CAACC,KAAK,EAAE;EACzB,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAAC,IAAIC,aAAa,CAACH,KAAK,CAAC,CAAC;AAC5D;AACA,MAAMG,aAAa,CAAC;EAChBC,WAAWA,CAACJ,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAK,IAAIA,CAACC,UAAU,EAAEL,MAAM,EAAE;IACrB,OAAOA,MAAM,CAACM,SAAS,CAAC,IAAIC,eAAe,CAACF,UAAU,EAAE,IAAI,CAACN,KAAK,CAAC,CAAC;EACxE;AACJ;AACA,MAAMQ,eAAe,SAASV,UAAU,CAAC;EACrCM,WAAWA,CAACK,WAAW,EAAET,KAAK,EAAE;IAC5B,KAAK,CAACS,WAAW,CAAC;IAClB,IAAI,CAACT,KAAK,GAAGA,KAAK;EACtB;EACAU,KAAKA,CAACC,CAAC,EAAE;IACL,IAAI,CAACF,WAAW,CAACG,IAAI,CAAC,IAAI,CAACZ,KAAK,CAAC;EACrC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
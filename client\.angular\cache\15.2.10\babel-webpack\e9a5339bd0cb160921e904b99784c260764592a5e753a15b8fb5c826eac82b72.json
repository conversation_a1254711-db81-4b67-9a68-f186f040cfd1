{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport '@firebase/installations';\nimport { Component } from '@firebase/component';\nimport { openDB, deleteDB } from 'idb';\nimport { ErrorFactory, isIndexedDBAvailable, validateIndexedDBOpenable, getModularInstance } from '@firebase/util';\nimport { _registerComponent, _getProvider, getApp } from '@firebase/app';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DEFAULT_VAPID_KEY = 'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\nconst ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n/** Key of FCM Payload in Notification's data field. */\nconst FCM_MSG = 'FCM_MSG';\nconst CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nconst SDK_PLATFORM_WEB = 3;\nconst EVENT_MESSAGE_DELIVERED = 1;\nvar MessageType$1;\n(function (MessageType) {\n  MessageType[MessageType[\"DATA_MESSAGE\"] = 1] = \"DATA_MESSAGE\";\n  MessageType[MessageType[\"DISPLAY_NOTIFICATION\"] = 3] = \"DISPLAY_NOTIFICATION\";\n})(MessageType$1 || (MessageType$1 = {}));\n\n/**\r\n * @license\r\n * Copyright 2018 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\r\n * in compliance with the License. You may obtain a copy of the License at\r\n *\r\n * http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software distributed under the License\r\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\r\n * or implied. See the License for the specific language governing permissions and limitations under\r\n * the License.\r\n */\nvar MessageType;\n(function (MessageType) {\n  MessageType[\"PUSH_RECEIVED\"] = \"push-received\";\n  MessageType[\"NOTIFICATION_CLICKED\"] = \"notification-clicked\";\n})(MessageType || (MessageType = {}));\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction arrayToBase64(array) {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction base64ToArray(base64String) {\n  const padding = '='.repeat((4 - base64String.length % 4) % 4);\n  const base64 = (base64String + padding).replace(/\\-/g, '+').replace(/_/g, '/');\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\r\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\r\n * callback is called for all versions of the old DB.\r\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\nfunction migrateOldDatabase(_x) {\n  return _migrateOldDatabase.apply(this, arguments);\n}\nfunction _migrateOldDatabase() {\n  _migrateOldDatabase = _asyncToGenerator(function* (senderId) {\n    if ('databases' in indexedDB) {\n      // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n      // typecast when it lands in TS types.\n      const databases = yield indexedDB.databases();\n      const dbNames = databases.map(db => db.name);\n      if (!dbNames.includes(OLD_DB_NAME)) {\n        // old DB didn't exist, no need to open.\n        return null;\n      }\n    }\n    let tokenDetails = null;\n    const db = yield openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n      upgrade: function () {\n        var _ref = _asyncToGenerator(function* (db, oldVersion, newVersion, upgradeTransaction) {\n          var _a;\n          if (oldVersion < 2) {\n            // Database too old, skip migration.\n            return;\n          }\n          if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n            // Database did not exist. Nothing to do.\n            return;\n          }\n          const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n          const value = yield objectStore.index('fcmSenderId').get(senderId);\n          yield objectStore.clear();\n          if (!value) {\n            // No entry in the database, nothing to migrate.\n            return;\n          }\n          if (oldVersion === 2) {\n            const oldDetails = value;\n            if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n              return;\n            }\n            tokenDetails = {\n              token: oldDetails.fcmToken,\n              createTime: (_a = oldDetails.createTime) !== null && _a !== void 0 ? _a : Date.now(),\n              subscriptionOptions: {\n                auth: oldDetails.auth,\n                p256dh: oldDetails.p256dh,\n                endpoint: oldDetails.endpoint,\n                swScope: oldDetails.swScope,\n                vapidKey: typeof oldDetails.vapidKey === 'string' ? oldDetails.vapidKey : arrayToBase64(oldDetails.vapidKey)\n              }\n            };\n          } else if (oldVersion === 3) {\n            const oldDetails = value;\n            tokenDetails = {\n              token: oldDetails.fcmToken,\n              createTime: oldDetails.createTime,\n              subscriptionOptions: {\n                auth: arrayToBase64(oldDetails.auth),\n                p256dh: arrayToBase64(oldDetails.p256dh),\n                endpoint: oldDetails.endpoint,\n                swScope: oldDetails.swScope,\n                vapidKey: arrayToBase64(oldDetails.vapidKey)\n              }\n            };\n          } else if (oldVersion === 4) {\n            const oldDetails = value;\n            tokenDetails = {\n              token: oldDetails.fcmToken,\n              createTime: oldDetails.createTime,\n              subscriptionOptions: {\n                auth: arrayToBase64(oldDetails.auth),\n                p256dh: arrayToBase64(oldDetails.p256dh),\n                endpoint: oldDetails.endpoint,\n                swScope: oldDetails.swScope,\n                vapidKey: arrayToBase64(oldDetails.vapidKey)\n              }\n            };\n          }\n        });\n        return function upgrade(_x29, _x30, _x31, _x32) {\n          return _ref.apply(this, arguments);\n        };\n      }()\n    });\n    db.close();\n    // Delete all old databases.\n    yield deleteDB(OLD_DB_NAME);\n    yield deleteDB('fcm_vapid_details_db');\n    yield deleteDB('undefined');\n    return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n  });\n  return _migrateOldDatabase.apply(this, arguments);\n}\nfunction checkTokenDetails(tokenDetails) {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const {\n    subscriptionOptions\n  } = tokenDetails;\n  return typeof tokenDetails.createTime === 'number' && tokenDetails.createTime > 0 && typeof tokenDetails.token === 'string' && tokenDetails.token.length > 0 && typeof subscriptionOptions.auth === 'string' && subscriptionOptions.auth.length > 0 && typeof subscriptionOptions.p256dh === 'string' && subscriptionOptions.p256dh.length > 0 && typeof subscriptionOptions.endpoint === 'string' && subscriptionOptions.endpoint.length > 0 && typeof subscriptionOptions.swScope === 'string' && subscriptionOptions.swScope.length > 0 && typeof subscriptionOptions.vapidKey === 'string' && subscriptionOptions.vapidKey.length > 0;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// Exported for tests.\nconst DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n/** Gets record(s) from the objectStore that match the given key. */\nfunction dbGet(_x2) {\n  return _dbGet.apply(this, arguments);\n}\n/** Assigns or overwrites the record for the given key with the given value. */\nfunction _dbGet() {\n  _dbGet = _asyncToGenerator(function* (firebaseDependencies) {\n    const key = getKey(firebaseDependencies);\n    const db = yield getDbPromise();\n    const tokenDetails = yield db.transaction(OBJECT_STORE_NAME).objectStore(OBJECT_STORE_NAME).get(key);\n    if (tokenDetails) {\n      return tokenDetails;\n    } else {\n      // Check if there is a tokenDetails object in the old DB.\n      const oldTokenDetails = yield migrateOldDatabase(firebaseDependencies.appConfig.senderId);\n      if (oldTokenDetails) {\n        yield dbSet(firebaseDependencies, oldTokenDetails);\n        return oldTokenDetails;\n      }\n    }\n  });\n  return _dbGet.apply(this, arguments);\n}\nfunction dbSet(_x3, _x4) {\n  return _dbSet.apply(this, arguments);\n}\n/** Removes record(s) from the objectStore that match the given key. */\nfunction _dbSet() {\n  _dbSet = _asyncToGenerator(function* (firebaseDependencies, tokenDetails) {\n    const key = getKey(firebaseDependencies);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    yield tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n    yield tx.done;\n    return tokenDetails;\n  });\n  return _dbSet.apply(this, arguments);\n}\nfunction dbRemove(_x5) {\n  return _dbRemove.apply(this, arguments);\n}\nfunction _dbRemove() {\n  _dbRemove = _asyncToGenerator(function* (firebaseDependencies) {\n    const key = getKey(firebaseDependencies);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    yield tx.objectStore(OBJECT_STORE_NAME).delete(key);\n    yield tx.done;\n  });\n  return _dbRemove.apply(this, arguments);\n}\nfunction getKey({\n  appConfig\n}) {\n  return appConfig.appId;\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERROR_MAP = {\n  [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\n  [\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */]: 'This method is available in a Window context.',\n  [\"only-available-in-sw\" /* ErrorCode.AVAILABLE_IN_SW */]: 'This method is available in a service worker context.',\n  [\"permission-default\" /* ErrorCode.PERMISSION_DEFAULT */]: 'The notification permission was not granted and dismissed instead.',\n  [\"permission-blocked\" /* ErrorCode.PERMISSION_BLOCKED */]: 'The notification permission was not granted and blocked instead.',\n  [\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */]: \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */]: \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [\"failed-service-worker-registration\" /* ErrorCode.FAILED_DEFAULT_REGISTRATION */]: 'We are unable to register the default service worker. {$browserErrorMessage}',\n  [\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */]: 'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */]: 'FCM returned no token when subscribing the user to push.',\n  [\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */]: 'A problem occurred while unsubscribing the ' + 'user from FCM: {$errorInfo}',\n  [\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */]: 'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */]: 'FCM returned no token when updating the user to push.',\n  [\"use-sw-after-get-token\" /* ErrorCode.USE_SW_AFTER_GET_TOKEN */]: 'The useServiceWorker() method may only be called once and must be ' + 'called before calling getToken() to ensure your service worker is used.',\n  [\"invalid-sw-registration\" /* ErrorCode.INVALID_SW_REGISTRATION */]: 'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [\"invalid-bg-handler\" /* ErrorCode.INVALID_BG_HANDLER */]: 'The input to setBackgroundMessageHandler() must be a function.',\n  [\"invalid-vapid-key\" /* ErrorCode.INVALID_VAPID_KEY */]: 'The public VAPID key must be a string.',\n  [\"use-vapid-key-after-get-token\" /* ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN */]: 'The usePublicVapidKey() method may only be called once and must be ' + 'called before calling getToken() to ensure your VAPID key is used.'\n};\nconst ERROR_FACTORY = new ErrorFactory('messaging', 'Messaging', ERROR_MAP);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction requestGetToken(_x6, _x7) {\n  return _requestGetToken.apply(this, arguments);\n}\nfunction _requestGetToken() {\n  _requestGetToken = _asyncToGenerator(function* (firebaseDependencies, subscriptionOptions) {\n    const headers = yield getHeaders(firebaseDependencies);\n    const body = getBody(subscriptionOptions);\n    const subscribeOptions = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(body)\n    };\n    let responseData;\n    try {\n      const response = yield fetch(getEndpoint(firebaseDependencies.appConfig), subscribeOptions);\n      responseData = yield response.json();\n    } catch (err) {\n      throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\n        errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n      });\n    }\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\n        errorInfo: message\n      });\n    }\n    if (!responseData.token) {\n      throw ERROR_FACTORY.create(\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */);\n    }\n\n    return responseData.token;\n  });\n  return _requestGetToken.apply(this, arguments);\n}\nfunction requestUpdateToken(_x8, _x9) {\n  return _requestUpdateToken.apply(this, arguments);\n}\nfunction _requestUpdateToken() {\n  _requestUpdateToken = _asyncToGenerator(function* (firebaseDependencies, tokenDetails) {\n    const headers = yield getHeaders(firebaseDependencies);\n    const body = getBody(tokenDetails.subscriptionOptions);\n    const updateOptions = {\n      method: 'PATCH',\n      headers,\n      body: JSON.stringify(body)\n    };\n    let responseData;\n    try {\n      const response = yield fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`, updateOptions);\n      responseData = yield response.json();\n    } catch (err) {\n      throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\n        errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n      });\n    }\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\n        errorInfo: message\n      });\n    }\n    if (!responseData.token) {\n      throw ERROR_FACTORY.create(\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */);\n    }\n\n    return responseData.token;\n  });\n  return _requestUpdateToken.apply(this, arguments);\n}\nfunction requestDeleteToken(_x10, _x11) {\n  return _requestDeleteToken.apply(this, arguments);\n}\nfunction _requestDeleteToken() {\n  _requestDeleteToken = _asyncToGenerator(function* (firebaseDependencies, token) {\n    const headers = yield getHeaders(firebaseDependencies);\n    const unsubscribeOptions = {\n      method: 'DELETE',\n      headers\n    };\n    try {\n      const response = yield fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${token}`, unsubscribeOptions);\n      const responseData = yield response.json();\n      if (responseData.error) {\n        const message = responseData.error.message;\n        throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\n          errorInfo: message\n        });\n      }\n    } catch (err) {\n      throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\n        errorInfo: err === null || err === void 0 ? void 0 : err.toString()\n      });\n    }\n  });\n  return _requestDeleteToken.apply(this, arguments);\n}\nfunction getEndpoint({\n  projectId\n}) {\n  return `${ENDPOINT}/projects/${projectId}/registrations`;\n}\nfunction getHeaders(_x12) {\n  return _getHeaders.apply(this, arguments);\n}\nfunction _getHeaders() {\n  _getHeaders = _asyncToGenerator(function* ({\n    appConfig,\n    installations\n  }) {\n    const authToken = yield installations.getToken();\n    return new Headers({\n      'Content-Type': 'application/json',\n      Accept: 'application/json',\n      'x-goog-api-key': appConfig.apiKey,\n      'x-goog-firebase-installations-auth': `FIS ${authToken}`\n    });\n  });\n  return _getHeaders.apply(this, arguments);\n}\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}) {\n  const body = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n  return body;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\nfunction getTokenInternal(_x13) {\n  return _getTokenInternal.apply(this, arguments);\n}\n/**\r\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\r\n * the push subscription if it exists.\r\n */\nfunction _getTokenInternal() {\n  _getTokenInternal = _asyncToGenerator(function* (messaging) {\n    const pushSubscription = yield getPushSubscription(messaging.swRegistration, messaging.vapidKey);\n    const subscriptionOptions = {\n      vapidKey: messaging.vapidKey,\n      swScope: messaging.swRegistration.scope,\n      endpoint: pushSubscription.endpoint,\n      auth: arrayToBase64(pushSubscription.getKey('auth')),\n      p256dh: arrayToBase64(pushSubscription.getKey('p256dh'))\n    };\n    const tokenDetails = yield dbGet(messaging.firebaseDependencies);\n    if (!tokenDetails) {\n      // No token, get a new one.\n      return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n    } else if (!isTokenValid(tokenDetails.subscriptionOptions, subscriptionOptions)) {\n      // Invalid token, get a new one.\n      try {\n        yield requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\n      } catch (e) {\n        // Suppress errors because of #2364\n        console.warn(e);\n      }\n      return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n    } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n      // Weekly token refresh\n      return updateToken(messaging, {\n        token: tokenDetails.token,\n        createTime: Date.now(),\n        subscriptionOptions\n      });\n    } else {\n      // Valid token, nothing to do.\n      return tokenDetails.token;\n    }\n  });\n  return _getTokenInternal.apply(this, arguments);\n}\nfunction deleteTokenInternal(_x14) {\n  return _deleteTokenInternal.apply(this, arguments);\n}\nfunction _deleteTokenInternal() {\n  _deleteTokenInternal = _asyncToGenerator(function* (messaging) {\n    const tokenDetails = yield dbGet(messaging.firebaseDependencies);\n    if (tokenDetails) {\n      yield requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\n      yield dbRemove(messaging.firebaseDependencies);\n    }\n    // Unsubscribe from the push subscription.\n    const pushSubscription = yield messaging.swRegistration.pushManager.getSubscription();\n    if (pushSubscription) {\n      return pushSubscription.unsubscribe();\n    }\n    // If there's no SW, consider it a success.\n    return true;\n  });\n  return _deleteTokenInternal.apply(this, arguments);\n}\nfunction updateToken(_x15, _x16) {\n  return _updateToken.apply(this, arguments);\n}\nfunction _updateToken() {\n  _updateToken = _asyncToGenerator(function* (messaging, tokenDetails) {\n    try {\n      const updatedToken = yield requestUpdateToken(messaging.firebaseDependencies, tokenDetails);\n      const updatedTokenDetails = Object.assign(Object.assign({}, tokenDetails), {\n        token: updatedToken,\n        createTime: Date.now()\n      });\n      yield dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n      return updatedToken;\n    } catch (e) {\n      yield deleteTokenInternal(messaging);\n      throw e;\n    }\n  });\n  return _updateToken.apply(this, arguments);\n}\nfunction getNewToken(_x17, _x18) {\n  return _getNewToken.apply(this, arguments);\n}\n/**\r\n * Gets a PushSubscription for the current user.\r\n */\nfunction _getNewToken() {\n  _getNewToken = _asyncToGenerator(function* (firebaseDependencies, subscriptionOptions) {\n    const token = yield requestGetToken(firebaseDependencies, subscriptionOptions);\n    const tokenDetails = {\n      token,\n      createTime: Date.now(),\n      subscriptionOptions\n    };\n    yield dbSet(firebaseDependencies, tokenDetails);\n    return tokenDetails.token;\n  });\n  return _getNewToken.apply(this, arguments);\n}\nfunction getPushSubscription(_x19, _x20) {\n  return _getPushSubscription.apply(this, arguments);\n}\n/**\r\n * Checks if the saved tokenDetails object matches the configuration provided.\r\n */\nfunction _getPushSubscription() {\n  _getPushSubscription = _asyncToGenerator(function* (swRegistration, vapidKey) {\n    const subscription = yield swRegistration.pushManager.getSubscription();\n    if (subscription) {\n      return subscription;\n    }\n    return swRegistration.pushManager.subscribe({\n      userVisibleOnly: true,\n      // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n      // submitted to pushManager#subscribe must be of type Uint8Array.\n      applicationServerKey: base64ToArray(vapidKey)\n    });\n  });\n  return _getPushSubscription.apply(this, arguments);\n}\nfunction isTokenValid(dbOptions, currentOptions) {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction externalizePayload(internalPayload) {\n  const payload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  };\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n  return payload;\n}\nfunction propagateNotificationPayload(payload, messagePayloadInternal) {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n  payload.notification = {};\n  const title = messagePayloadInternal.notification.title;\n  if (!!title) {\n    payload.notification.title = title;\n  }\n  const body = messagePayloadInternal.notification.body;\n  if (!!body) {\n    payload.notification.body = body;\n  }\n  const image = messagePayloadInternal.notification.image;\n  if (!!image) {\n    payload.notification.image = image;\n  }\n  const icon = messagePayloadInternal.notification.icon;\n  if (!!icon) {\n    payload.notification.icon = icon;\n  }\n}\nfunction propagateDataPayload(payload, messagePayloadInternal) {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n  payload.data = messagePayloadInternal.data;\n}\nfunction propagateFcmOptions(payload, messagePayloadInternal) {\n  var _a, _b, _c, _d, _e;\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (!messagePayloadInternal.fcmOptions && !((_a = messagePayloadInternal.notification) === null || _a === void 0 ? void 0 : _a.click_action)) {\n    return;\n  }\n  payload.fcmOptions = {};\n  const link = (_c = (_b = messagePayloadInternal.fcmOptions) === null || _b === void 0 ? void 0 : _b.link) !== null && _c !== void 0 ? _c : (_d = messagePayloadInternal.notification) === null || _d === void 0 ? void 0 : _d.click_action;\n  if (!!link) {\n    payload.fcmOptions.link = link;\n  }\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = (_e = messagePayloadInternal.fcmOptions) === null || _e === void 0 ? void 0 : _e.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions.analyticsLabel = analyticsLabel;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction isConsoleMessage(data) {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/** Returns a promise that resolves after given time passes. */\nfunction sleep(ms) {\n  return new Promise(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n_mergeStrings('hts/frbslgigp.ogepscmv/ieo/eaylg', 'tp:/ieaeogn-agolai.o/1frlglgc/o');\n_mergeStrings('AzSCbw63g1R0nCw85jG8', 'Iaya3yLKwmgvh7cF0q4');\nfunction stageLog(_x21, _x22) {\n  return _stageLog.apply(this, arguments);\n}\nfunction _stageLog() {\n  _stageLog = _asyncToGenerator(function* (messaging, internalPayload) {\n    const fcmEvent = createFcmEvent(internalPayload, yield messaging.firebaseDependencies.installations.getId());\n    createAndEnqueueLogEvent(messaging, fcmEvent);\n  });\n  return _stageLog.apply(this, arguments);\n}\nfunction createFcmEvent(internalPayload, fid) {\n  var _a, _b;\n  const fcmEvent = {};\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n  fcmEvent.instance_id = fid;\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType$1.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType$1.DATA_MESSAGE.toString();\n  }\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n  if (!!((_a = internalPayload.fcmOptions) === null || _a === void 0 ? void 0 : _a.analytics_label)) {\n    fcmEvent.analytics_label = (_b = internalPayload.fcmOptions) === null || _b === void 0 ? void 0 : _b.analytics_label;\n  }\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\nfunction createAndEnqueueLogEvent(messaging, fcmEvent) {\n  const logEvent = {};\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify(fcmEvent);\n  // eslint-disable-next-line camelcase\n  messaging.logEvents.push(logEvent);\n}\nfunction _mergeStrings(s1, s2) {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n  return resultArray.join('');\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction onSubChange(_x23, _x24) {\n  return _onSubChange.apply(this, arguments);\n}\nfunction _onSubChange() {\n  _onSubChange = _asyncToGenerator(function* (event, messaging) {\n    var _a, _b;\n    const {\n      newSubscription\n    } = event;\n    if (!newSubscription) {\n      // Subscription revoked, delete token\n      yield deleteTokenInternal(messaging);\n      return;\n    }\n    const tokenDetails = yield dbGet(messaging.firebaseDependencies);\n    yield deleteTokenInternal(messaging);\n    messaging.vapidKey = (_b = (_a = tokenDetails === null || tokenDetails === void 0 ? void 0 : tokenDetails.subscriptionOptions) === null || _a === void 0 ? void 0 : _a.vapidKey) !== null && _b !== void 0 ? _b : DEFAULT_VAPID_KEY;\n    yield getTokenInternal(messaging);\n  });\n  return _onSubChange.apply(this, arguments);\n}\nfunction onPush(_x25, _x26) {\n  return _onPush.apply(this, arguments);\n}\nfunction _onPush() {\n  _onPush = _asyncToGenerator(function* (event, messaging) {\n    const internalPayload = getMessagePayloadInternal(event);\n    if (!internalPayload) {\n      // Failed to get parsed MessagePayload from the PushEvent. Skip handling the push.\n      return;\n    }\n    // log to Firelog with user consent\n    if (messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      yield stageLog(messaging, internalPayload);\n    }\n    // foreground handling: eventually passed to onMessage hook\n    const clientList = yield getClientList();\n    if (hasVisibleClients(clientList)) {\n      return sendMessagePayloadInternalToWindows(clientList, internalPayload);\n    }\n    // background handling: display if possible and pass to onBackgroundMessage hook\n    if (!!internalPayload.notification) {\n      yield showNotification(wrapInternalPayload(internalPayload));\n    }\n    if (!messaging) {\n      return;\n    }\n    if (!!messaging.onBackgroundMessageHandler) {\n      const payload = externalizePayload(internalPayload);\n      if (typeof messaging.onBackgroundMessageHandler === 'function') {\n        yield messaging.onBackgroundMessageHandler(payload);\n      } else {\n        messaging.onBackgroundMessageHandler.next(payload);\n      }\n    }\n  });\n  return _onPush.apply(this, arguments);\n}\nfunction onNotificationClick(_x27) {\n  return _onNotificationClick.apply(this, arguments);\n}\nfunction _onNotificationClick() {\n  _onNotificationClick = _asyncToGenerator(function* (event) {\n    var _a, _b;\n    const internalPayload = (_b = (_a = event.notification) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b[FCM_MSG];\n    if (!internalPayload) {\n      return;\n    } else if (event.action) {\n      // User clicked on an action button. This will allow developers to act on action button clicks\n      // by using a custom onNotificationClick listener that they define.\n      return;\n    }\n    // Prevent other listeners from receiving the event\n    event.stopImmediatePropagation();\n    event.notification.close();\n    // Note clicking on a notification with no link set will focus the Chrome's current tab.\n    const link = getLink(internalPayload);\n    if (!link) {\n      return;\n    }\n    // FM should only open/focus links from app's origin.\n    const url = new URL(link, self.location.href);\n    const originUrl = new URL(self.location.origin);\n    if (url.host !== originUrl.host) {\n      return;\n    }\n    let client = yield getWindowClient(url);\n    if (!client) {\n      client = yield self.clients.openWindow(link);\n      // Wait three seconds for the client to initialize and set up the message handler so that it\n      // can receive the message.\n      yield sleep(3000);\n    } else {\n      client = yield client.focus();\n    }\n    if (!client) {\n      // Window Client will not be returned if it's for a third party origin.\n      return;\n    }\n    internalPayload.messageType = MessageType.NOTIFICATION_CLICKED;\n    internalPayload.isFirebaseMessaging = true;\n    return client.postMessage(internalPayload);\n  });\n  return _onNotificationClick.apply(this, arguments);\n}\nfunction wrapInternalPayload(internalPayload) {\n  const wrappedInternalPayload = Object.assign({}, internalPayload.notification);\n  // Put the message payload under FCM_MSG name so we can identify the notification as being an FCM\n  // notification vs a notification from somewhere else (i.e. normal web push or developer generated\n  // notification).\n  wrappedInternalPayload.data = {\n    [FCM_MSG]: internalPayload\n  };\n  return wrappedInternalPayload;\n}\nfunction getMessagePayloadInternal({\n  data\n}) {\n  if (!data) {\n    return null;\n  }\n  try {\n    return data.json();\n  } catch (err) {\n    // Not JSON so not an FCM message.\n    return null;\n  }\n}\n/**\r\n * @param url The URL to look for when focusing a client.\r\n * @return Returns an existing window client or a newly opened WindowClient.\r\n */\nfunction getWindowClient(_x28) {\n  return _getWindowClient.apply(this, arguments);\n}\n/**\r\n * @returns If there is currently a visible WindowClient, this method will resolve to true,\r\n * otherwise false.\r\n */\nfunction _getWindowClient() {\n  _getWindowClient = _asyncToGenerator(function* (url) {\n    const clientList = yield getClientList();\n    for (const client of clientList) {\n      const clientUrl = new URL(client.url, self.location.href);\n      if (url.host === clientUrl.host) {\n        return client;\n      }\n    }\n    return null;\n  });\n  return _getWindowClient.apply(this, arguments);\n}\nfunction hasVisibleClients(clientList) {\n  return clientList.some(client => client.visibilityState === 'visible' &&\n  // Ignore chrome-extension clients as that matches the background pages of extensions, which\n  // are always considered visible for some reason.\n  !client.url.startsWith('chrome-extension://'));\n}\nfunction sendMessagePayloadInternalToWindows(clientList, internalPayload) {\n  internalPayload.isFirebaseMessaging = true;\n  internalPayload.messageType = MessageType.PUSH_RECEIVED;\n  for (const client of clientList) {\n    client.postMessage(internalPayload);\n  }\n}\nfunction getClientList() {\n  return self.clients.matchAll({\n    type: 'window',\n    includeUncontrolled: true\n    // TS doesn't know that \"type: 'window'\" means it'll return WindowClient[]\n  });\n}\n\nfunction showNotification(notificationPayloadInternal) {\n  var _a;\n  // Note: Firefox does not support the maxActions property.\n  // https://developer.mozilla.org/en-US/docs/Web/API/notification/maxActions\n  const {\n    actions\n  } = notificationPayloadInternal;\n  const {\n    maxActions\n  } = Notification;\n  if (actions && maxActions && actions.length > maxActions) {\n    console.warn(`This browser only supports ${maxActions} actions. The remaining actions will not be displayed.`);\n  }\n  return self.registration.showNotification( /* title= */(_a = notificationPayloadInternal.title) !== null && _a !== void 0 ? _a : '', notificationPayloadInternal);\n}\nfunction getLink(payload) {\n  var _a, _b, _c;\n  // eslint-disable-next-line camelcase\n  const link = (_b = (_a = payload.fcmOptions) === null || _a === void 0 ? void 0 : _a.link) !== null && _b !== void 0 ? _b : (_c = payload.notification) === null || _c === void 0 ? void 0 : _c.click_action;\n  if (link) {\n    return link;\n  }\n  if (isConsoleMessage(payload.data)) {\n    // Notification created in the Firebase Console. Redirect to origin.\n    return self.location.origin;\n  } else {\n    return null;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction extractAppConfig(app) {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n  // Required app config keys\n  const configKeys = ['projectId', 'apiKey', 'appId', 'messagingSenderId'];\n  const {\n    options\n  } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n  return {\n    appName: app.name,\n    projectId: options.projectId,\n    apiKey: options.apiKey,\n    appId: options.appId,\n    senderId: options.messagingSenderId\n  };\n}\nfunction getMissingValueError(valueName) {\n  return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\n    valueName\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass MessagingService {\n  constructor(app, installations, analyticsProvider) {\n    // logging is only done with end user consent. Default to false.\n    this.deliveryMetricsExportedToBigQueryEnabled = false;\n    this.onBackgroundMessageHandler = null;\n    this.onMessageHandler = null;\n    this.logEvents = [];\n    this.isLogServiceStarted = false;\n    const appConfig = extractAppConfig(app);\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n  _delete() {\n    return Promise.resolve();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst SwMessagingFactory = container => {\n  const messaging = new MessagingService(container.getProvider('app').getImmediate(), container.getProvider('installations-internal').getImmediate(), container.getProvider('analytics-internal'));\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n  return messaging;\n};\n/**\r\n * The messaging instance registered in sw is named differently than that of in client. This is\r\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\r\n * `messaging-compat` and component with the same name can only be registered once.\r\n */\nfunction registerMessagingInSw() {\n  _registerComponent(new Component('messaging-sw', SwMessagingFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Checks whether all required APIs exist within SW Context\r\n * @returns a Promise that resolves to a boolean.\r\n *\r\n * @public\r\n */\nfunction isSwSupported() {\n  return _isSwSupported.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _isSwSupported() {\n  _isSwSupported = _asyncToGenerator(function* () {\n    // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n    // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n    // instantiating phase, informing the developers to import/call isSupported for special handling.\n    return isIndexedDBAvailable() && (yield validateIndexedDBOpenable()) && 'PushManager' in self && 'Notification' in self && ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') && PushSubscription.prototype.hasOwnProperty('getKey');\n  });\n  return _isSwSupported.apply(this, arguments);\n}\nfunction onBackgroundMessage$1(messaging, nextOrObserver) {\n  if (self.document !== undefined) {\n    throw ERROR_FACTORY.create(\"only-available-in-sw\" /* ErrorCode.AVAILABLE_IN_SW */);\n  }\n\n  messaging.onBackgroundMessageHandler = nextOrObserver;\n  return () => {\n    messaging.onBackgroundMessageHandler = null;\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable) {\n  messaging.deliveryMetricsExportedToBigQueryEnabled = enable;\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Retrieves a Firebase Cloud Messaging instance.\r\n *\r\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\r\n *\r\n * @public\r\n */\nfunction getMessagingInSw(app = getApp()) {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(isSupported => {\n    // If `isSwSupported()` resolved, but returned false.\n    if (!isSupported) {\n      throw ERROR_FACTORY.create(\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */);\n    }\n  }, _ => {\n    // If `isSwSupported()` rejected.\n    throw ERROR_FACTORY.create(\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */);\n  });\n\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n/**\r\n * Called when a message is received while the app is in the background. An app is considered to be\r\n * in the background if no active window is displayed.\r\n *\r\n * @param messaging - The {@link Messaging} instance.\r\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\r\n * message is received and the app is currently in the background.\r\n *\r\n * @returns To stop listening for messages execute this returned function\r\n *\r\n * @public\r\n */\nfunction onBackgroundMessage(messaging, nextOrObserver) {\n  messaging = getModularInstance(messaging);\n  return onBackgroundMessage$1(messaging, nextOrObserver);\n}\n/**\r\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\r\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\r\n * disable the export at runtime.\r\n *\r\n * @param messaging - The `FirebaseMessaging` instance.\r\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\r\n * BigQuery.\r\n *\r\n * @public\r\n */\nfunction experimentalSetDeliveryMetricsExportedToBigQueryEnabled(messaging, enable) {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nregisterMessagingInSw();\nexport { experimentalSetDeliveryMetricsExportedToBigQueryEnabled, getMessagingInSw as getMessaging, isSwSupported as isSupported, onBackgroundMessage };", "map": {"version": 3, "names": ["Component", "openDB", "deleteDB", "ErrorFactory", "isIndexedDBAvailable", "validateIndexedDBOpenable", "getModularInstance", "_registerComponent", "_get<PERSON><PERSON><PERSON>", "getApp", "DEFAULT_VAPID_KEY", "ENDPOINT", "FCM_MSG", "CONSOLE_CAMPAIGN_ID", "SDK_PLATFORM_WEB", "EVENT_MESSAGE_DELIVERED", "MessageType$1", "MessageType", "arrayToBase64", "array", "uint8Array", "Uint8Array", "base64String", "btoa", "String", "fromCharCode", "replace", "base64ToArray", "padding", "repeat", "length", "base64", "rawData", "atob", "outputArray", "i", "charCodeAt", "OLD_DB_NAME", "OLD_DB_VERSION", "OLD_OBJECT_STORE_NAME", "migrateOldDatabase", "_x", "_migrateOldDatabase", "apply", "arguments", "_asyncToGenerator", "senderId", "indexedDB", "databases", "dbNames", "map", "db", "name", "includes", "tokenDetails", "upgrade", "_ref", "oldVersion", "newVersion", "upgradeTransaction", "_a", "objectStoreNames", "contains", "objectStore", "value", "index", "get", "clear", "oldDetails", "auth", "p256dh", "endpoint", "token", "fcmToken", "createTime", "Date", "now", "subscriptionOptions", "swScope", "vapid<PERSON>ey", "_x29", "_x30", "_x31", "_x32", "close", "checkTokenDetails", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgradeDb", "createObjectStore", "dbGet", "_x2", "_dbGet", "firebaseDependencies", "key", "<PERSON><PERSON><PERSON>", "transaction", "oldTokenDetails", "appConfig", "dbSet", "_x3", "_x4", "_dbSet", "tx", "put", "done", "db<PERSON><PERSON><PERSON>", "_x5", "_db<PERSON><PERSON><PERSON>", "delete", "appId", "ERROR_MAP", "ERROR_FACTORY", "requestGetToken", "_x6", "_x7", "_requestGetToken", "headers", "getHeaders", "body", "getBody", "subscribeOptions", "method", "JSON", "stringify", "responseData", "response", "fetch", "getEndpoint", "json", "err", "create", "errorInfo", "toString", "error", "message", "requestUpdateToken", "_x8", "_x9", "_requestUpdateToken", "updateOptions", "requestDeleteToken", "_x10", "_x11", "_requestDeleteToken", "unsubscribeOptions", "projectId", "_x12", "_getHeaders", "installations", "authToken", "getToken", "Headers", "Accept", "<PERSON><PERSON><PERSON><PERSON>", "web", "applicationPubKey", "TOKEN_EXPIRATION_MS", "getTokenInternal", "_x13", "_getTokenInternal", "messaging", "pushSubscription", "getPushSubscription", "swRegistration", "scope", "getNewToken", "isTokenValid", "e", "console", "warn", "updateToken", "deleteTokenInternal", "_x14", "_deleteTokenInternal", "pushManager", "getSubscription", "unsubscribe", "_x15", "_x16", "_updateToken", "updatedToken", "updatedTokenDetails", "Object", "assign", "_x17", "_x18", "_getNewToken", "_x19", "_x20", "_getPushSubscription", "subscription", "subscribe", "userVisibleOnly", "applicationServerKey", "dbOptions", "currentOptions", "isVapidKeyEqual", "isEndpointEqual", "isAuthEqual", "isP256dhEqual", "externalizePayload", "internalPayload", "payload", "from", "<PERSON><PERSON>ey", "collapse_key", "messageId", "fcmMessageId", "propagateNotificationPayload", "propagateDataPayload", "propagateFcmOptions", "messagePayloadInternal", "notification", "title", "image", "icon", "data", "_b", "_c", "_d", "_e", "fcmOptions", "click_action", "link", "analyticsLabel", "analytics_label", "isConsoleMessage", "sleep", "ms", "Promise", "resolve", "setTimeout", "_mergeStrings", "stageLog", "_x21", "_x22", "_stageLog", "fcmEvent", "createFcmEvent", "getId", "createAndEnqueueLogEvent", "fid", "project_number", "message_id", "instance_id", "message_type", "DISPLAY_NOTIFICATION", "DATA_MESSAGE", "sdk_platform", "package_name", "self", "origin", "event", "logEvent", "event_time_ms", "Math", "floor", "source_extension_json_proto3", "logEvents", "push", "s1", "s2", "resultArray", "char<PERSON>t", "join", "onSubChange", "_x23", "_x24", "_onSubChange", "newSubscription", "onPush", "_x25", "_x26", "_onPush", "getMessagePayloadInternal", "deliveryMetricsExportedToBigQueryEnabled", "clientList", "getClientList", "hasVisibleClients", "sendMessagePayloadInternalToWindows", "showNotification", "wrapInternalPayload", "onBackgroundMessageHandler", "next", "onNotificationClick", "_x27", "_onNotificationClick", "action", "stopImmediatePropagation", "getLink", "url", "URL", "location", "href", "originUrl", "host", "client", "getWindowClient", "clients", "openWindow", "focus", "messageType", "NOTIFICATION_CLICKED", "isFirebaseMessaging", "postMessage", "wrappedInternalPayload", "_x28", "_getWindowClient", "clientUrl", "some", "visibilityState", "startsWith", "PUSH_RECEIVED", "matchAll", "type", "includeUncontrolled", "notificationPayloadInternal", "actions", "maxActions", "Notification", "registration", "extractAppConfig", "app", "options", "getMissingValueError", "config<PERSON><PERSON><PERSON>", "keyName", "appName", "messagingSenderId", "valueName", "MessagingService", "constructor", "analyticsProvider", "onMessageHandler", "isLogServiceStarted", "_delete", "SwMessagingFactory", "container", "get<PERSON><PERSON><PERSON>", "getImmediate", "addEventListener", "waitUntil", "registerMessagingInSw", "isSwSupported", "_isSwSupported", "ServiceWorkerRegistration", "prototype", "hasOwnProperty", "PushSubscription", "onBackgroundMessage$1", "nextOrObserver", "document", "undefined", "_setDeliveryMetricsExportedToBigQueryEnabled", "enable", "getMessagingInSw", "then", "isSupported", "_", "onBackgroundMessage", "experimentalSetDeliveryMetricsExportedToBigQueryEnabled", "getMessaging"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular/fire/node_modules/@firebase/messaging/dist/esm/index.sw.esm2017.js"], "sourcesContent": ["import '@firebase/installations';\nimport { Component } from '@firebase/component';\nimport { openDB, deleteDB } from 'idb';\nimport { ErrorFactory, isIndexedDBAvailable, validateIndexedDBOpenable, getModularInstance } from '@firebase/util';\nimport { _registerComponent, _getProvider, getApp } from '@firebase/app';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DEFAULT_VAPID_KEY = 'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\r\nconst ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\r\n/** Key of FCM Payload in Notification's data field. */\r\nconst FCM_MSG = 'FCM_MSG';\r\nconst CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\r\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\r\nconst SDK_PLATFORM_WEB = 3;\r\nconst EVENT_MESSAGE_DELIVERED = 1;\r\nvar MessageType$1;\r\n(function (MessageType) {\r\n    MessageType[MessageType[\"DATA_MESSAGE\"] = 1] = \"DATA_MESSAGE\";\r\n    MessageType[MessageType[\"DISPLAY_NOTIFICATION\"] = 3] = \"DISPLAY_NOTIFICATION\";\r\n})(MessageType$1 || (MessageType$1 = {}));\n\n/**\r\n * @license\r\n * Copyright 2018 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\r\n * in compliance with the License. You may obtain a copy of the License at\r\n *\r\n * http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software distributed under the License\r\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\r\n * or implied. See the License for the specific language governing permissions and limitations under\r\n * the License.\r\n */\r\nvar MessageType;\r\n(function (MessageType) {\r\n    MessageType[\"PUSH_RECEIVED\"] = \"push-received\";\r\n    MessageType[\"NOTIFICATION_CLICKED\"] = \"notification-clicked\";\r\n})(MessageType || (MessageType = {}));\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction arrayToBase64(array) {\r\n    const uint8Array = new Uint8Array(array);\r\n    const base64String = btoa(String.fromCharCode(...uint8Array));\r\n    return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\r\n}\r\nfunction base64ToArray(base64String) {\r\n    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\r\n    const base64 = (base64String + padding)\r\n        .replace(/\\-/g, '+')\r\n        .replace(/_/g, '/');\r\n    const rawData = atob(base64);\r\n    const outputArray = new Uint8Array(rawData.length);\r\n    for (let i = 0; i < rawData.length; ++i) {\r\n        outputArray[i] = rawData.charCodeAt(i);\r\n    }\r\n    return outputArray;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst OLD_DB_NAME = 'fcm_token_details_db';\r\n/**\r\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\r\n * callback is called for all versions of the old DB.\r\n */\r\nconst OLD_DB_VERSION = 5;\r\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\r\nasync function migrateOldDatabase(senderId) {\r\n    if ('databases' in indexedDB) {\r\n        // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\r\n        // typecast when it lands in TS types.\r\n        const databases = await indexedDB.databases();\r\n        const dbNames = databases.map(db => db.name);\r\n        if (!dbNames.includes(OLD_DB_NAME)) {\r\n            // old DB didn't exist, no need to open.\r\n            return null;\r\n        }\r\n    }\r\n    let tokenDetails = null;\r\n    const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\r\n        upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\r\n            var _a;\r\n            if (oldVersion < 2) {\r\n                // Database too old, skip migration.\r\n                return;\r\n            }\r\n            if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\r\n                // Database did not exist. Nothing to do.\r\n                return;\r\n            }\r\n            const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\r\n            const value = await objectStore.index('fcmSenderId').get(senderId);\r\n            await objectStore.clear();\r\n            if (!value) {\r\n                // No entry in the database, nothing to migrate.\r\n                return;\r\n            }\r\n            if (oldVersion === 2) {\r\n                const oldDetails = value;\r\n                if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\r\n                    return;\r\n                }\r\n                tokenDetails = {\r\n                    token: oldDetails.fcmToken,\r\n                    createTime: (_a = oldDetails.createTime) !== null && _a !== void 0 ? _a : Date.now(),\r\n                    subscriptionOptions: {\r\n                        auth: oldDetails.auth,\r\n                        p256dh: oldDetails.p256dh,\r\n                        endpoint: oldDetails.endpoint,\r\n                        swScope: oldDetails.swScope,\r\n                        vapidKey: typeof oldDetails.vapidKey === 'string'\r\n                            ? oldDetails.vapidKey\r\n                            : arrayToBase64(oldDetails.vapidKey)\r\n                    }\r\n                };\r\n            }\r\n            else if (oldVersion === 3) {\r\n                const oldDetails = value;\r\n                tokenDetails = {\r\n                    token: oldDetails.fcmToken,\r\n                    createTime: oldDetails.createTime,\r\n                    subscriptionOptions: {\r\n                        auth: arrayToBase64(oldDetails.auth),\r\n                        p256dh: arrayToBase64(oldDetails.p256dh),\r\n                        endpoint: oldDetails.endpoint,\r\n                        swScope: oldDetails.swScope,\r\n                        vapidKey: arrayToBase64(oldDetails.vapidKey)\r\n                    }\r\n                };\r\n            }\r\n            else if (oldVersion === 4) {\r\n                const oldDetails = value;\r\n                tokenDetails = {\r\n                    token: oldDetails.fcmToken,\r\n                    createTime: oldDetails.createTime,\r\n                    subscriptionOptions: {\r\n                        auth: arrayToBase64(oldDetails.auth),\r\n                        p256dh: arrayToBase64(oldDetails.p256dh),\r\n                        endpoint: oldDetails.endpoint,\r\n                        swScope: oldDetails.swScope,\r\n                        vapidKey: arrayToBase64(oldDetails.vapidKey)\r\n                    }\r\n                };\r\n            }\r\n        }\r\n    });\r\n    db.close();\r\n    // Delete all old databases.\r\n    await deleteDB(OLD_DB_NAME);\r\n    await deleteDB('fcm_vapid_details_db');\r\n    await deleteDB('undefined');\r\n    return checkTokenDetails(tokenDetails) ? tokenDetails : null;\r\n}\r\nfunction checkTokenDetails(tokenDetails) {\r\n    if (!tokenDetails || !tokenDetails.subscriptionOptions) {\r\n        return false;\r\n    }\r\n    const { subscriptionOptions } = tokenDetails;\r\n    return (typeof tokenDetails.createTime === 'number' &&\r\n        tokenDetails.createTime > 0 &&\r\n        typeof tokenDetails.token === 'string' &&\r\n        tokenDetails.token.length > 0 &&\r\n        typeof subscriptionOptions.auth === 'string' &&\r\n        subscriptionOptions.auth.length > 0 &&\r\n        typeof subscriptionOptions.p256dh === 'string' &&\r\n        subscriptionOptions.p256dh.length > 0 &&\r\n        typeof subscriptionOptions.endpoint === 'string' &&\r\n        subscriptionOptions.endpoint.length > 0 &&\r\n        typeof subscriptionOptions.swScope === 'string' &&\r\n        subscriptionOptions.swScope.length > 0 &&\r\n        typeof subscriptionOptions.vapidKey === 'string' &&\r\n        subscriptionOptions.vapidKey.length > 0);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// Exported for tests.\r\nconst DATABASE_NAME = 'firebase-messaging-database';\r\nconst DATABASE_VERSION = 1;\r\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\r\nlet dbPromise = null;\r\nfunction getDbPromise() {\r\n    if (!dbPromise) {\r\n        dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\r\n            upgrade: (upgradeDb, oldVersion) => {\r\n                // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\r\n                // because if there are multiple versions between the old version and the current version, we\r\n                // want ALL the migrations that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (oldVersion) {\r\n                    case 0:\r\n                        upgradeDb.createObjectStore(OBJECT_STORE_NAME);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return dbPromise;\r\n}\r\n/** Gets record(s) from the objectStore that match the given key. */\r\nasync function dbGet(firebaseDependencies) {\r\n    const key = getKey(firebaseDependencies);\r\n    const db = await getDbPromise();\r\n    const tokenDetails = (await db\r\n        .transaction(OBJECT_STORE_NAME)\r\n        .objectStore(OBJECT_STORE_NAME)\r\n        .get(key));\r\n    if (tokenDetails) {\r\n        return tokenDetails;\r\n    }\r\n    else {\r\n        // Check if there is a tokenDetails object in the old DB.\r\n        const oldTokenDetails = await migrateOldDatabase(firebaseDependencies.appConfig.senderId);\r\n        if (oldTokenDetails) {\r\n            await dbSet(firebaseDependencies, oldTokenDetails);\r\n            return oldTokenDetails;\r\n        }\r\n    }\r\n}\r\n/** Assigns or overwrites the record for the given key with the given value. */\r\nasync function dbSet(firebaseDependencies, tokenDetails) {\r\n    const key = getKey(firebaseDependencies);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\r\n    await tx.done;\r\n    return tokenDetails;\r\n}\r\n/** Removes record(s) from the objectStore that match the given key. */\r\nasync function dbRemove(firebaseDependencies) {\r\n    const key = getKey(firebaseDependencies);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    await tx.objectStore(OBJECT_STORE_NAME).delete(key);\r\n    await tx.done;\r\n}\r\nfunction getKey({ appConfig }) {\r\n    return appConfig.appId;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERROR_MAP = {\r\n    [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\r\n    [\"only-available-in-window\" /* ErrorCode.AVAILABLE_IN_WINDOW */]: 'This method is available in a Window context.',\r\n    [\"only-available-in-sw\" /* ErrorCode.AVAILABLE_IN_SW */]: 'This method is available in a service worker context.',\r\n    [\"permission-default\" /* ErrorCode.PERMISSION_DEFAULT */]: 'The notification permission was not granted and dismissed instead.',\r\n    [\"permission-blocked\" /* ErrorCode.PERMISSION_BLOCKED */]: 'The notification permission was not granted and blocked instead.',\r\n    [\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */]: \"This browser doesn't support the API's required to use the Firebase SDK.\",\r\n    [\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */]: \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\r\n    [\"failed-service-worker-registration\" /* ErrorCode.FAILED_DEFAULT_REGISTRATION */]: 'We are unable to register the default service worker. {$browserErrorMessage}',\r\n    [\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */]: 'A problem occurred while subscribing the user to FCM: {$errorInfo}',\r\n    [\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */]: 'FCM returned no token when subscribing the user to push.',\r\n    [\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */]: 'A problem occurred while unsubscribing the ' +\r\n        'user from FCM: {$errorInfo}',\r\n    [\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */]: 'A problem occurred while updating the user from FCM: {$errorInfo}',\r\n    [\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */]: 'FCM returned no token when updating the user to push.',\r\n    [\"use-sw-after-get-token\" /* ErrorCode.USE_SW_AFTER_GET_TOKEN */]: 'The useServiceWorker() method may only be called once and must be ' +\r\n        'called before calling getToken() to ensure your service worker is used.',\r\n    [\"invalid-sw-registration\" /* ErrorCode.INVALID_SW_REGISTRATION */]: 'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\r\n    [\"invalid-bg-handler\" /* ErrorCode.INVALID_BG_HANDLER */]: 'The input to setBackgroundMessageHandler() must be a function.',\r\n    [\"invalid-vapid-key\" /* ErrorCode.INVALID_VAPID_KEY */]: 'The public VAPID key must be a string.',\r\n    [\"use-vapid-key-after-get-token\" /* ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN */]: 'The usePublicVapidKey() method may only be called once and must be ' +\r\n        'called before calling getToken() to ensure your VAPID key is used.'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('messaging', 'Messaging', ERROR_MAP);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function requestGetToken(firebaseDependencies, subscriptionOptions) {\r\n    const headers = await getHeaders(firebaseDependencies);\r\n    const body = getBody(subscriptionOptions);\r\n    const subscribeOptions = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    let responseData;\r\n    try {\r\n        const response = await fetch(getEndpoint(firebaseDependencies.appConfig), subscribeOptions);\r\n        responseData = await response.json();\r\n    }\r\n    catch (err) {\r\n        throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\r\n            errorInfo: err === null || err === void 0 ? void 0 : err.toString()\r\n        });\r\n    }\r\n    if (responseData.error) {\r\n        const message = responseData.error.message;\r\n        throw ERROR_FACTORY.create(\"token-subscribe-failed\" /* ErrorCode.TOKEN_SUBSCRIBE_FAILED */, {\r\n            errorInfo: message\r\n        });\r\n    }\r\n    if (!responseData.token) {\r\n        throw ERROR_FACTORY.create(\"token-subscribe-no-token\" /* ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN */);\r\n    }\r\n    return responseData.token;\r\n}\r\nasync function requestUpdateToken(firebaseDependencies, tokenDetails) {\r\n    const headers = await getHeaders(firebaseDependencies);\r\n    const body = getBody(tokenDetails.subscriptionOptions);\r\n    const updateOptions = {\r\n        method: 'PATCH',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    let responseData;\r\n    try {\r\n        const response = await fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`, updateOptions);\r\n        responseData = await response.json();\r\n    }\r\n    catch (err) {\r\n        throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\r\n            errorInfo: err === null || err === void 0 ? void 0 : err.toString()\r\n        });\r\n    }\r\n    if (responseData.error) {\r\n        const message = responseData.error.message;\r\n        throw ERROR_FACTORY.create(\"token-update-failed\" /* ErrorCode.TOKEN_UPDATE_FAILED */, {\r\n            errorInfo: message\r\n        });\r\n    }\r\n    if (!responseData.token) {\r\n        throw ERROR_FACTORY.create(\"token-update-no-token\" /* ErrorCode.TOKEN_UPDATE_NO_TOKEN */);\r\n    }\r\n    return responseData.token;\r\n}\r\nasync function requestDeleteToken(firebaseDependencies, token) {\r\n    const headers = await getHeaders(firebaseDependencies);\r\n    const unsubscribeOptions = {\r\n        method: 'DELETE',\r\n        headers\r\n    };\r\n    try {\r\n        const response = await fetch(`${getEndpoint(firebaseDependencies.appConfig)}/${token}`, unsubscribeOptions);\r\n        const responseData = await response.json();\r\n        if (responseData.error) {\r\n            const message = responseData.error.message;\r\n            throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\r\n                errorInfo: message\r\n            });\r\n        }\r\n    }\r\n    catch (err) {\r\n        throw ERROR_FACTORY.create(\"token-unsubscribe-failed\" /* ErrorCode.TOKEN_UNSUBSCRIBE_FAILED */, {\r\n            errorInfo: err === null || err === void 0 ? void 0 : err.toString()\r\n        });\r\n    }\r\n}\r\nfunction getEndpoint({ projectId }) {\r\n    return `${ENDPOINT}/projects/${projectId}/registrations`;\r\n}\r\nasync function getHeaders({ appConfig, installations }) {\r\n    const authToken = await installations.getToken();\r\n    return new Headers({\r\n        'Content-Type': 'application/json',\r\n        Accept: 'application/json',\r\n        'x-goog-api-key': appConfig.apiKey,\r\n        'x-goog-firebase-installations-auth': `FIS ${authToken}`\r\n    });\r\n}\r\nfunction getBody({ p256dh, auth, endpoint, vapidKey }) {\r\n    const body = {\r\n        web: {\r\n            endpoint,\r\n            auth,\r\n            p256dh\r\n        }\r\n    };\r\n    if (vapidKey !== DEFAULT_VAPID_KEY) {\r\n        body.web.applicationPubKey = vapidKey;\r\n    }\r\n    return body;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// UpdateRegistration will be called once every week.\r\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\r\nasync function getTokenInternal(messaging) {\r\n    const pushSubscription = await getPushSubscription(messaging.swRegistration, messaging.vapidKey);\r\n    const subscriptionOptions = {\r\n        vapidKey: messaging.vapidKey,\r\n        swScope: messaging.swRegistration.scope,\r\n        endpoint: pushSubscription.endpoint,\r\n        auth: arrayToBase64(pushSubscription.getKey('auth')),\r\n        p256dh: arrayToBase64(pushSubscription.getKey('p256dh'))\r\n    };\r\n    const tokenDetails = await dbGet(messaging.firebaseDependencies);\r\n    if (!tokenDetails) {\r\n        // No token, get a new one.\r\n        return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\r\n    }\r\n    else if (!isTokenValid(tokenDetails.subscriptionOptions, subscriptionOptions)) {\r\n        // Invalid token, get a new one.\r\n        try {\r\n            await requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\r\n        }\r\n        catch (e) {\r\n            // Suppress errors because of #2364\r\n            console.warn(e);\r\n        }\r\n        return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\r\n    }\r\n    else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\r\n        // Weekly token refresh\r\n        return updateToken(messaging, {\r\n            token: tokenDetails.token,\r\n            createTime: Date.now(),\r\n            subscriptionOptions\r\n        });\r\n    }\r\n    else {\r\n        // Valid token, nothing to do.\r\n        return tokenDetails.token;\r\n    }\r\n}\r\n/**\r\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\r\n * the push subscription if it exists.\r\n */\r\nasync function deleteTokenInternal(messaging) {\r\n    const tokenDetails = await dbGet(messaging.firebaseDependencies);\r\n    if (tokenDetails) {\r\n        await requestDeleteToken(messaging.firebaseDependencies, tokenDetails.token);\r\n        await dbRemove(messaging.firebaseDependencies);\r\n    }\r\n    // Unsubscribe from the push subscription.\r\n    const pushSubscription = await messaging.swRegistration.pushManager.getSubscription();\r\n    if (pushSubscription) {\r\n        return pushSubscription.unsubscribe();\r\n    }\r\n    // If there's no SW, consider it a success.\r\n    return true;\r\n}\r\nasync function updateToken(messaging, tokenDetails) {\r\n    try {\r\n        const updatedToken = await requestUpdateToken(messaging.firebaseDependencies, tokenDetails);\r\n        const updatedTokenDetails = Object.assign(Object.assign({}, tokenDetails), { token: updatedToken, createTime: Date.now() });\r\n        await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\r\n        return updatedToken;\r\n    }\r\n    catch (e) {\r\n        await deleteTokenInternal(messaging);\r\n        throw e;\r\n    }\r\n}\r\nasync function getNewToken(firebaseDependencies, subscriptionOptions) {\r\n    const token = await requestGetToken(firebaseDependencies, subscriptionOptions);\r\n    const tokenDetails = {\r\n        token,\r\n        createTime: Date.now(),\r\n        subscriptionOptions\r\n    };\r\n    await dbSet(firebaseDependencies, tokenDetails);\r\n    return tokenDetails.token;\r\n}\r\n/**\r\n * Gets a PushSubscription for the current user.\r\n */\r\nasync function getPushSubscription(swRegistration, vapidKey) {\r\n    const subscription = await swRegistration.pushManager.getSubscription();\r\n    if (subscription) {\r\n        return subscription;\r\n    }\r\n    return swRegistration.pushManager.subscribe({\r\n        userVisibleOnly: true,\r\n        // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\r\n        // submitted to pushManager#subscribe must be of type Uint8Array.\r\n        applicationServerKey: base64ToArray(vapidKey)\r\n    });\r\n}\r\n/**\r\n * Checks if the saved tokenDetails object matches the configuration provided.\r\n */\r\nfunction isTokenValid(dbOptions, currentOptions) {\r\n    const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\r\n    const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\r\n    const isAuthEqual = currentOptions.auth === dbOptions.auth;\r\n    const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\r\n    return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction externalizePayload(internalPayload) {\r\n    const payload = {\r\n        from: internalPayload.from,\r\n        // eslint-disable-next-line camelcase\r\n        collapseKey: internalPayload.collapse_key,\r\n        // eslint-disable-next-line camelcase\r\n        messageId: internalPayload.fcmMessageId\r\n    };\r\n    propagateNotificationPayload(payload, internalPayload);\r\n    propagateDataPayload(payload, internalPayload);\r\n    propagateFcmOptions(payload, internalPayload);\r\n    return payload;\r\n}\r\nfunction propagateNotificationPayload(payload, messagePayloadInternal) {\r\n    if (!messagePayloadInternal.notification) {\r\n        return;\r\n    }\r\n    payload.notification = {};\r\n    const title = messagePayloadInternal.notification.title;\r\n    if (!!title) {\r\n        payload.notification.title = title;\r\n    }\r\n    const body = messagePayloadInternal.notification.body;\r\n    if (!!body) {\r\n        payload.notification.body = body;\r\n    }\r\n    const image = messagePayloadInternal.notification.image;\r\n    if (!!image) {\r\n        payload.notification.image = image;\r\n    }\r\n    const icon = messagePayloadInternal.notification.icon;\r\n    if (!!icon) {\r\n        payload.notification.icon = icon;\r\n    }\r\n}\r\nfunction propagateDataPayload(payload, messagePayloadInternal) {\r\n    if (!messagePayloadInternal.data) {\r\n        return;\r\n    }\r\n    payload.data = messagePayloadInternal.data;\r\n}\r\nfunction propagateFcmOptions(payload, messagePayloadInternal) {\r\n    var _a, _b, _c, _d, _e;\r\n    // fcmOptions.link value is written into notification.click_action. see more in b/232072111\r\n    if (!messagePayloadInternal.fcmOptions &&\r\n        !((_a = messagePayloadInternal.notification) === null || _a === void 0 ? void 0 : _a.click_action)) {\r\n        return;\r\n    }\r\n    payload.fcmOptions = {};\r\n    const link = (_c = (_b = messagePayloadInternal.fcmOptions) === null || _b === void 0 ? void 0 : _b.link) !== null && _c !== void 0 ? _c : (_d = messagePayloadInternal.notification) === null || _d === void 0 ? void 0 : _d.click_action;\r\n    if (!!link) {\r\n        payload.fcmOptions.link = link;\r\n    }\r\n    // eslint-disable-next-line camelcase\r\n    const analyticsLabel = (_e = messagePayloadInternal.fcmOptions) === null || _e === void 0 ? void 0 : _e.analytics_label;\r\n    if (!!analyticsLabel) {\r\n        payload.fcmOptions.analyticsLabel = analyticsLabel;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction isConsoleMessage(data) {\r\n    // This message has a campaign ID, meaning it was sent using the Firebase Console.\r\n    return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a promise that resolves after given time passes. */\r\nfunction sleep(ms) {\r\n    return new Promise(resolve => {\r\n        setTimeout(resolve, ms);\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n_mergeStrings('hts/frbslgigp.ogepscmv/ieo/eaylg', 'tp:/ieaeogn-agolai.o/1frlglgc/o');\r\n_mergeStrings('AzSCbw63g1R0nCw85jG8', 'Iaya3yLKwmgvh7cF0q4');\r\nasync function stageLog(messaging, internalPayload) {\r\n    const fcmEvent = createFcmEvent(internalPayload, await messaging.firebaseDependencies.installations.getId());\r\n    createAndEnqueueLogEvent(messaging, fcmEvent);\r\n}\r\nfunction createFcmEvent(internalPayload, fid) {\r\n    var _a, _b;\r\n    const fcmEvent = {};\r\n    /* eslint-disable camelcase */\r\n    // some fields should always be non-null. Still check to ensure.\r\n    if (!!internalPayload.from) {\r\n        fcmEvent.project_number = internalPayload.from;\r\n    }\r\n    if (!!internalPayload.fcmMessageId) {\r\n        fcmEvent.message_id = internalPayload.fcmMessageId;\r\n    }\r\n    fcmEvent.instance_id = fid;\r\n    if (!!internalPayload.notification) {\r\n        fcmEvent.message_type = MessageType$1.DISPLAY_NOTIFICATION.toString();\r\n    }\r\n    else {\r\n        fcmEvent.message_type = MessageType$1.DATA_MESSAGE.toString();\r\n    }\r\n    fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\r\n    fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\r\n    if (!!internalPayload.collapse_key) {\r\n        fcmEvent.collapse_key = internalPayload.collapse_key;\r\n    }\r\n    fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\r\n    if (!!((_a = internalPayload.fcmOptions) === null || _a === void 0 ? void 0 : _a.analytics_label)) {\r\n        fcmEvent.analytics_label = (_b = internalPayload.fcmOptions) === null || _b === void 0 ? void 0 : _b.analytics_label;\r\n    }\r\n    /* eslint-enable camelcase */\r\n    return fcmEvent;\r\n}\r\nfunction createAndEnqueueLogEvent(messaging, fcmEvent) {\r\n    const logEvent = {};\r\n    /* eslint-disable camelcase */\r\n    logEvent.event_time_ms = Math.floor(Date.now()).toString();\r\n    logEvent.source_extension_json_proto3 = JSON.stringify(fcmEvent);\r\n    // eslint-disable-next-line camelcase\r\n    messaging.logEvents.push(logEvent);\r\n}\r\nfunction _mergeStrings(s1, s2) {\r\n    const resultArray = [];\r\n    for (let i = 0; i < s1.length; i++) {\r\n        resultArray.push(s1.charAt(i));\r\n        if (i < s2.length) {\r\n            resultArray.push(s2.charAt(i));\r\n        }\r\n    }\r\n    return resultArray.join('');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function onSubChange(event, messaging) {\r\n    var _a, _b;\r\n    const { newSubscription } = event;\r\n    if (!newSubscription) {\r\n        // Subscription revoked, delete token\r\n        await deleteTokenInternal(messaging);\r\n        return;\r\n    }\r\n    const tokenDetails = await dbGet(messaging.firebaseDependencies);\r\n    await deleteTokenInternal(messaging);\r\n    messaging.vapidKey =\r\n        (_b = (_a = tokenDetails === null || tokenDetails === void 0 ? void 0 : tokenDetails.subscriptionOptions) === null || _a === void 0 ? void 0 : _a.vapidKey) !== null && _b !== void 0 ? _b : DEFAULT_VAPID_KEY;\r\n    await getTokenInternal(messaging);\r\n}\r\nasync function onPush(event, messaging) {\r\n    const internalPayload = getMessagePayloadInternal(event);\r\n    if (!internalPayload) {\r\n        // Failed to get parsed MessagePayload from the PushEvent. Skip handling the push.\r\n        return;\r\n    }\r\n    // log to Firelog with user consent\r\n    if (messaging.deliveryMetricsExportedToBigQueryEnabled) {\r\n        await stageLog(messaging, internalPayload);\r\n    }\r\n    // foreground handling: eventually passed to onMessage hook\r\n    const clientList = await getClientList();\r\n    if (hasVisibleClients(clientList)) {\r\n        return sendMessagePayloadInternalToWindows(clientList, internalPayload);\r\n    }\r\n    // background handling: display if possible and pass to onBackgroundMessage hook\r\n    if (!!internalPayload.notification) {\r\n        await showNotification(wrapInternalPayload(internalPayload));\r\n    }\r\n    if (!messaging) {\r\n        return;\r\n    }\r\n    if (!!messaging.onBackgroundMessageHandler) {\r\n        const payload = externalizePayload(internalPayload);\r\n        if (typeof messaging.onBackgroundMessageHandler === 'function') {\r\n            await messaging.onBackgroundMessageHandler(payload);\r\n        }\r\n        else {\r\n            messaging.onBackgroundMessageHandler.next(payload);\r\n        }\r\n    }\r\n}\r\nasync function onNotificationClick(event) {\r\n    var _a, _b;\r\n    const internalPayload = (_b = (_a = event.notification) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b[FCM_MSG];\r\n    if (!internalPayload) {\r\n        return;\r\n    }\r\n    else if (event.action) {\r\n        // User clicked on an action button. This will allow developers to act on action button clicks\r\n        // by using a custom onNotificationClick listener that they define.\r\n        return;\r\n    }\r\n    // Prevent other listeners from receiving the event\r\n    event.stopImmediatePropagation();\r\n    event.notification.close();\r\n    // Note clicking on a notification with no link set will focus the Chrome's current tab.\r\n    const link = getLink(internalPayload);\r\n    if (!link) {\r\n        return;\r\n    }\r\n    // FM should only open/focus links from app's origin.\r\n    const url = new URL(link, self.location.href);\r\n    const originUrl = new URL(self.location.origin);\r\n    if (url.host !== originUrl.host) {\r\n        return;\r\n    }\r\n    let client = await getWindowClient(url);\r\n    if (!client) {\r\n        client = await self.clients.openWindow(link);\r\n        // Wait three seconds for the client to initialize and set up the message handler so that it\r\n        // can receive the message.\r\n        await sleep(3000);\r\n    }\r\n    else {\r\n        client = await client.focus();\r\n    }\r\n    if (!client) {\r\n        // Window Client will not be returned if it's for a third party origin.\r\n        return;\r\n    }\r\n    internalPayload.messageType = MessageType.NOTIFICATION_CLICKED;\r\n    internalPayload.isFirebaseMessaging = true;\r\n    return client.postMessage(internalPayload);\r\n}\r\nfunction wrapInternalPayload(internalPayload) {\r\n    const wrappedInternalPayload = Object.assign({}, internalPayload.notification);\r\n    // Put the message payload under FCM_MSG name so we can identify the notification as being an FCM\r\n    // notification vs a notification from somewhere else (i.e. normal web push or developer generated\r\n    // notification).\r\n    wrappedInternalPayload.data = {\r\n        [FCM_MSG]: internalPayload\r\n    };\r\n    return wrappedInternalPayload;\r\n}\r\nfunction getMessagePayloadInternal({ data }) {\r\n    if (!data) {\r\n        return null;\r\n    }\r\n    try {\r\n        return data.json();\r\n    }\r\n    catch (err) {\r\n        // Not JSON so not an FCM message.\r\n        return null;\r\n    }\r\n}\r\n/**\r\n * @param url The URL to look for when focusing a client.\r\n * @return Returns an existing window client or a newly opened WindowClient.\r\n */\r\nasync function getWindowClient(url) {\r\n    const clientList = await getClientList();\r\n    for (const client of clientList) {\r\n        const clientUrl = new URL(client.url, self.location.href);\r\n        if (url.host === clientUrl.host) {\r\n            return client;\r\n        }\r\n    }\r\n    return null;\r\n}\r\n/**\r\n * @returns If there is currently a visible WindowClient, this method will resolve to true,\r\n * otherwise false.\r\n */\r\nfunction hasVisibleClients(clientList) {\r\n    return clientList.some(client => client.visibilityState === 'visible' &&\r\n        // Ignore chrome-extension clients as that matches the background pages of extensions, which\r\n        // are always considered visible for some reason.\r\n        !client.url.startsWith('chrome-extension://'));\r\n}\r\nfunction sendMessagePayloadInternalToWindows(clientList, internalPayload) {\r\n    internalPayload.isFirebaseMessaging = true;\r\n    internalPayload.messageType = MessageType.PUSH_RECEIVED;\r\n    for (const client of clientList) {\r\n        client.postMessage(internalPayload);\r\n    }\r\n}\r\nfunction getClientList() {\r\n    return self.clients.matchAll({\r\n        type: 'window',\r\n        includeUncontrolled: true\r\n        // TS doesn't know that \"type: 'window'\" means it'll return WindowClient[]\r\n    });\r\n}\r\nfunction showNotification(notificationPayloadInternal) {\r\n    var _a;\r\n    // Note: Firefox does not support the maxActions property.\r\n    // https://developer.mozilla.org/en-US/docs/Web/API/notification/maxActions\r\n    const { actions } = notificationPayloadInternal;\r\n    const { maxActions } = Notification;\r\n    if (actions && maxActions && actions.length > maxActions) {\r\n        console.warn(`This browser only supports ${maxActions} actions. The remaining actions will not be displayed.`);\r\n    }\r\n    return self.registration.showNotification(\r\n    /* title= */ (_a = notificationPayloadInternal.title) !== null && _a !== void 0 ? _a : '', notificationPayloadInternal);\r\n}\r\nfunction getLink(payload) {\r\n    var _a, _b, _c;\r\n    // eslint-disable-next-line camelcase\r\n    const link = (_b = (_a = payload.fcmOptions) === null || _a === void 0 ? void 0 : _a.link) !== null && _b !== void 0 ? _b : (_c = payload.notification) === null || _c === void 0 ? void 0 : _c.click_action;\r\n    if (link) {\r\n        return link;\r\n    }\r\n    if (isConsoleMessage(payload.data)) {\r\n        // Notification created in the Firebase Console. Redirect to origin.\r\n        return self.location.origin;\r\n    }\r\n    else {\r\n        return null;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction extractAppConfig(app) {\r\n    if (!app || !app.options) {\r\n        throw getMissingValueError('App Configuration Object');\r\n    }\r\n    if (!app.name) {\r\n        throw getMissingValueError('App Name');\r\n    }\r\n    // Required app config keys\r\n    const configKeys = [\r\n        'projectId',\r\n        'apiKey',\r\n        'appId',\r\n        'messagingSenderId'\r\n    ];\r\n    const { options } = app;\r\n    for (const keyName of configKeys) {\r\n        if (!options[keyName]) {\r\n            throw getMissingValueError(keyName);\r\n        }\r\n    }\r\n    return {\r\n        appName: app.name,\r\n        projectId: options.projectId,\r\n        apiKey: options.apiKey,\r\n        appId: options.appId,\r\n        senderId: options.messagingSenderId\r\n    };\r\n}\r\nfunction getMissingValueError(valueName) {\r\n    return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\r\n        valueName\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass MessagingService {\r\n    constructor(app, installations, analyticsProvider) {\r\n        // logging is only done with end user consent. Default to false.\r\n        this.deliveryMetricsExportedToBigQueryEnabled = false;\r\n        this.onBackgroundMessageHandler = null;\r\n        this.onMessageHandler = null;\r\n        this.logEvents = [];\r\n        this.isLogServiceStarted = false;\r\n        const appConfig = extractAppConfig(app);\r\n        this.firebaseDependencies = {\r\n            app,\r\n            appConfig,\r\n            installations,\r\n            analyticsProvider\r\n        };\r\n    }\r\n    _delete() {\r\n        return Promise.resolve();\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst SwMessagingFactory = (container) => {\r\n    const messaging = new MessagingService(container.getProvider('app').getImmediate(), container.getProvider('installations-internal').getImmediate(), container.getProvider('analytics-internal'));\r\n    self.addEventListener('push', e => {\r\n        e.waitUntil(onPush(e, messaging));\r\n    });\r\n    self.addEventListener('pushsubscriptionchange', e => {\r\n        e.waitUntil(onSubChange(e, messaging));\r\n    });\r\n    self.addEventListener('notificationclick', e => {\r\n        e.waitUntil(onNotificationClick(e));\r\n    });\r\n    return messaging;\r\n};\r\n/**\r\n * The messaging instance registered in sw is named differently than that of in client. This is\r\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\r\n * `messaging-compat` and component with the same name can only be registered once.\r\n */\r\nfunction registerMessagingInSw() {\r\n    _registerComponent(new Component('messaging-sw', SwMessagingFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Checks whether all required APIs exist within SW Context\r\n * @returns a Promise that resolves to a boolean.\r\n *\r\n * @public\r\n */\r\nasync function isSwSupported() {\r\n    // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\r\n    // might be prohibited to run. In these contexts, an error would be thrown during the messaging\r\n    // instantiating phase, informing the developers to import/call isSupported for special handling.\r\n    return (isIndexedDBAvailable() &&\r\n        (await validateIndexedDBOpenable()) &&\r\n        'PushManager' in self &&\r\n        'Notification' in self &&\r\n        ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\r\n        PushSubscription.prototype.hasOwnProperty('getKey'));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction onBackgroundMessage$1(messaging, nextOrObserver) {\r\n    if (self.document !== undefined) {\r\n        throw ERROR_FACTORY.create(\"only-available-in-sw\" /* ErrorCode.AVAILABLE_IN_SW */);\r\n    }\r\n    messaging.onBackgroundMessageHandler = nextOrObserver;\r\n    return () => {\r\n        messaging.onBackgroundMessageHandler = null;\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable) {\r\n    messaging.deliveryMetricsExportedToBigQueryEnabled =\r\n        enable;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Retrieves a Firebase Cloud Messaging instance.\r\n *\r\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\r\n *\r\n * @public\r\n */\r\nfunction getMessagingInSw(app = getApp()) {\r\n    // Conscious decision to make this async check non-blocking during the messaging instance\r\n    // initialization phase for performance consideration. An error would be thrown latter for\r\n    // developer's information. Developers can then choose to import and call `isSupported` for\r\n    // special handling.\r\n    isSwSupported().then(isSupported => {\r\n        // If `isSwSupported()` resolved, but returned false.\r\n        if (!isSupported) {\r\n            throw ERROR_FACTORY.create(\"unsupported-browser\" /* ErrorCode.UNSUPPORTED_BROWSER */);\r\n        }\r\n    }, _ => {\r\n        // If `isSwSupported()` rejected.\r\n        throw ERROR_FACTORY.create(\"indexed-db-unsupported\" /* ErrorCode.INDEXED_DB_UNSUPPORTED */);\r\n    });\r\n    return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\r\n}\r\n/**\r\n * Called when a message is received while the app is in the background. An app is considered to be\r\n * in the background if no active window is displayed.\r\n *\r\n * @param messaging - The {@link Messaging} instance.\r\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\r\n * message is received and the app is currently in the background.\r\n *\r\n * @returns To stop listening for messages execute this returned function\r\n *\r\n * @public\r\n */\r\nfunction onBackgroundMessage(messaging, nextOrObserver) {\r\n    messaging = getModularInstance(messaging);\r\n    return onBackgroundMessage$1(messaging, nextOrObserver);\r\n}\r\n/**\r\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\r\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\r\n * disable the export at runtime.\r\n *\r\n * @param messaging - The `FirebaseMessaging` instance.\r\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\r\n * BigQuery.\r\n *\r\n * @public\r\n */\r\nfunction experimentalSetDeliveryMetricsExportedToBigQueryEnabled(messaging, enable) {\r\n    messaging = getModularInstance(messaging);\r\n    return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nregisterMessagingInSw();\n\nexport { experimentalSetDeliveryMetricsExportedToBigQueryEnabled, getMessagingInSw as getMessaging, isSwSupported as isSupported, onBackgroundMessage };\n"], "mappings": ";AAAA,OAAO,yBAAyB;AAChC,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,MAAM,EAAEC,QAAQ,QAAQ,KAAK;AACtC,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClH,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,yFAAyF;AACnH,MAAMC,QAAQ,GAAG,4CAA4C;AAC7D;AACA,MAAMC,OAAO,GAAG,SAAS;AACzB,MAAMC,mBAAmB,GAAG,iBAAiB;AAC7C;AACA,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,uBAAuB,GAAG,CAAC;AACjC,IAAIC,aAAa;AACjB,CAAC,UAAUC,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EAC7DA,WAAW,CAACA,WAAW,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB;AACjF,CAAC,EAAED,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAAC,eAAe,CAAC,GAAG,eAAe;EAC9CA,WAAW,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;AAChE,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,KAAK,CAAC;EACxC,MAAMG,YAAY,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAGL,UAAU,CAAC,CAAC;EAC7D,OAAOE,YAAY,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACjF;AACA,SAASC,aAAaA,CAACL,YAAY,EAAE;EACjC,MAAMM,OAAO,GAAG,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,GAAIP,YAAY,CAACQ,MAAM,GAAG,CAAE,IAAI,CAAC,CAAC;EAC/D,MAAMC,MAAM,GAAG,CAACT,YAAY,GAAGM,OAAO,EACjCF,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACvB,MAAMM,OAAO,GAAGC,IAAI,CAACF,MAAM,CAAC;EAC5B,MAAMG,WAAW,GAAG,IAAIb,UAAU,CAACW,OAAO,CAACF,MAAM,CAAC;EAClD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACF,MAAM,EAAE,EAAEK,CAAC,EAAE;IACrCD,WAAW,CAACC,CAAC,CAAC,GAAGH,OAAO,CAACI,UAAU,CAACD,CAAC,CAAC;EAC1C;EACA,OAAOD,WAAW;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,GAAG,sBAAsB;AAC1C;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,qBAAqB,GAAG,wBAAwB;AAAC,SACxCC,kBAAkBA,CAAAC,EAAA;EAAA,OAAAC,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,oBAAA;EAAAA,mBAAA,GAAAG,iBAAA,CAAjC,WAAkCC,QAAQ,EAAE;IACxC,IAAI,WAAW,IAAIC,SAAS,EAAE;MAC1B;MACA;MACA,MAAMC,SAAS,SAASD,SAAS,CAACC,SAAS,CAAC,CAAC;MAC7C,MAAMC,OAAO,GAAGD,SAAS,CAACE,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,IAAI,CAAC;MAC5C,IAAI,CAACH,OAAO,CAACI,QAAQ,CAAChB,WAAW,CAAC,EAAE;QAChC;QACA,OAAO,IAAI;MACf;IACJ;IACA,IAAIiB,YAAY,GAAG,IAAI;IACvB,MAAMH,EAAE,SAASlD,MAAM,CAACoC,WAAW,EAAEC,cAAc,EAAE;MACjDiB,OAAO;QAAA,IAAAC,IAAA,GAAAX,iBAAA,CAAE,WAAOM,EAAE,EAAEM,UAAU,EAAEC,UAAU,EAAEC,kBAAkB,EAAK;UAC/D,IAAIC,EAAE;UACN,IAAIH,UAAU,GAAG,CAAC,EAAE;YAChB;YACA;UACJ;UACA,IAAI,CAACN,EAAE,CAACU,gBAAgB,CAACC,QAAQ,CAACvB,qBAAqB,CAAC,EAAE;YACtD;YACA;UACJ;UACA,MAAMwB,WAAW,GAAGJ,kBAAkB,CAACI,WAAW,CAACxB,qBAAqB,CAAC;UACzE,MAAMyB,KAAK,SAASD,WAAW,CAACE,KAAK,CAAC,aAAa,CAAC,CAACC,GAAG,CAACpB,QAAQ,CAAC;UAClE,MAAMiB,WAAW,CAACI,KAAK,CAAC,CAAC;UACzB,IAAI,CAACH,KAAK,EAAE;YACR;YACA;UACJ;UACA,IAAIP,UAAU,KAAK,CAAC,EAAE;YAClB,MAAMW,UAAU,GAAGJ,KAAK;YACxB,IAAI,CAACI,UAAU,CAACC,IAAI,IAAI,CAACD,UAAU,CAACE,MAAM,IAAI,CAACF,UAAU,CAACG,QAAQ,EAAE;cAChE;YACJ;YACAjB,YAAY,GAAG;cACXkB,KAAK,EAAEJ,UAAU,CAACK,QAAQ;cAC1BC,UAAU,EAAE,CAACd,EAAE,GAAGQ,UAAU,CAACM,UAAU,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC;cACpFC,mBAAmB,EAAE;gBACjBR,IAAI,EAAED,UAAU,CAACC,IAAI;gBACrBC,MAAM,EAAEF,UAAU,CAACE,MAAM;gBACzBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;gBAC7BO,OAAO,EAAEV,UAAU,CAACU,OAAO;gBAC3BC,QAAQ,EAAE,OAAOX,UAAU,CAACW,QAAQ,KAAK,QAAQ,GAC3CX,UAAU,CAACW,QAAQ,GACnB7D,aAAa,CAACkD,UAAU,CAACW,QAAQ;cAC3C;YACJ,CAAC;UACL,CAAC,MACI,IAAItB,UAAU,KAAK,CAAC,EAAE;YACvB,MAAMW,UAAU,GAAGJ,KAAK;YACxBV,YAAY,GAAG;cACXkB,KAAK,EAAEJ,UAAU,CAACK,QAAQ;cAC1BC,UAAU,EAAEN,UAAU,CAACM,UAAU;cACjCG,mBAAmB,EAAE;gBACjBR,IAAI,EAAEnD,aAAa,CAACkD,UAAU,CAACC,IAAI,CAAC;gBACpCC,MAAM,EAAEpD,aAAa,CAACkD,UAAU,CAACE,MAAM,CAAC;gBACxCC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;gBAC7BO,OAAO,EAAEV,UAAU,CAACU,OAAO;gBAC3BC,QAAQ,EAAE7D,aAAa,CAACkD,UAAU,CAACW,QAAQ;cAC/C;YACJ,CAAC;UACL,CAAC,MACI,IAAItB,UAAU,KAAK,CAAC,EAAE;YACvB,MAAMW,UAAU,GAAGJ,KAAK;YACxBV,YAAY,GAAG;cACXkB,KAAK,EAAEJ,UAAU,CAACK,QAAQ;cAC1BC,UAAU,EAAEN,UAAU,CAACM,UAAU;cACjCG,mBAAmB,EAAE;gBACjBR,IAAI,EAAEnD,aAAa,CAACkD,UAAU,CAACC,IAAI,CAAC;gBACpCC,MAAM,EAAEpD,aAAa,CAACkD,UAAU,CAACE,MAAM,CAAC;gBACxCC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;gBAC7BO,OAAO,EAAEV,UAAU,CAACU,OAAO;gBAC3BC,QAAQ,EAAE7D,aAAa,CAACkD,UAAU,CAACW,QAAQ;cAC/C;YACJ,CAAC;UACL;QACJ,CAAC;QAAA,gBAAAxB,QAAAyB,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;UAAA,OAAA3B,IAAA,CAAAb,KAAA,OAAAC,SAAA;QAAA;MAAA;IACL,CAAC,CAAC;IACFO,EAAE,CAACiC,KAAK,CAAC,CAAC;IACV;IACA,MAAMlF,QAAQ,CAACmC,WAAW,CAAC;IAC3B,MAAMnC,QAAQ,CAAC,sBAAsB,CAAC;IACtC,MAAMA,QAAQ,CAAC,WAAW,CAAC;IAC3B,OAAOmF,iBAAiB,CAAC/B,YAAY,CAAC,GAAGA,YAAY,GAAG,IAAI;EAChE,CAAC;EAAA,OAAAZ,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASyC,iBAAiBA,CAAC/B,YAAY,EAAE;EACrC,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACuB,mBAAmB,EAAE;IACpD,OAAO,KAAK;EAChB;EACA,MAAM;IAAEA;EAAoB,CAAC,GAAGvB,YAAY;EAC5C,OAAQ,OAAOA,YAAY,CAACoB,UAAU,KAAK,QAAQ,IAC/CpB,YAAY,CAACoB,UAAU,GAAG,CAAC,IAC3B,OAAOpB,YAAY,CAACkB,KAAK,KAAK,QAAQ,IACtClB,YAAY,CAACkB,KAAK,CAAC1C,MAAM,GAAG,CAAC,IAC7B,OAAO+C,mBAAmB,CAACR,IAAI,KAAK,QAAQ,IAC5CQ,mBAAmB,CAACR,IAAI,CAACvC,MAAM,GAAG,CAAC,IACnC,OAAO+C,mBAAmB,CAACP,MAAM,KAAK,QAAQ,IAC9CO,mBAAmB,CAACP,MAAM,CAACxC,MAAM,GAAG,CAAC,IACrC,OAAO+C,mBAAmB,CAACN,QAAQ,KAAK,QAAQ,IAChDM,mBAAmB,CAACN,QAAQ,CAACzC,MAAM,GAAG,CAAC,IACvC,OAAO+C,mBAAmB,CAACC,OAAO,KAAK,QAAQ,IAC/CD,mBAAmB,CAACC,OAAO,CAAChD,MAAM,GAAG,CAAC,IACtC,OAAO+C,mBAAmB,CAACE,QAAQ,KAAK,QAAQ,IAChDF,mBAAmB,CAACE,QAAQ,CAACjD,MAAM,GAAG,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwD,aAAa,GAAG,6BAA6B;AACnD,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,iBAAiB,GAAG,0BAA0B;AACpD,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAGxF,MAAM,CAACqF,aAAa,EAAEC,gBAAgB,EAAE;MAChDhC,OAAO,EAAEA,CAACoC,SAAS,EAAElC,UAAU,KAAK;QAChC;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACFkC,SAAS,CAACC,iBAAiB,CAACJ,iBAAiB,CAAC;QACtD;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAOC,SAAS;AACpB;AACA;AAAA,SACeI,KAAKA,CAAAC,GAAA;EAAA,OAAAC,MAAA,CAAApD,KAAA,OAAAC,SAAA;AAAA;AAmBpB;AAAA,SAAAmD,OAAA;EAAAA,MAAA,GAAAlD,iBAAA,CAnBA,WAAqBmD,oBAAoB,EAAE;IACvC,MAAMC,GAAG,GAAGC,MAAM,CAACF,oBAAoB,CAAC;IACxC,MAAM7C,EAAE,SAASuC,YAAY,CAAC,CAAC;IAC/B,MAAMpC,YAAY,SAAUH,EAAE,CACzBgD,WAAW,CAACX,iBAAiB,CAAC,CAC9BzB,WAAW,CAACyB,iBAAiB,CAAC,CAC9BtB,GAAG,CAAC+B,GAAG,CAAE;IACd,IAAI3C,YAAY,EAAE;MACd,OAAOA,YAAY;IACvB,CAAC,MACI;MACD;MACA,MAAM8C,eAAe,SAAS5D,kBAAkB,CAACwD,oBAAoB,CAACK,SAAS,CAACvD,QAAQ,CAAC;MACzF,IAAIsD,eAAe,EAAE;QACjB,MAAME,KAAK,CAACN,oBAAoB,EAAEI,eAAe,CAAC;QAClD,OAAOA,eAAe;MAC1B;IACJ;EACJ,CAAC;EAAA,OAAAL,MAAA,CAAApD,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEc0D,KAAKA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,MAAA,CAAA9D,KAAA,OAAAC,SAAA;AAAA;AAQpB;AAAA,SAAA6D,OAAA;EAAAA,MAAA,GAAA5D,iBAAA,CARA,WAAqBmD,oBAAoB,EAAE1C,YAAY,EAAE;IACrD,MAAM2C,GAAG,GAAGC,MAAM,CAACF,oBAAoB,CAAC;IACxC,MAAM7C,EAAE,SAASuC,YAAY,CAAC,CAAC;IAC/B,MAAMgB,EAAE,GAAGvD,EAAE,CAACgD,WAAW,CAACX,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMkB,EAAE,CAAC3C,WAAW,CAACyB,iBAAiB,CAAC,CAACmB,GAAG,CAACrD,YAAY,EAAE2C,GAAG,CAAC;IAC9D,MAAMS,EAAE,CAACE,IAAI;IACb,OAAOtD,YAAY;EACvB,CAAC;EAAA,OAAAmD,MAAA,CAAA9D,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEciE,QAAQA,CAAAC,GAAA;EAAA,OAAAC,SAAA,CAAApE,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmE,UAAA;EAAAA,SAAA,GAAAlE,iBAAA,CAAvB,WAAwBmD,oBAAoB,EAAE;IAC1C,MAAMC,GAAG,GAAGC,MAAM,CAACF,oBAAoB,CAAC;IACxC,MAAM7C,EAAE,SAASuC,YAAY,CAAC,CAAC;IAC/B,MAAMgB,EAAE,GAAGvD,EAAE,CAACgD,WAAW,CAACX,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMkB,EAAE,CAAC3C,WAAW,CAACyB,iBAAiB,CAAC,CAACwB,MAAM,CAACf,GAAG,CAAC;IACnD,MAAMS,EAAE,CAACE,IAAI;EACjB,CAAC;EAAA,OAAAG,SAAA,CAAApE,KAAA,OAAAC,SAAA;AAAA;AACD,SAASsD,MAAMA,CAAC;EAAEG;AAAU,CAAC,EAAE;EAC3B,OAAOA,SAAS,CAACY,KAAK;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG;EACd,CAAC,2BAA2B,CAAC,4CAA4C,iDAAiD;EAC1H,CAAC,0BAA0B,CAAC,sCAAsC,+CAA+C;EACjH,CAAC,sBAAsB,CAAC,kCAAkC,uDAAuD;EACjH,CAAC,oBAAoB,CAAC,qCAAqC,oEAAoE;EAC/H,CAAC,oBAAoB,CAAC,qCAAqC,kEAAkE;EAC7H,CAAC,qBAAqB,CAAC,sCAAsC,0EAA0E;EACvI,CAAC,wBAAwB,CAAC,yCAAyC,kGAAkG;EACrK,CAAC,oCAAoC,CAAC,8CAA8C,8EAA8E;EAClK,CAAC,wBAAwB,CAAC,yCAAyC,oEAAoE;EACvI,CAAC,0BAA0B,CAAC,2CAA2C,0DAA0D;EACjI,CAAC,0BAA0B,CAAC,2CAA2C,6CAA6C,GAChH,6BAA6B;EACjC,CAAC,qBAAqB,CAAC,sCAAsC,mEAAmE;EAChI,CAAC,uBAAuB,CAAC,wCAAwC,uDAAuD;EACxH,CAAC,wBAAwB,CAAC,yCAAyC,oEAAoE,GACnI,yEAAyE;EAC7E,CAAC,yBAAyB,CAAC,0CAA0C,sEAAsE;EAC3I,CAAC,oBAAoB,CAAC,qCAAqC,gEAAgE;EAC3H,CAAC,mBAAmB,CAAC,oCAAoC,wCAAwC;EACjG,CAAC,+BAA+B,CAAC,gDAAgD,qEAAqE,GAClJ;AACR,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIhH,YAAY,CAAC,WAAW,EAAE,WAAW,EAAE+G,SAAS,CAAC;;AAE3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeE,eAAeA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,gBAAA,CAAA5E,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA2E,iBAAA;EAAAA,gBAAA,GAAA1E,iBAAA,CAA9B,WAA+BmD,oBAAoB,EAAEnB,mBAAmB,EAAE;IACtE,MAAM2C,OAAO,SAASC,UAAU,CAACzB,oBAAoB,CAAC;IACtD,MAAM0B,IAAI,GAAGC,OAAO,CAAC9C,mBAAmB,CAAC;IACzC,MAAM+C,gBAAgB,GAAG;MACrBC,MAAM,EAAE,MAAM;MACdL,OAAO;MACPE,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI;IAC7B,CAAC;IACD,IAAIM,YAAY;IAChB,IAAI;MACA,MAAMC,QAAQ,SAASC,KAAK,CAACC,WAAW,CAACnC,oBAAoB,CAACK,SAAS,CAAC,EAAEuB,gBAAgB,CAAC;MAC3FI,YAAY,SAASC,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxC,CAAC,CACD,OAAOC,GAAG,EAAE;MACR,MAAMlB,aAAa,CAACmB,MAAM,CAAC,wBAAwB,CAAC,wCAAwC;QACxFC,SAAS,EAAEF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,QAAQ,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAIR,YAAY,CAACS,KAAK,EAAE;MACpB,MAAMC,OAAO,GAAGV,YAAY,CAACS,KAAK,CAACC,OAAO;MAC1C,MAAMvB,aAAa,CAACmB,MAAM,CAAC,wBAAwB,CAAC,wCAAwC;QACxFC,SAAS,EAAEG;MACf,CAAC,CAAC;IACN;IACA,IAAI,CAACV,YAAY,CAACxD,KAAK,EAAE;MACrB,MAAM2C,aAAa,CAACmB,MAAM,CAAC,0BAA0B,CAAC,wCAAwC,CAAC;IACnG;;IACA,OAAON,YAAY,CAACxD,KAAK;EAC7B,CAAC;EAAA,OAAA+C,gBAAA,CAAA5E,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc+F,kBAAkBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAnG,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAkG,oBAAA;EAAAA,mBAAA,GAAAjG,iBAAA,CAAjC,WAAkCmD,oBAAoB,EAAE1C,YAAY,EAAE;IAClE,MAAMkE,OAAO,SAASC,UAAU,CAACzB,oBAAoB,CAAC;IACtD,MAAM0B,IAAI,GAAGC,OAAO,CAACrE,YAAY,CAACuB,mBAAmB,CAAC;IACtD,MAAMkE,aAAa,GAAG;MAClBlB,MAAM,EAAE,OAAO;MACfL,OAAO;MACPE,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI;IAC7B,CAAC;IACD,IAAIM,YAAY;IAChB,IAAI;MACA,MAAMC,QAAQ,SAASC,KAAK,CAAE,GAAEC,WAAW,CAACnC,oBAAoB,CAACK,SAAS,CAAE,IAAG/C,YAAY,CAACkB,KAAM,EAAC,EAAEuE,aAAa,CAAC;MACnHf,YAAY,SAASC,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxC,CAAC,CACD,OAAOC,GAAG,EAAE;MACR,MAAMlB,aAAa,CAACmB,MAAM,CAAC,qBAAqB,CAAC,qCAAqC;QAClFC,SAAS,EAAEF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,QAAQ,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAIR,YAAY,CAACS,KAAK,EAAE;MACpB,MAAMC,OAAO,GAAGV,YAAY,CAACS,KAAK,CAACC,OAAO;MAC1C,MAAMvB,aAAa,CAACmB,MAAM,CAAC,qBAAqB,CAAC,qCAAqC;QAClFC,SAAS,EAAEG;MACf,CAAC,CAAC;IACN;IACA,IAAI,CAACV,YAAY,CAACxD,KAAK,EAAE;MACrB,MAAM2C,aAAa,CAACmB,MAAM,CAAC,uBAAuB,CAAC,qCAAqC,CAAC;IAC7F;;IACA,OAAON,YAAY,CAACxD,KAAK;EAC7B,CAAC;EAAA,OAAAsE,mBAAA,CAAAnG,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcoG,kBAAkBA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAAxG,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAuG,oBAAA;EAAAA,mBAAA,GAAAtG,iBAAA,CAAjC,WAAkCmD,oBAAoB,EAAExB,KAAK,EAAE;IAC3D,MAAMgD,OAAO,SAASC,UAAU,CAACzB,oBAAoB,CAAC;IACtD,MAAMoD,kBAAkB,GAAG;MACvBvB,MAAM,EAAE,QAAQ;MAChBL;IACJ,CAAC;IACD,IAAI;MACA,MAAMS,QAAQ,SAASC,KAAK,CAAE,GAAEC,WAAW,CAACnC,oBAAoB,CAACK,SAAS,CAAE,IAAG7B,KAAM,EAAC,EAAE4E,kBAAkB,CAAC;MAC3G,MAAMpB,YAAY,SAASC,QAAQ,CAACG,IAAI,CAAC,CAAC;MAC1C,IAAIJ,YAAY,CAACS,KAAK,EAAE;QACpB,MAAMC,OAAO,GAAGV,YAAY,CAACS,KAAK,CAACC,OAAO;QAC1C,MAAMvB,aAAa,CAACmB,MAAM,CAAC,0BAA0B,CAAC,0CAA0C;UAC5FC,SAAS,EAAEG;QACf,CAAC,CAAC;MACN;IACJ,CAAC,CACD,OAAOL,GAAG,EAAE;MACR,MAAMlB,aAAa,CAACmB,MAAM,CAAC,0BAA0B,CAAC,0CAA0C;QAC5FC,SAAS,EAAEF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,QAAQ,CAAC;MACtE,CAAC,CAAC;IACN;EACJ,CAAC;EAAA,OAAAW,mBAAA,CAAAxG,KAAA,OAAAC,SAAA;AAAA;AACD,SAASuF,WAAWA,CAAC;EAAEkB;AAAU,CAAC,EAAE;EAChC,OAAQ,GAAE1I,QAAS,aAAY0I,SAAU,gBAAe;AAC5D;AAAC,SACc5B,UAAUA,CAAA6B,IAAA;EAAA,OAAAC,WAAA,CAAA5G,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA2G,YAAA;EAAAA,WAAA,GAAA1G,iBAAA,CAAzB,WAA0B;IAAEwD,SAAS;IAAEmD;EAAc,CAAC,EAAE;IACpD,MAAMC,SAAS,SAASD,aAAa,CAACE,QAAQ,CAAC,CAAC;IAChD,OAAO,IAAIC,OAAO,CAAC;MACf,cAAc,EAAE,kBAAkB;MAClCC,MAAM,EAAE,kBAAkB;MAC1B,gBAAgB,EAAEvD,SAAS,CAACwD,MAAM;MAClC,oCAAoC,EAAG,OAAMJ,SAAU;IAC3D,CAAC,CAAC;EACN,CAAC;EAAA,OAAAF,WAAA,CAAA5G,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS+E,OAAOA,CAAC;EAAErD,MAAM;EAAED,IAAI;EAAEE,QAAQ;EAAEQ;AAAS,CAAC,EAAE;EACnD,MAAM2C,IAAI,GAAG;IACToC,GAAG,EAAE;MACDvF,QAAQ;MACRF,IAAI;MACJC;IACJ;EACJ,CAAC;EACD,IAAIS,QAAQ,KAAKrE,iBAAiB,EAAE;IAChCgH,IAAI,CAACoC,GAAG,CAACC,iBAAiB,GAAGhF,QAAQ;EACzC;EACA,OAAO2C,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAAA,SACtCC,gBAAgBA,CAAAC,IAAA;EAAA,OAAAC,iBAAA,CAAAxH,KAAA,OAAAC,SAAA;AAAA;AAsC/B;AACA;AACA;AACA;AAHA,SAAAuH,kBAAA;EAAAA,iBAAA,GAAAtH,iBAAA,CAtCA,WAAgCuH,SAAS,EAAE;IACvC,MAAMC,gBAAgB,SAASC,mBAAmB,CAACF,SAAS,CAACG,cAAc,EAAEH,SAAS,CAACrF,QAAQ,CAAC;IAChG,MAAMF,mBAAmB,GAAG;MACxBE,QAAQ,EAAEqF,SAAS,CAACrF,QAAQ;MAC5BD,OAAO,EAAEsF,SAAS,CAACG,cAAc,CAACC,KAAK;MACvCjG,QAAQ,EAAE8F,gBAAgB,CAAC9F,QAAQ;MACnCF,IAAI,EAAEnD,aAAa,CAACmJ,gBAAgB,CAACnE,MAAM,CAAC,MAAM,CAAC,CAAC;MACpD5B,MAAM,EAAEpD,aAAa,CAACmJ,gBAAgB,CAACnE,MAAM,CAAC,QAAQ,CAAC;IAC3D,CAAC;IACD,MAAM5C,YAAY,SAASuC,KAAK,CAACuE,SAAS,CAACpE,oBAAoB,CAAC;IAChE,IAAI,CAAC1C,YAAY,EAAE;MACf;MACA,OAAOmH,WAAW,CAACL,SAAS,CAACpE,oBAAoB,EAAEnB,mBAAmB,CAAC;IAC3E,CAAC,MACI,IAAI,CAAC6F,YAAY,CAACpH,YAAY,CAACuB,mBAAmB,EAAEA,mBAAmB,CAAC,EAAE;MAC3E;MACA,IAAI;QACA,MAAMmE,kBAAkB,CAACoB,SAAS,CAACpE,oBAAoB,EAAE1C,YAAY,CAACkB,KAAK,CAAC;MAChF,CAAC,CACD,OAAOmG,CAAC,EAAE;QACN;QACAC,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;MACnB;MACA,OAAOF,WAAW,CAACL,SAAS,CAACpE,oBAAoB,EAAEnB,mBAAmB,CAAC;IAC3E,CAAC,MACI,IAAIF,IAAI,CAACC,GAAG,CAAC,CAAC,IAAItB,YAAY,CAACoB,UAAU,GAAGsF,mBAAmB,EAAE;MAClE;MACA,OAAOc,WAAW,CAACV,SAAS,EAAE;QAC1B5F,KAAK,EAAElB,YAAY,CAACkB,KAAK;QACzBE,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACtBC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA,OAAOvB,YAAY,CAACkB,KAAK;IAC7B;EACJ,CAAC;EAAA,OAAA2F,iBAAA,CAAAxH,KAAA,OAAAC,SAAA;AAAA;AAAA,SAKcmI,mBAAmBA,CAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAAtI,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAqI,qBAAA;EAAAA,oBAAA,GAAApI,iBAAA,CAAlC,WAAmCuH,SAAS,EAAE;IAC1C,MAAM9G,YAAY,SAASuC,KAAK,CAACuE,SAAS,CAACpE,oBAAoB,CAAC;IAChE,IAAI1C,YAAY,EAAE;MACd,MAAM0F,kBAAkB,CAACoB,SAAS,CAACpE,oBAAoB,EAAE1C,YAAY,CAACkB,KAAK,CAAC;MAC5E,MAAMqC,QAAQ,CAACuD,SAAS,CAACpE,oBAAoB,CAAC;IAClD;IACA;IACA,MAAMqE,gBAAgB,SAASD,SAAS,CAACG,cAAc,CAACW,WAAW,CAACC,eAAe,CAAC,CAAC;IACrF,IAAId,gBAAgB,EAAE;MAClB,OAAOA,gBAAgB,CAACe,WAAW,CAAC,CAAC;IACzC;IACA;IACA,OAAO,IAAI;EACf,CAAC;EAAA,OAAAH,oBAAA,CAAAtI,KAAA,OAAAC,SAAA;AAAA;AAAA,SACckI,WAAWA,CAAAO,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAA5I,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA2I,aAAA;EAAAA,YAAA,GAAA1I,iBAAA,CAA1B,WAA2BuH,SAAS,EAAE9G,YAAY,EAAE;IAChD,IAAI;MACA,MAAMkI,YAAY,SAAS7C,kBAAkB,CAACyB,SAAS,CAACpE,oBAAoB,EAAE1C,YAAY,CAAC;MAC3F,MAAMmI,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErI,YAAY,CAAC,EAAE;QAAEkB,KAAK,EAAEgH,YAAY;QAAE9G,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC;MAAE,CAAC,CAAC;MAC3H,MAAM0B,KAAK,CAAC8D,SAAS,CAACpE,oBAAoB,EAAEyF,mBAAmB,CAAC;MAChE,OAAOD,YAAY;IACvB,CAAC,CACD,OAAOb,CAAC,EAAE;MACN,MAAMI,mBAAmB,CAACX,SAAS,CAAC;MACpC,MAAMO,CAAC;IACX;EACJ,CAAC;EAAA,OAAAY,YAAA,CAAA5I,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc6H,WAAWA,CAAAmB,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAAnJ,KAAA,OAAAC,SAAA;AAAA;AAU1B;AACA;AACA;AAFA,SAAAkJ,aAAA;EAAAA,YAAA,GAAAjJ,iBAAA,CAVA,WAA2BmD,oBAAoB,EAAEnB,mBAAmB,EAAE;IAClE,MAAML,KAAK,SAAS4C,eAAe,CAACpB,oBAAoB,EAAEnB,mBAAmB,CAAC;IAC9E,MAAMvB,YAAY,GAAG;MACjBkB,KAAK;MACLE,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACtBC;IACJ,CAAC;IACD,MAAMyB,KAAK,CAACN,oBAAoB,EAAE1C,YAAY,CAAC;IAC/C,OAAOA,YAAY,CAACkB,KAAK;EAC7B,CAAC;EAAA,OAAAsH,YAAA,CAAAnJ,KAAA,OAAAC,SAAA;AAAA;AAAA,SAIc0H,mBAAmBA,CAAAyB,IAAA,EAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAAtJ,KAAA,OAAAC,SAAA;AAAA;AAYlC;AACA;AACA;AAFA,SAAAqJ,qBAAA;EAAAA,oBAAA,GAAApJ,iBAAA,CAZA,WAAmC0H,cAAc,EAAExF,QAAQ,EAAE;IACzD,MAAMmH,YAAY,SAAS3B,cAAc,CAACW,WAAW,CAACC,eAAe,CAAC,CAAC;IACvE,IAAIe,YAAY,EAAE;MACd,OAAOA,YAAY;IACvB;IACA,OAAO3B,cAAc,CAACW,WAAW,CAACiB,SAAS,CAAC;MACxCC,eAAe,EAAE,IAAI;MACrB;MACA;MACAC,oBAAoB,EAAE1K,aAAa,CAACoD,QAAQ;IAChD,CAAC,CAAC;EACN,CAAC;EAAA,OAAAkH,oBAAA,CAAAtJ,KAAA,OAAAC,SAAA;AAAA;AAID,SAAS8H,YAAYA,CAAC4B,SAAS,EAAEC,cAAc,EAAE;EAC7C,MAAMC,eAAe,GAAGD,cAAc,CAACxH,QAAQ,KAAKuH,SAAS,CAACvH,QAAQ;EACtE,MAAM0H,eAAe,GAAGF,cAAc,CAAChI,QAAQ,KAAK+H,SAAS,CAAC/H,QAAQ;EACtE,MAAMmI,WAAW,GAAGH,cAAc,CAAClI,IAAI,KAAKiI,SAAS,CAACjI,IAAI;EAC1D,MAAMsI,aAAa,GAAGJ,cAAc,CAACjI,MAAM,KAAKgI,SAAS,CAAChI,MAAM;EAChE,OAAOkI,eAAe,IAAIC,eAAe,IAAIC,WAAW,IAAIC,aAAa;AAC7E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,eAAe,EAAE;EACzC,MAAMC,OAAO,GAAG;IACZC,IAAI,EAAEF,eAAe,CAACE,IAAI;IAC1B;IACAC,WAAW,EAAEH,eAAe,CAACI,YAAY;IACzC;IACAC,SAAS,EAAEL,eAAe,CAACM;EAC/B,CAAC;EACDC,4BAA4B,CAACN,OAAO,EAAED,eAAe,CAAC;EACtDQ,oBAAoB,CAACP,OAAO,EAAED,eAAe,CAAC;EAC9CS,mBAAmB,CAACR,OAAO,EAAED,eAAe,CAAC;EAC7C,OAAOC,OAAO;AAClB;AACA,SAASM,4BAA4BA,CAACN,OAAO,EAAES,sBAAsB,EAAE;EACnE,IAAI,CAACA,sBAAsB,CAACC,YAAY,EAAE;IACtC;EACJ;EACAV,OAAO,CAACU,YAAY,GAAG,CAAC,CAAC;EACzB,MAAMC,KAAK,GAAGF,sBAAsB,CAACC,YAAY,CAACC,KAAK;EACvD,IAAI,CAAC,CAACA,KAAK,EAAE;IACTX,OAAO,CAACU,YAAY,CAACC,KAAK,GAAGA,KAAK;EACtC;EACA,MAAM/F,IAAI,GAAG6F,sBAAsB,CAACC,YAAY,CAAC9F,IAAI;EACrD,IAAI,CAAC,CAACA,IAAI,EAAE;IACRoF,OAAO,CAACU,YAAY,CAAC9F,IAAI,GAAGA,IAAI;EACpC;EACA,MAAMgG,KAAK,GAAGH,sBAAsB,CAACC,YAAY,CAACE,KAAK;EACvD,IAAI,CAAC,CAACA,KAAK,EAAE;IACTZ,OAAO,CAACU,YAAY,CAACE,KAAK,GAAGA,KAAK;EACtC;EACA,MAAMC,IAAI,GAAGJ,sBAAsB,CAACC,YAAY,CAACG,IAAI;EACrD,IAAI,CAAC,CAACA,IAAI,EAAE;IACRb,OAAO,CAACU,YAAY,CAACG,IAAI,GAAGA,IAAI;EACpC;AACJ;AACA,SAASN,oBAAoBA,CAACP,OAAO,EAAES,sBAAsB,EAAE;EAC3D,IAAI,CAACA,sBAAsB,CAACK,IAAI,EAAE;IAC9B;EACJ;EACAd,OAAO,CAACc,IAAI,GAAGL,sBAAsB,CAACK,IAAI;AAC9C;AACA,SAASN,mBAAmBA,CAACR,OAAO,EAAES,sBAAsB,EAAE;EAC1D,IAAI3J,EAAE,EAAEiK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACtB;EACA,IAAI,CAACT,sBAAsB,CAACU,UAAU,IAClC,EAAE,CAACrK,EAAE,GAAG2J,sBAAsB,CAACC,YAAY,MAAM,IAAI,IAAI5J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsK,YAAY,CAAC,EAAE;IACpG;EACJ;EACApB,OAAO,CAACmB,UAAU,GAAG,CAAC,CAAC;EACvB,MAAME,IAAI,GAAG,CAACL,EAAE,GAAG,CAACD,EAAE,GAAGN,sBAAsB,CAACU,UAAU,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAACC,EAAE,GAAGR,sBAAsB,CAACC,YAAY,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,YAAY;EAC1O,IAAI,CAAC,CAACC,IAAI,EAAE;IACRrB,OAAO,CAACmB,UAAU,CAACE,IAAI,GAAGA,IAAI;EAClC;EACA;EACA,MAAMC,cAAc,GAAG,CAACJ,EAAE,GAAGT,sBAAsB,CAACU,UAAU,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,eAAe;EACvH,IAAI,CAAC,CAACD,cAAc,EAAE;IAClBtB,OAAO,CAACmB,UAAU,CAACG,cAAc,GAAGA,cAAc;EACtD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACV,IAAI,EAAE;EAC5B;EACA,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAAC,CAACA,IAAI,IAAI/M,mBAAmB,IAAI+M,IAAI;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,KAAKA,CAACC,EAAE,EAAE;EACf,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC1BC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC;EAC3B,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAI,aAAa,CAAC,kCAAkC,EAAE,iCAAiC,CAAC;AACpFA,aAAa,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;AAAC,SAC9CC,QAAQA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,SAAA,CAAArM,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAoM,UAAA;EAAAA,SAAA,GAAAnM,iBAAA,CAAvB,WAAwBuH,SAAS,EAAEyC,eAAe,EAAE;IAChD,MAAMoC,QAAQ,GAAGC,cAAc,CAACrC,eAAe,QAAQzC,SAAS,CAACpE,oBAAoB,CAACwD,aAAa,CAAC2F,KAAK,CAAC,CAAC,CAAC;IAC5GC,wBAAwB,CAAChF,SAAS,EAAE6E,QAAQ,CAAC;EACjD,CAAC;EAAA,OAAAD,SAAA,CAAArM,KAAA,OAAAC,SAAA;AAAA;AACD,SAASsM,cAAcA,CAACrC,eAAe,EAAEwC,GAAG,EAAE;EAC1C,IAAIzL,EAAE,EAAEiK,EAAE;EACV,MAAMoB,QAAQ,GAAG,CAAC,CAAC;EACnB;EACA;EACA,IAAI,CAAC,CAACpC,eAAe,CAACE,IAAI,EAAE;IACxBkC,QAAQ,CAACK,cAAc,GAAGzC,eAAe,CAACE,IAAI;EAClD;EACA,IAAI,CAAC,CAACF,eAAe,CAACM,YAAY,EAAE;IAChC8B,QAAQ,CAACM,UAAU,GAAG1C,eAAe,CAACM,YAAY;EACtD;EACA8B,QAAQ,CAACO,WAAW,GAAGH,GAAG;EAC1B,IAAI,CAAC,CAACxC,eAAe,CAACW,YAAY,EAAE;IAChCyB,QAAQ,CAACQ,YAAY,GAAGzO,aAAa,CAAC0O,oBAAoB,CAAClH,QAAQ,CAAC,CAAC;EACzE,CAAC,MACI;IACDyG,QAAQ,CAACQ,YAAY,GAAGzO,aAAa,CAAC2O,YAAY,CAACnH,QAAQ,CAAC,CAAC;EACjE;EACAyG,QAAQ,CAACW,YAAY,GAAG9O,gBAAgB,CAAC0H,QAAQ,CAAC,CAAC;EACnDyG,QAAQ,CAACY,YAAY,GAAGC,IAAI,CAACC,MAAM,CAACrO,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;EAChE,IAAI,CAAC,CAACmL,eAAe,CAACI,YAAY,EAAE;IAChCgC,QAAQ,CAAChC,YAAY,GAAGJ,eAAe,CAACI,YAAY;EACxD;EACAgC,QAAQ,CAACe,KAAK,GAAGjP,uBAAuB,CAACyH,QAAQ,CAAC,CAAC;EACnD,IAAI,CAAC,EAAE,CAAC5E,EAAE,GAAGiJ,eAAe,CAACoB,UAAU,MAAM,IAAI,IAAIrK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyK,eAAe,CAAC,EAAE;IAC/FY,QAAQ,CAACZ,eAAe,GAAG,CAACR,EAAE,GAAGhB,eAAe,CAACoB,UAAU,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,eAAe;EACxH;EACA;EACA,OAAOY,QAAQ;AACnB;AACA,SAASG,wBAAwBA,CAAChF,SAAS,EAAE6E,QAAQ,EAAE;EACnD,MAAMgB,QAAQ,GAAG,CAAC,CAAC;EACnB;EACAA,QAAQ,CAACC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACzL,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC4D,QAAQ,CAAC,CAAC;EAC1DyH,QAAQ,CAACI,4BAA4B,GAAGvI,IAAI,CAACC,SAAS,CAACkH,QAAQ,CAAC;EAChE;EACA7E,SAAS,CAACkG,SAAS,CAACC,IAAI,CAACN,QAAQ,CAAC;AACtC;AACA,SAASrB,aAAaA,CAAC4B,EAAE,EAAEC,EAAE,EAAE;EAC3B,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIvO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,EAAE,CAAC1O,MAAM,EAAEK,CAAC,EAAE,EAAE;IAChCuO,WAAW,CAACH,IAAI,CAACC,EAAE,CAACG,MAAM,CAACxO,CAAC,CAAC,CAAC;IAC9B,IAAIA,CAAC,GAAGsO,EAAE,CAAC3O,MAAM,EAAE;MACf4O,WAAW,CAACH,IAAI,CAACE,EAAE,CAACE,MAAM,CAACxO,CAAC,CAAC,CAAC;IAClC;EACJ;EACA,OAAOuO,WAAW,CAACE,IAAI,CAAC,EAAE,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeC,WAAWA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAArO,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAoO,aAAA;EAAAA,YAAA,GAAAnO,iBAAA,CAA1B,WAA2BmN,KAAK,EAAE5F,SAAS,EAAE;IACzC,IAAIxG,EAAE,EAAEiK,EAAE;IACV,MAAM;MAAEoD;IAAgB,CAAC,GAAGjB,KAAK;IACjC,IAAI,CAACiB,eAAe,EAAE;MAClB;MACA,MAAMlG,mBAAmB,CAACX,SAAS,CAAC;MACpC;IACJ;IACA,MAAM9G,YAAY,SAASuC,KAAK,CAACuE,SAAS,CAACpE,oBAAoB,CAAC;IAChE,MAAM+E,mBAAmB,CAACX,SAAS,CAAC;IACpCA,SAAS,CAACrF,QAAQ,GACd,CAAC8I,EAAE,GAAG,CAACjK,EAAE,GAAGN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACuB,mBAAmB,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,QAAQ,MAAM,IAAI,IAAI8I,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGnN,iBAAiB;IAClN,MAAMuJ,gBAAgB,CAACG,SAAS,CAAC;EACrC,CAAC;EAAA,OAAA4G,YAAA,CAAArO,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcsO,MAAMA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,OAAA,CAAA1O,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAyO,QAAA;EAAAA,OAAA,GAAAxO,iBAAA,CAArB,WAAsBmN,KAAK,EAAE5F,SAAS,EAAE;IACpC,MAAMyC,eAAe,GAAGyE,yBAAyB,CAACtB,KAAK,CAAC;IACxD,IAAI,CAACnD,eAAe,EAAE;MAClB;MACA;IACJ;IACA;IACA,IAAIzC,SAAS,CAACmH,wCAAwC,EAAE;MACpD,MAAM1C,QAAQ,CAACzE,SAAS,EAAEyC,eAAe,CAAC;IAC9C;IACA;IACA,MAAM2E,UAAU,SAASC,aAAa,CAAC,CAAC;IACxC,IAAIC,iBAAiB,CAACF,UAAU,CAAC,EAAE;MAC/B,OAAOG,mCAAmC,CAACH,UAAU,EAAE3E,eAAe,CAAC;IAC3E;IACA;IACA,IAAI,CAAC,CAACA,eAAe,CAACW,YAAY,EAAE;MAChC,MAAMoE,gBAAgB,CAACC,mBAAmB,CAAChF,eAAe,CAAC,CAAC;IAChE;IACA,IAAI,CAACzC,SAAS,EAAE;MACZ;IACJ;IACA,IAAI,CAAC,CAACA,SAAS,CAAC0H,0BAA0B,EAAE;MACxC,MAAMhF,OAAO,GAAGF,kBAAkB,CAACC,eAAe,CAAC;MACnD,IAAI,OAAOzC,SAAS,CAAC0H,0BAA0B,KAAK,UAAU,EAAE;QAC5D,MAAM1H,SAAS,CAAC0H,0BAA0B,CAAChF,OAAO,CAAC;MACvD,CAAC,MACI;QACD1C,SAAS,CAAC0H,0BAA0B,CAACC,IAAI,CAACjF,OAAO,CAAC;MACtD;IACJ;EACJ,CAAC;EAAA,OAAAuE,OAAA,CAAA1O,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcoP,mBAAmBA,CAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAAvP,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAsP,qBAAA;EAAAA,oBAAA,GAAArP,iBAAA,CAAlC,WAAmCmN,KAAK,EAAE;IACtC,IAAIpM,EAAE,EAAEiK,EAAE;IACV,MAAMhB,eAAe,GAAG,CAACgB,EAAE,GAAG,CAACjK,EAAE,GAAGoM,KAAK,CAACxC,YAAY,MAAM,IAAI,IAAI5J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgK,IAAI,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjN,OAAO,CAAC;IACtJ,IAAI,CAACiM,eAAe,EAAE;MAClB;IACJ,CAAC,MACI,IAAImD,KAAK,CAACmC,MAAM,EAAE;MACnB;MACA;MACA;IACJ;IACA;IACAnC,KAAK,CAACoC,wBAAwB,CAAC,CAAC;IAChCpC,KAAK,CAACxC,YAAY,CAACpI,KAAK,CAAC,CAAC;IAC1B;IACA,MAAM+I,IAAI,GAAGkE,OAAO,CAACxF,eAAe,CAAC;IACrC,IAAI,CAACsB,IAAI,EAAE;MACP;IACJ;IACA;IACA,MAAMmE,GAAG,GAAG,IAAIC,GAAG,CAACpE,IAAI,EAAE2B,IAAI,CAAC0C,QAAQ,CAACC,IAAI,CAAC;IAC7C,MAAMC,SAAS,GAAG,IAAIH,GAAG,CAACzC,IAAI,CAAC0C,QAAQ,CAACzC,MAAM,CAAC;IAC/C,IAAIuC,GAAG,CAACK,IAAI,KAAKD,SAAS,CAACC,IAAI,EAAE;MAC7B;IACJ;IACA,IAAIC,MAAM,SAASC,eAAe,CAACP,GAAG,CAAC;IACvC,IAAI,CAACM,MAAM,EAAE;MACTA,MAAM,SAAS9C,IAAI,CAACgD,OAAO,CAACC,UAAU,CAAC5E,IAAI,CAAC;MAC5C;MACA;MACA,MAAMI,KAAK,CAAC,IAAI,CAAC;IACrB,CAAC,MACI;MACDqE,MAAM,SAASA,MAAM,CAACI,KAAK,CAAC,CAAC;IACjC;IACA,IAAI,CAACJ,MAAM,EAAE;MACT;MACA;IACJ;IACA/F,eAAe,CAACoG,WAAW,GAAGhS,WAAW,CAACiS,oBAAoB;IAC9DrG,eAAe,CAACsG,mBAAmB,GAAG,IAAI;IAC1C,OAAOP,MAAM,CAACQ,WAAW,CAACvG,eAAe,CAAC;EAC9C,CAAC;EAAA,OAAAqF,oBAAA,CAAAvP,KAAA,OAAAC,SAAA;AAAA;AACD,SAASiP,mBAAmBA,CAAChF,eAAe,EAAE;EAC1C,MAAMwG,sBAAsB,GAAG3H,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEkB,eAAe,CAACW,YAAY,CAAC;EAC9E;EACA;EACA;EACA6F,sBAAsB,CAACzF,IAAI,GAAG;IAC1B,CAAChN,OAAO,GAAGiM;EACf,CAAC;EACD,OAAOwG,sBAAsB;AACjC;AACA,SAAS/B,yBAAyBA,CAAC;EAAE1D;AAAK,CAAC,EAAE;EACzC,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,IAAI;EACf;EACA,IAAI;IACA,OAAOA,IAAI,CAACxF,IAAI,CAAC,CAAC;EACtB,CAAC,CACD,OAAOC,GAAG,EAAE;IACR;IACA,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA;AAHA,SAIewK,eAAeA,CAAAS,IAAA;EAAA,OAAAC,gBAAA,CAAA5Q,KAAA,OAAAC,SAAA;AAAA;AAU9B;AACA;AACA;AACA;AAHA,SAAA2Q,iBAAA;EAAAA,gBAAA,GAAA1Q,iBAAA,CAVA,WAA+ByP,GAAG,EAAE;IAChC,MAAMd,UAAU,SAASC,aAAa,CAAC,CAAC;IACxC,KAAK,MAAMmB,MAAM,IAAIpB,UAAU,EAAE;MAC7B,MAAMgC,SAAS,GAAG,IAAIjB,GAAG,CAACK,MAAM,CAACN,GAAG,EAAExC,IAAI,CAAC0C,QAAQ,CAACC,IAAI,CAAC;MACzD,IAAIH,GAAG,CAACK,IAAI,KAAKa,SAAS,CAACb,IAAI,EAAE;QAC7B,OAAOC,MAAM;MACjB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EAAA,OAAAW,gBAAA,CAAA5Q,KAAA,OAAAC,SAAA;AAAA;AAKD,SAAS8O,iBAAiBA,CAACF,UAAU,EAAE;EACnC,OAAOA,UAAU,CAACiC,IAAI,CAACb,MAAM,IAAIA,MAAM,CAACc,eAAe,KAAK,SAAS;EACjE;EACA;EACA,CAACd,MAAM,CAACN,GAAG,CAACqB,UAAU,CAAC,qBAAqB,CAAC,CAAC;AACtD;AACA,SAAShC,mCAAmCA,CAACH,UAAU,EAAE3E,eAAe,EAAE;EACtEA,eAAe,CAACsG,mBAAmB,GAAG,IAAI;EAC1CtG,eAAe,CAACoG,WAAW,GAAGhS,WAAW,CAAC2S,aAAa;EACvD,KAAK,MAAMhB,MAAM,IAAIpB,UAAU,EAAE;IAC7BoB,MAAM,CAACQ,WAAW,CAACvG,eAAe,CAAC;EACvC;AACJ;AACA,SAAS4E,aAAaA,CAAA,EAAG;EACrB,OAAO3B,IAAI,CAACgD,OAAO,CAACe,QAAQ,CAAC;IACzBC,IAAI,EAAE,QAAQ;IACdC,mBAAmB,EAAE;IACrB;EACJ,CAAC,CAAC;AACN;;AACA,SAASnC,gBAAgBA,CAACoC,2BAA2B,EAAE;EACnD,IAAIpQ,EAAE;EACN;EACA;EACA,MAAM;IAAEqQ;EAAQ,CAAC,GAAGD,2BAA2B;EAC/C,MAAM;IAAEE;EAAW,CAAC,GAAGC,YAAY;EACnC,IAAIF,OAAO,IAAIC,UAAU,IAAID,OAAO,CAACnS,MAAM,GAAGoS,UAAU,EAAE;IACtDtJ,OAAO,CAACC,IAAI,CAAE,8BAA6BqJ,UAAW,wDAAuD,CAAC;EAClH;EACA,OAAOpE,IAAI,CAACsE,YAAY,CAACxC,gBAAgB,EACzC,YAAa,CAAChO,EAAE,GAAGoQ,2BAA2B,CAACvG,KAAK,MAAM,IAAI,IAAI7J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEoQ,2BAA2B,CAAC;AAC3H;AACA,SAAS3B,OAAOA,CAACvF,OAAO,EAAE;EACtB,IAAIlJ,EAAE,EAAEiK,EAAE,EAAEC,EAAE;EACd;EACA,MAAMK,IAAI,GAAG,CAACN,EAAE,GAAG,CAACjK,EAAE,GAAGkJ,OAAO,CAACmB,UAAU,MAAM,IAAI,IAAIrK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuK,IAAI,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAACC,EAAE,GAAGhB,OAAO,CAACU,YAAY,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,YAAY;EAC5M,IAAIC,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA,IAAIG,gBAAgB,CAACxB,OAAO,CAACc,IAAI,CAAC,EAAE;IAChC;IACA,OAAOkC,IAAI,CAAC0C,QAAQ,CAACzC,MAAM;EAC/B,CAAC,MACI;IACD,OAAO,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsE,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE;IACtB,MAAMC,oBAAoB,CAAC,0BAA0B,CAAC;EAC1D;EACA,IAAI,CAACF,GAAG,CAAClR,IAAI,EAAE;IACX,MAAMoR,oBAAoB,CAAC,UAAU,CAAC;EAC1C;EACA;EACA,MAAMC,UAAU,GAAG,CACf,WAAW,EACX,QAAQ,EACR,OAAO,EACP,mBAAmB,CACtB;EACD,MAAM;IAAEF;EAAQ,CAAC,GAAGD,GAAG;EACvB,KAAK,MAAMI,OAAO,IAAID,UAAU,EAAE;IAC9B,IAAI,CAACF,OAAO,CAACG,OAAO,CAAC,EAAE;MACnB,MAAMF,oBAAoB,CAACE,OAAO,CAAC;IACvC;EACJ;EACA,OAAO;IACHC,OAAO,EAAEL,GAAG,CAAClR,IAAI;IACjBiG,SAAS,EAAEkL,OAAO,CAAClL,SAAS;IAC5BQ,MAAM,EAAE0K,OAAO,CAAC1K,MAAM;IACtB5C,KAAK,EAAEsN,OAAO,CAACtN,KAAK;IACpBnE,QAAQ,EAAEyR,OAAO,CAACK;EACtB,CAAC;AACL;AACA,SAASJ,oBAAoBA,CAACK,SAAS,EAAE;EACrC,OAAO1N,aAAa,CAACmB,MAAM,CAAC,2BAA2B,CAAC,2CAA2C;IAC/FuM;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAACT,GAAG,EAAE9K,aAAa,EAAEwL,iBAAiB,EAAE;IAC/C;IACA,IAAI,CAACzD,wCAAwC,GAAG,KAAK;IACrD,IAAI,CAACO,0BAA0B,GAAG,IAAI;IACtC,IAAI,CAACmD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC3E,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC4E,mBAAmB,GAAG,KAAK;IAChC,MAAM7O,SAAS,GAAGgO,gBAAgB,CAACC,GAAG,CAAC;IACvC,IAAI,CAACtO,oBAAoB,GAAG;MACxBsO,GAAG;MACHjO,SAAS;MACTmD,aAAa;MACbwL;IACJ,CAAC;EACL;EACAG,OAAOA,CAAA,EAAG;IACN,OAAO1G,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0G,kBAAkB,GAAIC,SAAS,IAAK;EACtC,MAAMjL,SAAS,GAAG,IAAI0K,gBAAgB,CAACO,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAACC,YAAY,CAAC,CAAC,EAAEF,SAAS,CAACC,WAAW,CAAC,wBAAwB,CAAC,CAACC,YAAY,CAAC,CAAC,EAAEF,SAAS,CAACC,WAAW,CAAC,oBAAoB,CAAC,CAAC;EAChMxF,IAAI,CAAC0F,gBAAgB,CAAC,MAAM,EAAE7K,CAAC,IAAI;IAC/BA,CAAC,CAAC8K,SAAS,CAACvE,MAAM,CAACvG,CAAC,EAAEP,SAAS,CAAC,CAAC;EACrC,CAAC,CAAC;EACF0F,IAAI,CAAC0F,gBAAgB,CAAC,wBAAwB,EAAE7K,CAAC,IAAI;IACjDA,CAAC,CAAC8K,SAAS,CAAC5E,WAAW,CAAClG,CAAC,EAAEP,SAAS,CAAC,CAAC;EAC1C,CAAC,CAAC;EACF0F,IAAI,CAAC0F,gBAAgB,CAAC,mBAAmB,EAAE7K,CAAC,IAAI;IAC5CA,CAAC,CAAC8K,SAAS,CAACzD,mBAAmB,CAACrH,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;EACF,OAAOP,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASsL,qBAAqBA,CAAA,EAAG;EAC7BnV,kBAAkB,CAAC,IAAIP,SAAS,CAAC,cAAc,EAAEoV,kBAAkB,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;AAC9G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMeO,aAAaA,CAAA;EAAA,OAAAC,cAAA,CAAAjT,KAAA,OAAAC,SAAA;AAAA;AAY5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAgT,eAAA;EAAAA,cAAA,GAAA/S,iBAAA,CAZA,aAA+B;IAC3B;IACA;IACA;IACA,OAAQzC,oBAAoB,CAAC,CAAC,WACnBC,yBAAyB,CAAC,CAAC,CAAC,IACnC,aAAa,IAAIyP,IAAI,IACrB,cAAc,IAAIA,IAAI,IACtB+F,yBAAyB,CAACC,SAAS,CAACC,cAAc,CAAC,kBAAkB,CAAC,IACtEC,gBAAgB,CAACF,SAAS,CAACC,cAAc,CAAC,QAAQ,CAAC;EAC3D,CAAC;EAAA,OAAAH,cAAA,CAAAjT,KAAA,OAAAC,SAAA;AAAA;AAkBD,SAASqT,qBAAqBA,CAAC7L,SAAS,EAAE8L,cAAc,EAAE;EACtD,IAAIpG,IAAI,CAACqG,QAAQ,KAAKC,SAAS,EAAE;IAC7B,MAAMjP,aAAa,CAACmB,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC;EACtF;;EACA8B,SAAS,CAAC0H,0BAA0B,GAAGoE,cAAc;EACrD,OAAO,MAAM;IACT9L,SAAS,CAAC0H,0BAA0B,GAAG,IAAI;EAC/C,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuE,4CAA4CA,CAACjM,SAAS,EAAEkM,MAAM,EAAE;EACrElM,SAAS,CAACmH,wCAAwC,GAC9C+E,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACjC,GAAG,GAAG7T,MAAM,CAAC,CAAC,EAAE;EACtC;EACA;EACA;EACA;EACAkV,aAAa,CAAC,CAAC,CAACa,IAAI,CAACC,WAAW,IAAI;IAChC;IACA,IAAI,CAACA,WAAW,EAAE;MACd,MAAMtP,aAAa,CAACmB,MAAM,CAAC,qBAAqB,CAAC,mCAAmC,CAAC;IACzF;EACJ,CAAC,EAAEoO,CAAC,IAAI;IACJ;IACA,MAAMvP,aAAa,CAACmB,MAAM,CAAC,wBAAwB,CAAC,sCAAsC,CAAC;EAC/F,CAAC,CAAC;;EACF,OAAO9H,YAAY,CAACF,kBAAkB,CAACgU,GAAG,CAAC,EAAE,cAAc,CAAC,CAACiB,YAAY,CAAC,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,mBAAmBA,CAACvM,SAAS,EAAE8L,cAAc,EAAE;EACpD9L,SAAS,GAAG9J,kBAAkB,CAAC8J,SAAS,CAAC;EACzC,OAAO6L,qBAAqB,CAAC7L,SAAS,EAAE8L,cAAc,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,uDAAuDA,CAACxM,SAAS,EAAEkM,MAAM,EAAE;EAChFlM,SAAS,GAAG9J,kBAAkB,CAAC8J,SAAS,CAAC;EACzC,OAAOiM,4CAA4C,CAACjM,SAAS,EAAEkM,MAAM,CAAC;AAC1E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAZ,qBAAqB,CAAC,CAAC;AAEvB,SAASkB,uDAAuD,EAAEL,gBAAgB,IAAIM,YAAY,EAAElB,aAAa,IAAIc,WAAW,EAAEE,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  let config;\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    config = configOrBufferSize;\n  } else {\n    config = {\n      bufferSize: configOrBufferSize,\n      windowTime,\n      refCount: false,\n      scheduler\n    };\n  }\n  return source => source.lift(shareReplayOperator(config));\n}\nfunction shareReplayOperator({\n  bufferSize = Number.POSITIVE_INFINITY,\n  windowTime = Number.POSITIVE_INFINITY,\n  refCount: useRefCount,\n  scheduler\n}) {\n  let subject;\n  let refCount = 0;\n  let subscription;\n  let hasError = false;\n  let isComplete = false;\n  return function shareReplayOperation(source) {\n    refCount++;\n    let innerSub;\n    if (!subject || hasError) {\n      hasError = false;\n      subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n      innerSub = subject.subscribe(this);\n      subscription = source.subscribe({\n        next(value) {\n          subject.next(value);\n        },\n        error(err) {\n          hasError = true;\n          subject.error(err);\n        },\n        complete() {\n          isComplete = true;\n          subscription = undefined;\n          subject.complete();\n        }\n      });\n    } else {\n      innerSub = subject.subscribe(this);\n    }\n    this.add(() => {\n      refCount--;\n      innerSub.unsubscribe();\n      if (subscription && !isComplete && useRefCount && refCount === 0) {\n        subscription.unsubscribe();\n        subscription = undefined;\n        subject = undefined;\n      }\n    });\n  };\n}", "map": {"version": 3, "names": ["ReplaySubject", "shareReplay", "configOrBufferSize", "windowTime", "scheduler", "config", "bufferSize", "refCount", "source", "lift", "shareReplayOperator", "Number", "POSITIVE_INFINITY", "useRefCount", "subject", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "isComplete", "shareReplayOperation", "innerSub", "subscribe", "next", "value", "error", "err", "complete", "undefined", "add", "unsubscribe"], "sources": ["D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/rxjs/_esm2015/internal/operators/shareReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n    let config;\n    if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n        config = configOrBufferSize;\n    }\n    else {\n        config = {\n            bufferSize: configOrBufferSize,\n            windowTime,\n            refCount: false,\n            scheduler\n        };\n    }\n    return (source) => source.lift(shareReplayOperator(config));\n}\nfunction shareReplayOperator({ bufferSize = Number.POSITIVE_INFINITY, windowTime = Number.POSITIVE_INFINITY, refCount: useRefCount, scheduler }) {\n    let subject;\n    let refCount = 0;\n    let subscription;\n    let hasError = false;\n    let isComplete = false;\n    return function shareReplayOperation(source) {\n        refCount++;\n        let innerSub;\n        if (!subject || hasError) {\n            hasError = false;\n            subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n            innerSub = subject.subscribe(this);\n            subscription = source.subscribe({\n                next(value) { subject.next(value); },\n                error(err) {\n                    hasError = true;\n                    subject.error(err);\n                },\n                complete() {\n                    isComplete = true;\n                    subscription = undefined;\n                    subject.complete();\n                },\n            });\n        }\n        else {\n            innerSub = subject.subscribe(this);\n        }\n        this.add(() => {\n            refCount--;\n            innerSub.unsubscribe();\n            if (subscription && !isComplete && useRefCount && refCount === 0) {\n                subscription.unsubscribe();\n                subscription = undefined;\n                subject = undefined;\n            }\n        });\n    };\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,OAAO,SAASC,WAAWA,CAACC,kBAAkB,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACnE,IAAIC,MAAM;EACV,IAAIH,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;IAC9DG,MAAM,GAAGH,kBAAkB;EAC/B,CAAC,MACI;IACDG,MAAM,GAAG;MACLC,UAAU,EAAEJ,kBAAkB;MAC9BC,UAAU;MACVI,QAAQ,EAAE,KAAK;MACfH;IACJ,CAAC;EACL;EACA,OAAQI,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACC,mBAAmB,CAACL,MAAM,CAAC,CAAC;AAC/D;AACA,SAASK,mBAAmBA,CAAC;EAAEJ,UAAU,GAAGK,MAAM,CAACC,iBAAiB;EAAET,UAAU,GAAGQ,MAAM,CAACC,iBAAiB;EAAEL,QAAQ,EAAEM,WAAW;EAAET;AAAU,CAAC,EAAE;EAC7I,IAAIU,OAAO;EACX,IAAIP,QAAQ,GAAG,CAAC;EAChB,IAAIQ,YAAY;EAChB,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,UAAU,GAAG,KAAK;EACtB,OAAO,SAASC,oBAAoBA,CAACV,MAAM,EAAE;IACzCD,QAAQ,EAAE;IACV,IAAIY,QAAQ;IACZ,IAAI,CAACL,OAAO,IAAIE,QAAQ,EAAE;MACtBA,QAAQ,GAAG,KAAK;MAChBF,OAAO,GAAG,IAAId,aAAa,CAACM,UAAU,EAAEH,UAAU,EAAEC,SAAS,CAAC;MAC9De,QAAQ,GAAGL,OAAO,CAACM,SAAS,CAAC,IAAI,CAAC;MAClCL,YAAY,GAAGP,MAAM,CAACY,SAAS,CAAC;QAC5BC,IAAIA,CAACC,KAAK,EAAE;UAAER,OAAO,CAACO,IAAI,CAACC,KAAK,CAAC;QAAE,CAAC;QACpCC,KAAKA,CAACC,GAAG,EAAE;UACPR,QAAQ,GAAG,IAAI;UACfF,OAAO,CAACS,KAAK,CAACC,GAAG,CAAC;QACtB,CAAC;QACDC,QAAQA,CAAA,EAAG;UACPR,UAAU,GAAG,IAAI;UACjBF,YAAY,GAAGW,SAAS;UACxBZ,OAAO,CAACW,QAAQ,CAAC,CAAC;QACtB;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACDN,QAAQ,GAAGL,OAAO,CAACM,SAAS,CAAC,IAAI,CAAC;IACtC;IACA,IAAI,CAACO,GAAG,CAAC,MAAM;MACXpB,QAAQ,EAAE;MACVY,QAAQ,CAACS,WAAW,CAAC,CAAC;MACtB,IAAIb,YAAY,IAAI,CAACE,UAAU,IAAIJ,WAAW,IAAIN,QAAQ,KAAK,CAAC,EAAE;QAC9DQ,YAAY,CAACa,WAAW,CAAC,CAAC;QAC1Bb,YAAY,GAAGW,SAAS;QACxBZ,OAAO,GAAGY,SAAS;MACvB;IACJ,CAAC,CAAC;EACN,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}